@echo off

REM Run Maven clean, compile, and package
REM call mvn clean compile package


REM Check if Maven build was successful

REM Define SSH connection details
set HOST=**************
set USER=root
set PASSWORD=Efgh1234

REM Copy the file to the remote server

pscp -pw %PASSWORD% target\swallowtech.war %USER%@%HOST%:/home/<USER>/Wildfly-node2/standalone/deployments/swallowtech.war 
pscp -pw %PASSWORD% target\swallowtech.war %USER%@%HOST%:/home/<USER>/Wildfly-node1/standalone/deployments/swallowtech.war 


REM SSH commands to stop and start WildFly
REM sshpass -p %PASSWORD% ssh %USER%@%HOST% "/home/<USER>/Wildfly-node1/bin/wildfly.sh stop --cluster"
REM sshpass -p %PASSWORD% ssh %USER%@%HOST% "/home/<USER>/Wildfly-node1/bin/wildfly.sh start --cluster"

plink -batch -ssh root@************** -pw Efgh1234 -m command.txt


echo File upload complete.