<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
		 xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>

	<groupId>smart-predict</groupId>
	<artifactId>predict-java</artifactId>
	<packaging>war</packaging>
	<version>1071</version>

	<name>Smart Predict</name>

	<properties>
		<project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
		<java.version>1.8</java.version>

		<struts.version>6.3.0.2</struts.version>

		<spring-boot.version>2.7.18.tuxcare.5</spring-boot.version>
		<spring.version>5.3.39.tuxcare.4</spring.version>

		<spring.security.version>5.8.15</spring.security.version>

		<log4j.version>2.23.1</log4j.version>

		<!-- TODO migrate -->
		<org.springframework.cloud-zookeeper-discovery-version>2.2.1.RELEASE</org.springframework.cloud-zookeeper-discovery-version>
		<org.apache.curator-version>5.3.0</org.apache.curator-version>

		<org.bouncycastle-version>1.70</org.bouncycastle-version>

		<org.jasypt-version>1.8</org.jasypt-version>
		<io.jsonwebtoken-version>0.11.1</io.jsonwebtoken-version>
		<org.json-version>20201115</org.json-version>
		<jasperreports-version>6.21.4</jasperreports-version>


		<libdir>${project.basedir}/src/main/lib</libdir>
		<libdir-compile>${libdir}/compile</libdir-compile>

		<struts2-jquery-version>5.0.3</struts2-jquery-version>


	</properties>
	<repositories>
		<repository>
			<id>repository-swallowtech</id>
			<url>https://predict:<EMAIL>:8181/repository/swallowtech/</url>
		</repository>
		<repository>
			<id>djmaven2</id>
			<url>http://www.fdvs.com.ar/djmaven2</url>
			<name>DynamicJasper public Repository</name>
		</repository>
	</repositories>

	<dependencyManagement>
		<dependencies>
			<dependency>
				<!-- Import dependency management from Spring Boot -->
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-dependencies</artifactId>
				<version>${spring-boot.version}</version>
				<type>pom</type>
				<scope>import</scope>
			</dependency>
			<dependency>
				<groupId>org.apache.struts</groupId>
				<artifactId>struts2-bom</artifactId>
				<version>${struts.version}</version>
				<scope>import</scope>
				<type>pom</type>
			</dependency>

			<!-- spring-*.jar version management -->
			<dependency>
				<groupId>org.springframework</groupId>
				<artifactId>spring-aop</artifactId>
				<version>${spring.version}</version>
			</dependency>
			<dependency>
				<groupId>org.springframework</groupId>
				<artifactId>spring-aspects</artifactId>
				<version>${spring.version}</version>
			</dependency>
			<dependency>
				<groupId>org.springframework</groupId>
				<artifactId>spring-beans</artifactId>
				<version>${spring.version}</version>
			</dependency>
			<dependency>
				<groupId>org.springframework</groupId>
				<artifactId>spring-context</artifactId>
				<version>${spring.version}</version>
			</dependency>
			<dependency>
				<groupId>org.springframework</groupId>
				<artifactId>spring-core</artifactId>
				<version>${spring.version}</version>
			</dependency>
			<dependency>
				<groupId>org.springframework</groupId>
				<artifactId>spring-expression</artifactId>
				<version>${spring.version}</version>
			</dependency>
			<dependency>
				<groupId>org.springframework</groupId>
				<artifactId>spring-jcl</artifactId>
				<version>${spring.version}</version>
			</dependency>
			<dependency>
				<groupId>org.springframework</groupId>
				<artifactId>spring-jdbc</artifactId>
				<version>${spring.version}</version>
			</dependency>
			<dependency>
				<groupId>org.springframework</groupId>
				<artifactId>spring-orm</artifactId>
				<version>${spring.version}</version>
			</dependency>
			<dependency>
				<groupId>org.springframework</groupId>
				<artifactId>spring-tx</artifactId>
				<version>${spring.version}</version>
			</dependency>
			<dependency>
				<groupId>org.springframework</groupId>
				<artifactId>spring-web</artifactId>
				<version>${spring.version}</version>
			</dependency>
			<dependency>
				<groupId>org.springframework</groupId>
				<artifactId>spring-webmvc</artifactId>
				<version>${spring.version}</version>
			</dependency>
			<dependency>
				<groupId>net.sf.jasperreports</groupId>
				<artifactId>jasperreports</artifactId>
				<version>6.20.5</version>

				<exclusions>
					<exclusion>
						<groupId>com.lowagie</groupId>
						<artifactId>itext</artifactId>
					</exclusion>
				</exclusions>
			</dependency>
			<!-- Log4j version management -->
			<!--			<dependency>-->
			<!--			    <groupId>org.apache.logging.log4j</groupId>-->
			<!--			    <artifactId>log4j-core</artifactId>-->
			<!--			    <version>${log4j.version}</version>-->
			<!--			</dependency>-->
			<dependency>
				<groupId>org.apache.logging.log4j</groupId>
				<artifactId>log4j-api</artifactId>
				<version>${log4j.version}</version>
			</dependency>
			<dependency>
				<groupId>org.apache.logging.log4j</groupId>
				<artifactId>log4j-core</artifactId>
				<version>2.17.2</version>
			</dependency>
			<dependency>
				<groupId>org.apache.logging.log4j</groupId>
				<artifactId>log4j-jcl</artifactId>
				<version>2.17.2</version>
			</dependency>
			<dependency>
				<groupId>org.apache.logging.log4j</groupId>
				<artifactId>log4j-to-slf4j</artifactId>
				<version>${log4j.version}</version>
			</dependency>

			<!--Spring Security-->
			<dependency>
				<groupId>org.springframework.security</groupId>
				<artifactId>spring-security-core</artifactId>
				<version>${spring.security.version}</version>
			</dependency>
			<dependency>
				<groupId>org.springframework.security</groupId>
				<artifactId>spring-security-config</artifactId>
				<version>${spring.security.version}</version>
			</dependency>
			<dependency>
				<groupId>org.springframework.security</groupId>
				<artifactId>spring-security-web</artifactId>
				<version>${spring.security.version}</version>
			</dependency>

		</dependencies>
	</dependencyManagement>



	<dependencies>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-web</artifactId>
			<exclusions>
				<exclusion>
					<groupId>org.springframework.boot</groupId>
					<artifactId>spring-boot-starter-tomcat</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.apache.tomcat.embed</groupId>
					<artifactId>tomcat-embed-el</artifactId>
				</exclusion>
				<!--            	<exclusion>-->
				<!--            		<groupId>org.apache.logging.log4j</groupId>-->
				<!--            		<artifactId>log4j-to-slf4j</artifactId>-->
				<!--            	</exclusion>-->
				<!--            	<exclusion>-->
				<!--			        <artifactId>logback-classic</artifactId>-->
				<!--			        <groupId>ch.qos.logback</groupId>-->
				<!--			    </exclusion>-->
			</exclusions>
		</dependency>

		<dependency>
			<groupId>org.reactivestreams</groupId>
			<artifactId>reactive-streams</artifactId>
		</dependency>

		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-security</artifactId>
		</dependency>

		<!--<dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
        </dependency>-->


		<dependency>
			<groupId>javax.servlet</groupId>
			<artifactId>javax.servlet-api</artifactId>
			<!-- <version>3.0.1</version> -->
			<scope>provided</scope>
		</dependency>
		<dependency>
			<groupId>javax.servlet.jsp</groupId>
			<artifactId>javax.servlet.jsp-api</artifactId>
			<version>2.3.3</version>
		</dependency>
		<dependency>
			<groupId>javax.servlet</groupId>
			<artifactId>jstl</artifactId>
		</dependency>

		<dependency>
			<groupId>org.apache.struts</groupId>
			<artifactId>struts2-core</artifactId>
		</dependency>
		<dependency>
			<groupId>org.apache.struts</groupId>
			<artifactId>struts2-convention-plugin</artifactId>
		</dependency>

		<dependency>
			<groupId>org.apache.struts</groupId>
			<artifactId>struts2-spring-plugin</artifactId>
		</dependency>

		<dependency>
			<groupId>org.apache.struts</groupId>
			<artifactId>struts2-tiles-plugin</artifactId>
		</dependency>

		<dependency>
			<groupId>org.ow2.asm</groupId>
			<artifactId>asm</artifactId>
			<version>9.2</version>
		</dependency>

		<!-- <dependency>
            <groupId>org.apache.struts</groupId>
            <artifactId>struts2-convention-plugin</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.ow2.asm</groupId>
                    <artifactId>asm</artifactId>
                </exclusion>
            </exclusions>
        </dependency> -->

		<!--<dependency>
		    <groupId>org.apache.struts</groupId>
		    <artifactId>struts2-rest-plugin</artifactId>
		</dependency>-->

		<dependency>
			<groupId>org.apache.struts</groupId>
			<artifactId>struts2-json-plugin</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-test</artifactId>
			<scope>test</scope>
			<exclusions>
				<exclusion>
					<groupId>junit</groupId>
					<artifactId>junit</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.junit.vintage</groupId>
					<artifactId>junit-vintage-engine</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<!-- junit 5 -->
		<dependency>
			<groupId>org.junit.jupiter</groupId>
			<artifactId>junit-jupiter-api</artifactId>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>org.junit.jupiter</groupId>
			<artifactId>junit-jupiter-engine</artifactId>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>org.apache.httpcomponents</groupId>
			<artifactId>httpclient</artifactId>
			<scope>test</scope>
		</dependency>

		<!-- NEXT ARE WEB SERVER STARTERS -->
		<!-- Use Tomcat -->
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-tomcat</artifactId>
			<scope>provided</scope>
		</dependency>
		<dependency>
			<groupId>org.apache.tomcat.embed</groupId>
			<artifactId>tomcat-embed-jasper</artifactId>
			<scope>provided</scope>
		</dependency>

		<!-- Use Jetty instead of Tomcat-->
		<!--		<dependency>-->
		<!--			<groupId>org.springframework.boot</groupId>-->
		<!--			<artifactId>spring-boot-starter-jetty</artifactId>-->
		<!--		</dependency>-->
		<!--		<dependency>-->
		<!--		   <groupId>org.eclipse.jetty</groupId>-->
		<!--		   <artifactId>apache-jsp</artifactId>-->
		<!--		</dependency>-->
		<!--		<dependency>-->
		<!--		   <groupId>org.eclipse.jetty</groupId>-->
		<!--		   <artifactId>apache-jstl</artifactId>-->
		<!--		</dependency>-->

		<!-- Use Undertow instead of Tomcat-->
		<!--		<dependency>-->
		<!--			<groupId>org.springframework.boot</groupId>-->
		<!--			<artifactId>spring-boot-starter-undertow</artifactId>-->
		<!--		</dependency>-->
		<!--		<dependency>-->
		<!--		   <groupId>org.eclipse.jetty</groupId>-->
		<!--		   <artifactId>apache-jsp</artifactId>-->
		<!--		</dependency>-->
		<!--		<dependency>-->
		<!--		   <groupId>org.eclipse.jetty</groupId>-->
		<!--		   <artifactId>apache-jstl</artifactId>-->
		<!--		</dependency>-->


		<dependency>
			<groupId>commons-logging</groupId>
			<artifactId>commons-logging</artifactId>
			<version>1.1.3</version>
		</dependency>

		<!-- Log4j dependencies -->
		<dependency>
			<groupId>org.apache.logging.log4j</groupId>
			<artifactId>log4j-core</artifactId>
			<version>2.17.2</version>
		</dependency>
		<dependency>
			<groupId>org.apache.logging.log4j</groupId>
			<artifactId>log4j-jcl</artifactId>
			<version>2.17.2</version>
		</dependency>


		<dependency>
			<groupId>net.minidev</groupId>
			<artifactId>json-smart</artifactId>
			<!--            <version>2.4.9</version>-->
			<scope>compile</scope>
		</dependency>
		<dependency>
			<groupId>org.projectlombok</groupId>
			<artifactId>lombok</artifactId>
			<version>1.18.22</version>
			<scope>compile</scope>
		</dependency>
		<dependency>
			<groupId>com.amashchenko.struts2.pdfstream</groupId>
			<artifactId>struts2-pdfstream-plugin</artifactId>
			<version>2.0.0</version>
		</dependency>


		<!-- Dbcp -->
		<dependency>
			<groupId>commons-dbcp</groupId>
			<artifactId>commons-dbcp</artifactId>
			<version>1.4</version>
		</dependency>

		<!-- Pool -->
		<dependency>
			<groupId>commons-pool</groupId>
			<artifactId>commons-pool</artifactId>
		</dependency>

		<dependency>
			<groupId>org.apache.commons</groupId>
			<artifactId>commons-pool2</artifactId>
			<version>2.12.0</version>
		</dependency>

		<dependency>
			<groupId>org.apache.commons</groupId>
			<artifactId>commons-compress</artifactId>
			<version>1.27.0</version> <!-- Use the latest version available -->
		</dependency>
		<!-- JDBC Oracle Driver -->
		<dependency>
			<groupId>com.oracle.database.jdbc</groupId>
			<artifactId>ojdbc8</artifactId>
		</dependency>

		<!-- Hibernate -->
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-data-jpa</artifactId>
		</dependency>

		<dependency>
			<groupId>org.hibernate</groupId>
			<artifactId>hibernate-core</artifactId>
		</dependency>

		<dependency>
			<groupId>org.hibernate</groupId>
			<artifactId>hibernate-entitymanager</artifactId>
		</dependency>
		<dependency>
			<groupId>org.hibernate</groupId>
			<artifactId>hibernate-hikaricp</artifactId>
		</dependency>
		<dependency>
			<groupId>org.hibernate</groupId>
			<artifactId>hibernate-ehcache</artifactId>
		</dependency>


		<!-- PREDICT :: TO BE MIGRATED -->
		<dependency>
			<groupId>org.quartz-scheduler</groupId>
			<artifactId>quartz</artifactId>
			<version>2.3.2</version>
		</dependency>


		<dependency>
			<groupId>org.apache.curator</groupId>
			<artifactId>curator-framework</artifactId>
			<version>${org.apache.curator-version}</version>
			<exclusions>
				<exclusion>
					<groupId>log4j</groupId>
					<artifactId>log4j</artifactId>
				</exclusion>
				<exclusion>
					<groupId>io.netty</groupId>
					<artifactId>netty-handler</artifactId>
				</exclusion>
				<exclusion>
					<groupId>io.netty</groupId>
					<artifactId>
						netty-transport-native-epoll
					</artifactId>
				</exclusion>
			</exclusions>
		</dependency>

		<dependency>
			<groupId>org.apache.curator</groupId>
			<artifactId>curator-recipes</artifactId>
			<version>${org.apache.curator-version}</version>
		</dependency>

		<dependency>
			<groupId>org.apache.curator</groupId>
			<artifactId>curator-x-discovery</artifactId>
			<version>${org.apache.curator-version}</version>
		</dependency>

		<dependency>
			<groupId>org.springframework.cloud</groupId>
			<artifactId>spring-cloud-commons</artifactId>
			<version>${org.springframework.cloud-zookeeper-discovery-version}</version>
		</dependency>

		<dependency>
			<groupId>org.springframework.cloud</groupId>
			<artifactId>spring-cloud-zookeeper-discovery</artifactId>
			<version>${org.springframework.cloud-zookeeper-discovery-version}</version>
		</dependency>

		<!-- <dependency>
    <groupId>org.springframework.cloud</groupId>
    <artifactId>spring-cloud-starter-zookeeper-discovery</artifactId>
    <version>2.2.1.RELEASE</version>
</dependency> -->

		<!--
<dependency>
    <groupId>org.springframework.cloud</groupId>
    <artifactId>spring-cloud-starter-zookeeper-discovery</artifactId>
    <version>2.2.1.RELEASE</version>
</dependency>

<dependency>
    <groupId>commons-lang</groupId>
    <artifactId>commons-lang</artifactId>
    <version>2.6</version>
</dependency>
	<dependency>
    <groupId>commons-configuration</groupId>
    <artifactId>commons-configuration</artifactId>
    <version>1.8</version>
</dependency>
-->

		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-devtools</artifactId>
			<scope>compile</scope>
		</dependency>


		<dependency>
			<groupId>xerces</groupId>
			<artifactId>xercesImpl</artifactId>
			<scope>compile</scope>
			<version>2.9.1</version>
			<exclusions>
				<exclusion>
					<groupId>xml-apis</groupId>
					<artifactId>xml-apis</artifactId>
				</exclusion>
			</exclusions>
		</dependency>

		<!-- TODO upgrade ! -->
		<dependency>
			<groupId>com.whirlycott</groupId>
			<artifactId>whirlycache</artifactId>
			<version>0.7.1</version>
			<exclusions>
				<exclusion>
					<groupId>log4j</groupId>
					<artifactId>log4j</artifactId>
				</exclusion>
			</exclusions>
		</dependency>

		<dependency>
			<groupId>org.swallow</groupId>
			<artifactId>SwtJradius</artifactId>
			<version>1.0</version>
		</dependency>
		<dependency>
			<groupId>org.swallow</groupId>
			<artifactId>SwtController</artifactId>
			<version>1.0</version>
		</dependency>

		<dependency>
			<groupId>jett.core</groupId>
			<artifactId>jett-core</artifactId>
			<version>0.11.2</version>
		</dependency>


		<dependency>
			<groupId>xmlrpc</groupId>
			<artifactId>xmlrpc</artifactId>
			<version>2.0.1</version>
		</dependency>

		<dependency>
			<groupId>org.apache.ws.commons.util</groupId>
			<artifactId>ws-commons-util</artifactId>
			<version>1.0.2</version>
		</dependency>

		<!-- https://mvnrepository.com/artifact/org.codehaus.jackson/jackson-mapper-asl -->
		<dependency>
			<groupId>org.codehaus.jackson</groupId>
			<artifactId>jackson-mapper-asl</artifactId>
			<version>1.9.13</version>
		</dependency>


		<dependency>
			<groupId>org.json</groupId>
			<artifactId>json</artifactId>
			<version>${org.json-version}</version>
		</dependency>

		<dependency>
			<groupId>io.jsonwebtoken</groupId>
			<artifactId>jjwt-impl</artifactId>
			<version>${io.jsonwebtoken-version}</version>
		</dependency>

		<dependency>
			<groupId>io.jsonwebtoken</groupId>
			<artifactId>jjwt-jackson</artifactId>
			<version>${io.jsonwebtoken-version}</version>
		</dependency>

		<dependency>
			<groupId>org.jboss.ejb3</groupId>
			<artifactId>jboss-ejb3-ext-api</artifactId>
			<version>2.0.0</version>
			<scope>compile</scope>
		</dependency>
		<dependency>
			<groupId>org.jboss.spec.javax.ejb</groupId>
			<artifactId>jboss-ejb-api_3.1_spec</artifactId>
			<version>1.0.1.Final</version>
			<scope>compile</scope>
		</dependency>
		<dependency>
			<groupId>org.wildfly</groupId>
			<artifactId>wildfly-clustering-singleton-api</artifactId>
			<version>21.0.2.Final</version>
			<scope>compile</scope>
		</dependency>


		<dependency>
			<groupId>org.bouncycastle</groupId>
			<artifactId>bcpkix-jdk15on</artifactId>
			<version>${org.bouncycastle-version}</version>
		</dependency>

		<dependency>
			<groupId>org.bouncycastle</groupId>
			<artifactId>bcprov-ext-jdk15on</artifactId>
			<version>${org.bouncycastle-version}</version>
		</dependency>

		<dependency>
			<groupId>org.bouncycastle</groupId>
			<artifactId>bcprov-jdk15on</artifactId>
			<version>${org.bouncycastle-version}</version>
		</dependency>



		<dependency>
			<groupId>org.jfree</groupId>
			<artifactId>jfreechart</artifactId>
			<version>1.0.14</version>
			<exclusions>
				<exclusion>
					<groupId>com.lowagie</groupId>
					<artifactId>itext</artifactId>
				</exclusion>
			</exclusions>
		</dependency>



		<dependency>
			<groupId>org.eclipse.jdt.core.compiler</groupId>
			<artifactId>ecj</artifactId>
			<version>4.4.2</version>
		</dependency>


		<dependency>
			<groupId>org.jasypt</groupId>
			<artifactId>jasypt</artifactId>
			<version>${org.jasypt-version}</version>
		</dependency>


		<!-- https://mvnrepository.com/artifact/javax.mail/mail -->
		<dependency>
			<groupId>javax.mail</groupId>
			<artifactId>mail</artifactId>
			<version>1.4.4</version>
		</dependency>

		<!-- https://mvnrepository.com/artifact/javax.transaction/jta -->
		<dependency>
			<groupId>javax.transaction</groupId>
			<artifactId>jta</artifactId>
			<version>1.1</version>
		</dependency>

		<!-- https://mvnrepository.com/artifact/org.apache.mina/mina-core -->
		<dependency>
			<groupId>org.apache.mina</groupId>
			<artifactId>mina-core</artifactId>
			<version>2.0.4</version>
		</dependency>

		<dependency>
			<groupId>com.thoughtworks.xstream</groupId>
			<artifactId>xstream</artifactId>
			<version>1.4.20</version>
		</dependency>

		<dependency>
			<groupId>javax.ws.rs</groupId>
			<artifactId>javax.ws.rs-api</artifactId>
			<version>2.1</version>
		</dependency>


		<dependency>
			<groupId>com.google.code.gson</groupId>
			<artifactId>gson</artifactId>
			<version>2.8.5</version>
		</dependency>

		<dependency>
			<groupId>org.apache.httpcomponents</groupId>
			<artifactId>httpclient</artifactId>
			<version>4.5.13</version>
		</dependency>

		<dependency>
			<groupId>net.sourceforge.jexcelapi</groupId>
			<artifactId>jxl</artifactId>
			<version>2.6.12</version>
			<exclusions>
				<exclusion>
					<groupId>log4j</groupId>
					<artifactId>log4j</artifactId>
				</exclusion>
			</exclusions>
		</dependency>

		<dependency>
			<groupId>ar.com.fdvs</groupId>
			<artifactId>DynamicJasper</artifactId>
			<version>5.3.9</version>
		</dependency>

		<dependency>
			<groupId>org.apache.poi</groupId>
			<artifactId>poi-ooxml</artifactId>
			<version>5.4.0</version>
			<exclusions>
				<exclusion>
					<groupId>commons-io</groupId>
					<artifactId>commons-io</artifactId>
				</exclusion>
			</exclusions>
		</dependency>

		<dependency>
			<groupId>commons-io</groupId>
			<artifactId>commons-io</artifactId>
			<version>2.18.0</version>
		</dependency>

		<!-- Apache POI for Excel (XSSF and HSSF) -->
		<dependency>
			<groupId>org.apache.poi</groupId>
			<artifactId>poi-ooxml</artifactId>
			<version>5.4.0</version>
		</dependency>


		<dependency>
			<groupId>org.jsoup</groupId>
			<artifactId>jsoup</artifactId>
			<version>1.17.2</version>
		</dependency>



		<dependency>
			<groupId>commons-validator</groupId>
			<artifactId>commons-validator</artifactId>
			<version>1.7</version>
		</dependency>

		<dependency>
			<groupId>org.apache.commons</groupId>
			<artifactId>commons-jexl</artifactId>
			<version>2.1.1</version>
		</dependency>

		<dependency>
			<groupId>net.sf.jagg</groupId>
			<artifactId>jagg-core</artifactId>
			<version>0.9.0</version>
		</dependency>

	</dependencies>


	<build>
		<finalName>swallowtech</finalName>
		<resources>
			<resource>
				<directory>src/main/resources</directory>
			</resource>
			<resource>
				<directory>src/main/java</directory>
				<excludes>
					<exclude>**/*.java</exclude>
				</excludes>
			</resource>
			<resource>
				<directory>src/main/java-pcm</directory>
				<excludes>
					<exclude>**/*.java</exclude>
				</excludes>
			</resource>
		</resources>

		<plugins>
			<plugin>
				<groupId>org.codehaus.mojo</groupId>
				<artifactId>build-helper-maven-plugin</artifactId>
				<version>3.2.0</version>
				<executions>
					<execution>
						<id>add-source</id>
						<phase>generate-sources</phase>
						<goals>
							<goal>add-source</goal>
						</goals>
						<configuration>
							<sources>
								<source>src/main/java-pcm/</source>
							</sources>
						</configuration>
					</execution>
				</executions>
			</plugin>

			<plugin>
				<artifactId>maven-compiler-plugin</artifactId>
				<version>3.6.0</version>
				<configuration>
					<source>1.8</source>
					<target>1.8</target>
				</configuration>
			</plugin>
			<plugin>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-maven-plugin</artifactId>
				<version>2.5.15</version> <!-- Use a version compatible with your Java version (here Java 8) -->
				<executions>
					<execution>
						<goals>
							<goal>repackage</goal>
							<goal>build-info</goal>
						</goals>
					</execution>
				</executions>
			</plugin>
			<plugin>
				<groupId>org.wildfly.plugins</groupId>
				<artifactId>wildfly-maven-plugin</artifactId>
				<version>2.0.0.Final</version>
			</plugin>
			<!--
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-checkstyle-plugin</artifactId>
                <version>3.1.1</version>
                <executions>
                    <execution>
                        <id>verify-style</id>
                        <phase>process-classes</phase>
                        <goals>
                            <goal>check</goal>
                        </goals>
                    </execution>
                </executions>
                <configuration>
                    <logViolationsToConsole>true</logViolationsToConsole>
                    <checkstyleRules>
                        <module name="Checker">
                            <module name="FileLength">
                                <property name="max" value="3500" />
                                <property name="fileExtensions" value="java"/>
                            </module>
                            <module name="FileTabCharacter"/>
                            <module name="TreeWalker">
                                <module name="StaticVariableName"/>
                                <module name="TypeName">
                                    <property name="format" value="^_?[A-Z][a-zA-Z0-9]*$"/>
                                </module>
                            </module>
                        </module>
                    </checkstyleRules>
                </configuration>
            </plugin>
             -->

			<!-- Install jar files: swt-jradius-1.1.5.jar and swt-controller.jar -->
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-install-plugin</artifactId>
				<executions>
					<execution>
						<id>install-swt-jradius-1.1.5.jar</id>
						<phase>validate</phase>
						<configuration>
							<file>${libdir}/swt-jradius-1.1.5.jar</file>
							<groupId>org.swallow</groupId>
							<artifactId>SwtJradius</artifactId>
							<version>1.0</version>
							<packaging>jar</packaging>
							<generatePom>true</generatePom>
						</configuration>
						<goals>
							<goal>install-file</goal>
						</goals>
					</execution>

					<execution>
						<id>install-swt-controller.jar</id>
						<phase>validate</phase>
						<configuration>
							<file>${libdir}/swt-controller.jar</file>
							<groupId>org.swallow</groupId>
							<artifactId>SwtController</artifactId>
							<version>1.0</version>
							<packaging>jar</packaging>
							<generatePom>true</generatePom>

						</configuration>
						<goals>
							<goal>install-file</goal>
						</goals>
					</execution>

				</executions>
			</plugin>

			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-antrun-plugin</artifactId>
				<version>1.8</version>
				<executions>
					<execution>
						<phase>package</phase>
						<goals>
							<goal>run</goal>
						</goals>
						<configuration>
							<target>
								<mkdir dir="${project.build.directory}/war/swallowtech.war"/>
								<unzip src="${project.build.directory}/swallowtech.war"
									   dest="${project.build.directory}/war/swallowtech.war"/>
								<delete file="${project.build.directory}/swallowtech.war.original"/>
								<delete dir="${project.build.directory}/swallowtech"/>
							</target>
						</configuration>
					</execution>
				</executions>
			</plugin>
		</plugins>


		<defaultGoal>clean install</defaultGoal>
	</build>


</project>
