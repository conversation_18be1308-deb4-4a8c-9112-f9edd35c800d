package org.swallow.pcm.report.model;

import java.io.Serializable;
import java.util.Date;
import java.util.HashMap;

import javax.servlet.http.HttpServletRequest;

public class PCReport implements Serializable {
	
	private String reportType = null;
	private String outputFormat = null;
	private String entityId = null;
	private String entityName = null;
	private String currencyCode = null;
	private String currencyName = null;
	private String accountGroup = null;
	private String accountGroupName = null;
	private String source = null;
	private String sourceName = null;
	private boolean singleDate = true;
	private Date fromDate = null;
	private Date toDate = null;
	private String useCurrencyMultiplier = null;
	private String applyCcyThreshold = null;
	private String spreadOnly = null;
	private String showValueOrVolume = null;
	private String userId = null;
	private String status = null;
	private String dateAsIso = null;
	private String blockReason = null;
	private String initialFilter = null;
	private String userFilter = null;
	private String refFilter = null;
	private String order = null;
	private String ascDesc = null;
	private String rowBegin = null;
	private String rowEnd = null;
	private String archive = null;
	private String Account = null;
	private String ccyTime = null;
	private String inputSince = null;
	
	
	
	private String selectedFilter = null;
	private String selectedSort = null;
	private String queryToExecute = null;
	private Integer currentPage = null;
	private boolean printAllPages = false; 
	private Integer recordsPerPage = 100; 
	
	

	public String getSelectedFilter() {
		return selectedFilter;
	}

	public void setSelectedFilter(String selectedFilter) {
		this.selectedFilter = selectedFilter;
	}

	public String getSelectedSort() {
		return selectedSort;
	}

	public void setSelectedSort(String selectedSort) {
		this.selectedSort = selectedSort;
	}

	public String getQueryToExecute() {
		return queryToExecute;
	}

	public void setQueryToExecute(String queryToExecute) {
		this.queryToExecute = queryToExecute;
	}

	public Integer getCurrentPage() {
		return currentPage;
	}

	public void setCurrentPage(Integer currentPage) {
		this.currentPage = currentPage;
	}

	public boolean isPrintAllPages() {
		return printAllPages;
	}

	public void setPrintAllPages(boolean printAllPages) {
		this.printAllPages = printAllPages;
	}

	public Integer getRecordsPerPage() {
		return recordsPerPage;
	}

	public void setRecordsPerPage(Integer recordsPerPage) {
		this.recordsPerPage = recordsPerPage;
	}
	public String getShowValueOrVolume() {
		return showValueOrVolume;
	}

	public void setShowValueOrVolume(String showValueOrVolume) {
		this.showValueOrVolume = showValueOrVolume;
	}

	private HttpServletRequest request;
	private String dateFormatValue = null;
	
	
	
	private HashMap<String, String> dictionary = new HashMap<String, String>();

	public String getReportType() {
		return reportType;
	}

	public void setReportType(String reportType) {
		this.reportType = reportType;
	}

	public String getOutputFormat() {
		return outputFormat;
	}

	public void setOutputFormat(String outputFormat) {
		this.outputFormat = outputFormat;
	}

	public String getEntityId() {
		return entityId;
	}

	public void setEntityId(String entityId) {
		this.entityId = entityId;
	}

	public String getEntityName() {
		return entityName;
	}

	public void setEntityName(String entityName) {
		this.entityName = entityName;
	}

	public String getCurrencyCode() {
		return currencyCode;
	}

	public void setCurrencyCode(String currencyCode) {
		this.currencyCode = currencyCode;
	}

	public String getCurrencyName() {
		return currencyName;
	}

	public void setCurrencyName(String currencyName) {
		this.currencyName = currencyName;
	}

	public String getAccountGroup() {
		return accountGroup;
	}

	public void setAccountGroup(String accountGroup) {
		this.accountGroup = accountGroup;
	}

	public String getAccountGroupName() {
		return accountGroupName;
	}

	public void setAccountGroupName(String accountGroupName) {
		this.accountGroupName = accountGroupName;
	}

	public String getSource() {
		return source;
	}

	public void setSource(String source) {
		this.source = source;
	}

	public String getSourceName() {
		return sourceName;
	}

	public void setSourceName(String sourceName) {
		this.sourceName = sourceName;
	}

	public boolean isSingleDate() {
		return singleDate;
	}

	public void setSingleDate(boolean singleDate) {
		this.singleDate = singleDate;
	}

	public Date getFromDate() {
		return fromDate;
	}

	public void setFromDate(Date fromDate) {
		this.fromDate = fromDate;
	}

	public Date getToDate() {
		return toDate;
	}

	public void setToDate(Date toDate) {
		this.toDate = toDate;
	}



	public HashMap<String, String> getDictionary() {
		return dictionary;
	}

	public void setDictionary(HashMap<String, String> dictionary) {
		this.dictionary = dictionary;
	}

	public HttpServletRequest getRequest() {
		return request;
	}

	public void setRequest(HttpServletRequest request) {
		this.request = request;
	}

	public String getDateFormatValue() {
		return dateFormatValue;
	}

	public void setDateFormatValue(String dateFormatValue) {
		this.dateFormatValue = dateFormatValue;
	}

	public String getApplyCcyThreshold() {
		return applyCcyThreshold;
	}

	public void setApplyCcyThreshold(String applyCcyThreshold) {
		this.applyCcyThreshold = applyCcyThreshold;
	}

	public String getUseCurrencyMultiplier() {
		return useCurrencyMultiplier;
	}

	public void setUseCurrencyMultiplier(String useCurrencyMultiplier) {
		this.useCurrencyMultiplier = useCurrencyMultiplier;
	}

	public String getSpreadOnly() {
		return spreadOnly;
	}

	public void setSpreadOnly(String spreadOnly) {
		this.spreadOnly = spreadOnly;
	}

	public String getUserId() {
		return userId;
	}

	public void setUserId(String userId) {
		this.userId = userId;
	}

	public String getAccount() {
		return Account;
	}

	public void setAccount(String account) {
		Account = account;
	}

	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	public String getDateAsIso() {
		return dateAsIso;
	}

	public void setDateAsIso(String dateAsIso) {
		this.dateAsIso = dateAsIso;
	}

	public String getBlockReason() {
		return blockReason;
	}

	public void setBlockReason(String blockReason) {
		this.blockReason = blockReason;
	}

	public String getInitialFilter() {
		return initialFilter;
	}

	public void setInitialFilter(String initialFilter) {
		this.initialFilter = initialFilter;
	}

	public String getRefFilter() {
		return refFilter;
	}

	public void setRefFilter(String refFilter) {
		this.refFilter = refFilter;
	}

	public String getUserFilter() {
		return userFilter;
	}

	public void setUserFilter(String userFilter) {
		this.userFilter = userFilter;
	}

	public String getAscDesc() {
		return ascDesc;
	}

	public void setAscDesc(String ascDesc) {
		this.ascDesc = ascDesc;
	}

	public String getOrder() {
		return order;
	}

	public void setOrder(String order) {
		this.order = order;
	}

	public String getRowBegin() {
		return rowBegin;
	}

	public void setRowBegin(String rowBegin) {
		this.rowBegin = rowBegin;
	}

	public String getRowEnd() {
		return rowEnd;
	}

	public void setRowEnd(String rowEnd) {
		this.rowEnd = rowEnd;
	}

	public String getArchive() {
		return archive;
	}

	public void setArchive(String archive) {
		this.archive = archive;
	}

	public String getCcyTime() {
		return ccyTime;
	}

	public void setCcyTime(String ccyTime) {
		this.ccyTime = ccyTime;
	}

	public String getInputSince() {
		return inputSince;
	}

	public void setInputSince(String inputSince) {
		this.inputSince = inputSince;
	}
	
}
