/*
 * @(#)ReportDAO.java 1.0 25/06/2019
 *
 * Copyright (c) 2006-2019 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.pcm.report.dao;

import org.swallow.dao.DAO;
import org.swallow.exception.SwtException;
import org.swallow.pcm.report.model.PCReport;
import org.swallow.work.model.QueryResult;

import net.sf.jasperreports.engine.JasperPrint;

/**
 * <AUTHOR>
 * 
 */
public interface ReportDAO extends DAO {
	
	public JasperPrint getBlockPaymentReport(PCReport pcReport) throws SwtException;

	public JasperPrint getPCMDashbordMonitorReport(PCReport pcReport) throws SwtException;

	public QueryResult getQueryResultPage(PCReport pcReport) throws SwtException;
}
