/*
 * @(#)PaymentLog.java 1.0 2019-05-27
 *
 * Copyright (c) 2006-2019 SwallowTech, Inc.
 * 14 Lion Yard, Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.pcm.work.model;

import java.util.Date;
import java.util.Hashtable;

import org.swallow.model.BaseObject;

/**
 * 
 * <AUTHOR> 
 * 
 * Class contain setter,getter methods. These methods used to getting 
 * and setting the values 
 * 
 */
public class PaymentLog extends BaseObject implements org.swallow.model.AuditComponent {
	public static Hashtable  logTable = new Hashtable();
	static {
		logTable.put("logSeq","Log Sequence");
	}
	
	private static final long serialVersionUID = 1L;
	private Long logSeq;
	private Long payreqId;
	private Date logDate;
	private String logUser;
	private String logDetails;
	public Long getLogSeq() {
		return logSeq;
	}
	public void setLogSeq(Long logSeq) {
		this.logSeq = logSeq;
	}
	public Long getPayreqId() {
		return payreqId;
	}
	public void setPayreqId(Long payreqId) {
		this.payreqId = payreqId;
	}
	public Date getLogDate() {
		return logDate;
	}
	public void setLogDate(Date logDate) {
		this.logDate = logDate;
	}
	public String getLogUser() {
		return logUser;
	}
	public void setLogUser(String logUser) {
		this.logUser = logUser;
	}
	public String getLogDetails() {
		return logDetails;
	}
	public void setLogDetails(String logDetails) {
		this.logDetails = logDetails;
	}
	
	
}
