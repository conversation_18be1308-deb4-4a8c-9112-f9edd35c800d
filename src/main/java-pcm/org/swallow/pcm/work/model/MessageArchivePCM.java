/*
 * @(#)InputExceptionsDataModel.java 1.0 23/08/06
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */package org.swallow.pcm.work.model;


import java.sql.Clob;
import java.text.NumberFormat;
import java.text.SimpleDateFormat;
import java.util.Date;

import org.swallow.model.BaseObject;

public class MessageArchivePCM extends BaseObject {

	private Long seqNo;
	private Integer mediumType;
	private String mediumInstance  				= "";
	private Integer messageDirection;
	private String messageFormat  				= "";
	private String messageType  				= "";
	private Date   startTime;
	private Date   finishTime;
	private String status  						= "";
	private String statusNotes  				= "";
	private Clob   messageBody;
	private String messageCategory  			= "";
	private String transactionType  			= "";
	private String transactionReferenceNumber1	= "";
	private String transactionReferenceNumber2  = "";
	private String transactionReferenceNumber3  = "";
	private String transactionReferenceNumber4  = "";
	private Date   valueDate;
	private String currencyCode1  				= "";
	private String currencyCode2  				= "";
	private String currencyCode3  				= "";
	private String currencyCode4  				= "";
	private Double amount1						= 0d;
	private Double amount2						= 0d;
	private Double amount3						= 0d;
	private Double amount4						= 0d;
	private String amountSign1  				= "";
	private String amountSign2  				= "";
	private String amountSign3  				= "";
	private String amountSign4  				= "";
	private Integer rate1;
	private Integer rate2;
	private Integer rate3;
	private Integer rate4;
	private String orderingInstitution  		= "";
	private String sendersCorrespondent  		= "";
	private String receiversCorrespondent  		= "";
	private String accountWithInstitution  		= "";
	private String senderToReceiverInformation  = "";
	private String orderingCustomer  			= "";
	private String party1  						= "";
	private String party2  						= "";
	private String party3  						= "";
	private String party4  						= "";
	private String pcPaymentId  				= "";
	private String accountId  					= "";
	
	
	/**
	 * Getter method for seqNo 
	 * @return int
	 */
	public Long getSeqNo() {
		return seqNo;
	}
	/**
	 * Setter method for seqNo 
	 * @param seqNo 
	 * @return
	 */
	public void setSeqNo(Long seqNo) {
		this.seqNo = seqNo;
	}
	/**
	 * Getter method for mediumType 
	 * @return int
	 */
	public Integer getMediumType() {
		if(mediumType==null){
			return 0;
		}else {
			return mediumType;
		}
	}
	/**
	 * Setter method for mediumType 
	 * @param mediumType 
	 * @return
	 */
	public void setMediumType(Integer mediumType) {
		this.mediumType = mediumType;
	}
	/**
	 * Getter method for mediumInstance 
	 * @return String
	 */
	public String getMediumInstance() {
		return mediumInstance;
	}
	/**
	 * Setter method for mediumInstance 
	 * @param mediumInstance 
	 * @return
	 */
	public void setMediumInstance(String mediumInstance) {
		this.mediumInstance = mediumInstance;
	}
	/**
	 * Getter method for messageDirection 
	 * @return int
	 */
	public Integer getMessageDirection() {
		if(messageDirection==null){
			return 0;
		}else {
			return messageDirection;
		}
	}
	/**
	 * Setter method for messageDirection 
	 * @param messageDirection 
	 * @return
	 */
	public void setMessageDirection(Integer messageDirection) {
		this.messageDirection = messageDirection;
	}
	/**
	 * Getter method for messageFormat 
	 * @return String
	 */
	public String getMessageFormat() {
		return messageFormat;
	}
	/**
	 * Setter method for messageFormat 
	 * @param messageFormat 
	 * @return
	 */
	public void setMessageFormat(String messageFormat) {
		this.messageFormat = messageFormat;
	}
	/**
	 * Getter method for messageType 
	 * @return String
	 */
	public String getMessageType() {
		return messageType;
	}
	/**
	 * Setter method for messageType 
	 * @param messageType 
	 * @return
	 */
	public void setMessageType(String messageType) {
		this.messageType = messageType;
	}
	/**
	 * Getter method for startTime 
	 * @return Date
	 */
	public Date getStartTime() {
		return startTime;
	}
	/**
	 * Setter method for startTime 
	 * @param startTime 
	 * @return
	 */
	public void setStartTime(Date startTime) {
		this.startTime = startTime;
	}
	/**
	 * Getter method for finishTime 
	 * @return Date
	 */
	public Date getFinishTime() {
		return finishTime;
	}
	/**
	 * Setter method for finishTime 
	 * @param finishTime 
	 * @return
	 */
	public void setFinishTime(Date finishTime) {
		this.finishTime = finishTime;
	}
	/**
	 * Getter method for status 
	 * @return String
	 */
	public String getStatus() {
		return status;
	}
	/**
	 * Setter method for status 
	 * @param status 
	 * @return
	 */
	public void setStatus(String status) {
		this.status = status;
	}
	/**
	 * Getter method for statusNotes
	 * @return String
	 */
	public String getStatusNotes() {
		return statusNotes;
	}
	/**
	 * Setter method for statusNotes
	 * @param statusNotes
	 * @return
	 */
	public void setStatusNotes(String statusNotes) {
		this.statusNotes = statusNotes;
	}
	/**
	 * Getter method for messageBody
	 * @return String
	 */
	public Clob getMessageBody() {
		return messageBody;
	}
	/**
	 * Setter method for messageBody
	 * @param messageBody
	 * @return
	 */
	public void setMessageBody(Clob messageBody) {
		this.messageBody = messageBody;
	}
	/**
	 * Getter method for messageCategory
	 * @return String
	 */
	public String getMessageCategory() {
		return messageCategory;
	}
	/**
	 * Setter method for messageCategory
	 * @param messageCategory
	 * @return
	 */
	public void setMessageCategory(String messageCategory) {
		this.messageCategory = messageCategory;
	}
	/**
	 * Getter method for transactionType
	 * @return String
	 */
	public String getTransactionType() {
		return transactionType;
	}
	/**
	 * Setter method for transactionType
	 * @param transactionType
	 * @return
	 */
	public void setTransactionType(String transactionType) {
		this.transactionType = transactionType;
	}
	/**
	 * Getter method for transactionReferenceNumber1
	 * @return String
	 */
	public String getTransactionReferenceNumber1() {
		return transactionReferenceNumber1;
	}
	/**
	 * Setter method for transactionReferenceNumber1
	 * @param transactionReferenceNumber1
	 * @return
	 */
	public void setTransactionReferenceNumber1(String transactionReferenceNumber1) {
		this.transactionReferenceNumber1 = transactionReferenceNumber1;
	}
	/**
	 * Getter method for transactionReferenceNumber2
	 * @return String
	 */
	public String getTransactionReferenceNumber2() {
		return transactionReferenceNumber2;
	}
	/**
	 * Setter method for transactionReferenceNumber2
	 * @param transactionReferenceNumber2
	 * @return
	 */
	public void setTransactionReferenceNumber2(String transactionReferenceNumber2) {
		this.transactionReferenceNumber2 = transactionReferenceNumber2;
	}
	/**
	 * Getter method for transactionReferenceNumber3
	 * @return String
	 */
	public String getTransactionReferenceNumber3() {
		return transactionReferenceNumber3;
	}
	/**
	 * Setter method for transactionReferenceNumber3
	 * @param transactionReferenceNumber3
	 * @return
	 */
	public void setTransactionReferenceNumber3(String transactionReferenceNumber3) {
		this.transactionReferenceNumber3 = transactionReferenceNumber3;
	}
	/**
	 * Getter method for transactionReferenceNumber4
	 * @return String
	 */
	public String getTransactionReferenceNumber4() {
		return transactionReferenceNumber4;
	}
	/**
	 * Setter method for transactionReferenceNumber4
	 * @param transactionReferenceNumber4
	 * @return
	 */
	public void setTransactionReferenceNumber4(String transactionReferenceNumber4) {
		this.transactionReferenceNumber4 = transactionReferenceNumber4;
	}
	/**
	 * Getter method for valueDate
	 * @return Date
	 */
	public Date getValueDate() {
		return valueDate;
	}
	/**
	 * Setter method for valueDate
	 * @param valueDate
	 * @return
	 */
	public void setValueDate(Date valueDate) {
		this.valueDate = valueDate;
	}
	/**
	 * Getter method for currencyCode1
	 * @return String
	 */
	public String getCurrencyCode1() {
		return currencyCode1;
	}
	/**
	 * Setter method for currencyCode1
	 * @param currencyCode1
	 * @return
	 */
	public void setCurrencyCode1(String currencyCode1) {
		this.currencyCode1 = currencyCode1;
	}
	/**
	 * Getter method for currencyCode2
	 * @return String
	 */
	public String getCurrencyCode2() {
		return currencyCode2;
	}
	/**
	 * Setter method for currencyCode2
	 * @param currencyCode2
	 * @return
	 */
	public void setCurrencyCode2(String currencyCode2) {
		this.currencyCode2 = currencyCode2;
	}
	/**
	 * Getter method for currencyCode3
	 * @return String
	 */
	public String getCurrencyCode3() {
		return currencyCode3;
	}
	/**
	 * Setter method for currencyCode3
	 * @param currencyCode3
	 * @return
	 */
	public void setCurrencyCode3(String currencyCode3) {
		this.currencyCode3 = currencyCode3;
	}
	/**
	 * Getter method for currencyCode4
	 * @return String
	 */
	public String getCurrencyCode4() {
		return currencyCode4;
	}
	/**
	 * Setter method for currencyCode4
	 * @param currencyCode4 
	 * @return
	 */
	public void setCurrencyCode4(String currencyCode4) {
		this.currencyCode4 = currencyCode4;
	}
	/**
	 * Getter method for amount1
	 * @return Double
	 */
	public Double getAmount1() {
		return amount1;
	}
	/**
	 * Setter method for amount4
	 * @param amount4 
	 * @return
	 */
	public void setAmount1 (Double amount1) {
		this.amount1 = amount1;
	}
	/**
	 * Getter method for amount2
	 * @return Double
	 */
	public Double getAmount2 () {
		return amount2;
	}
	/**
	 * Setter method for amount2
	 * @param amount2
	 * @return
	 */
	public void setAmount2(Double amount2) {
		this.amount2 = amount2;
	}
	/**
	 * Getter method for amount3
	 * @return Double
	 */
	public Double getAmount3() {
		return amount3;
	}
	/**
	 * Setter method for amount3
	 * @param amount3 
	 * @return
	 */
	public void setAmount3(Double amount3) {
		this.amount3 = amount3;
	}
	/**
	 * Getter method for amount4
	 * @return Double
	 */
	public Double getAmount4() {
		return amount4;
	}
	/**
	 * Setter method for amount4
	 * @param amount4 
	 * @return
	 */
	public void setAmount4(Double amount4) {
		this.amount4 = amount4;
	}
	/**
	 * Getter method for amountSign1
	 * @return String
	 */
	public String getAmountSign1() {
		return amountSign1;
	}
	/**
	 * Setter method for amountSign1
	 * @param amountSign1 
	 * @return
	 */
	public void setAmountSign1(String amountSign1) {
		this.amountSign1 = amountSign1;
	}
	/**
	 * Getter method for amountSign2
	 * @return String
	 */
	public String getAmountSign2() {
		return amountSign2;
	}
	/**
	 * Setter method for amountSign2
	 * @param amountSign2 
	 * @return
	 */
	public void setAmountSign2(String amountSign2) {
		this.amountSign2 = amountSign2;
	}
	/**
	 * Getter method for amountSign3
	 * @return String
	 */
	public String getAmountSign3() {
		return amountSign3;
	}
	/**
	 * Setter method for amountSign3
	 * @param amountSign3 
	 * @return
	 */
	public void setAmountSign3(String amountSign3) {
		this.amountSign3 = amountSign3;
	}
	/**
	 * Getter method for amountSign4
	 * @return String
	 */
	public String getAmountSign4() {
		return amountSign4;
	}
	/**
	 * Setter method for amountSign4
	 * @param amountSign4 
	 * @return
	 */
	public void setAmountSign4(String amountSign4) {
		this.amountSign4 = amountSign4;
	}
	/**
	 * Getter method for Rate1
	 * @return int
	 */
	public Integer getRate1() {
		if(rate1==null){
			return 0;
		}else {
			return rate1;
		}
	}
	/**
	 * Setter method for rate1
	 * @param rate1 
	 * @return
	 */
	public void setRate1(Integer rate1) {
		this.rate1 = rate1;
	}
	/**
	 * Getter method for Rate2
	 * @return int
	 */
	public Integer getRate2() {
		if(rate2==null){
			return 0;
		}else {
			return rate2;
		}
	}
	/**
	 * Setter method for rate2
	 * @param rate2 
	 * @return
	 */
	public void setRate2(Integer rate2) {
		this.rate2 = rate2;
	}
	/**
	 * Getter method for Rate3
	 * @return int
	 */
	public Integer getRate3() {
		if(rate3==null){
			return 0;
		}else {
			return rate3;
		}
	}
	/**
	 * Setter method for rate3
	 * @param rate3 
	 * @return
	 */
	public void setRate3(Integer rate3) {
		this.rate3 = rate3;
	}
	/**
	 * Getter method for Rate4
	 * @return int
	 */
	public Integer getRate4() {
		if(rate4==null){
			return 0;
		}else {
			return rate4;
		}
	}
	/**
	 * Setter method for rate4
	 * @param rate4 
	 * @return
	 */
	public void setRate4(Integer rate4) {
		this.rate4 = rate4;
	}
	/**
	 * Getter method for orderingInstitution
	 * @return String
	 */
	public String getOrderingInstitution() {
		return orderingInstitution;
	}
	/**
	 * Setter method for orderingInstitution
	 * @param orderingInstitution 
	 * @return
	 */
	public void setOrderingInstitution(String orderingInstitution) {
		this.orderingInstitution = orderingInstitution;
	}
	/**
	 * Getter method for sendersCorrespondent
	 * @return String
	 */
	public String getSendersCorrespondent() {
		return sendersCorrespondent;
	}
	/**
	 * Setter method for sendersCorrespondent
	 * @param sendersCorrespondent 
	 * @return
	 */
	public void setSendersCorrespondent(String sendersCorrespondent) {
		this.sendersCorrespondent = sendersCorrespondent;
	}
	/**
	 * Getter method for receiversCorrespondent
	 * @return String
	 */
	public String getReceiversCorrespondent() {
		return receiversCorrespondent;
	}
	/**
	 * Setter method for receiversCorrespondent
	 * @param receiversCorrespondent 
	 * @return
	 */
	public void setReceiversCorrespondent(String receiversCorrespondent) {
		this.receiversCorrespondent = receiversCorrespondent;
	}
	/**
	 * Getter method for accountWithInstitution
	 * @return String
	 */
	public String getAccountWithInstitution() {
		return accountWithInstitution;
	}
	/**
	 * Setter method for accountWithInstitution
	 * @param accountWithInstitution 
	 * @return
	 */
	public void setAccountWithInstitution(String accountWithInstitution) {
		this.accountWithInstitution = accountWithInstitution;
	}
	/**
	 * Getter method for senderToReceiverInformation
	 * @return String
	 */
	public String getSenderToReceiverInformation() {
		return senderToReceiverInformation;
	}
	/**
	 * Setter method for senderToReceiverInformation
	 * @param senderToReceiverInformation
	 */
	public void setSenderToReceiverInformation(String senderToReceiverInformation) {
		this.senderToReceiverInformation = senderToReceiverInformation;
	}
	/**
	 * Getter method for orderingCustomer
	 * @return String
	 */
	public String getOrderingCustomer() {
		return orderingCustomer;
	}
	/**
	 * Setter method for orderingCustomer
	 * @param orderingCustomer
	 * @return
	 */
	public void setOrderingCustomer(String orderingCustomer) {
		this.orderingCustomer = orderingCustomer;
	}
	/**
	 * Getter method for party1
	 * @return String
	 */
	public String getParty1() {
		return party1;
	}
	/**
	 * Setter method for party1
	 * @param party1
	 * @return 
	 */
	public void setParty1(String party1) {
		this.party1 = party1;
	}
	/**
	 * Getter method for party2
	 * @return String
	 */
	public String getParty2() {
		return party2;
	}
	/**
	 * Setter method for party2 
	 * @param party2
	 * @return
	 */
	public void setParty2(String party2) {
		this.party2 = party2;
	}
	/**
	 * Getter method for party3
	 * @return String
	 */
	public String getParty3() {
		return party3;
	}
	/**
	 * Setter method for party3
	 * @param party3
	 * @return
	 */
	public void setParty3(String party3) {
		this.party3 = party3;
	}
	/**
	 * Getter method for party4
	 * @return String
	 */
	public String getParty4() {
		return party4;
	}
	/**
	 * Setter method for party4
	 * @param party4
	 * @return
	 */
	public void setParty4(String party4) {
		this.party4 = party4;
	}
	
	
	/**
	 * Getter Method for valueDateAsString
	 * @return String
	 */
	public String getValueDateAsString () {
		SimpleDateFormat df = new SimpleDateFormat ("yyyy-MM-dd");
		return getValueDate()==null?null:df.format (getValueDate());
	}
	
	/**
	 * Getter Method for startTimeAString
	 * @return String
	 */
	public String getStartTimeAsString () {
		SimpleDateFormat df = new SimpleDateFormat ("yyyy-MM-dd HH:mm");
		return getStartTime()==null?null:df.format (getStartTime());
	}
	
	/**
	 * Getter method for amount1AsString
	 * @return String
	 */
	public String getAmount1AsString () {
		NumberFormat fFormat = NumberFormat.getInstance ();
		fFormat.setMaximumFractionDigits (2);
		fFormat.setGroupingUsed (false);
		
		return getAmount1()==null?null:fFormat.format (getAmount1());
	}
	
	public String getPcPaymentId() {
		return pcPaymentId;
	}
	public void setPcPaymentId(String pcPaymentId) {
		this.pcPaymentId = pcPaymentId;
	}
	public String getAccountId() {
		return accountId;
	}
	public void setAccountId(String accountId) {
		this.accountId = accountId;
	}
}
