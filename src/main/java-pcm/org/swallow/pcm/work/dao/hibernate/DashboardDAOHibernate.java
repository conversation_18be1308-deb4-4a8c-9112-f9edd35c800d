/*
 * @(#)DashboardDAOHibernate.java 1.0 25/05/2019
 *
 * Copyright (c) 2006-2019 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.pcm.work.dao.hibernate;

import java.sql.CallableStatement;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.sql.Types;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.List;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.beans.factory.annotation.Qualifier;
import org.swallow.control.dao.hibernate.MaintenanceLogDAOHibernate;
import org.swallow.control.model.MaintenanceLog;
import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.maintenance.model.AcctMaintenance;
import org.swallow.model.ScreenInfo;
import org.swallow.pcm.maintenance.dao.hibernate.AccountGroupsMaintenanceDAOHibernate;
import org.swallow.pcm.maintenance.model.AccountGroup;
import org.swallow.pcm.maintenance.model.core.dashboardResult;
import org.swallow.pcm.work.dao.DashboardDAO;
import org.swallow.pcm.work.model.PaymentRequest;
import org.swallow.pcm.work.model.PaymentStop;
import org.swallow.util.JDBCCloser;
import org.swallow.util.LabelValueBean;
import org.swallow.util.SwtConstants;
import org.swallow.util.SwtInterceptor;
import org.swallow.util.SwtUtil;
import org.swallow.util.UserThreadLocalHolder;
import org.springframework.context.annotation.Lazy;
import org.springframework.orm.hibernate5.support.HibernateDaoSupport;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.stereotype.Repository;
import org.springframework.beans.factory.annotation.Autowired;
import org.hibernate.Hibernate;
import org.hibernate.HibernateException;
import org.hibernate.SessionFactory;
import org.hibernate.Transaction;
import org.hibernate.type.StandardBasicTypes;
import org.hibernate.type.StringType;
import org.hibernate.Session;







@Repository ("dashboardDAO")
@Transactional
public class DashboardDAOHibernate extends HibernateDaoSupport implements
		DashboardDAO {
	public DashboardDAOHibernate(@Lazy @Qualifier("sessionFactoryPC") SessionFactory sessionfactory) {
	    setSessionFactory(sessionfactory);
	}


	// Final instance for log
	private final Log log = LogFactory.getLog(DashboardDAOHibernate.class);

	
	/**
	 * This is used to fetches details for the currency dashboard
	 * 
	 * @param listEntities
	 * @param valueDate
	 * @param applyCurrencyThreshold
	 * @param spreadOnly
	 * @param userId
	 * @param pv_xml_out
	 * @return Collection
	 * @throws SwtException
	 */
	
	public String[] currencyDashboardDetails(String listEntities, String valueDate,
			String applyCurrencyThreshold, String spreadOnly, String userId, String volume, String multiplier) throws SwtException {
		/* Method's local variable declaration */
		Session session = null;
		Connection conn=null;
		CallableStatement pstmt=null;
		String result = null;
		String status = null;
		String inputSince = null;
		String[] resultArray = new String[3];
		try {
			
			log.debug(this.getClass().getName()
					+ " - [currencyDashboardDetails] - " + "Entry");

			/*
			 * Returns the HibernateTemplate and session factory for this DAO,
			 * and opening a Hibernate Session
			 */
			session = getHibernateTemplate().getSessionFactory().openSession();
			conn = SwtUtil.connection(session);
			/* Using Callable statement to execute the Stored Procedure */
			pstmt=conn.prepareCall("{call PKG_PC_DASHBOARD.sp_currency_dashboard(?,?,?,?,?,?,?,?,?, ?)}");
			pstmt.setString(1,listEntities);
			pstmt.setString(2,valueDate);
			pstmt.setString(3,applyCurrencyThreshold);
			pstmt.setString(4,spreadOnly);
			pstmt.setString(5,userId);
			pstmt.setString(6,volume);
			pstmt.setString(7,multiplier);
			pstmt.registerOutParameter(8, oracle.jdbc.OracleTypes.CLOB);
			pstmt.registerOutParameter(9,oracle.jdbc.OracleTypes.CHAR);
			pstmt.registerOutParameter(10,oracle.jdbc.OracleTypes.DATE);
		
			pstmt.execute();
			/* Fetching the Result */
			result =  pstmt.getString(8);
			resultArray[0]= result;
			status =  pstmt.getString(9);
			resultArray[1] = status;
			inputSince =  pstmt.getDate(10).toString();
			resultArray[2] = inputSince;
			
		} catch (Exception e) {
			log
					.error(this.getClass().getName()
							+ " - Exception Catched in [currencyDashboardDetails] method : - "
							+ e.getMessage());
			 throw SwtErrorHandler.getInstance().handleException(e, "currencyDashboardDetails", DashboardDAOHibernate.class);
		} finally {
			
			SwtException thrownException = null;
			Object[] exceptions = JDBCCloser.close(null,pstmt, conn, session);

			if (exceptions[0] != null)
				thrownException = SwtErrorHandler.getInstance()
						.handleException((SQLException) exceptions[0],
								"currencyDashboardDetails",
								DashboardDAOHibernate.class);
		
			if (thrownException == null && exceptions[1] != null)
				thrownException = SwtErrorHandler.getInstance()
						.handleException((HibernateException) exceptions[1],
								"currencyDashboardDetails",
								DashboardDAOHibernate.class);

			if (thrownException != null)
				throw thrownException;
			// nullify objects
			result = null;
		}
		return resultArray;
	}
	

	
	public dashboardResult dataGridDetails(String listEntities, String valueDate, String highValue, String currencyMultiplier, String status,  String blockedReason, String currencyCode, 
			String accountGroupId, String accountId, String userId, String initialFilter, String userFilter, String refFilter, String spreadOnly,
			String order, String ascDesc, String  rowbegin, String rowEnd, String archive, String ccyTime, String inputSince) throws SwtException {
		    /* Method's local variable declaration */

		    Session session = null;
		    Connection conn=null;
		    CallableStatement pstmt=null;
		    dashboardResult result = new dashboardResult();
		    
		    try {
			      
			      log.debug(this.getClass().getName()
		          + " - [dataGridDetails] - " + "Entry");
			
		      /*
		       * Returns the HibernateTemplate and session factory for this DAO,
		       * and opening a Hibernate Session
		       */
		      session = getHibernateTemplate().getSessionFactory().openSession();
		      conn = SwtUtil.connection(session);
		      /* Using Callable statement to execute the Stored Procedure */
		      pstmt=conn.prepareCall("{call PKG_PC_DASHBOARD.sp_pr_datagrid(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)}");
		      pstmt.setString(1,listEntities);
		      pstmt.setString(2,valueDate);
		      pstmt.setString(3,highValue);
		      pstmt.setString(4,currencyMultiplier);
		      pstmt.setString(5,status);
		      pstmt.setString(6,blockedReason);
		      pstmt.setString(7,currencyCode);
		      pstmt.setString(8,accountGroupId);
		      pstmt.setString(9,accountId);
		      pstmt.setString(10,userId);
		      pstmt.setString(11,initialFilter);
		      pstmt.setString(12,userFilter);
		      pstmt.setString(13,refFilter);
		      pstmt.setString(14,spreadOnly);
		      pstmt.setString(15,order);
		      pstmt.setString(16,ascDesc);
		      pstmt.setString(17, rowbegin);
		      pstmt.setString(18,rowEnd);
		      pstmt.setString(19,archive);
		      pstmt.setString(20,ccyTime);
		      pstmt.setString(21,inputSince);
		    
		
		      pstmt.registerOutParameter(21, oracle.jdbc.OracleTypes.VARCHAR);
		      pstmt.registerOutParameter(22, oracle.jdbc.OracleTypes.VARCHAR);
		      pstmt.registerOutParameter(23, oracle.jdbc.OracleTypes.VARCHAR);
		      pstmt.registerOutParameter(24, oracle.jdbc.OracleTypes.VARCHAR);
		      pstmt.registerOutParameter(25, oracle.jdbc.OracleTypes.VARCHAR);
		      pstmt.registerOutParameter(26, oracle.jdbc.OracleTypes.VARCHAR);
		      pstmt.registerOutParameter(27, oracle.jdbc.OracleTypes.VARCHAR);
		      pstmt.registerOutParameter(28, oracle.jdbc.OracleTypes.VARCHAR);
		      pstmt.registerOutParameter(29, oracle.jdbc.OracleTypes.VARCHAR);
		      
		      pstmt.registerOutParameter(30, oracle.jdbc.OracleTypes.CLOB);
		    
		      pstmt.execute();
		      result.setInputSince(pstmt.getString(21));
		      result.setSodBalance(pstmt.getString(22));
		      result.setConfirmedCredit(pstmt.getString(23));
		      result.setCreditLine(pstmt.getString(24));
		      result.setReleasedPayments(pstmt.getString(25));
		      result.setFcastDr(pstmt.getString(26));
		      result.setAvailableLiqEx(pstmt.getString(27));
		      result.setAvailabelLiqInc(pstmt.getString(28));
		      result.setReservedBalance(pstmt.getString(29));
		      result.setXmlOut((SwtUtil.isEmptyOrNull(pstmt.getString(30)) ? "" : pstmt.getString(30)));
		      
		    } catch (Exception e) {
		      log
		          .error(this.getClass().getName()
		              + " - Exception Catched in [dataGridDetails] method : - "
		              + e.getMessage());
		      throw SwtErrorHandler.getInstance().handleException(e, "dataGridDetails", DashboardDAOHibernate.class);
		    } finally {
		    	JDBCCloser.close(null, pstmt, conn, session);
		      
		    }
		    return result;
		  }
	
	public Collection unStopReleasePayment(ArrayList paymentReqId, ArrayList previousStatusId, String paymentAction,  String userId) throws SwtException {
		    /* Method's local variable declaration */

		    Session session = null;
		    Connection conn=null;
		    CallableStatement pstmt=null;
		    String result = null;
		    Collection errCodeId = null;
		    LabelValueBean L1;
		    
		    try {
		    	L1 = new LabelValueBean("", "");
		    	errCodeId = new ArrayList();
		      log.debug(this.getClass().getName()
		          + " - [unStopReleasePayment] - " + "Entry");
			
		      /*
		       * Returns the HibernateTemplate and session factory for this DAO,
		       * and opening a Hibernate Session
		       */
		      session = getHibernateTemplate().getSessionFactory().openSession();
		      conn = SwtUtil.connection(session);
		      /* Using Callable statement to execute the Stored Procedure */
		      pstmt=conn.prepareCall("{call PKG_PC_SCREEN_UTILITY.sp_release_unstop_paymt(?,?,?,?,?)}");
		      for (int i = 0; i < paymentReqId.size(); i++) {
			  int paymentReqIdNumber = Integer.parseInt(paymentReqId.get(i).toString());
			  String previousStatus = previousStatusId.get(i).toString();
		      pstmt.setInt(1,paymentReqIdNumber);
		      pstmt.setString(2,previousStatus);
		      pstmt.setString(3,paymentAction);
		      pstmt.setString(4,userId);
		   
		      pstmt.registerOutParameter(5, oracle.jdbc.OracleTypes.CLOB);
		      pstmt.execute();
		      //fetch result
		      result = pstmt.getString(5);
		      if(result.equals("ERR#NOT_FOUND") || result.equals("ERR#LOCK") || result.equals("ERR#STATUS") || result.equals("ERR#ACTION")) {
		    	  L1 = new LabelValueBean(paymentReqId.get(i).toString(), result);  
		    	  errCodeId.add(L1);
		      } 
   
		      }
		     
		  
		    } catch (Exception e) {
		      log
		          .error(this.getClass().getName()
		              + " - Exception Catched in [unStopReleasePayment] method : - "
		              + e.getMessage());
		      throw new SwtException(
						"errors.DataIntegrityViolationExceptioninAdd");
		    } finally {
		    	JDBCCloser.close(null, pstmt, conn, session);
		      
		    }
		    return errCodeId;
		  }
	
	
	public HashMap isBlockedStopped(ArrayList paymentReqId, String formatDate) throws SwtException {
		/* Method's local variable declaration */
		
		Session session = null;
		Connection conn=null;
		CallableStatement pstmt=null;
		String result = null;
		List arrayList = new ArrayList<Object[]>();  
		String[]  array;
		 array = new String[2];
			HashMap<String, String> idRule = new LinkedHashMap<String, String>();
		try {	
		   
		  // String[] array= new String[2];
			log.debug(this.getClass().getName()
					+ " - [isBlockedStopped] - " + "Entry");
			
			/*
			 * Returns the HibernateTemplate and session factory for this DAO,
			 * and opening a Hibernate Session
			 */
			session = getHibernateTemplate().getSessionFactory().openSession();
			conn = SwtUtil.connection(session);
			/* Using Callable statement to execute the Stored Procedure */
			pstmt=conn.prepareCall("{call PKG_PC_CHECK_RULES.is_blocked_pr_stopped(?,?,?)}");
			  for (int i = 0; i < paymentReqId.size(); i++) {
			  
			int paymentReqIdInt =  Integer.parseInt(paymentReqId.get(i).toString());
			
				pstmt.setInt(1,paymentReqIdInt);
				pstmt.setString(3,formatDate);
				pstmt.registerOutParameter(2, Types.VARCHAR);
				pstmt.execute();
				//fetch result
				result = pstmt.getString(2);
				if(result.indexOf("Y") > -1 || result.indexOf("P") > -1) {
					idRule.put(paymentReqId.get(i).toString(), result);
			 
			      } else if(result.equals("N")){
						idRule.put(paymentReqId.get(i).toString(), "N");
			      } else {
						idRule.put(paymentReqId.get(i).toString(), "L");

			      }
			  }
			
			
		} catch (Exception e) {
			log
			.error(this.getClass().getName()
					+ " - Exception Catched in [isBlockedStopped] method : - "
					+ e.getMessage());
			throw new SwtException(
					"errors.DataIntegrityViolationExceptioninAdd");
		} finally {
			JDBCCloser.close(null, pstmt, conn, session);
			
		}
		return idRule;
	}
	public String checkStopProcess() throws SwtException {
		/* Method's local variable declaration */
		
		Session session = null;
		Connection conn=null;
		CallableStatement pstmt=null;
		String result = null;
		
		try {	
			
			// String[] array= new String[2];
			log.debug(this.getClass().getName()
					+ " - [checkStopProcess] - " + "Entry");
			session = getHibernateTemplate().getSessionFactory().openSession();
			conn = SwtUtil.connection(session);
			pstmt=conn.prepareCall("{? = call PKG_PC_CHECK_RULES.is_stop_process_running()}");
		
				pstmt.registerOutParameter(1, oracle.jdbc.OracleTypes.VARCHAR);
				pstmt.execute();
				//fetch result
				result = pstmt.getString(1);
				
			
			
		} catch (Exception e) {
			log
			.error(this.getClass().getName()
					+ " - Exception Catched in [isBlockedStopped] method : - "
					+ e.getMessage());
			throw new SwtException(
					"errors.DataIntegrityViolationExceptioninAdd");
		} finally {
			JDBCCloser.close(null, pstmt, conn, session);
			
		}
		return result;
	}
	
	public Collection getAccountGrpDetails(String currency)
			throws SwtException {
		// Variable List to hold list Category Rules
		Collection accountGrpList = new ArrayList();
		String query= null;
		Session session = null;
		
		try {
			log.debug(this.getClass().getName()
					+ " - [getAccountGrpDetails] - " + "Entry");
			
			session= SwtUtil.pcSessionFactory.openSession();
			
			/* Assigning the query statement to the string object */
			  if (!currency.equals("All")) {
			        query = "from AccountGroup acct where acct.currencyCode = :currency and (acct.requireAuthorisation = 'N' OR acct.requireAuthorisation is null) order by lower(acct.id.accGrpId) asc";
			        accountGrpList = session.createQuery(query, AccountGroup.class)
	                .setParameter("currency", currency)
	                .getResultList();
			    } else {
			        query = "from AccountGroup acct where (acct.requireAuthorisation = 'N' OR acct.requireAuthorisation is null) order by lower(acct.id.accGrpId) asc";
			        accountGrpList = session.createQuery(query, AccountGroup.class)
		        		.getResultList();
			    }
			
			log.debug(this.getClass().getName()
					+ " - [getAccountGrpDetails] - " + "Exit");
			return accountGrpList;
			
		} catch (Exception exp) {
			// log error message
			log.error(this.getClass().getName()
					+ " - Exception Caught in [getAccountGrpDetails] method : - "
					+ exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp,
					"getAccountGrpDetails", this.getClass());
		} finally {
			// Nullify object
			accountGrpList = null;
			HibernateException hThrownException = JDBCCloser.close(session);
			if (hThrownException != null)
				throw new SwtException(hThrownException.getMessage());
			
			// Debug Message
			log.debug(this.getClass().getName() + "- [ getAccountGrpDetails ] - Exit");
		}
	}
	
	
	public Collection getAccountsDetails(String accountGrp, String currency)
			throws SwtException {
		// Variable List to hold list Category Rules
		Collection accountsList = new ArrayList();
		String query= null;
		Session session = null;
		
		try {
			log.debug(this.getClass().getName()
					+ " - [getAccountsDetails] - " + "Entry");
			
			session= SwtUtil.pcSessionFactory.openSession();
		    if (!accountGrp.equals("All") && currency.equals("All")) {
		        query = "select acct from AccountInGroup acctGrp join AcctMaintenance acct on acctGrp.id.accountId = acct.id.accountId and acctGrp.id.hostId = acct.id.hostId and acctGrp.id.entityId = acct.id.entityId where acctGrp.accGrpId = :accountGrp order by acct.id.accountId asc";
		        accountsList = session.createQuery(query, AcctMaintenance.class)
		                .setParameter("accountGrp", accountGrp, StringType.INSTANCE)
		                .getResultList();
		    } else if (currency.equals("All") && !accountGrp.equals("All")) {
		        query = "select acct from AccountInGroup acctGrp join AcctMaintenance acct on acctGrp.id.accountId = acct.id.accountId and acctGrp.id.hostId = acct.id.hostId and acctGrp.id.entityId = acct.id.entityId where acctGrp.accGrpId = :accountGrp order by acct.id.accountId asc";
		        accountsList = session.createQuery(query, AcctMaintenance.class)
		                .setParameter("accountGrp", accountGrp, StringType.INSTANCE)
		                .getResultList();
		    } else if (!currency.equals("All") && !accountGrp.equals("All")) {
		        query = "select acct from AccountInGroup acctGrp join AcctMaintenance acct on acctGrp.id.accountId = acct.id.accountId and acctGrp.id.hostId = acct.id.hostId and acctGrp.id.entityId = acct.id.entityId join AccountGroup accGrp on accGrp.id.accGrpId = acctGrp.accGrpId where accGrp.currencyCode = :currency and acctGrp.accGrpId = :accountGrp order by acct.id.accountId asc";
		        accountsList = session.createQuery(query, AcctMaintenance.class)
		                .setParameter("currency", currency, StringType.INSTANCE)
		                .setParameter("accountGrp", accountGrp, StringType.INSTANCE)
		                .getResultList();
		    } else {
		        query = "select acct from AccountInGroup acctGrp join AcctMaintenance acct on acctGrp.id.accountId = acct.id.accountId and acctGrp.id.hostId = acct.id.hostId and acctGrp.id.entityId = acct.id.entityId join AccountGroup accGrp on accGrp.id.accGrpId = acctGrp.accGrpId where accGrp.currencyCode = :currency order by acct.id.accountId asc";
		        accountsList = session.createQuery(query, AcctMaintenance.class)
		                .setParameter("currency", currency, StringType.INSTANCE)
		                .getResultList();
		    }
			
			log.debug(this.getClass().getName()
					+ " - [getAccountsDetails] - " + "Exit");
			return accountsList;
			
		} catch (Exception exp) {
			// log error message
			log.error(this.getClass().getName()
					+ " - Exception Caught in [getAccountsDetails] method : - "
					+ exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp,
					"getAccountsDetails", this.getClass());
		} finally {
			// Nullify object
			accountsList = null;
			HibernateException hThrownException = JDBCCloser.close(session);
			if (hThrownException != null)
				throw new SwtException(hThrownException.getMessage());
			
			// Debug Message
			log.debug(this.getClass().getName() + "- [ getAccountsDetails ] - Exit");
		}
	}
	public void updatePaymentCategory(String paymentReqId, String categoryId) 
			throws SwtException {
		String query= null;
		Session session = null;
		Transaction tx = null;
		SwtInterceptor interceptor = null;
		PaymentRequest payRequest = new PaymentRequest();
		Iterator itr;
		Connection conn= null;
		PreparedStatement stmt = null;
		
		try {
			
			session = SwtUtil.pcSessionFactory.openSession();
			conn = SwtUtil.connection(session);
		      String updateQuery = "UPDATE  PC_PAYMENT_REQUEST SET CATEGORY_ID = ? WHERE PAYREQ_ID = ?";
		       stmt = conn.prepareStatement(updateQuery);
		     
		        stmt.setString(1, categoryId);
		        stmt.setInt(2, Integer.parseInt(paymentReqId));
		        stmt.executeUpdate();
		      
		      conn.commit();
		      
		      try {
		    	  
				tx = session.beginTransaction();
				Long paymentReqIdLong = Long.parseLong(paymentReqId);
				query = "from PaymentRequest payReq where payReq.payReqId = :paymentReqId";
				List<PaymentRequest> records = session.createQuery(query, PaymentRequest.class)
				                                       .setParameter("paymentReqId", paymentReqIdLong, StandardBasicTypes.LONG)
				                                       .getResultList();
				if(records.size() == 1) {
				    payRequest = records.get(0);
				}
		    	  
				MaintenanceLogDAOHibernate maintenanceLogDAOHibernate = (MaintenanceLogDAOHibernate) (SwtUtil
						.getBean("maintenanceLogDAO"));
				MaintenanceLog mainLog = new MaintenanceLog();
				mainLog.setHostId(SwtUtil.getCurrentHostId());
				mainLog.setuserId(UserThreadLocalHolder.getUser());
				mainLog.setIpAddress(UserThreadLocalHolder.getUserIPAddress());
				mainLog.setAction(SwtConstants.ACTION_UPDATE);
				mainLog.setTableName("Payment Request");
				mainLog.setColumnName("Category");
				mainLog.setOldValue(payRequest != null? payRequest.getCategoryId() : "");
				mainLog.setNewValue(categoryId);
				mainLog.setReference(paymentReqId);
				mainLog.setLogDate(new Date());
				maintenanceLogDAOHibernate.logMaintenanceAudit(mainLog);
				
		      }catch(Exception e) {
		      }
				
			
		}catch (Exception e) {
			try {
				conn.rollback();
			} catch (SQLException e1) {
			}
			
			log.error(this.getClass().getName()
					+ " - Exception Catched in [updatePaymentCategory] method : - "
					+ e.getMessage());

			throw SwtErrorHandler.getInstance().handleException(e,
					"updatePaymentCategory",
					AccountGroupsMaintenanceDAOHibernate.class);
		}
		finally {
			JDBCCloser.close(null,null,conn,session);
		}
	}
	
	
	public void releasePayment(ArrayList paymentReqId, String userId) 
			throws SwtException {
		String query= null;
		Session session = null;
		Transaction tx = null;
		SwtInterceptor interceptor = null;
		PaymentRequest payRequest = new PaymentRequest();
		Iterator itr;
		
		try {
			interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");
			session = getHibernateTemplate().getSessionFactory().withOptions().interceptor(interceptor).openSession();
			tx = session.beginTransaction();
			
			for (int i = 0; i < paymentReqId.size(); i++) {
				query = "from PaymentRequest payReq where payReq.payReqId = :paymentReqId";
				Long paymentReqIdLong = Long.parseLong(paymentReqId.get(i).toString());
				List<PaymentRequest> records = session.createQuery(query, PaymentRequest.class)
                        .setParameter("paymentReqId", paymentReqIdLong, StandardBasicTypes.LONG)
                        .getResultList();
				if(records.size() == 1) {
					itr = records.iterator();
					while(itr.hasNext()){
						payRequest = (PaymentRequest) itr.next();
						payRequest.setReleaseDate(SwtUtil.getSysParamDate());
						
						if(payRequest.getStatus().equals("S")) {
							payRequest.setReleaseMethod("P");	
						} else if(payRequest.getStatus().equals("B")) {
							payRequest.setReleaseMethod("B");	
						} else if(payRequest.getStatus().equals("W")) {
							payRequest.setReleaseMethod("M");	
						}

						payRequest.setStatus("R");
						payRequest.setUpdatedBy(userId);
						session.update(payRequest);
					}
				} else {
					throw new SwtException(
							"errors.DataIntegrityViolationExceptioninAdd");
				}
				
			}
			
			
			tx.commit();
		}catch (Exception e) {
			try {
				if (tx != null) {
					tx.rollback();
				}
			} catch (HibernateException exception) {
				log
				.error("HibernateException occured in rolling back transaction. Cause : "
						+ exception.getMessage());
			}
			log.error(this.getClass().getName()
					+ " - Exception Catched in [releasePayment] method : - "
					+ e.getMessage());
			
			throw SwtErrorHandler.getInstance().handleException(e,
					"releasePayment",
					AccountGroupsMaintenanceDAOHibernate.class);
		}
		finally {
			HibernateException hThrownException = JDBCCloser.close(session);
			if (hThrownException != null)
				throw new SwtException(hThrownException.getMessage());
		}
	}
	public AccountGroup getSpreadId(String accountGroupId)
			throws SwtException {
		String query= null;
		AccountGroup acctGrp = null;
		Session session = null;
		Transaction tx = null;
		SwtInterceptor interceptor = null;
		Iterator itr;
		Collection records = new ArrayList();
		try {
			interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");
			session = getHibernateTemplate().getSessionFactory().withOptions().interceptor(interceptor).openSession();
			tx = session.beginTransaction();
			query = "from AccountGroup acct where acct.id.accGrpId = :accountGroupId";
			records = session.createQuery(query, AccountGroup.class)
		                    .setParameter("accountGroupId", accountGroupId, StandardBasicTypes.STRING)
		                    .getResultList();
		 	if(records != null && !records.isEmpty()) {
					itr = records.iterator();
					while(itr.hasNext()){
						acctGrp = (AccountGroup) itr.next();
					}
				}
			return acctGrp;
		}catch (Exception e) {
			try {
				if (tx != null) {
					tx.rollback();
				}
			} catch (HibernateException exception) {
				log
				.error("HibernateException occured in rolling back transaction. Cause : "
						+ exception.getMessage());
			}
			log.error(this.getClass().getName()
					+ " - Exception Catched in [releasePayment] method : - "
					+ e.getMessage());
			
			throw SwtErrorHandler.getInstance().handleException(e,
					"releasePayment",
					AccountGroupsMaintenanceDAOHibernate.class);
		}
		finally {
			acctGrp = null;
			HibernateException hThrownException = JDBCCloser.close(session);
			if (hThrownException != null)
				throw new SwtException(hThrownException.getMessage());
		}
	}
	public void unStopPayment(String paymentReqId, String userId) 
			throws SwtException {
		String query= null;
		String query2= null;
		Session session = null;
		Transaction tx = null;
		SwtInterceptor interceptor = null;
		PaymentRequest payRequest = new PaymentRequest();
		PaymentStop payStop = new PaymentStop();
		Iterator itr;
		Iterator itr2;
		
		try {
			interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");
			session = getHibernateTemplate().getSessionFactory().withOptions().interceptor(interceptor).openSession();
			tx = session.beginTransaction();
			query = "from PaymentRequest payReq where payReq.payReqId = :paymentReqId";
			query2 = "from PaymentStop payStop where payStop.id.payReqId = :paymentReqId";
			Long paymentReqIdLong = Long.parseLong(paymentReqId);
			List<PaymentRequest> records = session.createQuery(query, PaymentRequest.class)
                    .setParameter("paymentReqId", paymentReqIdLong, StandardBasicTypes.LONG)
                    .getResultList();
			List<PaymentRequest> records2 = session.createQuery(query2, PaymentRequest.class)
                    .setParameter("paymentReqId", paymentReqIdLong, StandardBasicTypes.LONG)
                    .getResultList();
			if(records.size() == 1) {
				itr = records.iterator();
				while(itr.hasNext()){
					payRequest = (PaymentRequest) itr.next();
					payRequest.setUnstopDate(SwtUtil.getSysParamDate());
					payRequest.setStatus("W");
					payRequest.setUpdatedBy(userId);
					session.update(payRequest);
				}
			} else {
				throw new SwtException(
						"errors.DataIntegrityViolationExceptioninAdd");
			}
			itr2 = records2.iterator();
			while(itr2.hasNext()){
				payStop = (PaymentStop) itr2.next();
				payStop.setUnstopDate(SwtUtil.getSysParamDate());
				payStop.setUnstopBy(userId);
				session.update(payStop);
			}
			
			tx.commit();
		}catch (Exception e) {
			try {
				if (tx != null) {
					tx.rollback();
				}
			} catch (HibernateException exception) {
				log
				.error("HibernateException occured in rolling back transaction. Cause : "
						+ exception.getMessage());
			}
			log.error(this.getClass().getName()
					+ " - Exception Catched in [unStopPayment] method : - "
					+ e.getMessage());
			
			throw SwtErrorHandler.getInstance().handleException(e,
					"unStopPayment",
					AccountGroupsMaintenanceDAOHibernate.class);
		}
		finally {
			HibernateException hThrownException = JDBCCloser.close(session);
			if (hThrownException != null)
				throw new SwtException(hThrownException.getMessage());
		}
	}
	
	public void saveScreenInfo(ArrayList<ScreenInfo>  screenInfoList)
			throws SwtException {
			List records = null;
			Session session = null;
			Transaction tx = null;
		try {
			log.debug(this.getClass().getName() + " - [saveScreenInfo] - Enter");
			session = getHibernateTemplate()
					.getSessionFactory()
					.openSession();
			tx = session.beginTransaction();
			if(screenInfoList.size() > 0) {
				
				
				for (Iterator iterator = screenInfoList.iterator(); iterator.hasNext();) {
					ScreenInfo screenInfo = (ScreenInfo) iterator.next();
					records = (List ) getHibernateTemplate().find("from ScreenInfo screenInfo where screenInfo.id.hostId=?0 and screenInfo.id.userId=?1 and screenInfo.id.entityId=?2 and screenInfo.id.screenId=?3 and screenInfo.id.clsId=?4 and screenInfo.id.propertyName=?5" ,
									new Object[] { screenInfo.getId().getHostId(), screenInfo.getId().getUserId(), screenInfo.getId().getEntityId(), screenInfo.getId().getScreenId(), "display", screenInfo.getId().getPropertyName() });
					// Condition to check list size
					if (records.size() == 0) {

						
						session.save(screenInfo);
					} else {
						session.update(screenInfo);
					}
					
				}
			}
			
			tx.commit();
			log.debug(this.getClass().getName() + " - [saveScreenInfo] - Exit");
		} catch (Exception e) {
			try {
				if (tx != null) {
					tx.rollback();
				}
			} catch (HibernateException exception) {
				log
						.error("HibernateException occured in rolling back transaction. Cause : "
								+ exception.getMessage());
			}
			log.error(this.getClass().getName()
					+ " - Exception Catched in [saveScreenInfo] method : - "
					+ e.getMessage());

			throw SwtErrorHandler.getInstance().handleException(e,
					"saveScreenInfo",
					AccountGroupsMaintenanceDAOHibernate.class);
		}
		finally {
			HibernateException hThrownException = JDBCCloser.close(session);
			if (hThrownException != null)
				throw new SwtException(hThrownException.getMessage());
			records = null;
		}
	}

}
