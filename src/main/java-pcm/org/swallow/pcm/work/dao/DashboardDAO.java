/*
 * @(#)DashboardDAO.java 1.0 25/05/2019
 *
 * Copyright (c) 2006-2019 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.pcm.work.dao;

import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;

import org.swallow.dao.DAO;
import org.swallow.exception.SwtException;
import org.swallow.model.ScreenInfo;
import org.swallow.pcm.maintenance.model.AccountGroup;
import org.swallow.pcm.maintenance.model.core.dashboardResult;

/**
 * <AUTHOR>
 * 
 */
public interface DashboardDAO extends DAO {

	/**
	 * This is used to fetches details for the currency dashboard
	 * 
	 * @param listEntities
	 * @param valueDate
	 * @param applyCurrencyThreshold
	 * @param spreadOnly
	 * @param userId
	 * @param value 
	 * @param pv_xml_out
	 * @return Collection
	 * @throws SwtException
	 */
	public String[] currencyDashboardDetails(String listEntities, String valueDate,
			String applyCurrencyThreshold, String spreadOnly, String userId, String volume, String multiplier) throws SwtException;
	
	
	/**
	 * Returns the collection of account group details from account group table
	 * 
	 * @return Collection
	 * @throws SwtException
	 */
	
	public Collection getAccountGrpDetails(String currency) throws SwtException;
	
	/**
	 * Returns the collection of accounts details from account  table
	 * 
	 * @return Collection
	 * @throws SwtException
	 */

	public Collection getAccountsDetails(String accountGrp, String currency) throws SwtException;
	
	/**
	 * Returns the updated pay request
	 * 
	 * @return void
	 * @throws SwtException
	 */
	 public void updatePaymentCategory(String paymentReqId, String categoryId)  throws SwtException;
	 
	 
	    /**
		 * unStopReleasePayment payment
		 * 
		 * @return void
		 * @throws SwtException
		 */
	 public Collection unStopReleasePayment(ArrayList paymentReqId, ArrayList previousStatus, String paymentAction,  String userId) throws SwtException;
	 
	 /**
	  * unstop payment
	  * 
	  * @return void
	  * @throws SwtException
	  */
	 public void unStopPayment(String paymentReqId, String userId) throws SwtException;
	
	 /**
	  * get dataGrid
	  * 
	  * @return string
	  * @throws SwtException
	  */
	 
	 public dashboardResult dataGridDetails(String listEntities, String valueDate, String highValue, String currencyMultiplier, String status, String blockedReason, String currencyCode, 
				String accountGroupId, String accountId, String userId, String initialFilter, String userFilter, String refFilter, String spreadOnly,
				String order, String ascDesc, String  rowbegin, String rowEnd, String archive, String ccyTime, String inputSince) throws SwtException ;
	 
	 /**
	  * get SpreadId by accountGroup
	  * 
	  * @return string
	  * @throws SwtException
	  */
	 public AccountGroup getSpreadId(String accountGroupId) throws SwtException;
	 
	 /**
	  * Blocked Stopped payment
	  * 
	  * @return string
	  * @throws SwtException
	  */
	 public HashMap isBlockedStopped(ArrayList paymentReqId, String formatDate) throws SwtException;
	 
	 /**
	  * Save screen info preference
	  * 
	  * @return string
	  * @throws SwtException
	  */
	 public void saveScreenInfo(ArrayList<ScreenInfo> screenInfoList) throws SwtException;
	 
	 /**
	  * Check stop rule process running  or not
	  * 
	  * @return string
	  * @throws SwtException
	  */
	 public String checkStopProcess() throws SwtException;
}

