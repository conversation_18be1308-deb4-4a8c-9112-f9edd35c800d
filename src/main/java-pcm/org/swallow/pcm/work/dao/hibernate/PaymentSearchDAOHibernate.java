/*
 * @(#)PaymentSearchDAOHibernate.java 1.0 25/05/2019
 *
 * Copyright (c) 2006-2019 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.pcm.work.dao.hibernate;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.List;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

import org.hibernate.query.Query;
import org.hibernate.type.StandardBasicTypes;
import org.hibernate.type.StringType;
import org.springframework.beans.factory.annotation.Qualifier;
import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.maintenance.model.Party;
import org.swallow.pcm.work.dao.PaymentSearchDAO;
import org.swallow.util.JDBCCloser;
import org.swallow.util.SwtUtil;
import org.springframework.context.annotation.Lazy;
import org.springframework.orm.hibernate5.support.HibernateDaoSupport;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.stereotype.Repository;
import org.springframework.beans.factory.annotation.Autowired;
import org.hibernate.HibernateException;
import org.hibernate.SessionFactory;
import org.hibernate.Transaction;
import org.hibernate.Session;

import javax.persistence.TemporalType;


@Repository ("paymentSearchDAO")
@Transactional
public class PaymentSearchDAOHibernate extends HibernateDaoSupport implements PaymentSearchDAO {
	public PaymentSearchDAOHibernate(@Lazy @Qualifier("sessionFactoryPC") SessionFactory sessionfactory) {
	    setSessionFactory(sessionfactory);
	}


	// Final instance for log
	private final Log log = LogFactory.getLog(PaymentSearchDAOHibernate.class);

	public ArrayList<Party> getPartySearchResult(String partyId, String partyName, String entityId, int pageSize,
			int currentPage, String selectedsort) throws SwtException {

		Party party = null;
		ArrayList<Party> partiesCollection = null;
		String sort = null;
		String[] selectedSortArray = null;
		String query = null;
		PreparedStatement pst = null;
		ResultSet rs = null;
		Connection conn = null;
		
		try {
			log.debug(this.getClass().getName() + " - [getPartySearchResult] - " + "Entry");
			partiesCollection = new ArrayList();
			if (!SwtUtil.isEmptyOrNull(selectedsort)) {
				selectedSortArray = selectedsort.split("\\|");
				if ("0".equals(selectedSortArray[0])) {
					sort = "PARTY_ID ";
				} else {
					sort = "PARTY_NAME ";
				}
				if ("true".equals(selectedSortArray[1])) {
					sort += "DESC";
				} else {
					sort += "ASC";
				}
				
			}

			conn = SwtUtil.connection(SwtUtil.sessionFactory.openSession());

			/*
			  SELECT *
			    FROM (SELECT ROW_NUMBER () OVER (ORDER BY PARTY_ID ASC) ROW_, query.*
			            FROM (SELECT *
			                    FROM p_party
			                   WHERE     entity_id = :entityId
			                         AND UPPER (party_id) LIKE UPPER ( :partyId || '%')
			                         AND UPPER (party_name) LIKE UPPER ( :partyName || '%'))
			                 query)
			   WHERE     ROW_ > ( :currentPage - 1) * :pageSize
			         AND ROW_ < :currentPage * :pageSize + 1
			 */
			query = "SELECT * "+
				    "FROM (SELECT ROW_NUMBER () OVER (ORDER BY " + sort +") ROW_, query.* "+
		            "FROM (SELECT * "+
		                    "FROM p_party "+
		                   "WHERE     entity_id = ? "+
		                         "AND UPPER (party_id) LIKE UPPER ( ? || '%') "+
		                         "AND UPPER (party_name) LIKE UPPER ( ? || '%')) "+
		                 "query) "+
		            "WHERE     ROW_ > ( ? - 1) * ? "+
		            "AND ROW_ < ? * ? + 1";
			
			pst = conn.prepareStatement(query);
			pst.setString(1, entityId);
			pst.setString(2, partyId);
			pst.setString(3, partyName);
			pst.setInt(4, currentPage);
			pst.setInt(5, pageSize);
			pst.setInt(6, currentPage);
			pst.setInt(7, pageSize);
			rs = pst.executeQuery();
			
			while (rs.next()) {
				party = new Party();
				party.getId().setPartyId(rs.getString("PARTY_ID"));
				party.setPartyName(rs.getString("PARTY_NAME"));
				partiesCollection.add(party);
			}

			log.debug(this.getClass().getName() + " - [getPartySearchResult] - " + "Exit");
			return partiesCollection;

		} catch (Exception exp) {
			exp.printStackTrace();
			// log error message
			log.error(this.getClass().getName() + " - Exception Caught in [getPartySearchResult] method : - "
					+ exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp, "getPartySearchResult", this.getClass());
		} finally {
			Object[] exceptions = JDBCCloser.close(rs, pst, conn, null);
			// Debug Message
			log.debug(this.getClass().getName() + "- [ getPartySearchResult ] - Exit");
		}
	}

	public int getTotalCount(String partyName, String partyId, String entityId) throws SwtException {

		String query = null;
		List<Object> paramArr = new ArrayList<>();
		List<String> paramName = new ArrayList<>();
		List<StringType> paramType = new ArrayList<StringType>();
		List<Long> noofRecords = null;
		long totalCount = 0;
		Session session = null;

		try {
			log.debug(this.getClass().getName() + " - [getTotalCount] - Entering");
			query = "select count(*) from Party c where c.id.entityId = :entityId ";

			paramArr.add(entityId);
			paramType.add(StandardBasicTypes.STRING);
			paramName.add("entityId");
			if (partyId != null && !partyId.trim().isEmpty()) {
				query += " and upper(c.id.partyId) like upper(:partyId || '%')";
				paramArr.add(partyId);
				paramType.add(StandardBasicTypes.STRING);
				paramName.add("partyId");
			}

			if (partyName != null && !partyName.trim().isEmpty()) {
				query += " and upper(c.partyName) like upper(:partyName || '%')";
				paramArr.add(partyName);
				paramType.add(StandardBasicTypes.STRING);
				paramName.add("partyName");
			}

			session = SwtUtil.sessionFactory.openSession();
			Query<Long> hqlQuery = session.createQuery(query, Long.class);

			for (int i = 0; i < paramArr.size(); i++) {
				hqlQuery.setParameter(paramName.get(i), paramArr.get(i), paramType.get(i));
			}

			noofRecords = hqlQuery.list();

			if (!noofRecords.isEmpty()) {
				totalCount = noofRecords.get(0);
			}

			return (int) totalCount;
		} catch (Exception exp) {
			log.error(this.getClass().getName() + " - Exception Caught in [getTotalCount] method : - " + exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp, "getTotalCount", this.getClass());
		} finally {
			if (session != null) {
				session.close();
			}
			log.debug(this.getClass().getName() + " - [getTotalCount] - Exiting");
		}

	}

}
