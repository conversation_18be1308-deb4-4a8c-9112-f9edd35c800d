<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE hibernate-mapping PUBLIC
        "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
        "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">

<hibernate-mapping>
    <class name="org.swallow.pcm.control.model.PCInterfaceInterruption" table="PC_INTERFACE_EXTENSION">
    	
    	<composite-id name="id" class="org.swallow.pcm.control.model.PCInterfaceInterruption$Id" >
			 <key-property name="interfaceId" access="field" column="INTERFACEID" type ="java.lang.String"/>	
			 <key-property name="messageType" access="field" column="MESSAGE_TYPE" type ="java.lang.String"/>			 
		</composite-id>

		<property name="startAlertTime" column="START_ALERT_TIME" type ="java.lang.String"/>	
		<property name="endAlertTime" column="END_ALERT_TIME" type ="java.lang.String"/>
		<property name="threshold" column="THRESHOLD"  type ="java.lang.String" />
	</class>
	
	
</hibernate-mapping>
