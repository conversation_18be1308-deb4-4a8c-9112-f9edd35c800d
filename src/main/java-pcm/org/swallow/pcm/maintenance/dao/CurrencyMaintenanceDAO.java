/*
 * @(#)CurrencyMaintenanceDAO.java 1.0 06/03/2019
 *
 * Copyright (c) 2006-2019 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.pcm.maintenance.dao;

import org.swallow.dao.DAO;
import org.swallow.exception.SwtException;
import org.swallow.pcm.maintenance.model.PCMCurrency;

import java.util.Collection;


/**
 * <AUTHOR>
 * This is DAO interface for Currency screen
 */
public interface CurrencyMaintenanceDAO extends DAO {

	
	/**
	 * Returns the collection of currency detail list from currency table
	 * 
	 * @return Collection
	 * @throws SwtException
	 */
	public Collection getCurrencyDetailList() throws SwtException;
	
	/**
	 * Returns the collection of currency combo list from pcmCurrency table
	 * 
	 * @return Collection
	 * @throws SwtException
	 */
	public Collection getCurrencyCombo() throws SwtException;
	
	
	/**
	 * Returns the collection of currency combo list from Currency table
	 * 
	 * @return Collection
	 * @throws SwtException
	 */
	public Collection getCurrencyMaintenanceCombo() throws SwtException;
	
	/**
	 * Returns the max ordinal from currency table
	 * 
	 * @return Integer
	 * @throws SwtException
	 */
	
	public Integer getMaxOrder() throws SwtException;
	
	
	/**
	 * Returns the collection of currency detail list from currency table
	 * 
	 * @param id
	 * @return PCMCurrency
	 * @throws SwtException
	 */
	public PCMCurrency getCurrencyDetailById(String id) throws SwtException;
	
	
	/**
	 * Save the added new currency in the DataBase
	 * 
	 * @param currency
	 * @return
	 * @throws SwtException
	 */
	public void saveCurrency(PCMCurrency currency) throws SwtException;

	/**
	 * Update the changed currency detail in the DataBase
	 * 
	 * @param currency
	 * @return
	 * @throws SwtException
	 */
	public void updateCurrency(PCMCurrency currency) throws SwtException;


	/**
	 * Delete a Currency from DB
	 * @param currency
	 * @throws SwtException
	 */
	public void deleteCurrency(String currencyCode)throws SwtException;
	
	
	/**
	 * Reorder ordinal of Currency from DB
	 * @param value_from
	 * @throws SwtException
	 */
	public void reOrder(Integer value_from, Integer orderIni) throws SwtException;


}
