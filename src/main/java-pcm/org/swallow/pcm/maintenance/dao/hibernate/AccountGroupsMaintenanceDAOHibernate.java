/*
 * @(#)AccountGroupsMaintenanceDAOHibernate.java 1.0 06/03/2019
 *
 * Copyright (c) 2006-2019 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.pcm.maintenance.dao.hibernate;

import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.List;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.maintenance.dao.hibernate.ILMGeneralMaintenanceDAOHibernate;
import org.swallow.maintenance.model.AcctMaintenance;
import org.swallow.pcm.maintenance.dao.AccountGroupsMaintenanceDAO;
import org.swallow.pcm.maintenance.model.AccountGroup;
import org.swallow.pcm.maintenance.model.AccountGroupCutoff;
import org.swallow.pcm.maintenance.model.AccountInGroup;
import org.swallow.pcm.maintenance.model.Reserve;
import org.swallow.pcm.maintenance.model.RuleConditions;
import org.swallow.pcm.maintenance.model.RulesDefinition;
import org.swallow.pcm.util.PCMUtil;
import org.swallow.util.JDBCCloser;
import org.swallow.util.SwtInterceptor;
import org.swallow.util.SwtUtil;
import org.springframework.context.annotation.Lazy;
import org.springframework.orm.hibernate5.support.HibernateDaoSupport;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.stereotype.Repository;
import org.springframework.beans.factory.annotation.Qualifier;
import org.hibernate.HibernateException;
import org.hibernate.SessionFactory;
import org.hibernate.Transaction;
import org.hibernate.Session;







@Repository ("accountGroupsMaintenanceDAO")
@Transactional
public class AccountGroupsMaintenanceDAOHibernate extends HibernateDaoSupport implements AccountGroupsMaintenanceDAO {
	public AccountGroupsMaintenanceDAOHibernate(@Lazy @Qualifier("sessionFactoryPC") SessionFactory sessionfactory) {
	    setSessionFactory(sessionfactory);
	}


	// Final instance for log
	private final Log log = LogFactory.getLog(AccountGroupsMaintenanceDAOHibernate.class);

	public void saveAccountGroup(AccountGroup acctGroup)
			throws SwtException {
			List records = null;
			Session session = null;
			Transaction tx = null;
			SwtInterceptor interceptor = null;
			boolean requireAuthorisation = false;
		try {
			log.debug(this.getClass().getName() + " - [saveAcountGroup] - Enter");
			if(acctGroup != null && !SwtUtil.isEmptyOrNull(acctGroup.getMainEventId())) {
				requireAuthorisation = true;
			}
			records = (List ) getHibernateTemplate().find("from AccountGroup acctGroup where acctGroup.id.accGrpId =?0 ",
							new Object[] {acctGroup.getId().getAccGrpId()});
			// Condition to check list size
			if (records.size() == 0) {
				interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");
				session = getHibernateTemplate()
						.getSessionFactory()
						.withOptions().interceptor(interceptor).openSession();
				tx = session.beginTransaction();
				if(requireAuthorisation) {
					acctGroup.setRequireAuthorisation("Y");
				}
				session.save(acctGroup);
//				if(requireAuthorisation) {
//					tx.rollback();
//					session.flush();
//				}
//				else 
			
				tx.commit();
			
			} else {
				throw new SwtException(
						"errors.DataIntegrityViolationExceptioninAdd");
			}
			log.debug(this.getClass().getName() + " - [saveAcountGroup] - Exit");
		} catch (Exception e) {
			try {
				if (tx != null) {
					tx.rollback();
				}
			} catch (HibernateException exception) {
				log
						.error("HibernateException occured in rolling back transaction. Cause : "
								+ exception.getMessage());
			}
			log.error(this.getClass().getName()
					+ " - Exception Catched in [saveAccountGroup] method : - "
					+ e.getMessage());

			throw SwtErrorHandler.getInstance().handleException(e,
					"saveAccountGroup",
					AccountGroupsMaintenanceDAOHibernate.class);
		}
		finally {
			HibernateException hThrownException = JDBCCloser.close(session);
			if (hThrownException != null)
				throw new SwtException(hThrownException.getMessage());
			records = null;
		}
	}
	public void crudAccountsInGroup(ArrayList<AccountInGroup> accountsInsert, ArrayList<AccountInGroup> accountsDelete, Long maintEventId )
			throws SwtException {
		List records = null;
		List recordsDelete = null;
		Session session = null;
		Transaction tx = null;
		SwtInterceptor interceptor = null;
		AccountInGroup acctInGrp = new AccountInGroup();
		Iterator itr;
		
		try {
			log.debug(this.getClass().getName() + " - [crudAccountsInGroup] - Enter");
			interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");
			session = getHibernateTemplate()
					.getSessionFactory()
					.withOptions().interceptor(interceptor).openSession();
			tx = session.beginTransaction();
			if(accountsInsert.size() > 0) {
				
				
			for (Iterator iterator = accountsInsert.iterator(); iterator.hasNext();) {
				AccountInGroup accountsI = (AccountInGroup) iterator.next();
				
				records = (List ) getHibernateTemplate().find("from AccountInGroup acct where acct.id.accountId =?0 and acct.id.hostId= ?1 and acct.id.entityId= ?2  ",
								new Object[] {accountsI.getId().getAccountId(), accountsI.getId().getHostId(), accountsI.getId().getEntityId()});
				if (records.size() == 0) {
					if(maintEventId != null)
						accountsI.setMainEventId(""+maintEventId);
					session.save(accountsI);
					
				} else {
					throw new SwtException(
							"errors.DataIntegrityViolationExceptioninAdd");
				}
				
			}
					
			}
			if(accountsDelete.size() > 0 ) {
				for (Iterator iterator = accountsDelete.iterator(); iterator.hasNext();) {
					AccountInGroup accountsD = (AccountInGroup) iterator.next();
					recordsDelete = getHibernateTemplate()
							.find("from AccountInGroup acct where acct.id.accountId =?0 ",
									new Object[] {accountsD.getId().getAccountId()});
					if(recordsDelete != null){
						itr = recordsDelete.iterator();
						while(itr.hasNext()){
							acctInGrp = (AccountInGroup) itr.next();
							if(maintEventId != null)
								acctInGrp.setMainEventId(""+maintEventId);
							session.delete(acctInGrp);
						}
					}
				}
			}
			if(maintEventId != null) {
				session.flush();
				tx.rollback();
			}
			else 
				tx.commit();
			
			// Condition to check list size
			
			log.debug(this.getClass().getName() + " - [crudReserve] - Exit");
		} catch (Exception e) {
			e.printStackTrace();
			
			try {
				if (tx != null) {
					tx.rollback();
				}
			} catch (HibernateException exception) {
				log
						.error("HibernateException occured in rolling back transaction. Cause : "
								+ exception.getMessage());
			}
			log.error(this.getClass().getName()
					+ " - Exception Catched in [crudAccountsInGroup] method : - "
					+ e.getMessage());

			throw SwtErrorHandler.getInstance().handleException(e,
					"crudAccountsInGroup",
					AccountGroupsMaintenanceDAOHibernate.class);
		}
		finally {
			HibernateException hThrownException = JDBCCloser.close(session);
			if (hThrownException != null)
				throw new SwtException(hThrownException.getMessage());
			records = null;
			recordsDelete = null;
		}
	}
	
	public void crudReserve(ArrayList<Reserve> reserveInsert, ArrayList<Reserve> reserveUpdate, ArrayList<Reserve> reserveDelete, Long maintEventId)
			throws SwtException {
		List records = null;
		Session session = null;
		Transaction tx = null;
		SwtInterceptor interceptor = null;
		try {
			log.debug(this.getClass().getName() + " - [crudReserve] - Enter");
			interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");
			session = getHibernateTemplate().getSessionFactory().withOptions().interceptor(interceptor).openSession();
			tx = session.beginTransaction();
			if(reserveInsert.size() > 0) {
				for (Iterator iterator = reserveInsert.iterator(); iterator.hasNext();) {
				Reserve reserveI = (Reserve) iterator.next();
				if(maintEventId != null) {
					reserveI.setMainEventId(""+maintEventId);
				}
				records = (List ) getHibernateTemplate().find("from Reserve res where res.accGrpId =?0 and res.accGrpTime= ?1 ",
								new Object[] {reserveI.getAccGrpId(), reserveI.getAccGrpTime()});
				if (records.size() == 0) {
					session.save(reserveI);
					
					
				} else {
					throw new SwtException(
							"errors.DataIntegrityViolationExceptioninAdd");
				}
				
			}
			}
			if (reserveUpdate.size() > 0) {
				for (Iterator iterator = reserveUpdate.iterator(); iterator.hasNext();) {
				Reserve reserveU = (Reserve) iterator.next();
				if(maintEventId != null) {
					reserveU.setMainEventId(""+maintEventId);
				}
				session.update(reserveU);
				
			}
			}
				if (reserveDelete.size() > 0) {
					for (Iterator iterator = reserveDelete.iterator(); iterator.hasNext();) {
						Reserve reserveD = (Reserve) iterator.next();
						if(maintEventId != null) {
							reserveD.setMainEventId(""+maintEventId);
						}
						session.delete(reserveD);
						
					}
			}
			
				if(maintEventId != null) {
					session.flush();
					tx.rollback();
				}
				else 
					tx.commit();
			
			// Condition to check list size
			
			log.debug(this.getClass().getName() + " - [crudReserve] - Exit");
		} catch (Exception e) {
			try {
				if (tx != null) {
					tx.rollback();
				}
			} catch (HibernateException exception) {
				log
						.error("HibernateException occured in rolling back transaction. Cause : "
								+ exception.getMessage());
			}
			log.error(this.getClass().getName()
					+ " - Exception Catched in [crudReserve] method : - "
					+ e.getMessage());

			throw SwtErrorHandler.getInstance().handleException(e,
					"crudReserve",
					AccountGroupsMaintenanceDAOHibernate.class);
		}
		finally {
			HibernateException hThrownException = JDBCCloser.close(session);
			if (hThrownException != null)
				throw new SwtException(hThrownException.getMessage());
			records = null;
		}
	}
	public void crudCutOff(ArrayList<AccountGroupCutoff> cutOffInsert, ArrayList<AccountGroupCutoff> cutOffUpdate, ArrayList<AccountGroupCutoff> cutOffDelete, Long maintEventId)
			throws SwtException {
		List records = null;
		List recordsCutOff = null;
		Session session = null;
		Transaction tx = null;
		SwtInterceptor interceptor = null;
		RulesDefinition ruleDefintion;
		RuleConditions.Id id = null;
		int n = 0;
		try {
			log.debug(this.getClass().getName() + " - [crudCutOff] - Enter");
			interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");
			session = getHibernateTemplate().getSessionFactory().withOptions().interceptor(interceptor).openSession();
			tx = session.beginTransaction();
			if(cutOffInsert.size() > 0) {

				for (Iterator iterator = cutOffInsert.iterator(); iterator.hasNext();) {
					AccountGroupCutoff cutOffI = (AccountGroupCutoff) iterator.next();
					Integer ruleDefintionId = null;
					ruleDefintion = cutOffI.getRulesDefinition();
					if(maintEventId != null) {
						ruleDefintion.setMainEventId(""+maintEventId);
					}
					session.save(ruleDefintion);
					ruleDefintionId = ruleDefintion.getRuleId();
					cutOffI.setRuleId(ruleDefintionId);
					if(maintEventId != null)
						cutOffI.setMainEventId(""+maintEventId);
					for (int i = 0; i < ruleDefintion.getRuleConditions().size(); i++) {
						ruleDefintion.getRuleConditions().get(i).getId().setRuleId(ruleDefintionId);
						if(maintEventId != null) {
							ruleDefintion.getRuleConditions().get(i).setIsActive("N");
							ruleDefintion.setMainEventId(""+maintEventId);
						}
						else 
							ruleDefintion.getRuleConditions().get(i).setIsActive("Y");
						session.save(ruleDefintion.getRuleConditions().get(i));
					}
					session.save(cutOffI);
				} 
			}
					
			
			if (cutOffUpdate.size() > 0) {
				for (Iterator iterator = cutOffUpdate.iterator() ; iterator.hasNext(); )  {
					AccountGroupCutoff cutOffU = (AccountGroupCutoff) iterator.next() ;
					RulesDefinition ruleD = cutOffU.getRulesDefinition() ;

					records = (List ) getHibernateTemplate().find("select acct.ruleId from AccountGroupCutoff acct where acct.cutOffRuleId =?0 ",
									new Object[] {cutOffU.getCutOffRuleId()});
					if(records.size() == 1) {
						Integer ruleId = (Integer) (records.get(0));
						cutOffU.setRuleId(ruleId);
						cutOffU.getRulesDefinition().setRuleId(ruleId);
						if(maintEventId != null)
							cutOffU.setMainEventId(""+maintEventId);
						/* Query to execute the records */
						records = (List ) getHibernateTemplate().find("from RuleConditions r  where r.id.ruleId=?0",
										new Object[] {ruleId});
						if (!records.isEmpty()) {
							for (Iterator it= records.iterator(); it.hasNext();) {
								RuleConditions ruleCondition =(RuleConditions) it.next();
								session.delete(ruleCondition);
							}
						}
						ruleDefintion = cutOffU.getRulesDefinition();
						if(maintEventId != null)
							ruleDefintion.setMainEventId(""+maintEventId);
						
						session.update(ruleDefintion);
						if(ruleDefintion.getRuleConditions() != null) {
							for (int i = 0 ; i < ruleDefintion.getRuleConditions().size() ; i++){
								id = new RuleConditions.Id();
								id.setRuleId(ruleId);
								id.setConditionId(ruleDefintion.getRuleConditions().get(i).getId().getConditionId());
								ruleDefintion.getRuleConditions().get(i).setId(id);
								if(maintEventId != null) {
									ruleDefintion.getRuleConditions().get(i).setIsActive("N");
									ruleDefintion.setMainEventId(""+maintEventId);
								}
								else 
									ruleDefintion.getRuleConditions().get(i).setIsActive("Y");
								session.save(ruleDefintion.getRuleConditions().get(i));
							}	
						}

						session.update(cutOffU);
					}

				}
			}
			if(cutOffDelete.size() > 0) {
				for (Iterator iterator = cutOffDelete.iterator() ; iterator.hasNext(); )  {
			          AccountGroupCutoff cutOffD = (AccountGroupCutoff) iterator.next() ;
			          RulesDefinition ruleD = cutOffD.getRulesDefinition() ;
			          records = (List ) getHibernateTemplate().find("select acct.ruleId from AccountGroupCutoff acct where acct.cutOffRuleId =?0 ",
										new Object[] {cutOffD.getCutOffRuleId()});
			          
			          if(records.size() == 1) {
			        	 Integer ruleId = (Integer) (records.get(0));
			        	  List recordsRulesConditions = getHibernateTemplate().find("select rc from RuleConditions rc where rc.id.ruleId = ?0 ",new Object[] {ruleId});
			        	  for (Object result : recordsRulesConditions ) {
			        		  session.delete(result);
			        	  }
			        	  List<RulesDefinition> recordsRulesDef = (List<RulesDefinition>) getHibernateTemplate().find("select r from RulesDefinition r where r.ruleId = ?0", new Object[] {ruleId});
			        	  for (RulesDefinition result : recordsRulesDef ) {
			        		  if(maintEventId != null)
			        			  result.setMainEventId(""+maintEventId);
			        		  session.delete(result);
			        	  }
			        	 
			        	 
			        	  List<AccountGroupCutoff> recordsCut = (List<AccountGroupCutoff>) getHibernateTemplate().find("select r from AccountGroupCutoff r where r.ruleId = ?0", new Object[] {ruleId});
			        	  for (AccountGroupCutoff result : recordsCut ) {
			        		  if(maintEventId != null)
			        		 	result.setMainEventId(""+maintEventId);
			        		  session.delete(result);
			        	  }  
			          }
			        }
			}
			
			if(maintEventId != null) {
				session.flush();
				tx.rollback();
			}
			else 
				tx.commit();
			
			// Condition to check list size
			
			log.debug(this.getClass().getName() + " - [crudCutOff] - Exit");
		} catch (Exception e) {
			e.printStackTrace();
			try {
				if (tx != null) {
					tx.rollback();
				}
			} catch (HibernateException exception) {
				log
						.error("HibernateException occured in rolling back transaction. Cause : "
								+ exception.getMessage());
			}
			log.error(this.getClass().getName()
					+ " - Exception Catched in [crudCutOff] method : - "
					+ e.getMessage());

			throw SwtErrorHandler.getInstance().handleException(e,
					"crudCutOff",
					AccountGroupsMaintenanceDAOHibernate.class);
		}
		finally {
			HibernateException hThrownException = JDBCCloser.close(session);
			if (hThrownException != null)
				throw new SwtException(hThrownException.getMessage());
			records = null;
		}
	}
	

	public List<AccountGroup> getAccountGroupDetailList(String selectedSpreadProfileId)
			throws SwtException {
		
		List<AccountGroup> listAccts = null;
	
		
		try {
			log.debug(this.getClass().getName() + " - [getAccountGroupDetailList] - Enter");
			// params[0] holds the restriction if the array is not empty 
			if (SwtUtil.isEmptyOrNull(selectedSpreadProfileId)) {
				listAccts = (List<AccountGroup> ) getHibernateTemplate().find(" select acctGroup from AccountGroup acctGroup, PCMCurrency ccy where acctGroup.currencyCode = ccy.id.currencyCode and (acctGroup.requireAuthorisation = 'N' OR acctGroup.requireAuthorisation is null) order by acctGroup.id.accGrpId, ccy.ordinal, acctGroup.ordinal");
			}
			else {
				listAccts = (List<AccountGroup> ) getHibernateTemplate().find("from AccountGroup acctGroup where acctGroup.spreadProfileId = ?0 and (acctGroup.requireAuthorisation = 'N' OR acctGroup.requireAuthorisation is null) order by acctGroup.id.accGrpId",
						new Object[] { selectedSpreadProfileId });
			}
			log.debug(this.getClass().getName() + " - [getAccountGroupDetailList] - Exit");
		} catch (Exception e) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [getAccountGroupDetailList] method : - "
					+ e.getMessage());

			throw SwtErrorHandler.getInstance().handleException(e,
					"getAccountGroupDetailList",
					AccountGroupsMaintenanceDAOHibernate.class);
		}

		return listAccts;
	}

	public AccountGroup getAcctGroupDetail(String acctGroupId)
			throws SwtException {
		
		AccountGroup acctGroup = null;
		List records = null;
		Iterator itr;
		
		try {
			log.debug(this.getClass().getName() + " - [getAcctGroupDetail] - Enter");
			records = (List ) getHibernateTemplate().find("from AccountGroup acctGrp where acctGrp.id.accGrpId = ?0 ",
							new Object[] {acctGroupId});
			if(records != null){
				itr = records.iterator();
				while(itr.hasNext()){
					acctGroup = (AccountGroup) itr.next();
				}
			}
			log.debug(this.getClass().getName() + " - [getAcctGroupDetail] - Exit");
		} catch (Exception e) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [getAcctGroupDetail] method : - "
					+ e.getMessage());

			throw SwtErrorHandler.getInstance().handleException(e,
					"getAcctGroupDetail",
					AccountGroupsMaintenanceDAOHibernate.class);
		}

		return acctGroup;
	}
	
   public Integer accountListCount(String groupId) throws SwtException {
		
		Integer accs = null;
		String query = null;
		List<Integer> noofRecords = null;
		
		try {
			log.debug(this.getClass().getName() + "- [accountListCount] - Entering ");
			query = "from AccountInGroup ac where ac.accGrpId =?0";
			// To execute the query and get the record count
			noofRecords = (List<Integer> ) getHibernateTemplate().find(
					query,
					new Object[] { groupId });
			accs = noofRecords.size();
		
			log.debug(this.getClass().getName() + "- [accountListCount] - Exit");
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [AccountListCount] method : - "
					+ exp.getMessage());
			exp.printStackTrace();
		} 
		
		return accs;
	}

	
	public void updateAcctGroup(AccountGroup acctGroup)
			throws SwtException {
		Session session = null;
		Transaction tx = null;
		SwtInterceptor interceptor = null;
		boolean requireAuthorisation = false;
		try {
			log.debug(this.getClass().getName() + " - [updateAcctGroup] - Enter");
			if(acctGroup != null && !SwtUtil.isEmptyOrNull(acctGroup.getMainEventId())) {
				requireAuthorisation = true;
			}
			interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");
			session = getHibernateTemplate().getSessionFactory().withOptions().interceptor(interceptor).openSession();
			tx = session.beginTransaction();
			session.update(acctGroup);
			if(requireAuthorisation) {
				session.flush();
				tx.rollback();
			}
			else 
				tx.commit();
			log.debug(this.getClass().getName() + " - [updateAcctGroup] - Exit");
		} catch (Exception e) {
			try {
				if (tx != null) {
					tx.rollback();
				}
			} catch (HibernateException exception) {
				log
						.error("HibernateException occured in rolling back transaction. Cause : "
								+ exception.getMessage());
			}
			log.error(this.getClass().getName()
					+ " - Exception Catched in [updateAcctGroup] method : - "
					+ e.getMessage());

			throw SwtErrorHandler.getInstance().handleException(e,
					"updateAcctGroup",
					AccountGroupsMaintenanceDAOHibernate.class);
		}
		finally {
			HibernateException hThrownException = JDBCCloser.close(session);
			if (hThrownException != null)
				throw new SwtException(hThrownException.getMessage());
		}
	}

	public void deleteAcctGroup(AccountGroup acctGroup)
			throws SwtException {
		Session session = null;
		Transaction tx = null;
		SwtInterceptor interceptor = null;
		Reserve reserve;
		boolean requireAuthorisation = false;
		try {
			log.debug(this.getClass().getName() + " - [deleteAcctGroup] - Enter");
			if(!acctGroup.isForceNoLogs()) {
				interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");
				session = getHibernateTemplate().getSessionFactory().withOptions().interceptor(interceptor).openSession();
			}else {
				session = getHibernateTemplate().getSessionFactory().openSession();
			}
			tx = session.beginTransaction();
			if(!SwtUtil.isEmptyOrNull(acctGroup.getMainEventId())) {
				requireAuthorisation = true;
			}
			if (acctGroup.getAccountInGroup() != null && acctGroup.getAccountInGroup().size() > 0 ) {
				for (AccountInGroup acctInGrp: acctGroup.getAccountInGroup()) {
					session.delete(acctInGrp);
				}
				
			}
			if (acctGroup.getAccountGroupCutoff() != null && acctGroup.getAccountGroupCutoff().size() > 0) {
				for (Iterator iterator = (acctGroup.getAccountGroupCutoff()).iterator() ; iterator.hasNext(); )  {
			          AccountGroupCutoff cutOffD = (AccountGroupCutoff) iterator.next() ;
			          RulesDefinition ruleD = cutOffD.getRulesDefinition() ;
			          List records = (List ) getHibernateTemplate().find("select acct.ruleId from AccountGroupCutoff acct where acct.cutOffRuleId =?0 ",
										new Object[] {cutOffD.getCutOffRuleId()});
			          
			          if(records.size() == 1) {
			        	 Integer ruleId = (Integer) (records.get(0));
			        	  List recordsRulesConditions = getHibernateTemplate().find("select rc from RuleConditions rc where rc.id.ruleId = ?0 ",new Object[] {ruleId});
			        	  for (Object result : recordsRulesConditions ) {
			        		  session.delete(result);
			        	  }
			        	  List recordsRulesDef = getHibernateTemplate().find("select r from RulesDefinition r where r.ruleId = ?0", new Object[] {ruleId});
			        	  for (Object resultRule : recordsRulesDef ) {
			        		  session.delete(resultRule);
			        	  }
			        	 
			          }
			        }
				
				
			}
		
			session.delete(acctGroup);
			
			if(requireAuthorisation) {
				session.flush();
				tx.rollback();
			}
			else 
				tx.commit();
			
			log.debug(this.getClass().getName() + " - [deleteAcctGroup] - Exit");
		} catch (Exception e) {
			e.printStackTrace();
			try {
				if (tx != null) {
					tx.rollback();
				}
			} catch (HibernateException exception) {
				log.error("HibernateException occured in rolling back transaction. Cause : "
						+ exception.getMessage());
			}
			log.error(this.getClass().getName()
					+ " - Exception Catched in [deleteCategory] method : - "
					+ e.getMessage());
			throw SwtErrorHandler.getInstance().handleException(e,
					"deleteCategory", AccountGroupsMaintenanceDAOHibernate.class);
		
		}finally {
			HibernateException hThrownException = JDBCCloser.close(session);
			if (hThrownException != null)
				throw new SwtException(hThrownException.getMessage());
		}
	}
	public Collection getAccountListDetails(String currencyCode)
			throws SwtException {
		
		ArrayList<Object[]> records = new ArrayList<Object[]>(); ;
		
		try {
			log.debug(this.getClass().getName() + " - [getAccountListDetails] - " + "Entry");
			records = (ArrayList<Object[]>) getHibernateTemplate().find(
					  "FROM AcctMaintenance a, Currency c "
					+ "WHERE c.id.hostId = a.id.hostId  AND c.id.entityId = a.id.entityId "
					+ " AND c.id.currencyCode = a.currcode AND  c.preFlag = 'Y'"
					+ " AND a.acctClass = 'N' AND  a.acctstatusflg  = 'O' AND a.currcode = ?0 "
					+ "AND not exists(FROM AccountInGroup  acctGrp WHERE a.id.hostId = acctGrp.id.hostId AND  "
					+ "a.id.entityId = acctGrp.id.entityId AND a.id.accountId = acctGrp.id.accountId) "
					+ "ORDER BY a.id.accountId", new Object[] {currencyCode});
			ArrayList<AcctMaintenance> accountList = new ArrayList<AcctMaintenance>();
			for (Object[] result : records) {
				AcctMaintenance account = (AcctMaintenance) result[0];
		
				accountList.add(account);
		      }
			
			log.debug(this.getClass().getName() + " - [getAccountListDetails] - " + "Exit");
			return accountList;
		} catch (Exception e) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [getAccountListDetails] method : - "
					+ e.getMessage());

			throw SwtErrorHandler.getInstance().handleException(e,
					"getAccountListDetails", ILMGeneralMaintenanceDAOHibernate.class);
			}
	}
		
	public Collection getOrdinalByCurrency(String currencyCode)
			throws SwtException {
		
		Collection records ;
		
		try {
			log.debug(this.getClass().getName() + " - [getOrdinalByCurrency] - " + "Entry");
			records = (List ) getHibernateTemplate().find(
					"from AccountGroup acct  where acct.currencyCode=?0", new Object[] {currencyCode});
			
			log.debug(this.getClass().getName() + " - [getOrdinalByCurrency] - " + "Exit");
		} catch (Exception e) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [getOrdinalByCurrency] method : - "
					+ e.getMessage());
			
			throw SwtErrorHandler.getInstance().handleException(e,
					"getOrdinalByCurrency", ILMGeneralMaintenanceDAOHibernate.class);
		}
		return records;
	}
	
	public Collection getReserveDetails(String acctGroupId)
			throws SwtException {
		
		List records = null;
		Iterator itr;
		
		try {
			log.debug(this.getClass().getName() + " - [getReserveDetails] - Enter");
			records = (List ) getHibernateTemplate().find("from Reserve res where res.accGrpId = ?0 ",
							new Object[] {acctGroupId});
			log.debug(this.getClass().getName() + " - [getReserveDetails] - Exit");
			
		} catch (Exception e) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [getReserveDetails] method : - "
					+ e.getMessage());

			throw SwtErrorHandler.getInstance().handleException(e,
					"getReserveDetails",
					AccountGroupsMaintenanceDAOHibernate.class);
		}

		return records;
	}
	public Collection getCutOffRulesDetails(String acctGroupId)
			throws SwtException {
		
		ArrayList<Object[]> records = new ArrayList<Object[]>();
		//ArrayList<Object[]> recordsConditon = new ArrayList<Object[]>();
		List recordsConditon = null;ArrayList
		<RuleConditions> ruleConditionsList = null;
		
		
		try {
			log.debug(this.getClass().getName() + " - [getCutOffRulesDetails] - Enter");
			records = (ArrayList<Object[]>) getHibernateTemplate()
					.find("from AccountGroupCutoff acctCut, RulesDefinition rulesDef where acctCut.accGrpId = ?0 and rulesDef.ruleId = acctCut.ruleId and rulesDef.ruleType = 'C' order by acctCut.ordinal asc",
							new Object[] {acctGroupId});
         ArrayList<AccountGroupCutoff> cutOffResult = new ArrayList<AccountGroupCutoff>();
			
			for (Object[] result : records) {
				AccountGroupCutoff cutOff = (AccountGroupCutoff) result[0];
				RulesDefinition rulesDef = (RulesDefinition) result[1];
				ruleConditionsList =  (ArrayList<RuleConditions>) getHibernateTemplate()
						.find("from RuleConditions rulesC where rulesC.id.ruleId = ?0",
								new Object[] {rulesDef.getRuleId()});
				rulesDef.setRuleConditions(ruleConditionsList);

				
				cutOff.setRulesDefinition(rulesDef);
		        
		        cutOffResult.add(cutOff);
		      }
			
			
			log.debug(this.getClass().getName() + " - [getCutOffRulesDetails] - Exit");
			return cutOffResult;
		} catch (Exception e) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [getCutOffRulesDetails] method : - "
					+ e.getMessage());
			
			throw SwtErrorHandler.getInstance().handleException(e,
					"getCutOffRulesDetails",
					AccountGroupsMaintenanceDAOHibernate.class);
		}
		
		
	}
	public Collection getAccountsInGroupList(String  accountGrpId) throws SwtException {
		
		ArrayList<Object[]> list = new ArrayList<Object[]>();
		
		try {
			log.debug(this.getClass().getName() + " - [getAccountsInGroupList] - " + "Entry");
			

			list = (ArrayList<Object[]>) getHibernateTemplate()
					.find("select acct from AccountInGroup acctGrp, AcctMaintenance acct  where acctGrp.accGrpId=?0 and acctGrp.id.accountId= acct.id.accountId and acctGrp.id.hostId= acct.id.hostId and acctGrp.id.entityId= acct.id.entityId",
							new Object[] { accountGrpId });
			
			log.debug(this.getClass().getName() + " - [getAccountsInGroupList] - " + "Exit");
		} catch (Exception e) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [getAccountsInGroupList] method : - "
					+ e.getMessage());

			throw SwtErrorHandler.getInstance().handleException(e,
					"getAccountsInGroupList",
					AccountGroupsMaintenanceDAOHibernate.class);
		}
		return list;
	}
	
	
	 public AccountGroup getAccountGroupFromAccountId(String hostId, String entityId , String accountId) throws SwtException {
		
		 AccountGroup group = null;
		ArrayList<AccountGroup> records ;
		
		try {
			log.debug(this.getClass().getName() + " - [getAccountGroupFromAccountId] - " + "Entry");
			
			
			records = (ArrayList<AccountGroup>)  getHibernateTemplate()
					.find("select grp from AccountInGroup acctGrp, AccountGroup grp  where  acctGrp.id.accountId= ?0 and acctGrp.id.hostId= ?1 and acctGrp.id.entityId= ?2 and acctGrp.accGrpId = grp.id.accGrpId",
							new Object[] { accountId, hostId , entityId});
			if(records.size()>0) {
				group = records.get(0);
			}
			
			log.debug(this.getClass().getName() + " - [getAccountGroupFromAccountId] - " + "Exit");
		} catch (Exception e) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [getAccountGroupFromAccountId] method : - "
					+ e.getMessage());
			
			throw SwtErrorHandler.getInstance().handleException(e,
					"getAccountGroupFromAccountId",
					AccountGroupsMaintenanceDAOHibernate.class);
		}
		return group;
	}
	 	
		public HashMap checkAccountInPayment(ArrayList accountsId, ArrayList entity) throws SwtException {
			String result=null;
			List  recordsIntegrity= null;
			HashMap<String,String> exist= null;
			HashMap<String, String> accountIdExist = new LinkedHashMap<String, String>();
			try {
				log.debug(this.getClass().getName() + " - [checkAccountInPayment] - "
						+ "Entry");
				 for (int i = 0; i < accountsId.size(); i++) {
				recordsIntegrity = PCMUtil.executeNamedSelectQuery("accountId_PayReq_query", accountsId.get(i).toString(), entity.get(i).toString());
					exist= (HashMap<String, String>) recordsIntegrity.get(0);
					result = exist.get("result");
					accountIdExist.put(accountsId.get(i).toString() + ";"+ entity.get(i).toString() , result);
				
				 }
				
				log.debug(this.getClass().getName() + " - [checkAccountInPayment] - "+ "Exit");
				return accountIdExist;
			} catch (Exception exp) {
				
				log.error(this.getClass().getName()
						+ " - Exception Catched in [checkAccountInPayment] method : - "
						+ exp.getMessage());
				throw SwtErrorHandler.getInstance().handleException(exp,
						"checkAccountInPayment", AccountGroupsMaintenanceDAOHibernate.class);
			} finally {
				accountIdExist = null;
				result= null;
				exist= null;
			}

		}
		@Override
		public Collection getAccountListDetailsMaintEvent(String currencyCode, String accountGroup) throws SwtException {
			ArrayList<Object[]> records = new ArrayList<Object[]>(); ;
			
			try {
				log.debug(this.getClass().getName() + " - [getAccountListDetails] - " + "Entry");
				records = (ArrayList<Object[]>) getHibernateTemplate().find(
						  "FROM AcctMaintenance a, Currency c "
						+ "WHERE c.id.hostId = a.id.hostId  AND c.id.entityId = a.id.entityId "
						+ " AND c.id.currencyCode = a.currcode AND  c.preFlag = 'Y'"
						+ " AND a.acctClass = 'N' AND  a.acctstatusflg  = 'O' AND a.currcode = ?0 "
						+ "AND not exists(FROM AccountInGroup  acctGrp WHERE a.id.hostId = acctGrp.id.hostId AND  "
						+ "a.id.entityId = acctGrp.id.entityId AND a.id.accountId = acctGrp.id.accountId AND acctGrp.accGrpId != ?1) "
						+ "ORDER BY a.id.accountId", new Object[] {currencyCode, accountGroup});
				ArrayList<AcctMaintenance> accountList = new ArrayList<AcctMaintenance>();
				for (Object[] result : records) {
					AcctMaintenance account = (AcctMaintenance) result[0];
			
					accountList.add(account);
			      }
				
				log.debug(this.getClass().getName() + " - [getAccountListDetails] - " + "Exit");
				return accountList;
			} catch (Exception e) {
				log.error(this.getClass().getName()
						+ " - Exception Catched in [getAccountListDetails] method : - "
						+ e.getMessage());

				throw SwtErrorHandler.getInstance().handleException(e,
						"getAccountListDetails", ILMGeneralMaintenanceDAOHibernate.class);
				}
		}
		
		 /**
		  * For Maintenance Event
		  * @param accountGroup
		  * @return
		  * @throws SwtException
		  */
		public void deleteRulesDefinitionForMaintEvent(String accountGroup) throws SwtException{
			
		}
		


}