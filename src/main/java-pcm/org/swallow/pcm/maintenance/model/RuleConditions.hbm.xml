<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE hibernate-mapping PUBLIC
        "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
        "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">

<hibernate-mapping>
	<class name="org.swallow.pcm.maintenance.model.RuleConditions" table="P_RULE_CONDITION">
		<composite-id name="id" class="org.swallow.pcm.maintenance.model.RuleConditions$Id"  >
			<key-property name="ruleId" column="rule_id" />
			<key-property name="conditionId" column="condition_id" />
		</composite-id>
		<property name="fieldName" column="FIELD_NAME" />
		<property name="operatorId" column="OPERATOR_ID"/>
		<property name="localValue" column="LOCAL_VALUE"/>
		<property name="fieldValue" column="FIELD_VALUE"/>
		<property name="dataType" column="DATA_TYPE"/>
		<property name="tableName" column="TABLE_NAME"/>
		<property name="nextCondition" column="NEXT_CONDITION"/>
		<property name="isActive" column="IS_ACTIVE" />
	</class>
</hibernate-mapping>