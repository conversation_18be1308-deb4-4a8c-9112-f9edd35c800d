/*
 * @(#)Currency.java 1.0 2019-02-25
 *
 * Copyright (c) 2006-2019 SwallowTech, Inc.
 * 14 Lion Yard, Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.pcm.maintenance.model;

import java.util.Hashtable;

import org.swallow.maintenance.model.CurrencyMaster;
import org.swallow.model.BaseObject;

/**
 * 
 * <AUTHOR> 
 * 
 * Class contain setter,getter methods. These methods used to getting 
 * and setting the values 
 * 
 */
public class PCMCurrency extends BaseObject implements org.swallow.model.AuditComponent  {
	
	private static final long serialVersionUID = 1L;

	public static Hashtable  logTable = new Hashtable();
	static {
		logTable.put("currencyCode","Currency Code");
		logTable.put("ordinal","Ordinal");
		logTable.put("displayMultiplier","Multiplier");
		logTable.put("largeAmountThreshold","Large Amount Threshold");
	}
	
	private Id id = new Id();
	private Integer ordinal;
	private String displayMultiplier;	
	private String multiplierDesc;	
	//private String ccyEntityForTimeframe;	
	private Long largeAmountThreshold;
	private CurrencyMaster currencyMaster = null;
	
	public Id getId() {
		return id;
	}
	public void setId(Id id) {
		this.id = id;
	}

	public static class Id extends BaseObject {
	
		private String currencyCode;
		
		public Id(String currencyCode) {
			this.currencyCode = currencyCode;
		}

		public Id() {
		}

		public String getCurrencyCode() {
			return currencyCode;
		}

		public void setCurrencyCode(String currencyCode) {
			this.currencyCode = currencyCode;
		}
	}

	public Integer getOrdinal() {
		return ordinal;
	}
	
	public void setOrdinal(Integer ordinal) {
		this.ordinal = ordinal;
	}
	
	public String getDisplayMultiplier() {
		return displayMultiplier;
	}
	
	public void setDisplayMultiplier(String displayMultiplier) {
		this.displayMultiplier = displayMultiplier;
	}
	
//	public String getCcyEntityForTimeframe() {
//		return ccyEntityForTimeframe;
//	}
//	
//	public void setCcyEntityForTimeframe(String ccyEntityForTimeframe) {
//		this.ccyEntityForTimeframe = ccyEntityForTimeframe;
//	}

	public Long getLargeAmountThreshold() {
		return largeAmountThreshold;
	}
	
	public void setLargeAmountThreshold(Long largeAmountThreshold) {
		this.largeAmountThreshold = largeAmountThreshold;
	}
	public CurrencyMaster getCurrencyMaster() {
		return currencyMaster;
	}
	public void setCurrencyMaster(CurrencyMaster currencyMaster) {
		this.currencyMaster = currencyMaster;
	}
	public String getMultiplierDesc() {
		return multiplierDesc;
	}
	public void setMultiplierDesc(String multiplierDesc) {
		this.multiplierDesc = multiplierDesc;
	}
}
