/**
 * @(#)ColumnInfo.java / 1.0 / 18 Aug 2009 / SEL Software
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard, Tremadoc Road, London UK
 * All Rights Reserved.
 * 
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.pcm.maintenance.model.core;

import java.io.Serializable;

/**
 * ColumnInfo.java
 * 
 * This java bean has getters & setters for datagrid columns
 * 
 * <AUTHOR>
 * @version New SMART-1.0
 * @date 29 Apr 2013: Modified by Saber Chebka to add support for generic datagrids
 */
public class ColumnInfo implements Serializable {

	// Column header
	private String columnHeader = null;
	// Data field tag
	private String columnData = null;
	// Column type
	private String columnType = null;
	// Column order
	private int columnOrder;
	// Column visual type
	private String visualType;
	// Column data id if that exist
	private String dataId;
	// Column data Provider
	private String dataProvider = null;
	// Column data Provider
	private String dataUrl;
	// Data params
	private String dataParams;
	// Column width
	private int columnWidth;
	// Column draggable flag
	private boolean draggable;
	// Column filterable flag
	private boolean filterable;
	// Column visible flag, default value is true
	private boolean visible = true;
	// Column visible flag for the default user
	private boolean visibleDEFAULT = true;
	/*START -  modified for  maintenance log screen by Thirumurugan on 18-sep-09*/
	// Column sort flag
	private boolean sort = true;
	/*END -  modified for  maintenance log screen by Thirumurugan on 18-sep-09*/
	
	// Physical column length in DB
	private int columnLength = 0;
	
	// Precision if column type is a number
	private int columnPrecision = 0;
	
	// column is nullable or not
	private boolean columnNullable = true;
	
	// The grid cell format
	private String cellFormat = null;
	
	// Column editable flag
	private boolean editable;
	// Column tooltip flag
	private String headerTooltip = null;
	// Column editable flag
	private boolean holiday = false;
	// Column editable flag
	private String columnGroup = null;
	private String maxChars = null;
	// Column dataprovider
	
	/**
	 * Default constructor
	 */
	public ColumnInfo() {
	}

	/**
	 * Constructor
	 * 
	 * @param columnHeader
	 * @param columnType
	 * @param columnData
	 * @param columnOrder
	 * @param columnWidth
	 * @param draggable
	 * @param filterable
	 */
	public ColumnInfo(String columnHeader, String columnData,
			String columnType, int columnOrder, int columnWidth,
			boolean draggable, boolean filterable) {
		// sets properties
		this.columnHeader = columnHeader;
		this.columnData = columnData;
		this.columnType = columnType;
		this.columnOrder = columnOrder;
		this.columnWidth = columnWidth;
		this.draggable = draggable;
		this.filterable = filterable;
	}

	/**
	 * Constructor
	 * 
	 * @param columnHeader
	 * @param columnType
	 * @param columnData
	 * @param columnOrder
	 * @param columnWidth
	 * @param draggable
	 * @param filterable
	 * @param visible
	 */
	public ColumnInfo(String columnHeader, String columnData,
			String columnType, int columnOrder, int columnWidth,
			boolean draggable, boolean filterable, boolean visible) {
		// sets properties
		this.columnHeader = columnHeader;
		this.columnData = columnData;
		this.columnType = columnType;
		this.columnOrder = columnOrder;
		this.columnWidth = columnWidth;
		this.draggable = draggable;
		this.filterable = filterable;
		this.visible = visible;
	}

	// Added for determining the sorting by Sarada 10/09/2009 start here
	/**
	 * Constructor added for sorting purpose
	 * 
	 * @param columnHeader
	 * @param columnType
	 * @param columnData
	 * @param columnOrder
	 * @param columnWidth
	 * @param draggable
	 * @param filterable
	 * @param sort
	 */
	public ColumnInfo(String columnHeader, String columnData,
			String columnType, boolean sort, int columnOrder, int columnWidth,
			boolean draggable, boolean filterable) {
		// sets properties
		this.columnHeader = columnHeader;
		this.columnData = columnData;
		this.columnType = columnType;
		this.columnOrder = columnOrder;
		this.columnWidth = columnWidth;
		this.draggable = draggable;
		this.filterable = filterable;
		this.sort = sort;// Added for determining the sorting
	}

	// Added for determining the sorting by Sarada 10/09/2009 end here

	// Add to set the boolean visible to column by Ezzeddine 29/11/2013 - Start
	/**
	 * Constructor added for sorting purpose
	 * 
	 * @param columnHeader
	 * @param columnType
	 * @param columnData
	 * @param columnOrder
	 * @param columnWidth
	 * @param draggable
	 * @param filterable
	 * @param sort
	 * @param visible
	 */
	public ColumnInfo(String columnHeader, String columnData,
			String columnType, boolean sort, int columnOrder, int columnWidth,
			boolean draggable, boolean filterable, boolean visible) {
		// sets properties
		this.columnHeader = columnHeader;
		this.columnData = columnData;
		this.columnType = columnType;
		this.columnOrder = columnOrder;
		this.columnWidth = columnWidth;
		this.draggable = draggable;
		this.filterable = filterable;
		this.sort = sort;
		this.visible = visible;
	}
	// Add to set the boolean visible to column by Ezzeddine 29/11/2013 - End
	
	/**
	 * Getter method of columnHeader
	 * 
	 * @return the columnHeader
	 */
	public String getColumnHeader() {
		return columnHeader;
	}

	/**
	 * Setter method of columnHeader
	 * 
	 * @param columnHeader
	 *            the columnHeader to set
	 */
	public void setColumnHeader(String columnHeader) {
		this.columnHeader = columnHeader;
	}

	/**
	 * Getter method of columnData
	 * 
	 * @return the columnData
	 */
	public String getColumnData() {
		return columnData;
	}

	/**
	 * Setter method of columnData
	 * 
	 * @param columnData
	 *            the columnData to set
	 */
	public void setColumnData(String columnData) {
		this.columnData = columnData;
	}

	/**
	 * Getter method of columnType
	 * 
	 * @return the columnType
	 */
	public String getColumnType() {
		return columnType;
	}

	/**
	 * Setter method of columnType
	 * 
	 * @param columnType
	 *            the columnType to set
	 */
	public void setColumnType(String columnType) {
		this.columnType = columnType;
	}

	/**
	 * Getter method of columnOrder
	 * 
	 * @return the columnOrder
	 */
	public int getColumnOrder() {
		return columnOrder;
	}

	/**
	 * Setter method of columnOrder
	 * 
	 * @param columnOrder
	 *            the columnOrder to set
	 */
	public void setColumnOrder(int columnOrder) {
		this.columnOrder = columnOrder;
	}

	/**
	 * Getter method of columnWidth
	 * 
	 * @return the columnWidth
	 */
	public int getColumnWidth() {
		return columnWidth;
	}

	/**
	 * Setter method of columnWidth
	 * 
	 * @param columnWidth
	 *            the columnWidth to set
	 */
	public void setColumnWidth(int columnWidth) {
		this.columnWidth = columnWidth;
	}

	/**
	 * Getter method of draggable
	 * 
	 * @return the draggable
	 */
	public boolean isDraggable() {
		return draggable;
	}

	/**
	 * Setter method of draggable
	 * 
	 * @param draggable
	 *            the draggable to set
	 */
	public void setDraggable(boolean draggable) {
		this.draggable = draggable;
	}

	/**
	 * Getter method of filterable
	 * 
	 * @return the filterable
	 */
	public boolean isFilterable() {
		return filterable;
	}

	/**
	 * Setter method of filterable
	 * 
	 * @param filterable
	 *            the filterable to set
	 */
	public void setFilterable(boolean filterable) {
		this.filterable = filterable;
	}

	/**
	 * Getter method of visible
	 * 
	 * @return the visible
	 */
	public boolean isVisible() {
		return visible;
	}

	/**
	 * Setter method of visible
	 * 
	 * @param visible
	 *            the visible to set
	 */
	public void setVisible(boolean visible) {
		this.visible = visible;
	}

	/**
	 * Getter method of sort
	 * 
	 * @return the sort
	 */
	public boolean isSort() {// Added for sort
		return sort;
	}

	/**
	 * Setter method of sort
	 * 
	 * @return the sort
	 */
	public void setSort(boolean sort) {// Added for sort
		this.sort = sort;
	}

	public String getDataId() {
		return dataId;
	}

	public void setDataId(String dataId) {
		this.dataId = dataId;
	}

	public String getVisualType() {
		return visualType;
	}

	public void setVisualType(String visualType) {
		this.visualType = visualType;
	}

	public String getDataParams() {
		return dataParams;
	}

	public void setDataParams(String dataParams) {
		this.dataParams = dataParams;
	}

	public String getDataProvider() {
		return dataProvider;
	}

	public void setDataProvider(String dataProvider) {
		this.dataProvider = dataProvider;
	}

	public boolean isEditable() {
		return editable;
	}

	public void setEditable(boolean editable) {
		this.editable = editable;
	}

	public int getColumnLength() {
		return columnLength;
	}

	public void setColumnLength(int columnLength) {
		this.columnLength = columnLength;
	}

	public int getColumnPrecision() {
		return columnPrecision;
	}

	public void setColumnPrecision(int columnPrecision) {
		this.columnPrecision = columnPrecision;
	}

	public boolean isColumnNullable() {
		return columnNullable;
	}

	public void setColumnNullable(boolean columnNullable) {
		this.columnNullable = columnNullable;
	}

	public String getCellFormat() {
		return cellFormat;
	}

	public void setCellFormat(String cellFormat) {
		this.cellFormat = cellFormat;
	}

	public boolean isVisibleDEFAULT() {
		return visibleDEFAULT;
	}

	public void setVisibleDEFAULT(boolean visibleDEFAULT) {
		this.visibleDEFAULT = visibleDEFAULT;
	}

	public String getDataUrl() {
		return dataUrl;
	}

	public void setDataUrl(String dataUrl) {
		this.dataUrl = dataUrl;
	}

	public boolean isHoliday() {
		return holiday;
	}

	public void setHoliday(boolean holiday) {
		this.holiday = holiday;
	}
	public String getHeaderTooltip() {
		return headerTooltip;
	}

	public void setHeaderTooltip(String headerTooltip) {
		this.headerTooltip = headerTooltip;
	}

	public String getColumnGroup() {
		return columnGroup;
	}

	public void setColumnGroup(String columnGroup) {
		this.columnGroup = columnGroup;
	}

	public String getMaxChars() {
		return maxChars;
	}

	public void setMaxChars(String maxChars) {
		this.maxChars = maxChars;
	}


}