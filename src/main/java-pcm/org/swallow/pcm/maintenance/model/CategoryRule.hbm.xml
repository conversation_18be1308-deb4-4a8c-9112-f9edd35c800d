<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE hibernate-mapping PUBLIC
        "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
        "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">

<hibernate-mapping>
    <class name="org.swallow.pcm.maintenance.model.CategoryRule" table="PC_CATEGORY_RULE">
    	<id name="categoryRuleId" type="long" column="CATEGORY_RULE_ID">
			<generator class="sequence">
				<param name="sequence_name">SEQ_PC_CATEGORY_RULE</param>
			</generator>
		</id>
		<property name="categoryId" column="CATEGORY_ID" not-null="true"/>	
		<property name="categoryRuleName" column="NAME" not-null="false"/>	
		<property name="ordinal" column="ORDINAL" not-null="false"/>	
		<property name="applyOnlyToSource" column="APPLY_ONLY_TO_SOURCE" not-null="false"/>			
		<property name="applyOnlyToCcy" column="APPLY_ONLY_TO_CCY" not-null="false"/>
		<property name="applyOnlyToEntity" column="APPLY_ONLY_TO_ENTITY" not-null="false"/>				
		<property name="ruleId" column="RULE_ID" not-null="false"/>	
	</class>
</hibernate-mapping>