/*
 * @(#)RunningTotals.java 1.0 2019-02-26
 *
 * Copyright (c) 2006-2019 SwallowTech, Inc.
 * 14 Lion Yard, Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.pcm.maintenance.model;

import java.util.Date;

import org.swallow.model.BaseObject;

/**
 * 
 * <AUTHOR> 
 * 
 * Class contain setter,getter methods. These methods used to getting 
 * and setting the values 
 * 
 */
public class RunningTotals extends BaseObject {
	
	private static final long serialVersionUID = 1L;

	private Id id = new Id();
	private long totalPaidForValueDate;	
	private long totalPaidSinceLastProcPt;	
	private Date valueDate;
	
	public Id getId() {
		return id;
	}
	public void setId(Id id) {
		this.id = id;
	}

	public static class Id extends BaseObject {
	
		private String accGrpId;
		private String priorityId;
		
		public Id(String accGrpId, String priorityId) {
			this.accGrpId = accGrpId;
			this.priorityId = priorityId;
		}

		public Id() {
		}

		public String getAccGrpId() {
			return accGrpId;
		}

		public void setAccGrpId(String accGrpId) {
			this.accGrpId = accGrpId;
		}

		public String getPriorityId() {
			return priorityId;
		}

		public void setPriorityId(String priorityId) {
			this.priorityId = priorityId;
		}
	}

	public long getTotalPaidForValueDate() {
		return totalPaidForValueDate;
	}
	public void setTotalPaidForValueDate(long totalPaidForValueDate) {
		this.totalPaidForValueDate = totalPaidForValueDate;
	}
	public long getTotalPaidSinceLastProcPt() {
		return totalPaidSinceLastProcPt;
	}
	public void setTotalPaidSinceLastProcPt(long totalPaidSinceLastProcPt) {
		this.totalPaidSinceLastProcPt = totalPaidSinceLastProcPt;
	}
	public Date getValueDate() {
		return valueDate;
	}
	public void setValueDate(Date valueDate) {
		this.valueDate = valueDate;
	}
}
