/*
 * @(#)AccountInGroup.java 1.0 2019-02-25
 *
 * Copyright (c) 2006-2019 SwallowTech, Inc.
 * 14 Lion Yard, Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.pcm.maintenance.model;

import java.util.Hashtable;

import org.swallow.maintenance.model.AcctMaintenance;
import org.swallow.model.BaseObject;

/**
 * 
 * <AUTHOR> 
 * 
 * Class contain setter,getter methods. These methods used to getting 
 * and setting the values 
 * 
 */
public class AccountInGroup extends BaseObject implements org.swallow.model.AuditComponent {
	
	private static final long serialVersionUID = 1L;
	public static Hashtable  logTable = new Hashtable();
	static {
		logTable.put("accGrpId","Account Group Id");
		logTable.put("id.accountId"," Account ID");
		logTable.put("id.entityId","Entity ID");
		logTable.put("id.hostId","Host ID");
	}
	public final static String CLASSNAME = "AccountInGroup";
	private Id id = new Id();
	//private String checkAccountLiquidity;
	private String accGrpId;	
	private AcctMaintenance acctMaintenance = null;
	
	public Id getId() {
		return id;
	}
	public void setId(Id id) {
		this.id = id;
	}

	
	
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (getClass() != obj.getClass())
			return false;
		AccountInGroup other = (AccountInGroup) obj;
		if (accGrpId == null) {
			if (other.accGrpId != null)
				return false;
		} else if (!accGrpId.equals(other.accGrpId))
			return false;
		if (id == null) {
			if (other.id != null)
				return false;
		} else if (!id.equals(other.id))
			return false;
		return true;
	}



	public static class Id extends BaseObject {
	
		private String hostId;
		private String entityId;		
		private String accountId;
		
		public Id(String hostId, String entityId, String accountId) {
			this.hostId = hostId;
			this.entityId = entityId;
			this.accountId = accountId;
		}

		public Id() {
		}

		public String getHostId() {
			return hostId;
		}

		public void setHostId(String hostId) {
			this.hostId = hostId;
		}

		public String getEntityId() {
			return entityId;
		}

		public void setEntityId(String entityId) {
			this.entityId = entityId;
		}

		public String getAccountId() {
			return accountId;
		}

		public void setAccountId(String accountId) {
			this.accountId = accountId;
		}


		@Override
		public boolean equals(Object obj) {
			if (this == obj)
				return true;
			if (getClass() != obj.getClass())
				return false;
			Id other = (Id) obj;
			if (accountId == null) {
				if (other.accountId != null)
					return false;
			} else if (!accountId.equals(other.accountId))
				return false;
			if (entityId == null) {
				if (other.entityId != null)
					return false;
			} else if (!entityId.equals(other.entityId))
				return false;
			if (hostId == null) {
				if (other.hostId != null)
					return false;
			} else if (!hostId.equals(other.hostId))
				return false;
			return true;
		}
		
		
		
	}

	/*public String getCheckAccountLiquidity() {
		return checkAccountLiquidity;
	}
	public void setCheckAccountLiquidity(String checkAccountLiquidity) {
		this.checkAccountLiquidity = checkAccountLiquidity;
	}*/
	public String getAccGrpId() {
		return accGrpId;
	}
	public void setAccGrpId(String accGrpId) {
		this.accGrpId = accGrpId;
	}
	public AcctMaintenance getAcctMaintenance() {
		return acctMaintenance;
	}
	public void setAcctMaintenance(AcctMaintenance acctMaintenance) {
		this.acctMaintenance = acctMaintenance;
	}

	

}
