package org.swallow.pcm.maintenance.model.core.kv;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;

import javax.xml.bind.annotation.XmlAttribute;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;

import org.swallow.exception.SwtException;
import org.swallow.util.SwtConstants;
import org.swallow.util.SwtUtil;


//XmLElementWrapper generates a wrapper element around XML representation
@XmlRootElement
public class TabKVType implements Serializable{
	private ArrayList<KVType> elementList;

	private String tableName;
	private String operation;
	private String tableLevel;

	public ArrayList<KVType> getElementList() {
		if(elementList==null)
			elementList = new ArrayList<KVType>();
		
		return elementList;
	}
	
	/**
	 * Get Kv value as a string, default value is supported
	 * @param key
	 * @param defaultValue
	 * @return
	 */
	public String getValueAsString(String key, String...defaultValue){
		String retVal = null;
		ArrayList<KVType> list = this.getElementList();
		for (KVType kv:list) {
			if(key.equalsIgnoreCase(kv.getKey())){
				retVal = kv.getValue();
				break;
			}
		}
		return retVal!=null?retVal:(defaultValue.length!=0?defaultValue[0]:null);
	}
	
	public int getValueAsInt(String key, int...defaultValue){
		String strValue = getValueAsString(key);
		return !SwtUtil.isEmptyOrNull(strValue)?Integer.parseInt(strValue):(defaultValue.length!=0?defaultValue[0]:null);
	}
	
	public Date getValueAsDate(String key, Date...defaultValue) throws SwtException{
		String strValue = getValueAsString(key);
		return !SwtUtil.isEmptyOrNull(strValue)?SwtUtil.parseDate(strValue, SwtConstants.ISO_DATE_FORMAT):(defaultValue.length!=0?defaultValue[0]:null);
	}
	
	public double getValueAsDouble(String key, double...defaultValue){
		String strValue = getValueAsString(key);
		return !SwtUtil.isEmptyOrNull(strValue)?Double.parseDouble(strValue):(defaultValue.length!=0?defaultValue[0]:null);
	}
	
	//TODO: Please add support for other data types here ....
	/////////................................................

	@XmlElement(name = "kvType")
	public void setElementList(ArrayList<KVType> elementList) {
		this.elementList = elementList;
	}

	public String getTableName() {
		return tableName;
	}
	@XmlAttribute
	public void setTableName(String tableName) {
		this.tableName = tableName;
	}

	public String getOperation() {
		return operation;
	}
	@XmlAttribute
	public void setOperation(String operation) {
		this.operation = operation;
	}

	public String getTableLevel() {
		return tableLevel;
	}
	@XmlAttribute
	public void setTableLevel(String tableLevel) {
		this.tableLevel = tableLevel;
	}


}
