<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE hibernate-mapping PUBLIC
        "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
        "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">

<hibernate-mapping>
    <class name="org.swallow.pcm.maintenance.model.Dictionary" table="S_DICTIONARY"   >
    	<composite-id name="id" class="org.swallow.pcm.maintenance.model.Dictionary$Id"  unsaved-value="any">
			<key-property name="textId" access="field" column="TEXT_ID" />
			<key-property name="languageId" access="field" column="LANGUAGE_ID" />	
		</composite-id>
		   <property column="TEXT" not-null="true" name="text"/>
    </class>
</hibernate-mapping>