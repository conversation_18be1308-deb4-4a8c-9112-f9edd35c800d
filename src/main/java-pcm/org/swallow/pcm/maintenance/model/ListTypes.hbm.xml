<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE hibernate-mapping PUBLIC
        "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
        "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">

<hibernate-mapping>
     <class name="org.swallow.pcm.maintenance.model.ListTypes" table="S_CFG_TYPE">
		<id name="typeId" type="integer" column="TYPE_ID">
			<generator class="assigned" />
		</id>
		
		<property name="name" column="NAME" not-null="true"/>	
		<property name="narrative" column="NARRATIVE"/>	
		<property name="moduleId" column="MODULE_ID" not-null="true"/>	
		<property name="editable" column="EDITABLE" not-null="true"/>	
    	<property name="maxCodeLength" column="MAX_CODE_LEN"/>
    	</class>
</hibernate-mapping>