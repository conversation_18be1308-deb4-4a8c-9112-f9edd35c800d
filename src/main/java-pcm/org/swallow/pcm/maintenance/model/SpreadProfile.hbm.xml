<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE hibernate-mapping PUBLIC
        "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
        "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">

<hibernate-mapping>
    <class name="org.swallow.pcm.maintenance.model.SpreadProfile" table="PC_SPREAD_PROFILE">
    	<composite-id class="org.swallow.pcm.maintenance.model.SpreadProfile$Id" name="id" unsaved-value="any">
		   <key-property name="spreadProfileId" access="field" column="SPREAD_PROFILE_ID"/>
		</composite-id>
    	
		<property name="spreadProfileName" column="NAME" not-null="false"/>	
		<property name="currencyCode" column="CURRENCY_CODE" not-null="false"/>	
		<set name="spreadProcessPoint" table="PC_SPREAD_PROCESS_POINT" inverse="true" cascade="delete" lazy="false">
			<key>
			    <column name="SPREAD_PROFILE_ID" not-null="true" />
			</key>
			<one-to-many class="org.swallow.pcm.maintenance.model.SpreadProcessPoint" />
		</set>
	
<property name="requireAuthorisation" column="REQUIRE_AUTHORISATION" not-null="false"/>
	</class>
</hibernate-mapping>