/**
 * @(#)TabInfo.java / 1.0 / 18 Aug 2009 / SEL Software
 *
 * Copyright (c) 2006-2019 SwallowTech, Inc.
 * 14 Lion Yard, Tremadoc Road, London UK
 * All Rights Reserved.
 * 
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.pcm.maintenance.model.core;

import java.io.Serializable;

/**
 * TabInfo.java
 * 
 * This java bean has getters & setters for form dropdown option
 * 
 * <AUTHOR>
 * @version 
 * @date 30 May 2019
 */
public class TabInfo implements Serializable{
	private String dateLabel  = null;
	private String content = null;
	private boolean businessday;
	private boolean selected;

	

	/**
	 * Default constructor
	 */
	public TabInfo() {
	}

	/**
	 * Constructor
	 * 
	 * @param dateLabel
	 * @param content
	 * @param businessday
	 */
	public TabInfo(String dateLabel, String content, boolean businessday) {
		// Set properties
		this.dateLabel = dateLabel;
		this.content = content;
		this.businessday = businessday;
	}

	public String getDateLabel() {
		return dateLabel;
	}

	public void setDateLabel(String dateLabel) {
		this.dateLabel = dateLabel;
	}

	public String getContent() {
		return content;
	}

	public void setContent(String content) {
		this.content = content;
	}

	public boolean isBusinessday() {
		return businessday;
	}

	public void setBusinessday(boolean businessday) {
		this.businessday = businessday;
	}

	public boolean isSelected() {
		return selected;
	}

	public void setSelected(boolean selected) {
		this.selected = selected;
	}

	/**
	 * Constructor
	 * 
	 * @param dateLabel
	 * @param content
	 * @param businessday
	 * @param selected
	 */
	public TabInfo(String dateLabel, String content, boolean businessday, boolean selected) {
		// Set properties
		this.dateLabel = dateLabel;
		this.content = content;
		this.businessday = businessday;
		this.selected = selected;
	}


	
}