/*
 * @(#)GroupSpreadRunInfo.java 1.0 2019-02-26
 *
 * Copyright (c) 2006-2019 SwallowTech, Inc.
 * 14 Lion Yard, Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.pcm.maintenance.model;

import org.swallow.model.BaseObject;

/**
 * 
 * <AUTHOR> 
 * 
 * Class contain setter,getter methods. These methods used to getting 
 * and setting the values 
 * 
 */
public class GroupSpreadRunInfo extends BaseObject {
	
	private static final long serialVersionUID = 1L;

	private Id id = new Id();
	private String lastSuccessfulRun;	
	
	public Id getId() {
		return id;
	}
	public void setId(Id id) {
		this.id = id;
	}

	public static class Id extends BaseObject {
	
		private String accGrpId;
		private String spreadProfileId;
		private long processPointSeq;
		
		public Id(String accGrpId, String spreadProfileId, long processPointSeq) {
			this.accGrpId = accGrpId;
			this.spreadProfileId = spreadProfileId;
			this.processPointSeq = processPointSeq;
		}

		public Id() {
		}

		public String getAccGrpId() {
			return accGrpId;
		}

		public void setAccGrpId(String accGrpId) {
			this.accGrpId = accGrpId;
		}

		public String getSpreadProfileId() {
			return spreadProfileId;
		}

		public void setSpreadProfileId(String spreadProfileId) {
			this.spreadProfileId = spreadProfileId;
		}
		
		public long getProcessPointSeq() {
			return processPointSeq;
		}
		
		public void setProcessPointSeq(long processPointSeq) {
			this.processPointSeq = processPointSeq;
		}
	}

	public String getLastSuccessfulRun() {
		return lastSuccessfulRun;
	}
	
	public void setLastSuccessfulRun(String lastSuccessfulRun) {
		this.lastSuccessfulRun = lastSuccessfulRun;
	}

	
}
