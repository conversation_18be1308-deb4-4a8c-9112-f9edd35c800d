/*
 * @(#)AccountGroupCutoff.java 1.0 2019-02-25
 *
 * Copyright (c) 2006-2019 SwallowTech, Inc.
 * 14 Lion Yard, Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.pcm.maintenance.model;

import java.util.Hashtable;

import org.swallow.model.BaseObject;

/**
 * 
 * <AUTHOR> 
 * 
 * Class contain setter,getter methods. These methods used to getting 
 * and setting the values 
 * 
 */
public class AccountGroupCutoff extends BaseObject implements org.swallow.model.AuditComponent {
	
	public static Hashtable  logTable = new Hashtable();
	static {
		logTable.put("accGrpId","Account Group Id");
		logTable.put("ruleId","Rule Id");
		logTable.put("logText","Log Text");
		logTable.put("ordinal","Ordinal");
		logTable.put("cutoffTime","Cut-off Time");
	}
	
	private static final long serialVersionUID = 1L;
	private Long cutOffRuleId;
	private String accGrpId;
	private Long ordinal;
	private String cutoffTime;
	private String logText;
	private Integer ruleId;
	private RulesDefinition rulesDefinition= null;
	public final static String CLASSNAME = "AccountGroupCutoff";
	
	public Long getCutOffRuleId() {
		return cutOffRuleId;
	}
	public void setCutOffRuleId(Long cutOffRuleId) {
		this.cutOffRuleId = cutOffRuleId;
	}
	public String getAccGrpId() {
		return accGrpId;
	}
	public void setAccGrpId(String accGrpId) {
		this.accGrpId = accGrpId;
	}
	public Long getOrdinal() {
		return ordinal;
	}
	public void setOrdinal(Long ordinal) {
		this.ordinal = ordinal;
	}
	public String getCutoffTime() {
		return cutoffTime;
	}
	public void setCutoffTime(String cutoffTime) {
		this.cutoffTime = cutoffTime;
	}
	
	public String getLogText() {
		return logText;
	}
	public void setLogText(String logText) {
		this.logText = logText;
	}
	public Integer getRuleId() {
		return ruleId;
	}
	public void setRuleId(Integer ruleId) {
		this.ruleId = ruleId;
	}
	public RulesDefinition getRulesDefinition() {
		return rulesDefinition;
	}
	public void setRulesDefinition(RulesDefinition rulesDefinition) {
		this.rulesDefinition = rulesDefinition;
	}
}
