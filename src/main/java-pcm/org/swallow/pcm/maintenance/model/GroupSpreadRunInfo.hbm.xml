<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE hibernate-mapping PUBLIC
        "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
        "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">

<hibernate-mapping>
    <class name="org.swallow.pcm.maintenance.model.GroupSpreadRunInfo" table="PC_GRP_SPREAD_RUN_INFO">
    	<composite-id name="id" class="org.swallow.pcm.maintenance.model.GroupSpreadRunInfo$Id" >
			 <key-property name="accGrpId" access="field" column="ACC_GRP_ID"/>
			 <key-property name="spreadProfileId" access="field" column="SPREAD_PROFILE_ID"/>
			 <key-property name="processPointSeq" access="field" column="PROCESS_POINT_SEQ"/>
		</composite-id>
		<property name="lastSuccessfulRun" column="LAST_SUCCESSFUL_RUN" not-null="false"/>	
	</class>
</hibernate-mapping>