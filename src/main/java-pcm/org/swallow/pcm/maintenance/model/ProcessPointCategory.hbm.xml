<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE hibernate-mapping PUBLIC
        "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
        "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">

<hibernate-mapping>
    <class name="org.swallow.pcm.maintenance.model.ProcessPointCategory" table="PC_PROCESS_POINT_CATEGORY">
    	<composite-id class="org.swallow.pcm.maintenance.model.ProcessPointCategory$Id" name="id" unsaved-value="any">
		   <key-property name="processPointId" access="field" column="PROCESS_POINT_ID"/>
		   <key-property name="categoryId" access="field" column="CATEGORY_ID"/>
		</composite-id>
	</class>
</hibernate-mapping>