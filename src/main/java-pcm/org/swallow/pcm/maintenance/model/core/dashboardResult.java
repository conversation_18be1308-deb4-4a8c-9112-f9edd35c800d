package org.swallow.pcm.maintenance.model.core;

import java.io.Serializable;

public class dashboardResult implements Serializable{
	private String sodBalance = null;
	private String confirmedCredit = null;
	private String creditLine = null;
	private String releasedPayments = null;
	private String availableLiqEx = null;
	private String availabelLiqInc = null;
	private String reservedBalance = null;
	private String inputSince = null;
	private String xmlOut = null;
	private String fcastDr = null;
	
	
	/**
	 * Default constructor
	 */
	public dashboardResult() {
	}

	/**
	 * Constructor
	 */
	public dashboardResult(String sodBalance, String confirmedCredit, String  creditLine, String  releasedPayments, String  availableLiqEx, String  availabelLiqInc,String  reservedBalance,String  xmlOut) {
		// Set properties
		this.sodBalance = sodBalance;
		this.confirmedCredit = confirmedCredit;
		this.creditLine = creditLine;
		this.releasedPayments = releasedPayments;
		this.availableLiqEx = availableLiqEx;
		this.availabelLiqInc = availabelLiqInc;
		this.reservedBalance = reservedBalance;
		this.xmlOut = xmlOut;
	}
	public String getSodBalance() {
		return sodBalance;
	}
	public void setSodBalance(String sodBalance) {
		this.sodBalance = sodBalance;
	}
	public String getConfirmedCredit() {
		return confirmedCredit;
	}
	public void setConfirmedCredit(String confirmedCredit) {
		this.confirmedCredit = confirmedCredit;
	}
	public String getCreditLine() {
		return creditLine;
	}
	public void setCreditLine(String creditLine) {
		this.creditLine = creditLine;
	}
	public String getAvailableLiqEx() {
		return availableLiqEx;
	}
	public void setAvailableLiqEx(String availableLiqEx) {
		this.availableLiqEx = availableLiqEx;
	}
	public String getAvailabelLiqInc() {
		return availabelLiqInc;
	}
	public void setAvailabelLiqInc(String availabelLiqInc) {
		this.availabelLiqInc = availabelLiqInc;
	}
	public String getReservedBalance() {
		return reservedBalance;
	}
	public void setReservedBalance(String reservedBalance) {
		this.reservedBalance = reservedBalance;
	}
	public String getXmlOut() {
		return xmlOut;
	}
	public void setXmlOut(String xmlOut) {
		this.xmlOut = xmlOut;
	}
	public String getReleasedPayments() {
		return releasedPayments;
	}
	public void setReleasedPayments(String releasedPayments) {
		this.releasedPayments = releasedPayments;
	}

	public String getInputSince() {
		return inputSince;
	}

	public void setInputSince(String inputSince) {
		this.inputSince = inputSince;
	}

	public String getFcastDr() {
		return fcastDr;
	}

	public void setFcastDr(String fcastDr) {
		this.fcastDr = fcastDr;
	}
	
	

}
