<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE hibernate-mapping PUBLIC
        "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
        "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">

<hibernate-mapping>
    <class name="org.swallow.pcm.maintenance.model.Category" table="PC_CATEGORY">
		<composite-id class="org.swallow.pcm.maintenance.model.Category$Id" name="id" unsaved-value="any">
		   <key-property name="categoryId" access="field" column="CATEGORY_ID"/>
		</composite-id>
		<property name="categoryName" column="NAME" not-null="true"/>	
		<property name="ordinal" column="ORDINAL" not-null="false"/>
		<property name="isActive" column="IS_ACTIVE" not-null="false"/>		
		<property name="urgentSpreadInd" column="URGENT_SPREAD_IND" not-null="false"/>			
		<property name="useLiqCheck" column="USE_LIQ_CHECK" not-null="false"/>
		<property name="assignmentMethod" column="ASSIGNMENT_METHOD" not-null="false"/>		
		<property name="setReleaseTime" column="SET_RELEASE_TIME" not-null="false"/>		
		<property name="specifiedReleaseTime" column="SPECIFIED_RELEASE_TIME" not-null="false"/>		
		<property name="inclTargetPayReleased" column="INCL_TARGET_PAY_RELEASED" not-null="false"/>		
		<property name="inclInAvailableLiqCalc" column="INCL_IN_AVAILABLE_LIQ_CALC" not-null="false"/>	
		<property name="releaseValueDateOffset" column="RELEASE_VDATE_OFFSET" not-null="false"/>
		
<property name="ruleAssignPriority" column="RULE_ASSIGN_PRIORITY" not-null="false"/>
		<set name="categoryRules" inverse="true" lazy="false"  cascade="all" table="PC_CATEGORY_RULE"  order-by="ordinal">
      		<key>
            	<column name="CATEGORY_ID" not-null="true" />
      		</key>
      		<one-to-many class="org.swallow.pcm.maintenance.model.CategoryRule" />
   		</set>
	</class>
</hibernate-mapping>