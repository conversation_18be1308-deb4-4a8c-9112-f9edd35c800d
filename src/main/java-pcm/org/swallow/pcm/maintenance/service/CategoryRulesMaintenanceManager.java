/*
 * @(#)CategoryRulesMaintenanceManager.java 1.0 06/03/2019
 *
 * Copyright (c) 2006-2019 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.pcm.maintenance.service;

import java.util.Collection;

import org.swallow.exception.SwtException;
import org.swallow.pcm.maintenance.dao.CategoryRulesMaintenanceDAO;
import org.swallow.pcm.maintenance.model.Category;
import org.swallow.pcm.maintenance.model.CategoryRule;

public interface CategoryRulesMaintenanceManager {

	public void setCategoryRulesMaintenanceDAO(CategoryRulesMaintenanceDAO categoryRulesMaintenanceDAO);
	
	public Integer getMaxOrder(String categoryId) throws SwtException;
	
	public Collection getCategoryRuleDetailList() throws SwtException;
	
	public Category getCategoryRuleByCategoryId(Category category) throws SwtException;
	
	public CategoryRule getCategoryRuleById(Long categoryRuleId) throws SwtException;
	
}
