/*
 * @(#)ExpressionBuilderManagerImpl.java 1.0 06/03/2019
 *
 * Copyright (c) 2006-2019 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */

package org.swallow.pcm.maintenance.service.impl;

import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.pcm.maintenance.dao.ExpressionBuilderDAO;
import org.swallow.pcm.maintenance.model.Dictionary;
import org.swallow.pcm.maintenance.model.RuleConditions;
import org.swallow.pcm.maintenance.model.RulesDefinition;
import org.swallow.pcm.maintenance.model.TypeValues;
import org.swallow.pcm.maintenance.service.ExpressionBuilderManager;

@Component("expressionBuilderManager")
public class ExpressionBuilderManagerImpl implements ExpressionBuilderManager {
	// Final instance for log
	private final Log log = LogFactory.getLog(ExpressionBuilderManagerImpl.class);
	
	@Autowired
	private ExpressionBuilderDAO expressionBuilderDAO = null;

	public void setExpressionBuilderDAO(ExpressionBuilderDAO expressionBuilderDAO) {
		this.expressionBuilderDAO = expressionBuilderDAO;
	}

	/**
	 * This method is used to get all the rulesDefinition objects from A_RULES table
	 * 
	 * @param langId
	 * @param selectedFilter
	 * @param selectedSort
	 * @param moduleId
	 * @param isRiskFactor
	 * @return List<RulesDefinition> - list of rules objects
	 * @throws SwtException
	 */
	public List<RulesDefinition> getListRules(String langId, String moduleId,String isRiskFactor, String selectedFilter,
			String selectedSort) throws SwtException {
		// Variable List to hold list RulesDefinition
		List<RulesDefinition> rulesDefList = null;

		try {
			// log debug message
			log.debug(this.getClass().getName() + " - [ getListRules ] - " + "Entry");
			// Get list rules
			rulesDefList = expressionBuilderDAO.getListRules(langId, moduleId,isRiskFactor, selectedFilter, selectedSort);

		} catch (Exception ex) {
			// log error message
			log.error(this.getClass().getName()
					+ " - Exception Caught in [getListRules] method : - "
					+ ex.getMessage());
			throw SwtErrorHandler.getInstance().handleException(ex,
					"getListRules", this.getClass());
		} finally {
			// log debug message
			log.debug(this.getClass().getName() + " - [getListRules] - Exit");
		}
		// return result as list
		return rulesDefList;
	}
	
	
	/**
	 * Method to remove Rule details
	 * 
	 * @param rule
	 * @throws SwtException
	 */
	public int removeRule(RulesDefinition rule) throws SwtException {

		// Variable Integer
		int n = 0;

		try {
			// log debug message
			log.debug(this.getClass().getName() + " - [ removeRule ] - "
					+ "Entry");
			// delete Rule
			n = expressionBuilderDAO.removeRule(rule);

		} catch (Exception ex) {
			// log error message
			log.error(this.getClass().getName()
					+ " - Exception Caught in [removeRule] method : - "
					+ ex.getMessage());
			// throw Exception
			throw SwtErrorHandler.getInstance().handleException(ex,
					"removeRule", this.getClass());
		} finally {
			// log debug message
			log.debug(this.getClass().getName() + " - [removeRule] - Exit");
		}
		// return number row deleted
		return n;
	}
	
	/**
	 * Method to save Rule details
	 * 
	 * @param rule
	 * @param actionSave
	 * @return
	 * @throws SwtException
	 */
	public void saveRule(RulesDefinition rule, String actionSave, String moduleId) throws SwtException {

		try {
			// log debug message
			log.debug(this.getClass().getName() + " - [ saveRule ] - "
					+ "Entry");
			// save Rule
			expressionBuilderDAO.saveRule(rule, actionSave, moduleId);

		} catch (Exception ex) {
			// log error message
			log.error(this.getClass().getName()
					+ " - Exception Caught in [saveRule] method : - "
					+ ex.getMessage());
			// throw Exception
			throw SwtErrorHandler.getInstance().handleException(ex, "saveRule",
					this.getClass());
		} finally {
			// log debug message
			log.debug(this.getClass().getName() + " - [saveRule] - Exit");
		}
	}
	
	/**
	 * getRuleListReport()
	 * 
	 * @param selectedfilter
	 *            - filter parameter
	 * @param selectedsort
	 *            - Sort parameter
	 * @param typeCol
	 *            - columns type
	 * @param sQuery
	 * @param langId
	 * @param moduleId
	 * @return Collection -get rule list
	 * @throws SwtException
	 * 
	 *             This method to get the rule list for given filter and sort
	 *             parameters
	 */
	public Collection<RulesDefinition> getRuleListReport(String selectedfilter,
			String selectedsort, HashMap<String, String> typeCol, String sQuery, String langId, String moduleId,String isRiskFactor)
			throws SwtException {

		// Collection to hold list Rule
		Collection<RulesDefinition> collection = null;

		try {
			// log debug message
			log.debug(this.getClass().getName()
					+ " - [ getRuleListReport ] - Entry");
			// get the value from RulesDefinitionDAOHibernate
			collection = expressionBuilderDAO.getRuleListReport(selectedfilter,
					selectedsort, typeCol, sQuery, langId, moduleId,isRiskFactor);
			// return result as Collection
			return collection;

		} catch (SwtException e) {
			// log error message
			log.error(this.getClass().getName()
					+ " - Exception Caught in [getRuleListReport] method : - "
					+ e.getMessage());
			// throw Exception
			throw SwtErrorHandler.getInstance().handleException(e,
					"getRuleListReport", this.getClass());
		} catch (Exception e) {
			// log error message
			log.error(this.getClass().getName()
					+ " - Exception Caught in [getRuleListReport] method : - "
					+ e.getMessage());
			// throw Exception
			throw SwtErrorHandler.getInstance().handleException(e,
					"getRuleListReport", this.getClass());
		} finally {
			// Nullify Objects
			collection = null;
			// log debug message
			log.debug(this.getClass().getName()
					+ "- [getRuleListReport] - Exiting ");
		}

	}
	
	/**
	 * getRuleById()
	 * 
	 * @param ruleId
	 *            - ruleId
	 * @param langId
	 * @return Collection -get rule
	 * @throws SwtException
	 * 
	 *             This method to get the rule for given rule id
	 */
	public RulesDefinition getRuleById(Integer ruleId, String langId) throws SwtException {

		// Instance object Rule
		RulesDefinition rule = null;

		try {
			// log debug message
			log.debug(this.getClass().getName() + " - [ getRuleById ] - Entry");
			// get the value from RulesDefinitionDAOHibernate
			rule = expressionBuilderDAO.getRuleById(ruleId, langId);

		} catch (SwtException exp) {
			// log error message
			log.error(this.getClass().getName()
					+ " - Exception Caught in [getRuleById] method : - "
					+ exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp,
					"getRuleById", this.getClass());
		} catch (Exception exp) {
			// log error message
			log.error(this.getClass().getName()
					+ " - Exception Caught in [getRuleById] method : - "
					+ exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp,
					"getRuleById", this.getClass());
		} finally {
			// log debug message
			log.debug(this.getClass().getName() + "- [getRuleById] - Exiting ");
		}
		// return result
		return rule;
	}
	
	/**
	 * This method is used to search list of rules objects from A_RULE table
	 * 
	 * @param sQuery
	 * @param langId
	 * @param moduleId
	 * @param isRiskFactor
	 * @return List<RulesDefinition> - list of Rule objects
	 * @throws SwtException
	 */
	public List<RulesDefinition> getSearchData(String sQuery, String langId, String moduleId,String isRiskFactor) throws SwtException {

		// variables List to hold list Rule
		List<RulesDefinition> listRules = null;

		try {
			// log debug message
			log.debug(this.getClass().getName() + " - [getSearchData] - Entry");
			// Get list Rule
			listRules = expressionBuilderDAO.getSearchData(sQuery, langId, moduleId,isRiskFactor);
			// return result as list
			return listRules;

		} catch (SwtException ex) {
			// log error message
			log.error(this.getClass().getName()
					+ " - Exception Caught in [getSearchData] method : - "
					+ ex.getMessage());
			// throw Exception
			throw SwtErrorHandler.getInstance().handleException(ex,
					"getSearchData", this.getClass());
		} catch (Exception ex) {
			// log error message
			log.error(this.getClass().getName()
					+ " - Exception Caught in [getSearchData] method : - "
					+ ex.getMessage());
			// throw Exception
			throw SwtErrorHandler.getInstance().handleException(ex,
					"getSearchData", this.getClass());
		} finally {
			// Nullify objects
			listRules = null;
			// log debug message
			log.debug(this.getClass().getName() + " - [getSearchData] - Exit");
		}
	}
	
	
	/**
	 * This method is used to get the list of label and code from S_CFG_TYPE_VALUES, S_DICTIONARY
	 * 
	 * @param typeId
	 * @param languageId
	 * @param showHidden
	 * @return LinkedHashMap<String, String>
	 * @throws SwtException
	 */
	public LinkedHashMap<String, String> getTypeValuesList(int typeId,String languageId, Boolean showHidden) throws SwtException{
		// variables List to hold LinkedHashMap
		LinkedHashMap<String, String> hashTypeValue = null;

		try {
			// log debug message
			log.debug(this.getClass().getName() + " - [getTypeValuesList] - Entry");
			// Get LinkedLinkedHashMap of typeValues
			hashTypeValue = expressionBuilderDAO.getTypeValuesList(typeId, languageId, showHidden);
			// return result as HashMap
			return hashTypeValue;

		} catch (SwtException ex) {
			// log error message
			log.error(this.getClass().getName()
					+ " - Exception Caught in [getTypeValuesList] method : - "
					+ ex.getMessage());
			// throw Exception
			throw SwtErrorHandler.getInstance().handleException(ex,
					"getTypeValuesList", this.getClass());
		} catch (Exception ex) {
			// log error message
			log.error(this.getClass().getName()
					+ " - Exception Caught in [getTypeValuesList] method : - "
					+ ex.getMessage());
			// throw Exception
			throw SwtErrorHandler.getInstance().handleException(ex,
					"getTypeValuesList", this.getClass());
		} finally {
			// Nullify objects
			hashTypeValue = null;
			// log debug message
			log.debug(this.getClass().getName() + " - [getTypeValuesList] - Exit");
		}
	}
	
	
	/**
	 * Method to save Rule conditions details
	 * 
	 * @param ruleCondition
	 * @param ruleId
	 * @param actionSave
	 * @param updateCondition
	 * @return
	 * @throws SwtException
	 */
	public void saveRulesConditions(ArrayList<RuleConditions> ruleCondition, int ruleId, String actionSave, boolean updateCondition) throws SwtException {

		try {
			// log debug message
			log.debug(this.getClass().getName() + " - [ saveRulesConditions ] - "
					+ "Entry");
			// save Rule
			expressionBuilderDAO.saveRulesConditions(ruleCondition, ruleId, actionSave, updateCondition);

		} catch (Exception ex) {
			// log error message
			log.error(this.getClass().getName()
					+ " - Exception Caught in [saveRulesConditions] method : - "
					+ ex.getMessage());
			// throw Exception
			throw SwtErrorHandler.getInstance().handleException(ex, "saveRulesConditions",
					this.getClass());
		} finally {
			// log debug message
			log.debug(this.getClass().getName() + " - [saveRulesConditions] - Exit");
		}
	}
	
	
	/**
	 * This method is used to get search criteria list from A_CFG_VALUES
	 * 
	 * @param typeId
	 * @param languageId
	 * @param showHidden
	 * @return List<TypeValues>
	 * @throws swtException
	 */

	public List<TypeValues>  getSearchListCriteria(int typeId,String languageId, Boolean showHidden) throws SwtException {

		try {
			
			log.debug(this.getClass().getName()+ "- [getSearchListCriteria] - Entering ");
			return expressionBuilderDAO.getSearchListCriteria(typeId,languageId, showHidden);
			
		} catch (SwtException exp) {

			log.error(this.getClass().getName()
					+ " - Exception Catched in [getSearchListCriteria] method : - "
					+ exp.getMessage());

			throw exp;
		} catch (Exception exp) {

			log.error(this.getClass().getName()
					+ " - Exception Catched in [getSearchListCriteria] method : - "
					+ exp.getMessage());

			throw SwtErrorHandler.getInstance().handleException(exp,
					"getSearchListCriteria", this.getClass());
		} finally {
			log.debug(this.getClass().getName()
					+ "- [getSearchListCriteria] - Exiting ");
		}

	}
	
	
	/**
	 * getDictionaryText() Method to get Dictionary Text
	 * 
	 * @param languageId
	 * @param textId
	 * @return Dictionary - Returns Dictionary object
	 * @throws SwtException
	 */
	public Dictionary getDictionaryText(String languageId, String textId) throws SwtException {
		/* Method's local variable declaration */
		Dictionary dict = null;
		try {
			// log debug message
			log.debug(this.getClass().getName()
					+ "- [getDictionaryText] - Enter");
			// get the dictionary
			dict = expressionBuilderDAO.getDictionaryText(languageId, textId);
			return dict;
		} catch (SwtException ex) {
			// log error message
			log.error(this.getClass().getName()
					+ " - Exception Catched in [getDictionaryText] method : - "
					+ ex.getMessage());
			throw ex;
		} catch (Exception exp) {
			// log error message
			log.error(this.getClass().getName()
					+ " - Exception Catched in [getDictionaryText] method : - "
					+ exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp,
					"getDictionaryText", this.getClass());
		} finally {
			// log debug message
			log.debug(this.getClass().getName()
					+ "- [getDictionaryText] - Exit");
		}
	}
	
	
	/**
	 * executeSelectQuery() Method to execute sQuery parameter as query
	 * 
	 * @param squery
	 * @return List<Object[]>
	 * @throws SwtException
	 */
	public List<Object[]> executeSelectQuery(String squery) throws SwtException{
		/* Method's local variable declaration */
		List<Object[]> list = null;
		try {
			// log debug message
			log.debug(this.getClass().getName()
					+ "- [executeSelectQuery] - Enter");
			// get the list of query result
			list = expressionBuilderDAO.executeSelectQuery(squery);
			return list;
		} catch (SwtException ex) {
			// log error message
			log.error(this.getClass().getName()
					+ " - Exception Catched in [executeSelectQuery] method : - "
					+ ex.getMessage());
			throw ex;
		} catch (Exception exp) {
			// log error message
			log.error(this.getClass().getName()
					+ " - Exception Catched in [executeSelectQuery] method : - "
					+ exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp,
					"executeSelectQuery", this.getClass());
		} finally {
			// log debug message
			log.debug(this.getClass().getName()
					+ "- [executeSelectQuery] - Exit");
		}
	}

	
//	/**
//	 * This method is used to get get criteria from A_PROFILE
//	 * @param languageId
//	 * @param ruleType
//	 * @param moduleId
//	 * @return List<FieldsProfile>
//	 * @throws SwtException
//	 */
//	public List<FieldsProfile> getListCriteriaToAdd(String languageId, String ruleType, String moduleId) throws SwtException {
//
//		try {
//			
//			log.debug(this.getClass().getName()+ "- [getListCriteriaToAdd] - Entering ");
//			return expressionBuilderDAO.getListCriteriaToAdd(languageId, ruleType, moduleId);
//			
//		} catch (SwtException exp) {
//
//			log.error(this.getClass().getName()
//					+ " - Exception Catched in [getListCriteriaToAdd] method : - "
//					+ exp.getMessage());
//
//			throw exp;
//		} catch (Exception exp) {
//
//			log.error(this.getClass().getName()
//					+ " - Exception Catched in [getListCriteriaToAdd] method : - "
//					+ exp.getMessage());
//
//			throw SwtErrorHandler.getInstance().handleException(exp,
//					"getListCriteriaToAdd", this.getClass());
//		} finally {
//			log.debug(this.getClass().getName()
//					+ "- [getListCriteriaToAdd] - Exiting ");
//		}
//
//	}
	
	
	/**
	 * validateQueryFromDataBase() Method to validate sQuery
	 * 
	 * @param squery
	 * @return boolean
	 * @throws SwtException
	 */
	public boolean validateQueryFromDataBase(String squery) throws SwtException {
		try {
			// log debug message
			log.debug(this.getClass().getName()
					+ "- [validateQueryFromDataBase] - Enter");
			// validate query
			return expressionBuilderDAO.validateQueryFromDataBase(squery);
		} catch (SwtException ex) {
			// log error message
			log.error(this.getClass().getName()
					+ " - Exception Catched in [validateQueryFromDataBase] method : - "
					+ ex.getMessage());
			throw ex;
		} catch (Exception exp) {
			// log error message
			log.error(this.getClass().getName()
					+ " - Exception Catched in [validateQueryFromDataBase] method : - "
					+ exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp,
					"validateQueryFromDataBase", this.getClass());
		} finally {
			// log debug message
			log.debug(this.getClass().getName()
					+ "- [validateQueryFromDataBase] - Exit");
		}
	}
	
	
	/**
	 * This method is used to get the list of rules name and descriptions from
	 * A_RULE, S_DICTIONARY
	 * 
	 * @param ruleId
	 * @param languageId
	 * @param moduleId
	 * @return LinkedHashMap<String, String>
	 * @throws SwtException
	 */
	public LinkedHashMap<String, String> getRulesFromDictionary(String ruleId, String languageId, String moduleId) throws SwtException {
		try {
			// log debug message
			log.debug(this.getClass().getName()	+ "- [getRulesFromDictionary] - Enter");
			// validate query
			return expressionBuilderDAO.getRulesFromDictionary(ruleId, languageId, moduleId);
		} catch (SwtException ex) {
			// log error message
			log.error(this.getClass().getName()
					+ " - Exception Catched in [getRulesFromDictionary] method : - "
					+ ex.getMessage());
			throw ex;
		} catch (Exception exp) {
			// log error message
			log.error(this.getClass().getName()
					+ " - Exception Catched in [getRulesFromDictionary] method : - "
					+ exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp,
					"getRulesFromDictionary", this.getClass());
		} finally {
			// log debug message
			log.debug(this.getClass().getName()
					+ "- [getRulesFromDictionary] - Exit");
		}
	}
	
	/**
	 * This method is used to get the list of profile name from A_PROFILE , S_DICTIONARY
	 * 
	 * @param profileId
	 * @param languageId
	 * @param ruleType
	 * @param moduleId
	 * @return LinkedHashMap<String, String>
	 * @throws SwtException
	 */
	public LinkedHashMap<String, String> getProfileFromDictionary(String profileId, String languageId, String ruleType, String moduleId) throws SwtException {
		
		try {
			// log debug message
			log.debug(this.getClass().getName()	+ "- [getProfileFromDictionary] - Enter");
			// validate query
			return expressionBuilderDAO.getProfileFromDictionary(profileId, languageId, ruleType, moduleId);
		} catch (SwtException ex) {
			// log error message
			log.error(this.getClass().getName()
					+ " - Exception Catched in [getProfileFromDictionary] method : - "
					+ ex.getMessage());
			throw ex;
		} catch (Exception exp) {
			// log error message
			log.error(this.getClass().getName()
					+ " - Exception Catched in [getProfileFromDictionary] method : - "
					+ exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp,
					"getProfileFromDictionary", this.getClass());
		} finally {
			// log debug message
			log.debug(this.getClass().getName()
					+ "- [getProfileFromDictionary] - Exit");
		}
		
	}
	
	
	
	/**
	 * This method is used to simulate a rule
	 * 
	 * @param ruleId
	 * @return int
	 * @throws SwtException
	 */
	public int simulateRule(Integer ruleId) throws SwtException
	{
		try {
			// log debug message
			log.debug(this.getClass().getName()	+ "- [simulateRule] - Enter");
			// validate query
			return expressionBuilderDAO.simulateRule(ruleId);
		} catch (SwtException ex) {
			// log error message
			log.error(this.getClass().getName()
					+ " - Exception Catched in [simulateRule] method : - "
					+ ex.getMessage());
			throw ex;
		} catch (Exception exp) {
			// log error message
			log.error(this.getClass().getName()
					+ " - Exception Catched in [simulateRule] method : - "
					+ exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp,
					"simulateRule", this.getClass());
		} finally {
			// log debug message
			log.debug(this.getClass().getName()
					+ "- [simulateRule] - Exit");
		}
	}
	
	
	/**
	 * This method is used to save simulated rule
	 * 
	 * @param ruleDef
	 * @return 
	 * @throws SwtException
	 */
	public void saveSimulatedRule(RulesDefinition ruleDef) throws SwtException 
	{
		try {
			// log debug message
			log.debug(this.getClass().getName()	+ "- [saveSimulatedRule] - Enter");
			// validate query
			expressionBuilderDAO.saveSimulatedRule(ruleDef);
			
		} catch (SwtException ex) {
			// log error message
			log.error(this.getClass().getName()
					+ " - Exception Catched in [saveSimulatedRule] method : - "
					+ ex.getMessage());
			throw ex;
		} catch (Exception exp) {
			// log error message
			log.error(this.getClass().getName()
					+ " - Exception Catched in [saveSimulatedRule] method : - "
					+ exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp,
					"saveSimulatedRule", this.getClass());
		} finally {
			// log debug message
			log.debug(this.getClass().getName()
					+ "- [saveSimulatedRule] - Exit");
		}
	}
	
	
	/**
	 * deleteSimilatedRule()
	 * 
	 * @param ruleId
	 * Method used to delete the simulate rule
	 * 
	 * @return
	 */
	public int deleteSimilatedRule(int ruleId) throws SwtException{
		try {
			// log debug message
			log.debug(this.getClass().getName()	+ "- [deleteSimilatedRule] - Enter");
			// validate query
			return expressionBuilderDAO.deleteSimilatedRule(ruleId);
		} catch (SwtException ex) {
			// log error message
			log.error(this.getClass().getName()
					+ " - Exception Catched in [deleteSimilatedRule] method : - "
					+ ex.getMessage());
			throw ex;
		} catch (Exception exp) {
			// log error message
			log.error(this.getClass().getName()
					+ " - Exception Catched in [deleteSimilatedRule] method : - "
					+ exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp,
					"deleteSimilatedRule", this.getClass());
		} finally {
			// log debug message
			log.debug(this.getClass().getName()
					+ "- [deleteSimilatedRule] - Exit");
		}
	}
	
	/**
	 * Method used to copy conditions of simulated rule from the original rule if ruleConditions is empty 
	 * 
	 * @param ruleId
	 * @param simulatedRuleId
	 * @return
	 * @throws SwtException
	 */
	public void copyRulesCondFromOriginalRule(int ruleId, int simulatedRuleId) throws SwtException {
		try {
			// log debug message
			log.debug(this.getClass().getName()	+ "- [copyRulesCondFromOriginalRule] - Enter");
			// copy conditions
			expressionBuilderDAO.copyRulesCondFromOriginalRule(ruleId, simulatedRuleId);
			
		} catch (SwtException ex) {
			// log error message
			log.error(this.getClass().getName()
					+ " - Exception Catched in [copyRulesCondFromOriginalRule] method : - "
					+ ex.getMessage());
			throw ex;
		} catch (Exception exp) {
			// log error message
			log.error(this.getClass().getName()
					+ " - Exception Catched in [copyRulesCondFromOriginalRule] method : - "
					+ exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp,
					"copyRulesCondFromOriginalRule", this.getClass());
		} finally {
			// log debug message
			log.debug(this.getClass().getName()
					+ "- [copyRulesCondFromOriginalRule] - Exit");
		}
	}
	
	/**
	 * This method is used to get the list of label and code from A_RULE_CONDITION, S_CFG_TYPE_VALUES, S_DICTIONARY
	 * 
	 * @param typeId
	 * @param ruleId
	 * @param languageId
	 * @param showHidden
	 * @param moduleId
	 * @return List<RuleConditions>
	 * @throws SwtException
	 */
	public List<RuleConditions> getCriteriaToBeChanged(int typeId, String ruleId, String languageId, Boolean showHidden, String moduleId) throws SwtException{
		// ArrayList Variable to hold code
		List<RuleConditions> listCriteria = new ArrayList<RuleConditions>();
		try {
			// log debug message
			log.debug(this.getClass().getName()	+ "- [getCriteriaToBeChanged] - Enter");
			// copy conditions
			listCriteria = expressionBuilderDAO.getCriteriaToBeChanged(typeId, ruleId, languageId, showHidden, moduleId);
			
		} catch (SwtException ex) {
			// log error message
			log.error(this.getClass().getName()
					+ " - Exception Catched in [getCriteriaToBeChanged] method : - "
					+ ex.getMessage());
			throw ex;
		} catch (Exception exp) {
			// log error message
			log.error(this.getClass().getName()
					+ " - Exception Catched in [getCriteriaToBeChanged] method : - "
					+ exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp,
					"getCriteriaToBeChanged", this.getClass());
		} finally {
			// log debug message
			log.debug(this.getClass().getName()
					+ "- [getCriteriaToBeChanged] - Exit");
		}
		return listCriteria;
	}


        /**
//	 * Method to save Rule Required Documents
//	 * 
//	 * @param ruleRequiredDocs
//	 * @param actionSave
//	 * @param ruleName
//	 * @param String ruleId
//	 * @return
//	 * @throws SwtException
//	 */
//	public void saveRuleRequiredDocs(List<FatcaRequiredDocument> ruleRequiredDocs, String ruleName, String ruleId, String actionSave) throws SwtException{
//
//		try {
//			// log debug message
//			log.debug(this.getClass().getName() + " - [ saveRulesConditions ] - "
//					+ "Entry");
//			// save Rule
//			expressionBuilderDAO.saveRuleRequiredDocs(ruleRequiredDocs, ruleName, ruleId, actionSave);
//
//		} catch (Exception ex) {
//			// log error message
//			log.error(this.getClass().getName()
//					+ " - Exception Caught in [saveRuleRequiredDocs] method : - "
//					+ ex.getMessage());
//			// throw Exception
//			throw SwtErrorHandler.getInstance().handleException(ex, "saveRuleRequiredDocs",
//					this.getClass());
//		} finally {
//			// log debug message
//			log.debug(this.getClass().getName() + " - [saveRuleRequiredDocs] - Exit");
//		}
//	}
	
	/**
	 * This method is used to get all the rulesDefinition objects from A_RULES table
	 * 
	 * @param moduleId
	 * @return List<RulesDefinition> - list of rules objects
	 * @throws SwtException
	 */
	public List<RulesDefinition> getListSecondaryRules(String moduleId) throws SwtException {

		// List to hold secondary rules
		List<RulesDefinition> lstSecondaryRules = null;

		try {
			// log debug message
			log.debug(this.getClass().getName() + " - [getListSecondaryRules] - " + "Entry");
			// Get list rules
			lstSecondaryRules = expressionBuilderDAO.getListSecondaryRules(moduleId);

		} catch (SwtException ex) {
			// log error message
			log.error(this.getClass().getName() + " - Exception Caught in [getListSecondaryRules] method : - "
					+ ex.getMessage());
			throw ex;
		} catch (Exception e) {
			// log error message
			log.error(this.getClass().getName() + " - Exception Caught in [getListSecondaryRules] method : - "
					+ e.getMessage());
			throw SwtErrorHandler.getInstance().handleException(e, "getListSecondaryRules", this.getClass());
		} finally {
			// log debug message
			log.debug(this.getClass().getName() + "- [getListSecondaryRules] - Exit");
		}
		// return list rules
		return lstSecondaryRules;
	}

	
	
	
	
}
