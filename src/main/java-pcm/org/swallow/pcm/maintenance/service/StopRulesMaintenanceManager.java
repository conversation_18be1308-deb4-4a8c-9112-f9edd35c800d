/*
 * @(#)PriorityRulesMaintenanceManager.java 1.0 06/03/2019
 *
 * Copyright (c) 2006-2019 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.pcm.maintenance.service;

import java.util.Collection;

import org.swallow.exception.SwtException;
import org.swallow.pcm.maintenance.dao.StopRulesMaintenanceDAO;
import org.swallow.pcm.maintenance.model.StopRule;

public interface StopRulesMaintenanceManager {

	public void setStopRulesMaintenanceDAO(StopRulesMaintenanceDAO stopRulesMaintenanceDAO);

	public Collection getStopRulesDetailList(String selectedStatus) throws SwtException;
	
	public StopRule getStopRuleDetails(String stopRuleId) throws SwtException;
	
	public void saveStopRule(StopRule stopRule, boolean isSaveAction) throws SwtException;
	
	public void deleteStopRule(String stopRuleId, Long maintEventId) throws SwtException;
	
	public boolean checkProcessStopRule(String stopRuleId) throws SwtException;
}
