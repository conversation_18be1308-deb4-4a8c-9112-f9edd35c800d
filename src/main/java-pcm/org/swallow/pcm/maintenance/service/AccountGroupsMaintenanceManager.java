/*
 * @(#)AccountGroupsMaintenanceManager.java 1.0 06/03/2019
 *
 * Copyright (c) 2006-2019 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.pcm.maintenance.service;

import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;

import org.swallow.exception.SwtException;
import org.swallow.pcm.maintenance.dao.AccountGroupsMaintenanceDAO;
import org.swallow.pcm.maintenance.model.AccountGroup;
import org.swallow.pcm.maintenance.model.AccountGroupCutoff;
import org.swallow.pcm.maintenance.model.AccountInGroup;
import org.swallow.pcm.maintenance.model.Reserve;

public interface AccountGroupsMaintenanceManager {

	public void setAccountGroupsMaintenanceDAO(AccountGroupsMaintenanceDAO accountGroupsMaintenanceDAO);

	/**
	 *  Used to save an account group detail  in DB
	 * @param acctGroup
	 * @throws SwtException
	 */
	public void saveAccountGroup(AccountGroup acctGroup)throws SwtException;
	
	
	
	/**
	 * Used to get AccountGroupDetails from DB
	 * 
	 * @param selectedSpreadProfileId
	 * @throws SwtException
	 */
	public List<AccountGroup> getAccountGroupDetailList(String selectedSpreadProfileId) throws SwtException;
	
	/**
	 * Used to get the account group details from database by id
	 * 
	 * @param acctGroupId
	 * @return AccountGroup
	 * @throws SwtException
	 */
	public AccountGroup getAcctGroupDetail(String acctGroupId)throws SwtException ;
	
	/**
	 * This method is used to update the account group details to database by id
	 * 
	 * @param acctGroup
	 * @return none
	 * @throws SwtException
	 */	
	public void updateAcctGroup(AccountGroup acctGroup)throws SwtException;


	/**
	 * Delete an account group
	 * @param acctGroup
	 * @throws SwtException
	 */
	public void deleteAcctGroup(AccountGroup acctGroup)throws SwtException;

	/**
	 * This method is used to get the total of accounts in account group 
	 * 
	 * @param groupId
	 * @return none
	 * @throws SwtException
	 */
	public Integer accountListCount(String groupId) throws SwtException;
	
	/**
	 * Used to get the list of accounts with details
	 * @return Collection
	 * @throws SwtException
	 */
	public Collection getAccountListDetails(String currencyCode) throws SwtException;
	
	/**
	 * This method is used to get Reserve details by acctgroupId 
	 * 
	 * @param acctGroupId
	 * @return Collection
	 * @throws SwtException
	 */
	public Collection getReserveDetails(String acctGroupId) throws SwtException;
	
	/**
	 * This method is used to get cut off rules details by acctgroupId 
	 * 
	 * @param acctGroupId
	 * @return Collection
	 * @throws SwtException
	 */
	public Collection getCutOffRulesDetails(String acctGroupId) throws SwtException;
	
	/**
	 * This method is used to get the accounts in group by acctgroupId 
	 * 
	 * @param acctGroupId
	 * @return Collection
	 * @throws SwtException
	 */
	 public Collection getAccountsInGroupList(String  accountGrpId) throws SwtException;
	 
	
	  
		 /**
		 * This method is used to do crud operations in accountsInGroup table
		 * 
		 * @param accountsInsert accountsDelete
		 * @return void
		 * @throws SwtException
		 */
	 
	 public void crudAccountsInGroup(ArrayList<AccountInGroup> accountsInsert, ArrayList<AccountInGroup> accountsDelete, Long maintEventId) throws SwtException;
		/**
		 * This method is used to do crud operations in reserve table
		 * 
		 * @param reserveInsert reserveUpdate reserveDelete
		 * @return void
		 * @throws SwtException
		 */
	 public void crudReserve(ArrayList<Reserve> reserveInsert, ArrayList<Reserve> reserveUpdate, ArrayList<Reserve> reserveDelete, Long maintEventId) throws SwtException;
	 
	 /**
	  * This method is used to do crud operations in cutoff and p_rule tables
	  * 
	  * @param cutOffInsert cutOffUpdate cutOffDelete
	  * @return Collection
	  * @throws SwtException
	  */
	 public void crudCutOff(ArrayList<AccountGroupCutoff> cutOffInsert, ArrayList<AccountGroupCutoff> cutOffUpdate, ArrayList<AccountGroupCutoff> cutOffDelete, Long maintEventId)throws SwtException;
	 
	 /**
	  * This method is used to get list og ordinal by ccy 
	  * 
	  * @param cutOffInsert currencyCode
	  * @return Collection
	  * @throws SwtException
	  */
	 public Collection getOrdinalByCurrency(String currencyCode) throws SwtException;
	 /**
	  * This method is used to get account group from accountId 
	  * 
	  * @param accountId accountId
	  * @return Collection
	  * @throws SwtException
	  */
	 public AccountGroup getAccountGroupFromAccountId(String hostId, String entityId , String accountId) throws SwtException;
	 /**
		 * Check if account id is related to pay
		 * 
		 * @param accountId
		 * @return String
		 * @throws SwtException
		 */
	 public  HashMap checkAccountInPayment(ArrayList accountId, ArrayList entity) throws SwtException;

	 /**
	  * For Maintenance Event
	  * @param currencyCode
	  * @return
	  * @throws SwtException
	  */
	public Collection getAccountListDetailsMaintEvent(String currencyCode, String accountGroup) throws SwtException;
	
	 /**
	  * For Maintenance Event
	  * @param accountGroup
	  * @return
	  * @throws SwtException
	  */
	public void deleteRulesDefinitionForMaintEvent(String accountGroup) throws SwtException;
		
}
