/*
 * @(#)PriorityRulesMaintenanceManager.java 1.0 06/03/2019
 *
 * Copyright (c) 2006-2019 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.pcm.maintenance.service;

import java.util.Collection;
import java.util.List;

import javax.servlet.http.HttpServletRequest;

import org.swallow.exception.SwtException;
import org.swallow.pcm.maintenance.dao.SpreadProfilesMaintenanceDAO;
import org.swallow.pcm.maintenance.model.SpreadProcessPoint;
import org.swallow.pcm.maintenance.model.SpreadProfile;

public interface SpreadProfilesMaintenanceManager {

	public void setSpreadProfilesMaintenanceDAO(SpreadProfilesMaintenanceDAO spreadProfilesMaintenanceDAO);

	/**
	 * Return the spread profile details with id spreadProfileId
	 * @param spreadProfileId
	 * @return SpreadProfile
	 * @throws SwtException
	 */
	public SpreadProfile getSpreadProfileDetails(String spreadProfileId) throws SwtException;
	
	/**
	 * Returns the collection of spread profiles list from Spread Profile table
	 * 
	 * @param curencyCode
	 * @return Collection
	 * @throws SwtException
	 */
	public List<SpreadProfile> getSpreadProfilesList(String curencyCode)throws SwtException;

	
	/**
	 * Returns the collection of spread profiles list for CategoryId
	 * 
	 * @param categoryId
	 * @return Collection
	 * @throws SwtException
	 */
	public Collection getSpreadProfilesByCategoryId(String categoryId)throws SwtException;
	
	/**
	 *  Returns the collection of spread process point
	 *  
	 * @param selectedSpreadProfileId
	 * @param request
	 * @return
	 * @throws SwtException
	 */
	public List<SpreadProcessPoint> getSpreadProcessPointList(HttpServletRequest request, String selectedSpreadProfileId) throws SwtException;
	/**
	 * Delete spread profile 
	 * 
	 * @param selectedSpreadProfileId
	 * @throws SwtException
	 */
	public void deleteSpreadProfile(String selectedSpreadProfileId, Long maintEventId) throws SwtException;

	/**
	 * Save Spread Profile
	 * 
	 * @param spreadProfile
	 * @throws SwtException
	 */
	public void saveSpreadProfile(SpreadProfile spreadProfile) throws SwtException;

	/**
	 * Update Spread Profile
	 * 
	 * @param spreadProfile
	 * @throws SwtException
	 */
	public void updateSpreadProfile(SpreadProfile spreadProfile) throws SwtException;
	
	/**
	 * Add, update and delete list of spread process points 
	 * 
	 * @param listSpreadProcessPointAdd
	 * @param listSpreadProcessPointUpdate
	 * @param listSpreadProcessPointdelete
	 * @param idGenerated 
	 * @throws SwtException
	 */
	public void crudSpreadProcessPoints(List<SpreadProcessPoint> listSpreadProcessPointAdd,
			List<SpreadProcessPoint> listSpreadProcessPointUpdate,
			List<SpreadProcessPoint> listSpreadProcessPointdelete, Long maintEventId) throws SwtException;
}
	
