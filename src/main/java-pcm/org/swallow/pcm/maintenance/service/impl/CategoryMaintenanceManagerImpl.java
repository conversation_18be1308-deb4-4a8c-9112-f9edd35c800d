/*
 * @(#)CategoryMaintenanceManagerImpl.java 1.0 06/03/2019
 *
 * Copyright (c) 2006-2019 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */

package org.swallow.pcm.maintenance.service.impl;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.pcm.maintenance.dao.CategoryMaintenanceDAO;
import org.swallow.pcm.maintenance.model.Category;
import org.swallow.pcm.maintenance.model.CategoryRule;
import org.swallow.pcm.maintenance.service.CategoryMaintenanceManager;

@Component ("categoryMaintenanceManager")
public class CategoryMaintenanceManagerImpl implements CategoryMaintenanceManager {
	// Final instance for log
	private final Log log = LogFactory.getLog(CategoryMaintenanceManagerImpl.class);
	@Autowired
	private CategoryMaintenanceDAO categoryMaintenanceDAO = null;

	public void setCategoryMaintenanceDAO(CategoryMaintenanceDAO categoryMaintenanceDAO) {
		this.categoryMaintenanceDAO = categoryMaintenanceDAO;
	}

	public Collection getCategoryDetailList() throws SwtException{
		// Variable List to hold list RulesDefinition
		List categoryList = null;
		try {
			// log debug message
			log.debug(this.getClass().getName() + " - [ getCategoryDetailList ] - " + "Entry");
			categoryList =  (List) categoryMaintenanceDAO.getCategoryDetailList();
		}catch(Exception ex){
			// log error message
			log.error(this.getClass().getName()
					+ " - Exception Caught in [getCategoryDetailList] method : - "
					+ ex.getMessage());
			throw SwtErrorHandler.getInstance().handleException(ex,
					"getCategoryDetailList", this.getClass());
		} finally {
			// log debug message
			log.debug(this.getClass().getName() + " - [getCategoryDetailList] - Exit");
		}
		// return result as list
		return  categoryList;
	}
	public Collection getCategoryCombo() throws SwtException{
		// Variable List to hold list RulesDefinition
		List categoryList = null;
		try {
			// log debug message
			log.debug(this.getClass().getName() + " - [ getCategoryCombo ] - " + "Entry");
			categoryList =  (List) categoryMaintenanceDAO.getCategoryCombo();
		}catch(Exception ex){
			// log error message
			log.error(this.getClass().getName()
					+ " - Exception Caught in [getCategoryCombo] method : - "
					+ ex.getMessage());
			throw SwtErrorHandler.getInstance().handleException(ex,
					"getCategoryCombo", this.getClass());
		} finally {
			// log debug message
			log.debug(this.getClass().getName() + " - [getCategoryCombo] - Exit");
		}
		// return result as list
		return  categoryList;
	}

	public void deleteCategory(String categoryId)throws SwtException{
		try {
			categoryMaintenanceDAO.deleteCategory(categoryId);
		}catch(SwtException ex){
			// log error message
			log.error(this.getClass().getName()
					+ " - Exception Caught in [deleteCategory] method : - "
					+ ex.getMessage());
			throw SwtErrorHandler.getInstance().handleException(ex,
					"deleteCategory", this.getClass());
		}
		
	}



	public Category getCategoryDetailById(String categoryId) throws SwtException {
		
		return categoryMaintenanceDAO.getCategoryDetailById(categoryId);

	}
	

	/**
	 * This method is used to do crud operations in Category Rule table
	 * @param categoryRuleInsert categoryRuleUpdate
	 * @throws SwtException
	 */
	public void crudCategoryRule(Category category, ArrayList<CategoryRule> categoryRuleInsert, ArrayList<CategoryRule> categoryRuleUpdate, ArrayList<CategoryRule> categoryRuleDelete, String screenName) throws SwtException {
		try {
			categoryMaintenanceDAO.crudCategoryRule(category,categoryRuleInsert, categoryRuleUpdate,categoryRuleDelete,screenName );
		} catch (Exception e) {
			log.error(this.getClass().getName()
					+ "- [crudCategoryRule] - Exception " + e.getMessage());
			throw SwtErrorHandler.getInstance().handleException(e,
					"crudCategoryRule",
					this.getClass());
		}
	}

	@Override
	public Integer getMaxOrder() throws SwtException {
		Integer maxOrder = null;
		try {
			//maxOrder= categoryMaintenanceDAO.getMaxOrder();
			return categoryMaintenanceDAO.getMaxOrder();
		}catch(Exception exp) {
			log.debug(this.getClass().getName()
					+ " - Exception Catched in [ReOrder] method : - "
					+ exp.getMessage());
			log.error(this.getClass().getName()
					+ " - Exception Catched in [ReOrder] method : - "
					+ exp.getMessage());

			throw SwtErrorHandler.getInstance().handleException(exp,
					"ReOrder", CategoryMaintenanceManagerImpl.class);
		}
		
	}
	
	@Override
	public Boolean existPayRequest(String categoryId)throws SwtException {
		Boolean exist = false;
		try {
			exist = categoryMaintenanceDAO.existPayRequest(categoryId);
		}catch(Exception exp) {
			log.debug(this.getClass().getName()
					+ " - Exception Catched in [existPayRequest] method : - "
					+ exp.getMessage());
			log.error(this.getClass().getName()
					+ " - Exception Catched in [existPayRequest] method : - "
					+ exp.getMessage());

			throw SwtErrorHandler.getInstance().handleException(exp,
					"existPayRequest", CategoryMaintenanceManagerImpl.class);
		}
		return exist;
	}
	
	@Override
	public void reOrder(Integer value_from, Integer orderTo) throws SwtException {
		try {
			log.debug(this.getClass().getName()
					+ " - [ReOrder] - " + "Entry");
			categoryMaintenanceDAO.reOrder(value_from, orderTo);
		}catch(Exception exp) {
			log.debug(this.getClass().getName()
					+ " - Exception Catched in [ReOrder] method : - "
					+ exp.getMessage());
			log.error(this.getClass().getName()
					+ " - Exception Catched in [ReOrder] method : - "
					+ exp.getMessage());

			throw SwtErrorHandler.getInstance().handleException(exp,
					"ReOrder", CurrencyMaintenanceManagerImpl.class);
		}finally {
			log.debug(this.getClass().getName()
					+ " - [ReOrder] - " + "Exit");
		}
		
	}
}
