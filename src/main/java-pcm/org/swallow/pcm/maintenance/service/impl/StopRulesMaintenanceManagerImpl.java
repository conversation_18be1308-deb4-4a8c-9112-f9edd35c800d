/*
 * @(#)StopRulesMaintenanceManagerImpl.java 1.0 06/03/2019
 *
 * Copyright (c) 2006-2019 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */

package org.swallow.pcm.maintenance.service.impl;

import java.util.Collection;
import java.util.List;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.pcm.maintenance.dao.StopRulesMaintenanceDAO;
import org.swallow.pcm.maintenance.model.StopRule;
import org.swallow.pcm.maintenance.service.StopRulesMaintenanceManager;

@Component("stopRulesMaintenanceManager")
public class StopRulesMaintenanceManagerImpl implements StopRulesMaintenanceManager {
	// Final instance for log
	private final Log log = LogFactory.getLog(StopRulesMaintenanceManagerImpl.class);
	
	@Autowired
	private StopRulesMaintenanceDAO stopRulesMaintenanceDAO = null;

	public void setStopRulesMaintenanceDAO(StopRulesMaintenanceDAO stopRulesMaintenanceDAO) {
		this.stopRulesMaintenanceDAO = stopRulesMaintenanceDAO;
	}

	@SuppressWarnings("unchecked")
	public Collection getStopRulesDetailList(String selectedStatus) throws SwtException{
		// Variable List to hold list RulesDefinition
		List categoryList = null;
		try {
			// log debug message
			log.debug(this.getClass().getName() + " - [ getCategoryDetailList ] - " + "Entry");
			categoryList =  (List) stopRulesMaintenanceDAO.getStopRulesDetailList(selectedStatus);
		}catch(Exception ex){
			// log error message
			log.error(this.getClass().getName()
					+ " - Exception Caught in [getCategoryDetailList] method : - "
					+ ex.getMessage());
			throw SwtErrorHandler.getInstance().handleException(ex,
					"getCategoryDetailList", this.getClass());
		} finally {
			// log debug message
			log.debug(this.getClass().getName() + " - [getCategoryDetailList] - Exit");
		}
		// return result as list
		return  categoryList;
	}
	
	
	@SuppressWarnings("unchecked")
	public StopRule getStopRuleDetails(String stopRuleId) throws SwtException{
		// Variable List to hold list RulesDefinition
		StopRule stopRule = null;
		try {
			// log debug message
			log.debug(this.getClass().getName() + " - [ getCategoryDetailList ] - " + "Entry");
			stopRule = stopRulesMaintenanceDAO.getStopRuleDetails(stopRuleId);
		}catch(Exception ex){
			// log error message
			log.error(this.getClass().getName()
					+ " - Exception Caught in [getCategoryDetailList] method : - "
					+ ex.getMessage());
			throw SwtErrorHandler.getInstance().handleException(ex,
					"getCategoryDetailList", this.getClass());
		} finally {
			// log debug message
			log.debug(this.getClass().getName() + " - [getCategoryDetailList] - Exit");
		}
		// return result as list
		return  stopRule;
	}

	@Override
	public void saveStopRule(StopRule stopRule, boolean isSaveAction) throws SwtException {
		log.debug(this.getClass().getName() + " - [ saveStopRule ] - " + "Entry");
		try {
			stopRulesMaintenanceDAO.saveStopRule(stopRule, isSaveAction);	
		}catch(Exception ex){
			// log error message
			log.error(this.getClass().getName()
					+ " - Exception Caught in [saveStopRule] method : - "
					+ ex.getMessage());
			throw SwtErrorHandler.getInstance().handleException(ex,
					"saveStopRule", this.getClass());
		}
	}
	
	@Override
	public void deleteStopRule(String stopRuleId, Long maintEventId) throws SwtException {
		log.debug(this.getClass().getName() + " - [ deleteStopRule ] - " + "Entry");
		try {
			stopRulesMaintenanceDAO.deleteStopRule(stopRuleId, maintEventId);	
		}catch(Exception ex){
			// log error message
			log.error(this.getClass().getName()
					+ " - Exception Caught in [deleteStopRule] method : - "
					+ ex.getMessage());
			throw SwtErrorHandler.getInstance().handleException(ex,
					"deleteStopRule", this.getClass());
		}
	}
	
	@SuppressWarnings("unchecked")
	public boolean checkProcessStopRule(String stopRuleId) throws SwtException{
		// Variable List to hold result
		boolean result = false;
		try {
			// log debug message
			log.debug(this.getClass().getName() + " - [ checkProcessStopRule ] - " + "Entry");
			result = stopRulesMaintenanceDAO.checkProcessStopRule(stopRuleId);
		}catch(Exception ex){
			// log error message
			log.error(this.getClass().getName()
					+ " - Exception Caught in [checkProcessStopRule] method : - "
					+ ex.getMessage());
			throw SwtErrorHandler.getInstance().handleException(ex,
					"getCategoryDetailList", this.getClass());
		} finally {
			// log debug message
			log.debug(this.getClass().getName() + " - [checkProcessStopRule] - Exit");
		}
		// return result as list
		return  result;
	}

	
	
	
	
	
}
