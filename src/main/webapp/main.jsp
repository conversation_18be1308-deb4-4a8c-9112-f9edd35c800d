<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>





<%@ taglib prefix="s" uri="/struts-tags" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>


<%-- <fmt:setLocale value="${user_lang}" scope="session" /> --%>

<%-- <s:set var="userLang" value="${user_lang}" scope="session" /> --%>
<%-- <s:property value="userLang" /> --%>

<%@ include file="/taglib.jsp"%>
<%@ taglib uri="/WEB-INF/tags-swallow" prefix="swallow"%>
<%@ include file="/angularJSUtils.jsp"%>
<%@ page import="org.swallow.mfa.RegisterMFA"  %>
<html>

<s:if test='"N"==#request.changepassword' >	<!--For New User-->
    <script>
        sessionStorage.setItem('passwordChangeStatus', 'N');
    </script>
</s:if>
<%-- Store the password change status in session storage when JSP renders --%>
<s:if test='"Y"==#request.changepassword' >
    <script>
        sessionStorage.setItem('passwordChangeStatus', 'Y');
    </script>
</s:if>

<s:if test='"E"==#request.changepassword' >
    <script>
        sessionStorage.setItem('passwordChangeStatus', 'E');
        sessionStorage.setItem('changepassworddays', '${changepassworddays}');

    </script>
</s:if>


<s:if test='"M"==#request.changepassword' ><!--Password changed using menu option-->
<script>
		sessionStorage.setItem('passwordChangeStatus', 'M');
</script>
</s:if>

<%-- Add this script block at the end of your JSP --%>
<script>


    const passwordStatus = sessionStorage.getItem('passwordChangeStatus');
// Prevent back button

    // Check stored password change status
const days = sessionStorage.getItem('changepassworddays');
const changepasswordadlertsent = sessionStorage.getItem('changepasswordadlertsent');
console.log('passwordStatus==',passwordStatus);
if(changepasswordadlertsent != 'Y'){
    if (passwordStatus === 'Y') {
        alert('<s:text name="alert.passwordExpired" />');
        window.location.href = '<%=request.getContextPath()%>/changepassword.do?screen=logon';
    }
    else if (passwordStatus === 'E') {
        const days = sessionStorage.getItem('changepassworddays');
        if (window.confirm('<s:text name="alert.passwordExpiresInFewDaysPart1" />' + days + '<s:text name="alert.passwordExpiresInFewDaysPart2" />')) {
            window.location.href = '<%=request.getContextPath()%>/changepassword.do?screen=logon';
        } else {
			//clearPasswordChangeStatus();
            //window.location.href = '<%=request.getContextPath()%>/logon.do?method=login&logonflag=afterlogon';
        }
    }
    else if (passwordStatus === 'N') {
        alert('<s:text name="login.newUSer.continue"/>');
        window.location.href = '<%=request.getContextPath()%>/changepassword.do?screen=logon';
    } else if (passwordStatus === 'M') {
        alert('<s:text name="login.password.modified"/>');
        window.location.href = '<%=request.getContextPath()%>/changepassword.do?screen=logon&deletePwdHst=y';
    }
}
sessionStorage.setItem('changepasswordadlertsent', 'N');
// Clear the status when password is successfully changed
function clearPasswordChangeStatus() {
    sessionStorage.removeItem('passwordChangeStatus');
    sessionStorage.removeItem('changepassworddays');
	sessionStorage.removeItem('changepasswordadlertsent');
}
  </script>
<head>
	<title>Main &nbsp;-&nbsp;Smart Predict</title>
	<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">

	<script type="text/javascript" src="SwtComponents/toaster/toastr.min.js"></script>
	<link rel="stylesheet" type="text/css" href="SwtComponents/toaster/toastr.min.css" >

	<%@ page import="org.swallow.util.*"%>
</head>
<SCRIPT LANGUAGE="JAVASCRIPT">
	var logoffButtonLang = "${user_lang}";

	var dictionaryEn = ${dic_en};
	var dictionaryFr = ${dic_fr};


	function getMessage(label, lang){
		var message = "";
		if(!lang) {
			lang = logoffButtonLang;
			if(!lang)
				lang = "EN";
		}

		if(label){
			if(lang == "FR") {
				if(dictionaryFr)
					message = dictionaryFr[label];
			}else {
				if(dictionaryEn)
					message = dictionaryEn[label];
			}
			if(message)
				return message;
			else
				return label;

		}else{
			return "";
		}
	}

</script>
<script type="text/javascript" src="js/menu_src.js"></script>
<script language="JAVASCRIPT" src="js/mmenudom.js"></script>
<script type="text/javascript" src="js/preloadmenuimages.js"></script>
<script language="JAVASCRIPT" src="js/menu_data.js"></script>
<script type="text/javascript" src="js/keypress.js"></script>

<style>
	.skin0 {
		position:absolute;
		text-align:left;
		width:58px;
		border:1px solid black;
		background-color:menu;
		font-family:Verdana,Helvetica;
		line-height:15px;
		visibility:hidden;
		border-right-width:2px;
		border-bottom-width:2px;
		border-color="#8F9397"
	}
	.menuitems {
		padding-left:7px;
		padding-right:2px;
	}
</style>

<SCRIPT LANGUAGE="JAVASCRIPT">

	var screenRoute = "jsAngularBridge";
	var menuSkin = "skin0";
	var isMainScreen = true;
	var WF_MENUITEM_ID=104;
	var SCENARIO_SUMMARY_SCREEN="scenarioSummary";

	<%session=request.getSession();%>
	var sessionId='<%=session.getId()%>';
	var userId='<%=SwtUtil.getCurrentUser(session).getId().getUserId()%>';
	var mfaToken='<%=SwtUtil.getMFAToken(session)%>';
	var scenarioSummaryaccess = '<%=SwtUtil.getMenuAccessValue(SwtConstants.MENU_ITEM_ALERT_SUMMARY_DISPLAY,request)%>';
	var notificationTextMessage = '<%=SwtUtil.getNotificationMessage(session)%>';
	/* Added to apply style on mandatory field ,if it's equal to undefined then no field will be colored */
	mandatoryFieldsArray= "undefined" ;

	/**
	 * Encode a text in base 64 using a function in jsflexbridge.swf
	 **/
	function encode64(text){

		return instanceElement.encode64(text);
	}

	function encrypt1(clearText){
		return instanceElement.encrypt(sessionId, userId, clearText);
	}

	function encryptPass(userId, clearText){
		return instanceElement.encrypt(sessionId, userId, clearText);
	}

	/**
	 * Function to stop the recalculation process of the ILM monitor will be called
	 * when a recalculation process is running and the user closes the screen.
	 **/
	function stopRecalculateProcess(sequenceNumber){
		var oXMLHTTP = new XMLHttpRequest();
		var sURL = requestURL + "ilmAnalysisMonitor.do?method=stopRecalculationProcess";
		sURL = sURL + "&uniqueSequenceId="+sequenceNumber;
		oXMLHTTP.open( "POST", sURL, false );
		oXMLHTTP.send();
	}

	/**
	 * Decode a text in base 64 using a function in jsAngularbridge
	 **/
	function decode64(text){
		return instanceElement.decode64(text);
	}
	

	window.addEventListener("message", receiveMessage, false);
			// Cleanup when window closes
	window.addEventListener('beforeunload', () => {
		localStorage.setItem('shouldCloseAbout', 'true');
	});

	function receiveMessage(e) {
		if(e && e.data){
			/*var methodName = e.data.data;
			if(methodName == "removeLock"){
				if(e.data.selectedMovements){
					
					// Check if the string is not null and not empty
					if (e.data.selectedMovements && e.data.selectedMovements.trim() !== "") {
					  // Remove trailing comma and then split the string into an array
					  // Loop through the array and call unlockMovementOnServer for each value
					    unlockMovementOnServer(e.data.selectedMovements);
					} else {
					  // Handle the case where the string is null or empty
					  console.log("String is null or empty. No action taken.");
					}
					
				}
				
				
			}*/
		}
		
	}

	function unlockMovementOnServer(movementIds)
	{
		var oXMLHTTP = new XMLHttpRequest();
		var appName = "<%=SwtUtil.appName%>";
		var sURL = requestURL + "movementLock.do?method=unlockMovements";
		sURL = sURL + "&movementIds="+movementIds;

		oXMLHTTP.open( "POST", sURL, false );
		oXMLHTTP.send();
		var str=new String(oXMLHTTP.responseText);
		return str;
	}



	/**
	 * Check the focus state on main page
	 **/


	/**
	 * show menu function
	 */
	function showMenu() {
		var rightedge = document.body.clientWidth-event.clientX;
		var bottomedge = document.body.clientHeight-event.clientY;
		if (rightedge < ie5menu.offsetWidth)
			ie5menu.style.left = document.body.scrollLeft + event.clientX - ie5menu.offsetWidth;
		else
			ie5menu.style.left = document.body.scrollLeft + event.clientX;
		if (bottomedge < ie5menu.offsetHeight)
			ie5menu.style.top = document.body.scrollTop + event.clientY - ie5menu.offsetHeight;
		else
			ie5menu.style.top = document.body.scrollTop + event.clientY;
		ie5menu.style.visibility = "visible";
		return false;

	}

	/**
	 * Hide menu
	 */
	function hideMenu() {
		ie5menu.style.visibility = "hidden";
	}

	/**
	 * highlight background
	 */
	function highlight() {
		if (event.srcElement.className == "menuitems") {
			event.srcElement.style.backgroundColor = "highlight";
			event.srcElement.style.color = "white";
		}
	}

	/**
	 * remove highlightening
	 */
	function lowlight() {
		if (event.srcElement.className == "menuitems") {
			event.srcElement.style.backgroundColor = "";
			event.srcElement.style.color = "black";
			window.status = "";
		}
	}

	/**
	 * Enable/disable alerts
	 */
	function EnableOrDisableAlerts() {
		ie5menu.style.visibility = "hidden";
		if (isAlertsEnable) {
			ShowErrMsgWindowWithBtn("", "<s:text name='main.alert.disable' />", YES_NO, disableYes);

		} else {
			ShowErrMsgWindowWithBtn("", "<s:text name='main.alert.enable' />", YES_NO, enableYes);

		}
	}
	function disableYes() {
		isAlertsEnable = false;
		var obj = document.getElementById("statusText");
		obj.innerText = "Enable";
		var image = document.getElementById("alertMgsImg");
		if(isInteger(flashCounts) && flashCounts > 0){
			image.src = "images/Alert/DAlert_animated.gif";
		}
		else{
			image.src = "images/Alert/DAlert.gif";
		}
	}
	function enableYes() {
		isAlertsEnable = true;
		var obj = document.getElementById("statusText");
		obj.innerText = "Disable";
		var image = document.getElementById("alertMgsImg");
		if(isInteger(flashCounts) && flashCounts >0 )
			image.src = "images/Alert/EAlert_animated.gif";
		else
			image.src = "images/Alert/EAlert.gif";
	}

</script>
<SCRIPT language="JAVASCRIPT">
	var maxSessionTimeOutPeriod="<%=PropertiesFileLoader.getInstance().getPropertiesValue(SwtConstants.MAX_SESSION_TIMEOUT_PERIOD)%>";
	var alertInterval = "${requestScope.alertInterval}";
	var loggedoffIn="<s:text name='autologgedoff.msg.loggedoffIn'/>";
	var time = "<s:text name='autologgedoff.msg.time'/>";
	var stayLoggedIn ="<s:text name='autologgedoff.msg.stayLoggedIn'/>"
	//Added by Firas, to specified a language
	var user_language = "${user_lang}";

	var windowContainer = new SwtWindowsContainer();
	var reloginflag = false;
	var interfaceNotificationMessages = new Array();
	var interfaceDetails = new Array();
	var isNotificationEnable = "${requestScope.notifyUserFlag}";
	var CurrencyDecimalMetaData = new Array();
	${requestScope.CurrencyDecimalMetaData}

	var requestURL = new String('<%=request.getRequestURL()%>');
	var idx = requestURL.lastIndexOf('/');
	requestURL = requestURL.substring(0,idx+1);

	var alertType = '<%=SwtUtil.getUserAlertType(session)%>';
	var alertBothType = '<%=SwtConstants.ALERT_BOTH%>';
	var alertPopup = '<%=SwtConstants.ALERT_POPUP%>';
	var isPopupAlert = false;
	var isExpiredSession=false;
	if(alertType == alertBothType || alertType == alertPopup)
		isPopupAlert = true;


	function onHover(e){
		var event = (window.event|| e);
		var target = (event.srcElement || event.target);
		if(target.filters !=null){
			target.filters[0].Strength=1;
		}
		target.border=2;
	}
	function onOut(e){
		var event = (window.event|| e);
		var target = (event.srcElement || event.target);
		if(target.filters !=null){
			target.filters[0].Strength=0;
		}
		target.border=0;
	}

	function optimize()
	{
		path="";

		if (screen.width >= 1024&&screen.width <1152)
		{
			path="url('images/bannerenlarge_1024.gif')";
		}
		else if (screen.width >= 1152&&screen.width <=1280)
		{
			path="url('images/bannerenlarge_1280.gif')";
		}
		else
		{
			path="url('images/bannerenlarge.gif')";
		}

		document.getElementById("backGround").style.background= path+"  white center no-repeat fixed";
	}


	function relogin()
	{
		reloginflag = true;
		document.forms[0].action = requestURL + "logon.do?method=reLogin";
		document.forms[0].submit();
	}

	function submitForm(methodName){
		window.onunload=null;
		window.onbeforeunload=null;
		document.forms[0].method.value = methodName;
		document.forms[0].submit();
	}


	////////////////////////////////////////////////
	//Notification section: START
	////////////////////////////////////////////////


	/**
	 * To open the child window from menu screen in order to set the menu screen as the parent.
	 * @Param  childWindowURL
	 * @Param  childWindowName
	 * @Param  childWindowProperties
	 * @Param  childWindowCascadeFlag
	 * @return
	 */
//Added by Sutheendran Balaji A on 30-08-2012 for Mantis 1327
	function openAlertSummaryChild(childWindowURL,childWindowName,childWindowProperties,childWindowCascadeFlag){
		openWindow(childWindowURL,childWindowName,childWindowProperties,childWindowCascadeFlag);
	}
	function checkNewInternalMessages()
	{
		var oXMLHTTP = new XMLHttpRequest();
		var sURL = requestURL + "checknewmsgs.do?user_lang1234=" + getUserLanguage();

		oXMLHTTP.onreadystatechange = function() {
			if (this.readyState == 4 && this.status == 200) {
				var str=this.responseText;
				var popupMessageObj = new PopupMessage(str);
				var msgtext=popupMessageObj.getMessage();
				if(msgtext != null && msgtext.trim().valueOf() != "")
				{
					if(popupMessageObj.isErrorStatus()){
						isExpiredSession=true;
						var message = new String(popupMessageObj.getMessage());
						message = message.replace(/&#233;/g,'�');
						onCloseMenuWindow(message);
					}
					else{
						ShowErrMsgWindowWithBtn("", popupMessageObj.getMessage(), null);
					}
					document.getElementById("msgDummy").focus();
					window.focus();
				}
			}
		};
		oXMLHTTP.open( "POST", sURL, false );
		oXMLHTTP.send();
	}

	function showNotificationAlertMsg()
	{
		var msgCount = interfaceNotificationMessages.length;
		var finalMsg = "";

		for(var idx = 0 ; idx <  msgCount ; ++idx)
		{
			var msg = interfaceNotificationMessages[idx];
			var interfaceDet = interfaceDetails[idx];
			finalMsg = finalMsg + msg + "\n";

			for(var i = 0 ; i <  interfaceDet.length ; ++i)
			{
				finalMsg = finalMsg + interfaceDet[i];
			}
		}

		if(msgCount > 0 )
			ShowErrMsgWindowWithBtn("", finalMsg, null);
	}

	function checkNotCallback(){
		isExpiredSession=true;
		onCloseMenuWindow();
	}

	function checkNotificationAlertMessages()
	{
		var oXMLHTTP = new XMLHttpRequest();
		oXMLHTTP.onreadystatechange = function() {
			if (this.readyState == 4 && this.status == 200) {
				var str=new String(this.responseText);
				var noOfMsg = 0;
				interfaceNotificationMessages = new Array();
				interfaceDetails = new Array();

				var popupMessageObj = new PopupMessage(str);

				if(str != null && str.trim().valueOf() != "")
				{
					if(popupMessageObj.isErrorStatus()){
						ShowErrMsgWindowWithBtn("", popupMessageObj.getMessage(), null,checkNotCallback );
						return;
					}

					noOfMsg = getCountSweepAlertMsg(popupMessageObj.getMessage());

					if(noOfMsg > 0 )
					{
						if(isBlinkOn == false)
						{
							//call blinkNotificationImage() method after 0.5ms : waiting the icons to be ready for loading
							imageBlinkObj = window.setInterval(blinkNotificationImage, 500);
							isBlinkOn = true;
						}
						if (isNotificationEnable == 'Y') {
							document.getElementById("msgDummy").focus();
						}
					}
					else
					{
						if(isBlinkOn == true)
						{
							window.clearInterval(imageBlinkObj);
							isBlinkOn = false;
							clearNotificationImage();
						}
					}

					var msgArr = getAlertMessages(popupMessageObj.getMessage());
					var count = msgArr.length;
					var showCombinedMsg = false;
					var combinedMsg = "";

					for(var idx = 1 ; idx < count; ++idx)
					{
						if(msgArr[idx] != undefined &&  msgArr[idx] != null && msgArr[idx] != "")
						{
							if(isPopupAlert == true) {
								if (isNotificationEnable == 'Y') {
									ie5menu.style.visibility = "hidden";
									showCombinedMsg = true;
									combinedMsg = combinedMsg + msgArr[idx];
								}
							}

							var arr = msgArr[idx].split("\n");
							storeNotificationMessage(arr[0],arr[1]);
						}
					}
					if (showCombinedMsg == true) {
						ShowErrMsgWindowWithBtn("", combinedMsg, null);
					}
				}
			}
		};
		var sURL = requestURL + "interfaceinterruption.do";
		oXMLHTTP.open( "POST", sURL, false );
		oXMLHTTP.send();
	}


	/**
	 * Replaces the enabled alert image with notification image
	 */
	function clearNotificationImage()
	{
		var image = document.getElementById("alertNotifyImg");
		var alertImageSrc = new String(image.src);
		if( alertImageSrc.indexOf('notification_animated.gif') > 0 )
			alertImageSrc = alertImageSrc.replace('notification_animated.gif','notification.JPG');

		image.src = alertImageSrc;
	}

	/**
	 * Makes the notification image blinking
	 call this method after 0.5ms : waiting the icons to be ready for loading
	 */
	function blinkNotificationImage()
	{

		var image = document.getElementById("alertNotifyImg");
		if(image != null && image !='undefined' ){
			var alertImageSrc = new String(image.src);
			if( alertImageSrc.indexOf('notification.JPG') > 0 ){
				alertImageSrc = alertImageSrc.replace('notification.JPG','notification_animated.gif');
			}
			image.src = alertImageSrc;
			window.clearInterval(imageBlinkObj);
		}
	}

	/**
	 * Temporarly store the notification message into local data structure
	 */
	function storeNotificationMessage(message,notificationDet)
	{
		var length = interfaceNotificationMessages.length;

		for (var idx = 0 ; idx <= length ; ++idx){
			if(idx == length)
			{
				interfaceNotificationMessages[idx] = message;
				var arr = new Array();
				arr[0] = notificationDet;
				interfaceDetails[idx] = arr;
			}

		}
	}

	/**
	 * This function is periodically called to check for new scenario alerts
	 * Added by Saber Chebka for Mantis 1443
	 */
	var isScenarioAlertsEnable = true;
	var noOfScenarioAlertMsg = 0;
	var scenarioAlertInterval = "${requestScope.alertInterval}";
	var scenarioMsgCheckObj = window.setInterval(checkScenarioAlertMessages, scenarioAlertInterval);
	var scenarioAlertMessages = new Array();
	var scenarioDetails = new Array();

	var isAlertsEnable = true;
	var noOfAlertCounts = new Array();
	var imageBlinkObj ;
	var imageBlinkObjForAlert ;
	var isBlinkOn = false;
	var isBlinkOnForAlert = false;

	var scenarioPopup;
	var scenarioFlash;

	// an array that should contrain flash+popup alert counts
	var alertCounts = null;
	var flashCounts = 0;
	var popupCounts = 0;

	function checkScenarioAlertMessages(){

		if (!windowContainer.isMenuItemIdExist(WF_MENUITEM_ID) || windowContainer.isWindowNameExist(SCENARIO_SUMMARY_SCREEN)){

			refreshDisplayedData();
			// Launch the scenario summary screen
			if (isAlertsEnable && isInteger(popupCounts) ) {
				showOrRefreshScenarioSummary(false,'popup');
			}
			// Launch the scenario summary screen
			if (isAlertsEnable && isInteger(flashCounts) ) {
				showOrRefreshScenarioSummary(false,'flash');
			}

		}

	}

	/**
	 *
	 */
	function refreshDisplayedData(){
		var oXMLHTTP = new XMLHttpRequest();
		var sURL = requestURL + "scenarioAlerts.do";


		oXMLHTTP.onreadystatechange = function() {
			if (this.readyState == 4 && this.status == 200) {
				var str=new String(this.responseText);
				scenarioAlertMessages = new Array();
				scenarioDetails = new Array();

				var popupMessageObj = new PopupMessage(str);

				if(str != null && str.trim().valueOf() != "")
				{
					if(popupMessageObj.isErrorStatus()){
						isExpiredSession=true;
						onCloseMenuWindow();
					}

					alertCounts = getAlertCounts(popupMessageObj.getMessage());
					if(typeof alertCounts!='undefined'&&alertCounts!=null){
						popupCounts = alertCounts[0];
						flashCounts = alertCounts[1];
					}
					if(isInteger(flashCounts))
					{
						if(document.getElementById("alertMsg"))
						{
							document.getElementById("alertMsg").innerHTML = "&nbsp;("+flashCounts+")";
							var digits = flashCounts.toString().length;
							document.getElementById("alertMsg").width = digits*9+15;
						}
					}
					else
					{
						if(document.getElementById("alertMsg"))
						{
							document.getElementById("alertMsg").innerHTML = "&nbsp;("+0+")";
							document.getElementById("alertMsg").width = 24;
						}
					}

					if(flashCounts > 0 )
					{
						if(isBlinkOnForAlert == false)
						{
							//call blinkAlertImage() method after 0.5ms : waiting the icons to be ready for loading
							imageBlinkObjForAlert = window.setInterval(blinkAlertImage, 500);
							isBlinkOnForAlert = true;
						}
						if (isAlertsEnable && document.getElementById("msgDummy")) {
							// Next line is commented due to an error: When alert icon begin flashing, the menu screen is popped to top of all opened child windows
							// To detect the cause: Use IE profiler

							//document.getElementById("msgDummy").focus();
						}
					}
					else
					{
						if(isBlinkOnForAlert == true)
						{
							window.clearInterval(imageBlinkObjForAlert);
							isBlinkOnForAlert = false;
							clearAlertImage();
						}
					}
				}
			}
		};
		oXMLHTTP.open( "POST", sURL, false );
		oXMLHTTP.send();
	}
	/**
	 * Returns the list of alert messages
	 */
	function getAlertCounts(str)
	{
		return str.split('~');
	}

	/**
	 * is value an integer
	 */
	function isInteger(val){
		if(val != null){
			for(var i=0;i<val.length;i++){
				if(!isDigit(val.charAt(i))){
					return false;
				}
			}
		}
		return true;
	}

	/**
	 * used on isInteger
	 */
	function isDigit(n) {
		if((n < "0") || ("9" < n)) {
			return false
		}
		else {
			return true
		}
	}

	function getCountSweepAlertMsg(str)
	{
		if(str != undefined && str != null && str.length > 0)
			return str.substring(0,str.indexOf('~'));
	}

	function getAlertMessages(str)
	{
		return str.split('~');
	}

	/**
	 * Makes the alert image blinking
	 call this method after 0.5ms : waiting the icons to be ready for loading
	 */
	function blinkAlertImage()
	{
		if(document.getElementById("alertMgsImg")){
			var image = document.getElementById("alertMgsImg");
			if (image !=null && image !='undefined') {
				var alertImageSrc = new String(image.src);
				if( alertImageSrc.indexOf('Alert.gif') > 0 )
					alertImageSrc = alertImageSrc.replace('Alert.gif','Alert_animated.gif');
				image.src = alertImageSrc;
				window.clearInterval(imageBlinkObjForAlert);
			}
		}
	}

	function clearAlertImage()
	{
		var image = document.getElementById("alertMgsImg");
		var alertImageSrc = new String(image.src);

		if( alertImageSrc.indexOf('Alert_animated.gif') > 0 )
			alertImageSrc = alertImageSrc.replace('Alert_animated.gif','Alert.gif');

		image.src = alertImageSrc;
	}

	document.onmousemove = function(e) {
		var event = e || window.event;
		window.mouseX = event.clientX;
		window.mouseY = event.clientY;

	}

	/**
	 * This function opens a new window as a target for the scenario summary screen
	 * When the scenario summary screen is already opened, then call it for refresh
	 */
	function showOrRefreshScenarioSummary(bringToFront,fromFlashOrPopUp){
// variable to hold focus state wether false/true
		var isInsidePredict= false;
		var appName = "<%=SwtUtil.appName%>";
		if(fromFlashOrPopUp=="popup") {

			if("2" != scenarioSummaryaccess){
				if((typeof(scenarioPopup)=="undefined" || scenarioPopup.closed)&&(popupCounts > 0)){
					//check if we are inside Predict
					isInsidePredict =(this.windowContainer.isInsidePredict() || document.hasFocus() );

					//scenarioPopup = openWindow(requestURL + "scenarioSummary.do?callerMethod=Y__&popupScreen=Y",'scenarioSummaryWindow','left=50,top=190,width=750,height=550,toolbar=0,status=yes, resizable=yes, scrollbars=yes',false);
					var param = '/' + appName + '/scenMaintenance.do?method=openAlertInstSummary&callerMethod=Y__&popupScreen=Y&menuItemId=156&ismenuItem=true';
					scenarioPopup = openWindow(param,'scenarioSummaryWindow','left=50,top=190,width=1200,height=750,toolbar=0,status=yes, resizable=yes, scrollbars=yes',false);

					if(isInsidePredict){
						scenarioPopup.focus();
					}
					else
						scenarioPopup.blur();
					return;
				}else{
					try{
						scenarioPopup.refreshFlexContent();
					}catch(exception){
						// Ignore
					}
				}
				if(bringToFront)
					scenarioPopup.focus();
			}
		}
		else if (fromFlashOrPopUp=="flash"){
			refreshDisplayedData();
			if((typeof(scenarioFlash)=="undefined" || scenarioFlash.closed)&&(flashCounts > 0)&&bringToFront){
//     		scenarioFlash = openWindow(requestURL + "scenarioSummary.do?&callerMethod=_Y_",'scenarioSummaryWindow','left=50,top=190,width=750,height=550,toolbar=0,status=yes, resizable=yes, scrollbars=yes',false);
				var param = '/' + appName + '/scenMaintenance.do?method=openAlertInstSummary&callerMethod=_Y_&menuItemId=156&ismenuItem=true';
				scenarioFlash = openWindow(param,'scenarioSummaryWindow','left=50,top=190,width=1200,height=750,toolbar=0,status=yes, resizable=yes, scrollbars=yes',false);
				scenarioFlash.focus();

			}else if (!(typeof(scenarioFlash)=="undefined"  ) && flashCounts > 0 ){
				try{
					scenarioFlash.refreshFlexContent();
				}catch(exception){
					// Ignore
				}
			}
			if(typeof(scenarioFlash)!="undefined"&&bringToFront)
				scenarioFlash.focus();

		}
	}

	////////////////////////////////////////////////
	// Notification section: END
	////////////////////////////////////////////////

	var profileChangedIndex;
	var profileOldIndex;
	function changeProfile(){

		profileChangedIndex = document.forms[0].elements["user.profileId"].selectedIndex;
		profileOldIndex = document.forms[0].elements["user.profileId"].oldSelectedIndex;

		ShowErrMsgWindowWithBtn("","<s:text name='main.alert.changeProfile' />", YES_NO, changeYes, changeNo);

	}
	function changeYes() {
		document.getElementById("user.profileId").oldSelectedIndex = profileChangedIndex;
		windowContainer.closeAllWindows();
		var profileId=document.forms[0].elements["user.profileId"].value;

		var oXMLHTTP = new XMLHttpRequest();
		var sURL = requestURL + "userprofiles.do?method=getProfileDetails";
		sURL = sURL + "&profileId="+profileId;

		oXMLHTTP.open( "POST", sURL, false );
		oXMLHTTP.send();
		var str=new String(oXMLHTTP.responseText);

		if(str!='false'){
			var screenValues=str.split("*");
			for(var idx = 0 ; idx < screenValues.length ; ++idx)
			{
				var screenValues1 = screenValues[idx].split("|");

				openWindow(screenValues1[0],screenValues1[1],screenValues1[2],screenValues1[3],screenValues1[4]);
			}
		}
	}
	function changeNo() {
		document.forms[0].elements["user.profileId"].selectedIndex = profileOldIndex;
	}

	var internalMsgCheckObj = window.setInterval(checkNewInternalMessages, 20000);
	var inputInterruptionMsgCheckObj = window.setInterval(checkNotificationAlertMessages, alertInterval);

	function focusMenu(e){
		var event = (window.event|| e);
		if(String.fromCharCode(event.keyCode) == 'N' && event.shiftKey)
			document.getElementById("el1").fireEvent("onmouseover");
		if(String.fromCharCode(event.keyCode) == 'W' && event.shiftKey)
			document.getElementById("el2").fireEvent("onmouseover");
		if(String.fromCharCode(event.keyCode) == 'T'&& event.shiftKey)
			document.getElementById("el3").fireEvent("onmouseover");
		if(String.fromCharCode(event.keyCode) == 'C' && event.shiftKey)
			document.getElementById("el4").fireEvent("onmouseover");
		if(String.fromCharCode(event.keyCode) == 'R' && event.shiftKey)
			document.getElementById("el5").fireEvent("onmouseover");
		if(String.fromCharCode(event.keyCode) == 'H' && event.shiftKey)
			document.getElementById("el6").fireEvent("onmouseover");
		if(String.fromCharCode(event.keyCode) == 'L' && event.shiftKey)
			document.getElementById("el0").fireEvent("onmouseover");
	}

	function refreshDesktop(){
		windowContainer.bringAllWindowsOnTop();
	}

	function showPreviousLoginDetails(){
		showLoginNotificationDetails();
	}
	//This function get called when user clicks on  'logoff' event.
	function onCloseMenuWindow(message)
	{
		//clear the interval values for internal messages,alert  & interface notification messages
		window.clearInterval(internalMsgCheckObj);
		window.clearInterval(scenarioMsgCheckObj);
		window.clearInterval(inputInterruptionMsgCheckObj);

		//Condition added to call log out action only if all the screens get closed by sami for Mantis 1327 on 06-Sep-2012
		while (windowContainer.windowsCollection.length!=0)
		{
			//to close the all opening screens
			windowContainer.closeAllWindows();
		}
		//call the log out action only if relogin not set
		if ((reloginflag == false)&& (logoutSubmitFlag==false))
		{
			//to avoid resubmitting the logout event ,logout submit flag set as true
			logoutSubmitFlag=true;

			var mfaEnabled = "<%=RegisterMFA.getInstance().isUseSmartAuthenticator()%>";
			var extraParams= "";
			if(mfaEnabled)
				extraParams="&userId="+userId+"&mfaToken="+mfaToken

			if(isExpiredSession)
				document.forms[0].action = "logout.do?method=logout&isExpiredSession=true"+extraParams+"&message="+escape(message);
			else
				document.forms[0].action = "logout.do?method=logout&refresh=true"+extraParams;
			document.forms[0].submit();

		}

	}
	function setShortcuts()
	{
		var appName = "<%=SwtUtil.appName%>";
		var requestURL = new String('<%=request.getRequestURL()%>');
		var idy = requestURL.indexOf('/' + appName + '/');
		requestURL = requestURL.substring(0, idy + 1);
		requestURL = requestURL + appName+"/shortcut.do?method=refreshShortcutlist";
		var result = sendRequest(requestURL);
		var shortcutlist = new Array()
		shortcutlist = result.split('#');
		return shortcutlist;
	}

	/**
	 *
	 *This method is used to refresh the shortcutdetails and frame the shortcut icon image in main window.
	 */
	function refreshShortcuts() {
		var i = 0;
		var tableData = "";
		//Get the shortcuts element id
		var table = document.getElementById("shortcuts");
		var shortcutlist = new Array();
		var shortcut = new Array();
		//Get the shortcuts element Tag name
		var table_cells=document.getElementById("shortcuts").getElementsByTagName("td");
		shortcutlist = setShortcuts();
		if(typeof shortcutlist != 'undefined' && shortcutlist != null && shortcutlist.length > 0){
			for (i = 0; i < shortcutlist.length - 1; i++) {
				shortcut[i] = shortcutlist[i].split(',');
				//Frame the shortcut icon image
				table_cells[i].innerHTML="<div style=\"position:relative;width:26px;\"><a onmouseover=\"onHover(event);\" onmouseout=\"onOut(event);\" onClick=\"checkShortcutAccess('"+shortcut[i][0]+"','"+shortcut[i][1]+"Window','width="+shortcut[i][5]+",height="+shortcut[i][4]+",toolbar=0, resizable=yes, scrollbars=yes','true')\"><img width=\"20\" height=\"20\" align=\"center\" id=\"shadowImg\"  src=\""+shortcut[i][3]+".gif\" name='"+shortcut[i][1]+"'  border=\"0\" style=\"border-color:white; border-style:outset\" title='"+shortcut[i][2]+"'></a></div>";
			}
			for ( var j = i; j < table_cells.length; j++)
				table_cells[j].innerHTML = "<div style=\"position:relative;width:26px;\"></div>";
		}
	}

	function sendRequest(requestURL)
	{
		var oXMLHTTP = new XMLHttpRequest();
		oXMLHTTP.open("POST", requestURL, false);
		oXMLHTTP.setRequestHeader("Pragma", "no-cache")
		oXMLHTTP.send();
		return new String(oXMLHTTP.responseText);


	}
	function setToolBars() {
		var sytemTestDate = "${requestScope.sytemTestDate}";
		if (sytemTestDate != "" && sytemTestDate != null) {
			var sysDate = new String(sytemTestDate).substr(0, 10);
			var year = new String(sysDate).substr(0, 4);
			var month = new String(sysDate).substr(5, 2);
			var day = new String(sysDate).substr(8, 2);
			var sysDateString = month + "/" + day + "/" + year;
			if (month.substr(0, 1) == "0") {
				month = month.substr(1, 1);
			}
			if ("${user_lang}" == "FR"){
				var monthValue = new Array('Jan', 'Fév', 'Mar', 'Avr', 'Mai', 'Jui', 'Jui', 'Août', 'Sep', 'Oct', 'Nov', 'Déc');
				user_language = "FR";
			}else
				var monthValue=new Array('Jan','Feb','Mar','Apr','May','Jun','Jul','Aug','Sep','Oct','Nov','Dec');
			ShowErrMsgWindowWithBtn("", "<s:text name='main.warningMsg'/>" + day + " " + monthValue[parseInt(month) - 1] + " " + year, null);
			document.forms[0].elements["testDateField"].value = sysDateString;
		} else {
			document.forms[0].elements["testDateField"].value = "";
		}
	}

	var logOutIndicator = false;
	var logoutSubmitFlag = false;

	function logout() {
		ShowErrMsgWindowWithBtn("", "<s:text name='main.alert.loggedOff'/>", YES_NO, logoffCallbackHandler);
	}


	function logoffCallbackHandler(){
		window.clearInterval(internalMsgCheckObj);
		window.clearInterval(scenarioMsgCheckObj);
		window.clearInterval(inputInterruptionMsgCheckObj);
		logOutIndicator = true;
		logoutSubmitFlag = true;

		var mfaEnabled = "<%=RegisterMFA.getInstance().isUseSmartAuthenticator()%>";
		var extraParams= "";
		if(mfaEnabled)
			extraParams="&userId="+userId+"&mfaToken="+mfaToken;


		document.forms[0].action = "logout.do?method=logout"+extraParams;
		document.forms[0].submit();
	}

	function onUnLoad() {
		if(popupWindow!=null && false == popupWindow.closed)
		{
			popupWindow.close();
		}
		window.clearInterval(internalMsgCheckObj);
		window.clearInterval(scenarioMsgCheckObj);
		window.clearInterval(inputInterruptionMsgCheckObj);

		var mfaEnabled = "<%=RegisterMFA.getInstance().isUseSmartAuthenticator()%>";
		var extraParams= "";
		if(mfaEnabled)
			extraParams="&userId="+userId+"&mfaToken="+mfaToken;

		document.forms[0].action = "logout.do?method=logout"+extraParams;
		if (!logOutIndicator) {
			if (!logoutSubmitFlag)
			{
				logoutSubmitFlag = true;
				document.forms[0].submit();
			}
		}
	}

			function saveProfile()
	{
		var url = "userprofiles.do?method=add";

		var profileSelectBox = document.forms[0].elements["user.profileId"];

		var selectedIndex = profileSelectBox.selectedIndex;

		var selectedOption = profileSelectBox.options[selectedIndex];

		var profileId = selectedOption.value;
		var profileName = selectedOption.text;

		url = url + "&" + "profileId=" + profileId;
		url = url + "&" + "profileName=" + profileName;
		url = url + "&wndsetting=" + getBaseWindows();

		openWindow(url,'saveProfileWindow','left=50,top=190,width=470,height=162,toolbar=0,status=yes, resizable=yes, scrollbars=yes');
	}
	function loadPage()
	{
		document.forms[0].elements["user.profileId"].oldSelectedIndex = document.forms[0].elements["user.profileId"].selectedIndex;
		ShowErrMsgWindowWithBtn("", document.forms[0].elements["user.profileId"].oldSelectedIndex, null);


	}

	var timerId;
	var popupWindow;
	function startSessiontTimer() {
		localStorage.setItem('shouldCloseAbout', 'false');
		clearTimeout(timerId);
		timerId = window.setTimeout('ShowAutoLogoutAlert()', maxSessionTimeOutPeriod*60*1000);

		const myTimeout = setTimeout(showLoginNotificationDetails, 3000);
	}

	function showLoginNotificationDetails() {
		// 		toastr.info('Are you the 6 fingered man?')
		toastr.options = {
			"closeButton" : false,
			"debug" : false,
			"newestOnTop" : false,
			"progressBar" : false,
			"positionClass" : "toast-top-right",
			"preventDuplicates" : false,
			"showDuration" : "500",
			"hideDuration" : "1000",
			"timeOut" : "8000",
			"extendedTimeOut" : "1000",
			"showEasing" : "swing",
			"hideEasing" : "linear",
			"showMethod" : "fadeIn",
			"hideMethod" : "fadeOut",
		};
		//data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAGwSURBVEhLtZa9SgNBEMc9sUxxRcoUKSzSWIhXpFMhhYWFhaBg4yPYiWCXZxBLERsLRS3EQkEfwCKdjWJAwSKCgoKCcudv4O5YLrt7EzgXhiU3/4+b2ckmwVjJSpKkQ6wAi4gwhT+z3wRBcEz0yjSseUTrcRyfsHsXmD0AmbHOC9Ii8VImnuXBPglHpQ5wwSVM7sNnTG7Za4JwDdCjxyAiH3nyA2mtaTJufiDZ5dCaqlItILh1NHatfN5skvjx9Z38m69CgzuXmZgVrPIGE763Jx9qKsRozWYw6xOHdER+nn2KkO+Bb+UV5CBN6WC6QtBgbRVozrahAbmm6HtUsgtPC19tFdxXZYBOfkbmFJ1VaHA1VAHjd0pp70oTZzvR+EVrx2Ygfdsq6eu55BHYR8hlcki+n+kERUFG8BrA0BwjeAv2M8WLQBtcy+SD6fNsmnB3AlBLrgTtVW1c2QN4bVWLATaIS60J2Du5y1TiJgjSBvFVZgTmwCU+dAZFoPxGEEs8nyHC9Bwe2GvEJv2WXZb0vjdyFT4Cxk3e/kIqlOGoVLwwPevpYHT+00T+hWwXDf4AJAOUqWcDhbwAAAAASUVORK5CYII=
		var buttonDetailsLabel='<s:text name="button.details"/>';
		var buttonDetailsTooltip='<s:text name="menulabel.itemid.45"/>';

		toastr
				.info('<div>'+notificationTextMessage+'</div><div id="ddimagebuttons" style="position: absolute;   top: 15px;  right: 20px;">		<a tabindex="18" title="'+buttonDetailsTooltip+'" onmouseout="collapsebutton(this)" onmouseover="highlightbutton(this)" onmousedown="expandbutton(this)" onmouseup="highlightbutton(this)" onclick="openMyUserDetailsScreen()" >'+buttonDetailsLabel+'</a>		</div></div>');

	}
	function openMyUserDetailsScreen(){

// 		var param = '/' + appName + '/scenMaintenance.do?method=openAlertInstSummary&callerMethod=Y__&popupScreen=Y&menuItemId=156&ismenuItem=true';
		var userDetailsPopupPopup = openWindow('useroptions.do?menuAccessId=0&menuItemId=45&ismenuItem=true','','width=650,height=650,toolbar=0, status=yes,resizable=yes, scrollbars=yes','true','45');

	}
	//This function appends 'menuAccessId' with the actionPath
	function checkShortcutAccess(actionPath, windowName, attributes, flag) {

		var actionPathStr = new String(actionPath);
		actionPathStr = actionPathStr.replace('?', '.');
		var menuAccessIdChild = getMenuAccessIdOfChildWindow(actionPathStr); //  find menuAccessId of child
		var childAccIdString = "&menuAccessId=" + menuAccessIdChild; //String to be appended in actionPath

		var appendedActionPath = new String(actionPath);
		var paramFound = appendedActionPath.search('=');
		if (paramFound != -1) { //aleready one parameter is attached
			appendedActionPath = appendedActionPath + childAccIdString;
		} else {
			childAccIdString = "?menuAccessId=" + menuAccessIdChild;
			appendedActionPath = appendedActionPath + childAccIdString;
		}
		openWindow(appendedActionPath, windowName, attributes, flag);

	}
	setTitleSuffix(document.forms[0]);
</script>
<swallow:openProfile />

<body style="background: #FFFFFF;" id="body1" leftmargin="0"
	  topmargin="0" marginwidth="0" marginheight="0"
	  onload="startSessiontTimer();openProfileWindows();setToolBars();optimize();refreshDisplayedData();"
	  onunLoad="onCloseMenuWindow();onUnLoad();"
	  onkeydown="focusMenu(event)">
<swallow:menu />
<%@ include file="angularSources/includeJS.jsp" %>
<% if (!SwtUtil.envTestEnabled()){ %>
<div id="backGround"
	 style="position: absolute; left: 0px; top: 59px; width: 100%; height: 90%; filter: progid:            DXImageTransform.            Microsoft.            BasicImage(grayscale =             0, xray =             0, mirror =             0, invert =             0, opacity =             0.95, rotation =             0)">
</div>
<% } %>	
<form action="logon.do">
	<table width="100%" border="0" cellpadding="0" cellspacing="0">
		<tr>
			<td>
				<table width="100%" border="0" cellpadding="0" cellspacing="0"
					   bgcolor="#DBDBD7">
					<tr align="left" valign="top">
						<td bgcolor="#DBDBD7" width="100%" height="27"></td>
					</tr>
					<tr>
						<td colspan="11"><img src="images/line.gif" width="100%"
											  height="1"></td>
					</tr>
					<tr>
						<td height="30">

							<table width="100%" cellpadding="0" class="shortcuts"
								   id="shortcuts">
								<tr>
									<s:iterator value="#request.shortcutList" var="shortcut">

										<s:if test="#shortcut.id.shortcutId != null">
											<s:if test="#shortcut.imageName !=null">
												<td width="40">
													<div style="position: relative; width: 26px;">
														<a onmouseover="onHover(event);" onmouseout="onOut(event);"
														   onClick="checkShortcutAccess('<s:property value="#shortcut.action" />', '<s:property value="#shortcut.id.shortcutId" />Window', 'width=<s:property value="#shortcut.width" />,height=<s:property value="#shortcut.height" />,toolbar=0, resizable=yes, scrollbars=yes', 'true');">
															<img width="20" height="20" align="center" id="shadowImg" border="0"
																 style="border-color: white; border-style: outset"
																 src="<s:property value="#shortcut.imageName" />.gif" name="<s:property value="#shortcut.id.shortcutId" />"
																 border="0" title="<s:property value="#shortcut.shortcutName" />">
														</a>
													</div>
												</td>
											</s:if>
											<s:else>
												<td width="40">&nbsp;
													<a onMouseOut="MM_swapImgRestore()" onmouseover="onHover(event);"
													   onmouseout="onOut(event);"
													   onClick="checkShortcutAccess('${shortcut.action}', '${shortcut.id.shortcutId}Window', 'width=${shortcut.width},height=${shortcut.height},toolbar=0, resizable=yes, scrollbars=yes', 'true');">
														<img src="images/SC10.gif" name="${shortcut.id.shortcutId}" border="0"
															 title="${shortcut.shortcutName}">
													</a>
												</td>
											</s:else>
										</s:if>
										<s:else>
											<td width="40">
												<div style="position:relative;width:26px;"></div>
											</td>
										</s:else>
									</s:iterator>

								</tr>
							</table>
						</td>
						<td>
							<table width="100%" border="0" cellpadding="0" cellspacing="0" style="table-layout:fixed;">
								<tr>
									<s:if test='#request.notifyUserFlag == "Y"'>
									<td width="30">&nbsp;
										<img id="alertNotifyImg" src="images/Alert/notification.JPG" border="0"
											 onclick="showNotificationAlertMsg()" title='<s:text name="tooltip.viewNotificationMessages" />'>
									</td>
									</s:if>

										<% String accessScenario=SwtUtil.getMenuAccessValue(SwtConstants.MENU_ITEM_ALERT_SUMMARY_DISPLAY,request);if (!"2".equals(accessScenario)){ %>
									<td width="35">&nbsp; <img id="alertMgsImg"
															   src="images/Alert/EAlert.gif" border="0"
															   onclick="showOrRefreshScenarioSummary(true,'flash')"
															   title="<s:text name='tooltip.enableDisableAlartPopups' />"></td>

									<td width="25" id="alertMsg" onclick="showOrRefreshScenarioSummary(true,'flash')" style="text-align:right;">&nbsp;(0)</td>
									<td width="40" id="alertMsg1" onclick="showOrRefreshScenarioSummary(true,'flash')" style="text-align:left;">&nbsp;<s:text name="main.alerts" /></td>
										<% } %>
									<td width="125">&nbsp;

										<s:select id="user.profileId"   value="%{#request.defaultProfile}" name="user.profileId"  onchange="javascript:changeProfile()" cssStyle="width:110px"
												  list="#request.profileList" listKey="value" listValue="label" />
									</td>

									<td width="20">
										<a
												onMouseOut="MM_swapImgRestore()"
												onMouseOver="MM_swapImage('Image171','','images/Save_R1.gif',1)"
												onClick="saveProfile()" title="<s:text name='tooltip.saveProfil' />"> <img
												src="images/Save1.gif" name="Image171" border="0" id="Image171">
										</a>
									</td>

									<td width="35">&nbsp;&nbsp;&nbsp;<img id="alertMgsImg"
																		  onMouseOut="MM_swapImgRestore()"
																		  onMouseOver="MM_swapImage('Image172','','images/reopen_R.gif',1)"
																		  src='images/reopen1.gif' border="0" onclick="refreshDesktop()"
																		  name="Image172" title="<s:text name='main.refreshDesktop' />">
									</td>
									<td width="5"></td>
									<td width="35">
										<img id="blueinfo"
											 src='images/blueinfo.png' border="0" onclick="showPreviousLoginDetails()"
											 name="Image173" title="<s:text name='main.showPreviousLogin' />"
										>
									</td>

						</td>

					</tr>
				</table>
			</td>

			<td>
				<div border="1" style="position: relative; width: 40px;"></div>
			</td>


		</tr>
		<tr>
			<td colspan="11"><img src="images/line.gif" width="100%"
								  height="3"></td>
		</tr>
	</table>
	</td>
	</tr>
	</table>
	<input id="msgDummy" style="width: 0px" readOnly />
	<input name="testDateField" value="" width="12px"
		   style="visibility: hidden">
	<input name="userId" value="<%=SwtUtil.getCurrentUser(session).getId().getUserId()%>" width="12px"
		   style="visibility: hidden">



	<div id="ie5menu" class="skin0" onMouseover="highlight()"
		 onMouseout="lowlight()" onClick="EnableOrDisableAlerts();">
		<div id="statusText" class="menuitems"
			 style="font-size: 8pt; font-style: Verdana">Disable</div>
	</div>
	<script language="JAVASCRIPT">
		ie5menu.className = menuSkin;
		var image = document.getElementById("alertMgsImg");
		image.oncontextmenu = showMenu;
		document.body.onclick = hideMenu;
	</script>

	<!--BEGIN: Added for mantis 1967 by KaisBS: Improving and integrating SWFProfiler tool on all Flex screens
 this property will indicate whether swf profiler is enabled or not -->
	<input type="text" name="swfProfilerEnabled" value="<%=PropertiesFileLoader.getInstance().getPropertiesValue(SwtConstants.PROPERTY_SWFPROFILER_ENABLED)%>" style="visibility: hidden"/>
	<script>
		// This function returns the value of swfPRofilerEnabled and will be called in the flex part
		function returnSwfProfEnabValue() {
			return document.forms[0].elements["swfProfilerEnabled"].value;
		}

		function getUserLanguage() {
			return user_language;
		}

	</script>

</form>

<% if (SwtUtil.envTestEnabled()){ %>
	<swallow:simplemenu/>
<% } %>	

</body>
</html>