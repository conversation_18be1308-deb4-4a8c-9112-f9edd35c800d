<%
response.setHeader("Pragma","no-cache"); //HTTP 1.0
response.setHeader("Cache-Control","no-cache"); //HTTP 1.1
response.setDateHeader ("Expires", 0); //prevents caching at the proxy server
%>
<%@ taglib prefix="s" uri="/struts-tags" %>

<%@ taglib uri="http://struts.apache.org/tags-html" prefix="html" %>

<%@ taglib uri="http://java.sun.com/jstl/fmt_rt" prefix="fmt" %>
<%@ page import="java.util.*"  %>

<html>
<body>
This is Entity JSP page. <br>
    
<s:text name="entity.domestic_currency"/>:</label>
${actionError} 

<html:form action="entity.do">
<bean:define id="entityId" name="entity" scope="session" />
<bean:define id="currencyMast" name="currencyMaster" scope="session" />
<bean:define id="countries" name="countries" scope="session" />

<html:select name="entityId" property="domesticCurrency" >
    <html:options collection="currencyMast" property="value" labelProperty="label" />
 </html:select>

 <html:select name="entityId" property="reprotingCurrency" >
    <html:options collection="currencyMast" property="value" labelProperty="label" />
 </html:select>

  <html:select name="entityId" property="countryId" >
    <html:options collection="countries" property="value" labelProperty="label" />
 </html:select>

</html:form>

</body>
</html>

