div[style*="outset"]{
	-moz-box-sizing: border-box !important;
	box-sizing:border-box;
	border-color: #FFFFFF !important;
	border-left-style: double !important;
	border-left-color: #FFFFFF !important;
	border-left-width: 2px !important;
	border-top-style: double !important;
	border-top-color: #FFFFFF !important;
	border-top-width: 2px !important;
	border-right-style: outset !important;
	border-right-color: #FFFFFF !important;
	border-right-width: 2px !important;
	border-bottom-style: outset !important;
	border-bottom-color: #FFFFFF !important;
	border-bottom-width: 2px !important;
}
body {
	overflow-y: scroll;
}

/* div[style*="border: 0px outset"]{ */
/* 	border-top-width: 0px !important; */
/* 	border-right-width: 0px !important; */
/* 	border-bottom-width: 0px !important; */
/* } */

#tabcontentcontainer{
	border-color: #FFFFFF !important;
	border-top-width: 0px !important;
	border-right-style: outset !important;
	border-right-color: #FFFFFF !important;
	border-right-width: 2px !important;

	border-bottom-width: 2px !important;
	border-bottom-style: outset !important;
	border-bottom-color: #FFFFFF !important;
}

.tabcontent div[style*="outset"]{
	border-top-width:0px !important;
}

fieldset[style*="groove"]{
	height: 100% !important;
	border:none !important;
	border-top-width: 2px !important;
	border-bottom-width: 2px !important;
	border-right-width: 2px !important;
	border-left-width: 2px !important;
	border-top-style: groove !important;
	border-bottom-style: groove !important;
	border-right-style: groove !important;
	border-left-style: groove !important;
	border-top-color: inherit !important;
	border-bottom-color: inherit !important;
	border-right-color: inherit !important;
	border-left-color: inherit !important;
	padding-left: 0px !important;
	padding-right: 0px !important;
	padding-top: 1px !important;
	padding-bottom: 1px !important;
	box-sizing: border-box;
}

img[src*="calendar-16.gif"]{
	margin-bottom: -7px !important;
}

img[src*="page_up.png"]{
	margin-top: -4px !important;
	margin-bottom: -1px !important;
}

img[src*="page_down.png"]{
	margin-top: -2px !important;
	margin-bottom: -4px !important;
}

select{
	border-color: #a9a9a9 !important;
	border-left-style: solid !important;
	border-left-color: #a9a9a9 !important;
	border-left-width: 1px !important;
	border-top-style: solid !important;
	border-top-color: #a9a9a9 !important;
	border-top-width: 1px !important;;
	border-bottom-style: solid !important;
	border-right-style: solid !important;
	border-right-width: 1px !important;
	border-right-color: #a9a9a9 !important;
	border-bottom-width: 1px !important;
	border-bottom-color: #a9a9a9 !important;	
	outline:none;
}

input[type="checkbox"] {
	-moz-box-sizing: border-box !important;
	box-sizing: border-box;
	width: auto !important;
	padding-left: 0px !important;
	padding-right: 0px !important;
	padding-top: 0px !important;
	padding-bottom: 0px !important;
	margin: 0px 0px 0px 0px;
	outline:none;
}

input[type="radio"] {
	-moz-box-sizing: border-box !important;
	box-sizing: border-box;
	width: auto !important;
	padding-left: 0px !important;
	padding-right: 0px !important;
	padding-top: 0px !important;
	padding-bottom: 0px !important;
	margin: 0px 0px 0px 0px;
	outline:none;
}

input[type="text"] {
	margin-top: 1px !important;
	margin-bottom: 1px !important;
	margin-left: 0px;
	margin-right: 0px;
	box-sizing: border-box;
	-moz-box-sizing: border-box;
	outline:none;
}

input[readonly="readonly"] {
	-moz-box-sizing: border-box !important;
	box-sizing: border-box;
	margin-top: 1px !important;
	margin-bottom: 0px !important;
	margin-left: 0px;
	margin-right: 0px;
	outline:none;
}

@-moz-document url-prefix() { 
   input[type="text"] {
	margin-top: 0px !important;
	margin-bottom: 0px !important;
	margin-left: 0px;
	margin-right: 0px;
	box-sizing: border-box;
	-moz-box-sizing: border-box;
}
fieldset[style*="groove"] {
	-moz-box-sizing: border-box !important;
	box-sizing: border-box;
	border-color: #FFFFFF !important;
	height: 100% !important;
	padding-left: 0px !important;
	padding-right: 0px !important;
	padding-top: 1px !important;
	padding-bottom: 1px !important;
	margin-top: 1px !important;
}
}

textarea {
	-moz-box-sizing: border-box !important;
	box-sizing: border-box;
	margin-top: 1px !important;
	margin-bottom: 1px !important;
	margin-left: 0px;
	margin-right: 0px;
	resize:none !important;
	border: 1px solid #A9A9A9;
}

input[value="..."] {
	padding-left: 0px !important;
	padding-right: 0px !important;
	padding-top: 2px !important;
	padding-bottom: 2px !important;
}

#ddimagebuttons a{
	font-family: verdana,helvetica !important;	
}


#tabcontentcontainer{
	z-index: 0;
}

.sort-table tbody td {
	-moz-box-sizing: border-box !important;
	box-sizing: border-box !important; 
	overflow: hidden !important;
	white-space: nowrap; 
}

.sort-table thead td {
	-moz-box-sizing: border-box !important;
	box-sizing: border-box !important; 
	padding-left: 0px;
}

.dropDown {
	overflow-x : hidden !important; 
	box-sizing: border-box !important;
	-moz-box-sizing: border-box !important;
	height: 150px !important;
	left: 0px;
}

#ddscrolltable {
-ms-overflow-style: display;
background-color: white;
}
tbody {
overflow:auto;
}

#helpIcon{
	z-index: 5;
}

#BookCode {
	z-index: 5;
}
#ddimagebuttons a.disabled {
	color : gray;
}
@-moz-document url-prefix() { 
  textarea {
    resize: none;
}
}select[disabled] {
	background-color: rgba(229,229,229,1);
	color: rgb(124,124,124);
	text-shadow: 1px 1px 0 rgb(255,255,255);
}

.inputText[disabled] {
	background-color: rgba(229,229,229,1);
	color: rgb(124,124,124);
	text-shadow: 1px 1px 0 rgb(255,255,255);
}