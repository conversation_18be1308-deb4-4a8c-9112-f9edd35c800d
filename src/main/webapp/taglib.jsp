<!-- Commented to disable running in compatibility mode -->
<%@page import="org.swallow.util.CommonDataManager"%>
<%@page import="org.swallow.web.XSSFilter"%>
<%@page import="org.apache.commons.logging.LogFactory"%>
<%@page import="org.apache.commons.logging.Log"%>
<meta http-equiv="X-UA-Compatible" content="IE=edge" />

<!-- CSP settings to override -->
<!-- <meta http-equiv="Content-Security-Policy" content="default-src 'self'; style-src 'self' 'unsafe-inline'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; "> -->
<%
	response.setHeader("Pragma","no-cache"); //HTTP 1.0
	response.setHeader("Cache-Control","no-cache"); //HTTP 1.1
	// Deny Cross Frame scripting (security issue)
	response.setHeader("X-Frame-Options","DENY");
	// Content Sniffing should be disabled (security issue)
	response.setHeader("X-Content-Type-Options","nosniff");
	response.setHeader("Strict-Transport-Security", "max-age=31622400; includeSubDomains");
	response.setDateHeader ("Expires", 0); //prevents caching at the proxy server
	
	// Disable displaying Reported Error (mode=reported only, not enforced): [Report Only] Refused to evaluate a string as JavaScript because 'unsafe-eval' is not an allowed source of script ...
	response.setHeader("Content-Security-Policy-Report-Only", "");
%>
<%@ page import="java.util.*"  %>
<%@ page import="org.swallow.util.SwtUtil"%>
<%@ page import="org.swallow.model.MenuItem"%>

<%@ page import="org.swallow.util.SwtConstants"%>
<%@ page import="org.swallow.util.PropertiesFileLoader"  %>
<%@ page contentType="text/html; charset=UTF-8"%>

<%@ taglib prefix="s" uri="/struts-tags" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>

<script type="text/javascript" > 
window.history.replaceState(null, null, window.location.pathname);
var sessionKey =   "<%= SwtUtil.generateNonce(request) %>";
var meta = document.createElement('meta');
meta.name = "csrf_token";
meta.content = sessionKey;
document.getElementsByTagName('head')[0].appendChild(meta);

var alertButtonLabels = [];
alertButtonLabels['ok'] = "<s:text name="button.ok"/>";
alertButtonLabels['yes'] = "<s:text name="button.yes"/>";
alertButtonLabels['no'] = "<s:text name="button.no"/>";
alertButtonLabels['cancel'] = "<s:text name="button.cancel"/>";
alertButtonLabels['include'] = "<s:text name="button.include"/>";
alertButtonLabels['exclude'] = "<s:text name="button.exclude"/>";
	
</script>


<script type="text/javascript" src="js/preloadtabs.js"></script>
<script type="text/javascript" src="js/commonJS.js"></script>
<script type="text/javascript" src="js/common.js"></script>
<script type="text/javascript" src="js/sortabletable.js"></script>
<script type="text/javascript" src="js/select.js"></script>
<script type="text/javascript" src="js/filter.js"></script>
<script type="text/javascript" src="js/dialog.js"></script>
<script type="text/javascript" src="js/excel.js"></script>
<script type="text/javascript" src="js/datavalidation.js"></script>
<script type="text/javascript" src="js/calendar.js"></script>
<script type="text/javascript" src="js/tabnew.js"></script>
<%-- Start: Added by RK on 13-Mar-2012 for AJAX support --%>
<script type="text/javascript" src="js/zxml.js"></script>
<%-- End: Added by RK on 13-Mar-2012 for AJAX support --%>
<script type="text/javascript" src="js/jquery-3.6.0.min.js"></script>
<script type="text/javascript" src="js/swt-jquery.extension.js"></script>
<script type="text/javascript" src="js/sweetalert2.all.min.js"></script>
<script type="text/javascript" src="js/moment.min.js"></script>
<script type="text/javascript" src="js/promise.min.js"></script>
<script type="text/javascript" src="js/<EMAIL>"></script>
<link rel="stylesheet" type="text/css" href="style/Style.css" >
<link rel="stylesheet" type="text/css" href="style/excel.css">
<link rel="stylesheet" type="text/css" href="style/sweetalert2.min.css">
<link rel="stylesheet" type="text/css" href="style/tabstylenew.css">
<link rel="stylesheet" type="text/css" href="style/calendar.css">
<script type="text/javascript">
$(document).ready(function(){
	$(':input').attr('autocomplete', 'off')
	});
</script>
<script> 
var b = document.documentElement;
b.setAttribute('data-useragent', navigator.userAgent);
b.setAttribute('data-platform', navigator.platform);
if(!isIEunder10){
	$('head').append('<link rel="stylesheet" href="style/ie10Style.css" type="text/css" />');
}

</script>
<%
	String titleSuffix = PropertiesFileLoader.getInstance().getPropertiesValue("windows.title.suffix"); 
	if(titleSuffix == null) {
		titleSuffix = "";
	}
	

boolean result = SwtUtil.hasAcessToFacility(request);
if(!result){
	CommonDataManager cdm = null;
	cdm = ((CommonDataManager) request.getSession().getAttribute(
			SwtConstants.CDM_BEAN));
	request.setAttribute("errordesc", 
			SwtUtil.getMessage("errors.authorization.attack", request));
	request.setAttribute("errorCause", "");
	Log log = LogFactory.getLog(XSSFilter.class);
	log.error(SwtUtil.getMessage("errors.authorization.attack.log", request) + "\n" +
			"{" + SwtUtil.getMessage("errors.user.log", request) + ": " + cdm.getUser().getId().getUserId() +
			", " + SwtUtil.getMessage("errors.ipAddress.log", request) + ": " + request.getRemoteAddr() +
			", " + SwtUtil.getMessage("errors.requestURI.log", request) + ": " + request.getAttribute("orginalURL") + 
			", Url Path : "+request.getAttribute("orginalURL") + "}");
	
	response.sendRedirect(request.getContextPath() + "/invalidRequest.jsp");
}
%>
<script>

function openAlertInstanceSummary(methodName, selectedNodeId, treeLevelValue){
	var param = '/' + appName + '/scenMaintenance.do?method='+methodName;
	param+='&selectedNodeId='+selectedNodeId;
	param+='&treeLevelValue='+treeLevelValue;
	var 	mainWindow = openWindow (param, 'openAlertInstSummary','left=10,top=230,width=1200,height=640,toolbar=0, resizable=yes, //status=yes, scrollbars=yes');	
	return false;
}
	/**
	 * This function returns title suffiz
	 *
	 * @return String - title suffix
	 */
	function getTitleSuffix() {
		return '<%= titleSuffix %>';
	}
	
	<%-- Start: Added by RK on 20-Mar-2012 for Mantis 1645 --%>
	/**
	 * This function returns url of the application
	 *
	 * @return String - url value
	 */
	function getUrl() {
		var appName = "<%= SwtUtil.appName %>";
		var requestURL = "<%= request.getRequestURL() %>";
		var idy = requestURL.indexOf('/' + appName + '/');
		return requestURL.substring(0, idy + 1) + appName;
	}
	<%-- End: Added by RK on 20-Mar-2012 for Mantis 1645 --%>
	
	/**
	 * method to open the facility screen related to the facility id of a selected scenario
	 **/
	function goTo(facilityID, hostID, entityID, matchIdKey, currencyCodeKey, movementIdKey, sweepIdKey,additionalParams){
		
		if (hostID == "" || entityID == "") {
			alert("<s:text name='alert.FacilityMissingValues'/>");
		} else {
			if (!facilityAccess(hostID, entityID, facilityID, currencyCodeKey))
				alert("<s:text name='alert.accessToFacility'/>");
			else{							
				var appName = "<%=SwtUtil.appName%>";
				var requestURL = new String('<%=request.getRequestURL()%>');
				var idy = requestURL.indexOf('/'+appName+'/');
				requestURL=requestURL.substring(0,idy+1) ; 
				requestURL = requestURL + appName+"/genericdisplay.do?method=getScreenDetails";
				requestURL = requestURL + "&facilityId=" + facilityID;
				var oXMLHTTP = new XMLHttpRequest();
				oXMLHTTP.open( "POST", requestURL, false );
				oXMLHTTP.send();
				var screenDetails=new String(oXMLHTTP.responseText);
				if(screenDetails == ""){
					return;
				}
				var screenDetailsList = screenDetails.split("<%=SwtConstants.SEPARATOR_SCREEN_DETAILS%>");
				var programName = screenDetailsList[0];
				if (programName.indexOf("?") == -1)
					programName += '?';
				var width = screenDetailsList[1];
				var height = screenDetailsList[2];
				
				var key = "&hostId=" + hostID + "&entityId="+ entityID +"&selectedEntityId=" + entityID;
				key += "&matchId=" + matchIdKey; 
				key += "&calledFrom=generic";
				key += "&currencyId=" + currencyCodeKey;
				key += "&selectedMovementId=" + movementIdKey;
				key += "&selectedSweepId=" + sweepIdKey;
				key +=getMenuWindow().decode64(additionalParams);
				openWindow(programName + key, 'facilityScreenWindow','left=0,top=55,width='+width+',height='+height+',toolbar=0, resizable=yes, scrollbars=yes,status=yes');
			}
		}
	}
	
	/**
	 * Return true or false, if the user has access to a facility screen or not
	 * 
	 * @param hostId
	 * @param entityId
	 * @param facilityId
	 * @param currencyCode
	 **/
	function facilityAccess(hostId, entityId, facilityId, currencyCode){


		var appName = "<%=SwtUtil.appName%>";
		var requestURL = new String('<%=request.getRequestURL()%>');
		var idy = requestURL.indexOf('/'+appName+'/');
		requestURL=requestURL.substring(0,idy+1) ; 
		requestURL = requestURL + appName+"/genericdisplay.do?method=getFacilityAccess";
		requestURL = requestURL + "&hostId=" + hostId;
		requestURL = requestURL + "&entityId=" + entityId;
		requestURL = requestURL + "&facilityId=" + facilityId;
		requestURL = requestURL + "&currencyCode=" + currencyCode;
		var oXMLHTTP = new XMLHttpRequest();
		oXMLHTTP.open( "POST", requestURL, false );
		oXMLHTTP.send();
		var access=new String(oXMLHTTP.responseText);
		return (parseInt(access) == <%=SwtConstants.FACILITY_NO_ACCESS%>) ? false: true;
	}

     function setStyleForBrowsers() {
    	 // For jQuery scripts
    	 (function($){
    	 if((navigator.userAgent.indexOf("rv:11.0") != -1) || (navigator.userAgent.indexOf("MSIE 10.0") != -1) || (navigator.userAgent.indexOf('Chrome') != -1) || (navigator.userAgent.indexOf('Firefox') != -1)){
			 //Added to show all the header (only a part of header was shown)
			 $("table[class='sort-table']:has(thead)").parent().css( "overflow", "hidden" );
			if(document.getElementById("exportReport"))
				
			$("table[class='sort-table']:has(thead)").parent().parent().css( "z-index", "0" );
			$("[name ='Help']").closest("div").css("zIndex","150");
			var headerHeight = new Array;
			var i = 0;
			jQuery(".sort-table").each(function() {
				$(this).parent().css("position",'');
				
				if($(this).has("thead")){
					var style = $(this).parent().attr('style');
					var regex = new RegExp("height(\\s)*:(\\s)*[0-9]+(\\s)*px(\\s)*!(\\s)*important"); 
					if (!regex.test(style)) {
						$(this).parent().css( "height", "23" );
					} 
					headerHeight[i] = $(this).parent().height();
					i++;
				}
				if($(this).has("tbody")){
					$(this).css("height",($(this).height()-3));
					if(isIEup10)
						$(this).css("white-space",'nowrap');
				}
				$(this).css("height",'0');
			});
			i = 0;
			$('div[id="ddscrolltable"]').each(function() {
				$(this).css("position",'');
				$(this).css("height",($(this).height()-headerHeight[i]));
				i++;
				if(navigator.userAgent.indexOf('Chrome') != -1){
					$(this).css("position",'');
				}
				this.parentNode.style.setProperty( 'border-right-width', '0px', 'important' );
				this.parentNode.style.setProperty( 'border-top-width', '0px', 'important' );
				this.parentNode.style.setProperty( 'border-bottom', '0px', 'important' );
				this.parentNode.style.setProperty( 'border-left', '2px double #FFFFFF', 'important' );
			});
		}
    	 })(jQuery);
     }
	
     

     function getTooltipTextValue(key) {
    	 try{
    	 return getMenuWindow().getMessage(key, null);
    	 }catch(e){
    		return "";
    	 }
     	}



     //Function to replace titleKey attributes with title attributes
     function replaceTitleKeysWithTooltips() {
         // Get all HTML elements with a titleKey attribute
         const elements = document.querySelectorAll('[titleKey]');
         // Loop through each element
         elements.forEach(element => {
             // Get the dynamic key value
             const key = element.getAttribute('titleKey');
             // Get the tooltip text using the getTooltipTextValue function
             const tooltipText = getTooltipTextValue(key);
             // Set the title attribute to the generated tooltip text
             element.setAttribute('title', tooltipText);
             // Remove the titleKey attribute
             element.removeAttribute('titleKey');
         });
     }
     
    /**
     * When document is ready, find each 'tr' that contains a 'td' which text ends with '*', 
     * then apply the style 'required' for each empty input   
     * Added by Saber Chebka
     * Updated by Med Amine Ben Ahmed
     **/
	var mandatoryFieldsArray="undefined";

	var dateFormat = '${CDM.dateFormatValue}'
	
	$(document).ready(function(){

  		// For jQuery scripts
  		(function($){
   		 $('<input>').attr('type','hidden').attr('value',sessionKey).attr('name','csrf').appendTo('form');
   		 $('<input>').attr('type','hidden').attr('value','true').attr('name','isForm').appendTo('form');

		
		setStyleForBrowsers();
		replaceTitleKeysWithTooltips();
		// Add disabled background color to all disabled inputs
	    $("input[disabled='disabled'][type='text'],input[disabled='disabled'][type='password'],textarea:disabled,select[disabled='disabled']")
								.css('background-color', '#e5e5e5') ;
							
		 // input type is text ==> add css class inputText
		$("input[type='text'],textarea,input[type='password'],input[disabled='']").not('[class*="textlabel1"],[class*="textlabel"]')
								.addClass('inputText');
		 
		$("input[type='text'],textarea,input[type='password'],input[disabled=''],input[type='hidden']").not('[class*="textlabel1"],[class*="textlabel"]').val(function( index, value ) {		
			return value.replace(/\\u0027/g, '\'').replace(/\\u0022/g, '"').replace(/\\u0028/g, '(').replace(/\\u0029/g, ')').replace(/\\u003C/g, '&lt;').replace(/\\u003E/g, '&gt;');
					});

		
	    if(mandatoryFieldsArray == (null||"undefined"))
		    return;
	    else if(mandatoryFieldsArray == "*"){
		 // Coloring according to content: Find all trs that one of their td ends or contains '*', then apply coloring according to content presence
	         $("tr:has(td:endsWith('*')),tr:has(th:endsWith('*'))").each(function() {
	        	// Get the current tr
	        	var tr = $(this);
	        	// Exclude logon pages inputs, and apply coloring function on inputs according to their content
				tr.find('input').not('[title*="Please enter your User ID"],[title*="Please enter your Password"],[disabled="disabled"]')
					.css('border-color',function(index)
						{
							if ($(this).val() == '')
							return 'red';
						}).on('blur keyup change', function()
	                        {
	                            if ($(this).val() == '')
	                                $(this).css('border-color','red');
	                            else
	                                $(this).css('border-color','');
	                        });
				
	
				// Find all text areas inside the current td, and apply coloring function (depending on its content)			
				tr.find('textarea').css('border-color',function(index) {
							if ($(this).val() == '')
							return 'red';
					  }).on('blur keyup change', function()
					  
	                        {    
	                           if ($(this).val() == '')
	                            $(this).css('border-color','red');
	                              else
	                            $(this).css('border-color','');
	                        });
				
			
				// Same for selects
			   if (tr.find('select').val() == ''){
					   tr.find('div').addClass(function(index){ 
							return 'divRed';
						}).change(function() 
							{
							 if (tr.find('select').val() == '')
	                            {
	                                tr.find('div').removeClass();
	                                tr.find('div').addClass('divRed');
	                            } else{
	                                tr.find('div').removeClass('divRed');
	                                tr.find('div').addClass('divNORed');
	                            }
	                        });
					}
	
			});

			}else{
				 jQuery.each(mandatoryFieldsArray , function(index, value){	
				    	var td= $("td[id="+value+"]");
				    		if( td != (null|| "undefined"))
					    	      td.find('input').css('border-color',function(index){
										if ($(this).val() == '')
											  return 'red';
										  }).on('blur keyup change', function() {
					                            if ($(this).val() == '')
					                                $(this).css('border-color','red');
					                            else
					                                $(this).css('border-color','');
					                        });
					    	   });
				 } 

			
			// Load common translated textes. Flex components that are on SwtToolBox should have their labels from main screen
			// Make sure variable label is already declared
			try {
			    label["text"]["label-refreshRate"] = "<s:text name="refreshRate.title.window"/>";
				label["tip"]["label-refreshRate"] = "<s:text name="tooltip.enterRate"/>";
				label["text"]["label-autoRefreshRate"] = "<s:text name="autoRefreshRate"/>";
				label["text"]["label-lastRefresh"] = "<s:text name="label.lastRefTime2"/>";

				label["text"]["button-valider"] = "<s:text name="button.forecastMonitor.ok"/>";
				label["tip"]["button-valider"] = "<s:text name="tooltip.forecastMonitor.ok"/>";
				label["tip"]["button-valider-refresh-rate"] = "<s:text name="tooltip.saveRefresh"/>";

				label["text"]["button-close"] = "<s:text name="button.close"/>";
				label["tip"]["button-close"] = "<s:text name="tooltip.closeWindow"/>";

				label["text"]["label-normal"] = "<s:text name="centralMonitorOptions.fontSizeNormal"/>";
				label["tip"]["label-normal"] = "<s:text name="tooltip.centralMonitorOptions.selectFontSizeNormal"/>";

				label["text"]["label-small"] = "<s:text name="centralMonitorOptions.fontSizeSmall"/>";
				label["tip"]["label-small"] = "<s:text name="tooltip.centralMonitorOptions.selectFontSizeSmall"/>";

				label["text"]["label-fontSize"] = "<s:text name="centralMonitorOptions.fontSize"/>";

				label["text"]["label-seconds"] = "<s:text name="label.refreshRate.seconds"/>";

				label["text"]["refreshRate-title"] = "<s:text name="refreshRate.title.window"/>";

				label["tip"]["button-help"] = "<s:text name="tooltip.help"/>";

				label["text"]["button-screanVersion"] = "<s:text name="screan.screanVersion"/>";

				label["text"]["label-showXML"] = "<s:text name="screen.showXML"/>";

				label["text"]["button-refreshRate"] = "<s:text name="workflowmonitor.refreshRate"/>";

				label["text"]["label-error"] = "<s:text name="screen.error"/>";
				
				label["text"]["label-noRecords"] = "<s:text name="ilmAccountGroupDetails.nbOfRecords"/>";
				
                label["text"]["label-unableSave"] = "<s:text name="label.unableSave"/>";
				
				label["text"]["label-notValidParent"] = "<s:text name="screen.notValidParent"/>";
				
				label["text"]["comboBox-invalidValue"] = "<s:text name="alert.comboBox.invalidValue"/>";
				label["text"]["label-currentPage"] = "<s:text name="label.currentPage"/>";
				label["text"]["label-exportMultiPages"] = "<s:text name="label.exportMultiPages"/>";
				label["text"]["alert-noValidParent"] = "<s:text name="alert.noValidParent"/>";
				label["text"]["alert-noValideTime"] = "<s:text name="alert.interfaceSettings.time"/>";
				label["text"]["alert-microsoftInternetExplorer"] = "<s:text name="alert.microsoftInternetExplorer"/>";
				label["text"]["alert-toTimeGreaterThanFromTimethe"] = "<s:text name="alert.toTimeGreaterThanFromTimethe"/>";

			} catch (error) {
		        // Ignore error due to compiling a non existing variable "label"
		 }
  		})(jQuery);

   		

});
</script>

<style type="text/css">
      .inputText
       {
        border-color: #a9a9a9;
	   	border-style:solid; 
	   	border-width:1px;
		font-family: verdana,helvetica;
		font-size: 9pt;
		resize:none !important;
       }
       
      .divRed
	   {
		border-bottom : red 1px solid;
		border-top : red 1px solid;
		border-left : red 1px solid;
		border-right : red 1px solid;
        }
      .divNoRed
	    { 
		border-bottom : transparent 1px solid;
		border-top : transparent 1px solid;
		border-left : transparent 1px solid;
		border-right : transparent 1px solid;
	    }
	    
      .is-disabled 
       {
		background-color: #e5e5e5 ;
	    border-color: #a9a9a9;
		border-style:solid; 
		border-width:1px;
	    }
	  
</style>