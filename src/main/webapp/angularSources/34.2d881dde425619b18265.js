(window.webpackJsonp=window.webpackJsonp||[]).push([[34],{"4YA6":function(t,e,i){"use strict";i.r(e);var n=i("CcnG"),o=i("mrSG"),a=i("447K"),s=i("ZYCi"),l=function(t){function e(e,i){var n=t.call(this,i,e)||this;return n.commonService=e,n.element=i,n.jsonReader=new a.L,n.menuAccessId=0,n.menuAccess="",n.attributeId=null,n.systemFlag=null,n.versionNumber="1.0",n.versionDate="04/11/2019",n.inputData=new a.G(n.commonService),n.requestParams=[],n.baseURL=a.Wb.getBaseURL(),n.actionMethod="",n.actionPath="",n.invalidComms="",n.closeWindow=!1,n.errorLocation=0,n.moduleId="Predict",n.screenVersion=new a.V(n.commonService),n.swtAlert=new a.bb(e),n}return o.d(e,t),e.prototype.ngOnDestroy=function(){instanceElement=null},e.prototype.ngOnInit=function(){instanceElement=this,this.screenName=a.Wb.getPredictMessage("label.accountattributedefinition.title.window",null),this.addButton.label=a.Wb.getPredictMessage("button.add",null),this.changeButton.label=a.Wb.getPredictMessage("button.change",null),this.viewButton.label=a.Wb.getPredictMessage("button.view",null),this.deleteButton.label=a.Wb.getPredictMessage("button.delete",null),this.closeButton.label=a.Wb.getPredictMessage("button.close",null),this.addButton.setFocus()},e.prototype.onLoad=function(){var t=this;try{this.accountAttributeGrid=this.canvasGrid.addChild(a.hb),this.accountAttributeGrid.uniqueColumn="attributeid",this.accountAttributeGrid.onRowClick=function(e){t.cellClickEventHandler(e)},this.menuAccess=a.x.call("eval","menuAccessId"),this.menuAccess&&(""!==this.menuAccess?this.menuAccessId=Number(this.menuAccess):this.menuAccessId=2),0!==this.menuAccessId&&(this.addButton.enabled=!1),this.initializeMenus(),this.inputData.cbStart=this.startOfComms.bind(this),this.inputData.cbStop=this.endOfComms.bind(this),this.inputData.cbResult=function(e){t.inputDataResult(e)},this.inputData.cbFault=this.inputDataFault.bind(this),this.inputData.encodeURL=!1,this.actionMethod="method=displayAcctAttributeDefinition",this.actionPath="accountAttribute.do?",this.requestParams=[],this.inputData.url=this.baseURL+this.actionPath+this.actionMethod,this.inputData.send(this.requestParams)}catch(e){console.log(e,this.moduleId,"AttributeDefinition","onLoad")}},e.prototype.initializeMenus=function(){this.screenVersion.loadScreenVersion(this,"Account Attribute Definition Screen",this.versionNumber,this.versionDate);var t=new a.n("Show JSON");t.MenuItemSelect=this.showGridJSON.bind(this),this.screenVersion.svContextMenu.customItems.push(t),this.contextMenu=this.screenVersion.svContextMenu},e.prototype.showGridJSON=function(t){this.showJSONPopup=a.Eb.createPopUp(this,a.M,{jsonData:this.lastReceivedJSON}),this.showJSONPopup.width="700",this.showJSONPopup.title="Last Received JSON",this.showJSONPopup.height="500",this.showJSONPopup.enableResize=!1,this.showJSONPopup.showControls=!0,this.showJSONPopup.display()},e.prototype.inputDataRefresh=function(t){this.lastReceivedJSON=t,this.jsonReader.setInputJSON(this.lastReceivedJSON),this.jsonReader.getRequestReplyStatus()?(this.accountAttributeGrid.gridData=this.jsonReader.getGridData(),this.accountAttributeGrid.setRowSize=this.accountAttributeGrid.gridData.length,this.accountAttributeGrid.selectedIndex=-1,this.changeButton.enabled=!1,this.deleteButton.enabled=!1,this.viewButton.enabled=!1):this.swtAlert.error(this.jsonReader.getRequestReplyMessage()+"\n"+this.jsonReader.getRequestReplyLocation(),a.Wb.getPredictMessage("screen.error",null),a.c.OK,this,this.errorHandler)},e.prototype.inputDataResult=function(t){try{if(this.inputData.isBusy())this.inputData.cbStop();else if(this.lastReceivedJSON=t,this.jsonReader.setInputJSON(this.lastReceivedJSON),JSON.stringify(this.lastReceivedJSON)!==JSON.stringify(this.prevReceivedJSON))if(this.jsonReader.getRequestReplyStatus()){if(!this.jsonReader.isDataBuilding()){this.accountAttributeGrid.colWidthURL(this.baseURL+"accountAttribute.do?&screenName=accountAttributeDefinition"),this.accountAttributeGrid.colOrderURL(this.baseURL+"accountAttribute.do?&screenName=accountAttributeDefinition"),this.accountAttributeGrid.saveWidths=!0,this.accountAttributeGrid.saveColumnOrder=!0;var e={columns:this.jsonReader.getColumnData()};this.accountAttributeGrid.doubleClickEnabled=!0,this.accountAttributeGrid.CustomGrid(e),this.jsonReader.getGridData()&&this.jsonReader.getGridData().size>0?(this.accountAttributeGrid.gridData=this.jsonReader.getGridData(),this.accountAttributeGrid.setRowSize=this.jsonReader.getRowSize(),this.accountAttributeGrid.refreshFilters()):(this.accountAttributeGrid.dataProvider=null,this.accountAttributeGrid.selectedIndex=-1)}this.prevReceivedJSON=this.lastReceivedJSON}else this.lastReceivedJSON.hasOwnProperty("request_reply")&&"true"==this.lastReceivedJSON.request_reply.closewindow&&(this.closeWindow=!0),this.swtAlert.error(this.jsonReader.getRequestReplyMessage()+"/n"+this.jsonReader.getRequestReplyLocation(),a.Wb.getPredictMessage("screen.error",null),a.c.OK,this,this.errorHandler)}catch(i){a.Wb.logError(i,this.moduleId,"AttributeDefinition","inputDataResult",this.errorLocation)}},e.prototype.inputDataFault=function(t){try{this.invalidComms=t.fault.faultString+"\n"+t.fault.faultCode+"\n"+t.fault.faultDetail,this.swtAlert.error("fault "+this.invalidComms)}catch(e){a.Wb.logError(e,this.moduleId,"AttributeDefinition","inputDataFault",this.errorLocation)}},e.prototype.updateData=function(){var t=this;try{this.requestParams=[],this.actionMethod="method=displayAcctAttributeDefinition",this.actionPath="accountAttribute.do?",this.inputData.cbResult=function(e){t.inputDataResult(e)},this.inputData.url=this.baseURL+this.actionPath+this.actionMethod,this.inputData.send(this.requestParams),this.accountAttributeGrid.refresh(),this.disableOrEnableButtons(!1)}catch(e){a.Wb.logError(e,this.moduleId,"AttributeDefinition","updateData",this.errorLocation)}},e.prototype.refreshdetails=function(){var t=this;this.menuAccessId=a.x.call("eval","menuAccessId"),0!=this.menuAccessId&&(this.addButton.enabled=!1),this.inputData.cbStart=this.startOfComms.bind(this),this.inputData.cbStop=this.endOfComms.bind(this),this.inputData.cbResult=function(e){t.inputDataRefresh(e)},this.inputData.cbFault=this.inputDataFault.bind(this),this.inputData.encodeURL=!1,this.actionPath="accountAttribute.do?",this.actionMethod="method=displayAcctAttributeDefinition",this.accountAttributeGrid.onRowClick=function(e){t.cellClickEventHandler(e)},this.inputData.url=this.baseURL+this.actionPath+this.actionMethod,this.inputData.send(this.requestParams)},e.prototype.addAccountAttributeHDR=function(){this.actionMethod="displayAddAccountAttributeHDR",a.x.call("openChildWindow",this.actionMethod)},e.prototype.changeViewAcctAttributeHDR=function(t){try{this.actionMethod="displayChangeOrViewAccountAttributeHDR",this.actionMethod=this.actionMethod+"&attributeId="+a.Z.encode64(this.attributeId),this.screenName=t?"changeScreen":"viewScreen",this.actionMethod=this.actionMethod+"&screenName="+this.screenName,a.x.call("openChildWindow",this.actionMethod)}catch(e){a.Wb.logError(e,this.moduleId,"AttributeDefinition","changeViewAcctAttributeHDR",this.errorLocation)}},e.prototype.getParamsFromParent=function(){var t=[];if("addScreen"==this.screenName)t=[{screenName:this.screenName,type:"Numeric"}];else{var e=this.accountAttributeGrid.selectedItem?this.accountAttributeGrid.selectedItem.type.content:"Numeric";t=[{screenName:this.screenName,type:e}]}return t},e.prototype.deleteAcctAttributeHDR=function(){try{this.swtAlert.confirm(a.Wb.getPredictMessage("alert.accountattributehdr.acctdelete",null),a.Wb.getPredictMessage("button.confirm",null),a.c.YES|a.c.NO,null,this.deleteAccountAttribute.bind(this),null)}catch(t){this.swtAlert.error("deleteAcctAttributeHDR: "+t),a.Wb.logError(t,this.moduleId,"AttributeDefinition","deleteAcctAttributeHDR",this.errorLocation)}},e.prototype.deleteAccountAttribute=function(t){try{t.detail===a.c.YES&&this.swtAlert.confirm(a.Wb.getPredictMessage("alert.accountattribute.acctdelete",null),a.Wb.getPredictMessage("alert.deletion.confirm",null),a.c.YES|a.c.NO,null,this.deletionDecision.bind(this),null)}catch(e){a.Wb.logError(e,this.moduleId,"AttributeDefinition","deleteCurrency",this.errorLocation)}},e.prototype.deletionDecision=function(t){var e=this;if(this.inputData.cbStart=this.startOfComms.bind(this),this.inputData.cbStop=this.endOfComms.bind(this),this.inputData.cbResult=function(t){e.inputDataRefresh(t)},this.requestParams=[],this.inputData.cbFault=this.inputDataFault.bind(this),this.inputData.encodeURL=!1,this.actionMethod="method=deleteAccountAttributeHDR",this.requestParams.attributeId=this.attributeId,t.detail==a.c.YES)this.requestParams.totalDelete="true";else{if(t.detail!=a.c.NO)return;this.requestParams.totalDelete="false"}this.inputData.url=this.baseURL+this.actionPath+this.actionMethod,this.inputData.send(this.requestParams)},e.prototype.closeHandler=function(){a.x.call("close")},e.prototype.startOfComms=function(){try{this.loadingImage.setVisible(!0)}catch(t){a.Wb.logError(t,this.moduleId,"AttributeDefinition","startOfComms",this.errorLocation)}},e.prototype.endOfComms=function(){try{this.loadingImage.setVisible(!1)}catch(t){a.Wb.logError(t,this.moduleId,"AttributeDefinition","endOfComms",this.errorLocation)}},e.prototype.doHelp=function(){try{a.x.call("help")}catch(t){a.Wb.logError(t,this.moduleId,"AttributeDefinition","doHelp",this.errorLocation)}},e.prototype.cellClickEventHandler=function(t){try{this.accountAttributeGrid.selectedIndex>-1&&this.accountAttributeGrid.selectable?(this.attributeId=this.accountAttributeGrid.selectedItem.attributeid.content,this.systemFlag=this.accountAttributeGrid.selectedItem.systemFlag.content,"N"===this.systemFlag?this.disableOrEnableButtons(!0):"Y"===this.systemFlag&&(this.changeButton.enabled=!1,this.deleteButton.enabled=!1,this.viewButton.enabled=!0)):this.disableOrEnableButtons(!1)}catch(e){a.Wb.logError(e,this.moduleId,"AttributeDefinition","cellClickEventHandler",this.errorLocation)}},e.prototype.disableOrEnableButtons=function(t){t?(this.enableChangeButton(0==this.menuAccessId),this.enableDeleteButton(0==this.menuAccessId),this.enableViewButton(this.menuAccessId<2)):(this.enableChangeButton(!1),this.enableViewButton(!1),this.enableDeleteButton(!1))},e.prototype.enableChangeButton=function(t){this.changeButton.enabled=t,this.changeButton.buttonMode=t},e.prototype.enableViewButton=function(t){this.viewButton.enabled=t,this.viewButton.buttonMode=t},e.prototype.enableDeleteButton=function(t){this.deleteButton.enabled=t,this.deleteButton.buttonMode=t},e.prototype.errorHandler=function(t){t.detail==a.c.OK&&this.closeWindow&&(this.closeWindow=!1)},e.prototype.hideShowColumn=function(t){},e}(a.yb),c=[{path:"",component:l}],u=(s.l.forChild(c),function(){return function(){}}()),r=i("pMnS"),d=i("RChO"),h=i("t6HQ"),b=i("WFGK"),p=i("5FqG"),m=i("Ip0R"),g=i("gIcY"),R=i("t/Na"),f=i("sE5F"),A=i("OzfB"),D=i("T7CS"),w=i("S7LP"),v=i("6aHO"),y=i("WzUx"),B=i("A7o+"),C=i("zCE2"),S=i("Jg5P"),I=i("3R0m"),O=i("hhbb"),M=i("5rxC"),k=i("Fzqc"),N=i("21Lb"),G=i("hUWP"),L=i("3pJQ"),P=i("V9q+"),J=i("VDKW"),W=i("kXfT"),H=i("BGbe");i.d(e,"AttributeDefinitionModuleNgFactory",function(){return E}),i.d(e,"RenderType_AttributeDefinition",function(){return F}),i.d(e,"View_AttributeDefinition_0",function(){return x}),i.d(e,"View_AttributeDefinition_Host_0",function(){return T}),i.d(e,"AttributeDefinitionNgFactory",function(){return q});var E=n.Gb(u,[],function(t){return n.Qb([n.Rb(512,n.n,n.vb,[[8,[r.a,d.a,h.a,b.a,p.Cb,p.Pb,p.r,p.rc,p.s,p.Ab,p.Bb,p.Db,p.qd,p.Hb,p.k,p.Ib,p.Nb,p.Ub,p.yb,p.Jb,p.v,p.A,p.e,p.c,p.g,p.d,p.Kb,p.f,p.ec,p.Wb,p.bc,p.ac,p.sc,p.fc,p.lc,p.jc,p.Eb,p.Fb,p.mc,p.Lb,p.nc,p.Mb,p.dc,p.Rb,p.b,p.ic,p.Yb,p.Sb,p.kc,p.y,p.Qb,p.cc,p.hc,p.pc,p.oc,p.xb,p.p,p.q,p.o,p.h,p.j,p.w,p.Zb,p.i,p.m,p.Vb,p.Ob,p.Gb,p.Xb,p.t,p.tc,p.zb,p.n,p.qc,p.a,p.z,p.rd,p.sd,p.x,p.td,p.gc,p.l,p.u,p.ud,p.Tb,q]],[3,n.n],n.J]),n.Rb(4608,m.m,m.l,[n.F,[2,m.u]]),n.Rb(4608,g.c,g.c,[]),n.Rb(4608,g.p,g.p,[]),n.Rb(4608,R.j,R.p,[m.c,n.O,R.n]),n.Rb(4608,R.q,R.q,[R.j,R.o]),n.Rb(5120,R.a,function(t){return[t,new a.tb]},[R.q]),n.Rb(4608,R.m,R.m,[]),n.Rb(6144,R.k,null,[R.m]),n.Rb(4608,R.i,R.i,[R.k]),n.Rb(6144,R.b,null,[R.i]),n.Rb(4608,R.f,R.l,[R.b,n.B]),n.Rb(4608,R.c,R.c,[R.f]),n.Rb(4608,f.c,f.c,[]),n.Rb(4608,f.g,f.b,[]),n.Rb(5120,f.i,f.j,[]),n.Rb(4608,f.h,f.h,[f.c,f.g,f.i]),n.Rb(4608,f.f,f.a,[]),n.Rb(5120,f.d,f.k,[f.h,f.f]),n.Rb(5120,n.b,function(t,e){return[A.j(t,e)]},[m.c,n.O]),n.Rb(4608,D.a,D.a,[]),n.Rb(4608,w.a,w.a,[]),n.Rb(4608,v.a,v.a,[n.n,n.L,n.B,w.a,n.g]),n.Rb(4608,y.c,y.c,[n.n,n.g,n.B]),n.Rb(4608,y.e,y.e,[y.c]),n.Rb(4608,B.l,B.l,[]),n.Rb(4608,B.h,B.g,[]),n.Rb(4608,B.c,B.f,[]),n.Rb(4608,B.j,B.d,[]),n.Rb(4608,B.b,B.a,[]),n.Rb(4608,B.k,B.k,[B.l,B.h,B.c,B.j,B.b,B.m,B.n]),n.Rb(4608,y.i,y.i,[[2,B.k]]),n.Rb(4608,y.r,y.r,[y.L,[2,B.k],y.i]),n.Rb(4608,y.t,y.t,[]),n.Rb(4608,y.w,y.w,[]),n.Rb(1073742336,s.l,s.l,[[2,s.r],[2,s.k]]),n.Rb(1073742336,m.b,m.b,[]),n.Rb(1073742336,g.n,g.n,[]),n.Rb(1073742336,g.l,g.l,[]),n.Rb(1073742336,C.a,C.a,[]),n.Rb(1073742336,S.a,S.a,[]),n.Rb(1073742336,g.e,g.e,[]),n.Rb(1073742336,I.a,I.a,[]),n.Rb(1073742336,B.i,B.i,[]),n.Rb(1073742336,y.b,y.b,[]),n.Rb(1073742336,R.e,R.e,[]),n.Rb(1073742336,R.d,R.d,[]),n.Rb(1073742336,f.e,f.e,[]),n.Rb(1073742336,O.b,O.b,[]),n.Rb(1073742336,M.b,M.b,[]),n.Rb(1073742336,A.c,A.c,[]),n.Rb(1073742336,k.a,k.a,[]),n.Rb(1073742336,N.d,N.d,[]),n.Rb(1073742336,G.c,G.c,[]),n.Rb(1073742336,L.a,L.a,[]),n.Rb(1073742336,P.a,P.a,[[2,A.g],n.O]),n.Rb(1073742336,J.b,J.b,[]),n.Rb(1073742336,W.a,W.a,[]),n.Rb(1073742336,H.b,H.b,[]),n.Rb(1073742336,a.Tb,a.Tb,[]),n.Rb(1073742336,u,u,[]),n.Rb(256,R.n,"XSRF-TOKEN",[]),n.Rb(256,R.o,"X-XSRF-TOKEN",[]),n.Rb(256,"config",{},[]),n.Rb(256,B.m,void 0,[]),n.Rb(256,B.n,void 0,[]),n.Rb(256,"popperDefaults",{},[]),n.Rb(1024,s.i,function(){return[[{path:"",component:l}]]},[])])}),_=[[""]],F=n.Hb({encapsulation:0,styles:_,data:{}});function x(t){return n.dc(0,[n.Zb(402653184,1,{_container:0}),n.Zb(402653184,2,{canvasGrid:0}),n.Zb(402653184,3,{loadingImage:0}),n.Zb(402653184,4,{addButton:0}),n.Zb(402653184,5,{changeButton:0}),n.Zb(402653184,6,{viewButton:0}),n.Zb(402653184,7,{deleteButton:0}),n.Zb(402653184,8,{closeButton:0}),(t()(),n.Jb(8,0,null,null,27,"SwtModule",[["height","100%"],["width","100%"]],null,[[null,"creationComplete"]],function(t,e,i){var n=!0,o=t.component;"creationComplete"===e&&(n=!1!==o.onLoad()&&n);return n},p.ad,p.hb)),n.Ib(9,4440064,null,0,a.yb,[n.r,a.i],{width:[0,"width"],height:[1,"height"]},{creationComplete:"creationComplete"}),(t()(),n.Jb(10,0,null,0,25,"VBox",[["height","100%"],["paddingBottom","5"],["paddingLeft","5"],["paddingRight","5"],["paddingTop","5"],["width","100%"]],null,null,null,p.od,p.vb)),n.Ib(11,4440064,null,0,a.ec,[n.r,a.i,n.T],{width:[0,"width"],height:[1,"height"],paddingTop:[2,"paddingTop"],paddingBottom:[3,"paddingBottom"],paddingLeft:[4,"paddingLeft"],paddingRight:[5,"paddingRight"]},null),(t()(),n.Jb(12,0,null,0,1,"SwtCanvas",[["height","90%"],["id","canvasGrid"],["width","100%"]],null,null,null,p.Nc,p.U)),n.Ib(13,4440064,[[2,4],["canvasGrid",4]],0,a.db,[n.r,a.i],{id:[0,"id"],width:[1,"width"],height:[2,"height"]},null),(t()(),n.Jb(14,0,null,0,21,"SwtCanvas",[["height","35"],["id","canvasButtons"],["width","100%"]],null,null,null,p.Nc,p.U)),n.Ib(15,4440064,null,0,a.db,[n.r,a.i],{id:[0,"id"],width:[1,"width"],height:[2,"height"]},null),(t()(),n.Jb(16,0,null,0,19,"HBox",[["width","100%"]],null,null,null,p.Dc,p.K)),n.Ib(17,4440064,null,0,a.C,[n.r,a.i],{width:[0,"width"]},null),(t()(),n.Jb(18,0,null,0,11,"HBox",[["paddingLeft","5"],["width","100%"]],null,null,null,p.Dc,p.K)),n.Ib(19,4440064,null,0,a.C,[n.r,a.i],{width:[0,"width"],paddingLeft:[1,"paddingLeft"]},null),(t()(),n.Jb(20,0,null,0,1,"SwtButton",[["id","addButton"]],null,[[null,"click"]],function(t,e,i){var n=!0,o=t.component;"click"===e&&(n=!1!==o.addAccountAttributeHDR()&&n);return n},p.Mc,p.T)),n.Ib(21,4440064,[[4,4],["addButton",4]],0,a.cb,[n.r,a.i],{id:[0,"id"],buttonMode:[1,"buttonMode"]},{onClick_:"click"}),(t()(),n.Jb(22,0,null,0,1,"SwtButton",[["enabled","false"],["id","changeButton"]],null,[[null,"click"]],function(t,e,i){var n=!0,o=t.component;"click"===e&&(n=!1!==o.changeViewAcctAttributeHDR(!0)&&n);return n},p.Mc,p.T)),n.Ib(23,4440064,[[5,4],["changeButton",4]],0,a.cb,[n.r,a.i],{id:[0,"id"],enabled:[1,"enabled"],buttonMode:[2,"buttonMode"]},{onClick_:"click"}),(t()(),n.Jb(24,0,null,0,1,"SwtButton",[["enabled","false"],["id","viewButton"]],null,[[null,"click"]],function(t,e,i){var n=!0,o=t.component;"click"===e&&(n=!1!==o.changeViewAcctAttributeHDR(!1)&&n);return n},p.Mc,p.T)),n.Ib(25,4440064,[[6,4],["viewButton",4]],0,a.cb,[n.r,a.i],{id:[0,"id"],enabled:[1,"enabled"],buttonMode:[2,"buttonMode"]},{onClick_:"click"}),(t()(),n.Jb(26,0,null,0,1,"SwtButton",[["id","deleteButton"]],null,[[null,"click"]],function(t,e,i){var n=!0,o=t.component;"click"===e&&(n=!1!==o.deleteAcctAttributeHDR()&&n);return n},p.Mc,p.T)),n.Ib(27,4440064,[[7,4],["deleteButton",4]],0,a.cb,[n.r,a.i],{id:[0,"id"],buttonMode:[1,"buttonMode"]},{onClick_:"click"}),(t()(),n.Jb(28,0,null,0,1,"SwtButton",[["id","closeButton"]],null,[[null,"click"]],function(t,e,i){var n=!0,o=t.component;"click"===e&&(n=!1!==o.closeHandler()&&n);return n},p.Mc,p.T)),n.Ib(29,4440064,[[8,4],["closeButton",4]],0,a.cb,[n.r,a.i],{id:[0,"id"],buttonMode:[1,"buttonMode"]},{onClick_:"click"}),(t()(),n.Jb(30,0,null,0,5,"HBox",[["horizontalAlign","right"],["paddingTop","2"]],null,null,null,p.Dc,p.K)),n.Ib(31,4440064,null,0,a.C,[n.r,a.i],{horizontalAlign:[0,"horizontalAlign"],paddingTop:[1,"paddingTop"]},null),(t()(),n.Jb(32,0,null,0,1,"SwtHelpButton",[["enabled","true"],["helpFile","groups-of-rules"],["id","helpIcon"]],null,[[null,"click"]],function(t,e,i){var n=!0,o=t.component;"click"===e&&(n=!1!==o.doHelp()&&n);return n},p.Wc,p.db)),n.Ib(33,4440064,null,0,a.rb,[n.r,a.i],{id:[0,"id"],enabled:[1,"enabled"],buttonMode:[2,"buttonMode"],helpFile:[3,"helpFile"]},{onClick_:"click"}),(t()(),n.Jb(34,0,null,0,1,"SwtLoadingImage",[],null,null,null,p.Zc,p.gb)),n.Ib(35,114688,[[3,4],["loadingImage",4]],0,a.xb,[n.r],null,null)],function(t,e){t(e,9,0,"100%","100%");t(e,11,0,"100%","100%","5","5","5","5");t(e,13,0,"canvasGrid","100%","90%");t(e,15,0,"canvasButtons","100%","35");t(e,17,0,"100%");t(e,19,0,"100%","5");t(e,21,0,"addButton",!0);t(e,23,0,"changeButton","false",!0);t(e,25,0,"viewButton","false",!0);t(e,27,0,"deleteButton",!0);t(e,29,0,"closeButton",!0);t(e,31,0,"right","2");t(e,33,0,"helpIcon","true",!0,"groups-of-rules"),t(e,35,0)},null)}function T(t){return n.dc(0,[(t()(),n.Jb(0,0,null,null,1,"app-attribute-definition",[],null,null,null,x,F)),n.Ib(1,4440064,null,0,l,[a.i,n.r],null,null)],function(t,e){t(e,1,0)},null)}var q=n.Fb("app-attribute-definition",l,T,{maxChars:"maxChars",restrict:"restrict",id:"id",dropShadowEnabled:"dropShadowEnabled",cornerRadius:"cornerRadius",borderThickness:"borderThickness",borderStyle:"borderStyle",borderColor:"borderColor",backGroundColor:"backGroundColor",right:"right",left:"left",bottom:"bottom",top:"top",horizontalGap:"horizontalGap",verticalGap:"verticalGap",textAlign:"textAlign",toolTip:"toolTip",toolTipPreviousValue:"toolTipPreviousValue",textDictionaryId:"tooltipDictionaryId",name:"name",styleName:"styleName",horizontalAlign:"horizontalAlign",verticalAlign:"verticalAlign",width:"width",showScrollBar:"showScrollBar",height:"height",minHeight:"minHeight",minWidth:"minWidth",maxHeight:"maxHeight",maxWidth:"maxWidth",includeInLayout:"includeInLayout",visible:"visible",enabled:"enabled",paddingTop:"paddingTop",paddingBottom:"paddingBottom",paddingLeft:"paddingLeft",paddingRight:"paddingRight",marginTop:"marginTop",marginBottom:"marginBottom",marginLeft:"marginLeft",marginRight:"marginRight",contextMenu:"contextMenu"},{onClick_:"click",dbClick_:"dbClick",doubleClick_:"doubleClick",itemDoubleClick_:"itemDoubleClick",onKeyDown_:"keyDown",onKeyUp_:"keyUp",mouseUp_:"mouseUp",mouseOver_:"mouseOver",mouseDown_:"mouseDown",mouseEnter_:"mouseEnter",mouseLeave_:"mouseLeave",mouseOut_:"mouseOut",mouseIn_:"mouseIn",mouseMove_:"mouseMove",focus_:"focus",focusIn_:"focusIn",onFocusOut_:"focusOut",keyFocusChange_:"keyFocusChange",change_:"change",onSpyChange:"onSpyChange",onSpyNoChange:"onSpyNoChange",scroll_:"scroll",creationComplete:"creationComplete",preinitialize:"preinitialize"},[])}}]);