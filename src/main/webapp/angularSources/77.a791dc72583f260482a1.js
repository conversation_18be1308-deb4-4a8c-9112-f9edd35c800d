(window.webpackJsonp=window.webpackJsonp||[]).push([[77],{nA5d:function(e,t,i){"use strict";i.r(t);var l=i("CcnG"),n=i("mrSG"),o=i("447K"),a=i("ZYCi"),r=function(e){function t(t,i){var l=e.call(this,i,t)||this;return l.commonService=t,l.element=i,l.baseURL=o.Wb.getBaseURL(),l.actionMethod="",l.actionPath="",l.requestParams=[],l.screenVersion=new o.V(l.commonService),l.moduleId="",l.userTemplateGrid=null,l.userBucketGrid=null,l.jsonReader=new o.L,l.inputData=new o.G(l.commonService),l.entityData=new o.G(l.commonService),l.saveData=new o.G(l.commonService),l.invalidComms="",l.lastIndex=-1,l.closeWindow=!1,l.screenName="Forecast Monitor Options",l.versionNumber="1.1.0002",l.releaseDate="20 May 2019",l.update=new Object,l.menuAccessIdParent=0,l.menuAccess=2,l.templateData=[],l.prevSelectedIndex=-1,l.swtAlert=new o.bb(t),window.Main=l,l}return n.d(t,e),t.prototype.ngOnDestroy=function(){instanceElement=null},t.prototype.ngOnInit=function(){instanceElement=this,this.userTemplateGrid=this.cvTemplateGridContainer.addChild(o.hb),this.userBucketGrid=this.cvBucketGridContainer.addChild(o.hb),this.userBucketGrid.editable=!0,this.entityLabel.text=o.Wb.getPredictMessage("label.forecastMonitor.entity",null),this.cbEntity.toolTip=o.Wb.getPredictMessage("tooltip.forecastMonitor.selectEntity",null),this.currencyLabel.text=o.Wb.getPredictMessage("label.forecastMonitor.currency",null),this.cbCurrency.toolTip=o.Wb.getPredictMessage("tooltip.forecastMonitor.selectCurrency",null),this.multiplier.label=o.Wb.getPredictMessage("label.forecastMonitorOptions.applyCurrencyMultiplier",null),this.multiplier.toolTip=o.Wb.getPredictMessage("tooltip.forecastMonitorOptions.applyCurrencyMultiplier",null),this.hideWeekend.label=o.Wb.getPredictMessage("label.forecastMonitorOptions.hideweekend",null),this.hideWeekend.toolTip=o.Wb.getPredictMessage("tooltip.forecastMonitorOptions.hideweekend",null),this.cumulativeTotal.label=o.Wb.getPredictMessage("label.forecastMonitorOptions.cumulativetotal",null),this.cumulativeTotal.toolTip=o.Wb.getPredictMessage("tooltip.forecastMonitorOptions.cumulativetotal",null),this.hideScenario.label=o.Wb.getPredictMessage("label.forecastMonitorOptions.hidescenario",null),this.hideScenario.toolTip=o.Wb.getPredictMessage("tooltip.forecastMonitorOptions.hidescenario",null),this.hideAssumption.toolTip=o.Wb.getPredictMessage("label.forecastMonitorOptions.hideassumption",null),this.hideAssumption.label=o.Wb.getPredictMessage("tooltip.forecastMonitorOptions.hideassumption",null),this.hideZeroValue.label=o.Wb.getPredictMessage("label.forecastMonitorOptions.hidezerovalue",null),this.hideZeroValue.toolTip=o.Wb.getPredictMessage("tooltip.forecastMonitorOptions.hidezerovalue",null),this.hideZeroSum.label=o.Wb.getPredictMessage("label.forecastMonitorOptions.hidezerosum",null),this.hideZeroSum.toolTip=o.Wb.getPredictMessage("tooltip.forecastMonitorOptions.hidezerosum",null),this.hideTotal.label=o.Wb.getPredictMessage("label.forecastMonitorOptions.hidetotal",null),this.hideTotal.toolTip=o.Wb.getPredictMessage("label.forecastMonitorOptions.hidetotal",null),this.btnCancel.label=o.Wb.getPredictMessage("button.forecastMonitor.cancel",null),this.btnCancel.toolTip=o.Wb.getPredictMessage("tooltip.forecastMonitor.cancel",null),this.btnSave.label=o.Wb.getPredictMessage("button.forecastMonitor.save",null),this.btnSave.toolTip=o.Wb.getPredictMessage("tooltip.forecastMonitor.save",null),this.btnTemplateAdd.label=o.Wb.getPredictMessage("button.forecastMonitor.add",null),this.btnTemplateAdd.toolTip=o.Wb.getPredictMessage("tooltip.forecastMonitor.add",null),this.btnTemplateChange.label=o.Wb.getPredictMessage("button.forecastMonitor.change",null),this.btnTemplateChange.toolTip=o.Wb.getPredictMessage("tooltip.forecastMonitor.change",null),this.btnTemplateDelete.label=o.Wb.getPredictMessage("button.forecastMonitor.delete",null),this.btnTemplateDelete.toolTip=o.Wb.getPredictMessage("tooltip.forecastMonitor.delete",null),this.btnBucketAdd.label=o.Wb.getPredictMessage("button.forecastMonitor.add",null),this.btnBucketAdd.toolTip=o.Wb.getPredictMessage("tooltip.forecastMonitor.add",null),this.btnBucketChange.label=o.Wb.getPredictMessage("button.forecastMonitor.change",null),this.btnBucketChange.toolTip=o.Wb.getPredictMessage("tooltip.forecastMonitor.change",null),this.btnBucketDelete.label=o.Wb.getPredictMessage("button.forecastMonitor.delete",null),this.btnBucketDelete.toolTip=o.Wb.getPredictMessage("tooltip.forecastMonitor.delete",null),this.userBuckLabel.text=o.Wb.getPredictMessage("label.forecastMonitorOption.userBuckets",null),this.userTempLabel.text=o.Wb.getPredictMessage("label.forecastMonitorOption.userTemplates",null)},t.prototype.onLoad=function(){var e=this;try{this.userTemplateGrid.onRowClick=function(t){e.onTemplateGridCellClick(t)},this.userBucketGrid.onRowClick=function(t){e.onBucketGridCellClick(t)},this.userBucketGrid.enableDisableCells=function(t){return e.enableDisableRow(t)},this.menuAccessIdParent=o.x.call("eval","menuAccessIdParent"),this.inputData.cbStart=this.startOfComms.bind(this),this.inputData.cbStop=this.endOfComms.bind(this),this.inputData.cbResult=function(t){e.inputDataResult(t)},this.inputData.cbFault=this.inputDataFault.bind(this),this.inputData.encodeURL=!1,this.entityData.cbStart=this.startOfComms.bind(this),this.entityData.cbStop=this.endOfComms.bind(this),this.entityData.cbResult=function(t){e.entityDataResult(t)},this.entityData.cbFault=this.inputDataFault.bind(this),this.entityData.encodeURL=!1,this.btnTemplateChange.enabled=!1,this.btnBucketChange.enabled=!1,this.btnTemplateDelete.enabled=!1,this.btnBucketDelete.enabled=!1,1==this.menuAccessIdParent&&(this.btnSave.enabled=!1),this.initializeMenus(),this.actionPath="forecastMonitor.do?",this.actionMethod="method=displayForecastMonitorOptions",this.inputData.url=this.baseURL+this.actionPath+this.actionMethod,this.inputData.send(this.requestParams),this.entityData.url=this.baseURL+this.actionPath+this.actionMethod,this.saveData.cbResult=function(t){e.saveDataResult(t)},this.saveData.cbFault=this.inputDataFault.bind(this),this.saveData.encodeURL=!1}catch(t){console.log(t,this.moduleId,"Forecast Monitor Options","onLoad")}},t.prototype.validateGrid=function(e){for(var t=e.listData.oldValue,i=e.listData.newValue,l=this.userBucketGrid.originalDataprovider,n=0;n<this.userBucketGrid.dataProvider.length;n++)if(String(i)!=String(t)){if(String(i)==String(l[n].daysto)){this.userBucketGrid.dataProvider[this.userBucketGrid.selectedIndex].daysto=t,this.swtAlert.show(o.x.call("getBundle","text","bucketExist","Selected bucket already exists"),o.x.call("getBundle","text","alert-warning","Warning"));break}if("T"==t&&1==Number(this.userBucketGrid.dataProvider[this.userBucketGrid.selectedIndex].bucketid)&&this.userBucketGrid.dataProvider.length>1){this.swtAlert.show(o.x.call("getBundle","text","lesserThanNext","The value should be lesser than the next value"),o.x.call("getBundle","text","alert-warning","Warning")),this.userBucketGrid.dataProvider[this.userBucketGrid.selectedIndex].daysto=t;break}if(this.userBucketGrid.dataProvider.length!=this.userBucketGrid.dataProvider[this.userBucketGrid.selectedIndex].bucketid&&null!=this.userBucketGrid.dataProvider.slickgrid_rowcontent[n+1].daysto.content&&Number(this.userBucketGrid.dataProvider[n+1].daysto)<=Number(i)){this.swtAlert.show(o.x.call("getBundle","text","lesserThanNext","The value should be lesser than the next value"),o.x.call("getBundle","text","alert-warning","Warning")),this.userBucketGrid.dataProvider[this.userBucketGrid.selectedIndex].daysto=t;break}if(1!=this.userBucketGrid.dataProvider.length&&this.userBucketGrid.dataProvider.length==this.userBucketGrid.dataProvider[this.userBucketGrid.selectedIndex].bucketid&&null!=this.userBucketGrid.dataProvider[n-1]&&Number(this.userBucketGrid.dataProvider[n-1].daysto)>Number(i)||"T"==t&&1!=Number(this.userBucketGrid.dataProvider[this.userBucketGrid.selectedIndex])){this.swtAlert.show(o.x.call("getBundle","text","greaterThanPrevious","The value should be greater than the previous value"),o.x.call("getBundle","text","alert-warning","Warning")),this.userBucketGrid.dataProvider[this.userBucketGrid.selectedIndex].daysto=t;break}}},t.prototype.enableDisableRow=function(e){return e.id==this.prevSelectedIndex},t.prototype.startOfComms=function(){this.disableFields(),this.loadingImage.setVisible(!0)},t.prototype.endOfComms=function(){this.loadingImage.setVisible(!1),this.enableFields()},t.prototype.disableFields=function(){this.btnSave.enabled=!1,this.btnSave.buttonMode=!1,this.btnCancel.enabled=!1,this.btnCancel.buttonMode=!1},t.prototype.enableFields=function(){1!=this.menuAccessIdParent&&(this.btnSave.enabled=!0,this.btnSave.buttonMode=!0),this.btnCancel.enabled=!0,this.btnCancel.buttonMode=!0},t.prototype.doHelp=function(e){o.x.call("help")},t.prototype.inputDataFault=function(e){this.invalidComms=e.fault.faultString+"\n"+e.fault.faultCode+"\n"+e.fault.faultDetail},t.prototype.initializeMenus=function(){this.screenVersion.loadScreenVersion(this,this.screenName,this.versionNumber,this.releaseDate);var e=new o.n("Show JSON");e.MenuItemSelect=this.showJSONSelect.bind(this),this.screenVersion.svContextMenu.customItems.push(e),this.contextMenu=this.screenVersion.svContextMenu},t.prototype.showJSONSelect=function(e){this.showJSONPopup=o.Eb.createPopUp(this,o.M,{jsonData:this.lastRecievedJSON}),this.showJSONPopup.width="700",this.showJSONPopup.title="Last Received JSON",this.showJSONPopup.height="500",this.showJSONPopup.enableResize=!1,this.showJSONPopup.showControls=!0,this.showJSONPopup.isModal=!0,this.showJSONPopup.display()},t.prototype.onTemplateGridCellClick=function(e){this.userTemplateGrid.selectedIndices.length>0?(this.btnTemplateChange.enabled=!0,this.btnTemplateDelete.enabled=!0):(this.btnTemplateChange.enabled=!1,this.btnTemplateDelete.enabled=!1)},t.prototype.onBucketGridCellClick=function(e){this.userBucketGrid.selectedIndices.length>0?(this.btnBucketChange.enabled=!0,"1"==this.userBucketGrid.selectedItem.bucketid.content||"2"==this.userBucketGrid.selectedItem.bucketid.content?this.btnBucketDelete.enabled=!1:this.btnBucketDelete.enabled=!0):(this.btnBucketChange.enabled=!1,this.btnBucketDelete.enabled=!1)},t.prototype.saveDataResult=function(e){this.inputData.isBusy()?this.inputData.cbStop():(this.lastRecievedJSON=e,this.jsonReader.setInputJSON(this.lastRecievedJSON),this.jsonReader.getRequestReplyStatus()&&o.x.call("closeWindow"))},t.prototype.entityDataResult=function(e){if(this.inputData.isBusy())this.inputData.cbStop();else{var t=new o.L,i=e;t.setInputJSON(i),t.getRequestReplyStatus()&&(this.cbEntity.setComboData(t.getSelects()),this.selectedEntity.text=this.cbEntity.selectedItem.value,this.cbCurrency.setComboData(t.getSelects()),this.selectedCurrency.text=this.cbCurrency.selectedItem.value)}""==o.Z.trim(this.cbCurrency.selectedLabel)&&this.swtAlert.error(o.Wb.getPredictMessage("alert.currencyAccess",null),"Error")},t.prototype.deepCopy=function(e){var t,i=[];for(t in e)i[t]=e[t];return i},t.prototype.inputDataResult=function(e){var t=this;this.lastRecievedJSON=e,this.jsonReader.setInputJSON(this.lastRecievedJSON),this.jsonReader.getRequestReplyStatus()?this.lastRecievedJSON!=this.prevRecievedJSON&&(this.cbEntity.setComboData(this.jsonReader.getSelects()),this.selectedEntity.text=this.cbEntity.selectedItem.value,this.cbCurrency.setComboData(this.jsonReader.getSelects()),this.selectedCurrency.text=this.cbCurrency.selectedItem.value,"Y"==this.lastRecievedJSON.monitoroptions.options.multiplier?this.multiplier.selected=!0:this.multiplier.selected=!1,"Y"==this.lastRecievedJSON.monitoroptions.options.hideweekend?this.hideWeekend.selected=!0:this.hideWeekend.selected=!1,"Y"==this.lastRecievedJSON.monitoroptions.options.hidezerosum?this.hideZeroSum.selected=!0:this.hideZeroSum.selected=!1,"Y"==this.lastRecievedJSON.monitoroptions.options.hidezerovalue?this.hideZeroValue.selected=!0:this.hideZeroValue.selected=!1,"Y"==this.lastRecievedJSON.monitoroptions.options.cumulativetotal?this.cumulativeTotal.selected=!0:this.cumulativeTotal.selected=!1,"Y"==this.lastRecievedJSON.monitoroptions.options.hidetotal?this.hideTotal.selected=!0:this.hideTotal.selected=!1,"Y"==this.lastRecievedJSON.monitoroptions.options.hideassumption?this.hideAssumption.selected=!0:this.hideAssumption.selected=!1,"Y"==this.lastRecievedJSON.monitoroptions.options.hidescenario?this.hideScenario.selected=!0:this.hideScenario.selected=!1,this.jsonReader.isDataBuilding()||(this.userTemplateGrid.CustomGrid(this.lastRecievedJSON.monitoroptions.templategrid.metadata),this.lastRecievedJSON.monitoroptions.templategrid.rows.size>0&&(this.userTemplateGrid.rowHeight=24,this.userTemplateGrid.gridData={row:this.lastRecievedJSON.monitoroptions.templategrid.rows.row,size:this.lastRecievedJSON.monitoroptions.templategrid.rows.size}),this.userBucketGrid.gridComboDataProviders(this.lastRecievedJSON.monitoroptions.selects),this.userBucketGrid.CustomGrid(this.lastRecievedJSON.monitoroptions.bucketgrid.metadata),this.lastRecievedJSON.monitoroptions.bucketgrid.rows.size>0&&(this.userBucketGrid.rowHeight=24,this.userBucketGrid.gridData={row:this.lastRecievedJSON.monitoroptions.bucketgrid.rows.row,size:this.lastRecievedJSON.monitoroptions.bucketgrid.rows.size}),this.userBucketGrid.ITEM_CHANGED.subscribe(function(e){t.validateGrid(e)})),this.prevRecievedJSON=this.lastRecievedJSON):(this.lastRecievedJSON.hasOwnProperty("request_reply")&&"true"==this.lastRecievedJSON.request_reply.closewindow&&(this.closeWindow=!0),this.swtAlert.error(this.jsonReader.getRequestReplyMessage()+"\n"+this.jsonReader.getRequestReplyLocation(),"Error",o.c.OK,this,this.errorHandler.bind(this))),""==o.Z.trim(this.cbCurrency.selectedLabel)&&this.swtAlert.error("Invalid: your role does not specify access to currencies/groups for this entity","Error")},t.prototype.changeCombo=function(e){this.selectedCurrency.text=this.cbCurrency.selectedItem.value},t.prototype.updateData=function(){if(""==o.Z.trim(this.cbCurrency.selectedLabel))this.swtAlert.show("Invalid: your role does not specify access to currencies/groups for this entity","Error");else{this.btnSave.enabled=!1,this.requestParams=[],this.actionMethod="method=saveForecastMonitorOptions";var e,t,i="",l="",n="",a="",r="",d="",s="",u="",h=[],c=[];i=this.multiplier.selected?"Y":"N",l=this.hideWeekend.selected?"Y":"N",n=this.hideTotal.selected?"Y":"N",a=this.hideAssumption.selected?"Y":"N",r=this.hideScenario.selected?"Y":"N",d=this.cumulativeTotal.selected?"Y":"N",s=this.hideZeroSum.selected?"Y":"N",u=this.hideZeroValue.selected?"Y":"N";for(var b=this.userTemplateGrid.changes.getValues(),p=this.userBucketGrid.dataProvider,g=0;g<b.length;g++){var m=b[g].crud_operation.substring(0,1);m="I"==m?"save":"U"==m?"update":"delete",this.requestParams[b[g].crud_data.currency+"_D_"+b[g].crud_data.templateid+"_D_"+b[g].crud_data.entity]=m,h.push(b[g].crud_data.currency+"_D_"+b[g].crud_data.templateid+"_D_"+b[g].crud_data.entity)}for(g=0;g<p.length;g++)c.push(p[g].bucketid+"_"+p[g].daysto+"_"+p[g].slickgrid_rowcontent.bucketstate.content+"_update");this.requestParams.updateTemplate=h.toString(),this.requestParams.updateBucket=c.toString(),e=this.cbEntity.selectedLabel,t=this.cbCurrency.selectedLabel,this.requestParams["forecastMonitorOptions.hideZeroSum"]=s,this.requestParams["forecastMonitorOptions.hideZeroValue"]=u,this.requestParams["forecastMonitorOptions.cumulativeBucketTotal"]=d,this.requestParams["forecastMonitorOptions.useCurrencyMultiplier"]=i,this.requestParams["forecastMonitorOptions.hideWeekends"]=l,this.requestParams["forecastMonitorOptions.hideTotal"]=n,this.requestParams["forecastMonitorOptions.hideAssumption"]=a,this.requestParams["forecastMonitorOptions.hideScenario"]=r,this.requestParams["forecastMonitorOptions.entity"]=e,this.requestParams["forecastMonitorOptions.currency"]=t,this.saveData.url=this.baseURL+this.actionPath+this.actionMethod,this.saveData.send(this.requestParams),this.userBucketGrid.changes.clear(),this.userTemplateGrid.changes.clear()}},t.prototype.errorHandler=function(e){e.detail==o.c.OK&&this.closeWindow&&(this.closeHandler(),this.closeWindow=!1)},t.prototype.closeHandler=function(){o.x.call("closeWindow")},t.prototype.addNewBucket=function(){this.btnBucketChange.enabled=!1,this.btnBucketDelete.enabled=!1,this.userBucketGrid.selectedIndex=-1;for(var e,t=!0,i=1,l=1,n=0;n<this.userBucketGrid.dataProvider.length;n++){if("30"==this.userBucketGrid.dataProvider[n].daysto){t=!1,this.swtAlert.warning(o.Wb.getPredictMessage("alert.forecastMonitorOption.maxValue",null),"Warning");break}"T"!=this.userBucketGrid.dataProvider[n].daysto&&"T+1"!=this.userBucketGrid.dataProvider[n].daysto?(i=Number(this.userBucketGrid.dataProvider[n].daysto)+1,l=Number(this.userBucketGrid.dataProvider[n].bucketid)+1):"T"==this.userBucketGrid.dataProvider[n].daysto?(i=2,l=2):"T+1"==this.userBucketGrid.dataProvider[n].daysto&&(i=3,l=3)}if(e=1==i?"T":2==i?"T+1":String(i),t){var a={bucketid:{content:String(l)},daysto:{content:String(e)},bucketstate:{content:"true"},modifystate:{content:"save"}};this.userBucketGrid.appendRow(a,!0),this.userBucketGrid.originalDataprovider=this.userBucketGrid.dataProvider.slice(),this.lastIndex=l}},t.prototype.changeBucket=function(){this.prevSelectedIndex=this.userBucketGrid.selectedIndex,this.userBucketGrid.refresh()},t.prototype.deleteBucket=function(){this.userBucketGrid.removeSelected(),this.btnBucketDelete.enabled=!1,this.btnBucketChange.enabled=!1},t.prototype.templateOptionClick=function(e){var t=new Object,i=null;this.templateData=this.userTemplateGrid.dataProvider.slice(),"add"==e?(i="openTemplateOptionsWindow",t=String(this.lastRecievedJSON.monitoroptions.templategrid.rows),o.x.call(i,t)):"change"==e?(t.entity=String(this.userTemplateGrid.selectedItem.entity.content),t.currency=String(this.userTemplateGrid.selectedItem.currency.content),t.template=String(this.userTemplateGrid.selectedItem.templateid.content),t.xml=String(this.lastRecievedJSON.monitoroptions.templategrid.rows),i="openTemplateOptionsWindow",o.x.call(i,t)):(this.userTemplateGrid.removeSelected(),this.btnTemplateDelete.enabled=!1,this.btnTemplateChange.enabled=!1)},t.prototype.reloadOption=function(e,t,i){for(var l=!0,n={currency:{content:String(t)},templateid:{content:String(i)},modifystate:{content:"save"},entity:{content:String(e)}},o=0;o<this.userTemplateGrid.dataProvider.length;o++)if(this.userTemplateGrid.dataProvider[o],this.userTemplateGrid.dataProvider[o].currency==t&&String(this.userTemplateGrid.dataProvider[o].entity)==e){this.userTemplateGrid.dataProvider[o].templateid=i,this.userTemplateGrid.dataProvider[o].slickgrid_rowcontent.templateid.content=i,this.userTemplateGrid.dataProvider[o].modifystate="update",this.userTemplateGrid.dataProvider[o].slickgrid_rowcontent.modifystate.content="update",l=!1,this.userTemplateGrid.refresh();break}l&&this.userTemplateGrid.appendRow(n,!0),this.userTemplateGrid.selectedIndex=-1,this.btnTemplateChange.enabled=!1,this.btnTemplateDelete.enabled=!1},t.prototype.updateEntityData=function(){this.requestParams=[];var e=this.cbCurrency.selectedLabel;this.requestParams.selectedCurrency=e;var t=this.cbEntity.selectedLabel;this.requestParams.selectedEntityId=t,this.entityData.cbStart=this.startOfComms.bind(this),this.entityData.cbStop=this.endOfComms.bind(this),this.entityData.send(this.requestParams)},t}(o.yb),d=[{path:"",component:r}],s=(a.l.forChild(d),function(){return function(){}}()),u=i("pMnS"),h=i("RChO"),c=i("t6HQ"),b=i("WFGK"),p=i("5FqG"),g=i("Ip0R"),m=i("gIcY"),w=i("t/Na"),f=i("sE5F"),v=i("OzfB"),k=i("T7CS"),y=i("S7LP"),C=i("6aHO"),S=i("WzUx"),T=i("A7o+"),B=i("zCE2"),R=i("Jg5P"),M=i("3R0m"),I=i("hhbb"),G=i("5rxC"),O=i("Fzqc"),P=i("21Lb"),J=i("hUWP"),L=i("3pJQ"),N=i("V9q+"),D=i("VDKW"),W=i("kXfT"),x=i("BGbe");i.d(t,"ForecastMonitorOptionsModuleNgFactory",function(){return A}),i.d(t,"RenderType_ForecastMonitorOptions",function(){return Z}),i.d(t,"View_ForecastMonitorOptions_0",function(){return E}),i.d(t,"View_ForecastMonitorOptions_Host_0",function(){return V}),i.d(t,"ForecastMonitorOptionsNgFactory",function(){return q});var A=l.Gb(s,[],function(e){return l.Qb([l.Rb(512,l.n,l.vb,[[8,[u.a,h.a,c.a,b.a,p.Cb,p.Pb,p.r,p.rc,p.s,p.Ab,p.Bb,p.Db,p.qd,p.Hb,p.k,p.Ib,p.Nb,p.Ub,p.yb,p.Jb,p.v,p.A,p.e,p.c,p.g,p.d,p.Kb,p.f,p.ec,p.Wb,p.bc,p.ac,p.sc,p.fc,p.lc,p.jc,p.Eb,p.Fb,p.mc,p.Lb,p.nc,p.Mb,p.dc,p.Rb,p.b,p.ic,p.Yb,p.Sb,p.kc,p.y,p.Qb,p.cc,p.hc,p.pc,p.oc,p.xb,p.p,p.q,p.o,p.h,p.j,p.w,p.Zb,p.i,p.m,p.Vb,p.Ob,p.Gb,p.Xb,p.t,p.tc,p.zb,p.n,p.qc,p.a,p.z,p.rd,p.sd,p.x,p.td,p.gc,p.l,p.u,p.ud,p.Tb,q]],[3,l.n],l.J]),l.Rb(4608,g.m,g.l,[l.F,[2,g.u]]),l.Rb(4608,m.c,m.c,[]),l.Rb(4608,m.p,m.p,[]),l.Rb(4608,w.j,w.p,[g.c,l.O,w.n]),l.Rb(4608,w.q,w.q,[w.j,w.o]),l.Rb(5120,w.a,function(e){return[e,new o.tb]},[w.q]),l.Rb(4608,w.m,w.m,[]),l.Rb(6144,w.k,null,[w.m]),l.Rb(4608,w.i,w.i,[w.k]),l.Rb(6144,w.b,null,[w.i]),l.Rb(4608,w.f,w.l,[w.b,l.B]),l.Rb(4608,w.c,w.c,[w.f]),l.Rb(4608,f.c,f.c,[]),l.Rb(4608,f.g,f.b,[]),l.Rb(5120,f.i,f.j,[]),l.Rb(4608,f.h,f.h,[f.c,f.g,f.i]),l.Rb(4608,f.f,f.a,[]),l.Rb(5120,f.d,f.k,[f.h,f.f]),l.Rb(5120,l.b,function(e,t){return[v.j(e,t)]},[g.c,l.O]),l.Rb(4608,k.a,k.a,[]),l.Rb(4608,y.a,y.a,[]),l.Rb(4608,C.a,C.a,[l.n,l.L,l.B,y.a,l.g]),l.Rb(4608,S.c,S.c,[l.n,l.g,l.B]),l.Rb(4608,S.e,S.e,[S.c]),l.Rb(4608,T.l,T.l,[]),l.Rb(4608,T.h,T.g,[]),l.Rb(4608,T.c,T.f,[]),l.Rb(4608,T.j,T.d,[]),l.Rb(4608,T.b,T.a,[]),l.Rb(4608,T.k,T.k,[T.l,T.h,T.c,T.j,T.b,T.m,T.n]),l.Rb(4608,S.i,S.i,[[2,T.k]]),l.Rb(4608,S.r,S.r,[S.L,[2,T.k],S.i]),l.Rb(4608,S.t,S.t,[]),l.Rb(4608,S.w,S.w,[]),l.Rb(1073742336,a.l,a.l,[[2,a.r],[2,a.k]]),l.Rb(1073742336,g.b,g.b,[]),l.Rb(1073742336,m.n,m.n,[]),l.Rb(1073742336,m.l,m.l,[]),l.Rb(1073742336,B.a,B.a,[]),l.Rb(1073742336,R.a,R.a,[]),l.Rb(1073742336,m.e,m.e,[]),l.Rb(1073742336,M.a,M.a,[]),l.Rb(1073742336,T.i,T.i,[]),l.Rb(1073742336,S.b,S.b,[]),l.Rb(1073742336,w.e,w.e,[]),l.Rb(1073742336,w.d,w.d,[]),l.Rb(1073742336,f.e,f.e,[]),l.Rb(1073742336,I.b,I.b,[]),l.Rb(1073742336,G.b,G.b,[]),l.Rb(1073742336,v.c,v.c,[]),l.Rb(1073742336,O.a,O.a,[]),l.Rb(1073742336,P.d,P.d,[]),l.Rb(1073742336,J.c,J.c,[]),l.Rb(1073742336,L.a,L.a,[]),l.Rb(1073742336,N.a,N.a,[[2,v.g],l.O]),l.Rb(1073742336,D.b,D.b,[]),l.Rb(1073742336,W.a,W.a,[]),l.Rb(1073742336,x.b,x.b,[]),l.Rb(1073742336,o.Tb,o.Tb,[]),l.Rb(1073742336,s,s,[]),l.Rb(256,w.n,"XSRF-TOKEN",[]),l.Rb(256,w.o,"X-XSRF-TOKEN",[]),l.Rb(256,"config",{},[]),l.Rb(256,T.m,void 0,[]),l.Rb(256,T.n,void 0,[]),l.Rb(256,"popperDefaults",{},[]),l.Rb(1024,a.i,function(){return[[{path:"",component:r}]]},[])])}),_=[[""]],Z=l.Hb({encapsulation:0,styles:_,data:{}});function E(e){return l.dc(0,[l.Zb(402653184,1,{_container:0}),l.Zb(402653184,2,{cvTemplateGridContainer:0}),l.Zb(402653184,3,{cvBucketGridContainer:0}),l.Zb(402653184,4,{loadingImage:0}),l.Zb(402653184,5,{multiplier:0}),l.Zb(402653184,6,{hideWeekend:0}),l.Zb(402653184,7,{cumulativeTotal:0}),l.Zb(402653184,8,{hideScenario:0}),l.Zb(402653184,9,{hideAssumption:0}),l.Zb(402653184,10,{hideZeroValue:0}),l.Zb(402653184,11,{hideZeroSum:0}),l.Zb(402653184,12,{hideTotal:0}),l.Zb(402653184,13,{btnCancel:0}),l.Zb(402653184,14,{btnSave:0}),l.Zb(402653184,15,{btnTemplateDelete:0}),l.Zb(402653184,16,{btnTemplateChange:0}),l.Zb(402653184,17,{btnTemplateAdd:0}),l.Zb(402653184,18,{btnBucketAdd:0}),l.Zb(402653184,19,{btnBucketChange:0}),l.Zb(402653184,20,{btnBucketDelete:0}),l.Zb(402653184,21,{helpIcon:0}),l.Zb(402653184,22,{cbEntity:0}),l.Zb(402653184,23,{cbCurrency:0}),l.Zb(402653184,24,{entityLabel:0}),l.Zb(402653184,25,{selectedEntity:0}),l.Zb(402653184,26,{currencyLabel:0}),l.Zb(402653184,27,{selectedCurrency:0}),l.Zb(402653184,28,{templateIdLabel:0}),l.Zb(402653184,29,{lblTemplateId:0}),l.Zb(402653184,30,{userTempLabel:0}),l.Zb(402653184,31,{userBuckLabel:0}),l.Zb(402653184,32,{breakdown:0}),l.Zb(402653184,33,{movementRadio:0}),l.Zb(402653184,34,{bookRadio:0}),(e()(),l.Jb(34,0,null,null,127,"SwtModule",[["height","100%"],["width","100%"]],null,[[null,"creationComplete"]],function(e,t,i){var l=!0,n=e.component;"creationComplete"===t&&(l=!1!==n.onLoad()&&l);return l},p.ad,p.hb)),l.Ib(35,4440064,null,0,o.yb,[l.r,o.i],{width:[0,"width"],height:[1,"height"]},{creationComplete:"creationComplete"}),(e()(),l.Jb(36,0,null,0,125,"VBox",[["height","100%"],["id","appContainer"],["paddingBottom","5"],["paddingLeft","5"],["paddingRight","5"],["paddingTop","5"],["verticalGap","0"],["width","100%"]],null,null,null,p.od,p.vb)),l.Ib(37,4440064,null,0,o.ec,[l.r,o.i,l.T],{id:[0,"id"],verticalGap:[1,"verticalGap"],width:[2,"width"],height:[3,"height"],paddingTop:[4,"paddingTop"],paddingBottom:[5,"paddingBottom"],paddingLeft:[6,"paddingLeft"],paddingRight:[7,"paddingRight"]},null),(e()(),l.Jb(38,0,null,0,31,"SwtCanvas",[["height","13%"],["id","swtControlBar"],["width","100%"]],null,null,null,p.Nc,p.U)),l.Ib(39,4440064,null,0,o.db,[l.r,o.i],{id:[0,"id"],width:[1,"width"],height:[2,"height"]},null),(e()(),l.Jb(40,0,null,0,29,"Grid",[["height","100%"],["width","100%"]],null,null,null,p.Cc,p.H)),l.Ib(41,4440064,null,0,o.z,[l.r,o.i],{width:[0,"width"],height:[1,"height"]},null),(e()(),l.Jb(42,0,null,0,13,"GridRow",[["height","100%"],["paddingLeft","10"],["width","100%"]],null,null,null,p.Bc,p.J)),l.Ib(43,4440064,null,0,o.B,[l.r,o.i],{width:[0,"width"],height:[1,"height"],paddingLeft:[2,"paddingLeft"]},null),(e()(),l.Jb(44,0,null,0,3,"GridItem",[["height","100%"],["width","15%"]],null,null,null,p.Ac,p.I)),l.Ib(45,4440064,null,0,o.A,[l.r,o.i],{width:[0,"width"],height:[1,"height"]},null),(e()(),l.Jb(46,0,null,0,1,"SwtLabel",[["fontWeight","bold"],["paddingLeft","5"],["paddingTop","5"],["styleName","labelRight"]],null,null,null,p.Yc,p.fb)),l.Ib(47,4440064,[[24,4],["entityLabel",4]],0,o.vb,[l.r,o.i],{styleName:[0,"styleName"],paddingTop:[1,"paddingTop"],paddingLeft:[2,"paddingLeft"],fontWeight:[3,"fontWeight"]},null),(e()(),l.Jb(48,0,null,0,3,"GridItem",[["height","100%"],["width","40%"]],null,null,null,p.Ac,p.I)),l.Ib(49,4440064,null,0,o.A,[l.r,o.i],{width:[0,"width"],height:[1,"height"]},null),(e()(),l.Jb(50,0,null,0,1,"SwtComboBox",[["dataLabel","entity"],["id","cbEntity"]],null,[[null,"change"],["window","mousewheel"]],function(e,t,i){var n=!0,o=e.component;"window:mousewheel"===t&&(n=!1!==l.Tb(e,51).mouseWeelEventHandler(i.target)&&n);"change"===t&&(n=!1!==o.updateEntityData()&&n);return n},p.Pc,p.W)),l.Ib(51,4440064,[[22,4],["cbEntity",4]],0,o.gb,[l.r,o.i],{dataLabel:[0,"dataLabel"],id:[1,"id"]},{change_:"change"}),(e()(),l.Jb(52,0,null,0,3,"GridItem",[["height","100%"],["width","100%"]],null,null,null,p.Ac,p.I)),l.Ib(53,4440064,null,0,o.A,[l.r,o.i],{width:[0,"width"],height:[1,"height"]},null),(e()(),l.Jb(54,0,null,0,1,"SwtLabel",[["fontWeight","normal"],["id","selectedEntity"],["paddingTop","5"],["styleName","labelRight"]],null,null,null,p.Yc,p.fb)),l.Ib(55,4440064,[[25,4],["selectedEntity",4]],0,o.vb,[l.r,o.i],{id:[0,"id"],styleName:[1,"styleName"],paddingTop:[2,"paddingTop"],fontWeight:[3,"fontWeight"]},null),(e()(),l.Jb(56,0,null,0,13,"GridRow",[["height","100%"],["paddingLeft","10"],["width","100%"]],null,null,null,p.Bc,p.J)),l.Ib(57,4440064,null,0,o.B,[l.r,o.i],{width:[0,"width"],height:[1,"height"],paddingLeft:[2,"paddingLeft"]},null),(e()(),l.Jb(58,0,null,0,3,"GridItem",[["height","100%"],["width","15%"]],null,null,null,p.Ac,p.I)),l.Ib(59,4440064,null,0,o.A,[l.r,o.i],{width:[0,"width"],height:[1,"height"]},null),(e()(),l.Jb(60,0,null,0,1,"SwtLabel",[["fontWeight","bold"],["paddingLeft","5"],["paddingTop","5"],["styleName","labelRight"]],null,null,null,p.Yc,p.fb)),l.Ib(61,4440064,[[26,4],["currencyLabel",4]],0,o.vb,[l.r,o.i],{styleName:[0,"styleName"],paddingTop:[1,"paddingTop"],paddingLeft:[2,"paddingLeft"],fontWeight:[3,"fontWeight"]},null),(e()(),l.Jb(62,0,null,0,3,"GridItem",[["height","100%"],["width","40%"]],null,null,null,p.Ac,p.I)),l.Ib(63,4440064,null,0,o.A,[l.r,o.i],{width:[0,"width"],height:[1,"height"]},null),(e()(),l.Jb(64,0,null,0,1,"SwtComboBox",[["dataLabel","currency"],["id","cbCurrency"]],null,[[null,"change"],["window","mousewheel"]],function(e,t,i){var n=!0,o=e.component;"window:mousewheel"===t&&(n=!1!==l.Tb(e,65).mouseWeelEventHandler(i.target)&&n);"change"===t&&(n=!1!==o.changeCombo(i)&&n);return n},p.Pc,p.W)),l.Ib(65,4440064,[[23,4],["cbCurrency",4]],0,o.gb,[l.r,o.i],{dataLabel:[0,"dataLabel"],id:[1,"id"]},{change_:"change"}),(e()(),l.Jb(66,0,null,0,3,"GridItem",[["height","100%"],["width","100%"]],null,null,null,p.Ac,p.I)),l.Ib(67,4440064,null,0,o.A,[l.r,o.i],{width:[0,"width"],height:[1,"height"]},null),(e()(),l.Jb(68,0,null,0,1,"SwtLabel",[["fontWeight","normal"],["id","selectedCurrency"],["paddingTop","5"],["styleName","labelRight"]],null,null,null,p.Yc,p.fb)),l.Ib(69,4440064,[[27,4],["selectedCurrency",4]],0,o.vb,[l.r,o.i],{id:[0,"id"],styleName:[1,"styleName"],paddingTop:[2,"paddingTop"],fontWeight:[3,"fontWeight"]},null),(e()(),l.Jb(70,0,null,0,41,"SwtCanvas",[["height","85"],["id","swtControl"],["width","100%"]],null,null,null,p.Nc,p.U)),l.Ib(71,4440064,null,0,o.db,[l.r,o.i],{id:[0,"id"],width:[1,"width"],height:[2,"height"]},null),(e()(),l.Jb(72,0,null,0,39,"Grid",[["height","100%"],["paddingLeft","5"],["paddingTop","8"],["width","100%"]],null,null,null,p.Cc,p.H)),l.Ib(73,4440064,null,0,o.z,[l.r,o.i],{width:[0,"width"],height:[1,"height"],paddingTop:[2,"paddingTop"],paddingLeft:[3,"paddingLeft"]},null),(e()(),l.Jb(74,0,null,0,13,"GridRow",[["height","30%"],["width","100%"]],null,null,null,p.Bc,p.J)),l.Ib(75,4440064,null,0,o.B,[l.r,o.i],{width:[0,"width"],height:[1,"height"]},null),(e()(),l.Jb(76,0,null,0,3,"GridItem",[["height","100%"],["paddingLeft","10"],["width","100%"]],null,null,null,p.Ac,p.I)),l.Ib(77,4440064,null,0,o.A,[l.r,o.i],{width:[0,"width"],height:[1,"height"],paddingLeft:[2,"paddingLeft"]},null),(e()(),l.Jb(78,0,null,0,1,"SwtCheckBox",[["id","multiplier"]],null,null,null,p.Oc,p.V)),l.Ib(79,4440064,[[5,4],["multiplier",4]],0,o.eb,[l.r,o.i],{id:[0,"id"]},null),(e()(),l.Jb(80,0,null,0,3,"GridItem",[["height","100%"],["paddingLeft","2"],["width","100%"]],null,null,null,p.Ac,p.I)),l.Ib(81,4440064,null,0,o.A,[l.r,o.i],{width:[0,"width"],height:[1,"height"],paddingLeft:[2,"paddingLeft"]},null),(e()(),l.Jb(82,0,null,0,1,"SwtCheckBox",[["id","hideWeekend"]],null,null,null,p.Oc,p.V)),l.Ib(83,4440064,[[6,4],["hideWeekend",4]],0,o.eb,[l.r,o.i],{id:[0,"id"]},null),(e()(),l.Jb(84,0,null,0,3,"GridItem",[["height","100%"],["paddingLeft","1"],["width","100%"]],null,null,null,p.Ac,p.I)),l.Ib(85,4440064,null,0,o.A,[l.r,o.i],{width:[0,"width"],height:[1,"height"],paddingLeft:[2,"paddingLeft"]},null),(e()(),l.Jb(86,0,null,0,1,"SwtCheckBox",[["id","cumulativeTotal"]],null,null,null,p.Oc,p.V)),l.Ib(87,4440064,[[7,4],["cumulativeTotal",4]],0,o.eb,[l.r,o.i],{id:[0,"id"]},null),(e()(),l.Jb(88,0,null,0,9,"GridRow",[["height","30%"],["width","100%"]],null,null,null,p.Bc,p.J)),l.Ib(89,4440064,null,0,o.B,[l.r,o.i],{width:[0,"width"],height:[1,"height"]},null),(e()(),l.Jb(90,0,null,0,3,"GridItem",[["height","100%"],["paddingLeft","10"],["width","50%"]],null,null,null,p.Ac,p.I)),l.Ib(91,4440064,null,0,o.A,[l.r,o.i],{width:[0,"width"],height:[1,"height"],paddingLeft:[2,"paddingLeft"]},null),(e()(),l.Jb(92,0,null,0,1,"SwtCheckBox",[["id","hideZeroValue"]],null,null,null,p.Oc,p.V)),l.Ib(93,4440064,[[10,4],["hideZeroValue",4]],0,o.eb,[l.r,o.i],{id:[0,"id"]},null),(e()(),l.Jb(94,0,null,0,3,"GridItem",[["height","100%"],["paddingLeft","2"],["width","100%"]],null,null,null,p.Ac,p.I)),l.Ib(95,4440064,null,0,o.A,[l.r,o.i],{width:[0,"width"],height:[1,"height"],paddingLeft:[2,"paddingLeft"]},null),(e()(),l.Jb(96,0,null,0,1,"SwtCheckBox",[["id","hideZeroSum"]],null,null,null,p.Oc,p.V)),l.Ib(97,4440064,[[11,4],["hideZeroSum",4]],0,o.eb,[l.r,o.i],{id:[0,"id"]},null),(e()(),l.Jb(98,0,null,0,13,"GridRow",[["height","30%"],["width","100%"]],null,null,null,p.Bc,p.J)),l.Ib(99,4440064,null,0,o.B,[l.r,o.i],{width:[0,"width"],height:[1,"height"]},null),(e()(),l.Jb(100,0,null,0,3,"GridItem",[["height","100%"],["paddingLeft","10"],["width","100%"]],null,null,null,p.Ac,p.I)),l.Ib(101,4440064,null,0,o.A,[l.r,o.i],{width:[0,"width"],height:[1,"height"],paddingLeft:[2,"paddingLeft"]},null),(e()(),l.Jb(102,0,null,0,1,"SwtCheckBox",[["id","hideTotal"]],null,null,null,p.Oc,p.V)),l.Ib(103,4440064,[[12,4],["hideTotal",4]],0,o.eb,[l.r,o.i],{id:[0,"id"]},null),(e()(),l.Jb(104,0,null,0,3,"GridItem",[["height","100%"],["paddingLeft","2"],["width","100%"]],null,null,null,p.Ac,p.I)),l.Ib(105,4440064,null,0,o.A,[l.r,o.i],{width:[0,"width"],height:[1,"height"],paddingLeft:[2,"paddingLeft"]},null),(e()(),l.Jb(106,0,null,0,1,"SwtCheckBox",[["id","hideAssumption"]],null,null,null,p.Oc,p.V)),l.Ib(107,4440064,[[9,4],["hideAssumption",4]],0,o.eb,[l.r,o.i],{id:[0,"id"]},null),(e()(),l.Jb(108,0,null,0,3,"GridItem",[["height","100%"],["paddingLeft","1"],["width","100%"]],null,null,null,p.Ac,p.I)),l.Ib(109,4440064,null,0,o.A,[l.r,o.i],{width:[0,"width"],height:[1,"height"],paddingLeft:[2,"paddingLeft"]},null),(e()(),l.Jb(110,0,null,0,1,"SwtCheckBox",[["id","hideScenario"]],null,null,null,p.Oc,p.V)),l.Ib(111,4440064,[[8,4],["hideScenario",4]],0,o.eb,[l.r,o.i],{id:[0,"id"]},null),(e()(),l.Jb(112,0,null,0,33,"HBox",[["height","100%"],["id","hbGridContainer"],["width","100%"]],null,null,null,p.Dc,p.K)),l.Ib(113,4440064,null,0,o.C,[l.r,o.i],{id:[0,"id"],width:[1,"width"],height:[2,"height"]},null),(e()(),l.Jb(114,0,null,0,15,"SwtCanvas",[["border","false"],["height","99%"],["style","border: 1px solid gray"],["width","50%"]],null,null,null,p.Nc,p.U)),l.Ib(115,4440064,null,0,o.db,[l.r,o.i],{width:[0,"width"],height:[1,"height"],border:[2,"border"]},null),(e()(),l.Jb(116,0,null,0,13,"VBox",[["height","100%"],["styleName","controlBar"],["width","100%"]],null,null,null,p.od,p.vb)),l.Ib(117,4440064,null,0,o.ec,[l.r,o.i,l.T],{styleName:[0,"styleName"],width:[1,"width"],height:[2,"height"]},null),(e()(),l.Jb(118,0,null,0,1,"SwtLabel",[["fontWeight","normal"],["height","15"],["paddingLeft","10"]],null,null,null,p.Yc,p.fb)),l.Ib(119,4440064,[[30,4],["userTempLabel",4]],0,o.vb,[l.r,o.i],{height:[0,"height"],paddingLeft:[1,"paddingLeft"],fontWeight:[2,"fontWeight"]},null),(e()(),l.Jb(120,0,null,0,1,"SwtCanvas",[["height","100%"],["id","cvTemplateGridContainer"],["width","100%"]],null,null,null,p.Nc,p.U)),l.Ib(121,4440064,[[2,4],["cvTemplateGridContainer",4]],0,o.db,[l.r,o.i],{id:[0,"id"],width:[1,"width"],height:[2,"height"]},null),(e()(),l.Jb(122,0,null,0,7,"HBox",[["height","35"],["minHeight","50"],["paddingLeft","15"],["paddingTop","8"],["styleName","hgroupTop"],["width","50%"]],null,null,null,p.Dc,p.K)),l.Ib(123,4440064,null,0,o.C,[l.r,o.i],{styleName:[0,"styleName"],width:[1,"width"],height:[2,"height"],minHeight:[3,"minHeight"],paddingTop:[4,"paddingTop"],paddingLeft:[5,"paddingLeft"]},null),(e()(),l.Jb(124,0,null,0,1,"SwtButton",[["buttonMode","true"],["id","btnTemplateAdd"]],null,[[null,"click"]],function(e,t,i){var l=!0,n=e.component;"click"===t&&(l=!1!==n.templateOptionClick("add")&&l);return l},p.Mc,p.T)),l.Ib(125,4440064,[[17,4],["btnTemplateAdd",4]],0,o.cb,[l.r,o.i],{id:[0,"id"],buttonMode:[1,"buttonMode"]},{onClick_:"click"}),(e()(),l.Jb(126,0,null,0,1,"SwtButton",[["buttonMode","true"],["id","btnTemplateChange"]],null,[[null,"click"]],function(e,t,i){var l=!0,n=e.component;"click"===t&&(l=!1!==n.templateOptionClick("change")&&l);return l},p.Mc,p.T)),l.Ib(127,4440064,[[16,4],["btnTemplateChange",4]],0,o.cb,[l.r,o.i],{id:[0,"id"],buttonMode:[1,"buttonMode"]},{onClick_:"click"}),(e()(),l.Jb(128,0,null,0,1,"SwtButton",[["buttonMode","true"],["id","btnTemplateDelete"]],null,[[null,"click"]],function(e,t,i){var l=!0,n=e.component;"click"===t&&(l=!1!==n.templateOptionClick("delete")&&l);return l},p.Mc,p.T)),l.Ib(129,4440064,[[15,4],["btnTemplateDelete",4]],0,o.cb,[l.r,o.i],{id:[0,"id"],buttonMode:[1,"buttonMode"]},{onClick_:"click"}),(e()(),l.Jb(130,0,null,0,15,"SwtCanvas",[["border","false"],["height","99%"],["style","border: 1px solid gray"],["width","50%"]],null,null,null,p.Nc,p.U)),l.Ib(131,4440064,null,0,o.db,[l.r,o.i],{width:[0,"width"],height:[1,"height"],border:[2,"border"]},null),(e()(),l.Jb(132,0,null,0,13,"VBox",[["height","100%"],["styleName","controlBar"],["width","100%"]],null,null,null,p.od,p.vb)),l.Ib(133,4440064,null,0,o.ec,[l.r,o.i,l.T],{styleName:[0,"styleName"],width:[1,"width"],height:[2,"height"]},null),(e()(),l.Jb(134,0,null,0,1,"SwtLabel",[["fontWeight","normal"],["height","15"],["paddingLeft","10"]],null,null,null,p.Yc,p.fb)),l.Ib(135,4440064,[[31,4],["userBuckLabel",4]],0,o.vb,[l.r,o.i],{height:[0,"height"],paddingLeft:[1,"paddingLeft"],fontWeight:[2,"fontWeight"]},null),(e()(),l.Jb(136,0,null,0,1,"SwtCanvas",[["height","100%"],["id","cvBucketGridContainer"],["width","100%"]],null,null,null,p.Nc,p.U)),l.Ib(137,4440064,[[3,4],["cvBucketGridContainer",4]],0,o.db,[l.r,o.i],{id:[0,"id"],width:[1,"width"],height:[2,"height"]},null),(e()(),l.Jb(138,0,null,0,7,"HBox",[["bottom","16"],["height","34"],["minHeight","50"],["paddingLeft","15"],["paddingTop","8"],["styleName","hgroupTop"],["width","50%"]],null,null,null,p.Dc,p.K)),l.Ib(139,4440064,null,0,o.C,[l.r,o.i],{bottom:[0,"bottom"],styleName:[1,"styleName"],width:[2,"width"],height:[3,"height"],minHeight:[4,"minHeight"],paddingTop:[5,"paddingTop"],paddingLeft:[6,"paddingLeft"]},null),(e()(),l.Jb(140,0,null,0,1,"SwtButton",[["buttonMode","true"],["id","btnBucketAdd"]],null,[[null,"click"]],function(e,t,i){var l=!0,n=e.component;"click"===t&&(l=!1!==n.addNewBucket()&&l);return l},p.Mc,p.T)),l.Ib(141,4440064,[[18,4],["btnBucketAdd",4]],0,o.cb,[l.r,o.i],{id:[0,"id"],buttonMode:[1,"buttonMode"]},{onClick_:"click"}),(e()(),l.Jb(142,0,null,0,1,"SwtButton",[["buttonMode","true"],["id","btnBucketChange"]],null,[[null,"click"]],function(e,t,i){var l=!0,n=e.component;"click"===t&&(l=!1!==n.changeBucket()&&l);return l},p.Mc,p.T)),l.Ib(143,4440064,[[19,4],["btnBucketChange",4]],0,o.cb,[l.r,o.i],{id:[0,"id"],buttonMode:[1,"buttonMode"]},{onClick_:"click"}),(e()(),l.Jb(144,0,null,0,1,"SwtButton",[["buttonMode","true"],["id","btnBucketDelete"]],null,[[null,"click"]],function(e,t,i){var l=!0,n=e.component;"click"===t&&(l=!1!==n.deleteBucket()&&l);return l},p.Mc,p.T)),l.Ib(145,4440064,[[20,4],["btnBucketDelete",4]],0,o.cb,[l.r,o.i],{id:[0,"id"],buttonMode:[1,"buttonMode"]},{onClick_:"click"}),(e()(),l.Jb(146,0,null,0,15,"SwtCanvas",[["height","35"],["id","cvSaveOptions"],["width","100%"]],null,null,null,p.Nc,p.U)),l.Ib(147,4440064,null,0,o.db,[l.r,o.i],{id:[0,"id"],width:[1,"width"],height:[2,"height"]},null),(e()(),l.Jb(148,0,null,0,13,"HBox",[["width","100%"]],null,null,null,p.Dc,p.K)),l.Ib(149,4440064,null,0,o.C,[l.r,o.i],{width:[0,"width"]},null),(e()(),l.Jb(150,0,null,0,5,"HBox",[["width","90%"]],null,null,null,p.Dc,p.K)),l.Ib(151,4440064,null,0,o.C,[l.r,o.i],{width:[0,"width"]},null),(e()(),l.Jb(152,0,null,0,1,"SwtButton",[["buttonMode","true"],["id","btnSave"]],null,[[null,"click"]],function(e,t,i){var l=!0,n=e.component;"click"===t&&(l=!1!==n.updateData()&&l);return l},p.Mc,p.T)),l.Ib(153,4440064,[[14,4],["btnSave",4]],0,o.cb,[l.r,o.i],{id:[0,"id"],buttonMode:[1,"buttonMode"]},{onClick_:"click"}),(e()(),l.Jb(154,0,null,0,1,"SwtButton",[["buttonMode","true"],["id","btnCancel"]],null,[[null,"click"]],function(e,t,i){var l=!0,n=e.component;"click"===t&&(l=!1!==n.closeHandler()&&l);return l},p.Mc,p.T)),l.Ib(155,4440064,[[13,4],["btnCancel",4]],0,o.cb,[l.r,o.i],{id:[0,"id"],buttonMode:[1,"buttonMode"]},{onClick_:"click"}),(e()(),l.Jb(156,0,null,0,5,"HBox",[["horizontalAlign","right"],["width","5%"]],null,null,null,p.Dc,p.K)),l.Ib(157,4440064,null,0,o.C,[l.r,o.i],{horizontalAlign:[0,"horizontalAlign"],width:[1,"width"]},null),(e()(),l.Jb(158,0,null,0,1,"SwtHelpButton",[["id","helpIcon"]],null,[[null,"click"]],function(e,t,i){var l=!0,n=e.component;"click"===t&&(l=!1!==n.doHelp(i)&&l);return l},p.Wc,p.db)),l.Ib(159,4440064,[[21,4],["helpIcon",4]],0,o.rb,[l.r,o.i],{id:[0,"id"]},{onClick_:"click"}),(e()(),l.Jb(160,0,null,0,1,"SwtLoadingImage",[],null,null,null,p.Zc,p.gb)),l.Ib(161,114688,[[4,4],["loadingImage",4]],0,o.xb,[l.r],null,null)],function(e,t){e(t,35,0,"100%","100%");e(t,37,0,"appContainer","0","100%","100%","5","5","5","5");e(t,39,0,"swtControlBar","100%","13%");e(t,41,0,"100%","100%");e(t,43,0,"100%","100%","10");e(t,45,0,"15%","100%");e(t,47,0,"labelRight","5","5","bold");e(t,49,0,"40%","100%");e(t,51,0,"entity","cbEntity");e(t,53,0,"100%","100%");e(t,55,0,"selectedEntity","labelRight","5","normal");e(t,57,0,"100%","100%","10");e(t,59,0,"15%","100%");e(t,61,0,"labelRight","5","5","bold");e(t,63,0,"40%","100%");e(t,65,0,"currency","cbCurrency");e(t,67,0,"100%","100%");e(t,69,0,"selectedCurrency","labelRight","5","normal");e(t,71,0,"swtControl","100%","85");e(t,73,0,"100%","100%","8","5");e(t,75,0,"100%","30%");e(t,77,0,"100%","100%","10");e(t,79,0,"multiplier");e(t,81,0,"100%","100%","2");e(t,83,0,"hideWeekend");e(t,85,0,"100%","100%","1");e(t,87,0,"cumulativeTotal");e(t,89,0,"100%","30%");e(t,91,0,"50%","100%","10");e(t,93,0,"hideZeroValue");e(t,95,0,"100%","100%","2");e(t,97,0,"hideZeroSum");e(t,99,0,"100%","30%");e(t,101,0,"100%","100%","10");e(t,103,0,"hideTotal");e(t,105,0,"100%","100%","2");e(t,107,0,"hideAssumption");e(t,109,0,"100%","100%","1");e(t,111,0,"hideScenario");e(t,113,0,"hbGridContainer","100%","100%");e(t,115,0,"50%","99%","false");e(t,117,0,"controlBar","100%","100%");e(t,119,0,"15","10","normal");e(t,121,0,"cvTemplateGridContainer","100%","100%");e(t,123,0,"hgroupTop","50%","35","50","8","15");e(t,125,0,"btnTemplateAdd","true");e(t,127,0,"btnTemplateChange","true");e(t,129,0,"btnTemplateDelete","true");e(t,131,0,"50%","99%","false");e(t,133,0,"controlBar","100%","100%");e(t,135,0,"15","10","normal");e(t,137,0,"cvBucketGridContainer","100%","100%");e(t,139,0,"16","hgroupTop","50%","34","50","8","15");e(t,141,0,"btnBucketAdd","true");e(t,143,0,"btnBucketChange","true");e(t,145,0,"btnBucketDelete","true");e(t,147,0,"cvSaveOptions","100%","35");e(t,149,0,"100%");e(t,151,0,"90%");e(t,153,0,"btnSave","true");e(t,155,0,"btnCancel","true");e(t,157,0,"right","5%");e(t,159,0,"helpIcon"),e(t,161,0)},null)}function V(e){return l.dc(0,[(e()(),l.Jb(0,0,null,null,1,"app-forecast-monitor-options",[],null,null,null,E,Z)),l.Ib(1,4440064,null,0,r,[o.i,l.r],null,null)],function(e,t){e(t,1,0)},null)}var q=l.Fb("app-forecast-monitor-options",r,V,{maxChars:"maxChars",restrict:"restrict",id:"id",dropShadowEnabled:"dropShadowEnabled",cornerRadius:"cornerRadius",borderThickness:"borderThickness",borderStyle:"borderStyle",borderColor:"borderColor",backGroundColor:"backGroundColor",right:"right",left:"left",bottom:"bottom",top:"top",horizontalGap:"horizontalGap",verticalGap:"verticalGap",textAlign:"textAlign",toolTip:"toolTip",toolTipPreviousValue:"toolTipPreviousValue",textDictionaryId:"tooltipDictionaryId",name:"name",styleName:"styleName",horizontalAlign:"horizontalAlign",verticalAlign:"verticalAlign",width:"width",showScrollBar:"showScrollBar",height:"height",minHeight:"minHeight",minWidth:"minWidth",maxHeight:"maxHeight",maxWidth:"maxWidth",includeInLayout:"includeInLayout",visible:"visible",enabled:"enabled",paddingTop:"paddingTop",paddingBottom:"paddingBottom",paddingLeft:"paddingLeft",paddingRight:"paddingRight",marginTop:"marginTop",marginBottom:"marginBottom",marginLeft:"marginLeft",marginRight:"marginRight",contextMenu:"contextMenu"},{onClick_:"click",dbClick_:"dbClick",doubleClick_:"doubleClick",itemDoubleClick_:"itemDoubleClick",onKeyDown_:"keyDown",onKeyUp_:"keyUp",mouseUp_:"mouseUp",mouseOver_:"mouseOver",mouseDown_:"mouseDown",mouseEnter_:"mouseEnter",mouseLeave_:"mouseLeave",mouseOut_:"mouseOut",mouseIn_:"mouseIn",mouseMove_:"mouseMove",focus_:"focus",focusIn_:"focusIn",onFocusOut_:"focusOut",keyFocusChange_:"keyFocusChange",change_:"change",onSpyChange:"onSpyChange",onSpyNoChange:"onSpyNoChange",scroll_:"scroll",creationComplete:"creationComplete",preinitialize:"preinitialize"},[])}}]);