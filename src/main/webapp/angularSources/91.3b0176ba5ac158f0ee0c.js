(window.webpackJsonp=window.webpackJsonp||[]).push([[91],{xPSK:function(t,e,l){"use strict";l.r(e);var i=l("CcnG"),o=l("mrSG"),n=l("447K"),a=l("wd/R"),r=l.n(a),s=l("nxpO"),u=l("hCsK"),h=l("ZYCi"),d=function(){function t(){this._id="",this._timeData="",this._percentData="",this._threshold1Time="",this._threshold2Time="",this._threshold1Color="",this._threshold2Color="",this._startTime="",this._endTime=""}return Object.defineProperty(t.prototype,"startTime",{get:function(){return this._startTime},set:function(t){this._startTime=t},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"endTime",{get:function(){return this._endTime},set:function(t){this._endTime=t},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"threshold1Color",{get:function(){return this._threshold1Color},set:function(t){this._threshold1Color=t},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"threshold2Color",{get:function(){return this._threshold2Color},set:function(t){this._threshold2Color=t},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"id",{get:function(){return this._id},set:function(t){this._id=t},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"timeData",{get:function(){return this._timeData},set:function(t){this._timeData=t},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"percentData",{get:function(){return this._percentData},set:function(t){this._percentData=t},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"threshold1Time",{get:function(){return this._threshold1Time},set:function(t){this._threshold1Time=t},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"threshold2Time",{get:function(){return this._threshold2Time},set:function(t){this._threshold2Time=t},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"threshold1Percentage",{get:function(){return this._threshold1Percentage},set:function(t){this._threshold1Percentage=t},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"threshold2Percentage",{get:function(){return this._threshold2Percentage},set:function(t){this._threshold2Percentage=t},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"threshold1State",{get:function(){return this._threshold1State},set:function(t){this._threshold1State=t},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"threshold2State",{get:function(){return this._threshold2State},set:function(t){this._threshold2State=t},enumerable:!0,configurable:!0}),t}(),c=l("ik3b"),b=l("88/t"),p=(l("0GgQ"),function(t){function e(e,l,i,o){var a=t.call(this,l,e)||this;return a.commonService=e,a.element=l,a.viewContainerRef=i,a.cfr=o,a.inputData=new n.G(a.commonService),a.alertingData=new n.G(a.commonService),a.baseURL=n.Wb.getBaseURL(),a.invalidComms=null,a.actionMethod="",a.actionPath="",a.requestParams=[],a.previousCCy=null,a.previousEntity=null,a.jsonReader=new n.L,a.screenVersion=new n.V(a.commonService),a.throuputLogic=new n.h,a.dateFormat="",a.refreshRate=30,a.versionNumber="1.0",a.screenName="",a.tabsChartsData=new n.H,a.comboOpen=!1,a.comboChange=!1,a.entitycomboChange=!1,a.arrayOftabs=[],a.moduleId="PREDICT.THROUGHPUTMONITOR",a.fromILMScreen=!1,a.tooltipFacilityId=null,a.tooltipOtherParams=[],a.tooltipEntityId=null,a.tooltipCurrencyCode=null,a.tooltipSelectedDate=null,a.selectedNodeId=null,a.treeLevelValue=null,a.chartsAsDATAURl=new n.H,a.chartsToExportSize=0,a.lastExportType="",a.lastSelectedField=null,a.rateData=new n.G(a.commonService),a.lastSelectedTooltipParams=null,a.eventsCreated=!1,a.customTooltip=null,a.swtAlert=new n.bb(e),window.Main=a,a}return o.d(e,t),e.prototype.ngOnDestroy=function(){instanceElement=null},e.prototype.ngOnInit=function(){var t=this;instanceElement=this,this.throughputGrid=this.displaycontainer.addChild(n.pb),this.throughputGrid.uniqueColumn="ilm_group",this.throughputGrid.lockedColumnCount=4,this.screenName=n.x.call("getBundle","text","label-screenName","Inra-Day Liquidity Monitor - Main Screen"),this.entityLabel.text=n.x.call("getBundle","text","entity","Entity"),this.entityCombo.toolTip=n.x.call("getBundle","tip","entity","Select an entity ID"),this.ccyLabel.text=n.x.call("getBundle","text","currency","Currency"),this.ccyCombo.toolTip=n.x.call("getBundle","tip","currency","Select currency code"),this.valueDateLabel.text=n.x.call("getBundle","text","valuedate","Value Date"),this.lastRefLabel.text=n.x.call("getBundle","text","lastrefresh","Last Refresh:"),this.ccyLabel.text=n.x.call("getBundle","text","currency","Currency"),this.ccyCombo.toolTip=n.x.call("getBundle","tip","currency","Select currency code"),this.refreshButton.toolTip=n.x.call("getBundle","tip","button-refresh","Refresh window"),this.refreshButton.label=n.x.call("getBundle","text","button-refresh","Refresh"),this.closeButton.label=n.x.call("getBundle","text","button-close","Close"),this.closeButton.toolTip=n.x.call("getBundle","tip","button-close","Close window"),this.todayRadio.label=n.Wb.getPredictMessage("ilmthroughput.Today"),this.optionsButton.toolTip=n.x.call("getBundle","tip","button-options","Options"),this.optionsButton.label=n.x.call("getBundle","text","button-options","Options"),this.throughputGrid.rowColorFunction=function(e,l,i,o){return t.drawRowBackground(e,l,i,o)},this.throughputGrid.ITEM_CLICK.subscribe(function(e){t.cellLogic(e)}),n.v.subscribe(function(e){t.report(e)}),this.throughputGrid.colWidthURL(this.baseURL+"ilmAnalysisMonitor.do?screenName=ilmThroughPutMonitor"),this.throughputGrid.colOrderURL(this.baseURL+"ilmAnalysisMonitor.do?"),this.throughputGrid.saveWidths=!0,this.throughputGrid.saveColumnOrder=!0},e.prototype.report=function(t){this.todayRadio.selected||this.valueDate.text,this.lastExportType=t;this.chartsAsDATAURl.clear(),this.chartsToExportSize=this.arrayOftabs.length;for(var e=0;e<this.arrayOftabs.length;e++){var l=this.arrayOftabs[e],i=l.getChildAt(1).component.chart.getSVG();this.svg_to_png_data(l.label,i)}},e.prototype.svg_to_png_data=function(t,e){var l=this,i=this.createElementFromHTML(e).cloneNode(!0).outerHTML,o=new Blob([i],{type:"image/svg+xml;charset=utf-8"}),n=webkitURL.createObjectURL(o),a=new Image;a.onload=function(){var e=document.createElement("canvas");e.width=950,e.height=550,e.getContext("2d").drawImage(a,0,0,950,500);var i=e.toDataURL();l.runReport(t,i)},a.src=n},e.prototype.createElementFromHTML=function(t){var e=document.createElement("div");return e.innerHTML=t.trim(),e.firstChild},e.prototype.runReport=function(t,e){if(this.chartsAsDATAURl.put(t,e),this.chartsAsDATAURl.size()==this.chartsToExportSize){var l="";l=this.todayRadio.selected?"":this.valueDate.text;for(var i=[],o=null,a=null,r=0;r<this.chartsAsDATAURl.getKeys().length;r++){var s={},u=this.chartsAsDATAURl.getKeys()[r];this.chartsAsDATAURl.getValue(u),s.id=u,s.chartsData=this.chartsAsDATAURl.getValue(u).split(",")[1],i.push(s)}this.throughputGrid.selectedItem?(o=this.throughputGrid.selectedItem.ilm_group.content,a=this.groupCombo.dataProvider[this.groupCombo.getIndexOf(o)].value):(o=this.groupCombo.selectedLabel,a=this.groupCombo.selectedValue),n.x.call("report",this.entityCombo.selectedLabel,this.ccyCombo.selectedLabel,this.scenarioCombo.selectedLabel,l,o,this.lastExportType,JSON.stringify(i),this.entityCombo.selectedValue,this.ccyCombo.selectedValue,this.scenarioCombo.selectedValue,a,this.calculateASCombo.selectedValue)}},e.prototype.cellLogic=function(t){if(this.throughputGrid.selectedIndex>-1){var e=this.lastSelectedField=t.target.field,l=t.target.data;if(l.slickgrid_rowcontent[e]?l.slickgrid_rowcontent[e].clickable:null){var i=l.ccy,o=l.entity,n=l.ilm_group,a=this.scenarioCombo.selectedLabel;this.clickLink(o,i,n,a,this.valueDate.text)}}},e.prototype.clickLink=function(t,e,l,i,o){n.x.call("openChildWindow",t,e,l,i,o)},e.prototype.changeRadioGroup=function(t){this.todayRadio.selected?(this.valueDate.enabled=!1,this.valueDate.text="",this.dataRefresh(null)):this.valueDate.enabled=!0},e.prototype.validateDate=function(){this.validateDateField(this.valueDate)&&this.dataRefresh(null)},e.prototype.validateDateField=function(t){var e=this;try{var l=void 0,i=n.Wb.getPredictMessage("alert.enterValidDate",null);if(!t.text)return this.swtAlert.error(i,null,null,null,function(){e.setFocusDateField(t)}),!1;if(!(l=r()(t.text,this.dateFormat.toUpperCase(),!0)).isValid())return this.swtAlert.error(i,null,null,null,function(){e.setFocusDateField(t)}),!1;t.selectedDate=l.toDate()}catch(o){}return!0},e.prototype.setFocusDateField=function(t){t.setFocus(),t.text=this.jsonReader.getScreenAttributes().datefrom},e.prototype.changeCombo=function(t){this.comboChange=!0,this.entitycomboChange=!1,this.dataRefresh(null)},e.prototype.entityChangeCombo=function(t){this.comboChange=!0,this.entitycomboChange=!0,this.dataRefresh(null)},e.prototype.openedCombo=function(t){this.comboOpen=!0},e.prototype.closedCombo=function(t){this.comboOpen=!1},e.prototype.closeHandler=function(){n.x.call("close")},e.prototype.closedDateField=function(t){this.comboOpen=!1,this.valueDate.interruptComms=!1},e.prototype.drawRowBackground=function(t,e,l,i){if("threshold1"==i||"threshold2"==i)try{if("threshold1"==i)return t.slickgrid_rowcontent.threshold1Color.content;if("threshold2"==i)return t.slickgrid_rowcontent.threshold2Color.content}catch(o){}return""},e.prototype.initializeMenus=function(){this.screenVersion.loadScreenVersion(this,this.screenName,this.versionNumber,null);var t=new n.n("Show JSON");t.MenuItemSelect=this.showGridJSON.bind(this),this.screenVersion.svContextMenu.customItems.push(t),this.contextMenu=this.screenVersion.svContextMenu},e.prototype.showGridJSON=function(){this.showJSONPopup=n.Eb.createPopUp(this,n.M,{jsonData:this.lastRecievedJSON}),this.showJSONPopup.width="700",this.showJSONPopup.title="Last Received JSON",this.showJSONPopup.height="500",this.showJSONPopup.enableResize=!1,this.showJSONPopup.showControls=!0,this.showJSONPopup.isModal=!0,this.showJSONPopup.display()},e.prototype.saveRefreshRate=function(t){try{this.refreshRate=parseInt(t),this.actionPath="ilmAnalysisMonitor.do?",this.actionMethod="method=saveThroughputRefreshRate",this.requestParams=[],this.requestParams.refresh=t,this.rateData.encodeURL=!1,this.rateData.url=this.baseURL+this.actionPath+this.actionMethod,this.rateData.send(this.requestParams)}catch(e){console.log(e,this.moduleId,"PCM Monitor","saveRefreshRate")}},e.prototype.optionsHandler=function(){var t=this;try{this.win=n.Eb.createPopUp(this,s.a,{title:"Auto-refresh Rate",refreshText:this.refreshRate}),this.win.width="340",this.win.height="150",this.win.id="myOptionsPopUp",this.win.enableResize=!1,this.win.showControls=!0,this.win.isModal=!0,this.win.onClose.subscribe(function(){t.autoRefreshAfterStop()},function(t){console.log(t)}),this.win.display()}catch(e){n.Wb.logError(e,this.moduleId,"Dashboard","optionsHandler",0)}},e.prototype.autoRefreshAfterStop=function(){this.autoRefresh&&this.autoRefresh.delay(1e3*this.refreshRate)},e.prototype.onLoad=function(){var t=this;try{this.requestParams=[],this.initializeMenus(),this.dateFormat=n.x.call("eval","dateFormat")||"DD/MM/YYYY",this.fromILMScreen=n.Z.isTrue(n.x.call("eval","fromILMMonitor")),this.fromILMScreen?(this.requestParams.entityId=n.x.call("eval","entityId"),this.requestParams.currencyId=n.x.call("eval","currencyId"),this.requestParams.selectedScenario=n.x.call("eval","selectedScenario"),this.requestParams.accountGroup=n.x.call("eval","accountGroup"),this.requestParams.fromILMScreen=this.fromILMScreen,this.requestParams.entityChanged=!1):this.requestParams.entityChanged=!0,this.throuputLogic.testDate=n.x.call("eval","dbDate"),this.valueDate.formatString=this.dateFormat.toUpperCase(),"DD/MM/YYYY"==this.dateFormat?this.valueDate.toolTip=n.x.call("getBundle","tip","valuedateDDMMYY","Enter value date ('DD/MM/YYYY')"):this.valueDate.toolTip=n.x.call("getBundle","tip","valuedateMMDDYY","Enter value date ('MM/DD/YYYY')"),this.inputData.cbStart=this.startOfComms.bind(this),this.inputData.cbStop=this.endOfComms.bind(this),this.inputData.cbResult=function(e){t.inputDataResult(e)},this.inputData.cbFault=this.inputDataFault.bind(this),this.inputData.encodeURL=!1,this.actionPath="ilmAnalysisMonitor.do?",this.actionMethod="method=ilmThroughPutMonitorDisplay",this.requestParams.firstLoad=!0,this.inputData.url=this.baseURL+this.actionPath+this.actionMethod,this.inputData.send(this.requestParams),this.throughputGrid.ITEM_CLICK.subscribe(function(e){setTimeout(function(){t.itemClickFunction(e)},0)}),b.a.fromEvent(document.body,"click").subscribe(function(e){t.positionX=e.clientX,t.positionY=e.clientY})}catch(e){console.log(e)}},e.prototype.inputDataFault=function(t){try{this.invalidComms=t.fault.faultString+"\n"+t.fault.faultCode+"\n"+t.fault.faultDetail,null!=this.autoRefresh&&(this.autoRefresh.running||this.autoRefresh.start())}catch(e){n.Wb.logError(e,this.screenName,"ClassName","inputDataFault",0)}},e.prototype.getParams=function(){return[this.throughputGrid.selectedItem.entity.content,this.throughputGrid.selectedItem.ccy.content,this.throughputGrid.selectedItem.ilm_group.content,this.scenarioCombo.selectedLabel,this.lastSelectedField,this.valueDate.text]},e.prototype.calculateHeight=function(){return window.outerHeight-230},e.prototype.inputDataResult=function(t){try{if(this.inputData.isBusy())this.inputData.cbStop();else if(this.lastRecievedJSON=t,this.jsonReader.setInputJSON(this.lastRecievedJSON),this.jsonReader.getRequestReplyStatus()){if(this.lastRecievedJSON!=this.prevRecievedJSON){this.tabsChartsData.clear(),this.sysdate=r()(this.jsonReader.getSingletons().sysdate,this.dateFormat.toUpperCase()).toDate(),this.entityCombo.setComboData(this.jsonReader.getSelects()),this.ccyCombo.setComboData(this.jsonReader.getSelects()),this.groupCombo.setComboData(this.jsonReader.getSelects()),this.scenarioCombo.setComboData(this.jsonReader.getSelects()),this.calculateASCombo.setComboData(this.jsonReader.getSelects(),!0),this.selectedEntity.text=this.entityCombo.selectedValue,this.selectedCcy.text=this.ccyCombo.selectedValue,this.selectedGroup.text=this.groupCombo.selectedValue,this.valueDate.showToday=!1,this.valueDate.enabled=!0,this.valueDate.selectedDate=r()(this.jsonReader.getSingletons().valueDate,this.dateFormat.toUpperCase()).toDate(),this.valueDate.selectableRange={rangStart:null,rangeEnd:this.sysdate},this.selectedDateRadio.selected?this.valueDate.enabled=!0:this.valueDate.enabled=!1,this.lastRefTime.text=this.jsonReader.getSingletons().lastRefTime;for(var e=this.jsonReader.getSingletons().tabList.split(","),l=[],i=this.arrayOftabs.length;i--;)-1==e.indexOf(this.arrayOftabs[i].label)?(0!=this.tabs.selectedIndex&&(this.tabs.selectedIndex=0),this.tabs.removeChild(this.arrayOftabs[i]),this.arrayOftabs.splice(i,1)):l.push(this.arrayOftabs[i].label);for(var o=n.Wb.convertObjectToArray(this.lastRecievedJSON.ilmThrouputMonitor.tabs.row),a=null,s=0;s<o.length;s++)try{var h=new d;if(h.id=o[s].id,h.timeData=o[s].dataTime,h.percentData=""+o[s].dataPercentage,h.threshold1Time=o[s].threshold1Time,h.threshold2Time=o[s].threshold2Time,h.threshold1Percentage=o[s].threshold1Percentage,h.threshold2Percentage=o[s].threshold2Percentage,h.threshold1State=o[s].threshold1State,h.threshold2State=o[s].threshold2State,h.threshold1Color=o[s].threshold1Color,h.threshold2Color=o[s].threshold2Color,h.startTime=o[s].startTime,h.endTime=o[s].endTime,this.tabsChartsData.put(o[s].id,h),-1==l.indexOf(o[s].id)){(a=this.tabs.addChild(n.Xb)).height="100%",a.label=o[s].id;var b=a.addChild(u.a);b.height="100%",b.setChartsData(h),this.arrayOftabs.push(a)}}catch(y){}for(i=this.arrayOftabs.length;i--;)-1!=l.indexOf(this.arrayOftabs[i].label)&&this.arrayOftabs[i].getChildAt(1).component.updateChart(this.tabsChartsData.getValue(this.arrayOftabs[i].label));this.throughputGrid.CustomGrid(t.ilmThrouputMonitor.grid.metadata);for(var p=0;p<this.throughputGrid.columnDefinitions.length;p++){var m=this.throughputGrid.columnDefinitions[p];if("alerting"==m.field){var g="./"+n.x.call("eval","alertOrangeImage"),f="./"+n.x.call("eval","alertRedImage");m.properties={enabled:!1,columnName:"alerting",imageEnabled:g,imageCritEnabled:f,imageDisabled:"",_toolTipFlag:!0,style:" display: block; margin-left: auto; margin-right: auto;"},this.throughputGrid.columnDefinitions[p].editor=null,this.throughputGrid.columnDefinitions[p].formatter=c.a}}this.refreshRate=parseInt(this.jsonReader.getSingletons().refresh),null==this.autoRefresh?(this.autoRefresh=new n.cc(1e3*this.refreshRate,0),this.autoRefresh.addEventListener("timer",this.dataRefresh.bind(this))):this.autoRefresh.delay(1e3*this.refreshRate),this.throughputGrid.gridData=this.jsonReader.getGridData(),this.throughputGrid.setRowSize=this.jsonReader.getRowSize(),this.jsonReader.getRowSize()<1?this.dataExport.enabled=!1:this.dataExport.enabled=!0}}else this.swtAlert.show(n.x.call("getBundle","text","label-contactAdmin","Error occurred, Please contact your System Administrator: \n")+this.jsonReader.getRequestReplyMessage(),n.x.call("getBundle","text","alert-error","Error"));null!=this.autoRefresh&&(this.autoRefresh.running||this.autoRefresh.start()),this.entitycomboChange=!1}catch(w){console.log(w)}},e.prototype.doHelp=function(){},e.prototype.dataRefresh=function(t){this.actionPath="ilmAnalysisMonitor.do?",this.actionMethod="method=ilmThroughPutMonitorDisplay",this.requestParams.entityId=this.entityCombo.selectedLabel,this.requestParams.entityChanged=this.entitycomboChange,this.requestParams.currencyId=this.ccyCombo.selectedLabel,this.requestParams.selectedScenario=this.scenarioCombo.selectedLabel,this.todayRadio.selected?this.requestParams.selectedDate="":this.requestParams.selectedDate=this.valueDate.text,this.requestParams.accountGroup=this.groupCombo.selectedLabel,this.requestParams.calculateAs=this.calculateASCombo.selectedValue,this.requestParams.firstLoad=!1,this.requestParams.fromILMScreen=this.fromILMScreen,this.inputData.url=this.baseURL+this.actionPath+this.actionMethod,this.inputData.send(this.requestParams)},e.prototype.startOfComms=function(){this.loadingImage.setVisible(!0),this.refreshButton.enabled=!1,this.refreshButton.buttonMode=!1,this.valueDate.enabled&&(this.valueDate.enabled=!1)},e.prototype.endOfComms=function(){this.inputData.isBusy()||this.loadingImage.setVisible(!1),this.refreshButton.enabled=!0,this.refreshButton.buttonMode=!0},e.prototype.getParamsFromParent=function(){return{sqlParams:this.lastSelectedTooltipParams,facilityId:this.tooltipFacilityId,selectedNodeId:this.selectedNodeId,treeLevelValue:this.treeLevelValue,tooltipCurrencyCode:this.tooltipCurrencyCode,tooltipEntityId:this.tooltipEntityId,tooltipSelectedDate:this.tooltipSelectedDate,tooltipSelectedAccount:null,tooltipOtherParams:this.tooltipOtherParams}},e.prototype.createTooltip=function(t){var e=this;this.customTooltip&&this.customTooltip.close&&this.removeTooltip();try{this.customTooltip=n.Eb.createPopUp(parent,n.u,{}),window.innerHeight<this.positionY+450&&(this.positionY=120),this.customTooltip.setWindowXY(this.positionX+20,this.positionY),this.customTooltip.enableResize=!1,this.customTooltip.width="420",this.customTooltip.height="450",this.customTooltip.enableResize=!1,this.customTooltip.title="Alert Summary Tooltip",this.customTooltip.showControls=!0,this.customTooltip.showHeader=!1,this.customTooltip.parentDocument=this,this.customTooltip.processBox=this,this.customTooltip.display(),setTimeout(function(){e.eventsCreated||e.customTooltip.getChild().DISPLAY_LIST_CLICK.subscribe(function(t){e.lastSelectedTooltipParams=t.noode.data,n.x.call("openAlertInstanceSummary","openAlertInstSummary",e.selectedNodeId,e.treeLevelValue)})},0),setTimeout(function(){e.eventsCreated||e.customTooltip.getChild().LINK_TO_SPECIF_CLICK.subscribe(function(t){e.getScenarioFacility(t.noode.data.scenario_id),e.lastSelectedTooltipParams=t.noode.data,e.hostId=t.hostId,e.entityId=e.lastSelectedTooltipParams.ENTITY,e.currencyId=e.lastSelectedTooltipParams.CCY})},0),setTimeout(function(){e.eventsCreated||e.customTooltip.getChild().ITEM_CLICK.subscribe(function(t){e.selectedNodeId=t.noode.data.id,e.treeLevelValue=t.noode.data.treeLevelValue,e.customTooltip.getChild().linkToSpecificButton.enabled=!1,1==t.noode.data.count&&0==t.noode.isBranch&&(e.customTooltip.getChild().linkToSpecificButton.enabled=!0)})},0)}catch(l){console.log("SwtCommonGrid -> createTooltip -> error",l)}},e.prototype.removeTooltip=function(){null!=this.customTooltip&&this.customTooltip.close()},e.prototype.getScenarioFacility=function(t){var e=this;this.requestParams=[],this.alertingData.cbStart=this.startOfComms.bind(this),this.alertingData.cbStop=this.endOfComms.bind(this),this.alertingData.cbFault=this.inputDataFault.bind(this),this.alertingData.encodeURL=!1,this.actionPath="scenarioSummary.do?",this.actionMethod="method=getScenarioFacility",this.requestParams.scenarioId=t,this.alertingData.url=this.baseURL+this.actionPath+this.actionMethod,this.alertingData.cbResult=function(t){e.openGoToScreen(t)},this.alertingData.send(this.requestParams)},e.prototype.openGoToScreen=function(t){if(t&&t.ScenarioSummary&&t.ScenarioSummary.scenarioFacility){var e=t.ScenarioSummary.scenarioFacility,l=null!=this.lastSelectedTooltipParams&&null!=this.lastSelectedTooltipParams.entity_id?this.lastSelectedTooltipParams.entity_id:this.tooltipEntityId,i=null!=this.lastSelectedTooltipParams&&null!=this.lastSelectedTooltipParams.currency_code?this.lastSelectedTooltipParams.currency_code:this.tooltipCurrencyCode,o=null!=this.lastSelectedTooltipParams&&null!=this.lastSelectedTooltipParams.match_id?this.lastSelectedTooltipParams.match_id:null,a=null!=this.lastSelectedTooltipParams&&null!=this.lastSelectedTooltipParams.movement_id?this.lastSelectedTooltipParams.movement_id:null,r=null!=this.lastSelectedTooltipParams&&null!=this.lastSelectedTooltipParams.sweep_id?this.lastSelectedTooltipParams.sweep_id:null;n.x.call("goTo",e,this.hostId,l,o,i,a,r,"")}},e.prototype.itemClickFunction=function(t){var e=this;null==t.target||null==t.target.field||"alerting"!=t.target.field||"C"!=t.target.data.alerting&&"Y"!=t.target.data.alerting?this.removeTooltip():(this.tooltipCurrencyCode=t.target.data.ccy,this.tooltipEntityId=t.target.data.entity,this.tooltipFacilityId="ILM_THROUGHPUT_MONITOR_GRP_ROW",this.tooltipOtherParams.ilmAccountGroup=t.target.data.ilm_group,this.tooltipSelectedDate=this.valueDate.text,setTimeout(function(){e.createTooltip(null)},100))},e}(n.yb)),m=[{path:"",component:p}],g=(h.l.forChild(m),function(){return function(){}}()),f=l("pMnS"),y=l("RChO"),w=l("t6HQ"),C=l("WFGK"),R=l("5FqG"),I=l("Ip0R"),v=l("gIcY"),S=l("t/Na"),D=l("sE5F"),T=l("OzfB"),L=l("T7CS"),x=l("S7LP"),P=l("6aHO"),A=l("WzUx"),_=l("A7o+"),B=l("zCE2"),O=l("Jg5P"),M=l("3R0m"),G=l("hhbb"),J=l("5rxC"),E=l("Fzqc"),k=l("21Lb"),N=l("hUWP"),F=l("3pJQ"),W=l("V9q+"),Y=l("VDKW"),z=l("kXfT"),H=l("BGbe");l.d(e,"ILMThroughPutRatioMonitorModuleNgFactory",function(){return U}),l.d(e,"RenderType_ILMThroughPutRatioMonitor",function(){return q}),l.d(e,"View_ILMThroughPutRatioMonitor_0",function(){return V}),l.d(e,"View_ILMThroughPutRatioMonitor_Host_0",function(){return Z}),l.d(e,"ILMThroughPutRatioMonitorNgFactory",function(){return K});var U=i.Gb(g,[],function(t){return i.Qb([i.Rb(512,i.n,i.vb,[[8,[f.a,y.a,w.a,C.a,R.Cb,R.Pb,R.r,R.rc,R.s,R.Ab,R.Bb,R.Db,R.qd,R.Hb,R.k,R.Ib,R.Nb,R.Ub,R.yb,R.Jb,R.v,R.A,R.e,R.c,R.g,R.d,R.Kb,R.f,R.ec,R.Wb,R.bc,R.ac,R.sc,R.fc,R.lc,R.jc,R.Eb,R.Fb,R.mc,R.Lb,R.nc,R.Mb,R.dc,R.Rb,R.b,R.ic,R.Yb,R.Sb,R.kc,R.y,R.Qb,R.cc,R.hc,R.pc,R.oc,R.xb,R.p,R.q,R.o,R.h,R.j,R.w,R.Zb,R.i,R.m,R.Vb,R.Ob,R.Gb,R.Xb,R.t,R.tc,R.zb,R.n,R.qc,R.a,R.z,R.rd,R.sd,R.x,R.td,R.gc,R.l,R.u,R.ud,R.Tb,K]],[3,i.n],i.J]),i.Rb(4608,I.m,I.l,[i.F,[2,I.u]]),i.Rb(4608,v.c,v.c,[]),i.Rb(4608,v.p,v.p,[]),i.Rb(4608,S.j,S.p,[I.c,i.O,S.n]),i.Rb(4608,S.q,S.q,[S.j,S.o]),i.Rb(5120,S.a,function(t){return[t,new n.tb]},[S.q]),i.Rb(4608,S.m,S.m,[]),i.Rb(6144,S.k,null,[S.m]),i.Rb(4608,S.i,S.i,[S.k]),i.Rb(6144,S.b,null,[S.i]),i.Rb(4608,S.f,S.l,[S.b,i.B]),i.Rb(4608,S.c,S.c,[S.f]),i.Rb(4608,D.c,D.c,[]),i.Rb(4608,D.g,D.b,[]),i.Rb(5120,D.i,D.j,[]),i.Rb(4608,D.h,D.h,[D.c,D.g,D.i]),i.Rb(4608,D.f,D.a,[]),i.Rb(5120,D.d,D.k,[D.h,D.f]),i.Rb(5120,i.b,function(t,e){return[T.j(t,e)]},[I.c,i.O]),i.Rb(4608,L.a,L.a,[]),i.Rb(4608,x.a,x.a,[]),i.Rb(4608,P.a,P.a,[i.n,i.L,i.B,x.a,i.g]),i.Rb(4608,A.c,A.c,[i.n,i.g,i.B]),i.Rb(4608,A.e,A.e,[A.c]),i.Rb(4608,_.l,_.l,[]),i.Rb(4608,_.h,_.g,[]),i.Rb(4608,_.c,_.f,[]),i.Rb(4608,_.j,_.d,[]),i.Rb(4608,_.b,_.a,[]),i.Rb(4608,_.k,_.k,[_.l,_.h,_.c,_.j,_.b,_.m,_.n]),i.Rb(4608,A.i,A.i,[[2,_.k]]),i.Rb(4608,A.r,A.r,[A.L,[2,_.k],A.i]),i.Rb(4608,A.t,A.t,[]),i.Rb(4608,A.w,A.w,[]),i.Rb(1073742336,h.l,h.l,[[2,h.r],[2,h.k]]),i.Rb(1073742336,I.b,I.b,[]),i.Rb(1073742336,v.n,v.n,[]),i.Rb(1073742336,v.l,v.l,[]),i.Rb(1073742336,B.a,B.a,[]),i.Rb(1073742336,O.a,O.a,[]),i.Rb(1073742336,v.e,v.e,[]),i.Rb(1073742336,M.a,M.a,[]),i.Rb(1073742336,_.i,_.i,[]),i.Rb(1073742336,A.b,A.b,[]),i.Rb(1073742336,S.e,S.e,[]),i.Rb(1073742336,S.d,S.d,[]),i.Rb(1073742336,D.e,D.e,[]),i.Rb(1073742336,G.b,G.b,[]),i.Rb(1073742336,J.b,J.b,[]),i.Rb(1073742336,T.c,T.c,[]),i.Rb(1073742336,E.a,E.a,[]),i.Rb(1073742336,k.d,k.d,[]),i.Rb(1073742336,N.c,N.c,[]),i.Rb(1073742336,F.a,F.a,[]),i.Rb(1073742336,W.a,W.a,[[2,T.g],i.O]),i.Rb(1073742336,Y.b,Y.b,[]),i.Rb(1073742336,z.a,z.a,[]),i.Rb(1073742336,H.b,H.b,[]),i.Rb(1073742336,n.Tb,n.Tb,[]),i.Rb(1073742336,g,g,[]),i.Rb(256,S.n,"XSRF-TOKEN",[]),i.Rb(256,S.o,"X-XSRF-TOKEN",[]),i.Rb(256,"config",{},[]),i.Rb(256,_.m,void 0,[]),i.Rb(256,_.n,void 0,[]),i.Rb(256,"popperDefaults",{},[]),i.Rb(1024,h.i,function(){return[[{path:"",component:p}]]},[])])}),j=[[""]],q=i.Hb({encapsulation:0,styles:j,data:{}});function V(t){return i.dc(0,[i.Zb(402653184,1,{_container:0}),i.Zb(402653184,2,{refreshButton:0}),i.Zb(402653184,3,{optionsButton:0}),i.Zb(402653184,4,{closeButton:0}),i.Zb(402653184,5,{loadingImage:0}),i.Zb(402653184,6,{valueDate:0}),i.Zb(402653184,7,{tabs:0}),i.Zb(402653184,8,{entityCombo:0}),i.Zb(402653184,9,{ccyCombo:0}),i.Zb(402653184,10,{groupCombo:0}),i.Zb(402653184,11,{scenarioCombo:0}),i.Zb(402653184,12,{calculateASCombo:0}),i.Zb(402653184,13,{dataExport:0}),i.Zb(402653184,14,{lastRefTime:0}),i.Zb(402653184,15,{entityLabel:0}),i.Zb(402653184,16,{ccyLabel:0}),i.Zb(402653184,17,{selectedEntity:0}),i.Zb(402653184,18,{selectedCcy:0}),i.Zb(402653184,19,{selectedGroup:0}),i.Zb(402653184,20,{valueDateLabel:0}),i.Zb(402653184,21,{lastRefLabel:0}),i.Zb(402653184,22,{dateSelected:0}),i.Zb(402653184,23,{todayRadio:0}),i.Zb(402653184,24,{selectedDateRadio:0}),i.Zb(402653184,25,{displaycontainer:0}),i.Zb(402653184,26,{dynamicChartTab:0}),(t()(),i.Jb(26,0,null,null,132,"SwtModule",[["height","100%"],["width","100%"]],null,[[null,"creationComplete"]],function(t,e,l){var i=!0,o=t.component;"creationComplete"===e&&(i=!1!==o.onLoad()&&i);return i},R.ad,R.hb)),i.Ib(27,4440064,null,0,n.yb,[i.r,n.i],{width:[0,"width"],height:[1,"height"]},{creationComplete:"creationComplete"}),(t()(),i.Jb(28,0,null,0,130,"VBox",[["height","100%"],["id","vBox1"],["paddingBottom","5"],["paddingLeft","5"],["paddingRight","5"],["paddingTop","5"],["width","100%"]],null,null,null,R.od,R.vb)),i.Ib(29,4440064,null,0,n.ec,[i.r,n.i,i.T],{id:[0,"id"],width:[1,"width"],height:[2,"height"],paddingTop:[3,"paddingTop"],paddingBottom:[4,"paddingBottom"],paddingLeft:[5,"paddingLeft"],paddingRight:[6,"paddingRight"]},null),(t()(),i.Jb(30,0,null,0,90,"SwtCanvas",[["height","97"],["minWidth","1000"],["width","100%"]],null,null,null,R.Nc,R.U)),i.Ib(31,4440064,null,0,n.db,[i.r,n.i],{width:[0,"width"],height:[1,"height"],minWidth:[2,"minWidth"]},null),(t()(),i.Jb(32,0,null,0,88,"Grid",[["height","100%"],["width","100%"]],null,null,null,R.Cc,R.H)),i.Ib(33,4440064,null,0,n.z,[i.r,n.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),i.Jb(34,0,null,0,34,"GridRow",[],null,null,null,R.Bc,R.J)),i.Ib(35,4440064,null,0,n.B,[i.r,n.i],null,null),(t()(),i.Jb(36,0,null,0,13,"GridItem",[["width","50%"]],null,null,null,R.Ac,R.I)),i.Ib(37,4440064,null,0,n.A,[i.r,n.i],{width:[0,"width"]},null),(t()(),i.Jb(38,0,null,0,3,"GridItem",[["width","120"]],null,null,null,R.Ac,R.I)),i.Ib(39,4440064,null,0,n.A,[i.r,n.i],{width:[0,"width"]},null),(t()(),i.Jb(40,0,null,0,1,"SwtLabel",[["id","entityLabel"],["textDictionaryId","ilmthroughput.entity"]],null,null,null,R.Yc,R.fb)),i.Ib(41,4440064,[[15,4],["entityLabel",4]],0,n.vb,[i.r,n.i],{id:[0,"id"],textDictionaryId:[1,"textDictionaryId"]},null),(t()(),i.Jb(42,0,null,0,3,"GridItem",[],null,null,null,R.Ac,R.I)),i.Ib(43,4440064,null,0,n.A,[i.r,n.i],null,null),(t()(),i.Jb(44,0,null,0,1,"SwtComboBox",[["dataLabel","entityList"],["id","entityCombo"],["width","200"]],null,[[null,"open"],[null,"close"],[null,"change"],["window","mousewheel"]],function(t,e,l){var o=!0,n=t.component;"window:mousewheel"===e&&(o=!1!==i.Tb(t,45).mouseWeelEventHandler(l.target)&&o);"open"===e&&(o=!1!==n.openedCombo(l)&&o);"close"===e&&(o=!1!==n.closedCombo(l)&&o);"change"===e&&(o=!1!==n.entityChangeCombo(l)&&o);return o},R.Pc,R.W)),i.Ib(45,4440064,[[8,4],["entityCombo",4]],0,n.gb,[i.r,n.i],{dataLabel:[0,"dataLabel"],width:[1,"width"],id:[2,"id"]},{open_:"open",close_:"close",change_:"change"}),(t()(),i.Jb(46,0,null,0,3,"GridItem",[],null,null,null,R.Ac,R.I)),i.Ib(47,4440064,null,0,n.A,[i.r,n.i],null,null),(t()(),i.Jb(48,0,null,0,1,"SwtLabel",[["fontWeight","normal"],["id","selectedEntity"],["paddingLeft","10"]],null,null,null,R.Yc,R.fb)),i.Ib(49,4440064,[[17,4],["selectedEntity",4]],0,n.vb,[i.r,n.i],{id:[0,"id"],paddingLeft:[1,"paddingLeft"],fontWeight:[2,"fontWeight"]},null),(t()(),i.Jb(50,0,null,0,18,"GridItem",[["width","50%"]],null,null,null,R.Ac,R.I)),i.Ib(51,4440064,null,0,n.A,[i.r,n.i],{width:[0,"width"]},null),(t()(),i.Jb(52,0,null,0,16,"HBox",[["horizontalAlign","right"],["width","100%"]],null,null,null,R.Dc,R.K)),i.Ib(53,4440064,null,0,n.C,[i.r,n.i],{horizontalAlign:[0,"horizontalAlign"],width:[1,"width"]},null),(t()(),i.Jb(54,0,null,0,3,"GridItem",[["width","170"]],null,null,null,R.Ac,R.I)),i.Ib(55,4440064,null,0,n.A,[i.r,n.i],{width:[0,"width"]},null),(t()(),i.Jb(56,0,null,0,1,"SwtLabel",[["id","valueDateLabel"],["textDictionaryId","ilmthroughput.date"]],null,null,null,R.Yc,R.fb)),i.Ib(57,4440064,[[20,4],["valueDateLabel",4]],0,n.vb,[i.r,n.i],{id:[0,"id"],textDictionaryId:[1,"textDictionaryId"]},null),(t()(),i.Jb(58,0,null,0,10,"GridItem",[],null,null,null,R.Ac,R.I)),i.Ib(59,4440064,null,0,n.A,[i.r,n.i],null,null),(t()(),i.Jb(60,0,null,0,6,"SwtRadioButtonGroup",[["align","horizontal"],["id","dateSelected"]],null,[[null,"change"]],function(t,e,l){var i=!0,o=t.component;"change"===e&&(i=!1!==o.changeRadioGroup(l)&&i);return i},R.ed,R.lb)),i.Ib(61,4440064,[[22,4],["dateSelected",4]],1,n.Hb,[S.c,i.r,n.i],{id:[0,"id"],align:[1,"align"]},{change_:"change"}),i.Zb(603979776,27,{radioItems:1}),(t()(),i.Jb(63,0,null,0,1,"SwtRadioItem",[["groupName","dateSelected"],["id","todayRadio"],["selected","true"],["value","T"],["width","120"]],null,null,null,R.fd,R.mb)),i.Ib(64,4440064,[[27,4],[23,4],["todayRadio",4]],0,n.Ib,[i.r,n.i],{id:[0,"id"],width:[1,"width"],groupName:[2,"groupName"],value:[3,"value"],selected:[4,"selected"]},null),(t()(),i.Jb(65,0,null,0,1,"SwtRadioItem",[["groupName","dateSelected"],["id","selectedDateRadio"],["value","D"]],null,null,null,R.fd,R.mb)),i.Ib(66,4440064,[[27,4],[24,4],["selectedDateRadio",4]],0,n.Ib,[i.r,n.i],{id:[0,"id"],groupName:[1,"groupName"],value:[2,"value"]},null),(t()(),i.Jb(67,0,null,0,1,"SwtDateField",[["enabled","false"],["id","valueDate"],["restrict","0-9/"],["toolTip","Enter value date (DD/MM/YYYY)"],["width","70"]],null,[[null,"change"],[null,"keyup.enter"]],function(t,e,l){var i=!0,o=t.component;"change"===e&&(i=!1!==o.validateDate()&&i);"keyup.enter"===e&&(i=!1!==o.validateDate()&&i);return i},R.Tc,R.ab)),i.Ib(68,4308992,[[6,4],["valueDate",4]],0,n.lb,[i.r,n.i,i.T],{restrict:[0,"restrict"],toolTip:[1,"toolTip"],id:[2,"id"],enabled:[3,"enabled"],width:[4,"width"]},{changeEventOutPut:"change"}),(t()(),i.Jb(69,0,null,0,27,"GridRow",[],null,null,null,R.Bc,R.J)),i.Ib(70,4440064,null,0,n.B,[i.r,n.i],null,null),(t()(),i.Jb(71,0,null,0,13,"GridItem",[["width","50%"]],null,null,null,R.Ac,R.I)),i.Ib(72,4440064,null,0,n.A,[i.r,n.i],{width:[0,"width"]},null),(t()(),i.Jb(73,0,null,0,3,"GridItem",[["width","120"]],null,null,null,R.Ac,R.I)),i.Ib(74,4440064,null,0,n.A,[i.r,n.i],{width:[0,"width"]},null),(t()(),i.Jb(75,0,null,0,1,"SwtLabel",[["id","ccyLabel"],["textDictionaryId","ilmthroughput.currency"]],null,null,null,R.Yc,R.fb)),i.Ib(76,4440064,[[16,4],["ccyLabel",4]],0,n.vb,[i.r,n.i],{id:[0,"id"],textDictionaryId:[1,"textDictionaryId"]},null),(t()(),i.Jb(77,0,null,0,3,"GridItem",[],null,null,null,R.Ac,R.I)),i.Ib(78,4440064,null,0,n.A,[i.r,n.i],null,null),(t()(),i.Jb(79,0,null,0,1,"SwtComboBox",[["dataLabel","currencyList"],["id","ccyCombo"],["width","200"]],null,[[null,"change"],[null,"open"],[null,"close"],["window","mousewheel"]],function(t,e,l){var o=!0,n=t.component;"window:mousewheel"===e&&(o=!1!==i.Tb(t,80).mouseWeelEventHandler(l.target)&&o);"change"===e&&(o=!1!==n.changeCombo(l)&&o);"open"===e&&(o=!1!==n.openedCombo(l)&&o);"close"===e&&(o=!1!==n.closedCombo(l)&&o);return o},R.Pc,R.W)),i.Ib(80,4440064,[[9,4],["ccyCombo",4]],0,n.gb,[i.r,n.i],{dataLabel:[0,"dataLabel"],width:[1,"width"],id:[2,"id"]},{open_:"open",close_:"close",change_:"change"}),(t()(),i.Jb(81,0,null,0,3,"GridItem",[],null,null,null,R.Ac,R.I)),i.Ib(82,4440064,null,0,n.A,[i.r,n.i],null,null),(t()(),i.Jb(83,0,null,0,1,"SwtLabel",[["fontWeight","normal"],["id","selectedCcy"],["paddingLeft","10"]],null,null,null,R.Yc,R.fb)),i.Ib(84,4440064,[[18,4],["selectedCcy",4]],0,n.vb,[i.r,n.i],{id:[0,"id"],paddingLeft:[1,"paddingLeft"],fontWeight:[2,"fontWeight"]},null),(t()(),i.Jb(85,0,null,0,11,"GridItem",[["width","50%"]],null,null,null,R.Ac,R.I)),i.Ib(86,4440064,null,0,n.A,[i.r,n.i],{width:[0,"width"]},null),(t()(),i.Jb(87,0,null,0,9,"HBox",[["horizontalAlign","right"],["width","100%"]],null,null,null,R.Dc,R.K)),i.Ib(88,4440064,null,0,n.C,[i.r,n.i],{horizontalAlign:[0,"horizontalAlign"],width:[1,"width"]},null),(t()(),i.Jb(89,0,null,0,3,"GridItem",[["width","180"]],null,null,null,R.Ac,R.I)),i.Ib(90,4440064,null,0,n.A,[i.r,n.i],{width:[0,"width"]},null),(t()(),i.Jb(91,0,null,0,1,"SwtLabel",[["id","calculateAS"],["textDictionaryId","ilmthroughputCalculateAs"]],null,null,null,R.Yc,R.fb)),i.Ib(92,4440064,[[15,4],["entityLabel",4]],0,n.vb,[i.r,n.i],{id:[0,"id"],textDictionaryId:[1,"textDictionaryId"]},null),(t()(),i.Jb(93,0,null,0,3,"GridItem",[],null,null,null,R.Ac,R.I)),i.Ib(94,4440064,null,0,n.A,[i.r,n.i],null,null),(t()(),i.Jb(95,0,null,0,1,"SwtComboBox",[["dataLabel","calculateAS"],["id","calculateASCombo"],["width","230"]],null,[[null,"open"],[null,"close"],[null,"change"],["window","mousewheel"]],function(t,e,l){var o=!0,n=t.component;"window:mousewheel"===e&&(o=!1!==i.Tb(t,96).mouseWeelEventHandler(l.target)&&o);"open"===e&&(o=!1!==n.openedCombo(l)&&o);"close"===e&&(o=!1!==n.closedCombo(l)&&o);"change"===e&&(o=!1!==n.changeCombo(l)&&o);return o},R.Pc,R.W)),i.Ib(96,4440064,[[12,4],["calculateASCombo",4]],0,n.gb,[i.r,n.i],{dataLabel:[0,"dataLabel"],width:[1,"width"],id:[2,"id"]},{open_:"open",close_:"close",change_:"change"}),(t()(),i.Jb(97,0,null,0,23,"GridRow",[["id","selectGroup"]],null,null,null,R.Bc,R.J)),i.Ib(98,4440064,[["selectGroup",4]],0,n.B,[i.r,n.i],{id:[0,"id"]},null),(t()(),i.Jb(99,0,null,0,13,"GridItem",[["width","50%"]],null,null,null,R.Ac,R.I)),i.Ib(100,4440064,null,0,n.A,[i.r,n.i],{width:[0,"width"]},null),(t()(),i.Jb(101,0,null,0,3,"GridItem",[["width","120"]],null,null,null,R.Ac,R.I)),i.Ib(102,4440064,null,0,n.A,[i.r,n.i],{width:[0,"width"]},null),(t()(),i.Jb(103,0,null,0,1,"SwtLabel",[["id","groupcomboLabel"],["textDictionaryId","ilmthroughput.group"]],null,null,null,R.Yc,R.fb)),i.Ib(104,4440064,[["groupcomboLabel",4]],0,n.vb,[i.r,n.i],{id:[0,"id"],textDictionaryId:[1,"textDictionaryId"]},null),(t()(),i.Jb(105,0,null,0,3,"GridItem",[],null,null,null,R.Ac,R.I)),i.Ib(106,4440064,null,0,n.A,[i.r,n.i],null,null),(t()(),i.Jb(107,0,null,0,1,"SwtComboBox",[["dataLabel","AcctGrpList"],["id","groupCombo"],["width","200"]],null,[[null,"change"],["window","mousewheel"]],function(t,e,l){var o=!0,n=t.component;"window:mousewheel"===e&&(o=!1!==i.Tb(t,108).mouseWeelEventHandler(l.target)&&o);"change"===e&&(o=!1!==n.changeCombo(l)&&o);return o},R.Pc,R.W)),i.Ib(108,4440064,[[10,4],["groupCombo",4]],0,n.gb,[i.r,n.i],{dataLabel:[0,"dataLabel"],width:[1,"width"],id:[2,"id"]},{change_:"change"}),(t()(),i.Jb(109,0,null,0,3,"GridItem",[],null,null,null,R.Ac,R.I)),i.Ib(110,4440064,null,0,n.A,[i.r,n.i],null,null),(t()(),i.Jb(111,0,null,0,1,"SwtLabel",[["fontWeight","normal"],["id","selectedGroup"],["paddingLeft","10"]],null,null,null,R.Yc,R.fb)),i.Ib(112,4440064,[[19,4],["selectedGroup",4]],0,n.vb,[i.r,n.i],{id:[0,"id"],paddingLeft:[1,"paddingLeft"],fontWeight:[2,"fontWeight"]},null),(t()(),i.Jb(113,0,null,0,7,"GridItem",[["width","50%"]],null,null,null,R.Ac,R.I)),i.Ib(114,4440064,null,0,n.A,[i.r,n.i],{width:[0,"width"]},null),(t()(),i.Jb(115,0,null,0,5,"HBox",[["horizontalAlign","right"],["width","100%"]],null,null,null,R.Dc,R.K)),i.Ib(116,4440064,null,0,n.C,[i.r,n.i],{horizontalAlign:[0,"horizontalAlign"],width:[1,"width"]},null),(t()(),i.Jb(117,0,null,0,1,"SwtLabel",[["id","scenarioLabel"],["textDictionaryId","scenario.scenarioId"],["width","180"]],null,null,null,R.Yc,R.fb)),i.Ib(118,4440064,[["scenarioLabel",4]],0,n.vb,[i.r,n.i],{id:[0,"id"],textDictionaryId:[1,"textDictionaryId"],width:[2,"width"]},null),(t()(),i.Jb(119,0,null,0,1,"SwtComboBox",[["dataLabel","scenarioList"],["id","scenarioCombo"],["width","230"]],null,[[null,"change"],["window","mousewheel"]],function(t,e,l){var o=!0,n=t.component;"window:mousewheel"===e&&(o=!1!==i.Tb(t,120).mouseWeelEventHandler(l.target)&&o);"change"===e&&(o=!1!==n.changeCombo(l)&&o);return o},R.Pc,R.W)),i.Ib(120,4440064,[[11,4],["scenarioCombo",4]],0,n.gb,[i.r,n.i],{dataLabel:[0,"dataLabel"],width:[1,"width"],id:[2,"id"]},{change_:"change"}),(t()(),i.Jb(121,0,null,0,8,"SwtTabNavigator",[["borderBottom","false"],["id","tabs"],["minWidth","1000"],["width","100%"]],null,null,null,R.id,R.pb)),i.Ib(122,4440064,[[7,4],["tabs",4]],1,n.Ob,[i.r,n.i,i.k],{id:[0,"id"],width:[1,"width"],height:[2,"height"],minWidth:[3,"minWidth"],borderBottom:[4,"borderBottom"]},null),i.Zb(603979776,28,{tabChildren:1}),(t()(),i.Jb(124,0,null,0,5,"SwtTab",[["height","100%"],["id","summaryTab"],["label","Summary"],["width","100%"]],null,null,null,R.nd,R.tb)),i.Ib(125,4440064,[[28,4],["summaryTab",4]],0,n.Xb,[i.r,n.i],{id:[0,"id"],width:[1,"width"],height:[2,"height"],label:[3,"label"]},null),(t()(),i.Jb(126,0,null,0,3,"VBox",[["height","100%"],["paddingLeft","5"],["styleName","borderVBox"],["width","100%"]],null,null,null,R.od,R.vb)),i.Ib(127,4440064,null,0,n.ec,[i.r,n.i,i.T],{styleName:[0,"styleName"],width:[1,"width"],height:[2,"height"],paddingLeft:[3,"paddingLeft"]},null),(t()(),i.Jb(128,0,null,0,1,"SwtCanvas",[["border","false"],["height","100%"],["id","displaycontainer"],["styleName","canvasWithGreyBorder"],["width","100%"]],null,null,null,R.Nc,R.U)),i.Ib(129,4440064,[[25,4],["displaycontainer",4]],0,n.db,[i.r,n.i],{id:[0,"id"],styleName:[1,"styleName"],width:[2,"width"],height:[3,"height"],border:[4,"border"]},null),(t()(),i.Jb(130,0,null,0,28,"SwtCanvas",[["height","40"],["marginBottom","0"],["minWidth","1000"],["width","100%"]],null,null,null,R.Nc,R.U)),i.Ib(131,4440064,null,0,n.db,[i.r,n.i],{width:[0,"width"],height:[1,"height"],minWidth:[2,"minWidth"],marginBottom:[3,"marginBottom"]},null),(t()(),i.Jb(132,0,null,0,26,"HBox",[["width","100%"]],null,null,null,R.Dc,R.K)),i.Ib(133,4440064,null,0,n.C,[i.r,n.i],{width:[0,"width"]},null),(t()(),i.Jb(134,0,null,0,7,"HBox",[["paddingLeft","5"],["width","50%"]],null,null,null,R.Dc,R.K)),i.Ib(135,4440064,null,0,n.C,[i.r,n.i],{width:[0,"width"],paddingLeft:[1,"paddingLeft"]},null),(t()(),i.Jb(136,0,null,0,1,"SwtButton",[["id","refreshButton"]],null,[[null,"click"]],function(t,e,l){var i=!0,o=t.component;"click"===e&&(i=!1!==o.dataRefresh(null)&&i);return i},R.Mc,R.T)),i.Ib(137,4440064,[[2,4],["refreshButton",4]],0,n.cb,[i.r,n.i],{id:[0,"id"],buttonMode:[1,"buttonMode"]},{onClick_:"click"}),(t()(),i.Jb(138,0,null,0,1,"SwtButton",[["id","optionsButton"]],null,[[null,"click"]],function(t,e,l){var i=!0,o=t.component;"click"===e&&(i=!1!==o.optionsHandler()&&i);return i},R.Mc,R.T)),i.Ib(139,4440064,[[3,4],["optionsButton",4]],0,n.cb,[i.r,n.i],{id:[0,"id"],buttonMode:[1,"buttonMode"]},{onClick_:"click"}),(t()(),i.Jb(140,0,null,0,1,"SwtButton",[["id","closeButton"]],null,[[null,"click"]],function(t,e,l){var i=!0,o=t.component;"click"===e&&(i=!1!==o.closeHandler()&&i);return i},R.Mc,R.T)),i.Ib(141,4440064,[[4,4],["closeButton",4]],0,n.cb,[i.r,n.i],{id:[0,"id"],buttonMode:[1,"buttonMode"]},{onClick_:"click"}),(t()(),i.Jb(142,0,null,0,7,"HBox",[["horizontalAlign","right"],["horizontalGap","2"],["width","50%"]],null,null,null,R.Dc,R.K)),i.Ib(143,4440064,null,0,n.C,[i.r,n.i],{horizontalGap:[0,"horizontalGap"],horizontalAlign:[1,"horizontalAlign"],width:[2,"width"]},null),(t()(),i.Jb(144,0,null,0,1,"SwtLabel",[["color","red"],["height","16"],["id","lostConnectionText"],["textDictionaryId","screen.connectionError"],["visible","false"]],null,null,null,R.Yc,R.fb)),i.Ib(145,4440064,[["lostConnectionText",4]],0,n.vb,[i.r,n.i],{id:[0,"id"],textDictionaryId:[1,"textDictionaryId"],height:[2,"height"],visible:[3,"visible"],color:[4,"color"]},null),(t()(),i.Jb(146,0,null,0,1,"SwtLabel",[["fontWeight","normal"],["id","lastRefLabel"],["styleName","labelLeftRefTime"]],null,null,null,R.Yc,R.fb)),i.Ib(147,4440064,[[21,4],["lastRefLabel",4]],0,n.vb,[i.r,n.i],{id:[0,"id"],styleName:[1,"styleName"],fontWeight:[2,"fontWeight"]},null),(t()(),i.Jb(148,0,null,0,1,"SwtLabel",[["fontWeight","normal"],["id","lastRefTime"]],null,null,null,R.Yc,R.fb)),i.Ib(149,4440064,[[14,4],["lastRefTime",4]],0,n.vb,[i.r,n.i],{id:[0,"id"],fontWeight:[1,"fontWeight"]},null),(t()(),i.Jb(150,0,null,0,8,"HBox",[["horizontalAlign","right"],["horizontalGap","2"]],null,null,null,R.Dc,R.K)),i.Ib(151,4440064,null,0,n.C,[i.r,n.i],{horizontalGap:[0,"horizontalGap"],horizontalAlign:[1,"horizontalAlign"]},null),(t()(),i.Jb(152,0,null,0,2,"div",[],null,null,null,null,null)),(t()(),i.Jb(153,0,null,null,1,"DataExport",[["id","dataExport"]],null,null,null,R.Sc,R.Z)),i.Ib(154,4440064,[[13,4],["dataExport",4]],0,n.kb,[n.i,i.r],{id:[0,"id"]},null),(t()(),i.Jb(155,0,null,0,1,"SwtHelpButton",[["id","helpIcon"]],null,[[null,"click"]],function(t,e,l){var i=!0,o=t.component;"click"===e&&(i=!1!==o.doHelp()&&i);return i},R.Wc,R.db)),i.Ib(156,4440064,null,0,n.rb,[i.r,n.i],{id:[0,"id"]},{onClick_:"click"}),(t()(),i.Jb(157,0,null,0,1,"SwtLoadingImage",[],null,null,null,R.Zc,R.gb)),i.Ib(158,114688,[[5,4],["loadingImage",4]],0,n.xb,[i.r],null,null)],function(t,e){var l=e.component;t(e,27,0,"100%","100%");t(e,29,0,"vBox1","100%","100%","5","5","5","5");t(e,31,0,"100%","97","1000");t(e,33,0,"100%","100%"),t(e,35,0);t(e,37,0,"50%");t(e,39,0,"120");t(e,41,0,"entityLabel","ilmthroughput.entity"),t(e,43,0);t(e,45,0,"entityList","200","entityCombo"),t(e,47,0);t(e,49,0,"selectedEntity","10","normal");t(e,51,0,"50%");t(e,53,0,"right","100%");t(e,55,0,"170");t(e,57,0,"valueDateLabel","ilmthroughput.date"),t(e,59,0);t(e,61,0,"dateSelected","horizontal");t(e,64,0,"todayRadio","120","dateSelected","T","true");t(e,66,0,"selectedDateRadio","dateSelected","D");t(e,68,0,"0-9/","Enter value date (DD/MM/YYYY)","valueDate","false","70"),t(e,70,0);t(e,72,0,"50%");t(e,74,0,"120");t(e,76,0,"ccyLabel","ilmthroughput.currency"),t(e,78,0);t(e,80,0,"currencyList","200","ccyCombo"),t(e,82,0);t(e,84,0,"selectedCcy","10","normal");t(e,86,0,"50%");t(e,88,0,"right","100%");t(e,90,0,"180");t(e,92,0,"calculateAS","ilmthroughputCalculateAs"),t(e,94,0);t(e,96,0,"calculateAS","230","calculateASCombo");t(e,98,0,"selectGroup");t(e,100,0,"50%");t(e,102,0,"120");t(e,104,0,"groupcomboLabel","ilmthroughput.group"),t(e,106,0);t(e,108,0,"AcctGrpList","200","groupCombo"),t(e,110,0);t(e,112,0,"selectedGroup","10","normal");t(e,114,0,"50%");t(e,116,0,"right","100%");t(e,118,0,"scenarioLabel","scenario.scenarioId","180");t(e,120,0,"scenarioList","230","scenarioCombo");t(e,122,0,"tabs","100%",l.calculateHeight(),"1000","false");t(e,125,0,"summaryTab","100%","100%","Summary");t(e,127,0,"borderVBox","100%","100%","5");t(e,129,0,"displaycontainer","canvasWithGreyBorder","100%","100%","false");t(e,131,0,"100%","40","1000","0");t(e,133,0,"100%");t(e,135,0,"50%","5");t(e,137,0,"refreshButton",!0);t(e,139,0,"optionsButton",!0);t(e,141,0,"closeButton",!0);t(e,143,0,"2","right","50%");t(e,145,0,"lostConnectionText","screen.connectionError","16","false","red");t(e,147,0,"lastRefLabel","labelLeftRefTime","normal");t(e,149,0,"lastRefTime","normal");t(e,151,0,"2","right");t(e,154,0,"dataExport");t(e,156,0,"helpIcon"),t(e,158,0)},null)}function Z(t){return i.dc(0,[(t()(),i.Jb(0,16777216,null,null,1,"ilmthrough-put-ratio-monitor",[],null,null,null,V,q)),i.Ib(1,4440064,null,0,p,[n.i,i.r,i.ib,i.n],null,null)],function(t,e){t(e,1,0)},null)}var K=i.Fb("ilmthrough-put-ratio-monitor",p,Z,{maxChars:"maxChars",restrict:"restrict",id:"id",dropShadowEnabled:"dropShadowEnabled",cornerRadius:"cornerRadius",borderThickness:"borderThickness",borderStyle:"borderStyle",borderColor:"borderColor",backGroundColor:"backGroundColor",right:"right",left:"left",bottom:"bottom",top:"top",horizontalGap:"horizontalGap",verticalGap:"verticalGap",textAlign:"textAlign",toolTip:"toolTip",toolTipPreviousValue:"toolTipPreviousValue",textDictionaryId:"tooltipDictionaryId",name:"name",styleName:"styleName",horizontalAlign:"horizontalAlign",verticalAlign:"verticalAlign",width:"width",showScrollBar:"showScrollBar",height:"height",minHeight:"minHeight",minWidth:"minWidth",maxHeight:"maxHeight",maxWidth:"maxWidth",includeInLayout:"includeInLayout",visible:"visible",enabled:"enabled",paddingTop:"paddingTop",paddingBottom:"paddingBottom",paddingLeft:"paddingLeft",paddingRight:"paddingRight",marginTop:"marginTop",marginBottom:"marginBottom",marginLeft:"marginLeft",marginRight:"marginRight",contextMenu:"contextMenu"},{onClick_:"click",dbClick_:"dbClick",doubleClick_:"doubleClick",itemDoubleClick_:"itemDoubleClick",onKeyDown_:"keyDown",onKeyUp_:"keyUp",mouseUp_:"mouseUp",mouseOver_:"mouseOver",mouseDown_:"mouseDown",mouseEnter_:"mouseEnter",mouseLeave_:"mouseLeave",mouseOut_:"mouseOut",mouseIn_:"mouseIn",mouseMove_:"mouseMove",focus_:"focus",focusIn_:"focusIn",onFocusOut_:"focusOut",keyFocusChange_:"keyFocusChange",change_:"change",onSpyChange:"onSpyChange",onSpyNoChange:"onSpyNoChange",scroll_:"scroll",creationComplete:"creationComplete",preinitialize:"preinitialize"},[])}}]);