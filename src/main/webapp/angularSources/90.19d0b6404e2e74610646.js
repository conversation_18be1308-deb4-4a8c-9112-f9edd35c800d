(window.webpackJsonp=window.webpackJsonp||[]).push([[90],{PBzc:function(t,e,l){"use strict";l.r(e);var n=l("CcnG"),i=l("mrSG"),o=l("ZYCi"),s=l("447K"),r=l("kAxm"),a=function(t){function e(e,l){var n=t.call(this,l,e)||this;return n.commonService=e,n.element=l,n.moduleId="PREDICT.THROUGHPUTBREAKDOWN",n.baseURL=s.Wb.getBaseURL(),n.inputData=new s.G(n.commonService),n.cancelExport=new s.G(n.commonService),n.invalidComms=null,n.actionMethod="",n.actionPath="",n.requestParams=[],n.jsonReader=new s.L,n.screenVersion=new s.V(n.commonService),n.dateFormat="",n.refreshRate=150,n.comboOpen=!1,n.comboChange=!1,n.entitycomboChange=!1,n.screenName="",n.lastNumber=0,n.entityId=null,n.currencyCode=null,n.ilm_group=null,n.scenarioId=null,n.drilldownColumn=null,n.valueDate=null,n.menuEntityCurrGrpAccess="2",n.previousSort="7|true|",n.lastSelectedFilterCheckboxes="YNNNN",n.selectedFont=0,n.fontValue="",n.swtAlert=new s.bb(e),n}return i.d(e,t),e.prototype.ngOnInit=function(){var t=this;if(window.opener&&window.opener.Main){var e=window.opener.Main.getParams();6==e.length&&(this.entityId=e[0],this.currencyCode=e[1],this.ilm_group=e[2],this.scenarioId=e[3],this.drilldownColumn=e[4],this.valueDate=e[5])}this.throughputBreakdownGrid=this.displaycontainer.addChild(s.hb),this.throughputBreakdownGrid.uniqueColumn="movement",this.throughputBreakdownGrid.clientSideSort=!1,this.throughputBreakdownGrid.clientSideFilter=!1,this.foreOutlflowsCheckbox.label=s.Wb.getPredictMessage("ilmthroughputbreakdown.foreOutlflowsCheckbox"),this.actOutlflowsCheckbox.label=s.Wb.getPredictMessage("ilmthroughputbreakdown.actOutlflowsCheckbox"),this.foreIntlflowsCheckbox.label=s.Wb.getPredictMessage("ilmthroughputbreakdown.foreIntlflowsCheckbox"),this.actInlflowsCheckbox.label=s.Wb.getPredictMessage("ilmthroughputbreakdown.actInlflowsCheckbox"),this.unsettledOutflows.label=s.Wb.getPredictMessage("ilmthroughputbreakdown.unsettledOutflows"),this.ccyThresholdCheckbox.label=s.Wb.getPredictMessage("ilmthroughputbreakdown.ccyThresholdCheckbox"),this.screenName=s.x.call("getBundle","text","label-screenName","Inra-Day Liquidity Monitor - Main Screen"),this.entityLabel.text=s.x.call("getBundle","text","entity","Entity"),this.entityCombo.toolTip=s.x.call("getBundle","tip","entity","Select an entity ID"),this.ccyLabel.text=s.x.call("getBundle","text","currency","Currency"),this.ccyCombo.toolTip=s.x.call("getBundle","tip","currency","Select currency code"),this.refreshButton.toolTip=s.x.call("getBundle","tip","button-refresh","Refresh window"),this.refreshButton.label=s.x.call("getBundle","text","button-refresh","Refresh"),this.closeButton.label=s.x.call("getBundle","text","button-close","Close"),this.closeButton.toolTip=s.x.call("getBundle","tip","button-close","Close window"),this.optionButton.toolTip=s.x.call("getBundle","tip","button-options","Options"),this.optionButton.label=s.x.call("getBundle","text","button-options","Options"),this.noteButton.toolTip=s.x.call("getBundle","tip","button-notes","Movement notes"),this.noteButton.label=s.x.call("getBundle","text","button-notes","Notes"),this.movementButton.toolTip=s.x.call("getBundle","tip","button-movement","Show selected movement in detail"),this.movementButton.label=s.x.call("getBundle","text","button-movement","Mvmnt"),this.messageButton.toolTip=s.x.call("getBundle","tip","button-message","Messages on selected movement"),this.messageButton.label=s.x.call("getBundle","text","button-message","Message"),this.lastRefLabel.text=s.x.call("getBundle","text","lastrefresh","Last Refresh:"),this.throughputBreakdownGrid.onRowClick=function(e){t.cellClickEventHandler(e)},this.throughputBreakdownGrid.onPaginationChanged=function(e){t.paginationChanged(e)},this.throughputBreakdownGrid.onSortChanged=function(e){t.paginationChanged(e)},this.throughputBreakdownGrid.onFilterChanged=function(e){t.paginationChanged(e)},s.v.subscribe(function(e){t.report(e.type,e.startPage,e.noOfPages)})},e.prototype.exportCancel=function(t){var e=this;this.cancelExport.cbStart=this.startOfComms.bind(this),this.cancelExport.cbStop=this.endOfComms.bind(this),this.cancelExport.cbResult=function(t){e.ExportCanceled(t)},this.actionPath="outstandingmovement.do?",this.actionMethod="method=cancelILMExport",this.cancelExport.url=this.baseURL+this.actionPath+this.actionMethod,this.cancelExport.encodeURL=!1,this.cancelExport.cbFault=this.inputDataFault.bind(this),this.requestParams.cancelExport="true",this.cancelExport.send(this.requestParams)},e.prototype.ExportCanceled=function(t){},e.prototype.report=function(t,e,l){try{this.requestParams.exportType=t,this.requestParams.currentPage=e,this.requestParams.pageCount=l,this.requestParams.applyThreshold=this.ccyThresholdCheckbox.selected?"Y":"N",this.requestParams.currentFilter=this.getCurrentFilter(),this.requestParams.entityId=this.entityId,this.requestParams.currencyId=this.currencyCode,this.requestParams.selectedScenario=this.scenarioId,this.requestParams.selectedDate=this.valueDate,this.requestParams.accountGroup=this.ilm_group;var n=this.throughputBreakdownGrid.getMsdSortedGridColumn()?this.throughputBreakdownGrid.getMsdSortedGridColumn()+"|":this.previousSort,i=this.throughputBreakdownGrid.getFilteredGridColumns();-1!=i.indexOf("||")&&(i=i.replace("||","|(EMPTY)|")),this.requestParams.selectedSort=n,this.requestParams.selectedFilter=i,this.requestParams.tokenForDownload=(new Date).getTime(),this.requestParams.screen=s.Wb.getPredictMessage("ilmthroughputbreakdown.title.window",null);var o=Object.assign({},this.requestParams);s.x.call("sendParams",o)}catch(r){}},e.prototype.onLoad=function(){var t=this;this.actionPath="ilmAnalysisMonitor.do?",this.actionMethod="method=getThroughputBreakdownDetails",this.inputData.cbStart=this.startOfComms.bind(this),this.inputData.cbStop=this.endOfComms.bind(this),this.inputData.cbResult=function(e){t.inputDataResult(e)},this.drilldownColumn&&("actinf"==this.drilldownColumn?this.actInlflowsCheckbox.selected=!0:"actout"==this.drilldownColumn?this.actOutlflowsCheckbox.selected=!0:"unsetout"==this.drilldownColumn?this.unsettledOutflows.selected=!0:"forcinf"==this.drilldownColumn?this.foreIntlflowsCheckbox.selected=!0:"forcout"==this.drilldownColumn&&(this.foreOutlflowsCheckbox.selected=!0)),this.checkIfAtLeastOneSelected(),this.requestParams.foreOutlflowsCheckbox=this.foreOutlflowsCheckbox.selected,this.requestParams.actOutlflowsCheckbox=this.actOutlflowsCheckbox.selected,this.requestParams.foreIntlflowsCheckbox=this.foreIntlflowsCheckbox.selected,this.requestParams.actInlflowsCheckbox=this.actInlflowsCheckbox.selected,this.requestParams.unsettledOutflows=this.unsettledOutflows.selected,this.requestParams.ccyThresholdCheckbox=this.ccyThresholdCheckbox.selected,this.requestParams.currentFilter=this.getCurrentFilter(),this.requestParams.entityId=this.entityId,this.requestParams.entityChanged=this.entitycomboChange,this.requestParams.currencyId=this.currencyCode,this.requestParams.selectedScenario=this.scenarioId,this.requestParams.selectedDate=this.valueDate,this.requestParams.accountGroup=this.ilm_group,this.inputData.cbFault=this.inputDataFault.bind(this),this.inputData.encodeURL=!1,this.inputData.url=this.baseURL+this.actionPath+this.actionMethod,this.inputData.send(this.requestParams),this.dataExport.exportCancelFunction=this.exportCancel.bind(this)},e.prototype.checkIfAtLeastOneSelected=function(){return!!(this.foreOutlflowsCheckbox.selected||this.actOutlflowsCheckbox.selected||this.foreIntlflowsCheckbox.selected||this.actInlflowsCheckbox.selected||this.unsettledOutflows.selected)||(this.restoreLastCheckboxesState(),!1)},e.prototype.getCurrentFilter=function(){var t="";return this.unsettledOutflows.selected?"N|N|Y|":(this.foreOutlflowsCheckbox.selected||this.foreIntlflowsCheckbox.selected?this.foreOutlflowsCheckbox.selected&&this.foreIntlflowsCheckbox.selected?t+="F|":this.foreOutlflowsCheckbox.selected?t+="FO|":t+="FI|":t+="N|",this.actInlflowsCheckbox.selected||this.actOutlflowsCheckbox.selected?this.actInlflowsCheckbox.selected&&this.actOutlflowsCheckbox.selected?t+="A|":this.actInlflowsCheckbox.selected?t+="AI|":t+="AO|":t+="N|",t+="N|")},e.prototype.inputDataResult=function(t){var e=null;try{this.inputData.isBusy()?this.inputData.cbStop():(this.lastRecievedJSON=t,this.lostConnectionText.visible=!1,this.jsonReader.setInputJSON(this.lastRecievedJSON),JSON.stringify(this.lastRecievedJSON)!==JSON.stringify(this.prevRecievedJSON)&&(this.jsonReader.getRequestReplyStatus()?(this.jsonReader.isDataBuilding()||(this.numstepper.value=Number(t.ILMThroughPutRatioBreakdown.grid.paging.currentpage),e=t.ILMThroughPutRatioBreakdown.grid.paging.maxpage,this.numstepper.maximum=Number(e),this.entityCombo.setComboData(this.jsonReader.getSelects()),this.ccyCombo.setComboData(this.jsonReader.getSelects()),this.groupCombo.setComboData(this.jsonReader.getSelects()),this.scenarioCombo.setComboData(this.jsonReader.getSelects()),this.selectedEntity.text=this.entityCombo.selectedValue,this.selectedCcy.text=this.ccyCombo.selectedValue,this.selectedGroup.text=this.groupCombo.selectedValue,this.lastRefTime.text=this.jsonReader.getSingletons().lastRefTime,this.menuEntityCurrGrpAccess=this.jsonReader.getSingletons().menuEntityCurrGrpAccess,this.throughputBreakdownGrid.CustomGrid(t.ILMThroughPutRatioBreakdown.grid.metadata),this.throughputBreakdownGrid.paginationComponent=this.numstepper,t.ILMThroughPutRatioBreakdown.grid.rows.row&&this.jsonReader.getGridData().size>0&&Number(e)>0?(this.throughputBreakdownGrid.gridData=this.jsonReader.getGridData(),this.throughputBreakdownGrid.setRowSize=this.jsonReader.getRowSize(),this.throughputBreakdownGrid.gridData=t.ILMThroughPutRatioBreakdown.grid.rows,this.cellClickEventHandler(event),this.dataExport.enabled=!0):(this.throughputBreakdownGrid.dataProvider=[],this.throughputBreakdownGrid.selectedIndex=-1,this.dataExport.enabled=!1)),this.dataExport.maxPages=Number(e),this.dataExport.totalPages=Number(e),this.dataExport.currentPage=this.numstepper.value,this.jsonReader.getRefreshRate()&&(this.refreshRate=parseInt(this.jsonReader.getRefreshRate())),null==this.autoRefresh?(this.autoRefresh=new s.cc(1e3*this.refreshRate,0),this.autoRefresh.addEventListener("timer",this.doRefreshPage.bind(this,!0)),this.autoRefresh.start()):this.autoRefresh.delay(1e3*this.refreshRate),this.prevRecievedJSON=this.lastRecievedJSON,this.lastSelectedFilterCheckboxes=this.getCurrentCheckboxesState()):"errors.DataIntegrityViolationExceptioninDelete"==this.jsonReader.getRequestReplyMessage()?this.swtAlert.error("Unable to delete, this spread profile is linked to an existing account group"):this.swtAlert.error(s.Wb.getCommonMessages("alert.generic_exception"))))}catch(l){}this.entitycomboChange=!1},e.prototype.getCurrentCheckboxesState=function(){var t="";return t+=this.foreOutlflowsCheckbox.selected?"Y":"N",t+=this.foreIntlflowsCheckbox.selected?"Y":"N",t+=this.actOutlflowsCheckbox.selected?"Y":"N",t+=this.actInlflowsCheckbox.selected?"Y":"N",t+=this.unsettledOutflows.selected?"Y":"N"},e.prototype.restoreLastCheckboxesState=function(){this.lastSelectedFilterCheckboxes.length>0&&5==this.lastSelectedFilterCheckboxes.length&&(this.foreOutlflowsCheckbox.selected="Y"==this.lastSelectedFilterCheckboxes[0],this.foreIntlflowsCheckbox.selected="Y"==this.lastSelectedFilterCheckboxes[1],this.actOutlflowsCheckbox.selected="Y"==this.lastSelectedFilterCheckboxes[2],this.actInlflowsCheckbox.selected="Y"==this.lastSelectedFilterCheckboxes[3],this.unsettledOutflows.selected="Y"==this.lastSelectedFilterCheckboxes[4])},e.prototype.cellClickEventHandler=function(t){var e=!1;this.throughputBreakdownGrid.selectedIndices.length>0&&(e=!0),this.messageButton.enabled=e,this.messageButton.buttonMode=e,this.movementButton.enabled=e,this.movementButton.buttonMode=e,this.noteButton.enabled=e,this.noteButton.buttonMode=e},e.prototype.openNotes=function(){var t=!1;if(this.throughputBreakdownGrid.selectedIndices.length>0&&(t=!0),t){var e=this.throughputBreakdownGrid.selectedItem.movementId.content;s.x.call("movementNotesFromSearch","notes",e,"",this.entityId,this.currencyCode,this.valueDate,this.menuEntityCurrGrpAccess)}},e.prototype.openMessages=function(){var t,e,l,n=!1;if(this.throughputBreakdownGrid.selectedIndices.length>0&&(n=!0),n){var i=this.throughputBreakdownGrid.selectedItem.movementId.content;e=(t=s.x.call("setMsgButtonStatus",i)).substring(0,t.indexOf("|")),l=t.substring(t.indexOf("|")+1,t.length),s.x.call("openMsgDisplayWindow",i,e,l)}},e.prototype.openMovement=function(){var t,e=!1;if(this.throughputBreakdownGrid.selectedIndices.length>0&&(e=!0),e){var l=this.throughputBreakdownGrid.selectedItem.movementId.content;t=this.entityCombo.selectedLabel.toString(),s.x.call("showMvmnt","showMovementDetails",l,t,"")}},e.prototype.fontSettingHandler=function(){var t=this;this.win=s.Eb.createPopUp(this,r.a,{title:"Options",fontSizeValue:this.selectedFont}),this.win.isModal=!0,this.win.enableResize=!1,this.win.width="360",this.win.height="140",this.win.showControls=!0,this.win.id="optionsWindow",this.win.onClose.subscribe(function(e){t.submitFontSize(e)}),this.win.display()},e.prototype.submitFontSize=function(t){this.fontValue=t.fontSize.value,"N"==this.fontValue?(this.selectedFont=0,this.throughputBreakdownGrid.styleName="dataGridNormal",this.throughputBreakdownGrid.rowHeight=18):"S"==this.fontValue&&(this.selectedFont=1,this.throughputBreakdownGrid.styleName="dataGridSmall",this.throughputBreakdownGrid.rowHeight=15)},e.prototype.closeHandler=function(){s.x.call("close")},e.prototype.entityChangeCombo=function(t){this.comboChange=!0,this.entitycomboChange=!0,this.doRefreshPage(!0)},e.prototype.changeCombo=function(t){this.comboChange=!0,this.doRefreshPage(!0),this.entitycomboChange=!1},e.prototype.openedCombo=function(t){this.comboOpen=!0},e.prototype.closedCombo=function(t){this.comboOpen=!1},e.prototype.inputDataFault=function(t){try{this.lostConnectionText.visible=!0,this.invalidComms=t.fault.faultString+"\n"+t.fault.faultCode+"\n"+t.fault.faultDetail,this.swtAlert.error(t.fault.faultstring+"\n"+t.fault.faultCode+"\n"+t.fault.faultDetail),null!=this.autoRefresh&&(this.autoRefresh.running||this.autoRefresh.start())}catch(e){s.Wb.logError(e,this.moduleId,"ClassName","inputDataFault",0)}},e.prototype.paginationChanged=function(t){this.numstepper.processing=!0,this.doRefreshPage()},e.prototype.doHelp=function(){},e.prototype.doRefreshPage=function(t,e){var l;void 0===t&&(t=!1),void 0===e&&(e=null);try{if(this.numstepper.value>0&&(this.numstepper.value<=this.numstepper.maximum&&0!=this.numstepper.value||t)){if(l=this.numstepper.value,this.lastNumber=l,this.requestParams=[],this.requestParams.currentPage=l,e&&("isActualOrForecast"==e?this.unsettledOutflows.selected=!1:"isUnsettled"==e&&(this.foreOutlflowsCheckbox.selected=!1,this.actOutlflowsCheckbox.selected=!1,this.foreIntlflowsCheckbox.selected=!1,this.actInlflowsCheckbox.selected=!1)),!this.checkIfAtLeastOneSelected())return void this.swtAlert.warning(s.Wb.getPredictMessage("alert.throuputbreakdown.atleastOneFilter",null),"Warning");this.requestParams.foreOutlflowsCheckbox=this.foreOutlflowsCheckbox.selected,this.requestParams.actOutlflowsCheckbox=this.actOutlflowsCheckbox.selected,this.requestParams.foreIntlflowsCheckbox=this.foreIntlflowsCheckbox.selected,this.requestParams.actInlflowsCheckbox=this.actInlflowsCheckbox.selected,this.requestParams.unsettledOutflows=this.unsettledOutflows.selected,this.requestParams.ccyThresholdCheckbox=this.ccyThresholdCheckbox.selected,this.requestParams.currentFilter=this.getCurrentFilter(),this.entityId=this.entityCombo.selectedLabel,this.currencyCode=this.ccyCombo.selectedLabel,this.scenarioId=this.scenarioCombo.selectedLabel,this.ilm_group=this.groupCombo.selectedLabel,this.requestParams.entityId=this.entityId,this.requestParams.entityChanged=this.entitycomboChange,this.requestParams.currencyId=this.currencyCode,this.requestParams.selectedScenario=this.scenarioId,this.requestParams.selectedDate=this.valueDate,this.requestParams.accountGroup=this.ilm_group;var n=this.throughputBreakdownGrid.getMsdSortedGridColumn()?this.throughputBreakdownGrid.getMsdSortedGridColumn()+"|":this.previousSort,i=this.throughputBreakdownGrid.getFilteredGridColumns();-1!=i.indexOf("||")&&(i=i.replace("||","|(EMPTY)|")),this.requestParams.selectedSort=n,this.requestParams.selectedFilter=i,this.previousSort=n,this.actionPath="ilmAnalysisMonitor.do?",this.actionMethod="method=getThroughputBreakdownDetails",this.inputData.url=this.baseURL+this.actionPath+this.actionMethod,this.inputData.send(this.requestParams)}}catch(o){s.Wb.logError(o,this.moduleId,"ClassName","inputDataFault",0)}},e.prototype.enableDisableControls=function(t){this.entityCombo.enabled=t,this.ccyCombo.enabled=t,this.groupCombo.enabled=t,this.scenarioCombo.enabled=t,this.numstepper.enabled=t,this.foreOutlflowsCheckbox.enabled=t,this.actOutlflowsCheckbox.enabled=t,this.foreIntlflowsCheckbox.enabled=t,this.actInlflowsCheckbox.enabled=t,this.unsettledOutflows.enabled=t,this.ccyThresholdCheckbox.enabled=t},e.prototype.startOfComms=function(){this.loadingImage.setVisible(!0),this.refreshButton.enabled=!1,this.refreshButton.buttonMode=!1,this.enableDisableControls(!1)},e.prototype.endOfComms=function(){this.inputData.isBusy()||this.loadingImage.setVisible(!1),this.refreshButton.enabled=!0,this.refreshButton.buttonMode=!0,this.enableDisableControls(!0)},e}(s.yb),u=[{path:"",component:a}],h=(o.l.forChild(u),function(){return function(){}}()),c=l("pMnS"),d=l("RChO"),b=l("t6HQ"),g=l("WFGK"),m=l("5FqG"),p=l("Ip0R"),w=l("gIcY"),f=l("t/Na"),C=l("sE5F"),x=l("OzfB"),I=l("T7CS"),k=l("S7LP"),y=l("6aHO"),R=l("WzUx"),B=l("A7o+"),v=l("zCE2"),S=l("Jg5P"),O=l("3R0m"),L=l("hhbb"),P=l("5rxC"),G=l("Fzqc"),A=l("21Lb"),D=l("hUWP"),J=l("3pJQ"),T=l("V9q+"),M=l("VDKW"),N=l("kXfT"),F=l("BGbe");l.d(e,"ILMThroughPutRatioBreakdownModuleNgFactory",function(){return q}),l.d(e,"RenderType_ILMThroughPutRatioBreakdown",function(){return _}),l.d(e,"View_ILMThroughPutRatioBreakdown_0",function(){return W}),l.d(e,"View_ILMThroughPutRatioBreakdown_Host_0",function(){return z}),l.d(e,"ILMThroughPutRatioBreakdownNgFactory",function(){return H});var q=n.Gb(h,[],function(t){return n.Qb([n.Rb(512,n.n,n.vb,[[8,[c.a,d.a,b.a,g.a,m.Cb,m.Pb,m.r,m.rc,m.s,m.Ab,m.Bb,m.Db,m.qd,m.Hb,m.k,m.Ib,m.Nb,m.Ub,m.yb,m.Jb,m.v,m.A,m.e,m.c,m.g,m.d,m.Kb,m.f,m.ec,m.Wb,m.bc,m.ac,m.sc,m.fc,m.lc,m.jc,m.Eb,m.Fb,m.mc,m.Lb,m.nc,m.Mb,m.dc,m.Rb,m.b,m.ic,m.Yb,m.Sb,m.kc,m.y,m.Qb,m.cc,m.hc,m.pc,m.oc,m.xb,m.p,m.q,m.o,m.h,m.j,m.w,m.Zb,m.i,m.m,m.Vb,m.Ob,m.Gb,m.Xb,m.t,m.tc,m.zb,m.n,m.qc,m.a,m.z,m.rd,m.sd,m.x,m.td,m.gc,m.l,m.u,m.ud,m.Tb,H]],[3,n.n],n.J]),n.Rb(4608,p.m,p.l,[n.F,[2,p.u]]),n.Rb(4608,w.c,w.c,[]),n.Rb(4608,w.p,w.p,[]),n.Rb(4608,f.j,f.p,[p.c,n.O,f.n]),n.Rb(4608,f.q,f.q,[f.j,f.o]),n.Rb(5120,f.a,function(t){return[t,new s.tb]},[f.q]),n.Rb(4608,f.m,f.m,[]),n.Rb(6144,f.k,null,[f.m]),n.Rb(4608,f.i,f.i,[f.k]),n.Rb(6144,f.b,null,[f.i]),n.Rb(4608,f.f,f.l,[f.b,n.B]),n.Rb(4608,f.c,f.c,[f.f]),n.Rb(4608,C.c,C.c,[]),n.Rb(4608,C.g,C.b,[]),n.Rb(5120,C.i,C.j,[]),n.Rb(4608,C.h,C.h,[C.c,C.g,C.i]),n.Rb(4608,C.f,C.a,[]),n.Rb(5120,C.d,C.k,[C.h,C.f]),n.Rb(5120,n.b,function(t,e){return[x.j(t,e)]},[p.c,n.O]),n.Rb(4608,I.a,I.a,[]),n.Rb(4608,k.a,k.a,[]),n.Rb(4608,y.a,y.a,[n.n,n.L,n.B,k.a,n.g]),n.Rb(4608,R.c,R.c,[n.n,n.g,n.B]),n.Rb(4608,R.e,R.e,[R.c]),n.Rb(4608,B.l,B.l,[]),n.Rb(4608,B.h,B.g,[]),n.Rb(4608,B.c,B.f,[]),n.Rb(4608,B.j,B.d,[]),n.Rb(4608,B.b,B.a,[]),n.Rb(4608,B.k,B.k,[B.l,B.h,B.c,B.j,B.b,B.m,B.n]),n.Rb(4608,R.i,R.i,[[2,B.k]]),n.Rb(4608,R.r,R.r,[R.L,[2,B.k],R.i]),n.Rb(4608,R.t,R.t,[]),n.Rb(4608,R.w,R.w,[]),n.Rb(1073742336,o.l,o.l,[[2,o.r],[2,o.k]]),n.Rb(1073742336,p.b,p.b,[]),n.Rb(1073742336,w.n,w.n,[]),n.Rb(1073742336,w.l,w.l,[]),n.Rb(1073742336,v.a,v.a,[]),n.Rb(1073742336,S.a,S.a,[]),n.Rb(1073742336,w.e,w.e,[]),n.Rb(1073742336,O.a,O.a,[]),n.Rb(1073742336,B.i,B.i,[]),n.Rb(1073742336,R.b,R.b,[]),n.Rb(1073742336,f.e,f.e,[]),n.Rb(1073742336,f.d,f.d,[]),n.Rb(1073742336,C.e,C.e,[]),n.Rb(1073742336,L.b,L.b,[]),n.Rb(1073742336,P.b,P.b,[]),n.Rb(1073742336,x.c,x.c,[]),n.Rb(1073742336,G.a,G.a,[]),n.Rb(1073742336,A.d,A.d,[]),n.Rb(1073742336,D.c,D.c,[]),n.Rb(1073742336,J.a,J.a,[]),n.Rb(1073742336,T.a,T.a,[[2,x.g],n.O]),n.Rb(1073742336,M.b,M.b,[]),n.Rb(1073742336,N.a,N.a,[]),n.Rb(1073742336,F.b,F.b,[]),n.Rb(1073742336,s.Tb,s.Tb,[]),n.Rb(1073742336,h,h,[]),n.Rb(256,f.n,"XSRF-TOKEN",[]),n.Rb(256,f.o,"X-XSRF-TOKEN",[]),n.Rb(256,"config",{},[]),n.Rb(256,B.m,void 0,[]),n.Rb(256,B.n,void 0,[]),n.Rb(256,"popperDefaults",{},[]),n.Rb(1024,o.i,function(){return[[{path:"",component:a}]]},[])])}),E=[[""]],_=n.Hb({encapsulation:0,styles:E,data:{}});function W(t){return n.dc(0,[n.Zb(402653184,1,{_container:0}),n.Zb(402653184,2,{refreshButton:0}),n.Zb(402653184,3,{optionButton:0}),n.Zb(402653184,4,{noteButton:0}),n.Zb(402653184,5,{movementButton:0}),n.Zb(402653184,6,{messageButton:0}),n.Zb(402653184,7,{closeButton:0}),n.Zb(402653184,8,{loadingImage:0}),n.Zb(402653184,9,{entityCombo:0}),n.Zb(402653184,10,{ccyCombo:0}),n.Zb(402653184,11,{groupCombo:0}),n.Zb(402653184,12,{scenarioCombo:0}),n.Zb(402653184,13,{lastRefTime:0}),n.Zb(402653184,14,{entityLabel:0}),n.Zb(402653184,15,{ccyLabel:0}),n.Zb(402653184,16,{selectedEntity:0}),n.Zb(402653184,17,{selectedCcy:0}),n.Zb(402653184,18,{selectedGroup:0}),n.Zb(402653184,19,{lostConnectionText:0}),n.Zb(402653184,20,{lastRefLabel:0}),n.Zb(402653184,21,{displaycontainer:0}),n.Zb(402653184,22,{numstepper:0}),n.Zb(402653184,23,{foreOutlflowsCheckbox:0}),n.Zb(402653184,24,{actOutlflowsCheckbox:0}),n.Zb(402653184,25,{foreIntlflowsCheckbox:0}),n.Zb(402653184,26,{actInlflowsCheckbox:0}),n.Zb(402653184,27,{ccyThresholdCheckbox:0}),n.Zb(402653184,28,{unsettledOutflows:0}),n.Zb(402653184,29,{dataExport:0}),(t()(),n.Jb(29,0,null,null,140,"SwtModule",[["height","100%"],["width","100%"]],null,[[null,"creationComplete"]],function(t,e,l){var n=!0,i=t.component;"creationComplete"===e&&(n=!1!==i.onLoad()&&n);return n},m.ad,m.hb)),n.Ib(30,4440064,null,0,s.yb,[n.r,s.i],{width:[0,"width"],height:[1,"height"]},{creationComplete:"creationComplete"}),(t()(),n.Jb(31,0,null,0,138,"VBox",[["height","100%"],["paddingBottom","5"],["paddingLeft","5"],["paddingRight","5"],["paddingTop","5"],["width","100%"]],null,null,null,m.od,m.vb)),n.Ib(32,4440064,null,0,s.ec,[n.r,s.i,n.T],{width:[0,"width"],height:[1,"height"],paddingTop:[2,"paddingTop"],paddingBottom:[3,"paddingBottom"],paddingLeft:[4,"paddingLeft"],paddingRight:[5,"paddingRight"]},null),(t()(),n.Jb(33,0,null,0,95,"SwtCanvas",[["height","120"],["width","100%"]],null,null,null,m.Nc,m.U)),n.Ib(34,4440064,null,0,s.db,[n.r,s.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),n.Jb(35,0,null,0,93,"Grid",[["height","100%"],["width","100%"]],null,null,null,m.Cc,m.H)),n.Ib(36,4440064,null,0,s.z,[n.r,s.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),n.Jb(37,0,null,0,21,"GridRow",[],null,null,null,m.Bc,m.J)),n.Ib(38,4440064,null,0,s.B,[n.r,s.i],null,null),(t()(),n.Jb(39,0,null,0,13,"GridItem",[["width","50%"]],null,null,null,m.Ac,m.I)),n.Ib(40,4440064,null,0,s.A,[n.r,s.i],{width:[0,"width"]},null),(t()(),n.Jb(41,0,null,0,3,"GridItem",[["width","120"]],null,null,null,m.Ac,m.I)),n.Ib(42,4440064,null,0,s.A,[n.r,s.i],{width:[0,"width"]},null),(t()(),n.Jb(43,0,null,0,1,"SwtLabel",[["id","entityLabel"],["textDictionaryId","ilmthroughput.entity"]],null,null,null,m.Yc,m.fb)),n.Ib(44,4440064,[[14,4],["entityLabel",4]],0,s.vb,[n.r,s.i],{id:[0,"id"],textDictionaryId:[1,"textDictionaryId"]},null),(t()(),n.Jb(45,0,null,0,3,"GridItem",[],null,null,null,m.Ac,m.I)),n.Ib(46,4440064,null,0,s.A,[n.r,s.i],null,null),(t()(),n.Jb(47,0,null,0,1,"SwtComboBox",[["dataLabel","entityList"],["id","entityCombo"],["width","200"]],null,[[null,"open"],[null,"close"],[null,"change"],["window","mousewheel"]],function(t,e,l){var i=!0,o=t.component;"window:mousewheel"===e&&(i=!1!==n.Tb(t,48).mouseWeelEventHandler(l.target)&&i);"open"===e&&(i=!1!==o.openedCombo(l)&&i);"close"===e&&(i=!1!==o.closedCombo(l)&&i);"change"===e&&(i=!1!==o.entityChangeCombo(l)&&i);return i},m.Pc,m.W)),n.Ib(48,4440064,[[9,4],["entityCombo",4]],0,s.gb,[n.r,s.i],{dataLabel:[0,"dataLabel"],width:[1,"width"],id:[2,"id"]},{open_:"open",close_:"close",change_:"change"}),(t()(),n.Jb(49,0,null,0,3,"GridItem",[],null,null,null,m.Ac,m.I)),n.Ib(50,4440064,null,0,s.A,[n.r,s.i],null,null),(t()(),n.Jb(51,0,null,0,1,"SwtLabel",[["fontWeight","normal"],["id","selectedEntity"],["paddingLeft","10"]],null,null,null,m.Yc,m.fb)),n.Ib(52,4440064,[[16,4],["selectedEntity",4]],0,s.vb,[n.r,s.i],{id:[0,"id"],paddingLeft:[1,"paddingLeft"],fontWeight:[2,"fontWeight"]},null),(t()(),n.Jb(53,0,null,0,5,"GridItem",[["width","50%"]],null,null,null,m.Ac,m.I)),n.Ib(54,4440064,null,0,s.A,[n.r,s.i],{width:[0,"width"]},null),(t()(),n.Jb(55,0,null,0,3,"HBox",[["horizontalAlign","right"],["width","100%"]],null,null,null,m.Dc,m.K)),n.Ib(56,4440064,null,0,s.C,[n.r,s.i],{horizontalAlign:[0,"horizontalAlign"],width:[1,"width"]},null),(t()(),n.Jb(57,0,null,0,1,"SwtLabel",[["id","valueDateLabel"],["textDictionaryId","ilmthroughputbreakdown.currentFilter"],["width","480"]],null,null,null,m.Yc,m.fb)),n.Ib(58,4440064,[["valueDateLabel",4]],0,s.vb,[n.r,s.i],{id:[0,"id"],textDictionaryId:[1,"textDictionaryId"],width:[2,"width"]},null),(t()(),n.Jb(59,0,null,0,27,"GridRow",[],null,null,null,m.Bc,m.J)),n.Ib(60,4440064,null,0,s.B,[n.r,s.i],null,null),(t()(),n.Jb(61,0,null,0,13,"GridItem",[["width","50%"]],null,null,null,m.Ac,m.I)),n.Ib(62,4440064,null,0,s.A,[n.r,s.i],{width:[0,"width"]},null),(t()(),n.Jb(63,0,null,0,3,"GridItem",[["width","120"]],null,null,null,m.Ac,m.I)),n.Ib(64,4440064,null,0,s.A,[n.r,s.i],{width:[0,"width"]},null),(t()(),n.Jb(65,0,null,0,1,"SwtLabel",[["id","ccyLabel"],["textDictionaryId","ilmthroughput.currency"]],null,null,null,m.Yc,m.fb)),n.Ib(66,4440064,[[15,4],["ccyLabel",4]],0,s.vb,[n.r,s.i],{id:[0,"id"],textDictionaryId:[1,"textDictionaryId"]},null),(t()(),n.Jb(67,0,null,0,3,"GridItem",[],null,null,null,m.Ac,m.I)),n.Ib(68,4440064,null,0,s.A,[n.r,s.i],null,null),(t()(),n.Jb(69,0,null,0,1,"SwtComboBox",[["dataLabel","currencyList"],["id","ccyCombo"],["width","200"]],null,[[null,"change"],[null,"open"],[null,"close"],["window","mousewheel"]],function(t,e,l){var i=!0,o=t.component;"window:mousewheel"===e&&(i=!1!==n.Tb(t,70).mouseWeelEventHandler(l.target)&&i);"change"===e&&(i=!1!==o.changeCombo(l)&&i);"open"===e&&(i=!1!==o.openedCombo(l)&&i);"close"===e&&(i=!1!==o.closedCombo(l)&&i);return i},m.Pc,m.W)),n.Ib(70,4440064,[[10,4],["ccyCombo",4]],0,s.gb,[n.r,s.i],{dataLabel:[0,"dataLabel"],width:[1,"width"],id:[2,"id"]},{open_:"open",close_:"close",change_:"change"}),(t()(),n.Jb(71,0,null,0,3,"GridItem",[],null,null,null,m.Ac,m.I)),n.Ib(72,4440064,null,0,s.A,[n.r,s.i],null,null),(t()(),n.Jb(73,0,null,0,1,"SwtLabel",[["fontWeight","normal"],["id","selectedCcy"],["paddingLeft","10"]],null,null,null,m.Yc,m.fb)),n.Ib(74,4440064,[[17,4],["selectedCcy",4]],0,s.vb,[n.r,s.i],{id:[0,"id"],paddingLeft:[1,"paddingLeft"],fontWeight:[2,"fontWeight"]},null),(t()(),n.Jb(75,0,null,0,11,"GridItem",[["width","50%"]],null,null,null,m.Ac,m.I)),n.Ib(76,4440064,null,0,s.A,[n.r,s.i],{width:[0,"width"]},null),(t()(),n.Jb(77,0,null,0,9,"HBox",[["horizontalAlign","right"],["width","100%"]],null,null,null,m.Dc,m.K)),n.Ib(78,4440064,null,0,s.C,[n.r,s.i],{horizontalAlign:[0,"horizontalAlign"],width:[1,"width"]},null),(t()(),n.Jb(79,0,null,0,7,"GridItem",[],null,null,null,m.Ac,m.I)),n.Ib(80,4440064,null,0,s.A,[n.r,s.i],null,null),(t()(),n.Jb(81,0,null,0,1,"SwtCheckBox",[["id","foreOutlflowsCheckbox"],["width","160"]],null,[[null,"change"]],function(t,e,l){var n=!0,i=t.component;"change"===e&&(n=!1!==i.doRefreshPage(!0,"isActualOrForecast")&&n);return n},m.Oc,m.V)),n.Ib(82,4440064,[[23,4],["foreOutlflowsCheckbox",4]],0,s.eb,[n.r,s.i],{id:[0,"id"],width:[1,"width"]},{change_:"change"}),(t()(),n.Jb(83,0,null,0,1,"SwtCheckBox",[["id","actOutlflowsCheckbox"],["width","160"]],null,[[null,"change"]],function(t,e,l){var n=!0,i=t.component;"change"===e&&(n=!1!==i.doRefreshPage(!0,"isActualOrForecast")&&n);return n},m.Oc,m.V)),n.Ib(84,4440064,[[24,4],["actOutlflowsCheckbox",4]],0,s.eb,[n.r,s.i],{id:[0,"id"],width:[1,"width"]},{change_:"change"}),(t()(),n.Jb(85,0,null,0,1,"SwtCheckBox",[["id","ccyThresholdCheckbox"],["selected","true"],["width","160"]],null,[[null,"change"]],function(t,e,l){var n=!0,i=t.component;"change"===e&&(n=!1!==i.doRefreshPage(!0)&&n);return n},m.Oc,m.V)),n.Ib(86,4440064,[[27,4],["ccyThresholdCheckbox",4]],0,s.eb,[n.r,s.i],{id:[0,"id"],width:[1,"width"],selected:[2,"selected"]},{change_:"change"}),(t()(),n.Jb(87,0,null,0,27,"GridRow",[["id","selectGroup"]],null,null,null,m.Bc,m.J)),n.Ib(88,4440064,[["selectGroup",4]],0,s.B,[n.r,s.i],{id:[0,"id"]},null),(t()(),n.Jb(89,0,null,0,13,"GridItem",[["width","50%"]],null,null,null,m.Ac,m.I)),n.Ib(90,4440064,null,0,s.A,[n.r,s.i],{width:[0,"width"]},null),(t()(),n.Jb(91,0,null,0,3,"GridItem",[["width","120"]],null,null,null,m.Ac,m.I)),n.Ib(92,4440064,null,0,s.A,[n.r,s.i],{width:[0,"width"]},null),(t()(),n.Jb(93,0,null,0,1,"SwtLabel",[["id","groupcomboLabel"],["textDictionaryId","ilmthroughput.group"]],null,null,null,m.Yc,m.fb)),n.Ib(94,4440064,[["groupcomboLabel",4]],0,s.vb,[n.r,s.i],{id:[0,"id"],textDictionaryId:[1,"textDictionaryId"]},null),(t()(),n.Jb(95,0,null,0,3,"GridItem",[],null,null,null,m.Ac,m.I)),n.Ib(96,4440064,null,0,s.A,[n.r,s.i],null,null),(t()(),n.Jb(97,0,null,0,1,"SwtComboBox",[["dataLabel","AcctGrpList"],["id","groupCombo"],["width","200"]],null,[[null,"change"],["window","mousewheel"]],function(t,e,l){var i=!0,o=t.component;"window:mousewheel"===e&&(i=!1!==n.Tb(t,98).mouseWeelEventHandler(l.target)&&i);"change"===e&&(i=!1!==o.changeCombo(l)&&i);return i},m.Pc,m.W)),n.Ib(98,4440064,[[11,4],["groupCombo",4]],0,s.gb,[n.r,s.i],{dataLabel:[0,"dataLabel"],width:[1,"width"],id:[2,"id"]},{change_:"change"}),(t()(),n.Jb(99,0,null,0,3,"GridItem",[],null,null,null,m.Ac,m.I)),n.Ib(100,4440064,null,0,s.A,[n.r,s.i],null,null),(t()(),n.Jb(101,0,null,0,1,"SwtLabel",[["fontWeight","normal"],["id","selectedGroup"],["paddingLeft","10"]],null,null,null,m.Yc,m.fb)),n.Ib(102,4440064,[[18,4],["selectedGroup",4]],0,s.vb,[n.r,s.i],{id:[0,"id"],paddingLeft:[1,"paddingLeft"],fontWeight:[2,"fontWeight"]},null),(t()(),n.Jb(103,0,null,0,11,"GridItem",[["width","50%"]],null,null,null,m.Ac,m.I)),n.Ib(104,4440064,null,0,s.A,[n.r,s.i],{width:[0,"width"]},null),(t()(),n.Jb(105,0,null,0,9,"HBox",[["horizontalAlign","right"],["width","100%"]],null,null,null,m.Dc,m.K)),n.Ib(106,4440064,null,0,s.C,[n.r,s.i],{horizontalAlign:[0,"horizontalAlign"],width:[1,"width"]},null),(t()(),n.Jb(107,0,null,0,7,"GridItem",[],null,null,null,m.Ac,m.I)),n.Ib(108,4440064,null,0,s.A,[n.r,s.i],null,null),(t()(),n.Jb(109,0,null,0,1,"SwtCheckBox",[["id","foreIntlflowsCheckbox"],["width","160"]],null,[[null,"change"]],function(t,e,l){var n=!0,i=t.component;"change"===e&&(n=!1!==i.doRefreshPage(!0,"isActualOrForecast")&&n);return n},m.Oc,m.V)),n.Ib(110,4440064,[[25,4],["foreIntlflowsCheckbox",4]],0,s.eb,[n.r,s.i],{id:[0,"id"],width:[1,"width"]},{change_:"change"}),(t()(),n.Jb(111,0,null,0,1,"SwtCheckBox",[["id","actInlflowsCheckbox"],["width","160"]],null,[[null,"change"]],function(t,e,l){var n=!0,i=t.component;"change"===e&&(n=!1!==i.doRefreshPage(!0,"isActualOrForecast")&&n);return n},m.Oc,m.V)),n.Ib(112,4440064,[[26,4],["actInlflowsCheckbox",4]],0,s.eb,[n.r,s.i],{id:[0,"id"],width:[1,"width"]},{change_:"change"}),(t()(),n.Jb(113,0,null,0,1,"SwtCheckBox",[["id","unsettledOutflows"],["width","160"]],null,[[null,"change"]],function(t,e,l){var n=!0,i=t.component;"change"===e&&(n=!1!==i.doRefreshPage(!0,"isUnsettled")&&n);return n},m.Oc,m.V)),n.Ib(114,4440064,[[28,4],["unsettledOutflows",4]],0,s.eb,[n.r,s.i],{id:[0,"id"],width:[1,"width"]},{change_:"change"}),(t()(),n.Jb(115,0,null,0,13,"GridRow",[],null,null,null,m.Bc,m.J)),n.Ib(116,4440064,null,0,s.B,[n.r,s.i],null,null),(t()(),n.Jb(117,0,null,0,5,"GridItem",[["width","50%"]],null,null,null,m.Ac,m.I)),n.Ib(118,4440064,null,0,s.A,[n.r,s.i],{width:[0,"width"]},null),(t()(),n.Jb(119,0,null,0,3,"HBox",[["horizontalAlign","right"],["width","100%"]],null,null,null,m.Dc,m.K)),n.Ib(120,4440064,null,0,s.C,[n.r,s.i],{horizontalAlign:[0,"horizontalAlign"],width:[1,"width"]},null),(t()(),n.Jb(121,0,null,0,1,"SwtCommonGridPagination",[],null,null,null,m.Qc,m.Y)),n.Ib(122,2211840,[[22,4],["numstepper",4]],0,s.ib,[f.c,n.r],null,null),(t()(),n.Jb(123,0,null,0,5,"GridItem",[["width","50%"]],null,null,null,m.Ac,m.I)),n.Ib(124,4440064,null,0,s.A,[n.r,s.i],{width:[0,"width"]},null),(t()(),n.Jb(125,0,null,0,3,"HBox",[["horizontalAlign","right"],["paddingRight","30"],["width","100%"]],null,null,null,m.Dc,m.K)),n.Ib(126,4440064,null,0,s.C,[n.r,s.i],{horizontalAlign:[0,"horizontalAlign"],width:[1,"width"],paddingRight:[2,"paddingRight"]},null),(t()(),n.Jb(127,0,null,0,1,"SwtComboBox",[["dataLabel","scenarioList"],["id","scenarioCombo"],["width","350"]],null,[[null,"change"],["window","mousewheel"]],function(t,e,l){var i=!0,o=t.component;"window:mousewheel"===e&&(i=!1!==n.Tb(t,128).mouseWeelEventHandler(l.target)&&i);"change"===e&&(i=!1!==o.changeCombo(l)&&i);return i},m.Pc,m.W)),n.Ib(128,4440064,[[12,4],["scenarioCombo",4]],0,s.gb,[n.r,s.i],{dataLabel:[0,"dataLabel"],width:[1,"width"],id:[2,"id"]},{change_:"change"}),(t()(),n.Jb(129,0,null,0,3,"VBox",[["height","100%"],["verticalGap","1"],["width","100%"]],null,null,null,m.od,m.vb)),n.Ib(130,4440064,null,0,s.ec,[n.r,s.i,n.T],{verticalGap:[0,"verticalGap"],width:[1,"width"],height:[2,"height"]},null),(t()(),n.Jb(131,0,null,0,1,"SwtCanvas",[["border","false"],["height","100%"],["id","displaycontainer"],["styleName","canvasWithGreyBorder"],["width","100%"]],null,null,null,m.Nc,m.U)),n.Ib(132,4440064,[[21,4],["displaycontainer",4]],0,s.db,[n.r,s.i],{id:[0,"id"],styleName:[1,"styleName"],width:[2,"width"],height:[3,"height"],border:[4,"border"]},null),(t()(),n.Jb(133,0,null,0,36,"SwtCanvas",[["height","40"],["marginBottom","0"],["width","100%"]],null,null,null,m.Nc,m.U)),n.Ib(134,4440064,null,0,s.db,[n.r,s.i],{width:[0,"width"],height:[1,"height"],marginBottom:[2,"marginBottom"]},null),(t()(),n.Jb(135,0,null,0,34,"HBox",[["top","1"],["width","100%"]],null,null,null,m.Dc,m.K)),n.Ib(136,4440064,null,0,s.C,[n.r,s.i],{top:[0,"top"],width:[1,"width"]},null),(t()(),n.Jb(137,0,null,0,13,"HBox",[["paddingLeft","5"],["width","100%"]],null,null,null,m.Dc,m.K)),n.Ib(138,4440064,null,0,s.C,[n.r,s.i],{width:[0,"width"],paddingLeft:[1,"paddingLeft"]},null),(t()(),n.Jb(139,0,null,0,1,"SwtButton",[["enabled","false"],["id","refreshButton"]],null,[[null,"click"]],function(t,e,l){var n=!0,i=t.component;"click"===e&&(n=!1!==i.doRefreshPage(!0)&&n);return n},m.Mc,m.T)),n.Ib(140,4440064,[[2,4],["refreshButton",4]],0,s.cb,[n.r,s.i],{id:[0,"id"],enabled:[1,"enabled"]},{onClick_:"click"}),(t()(),n.Jb(141,0,null,0,1,"SwtButton",[["enabled","false"],["id","noteButton"]],null,[[null,"click"]],function(t,e,l){var n=!0,i=t.component;"click"===e&&(n=!1!==i.openNotes()&&n);return n},m.Mc,m.T)),n.Ib(142,4440064,[[4,4],["noteButton",4]],0,s.cb,[n.r,s.i],{id:[0,"id"],enabled:[1,"enabled"]},{onClick_:"click"}),(t()(),n.Jb(143,0,null,0,1,"SwtButton",[["enabled","false"],["id","movementButton"]],null,[[null,"click"]],function(t,e,l){var n=!0,i=t.component;"click"===e&&(n=!1!==i.openMovement()&&n);return n},m.Mc,m.T)),n.Ib(144,4440064,[[5,4],["movementButton",4]],0,s.cb,[n.r,s.i],{id:[0,"id"],enabled:[1,"enabled"]},{onClick_:"click"}),(t()(),n.Jb(145,0,null,0,1,"SwtButton",[["enabled","false"],["id","messageButton"]],null,[[null,"click"]],function(t,e,l){var n=!0,i=t.component;"click"===e&&(n=!1!==i.openMessages()&&n);return n},m.Mc,m.T)),n.Ib(146,4440064,[[6,4],["messageButton",4]],0,s.cb,[n.r,s.i],{id:[0,"id"],enabled:[1,"enabled"]},{onClick_:"click"}),(t()(),n.Jb(147,0,null,0,1,"SwtButton",[["id","optionButton"]],null,[[null,"click"]],function(t,e,l){var n=!0,i=t.component;"click"===e&&(n=!1!==i.fontSettingHandler()&&n);return n},m.Mc,m.T)),n.Ib(148,4440064,[[3,4],["optionButton",4]],0,s.cb,[n.r,s.i],{id:[0,"id"]},{onClick_:"click"}),(t()(),n.Jb(149,0,null,0,1,"SwtButton",[["id","closeButton"]],null,[[null,"click"]],function(t,e,l){var n=!0,i=t.component;"click"===e&&(n=!1!==i.closeHandler()&&n);return n},m.Mc,m.T)),n.Ib(150,4440064,[[7,4],["closeButton",4]],0,s.cb,[n.r,s.i],{id:[0,"id"]},{onClick_:"click"}),(t()(),n.Jb(151,0,null,0,3,"HBox",[["horizontalAlign","right"],["paddingRight","10"],["width","100%"]],null,null,null,m.Dc,m.K)),n.Ib(152,4440064,null,0,s.C,[n.r,s.i],{horizontalAlign:[0,"horizontalAlign"],width:[1,"width"],paddingRight:[2,"paddingRight"]},null),(t()(),n.Jb(153,0,null,0,1,"SwtLabel",[["color","red"],["height","16"],["id","lostConnectionText"],["right","300"],["text","CONNECTION ERROR"],["visible","false"]],null,null,null,m.Yc,m.fb)),n.Ib(154,4440064,[[19,4],["lostConnectionText",4]],0,s.vb,[n.r,s.i],{id:[0,"id"],right:[1,"right"],height:[2,"height"],visible:[3,"visible"],text:[4,"text"],color:[5,"color"]},null),(t()(),n.Jb(155,0,null,0,5,"HBox",[["horizontalAlign","right"],["paddingRight","10"]],null,null,null,m.Dc,m.K)),n.Ib(156,4440064,null,0,s.C,[n.r,s.i],{horizontalAlign:[0,"horizontalAlign"],paddingRight:[1,"paddingRight"]},null),(t()(),n.Jb(157,0,null,0,1,"SwtLabel",[["fontWeight","normal"],["height","16"],["id","lastRefLabel"],["right","220"],["textDictionaryId","screen.lastRefresh"]],null,null,null,m.Yc,m.fb)),n.Ib(158,4440064,[[20,4],["lastRefLabel",4]],0,s.vb,[n.r,s.i],{id:[0,"id"],right:[1,"right"],textDictionaryId:[2,"textDictionaryId"],height:[3,"height"],fontWeight:[4,"fontWeight"]},null),(t()(),n.Jb(159,0,null,0,1,"SwtLabel",[["fontWeight","normal"],["height","16"],["id","lastRefTime"],["right","180"],["styleName","labelLeftRefTime"]],null,null,null,m.Yc,m.fb)),n.Ib(160,4440064,[[13,4],["lastRefTime",4]],0,s.vb,[n.r,s.i],{id:[0,"id"],right:[1,"right"],styleName:[2,"styleName"],height:[3,"height"],fontWeight:[4,"fontWeight"]},null),(t()(),n.Jb(161,0,null,0,8,"HBox",[["horizontalAlign","right"],["paddingRight","10"]],null,null,null,m.Dc,m.K)),n.Ib(162,4440064,null,0,s.C,[n.r,s.i],{horizontalAlign:[0,"horizontalAlign"],paddingRight:[1,"paddingRight"]},null),(t()(),n.Jb(163,0,null,0,2,"div",[],null,null,null,null,null)),(t()(),n.Jb(164,0,null,null,1,"DataExportMultiPage",[["enabled","false"],["id","dataExport"]],null,null,null,m.yc,m.F)),n.Ib(165,4440064,[[29,4],["dataExport",4]],0,s.q,[s.i,n.r],{id:[0,"id"],enabled:[1,"enabled"]},null),(t()(),n.Jb(166,0,null,0,1,"SwtHelpButton",[],null,[[null,"click"]],function(t,e,l){var n=!0,i=t.component;"click"===e&&(n=!1!==i.doHelp()&&n);return n},m.Wc,m.db)),n.Ib(167,4440064,null,0,s.rb,[n.r,s.i],null,{onClick_:"click"}),(t()(),n.Jb(168,0,null,0,1,"SwtLoadingImage",[],null,null,null,m.Zc,m.gb)),n.Ib(169,114688,[[8,4],["loadingImage",4]],0,s.xb,[n.r],null,null)],function(t,e){t(e,30,0,"100%","100%");t(e,32,0,"100%","100%","5","5","5","5");t(e,34,0,"100%","120");t(e,36,0,"100%","100%"),t(e,38,0);t(e,40,0,"50%");t(e,42,0,"120");t(e,44,0,"entityLabel","ilmthroughput.entity"),t(e,46,0);t(e,48,0,"entityList","200","entityCombo"),t(e,50,0);t(e,52,0,"selectedEntity","10","normal");t(e,54,0,"50%");t(e,56,0,"right","100%");t(e,58,0,"valueDateLabel","ilmthroughputbreakdown.currentFilter","480"),t(e,60,0);t(e,62,0,"50%");t(e,64,0,"120");t(e,66,0,"ccyLabel","ilmthroughput.currency"),t(e,68,0);t(e,70,0,"currencyList","200","ccyCombo"),t(e,72,0);t(e,74,0,"selectedCcy","10","normal");t(e,76,0,"50%");t(e,78,0,"right","100%"),t(e,80,0);t(e,82,0,"foreOutlflowsCheckbox","160");t(e,84,0,"actOutlflowsCheckbox","160");t(e,86,0,"ccyThresholdCheckbox","160","true");t(e,88,0,"selectGroup");t(e,90,0,"50%");t(e,92,0,"120");t(e,94,0,"groupcomboLabel","ilmthroughput.group"),t(e,96,0);t(e,98,0,"AcctGrpList","200","groupCombo"),t(e,100,0);t(e,102,0,"selectedGroup","10","normal");t(e,104,0,"50%");t(e,106,0,"right","100%"),t(e,108,0);t(e,110,0,"foreIntlflowsCheckbox","160");t(e,112,0,"actInlflowsCheckbox","160");t(e,114,0,"unsettledOutflows","160"),t(e,116,0);t(e,118,0,"50%");t(e,120,0,"right","100%"),t(e,122,0);t(e,124,0,"50%");t(e,126,0,"right","100%","30");t(e,128,0,"scenarioList","350","scenarioCombo");t(e,130,0,"1","100%","100%");t(e,132,0,"displaycontainer","canvasWithGreyBorder","100%","100%","false");t(e,134,0,"100%","40","0");t(e,136,0,"1","100%");t(e,138,0,"100%","5");t(e,140,0,"refreshButton","false");t(e,142,0,"noteButton","false");t(e,144,0,"movementButton","false");t(e,146,0,"messageButton","false");t(e,148,0,"optionButton");t(e,150,0,"closeButton");t(e,152,0,"right","100%","10");t(e,154,0,"lostConnectionText","300","16","false","CONNECTION ERROR","red");t(e,156,0,"right","10");t(e,158,0,"lastRefLabel","220","screen.lastRefresh","16","normal");t(e,160,0,"lastRefTime","180","labelLeftRefTime","16","normal");t(e,162,0,"right","10");t(e,165,0,"dataExport","false"),t(e,167,0),t(e,169,0)},null)}function z(t){return n.dc(0,[(t()(),n.Jb(0,0,null,null,1,"ilmthrough-put-ratio-breakdown",[],null,null,null,W,_)),n.Ib(1,4440064,null,0,a,[s.i,n.r],null,null)],function(t,e){t(e,1,0)},null)}var H=n.Fb("ilmthrough-put-ratio-breakdown",a,z,{maxChars:"maxChars",restrict:"restrict",id:"id",dropShadowEnabled:"dropShadowEnabled",cornerRadius:"cornerRadius",borderThickness:"borderThickness",borderStyle:"borderStyle",borderColor:"borderColor",backGroundColor:"backGroundColor",right:"right",left:"left",bottom:"bottom",top:"top",horizontalGap:"horizontalGap",verticalGap:"verticalGap",textAlign:"textAlign",toolTip:"toolTip",toolTipPreviousValue:"toolTipPreviousValue",textDictionaryId:"tooltipDictionaryId",name:"name",styleName:"styleName",horizontalAlign:"horizontalAlign",verticalAlign:"verticalAlign",width:"width",showScrollBar:"showScrollBar",height:"height",minHeight:"minHeight",minWidth:"minWidth",maxHeight:"maxHeight",maxWidth:"maxWidth",includeInLayout:"includeInLayout",visible:"visible",enabled:"enabled",paddingTop:"paddingTop",paddingBottom:"paddingBottom",paddingLeft:"paddingLeft",paddingRight:"paddingRight",marginTop:"marginTop",marginBottom:"marginBottom",marginLeft:"marginLeft",marginRight:"marginRight",contextMenu:"contextMenu"},{onClick_:"click",dbClick_:"dbClick",doubleClick_:"doubleClick",itemDoubleClick_:"itemDoubleClick",onKeyDown_:"keyDown",onKeyUp_:"keyUp",mouseUp_:"mouseUp",mouseOver_:"mouseOver",mouseDown_:"mouseDown",mouseEnter_:"mouseEnter",mouseLeave_:"mouseLeave",mouseOut_:"mouseOut",mouseIn_:"mouseIn",mouseMove_:"mouseMove",focus_:"focus",focusIn_:"focusIn",onFocusOut_:"focusOut",keyFocusChange_:"keyFocusChange",change_:"change",onSpyChange:"onSpyChange",onSpyNoChange:"onSpyNoChange",scroll_:"scroll",creationComplete:"creationComplete",preinitialize:"preinitialize"},[])}}]);