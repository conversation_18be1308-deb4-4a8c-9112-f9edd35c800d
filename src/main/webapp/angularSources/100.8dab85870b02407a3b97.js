(window.webpackJsonp=window.webpackJsonp||[]).push([[100],{"69Bu":function(t,e,n){"use strict";n.r(e);var i=n("CcnG"),s=n("mrSG"),a=n("mjvp"),o=n("447K"),l=n("ZYCi"),d=n("kAxm"),h=n("6blF"),r=(n("0GgQ"),n("ik3b")),c=n("7EaE"),u=function(t){function e(e,n){var i=t.call(this,n,e)||this;return i.commonService=e,i.element=n,i.jsonReader=new o.L,i.inputData=new o.G(i.commonService),i.alertingData=new o.G(i.commonService),i.logicUpdate=new o.G(i.commonService),i.updateFontSize=new o.G(i.commonService),i.changeMatchService=new o.G(i.commonService),i.baseURL=o.Wb.getBaseURL(),i.requestURL="",i.actionMethod="",i.actionPath="",i.matchIds="",i.inScreenMatchId="",i.matchType="",i.requestParams=[],i.requestParameter=[],i.movIds=[],i.invalidComms="",i.fullAccess=null,i.removeFlagUnmatch=!1,i.suspendFlag=!1,i.confirmFlagOk=!1,i.reconcileFlag=!1,i.predictFlagConfirm=!1,i.selectedMovements=[],i.previousSelectedIndices=[],i.suspendFlagListener=!1,i.confirmFlagListener=!1,i.reconcileFlagLisner=!1,i.refreshFlag=!1,i.matchHash="",i.showJsonPopup=null,i.screenVersion=new o.V(i.commonService),i.screenName="Movement Match Summary Display",i.versionNumber="1.1.0029",i.versionDate="04/11/2019",i.fontValue="",i.fontLabel="",i.fontRequest="",i.currentFontSize="",i.tempFontSize="",i.subScreenName=null,i.reportType=null,i.serverBusyFlag=!1,i.acctAccessFlag=!1,i.posTotal="Position Totals",i.errorLocation=0,i.moduleId="Predict",i.tooltipMatchId=null,i.tooltipMvtId=null,i.tooltipFacilityId=null,i.tooltipSelectedDate=null,i.selectedNodeId=null,i.treeLevelValue=null,i.tooltipOtherParams=[],i.ccyTol=null,i.suppMatchDiffWarning=null,i.currencyPattern=null,i.lastSelectedTooltipParams=null,i.eventsCreated=!1,i.customTooltip=null,i.isActionRun=!1,i.isRefreshRun=!1,i.swtAlert=new o.bb(e),window.Main=i,i}return s.d(e,t),e.prototype.ngOnInit=function(){instanceElement=this,this.mmsdGrid=this.gridCanvas.addChild(o.hb),this.mmsdGrid.uniqueColumn="movement",this.mmsdGrid.lockedColumnCount=2,this.mmsdGrid.clientSideFilter=!0,this.mmsdGrid.onFilterChanged=this.filterUpdate.bind(this),this.matchLabel.text=o.Wb.getPredictMessage("matchQuality.matchId",null),this.matchIdText.toolTip=o.Wb.getPredictMessage("matchQuality.matchId",null),this.currencyLabel.text=o.Wb.getPredictMessage("matchQuality.currencyCode",null),this.entityLabel.text=o.Wb.getPredictMessage("matchQuality.entityId",null),this.entityLabel.toolTip=o.Wb.getPredictMessage("matchQuality.entityId",null),this.externalLabel.text=o.Wb.getPredictMessage("matchQuality.posTotalExternal",null),this.internalLabel.text=o.Wb.getPredictMessage("matchQuality.posTotalInternal",null),this.posTotal=o.Wb.getPredictMessage("matchQuality.posTotal",null),this.ccyTolLabel.text=o.Wb.getPredictMessage("matchQuality.ccyTolerance",null),this.diffLabel.text=o.Wb.getPredictMessage("matchQuality.difference",null)},e.prototype.ngOnDestroy=function(){instanceElement=null},e.prototype.setButtonsProperties=function(){var t=this;this.noteButton.id="noteButton",this.noteButton.toolTip=o.Wb.getPredictMessage("tooltip.matchNotes",null),this.noteButton.label=o.Wb.getPredictMessage("button.notes",null),this.noteButton.click=function(){t.openNotes()},this.logButton.id="logButton",this.logButton.toolTip=o.Wb.getPredictMessage("tooltip.logSelMvm",null),this.logButton.label=o.Wb.getPredictMessage("button.log",null),this.logButton.click=function(){t.openLog()},this.unMatchButton.id="unMatchButton",this.unMatchButton.toolTip=o.Wb.getPredictMessage("tooltip.unMatch",null),this.unMatchButton.label=o.Wb.getPredictMessage("button.unmatch",null),this.unMatchButton.click=function(){t.openUnMatch()},this.mvmntButton.id="mvmntButton",this.mvmntButton.toolTip=o.Wb.getPredictMessage("tooltip.showSelMovDetail",null),this.mvmntButton.label=o.Wb.getPredictMessage("button.mvmnt",null),this.mvmntButton.click=function(e){t.openMvmnt()},this.reconButton.id="reconButton",this.reconButton.toolTip=o.Wb.getPredictMessage("tooltip.reconMatch",null),this.reconButton.label=o.Wb.getPredictMessage("button.reconcile",null),this.reconButton.click=function(){t.reconcileMvmnt()},this.removeButton.id="removeButton",this.removeButton.toolTip=o.Wb.getPredictMessage("tooltip.removeSelMov",null),this.removeButton.label=o.Wb.getPredictMessage("button.remove",null),this.removeButton.click=function(){t.removeMvmnt()},this.addButton.id="addButton",this.addButton.toolTip=o.Wb.getPredictMessage("tooltip.addMov",null),this.addButton.label=o.Wb.getPredictMessage("button.add",null),this.addButton.click=function(){t.addMvmnt()},this.optionsButton.id="optionsButton",this.optionsButton.toolTip=o.Wb.getPredictMessage("tooltip.options",null),this.optionsButton.label=o.Wb.getPredictMessage("button.options",null),this.optionsButton.click=function(){t.fontSettingHandler()},this.closeButton.id="closeButton",this.closeButton.toolTip=o.Wb.getPredictMessage("button.close",null),this.closeButton.label=o.Wb.getPredictMessage("button.close",null),this.closeButton.click=function(e){t.closeHandler(e)}},e.prototype.setAllButtonsStatus=function(){var t=this;this.prevButton.id="prevButton",this.prevButton.toolTip=o.Wb.getPredictMessage("tooltip.previousMatch",null),this.prevButton.label=o.Wb.getPredictMessage("button.previous",null),this.prevButton.click=function(){t.openPreviousRecord()},this.nextButton.id="nextButton",this.nextButton.toolTip=o.Wb.getPredictMessage("tooltip.nextMatch",null),this.nextButton.label=o.Wb.getPredictMessage("button.next",null),this.nextButton.click=function(){t.openNextRecord()},this.setButtonsProperties()},e.prototype.setButtonsStatus=function(){this.noteButton=this.buttonsContainer.addChild(o.cb),this.logButton=this.buttonsContainer.addChild(o.cb),this.mvmntButton=this.buttonsContainer.addChild(o.cb),this.unMatchButton=this.buttonsContainer.addChild(o.cb),this.suspendButton=this.buttonsContainer.addChild(o.cb),this.confirmButton=this.buttonsContainer.addChild(o.cb),this.reconButton=this.buttonsContainer.addChild(o.cb),this.removeButton=this.buttonsContainer.addChild(o.cb),this.addButton=this.buttonsContainer.addChild(o.cb),this.optionsButton=this.buttonsContainer.addChild(o.cb),this.closeButton=this.buttonsContainer.addChild(o.cb),this.setButtonsProperties()},e.prototype.onLoad=function(){var t=this;this.initializeMenus(),o.v.subscribe(function(e){t.export(e)}),this.currentUser=o.x.call("eval","currentUser"),this.matchType=o.x.call("eval","match");var e=o.x.call("eval","calledFrom");this.changeMatchService.cbStart=this.startOfCommsAction.bind(this),this.changeMatchService.cbStop=this.endOfCommsAction.bind(this),this.inputData.cbStart=this.startOfComms.bind(this),this.inputData.cbStop=this.endOfComms.bind(this),this.inputData.cbResult=function(e){t.inputDataResult(e)},this.inputData.cbFault=this.inputDataFault.bind(this),this.inputData.encodeURL=!1,this.actionPath="movementmatchdisplay.do?",this.actionMethod="method=flexdisplay",this.requestParams=[],this.mmsdGrid.onRowClick=function(e){t.cellLogic(e)},h.a.fromEvent(document.body,"click").subscribe(function(e){t.positionX=e.clientX,t.positionY=e.clientY}),this.mmsdGrid.ITEM_CLICK.subscribe(function(e){setTimeout(function(){t.itemClickFunction(e)},0)}),"manual"!=this.matchType?(this.buttonsContainer.removeAllChildren(),"scenarioSummary"==e?(this.actionMethod=this.actionMethod+"&day="+o.x.call("eval","day")+"&matchCount="+o.x.call("eval","matchCount")+"&currencyCode="+o.x.call("eval","currencyCode")+"&status="+o.x.call("eval","status")+"&date="+o.x.call("eval","date")+"&entityId="+o.x.call("eval","entityId")+"&quality="+o.x.call("eval","quality")+"&menuAccessId="+o.x.call("eval","menuAccessId")+"&applyCurrencyThreshold="+o.x.call("eval","applyCurrencyThreshold")+"&noIncludedMovementMatches="+o.x.call("eval","noIncludedMovementMatches")+"&dateTabIndFlag="+o.x.call("eval","dateTabIndFlag")+"&dateTabInd="+o.x.call("eval","dateTabInd")+"&valueDate="+o.x.call("eval","valueDate")+"&scenarioId="+o.x.call("eval","scenarioId")+"&currGrp="+o.x.call("eval","currGrp")+"&calledFrom="+o.x.call("eval","calledFrom"),this.inputData.url=this.baseURL+this.actionPath+this.actionMethod,this.inputData.send(this.requestParams),this.prevButton=this.buttonsContainer.addChild(o.cb),this.nextButton=this.buttonsContainer.addChild(o.cb),this.noteButton=this.buttonsContainer.addChild(o.cb),this.logButton=this.buttonsContainer.addChild(o.cb),this.mvmntButton=this.buttonsContainer.addChild(o.cb),this.unMatchButton=this.buttonsContainer.addChild(o.cb),this.suspendButton=this.buttonsContainer.addChild(o.cb),this.confirmButton=this.buttonsContainer.addChild(o.cb),this.reconButton=this.buttonsContainer.addChild(o.cb),this.removeButton=this.buttonsContainer.addChild(o.cb),this.addButton=this.buttonsContainer.addChild(o.cb),this.optionsButton=this.buttonsContainer.addChild(o.cb),this.closeButton=this.buttonsContainer.addChild(o.cb),this.confirmButton.id="confirmButton",this.confirmButton.toolTip=o.Wb.getPredictMessage("tooltip.ConfMatch",null),this.confirmButton.label=o.Wb.getPredictMessage("button.confirm",null),this.confirmButton.click=function(){t.confirmMvmnt()},this.suspendButton.id="suspendButton",this.suspendButton.toolTip=o.Wb.getPredictMessage("tooltip.suspMatch",null),this.suspendButton.label=o.Wb.getPredictMessage("button.suspend",null),this.suspendButton.click=function(e){t.suspendMvmnt()},this.setAllButtonsStatus(),this.prevButton.enabled=!1,this.mvmntButton.enabled=!1,this.removeButton.enabled=!1,this.nextButton.enabled=!1,this.noteButton.enabled=!1,this.logButton.enabled=!1,this.unMatchButton.enabled=!1,this.suspendButton.enabled=!1,this.confirmButton.enabled=!1,this.reconButton.enabled=!1,this.addButton.enabled=!1):(this.actionMethod=this.actionMethod+"&day="+o.x.call("eval","day")+"&matchCount="+o.x.call("eval","matchCount")+"&currencyCode="+o.x.call("eval","currencyCode")+"&status="+o.x.call("eval","status")+"&date="+o.x.call("eval","date")+"&entityId="+o.x.call("eval","entityId")+"&quality="+o.x.call("eval","quality")+"&menuAccessId="+o.x.call("eval","menuAccessId")+"&applyCurrencyThreshold="+o.x.call("eval","applyCurrencyThreshold")+"&noIncludedMovementMatches="+o.x.call("eval","noIncludedMovementMatches")+"&dateTabIndFlag="+o.x.call("eval","dateTabIndFlag")+"&dateTabInd="+o.x.call("eval","dateTabInd")+"&valueDate="+o.x.call("eval","valueDate"),this.logicUpdate.cbStart=this.startOfComms.bind(this),this.logicUpdate.cbStop=this.endOfComms.bind(this),this.logicUpdate.cbResult=function(e){t.logicUpdateResult(e)},"M"==o.x.call("eval","status")&&(this.prevButton=this.buttonsContainer.addChild(o.cb),this.nextButton=this.buttonsContainer.addChild(o.cb),this.noteButton=this.buttonsContainer.addChild(o.cb),this.logButton=this.buttonsContainer.addChild(o.cb),this.mvmntButton=this.buttonsContainer.addChild(o.cb),this.unMatchButton=this.buttonsContainer.addChild(o.cb),this.suspendButton=this.buttonsContainer.addChild(o.cb),this.confirmButton=this.buttonsContainer.addChild(o.cb),this.reconButton=this.buttonsContainer.addChild(o.cb),this.removeButton=this.buttonsContainer.addChild(o.cb),this.addButton=this.buttonsContainer.addChild(o.cb),this.optionsButton=this.buttonsContainer.addChild(o.cb),this.closeButton=this.buttonsContainer.addChild(o.cb),this.confirmButton.id="confirmButton",this.confirmButton.toolTip=o.Wb.getPredictMessage("tooltip.ConfMatch",null),this.confirmButton.label=o.Wb.getPredictMessage("button.confirm",null),this.confirmButton.click=function(){t.confirmMvmnt()},this.suspendButton.id="suspendButton",this.suspendButton.toolTip=o.Wb.getPredictMessage("tooltip.suspMatch",null),this.suspendButton.label=o.Wb.getPredictMessage("button.suspend",null),this.suspendButton.click=function(e){t.suspendMvmnt()},this.setAllButtonsStatus(),this.prevButton.enabled=!1,this.mvmntButton.enabled=!1,this.removeButton.enabled=!1,this.nextButton.enabled=!1,this.noteButton.enabled=!1,this.logButton.enabled=!1,this.unMatchButton.enabled=!1,this.suspendButton.enabled=!1,this.confirmButton.enabled=!1,this.reconButton.enabled=!1,this.addButton.enabled=!1),"S"==o.x.call("eval","status")&&(this.prevButton=this.buttonsContainer.addChild(o.cb),this.nextButton=this.buttonsContainer.addChild(o.cb),this.noteButton=this.buttonsContainer.addChild(o.cb),this.logButton=this.buttonsContainer.addChild(o.cb),this.mvmntButton=this.buttonsContainer.addChild(o.cb),this.unMatchButton=this.buttonsContainer.addChild(o.cb),this.confirmButton=this.buttonsContainer.addChild(o.cb),this.reconButton=this.buttonsContainer.addChild(o.cb),this.removeButton=this.buttonsContainer.addChild(o.cb),this.addButton=this.buttonsContainer.addChild(o.cb),this.optionsButton=this.buttonsContainer.addChild(o.cb),this.closeButton=this.buttonsContainer.addChild(o.cb),this.confirmButton.id="confirmButton",this.confirmButton.toolTip=o.Wb.getPredictMessage("tooltip.ConfMatch",null),this.confirmButton.label=o.Wb.getPredictMessage("button.confirm",null),this.confirmButton.click=function(){t.confirmMvmnt()},this.setAllButtonsStatus(),this.prevButton.enabled=!1,this.mvmntButton.enabled=!1,this.removeButton.enabled=!1,this.nextButton.enabled=!1,this.noteButton.enabled=!1,this.logButton.enabled=!1,this.unMatchButton.enabled=!1,this.confirmButton.enabled=!1,this.reconButton.enabled=!1,this.addButton.enabled=!1),"C"==o.x.call("eval","status")&&(this.prevButton=this.buttonsContainer.addChild(o.cb),this.nextButton=this.buttonsContainer.addChild(o.cb),this.noteButton=this.buttonsContainer.addChild(o.cb),this.logButton=this.buttonsContainer.addChild(o.cb),this.mvmntButton=this.buttonsContainer.addChild(o.cb),this.unMatchButton=this.buttonsContainer.addChild(o.cb),this.suspendButton=this.buttonsContainer.addChild(o.cb),this.reconButton=this.buttonsContainer.addChild(o.cb),this.removeButton=this.buttonsContainer.addChild(o.cb),this.addButton=this.buttonsContainer.addChild(o.cb),this.optionsButton=this.buttonsContainer.addChild(o.cb),this.closeButton=this.buttonsContainer.addChild(o.cb),this.suspendButton.id="suspendButton",this.suspendButton.toolTip=o.Wb.getPredictMessage("tooltip.suspMatch",null),this.suspendButton.label=o.Wb.getPredictMessage("button.suspend",null),this.suspendButton.click=function(e){t.suspendMvmnt()},this.setAllButtonsStatus(),this.prevButton.enabled=!1,this.mvmntButton.enabled=!1,this.removeButton.enabled=!1,this.nextButton.enabled=!1,this.noteButton.enabled=!1,this.logButton.enabled=!1,this.unMatchButton.enabled=!1,this.suspendButton.enabled=!1,this.reconButton.enabled=!1,this.addButton.enabled=!1),this.requestParams=[],this.inputData.url=this.baseURL+this.actionPath+this.actionMethod,this.inputData.send(this.requestParams))):(this.noteButton=this.buttonsContainer.addChild(o.cb),this.logButton=this.buttonsContainer.addChild(o.cb),this.mvmntButton=this.buttonsContainer.addChild(o.cb),this.unMatchButton=this.buttonsContainer.addChild(o.cb),this.suspendButton=this.buttonsContainer.addChild(o.cb),this.confirmButton=this.buttonsContainer.addChild(o.cb),this.reconButton=this.buttonsContainer.addChild(o.cb),this.removeButton=this.buttonsContainer.addChild(o.cb),this.addButton=this.buttonsContainer.addChild(o.cb),this.optionsButton=this.buttonsContainer.addChild(o.cb),this.closeButton=this.buttonsContainer.addChild(o.cb),this.confirmButton.id="confirmButton",this.confirmButton.toolTip=o.Wb.getPredictMessage("tooltip.ConfMatch",null),this.confirmButton.label=o.Wb.getPredictMessage("button.confirm",null),this.confirmButton.click=function(){t.confirmMvmnt()},this.suspendButton.id="suspendButton",this.suspendButton.toolTip=o.Wb.getPredictMessage("tooltip.suspMatch",null),this.suspendButton.label=o.Wb.getPredictMessage("button.suspend",null),this.suspendButton.click=function(e){t.suspendMvmnt()},this.setButtonsProperties(),"mvmntdisplay"==e?(this.actionMethod="method=flexshowMatchDetails",this.actionMethod=this.actionMethod+"&menuAccessId="+o.x.call("eval","menuAccessId")+"&movId="+o.x.call("eval","movId")+"&matchType="+o.x.call("eval","screen")+"&matchList="+o.x.call("eval","matches"),this.actionMethod=this.actionMethod+"&day=&matchCount=&currencyCode=&date=&entityId="+o.x.call("eval","entityId")+"&quality="+o.x.call("eval","quality")+"&archiveId="+o.x.call("eval","archiveId")+"&status="+o.x.call("eval","status"),this.inputData.url=this.baseURL+this.actionPath+this.actionMethod,this.inputData.send(this.requestParams),this.noteButton.enabled=!0,this.logButton.enabled=!0,this.mvmntButton.enabled=!0,this.unMatchButton.enabled=!0,this.suspendButton.enabled=!0,this.confirmButton.enabled=!0,this.reconButton.enabled=!0,this.removeButton.enabled=!0,this.addButton.enabled=!0,this.optionsButton.enabled=!0):"msd"==e?(""!=o.x.call("eval","matchId")?(this.actionMethod="method=flexdisplay",this.actionMethod=this.actionMethod+"&menuAccessId="+o.x.call("eval","menuAccessId")+"&matchIds='"+o.x.call("eval","matchId")+"'&matchType=manual",this.actionMethod=this.actionMethod+"&day=&matchCount=&currencyCode=&status=&date=&entityId=&quality=&menuAccessId=&applyCurrencyThreshold=&noIncludedMovementMatches=&dateTabIndFlag=&dateTabInd="):(this.actionMethod="method=flexofferedMatch",this.actionMethod=this.actionMethod+"&menuAccessId="+o.x.call("eval","menuAccessId")+"&selectedList="+o.x.call("eval","selectedList")+"&selectedTab="+o.x.call("eval","selectedTab")+"&selectedCurrencyCode="+o.x.call("eval","currencyId"),this.actionMethod=this.actionMethod+"&day=&matchCount=&currencyCode=&date=&entityCode="+o.x.call("eval","entityId")),this.inputData.url=this.baseURL+this.actionPath+this.actionMethod,this.inputData.send(this.requestParams),this.noteButton.enabled=!0,this.logButton.enabled=!0,this.mvmntButton.enabled=!0,this.unMatchButton.enabled=!0,this.suspendButton.enabled=!0,this.confirmButton.enabled=!0,this.reconButton.enabled=!0,this.removeButton.enabled=!0,this.addButton.enabled=!0,this.optionsButton.enabled=!0):"generic"==e?(this.actionMethod="method=flexdisplay",this.actionMethod=this.actionMethod+"&menuAccessId="+o.x.call("eval","menuAccessId")+"&matchIds='"+o.x.call("eval","matchIdFromGeneric")+"'&matchType=manual",this.actionMethod=this.actionMethod+"&day=&matchCount=&currencyCode=&status=&date=&quality=&menuAccessId=&applyCurrencyThreshold=&noIncludedMovementMatches=&dateTabIndFlag=&dateTabInd=&archiveId=",this.actionMethod=this.actionMethod+"&entityId="+o.x.call("eval","entityId")+"&hostId="+o.x.call("eval","hostId"),this.actionMethod=this.actionMethod+"&ScreenName=manual",this.inputData.url=this.baseURL+this.actionPath+this.actionMethod,this.inputData.send(this.requestParams),this.noteButton.enabled=!1,this.logButton.enabled=!1,this.mvmntButton.enabled=!1,this.unMatchButton.enabled=!1,this.suspendButton.enabled=!1,this.confirmButton.enabled=!1,this.reconButton.enabled=!1,this.removeButton.enabled=!1,this.addButton.enabled=!1,this.optionsButton.enabled=!1):(this.matchIdText.editable=!0,this.matchIdText.setFocus(),this.loadingImage.setVisible(!1),this.noteButton.enabled=!1,this.logButton.enabled=!1,this.mvmntButton.enabled=!1,this.unMatchButton.enabled=!1,this.suspendButton.enabled=!1,this.confirmButton.enabled=!1,this.reconButton.enabled=!1,this.removeButton.enabled=!1,this.addButton.enabled=!1,this.optionsButton.enabled=!1))},e.prototype.getParamsFromParent=function(){return{sqlParams:this.lastSelectedTooltipParams,facilityId:this.tooltipFacilityId,selectedNodeId:this.selectedNodeId,treeLevelValue:this.treeLevelValue,tooltipMvtId:this.tooltipMvtId,tooltipMatchId:this.tooltipMatchId}},e.prototype.createTooltip=function(t){var e=this;this.customTooltip&&this.customTooltip.close&&this.removeTooltip();try{this.customTooltip=o.Eb.createPopUp(parent,o.u,{}),window.innerHeight<this.positionY+450&&(this.positionY=120),this.customTooltip.setWindowXY(this.positionX+20,this.positionY),this.customTooltip.enableResize=!1,this.customTooltip.width="420",this.customTooltip.height="450",this.customTooltip.enableResize=!1,this.customTooltip.title="Alert Summary Tooltip",this.customTooltip.showControls=!0,this.customTooltip.showHeader=!1,this.customTooltip.parentDocument=this,this.customTooltip.processBox=this,this.customTooltip.display(),setTimeout(function(){e.eventsCreated||e.customTooltip.getChild().DISPLAY_LIST_CLICK.subscribe(function(t){e.lastSelectedTooltipParams=t.noode.data,o.x.call("openAlertInstanceSummary","openAlertInstSummary",e.selectedNodeId,e.treeLevelValue)})},0),setTimeout(function(){e.eventsCreated||e.customTooltip.getChild().LINK_TO_SPECIF_CLICK.subscribe(function(t){e.getScenarioFacility(t.noode.data.scenario_id),e.lastSelectedTooltipParams=t.noode.data,e.hostId=t.hostId})},0),setTimeout(function(){e.eventsCreated||e.customTooltip.getChild().ITEM_CLICK.subscribe(function(t){e.selectedNodeId=t.noode.data.id,e.treeLevelValue=t.noode.data.treeLevelValue,e.customTooltip.getChild().linkToSpecificButton.enabled=!1,1==t.noode.data.count&&0==t.noode.isBranch&&(e.customTooltip.getChild().linkToSpecificButton.enabled=!0)})},0)}catch(n){console.log("SwtCommonGrid -> createTooltip -> error",n)}},e.prototype.getScenarioFacility=function(t){var e=this;this.requestParams=[],this.alertingData.cbStart=this.startOfComms.bind(this),this.alertingData.cbStop=this.endOfComms.bind(this),this.alertingData.cbFault=this.inputDataFault.bind(this),this.alertingData.encodeURL=!1,this.actionPath="scenarioSummary.do?",this.actionMethod="method=getScenarioFacility",this.requestParams.scenarioId=t,this.alertingData.url=this.baseURL+this.actionPath+this.actionMethod,this.alertingData.cbResult=function(t){e.openGoToScreen(t)},this.alertingData.send(this.requestParams)},e.prototype.openGoToScreen=function(t){if(t&&t.ScenarioSummary&&t.ScenarioSummary.scenarioFacility){var e=t.ScenarioSummary.scenarioFacility,n=null!=this.lastSelectedTooltipParams&&null!=this.lastSelectedTooltipParams.entity_id?this.lastSelectedTooltipParams.entity_id:this.entityCombo.selectedLabel,i=null!=this.lastSelectedTooltipParams&&null!=this.lastSelectedTooltipParams.currency_code?this.lastSelectedTooltipParams.currency_code:"All",s=null!=this.lastSelectedTooltipParams&&null!=this.lastSelectedTooltipParams.match_id?this.lastSelectedTooltipParams.match_id:null,a=null!=this.lastSelectedTooltipParams&&null!=this.lastSelectedTooltipParams.movement_id?this.lastSelectedTooltipParams.movement_id:this.tooltipMvtId,l=null!=this.lastSelectedTooltipParams&&null!=this.lastSelectedTooltipParams.sweep_id?this.lastSelectedTooltipParams.sweep_id:null;o.x.call("goTo",e,this.hostId,n,s,i,a,l,"")}},e.prototype.removeTooltip=function(){this.customTooltip.close()},e.prototype.imageClickFunction=function(t){var e=null!=this.jsonReader.getScreenAttributes().scenarioAlerting.toString()?this.jsonReader.getScreenAttributes().scenarioAlerting.toString():"";"Y"!=e&&"C"!=e||(this.tooltipMvtId=null,this.tooltipMatchId=this.matchIdText.text,this.tooltipFacilityId="MATCH_DISPLAY_MOVEMENT_ROW",this.tooltipOtherParams=[],this.createTooltip(t))},e.prototype.itemClickFunction=function(t){var e=this;this.tooltipMatchId=null,null==t.selectedCellTarget||null==t.selectedCellTarget.field||"alerting"!=t.selectedCellTarget.field||"C"!=t.selectedCellTarget.data.alerting&&"Y"!=t.selectedCellTarget.data.alerting?this.removeTooltip():(this.tooltipMvtId=t.selectedCellTarget.data.movement,this.tooltipFacilityId="MATCH_DISPLAY_MOVEMENT_ROW",this.tooltipOtherParams=[],setTimeout(function(){e.createTooltip(null)},100))},e.prototype.logicUpdateResult=function(t){var e=t,n=new o.L;n.setInputJSON(e),n.getRequestReplyStatus()||this.swtAlert.error(""+e.message)},e.prototype.inputDataResult=function(t){var e=this,n=!0;if(this.inputData.isBusy())this.inputData.cbStop();else if(this.lastReceivedJSON=t,this.jsonReader.setInputJSON(this.lastReceivedJSON),this.exportContainer.enabled=!0,this.lostConnectionText.visible=!1,this.jsonReader.getRequestReplyStatus()){if(this.entityCombo.setComboData(this.jsonReader.getSelects()),JSON.stringify(this.lastReceivedJSON)!==JSON.stringify(this.prevRecievedJSON)||0==this.mmsdGrid.dataProvider.length){if(this.selectedEntityId.text=this.jsonReader.getScreenAttributes().entityid,this.selectedEntityName.text=this.jsonReader.getScreenAttributes().entityname,this.currentFontSize=this.jsonReader.getScreenAttributes().currfontsize,this.jsonReader.getScreenAttributes().matchid?this.matchIdText.text=this.jsonReader.getScreenAttributes().matchid:this.matchIdText.text="",this.matchIdText.editable="manual"==this.matchType,this.fullAccess=this.jsonReader.getScreenAttributes().fullAccess,this.matchStatus.text=this.jsonReader.getScreenAttributes().matchstatus,"manual"!=this.matchType&&(this.matchIndices.text=this.jsonReader.getScreenAttributes().matchindex+"/"+this.jsonReader.getScreenAttributes().totalmatches),"CONFIRMED"==this.matchStatus.text?(this.reconButton.visible=!0,this.reconButton.includeInLayout=!0):(this.reconButton.visible=!1,this.reconButton.includeInLayout=!1),this.matchQuality.text=this.jsonReader.getScreenAttributes().matchquality,this.updateDate.text=this.jsonReader.getScreenAttributes().updatedate,this.updateUser.text=this.jsonReader.getScreenAttributes().updateuser,this.currencyCode.text=this.jsonReader.getScreenAttributes().currencycode,this.posIntlvl.text=this.jsonReader.getScreenAttributes().posintlvl,this.posExtlvl.text=this.jsonReader.getScreenAttributes().posextlvl,this.matchHash=this.jsonReader.getScreenAttributes().matchHash,this.ccyTol=this.jsonReader.getScreenAttributes().ccyTolerance?this.jsonReader.getScreenAttributes().ccyTolerance:0,this.suppMatchDiffWarning=this.jsonReader.getScreenAttributes().suppMatchDiffWarning,this.currencyPattern=this.jsonReader.getScreenAttributes().currencyPattern,this.ccyTolValue.text=checkCurrencyPlacesFromNumber(this.ccyTol.toString(),this.currencyPattern,this.currencyCode.text),this.jsonReader.isDataBuilding())this.dataBuildingText.visible=!0;else{this.dataBuildingText.visible=!1;var i={columns:this.jsonReader.getColumnData()};this.mmsdGrid.doubleClickEnabled=!0,this.mmsdGrid.CustomGrid(i);for(var s=0;s<this.mmsdGrid.columnDefinitions.length;s++){var a=this.mmsdGrid.columnDefinitions[s];if("alerting"==a.field){var l="./"+o.x.call("eval","alertOrangeImage"),d="./"+o.x.call("eval","alertRedImage");"Normal"==this.currentFontSize?a.properties={enabled:!1,columnName:"alerting",imageEnabled:l,imageCritEnabled:d,imageDisabled:"",_toolTipFlag:!0,style:" display: block; margin-left: auto; margin-right: auto;"}:a.properties={enabled:!1,columnName:"alerting",imageEnabled:l,imageCritEnabled:d,imageDisabled:"",_toolTipFlag:!0,style:"height:15px; width:15px; display: block; margin-left: auto; margin-right: auto;"},this.mmsdGrid.columnDefinitions[s].editor=null,this.mmsdGrid.columnDefinitions[s].formatter=r.a}}this.mmsdGrid.colWidthURL(this.baseURL+"movementmatchdisplay.do?"),this.mmsdGrid.colOrderURL(this.baseURL+"movementmatchdisplay.do?"),this.mmsdGrid.saveWidths=!0,this.mmsdGrid.saveColumnOrder=!0,this.mmsdGrid.listenHorizontalScrollEvent=!0,this.jsonReader.getGridData()&&this.jsonReader.getGridData().size>0?(this.mmsdGrid.gridData=this.jsonReader.getGridData(),this.mmsdGrid.setRowSize=this.jsonReader.getRowSize(),this.mmsdGrid.allowMultipleSelection=!0):this.mmsdGrid.gridData={row:[],size:0},this.acctAccessFlag="boolean"==typeof this.jsonReader.getScreenAttributes().acctaccessstatus?this.jsonReader.getScreenAttributes().acctaccessstatus:"false"!==String(this.jsonReader.getScreenAttributes().acctaccessstatus).toLowerCase(),"Normal"==this.currentFontSize?(this.mmsdGrid.styleName="dataGridNormal",this.mmsdGrid.rowHeight=18,this.selectedFont=0):"Small"==this.currentFontSize&&(this.mmsdGrid.styleName="dataGridSmall",this.mmsdGrid.rowHeight=15,this.selectedFont=1)}this.mmsdGrid.entityID=this.entityCombo.selectedLabel,this.prevRecievedJSON=this.lastReceivedJSON}}else{if("Match not on file"!=this.jsonReader.getRequestReplyMessage())return"Movements not available for this MatchId"==this.jsonReader.getRequestReplyMessage()?void this.swtAlert.warning(this.jsonReader.getRequestReplyMessage()+"\n"+this.jsonReader.getRequestReplyLocation(),o.Wb.getPredictMessage("screen.warning",null)):void this.swtAlert.error(this.jsonReader.getRequestReplyMessage()+"\n"+this.jsonReader.getRequestReplyLocation(),o.Wb.getPredictMessage("screen.error",null));if("msd"==o.x.call("eval","calledFrom"))return this.swtAlert.warning(this.jsonReader.getRequestReplyMessage()+"\n",o.Wb.getPredictMessage("screen.warning",null),o.c.OK,this,this.alertListenerCloseWindow,null),void(n=!1);this.swtAlert.warning(this.jsonReader.getRequestReplyMessage()+"\n",o.Wb.getPredictMessage("screen.warning",null))}this.previousSelectedIndices=[],this.matchType=o.x.call("eval","match"),"manual"!==this.matchType&&(1==parseInt(this.jsonReader.getScreenAttributes().matchindex,10)&&parseInt(this.jsonReader.getScreenAttributes().totalmatches,10)-1&&(this.prevButton.enabled=!1,this.prevButton.buttonMode=!1,this.nextButton.enabled=!0,this.nextButton.buttonMode=!0),1!=parseInt(this.jsonReader.getScreenAttributes().matchindex,10)&&parseInt(this.jsonReader.getScreenAttributes().totalmatches,10)-1&&(this.prevButton.enabled=!0,this.prevButton.buttonMode=!0,this.nextButton.enabled=!0,this.nextButton.buttonMode=!0),parseInt(this.jsonReader.getScreenAttributes().matchindex,10)==parseInt(this.jsonReader.getScreenAttributes().totalmatches,10)&&(this.nextButton.enabled=!1,this.nextButton.buttonMode=!1),1==parseInt(this.jsonReader.getScreenAttributes().totalmatches,10)&&(this.prevButton.enabled=!1,this.prevButton.buttonMode=!1,this.nextButton.enabled=!1,this.nextButton.buttonMode=!1)),setTimeout(function(){e.predictStatusAmtTotalWarning()},100),setTimeout(function(){e.CalculateAmtDiff()},100),"manual"===this.matchType?("OFFERED"==this.jsonReader.getScreenAttributes().matchstatus&&(this.buttonsContainer.contains(this.confirmButton)||this.addButtonsToButtonContainer("confirmButton",this.confirmbuttonIndex),this.buttonsContainer.contains(this.suspendButton)||this.addButtonsToButtonContainer("suspendButton",this.suspendbuttonIndex),this.buttonsContainer.contains(this.reconButton)||this.addButtonsToButtonContainer("reconButton",this.reconcilebuttonIndex),this.noteButton.enabled=!0,this.logButton.enabled=!0,this.mvmntButton.enabled=!1,this.unMatchButton.enabled=!0,this.suspendButton.enabled=!0,this.confirmButton.enabled=!0,this.reconButton.enabled=!0,this.removeButton.enabled=!1,this.addButton.enabled=!0),"SUSPENDED"==this.jsonReader.getScreenAttributes().matchstatus&&(this.buttonsContainer.contains(this.reconButton)||this.addButtonsToButtonContainer("reconButton",this.reconcilebuttonIndex),this.buttonsContainer.contains(this.confirmButton)||this.addButtonsToButtonContainer("confirmButton",this.confirmbuttonIndex),this.noteButton.enabled=!0,this.logButton.enabled=!0,this.mvmntButton.enabled=!1,this.unMatchButton.enabled=!0,this.confirmButton.enabled=!0,this.reconButton.enabled=!0,this.removeButton.enabled=!1,this.addButton.enabled=!0,this.buttonsContainer.contains(this.suspendButton)&&(this.suspendbuttonIndex=this.buttonsContainer.getChildIndex(this.suspendButton),this.buttonsContainer.removeChild(this.suspendButton))),"CONFIRMED"==this.jsonReader.getScreenAttributes().matchstatus&&(this.buttonsContainer.contains(this.reconButton)||this.addButtonsToButtonContainer("reconButton",this.reconcilebuttonIndex),this.buttonsContainer.contains(this.suspendButton)||this.addButtonsToButtonContainer("suspendButton",this.suspendbuttonIndex),this.noteButton.enabled=!0,this.logButton.enabled=!0,this.mvmntButton.enabled=!1,this.unMatchButton.enabled=!0,this.suspendButton.enabled=!0,this.reconButton.enabled=!0,this.removeButton.enabled=!1,this.addButton.enabled=!0,this.buttonsContainer.contains(this.confirmButton)&&(this.confirmbuttonIndex=this.buttonsContainer.getChildIndex(this.confirmButton),this.buttonsContainer.removeChild(this.confirmButton))),"RECONCILED"==this.jsonReader.getScreenAttributes().matchstatus&&(this.buttonsContainer.contains(this.confirmButton)||this.addButtonsToButtonContainer("confirmButton",this.confirmbuttonIndex),this.buttonsContainer.contains(this.suspendButton)||this.addButtonsToButtonContainer("suspendButton",this.suspendbuttonIndex),this.noteButton.enabled=!0,this.logButton.enabled=!0,this.mvmntButton.enabled=!1,this.unMatchButton.enabled=!0,this.suspendButton.enabled=!0,this.confirmButton.enabled=!0,this.removeButton.enabled=!1,this.addButton.enabled=!0,this.buttonsContainer.contains(this.reconButton)&&(this.reconcilebuttonIndex=this.buttonsContainer.getChildIndex(this.reconButton),this.buttonsContainer.removeChild(this.reconButton))),""==this.jsonReader.getScreenAttributes().matchstatus&&(this.noteButton.enabled=!1,this.logButton.enabled=!1,this.mvmntButton.enabled=!1,this.unMatchButton.enabled=!1,this.suspendButton.enabled=!1,this.buttonsContainer.contains(this.suspendButton)&&(this.suspendButton.enabled=!1),this.buttonsContainer.contains(this.confirmButton)&&(this.confirmButton.enabled=!1),this.removeButton.enabled=!1,this.addButton.enabled=!1),null!=this.lastReceivedJSON.movementmatchsummarydisplay.grid&&0!=this.lastReceivedJSON.movementmatchsummarydisplay.grid.rows.size||(this.matchIndices.text="",this.matchStatus.text="",this.matchQuality.text="",this.updateDate.text="",this.updateUser.text="",this.currencyCode.text="",this.posIntlvl.text="",this.posExtlvl.text="",this.selectedEntityId.text="",0!=this.mmsdGrid.gridData.length&&(this.mmsdGrid.gridData={row:[],size:0}),this.selectedEntityName.text="",this.noteButton.enabled=!1,this.logButton.enabled=!1,this.mvmntButton.enabled=!1,this.unMatchButton.enabled=!1,this.buttonsContainer.contains(this.suspendButton)&&(this.suspendButton.enabled=!1),this.buttonsContainer.contains(this.confirmButton)&&(this.confirmButton.enabled=!1),this.buttonsContainer.contains(this.reconButton)&&(this.reconButton.enabled=!1),this.removeButton.enabled=!1,this.addButton.enabled=!1,this.exportContainer.enabled=!1)):("M"==o.x.call("eval","status")&&(this.noteButton.enabled=!0,this.logButton.enabled=!0,this.mvmntButton.enabled=!1,this.unMatchButton.enabled=!0,this.suspendButton.enabled=!0,this.confirmButton.enabled=!0,this.reconButton.enabled=!0,this.removeButton.enabled=!1,this.addButton.enabled=!0),"S"==o.x.call("eval","status")&&(this.noteButton.enabled=!0,this.logButton.enabled=!0,this.mvmntButton.enabled=!1,this.unMatchButton.enabled=!0,this.confirmButton.enabled=!0,this.reconButton.enabled=!0,this.removeButton.enabled=!1,this.addButton.enabled=!0),"C"==o.x.call("eval","status")&&("RECONCILED"==this.jsonReader.getScreenAttributes().matchstatus?(this.noteButton.enabled=!0,this.logButton.enabled=!0,this.mvmntButton.enabled=!1,this.unMatchButton.enabled=!0,this.suspendButton.enabled=!0,this.removeButton.enabled=!1,this.addButton.enabled=!0,this.buttonsContainer.contains(this.reconButton)&&(this.reconcilebuttonIndex=this.buttonsContainer.getChildIndex(this.reconButton),this.buttonsContainer.removeChild(this.reconButton)),this.buttonsContainer.contains(this.confirmButton)||this.addButtonsToButtonContainer("confirmButton",this.confirmbuttonIndex),this.confirmButton.enabled=!0):(this.noteButton.enabled=!0,this.logButton.enabled=!0,this.mvmntButton.enabled=!1,this.unMatchButton.enabled=!0,this.suspendButton.enabled=!0,this.reconButton.enabled=!0,this.removeButton.enabled=!1,this.addButton.enabled=!0))),"Y"==this.jsonReader.getScreenAttributes().usernotes?(this.noteImage.source=this.baseURL+o.x.call("eval","notesImage"),this.noteImage.toolTip="Notes Available"):(this.noteImage.source=this.baseURL+o.x.call("eval","blankImage"),this.noteImage.toolTip="");var h=null!=this.jsonReader.getScreenAttributes().scenarioAlerting.toString()?this.jsonReader.getScreenAttributes().scenarioAlerting.toString():"";"Y"==h?(this.alertImage.source=this.baseURL+o.x.call("eval","alertOrangeImage"),this.alertImage.toolTip="alerts Available"):"C"==h?(this.alertImage.source=this.baseURL+o.x.call("eval","alertRedImage"),this.alertImage.toolTip="alerts Available"):(this.alertImage.source=this.baseURL+o.x.call("eval","blankImage"),this.alertImage.toolTip=""),"manual"!=this.matchType&&this.lastReceivedJSON.movementmatchsummarydisplay.grid&&0==this.lastReceivedJSON.movementmatchsummarydisplay.grid.rows.size&&o.x.call("parentFormRefresh1"),"manual"==this.matchType&&"movementDisplay"==o.x.call("eval","inputMethod")&&0==this.lastReceivedJSON.movementmatchsummarydisplay.grid.rows.size&&o.x.call("parentFormMovDispRefresh"),n&&"msd"==o.x.call("eval","calledFrom")&&this.lastReceivedJSON.movementmatchsummarydisplay.grid&&0==this.lastReceivedJSON.movementmatchsummarydisplay.grid.rows.size&&o.x.call("close"),null!=this.lastReceivedJSON.movementmatchsummarydisplay.grid&&0!=this.lastReceivedJSON.movementmatchsummarydisplay.grid.rows.size&&(this.matchIds=this.matchIdText.text),this.acctAccessFlag||(this.removeButton.enabled=!1,this.removeButton.buttonMode=!1,this.unMatchButton.enabled=!1,this.reconButton.enabled=!1,this.reconButton.buttonMode=!1,this.unMatchButton.buttonMode=!1,this.suspendButton.enabled=!1,this.suspendButton.buttonMode=!1,this.buttonsContainer.contains(this.confirmButton)&&(this.confirmButton.enabled=!1,this.confirmButton.buttonMode=!1),this.addButton.enabled=!1,this.addButton.buttonMode=!1),""!=o.x.call("eval","archiveId")&&(this.removeButton.enabled=!1,this.removeButton.buttonMode=!1,this.mvmntButton.enabled=!1,this.mvmntButton.buttonMode=!1,this.logButton.enabled=!1,this.logButton.buttonMode=!1,this.unMatchButton.enabled=!1,this.unMatchButton.buttonMode=!1,this.suspendButton.enabled=!1,this.suspendButton.buttonMode=!1,this.buttonsContainer.contains(this.confirmButton)&&(this.confirmButton.enabled=!1,this.confirmButton.buttonMode=!1),this.addButton.enabled=!1,this.addButton.buttonMode=!1),this.refreshFlag&&o.x.call("CallBackApp"),this.mmsdDisableButton(),c.a.parseBooleanValue(this.fullAccess)||(this.unMatchButton.enabled=!1,this.suspendButton.enabled=!1,this.buttonsContainer.contains(this.confirmButton)&&(this.confirmButton.enabled=!1),this.addButton.enabled=!1)},e.prototype.addButtonsToButtonContainer=function(t,e){var n=this;"confirmButton"==t&&(this.confirmButton=this.buttonsContainer.addChildAt(o.cb,e),this.confirmButton.id="confirmButton",this.confirmButton.toolTip=o.Wb.getPredictMessage("tooltip.ConfMatch",null),this.confirmButton.label=o.Wb.getPredictMessage("button.confirm",null),this.confirmButton.click=function(){n.confirmMvmnt()}),"suspendButton"==t&&(this.suspendButton=this.buttonsContainer.addChildAt(o.cb,e),this.suspendButton.id="suspendButton",this.suspendButton.toolTip=o.Wb.getPredictMessage("tooltip.suspMatch",null),this.suspendButton.label=o.Wb.getPredictMessage("button.suspend",null),this.suspendButton.click=function(t){n.suspendMvmnt()}),"reconButton"==t&&(this.reconButton=this.buttonsContainer.addChildAt(o.cb,e),this.reconButton.id="reconButton",this.reconButton.toolTip=o.Wb.getPredictMessage("tooltip.reconMatch",null),this.reconButton.label=o.Wb.getPredictMessage("button.reconcile",null),this.reconButton.click=function(){n.reconcileMvmnt()})},e.prototype.inputDataFault=function(t){this.invalidComms=t.fault.faultstring+"\n"+t.fault.faultCode+"\n"+t.fault.faultDetail},e.prototype.predictStatusAmtTotalWarning=function(){var t=[],e="";if(this.lastReceivedJSON.movementmatchsummarydisplay.grid&&this.lastReceivedJSON.movementmatchsummarydisplay.grid.rows.size>0){for(var n=0;n<this.mmsdGrid.dataProvider.length;n++)t[n]=this.mmsdGrid.dataProvider[n].slickgrid_rowcontent.pos.positionlevel;this.PosLvlCorrAmts(t)&&this.swtAlert.warning(o.Wb.getPredictMessage("label.matchDifferentAmountTotalsAcrossPositionLevels",null)),this.PosLvlCorrPreductStatus(t)&&this.swtAlert.warning(o.Wb.getPredictMessage("label.includedItemsAtMultiplePositionLevels",null)),e=o.x.call("checkUnmatchFlag",this.matchIdText.text,"","ExtBal",this.selectedEntityId.text),c.a.parseBooleanValue(e)&&this.swtAlert.warning(o.Wb.getPredictMessage("label.includedItemsForExternalBalance",null))}},e.prototype.helpHandler=function(){try{o.x.call("help")}catch(t){o.Wb.logError(t,this.moduleId,"MovementMatchSummaryDisplay","doHelp",this.errorLocation)}},e.prototype.mmsdDisableButton=function(){var t=o.x.call("eval","calledFrom"),e=o.x.call("eval","parentScreen");if("mvmntdisplay"==t&&"movementSummaryDisplay"!=e&&""!=this.matchIdText.text){var n=o.x.call("checkLockForMatch",this.matchIdText.text);"false"!=n&&(this.swtAlert.warning(o.Wb.getPredictMessage("label.mvmtAreBusy",null)+" "+n),this.mvmntButton.enabled=!1,this.mvmntButton.buttonMode=!1,this.unMatchButton.enabled=!1,this.unMatchButton.buttonMode=!1,this.suspendButton.enabled=!1,this.suspendButton.buttonMode=!1,this.buttonsContainer.contains(this.confirmButton)&&(this.confirmButton.enabled=!1,this.confirmButton.buttonMode=!1),this.reconButton.enabled=!1,this.reconButton.buttonMode=!1,this.removeButton.enabled=!1,this.removeButton.buttonMode=!1,this.addButton.enabled=!1,this.addButton.buttonMode=!1,this.mmsdGrid.selectable=!1,this.mmsdGrid.removeEventListener("onRowClick",this.cellLogic,!1),this.mmsdGrid.removeEventListener("onFilterChanged",this.cellLogic,!1))}},e.prototype.alertListenerCloseWindow=function(t){t.detail==o.c.OK&&o.x.call("close")},e.prototype.openNotes=function(){this.subScreenName="Notes",this.validateMatchId()},e.prototype.openSuspendMvmnt=function(){var t=this;this.confirmFlagOk=!1,this.reconcileFlag=!1,this.removeFlagUnmatch=!1,this.confirmFlagListener=!1,this.reconcileFlagLisner=!1;for(var e,n=this.mmsdGrid.dataProvider.length,i="",s=!1,a=o.x.call("eval","calledFrom"),l=o.x.call("eval","parentScreen"),d=o.x.call("eval","loggedInUser"),h=0;h<n;h++)i+=this.mmsdGrid.dataProvider[h].movement+",";if(d==(e=o.x.call("checkLocksOnServer",i))&&"mvmntdisplay"==a&&"movementSummaryDisplay"==l&&(s=!0),c.a.parseBooleanValue(e)||s){for(var r=[],u=!1,m=0;m<this.mmsdGrid.dataProvider.length;m++)r[m]=this.mmsdGrid.dataProvider[m].slickgrid_rowcontent.pos.positionlevel;for(var b=1;b<r.length;b++)r[b-1]!=r[b]&&(u=!0);if(u)if(this.PosLvlCorrAmts(r)){this.swtAlert.question("This match has different amount totals across position levels.Do you wish to continue?","Microsoft Internet Explorer",o.c.OK|o.c.CANCEL,this,this.alertListener1.bind(this),null),this.suspendFlagListener=!0}else{var v=this.selectedEntityId.text,p=o.x.call("lockMatchOnServer","suspend",this.matchIds,v);if(c.a.parseBooleanValue(p))if("manual"==o.x.call("eval","match")&&"msd"!=o.x.call("eval","calledFrom")){this.refreshFlag=!0,this.actionMethod="method=suspend",this.actionMethod=this.actionMethod+"&nextRecord=next",this.actionMethod=this.actionMethod+"&day=&matchCount=&menuAccessId=&manualMatchScreen=frommanualMatchScreen&matchId="+this.jsonReader.getScreenAttributes().matchid+"&applyCurrencyThreshold="+o.x.call("eval","applyCurrencyThreshold")+"&noIncludedMovementMatches="+o.x.call("eval","noIncludedMovementMatches")+"&dateTabIndFlag=&dateTabInd="+o.x.call("eval","dateTabInd")+"&matchType="+o.x.call("eval","screen")+"&inputMethod="+o.x.call("eval","inputMethod")+"&currentIndex="+(parseInt(this.jsonReader.getScreenAttributes().matchindex,10)-1)+"&accountAccessStatus="+this.acctAccessFlag;this.jsonReader.getScreenAttributes().matchid;this.changeMatchService.url=this.baseURL+this.actionPath+this.actionMethod,this.requestParams["movement.id.entityId"]=this.jsonReader.getScreenAttributes().entityid,this.requestParams["movement.matchStatus"]=o.x.call("eval","status"),this.requestParams["movement.currencyCode"]=this.jsonReader.getScreenAttributes().currencycode,this.requestParams["movementDetail.matchQuality"]=this.jsonReader.getScreenAttributes().matchquality,this.changeMatchService.cbResult=function(e){t.callFlexDisplay()},this.changeMatchService.send(this.requestParams)}else this.actionMethod="method=suspend",this.actionMethod=this.actionMethod+"&nextRecord=next",this.actionMethod=this.actionMethod+"&day=&matchCount=&menuAccessId=&matchId="+this.jsonReader.getScreenAttributes().matchid+"&applyCurrencyThreshold="+o.x.call("eval","applyCurrencyThreshold")+"&noIncludedMovementMatches="+o.x.call("eval","noIncludedMovementMatches")+"&dateTabIndFlag=&dateTabInd="+o.x.call("eval","dateTabInd")+"&matchType="+o.x.call("eval","screen")+"&inputMethod="+o.x.call("eval","inputMethod")+"&currentIndex="+(parseInt(this.jsonReader.getScreenAttributes().matchindex,10)-1)+"&valueDate="+o.x.call("eval","valueDate")+"&accountAccessStatus="+this.acctAccessFlag,this.inputData.url=this.baseURL+this.actionPath+this.actionMethod,this.requestParams["movement.id.entityId"]=this.jsonReader.getScreenAttributes().entityid,this.requestParams["movement.matchStatus"]=o.x.call("eval","status"),this.requestParams["movement.currencyCode"]=this.jsonReader.getScreenAttributes().currencycode,this.requestParams["movementDetail.matchQuality"]=this.jsonReader.getScreenAttributes().matchquality,this.inputData.send(this.requestParams);else this.swtAlert.warning(o.Wb.getPredictMessage("label.matchIsInUseByanotherProcess",null))}else{this.swtAlert.question("Only 1 Position level is available. Do you want to Continue ?","Microsoft Internet Explorer",o.c.OK|o.c.CANCEL,this,this.alertListener.bind(this),null),this.suspendFlag=!0}}else this.swtAlert.warning(o.Wb.getPredictMessage("label.mvmtAreBusy",null)+" "+e)},e.prototype.openRemoveMvmnt=function(){this.suspendFlag=!1,this.confirmFlagOk=!1,this.reconcileFlag=!1,this.removeFlagUnmatch=!0;var t="";if(this.mmsdGrid.selectedIndices.length>0)for(var e=0;e<this.mmsdGrid.selectedIndices.length;e++)t+="'"+this.mmsdGrid.dataProvider[this.mmsdGrid.selectedIndices[e]].movement+"',";var n=o.x.call("checkUnmatchFlag",this.matchIdText.text,t,"unmatch",this.selectedEntityId.text);if(c.a.parseBooleanValue(n)){this.swtAlert.question("Removing these movements will delete the match. Continue?","Microsoft Internet Explorer",o.c.OK|o.c.CANCEL,this,this.alertListener.bind(this),null)}else if(this.calculateRowsSelected()||this.remainingRows()){this.swtAlert.question("Removing these movements will delete the match. Continue?","Microsoft Internet Explorer",o.c.OK|o.c.CANCEL,this,this.alertListener.bind(this),null)}else{var i="";if(this.mmsdGrid.selectedIndices.length>0)for(var s=0;s<this.mmsdGrid.selectedIndices.length;s++)i+="'"+this.mmsdGrid.dataProvider[this.mmsdGrid.selectedIndices[s]].movement+"',";this.actionMethod="method=remove",this.actionMethod=this.actionMethod+"&day=&matchCount="+o.x.call("eval","matchCount")+"&selectedList="+i+"&matchList="+o.x.call("eval","matches")+"&menuAccessId=&applyCurrencyThreshold="+o.x.call("eval","applyCurrencyThreshold")+"&noIncludedMovementMatches="+o.x.call("eval","noIncludedMovementMatches")+"&dateTabIndFlag=&dateTabInd="+o.x.call("eval","dateTabInd")+"&matchType="+o.x.call("eval","screen")+"&inputMethod="+o.x.call("eval","inputMethod")+"&currentIndex="+(parseInt(this.jsonReader.getScreenAttributes().matchindex,10)-1)+"&valueDate="+o.x.call("eval","valueDate")+"&accountAccessStatus="+this.acctAccessFlag,this.inputData.url=this.baseURL+this.actionPath+this.actionMethod,this.requestParams["movement.id.entityId"]=this.jsonReader.getScreenAttributes().entityid,this.requestParams["movement.matchId"]=this.jsonReader.getScreenAttributes().matchid,this.requestParams["movement.matchStatus"]=o.x.call("eval","status"),this.requestParams["movementDetail.matchQuality"]=this.jsonReader.getScreenAttributes().matchquality,this.inputData.send(this.requestParams)}},e.prototype.openAddMovement=function(){var t=o.x.call("eval","matchCount"),e=this.selectedEntityId.text;this.selectedMovements=[];for(var n=this.currencyCode.text,i="N",s=this.mmsdGrid.dataProvider[0].amount,a=this.mmsdGrid.dataProvider[0].sign,l=1;l<this.mmsdGrid.dataProvider.length;l++){var d=this.mmsdGrid.dataProvider[l].amount,h=this.mmsdGrid.dataProvider[l].sign;if(d!=s||a!=h){i="Y";break}}var r=s+a;o.x.call("openAddScreen","addMvmnt",e,n,i,r,t)},e.prototype.remainingRows=function(){var t=this.lastReceivedJSON.movementmatchsummarydisplay.grid.rows.size-this.mmsdGrid.selectedIndices.length,e="";if(t>0){if(1==t)return!0;if(1==this.mmsdGrid.selectedIndices.length){for(var n=0;n<this.lastReceivedJSON.movementmatchsummarydisplay.grid.rows.size;n++)if(this.mmsdGrid.dataProvider[n].movement!=this.mmsdGrid.dataProvider[this.mmsdGrid.selectedIndex].movement)if(""==e)e=this.mmsdGrid.dataProvider[n].slickgrid_rowcontent.pos.positionlevel;else if(this.mmsdGrid.dataProvider[n].slickgrid_rowcontent.pos.positionlevel!==e)return!1;return!0}if(this.mmsdGrid.selectedIndices.length>1){for(var i=[],s=0;s<this.mmsdGrid.dataProvider.length;s++)-1!=this.mmsdGrid.selectedIndices.indexOf(s)&&i.push(this.mmsdGrid.dataProvider[s].movement);for(var a=0;a<this.lastReceivedJSON.movementmatchsummarydisplay.grid.rows.size;a++)if(-1==this.selectedMovements.indexOf(this.mmsdGrid.dataProvider[a].movement))if(""==e)e=this.mmsdGrid.dataProvider[a].slickgrid_rowcontent.pos.positionlevel;else if(this.mmsdGrid.dataProvider[a].slickgrid_rowcontent.pos.positionlevel!==e)return!1;return!0}}return!1},e.prototype.calculateRowsSelected=function(){for(var t=0,e=!1,n=0;n<this.mmsdGrid.selectedIndices.length;n++)t+=1;return t==this.lastReceivedJSON.movementmatchsummarydisplay.grid.rows.size&&(e=!0),e},e.prototype.postValidateMatchIdAction=function(){switch(this.subScreenName){case"Notes":o.x.call("showMatchNotes","showMatchNotes",this.removeLeadingZeros(this.matchIdText.text),this.selectedEntityId.text);break;case"Log":o.x.call("matchLog","matchLog",this.removeLeadingZeros(this.matchIdText.text),this.updateDate.text);break;case"Mvmnt":o.x.call("showMvmnt","showMovementDetails",this.mmsdGrid.dataProvider[this.mmsdGrid.selectedIndex].movement,this.selectedEntityId.text);break;case"UnMatch":this.isActionRun=!0,this.isRefreshRun=!0,this.unMatchMatchId();break;case"Suspend":this.isActionRun=!0,this.isRefreshRun=!0,this.openSuspendMvmnt();break;case"Confirm":this.isActionRun=!0,this.isRefreshRun=!0,this.openConfirmMvmnt();break;case"Recon":this.isActionRun=!0,this.isRefreshRun=!0,this.openReconcileMvmnt();break;case"Remove":this.isActionRun=!0,this.isRefreshRun=!0,this.openRemoveMvmnt();break;case"Add":this.openAddMovement();break;case"Export":this.exportGridData()}},e.prototype.openConfirmMvmnt=function(){var t=this;this.suspendFlag=!1,this.reconcileFlag=!1,this.removeFlagUnmatch=!1,this.suspendFlagListener=!1,this.reconcileFlagLisner=!1;for(var e,n=this.mmsdGrid.dataProvider.length,i="",s=!1,a=o.x.call("eval","calledFrom"),l=o.x.call("eval","parentScreen"),d=o.x.call("eval","loggedInUser"),h=0;h<n;h++)i+=this.mmsdGrid.dataProvider[h].movement+",";if(d==(e=o.x.call("checkLocksOnServer",i))&&"mvmntdisplay"==a&&"movementSummaryDisplay"==l&&(s=!0),c.a.parseBooleanValue(e)||s){for(var r=[],u=!1,m=0;m<this.mmsdGrid.dataProvider.length;m++)r[m]=this.mmsdGrid.dataProvider[m].slickgrid_rowcontent.pos.positionlevel;for(var b=1;b<r.length;b++)r[b-1]!=r[b]&&(u=!0);if(u)if(this.PosLvlCorrAmts(r)){this.swtAlert.question("This match has different amount totals across position levels. Do you wish to continue?","Microsoft Internet Explorer",o.c.OK|o.c.CANCEL,this,this.alertListener1.bind(this),null),this.confirmFlagListener=!0}else{var v=this.selectedEntityId.text,p=o.x.call("lockMatchOnServer","confirm",this.matchIds,v);if(c.a.parseBooleanValue(p))"manual"==o.x.call("eval","match")&&"msd"!=o.x.call("eval","calledFrom")?(this.refreshFlag=!0,this.actionMethod="method=confirm",this.actionMethod=this.actionMethod+"&nextRecord=next",this.actionMethod=this.actionMethod+"&day=&matchCount=&menuAccessId=&manualMatchScreen=frommanualMatchScreen&matchId="+this.jsonReader.getScreenAttributes().matchid+"&applyCurrencyThreshold="+o.x.call("eval","applyCurrencyThreshold")+"&noIncludedMovementMatches="+o.x.call("eval","noIncludedMovementMatches")+"&dateTabIndFlag=&dateTabInd="+o.x.call("eval","dateTabInd")+"&matchType="+o.x.call("eval","screen")+"&inputMethod="+o.x.call("eval","inputMethod")+"&currentIndex="+(parseInt(this.jsonReader.getScreenAttributes().matchindex,10)-1)+"&accountAccessStatus="+this.acctAccessFlag,this.changeMatchService.url=this.baseURL+this.actionPath+this.actionMethod,this.requestParams["movement.id.entityId"]=this.jsonReader.getScreenAttributes().entityid,this.requestParams["movement.matchStatus"]=o.x.call("eval","status"),this.requestParams["movement.currencyCode"]=this.jsonReader.getScreenAttributes().currencycode,this.requestParams["movementDetail.matchQuality"]=this.jsonReader.getScreenAttributes().matchquality,this.changeMatchService.cbResult=function(e){t.callFlexDisplay()},this.changeMatchService.send(this.requestParams)):(this.actionMethod="method=confirm",this.actionMethod=this.actionMethod+"&nextRecord=next",this.actionMethod=this.actionMethod+"&day=&matchCount=&menuAccessId=&matchId="+this.jsonReader.getScreenAttributes().matchid+"&applyCurrencyThreshold="+o.x.call("eval","applyCurrencyThreshold")+"&noIncludedMovementMatches="+o.x.call("eval","noIncludedMovementMatches")+"&dateTabIndFlag=&dateTabInd="+o.x.call("eval","dateTabInd")+"&matchType="+o.x.call("eval","screen")+"&inputMethod="+o.x.call("eval","inputMethod")+"&currentIndex="+(parseInt(this.jsonReader.getScreenAttributes().matchindex,10)-1)+"&valueDate="+o.x.call("eval","valueDate")+"&accountAccessStatus="+this.acctAccessFlag,this.inputData.url=this.baseURL+this.actionPath+this.actionMethod,this.requestParams["movement.id.entityId"]=this.jsonReader.getScreenAttributes().entityid,this.requestParams["movement.matchStatus"]=o.x.call("eval","status"),this.requestParams["movement.currencyCode"]=this.jsonReader.getScreenAttributes().currencycode,this.requestParams["movementDetail.matchQuality"]=this.jsonReader.getScreenAttributes().matchquality,this.inputData.send(this.requestParams));else this.swtAlert.warning(o.Wb.getPredictMessage("label.matchIsInUseByanotherProcess",null))}else{this.swtAlert.question("Only 1 Position level is available. Do you want to Continue ?","Microsoft Internet Explorer",o.c.OK|o.c.CANCEL,this,this.alertListener.bind(this),null),this.confirmFlagOk=!0}}else this.swtAlert.warning(o.Wb.getPredictMessage("label.mvmtAreBusy",null)+" "+e)},e.prototype.openReconcileMvmnt=function(){var t=this;this.suspendFlag=!1,this.confirmFlagOk=!1,this.removeFlagUnmatch=!1,this.suspendFlagListener=!1,this.confirmFlagListener=!1;for(var e,n=this.mmsdGrid.dataProvider.length,i="",s=!1,a=o.x.call("eval","calledFrom"),l=o.x.call("eval","parentScreen"),d=o.x.call("eval","loggedInUser"),h=0;h<n;h++)i+=this.mmsdGrid.dataProvider[h].movement+",";if(d==(e=o.x.call("checkLocksOnServer",i))&&"mvmntdisplay"==a&&"movementSummaryDisplay"==l&&(s=!0),c.a.parseBooleanValue(e)||s){this.reconcileFlag=!0;for(var r=[],u=!1,m=0;m<this.mmsdGrid.dataProvider.length;m++)r[m]=this.mmsdGrid.dataProvider[m].slickgrid_rowcontent.pos.positionlevel;for(var b=1;b<r.length;b++)r[b-1]!=r[b]&&(u=!0);if(u)if(this.PosLvlCorrAmts(r)){this.swtAlert.question("This match has different amount totals across position levels.Do you wish to continue?","Microsoft Internet Explorer",o.c.OK|o.c.CANCEL,this,this.alertListener1.bind(this),null),this.reconcileFlagLisner=!0}else{var v=this.selectedEntityId.text,p=o.x.call("lockMatchOnServer","reconcile",this.matchIds,v);if(c.a.parseBooleanValue(p))if("manual"==o.x.call("eval","match")&&"msd"!=o.x.call("eval","calledFrom")){this.refreshFlag=!0,this.actionMethod="method=reconcile",this.actionMethod=this.actionMethod+"&nextRecord=next",this.actionMethod=this.actionMethod+"&day=&matchCount=&menuAccessId=&matchList="+o.x.call("eval","matches")+"&manualMatchScreen=frommanualMatchScreen&matchId="+this.jsonReader.getScreenAttributes().matchid+"&applyCurrencyThreshold="+o.x.call("eval","applyCurrencyThreshold")+"&noIncludedMovementMatches="+o.x.call("eval","noIncludedMovementMatches")+"&dateTabIndFlag=&dateTabInd="+o.x.call("eval","dateTabInd")+"&matchType="+o.x.call("eval","screen")+"&inputMethod="+o.x.call("eval","inputMethod")+"&currentIndex="+(parseInt(this.jsonReader.getScreenAttributes().matchindex,10)-1)+"&accountAccessStatus="+this.acctAccessFlag;this.jsonReader.getScreenAttributes().matchid;this.changeMatchService.url=this.baseURL+this.actionPath+this.actionMethod,this.requestParams["movement.id.entityId"]=this.jsonReader.getScreenAttributes().entityid,this.requestParams["movement.matchStatus"]=o.x.call("eval","status"),this.requestParams["movement.currencyCode"]=this.jsonReader.getScreenAttributes().currencycode,this.requestParams["movementDetail.matchQuality"]=this.jsonReader.getScreenAttributes().matchquality,this.changeMatchService.cbResult=function(e){t.callFlexDisplay()},this.changeMatchService.send(this.requestParams)}else this.actionMethod="method=reconcile",this.actionMethod=this.actionMethod+"&nextRecord=next",this.actionMethod=this.actionMethod+"&day=&matchCount=&menuAccessId=&matchId="+this.jsonReader.getScreenAttributes().matchid+"&applyCurrencyThreshold="+o.x.call("eval","applyCurrencyThreshold")+"&noIncludedMovementMatches="+o.x.call("eval","noIncludedMovementMatches")+"&dateTabIndFlag=&dateTabInd="+o.x.call("eval","dateTabInd")+"&matchType="+o.x.call("eval","screen")+"&inputMethod="+o.x.call("eval","inputMethod")+"&currentIndex="+(parseInt(this.jsonReader.getScreenAttributes().matchindex,10)-1)+"&valueDate="+o.x.call("eval","valueDate")+"&accountAccessStatus="+this.acctAccessFlag,this.inputData.url=this.baseURL+this.actionPath+this.actionMethod,this.requestParams["movement.id.entityId"]=this.jsonReader.getScreenAttributes().entityid,this.requestParams["movement.matchStatus"]=o.x.call("eval","status"),this.requestParams["movement.currencyCode"]=this.jsonReader.getScreenAttributes().currencycode,this.requestParams["movementDetail.matchQuality"]=this.jsonReader.getScreenAttributes().matchquality,this.inputData.send(this.requestParams);else this.swtAlert.warning(o.Wb.getPredictMessage("label.matchIsInUseByanotherProcess",null))}else{this.swtAlert.question("Only 1 Position level is available. Do you want to Continue ?","Microsoft Internet Explorer",o.c.OK|o.c.CANCEL,this,this.alertListener.bind(this),null),this.reconcileFlag=!0}}else this.swtAlert.warning(o.Wb.getPredictMessage("label.mvmtAreBusy",null)+" "+e)},e.prototype.PosLvlCorrAmts=function(t){for(var e,n="",i="",s="",a=!1,o=0;o<t.length;o++)(i=t[o])!=n&&(s=s+this.getPosLvlTotalAmount(t[o]).toString()+","),n=i;e=(s=s.substring(0,s.length-1)).split(",");for(var l=1;l<e.length;l++)if("N"==this.suppMatchDiffWarning){if(parseFloat(Number(e[l-1]).toFixed(2))!=parseFloat(Number(e[l]).toFixed(2))){a=!0;break}}else if(parseFloat(Math.abs(Number(e[l-1])-Number(e[l])).toFixed(2))>parseFloat(Number(this.ccyTol).toFixed(2))){a=!0;break}return a},e.prototype.getPosLvlTotalAmount=function(t){for(var e=0,n=0;n<this.mmsdGrid.dataProvider.length;n++){if(this.mmsdGrid.dataProvider[n].slickgrid_rowcontent.pos.positionlevel==t){var i=Number(this.getAmtValue(this.mmsdGrid.dataProvider[n].amount)),s=this.mmsdGrid.dataProvider[n].sign;"C"==s?e+=i:"D"==s&&(e-=i)}}return e},e.prototype.getAmtValue=function(t){for(var e="",n="",i=0;i<t.length;i++)n=t.charAt(i),"currencyPat1"==this.currencyPattern?","!=n&&(e+=n):"."!=n&&(e+=n);return e.replace(/,/g,".")},e.prototype.export=function(t){this.subScreenName="Export",this.reportType=t.toString(),this.validateMatchId()},e.prototype.exportGridData=function(){var t=[],e=this.mmsdGrid.selectedIndices;t.push("Entity="+this.selectedEntityId.text),t.push("Match Id="+this.removeLeadingZeros(this.matchIdText.text)),t.push("Currency Code="+this.currencyCode.text),t.push("Match Status="+this.matchStatus.text),t.push("Match Quality="+this.matchQuality.text),t.push("Update date="+this.updateDate.text),t.push("Update user="+this.updateUser.text),t.push("No. of Internal Position level="+this.posIntlvl.text),t.push("No. of External Position level="+this.posExtlvl.text);for(var n="<data>",i="",s=this.lastReceivedJSON.movementmatchsummarydisplay.grid.metadata.columns.column,a=0;a<this.mmsdGrid.dataProvider.length;a++){"<row>";for(var l=0;l<s.length;l++){var d=s[l].dataelement;null==this.mmsdGrid.dataProvider[a][d.toLowerCase()]||""==this.mmsdGrid.dataProvider[a][d.toLowerCase()]?"<"+d.toLowerCase()+"> </"+d.toLowerCase()+">":"<"+d.toLowerCase()+">"+this.mmsdGrid.dataProvider[a][d.toLowerCase()]+"</"+d.toLowerCase()+">"}"</row>"}n+=i="<total>"+i+"</total>";var h="<filters>";if(null!=t)for(var r=0;r<t.length;r++)h+='<filter id="'+t[r].split("=")[0]+'">'+t[r].split("=")[1]+"</filter>";if(this.mmsdGrid.isFiltered)for(var c=0;c<this.mmsdGrid.getFilterColumns().length;c++)for(var u=this.mmsdGrid.getFilterColumns()[c],m=0;m<u.length;m++)h+='<filter id="Column Filter '+m+'">'+u[m]+"</filter>";n+=h+="</filters>";for(var b=(n+="</data>").split("&"),v=0;v<b.length;v++)0==v?n=b[v]:n+="&amp;"+b[v];e.length<=0?this.mmsdGrid.selectedIndices=[]:this.mmsdGrid.selectedIndices=e,this.exportContainer.convertData(this.lastReceivedJSON.movementmatchsummarydisplay.grid.metadata.columns,this.mmsdGrid,null,t,this.reportType,!1),o.x.call("eval","document.getElementById('exportDataForm').action='flexdataexport.do?method="+this.reportType+"';"),o.x.call("eval","document.getElementById('exportData').value='"+n+"';"),o.x.call("eval","document.getElementById('exportDataForm').submit();")},e.prototype.suspendMvmnt=function(){this.suspendButton.enabled=!1,this.subScreenName="Suspend",this.validateMatchId()},e.prototype.checkStatus=function(t){var e=this.selectedEntityId.text,n=0,i=1,s=0,a=this.matchStatus.text,l="",d="";do{n=t-s>100?s+100:t;for(var h=s;h<n&&h<this.mmsdGrid.dataProvider.length;h++)l+=this.mmsdGrid.dataProvider[h].movement+",";d=o.x.call("checkStatus",this.matchIds,e,a,l,t,i,n,this.matchHash),s=n,i++,l=""}while(n<t);return d},e.prototype.validateMatchId=function(){var t;if(this.serverBusyFlag)this.swtAlert.warning(o.Wb.getPredictMessage("label.resourceBusy",null));else{var e=this.mmsdGrid.dataProvider.length;"no_match"!=(t=this.checkStatus(e))?c.a.parseBooleanValue(t)?""==this.inScreenMatchId||this.inScreenMatchId==this.removeLeadingZeros(this.matchIdText.text)?this.postValidateMatchIdAction():this.swtAlert.warning(o.Wb.getPredictMessage("label.matchIDFiledAmended",null),null,0,this,this.alertCloseHandler):this.swtAlert.warning(o.Wb.getPredictMessage("label.matchHasBeenChanged",null)):this.swtAlert.error(o.Wb.getPredictMessage("match.doesnotexist",null))}},e.prototype.alertListener=function(t){this.matchIds=this.removeLeadingZeros(this.matchIdText.text);for(var e=this.selectedEntityId.text,n=0;n<this.mmsdGrid.dataProvider.length;n++)this.mmsdGrid.dataProvider[n].movement+",";var i=!1;if(t.detail==o.c.OK&&(i=!0),i){if(this.removeFlagUnmatch){var s=o.x.call("lockMatchOnServer","remove",this.matchIds,e);if(c.a.parseBooleanValue(s))"manual"==o.x.call("eval","match")?(this.actionMethod="method=unmatch",this.actionMethod=this.actionMethod+"&day=&matchCount=&menuAccessId=&matchId="+this.matchIds+"&manualMatchScreen=frommanualMatchScreen&applyCurrencyThreshold="+o.x.call("eval","applyCurrencyThreshold")+"&noIncludedMovementMatches="+o.x.call("eval","noIncludedMovementMatches")+"&dateTabIndFlag=&dateTabInd="+o.x.call("eval","dateTabInd")+"&matchType="+o.x.call("eval","screen")+"&inputMethod="+o.x.call("eval","inputMethod")+"&currentIndex="+(parseInt(this.jsonReader.getScreenAttributes().matchindex,10)-1)+"&accountAccessStatus="+this.acctAccessFlag,this.inputData.url=this.baseURL+this.actionPath+this.actionMethod,this.requestParams["movement.id.entityId"]=this.jsonReader.getScreenAttributes().entityid,this.requestParams["movement.matchId"]=this.jsonReader.getScreenAttributes().matchid,this.requestParams["movement.matchStatus"]=o.x.call("eval","status"),this.requestParams["movement.currencyCode"]=this.jsonReader.getScreenAttributes().currencycode,this.requestParams["movementDetail.matchQuality"]=this.jsonReader.getScreenAttributes().matchquality,this.inputData.send(this.requestParams)):(this.actionMethod="method=unmatch",this.actionMethod=this.actionMethod+"&day=&matchCount=&menuAccessId=&matchId="+this.matchIds+"&applyCurrencyThreshold="+o.x.call("eval","applyCurrencyThreshold")+"&noIncludedMovementMatches="+o.x.call("eval","noIncludedMovementMatches")+"&dateTabIndFlag=&dateTabInd="+o.x.call("eval","dateTabInd")+"&matchType="+o.x.call("eval","screen")+"&inputMethod="+o.x.call("eval","inputMethod")+"&currentIndex="+(parseInt(this.jsonReader.getScreenAttributes().matchindex,10)-1)+"&valueDate="+o.x.call("eval","valueDate")+"&accountAccessStatus="+this.acctAccessFlag,this.inputData.url=this.baseURL+this.actionPath+this.actionMethod,this.requestParams["movement.id.entityId"]=this.jsonReader.getScreenAttributes().entityid,this.requestParams["movement.matchId"]=this.jsonReader.getScreenAttributes().matchid,this.requestParams["movement.matchStatus"]=o.x.call("eval","status"),this.requestParams["movement.currencyCode"]=this.jsonReader.getScreenAttributes().currencycode,this.requestParams["movementDetail.matchQuality"]=this.jsonReader.getScreenAttributes().matchquality,this.inputData.send(this.requestParams));else this.swtAlert.warning(o.Wb.getPredictMessage("label.matchIsInUseByanotherProcess",null))}if(this.suspendFlag){var a=o.x.call("lockMatchOnServer","suspend",this.matchIds,e);if(c.a.parseBooleanValue(a))"manual"==o.x.call("eval","match")?(this.refreshFlag=!0,this.actionMethod="method=suspend",this.actionMethod=this.actionMethod+"&nextRecord=next",this.actionMethod=this.actionMethod+"&day=&matchCount=&menuAccessId=&manualMatchScreen=frommanualMatchScreen&matchId="+this.jsonReader.getScreenAttributes().matchid+"&applyCurrencyThreshold="+o.x.call("eval","applyCurrencyThreshold")+"&noIncludedMovementMatches="+o.x.call("eval","noIncludedMovementMatches")+"&dateTabIndFlag=&dateTabInd="+o.x.call("eval","dateTabInd")+"&matchType="+o.x.call("eval","screen")+"&inputMethod="+o.x.call("eval","inputMethod")+"&currentIndex="+(parseInt(this.jsonReader.getScreenAttributes().matchindex,10)-1)+"&accountAccessStatus="+this.acctAccessFlag,this.inputData.url=this.baseURL+this.actionPath+this.actionMethod,this.requestParams["movement.id.entityId"]=this.jsonReader.getScreenAttributes().entityid,this.requestParams["movement.matchStatus"]=o.x.call("eval","status"),this.requestParams["movement.currencyCode"]=this.jsonReader.getScreenAttributes().currencycode,this.requestParams["movementDetail.matchQuality"]=this.jsonReader.getScreenAttributes().matchquality,this.inputData.send(this.requestParams)):(this.actionMethod="method=suspend",this.actionMethod=this.actionMethod+"&nextRecord=next",this.actionMethod=this.actionMethod+"&day=&matchCount=&menuAccessId=&matchId="+this.jsonReader.getScreenAttributes().matchid+"&applyCurrencyThreshold="+o.x.call("eval","applyCurrencyThreshold")+"&noIncludedMovementMatches="+o.x.call("eval","noIncludedMovementMatches")+"&dateTabIndFlag=&dateTabInd="+o.x.call("eval","dateTabInd")+"&matchType="+o.x.call("eval","screen")+"&inputMethod="+o.x.call("eval","inputMethod")+"&currentIndex="+(parseInt(this.jsonReader.getScreenAttributes().matchindex,10)-1)+"&valueDate="+o.x.call("eval","valueDate")+"&accountAccessStatus="+this.acctAccessFlag,this.inputData.url=this.baseURL+this.actionPath+this.actionMethod,this.requestParams["movement.id.entityId"]=this.jsonReader.getScreenAttributes().entityid,this.requestParams["movement.matchStatus"]=o.x.call("eval","status"),this.requestParams["movement.currencyCode"]=this.jsonReader.getScreenAttributes().currencycode,this.requestParams["movementDetail.matchQuality"]=this.jsonReader.getScreenAttributes().matchquality,this.inputData.send(this.requestParams));else this.swtAlert.warning(o.Wb.getPredictMessage("label.matchIsInUseByanotherProcess",null))}if(this.confirmFlagOk){var l=o.x.call("lockMatchOnServer","confirm",this.matchIds,e);if(c.a.parseBooleanValue(l))"manual"==o.x.call("eval","match")?(this.refreshFlag=!0,this.actionMethod="method=confirm",this.actionMethod=this.actionMethod+"&nextRecord=next",this.actionMethod=this.actionMethod+"&day=&matchCount=&menuAccessId=&manualMatchScreen=frommanualMatchScreen&matchId="+this.jsonReader.getScreenAttributes().matchid+"&applyCurrencyThreshold="+o.x.call("eval","applyCurrencyThreshold")+"&noIncludedMovementMatches="+o.x.call("eval","noIncludedMovementMatches")+"&dateTabIndFlag=&dateTabInd="+o.x.call("eval","dateTabInd")+"&matchType="+o.x.call("eval","screen")+"&inputMethod="+o.x.call("eval","inputMethod")+"&currentIndex="+(parseInt(this.jsonReader.getScreenAttributes().matchindex,10)-1)+"&accountAccessStatus="+this.acctAccessFlag,this.inputData.url=this.baseURL+this.actionPath+this.actionMethod,this.requestParams["movement.id.entityId"]=this.jsonReader.getScreenAttributes().entityid,this.requestParams["movement.matchStatus"]=o.x.call("eval","status"),this.requestParams["movement.currencyCode"]=this.jsonReader.getScreenAttributes().currencycode,this.requestParams["movementDetail.matchQuality"]=this.jsonReader.getScreenAttributes().matchquality,this.inputData.send(this.requestParams)):(this.actionMethod="method=confirm",this.actionMethod=this.actionMethod+"&nextRecord=next",this.actionMethod=this.actionMethod+"&day=&matchCount=&menuAccessId=&matchId="+this.jsonReader.getScreenAttributes().matchid+"&applyCurrencyThreshold="+o.x.call("eval","applyCurrencyThreshold")+"&noIncludedMovementMatches="+o.x.call("eval","noIncludedMovementMatches")+"&dateTabIndFlag=&dateTabInd="+o.x.call("eval","dateTabInd")+"&matchType="+o.x.call("eval","screen")+"&inputMethod="+o.x.call("eval","inputMethod")+"&currentIndex="+(parseInt(this.jsonReader.getScreenAttributes().matchindex,10)-1)+"&valueDate="+o.x.call("eval","valueDate")+"&accountAccessStatus="+this.acctAccessFlag,this.inputData.url=this.baseURL+this.actionPath+this.actionMethod,this.requestParams["movement.id.entityId"]=this.jsonReader.getScreenAttributes().entityid,this.requestParams["movement.matchStatus"]=o.x.call("eval","status"),this.requestParams["movement.currencyCode"]=this.jsonReader.getScreenAttributes().currencycode,this.requestParams["movementDetail.matchQuality"]=this.jsonReader.getScreenAttributes().matchquality,this.inputData.send(this.requestParams));else this.swtAlert.warning(o.Wb.getPredictMessage("label.matchIsInUseByanotherProcess",null))}if(this.reconcileFlag){var d=o.x.call("lockMatchOnServer","reconcile",this.matchIds,e);if(c.a.parseBooleanValue(d))"manual"==o.x.call("eval","match")?(this.refreshFlag=!0,this.actionMethod="method=reconcile",this.actionMethod=this.actionMethod+"&nextRecord=next",this.actionMethod=this.actionMethod+"&day=&matchCount=&menuAccessId=&matchList="+o.x.call("eval","matches")+"&manualMatchScreen=frommanualMatchScreen&matchId="+this.jsonReader.getScreenAttributes().matchid+"&applyCurrencyThreshold="+o.x.call("eval","applyCurrencyThreshold")+"&noIncludedMovementMatches="+o.x.call("eval","noIncludedMovementMatches")+"&dateTabIndFlag=&dateTabInd="+o.x.call("eval","dateTabInd")+"&matchType="+o.x.call("eval","screen")+"&inputMethod="+o.x.call("eval","inputMethod")+"&currentIndex="+(parseInt(this.jsonReader.getScreenAttributes().matchindex,10)-1)+"&accountAccessStatus="+this.acctAccessFlag,this.inputData.url=this.baseURL+this.actionPath+this.actionMethod,this.requestParams["movement.id.entityId"]=this.jsonReader.getScreenAttributes().entityid,this.requestParams["movement.matchStatus"]=o.x.call("eval","status"),this.requestParams["movement.currencyCode"]=this.jsonReader.getScreenAttributes().currencycode,this.requestParams["movementDetail.matchQuality"]=this.jsonReader.getScreenAttributes().matchquality,this.inputData.send(this.requestParams)):(this.actionMethod="method=reconcile",this.actionMethod=this.actionMethod+"&nextRecord=next",this.actionMethod=this.actionMethod+"&day=&matchCount=&menuAccessId=&matchId="+this.jsonReader.getScreenAttributes().matchid+"&applyCurrencyThreshold="+o.x.call("eval","applyCurrencyThreshold")+"&noIncludedMovementMatches="+o.x.call("eval","noIncludedMovementMatches")+"&dateTabIndFlag=&dateTabInd="+o.x.call("eval","dateTabInd")+"&matchType="+o.x.call("eval","screen")+"&inputMethod="+o.x.call("eval","inputMethod")+"&currentIndex="+(parseInt(this.jsonReader.getScreenAttributes().matchindex,10)-1)+"&valueDate="+o.x.call("eval","valueDate")+"&accountAccessStatus="+this.acctAccessFlag,this.inputData.url=this.baseURL+this.actionPath+this.actionMethod,this.requestParams["movement.id.entityId"]=this.jsonReader.getScreenAttributes().entityid,this.requestParams["movement.matchStatus"]=o.x.call("eval","status"),this.requestParams["movement.currencyCode"]=this.jsonReader.getScreenAttributes().currencycode,this.requestParams["movementDetail.matchQuality"]=this.jsonReader.getScreenAttributes().matchquality,this.inputData.send(this.requestParams));else this.swtAlert.warning(o.Wb.getPredictMessage("label.matchIsInUseByanotherProcess",null))}}else this.alertCancelButtonHandler(this.suspendFlag,this.confirmFlagOk)},e.prototype.alertCancelButtonHandler=function(t,e){e&&this.buttonsContainer.contains(this.confirmButton)&&(this.confirmButton.enabled=!0),t&&this.buttonsContainer.contains(this.suspendButton)&&(this.suspendButton.enabled=!0)},e.prototype.alertListener1=function(t){var e=!1;if(t.detail==o.c.OK&&(e=!0),e){var n=[],i=this.selectedEntityId.text;this.matchIds=this.removeLeadingZeros(this.matchIdText.text);for(var s=o.x.call("eval","match"),a=0;a<this.mmsdGrid.dataProvider.length;a++)n[a]=this.mmsdGrid.dataProvider[a].slickgrid_rowcontent.pos.positionlevel;if(this.suspendFlagListener)if(this.PosLvlCorrPreductStatus(n)){this.swtAlert.question("There are included items at multiple position levels. Do you wish to continue?","Microsoft Internet Explorer",o.c.OK|o.c.CANCEL,this,this.alertListener.bind(this),null),this.suspendFlag=!0}else{var l=o.x.call("lockMatchOnServer","suspend",this.matchIds,i);c.a.parseBooleanValue(l)?"manual"==s?(this.refreshFlag=!0,this.actionMethod="method=suspend",this.actionMethod=this.actionMethod+"&nextRecord=next",this.actionMethod=this.actionMethod+"&day=&matchCount=&menuAccessId=&manualMatchScreen=frommanualMatchScreen&matchId="+this.jsonReader.getScreenAttributes().matchid+"&applyCurrencyThreshold="+o.x.call("eval","applyCurrencyThreshold")+"&noIncludedMovementMatches="+o.x.call("eval","noIncludedMovementMatches")+"&dateTabIndFlag=&dateTabInd="+o.x.call("eval","dateTabInd")+"&matchType="+o.x.call("eval","screen")+"&inputMethod="+o.x.call("eval","inputMethod")+"&currentIndex="+(parseInt(this.jsonReader.getScreenAttributes().matchindex,10)-1)+"&accountAccessStatus="+this.acctAccessFlag,this.inputData.url=this.baseURL+this.actionPath+this.actionMethod,this.requestParams["movement.id.entityId"]=this.jsonReader.getScreenAttributes().entityid,this.requestParams["movement.matchStatus"]=o.x.call("eval","status"),this.requestParams["movement.currencyCode"]=this.jsonReader.getScreenAttributes().currencycode,this.requestParams["movementDetail.matchQuality"]=this.jsonReader.getScreenAttributes().matchquality,this.inputData.send(this.requestParams)):(this.actionMethod="method=suspend",this.actionMethod=this.actionMethod+"&nextRecord=next",this.actionMethod=this.actionMethod+"&day=&matchCount=&menuAccessId=&matchId="+this.jsonReader.getScreenAttributes().matchid+"&applyCurrencyThreshold="+o.x.call("eval","applyCurrencyThreshold")+"&noIncludedMovementMatches="+o.x.call("eval","noIncludedMovementMatches")+"&dateTabIndFlag=&dateTabInd="+o.x.call("eval","dateTabInd")+"&matchType="+o.x.call("eval","screen")+"&inputMethod="+o.x.call("eval","inputMethod")+"&currentIndex="+(parseInt(this.jsonReader.getScreenAttributes().matchindex,10)-1)+"&valueDate="+o.x.call("eval","valueDate")+"&accountAccessStatus="+this.acctAccessFlag,this.inputData.url=this.baseURL+this.actionPath+this.actionMethod,this.requestParams["movement.id.entityId"]=this.jsonReader.getScreenAttributes().entityid,this.requestParams["movement.matchStatus"]=o.x.call("eval","status"),this.requestParams["movement.currencyCode"]=this.jsonReader.getScreenAttributes().currencycode,this.requestParams["movementDetail.matchQuality"]=this.jsonReader.getScreenAttributes().matchquality,this.inputData.send(this.requestParams)):this.swtAlert.warning(o.Wb.getPredictMessage("label.matchIsInUseByanotherProcess",null))}if(this.confirmFlagListener)if(this.PosLvlCorrPreductStatus(n)){this.swtAlert.question("There are included items at multiple position levels. Do you wish to continue?","Microsoft Internet Explorer",o.c.OK|o.c.CANCEL,this,this.alertListener.bind(this),null),this.confirmFlagOk=!0}else{var d=o.x.call("lockMatchOnServer","confirm",this.matchIds,i);c.a.parseBooleanValue(d)?"manual"==s?(this.refreshFlag=!0,this.actionMethod="method=confirm",this.actionMethod=this.actionMethod+"&nextRecord=next",this.actionMethod=this.actionMethod+"&day=&matchCount=&menuAccessId=&manualMatchScreen=frommanualMatchScreen&matchId="+this.jsonReader.getScreenAttributes().matchid+"&applyCurrencyThreshold="+o.x.call("eval","applyCurrencyThreshold")+"&noIncludedMovementMatches="+o.x.call("eval","noIncludedMovementMatches")+"&dateTabIndFlag=&dateTabInd="+o.x.call("eval","dateTabInd")+"&matchType="+o.x.call("eval","screen")+"&inputMethod="+o.x.call("eval","inputMethod")+"&currentIndex="+(parseInt(this.jsonReader.getScreenAttributes().matchindex,10)-1)+"&accountAccessStatus="+this.acctAccessFlag,this.inputData.url=this.baseURL+this.actionPath+this.actionMethod,this.requestParams["movement.id.entityId"]=this.jsonReader.getScreenAttributes().entityid,this.requestParams["movement.matchStatus"]=o.x.call("eval","status"),this.requestParams["movement.currencyCode"]=this.jsonReader.getScreenAttributes().currencycode,this.requestParams["movementDetail.matchQuality"]=this.jsonReader.getScreenAttributes().matchquality,this.inputData.send(this.requestParams)):(this.actionMethod="method=confirm",this.actionMethod=this.actionMethod+"&nextRecord=next",this.actionMethod=this.actionMethod+"&day=&matchCount=&menuAccessId=&matchId="+this.jsonReader.getScreenAttributes().matchid+"&applyCurrencyThreshold="+o.x.call("eval","applyCurrencyThreshold")+"&noIncludedMovementMatches="+o.x.call("eval","noIncludedMovementMatches")+"&dateTabIndFlag=&dateTabInd="+o.x.call("eval","dateTabInd")+"&matchType="+o.x.call("eval","screen")+"&inputMethod="+o.x.call("eval","inputMethod")+"&currentIndex="+(parseInt(this.jsonReader.getScreenAttributes().matchindex,10)-1)+"&valueDate="+o.x.call("eval","valueDate")+"&accountAccessStatus="+this.acctAccessFlag,this.inputData.url=this.baseURL+this.actionPath+this.actionMethod,this.requestParams["movement.id.entityId"]=this.jsonReader.getScreenAttributes().entityid,this.requestParams["movement.matchStatus"]=o.x.call("eval","status"),this.requestParams["movement.currencyCode"]=this.jsonReader.getScreenAttributes().currencycode,this.requestParams["movementDetail.matchQuality"]=this.jsonReader.getScreenAttributes().matchquality,this.inputData.send(this.requestParams)):this.swtAlert.warning(o.Wb.getPredictMessage("label.matchIsInUseByanotherProcess",null))}if(this.reconcileFlagLisner)if(this.PosLvlCorrPreductStatus(n)){this.swtAlert.question("There are included items at multiple position levels. Do you wish to continue?","Microsoft Internet Explorer",o.c.OK|o.c.CANCEL,this,this.alertListener.bind(this),null),this.reconcileFlag=!0}else{var h=o.x.call("lockMatchOnServer","reconcile",this.matchIds,i);c.a.parseBooleanValue(h)?"manual"==s?(this.refreshFlag=!0,this.actionMethod="method=reconcile",this.actionMethod=this.actionMethod+"&nextRecord=next",this.actionMethod=this.actionMethod+"&day=&matchCount=&menuAccessId=&matchList="+o.x.call("eval","matches")+"&manualMatchScreen=frommanualMatchScreen&matchId="+this.jsonReader.getScreenAttributes().matchid+"&applyCurrencyThreshold="+o.x.call("eval","applyCurrencyThreshold")+"&noIncludedMovementMatches="+o.x.call("eval","noIncludedMovementMatches")+"&dateTabIndFlag=&dateTabInd="+o.x.call("eval","dateTabInd")+"&matchType="+o.x.call("eval","screen")+"&inputMethod="+o.x.call("eval","inputMethod")+"&currentIndex="+(parseInt(this.jsonReader.getScreenAttributes().matchindex,10)-1)+"&accountAccessStatus="+this.acctAccessFlag,this.inputData.url=this.baseURL+this.actionPath+this.actionMethod,this.requestParams["movement.id.entityId"]=this.jsonReader.getScreenAttributes().entityid,this.requestParams["movement.matchStatus"]=o.x.call("eval","status"),this.requestParams["movement.currencyCode"]=this.jsonReader.getScreenAttributes().currencycode,this.requestParams["movementDetail.matchQuality"]=this.jsonReader.getScreenAttributes().matchquality,this.inputData.send(this.requestParams)):(this.actionMethod="method=reconcile",this.actionMethod=this.actionMethod+"&nextRecord=next",this.actionMethod=this.actionMethod+"&day=&matchCount=&menuAccessId=&matchId="+this.jsonReader.getScreenAttributes().matchid+"&applyCurrencyThreshold="+o.x.call("eval","applyCurrencyThreshold")+"&noIncludedMovementMatches="+o.x.call("eval","noIncludedMovementMatches")+"&dateTabIndFlag=&dateTabInd="+o.x.call("eval","dateTabInd")+"&matchType="+o.x.call("eval","screen")+"&inputMethod="+o.x.call("eval","inputMethod")+"&currentIndex="+(parseInt(this.jsonReader.getScreenAttributes().matchindex,10)-1)+"&valueDate="+o.x.call("eval","valueDate")+"&accountAccessStatus="+this.acctAccessFlag,this.inputData.url=this.baseURL+this.actionPath+this.actionMethod,this.requestParams["movement.id.entityId"]=this.jsonReader.getScreenAttributes().entityid,this.requestParams["movement.matchStatus"]=o.x.call("eval","status"),this.requestParams["movement.currencyCode"]=this.jsonReader.getScreenAttributes().currencycode,this.requestParams["movementDetail.matchQuality"]=this.jsonReader.getScreenAttributes().matchquality,this.inputData.send(this.requestParams)):this.swtAlert.warning(o.Wb.getPredictMessage("label.matchIsInUseByanotherProcess",null))}}else this.alertCancelButtonHandler(this.suspendFlagListener,this.confirmFlagListener)},e.prototype.callFlexDisplay=function(){this.requestParameter["movement.matchId"]=this.removeLeadingZeros(this.matchIdText.text),this.actionMethod="method=flexdisplay",this.actionMethod=this.actionMethod+"&menuAccessId="+o.x.call("eval","menuAccessId")+"&matchIds='"+this.matchIds+"'&matchType=manual",this.actionMethod=this.actionMethod+"&day=&matchCount=&currencyCode=&status=&date=&entityId=&quality=&menuAccessId=&applyCurrencyThreshold=&noIncludedMovementMatches=&dateTabIndFlag=&dateTabInd=",this.inputData.url=this.baseURL+this.actionPath+this.actionMethod,this.inputData.cbStart=this.startOfComms.bind(this),this.inputData.cbStop=this.endOfComms.bind(this),this.inputData.send(this.requestParameter)},e.prototype.PosLvlCorrPreductStatus=function(t){for(var e="",n="",i="",s="",a=!1,o=0;o<t.length;o++)(n=t[o])!=e&&(i+=this.mmsdGrid.dataProvider[o].pred+","),e=n;for(var l=(i=i.substring(0,i.length-1)).split(","),d=1;d<l.length;d++)if(l[d-1]==l[d]){a=!0,s=l[d];break}return a&&(a="E"!=s),a},e.prototype.alertCloseHandler=function(t){for(var e;this.movIds.length;)e=this.movIds.pop(),o.x.call("unlockMovementOnServer",e);this.isValidMatchId()?(this.actionMethod="method=flexdisplay",this.actionMethod=this.actionMethod+"&menuAccessId="+o.x.call("eval","menuAccessId")+"&matchIds='"+this.matchIds+"'&matchType=manual",this.actionMethod=this.actionMethod+"&day=&matchCount=&currencyCode=&status=&date=&entityId=&quality=&menuAccessId=&applyCurrencyThreshold=&noIncludedMovementMatches=&dateTabIndFlag=&dateTabInd=",this.actionMethod=this.actionMethod+"&ScreenName=manual",this.inputData.url=this.baseURL+this.actionPath+this.actionMethod,this.inputData.send(this.requestParams),this.noteButton.enabled=!0,this.logButton.enabled=!0,this.unMatchButton.enabled=!0,this.suspendButton.enabled=!0,this.buttonsContainer.contains(this.confirmButton)&&(this.confirmButton.enabled=!0),this.reconButton.enabled=!0,this.addButton.enabled=!0,this.mvmntButton.enabled=!1,this.removeButton.enabled=!1,this.exportContainer.enabled=!0,this.optionsButton.enabled=!0,this.inScreenMatchId=this.matchIdText.text):(this.mmsdGrid.dataProvider=[],this.matchStatus.text="",this.matchQuality.text="",this.updateDate.text="",this.updateUser.text="",this.currencyCode.text="",this.posIntlvl.text="",this.posExtlvl.text="",this.selectedEntityId.text="",this.selectedEntityName.text="",this.logButton.enabled=!1,this.logButton.buttonMode=!1,this.noteButton.enabled=!1,this.noteButton.buttonMode=!1,this.mvmntButton.enabled=!1,this.mvmntButton.buttonMode=!1,this.unMatchButton.enabled=!1,this.unMatchButton.buttonMode=!1,this.suspendButton.enabled=!1,this.suspendButton.buttonMode=!1,this.buttonsContainer.contains(this.confirmButton)&&(this.confirmButton.enabled=!1,this.confirmButton.buttonMode=!1),this.removeButton.enabled=!1,this.removeButton.buttonMode=!1,this.addButton.enabled=!1,this.addButton.buttonMode=!1,this.mmsdGrid.selectable=!1,this.noteImage.setVisible(!1),this.exportContainer.enabled=!1)},e.prototype.isValidMatchId=function(){return this.matchIds=this.removeLeadingZeros(this.matchIdText.text),""!=this.matchIds&&!Number(this.matchIds)||(this.swtAlert.warning(o.Wb.getPredictMessage("label.validMatchId",null)),!1)},e.prototype.confirmMvmnt=function(){this.buttonsContainer.contains(this.confirmButton)&&(this.confirmButton.enabled=!1),this.subScreenName="Confirm",this.validateMatchId()},e.prototype.openLog=function(){this.subScreenName="Log",this.validateMatchId()},e.prototype.addMvmnt=function(){this.subScreenName="Add",this.validateMatchId()},e.prototype.unlockMvmnt=function(){if(null!=this.mmsdGrid&&this.mmsdGrid.selectedIndices.length>0)for(var t=0;t<this.mmsdGrid.selectedIndices.length;t++){new Object;var e="",n=o.x.call("checkLockOnServer",this.mmsdGrid.dataProvider[this.mmsdGrid.selectedIndices[t]].movement);this.currentUser.toLowerCase()!=n.toLowerCase()&&"true"!=n.toString()||(e=o.x.call("unlockMovementOnServer",this.mmsdGrid.dataProvider[this.mmsdGrid.selectedIndices[t]].movement).toString(),c.a.parseBooleanValue(e)||(o.Wb.getPredictMessage("label.movementCannotBeUnlocked",null),this.swtAlert.warning(o.Wb.getPredictMessage("label.movementCannotBeUnlocked",null),"Warning")))}},e.prototype.openMvmnt=function(){this.subScreenName="Mvmnt",this.validateMatchId()},e.prototype.openPreviousRecord=function(){if(this.diffValue.text="",!this.serverBusyFlag){this.actionMethod="method=next",this.actionMethod=this.actionMethod+"&nextRecord=previous";var t,e="false"==o.x.call("checkMatchId",this.matchIdText.text)?0:1;for(this.actionMethod=this.actionMethod+"&day=&matchCount=&menuAccessId="+o.x.call("eval","menuAccessId")+"&status="+o.x.call("eval","status")+"&applyCurrencyThreshold="+o.x.call("eval","applyCurrencyThreshold")+"&noIncludedMovementMatches="+o.x.call("eval","noIncludedMovementMatches")+"&dateTabIndFlag=&dateTabInd="+o.x.call("eval","dateTabInd")+"&matchType="+o.x.call("eval","screen")+"&inputMethod="+o.x.call("eval","inputMethod")+"&currentIndex="+(Number(this.jsonReader.getScreenAttributes().matchindex)-e)+"&valueDate="+o.x.call("eval","valueDate"),"scenarioSummary"==o.x.call("eval","calledFrom")?(this.actionMethod=this.actionMethod+"&scenarioId="+o.x.call("eval","scenarioId")+"&currGrp="+o.x.call("eval","currGrp")+"&calledFrom="+o.x.call("eval","calledFrom"),this.requestParams["movement.id.entityId"]=o.x.call("eval","entityId"),this.requestParams["movement.matchStatus"]=o.x.call("eval","status"),this.requestParams["movement.currencyCode"]=o.x.call("eval","currencyCode"),this.requestParams["movementDetail.matchQuality"]=o.x.call("eval","quality")):(this.requestParams["movement.id.entityId"]=this.jsonReader.getScreenAttributes().entityid,this.requestParams["movement.matchStatus"]=o.x.call("eval","status"),this.requestParams["movement.currencyCode"]=this.jsonReader.getScreenAttributes().currencycode,this.requestParams["movementDetail.matchQuality"]=this.jsonReader.getScreenAttributes().matchquality),this.inputData.url=this.baseURL+this.actionPath+this.actionMethod,this.selectedMovements=[];this.movIds.length;)t=this.movIds.pop(),o.x.call("unlockMovementOnServer",t);this.inputData.send(this.requestParams),this.mmsdGrid.selectedIndex=-1}},e.prototype.openNextRecord=function(){if(this.diffValue.text="",!this.serverBusyFlag){this.actionMethod="method=next",this.actionMethod=this.actionMethod+"&nextRecord=next";var t,e="false"==o.x.call("checkMatchId",this.matchIdText.text)?2:1;for(this.actionMethod=this.actionMethod+"&day=&matchCount=&menuAccessId="+o.x.call("eval","menuAccessId")+"&status="+o.x.call("eval","status")+"&applyCurrencyThreshold="+o.x.call("eval","applyCurrencyThreshold")+"&noIncludedMovementMatches="+o.x.call("eval","noIncludedMovementMatches")+"&dateTabIndFlag=&dateTabInd="+o.x.call("eval","dateTabInd")+"&matchType="+o.x.call("eval","screen")+"&inputMethod="+o.x.call("eval","inputMethod")+"&currentIndex="+(Number(this.jsonReader.getScreenAttributes().matchindex)-e)+"&valueDate="+o.x.call("eval","valueDate"),"scenarioSummary"==o.x.call("eval","calledFrom")?(this.actionMethod=this.actionMethod+"&scenarioId="+o.x.call("eval","scenarioId")+"&currGrp="+o.x.call("eval","currGrp")+"&calledFrom="+o.x.call("eval","calledFrom"),this.requestParams["movement.id.entityId"]=o.x.call("eval","entityId"),this.requestParams["movement.matchStatus"]=o.x.call("eval","status"),this.requestParams["movement.currencyCode"]=o.x.call("eval","currencyCode"),this.requestParams["movementDetail.matchQuality"]=o.x.call("eval","quality")):(this.requestParams["movement.id.entityId"]=this.jsonReader.getScreenAttributes().entityid,this.requestParams["movement.matchStatus"]=o.x.call("eval","status"),this.requestParams["movement.currencyCode"]=this.jsonReader.getScreenAttributes().currencycode,this.requestParams["movementDetail.matchQuality"]=this.jsonReader.getScreenAttributes().matchquality),this.inputData.url=this.baseURL+this.actionPath+this.actionMethod,this.selectedMovements=[];this.movIds.length;)t=this.movIds.pop(),o.x.call("unlockMovementOnServer",t);this.inputData.send(this.requestParams),this.mmsdGrid.selectedIndex=-1}},e.prototype.openUnMatch=function(){this.subScreenName="UnMatch",this.validateMatchId()},e.prototype.reconcileMvmnt=function(){this.subScreenName="Recon",this.validateMatchId()},e.prototype.removeMvmnt=function(){this.subScreenName="Remove",this.validateMatchId()},e.prototype.unMatchMatchId=function(){for(var t,e=o.x.call("eval","match"),n=this.lastReceivedJSON.movementmatchsummarydisplay.grid.rows.size,i="",s=!1,a=o.x.call("eval","calledFrom"),l=o.x.call("eval","parentScreen"),d=o.x.call("eval","loggedInUser"),h=0;h<n;h++)i+=this.mmsdGrid.dataProvider[h].movement+",";if(d==(t=o.x.call("checkLocksOnServer",i))&&"mvmntdisplay"==a&&"movementSummaryDisplay"==l&&(s=!0),c.a.parseBooleanValue(t)||s){var r=this.selectedEntityId.text,u=o.x.call("lockMatchOnServer","unmatch",this.matchIds,r);c.a.parseBooleanValue(u)?"manual"==e?(this.refreshFlag=!0,this.actionMethod="method=unmatch",this.actionMethod=this.actionMethod+"&day=&matchCount=&menuAccessId=&manualMatchScreen=frommanualMatchScreen&matchId="+this.jsonReader.getScreenAttributes().matchid+"&applyCurrencyThreshold="+o.x.call("eval","applyCurrencyThreshold")+"&noIncludedMovementMatches="+o.x.call("eval","noIncludedMovementMatches")+"&dateTabIndFlag=&dateTabInd="+o.x.call("eval","dateTabInd")+"&matchType="+o.x.call("eval","screen")+"&inputMethod="+o.x.call("eval","inputMethod")+"&currentIndex="+(parseInt(this.jsonReader.getScreenAttributes().matchindex,10)-1)+"&accountAccessStatus="+this.acctAccessFlag,this.inputData.url=this.baseURL+this.actionPath+this.actionMethod,this.requestParams["movement.id.entityId"]=this.jsonReader.getScreenAttributes().entityid,this.requestParams["movement.matchStatus"]=o.x.call("eval","status"),this.requestParams["movement.currencyCode"]=this.jsonReader.getScreenAttributes().currencycode,this.requestParams["movementDetail.matchQuality"]=this.jsonReader.getScreenAttributes().matchquality,this.inputData.send(this.requestParams)):(this.actionMethod="method=unmatch",this.actionMethod=this.actionMethod+"&day=&matchCount=&menuAccessId=&matchId="+this.jsonReader.getScreenAttributes().matchid+"&applyCurrencyThreshold="+o.x.call("eval","applyCurrencyThreshold")+"&noIncludedMovementMatches="+o.x.call("eval","noIncludedMovementMatches")+"&dateTabIndFlag=&dateTabInd="+o.x.call("eval","dateTabInd")+"&matchType="+o.x.call("eval","screen")+"&inputMethod="+o.x.call("eval","inputMethod")+"&currentIndex="+(parseInt(this.jsonReader.getScreenAttributes().matchindex,10)-1)+"&valueDate="+o.x.call("eval","valueDate")+"&accountAccessStatus="+this.acctAccessFlag,this.inputData.url=this.baseURL+this.actionPath+this.actionMethod,this.requestParams["movement.id.entityId"]=this.jsonReader.getScreenAttributes().entityid,this.requestParams["movement.matchStatus"]=o.x.call("eval","status"),this.requestParams["movement.currencyCode"]=this.jsonReader.getScreenAttributes().currencycode,this.requestParams["movementDetail.matchQuality"]=this.jsonReader.getScreenAttributes().matchquality,this.inputData.send(this.requestParams)):this.swtAlert.warning(o.Wb.getPredictMessage("label.matchIsInUseByanotherProces",null))}else this.swtAlert.warning(o.Wb.getPredictMessage("label.mvmtAreBusy",null)+" "+t)},e.prototype.fontSettingHandler=function(){var t=this;this.win=o.Eb.createPopUp(this,d.a,{title:"Options",fontSizeValue:this.selectedFont}),this.win.isModal=!0,this.win.enableResize=!1,this.win.width="340",this.win.height="140",this.win.id="fontPopUp",this.win.enableResize=!1,this.win.showControls=!0,this.win.onClose.subscribe(function(e){t.submitFontSize(e)},function(t){console.log(t)}),this.win.display()},e.prototype.submitFontSize=function(t){this.fontValue=t.fontSize.value,"N"==this.fontValue?(this.selectedFont=0,this.fontLabel=t.fontSize.label,this.mmsdGrid.styleName="dataGridNormal",this.mmsdGrid.rowHeight=18,this.tempFontSize="Normal",this.fontRequest=o.x.call("getUpdateFontSize",this.fontLabel)):"S"==this.fontValue&&(this.selectedFont=1,this.fontLabel=t.fontSize.label,this.mmsdGrid.styleName="dataGridSmall",this.mmsdGrid.rowHeight=15,this.fontRequest=o.x.call("getUpdateFontSize",this.fontLabel)),null!=this.fontRequest&&""!=this.fontRequest&&(this.updateFontSize.url=this.baseURL+this.fontRequest,this.updateFontSize.send())},e.prototype.cellLogic=function(t){for(var e=null,n=!1,i=o.x.call("eval","calledFrom"),s=o.x.call("eval","parentScreen"),a=0;a<this.mmsdGrid.selectedItems.length;a++)"mvmntdisplay"==i&&"movementSummaryDisplay"==s&&(n=!0),new Object,e="",-1==this.previousSelectedIndices.indexOf(this.mmsdGrid.selectedItems[a].movement.content)&&(e=o.x.call("lockMovementOnServer",this.mmsdGrid.selectedItems[a].movement.content).toString(),c.a.parseBooleanValue(e)||n||this.swtAlert.warning(o.Wb.getPredictMessage("manualInput.id.movementId",null)+" "+this.mmsdGrid.selectedItems[a].movement.content+" "+o.Wb.getPredictMessage("label.isInBusyBy",null)+" "+e));var l=[];for(a=0;a<this.mmsdGrid.selectedItems.length;a++)l.push(this.mmsdGrid.selectedItems[a].movement.content);for(a=0;a<this.previousSelectedIndices.length;a++)if(new Object,e="",-1==l.indexOf(this.previousSelectedIndices[a])){var d=o.x.call("checkLockOnServer",this.previousSelectedIndices[a]);this.currentUser.toLowerCase()!=d.toLowerCase()&&"true"!=d.toString()||(e=o.x.call("unlockMovementOnServer",this.previousSelectedIndices[a]).toString(),c.a.parseBooleanValue(e)||(o.Wb.getPredictMessage("label.movementCannotBeUnlocked",null),this.swtAlert.warning(o.Wb.getPredictMessage("label.movementCannotBeUnlocked",null),"Warning")))}this.previousSelectedIndices=[];for(a=0;a<this.mmsdGrid.selectedItems.length;a++)this.previousSelectedIndices.push(this.mmsdGrid.selectedItems[a].movement.content);this.mmsdGrid.selectedIndices.length>1?c.a.parseBooleanValue(this.fullAccess)?(this.unMatchButton.enabled=!0,this.removeButton.enabled=!0,this.noteButton.enabled=!0,this.noteButton.buttonMode=!0,this.logButton.enabled=!0,this.logButton.buttonMode=!0,this.buttonsContainer.contains(this.suspendButton)&&(this.suspendButton.enabled=!0,this.suspendButton.buttonMode=!0),this.buttonsContainer.contains(this.confirmButton)&&(this.confirmButton.enabled=!0,this.confirmButton.buttonMode=!0),this.reconButton.enabled=!0,this.reconButton.buttonMode=!0,this.mvmntButton.enabled=!1,this.mvmntButton.buttonMode=!0,this.unMatchButton.buttonMode=!0,this.removeButton.buttonMode=!0,this.removeButton.enabled=!0):(this.unMatchButton.enabled=!1,this.removeButton.enabled=!1,this.mvmntButton.enabled=!1,this.mvmntButton.buttonMode=!0):1==this.mmsdGrid.selectedIndices.length?(this.mvmntButton.enabled=!0,c.a.parseBooleanValue(this.fullAccess)?(this.noteButton.enabled=!0,this.noteButton.buttonMode=!0,this.logButton.enabled=!0,this.logButton.buttonMode=!0,this.buttonsContainer.contains(this.suspendButton)&&(this.suspendButton.enabled=!0,this.suspendButton.buttonMode=!0),this.buttonsContainer.contains(this.confirmButton)&&(this.confirmButton.enabled=!0,this.confirmButton.buttonMode=!0),this.reconButton.enabled=!0,this.reconButton.buttonMode=!0,this.mvmntButton.buttonMode=!0,this.unMatchButton.buttonMode=!0,this.removeButton.buttonMode=!0,this.removeButton.enabled=!0):(this.unMatchButton.enabled=!1,this.removeButton.enabled=!1,this.mvmntButton.buttonMode=!0)):0==this.mmsdGrid.selectedIndices.length&&(0==this.mmsdGrid.dataProvider.length?(this.noteButton.enabled=!1,this.logButton.enabled=!1,this.unMatchButton.enabled=!1,this.buttonsContainer.contains(this.suspendButton)&&(this.suspendButton.enabled=!1),this.buttonsContainer.contains(this.confirmButton)&&(this.confirmButton.enabled=!1)):c.a.parseBooleanValue(this.fullAccess)?(this.noteButton.enabled=!0,this.logButton.enabled=!0,this.unMatchButton.enabled=!0,this.buttonsContainer.contains(this.suspendButton)&&(this.suspendButton.enabled=!0),this.reconButton.enabled=!0,this.buttonsContainer.contains(this.confirmButton)&&(this.confirmButton.enabled=!0,this.confirmButton.buttonMode=!0)):(this.noteButton.enabled=!0,this.logButton.enabled=!0,this.unMatchButton.enabled=!1,this.buttonsContainer.contains(this.suspendButton)&&(this.suspendButton.enabled=!1),this.buttonsContainer.contains(this.confirmButton)&&(this.confirmButton.enabled=!1)),this.removeButton.enabled=!1,this.removeButton.buttonMode=!1,this.mvmntButton.enabled=!1,this.mvmntButton.buttonMode=!1,this.noteButton.buttonMode=!0,this.logButton.buttonMode=!0,this.unMatchButton.buttonMode=!0,this.buttonsContainer.contains(this.suspendButton)&&(this.suspendButton.buttonMode=!0),this.reconButton.buttonMode=!0),""!=o.x.call("eval","archiveId")&&(this.removeButton.enabled=!1,this.removeButton.buttonMode=!1,this.mvmntButton.enabled=!1,this.mvmntButton.buttonMode=!1,this.logButton.enabled=!1,this.logButton.buttonMode=!1,this.unMatchButton.enabled=!1,this.unMatchButton.buttonMode=!1,this.buttonsContainer.contains(this.suspendButton)&&(this.suspendButton.enabled=!1,this.suspendButton.buttonMode=!1),this.buttonsContainer.contains(this.confirmButton)&&(this.confirmButton.enabled=!1,this.confirmButton.buttonMode=!1),this.addButton.enabled=!1,this.addButton.buttonMode=!1),this.acctAccessFlag||(this.removeButton.enabled=!1,this.removeButton.buttonMode=!1,this.unMatchButton.enabled=!1,this.unMatchButton.buttonMode=!1,this.buttonsContainer.contains(this.suspendButton)&&(this.suspendButton.enabled=!1,this.suspendButton.buttonMode=!1),this.reconButton.enabled=!1,this.reconButton.buttonMode=!1,this.buttonsContainer.contains(this.confirmButton)&&(this.confirmButton.enabled=!1,this.confirmButton.buttonMode=!1),this.addButton.enabled=!1,this.addButton.buttonMode=!1);for(var h="",r=0;r<this.mmsdGrid.dataProvider.length;r++)h+=this.mmsdGrid.dataProvider[r].movement+",";o.x.call("setSelectedMovementForLock",h)},e.prototype.cellLogic2=function(t){var e=!1,n=o.x.call("eval","calledFrom"),i=o.x.call("eval","parentScreen");if(this.mmsdGrid.selectedIndices.length>0){this.previousSelectedIndices=this.mmsdGrid.selectedIndices;for(var s=0;s<this.mmsdGrid.selectedIndices.length;s++){for(var a=!1,l=0;l<this.selectedMovements.length;l++)this.selectedMovements[l]==this.mmsdGrid.dataProvider[this.mmsdGrid.selectedIndices[s]].movement&&(a=!0);if("mvmntdisplay"==n&&"movementSummaryDisplay"==i&&(e=!0),!a){var d,h=new Object;if(h=o.x.call("lockMovementOnServer",this.mmsdGrid.dataProvider[this.mmsdGrid.selectedIndices[s]].movement),this.movIds.push(this.mmsdGrid.dataProvider[this.mmsdGrid.selectedIndices[s]].movement),d=h.toString(),!c.a.parseBooleanValue(d)&&!e){this.swtAlert.warning(o.Wb.getPredictMessage("manualInput.id.movementId",null)+" "+this.mmsdGrid.dataProvider[this.mmsdGrid.selectedIndices[s]].movement+" "+o.Wb.getPredictMessage("label.isInBusyBy",null)+" "+d);for(var r=this.mmsdGrid.selectedIndices,u=0;u<r.length;u++)r[u]==this.mmsdGrid.selectedIndices[s]&&(r.splice(u,1),s-=1);this.mmsdGrid.selectedIndices=[],this.mmsdGrid.selectedIndices=r}}}}for(var m=[],b=[],v=0;v<this.mmsdGrid.selectedIndices.length;v++)m.push(this.mmsdGrid.dataProvider[this.mmsdGrid.selectedIndices[v]].movement),b.push(this.mmsdGrid.dataProvider[this.mmsdGrid.selectedIndices[v]].account);for(var p=0;p<this.selectedMovements.length;p++){for(var g=!1,M=!1,B=0;B<this.mmsdGrid.dataProvider.length;B++)this.selectedMovements[p]==this.mmsdGrid.dataProvider[B].movement&&(M=!0);M||(g=!0);for(var I=0;I<m.length;I++)M&&this.selectedMovements[p]==m[I]&&(g=!0);if(!g){new Object;var y="",f=o.x.call("checkLockOnServer",this.selectedMovements[p]);this.currentUser.toLowerCase()!=f.toLowerCase()&&"true"!=f.toString()||(y=o.x.call("unlockMovementOnServer",this.selectedMovements[p]).toString(),c.a.parseBooleanValue(y)||this.swtAlert.warning(o.Wb.getPredictMessage("label.movementCannotBeUnlocked",null)))}}this.selectedMovements=[];for(var x=0;x<this.mmsdGrid.selectedIndices.length;x++)this.selectedMovements.push(this.mmsdGrid.dataProvider[this.mmsdGrid.selectedIndices[x]].movement);this.mmsdGrid.selectedIndices.length>1?c.a.parseBooleanValue(this.fullAccess)?(this.unMatchButton.enabled=!0,this.removeButton.enabled=!0,this.noteButton.enabled=!0,this.noteButton.buttonMode=!0,this.logButton.enabled=!0,this.logButton.buttonMode=!0,this.buttonsContainer.contains(this.suspendButton)&&(this.suspendButton.enabled=!0,this.suspendButton.buttonMode=!0),this.buttonsContainer.contains(this.confirmButton)&&(this.confirmButton.enabled=!0,this.confirmButton.buttonMode=!0),this.reconButton.enabled=!0,this.reconButton.buttonMode=!0,this.mvmntButton.enabled=!1,this.mvmntButton.buttonMode=!0,this.unMatchButton.buttonMode=!0,this.removeButton.buttonMode=!0,this.removeButton.enabled=!0):(this.unMatchButton.enabled=!1,this.removeButton.enabled=!1,this.mvmntButton.enabled=!1,this.mvmntButton.buttonMode=!0):1==this.mmsdGrid.selectedIndices.length?(this.mvmntButton.enabled=!0,c.a.parseBooleanValue(this.fullAccess)?(this.noteButton.enabled=!0,this.noteButton.buttonMode=!0,this.logButton.enabled=!0,this.logButton.buttonMode=!0,this.buttonsContainer.contains(this.suspendButton)&&(this.suspendButton.enabled=!0,this.suspendButton.buttonMode=!0),this.buttonsContainer.contains(this.confirmButton)&&(this.confirmButton.enabled=!0,this.confirmButton.buttonMode=!0),this.reconButton.enabled=!0,this.reconButton.buttonMode=!0,this.mvmntButton.buttonMode=!0,this.unMatchButton.buttonMode=!0,this.removeButton.buttonMode=!0,this.removeButton.enabled=!0):(this.unMatchButton.enabled=!1,this.removeButton.enabled=!1,this.mvmntButton.buttonMode=!0)):0==this.mmsdGrid.selectedIndices.length&&(0==this.mmsdGrid.dataProvider.length?(this.noteButton.enabled=!1,this.logButton.enabled=!1,this.unMatchButton.enabled=!1,this.buttonsContainer.contains(this.suspendButton)&&(this.suspendButton.enabled=!1),this.buttonsContainer.contains(this.confirmButton)&&(this.confirmButton.enabled=!1)):c.a.parseBooleanValue(this.fullAccess)?(this.noteButton.enabled=!0,this.logButton.enabled=!0,this.unMatchButton.enabled=!0,this.buttonsContainer.contains(this.suspendButton)&&(this.suspendButton.enabled=!0,this.suspendButton.buttonMode=!0),this.reconButton.enabled=!0,this.buttonsContainer.contains(this.confirmButton)&&(this.confirmButton.enabled=!0,this.confirmButton.buttonMode=!0)):(this.noteButton.enabled=!0,this.logButton.enabled=!0,this.unMatchButton.enabled=!1,this.buttonsContainer.contains(this.suspendButton)&&(this.suspendButton.enabled=!1),this.buttonsContainer.contains(this.confirmButton)&&(this.confirmButton.enabled=!1)),this.removeButton.enabled=!1,this.removeButton.buttonMode=!1,this.mvmntButton.enabled=!1,this.mvmntButton.buttonMode=!1,this.noteButton.buttonMode=!0,this.logButton.buttonMode=!0,this.unMatchButton.buttonMode=!0,this.reconButton.buttonMode=!0),""!=o.x.call("eval","archiveId")&&(this.removeButton.enabled=!1,this.removeButton.buttonMode=!1,this.mvmntButton.enabled=!1,this.mvmntButton.buttonMode=!1,this.logButton.enabled=!1,this.logButton.buttonMode=!1,this.unMatchButton.enabled=!1,this.unMatchButton.buttonMode=!1,this.buttonsContainer.contains(this.suspendButton)&&(this.suspendButton.enabled=!1,this.suspendButton.buttonMode=!1),this.buttonsContainer.contains(this.confirmButton)&&(this.confirmButton.enabled=!1,this.confirmButton.buttonMode=!1),this.addButton.enabled=!1,this.addButton.buttonMode=!1),this.acctAccessFlag||(this.removeButton.enabled=!1,this.removeButton.buttonMode=!1,this.unMatchButton.enabled=!1,this.unMatchButton.buttonMode=!1,this.buttonsContainer.contains(this.suspendButton)&&(this.suspendButton.enabled=!1,this.suspendButton.buttonMode=!1),this.reconButton.enabled=!1,this.reconButton.buttonMode=!1,this.buttonsContainer.contains(this.confirmButton)&&(this.confirmButton.enabled=!1,this.confirmButton.buttonMode=!1),this.addButton.enabled=!1,this.addButton.buttonMode=!1)},e.prototype.onPaymentSuccess=function(t){var e=this;setTimeout(function(){t.detail&&e.movementNotes(t.detail.method,t.detail.movementId,t.detail.notesFlag)},0)},e.prototype.movementNotes=function(t,e,n){if("flexadd"==t){this.isActionRun=!0,this.isRefreshRun=!0,this.actionMethod="method="+t,this.actionMethod=this.actionMethod+"&menuAccessId="+o.x.call("eval","menuAccessId")+"&copiedMovementId="+e+"&matchType="+o.x.call("eval","screen")+"&matchList="+o.x.call("eval","matches"),this.actionMethod=this.actionMethod+"&day=&currencyCode=&status="+this.jsonReader.getScreenAttributes().mvmntstatus+"&date=&entityId="+this.jsonReader.getScreenAttributes().entityid+"&quality=&menuAccessId=&applyCurrencyThreshold="+o.x.call("eval","applyCurrencyThreshold")+"&noIncludedMovementMatches="+o.x.call("eval","noIncludedMovementMatches")+"&dateTabIndFlag=&dateTabInd="+o.x.call("eval","dateTabInd")+"&currentIndex="+(parseInt(this.jsonReader.getScreenAttributes().matchindex,10)-1);new Object;var i="";if(this.mmsdGrid.selectedIndices.length>0)for(var s=0;s<this.mmsdGrid.selectedIndices.length;s++)i=o.x.call("unlockMovementOnServer",this.mmsdGrid.dataProvider[this.mmsdGrid.selectedIndices[s]].movement).toString(),c.a.parseBooleanValue(i)||this.swtAlert.warning(o.Wb.getPredictMessage("label.movementCannotBeUnlocked",null),"Warning");this.mmsdGrid.selectedIndex=-1,""!=n&&(this.actionMethod=this.actionMethod+"&matchCount="+n),this.requestParams["movement.matchId"]=this.jsonReader.getScreenAttributes().matchid,this.requestParams["movement.matchStatus"]=o.x.call("eval","status"),this.requestParams["movementDetail.matchQuality"]=this.jsonReader.getScreenAttributes().matchquality,this.inputData.url=this.baseURL+this.actionPath+this.actionMethod,this.inputData.send(this.requestParams)}else this.requestURL=o.Wb.getBaseURL(),"Y"==n?(this.noteImage.source=this.requestURL+o.x.call("eval","notesImage"),this.noteImage.toolTip="Notes Available"):(this.noteImage.source=this.requestURL+o.x.call("eval","blankImage"),this.noteImage.toolTip="");if("unlockallmovements"==t){var a="";if(null!=this.mmsdGrid&&this.mmsdGrid.dataProvider.length>0)for(var l=0;l<this.mmsdGrid.dataProvider.length;l++)a=this.mmsdGrid.dataProvider[l].movement,o.x.call("unlockMovementOnServer",a)}},e.prototype.startOfComms=function(){try{if(this.isRefreshRun){this.isRefreshRun=!1;var t=0;this.lastReceivedJSON&&this.lastReceivedJSON.movementmatchsummarydisplay&&this.lastReceivedJSON.movementmatchsummarydisplay.grid&&this.lastReceivedJSON.movementmatchsummarydisplay.grid.rows&&(t=this.lastReceivedJSON.movementmatchsummarydisplay.grid.rows.size),t>100&&(this.winProgress=o.Eb.createPopUp(this,a.a,{title:"Processing..."}),this.winProgress.width="280",this.winProgress.height="120",this.winProgress.id="myOptionsPopUp",this.winProgress.enableResize=!1,this.winProgress.showControls=!1,this.winProgress.isModal=!0,this.winProgress.onClose.subscribe(function(){},function(t){console.log(t)}),this.winProgress.display())}}catch(e){o.Wb.logError(e,this.moduleId,"Dashboard","optionsHandler",this.errorLocation)}this.loadingImage.setVisible(!0)},e.prototype.endOfComms=function(){try{this.winProgress.close()}catch(t){}this.loadingImage.setVisible(!1)},e.prototype.startOfCommsAction=function(){try{if(this.isActionRun){this.isActionRun=!1;var t=0;this.lastReceivedJSON&&this.lastReceivedJSON.movementmatchsummarydisplay&&this.lastReceivedJSON.movementmatchsummarydisplay.grid&&this.lastReceivedJSON.movementmatchsummarydisplay.grid.rows&&(t=this.lastReceivedJSON.movementmatchsummarydisplay.grid.rows.size),t>100&&(this.winProgress=o.Eb.createPopUp(this,a.a,{title:"Processing..."}),this.winProgress.width="280",this.winProgress.height="120",this.winProgress.id="myOptionsPopUp",this.winProgress.enableResize=!1,this.winProgress.showControls=!1,this.winProgress.isModal=!0,this.winProgress.onClose.subscribe(function(){},function(t){console.log(t)}),this.winProgress.display())}}catch(e){o.Wb.logError(e,this.moduleId,"Dashboard","optionsHandler",this.errorLocation)}this.loadingImage.setVisible(!0)},e.prototype.endOfCommsAction=function(){try{this.winProgress.close()}catch(t){}this.loadingImage.setVisible(!1)},e.prototype.connError=function(){this.swtAlert.error(""+this.invalidComms,"Error")},e.prototype.getMatchId=function(t){if("manual"==this.matchType&&t.keyCode==o.N.ENTER){var e=void 0;for(this.selectedMovements=[];this.movIds.length;)e=this.movIds.pop(),o.x.call("unlockMovementOnServer",e);this.inScreenMatchId=this.removeLeadingZeros(this.matchIdText.text),this.matchIds=this.removeLeadingZeros(this.matchIdText.text),""==this.matchIds?(this.swtAlert.warning("Please enter a valid MatchId"),this.mmsdGrid.dataProvider=[],this.matchStatus.text="",this.matchQuality.text="",this.updateDate.text="",this.updateUser.text="",this.currencyCode.text="",this.posIntlvl.text="",this.posExtlvl.text="",this.selectedEntityId.text="",this.selectedEntityName.text="",this.logButton.enabled=!1,this.logButton.buttonMode=!1,this.buttonsContainer.contains(this.noteButton)&&(this.noteButton.enabled=!1,this.noteButton.buttonMode=!1),this.mvmntButton.enabled=!1,this.mvmntButton.buttonMode=!1,this.buttonsContainer.contains(this.unMatchButton)&&(this.unMatchButton.enabled=!1,this.unMatchButton.buttonMode=!1),this.buttonsContainer.contains(this.suspendButton)&&(this.suspendButton.enabled=!1,this.suspendButton.buttonMode=!1),this.buttonsContainer.contains(this.confirmButton)&&(this.confirmButton.enabled=!1,this.confirmButton.buttonMode=!1),this.buttonsContainer.contains(this.removeButton)&&(this.removeButton.enabled=!1,this.removeButton.buttonMode=!1),this.buttonsContainer.contains(this.addButton)&&(this.addButton.enabled=!1,this.addButton.buttonMode=!1),this.noteImage.setVisible(!1),this.exportContainer.enabled=!1):(this.actionMethod="method=flexdisplay",this.actionMethod=this.actionMethod+"&menuAccessId="+o.x.call("eval","menuAccessId")+"&matchIds='"+this.matchIds+"'&matchType=manual",this.actionMethod=this.actionMethod+"&day=&matchCount=&currencyCode=&status=&date=&quality=&menuAccessId=&applyCurrencyThreshold=&noIncludedMovementMatches=&dateTabIndFlag=&dateTabInd=&archiveId="+o.x.call("eval","archiveId")+"&entityId="+o.x.call("eval","entityId"),this.actionMethod=this.actionMethod+"&ScreenName=manual",this.inputData.url=this.baseURL+this.actionPath+this.actionMethod,this.inputData.send(this.requestParams),this.noteButton.enabled=!0,this.logButton.enabled=!0,this.unMatchButton.enabled=!0,this.buttonsContainer.contains(this.suspendButton)&&(this.suspendButton.enabled=!0),this.buttonsContainer.contains(this.confirmButton)&&(this.confirmButton.enabled=!0),this.buttonsContainer.contains(this.reconButton)&&(this.reconButton.enabled=!0),this.buttonsContainer.contains(this.addButton)&&(this.addButton.enabled=!0),this.mvmntButton.enabled=!1,this.buttonsContainer.contains(this.removeButton)&&(this.removeButton.enabled=!1),this.exportContainer.enabled=!0,this.optionsButton.enabled=!0,this.noteImage.setVisible(!0))}},e.prototype.initializeMenus=function(){this.screenVersion.loadScreenVersion(this,this.screenName,this.versionNumber,this.versionDate);var t=new o.n(o.Wb.getPredictMessage("screen.showJSON",null));t.MenuItemSelect=this.showJSONSelect.bind(this),this.screenVersion.svContextMenu.customItems.push(t),this.contextMenu=this.screenVersion.svContextMenu},e.prototype.showJSONSelect=function(t){this.showJsonPopup=o.Eb.createPopUp(this,o.M,{jsonData:this.lastReceivedJSON}),this.showJsonPopup.width="700",this.showJsonPopup.title="Last Received JSON",this.showJsonPopup.height="500",this.showJsonPopup.enableResize=!1,this.showJsonPopup.showControls=!0,this.showJsonPopup.display()},e.prototype.removeLeadingZeros=function(t){for(;0==t.indexOf("0");)t=t.substr(1);return t},e.prototype.filterUpdate=function(t){this.cellLogic(t)},e.prototype.closeHandler=function(t){if(null!=this.mmsdGrid&&this.mmsdGrid.selectedIndices.length>0)for(var e=0;e<this.mmsdGrid.selectedIndices.length;e++){new Object;var n=o.x.call("checkLockOnServer",this.mmsdGrid.dataProvider[this.mmsdGrid.selectedIndices[e]].movement);if(this.currentUser.toLowerCase()===n.toLowerCase()||c.a.parseBooleanValue(n)){var i=o.x.call("unlockMovementOnServer",this.mmsdGrid.dataProvider[this.mmsdGrid.selectedIndices[e]].movement);c.a.parseBooleanValue(i)||this.swtAlert.warning(o.Wb.getPredictMessage("label.movementCannotBeUnlocked",null))}}"movementDisplay"!=o.x.call("eval","inputMethod")&&"manual"!=this.matchType?o.x.call("QueueRefresh"):o.x.call("close")},e.prototype.CalculateAmtDiff=function(){var t,e,n=0,i="",s=0,a=0,l=[],d=[],h=[];this.diffValue.text="";for(var r=0;r<this.mmsdGrid.dataProvider.length;r++){if(t=this.mmsdGrid.dataProvider[r].positionlevel?parseInt(this.mmsdGrid.dataProvider[r].positionlevel):parseInt(this.mmsdGrid.dataProvider[r].slickgrid_rowcontent.pos.positionlevel),d.push(t),e=this.mmsdGrid.dataProvider[r].sign?this.mmsdGrid.dataProvider[r].sign:this.mmsdGrid.dataProvider[r].slickgrid_rowcontent.sign.content,i=this.mmsdGrid.dataProvider[r].amount?this.mmsdGrid.dataProvider[r].amount:this.mmsdGrid.dataProvider[r].slickgrid_rowcontent.amount.content,"currencyPat1"==this.currencyPattern){h=i.split(","),i="";for(var c=0;c<h.length;c++)i+=h[c]}else{h=i.split("."),i="";for(var u=0;u<h.length;u++)i+=h[u];i=i.replace(",",".")}switch(n=parseFloat(i),"D"==e?n:n,t){case 1:null==l[0]&&(l[0]=0),l[0]="D"==e?l[0]-n:l[0]+n;break;case 2:null==l[1]&&(l[1]=0),l[1]="D"==e?l[1]-n:l[1]+n;break;case 3:null==l[2]&&(l[2]=0),l[2]="D"==e?l[2]-n:l[2]+n;break;case 4:null==l[3]&&(l[3]=0),l[3]="D"==e?l[3]-n:l[3]+n;break;case 5:null==l[4]&&(l[4]=0),l[4]="D"==e?l[4]-n:l[4]+n;break;case 6:null==l[5]&&(l[5]=0),l[5]="D"==e?l[5]-n:l[5]+n;break;case 7:null==l[6]&&(l[6]=0),l[6]="D"==e?l[6]-n:l[6]+n;break;case 8:null==l[7]&&(l[7]=0),l[7]="D"==e?l[7]-n:l[7]+n;break;case 9:null==l[8]&&(l[8]=0),l[8]="D"==e?l[8]-n:l[8]+n}for(var m=0,b=0;b<l.length;b++)null!=l[b]&&(0==m?(m=-1,s=l[b],a=l[b]):("Y",l[b]<a&&(a=l[b]),l[b]>s&&(s=l[b])));var v={precision:"",rounding:""};v.precision="2",v.rounding="nearest";for(var p=0,g=!1,M=0;M<d.length;M++)if(d[M]&&d[M+1]&&d[M]!==d[M+1]){g=!0;break}if(0==(p=s-a))g?"currencyPat1"==this.currencyPattern?this.diffValue.text="0.00":this.diffValue.text="0,00":this.diffValue.text="";else{var B=this.addZeroes(p.toString());if(-1==B.indexOf("."))B+="00";else{var I;I=B.split("."),B=I[0]+I[1]}this.diffValue.text=g?o.x.call("expandAmtDifference",B,this.currencyPattern,this.currencyCode.text):""}}},e.prototype.addZeroes=function(t){t.split(".")[1];return Number(t).toFixed(2)},e}(o.yb),m=[{path:"",component:u}],b=(l.l.forChild(m),function(){return function(){}}()),v=n("pMnS"),p=n("RChO"),g=n("t6HQ"),M=n("WFGK"),B=n("5FqG"),I=n("Ip0R"),y=n("gIcY"),f=n("t/Na"),x=n("sE5F"),C=n("OzfB"),S=n("T7CS"),R=n("S7LP"),P=n("6aHO"),A=n("WzUx"),T=n("A7o+"),w=n("zCE2"),L=n("Jg5P"),G=n("3R0m"),D=n("hhbb"),F=n("5rxC"),q=n("Fzqc"),j=n("21Lb"),k=n("hUWP"),N=n("3pJQ"),O=n("V9q+"),W=n("VDKW"),J=n("kXfT"),U=n("BGbe");n.d(e,"MovementMatchSummaryDisplayModuleNgFactory",function(){return E}),n.d(e,"RenderType_MovementMatchSummaryDisplay",function(){return z}),n.d(e,"View_MovementMatchSummaryDisplay_0",function(){return _}),n.d(e,"View_MovementMatchSummaryDisplay_Host_0",function(){return Z}),n.d(e,"MovementMatchSummaryDisplayNgFactory",function(){return Q});var E=i.Gb(b,[],function(t){return i.Qb([i.Rb(512,i.n,i.vb,[[8,[v.a,p.a,g.a,M.a,B.Cb,B.Pb,B.r,B.rc,B.s,B.Ab,B.Bb,B.Db,B.qd,B.Hb,B.k,B.Ib,B.Nb,B.Ub,B.yb,B.Jb,B.v,B.A,B.e,B.c,B.g,B.d,B.Kb,B.f,B.ec,B.Wb,B.bc,B.ac,B.sc,B.fc,B.lc,B.jc,B.Eb,B.Fb,B.mc,B.Lb,B.nc,B.Mb,B.dc,B.Rb,B.b,B.ic,B.Yb,B.Sb,B.kc,B.y,B.Qb,B.cc,B.hc,B.pc,B.oc,B.xb,B.p,B.q,B.o,B.h,B.j,B.w,B.Zb,B.i,B.m,B.Vb,B.Ob,B.Gb,B.Xb,B.t,B.tc,B.zb,B.n,B.qc,B.a,B.z,B.rd,B.sd,B.x,B.td,B.gc,B.l,B.u,B.ud,B.Tb,Q]],[3,i.n],i.J]),i.Rb(4608,I.m,I.l,[i.F,[2,I.u]]),i.Rb(4608,y.c,y.c,[]),i.Rb(4608,y.p,y.p,[]),i.Rb(4608,f.j,f.p,[I.c,i.O,f.n]),i.Rb(4608,f.q,f.q,[f.j,f.o]),i.Rb(5120,f.a,function(t){return[t,new o.tb]},[f.q]),i.Rb(4608,f.m,f.m,[]),i.Rb(6144,f.k,null,[f.m]),i.Rb(4608,f.i,f.i,[f.k]),i.Rb(6144,f.b,null,[f.i]),i.Rb(4608,f.f,f.l,[f.b,i.B]),i.Rb(4608,f.c,f.c,[f.f]),i.Rb(4608,x.c,x.c,[]),i.Rb(4608,x.g,x.b,[]),i.Rb(5120,x.i,x.j,[]),i.Rb(4608,x.h,x.h,[x.c,x.g,x.i]),i.Rb(4608,x.f,x.a,[]),i.Rb(5120,x.d,x.k,[x.h,x.f]),i.Rb(5120,i.b,function(t,e){return[C.j(t,e)]},[I.c,i.O]),i.Rb(4608,S.a,S.a,[]),i.Rb(4608,R.a,R.a,[]),i.Rb(4608,P.a,P.a,[i.n,i.L,i.B,R.a,i.g]),i.Rb(4608,A.c,A.c,[i.n,i.g,i.B]),i.Rb(4608,A.e,A.e,[A.c]),i.Rb(4608,T.l,T.l,[]),i.Rb(4608,T.h,T.g,[]),i.Rb(4608,T.c,T.f,[]),i.Rb(4608,T.j,T.d,[]),i.Rb(4608,T.b,T.a,[]),i.Rb(4608,T.k,T.k,[T.l,T.h,T.c,T.j,T.b,T.m,T.n]),i.Rb(4608,A.i,A.i,[[2,T.k]]),i.Rb(4608,A.r,A.r,[A.L,[2,T.k],A.i]),i.Rb(4608,A.t,A.t,[]),i.Rb(4608,A.w,A.w,[]),i.Rb(1073742336,l.l,l.l,[[2,l.r],[2,l.k]]),i.Rb(1073742336,I.b,I.b,[]),i.Rb(1073742336,y.n,y.n,[]),i.Rb(1073742336,y.l,y.l,[]),i.Rb(1073742336,w.a,w.a,[]),i.Rb(1073742336,L.a,L.a,[]),i.Rb(1073742336,y.e,y.e,[]),i.Rb(1073742336,G.a,G.a,[]),i.Rb(1073742336,T.i,T.i,[]),i.Rb(1073742336,A.b,A.b,[]),i.Rb(1073742336,f.e,f.e,[]),i.Rb(1073742336,f.d,f.d,[]),i.Rb(1073742336,x.e,x.e,[]),i.Rb(1073742336,D.b,D.b,[]),i.Rb(1073742336,F.b,F.b,[]),i.Rb(1073742336,C.c,C.c,[]),i.Rb(1073742336,q.a,q.a,[]),i.Rb(1073742336,j.d,j.d,[]),i.Rb(1073742336,k.c,k.c,[]),i.Rb(1073742336,N.a,N.a,[]),i.Rb(1073742336,O.a,O.a,[[2,C.g],i.O]),i.Rb(1073742336,W.b,W.b,[]),i.Rb(1073742336,J.a,J.a,[]),i.Rb(1073742336,U.b,U.b,[]),i.Rb(1073742336,o.Tb,o.Tb,[]),i.Rb(1073742336,b,b,[]),i.Rb(256,f.n,"XSRF-TOKEN",[]),i.Rb(256,f.o,"X-XSRF-TOKEN",[]),i.Rb(256,"config",{},[]),i.Rb(256,T.m,void 0,[]),i.Rb(256,T.n,void 0,[]),i.Rb(256,"popperDefaults",{},[]),i.Rb(1024,l.i,function(){return[[{path:"",component:u}]]},[])])}),V=[[""]],z=i.Hb({encapsulation:0,styles:V,data:{}});function _(t){return i.dc(0,[i.Zb(402653184,1,{_container:0}),i.Zb(402653184,2,{gridCanvas:0}),i.Zb(402653184,3,{entityCombo:0}),i.Zb(402653184,4,{matchIdText:0}),i.Zb(402653184,5,{dataBuildingText:0}),i.Zb(402653184,6,{lostConnectionText:0}),i.Zb(402653184,7,{loadingImage:0}),i.Zb(402653184,8,{currencyLabel:0}),i.Zb(402653184,9,{currencyCode:0}),i.Zb(402653184,10,{ccyTolLabel:0}),i.Zb(402653184,11,{ccyTolValue:0}),i.Zb(402653184,12,{entityLabel:0}),i.Zb(402653184,13,{selectedEntityId:0}),i.Zb(402653184,14,{selectedEntityName:0}),i.Zb(402653184,15,{matchLabel:0}),i.Zb(402653184,16,{matchIndices:0}),i.Zb(402653184,17,{matchStatus:0}),i.Zb(402653184,18,{matchQuality:0}),i.Zb(402653184,19,{updateDate:0}),i.Zb(402653184,20,{updateUser:0}),i.Zb(402653184,21,{noteImage:0}),i.Zb(402653184,22,{alertImage:0}),i.Zb(402653184,23,{selectedEntity:0}),i.Zb(402653184,24,{posIntlvl:0}),i.Zb(402653184,25,{posExtlvl:0}),i.Zb(402653184,26,{internalLabel:0}),i.Zb(402653184,27,{externalLabel:0}),i.Zb(402653184,28,{usedBye:0}),i.Zb(402653184,29,{movement:0}),i.Zb(402653184,30,{doesnotexist:0}),i.Zb(402653184,31,{movementCannotBeUnlocked:0}),i.Zb(402653184,32,{diffLabel:0}),i.Zb(402653184,33,{diffValue:0}),i.Zb(402653184,34,{exportContainer:0}),i.Zb(402653184,35,{nextButton:0}),i.Zb(402653184,36,{prevButton:0}),i.Zb(402653184,37,{logButton:0}),i.Zb(402653184,38,{noteButton:0}),i.Zb(402653184,39,{mvmntButton:0}),i.Zb(402653184,40,{unMatchButton:0}),i.Zb(402653184,41,{suspendButton:0}),i.Zb(402653184,42,{reconButton:0}),i.Zb(402653184,43,{closeButton:0}),i.Zb(402653184,44,{confirmButton:0}),i.Zb(402653184,45,{addButton:0}),i.Zb(402653184,46,{removeButton:0}),i.Zb(402653184,47,{optionsButton:0}),i.Zb(402653184,48,{helpIcon:0}),i.Zb(402653184,49,{buttonsContainer:0}),(t()(),i.Jb(49,0,null,null,141,"SwtModule",[["height","100%"],["width","100%"]],null,[[null,"creationComplete"]],function(t,e,n){var i=!0,s=t.component;"creationComplete"===e&&(i=!1!==s.onLoad()&&i);return i},B.ad,B.hb)),i.Ib(50,4440064,null,0,o.yb,[i.r,o.i],{width:[0,"width"],height:[1,"height"]},{creationComplete:"creationComplete"}),(t()(),i.Jb(51,0,null,0,139,"VBox",[["height","100%"],["id","vBox1"],["paddingBottom","5"],["paddingLeft","5"],["paddingRight","5"],["paddingTop","5"],["width","100%"]],null,null,null,B.od,B.vb)),i.Ib(52,4440064,null,0,o.ec,[i.r,o.i,i.T],{id:[0,"id"],width:[1,"width"],height:[2,"height"],paddingTop:[3,"paddingTop"],paddingBottom:[4,"paddingBottom"],paddingLeft:[5,"paddingLeft"],paddingRight:[6,"paddingRight"]},null),(t()(),i.Jb(53,0,null,0,110,"SwtCanvas",[["height","80"],["minWidth","1340"],["width","100%"]],null,null,null,B.Nc,B.U)),i.Ib(54,4440064,null,0,o.db,[i.r,o.i],{width:[0,"width"],height:[1,"height"],minWidth:[2,"minWidth"]},null),(t()(),i.Jb(55,0,null,0,1,"SwtComboBox",[["dataLabel","entity"],["enabled","false"],["id","entityCombo"],["visible","false"]],null,[["window","mousewheel"]],function(t,e,n){var s=!0;"window:mousewheel"===e&&(s=!1!==i.Tb(t,56).mouseWeelEventHandler(n.target)&&s);return s},B.Pc,B.W)),i.Ib(56,4440064,[[3,4],["entityCombo",4]],0,o.gb,[i.r,o.i],{dataLabel:[0,"dataLabel"],id:[1,"id"],enabled:[2,"enabled"],visible:[3,"visible"]},null),(t()(),i.Jb(57,0,null,0,63,"Grid",[["height","100%"],["paddingLeft","5"],["verticalGap","2"],["width","80%"]],null,null,null,B.Cc,B.H)),i.Ib(58,4440064,null,0,o.z,[i.r,o.i],{verticalGap:[0,"verticalGap"],width:[1,"width"],height:[2,"height"],paddingLeft:[3,"paddingLeft"]},null),(t()(),i.Jb(59,0,null,0,13,"GridRow",[["height","30%"],["width","100%"]],null,null,null,B.Bc,B.J)),i.Ib(60,4440064,null,0,o.B,[i.r,o.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),i.Jb(61,0,null,0,3,"GridItem",[["width","70"]],null,null,null,B.Ac,B.I)),i.Ib(62,4440064,null,0,o.A,[i.r,o.i],{width:[0,"width"]},null),(t()(),i.Jb(63,0,null,0,1,"SwtLabel",[["fontWeight","bold"],["id","entityLabel"]],null,null,null,B.Yc,B.fb)),i.Ib(64,4440064,[[12,4],["entityLabel",4]],0,o.vb,[i.r,o.i],{id:[0,"id"],fontWeight:[1,"fontWeight"]},null),(t()(),i.Jb(65,0,null,0,3,"GridItem",[["width","130"]],null,null,null,B.Ac,B.I)),i.Ib(66,4440064,null,0,o.A,[i.r,o.i],{width:[0,"width"]},null),(t()(),i.Jb(67,0,null,0,1,"SwtLabel",[["fontWeight","normal"],["id","selectedEntityId"]],null,null,null,B.Yc,B.fb)),i.Ib(68,4440064,[[13,4],["selectedEntityId",4]],0,o.vb,[i.r,o.i],{id:[0,"id"],fontWeight:[1,"fontWeight"]},null),(t()(),i.Jb(69,0,null,0,3,"GridItem",[["width","20%"]],null,null,null,B.Ac,B.I)),i.Ib(70,4440064,null,0,o.A,[i.r,o.i],{width:[0,"width"]},null),(t()(),i.Jb(71,0,null,0,1,"SwtLabel",[["fontWeight","normal"],["id","selectedEntityName"]],null,null,null,B.Yc,B.fb)),i.Ib(72,4440064,[[14,4],["selectedEntityName",4]],0,o.vb,[i.r,o.i],{id:[0,"id"],fontWeight:[1,"fontWeight"]},null),(t()(),i.Jb(73,0,null,0,37,"GridRow",[["height","30%"],["width","100%"]],null,null,null,B.Bc,B.J)),i.Ib(74,4440064,null,0,o.B,[i.r,o.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),i.Jb(75,0,null,0,3,"GridItem",[["width","70"]],null,null,null,B.Ac,B.I)),i.Ib(76,4440064,null,0,o.A,[i.r,o.i],{width:[0,"width"]},null),(t()(),i.Jb(77,0,null,0,1,"SwtLabel",[["fontWeight","bold"],["id","matchLabel"]],null,null,null,B.Yc,B.fb)),i.Ib(78,4440064,[[15,4],["matchLabel",4]],0,o.vb,[i.r,o.i],{id:[0,"id"],fontWeight:[1,"fontWeight"]},null),(t()(),i.Jb(79,0,null,0,3,"GridItem",[["width","160"]],null,null,null,B.Ac,B.I)),i.Ib(80,4440064,null,0,o.A,[i.r,o.i],{width:[0,"width"]},null),(t()(),i.Jb(81,0,null,0,1,"SwtTextInput",[["height","21"],["id","matchIdText"],["maxChars","12"],["restrict","0-9"],["textAlign","right"],["width","150"]],null,[[null,"keydown"]],function(t,e,n){var i=!0,s=t.component;"keydown"===e&&(i=!1!==s.getMatchId(n)&&i);return i},B.kd,B.sb)),i.Ib(82,4440064,[[4,4],["matchIdText",4]],0,o.Rb,[i.r,o.i],{maxChars:[0,"maxChars"],restrict:[1,"restrict"],id:[2,"id"],textAlign:[3,"textAlign"],width:[4,"width"],height:[5,"height"]},null),(t()(),i.Jb(83,0,null,0,3,"GridItem",[["width","90"]],null,null,null,B.Ac,B.I)),i.Ib(84,4440064,null,0,o.A,[i.r,o.i],{width:[0,"width"]},null),(t()(),i.Jb(85,0,null,0,1,"SwtLabel",[["id","matchIndices"],["styleName","labelMatchGreen"]],null,null,null,B.Yc,B.fb)),i.Ib(86,4440064,[[16,4],["matchIndices",4]],0,o.vb,[i.r,o.i],{id:[0,"id"],styleName:[1,"styleName"]},null),(t()(),i.Jb(87,0,null,0,3,"GridItem",[["width","75"]],null,null,null,B.Ac,B.I)),i.Ib(88,4440064,null,0,o.A,[i.r,o.i],{width:[0,"width"]},null),(t()(),i.Jb(89,0,null,0,1,"SwtLabel",[["id","matchStatus"],["styleName","labelMatchGreen"]],null,null,null,B.Yc,B.fb)),i.Ib(90,4440064,[[17,4],["matchStatus",4]],0,o.vb,[i.r,o.i],{id:[0,"id"],styleName:[1,"styleName"]},null),(t()(),i.Jb(91,0,null,0,3,"GridItem",[["width","17"]],null,null,null,B.Ac,B.I)),i.Ib(92,4440064,null,0,o.A,[i.r,o.i],{width:[0,"width"]},null),(t()(),i.Jb(93,0,null,0,1,"SwtLabel",[["id","matchQuality"],["styleName","labelMatchGreen"]],null,null,null,B.Yc,B.fb)),i.Ib(94,4440064,[[18,4],["matchQuality",4]],0,o.vb,[i.r,o.i],{id:[0,"id"],styleName:[1,"styleName"]},null),(t()(),i.Jb(95,0,null,0,3,"GridItem",[["width","75"]],null,null,null,B.Ac,B.I)),i.Ib(96,4440064,null,0,o.A,[i.r,o.i],{width:[0,"width"]},null),(t()(),i.Jb(97,0,null,0,1,"SwtLabel",[["id","updateDate"],["styleName","labelMatchGreen"]],null,null,null,B.Yc,B.fb)),i.Ib(98,4440064,[[19,4],["updateDate",4]],0,o.vb,[i.r,o.i],{id:[0,"id"],styleName:[1,"styleName"]},null),(t()(),i.Jb(99,0,null,0,3,"GridItem",[["width","55"]],null,null,null,B.Ac,B.I)),i.Ib(100,4440064,null,0,o.A,[i.r,o.i],{width:[0,"width"]},null),(t()(),i.Jb(101,0,null,0,1,"SwtLabel",[["id","updateUser"],["styleName","labelMatchGreen"]],null,null,null,B.Yc,B.fb)),i.Ib(102,4440064,[[20,4],["updateUser",4]],0,o.vb,[i.r,o.i],{id:[0,"id"],styleName:[1,"styleName"]},null),(t()(),i.Jb(103,0,null,0,3,"GridItem",[["width","2%"]],null,null,null,B.Ac,B.I)),i.Ib(104,4440064,null,0,o.A,[i.r,o.i],{width:[0,"width"]},null),(t()(),i.Jb(105,0,null,0,1,"SwtImage",[["id","noteImage"]],null,null,null,B.Xc,B.eb)),i.Ib(106,4440064,[[21,4],["noteImage",4]],0,o.ub,[i.r],{id:[0,"id"]},null),(t()(),i.Jb(107,0,null,0,3,"GridItem",[["paddingTop","2"],["width","2%"]],null,null,null,B.Ac,B.I)),i.Ib(108,4440064,null,0,o.A,[i.r,o.i],{width:[0,"width"],paddingTop:[1,"paddingTop"]},null),(t()(),i.Jb(109,0,null,0,1,"SwtImage",[["id","alertImage"],["width","17"]],null,[[null,"click"]],function(t,e,n){var i=!0,s=t.component;"click"===e&&(i=!1!==s.imageClickFunction(n)&&i);return i},B.Xc,B.eb)),i.Ib(110,4440064,[[22,4],["alertImage",4]],0,o.ub,[i.r],{id:[0,"id"],width:[1,"width"]},{onClick_:"click"}),(t()(),i.Jb(111,0,null,0,9,"GridRow",[["height","30%"],["width","100%"]],null,null,null,B.Bc,B.J)),i.Ib(112,4440064,null,0,o.B,[i.r,o.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),i.Jb(113,0,null,0,3,"GridItem",[["width","70"]],null,null,null,B.Ac,B.I)),i.Ib(114,4440064,null,0,o.A,[i.r,o.i],{width:[0,"width"]},null),(t()(),i.Jb(115,0,null,0,1,"SwtLabel",[["fontWeight","bold"],["id","currencyLabel"],["width","60"]],null,null,null,B.Yc,B.fb)),i.Ib(116,4440064,[[8,4],["currencyLabel",4]],0,o.vb,[i.r,o.i],{id:[0,"id"],width:[1,"width"],fontWeight:[2,"fontWeight"]},null),(t()(),i.Jb(117,0,null,0,3,"GridItem",[["width","65%"]],null,null,null,B.Ac,B.I)),i.Ib(118,4440064,null,0,o.A,[i.r,o.i],{width:[0,"width"]},null),(t()(),i.Jb(119,0,null,0,1,"SwtLabel",[["fontWeight","normal"],["id","currencyCode"]],null,null,null,B.Yc,B.fb)),i.Ib(120,4440064,[[9,4],["currencyCode",4]],0,o.vb,[i.r,o.i],{id:[0,"id"],fontWeight:[1,"fontWeight"]},null),(t()(),i.Jb(121,0,null,0,13,"Grid",[["height","100%"],["paddingLeft","5"],["verticalGap","2"],["width","22%"]],null,null,null,B.Cc,B.H)),i.Ib(122,4440064,null,0,o.z,[i.r,o.i],{verticalGap:[0,"verticalGap"],width:[1,"width"],height:[2,"height"],paddingLeft:[3,"paddingLeft"]},null),(t()(),i.Jb(123,0,null,0,1,"GridRow",[["height","60%"],["width","100%"]],null,null,null,B.Bc,B.J)),i.Ib(124,4440064,null,0,o.B,[i.r,o.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),i.Jb(125,0,null,0,9,"GridRow",[["height","30%"],["width","100%"]],null,null,null,B.Bc,B.J)),i.Ib(126,4440064,null,0,o.B,[i.r,o.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),i.Jb(127,0,null,0,3,"GridItem",[["width","55%"]],null,null,null,B.Ac,B.I)),i.Ib(128,4440064,null,0,o.A,[i.r,o.i],{width:[0,"width"]},null),(t()(),i.Jb(129,0,null,0,1,"SwtLabel",[["fontWeight","bold"],["id","ccyTolLabel"]],null,null,null,B.Yc,B.fb)),i.Ib(130,4440064,[[10,4],["ccyTolLabel",4]],0,o.vb,[i.r,o.i],{id:[0,"id"],fontWeight:[1,"fontWeight"]},null),(t()(),i.Jb(131,0,null,0,3,"GridItem",[["width","30%"]],null,null,null,B.Ac,B.I)),i.Ib(132,4440064,null,0,o.A,[i.r,o.i],{width:[0,"width"]},null),(t()(),i.Jb(133,0,null,0,1,"SwtLabel",[["fontWeight","normal"],["id","ccyTolValue"]],null,null,null,B.Yc,B.fb)),i.Ib(134,4440064,[[11,4],["ccyTolValue",4]],0,o.vb,[i.r,o.i],{id:[0,"id"],fontWeight:[1,"fontWeight"]},null),(t()(),i.Jb(135,0,null,0,28,"fieldset",[["style","height:95%;width: 12%"]],null,null,null,null,null)),(t()(),i.Jb(136,0,null,null,1,"legend",[],null,null,null,null,null)),(t()(),i.bc(137,null,[" "," "])),(t()(),i.Jb(138,0,null,null,25,"Grid",[["height","100%"],["width","100%"]],null,null,null,B.Cc,B.H)),i.Ib(139,4440064,null,0,o.z,[i.r,o.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),i.Jb(140,0,null,0,11,"GridRow",[],null,null,null,B.Bc,B.J)),i.Ib(141,4440064,null,0,o.B,[i.r,o.i],null,null),(t()(),i.Jb(142,0,null,0,3,"GridItem",[],null,null,null,B.Ac,B.I)),i.Ib(143,4440064,null,0,o.A,[i.r,o.i],null,null),(t()(),i.Jb(144,0,null,0,1,"SwtLabel",[["fontWeight","bold"],["id","internalLabel"]],null,null,null,B.Yc,B.fb)),i.Ib(145,4440064,[[26,4],["internalLabel",4]],0,o.vb,[i.r,o.i],{id:[0,"id"],fontWeight:[1,"fontWeight"]},null),(t()(),i.Jb(146,0,null,0,1,"GridItem",[["paddingLeft","3"],["paddingTop","8"]],null,null,null,B.Ac,B.I)),i.Ib(147,4440064,null,0,o.A,[i.r,o.i],{paddingTop:[0,"paddingTop"],paddingLeft:[1,"paddingLeft"]},null),(t()(),i.Jb(148,0,null,0,3,"GridItem",[["paddingRight","3"]],null,null,null,B.Ac,B.I)),i.Ib(149,4440064,null,0,o.A,[i.r,o.i],{paddingRight:[0,"paddingRight"]},null),(t()(),i.Jb(150,0,null,0,1,"SwtLabel",[["id","posIntlvl"],["paddingLeft","20"]],null,null,null,B.Yc,B.fb)),i.Ib(151,4440064,[[24,4],["posIntlvl",4]],0,o.vb,[i.r,o.i],{id:[0,"id"],paddingLeft:[1,"paddingLeft"]},null),(t()(),i.Jb(152,0,null,0,11,"GridRow",[],null,null,null,B.Bc,B.J)),i.Ib(153,4440064,null,0,o.B,[i.r,o.i],null,null),(t()(),i.Jb(154,0,null,0,3,"GridItem",[],null,null,null,B.Ac,B.I)),i.Ib(155,4440064,null,0,o.A,[i.r,o.i],null,null),(t()(),i.Jb(156,0,null,0,1,"SwtLabel",[["fontWeight","bold"],["id","externalLabel"]],null,null,null,B.Yc,B.fb)),i.Ib(157,4440064,[[27,4],["externalLabel",4]],0,o.vb,[i.r,o.i],{id:[0,"id"],fontWeight:[1,"fontWeight"]},null),(t()(),i.Jb(158,0,null,0,1,"GridItem",[["paddingLeft","3"],["paddingTop","0"]],null,null,null,B.Ac,B.I)),i.Ib(159,4440064,null,0,o.A,[i.r,o.i],{paddingTop:[0,"paddingTop"],paddingLeft:[1,"paddingLeft"]},null),(t()(),i.Jb(160,0,null,0,3,"GridItem",[["paddingBottom","3"],["paddingRight","3"]],null,null,null,B.Ac,B.I)),i.Ib(161,4440064,null,0,o.A,[i.r,o.i],{paddingBottom:[0,"paddingBottom"],paddingRight:[1,"paddingRight"]},null),(t()(),i.Jb(162,0,null,0,1,"SwtLabel",[["id","posExtlvl"],["paddingLeft","20"]],null,null,null,B.Yc,B.fb)),i.Ib(163,4440064,[[25,4],["posExtlvl",4]],0,o.vb,[i.r,o.i],{id:[0,"id"],paddingLeft:[1,"paddingLeft"]},null),(t()(),i.Jb(164,0,null,0,1,"SwtCanvas",[["height","100%"],["id","gridCanvas"],["minHeight","100"],["minWidth","1340"],["width","100%"]],null,null,null,B.Nc,B.U)),i.Ib(165,4440064,[[2,4],["gridCanvas",4]],0,o.db,[i.r,o.i],{id:[0,"id"],width:[1,"width"],height:[2,"height"],minHeight:[3,"minHeight"],minWidth:[4,"minWidth"]},null),(t()(),i.Jb(166,0,null,0,24,"SwtCanvas",[["height","40"],["id","canvasButtons"],["marginBottom","0"],["marginTop","5"],["minWidth","1340"],["width","100%"]],null,null,null,B.Nc,B.U)),i.Ib(167,4440064,null,0,o.db,[i.r,o.i],{id:[0,"id"],width:[1,"width"],height:[2,"height"],minWidth:[3,"minWidth"],marginTop:[4,"marginTop"],marginBottom:[5,"marginBottom"]},null),(t()(),i.Jb(168,0,null,0,22,"HBox",[["width","100%"]],null,null,null,B.Dc,B.K)),i.Ib(169,4440064,null,0,o.C,[i.r,o.i],{width:[0,"width"]},null),(t()(),i.Jb(170,0,null,0,1,"HBox",[["horizontalGap","2"],["id","buttonsContainer"],["paddingLeft","5"],["width","100%"]],null,null,null,B.Dc,B.K)),i.Ib(171,4440064,[[49,4],["buttonsContainer",4]],0,o.C,[i.r,o.i],{id:[0,"id"],horizontalGap:[1,"horizontalGap"],width:[2,"width"],paddingLeft:[3,"paddingLeft"]},null),(t()(),i.Jb(172,0,null,0,18,"HBox",[["horizontalAlign","right"],["horizontalGap","3"]],null,null,null,B.Dc,B.K)),i.Ib(173,4440064,null,0,o.C,[i.r,o.i],{horizontalGap:[0,"horizontalGap"],horizontalAlign:[1,"horizontalAlign"]},null),(t()(),i.Jb(174,0,null,0,1,"SwtText",[["height","16"],["id","dataBuildingText"],["text","DATA BUILD IN PROGRESS"],["visible","false"],["width","155"]],null,null,null,B.ld,B.qb)),i.Ib(175,4440064,[[5,4],["dataBuildingText",4]],0,o.Pb,[i.r,o.i],{id:[0,"id"],width:[1,"width"],height:[2,"height"],visible:[3,"visible"],text:[4,"text"]},null),(t()(),i.Jb(176,0,null,0,1,"SwtText",[["id","lostConnectionText"],["text","CONNECTION ERROR"],["visible","false"]],null,[[null,"click"]],function(t,e,n){var i=!0,s=t.component;"click"===e&&(i=!1!==s.connError()&&i);return i},B.ld,B.qb)),i.Ib(177,4440064,[[6,4],["lostConnectionText",4]],0,o.Pb,[i.r,o.i],{id:[0,"id"],visible:[1,"visible"],text:[2,"text"]},{onClick_:"click"}),(t()(),i.Jb(178,0,null,0,5,"VBox",[["height","100%"],["marginRight","10"],["verticalGap","0"]],null,null,null,B.od,B.vb)),i.Ib(179,4440064,null,0,o.ec,[i.r,o.i,i.T],{verticalGap:[0,"verticalGap"],height:[1,"height"],marginRight:[2,"marginRight"]},null),(t()(),i.Jb(180,0,null,0,1,"SwtLabel",[["fontWeight","bold"],["id","diffLabel"],["marginTop","-3"],["width","70"]],null,null,null,B.Yc,B.fb)),i.Ib(181,4440064,[[32,4],["diffLabel",4]],0,o.vb,[i.r,o.i],{id:[0,"id"],width:[1,"width"],marginTop:[2,"marginTop"],fontWeight:[3,"fontWeight"]},null),(t()(),i.Jb(182,0,null,0,1,"SwtLabel",[["fontWeight","normal"],["id","diffValue"],["marginTop","-15"]],null,null,null,B.Yc,B.fb)),i.Ib(183,4440064,[[33,4],["diffValue",4]],0,o.vb,[i.r,o.i],{id:[0,"id"],marginTop:[1,"marginTop"],fontWeight:[2,"fontWeight"]},null),(t()(),i.Jb(184,0,null,0,2,"div",[],null,null,null,null,null)),(t()(),i.Jb(185,0,null,null,1,"DataExport",[["enabled","false"],["id","exportContainer"]],null,null,null,B.Sc,B.Z)),i.Ib(186,4440064,[[34,4],["exportContainer",4]],0,o.kb,[o.i,i.r],{id:[0,"id"],enabled:[1,"enabled"]},null),(t()(),i.Jb(187,0,null,0,1,"SwtLoadingImage",[["id","loadingImage"]],null,null,null,B.Zc,B.gb)),i.Ib(188,114688,[[7,4],["loadingImage",4]],0,o.xb,[i.r],null,null),(t()(),i.Jb(189,0,null,0,1,"SwtHelpButton",[["id","helpIcon"]],null,[[null,"click"]],function(t,e,n){var i=!0,s=t.component;"click"===e&&(i=!1!==s.helpHandler()&&i);return i},B.Wc,B.db)),i.Ib(190,4440064,[[48,4],["helpIcon",4]],0,o.rb,[i.r,o.i],{id:[0,"id"]},{onClick_:"click"})],function(t,e){t(e,50,0,"100%","100%");t(e,52,0,"vBox1","100%","100%","5","5","5","5");t(e,54,0,"100%","80","1340");t(e,56,0,"entity","entityCombo","false","false");t(e,58,0,"2","80%","100%","5");t(e,60,0,"100%","30%");t(e,62,0,"70");t(e,64,0,"entityLabel","bold");t(e,66,0,"130");t(e,68,0,"selectedEntityId","normal");t(e,70,0,"20%");t(e,72,0,"selectedEntityName","normal");t(e,74,0,"100%","30%");t(e,76,0,"70");t(e,78,0,"matchLabel","bold");t(e,80,0,"160");t(e,82,0,"12","0-9","matchIdText","right","150","21");t(e,84,0,"90");t(e,86,0,"matchIndices","labelMatchGreen");t(e,88,0,"75");t(e,90,0,"matchStatus","labelMatchGreen");t(e,92,0,"17");t(e,94,0,"matchQuality","labelMatchGreen");t(e,96,0,"75");t(e,98,0,"updateDate","labelMatchGreen");t(e,100,0,"55");t(e,102,0,"updateUser","labelMatchGreen");t(e,104,0,"2%");t(e,106,0,"noteImage");t(e,108,0,"2%","2");t(e,110,0,"alertImage","17");t(e,112,0,"100%","30%");t(e,114,0,"70");t(e,116,0,"currencyLabel","60","bold");t(e,118,0,"65%");t(e,120,0,"currencyCode","normal");t(e,122,0,"2","22%","100%","5");t(e,124,0,"100%","60%");t(e,126,0,"100%","30%");t(e,128,0,"55%");t(e,130,0,"ccyTolLabel","bold");t(e,132,0,"30%");t(e,134,0,"ccyTolValue","normal");t(e,139,0,"100%","100%"),t(e,141,0),t(e,143,0);t(e,145,0,"internalLabel","bold");t(e,147,0,"8","3");t(e,149,0,"3");t(e,151,0,"posIntlvl","20"),t(e,153,0),t(e,155,0);t(e,157,0,"externalLabel","bold");t(e,159,0,"0","3");t(e,161,0,"3","3");t(e,163,0,"posExtlvl","20");t(e,165,0,"gridCanvas","100%","100%","100","1340");t(e,167,0,"canvasButtons","100%","40","1340","5","0");t(e,169,0,"100%");t(e,171,0,"buttonsContainer","2","100%","5");t(e,173,0,"3","right");t(e,175,0,"dataBuildingText","155","16","false","DATA BUILD IN PROGRESS");t(e,177,0,"lostConnectionText","false","CONNECTION ERROR");t(e,179,0,"0","100%","10");t(e,181,0,"diffLabel","70","-3","bold");t(e,183,0,"diffValue","-15","normal");t(e,186,0,"exportContainer","false"),t(e,188,0);t(e,190,0,"helpIcon")},function(t,e){t(e,137,0,e.component.posTotal)})}function Z(t){return i.dc(0,[(t()(),i.Jb(0,0,null,null,1,"app-movement-match-summary",[],null,[["window","mmsd.movementNotes"]],function(t,e,n){var s=!0;"window:mmsd.movementNotes"===e&&(s=!1!==i.Tb(t,1).onPaymentSuccess(n)&&s);return s},_,z)),i.Ib(1,4440064,null,0,u,[o.i,i.r],null,null)],function(t,e){t(e,1,0)},null)}var Q=i.Fb("app-movement-match-summary",u,Z,{maxChars:"maxChars",restrict:"restrict",id:"id",dropShadowEnabled:"dropShadowEnabled",cornerRadius:"cornerRadius",borderThickness:"borderThickness",borderStyle:"borderStyle",borderColor:"borderColor",backGroundColor:"backGroundColor",right:"right",left:"left",bottom:"bottom",top:"top",horizontalGap:"horizontalGap",verticalGap:"verticalGap",textAlign:"textAlign",toolTip:"toolTip",toolTipPreviousValue:"toolTipPreviousValue",textDictionaryId:"tooltipDictionaryId",name:"name",styleName:"styleName",horizontalAlign:"horizontalAlign",verticalAlign:"verticalAlign",width:"width",showScrollBar:"showScrollBar",height:"height",minHeight:"minHeight",minWidth:"minWidth",maxHeight:"maxHeight",maxWidth:"maxWidth",includeInLayout:"includeInLayout",visible:"visible",enabled:"enabled",paddingTop:"paddingTop",paddingBottom:"paddingBottom",paddingLeft:"paddingLeft",paddingRight:"paddingRight",marginTop:"marginTop",marginBottom:"marginBottom",marginLeft:"marginLeft",marginRight:"marginRight",contextMenu:"contextMenu"},{onClick_:"click",dbClick_:"dbClick",doubleClick_:"doubleClick",itemDoubleClick_:"itemDoubleClick",onKeyDown_:"keyDown",onKeyUp_:"keyUp",mouseUp_:"mouseUp",mouseOver_:"mouseOver",mouseDown_:"mouseDown",mouseEnter_:"mouseEnter",mouseLeave_:"mouseLeave",mouseOut_:"mouseOut",mouseIn_:"mouseIn",mouseMove_:"mouseMove",focus_:"focus",focusIn_:"focusIn",onFocusOut_:"focusOut",keyFocusChange_:"keyFocusChange",change_:"change",onSpyChange:"onSpyChange",onSpyNoChange:"onSpyNoChange",scroll_:"scroll",creationComplete:"creationComplete",preinitialize:"preinitialize"},[])}}]);