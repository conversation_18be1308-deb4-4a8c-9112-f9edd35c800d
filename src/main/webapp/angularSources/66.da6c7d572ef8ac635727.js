(window.webpackJsonp=window.webpackJsonp||[]).push([[66],{A9kd:function(e,t,i){"use strict";i.r(t);var s=i("CcnG"),a=i("mrSG"),r=i("ZYCi"),n=i("447K"),l=function(e){function t(t,i){var s=e.call(this,i,t)||this;return s.commonService=t,s.element=i,s.jsonReader=new n.L,s.summaryJSONReader=new n.L,s.inputData=new n.G(s.commonService),s.summaryData=new n.G(s.commonService),s.sendData=new n.G(s.commonService),s.requestParams=[],s.invalidComms="",s.baseURL=n.Wb.getBaseURL(),s.actionPath="",s.actionMethod="",s.TREE="tree",s.SCENARIO="scenario",s.comboOpen=!1,s.comboChange=!1,s.screenVersion=new n.V(s.commonService),s.screenName=n.Wb.getPredictMessage("scenarioSummary.title",null),s.versionNumber="1.0.0001",s.releaseDate="16 Apr 2020",s.currentUser="",s.menuAccessId="",s.callerMethod="",s.flashScenarios="",s.popupScenarios="",s.emailScenarios=!1,s.lastSelectedId="",s.firstLoad=!0,s.tabDataCategory=[],s.scenarioTitle="",s.fisrtTablastSelectedId=null,s.secondTablastSelectedId=null,s.firstTabTreeOpenedItems=[],s.firstTabTreeClosedItems=[],s.secondTabTreeOpenedItems=[],s.secondTabTreeClosedItems=[],s.previousSelectedTabIndex=-1,s.logger=new n.R("Scenario Summary",s.commonService.httpclient),s.swtAlert=new n.bb(t),window.Main=s,s}return a.d(t,e),t.prototype.ngOnInit=function(){instanceElement=this,this.summary.summaryGrid.clientSideSort=!1,this.summary.summaryGrid.clientSideFilter=!1,this.closeButton.toolTip=n.Wb.getPredictMessage("tooltip.close",null),this.closeButton.label=n.Wb.getPredictMessage("button.genericdisplaymonitor.close",null),this.refreshButton.toolTip=n.Wb.getPredictMessage("tooltip.refreshWindow",null),this.refreshButton.label=n.Wb.getPredictMessage("button.genericdisplaymonitor.refresh",null),this.lblCombo.text=n.Wb.getPredictMessage("scenarioSummary.Entity",null),this.lastRefTimeLabel.text=n.Wb.getPredictMessage("screen.lastRefresh",null),this.entityCombo.toolTip=n.Wb.getPredictMessage("tooltip.selectEntityid",null),this.alertLabel.text=n.Wb.getPredictMessage("scenarioSummary.alertableScen",null),this.zeroLabel.text=n.Wb.getPredictMessage("scenarioSummary.zeroTotals",null),this.currLabel.text=n.Wb.getPredictMessage("scenarioSummary.applyCcy",null),this.lostConnectionText.text=n.Wb.getPredictMessage("screen.connectionError",null)},t.prototype.ngOnDestroy=function(){instanceElement=null},t.prototype.onLoad=function(){var e=this;try{this.logger.info("method [onLoad] - START "),this.initializeMenus(),this.callerMethod=n.x.call("eval","callerMethod"),this.currentUser=n.x.call("eval","currentUser"),this.menuAccessId=n.x.call("eval","menuAccessId"),this.summary.tree.ITEM_CLICK.subscribe(function(t){e.summaryTreeEventHandler(t)}),this.summary.summaryGrid.ITEM_CLICK.subscribe(function(t){e.cellClickEventHandler(t)}),this.summary.summaryGrid.columnWidthChanged.subscribe(function(t){e.updateWidths(t)}),n.E.subscribe(function(t){var i=""+e.summary.divBox.widthLeft;e.currDividerPosition=Number(i.substr(0,i.length-1)),e.updateWidths(t)}),this.summary.summaryGrid.onFilterChanged=this.doUpdateSortFilter.bind(this),this.summary.summaryGrid.onSortChanged=this.doUpdateSortFilter.bind(this),this.requestParams=[],this.actionPath="scenarioSummary.do?",this.actionMethod="method=summaryScreenInfo",this.inputData.cbStart=this.startOfComms.bind(this),this.inputData.cbStop=this.endOfComms.bind(this),this.inputData.cbResult=function(t){e.inputDataResult(t)},this.inputData.cbFault=this.inputDataFault.bind(this),this.inputData.encodeURL=!1,this.requestParams.callerMethod=this.callerMethod,this.inputData.url=this.baseURL+this.actionPath+this.actionMethod,this.inputData.send(this.requestParams),this.logger.info("method [onLoad] - END ")}catch(t){this.logger.error("GenericDisplay - method [onLoad] - error ",t)}},t.prototype.updateDividerPosition=function(){var e=this.summary.mainHGroup.width,t=this.summary.treeContainer.width;this.currDividerPosition=e-t},t.prototype.comboFocusOutHandler=function(e){0===n.Z.trim(this.entityCombo.selectedItem.text).length&&this.entityCombo.setComboData(this.jsonReader.getSelects())},t.prototype.updateWidths=function(e){for(var t=this,i=[],s=this.summary.summaryGrid.gridObj.getColumns().slice(0),a=s.length,r=0;r<a-1;r++)"expand"==s[r].id?s[r].width=5:null!=s[r].field&&i.push(s[r].field+"="+s[r].width);i.push("divider="+this.currDividerPosition),this.requestParams=[],this.sendData.encodeURL=!1,this.requestParams.width=i.join(","),this.actionPath="scenarioSummary.do?",this.actionMethod="method=saveColumnWidth&",this.sendData.cbStart=this.startOfComms.bind(this),this.sendData.cbStop=this.endOfComms.bind(this),this.sendData.cbResult=function(e){t.inputDataResultColumnsChange(e)},this.sendData.url=this.baseURL+this.actionPath+this.actionMethod,this.sendData.send(this.requestParams)},t.prototype.inputDataResultColumnsChange=function(e){if(this.inputData.isBusy())this.inputData.cbStop();else{this.lastReceivedWidthJSON=e;var t=new n.L;t.setInputJSON(this.lastReceivedWidthJSON),"Column width saved ok"!==t.getRequestReplyMessage()&&this.swtAlert.error(n.Wb.getPredictMessage("error.contactAdmin",null)+"\n"+t.getRequestReplyMessage())}},t.prototype.cellClickEventHandler=function(e){var t=e.target.field,i=this.summary.setClickable(e.target.data,null,null),s=e.target.data.slickgrid_rowcontent[t]?e.target.data.slickgrid_rowcontent[t].content:null;if(i&&s>0){var a=e.target.data.slickgrid_rowcontent.entity.content,r=e.target.data.slickgrid_rowcontent.ccy.content,l=e.target.data.slickgrid_rowcontent[t].content,o=this.summary.facilityId,h=this.summary.facilityName,u=this.summary.useGeneric,d=this.summary.scenarioTitle,c=!0===this.currBox.selected?"Y":"N",m=this.summary.selectedscenario;n.x.call("openFacility",d,u,o,h,m,a,r,c,l)}},t.prototype.loadSummary=function(){var e=this;this.summaryData.cbStart=this.startOfComms.bind(this),this.summaryData.cbStop=this.endOfComms.bind(this),this.summaryData.cbResult=function(t){e.summaryDataResult(t)},this.summaryData.cbFault=this.inputDataFault.bind(this),this.summaryData.encodeURL=!1,this.requestParams.entityId=this.entityCombo.selectedItem.content,this.firstLoad?this.requestParams.firstLoad="true":this.requestParams.firstLoad="false";var t=n.x.call("eval","document.hasFocus()");this.requestParams.hasFocus=t,this.requestParams.selectedTab=this.tabCategoryList.getSelectedTab().id,-1!==this.tabCategoryList.selectedIndex&&(this.requestParams.selectedCategory=this.tabDataCategory[this.tabCategoryList.selectedIndex].tabName),this.actionPath="scenarioSummary.do?",this.actionMethod="method=summaryData",this.summaryData.encodeURL=!1,this.summaryData.url=this.baseURL+this.actionPath+this.actionMethod,this.summaryData.send(this.requestParams)},t.prototype.inputDataResult=function(e){if(this.inputData.isBusy())this.inputData.cbStop();else{if(this.lastRecievedJSON=e,this.jsonReader.setInputJSON(this.lastRecievedJSON),this.lostConnectionText.visible=!1,""!==this.jsonReader.getScreenAttributes().lastRefTime){var t=this.jsonReader.getScreenAttributes().lastRefTime;this.lastRefTime.text=t.replace(/\\u0028/g,"(").replace(/\\u0029/g,")"),this.lastRefTime.visible=!0}if(this.jsonReader.getRequestReplyStatus()){if(this.lastRecievedJSON!==this.prevRecievedJSON){if(this.entityCombo.setComboData(this.jsonReader.getSelects()),this.selectedEntity.text=this.entityCombo.selectedItem.value,this.currBox.selected="Y"===this.jsonReader.getScreenAttributes().currencythreshold,this.zeroBox.selected=Boolean(this.jsonReader.getScreenAttributes().hidezerocounts),this.alertBox.selected=Boolean(this.jsonReader.getScreenAttributes().alertablescenarios),this.popupScenarios="true"===this.jsonReader.getScreenAttributes().popupScenarios?"true":"false",this.flashScenarios="true"===this.jsonReader.getScreenAttributes().flashScenarios?"true":"false",this.emailScenarios="true"===this.jsonReader.getScreenAttributes().emailScenarios,this.firstLoad&&(this.tabDataCategory=[],0===this.tabCategoryList.getTabChildren().length&&this.lastRecievedJSON.scenarioSummaryScreen.tabsCategory.displaytab))for(var i=0;i<2;i++)this.tabDataCategory[i]=this.tabCategoryList.addChild(n.Xb),this.tabDataCategory[i].id=this.lastRecievedJSON.scenarioSummaryScreen.tabsCategory.displaytab[i].tabId,this.tabDataCategory[i].label=this.lastRecievedJSON.scenarioSummaryScreen.tabsCategory.displaytab[i].tabName,this.tabDataCategory[i].tabName=this.lastRecievedJSON.scenarioSummaryScreen.tabsCategory.displaytab[i].tabName,this.tabDataCategory[i].count=this.lastRecievedJSON.scenarioSummaryScreen.tabsCategory.displaytab[i].count;var s=Number(this.lastRecievedJSON.scenarioSummaryScreen.tabsCategory.selectedIndex.index);this.tabCategoryList.selectedIndex=s}}else n.Wb.getPredictMessage("error.contactAdmin",null),this.swtAlert.error(n.Wb.getPredictMessage("error.contactAdmin",null)+"\n"+this.jsonReader.getRequestReplyMessage()+"\n"+this.jsonReader.getRequestReplyLocation());this.prevRecievedJSON=this.lastRecievedJSON,this.loadSummary()}this.previousSelectedTabIndex=this.tabCategoryList.selectedIndex},t.prototype.summaryDataResult=function(e){this.summaryData.isBusy()?this.summaryData.cbStop():(this.lastRecievedSummaryJSON=e,this.summaryJSONReader.setInputJSON(this.lastRecievedSummaryJSON),this.summaryJSONReader.getRequestReplyStatus()?this.lastRecievedSummaryJSON!==this.prevRecievedSummaryJSON&&(""!==this.summaryJSONReader.getScreenAttributes().scenarioTotalForTab1&&(this.tabCategoryList.getChildAt(0).label=this.tabDataCategory[0].tabName+"("+this.summaryJSONReader.getScreenAttributes().scenarioTotalForTab1+")"),""!==this.summaryJSONReader.getScreenAttributes().scenarioTotalForTab2&&(this.tabCategoryList.getChildAt(1).label=this.tabDataCategory[1].tabName+"("+this.summaryJSONReader.getScreenAttributes().scenarioTotalForTab2+")"),0===this.tabCategoryList.selectedIndex?(this.lastSelectedId=this.fisrtTablastSelectedId,this.summary.treeOpenedItems=this.firstTabTreeOpenedItems.concat(),this.summary.treeClosedItems=this.firstTabTreeClosedItems.concat()):(this.lastSelectedId=this.secondTablastSelectedId,this.summary.treeOpenedItems=this.secondTabTreeOpenedItems.concat(),this.summary.treeClosedItems=this.secondTabTreeClosedItems.concat()),0===this.summary.treeOpenedItems.length&&(this.summary.tree.openItems=[]),0===this.summary.treeClosedItems.length&&(this.summary.tree.closeItems=[]),this.summary.setBaseURL(this.baseURL),this.summary.setActionPath(this.actionPath),this.summary.dataProvider(this.lastRecievedSummaryJSON),this.firstLoad?(this.summary.tree.expandAll(n.o.LEVEL_1_STR),this.summary.setFirstSubNode(),this.firstLoad=!1):(this.summary.openTreeItems(),this.summary.tree.selectNodeByAttribute("id",this.lastSelectedId)),!1===this.mainGroup.visible&&(this.mainGroup.visible=!0)):this.swtAlert.error(n.Wb.getPredictMessage("error.contactAdmin",null)+"\n"+this.summaryJSONReader.getRequestReplyMessage()+"\n"+this.summaryJSONReader.getRequestReplyLocation()),this.prevRecievedSummaryJSON=this.lastRecievedSummaryJSON),this.refreshTreeItemRender(),this.previousSelectedTabIndex=this.tabCategoryList.selectedIndex},t.prototype.refreshTreeItemRender=function(){},t.prototype.summaryTreeEventHandler=function(e){this.summary.summaryGrid.resetFilter(),!0!==e.data.isBranch?this.updateData("tree"):this.summary.resetGridData()},t.prototype.getfilteredGridColumns=function(){try{var e="",t=[];if(""===this.summary.summaryGrid.filteredGridColumns)return e="";for(var i=this.summary.summaryGrid.getFilterColumns(),s=this.summary.summaryGrid.filteredGridColumns,a=0;a<i.length;a++)t[a]=i[a].field;if(""!=s){var r=s.split("|");for(a=0;a<t.length-1;a++)e=e+r[a]+"|"}else e=this.summary.summaryGrid.getFilteredGridColumns();return e.slice(4,e.length)}catch(n){}},t.prototype.doUpdateSortFilter=function(){var e=null,t=null,i=this.zeroBox.selected.toString(),s=this.currBox.selected,a=this.alertBox.selected.toString();try{this.summary.summaryGrid.selectedItem=null,t=this.getfilteredGridColumns(),e=this.summary.getSortedGridColumn(),this.requestParams=[],this.firstLoad=!1,this.requestParams.selectedFilter=t,this.requestParams.selectedSort=e,this.requestParams.callerMethod=this.callerMethod,this.requestParams.entityId=this.entityCombo.selectedItem.content,this.requestParams.userId=this.currentUser,this.requestParams.menuAccessId=this.menuAccessId,this.requestParams.zeroBalances=i,this.requestParams.currThreshold=1==s?"Y":"N",this.requestParams.selectedScenario=this.summary.getSelectedItemID(),this.requestParams.isScenarioAlertable=this.summary.getIsScenarioAlertable(),this.requestParams.dividerPosition=this.summary.getDividerPosition(),this.requestParams.alertScenarios=a,this.requestParams.flashScenarios=this.flashScenarios,this.requestParams.popupScenarios=this.popupScenarios,this.requestParams.emailScenarios=this.emailScenarios.toString(),this.summary.saveTreeOpenState(),0===this.previousSelectedTabIndex?(this.firstTabTreeOpenedItems=this.summary.treeOpenedItems.concat(),this.firstTabTreeClosedItems=this.summary.treeClosedItems.concat()):(this.secondTabTreeOpenedItems=this.summary.treeOpenedItems.concat(),this.secondTabTreeClosedItems=this.summary.treeClosedItems.concat()),this.loadSummary()}catch(r){}},t.prototype.updateData=function(e){var t=this.zeroBox.selected.toString(),i=this.currBox.selected,s=this.alertBox.selected.toString();this.requestParams=[],this.requestParams.callerMethod=this.callerMethod,this.requestParams.entityId=this.entityCombo.selectedItem.content,this.requestParams.userId=this.currentUser,this.requestParams.menuAccessId=this.menuAccessId,this.requestParams.zeroBalances=t,this.requestParams.currThreshold=1==i?"Y":"N",this.requestParams.selectedScenario=this.summary.getSelectedItemID(),this.requestParams.isScenarioAlertable=this.summary.getIsScenarioAlertable(),this.requestParams.dividerPosition=this.summary.getDividerPosition(),this.requestParams.selectedSort=this.summary.getSortedGridColumn(),this.requestParams.alertScenarios=s,this.requestParams.flashScenarios=this.flashScenarios,this.requestParams.popupScenarios=this.popupScenarios,this.requestParams.emailScenarios=this.emailScenarios.toString(),this.requestParams.selectedTab=this.tabCategoryList.getSelectedTab().id,-1!==this.tabCategoryList.selectedIndex&&(this.requestParams.selectedCategory=this.tabDataCategory[this.tabCategoryList.selectedIndex].tabName),this.summary.tree.selectedItem?0===this.previousSelectedTabIndex?this.fisrtTablastSelectedId=this.summary.tree.selectedItem.id:this.secondTablastSelectedId=this.summary.tree.selectedItem.id:0===this.previousSelectedTabIndex?this.fisrtTablastSelectedId=null:this.secondTablastSelectedId=null,this.summary.tree.selectedIndex=-1,this.summary.saveTreeOpenState(),0===this.previousSelectedTabIndex?(this.firstTabTreeOpenedItems=this.summary.treeOpenedItems.concat(),this.firstTabTreeClosedItems=this.summary.treeClosedItems.concat()):(this.secondTabTreeOpenedItems=this.summary.treeOpenedItems.concat(),this.secondTabTreeClosedItems=this.summary.treeClosedItems.concat()),e===this.TREE?this.loadSummary():e===this.SCENARIO&&this.inputData.send(this.requestParams),this.previousSelectedTabIndex=this.tabCategoryList.selectedIndex},t.prototype.openedCombo=function(e){this.comboOpen=!0,this.inputData.isBusy()&&(this.inputData.cancel(),e.currentTarget.interruptComms=!0)},t.prototype.closedCombo=function(e){this.comboOpen=!1,null!==e.triggerEvent&&"mouseDownOutside"===e.triggerEvent.type&&e.currentTarget.interruptComms&&(e.currentTarget.interruptComms=!1,this.updateData("scenario"))},t.prototype.entityComboChange=function(e){this.comboChange=!0,this.updateData("scenario")},t.prototype.startOfComms=function(){this.loadingImage.setVisible(!0),this.disableInterface()},t.prototype.endOfComms=function(){this.loadingImage.setVisible(!1),this.enableInterface()},t.prototype.inputDataFault=function(e){this.lostConnectionText.visible=!0,this.invalidComms=e.fault.faultString+"\n"+e.fault.faultCode+"\n"+e.fault.faultDetail,this.swtAlert.error(this.invalidComms)},t.prototype.disableInterface=function(){this.refreshButton.enabled=!1,this.refreshButton.buttonMode=!1,this.currBox.enabled=!1,this.zeroBox.enabled=!1,this.alertBox.enabled=!1,this.summary.disableTree(),this.summary.tree.removeEventListener("treeItemClick",this.summaryTreeEventHandler,!1)},t.prototype.enableInterface=function(){this.refreshButton.enabled=!0,this.refreshButton.buttonMode=!0,this.currBox.enabled=!0,this.zeroBox.enabled=!0,this.alertBox.enabled=!0,this.summary.enableTree()},t.prototype.initializeMenus=function(){this.screenVersion.loadScreenVersion(this,this.screenName,this.versionNumber,this.releaseDate);var e=new n.n("Show screen details JSON");e.MenuItemSelect=this.showXMLSelect.bind(this),this.screenVersion.svContextMenu.customItems.push(e);var t=new n.n("Show summary details JSON");t.MenuItemSelect=this.showSummaryXMLSelect.bind(this),this.screenVersion.svContextMenu.customItems.push(t),this.contextMenu=this.screenVersion.svContextMenu},t.prototype.showXMLSelect=function(e){null!==this.lastRecievedJSON?(this.showXMLPopup=n.Eb.createPopUp(this,n.M,{jsonData:this.lastRecievedJSON}),this.showXMLPopup.width="700",this.showXMLPopup.title="Summary Details JSON",this.showXMLPopup.height="350",this.showXMLPopup.enableResize=!1,this.showXMLPopup.showControls=!0,this.showXMLPopup.display()):this.swtAlert.warning(n.Wb.getPredictMessage("alert.scenarioSummary.noData",null),n.Wb.getPredictMessage("alert.scenarioSummary.noData.title",null))},t.prototype.showSummaryXMLSelect=function(e){null!==this.lastRecievedSummaryJSON?(this.showJSON=n.Eb.createPopUp(this,n.M,{jsonData:this.lastRecievedSummaryJSON}),this.showXMLPopup.width="700",this.showXMLPopup.title="Summary Details JSON",this.showXMLPopup.height="350",this.showXMLPopup.enableResize=!1,this.showXMLPopup.showControls=!0,this.showXMLPopup.display()):this.swtAlert.show(n.Wb.getPredictMessage("alert.scenarioSummary.noData",null),n.Wb.getPredictMessage("alert.scenarioSummary.noData.title",null))},t.prototype.closeHandler=function(e){n.x.call("close")},t.prototype.refreshFromJsp=function(){this.updateData("scenario")},t.prototype.tabBarLabelCategory=function(e){return e.label},t.prototype.doHelp=function(){try{n.x.call("help")}catch(e){n.Wb.logError(e,"Predict","ScenarioSummary","doHelp",0)}},t}(n.yb),o=[{path:"",component:l}],h=(r.l.forChild(o),function(){return function(){}}()),u=i("pMnS"),d=i("RChO"),c=i("t6HQ"),m=i("WFGK"),b=i("5FqG"),g=i("Ip0R"),p=i("gIcY"),y=i("t/Na"),S=i("sE5F"),f=i("OzfB"),C=i("T7CS"),R=i("S7LP"),w=i("6aHO"),I=i("WzUx"),v=i("A7o+"),T=i("zCE2"),x=i("Jg5P"),L=i("3R0m"),P=i("hhbb"),D=i("5rxC"),O=i("Fzqc"),B=i("21Lb"),N=i("hUWP"),J=i("3pJQ"),M=i("V9q+"),q=i("VDKW"),A=i("kXfT"),W=i("BGbe");i.d(t,"ScenarioSummaryModuleNgFactory",function(){return k}),i.d(t,"RenderType_ScenarioSummary",function(){return E}),i.d(t,"View_ScenarioSummary_0",function(){return z}),i.d(t,"View_ScenarioSummary_Host_0",function(){return _}),i.d(t,"ScenarioSummaryNgFactory",function(){return F});var k=s.Gb(h,[],function(e){return s.Qb([s.Rb(512,s.n,s.vb,[[8,[u.a,d.a,c.a,m.a,b.Cb,b.Pb,b.r,b.rc,b.s,b.Ab,b.Bb,b.Db,b.qd,b.Hb,b.k,b.Ib,b.Nb,b.Ub,b.yb,b.Jb,b.v,b.A,b.e,b.c,b.g,b.d,b.Kb,b.f,b.ec,b.Wb,b.bc,b.ac,b.sc,b.fc,b.lc,b.jc,b.Eb,b.Fb,b.mc,b.Lb,b.nc,b.Mb,b.dc,b.Rb,b.b,b.ic,b.Yb,b.Sb,b.kc,b.y,b.Qb,b.cc,b.hc,b.pc,b.oc,b.xb,b.p,b.q,b.o,b.h,b.j,b.w,b.Zb,b.i,b.m,b.Vb,b.Ob,b.Gb,b.Xb,b.t,b.tc,b.zb,b.n,b.qc,b.a,b.z,b.rd,b.sd,b.x,b.td,b.gc,b.l,b.u,b.ud,b.Tb,F]],[3,s.n],s.J]),s.Rb(4608,g.m,g.l,[s.F,[2,g.u]]),s.Rb(4608,p.c,p.c,[]),s.Rb(4608,p.p,p.p,[]),s.Rb(4608,y.j,y.p,[g.c,s.O,y.n]),s.Rb(4608,y.q,y.q,[y.j,y.o]),s.Rb(5120,y.a,function(e){return[e,new n.tb]},[y.q]),s.Rb(4608,y.m,y.m,[]),s.Rb(6144,y.k,null,[y.m]),s.Rb(4608,y.i,y.i,[y.k]),s.Rb(6144,y.b,null,[y.i]),s.Rb(4608,y.f,y.l,[y.b,s.B]),s.Rb(4608,y.c,y.c,[y.f]),s.Rb(4608,S.c,S.c,[]),s.Rb(4608,S.g,S.b,[]),s.Rb(5120,S.i,S.j,[]),s.Rb(4608,S.h,S.h,[S.c,S.g,S.i]),s.Rb(4608,S.f,S.a,[]),s.Rb(5120,S.d,S.k,[S.h,S.f]),s.Rb(5120,s.b,function(e,t){return[f.j(e,t)]},[g.c,s.O]),s.Rb(4608,C.a,C.a,[]),s.Rb(4608,R.a,R.a,[]),s.Rb(4608,w.a,w.a,[s.n,s.L,s.B,R.a,s.g]),s.Rb(4608,I.c,I.c,[s.n,s.g,s.B]),s.Rb(4608,I.e,I.e,[I.c]),s.Rb(4608,v.l,v.l,[]),s.Rb(4608,v.h,v.g,[]),s.Rb(4608,v.c,v.f,[]),s.Rb(4608,v.j,v.d,[]),s.Rb(4608,v.b,v.a,[]),s.Rb(4608,v.k,v.k,[v.l,v.h,v.c,v.j,v.b,v.m,v.n]),s.Rb(4608,I.i,I.i,[[2,v.k]]),s.Rb(4608,I.r,I.r,[I.L,[2,v.k],I.i]),s.Rb(4608,I.t,I.t,[]),s.Rb(4608,I.w,I.w,[]),s.Rb(1073742336,r.l,r.l,[[2,r.r],[2,r.k]]),s.Rb(1073742336,g.b,g.b,[]),s.Rb(1073742336,p.n,p.n,[]),s.Rb(1073742336,p.l,p.l,[]),s.Rb(1073742336,T.a,T.a,[]),s.Rb(1073742336,x.a,x.a,[]),s.Rb(1073742336,p.e,p.e,[]),s.Rb(1073742336,L.a,L.a,[]),s.Rb(1073742336,v.i,v.i,[]),s.Rb(1073742336,I.b,I.b,[]),s.Rb(1073742336,y.e,y.e,[]),s.Rb(1073742336,y.d,y.d,[]),s.Rb(1073742336,S.e,S.e,[]),s.Rb(1073742336,P.b,P.b,[]),s.Rb(1073742336,D.b,D.b,[]),s.Rb(1073742336,f.c,f.c,[]),s.Rb(1073742336,O.a,O.a,[]),s.Rb(1073742336,B.d,B.d,[]),s.Rb(1073742336,N.c,N.c,[]),s.Rb(1073742336,J.a,J.a,[]),s.Rb(1073742336,M.a,M.a,[[2,f.g],s.O]),s.Rb(1073742336,q.b,q.b,[]),s.Rb(1073742336,A.a,A.a,[]),s.Rb(1073742336,W.b,W.b,[]),s.Rb(1073742336,n.Tb,n.Tb,[]),s.Rb(1073742336,h,h,[]),s.Rb(256,y.n,"XSRF-TOKEN",[]),s.Rb(256,y.o,"X-XSRF-TOKEN",[]),s.Rb(256,"config",{},[]),s.Rb(256,v.m,void 0,[]),s.Rb(256,v.n,void 0,[]),s.Rb(256,"popperDefaults",{},[]),s.Rb(1024,r.i,function(){return[[{path:"",component:l}]]},[])])}),G=[[""]],E=s.Hb({encapsulation:0,styles:G,data:{}});function z(e){return s.dc(0,[s.Zb(402653184,1,{_container:0}),s.Zb(402653184,2,{refreshButton:0}),s.Zb(402653184,3,{closeButton:0}),s.Zb(402653184,4,{lblCombo:0}),s.Zb(402653184,5,{selectedEntity:0}),s.Zb(402653184,6,{currLabel:0}),s.Zb(402653184,7,{zeroLabel:0}),s.Zb(402653184,8,{alertLabel:0}),s.Zb(402653184,9,{entityCombo:0}),s.Zb(402653184,10,{currBox:0}),s.Zb(402653184,11,{zeroBox:0}),s.Zb(402653184,12,{alertBox:0}),s.Zb(402653184,13,{loadingImage:0}),s.Zb(402653184,14,{exportContainer:0}),s.Zb(402653184,15,{lostConnectionText:0}),s.Zb(402653184,16,{lastRefTime:0}),s.Zb(402653184,17,{lastRefTimeLabel:0}),s.Zb(402653184,18,{mainGroup:0}),s.Zb(402653184,19,{tabCategoryList:0}),s.Zb(402653184,20,{displayContainerPredict:0}),s.Zb(402653184,21,{displayContainerPCM:0}),s.Zb(402653184,22,{summary:0}),(e()(),s.Jb(22,0,null,null,68,"SwtModule",[["height","100%"],["width","100%"]],null,[[null,"creationComplete"]],function(e,t,i){var s=!0,a=e.component;"creationComplete"===t&&(s=!1!==a.onLoad()&&s);return s},b.ad,b.hb)),s.Ib(23,4440064,null,0,n.yb,[s.r,n.i],{width:[0,"width"],height:[1,"height"]},{creationComplete:"creationComplete"}),(e()(),s.Jb(24,0,null,0,66,"VBox",[["height","100%"],["paddingBottom","5"],["paddingLeft","5"],["paddingRight","5"],["paddingTop","5"],["verticalGap","0"],["width","100%"]],null,null,null,b.od,b.vb)),s.Ib(25,4440064,null,0,n.ec,[s.r,n.i,s.T],{verticalGap:[0,"verticalGap"],width:[1,"width"],height:[2,"height"],paddingTop:[3,"paddingTop"],paddingBottom:[4,"paddingBottom"],paddingLeft:[5,"paddingLeft"],paddingRight:[6,"paddingRight"]},null),(e()(),s.Jb(26,0,null,0,40,"SwtCanvas",[["height","13%"],["width","100%"]],null,null,null,b.Nc,b.U)),s.Ib(27,4440064,null,0,n.db,[s.r,n.i],{width:[0,"width"],height:[1,"height"]},null),(e()(),s.Jb(28,0,null,0,38,"VBox",[["height","100%"],["width","100%"]],null,null,null,b.od,b.vb)),s.Ib(29,4440064,null,0,n.ec,[s.r,n.i,s.T],{width:[0,"width"],height:[1,"height"]},null),(e()(),s.Jb(30,0,null,0,36,"HBox",[["height","100%"],["width","100%"]],null,null,null,b.Dc,b.K)),s.Ib(31,4440064,null,0,n.C,[s.r,n.i],{width:[0,"width"],height:[1,"height"]},null),(e()(),s.Jb(32,0,null,0,14,"VBox",[["height","100%"],["width","50%"]],null,null,null,b.od,b.vb)),s.Ib(33,4440064,null,0,n.ec,[s.r,n.i,s.T],{width:[0,"width"],height:[1,"height"]},null),(e()(),s.Jb(34,0,null,0,7,"HBox",[["height","80%"],["width","100%"]],null,null,null,b.Dc,b.K)),s.Ib(35,4440064,null,0,n.C,[s.r,n.i],{width:[0,"width"],height:[1,"height"]},null),(e()(),s.Jb(36,0,null,0,1,"SwtLabel",[["fontWeight","bold"],["id","lblCombo"]],null,null,null,b.Yc,b.fb)),s.Ib(37,4440064,[[4,4],["lblCombo",4]],0,n.vb,[s.r,n.i],{id:[0,"id"],fontWeight:[1,"fontWeight"]},null),(e()(),s.Jb(38,0,null,0,1,"SwtComboBox",[["dataLabel","entity"],["id","entityCombo"]],null,[[null,"change"],["window","mousewheel"]],function(e,t,i){var a=!0,r=e.component;"window:mousewheel"===t&&(a=!1!==s.Tb(e,39).mouseWeelEventHandler(i.target)&&a);"change"===t&&(a=!1!==r.entityComboChange(i)&&a);return a},b.Pc,b.W)),s.Ib(39,4440064,[[9,4],["entityCombo",4]],0,n.gb,[s.r,n.i],{dataLabel:[0,"dataLabel"],id:[1,"id"]},{change_:"change"}),(e()(),s.Jb(40,0,null,0,1,"SwtLabel",[["fontWeight","normal"],["id","selectedEntity"],["paddingLeft","10"],["text",""],["textAlign","left"]],null,null,null,b.Yc,b.fb)),s.Ib(41,4440064,[[5,4],["selectedEntity",4]],0,n.vb,[s.r,n.i],{id:[0,"id"],textAlign:[1,"textAlign"],paddingLeft:[2,"paddingLeft"],text:[3,"text"],fontWeight:[4,"fontWeight"]},null),(e()(),s.Jb(42,0,null,0,4,"HBox",[["height","25%"],["width","100%"]],null,null,null,b.Dc,b.K)),s.Ib(43,4440064,null,0,n.C,[s.r,n.i],{width:[0,"width"],height:[1,"height"]},null),(e()(),s.Jb(44,0,null,0,2,"SwtTabNavigator",[["borderBottom","false"],["height","100%"],["id","tabCategoryList"],["width","100%"]],null,[[null,"onChange"]],function(e,t,i){var s=!0,a=e.component;"onChange"===t&&(s=!1!==a.updateData("tree")&&s);return s},b.id,b.pb)),s.Ib(45,4440064,[[19,4],["tabCategoryList",4]],1,n.Ob,[s.r,n.i,s.k],{id:[0,"id"],width:[1,"width"],height:[2,"height"],borderBottom:[3,"borderBottom"]},{onChange_:"onChange"}),s.Zb(603979776,23,{tabChildren:1}),(e()(),s.Jb(47,0,null,0,19,"VBox",[["height","100%"],["verticalGap","0"],["width","50%"]],null,null,null,b.od,b.vb)),s.Ib(48,4440064,null,0,n.ec,[s.r,n.i,s.T],{verticalGap:[0,"verticalGap"],width:[1,"width"],height:[2,"height"]},null),(e()(),s.Jb(49,0,null,0,5,"HBox",[["height","30%"],["horizontalAlign","right"],["width","100%"]],null,null,null,b.Dc,b.K)),s.Ib(50,4440064,null,0,n.C,[s.r,n.i],{horizontalAlign:[0,"horizontalAlign"],width:[1,"width"],height:[2,"height"]},null),(e()(),s.Jb(51,0,null,0,1,"SwtLabel",[["fontWeight","bold"],["width","200"]],null,null,null,b.Yc,b.fb)),s.Ib(52,4440064,[[6,4],["currLabel",4]],0,n.vb,[s.r,n.i],{width:[0,"width"],fontWeight:[1,"fontWeight"]},null),(e()(),s.Jb(53,0,null,0,1,"SwtCheckBox",[["id","currBox"],["selected","false"],["value","N"]],null,[[null,"change"]],function(e,t,i){var s=!0,a=e.component;"change"===t&&(s=!1!==a.updateData("scenario")&&s);return s},b.Oc,b.V)),s.Ib(54,4440064,[[10,4],["currBox",4]],0,n.eb,[s.r,n.i],{id:[0,"id"],value:[1,"value"],selected:[2,"selected"]},{change_:"change"}),(e()(),s.Jb(55,0,null,0,5,"HBox",[["height","30%"],["horizontalAlign","right"],["width","100%"]],null,null,null,b.Dc,b.K)),s.Ib(56,4440064,null,0,n.C,[s.r,n.i],{horizontalAlign:[0,"horizontalAlign"],width:[1,"width"],height:[2,"height"]},null),(e()(),s.Jb(57,0,null,0,1,"SwtLabel",[["fontWeight","bold"],["width","200"]],null,null,null,b.Yc,b.fb)),s.Ib(58,4440064,[[7,4],["zeroLabel",4]],0,n.vb,[s.r,n.i],{width:[0,"width"],fontWeight:[1,"fontWeight"]},null),(e()(),s.Jb(59,0,null,0,1,"SwtCheckBox",[["id","zeroBox"],["selected","false"],["value","N"]],null,[[null,"change"]],function(e,t,i){var s=!0,a=e.component;"change"===t&&(s=!1!==a.updateData("scenario")&&s);return s},b.Oc,b.V)),s.Ib(60,4440064,[[11,4],["zeroBox",4]],0,n.eb,[s.r,n.i],{id:[0,"id"],value:[1,"value"],selected:[2,"selected"]},{change_:"change"}),(e()(),s.Jb(61,0,null,0,5,"HBox",[["height","30%"],["horizontalAlign","right"],["width","100%"]],null,null,null,b.Dc,b.K)),s.Ib(62,4440064,null,0,n.C,[s.r,n.i],{horizontalAlign:[0,"horizontalAlign"],width:[1,"width"],height:[2,"height"]},null),(e()(),s.Jb(63,0,null,0,1,"SwtLabel",[["fontWeight","bold"],["width","200"]],null,null,null,b.Yc,b.fb)),s.Ib(64,4440064,[[8,4],["alertLabel",4]],0,n.vb,[s.r,n.i],{width:[0,"width"],fontWeight:[1,"fontWeight"]},null),(e()(),s.Jb(65,0,null,0,1,"SwtCheckBox",[["id","alertBox"],["selected","false"],["value","N"]],null,[[null,"change"]],function(e,t,i){var s=!0,a=e.component;"change"===t&&(s=!1!==a.updateData("scenario")&&s);return s},b.Oc,b.V)),s.Ib(66,4440064,[[12,4],["alertBox",4]],0,n.eb,[s.r,n.i],{id:[0,"id"],value:[1,"value"],selected:[2,"selected"]},{change_:"change"}),(e()(),s.Jb(67,0,null,0,3,"SwtCanvas",[["height","75%"],["width","100%"]],null,null,null,b.Nc,b.U)),s.Ib(68,4440064,[[18,4],["mainGroup",4]],0,n.db,[s.r,n.i],{width:[0,"width"],height:[1,"height"]},null),(e()(),s.Jb(69,0,null,0,1,"SwtSummary",[["IDField","id"],["height","100%"],["id","summary"],["width","100%"]],null,null,null,b.hd,b.ob)),s.Ib(70,4440064,[[22,4],["summary",4]],0,n.Nb,[s.r,n.i,s.T],{id:[0,"id"],width:[1,"width"],height:[2,"height"],IDField:[3,"IDField"]},null),(e()(),s.Jb(71,0,null,0,19,"SwtCanvas",[["height","7%"],["width","100%"]],null,null,null,b.Nc,b.U)),s.Ib(72,4440064,null,0,n.db,[s.r,n.i],{width:[0,"width"],height:[1,"height"]},null),(e()(),s.Jb(73,0,null,0,17,"HBox",[["height","100%"],["id","controlContainer"],["width","100%"]],null,null,null,b.Dc,b.K)),s.Ib(74,4440064,null,0,n.C,[s.r,n.i],{id:[0,"id"],width:[1,"width"],height:[2,"height"]},null),(e()(),s.Jb(75,0,null,0,5,"HBox",[["paddingLeft","8"],["width","50%"]],null,null,null,b.Dc,b.K)),s.Ib(76,4440064,null,0,n.C,[s.r,n.i],{width:[0,"width"],paddingLeft:[1,"paddingLeft"]},null),(e()(),s.Jb(77,0,null,0,1,"SwtButton",[["id","refreshButton"]],null,[[null,"click"]],function(e,t,i){var s=!0,a=e.component;"click"===t&&(s=!1!==a.updateData("tree")&&s);return s},b.Mc,b.T)),s.Ib(78,4440064,[[2,4],["refreshButton",4]],0,n.cb,[s.r,n.i],{id:[0,"id"]},{onClick_:"click"}),(e()(),s.Jb(79,0,null,0,1,"SwtButton",[["id","closeButton"]],null,[[null,"click"]],function(e,t,i){var s=!0,a=e.component;"click"===t&&(s=!1!==a.closeHandler(i)&&s);return s},b.Mc,b.T)),s.Ib(80,4440064,[[3,4],["closeButton",4]],0,n.cb,[s.r,n.i],{id:[0,"id"]},{onClick_:"click"}),(e()(),s.Jb(81,0,null,0,9,"HBox",[["horizontalAlign","right"],["width","50%"]],null,null,null,b.Dc,b.K)),s.Ib(82,4440064,null,0,n.C,[s.r,n.i],{horizontalAlign:[0,"horizontalAlign"],width:[1,"width"]},null),(e()(),s.Jb(83,0,null,0,1,"SwtText",[["height","16"],["id","lostConnectionText"],["right","45"],["styleName","redText"],["text","CONNECTION ERROR"],["visible","false"]],null,null,null,b.ld,b.qb)),s.Ib(84,4440064,[[15,4],["lostConnectionText",4]],0,n.Pb,[s.r,n.i],{id:[0,"id"],right:[1,"right"],styleName:[2,"styleName"],height:[3,"height"],visible:[4,"visible"],text:[5,"text"]},null),(e()(),s.Jb(85,0,null,0,1,"SwtText",[["fontWeight","normal"],["height","16"],["id","lastRefTimeLabel"],["right","65"]],null,null,null,b.ld,b.qb)),s.Ib(86,4440064,[[17,4],["lastRefTimeLabel",4]],0,n.Pb,[s.r,n.i],{id:[0,"id"],right:[1,"right"],height:[2,"height"],fontWeight:[3,"fontWeight"]},null),(e()(),s.Jb(87,0,null,0,1,"SwtText",[["height","16"],["id","lastRefTime"],["right","65"],["visible","false"],["width","90"]],null,null,null,b.ld,b.qb)),s.Ib(88,4440064,[[16,4],["lastRefTime",4]],0,n.Pb,[s.r,n.i],{id:[0,"id"],right:[1,"right"],width:[2,"width"],height:[3,"height"],visible:[4,"visible"]},null),(e()(),s.Jb(89,0,null,0,1,"SwtLoadingImage",[],null,null,null,b.Zc,b.gb)),s.Ib(90,114688,[[13,4],["loadingImage",4]],0,n.xb,[s.r],null,null)],function(e,t){e(t,23,0,"100%","100%");e(t,25,0,"0","100%","100%","5","5","5","5");e(t,27,0,"100%","13%");e(t,29,0,"100%","100%");e(t,31,0,"100%","100%");e(t,33,0,"50%","100%");e(t,35,0,"100%","80%");e(t,37,0,"lblCombo","bold");e(t,39,0,"entity","entityCombo");e(t,41,0,"selectedEntity","left","10","","normal");e(t,43,0,"100%","25%");e(t,45,0,"tabCategoryList","100%","100%","false");e(t,48,0,"0","50%","100%");e(t,50,0,"right","100%","30%");e(t,52,0,"200","bold");e(t,54,0,"currBox","N","false");e(t,56,0,"right","100%","30%");e(t,58,0,"200","bold");e(t,60,0,"zeroBox","N","false");e(t,62,0,"right","100%","30%");e(t,64,0,"200","bold");e(t,66,0,"alertBox","N","false");e(t,68,0,"100%","75%");e(t,70,0,"summary","100%","100%","id");e(t,72,0,"100%","7%");e(t,74,0,"controlContainer","100%","100%");e(t,76,0,"50%","8");e(t,78,0,"refreshButton");e(t,80,0,"closeButton");e(t,82,0,"right","50%");e(t,84,0,"lostConnectionText","45","redText","16","false","CONNECTION ERROR");e(t,86,0,"lastRefTimeLabel","65","16","normal");e(t,88,0,"lastRefTime","65","90","16","false"),e(t,90,0)},null)}function _(e){return s.dc(0,[(e()(),s.Jb(0,0,null,null,1,"app-scenario-summary",[],null,null,null,z,E)),s.Ib(1,4440064,null,0,l,[n.i,s.r],null,null)],function(e,t){e(t,1,0)},null)}var F=s.Fb("app-scenario-summary",l,_,{maxChars:"maxChars",restrict:"restrict",id:"id",dropShadowEnabled:"dropShadowEnabled",cornerRadius:"cornerRadius",borderThickness:"borderThickness",borderStyle:"borderStyle",borderColor:"borderColor",backGroundColor:"backGroundColor",right:"right",left:"left",bottom:"bottom",top:"top",horizontalGap:"horizontalGap",verticalGap:"verticalGap",textAlign:"textAlign",toolTip:"toolTip",toolTipPreviousValue:"toolTipPreviousValue",textDictionaryId:"tooltipDictionaryId",name:"name",styleName:"styleName",horizontalAlign:"horizontalAlign",verticalAlign:"verticalAlign",width:"width",showScrollBar:"showScrollBar",height:"height",minHeight:"minHeight",minWidth:"minWidth",maxHeight:"maxHeight",maxWidth:"maxWidth",includeInLayout:"includeInLayout",visible:"visible",enabled:"enabled",paddingTop:"paddingTop",paddingBottom:"paddingBottom",paddingLeft:"paddingLeft",paddingRight:"paddingRight",marginTop:"marginTop",marginBottom:"marginBottom",marginLeft:"marginLeft",marginRight:"marginRight",contextMenu:"contextMenu"},{onClick_:"click",dbClick_:"dbClick",doubleClick_:"doubleClick",itemDoubleClick_:"itemDoubleClick",onKeyDown_:"keyDown",onKeyUp_:"keyUp",mouseUp_:"mouseUp",mouseOver_:"mouseOver",mouseDown_:"mouseDown",mouseEnter_:"mouseEnter",mouseLeave_:"mouseLeave",mouseOut_:"mouseOut",mouseIn_:"mouseIn",mouseMove_:"mouseMove",focus_:"focus",focusIn_:"focusIn",onFocusOut_:"focusOut",keyFocusChange_:"keyFocusChange",change_:"change",onSpyChange:"onSpyChange",onSpyNoChange:"onSpyNoChange",scroll_:"scroll",creationComplete:"creationComplete",preinitialize:"preinitialize"},[])}}]);