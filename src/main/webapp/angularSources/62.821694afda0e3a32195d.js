(window.webpackJsonp=window.webpackJsonp||[]).push([[62],{pRS9:function(l,e,t){"use strict";t.r(e);var n=t("CcnG"),i=t("mrSG"),u=t("ZYCi"),c=t("447K"),s=function(l){function e(e,t){var n=l.call(this,t,e)||this;return n.commonService=e,n.element=t,n.listParams=[],n.columns=[],n.swtalert=new c.bb(e),n}return i.d(e,l),e.prototype.ngOnInit=function(){this.scenarioIdLbl.text=c.Wb.getPredictMessage("scenario.scenarioId",null),this.apiTypeLbl.text=c.Wb.getPredictMessage("scenario.apiTypeLbl",null),this.hostLbl.text=c.Wb.getPredictMessage("scenario.hostLbl",null),this.entityLbl.text=c.Wb.getPredictMessage("scenario.entityLbl",null),this.ccyLbl.text=c.Wb.getPredictMessage("scenario.ccyLbl",null),this.accountLbl.text=c.Wb.getPredictMessage("scenario.accountLbl",null),this.valDateLbl.text=c.Wb.getPredictMessage("scenario.valDateLbl",null),this.amountLbl.text=c.Wb.getPredictMessage("scenario.amountLbl",null),this.signLbl.text=c.Wb.getPredictMessage("scenario.signLbl",null),this.mvtLbl.text=c.Wb.getPredictMessage("scenario.mvtLbl",null),this.matchLbl.text=c.Wb.getPredictMessage("scenario.matchLbl",null),this.sweepLbl.text=c.Wb.getPredictMessage("scenario.sweepLbl",null),this.payLbl.text=c.Wb.getPredictMessage("scenario.payLbl",null),this.allLbl.text=c.Wb.getPredictMessage("scenario.allLbl",null),this.cancelButton.label=c.Wb.getPredictMessage("button.cancel",null),this.okButton.label=c.Wb.getPredictMessage("button.ok",null),this.fieldSet.legendText=c.Wb.getPredictMessage("scenario.configFieldSet.legendText",null),this.scenarioIdtxt.toolTip=c.Wb.getPredictMessage("scenario.scenarioId",null),this.apiTypeCombo.toolTip=c.Wb.getPredictMessage("scenario.tooltip.apiType",null),this.hostCheck.toolTip=c.Wb.getPredictMessage("scenario.tooltip.hostId",null),this.entityCheck.toolTip=c.Wb.getPredictMessage("scenario.tooltip.entityId",null),this.ccyCheck.toolTip=c.Wb.getPredictMessage("scenario.tooltip.ccy",null),this.accountCheck.toolTip=c.Wb.getPredictMessage("scenario.tooltip.accountId",null),this.valDateCheck.toolTip=c.Wb.getPredictMessage("scenario.tooltip.valDate",null),this.amountCheck.toolTip=c.Wb.getPredictMessage("scenario.tooltip.amount",null),this.signCheck.toolTip=c.Wb.getPredictMessage("scenario.tooltip.sign",null),this.mvtCheck.toolTip=c.Wb.getPredictMessage("scenario.tooltip.mvt",null),this.matchCheck.toolTip=c.Wb.getPredictMessage("scenario.tooltip.match",null),this.sweepCheck.toolTip=c.Wb.getPredictMessage("scenario.tooltip.sweep",null),this.payCheck.toolTip=c.Wb.getPredictMessage("scenario.tooltip.payment",null),this.allCheck.toolTip=c.Wb.getPredictMessage("scenario.tooltip.checkedAll",null),this.cancelButton.toolTip=c.Wb.getPredictMessage("tooltip.cancelbutton",null),this.okButton.toolTip=c.Wb.getPredictMessage("tooltip.ok",null)},e.prototype.onLoad=function(){var l,e;if(window.opener&&window.opener.instanceElement){e=[e=(l=window.opener.instanceElement.sendGeneralDataToConfigScreen()).apiTypeList.option],this.apiTypeCombo.setComboData(e),this.apiTypeCombo.dataProvider=e,this.methodName=l.methodName;var t=window.opener.instanceElement.savedApiParams,n=window.opener.instanceElement.apiRequiredCols;this.scenarioIdtxt.text=l.scenarioId,this.savedAttributes=n.length>0?n.toString():t,this.savedAttributes&&this.populateCheckBoxes()}},e.prototype.populateCheckBoxes=function(){if(this.columns=this.savedAttributes.replace("[","").replace("]","").split(","),11==this.columns.length)this.checkAll(),this.allCheck.selected=!0;else for(var l=0;l<this.columns.length;l++)this.selectMapping(this.columns[l].replace(/"/g,""))},e.prototype.selectMapping=function(l){switch(l){case c.Wb.getPredictMessage("scenario.hostLbl",null):this.hostCheck.selected=!0;break;case c.Wb.getPredictMessage("scenario.entityLbl",null):this.entityCheck.selected=!0;break;case c.Wb.getPredictMessage("scenario.ccyLbl",null):this.ccyCheck.selected=!0;break;case c.Wb.getPredictMessage("scenario.accountLbl",null):this.accountCheck.selected=!0;break;case c.Wb.getPredictMessage("scenario.valDateLbl",null):this.valDateCheck.selected=!0;break;case c.Wb.getPredictMessage("scenario.amountLbl",null):this.amountCheck.selected=!0;break;case c.Wb.getPredictMessage("scenario.signLbl",null):this.signCheck.selected=!0;break;case c.Wb.getPredictMessage("scenario.mvtLbl",null):this.mvtCheck.selected=!0;break;case c.Wb.getPredictMessage("scenario.matchLbl",null):this.matchCheck.selected=!0;break;case c.Wb.getPredictMessage("scenario.sweepLbl",null):this.sweepCheck.selected=!0;break;case c.Wb.getPredictMessage("scenario.payLbl",null):this.payCheck.selected=!0}},e.prototype.checkAll=function(){0==this.allCheck.selected?(this.hostCheck.selected=!0,this.entityCheck.selected=!0,this.ccyCheck.selected=!0,this.accountCheck.selected=!0,this.valDateCheck.selected=!0,this.amountCheck.selected=!0,this.signCheck.selected=!0,this.mvtCheck.selected=!0,this.matchCheck.selected=!0,this.sweepCheck.selected=!0,this.payCheck.selected=!0):(this.hostCheck.selected=!1,this.entityCheck.selected=!1,this.ccyCheck.selected=!1,this.accountCheck.selected=!1,this.valDateCheck.selected=!1,this.amountCheck.selected=!1,this.signCheck.selected=!1,this.mvtCheck.selected=!1,this.matchCheck.selected=!1,this.sweepCheck.selected=!1,this.payCheck.selected=!1)},e.prototype.cancelHandler=function(){c.x.call("close")},e.prototype.saveMandatoryAttributes=function(){this.listParams=[],1==this.hostCheck.selected&&this.listParams.push(c.Wb.getPredictMessage("scenario.hostLbl",null)),1==this.entityCheck.selected&&this.listParams.push(c.Wb.getPredictMessage("scenario.entityLbl",null)),1==this.ccyCheck.selected&&this.listParams.push(c.Wb.getPredictMessage("scenario.ccyLbl",null)),1==this.accountCheck.selected&&this.listParams.push(c.Wb.getPredictMessage("scenario.accountLbl",null)),1==this.valDateCheck.selected&&this.listParams.push(c.Wb.getPredictMessage("scenario.valDateLbl",null)),1==this.amountCheck.selected&&this.listParams.push(c.Wb.getPredictMessage("scenario.amountLbl",null)),1==this.signCheck.selected&&this.listParams.push(c.Wb.getPredictMessage("scenario.signLbl",null)),1==this.mvtCheck.selected&&this.listParams.push(c.Wb.getPredictMessage("scenario.mvtLbl",null)),1==this.matchCheck.selected&&this.listParams.push(c.Wb.getPredictMessage("scenario.matchLbl",null)),1==this.sweepCheck.selected&&this.listParams.push(c.Wb.getPredictMessage("scenario.sweepLbl",null)),1==this.payCheck.selected&&this.listParams.push(c.Wb.getPredictMessage("scenario.payLbl",null)),window.opener&&window.opener.instanceElement&&(window.opener.instanceElement.apiRequiredCols=this.listParams,window.opener.instanceElement.CreateInsDesc.text=this.listParams.length>0?c.Wb.getPredictMessage("scenario.CreateInsDescFull",null)+"<"+this.listParams.toString()+">":c.Wb.getPredictMessage("scenario.CreateInsDesc",null)),window.close()},e}(c.yb),a=[{path:"",component:s}],b=(u.l.forChild(a),function(){return function(){}}()),d=t("pMnS"),o=t("RChO"),h=t("t6HQ"),r=t("WFGK"),g=t("5FqG"),p=t("Ip0R"),w=t("gIcY"),m=t("t/Na"),C=t("sE5F"),k=t("OzfB"),I=t("T7CS"),f=t("S7LP"),L=t("6aHO"),J=t("WzUx"),R=t("A7o+"),y=t("zCE2"),v=t("Jg5P"),P=t("3R0m"),A=t("hhbb"),B=t("5rxC"),M=t("Fzqc"),T=t("21Lb"),W=t("hUWP"),x=t("3pJQ"),G=t("V9q+"),S=t("VDKW"),D=t("kXfT"),Z=t("BGbe");t.d(e,"ConfigureParamsModuleNgFactory",function(){return _}),t.d(e,"RenderType_ConfigureParams",function(){return z}),t.d(e,"View_ConfigureParams_0",function(){return V}),t.d(e,"View_ConfigureParams_Host_0",function(){return H}),t.d(e,"ConfigureParamsNgFactory",function(){return E});var _=n.Gb(b,[],function(l){return n.Qb([n.Rb(512,n.n,n.vb,[[8,[d.a,o.a,h.a,r.a,g.Cb,g.Pb,g.r,g.rc,g.s,g.Ab,g.Bb,g.Db,g.qd,g.Hb,g.k,g.Ib,g.Nb,g.Ub,g.yb,g.Jb,g.v,g.A,g.e,g.c,g.g,g.d,g.Kb,g.f,g.ec,g.Wb,g.bc,g.ac,g.sc,g.fc,g.lc,g.jc,g.Eb,g.Fb,g.mc,g.Lb,g.nc,g.Mb,g.dc,g.Rb,g.b,g.ic,g.Yb,g.Sb,g.kc,g.y,g.Qb,g.cc,g.hc,g.pc,g.oc,g.xb,g.p,g.q,g.o,g.h,g.j,g.w,g.Zb,g.i,g.m,g.Vb,g.Ob,g.Gb,g.Xb,g.t,g.tc,g.zb,g.n,g.qc,g.a,g.z,g.rd,g.sd,g.x,g.td,g.gc,g.l,g.u,g.ud,g.Tb,E]],[3,n.n],n.J]),n.Rb(4608,p.m,p.l,[n.F,[2,p.u]]),n.Rb(4608,w.c,w.c,[]),n.Rb(4608,w.p,w.p,[]),n.Rb(4608,m.j,m.p,[p.c,n.O,m.n]),n.Rb(4608,m.q,m.q,[m.j,m.o]),n.Rb(5120,m.a,function(l){return[l,new c.tb]},[m.q]),n.Rb(4608,m.m,m.m,[]),n.Rb(6144,m.k,null,[m.m]),n.Rb(4608,m.i,m.i,[m.k]),n.Rb(6144,m.b,null,[m.i]),n.Rb(4608,m.f,m.l,[m.b,n.B]),n.Rb(4608,m.c,m.c,[m.f]),n.Rb(4608,C.c,C.c,[]),n.Rb(4608,C.g,C.b,[]),n.Rb(5120,C.i,C.j,[]),n.Rb(4608,C.h,C.h,[C.c,C.g,C.i]),n.Rb(4608,C.f,C.a,[]),n.Rb(5120,C.d,C.k,[C.h,C.f]),n.Rb(5120,n.b,function(l,e){return[k.j(l,e)]},[p.c,n.O]),n.Rb(4608,I.a,I.a,[]),n.Rb(4608,f.a,f.a,[]),n.Rb(4608,L.a,L.a,[n.n,n.L,n.B,f.a,n.g]),n.Rb(4608,J.c,J.c,[n.n,n.g,n.B]),n.Rb(4608,J.e,J.e,[J.c]),n.Rb(4608,R.l,R.l,[]),n.Rb(4608,R.h,R.g,[]),n.Rb(4608,R.c,R.f,[]),n.Rb(4608,R.j,R.d,[]),n.Rb(4608,R.b,R.a,[]),n.Rb(4608,R.k,R.k,[R.l,R.h,R.c,R.j,R.b,R.m,R.n]),n.Rb(4608,J.i,J.i,[[2,R.k]]),n.Rb(4608,J.r,J.r,[J.L,[2,R.k],J.i]),n.Rb(4608,J.t,J.t,[]),n.Rb(4608,J.w,J.w,[]),n.Rb(1073742336,u.l,u.l,[[2,u.r],[2,u.k]]),n.Rb(1073742336,p.b,p.b,[]),n.Rb(1073742336,w.n,w.n,[]),n.Rb(1073742336,w.l,w.l,[]),n.Rb(1073742336,y.a,y.a,[]),n.Rb(1073742336,v.a,v.a,[]),n.Rb(1073742336,w.e,w.e,[]),n.Rb(1073742336,P.a,P.a,[]),n.Rb(1073742336,R.i,R.i,[]),n.Rb(1073742336,J.b,J.b,[]),n.Rb(1073742336,m.e,m.e,[]),n.Rb(1073742336,m.d,m.d,[]),n.Rb(1073742336,C.e,C.e,[]),n.Rb(1073742336,A.b,A.b,[]),n.Rb(1073742336,B.b,B.b,[]),n.Rb(1073742336,k.c,k.c,[]),n.Rb(1073742336,M.a,M.a,[]),n.Rb(1073742336,T.d,T.d,[]),n.Rb(1073742336,W.c,W.c,[]),n.Rb(1073742336,x.a,x.a,[]),n.Rb(1073742336,G.a,G.a,[[2,k.g],n.O]),n.Rb(1073742336,S.b,S.b,[]),n.Rb(1073742336,D.a,D.a,[]),n.Rb(1073742336,Z.b,Z.b,[]),n.Rb(1073742336,c.Tb,c.Tb,[]),n.Rb(1073742336,b,b,[]),n.Rb(256,m.n,"XSRF-TOKEN",[]),n.Rb(256,m.o,"X-XSRF-TOKEN",[]),n.Rb(256,"config",{},[]),n.Rb(256,R.m,void 0,[]),n.Rb(256,R.n,void 0,[]),n.Rb(256,"popperDefaults",{},[]),n.Rb(1024,u.i,function(){return[[{path:"",component:s}]]},[])])}),O=[[""]],z=n.Hb({encapsulation:0,styles:O,data:{}});function V(l){return n.dc(0,[n.Zb(*********,1,{_container:0}),n.Zb(*********,2,{scenarioIdLbl:0}),n.Zb(*********,3,{apiTypeLbl:0}),n.Zb(*********,4,{scenarioIdtxt:0}),n.Zb(*********,5,{apiTypeCombo:0}),n.Zb(*********,6,{scheduleCanvas:0}),n.Zb(*********,7,{okButton:0}),n.Zb(*********,8,{cancelButton:0}),n.Zb(*********,9,{fieldSet:0}),n.Zb(*********,10,{hostCheck:0}),n.Zb(*********,11,{entityCheck:0}),n.Zb(*********,12,{ccyCheck:0}),n.Zb(*********,13,{accountCheck:0}),n.Zb(*********,14,{valDateCheck:0}),n.Zb(*********,15,{amountCheck:0}),n.Zb(*********,16,{signCheck:0}),n.Zb(*********,17,{mvtCheck:0}),n.Zb(*********,18,{matchCheck:0}),n.Zb(*********,19,{sweepCheck:0}),n.Zb(*********,20,{payCheck:0}),n.Zb(*********,21,{allCheck:0}),n.Zb(*********,22,{hostLbl:0}),n.Zb(*********,23,{entityLbl:0}),n.Zb(*********,24,{ccyLbl:0}),n.Zb(*********,25,{accountLbl:0}),n.Zb(*********,26,{valDateLbl:0}),n.Zb(*********,27,{amountLbl:0}),n.Zb(*********,28,{signLbl:0}),n.Zb(*********,29,{mvtLbl:0}),n.Zb(*********,30,{matchLbl:0}),n.Zb(*********,31,{sweepLbl:0}),n.Zb(*********,32,{payLbl:0}),n.Zb(*********,33,{allLbl:0}),(l()(),n.Jb(33,0,null,null,165,"SwtModule",[["height","100%"],["width","100%"]],null,[[null,"creationComplete"]],function(l,e,t){var n=!0,i=l.component;"creationComplete"===e&&(n=!1!==i.onLoad()&&n);return n},g.ad,g.hb)),n.Ib(34,4440064,null,0,c.yb,[n.r,c.i],{width:[0,"width"],height:[1,"height"]},{creationComplete:"creationComplete"}),(l()(),n.Jb(35,0,null,0,163,"VBox",[["height","100%"],["paddingBottom","5"],["paddingLeft","5"],["paddingRight","5"],["paddingTop","5"],["width","100%"]],null,null,null,g.od,g.vb)),n.Ib(36,4440064,null,0,c.ec,[n.r,c.i,n.T],{width:[0,"width"],height:[1,"height"],paddingTop:[2,"paddingTop"],paddingBottom:[3,"paddingBottom"],paddingLeft:[4,"paddingLeft"],paddingRight:[5,"paddingRight"]},null),(l()(),n.Jb(37,0,null,0,21,"Grid",[["height","20%"],["paddingTop","10"],["width","100%"]],null,null,null,g.Cc,g.H)),n.Ib(38,4440064,null,0,c.z,[n.r,c.i],{width:[0,"width"],height:[1,"height"],paddingTop:[2,"paddingTop"]},null),(l()(),n.Jb(39,0,null,0,9,"GridRow",[["paddingLeft","23"]],null,null,null,g.Bc,g.J)),n.Ib(40,4440064,null,0,c.B,[n.r,c.i],{paddingLeft:[0,"paddingLeft"]},null),(l()(),n.Jb(41,0,null,0,3,"GridItem",[["width","120"]],null,null,null,g.Ac,g.I)),n.Ib(42,4440064,null,0,c.A,[n.r,c.i],{width:[0,"width"]},null),(l()(),n.Jb(43,0,null,0,1,"SwtLabel",[],null,null,null,g.Yc,g.fb)),n.Ib(44,4440064,[[2,4],["scenarioIdLbl",4]],0,c.vb,[n.r,c.i],null,null),(l()(),n.Jb(45,0,null,0,3,"GridItem",[],null,null,null,g.Ac,g.I)),n.Ib(46,4440064,null,0,c.A,[n.r,c.i],null,null),(l()(),n.Jb(47,0,null,0,1,"SwtTextInput",[["enabled","false"],["width","200"]],null,null,null,g.kd,g.sb)),n.Ib(48,4440064,[[4,4],["scenarioIdtxt",4]],0,c.Rb,[n.r,c.i],{width:[0,"width"],enabled:[1,"enabled"]},null),(l()(),n.Jb(49,0,null,0,9,"GridRow",[["paddingLeft","23"]],null,null,null,g.Bc,g.J)),n.Ib(50,4440064,null,0,c.B,[n.r,c.i],{paddingLeft:[0,"paddingLeft"]},null),(l()(),n.Jb(51,0,null,0,3,"GridItem",[["width","120"]],null,null,null,g.Ac,g.I)),n.Ib(52,4440064,null,0,c.A,[n.r,c.i],{width:[0,"width"]},null),(l()(),n.Jb(53,0,null,0,1,"SwtLabel",[],null,null,null,g.Yc,g.fb)),n.Ib(54,4440064,[[3,4],["apiTypeLbl",4]],0,c.vb,[n.r,c.i],null,null),(l()(),n.Jb(55,0,null,0,3,"GridItem",[],null,null,null,g.Ac,g.I)),n.Ib(56,4440064,null,0,c.A,[n.r,c.i],null,null),(l()(),n.Jb(57,0,null,0,1,"SwtComboBox",[["id","apiTypeCombo"],["width","200"]],null,[["window","mousewheel"]],function(l,e,t){var i=!0;"window:mousewheel"===e&&(i=!1!==n.Tb(l,58).mouseWeelEventHandler(t.target)&&i);return i},g.Pc,g.W)),n.Ib(58,4440064,[[5,4],["apiTypeCombo",4]],0,c.gb,[n.r,c.i],{width:[0,"width"],id:[1,"id"]},null),(l()(),n.Jb(59,0,null,0,131,"SwtFieldSet",[["id","fieldSet"],["style","height: 70%; width: 100%; color:blue; padding-right:5px; padding-bottom: 10px; padding-left: 5px;"]],null,null,null,g.Vc,g.cb)),n.Ib(60,4440064,[[9,4],["fieldSet",4]],0,c.ob,[n.r,c.i],{id:[0,"id"]},null),(l()(),n.Jb(61,0,null,0,129,"HBox",[["height","100%"],["width","100%"]],null,null,null,g.Dc,g.K)),n.Ib(62,4440064,null,0,c.C,[n.r,c.i],{width:[0,"width"],height:[1,"height"]},null),(l()(),n.Jb(63,0,null,0,73,"HBox",[["height","100%"],["width","50%"]],null,null,null,g.Dc,g.K)),n.Ib(64,4440064,null,0,c.C,[n.r,c.i],{width:[0,"width"],height:[1,"height"]},null),(l()(),n.Jb(65,0,null,0,71,"Grid",[["height","100%"],["paddingLeft","10"],["paddingTop","10"],["width","100%"]],null,null,null,g.Cc,g.H)),n.Ib(66,4440064,null,0,c.z,[n.r,c.i],{width:[0,"width"],height:[1,"height"],paddingTop:[2,"paddingTop"],paddingLeft:[3,"paddingLeft"]},null),(l()(),n.Jb(67,0,null,0,9,"GridRow",[],null,null,null,g.Bc,g.J)),n.Ib(68,4440064,null,0,c.B,[n.r,c.i],null,null),(l()(),n.Jb(69,0,null,0,3,"GridItem",[["width","30"]],null,null,null,g.Ac,g.I)),n.Ib(70,4440064,null,0,c.A,[n.r,c.i],{width:[0,"width"]},null),(l()(),n.Jb(71,0,null,0,1,"SwtCheckBox",[["id","hostCheck"],["selected","false"]],null,null,null,g.Oc,g.V)),n.Ib(72,4440064,[[10,4],["hostCheck",4]],0,c.eb,[n.r,c.i],{id:[0,"id"],selected:[1,"selected"]},null),(l()(),n.Jb(73,0,null,0,3,"GridItem",[["width","250"]],null,null,null,g.Ac,g.I)),n.Ib(74,4440064,null,0,c.A,[n.r,c.i],{width:[0,"width"]},null),(l()(),n.Jb(75,0,null,0,1,"SwtLabel",[],null,null,null,g.Yc,g.fb)),n.Ib(76,4440064,[[22,4],["hostLbl",4]],0,c.vb,[n.r,c.i],null,null),(l()(),n.Jb(77,0,null,0,9,"GridRow",[],null,null,null,g.Bc,g.J)),n.Ib(78,4440064,null,0,c.B,[n.r,c.i],null,null),(l()(),n.Jb(79,0,null,0,3,"GridItem",[["width","30"]],null,null,null,g.Ac,g.I)),n.Ib(80,4440064,null,0,c.A,[n.r,c.i],{width:[0,"width"]},null),(l()(),n.Jb(81,0,null,0,1,"SwtCheckBox",[["id","entityCheck"],["selected","false"]],null,null,null,g.Oc,g.V)),n.Ib(82,4440064,[[11,4],["entityCheck",4]],0,c.eb,[n.r,c.i],{id:[0,"id"],selected:[1,"selected"]},null),(l()(),n.Jb(83,0,null,0,3,"GridItem",[["width","250"]],null,null,null,g.Ac,g.I)),n.Ib(84,4440064,null,0,c.A,[n.r,c.i],{width:[0,"width"]},null),(l()(),n.Jb(85,0,null,0,1,"SwtLabel",[],null,null,null,g.Yc,g.fb)),n.Ib(86,4440064,[[23,4],["entityLbl",4]],0,c.vb,[n.r,c.i],null,null),(l()(),n.Jb(87,0,null,0,9,"GridRow",[],null,null,null,g.Bc,g.J)),n.Ib(88,4440064,null,0,c.B,[n.r,c.i],null,null),(l()(),n.Jb(89,0,null,0,3,"GridItem",[["width","30"]],null,null,null,g.Ac,g.I)),n.Ib(90,4440064,null,0,c.A,[n.r,c.i],{width:[0,"width"]},null),(l()(),n.Jb(91,0,null,0,1,"SwtCheckBox",[["id","ccyCheck"],["selected","false"]],null,null,null,g.Oc,g.V)),n.Ib(92,4440064,[[12,4],["ccyCheck",4]],0,c.eb,[n.r,c.i],{id:[0,"id"],selected:[1,"selected"]},null),(l()(),n.Jb(93,0,null,0,3,"GridItem",[["width","250"]],null,null,null,g.Ac,g.I)),n.Ib(94,4440064,null,0,c.A,[n.r,c.i],{width:[0,"width"]},null),(l()(),n.Jb(95,0,null,0,1,"SwtLabel",[],null,null,null,g.Yc,g.fb)),n.Ib(96,4440064,[[24,4],["ccyLbl",4]],0,c.vb,[n.r,c.i],null,null),(l()(),n.Jb(97,0,null,0,9,"GridRow",[],null,null,null,g.Bc,g.J)),n.Ib(98,4440064,null,0,c.B,[n.r,c.i],null,null),(l()(),n.Jb(99,0,null,0,3,"GridItem",[["width","30"]],null,null,null,g.Ac,g.I)),n.Ib(100,4440064,null,0,c.A,[n.r,c.i],{width:[0,"width"]},null),(l()(),n.Jb(101,0,null,0,1,"SwtCheckBox",[["id","accountCheck"],["selected","false"]],null,null,null,g.Oc,g.V)),n.Ib(102,4440064,[[13,4],["accountCheck",4]],0,c.eb,[n.r,c.i],{id:[0,"id"],selected:[1,"selected"]},null),(l()(),n.Jb(103,0,null,0,3,"GridItem",[["width","250"]],null,null,null,g.Ac,g.I)),n.Ib(104,4440064,null,0,c.A,[n.r,c.i],{width:[0,"width"]},null),(l()(),n.Jb(105,0,null,0,1,"SwtLabel",[],null,null,null,g.Yc,g.fb)),n.Ib(106,4440064,[[25,4],["accountLbl",4]],0,c.vb,[n.r,c.i],null,null),(l()(),n.Jb(107,0,null,0,9,"GridRow",[],null,null,null,g.Bc,g.J)),n.Ib(108,4440064,null,0,c.B,[n.r,c.i],null,null),(l()(),n.Jb(109,0,null,0,3,"GridItem",[["width","30"]],null,null,null,g.Ac,g.I)),n.Ib(110,4440064,null,0,c.A,[n.r,c.i],{width:[0,"width"]},null),(l()(),n.Jb(111,0,null,0,1,"SwtCheckBox",[["id","valDateCheck"],["selected","false"]],null,null,null,g.Oc,g.V)),n.Ib(112,4440064,[[14,4],["valDateCheck",4]],0,c.eb,[n.r,c.i],{id:[0,"id"],selected:[1,"selected"]},null),(l()(),n.Jb(113,0,null,0,3,"GridItem",[["width","250"]],null,null,null,g.Ac,g.I)),n.Ib(114,4440064,null,0,c.A,[n.r,c.i],{width:[0,"width"]},null),(l()(),n.Jb(115,0,null,0,1,"SwtLabel",[],null,null,null,g.Yc,g.fb)),n.Ib(116,4440064,[[26,4],["valDateLbl",4]],0,c.vb,[n.r,c.i],null,null),(l()(),n.Jb(117,0,null,0,9,"GridRow",[],null,null,null,g.Bc,g.J)),n.Ib(118,4440064,null,0,c.B,[n.r,c.i],null,null),(l()(),n.Jb(119,0,null,0,3,"GridItem",[["width","30"]],null,null,null,g.Ac,g.I)),n.Ib(120,4440064,null,0,c.A,[n.r,c.i],{width:[0,"width"]},null),(l()(),n.Jb(121,0,null,0,1,"SwtCheckBox",[["id","amountCheck"],["selected","false"]],null,null,null,g.Oc,g.V)),n.Ib(122,4440064,[[15,4],["amountCheck",4]],0,c.eb,[n.r,c.i],{id:[0,"id"],selected:[1,"selected"]},null),(l()(),n.Jb(123,0,null,0,3,"GridItem",[["width","250"]],null,null,null,g.Ac,g.I)),n.Ib(124,4440064,null,0,c.A,[n.r,c.i],{width:[0,"width"]},null),(l()(),n.Jb(125,0,null,0,1,"SwtLabel",[],null,null,null,g.Yc,g.fb)),n.Ib(126,4440064,[[27,4],["amountLbl",4]],0,c.vb,[n.r,c.i],null,null),(l()(),n.Jb(127,0,null,0,9,"GridRow",[],null,null,null,g.Bc,g.J)),n.Ib(128,4440064,null,0,c.B,[n.r,c.i],null,null),(l()(),n.Jb(129,0,null,0,3,"GridItem",[["width","30"]],null,null,null,g.Ac,g.I)),n.Ib(130,4440064,null,0,c.A,[n.r,c.i],{width:[0,"width"]},null),(l()(),n.Jb(131,0,null,0,1,"SwtCheckBox",[["id","signCheck"],["selected","false"]],null,null,null,g.Oc,g.V)),n.Ib(132,4440064,[[16,4],["signCheck",4]],0,c.eb,[n.r,c.i],{id:[0,"id"],selected:[1,"selected"]},null),(l()(),n.Jb(133,0,null,0,3,"GridItem",[["width","250"]],null,null,null,g.Ac,g.I)),n.Ib(134,4440064,null,0,c.A,[n.r,c.i],{width:[0,"width"]},null),(l()(),n.Jb(135,0,null,0,1,"SwtLabel",[],null,null,null,g.Yc,g.fb)),n.Ib(136,4440064,[[28,4],["signLbl",4]],0,c.vb,[n.r,c.i],null,null),(l()(),n.Jb(137,0,null,0,53,"HBox",[["height","100%"],["horizontalAlign","right"],["width","50%"]],null,null,null,g.Dc,g.K)),n.Ib(138,4440064,null,0,c.C,[n.r,c.i],{horizontalAlign:[0,"horizontalAlign"],width:[1,"width"],height:[2,"height"]},null),(l()(),n.Jb(139,0,null,0,51,"Grid",[["paddingLeft","10"],["paddingTop","10"]],null,null,null,g.Cc,g.H)),n.Ib(140,4440064,null,0,c.z,[n.r,c.i],{paddingTop:[0,"paddingTop"],paddingLeft:[1,"paddingLeft"]},null),(l()(),n.Jb(141,0,null,0,9,"GridRow",[],null,null,null,g.Bc,g.J)),n.Ib(142,4440064,null,0,c.B,[n.r,c.i],null,null),(l()(),n.Jb(143,0,null,0,3,"GridItem",[["width","30"]],null,null,null,g.Ac,g.I)),n.Ib(144,4440064,null,0,c.A,[n.r,c.i],{width:[0,"width"]},null),(l()(),n.Jb(145,0,null,0,1,"SwtCheckBox",[["id","mvtCheck"],["selected","false"]],null,null,null,g.Oc,g.V)),n.Ib(146,4440064,[[17,4],["mvtCheck",4]],0,c.eb,[n.r,c.i],{id:[0,"id"],selected:[1,"selected"]},null),(l()(),n.Jb(147,0,null,0,3,"GridItem",[["width","250"]],null,null,null,g.Ac,g.I)),n.Ib(148,4440064,null,0,c.A,[n.r,c.i],{width:[0,"width"]},null),(l()(),n.Jb(149,0,null,0,1,"SwtLabel",[],null,null,null,g.Yc,g.fb)),n.Ib(150,4440064,[[29,4],["mvtLbl",4]],0,c.vb,[n.r,c.i],null,null),(l()(),n.Jb(151,0,null,0,9,"GridRow",[],null,null,null,g.Bc,g.J)),n.Ib(152,4440064,null,0,c.B,[n.r,c.i],null,null),(l()(),n.Jb(153,0,null,0,3,"GridItem",[["width","30"]],null,null,null,g.Ac,g.I)),n.Ib(154,4440064,null,0,c.A,[n.r,c.i],{width:[0,"width"]},null),(l()(),n.Jb(155,0,null,0,1,"SwtCheckBox",[["id","matchCheck"],["selected","false"]],null,null,null,g.Oc,g.V)),n.Ib(156,4440064,[[18,4],["matchCheck",4]],0,c.eb,[n.r,c.i],{id:[0,"id"],selected:[1,"selected"]},null),(l()(),n.Jb(157,0,null,0,3,"GridItem",[["width","250"]],null,null,null,g.Ac,g.I)),n.Ib(158,4440064,null,0,c.A,[n.r,c.i],{width:[0,"width"]},null),(l()(),n.Jb(159,0,null,0,1,"SwtLabel",[],null,null,null,g.Yc,g.fb)),n.Ib(160,4440064,[[30,4],["matchLbl",4]],0,c.vb,[n.r,c.i],null,null),(l()(),n.Jb(161,0,null,0,9,"GridRow",[],null,null,null,g.Bc,g.J)),n.Ib(162,4440064,null,0,c.B,[n.r,c.i],null,null),(l()(),n.Jb(163,0,null,0,3,"GridItem",[["width","30"]],null,null,null,g.Ac,g.I)),n.Ib(164,4440064,null,0,c.A,[n.r,c.i],{width:[0,"width"]},null),(l()(),n.Jb(165,0,null,0,1,"SwtCheckBox",[["id","sweepCheck"],["selected","false"]],null,null,null,g.Oc,g.V)),n.Ib(166,4440064,[[19,4],["sweepCheck",4]],0,c.eb,[n.r,c.i],{id:[0,"id"],selected:[1,"selected"]},null),(l()(),n.Jb(167,0,null,0,3,"GridItem",[["width","250"]],null,null,null,g.Ac,g.I)),n.Ib(168,4440064,null,0,c.A,[n.r,c.i],{width:[0,"width"]},null),(l()(),n.Jb(169,0,null,0,1,"SwtLabel",[],null,null,null,g.Yc,g.fb)),n.Ib(170,4440064,[[31,4],["sweepLbl",4]],0,c.vb,[n.r,c.i],null,null),(l()(),n.Jb(171,0,null,0,9,"GridRow",[["paddingBottom","40"]],null,null,null,g.Bc,g.J)),n.Ib(172,4440064,null,0,c.B,[n.r,c.i],{paddingBottom:[0,"paddingBottom"]},null),(l()(),n.Jb(173,0,null,0,3,"GridItem",[["width","30"]],null,null,null,g.Ac,g.I)),n.Ib(174,4440064,null,0,c.A,[n.r,c.i],{width:[0,"width"]},null),(l()(),n.Jb(175,0,null,0,1,"SwtCheckBox",[["id","payCheck"],["selected","false"]],null,null,null,g.Oc,g.V)),n.Ib(176,4440064,[[20,4],["payCheck",4]],0,c.eb,[n.r,c.i],{id:[0,"id"],selected:[1,"selected"]},null),(l()(),n.Jb(177,0,null,0,3,"GridItem",[["width","250"]],null,null,null,g.Ac,g.I)),n.Ib(178,4440064,null,0,c.A,[n.r,c.i],{width:[0,"width"]},null),(l()(),n.Jb(179,0,null,0,1,"SwtLabel",[],null,null,null,g.Yc,g.fb)),n.Ib(180,4440064,[[32,4],["payLbl",4]],0,c.vb,[n.r,c.i],null,null),(l()(),n.Jb(181,0,null,0,9,"GridRow",[],null,null,null,g.Bc,g.J)),n.Ib(182,4440064,null,0,c.B,[n.r,c.i],null,null),(l()(),n.Jb(183,0,null,0,3,"GridItem",[["width","30"]],null,null,null,g.Ac,g.I)),n.Ib(184,4440064,null,0,c.A,[n.r,c.i],{width:[0,"width"]},null),(l()(),n.Jb(185,0,null,0,1,"SwtCheckBox",[["id","allCheck"],["selected","false"]],null,[[null,"click"]],function(l,e,t){var n=!0,i=l.component;"click"===e&&(n=!1!==i.checkAll()&&n);return n},g.Oc,g.V)),n.Ib(186,4440064,[[21,4],["allCheck",4]],0,c.eb,[n.r,c.i],{id:[0,"id"],selected:[1,"selected"]},{onClick_:"click"}),(l()(),n.Jb(187,0,null,0,3,"GridItem",[["width","300"]],null,null,null,g.Ac,g.I)),n.Ib(188,4440064,null,0,c.A,[n.r,c.i],{width:[0,"width"]},null),(l()(),n.Jb(189,0,null,0,1,"SwtLabel",[],null,null,null,g.Yc,g.fb)),n.Ib(190,4440064,[[33,4],["allLbl",4]],0,c.vb,[n.r,c.i],null,null),(l()(),n.Jb(191,0,null,0,7,"SwtCanvas",[["height","10%"],["paddingTop","8"],["width","100%"]],null,null,null,g.Nc,g.U)),n.Ib(192,4440064,null,0,c.db,[n.r,c.i],{width:[0,"width"],height:[1,"height"],paddingTop:[2,"paddingTop"]},null),(l()(),n.Jb(193,0,null,0,5,"HBox",[["horizontalGap","5"],["paddingLeft","10"],["width","100%"]],null,null,null,g.Dc,g.K)),n.Ib(194,4440064,null,0,c.C,[n.r,c.i],{horizontalGap:[0,"horizontalGap"],width:[1,"width"],paddingLeft:[2,"paddingLeft"]},null),(l()(),n.Jb(195,0,null,0,1,"SwtButton",[["enabled","true"]],null,[[null,"click"]],function(l,e,t){var n=!0,i=l.component;"click"===e&&(n=!1!==i.saveMandatoryAttributes()&&n);return n},g.Mc,g.T)),n.Ib(196,4440064,[[7,4],["okButton",4]],0,c.cb,[n.r,c.i],{enabled:[0,"enabled"]},{onClick_:"click"}),(l()(),n.Jb(197,0,null,0,1,"SwtButton",[["enabled","true"]],null,[[null,"click"]],function(l,e,t){var n=!0,i=l.component;"click"===e&&(n=!1!==i.cancelHandler()&&n);return n},g.Mc,g.T)),n.Ib(198,4440064,[[8,4],["cancelButton",4]],0,c.cb,[n.r,c.i],{enabled:[0,"enabled"]},{onClick_:"click"})],function(l,e){l(e,34,0,"100%","100%");l(e,36,0,"100%","100%","5","5","5","5");l(e,38,0,"100%","20%","10");l(e,40,0,"23");l(e,42,0,"120"),l(e,44,0),l(e,46,0);l(e,48,0,"200","false");l(e,50,0,"23");l(e,52,0,"120"),l(e,54,0),l(e,56,0);l(e,58,0,"200","apiTypeCombo");l(e,60,0,"fieldSet");l(e,62,0,"100%","100%");l(e,64,0,"50%","100%");l(e,66,0,"100%","100%","10","10"),l(e,68,0);l(e,70,0,"30");l(e,72,0,"hostCheck","false");l(e,74,0,"250"),l(e,76,0),l(e,78,0);l(e,80,0,"30");l(e,82,0,"entityCheck","false");l(e,84,0,"250"),l(e,86,0),l(e,88,0);l(e,90,0,"30");l(e,92,0,"ccyCheck","false");l(e,94,0,"250"),l(e,96,0),l(e,98,0);l(e,100,0,"30");l(e,102,0,"accountCheck","false");l(e,104,0,"250"),l(e,106,0),l(e,108,0);l(e,110,0,"30");l(e,112,0,"valDateCheck","false");l(e,114,0,"250"),l(e,116,0),l(e,118,0);l(e,120,0,"30");l(e,122,0,"amountCheck","false");l(e,124,0,"250"),l(e,126,0),l(e,128,0);l(e,130,0,"30");l(e,132,0,"signCheck","false");l(e,134,0,"250"),l(e,136,0);l(e,138,0,"right","50%","100%");l(e,140,0,"10","10"),l(e,142,0);l(e,144,0,"30");l(e,146,0,"mvtCheck","false");l(e,148,0,"250"),l(e,150,0),l(e,152,0);l(e,154,0,"30");l(e,156,0,"matchCheck","false");l(e,158,0,"250"),l(e,160,0),l(e,162,0);l(e,164,0,"30");l(e,166,0,"sweepCheck","false");l(e,168,0,"250"),l(e,170,0);l(e,172,0,"40");l(e,174,0,"30");l(e,176,0,"payCheck","false");l(e,178,0,"250"),l(e,180,0),l(e,182,0);l(e,184,0,"30");l(e,186,0,"allCheck","false");l(e,188,0,"300"),l(e,190,0);l(e,192,0,"100%","10%","8");l(e,194,0,"5","100%","10");l(e,196,0,"true");l(e,198,0,"true")},null)}function H(l){return n.dc(0,[(l()(),n.Jb(0,0,null,null,1,"app-configure-params",[],null,null,null,V,z)),n.Ib(1,4440064,null,0,s,[c.i,n.r],null,null)],function(l,e){l(e,1,0)},null)}var E=n.Fb("app-configure-params",s,H,{maxChars:"maxChars",restrict:"restrict",id:"id",dropShadowEnabled:"dropShadowEnabled",cornerRadius:"cornerRadius",borderThickness:"borderThickness",borderStyle:"borderStyle",borderColor:"borderColor",backGroundColor:"backGroundColor",right:"right",left:"left",bottom:"bottom",top:"top",horizontalGap:"horizontalGap",verticalGap:"verticalGap",textAlign:"textAlign",toolTip:"toolTip",toolTipPreviousValue:"toolTipPreviousValue",textDictionaryId:"tooltipDictionaryId",name:"name",styleName:"styleName",horizontalAlign:"horizontalAlign",verticalAlign:"verticalAlign",width:"width",showScrollBar:"showScrollBar",height:"height",minHeight:"minHeight",minWidth:"minWidth",maxHeight:"maxHeight",maxWidth:"maxWidth",includeInLayout:"includeInLayout",visible:"visible",enabled:"enabled",paddingTop:"paddingTop",paddingBottom:"paddingBottom",paddingLeft:"paddingLeft",paddingRight:"paddingRight",marginTop:"marginTop",marginBottom:"marginBottom",marginLeft:"marginLeft",marginRight:"marginRight",contextMenu:"contextMenu"},{onClick_:"click",dbClick_:"dbClick",doubleClick_:"doubleClick",itemDoubleClick_:"itemDoubleClick",onKeyDown_:"keyDown",onKeyUp_:"keyUp",mouseUp_:"mouseUp",mouseOver_:"mouseOver",mouseDown_:"mouseDown",mouseEnter_:"mouseEnter",mouseLeave_:"mouseLeave",mouseOut_:"mouseOut",mouseIn_:"mouseIn",mouseMove_:"mouseMove",focus_:"focus",focusIn_:"focusIn",onFocusOut_:"focusOut",keyFocusChange_:"keyFocusChange",change_:"change",onSpyChange:"onSpyChange",onSpyNoChange:"onSpyNoChange",scroll_:"scroll",creationComplete:"creationComplete",preinitialize:"preinitialize"},[])}}]);