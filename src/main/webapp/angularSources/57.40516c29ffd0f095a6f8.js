(window.webpackJsonp=window.webpackJsonp||[]).push([[57],{"SN+q":function(t,e,n){"use strict";n.r(e);var o=n("CcnG"),i=n("mrSG"),l=n("ZYCi"),a=n("447K"),s=n("EVdn"),c=n("xRo1"),d=function(t){function e(e,n){var o=t.call(this,n,e)||this;return o.commonService=e,o.element=n,o.jsonReader=new a.L,o.lastRecievedJSON=null,o._menuAccessId=null,o._isPublic=null,o.comboOpen=!1,o._screenName="Connection Pool Stats Monitor",o._versionNumber="1.0.0",o._inputData=new a.G(o.commonService),o._baseURL=a.Wb.getBaseURL(),o._actionMethod=null,o._actionPath=null,o._requestParams=new Array,o._closeWindow=!1,o.connectionModuleId=null,o.moduleId=null,o.connectionPoolId=null,o.parentMethodName=null,o.previousSelectedIndex=-1,o._connectionPoolStatsGrid=null,o.logger=new a.R("Connection Pool Stats Monitor",o.commonService.httpclient),o.swtAlert=new a.bb(e),o}return i.d(e,t),e.prototype.ngOnDestroy=function(){instanceElement=null},e.prototype.ngOnInit=function(){instanceElement=this,this.viewButton.label=a.Wb.getPredictMessage("button.view",null),this.killButton.label=a.Wb.getPredictMessage("button.kill",null),this.refreshButton.label=a.Wb.getPredictMessage("button.refresh",null),this.closeButton.label=a.Wb.getPredictMessage("button.close",null),this.lastRef.text=a.Wb.getPredictMessage("screen.lastRefresh",null)},e.prototype.onLoad=function(){var t=this;this._connectionPoolStatsGrid=this.cvGridContainer.addChild(a.hb);try{this._menuAccessId=a.x.call("eval","menuAccessId"),this._connectionPoolStatsGrid.onRowClick=function(e){t.cellLogic(e)},this._connectionPoolStatsGrid.onRowDoubleClick=function(e){t.viewDetails()},this.connectGridEvents(),"0"!=this._menuAccessId&&(this.viewButton.enabled=!1,this.killButton.enabled=!1),this._inputData.cbStart=this.startOfComms.bind(this),this._inputData.cbStop=this.endOfComms.bind(this),this._inputData.cbResult=function(e){t.inputDataResult(e)},this._inputData.cbFault=this.inputDataFault.bind(this),this._inputData.encodeURL=!1,this._actionPath="connectionPool.do?method=",this._actionMethod="displayConnectionPoolList",this._inputData.url=this._baseURL+this._actionPath+this._actionMethod,this._inputData.send(this._requestParams)}catch(e){this.logger.error("Method [onLoad]",e)}},e.prototype.inputDataResult=function(t){var e=this;try{if(this.lastRecievedJSON=t,this.jsonReader.setInputJSON(this.lastRecievedJSON),this.jsonReader.getRequestReplyStatus()){if(!this.jsonReader.isDataBuilding()){0==this._connectionPoolStatsGrid.columnDefinitions.length&&(this._connectionPoolStatsGrid.allowMultipleSelection=!0,this._connectionPoolStatsGrid.doubleClickEnabled=!0,this._connectionPoolStatsGrid.CustomGrid(this.lastRecievedJSON.connectionPoolMonitor.grid.metadata)),this._connectionPoolStatsGrid.CustomGrid(this.lastRecievedJSON.connectionPoolMonitor.grid.metadata),this._connectionPoolStatsGrid.gridData=this.jsonReader.getGridData(),this._connectionPoolStatsGrid.rowColorFunction=function(t,n,o){return e.drawRowBackground(t,n,o)},this._connectionPoolStatsGrid.setRowSize=this._connectionPoolStatsGrid.gridData.length,this.moduleCombo.setComboData(this.jsonReader.getSelects(),!1),this.activeStatsLabel.text=this.jsonReader.getSingletons().activeStats,this.freshViewsData=this.jsonReader.getSingletons().freshViewsData,this.idleStatsLabel.text=this.jsonReader.getSingletons().idleStats,this._menuAccessId=this.jsonReader.getScreenAttributes().menuaccess,"0"==this._menuAccessId?this.killButton.enabled=!0:this.killButton.enabled=!1,-1!=this.previousSelectedIndex&&this._connectionPoolStatsGrid.gridData.length>=this.previousSelectedIndex+1&&(this._connectionPoolStatsGrid.selectedIndex=this.previousSelectedIndex);var n=this.jsonReader.getScreenAttributes().lastRefTime;this.lastRefTime.text=n.replace(/\\u0028/g,"(").replace(/\\u0029/g,")"),this.cellLogic(null),""+this.freshViewsData=="false"&&this.swtAlert.error(a.Wb.getPredictMessage("connectionPool.alertDBConnectionErr",null))}}else"NO_ENOUGH_GRANTS"==this.jsonReader.getRequestReplyMessage()?this.swtAlert.error(a.Wb.getPredictMessage("connectionPool.alertDBGrantsNeeded",null)):this.swtAlert.show(this.jsonReader.getRequestReplyMessage()+"\n"+this.jsonReader.getRequestReplyLocation(),"Error",a.c.OK,this,this.errorHandler.bind(this));this._connectionPoolStatsGrid.validateNow()}catch(o){console.log("e",o),this.logger.error("Method [inputDataResult]",o),this.swtAlert.show(a.x.call("eval","label['alert']['server_error']"))}},e.prototype.drawRowBackground=function(t,e,n){var o;try{t&&(o="RED"==t.slickgrid_rowcontent.bgColor?"#FA8C8C":"GREY"==t.slickgrid_rowcontent.bgColor?"grey":"transparent")}catch(i){console.log("error drawRowBackground ",i)}return o},e.prototype.errorHandler=function(t){t.detail==a.c.OK&&this._closeWindow&&(this._closeWindow=!1)},e.prototype.inputDataFault=function(t){this._invalidComms=t.fault.faultString+"\n"+t.fault.faultCode+"\n"+t.fault.faultDetail,this.swtAlert.show("fault "+this._invalidComms)},e.prototype.startOfComms=function(){this.loadingImage.visible=!0},e.prototype.endOfComms=function(){this.loadingImage.visible=!1},e.prototype.cellLogic=function(t){if(this._connectionPoolStatsGrid.selectedIndices.length<1)this.connectionModuleId=null,this.connectionPoolId=null;else if(1==this._connectionPoolStatsGrid.selectedIndices.length){var e=this._connectionPoolStatsGrid.selectedIndex;this.connectionModuleId=this._connectionPoolStatsGrid.dataProvider[e].connectionModuleId,this.connectionPoolId=this._connectionPoolStatsGrid.dataProvider[e].connectionPoolId}else this.connectionModuleId=null,this.connectionPoolId=null;this.disableOrEnableButtons(this._connectionPoolStatsGrid.selectedIndices.length)},e.prototype.disableOrEnableButtons=function(t){var e=parseInt(this._menuAccessId,10);1==t?(this.killButton.enabled=0==e,this.viewButton.enabled=e<2):t>1?(this.killButton.enabled=0==e,this.viewButton.enabled=!1):(this.killButton.enabled=!1,this.viewButton.enabled=!1)},e.prototype.doHelp=function(){try{a.x.call("help")}catch(t){a.Wb.logError(t,this.moduleId,"CategoryMaintenance","doHelp",10)}},e.prototype.killConnection=function(){var t=this;try{this._requestParams=[],this._requestParams.connectionIds=a.L.jsonpath(this._connectionPoolStatsGrid.selectedItems,"$.*.id.content").toString(),this._requestParams.moduleId=this.moduleCombo.selectedValue,this._actionMethod="method=killConnectionPool",this._actionPath="connectionPool.do?",this._inputData.url=this._baseURL+this._actionPath+this._actionMethod,this._inputData.cbResult=function(e){t.inputDataResult(e)},this._inputData.send(this._requestParams)}catch(e){a.Wb.logError(e,this.moduleId,"killConnection","doHelp",10)}},e.prototype.doKillConnectionEventHandler=function(t){var e=a.Wb.getPredictMessage("connectionPool.alertKillingConsequences",null);this.swtAlert.confirm(e,null,a.c.YES|a.c.NO,null,this.checkConnectionStatusChanged.bind(this))},e.prototype.checkConnectionStatusChanged=function(t){var e=this;t.detail===a.c.YES&&(1==this._connectionPoolStatsGrid.selectedItems.length?(this._requestParams=[],this._actionMethod="method=checkConnectionChanged",this._actionPath="connectionPool.do?",this._requestParams.connectionId=this._connectionPoolStatsGrid.selectedItem.id.content,this._requestParams.moduleId=this.moduleCombo.selectedValue,this._requestParams.connectionId=this._connectionPoolStatsGrid.selectedItem.id.content,this._requestParams.sqlId=this._connectionPoolStatsGrid.selectedItem.sqlId.content,this._requestParams.duration=this._connectionPoolStatsGrid.selectedItem.duration.content,this._requestParams.sqlStatement=this._connectionPoolStatsGrid.selectedItem.sqlStatement.content,this._requestParams.sqlStatus=this._connectionPoolStatsGrid.selectedItem.sqlStatus.content,this._requestParams.moduleId=this._connectionPoolStatsGrid.selectedItem.module.content,this._requestParams.audSid=this._connectionPoolStatsGrid.selectedItem.audSid.content,this._requestParams.stackTrace=this._connectionPoolStatsGrid.selectedItem.stackTrace.content,this._requestParams.ssid=this._connectionPoolStatsGrid.selectedItem.SSID.content,this._requestParams.lastActionTime=this._connectionPoolStatsGrid.selectedItem.lastActionTime.content,this._requestParams.status=this._connectionPoolStatsGrid.selectedItem.status.content,this._inputData.url=this._baseURL+this._actionPath+this._actionMethod,this._inputData.cbResult=function(t){e.checkConnectionChangedDataResult(t)},this._inputData.send(this._requestParams)):this.killConnection())},e.prototype.checkConnectionChangedDataResult=function(t){try{if(this.lastRecievedJSON=t,this.jsonReader.setInputJSON(this.lastRecievedJSON),this.jsonReader.getRequestReplyStatus())this.killConnection();else if("CONNECTION_NOT_EXIST"==this.jsonReader.getRequestReplyMessage()){var e=a.Wb.getPredictMessage("connectionPool.alertConnectionKilled",null);this.swtAlert.confirm(e,null,a.c.YES,null,this.refresh.bind(this))}else"CONNECTION_DETAILS_CHANGED"==this.jsonReader.getRequestReplyMessage()&&this.swtAlert.confirm(a.Wb.getPredictMessage("connectionPool.alertDetailsChanged",null),null,a.c.YES|a.c.NO,null,this.connectionChangeAlertHandler.bind(this))}catch(n){this.logger.error("Method [inputDataResult]",n),this.swtAlert.show(a.x.call("eval","label['alert']['server_error']"))}},e.prototype.connectionChangeAlertHandler=function(t){t.detail===a.c.YES?this.killConnection():this.refresh()},e.prototype.viewDetails=function(){try{a.x.call("openChildWindow")}catch(t){a.Wb.logError(t,this.moduleId,"viewDetails","doHelp",10)}},e.prototype.refresh=function(){var t=this;try{this._requestParams=[],this._requestParams.moduleId=this.moduleCombo.selectedValue,this._actionMethod="method=displayConnectionPoolList",this._actionPath="connectionPool.do?",this._inputData.url=this._baseURL+this._actionPath+this._actionMethod,this._inputData.cbResult=function(e){t.inputDataResult(e)},this._inputData.send(this._requestParams)}catch(e){a.Wb.logError(e,this.moduleId,"refresh","doHelp",10)}},e.prototype.getParamsFromParent=function(){return[{connectionId:this._connectionPoolStatsGrid.selectedItem.id.content,menuAccessId:this._menuAccessId,sqlId:this._connectionPoolStatsGrid.selectedItem.sqlId.content,duration:this._connectionPoolStatsGrid.selectedItem.duration.content,sqlStatement:this._connectionPoolStatsGrid.selectedItem.sqlStatement.content,sqlStatus:this._connectionPoolStatsGrid.selectedItem.sqlStatus.content,moduleId:this._connectionPoolStatsGrid.selectedItem.module.content,audSid:this._connectionPoolStatsGrid.selectedItem.audSid.content,stackTrace:this._connectionPoolStatsGrid.selectedItem.stackTrace.content,ssid:this._connectionPoolStatsGrid.selectedItem.SSID.content,lastActionTime:this._connectionPoolStatsGrid.selectedItem.lastActionTime.content,status:this._connectionPoolStatsGrid.selectedItem.status.content,sqlExecStart:this._connectionPoolStatsGrid.selectedItem.sqlExecStart.content}]},e.prototype.convertArrayToXML=function(t,e,n,o){var i,l=n?"<"+n+">":"";try{for(var a={compact:!0,ignoreComment:!0,spaces:4},s=0;s<t.length;s++)if(delete t[s].id,delete t[s].content,delete t[s].contains,delete t[s].remove,o){t[s]._attributes={};for(var d=0;d<o.length;d++){var r=o[d];"dataelement"==r&&"id"==t[s][r]?t[s]._attributes[r]="_id":t[s]._attributes[r]=t[s][r],delete t[s][r]}l+=c.js2xml(((i={})[e]=t[s],i),a)}else{if(t[s].slickgrid_rowcontent)for(var u in t[s])if(t[s].slickgrid_rowcontent[u]){var h=t[s].slickgrid_rowcontent[u];delete h.contains,delete h.remove,delete h.content;var b=t[s][u];for(var p in delete t[s][u],t[s][u]=new Object,t[s][u]._text=b,t[s][u]._attributes={},h)"content"!=p&&"contains"!=p&&"remove"!=p&&(t[s][u]._attributes[p]=h[p])}delete t[s].id,delete t[s].content,delete t[s].remove,delete t[s].contains,delete t[s].slickgrid_rowcontent,l+="<"+e+">"+c.js2xml(t[s],a)+"</"+e+">"}l+=n?"</"+n+">":""}catch(g){console.log("e",g)}return l},e.prototype.convertData=function(t,e,n,o,i,l){void 0===i&&(i="pdf"),void 0===l&&(l=!1);var c="<data>",d="",r=JSON.parse(JSON.stringify(t.column)),u=s.extend(!0,[],e.getFilteredItems()),h=this.convertArrayToXML(u,"row","rows"),b=this.convertArrayToXML(r,"column","columns",["filterable","dataelement","draggable","heading","width","type","holiday","visible"]);if(c+=this.removeLineBreaks(b),c=(c=(c=(c+=this.removeLineBreaks(h)).split("\\").join("BACKSLASH_REPLACE")).split("%").join("PERCENTAGE_REPLACE")).split("+").join("PLUSSYMBOL_REPLACE"),null!=n&&l){var p=this.convertArrayToXML(s.extend(!0,[],n),"total");d=this.removeLineBreaks(p)}c+=d;var g="<filters>";if(null!=o)for(var m=0;m<o.length;m++)g+='<filter id="'+o[m].split("=")[0]+'">'+o[m].split("=")[1]+"</filter>";if(e.isFiltered);c+=g+="</filters>",c+="</data>",a.x.call("exportDataExcel",i,c)},e.prototype.removeLineBreaks=function(t){var e,n="";e=t.split("\n");for(var o=0;o<e.length;o++)n+=e[o];return n},e.prototype.report=function(t){this.convertData(this.lastRecievedJSON.connectionPoolMonitor.grid.metadata.columns,this._connectionPoolStatsGrid,null,null,t,!1)},e.prototype.popupClosed=function(){window.close()},e.prototype.connectGridEvents=function(){var t=this;this._connectionPoolStatsGrid.onSortChanged=function(e){t.onGridSortChanged(e)}},e.prototype.onGridSortChanged=function(t){var e=this._connectionPoolStatsGrid.sortedGridColumnId;console.log("\ud83d\ude80 ~ ConnectionPoolStats ~ onGridSortChanged ~ sortedColumnId:",e);var n=this._connectionPoolStatsGrid.sortDirection;console.log("\ud83d\ude80 ~ ConnectionPoolStats ~ onGridSortChanged ~ sortDirection:",n);var o=this._connectionPoolStatsGrid.columnDefinitions,i=this._connectionPoolStatsGrid.gridData;this.applySort(e,n,o,i),this._connectionPoolStatsGrid.refresh()},e.prototype.applySort=function(t,e,n,o){if(console.log("\ud83d\ude80 ~ ConnectionPoolStats ~ applySort ~ applySort:"),t){var i=t;console.log("\ud83d\ude80 ~ ConnectionPoolStats ~ applySort ~ colId:",i);var l=e;console.log("\ud83d\ude80 ~ ConnectionPoolStats ~ applySort ~ direction:",l);var a=n.find(function(t){return t.dataelement===i});console.log("\ud83d\ude80 ~ ConnectionPoolStats ~ applySort ~ columnDef:",a);var s=a?a.type:"str";console.log("\ud83d\ude80 ~ ConnectionPoolStats ~ applySort ~ colType:",s),o.sort(function(t,e){var n=t[i]&&void 0!==t[i].content?t[i].content:"",o=e[i]&&void 0!==e[i].content?e[i].content:"",a=null==n||""===n,c=null==o||""===o;if(a&&c)return 0;if(a)return 1;if(c)return-1;if("num"===s){var d=parseFloat(n),r=parseFloat(o);return isNaN(d)&&isNaN(r)?0:isNaN(d)?1:isNaN(r)?-1:l?d-r:r-d}if("date"===s){var u=function(t){if(!t)return new Date(0);var e=t.split(" "),n=e[0].split("/");if(3===n.length){var o=parseInt(n[0],10),i=parseInt(n[1],10)-1,l=parseInt(n[2],10),a=0,s=0,c=0;if(e[1]){var d=e[1].split(":");a=parseInt(d[0],10)||0,s=parseInt(d[1],10)||0,c=parseInt(d[2],10)||0}return new Date(l,i,o,a,s,c)}return new Date(t)},h=u(n),b=u(o);return l?h.getTime()-b.getTime():b.getTime()-h.getTime()}return l?n.localeCompare(o):o.localeCompare(n)})}},e}(a.yb),r=[{path:"",component:d}],u=(l.l.forChild(r),function(){return function(){}}()),h=n("pMnS"),b=n("RChO"),p=n("t6HQ"),g=n("WFGK"),m=n("5FqG"),f=n("Ip0R"),S=n("gIcY"),_=n("t/Na"),w=n("sE5F"),R=n("OzfB"),v=n("T7CS"),P=n("S7LP"),I=n("6aHO"),C=n("WzUx"),y=n("A7o+"),L=n("zCE2"),k=n("Jg5P"),D=n("3R0m"),B=n("hhbb"),G=n("5rxC"),N=n("Fzqc"),x=n("21Lb"),T=n("hUWP"),M=n("3pJQ"),q=n("V9q+"),A=n("VDKW"),J=n("kXfT"),E=n("BGbe");n.d(e,"ConnectionPoolStatsModuleNgFactory",function(){return O}),n.d(e,"RenderType_ConnectionPoolStats",function(){return j}),n.d(e,"View_ConnectionPoolStats_0",function(){return W}),n.d(e,"View_ConnectionPoolStats_Host_0",function(){return F}),n.d(e,"ConnectionPoolStatsNgFactory",function(){return K});var O=o.Gb(u,[],function(t){return o.Qb([o.Rb(512,o.n,o.vb,[[8,[h.a,b.a,p.a,g.a,m.Cb,m.Pb,m.r,m.rc,m.s,m.Ab,m.Bb,m.Db,m.qd,m.Hb,m.k,m.Ib,m.Nb,m.Ub,m.yb,m.Jb,m.v,m.A,m.e,m.c,m.g,m.d,m.Kb,m.f,m.ec,m.Wb,m.bc,m.ac,m.sc,m.fc,m.lc,m.jc,m.Eb,m.Fb,m.mc,m.Lb,m.nc,m.Mb,m.dc,m.Rb,m.b,m.ic,m.Yb,m.Sb,m.kc,m.y,m.Qb,m.cc,m.hc,m.pc,m.oc,m.xb,m.p,m.q,m.o,m.h,m.j,m.w,m.Zb,m.i,m.m,m.Vb,m.Ob,m.Gb,m.Xb,m.t,m.tc,m.zb,m.n,m.qc,m.a,m.z,m.rd,m.sd,m.x,m.td,m.gc,m.l,m.u,m.ud,m.Tb,K]],[3,o.n],o.J]),o.Rb(4608,f.m,f.l,[o.F,[2,f.u]]),o.Rb(4608,S.c,S.c,[]),o.Rb(4608,S.p,S.p,[]),o.Rb(4608,_.j,_.p,[f.c,o.O,_.n]),o.Rb(4608,_.q,_.q,[_.j,_.o]),o.Rb(5120,_.a,function(t){return[t,new a.tb]},[_.q]),o.Rb(4608,_.m,_.m,[]),o.Rb(6144,_.k,null,[_.m]),o.Rb(4608,_.i,_.i,[_.k]),o.Rb(6144,_.b,null,[_.i]),o.Rb(4608,_.f,_.l,[_.b,o.B]),o.Rb(4608,_.c,_.c,[_.f]),o.Rb(4608,w.c,w.c,[]),o.Rb(4608,w.g,w.b,[]),o.Rb(5120,w.i,w.j,[]),o.Rb(4608,w.h,w.h,[w.c,w.g,w.i]),o.Rb(4608,w.f,w.a,[]),o.Rb(5120,w.d,w.k,[w.h,w.f]),o.Rb(5120,o.b,function(t,e){return[R.j(t,e)]},[f.c,o.O]),o.Rb(4608,v.a,v.a,[]),o.Rb(4608,P.a,P.a,[]),o.Rb(4608,I.a,I.a,[o.n,o.L,o.B,P.a,o.g]),o.Rb(4608,C.c,C.c,[o.n,o.g,o.B]),o.Rb(4608,C.e,C.e,[C.c]),o.Rb(4608,y.l,y.l,[]),o.Rb(4608,y.h,y.g,[]),o.Rb(4608,y.c,y.f,[]),o.Rb(4608,y.j,y.d,[]),o.Rb(4608,y.b,y.a,[]),o.Rb(4608,y.k,y.k,[y.l,y.h,y.c,y.j,y.b,y.m,y.n]),o.Rb(4608,C.i,C.i,[[2,y.k]]),o.Rb(4608,C.r,C.r,[C.L,[2,y.k],C.i]),o.Rb(4608,C.t,C.t,[]),o.Rb(4608,C.w,C.w,[]),o.Rb(1073742336,l.l,l.l,[[2,l.r],[2,l.k]]),o.Rb(1073742336,f.b,f.b,[]),o.Rb(1073742336,S.n,S.n,[]),o.Rb(1073742336,S.l,S.l,[]),o.Rb(1073742336,L.a,L.a,[]),o.Rb(1073742336,k.a,k.a,[]),o.Rb(1073742336,S.e,S.e,[]),o.Rb(1073742336,D.a,D.a,[]),o.Rb(1073742336,y.i,y.i,[]),o.Rb(1073742336,C.b,C.b,[]),o.Rb(1073742336,_.e,_.e,[]),o.Rb(1073742336,_.d,_.d,[]),o.Rb(1073742336,w.e,w.e,[]),o.Rb(1073742336,B.b,B.b,[]),o.Rb(1073742336,G.b,G.b,[]),o.Rb(1073742336,R.c,R.c,[]),o.Rb(1073742336,N.a,N.a,[]),o.Rb(1073742336,x.d,x.d,[]),o.Rb(1073742336,T.c,T.c,[]),o.Rb(1073742336,M.a,M.a,[]),o.Rb(1073742336,q.a,q.a,[[2,R.g],o.O]),o.Rb(1073742336,A.b,A.b,[]),o.Rb(1073742336,J.a,J.a,[]),o.Rb(1073742336,E.b,E.b,[]),o.Rb(1073742336,a.Tb,a.Tb,[]),o.Rb(1073742336,u,u,[]),o.Rb(256,_.n,"XSRF-TOKEN",[]),o.Rb(256,_.o,"X-XSRF-TOKEN",[]),o.Rb(256,"config",{},[]),o.Rb(256,y.m,void 0,[]),o.Rb(256,y.n,void 0,[]),o.Rb(256,"popperDefaults",{},[]),o.Rb(1024,l.i,function(){return[[{path:"",component:d}]]},[])])}),H=[[""]],j=o.Hb({encapsulation:0,styles:H,data:{}});function W(t){return o.dc(0,[o.Zb(402653184,1,{_container:0}),o.Zb(402653184,2,{cvGridContainer:0}),o.Zb(402653184,3,{viewButton:0}),o.Zb(402653184,4,{killButton:0}),o.Zb(402653184,5,{refreshButton:0}),o.Zb(402653184,6,{closeButton:0}),o.Zb(402653184,7,{loadingImage:0}),o.Zb(402653184,8,{activeStatsLabel:0}),o.Zb(402653184,9,{idleStatsLabel:0}),o.Zb(402653184,10,{moduleCombo:0}),o.Zb(402653184,11,{lastRef:0}),o.Zb(402653184,12,{lastRefTime:0}),(t()(),o.Jb(12,0,null,null,71,"SwtModule",[["height","100%"],["width","100%"]],null,[[null,"creationComplete"]],function(t,e,n){var o=!0,i=t.component;"creationComplete"===e&&(o=!1!==i.onLoad()&&o);return o},m.ad,m.hb)),o.Ib(13,4440064,null,0,a.yb,[o.r,a.i],{width:[0,"width"],height:[1,"height"]},{creationComplete:"creationComplete"}),(t()(),o.Jb(14,0,null,0,69,"VBox",[["height","100%"],["id","vbParent"],["paddingBottom","5"],["paddingLeft","5"],["paddingRight","5"],["paddingTop","5"],["width","100%"]],null,null,null,m.od,m.vb)),o.Ib(15,4440064,null,0,a.ec,[o.r,a.i,o.T],{id:[0,"id"],width:[1,"width"],height:[2,"height"],paddingTop:[3,"paddingTop"],paddingBottom:[4,"paddingBottom"],paddingLeft:[5,"paddingLeft"],paddingRight:[6,"paddingRight"]},null),(t()(),o.Jb(16,0,null,0,7,"SwtCanvas",[["height","7%"],["id","attAccContainer"],["width","100%"]],null,null,null,m.Nc,m.U)),o.Ib(17,4440064,null,0,a.db,[o.r,a.i],{id:[0,"id"],width:[1,"width"],height:[2,"height"]},null),(t()(),o.Jb(18,0,null,0,5,"HBox",[["height","100%"],["paddingLeft","5"],["width","100%"]],null,null,null,m.Dc,m.K)),o.Ib(19,4440064,null,0,a.C,[o.r,a.i],{width:[0,"width"],height:[1,"height"],paddingLeft:[2,"paddingLeft"]},null),(t()(),o.Jb(20,0,null,0,1,"SwtLabel",[["paddingTop","3"],["styleName","labelBold"],["textDictionaryId","connectionPool.connectionPool"],["width","120"]],null,null,null,m.Yc,m.fb)),o.Ib(21,4440064,null,0,a.vb,[o.r,a.i],{textDictionaryId:[0,"textDictionaryId"],styleName:[1,"styleName"],width:[2,"width"],paddingTop:[3,"paddingTop"]},null),(t()(),o.Jb(22,0,null,0,1,"SwtComboBox",[["dataLabel","moduleList"],["id","moduleCombo"],["width","120"]],null,[[null,"change"],["window","mousewheel"]],function(t,e,n){var i=!0,l=t.component;"window:mousewheel"===e&&(i=!1!==o.Tb(t,23).mouseWeelEventHandler(n.target)&&i);"change"===e&&(i=!1!==l.refresh()&&i);return i},m.Pc,m.W)),o.Ib(23,4440064,[[10,4],["moduleCombo",4]],0,a.gb,[o.r,a.i],{dataLabel:[0,"dataLabel"],width:[1,"width"],id:[2,"id"]},{change_:"change"}),(t()(),o.Jb(24,0,null,0,27,"SwtCanvas",[["height","85%"],["width","100%"]],null,null,null,m.Nc,m.U)),o.Ib(25,4440064,null,0,a.db,[o.r,a.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),o.Jb(26,0,null,0,25,"VBox",[["height","100%"],["width","100%"]],null,null,null,m.od,m.vb)),o.Ib(27,4440064,null,0,a.ec,[o.r,a.i,o.T],{width:[0,"width"],height:[1,"height"]},null),(t()(),o.Jb(28,0,null,0,3,"HBox",[["paddingLeft","5"],["width","100%"]],null,null,null,m.Dc,m.K)),o.Ib(29,4440064,null,0,a.C,[o.r,a.i],{width:[0,"width"],paddingLeft:[1,"paddingLeft"]},null),(t()(),o.Jb(30,0,null,0,1,"SwtLabel",[["styleName","labelBold"],["textDictionaryId","connectionPool.poolStats"]],null,null,null,m.Yc,m.fb)),o.Ib(31,4440064,null,0,a.vb,[o.r,a.i],{textDictionaryId:[0,"textDictionaryId"],styleName:[1,"styleName"]},null),(t()(),o.Jb(32,0,null,0,5,"HBox",[["width","100%"]],null,null,null,m.Dc,m.K)),o.Ib(33,4440064,null,0,a.C,[o.r,a.i],{width:[0,"width"]},null),(t()(),o.Jb(34,0,null,0,1,"SwtLabel",[["paddingLeft","15"],["styleName","labelBold"],["textDictionaryId","connectionPool.activeConnections"],["width","250"]],null,null,null,m.Yc,m.fb)),o.Ib(35,4440064,null,0,a.vb,[o.r,a.i],{textDictionaryId:[0,"textDictionaryId"],styleName:[1,"styleName"],width:[2,"width"],paddingLeft:[3,"paddingLeft"]},null),(t()(),o.Jb(36,0,null,0,1,"SwtLabel",[["id","activeStatsLabel"],["paddingLeft","20"]],null,null,null,m.Yc,m.fb)),o.Ib(37,4440064,[[8,4],["activeStatsLabel",4]],0,a.vb,[o.r,a.i],{id:[0,"id"],paddingLeft:[1,"paddingLeft"]},null),(t()(),o.Jb(38,0,null,0,5,"HBox",[["width","100%"]],null,null,null,m.Dc,m.K)),o.Ib(39,4440064,null,0,a.C,[o.r,a.i],{width:[0,"width"]},null),(t()(),o.Jb(40,0,null,0,1,"SwtLabel",[["paddingLeft","15"],["styleName","labelBold"],["textDictionaryId","connectionPool.idleConnections"],["width","250"]],null,null,null,m.Yc,m.fb)),o.Ib(41,4440064,null,0,a.vb,[o.r,a.i],{textDictionaryId:[0,"textDictionaryId"],styleName:[1,"styleName"],width:[2,"width"],paddingLeft:[3,"paddingLeft"]},null),(t()(),o.Jb(42,0,null,0,1,"SwtLabel",[["id","idleStatsLabel"],["paddingLeft","20"]],null,null,null,m.Yc,m.fb)),o.Ib(43,4440064,[[9,4],["idleStatsLabel",4]],0,a.vb,[o.r,a.i],{id:[0,"id"],paddingLeft:[1,"paddingLeft"]},null),(t()(),o.Jb(44,0,null,0,3,"HBox",[["paddingLeft","5"],["width","100%"]],null,null,null,m.Dc,m.K)),o.Ib(45,4440064,null,0,a.C,[o.r,a.i],{width:[0,"width"],paddingLeft:[1,"paddingLeft"]},null),(t()(),o.Jb(46,0,null,0,1,"SwtLabel",[["styleName","labelBold"],["textDictionaryId","connectionPool.Connections"]],null,null,null,m.Yc,m.fb)),o.Ib(47,4440064,null,0,a.vb,[o.r,a.i],{textDictionaryId:[0,"textDictionaryId"],styleName:[1,"styleName"]},null),(t()(),o.Jb(48,0,null,0,3,"HBox",[["height","80%"],["paddingLeft","5"],["width","100%"]],null,null,null,m.Dc,m.K)),o.Ib(49,4440064,null,0,a.C,[o.r,a.i],{width:[0,"width"],height:[1,"height"],paddingLeft:[2,"paddingLeft"]},null),(t()(),o.Jb(50,0,null,0,1,"SwtCanvas",[["border","false"],["height","100%"],["id","cvGridContainer"],["width","100%"]],null,null,null,m.Nc,m.U)),o.Ib(51,4440064,[[2,4],["cvGridContainer",4]],0,a.db,[o.r,a.i],{id:[0,"id"],width:[1,"width"],height:[2,"height"],border:[3,"border"]},null),(t()(),o.Jb(52,0,null,0,31,"SwtCanvas",[["height","7%"],["id","swtButtonBar"],["width","100%"]],null,null,null,m.Nc,m.U)),o.Ib(53,4440064,null,0,a.db,[o.r,a.i],{id:[0,"id"],width:[1,"width"],height:[2,"height"]},null),(t()(),o.Jb(54,0,null,0,29,"HBox",[["height","100%"],["width","100%"]],null,null,null,m.Dc,m.K)),o.Ib(55,4440064,null,0,a.C,[o.r,a.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),o.Jb(56,0,null,0,11,"SwtCanvas",[["border","false"],["height","100%"],["paddingTop","5"],["width","100%"]],null,null,null,m.Nc,m.U)),o.Ib(57,4440064,null,0,a.db,[o.r,a.i],{width:[0,"width"],height:[1,"height"],paddingTop:[2,"paddingTop"],border:[3,"border"]},null),(t()(),o.Jb(58,0,null,0,9,"HBox",[["height","100%"],["paddingLeft","11"],["styleName","hgroupLeft"],["width","100%"]],null,null,null,m.Dc,m.K)),o.Ib(59,4440064,null,0,a.C,[o.r,a.i],{styleName:[0,"styleName"],width:[1,"width"],height:[2,"height"],paddingLeft:[3,"paddingLeft"]},null),(t()(),o.Jb(60,0,null,0,1,"SwtButton",[["id","refreshButton"]],null,[[null,"click"]],function(t,e,n){var o=!0,i=t.component;"click"===e&&(o=!1!==i.refresh()&&o);return o},m.Mc,m.T)),o.Ib(61,4440064,[[5,4],["refreshButton",4]],0,a.cb,[o.r,a.i],{id:[0,"id"]},{onClick_:"click"}),(t()(),o.Jb(62,0,null,0,1,"SwtButton",[["id","viewButton"]],null,[[null,"click"]],function(t,e,n){var o=!0,i=t.component;"click"===e&&(o=!1!==i.viewDetails()&&o);return o},m.Mc,m.T)),o.Ib(63,4440064,[[3,4],["viewButton",4]],0,a.cb,[o.r,a.i],{id:[0,"id"]},{onClick_:"click"}),(t()(),o.Jb(64,0,null,0,1,"SwtButton",[["enabled","false"],["id","killButton"]],null,[[null,"click"]],function(t,e,n){var o=!0,i=t.component;"click"===e&&(o=!1!==i.doKillConnectionEventHandler(n)&&o);return o},m.Mc,m.T)),o.Ib(65,4440064,[[4,4],["killButton",4]],0,a.cb,[o.r,a.i],{id:[0,"id"],enabled:[1,"enabled"]},{onClick_:"click"}),(t()(),o.Jb(66,0,null,0,1,"SwtButton",[["id","closeButton"]],null,[[null,"click"]],function(t,e,n){var o=!0,i=t.component;"click"===e&&(o=!1!==i.popupClosed()&&o);return o},m.Mc,m.T)),o.Ib(67,4440064,[[6,4],["closeButton",4]],0,a.cb,[o.r,a.i],{id:[0,"id"]},{onClick_:"click"}),(t()(),o.Jb(68,0,null,0,15,"HBox",[["horizontalAlign","right"],["styleName","hgroupRight"]],null,null,null,m.Dc,m.K)),o.Ib(69,4440064,null,0,a.C,[o.r,a.i],{styleName:[0,"styleName"],horizontalAlign:[1,"horizontalAlign"]},null),(t()(),o.Jb(70,0,null,0,1,"SwtText",[["id","lastRef"],["paddingTop","8"],["width","80"]],null,null,null,m.ld,m.qb)),o.Ib(71,4440064,[[11,4],["lastRef",4]],0,a.Pb,[o.r,a.i],{id:[0,"id"],width:[1,"width"],paddingTop:[2,"paddingTop"]},null),(t()(),o.Jb(72,0,null,0,1,"SwtText",[["id","lastRefTime"],["paddingTop","8"],["width","100"]],null,null,null,m.ld,m.qb)),o.Ib(73,4440064,[[12,4],["lastRefTime",4]],0,a.Pb,[o.r,a.i],{id:[0,"id"],width:[1,"width"],paddingTop:[2,"paddingTop"]},null),(t()(),o.Jb(74,0,null,0,9,"HBox",[["paddingTop","8"]],null,null,null,m.Dc,m.K)),o.Ib(75,4440064,null,0,a.C,[o.r,a.i],{paddingTop:[0,"paddingTop"]},null),(t()(),o.Jb(76,0,null,0,1,"SwtLoadingImage",[],null,null,null,m.Zc,m.gb)),o.Ib(77,114688,[[7,4],["loadingImage",4]],0,a.xb,[o.r],null,null),(t()(),o.Jb(78,0,null,0,1,"SwtHelpButton",[["enabled","true"],["helpFile","spread-profile"],["id","btnHelp"]],null,[[null,"click"]],function(t,e,n){var o=!0,i=t.component;"click"===e&&(o=!1!==i.doHelp()&&o);return o},m.Wc,m.db)),o.Ib(79,4440064,null,0,a.rb,[o.r,a.i],{id:[0,"id"],enabled:[1,"enabled"],buttonMode:[2,"buttonMode"],helpFile:[3,"helpFile"]},{onClick_:"click"}),(t()(),o.Jb(80,0,null,0,1,"SwtButton",[["buttonMode","true"],["enabled","true"],["id","excel"],["styleName","excelIcon"]],null,[[null,"click"]],function(t,e,n){var o=!0,i=t.component;"click"===e&&(o=!1!==i.report("excel")&&o);return o},m.Mc,m.T)),o.Ib(81,4440064,[["excel",4]],0,a.cb,[o.r,a.i],{id:[0,"id"],styleName:[1,"styleName"],enabled:[2,"enabled"],buttonMode:[3,"buttonMode"]},{onClick_:"click"}),(t()(),o.Jb(82,0,null,0,1,"SwtButton",[["buttonMode","true"],["enabled","true"],["id","csv"],["styleName","csvIcon"]],null,[[null,"click"]],function(t,e,n){var o=!0,i=t.component;"click"===e&&(o=!1!==i.report("csv")&&o);return o},m.Mc,m.T)),o.Ib(83,4440064,[["csv",4]],0,a.cb,[o.r,a.i],{id:[0,"id"],styleName:[1,"styleName"],enabled:[2,"enabled"],buttonMode:[3,"buttonMode"]},{onClick_:"click"})],function(t,e){t(e,13,0,"100%","100%");t(e,15,0,"vbParent","100%","100%","5","5","5","5");t(e,17,0,"attAccContainer","100%","7%");t(e,19,0,"100%","100%","5");t(e,21,0,"connectionPool.connectionPool","labelBold","120","3");t(e,23,0,"moduleList","120","moduleCombo");t(e,25,0,"100%","85%");t(e,27,0,"100%","100%");t(e,29,0,"100%","5");t(e,31,0,"connectionPool.poolStats","labelBold");t(e,33,0,"100%");t(e,35,0,"connectionPool.activeConnections","labelBold","250","15");t(e,37,0,"activeStatsLabel","20");t(e,39,0,"100%");t(e,41,0,"connectionPool.idleConnections","labelBold","250","15");t(e,43,0,"idleStatsLabel","20");t(e,45,0,"100%","5");t(e,47,0,"connectionPool.Connections","labelBold");t(e,49,0,"100%","80%","5");t(e,51,0,"cvGridContainer","100%","100%","false");t(e,53,0,"swtButtonBar","100%","7%");t(e,55,0,"100%","100%");t(e,57,0,"100%","100%","5","false");t(e,59,0,"hgroupLeft","100%","100%","11");t(e,61,0,"refreshButton");t(e,63,0,"viewButton");t(e,65,0,"killButton","false");t(e,67,0,"closeButton");t(e,69,0,"hgroupRight","right");t(e,71,0,"lastRef","80","8");t(e,73,0,"lastRefTime","100","8");t(e,75,0,"8"),t(e,77,0);t(e,79,0,"btnHelp","true",!0,"spread-profile");t(e,81,0,"excel","excelIcon","true","true");t(e,83,0,"csv","csvIcon","true","true")},null)}function F(t){return o.dc(0,[(t()(),o.Jb(0,0,null,null,1,"app-connection-pool-stats",[],null,null,null,W,j)),o.Ib(1,4440064,null,0,d,[a.i,o.r],null,null)],function(t,e){t(e,1,0)},null)}var K=o.Fb("app-connection-pool-stats",d,F,{maxChars:"maxChars",restrict:"restrict",id:"id",dropShadowEnabled:"dropShadowEnabled",cornerRadius:"cornerRadius",borderThickness:"borderThickness",borderStyle:"borderStyle",borderColor:"borderColor",backGroundColor:"backGroundColor",right:"right",left:"left",bottom:"bottom",top:"top",horizontalGap:"horizontalGap",verticalGap:"verticalGap",textAlign:"textAlign",toolTip:"toolTip",toolTipPreviousValue:"toolTipPreviousValue",textDictionaryId:"tooltipDictionaryId",name:"name",styleName:"styleName",horizontalAlign:"horizontalAlign",verticalAlign:"verticalAlign",width:"width",showScrollBar:"showScrollBar",height:"height",minHeight:"minHeight",minWidth:"minWidth",maxHeight:"maxHeight",maxWidth:"maxWidth",includeInLayout:"includeInLayout",visible:"visible",enabled:"enabled",paddingTop:"paddingTop",paddingBottom:"paddingBottom",paddingLeft:"paddingLeft",paddingRight:"paddingRight",marginTop:"marginTop",marginBottom:"marginBottom",marginLeft:"marginLeft",marginRight:"marginRight",contextMenu:"contextMenu"},{onClick_:"click",dbClick_:"dbClick",doubleClick_:"doubleClick",itemDoubleClick_:"itemDoubleClick",onKeyDown_:"keyDown",onKeyUp_:"keyUp",mouseUp_:"mouseUp",mouseOver_:"mouseOver",mouseDown_:"mouseDown",mouseEnter_:"mouseEnter",mouseLeave_:"mouseLeave",mouseOut_:"mouseOut",mouseIn_:"mouseIn",mouseMove_:"mouseMove",focus_:"focus",focusIn_:"focusIn",onFocusOut_:"focusOut",keyFocusChange_:"keyFocusChange",change_:"change",onSpyChange:"onSpyChange",onSpyNoChange:"onSpyNoChange",scroll_:"scroll",creationComplete:"creationComplete",preinitialize:"preinitialize"},[])}}]);