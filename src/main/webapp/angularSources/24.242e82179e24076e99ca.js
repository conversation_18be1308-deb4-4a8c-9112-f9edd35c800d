(window.webpackJsonp=window.webpackJsonp||[]).push([[24],{VIur:function(t,e,i){"use strict";i.r(e);var o=i("CcnG"),n=i("mrSG"),l=i("wd/R"),s=i.n(l),a=i("447K"),r=i("MA5k"),h=i("ZYCi"),u=function(t){function e(e,i){var o=t.call(this,i,e)||this;return o.commonService=e,o.element=i,o.logger=null,o.requestParams=[],o.baseURL=a.Wb.getBaseURL(),o.actionMethod=null,o.actionPath=null,o.inputData=new a.G(o.commonService),o.jsonReader=new a.L,o.lastRecievedJSON=null,o.gridJSONList=null,o.originalJSONList=null,o.firstJSONList=null,o.firstGridData=null,o.tabName=null,o.typeId=null,o.like=null,o.notLike=null,o.into=null,o.notIn=null,o.between=null,o.operation="",o.operationToDisplay="",o.and=null,o.or=null,o.inList=null,o.exists=null,o.errorLocation=0,o.countLeftP=1,o.countRightP=1,o.arrayToDisplayQuery=[],o.arrayToExecuteQuery=[],o.queryToExecute="",o.queryToDisplay="",o.andOrString="",o.andOrStringToDisplay="",o.queryToDisplay2="",o.sysdateformat=null,o.nameDays=[],o.nameMonths=[],o.tempNameDays="",o.tempNameMonths="",o.selectedItemsList=null,o.buttonId=null,o.moduleId=null,o.programId=null,o.tableToJoin=[],o.tabNames=null,o.index=0,o.tabAllConditions=[],o.agregationFunc=[],o.varToBind=[],o.sysDateActive=!1,o.aliasList=[],o.currQueryToDisplay="",o.currentSort="",o.currentFilter="",o.currentSortToDisplay="",o.commonSysDateFormat="DD/MM/YYYY",o.screenName=null,o.sortTab="",o.recUnitGroupDisplay="F",o.execStatus=null,o.isValidQuery=!1,o.actionToDo=null,o.invalidComms=null,o.externalFields=null,o.ruleType=null,o.ruleId=null,o.message=null,o.previousCondId=0,o.previousTypeCode="",o.filterTextOfInput="",o.dateTimeArray=[],o.logger=new a.R("Search",o.commonService.httpclient),o.swtAlert=new a.bb(o.commonService),o}return n.d(e,t),e.prototype.unloadHandler=function(t){window.opener.instanceElement.enableButtons()},e.prototype.loadSearchDefaults=function(){try{var t=void 0;window.opener&&window.opener.instanceElement&&(t=window.opener.instanceElement.getParamsFromParent(),this.screenName=t[0].screenName,t[0].tabAllConditions&&t[0].tabAllConditions.length>0&&(this.tabAllConditions=JSON.parse(t[0].tabAllConditions)),t[0].tableToJoin&&t[0].tableToJoin.length>0&&(this.tableToJoin=JSON.parse(t[0].tableToJoin)),t[0].queryToExecute&&(this.queryToExecute=t[0].queryToExecute),t[0].queryToDisplay&&(this.queryToDisplay=t[0].queryToDisplay)),this.screenName||(this.screenName="add"),this.listCriteria=this.customGrid.addChild(a.hb),"change"===this.screenName?(this.addButton.visible=!1,this.addButton.includeInLayout=!1,this.undoButton.visible=this.undoButton.includeInLayout=!1,this.labelQuery.visible=!0,this.leftParentheseButton.visible=this.leftParentheseButton.includeInLayout=!1,this.rightParentheseButton.visible=this.rightParentheseButton.includeInLayout=!1,this.andButton.visible=this.andButton.includeInLayout=!1,this.changeButton.visible=!0,this.orButton.visible=this.orButton.includeInLayout=!1,this.changeCritButton.visible=!0):(this.changeButton.visible=this.changeButton.includeInLayout=!1,this.labelQuery.visible=this.labelQuery.includeInLayout=!1,this.changeCritButton.visible=this.changeCritButton.includeInLayout=!1),"change"===this.screenName&&this.parentDocument&&""!==this.parentDocument.searchQuery&&(this.queryToDisplay=this.parentDocument.queryToDisplay,this.queryToExecute=this.parentDocument.searchQuery,this.title=a.Wb.getCommonMessages("rulesDefinitionScreen.windowtitle.help_screen"),this.message=a.Wb.getCommonMessages("rulesDefinitionScreen.message.help_message")),this.like=a.Wb.getCommonMessages("searchScreen.label.like"),this.notLike=a.Wb.getCommonMessages("searchScreen.label.notLike"),this.into=a.Wb.getCommonMessages("searchScreen.label.in"),this.notIn=a.Wb.getCommonMessages("searchScreen.label.notIn"),this.between=a.Wb.getCommonMessages("searchScreen.label.between"),this.and="and",this.or=a.Wb.getCommonMessages("searchScreen.label.or"),this.inList=a.Wb.getCommonMessages("searchScreen.label.inList"),this.exists=a.Wb.getCommonMessages("searchScreen.label.contains"),this.undoButton.label="Undo",this.addButton.label="Add",this.changeButton.label="Change",this.tempNameDays="S,M,T,W,T,F,S",this.tempNameMonths="January,February,March,April,May,June,July,August,September,October,November,December",this.nameDays=this.tempNameDays.split(","),this.nameMonths=this.tempNameMonths.split(",")}catch(e){console.log("error",e)}},e.prototype.init=function(){var t=this;try{this.actionMethod="method=getSearchListCriteria",this.actionPath="expressionBuilderPCM.do?",this.inputData.cbStart=this.startOfComms.bind(this),this.inputData.cbStop=this.endOfComms.bind(this),this.inputData.cbResult=function(e){t.inputDataResult(e)},this.inputData.cbFault=this.inputDataFault.bind(this),this.inputData.encodeURL=!1,this.requestParams.method="getSearchListCriteria",this.inputData.url=this.baseURL+this.actionPath+this.actionMethod,this.requestParams=[],"add"==this.screenName?(this.requestParams.typeId=10,this.requestParams.moduleId=this.moduleId,this.requestParams.programId=this.programId,this.requestParams.externalFields=this.externalFields,this.requestParams.ruleType=this.ruleType,this.requestParams.screenName=this.screenName):(this.requestParams.typeId=10,this.requestParams.moduleId=this.moduleId,this.requestParams.programId=this.programId,this.requestParams.externalFields=this.externalFields,this.requestParams.ruleType=this.ruleType,this.requestParams.ruleId=this.ruleId,this.requestParams.screenName=this.screenName),this.inputData.send(this.requestParams)}catch(e){console.log("error in init",e)}},e.prototype.inputDataResult=function(t){var e=this,i=a.Wb.getPredictMessage("queryBuilderScreen.alert.unexprectedError",null);try{if(this.logger.info("method [inputDataResult] - START "),this.inputData.isBusy())this.inputData.cbStop();else{if(this.tabAllConditions&&this.tabAllConditions.length>0){for(var o=[],n=[],l=0;l<this.tabAllConditions.length;l++)o.push(this.tabAllConditions[l].columnToStore),n[this.tabAllConditions[l].columnToStore]=this.tabAllConditions[l];a.L.jsonpath(t,"$.search.grid.rows.*").forEach(function(t){if(t===Object(t)&&o.indexOf(t.code)>-1){if(t.conditionId=n[t.code].conditionId,t.toChange="Y",t.macroColumn)if(n[t.code].clause)t.clause=a.Z.trim(n[t.code].clause);else{var e=t.macroColumn,i=/ :op/gi,l=/ :param/gi;e="in"==n[t.code].operation||"not in"==n[t.code].operation?(e=e.replace(i," "+n[t.code].operation)).replace(l,"  "+a.Z.trim(n[t.code].columnCodeValue)):(e=e.replace(i," "+n[t.code].operation)).replace(l," "+a.Z.trim(n[t.code].columnCodeValue)),t.clause=e}t.fieldValue=a.Z.trim(n[t.code].columnCodeValue),t.operatorId=n[t.code].operation,t.condFieldName=a.Z.trim(n[t.code].columnToStore),t.nextCondition=n[t.code].andOrString,t.profileFieldValue=n[t.code].profileFieldValue}})}if(this.lastRecievedJSON=t,this.jsonReader.setInputJSON(this.lastRecievedJSON),this.jsonReader.getRequestReplyStatus()){this.sysdateformat=this.jsonReader.getSingletons().dateFormat,this.currencyPattern=this.jsonReader.getSingletons().currencyPattern,this.sysdateformat=this.sysdateformat.replace(null,"Y"),this.sysdateformat=this.sysdateformat.replace(null,"D");var s=[];"change"!=this.screenName&&a.L.jsonpath(t,"$.search.grid.rows.*").forEach(function(t){t===Object(t)&&s.push({label:t})}),"change"===this.screenName&&(this.firstJSONList=this.jsonReader.getGridData(),this.originalJSONList=this.jsonReader.getGridData(),a.L.jsonpath(t,"$.search.grid.rows.*").forEach(function(t){t===Object(t)&&"Y"==t.toChange&&null!=Object(t).toChange&&s.push({label:t})})),this.firstGridData={row:s,size:s.length};this.listCriteria.CustomGrid({columns:{column:[{columnorder:0,dataelement:"label",draggable:!0,filterable:!0,format:"",heading:"Constraints",sort:!0,type:"str",visible:!0,visible_default:!0,width:300}]}});try{this.listCriteria.gridData={row:s,size:s.length},this.gridJSONList=this.listCriteria.gridData}catch(r){}this.listCriteria.onRowClick=function(t){e.selectCriteria()},this.filterText.enabled=!0}else this.swtAlert.error(i)}}catch(h){this.logger.error("method [inputDataResult] - error : ",h,"- errorLocation :",this.errorLocation),a.Wb.logError(h,a.Wb.SYSTEM_MODULE_ID,this.commonService.getQualifiedClassName(this),"inputDataResult",this.errorLocation)}this.logger.info("method [inputDataResult] - END ")},e.prototype.startOfComms=function(){this.loadingImage.setVisible(!0)},e.prototype.endOfComms=function(){this.loadingImage.setVisible(!1)},e.prototype.inputDataFault=function(t){this.invalidComms=t.faultString+"\n"+t.faultCode+"\n"+t.faultDetail,this.swtAlert.error(t.faultCode)},e.prototype.doHelp=function(t){try{a.x.call("help")}catch(e){a.Wb.logError(e,this.moduleId,"ClassName","doHelp",this.errorLocation)}},e.prototype.keyDownEventHandler=function(t){try{if(this.logger.info("method [keyDownEventHandler] - START "),t.keyCode==a.N.ENTER){var e=Object(a.ic.getFocus()).name;"addButton"==e?this.createQuery():"undoButton"==e?this.previous():"okButton"==e?this.executeQuery(t):"resetButton"==e?this.reset(t):"cancelButton"==e?this.close():"helpIcon"==e?this.doHelp(t):"equalButton"==e?(this.operation=this.equalButton.label,this.operationToDisplay=this.equalButton.label,this.doOperation()):"differentButton"==e?(this.operation=this.differentButton.label,this.operationToDisplay=this.differentButton.label,this.doOperation()):"higherButton"==e?(this.operation=this.higherButton.label,this.operationToDisplay=this.higherButton.label,this.doOperation()):"higherequalButton"==e?(this.operation=this.higherequalButton.label,this.operationToDisplay=this.higherequalButton.label,this.doOperation()):"lowerButton"==e?(this.operation=this.lowerButton.label,this.operationToDisplay=this.lowerButton.label,this.doOperation()):"lowerequalButton"==e?(this.operation=this.lowerequalButton.label,this.operationToDisplay=this.lowerequalButton.label,this.doOperation()):"likeButton"==e?(this.operation="like",this.operationToDisplay=this.likeButton.label,this.doOperation()):"notLikeButton"==e?(this.operation="Not Like",this.operationToDisplay=this.notLikeButton.label,this.doOperation()):"inButton"==e?(this.operation="in",this.operationToDisplay=this.inButton.label,this.doOperation()):"notinButton"==e?(this.operation="not in",this.operationToDisplay=this.notinButton.label,this.doOperation()):"betweenButton"==e?(this.operation="between",this.operationToDisplay=this.betweenButton.label,this.doOperation()):"andButton"==e?(this.andOrString="and",this.andOrStringToDisplay=this.andButton.label,this.operationToDisplay=this.andButton.label,this.addOperation()):"orButton"==e?(this.andOrString="or",this.andOrStringToDisplay=this.orButton.label,this.operationToDisplay=this.orButton.label,this.addOperation()):"leftParentheseButton"==e?this.doOpenCloseParentheses():"rightParentheseButton"==e?this.doOpenCloseParentheses():"listValuesButton"==e?this.addListValuesEventHandler(t):"sysDateButton"==e?this.addSysDateEventHandler(t):"inListButton"==e?(this.operation="in list",this.operationToDisplay=this.inListButton.label,this.doOperation()):"changeButton"==e?this.createQuery():"changeCritButton"==e?this.changeCriteria():"containsButton"==e&&(this.operation="exists",this.operationToDisplay=this.containsButton.label,this.doOperation())}}catch(i){this.logger.error("method [keyDownEventHandler] - error : ",i,"- errorLocation :",this.errorLocation)}this.logger.info("method [keyDownEventHandler] - END ")},e.prototype.selectCriteria=function(){try{if(this.logger.info("method [selectCriteria] - START "),1==this.listCriteria.selectedIndices.length){var t=this.listCriteria.selectedItem.label.code,e=this.listCriteria.selectedItem.label.typeCode,i=(this.listCriteria.selectedItem.label.typeEnum,this.listCriteria.selectedItem.label.typeList),o=this.listCriteria.selectedItem.label.macroColumn,n=this.listCriteria.selectedItem.label.hiddenOperator;if(this.addButton.enabled=!1,this.propertiesLabel.text=this.listCriteria.selectedItem.label.label,this.propertiesLabel.width=NaN,this.propertiesLabel.toolTip=null,this.hBoxContainer.removeAllChildren(),this.operator.text="","CHAR"==e)this.equalButton.enabled=!0,this.differentButton.enabled=!0,this.higherButton.enabled=!1,this.higherequalButton.enabled=!1,this.lowerButton.enabled=!1,this.lowerequalButton.enabled=!1,t.toLowerCase().indexOf("time")>-1?(this.likeButton.enabled=!1,this.notLikeButton.enabled=!1):(this.likeButton.enabled=!0,this.notLikeButton.enabled=!0),this.inButton.enabled=!0,this.notinButton.enabled=!0,this.betweenButton.enabled=!1,0!=Number(i)?(this.inListButton.enabled=!0,this.containsButton.enabled=!0):(this.inListButton.enabled=!1,this.containsButton.enabled=!1);else if("NUM"==e||"DECI"==e)this.equalButton.enabled=!0,this.differentButton.enabled=!0,this.higherButton.enabled=!0,this.higherequalButton.enabled=!0,this.lowerButton.enabled=!0,this.lowerequalButton.enabled=!0,this.likeButton.enabled=!1,this.notLikeButton.enabled=!1,t&&-1==t.toLocaleLowerCase().indexOf("amount")&&(this.inButton.enabled=!0,this.notinButton.enabled=!0),this.betweenButton.enabled=!0,this.betweenButton.buttonMode=!0,this.betweenButton.enabled=""==o,0!=Number(i)&&null!=i?(this.inListButton.enabled=!0,this.containsButton.enabled=!0):(this.inListButton.enabled=!1,this.containsButton.enabled=!1);else if("DATE"==e)this.equalButton.enabled=!0,this.differentButton.enabled=!0,this.higherButton.enabled=!0,this.higherequalButton.enabled=!0,this.lowerButton.enabled=!0,this.lowerequalButton.enabled=!0,this.likeButton.enabled=!1,this.notLikeButton.enabled=!1,this.inButton.enabled=!0,this.notinButton.enabled=!0,this.betweenButton.enabled=!0,0!=Number(i)&&null!=i?(this.inListButton.enabled=!0,this.containsButton.enabled=!0):(this.inListButton.enabled=!1,this.containsButton.enabled=!1);else if("DTIME"==e)this.equalButton.enabled=!0,this.differentButton.enabled=!0,this.higherButton.enabled=!0,this.higherequalButton.enabled=!0,this.lowerButton.enabled=!0,this.lowerequalButton.enabled=!0,this.likeButton.enabled=!1,this.notLikeButton.enabled=!1,this.inButton.enabled=!0,this.notinButton.enabled=!0,this.betweenButton.enabled=!0,0!=Number(i)&&null!=i?(this.inListButton.enabled=!0,this.containsButton.enabled=!0):(this.inListButton.enabled=!1,this.containsButton.enabled=!1);else if("ENUM"==e){var l=this.jsonReader.getSelects().select.find(function(e){return e.id==t}).option[0].type;this.equalButton.enabled=!0,this.differentButton.enabled=!0,this.likeButton.enabled=!1,this.notLikeButton.enabled=!1,this.inButton.enabled=!0,this.notinButton.enabled=!0,0!=Number(i)&&null!=i?(this.inListButton.enabled=!0,this.containsButton.enabled=!0):(this.containsButton.enabled=!1,this.inListButton.enabled=!1),"NUM"==l||"DECI"==l?(this.betweenButton.enabled=""==o,this.betweenButton.enabled=!0,this.higherButton.enabled=!0,this.higherequalButton.enabled=!0,this.lowerButton.enabled=!0,this.lowerequalButton.enabled=!0):(this.betweenButton.enabled=!1,this.higherButton.enabled=!1,this.higherequalButton.enabled=!1,this.lowerButton.enabled=!1,this.lowerequalButton.enabled=!1)}if(""!=n&&null!=n)for(var s=n.split("|"),a=0;a<s.length;a++)"in"==s[a]&&(this.inButton.enabled=!1),"notin"==s[a]&&(this.notinButton.enabled=!1),"equalButton"==s[a]&&(this.equalButton.enabled=!1),"different"==s[a]&&(this.differentButton.enabled=!1),"higher"==s[a]&&(this.higherButton.enabled=!1),"higherequal"==s[a]&&(this.higherequalButton.enabled=!1),"lower"==s[a]&&(this.lowerButton.enabled=!1),"lowerequal"==s[a]&&(this.lowerequalButton.enabled=!1),"like"==s[a]&&(this.likeButton.enabled=!1),"notLike"==s[a]&&(this.notLikeButton.enabled=!1),"between"==s[a]&&(this.betweenButton.enabled=!1),"inList"==s[a]&&(this.inListButton.enabled=!1),"exists"==s[a]&&(this.containsButton.enabled=!1);"change"==this.screenName&&(this.changeCritButton.enabled=!0,this.labelQuery.text=this.getConditionById(this.listCriteria.selectedItem.label.conditionId-1))}else this.changeCritButton.enabled=!1,this.clearContraints()}catch(r){console.log("error in select criteria",r)}},e.prototype.doOperation=function(){var t=this;try{var e=this.listCriteria.selectedItem.label.code,i=this.listCriteria.selectedItem.label.typeCode,o=this.listCriteria.selectedItem.label.typeEnum,n=this.listCriteria.selectedItem.label.dataType,l=this.listCriteria.selectedItem.label.profileField,s=void 0,r="";if(this.addButton.enabled=!0,this.addButton.buttonMode=!0,this.changeButton.enabled=!0,this.changeButton.buttonMode=!0,this.operator.text=this.operationToDisplay,8*String(this.listCriteria.selectedItem.label.label).length>250&&(this.propertiesLabel.width=250,this.propertiesLabel.toolTip=this.listCriteria.selectedItem.label.label),this.vBoxContainer.contains(this.hBoxContainer2),this.hBoxContainer.removeAllChildren(),"CHAR"==i)if(this.operation==this.equalButton.label||this.operation==this.differentButton.label)null!=(s=this.jsonReader.getSelects().select.find(function(t){return t.id==e}))&&"0"==o?(this.comboBox=this.hBoxContainer.addChild(a.gb),this.comboBox.width="250",this.comboBox.styleName="dropDown",this.comboBox.dataLabel=e,this.comboBox.id="SwtComboBox",this.comboBox.setComboData(this.jsonReader.getSelects(),!0),this.comboBox.change=function(){t.comboChangeHandler(event)},this.comboBox.enabled=!0,this.comboBox.setFocus(),this.comboChangeHandler(event)):(this.textInput=this.hBoxContainer.addChild(a.Rb),this.textInput.width="200",this.textInput.styleName="textbox",this.textInput.id="SwtTextInput",this.textInput.doubleClick=function(){t.openTextAreaInWindow(event)},e&&e.toLocaleLowerCase().indexOf("time")>-1&&(this.textInput.pattern="^(0[0-9]|1[0-9]|2[0-3]|[0-9]):[0-5][0-9]$",this.textInput.textAlign="center",this.textInput.width="70",this.textInput.onFocusOut=function(e){t.validateTime(t.textInput)}),this.textInput.enabled=!0,this.textInput.setFocus());else if("in"==this.operation||"not in"==this.operation){if(this.textInput=this.hBoxContainer.addChild(a.Rb),this.textInput.width="200",this.textInput.styleName="textbox",this.textInput.id="SwtTextInput",this.textInput.doubleClick=function(){t.openTextAreaInWindow(event)},this.textInput.enabled=!0,this.textInput.setFocus(),e&&e.toLocaleLowerCase().indexOf("time")>-1)(c=this.hBoxContainer.addChild(a.vb)).text="hh:mm, hh:mm, ...",c.fontWeight="normal";null!=(s=this.jsonReader.getSelects().select.find(function(t){return t.id==e}))&&(this.listValuesButton=[],this.listValuesButton=this.hBoxContainer.addChild(a.cb),this.listValuesButton.styleName="flexButton",this.listValuesButton.label="...",this.listValuesButton.id="b1",this.listValuesButton.name="listValuesButton",this.listValuesButton.click=function(){t.addListValuesEventHandler(event)},this.listValuesButton.keyDown=function(){t.keyDownEventHandler(event)})}else"in list"==this.operation||"exists"==this.operation?(this.comboBox=this.hBoxContainer.addChild(a.gb),this.comboBox.width="250",this.comboBox.styleName="dropDown",this.comboBox.dataLabel=e+"_LIST",this.comboBox.id="SwtComboBox",this.comboBox.setComboData(this.jsonReader.getSelects(),!0),this.comboBox.enabled=!0,this.comboBox.setFocus(),"in list"==this.operation&&(this.operation="in")):(this.textInput=this.hBoxContainer.addChild(a.Rb),this.textInput.width="200",this.textInput.styleName="textbox",this.textInput.id="SwtTextInput",this.textInput.doubleClick=function(){t.openTextAreaInWindow(event)},e&&e.toLocaleLowerCase().indexOf("time")>-1&&(this.textInput.pattern="^(0[0-9]|1[0-9]|2[0-3]|[0-9]):[0-5][0-9]$",this.textInput.textAlign="center",this.textInput.width="70",this.textInput.onFocusOut=function(e){t.validateTime(t.textInput)}),this.textInput.enabled=!0,this.textInput.setFocus());else if("NUM"==i||"DECI"==i)if(this.operation==this.equalButton.label||this.operation==this.differentButton.label||this.operation==this.higherButton.label||this.operation==this.higherequalButton.label||this.operation==this.lowerButton.label||this.operation==this.lowerequalButton.label)"Y"==l?("A"==n?r="THRESHOLD_AMOUNT":"C"==n&&(r="THRESHOLD_COUNT"),null!=(s=this.jsonReader.getSelects().select.find(function(t){return t.id==r}))&&(o="0",e=r)):s=this.jsonReader.getSelects().select.find(function(t){return t.id==e}),null!=s?"0"==o?(this.comboBox=this.hBoxContainer.addChild(a.gb),this.comboBox.width="280",this.comboBox.styleName="dropDown",this.comboBox.id="SwtComboBox",this.comboBox.dataLabel=e,this.comboBox.setComboData(this.jsonReader.getSelects(),!0),this.comboBox.change=function(){t.comboChangeHandler(event)},this.comboBox.enabled=!0,this.comboBox.setFocus(),this.comboChangeHandler(event)):(this.textInput=this.hBoxContainer.addChild(a.Rb),this.textInput.width="200",this.textInput.styleName="textbox",e&&e.toLocaleLowerCase().indexOf("amount")>-1?(this.textInput.restrict="0-9-,.TBMtbm",this.textInput.onFocusOut=function(e){t.validateAmount()}):this.textInput.restrict="0-9.\\-",this.textInput.enabled=!0,this.textInput.setFocus()):(this.textInput=this.hBoxContainer.addChild(a.Rb),this.textInput.width="200",this.textInput.styleName="textbox",this.textInput.enabled=!0,e&&e.toLocaleLowerCase().indexOf("amount")>-1?(this.textInput.restrict="0-9-,.TBMtbm",this.textInput.onFocusOut=function(e){t.validateAmount()}):this.textInput.restrict="0-9.\\-",this.textInput.setFocus());else if("in"==this.operation||"not in"==this.operation)this.textInput=this.hBoxContainer.addChild(a.Rb),this.textInput.width="200",this.textInput.styleName="textbox",e&&e.toLocaleLowerCase().indexOf("amount")>-1?(this.textInput.restrict="0-9-,.TBMtbm",this.textInput.onFocusOut=function(e){t.validateAmount()}):this.textInput.restrict="0-9.\\-",this.textInput.id="SwtTextInput",this.textInput.doubleClick=function(){t.openTextAreaInWindow(event)},this.textInput.enabled=!0,this.textInput.setFocus(),null!=(s=this.jsonReader.getSelects().select.find(function(t){return t.id==e}))&&(this.listValuesButton=this.hBoxContainer.addChild(a.cb),this.listValuesButton.styleName="flexButton",this.listValuesButton.label="...",this.listValuesButton.id="b1",this.listValuesButton.name="listValuesButton",this.listValuesButton.click=function(){t.addListValuesEventHandler(event)},this.listValuesButton.keyDown=function(){t.keyDownEventHandler(event)});else if("between"==this.operation)if("Y"==l?("A"==n?r="THRESHOLD_AMOUNT":"C"==n&&(r="THRESHOLD_COUNT"),null!=(s=this.jsonReader.getSelects().select.find(function(t){return t.id==r}))&&(o="0",e=r)):s=this.jsonReader.getSelects().select.find(function(t){return t.id==e}),null!=s){this.comboBox=this.hBoxContainer.addChild(a.gb),this.comboBox.width="200",this.comboBox.styleName="dropDown",this.comboBox.dataLabel=e,this.comboBox.id="SwtComboBox",this.comboBox.setComboData(this.jsonReader.getSelects(),!0),this.comboBox.enabled=!0,this.comboBox.change=function(){t.comboBetweenChangeHandler(event)},this.comboBetweenChangeHandler(event),this.comboBox.setFocus();var h=this.hBoxContainer.addChild(a.vb);h.text="  "+this.and+"  ",h.styleName="label",h.width="50",this.comboBox2=this.hBoxContainer.addChild(a.gb),this.comboBox2.width="200",this.comboBox2.styleName="dropDown",this.comboBox2.id="SwtComboBox",this.comboBox2.dataLabel=e,this.comboBox2.setComboData(this.jsonReader.getSelects(),!0),this.comboBox2.enabled=!0,this.comboBox2.change=function(){t.comboBetweenChangeHandler(event)},this.comboBetweenChangeHandler(event)}else{this.textInput=this.hBoxContainer.addChild(a.Rb),this.textInput.width="200",this.textInput.styleName="textbox",this.textInput.id="SwtTextInput",e&&e.toLocaleLowerCase().indexOf("amount")>-1?(this.textInput.restrict="0-9-,.TBMtbm",this.textInput.onFocusOut=function(e){t.validateAmount()}):this.textInput.restrict="0-9.\\-",this.textInput.enabled=!0,this.textInput.setFocus();var u=this.hBoxContainer.addChild(a.vb);u.text="  "+this.and+"  ",u.styleName="label",u.width="50",this.textInput2=this.hBoxContainer.addChild(a.Rb),this.textInput2.width="200",this.textInput2.styleName="textbox",this.textInput2.id="SwtTextInput",e&&e.toLocaleLowerCase().indexOf("amount")>-1?(this.textInput2.restrict="0-9-,.TBMtbm",this.textInput2.onFocusOut=function(e){t.validateAmount()}):this.textInput2.restrict="0-9.\\-",this.textInput2.enabled=!0}else"in list"==this.operation||"exists"==this.operation?(this.comboBox=this.hBoxContainer.addChild(a.gb),this.comboBox.width="250",this.comboBox.styleName="dropDown",this.comboBox.id="SwtComboBox",this.comboBox.dataLabel=e+"_LIST",this.comboBox.setComboData(this.jsonReader.getSelects(),!0),this.comboBox.enabled=!0,this.comboBox.setFocus(),this.operation="in"):(this.textInput=this.hBoxContainer.addChild(a.Rb),this.textInput.width="200",this.textInput.styleName="textbox",e&&e.toLocaleLowerCase().indexOf("amount")>-1?(this.textInput.restrict="0-9-,.TBMtbm",this.textInput.onFocusOut=function(e){t.validateAmount()}):this.textInput.restrict="0-9.\\-",this.textInput.enabled=!0,this.textInput.setFocus());else if("DTIME"==i){if("in"==this.operation||"not in"==this.operation)this.textInput=this.hBoxContainer.addChild(a.Rb),this.textInput.width="250",this.textInput.styleName="textbox",this.textInput.id="SwtTextInput",this.textInput.doubleClick=function(){t.openTextAreaInWindow(event)},this.textInput.onFocusOut=function(t){},this.textInput.enabled=!0,this.textInput.setFocus(),(c=this.hBoxContainer.addChild(a.vb)).text=" "+this.sysdateformat+" hh:mm, "+this.sysdateformat+" hh:mm, ...",c.fontWeight="normal";else if(this.dateField=this.hBoxContainer.addChild(a.lb),this.dateField.formatString=this.sysdateformat,this.dateField.id="frmDateChooser",this.dateField.width="80",this.dateField.restrict="0-9\\-",this.dateField.enabled=!0,this.dateField.showToday=!1,this.dateField.selectableRange={rangeEnd:new Date},this.dateField.open=function(){t.datePickerOn(event)},this.dateField.close=function(){t.datePickerOff(event)},this.dateField.setFocus(),this.spacer=this.hBoxContainer.addChild(a.Y),this.spacer.width="5",this.timeField=this.hBoxContainer.addChild(a.Rb),this.timeField.pattern="^(0[0-9]|1[0-9]|2[0-3]|[0-9]):[0-5][0-9]$",this.timeField.textAlign="center",this.timeField.width="70",this.timeField.onFocusOut=function(e){t.validateTime(t.timeField)},"between"==this.operation){var d=new Date;(b=this.hBoxContainer.addChild(a.vb)).text="  "+this.and+"  ",b.styleName="label",this.dateField2=this.hBoxContainer.addChild(a.lb),this.dateField2.id="toDateChooser",this.dateField2.width="80",this.dateField2.restrict="0-9\\-",this.dateField2.enabled=!0,this.dateField2.showToday=!1,this.dateField2.formatString=this.sysdateformat,this.dateField2.open=function(){t.datePickerOn(event)},this.dateField2.close=function(){t.datePickerOff(event)},this.dateField2.selectableRange={rangeStart:d,rangeEnd:new Date},this.spacer=this.hBoxContainer.addChild(a.Y),this.spacer.width="5",this.timeField2=this.hBoxContainer.addChild(a.Rb),this.timeField2.pattern="^(0[0-9]|1[0-9]|2[0-3]|[0-9]):[0-5][0-9]$",this.timeField2.textAlign="center",this.timeField2.width="70",this.timeField2.onFocusOut=function(e){t.validateTime(t.timeField2)}}}else if("DATE"==i){var c;if("in"==this.operation||"not in"==this.operation)this.textInput=this.hBoxContainer.addChild(a.Rb),this.textInput.width="250",this.textInput.styleName="textbox",this.textInput.id="SwtTextInput",this.textInput.doubleClick=function(){t.openTextAreaInWindow(event)},this.textInput.enabled=!0,this.textInput.setFocus(),null!=this.jsonReader.getSelects().select.find(function(t){return t.id==e})&&(this.listValuesButton=this.hBoxContainer.addChild(a.cb),this.listValuesButton.styleName="flexButton",this.listValuesButton.label="...",this.listValuesButton.id="b1",this.listValuesButton.name="listValuesButton",this.listValuesButton.click=function(){t.addListValuesEventHandler(event)},this.listValuesButton.keyDown=function(){t.keyDownEventHandler(event)}),(c=this.hBoxContainer.addChild(a.vb)).text=" "+this.sysdateformat+", "+this.sysdateformat+", ...",c.fontWeight="normal";else if("in list"==this.operation||"exists"==this.operation)this.comboBox=this.hBoxContainer.addChild(a.gb),this.comboBox.width="250",this.comboBox.styleName="dropDown",this.comboBox.dataLabel=e+"_LIST",this.comboBox.id="SwtComboBox",this.comboBox.setComboData(this.jsonReader.getSelects(),!0),this.comboBox.enabled=!0,this.comboBox.setFocus(),this.operation="in";else{if(this.dateField=this.hBoxContainer.addChild(a.lb),this.dateField.formatString=this.sysdateformat,this.dateField.id="frmDateChooser",this.dateField.width="80",this.dateField.restrict="0-9\\-",this.dateField.enabled=!0,this.dateField.showToday=!1,this.dateField.selectableRange={rangeEnd:new Date},this.dateField.open=function(){t.datePickerOn(event)},this.dateField.close=function(){t.datePickerOff(event)},this.dateField.setFocus(),"between"==this.operation){var b;d=new Date;(b=this.hBoxContainer.addChild(a.vb)).text="  "+this.and+"  ",b.styleName="label",this.dateField2=this.hBoxContainer.addChild(a.lb),this.dateField2.id="toDateChooser",this.dateField2.width="80",this.dateField2.restrict="0-9\\-",this.dateField2.enabled=!0,this.dateField2.showToday=!1,this.dateField2.formatString=this.sysdateformat,this.dateField2.open=function(){t.datePickerOn(event)},this.dateField2.close=function(){t.datePickerOff(event)},this.dateField2.selectableRange={rangeStart:d,rangeEnd:new Date}}this.spacer=this.hBoxContainer.addChild(a.Y),this.spacer.width="5",this.sysDateButton=this.hBoxContainer.addChild(a.cb),this.sysDateButton.label="Sys Date",this.sysDateButton.id="b11",this.sysDateButton.name="sysDateButton",this.sysDateButton.click=function(){t.addSysDateEventHandler(event)},this.sysDateButton.keyDown=function(){t.keyDownEventHandler(event)}}}else if("ENUM"==i){var p=this.jsonReader.getSelects().select.find(function(t){return t.id==e}).option[0].type;if("in"==this.operation||"not in"==this.operation)"CHAR"==p?(this.textInput=this.hBoxContainer.addChild(a.Rb),this.textInput.width="200",this.textInput.enabled=!0,this.textInput.id="SwtTextInput",this.textInput.doubleClick=function(){t.openTextAreaInWindow(event)},this.textInput.setFocus(),this.listValuesButton=this.hBoxContainer.addChild(a.cb),this.listValuesButton.label="...",this.listValuesButton.id="b1",this.listValuesButton.width="70",this.listValuesButton.name="listValuesButton",this.listValuesButton.click=function(){t.addListValuesEventHandler(event)},this.listValuesButton.keyDown=function(){t.keyDownEventHandler(event)}):"NUM"==p||"DECI"==p?(this.textInput=this.hBoxContainer.addChild(a.Rb),this.textInput.width="200",this.textInput.styleName="textbox",e&&e.toLocaleLowerCase().indexOf("amount")>-1?(this.textInput.restrict="0-9-,.TBMtbm",this.textInput.onFocusOut=function(e){t.validateAmount()}):this.textInput.restrict="0-9.\\-",this.textInput.enabled=!0,this.textInput.id="SwtTextInput",this.textInput.doubleClick=function(){t.openTextAreaInWindow(event)},this.textInput.setFocus(),this.listValuesButton=this.hBoxContainer.addChild(a.cb),this.listValuesButton.styleName="flexButton",this.listValuesButton.label="...",this.listValuesButton.id="b1",this.listValuesButton.name="listValuesButton",this.listValuesButton.click=function(){t.addListValuesEventHandler(event)},this.listValuesButton.keyDown=function(){t.keyDownEventHandler(event)}):"BIND_C"!=p&&"BIND_N"!=p||(this.textInput=this.hBoxContainer.addChild(a.Rb),this.textInput.width="200",this.textInput.styleName="textbox",this.textInput.enabled=!1,this.textInput.id="SwtTextInput",this.textInput.doubleClick=function(){t.openTextAreaInWindow(event)},this.textInput.setFocus(),this.listValuesButton=this.hBoxContainer.addChild(a.cb),this.listValuesButton.styleName="flexButton",this.listValuesButton.label="...",this.listValuesButton.id="b1",this.listValuesButton.name="listValuesButton",this.listValuesButton.click=function(){t.addListValuesEventHandler(event)},this.listValuesButton.keyDown=function(){t.keyDownEventHandler(event)});else if("between"==this.operation){this.comboBox=this.hBoxContainer.addChild(a.gb),this.comboBox.width="200",this.comboBox.styleName="dropDown",this.comboBox.id="SwtComboBox",this.comboBox.dataLabel=e,this.comboBox.setComboData(this.jsonReader.getSelects(),!0),this.comboBox.change=function(){t.comboBetweenChangeHandler(event)},this.comboBox.enabled=!0,this.comboBox.setFocus(),this.comboChangeHandler(event);var m=this.hBoxContainer.addChild(a.vb);m.text="  "+this.and+"  ",m.styleName="label",this.comboBox2=this.hBoxContainer.addChild(a.gb),this.comboBox2.width="200",this.comboBox2.styleName="dropDown",this.comboBox2.id="SwtComboBox",this.comboBox2.dataLabel=e,this.comboBox2.setComboData(this.jsonReader.getSelects(),!0),this.comboBox2.change=function(){t.comboBetweenChangeHandler(event)},this.comboBox2.enabled=!0,this.comboChangeHandler(event)}else"in list"==this.operation||"exists"==this.operation?(this.comboBox=this.hBoxContainer.addChild(a.gb),this.comboBox.width="250",this.comboBox.styleName="dropDown",this.comboBox.id="SwtComboBox",this.comboBox.dataLabel=e+"_LIST",this.comboBox.setComboData(this.jsonReader.getSelects(),!0),this.comboBox.enabled=!0,this.comboBox.setFocus(),this.operation="in"):(this.comboBox=this.hBoxContainer.addChild(a.gb),this.comboBox.width="250",this.comboBox.dataLabel=e,this.comboBox.id="SwtComboBox",this.comboBox.setComboData(this.jsonReader.getSelects(),!0),this.comboBox.change=function(){t.comboChangeHandler(event)},this.comboBox.enabled=!0,this.comboBox.setFocus(),this.comboChangeHandler(event))}}catch(g){console.log("error in do operation",g),this.logger.error("method [doOperation] - error : ",g,"- errorLocation :",this.errorLocation),a.Wb.logError(g,a.Wb.SYSTEM_MODULE_ID,this.commonService.getQualifiedClassName(this),"doOperation",this.errorLocation)}this.logger.info("method [doOperation] - END ")},e.prototype.validateLikeOp=function(t,e){return-1!=e.indexOf("%")||(this.swtAlert.question(t+" "+a.Wb.getPredictMessage("alert.likeOperator",null),null,a.c.YES|a.c.NO,null,this.checkTextInput.bind(this)),!1)},e.prototype.checkTextInput=function(t){t.detail===a.c.YES||this.createQuery()},e.prototype.chekcFields=function(){var t=this.listCriteria.selectedItem.label.label;"like"==this.operation||"Not Like"==this.operation?""==this.textInput.text?this.swtAlert.warning(a.Wb.getPredictMessage("queryBuilderScreen.alert.missingProperty",null)+" "+t,"Warning"):-1==this.textInput.text.indexOf("%")?this.validateLikeOp(t,this.textInput.text):this.createQuery():this.createQuery()},e.prototype.createQuery=function(){try{this.logger.info("method [createQuery] - START ");for(var t=this.listCriteria.selectedItem.label.code,e=this.listCriteria.selectedItem.label.label,i=this.listCriteria.selectedItem.label.typeCode,o=this.listCriteria.selectedItem.label.tableName,n=this.listCriteria.selectedItem.label.localValue,l=this.listCriteria.selectedItem.label.profileField,s=this.listCriteria.selectedItem.label.expression,r=(this.listCriteria.selectedItem.label.fieldName,this.listCriteria.selectedItem.label.aliasTable,this.listCriteria.selectedItem.label.columnValueUpper),h=this.listCriteria.selectedItem.label.conditionId,u="true"==this.listCriteria.selectedItem.label.isIndexFunction,d=void 0===this.listCriteria.selectedItem.label.macroColumn?"":this.listCriteria.selectedItem.label.macroColumn,c="",b="",p="",m="",g=this.hBoxContainer.getChildren(),y=g[0].childType,B=[],x=0;x<g.length;x++)B.push(g[x].childType);var f=/[,]+/gi,T="",C=/ :op/gi,D=/ :param/gi,w=null,I=null,S=null,v=!1,N="",E=this.listCriteria.selectedIndex;if("SwtComboBox"==(y=y.replace(/[0-9]+/gi,""))&&"TYPETEXT"==this.comboBox.selectedItem.id&&(y="SwtTextInput"),"change"===this.screenName&&(this.andOrString=this.listCriteria.selectedItem.label.nextCondition),"Y"===l){for(var k=0;k<this.tableToJoin.length;k++)if(String(this.tableToJoin[k]).indexOf(o)>=0){v=!0;break}v||(this.tableToJoin[this.index]=this.tabName,this.index=this.index+1)}else if(null!=o&&""!==o){for(var O=0;O<this.tableToJoin.length;O++)if(String(this.tableToJoin[O]).indexOf(o+" ")>=0){v=!0;break}v||t.indexOf(".")>=0&&(w=t.substring(0,t.indexOf(".")),this.tableToJoin[this.index]=o+" "+w,this.index=this.index+1)}if("SwtTextInput"===y){var L=!1,F=null;if(c=(c="YY"===r||"NY"===r?this.textInput.text.toUpperCase():this.textInput.text).replace(/[']/gi,"''"),"DATE"===i&&""!=c&&((F=(c=this.textInput.text).split(","))[0]=F[0].replace(/(^\s)|(\s$)/gi,""),""==F[0]?L=!0:T=" TO_DATE('"+F[0]+"', '"+this.sysdateformat+"')",F.length>1))for(var A=1;A<F.length;A++)F[A]=F[A].replace(/(^\s)|(\s$)/gi,""),""===F[A]?L=!0:T=T+", TO_DATE('"+F[A]+"', '"+this.sysdateformat+"')";if("DTIME"===i&&""!=c&&((F=(c=this.textInput.text).split(","))[0]=F[0].replace(/(^\s)|(\s$)/gi,""),""==F[0]?L=!0:T=" TO_DATE('"+F[0]+"', '"+this.sysdateformat+" HH24:MI')",F.length>1))for(A=1;A<F.length;A++)F[A]=F[A].replace(/(^\s)|(\s$)/gi,""),""===F[A]?L=!0:T=T+", TO_DATE('"+F[A]+"', '"+this.sysdateformat+" HH24:MI')";if(this.hBoxContainer.contains(this.listValuesButton)&&""!==c&&"DATE"!==i&&"DTIME"!==i){c=this.textInput.text,F="YY"==r||"NY"==r?c.toUpperCase().split(","):c.split(",");for(var M=new a.H,R=this.jsonReader.getSelects().select.find(function(e){return e.id==t}).option,P=0;P<R.length;P++){var q=R[P].content,_=R[P].id;"YY"==r||"NY"==r?M.put(q.toUpperCase(),_.toUpperCase()):M.put(q,_)}for(x=0;x<F.length;x++)F[x]=F[x].replace(/(^\s)|(\s$)/gi,""),T=M.containsKey(F[x])?""==T?M.getValue(F[x]):T+","+M.getValue(F[x]):""==T?F[x]:T+","+F[x]}else this.hBoxContainer.contains(this.listValuesButton)||""==c||"DATE"==i||"DTIME"===i||(T="YY"==r||"NY"==r?this.textInput.text.toUpperCase():this.textInput.text);if(T=String(T),"NUM"==i||"DECI"==i)"in"==this.operation||"not in"==this.operation?(c=this.verifyTextInput(c,i),T=this.verifyTextInput(T,i),"false"!=c&&"false"!=T&&(p=""==d?t+" "+this.operation+" ("+T+")":d=(d=d.replace(C," "+this.operation)).replace(D," ("+T+")"),m="[ "+e+" "+this.operationToDisplay+" ("+c+") ]",this.tabAllConditions.push({columnToStore:t,operation:this.operation,columnCodeValue:" ("+T+")",typeCode:i,localValue:n,tabName:o,profileField:l,andOrString:this.andOrString,fieldExpression:s,clause:p,enumProfile:"N",index:E,conditionId:h}))):"between"==this.operation?""==(b=this.textInput2.text)?c="":(c=this.verifyTextInput(c,i),b=this.verifyTextInput(b,i),"false"!=c&&"false"!=b&&(p=t+"  "+this.operation+" To_number("+this.getNumberFromAmount(c)+") and  To_number("+this.getNumberFromAmount(b)+")",m="[ "+e+" "+this.operationToDisplay+" "+c+" and "+b+" ]",this.tabAllConditions.push({columnToStore:t,operation:this.operation,columnCodeValue:" To_number("+this.getNumberFromAmount(c)+") and  To_number("+this.getNumberFromAmount(b)+")",typeCode:i,localValue:n,tabName:o,profileField:l,andOrString:this.andOrString,fieldExpression:s,clause:p,enumProfile:"N",index:E,conditionId:h}))):"false"!=(c=this.verifyTextInput(c,i))&&(""==c?"="==this.operation?(I=" = To_number(0)",S=" = 0 ",this.operation="",this.operationToDisplay="",c="is null"):"<>"==this.operation&&(I=" = To_number(0)",S=" = 0 ",this.operation="",this.operationToDisplay="",c=" is not null"):(I=" To_number("+this.getNumberFromAmount(c)+")",S=c),p=""==d?t+" "+this.operation+I:d=(d=d.replace(C," "+this.operation)).replace(D,I),m="[ "+e+" "+this.operationToDisplay+" "+S+" ]",this.tabAllConditions.push({columnToStore:t,operation:this.operation,columnCodeValue:I,typeCode:i,localValue:n,tabName:o,profileField:l,andOrString:this.andOrString,fieldExpression:s,clause:p,enumProfile:"N",index:E,conditionId:h}));else if("CHAR"==i)"in"==this.operation||"not in"==this.operation?(e&&e.toLocaleLowerCase().indexOf("time")>-1&&"error_time"==this.validateTimes(this.textInput.text)&&(c="error_time"),T=T.replace(/(^,)|(,$)/gi,""),c=c.replace(/(^,)|(,$)/gi,""),T=(T=T.replace(/[']/g,"''")).replace(f,"','"),c=c.replace(f,","),"YY"==r?(t="upper("+t+") ",T=" ('"+T.toUpperCase()+"')"):"NY"==r?T=" ('"+T.toUpperCase()+"')":"YN"==r?(t="upper("+t+") ",T=" ('"+T+"')"):T=" ('"+T+"')",p=""==d?t+" "+this.operation+T:d=(d=d.replace(C," "+this.operation)).replace(D," "+T),m="[ "+e+" "+this.operationToDisplay+" ("+c+") ]",this.tabAllConditions.push({columnToStore:t,operation:this.operation,columnCodeValue:T,typeCode:i,localValue:n,tabName:o,profileField:l,andOrString:this.andOrString,fieldExpression:s,clause:p,enumProfile:"N",index:E,conditionId:h})):(""==c?"="==this.operation?(I="IS NULL",S=" is null",this.operation="",this.operationToDisplay="",c=" is null"):"<>"==this.operation&&(I="IS NOT NULL",S=" is not null",this.operation="",this.operationToDisplay="",c=" is not null"):(""!=d&&(c=c.replace(/[']/g,"''")),I="YY"==r||"NY"==r?" '"+c.toUpperCase()+"'":" '"+c+"'",S=c),"YY"==r&&(t="upper("+t+") "),p=""==d?t+" "+this.operation+" "+I:d=(d=d.replace(C," "+this.operation)).replace(D,I),m="[ "+e+" "+this.operationToDisplay+" "+S+" ]",this.tabAllConditions.push({columnToStore:t,operation:this.operation,columnCodeValue:I,typeCode:i,localValue:n,tabName:o,profileField:l,andOrString:this.andOrString,fieldExpression:s,clause:p,enumProfile:"N",index:E,conditionId:h}));else if("ENUM"==i){var V=this.listCriteria.selectedItem.label.code,J=this.jsonReader.getSelects().select.find(function(t){return t.id==V}).option[0].type;if("in"==this.operation||"not in"==this.operation){if("NUM"==J||"DECI"==J?"false"!=(c=this.verifyTextInput(c,J))&&(p=""==d?t+" "+this.operation+" ("+c+")":d=(d=d.replace(C," "+this.operation)).replace(D," ("+c+")"),m="[ "+t+" "+this.operationToDisplay+" ("+c+") ]",this.tabAllConditions.push({columnToStore:t,operation:this.operation,columnCodeValue:" ("+c+")",typeCode:i,localValue:n,tabName:o,profileField:l,andOrString:this.andOrString,fieldExpression:s,clause:p,enumProfile:"N",index:E,conditionId:h})):"CHAR"==J&&(T=T.replace(/(^,)|(,$)/gi,""),c=c.replace(/(^,)|(,$)/gi,""),T=T.replace(f,"','"),c=c.replace(f,","),""!=d&&(T=T.replace(/[']/g,"''")),"YY"==r?(t="upper("+t+") ",T="('"+T.toUpperCase()+"')"):"NY"==r?T="('"+T.toUpperCase()+"')":"YN"==r?(t="upper("+t+")",T="('"+T+"')"):T="('"+T+"')",p=""==d?t+" "+this.operation+" "+T:d=(d=d.replace(C," "+this.operation)).replace(D," "+T),this.tabAllConditions.push({columnToStore:t,operation:this.operation,columnCodeValue:T,typeCode:i,localValue:n,tabName:o,profileField:l,andOrString:this.andOrString,fieldExpression:s,clause:p,enumProfile:"N",index:E,conditionId:h}),m="[ "+e+" "+this.operationToDisplay+" ("+c+") ]"),"BIND_N"==J){var H=[];H=c.split(",");for(var Y=0;Y<H.length;Y++)this.varToBind.push(H[Y]);p=t+" "+this.operation+" ("+c+")",m="[ "+e+" "+this.operationToDisplay+" ("+c+") ]",this.tabAllConditions.push({columnToStore:t,operation:this.operation,columnCodeValue:" ("+c+")",typeCode:i,localValue:n,tabName:o,profileField:l,andOrString:this.andOrString,fieldExpression:s,clause:p,enumProfile:"N",index:E,conditionId:h})}else if("BIND_C"==J){H=T.split(",");for(var U=0;U<H.length;U++)this.varToBind.push(H[U]);"YY"==r?(t="upper("+t+") ",T=(T=" (upper("+T+"))").replace(f,"),upper("),p=t+this.operation+T):"NY"==r?(T=(T=" (upper("+T+"))").replace(f,"),upper("),p=t+" "+this.operation+T):"YN"==r?(T=" upper("+T+") ",p=t+" "+this.operation+T):p=(t="upper("+t+") ")+" "+this.operation+T,this.tabAllConditions.push({columnToStore:t,operation:this.operation,columnCodeValue:T,typeCode:i,localValue:n,tabName:o,profileField:l,andOrString:this.andOrString,fieldExpression:s,clause:p,enumProfile:"N",index:E,conditionId:h}),m="[ "+e+" "+this.operationToDisplay+" ("+c+") ]"}}else"between"==this.operation&&(""==(b=this.textInput2.text)?c="":"BIND_N"==J?(p=t+"  "+this.operation+" "+c+" and "+b,m="[ "+e+" "+this.operationToDisplay+" "+c+" and "+b+" ]",this.tabAllConditions.push({columnToStore:t,operation:this.operation,columnCodeValue:" "+c+" and  "+b,typeCode:i,localValue:n,tabName:o,profileField:l,andOrString:this.andOrString,fieldExpression:s,clause:p,enumProfile:"N",index:E,conditionId:h}),this.varToBind.push(c),this.varToBind.push(b)):(c=this.verifyTextInput(c,i),b=this.verifyTextInput(b,i),"false"!=c&&"false"!=b&&(p=t+"  "+this.operation+" To_number("+this.getNumberFromAmount(c)+") and  To_number("+this.getNumberFromAmount(b)+")",m="[ "+e+" "+this.operationToDisplay+" "+c+" and "+b+" ]",this.tabAllConditions.push({columnToStore:t,operation:this.operation,columnCodeValue:" To_number("+this.getNumberFromAmount(c)+") and  To_number("+this.getNumberFromAmount(b)+")",typeCode:i,localValue:n,tabName:o,profileField:l,andOrString:this.andOrString,fieldExpression:s,clause:p,enumProfile:"N",index:E,conditionId:h}))))}else"DATE"==i?"in"!=this.operation&&"not in"!=this.operation||(T=T.replace(/(^,)|(,$)/gi,""),c=c.replace(/(^,)|(,$)/gi,""),p=""==d?"TRUNC("+t+") "+this.operation+" ("+T+")":d=(d=d.replace(C," "+this.operation)).replace(D," ("+T+")"),m=L?"[ "+e+" "+this.operationToDisplay+" ("+c+")":"[ "+e+" "+this.operationToDisplay+" ("+c+") ]",L&&(p=p+" OR "+t+" IS NULL",m=m+" "+this.orButton.label+" "+e+"  is null]"),this.tabAllConditions.push({tabName:o,columnToStore:t,clause:p})):"DTIME"==i&&("in"!=this.operation&&"not in"!=this.operation||(T=T.replace(/(^,)|(,$)/gi,""),c=c.replace(/(^,)|(,$)/gi,""),"error_dateTime"==this.validateDateTimeField(c)&&(c="error_dateTime"),p=""==d?"TO_DATE("+t+") "+this.operation+" ("+T+")":d=(d=d.replace(C," "+this.operation)).replace(D," ("+T+")"),m=L?"[ "+e+" "+this.operationToDisplay+" ("+c+")":"[ "+e+" "+this.operationToDisplay+" ("+c+") ]",L&&(p=p+" OR "+t+" IS NULL",m=m+" "+this.orButton.label+" "+e+"  is null]"),this.tabAllConditions.push({tabName:o,columnToStore:t,clause:p})))}else if("SwtComboBox"==y){N=this.comboBox.selectedItem.content;var Q="",W="",j="",Z="";if("between"==this.operation){var K="",G="";"TYPETEXT"==this.comboBox.selectedItem.id?(c=this.textInput.text,Z=this.textInput.text,W="TT"):(c=this.comboBox.selectedItem.id,Z=this.comboBox.selectedItem.content),"TYPETEXT"==this.comboBox2.selectedItem.id?""==this.textInput2.text?c="":(b=this.textInput2.text,K=this.textInput2.text,j="TT"):(b=this.comboBox2.selectedItem.id,K=this.comboBox2.selectedItem.content),"NUM"!=this.comboBox.selectedItem.type&&"DECI"!=this.comboBox.selectedItem.type||"TT"!=W?(Q=c,p=t+" "+this.operation+" "+Q):(Q="To_number("+this.getNumberFromAmount(c)+")",p=t+" "+this.operation+" "+Q),p="NUM"!=this.comboBox2.selectedItem.type&&"DECI"!=this.comboBox2.selectedItem.type||"TT"!=j?p+" and "+(G=b):p+" and "+(G="To_number("+this.getNumberFromAmount(b)+")"),m="[ "+e+" "+this.operationToDisplay+" "+Z+" "+this.andButton.label+" "+K+" ]","P"==W||"P"==j?this.tabAllConditions.push({columnToStore:t,operation:this.operation,columnCodeValue:Q+" and "+G,columnValue:Q,columnValue2:G,typeCode:i,localValue:n,tabName:o,profileField:l,andOrString:this.andOrString,fieldExpression:s,clause:p,enumProfile:"Y",index:E,conditionId:h}):"B"==W||"B"==j?this.tabAllConditions.push({columnToStore:t,operation:this.operation,columnCodeValue:Q+" and "+G,columnValue:Q,columnValue2:G,typeCode:i,localValue:n,tabName:o,profileField:l,andOrString:this.andOrString,fieldExpression:s,clause:p,enumProfile:"B",index:E,conditionId:h}):this.tabAllConditions.push({columnToStore:t,operation:this.operation,columnCodeValue:Q+" and "+G,columnValue:Q,columnValue2:G,typeCode:i,localValue:n,tabName:o,profileField:l,andOrString:this.andOrString,fieldExpression:s,clause:p,enumProfile:"N",index:E,conditionId:h})}else"in"==this.operation||"exists"==this.operation?(c=this.comboBox.selectedItem.id,"LIST"==this.comboBox.selectedItem.type?p=t+" "+this.operation+" ("+c+")":"NUM"==this.comboBox.selectedItem.type?(c=c.replace(":value",this.comboBox.selectedItem.value),p=t+" "+this.operation+" ("+c+")"):"CHAR"==this.comboBox.selectedItem.type?("YN"!=r&&"YY"!=r||(c=c.replace(/:value/g,"'"+this.comboBox.selectedItem.value+"'"),p="upper("+t+") "+this.operation+" ("+c+")"),"NY"!=r&&"YY"!=r||(c=c.replace(/:value/g,"'"+String(this.comboBox.selectedItem.value).toUpperCase()+"'"),p=t+" "+this.operation+" ("+c+")"),"NN"==r&&(c=c.replace(/:value/g," TO_DATE('"+this.comboBox.selectedItem.value+"', '"+this.commonSysDateFormat+"')"),p=t+" "+this.operation+" ("+c+")")):"DATE"==this.comboBox.selectedItem.type&&(c=c.replace(/:value/g,"'"+this.comboBox.selectedItem.value+"'"),p=t+" "+this.operation+" ("+c+")"),Q="("+c.replace(/:column_code/g,t)+")",Z=this.comboBox.selectedItem.toString(),i=this.comboBox.selectedItem.type,m="[ "+e+" "+this.operationToDisplay+" "+Z+" ]","exists"==this.operation&&(p=(p=p.replace(t+" ","")).replace("upper("+t+") ","")),p=p.replace(/:column_code/g,t),this.tabAllConditions.push({columnToStore:t,operation:this.operation,columnCodeValue:Q,typeCode:i,localValue:n,tabName:o,profileField:l,andOrString:this.andOrString,fieldExpression:s,clause:p,enumProfile:"N",index:E,conditionId:h})):("TYPETEXT"==this.comboBox.selectedItem.id?(c=this.textInput.text,Z=this.textInput.text,N="TT",""==c&&"CHAR"==this.comboBox.selectedItem.type&&("="==this.operation?(Z="is null",this.operation="",this.operationToDisplay="",c="is null"):"<>"==this.operation&&(Z="is not null",this.operation="",this.operationToDisplay="",c="is not null"))):(c=this.comboBox.selectedItem.id,Z=this.comboBox.selectedItem.content),"NUM"!=this.comboBox.selectedItem.type&&"DECI"!=this.comboBox.selectedItem.type||"P"==N||"B"==N?"BIND_C"==this.comboBox.selectedItem.type?(Q="YY"==r||"NY"==r?"upper("+c+")":c,p="YY"==r||"YN"==r?(t="upper("+t+") ")+this.operation+" "+Q:t+" "+this.operation+" "+Q,this.varToBind.push(c)):"BIND_N"==this.comboBox.selectedItem.type?(Q=c,p=t+" "+this.operation+" "+Q,this.varToBind.push(c)):(""!=d&&(c=c.replace(/[']/g,"''")),Q="P"==N||"B"==N?c:" is null"==c||" is not null"==c?c:"YY"==r||"NY"==r?"'"+c.toUpperCase()+"'":"'"+c+"'",""==d?"YY"==r||"YN"==r?(p="upper("+t+") "+this.operation+" "+Q,t="upper("+t+") "):p=t+" "+this.operation+" "+Q:p=d=(d=d.replace(C," "+this.operation)).replace(D," "+Q)):(Q="To_number("+this.getNumberFromAmount(c)+")",T=" "+c,p=t+" "+this.operation+" "+Q),m="[ "+e+" "+this.operationToDisplay+" "+Z+" ]","P"==N?this.tabAllConditions.push({columnToStore:t,operation:this.operation,columnCodeValue:Q,typeCode:i,localValue:n,tabName:o,profileField:l,andOrString:this.andOrString,fieldExpression:s,clause:p,enumProfile:"Y",index:E,conditionId:h}):"B"==N?this.tabAllConditions.push({columnToStore:t,operation:this.operation,columnCodeValue:Q,typeCode:i,localValue:n,tabName:o,profileField:l,andOrString:this.andOrString,fieldExpression:s,clause:p,enumProfile:"B",index:E,conditionId:h}):this.tabAllConditions.push({columnToStore:t,operation:this.operation,columnCodeValue:Q,typeCode:i,localValue:n,tabName:o,profileField:l,andOrString:this.andOrString,fieldExpression:s,clause:p,enumProfile:"N",index:E,conditionId:h}))}else if("SwtDateField"==y&&"DTIME"!=i){var z="System date",$="",X="",tt={columnToStore:"",columnValue:""};if(""!=this.dateField.text){if("error_date"==this.validateDateField(this.dateField.text))return void(c="error_date");"DD-MMM-YYYY"==this.sysdateformat.toUpperCase()?(X=this.transformDate2(this.dateField.dropdown.selectedDate),c=a.j.formatDate(this.dateField.parseDate(X,this.sysdateformat.toUpperCase()),this.commonSysDateFormat)):c=a.j.formatDate(this.dateField.parseDate(this.dateField.text,this.sysdateformat.toUpperCase()),this.commonSysDateFormat),""==c&&(c="false")}var et=void 0;if("between"==this.operation){if(""==(et=this.dateField2.text)?c="":("DD-MMM-YYYY"==this.sysdateformat.toUpperCase()?(X="",X=this.transformDate2(this.dateField2.dropdown.selectedDate),c=a.j.formatDate(this.dateField2.parseDate(X,this.sysdateformat.toUpperCase()),this.commonSysDateFormat)):c=a.j.formatDate(this.dateField2.parseDate(this.dateField.text,this.sysdateformat.toUpperCase()),this.commonSysDateFormat),""==et&&(c="false")),this.sysDateActive&&(this.tolerenceNumStep.value>0?$=" + "+this.tolerenceNumStep.value:this.tolerenceNumStep.value<0&&($=""+this.tolerenceNumStep.value),et="SYSDATE"),""==d)p=this.sysDateActive?this.getOracleDateFormat(t,c,et,this.operation,$,u,tt):this.getOracleDateFormat(t,c,et,this.operation,"",u,tt),c=tt.columnValue,t=tt.columnToStore;else{var it="";I=" TO_DATE('"+c+"', '"+this.commonSysDateFormat+"')",it=this.sysDateActive?" TRUNC(SYSDATE)"+$:" TO_DATE('"+et+"', '"+this.commonSysDateFormat+"')",p=(d=(d=d.replace(C," "+this.operation)).replace(D,I))+" and"+it}this.sysDateActive?($=this.tolerenceNumStep.value>0?" + "+this.tolerenceNumStep.value:this.tolerenceNumStep.value<0?String(this.tolerenceNumStep.value):"",m="[ "+e+" "+this.operationToDisplay+" "+this.dateField.text+" "+this.andButton.label+" "+z+$+" ]"):m="[ "+e+" "+this.operationToDisplay+" "+this.dateField.text+" "+this.andButton.label+" "+this.dateField2.text+" ]",this.tabAllConditions.push({columnToStore:t,operation:this.operation,columnCodeValue:c,typeCode:i,localValue:n,tabName:o,profileField:l,andOrString:this.andOrString,fieldExpression:s,clause:p,enumProfile:"N",index:E,conditionId:h})}else""!=c||this.sysDateActive?"false"!=c&&(I=' TO_DATE("" + columnValue + "',S=this.dateField.text):"="==this.operation?(I="IS NULL",S=" is null",this.operationToDisplay="",c="is null"):"<>"==this.operation&&(I="IS NOT NULL",S="is not null",this.operationToDisplay="",c="is not null"),this.sysDateActive&&(this.tolerenceNumStep.value>0?$=" + "+this.tolerenceNumStep.value:this.tolerenceNumStep.value<0&&($=""+this.tolerenceNumStep.value),c="SYSDATE",I=""!=$?"TRUNC(SYSDATE"+$+")":"TRUNC(SYSDATE)"),""==d?(p=this.sysDateActive?this.getOracleDateFormat(t,c,et,this.operation,$,u,tt):this.getOracleDateFormat(t,c,et,this.operation,"",u,tt),c=tt.columnValue,t=tt.columnToStore):p=d=(d=d.replace(C," "+this.operation)).replace(D," "+I),this.sysDateActive?($=this.tolerenceNumStep.value>0?" + "+String(this.tolerenceNumStep.value):this.tolerenceNumStep.value<0?String(this.tolerenceNumStep.value):"",m="[ "+e+" "+this.operationToDisplay+" "+z+$+" ]"):m="[ "+e+" "+this.operationToDisplay+" "+S+" ]",this.tabAllConditions.push({columnToStore:t,operation:-1!=c.indexOf("is null")||-1!=c.indexOf("is not null")?"":this.operation,columnCodeValue:c,typeCode:i,localValue:n,tabName:o,profileField:l,andOrString:this.andOrString,fieldExpression:s,clause:p,enumProfile:"N",index:E,conditionId:h})}else if(B.indexOf("SwtTextInput")>-1&&B.indexOf("SwtDateField")>-1){z="System date",$="",X="",tt={columnToStore:"",columnValue:""};if(""!=this.dateField.text){if("error_date"==this.validateDateField(this.dateField.text))return void(c="error_date");"DD-MMM-YYYY"==this.sysdateformat.toUpperCase()?(X=this.transformDate2(this.dateField.dropdown.selectedDate),c=a.j.formatDate(this.dateField.parseDate(X,this.sysdateformat.toUpperCase()),this.commonSysDateFormat)+" "+this.timeField.text?this.timeField.text:"00:00"):c=""!=this.timeField.text?a.j.formatDate(this.dateField.parseDate(this.dateField.text,this.sysdateformat.toUpperCase()),this.commonSysDateFormat)+this.timeField.text:a.j.formatDate(this.dateField.parseDate(this.dateField.text,this.sysdateformat.toUpperCase()),this.commonSysDateFormat),""==c&&(c="false")}et=void 0;if("between"==this.operation){if(""==(et=this.dateField2.text+this.timeField.text?this.timeField.text:"00:00")?c="":("DD-MMM-YYYY"==this.sysdateformat.toUpperCase()?(X="",X=this.transformDate2(this.dateField2.dropdown.selectedDate),c=a.j.formatDate(this.dateField2.parseDate(X,this.sysdateformat.toUpperCase()),this.commonSysDateFormat)+" "+this.timeField.text):c=a.j.formatDate(this.dateField2.parseDate(this.dateField.text,this.sysdateformat.toUpperCase()),this.commonSysDateFormat)+" "+this.timeField.text,""==et&&(c="false")),""==d)p=this.getOracleDateFormat(t,c,et,this.operation,"",u,tt),c=tt.columnValue,t=tt.columnToStore;else{it="";I=" TO_DATE('"+c+"', '"+this.commonSysDateFormat+"HH24:MI')",it=this.sysDateActive?" TRUNC(SYSDATE)"+$:" TO_DATE('"+et+"', '"+this.commonSysDateFormat+"HH24:MI')",p=(d=(d=d.replace(C," "+this.operation)).replace(D,I))+" and"+it}this.sysDateActive?($=this.tolerenceNumStep.value>0?" + "+this.tolerenceNumStep.value:this.tolerenceNumStep.value<0?String(this.tolerenceNumStep.value):"",m="[ "+e+" "+this.operationToDisplay+" "+this.dateField.text+" "+this.timeField.text+" "+this.andButton.label+" "+z+$+" ]"):m="[ "+e+" "+this.operationToDisplay+" "+this.dateField.text+" "+this.timeField.text+" "+this.andButton.label+" "+this.dateField2.text+" ]",this.tabAllConditions.push({columnToStore:t,operation:this.operation,columnCodeValue:c,typeCode:i,localValue:n,tabName:o,profileField:l,andOrString:this.andOrString,fieldExpression:s,clause:p,enumProfile:"N",index:E,conditionId:h})}else""!=c||this.sysDateActive?"false"!=c&&(I=" TO_DATE('"+c+"', '"+this.commonSysDateFormat+" HH24:MI')",S=this.dateField.text+" "+this.timeField.text):"="==this.operation?(I="IS NULL",S=" is null",this.operationToDisplay="",c="is null"):"<>"==this.operation&&(I="IS NOT NULL",S="is not null",this.operationToDisplay="",c="is not null"),""==d?(p=this.sysDateActive?this.getOracleDateTimeFormat(t,c,et,this.operation,$,u,tt):this.getOracleDateTimeFormat(t,c,et,this.operation,"",u,tt),c=tt.columnValue,t=tt.columnToStore):p=d=(d=d.replace(C," "+this.operation)).replace(D," "+I),this.sysDateActive?($=this.tolerenceNumStep.value>0?" + "+String(this.tolerenceNumStep.value):this.tolerenceNumStep.value<0?String(this.tolerenceNumStep.value):"",m="[ "+e+" "+this.operationToDisplay+" "+z+$+" ]"):m="[ "+e+" "+this.operationToDisplay+" "+S+" ]",this.tabAllConditions.push({columnToStore:t,operation:-1!=c.indexOf("is null")||-1!=c.indexOf("is not null")?"":this.operation,columnCodeValue:c,typeCode:i,localValue:n,tabName:o,profileField:l,andOrString:this.andOrString,fieldExpression:s,clause:p,enumProfile:"N",index:E,conditionId:h})}if(""==c&&"like"!=this.operation&&"Not Like"!=this.operation)this.swtAlert.warning(a.Wb.getPredictMessage("queryBuilderScreen.alert.missingProperty",null)+" "+e,"Warning");else if("false"==c)this.swtAlert.warning(a.Wb.getPredictMessage("queryBuilderScreen.alert.checkEnteredValue")+" "+e,"Warning");else if("error_dateTime"==c)this.swtAlert.warning(a.Wb.getPredictMessage("queryBuilderScreen.alert.dateTimeFormat")+" "+this.sysdateformat.toUpperCase()+" HH: MM");else if("error_time"==c)this.swtAlert.warning(a.Wb.getPredictMessage("alert.validTime"));else if(""!=c&&"false"!=c&&"add"==this.screenName){""==this.queryToDisplay?(this.arrayToDisplayQuery.push(m),this.arrayToExecuteQuery.push(p),"Y"!=l&&"N"!=l||this.agregationFunc.push(t),"P"!=N&&"B"!=N||this.agregationFunc.push(this.tabAllConditions[this.tabAllConditions.length-1].columnCodeValue)):(this.arrayToDisplayQuery.push(" "+m),this.arrayToExecuteQuery.push(" "+p),"Y"==l&&this.agregationFunc.push(t),"P"!=N&&"B"!=N||this.agregationFunc.push(this.tabAllConditions[this.tabAllConditions.length-1].columnCodeValue)),this.queryToDisplay="",this.queryToExecute="";for(var ot=0;ot<this.arrayToDisplayQuery.length;ot++)this.queryToDisplay=this.queryToDisplay+this.arrayToDisplayQuery[ot],this.queryToExecute=this.queryToExecute+this.arrayToExecuteQuery[ot];this.queryToDisplay2=this.queryToExecute,this.undoButton.enabled=!0,this.undoButton.buttonMode=!0,this.resetButton.enabled=!0,this.resetButton.buttonMode=!0,this.listCriteria.enabled=!1,this.leftParentheseButton.enabled=!1,this.leftParentheseButton.buttonMode=!1,this.andOrString="",this.countRightP%this.countLeftP==0?(this.rightParentheseButton.enabled=!1,this.rightParentheseButton.buttonMode=!1,this.okButton.enabled=!0,this.okButton.buttonMode=!0):(this.rightParentheseButton.enabled=!0,this.rightParentheseButton.buttonMode=!0,this.okButton.enabled=!1,this.okButton.buttonMode=!1),this.clearContraints()}else if(""!=c&&"false"!=c&&"change"==this.screenName){var nt="",lt="",st="";this.queryToDisplay=this.replaceConditionToDisplayById(h-1,m),this.labelQuery.text=m,""!=this.previousTypeCode&&(i=this.previousTypeCode),st=this.listCriteria.selectedItem.label.condFieldName,"exists"==this.listCriteria.selectedItem.label.operatorId?nt=this.listCriteria.selectedItem.label.operatorId+" "+this.listCriteria.selectedItem.label.fieldValue:"DATE"==this.listCriteria.selectedItem.label.typeCode&&("DATE"==this.previousTypeCode||""==this.previousTypeCode)||"DATE"==this.previousTypeCode||"DTIME"==this.listCriteria.selectedItem.label.typeCode&&("DTIME"==this.previousTypeCode||""==this.previousTypeCode)||"DTIME"==this.previousTypeCode?nt=this.listCriteria.selectedItem.label.fieldValue:this.listCriteria.selectedItem.label.clause?(this.listCriteria.selectedItem.label.operatorId,nt=this.listCriteria.selectedItem.label.clause,lt=" "+this.listCriteria.selectedItem.label.clause):""==this.listCriteria.selectedItem.label.operatorId?(nt=st+"  "+this.listCriteria.selectedItem.label.fieldValue,lt=st+" "+this.listCriteria.selectedItem.label.fieldValue):(nt=st+" "+this.listCriteria.selectedItem.label.operatorId+"  "+this.listCriteria.selectedItem.label.fieldValue,lt=st+" "+this.listCriteria.selectedItem.label.operatorId+" "+this.listCriteria.selectedItem.label.fieldValue);var at=this.replaceConditionToExecuteById(h-1,nt,lt,p);""==at&&"DECI"==i&&(st=this.listCriteria.selectedItem.label.condFieldName+" * :prate","exists"==this.listCriteria.selectedItem.label.operatorId?(nt=this.listCriteria.selectedItem.label.operatorId+"  "+this.listCriteria.selectedItem.label.fieldValue,lt=this.listCriteria.selectedItem.label.operatorId+" "+this.listCriteria.selectedItem.label.fieldValue):(nt=st+" "+this.listCriteria.selectedItem.label.operatorId+"  "+this.listCriteria.selectedItem.label.fieldValue,lt=st+" "+this.listCriteria.selectedItem.label.operatorId+" "+this.listCriteria.selectedItem.label.fieldValue),at=this.replaceConditionToExecuteById(h-1,nt,lt,p)),this.queryToExecute=at,this.clearContraints(),this.labelQuery.text="",this.okButton.enabled=!0,this.okButton.buttonMode=!0,this.resetButton.enabled=!0,this.resetButton.buttonMode=!0;var rt=!1,ht=-1;for(var ut in this.originalJSONList)this.originalJSONList[ut].code==this.selectedItemsList&&null!=this.selectedItemsList&&(this.originalJSONList[ut].fieldValue=a.Z.trim(String(this.tabAllConditions[this.tabAllConditions.length-1].columnCodeValue)),this.originalJSONList[ut].operatorId=this.tabAllConditions[this.tabAllConditions.length-1].operation,this.originalJSONList[ut].conditionId=this.tabAllConditions[this.tabAllConditions.length-1].conditionId,this.originalJSONList[ut].condFieldName=a.Z.trim(this.tabAllConditions[this.tabAllConditions.length-1].columnToStore),this.originalJSONList[ut].nextCondition=this.tabAllConditions[this.tabAllConditions.length-1].andOrString,this.originalJSONList[ut].profileFieldValue=this.tabAllConditions[this.tabAllConditions.length-1].profileFieldValue);for(var dt=0;dt<this.tabAllConditions.length-1;dt++)if(this.tabAllConditions[dt].conditionId==h){rt=!0,ht=dt;break}rt&&this.tabAllConditions.splice(ht,1);var ct=[];for(var ut in this.originalJSONList)"Y"==this.originalJSONList[ut].toChange&&ct.push({label:this.originalJSONList[ut]});this.gridJSONList={row:ct,size:ct.length},this.listCriteria.gridData=this.gridJSONList}}catch(bt){this.logger.error("method [createQuery] - error : ",bt,"- errorLocation :",this.errorLocation),a.Wb.logError(bt,a.Wb.SYSTEM_MODULE_ID,this.commonService.getQualifiedClassName(this),"createQuery",this.errorLocation)}this.logger.info("method [createQuery] - END ")},e.prototype.transformDate=function(t){var e="";try{this.logger.info("method [transformDate] - START ");for(var i=t.substr(0,2),o=t.substr(3,3),n=t.substr(7,t.length),l=0;l<this.nameMonths.length;l++)this.nameMonths[l].substr(0,3)==o&&(e=l<10?i+"/0"+(l+1)+"/"+n:i+"/"+(l+1)+"/"+n)}catch(s){this.logger.error("method [transformDate] - error : ",s,"- errorLocation :",this.errorLocation),a.Wb.logError(s,a.Wb.SYSTEM_MODULE_ID,this.commonService.getQualifiedClassName(this),"transformDate",this.errorLocation)}return this.logger.info("method [transformDate] - END "),e},e.prototype.doOpenCloseParentheses=function(){try{this.logger.info("method [doOpenCloseParentheses] - START ");var t=Object(a.ic.getFocus()),e=void 0;e="string"==typeof t.id?t.id:t.id&&"string"==typeof t.id.id?t.id.id:"",this.okButton.enabled=!1,this.okButton.buttonMode=!1,"T"==this.recUnitGroupDisplay&&(this.execStatus="N"),"leftParentheseButton"==e?this.openParenthese():"rightParentheseButton"==e&&this.closeParenthese()}catch(i){this.logger.error("method [doOpenCloseParentheses] - error : ",i,"- errorLocation :",this.errorLocation),a.Wb.logError(i,a.Wb.SYSTEM_MODULE_ID,this.commonService.getQualifiedClassName(this),"doOpenCloseParentheses",this.errorLocation)}this.logger.info("method [doOpenCloseParentheses] - END ")},e.prototype.openParenthese=function(){try{this.logger.info("method [openParenthese] - START "),this.countLeftP++,""==this.queryToDisplay?(this.queryToExecute="",this.arrayToDisplayQuery.push("("),this.arrayToExecuteQuery.push("("),this.queryToDisplay=this.queryToDisplay+"(",this.queryToExecute=this.queryToExecute+"("):(this.arrayToDisplayQuery.push(" ("),this.arrayToExecuteQuery.push(" ("),this.queryToDisplay=this.queryToDisplay+" (",this.queryToExecute=this.queryToExecute+" ("),this.queryToDisplay2=this.queryToExecute,this.undoButton.enabled=!0,this.undoButton.buttonMode=!0,this.resetButton.enabled=!0,this.resetButton.buttonMode=!0,this.listCriteria.enabled=!0,this.rightParentheseButton.enabled=!1,this.rightParentheseButton.buttonMode=!1,this.andButton.enabled=!1,this.andButton.buttonMode=!1,this.orButton.enabled=!1,this.orButton.buttonMode=!1}catch(t){this.logger.error("method [openParenthese] - error : ",t,"- errorLocation :",this.errorLocation),a.Wb.logError(t,a.Wb.SYSTEM_MODULE_ID,this.commonService.getQualifiedClassName(this),"openParenthese",this.errorLocation)}this.logger.info("method [openParenthese] - END ")},e.prototype.closeParenthese=function(){try{this.logger.info("method [closeParenthese] - START "),this.countRightP++,this.arrayToDisplayQuery.push(" )"),this.arrayToExecuteQuery.push(" )"),this.queryToDisplay=this.queryToDisplay+" )",this.queryToExecute=this.queryToExecute+" )",this.queryToDisplay2=this.queryToExecute,this.countRightP%this.countLeftP==0?(this.rightParentheseButton.enabled=!1,this.rightParentheseButton.buttonMode=!1,this.okButton.enabled=!0,this.okButton.buttonMode=!0,"T"==this.recUnitGroupDisplay&&(this.execStatus="Y")):(this.rightParentheseButton.enabled=!0,this.rightParentheseButton.buttonMode=!0,this.okButton.enabled=!1,this.okButton.buttonMode=!1,"T"==this.recUnitGroupDisplay&&(this.execStatus="N"))}catch(t){this.logger.error("method [closeParenthese] - error : ",t,"- errorLocation :",this.errorLocation),a.Wb.logError(t,a.Wb.SYSTEM_MODULE_ID,this.commonService.getQualifiedClassName(this),"closeParenthese",this.errorLocation)}this.logger.info("method [closeParenthese] - END ")},e.prototype.addOperation=function(){try{this.logger.info("method [addOperation] - START ")," and"!=this.arrayToExecuteQuery[this.arrayToExecuteQuery.length-1]&&" or"!=this.arrayToExecuteQuery[this.arrayToExecuteQuery.length-1]||this.previous(),this.arrayToDisplayQuery.push(" "+this.andOrStringToDisplay),this.arrayToExecuteQuery.push(" "+this.andOrString),this.queryToDisplay=this.queryToDisplay+" "+this.andOrStringToDisplay,this.queryToExecute=this.queryToExecute+" "+this.andOrString,this.queryToDisplay2=this.queryToDisplay2+" "+this.andOrString,this.okButton.enabled=!1,this.okButton.buttonMode=!1,"T"==this.recUnitGroupDisplay&&(this.execStatus="N"),this.undoButton.enabled=!0,this.resetButton.buttonMode=!0,this.resetButton.enabled=!0,this.undoButton.buttonMode=!0,this.leftParentheseButton.enabled=!0,this.leftParentheseButton.buttonMode=!0,this.listCriteria.enabled=!0}catch(t){this.logger.error("method [addOperation] - error : ",t,"- errorLocation :",this.errorLocation),a.Wb.logError(t,a.Wb.SYSTEM_MODULE_ID,this.commonService.getQualifiedClassName(this),"addOperation",this.errorLocation)}this.logger.info("method [addOperation] - END ")},e.prototype.previous=function(){this.clearContraints();try{if(this.logger.info("method [previous] - START "),this.arrayToDisplayQuery.length>0){" )"==this.arrayToExecuteQuery[this.arrayToExecuteQuery.length-1]&&this.countRightP--,"("!=this.arrayToExecuteQuery[this.arrayToExecuteQuery.length-1]&&" ("!=this.arrayToExecuteQuery[this.arrayToExecuteQuery.length-1]||this.countLeftP--;var t=String(this.arrayToDisplayQuery[this.arrayToDisplayQuery.length-1]),e=t.substr(t.length-2,t.length),i=t.substr(0,2);if("[ "==i&&" ]"==e||" ["==i&&" ]"==e){if(this.tableToJoin.length>0){for(var o=this.tabAllConditions[this.tabAllConditions.length-1].tabName,n=!1,l=0;l<this.tabAllConditions.length-1;l++){this.tabAllConditions[l].tabName==o&&(n=!0);break}n||(this.tableToJoin.splice(this.tableToJoin.length-1,1),this.aliasList.splice(this.aliasList.length-1,1),this.index=this.index-1)}this.tabAllConditions.splice(this.tabAllConditions.length-1,1)}this.arrayToDisplayQuery.splice(this.arrayToDisplayQuery.length-1,1),this.arrayToExecuteQuery.splice(this.arrayToExecuteQuery.length-1,1),this.queryToDisplay="",this.queryToExecute="";for(var s=0;s<this.arrayToDisplayQuery.length;s++)this.queryToDisplay=this.queryToDisplay+this.arrayToDisplayQuery[s],this.queryToExecute=this.queryToExecute+this.arrayToExecuteQuery[s];if(this.queryToDisplay2=this.queryToExecute,this.arrayToDisplayQuery.length>0)if(this.undoButton.enabled=!0,this.undoButton.buttonMode=!0,this.resetButton.enabled=!0,this.resetButton.buttonMode=!0," and"==this.arrayToExecuteQuery[this.arrayToExecuteQuery.length-1]||" or"==this.arrayToExecuteQuery[this.arrayToExecuteQuery.length-1])this.okButton.enabled=!1,this.okButton.buttonMode=!1,"T"==this.recUnitGroupDisplay&&(this.execStatus="N"),this.listCriteria.enabled=!0,this.leftParentheseButton.enabled=!0,this.leftParentheseButton.buttonMode=!0,this.rightParentheseButton.enabled=!1,this.rightParentheseButton.buttonMode=!1,this.andButton.enabled=!0,this.andButton.buttonMode=!0,this.orButton.enabled=!0,this.orButton.buttonMode=!0;else{var r=String(this.arrayToDisplayQuery[this.arrayToDisplayQuery.length-1]),h=r.substr(r.length-1,r.length);"]"==h||")"==h?(this.listCriteria.enabled=!1,this.leftParentheseButton.enabled=!1,this.leftParentheseButton.buttonMode=!1,this.andButton.enabled=!0,this.andButton.buttonMode=!0,this.orButton.enabled=!0,this.orButton.buttonMode=!0,this.countRightP%this.countLeftP==0?(this.okButton.enabled=!0,this.okButton.buttonMode=!0,"T"==this.recUnitGroupDisplay&&(this.execStatus="Y"),this.rightParentheseButton.enabled=!1,this.rightParentheseButton.buttonMode=!1):(this.okButton.enabled=!1,this.okButton.buttonMode=!1,"T"==this.recUnitGroupDisplay&&(this.execStatus="N"),this.saveButton.enabled=!1,this.saveButton.buttonMode=!1,this.rightParentheseButton.enabled=!0,this.rightParentheseButton.buttonMode=!0)):"("==h?(this.okButton.enabled=!1,this.okButton.buttonMode=!1,"T"==this.recUnitGroupDisplay&&(this.execStatus="N"),this.saveButton.enabled=!1,this.saveButton.buttonMode=!1,this.listCriteria.enabled=!0,this.leftParentheseButton.enabled=!0,this.leftParentheseButton.buttonMode=!0,this.rightParentheseButton.enabled=!1,this.rightParentheseButton.buttonMode=!1,this.andButton.enabled=!1,this.andButton.buttonMode=!1,this.orButton.enabled=!1,this.orButton.buttonMode=!1):(this.okButton.enabled=!1,this.okButton.buttonMode=!1,"T"==this.recUnitGroupDisplay&&(this.execStatus="N"),this.saveButton.enabled=!1,this.saveButton.buttonMode=!1,this.listCriteria.enabled=!1,this.leftParentheseButton.enabled=!1,this.leftParentheseButton.buttonMode=!1,this.rightParentheseButton.enabled=!1,this.rightParentheseButton.buttonMode=!1,this.andButton.enabled=!1,this.andButton.buttonMode=!1,this.orButton.enabled=!1,this.orButton.buttonMode=!1)}else this.undoButton.enabled=!1,this.undoButton.buttonMode=!1,this.resetButton.enabled=!1,this.resetButton.buttonMode=!1,this.listCriteria.enabled=!0,this.andButton.enabled=!1,this.andButton.buttonMode=!1,this.orButton.enabled=!1,this.orButton.buttonMode=!1,this.okButton.enabled=!1,this.okButton.buttonMode=!1,"T"==this.recUnitGroupDisplay&&(this.execStatus="N"),this.saveButton.enabled=!1,this.saveButton.buttonMode=!1,this.leftParentheseButton.enabled=!0,this.leftParentheseButton.buttonMode=!0,this.rightParentheseButton.enabled=!1,this.rightParentheseButton.buttonMode=!1}}catch(u){this.logger.error("method [previous] - error : ",u,"- errorLocation :",this.errorLocation),a.Wb.logError(u,a.Wb.SYSTEM_MODULE_ID,this.commonService.getQualifiedClassName(this),"previous",this.errorLocation)}this.logger.info("method [previous] - END ")},e.prototype.clearContraints=function(){var t=this;try{this.logger.info("method [init] - START "),this.propertiesLabel.text="",this.operator.text="",this.operation="",this.operationToDisplay="",this.listCriteria.selectedIndex=-1,this.equalButton.enabled=!1,this.equalButton.buttonMode=!1,this.differentButton.enabled=!1,this.differentButton.buttonMode=!1,this.higherButton.enabled=!1,this.higherButton.buttonMode=!1,this.higherequalButton.enabled=!1,this.higherequalButton.buttonMode=!1,this.lowerButton.enabled=!1,this.lowerButton.buttonMode=!1,this.lowerequalButton.enabled=!1,this.lowerequalButton.buttonMode=!1,this.likeButton.enabled=!1,this.likeButton.buttonMode=!1,this.notLikeButton.enabled=!1,this.notLikeButton.buttonMode=!1,this.inButton.enabled=!1,this.inButton.buttonMode=!1,this.notinButton.enabled=!1,this.notinButton.buttonMode=!1,this.betweenButton.enabled=!1,this.betweenButton.buttonMode=!1,this.andButton.enabled=!0,this.andButton.buttonMode=!0,this.orButton.enabled=!0,this.orButton.buttonMode=!0,this.addButton.enabled=!1,this.addButton.buttonMode=!1,this.inListButton.enabled=!1,this.inListButton.buttonMode=!1,this.changeButton.buttonMode=!1,this.changeButton.enabled=!1,this.changeCritButton.enabled=!1,this.hBoxContainer.contains(this.listValuesButton)&&(this.listValuesButton.click=function(){t.addListValuesEventHandler(event)}),this.hBoxContainer.contains(this.dateField),this.hBoxContainer.contains(this.dateField2)&&(this.dateField2.open=function(){t.datePickerOn(event)}),this.hBoxContainer.removeAllChildren()}catch(e){this.logger.error("method [popupClosedEventHandler] - error : ",e,"- errorLocation :",this.errorLocation),a.Wb.logError(e,a.Wb.SYSTEM_MODULE_ID,this.commonService.getQualifiedClassName(this),"popupClosedEventHandler",this.errorLocation)}this.logger.info("method [popupClosedEventHandler] - END ")},e.prototype.reset=function(t){try{for(var e in this.logger.info("method [reset] - START "),"add"==this.screenName?(this.queryToExecute="",this.queryToDisplay="",this.queryToDisplay2=""):this.parentDocument&&(this.queryToExecute=this.parentDocument.searchQuery,this.queryToDisplay=this.parentDocument.queryToDisplay),this.labelQuery.text="",this.originalJSONList=this.firstJSONList,this.firstJSONList)null!=this.firstJSONList[e].toChange&&(this.firstJSONList[e].toChange="Y");if(this.gridJSONList=this.firstJSONList,this.countLeftP=1,this.countRightP=1,this.okButton.enabled=!1,this.okButton.buttonMode=!1,this.undoButton.enabled=!1,this.undoButton.buttonMode=!1,this.resetButton.enabled=!1,this.resetButton.buttonMode=!1,this.addButton.enabled=!1,this.addButton.buttonMode=!1,this.listCriteria.enabled=!0,this.arrayToDisplayQuery=[],this.arrayToExecuteQuery=[],this.tabAllConditions=[],this.tableToJoin=[],this.index=0,this.andOrString="",this.andOrStringToDisplay="",this.clearContraints(),this.andButton.enabled=!1,this.andButton.buttonMode=!1,this.orButton.enabled=!1,this.orButton.buttonMode=!1,this.leftParentheseButton.enabled=!0,this.leftParentheseButton.buttonMode=!0,this.listCriteria.gridData=this.firstGridData,this.tabAllConditions=[],this.previousCondId=0,this.previousTypeCode="",this.filtringGrid(""),window.opener&&window.opener.instanceElement){var i;i=window.opener.instanceElement.getParamsFromParent(),this.screenName=i[0].screenName,i[0].tabAllConditions&&i[0].tabAllConditions.length>0&&(this.tabAllConditions=JSON.parse(i[0].tabAllConditions)),i[0].tableToJoin&&i[0].tableToJoin.length>0&&(this.tableToJoin=JSON.parse(i[0].tableToJoin)),i[0].queryToExecute&&(this.queryToExecute=i[0].queryToExecute),i[0].queryToDisplay&&(this.queryToDisplay=i[0].queryToDisplay)}}catch(o){console.log("error resttt",o)}},e.prototype.executeQuery=function(t){var e,i,o=this;try{if(""!=this.queryToExecute){if("change"==this.screenName){this.tableToJoin=[];for(var n=0,l=0;l<this.gridJSONList.size;l++){var s=void 0;void 0!==this.gridJSONList.row[l].label.code&&(e=this.gridJSONList.row[l].label.code);var r=!1;if(void 0!==this.gridJSONList.row[l].label.tableName&&(i=this.gridJSONList.row[l].label.tableName),e.indexOf(".")>=0){for(var h=0;h<this.tableToJoin.length;h++)if(String(this.tableToJoin[h]).indexOf(i+" ")>=0){r=!0;break}r||(s=e.substr(0,e.indexOf(".")),this.tableToJoin[n]=i+" "+s,n++)}else{for(var u=0;u<this.tableToJoin.length;u++)if(String(this.tableToJoin[u]).indexOf(this.tabName)>=0){r=!0;break}r||(this.tableToJoin[n]=this.tabName,n++)}}this.agregationFunc=[];for(var d=0;d<this.tabAllConditions.length;d++)"Y"==this.tabAllConditions[d].profileField&&this.agregationFunc.push(this.tabAllConditions[d].columnToStore),"Y"!=this.tabAllConditions[d].enumProfile&&"B"!=this.tabAllConditions[d].enumProfile||this.agregationFunc.push(this.tabAllConditions[d].columnCodeValue);for(var c=0;c<this.gridJSONList.length;c++)"Y"==this.gridJSONList[c].slickgrid_rowcontent.label.profileField&&this.agregationFunc.push(this.gridJSONList[c].slickgrid_rowcontent.label.code),"Y"==this.gridJSONList[c].slickgrid_rowcontent.label.profileFieldValue&&this.agregationFunc.push(this.gridJSONList[c].slickgrid_rowcontent.label.fieldValue),"B"==this.gridJSONList[c].slickgrid_rowcontent.label.profileFieldValue&&this.agregationFunc.push(this.gridJSONList[c].slickgrid_rowcontent.label.fieldValue);this.queryToExecute.indexOf(" where (")>=0?this.queryToExecute=this.queryToExecute.substr(this.queryToExecute.indexOf(" where ")+8,this.queryToExecute.length):this.queryToExecute=this.queryToExecute.substr(this.queryToExecute.indexOf(" where ")+7,this.queryToExecute.length),this.queryToExecute=this.queryToExecute.replace("M.message_id = T.message_id and T.client_id = C.client_id and (",""),this.queryToExecute=this.queryToExecute.replace("M.message_id = T.message_id and (",""),this.queryToExecute=this.queryToExecute.replace("T.client_id = C.client_id and (",""),this.queryToExecute=this.queryToExecute.replace("M.message_id = T.message_id and T.client_id = C.client_id and (",""),this.queryToExecute=this.queryToExecute.replace("T.client_id = C.client_id and (",""),this.queryToExecute=this.queryToExecute.substr(0,this.queryToExecute.length-1)}this.tabNames=this.tableToJoin[0];for(var b=1;b<this.tableToJoin.length;b++)this.tabNames=this.tabNames+", "+this.tableToJoin[b];var p=this.queryToExecute;this.queryToExecute="select count(*) from "+this.tabNames+" where ("+p+")";var m=this.queryToExecute;if(m=m.replace(" :prate "," 1 "),"Y"==this.externalFields&&0!=this.agregationFunc.length)for(var g=!1,y=0;y<this.agregationFunc.length;y++){g=!1;for(var B=0;B<this.tabAllConditions.length;B++)this.tabAllConditions[B].columnToStore==this.agregationFunc[y]&&(g=!0,"DATE"==this.tabAllConditions[B].typeCode?(m=m.replace(this.agregationFunc[y]," SYSDATE "),this.queryToExecute=this.queryToExecute.replace("TRUNC("+this.agregationFunc[y]+")"," TO_DATE("+this.agregationFunc[y]+", '"+this.commonSysDateFormat+"')")):m=m.replace(this.agregationFunc[y]," 1 ")),this.tabAllConditions[B].columnCodeValue==this.agregationFunc[y]&&(g=!0,"between"==this.tabAllConditions[B].operation&&(m=(m=m.replace(this.tabAllConditions[B].columnValue," 1 ")).replace(this.tabAllConditions[B].columnValue2," 1 "))),g||"change"!=this.screenName||(m=m.replace(this.agregationFunc[y]," 1 "))}if(0!=this.varToBind.length)for(var x=0;x<this.varToBind.length;x++)m=m.replace(this.varToBind[x]," '1' ");this.queryToExecute=this.verifyQuery(this.queryToExecute),this instanceof a.dc&&(this.result=this.queryToDisplay);var f=a.Z.encode64(this.queryToExecute);m=this.verifyQuery(m),this.actionMethod="method=validateQuery",this.actionPath="expressionBuilderPCM.do?",this.requestParams.sQuery=f,this.inputData.cbResult=function(t){o.validateQueryResult(t)},this.inputData.url=this.baseURL+this.actionPath+this.actionMethod,this.inputData.encodeURL=!0,this.inputData.send(this.requestParams)}else this.parentDocument?this.parentDocument.searchQuery="":window.opener&&window.opener.instanceElement&&window.opener.instanceElement.saveRuleDetails([],[],"","")}catch(T){console.log("error in execu query",T)}},e.prototype.setParamsFromParents=function(){this.screenName="add",alert(this.screenName)},e.prototype.verifyQuery=function(t){try{t=t.replace(/\(  \)/gi,"")}catch(e){console.log("error in verifyQuery",e)}return t},e.prototype.validateQueryResult=function(t){try{if(this.inputData.isBusy())this.inputData.cbStop();else{var e=new a.L;if(e.setInputJSON(t),e.getRequestReplyStatus()){try{if(this.parentDocument){this.parentDocument.searchQuery=this.queryToExecute,this.parentDocument.queryToDisplay=this.queryToDisplay,this.parentDocument.tableToJoin=[];for(var i=0;i<this.tableToJoin.length;i++)this.parentDocument.tableToJoin[i]=this.tableToJoin[i];for(var o=0;o<this.tabAllConditions.length;o++)this.parentDocument.tabAllConditions[o]=this.tabAllConditions[o]}}catch(n){console.log("the variable tableToJoin is undefined")}if(window.opener&&window.opener.instanceElement){for(o=0;o<this.tabAllConditions.length;o++)this.tabAllConditions[o].conditionId||(this.tabAllConditions[o].conditionId=o+1);this.tabAllConditions.sort(this.compare),window.opener.instanceElement.saveRuleDetails(JSON.stringify(this.tabAllConditions),JSON.stringify(this.tableToJoin),this.queryToDisplay,this.queryToExecute)}this.titleWindow?this.close():window.close()}else this.swtAlert.warning(a.Wb.getPredictMessage("queryBuilderScreen.alert.invalidQuery")+" "+e.getRequestReplyMessage()),e=null,this.queryToExecute=this.queryToExecute.substr(this.queryToExecute.indexOf(" where ")+7,this.queryToExecute.length)}}catch(n){console.log("error in vqlidatio",n)}},e.prototype.compare=function(t,e){return t.conditionId>e.conditionId?1:e.conditionId>t.conditionId?-1:0},e.prototype.addListValuesEventHandler=function(t){var e=this;try{this.logger.info("method [addListValuesEventHandler] - START ");var i=Object(a.ic.getFocus());"string"==typeof i.id?this.buttonId=i.id:i.id&&"string"==typeof i.id.id?this.buttonId=i.id.id:this.buttonId="",this.selectedItemsList=null,this.listValuesButton.enabled=!1;var o=this.listCriteria.selectedItem.label.label,n=this.listCriteria.selectedItem.label.code;this.moduleReadyEventHandler(t),this.win=a.Eb.createPopUp(this,r.a,{title:this.listCriteria.selectedItem.label.label,operation:this.operation,columnLabel:o,columnCode:n}),this.win.enableResize=!1,this.win.id="listValuesPopup",this.win.width="500",this.win.height="500",this.win.showControls=!0,this.win.onClose.subscribe(function(t){e.popupClosedEventHandler(t)}),this.win.display()}catch(l){this.logger.error("method [addListValuesEventHandler] - error : ",l,"- errorLocation :",this.errorLocation),a.Wb.logError(l,a.Wb.SYSTEM_MODULE_ID,this.commonService.getQualifiedClassName(this),"addListValuesEventHandler",this.errorLocation)}this.logger.info("method [addListValuesEventHandler] - END ")},e.prototype.moduleReadyEventHandler=function(t){try{this.logger.info("method [moduleReadyEventHandler] - START "),this.cancelButton.enabled=!1,"b1"==this.buttonId&&(this.listValuesButton.enabled=!1),this.actionToDo=""}catch(e){}},e.prototype.popupClosedEventHandler=function(t){var e=[];try{if(""!=this.selectedItemsList&&null!=t){if("changeCritButton"==this.buttonId&&"okButton"==t.buttonClicked){for(var i in this.originalJSONList)null!=this.originalJSONList[i].code&&this.originalJSONList[i].code==this.listCriteria.selectedItem.label.code&&null!=this.originalJSONList[i].toChange&&(this.originalJSONList[i].toChange="N",this.originalJSONList[i]),null!=this.originalJSONList[i].code&&this.originalJSONList[i].code==this.selectedItemsList&&(this.originalJSONList[i].toChange="Y",this.originalJSONList[i].fieldValue=this.listCriteria.selectedItem.label.fieldValue,this.originalJSONList[i].operatorId=this.listCriteria.selectedItem.label.operatorId,this.originalJSONList[i].conditionId=this.listCriteria.selectedItem.label.conditionId,this.originalJSONList[i].condFieldName=this.listCriteria.selectedItem.label.condFieldName,this.originalJSONList[i].nextCondition=this.listCriteria.selectedItem.label.nextCondition,this.originalJSONList[i].profileFieldValue=this.listCriteria.selectedItem.label.profileFieldValue,this.originalJSONList[i].clause=this.listCriteria.selectedItem.label.clause),null!=this.originalJSONList[i].toChange&&"Y"==this.originalJSONList[i].toChange&&e.push({label:this.originalJSONList[i]});this.gridJSONList={row:e,size:e.length},this.listCriteria.gridData=this.gridJSONList,this.listCriteria.selectedIndex=-1,this.selectCriteria()}else"in"==this.operation||"not in"==this.operation?this.textInput.text=this.selectedItemsList:"between"==this.operation&&"b1"==this.buttonId&&(this.textInput.text=this.selectedItemsList)}}catch(o){console.log("error in pop upclose",o)}},e.prototype.datePickerOn=function(t){try{if(this.logger.info("method [datePickerOn] - START "),"frmDateChooser"==this.dateField.id&&(this.dateField.formatString=this.sysdateformat),"toDateChooser"==this.dateField2.id){this.dateField2.formatString=this.sysdateformat;var e=this.dateField.parseDate(this.dateField.text,this.sysdateformat);this.dateField2.selectableRange={rangeStart:e,rangeEnd:new Date}}}catch(i){this.logger.error("method [datePickerOn] - error : ",i,"- errorLocation :",this.errorLocation),a.Wb.logError(i,a.Wb.SYSTEM_MODULE_ID,this.commonService.getQualifiedClassName(this),"datePickerOn",this.errorLocation)}this.logger.info("method [datePickerOn] - END ")},e.prototype.datePickerOff=function(t){try{this.logger.info("method [datePickerOff] - START ");var e=Object(a.ic.getFocus()),i="";"string"==typeof e.id?i=e.id:e.id&&"string"==typeof e.id.id&&(i=e.id.id),"frmDateChooser"===i?"between"!==this.operation&&(this.sysDateActive=!1,this.sysDateButton.setStyle("color","black")):"toDateChooser"===i&&(this.sysDateActive=!1,this.sysDateButton.setStyle("color","black"))}catch(o){this.logger.error("method [datePickerOff] - error : ",o,"- errorLocation :",this.errorLocation),a.Wb.logError(o,a.Wb.SYSTEM_MODULE_ID,this.commonService.getQualifiedClassName(this),"datePickerOff",this.errorLocation)}this.logger.info("method [datePickerOff] - END ")},e.prototype.verifyTextInput=function(t,e){try{if(this.logger.info("method [verifyTextInput] - START "),""==t)return t;if("NUM"==e){t=(t=(t=(t=t.replace(/^[,.]/gi,"")).replace(/,{2,}/gi,",")).replace(/\.{2,}/gi,".")).replace(/[,.]$/gi,"");if("in"==this.operation||"not in"==this.operation||"between"==this.operation)for(var i=t.split(","),o=0;o<i.length;o++)"NaN"==Number(i[o].toString()).toString()&&(this.textInput.setFocus(),t="false");else"NaN"==Number(this.getNumberFromAmount(t)).toString()&&(this.textInput.setFocus(),t="false")}else"CHAR"==e&&(t=(t=t.replace(/^[,]/gi,"")).replace(/[,.]$/gi,""))}catch(n){this.logger.error("method [verifyTextInput] - error : ",n,"- errorLocation :",this.errorLocation),a.Wb.logError(n,a.Wb.SYSTEM_MODULE_ID,this.commonService.getQualifiedClassName(this),"verifyTextInput",this.errorLocation)}return this.logger.info("method [verifyTextInput] - END "),t},e.prototype.enableAllButton=function(){try{if(this.logger.info("method [enableAllButton] - START "),this.arrayToDisplayQuery.length>0)if(this.undoButton.enabled=!0,this.resetButton.enabled=!0," and"==this.arrayToExecuteQuery[this.arrayToExecuteQuery.length-1]||" or"==this.arrayToExecuteQuery[this.arrayToExecuteQuery.length-1])this.okButton.enabled=!1,"T"==this.recUnitGroupDisplay&&(this.execStatus="N"),this.saveButton.enabled=!1,this.listCriteria.enabled=!0,this.leftParentheseButton.enabled=!0,this.rightParentheseButton.enabled=!1,this.andButton.enabled=!0,this.orButton.enabled=!0;else{var t=String(this.arrayToDisplayQuery[this.arrayToDisplayQuery.length-1]),e=t.substr(t.length-1,t.length);"]"==e||")"==e?(this.listCriteria.enabled=!1,this.leftParentheseButton.enabled=!1,this.andButton.enabled=!0,this.orButton.enabled=!0,this.countRightP%this.countLeftP==0?(this.okButton.enabled=!0,"T"==this.recUnitGroupDisplay&&(this.execStatus="Y"),this.saveButton.enabled=!0,this.rightParentheseButton.enabled=!1):(this.okButton.enabled=!1,"T"==this.recUnitGroupDisplay&&(this.execStatus="N"),this.saveButton.enabled=!1,this.rightParentheseButton.enabled=!0)):"("==e?(this.okButton.enabled=!1,"T"==this.recUnitGroupDisplay&&(this.execStatus="N"),this.saveButton.enabled=!1,this.listCriteria.enabled=!0,this.leftParentheseButton.enabled=!0,this.rightParentheseButton.enabled=!1,this.andButton.enabled=!1,this.orButton.enabled=!1):(this.okButton.enabled=!1,"T"==this.recUnitGroupDisplay&&(this.execStatus="N"),this.saveButton.enabled=!1,this.listCriteria.enabled=!1,this.leftParentheseButton.enabled=!1,this.rightParentheseButton.enabled=!1,this.andButton.enabled=!1,this.orButton.enabled=!1)}else this.undoButton.enabled=!1,this.resetButton.enabled=!1,this.listCriteria.enabled=!0,this.andButton.enabled=!1,this.orButton.enabled=!1,this.okButton.enabled=!1,"T"==this.recUnitGroupDisplay&&(this.execStatus="N"),this.saveButton.enabled=!1,this.leftParentheseButton.enabled=!0,this.rightParentheseButton.enabled=!1}catch(i){this.logger.error("method [enableAllButton] - error : ",i,"- errorLocation :",this.errorLocation),a.Wb.logError(i,a.Wb.SYSTEM_MODULE_ID,this.commonService.getQualifiedClassName(this),"enableAllButton",this.errorLocation)}this.logger.info("method [enableAllButton] - END ")},e.prototype.disableAllButton=function(){try{this.logger.info("method [disableAllButton] - START "),this.equalButton.enabled=!1,this.equalButton.buttonMode=!1,this.differentButton.enabled=!1,this.differentButton.buttonMode=!1,this.likeButton.enabled=!1,this.likeButton.buttonMode=!1,this.notLikeButton.enabled=!1,this.notLikeButton.buttonMode=!1,this.betweenButton.enabled=!1,this.betweenButton.buttonMode=!1,this.higherButton.enabled=!1,this.higherButton.buttonMode=!1,this.higherequalButton.enabled=!1,this.higherequalButton.buttonMode=!1,this.inButton.enabled=!1,this.inButton.buttonMode=!1,this.andButton.enabled=!1,this.andButton.buttonMode=!1,this.lowerButton.enabled=!1,this.lowerButton.buttonMode=!1,this.lowerequalButton.enabled=!1,this.lowerequalButton.buttonMode=!1,this.notinButton.enabled=!1,this.notinButton.buttonMode=!1,this.inListButton.enabled=!1,this.inListButton.buttonMode=!1,this.orButton.enabled=!1,this.orButton.buttonMode=!1,this.leftParentheseButton.enabled=!1,this.leftParentheseButton.buttonMode=!1,this.rightParentheseButton.enabled=!1,this.rightParentheseButton.buttonMode=!1,this.undoButton.enabled=!1,this.undoButton.buttonMode=!1,this.addButton.enabled=!1,this.addButton.buttonMode=!1,this.okButton.enabled=!1,this.okButton.buttonMode=!1,"T"==this.recUnitGroupDisplay&&(this.execStatus="N"),this.cancelButton.enabled=!1,this.cancelButton.buttonMode=!1,this.saveButton.enabled=!1,this.saveButton.buttonMode=!1,this.resetButton.enabled=!1,this.resetButton.buttonMode=!1,this.listCriteria.enabled=!1,this.helpIcon.enabled=!1,this.helpIcon.buttonMode=!1}catch(t){console.log("errorrr")}},e.prototype.addSysDateEventHandler=function(t){var e=this;try{this.logger.info("method [addSysDateEventHandler] - START "),this.sysDateActive=!0,this.sysDateButton.setStyle("color","#4169E1"),"between"==this.operation?this.dateField2.text=a.j.formatDate(new Date,this.sysdateformat.toUpperCase()):(this.dateField.text=this.getAbbreviationFromDate(this.sysdateformat.toUpperCase(),new Date),this.dateField.text=a.j.formatDate(new Date,this.sysdateformat.toUpperCase())),this.hBoxContainer.contains(this.tolerenceNumStep)||(this.tolerenceNumStep=this.hBoxContainer.addChild(a.Mb),this.tolerenceNumStep.id="tolerenceNumStep",this.tolerenceNumStep.text="0",this.tolerenceNumStep.height="28",this.tolerenceNumStep.width="40",this.tolerenceNumStep.minimum=-36525,this.tolerenceNumStep.maximum=36525,this.tolerenceNumStep.keyDown=function(){e.keyDownEventHandler(t)})}catch(i){this.logger.error("method [addSysDateEventHandler] - error : ",i,"- errorLocation :",this.errorLocation),a.Wb.logError(i,a.Wb.SYSTEM_MODULE_ID,this.commonService.getQualifiedClassName(this),"addSysDateEventHandler",this.errorLocation)}this.logger.info("method [addSysDateEventHandler] - END ")},e.prototype.getAbbreviationFromDate=function(t,e){var i,o,n,l="";try{this.logger.info("method [getAbbreviationFromDate] - START "),"DD-MMM-YYYY"==this.sysdateformat?(i=e.getDate(),o=e.getMonth(),n=e.getFullYear(),l=i>=10?i+"-"+this.nameMonths[o].substr(0,3)+"-"+n:"0"+i+"-"+this.nameMonths[o].substr(0,3)+"-"+n):l=a.j.formatDate(e,t)}catch(s){this.logger.error("method [getAbbreviationFromDate] - error : ",s,"- errorLocation :",this.errorLocation),a.Wb.logError(s,a.Wb.SYSTEM_MODULE_ID,this.commonService.getQualifiedClassName(this),"getAbbreviationFromDate",this.errorLocation)}return this.logger.info("method [getAbbreviationFromDate] - END "),l},e.prototype.getOracleDateFormat=function(t,e,i,o,n,l,s){var a="";try{this.logger.info("method [getOracleDateFormat] - START "),""==o?a=t+" "+e:(l?"between"==o?(a=i.indexOf("SYSDATE")<0?"TRUNC("+t+") "+o+" TO_DATE('"+e+"', '"+this.commonSysDateFormat+"') and  TO_DATE('"+i+"', '"+this.commonSysDateFormat+"')"+n:"TRUNC("+t+") "+o+" TO_DATE('"+e+"', '"+this.commonSysDateFormat+"') and  TRUNC("+i+")"+n,t="TRUNC("+t+") "):">="==o||"<"==o||"<="==o||">"==o||"="==o||"<>"==o?(a=e.indexOf("SYSDATE")<0?"TRUNC("+t+") "+o+" TO_DATE('"+e+"', '"+this.commonSysDateFormat+"')"+n:"TRUNC("+t+") "+o+" TRUNC("+e+")"+n,t="TRUNC("+t+") "):a=t+" "+o+" "+e:">="==o||"<"==o?a=e.indexOf("SYSDATE")<0?t+" "+o+" TO_DATE('"+e+"', '"+this.commonSysDateFormat+"')"+n:t+" "+o+" TRUNC("+e+") "+n:"<="==o||">"==o?(o=">"==o?">=":"<",a=e.indexOf("SYSDATE")<0?t+" "+o+" TO_DATE('"+e+"', '"+this.commonSysDateFormat+"') + 1"+n:t+" "+o+" TRUNC("+e+" + 1)"+n):a="="==o?e.indexOf("SYSDATE")<0?t+" >= TO_DATE('"+e+"', '"+this.commonSysDateFormat+"')"+n+" and "+t+" < TO_DATE('"+e+"', '"+this.commonSysDateFormat+"') + 1"+n:t+" >= TRUNC("+e+") "+n+" and "+t+" < TRUNC("+e+" + 1)"+n:"<>"==o?e.indexOf("SYSDATE")<0?t+" < TO_DATE('"+e+"', '"+this.commonSysDateFormat+"')"+n+" or "+t+" >= TO_DATE('"+e+"', '"+this.commonSysDateFormat+"') + 1"+n:t+" < TRUNC("+e+") "+n+" or "+t+" >= TRUNC("+e+" + 1)"+n:"between"==o?i.indexOf("SYSDATE")<0?t+" >= TO_DATE('"+e+"', '"+this.commonSysDateFormat+"')"+n+" and "+t+" < TO_DATE('"+i+"', '"+this.commonSysDateFormat+"') + 1"+n:t+" >= TO_DATE('"+e+"', '"+this.commonSysDateFormat+"')"+n+" and "+t+" < TRUNC("+i+" + 1)"+n:t+" "+o+" "+e,s.columnToStore=t,s.columnValue=a)}catch(r){this.logger.error("method [getOracleDateFormat] - error : ",r,"- errorLocation :",this.errorLocation)}return this.logger.info("method [getOracleDateFormat] - END "),a},e.prototype.getOracleDateTimeFormat=function(t,e,i,o,n,l,s){var a="";try{this.logger.info("method [getOracleDateTimeFormat] - START "),""==o?a=t+" "+e:(l?"between"==o?(a=i.indexOf("SYSDATE")<0?"TRUNC("+t+") "+o+" TO_DATE('"+e+"', '"+this.commonSysDateFormat+" HH24:MI') and  TO_DATE('"+i+"', '"+this.commonSysDateFormat+" HH24:MI')"+n:"TRUNC("+t+") "+o+" TO_DATE('"+e+"', '"+this.commonSysDateFormat+" HH24:MI') and  TRUNC("+i+")"+n,t="TRUNC("+t+") "):">="==o||"<"==o||"<="==o||">"==o||"="==o||"<>"==o?(a=e.indexOf("SYSDATE")<0?"TRUNC("+t+") "+o+" TO_DATE('"+e+"', '"+this.commonSysDateFormat+" HH24:MI')"+n:"TRUNC("+t+") "+o+" TRUNC("+e+")"+n,t="TRUNC("+t+") "):a=t+" "+o+" "+e:">="==o||"<"==o?a=e.indexOf("SYSDATE")<0?t+" "+o+" TO_DATE('"+e+"', '"+this.commonSysDateFormat+" HH24:MI')"+n:t+" "+o+" TRUNC("+e+") "+n:"<="==o||">"==o?(o=">"==o?">=":"<",a=e.indexOf("SYSDATE")<0?t+" "+o+" TO_DATE('"+e+"', '"+this.commonSysDateFormat+" HH24:MI') + 1"+n:t+" "+o+" TRUNC("+e+" + 1)"+n):a="="==o?e.indexOf("SYSDATE")<0?t+" >= TO_DATE('"+e+"', '"+this.commonSysDateFormat+" HH24:MI')"+n+" and "+t+" < TO_DATE('"+e+"', '"+this.commonSysDateFormat+" HH24:MI') + 1"+n:t+" >= TRUNC("+e+") "+n+" and "+t+" < TRUNC("+e+" + 1)"+n:"<>"==o?e.indexOf("SYSDATE")<0?t+" < TO_DATE('"+e+"', '"+this.commonSysDateFormat+" HH24:MI')"+n+" or "+t+" >= TO_DATE('"+e+"', '"+this.commonSysDateFormat+" HH24:MI') + 1"+n:t+" < TRUNC("+e+") "+n+" or "+t+" >= TRUNC("+e+" + 1)"+n:"between"==o?i.indexOf("SYSDATE")<0?t+" >= TO_DATE('"+e+"', '"+this.commonSysDateFormat+" HH24:MI')"+n+" and "+t+" < TO_DATE('"+i+"', '"+this.commonSysDateFormat+" HH24:MI') + 1"+n:t+" >= TO_DATE('"+e+"', '"+this.commonSysDateFormat+" HH24:MI')"+n+" and "+t+" < TRUNC("+i+" + 1)"+n:t+" "+o+" "+e,s.columnToStore=t,s.columnValue=a)}catch(r){this.logger.error("method [getOracleDateFormat] - error : ",r,"- errorLocation :",this.errorLocation)}return this.logger.info("method [getOracleDateTimeFormat] - END "),a},e.prototype.transformDate2=function(t){var e,i,o,n="";try{null!=t&&(e=t.getDate(),i=t.getMonth()+1,o=t.getFullYear(),n=e>=10?i>=10?e+"/"+i+"/"+o:e+"/0"+i+"/"+o:i>=10?"0"+e+"/"+i+"/"+o:e+"/0"+i+"/"+o)}catch(l){this.logger.error("method [transformDate2] - error : ",l,"- errorLocation :",this.errorLocation),a.Wb.logError(l,a.Wb.SYSTEM_MODULE_ID,this.commonService.getQualifiedClassName(this),"transformDate2",this.errorLocation)}return this.logger.info("method [transformDate2] - END "),n},e.prototype.comboChangeHandler=function(t){var e=this;try{this.logger.info("method [comboChangeHandler] - START "),"TYPETEXT"!=this.comboBox.selectedItem.id||this.hBoxContainer.contains(this.textInput)?"TYPETEXT"!=this.comboBox.selectedItem.id&&this.hBoxContainer.contains(this.textInput)&&this.hBoxContainer.removeChild(this.textInput):(this.textInput=this.hBoxContainer.addChild(a.Rb),this.textInput.width=200,this.textInput.id="SwtTextInput",this.textInput.doubleClick=function(){e.openTextAreaInWindow(t)},"NUM"!=this.comboBox.selectedItem.type&&"DECI"!=this.comboBox.selectedItem.type&&"BIND_N"!=this.comboBox.selectedItem.type||(this.textInput.restrict="0-9.\\-"),this.textInput.enabled=!0,this.textInput.setFocus())}catch(i){this.logger.error("method [comboChangeHandler] - error : ",i,"- errorLocation :",this.errorLocation),a.Wb.logError(i,a.Wb.SYSTEM_MODULE_ID,this.commonService.getQualifiedClassName(this),"comboChangeHandler",this.errorLocation)}this.logger.info("method [comboChangeHandler] - END ")},e.prototype.comboBetweenChangeHandler=function(t){var e=this;try{this.logger.info("method [comboBetweenChangeHandler] - START "),this.vBoxContainer.contains(this.hBoxContainer2)||this.vBoxContainer.addChild(a.C),null==this.comboBox||"TYPETEXT"!=this.comboBox.selectedItem.id||this.hBoxContainer2.contains(this.textInput)?null!=this.comboBox&&"TYPETEXT"!=this.comboBox.selectedItem.id&&this.hBoxContainer2.contains(this.textInput)&&(this.hBoxContainer2.removeChild(this.textInput),this.hBoxContainer2.contains(this.textInput2)&&(this.labelBetweenCombo.width="308")):(this.textInput=this.hBoxContainer2.addChild(a.Rb),this.textInput.width="200",this.textInput.styleName="textbox",this.textInput.id="SwtTextInput",this.textInput.doubleClick=function(){e.openTextAreaInWindow(t)},(null!=this.comboBox&&"NUM"==this.comboBox.selectedItem.type||"DECI"==this.comboBox.selectedItem.type||"BIND_N"==this.comboBox.selectedItem.type)&&(this.textInput.restrict="0-9.\\-"),this.hBoxContainer2.addChildAt(a.Rb,0),this.textInput.enabled=!0,this.textInput.setFocus(),this.hBoxContainer2.contains(this.labelBetweenCombo)&&(this.labelBetweenCombo.width="50")),this.hBoxContainer2.contains(this.labelBetweenCombo)||(this.labelBetweenCombo=this.hBoxContainer2.addChild(a.vb),this.labelBetweenCombo.styleName="label",this.hBoxContainer2.contains(this.textInput)?(this.labelBetweenCombo.width="50",this.hBoxContainer2.addChildAt(a.gb,1)):(this.labelBetweenCombo.width="308",this.hBoxContainer2.addChildAt(a.gb,0))),null==this.comboBox2||"TYPETEXT"!=this.comboBox2.selectedItem.id||this.hBoxContainer2.contains(this.textInput2)?null!=this.comboBox2&&"TYPETEXT"!=this.comboBox2.selectedItem.id&&this.hBoxContainer2.contains(this.textInput2)&&this.hBoxContainer2.removeChild(this.textInput2):(this.textInput2=this.hBoxContainer2.addChild(a.Rb),this.textInput2.width="200",this.textInput2.styleName="textbox",this.textInput2.id="SwtTextInput2",this.textInput2.doubleClick=function(){e.openTextAreaInWindow(t)},(null!=this.comboBox2&&"NUM"==this.comboBox2.selectedItem.type||"DECI"==this.comboBox2.selectedItem.type||"BIND_N"==this.comboBox2.selectedItem.type)&&(this.textInput2.restrict="0-9.\\-"),this.hBoxContainer2.contains(this.textInput)?this.hBoxContainer2.addChildAt(a.Rb,2):(this.hBoxContainer2.addChildAt(a.Rb,0),this.labelBetweenCombo.width="265"),this.textInput2.enabled=!0,this.textInput2.setFocus()),this.hBoxContainer2.contains(this.textInput)||this.hBoxContainer2.contains(this.textInput2)||(this.hBoxContainer2.removeChild(this.labelBetweenCombo),this.vBoxContainer.removeChild(this.hBoxContainer2))}catch(i){console.log("error in combo change",i),this.logger.error("method [comboBetweenChangeHandler] - error : ",i,"- errorLocation :",this.errorLocation),a.Wb.logError(i,a.Wb.SYSTEM_MODULE_ID,this.commonService.getQualifiedClassName(this),"comboBetweenChangeHandler",this.errorLocation)}this.logger.info("method [comboBetweenChangeHandler] - END ")},e.prototype.filtringGrid=function(t){try{this.arrayToDisplayQuery.length>0&&this.clearContraints(),this.filterTextOfInput=t,this.updateFilter()}catch(e){console.log("error",e)}},e.prototype.updateFilter=function(){try{this.listCriteria.dataviewObj.beginUpdate(),this.listCriteria.dataviewObj.setItems(this.listCriteria.gridData),this.listCriteria.dataviewObj.setFilterArgs({searchString:this.filterTextOfInput}),this.listCriteria.dataviewObj.setFilter(this.filterFunction),this.listCriteria.dataviewObj.endUpdate(),this.listCriteria.selectedIndex=-1}catch(t){console.log("errror",t)}},e.prototype.filterFunction=function(t,e){return-1!=t.label.toLowerCase().indexOf(e.searchString.toLowerCase())},e.prototype.validateAmount=function(){if(!validateCurrencyPlaces(this.textInput,this.currencyPattern,null))return this.swtAlert.warning(a.Wb.getPredictMessage("alert.validAmount",null)),!1},e.prototype.validateTime=function(t){if(t.text.endsWith(":")&&(t.text=t.text+"00"),t.text&&0==validateFormatTime(t))return this.swtAlert.warning(a.Wb.getPredictMessage("alert.validTime",null),null),t.text="",!1;t.text=t.text.substring(0,5)},e.prototype.validateTimes=function(t){var e=[],i=t.split(",");if(i.toString().endsWith(",")&&(i=i.substr(0,i.length-1)),i.forEach(function(t){e.push(t)}),e.length>=1)for(var o=0;o<e.length;o++){var n=e[o].toString().trim();if(n.endsWith(":")&&(n+="00"),!s()(n,"HH:mm",!0).isValid())return"error_time"}return!0},e.prototype.validateDateTimeField=function(t){try{var e=[],i=t.split(",");if(i.toString().endsWith(",")&&(i=i.substr(0,i.length-1)),i.forEach(function(t){e.push(t)}),e.length>=1)for(var o=0;o<e.length;o++){var n=e[o].toString().trim();if(!s()(n,this.sysdateformat.toUpperCase()+" HH:mm",!0).isValid())return"error_dateTime"}return!0}catch(l){console.log("error",l),a.Wb.logError(l,a.Wb.SYSTEM_MODULE_ID,"PCDashboard"," validateDateTimeField",this.errorLocation)}return!0},e.prototype.validateDateField=function(t){try{return!(t&&!s()(t,this.sysdateformat.toUpperCase(),!0).isValid())||(this.swtAlert.warning(a.Wb.getPredictMessage("queryBuilderScreen.alert.dateFormat",null)+" "+this.sysdateformat.toUpperCase()),"error_date")}catch(e){a.Wb.logError(e,a.Wb.SYSTEM_MODULE_ID,"PCDashboard"," validateDateField",this.errorLocation)}return!0},e.prototype.getNumberFromAmount=function(t){var e;return"currencyPat2"==this.currencyPattern?t&&(e=Number(t.replace(/\./g,"").replace(/,/g,"."))):"currencyPat1"==this.currencyPattern&&t&&(e=Number(t.replace(/,/g,""))),e},e.prototype.openTextAreaInWindow=function(t){},e.prototype.closeTextAreaInWindow=function(t){},e.prototype.getConditionById=function(t){var e=null;try{e=this.queryToDisplay.match(/\[(.*?)\]/gi)[t]}catch(i){}return e},e.prototype.replaceConditionToDisplayById=function(t,e){var i,o,n=null,l=/\[(.*?)\]/gi;try{var s=void 0;s=l.exec(this.queryToDisplay);for(var a=0;null!=s;){if(a==t){i=s.index,o=l.lastIndex;break}s=l.exec(this.queryToDisplay),a++}n=this.queryToDisplay.substring(0,i)+e+this.queryToDisplay.substr(o,this.queryToDisplay.length)}catch(r){}return n},e.prototype.replaceConditionToExecuteById=function(t,e,i,o){var n,l,s="",a=0,r=!1;try{for(var h=0;h<=this.listCriteria.selectedIndex;h++)this.listCriteria.selectedItem.label.content==this.listCriteria.dataProvider[h].label&&a++;(n=this.nthIndexOf(this.queryToExecute,e+" ",a,0))<0&&(n=this.nthIndexOf(this.queryToExecute,e+")",a,0)),n<0&&(n=this.nthIndexOf(this.queryToExecute,i+" ",a,0),r=!0),n<0&&(n=this.nthIndexOf(this.queryToExecute,i+")",a,0),r=!0),l=r?n+i.length:n+e.length,n>=0&&(s=this.queryToExecute.substring(0,n)+o+this.queryToExecute.substr(l,this.queryToExecute.length)),""==s&&console.log("createQuery: Matching between query and oldCondition is false, oldCondition: ["+e+"], query to be replaced: ["+this.queryToExecute+"], new Condition: ["+s+"], new query to replace: ["+o+"]"+this.errorLocation)}catch(u){console.log("errror in replaceConditionToExecuteById",u)}return s},e.prototype.nthIndexOf=function(t,e,i,o){try{for(i=i||0,o=o||0;i>0;){if((o=t.indexOf(e,o))<0)return-1;--i,++o}}catch(n){console.log("error in nth index",n)}return o-1},e.prototype.changeCriteria=function(){var t,e=this;try{this.selectedItemsList=null,t=8*String(this.listCriteria.selectedItem.label.label).length>350?String(this.listCriteria.selectedItem.label.label).substr(0,45)+"...":this.listCriteria.selectedItem.label.label;this.previousCondId=this.listCriteria.selectedItem.label.conditionId,this.previousTypeCode=this.listCriteria.selectedItem.label.typeCode,this.buttonId="changeCritButton",this.win=a.Eb.createPopUp(this,r.a,{title:t,dataSource:"fromGridData",columnLabel:"Constraints",columnCode:"code"}),this.win.enableResize=!1,this.win.id="listValuesPopup",this.win.width="500",this.win.height="500",this.win.showControls=!0,this.win.onClose.subscribe(function(t){e.popupClosedEventHandler(t)}),this.win.display()}catch(i){console.log("error in changeCriteria",i)}},e.prototype.closePopUp=function(){window.close()},e}(a.yb),d=[{path:"",component:u}],c=(h.l.forChild(d),function(){return function(){}}()),b=i("pMnS"),p=i("RChO"),m=i("t6HQ"),g=i("WFGK"),y=i("5FqG"),B=i("Ip0R"),x=i("gIcY"),f=i("t/Na"),T=i("sE5F"),C=i("OzfB"),D=i("T7CS"),w=i("S7LP"),I=i("6aHO"),S=i("WzUx"),v=i("A7o+"),N=i("zCE2"),E=i("Jg5P"),k=i("3R0m"),O=i("hhbb"),L=i("5rxC"),F=i("Fzqc"),A=i("21Lb"),M=i("hUWP"),R=i("3pJQ"),P=i("V9q+"),q=i("VDKW"),_=i("kXfT"),V=i("BGbe");i.d(e,"ExpressionBuilderModuleNgFactory",function(){return J}),i.d(e,"RenderType_RulesDefinitionAddRule",function(){return Y}),i.d(e,"View_RulesDefinitionAddRule_0",function(){return U}),i.d(e,"View_RulesDefinitionAddRule_Host_0",function(){return Q}),i.d(e,"RulesDefinitionAddRuleNgFactory",function(){return W});var J=o.Gb(c,[],function(t){return o.Qb([o.Rb(512,o.n,o.vb,[[8,[b.a,p.a,m.a,g.a,y.Cb,y.Pb,y.r,y.rc,y.s,y.Ab,y.Bb,y.Db,y.qd,y.Hb,y.k,y.Ib,y.Nb,y.Ub,y.yb,y.Jb,y.v,y.A,y.e,y.c,y.g,y.d,y.Kb,y.f,y.ec,y.Wb,y.bc,y.ac,y.sc,y.fc,y.lc,y.jc,y.Eb,y.Fb,y.mc,y.Lb,y.nc,y.Mb,y.dc,y.Rb,y.b,y.ic,y.Yb,y.Sb,y.kc,y.y,y.Qb,y.cc,y.hc,y.pc,y.oc,y.xb,y.p,y.q,y.o,y.h,y.j,y.w,y.Zb,y.i,y.m,y.Vb,y.Ob,y.Gb,y.Xb,y.t,y.tc,y.zb,y.n,y.qc,y.a,y.z,y.rd,y.sd,y.x,y.td,y.gc,y.l,y.u,y.ud,y.Tb,W]],[3,o.n],o.J]),o.Rb(4608,B.m,B.l,[o.F,[2,B.u]]),o.Rb(4608,x.c,x.c,[]),o.Rb(4608,x.p,x.p,[]),o.Rb(4608,f.j,f.p,[B.c,o.O,f.n]),o.Rb(4608,f.q,f.q,[f.j,f.o]),o.Rb(5120,f.a,function(t){return[t,new a.tb]},[f.q]),o.Rb(4608,f.m,f.m,[]),o.Rb(6144,f.k,null,[f.m]),o.Rb(4608,f.i,f.i,[f.k]),o.Rb(6144,f.b,null,[f.i]),o.Rb(4608,f.f,f.l,[f.b,o.B]),o.Rb(4608,f.c,f.c,[f.f]),o.Rb(4608,T.c,T.c,[]),o.Rb(4608,T.g,T.b,[]),o.Rb(5120,T.i,T.j,[]),o.Rb(4608,T.h,T.h,[T.c,T.g,T.i]),o.Rb(4608,T.f,T.a,[]),o.Rb(5120,T.d,T.k,[T.h,T.f]),o.Rb(5120,o.b,function(t,e){return[C.j(t,e)]},[B.c,o.O]),o.Rb(4608,D.a,D.a,[]),o.Rb(4608,w.a,w.a,[]),o.Rb(4608,I.a,I.a,[o.n,o.L,o.B,w.a,o.g]),o.Rb(4608,S.c,S.c,[o.n,o.g,o.B]),o.Rb(4608,S.e,S.e,[S.c]),o.Rb(4608,v.l,v.l,[]),o.Rb(4608,v.h,v.g,[]),o.Rb(4608,v.c,v.f,[]),o.Rb(4608,v.j,v.d,[]),o.Rb(4608,v.b,v.a,[]),o.Rb(4608,v.k,v.k,[v.l,v.h,v.c,v.j,v.b,v.m,v.n]),o.Rb(4608,S.i,S.i,[[2,v.k]]),o.Rb(4608,S.r,S.r,[S.L,[2,v.k],S.i]),o.Rb(4608,S.t,S.t,[]),o.Rb(4608,S.w,S.w,[]),o.Rb(1073742336,h.l,h.l,[[2,h.r],[2,h.k]]),o.Rb(1073742336,B.b,B.b,[]),o.Rb(1073742336,x.n,x.n,[]),o.Rb(1073742336,x.l,x.l,[]),o.Rb(1073742336,N.a,N.a,[]),o.Rb(1073742336,E.a,E.a,[]),o.Rb(1073742336,x.e,x.e,[]),o.Rb(1073742336,k.a,k.a,[]),o.Rb(1073742336,v.i,v.i,[]),o.Rb(1073742336,S.b,S.b,[]),o.Rb(1073742336,f.e,f.e,[]),o.Rb(1073742336,f.d,f.d,[]),o.Rb(1073742336,T.e,T.e,[]),o.Rb(1073742336,O.b,O.b,[]),o.Rb(1073742336,L.b,L.b,[]),o.Rb(1073742336,C.c,C.c,[]),o.Rb(1073742336,F.a,F.a,[]),o.Rb(1073742336,A.d,A.d,[]),o.Rb(1073742336,M.c,M.c,[]),o.Rb(1073742336,R.a,R.a,[]),o.Rb(1073742336,P.a,P.a,[[2,C.g],o.O]),o.Rb(1073742336,q.b,q.b,[]),o.Rb(1073742336,_.a,_.a,[]),o.Rb(1073742336,V.b,V.b,[]),o.Rb(1073742336,a.Tb,a.Tb,[]),o.Rb(1073742336,c,c,[]),o.Rb(256,f.n,"XSRF-TOKEN",[]),o.Rb(256,f.o,"X-XSRF-TOKEN",[]),o.Rb(256,"config",{},[]),o.Rb(256,v.m,void 0,[]),o.Rb(256,v.n,void 0,[]),o.Rb(256,"popperDefaults",{},[]),o.Rb(1024,h.i,function(){return[[{path:"",component:u}]]},[])])}),H=[[""]],Y=o.Hb({encapsulation:0,styles:H,data:{}});function U(t){return o.dc(0,[o.Zb(402653184,1,{_container:0}),o.Zb(402653184,2,{propertiesLabel:0}),o.Zb(402653184,3,{operator:0}),o.Zb(402653184,4,{nameQueryLabel:0}),o.Zb(402653184,5,{nameSortLabel:0}),o.Zb(402653184,6,{labelQuery:0}),o.Zb(402653184,7,{undoButton:0}),o.Zb(402653184,8,{leftParentheseButton:0}),o.Zb(402653184,9,{rightParentheseButton:0}),o.Zb(402653184,10,{andButton:0}),o.Zb(402653184,11,{orButton:0}),o.Zb(402653184,12,{saveButton:0}),o.Zb(402653184,13,{okButton:0}),o.Zb(402653184,14,{okSortButton:0}),o.Zb(402653184,15,{cancelSortButton:0}),o.Zb(402653184,16,{removeSortButton:0}),o.Zb(402653184,17,{addButton:0}),o.Zb(402653184,18,{callSortButton:0}),o.Zb(402653184,19,{callButton:0}),o.Zb(402653184,20,{resetButton:0}),o.Zb(402653184,21,{equalButton:0}),o.Zb(402653184,22,{differentButton:0}),o.Zb(402653184,23,{higherButton:0}),o.Zb(402653184,24,{higherequalButton:0}),o.Zb(402653184,25,{lowerequalButton:0}),o.Zb(402653184,26,{lowerButton:0}),o.Zb(402653184,27,{notLikeButton:0}),o.Zb(402653184,28,{inButton:0}),o.Zb(402653184,29,{notinButton:0}),o.Zb(402653184,30,{likeButton:0}),o.Zb(402653184,31,{betweenButton:0}),o.Zb(402653184,32,{inListButton:0}),o.Zb(402653184,33,{cancelButton:0}),o.Zb(402653184,34,{helpIcon:0}),o.Zb(402653184,35,{saveSortButton:0}),o.Zb(402653184,36,{resetSortButton:0}),o.Zb(402653184,37,{addItemsAscBtn:0}),o.Zb(402653184,38,{addItemsDescBtn:0}),o.Zb(402653184,39,{removeItemsButton:0}),o.Zb(402653184,40,{upButton:0}),o.Zb(402653184,41,{downButton:0}),o.Zb(402653184,42,{changeButton:0}),o.Zb(402653184,43,{changeCritButton:0}),o.Zb(402653184,44,{containsButton:0}),o.Zb(402653184,45,{loadingImage:0}),o.Zb(402653184,46,{filterText:0}),o.Zb(402653184,47,{buttonHbox:0}),o.Zb(402653184,48,{hBoxContainer:0}),o.Zb(402653184,49,{hBoxContainer2:0}),o.Zb(402653184,50,{vBoxContainer:0}),o.Zb(402653184,51,{queryText:0}),o.Zb(402653184,52,{customGrid:0}),o.Zb(402653184,53,{rightItemsList:0}),o.Zb(402653184,54,{leftItemsList:0}),(t()(),o.Jb(54,0,null,null,141,"SwtModule",[["height","100%"],["width","100%"]],[[8,"title",0]],[[null,"creationComplete"]],function(t,e,i){var o=!0,n=t.component;"creationComplete"===e&&(n.loadSearchDefaults(),o=!1!==n.init()&&o);return o},y.ad,y.hb)),o.Ib(55,4440064,null,0,a.yb,[o.r,a.i],{width:[0,"width"],height:[1,"height"]},{creationComplete:"creationComplete"}),(t()(),o.Jb(56,0,null,0,139,"VBox",[["height","100%"],["styleName","vboxOuter"],["width","100%"]],null,null,null,y.od,y.vb)),o.Ib(57,4440064,null,0,a.ec,[o.r,a.i,o.T],{styleName:[0,"styleName"],width:[1,"width"],height:[2,"height"]},null),(t()(),o.Jb(58,0,null,0,119,"SwtCanvas",[["height","90%"],["width","100%"]],null,null,null,y.Nc,y.U)),o.Ib(59,4440064,null,0,a.db,[o.r,a.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),o.Jb(60,0,null,0,117,"VBox",[["height","100%"],["width","100%"]],null,null,null,y.od,y.vb)),o.Ib(61,4440064,null,0,a.ec,[o.r,a.i,o.T],{width:[0,"width"],height:[1,"height"]},null),(t()(),o.Jb(62,0,null,0,83,"SwtPanel",[["height","55%"],["styleName","panelInsideFormLayout"],["title","Search Criteria"],["width","100%"]],null,null,null,y.dd,y.kb)),o.Ib(63,4440064,null,0,a.Cb,[o.r,a.i,o.T],{styleName:[0,"styleName"],width:[1,"width"],height:[2,"height"],title:[3,"title"]},null),(t()(),o.Jb(64,0,null,0,81,"VBox",[["height","100%"],["width","100%"]],null,null,null,y.od,y.vb)),o.Ib(65,4440064,null,0,a.ec,[o.r,a.i,o.T],{width:[0,"width"],height:[1,"height"]},null),(t()(),o.Jb(66,0,null,0,75,"VBox",[["height","95%"],["width","100%"]],null,null,null,y.od,y.vb)),o.Ib(67,4440064,null,0,a.ec,[o.r,a.i,o.T],{width:[0,"width"],height:[1,"height"]},null),(t()(),o.Jb(68,0,null,0,5,"HBox",[["height","15%"],["horizontalAlign","left"],["id","filterBox"],["paddingTop","5"],["width","100%"]],null,null,null,y.Dc,y.K)),o.Ib(69,4440064,null,0,a.C,[o.r,a.i],{id:[0,"id"],horizontalAlign:[1,"horizontalAlign"],width:[2,"width"],height:[3,"height"],paddingTop:[4,"paddingTop"]},null),(t()(),o.Jb(70,0,null,0,1,"SwtLabel",[["id","labelFilter"],["text","Filter"],["width","50"]],null,null,null,y.Yc,y.fb)),o.Ib(71,4440064,null,0,a.vb,[o.r,a.i],{id:[0,"id"],width:[1,"width"],text:[2,"text"]},null),(t()(),o.Jb(72,0,null,0,1,"SwtTextInput",[["enabled","false"],["id","filterText"],["styleName","textbox"],["width","400"]],null,[[null,"change"]],function(t,e,i){var n=!0,l=t.component;"change"===e&&(n=!1!==l.filtringGrid(o.Tb(t,73).text)&&n);return n},y.kd,y.sb)),o.Ib(73,4440064,[[46,4],["filterText",4]],0,a.Rb,[o.r,a.i],{id:[0,"id"],styleName:[1,"styleName"],width:[2,"width"],enabled:[3,"enabled"]},{change_:"change"}),(t()(),o.Jb(74,0,null,0,67,"HBox",[["height","95%"],["horizontalGap","0"],["width","100%"]],null,null,null,y.Dc,y.K)),o.Ib(75,4440064,null,0,a.C,[o.r,a.i],{horizontalGap:[0,"horizontalGap"],width:[1,"width"],height:[2,"height"]},null),(t()(),o.Jb(76,0,null,0,1,"SwtCanvas",[["height","95%"],["id","customGrid"],["width","50%"]],null,null,null,y.Nc,y.U)),o.Ib(77,4440064,[[52,4],["customGrid",4]],0,a.db,[o.r,a.i],{id:[0,"id"],width:[1,"width"],height:[2,"height"]},null),(t()(),o.Jb(78,0,null,0,1,"spacer",[["width","5%"]],null,null,null,y.Kc,y.R)),o.Ib(79,4440064,null,0,a.Y,[o.r,a.i],{width:[0,"width"]},null),(t()(),o.Jb(80,0,null,0,3,"VBox",[["height","90%"],["horizontalAlign","center"],["verticalAlign","middle"],["width","10%"]],null,null,null,y.od,y.vb)),o.Ib(81,4440064,null,0,a.ec,[o.r,a.i,o.T],{horizontalAlign:[0,"horizontalAlign"],verticalAlign:[1,"verticalAlign"],width:[2,"width"],height:[3,"height"]},null),(t()(),o.Jb(82,0,null,0,1,"SwtButton",[["enabled","false"],["id","changeCritButton"],["marginTop","50"],["styleName","editIcon"],["visible","false"],["width","33"]],null,[[null,"click"],[null,"keyDown"]],function(t,e,i){var o=!0,n=t.component;"click"===e&&(o=!1!==n.changeCriteria()&&o);"keyDown"===e&&(o=!1!==n.keyDownEventHandler(i)&&o);return o},y.Mc,y.T)),o.Ib(83,4440064,[[43,4],["changeCritButton",4]],0,a.cb,[o.r,a.i],{id:[0,"id"],styleName:[1,"styleName"],width:[2,"width"],visible:[3,"visible"],enabled:[4,"enabled"],marginTop:[5,"marginTop"],buttonMode:[6,"buttonMode"]},{onClick_:"click",onKeyDown_:"keyDown"}),(t()(),o.Jb(84,0,null,0,1,"spacer",[["width","5%"]],null,null,null,y.Kc,y.R)),o.Ib(85,4440064,null,0,a.Y,[o.r,a.i],{width:[0,"width"]},null),(t()(),o.Jb(86,0,null,0,55,"SwtCanvas",[["height","90%"],["id","CanvOperator"],["paddingRight","5"],["width","40%"]],null,null,null,y.Nc,y.U)),o.Ib(87,4440064,null,0,a.db,[o.r,a.i],{id:[0,"id"],width:[1,"width"],height:[2,"height"],paddingRight:[3,"paddingRight"]},null),(t()(),o.Jb(88,0,null,0,53,"VBox",[["height","100%"],["width","100%"]],null,null,null,y.od,y.vb)),o.Ib(89,4440064,null,0,a.ec,[o.r,a.i,o.T],{width:[0,"width"],height:[1,"height"]},null),(t()(),o.Jb(90,0,null,0,11,"HBox",[["height","35"],["horizontalAlign","left"],["width","500"]],null,null,null,y.Dc,y.K)),o.Ib(91,4440064,null,0,a.C,[o.r,a.i],{horizontalAlign:[0,"horizontalAlign"],width:[1,"width"],height:[2,"height"]},null),(t()(),o.Jb(92,0,null,0,1,"SwtButton",[["enabled","false"],["id","equalButton"],["label","="],["width","60"]],null,[[null,"click"],[null,"keyDown"]],function(t,e,i){var n=!0,l=t.component;"click"===e&&(l.operation=o.Tb(t,93).label,l.operationToDisplay=o.Tb(t,93).label,n=!1!==l.doOperation()&&n);"keyDown"===e&&(n=!1!==l.keyDownEventHandler(i)&&n);return n},y.Mc,y.T)),o.Ib(93,4440064,[[21,4],["equalButton",4]],0,a.cb,[o.r,a.i],{id:[0,"id"],width:[1,"width"],enabled:[2,"enabled"],label:[3,"label"]},{onClick_:"click",onKeyDown_:"keyDown"}),(t()(),o.Jb(94,0,null,0,1,"SwtButton",[["enabled","false"],["id","differentButton"],["label","<>"],["width","60"]],null,[[null,"click"],[null,"keyDown"]],function(t,e,i){var n=!0,l=t.component;"click"===e&&(l.operation=o.Tb(t,95).label,l.operationToDisplay=o.Tb(t,95).label,n=!1!==l.doOperation()&&n);"keyDown"===e&&(n=!1!==l.keyDownEventHandler(i)&&n);return n},y.Mc,y.T)),o.Ib(95,4440064,[[22,4],["differentButton",4]],0,a.cb,[o.r,a.i],{id:[0,"id"],width:[1,"width"],enabled:[2,"enabled"],label:[3,"label"]},{onClick_:"click",onKeyDown_:"keyDown"}),(t()(),o.Jb(96,0,null,0,1,"SwtButton",[["enabled","false"],["id","inButton"],["label","In"],["width","60"]],null,[[null,"click"],[null,"keyDown"]],function(t,e,i){var n=!0,l=t.component;"click"===e&&(l.operation="in",l.operationToDisplay=o.Tb(t,97).label,n=!1!==l.doOperation()&&n);"keyDown"===e&&(n=!1!==l.keyDownEventHandler(i)&&n);return n},y.Mc,y.T)),o.Ib(97,4440064,[[28,4],["inButton",4]],0,a.cb,[o.r,a.i],{id:[0,"id"],width:[1,"width"],enabled:[2,"enabled"],label:[3,"label"]},{onClick_:"click",onKeyDown_:"keyDown"}),(t()(),o.Jb(98,0,null,0,1,"spacer",[["id","space1"],["width","30"]],null,null,null,y.Kc,y.R)),o.Ib(99,4440064,null,0,a.Y,[o.r,a.i],{id:[0,"id"],width:[1,"width"]},null),(t()(),o.Jb(100,0,null,0,1,"SwtButton",[["id","leftParentheseButton"],["label","("],["width","60"]],null,[[null,"click"],[null,"keyDown"]],function(t,e,i){var o=!0,n=t.component;"click"===e&&(o=!1!==n.doOpenCloseParentheses()&&o);"keyDown"===e&&(o=!1!==n.keyDownEventHandler(i)&&o);return o},y.Mc,y.T)),o.Ib(101,4440064,[[8,4],["leftParentheseButton",4]],0,a.cb,[o.r,a.i],{id:[0,"id"],width:[1,"width"],label:[2,"label"]},{onClick_:"click",onKeyDown_:"keyDown"}),(t()(),o.Jb(102,0,null,0,11,"HBox",[["height","35"],["horizontalAlign","left"],["width","500"]],null,null,null,y.Dc,y.K)),o.Ib(103,4440064,null,0,a.C,[o.r,a.i],{horizontalAlign:[0,"horizontalAlign"],width:[1,"width"],height:[2,"height"]},null),(t()(),o.Jb(104,0,null,0,1,"SwtButton",[["enabled","false"],["id","higherButton"],["label",">"],["width","60"]],null,[[null,"click"],[null,"keyDown"]],function(t,e,i){var n=!0,l=t.component;"click"===e&&(l.operation=o.Tb(t,105).label,l.operationToDisplay=o.Tb(t,105).label,n=!1!==l.doOperation()&&n);"keyDown"===e&&(n=!1!==l.keyDownEventHandler(i)&&n);return n},y.Mc,y.T)),o.Ib(105,4440064,[[23,4],["higherButton",4]],0,a.cb,[o.r,a.i],{id:[0,"id"],width:[1,"width"],enabled:[2,"enabled"],label:[3,"label"]},{onClick_:"click",onKeyDown_:"keyDown"}),(t()(),o.Jb(106,0,null,0,1,"SwtButton",[["enabled","false"],["id","higherequalButton"],["label",">="],["width","60"]],null,[[null,"click"],[null,"keyDown"]],function(t,e,i){var n=!0,l=t.component;"click"===e&&(l.operation=o.Tb(t,107).label,l.operationToDisplay=o.Tb(t,107).label,n=!1!==l.doOperation()&&n);"keyDown"===e&&(n=!1!==l.keyDownEventHandler(i)&&n);return n},y.Mc,y.T)),o.Ib(107,4440064,[[24,4],["higherequalButton",4]],0,a.cb,[o.r,a.i],{id:[0,"id"],width:[1,"width"],enabled:[2,"enabled"],label:[3,"label"]},{onClick_:"click",onKeyDown_:"keyDown"}),(t()(),o.Jb(108,0,null,0,1,"SwtButton",[["enabled","false"],["id","notinButton"],["label","Not In"],["width","60"]],null,[[null,"click"],[null,"keyDown"]],function(t,e,i){var n=!0,l=t.component;"click"===e&&(l.operation="not in",l.operationToDisplay=o.Tb(t,109).label,n=!1!==l.doOperation()&&n);"keyDown"===e&&(n=!1!==l.keyDownEventHandler(i)&&n);return n},y.Mc,y.T)),o.Ib(109,4440064,[[29,4],["notinButton",4]],0,a.cb,[o.r,a.i],{id:[0,"id"],width:[1,"width"],enabled:[2,"enabled"],label:[3,"label"]},{onClick_:"click",onKeyDown_:"keyDown"}),(t()(),o.Jb(110,0,null,0,1,"spacer",[["width","30"]],null,null,null,y.Kc,y.R)),o.Ib(111,4440064,null,0,a.Y,[o.r,a.i],{width:[0,"width"]},null),(t()(),o.Jb(112,0,null,0,1,"SwtButton",[["enabled","false"],["id","rightParentheseButton"],["label",")"],["width","60"]],null,[[null,"click"],[null,"keyDown"]],function(t,e,i){var o=!0,n=t.component;"click"===e&&(o=!1!==n.doOpenCloseParentheses()&&o);"keyDown"===e&&(o=!1!==n.keyDownEventHandler(i)&&o);return o},y.Mc,y.T)),o.Ib(113,4440064,[[9,4],["rightParentheseButton",4]],0,a.cb,[o.r,a.i],{id:[0,"id"],width:[1,"width"],enabled:[2,"enabled"],label:[3,"label"]},{onClick_:"click",onKeyDown_:"keyDown"}),(t()(),o.Jb(114,0,null,0,11,"HBox",[["height","35"],["horizontalAlign","left"],["width","500"]],null,null,null,y.Dc,y.K)),o.Ib(115,4440064,null,0,a.C,[o.r,a.i],{horizontalAlign:[0,"horizontalAlign"],width:[1,"width"],height:[2,"height"]},null),(t()(),o.Jb(116,0,null,0,1,"SwtButton",[["enabled","false"],["id","lowerButton"],["label","<"],["width","60"]],null,[[null,"click"],[null,"keyDown"]],function(t,e,i){var n=!0,l=t.component;"click"===e&&(l.operation=o.Tb(t,117).label,l.operationToDisplay=o.Tb(t,117).label,n=!1!==l.doOperation()&&n);"keyDown"===e&&(n=!1!==l.keyDownEventHandler(i)&&n);return n},y.Mc,y.T)),o.Ib(117,4440064,[[26,4],["lowerButton",4]],0,a.cb,[o.r,a.i],{id:[0,"id"],width:[1,"width"],enabled:[2,"enabled"],label:[3,"label"]},{onClick_:"click",onKeyDown_:"keyDown"}),(t()(),o.Jb(118,0,null,0,1,"SwtButton",[["enabled","false"],["id","lowerequalButton"],["label","<="],["width","60"]],null,[[null,"click"],[null,"keyDown"]],function(t,e,i){var n=!0,l=t.component;"click"===e&&(l.operation=o.Tb(t,119).label,l.operationToDisplay=o.Tb(t,119).label,n=!1!==l.doOperation()&&n);"keyDown"===e&&(n=!1!==l.keyDownEventHandler(i)&&n);return n},y.Mc,y.T)),o.Ib(119,4440064,[[25,4],["lowerequalButton",4]],0,a.cb,[o.r,a.i],{id:[0,"id"],width:[1,"width"],enabled:[2,"enabled"],label:[3,"label"]},{onClick_:"click",onKeyDown_:"keyDown"}),(t()(),o.Jb(120,0,null,0,1,"SwtButton",[["enabled","false"],["id","betweenButton"],["label","Between"],["width","60"]],null,[[null,"click"],[null,"keyDown"]],function(t,e,i){var n=!0,l=t.component;"click"===e&&(l.operation="between",l.operationToDisplay=o.Tb(t,121).label,n=!1!==l.doOperation()&&n);"keyDown"===e&&(n=!1!==l.keyDownEventHandler(i)&&n);return n},y.Mc,y.T)),o.Ib(121,4440064,[[31,4],["betweenButton",4]],0,a.cb,[o.r,a.i],{id:[0,"id"],width:[1,"width"],enabled:[2,"enabled"],label:[3,"label"]},{onClick_:"click",onKeyDown_:"keyDown"}),(t()(),o.Jb(122,0,null,0,1,"spacer",[["width","30"]],null,null,null,y.Kc,y.R)),o.Ib(123,4440064,null,0,a.Y,[o.r,a.i],{width:[0,"width"]},null),(t()(),o.Jb(124,0,null,0,1,"SwtButton",[["enabled","false"],["id","andButton"],["label","And"],["width","60"]],null,[[null,"click"],[null,"keyDown"]],function(t,e,i){var n=!0,l=t.component;"click"===e&&(l.andOrString="and",l.andOrStringToDisplay=o.Tb(t,125).label,l.operationToDisplay=o.Tb(t,125).label,n=!1!==l.addOperation()&&n);"keyDown"===e&&(n=!1!==l.keyDownEventHandler(i)&&n);return n},y.Mc,y.T)),o.Ib(125,4440064,[[10,4],["andButton",4]],0,a.cb,[o.r,a.i],{id:[0,"id"],width:[1,"width"],enabled:[2,"enabled"],label:[3,"label"]},{onClick_:"click",onKeyDown_:"keyDown"}),(t()(),o.Jb(126,0,null,0,11,"HBox",[["height","35"],["horizontalAlign","left"],["width","500"]],null,null,null,y.Dc,y.K)),o.Ib(127,4440064,null,0,a.C,[o.r,a.i],{horizontalAlign:[0,"horizontalAlign"],width:[1,"width"],height:[2,"height"]},null),(t()(),o.Jb(128,0,null,0,1,"SwtButton",[["enabled","false"],["id","likeButton"],["label","Like"],["width","60"]],null,[[null,"click"],[null,"keyDown"]],function(t,e,i){var n=!0,l=t.component;"click"===e&&(l.operation="like",l.operationToDisplay=o.Tb(t,129).label,n=!1!==l.doOperation()&&n);"keyDown"===e&&(n=!1!==l.keyDownEventHandler(i)&&n);return n},y.Mc,y.T)),o.Ib(129,4440064,[[30,4],["likeButton",4]],0,a.cb,[o.r,a.i],{id:[0,"id"],width:[1,"width"],enabled:[2,"enabled"],label:[3,"label"]},{onClick_:"click",onKeyDown_:"keyDown"}),(t()(),o.Jb(130,0,null,0,1,"SwtButton",[["enabled","false"],["id","notLikeButton"],["label","Not Like"],["width","60"]],null,[[null,"click"],[null,"keyDown"]],function(t,e,i){var n=!0,l=t.component;"click"===e&&(l.operation="Not Like",l.operationToDisplay=o.Tb(t,131).label,n=!1!==l.doOperation()&&n);"keyDown"===e&&(n=!1!==l.keyDownEventHandler(i)&&n);return n},y.Mc,y.T)),o.Ib(131,4440064,[[27,4],["notLikeButton",4]],0,a.cb,[o.r,a.i],{id:[0,"id"],width:[1,"width"],enabled:[2,"enabled"],label:[3,"label"]},{onClick_:"click",onKeyDown_:"keyDown"}),(t()(),o.Jb(132,0,null,0,1,"SwtButton",[["enabled","false"],["id","inListButton"],["label","In List"],["width","60"]],null,[[null,"click"],[null,"keyDown"]],function(t,e,i){var n=!0,l=t.component;"click"===e&&(l.operation="in list",l.operationToDisplay=o.Tb(t,133).label,n=!1!==l.doOperation()&&n);"keyDown"===e&&(n=!1!==l.keyDownEventHandler(i)&&n);return n},y.Mc,y.T)),o.Ib(133,4440064,[[32,4],["inListButton",4]],0,a.cb,[o.r,a.i],{id:[0,"id"],width:[1,"width"],enabled:[2,"enabled"],label:[3,"label"]},{onClick_:"click",onKeyDown_:"keyDown"}),(t()(),o.Jb(134,0,null,0,1,"spacer",[["width","30"]],null,null,null,y.Kc,y.R)),o.Ib(135,4440064,null,0,a.Y,[o.r,a.i],{width:[0,"width"]},null),(t()(),o.Jb(136,0,null,0,1,"SwtButton",[["enabled","false"],["id","orButton"],["label","Or"],["width","60"]],null,[[null,"click"],[null,"keyDown"]],function(t,e,i){var n=!0,l=t.component;"click"===e&&(l.andOrString="or",l.andOrStringToDisplay=o.Tb(t,137).label,l.operationToDisplay=o.Tb(t,137).label,n=!1!==l.addOperation()&&n);"keyDown"===e&&(n=!1!==l.keyDownEventHandler(i)&&n);return n},y.Mc,y.T)),o.Ib(137,4440064,[[11,4],["orButton",4]],0,a.cb,[o.r,a.i],{id:[0,"id"],width:[1,"width"],enabled:[2,"enabled"],label:[3,"label"]},{onClick_:"click",onKeyDown_:"keyDown"}),(t()(),o.Jb(138,0,null,0,3,"HBox",[["height","35"],["horizontalAlign","left"],["width","500"]],null,null,null,y.Dc,y.K)),o.Ib(139,4440064,null,0,a.C,[o.r,a.i],{horizontalAlign:[0,"horizontalAlign"],width:[1,"width"],height:[2,"height"]},null),(t()(),o.Jb(140,0,null,0,1,"SwtButton",[["enabled","false"],["id","containsButton"],["label","Contains"],["width","70"]],null,[[null,"click"],[null,"keyDown"]],function(t,e,i){var n=!0,l=t.component;"click"===e&&(l.operation="exists",l.operationToDisplay=o.Tb(t,141).label,n=!1!==l.doOperation()&&n);"keyDown"===e&&(n=!1!==l.keyDownEventHandler(i)&&n);return n},y.Mc,y.T)),o.Ib(141,4440064,[[44,4],["containsButton",4]],0,a.cb,[o.r,a.i],{id:[0,"id"],width:[1,"width"],enabled:[2,"enabled"],label:[3,"label"]},{onClick_:"click",onKeyDown_:"keyDown"}),(t()(),o.Jb(142,0,null,0,3,"VBox",[["height","10%"],["width","100%"]],null,null,null,y.od,y.vb)),o.Ib(143,4440064,null,0,a.ec,[o.r,a.i,o.T],{width:[0,"width"],height:[1,"height"]},null),(t()(),o.Jb(144,0,null,0,1,"SwtLabel",[["height","100%"],["id","labelQuery"],["width","100%"]],null,null,null,y.Yc,y.fb)),o.Ib(145,4440064,[[6,4],["labelQuery",4]],0,a.vb,[o.r,a.i],{id:[0,"id"],width:[1,"width"],height:[2,"height"]},null),(t()(),o.Jb(146,0,null,0,25,"SwtPanel",[["height","20%"],["styleName","panelInsideFormLayout"],["title","Values"],["width","100%"]],null,null,null,y.dd,y.kb)),o.Ib(147,4440064,null,0,a.Cb,[o.r,a.i,o.T],{styleName:[0,"styleName"],width:[1,"width"],height:[2,"height"],title:[3,"title"]},null),(t()(),o.Jb(148,0,null,0,23,"HBox",[["height","100%"],["paddingTop","8"],["width","100%"]],null,null,null,y.Dc,y.K)),o.Ib(149,4440064,null,0,a.C,[o.r,a.i],{width:[0,"width"],height:[1,"height"],paddingTop:[2,"paddingTop"]},null),(t()(),o.Jb(150,0,null,0,7,"VBox",[["height","100%"],["width","35%"]],null,null,null,y.od,y.vb)),o.Ib(151,4440064,null,0,a.ec,[o.r,a.i,o.T],{width:[0,"width"],height:[1,"height"]},null),(t()(),o.Jb(152,0,null,0,5,"HBox",[["width","100%"]],null,null,null,y.Dc,y.K)),o.Ib(153,4440064,null,0,a.C,[o.r,a.i],{width:[0,"width"]},null),(t()(),o.Jb(154,0,null,0,1,"SwtLabel",[["id","propertiesLabel"],["paddingTop","20"],["styleName","label"]],null,null,null,y.Yc,y.fb)),o.Ib(155,4440064,[[2,4],["propertiesLabel",4]],0,a.vb,[o.r,a.i],{id:[0,"id"],styleName:[1,"styleName"],paddingTop:[2,"paddingTop"]},null),(t()(),o.Jb(156,0,null,0,1,"SwtLabel",[["height","20"],["id","operator"],["paddingLeft","15"],["paddingRight","15"],["styleName","label"]],null,null,null,y.Yc,y.fb)),o.Ib(157,4440064,[[3,4],["operator",4]],0,a.vb,[o.r,a.i],{id:[0,"id"],styleName:[1,"styleName"],height:[2,"height"],paddingLeft:[3,"paddingLeft"],paddingRight:[4,"paddingRight"],text:[5,"text"]},null),(t()(),o.Jb(158,0,null,0,5,"VBox",[["id","vBoxContainer"],["width","60%"]],null,null,null,y.od,y.vb)),o.Ib(159,4440064,[[50,4],["vBoxContainer",4]],0,a.ec,[o.r,a.i,o.T],{id:[0,"id"],width:[1,"width"]},null),(t()(),o.Jb(160,0,null,0,1,"HBox",[["height","50%"],["id","hBoxContainer"]],null,null,null,y.Dc,y.K)),o.Ib(161,4440064,[[48,4],["hBoxContainer",4]],0,a.C,[o.r,a.i],{id:[0,"id"],height:[1,"height"]},null),(t()(),o.Jb(162,0,null,0,1,"HBox",[["height","50%"],["id","hBoxContainer2"]],null,null,null,y.Dc,y.K)),o.Ib(163,4440064,[[49,4],["hBoxContainer2",4]],0,a.C,[o.r,a.i],{id:[0,"id"],height:[1,"height"]},null),(t()(),o.Jb(164,0,null,0,7,"VBox",[["height","100%"],["horizontalAlign","right"],["paddingBottom","5"],["width","10%"]],null,null,null,y.od,y.vb)),o.Ib(165,4440064,null,0,a.ec,[o.r,a.i,o.T],{horizontalAlign:[0,"horizontalAlign"],width:[1,"width"],height:[2,"height"],paddingBottom:[3,"paddingBottom"]},null),(t()(),o.Jb(166,0,null,0,1,"SwtButton",[["enabled","false"],["id","addButton"],["label","Add"],["marginBottom","2"],["width","60"]],null,[[null,"click"]],function(t,e,i){var o=!0,n=t.component;"click"===e&&(o=!1!==n.chekcFields()&&o);return o},y.Mc,y.T)),o.Ib(167,4440064,[[17,4],["addButton",4]],0,a.cb,[o.r,a.i],{id:[0,"id"],width:[1,"width"],enabled:[2,"enabled"],marginBottom:[3,"marginBottom"],label:[4,"label"]},{onClick_:"click"}),(t()(),o.Jb(168,0,null,0,1,"SwtButton",[["enabled","false"],["id","undoButton"],["label","Undo"],["marginBottom","2"],["width","60"]],null,[[null,"click"],[null,"keyDown"]],function(t,e,i){var o=!0,n=t.component;"click"===e&&(o=!1!==n.previous()&&o);"keyDown"===e&&(o=!1!==n.keyDownEventHandler(i)&&o);return o},y.Mc,y.T)),o.Ib(169,4440064,[[7,4],["undoButton",4]],0,a.cb,[o.r,a.i],{id:[0,"id"],width:[1,"width"],enabled:[2,"enabled"],marginBottom:[3,"marginBottom"],label:[4,"label"]},{onClick_:"click",onKeyDown_:"keyDown"}),(t()(),o.Jb(170,0,null,0,1,"SwtButton",[["enabled","false"],["id","changeButton"],["label","Change"],["visible","false"],["width","60"]],null,[[null,"click"],[null,"keyDown"]],function(t,e,i){var o=!0,n=t.component;"click"===e&&(o=!1!==n.chekcFields()&&o);"keyDown"===e&&(o=!1!==n.keyDownEventHandler(i)&&o);return o},y.Mc,y.T)),o.Ib(171,4440064,[[42,4],["changeButton",4]],0,a.cb,[o.r,a.i],{id:[0,"id"],width:[1,"width"],visible:[2,"visible"],enabled:[3,"enabled"],label:[4,"label"]},{onClick_:"click",onKeyDown_:"keyDown"}),(t()(),o.Jb(172,0,null,0,5,"SwtPanel",[["height","23%"],["styleName","panelInsideFormLayout"],["title","Rule to add"],["width","100%"]],null,null,null,y.dd,y.kb)),o.Ib(173,4440064,null,0,a.Cb,[o.r,a.i,o.T],{styleName:[0,"styleName"],width:[1,"width"],height:[2,"height"],title:[3,"title"]},null),(t()(),o.Jb(174,0,null,0,3,"VBox",[["height","100%"],["paddingBottom","5"],["paddingRight","10"],["paddingTop","10"],["width","100%"]],null,null,null,y.od,y.vb)),o.Ib(175,4440064,null,0,a.ec,[o.r,a.i,o.T],{width:[0,"width"],height:[1,"height"],paddingTop:[2,"paddingTop"],paddingBottom:[3,"paddingBottom"],paddingRight:[4,"paddingRight"]},null),(t()(),o.Jb(176,0,null,0,1,"SwtTextArea",[["editable","false"],["height","100%"],["id","queryText"],["toolTip","Expression Rule"]],null,null,null,y.jd,y.rb)),o.Ib(177,4440064,[[51,4],["queryText",4]],0,a.Qb,[o.r,a.i,o.L],{id:[0,"id"],toolTip:[1,"toolTip"],height:[2,"height"],text:[3,"text"],editable:[4,"editable"]},null),(t()(),o.Jb(178,0,null,0,17,"SwtCanvas",[["id","buttonCanvas"]],null,null,null,y.Nc,y.U)),o.Ib(179,4440064,null,0,a.db,[o.r,a.i],{id:[0,"id"]},null),(t()(),o.Jb(180,0,null,0,15,"HBox",[["width","100%"]],null,null,null,y.Dc,y.K)),o.Ib(181,4440064,null,0,a.C,[o.r,a.i],{width:[0,"width"]},null),(t()(),o.Jb(182,0,null,0,7,"HBox",[["id","buttonHbox"],["paddingLeft","5"],["width","100%"]],null,null,null,y.Dc,y.K)),o.Ib(183,4440064,[[47,4],["buttonHbox",4]],0,a.C,[o.r,a.i],{id:[0,"id"],width:[1,"width"],paddingLeft:[2,"paddingLeft"]},null),(t()(),o.Jb(184,0,null,0,1,"SwtButton",[["enabled","false"],["id","okButton"],["label","Ok"],["toolTip","Ok"],["width","70"]],null,[[null,"click"],[null,"keyDown"]],function(t,e,i){var o=!0,n=t.component;"click"===e&&(o=!1!==n.executeQuery(i)&&o);"keyDown"===e&&(o=!1!==n.keyDownEventHandler(i)&&o);return o},y.Mc,y.T)),o.Ib(185,4440064,[[13,4],["okButton",4]],0,a.cb,[o.r,a.i],{id:[0,"id"],toolTip:[1,"toolTip"],width:[2,"width"],enabled:[3,"enabled"],label:[4,"label"]},{onClick_:"click",onKeyDown_:"keyDown"}),(t()(),o.Jb(186,0,null,0,1,"SwtButton",[["enabled","false"],["id","resetButton"],["label","Reset"],["toolTip",""],["width","70"]],null,[[null,"click"],[null,"keyDown"]],function(t,e,i){var n=!0,l=t.component;"click"===e&&(l.reset(i),n=!1!==(o.Tb(t,73).text="")&&n);"keyDown"===e&&(n=!1!==l.keyDownEventHandler(i)&&n);return n},y.Mc,y.T)),o.Ib(187,4440064,[[20,4],["resetButton",4]],0,a.cb,[o.r,a.i],{id:[0,"id"],toolTip:[1,"toolTip"],width:[2,"width"],enabled:[3,"enabled"],label:[4,"label"]},{onClick_:"click",onKeyDown_:"keyDown"}),(t()(),o.Jb(188,0,null,0,1,"SwtButton",[["id","cancelButton"],["label","Cancel"],["toolTip","Cancel"],["width","70"]],null,[[null,"click"],[null,"keyDown"]],function(t,e,i){var o=!0,n=t.component;"click"===e&&(o=!1!==n.closePopUp()&&o);"keyDown"===e&&(o=!1!==n.keyDownEventHandler(i)&&o);return o},y.Mc,y.T)),o.Ib(189,4440064,[[33,4],["cancelButton",4]],0,a.cb,[o.r,a.i],{id:[0,"id"],toolTip:[1,"toolTip"],width:[2,"width"],label:[3,"label"]},{onClick_:"click",onKeyDown_:"keyDown"}),(t()(),o.Jb(190,0,null,0,5,"HBox",[["horizontalAlign","right"],["paddingRight","10"]],null,null,null,y.Dc,y.K)),o.Ib(191,4440064,null,0,a.C,[o.r,a.i],{horizontalAlign:[0,"horizontalAlign"],paddingRight:[1,"paddingRight"]},null),(t()(),o.Jb(192,0,null,0,1,"SwtLoadingImage",[],null,null,null,y.Zc,y.gb)),o.Ib(193,114688,[[45,4],["loadingImage",4]],0,a.xb,[o.r],null,null),(t()(),o.Jb(194,0,null,0,1,"SwtHelpButton",[["enabled","true"],["id","helpIcon"],["toolTip","Help"]],null,[[null,"click"]],function(t,e,i){var o=!0,n=t.component;"click"===e&&(o=!1!==n.doHelp(i)&&o);return o},y.Wc,y.db)),o.Ib(195,4440064,[[34,4],["helpIcon",4]],0,a.rb,[o.r,a.i],{id:[0,"id"],toolTip:[1,"toolTip"],enabled:[2,"enabled"]},{onClick_:"click"})],function(t,e){var i=e.component;t(e,55,0,"100%","100%");t(e,57,0,"vboxOuter","100%","100%");t(e,59,0,"100%","90%");t(e,61,0,"100%","100%");t(e,63,0,"panelInsideFormLayout","100%","55%","Search Criteria");t(e,65,0,"100%","100%");t(e,67,0,"100%","95%");t(e,69,0,"filterBox","left","100%","15%","5");t(e,71,0,"labelFilter","50","Filter");t(e,73,0,"filterText","textbox","400","false");t(e,75,0,"0","100%","95%");t(e,77,0,"customGrid","50%","95%");t(e,79,0,"5%");t(e,81,0,"center","middle","10%","90%");t(e,83,0,"changeCritButton","editIcon","33","false","false","50",!1);t(e,85,0,"5%");t(e,87,0,"CanvOperator","40%","90%","5");t(e,89,0,"100%","100%");t(e,91,0,"left","500","35");t(e,93,0,"equalButton","60","false","=");t(e,95,0,"differentButton","60","false","<>");t(e,97,0,"inButton","60","false","In");t(e,99,0,"space1","30");t(e,101,0,"leftParentheseButton","60","(");t(e,103,0,"left","500","35");t(e,105,0,"higherButton","60","false",">");t(e,107,0,"higherequalButton","60","false",">=");t(e,109,0,"notinButton","60","false","Not In");t(e,111,0,"30");t(e,113,0,"rightParentheseButton","60","false",")");t(e,115,0,"left","500","35");t(e,117,0,"lowerButton","60","false","<");t(e,119,0,"lowerequalButton","60","false","<=");t(e,121,0,"betweenButton","60","false","Between");t(e,123,0,"30");t(e,125,0,"andButton","60","false","And");t(e,127,0,"left","500","35");t(e,129,0,"likeButton","60","false","Like");t(e,131,0,"notLikeButton","60","false","Not Like");t(e,133,0,"inListButton","60","false","In List");t(e,135,0,"30");t(e,137,0,"orButton","60","false","Or");t(e,139,0,"left","500","35");t(e,141,0,"containsButton","70","false","Contains");t(e,143,0,"100%","10%");t(e,145,0,"labelQuery","100%","100%");t(e,147,0,"panelInsideFormLayout","100%","20%","Values");t(e,149,0,"100%","100%","8");t(e,151,0,"35%","100%");t(e,153,0,"100%");t(e,155,0,"propertiesLabel","label","20");t(e,157,0,"operator","label","20","15","15",o.Lb(1,"",i.operationToDisplay,""));t(e,159,0,"vBoxContainer","60%");t(e,161,0,"hBoxContainer","50%");t(e,163,0,"hBoxContainer2","50%");t(e,165,0,"right","10%","100%","5");t(e,167,0,"addButton","60","false","2","Add");t(e,169,0,"undoButton","60","false","2","Undo");t(e,171,0,"changeButton","60","false","false","Change");t(e,173,0,"panelInsideFormLayout","100%","23%","Rule to add");t(e,175,0,"100%","100%","10","5","10");t(e,177,0,"queryText","Expression Rule","100%",o.Lb(1,"",i.queryToDisplay,""),"false");t(e,179,0,"buttonCanvas");t(e,181,0,"100%");t(e,183,0,"buttonHbox","100%","5");t(e,185,0,"okButton","Ok","70","false","Ok");t(e,187,0,"resetButton","","70","false","Reset");t(e,189,0,"cancelButton","Cancel","70","Cancel");t(e,191,0,"right","10"),t(e,193,0);t(e,195,0,"helpIcon","Help","true")},function(t,e){var i=e.component;t(e,54,0,o.Lb(1,"",i.title,""))})}function Q(t){return o.dc(0,[(t()(),o.Jb(0,0,null,null,1,"app-rules-definition-add-rule",[],null,[["window","unload"]],function(t,e,i){var n=!0;"window:unload"===e&&(n=!1!==o.Tb(t,1).unloadHandler(i)&&n);return n},U,Y)),o.Ib(1,4440064,null,0,u,[a.i,o.r],null,null)],function(t,e){t(e,1,0)},null)}var W=o.Fb("app-rules-definition-add-rule",u,Q,{maxChars:"maxChars",restrict:"restrict",id:"id",dropShadowEnabled:"dropShadowEnabled",cornerRadius:"cornerRadius",borderThickness:"borderThickness",borderStyle:"borderStyle",borderColor:"borderColor",backGroundColor:"backGroundColor",right:"right",left:"left",bottom:"bottom",top:"top",horizontalGap:"horizontalGap",verticalGap:"verticalGap",textAlign:"textAlign",toolTip:"toolTip",toolTipPreviousValue:"toolTipPreviousValue",textDictionaryId:"tooltipDictionaryId",name:"name",styleName:"styleName",horizontalAlign:"horizontalAlign",verticalAlign:"verticalAlign",width:"width",showScrollBar:"showScrollBar",height:"height",minHeight:"minHeight",minWidth:"minWidth",maxHeight:"maxHeight",maxWidth:"maxWidth",includeInLayout:"includeInLayout",visible:"visible",enabled:"enabled",paddingTop:"paddingTop",paddingBottom:"paddingBottom",paddingLeft:"paddingLeft",paddingRight:"paddingRight",marginTop:"marginTop",marginBottom:"marginBottom",marginLeft:"marginLeft",marginRight:"marginRight",contextMenu:"contextMenu"},{onClick_:"click",dbClick_:"dbClick",doubleClick_:"doubleClick",itemDoubleClick_:"itemDoubleClick",onKeyDown_:"keyDown",onKeyUp_:"keyUp",mouseUp_:"mouseUp",mouseOver_:"mouseOver",mouseDown_:"mouseDown",mouseEnter_:"mouseEnter",mouseLeave_:"mouseLeave",mouseOut_:"mouseOut",mouseIn_:"mouseIn",mouseMove_:"mouseMove",focus_:"focus",focusIn_:"focusIn",onFocusOut_:"focusOut",keyFocusChange_:"keyFocusChange",change_:"change",onSpyChange:"onSpyChange",onSpyNoChange:"onSpyNoChange",scroll_:"scroll",creationComplete:"creationComplete",preinitialize:"preinitialize"},[])}}]);