<?xml version="1.0" encoding="UTF-8"  ?>
<!-- Created with iReport - A designer for JasperReports -->
<!-- xml namespace changed,deprecated 'issplittype' attribute replaced with 'splitType' attribute for Mantis 2035 by <PERSON> on 18-Sep-2012 -->
 
<jasperReport 
  xmlns="http://jasperreports.sourceforge.net/jasperreports"

		 name="Currency Funding Report"
		 columnCount="1"
		 printOrder="Vertical"
		 orientation="Landscape"
		 pageWidth="882"
		 pageHeight="595"
		 columnWidth="822"
		 columnSpacing="0"
		 leftMargin="30"
		 rightMargin="30"
		 topMargin="20"
		 bottomMargin="20"
		 whenNoDataType="NoPages"
		 isTitleNewPage="false"
		 isSummaryNewPage="false">
	<property name="ireport.scriptlethandling" value="0" />
	<property name="ireport.encoding" value="UTF-8" />
	<import value="java.util.*" />
	<import value="net.sf.jasperreports.engine.*" />
	<import value="net.sf.jasperreports.engine.data.*" />

	<parameter name="pHost_Id" isForPrompting="true" class="java.lang.String"/>
	<parameter name="pEntity_Id" isForPrompting="true" class="java.lang.String"/>
	<parameter name="pEntity_Name" isForPrompting="true" class="java.lang.String"/>
	<parameter name="pCurrency_Code" isForPrompting="true" class="java.lang.String"/>
	<parameter name="pCurrency_Name" isForPrompting="true" class="java.lang.String"/>
	<parameter name="pCorrAcc_Id" isForPrompting="true" class="java.lang.String"/>
	<parameter name="pReport_Date" isForPrompting="true" class="java.util.Date"/>
	<parameter name="pThreshold_Value" isForPrompting="true" class="java.lang.Double"/>
	<parameter name="pShow_DR" isForPrompting="true" class="java.lang.String"/>
	<parameter name="pShow_CR" isForPrompting="true" class="java.lang.String"/>
	<parameter name="pPredict_Balanace" isForPrompting="true" class="java.lang.String"/>
	<parameter name="pActual_Balanace" isForPrompting="true" class="java.lang.String"/>
	<parameter name="pAccount_Id" isForPrompting="false" class="java.lang.String"/>
	<parameter name="pThresholdValue_format" isForPrompting="false" class="java.lang.String"/>
	<parameter name="pCurrencyPattern" isForPrompting="false" class="java.lang.String"/>
	<queryString><![CDATA[SELECT   mov.amount, mov.SIGN, pos.position_level_name, mov.reference1,
         mov.reference2, mov.reference3, mov.beneficiary_id
    FROM p_movement mov, p_position_level_name pos
   WHERE mov.host_id = pos.host_id
     AND mov.entity_id = pos.entity_id
     AND mov.position_level = pos.position_level
     AND mov.host_id = $P{pHost_Id}
     AND mov.entity_id = $P{pEntity_Id}
     AND mov.predict_status = 'I'
     AND mov.currency_code = $P{pCurrency_Code}
     AND mov.account_id = $P{pAccount_Id}
     AND mov.value_date = $P{pReport_Date}
     AND mov.amount >= $P{pThreshold_Value}
     AND (mov.SIGN = $P{pShow_DR} OR mov.SIGN = $P{pShow_CR})
     AND (   (    mov.match_status = 'L'
              AND mov.position_level <
                     (SELECT MAX (position_level)
                        FROM p_position_level_name pos
                       WHERE pos.host_id = mov.host_id
                         AND pos.entity_id = mov.entity_id
                         AND pos.internal_external = 'I')
             )
          OR (    mov.match_status IN ('C', 'M', 'S')
              AND EXISTS (
                     SELECT NULL
                       FROM p_match mat
                      WHERE mat.host_id = mov.host_id
                        AND mat.entity_id = mov.entity_id
                        AND mat.match_id = mov.match_id
                        AND highest_position_level <
                               (SELECT MAX (position_level)
                                  FROM p_position_level_name pos
                                 WHERE pos.host_id = mov.host_id
                                   AND pos.entity_id = mov.entity_id
                                   AND pos.internal_external = 'I'))
             )
         )
ORDER BY amount DESC, SIGN, movement_id]]></queryString>

	<field name="AMOUNT" class="java.math.BigDecimal"/>
	<field name="SIGN" class="java.lang.String"/>
	<field name="REFERENCE1" class="java.lang.String"/>
	<field name="REFERENCE2" class="java.lang.String"/>
	<field name="REFERENCE3" class="java.lang.String"/>
	<field name="BENEFICIARY_ID" class="java.lang.String"/>
	<field name="POSITION_LEVEL_NAME" class="java.lang.String"/>

	<variable name="CreditSum" class="java.math.BigDecimal" resetType="Report" calculation="Sum">
		<variableExpression><![CDATA[(($F{AMOUNT} != null) ? ($F{SIGN}.equals("C") ?  $F{AMOUNT} : (new BigDecimal(0.00))) : (new BigDecimal(0.00)))]]></variableExpression>
	</variable>
	<variable name="DebitSum" class="java.math.BigDecimal" resetType="Report" calculation="Sum">
		<variableExpression><![CDATA[(($F{AMOUNT} != null) ? ($F{SIGN}.equals("D") ?  $F{AMOUNT} : (new BigDecimal(0.00))) : (new BigDecimal(0.00)))]]></variableExpression>
	</variable>
		<background>
			<band height="0"  splitType="Stretch" >
			</band>
		</background>
		<title>
			<band height="143"  splitType="Stretch" >
				<staticText>
					<reportElement
						x="188"
						y="3"
						width="450"
						height="31"
						forecolor="#000000"
						backcolor="#FFFFFF"
						key="staticText-1"
						isPrintInFirstWholeBand="true"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Center">
						<font size="24"/>
					</textElement>
				<text><![CDATA[Currency Funding (Unsettled Items)]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="10"
						y="45"
						width="74"
						height="21"
						key="staticText-3"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left" verticalAlignment="Top">
						<font pdfFontName="Helvetica-Bold" size="12" isBold="true"/>
					</textElement>
				<text><![CDATA[Entity]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" pattern="" isBlankWhenNull="true" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="84"
						y="46"
						width="111"
						height="21"
						key="textField-1"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font size="11"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$P{pEntity_Id}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="false" pattern="" isBlankWhenNull="true" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="195"
						y="47"
						width="189"
						height="21"
						key="textField-8"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font size="11"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$P{pEntity_Name}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="false" pattern="dd MMMMM yyyy" isBlankWhenNull="true" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="664"
						y="67"
						width="110"
						height="20"
						key="textField-9"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font size="11"/>
					</textElement>
				<textFieldExpression   class="java.util.Date"><![CDATA[$P{pReport_Date}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="10"
						y="88"
						width="74"
						height="22"
						key="staticText-20"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left" verticalAlignment="Top">
						<font pdfFontName="Helvetica-Bold" size="12" isBold="true"/>
					</textElement>
				<text><![CDATA[Corr. Code]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="10"
						y="66"
						width="74"
						height="22"
						key="staticText-21"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left" verticalAlignment="Top">
						<font pdfFontName="Helvetica-Bold" size="12" isBold="true"/>
					</textElement>
				<text><![CDATA[Currency]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="539"
						y="66"
						width="118"
						height="22"
						key="staticText-22"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left" verticalAlignment="Top">
						<font pdfFontName="Helvetica-Bold" size="12" isBold="true"/>
					</textElement>
				<text><![CDATA[Report Date]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="539"
						y="45"
						width="123"
						height="21"
						key="staticText-23"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left" verticalAlignment="Top">
						<font pdfFontName="Helvetica-Bold" size="12" isBold="true"/>
					</textElement>
				<text><![CDATA[Threshold]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="539"
						y="87"
						width="118"
						height="22"
						key="staticText-24"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left" verticalAlignment="Top">
						<font pdfFontName="Helvetica-Bold" size="12" isBold="true"/>
					</textElement>
				<text><![CDATA[Forecasted Balance]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="539"
						y="109"
						width="118"
						height="20"
						key="staticText-25"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left" verticalAlignment="Top">
						<font pdfFontName="Helvetica-Bold" size="12" isBold="true"/>
					</textElement>
				<text><![CDATA[Actual Balance]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" pattern="" isBlankWhenNull="true" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="84"
						y="89"
						width="226"
						height="21"
						key="textField-19"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font size="11"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$P{pCorrAcc_Id}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="false" pattern="" isBlankWhenNull="true" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="84"
						y="67"
						width="111"
						height="22"
						key="textField-20"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font size="11"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$P{pCurrency_Code}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="false" pattern="" isBlankWhenNull="true" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="661"
						y="44"
						width="158"
						height="22"
						key="textField-22"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Right">
						<font size="11"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$P{pThresholdValue_format}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="false" pattern="" isBlankWhenNull="true" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="195"
						y="67"
						width="189"
						height="22"
						key="textField-23"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font size="11"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$P{pCurrency_Name}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="false" pattern="#,##0.00" isBlankWhenNull="true" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="661"
						y="87"
						width="158"
						height="22"
						key="textField-25"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Right">
						<font size="11"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$P{pPredict_Balanace}]]></textFieldExpression>
				</textField>
				<line direction="TopDown">
					<reportElement
						x="10"
						y="39"
						width="810"
						height="0"
						key="line-1"/>
					<graphicElement stretchType="NoStretch"/>
				</line>
				<line direction="TopDown">
					<reportElement
						x="10"
						y="131"
						width="810"
						height="0"
						key="line-2"/>
					<graphicElement stretchType="NoStretch"/>
				</line>
				<textField isStretchWithOverflow="false" pattern="#,##0.00" isBlankWhenNull="true" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="657"
						y="109"
						width="162"
						height="20"
						key="textField-27"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Right">
						<font size="11"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$P{pActual_Balanace}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="652"
						y="109"
						width="8"
						height="17"
						key="staticText-43"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left" verticalAlignment="Top">
						<font pdfFontName="Helvetica-Bold" size="12" isBold="true" isUnderline="false"/>
					</textElement>
				<text><![CDATA[ :]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="652"
						y="86"
						width="8"
						height="17"
						key="staticText-44"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left" verticalAlignment="Top">
						<font pdfFontName="Helvetica-Bold" size="12" isBold="true" isUnderline="false"/>
					</textElement>
				<text><![CDATA[ :]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="652"
						y="66"
						width="8"
						height="17"
						key="staticText-45"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left" verticalAlignment="Top">
						<font pdfFontName="Helvetica-Bold" size="12" isBold="true" isUnderline="false"/>
					</textElement>
				<text><![CDATA[ :]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="652"
						y="44"
						width="8"
						height="17"
						key="staticText-46"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left" verticalAlignment="Top">
						<font pdfFontName="Helvetica-Bold" size="12" isBold="true" isUnderline="false"/>
					</textElement>
				<text><![CDATA[ :]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="70"
						y="88"
						width="14"
						height="17"
						key="staticText-47"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left" verticalAlignment="Top">
						<font pdfFontName="Helvetica-Bold" size="12" isBold="true" isUnderline="false"/>
					</textElement>
				<text><![CDATA[ :]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="70"
						y="67"
						width="14"
						height="17"
						key="staticText-48"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left" verticalAlignment="Top">
						<font pdfFontName="Helvetica-Bold" size="12" isBold="true" isUnderline="false"/>
					</textElement>
				<text><![CDATA[ :]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="70"
						y="45"
						width="14"
						height="20"
						key="staticText-49"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left" verticalAlignment="Top">
						<font pdfFontName="Helvetica-Bold" size="12" isBold="true" isUnderline="false"/>
					</textElement>
				<text><![CDATA[ :]]></text>
				</staticText>
			</band>
		</title>
		<pageHeader>
			<band height="25"  splitType="Prevent" >
				<staticText>
					<reportElement
						x="10"
						y="5"
						width="115"
						height="17"
						key="staticText-5">
							<printWhenExpression><![CDATA[($F{AMOUNT}!=null) ?  new Boolean(true) :new Boolean(false)]]></printWhenExpression>
						</reportElement>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Right" verticalAlignment="Top">
						<font pdfFontName="Helvetica-Bold" size="10" isBold="true" isUnderline="false"/>
					</textElement>
				<text><![CDATA[                 Amount]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="137"
						y="5"
						width="27"
						height="17"
						key="staticText-26">
							<printWhenExpression><![CDATA[($F{AMOUNT}!=null) ?  new Boolean(true) :new Boolean(false)]]></printWhenExpression>
						</reportElement>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left" verticalAlignment="Top">
						<font pdfFontName="Helvetica-Bold" size="10" isBold="true" isUnderline="false"/>
					</textElement>
				<text><![CDATA[Sign]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="703"
						y="5"
						width="115"
						height="17"
						key="staticText-37">
							<printWhenExpression><![CDATA[($F{AMOUNT}!=null) ?  new Boolean(true) :new Boolean(false)]]></printWhenExpression>
						</reportElement>
					<box></box>
					<textElement textAlignment="Left">
						<font pdfFontName="Helvetica-Bold" size="10" isBold="true" isUnderline="false"/>
					</textElement>
				<text><![CDATA[Beneficiary]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="366"
						y="5"
						width="115"
						height="17"
						key="staticText-39">
							<printWhenExpression><![CDATA[($F{AMOUNT}!=null) ?  new Boolean(true) :new Boolean(false)]]></printWhenExpression>
						</reportElement>
					<box></box>
					<textElement textAlignment="Left">
						<font pdfFontName="Helvetica-Bold" size="10" isBold="true" isUnderline="false"/>
					</textElement>
				<text><![CDATA[Reference 2]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="481"
						y="5"
						width="206"
						height="17"
						key="staticText-40">
							<printWhenExpression><![CDATA[($F{AMOUNT}!=null) ?  new Boolean(true) :new Boolean(false)]]></printWhenExpression>
						</reportElement>
					<box></box>
					<textElement textAlignment="Left">
						<font pdfFontName="Helvetica-Bold" size="10" isBold="true" isUnderline="false"/>
					</textElement>
				<text><![CDATA[Reference 3]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="251"
						y="5"
						width="115"
						height="17"
						key="staticText-41">
							<printWhenExpression><![CDATA[($F{AMOUNT}!=null) ?  new Boolean(true) :new Boolean(false)]]></printWhenExpression>
						</reportElement>
					<box></box>
					<textElement textAlignment="Left" verticalAlignment="Top">
						<font pdfFontName="Helvetica-Bold" size="10" isBold="true" isUnderline="false"/>
					</textElement>
				<text><![CDATA[Reference 1]]></text>
				</staticText>
				<line direction="TopDown">
					<reportElement
						x="251"
						y="17"
						width="104"
						height="0"
						key="line-4">
							<printWhenExpression><![CDATA[($F{AMOUNT}!=null) ?  new Boolean(true) :new Boolean(false)]]></printWhenExpression>
						</reportElement>
					<graphicElement stretchType="NoStretch"/>
				</line>
				<line direction="TopDown">
					<reportElement
						x="365"
						y="17"
						width="104"
						height="0"
						key="line-5">
							<printWhenExpression><![CDATA[($F{AMOUNT}!=null) ?  new Boolean(true) :new Boolean(false)]]></printWhenExpression>
						</reportElement>
					<graphicElement stretchType="NoStretch"/>
				</line>
				<line direction="TopDown">
					<reportElement
						x="481"
						y="18"
						width="190"
						height="0"
						key="line-6">
							<printWhenExpression><![CDATA[($F{AMOUNT}!=null) ?  new Boolean(true) :new Boolean(false)]]></printWhenExpression>
						</reportElement>
					<graphicElement stretchType="NoStretch"/>
				</line>
				<line direction="TopDown">
					<reportElement
						x="704"
						y="18"
						width="112"
						height="0"
						key="line-7">
							<printWhenExpression><![CDATA[($F{AMOUNT}!=null) ?  new Boolean(true) :new Boolean(false)]]></printWhenExpression>
						</reportElement>
					<graphicElement stretchType="NoStretch"/>
				</line>
				<line direction="TopDown">
					<reportElement
						x="137"
						y="17"
						width="24"
						height="0"
						key="line-10">
							<printWhenExpression><![CDATA[($F{AMOUNT}!=null) ?  new Boolean(true) :new Boolean(false)]]></printWhenExpression>
						</reportElement>
					<graphicElement stretchType="NoStretch"/>
				</line>
				<line direction="TopDown">
					<reportElement
						x="19"
						y="17"
						width="105"
						height="0"
						key="line-11">
							<printWhenExpression><![CDATA[($F{AMOUNT}!=null) ?  new Boolean(true) :new Boolean(false)]]></printWhenExpression>
						</reportElement>
					<graphicElement stretchType="NoStretch"/>
				</line>
				<staticText>
					<reportElement
						x="166"
						y="5"
						width="85"
						height="17"
						key="staticText-50">
							<printWhenExpression><![CDATA[($F{AMOUNT}!=null) ?  new Boolean(true) :new Boolean(false)]]></printWhenExpression>
						</reportElement>
					<box></box>
					<textElement textAlignment="Left" verticalAlignment="Top">
						<font pdfFontName="Helvetica-Bold" size="10" isBold="true" isUnderline="false"/>
					</textElement>
				<text><![CDATA[Level]]></text>
				</staticText>
				<line direction="TopDown">
					<reportElement
						x="166"
						y="17"
						width="75"
						height="0"
						key="line-12">
							<printWhenExpression><![CDATA[($F{AMOUNT}!=null) ?  new Boolean(true) :new Boolean(false)]]></printWhenExpression>
						</reportElement>
					<graphicElement stretchType="NoStretch"/>
				</line>
			</band>
		</pageHeader>
		<columnHeader>
			<band height="0"  splitType="Prevent" >
			</band>
		</columnHeader>
		<detail>
			<band height="28"  splitType="Stretch" >
				<textField isStretchWithOverflow="false" pattern="" isBlankWhenNull="true" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						mode="Opaque"
						x="10"
						y="7"
						width="115"
						height="19"
						key="textField-4"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Right">
						<font pdfFontName="Helvetica" size="10" isBold="false"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[($F{AMOUNT}!=null) ?(($P{pCurrencyPattern}.equals("currencyPat1")) ? new DecimalFormat("#,##0.00").format($F{AMOUNT}): new DecimalFormat("#,##0.00").format($F{AMOUNT}).replaceAll("\\.", "^").replaceAll(",", ".").replaceAll("\\^", ",")):""]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="false" pattern="" isBlankWhenNull="true" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="137"
						y="7"
						width="29"
						height="19"
						key="textField-5"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font size="10"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{SIGN}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="false" pattern="" isBlankWhenNull="true" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="251"
						y="7"
						width="115"
						height="19"
						key="textField-6"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font size="10"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{REFERENCE1}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="false" pattern="" isBlankWhenNull="true" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="366"
						y="7"
						width="115"
						height="19"
						key="textField-7"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font size="10"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{REFERENCE2}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="false" pattern="" isBlankWhenNull="true" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="481"
						y="7"
						width="223"
						height="19"
						key="textField-10"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font size="10"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{REFERENCE3}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="false" pattern="" isBlankWhenNull="true" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="704"
						y="7"
						width="115"
						height="19"
						key="textField-26"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font size="10"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{BENEFICIARY_ID}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="false" pattern="" isBlankWhenNull="true" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="166"
						y="7"
						width="85"
						height="19"
						key="textField-28"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font size="10"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{POSITION_LEVEL_NAME}]]></textFieldExpression>
				</textField>
			</band>
		</detail>
		<columnFooter>
			<band height="0"  splitType="Stretch" >
			</band>
		</columnFooter>
		<pageFooter>
			<band height="44"  splitType="Stretch" >
				<textField isStretchWithOverflow="false" pattern="" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="612"
						y="25"
						width="172"
						height="19"
						forecolor="#000000"
						backcolor="#FFFFFF"
						key="textField-16"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Right">
						<font size="10"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA["Page " + $V{PAGE_NUMBER} + " of "+" "]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="false" pattern="" isBlankWhenNull="false" evaluationTime="Report" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="782"
						y="25"
						width="40"
						height="19"
						forecolor="#000000"
						backcolor="#FFFFFF"
						key="textField-17"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement>
						<font size="10"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA["  " + $V{PAGE_NUMBER}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="false" pattern="" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="10"
						y="23"
						width="100"
						height="21"
						forecolor="#000000"
						backcolor="#FFFFFF"
						key="textField-18"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement>
						<font size="10"/>
					</textElement>
				<textFieldExpression   class="java.util.Date"><![CDATA[new Date()]]></textFieldExpression>
				</textField>
			</band>
		</pageFooter>
		<summary>
			<band height="140"  splitType="Stretch" >
				<staticText>
					<reportElement
						x="338"
						y="21"
						width="145"
						height="18"
						key="staticText-12"
						stretchType="RelativeToBandHeight">
							<printWhenExpression><![CDATA[($F{SIGN}==null) ?  new Boolean(true) :new Boolean(false)]]></printWhenExpression>
						</reportElement>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Justified" verticalAlignment="Bottom">
						<font size="12"/>
					</textElement>
				<text><![CDATA[     No records to display]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="347"
						y="122"
						width="157"
						height="18"
						key="staticText-16"
						positionType="FixRelativeToBottom"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement>
						<font size="14"/>
					</textElement>
				<text><![CDATA[*** End of Report ***]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="10"
						y="21"
						width="99"
						height="21"
						key="staticText-34">
							<printWhenExpression><![CDATA[($F{AMOUNT}!=null) ?  new Boolean(true) :new Boolean(false)]]></printWhenExpression>
						</reportElement>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left" verticalAlignment="Top">
						<font pdfFontName="Helvetica-Bold" size="10" isBold="true" isUnderline="true"/>
					</textElement>
				<text><![CDATA[Total of Amounts]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="42"
						y="54"
						width="52"
						height="17"
						key="staticText-35">
							<printWhenExpression><![CDATA[($F{AMOUNT}!=null) ?  new Boolean(true) :new Boolean(false)]]></printWhenExpression>
						</reportElement>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Right" verticalAlignment="Top">
						<font pdfFontName="Helvetica-Bold" size="10" isBold="true" isUnderline="false"/>
					</textElement>
				<text><![CDATA[Credits  :]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="42"
						y="76"
						width="52"
						height="17"
						key="staticText-36">
							<printWhenExpression><![CDATA[($F{AMOUNT}!=null) ?  new Boolean(true) :new Boolean(false)]]></printWhenExpression>
						</reportElement>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Right" verticalAlignment="Top">
						<font pdfFontName="Helvetica-Bold" size="10" isBold="true" isUnderline="false"/>
					</textElement>
				<text><![CDATA[Debits   :]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" pattern="" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="95"
						y="54"
						width="125"
						height="17"
						key="textField">
							<printWhenExpression><![CDATA[($F{AMOUNT}!=null) ?  new Boolean(true) :new Boolean(false)]]></printWhenExpression>
						</reportElement>
					<box></box>
					<textElement textAlignment="Right">
						<font size="10"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[($V{CreditSum}!=null) ?(($P{pCurrencyPattern}.equals("currencyPat1")) ? new DecimalFormat("#,##0.00").format($V{CreditSum}): new DecimalFormat("#,##0.00").format($V{CreditSum}).replaceAll("\\.", "^").replaceAll(",", ".").replaceAll("\\^", ",")):""]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="false" pattern="" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="95"
						y="76"
						width="125"
						height="17"
						key="textField">
							<printWhenExpression><![CDATA[($F{AMOUNT}!=null) ?  new Boolean(true) :new Boolean(false)]]></printWhenExpression>
						</reportElement>
					<box></box>
					<textElement textAlignment="Right">
						<font size="10"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[($V{DebitSum}!=null) ?(($P{pCurrencyPattern}.equals("currencyPat1")) ? new DecimalFormat("#,##0.00").format($V{DebitSum}): new DecimalFormat("#,##0.00").format($V{DebitSum}).replaceAll("\\.", "^").replaceAll(",", ".").replaceAll("\\^", ",")):""]]></textFieldExpression>
				</textField>
				<line direction="TopDown">
					<reportElement
						x="10"
						y="5"
						width="810"
						height="1"
						key="line-3">
							<printWhenExpression><![CDATA[($F{AMOUNT}!=null) ?  new Boolean(true) :new Boolean(false)]]></printWhenExpression>
						</reportElement>
					<graphicElement stretchType="NoStretch"/>
				</line>
			</band>
		</summary>
</jasperReport>
