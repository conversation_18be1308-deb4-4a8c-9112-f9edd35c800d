<?xml version="1.0" encoding="UTF-8"  ?>
<!-- Created with iReport - A designer for JasperReports -->
<!-- xml namespace changed,deprecated 'issplittype' attribute replaced with 'splitType' attribute for Mantis 2035 by <PERSON> on 18-Sep-2012 -->
<jasperReport 
  xmlns="http://jasperreports.sourceforge.net/jasperreports"
		 name="RoleReportExcel"
		 columnCount="1"
		 printOrder="Vertical"
		 orientation="Portrait"
		 pageWidth="780"
		 pageHeight="6500"
		 columnWidth="535"
		 columnSpacing="0"
		 leftMargin="30"
		 rightMargin="30"
		 topMargin="20"
		 bottomMargin="20"
		 whenNoDataType="NoPages"
		 isTitleNewPage="false"
		 isSummaryNewPage="false">
	<property name="ireport.scriptlethandling" value="0" />
	<property name="ireport.encoding" value="UTF-8" />
	<import value="java.util.*" />
	<import value="net.sf.jasperreports.engine.*" />
	<import value="net.sf.jasperreports.engine.data.*" />

	<parameter name="P_ROLE" isForPrompting="true" class="java.lang.String"/>
	<parameter name="SUBREPORT_DIR" isForPrompting="true" class="java.lang.String">
		<defaultValueExpression ><![CDATA["C:\\Program Files\\JasperSoft\\iReport-1.3.3\\stl_compiled\\"]]></defaultValueExpression>
	</parameter>
	 
	<queryString><![CDATA[SELECT S_ROLE.HOST_ID,
       S_ROLE.ROLE_ID,
       S_ROLE.ROLE_NAME,
       DECODE(S_ROLE.ALERT_TYPE,0,'Both',1,'Email',2,'Pop-up','') ALERT_TYPE,
       DECODE(S_ROLE.AUTHORIZE_INPUT,'Y','Yes','No') AUTHORIZE_INPUT,
       DECODE(S_ROLE.RESTRICT_LOCATIONS,'Y','Yes','No') RESTRICT_LOCATIONS,
       DECODE(S_ROLE.ALL_ENTITY_OPTION,'Y','Yes','No') ALL_ENTITY_OPTION,
       DECODE(S_ROLE.INTERFACE_INTERRUPTION,'Y','Yes','No') INTERFACE_INTERRUPTION,
       DECODE(S_ROLE.ADVANCED_USER,'Y','Yes','No') ADVANCED_USER,      
	   DECODE(S_ROLE.ACCOUNT_ACCESS_CONTROL,'Y','Yes','No') ACCOUNT_ACCESS_CONTROL,   
	   DECODE(S_ROLE.MAINTAIN_ANY_ILM_SCENARIO,'Y','Yes','No') MAINTAIN_ANY_ILM_SCENARIO,    
	   DECODE(S_ROLE.MAINTAIN_ANY_ILM_GROUP,'Y','Yes','No') MAINTAIN_ANY_ILM_GROUP	
FROM S_ROLE
WHERE ($P{P_ROLE} IS NULL OR S_ROLE.ROLE_ID = $P{P_ROLE})ORDER BY ROLE_ID]]></queryString>
	
	<field name="HOST_ID" class="java.lang.String"/>
	<field name="ROLE_ID" class="java.lang.String"/>
	<field name="ROLE_NAME" class="java.lang.String"/>
	<field name="ALERT_TYPE" class="java.lang.String"/>
	<field name="AUTHORIZE_INPUT" class="java.lang.String"/>
	<field name="RESTRICT_LOCATIONS" class="java.lang.String"/>
	<field name="ALL_ENTITY_OPTION" class="java.lang.String"/>
	<field name="INTERFACE_INTERRUPTION" class="java.lang.String"/>
	<field name="ADVANCED_USER" class="java.lang.String"/>
	<field name="MAINTAIN_ANY_ILM_GROUP" class="java.lang.String"/>
	<field name="MAINTAIN_ANY_ILM_SCENARIO" class="java.lang.String"/>
	
	<field name="ACCOUNT_ACCESS_CONTROL" class="java.lang.String"/>
	

		<group  name="Group1" isStartNewPage="true" >
			<groupExpression><![CDATA[$F{ROLE_ID}]]></groupExpression>
			<groupHeader>
			<band height="0"  splitType="Stretch" >
			</band>
			</groupHeader>
			<groupFooter>
			<band height="0"  splitType="Stretch" >
			</band>
			</groupFooter>
		</group>
		<background>
			<band height="0"  splitType="Stretch" >
			</band>
		</background>
		<title>
			<band height="0"  splitType="Stretch" >
			</band>
		</title>
		<pageHeader>
			<band height="0"  splitType="Stretch" >
			</band>
		</pageHeader>
		<columnHeader>
			<band height="0"  splitType="Stretch" >
			</band>
		</columnHeader>
		<detail>
		
			<band height="358"  splitType="Stretch" >
		
				<staticText>
					<reportElement
						x="0"
						y="0"
						width="575"
						height="40"
						forecolor="#000000"
						backcolor="#FFFFFF"
						key="staticText"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Center">						
						<font fontName="SansSerif" size="30"/>
					</textElement>
				<text><![CDATA[Role Report]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" pattern="dd-MMM-yyyy" isBlankWhenNull="false" evaluationTime="Report" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="390"
						y="40"
						width="185"
						height="20"
						key="textField-1"/>
					<box></box>
				
					<textElement textAlignment="Right" verticalAlignment="Middle">						
						<font fontName="SansSerif" size="10"/>
					</textElement>
				<textFieldExpression   class="java.util.Date"><![CDATA[new java.util.Date()]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="197"
						y="40"
						width="193"
						height="20"
						key="staticText-1"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">						
						<font fontName="SansSerif" pdfFontName="Helvetica-Bold" size="12" isBold="true"/>
					</textElement>
				<text><![CDATA[Report Date:]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" pattern="" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						mode="Opaque"
						x="0"
						y="60"
						width="575"
						height="18"
						forecolor="#FFFFFF"
						backcolor="#000000"
						key="textField"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement>						
						<font fontName="SansSerif" pdfFontName="Helvetica-Bold" size="12" isBold="true"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{ROLE_ID}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="false" pattern="" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="197"
						y="78"
						width="193"
						height="17"
						forecolor="#000000"
						backcolor="#FFFFFF"
						key="textField"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement>						
						<font fontName="SansSerif" size="10"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{ROLE_NAME}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="0"
						y="78"
						width="197"
						height="17"
						forecolor="#000000"
						backcolor="#FFFFFF"
						key="staticText"
						positionType="Float"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement>						
						<font fontName="SansSerif" size="10"/>
					</textElement>
				<text><![CDATA[Name:]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" pattern="" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="197"
						y="95"
						width="193"
						height="17"
						forecolor="#000000"
						backcolor="#FFFFFF"
						key="textField"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement>						
						<font fontName="SansSerif" size="10"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{ALERT_TYPE}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="0"
						y="95"
						width="197"
						height="17"
						forecolor="#000000"
						backcolor="#FFFFFF"
						key="staticText"
						positionType="Float"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement>						
						<font fontName="SansSerif" size="10"/>
					</textElement>
				<text><![CDATA[Alert Type:]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" pattern="" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="197"
						y="112"
						width="193"
						height="17"
						forecolor="#000000"
						backcolor="#FFFFFF"
						key="textField"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement>						
						<font fontName="SansSerif" size="10"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{AUTHORIZE_INPUT}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="0"
						y="112"
						width="197"
						height="17"
						forecolor="#000000"
						backcolor="#FFFFFF"
						key="staticText"
						positionType="Float"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement>						
						<font fontName="SansSerif" size="10"/>
					</textElement>
				<text><![CDATA[Authorise Manual input:]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" pattern="" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="197"
						y="180"
						width="193"
						height="17"
						forecolor="#000000"
						backcolor="#FFFFFF"
						key="textField"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement>						
						<font fontName="SansSerif" size="10"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{RESTRICT_LOCATIONS}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="0"
						y="180"
						width="197"
						height="17"
						forecolor="#000000"
						backcolor="#FFFFFF"
						key="staticText"
						positionType="Float"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement>						
						<font fontName="SansSerif" size="10"/>
					</textElement>
				<text><![CDATA[Location Restriction:]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" pattern="" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="197"
						y="129"
						width="193"
						height="17"
						forecolor="#000000"
						backcolor="#FFFFFF"
						key="textField"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement>						
						<font fontName="SansSerif" size="10"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{ALL_ENTITY_OPTION}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="0"
						y="129"
						width="197"
						height="17"
						forecolor="#000000"
						backcolor="#FFFFFF"
						key="staticText"
						positionType="Float"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement>						
						<font fontName="SansSerif" size="10"/>
					</textElement>
				<text><![CDATA['All' Entity Selection in Monitors:]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" pattern="" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="197"
						y="146"
						width="193"
						height="17"
						forecolor="#000000"
						backcolor="#FFFFFF"
						key="textField"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement>						
						<font fontName="SansSerif" size="10"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{INTERFACE_INTERRUPTION}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="false" pattern="" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="197"
						y="163"
						width="193"
						height="17"
						forecolor="#000000"
						backcolor="#FFFFFF"
						key="textField"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement>						
						<font fontName="SansSerif" size="10"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{ADVANCED_USER}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="0"
						y="163"
						width="197"
						height="17"
						forecolor="#000000"
						backcolor="#FFFFFF"
						key="staticText"
						positionType="Float"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement>						
						<font fontName="SansSerif" size="10"/>
					</textElement>
				<text><![CDATA[Advanced User Config:]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="0"
						y="146"
						width="197"
						height="17"
						forecolor="#000000"
						backcolor="#FFFFFF"
						key="staticText-2"
						positionType="Float"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement verticalAlignment="Top">					
						<font fontName="SansSerif" size="10"/>
					</textElement>
				<text><![CDATA[Notification - Interface Interruption:]]></text>
				</staticText>
				<subreport  isUsingCache="true">				
					<reportElement
						x="-30"
						y="266"
						width="605"
						height="20"
						key="subreport-1"
						positionType="Float"/>
					<subreportParameter  name="P_ROLE">
						<subreportParameterExpression><![CDATA[$F{ROLE_ID}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter  name="P_RESTRICT_LOCATIONS">
						<subreportParameterExpression><![CDATA[$F{RESTRICT_LOCATIONS}]]></subreportParameterExpression>
					</subreportParameter>
					<connectionExpression><![CDATA[$P{REPORT_CONNECTION}]]></connectionExpression>
					<subreportExpression  class="java.lang.String"><![CDATA[$P{SUBREPORT_DIR} + "subRepRoleLocationAccessExcel.jasper"]]></subreportExpression>
				</subreport>
				<subreport  isUsingCache="true">
					<reportElement
						x="-30"
						y="246"
						width="605"
						height="20"
						key="subreport-2"
						positionType="Float"/>
					<subreportParameter  name="P_ROLE">
						<subreportParameterExpression><![CDATA[$F{ROLE_ID}]]></subreportParameterExpression>
					</subreportParameter>
					<connectionExpression><![CDATA[$P{REPORT_CONNECTION}]]></connectionExpression>
					<subreportExpression  class="java.lang.String"><![CDATA[$P{SUBREPORT_DIR} + "subRepRoleCcyAccessExcel.jasper"]]></subreportExpression>
				</subreport>
				<subreport  isUsingCache="true">
					<reportElement
						x="-30"
						y="226"
						width="605"
						height="20"
						key="subreport-3"
						positionType="Float"/>
					<subreportParameter  name="P_ROLE">
						<subreportParameterExpression><![CDATA[$F{ROLE_ID}]]></subreportParameterExpression>
					</subreportParameter>
					<connectionExpression><![CDATA[$P{REPORT_CONNECTION}]]></connectionExpression>
					<subreportExpression  class="java.lang.String"><![CDATA[$P{SUBREPORT_DIR} + "subRepRoleEntityAccessExcel.jasper"]]></subreportExpression>
				</subreport>
				<subreport  isUsingCache="true">
					<reportElement
						x="-30"
						y="276"
						width="605"
						height="20"
						key="subreport-4"
						positionType="Float"/>
					<subreportParameter  name="P_ROLE">
						<subreportParameterExpression><![CDATA[$F{ROLE_ID}]]></subreportParameterExpression>
					</subreportParameter>
					<connectionExpression><![CDATA[$P{REPORT_CONNECTION}]]></connectionExpression>
					<subreportExpression  class="java.lang.String"><![CDATA[$P{SUBREPORT_DIR} + "subRepRoleSweepLimitsExcel.jasper"]]></subreportExpression>
				</subreport>
				<subreport  isUsingCache="true">
					<reportElement
						x="-30"
						y="306"
						width="605"
						height="20"
						key="subreport-5"
						positionType="Float"/>
					<subreportParameter  name="P_ROLE">
						<subreportParameterExpression><![CDATA[$F{ROLE_ID}]]></subreportParameterExpression>
					</subreportParameter>
					<connectionExpression><![CDATA[$P{REPORT_CONNECTION}]]></connectionExpression>
					<subreportExpression  class="java.lang.String"><![CDATA[$P{SUBREPORT_DIR} + "subRepRoleUsersExcel.jasper"]]></subreportExpression>
				</subreport>
				<subreport  isUsingCache="true">
					<reportElement
						x="-30"
						y="326"
						width="605"
						height="20"
						key="subreport-6"
						positionType="Float"/>
					<subreportParameter  name="P_ROLE">
						<subreportParameterExpression><![CDATA[$F{ROLE_ID}]]></subreportParameterExpression>
					</subreportParameter>
					<connectionExpression><![CDATA[$P{REPORT_CONNECTION}]]></connectionExpression>
					<subreportExpression  class="java.lang.String"><![CDATA[$P{SUBREPORT_DIR} + "subRepRoleMenuAccessExcel.jasper"]]></subreportExpression>
				</subreport>
				<textField isStretchWithOverflow="false" pattern="" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="197"
						y="197"
						width="193"
						height="17"
						forecolor="#000000"
						backcolor="#FFFFFF"
						key="textField-2"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement>						
						<font fontName="SansSerif" size="10"/>
					</textElement>
				
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{ACCOUNT_ACCESS_CONTROL}]]></textFieldExpression>				
				</textField>
				
				<staticText>
					<reportElement
						x="0"
						y="197"
						width="197"
						height="17"
						forecolor="#000000"
						backcolor="#FFFFFF"
						key="staticText-3"
						positionType="Float"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement>					
						<font fontName="SansSerif" size="10"/>
					</textElement>
				<text><![CDATA[Account Access Control:]]></text>
				</staticText>				
				<textField isStretchWithOverflow="false" pattern="" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="197"
						y="217"
						width="193"
						height="17"
						forecolor="#000000"
						backcolor="#FFFFFF"
						key="textField-2"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement>						
						<font fontName="SansSerif" size="10"/>
					</textElement>
				
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{MAINTAIN_ANY_ILM_SCENARIO}]]></textFieldExpression>				
				</textField>
				
				<staticText>
					<reportElement
						x="0"
						y="217"
						width="197"
						height="17"
						forecolor="#000000"
						backcolor="#FFFFFF"
						key="staticText-3"
						positionType="Float"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement>					
						<font fontName="SansSerif" size="10"/>
					</textElement>
				<text><![CDATA[Maintain Any ILM Scenario:]]></text>
				</staticText>				
				<textField isStretchWithOverflow="false" pattern="" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="197"
						y="237"
						width="193"
						height="17"
						forecolor="#000000"
						backcolor="#FFFFFF"
						key="textField-2"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement>						
						<font fontName="SansSerif" size="10"/>
					</textElement>
				
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{MAINTAIN_ANY_ILM_GROUP}]]></textFieldExpression>				
				</textField>
				
				<staticText>
					<reportElement
						x="0"
						y="237"
						width="197"
						height="17"
						forecolor="#000000"
						backcolor="#FFFFFF"
						key="staticText-3"
						positionType="Float"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement>					
						<font fontName="SansSerif" size="10"/>
					</textElement>
				<text><![CDATA[Maintain Any ILM Account Group:]]></text>
				</staticText>				
			</band>
		</detail>
		<columnFooter>
			<band height="0"  splitType="Stretch" >
			</band>
		</columnFooter>
		<pageFooter>
			<band height="0"  splitType="Stretch" >
			</band>
		</pageFooter>
		<summary>
			<band height="0"  splitType="Stretch" >
			</band>
		</summary>
</jasperReport>
