<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="OpportunityCost" pageWidth="842" pageHeight="99999" orientation="Landscape" whenNoDataType="AllSectionsNoDetail" columnWidth="782" leftMargin="30" rightMargin="30" topMargin="20" bottomMargin="20" uuid="ef40fd0b-53bf-4d93-8691-2861e49c7aaf">
	<property name="ireport.scriptlethandling" value="0"/>
	<property name="ireport.encoding" value="UTF-8"/>
	<property name="ireport.zoom" value="1.0000000000000029"/>
	<property name="ireport.x" value="7"/>
	<property name="ireport.y" value="0"/>
	<import value="net.sf.jasperreports.engine.*"/>
	<import value="java.util.*"/>
	<import value="net.sf.jasperreports.engine.data.*"/>
	<style name="TEST" forecolor="#000000" isBold="false" isItalic="false">
		<conditionalStyle>
			<conditionExpression><![CDATA[java.lang.Boolean.valueOf($F{MAIN_OR_SUB}.equalsIgnoreCase("M"))]]></conditionExpression>
			<style forecolor="#000000" isBold="true" isItalic="false" isUnderline="true"/>
		</conditionalStyle>
	</style>
	<style name="table">
		<box>
			<pen lineWidth="1.0" lineColor="#000000"/>
		</box>
	</style>
	<style name="table_TH" mode="Opaque" backcolor="#F0F8FF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="table_CH" mode="Opaque" backcolor="#BFE1FF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="table_TD" mode="Opaque" backcolor="#FFFFFF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="table 1">
		<box>
			<pen lineWidth="1.0" lineColor="#000000"/>
		</box>
	</style>
	<style name="table 1_TH" mode="Opaque" backcolor="#F0F8FF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="table 1_CH" mode="Opaque" backcolor="#BFE1FF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="table 1_TD" mode="Opaque" backcolor="#FFFFFF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
		</box>
		<conditionalStyle>
			<conditionExpression><![CDATA[new Boolean($V{REPORT_COUNT}.intValue()%2==0)]]></conditionExpression>
			<style backcolor="#EFF7FF"/>
		</conditionalStyle>
	</style>
	<style name="table 2">
		<box>
			<pen lineWidth="1.0" lineColor="#000000"/>
		</box>
	</style>
	<style name="table 2_TH" mode="Opaque" backcolor="#F0F8FF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="table 2_CH" mode="Opaque" backcolor="#BFE1FF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="table 2_TD" mode="Opaque" backcolor="#FFFFFF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="table 3">
		<box>
			<pen lineWidth="1.0" lineColor="#000000"/>
		</box>
	</style>
	<style name="table 3_TH" mode="Opaque" backcolor="#F0F8FF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="table 3_CH" mode="Opaque" backcolor="#BFE1FF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="table 3_TD" mode="Opaque" backcolor="#FFFFFF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
		</box>
		<conditionalStyle>
			<conditionExpression><![CDATA[new Boolean($V{REPORT_COUNT}.intValue()%2==0)]]></conditionExpression>
			<style backcolor="#EFF7FF"/>
		</conditionalStyle>
	</style>
	<subDataset name="New Dataset 1" uuid="0a034fc9-482d-41fb-b46b-3c797479b8a4">
		<parameter name="pHOST" class="java.lang.String">
			<defaultValueExpression><![CDATA[""]]></defaultValueExpression>
		</parameter>
		<parameter name="pENTITY" class="java.lang.String">
			<defaultValueExpression><![CDATA[""]]></defaultValueExpression>
		</parameter>
		<parameter name="pREPORT_DATE" class="java.util.Date"/>
		<parameter name="pTHRESHOLD" class="java.lang.Double"/>
		<parameter name="pCurrencyPattern" class="java.lang.String" isForPrompting="false"/>
		<parameter name="pCURRENCYCODE" class="java.lang.String" isForPrompting="false"/>
		<parameter name="pREP_CCY" class="java.lang.String">
			<defaultValueExpression><![CDATA[""]]></defaultValueExpression>
		</parameter>
		<parameter name="pOutputFormat" class="java.lang.String"/>
		<queryString>
			<![CDATA[WITH PARMS
     AS (SELECT $P{pREPORT_DATE} PREPORT_DATE,
                $P{pHOST} PHOST,
                $P{pENTITY} PENTITY,
                $P{pCURRENCYCODE} PCURRENCYCODE,
                $P{pTHRESHOLD} PTHRESHOLD
           FROM DUAL)
--SELECT * FROM PARMS;
,
     ACCS
     AS (SELECT P_ACCOUNT.HOST_ID,
                P_ACCOUNT.ENTITY_ID,
                P_ACCOUNT.ACCOUNT_ID,
                P_ACCOUNT.ACCOUNT_CLASS,
                P_ACCOUNT.ACCOUNT_LEVEL,
                P_ACCOUNT.ACCOUNT_NAME,
                P_ACCOUNT.ACCOUNT_TYPE,
                P_ACCOUNT.CURRENCY_CODE,
                PKG_NON_WORKDAY.GETPREVBUSINESSDATE (
                                 'OPPORTUNITY_COST_REPORT',
                                  $P{pREPORT_DATE},
                                  $P{pENTITY},
                                  P_ACCOUNT.ACCOUNT_ID,
                                  P_ACCOUNT.CURRENCY_CODE) PREV_BUSINESS_DATE
           FROM P_ACCOUNT
          WHERE P_ACCOUNT.HOST_ID = $P{pHOST}
            AND P_ACCOUNT.ENTITY_ID = $P{pENTITY}
            AND P_ACCOUNT.CURRENCY_CODE = $P{pCURRENCYCODE}
            AND P_ACCOUNT.ACCOUNT_ID != '*'
            AND P_ACCOUNT.ACCOUNT_CLASS = 'N'
            AND P_ACCOUNT.ACCOUNT_TYPE IN ('C', 'U')
            AND PKG_NON_WORKDAY.ISDATEAHOLIDAY ('A',
                                                $P{pREPORT_DATE},
                                                $P{pENTITY},
                                                P_ACCOUNT.ACCOUNT_ID,
                                                NULL) = 'N'
         )
--SELECT * FROM ACCS;
,
     ACC_CCY_EXCH_RATE           -- exchange rate applicable for account's ccy
     AS (SELECT S_CURRENCY_EXCHANGE_RATE.HOST_ID,
                S_CURRENCY_EXCHANGE_RATE.ENTITY_ID,
                S_CURRENCY_EXCHANGE_RATE.CURRENCY_CODE,
                ACCS.ACCOUNT_ID,
                S_CURRENCY_EXCHANGE_RATE.EXCHANGE_RATE_DATE,
                S_CURRENCY_EXCHANGE_RATE.EXCHANGE_RATE
           FROM PARMS
                CROSS JOIN ACCS
                INNER JOIN S_CURRENCY_EXCHANGE_RATE
                   ON (    S_CURRENCY_EXCHANGE_RATE.HOST_ID = PARMS.PHOST
                       AND S_CURRENCY_EXCHANGE_RATE.ENTITY_ID = PARMS.PENTITY
                       AND S_CURRENCY_EXCHANGE_RATE.CURRENCY_CODE = ACCS.CURRENCY_CODE
                       AND S_CURRENCY_EXCHANGE_RATE.EXCHANGE_RATE_DATE =
                              (SELECT MAX (S.EXCHANGE_RATE_DATE)
                                 FROM S_CURRENCY_EXCHANGE_RATE S
                                WHERE S.HOST_ID = PARMS.PHOST
                                  AND S.ENTITY_ID = PARMS.PENTITY
                                  AND S.CURRENCY_CODE = ACCS.CURRENCY_CODE
                                  AND S.EXCHANGE_RATE_DATE <= ACCS.PREV_BUSINESS_DATE)
                       )
         )
--SELECT * FROM ACC_CCY_EXCH_RATE;
,
     EREP_CCY_EXCH_RATE -- exchange rate applicable for entity's reporting ccy
     AS (SELECT S_CURRENCY_EXCHANGE_RATE.HOST_ID,
                S_CURRENCY_EXCHANGE_RATE.ENTITY_ID,
                S_CURRENCY_EXCHANGE_RATE.CURRENCY_CODE,
                ACCS.ACCOUNT_ID,
                S_CURRENCY_EXCHANGE_RATE.EXCHANGE_RATE_DATE,
                S_CURRENCY_EXCHANGE_RATE.EXCHANGE_RATE
           FROM PARMS
                CROSS JOIN ACCS
                INNER JOIN S_ENTITY
                   ON (    S_ENTITY.HOST_ID = PARMS.PHOST
                       AND S_ENTITY.ENTITY_ID = PARMS.PENTITY)
                INNER JOIN S_CURRENCY_EXCHANGE_RATE
                   ON (    S_CURRENCY_EXCHANGE_RATE.HOST_ID = PARMS.PHOST
                       AND S_CURRENCY_EXCHANGE_RATE.ENTITY_ID = PARMS.PENTITY
                       AND S_CURRENCY_EXCHANGE_RATE.CURRENCY_CODE = S_ENTITY.REPORT_CURRENCY
                       AND S_CURRENCY_EXCHANGE_RATE.EXCHANGE_RATE_DATE =
                              (SELECT MAX (S.EXCHANGE_RATE_DATE)
                                 FROM S_CURRENCY_EXCHANGE_RATE S
                                WHERE     S.HOST_ID = PARMS.PHOST
                                      AND S.ENTITY_ID = PARMS.PENTITY
                                      AND S.CURRENCY_CODE =
                                             S_ENTITY.REPORT_CURRENCY
                                      AND S.EXCHANGE_RATE_DATE <=
                                             ACCS.PREV_BUSINESS_DATE)
                       )
         )
--SELECT * FROM EREP_CCY_EXCH_RATE;
,
     ACCDATA
     AS (SELECT ACCS.HOST_ID,
                ACCS.ENTITY_ID,
                ACCS.ACCOUNT_ID,
                ACCS.ACCOUNT_NAME,
                ACCS.CURRENCY_CODE,
                ACCS.ACCOUNT_LEVEL,
                ACCS.PREV_BUSINESS_DATE,
                P_BALANCE.BALANCE_DATE,
                P_BALANCE.SUPPLIED_EXTERNAL_BALANCE,
                P_BALANCE.USER_NOTES,
                P_BALANCE.REASON_CODE,
                P_REASON_CODES.DESCRIPTION,
                P_ACCOUNT_INTEREST_RATE.INTEREST_RATE_DATE  ACC_INT_RATE_DATE,
                P_ACCOUNT_INTEREST_RATE.CREDIT_RATE         ACC_INT_CREDIT_RATE,
                P_ACCOUNT_INTEREST_RATE.OVERDRAFT_RATE      ACC_INT_DEBIT_RATE,
                S_CURRENCY.INTEREST_BASIS,
                S_CURRENCY_INTEREST_RATE.INTEREST_RATE      CCY_INT_RATE,
                NVL(ACC_CCY_EXCH_RATE.EXCHANGE_RATE, 1)     ACC_CCY_EX_RATE,
                NVL(EREP_CCY_EXCH_RATE.EXCHANGE_RATE, 1)    EREP_CCY_EX_RATE,

                S_ENTITY.EXCHANGE_RATE_FORMAT,
                S_ENTITY.DOMESTIC_CURRENCY,
                S_ENTITY.REPORT_CURRENCY,

                PREPORT_DATE - ACCS.PREV_BUSINESS_DATE DIFF_IN_DAYS,

                CASE
                   WHEN SIGN (S_CURRENCY_INTEREST_RATE.INTEREST_RATE) = -1
                   THEN
                      --return effective rate of margin rate when mkt rate is -ve
                      P_ACCOUNT_INTEREST_RATE.CREDIT_RATE
                   ELSE
                      -- else return sum of mkt and margin (zero if that sum is -ve)
                      CASE
                            WHEN SIGN (  P_ACCOUNT_INTEREST_RATE.CREDIT_RATE
                                       + S_CURRENCY_INTEREST_RATE.INTEREST_RATE
                                       ) = -1
                            THEN 0
                            ELSE (  P_ACCOUNT_INTEREST_RATE.CREDIT_RATE
                                  + S_CURRENCY_INTEREST_RATE.INTEREST_RATE
                                  )
                         END
                END CR_RATE,

                CASE
                   WHEN SIGN (S_CURRENCY_INTEREST_RATE.INTEREST_RATE) = -1
                   THEN
                      P_ACCOUNT_INTEREST_RATE.OVERDRAFT_RATE
                   ELSE
                      (  P_ACCOUNT_INTEREST_RATE.OVERDRAFT_RATE
                       + S_CURRENCY_INTEREST_RATE.INTEREST_RATE)
                END DB_RATE                                   -- rate for DEBIT balance
           FROM PARMS
                INNER JOIN S_ENTITY
                   ON (    S_ENTITY.HOST_ID = PARMS.PHOST
                       AND S_ENTITY.ENTITY_ID = PARMS.PENTITY)
                CROSS JOIN ACCS
                INNER JOIN S_CURRENCY
                   ON (    S_CURRENCY.HOST_ID = PARMS.PHOST
                       AND S_CURRENCY.ENTITY_ID = PARMS.PENTITY
                       AND S_CURRENCY.CURRENCY_CODE = ACCS.CURRENCY_CODE)
                INNER JOIN P_BALANCE
                   ON (    ACCS.HOST_ID = P_BALANCE.HOST_ID
                       AND ACCS.ENTITY_ID = P_BALANCE.ENTITY_ID
                       AND ACCS.ACCOUNT_ID = P_BALANCE.BAL_TYPE_ID
                       AND BALANCE_DATE =
                              (SELECT MAX (D.BALANCE_DATE) -- Latest start balance date
                                 FROM P_BALANCE D
                                WHERE D.HOST_ID = PARMS.PHOST
                                  AND D.ENTITY_ID = PARMS.PENTITY
                                  AND D.BAL_TYPE_ID = ACCS.ACCOUNT_ID
                                  AND D.SUPPLIED_EXTERNAL_BALANCE IS NOT NULL
                                  AND D.BALANCE_DATE <= (ACCS.PREV_BUSINESS_DATE + 1)
                               )
                       )
                INNER JOIN P_ACCOUNT_INTEREST_RATE -- Latest ACCOUNT interest rate
                   ON (    ACCS.HOST_ID = P_ACCOUNT_INTEREST_RATE.HOST_ID
                       AND ACCS.ENTITY_ID = P_ACCOUNT_INTEREST_RATE.ENTITY_ID
                       AND ACCS.ACCOUNT_ID = P_ACCOUNT_INTEREST_RATE.ACCOUNT_ID
                       AND P_ACCOUNT_INTEREST_RATE.INTEREST_RATE_DATE =
                              (SELECT MAX (AI.INTEREST_RATE_DATE)
                                 FROM P_ACCOUNT_INTEREST_RATE AI
                                WHERE ACCS.HOST_ID = AI.HOST_ID
                                  AND ACCS.ENTITY_ID = AI.ENTITY_ID
                                  AND ACCS.ACCOUNT_ID = AI.ACCOUNT_ID
                                  AND (AI.INTEREST_RATE_DATE) <= ACCS.PREV_BUSINESS_DATE)
                       )
                INNER JOIN S_CURRENCY_INTEREST_RATE -- Latest CURRENCY interest rate
                   ON (    S_CURRENCY_INTEREST_RATE.HOST_ID = PARMS.PHOST
                       AND S_CURRENCY_INTEREST_RATE.ENTITY_ID = PARMS.PENTITY
                       AND S_CURRENCY_INTEREST_RATE.CURRENCY_CODE = ACCS.CURRENCY_CODE
                       AND S_CURRENCY_INTEREST_RATE.INTEREST_RATE_DATE =
                              (SELECT MAX (I.INTEREST_RATE_DATE)
                                 FROM S_CURRENCY_INTEREST_RATE I
                                WHERE I.HOST_ID = PARMS.PHOST
                                  AND I.ENTITY_ID = PARMS.PENTITY
                                  AND I.CURRENCY_CODE = ACCS.CURRENCY_CODE
                                  AND I.INTEREST_RATE_DATE <=ACCS.PREV_BUSINESS_DATE)
                       )
                LEFT OUTER JOIN ACC_CCY_EXCH_RATE
                   ON (    ACC_CCY_EXCH_RATE.HOST_ID = PARMS.PHOST
                       AND ACC_CCY_EXCH_RATE.ENTITY_ID = PARMS.PENTITY
                       AND ACC_CCY_EXCH_RATE.CURRENCY_CODE = ACCS.CURRENCY_CODE
                       AND ACC_CCY_EXCH_RATE.ACCOUNT_ID = ACCS.ACCOUNT_ID)
                LEFT OUTER JOIN EREP_CCY_EXCH_RATE
                   ON (    EREP_CCY_EXCH_RATE.HOST_ID = PARMS.PHOST
                       AND EREP_CCY_EXCH_RATE.ENTITY_ID = PARMS.PENTITY
                       AND EREP_CCY_EXCH_RATE.CURRENCY_CODE = S_ENTITY.REPORT_CURRENCY
                       AND EREP_CCY_EXCH_RATE.ACCOUNT_ID = ACCS.ACCOUNT_ID)
                LEFT OUTER JOIN P_REASON_CODES
                   ON (    P_REASON_CODES.HOST_ID = PARMS.PHOST
                       AND P_REASON_CODES.ENTITY_ID = PARMS.PENTITY
                       AND P_REASON_CODES.REASON_CODE = P_BALANCE.REASON_CODE)
         )
--SELECT * FROM ACCDATA;
,
FULL_CALCS
AS(SELECT A.CURRENCY_CODE                      AS CCY,
          A.ACCOUNT_LEVEL                      AS MAIN_OR_SUB,
          A.ACCOUNT_ID                         AS ACCOUNT_ID,
          A.ACCOUNT_NAME                       AS ACCOUNT_NAME,
          NVL(A.SUPPLIED_EXTERNAL_BALANCE, 0) AS START_BALANCE,

          -- ACCOUNT RATE use cedit/credit rate according to balance sign
          -- If -ve balance and mkt rate also -ve, then use value of 0
          CASE
             WHEN SIGN (A.SUPPLIED_EXTERNAL_BALANCE) = -1
             THEN A.DB_RATE
             ELSE CASE WHEN SIGN (A.CR_RATE) = -1
                       THEN 0
                       ELSE A.CR_RATE
                  END
          END AS ACCOUNT_RATE,

          CCY_INT_RATE AS MARKET_RATE,

          -- if market rate negative and balance is credit then interest amt = 0
          CASE
             WHEN (    SIGN (A.CCY_INT_RATE) = -1
                   AND SIGN (NVL(A.SUPPLIED_EXTERNAL_BALANCE, 0)) = 1)
             THEN 0
             ELSE NVL( (  A.SUPPLIED_EXTERNAL_BALANCE -- market interest rate for currency (Credit rate)
                        * CASE
                             WHEN SIGN (A.SUPPLIED_EXTERNAL_BALANCE) = -1
                             THEN A.DB_RATE
                             ELSE A.CR_RATE
                          END
                        * 0.01 / A.INTEREST_BASIS
                        * A.DIFF_IN_DAYS)
                      , 0  )
          END AS ACC_INTEREST_AMOUNT,

          CASE
             -- if market rate negative and balance is credit then opp cost = -(mkt rate)
             WHEN (    SIGN (A.CCY_INT_RATE) = -1
                   AND SIGN (A.SUPPLIED_EXTERNAL_BALANCE) = 1)
             THEN NVL( ( -(  A.SUPPLIED_EXTERNAL_BALANCE
                           * A.CCY_INT_RATE
                           * 0.01 / A.INTEREST_BASIS)
                           * A.DIFF_IN_DAYS)
                      , 0)
             -- if market rate negative and balance is debit then opp cost = margin rate
             WHEN (    SIGN (A.CCY_INT_RATE) = -1
                   AND SIGN (A.SUPPLIED_EXTERNAL_BALANCE) = -1)
             THEN NVL( ( -(  A.SUPPLIED_EXTERNAL_BALANCE
                           * A.DB_RATE
                           * 0.01 / A.INTEREST_BASIS)
                           * A.DIFF_IN_DAYS)
                      , 0)
             ELSE NVL( ( (  A.SUPPLIED_EXTERNAL_BALANCE
                          * CCY_INT_RATE
                          * 0.01 / A.INTEREST_BASIS)

                        -(  A.SUPPLIED_EXTERNAL_BALANCE
                          * CASE SIGN (A.SUPPLIED_EXTERNAL_BALANCE)
                               WHEN -1
                               THEN A.DB_RATE
                               ELSE A.CR_RATE
                            END
                        * 0.01 / A.INTEREST_BASIS))
                      * A.DIFF_IN_DAYS
                      , 0)
          END AS  OPP_MKT_MINUS_ACC, -- Market rate interest minus Account interest

          ACC_CCY_EX_RATE AS EX_RATE, -- Exchange rate between acc ccy and domestic ccy

          -- Domestic equivalent = oppcost applied with exch rate of dom ccy
          CASE
             -- if market rate negative and balance is credit then opp cost = -(mkt rate)
             WHEN (    SIGN (A.CCY_INT_RATE) = -1
                   AND SIGN (A.SUPPLIED_EXTERNAL_BALANCE) = 1)
             THEN NVL( ( -(  A.SUPPLIED_EXTERNAL_BALANCE
                           * A.CCY_INT_RATE
                           * 0.01 / A.INTEREST_BASIS)
                           * A.DIFF_IN_DAYS)
                      , 0)
             -- if market rate negative and balance is debit then opp cost = margin rate
             WHEN (    SIGN (A.CCY_INT_RATE) = -1
                   AND SIGN (A.SUPPLIED_EXTERNAL_BALANCE) = -1)
             THEN NVL( ( -(  A.SUPPLIED_EXTERNAL_BALANCE
                           * A.DB_RATE
                           * 0.01 / A.INTEREST_BASIS)
                           * A.DIFF_IN_DAYS)
                      , 0)
             ELSE NVL( ( (  A.SUPPLIED_EXTERNAL_BALANCE
                          * CCY_INT_RATE
                          * 0.01 / A.INTEREST_BASIS)

                        -(  A.SUPPLIED_EXTERNAL_BALANCE
                          * CASE SIGN (A.SUPPLIED_EXTERNAL_BALANCE)
                               WHEN -1
                               THEN A.DB_RATE
                               ELSE A.CR_RATE
                            END
                        * 0.01 / A.INTEREST_BASIS))
                      * A.DIFF_IN_DAYS
                      , 0)
          END
          *
          CASE EXCHANGE_RATE_FORMAT
             WHEN '2'
             THEN -- FORMAT CCY/DOM
                  1 / ACC_CCY_EX_RATE
             ELSE -- FORMAT DOM/CCY
                  ACC_CCY_EX_RATE
          END
          AS DOM_EQUIV,
          DOMESTIC_CURRENCY DOM_CURR,

          -- REP_CCY_EQUIV. CALC USING OPP_MKT_MINUS_ACC x SUITABLE RATE
                      -- Domestic equiv calcn same as oppcost x exch rate
          CASE
             -- if market rate negative and balance is credit then opp cost = -(mkt rate)
             WHEN (    SIGN (A.CCY_INT_RATE) = -1
                   AND SIGN (A.SUPPLIED_EXTERNAL_BALANCE) = 1)
             THEN NVL( ( -(  A.SUPPLIED_EXTERNAL_BALANCE
                           * A.CCY_INT_RATE
                           * 0.01 / A.INTEREST_BASIS)
                           * A.DIFF_IN_DAYS)
                      , 0)
             -- if market rate negative and balance is debit then opp cost = margin rate
             WHEN (    SIGN (A.CCY_INT_RATE) = -1
                   AND SIGN (A.SUPPLIED_EXTERNAL_BALANCE) = -1)
             THEN NVL( ( -(  A.SUPPLIED_EXTERNAL_BALANCE
                           * A.DB_RATE
                           * 0.01 / A.INTEREST_BASIS)
                           * A.DIFF_IN_DAYS)
                      , 0)
             ELSE NVL( ( (  A.SUPPLIED_EXTERNAL_BALANCE
                          * CCY_INT_RATE
                          * 0.01 / A.INTEREST_BASIS)

                        -(  A.SUPPLIED_EXTERNAL_BALANCE
                          * CASE SIGN (A.SUPPLIED_EXTERNAL_BALANCE)
                               WHEN -1
                               THEN A.DB_RATE
                               ELSE A.CR_RATE
                            END
                        * 0.01 / A.INTEREST_BASIS))
                      * A.DIFF_IN_DAYS
                      , 0)
          END
          *
          CASE EXCHANGE_RATE_FORMAT
             WHEN '2'
             THEN -- FORMAT CCY/DOM
                  EREP_CCY_EX_RATE / ACC_CCY_EX_RATE
             ELSE -- FORMAT DOM/CCY
                  ACC_CCY_EX_RATE / EREP_CCY_EX_RATE
          END
          AS REP_CCY_EQUIV,                                  --change name???!!!!!!!!!!!!!!!

          REPORT_CURRENCY REP_CCY,
          EREP_CCY_EX_RATE AS REP_CCY_EX_RATE, -- Exchange rate between reporting ccy and domestic ccy

          REASON_CODE,
          DESCRIPTION REASON_DESCRIPTION,
          USER_NOTES
     FROM ACCDATA A
   )
SELECT FULL_CALCS.*
  FROM PARMS
       CROSS JOIN FULL_CALCS
WHERE (PARMS.PTHRESHOLD > 0 AND REP_CCY_EQUIV >= $P{pTHRESHOLD})
   OR (PARMS.PTHRESHOLD = 0 AND REP_CCY_EQUIV > 0)
ORDER BY CCY, REP_CCY_EQUIV DESC]]>
		</queryString>
		<field name="CCY" class="java.lang.String"/>
		<field name="MAIN_OR_SUB" class="java.lang.String"/>
		<field name="ACCOUNT_ID" class="java.lang.String"/>
		<field name="ACCOUNT_NAME" class="java.lang.String"/>
		<field name="START_BALANCE" class="java.math.BigDecimal"/>
		<field name="ACCOUNT_RATE" class="java.math.BigDecimal"/>
		<field name="MARKET_RATE" class="java.math.BigDecimal"/>
		<field name="ACC_INTEREST_AMOUNT" class="java.math.BigDecimal"/>
		<field name="OPP_MKT_MINUS_ACC" class="java.math.BigDecimal"/>
		<field name="EX_RATE" class="java.math.BigDecimal"/>
		<field name="DOM_EQUIV" class="java.math.BigDecimal"/>
		<field name="DOM_CURR" class="java.lang.String"/>
		<field name="REASON_CODE" class="java.lang.String"/>
		<field name="REASON_DESCRIPTION" class="java.lang.String"/>
		<field name="USER_NOTES" class="java.lang.String"/>
		<field name="REP_CCY_EQUIV" class="java.lang.Double"/>
		<field name="REP_CCY" class="java.lang.String"/>
	</subDataset>
	<parameter name="pHOST" class="java.lang.String">
		<defaultValueExpression><![CDATA[""]]></defaultValueExpression>
	</parameter>
	<parameter name="pENTITY" class="java.lang.String">
		<defaultValueExpression><![CDATA[""]]></defaultValueExpression>
	</parameter>
	<parameter name="pREPORT_DATE" class="java.util.Date"/>
	<parameter name="pTHRESHOLD" class="java.lang.Double"/>
	<parameter name="pCurrencyPattern" class="java.lang.String" isForPrompting="false"/>
	<parameter name="pCURRENCYCODE" class="java.lang.String" isForPrompting="false"/>
	<parameter name="pREP_CCY" class="java.lang.String">
		<defaultValueExpression><![CDATA[""]]></defaultValueExpression>
	</parameter>
	<parameter name="pOutputFormat" class="java.lang.String"/>
	<queryString>
		<![CDATA[select 1 from dual]]>
	</queryString>
	<field name="1" class="java.math.BigDecimal"/>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band height="84" splitType="Stretch">
			<staticText>
				<reportElement uuid="8ba66541-0d6a-4ee2-8cc0-659e0c4676c5" key="staticText-1" x="170" y="6" width="412" height="40" forecolor="#000000" backcolor="#FFFFFF"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center">
					<font size="24"/>
				</textElement>
				<text><![CDATA[Opportunity Cost Report]]></text>
			</staticText>
			<textField isBlankWhenNull="false">
				<reportElement uuid="2601cff8-2872-4991-bcd7-934bc90c2c7c" key="textField-2" x="60" y="56" width="100" height="23"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Top">
					<font size="12"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{pENTITY}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement uuid="605eb2e2-d8a2-45f7-b038-caa1c0956c50" key="staticText-3" x="8" y="56" width="50" height="23"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Top">
					<font size="12" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Entity:]]></text>
			</staticText>
			<staticText>
				<reportElement uuid="e0a425f5-98f2-4dd8-80d1-90be0168db0d" key="staticText-4" x="542" y="56" width="171" height="23"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Top">
					<font size="13" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Report Date:]]></text>
			</staticText>
			<textField pattern="yyyy-MM-dd" isBlankWhenNull="false">
				<reportElement uuid="cdb4b2c8-d963-4372-9cde-125e5281e063" key="textField-3" x="713" y="56" width="146" height="23"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Top">
					<font size="13"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{pREPORT_DATE}]]></textFieldExpression>
			</textField>
			<line>
				<reportElement uuid="2fee923c-fd86-4a8a-af57-d7ff831d4d62" key="line-1" x="2" y="50" width="1625" height="1"/>
			</line>
			<textField isBlankWhenNull="false">
				<reportElement uuid="58823c3e-39fa-4e6e-8d69-9a75bd0a1a43" key="textField-9" x="325" y="56" width="205" height="23"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Top">
					<font size="12"/>
				</textElement>
				<textFieldExpression><![CDATA[($P{pCurrencyPattern} != null)?(
($P{pCurrencyPattern}.equals("currencyPat1")) ?
 new DecimalFormat("#,##0.00").format($P{pTHRESHOLD}):
 new DecimalFormat("#,##0.00").format($P{pTHRESHOLD}).replaceAll("\\.", "^").replaceAll(",", ".").replaceAll("\\^", ",")):""]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement uuid="e3d90494-4673-4fb9-9823-a59dbfbac1ba" key="textField-7" x="170" y="56" width="155" height="23" forecolor="#000000" backcolor="#FFFFFF">
					<property name="local_mesure_unitx" value="pixel"/>
					<property name="local_mesure_unitwidth" value="pixel"/>
					<printWhenExpression><![CDATA[($P{pREP_CCY}!=null) ?  new Boolean(true) :new Boolean(false)]]></printWhenExpression>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement>
					<font size="12" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression><![CDATA[java.lang.String.valueOf("Threshold(").concat(($P{pREP_CCY})).concat("):")]]></textFieldExpression>
			</textField>
			<break>
				<reportElement uuid="2155feca-e8bd-4bbe-9026-5a7ecfdacea0" x="0" y="79" width="100" height="1">
					<printWhenExpression><![CDATA[$P{pOutputFormat}.equals( "C" )]]></printWhenExpression>
				</reportElement>
			</break>
		</band>
	</title>
	<detail>
		<band height="72" splitType="Stretch">
			<componentElement>
				<reportElement uuid="7f8b6802-d0cb-4811-aab7-05bddf4b2ea3" key="table 3" style="table 3" x="0" y="22" width="1156" height="50"/>
				<jr:table xmlns:jr="http://jasperreports.sourceforge.net/jasperreports/components" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports/components http://jasperreports.sourceforge.net/xsd/components.xsd">
					<datasetRun subDataset="New Dataset 1" uuid="2fd8afa2-88c2-4482-a543-c841ab059ff3">
						<datasetParameter name="pREP_CCY">
							<datasetParameterExpression><![CDATA[$P{pREP_CCY}]]></datasetParameterExpression>
						</datasetParameter>
						<datasetParameter name="pCURRENCYCODE">
							<datasetParameterExpression><![CDATA[$P{pCURRENCYCODE}]]></datasetParameterExpression>
						</datasetParameter>
						<datasetParameter name="pCurrencyPattern">
							<datasetParameterExpression><![CDATA[$P{pCurrencyPattern}]]></datasetParameterExpression>
						</datasetParameter>
						<datasetParameter name="pTHRESHOLD">
							<datasetParameterExpression><![CDATA[$P{pTHRESHOLD}]]></datasetParameterExpression>
						</datasetParameter>
						<datasetParameter name="pREPORT_DATE">
							<datasetParameterExpression><![CDATA[$P{pREPORT_DATE}]]></datasetParameterExpression>
						</datasetParameter>
						<datasetParameter name="pENTITY">
							<datasetParameterExpression><![CDATA[$P{pENTITY}]]></datasetParameterExpression>
						</datasetParameter>
						<datasetParameter name="pHOST">
							<datasetParameterExpression><![CDATA[$P{pHOST}]]></datasetParameterExpression>
						</datasetParameter>
						<connectionExpression><![CDATA[$P{REPORT_CONNECTION}]]></connectionExpression>
					</datasetRun>
					<jr:column uuid="59d70b34-297f-4119-b2f0-1f3ac0eea18e" width="90">
						<jr:columnHeader style="table 3_CH" height="30" rowSpan="1">
							<staticText>
								<reportElement uuid="7eaa440f-9ac1-4238-8eec-902baae3a9c5" key="staticText-5" positionType="Float" x="0" y="0" width="90" height="30" forecolor="#000000" backcolor="#FFFFFF">
									<printWhenExpression><![CDATA[($F{CCY}!=null) ?  new Boolean(true) :new Boolean(false)]]></printWhenExpression>
								</reportElement>
								<box>
									<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<bottomPen lineWidth="0.0" lineColor="#000000"/>
									<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								</box>
								<textElement textAlignment="Left" verticalAlignment="Top"/>
								<text><![CDATA[CCY]]></text>
							</staticText>
						</jr:columnHeader>
						<jr:detailCell style="table 3_TD" height="20" rowSpan="1">
							<textField pattern="" isBlankWhenNull="true">
								<reportElement uuid="b451e110-5014-4fc1-9c5a-d760c0260892" key="textField-7" x="0" y="0" width="90" height="20" forecolor="#000000" backcolor="#FFFFFF">
									<printWhenExpression><![CDATA[($F{CCY}!=null) ?  new Boolean(true) :new Boolean(false)]]></printWhenExpression>
								</reportElement>
								<box>
									<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<bottomPen lineWidth="0.0" lineColor="#000000"/>
									<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								</box>
								<textElement/>
								<textFieldExpression><![CDATA[$F{CCY}]]></textFieldExpression>
							</textField>
						</jr:detailCell>
					</jr:column>
					<jr:column uuid="02961715-8111-4b96-8ba5-881879f1ae57" width="150">
						<jr:columnHeader style="table 3_CH" height="30" rowSpan="1">
							<staticText>
								<reportElement uuid="7eaa440f-9ac1-4238-8eec-902baae3a9c5" key="staticText-5" positionType="Float" x="0" y="0" width="90" height="30" forecolor="#000000" backcolor="#FFFFFF">
									<printWhenExpression><![CDATA[($F{CCY}!=null) ?  new Boolean(true) :new Boolean(false)]]></printWhenExpression>
								</reportElement>
								<box>
									<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<bottomPen lineWidth="0.0" lineColor="#000000"/>
									<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								</box>
								<textElement textAlignment="Left" verticalAlignment="Top"/>
								<text><![CDATA[Account]]></text>
							</staticText>
						</jr:columnHeader>
						<jr:detailCell style="table 3_TD" height="20" rowSpan="1">
							<textField isBlankWhenNull="true">
								<reportElement uuid="********-8320-482d-bd18-064592d6a5ff" key="textField" style="TEST" x="0" y="0" width="150" height="20"/>
								<box>
									<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<bottomPen lineWidth="0.0" lineColor="#000000"/>
									<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								</box>
								<textElement/>
								<textFieldExpression><![CDATA[$F{ACCOUNT_ID}]]></textFieldExpression>
							</textField>
						</jr:detailCell>
					</jr:column>
					<jr:column uuid="4ddf75d5-7335-4c49-9e97-f8e119524dd9" width="200">
						<jr:columnHeader style="table 3_CH" height="30" rowSpan="1">
							<staticText>
								<reportElement uuid="7eaa440f-9ac1-4238-8eec-902baae3a9c5" key="staticText-5" positionType="Float" x="0" y="0" width="90" height="30" forecolor="#000000" backcolor="#FFFFFF">
									<printWhenExpression><![CDATA[($F{CCY}!=null) ?  new Boolean(true) :new Boolean(false)]]></printWhenExpression>
								</reportElement>
								<box>
									<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<bottomPen lineWidth="0.0" lineColor="#000000"/>
									<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								</box>
								<textElement textAlignment="Left" verticalAlignment="Top"/>
								<text><![CDATA[Account Name]]></text>
							</staticText>
						</jr:columnHeader>
						<jr:detailCell style="table 3_TD" height="20" rowSpan="1">
							<textField isBlankWhenNull="true">
								<reportElement uuid="5a338356-4783-49ea-8f98-485dee22fa26" key="textField" x="0" y="0" width="200" height="20">
									<property name="local_mesure_unity" value="pixel"/>
								</reportElement>
								<box>
									<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<bottomPen lineWidth="0.0" lineColor="#000000"/>
									<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								</box>
								<textElement/>
								<textFieldExpression><![CDATA[$F{ACCOUNT_NAME}]]></textFieldExpression>
							</textField>
						</jr:detailCell>
					</jr:column>
					<jr:column uuid="5c63c7d0-e943-4a01-8ff7-8178da0ff5d6" width="90">
						<jr:columnHeader style="table 3_CH" height="30" rowSpan="1">
							<staticText>
								<reportElement uuid="7eaa440f-9ac1-4238-8eec-902baae3a9c5" key="staticText-5" positionType="Float" x="0" y="0" width="90" height="30" forecolor="#000000" backcolor="#FFFFFF">
									<printWhenExpression><![CDATA[($F{CCY}!=null) ?  new Boolean(true) :new Boolean(false)]]></printWhenExpression>
								</reportElement>
								<box>
									<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<bottomPen lineWidth="0.0" lineColor="#000000"/>
									<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								</box>
								<textElement textAlignment="Left" verticalAlignment="Top"/>
								<text><![CDATA[SOD Balance]]></text>
							</staticText>
						</jr:columnHeader>
						<jr:detailCell style="table 3_TD" height="20" rowSpan="1">
							<textField pattern="" isBlankWhenNull="true">
								<reportElement uuid="cab8a588-10a7-4464-9141-7674456e8bb8" key="textField" x="0" y="0" width="90" height="20"/>
								<box>
									<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<bottomPen lineWidth="0.0" lineColor="#000000"/>
									<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								</box>
								<textElement textAlignment="Right"/>
								<textFieldExpression><![CDATA[($P{pCurrencyPattern} != null)?(
($P{pCurrencyPattern}.equals("currencyPat1")) ?
 new DecimalFormat("#,##0.00").format($F{START_BALANCE}):
 new DecimalFormat("#,##0.00").format($F{START_BALANCE}).replaceAll("\\.", "^").replaceAll(",", ".").replaceAll("\\^", ",")):""]]></textFieldExpression>
							</textField>
						</jr:detailCell>
					</jr:column>
					<jr:column uuid="177e452f-6dfc-4829-8ad9-c743a5b33667" width="90">
						<jr:columnHeader style="table 3_CH" height="30" rowSpan="1">
							<staticText>
								<reportElement uuid="7eaa440f-9ac1-4238-8eec-902baae3a9c5" key="staticText-5" positionType="Float" x="0" y="0" width="90" height="30" forecolor="#000000" backcolor="#FFFFFF">
									<printWhenExpression><![CDATA[($F{CCY}!=null) ?  new Boolean(true) :new Boolean(false)]]></printWhenExpression>
								</reportElement>
								<box>
									<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<bottomPen lineWidth="0.0" lineColor="#000000"/>
									<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								</box>
								<textElement textAlignment="Left" verticalAlignment="Top"/>
								<text><![CDATA[Interest Rate%]]></text>
							</staticText>
						</jr:columnHeader>
						<jr:detailCell style="table 3_TD" height="20" rowSpan="1">
							<textField pattern="###0.0000" isBlankWhenNull="true">
								<reportElement uuid="7f8afa68-c8ee-493f-81fc-7d150faea363" key="textField" x="0" y="0" width="90" height="20"/>
								<box>
									<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<bottomPen lineWidth="0.0" lineColor="#000000"/>
									<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								</box>
								<textElement textAlignment="Left"/>
								<textFieldExpression><![CDATA[($P{pCurrencyPattern} != null)?(
($P{pCurrencyPattern}.equals("currencyPat1")) ?
 new DecimalFormat("#,##0.00").format($F{ACCOUNT_RATE}):
 new DecimalFormat("#,##0.00").format($F{ACCOUNT_RATE}).replaceAll("\\.", "^").replaceAll(",", ".").replaceAll("\\^", ",")):""]]></textFieldExpression>
							</textField>
						</jr:detailCell>
					</jr:column>
					<jr:column uuid="20ea8655-30ab-4ea2-9b80-8bce9b2e4dc2" width="90">
						<jr:columnHeader style="table 3_CH" height="30" rowSpan="1">
							<staticText>
								<reportElement uuid="7eaa440f-9ac1-4238-8eec-902baae3a9c5" key="staticText-5" positionType="Float" x="0" y="0" width="90" height="30" forecolor="#000000" backcolor="#FFFFFF">
									<printWhenExpression><![CDATA[($F{CCY}!=null) ?  new Boolean(true) :new Boolean(false)]]></printWhenExpression>
								</reportElement>
								<box>
									<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<bottomPen lineWidth="0.0" lineColor="#000000"/>
									<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								</box>
								<textElement textAlignment="Left" verticalAlignment="Top"/>
								<text><![CDATA[Market Rate %]]></text>
							</staticText>
						</jr:columnHeader>
						<jr:detailCell style="table 3_TD" height="20" rowSpan="1">
							<textField pattern="###0.0000" isBlankWhenNull="true">
								<reportElement uuid="3d17e11f-8826-4b61-a625-63a83cc5b2a2" key="textField" x="0" y="0" width="90" height="20"/>
								<box>
									<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<bottomPen lineWidth="0.0" lineColor="#000000"/>
									<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								</box>
								<textElement textAlignment="Right"/>
								<textFieldExpression><![CDATA[($P{pCurrencyPattern} != null)?(
($P{pCurrencyPattern}.equals("currencyPat1")) ?
 new DecimalFormat("#,##0.00").format($F{MARKET_RATE}):
 new DecimalFormat("#,##0.00").format($F{MARKET_RATE}).replaceAll("\\.", "^").replaceAll(",", ".").replaceAll("\\^", ",")):""]]></textFieldExpression>
							</textField>
						</jr:detailCell>
					</jr:column>
					<jr:column uuid="f1d98666-4840-4ead-8748-3934df7a934f" width="90">
						<jr:columnHeader style="table 3_CH" height="30" rowSpan="1">
							<staticText>
								<reportElement uuid="7eaa440f-9ac1-4238-8eec-902baae3a9c5" key="staticText-5" positionType="Float" x="0" y="0" width="90" height="30" forecolor="#000000" backcolor="#FFFFFF">
									<printWhenExpression><![CDATA[($F{CCY}!=null) ?  new Boolean(true) :new Boolean(false)]]></printWhenExpression>
								</reportElement>
								<box>
									<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<bottomPen lineWidth="0.0" lineColor="#000000"/>
									<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								</box>
								<textElement textAlignment="Left" verticalAlignment="Top"/>
								<text><![CDATA[Interest Amount]]></text>
							</staticText>
						</jr:columnHeader>
						<jr:detailCell style="table 3_TD" height="20" rowSpan="1">
							<textField pattern="###0.00" isBlankWhenNull="true">
								<reportElement uuid="5a534b0f-e691-4175-a64b-4d4f0eabe11a" key="textField" x="0" y="0" width="90" height="20"/>
								<box>
									<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<bottomPen lineWidth="0.0" lineColor="#000000"/>
									<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								</box>
								<textElement textAlignment="Right"/>
								<textFieldExpression><![CDATA[($P{pCurrencyPattern} != null)?(
($P{pCurrencyPattern}.equals("currencyPat1")) ?
 new DecimalFormat("#,##0.00").format($F{ACC_INTEREST_AMOUNT}):
 new DecimalFormat("#,##0.00").format($F{ACC_INTEREST_AMOUNT}).replaceAll("\\.", "^").replaceAll(",", ".").replaceAll("\\^", ",")):""]]></textFieldExpression>
							</textField>
						</jr:detailCell>
					</jr:column>
					<jr:column uuid="81f5151d-ec15-4da6-acff-a60b4051dde0" width="90">
						<jr:columnHeader style="table 3_CH" height="30" rowSpan="1">
							<staticText>
								<reportElement uuid="7eaa440f-9ac1-4238-8eec-902baae3a9c5" key="staticText-5" positionType="Float" x="0" y="0" width="90" height="30" forecolor="#000000" backcolor="#FFFFFF">
									<printWhenExpression><![CDATA[($F{CCY}!=null) ?  new Boolean(true) :new Boolean(false)]]></printWhenExpression>
								</reportElement>
								<box>
									<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<bottomPen lineWidth="0.0" lineColor="#000000"/>
									<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								</box>
								<textElement textAlignment="Left" verticalAlignment="Top"/>
								<text><![CDATA[Opportunity]]></text>
							</staticText>
						</jr:columnHeader>
						<jr:detailCell style="table 3_TD" height="20" rowSpan="1">
							<textField pattern="###0.00" isBlankWhenNull="true">
								<reportElement uuid="779865cc-aba2-4ca1-b8fa-f62ac8223f3e" key="textField" x="0" y="0" width="90" height="20"/>
								<box>
									<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<bottomPen lineWidth="0.0" lineColor="#000000"/>
									<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								</box>
								<textElement textAlignment="Right"/>
								<textFieldExpression><![CDATA[($P{pCurrencyPattern} != null)?(
($P{pCurrencyPattern}.equals("currencyPat1")) ?
 new DecimalFormat("#,##0.00").format($F{OPP_MKT_MINUS_ACC}):
 new DecimalFormat("#,##0.00").format($F{OPP_MKT_MINUS_ACC}).replaceAll("\\.", "^").replaceAll(",", ".").replaceAll("\\^", ",")):""]]></textFieldExpression>
							</textField>
						</jr:detailCell>
					</jr:column>
					<jr:column uuid="7d6af23b-104d-466b-8fa8-9f4a0343f33d" width="90">
						<jr:columnHeader style="table 3_CH" height="30" rowSpan="1">
							<staticText>
								<reportElement uuid="7eaa440f-9ac1-4238-8eec-902baae3a9c5" key="staticText-5" positionType="Float" x="0" y="0" width="90" height="30" forecolor="#000000" backcolor="#FFFFFF">
									<printWhenExpression><![CDATA[($F{CCY}!=null) ?  new Boolean(true) :new Boolean(false)]]></printWhenExpression>
								</reportElement>
								<box>
									<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<bottomPen lineWidth="0.0" lineColor="#000000"/>
									<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								</box>
								<textElement textAlignment="Left" verticalAlignment="Top"/>
								<text><![CDATA[Exchange Rate]]></text>
							</staticText>
						</jr:columnHeader>
						<jr:detailCell style="table 3_TD" height="20" rowSpan="1">
							<textField pattern="#,##0.0000" isBlankWhenNull="true">
								<reportElement uuid="da82b4f9-54d9-4846-88ce-a9c18ea8efae" key="textField" x="0" y="0" width="90" height="20"/>
								<box>
									<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<bottomPen lineWidth="0.0" lineColor="#000000"/>
									<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								</box>
								<textElement textAlignment="Right"/>
								<textFieldExpression><![CDATA[($P{pCurrencyPattern} != null)?(
($P{pCurrencyPattern}.equals("currencyPat1")) ?
 new DecimalFormat("#,##0.000").format(new java.math.BigDecimal($F{EX_RATE}.toString()).divide(new java.math.BigDecimal("1"),java.math.RoundingMode.CEILING)):
 new DecimalFormat("#,##0.000").format(new java.math.BigDecimal($F{EX_RATE}.toString()).divide(new java.math.BigDecimal("1"),java.math.RoundingMode.CEILING)).replaceAll("\\.", "^").replaceAll(",", ".").replaceAll("\\^", ",")):""]]></textFieldExpression>
							</textField>
						</jr:detailCell>
					</jr:column>
					<jr:column uuid="7bdc181e-c8f0-4860-ba1c-41e20b0278e0" width="120">
						<jr:columnHeader style="table 3_CH" height="30" rowSpan="1">
							<staticText>
								<reportElement uuid="7eaa440f-9ac1-4238-8eec-902baae3a9c5" key="staticText-5" positionType="Float" x="0" y="0" width="90" height="30" forecolor="#000000" backcolor="#FFFFFF">
									<printWhenExpression><![CDATA[($F{CCY}!=null) ?  new Boolean(true) :new Boolean(false)]]></printWhenExpression>
								</reportElement>
								<box>
									<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<bottomPen lineWidth="0.0" lineColor="#000000"/>
									<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								</box>
								<textElement textAlignment="Left" verticalAlignment="Top"/>
								<text><![CDATA[Equiv Opportunity]]></text>
							</staticText>
						</jr:columnHeader>
						<jr:detailCell style="table 3_TD" height="20" rowSpan="1">
							<textField pattern="" isBlankWhenNull="true">
								<reportElement uuid="4d1bf566-2844-4a2e-9bde-df124a309f1a" key="textField" x="0" y="0" width="120" height="20"/>
								<box>
									<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<bottomPen lineWidth="0.0" lineColor="#000000"/>
									<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								</box>
								<textElement textAlignment="Right"/>
								<textFieldExpression><![CDATA[($P{pCurrencyPattern} != null)?(
($P{pCurrencyPattern}.equals("currencyPat1")) ?
 new DecimalFormat("#,##0.00").format($F{DOM_EQUIV}):
 new DecimalFormat("#,##0.00").format($F{DOM_EQUIV}).replaceAll("\\.", "^").replaceAll(",", ".").replaceAll("\\^", ","))+$F{DOM_CURR}:""]]></textFieldExpression>
							</textField>
						</jr:detailCell>
					</jr:column>
					<jr:column uuid="1245018d-0b2f-4ed0-bd55-b506f270f1fa" width="171">
						<jr:columnHeader style="table 3_CH" height="30" rowSpan="1">
							<staticText>
								<reportElement uuid="7eaa440f-9ac1-4238-8eec-902baae3a9c5" key="staticText-5" positionType="Float" x="0" y="0" width="90" height="30" forecolor="#000000" backcolor="#FFFFFF">
									<printWhenExpression><![CDATA[($F{CCY}!=null) ?  new Boolean(true) :new Boolean(false)]]></printWhenExpression>
								</reportElement>
								<box>
									<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<bottomPen lineWidth="0.0" lineColor="#000000"/>
									<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								</box>
								<textElement textAlignment="Left" verticalAlignment="Top"/>
								<text><![CDATA[Reason]]></text>
							</staticText>
						</jr:columnHeader>
						<jr:detailCell style="table 3_TD" height="20" rowSpan="1">
							<textField isBlankWhenNull="true">
								<reportElement uuid="2cf5484f-e285-4d9b-b58f-0382489b7d4a" key="textField-8" x="0" y="0" width="171" height="20" forecolor="#00009C">
									<property name="local_mesure_unity" value="pixel"/>
									<property name="local_mesure_unitx" value="pixel"/>
									<property name="local_mesure_unitwidth" value="pixel"/>
								</reportElement>
								<textElement textAlignment="Left">
									<font isBold="true" pdfFontName="Helvetica-Bold"/>
								</textElement>
								<textFieldExpression><![CDATA[$F{REASON_DESCRIPTION}]]></textFieldExpression>
							</textField>
						</jr:detailCell>
					</jr:column>
					<jr:column uuid="a3024483-4399-49c7-bf93-c12332f75bec" width="356">
						<jr:columnHeader style="table 3_CH" height="30" rowSpan="1">
							<staticText>
								<reportElement uuid="7eaa440f-9ac1-4238-8eec-902baae3a9c5" key="staticText-5" positionType="Float" x="0" y="0" width="90" height="30" forecolor="#000000" backcolor="#FFFFFF">
									<printWhenExpression><![CDATA[($F{CCY}!=null) ?  new Boolean(true) :new Boolean(false)]]></printWhenExpression>
								</reportElement>
								<box>
									<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<bottomPen lineWidth="0.0" lineColor="#000000"/>
									<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								</box>
								<textElement textAlignment="Left" verticalAlignment="Top"/>
								<text><![CDATA[User Note]]></text>
							</staticText>
						</jr:columnHeader>
						<jr:detailCell style="table 3_TD" height="20" rowSpan="1">
							<textField isBlankWhenNull="true">
								<reportElement uuid="6b195cdf-27a4-42b5-a434-f020c9c1aa90" key="textField" mode="Transparent" x="0" y="0" width="356" height="20" isPrintInFirstWholeBand="true" forecolor="#00009C">
									<property name="local_mesure_unity" value="pixel"/>
									<property name="local_mesure_unitx" value="pixel"/>
									<property name="net.sf.jasperreports.print.keep.full.text" value="true"/>
								</reportElement>
								<textElement>
									<font isBold="true" pdfFontName="Helvetica-Bold"/>
								</textElement>
								<textFieldExpression><![CDATA[($F{USER_NOTES}!= null)? $F{USER_NOTES}:""]]></textFieldExpression>
							</textField>
						</jr:detailCell>
					</jr:column>
				</jr:table>
			</componentElement>
		</band>
	</detail>
	<columnFooter>
		<band splitType="Stretch"/>
	</columnFooter>
	<summary>
		<band height="113" splitType="Stretch"/>
	</summary>
</jasperReport>
