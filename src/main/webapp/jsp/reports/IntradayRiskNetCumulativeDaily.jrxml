<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 4.0.2-->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="IntraliquidityRiskReportDaily" pageWidth="595" pageHeight="842" columnWidth="535" leftMargin="30" rightMargin="30" topMargin="20" bottomMargin="20" isFloatColumnFooter="true">
	<property name="ireport.scriptlethandling" value="0"/>
	<property name="ireport.encoding" value="UTF-8"/>
	<property name="ireport.zoom" value="1.0"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="61"/>
	<import value="net.sf.jasperreports.engine.*"/>
	<import value="org.swallow.util.*"/>
	<import value="java.util.*"/>
	<import value="net.sf.jasperreports.engine.data.*"/>
	<parameter name="pHost_Id" class="java.lang.String"/>
	<parameter name="pEntity_Id" class="java.lang.String"/>
	<parameter name="pCurrency_Code" class="java.lang.String"/>
	<parameter name="pILMGroup_Id" class="java.lang.String"/>
	<parameter name="pDBLink" class="java.lang.String"/>
	<parameter name="pRoleId" class="java.lang.String"/>
	<parameter name="pDateFormat" class="java.lang.String"/>
	<parameter name="pCurrencyPattern" class="java.lang.String"/>
	<parameter name="pValue_Date" class="java.util.Date"/>
	<parameter name="pValue_DateEnd" class="java.util.Date"/>
	<parameter name="sdf" class="java.text.SimpleDateFormat"/>
	<parameter name="pSeriesIdentifier" class="java.lang.String"/>
	<parameter name="pILMReportType" class="java.lang.String"/>
	<parameter name="pInflow_Outflow_Sum" class="java.lang.String"/>
	<parameter name="pCurrencyName" class="java.lang.String"/>
	<parameter name="pEntityName" class="java.lang.String"/>
	<parameter name="pScenarioName" class="java.lang.String"/>
	<parameter name="pUseCcyMultiplier" class="java.lang.String"/>
	<parameter name="pDictionary_Data" class="java.util.HashMap"/>
	<parameter name="pIlmUtil" class="org.swallow.reports.dao.hibernate.ReportsDAOHibernate.ILMReportUtils"/>
	<queryString>
		<![CDATA[WITH headers
     AS (SELECT HOST_ID,
                ENTITY_ID,
                ENTITY_NAME,
                CCY,
                CCY_NAME,
                ILM_SCENARIO_ID,
                ILM_SCENARIO_NAME,
                DECODE ( $P{pUseCcyMultiplier}, 'Y', CCY_MULTIPLIER, 'N')
                   CCY_MULTIPLIER,
                DECODE ( $P{pUseCcyMultiplier}, 'Y', CCY_MULTIPLIER_VALUE, 1)
                   CCY_MULTIPLIER_VALUE,
                MISSING_D_START,
                MISSING_D_END,
                COLLECT_NET_CUM_POS,
				$P{pSeriesIdentifier} pSeriesIdentifier,
				$P{pDBLink} pDBLink,
				$P{pRoleId} pRoleId,
				$P{pValue_Date} pValue_Date,
				$P{pDateFormat} pDateFormat
           FROM TABLE (pkg_ilm_rep.fn_header ( $P{pHost_Id},
                            $P{pEntity_Id},
                            $P{pCurrency_Code},
                            $P{pILMGroup_Id},
                            $P{pSeriesIdentifier},
                            $P{pRoleId},
                            $P{pDBLink},
                            $P{pValue_Date},
                            $P{pValue_Date}))),
                                         
 DATA AS (SELECT headers.ENTITY_ID,
       ENTITY_NAME,
       headers.CCY,
       CCY_NAME,
       headers.ILM_SCENARIO_ID,
       ILM_SCENARIO_NAME,
       data.main_agent MAIN_AGENT,
       TO_CHAR (data.MAX1_POS_NET_CUM_D,'HH24:MI') AS MAX1_POS_NET_CUM_D,
       data.MAX1_POS_NET_CUM_V / CCY_MULTIPLIER_VALUE AS MAX1_POS_NET_CUM_V, 
       TO_CHAR (data.MAX1_NEG_NET_CUM_D, 'HH24:MI') AS MAX1_NEG_NET_CUM_D,
       data.MAX1_NEG_NET_CUM_V / CCY_MULTIPLIER_VALUE AS MAX1_NEG_NET_CUM_V,
       CCY_MULTIPLIER,
       CCY_MULTIPLIER_VALUE,
       rownum current_row,
       DECODE (MAIN_AGENT, NULL, 'NO_DATA', 'OK') DATA_STATE,
       MIN(headers.missing_d_start) OVER () AS MISSING_D_START, 
       MAX(headers.missing_d_end) OVER() AS MISSING_D_END, 
	   COUNT(*) OVER (PARTITION BY DECODE (MAIN_AGENT, NULL, 'NO_DATA', 'OK')) GL_DATA_STATE,
       COUNT(*) OVER () GL_DATA_ALL
  FROM headers LEFT JOIN TABLE (pkg_ilm_rep.fn_rank_net_cum_pos (
                                               headers.host_id,
                                               headers.entity_id,
                                               headers.ccy,
                                               NULL,
                                               pSeriesIdentifier,
                                               pDBLink,
                                               pValue_Date,
                                               pValue_Date, 
                                               pRoleId, 
                                               'Y')) data ON (1=1)) 
    SELECT * FROM DATA
-- Keep only data where MAIN_AGENT is not null OR keep the last row if ALL MAIN_AGENTs are NULL (Refering to GL_DATA_STATE and GL_DATA_ALL)                                               
WHERE MAIN_AGENT IS NOT NULL 
  OR (GL_DATA_STATE=GL_DATA_ALL AND ROWNUM<2)
 ORDER BY CCY,MAIN_AGENT]]>
	</queryString>
	<field name="ENTITY_ID" class="java.lang.String"/>
	<field name="ENTITY_NAME" class="java.lang.String"/>
	<field name="CCY" class="java.lang.String"/>
	<field name="CCY_NAME" class="java.lang.String"/>
	<field name="ILM_SCENARIO_ID" class="java.lang.String"/>
	<field name="ILM_SCENARIO_NAME" class="java.lang.String"/>
	<field name="CCY_MULTIPLIER" class="java.lang.String"/>
	<field name="MAX1_POS_NET_CUM_D" class="java.lang.String"/>
	<field name="MAX1_POS_NET_CUM_V" class="java.lang.Double"/>
	<field name="MAX1_NEG_NET_CUM_D" class="java.lang.String"/>
	<field name="MAX1_NEG_NET_CUM_V" class="java.lang.Double"/>
	<field name="CURRENT_ROW" class="java.lang.Integer"/>
	<field name="MISSING_D_START" class="java.util.Date"/>
	<field name="MISSING_D_END" class="java.util.Date"/>
	<field name="MAIN_AGENT" class="java.lang.String"/>
	<field name="DATA_STATE" class="java.lang.String"/>
	<group name="NetCumulativePosition" isReprintHeaderOnEachPage="true" keepTogether="true">
		<groupExpression><![CDATA[$F{CCY}+""+$F{MAIN_AGENT}]]></groupExpression>
		<groupHeader>
			<band height="60" splitType="Stretch">
				<printWhenExpression><![CDATA[!$F{DATA_STATE}.equals("NO_DATA")]]></printWhenExpression>
				<textField pattern="" isBlankWhenNull="false">
					<reportElement key="textField" mode="Opaque" x="0" y="2" width="534" height="20" forecolor="#000000" backcolor="#E6E6FA">
						<property name="local_mesure_unity" value="pixel"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="11" isBold="true" pdfFontName="Helvetica-Bold"/>
						<paragraph leftIndent="7"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[$F{CCY}+": "+$F{MAIN_AGENT}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement key="textField" positionType="Float" x="7" y="24" width="219" height="16" forecolor="#000000" backcolor="#FFFFFF">
						<property name="local_mesure_unitheight" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement>
						<font fontName="SansSerif" size="10" isBold="false"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[$P{pDictionary_Data}.get("pLARGEST_POSITIVE_POISTION_LABEL")]]></textFieldExpression>
				</textField>
				<textField pattern="">
					<reportElement key="textField-1" x="250" y="24" width="110" height="16">
						<property name="local_mesure_unitheight" value="pixel"/>
						<property name="local_mesure_unitx" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="local_mesure_unitwidth" value="pixel"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="10" isItalic="true" pdfFontName="Helvetica-Oblique"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[($F{MAX1_POS_NET_CUM_D}!=null)?$F{MAX1_POS_NET_CUM_D}:"-"]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement key="textField-1" x="390" y="24" width="132" height="16">
						<property name="local_mesure_unitheight" value="pixel"/>
						<property name="local_mesure_unitx" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle"/>
					<textFieldExpression class="java.lang.String"><![CDATA[($F{MAX1_POS_NET_CUM_V}!=null)?($P{pIlmUtil}.formatCurrency($F{MAX1_POS_NET_CUM_V},$P{pCurrencyPattern})):"-"]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement key="textField" positionType="Float" x="7" y="41" width="218" height="16" forecolor="#000000" backcolor="#FFFFFF">
						<property name="local_mesure_unitheight" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement>
						<font fontName="SansSerif" size="10" isBold="false"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[$P{pDictionary_Data}.get("pLARGEST_NEGATIVE_POISTION_LABEL")]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement key="textField-1" x="250" y="41" width="110" height="16">
						<property name="local_mesure_unitheight" value="pixel"/>
						<property name="local_mesure_unitx" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="local_mesure_unitwidth" value="pixel"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="10" isItalic="true" pdfFontName="Helvetica-Oblique"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[($F{MAX1_NEG_NET_CUM_D}!=null)?$F{MAX1_NEG_NET_CUM_D}:"-"]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement key="textField-1" x="390" y="41" width="132" height="16">
						<property name="local_mesure_unitx" value="pixel"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle"/>
					<textFieldExpression class="java.lang.String"><![CDATA[($F{MAX1_NEG_NET_CUM_V}!=null)?($P{pIlmUtil}.formatCurrency($F{MAX1_NEG_NET_CUM_V},$P{pCurrencyPattern})):"-"]]></textFieldExpression>
				</textField>
			</band>
		</groupHeader>
	</group>
	<background>
		<band splitType="Stretch"/>
	</background>
	<pageHeader>
		<band height="150" splitType="Stretch">
			<textField pattern="" isBlankWhenNull="false">
				<reportElement key="textField" mode="Opaque" x="0" y="0" width="535" height="30" forecolor="#000000" backcolor="#E6E6FA"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="12" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{pDictionary_Data}.get("pTitleIntraDayRisk")]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement key="textField-1" x="270" y="30" width="136" height="20">
					<property name="local_mesure_unitx" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="10" isBold="true" pdfFontName="Helvetica-Bold"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{pDictionary_Data}.get("pREPORT_DATE_LABEL")]]></textFieldExpression>
			</textField>
			<textField evaluationTime="Report" pattern="dd-MMM-yyyy HH:mm:ss" isBlankWhenNull="false">
				<reportElement key="textField-1" x="390" y="30" width="144" height="20">
					<property name="local_mesure_unitx" value="pixel"/>
					<property name="local_mesure_unitwidth" value="pixel"/>
				</reportElement>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="10"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[new SimpleDateFormat($P{pDateFormat}+" HH:mm:ss").format(new Date())]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement key="textField" positionType="Float" x="10" y="50" width="140" height="16" forecolor="#000000" backcolor="#FFFFFF">
					<property name="local_mesure_unity" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font size="10" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{pDictionary_Data}.get("pENTITY_LABEL")]]></textFieldExpression>
			</textField>
			<textField pattern="###" isBlankWhenNull="true">
				<reportElement key="textField" x="150" y="50" width="159" height="16">
					<property name="local_mesure_unity" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle"/>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{pEntity_Id}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement key="textField-1" x="359" y="50" width="195" height="16">
					<property name="local_mesure_unity" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle"/>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{pEntityName}.equals("All")?"":$P{pEntityName}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement key="textField" positionType="Float" x="10" y="66" width="157" height="16" forecolor="#000000" backcolor="#FFFFFF">
					<property name="local_mesure_unity" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font size="10" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{pDictionary_Data}.get("pCURRENCY_LABEL")]]></textFieldExpression>
			</textField>
			<textField pattern="###" isBlankWhenNull="true">
				<reportElement key="textField" x="150" y="66" width="159" height="16">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="local_mesure_unity" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle"/>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{pCurrency_Code}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement key="textField-1" x="358" y="66" width="195" height="16">
					<property name="local_mesure_unitx" value="pixel"/>
					<property name="local_mesure_unitheight" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="local_mesure_unity" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle"/>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{pCurrencyName}.equals("All")?"":$P{pCurrencyName}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement key="textField" positionType="Float" x="10" y="82" width="157" height="16" forecolor="#000000" backcolor="#FFFFFF">
					<property name="local_mesure_unity" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font size="10" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{pDictionary_Data}.get("pScenario_LABEL")]]></textFieldExpression>
			</textField>
			<textField pattern="###" isBlankWhenNull="true">
				<reportElement key="textField" x="150" y="82" width="200" height="16">
					<property name="local_mesure_unity" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle"/>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{pSeriesIdentifier}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement key="textField-1" x="359" y="82" width="194" height="16">
					<property name="local_mesure_unity" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle"/>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{pScenarioName}.equals("Standard")?"":$P{pScenarioName}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement key="textField" positionType="Float" x="10" y="98" width="157" height="16" forecolor="#000000" backcolor="#FFFFFF">
					<property name="local_mesure_unity" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font size="10" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{pDictionary_Data}.get("pGROUP_LABEL")]]></textFieldExpression>
			</textField>
			<textField pattern="###" isBlankWhenNull="true">
				<reportElement key="textField" x="150" y="98" width="180" height="16">
					<property name="local_mesure_unity" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle"/>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{pDictionary_Data}.get("pCorrespondentBankGroups")]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement key="textField" positionType="Float" x="10" y="114" width="170" height="16" forecolor="#000000" backcolor="#FFFFFF">
					<property name="local_mesure_unitheight" value="pixel"/>
					<property name="local_mesure_unity" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="local_mesure_unitwidth" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font size="10" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{pDictionary_Data}.get("pREPORTING_PERIOD_LABEL")]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement key="textField-1" x="150" y="114" width="80" height="16">
					<property name="local_mesure_unitx" value="pixel"/>
					<property name="local_mesure_unity" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle"/>
				<textFieldExpression class="java.lang.String"><![CDATA[new SimpleDateFormat($P{pDateFormat}).format($P{pValue_Date})]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement key="textField" positionType="Float" x="10" y="130" width="170" height="16" forecolor="#000000" backcolor="#FFFFFF">
					<property name="local_mesure_unitheight" value="pixel"/>
					<property name="local_mesure_unity" value="pixel"/>
					<property name="local_mesure_unitx" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font size="10" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{pDictionary_Data}.get("pCCY_MULTIPLIER_LABEL")]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement key="textField-1" x="150" y="130" width="80" height="16">
					<property name="local_mesure_unitwidth" value="pixel"/>
					<property name="local_mesure_unitx" value="pixel"/>
					<property name="local_mesure_unity" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle"/>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{pDictionary_Data}.get("pCCY_MULTIPLIER_"+$F{CCY_MULTIPLIER})]]></textFieldExpression>
			</textField>
		</band>
	</pageHeader>
	<pageFooter>
		<band height="29" splitType="Stretch">
			<line>
				<reportElement key="line" x="0" y="3" width="540" height="1">
					<property name="local_mesure_unitwidth" value="pixel"/>
				</reportElement>
			</line>
			<textField pattern="" isBlankWhenNull="false">
				<reportElement key="textField" x="320" y="3" width="170" height="19" forecolor="#000000" backcolor="#FFFFFF">
					<property name="local_mesure_unitx" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right">
					<font size="10"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA["Page " + $V{PAGE_NUMBER} + " " +$P{pDictionary_Data}.get("pOF_LABEL")]]></textFieldExpression>
			</textField>
			<textField evaluationTime="Report" pattern="" isBlankWhenNull="false">
				<reportElement key="textField" x="490" y="3" width="36" height="19" forecolor="#000000" backcolor="#FFFFFF">
					<property name="local_mesure_unitx" value="pixel"/>
					<property name="local_mesure_unity" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement>
					<font size="10"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA["" + $V{PAGE_NUMBER}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField" x="10" y="8" width="209" height="19" forecolor="#000000" backcolor="#FFFFFF">
					<property name="local_mesure_unity" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font size="10"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[new SimpleDateFormat($P{pDateFormat}).format(new Date())]]></textFieldExpression>
			</textField>
		</band>
	</pageFooter>
	<summary>
		<band height="36">
			<textField>
				<reportElement key="textField-3" x="120" y="4" width="240" height="32">
					<property name="local_mesure_unitx" value="pixel"/>
					<property name="local_mesure_unity" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center">
					<font size="12"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[($F{DATA_STATE}.equals("NO_DATA")) ? ($P{pDictionary_Data}.get("pNO_DATA_FOUND")+ "\r\n" +$P{pDictionary_Data}.get("pEND_OF_REPORT_LABEL")) : ($P{pDictionary_Data}.get("pEND_OF_REPORT_LABEL"))]]></textFieldExpression>
			</textField>
		</band>
	</summary>
</jasperReport>
