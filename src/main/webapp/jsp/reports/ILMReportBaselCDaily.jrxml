<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.12.2.final using JasperReports Library version 6.12.2-75c5e90a222ab406e416cbf590a5397028a52de3  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="ILMReportBaselCDaily" pageWidth="595" pageHeight="842" columnWidth="535" leftMargin="30" rightMargin="30" topMargin="20" bottomMargin="20" uuid="b36b73d5-f2fc-46da-974b-4f0d2c97a4c7">
	<property name="ireport.scriptlethandling" value="0"/>
	<property name="ireport.encoding" value="UTF-8"/>
	<property name="ireport.zoom" value="1.0"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<import value="net.sf.jasperreports.engine.*"/>
	<import value="java.util.*"/>
	<import value="net.sf.jasperreports.engine.data.*"/>
	<parameter name="pHost_Id" class="java.lang.String"/>
	<parameter name="pEntity_Id" class="java.lang.String"/>
	<parameter name="pCurrency_Code" class="java.lang.String"/>
	<parameter name="pDBLink" class="java.lang.String"/>
	<parameter name="pRoleId" class="java.lang.String"/>
	<parameter name="pDateFormat" class="java.lang.String"/>
	<parameter name="pCurrencyPattern" class="java.lang.String"/>
	<parameter name="pUseCcyMultiplier" class="java.lang.String"/>
	<parameter name="pSelectedCostumerPayments" class="java.lang.String"/>
	<parameter name="pLoroPayments" class="java.lang.String"/>
	<parameter name="pCorpPayments" class="java.lang.String"/>
	<parameter name="pOtherPayments" class="java.lang.String"/>
	<parameter name="pBranchPayments" class="java.lang.String"/>
	<parameter name="pCheckBoxes" class="java.lang.String"/>
	<parameter name="pValue_Date" class="java.util.Date"/>
	<parameter name="pDictionary_Data" class="java.util.HashMap"/>
	<parameter name="pIlmUtil" class="org.swallow.reports.dao.hibernate.ReportsDAOHibernate.ILMReportUtils"/>
	<queryString>
		<![CDATA[WITH headers AS
                        (SELECT  HOST_ID, ENTITY_ID, ENTITY_NAME, CCY, CCY_NAME,
                                    DECODE ($P{pUseCcyMultiplier}, 'Y', CCY_MULTIPLIER, 'N' ) CCY_MULTIPLIER,
                                    DECODE ($P{pUseCcyMultiplier}, 'Y', CCY_MULTIPLIER_VALUE, 1 ) CCY_MULTIPLIER_VALUE,
                                    TOTAL_ROWS
                               FROM TABLE (pkg_ilm_rep.fn_header_baselc (
                                                           $P{pHost_Id},
                                                          $P{pEntity_Id},
                                                          $P{pCurrency_Code},
                                                        $P{pRoleId},
                                                        $P{pDBLink},
                                                        $P{pValue_Date},
                                                        $P{pValue_Date}
                                          ))
                         )


                SELECT     headers.ENTITY_ID,ENTITY_NAME,headers.CCY,CCY_NAME,
                        data.MAX1_CUSTOMER_PAYMENTS /CCY_MULTIPLIER_VALUE         AS MAX1_CUSTOMER_PAYMENTS ,
                        data.MAX_CREDIT_LINE_TOTAL /CCY_MULTIPLIER_VALUE        AS MAX_CREDIT_LINE_TOTAL,
                        data.MAX1_CREDIT_LINE_SECURED /CCY_MULTIPLIER_VALUE      AS MAX1_CREDIT_LINE_SECURED ,
                        data.MAX1_CREDIT_LINE_COMMITTED /CCY_MULTIPLIER_VALUE    AS MAX1_CREDIT_LINE_COMMITTED,
                        data.MAX1_PEAK_CREDIT_LINE_USED /CCY_MULTIPLIER_VALUE   AS MAX1_PEAK_CREDIT_LINE_USED,
                        
                        data.MAX1_LORO_CLIENT_PAYMENTS / CCY_MULTIPLIER_VALUE       AS MAX1_LORO_CLIENT_PAYMENTS,
                        data.MAX1_CORP_CLIENT_PAYMENTS / CCY_MULTIPLIER_VALUE       AS MAX1_CORP_CLIENT_PAYMENTS,
                        data.MAX1_OWN_ENTITY_PAYMENTS / CCY_MULTIPLIER_VALUE       AS MAX1_OWN_ENTITY_PAYMENTS,
                        data.MAX1_OTHER_PAYMENTS / CCY_MULTIPLIER_VALUE            AS MAX1_OTHER_PAYMENTS,
                        data.MAX1_BRANCH_PAYMENTS / CCY_MULTIPLIER_VALUE       AS MAX1_BRANCH_PAYMENTS,
                        
                        data.MAX1_TOTAL_PAYMENTS / CCY_MULTIPLIER_VALUE       AS MAX1_TOTAL_PAYMENTS,
                        DECODE (data.entity_id, NULL, 'NO_DATA', 'OK')             DATA_STATUS,
                        CCY_MULTIPLIER,
                        ROWNUM CURRENT_ROW,
                        TOTAL_ROWS
                  FROM headers

                  LEFT OUTER JOIN TABLE (pkg_ilm_rep.fn_main_data_baselc (headers.host_id,
                                                               headers.entity_id,
                                                               headers.ccy,
                                                               $P{pValue_Date},
                                                               $P{pValue_Date},
                                                               $P{pDBLink},
                                                               $P{pCheckBoxes}
                                                              )) data
                  ON (headers.entity_id = data.entity_id AND headers.ccy = data.ccy)]]>
	</queryString>
	<field name="ENTITY_ID" class="java.lang.String"/>
	<field name="ENTITY_NAME" class="java.lang.String"/>
	<field name="CCY" class="java.lang.String"/>
	<field name="CCY_NAME" class="java.lang.String"/>
	<field name="CCY_MULTIPLIER" class="java.lang.String"/>
	<field name="DATA_STATUS" class="java.lang.String"/>
	<field name="MAX1_CUSTOMER_PAYMENTS" class="java.lang.Double"/>
	<field name="MAX_CREDIT_LINE_TOTAL" class="java.lang.Double"/>
	<field name="MAX1_CREDIT_LINE_SECURED" class="java.lang.Double"/>
	<field name="MAX1_CREDIT_LINE_COMMITTED" class="java.lang.Double"/>
	<field name="MAX1_PEAK_CREDIT_LINE_USED" class="java.lang.Double"/>
	<field name="MAX1_OTHER_PAYMENTS" class="java.lang.Double"/>
	<field name="MAX1_OWN_ENTITY_PAYMENTS" class="java.lang.Double"/>
	<field name="MAX1_CORP_CLIENT_PAYMENTS" class="java.lang.Double"/>
	<field name="MAX1_LORO_CLIENT_PAYMENTS" class="java.lang.Double"/>
	<field name="MAX1_BRANCH_PAYMENTS" class="java.lang.Double"/>
	<field name="MAX1_TOTAL_PAYMENTS" class="java.lang.Double"/>
	<field name="CURRENT_ROW" class="java.lang.Integer"/>
	<field name="TOTAL_ROWS" class="java.lang.Integer"/>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band height="48" splitType="Stretch">
			<textField pattern="" isBlankWhenNull="false">
				<reportElement key="textField" mode="Opaque" x="0" y="0" width="535" height="24" forecolor="#000000" backcolor="#DCE6F1" uuid="56709fdd-8803-4c54-be5c-cccc91884425"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="12" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{pDictionary_Data}.get("pRAPORT_TITLE_DAILY_LABEL")]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement key="textField-1" x="280" y="24" width="136" height="20" uuid="a29f4aa2-7eed-4673-ae12-0d071acbbb11">
					<property name="local_mesure_unitx" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="10" isBold="true" pdfFontName="Helvetica-Bold"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{pDictionary_Data}.get("pREPORT_DATE_LABEL")]]></textFieldExpression>
			</textField>
			<textField evaluationTime="Report" pattern="dd-MMM-yyyy HH:mm:ss" isBlankWhenNull="false">
				<reportElement key="textField-1" x="415" y="24" width="120" height="20" uuid="1ae876d0-4d2c-4bd6-8c0b-fd00667c0460"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="10"/>
				</textElement>
				<textFieldExpression><![CDATA[new SimpleDateFormat($P{pDateFormat}+" HH:mm:ss").format(new Date())]]></textFieldExpression>
			</textField>
		</band>
	</title>
	<columnHeader>
		<band splitType="Stretch"/>
	</columnHeader>
	<detail>
		<band height="64" splitType="Prevent">
			<textField>
				<reportElement key="textField" positionType="Float" x="10" y="0" width="157" height="16" forecolor="#000000" backcolor="#FFFFFF" uuid="9fa3bade-d49d-4c2a-b160-ee87914ab02b"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font size="10" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{pDictionary_Data}.get("pENTITY_LABEL")]]></textFieldExpression>
			</textField>
			<textField pattern="###" isBlankWhenNull="true">
				<reportElement key="textField" x="200" y="0" width="120" height="16" uuid="1943ac1a-6488-4e26-860a-48b19c4ae92b"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle"/>
				<textFieldExpression><![CDATA[($F{ENTITY_ID}!=null)? $F{ENTITY_ID} :""]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement key="textField-1" x="360" y="0" width="170" height="16" uuid="7116c1b1-e499-4360-9cc5-ec2a8cc5aca9"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle"/>
				<textFieldExpression><![CDATA[($F{ENTITY_NAME}!=null)?$F{ENTITY_NAME}:""]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement key="textField" positionType="Float" x="10" y="16" width="157" height="16" forecolor="#000000" backcolor="#FFFFFF" uuid="9ad5a1df-6bd1-45ad-99f8-fa22a77c993f"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font size="10" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{pDictionary_Data}.get("pCURRENCY_LABEL")]]></textFieldExpression>
			</textField>
			<textField pattern="###" isBlankWhenNull="true">
				<reportElement key="textField" x="200" y="16" width="120" height="16" uuid="5e50c276-70aa-49e5-8999-059acbfb6c20">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle"/>
				<textFieldExpression><![CDATA[($F{CCY}!=null)? $F{CCY}:""]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement key="textField-1" x="360" y="16" width="140" height="16" uuid="d3f74efa-89b1-4f56-8189-3855ba680547"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle"/>
				<textFieldExpression><![CDATA[($F{CCY_NAME}!=null)?$F{CCY_NAME}:""]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement key="textField" positionType="Float" x="10" y="32" width="170" height="16" forecolor="#000000" backcolor="#FFFFFF" uuid="daee7584-dd74-4b22-9ba6-f664f4bd839d"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font size="10" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{pDictionary_Data}.get("pREPORTING_PERIOD_LABEL")]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement key="textField-1" x="200" y="32" width="80" height="16" uuid="ea847737-3ff4-4bce-93a2-49a9e10ce26c"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle"/>
				<textFieldExpression><![CDATA[new SimpleDateFormat($P{pDateFormat}).format($P{pValue_Date})]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement key="textField" positionType="Float" x="10" y="48" width="170" height="16" forecolor="#000000" backcolor="#FFFFFF" uuid="f8200bb9-805e-4ff8-b7ce-e7661d3fc00d">
					<property name="local_mesure_unitwidth" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font size="10" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{pDictionary_Data}.get("pCCY_MULTIPLIER_LABEL")]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement key="textField-1" x="200" y="48" width="80" height="16" uuid="c85fde15-e4f9-4bbc-a989-fc39b6883871"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle"/>
				<textFieldExpression><![CDATA[$P{pDictionary_Data}.get("pCCY_MULTIPLIER_"+$F{CCY_MULTIPLIER})]]></textFieldExpression>
			</textField>
		</band>
		<band height="17">
			<textField pattern="" isBlankWhenNull="false">
				<reportElement key="textField" mode="Opaque" x="10" y="0" width="170" height="16" forecolor="#000000" uuid="57e20c98-ebde-4a93-918b-2e98862c6501">
					<property name="local_mesure_unitwidth" value="pixel"/>
					<property name="local_mesure_unitx" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font size="10" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{pDictionary_Data}.get("pDATA_STATE")]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="false">
				<reportElement key="textField" mode="Opaque" x="200" y="0" width="250" height="16" forecolor="#000000" uuid="ac6f27fe-addd-4db3-991b-1efe05637526"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle"/>
				<textFieldExpression><![CDATA[$F{DATA_STATUS}.equals("NO_DATA")? $P{pDictionary_Data}.get("pINCOMPLETE") : $P{pDictionary_Data}.get("pCOMPLETE")]]></textFieldExpression>
			</textField>
		</band>
		<band height="90">
			<printWhenExpression><![CDATA[!$F{DATA_STATUS}.equals("NO_DATA") && $P{pSelectedCostumerPayments} == null]]></printWhenExpression>
			<textField pattern="" isBlankWhenNull="false">
				<reportElement key="textField" mode="Opaque" x="0" y="10" width="535" height="30" forecolor="#000000" backcolor="#DCE6F1" uuid="159b7a20-fdbc-46b4-8391-4c772efcdc8c">
					<property name="local_mesure_unity" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Top">
					<font size="10" isBold="true" pdfFontName="Helvetica-Bold"/>
					<paragraph leftIndent="7"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{pDictionary_Data}.get("pVALUE_PAYMENTS")]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement key="textField" positionType="Float" x="10" y="45" width="270" height="45" forecolor="#000000" backcolor="#FFFFFF" uuid="72ec74e5-5a78-4259-83cc-fb5fceba83d8"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement>
					<font fontName="SansSerif" size="10" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{pDictionary_Data}.get("pTOTAL_CROSS_PAYMENTS")]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement key="textField-1" x="392" y="45" width="132" height="16" uuid="61e5d7b1-6b6e-47da-aa7b-83f53593da3e"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[($F{MAX1_CUSTOMER_PAYMENTS}!=null)?($P{pIlmUtil}.formatCurrency($F{MAX1_CUSTOMER_PAYMENTS},$P{pCurrencyPattern})):"-"]]></textFieldExpression>
			</textField>
		</band>
		<band height="120">
			<printWhenExpression><![CDATA[!$F{DATA_STATUS}.equals("NO_DATA") && $P{pSelectedCostumerPayments} != null]]></printWhenExpression>
			<textField pattern="" isBlankWhenNull="false">
				<reportElement key="textField" mode="Opaque" x="0" y="10" width="535" height="33" forecolor="#000000" backcolor="#DCE6F1" uuid="1581c0cb-10ab-41b7-a3ee-41428dd64983">
					<property name="local_mesure_unity" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Top">
					<font size="10" isBold="true" pdfFontName="Helvetica-Bold"/>
					<paragraph leftIndent="7"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{pDictionary_Data}.get("pVALUE_PAYMENTS")]]></textFieldExpression>
			</textField>
			<frame>
				<reportElement positionType="Float" x="0" y="43" width="370" height="77" backcolor="#999999" uuid="a3fba9e7-b53f-4d01-af26-97607a72d296">
					<property name="com.jaspersoft.studio.layout" value="com.jaspersoft.studio.editor.layout.VerticalRowLayout"/>
					<printWhenExpression><![CDATA[!$P{pCorpPayments}.equals( null )]]></printWhenExpression>
				</reportElement>
				<textField>
					<reportElement mode="Opaque" x="0" y="0" width="370" height="17" backcolor="#D9D9D9" uuid="9f5a93ca-ccd1-4e32-b985-51ea40703d24">
						<property name="com.jaspersoft.studio.unit.leftIndent" value="px"/>
					</reportElement>
					<textElement verticalAlignment="Middle">
						<paragraph leftIndent="10"/>
					</textElement>
					<textFieldExpression><![CDATA[$P{pDictionary_Data}.get("pLoroClient_LABEL")]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement positionType="FixRelativeToBottom" mode="Opaque" x="0" y="17" width="370" height="15" backcolor="#D9D9D9" uuid="26d202ca-6641-4f85-8bd9-393ffc9e404f">
						<property name="com.jaspersoft.studio.unit.leftIndent" value="px"/>
					</reportElement>
					<textElement verticalAlignment="Middle">
						<paragraph leftIndent="10"/>
					</textElement>
					<textFieldExpression><![CDATA[$P{pDictionary_Data}.get("pCorporateClient_LABEL")]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Opaque" x="0" y="32" width="370" height="15" isPrintInFirstWholeBand="true" backcolor="#D9D9D9" uuid="ad17ac8d-cd7e-4c31-8d29-90291bad1657">
						<property name="com.jaspersoft.studio.unit.leftIndent" value="px"/>
					</reportElement>
					<textElement verticalAlignment="Middle">
						<paragraph leftIndent="10"/>
					</textElement>
					<textFieldExpression><![CDATA[$P{pDictionary_Data}.get("pBranch_LABEL")]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Opaque" x="0" y="47" width="370" height="15" backcolor="#D9D9D9" uuid="80deee4c-dfc9-44c5-a56c-98584028d91b">
						<property name="com.jaspersoft.studio.unit.leftIndent" value="px"/>
					</reportElement>
					<textElement verticalAlignment="Middle">
						<paragraph leftIndent="10"/>
					</textElement>
					<textFieldExpression><![CDATA[$P{pDictionary_Data}.get("pOtherClient_LABEL")]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement key="textField" positionType="Float" mode="Opaque" x="0" y="62" width="370" height="15" forecolor="#000000" backcolor="#BFBFBF" uuid="31a73fe3-cf8c-43d3-8a63-3c95f0338a8a">
						<property name="com.jaspersoft.studio.unit.leftIndent" value="px"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement verticalAlignment="Middle">
						<font size="10" isBold="true" pdfFontName="Helvetica-Bold"/>
						<paragraph leftIndent="10"/>
					</textElement>
					<textFieldExpression><![CDATA[$P{pDictionary_Data}.get("pTOTAL_LABEL")]]></textFieldExpression>
				</textField>
			</frame>
			<frame>
				<reportElement positionType="Float" x="370" y="43" width="164" height="77" backcolor="#999999" uuid="e6c8d347-0ee6-45e2-85c8-934d18947da6">
					<property name="com.jaspersoft.studio.layout" value="com.jaspersoft.studio.editor.layout.VerticalRowLayout"/>
					<printWhenExpression><![CDATA[!$P{pCorpPayments}.equals( null )]]></printWhenExpression>
				</reportElement>
				<textField>
					<reportElement mode="Opaque" x="0" y="0" width="164" height="17" backcolor="#D9D9D9" uuid="20c346e4-1329-46bc-814c-2a26833a9c67">
						<property name="com.jaspersoft.studio.unit.rightIndent" value="px"/>
					</reportElement>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<paragraph rightIndent="10"/>
					</textElement>
					<textFieldExpression><![CDATA[($F{MAX1_LORO_CLIENT_PAYMENTS}!=null &&  !$P{pLoroPayments}.isEmpty())?($P{pIlmUtil}.formatCurrency($F{MAX1_LORO_CLIENT_PAYMENTS},$P{pCurrencyPattern})):"-"]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Opaque" x="0" y="17" width="164" height="15" backcolor="#D9D9D9" uuid="6ced238d-a16d-47ee-a5f2-186f599eaca5">
						<property name="com.jaspersoft.studio.unit.rightIndent" value="px"/>
					</reportElement>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<paragraph rightIndent="10"/>
					</textElement>
					<textFieldExpression><![CDATA[($F{MAX1_CORP_CLIENT_PAYMENTS}!=null && !$P{pCorpPayments}.isEmpty())?($P{pIlmUtil}.formatCurrency($F{MAX1_CORP_CLIENT_PAYMENTS},$P{pCurrencyPattern})):"-"]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Opaque" x="0" y="32" width="164" height="15" backcolor="#D9D9D9" uuid="92c090c7-6d46-4408-87e7-736688c5316a">
						<property name="com.jaspersoft.studio.unit.rightIndent" value="px"/>
					</reportElement>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<paragraph rightIndent="10"/>
					</textElement>
					<textFieldExpression><![CDATA[($F{MAX1_BRANCH_PAYMENTS}!=null &&  !$P{pBranchPayments}.isEmpty())?($P{pIlmUtil}.formatCurrency($F{MAX1_BRANCH_PAYMENTS},$P{pCurrencyPattern})):"-"]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement mode="Opaque" x="0" y="47" width="164" height="15" backcolor="#D9D9D9" uuid="4e47d558-9a6b-4e14-a09e-2987068405d3">
						<property name="com.jaspersoft.studio.unit.rightIndent" value="px"/>
					</reportElement>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<paragraph rightIndent="10"/>
					</textElement>
					<textFieldExpression><![CDATA[($F{MAX1_OTHER_PAYMENTS}!=null && !$P{pOtherPayments}.isEmpty())?($P{pIlmUtil}.formatCurrency($F{MAX1_OTHER_PAYMENTS},$P{pCurrencyPattern})):"-"]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement key="textField-1" mode="Opaque" x="0" y="62" width="164" height="15" backcolor="#BFBFBF" uuid="a044c344-ed81-4f9c-a546-5dc77ca55dc1">
						<property name="com.jaspersoft.studio.unit.leftIndent" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.rightIndent" value="px"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font size="10" isBold="true" pdfFontName="Helvetica-Bold"/>
						<paragraph rightIndent="10"/>
					</textElement>
					<textFieldExpression><![CDATA[($F{MAX1_TOTAL_PAYMENTS}!=null)?($P{pIlmUtil}.formatCurrency($F{MAX1_TOTAL_PAYMENTS},$P{pCurrencyPattern})):"-"]]></textFieldExpression>
				</textField>
			</frame>
		</band>
		<band height="127" splitType="Prevent">
			<printWhenExpression><![CDATA[!$F{DATA_STATUS}.equals("NO_DATA")]]></printWhenExpression>
			<textField pattern="" isBlankWhenNull="false">
				<reportElement key="textField" mode="Opaque" x="0" y="0" width="535" height="32" forecolor="#000000" backcolor="#DCE6F1" uuid="5c39ff79-2814-4c04-b370-8642755c0e4c"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Top">
					<font size="10" isBold="true" pdfFontName="Helvetica-Bold"/>
					<paragraph leftIndent="7"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{pDictionary_Data}.get("pINTRADAY_CREDIT_LINES")]]></textFieldExpression>
			</textField>
			<frame>
				<reportElement positionType="Float" mode="Opaque" x="0" y="30" width="535" height="32" backcolor="#BFBFBF" uuid="da6a4632-87c4-4035-9574-f9399c122a24">
					<property name="local_mesure_unitx" value="pixel"/>
				</reportElement>
				<textField pattern="" isBlankWhenNull="false">
					<reportElement key="textField" mode="Opaque" x="10" y="0" width="250" height="30" forecolor="#000000" backcolor="#BFBFBF" uuid="2c845510-9c06-4bbb-a7b7-53c4e322f064"/>
					<textElement textAlignment="Left" verticalAlignment="Top">
						<font size="10" isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<textFieldExpression><![CDATA[$P{pDictionary_Data}.get("pINTRADAY_TOTAL_VALUE_CREDIT")]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="392" y="10" width="132" height="16" uuid="fadcedfc-0140-43ce-a862-fba454d92821"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font size="10" isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<textFieldExpression><![CDATA[($F{MAX_CREDIT_LINE_TOTAL}!=null)?($P{pIlmUtil}.formatCurrency($F{MAX_CREDIT_LINE_TOTAL},$P{pCurrencyPattern})):"-"]]></textFieldExpression>
				</textField>
			</frame>
			<frame>
				<reportElement positionType="Float" mode="Opaque" x="0" y="62" width="535" height="65" backcolor="#F2F2F2" uuid="9507a278-761d-4eaa-8dcb-19c18ae3417b"/>
				<textField>
					<reportElement key="textField" positionType="Float" x="10" y="10" width="219" height="16" forecolor="#000000" backcolor="#FFFFFF" uuid="cca95942-7b41-4ac7-bf8a-4a1c84008ffb"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement>
						<font fontName="SansSerif" size="10" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$P{pDictionary_Data}.get("pOF_WHICH_SECURED")]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement key="textField-1" x="392" y="10" width="132" height="16" uuid="29f0d786-e85f-4f0f-bf39-b6b0639458f9"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle"/>
					<textFieldExpression><![CDATA[($F{MAX1_CREDIT_LINE_SECURED}!=null)?($P{pIlmUtil}.formatCurrency($F{MAX1_CREDIT_LINE_SECURED},$P{pCurrencyPattern})):"-"]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement key="textField" positionType="Float" x="10" y="26" width="219" height="16" forecolor="#000000" backcolor="#FFFFFF" uuid="932ca493-0343-4e8a-83dd-bd207d25222f"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement>
						<font fontName="SansSerif" size="10" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$P{pDictionary_Data}.get("pOF_WHICH_COMMITTED")]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement key="textField-1" x="392" y="26" width="132" height="16" uuid="8e25a8ea-4d6c-4d05-bb50-f3c22cb84669"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle"/>
					<textFieldExpression><![CDATA[($F{MAX1_CREDIT_LINE_COMMITTED}!=null)?($P{pIlmUtil}.formatCurrency($F{MAX1_CREDIT_LINE_COMMITTED},$P{pCurrencyPattern})):"-"]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement key="textField" positionType="Float" x="10" y="42" width="219" height="16" forecolor="#000000" backcolor="#FFFFFF" uuid="2d893d33-62b6-4740-9966-727aec2fe6f0"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement>
						<font fontName="SansSerif" size="10" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$P{pDictionary_Data}.get("pOF_WHICH_PEAK_USAGE")]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement key="textField-1" x="392" y="42" width="132" height="16" uuid="f4d1f509-d377-48ca-84d6-91be833b52f6"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle"/>
					<textFieldExpression><![CDATA[($F{MAX1_PEAK_CREDIT_LINE_USED}!=null)?($P{pIlmUtil}.formatCurrency($F{MAX1_PEAK_CREDIT_LINE_USED},$P{pCurrencyPattern})):"-"]]></textFieldExpression>
				</textField>
			</frame>
		</band>
		<band height="40">
			<printWhenExpression><![CDATA[$F{DATA_STATUS}.equals("NO_DATA")]]></printWhenExpression>
			<textField pattern="" isBlankWhenNull="false">
				<reportElement key="textField" mode="Opaque" x="0" y="0" width="534" height="40" forecolor="#000000" backcolor="#FFFFFF" uuid="106f2a13-7660-4fdc-9d8a-5fd3ecb0966d"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="12" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{pDictionary_Data}.get("pNO_DATA_FOUND")]]></textFieldExpression>
			</textField>
		</band>
		<band height="1">
			<printWhenExpression><![CDATA[$F{CURRENT_ROW} != $F{TOTAL_ROWS}]]></printWhenExpression>
			<break>
				<reportElement x="0" y="0" width="100" height="1" uuid="b9c2ffb9-d860-41ce-b7d2-535b71be512d"/>
			</break>
		</band>
	</detail>
	<columnFooter>
		<band splitType="Stretch"/>
	</columnFooter>
	<pageFooter>
		<band height="27" splitType="Stretch">
			<textField pattern="" isBlankWhenNull="false">
				<reportElement key="textField" x="326" y="4" width="170" height="19" forecolor="#000000" backcolor="#FFFFFF" uuid="3b26bd25-365b-4024-bc2d-6753fbbfbbe5">
					<property name="local_mesure_unitx" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right">
					<font size="10"/>
				</textElement>
				<textFieldExpression><![CDATA["Page " + $V{PAGE_NUMBER} + " " +$P{pDictionary_Data}.get("pOF_LABEL")]]></textFieldExpression>
			</textField>
			<textField evaluationTime="Report" pattern="" isBlankWhenNull="false">
				<reportElement key="textField" x="501" y="4" width="36" height="19" forecolor="#000000" backcolor="#FFFFFF" uuid="cda63399-ce1e-490d-bee5-0f2b8cd2ca0b">
					<property name="local_mesure_unitx" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement>
					<font size="10"/>
				</textElement>
				<textFieldExpression><![CDATA["" + $V{PAGE_NUMBER}]]></textFieldExpression>
			</textField>
			<line>
				<reportElement key="line" x="0" y="3" width="535" height="1" uuid="6a655829-1338-4433-85c1-28bb36f4278b">
					<property name="local_mesure_unitwidth" value="pixel"/>
				</reportElement>
			</line>
			<textField pattern="dd/MM/yyyy" isBlankWhenNull="false">
				<reportElement key="textField" x="20" y="4" width="209" height="19" forecolor="#000000" backcolor="#FFFFFF" uuid="6b3f1b96-fe18-444a-b74b-8bb665f8133e"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement>
					<font size="10"/>
				</textElement>
				<textFieldExpression><![CDATA[new SimpleDateFormat($P{pDateFormat}).format(new Date())]]></textFieldExpression>
			</textField>
		</band>
	</pageFooter>
	<summary>
		<band height="34" splitType="Prevent">
			<textField>
				<reportElement key="textField-3" x="167" y="14" width="200" height="20" uuid="522750b8-c30d-4447-a5d6-ecc85aa28047"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center">
					<font size="12"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{pDictionary_Data}.get("pEND_OF_REPORT_LABEL")]]></textFieldExpression>
			</textField>
		</band>
	</summary>
</jasperReport>
