<%@ page import="com.crystaldecisions.report.web.viewer.*"%> 
<%@ page import="com.crystaldecisions.report.htmlrender.*"%> 
<%@ page import="com.crystaldecisions.reports.reportengineinterface.*"%> 
<%@ page import="com.crystaldecisions.sdk.occa.report.reportsource.*"%> 
<%@ page import="com.crystaldecisions.sdk.occa.report.data.*"%> 
<%@ page import="com.crystaldecisions.common.keycode.*"%> 
 
<%@ page import="com.crystaldecisions.sdk.occa.report.reportsource.IReportSource"%>
<%@ page import="com.crystaldecisions.sdk.occa.report.reportsource.IReportSourceFactory2"%>
<%@ page import="org.swallow.reports.model.Reports" %>
<%@ page import="java.util.*"%> 
<html>
<head><title>JSP Page</title></head>
<body>

<% 
try { 

String report = "TurnOverStaticReport.rpt"; 
       IReportSourceFactory2 rptSrcFactory = new JPEReportSourceFactory(); 
                
IReportSource reportSource = (IReportSource) rptSrcFactory.createReportSource(report,request.getLocale());
Fields fields=new Fields();

ParameterField  pfield1 = new ParameterField();
ParameterField  pfield2 = new ParameterField ();

Values vals1=new Values();
Values vals2=new Values();

ParameterFieldDiscreteValue pfieldsDV1=new ParameterFieldDiscreteValue();
pfield1.setReportName("");

String userName=request.getParameter("userName");
String date1=request.getParameter("date1");
String date2=request.getParameter("date2");
Reports users=new Reports();
  users.setFromDateAsString(date1);
  java.text.SimpleDateFormat sdf = new java.text.SimpleDateFormat("mm/DD/yyyy");
  java.util.Date dateFrom=sdf.parse(date1);
out.println("userName============"+userName);
out.println("Date1============"+date1);
out.println("Date1New============"+dateFrom);
out.println("Date2============"+date2);
// set the value getting from jsp
pfieldsDV1.setValue("user1");

// set name given in the Crystal report 
pfield1.setName("user1");// frist paramter

// add parameter to value Object 
vals1.add(pfieldsDV1);
// set the current value to parameter 
pfield1.setCurrentValues(vals1);
 // add parameter in field 
fields.add(pfield1);

ConnectionInfo ci = new ConnectionInfo();
ci.setUserName("pred_usr2");
ci.setPassword("password");

ConnectionInfos connections = new ConnectionInfos();
connections.add(ci);
out.println("Error1=");
CrystalReportViewer viewer = new CrystalReportViewer(); 
viewer.setReportSource(reportSource); 
viewer.setDatabaseLogonInfos(connections);
out.println("Error2=");
viewer.setParameterFields(fields);
out.println("Error3=");
viewer.refresh();
out.println("Error4=");
viewer.processHttpRequest(request, response, getServletConfig().getServletContext(),out); 
out.println("Error5=");
viewer.dispose(); 

}catch(Exception e){ 
out.println("exception at the end= " + e.getMessage()); 
} 
%>


</body>
</html>
