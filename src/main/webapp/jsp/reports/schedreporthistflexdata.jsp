<?xml version="1.0" encoding="utf-8"?>

<%@ page contentType="text/xml" %>
<%@ taglib prefix="s" uri="/struts-tags" %>

<schedreporthist >
	<request_reply>
		<status_ok><s:property value="#request.reply_status_ok" /></status_ok>
		<message><s:property value="#request.reply_message" /></message>
		<location />
	</request_reply>
	<singletons>
		<deleteResult><s:property value="#request.deleteResult" /></deleteResult>
	</singletons>
	<grid>
		<metadata>
			<columns>
				<s:iterator value="#request.column_order" var="order" >	
				<s:if test='"fileId" == #request.order' >
						<column heading="<s:text name="label.schedreporthist.column.fileId"/>"
						 tooltip = "<s:text name="tooltip.schedreporthist.fileId"/>"
						 clickable="true" 
						 draggable="true"
						 filterable="true" 
						 sort="true"
						 type="num" 
						 dataelement="fileId"
						 width="<s:property value="#request.column_width.fileId" />"/>
				</s:if>
					<s:if test='"runDate" == #request.order' >
						<column heading="<s:text name="label.schedreporthist.column.runDate"/>"
						tooltip = "<s:text name="tooltip.schedreporthist.runDate"/>"
						 clickable="true" 
						 draggable="true"
						 filterable="true" 
						 type="date" 
						  sort="true"
						 dataelement="runDate"
						 width="<s:property value="#request.column_width.runDate" />"/>
					</s:if>
					<s:if test='"reportName" == #request.order' >
						<column heading="<s:text name="label.schedreporthist.column.reportName"/>"
						tooltip = "<s:text name="tooltip.schedreporthist.reportName"/>"
						 clickable="true" 
						 draggable="true"
						 filterable="true" 
						 type="str" 
						  sort="true"
						 dataelement="reportName"
						 width="<s:property value="#request.column_width.reportName" />"/>
					</s:if>
					<s:if test='"elapsedTime" == #request.order' >
						<column heading="<s:text name="label.schedreporthist.column.elapsedTime"/>"
						tooltip = "<s:text name="tooltip.schedreporthist.elapsedTime"/>"
						 clickable="true" 
						 draggable="true"
						 filterable="true" 
						 type="date" 
						  sort="true"
						 dataelement="elapsedTime"
						 width="<s:property value="#request.column_width.elapsedTime" />"/>
					</s:if>
					<s:if test='"fileName" == #request.order' >
						<column heading="<s:text name="label.schedreporthist.column.fileName"/>"
						tooltip = "<s:text name="tooltip.schedreporthist.fileName"/>"
						 clickable="true" 
						 draggable="true"
						 filterable="true" 
						 type="str" 
						  sort="true"
						 dataelement="fileName"
						 width="<s:property value="#request.column_width.fileName" />"/>
					</s:if>
					<s:if test='"exportStatus" == #request.order' >
						<column heading="<s:text name="label.schedreporthist.column.exportStatus"/>"
						tooltip = "<s:text name="tooltip.schedreporthist.exportStatus"/>"
						 clickable="true" 
						 draggable="true"
						 filterable="true" 
						 type="str" 
						  sort="true"
						 dataelement="exportStatus"
						 width="<s:property value="#request.column_width.exportStatus" />"/>
					</s:if>
					<s:if test='"mailStatus" == #request.order' >
						<column heading="<s:text name="label.schedreporthist.column.mailStatus"/>"
						tooltip = "<s:text name="tooltip.schedreporthist.mailStatus"/>"
						 clickable="true" 
						 draggable="true"
						 filterable="true" 
						 type="str" 
						  sort="true"
						 dataelement="mailStatus"
						 width="<s:property value="#request.column_width.mailStatus" />"/>
					</s:if>
				</s:iterator>
			</columns>
		</metadata>
		<rows size="${recordCount}">
			<s:iterator value="#request.schedReportHistList" var="schedReportHistList" >
				<row>
					<fileId clickable="false"><s:property value="#schedReportHistList.id.fileId" /></fileId>
					<hostId clickable="false"><s:property value="#schedReportHistList.hostId" /></hostId>
					<jobId clickable="false"><s:property value="#schedReportHistList.jobId" /></jobId>
					<reportName clickable="false"><s:property value="#schedReportHistList.reportName" /></reportName>
					<reportTypeId clickable="false"><s:property value="#schedReportHistList.reportTypeId" /></reportTypeId>
					<scheduleId clickable="false"><s:property value="#schedReportHistList.scheduleId" /></scheduleId>
					<runDate clickable="false"><s:property value="#schedReportHistList.runDateAsString" /></runDate>
					<elapsedTime clickable="false"><s:property value="#schedReportHistList.elapsedTimeAsString" /></elapsedTime>
					<fileName clickable="false"><s:property value="#schedReportHistList.fileName" /></fileName>
					<outputLocation clickable="false"><s:property value="#schedReportHistList.outputLocation" /></outputLocation>
					<fileSize clickable="false"><s:property value="#schedReportHistList.fileSize" /></fileSize>
					<exportStatus clickable="false"><s:property value="#schedReportHistList.exportStatus" /></exportStatus>
					<mailStatus clickable="false"><s:property value="#schedReportHistList.mailStatus" /></mailStatus>
					<exportError clickable="false"><s:property value="#schedReportHistList.exportError" /></exportError>
					<mailRsult clickable="false"><s:property value="#schedReportHistList.mailRsult" /></mailRsult>
				</row>
			</s:iterator>
		</rows>					
	</grid>			
	<selects> 
		<select id="reportJobs"> 
			<s:iterator value="#request.reportJobs" var="job" >
				<option value="<s:property value="#job.value" />"
					selected="<s:if test="#job.value == #request.selectedReportJob">1</s:if><s:if test="#job.value != #request.selectedReportJob">0</s:if>">
					<s:property value="#job.label" /></option>
			</s:iterator> 
		</select> 
		<select id="reportTypes"> 
			<s:iterator value="#request.reportTypes" var="type" >
				<option value="<s:property value="#type.value" />"
					selected="<s:if test="#type.value == #request.selectedReportType">1</s:if><s:if test="#type.value != #request.selectedReportType">0</s:if>">
					<s:property value="#type.label" /></option>
			</s:iterator> 
		</select> 
	</selects>				
</schedreporthist>