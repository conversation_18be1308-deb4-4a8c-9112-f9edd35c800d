<?xml version="1.0" encoding="UTF-8"  ?>
<!-- Created with iReport - A designer for JasperReports -->
<!-- xml namespace changed,deprecated 'issplittype' attribute replaced with 'splitType' attribute for Mantis 2035 by <PERSON> on 18-Sep-2012 -->
<jasperReport 
  xmlns="http://jasperreports.sourceforge.net/jasperreports"
		 name="IntradayBalanceGraph"
		 columnCount="1"
		 printOrder="Vertical"
		 orientation="Landscape"
		 pageWidth="895"
		 pageHeight="750"
		 columnWidth="835"
		 columnSpacing="0"
		 leftMargin="30"
		 rightMargin="30"
		 topMargin="20"
		 bottomMargin="20"
		 whenNoDataType="AllSectionsNoDetail"
		 isTitleNewPage="false"
		 isSummaryNewPage="false">
	<property name="ireport.scriptlethandling" value="0" />
	<property name="ireport.encoding" value="UTF-8" />
	<import value="java.util.*" />
	<import value="net.sf.jasperreports.engine.*" />
	<import value="net.sf.jasperreports.engine.data.*" />

	<parameter name="pHost_Id" isForPrompting="true" class="java.lang.String"/>
	<parameter name="pEntity_Id" isForPrompting="true" class="java.lang.String"/>
	<parameter name="pEntity_Name" isForPrompting="true" class="java.lang.String"/>
	<parameter name="pReport_Date" isForPrompting="true" class="java.util.Date"/>
	<parameter name="pAccount_Id" isForPrompting="true" class="java.lang.String"/>
	<parameter name="pCurrency_Code" isForPrompting="true" class="java.lang.String"/>
	<parameter name="pCurrency_Name" isForPrompting="true" class="java.lang.String"/>
	<parameter name="pCurrencyPattern" isForPrompting="false" class="java.lang.String"/>
	<parameter name="pdateFormat" class="java.lang.String"/>
	<queryString><![CDATA[SELECT   nvl(sodbal.SOD_Balance,0) AS SOD_Balance,nvl(sodbal.interval_start_time,0) AS interval_start_time, sodbal.debit_sum, sodbal.credit_sum,nvl(sodbal.Balance,0) as Balance, acn.account_name, acn.account_id, nvl(decode(trunc(sodbal.interval_end-1),trunc(sodbal.interval_start),to_char((sodbal.interval_end-1)+(23/24)+(59/1440),$P{pdateFormat}),sodbal.interval_end_time),0) AS interval_end_time,acn.account_level
  FROM ( SELECT bal.host_id, bal.entity_id, bal.bal_type_id as account_id, TO_CHAR(sod.interval_end,$P{pdateFormat}) AS interval_end_time, sod.interval_start, sod.interval_end,
                TO_CHAR(sod.interval_start,$P{pdateFormat}) AS interval_start_time,(NVL(bal.working_external_sod, 0) + NVL(bal.bv_external_adjust, 0)) SOD_Balance,
                NVL(sod.external_amount_dr,0) AS debit_sum, NVL(sod.external_amount_cr,0) AS credit_sum,
                (NVL(bal.working_external_sod, 0) + NVL(bal.bv_external_adjust, 0)) - sum(NVL(sod.bal,0)) OVER (PARTITION BY bal.bal_type_id ORDER BY bal.bal_type_id,sod.interval_end) AS Balance
           FROM (SELECT i.host_id, i.entity_id, i.account_id, i.interval_end, TRUNC(i.interval_start)  intr_dt,i.interval_start,
                        NVL(i.external_amount_dr,0) AS external_amount_dr , NVL(i.external_amount_cr,0) AS external_amount_cr,
                        (NVL(i.external_amount_dr,0) - NVL(i.external_amount_cr,0)) AS bal,
                        ROW_NUMBER () OVER (ORDER BY i.account_id,i.interval_end) AS rowno
                   FROM p_intraday_stats I, p_account a, s_currency c
                  WHERE i.host_id = $P{pHost_Id}
                    AND i.entity_id = $P{pEntity_Id}
                    AND TRUNC (i.interval_start) = $P{pReport_Date}
					AND a.ACCOUNT_ID = $P{pAccount_Id}
                    AND I.host_id = a.host_id
                    AND I.entity_id = a.entity_id
                    AND I.ACCOUNT_ID = a.ACCOUNT_ID
                    AND a.host_id = c.host_id
                    AND a.entity_id = c.entity_id
                    AND a.currency_code = c.currency_code
                    AND c.currency_code = $P{pCurrency_Code}) sod,
                (SELECT b.*
                   FROM p_balance b, p_account a, s_currency c
                  WHERE b.host_id = $P{pHost_Id}
                    AND b.entity_id = $P{pEntity_Id}
                    AND b.balance_date = $P{pReport_Date}
					AND a.ACCOUNT_ID = $P{pAccount_Id}
                    AND b.host_id = a.host_id
                    AND b.entity_id = a.entity_id
                    AND b.bal_type_id = a.ACCOUNT_ID
                    AND a.host_id = c.host_id
                    AND a.entity_id = c.entity_id
                    AND a.currency_code = c.currency_code
                    AND c.currency_code = $P{pCurrency_Code}) bal
          WHERE bal.host_id = sod.host_id(+)
            AND bal.entity_id = sod.entity_id(+)
            AND bal.balance_date = sod.intr_dt(+)
            AND bal.bal_type_id = sod.account_id(+)
       ) sodbal,
       ( SELECT a.host_id, a.entity_id, a.account_id, a.account_name, a.account_level            
           FROM p_account a, s_currency c
          WHERE a.host_id = c.host_id
            AND a.entity_id = c.entity_id
            AND a.currency_code = c.currency_code
			AND a.ACCOUNT_ID = $P{pAccount_Id}
            AND c.currency_code = $P{pCurrency_Code}
       ) acn
  WHERE SODBAL.host_id(+) = acn.host_id
    AND SODBAL.entity_id(+) = acn.entity_id
    AND SODBAL.account_id(+) = acn.account_id
    AND SODBAL.interval_end_time IS NOT NULL
   ORDER BY acn.account_level]]></queryString>

	<field name="SOD_BALANCE" class="java.math.BigDecimal"/>
	<field name="INTERVAL_END_TIME" class="java.lang.String"/>
	<field name="INTERVAL_START_TIME" class="java.lang.String"/>
	<field name="DEBIT_SUM" class="java.math.BigDecimal"/>
	<field name="CREDIT_SUM" class="java.math.BigDecimal"/>
	<field name="BALANCE" class="java.math.BigDecimal"/>
	<field name="ACCOUNT_NAME" class="java.lang.String"/>
	<field name="ACCOUNT_ID" class="java.lang.String"/>


		<group  name="Group1" isStartNewPage="true" >
			<groupExpression><![CDATA[$F{ACCOUNT_ID}]]></groupExpression>
			<groupHeader>
			<band height="0"  splitType="Stretch" >
			</band>
			</groupHeader>
			<groupFooter>
			<band height="0"  splitType="Stretch" >
			</band>
			</groupFooter>
		</group>
		<group  name="Group2" >
			<groupExpression><![CDATA[$F{INTERVAL_END_TIME}]]></groupExpression>
			<groupHeader>
			<band height="0"  splitType="Stretch" >
			</band>
			</groupHeader>
			<groupFooter>
			<band height="0"  splitType="Stretch" >
			</band>
			</groupFooter>
		</group>
		<background>
			<band height="0"  splitType="Stretch" >
			</band>
		</background>
		<title>
			<band height="124"  splitType="Stretch" >
				<staticText>
					<reportElement
						x="27"
						y="54"
						width="61"
						height="23"
						key="staticText-1"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left" verticalAlignment="Top">
						<font pdfFontName="Helvetica-Bold" size="12" isBold="true"/>
					</textElement>
				<text><![CDATA[Entity]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="97"
						y="54"
						width="92"
						height="23"
						key="textField-1"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left" verticalAlignment="Top">
						<font size="12"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$P{pEntity_Id}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="575"
						y="53"
						width="87"
						height="24"
						key="staticText-2"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left" verticalAlignment="Top">
						<font pdfFontName="Helvetica-Bold" size="12" isBold="true"/>
					</textElement>
				<text><![CDATA[Balance Date :]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" pattern="dd-MMM-yyyy" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="669"
						y="53"
						width="100"
						height="24"
						key="textField-2"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left" verticalAlignment="Top">
						<font size="12"/>
					</textElement>
				<textFieldExpression   class="java.util.Date"><![CDATA[$P{pReport_Date}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="27"
						y="83"
						width="61"
						height="23"
						key="staticText-3"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left" verticalAlignment="Top">
						<font pdfFontName="Helvetica-Bold" size="12" isBold="true"/>
					</textElement>
				<text><![CDATA[Currency]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="97"
						y="83"
						width="92"
						height="23"
						key="textField-3"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left" verticalAlignment="Top">
						<font size="12"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$P{pCurrency_Code}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="276"
						y="12"
						width="299"
						height="27"
						key="staticText-5"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Center" verticalAlignment="Top">
						<font pdfFontName="Helvetica-Bold" size="18" isBold="true"/>
					</textElement>
				<text><![CDATA[Intra-Day Balance Fluctuation ]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >					
					<reportElement
						x="190"
						y="54"
						width="250"
						height="23"
						key="textField-10"/>					
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left" verticalAlignment="Top">
						<font size="12"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$P{pEntity_Name}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="false" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="190"
						y="83"
						width="242"
						height="23"
						key="textField-11"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left" verticalAlignment="Top">
						<font size="12"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$P{pCurrency_Name}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="78"
						y="54"
						width="8"
						height="20"
						key="staticText-11"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left" verticalAlignment="Top">
						<font pdfFontName="Helvetica-Bold" size="12" isBold="true" isUnderline="false"/>
					</textElement>
				<text><![CDATA[ :]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="78"
						y="84"
						width="11"
						height="20"
						key="staticText-12"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left" verticalAlignment="Top">
						<font pdfFontName="Helvetica-Bold" size="12" isBold="true" isUnderline="false"/>
					</textElement>
				<text><![CDATA[ :]]></text>
				</staticText>
				<line direction="TopDown">
					<reportElement
						x="2"
						y="47"
						width="829"
						height="0"
						key="line-1"/>
					<graphicElement stretchType="NoStretch"/>
				</line>
				<line direction="TopDown">
					<reportElement
						x="2"
						y="112"
						width="829"
						height="0"
						key="line-2"/>
					<graphicElement stretchType="NoStretch"/>
				</line>
			</band>
		</title>
		<pageHeader>
			<band height="504"  splitType="Stretch" >
				<multiAxisChart>
					<chart evaluationTime="Group" evaluationGroup="Group1"  hyperlinkTarget="Self" >
					<reportElement
						x="27"
						y="101"
						width="786"
						height="395"
						key="element-1">
							<printWhenExpression><![CDATA[($F{ACCOUNT_ID}!=null) ?  new Boolean(true) :new Boolean(false)]]></printWhenExpression>
						</reportElement>
					<box></box>
						<chartLegend textColor="#000000" backgroundColor="#FFFFFF" >
					</chartLegend>
					</chart>
					<multiAxisPlot>
						<plot />
							<axis position="rightOrBottom" >
				<timeSeriesChart>
					<chart customizerClass="org.swallow.util.SwtChartCustomizer" evaluationTime="Group" evaluationGroup="Group1"  hyperlinkTarget="Self" >
					<reportElement
						x="-1920"
						y="-30746"
						width="370"
						height="275"
						key="element-351"
						positionType="Float"/>
					<box></box>
						<chartLegend textColor="#000000" backgroundColor="#FFFFFF" >
					</chartLegend>
					</chart>
					<timeSeriesDataset timePeriod="Minute"
>
						<dataset resetType="Group" resetGroup="Group1" incrementType="Group" incrementGroup="Group2" >
						</dataset>
						<timeSeries>
							<seriesExpression><![CDATA["Credit"]]></seriesExpression>
							<timePeriodExpression><![CDATA[($F{INTERVAL_END_TIME} != null )? new java.util.Date($F{INTERVAL_END_TIME}):new java.util.Date(0)]]></timePeriodExpression>
							<valueExpression><![CDATA[$F{CREDIT_SUM}]]></valueExpression>
				<itemHyperlink >
				</itemHyperlink>
						</timeSeries>
						<timeSeries>
							<seriesExpression><![CDATA["Debit"]]></seriesExpression>
							<timePeriodExpression><![CDATA[($F{INTERVAL_END_TIME} != null )? new java.util.Date($F{INTERVAL_END_TIME}):new java.util.Date(0)]]></timePeriodExpression>
							<valueExpression><![CDATA[$F{DEBIT_SUM}]]></valueExpression>
				<itemHyperlink >
				</itemHyperlink>
						</timeSeries>
					</timeSeriesDataset>
					<timeSeriesPlot isShowShapes="false" >
						<plot >
							<seriesColor seriesOrder="0" color="#000000"/>
</plot>
					<timeAxisLabelExpression><![CDATA["Time"]]></timeAxisLabelExpression>
						<timeAxisFormat>
							<axisFormat axisLineColor="#330033" >
							</axisFormat>
						</timeAxisFormat>
					<valueAxisLabelExpression><![CDATA["Credit/Debit"]]></valueAxisLabelExpression>
						<valueAxisFormat>
							<axisFormat >
							</axisFormat>
						</valueAxisFormat>
					</timeSeriesPlot>
				</timeSeriesChart>
							</axis>
							<axis >
				<timeSeriesChart>
					<chart evaluationTime="Group" evaluationGroup="Group1"  hyperlinkTarget="Self" >
					<reportElement
						x="-1920"
						y="-30746"
						width="370"
						height="275"
						key="element-351"
						positionType="Float"/>
					<box></box>
						<chartLegend textColor="#000000" backgroundColor="#FFFFFF" >
					</chartLegend>
					</chart>
					<timeSeriesDataset timePeriod="Minute"
>
						<dataset resetType="Group" resetGroup="Group1" incrementType="Group" incrementGroup="Group2" >
						</dataset>
						<timeSeries>
							<seriesExpression><![CDATA["Balance"]]></seriesExpression>
							<timePeriodExpression><![CDATA[($F{INTERVAL_END_TIME} != null )? new java.util.Date($F{INTERVAL_END_TIME}):new java.util.Date(0)]]></timePeriodExpression>
							<valueExpression><![CDATA[$F{BALANCE}]]></valueExpression>
				<itemHyperlink >
				</itemHyperlink>
						</timeSeries>
					</timeSeriesDataset>
					<timeSeriesPlot isShowShapes="false" >
						<plot >
							<seriesColor seriesOrder="0" color="#0000FF"/>
</plot>
					<timeAxisLabelExpression><![CDATA["Time"]]></timeAxisLabelExpression>
						<timeAxisFormat>
							<axisFormat axisLineColor="#330033" >
							</axisFormat>
						</timeAxisFormat>
					<valueAxisLabelExpression><![CDATA["Balance"]]></valueAxisLabelExpression>
						<valueAxisFormat>
							<axisFormat labelColor="#3300CC" tickLabelColor="#3300CC" axisLineColor="#3300CC" >
							</axisFormat>
						</valueAxisFormat>
					</timeSeriesPlot>
				</timeSeriesChart>
							</axis>
					</multiAxisPlot>
				</multiAxisChart>
				<staticText>
					<reportElement
						x="27"
						y="48"
						width="59"
						height="23"
						key="staticText-7">
							<printWhenExpression><![CDATA[($F{SOD_BALANCE}!=null)?new Boolean(true):new Boolean(false)]]></printWhenExpression>
						</reportElement>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left" verticalAlignment="Top">
						<font pdfFontName="Helvetica-Bold" size="12" isBold="true"/>
					</textElement>
				<text><![CDATA[SOD:]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="97"
						y="48"
						width="162"
						height="23"
						key="textField-7"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left" verticalAlignment="Top">
						<font size="12"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[($F{SOD_BALANCE}!=null)?(
				($P{pCurrencyPattern}.equals("currencyPat1")) ?
				 new DecimalFormat("#,##0.00").format($F{SOD_BALANCE}):
 new DecimalFormat("#,##0.00").format($F{SOD_BALANCE}).replaceAll("\\.", "^").replaceAll(",", ".").replaceAll("\\^", ",")):""]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="27"
						y="13"
						width="59"
						height="23"
						key="staticText-8">
							<printWhenExpression><![CDATA[($F{ACCOUNT_ID}!=null) ?  new Boolean(true) :new Boolean(false)]]></printWhenExpression>
						</reportElement>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left" verticalAlignment="Top">
						<font pdfFontName="Helvetica-Bold" size="12" isBold="true"/>
					</textElement>
				<text><![CDATA[Account:]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="97"
						y="13"
						width="100"
						height="23"
						key="textField-8"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left" verticalAlignment="Top">
						<font size="12"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[($F{ACCOUNT_ID}!=null) ?  $F{ACCOUNT_ID} :""]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="false" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="241"
						y="13"
						width="381"
						height="23"
						key="textField-9"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left" verticalAlignment="Top">
						<font size="12"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[($F{ACCOUNT_NAME}!=null) ? $F{ACCOUNT_NAME} :""]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="330"
						y="71"
						width="165"
						height="23"
						forecolor="#000000"
						backcolor="#FFFFFF"
						key="staticText-10"
						positionType="Float">
							<printWhenExpression><![CDATA[($F{ACCOUNT_ID}==null) ?  new Boolean(true) :new Boolean(false)]]></printWhenExpression>
						</reportElement>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Center" verticalAlignment="Top">
						<font size="12"/>
					</textElement>
				<text><![CDATA[No records to display ]]></text>
				</staticText>
			</band>
		</pageHeader>
		<columnHeader>
			<band height="0"  splitType="Stretch" >
			</band>
		</columnHeader>
		<detail>
			<band height="0"  splitType="Stretch" >
			</band>
		</detail>
		<columnFooter>
			<band height="0"  splitType="Stretch" >
			</band>
		</columnFooter>
		<pageFooter>
			<band height="33"  splitType="Stretch" >
				<textField isStretchWithOverflow="false" pattern="" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="624"
						y="13"
						width="170"
						height="19"
						forecolor="#000000"
						backcolor="#FFFFFF"
						key="textField-5"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Right">
						<font size="12"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA["Page " + $V{PAGE_NUMBER} + " of "+" "]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="false" pattern="" isBlankWhenNull="false" evaluationTime="Report" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="794"
						y="13"
						width="36"
						height="19"
						forecolor="#000000"
						backcolor="#FFFFFF"
						key="textField-6"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement>
						<font size="12"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA["  " + $V{PAGE_NUMBER}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="false" pattern="" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="27"
						y="11"
						width="100"
						height="21"
						forecolor="#000000"
						backcolor="#FFFFFF"
						key="textField-12"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement>
						<font size="12"/>
					</textElement>
				<textFieldExpression   class="java.util.Date"><![CDATA[new Date()]]></textFieldExpression>
				</textField>
			</band>
		</pageFooter>
		<summary>
			<band height="33"  splitType="Stretch" >
				<staticText>
					<reportElement
						x="345"
						y="10"
						width="165"
						height="23"
						forecolor="#000000"
						backcolor="#FFFFFF"
						key="staticText-9"
						positionType="Float"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Center" verticalAlignment="Top">
						<font size="12"/>
					</textElement>
				<text><![CDATA[*** End of Report ***]]></text>
				</staticText>
			</band>
		</summary>
</jasperReport>