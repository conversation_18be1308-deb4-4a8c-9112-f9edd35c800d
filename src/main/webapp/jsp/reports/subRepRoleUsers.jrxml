<?xml version="1.0" encoding="UTF-8"  ?>
<!-- Created with iR<PERSON>ort - A designer for JasperReports -->
<!-- xml namespace changed,deprecated 'issplittype' attribute replaced with 'splitType' attribute for Mantis 2035 by <PERSON> on 18-Sep-2012 -->
<jasperReport 
  xmlns="http://jasperreports.sourceforge.net/jasperreports"
		 name="subRepRoleUsers"
		 columnCount="1"
		 printOrder="Vertical"
		 orientation="Portrait"
		 pageWidth="595"
		 pageHeight="842"
		 columnWidth="535"
		 columnSpacing="0"
		 leftMargin="30"
		 rightMargin="30"
		 topMargin="20"
		 bottomMargin="20"
		 whenNoDataType="NoPages"
		 isTitleNewPage="false"
		 isSummaryNewPage="false">
	<property name="ireport.scriptlethandling" value="0" />
	<property name="ireport.encoding" value="UTF-8" />
	<import value="java.util.*" />
	<import value="net.sf.jasperreports.engine.*" />
	<import value="net.sf.jasperreports.engine.data.*" />

	<parameter name="P_ROLE" isForPrompting="true" class="java.lang.String"/>
	<queryString><![CDATA[SELECT S_USERS.USER_ID,
       S_USERS.USER_NAME,
       S_USERS.SECTION_ID
FROM S_USERS
WHERE ROLE_ID =$P{P_ROLE}]]></queryString>

	<field name="USER_ID" class="java.lang.String"/>
	<field name="USER_NAME" class="java.lang.String"/>
	<field name="SECTION_ID" class="java.lang.String"/>

		<background>
			<band height="0"  splitType="Stretch" >
			</band>
		</background>
		<title>
			<band height="23"  splitType="Stretch" >
				<staticText>
					<reportElement
						x="70"
						y="0"
						width="160"
						height="20"
						key="staticText-1"/>
					<box></box>
					<textElement verticalAlignment="Middle">
						<font size="12"/>
					</textElement>
				<text><![CDATA[Users:]]></text>
				</staticText>
			</band>
		</title>
		<pageHeader>
			<band height="0"  splitType="Stretch" >
			</band>
		</pageHeader>
		<columnHeader>
			<band height="20"  splitType="Stretch" >
				<rectangle radius="0" >
					<reportElement
						mode="Opaque"
						x="70"
						y="1"
						width="465"
						height="17"
						forecolor="#000000"
						backcolor="#999999"
						key="element-22"/>
					<graphicElement stretchType="NoStretch">
					<pen lineWidth="0.25" lineStyle="Solid"/>
</graphicElement>
				</rectangle>
				<staticText>
					<reportElement
						x="70"
						y="1"
						width="108"
						height="16"
						forecolor="#FFFFFF"
						key="element-90"/>
					<box leftPadding="2" rightPadding="2">					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement>						
						<font fontName="SansSerif" size="12"/>
					</textElement>
				<text><![CDATA[User ID]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="178"
						y="1"
						width="178"
						height="16"
						forecolor="#FFFFFF"
						key="element-90"/>
					<box leftPadding="2" rightPadding="2">					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement>						
						<font fontName="SansSerif" size="12"/>
					</textElement>
				<text><![CDATA[Name]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="356"
						y="1"
						width="178"
						height="16"
						forecolor="#FFFFFF"
						key="element-90"/>
					<box leftPadding="2" rightPadding="2">					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement>						
						<font fontName="SansSerif" size="12"/>
					</textElement>
				<text><![CDATA[Section]]></text>
				</staticText>
			</band>
		</columnHeader>
		<detail>
			<band height="19"  splitType="Stretch" >
				<line direction="TopDown">
					<reportElement
						x="70"
						y="17"
						width="465"
						height="0"
						forecolor="#808080"
						key="line"
						positionType="FixRelativeToBottom"/>
					<graphicElement stretchType="NoStretch">
					<pen lineWidth="0.25" lineStyle="Solid"/>
</graphicElement>
				</line>
				<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="70"
						y="1"
						width="108"
						height="15"
						key="textField"/>
					<box leftPadding="2" rightPadding="2">					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement>						
						<font fontName="SansSerif" size="12"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{USER_ID}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="178"
						y="1"
						width="178"
						height="15"
						key="textField"/>
					<box leftPadding="2" rightPadding="2">					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement>						
						<font fontName="SansSerif" size="12"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{USER_NAME}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="356"
						y="1"
						width="178"
						height="15"
						key="textField"/>
					<box leftPadding="2" rightPadding="2">					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement>						
						<font fontName="SansSerif" size="12"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{SECTION_ID}]]></textFieldExpression>
				</textField>
			</band>
		</detail>
		<columnFooter>
			<band height="0"  splitType="Stretch" >
			</band>
		</columnFooter>
		<pageFooter>
			<band height="0"  splitType="Stretch" >
			</band>
		</pageFooter>
		<summary>
			<band height="0"  splitType="Stretch" >
			</band>
		</summary>
</jasperReport>
