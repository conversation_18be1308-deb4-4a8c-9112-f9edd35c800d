<?xml version="1.0" encoding="UTF-8"  ?>
<!-- Created with iReport - A designer for JasperReports -->
<!-- xml namespace changed,deprecated 'issplittype' attribute replaced with 'splitType' attribute for Mantis 2035 by <PERSON> on 18-Sep-2012 -->
<jasperReport 
  xmlns="http://jasperreports.sourceforge.net/jasperreports"
		 name="RoleReport"
		 columnCount="1"
		 printOrder="Vertical"
		 orientation="Portrait"
		 pageWidth="595"
		 pageHeight="842"
		 columnWidth="535"
		 columnSpacing="0"
		 leftMargin="30"
		 rightMargin="30"
		 topMargin="20"
		 bottomMargin="20"
		 whenNoDataType="NoPages"
		 isTitleNewPage="false"
		 isSummaryNewPage="false">
	<property name="ireport.scriptlethandling" value="0" />
	<property name="ireport.encoding" value="UTF-8" />
	<import value="java.util.*" />
	<import value="net.sf.jasperreports.engine.*" />
	<import value="net.sf.jasperreports.engine.data.*" />

	<parameter name="P_ROLE" isForPrompting="true" class="java.lang.String"/>
	<parameter name="SUBREPORT_DIR" isForPrompting="true" class="java.lang.String">
		<defaultValueExpression ><![CDATA["C:\\Program Files\\JasperSoft\\iReport-1.3.3\\stl_compiled\\"]]></defaultValueExpression>
	</parameter>	
	<queryString><![CDATA[SELECT S_ROLE.HOST_ID,
       S_ROLE.ROLE_ID,
       S_ROLE.ROLE_NAME,
       DECODE(S_ROLE.ALERT_TYPE,0,'Both',1,'Email',2,'Pop-up','') ALERT_TYPE,
       DECODE(S_ROLE.AUTHORIZE_INPUT,'Y','Yes','No') AUTHORIZE_INPUT,
       DECODE(S_ROLE.RESTRICT_LOCATIONS,'Y','Yes','No') RESTRICT_LOCATIONS,
       DECODE(S_ROLE.ALL_ENTITY_OPTION,'Y','Yes','No') ALL_ENTITY_OPTION,
       DECODE(S_ROLE.INTERFACE_INTERRUPTION,'Y','Yes','No') INTERFACE_INTERRUPTION,
       DECODE(S_ROLE.ADVANCED_USER,'Y','Yes','No') ADVANCED_USER,      
	   DECODE(S_ROLE.ACCOUNT_ACCESS_CONTROL,'Y','Yes','No') ACCOUNT_ACCESS_CONTROL,	    
	   DECODE(S_ROLE.MAINTAIN_ANY_ILM_SCENARIO,'Y','Yes','No') MAINTAIN_ANY_ILM_SCENARIO,    
	   DECODE(S_ROLE.MAINTAIN_ANY_ILM_GROUP,'Y','Yes','No') MAINTAIN_ANY_ILM_GROUP	    
FROM S_ROLE
WHERE ($P{P_ROLE} IS NULL OR S_ROLE.ROLE_ID = $P{P_ROLE})ORDER BY ROLE_ID]]></queryString>	

	<field name="HOST_ID" class="java.lang.String"/>
	<field name="ROLE_ID" class="java.lang.String"/>
	<field name="ROLE_NAME" class="java.lang.String"/>
	<field name="ALERT_TYPE" class="java.lang.String"/>
	<field name="AUTHORIZE_INPUT" class="java.lang.String"/>
	<field name="RESTRICT_LOCATIONS" class="java.lang.String"/>
	<field name="ALL_ENTITY_OPTION" class="java.lang.String"/>
	<field name="INTERFACE_INTERRUPTION" class="java.lang.String"/>
	<field name="ADVANCED_USER" class="java.lang.String"/>	
	<field name="ACCOUNT_ACCESS_CONTROL" class="java.lang.String"/>
	<field name="MAINTAIN_ANY_ILM_SCENARIO" class="java.lang.String"/>
	<field name="MAINTAIN_ANY_ILM_GROUP" class="java.lang.String"/>
		<background>
			<band height="0"  splitType="Stretch" >
			</band>
		</background>
		<title>
			<band height="73"  splitType="Stretch" >
				<staticText>
					<reportElement
						x="61"
						y="5"
						width="412"
						height="40"
						forecolor="#000000"
						backcolor="#FFFFFF"
						key="staticText"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Center">
						<font size="30"/>
					</textElement>
				<text><![CDATA[Role Report]]></text>
				</staticText>
				<line direction="TopDown">
					<reportElement
						x="0"
						y="48"
						width="534"
						height="0"
						key="line"/>
					<graphicElement stretchType="NoStretch"/>
				</line>
				<line direction="TopDown">
					<reportElement
						x="0"
						y="3"
						width="534"
						height="0"
						key="line"/>
					<graphicElement stretchType="NoStretch"/>
				</line>
				<textField isStretchWithOverflow="false" pattern="dd-MMM-yyyy" isBlankWhenNull="false" evaluationTime="Report" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="450"
						y="50"
						width="83"
						height="20"
						key="textField-1"/>
					<box></box>
					<textElement verticalAlignment="Middle">
						<font size="10"/>
					</textElement>
				<textFieldExpression   class="java.util.Date"><![CDATA[new java.util.Date()]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="360"
						y="50"
						width="90"
						height="20"
						key="staticText-1"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font pdfFontName="Helvetica-Bold" size="12" isBold="true"/>
					</textElement>
				<text><![CDATA[Report Date:]]></text>
				</staticText>
			</band>
		</title>
		<pageHeader>
			<band height="0"  splitType="Stretch" >
			</band>
		</pageHeader>
		<columnHeader>
			<band height="0"  splitType="Stretch" >
			</band>
		</columnHeader>
		<detail>
			<band height="368"  splitType="Stretch" >
				<textField isStretchWithOverflow="false" pattern="" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						mode="Opaque"
						x="0"
						y="3"
						width="530"
						height="18"
						forecolor="#FFFFFF"
						backcolor="#000000"
						key="textField"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement>
						<font pdfFontName="Helvetica-Bold" size="14" isBold="true"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{ROLE_ID}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="false" pattern="" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="170"
						y="26"
						width="358"
						height="17"
						forecolor="#000000"
						backcolor="#FFFFFF"
						key="textField"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement>
						<font size="12"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{ROLE_NAME}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="3"
						y="26"
						width="157"
						height="17"
						forecolor="#000000"
						backcolor="#FFFFFF"
						key="staticText"
						positionType="Float"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement>
						<font size="12"/>
					</textElement>
				<text><![CDATA[Name:]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" pattern="" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="170"
						y="46"
						width="358"
						height="17"
						forecolor="#000000"
						backcolor="#FFFFFF"
						key="textField"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement>
						<font size="12"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{ALERT_TYPE}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="3"
						y="46"
						width="157"
						height="17"
						forecolor="#000000"
						backcolor="#FFFFFF"
						key="staticText"
						positionType="Float"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement>
						<font size="12"/>
					</textElement>
				<text><![CDATA[Alert Type:]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" pattern="" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="170"
						y="66"
						width="358"
						height="17"
						forecolor="#000000"
						backcolor="#FFFFFF"
						key="textField"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement>
						<font size="12"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{AUTHORIZE_INPUT}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="3"
						y="66"
						width="157"
						height="17"
						forecolor="#000000"
						backcolor="#FFFFFF"
						key="staticText"
						positionType="Float"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement>
						<font size="12"/>
					</textElement>
				<text><![CDATA[Authorise Manual input:]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" pattern="" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="170"
						y="180"
						width="355"
						height="17"
						forecolor="#000000"
						backcolor="#FFFFFF"
						key="textField"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement>
						<font size="12"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{RESTRICT_LOCATIONS}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="3"
						y="180"
						width="157"
						height="17"
						forecolor="#000000"
						backcolor="#FFFFFF"
						key="staticText"
						positionType="Float"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement>
						<font size="12"/>
					</textElement>
				<text><![CDATA[Location Restriction:]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" pattern="" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="170"
						y="100"
						width="358"
						height="17"
						forecolor="#000000"
						backcolor="#FFFFFF"
						key="textField"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement>
						<font size="12"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{ALL_ENTITY_OPTION}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="3"
						y="86"
						width="157"
						height="31"
						forecolor="#000000"
						backcolor="#FFFFFF"
						key="staticText"
						positionType="Float"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement>
						<font size="12"/>
					</textElement>
				<text><![CDATA['All' Entity Selection in Monitors:]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" pattern="" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="170"
						y="137"
						width="358"
						height="17"
						forecolor="#000000"
						backcolor="#FFFFFF"
						key="textField"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement>
						<font size="12"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{INTERFACE_INTERRUPTION}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="3"
						y="120"
						width="157"
						height="17"
						forecolor="#000000"
						backcolor="#FFFFFF"
						key="staticText"
						positionType="Float"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement>
						<font size="12"/>
					</textElement>
				<text><![CDATA[Notification -]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" pattern="" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="170"
						y="160"
						width="358"
						height="17"
						forecolor="#000000"
						backcolor="#FFFFFF"
						key="textField"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement>
						<font size="12"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{ADVANCED_USER}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="3"
						y="160"
						width="157"
						height="17"
						forecolor="#000000"
						backcolor="#FFFFFF"
						key="staticText"
						positionType="Float"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement>
						<font size="12"/>
					</textElement>
				<text><![CDATA[Advanced User Config:]]></text>
				</staticText>
				
				
				<textField isStretchWithOverflow="false" pattern="" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="170"
						y="220"
						width="358"
						height="17"
						forecolor="#000000"
						backcolor="#FFFFFF"
						key="textField"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement>
						<font size="12"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{MAINTAIN_ANY_ILM_SCENARIO}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="3"
						y="220"
						width="157"
						height="17"
						forecolor="#000000"
						backcolor="#FFFFFF"
						key="staticText"
						positionType="Float"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement>
						<font size="12"/>
					</textElement>
				<text><![CDATA[Maintain Any ILM Scenario:]]></text>
				</staticText>
				
				
				
				
				<textField isStretchWithOverflow="false" pattern="" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="170"
						y="240"
						width="358"
						height="17"
						forecolor="#000000"
						backcolor="#FFFFFF"
						key="textField"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement>
						<font size="12"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{MAINTAIN_ANY_ILM_GROUP}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="3"
						y="240"
						width="157"
						height="17"
						forecolor="#000000"
						backcolor="#FFFFFF"
						key="staticText"
						positionType="Float"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement>
						<font size="12"/>
					</textElement>
				<text><![CDATA[Maintain Any ILM Account Group:]]></text>
				</staticText>
				
				
				<staticText>
					<reportElement
						x="3"
						y="140"
						width="157"
						height="17"
						forecolor="#000000"
						backcolor="#FFFFFF"
						key="staticText-2"
						positionType="Float"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement verticalAlignment="Top">
						<font size="12"/>
					</textElement>
				<text><![CDATA[Interface Interruption:]]></text>
				</staticText>
				<subreport  isUsingCache="true">
					<reportElement
						x="-40"
						y="275"
						width="590"
						height="20"
						key="subreport-1"
						positionType="Float"/>
					<subreportParameter  name="P_ROLE">
						<subreportParameterExpression><![CDATA[$F{ROLE_ID}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter  name="P_RESTRICT_LOCATIONS">
						<subreportParameterExpression><![CDATA[$F{RESTRICT_LOCATIONS}]]></subreportParameterExpression>
					</subreportParameter>
					<connectionExpression><![CDATA[$P{REPORT_CONNECTION}]]></connectionExpression>
					<subreportExpression  class="java.lang.String"><![CDATA[$P{SUBREPORT_DIR} + "subRepRoleLocationAccess.jasper"]]></subreportExpression>
				</subreport>
				<subreport  isUsingCache="true">
					<reportElement
						x="-40"
						y="252"
						width="590"
						height="20"
						key="subreport-2"
						positionType="Float"/>
					<subreportParameter  name="P_ROLE">
						<subreportParameterExpression><![CDATA[$F{ROLE_ID}]]></subreportParameterExpression>
					</subreportParameter>
					<connectionExpression><![CDATA[$P{REPORT_CONNECTION}]]></connectionExpression>
					<subreportExpression  class="java.lang.String"><![CDATA[$P{SUBREPORT_DIR} + "subRepRoleCcyAccess.jasper"]]></subreportExpression>
				</subreport>
				<subreport  isUsingCache="true">
					<reportElement
						x="-40"
						y="231"
						width="590"
						height="20"
						key="subreport-3"
						positionType="Float"/>
					<subreportParameter  name="P_ROLE">
						<subreportParameterExpression><![CDATA[$F{ROLE_ID}]]></subreportParameterExpression>
					</subreportParameter>
					<connectionExpression><![CDATA[$P{REPORT_CONNECTION}]]></connectionExpression>
					<subreportExpression  class="java.lang.String"><![CDATA[$P{SUBREPORT_DIR} + "subRepRoleEntityAccess.jasper"]]></subreportExpression>
				</subreport>
				<subreport  isUsingCache="true">
					<reportElement
						x="-40"
						y="298"
						width="590"
						height="20"
						key="subreport-4"
						positionType="Float"/>
					<subreportParameter  name="P_ROLE">
						<subreportParameterExpression><![CDATA[$F{ROLE_ID}]]></subreportParameterExpression>
					</subreportParameter>
					<connectionExpression><![CDATA[$P{REPORT_CONNECTION}]]></connectionExpression>
					<subreportExpression  class="java.lang.String"><![CDATA[$P{SUBREPORT_DIR} + "subRepRoleSweepLimits.jasper"]]></subreportExpression>
				</subreport>
				<subreport  isUsingCache="true">
					<reportElement
						x="-40"
						y="321"
						width="590"
						height="20"
						key="subreport-5"
						positionType="Float"/>
					<subreportParameter  name="P_ROLE">
						<subreportParameterExpression><![CDATA[$F{ROLE_ID}]]></subreportParameterExpression>
					</subreportParameter>
					<connectionExpression><![CDATA[$P{REPORT_CONNECTION}]]></connectionExpression>
					<subreportExpression  class="java.lang.String"><![CDATA[$P{SUBREPORT_DIR} + "subRepRoleUsers.jasper"]]></subreportExpression>
				</subreport>
				<subreport  isUsingCache="true">
					<reportElement
						x="-40"
						y="346"
						width="590"
						height="20"
						key="subreport-6"
						positionType="Float"/>
					<subreportParameter  name="P_ROLE">
						<subreportParameterExpression><![CDATA[$F{ROLE_ID}]]></subreportParameterExpression>
					</subreportParameter>
					<connectionExpression><![CDATA[$P{REPORT_CONNECTION}]]></connectionExpression>
					<subreportExpression  class="java.lang.String"><![CDATA[$P{SUBREPORT_DIR} + "subRepRoleMenuAccess.jasper"]]></subreportExpression>
				</subreport>				
				<textField isStretchWithOverflow="false" pattern="" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="170"
						y="200"
						width="355"
						height="17"
						forecolor="#000000"
						backcolor="#FFFFFF"
						key="textField-3"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement>
						<font size="12"/>
					</textElement>
				
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{ACCOUNT_ACCESS_CONTROL}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="3"
						y="200"
						width="157"
						height="17"
						forecolor="#000000"
						backcolor="#FFFFFF"
						key="staticText-5"
						positionType="Float"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement>
						<font size="12"/>
					</textElement>
				<text><![CDATA[Account Access Control]]></text>
				</staticText>
				
			</band>
		</detail>
		<columnFooter>
			<band height="0"  splitType="Stretch" >
			</band>
		</columnFooter>
		<pageFooter>
			<band height="27"  splitType="Stretch" >
				<textField isStretchWithOverflow="false" pattern="" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="325"
						y="4"
						width="170"
						height="19"
						forecolor="#000000"
						backcolor="#FFFFFF"
						key="textField"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Right">
						<font size="10"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA["Page " + $V{PAGE_NUMBER} + " of "]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="false" pattern="" isBlankWhenNull="false" evaluationTime="Report" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="499"
						y="4"
						width="36"
						height="19"
						forecolor="#000000"
						backcolor="#FFFFFF"
						key="textField"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement>
						<font size="10"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA["" + $V{PAGE_NUMBER}]]></textFieldExpression>
				</textField>
				<line direction="TopDown">
					<reportElement
						x="0"
						y="3"
						width="535"
						height="0"
						key="line"/>
					<graphicElement stretchType="NoStretch"/>
				</line>
				<textField isStretchWithOverflow="false" pattern="" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="1"
						y="6"
						width="209"
						height="19"
						forecolor="#000000"
						backcolor="#FFFFFF"
						key="textField"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement>
						<font size="10"/>
					</textElement>
				<textFieldExpression   class="java.util.Date"><![CDATA[new Date()]]></textFieldExpression>
				</textField>
			</band>
		</pageFooter>
		<summary>
			<band height="48"  splitType="Stretch" >
				<staticText>
					<reportElement
						x="160"
						y="26"
						width="188"
						height="20"
						key="staticText-3"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Center">
						<font size="14"/>
					</textElement>
				<text><![CDATA[*** End of Report ***]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="490"
						y="5"
						width="40"
						height="20"
						key="textField-2"/>
					<box></box>
					<textElement>
						<font/>
					</textElement>
				<textFieldExpression   class="java.lang.Integer"><![CDATA[$V{REPORT_COUNT}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="370"
						y="5"
						width="110"
						height="20"
						key="staticText-4"/>
					<box></box>
					<textElement textAlignment="Right">
						<font/>
					</textElement>
				<text><![CDATA[Total Roles:]]></text>
				</staticText>
			</band>
		</summary>
</jasperReport>
