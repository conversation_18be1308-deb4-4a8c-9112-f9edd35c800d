<%@ page import="com.crystaldecisions.report.web.viewer.ReportExportControl" %>
<%@ page import="com.crystaldecisions.reports.reportengineinterface.JPEReportSourceFactory" %>
<%@ page import="com.crystaldecisions.sdk.occa.report.reportsource.IReportSourceFactory2"%>
<%@ page import="com.crystaldecisions.sdk.occa.report.reportsource.IReportSource"%>
<%@ page import="com.crystaldecisions.sdk.occa.report.exportoptions.ExportOptions"%>
<%@ page import="com.crystaldecisions.sdk.occa.report.exportoptions.ReportExportFormat" %>
<%@ page import="com.crystaldecisions.sdk.occa.report.exportoptions.IRTFWordExportFormatOptions" %>
<%@ page import="com.crystaldecisions.sdk.occa.report.exportoptions.PageBasedExportFormatOptions" %>
<%@ page  import="com.crystaldecisions.sdk.occa.report.exportoptions.PDFExportFormatOptions" %>
<%@ page import="com.crystaldecisions.reports.sdk.ReportClientDocument" %>
<%@ page import="com.crystaldecisions.sdk.occa.report.lib.*" %>
<%@ page import="com.crystaldecisions.sdk.occa.report.data.*"%>
<%@ page import="com.crystaldecisions.sdk.occa.report.lib.PropertyBag" %>
<%@ page import="java.io.*" %>
<%@ page import="java.util.*"%>
<%@page import="com.crystaldecisions.reports.sdk.*"%>
<%@ page import="org.swallow.reports.model.Reports" %>
<%@ page import="org.swallow.util.CommonDataManager" %>
<%@ page import="javax.sql.DataSource"%>
<%@ page import="org.swallow.util.SwtUtil"%>
<%@ page import="java.sql.DatabaseMetaData" %>
<%@ page import="java.sql.DriverManager" %>
<%@ page import="org.apache.commons.dbcp.BasicDataSource" %>
 <%
String reportName = (String)request.getAttribute("reportName");
System.out.println("report============"+reportName);
String userName = (String)request.getAttribute("userName");
System.out.println("userName============"+userName);
String date1 = (String)request.getAttribute("date1");
System.out.println("date1============"+date1);
String date2 = (String)request.getAttribute("date2");
System.out.println("date2============"+date2);

CommonDataManager cdm = (CommonDataManager)request.getSession().getAttribute("CDM");
System.out.println("CDM: "+cdm);
java.text.SimpleDateFormat sdf = new java.text.SimpleDateFormat(cdm.getDateFormatValue());
Date dateFrom = sdf.parse(date1);
System.out.println("dateFrom========="+dateFrom);
Date dateTo = sdf.parse(date2);
System.out.println("dateTo=========="+dateTo);

BasicDataSource dataSource = (BasicDataSource)SwtUtil.getBean("dataSource");

System.out.println("dataSource driver=========="+dataSource.getDriverClassName());
System.out.println("dataSource url=========="+dataSource.getUrl());
System.out.println("dataSource username=========="+dataSource.getUsername());
System.out.println("dataSource password=========="+dataSource.getPassword());
IReportSourceFactory2 rptSrcFactory = new JPEReportSourceFactory();
IReportSource reportSource = (IReportSource)
rptSrcFactory.createReportSource(reportName, request.getLocale());
  
ConnectionInfo ci = new ConnectionInfo();
PropertyBag innerPropertyBagObj = ci.getAttributes(); 
//set the ConnectionInfo Attribute properties with their new values 

innerPropertyBagObj.putStringValue("Connection URL", dataSource.getUrl());
innerPropertyBagObj.putStringValue("Database Class Name", dataSource.getDriverClassName());  
ci.setUserName(dataSource.getUsername());
ci.setPassword(dataSource.getPassword());
ConnectionInfos connections = new ConnectionInfos();
connections.add(ci);
System.out.println("got connection============");

Fields fields = new Fields();

ParameterField  pfield1 = new ParameterField();
ParameterField  pfield2 = new ParameterField ();
ParameterField  pfield3 = new ParameterField ();
Values vals1 = new Values();
Values vals2 = new Values();
Values vals3 = new Values();
ParameterFieldDiscreteValue pfieldsDV1=new ParameterFieldDiscreteValue();
ParameterFieldDiscreteValue pfieldsDV2=new ParameterFieldDiscreteValue();
ParameterFieldDiscreteValue pfieldsDV3=new ParameterFieldDiscreteValue();
pfield1.setReportName("");

// set name given in the Crystal report 
pfield1.setName("user"); // first parameter
pfield2.setName("Date1"); // second parameter
pfield3.setName("Date2"); // third parameter

// set the value getting from jsp
pfieldsDV1.setValue(userName);
pfieldsDV2.setValue(dateFrom);
pfieldsDV3.setValue(dateTo);

// add parameter to value Object 
vals1.add(pfieldsDV1);
vals2.add(pfieldsDV2);
vals3.add(pfieldsDV3);

// set the current value to parameter 
pfield1.setCurrentValues(vals1);
pfield2.setCurrentValues(vals2);
pfield3.setCurrentValues(vals3);

 // add parameter in field 
fields.add(pfield1);
fields.add(pfield2);
fields.add(pfield3); 
 
ReportExportControl exportControl = new ReportExportControl();
exportControl.setDatabaseLogonInfos(connections);

ExportOptions exportOptions = new ExportOptions();
exportOptions.setExportFormatType(ReportExportFormat.PDF);
PDFExportFormatOptions PDFExpOpts = new PDFExportFormatOptions();

PDFExpOpts.setStartPageNumber(1);
PDFExpOpts.setEndPageNumber(3);
exportControl.setParameterFields(fields);
exportOptions.setFormatOptions(PDFExpOpts);
exportControl.setReportSource(reportSource);
exportControl.setExportOptions(exportOptions);
exportControl.setExportAsAttachment(true);
exportControl.processHttpRequest(request, response,
getServletConfig().getServletContext(), out);
exportControl.dispose();
%>