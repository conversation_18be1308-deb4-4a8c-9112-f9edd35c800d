<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.12.2.final using JasperReports Library version 6.12.2-75c5e90a222ab406e416cbf590a5397028a52de3  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="ILMReportBaselCDateRange" pageWidth="842" pageHeight="595" orientation="Landscape" columnWidth="800" leftMargin="10" rightMargin="10" topMargin="10" bottomMargin="10" uuid="e1800ea8-8684-4f93-83c5-729e98b638fa">
	<property name="ireport.scriptlethandling" value="0"/>
	<property name="ireport.encoding" value="UTF-8"/>
	<property name="ireport.zoom" value="1.0"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<import value="net.sf.jasperreports.engine.*"/>
	<import value="java.util.*"/>
	<import value="net.sf.jasperreports.engine.data.*"/>
	<parameter name="pHost_Id" class="java.lang.String"/>
	<parameter name="pEntity_Id" class="java.lang.String"/>
	<parameter name="pCurrency_Code" class="java.lang.String"/>
	<parameter name="pDBLink" class="java.lang.String"/>
	<parameter name="pRoleId" class="java.lang.String"/>
	<parameter name="pDateFormat" class="java.lang.String"/>
	<parameter name="pCurrencyPattern" class="java.lang.String"/>
	<parameter name="pSelectedCostumerPayments" class="java.lang.String"/>
	<parameter name="pLoroPayments" class="java.lang.String"/>
	<parameter name="pCorpPayments" class="java.lang.String"/>
	<parameter name="pOtherPayments" class="java.lang.String"/>
	<parameter name="pBranchPayments" class="java.lang.String"/>
	<parameter name="pCheckBoxes" class="java.lang.String"/>
	<parameter name="pUseCcyMultiplier" class="java.lang.String"/>
	<parameter name="pValue_Date" class="java.util.Date"/>
	<parameter name="pValue_DateEnd" class="java.util.Date"/>
	<parameter name="pDictionary_Data" class="java.util.HashMap"/>
	<parameter name="pIlmUtil" class="org.swallow.reports.dao.hibernate.ReportsDAOHibernate.ILMReportUtils"/>
	<queryString>
		<![CDATA[WITH headers AS
                        (SELECT  HOST_ID, ENTITY_ID, ENTITY_NAME, CCY, CCY_NAME,
                                    DECODE ($P{pUseCcyMultiplier}, 'Y', CCY_MULTIPLIER, 'N' ) CCY_MULTIPLIER,
                                    DECODE ($P{pUseCcyMultiplier}, 'Y', CCY_MULTIPLIER_VALUE, 1 ) CCY_MULTIPLIER_VALUE,
                                    MISSING_D_START,MISSING_D_END,total_rows
                               FROM TABLE (pkg_ilm_rep.fn_header_baselc (
                                                           $P{pHost_Id},
                                                          $P{pEntity_Id},
                                                          $P{pCurrency_Code},
                                                          $P{pRoleId},
                                                          $P{pDBLink},
                                                        $P{pValue_Date},
                                                        $P{pValue_DateEnd} 
                                          ))
                         )

                SELECT     headers.ENTITY_ID,ENTITY_NAME,headers.CCY,CCY_NAME,
                        data.MAX1_CUSTOMER_PAYMENTS /CCY_MULTIPLIER_VALUE         AS MAX1_CUSTOMER_PAYMENTS ,
                        data.MAX_CREDIT_LINE_TOTAL /CCY_MULTIPLIER_VALUE        AS MAX_CREDIT_LINE_TOTAL,
                        data.MAX1_CREDIT_LINE_SECURED /CCY_MULTIPLIER_VALUE      AS MAX1_CREDIT_LINE_SECURED ,
                        data.MAX1_CREDIT_LINE_COMMITTED /CCY_MULTIPLIER_VALUE    AS MAX1_CREDIT_LINE_COMMITTED,
                        data.MAX1_PEAK_CREDIT_LINE_USED /CCY_MULTIPLIER_VALUE   AS MAX1_PEAK_CREDIT_LINE_USED,

                        data.MAX2_CUSTOMER_PAYMENTS /CCY_MULTIPLIER_VALUE         AS MAX2_CUSTOMER_PAYMENTS ,
                        data.MAX2_CREDIT_LINE_TOTAL /CCY_MULTIPLIER_VALUE        AS MAX2_CREDIT_LINE_TOTAL,
                        data.MAX2_CREDIT_LINE_SECURED /CCY_MULTIPLIER_VALUE      AS MAX2_CREDIT_LINE_SECURED ,
                        data.MAX2_CREDIT_LINE_COMMITTED /CCY_MULTIPLIER_VALUE    AS MAX2_CREDIT_LINE_COMMITTED,
                        data.MAX2_PEAK_CREDIT_LINE_USED /CCY_MULTIPLIER_VALUE    AS  MAX2_PEAK_CREDIT_LINE_USED,

                        data.MAX3_CUSTOMER_PAYMENTS /CCY_MULTIPLIER_VALUE         AS MAX3_CUSTOMER_PAYMENTS ,
                        data.MAX3_CREDIT_LINE_TOTAL /CCY_MULTIPLIER_VALUE        AS MAX3_CREDIT_LINE_TOTAL,
                        data.MAX3_CREDIT_LINE_SECURED /CCY_MULTIPLIER_VALUE      AS MAX3_CREDIT_LINE_SECURED ,
                        data.MAX3_CREDIT_LINE_COMMITTED /CCY_MULTIPLIER_VALUE    AS MAX3_CREDIT_LINE_COMMITTED,
                        data.MAX3_PEAK_CREDIT_LINE_USED /CCY_MULTIPLIER_VALUE   AS MAX3_PEAK_CREDIT_LINE_USED,

                        data.AVG_CUSTOMER_PAYMENTS /CCY_MULTIPLIER_VALUE         AS AVG_CUSTOMER_PAYMENTS ,
                        data.AVG_CREDIT_LINE_TOTAL /CCY_MULTIPLIER_VALUE        AS AVG_CREDIT_LINE_TOTAL,
                        data.AVG_CREDIT_LINE_SECURED /CCY_MULTIPLIER_VALUE      AS AVG_CREDIT_LINE_SECURED ,
                        data.AVG_CREDIT_LINE_COMMITTED /CCY_MULTIPLIER_VALUE    AS AVG_CREDIT_LINE_COMMITTED,
                        data.AVG_PEAK_CREDIT_LINE_USED /CCY_MULTIPLIER_VALUE   AS AVG_PEAK_CREDIT_LINE_USED,

                        DECODE (data.entity_id, NULL, 'NO_DATA', 'OK')             DATA_STATUS,
                        CCY_MULTIPLIER,
                        to_char(MAX1_PAYMENTS_DATE,upper($P{pDateFormat})) AS MAX1_PAYMENTS_DATE,
                        to_char(MAX2_PAYMENTS_DATE,upper($P{pDateFormat})) AS MAX2_PAYMENTS_DATE,
                        to_char(MAX3_PAYMENTS_DATE,upper($P{pDateFormat})) AS MAX3_PAYMENTS_DATE,
                        to_char(MAX1_CREDIT_LINE_DATE,upper($P{pDateFormat})) AS MAX1_CREDIT_LINE_DATE,
                        to_char(MAX2_CREDIT_LINE_DATE,upper($P{pDateFormat})) AS MAX2_CREDIT_LINE_DATE,
                        to_char(MAX3_CREDIT_LINE_DATE,upper($P{pDateFormat})) AS MAX3_CREDIT_LINE_DATE,
                        ROWNUM CURRENT_ROW,
                        total_rows,
                        MISSING_D_START,
                        MISSING_D_END,
                        --
                        data.MAX1_LORO_CLIENT_PAYMENTS /CCY_MULTIPLIER_VALUE         AS MAX1_LORO_CLIENT_PAYMENTS,
                        data.MAX1_CORP_CLIENT_PAYMENTS /CCY_MULTIPLIER_VALUE        AS MAX1_CORP_CLIENT_PAYMENTS,
                        data.MAX1_OWN_ENTITY_PAYMENTS /CCY_MULTIPLIER_VALUE      AS MAX1_OWN_ENTITY_PAYMENTS,
                        data.MAX1_OTHER_PAYMENTS /CCY_MULTIPLIER_VALUE    AS MAX1_OTHER_PAYMENTS,
                        data.MAX1_BRANCH_PAYMENTS /CCY_MULTIPLIER_VALUE   AS MAX1_BRANCH_PAYMENTS,
                        
                        data.MAX2_LORO_CLIENT_PAYMENTS /CCY_MULTIPLIER_VALUE         AS MAX2_LORO_CLIENT_PAYMENTS,
                        data.MAX2_CORP_CLIENT_PAYMENTS /CCY_MULTIPLIER_VALUE        AS MAX2_CORP_CLIENT_PAYMENTS,
                        data.MAX2_OWN_ENTITY_PAYMENTS /CCY_MULTIPLIER_VALUE      AS MAX2_OWN_ENTITY_PAYMENTS,
                        data.MAX2_OTHER_PAYMENTS /CCY_MULTIPLIER_VALUE    AS MAX2_OTHER_PAYMENTS,
                        data.MAX2_BRANCH_PAYMENTS /CCY_MULTIPLIER_VALUE   AS MAX2_BRANCH_PAYMENTS,
                        
                        data.MAX3_LORO_CLIENT_PAYMENTS /CCY_MULTIPLIER_VALUE         AS MAX3_LORO_CLIENT_PAYMENTS,
                        data.MAX3_CORP_CLIENT_PAYMENTS /CCY_MULTIPLIER_VALUE        AS MAX3_CORP_CLIENT_PAYMENTS,
                        data.MAX3_OWN_ENTITY_PAYMENTS /CCY_MULTIPLIER_VALUE      AS MAX3_OWN_ENTITY_PAYMENTS,
                        data.MAX3_OTHER_PAYMENTS /CCY_MULTIPLIER_VALUE    AS MAX3_OTHER_PAYMENTS,
                        data.MAX3_BRANCH_PAYMENTS /CCY_MULTIPLIER_VALUE   AS MAX3_BRANCH_PAYMENTS,
                        
                        data.AVG_LORO_CLIENT_PAYMENTS /CCY_MULTIPLIER_VALUE         AS AVG_LORO_CLIENT_PAYMENTS,
                        data.AVG_CORP_CLIENT_PAYMENTS /CCY_MULTIPLIER_VALUE        AS AVG_CORP_CLIENT_PAYMENTS,
                        data.AVG_OWN_ENTITY_PAYMENTS /CCY_MULTIPLIER_VALUE      AS AVG_OWN_ENTITY_PAYMENTS ,
                        data.AVG_OTHER_PAYMENTS /CCY_MULTIPLIER_VALUE    AS AVG_OTHER_PAYMENTS,
                        data.AVG_BRANCH_PAYMENTS /CCY_MULTIPLIER_VALUE   AS AVG_BRANCH_PAYMENTS,
                        data.MAX1_TOTAL_PAYMENTS /CCY_MULTIPLIER_VALUE   AS MAX1_TOTAL_PAYMENTS,
                        data.MAX2_TOTAL_PAYMENTS /CCY_MULTIPLIER_VALUE   AS MAX2_TOTAL_PAYMENTS,
                        data.MAX3_TOTAL_PAYMENTS /CCY_MULTIPLIER_VALUE   AS MAX3_TOTAL_PAYMENTS,
                        data.AVG_TOTAL_PAYMENTS /CCY_MULTIPLIER_VALUE   AS AVG_TOTAL_PAYMENTS,
                        to_char(max1_tot_pay_Date,upper($P{pDateFormat})) AS max1_tot_pay_Date,
                        to_char(max2_tot_pay_Date,upper($P{pDateFormat})) AS max2_tot_pay_Date,
                        to_char(max3_tot_pay_Date,upper($P{pDateFormat})) AS max3_tot_pay_Date
                  FROM headers

                  LEFT OUTER JOIN TABLE (pkg_ilm_rep.fn_main_data_baselc(headers.host_id,
                                                               headers.entity_id,
                                                               headers.ccy,
                                                               $P{pValue_Date},
                                                               $P{pValue_DateEnd},
                                                               $P{pDBLink},
                                                               $P{pCheckBoxes}
                                                              )) data
                  ON (headers.entity_id = data.entity_id AND headers.ccy = data.ccy)]]>
	</queryString>
	<field name="ENTITY_ID" class="java.lang.String"/>
	<field name="ENTITY_NAME" class="java.lang.String"/>
	<field name="CCY" class="java.lang.String"/>
	<field name="CCY_NAME" class="java.lang.String"/>
	<field name="CCY_MULTIPLIER" class="java.lang.String"/>
	<field name="MAX1_PAYMENTS_DATE" class="java.lang.String"/>
	<field name="MAX2_PAYMENTS_DATE" class="java.lang.String"/>
	<field name="MAX3_PAYMENTS_DATE" class="java.lang.String"/>
	<field name="max1_tot_pay_Date" class="java.lang.String"/>
	<field name="max2_tot_pay_Date" class="java.lang.String"/>
	<field name="max3_tot_pay_Date" class="java.lang.String"/>
	<field name="MAX1_CREDIT_LINE_DATE" class="java.lang.String"/>
	<field name="MAX2_CREDIT_LINE_DATE" class="java.lang.String"/>
	<field name="MAX3_CREDIT_LINE_DATE" class="java.lang.String"/>
	<field name="DATA_STATUS" class="java.lang.String"/>
	<field name="MAX1_CUSTOMER_PAYMENTS" class="java.lang.Double"/>
	<field name="MAX_CREDIT_LINE_TOTAL" class="java.lang.Double"/>
	<field name="MAX1_CREDIT_LINE_SECURED" class="java.lang.Double"/>
	<field name="MAX1_CREDIT_LINE_COMMITTED" class="java.lang.Double"/>
	<field name="MAX1_PEAK_CREDIT_LINE_USED" class="java.lang.Double"/>
	<field name="MAX2_CUSTOMER_PAYMENTS" class="java.lang.Double"/>
	<field name="MAX2_CREDIT_LINE_TOTAL" class="java.lang.Double"/>
	<field name="MAX2_CREDIT_LINE_SECURED" class="java.lang.Double"/>
	<field name="MAX2_CREDIT_LINE_COMMITTED" class="java.lang.Double"/>
	<field name="MAX2_PEAK_CREDIT_LINE_USED" class="java.lang.Double"/>
	<field name="MAX3_CUSTOMER_PAYMENTS" class="java.lang.Double"/>
	<field name="MAX3_CREDIT_LINE_TOTAL" class="java.lang.Double"/>
	<field name="MAX3_CREDIT_LINE_SECURED" class="java.lang.Double"/>
	<field name="MAX3_CREDIT_LINE_COMMITTED" class="java.lang.Double"/>
	<field name="MAX3_PEAK_CREDIT_LINE_USED" class="java.lang.Double"/>
	<field name="AVG_CUSTOMER_PAYMENTS" class="java.lang.Double"/>
	<field name="AVG_CREDIT_LINE_TOTAL" class="java.lang.Double"/>
	<field name="AVG_CREDIT_LINE_SECURED" class="java.lang.Double"/>
	<field name="AVG_CREDIT_LINE_COMMITTED" class="java.lang.Double"/>
	<field name="AVG_PEAK_CREDIT_LINE_USED" class="java.lang.Double"/>
	<field name="MAX1_TOTAL_PAYMENTS" class="java.lang.Double"/>
	<field name="MAX2_TOTAL_PAYMENTS" class="java.lang.Double"/>
	<field name="MAX3_TOTAL_PAYMENTS" class="java.lang.Double"/>
	<field name="AVG_TOTAL_PAYMENTS" class="java.lang.Double"/>
	<field name="MAX1_LORO_CLIENT_PAYMENTS" class="java.lang.Double"/>
	<field name="MAX2_LORO_CLIENT_PAYMENTS" class="java.lang.Double"/>
	<field name="MAX3_LORO_CLIENT_PAYMENTS" class="java.lang.Double"/>
	<field name="AVG_LORO_CLIENT_PAYMENTS" class="java.lang.Double"/>
	<field name="MAX1_CORP_CLIENT_PAYMENTS" class="java.lang.Double"/>
	<field name="MAX2_CORP_CLIENT_PAYMENTS" class="java.lang.Double"/>
	<field name="MAX3_CORP_CLIENT_PAYMENTS" class="java.lang.Double"/>
	<field name="AVG_CORP_CLIENT_PAYMENTS" class="java.lang.Double"/>
	<field name="MAX1_OTHER_PAYMENTS" class="java.lang.Double"/>
	<field name="MAX2_OTHER_PAYMENTS" class="java.lang.Double"/>
	<field name="MAX3_OTHER_PAYMENTS" class="java.lang.Double"/>
	<field name="AVG_OTHER_PAYMENTS" class="java.lang.Double"/>
	<field name="MAX1_BRANCH_PAYMENTS" class="java.lang.Double"/>
	<field name="MAX2_BRANCH_PAYMENTS" class="java.lang.Double"/>
	<field name="MAX3_BRANCH_PAYMENTS" class="java.lang.Double"/>
	<field name="AVG_BRANCH_PAYMENTS" class="java.lang.Double"/>
	<field name="CURRENT_ROW" class="java.lang.Integer"/>
	<field name="TOTAL_ROWS" class="java.lang.Integer"/>
	<field name="MISSING_D_START" class="java.util.Date"/>
	<field name="MISSING_D_END" class="java.util.Date"/>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band height="48" splitType="Stretch">
			<textField pattern="" isBlankWhenNull="false">
				<reportElement key="textField" mode="Opaque" x="0" y="0" width="822" height="24" forecolor="#000000" backcolor="#DCE6F1" uuid="32c222c0-75c3-450b-a986-5192b8581018"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="12" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{pDictionary_Data}.get("pRAPORT_TITLE_MONTHLY_LABEL")]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement key="textField-1" x="566" y="24" width="136" height="20" uuid="3b4c0226-8824-4cda-abd7-b9bc7c94d4a1">
					<property name="local_mesure_unitx" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="10" isBold="true" pdfFontName="Helvetica-Bold"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{pDictionary_Data}.get("pREPORT_DATE_LABEL")]]></textFieldExpression>
			</textField>
			<textField evaluationTime="Report" pattern="dd-MMM-yyyy HH:mm:ss" isBlankWhenNull="false">
				<reportElement key="textField-1" x="702" y="24" width="120" height="20" uuid="e9ce0c4d-3106-4194-93c4-0d629ee68ef8">
					<property name="local_mesure_unitx" value="pixel"/>
					<property name="local_mesure_unitwidth" value="pixel"/>
				</reportElement>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="10"/>
				</textElement>
				<textFieldExpression><![CDATA[new SimpleDateFormat($P{pDateFormat}+" HH:mm:ss").format(new Date())]]></textFieldExpression>
			</textField>
		</band>
	</title>
	<columnHeader>
		<band splitType="Stretch"/>
	</columnHeader>
	<detail>
		<band height="80" splitType="Prevent">
			<textField>
				<reportElement key="textField" positionType="Float" x="10" y="0" width="157" height="16" forecolor="#000000" backcolor="#FFFFFF" uuid="c3687440-7afa-458d-a356-c9bb9dea37b9"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font size="10" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{pDictionary_Data}.get("pENTITY_LABEL")]]></textFieldExpression>
			</textField>
			<textField pattern="###" isBlankWhenNull="true">
				<reportElement key="textField" x="300" y="0" width="120" height="16" uuid="8bbd14dc-c532-423f-b495-76fd1e1e737a"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle"/>
				<textFieldExpression><![CDATA[($F{ENTITY_ID}!=null)? $F{ENTITY_ID} :""]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement key="textField-1" x="566" y="0" width="170" height="16" uuid="c39a937f-7c6a-4a52-b063-6fa6e533d32d"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle"/>
				<textFieldExpression><![CDATA[($F{ENTITY_NAME}!=null)?$F{ENTITY_NAME}:""]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement key="textField" positionType="Float" x="10" y="16" width="157" height="16" forecolor="#000000" backcolor="#FFFFFF" uuid="e3743c07-2f25-4bb3-9ac7-e4351db02c7f"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font size="10" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{pDictionary_Data}.get("pCURRENCY_LABEL")]]></textFieldExpression>
			</textField>
			<textField pattern="###" isBlankWhenNull="true">
				<reportElement key="textField" x="300" y="16" width="120" height="16" uuid="abce4604-5597-435d-b519-************">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle"/>
				<textFieldExpression><![CDATA[($F{CCY}!=null)? $F{CCY}:""]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement key="textField-1" x="566" y="16" width="140" height="16" uuid="579e3c0a-beb3-4a53-897c-4dffa7483e91">
					<property name="local_mesure_unitx" value="pixel"/>
					<property name="local_mesure_unitheight" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="local_mesure_unity" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle"/>
				<textFieldExpression><![CDATA[($F{CCY_NAME}!=null)?$F{CCY_NAME}:""]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement key="textField" positionType="Float" x="10" y="32" width="170" height="16" forecolor="#000000" backcolor="#FFFFFF" uuid="5fbb0330-1e27-437e-9ed4-8aa4d1838fcc">
					<property name="local_mesure_unitheight" value="pixel"/>
					<property name="local_mesure_unity" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="local_mesure_unitwidth" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font size="10" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{pDictionary_Data}.get("pREPORTING_PERIOD_LABEL")]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement key="textField-1" x="300" y="32" width="80" height="16" uuid="727ee988-c11a-4fb0-a37f-c53db670b00f"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle"/>
				<textFieldExpression><![CDATA[new SimpleDateFormat($P{pDateFormat}).format($P{pValue_Date})]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement key="textField-1" x="300" y="48" width="80" height="16" uuid="14ac8e2f-88f3-43d4-a210-234dc8c7bb9c"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle"/>
				<textFieldExpression><![CDATA[new SimpleDateFormat($P{pDateFormat}).format($P{pValue_DateEnd})]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement key="textField" positionType="Float" x="10" y="64" width="170" height="16" forecolor="#000000" backcolor="#FFFFFF" uuid="0559e9f1-2d52-4f29-832c-3174c9761dc6">
					<property name="local_mesure_unitwidth" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font size="10" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{pDictionary_Data}.get("pCCY_MULTIPLIER_LABEL")]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement key="textField-1" x="300" y="64" width="80" height="16" uuid="a8d1e934-8318-4094-84f5-8a3e1ba0bbd4">
					<property name="local_mesure_unitheight" value="pixel"/>
					<property name="local_mesure_unity" value="pixel"/>
					<property name="local_mesure_unitx" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle"/>
				<textFieldExpression><![CDATA[$P{pDictionary_Data}.get("pCCY_MULTIPLIER_"+$F{CCY_MULTIPLIER})]]></textFieldExpression>
			</textField>
		</band>
		<band height="17">
			<textField pattern="" isBlankWhenNull="false">
				<reportElement key="textField" mode="Opaque" x="10" y="0" width="170" height="16" forecolor="#000000" uuid="fa7e0aa1-430f-422e-aa54-************"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font size="11" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{pDictionary_Data}.get("pDATA_STATE")]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="false">
				<reportElement key="textField" mode="Opaque" x="300" y="0" width="460" height="16" forecolor="#000000" uuid="9ed5ffe1-297b-49cf-8ddd-7c59d7830294">
					<property name="local_mesure_unitx" value="pixel"/>
					<printWhenExpression><![CDATA[$F{MISSING_D_START} != null && $F{MISSING_D_END} != null && (!($F{MISSING_D_END}).equals($F{MISSING_D_START}))]]></printWhenExpression>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle"/>
				<textFieldExpression><![CDATA[$P{pDictionary_Data}.get("pINCOMPLETED_DATA") +" "+new SimpleDateFormat($P{pDateFormat}).format($F{MISSING_D_START})+" "+$P{pDictionary_Data}.get("pTo_Value")+" "+new SimpleDateFormat($P{pDateFormat}).format($F{MISSING_D_END})]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="false">
				<reportElement key="textField" mode="Opaque" x="300" y="0" width="460" height="16" forecolor="#000000" uuid="d0e8d5bf-ddc4-4e93-a892-19aac865a4ff">
					<property name="local_mesure_unitx" value="pixel"/>
					<printWhenExpression><![CDATA[$F{MISSING_D_START} != null && $F{MISSING_D_END} != null && (($F{MISSING_D_END}).equals($F{MISSING_D_START}))]]></printWhenExpression>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle"/>
				<textFieldExpression><![CDATA[$P{pDictionary_Data}.get("pONE_DAY_MISSING") +" "+new SimpleDateFormat($P{pDateFormat}).format($F{MISSING_D_START})]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="false">
				<reportElement key="textField" mode="Opaque" x="300" y="0" width="460" height="16" forecolor="#000000" uuid="f9437c66-7ca3-4041-a3ef-b896007ab418">
					<property name="local_mesure_unitx" value="pixel"/>
					<printWhenExpression><![CDATA[$F{MISSING_D_START} == null && $F{MISSING_D_END} == null]]></printWhenExpression>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle"/>
				<textFieldExpression><![CDATA[$P{pDictionary_Data}.get("pCOMPLETE")]]></textFieldExpression>
			</textField>
		</band>
		<band height="95">
			<printWhenExpression><![CDATA[!$F{DATA_STATUS}.equals("NO_DATA") && $P{pSelectedCostumerPayments} == null]]></printWhenExpression>
			<frame>
				<reportElement positionType="Float" mode="Opaque" x="0" y="0" width="822" height="40" backcolor="#DCE6F1" uuid="cd25bd25-df66-4f04-abb6-55d1eb589dd8"/>
				<textField pattern="" isBlankWhenNull="false">
					<reportElement key="textField" mode="Opaque" x="0" y="0" width="315" height="32" forecolor="#000000" backcolor="#DCE6F1" uuid="c3445b45-09ca-4d38-9a09-58236f9bdffe"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="10" isBold="true" pdfFontName="Helvetica-Bold"/>
						<paragraph leftIndent="7"/>
					</textElement>
					<textFieldExpression><![CDATA[$P{pDictionary_Data}.get("pVALUE_PAYMENTS")]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="false">
					<reportElement key="textField" mode="Opaque" x="350" y="3" width="110" height="16" forecolor="#000000" backcolor="#DCE6F1" uuid="505ad241-9f10-4e17-bff1-66778ac9cc1e"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Top">
						<font size="10"/>
						<paragraph leftIndent="7"/>
					</textElement>
					<textFieldExpression><![CDATA[$P{pDictionary_Data}.get("pMAX_LABEL")]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="false">
					<reportElement key="textField" mode="Opaque" x="465" y="3" width="110" height="16" forecolor="#000000" backcolor="#DCE6F1" uuid="5f1fb149-ea9d-42bc-a441-416ce8d18745"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Top">
						<font size="10"/>
						<paragraph leftIndent="7"/>
					</textElement>
					<textFieldExpression><![CDATA[$P{pDictionary_Data}.get("p2EME_MAX_LABEL")]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="false">
					<reportElement key="textField" mode="Opaque" x="580" y="3" width="110" height="16" forecolor="#000000" backcolor="#DCE6F1" uuid="e5db0525-4e50-49ba-928d-20728ee8ee5e"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Top">
						<font size="10"/>
						<paragraph leftIndent="7"/>
					</textElement>
					<textFieldExpression><![CDATA[$P{pDictionary_Data}.get("p3EME_MAX_LABEL")]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="false">
					<reportElement key="textField" mode="Opaque" x="706" y="3" width="110" height="16" forecolor="#000000" backcolor="#DCE6F1" uuid="4de18804-8bfc-4f11-b379-b0a5c01c0189"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Top">
						<font size="10"/>
						<paragraph leftIndent="7"/>
					</textElement>
					<textFieldExpression><![CDATA[$P{pDictionary_Data}.get("pAVERAGE_LABEL")]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="false">
					<reportElement key="textField" mode="Opaque" x="350" y="19" width="110" height="16" forecolor="#000000" backcolor="#DCE6F1" uuid="02c311ab-7a4a-4e78-8321-330eb940387e"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Top">
						<font size="10"/>
						<paragraph leftIndent="7"/>
					</textElement>
					<textFieldExpression><![CDATA[($F{MAX1_PAYMENTS_DATE}!=null)?$F{MAX1_PAYMENTS_DATE}:"-"]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="false">
					<reportElement key="textField" mode="Opaque" x="465" y="19" width="110" height="16" forecolor="#000000" backcolor="#DCE6F1" uuid="fefdcc00-105f-4b34-8699-72e308da35ee"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Top">
						<font size="10"/>
						<paragraph leftIndent="7"/>
					</textElement>
					<textFieldExpression><![CDATA[($F{MAX2_PAYMENTS_DATE}!=null)?$F{MAX2_PAYMENTS_DATE}:"-"]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="false">
					<reportElement key="textField" mode="Opaque" x="580" y="19" width="110" height="16" forecolor="#000000" backcolor="#DCE6F1" uuid="f69fd4c7-ee91-422d-b17f-33f4916fa1c9"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Top">
						<font size="10" isBold="false" pdfFontName="Helvetica-Oblique"/>
						<paragraph leftIndent="7"/>
					</textElement>
					<textFieldExpression><![CDATA[($F{MAX3_PAYMENTS_DATE}!=null)?$F{MAX3_PAYMENTS_DATE}:"-"]]></textFieldExpression>
				</textField>
			</frame>
			<textField>
				<reportElement key="textField" positionType="Float" x="10" y="45" width="270" height="45" forecolor="#000000" backcolor="#FFFFFF" uuid="d745e6e8-2901-4418-a842-c3905eb5a2ee">
					<property name="local_mesure_unitheight" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement>
					<font fontName="SansSerif" size="10" isBold="false" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{pDictionary_Data}.get("pTOTAL_CROSS_PAYMENTS")]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement key="textField-1" x="350" y="45" width="110" height="16" uuid="dcd407f1-046a-4a12-9bcb-a9f5e4c68592">
					<property name="local_mesure_unitheight" value="pixel"/>
					<property name="local_mesure_unitx" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[($F{MAX1_CUSTOMER_PAYMENTS}!=null)?($P{pIlmUtil}.formatCurrency($F{MAX1_CUSTOMER_PAYMENTS},$P{pCurrencyPattern})):"-"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement key="textField-1" x="465" y="45" width="110" height="16" uuid="f0d3b17d-81e0-4d22-8fba-40c282849d6e">
					<property name="local_mesure_unitheight" value="pixel"/>
					<property name="local_mesure_unitx" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[($F{MAX2_CUSTOMER_PAYMENTS}!=null)?($P{pIlmUtil}.formatCurrency($F{MAX2_CUSTOMER_PAYMENTS},$P{pCurrencyPattern})):"-"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement key="textField-1" x="580" y="45" width="110" height="16" uuid="76c84311-025c-4c94-aca1-1ef85f2b2931">
					<property name="local_mesure_unitheight" value="pixel"/>
					<property name="local_mesure_unitx" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[($F{MAX3_CUSTOMER_PAYMENTS}!=null)?($P{pIlmUtil}.formatCurrency($F{MAX3_CUSTOMER_PAYMENTS},$P{pCurrencyPattern})):"-"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement key="textField-1" x="706" y="45" width="110" height="16" uuid="8ddd08cf-9119-4a59-a232-c9ad188fa55e">
					<property name="local_mesure_unitheight" value="pixel"/>
					<property name="local_mesure_unitx" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[($F{AVG_CUSTOMER_PAYMENTS}!=null)?($P{pIlmUtil}.formatCurrency($F{AVG_CUSTOMER_PAYMENTS},$P{pCurrencyPattern})):"-"]]></textFieldExpression>
			</textField>
		</band>
		<band height="140">
			<printWhenExpression><![CDATA[!$F{DATA_STATUS}.equals("NO_DATA") && $P{pSelectedCostumerPayments} != null]]></printWhenExpression>
			<frame>
				<reportElement positionType="Float" mode="Opaque" x="0" y="0" width="822" height="40" backcolor="#DCE6F1" uuid="cd25bd25-df66-4f04-abb6-55d1eb589dd8"/>
				<textField pattern="" isBlankWhenNull="false">
					<reportElement key="textField" mode="Opaque" x="0" y="0" width="315" height="32" forecolor="#000000" backcolor="#DCE6F1" uuid="c3445b45-09ca-4d38-9a09-58236f9bdffe"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="10" isBold="true" pdfFontName="Helvetica-Bold"/>
						<paragraph leftIndent="7"/>
					</textElement>
					<textFieldExpression><![CDATA[$P{pDictionary_Data}.get("pVALUE_PAYMENTS")]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="false">
					<reportElement key="textField" mode="Opaque" x="350" y="3" width="110" height="16" forecolor="#000000" backcolor="#DCE6F1" uuid="505ad241-9f10-4e17-bff1-66778ac9cc1e"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Top">
						<font size="10"/>
						<paragraph leftIndent="7"/>
					</textElement>
					<textFieldExpression><![CDATA[$P{pDictionary_Data}.get("pMAX_LABEL")]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="false">
					<reportElement key="textField" mode="Opaque" x="465" y="3" width="110" height="16" forecolor="#000000" backcolor="#DCE6F1" uuid="5f1fb149-ea9d-42bc-a441-416ce8d18745"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Top">
						<font size="10"/>
						<paragraph leftIndent="7"/>
					</textElement>
					<textFieldExpression><![CDATA[$P{pDictionary_Data}.get("p2EME_MAX_LABEL")]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="false">
					<reportElement key="textField" mode="Opaque" x="581" y="3" width="110" height="16" forecolor="#000000" backcolor="#DCE6F1" uuid="e5db0525-4e50-49ba-928d-20728ee8ee5e"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Top">
						<font size="10"/>
						<paragraph leftIndent="7"/>
					</textElement>
					<textFieldExpression><![CDATA[$P{pDictionary_Data}.get("p3EME_MAX_LABEL")]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="false">
					<reportElement key="textField" mode="Opaque" x="706" y="3" width="110" height="16" forecolor="#000000" backcolor="#DCE6F1" uuid="4de18804-8bfc-4f11-b379-b0a5c01c0189"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Top">
						<font size="10"/>
						<paragraph leftIndent="7"/>
					</textElement>
					<textFieldExpression><![CDATA[$P{pDictionary_Data}.get("pAVERAGE_LABEL")]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="false">
					<reportElement key="textField" mode="Opaque" x="350" y="19" width="110" height="16" forecolor="#000000" backcolor="#DCE6F1" uuid="02c311ab-7a4a-4e78-8321-330eb940387e"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Top">
						<font size="10"/>
						<paragraph leftIndent="7"/>
					</textElement>
					<textFieldExpression><![CDATA[($F{max1_tot_pay_Date}!=null)?$F{max1_tot_pay_Date}:"-"]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="false">
					<reportElement key="textField" mode="Opaque" x="465" y="19" width="110" height="16" forecolor="#000000" backcolor="#DCE6F1" uuid="fefdcc00-105f-4b34-8699-72e308da35ee"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Top">
						<font size="10"/>
						<paragraph leftIndent="7"/>
					</textElement>
					<textFieldExpression><![CDATA[($F{max2_tot_pay_Date}!=null)?$F{max2_tot_pay_Date}:"-"]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="false">
					<reportElement key="textField" mode="Opaque" x="579" y="19" width="110" height="16" forecolor="#000000" backcolor="#DCE6F1" uuid="f69fd4c7-ee91-422d-b17f-33f4916fa1c9"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Top">
						<font size="10" isBold="false" pdfFontName="Helvetica-Oblique"/>
						<paragraph leftIndent="7"/>
					</textElement>
					<textFieldExpression><![CDATA[($F{max3_tot_pay_Date}!=null)?$F{max3_tot_pay_Date}:"-"]]></textFieldExpression>
				</textField>
			</frame>
			<frame>
				<reportElement mode="Opaque" x="0" y="40" width="350" height="80" backcolor="#D9D9D9" uuid="cf23e861-6a01-4606-8f3d-7ce588ac7589">
					<property name="com.jaspersoft.studio.layout" value="com.jaspersoft.studio.editor.layout.VerticalRowLayout"/>
				</reportElement>
				<textField>
					<reportElement key="textField" positionType="Float" x="0" y="0" width="350" height="20" forecolor="#000000" backcolor="#FFFFFF" uuid="d745e6e8-2901-4418-a842-c3905eb5a2ee">
						<property name="local_mesure_unitheight" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.leftIndent" value="px"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement verticalAlignment="Middle">
						<font fontName="SansSerif" size="10" isBold="false" pdfFontName="Helvetica-Bold"/>
						<paragraph leftIndent="10"/>
					</textElement>
					<textFieldExpression><![CDATA[$P{pDictionary_Data}.get("pLoroClient_LABEL")]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="0" y="20" width="350" height="20" uuid="ac3c2dea-2ab0-4a79-a10a-c117e9e95691">
						<property name="com.jaspersoft.studio.unit.leftIndent" value="px"/>
					</reportElement>
					<textElement verticalAlignment="Middle">
						<paragraph leftIndent="10"/>
					</textElement>
					<textFieldExpression><![CDATA[$P{pDictionary_Data}.get("pCorporateClient_LABEL")]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="0" y="40" width="350" height="20" uuid="906a5cc8-0b21-465e-be98-006bc03c981c">
						<property name="com.jaspersoft.studio.unit.leftIndent" value="px"/>
					</reportElement>
					<textElement verticalAlignment="Middle">
						<paragraph leftIndent="10"/>
					</textElement>
					<textFieldExpression><![CDATA[$P{pDictionary_Data}.get("pBranch_LABEL")]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="0" y="60" width="350" height="20" uuid="3d85e15a-1499-49e5-a87d-b272b4e587f1">
						<property name="com.jaspersoft.studio.unit.leftIndent" value="px"/>
					</reportElement>
					<textElement verticalAlignment="Middle">
						<paragraph leftIndent="10"/>
					</textElement>
					<textFieldExpression><![CDATA[$P{pDictionary_Data}.get("pOtherClient_LABEL")]]></textFieldExpression>
				</textField>
			</frame>
			<frame>
				<reportElement mode="Opaque" x="350" y="40" width="118" height="80" backcolor="#D9D9D9" uuid="20711f72-e00e-4745-ba6b-7cb13556b598">
					<property name="com.jaspersoft.studio.layout" value="com.jaspersoft.studio.editor.layout.VerticalRowLayout"/>
				</reportElement>
				<textField>
					<reportElement key="textField-1" x="0" y="0" width="118" height="20" uuid="dcd407f1-046a-4a12-9bcb-a9f5e4c68592">
						<property name="local_mesure_unitheight" value="pixel"/>
						<property name="local_mesure_unitx" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.rightIndent" value="px"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font isBold="true"/>
						<paragraph rightIndent="5"/>
					</textElement>
					<textFieldExpression><![CDATA[($F{MAX1_LORO_CLIENT_PAYMENTS}!=null &&  !$P{pLoroPayments}.isEmpty())?($P{pIlmUtil}.formatCurrency($F{MAX1_LORO_CLIENT_PAYMENTS},$P{pCurrencyPattern})):"-"]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="0" y="20" width="118" height="20" uuid="f04c964e-6663-4ac5-abc4-f54ee030daf7">
						<property name="com.jaspersoft.studio.unit.rightIndent" value="px"/>
					</reportElement>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<paragraph rightIndent="5"/>
					</textElement>
					<textFieldExpression><![CDATA[($F{MAX1_CORP_CLIENT_PAYMENTS}!=null && !$P{pCorpPayments}.isEmpty())?($P{pIlmUtil}.formatCurrency($F{MAX1_CORP_CLIENT_PAYMENTS},$P{pCurrencyPattern})):"-"]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="0" y="40" width="118" height="20" uuid="a8472b9b-fbd7-4abb-96a2-ec029548f736">
						<property name="com.jaspersoft.studio.unit.rightIndent" value="px"/>
					</reportElement>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<paragraph rightIndent="5"/>
					</textElement>
					<textFieldExpression><![CDATA[($F{MAX1_BRANCH_PAYMENTS}!=null &&  !$P{pBranchPayments}.isEmpty())?($P{pIlmUtil}.formatCurrency($F{MAX1_BRANCH_PAYMENTS},$P{pCurrencyPattern})):"-"]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="0" y="60" width="118" height="20" uuid="b4d2f1a8-5811-4765-861e-aa81d15cc3a1">
						<property name="com.jaspersoft.studio.unit.rightIndent" value="px"/>
					</reportElement>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<paragraph rightIndent="5"/>
					</textElement>
					<textFieldExpression><![CDATA[($F{MAX1_OTHER_PAYMENTS}!=null && !$P{pOtherPayments}.isEmpty())?($P{pIlmUtil}.formatCurrency($F{MAX1_OTHER_PAYMENTS},$P{pCurrencyPattern})):"-"]]></textFieldExpression>
				</textField>
			</frame>
			<frame>
				<reportElement mode="Opaque" x="468" y="40" width="112" height="80" backcolor="#D9D9D9" uuid="f62c20bf-4bf1-493e-bf1c-65658de8d357">
					<property name="com.jaspersoft.studio.layout" value="com.jaspersoft.studio.editor.layout.VerticalRowLayout"/>
				</reportElement>
				<textField>
					<reportElement key="textField-1" x="0" y="0" width="112" height="20" uuid="f0d3b17d-81e0-4d22-8fba-40c282849d6e">
						<property name="local_mesure_unitheight" value="pixel"/>
						<property name="local_mesure_unitx" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.rightIndent" value="px"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font isBold="true"/>
						<paragraph rightIndent="5"/>
					</textElement>
					<textFieldExpression><![CDATA[($F{MAX2_LORO_CLIENT_PAYMENTS}!=null &&  !$P{pLoroPayments}.isEmpty())?($P{pIlmUtil}.formatCurrency($F{MAX2_LORO_CLIENT_PAYMENTS},$P{pCurrencyPattern})):"-"]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="0" y="20" width="112" height="20" uuid="f315e29a-22f5-4d3f-94bb-e603354ec6c3">
						<property name="com.jaspersoft.studio.unit.rightIndent" value="px"/>
					</reportElement>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<paragraph rightIndent="5"/>
					</textElement>
					<textFieldExpression><![CDATA[($F{MAX2_CORP_CLIENT_PAYMENTS}!=null && !$P{pCorpPayments}.isEmpty())?($P{pIlmUtil}.formatCurrency($F{MAX2_CORP_CLIENT_PAYMENTS},$P{pCurrencyPattern})):"-"]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="0" y="40" width="112" height="20" uuid="eb599a06-0c61-4f76-bdeb-842adec2e415">
						<property name="com.jaspersoft.studio.unit.rightIndent" value="px"/>
					</reportElement>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<paragraph rightIndent="5"/>
					</textElement>
					<textFieldExpression><![CDATA[($F{MAX2_BRANCH_PAYMENTS}!=null &&  !$P{pBranchPayments}.isEmpty())?($P{pIlmUtil}.formatCurrency($F{MAX2_BRANCH_PAYMENTS},$P{pCurrencyPattern})):"-"]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="0" y="60" width="112" height="20" uuid="0866c31a-369b-4a12-ae48-7b64295da8df">
						<property name="com.jaspersoft.studio.unit.rightIndent" value="px"/>
					</reportElement>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<paragraph rightIndent="5"/>
					</textElement>
					<textFieldExpression><![CDATA[($F{MAX2_OTHER_PAYMENTS}!=null && !$P{pOtherPayments}.isEmpty())?($P{pIlmUtil}.formatCurrency($F{MAX2_OTHER_PAYMENTS},$P{pCurrencyPattern})):"-"]]></textFieldExpression>
				</textField>
			</frame>
			<frame>
				<reportElement mode="Opaque" x="580" y="40" width="118" height="80" backcolor="#D9D9D9" uuid="9fb619e5-ef3d-4ee4-92ce-11d2ca619bf7">
					<property name="com.jaspersoft.studio.layout" value="com.jaspersoft.studio.editor.layout.VerticalRowLayout"/>
				</reportElement>
				<textField>
					<reportElement key="textField-1" x="0" y="0" width="118" height="20" uuid="76c84311-025c-4c94-aca1-1ef85f2b2931">
						<property name="local_mesure_unitheight" value="pixel"/>
						<property name="local_mesure_unitx" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.rightIndent" value="px"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font isBold="true"/>
						<paragraph rightIndent="8"/>
					</textElement>
					<textFieldExpression><![CDATA[($F{MAX3_LORO_CLIENT_PAYMENTS}!=null &&  !$P{pLoroPayments}.isEmpty())?($P{pIlmUtil}.formatCurrency($F{MAX3_LORO_CLIENT_PAYMENTS},$P{pCurrencyPattern})):"-"]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="0" y="20" width="118" height="20" uuid="3d821471-4280-4876-8ff8-7230e62ffe67">
						<property name="com.jaspersoft.studio.unit.rightIndent" value="px"/>
					</reportElement>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<paragraph rightIndent="8"/>
					</textElement>
					<textFieldExpression><![CDATA[($F{MAX3_CORP_CLIENT_PAYMENTS}!=null && !$P{pCorpPayments}.isEmpty())?($P{pIlmUtil}.formatCurrency($F{MAX3_CORP_CLIENT_PAYMENTS},$P{pCurrencyPattern})):"-"]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="0" y="40" width="118" height="20" uuid="d25ec6a2-1e42-4ec4-a901-4623fe8b5f39">
						<property name="com.jaspersoft.studio.unit.rightIndent" value="px"/>
					</reportElement>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<paragraph rightIndent="8"/>
					</textElement>
					<textFieldExpression><![CDATA[($F{MAX3_BRANCH_PAYMENTS}!=null &&  !$P{pBranchPayments}.isEmpty())?($P{pIlmUtil}.formatCurrency($F{MAX3_BRANCH_PAYMENTS},$P{pCurrencyPattern})):"-"]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="0" y="60" width="118" height="20" uuid="9970ce5b-604e-478c-8dc3-1c557eebdee7">
						<property name="com.jaspersoft.studio.unit.rightIndent" value="px"/>
					</reportElement>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<paragraph rightIndent="8"/>
					</textElement>
					<textFieldExpression><![CDATA[($F{MAX3_OTHER_PAYMENTS}!=null && !$P{pOtherPayments}.isEmpty())?($P{pIlmUtil}.formatCurrency($F{MAX3_OTHER_PAYMENTS},$P{pCurrencyPattern})):"-"]]></textFieldExpression>
				</textField>
			</frame>
			<frame>
				<reportElement mode="Opaque" x="698" y="39" width="124" height="81" backcolor="#D9D9D9" uuid="b5f460ec-e34e-472e-ade4-29807b804f1a">
					<property name="com.jaspersoft.studio.layout" value="com.jaspersoft.studio.editor.layout.VerticalRowLayout"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<textField>
					<reportElement key="textField-1" x="0" y="0" width="124" height="21" uuid="8ddd08cf-9119-4a59-a232-c9ad188fa55e">
						<property name="local_mesure_unitheight" value="pixel"/>
						<property name="local_mesure_unitx" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.rightIndent" value="px"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font isBold="true"/>
						<paragraph rightIndent="5"/>
					</textElement>
					<textFieldExpression><![CDATA[($F{AVG_LORO_CLIENT_PAYMENTS}!=null &&  !$P{pLoroPayments}.isEmpty())?($P{pIlmUtil}.formatCurrency($F{AVG_LORO_CLIENT_PAYMENTS},$P{pCurrencyPattern})):"-"]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="0" y="21" width="124" height="20" uuid="f4698046-c498-4afb-9b7f-9adc69a438df">
						<property name="com.jaspersoft.studio.unit.rightIndent" value="px"/>
					</reportElement>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<paragraph rightIndent="5"/>
					</textElement>
					<textFieldExpression><![CDATA[($F{AVG_CORP_CLIENT_PAYMENTS}!=null && !$P{pCorpPayments}.isEmpty())?($P{pIlmUtil}.formatCurrency($F{AVG_CORP_CLIENT_PAYMENTS},$P{pCurrencyPattern})):"-"]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="0" y="41" width="124" height="20" uuid="842c7162-9156-47c9-8beb-cb8c73278d53">
						<property name="com.jaspersoft.studio.unit.rightIndent" value="px"/>
					</reportElement>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<paragraph rightIndent="5"/>
					</textElement>
					<textFieldExpression><![CDATA[($F{AVG_BRANCH_PAYMENTS}!=null &&  !$P{pBranchPayments}.isEmpty())?($P{pIlmUtil}.formatCurrency($F{AVG_BRANCH_PAYMENTS},$P{pCurrencyPattern})):"-"]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="0" y="61" width="124" height="20" uuid="2b0ce83e-632b-4e95-9a22-14eb85e4d096">
						<property name="com.jaspersoft.studio.unit.rightIndent" value="px"/>
					</reportElement>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<paragraph rightIndent="5"/>
					</textElement>
					<textFieldExpression><![CDATA[($F{AVG_OTHER_PAYMENTS}!=null && !$P{pOtherPayments}.isEmpty())?($P{pIlmUtil}.formatCurrency($F{AVG_OTHER_PAYMENTS},$P{pCurrencyPattern})):"-"]]></textFieldExpression>
				</textField>
			</frame>
			<frame>
				<reportElement mode="Opaque" x="0" y="120" width="822" height="20" backcolor="#B8B4B4" uuid="b9b93d97-3250-4f28-893d-b845daed3f17">
					<property name="com.jaspersoft.studio.layout" value="com.jaspersoft.studio.editor.layout.FreeLayout"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<textField>
					<reportElement x="0" y="0" width="350" height="20" uuid="e1cd7cb6-c5ed-43bf-9753-c43e06d982a8">
						<property name="com.jaspersoft.studio.unit.leftIndent" value="px"/>
					</reportElement>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="10" isBold="true" pdfFontName="Helvetica-Bold"/>
						<paragraph leftIndent="10"/>
					</textElement>
					<textFieldExpression><![CDATA[$P{pDictionary_Data}.get("pTOTAL_LABEL")]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="350" y="0" width="118" height="20" uuid="c011ff74-8177-4c20-83bf-e406f4ab3c50">
						<property name="com.jaspersoft.studio.unit.rightIndent" value="px"/>
					</reportElement>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font size="10" isBold="true" pdfFontName="Helvetica-Bold"/>
						<paragraph rightIndent="8"/>
					</textElement>
					<textFieldExpression><![CDATA[($F{MAX1_TOTAL_PAYMENTS}!=null)?($P{pIlmUtil}.formatCurrency($F{MAX1_TOTAL_PAYMENTS},$P{pCurrencyPattern})):"-"]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="468" y="0" width="112" height="20" uuid="2bee0b91-f683-4c3f-bff5-fef3cd484a37">
						<property name="com.jaspersoft.studio.unit.rightIndent" value="px"/>
					</reportElement>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font size="10" isBold="true" pdfFontName="Helvetica-Bold"/>
						<paragraph rightIndent="5"/>
					</textElement>
					<textFieldExpression><![CDATA[($F{MAX2_TOTAL_PAYMENTS}!=null)?($P{pIlmUtil}.formatCurrency($F{MAX2_TOTAL_PAYMENTS},$P{pCurrencyPattern})):"-"]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="579" y="0" width="119" height="20" uuid="8ee88c6f-d792-46fc-8555-30126f218215">
						<property name="com.jaspersoft.studio.unit.rightIndent" value="px"/>
					</reportElement>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font size="10" isBold="true" pdfFontName="Helvetica-Bold"/>
						<paragraph rightIndent="8"/>
					</textElement>
					<textFieldExpression><![CDATA[($F{MAX3_TOTAL_PAYMENTS}!=null)?($P{pIlmUtil}.formatCurrency($F{MAX3_TOTAL_PAYMENTS},$P{pCurrencyPattern})):"-"]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="698" y="0" width="123" height="20" uuid="edc54875-a847-4a07-8dc1-2fd9907b8eed">
						<property name="com.jaspersoft.studio.unit.rightIndent" value="px"/>
					</reportElement>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font size="10" isBold="true" pdfFontName="Helvetica-Bold"/>
						<paragraph rightIndent="5"/>
					</textElement>
					<textFieldExpression><![CDATA[($F{AVG_TOTAL_PAYMENTS}!=null)?($P{pIlmUtil}.formatCurrency($F{AVG_TOTAL_PAYMENTS},$P{pCurrencyPattern})):"-"]]></textFieldExpression>
				</textField>
			</frame>
		</band>
		<band height="76" splitType="Prevent">
			<printWhenExpression><![CDATA[!$F{DATA_STATUS}.equals("NO_DATA")]]></printWhenExpression>
			<textField pattern="" isBlankWhenNull="false">
				<reportElement key="textField" mode="Opaque" x="0" y="0" width="822" height="40" forecolor="#000000" backcolor="#DCE6F1" uuid="f59141dc-8302-48b1-8f83-bf53f098de6f"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font size="10" isBold="true" pdfFontName="Helvetica-Bold"/>
					<paragraph leftIndent="7"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{pDictionary_Data}.get("pINTRADAY_CREDIT_LINES")]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="false">
				<reportElement key="textField" mode="Opaque" x="350" y="3" width="110" height="16" forecolor="#000000" backcolor="#DCE6F1" uuid="f74d6c79-3ac7-4b09-811d-a0514548a0f1"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Top">
					<font size="10"/>
					<paragraph leftIndent="7"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{pDictionary_Data}.get("pMAX_LABEL")]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="false">
				<reportElement key="textField" mode="Opaque" x="465" y="3" width="110" height="16" forecolor="#000000" backcolor="#DCE6F1" uuid="5a0bb7c9-e19e-4079-b6ab-f4ad7e9513ff"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Top">
					<font size="10"/>
					<paragraph leftIndent="7"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{pDictionary_Data}.get("p2EME_MAX_LABEL")]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="false">
				<reportElement key="textField" mode="Opaque" x="580" y="3" width="110" height="16" forecolor="#000000" backcolor="#DCE6F1" uuid="59cdf272-61bc-4704-b5ed-7a9b02032704"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Top">
					<font size="10"/>
					<paragraph leftIndent="7"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{pDictionary_Data}.get("p3EME_MAX_LABEL")]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="false">
				<reportElement key="textField" mode="Opaque" x="706" y="3" width="110" height="16" forecolor="#000000" backcolor="#DCE6F1" uuid="876b604e-66e3-46b9-a71b-6f7a552644c0"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Top">
					<font size="10" isBold="false"/>
					<paragraph leftIndent="7"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{pDictionary_Data}.get("pAVERAGE_LABEL")]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="false">
				<reportElement key="textField" mode="Opaque" x="350" y="19" width="110" height="16" forecolor="#000000" backcolor="#DCE6F1" uuid="804c9de2-c31b-4f3b-9fa9-68c6e6dd3d85"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Top">
					<font size="10" isBold="false" pdfFontName="Helvetica-Oblique"/>
					<paragraph leftIndent="7"/>
				</textElement>
				<textFieldExpression><![CDATA[($F{MAX1_CREDIT_LINE_DATE}!=null)?$F{MAX1_CREDIT_LINE_DATE}:"-"]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="false">
				<reportElement key="textField" mode="Opaque" x="465" y="19" width="110" height="16" forecolor="#000000" backcolor="#DCE6F1" uuid="a8d3f808-47cd-4617-9a8e-1c33e65e04df"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Top">
					<font size="10" isBold="false" pdfFontName="Helvetica-Oblique"/>
					<paragraph leftIndent="7"/>
				</textElement>
				<textFieldExpression><![CDATA[($F{MAX2_CREDIT_LINE_DATE}!=null)?$F{MAX2_CREDIT_LINE_DATE}:"-"]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="false">
				<reportElement key="textField" mode="Opaque" x="580" y="19" width="110" height="16" forecolor="#000000" backcolor="#DCE6F1" uuid="a765e45e-d520-44ab-8199-c2b5dab637dc"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Top">
					<font size="10" isBold="false" pdfFontName="Helvetica-Oblique"/>
					<paragraph leftIndent="7"/>
				</textElement>
				<textFieldExpression><![CDATA[($F{MAX3_CREDIT_LINE_DATE}!=null)?$F{MAX3_CREDIT_LINE_DATE}:"-"]]></textFieldExpression>
			</textField>
			<frame>
				<reportElement positionType="Float" mode="Opaque" x="0" y="40" width="822" height="36" backcolor="#BFBFBF" uuid="32ff36a1-3b58-43d3-96b2-556c3182aa25"/>
				<textField pattern="" isBlankWhenNull="false">
					<reportElement key="textField" mode="Opaque" x="10" y="2" width="250" height="32" forecolor="#000000" backcolor="#BFBFBF" uuid="792d0fa3-70cb-49ad-a4ec-ffbb0e3c1576"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="10" isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<textFieldExpression><![CDATA[$P{pDictionary_Data}.get("pINTRADAY_TOTAL_VALUE_CREDIT")]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="350" y="10" width="110" height="16" uuid="f3983cc9-e411-4b14-902b-c528828f12a7">
						<property name="local_mesure_unitx" value="pixel"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font size="10" isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<textFieldExpression><![CDATA[($F{MAX_CREDIT_LINE_TOTAL}!=null)?($P{pIlmUtil}.formatCurrency($F{MAX_CREDIT_LINE_TOTAL},$P{pCurrencyPattern})):"-"]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="465" y="10" width="110" height="16" uuid="a63854dd-374c-4299-ae11-c8dcb4207784">
						<property name="local_mesure_unitx" value="pixel"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font size="10" isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<textFieldExpression><![CDATA[($F{MAX2_CREDIT_LINE_TOTAL}!=null)?($P{pIlmUtil}.formatCurrency($F{MAX2_CREDIT_LINE_TOTAL},$P{pCurrencyPattern})):"-"]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="580" y="10" width="110" height="16" uuid="998f0200-e230-4c21-8705-99336542c4df">
						<property name="local_mesure_unitx" value="pixel"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font size="10" isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<textFieldExpression><![CDATA[($F{MAX3_CREDIT_LINE_TOTAL}!=null)?($P{pIlmUtil}.formatCurrency($F{MAX3_CREDIT_LINE_TOTAL},$P{pCurrencyPattern})):"-"]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="706" y="10" width="110" height="16" uuid="c9b34b16-6432-4e53-8482-7970b2dce2bb">
						<property name="local_mesure_unitx" value="pixel"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font size="10" isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<textFieldExpression><![CDATA[($F{AVG_CREDIT_LINE_TOTAL}!=null)?($P{pIlmUtil}.formatCurrency($F{AVG_CREDIT_LINE_TOTAL},$P{pCurrencyPattern})):"-"]]></textFieldExpression>
				</textField>
			</frame>
		</band>
		<band height="66" splitType="Immediate">
			<printWhenExpression><![CDATA[!$F{DATA_STATUS}.equals("NO_DATA")]]></printWhenExpression>
			<frame>
				<reportElement positionType="Float" mode="Opaque" x="0" y="0" width="822" height="65" backcolor="#F2F2F2" uuid="f364ec80-0bbb-4daa-a0c7-6f747b346bec"/>
				<textField>
					<reportElement key="textField" positionType="Float" x="10" y="10" width="219" height="16" forecolor="#000000" backcolor="#FFFFFF" uuid="c3795233-8389-499a-a441-70bfa3136eb0">
						<property name="local_mesure_unitheight" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement>
						<font fontName="SansSerif" size="10" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$P{pDictionary_Data}.get("pOF_WHICH_SECURED")]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement key="textField-1" x="350" y="10" width="110" height="16" uuid="50df94d3-c960-4e26-a4a1-003df4054ecc">
						<property name="local_mesure_unitheight" value="pixel"/>
						<property name="local_mesure_unitx" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle"/>
					<textFieldExpression><![CDATA[($F{MAX1_CREDIT_LINE_SECURED}!=null)?($P{pIlmUtil}.formatCurrency($F{MAX1_CREDIT_LINE_SECURED},$P{pCurrencyPattern})):"-"]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement key="textField-1" x="465" y="10" width="110" height="16" uuid="2aa96578-1c31-4ef6-bf2c-184bb9906272">
						<property name="local_mesure_unitheight" value="pixel"/>
						<property name="local_mesure_unitx" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle"/>
					<textFieldExpression><![CDATA[($F{MAX2_CREDIT_LINE_SECURED}!=null)?($P{pIlmUtil}.formatCurrency($F{MAX2_CREDIT_LINE_SECURED},$P{pCurrencyPattern})):"-"]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement key="textField-1" x="580" y="10" width="110" height="16" uuid="cc37a9e7-2d90-40f3-9185-53d776add528">
						<property name="local_mesure_unitheight" value="pixel"/>
						<property name="local_mesure_unitx" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle"/>
					<textFieldExpression><![CDATA[($F{MAX3_CREDIT_LINE_SECURED}!=null)?($P{pIlmUtil}.formatCurrency($F{MAX3_CREDIT_LINE_SECURED},$P{pCurrencyPattern})):"-"]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement key="textField-1" x="706" y="10" width="110" height="16" uuid="89d5cf9c-6490-4fb1-9192-8a62a309872b">
						<property name="local_mesure_unitheight" value="pixel"/>
						<property name="local_mesure_unitx" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle"/>
					<textFieldExpression><![CDATA[($F{AVG_CREDIT_LINE_SECURED}!=null)?($P{pIlmUtil}.formatCurrency($F{AVG_CREDIT_LINE_SECURED},$P{pCurrencyPattern})):"-"]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement key="textField" positionType="Float" x="10" y="26" width="219" height="16" forecolor="#000000" backcolor="#FFFFFF" uuid="c95b8019-5027-43d5-8a47-1cbf64cdd96c">
						<property name="local_mesure_unitheight" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement>
						<font fontName="SansSerif" size="10" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$P{pDictionary_Data}.get("pOF_WHICH_COMMITTED")]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement key="textField-1" x="350" y="26" width="110" height="16" uuid="f76487fe-7794-4e82-a0ed-08934b89764f">
						<property name="local_mesure_unitheight" value="pixel"/>
						<property name="local_mesure_unitx" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle"/>
					<textFieldExpression><![CDATA[($F{MAX1_CREDIT_LINE_COMMITTED}!=null)?($P{pIlmUtil}.formatCurrency($F{MAX1_CREDIT_LINE_COMMITTED},$P{pCurrencyPattern})):"-"]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement key="textField-1" x="465" y="26" width="110" height="16" uuid="ce1d8d9d-73f4-438b-bdda-f28275ee1d60">
						<property name="local_mesure_unitheight" value="pixel"/>
						<property name="local_mesure_unitx" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle"/>
					<textFieldExpression><![CDATA[($F{MAX2_CREDIT_LINE_COMMITTED}!=null)?($P{pIlmUtil}.formatCurrency($F{MAX2_CREDIT_LINE_COMMITTED},$P{pCurrencyPattern})):"-"]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement key="textField-1" x="580" y="26" width="110" height="16" uuid="0bbcf07f-e22e-41b8-bde6-f70b0229bc25">
						<property name="local_mesure_unitheight" value="pixel"/>
						<property name="local_mesure_unitx" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle"/>
					<textFieldExpression><![CDATA[($F{MAX3_CREDIT_LINE_COMMITTED}!=null)?($P{pIlmUtil}.formatCurrency($F{MAX3_CREDIT_LINE_COMMITTED},$P{pCurrencyPattern})):"-"]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement key="textField-1" x="706" y="26" width="110" height="16" uuid="7a1d1e81-f359-4a02-9e07-d32631086cce">
						<property name="local_mesure_unitheight" value="pixel"/>
						<property name="local_mesure_unitx" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle"/>
					<textFieldExpression><![CDATA[($F{AVG_CREDIT_LINE_COMMITTED}!=null)?($P{pIlmUtil}.formatCurrency($F{AVG_CREDIT_LINE_COMMITTED},$P{pCurrencyPattern})):"-"]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement key="textField" positionType="Float" x="10" y="42" width="219" height="16" forecolor="#000000" backcolor="#FFFFFF" uuid="6a1c361a-db66-448f-bc3d-31cf71fad673">
						<property name="local_mesure_unitheight" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement>
						<font fontName="SansSerif" size="10" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$P{pDictionary_Data}.get("pOF_WHICH_PEAK_USAGE")]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement key="textField-1" x="350" y="42" width="110" height="16" uuid="b6203190-9616-44c4-bf2a-18852d4a641d">
						<property name="local_mesure_unitheight" value="pixel"/>
						<property name="local_mesure_unitx" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle"/>
					<textFieldExpression><![CDATA[($F{MAX1_PEAK_CREDIT_LINE_USED}!=null)?($P{pIlmUtil}.formatCurrency($F{MAX1_PEAK_CREDIT_LINE_USED},$P{pCurrencyPattern})):"-"]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement key="textField-1" x="465" y="42" width="110" height="16" uuid="f3ef67ca-5fc3-4e36-9d6b-801d8eef0373">
						<property name="local_mesure_unitheight" value="pixel"/>
						<property name="local_mesure_unitx" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle"/>
					<textFieldExpression><![CDATA[($F{MAX2_PEAK_CREDIT_LINE_USED}!=null)?($P{pIlmUtil}.formatCurrency($F{MAX2_PEAK_CREDIT_LINE_USED},$P{pCurrencyPattern})):"-"]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement key="textField-1" x="580" y="42" width="110" height="16" uuid="3aa031a1-7fbe-4fe9-92f3-93d3d57df578">
						<property name="local_mesure_unitheight" value="pixel"/>
						<property name="local_mesure_unitx" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle"/>
					<textFieldExpression><![CDATA[($F{MAX3_PEAK_CREDIT_LINE_USED}!=null)?($P{pIlmUtil}.formatCurrency($F{MAX3_PEAK_CREDIT_LINE_USED},$P{pCurrencyPattern})):"-"]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement key="textField-1" x="706" y="42" width="110" height="16" uuid="6eaa7e93-9f7b-4fb0-b440-94c0871625d8">
						<property name="local_mesure_unitheight" value="pixel"/>
						<property name="local_mesure_unitx" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle"/>
					<textFieldExpression><![CDATA[($F{AVG_PEAK_CREDIT_LINE_USED}!=null)?($P{pIlmUtil}.formatCurrency($F{AVG_PEAK_CREDIT_LINE_USED},$P{pCurrencyPattern})):"-"]]></textFieldExpression>
				</textField>
			</frame>
		</band>
		<band height="43">
			<printWhenExpression><![CDATA[$F{DATA_STATUS}.equals("NO_DATA")]]></printWhenExpression>
			<textField pattern="" isBlankWhenNull="false">
				<reportElement key="textField" mode="Opaque" x="142" y="3" width="534" height="40" forecolor="#000000" backcolor="#FFFFFF" uuid="26a2c019-449b-4caa-a9ca-cb0dfe76e1de"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="12" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{pDictionary_Data}.get("pNO_DATA_FOUND")]]></textFieldExpression>
			</textField>
		</band>
		<band height="8">
			<printWhenExpression><![CDATA[$F{CURRENT_ROW} != $F{TOTAL_ROWS}]]></printWhenExpression>
			<break>
				<reportElement x="0" y="0" width="100" height="1" uuid="4a9239fd-4c3f-4fa3-96da-0a4b662b24c9"/>
			</break>
		</band>
	</detail>
	<columnFooter>
		<band splitType="Stretch"/>
	</columnFooter>
	<pageFooter>
		<band height="27" splitType="Stretch">
			<textField pattern="" isBlankWhenNull="false">
				<reportElement key="textField" x="614" y="3" width="170" height="19" forecolor="#000000" backcolor="#FFFFFF" uuid="8779fed0-2eaa-4a1f-9299-aeaab4e7360f">
					<property name="local_mesure_unitx" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right">
					<font size="10"/>
				</textElement>
				<textFieldExpression><![CDATA["Page " + $V{PAGE_NUMBER} + " " +$P{pDictionary_Data}.get("pOF_LABEL")]]></textFieldExpression>
			</textField>
			<textField evaluationTime="Report" pattern="" isBlankWhenNull="false">
				<reportElement key="textField" x="786" y="3" width="36" height="19" forecolor="#000000" backcolor="#FFFFFF" uuid="8e2cf6ed-3427-460c-9c7d-60af3e6bcc51">
					<property name="local_mesure_unitx" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement>
					<font size="10"/>
				</textElement>
				<textFieldExpression><![CDATA["" + $V{PAGE_NUMBER}]]></textFieldExpression>
			</textField>
			<line>
				<reportElement key="line" x="0" y="3" width="825" height="1" uuid="df6f4533-86ef-4941-acc8-a81ad9abfde9">
					<property name="local_mesure_unitwidth" value="pixel"/>
				</reportElement>
			</line>
			<textField pattern="dd/MM/yyyy" isBlankWhenNull="false">
				<reportElement key="textField" x="20" y="4" width="209" height="19" forecolor="#000000" backcolor="#FFFFFF" uuid="d735ce5a-bfd7-4a61-b612-835e360d2836"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement>
					<font size="10"/>
				</textElement>
				<textFieldExpression><![CDATA[new SimpleDateFormat($P{pDateFormat}).format(new Date())]]></textFieldExpression>
			</textField>
		</band>
	</pageFooter>
	<summary>
		<band height="34" splitType="Prevent">
			<textField>
				<reportElement key="textField-3" x="280" y="14" width="250" height="20" uuid="4cb7cc13-5924-44c2-900f-f0c80acf194b"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center">
					<font size="12"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{pDictionary_Data}.get("pEND_OF_REPORT_LABEL")]]></textFieldExpression>
			</textField>
		</band>
	</summary>
</jasperReport>
