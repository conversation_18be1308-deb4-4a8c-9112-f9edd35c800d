<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 4.0.2-->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="SubReportILMIntraliquitityDateRange" pageWidth="842" pageHeight="595" orientation="Landscape" columnWidth="800" leftMargin="0" rightMargin="10" topMargin="10" bottomMargin="10">
	<property name="ireport.scriptlethandling" value="0"/>
	<property name="ireport.encoding" value="UTF-8"/>
	<property name="ireport.zoom" value="1.0"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<import value="net.sf.jasperreports.engine.*"/>
	<import value="java.util.*"/>
	<import value="net.sf.jasperreports.engine.data.*"/>
	<subDataset name="dataset1">
		<parameter name="ppHOST_ID" class="java.lang.String"/>
		<parameter name="ppROLE_ID" class="java.lang.String"/>
		<parameter name="ppENTITY_ID" class="java.lang.String"/>
		<parameter name="ppCCY" class="java.lang.String"/>
		<parameter name="ppILM_GROUP_ID" class="java.lang.String"/>
		<parameter name="ppVALUE_DATE" class="java.util.Date"/>
		<parameter name="ppVALUE_DATE_END" class="java.util.Date"/>
		<parameter name="ppVALUE_DATE_MIN1" class="java.util.Date"/>
		<parameter name="ppVALUE_DATE_MIN2" class="java.util.Date"/>
		<parameter name="ppVALUE_DATE_MIN3" class="java.util.Date"/>
		<parameter name="ppSERIES_IDENTIFIER" class="java.lang.String"/>
		<parameter name="ppDB_LINK" class="java.lang.String"/>
		<parameter name="ppILM_UTIL" class="org.swallow.reports.dao.hibernate.ReportsDAOHibernate.ILMReportUtils"/>
		<parameter name="ppCURRENCY_PATTERN" class="java.lang.String"/>
		<parameter name="ppCCY_MULTIPLIER_VALUE" class="java.lang.Double"/>
		<queryString>
			<![CDATA[SELECT min1.attribute_id,
							min1.attribute_name||' ('||DECODE(min1.contribute_total, 'PLUS', '+', 'MINUS', '-', '')||')' ATTRIBUTE_LABEL,
						    min1.avg_num_value/$P{ppCCY_MULTIPLIER_VALUE} AS  VALUE_MIN1 ,
						    min2.avg_num_value/$P{ppCCY_MULTIPLIER_VALUE} AS  VALUE_MIN2 ,
						    min3.avg_num_value/$P{ppCCY_MULTIPLIER_VALUE} AS  VALUE_MIN3 ,
						    avg.avg_num_value/$P{ppCCY_MULTIPLIER_VALUE}  AS  AVG_VALUE
					FROM TABLE (pkg_ilm_rep.fn_avg_attrs (
							$P{ppHOST_ID},
							$P{ppENTITY_ID},
							$P{ppCCY},
							$P{ppILM_GROUP_ID},
							$P{ppSERIES_IDENTIFIER},
							$P{ppDB_LINK},
							$P{ppVALUE_DATE_MIN1},
							$P{ppVALUE_DATE_MIN1},
							$P{ppROLE_ID})) min1,
					    TABLE (pkg_ilm_rep.fn_avg_attrs (
					        $P{ppHOST_ID},
							$P{ppENTITY_ID},
							$P{ppCCY},
							$P{ppILM_GROUP_ID},
							$P{ppSERIES_IDENTIFIER},
							$P{ppDB_LINK},
							$P{ppVALUE_DATE_MIN2},
							$P{ppVALUE_DATE_MIN2},
							$P{ppROLE_ID})) min2,
					   TABLE (pkg_ilm_rep.fn_avg_attrs (
					  	    $P{ppHOST_ID},
							$P{ppENTITY_ID},
							$P{ppCCY},
							$P{ppILM_GROUP_ID},
							$P{ppSERIES_IDENTIFIER},
							$P{ppDB_LINK},
							$P{ppVALUE_DATE_MIN3},
							$P{ppVALUE_DATE_MIN3},
							$P{ppROLE_ID})) min3,
				       TABLE (pkg_ilm_rep.fn_avg_attrs (
				       		$P{ppHOST_ID},
							$P{ppENTITY_ID},
							$P{ppCCY},
							$P{ppILM_GROUP_ID},
							$P{ppSERIES_IDENTIFIER},
							$P{ppDB_LINK},
							$P{ppVALUE_DATE},
							$P{ppVALUE_DATE_END},
							$P{ppROLE_ID})) avg
					 WHERE min1.attribute_id=min2.attribute_id
    					AND min1.attribute_id=min3.attribute_id
   						AND min1.attribute_id=avg.attribute_id]]>
		</queryString>
		<field name="ATTRIBUTE_LABEL" class="java.lang.String"/>
		<field name="VALUE_MIN1" class="java.lang.Double"/>
		<field name="VALUE_MIN2" class="java.lang.Double"/>
		<field name="VALUE_MIN3" class="java.lang.Double"/>
		<field name="AVG_VALUE" class="java.lang.Double"/>
	</subDataset>
	<parameter name="P_HOST_ID" class="java.lang.String"/>
	<parameter name="P_ROLE_ID" class="java.lang.String"/>
	<parameter name="p_ENTITY_ID" class="java.lang.String"/>
	<parameter name="p_VALUE_DATE" class="java.util.Date"/>
	<parameter name="p_VALUE_DATE_MIN1" class="java.util.Date"/>
	<parameter name="p_VALUE_DATE_MIN2" class="java.util.Date"/>
	<parameter name="p_VALUE_DATE_MIN3" class="java.util.Date"/>
	<parameter name="p_VALUE_DATE_END" class="java.util.Date"/>
	<parameter name="p_CCY" class="java.lang.String"/>
	<parameter name="p_CURRENCY_PATTERN" class="java.lang.String"/>
	<parameter name="p_ILM_GROUP_ID" class="java.lang.String"/>
	<parameter name="p_SERIES_IDENTIFIER" class="java.lang.String"/>
	<parameter name="p_DB_LINK" class="java.lang.String"/>
	<parameter name="p_Dictionary_Data" class="java.util.HashMap"/>
	<parameter name="p_ILM_UTIL" class="org.swallow.reports.dao.hibernate.ReportsDAOHibernate.ILMReportUtils"/>
	<parameter name="p_CCY_MULTIPLIER_VALUE" class="java.lang.Double"/>
	<queryString>
		<![CDATA[SELECT 	    min1.AVG_EXTERNAL_SOD/$P{p_CCY_MULTIPLIER_VALUE} AS MIN1_AVG_EXTERNAL_SOD,
								min1.AVG_LGST_POS_NET_CUM/$P{p_CCY_MULTIPLIER_VALUE} AS MIN1_AVG_LGST_POS_NET_CUM,
								min1.AVG_LGST_NEG_NET_CUM/$P{p_CCY_MULTIPLIER_VALUE} AS MIN1_AVG_LGST_NEG_NET_CUM,
								min1.AVG_INFLOW/$P{p_CCY_MULTIPLIER_VALUE} AS MIN1_AVG_INFLOW,
								min1.AVG_OUTFLOW/$P{p_CCY_MULTIPLIER_VALUE} AS MIN1_AVG_OUTFLOW,
								min1.AVG_CRITICAL_INFLOW/$P{p_CCY_MULTIPLIER_VALUE} AS MIN1_AVG_CRITICAL_INFLOW,
								min1.AVG_CRITICAL_OUTFLOW/$P{p_CCY_MULTIPLIER_VALUE} AS MIN1_AVG_CRITICAL_OUTFLOW,
								min1.AVG_COLLATERAL/$P{p_CCY_MULTIPLIER_VALUE} AS MIN1_AVG_COLLATERAL,
								min1.AVG_CREDIT_LINE_TOTAL/$P{p_CCY_MULTIPLIER_VALUE} AS MIN1_AVG_CREDIT_LINE_TOTAL,
								min1.AVG_CREDIT_LINE_SECURED/$P{p_CCY_MULTIPLIER_VALUE} AS MIN1_AVG_CREDIT_LINE_SECURED,
								min1.AVG_CREDIT_LINE_COMMITTED/$P{p_CCY_MULTIPLIER_VALUE} AS MIN1_AVG_CREDIT_LINE_COMMITTED,
								min1.AVG_UNENCUMBERED_LIQUID_ASSETS/$P{p_CCY_MULTIPLIER_VALUE} AS MIN1_AVG_UNENCUMBERED_LIQ_ASS,
								min1.AVG_OTHER_TOTAL/$P{p_CCY_MULTIPLIER_VALUE}  AS MIN1_AVG_OTHER_TOTAL,

							    min2.AVG_EXTERNAL_SOD/$P{p_CCY_MULTIPLIER_VALUE} AS MIN2_AVG_EXTERNAL_SOD,
								min2.AVG_LGST_POS_NET_CUM/$P{p_CCY_MULTIPLIER_VALUE} AS MIN2_AVG_LGST_POS_NET_CUM,
								min2.AVG_LGST_NEG_NET_CUM/$P{p_CCY_MULTIPLIER_VALUE} AS MIN2_AVG_LGST_NEG_NET_CUM,
								min2.AVG_INFLOW/$P{p_CCY_MULTIPLIER_VALUE} AS MIN2_AVG_INFLOW,
								min2.AVG_OUTFLOW/$P{p_CCY_MULTIPLIER_VALUE} AS MIN2_AVG_OUTFLOW,
								min2.AVG_CRITICAL_INFLOW/$P{p_CCY_MULTIPLIER_VALUE} AS MIN2_AVG_CRITICAL_INFLOW,
								min2.AVG_CRITICAL_OUTFLOW/$P{p_CCY_MULTIPLIER_VALUE} AS MIN2_AVG_CRITICAL_OUTFLOW,
								min2.AVG_COLLATERAL/$P{p_CCY_MULTIPLIER_VALUE} AS MIN2_AVG_COLLATERAL,
								min2.AVG_CREDIT_LINE_TOTAL/$P{p_CCY_MULTIPLIER_VALUE} AS MIN2_AVG_CREDIT_LINE_TOTAL,
								min2.AVG_CREDIT_LINE_SECURED/$P{p_CCY_MULTIPLIER_VALUE} AS MIN2_AVG_CREDIT_LINE_SECURED,
								min2.AVG_CREDIT_LINE_COMMITTED/$P{p_CCY_MULTIPLIER_VALUE} AS MIN2_AVG_CREDIT_LINE_COMMITTED,
								min2.AVG_UNENCUMBERED_LIQUID_ASSETS/$P{p_CCY_MULTIPLIER_VALUE} AS MIN2_AVG_UNENCUMBERED_LIQ_ASS,
								min2.AVG_OTHER_TOTAL/$P{p_CCY_MULTIPLIER_VALUE}  AS MIN2_AVG_OTHER_TOTAL,

							    min3.AVG_EXTERNAL_SOD/$P{p_CCY_MULTIPLIER_VALUE} AS MIN3_AVG_EXTERNAL_SOD,
								min3.AVG_LGST_POS_NET_CUM/$P{p_CCY_MULTIPLIER_VALUE} AS MIN3_AVG_LGST_POS_NET_CUM,
								min3.AVG_LGST_NEG_NET_CUM/$P{p_CCY_MULTIPLIER_VALUE} AS MIN3_AVG_LGST_NEG_NET_CUM,
								min3.AVG_INFLOW/$P{p_CCY_MULTIPLIER_VALUE} AS MIN3_AVG_INFLOW,
								min3.AVG_OUTFLOW/$P{p_CCY_MULTIPLIER_VALUE} AS MIN3_AVG_OUTFLOW,
								min3.AVG_CRITICAL_INFLOW/$P{p_CCY_MULTIPLIER_VALUE} AS MIN3_AVG_CRITICAL_INFLOW,
								min3.AVG_CRITICAL_OUTFLOW/$P{p_CCY_MULTIPLIER_VALUE} AS MIN3_AVG_CRITICAL_OUTFLOW,
								min3.AVG_COLLATERAL/$P{p_CCY_MULTIPLIER_VALUE} AS MIN3_AVG_COLLATERAL,
								min3.AVG_CREDIT_LINE_TOTAL/$P{p_CCY_MULTIPLIER_VALUE} AS MIN3_AVG_CREDIT_LINE_TOTAL,
								min3.AVG_CREDIT_LINE_SECURED/$P{p_CCY_MULTIPLIER_VALUE} AS MIN3_AVG_CREDIT_LINE_SECURED,
								min3.AVG_CREDIT_LINE_COMMITTED/$P{p_CCY_MULTIPLIER_VALUE} AS MIN3_AVG_CREDIT_LINE_COMMITTED,
								min3.AVG_UNENCUMBERED_LIQUID_ASSETS/$P{p_CCY_MULTIPLIER_VALUE} AS MIN3_AVG_UNENCUMBERED_LIQ_ASS,
								min3.AVG_OTHER_TOTAL/$P{p_CCY_MULTIPLIER_VALUE}  AS MIN3_AVG_OTHER_TOTAL,

							    avg.AVG_EXTERNAL_SOD/$P{p_CCY_MULTIPLIER_VALUE} AS AVG_EXTERNAL_SOD,
								avg.AVG_LGST_POS_NET_CUM/$P{p_CCY_MULTIPLIER_VALUE} AS AVG_LGST_POS_NET_CUM,
								avg.AVG_LGST_NEG_NET_CUM/$P{p_CCY_MULTIPLIER_VALUE} AS AVG_LGST_NEG_NET_CUM,
								avg.AVG_INFLOW/$P{p_CCY_MULTIPLIER_VALUE} AS AVG_INFLOW,
								avg.AVG_OUTFLOW/$P{p_CCY_MULTIPLIER_VALUE} AS AVG_OUTFLOW,
								avg.AVG_CRITICAL_INFLOW/$P{p_CCY_MULTIPLIER_VALUE} AS AVG_CRITICAL_INFLOW,
								avg.AVG_CRITICAL_OUTFLOW/$P{p_CCY_MULTIPLIER_VALUE} AS AVG_CRITICAL_OUTFLOW,
								avg.AVG_COLLATERAL/$P{p_CCY_MULTIPLIER_VALUE} AS AVG_COLLATERAL,
								avg.AVG_CREDIT_LINE_TOTAL/$P{p_CCY_MULTIPLIER_VALUE} AS AVG_CREDIT_LINE_TOTAL,
								avg.AVG_CREDIT_LINE_SECURED/$P{p_CCY_MULTIPLIER_VALUE} AS AVG_CREDIT_LINE_SECURED,
								avg.AVG_CREDIT_LINE_COMMITTED/$P{p_CCY_MULTIPLIER_VALUE} AS AVG_CREDIT_LINE_COMMITTED,
								avg.AVG_UNENCUMBERED_LIQUID_ASSETS/$P{p_CCY_MULTIPLIER_VALUE} AS AVG_UNENCUMBERED_LIQ_ASS,
								avg.AVG_OTHER_TOTAL/$P{p_CCY_MULTIPLIER_VALUE}  AS AVG_OTHER_TOTAL

			     FROM
			     TABLE (pkg_ilm_rep.fn_avg_main_data (
			    			   $P{P_HOST_ID},
							   $P{p_ENTITY_ID},
							   $P{p_CCY},
							   $P{p_ILM_GROUP_ID},
							   $P{p_SERIES_IDENTIFIER},
							   $P{p_DB_LINK},
							   $P{p_VALUE_DATE_MIN1},
							   $P{p_VALUE_DATE_MIN1},
							   $P{P_ROLE_ID})) min1,

			     TABLE (pkg_ilm_rep.fn_avg_main_data (
			    			   $P{P_HOST_ID},
							   $P{p_ENTITY_ID},
							   $P{p_CCY},
							   $P{p_ILM_GROUP_ID},
							   $P{p_SERIES_IDENTIFIER},
							   $P{p_DB_LINK},
							   $P{p_VALUE_DATE_MIN2},
							   $P{p_VALUE_DATE_MIN2},
							   $P{P_ROLE_ID})) min2,

			     TABLE (pkg_ilm_rep.fn_avg_main_data (
			    			   $P{P_HOST_ID},
							   $P{p_ENTITY_ID},
							   $P{p_CCY},
							   $P{p_ILM_GROUP_ID},
							   $P{p_SERIES_IDENTIFIER},
							   $P{p_DB_LINK},
							   $P{p_VALUE_DATE_MIN3},
							   $P{p_VALUE_DATE_MIN3},
							   $P{P_ROLE_ID})) min3,

			     TABLE (pkg_ilm_rep.fn_avg_main_data (
			                   $P{P_HOST_ID},
							   $P{p_ENTITY_ID},
							   $P{p_CCY},
							   $P{p_ILM_GROUP_ID},
							   $P{p_SERIES_IDENTIFIER},
							   $P{p_DB_LINK},
							   $P{p_VALUE_DATE},
							   $P{p_VALUE_DATE_END},
							   $P{P_ROLE_ID})) avg]]>
	</queryString>
	<field name="MIN1_AVG_EXTERNAL_SOD" class="java.lang.Double"/>
	<field name="MIN1_AVG_LGST_POS_NET_CUM" class="java.lang.Double"/>
	<field name="MIN1_AVG_LGST_NEG_NET_CUM" class="java.lang.Double"/>
	<field name="MIN1_AVG_INFLOW" class="java.lang.Double"/>
	<field name="MIN1_AVG_OUTFLOW" class="java.lang.Double"/>
	<field name="MIN1_AVG_CRITICAL_INFLOW" class="java.lang.Double"/>
	<field name="MIN1_AVG_CRITICAL_OUTFLOW" class="java.lang.Double"/>
	<field name="MIN1_AVG_COLLATERAL" class="java.lang.Double"/>
	<field name="MIN1_AVG_CREDIT_LINE_TOTAL" class="java.lang.Double"/>
	<field name="MIN1_AVG_CREDIT_LINE_SECURED" class="java.lang.Double"/>
	<field name="MIN1_AVG_CREDIT_LINE_COMMITTED" class="java.lang.Double"/>
	<field name="MIN1_AVG_UNENCUMBERED_LIQ_ASS" class="java.lang.Double"/>
	<field name="MIN1_AVG_OTHER_TOTAL" class="java.lang.Double"/>
	<field name="MIN2_AVG_EXTERNAL_SOD" class="java.lang.Double"/>
	<field name="MIN2_AVG_LGST_POS_NET_CUM" class="java.lang.Double"/>
	<field name="MIN2_AVG_LGST_NEG_NET_CUM" class="java.lang.Double"/>
	<field name="MIN2_AVG_INFLOW" class="java.lang.Double"/>
	<field name="MIN2_AVG_OUTFLOW" class="java.lang.Double"/>
	<field name="MIN2_AVG_CRITICAL_INFLOW" class="java.lang.Double"/>
	<field name="MIN2_AVG_CRITICAL_OUTFLOW" class="java.lang.Double"/>
	<field name="MIN2_AVG_COLLATERAL" class="java.lang.Double"/>
	<field name="MIN2_AVG_CREDIT_LINE_TOTAL" class="java.lang.Double"/>
	<field name="MIN2_AVG_CREDIT_LINE_SECURED" class="java.lang.Double"/>
	<field name="MIN2_AVG_CREDIT_LINE_COMMITTED" class="java.lang.Double"/>
	<field name="MIN2_AVG_UNENCUMBERED_LIQ_ASS" class="java.lang.Double"/>
	<field name="MIN3_AVG_EXTERNAL_SOD" class="java.lang.Double"/>
	<field name="MIN3_AVG_LGST_POS_NET_CUM" class="java.lang.Double"/>
	<field name="MIN3_AVG_LGST_NEG_NET_CUM" class="java.lang.Double"/>
	<field name="MIN3_AVG_INFLOW" class="java.lang.Double"/>
	<field name="MIN3_AVG_OUTFLOW" class="java.lang.Double"/>
	<field name="MIN3_AVG_CRITICAL_INFLOW" class="java.lang.Double"/>
	<field name="MIN3_AVG_CRITICAL_OUTFLOW" class="java.lang.Double"/>
	<field name="MIN3_AVG_COLLATERAL" class="java.lang.Double"/>
	<field name="MIN3_AVG_CREDIT_LINE_TOTAL" class="java.lang.Double"/>
	<field name="MIN3_AVG_CREDIT_LINE_SECURED" class="java.lang.Double"/>
	<field name="MIN3_AVG_CREDIT_LINE_COMMITTED" class="java.lang.Double"/>
	<field name="MIN3_AVG_UNENCUMBERED_LIQ_ASS" class="java.lang.Double"/>
	<field name="AVG_UNENCUMBERED_LIQ_ASS" class="java.lang.Double"/>
	<field name="AVG_EXTERNAL_SOD" class="java.lang.Double"/>
	<field name="AVG_LGST_POS_NET_CUM" class="java.lang.Double"/>
	<field name="AVG_LGST_NEG_NET_CUM" class="java.lang.Double"/>
	<field name="AVG_INFLOW" class="java.lang.Double"/>
	<field name="AVG_OUTFLOW" class="java.lang.Double"/>
	<field name="AVG_CRITICAL_INFLOW" class="java.lang.Double"/>
	<field name="AVG_CRITICAL_OUTFLOW" class="java.lang.Double"/>
	<field name="AVG_COLLATERAL" class="java.lang.Double"/>
	<field name="AVG_CREDIT_LINE_TOTAL" class="java.lang.Double"/>
	<field name="AVG_CREDIT_LINE_SECURED" class="java.lang.Double"/>
	<field name="AVG_CREDIT_LINE_COMMITTED" class="java.lang.Double"/>
	<field name="MIN2_AVG_OTHER_TOTAL" class="java.lang.Double"/>
	<field name="MIN3_AVG_OTHER_TOTAL" class="java.lang.Double"/>
	<field name="AVG_OTHER_TOTAL" class="java.lang.Double"/>
	<background>
		<band splitType="Stretch"/>
	</background>
	<pageHeader>
		<band splitType="Stretch"/>
	</pageHeader>
	<detail>
		<band height="132" splitType="Stretch">
			<frame>
				<reportElement positionType="Float" mode="Opaque" x="0" y="0" width="820" height="50" backcolor="#D9D9D9"/>
				<textField>
					<reportElement key="textField" positionType="Float" x="5" y="0" width="190" height="16" forecolor="#000000" backcolor="#FFFFFF">
						<property name="local_mesure_unitheight" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement>
						<font fontName="SansSerif" size="10" isBold="false"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[$P{p_Dictionary_Data}.get("pBALANCES")]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement key="textField-1" x="318" y="0" width="142" height="16">
						<property name="local_mesure_unitheight" value="pixel"/>
						<property name="local_mesure_unitx" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle"/>
					<textFieldExpression class="java.lang.String"><![CDATA[($F{MIN1_AVG_EXTERNAL_SOD}!=null)?($P{p_ILM_UTIL}.formatCurrency($F{MIN1_AVG_EXTERNAL_SOD},$P{p_CURRENCY_PATTERN})):"-"]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement key="textField-1" x="434" y="0" width="142" height="16">
						<property name="local_mesure_unitheight" value="pixel"/>
						<property name="local_mesure_unitx" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle"/>
					<textFieldExpression class="java.lang.String"><![CDATA[($F{MIN2_AVG_EXTERNAL_SOD}!=null)?($P{p_ILM_UTIL}.formatCurrency($F{MIN2_AVG_EXTERNAL_SOD},$P{p_CURRENCY_PATTERN})):"-"]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement key="textField-1" x="548" y="0" width="142" height="16">
						<property name="local_mesure_unitheight" value="pixel"/>
						<property name="local_mesure_unitx" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle"/>
					<textFieldExpression class="java.lang.String"><![CDATA[($F{MIN3_AVG_EXTERNAL_SOD}!=null)?($P{p_ILM_UTIL}.formatCurrency($F{MIN3_AVG_EXTERNAL_SOD},$P{p_CURRENCY_PATTERN})):"-"]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement key="textField-1" x="663" y="0" width="142" height="16">
						<property name="local_mesure_unitheight" value="pixel"/>
						<property name="local_mesure_unitx" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle"/>
					<textFieldExpression class="java.lang.String"><![CDATA[($F{AVG_EXTERNAL_SOD}!=null)?($P{p_ILM_UTIL}.formatCurrency($F{AVG_EXTERNAL_SOD},$P{p_CURRENCY_PATTERN})):"-"]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement key="textField" positionType="Float" x="5" y="16" width="190" height="16" forecolor="#000000" backcolor="#FFFFFF">
						<property name="local_mesure_unitheight" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement>
						<font fontName="SansSerif" size="10" isBold="false"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[$P{p_Dictionary_Data}.get("pCOLLATERAL")]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement key="textField-1" x="318" y="16" width="142" height="16">
						<property name="local_mesure_unitheight" value="pixel"/>
						<property name="local_mesure_unitx" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="local_mesure_unity" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle"/>
					<textFieldExpression class="java.lang.String"><![CDATA[($F{MIN1_AVG_COLLATERAL}!=null)?($P{p_ILM_UTIL}.formatCurrency($F{MIN1_AVG_COLLATERAL},$P{p_CURRENCY_PATTERN})):"-"]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement key="textField-1" x="434" y="16" width="142" height="16">
						<property name="local_mesure_unitheight" value="pixel"/>
						<property name="local_mesure_unitx" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="local_mesure_unity" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle"/>
					<textFieldExpression class="java.lang.String"><![CDATA[($F{MIN2_AVG_COLLATERAL}!=null)?($P{p_ILM_UTIL}.formatCurrency($F{MIN2_AVG_COLLATERAL},$P{p_CURRENCY_PATTERN})):"-"]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement key="textField-1" x="548" y="16" width="142" height="16">
						<property name="local_mesure_unitheight" value="pixel"/>
						<property name="local_mesure_unitx" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="local_mesure_unity" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle"/>
					<textFieldExpression class="java.lang.String"><![CDATA[($F{MIN3_AVG_COLLATERAL}!=null)?($P{p_ILM_UTIL}.formatCurrency($F{MIN3_AVG_COLLATERAL},$P{p_CURRENCY_PATTERN})):"-"]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement key="textField-1" x="663" y="16" width="142" height="16">
						<property name="local_mesure_unitheight" value="pixel"/>
						<property name="local_mesure_unitx" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="local_mesure_unity" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle"/>
					<textFieldExpression class="java.lang.String"><![CDATA[($F{AVG_COLLATERAL}!=null)?($P{p_ILM_UTIL}.formatCurrency($F{AVG_COLLATERAL},$P{p_CURRENCY_PATTERN})):"-"]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement key="textField" positionType="Float" x="5" y="32" width="190" height="16" forecolor="#000000" backcolor="#FFFFFF">
						<property name="local_mesure_unitheight" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement>
						<font fontName="SansSerif" size="10" isBold="false"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[$P{p_Dictionary_Data}.get("pTOTAL_CREDIT_LINES_AVAILABLE")]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement key="textField-1" x="318" y="32" width="142" height="16"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle"/>
					<textFieldExpression class="java.lang.String"><![CDATA[($F{MIN1_AVG_CREDIT_LINE_TOTAL}!=null)?($P{p_ILM_UTIL}.formatCurrency($F{MIN1_AVG_CREDIT_LINE_TOTAL},$P{p_CURRENCY_PATTERN})):"-"]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement key="textField-1" x="434" y="32" width="142" height="16"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle"/>
					<textFieldExpression class="java.lang.String"><![CDATA[($F{MIN2_AVG_CREDIT_LINE_TOTAL}!=null)?($P{p_ILM_UTIL}.formatCurrency($F{MIN2_AVG_CREDIT_LINE_TOTAL},$P{p_CURRENCY_PATTERN})):"-"]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement key="textField-1" x="548" y="32" width="142" height="16"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle"/>
					<textFieldExpression class="java.lang.String"><![CDATA[($F{MIN3_AVG_CREDIT_LINE_TOTAL}!=null)?($P{p_ILM_UTIL}.formatCurrency($F{MIN3_AVG_CREDIT_LINE_TOTAL},$P{p_CURRENCY_PATTERN})):"-"]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement key="textField-1" x="663" y="32" width="142" height="16">
						<property name="local_mesure_unitheight" value="pixel"/>
						<property name="local_mesure_unitx" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="local_mesure_unity" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle"/>
					<textFieldExpression class="java.lang.String"><![CDATA[($F{AVG_CREDIT_LINE_TOTAL}!=null)?($P{p_ILM_UTIL}.formatCurrency($F{AVG_CREDIT_LINE_TOTAL},$P{p_CURRENCY_PATTERN})):"-"]]></textFieldExpression>
				</textField>
			</frame>
			<frame>
				<reportElement positionType="Float" mode="Opaque" x="0" y="48" width="820" height="52" backcolor="#F2F2F2"/>
				<textField>
					<reportElement key="textField" positionType="Float" x="5" y="0" width="190" height="16" forecolor="#000000" backcolor="#FFFFFF"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement>
						<font fontName="SansSerif" size="10" isBold="false" isItalic="true"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[$P{p_Dictionary_Data}.get("pOF_WHICH")]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement key="textField" positionType="Float" x="20" y="16" width="190" height="16" forecolor="#000000" backcolor="#FFFFFF"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement>
						<font fontName="SansSerif" size="10" isBold="false"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[$P{p_Dictionary_Data}.get("pCREDIT_LINES_SECURED")]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement key="textField-1" x="318" y="16" width="142" height="16"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle"/>
					<textFieldExpression class="java.lang.String"><![CDATA[($F{MIN1_AVG_CREDIT_LINE_SECURED}!=null)?($P{p_ILM_UTIL}.formatCurrency($F{MIN1_AVG_CREDIT_LINE_SECURED},$P{p_CURRENCY_PATTERN})):"-"]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement key="textField-1" x="434" y="16" width="142" height="16"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle"/>
					<textFieldExpression class="java.lang.String"><![CDATA[($F{MIN2_AVG_CREDIT_LINE_SECURED}!=null)?($P{p_ILM_UTIL}.formatCurrency($F{MIN2_AVG_CREDIT_LINE_SECURED},$P{p_CURRENCY_PATTERN})):"-"]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement key="textField-1" x="548" y="16" width="142" height="16">
						<property name="local_mesure_unitheight" value="pixel"/>
						<property name="local_mesure_unitx" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="local_mesure_unity" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle"/>
					<textFieldExpression class="java.lang.String"><![CDATA[($F{MIN3_AVG_CREDIT_LINE_SECURED}!=null)?($P{p_ILM_UTIL}.formatCurrency($F{MIN3_AVG_CREDIT_LINE_SECURED},$P{p_CURRENCY_PATTERN})):"-"]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement key="textField-1" x="663" y="16" width="142" height="16">
						<property name="local_mesure_unitheight" value="pixel"/>
						<property name="local_mesure_unitx" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="local_mesure_unity" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle"/>
					<textFieldExpression class="java.lang.String"><![CDATA[($F{AVG_CREDIT_LINE_SECURED}!=null)?($P{p_ILM_UTIL}.formatCurrency($F{AVG_CREDIT_LINE_SECURED},$P{p_CURRENCY_PATTERN})):"-"]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement key="textField" positionType="Float" x="20" y="32" width="190" height="16" forecolor="#000000" backcolor="#FFFFFF">
						<property name="local_mesure_unitheight" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement>
						<font fontName="SansSerif" size="10" isBold="false"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[$P{p_Dictionary_Data}.get("pCREDIT_LINES_COMMITTED")]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement key="textField-1" x="318" y="32" width="142" height="16">
						<property name="local_mesure_unitheight" value="pixel"/>
						<property name="local_mesure_unitx" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="local_mesure_unity" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle"/>
					<textFieldExpression class="java.lang.String"><![CDATA[($F{MIN1_AVG_CREDIT_LINE_COMMITTED}!=null)?($P{p_ILM_UTIL}.formatCurrency($F{MIN1_AVG_CREDIT_LINE_COMMITTED},$P{p_CURRENCY_PATTERN})):"-"]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement key="textField-1" x="434" y="32" width="142" height="16">
						<property name="local_mesure_unitheight" value="pixel"/>
						<property name="local_mesure_unitx" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="local_mesure_unity" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle"/>
					<textFieldExpression class="java.lang.String"><![CDATA[($F{MIN2_AVG_CREDIT_LINE_COMMITTED}!=null)?($P{p_ILM_UTIL}.formatCurrency($F{MIN2_AVG_CREDIT_LINE_COMMITTED},$P{p_CURRENCY_PATTERN})):"-"]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement key="textField-1" x="548" y="32" width="142" height="16">
						<property name="local_mesure_unitheight" value="pixel"/>
						<property name="local_mesure_unitx" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="local_mesure_unity" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle"/>
					<textFieldExpression class="java.lang.String"><![CDATA[($F{MIN3_AVG_CREDIT_LINE_COMMITTED}!=null)?($P{p_ILM_UTIL}.formatCurrency($F{MIN3_AVG_CREDIT_LINE_COMMITTED},$P{p_CURRENCY_PATTERN})):"-"]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement key="textField-1" x="663" y="32" width="142" height="16">
						<property name="local_mesure_unitheight" value="pixel"/>
						<property name="local_mesure_unitx" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="local_mesure_unity" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle"/>
					<textFieldExpression class="java.lang.String"><![CDATA[($F{AVG_CREDIT_LINE_COMMITTED}!=null)?($P{p_ILM_UTIL}.formatCurrency($F{AVG_CREDIT_LINE_COMMITTED},$P{p_CURRENCY_PATTERN})):"-"]]></textFieldExpression>
				</textField>
			</frame>
			<frame>
				<reportElement positionType="Float" mode="Opaque" x="0" y="100" width="820" height="32" backcolor="#D9D9D9"/>
				<textField>
					<reportElement key="textField" positionType="Float" x="5" y="0" width="190" height="16" forecolor="#000000" backcolor="#FFFFFF">
						<property name="local_mesure_unitheight" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement>
						<font fontName="SansSerif" size="10" isBold="false"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[$P{p_Dictionary_Data}.get("pUNENCUMBERED_LIQUID_ASSETS")]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement key="textField-1" x="318" y="0" width="142" height="16">
						<property name="local_mesure_unitheight" value="pixel"/>
						<property name="local_mesure_unitx" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="local_mesure_unity" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle"/>
					<textFieldExpression class="java.lang.String"><![CDATA[($F{MIN1_AVG_UNENCUMBERED_LIQ_ASS}!=null)?($P{p_ILM_UTIL}.formatCurrency($F{MIN1_AVG_UNENCUMBERED_LIQ_ASS},$P{p_CURRENCY_PATTERN})):"-"]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement key="textField-1" x="434" y="0" width="142" height="16">
						<property name="local_mesure_unitheight" value="pixel"/>
						<property name="local_mesure_unitx" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="local_mesure_unity" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle"/>
					<textFieldExpression class="java.lang.String"><![CDATA[($F{MIN2_AVG_UNENCUMBERED_LIQ_ASS}!=null)?($P{p_ILM_UTIL}.formatCurrency($F{MIN2_AVG_UNENCUMBERED_LIQ_ASS},$P{p_CURRENCY_PATTERN})):"-"]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement key="textField-1" x="548" y="0" width="142" height="16">
						<property name="local_mesure_unitheight" value="pixel"/>
						<property name="local_mesure_unitx" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="local_mesure_unity" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle"/>
					<textFieldExpression class="java.lang.String"><![CDATA[($F{MIN3_AVG_UNENCUMBERED_LIQ_ASS}!=null)?($P{p_ILM_UTIL}.formatCurrency($F{MIN3_AVG_UNENCUMBERED_LIQ_ASS},$P{p_CURRENCY_PATTERN})):"-"]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement key="textField-1" x="663" y="0" width="142" height="16">
						<property name="local_mesure_unitheight" value="pixel"/>
						<property name="local_mesure_unitx" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="local_mesure_unity" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle"/>
					<textFieldExpression class="java.lang.String"><![CDATA[($F{AVG_UNENCUMBERED_LIQ_ASS}!=null)?($P{p_ILM_UTIL}.formatCurrency($F{AVG_UNENCUMBERED_LIQ_ASS},$P{p_CURRENCY_PATTERN})):"-"]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement key="textField" positionType="Float" x="5" y="16" width="190" height="16" forecolor="#000000" backcolor="#FFFFFF">
						<property name="local_mesure_unitheight" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement>
						<font fontName="SansSerif" size="10" isBold="false"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[$P{p_Dictionary_Data}.get("pOTHER")]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement key="textField-1" x="318" y="16" width="142" height="16">
						<property name="local_mesure_unitheight" value="pixel"/>
						<property name="local_mesure_unitx" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="local_mesure_unity" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle"/>
					<textFieldExpression class="java.lang.String"><![CDATA[($F{MIN1_AVG_OTHER_TOTAL}!=null)?($P{p_ILM_UTIL}.formatCurrency($F{MIN1_AVG_OTHER_TOTAL},$P{p_CURRENCY_PATTERN})):"-"]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement key="textField-1" x="434" y="16" width="142" height="16">
						<property name="local_mesure_unitheight" value="pixel"/>
						<property name="local_mesure_unitx" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="local_mesure_unity" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle"/>
					<textFieldExpression class="java.lang.String"><![CDATA[($F{MIN2_AVG_OTHER_TOTAL}!=null)?($P{p_ILM_UTIL}.formatCurrency($F{MIN2_AVG_OTHER_TOTAL},$P{p_CURRENCY_PATTERN})):"-"]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement key="textField-1" x="548" y="16" width="142" height="16">
						<property name="local_mesure_unitheight" value="pixel"/>
						<property name="local_mesure_unitx" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="local_mesure_unity" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle"/>
					<textFieldExpression class="java.lang.String"><![CDATA[($F{MIN3_AVG_OTHER_TOTAL}!=null)?($P{p_ILM_UTIL}.formatCurrency($F{MIN3_AVG_OTHER_TOTAL},$P{p_CURRENCY_PATTERN})):"-"]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement key="textField-1" x="663" y="16" width="142" height="16">
						<property name="local_mesure_unitheight" value="pixel"/>
						<property name="local_mesure_unitx" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="local_mesure_unity" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle"/>
					<textFieldExpression class="java.lang.String"><![CDATA[($F{AVG_OTHER_TOTAL}!=null)?($P{p_ILM_UTIL}.formatCurrency($F{AVG_OTHER_TOTAL},$P{p_CURRENCY_PATTERN})):"-"]]></textFieldExpression>
				</textField>
			</frame>
		</band>
		<band height="32">
			<printWhenExpression><![CDATA[$P{p_SERIES_IDENTIFIER}.equals("Standard")]]></printWhenExpression>
			<frame>
				<reportElement positionType="Float" mode="Opaque" x="0" y="0" width="820" height="32" backcolor="#F2F2F2"/>
				<textField>
					<reportElement key="textField" positionType="Float" x="5" y="0" width="190" height="16" forecolor="#000000" backcolor="#FFFFFF">
						<property name="local_mesure_unitheight" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement>
						<font fontName="SansSerif" size="10" isBold="false" isItalic="true"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[$P{p_Dictionary_Data}.get("pOF_WHICH")]]></textFieldExpression>
				</textField>
				<componentElement>
					<reportElement positionType="Float" x="20" y="16" width="800" height="16">
						<property name="com.jaspersoft.studio.layout" value="com.jaspersoft.studio.editor.layout.HorizontalRowLayout"/>
					</reportElement>
					<jr:list xmlns:jr="http://jasperreports.sourceforge.net/jasperreports/components" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports/components http://jasperreports.sourceforge.net/xsd/components.xsd" printOrder="Vertical">
						<datasetRun subDataset="dataset1">
							<datasetParameter name="ppHOST_ID">
								<datasetParameterExpression><![CDATA[$P{P_HOST_ID}]]></datasetParameterExpression>
							</datasetParameter>
							<datasetParameter name="ppROLE_ID">
								<datasetParameterExpression><![CDATA[$P{P_ROLE_ID}]]></datasetParameterExpression>
							</datasetParameter>
							<datasetParameter name="ppENTITY_ID">
								<datasetParameterExpression><![CDATA[$P{p_ENTITY_ID}]]></datasetParameterExpression>
							</datasetParameter>
							<datasetParameter name="ppCCY">
								<datasetParameterExpression><![CDATA[$P{p_CCY}]]></datasetParameterExpression>
							</datasetParameter>
							<datasetParameter name="ppVALUE_DATE">
								<datasetParameterExpression><![CDATA[$P{p_VALUE_DATE}]]></datasetParameterExpression>
							</datasetParameter>
							<datasetParameter name="ppVALUE_DATE_END">
								<datasetParameterExpression><![CDATA[$P{p_VALUE_DATE_END}]]></datasetParameterExpression>
							</datasetParameter>
							<datasetParameter name="ppILM_GROUP_ID">
								<datasetParameterExpression><![CDATA[$P{p_ILM_GROUP_ID}]]></datasetParameterExpression>
							</datasetParameter>
							<datasetParameter name="ppSERIES_IDENTIFIER">
								<datasetParameterExpression><![CDATA[$P{p_SERIES_IDENTIFIER}]]></datasetParameterExpression>
							</datasetParameter>
							<datasetParameter name="ppVALUE_DATE_MIN1">
								<datasetParameterExpression><![CDATA[$P{p_VALUE_DATE_MIN1}]]></datasetParameterExpression>
							</datasetParameter>
							<datasetParameter name="ppVALUE_DATE_MIN2">
								<datasetParameterExpression><![CDATA[$P{p_VALUE_DATE_MIN2}]]></datasetParameterExpression>
							</datasetParameter>
							<datasetParameter name="ppVALUE_DATE_MIN3">
								<datasetParameterExpression><![CDATA[$P{p_VALUE_DATE_MIN3}]]></datasetParameterExpression>
							</datasetParameter>
							<datasetParameter name="ppDB_LINK">
								<datasetParameterExpression><![CDATA[$P{p_DB_LINK}]]></datasetParameterExpression>
							</datasetParameter>
							<datasetParameter name="ppILM_UTIL">
								<datasetParameterExpression><![CDATA[$P{p_ILM_UTIL}]]></datasetParameterExpression>
							</datasetParameter>
							<datasetParameter name="ppCURRENCY_PATTERN">
								<datasetParameterExpression><![CDATA[$P{p_CURRENCY_PATTERN}]]></datasetParameterExpression>
							</datasetParameter>
							<datasetParameter name="ppCCY_MULTIPLIER_VALUE">
								<datasetParameterExpression><![CDATA[$P{p_CCY_MULTIPLIER_VALUE}]]></datasetParameterExpression>
							</datasetParameter>
						</datasetRun>
						<jr:listContents height="16" width="800">
							<textField>
								<reportElement x="0" y="0" width="210" height="16" forecolor="#000000" backcolor="#FFFFFF"/>
								<box>
									<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<bottomPen lineWidth="0.0" lineColor="#000000"/>
									<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								</box>
								<textElement>
									<font fontName="SansSerif" size="10" isBold="false"/>
								</textElement>
								<textFieldExpression class="java.lang.String"><![CDATA[$F{ATTRIBUTE_LABEL}]]></textFieldExpression>
							</textField>
							<textField>
								<reportElement x="298" y="0" width="142" height="16">
									<property name="local_mesure_unitx" value="pixel"/>
								</reportElement>
								<box>
									<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<bottomPen lineWidth="0.0" lineColor="#000000"/>
									<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								</box>
								<textElement textAlignment="Right" verticalAlignment="Middle"/>
								<textFieldExpression class="java.lang.String"><![CDATA[($F{VALUE_MIN1}!=null)?($P{ppILM_UTIL}.formatCurrency($F{VALUE_MIN1},$P{ppCURRENCY_PATTERN})):"-"]]></textFieldExpression>
							</textField>
							<textField>
								<reportElement x="414" y="0" width="142" height="16">
									<property name="local_mesure_unitx" value="pixel"/>
								</reportElement>
								<box>
									<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<bottomPen lineWidth="0.0" lineColor="#000000"/>
									<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								</box>
								<textElement textAlignment="Right" verticalAlignment="Middle"/>
								<textFieldExpression class="java.lang.String"><![CDATA[($F{VALUE_MIN2}!=null)?($P{ppILM_UTIL}.formatCurrency($F{VALUE_MIN2},$P{ppCURRENCY_PATTERN})):"-"]]></textFieldExpression>
							</textField>
							<textField>
								<reportElement x="529" y="0" width="142" height="16">
									<property name="local_mesure_unitx" value="pixel"/>
									<property name="local_mesure_unitheight" value="pixel"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
								</reportElement>
								<box>
									<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<bottomPen lineWidth="0.0" lineColor="#000000"/>
									<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								</box>
								<textElement textAlignment="Right" verticalAlignment="Middle"/>
								<textFieldExpression class="java.lang.String"><![CDATA[($F{VALUE_MIN3}!=null)?($P{ppILM_UTIL}.formatCurrency($F{VALUE_MIN3},$P{ppCURRENCY_PATTERN})):"-"]]></textFieldExpression>
							</textField>
							<textField>
								<reportElement x="644" y="0" width="142" height="16">
									<property name="local_mesure_unitx" value="pixel"/>
									<property name="local_mesure_unitheight" value="pixel"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
								</reportElement>
								<box>
									<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<bottomPen lineWidth="0.0" lineColor="#000000"/>
									<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								</box>
								<textElement textAlignment="Right" verticalAlignment="Middle"/>
								<textFieldExpression class="java.lang.String"><![CDATA[($F{AVG_VALUE}!=null)?($P{ppILM_UTIL}.formatCurrency($F{AVG_VALUE},$P{ppCURRENCY_PATTERN})):"-"]]></textFieldExpression>
							</textField>
						</jr:listContents>
					</jr:list>
				</componentElement>
			</frame>
		</band>
	</detail>
</jasperReport>
