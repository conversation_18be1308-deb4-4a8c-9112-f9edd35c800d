<%@ include file="/taglib.jsp"%>
<%@ page import="org.swallow.util.SwtConstants"%>
<%@ page import="org.swallow.util.SwtUtil"%>
<%@page import="org.swallow.util.LabelValueBean"%>
<html>

<head>
    <meta http-equiv="Content-Type" content="text/html; charset=ISO-8859-1">
    <script type="text/javascript" src="js/jquery.multicolselect.js"></script>
    <title>
        <s:text name="ilmReport.title"/>
    </title>
    <script language="JAVASCRIPT">
        var isSingleDate = true;
	var dateInCcyTimeframe = "${requestScope.dateInCcyTimeframe}";
	var dateInCcyTimeframeAsString = "${requestScope.dateInCcyTimeframeAsString}";
        var maxReportDate = "${requestScope.maxReportDate}";
        var dateValidationMessage = "<s:text name='ilmreport.dateGreaterThanCcyTimeframeDate'/>";
        var dateValidationMessageAll = "<s:text name='ilmreport.dateGreaterThanCcyTimeframeDateAll'/>";
        dateValidationMessage = dateValidationMessage.replace("{0}", maxReportDate);
        dateValidationMessageAll = dateValidationMessageAll.replace("{0}", maxReportDate);
	var globalGrp;
	var configScheduler = "${requestScope.configScheduler}";
	var newILMReportSchedConfig = "${requestScope.newILMReportSchedConfig}";
	var schedulerConfigXML = "${requestScope.schedulerConfigXML}";
	var roleId = "${requestScope.roleId}";
	var cal;
	var costumerPayments = ""; 
	var totalCheked;
	var chekedBox;
	costumerPayments = '${requestScope.usageInSession}'; 
	
	$(window).on("load", function() {
		if (configScheduler == "true") {
			document.forms[0].newILMReportSchedConfig.value=newILMReportSchedConfig;
			document.forms[0].elements["reports.reportType"].disabled='true';
			document.forms[0].elements["reports.reportType"].className='is-disabled';
		}
		var reportType = document.forms[0].elements["reports.reportType"].value;
		if (reportType != "<%=SwtConstants.ILM_REPORT_TYPE_BASEL_B%>") {
			if (document.forms[0].elements["reports.defaultIlmGrp"][0].checked) {
				$("#selectGroup *").attr('disabled', true);
			} else {
				$("#selectGroup *").removeAttr('disabled');
			}
		}
		$("input[type='radio']").on('click', function() {
			document.getElementById("warningCB").style.visibility = "hidden";
			if ($(this).val() == "R") {
				$("#divTo").css("visibility", "visible");
				$("#divToTime").css("visibility", "visible");
				document.forms[0].elements['reports.fromDateAsString'].value ="${requestScope.defaultFirstDateOfPreviousMonth}";
				document.forms[0].elements['reports.toDateAsString'].value ="${requestScope.defaultLastDateOfPreviousMonth}";
				isSingleDate=false;
			} else if ($(this).val() == "S") {
				$("#divTo").css("visibility", "hidden");
				$("#divToTime").css("visibility", "hidden");
				document.forms[0].elements['reports.fromDateAsString'].value ="${requestScope.yesterday}";
				isSingleDate=true;
			}else if ($(this).val() == "default") {
					$("#selectGroup *").attr('disabled',true);
			}else if ($(this).val() == "noDefault") {
				$("#selectGroup *").removeAttr('disabled');
			}else if ($(this).val() == "CB") {
				$("#selectGroup *").removeAttr('disabled');
				if("${requestScope.isCentralBankExist}" == "false"){
					document.getElementById("warningCB").style.visibility = "visible";
				}
			}else if ($(this).val() == "Y") {
				 chekedBox = "L";
				 totalCheked = 1 ;
				 document.forms[0].elements["reports.loroPayments"].disabled = "";	
			 	 document.forms[0].elements["reports.loroPayments"].checked = true;
				 document.forms[0].elements["reports.corporatePayments"].disabled = "";
			 	 document.forms[0].elements["reports.corporatePayments"].checked = false;
			 	 document.forms[0].elements["reports.branchPayments"].disabled = "";
			 	 document.forms[0].elements["reports.branchPayments"].checked = false;
			 	 document.forms[0].elements["reports.otherPayments"].disabled = "";
			 	 document.forms[0].elements["reports.otherPayments"].checked = false;
			}else if ($(this).val() == "N") {
				 chekedBox = "";
				 totalCheked = 0 ;
				 document.forms[0].elements["reports.loroPayments"].disabled = "true";	
			 	 document.forms[0].elements["reports.loroPayments"].checked = "";
				 document.forms[0].elements["reports.corporatePayments"].disabled = "true";
			 	 document.forms[0].elements["reports.corporatePayments"].checked = "";
			 	 document.forms[0].elements["reports.branchPayments"].disabled = "true";
			 	 document.forms[0].elements["reports.branchPayments"].checked = "";
			 	 document.forms[0].elements["reports.otherPayments"].disabled = "true";
			 	 document.forms[0].elements["reports.otherPayments"].checked = ""
			}
		})
	
		
		$("input[type='checkbox']").on('click', function() {
			if (( totalCheked == 1  && chekedBox == $(this).val()) ) {
				event.preventDefault();
				alert('<s:text name="ilmReport.alertSelectPayment"/>');
			}else{
				totalCheked = OneChecked();
			}
			
		})
	});
	function bodyOnLoad() {
		var entityDropBoxElement = new SwSelectBox(document.forms[0]
				.elements["reports.entityId"], document
				.getElementById("entityName"));
		var currencyDropBoxElement = new SwSelectBox(document.forms[0]
				.elements["reports.currencyCode"], document
				.getElementById("currencyDesc"));

		var ilmGroupDropBoxElement = new SwSelectBox(document.forms[0]
				.elements["reports.ilmGroup"], document
				.getElementById("ilmGrpName"));
		
		var scenarioDropBoxElement = new SwSelectBox(document.forms[0]
		.elements["reports.scenario"], document
		.getElementById("scenarioName"));
		setParentChildsFocus();
		
		var entityId = document.forms[0].elements["reports.entityId"].value;
		var ccyCode = document.forms[0].elements["reports.currencyCode"].value;
		var reportType = document.forms[0].elements["reports.reportType"].value;

		if (document.forms[0].elements["reports.singleOrRange"][0].checked) {
			$("#divTo").css("visibility", "hidden");
			$("#divToTime").css("visibility", "hidden");
			if("${requestScope.changeEntity}" == "Y"){
				document.forms[0].elements['reports.fromDateAsString'].value ="${requestScope.yesterday}";
			}
		} else if (document.forms[0].elements["reports.singleOrRange"][1].checked) {
			$("#divTo").css("visibility", "visible");
			$("#divToTime").css("visibility", "visible");
			isSingleDate=false;
			
			if("${requestScope.changeEntity}" == "Y"){
				document.forms[0].elements['reports.fromDateAsString'].value ="${requestScope.firstDateOfPreviousMonth}";
				document.forms[0].elements['reports.toDateAsString'].value ="${requestScope.lastDateOfPreviousMonth}";
			}
		}
		
		if (entityId == "All") {
			document.forms[0].elements["reports.useCcyMultiplier"].disabled = true;
			document.forms[0].elements["reports.useCcyMultiplier"].checked = false;
		} else {
			document.forms[0].elements["reports.useCcyMultiplier"].disabled = false;
		}
		
		if (reportType == "<%=SwtConstants.ILM_REPORT_TYPE_GROUP%>") {
			if (entityId != "All") {
				if (ccyCode != "All") {
					document.forms[0].elements["reports.scenario"].disabled = false;
					var appName = "<%=SwtUtil.appName%>";
					var requestURL = new String('<%=request.getRequestURL()%>');
					var idy = requestURL.indexOf('/' + appName + '/');

					requestURL = requestURL.substring(0, idy + 1);
					requestURL = requestURL + appName
							+ "/intraDayLiquidity.do?method=getGlobalGroup";
					requestURL = requestURL + "&entityId=" + entityId + "&ccyCode=" + ccyCode;
				
					var oXMLHTTP = new XMLHttpRequest();
					oXMLHTTP.open("POST", requestURL, false);
					oXMLHTTP.send();
					globalGrp = new String(oXMLHTTP.responseText);
					document.getElementById('ccyGlobalGrp').innerHTML = '<s:text name="ilmReport.currencyGlobalGroup"/>' + " [" + globalGrp + "]";
				} else {
				    //document.forms[0].elements["reports.scenario"].disabled = true;
					//document.forms[0].elements["reports.scenario"].selectedIndex = 0;
					document.getElementById('ccyGlobalGrp').innerHTML = '<s:text name="ilmReport.currencyGlobalGroup"/>';
					document.getElementById("ilmGrps").disabled = true;
					$('#ilmGrps *').attr("disabled", true);
					document.forms[0].elements["reports.ilmGroup"].className='is-disabled';
				}
			} else {
				//document.forms[0].elements["reports.scenario"].disabled = true;
				//document.forms[0].elements["reports.scenario"].selectedIndex = 0;
				if (document.forms[0].elements["reports.currencyCode"].length == 1){
					alert('<s:text name="ilmanalysismonitor.errorAllOptionNotAvailable"/>');
				}
				document.getElementById('ccyGlobalGrp').innerHTML = '<s:text name="ilmReport.currencyGlobalGroup"/>';
			}
		} else if (reportType == "<%=SwtConstants.ILM_REPORT_TYPE_BASEL_A%>") {
			var entityIdOption = document.forms[0].elements["reports.entityId"].getElementsByTagName("option");
			for (var i = 0; i < entityIdOption.length; i++) {
			  (entityIdOption[i].value == "All") ? entityIdOption[i].disabled = true : entityIdOption[i].disabled = false;
			}
			/*if (ccyCode != "All") {
				document.forms[0].elements["reports.scenario"].disabled = false;
			}else{
				document.forms[0].elements["reports.scenario"].disabled = true;
				document.forms[0].elements["reports.scenario"].selectedIndex = 0;
			}*/
			document.getElementById('ccyGlobalGrp').innerHTML = '<s:text name="ilmReport.currencyGlobalGroup"/>';
			document.getElementById("ilmGrps").disabled = true;
			$('#ilmGrps *').attr("disabled", true);
			document.forms[0].elements["reports.ilmGroup"].className = 'is-disabled';
			if (!document.forms[0].elements["reports.sumInOutFlows"][0].checked && !document.forms[0].elements["reports.sumInOutFlows"][1].checked)
				document.forms[0].elements["reports.sumInOutFlows"][0].checked = true;
			document.forms[0].elements["reports.ilmGroup"].value="";
			document.getElementById("ilmGrpName").innerHTML="";
			if("${requestScope.isCentralBankExist}" == "false" && document.getElementById("radioCB").checked == true){
				document.getElementById("warningCB").style.visibility = "visible";
			}
			
		} else if (reportType == "<%=SwtConstants.ILM_REPORT_TYPE_BASEL_B%>") {
			var entityIdOption = document.forms[0].elements["reports.entityId"].getElementsByTagName("option");
			for (var i = 0; i < entityIdOption.length; i++) {
			  (entityIdOption[i].value == "All") ? entityIdOption[i].disabled = true : entityIdOption[i].disabled = false;
			}
			/*if (ccyCode != "All") {
				document.forms[0].elements["reports.scenario"].disabled = false;
			}else{
				document.forms[0].elements["reports.scenario"].disabled = true;
				document.forms[0].elements["reports.scenario"].selectedIndex = 0;
			}*/
			$('#ccyGlobalGrpRadio *').attr("disabled", true);
			//document.getElementById("ccyGlobalGrpRadio").disabled = true;
			document.forms[0].elements["reports.defaultIlmGrp"][1].checked = true;
			if (!document.forms[0].elements["reports.sumInOutFlows"][0].checked && !document.forms[0].elements["reports.sumInOutFlows"][1].checked)
				document.forms[0].elements["reports.sumInOutFlows"][0].checked = true;
			
		} else if (reportType == "<%=SwtConstants.ILM_REPORT_TYPE_BASEL_C%>") {
			var entityIdOption = document.forms[0].elements["reports.entityId"].getElementsByTagName("option");
			for (var i = 0; i < entityIdOption.length; i++) {
			  (entityIdOption[i].value == "All") ? entityIdOption[i].disabled = true : entityIdOption[i].disabled = false;
			}
			document.getElementById('ccyGlobalGrp').innerHTML = '<s:text name="ilmReport.currencyGlobalGroup"/>';
			document.getElementById("ilmGrps").disabled = true;
			$('#ilmGrps *').attr("disabled", true);
			document.forms[0].elements["reports.ilmGroup"].className = 'is-disabled';
			document.forms[0].elements["reports.scenario"].disabled = true;
			document.forms[0].elements["reports.scenario"].selectedIndex = 0;
			document.getElementById("scenarioName").innerHTML = "Standard";
			document.forms[0].elements["reports.ilmGroup"].value="";
			document.getElementById("ilmGrpName").innerHTML="";
			document.forms[0].costumerPayments.value = '${sessionScope.costumerPayments}';
			if (document.forms[0].elements["reports.costumerPayments"][1].checked) {
				 document.forms[0].elements["reports.loroPayments"].disabled = "";	
				 document.forms[0].elements["reports.corporatePayments"].disabled = "";
			 	 document.forms[0].elements["reports.branchPayments"].disabled = "";
			 	 document.forms[0].elements["reports.otherPayments"].disabled = "";
			
			} else if (document.forms[0].elements["reports.costumerPayments"][0].checked) {
				 document.forms[0].elements["reports.loroPayments"].disabled = "true";	
			 	 document.forms[0].elements["reports.loroPayments"].checked = "";
				 document.forms[0].elements["reports.corporatePayments"].disabled = "true";
			 	 document.forms[0].elements["reports.corporatePayments"].checked = "";
			 	 document.forms[0].elements["reports.branchPayments"].disabled = "true";
			 	 document.forms[0].elements["reports.branchPayments"].checked = "";
			 	 document.forms[0].elements["reports.otherPayments"].disabled = "true";
			 	 document.forms[0].elements["reports.otherPayments"].checked = ""
			}
			
			 totalCheked = OneChecked();

		}else if (reportType == "<%=SwtConstants.ILM_REPORT_TYPE_INTRADAY_RISK_NET_CUM%>") {
			document.getElementById('ccyGlobalGrp').innerHTML = '<s:text name="ilmReport.mainCorrespondentBank"/>';
			document.getElementById("ilmGrps").disabled = true;
			$('#ilmGrps *').attr("disabled", true);
			document.forms[0].elements["reports.ilmGroup"].className = 'is-disabled';
			document.forms[0].elements["reports.ilmGroup"].value="";
			document.getElementById("ilmGrpName").innerHTML="";
			
			if(entityId == "All" || ccyCode == "All"){
// 				document.forms[0].elements["reports.scenario"].disabled = true;
// 				document.forms[0].elements["reports.scenario"].selectedIndex = 0;
				document.forms[0].elements["reports.useCcyMultiplier"].disabled = true;
				document.forms[0].elements["reports.useCcyMultiplier"].checked = false;
			}
			
		}else if (reportType == "<%=SwtConstants.ILM_REPORT_TYPE_MULTI_CURRENCY_ILM_REPORTE_EXCEL%>") {
			if (entityId != "All") {
				if (ccyCode != "All") {
					document.forms[0].elements["reports.scenario"].disabled = false;
					var appName = "<%=SwtUtil.appName%>";
					var requestURL = new String('<%=request.getRequestURL()%>');
					var idy = requestURL.indexOf('/' + appName + '/');
					requestURL = requestURL.substring(0, idy + 1);
					requestURL = requestURL + appName
							+ "/intraDayLiquidity.do?method=getGlobalGroup";
					requestURL = requestURL + "&entityId=" + entityId + "&ccyCode=" + ccyCode;
					var oXMLHTTP = new XMLHttpRequest();
					oXMLHTTP.open("POST", requestURL, false);
					oXMLHTTP.send();
					globalGrp = new String(oXMLHTTP.responseText);
					document.getElementById('ccyGlobalGrp').innerHTML = '<s:text name="ilmReport.currencyGlobalGroup"/>' + " [" + globalGrp + "]";
					document.forms[0].elements["reports.ilmGroup"].title='<s:text name="tooltip.selectILMExcelAccountGroup"/>';
					document.forms[0].elements["reports.defaultIlmGrp"].title='<s:text name="tooltip.selectILMExcelAccountGroup"/>';
				} else {
				    //document.forms[0].elements["reports.scenario"].disabled = true;
					//document.forms[0].elements["reports.scenario"].selectedIndex = 0;
					document.getElementById('ccyGlobalGrp').innerHTML = '<s:text name="ilmReport.currencyGlobalGroup"/>';
					document.getElementById("ilmGrps").disabled = true;
					$('#ilmGrps *').attr("disabled", true);
					document.forms[0].elements["reports.ilmGroup"].className='is-disabled';
				}
			} else {
				//document.forms[0].elements["reports.scenario"].disabled = true;
				//document.forms[0].elements["reports.scenario"].selectedIndex = 0;
				if (document.forms[0].elements["reports.currencyCode"].length == 1){
					alert('<s:text name="ilmanalysismonitor.errorAllOptionNotAvailable"/>');
				}
				document.getElementById('ccyGlobalGrp').innerHTML = '<s:text name="ilmReport.currencyGlobalGroup"/>';
			}

			document.forms[0].elements["reports.ilmGroup"].titlekey ="";
			if (!document.forms[0].elements["reports.criticalTransactions"][0].checked && !document.forms[0].elements["reports.criticalTransactions"][1].checked)
				document.forms[0].elements["reports.criticalTransactions"][0].checked = true;
		}
		
		
		changeILMGroupNameStyle();

		if (document.getElementById("ilmGrps").disabled) {
			document.forms[0].elements["reports.defaultIlmGrp"][0].checked = true;	
		}

		
		if (configScheduler == "true") {
			loadCalendarWithKeyords();
		} else {
// 			document.getElementById("cancelbutton").innerHTML = document.getElementById("canceldisablebutton").innerHTML;
			cal = new CalendarPopup("caldiv", false, "calFrame");
			cal.addDisabledDates(dateInCcyTimeframe, null); 
			cal.offsetX = -3;
			cal.offsetY = -97;
		}
	}
	
	function loadCalendarWithKeyords()
	{
		<% 
			ArrayList<LabelValueBean> keyWords = (ArrayList<LabelValueBean>)request.getAttribute("keywords");
			Iterator it = keyWords.iterator();
			while (it.hasNext())
			{
				LabelValueBean lvb = (LabelValueBean) it.next();
		%>
			var newElement = {};
			newElement[headers[1]] = "<%=lvb.getValue()%>";
			newElement[headers[0]] = "<%=lvb.getLabel()%>";
			dataprovider.push(newElement);
		<%
			}
		
		%>

		cal = new CalendarPopup("caldiv", false, "calFrame");
		cal.addDisabledDates(dateInCcyTimeframe, null); 
		cal.offsetX = -200;
		cal.offsetY = -170;
		cal.withKeyWord= true;
		cal.comboboxId= 'ilmStartDay';
		cal.inputKeywordName= 'keywordInput';
		cal.keyWordDataProvider = dataprovider;
	}
	var headers = ["Keyword", "Description"];

	 
	var dataprovider = new Array();

	
	function submitForm(methodName){
	
		if (methodName == 'changeEntity') {
			document.forms[0].changeEntity.value = "Y";
			methodName = "getILMReport";

		} 
		else if (methodName == 'changeCcy') {
			document.forms[0].changeCcy.value = "Y";
			methodName = "getILMReport";

		} 
		else if (methodName == 'getILMReportFromReportType'){
			document.forms[0].changeCcy.value = "N";
			document.forms[0].isFromReportType.value="true";
			methodName="getILMReport";
		}else
			document.forms[0].changeCcy.value = "N";
		document.forms[0].elements["reports.currencyCode"].disabled = false;
		document.forms[0].method.value = methodName;
		document.forms[0].entityId.value = document.forms[0]
				.elements["reports.entityId"].value;
	
		document.forms[0].currencyCode.value =  document.forms[0]
				.elements["reports.currencyCode"].value;
		if (configScheduler == "true") {
			document.forms[0].configScheduler.value = configScheduler;
			document.forms[0].newILMReportSchedConfig.value = newILMReportSchedConfig;
			document.forms[0].schedulerConfigXML.value = schedulerConfigXML;
			document.forms[0].roleId.value = roleId;
			document.forms[0].reportType.value = document.forms[0].elements["reports.reportType"].value;
			enableFields();
		}
		document.forms[0].submit();
		
	}
	
	
	function OneChecked(){
		 var totalChecked = 0;
		 var checkLORO = document.forms[0].elements["reports.loroPayments"].checked;
		 var checkCorporate = document.forms[0].elements["reports.corporatePayments"].checked;
		 var checkBranch = document.forms[0].elements["reports.branchPayments"].checked;
		 var checkOther = document.forms[0].elements["reports.otherPayments"].checked ;
		 
		 if (checkLORO == true){
			 totalChecked += 1;
			 chekedBox = "L";
		 }
			 
		if (checkCorporate == true){
			 totalChecked += 1;
			 chekedBox = "C";
		}
			
		
		if (checkBranch == true){
			totalChecked += 1;
			 chekedBox = "B";
		}
			
		if (checkOther == true){
			 totalChecked ++;
			 chekedBox = "O";
		}
				
			return totalChecked;
	
		}
	
	
	/**
	 * if the document is ready then add a listener to exportDataForm in order to capture when 
	 * the download will be finished after exporting
	 */
// 	$(document).ready(function () {
// 	    $('#reportForm').submit(function () {
// 	    	blockUIForDownload();
// 	    });
// 	});

	/**
	 * This is used to capture when the download has been finished in order to close the popup in flex part
	 */
	function blockUIForDownload() {
		// Use the current timestamp as the token value
	    var token = new Date().getTime(); 
	    // Set the hidden input download_token_value_id as token. It will be sent to the action part.
		$('#download_token_value_id').val(token);
	   	/* The setInterval() method calls a function at specified intervals (in milliseconds). 
	   	   If we received the same token (from cookie) then the download is finished */
	    fileDownloadCheckTimer = window.setInterval(function () {
	      var downloadToken;
		  var downloadError;
	      var cookieValue = $.cookie('fileDownloadToken');
	      downloadToken=downloadError=cookieValue;  
	      
	      if (cookieValue!= null){
		      var fileDownloadTokenValue= cookieValue.split("|");
		      downloadToken=fileDownloadTokenValue[0];
		      downloadError=fileDownloadTokenValue[1] ;
	      }
	      if (downloadError == "KO")
	     	 errorFileDownload() ;
	      else if (downloadError == "MEM"){
	     	 errorMemFileDownload() ;
	      }else if (downloadError == "ERR"){
	    	  errorErrFileDownload() ;
	      }else  if (downloadToken == token)
	      	 downloadFinished();
	    }, 300); // the intervals (in milliseconds) on how often to execute the code  
	    
	    downloadInProgressTimer = window.setInterval(function () {
	    	
				var appName = "<%=SwtUtil.appName%>";
				var requestURL = new String('<%=request.getRequestURL()%>');
				var idy = requestURL.indexOf('/' + appName + '/');
		
				requestURL = requestURL.substring(0, idy + 1);
				requestURL = requestURL + appName
						+ "/ilmReport.do?method=reportInProgressValidation";
				
				var oXMLHTTP = new XMLHttpRequest();
				oXMLHTTP.open("POST", requestURL, false);
				oXMLHTTP.send();
	    }, 10000); // the intervals (in milliseconds) on how often to execute the code  
	}


	/**
	 * Clear the interval, clear the token and then close the popup in flex part using call back method 
	 */
	function downloadFinished() {
		  window.clearInterval(fileDownloadCheckTimer);
		  window.clearInterval(downloadInProgressTimer);
		  
		  $.cookie('fileDownloadToken', null); //clears this cookie value
		  hideLoadingGif();
	}
	function errorFileDownload(){
		 window.clearInterval(fileDownloadCheckTimer);	
		 window.clearInterval(downloadInProgressTimer);
		 $.cookie('fileDownloadToken', null); //clears this cookie value
		 hideLoadingGif();
		 Swal.fire({
			  html: 'Report Canceled',
			  showCancelButton: false,
			  width:350,
			  confirmButtonText: 'OK',
			});
		 
	}
	function errorMemFileDownload(){
		 window.clearInterval(fileDownloadCheckTimer);	
		 window.clearInterval(downloadInProgressTimer);
		 $.cookie('fileDownloadToken', null); //clears this cookie value
		 hideLoadingGif();
		alert('Out of memory');
		 
	}
	function errorErrFileDownload(){
		 window.clearInterval(fileDownloadCheckTimer);	
		 window.clearInterval(downloadInProgressTimer);
		 $.cookie('fileDownloadToken', null); //clears this cookie value
		 hideLoadingGif();
		alert('Error when creating report, please check logs');
		 
	}
	
	function validateReportDates(){

		if(isSingleDate){
				if(document.forms[0].elements['reports.fromDateAsString'].value == "" ){
					alert('<s:text name="alert.enterValidDate"/>');
				}else{
					document.forms[0].elements['reports.toDateAsString'].value ="";
					return validateDateField('reports.fromDateAsString','fromDateAsString');
				}
 		}else{
 			 	if(document.forms[0].elements['reports.fromDateAsString'].value == ""){
 			 		alert('<s:text name="alert.enterValidFromDate"/>');
 			 	}else if (document.forms[0].elements['reports.toDateAsString'].value == ""){
 			 		alert('<s:text name="alert.enterValidToDate"/>');
				}else{
		 		 return comparedates(document.forms[0].elements['reports.fromDateAsString'].value,
				                     document.forms[0].elements['reports.toDateAsString'].value,dateFormat,'Start Date','End Date');
				}
		  }
		
	}
	
	function validateDatesFromReportConfig(){
		if(isSingleDate){
				if(document.forms[0].elements['reports.fromDateAsString'].value == "" ){
					alert('<s:text name="alert.enterValidDate"/>');
				}else{
					document.forms[0].elements['reports.toDateAsString'].value ="";
				
					var appName = "<%=SwtUtil.appName%>";
					var requestURL = new String('<%=request.getRequestURL()%>');
					var idy = requestURL.indexOf('/' + appName + '/');

					requestURL = requestURL.substring(0, idy + 1);
					requestURL = requestURL + appName
							+ "/ilmReport.do?method=validateDates";
					requestURL = requestURL + "&dateFormat="+dateFormat+"&entityId=" + document.forms[0].elements["reports.entityId"].value +"&fromDate=" + document.forms[0].elements['reports.fromDateAsString'].value + "&toDate=" + document.forms[0].elements['reports.toDateAsString'].value;
				
					var oXMLHTTP = new XMLHttpRequest();
					oXMLHTTP.open("POST", requestURL, false);
					oXMLHTTP.send();
					var result = new String(oXMLHTTP.responseText);
					if(result == "true") {
						  return true;
					}else {
						alert('<s:text name="alert.enterValidToDate"/>');
						return false;
					}
					//return validateDateField('reports.fromDateAsString','fromDateAsString');
				}
 		}else{
 			 	if(document.forms[0].elements['reports.fromDateAsString'].value == ""){
 			 		alert('<s:text name="alert.enterValidFromDate"/>');
 			 	}else if (document.forms[0].elements['reports.toDateAsString'].value == ""){
 			 		alert('<s:text name="alert.enterValidToDate"/>');
				}else{
					
					var appName = "<%=SwtUtil.appName%>";
					var requestURL = new String('<%=request.getRequestURL()%>');
					var idy = requestURL.indexOf('/' + appName + '/');

					requestURL = requestURL.substring(0, idy + 1);
					requestURL = requestURL + appName
							+ "/ilmReport.do?method=validateDates";
					requestURL = requestURL + "&dateFormat="+dateFormat+"&entityId=" + document.forms[0].elements["reports.entityId"].value +"&fromDate=" + document.forms[0].elements['reports.fromDateAsString'].value + "&toDate=" + document.forms[0].elements['reports.toDateAsString'].value;
				
					var oXMLHTTP = new XMLHttpRequest();
					oXMLHTTP.open("POST", requestURL, false);
					oXMLHTTP.send();
					var result = new String(oXMLHTTP.responseText);
					if(result == "true") {
						  return true;
					}else {
						alert('<s:text name="alertDateShouldNotBeEarlierEhanFromDate"/>');
						  return false;
					}
					
				}
		  }
		
	}
	
	function validateDateField(elem,fieldName){
		var isValid = false;
		if(dateFormat == 'datePat2'){
			isValid = (validateField(document.forms[0].elements[elem],fieldName,'datePat4'));
		} else if(dateFormat == 'datePat1'){		
			isValid = (validateField(document.forms[0].elements[elem],fieldName,'datePat1'));
		}
		if(!isValid)
			document.forms[0].elements[elem].value="";   
		
                var maxReportDateToCompare;
		var selectedDateToCompare;
                var dateArray = maxReportDate.split("/");
		if (dateFormatValue == "dd/MM/yyyy") {
			// NOTE: month is 0-11
                        maxReportDateToCompare = new Date(dateArray[2], dateArray[1]-1, dateArray[0]);
			dateArray = document.forms[0].elements[elem].value.split("/");
			selectedDateToCompare = new Date(dateArray[2], dateArray[1]-1, dateArray[0]);
		} else {
			// NOTE: month is 0-11
                        maxReportDateToCompare = new Date(dateArray[2], dateArray[0]-1, dateArray[1]);
			dateArray = document.forms[0].elements[elem].value.split("/");
			selectedDateToCompare = new Date(dateArray[2], dateArray[0]-1, dateArray[1]);
		}
		
                if (selectedDateToCompare.getTime() > maxReportDateToCompare.getTime()) {
                    var ccyCode = document.forms[0].elements["reports.currencyCode"].value;
                    if (ccyCode != "All") {
                        alert(dateValidationMessage);
                    }else {
                        alert(dateValidationMessageAll)
                    }
			setTimeout(function(){document.getElementById(elem).focus();}, 1);
			document.forms[0].elements[elem].value = "";
			return false;
		}
		
		return isValid;
	}
	
	function report(methodName){
	
		if (validateReportDates()) {
			document.forms[0].method.value = methodName;
			document.forms[0].entityId.value = document.forms[0]
					.elements["reports.entityId"].value;
			document.forms[0].currencyCode.value = document.forms[0]
					.elements["reports.currencyCode"].value;
			 $("div[id='ILMGrpRadio']").each(function() {
					var div = $(this);
					var checkedValue =	 div.find("input[type ='radio']:checked").val();
						if(checkedValue != undefined)
							document.forms[0].ilmGroup.value = checkedValue;
			 });
			document.forms[0].ccyText.value = document.getElementById("currencyDesc").innerText;
			document.forms[0].entityText.value = document.getElementById("entityName").innerText;
			document.forms[0].scenarioText.value = document.getElementById("scenarioName").innerText;
	
			
			if(document.forms[0].entityId.value == "All"){
				
				var appName = "<%=SwtUtil.appName%>";
				var requestURL = new String('<%=request.getRequestURL()%>');
				var idy = requestURL.indexOf('/' + appName + '/');
	
				requestURL = requestURL.substring(0, idy + 1);
				requestURL = requestURL + appName
						+ "/ilmReport.do?method=isAllEntityAvailable";
				requestURL = requestURL + "&selectedCurrencyCode=" + document.forms[0].currencyCode.value;
				
				var oXMLHTTP = new XMLHttpRequest();
				oXMLHTTP.open("POST", requestURL, false);
				oXMLHTTP.send();
				var isAllEntityAvailable = new String(oXMLHTTP.responseText);
				if(isAllEntityAvailable=="false"){
					alert('<s:text name="ilmanalysismonitor.errorAllOptionNotAvailable"/>');
					return;
				}
			}
			if(checkMissingData()){
				showLodingGif();
				if(document.forms[0].elements["reports.scenario"].disabled == true){
					blockUIForDownload();
					document.forms[0].elements["reports.scenario"].disabled = false;
					$('<iframe name="tmp" style="display: none;"/>').appendTo('body').attr({'id': 'tmp'});
					document.forms[0].target = 'tmp';
					document.forms[0].submit();
					document.forms[0].elements["reports.scenario"].disabled = true;
					
				}else{
					blockUIForDownload();
					$('<iframe name="tmp" style="display: none;"/>').appendTo('body').attr({'id': 'tmp'});
					document.forms[0].target = 'tmp';
					document.forms[0].submit();
				}
			}
		}
		setParentChildsFocus();
	}

	
	function ShowLoadingBar() {
		let timerInterval
		try {
 
			Swal.fire({
				  title: 'Generating report',
				  html: 'Please wait while we generate the report...<br><br><progress class="swal2-progress" style="width: 100%; height: 20px;"></progress>',
				  allowOutsideClick: false,
				  allowEscapeKey: false,
				  showConfirmButton: false,
				  showCancelButton: true,
				  showClass: {
					    popup: '',
					    icon: ''
					  },
					  hideClass: {
					    popup: '',
					  },
				  cancelButtonText: 'Cancel',
				  onBeforeOpen: () => {
				    const cancelButton = Swal.getCancelButton();
				    const progress = Swal.getContent().querySelector('progress');
				    const interval = setInterval(() => {
				      if (progress.value < progress.max) {
				    	  if(finishDownload){
				        	progress.value++;
				    	  }
				      } else {
				        clearInterval(interval);
				        Swal.close();
				      }
				    }, 1000);

				    cancelButton.addEventListener('click', () => {
				      clearInterval(interval);
				      Swal.fire({
				        html: 'Are you sure you want to cancel?',
				        imageUrl: 'assets/images/warning.png',
				        showCancelButton: true,
				        confirmButtonText: 'Yes',
				        cancelButtonText: 'No',
				        width:350,
				        reverseButtons: false,
				        allowOutsideClick: false,
				        allowEscapeKey: false,
// 				        icon: 'warning'
				      }).then((result) => {
				        if (result.isConfirmed) {
			        	cancelReport();
				          Swal.close();
				        } else {
				        	ShowLoadingBar();
				        }
				      });
				    });
				  }
				});
			
		} catch (e) {
			console.log(e);
		}

	}
	//ShowLoadingBar()
	
	
	
	function showLodingGif() {
		finishDownload = false;
// 		   var pb = document.getElementById("pnlRefreshIcon");
// 		   pb.innerHTML = '<img src="images/Loading.gif">';
// 		   pb.style.display = '';
// 		   if (configScheduler != "true") {
// 			   document.getElementById("cancelbutton").innerHTML = document.getElementById("cancelenablebutton").innerHTML;
// 		   }
		   
		   ShowLoadingBar();
		 
		}
	
	var finishDownload=false;
	function hideLoadingGif() {
		finishDownload = true;
// 		   var pb = document.getElementById("pnlRefreshIcon");
// 		   pb.innerHTML = '';
// 		   pb.style.display = 'none';
// 		   if (configScheduler != "true") {
// 			   document.getElementById("cancelbutton").innerHTML = document.getElementById("canceldisablebutton").innerHTML;
// 		   }
		   document.forms[0].target = "";
		}
	
	function changeILMGroupNameStyle(){
		var element = document.getElementById("ilmGrpName");
		if(element != undefined && element != null && element.offsetWidth > 200){
			document.getElementById("tdOfGrpName").style["word-break"] = "break-all";
		}
	}
	function cancelReport(){
		
		var appName = "<%=SwtUtil.appName%>";
		var requestURL = new String('<%=request.getRequestURL()%>');
		var idy = requestURL.indexOf('/' + appName + '/');
       
		requestURL = requestURL.substring(0, idy + 1);
		requestURL = requestURL + appName
				+ "/ilmReport.do?method=cancelILMExport";
		
		var oXMLHTTP = new XMLHttpRequest();
		oXMLHTTP.open("POST", requestURL, false);
		oXMLHTTP.send();
		
	}
	
	function checkMissingData(){

		var appName = "<%=SwtUtil.appName%>";
		var requestURL = new String('<%=request.getRequestURL()%>');
		var idy = requestURL.indexOf('/' + appName + '/');
       
		requestURL = requestURL.substring(0, idy + 1);
		requestURL = requestURL + appName
				+ "/ilmReport.do?method=checkMissingData";
		
		requestURL = requestURL + "&entityId=" + document.forms[0].elements["reports.entityId"].value;
		requestURL = requestURL + "&currencyCode=" + document.forms[0].currencyCode.value;	
		requestURL = requestURL + "&reportType=" + document.forms[0].elements["reports.reportType"].value;
		var ilmgrp = (document.forms[0].ilmGroup.value == "default")? globalGrp : document.forms[0].elements["reports.ilmGroup"].value;
		requestURL = requestURL + "&ilmGroup=" + ((ilmgrp === undefined )? "" : ilmgrp);
		var ilmgrpname = (document.forms[0].ilmGroup.value == "default")? globalGrp : document.getElementById('ilmGrpName').innerHTML;
		document.forms[0].ilmGrpName.value = ilmgrpname
		requestURL = requestURL + "&ilmGrpName=" + + ((ilmgrpname === undefined )? "" : ilmgrpname);
		requestURL = requestURL + "&scenarioId=" + document.forms[0].elements["reports.scenario"].value;	
		requestURL = requestURL + "&startDateRange=" + document.forms[0].elements['reports.fromDateAsString'].value;	
		requestURL = requestURL + "&endDateRange=" + (isSingleDate? document.forms[0].elements['reports.fromDateAsString'].value :document.forms[0].elements['reports.toDateAsString'].value);	
		var oXMLHTTP = new XMLHttpRequest();
		oXMLHTTP.open("POST", requestURL, false);
		oXMLHTTP.send();
	
		var missingDates = new String(oXMLHTTP.responseText);	
		
		if(missingDates != "S"){
			if(missingDates =="F"){
				alert('<s:text name="ilmReport.errorMissingData"/>');
				return false;
			}

			dates = missingDates.split("#");
			earliestDate =	dates[0];
			latestDate = dates[1];
			if (earliestDate == latestDate){
				 ShowErrMsgWindowWithBtn('',"<s:text name="ilmReport.oneDayMissingData" />".replace("{0}", earliestDate), YES_NO, yesFunc, noFunc);
				 return;
			}else{

			 ShowErrMsgWindowWithBtn('', "<s:text name="ilmReport.warningMissingData" />".replace("{0}", earliestDate).replace("{1}", latestDate), YES_NO, yesFunc, noFunc);
			 return;
			}
			
			
		}
		
		return true;

	}
	
	function yesFunc() {
		showLodingGif();
		if(document.forms[0].elements["reports.scenario"].disabled == true){
			blockUIForDownload();
			document.forms[0].elements["reports.scenario"].disabled = false;
			$('<iframe name="tmp" style="display: none;"/>').appendTo('body').attr({'id': 'tmp'});
			document.forms[0].target = 'tmp';
			document.forms[0].submit();
			document.forms[0].elements["reports.scenario"].disabled = true;
	
		}else{
			blockUIForDownload();
			$('<iframe name="tmp" style="display: none;"/>').appendTo('body').attr({'id': 'tmp'});
			document.forms[0].target = 'tmp';
			document.forms[0].submit();
		}
	}
	function  noFunc() {
		return false;
	}
	function saveSchedulerReportOptions(){
		if(validateDatesFromReportConfig()){
			var schedulerConfigXML;
			var reportSchedulerData = new Object();
			reportSchedulerData.reportType = document.forms[0].elements["reports.reportType"].value;
			reportSchedulerData.entityId = document.forms[0].elements["reports.entityId"].value;
			reportSchedulerData.entityName = document.getElementById("entityName").innerText;
			reportSchedulerData.currencyCode = document.forms[0].elements["reports.currencyCode"].value;
			reportSchedulerData.scenarioId = document.forms[0].elements["reports.scenario"].value;
			$("div[id='ILMGrpRadio']").each(function() {
				var div = $(this);
				var checkedValue = div.find("input[type ='radio']:checked").val();
				if(checkedValue != undefined)
					document.forms[0].ilmGroup.value = checkedValue;
			});
			var ilmgrp = (document.forms[0].ilmGroup.value == "default")? globalGrp : document.forms[0].elements["reports.ilmGroup"].value;
			reportSchedulerData.ilmGroup = ((ilmgrp === undefined )? "" : ilmgrp);
			var ilmgrpname = (document.forms[0].ilmGroup.value == "default")? globalGrp : document.getElementById('ilmGrpName').innerHTML;
			reportSchedulerData.ilmGroupName = ilmgrpname;
			if (document.forms[0].elements["reports.defaultIlmGrp"][0].checked) {
				reportSchedulerData.defaultIlmGroupId = 'default';
			} else if (document.forms[0].elements["reports.defaultIlmGrp"][1].checked) {
				reportSchedulerData.defaultIlmGroupId = 'noDefault';
			}
			if (document.forms[0].elements["reports.singleOrRange"][0].checked) {
				reportSchedulerData.singleOrRange = 'S';
			} else if (document.forms[0].elements["reports.singleOrRange"][1].checked) {
				reportSchedulerData.singleOrRange = 'R';
			}
			reportSchedulerData.fromDateAsString = document.forms[0].elements["reports.fromDateAsString"].value;
			reportSchedulerData.toDateAsString = (isSingleDate? document.forms[0].elements['reports.fromDateAsString'].value :document.forms[0].elements['reports.toDateAsString'].value);
			if (document.forms[0].elements["reports.sumInOutFlows"] != undefined) {
					if (document.forms[0].elements["reports.sumInOutFlows"][0].checked) {
						if ('${ilmReportType}' == "<%=SwtConstants.ILM_REPORT_TYPE_BASEL_A%>") {
							reportSchedulerData.sumInOutFlows = 'CB';
						} else if ('${ilmReportType}' == "<%=SwtConstants.ILM_REPORT_TYPE_BASEL_B%>") {
							reportSchedulerData.sumInOutFlows = 'CORR';
						}
					} else if (document.forms[0].elements["reports.sumInOutFlows"][1].checked) {
						reportSchedulerData.sumInOutFlows = 'GC';
					}
			}
			if ('${ilmReportType}' == "<%=SwtConstants.ILM_REPORT_TYPE_BASEL_C%>") {
				if (document.forms[0].elements["reports.costumerPayments"][1].checked) {
					reportSchedulerData.costumerPayments ="Y";
					reportSchedulerData.loroPayments = document.forms[0].elements["reports.loroPayments"].checked;
					reportSchedulerData.corporatePayments = document.forms[0].elements["reports.corporatePayments"].checked;
					reportSchedulerData.branchPayments = document.forms[0].elements["reports.branchPayments"].checked;
					reportSchedulerData.otherPayments= document.forms[0].elements["reports.otherPayments"].checked;
				} else 	if (document.forms[0].elements["reports.costumerPayments"][0].checked) {
					reportSchedulerData.costumerPayments ="N";
				}
				
			}
			reportSchedulerData.useCcyMultiplier = document.forms[0].elements["reports.useCcyMultiplier"].checked;
			if (document.forms[0].elements["reports.criticalTransactions"] != undefined) {
				if (document.forms[0].elements["reports.criticalTransactions"][0].checked) {
					reportSchedulerData.criticalTransactions = 'SHOP';
				} else if (document.forms[0].elements["reports.criticalTransactions"][1].checked) {
					reportSchedulerData.criticalTransactions = 'SHA';
				}
			}
			
			reportSchedulerData.jobId = '${jobId}';
			schedulerConfigXML = convertConfigObjectToXML(reportSchedulerData);
			this.opener.document.forms[0].schedulerConfigXML.value = getMenuWindow().encode64(schedulerConfigXML.innerHTML);
			this.opener.updateSchedulerConfigParams("");
			window.close();
		}
	}
	
	/**
	*
	* Used to create XML Data that contain information 
	* about scheduler ILM report configuration
	*
	**/
	function convertConfigObjectToXML(reportSchedulerData){
		var schedulerConfigXML = document.createElement("div");
		var transactionNode = document.createElement("schedConfig");
		
		var jobIdNode = document.createElement("jobid");
		jobIdNode.appendChild(document.createTextNode(reportSchedulerData.jobId));
		
		var reportTypeNode = document.createElement("reporttype");
		reportTypeNode.appendChild(document.createTextNode(reportSchedulerData.reportType));
		
		var entityIdNode = document.createElement("entityid");
		entityIdNode.appendChild(document.createTextNode(reportSchedulerData.entityId));
		
		var entityNameNode = document.createElement("entityname");
		entityNameNode.appendChild(document.createTextNode(reportSchedulerData.entityName));
		
		var currencyCodeNode = document.createElement("currencycode");
		currencyCodeNode.appendChild(document.createTextNode(reportSchedulerData.currencyCode));
		
		var scenarioIdNode = document.createElement("scenarioid");
		scenarioIdNode.appendChild(document.createTextNode(reportSchedulerData.scenarioId));
		
		var ilmGroupNode = document.createElement("ilmgroup");
		ilmGroupNode.appendChild(document.createTextNode(reportSchedulerData.ilmGroup));
		
		var ilmGroupNameNode = document.createElement("ilmgroupname");
		ilmGroupNameNode.appendChild(document.createTextNode(reportSchedulerData.ilmGroupName));
		
		var defaultIlmGroupIdNode = document.createElement("defaultilmgroupid");
		defaultIlmGroupIdNode.appendChild(document.createTextNode(reportSchedulerData.defaultIlmGroupId));
		
		var singleOrRangeNode = document.createElement("singleorrange");
		singleOrRangeNode.appendChild(document.createTextNode(reportSchedulerData.singleOrRange));
		
		var fromDateAsStringNode = document.createElement("fromdateasstring");
		fromDateAsStringNode.appendChild(document.createTextNode(reportSchedulerData.fromDateAsString));
		
		var toDateAsStringNode = document.createElement("todateasstring");
		toDateAsStringNode.appendChild(document.createTextNode(reportSchedulerData.toDateAsString));
		
		var sumInOutFlowsNode = document.createElement("suminoutflows");
		if (reportSchedulerData.sumInOutFlows == undefined) {
			sumInOutFlowsNode.appendChild(document.createTextNode(""));
		} else {
			sumInOutFlowsNode.appendChild(document.createTextNode(reportSchedulerData.sumInOutFlows));
		}
		
		var criticalTransactionsNode = document.createElement("criticaltransactions");
		if (reportSchedulerData.criticalTransactions == undefined) {
			criticalTransactionsNode.appendChild(document.createTextNode(""));
		} else {
			criticalTransactionsNode.appendChild(document.createTextNode(reportSchedulerData.criticalTransactions));
		}
		
		var reportTypeNode = document.createElement("reporttype");
		reportTypeNode.appendChild(document.createTextNode(reportSchedulerData.reportType));
		
		var useCcyMultiplierNode = document.createElement("useccymultiplier");
		useCcyMultiplierNode.appendChild(document.createTextNode(reportSchedulerData.useCcyMultiplier));
	
		var costumerPaymentsNode = document.createElement("costumerPayments");
		if (reportSchedulerData.costumerPayments == undefined) {
			costumerPaymentsNode.appendChild(document.createTextNode("N"));
		} else {
			costumerPaymentsNode.appendChild(document.createTextNode(reportSchedulerData.costumerPayments));
		}
		var loroPaymentsNode = document.createElement("loroPayments");
		if (reportSchedulerData.loroPayments == undefined) {
			loroPaymentsNode.appendChild(document.createTextNode(""));
		}else{
			loroPaymentsNode.appendChild(document.createTextNode(reportSchedulerData.loroPayments));
		}
		
		var corporatePaymentsNode = document.createElement("corporatePayments");
		if (reportSchedulerData.corporatePayments == undefined) {
			corporatePaymentsNode.appendChild(document.createTextNode(""));
		}else{
			corporatePaymentsNode.appendChild(document.createTextNode(reportSchedulerData.corporatePayments));
		}
		
		var branchPaymentsNode = document.createElement("branchPayments");
		if (reportSchedulerData.branchPayments == undefined) {
			branchPaymentsNode.appendChild(document.createTextNode(""));
		}else{
			branchPaymentsNode.appendChild(document.createTextNode(reportSchedulerData.branchPayments));
		}
		
		var otherPaymentsNode = document.createElement("otherPayments");
		if (reportSchedulerData.otherPayments == undefined) {
			otherPaymentsNode.appendChild(document.createTextNode(""));
		}else{
			otherPaymentsNode.appendChild(document.createTextNode(reportSchedulerData.otherPayments));
		}
		
		var dateFormatNode = document.createElement("dateformat");
		dateFormatNode.appendChild(document.createTextNode(dateFormatValue));
		
		transactionNode.appendChild(jobIdNode);
		transactionNode.appendChild(reportTypeNode);
		transactionNode.appendChild(reportTypeNode);
		transactionNode.appendChild(entityIdNode);
		transactionNode.appendChild(entityNameNode);
		transactionNode.appendChild(currencyCodeNode);
		transactionNode.appendChild(scenarioIdNode);
		transactionNode.appendChild(ilmGroupNode);
		transactionNode.appendChild(ilmGroupNameNode);
		transactionNode.appendChild(defaultIlmGroupIdNode);
		transactionNode.appendChild(singleOrRangeNode);
		transactionNode.appendChild(fromDateAsStringNode);
		transactionNode.appendChild(toDateAsStringNode);
		transactionNode.appendChild(dateFormatNode);
		transactionNode.appendChild(sumInOutFlowsNode);
		transactionNode.appendChild(criticalTransactionsNode);
		transactionNode.appendChild(useCcyMultiplierNode);
		transactionNode.appendChild(costumerPaymentsNode);
		transactionNode.appendChild(loroPaymentsNode);
		transactionNode.appendChild(corporatePaymentsNode);
		transactionNode.appendChild(branchPaymentsNode);
		transactionNode.appendChild(otherPaymentsNode);
			
		schedulerConfigXML.appendChild(transactionNode);
		return schedulerConfigXML;
	}
	
	function enableFields(){
		document.forms[0].elements["reports.reportType"].disabled = "";
	}
	
	var dateFormat = '<s:property value="#request.session.CDM.dateFormat" />';
	var dateFormatValue = '<s:property value="#request.session.CDM.dateFormatValue" />';

</script>
    <style>
        #ilmStartDay{
   margin-left: -200px;
}
input[name ="keywordInput"] {
	width: 120px
}

</style>
</head>

<body leftmargin="0" topmargin="0" marginheight="0" onLoad="bodyOnLoad();setParentChildsFocus();setFocus(document.forms[0]);ShowErrMsgWindow('${actionError}');" onunload="call()">
    <s:form id="reportForm" action="ilmReport.do">
        <input name="method" type="hidden" value="">
        <input name="reportTypeInput" type="hidden" value="">
        <input name="entityId" type="hidden" value="">
        <input name="currencyCode" type="hidden" value="">
        <input name="changeEntity" type="hidden" value="">
        <input name="changeCcy" type="hidden" value="">
        <input name="entityText" type="hidden" value="">
        <input name="scenarioText" type="hidden" value="">
        <input type="hidden" name="tokenForDownload" id="download_token_value_id" />
        <input name="reportTypeInRequest" type="hidden" value="${reportTypeInRequest}">
        <input name="ccyText" type="hidden" value="">
        <input name="isRange" type="hidden" value="">
        <input name="ilmGroup" type="hidden" value="">
        <input name="ilmGrpName" type="hidden" value="">
        <input name="isFromReportType" type="hidden" value="">
        <input name="changeScenario" type="hidden" value="">
        <input name=configScheduler type="hidden" value="">
        <input name=newILMReportSchedConfig type="hidden" value="">
        <input name=schedulerConfigXML type="hidden" value="">
        <input name=roleId type="hidden" value="">
        <input name=reportType type="hidden" value="">
        <input name=costumerPayments type="hidden" value="">
        <div id="ilmReport" style="position: absolute; left: 10px; top: 10px; width: 650px; height: 413px; border: 2px outset;" color="#7E97AF">
            <div id="caldiv" style="z-index: 99; position: absolute; visibility: visible; background-color: white; layer-background-color: white;"></div>
            <iframe id="calFrame" src="javascript:false;" scrolling="no" frameborder="0" style="position: absolute; top: 0px; left: 0px; display: none;">
            </iframe>
            <div style="position: absolute; width: 650;">
                <table>
                    <tr height="25">
                        <td width="198" style="padding-left: 10px;"><b>
                                <s:text name="ilmReport.reportType"/></b></td>
                        <td width="300px">
                            <s:select id="reports.reportType" name="reports.reportType" cssClass="htmlTextAlpha" titleKey="tooltip.SelectReportType" cssStyle="width: 420px;" tabindex="1" onchange="submitForm('getILMReportFromReportType')" list="#request.reportTypes" listKey="value" listValue="label" />
                            
                        </td>
                    </tr>
                </table>
                <table style='position:fixed;'>
                    <tr height="30">
                        <td width="198" style="padding-left:10px"><b>
                                <s:text name="groupCode.entity"/></b></td>
                        <td width="150px">
                            <s:select id="reports.entityId" name="reports.entityId" cssClass="htmlTextAlpha" titleKey="tooltip.reportEntity" cssStyle="width:145px" tabindex="2" onchange="submitForm('changeEntity')" list="#request.entities" listKey="value" listValue="label" />
                            
                        </td>
                        <td width="220"><span id="entityName" name="entityName" class="spantext"></td>
                    </tr>
                    <tr>
                        <td width="198" style="padding-left:10px"><b>
                                <s:text name="ilmTransactionSet.currencyCode"/></b></td>
                        <td width="150">
                            <s:select id="reports.currencyCode" name="reports.currencyCode" titleKey="tooltip.selectCurrencyCode" cssStyle="width:55px;height: 21px" onchange="submitForm('changeCcy')" tabindex="3" list="#request.currencies" listKey="value" listValue="label" />
                            
                        </td>
                        <td width="220"><span id="currencyDesc" class="spantext"></td>
                    </tr>
                    <tr height="30">
                        <td width="198" style="padding-left:10px"><b>
                                <s:text name="ilmReport.scenario"/></b></td>
                        <td width="150px">
                            <s:select id="reports.scenario" name="reports.scenario" cssClass="htmlTextAlpha" titleKey="tooltip.selectScenario" cssStyle="width:145px" tabindex="2" list="#request.scenarios" listKey="value" listValue="label" />
                            
                        </td>
                        <td width="220"><span id="scenarioName" name="scenarioName" class="spantext"></td>
                    </tr>
                </table>
            </div>
            <div id="ILMGrpRadio" style="position: absolute; top: 120;">
                <table style="position: absolute; width: 630">
                    <tr>
                        <td width="200" style="padding-left: 8px;"><b>
                                <s:text name="ilmReport.ilmGroup.title"/></b></td>
                        <td style="width: 32;margin-left: 12px" id="ccyGlobalGrpRadio">
                            <s:radio name="reports.defaultIlmGrp" list="#{'default':''}" tabindex="4" />
                        </td>
                        <td><label id="ccyGlobalGrp">
                                <s:text name="ilmReport.currencyGlobalGroup"/>
                            </label>&nbsp;</td>
                    </tr>
                </table>
                <div style="position: absolute; width: 300; left: 210; top: 30;padding-left: 1px;" id="ilmGrps">
                    <div style="position: absolute;width: 430px;">
                        <table>
                            <tr style="height:30px;">
                                <td style="margin-left: 10px">
                                    <s:radio cssStyle="margin-right:10" disabled="%{#attr.screenFieldsStatus}" name="reports.defaultIlmGrp" list="#{'noDefault':''}" tabindex="5"/>
                                </td>
                                <td id="selectGroup">
                                    <s:select id="reports.ilmGroup" name="reports.ilmGroup" cssStyle="width:145px;height: 21px ;" onchange="changeILMGroupNameStyle()" titleKey="tooltip.ilmReport.selectGroupILM" disabled="%{#attr.screenFieldsStatus}" tabindex="6" list="#request.ilmAccountGroups" listKey="value" listValue="label" />
                                    
                                </td>
                                <td width="15">&nbsp;</td>
                                <td width="200" id="tdOfGrpName"><span style="width: 200px;" id="ilmGrpName" name="ilmGrpName" class="spantext"></td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>
            </table>
            <div style="position: absolute; top: 180; margin-top: 10">
                <table style="position: absolute; width: 520">
                    <tr>
                        <td width="248" style="box-sizing: border-box;"><b style="position:relative;left:10px">
                                <s:text name="label.entityMonitor.date"/></b></td>
                        <td width="32">
                            <s:radio cssStyle="margin-right:10" name="reports.singleOrRange" list="#{'S':''}" tabindex="7"/>
                        </td>
                        <td width="115"><label title='<s:text name="ilmReport.singleDay"/>' for="4">
                                <s:text name="ilmReport.singleDay"/></label>&nbsp;</td>
                        <td width="32">
                            <s:radio name="reports.singleOrRange" list="#{'R':''}" tabindex="8"/>
                        </td>
                        <td width="150">&nbsp<label title='<s:text name="ilmReport.dateRange"/>' for="4">
                                <s:text name="ilmReport.dateRange"/></label>&nbsp;</td>
                    </tr>
                </table>
                <s:if test='"true" == #request.configScheduler' >
                    <div style="position: absolute; width: 450; left: 213; top: 25">
                        <div style="position: absolute;">
                            <s:textfield tabindex="9" titleKey="tooltip.reportDate" cssClass="htmlTextNumeric" name="reports.fromDateAsString" cssStyle="width:160px;margin-bottom:5px;height:21" readonly="false" maxlength="10" onchange="validateDateField('reports.fromDateAsString','fromDateAsString');" onmouseout="dateSelected=false" />
                            <A title='<s:text name="tooltip.calendarreportdate"/>' tabindex="10" name="datelink" ID="datelink" onClick="cal.select(document.forms[0].elements['reports.fromDateAsString'],'datelink',dateFormatValue);dateSelected=true;return false;"><img src="images/calendar-16.gif"></A>
                        </div>
                        <div id="divTo" style="position: absolute;left: 200; top: 5;visibility: hidden;">
                            <label>
                                <s:text name="movementsearch.valueto"/></label>
                        </div>
                        <div id="divToTime" style="position: absolute;left: 233;visibility: hidden;">
                            <s:textfield tabindex="11" titleKey="tooltip.reportDate" cssClass="htmlTextNumeric" name="reports.toDateAsString" cssStyle="width:160px;margin-bottom:5px;height:21;" readonly="false" maxlength="10" onchange="validateDateField('reports.toDateAsString','toDateAsString');" onmouseout="dateSelected=false" />
                            <A title='<s:text name="tooltip.calendarreportdate"/>' tabindex="12" name="datelink1" ID="datelink1" onClick="cal.select(document.forms[0].elements['reports.toDateAsString'],'datelink1',dateFormatValue);dateSelected=true;return false;"><img src="images/calendar-16.gif" /></A>
                        </div>
                    </div>
                </s:if>
                <s:if test='"true" != #request.configScheduler' >
                    <div style="position: absolute; width: 350; left: 213; top: 25">
                        <div style="position: absolute;">
                            <s:textfield tabindex="9" titleKey="tooltip.reportDate" cssClass="htmlTextNumeric" name="reports.fromDateAsString" cssStyle="width:80px;margin-bottom:5px;height:21" readonly="false" maxlength="10" onchange="validateDateField('reports.fromDateAsString','fromDateAsString');" onmouseout="dateSelected=false" />
                            <A title='<s:text name="tooltip.calendarreportdate"/>' tabindex="10" name="datelink" ID="datelink" onClick="cal.select(document.forms[0].elements['reports.fromDateAsString'],'datelink',dateFormatValue);dateSelected=true;return false;"><img src="images/calendar-16.gif"></A>
                        </div>
                        <div id="divTo" style="position: absolute;left: 120; top: 5;visibility: hidden;">
                            <label>
                                <s:text name="movementsearch.valueto"/></label>
                        </div>
                        <div id="divToTime" style="position: absolute;left: 153;visibility: hidden;">
                            <s:textfield tabindex="11" titleKey="tooltip.reportDate" cssClass="htmlTextNumeric" name="reports.toDateAsString" cssStyle="width:80px;margin-bottom:5px;height:21;" readonly="false" maxlength="10" onchange="validateDateField('reports.toDateAsString','toDateAsString');" onmouseout="dateSelected=false" />
                            <A title='<s:text name="tooltip.calendarreportdate"/>' tabindex="12" name="datelink1" ID="datelink1" onClick="cal.select(document.forms[0].elements['reports.toDateAsString'],'datelink1',dateFormatValue);dateSelected=true;return false;"><img src="images/calendar-16.gif" /></A>
                        </div>
                    </div>
                </s:if>
            </div>
            <div style="position: absolute; top: 233; margin-top: 15">
                <table style="position: absolute; width: 560px">
					<s:if test="#request.ilmReportType == @org.swallow.util.SwtConstants@ILM_REPORT_TYPE_BASEL_A">
                        <tr>
                            <td width="233" style="padding-left: 10px;"><b>
                                    <s:text name="ilmReport.sumInOutflows"/></b></td>
                            <td width="29" style="padding-right:5px;padding-left:5px;">
                                <s:radio name="reports.sumInOutFlows" list="#{'CB':''}" id="radio" titleKey="ilmReport.sumInOutflows.tooltip" />
                            </td>
                            <td width="135">
                                <s:text name="entity.predict.centralBank"/>&nbsp;</td>
                            <td width="35" style="padding-right:10px;">
                                <s:radio name="reports.sumInOutFlows" list="#{'GC':''}" titleKey="ilmReport.sumInOutflows.tooltip" />
                            </td>
                            <td width="230">
                                <s:text name="ilmReport.globalCcygrp"/>&nbsp;</td>
                        </tr>
                    </s:if>
					<s:if test="#request.ilmReportType == @org.swallow.util.SwtConstants@ILM_REPORT_TYPE_BASEL_B">
                        <tr>
                            <td width="230" style="padding-left: 10px;"><b>
                                    <s:text name="ilmReport.sumInOutflows"/></b></td>
                            <td width="29" style="padding-right:5px" ;>
                                <s:radio name="reports.sumInOutFlows" list="#{'CORR':''}" titleKey="ilmReport.sumInOutflows.tooltip" />
                            </td>
                            <td width="110">
                                <s:text name="ilmReport.correspondent"/>&nbsp;</td>
                            <td width="32">
                                <s:radio name="reports.sumInOutFlows" id="radioGC" list="#{'GC':''}" titleKey="ilmReport.sumInOutflows.tooltip" />
                            </td>
                            <td width="230">
                                <s:text name="ilmReport.globalCcygrp"/>&nbsp;</td>
                        </tr>
                    </s:if>
                </table>
                <table style="${reportTypeInRequest eq 'ILM_BASEL_A' || reportTypeInRequest eq 'ILM_BASEL_B'? 'position: absolute; width: 250; top:30px;': 'position: absolute; width: 250; top:5px;'}" id="currencyMultiplier">
                    <tr>
                        <td width="200" style="padding-left: 10px;"><b>
                                <s:text name="ccyMonitorOptions.useCcyMultiplier"/></b></td>
                        <td width="35">
                            <s:checkbox name="reports.useCcyMultiplier" fieldValue="Y" value='%{#request.reports.useCcyMultiplier == "Y"}' cssClass="htmlTextAlpha" />
                        </td>
                    </tr>
                </table>
                <table style="position: absolute; width: 560; top:25px;">
               		 <s:if test="#request.ilmReportType == @org.swallow.util.SwtConstants@ILM_REPORT_TYPE_MULTI_CURRENCY_ILM_REPORTE_EXCEL">
                        <tr height="25">
                            <td width="230" style="padding-left: 10px;"><b>
                                    <s:text name="ilmReport.criticaltransactions"/></b></td>
                            <td width="29" style="padding-right:5px" ;>
                                <s:radio name="reports.criticalTransactions" list="#{'SHOP':''}" titleKey="ilmReport.showonlypayments.tooltip" />
                                
                            </td>
                            <td width="390">
                                <s:text name="ilmReport.showonlypayments"/>&nbsp;</td>
                        </tr>
                        <tr>
                            <td width="230" style="padding-left: 10px;"></td>
                            <td width="32">
                                <s:radio name="reports.criticalTransactions" id="radioGC" list="#{'SHA':''}" titleKey="ilmReport.showall.tooltip" />
                                
                            </td>
                            <td width="390">
                                <s:text name="ilmReport.showall"/>&nbsp;</td>
                        </tr>
                    </s:if>
                </table>
                <table style="position: absolute; width: 630px; top:25px;">
					<s:if test="#request.ilmReportType == @org.swallow.util.SwtConstants@ILM_REPORT_TYPE_BASEL_C">
                        <tr height="25">
                            <td width="197" style="padding-left: 10px;"><b>
                                    <s:text name="ilmReport.costumerPayments"/></b></td>
                            <td width="35">
                                <s:radio name="reports.costumerPayments" list="#{'N':''}" titleKey="ilmReport.UsePartyCashflowData.tooltip" />
                                
                            </td>
                            <td><label>
                                    <s:text name="ilmReport.UsePartyCashflowData"/>&nbsp;</label></td>
                        </tr>
                        <tr>
                            <td width="197" style="padding-left: 10px;"></td>
                            <td width="35">
                                <s:radio name="reports.costumerPayments" id="radioGC" list="#{'Y':''}" titleKey="ilmReport.UsePaymentTypeData.tooltip" />
                                
                            </td>
                            <td><label>
                                    <s:text name="ilmReport.UsePaymentTypeData"/></label>&nbsp;</td>
                        </tr>
                    </s:if>
                </table>
                <table style="position: absolute; width: 630px; top:80px;  padding-left: 42px; ">
				 <s:if test="#request.ilmReportType == @org.swallow.util.SwtConstants@ILM_REPORT_TYPE_BASEL_C">
                        <s:if test='"Y" != #request.costumerPayments' >
                            <tr height:25px;>
                                <td width="500" style="padding-left: 10px;"></td>
                                <td style="padding-right: 20px; padding-left: 87px">
                                    <s:text name="ilmReport.include"/>
                                </td>
                                <td width="32">
                                    <s:checkbox disabled="true" name="reports.loroPayments" fieldValue="L" value='%{#request.reports.loroPayments == "L"}' cssClass="htmlTextAlpha" />
                                </td>
                                <td width="200">
                                    <s:text name="ilmReport.LoroClient"/>&nbsp;</td>
                                <td width="32">
                                    <s:checkbox disabled="true" name="reports.corporatePayments" fieldValue="C" value='%{#request.reports.corporatePayments == "C"}' cssClass="htmlTextAlpha" />
                                </td>
                                <td width="200">
                                    <s:text name="ilmReport.CorporateClient"/>&nbsp;</td>
                            </tr>
                            <tr>
                                <td width="300" style="padding-left: 8px;"></td>
                                <td width="32"></td>
                                <td width="32">
                                    <s:checkbox disabled="true" name="reports.branchPayments" fieldValue="B" value='%{#request.reports.branchPayments == "B"}' cssClass="htmlTextAlpha" />
                                </td>
                                <td width="390">
                                    <s:text name="ilmReport.Branch"/>&nbsp;</td>
                                <td width="32">
                                    <s:checkbox disabled="true" name="reports.otherPayments" fieldValue="O" value='%{#request.reports.otherPayments == "O"}' cssClass="htmlTextAlpha" />
                                </td>
                                <td width="390">
                                    <s:text name="ilmReport.Other"/>&nbsp;</td>
                            </tr>
                        
                        </s:if>
                        <s:if test='"Y" == #request.costumerPayments' >
                            <tr height:25px;>
                                <td width="500" style="padding-left: 10px;"></td>
                                <td style="padding-right: 20px; padding-left: 87px">
                                    <s:text name="ilmReport.include"/>
                                </td>
                                <td width="32">
								<s:checkbox name="reports.loroPayments" fieldValue="L" value="%{#request.reports.loroPayments == 'true' || #request.reports.loroPayments == 'Y'}" cssClass="htmlTextAlpha" />
                                </td>
                                <td width="200">
                                    <s:text name="ilmReport.LoroClient"/>&nbsp;</td>
                                <td width="32">
                                    <s:checkbox name="reports.corporatePayments" fieldValue="C" value='%{#request.reports.corporatePayments == "C"}' cssClass="htmlTextAlpha" />
                                </td>
                                <td width="200">
                                    <s:text name="ilmReport.CorporateClient"/>&nbsp;</td>
                            </tr>
                            <tr>
                                <td width="300" style="padding-left: 8px;"></td>
                                <td width="32"></td>
                                <td width="32">
                                    <s:checkbox name="reports.branchPayments" fieldValue="B" value='%{#request.reports.branchPayments == "B"}' cssClass="htmlTextAlpha" />
                                </td>
                                <td width="390">
                                    <s:text name="ilmReport.Branch"/>&nbsp;</td>
                                <td width="32">
                                    <s:checkbox name="reports.otherPayments" fieldValue="O" value='%{#request.reports.otherPayments == "O"}' cssClass="htmlTextAlpha" />
                                </td>
                                <td width="390">
                                    <s:text name="ilmReport.Other"/>&nbsp;</td>
                            </tr>
                        </s:if>
                    </s:if>
                </table>
            </div>
        </div>
        <!-- 		<div id="pnlRefreshIcon" style="position: absolute; top:447;left: 593px; width: 10px; height: 15px;display: none;"> -->
        </div>
        <div id="ilmReport" style="position: absolute; left: 580; top: 443; width: 70px; height: 30px; visibility: visible;">
            <table width="60px" border="0" cellspacing="0" cellpadding="0" height="20">
                <tr>
                    <td align="Right" style="padding-top: 2px;"><a tabindex="13" href=# onclick="javascript:openWindow(buildPrintURL('print','ILM Report'),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')" onMouseOut="MM_swapImgRestore()" onMouseOver="MM_swapImage('Help','','images/help_default.GIF ',1)"><img src="images/help_default.GIF " name="Help" border="0" title='<s:text name="tooltip.helpScreen"/>'></a></td>
                </tr>
            </table>
        </div>
        <div id="ddimagebuttons" color="#7E97AF" style="position: absolute; left: 10; top: 434; border: 2px outset; width: 650px; height: 40px; visibility: visible;">
            <div id="ilmReport" style="position: absolute; left: 6; top: 4; width: 425; height: 15px; visibility: visible;">
                <table width="140" border="0" cellspacing="0" cellpadding="0" style="width:600px" height="20">
                    <tr>
                        <s:if test='"true" == #request.configScheduler' >
                            <td title='<s:text name="tooltip.save"/>'><a tabindex="14" width="70" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onclick="saveSchedulerReportOptions('ILMReport');">
                                    <s:text name="button.save"/></a></td>
                        </s:if>
                        <s:if test='"true" != #request.configScheduler' >
                            <td title='<s:text name="tooltip.submitbutton"/>'><a tabindex="14" width="70" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onclick="report('ILMReport');">
                                    <s:text name="button.report"/></a></td>
                            <%-- 							<td id="cancelbutton" title='<s:text name="tooltip.cancel"/>'> --%>
                            <!-- 								<a tabindex="15" onMouseOut="collapsebutton(this)" -->
                            <!-- 								onMouseOver="highlightbutton(this)" -->
                            <!-- 								onMouseDown="expandbutton(this)" -->
                            <%-- 								onMouseUp="highlightbutton(this)" onclick="cancelReport();"><s:message --%>
                            <%-- 										key="button.cancel" /></a></td> --%>
                        </s:if>
                        <td id="closebutton" title='<s:text name="tooltip.cancel"/>'>
                            <a tabindex="15" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onclick="confirmClose('C')">
                                <s:text name="button.close"/></a></td>
                        <td style="padding-left: 15px;font-style: italic;"><label id="warningCB" style="width: 300px;visibility: hidden;">
                                <s:text name="ilmReport.centralBankNotFound"/>
                            </label>&nbsp;</td>
                    </tr>
                </table>
            </div>
            <div id="report" style="position: absolute; left: 6; top: 4; width: 425; height: 15px; visibility: hidden;">
                <table width="200" border="0" cellspacing="0" cellpadding="0" height="20" style="visibility: hidden">
                    <tr>
                        <td id="cancelenablebutton"><a tabindex="15" title='<s:text name="tooltip.cancel"/>' onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onClick="javascript:cancelReport();">
                                <s:text name="button.cancel"/></a></td>
                        <td id="canceldisablebutton"><a class="disabled" disabled="disabled">
                                <s:text name="button.cancel"/></a></td>
                    </tr>
                </table>
            </div>
        </div>
    </s:form>
</body>

</html>