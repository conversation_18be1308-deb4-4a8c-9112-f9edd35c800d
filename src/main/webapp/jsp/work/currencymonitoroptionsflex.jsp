<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<!--
  - The main purpose of this jsp file is to load the Currency Monitor Options screen.
  - Also, to embed the sub screens of Currency Monitor Options screen:
  - (i) Personal Entity List
  - (ii) Personal Currency List
  - Also, to load the label values for this screen.
  - 
  - Modified by: I. Marshal <PERSON>
  - Date: 01-06-2011
  -->
<html>
<head>
<%@ include file="/taglib.jsp"%>
<title><s:text name="currencyMonitor.options.title" /></title>

<script type="text/javascript">
	var cancelcloseElements = new Array(1);
	var defaultDaysChange = false;
	cancelcloseElements[0] = "cancelbutton";
	
	<s:if test='#request.parentFormRefresh == "Y"'>
	window.opener.refreshPending = true;
	self.close();
	</s:if>
	
	/*Start: Modified for Mantis 1386:"User defined option to show normal and small font" by Marshal on 31-May-2011*/
	/**
      * This function is used to submit the form changes when the Save
	  * button in the Options screen is clicked
	  *
	  * @param methodName - method name when the save button is being clicked
	  */
	function submitForm (methodName) {
		   // Gets the entered defaultDays value
		   var defaultDays=validateField(document.forms[0].elements['currMonitorOptions.numberOfDays'],'currMonitorOptions.numberOfDays','numberPat');
		   // Gets the entered rateStatus value
		   var rateStatus=validateField(document.forms[0].elements['currMonitorOptions.propertyValue'],'currMonitorOptions.propertyValue','numberPat');
		   // Submit form if conditions are satisfied 
		   var submitFormFlag = false;
	   // Checks the availability of defaultDays, else set the focus on it	   
	   if(defaultDays) {
	   	// Checks the availability of rateStatus, else set the focus on it
		if (rateStatus) {	
		  // If both the validation of default days and refresh rate is true, submit the form.		
		  if (validateForm() && validateRefreshRate()) {		  
		    	document.forms[0].method.value = methodName;
		    	
		    	var result = checkDateRange();
		    	if(defaultDaysChange){
		    		if(result){
		    		// Calls the confirm function to prompt the user on setting 5 as the refresh rate
					var refreshRateConfirm = confirm('<s:text name="currencyMonitor.alert.dateRange"/>');
						// Added by KaisBS for mantis 1868 (issue 1054_SEL_091)
				    	// Alert the user that changed values will not be applied until he restarts the screen.
						if (refreshRateConfirm) {
							submitFormFlag = true;
						} else {
							document.forms[0].elements['currMonitorOptions.numberOfDays'].focus();
						}
		    		}else {
						submitFormFlag = true;
		    		}
				alert('<s:text name="currencyMonitor.alert.defDayschanged"/>');
		    	}else {
					submitFormFlag = true;
		    	}
		 	  }
		   } else {				
				document.forms[0].elements['currMonitorOptions.propertyValue'].focus();
		   }
		}
		else {			
		    document.forms[0].elements['currMonitorOptions.numberOfDays'].focus();
		}
	   if(submitFormFlag)
			document.forms[0].submit();
	}
	/*End: Modified for Mantis 1386:"User defined option to show normal and small font" by Marshal on 31-May-2011*/
	
	//Function return of the system date + numberOfDays entred by the user exeed the date range.
	function checkDateRange() {
		var dateFormat = window.opener.dateFormat;
		var systemDate = window.opener.dbDate;
		var nDaysAheadToToday=window.opener.nDaysAheadToToday;
		var result = convertUKtoUS(systemDate,dateFormat) ;
		var todate   = new Date(result);
		var aheadDate=new Date(result);
		var numberOfDays = document.forms[0].elements["currMonitorOptions.numberOfDays"].value;
		var days = parseInt(numberOfDays,10);
		todate.setDate(todate.getDate()+days);
		aheadDate.setDate(aheadDate.getDate()+nDaysAheadToToday);
		if( todate > aheadDate){
			return true;
		}else {
			return false;
		}
	}
	//Convert date from Uk to US format
	function convertUKtoUS(input,dateFormat)
	{
		var rtn=null;
			if (dateFormat.toLowerCase() == "dd/mm/yyyy")
			{
				var dateArry=input.split("/");
				rtn=dateArry[1] + "/" + dateArry[0] + "/" + dateArry[2];
			}
			else
			{
				rtn=input;//Already in US format
			}
		
		return rtn;
	}
	function validateForm () {
		var defDays = document.forms[0].elements["currMonitorOptions.numberOfDays"].value;
		
		if (defDays > 14 || defDays < 1) {
			alert('<s:text name="currencyMonitor.alert.defDays"/>');
			return false;
		} else {
			return true;
		}
	}	
	
	function bodyOnLoad () {
		var menuAccessIdParent = getMenuAccessIdOfChildWindow("currmonitorNew.do");		
		/*if (menuAccessIdParent != "0") {
			document.getElementById("savebutton").innerHTML = document.getElementById("savedisablebutton").innerHTML;
		}	*/	
	}
	
	window.onload = function () {
		ShowErrMsgWindow ('${actionError}');
		bodyOnLoad();
		setParentChildsFocus();
		setTitleSuffix(document.forms[0]); 
	}
	
	window.onunload = call;
	
	function closeWindow() {
		window.opener.callApp("Y");
		window.close();
	}
	
	function callParent() {
		window.opener.callApp("Y");
	}
	
	/*Start: Added for Mantis 1386:"User defined option to show normal and small font" by Marshal on 31-May-2011*/
	/**
	  * This function is used to validate the given refresh rate.
      *
	  */
	function validateRefreshRate() {
		var refreshRate=document.getElementById("currMonitorOptions.propertyValue").value;
		// Checks the entered refresh rate, else set the focus to the rate text box
		if (refreshRate) {
		// Checks the given refresh rate is less than 5 and sets it's value accordingly
		if (refreshRate < 5) {
			// Calls the confirm function to prompt the user on setting 5 as the refresh rate
			var refreshRateConfirm = confirm('<s:text name="alert.currencyMonitor.confirmRefreshRate"/>');
			// Checks and sets the refresh rate to 5, else set the focus to the rate text box
			if (refreshRateConfirm) {
				document.getElementById("currMonitorOptions.propertyValue").value = 5;				
			} else {
				document.forms[0].elements['currMonitorOptions.propertyValue'].focus();
				return false;
			}
		}
		return true;
		} else {
			alert('<s:text name="alert.currencyMonitor.refreshRate"/>');
			document.forms[0].elements['currMonitorOptions.propertyValue'].focus();
			return false;
		}		
	}
	/*End: Added for Mantis 1386:"User defined option to show normal and small font" by Marshal on 31-May-2011*/
	// Added by KaisBS to catch the change of defaultdays	 
	function affectVar(){
		defaultDaysChange = true;
	}
	
	
	</script>
</head>

<body leftmargin="0" topmargin="0" marginheight="0" onunload="callParent()">
<s:form action="currmonitorNew.do">
	<input name="method" type="hidden" value="">
	<!-- Start: Modified for Mantis 1386: User defined option to show normal and small fonts by Marshal on 19-May-2011 -->
	<div id="MonitorOptions"
		style="position: absolute; left: 20px; top: 20px; width: 470; height: 184px; border: 2px outset;"
		color="#7E97AF">
	<div id="MonitorOptions"
		style="position: absolute; left: 8px; top: 4px; width: 470px; height: 100;">
	<table width="415" border="0" cellpadding="0" cellspacing="0"
		height="30">
		<tr height="24">
			<td width="180px"><b><s:text name="ccyMonitorOptions.useCcyMultiplier" /></b></td>

			<td width="120px"><s:checkbox
				name="currMonitorOptions.useCurrencyMultiplier" value='%{#request.currMonitorOptions.useCurrencyMultiplier =="Y"}'
				cssStyle="width:13;" cssClass="htmlTextAlpha" fieldValue="Y"
				titleKey="ccyMonitorOptions.useCcyMultiplier" tabindex="1" /></td>
		</tr>


		<tr height="24">
			<td width="180px"><b><s:text name="ccyMonitorOptions.hideWeekends" /></b></td>

			<td width="120px"><s:checkbox
				name="currMonitorOptions.hideWeekends" cssStyle="width:13;"
				cssClass="htmlTextAlpha" fieldValue="Y" value='%{#request.currMonitorOptions.hideWeekends =="Y"}'
				titleKey="ccyMonitorOptions.hideWeekends" tabindex="2" /></td>
		</tr>

		<tr height="24">
			<td width="180px"><b><s:text name="ccyMonitorOptions.hideLoro" /></b></td>

			<td width="120px"><s:checkbox
				name="currMonitorOptions.hideLoro" cssStyle="width:13;"
				cssClass="htmlTextAlpha" fieldValue="Y" value='%{#request.currMonitorOptions.hideLoro =="Y"}'
				titleKey="ccyMonitorOptions.hideLoro" tabindex="3" /></td>
		</tr>

		<tr height="24">
			<td width="180px"><b><s:text name="ccyMonitorOptions.defaultDays" /></b></td>

			<td width="120px" align="Left"><s:textfield
				name="currMonitorOptions.numberOfDays" maxlength='2'
				cssStyle="htmlTextNumeric" tabindex="4" style="width:50px;"
				onchange="validateField(this,'currMonitorOptions.numberOfDays','numberPat');affectVar();"
				titleKey="ccyMonitorOptions.enterDefaultDays" /></td>
		</tr>

		<tr height="24">
			<td width="180px"><b><s:text name="ccyMonitorOptions.usePersonalCurrencyList" /></b></td>

			<td width="120px"><s:checkbox
				name="currMonitorOptions.ccyPersonalList" cssStyle="width:13;"
				cssClass="htmlTextAlpha" fieldValue="Y" value='%{#request.currMonitorOptions.ccyPersonalList =="Y"}'
				titleKey="ccyMonitorOptions.usePersonalCurrencyList" tabindex="5" />
			</td>
		</tr>

		<tr height="24">
			<td width="180px"><b><s:text name="ccyMonitorOptions.refreshRate" /></b></td>

			<td width="120px"><s:textfield
				name="currMonitorOptions.propertyValue" maxlength='3'
				cssStyle="htmlTextNumeric" tabindex="6" style="width:50px;"
				onchange="return validateField(this,'currMonitorOptions.propertyValue','numberPat');"
				titleKey="tooltip.ccyMonitorOptions.enterRefreshRate" /></td>
		</tr>

		<tr height="24">
			<td width="180px"><b><s:text name="ccyMonitorOptions.fontSize" /></b></td>

			<td width="160px"><s:radio id="1" cssStyle="width:13px"
				tabindex="7" name="currMonitorOptions.fontSize" list="#{'Normal':''}"
				titleKey="tooltip.ccyMonitorOptions.selectFontSizeNormal" /> <label
				title="" for="1"><s:text name="ccyMonitorOptions.fontSizeNormal" />&nbsp;</label> <s:radio
				id="2" cssStyle="width:13px" tabindex="8"
				name="currMonitorOptions.fontSize" list="#{'Small':''}"
				titleKey="tooltip.ccyMonitorOptions.selectFontSizeSmall" /> <label
				title="" for="2"><s:text name="ccyMonitorOptions.fontSizeSmall" /></label></td>

		</tr>
	</table>
	</div>
	</div>

	<div id="ddimagebuttons"
		style="position: absolute; z-index: 99; left: 290px; top: 100px; width: 100px; height: 0px; visibility: visible">
	<div id="buttonstatus"
		style="position: absolute; left: 12; top: 19; width: 100px; height: 30px; visibility: visible;">
	<table width="70" border="0" cellspacing="0" cellpadding="0">
		<tr>
			<td id="linkbutton"></td>
		</tr>
		<tr height="25px" valign="bottom">
			<td id="contactbutton" style="padding-bottom: 5px;"><a tabindex="5"
				title='<s:text name="tooltip.Currencies"/>'
				onMouseOut="collapsebutton(this)"
				onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)"
				onMouseUp="highlightbutton(this)"
				onClick="javascript:openWindow('personalcurrency.do?','personalCurrencyMaintenanceWindow','left=50,top=190,width=560,height=300,toolbar=0, resizable=yes','true')"><s:text name="button.currency" /></a></td>
		</tr>
	</table>
	</div>
	</div>

	<div id="MonitorOptionsButtons"
		style="position: absolute; left: 390; top: 218; width: 70px; height: 15px; visibility: visible;">
	<table width="60px" border="0" cellspacing="0" cellpadding="0"
		height="20">
		<tr>
			<td align="Right"><a tabindex="7" href=#
				onclick="javascript:openWindow(buildPrintURL('print','Monitor Options'),'monitoroptionswindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')"
				onMouseOut="MM_swapImgRestore()"
				onMouseOver="MM_swapImage('Help','','images/help_default.GIF ',1)"><img
				src="images/help_default.GIF " name="Help" border="0"
				title='<s:text name="tooltip.helpScreen"/>'></a></td>
			<td align="right" id="Print"><a tabindex="8"
				onclick="printPage();" onMouseOut="MM_swapImgRestore()"
				onMouseOver="MM_swapImage('Print','','images/Print_R.gif ',1)"><img
				src="images/Print.gif " name="Print" border="0"
				title='<s:text name= "tooltip.printScreen"/>'></a></td>
		</tr>
	</table>
	</div>
	<div id="ddimagebuttons" color="#7E97AF"
		style="position: absolute; border: 2px outset; left: 20px; top: 210; border: 2px outset; width: 470; height: 39px; visibility: visible;">
	<div id="MonitorOptionsButtons"
		style="position: absolute; left: 6; top: 4; width: 280px; height: 15px; visibility: visible;">

	<table width="140" border="0" cellspacing="0" cellpadding="0"
		height="20">
		<tr>
			<td id="savebutton" width="70px"><a tabindex="9"
				title='<s:text name = "tooltip.save"/>'
				onMouseOut="collapsebutton(this)"
				onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)"
				onMouseUp="highlightbutton(this)"
				onclick="javascript:submitForm('saveCurrencyMonitorOptions')"><s:text name="button.save" /></a></td>
			<td id="cancelbutton" width="70px"><a
				title='<s:text name= "tooltip.canceloption"/>' tabindex="10"
				onMouseOut="collapsebutton(this)"
				onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)"
				onMouseUp="highlightbutton(this)" onclick="closeWindow();"><s:text name="button.cancel" /></a></td>
		</tr>
	</table>
	<!-- End: Modified for Mantis 1386: User defined option to show normal and small fonts by Marshal on 19-May-2011 -->
	</div>
	<div
		style="position: absolute; left: 6; top: 4; width: 470; height: 15px; visibility: hidden;">
	<table width="7" border="0" cellspacing="0" cellpadding="0" height="20"
		style="visibility: hidden">
		<tr>
			<td id="savedisablebutton"><a class="disabled"
				disabled="disabled"><s:text name="button.save" /></a></td>
		</tr>
	</table>
	</div>


	</div>
</s:form>
</body>
</html>