<!doctype html>
<%@ include file="/taglib.jsp"%>
<%@ page import="org.swallow.util.SwtConstants"%>
<html lang="en">
<head>
<meta charset="utf-8">
<title><s:text name = "ilmthroughputbreakdown.title.window"/></title>
<!-- <base href="."> -->
<meta name="viewport" content="width=device-width, initial-scale=1">
<link rel="icon" type="image/x-icon" href="favicon.ico">
<link rel="stylesheet" href="angularSources/styles.css?version=<%= SwtUtil.appVersion %>">
</head>
<body>
<%@ include file="/angularJSUtils.jsp"%>
<script type="text/javascript">
var appName = "<%=SwtUtil.appName%>";
var baseURL = new String('<%=request.getRequestURL()%>');
var screenRoute = "ILMThroughPutBreakDown";
var label = new Array ();
label["text"] = new Array ();
label["tip"] = new Array ();

label["text"]["entity"] = "<s:text name="message.entityId"/>";	
label["tip"]["entity"] = "<s:text name="tooltip.selectEntityid"/>";

label["text"]["threshold"] = "<s:text name="mvmt.applyCurrencyThreshold"/>";
label["tip"]["threshold"] = "<s:text name="tooltip.applyCurrencyThreshold"/>";
			
label["text"]["button-refresh"] = "<s:text name="button.Refresh"/>";
label["tip"]["button-refresh"] = "<s:text name="tooltip.refreshWindow"/>";

label["text"]["button-movement"] = "<s:text name="button.mvmnt"/>";
label["tip"]["button-movement"] = "<s:text name="tooltip.showSelMovDetail"/>";

label["text"]["button-notes"] = "<s:text name="button.notes"/>";
label["tip"]["button-notes"] = "<s:text name="tooltip.mvmntnotes"/>";

label["text"]["button-message"] = "<s:text name="button.message"/>";
label["tip"]["button-message"] = "<s:text name="tooltip.msgSelMvm"/>";

label["text"]["button-close"] = "<s:text name="button.close"/>";
label["tip"]["button-close"] = "<s:text name="tooltip.close"/>";

label["text"]["button-options"] = "<s:text name="button.options"/>";
label["tip"]["button-options"] = "<s:text name="tooltip.options"/>";
/**
 * help
 * This function opens the help screen 
 * @return none
 */
function help(){
	openWindow(buildPrintURL('print','ILM ThroughPut BreakDown'),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')
}

function setMsgButtonStatus(movId) {    
	var oXMLHTTP = new XMLHttpRequest();
	var sURL = baseURL + appName+"/movement.do?method=getMvmntMessageCount";
		sURL+= '&movmentId=' + movId;		
	oXMLHTTP.open( "POST", sURL, false );
	oXMLHTTP.send();	

	var concatStr = new String(oXMLHTTP.responseText);		
	return ""+concatStr;
}

/**
 * This function is used to display the Movement Display screen
 *@param methodName
 *@param movementId
 *@param entityid
 *@param isNotesPresent
 *return boolean
 *
 */
function showMvmnt(methodName, movementId, entityId, isNotesPresent) {
	//variable declared for menuAccessIdOfChildWindow
	var menuAccessIdOfChildWindow = getMenuAccessIdOfChildWindow("Movement Display");
	//variable declared for menuName
	var menuName = new String(
			'<s:text name="mvmDisplay.title.window"/>');
	//variable declared for smrtPredPos
	var smrtPredPos = menuName
			.search('<s:text name="alert.SmartPredict"/>');
	menuName = menuName.substr(0, smrtPredPos - 3);
	//access level check
	if (menuAccessIdOfChildWindow == 2) {
		alert('<s:text name="alert.AccessNotAvl"/>' + menuName
				+ '<s:text name="alert.ContactSysAdm"/>');
	} else {
		//to open Movement display screen  
		var movId = movementId;
		var param = 'movement.do?method=' + methodName;
		param += '&entityCode=' + entityId;
		param += '&movementId=' + movId;
		param += '&archiveId=${archiveId}';
		param += '&isNotesPresent=' + isNotesPresent;
		param += '&menuAccessId=' + menuAccessIdOfChildWindow;
		//Code Modified by Chinniah for Mantis 2066 on 4-Oct-2012:Movement is not locked while changing
		param += '&initialInputScreen='
				+ '${requestScope.initialinputscreen}';
		openWindow(
				param,
				'movementWindow',
				'left=50,top=190,width=994,height=789,toolbar=0, resizable=yes, status=yes, scrollbars=yes',
				'true');
	}
	return false;
}
function openMsgDisplayWindow(movementId, noOfMsgsSelectedMov, mesId) {
	if (noOfMsgsSelectedMov == 1) {
		var param = 'inputexceptionsmessages.do?method=single&seqid=';
		param += mesId;
		javascript: openWindow(
				param,
				'messageViewScreen',
				'left=50,top=190,width=650,height=500,toolbar=0, resizable=yes, scrollbars=yes',
				'true')

	} else {
		var param = 'movement.do?method=mvmntMessageDisplay';
		param += '&movmentId=' + movementId;
		javascript: openWindow(
				param,
				'movMessageWindow',
				'left=50,top=190,width=560,height=445,toolbar=0, resizable=yes, scrollbars=yes',
				'true');
	}
}

function checkLockOnServer(movementId)
{
	
	var oXMLHTTP = new XMLHttpRequest();
	var requestURL = new String('<%=request.getRequestURL()%>');
	var idy = requestURL.indexOf('/'+appName+'/');
	requestURL=requestURL.substring(0,idy+1) ; 
	requestURL = requestURL + appName+"/movementLock.do?method=checkLock"
	requestURL = requestURL + "&movementId="+movementId;
	oXMLHTTP.open( "POST", requestURL, false );
	oXMLHTTP.send();
	var str=oXMLHTTP.responseText;
	return str;
}


	function sendParams(params) {
		for ( var key in params) {
			if (params.hasOwnProperty(key)) {
				try {
					if (key != "nocache"){
						document.getElementById(key).value = params[key]	
					}
				} catch (e) {
				}
			}
		}
		document.getElementById('ccyThresholdCheckbox').value = "Y";
		document.getElementById('exportDataForm').action = 'ilmAnalysisMonitor.do?method=ilmThrouputBreakDownReport';
		document.forms[0].submit();

	}

	/*
	 * This function is used to open Movement notes screen
	 * @param mname
	 * @param movementId
	 * @param isNotesPresent
	 */
	function movementNotesFromSearch(mname, movementId, isNotesPresent,
			entityId, currencyId, dateStr, menuEntityCurrGrpAccess) {
		//framing url for sending reguest
		var param = 'notes.do?method=' + mname + '&movId=';
		param += movementId;
		param += '&entityCode=' + entityId;
		param += '&screenName=' + 'searchParams';
		param += '&isNotesPresent=' + isNotesPresent;
		param += '&currencyCode=' + currencyId;
		param += '&date=' + dateStr;
		param += '&archiveId=${archiveId}';
		param += '&currencyAccess=' + menuEntityCurrGrpAccess;
		//check the lock of movement id if the method name is search
		var disBtnLock = checkLockOnServer(movementId);
		if (disBtnLock != "true")
			disBtnLock = "Y";
		else
			disBtnLock = "N";
		param += '&disBtnLock=' + disBtnLock;
		//function to new window, i.e., Movement notes
		openWindow(
				param,
				'NotesWindow',
				'left=50,top=190,width=588,height=272,toolbar=0, resizable=yes, scrollbars=yes',
				'true');
	}
</script>
<%@ include file="/angularscripts.jsp"%>
	
	<form id="exportDataForm" method="post" enctype="multipart/form-data" target="tmp">
		<input name="exportType" id="exportType" type="hidden" value="">
		<input name="reportType" id="reportType" type="hidden" value="">
		<input name="currentPage" id="currentPage" type="hidden" value="">
		<input name="pageCount" id="pageCount" type="hidden" value="">
		<input name="accountGroup" id="accountGroup" type="hidden" value="">
		<input  name="selectedDate" id="selectedDate" type="hidden" value="">
		<input name="selectedScenario" id="selectedScenario" type="hidden" value="">
		<input name="currencyId" id="currencyId" type="hidden" value="">
		<input name="entityId" id="entityId" type="hidden" value="">
		<input name="currentFilter" id="currentFilter" type="hidden" value="">
		<input name="applyThreshold" id="applyThreshold" type="hidden" value="">
		<input name="ccyThresholdCheckbox" id="ccyThresholdCheckbox" type="hidden" value="">
		<input name="unsettledOutflows" id="unsettledOutflows" type="hidden" value="">
		<input name="actInlflowsCheckbox" id="actInlflowsCheckbox" type="hidden" value="">
		<input name="foreIntlflowsCheckbox" id="foreIntlflowsCheckbox" type="hidden" value="">
		<input name="actOutlflowsCheckbox" id="actOutlflowsCheckbox" type="hidden" value="">
		<input name="foreOutlflowsCheckbox" id="foreOutlflowsCheckbox" type="hidden" value="">
		<input name="screen" id="screen" type="hidden" value="">
		<input name="selectedSort" id="selectedSort" type="hidden" value="">
		<input name="selectedFilter" id="selectedFilter" type="hidden" value="">
		<input name="tokenForDownload" id="tokenForDownload" type="hidden" value="">
		<input type="hidden" name="tokenForDownload" id="download_token_value_id"/>
		<input id="mybutton" type="submit" value="post" style="visibility: hidden;" />
		<iframe name="tmp" width="0%" height="0%" src="#" style="border-width: 0px; height: 0px;"></iframe>
	</form>
</body>
</html>
