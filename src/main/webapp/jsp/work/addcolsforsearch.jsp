<!doctype html>
<%@ include file="/taglib.jsp"%>
<%@ page import="org.swallow.util.SwtConstants"%>

<html lang="en">
<head>
<meta charset="utf-8">
<title><s:text name = "movementsearch.criteria.title"/></title>
<!-- <base href="./"> -->
<meta name="viewport" content="width=device-width, initial-scale=1">
<link rel="icon" type="image/x-icon" href="favicon.ico">
<link rel="stylesheet" href="angularSources/styles.css?version=<%= SwtUtil.appVersion %>">
</head>
<body onunload="closeAndUpdate()">
<%@ include file="/angularJSUtils.jsp"%>

<script type="text/javascript">
var appName = "<%=SwtUtil.appName%>";
var baseURL = new String('<%=request.getRequestURL()%>');
var screenRoute = "addColsForSearch";
var msdDisplayColsList=  "${requestScope.msdDisplayColsList}";
var currencyFormat=  "${requestScope.currencyFormat}";
var dateFormat=  "${requestScope.dateFormat}";
function updateSearchFilter(extraFilter){
	window.opener.extraFilter= extraFilter;
	self.close();
}

function closeHandler(){
	self.close();
}

function closeAndUpdate() {
	 Main.closeAndUpdate();
	}
</script>
<%@ include file="/angularscripts.jsp"%>
</body>
</html>