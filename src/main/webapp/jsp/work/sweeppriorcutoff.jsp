<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@ include file="/taglib.jsp"%>
<%@ page import="org.swallow.util.SwtConstants"%>
<%@ page import="org.swallow.util.CommonDataManager"%>
<%@ page import="org.swallow.util.SwtUtil"%>

<html>
<head>
<title><s:text name="sweepPrior.title.window" /></title>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">


<SCRIPT language="JAVASCRIPT">
var cancelcloseElements = new Array(1);
cancelcloseElements[0] = "cancelbutton";


var lastRefTime = "${requestScope.lastRefTime}";

var requestURL = new String('<%=request.getRequestURL()%>');
var appName = "<%=SwtUtil.appName%>"; 
var idy = requestURL.indexOf('/'+appName+'/');	
requestURL=requestURL.substring(0,idy+1);

			



var mouse_click_counter=0;
var mouse_down_counter=0;
var refreshFlag_drop_down=true;

var checkClickStatus= false; 


var autoRefreshRate= "${requestScope.autoRefreshRate}";
var filterstatus= "${requestScope.filterStatus}";
var sortStatus="${requestScope.sortStatus}";
var sortDescending="${requestScope.sortDescending}";
var columns = "${requestScope.totalColumns}";
var pageLoaded = true;

var appName = "<%=SwtUtil.appName%>";
var requestURL = new String('<%=request.getRequestURL()%>');
var idy = requestURL.indexOf('/'+appName+'/');
requestURL=requestURL.substring(0,idy+1) ;
var menuEntityCurrGrpAccess = "${requestScope.menuEntityCurrGrpAccess}";

/*Added to apply style on mandatory field ,if it's equal to undefined then no field will be colored*/
 mandatoryFieldsArray= "undefined" ;

var pvousObj = null;
function click_on_drop(event)
{
	setFlag2();

	ev= event||window.event;
	pvousObj = ev.srcElement;
	mouse_click_counter++;	
	if (checkClickStatus) {
		resetFlag2();		
	}
	checkClickStatus = false;

}


function mouse_down(){
	ev= event||window.event;
	mouse_down_counter++;

	if(mouse_down_counter==2){
		var  maxoption=30;
		var scroll_width=20;

		if(this.options.length<=maxoption){
			var x= this.offsetHeight*this.options.length;
			scroll_width=0;
		}
		else
			var x= this.offsetHeight*30;

		 
		if( ev.offsetX<0|| ev.offsetY<0 ){
			resetFlag2();
			mouse_down_counter=0;
		}
		 

		else 
		if( ev.offsetX>this.offsetWidth){
			resetFlag2();
			mouse_down_counter=0;
		}
		else 
		if(ev.offsetX<=this.offsetWidth&& ev.offsetY<=this.offsetHeight) 
		{// calculate  hight  and  width  of  select  box  .... 


			if (pvousObj.sourceIndex == ev.srcElement.sourceIndex) {
			checkClickStatus = true; //Click is made on the select box	
			mouse_down_counter=0;
			}else {
			mouse_down_counter--;
			}

			resetFlag2();
		}
		else

		if(ev.offsetX<=this.offsetWidth-(scroll_width)&& ev.offsetY<=x) // calculate  hight  and  width  of  select  box  .... 
		{
		
		
			if (pvousObj.sourceIndex == ev.srcElement.sourceIndex){		
			checkClickStatus = true;  //Click is made inside the select box
			}
			
			resetFlag2();
			mouse_down_counter=0;
		}
		

		else
		mouse_down_counter--;
		}

}




function setFlag2(){
	refreshFlag_drop_down=false;
}

function resetFlag2(){
	refreshFlag_drop_down=true;
}


function refreshScreen(){

document.getElementById("refreshbutton").innerHTML = document.getElementById("refreshdisablebutton").innerHTML;


	if(refreshFlag_drop_down==true){	
		maintainSortFilterStatus();
		//To prevent closing the child window on autoRefresh
		isDoNotCloseMyChilds= true;	
		//Code Changed for stoping auto refresh till screen is fully Loaded
		if(pageLoaded)
		{
		   pageLoaded = false;
			submitForm('displaySweepDetails','','Y');
		}
	
    }
	
	var refreshScreenCheckObj = window.setTimeout(refreshScreen, autoRefreshRate*1000);

}

/**
  * bodyOnLoad
  * 			
  * This method is called when the screen is onloaded to set the status for elements.  
  */
function bodyOnLoad(){
	//Set the entityId values in dropdown box
	var dropBox1 = new SwSelectBox(document.forms[0].elements["sweeppriorcutoff.id.entityId"],document.getElementById("entityDesc"));
	var currencyFormat = '<s:property value="#request.session.CDM.currencyFormat"/>';
	var dateFormat = '<s:property value="#request.session.CDM.dateFormat" />';
	//Set the column filter,sorted value
	table1 = new  XLSheet("SweepPriorToCutOff","table_1",["String","String","String","String",currencyFormat,dateFormat,"String", "String"],"12222222",false);
	table1.onsort = table1.onfilter = updateColors; 
	highlightTableRows("SweepPriorToCutOff");
	//enable sweep,rate button 
	document.getElementById("sweepbutton").innerHTML = document.getElementById("sweepenablebutton").innerHTML;
	document.getElementById("ratebutton").innerHTML = document.getElementById("rateenablebutton").innerHTML;
	document.getElementById("sweepbutton").innerHTML = document.getElementById("sweepdisablebutton").innerHTML;	
	if(filterstatus !=""){
		if(document.getElementById("SweepPriorToCutOff").innerText != "")
		table1.setFilterStatus(filterstatus.split(","));
	}
	var rows = table1.dataTable.tBody.rows;
	if( rows.length > 0 ){
	if(sortDescending !="" && sortDescending !="null" && sortDescending == "true"){	
		table1.dataTable.defaultDescending = true;
		table1.dataTable.doManualSorting(sortStatus);
	}
	if(sortDescending !="" && sortDescending !="null" && sortDescending == "false"){	
		table1.dataTable.doManualSorting(sortStatus);	
	}
	}	
	highlightTableRows("SweepPriorToCutOff");
	var obj1 = document.forms[0].elements['sweeppriorcutoff.extendDisplayTime'];
	var obj2 = document.forms[0].elements['sweeppriorcutoff.extendDisplayTimeBy'];
	/*Start: Code Added For Mantis 1708 by Sudhakar on 07-02-2011:Sweep button should be disabled when account not enabled for manual sweep is selected */
	//set the menuAccessId in the form
	document.forms[0].menuAccessId.value = "${requestScope.menuAccessId}";
	//Set the EntityName tooltip
	document.getElementById("entityIdtooltip").title=document.getElementById("entityDesc").value;
	/*End: Code Added For Mantis 1708 by Sudhakar on 07-02-2011:Sweep button should be disabled when account not enabled for manual sweep is selected */
	
	if (obj1.checked == true){
		obj2.disabled = false;
	}
	else {
		obj2.disabled = true
	}
	for (i = 0; i < document.getElementsByTagName("select").length; i++) {
		obj = document.getElementsByTagName("select")[i];
		obj.onblur=resetFlag2;
		obj.onmousedown=mouse_down;
		obj.onclick =click_on_drop;
	}

	var rows = table1.dataTable.tBody.rows;	
	var l = rows.length;
	var selectedSweepReference = getStoredParam('sweepSelectedReference');
	for (var i=0; i<l; i++)
	{
				if(rows[i].cells[1].innerText.trim()== selectedSweepReference) {
					highLightTableRow(rows[i]);
					rows[i].className = 'selectrow';
                                        break;
				}
     }
	document.getElementById("lastRefTime").innerText = lastRefTime;

}

function submitFormOnEntityChange(methodName) {

document.getElementById("refreshbutton").innerHTML = document.getElementById("refreshdisablebutton").innerHTML;

	document.forms[0].method.value = methodName;
	document.forms[0].submit();
}

function submitForm(methodName,itemChanged,fromCache){
    document.forms[0].method.value=methodName;
	document.forms[0].submit();
}

if(autoRefreshRate != 0){
	var refreshScreenCheckObj = window.setTimeout(refreshScreen, autoRefreshRate*1000);
}

function openRefreshRateWindow(methodName){

	var param = 'screenOption.do?method='+methodName;
	param +='&parentRefreshMethod=displaySweepDetails';
	param +='&screenId='+<%=SwtConstants.SWEEP_PRIOR_CUT_OFF_ID%>;
	return param;
}

function submitFormOnRefresh(){

maintainSortFilterStatus();

document.getElementById("refreshbutton").innerHTML = document.getElementById("refreshdisablebutton").innerHTML;

	var priorValue=validateField(document.forms[0].elements['sweeppriorcutoff.extendDisplayTimeBy'],'sweeppriorcutoff.extendDisplayTimeBy','numberPat');
	
    if(priorValue)
	{

	isDoNotCloseMyChilds= true;
	if (pageLoaded) {
	pageLoaded = false;
	submitForm('displaySweepDetails','','N');
	
	}
	}
	else
	{
	   document.forms[0].elements['sweeppriorcutoff.extendDisplayTimeBy'].focus();
	}
}

function maintainSortFilterStatus(){
	
	var sortColumn = table1.dataTable.sortColumn ;
	document.forms[0].selectedSortStatus.value = sortColumn ;

	var sSortDescending =  table1.dataTable.descending;		
	document.forms[0].selectedSortDescending.value = sSortDescending ;
	

	

	var filterArr = new Array(columns);

	if(document.getElementById("SweepPriorToCutOff").innerText != ""){
		filterArr = table1.getFilterStatus();
	}

	for(var idy = 0 ; idy < filterArr.length-1; ++idy)	
	document.forms[0].selectedFilterStatus.value += filterArr[idy] + "," ;
	
}

function updateColors()
{
	var rows = table1.dataTable.tBody.rows;
	var l = rows.length;
	var count = 0;
	for (var i=0; i<l; i++)
	{
		if (table1.isRowVisible(i))
		{
			removeClassName(rows[i], count % 2 ? "odd" : "even");
			addClassName(rows[i], count % 2 ? "even" : "odd");
			count++;
		}
	}	
}
var globalWindowName;
var globalAttr;
var globalIsCascade;
function openSweepDetailWindow(windowName,attr,isCascade)
{
	globalWindowName= windowName;
	globalAttr= attr;
	globalIsCascade= isCascade;
	document.forms[0].selectedList.value = getSelectedList("forCheckSweep");
	
	var url = "";

	
	var oXMLHTTP = new XMLHttpRequest();
	

		var sURL=requestURL + appName+"/sweeppriorcutoff.do?method=checkSweep";
		sURL = sURL + "&entityCode="+document.forms[0].elements['sweeppriorcutoff.id.entityId'].value;
		sURL = sURL + "&selectedList="+document.forms[0].selectedList.value;
		oXMLHTTP.open( "POST", sURL, false );
		oXMLHTTP.send();

		var str=new String(oXMLHTTP.responseText);

		if(str != "false"){
		ShowErrMsgWindowWithBtn('', '<s:text name="ShowErrMsgWindowWithBtn.errorMessage2"/>' + ' '+str+'<br><s:text name="ShowErrMsgWindowWithBtn.errorMessage3"/>', YES_NO, yesFunc);
		
		}else{
				url = sweep();
				openWindow(url,windowName,attr,isCascade);
		}
	
	
}
function yesFunc() {
	var url = "";
	url = sweep();
	openWindow(url,globalWindowName,globalAttr,globalIsCascade);
	
}

/* Start:Code Modified For Mantis 1708 by Sudhakar on 07-02-2011:Sweep button should be disabled when account not enabled for manual sweep is selected */
/**
  * @param rowElement
  * @param isSelected
  *
  * Method called when a row in data grid selected, to enable sweep button
  */	
function onSelectTableRow(rowElement,isSelected){
	if(isSelected){
        		setStoredParam('sweepSelectedReference',rowElement.cells[1].innerText.trim());
		document.forms[0].selectedList.value = getSelectedList("forCheckSweep");
		//variable to hold menuAccessId
		var menuAccessId=document.forms[0].menuAccessId.value;
		//variable to hold manualSweepFlag
		var manualSweepFlag = rowElement.getElementsByTagName("input")[2];
		var oXMLHTTP = new XMLHttpRequest();
		//frame the requestURL
		var sURL1=requestURL + appName+"/sweeppriorcutoff.do?method=checkAccess";
		sURL1 = sURL1 + "&entityCode="+document.forms[0].elements['sweeppriorcutoff.id.entityId'].value;
		sURL1 = sURL1 + "&selectedList="+document.forms[0].selectedList.value;
		sURL1 = sURL1 + "&menuAccessId="+ menuAccessId;
		oXMLHTTP.open( "POST", sURL1, false );
		//send the request for sweepStatus
		oXMLHTTP.send();
		var isSweepButtonEnable=new String(oXMLHTTP.responseText);
		if(menuEntityCurrGrpAccess == <%=SwtConstants.MENU_ENTITY_CURRENCYGRP_ACCESS%> && isSweepButtonEnable == "true" && manualSweepFlag.value=="<%=SwtConstants.MANUAL_SWEEP_SELECTED%>" && menuAccessId==<%=SwtConstants.MENU_FULL_ACCESS_ID%>){
			document.getElementById("sweepbutton").innerHTML = document.getElementById("sweepenablebutton").innerHTML;
		}else{
			document.getElementById("sweepbutton").innerHTML = document.getElementById("sweepdisablebutton").innerHTML;
		}
		accountAccess()
    }else{
		document.getElementById("sweepbutton").innerHTML = document.getElementById("sweepdisablebutton").innerHTML;
				setStoredParam('sweepSelectedReference','');
}

}
/* End:Code Modified For Mantis 1708 by Sudhakar on 07-02-2011:Sweep button should be disabled when account not enabled for manual sweep is selected */
function accountAccess(){
	var table = document.getElementById("SweepPriorToCutOff");  
	var tbody = table.getElementsByTagName("tbody")[0];    
	var rows = tbody.getElementsByTagName("tr");
	var selectedList = "";
    var flag = true
	var entity = document.forms[0].elements['sweeppriorcutoff.id.entityId'].value
	for (i=0; i < rows.length; i++) 
	 {
		if( isRowSelected(rows[i])){
			var accountId = rows[i].cells[1].innerText;
			flag=accountAccessConfirm(accountId.trim(),entity)
			if(flag=="false")
			{
				document.getElementById("sweepbutton").innerHTML = document.getElementById("sweepdisablebutton").innerHTML;
			}
			
			}			
		}
	 }  		
	
function accountAccessConfirm(accountId,entity){
	var oXMLHTTP = new XMLHttpRequest();
	var sURL = requestURL + appName+"/accountAccess.do?method=acctAccessConfirm";
	sURL = sURL + "&accountId="+accountId;
	sURL = sURL + "&entityId="+entity;
	sURL = sURL + "&status=Sweeping";
	oXMLHTTP.open( "POST", sURL, false );
	oXMLHTTP.send();
	var str=oXMLHTTP.responseText;
	return str;
}	


function enableExtendTimeTextBox(element) {
	var obj = document.forms[0].elements['sweeppriorcutoff.extendDisplayTimeBy'];
	if (element.checked == true)
	{
		obj.disabled = false;
	}
	else {
		obj.disabled = true
	}
}

function getSelectedList(calledFrom){
            
	 var table = document.getElementById("SweepPriorToCutOff");  
	 var tbody = table.getElementsByTagName("tbody")[0];    
	 var rows = tbody.getElementsByTagName("tr");
	 var selectedList = "";
	 
	 for (i=0; i < rows.length; i++) 
	 {
		if( isRowSelected(rows[i])){
			var hiddenElements = rows[i].getElementsByTagName("input");
			var entityId1 = document.forms[0].elements['sweeppriorcutoff.id.entityId'].value;
			var mainAccountId = "";

			if(hiddenElements != null && hiddenElements.length > 0 ){
				mainAccountId = hiddenElements[0].value;
		
			}
						 
		 if(calledFrom=="forCheckSweep"){
			selectedList = selectedList + "'"+rows[i].cells[5].innerText+"',"+rows[i].cells[1].innerText+"',"+entityId1+"' ,"
			+rows[i].cells[0].innerText+ "," + mainAccountId + "|" ;
		}else{
			selectedList = selectedList + "'"+rows[i].cells[0].innerText+"',"+rows[i].cells[2].innerText+"',"+entityId1+"|";
		}

			
		}
	 }  	
	 return selectedList;
}

function sweep(){
		document.forms[0].selectedList.value = getSelectedList("forCheckSweep");
		var param='sweepdetail.do?method=manual&entityCode=';
		param +=document.forms[0].elements['sweeppriorcutoff.id.entityId'].value;
		param +='&selectedList='+document.forms[0].selectedList.value;
		param +='&calledFrom='+"sweeppriorcutoff";
		
		return  param;
}




</SCRIPT>
</head>

<body leftmargin="0" topmargin="0" marginheight="0"
	onLoad="setParentChildsFocus();bodyOnLoad(); ShowErrMsgWindow('${actionError}');setTitleSuffix(document.forms[0]);"
	onunload="call()">

<s:form action="sweeppriorcutoff.do">
	<bean:define id="CDM" name="CDM"
		type="org.swallow.util.CommonDataManager" scope="session" />
	<input name="method" type="hidden" value="list">
	<input name="selectedList" type="hidden" value="">
	<input name="selectedReferenceId" type="hidden" value="viewDetails">
	<input name="selectedFilterStatus" type="hidden" value="">
	<input name="selectedSortDescending" type="hidden" value="">
	<input name="selectedSortStatus" type="hidden" value="">
	<input name="selectedCurrencyCode" type="hidden" value="GPB">


	<input name="menuAccessId" type="hidden">

	<!-- Start:Code Modified For Mantis 1708 by Sudhakar on 07-02-2011:Sweep button should be disabled when account not enabled for manual sweep is selected -->
	<input name="extendedTime" type="hidden" value="">
	<input name="extendCutOffTimeBy" type="hidden" value="">
	<div id="SweepPriorCutOff"
		style="position: absolute; left: 20; top: 20; width: 1076px; height: 70px; visibility: visible; border: 2px outset;"
		color="#7E97AF">
	<div id="SweepPriorCutOff"
		style="position: absolute; left: 8px; top: 4px; width: 950px; height: 10px;">
	<table width="1045px" border="0" cellpadding="0" cellspacing="0"
		height="28px">
		<tr height="25px">
			<td width="132px"><b><s:text name="entity.id" /></b></td>
			<td width="27px">&nbsp;</td>
			<td width="140px"><s:select cssClass="htmlTextAlpha" tabindex="1" titleKey="tooltip.selectEntityid" id="sweeppriorcutoff.id.entityId" name="sweeppriorcutoff.id.entityId" onchange="submitFormOnEntityChange('displaySweepDetails');" cssStyle="width:140px" list="#request.entities" listKey="value" listValue="label" /></td>
			<td width="8px">&nbsp;</td>
			<td id="entityIdtooltip" width="295px"><span class="textAlpha"
				style="width: 291px; background: transparent; border: thin;"
				readonly id="entityDesc" size="20"></td>
			</td>
			<td width="80px"><b><s:text name="label.sweepPriorCuttoff.entityTime" /></b></td>

			<td width="55px"><input type="text" size="8" name="Clock"
				readonly="true"
				style="border: thin none; background-color: transparent; width: 105"
				value="<%=(String)request.getAttribute("hrs") %>:<%=(String)request.getAttribute("min") %>  (<%=(String)request.getAttribute("offSet") %>)">




			</td>
			<td width="18px">&nbsp;</td>
			<td width="180px"><b><s:text name="sweeppriorcutoff.leadTime" /></b></td>
			<td width="15px"><s:textfield name="sweeppriorcutoff.leadTime"
				cssClass="textlabel1" readonly="true" tabindex="1"
				style="width:25px;" /></td>
		</tr>
	</table>
	<table width="1045px" border="0" cellpadding="0" cellspacing="0"
		height="28px">
		<tr height="25px">
			<td width="97px"><b><s:text name="sweeppriorcutoff.accountClass" /></b></td>
			<td width="35px">&nbsp;</td>
			<td width="285px"><s:textfield name="sweeppriorcutoff.acctType" cssClass="htmlTextAlpha"
				tabindex="1" disabled="true" style="width:140px;"  /></td>
			<td width="77px">&nbsp;</td>
			<td width="20px" align="right"><s:checkbox tabindex="24" cssStyle="width:13px;" name="sweeppriorcutoff.extendDisplayTime" fieldValue="Y" value='%{#request.sweeppriorcutoff.extendDisplayTime == "Y"}' titleKey="tooltip.extendDisplayTo" onclick="javascript:enableExtendTimeTextBox(this);" cssClass="htmlTextAlpha"  />&nbsp;</td>
			<td width="116px"><b><s:text name="sweepPrior.extendDisplay" /></b></td>
			<td width="30px"><s:textfield name="sweeppriorcutoff.extendDisplayTimeBy"
				cssClass="htmlTextAlpha" tabindex="1"
				titleKey="tooltip.minutesAfterCutOff" style="width:35px;"
				maxlength="3"
				onchange="return validateField(this,'sweeppriorcutoff.extendDisplayTimeBy','numberPat');"  />
			</td>
			<td width="170px"><b><s:text name="sweepPrior.minutesAfterCoutOff" /></b></td>
		</tr>
	</table>
	</div>
	</div>


	<div id="SweepPriorCutOff"
		style="position: absolute; left: 20px; top: 110px; width: 1096px; height: 370px;">
	<div id="SweepPriorCutOff"
		style="position: absolute; z-index: 99; left: 0px; top: 0px; width: 1061px; height: 10px;">
	<table class="sort-table" id="table_1" bgcolor="#B0AFAF" width="1061px"
		border="0" cellspacing="1" cellpadding="0" height="25px">
		<thead>
			<tr>
				<td width="69px" align="center"
					title='<s:text name="mvmDisplay.tooltip.currencyCode"/>'><b><s:text name="sweeppriorcutoff.ccy" /></b></td>
				<td width="199px" align="center"
					title='<s:text name="tooltip.sortAccountId"/>'><b><s:text name="movement.accountId" /></b></td>
				<td width="159px" align="center"
					title='<s:text name="tooltip.accName"/>'><b><s:text name="movement.Name" /></b></td>
				<td width="92px" align="center"
					title='<s:text name="tooltip.sortCutOffTime"/>'><b><s:text name="sweeppriorcutoff.cutOff" /></b></td>
				<td width="130px" align="center"
					title='<s:text name="tooltip.sortTargetBalance"/>'><b><s:text name="movement.targetbalance" /></b></td>
				<td width="99px" align="center"
					title='<s:text name="tooltip.sortValueDate"/>'><b><s:text name="sweeppriorcutoff.valueDate" /></b></td>

				<td width="67px" align="center" title=''><b><s:text name="sweep.manual" /></b></td>
				<td width="103px" align="center" title=''><b><s:text name="sweep.swpFrm" /></b></td>


				<td width="130px" align="center"
					title='<s:text name="tooltip.sortPredictBalance"/>'><b><s:text name="movement.balances" /></b></td>

			</tr>
		</thead>
	</table>
	</div>
	<div id="ddscrolltable"
		style="position: absolute; left: 0px; top: 1px; width: 1077px; height: 390px; overflow: auto">
	<div id="SweepPriorCutOff"
		style="position: absolute; z-index: 99; left: 0px; top: 22px; width: 1076px; height: 10px;">
	<table class="sort-table" id="SweepPriorToCutOff" width="1061px"
		border="0" cellspacing="1" cellpadding="0" height="367px">
		<tbody>
			<%
				int count = 0;
			%>

			<s:iterator value='#request.sweepPriorCutOffList' var='sweepPriorCutOffList' >
				<%
					if (count % 2 == 0) {
				%><tr class="even">
					<%
						} else {
					%>
				
				<tr class="odd">
					<%
						}
							++count;
					%>
					<s:hidden name="sweepPriorCutOffList.mainAccountId" value="%{#sweepPriorCutOffList.mainAccountId}" disabled="true"/>

					<s:hidden name="sweepPriorCutOffList.mainAcccoutName" value="%{#sweepPriorCutOffList.mainAcccoutName}" disabled="true"/>

					<s:hidden name="sweepPriorCutOffList.manualSweepFlag" value="%{#sweepPriorCutOffList.manualSweepFlag}" disabled="true"/>
					<td width="69px"><s:property value='#sweepPriorCutOffList.currcode' />&nbsp;</td>
					<td width="199px" align="left"><s:property value='#sweepPriorCutOffList.id.accountId' />&nbsp;</td>
					<td width="159px"><s:property value='#sweepPriorCutOffList.acctname' />&nbsp;</td>
					<td width="92px" align="center"><s:property value='#sweepPriorCutOffList.cutOff' />&nbsp;</td>
					<td width="130px" align="right"
						<s:if test='true==#sweepPriorCutOffList.targetBalanceNegative' > 
							style="color:red"
							</s:if>>
					<s:property value='#sweepPriorCutOffList.targetBalanceAsString' />&nbsp;</td>
					<td width="99px"><s:property value='#sweepPriorCutOffList.valuedateAsString' />&nbsp;</td>
					<td width="67px" align="center"><s:property value='#sweepPriorCutOffList.manualSweepFlag' />&nbsp;</td>
					<td width="103px"><s:property value='#sweepPriorCutOffList.sweepFrmbal' />&nbsp;</td>


					<td width="130px" align="right"
						<s:if test='"Predicted"==#sweepPriorCutOffList.sweepFrmbal' >
							<s:if test='true==#sweepPriorCutOffList.predictedBalanceNegative' > 
							style="color:red"
							</s:if>>
					<s:property value='#sweepPriorCutOffList.predictBalanceAsString' />&nbsp; </s:if> <s:if test='"External"==#sweepPriorCutOffList.sweepFrmbal' >
						<s:if test='true==#sweepPriorCutOffList.externalBalanceNegative' > 
							style="color:red"
							</s:if>>	<s:property value='#sweepPriorCutOffList.externalBalanceAsString' />&nbsp;
							</s:if></td>
			</s:iterator>

		</tbody>
		<tfoot>
			<tr>
				<td colspan="9"></td>
			</tr>
		</tfoot>

	</table>
	</div>
	</div>
	</div>


	<div id="SweepPriorCutOff"
		style="position: absolute; left: 1006; top: 517; width: 76; height: 15px; visibility: visible;">
	<table width="60px" border="0" cellspacing="0" cellpadding="0"
		height="20">
		<tr>

			<td align="Right"><a tabindex="11" href=#
				onclick="javascript:openWindow(buildPrintURL('print','Sweeps Prior to Cut-Off'),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')"
				onMouseOut="MM_swapImgRestore()"
				onMouseOver="MM_swapImage('Help','','images/help_default.GIF ',1)"><img
				src="images/help_default.GIF " name="Help" border="0"
				title='<s:text name="tooltip.helpScreen"/>'></a></td>

			<td align="right" id="Print"><a tabindex="11"
				onclick="printPage();" onMouseOut="MM_swapImgRestore()"
				onMouseOver="MM_swapImage('Print','','images/Print_R.gif ',1)"><img
				src="images/Print.gif " name="Print" border="0"
				title='<s:text name="tooltip.printScreen"/>'></a></td>
		</tr>
	</table>
	</div>

	<div id="ddimagebuttons" color="#7E97AF"
		style="position: absolute; border: 2px outset; left: 20; top: 510; width: 1076px; height: 39px; visibility: visible;">
	<div id="SweepPriorCutOff"
		style="position: absolute; left: 6; top: 4; width: 473px; height: 15px; visibility: visible;">
	<table width="280" border="0" cellspacing="0" cellpadding="0"
		height="20">
		<tr>
			<td id="refreshbutton"><a width="70px" tabindex="8"
				title='<s:text name="tooltip.refreshWindow"/>'
				onMouseOut="collapsebutton(this)"
				onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)"
				onMouseUp="highlightbutton(this)"
				onClick="javascript:submitFormOnRefresh('displaySweepDetails');"><s:text name="button.Refresh" /></a></td>
			<td id="ratebutton"></td>
			<td id="sweepbutton"><a><s:text name="button.sweep" /></a></td>

			<td id="cancelbutton" width="70px"><a
				title='<s:text name="tooltip.close"/>' tabindex="10"
				onMouseOut="collapsebutton(this)"
				onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)"
				onMouseUp="highlightbutton(this)" onclick="confirmClose('C');"><s:text name="button.close" /></a></td>
		</tr>
	</table>
	</div>

	<table height="33">
		<tr>
			<td id="lastRefTimeLable" width="844px" align="right"><b><s:text name="label.lastRefTime" /></b></td>
			<td id="lastRefTime"><input class="textAlpha"
				style="background: transparent; border: 1;" tabindex="-1" readonly
				name="maxPageNo" value="" size="14"></td>
		</tr>
	</table>

	<div
		style="position: absolute; left: 6; top: 4; width: 80px; height: 15px; visibility: hidden;">
	<table width="70" border="0" cellspacing="0" cellpadding="0"
		height="20 " style="visibility: hidden">
		<tr>

			<td id="refreshdisablebutton"><a class="disabled"
				disabled="disabled"><s:text name="button.Refresh" /></a></td>

			<td width="70px" id="sweepenablebutton"><a tabindex="9"
				title='<s:text name="tooltip.Sweep"/>'
				onMouseOut="collapsebutton(this)"
				onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)"
				onMouseUp="highlightbutton(this)"
				onClick="javascript:openSweepDetailWindow('generateSweep','left=50,top=190,width=1021,height=630,toolbar=0, resizable=yes, status=yes, scrollbars=yes','true')";><s:text name="button.sweep" /></a></td>
			<td id="sweepdisablebutton" width="70px"><a class="disabled"
				disabled="disabled" title='<s:text name="tooltip.Sweep"/>'><s:text name="button.sweep" /></a></td>
			<td id="rateenablebutton"><a tabindex="9"
				title="<s:text name="tooltip.rateButton"/>"
				onMouseOut="collapsebutton(this)"
				onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)"
				onMouseUp="highlightbutton(this)"
				) onClick="javascript:openWindow(openRefreshRateWindow('getRate'),'autoRefreshWindow','left=50,top=190,width=360,height=150,toolbar=0, resizable=yes, status=yes,scrollbars=yes','true')"><s:text name="accountmonitorbutton.Rate" /></a></td>
			<td id="ratedisablebutton"><a class="disabled"
				disabled="disabled"><s:text name="accountmonitorbutton.Rate" /></a>
			</td>
		</tr>
	</table>
	</div>
	</div>
	<!-- End:Code Modified For Mantis 1708 by Sudhakar on 07-02-2011:Sweep button should be disabled when account not enabled for manual sweep is selected -->
	<blockquote>&nbsp;</blockquote>
	<p>&nbsp;</p>
</s:form>
</body>
</html>