<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@ include file="/taglib.jsp"%>
<%@ page import="org.swallow.util.SwtConstants"%>
<%@ page import="org.swallow.util.SwtUtil"%>
<%@ include file="/angularJSUtils.jsp"%>
<html>  
<head>
<title><s:text name="inputexceptions.title.window" /><%="yes".equals(request.getAttribute("fromPCM")) ? " (PCM)":""%></title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<link rel="icon" type="image/x-icon" href="favicon.ico">
<link rel="stylesheet" href="angularSources/styles.css?version=<%= SwtUtil.appVersion %>">
</head>
<body>
<script type="text/javascript" src="jsp/work/assets/swfobject.js"></script>
<script type="text/javascript">
	var refresh = false;
	var testDate= "<%=SwtUtil.getSystemDateString() %>";
	var screenRoute = "InputException";
	//code added for 1053_STL_2 issue by karthik on 20110921 
	//To hold window object for  input exceptions message details
	var winObject = null;
	var dbDate="<%=SwtUtil.getSysDateWithFmt(SwtUtil.getSystemDateFromDB())%>";
	
					
	var label = new Array ();
	label["text"] = new Array ();
	label["tip"] = new Array ();
	label["tip"]["datefromDDMMYY"] = "<s:text name="tooltip.selectFromDateDDMMYY"/>";			
	
	label["tip"]["datefromMMDDYY"] = "<s:text name="tooltip.selectFromDateMMDDYY"/>";

	label["tip"]["showdays"] = "<s:text name="tooltip.showdays"/>";
	label["text"]["showdays"] = "<s:text name="text.showdays"/>";
	label["text"]["day"] = "<s:text name="text.day"/>";
	label["text"]["days"] = "<s:text name="text.days"/>";
	
	label["tip"]["refresh_button"] = "<s:text name="tooltip.refreshWindow"/>";
	label["tip"]["rate_button"] = "<s:text name="tooltip.RateWindow"/>";
	label["tip"]["close_button"] = "<s:text name="tooltip.close"/>";
	
	/*Start : Added By Imed B on 19-02-2014*/
	label["text"]["label-inputExceptionMonitor"] = "<s:text name="inputException.title"/>";
	label["text"]["label-showXML"] = "<s:text name="screen.showXML"/>";
	label["text"]["alert-notANumber"] = "<s:text name="inputException.notANumber"/>";
	label["text"]["alert-error"] = "<s:text name="screen.error"/>";
	label["text"]["alert-rateSelected"] = "<s:text name="inputException.rateSelected"/>";
	label["text"]["alert-warning"] = "<s:text name="screen.warning"/>";
	label["text"]["label-startDate"] = "<s:text name="inputException.startDate"/>";
	label["text"]["label-endDate"] = "<s:text name="inputException.endDate"/>";
	label["text"]["label-inputDate"] = "<s:text name="inputException.inputDate"/>";
	label["text"]["label-enterFromDate"] = "<s:text name="inputException.enterFromDate"/>";
	label["text"]["label-refresh"] = "<s:text name="inputException.refresh"/>";
	label["text"]["label-rate"] = "<s:text name="inputException.rate"/>";
	label["text"]["label-close"] = "<s:text name="inputException.close"/>";
	label["text"]["label-buildInProgress"] = "<s:text name="screen.buildInProgress"/>";
	label["text"]["label-lastRefresh"] = "<s:text name="screen.lastRefresh"/>";
	label["text"]["label-from"]="<s:text name="label.from"/>";
	label["text"]["label-dataBuildInProgress"]="<s:text name="screen.buildInProgress"/>";
	label["text"]["label-connectionError"]="<s:text name="screen.connectionError"/>";
	label["text"]["label-showValue"]="<s:text name="inputException.showValue"/>";
	/*End : Added By Imed B on 19-02-2014*/	
	
	var dateFormat = '<s:property value="#request.session.CDM.dateFormatValue" />';
	
	window.onload = function () {
		setTitleSuffix(document.forms[0]);
		setParentChildsFocus();
<%-- 		var so = new SWFObject("jsp/work/inputexceptions.swf?version=<%= SwtUtil.appVersion %>", "inputExceptions", "100%", "100%", "9", "#D6E3FE"); --%>
// 		so.write("inputExceptionscontent"); 
	}
	var userId = '${requestScope.userId}';
	var itemId = '${requestScope.itemId}';
	var fromPCM = '${requestScope.fromPCM}';
	
	 // Start:added by Mefteh for Mantis 2016 
	<% List<Integer> listPriorAheadDays=SwtUtil.getPriorAndAheadDaysToToday();%>
    var nDaysPriorToToday =<%=listPriorAheadDays.get(0)%>;
	var nDaysAheadToToday =<%=listPriorAheadDays.get(1)%>;
     // End: added by Mefteh for Mantis 2016 
     
	//flash uses this to determine what to request to save a new refresh rate
	function getUpdateRefreshRequest (rate) {
 		return "screenOption.do?method=save&screenOption.id.hostId=" + "<%=SwtUtil.getCurrentHostId()%>" + "&screenOption.id.userId=" + userId + "&screenOption.id.screenId=" + "<%=SwtConstants.INPUTEXCEPTION_ID%>" + "&screenOption.propertyValue=" + rate;
	}
	
	function openJavaWindow(a, left,top,width,height) {
		openWindow(a,'rolemaintenancechangeWindow','left='+left+',top='+top+',width='+width+',height='+height+',toolbar=0, resizable=yes, scrollbars=yes,status=yes');
	}
	
	/* Start:code added for 1053_STL_2 issue by karthik on 20110921 */
	/**
 	  * clickInterface
      * 			
      * This method is used to open the input exceptions message details monitor
      */
	function clickInterface(urlString) {
		//if window is already opened then close that window 
		if(winObject != null) {
			//call the window close 
			winObject.close();
		}
		//open the input exceptions message details monitor 
		var winTop = window.screenTop ? window.screenTop : window.screenY;
		var winLeft = window.screenLeft ? window.screenLeft : window.screenX;
		winObject = window.open(urlString,'exceptions','left='+winLeft+',top='+winTop+',width=1100,height=680,toolbar=0, resizable=yes, status=yes,scrollbars=yes','true');	
	}
	function help(){
		openWindow(buildPrintURL('print','Input Exception'),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')
    }
    
	/* End:code added for 1053_STL_2 issue by karthik on 20110921 */
</script>
<%@ include file="/angularscripts.jsp"%>


<!-- <form action="inputexceptions.do"  style="display:none;"> -->
<!-- 	<input name="method" type="hidden" value=""> -->
<!-- </form> -->
<form id="exportDataForm" target="tmp" method="post"><input
	type="hidden" name="data" id="exportData" /> <input type="hidden"
	name="screen" id="exportDataScreen"
	value="<s:text name="inputexceptions.title.window" />" /></form>
<iframe name="tmp" width="0%" height="0%" src="#" /> 
</body>
</html>