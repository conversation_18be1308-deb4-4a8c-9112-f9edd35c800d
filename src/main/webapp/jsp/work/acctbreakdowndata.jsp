<?xml version="1.0" encoding="UTF-8"?>
<%--
 	acctbreakdowndata.jsp
 	
 	<AUTHOR> R / 14-Mar-2012 Modified By <PERSON><PERSON><PERSON> for Mantis 2142 
 	
 	This file generates data for controls in XML format. This will be invoked by 
 	AJAX request on change of control value.
--%>

<%@ page contentType="text/xml" %>
<%@ taglib prefix="s" uri="/struts-tags" %>

<acctbreakdown>

	<selects>		
		<%-- START: CURENCY DROPDOWN --%> 
		<s:if test='#request.currencies != null'>
		<dropdown id="ddCurrencyCode">
				<select  name="acctBreakdownMonitor.currencyCode" tabindex="3" 
					onchange="onDataChange('currencyChanged')" style="width:55px" 
					class="htmlTextAlpha" title='<s:text name="tooltip.selectCurrencyCode"/>'>
					<s:iterator value="#request.currencies" var="currency">
						   <option value="<s:property value="#currency.value" />" 
						   	<s:if test='#request.acctBreakdownMonitor.currencyCode == #currency.value'>selected='selected' </s:if>>
						    <s:property value="#currency.label" /></option>
		            </s:iterator>
				</select>
		</dropdown>
		</s:if>
		<%-- END: CURENCY DROPDOWN --%> 
		<%-- START: ACCOUNT CLASS DROPDOWN --%> 
		<s:if test='#request.accountClasses != null'>
			<dropdown id="ddAcctClass">
				<select name="acctBreakdownMonitor.acctClass" tabindex="5" 
					onchange="onDataChange('acctClassChanged')" style="width:92px" 
					class="htmlTextAlpha" title='<s:text name="tooltip.selectAccountClass"/>'>
					<s:iterator value="#request.accountClasses" var="acctClass">
						   <option value="<s:property value="#acctClass.value" />"  
							<s:if test='#request.acctBreakdownMonitor.acctClass == #acctClass.value'>selected='selected' </s:if>>	
							<s:property value="#acctClass.label" /></option>					   
		             </s:iterator>
				</select>
			</dropdown>
		</s:if>
		<%-- END: ACCOUNT CLASS DROPDOWN --%> 
		<%-- START: ACCOUNT DROPDOWN --%> 
		<s:if test='#request.accountIds != null'>
		<dropdown id="ddAcctId">
				<select name="acctBreakdownMonitor.acctId" tabindex="7" 
					onchange="javascript: refresh();" style="width:240px" 
					class="htmlTextAlpha" title="'<s:text name="tooltip.selectAccountId"/>'">
					<s:iterator value="#request.accountIds" var="accountId">
					 <option value="<s:property value="#accountId.value" />"  
					 	<s:if test='#request.acctBreakdownMonitor.acctId == #accountId.value'>selected='selected' </s:if>>
					   <s:property value="#accountId.label" /></option>
				      </s:iterator>	
				</select>
				</dropdown>
		</s:if>
		<%-- END: ACCOUNT DROPDOWN --%> 
	</selects>	
</acctbreakdown>
