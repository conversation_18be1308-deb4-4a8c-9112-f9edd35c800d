<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@ include file="/taglib.jsp"  %>
<%@ page import="org.swallow.util.SwtConstants" %>
<%@ page import="org.swallow.util.CommonDataManager" %>
<%@ page import="org.swallow.util.SwtUtil"%>
<html>
<head>
<title><s:text name = "sweepDisplay.title.window"/> </title>

<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">

 
<SCRIPT language="JAVASCRIPT">
var dateFormat = '<s:property value="#request.session.CDM.dateFormat" />';
var requestURL = new String('<%=request.getRequestURL()%>');
var appName = "<%=SwtUtil.appName%>";  
var idy = requestURL.indexOf('/'+appName+'/');
requestURL=requestURL.substring(0,idy+1) ;
 var oXMLHTTP = new XMLHttpRequest();

var enteredSweepId=null; 
var currencyAccessInd="${requestScope.currGrpAccess}";
var movementAmendedFlag=false;
var archiveId =  '<%=SwtUtil.isEmptyOrNull(request.getParameter("archiveId"))?"":request.getParameter("archiveId")%>';
function bodyOnLoad()
{
	//Checks the archive Id value, if it is have any value then disable the msg button
	if(archiveId!="" && archiveId!="null"){
		<s:if test='"yes"!=#request.fromArchive' >
			document.getElementById("msgbutton").innerHTML = document.getElementById("msgdisablebutton").innerHTML;
		</s:if>
	}
			
		<s:if test='"true"==#request.ifNosweepDetailsPresent' >
			
				document.forms[0].elements['sweepsearch.id.sweepId'].value = "";
			
				var entityId = "${requestScope.entityId}";
			
				if(entityId == null || entityId == "" ){
					 document.getElementById("detailsbutton").innerHTML = document.getElementById("detailsdisablebutton").innerHTML;
					 document.getElementById("notesbutton").innerHTML = document.getElementById("notesdisablebutton").innerHTML;
					 document.getElementById("msgbutton").innerHTML = document.getElementById("msgdisablebutton").innerHTML;
					 					 					 				 
					 for(i = 0;i < document.forms[0].elements.length;i++)
					 {
					 		document.forms[0].elements[i].value = "";					 	
					 }
	 			}
	 			alert('<s:text name="sweepId.doesnotmatch"/>');	
		</s:if>
			
		<s:if test='"true"==#request.sweepDoesNotHaveAccess' >
			
				document.forms[0].elements['sweepsearch.id.sweepId'].value = "";
			
				var entityId = "${requestScope.entityId}";
			
				if(entityId == null || entityId == "" ){
					 document.getElementById("detailsbutton").innerHTML = document.getElementById("detailsdisablebutton").innerHTML;
					 document.getElementById("notesbutton").innerHTML = document.getElementById("notesdisablebutton").innerHTML;
					 document.getElementById("msgbutton").innerHTML = document.getElementById("msgdisablebutton").innerHTML;
					 					 					 				 
					 for(i = 0;i < document.forms[0].elements.length;i++)
					 {
					 		document.forms[0].elements[i].value = "";					 	
					 }
	 			}
	 				
		</s:if>	
					

	<s:if test='"view"==#request.methodName' >	
		var entityId = "${requestScope.entityId}";
		document.forms[0].selectedentityId.value =entityId; 
		 if(currencyAccessInd == 2)
	{
	document.getElementById("detailsbutton").innerHTML = document.getElementById("detailsdisablebutton").innerHTML;
	document.getElementById("notesbutton").innerHTML = document.getElementById("notesdisablebutton").innerHTML;
	document.getElementById("msgbutton").innerHTML = document.getElementById("msgdisablebutton").innerHTML;
	
	}
	</s:if>

	<s:if test='"view"!=#request.methodName' >
	//Set the focus to the sweep id of text field in loading of screen
	 this.window.focus();
     document.forms[0].elements["sweepsearch.id.sweepId"].focus();	

	 var entityId = "${requestScope.entityId}";
	 
	 if(entityId != null){
		document.forms[0].selectedentityId.value =entityId; 
		
	 } 
	 if(entityId == null || entityId == "" ){
		document.getElementById("detailsbutton").innerHTML = document.getElementById("detailsdisablebutton").innerHTML;
		 document.getElementById("notesbutton").innerHTML = document.getElementById("notesdisablebutton").innerHTML;
		 document.getElementById("msgbutton").innerHTML = document.getElementById("msgdisablebutton").innerHTML;
	 }else{

		document.getElementById("detailsbutton").innerHTML = document.getElementById("detailsenablebutton").innerHTML;
		 document.getElementById("notesbutton").innerHTML = document.getElementById("notesenablebutton").innerHTML;
		  document.getElementById("msgbutton").innerHTML = document.getElementById("msgenablebutton").innerHTML;
		
	 }
	 if(currencyAccessInd == 2)
	{
	document.getElementById("detailsbutton").innerHTML = document.getElementById("detailsdisablebutton").innerHTML;
	document.getElementById("notesbutton").innerHTML = document.getElementById("notesdisablebutton").innerHTML;
	document.getElementById("msgbutton").innerHTML = document.getElementById("msgdisablebutton").innerHTML;
	
	}
	</s:if>
	
	enteredSweepId=document.forms[0].elements['sweepsearch.id.sweepId'].value;	
	
}
/**
  * This function is used to validate the Sweep id if entered sweep id is not 
  * in the database then display the alet msg to user  
  * 
  * @param methodName
  *           
  */

  function submitForm(methodName,e){
   var event = (window.event|| e);
  //Getting sweep id from form elements
  var element = document.forms[0].elements["sweepsearch.id.sweepId"];
	if((movementAmendedFlag == true)||(element.value.length > 0 && event.keyCode && (event.keyCode == 9 || event.keyCode == 13)))
	{
		var thePat = PatternsDict['numberPat'];
		var gotIt = thePat.exec(element.value);
		if( gotIt != null) {
			document.forms[0].method.value = methodName;
			document.forms[0].selectedSweepId.value = element.value;
			//Enable the details,notes, msgs button based on sweep id
			if(document.forms[0].selectedSweepId.value != null){

			  document.getElementById("detailsbutton").innerHTML = document.getElementById("detailsenablebutton").innerHTML;
			  document.getElementById("notesbutton").innerHTML = document.getElementById("notesenablebutton").innerHTML;
			  document.getElementById("msgbutton").innerHTML = document.getElementById("msgenablebutton").innerHTML;
			 }
			document.forms[0].submit();
		} else {
			if (window.event)
				window.event.returnValue = false;
			alert("Please enter a valid "+PatternsMetaData['numberPat']);
			/* Start:Code modified by sudhakar For Mantis 1561_1054_STL_003 */
			document.forms[0].elements["sweepsearch.id.sweepId"].value="";
		   //calling the emptySweepDetails method to reload the Sweep display screen 
			emptySweepDetails('populate');
			/* End:Code modified by sudhakar For Mantis 1561_1054_STL_003 */
			element.focus();
			return false;
		}	
		
	}
}

function sweep(){
		var param='sweepdetail.do?method=displayQueue&entid=';
		param +=document.forms[0].selectedentityId.value;
		param +='&swpid='+document.forms[0].elements["sweepsearch.id.sweepId"].value;
		param += '&currencyAccess=' + currencyAccessInd;
		param +='&qname= C';
		param += '&archiveId='+ document.forms[0].archiveId.value;
		return  param;
}

function showSweepNotes(methodName){
var param = 'notes.do?method='+methodName+'&selectedSweepId=';			 
param += document.forms[0].elements["sweepsearch.id.sweepId"].value;
param += '&entityCode=' + document.forms[0].selectedentityId.value;
param += '&currencyAccess=' + currencyAccessInd;
param += '&archiveId='+ document.forms[0].archiveId.value;
return  param;
	
}
/**
  * This method is used to validate the sweep Id is valid or not
  *
  *@Param buttonValue
*/

function checkingMovementId(buttonValue) {
	/*Condition to validate the sweep id is integer or not*/
	if (validateField(document.forms[0].elements["sweepsearch.id.sweepId"],'sweepId','numberPat')){
		/*Condition to validate the sweep id is empty*/
		if (document.forms[0].elements["sweepsearch.id.sweepId"].value.trim() == ""){
			/*Throws the alert message*/	
			alert('<s:text name="sweepId.doesnotmatch"/>');
			/* Start:Code modified by sudhakar For Mantis 1561_1054_STL_003 */
			 //calling the emptySweepDetails method to reload the Sweep display screen 
			emptySweepDetails('populate');
			return false;
		}
		else{
			var oXMLHTTP = new XMLHttpRequest();	
			var sURL=requestURL + appName+"/sweepsearch.do?method=checkSweepId";
			
			sURL = sURL + "&sweepId="+document.forms[0].elements["sweepsearch.id.sweepId"].value;
			if(archiveId)
				sURL = sURL + "&archiveId="+archiveId;
			oXMLHTTP.open( "POST", sURL, false );
			oXMLHTTP.send();
			var sweepStatus=new String(oXMLHTTP.responseText);
			/*Conditon to validate the sweep id is valid or not*/
			if (sweepStatus == "false"){
				alert('<s:text name="sweepId.doesnotmatch"/>');
				 //calling the emptySweepDetails method to reload the Sweep display screen 
				emptySweepDetails('populate');
				return false;
			}
			
		else if(enteredSweepId != null && enteredSweepId != document.forms[0].elements["sweepsearch.id.sweepId"].value) {
			alert('<s:text name="sweepId.alert.sweepIdamended"/>');
			movementAmendedFlag = true;
			submitForm('populate',e);
			movementAmendedFlag = false;
			return false;
				
		}
			else{
				/*Condition to check the clicked button is ''Search'*/
				if(buttonValue =='searchbutton')
					/*Open Sweep Search screen*/
					  	javascript:openWindow(sweepSearch(),'sweepsearchWindow','left=50,top=190,width=911,height=488,toolbar=0, resizable=yes, scrollbars=yes','true');
				     /*Condition to check the clicked button is ''Detils'*/
				else if(buttonValue == 'detailsbutton')
					/*Open Sweep Details screen*/
					javascript:openWindow(sweep(),'sweepdetailsWindow','left=50,top=190,width=1031,height=635,toolbar=0, resizable=yes, scrollbars=yes','true');
				/*Condition to check the clicked button is 'Notes'*/
				else if(buttonValue == 'notesbutton')
				/*Open Sweep Notes window*/
					javascript:openWindow(showSweepNotes('showSweepNotes'),'sweepNotesWindow','left=170,top=210,width=588,height=272,toolbar=0, resizable=yes, scrollbars=yes');
				/*Condition to check the clicked button is ''Msgs'*/
				else if(buttonValue == 'msgbutton')
				/*Open Sweep Message button*/
				javascript:openWindow(sweepMessage(),'sweepMessageWindow','left=50,top=190,width=560,height=445,toolbar=0, resizable=yes, scrollbars=yes','true');
				return true;
			}
		}
	}
	 //calling the emptySweepDetails method to reload the Sweep display screen 
	emptySweepDetails('populate');
	
}
/** 
 *
 * This method is used to submit empty form for reloading Sweep display screen when Sweep id is invalid
 * @param method
 */
function emptySweepDetails(method){
		//Empty all form elements
		 for(i = 0;i < document.forms[0].elements.length;i++)
		{
		  document.forms[0].elements[i].value = "";					 	
		  }
		 document.forms[0].method.value = method;
		 //Submitting Form to empty the sweep details when sweepId is invalid
		 document.forms[0].submit();
	}
	/* End:Code modified by sudhakar For Mantis 1561_1054_STL_003 */
function sweepSearch(){

	var param='sweepsearch.do?method=display';
	return  param;
}

function sweepMessage(){

 var param='sweepdetail.do?method=sweepMessageDisplay';
 param += '&sweepId='+document.forms[0].elements['sweepsearch.id.sweepId'].value;
 return  param;
}

</SCRIPT>

</head>
<body leftmargin="0" topmargin="0" marginheight="0" onLoad="bodyOnLoad();setParentChildsFocus();setFocus(document.forms[0]);ShowErrMsgWindow('${actionError}');" onunload="call()">

<s:form action="sweepsearch.do">

<input name="method" type="hidden" value="display">
<input name="selectedSweepId" type="hidden" value="">
<input name="selectedentityId" type="hidden" value="">
<input name="archiveId" type="hidden" value="${archiveId}">

<bean:define id="CDM" name="CDM" type="org.swallow.util.CommonDataManager" scope="session"/>

<div id="SweepSearch" style="position:absolute; left:20px; top:20px; width:740px; height:650px; border:2px outset;"  color="#7E97AF">
<div id="SweepSearch" style="position:absolute; left:8px; top:4px; width:735px; height:100px;">


<div id="SweepSearch" style="position:absolute; left:0px; top:0px; width:720px; height:85px;">
   <div style="left:8px; top:4px;height: 345px;">

    <fieldset style="border:2px groove;"> <legend><s:text name="sweepsearch.sweepdetails"/></legend>

      <table width="700px" border="0" cellpadding="0"  cellspacing="0" class="content">
       <tr height="25">
    
		  <td width="132px">&nbsp;<b><s:text name="sweepsearch.sweepId"/></b></td>
		   <td width="160px" align="right">
		  <s:if test='"view"==#request.methodName' >		
			<s:textfield titleKey="tooltip.EnterSweepID" name="sweepsearch.id.sweepId"  style="width:120px;" tabindex="1" cssClass="htmlTextNumeric" readonly="true" /> 
		  </s:if>
		 
		 <s:if test='"view"!=#request.methodName' >
			 <s:textfield titleKey="tooltip.EnterSweepID" name="sweepsearch.id.sweepId"  style="width:120px;" tabindex="1" cssClass="htmlTextNumeric" maxlength="12" onkeydown="javascript:submitForm('populate',event)" /> 
		 </s:if>
           </td>
		 
		   <td width="100px" align="right">
	    <s:if test='"view"==#request.methodName' >		
			<s:textfield cssClass="textlabel" name="sweepsearch.sweepStatus"  style="width:80px;" readonly="true" /> 
		  </s:if>
		 <s:if test='"view"!=#request.methodName' >
			 <s:textfield cssClass="textlabel"  name="sweepsearch.sweepStatus" style="width:80px;" /> 
		 </s:if>
	   </td>
	   <td width="132px"></td>
	   <td width="160px"></td>
	   </tr>
	    <tr height="25">
	<td width="132px">&nbsp;<b><s:text name="sweep.valueDate"/></b></td>
           <td width="160px" align="right">
			<s:textfield cssClass="textlabel1" name="sweepsearch.valueDateAsString" style="width:80px;" readonly="true" /> 
		  </td>
		  <td width="100px" ></td>
		<td width="132px"></td>
	   <td width="160px"></td>
	</tr>
	    <tr height="25"> 
		  <td width="132px">&nbsp;<b><s:text name="sweepsearch.sweepamt"/></td>
            <td width="160px" align="right">
			  <s:textfield cssClass="textlabel1" name="sweepsearch.amt" style="width:120px;" readonly="true" /> 
          </td>
		   <td width="100px" align="right">
			<s:textfield cssClass="textlabel" name="sweepsearch.currencyCode" style="width:80px;" readonly="true" /> 
          </td><td width="132px"></td>
	   <td width="160px"></td>
		  </tr>
		
    
		   <tr height="25">
    	  <td width="132px">&nbsp;<b><s:text name="sweepsearch.generatedby"/></b></td>
           <td width="160px" align="right">
			<s:textfield cssClass="textlabel1" name="sweepsearch.inputUser"  style="width:144px;" readonly="true" /> 
		  </td> 
	 <td width="100px"></td>
	  <td width="132px"></td>
	   <td width="160px"></td>
        </tr>
		    <tr height="25">
		 <td width="132px">&nbsp;<b><s:text name="sweepsearch.time"/></b></td>
		<td width="160px" align="right">
			<s:textfield cssClass="textlabel1" name="sweepsearch.inputtime"  style="width:160px;" readonly="true" /> 
	   </td> <td width="100px"></td>
	  <td width="132px"></td>
	   <td width="160px"></td>
		   </tr>
		     <tr height="25" >
      <td width="132px">&nbsp;<b><s:text name="sweep.postcutoff1"/></b></td>
		<td width="160px" align="right">
			<s:checkbox name="sweepsearch.generatedpostcutoffflg" fieldValue="Y" value='%{#request.sweepsearch.generatedpostcutoffflg == "Y"}' cssStyle="width:13;" disabled="true"  /> 
          </td> <td width="100px"></td>
	  <td width="132px"></td>
	   <td width="160px"></td>
	    
		  </tr> 
		   <tr height="25" >
      <td width="132px">&nbsp;<b><s:text name="sweepDetail.additionalRefCut"/></b></td>
		<td width="160px" align="right">
			<s:textfield cssClass="textlabel1" name="sweepsearch.additionalReference"  style="width:144px;" readonly="true" /> 
          </td> <td width="100px"></td>
	  <td width="132px"></td>
	   <td width="160px"></td>
	    
		  </tr>
		  </table>
		  
		   <table width="700px" border="0" cellpadding="0"  cellspacing="0" class="content">
		  <tr height="25">	 
		  <td width="105px" >&nbsp;<b><s:text name="sweep.sweepuetr1"/></b></td>
           <td width="350px"  align="left">
			<s:textfield cssClass="textlabelalpha" titleKey="tooltip.sweep.uetr1" name="sweepsearch.sweepUetr1"  style="width:350px;" readonly="true" /> 
          </td>
		  </tr>
		  
		  <tr height="25">	          
          <td width="105px">&nbsp;<b><s:text name="sweep.sweepuetr2"/></b></td>
           <td width="350px" align="left">
			<s:textfield cssClass="textlabelalpha" titleKey="tooltip.sweep.uetr2" name="sweepsearch.sweepUetr2"  style="width:350px;" readonly="true" /> 
          </td>
		  </tr>
		  </table>
		  
		   <table width="700px" border="0" cellpadding="0"  cellspacing="0" class="content">
		   <tr height="25">	 
		  <td width="132px" >&nbsp;<b style="text-decoration: underline;"><s:text name="account.debitLeg"/></b></td>
            <td width="160px" align="right">
          </td>
		    <td width="100px">&nbsp;</td>
          <td width="132px" style="text-decoration: underline;" ><b><s:text name="account.creditLeg"/></b></td>
		   <td width="160px" align="right">&nbsp;&nbsp;&nbsp;
           </td>
		  </tr>
		     <tr height="25">
		  <td width="132px">&nbsp;<b><s:text name="sweepsearch.entity"/></b></td>
           <td width="160px" align="right">
			<s:textfield cssClass="textlabel1" name="sweepsearch.entityIdDr"  style="width:120px;" readonly="true" /> 
          </td>
		    <td width="100px">&nbsp;</td>
          <td width="132px"><b><s:text name="sweepsearch.entity"/></b></td>
		  <td width="160px" align="right">&nbsp;&nbsp;&nbsp;
			<s:textfield cssClass="textlabel1" name="sweepsearch.entityIdCr"  style="width:120px;" readonly="true" /> 
		  </td>
        </tr>
		  <tr height="25">	 
		  <td width="132px">&nbsp;<b ><s:text name="sweepsearch.debited"/></b></td>
          <td width="160px" align="right">
			<s:textfield cssClass="textlabel1"  name="sweepsearch.accountIdDr" style="width:120px;" readonly="true" /> 
          </td>
		    <td width="100px">&nbsp;</td>
          <td width="132px"><b><s:text name="sweepsearch.credited"/></b></td>
		   <td width="160px" align="right">&nbsp;&nbsp;&nbsp;
				<s:textfield cssClass="textlabel1" name="sweepsearch.accountIdCr" style="width:120px;" readonly="true" /> 
           </td>
        </tr>
			
  			<tr height="25">	 
		  <td width="132px">&nbsp;<b><s:text name="account.settleMethodSweep"/></b></td>
          <td width="160px" align="right">
			<s:textfield cssClass="textlabel1"  name="sweepsearch.settleMethodDR" style="width:120px;" readonly="true" /> 
          </td>
		    <td width="100px">&nbsp;</td>
          <td width="132px"><b><s:text name="account.settleMethodSweep"/></b></td>
		   <td width="160px" align="right">&nbsp;&nbsp;&nbsp;
				<s:textfield cssClass="textlabel1" name="sweepsearch.settleMethodCR" style="width:120px;" readonly="true" /> 
           </td>
        </tr>
        
		  <tr height = "1"><td colspan="5"/></tr>
      </table>
       </fieldset>
       </div>
<div style="left:8px; top:0px ;width:720px;height: 170px;">
    <fieldset style="border:2px groove;height: 200px;"> <legend><s:text name="sweepsearch.mvmntdetails"/></legend>
      <table width="700" border="0" cellpadding="0" cellspacing="0" class="content">
		   <tr height="25">
		  <td width="132px">&nbsp;<b><s:text name="sweepsearch.bookcode"/></b></td>
           <td width="160px" align="right">
<input class="textlabel1"  value="${bookcodedr}" name="bookCodeDr" style="width:144px;" readonly="true"/>
          </td>
		<td width="100px">&nbsp;</td>
          <td width="132px"><b><s:text name="sweepsearch.bookcode"/></b></td>
		  <td width="160px" align="right">
           <input class ="textlabel1" value="${bookcodecr}" name="bookCodeCr"  readonly="true"/>
          </td>
        </tr>
		  <tr height="25">	 
		  <td width="132px">&nbsp;<b><s:text name="sweepsearch.mvmntId"/></b></td>
          <td width="160px" align="right">
			<s:textfield cssClass="textlabel1" name="sweepsearch.movementIdDr" style="width:120px;" readonly="true" /> 
		  </td>
		   <td width="100px">&nbsp;</td>
          <td width="132px"><b><s:text name="sweepsearch.mvmntId"/></b></td>
		   <td width="160px" align="right">
			<s:textfield cssClass="textlabel1" name="sweepsearch.movementIdCr" style="width:120px;"  readonly="true" /> 
		  </td>
        </tr>
		  <tr height="25">	 
		  <td width="132px">&nbsp;<b><s:text name="sweepsearch.matchId"/></b></td>
          <td width="160px" align="right">
		<input class ="textlabel1" value="${matchiddr}" name="matchIdDr"  style="width:120px;" readonly="true"/>
          </td>
		    <td width="100px">&nbsp;</td>
                <td width="132px"><b><s:text name="sweepsearch.matchId"/></b></td>
		    <td width="160px" align="right">
			<input class ="textlabel1" value="${matchidcr}" name="matchIdDr" style="width:120px;" readonly="true"/>
		 </td>
        </tr>
		  <tr height="25">	 
		  <td width="132px">&nbsp;<b><s:text name="sweepsearch.postlevel"/></b></td>
          <td width="160px" align="right">
			<input  class ="textlabel1" value="${positionleveldr}" name="poslevelDr" style="width:140px;"  readonly="true"/>
          </td>
		    <td width="100px">&nbsp;</td>
          <td width="132px"><b><s:text name="sweepsearch.postlevel"/></b></td>
     	   <td width="160px" align="right">
		  <input class ="textlabel1" value="${positionlevelcr}" name="poslevelCr"  style="width:140px;" readonly="true"/>
		  </td>
		</tr>
		  <tr height="25"> 
		  <td width="132px">&nbsp;<b><s:text name="sweepsearch.predictstatus"/></b></td>
           <td width="160px" align="right">
 <input  class ="textlabel1" value="${predictstatusdr}" name="predictstatusdr"  style="width:144px;"  readonly="true"/>
          </td>
            <td width="100px">&nbsp;</td>
          <td width="132px"><b><s:text name="sweepsearch.predictstatus"/></b></td>
		   <td width="160px" align="right">
		   <input class ="textlabel1" value="${predictstatuscr}" name="predictstcr" style="width:144px;" readonly="true"/>
		  </td>
        </tr>
        <tr height="25"> 
		  <td width="132px">&nbsp;<b><s:text name="sweepsearch.externalstatus"/></b></td>
           <td width="160px" align="right">
 <input  class ="textlabel1" value="${externalstatusdr}" name="externalstatusdr"  style="width:144px;"  readonly="true"/>
          </td>
            <td width="100px">&nbsp;</td>
          <td width="132px"><b><s:text name="sweepsearch.externalstatus"/></b></td>
		   <td width="160px" align="right">
		   <input class ="textlabel1" value="${externalstatuscr}" name="externalstatuscr" style="width:144px;" readonly="true"/>
		  </td>
        </tr>
		<tr height = "1"><td colspan="5"/></tr>
      </table>
       </fieldset>
	   </div>
   	<div style="left:8px; top:4px;height: 120px;">
  	  <fieldset style="border:2px groove; "><legend><s:text name="sweepsearch.sweephistory"/></legend>

      <table width="700" border="0" cellpadding="0" cellspacing="0" class="content">
		   <tr height="25">
		    <td width="132px">&nbsp;<b><s:text name="sweepsearch.submittedby"/></b></td>
           <td width="160px" align="right">
			<s:textfield cssClass="textlabel1" name="sweepsearch.submitUser" style="width:144px;" readonly="true" /> 
		  </td>
		    <td width="100px">&nbsp;</td>
			  <td width="132px"><b><s:text name="sweepsearch.authorizedby"/></b></td>
            <td width="160px" align="right">
			<s:textfield cssClass="textlabel1" name="sweepsearch.authorizedUser" style="width:120px;" readonly="true" /> 
		</td>
		</tr>
		<tr height="25px">	
		  <td width="132px">&nbsp;<b><s:text name="sweepsearch.sweepamt"/></td>
		   <td width="160px" align="right">
			<s:textfield cssClass="textlabel1" name="sweepsearch.submitSweepAmtasstring"  style="width:120px;" readonly="true" /> 
		  </td>
		  <td width="100px">&nbsp;</td>
		    <td width="132px"><b><s:text name="sweepsearch.sweepamt"/></td>
			<td wdth="160px" align="right">
			<s:textfield cssClass="textlabel1" name="sweepsearch.authorizeSweepAmtasstring" style="width:120px;padding-right:5px" readonly="true" /> 
		  </td> 
		</tr>
		<tr height="25px">	
		 	 <td width="132px">&nbsp;<b><s:text name="sweepsearch.time"/></b></td>
		<td width="160px" align="right">
			<s:textfield cssClass="textlabel1" name="sweepsearch.submittedtime"  style="width:160px;" readonly="true" /> 
	   </td> 
		 <td width="100px">&nbsp;</td>
	 <td width="132px"><b><s:text name="sweepsearch.time"/></b></td>
		<td width="160px" align="right">
			<s:textfield cssClass="textlabel1"  name="sweepsearch.authorisetime" style="width:160px;"  readonly="true" /> 
		</td> 
		   </tr>
		   	<tr height="25px">	
			  <td width="132px">&nbsp;<b><s:text name="sweep.postcutoff1"/></b></td>
		<td width="160px" align="right" >
			<s:checkbox name="sweepsearch.submittedpostcutoffflg" fieldValue="Y" value='%{#request.sweepsearch.submittedpostcutoffflg == "Y"}' cssStyle="width:13;" disabled="true"  /> 
		  </td> 
		   <td width="100px">&nbsp;</td>
		    <td width="132px"><b><s:text name="sweep.postcutoff1"/></b></td>
			  <td width="160px" align="right">
				<s:checkbox name="sweepsearch.authorisedpostcutoffflg" fieldValue="Y" value='%{#request.sweepsearch.authorisedpostcutoffflg == "Y"}' cssStyle="width:13;" disabled="true"  /> 
          </td>
          
		  </tr><tr height = "1"><td colspan="5"/></tr>
      </table> 
      </fieldset>
	  </div>
</div>
</div>

<div id="MovementSearch" style="position:absolute ;left:650; top:660px; width:70px; height:39px; visibility:visible;">

	<table width="60" border="0" cellspacing="0" cellpadding="0" height="20">
		<tr>

		<td align="Right">
			<a tabindex="6" href=# onclick="javascript:openWindow(buildPrintURL('print','Sweep Display'),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')" onMouseOut="MM_swapImgRestore()" onMouseOver="MM_swapImage('Help','','images/help_default.GIF ',1)"><img src="images/help_default.GIF " name="Help"  border="0" ></a> 
		 </td>

			<td align="right" id="Print">
				<a tabindex="6" onclick="printPage();" onMouseOut="MM_swapImgRestore()" onMouseOver="MM_swapImage('Print','','images/Print_R.gif ',1)"><img src="images/Print.gif " name="Print"  border="0" title='<s:text name = "tooltip.printScreen"/>'></a>	
			</td>
		</tr>
	</table>
</div>

<div id="ddimagebuttons" color="#7E97AF" style="position:absolute; border:2px outset; left:-2; top:660; width:740px; height:39px; visibility:visible;">

<div id="manual" style="position:absolute; left:0; top:4; width:715px; height:15px; visibility:visible;">
	<s:if test='"view"!=#request.methodName' >
  	 <table width="350" border="0" cellspacing="0" cellpadding="0" height="20">
	</s:if>
	<s:if test='"view"==#request.methodName' >
  	 <table width="280" border="0" cellspacing="0" cellpadding="0" height="20">
	</s:if>
		<tr>
		 
			<td id="detailsbutton"  width="70px">	
			<s:if test='"view"==#request.methodName' >
			
			
			<a  tabindex="2" title='<s:text name = "tooltip.showSweepDetails"/>' onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onClick="checkingMovementId('detailsbutton')"; ><s:text name = "button.details"/></a>


			</s:if>
			</td>
			<s:if test='"view"!=#request.methodName' >
    				<td id="searchbutton" width="70px"><a title='<s:text name = "tooltip.executeSearch"/>' tabindex="3" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onClick="javascript:openWindow(sweepSearch(),'sweepsearchWindow','left=50,top=190,width=911,height=488,toolbar=0, resizable=yes, scrollbars=yes','true')"><s:text name = "button.search"/></a>
    			 
			 </td>
			</s:if>
			
			<td id="notesbutton" width="70px">
			<s:if test='"view"==#request.methodName' >
			<a  title='<s:text name = "tooltip.sweepNotes"/>' tabindex="4" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onClick="checkingMovementId('notesbutton')"; ><s:text name = "button.notes"/></a>
			</s:if>
			 </td>


		<td id="msgbutton">
					
					<a title='<s:text name = "tooltip.msgs"/>' tabindex="5"onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onclick="checkingMovementId('msgbutton')";><s:text name = "SweepDisplay.msg"/></a>			
					
				</td>

			

            <td width="70px">
			<a title='<s:text name = "tooltip.close"/>' tabindex="5"onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onclick="confirmClose('C');"><s:text name = "button.close"/></a>			
			</td>


		</tr>
		</table>
	</div>

<div style="position:absolute; left:6; top:4; width:705px; height:15px; visibility:hidden;">  	
		<table border="0" cellspacing="0" cellpadding="0" height="20" style="visibility:hidden">
			<tr>
				<td id="detailsenablebutton">		
					
					<a  title='<s:text name = "tooltip.showSweepDetails"/>' tabindex="2" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onClick="checkingMovementId('detailsbutton')";><s:text name = "button.details"/></a>
					
				</td>		
				<td id="detailsdisablebutton">
					<a class="disabled" disabled="disabled">Details</a>
				</td>
				<td id="notesenablebutton">		
					
					<a  title='<s:text name = "tooltip.sweepNotes"/>' tabindex="4" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onClick="checkingMovementId('notesbutton')";><s:text name = "button.notes"/></a>
					
				</td>		
				<td id="notesdisablebutton">
					<a class="disabled" disabled="disabled"><s:text name = "button.notes"/></a>
				</td>


			<td id="msgdisablebutton">
					<a class="disabled" disabled="disabled"><s:text name = "SweepDisplay.msg"/></a>
				</td>
				<td id="msgenablebutton" width="70px">
			
			<a title='<s:text name = "tooltip.msgs"/>' tabindex="5"onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onclick="checkingMovementId('msgbutton')";><s:text name = "SweepDisplay.msg"/></a>			
			
			</td>
			
				 
			</tr>
		</table>
	</div>
</div>

</s:form>
</body>
</html>