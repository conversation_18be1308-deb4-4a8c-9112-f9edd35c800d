<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@ include file="/taglib.jsp"%>
<%@ page import="org.swallow.util.SwtConstants"%>
<%@ page import="org.swallow.util.CommonDataManager"%>
<%@ page import="org.swallow.util.SwtUtil"%>
<html>
<head>
<title><s:text name="manSweeping.title.window" /></title>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
<script type="text/javascript" src="js/jquery.exportselect.js"></script>
<SCRIPT language="JAVASCRIPT">

var initialtab=[${requestScope.selectedTabIndex}, "${requestScope.selectedTabName}"];
var hideAccountsAfterCutOff= "${requestScope.hideAccountsAfterCutOff}";
hideAccountsAfterCutOff = hideAccountsAfterCutOff === "true" || hideAccountsAfterCutOff === true;
var dynamicTab = true;
var tabIndex=initialtab[0];
var selectedtab = initialtab[1];
var previoustab=""
var dbDate="<%=SwtUtil.getSysDateWithFmt(SwtUtil.getSystemDateFromDB())%>";
var globalWindowName = "";
var globalAttr = "";
var globalIsCascad = ""
var tabNames = new Array( 'sweepTodayParent','sweepTodayPlusOneParent','sweepTodayPlusTwoParent','sweepTodayPlusThreeParent',
					'sweepTodayPlusFourParent','sweepTodayPlusFiveParent','sweepTodayPlusSixParent','sweepTodayPlusSevenParent','sweepTodayPlusEightParent');
var lastRefTime = "${requestScope.lastRefTime}";

var appName = "<%=SwtUtil.appName%>";
var requestURL = new String('<%=request.getRequestURL()%>');
var idy = requestURL.indexOf('/'+appName+'/');
requestURL=requestURL.substring(0,idy+1) ;
/* Start : Code added for Mantis 2150 by M.Bouraoui on 01/04/2012  */

// set isSelectedNoWorkingDay flag from request parameters 
var isSelectedNoWorkingDay="${requestScope.tabFlag}".charAt(tabIndex-1);
/* End : Code added for Mantis 2150 by M.Bouraoui on 01/04/2012  */

/*Added to apply style on mandatory field ,if it's equal to undefined then no field will be colored*/
 mandatoryFieldsArray= "undefined" ;
 
 function record()
{
return document.getElementById ("sweepDetailsToday").getElementsByTagName('tr').length-1;
}

/**
	* openSweepDetailWindow
	*
	* This method is used to open the sweep details screen when click on sweep button. 
	**/
	
   
function openSweepDetailWindow(windowName,attr,isCascade)
{
	document.forms[0].selectedList.value = getSelectedList();
	var url = "";
	var oXMLHTTP = new XMLHttpRequest();
	 var table = document.getElementById("sweepDetailsToday");
	 var tbody = table.getElementsByTagName("tbody")[0];    
	 var rows = tbody.getElementsByTagName("tr");
	 var check = 0;
	 var displaylevel1= "";
	 var displaylevel2= "";
	 globalWindowName = windowName;
	 globalAttr = attr;
	 globalIsCascad = isCascade;
	 //Start:code modified by sandeepkumar for mantis 2039 -  Visually indicate accounts past cut-off or subject to non-working day
	 //set sweep detail flag as true 
	 var swpDetailFlag = true;
	 for (var i=0; i < rows.length; i++) 
	 {
	 // condition to check if isvaluedateachievable is "N" which is hidden in last column 
		if(( isRowSelected(rows[i]) ) && (rows[i].cells[9].innerText =="N")){
		swpDetailFlag = false;	
        ShowErrMsgWindowWithBtn('','<s:text name="manualSweeping.warning.messageForNonWorkingDays"/>' , OK_CANCEL, okSweep);

// 		if(swpDetailFlag == false)
// 		return;
	 } 	
	 } 
	 if(swpDetailFlag == true){
		 okSweep();
	 }
}
function okSweep() {
	document.forms[0].selectedList.value = getSelectedList();
	var url = "";
	var oXMLHTTP = new XMLHttpRequest();
	 var table = document.getElementById("sweepDetailsToday");
	 var tbody = table.getElementsByTagName("tbody")[0];    
	 var rows = tbody.getElementsByTagName("tr");
	 var check = 0;
	 var displaylevel1= "";
	 var displaylevel2= "";
	 //Start:code modified by sandeepkumar for mantis 2039 -  Visually indicate accounts past cut-off or subject to non-working day
	 //set sweep detail flag as true 
	
	for (i=0; i < rows.length; i++) 
	 {
			if( isRowSelected(rows[i])){
				var hiddenElements = rows[i].getElementsByTagName("input");
				if(hiddenElements != null && hiddenElements.length > 0 ){
					if(check == 0) {
						displaylevel1 = hiddenElements[3].value;
					}else {
						displaylevel2 = hiddenElements[3].value;
					}
						
				}
				check = 1;
				
			}
		
		}
		var yourstate = true;
		if(displaylevel1 == displaylevel2) {
			ShowErrMsgWindowWithBtn("", '<s:text name="label.bothAccountSelectedAre"/>' + displaylevel1 +''+ '<s:text name="label.accounts"/>' +' '+  '<s:text name="recovery.confrim.continue"/>', OK_CANCEL, okHandle)
			
		}
		else 
		{
		
			var sURL=requestURL + appName+"/sweep.do?method=checkSweep";
			sURL = sURL + "&entityCode="+document.forms[0].elements['movement.id.entityId'].value;
			sURL = sURL + "&selectedList="+document.forms[0].selectedList.value;
			
			oXMLHTTP.open( "POST", sURL, false );
			oXMLHTTP.send();

			var str=new String(oXMLHTTP.responseText);
			if(str != "false"){
			ShowErrMsgWindowWithBtn('', '<s:text name="ShowErrMsgWindowWithBtn.errorMessage2"/>'+str+'<br><s:text name="ShowErrMsgWindowWithBtn.errorMessage3"/>', YES_NO, yesHandle);

					}else{
					url = sweep();
					openWindow(url,globalWindowName,globalAttr,globalIsCascad);
					}
		}
		

}
function okHandle() {
	var oXMLHTTP = new XMLHttpRequest();
	var sURL=requestURL + appName+"/sweep.do?method=checkSweep";
	sURL = sURL + "&entityCode="+document.forms[0].elements['movement.id.entityId'].value;
	sURL = sURL + "&selectedList="+document.forms[0].selectedList.value;
	
	oXMLHTTP.open( "POST", sURL, false );
	oXMLHTTP.send();

	var str=new String(oXMLHTTP.responseText);
	if(str != "false"){
	ShowErrMsgWindowWithBtn('', '<s:text name="ShowErrMsgWindowWithBtn.errorMessage2"/>'+str+'<br><s:text name="ShowErrMsgWindowWithBtn.errorMessage3"/>', YES_NO, yesHandle);

	}else{
	url = sweep();
	openWindow(url,globalWindowName,globalAttr,globalIsCascad);
	}
	
}
function yesHandle() {
	var url = "";
	url = sweep();
	openWindow(url,globalWindowName,globalAttr,globalIsCascad);
}
// End:code modified by sandeepkumar for mantis 2039 -   Visually indicate accounts past cut-off or subject to non-working day
var countx=tabIndex;



function countIt(x){
	countx=x;
}
 
function getSelectedList(calledFrom){
          
	 var table = document.getElementById("sweepDetailsToday");  
	 var tbody = table.getElementsByTagName("tbody")[0];    
	 var rows = tbody.getElementsByTagName("tr");
	 var selectedList = "";
	 for (i=0; i < rows.length; i++) 
	 {
		if( isRowSelected(rows[i])){
			var hiddenElements = rows[i].getElementsByTagName("input");
			var entityId1 = "";
			var currency= "";
			var movementEntityId="";
			if(hiddenElements != null && hiddenElements.length > 0 ){
				
				entityId1 = hiddenElements[2].value;
				
				currency= hiddenElements[1].value;		
			}
						 
		 if(calledFrom=="forCheckSweep"){
			selectedList = selectedList + "'"+rows[i].cells[0].innerText+"',"+rows[i].cells[3].innerText+"',"+entityId1+"',"
			+currency+"|";
		}else{
			selectedList = selectedList + "'"+rows[i].cells[0].innerText+"',"+rows[i].cells[3].innerText+"',"+entityId1+"|";
		}
	
		}
	 } 
	 return selectedList;
}


function getSelectedListForDisplay(){
    
	 var table = document.getElementById("sweepDetailsToday");  
	 var tbody = table.getElementsByTagName("tbody")[0];    
	 var rows = tbody.getElementsByTagName("tr");
	 var selectedList = "";
	 var acc1 = "";
	 var acc2 = "";
	 for (i=0; i < rows.length; i++) 
	 {
		if( isRowSelected(rows[i])){
			var hiddenElements = rows[i].getElementsByTagName("input");
			var entityId1 = "";
			var movementEntityId="";
			if(hiddenElements != null && hiddenElements.length > 0 ){
				entityId1 = hiddenElements[2].value;
				
			}
		 if(acc1){
			 acc2 = rows[i].cells[3].innerText+"("+entityId1+")";
		 	
		 }
		 else {
			 acc1 = rows[i].cells[3].innerText+"("+entityId1+")";
		 }
	
		}
	 } 
	  document.getElementById("selectedAccount1").textContent =  acc1;
	  if(acc2)
		  acc2=",  "+acc2;
     document.getElementById("selectedAccount2").textContent =  acc2;
     
     if(!document.getElementById("selectedAccount1").textContent)
     	document.getElementById("clearbutton").innerHTML = document.getElementById("clearButtondisabled").innerHTML;
     else 
     	document.getElementById("clearbutton").innerHTML = document.getElementById("clearButtonEnabled").innerHTML;
	  
}
var table1;
	/**
		 * This function updated background color for grid row.
		 * The row color is based on isValueDateAchievable.
		 * - If isValueDateAchievable is N, then it should grey 
		 * - If other value, then it should be either odd or even 
		 */
function updateGridColor()
{
	var rows = table1.dataTable.tBody.rows;
	var l = rows.length;
	var count = 0;
	for (var i=0; i<l; i++)
	{
	if (table1.isRowVisible(i))
		{
		//Start:code modified by sandeepkumar for mantis 2039 -  Visually indicate accounts past cut-off or subject to non-working day
		//Read isValueDateAchievable value which is hidden in last column and paste the grey color in grid if value is 'N'
			var isValueDateAchievable = rows[i].cells[9].innerText.trim();
			if(isValueDateAchievable == "N") {
			addClassName(rows[i], "accn"); 
			}
			//End:code modified by sandeepkumar for mantis 2039 -  Visually indicate accounts past cut-off or subject to non-working day
			removeClassName(rows[i], count % 2 ? "odd" : "even");
			addClassName(rows[i], count % 2 ? "even" : "odd");
			count++;
		}
	}	
}


function setTabInfo()
{
	document.forms[0].elements["selectedTabIndex"].value = getSelectedTabIndex();
	document.forms[0].elements["selectedTabName"].value = getselectedtab();
}

function submitForm(methodName,tabIndex){

	 var checkbox = document.getElementById('cutoffCheckbox');
	 var shouldHideRow = checkbox.checked;
	 document.forms[0].hideAccountsAfterCutOff.value = shouldHideRow;
	    
	document.getElementById("refreshbutton").innerHTML = document.getElementById("refreshdisablebutton").innerHTML;
	setTabInfo();
	document.forms[0].method.value = methodName;
	document.getElementById('selectedTabIndex').value =tabIndex;
	
	
	document.forms[0].hideAccountsAfterCutOff.value = shouldHideRow;
	
	document.forms[0].submit();
}

function submitForm(methodName){
	var rows = table1.dataTable.tBody.rows;	
	var l = rows.length;
	var selectedList  = new Array();
	var j = 0;
	for (var i=0; i<l; i++)
	{
				if(isRowSelected(rows[i])) {
					selectedList[j]=rows[i].cells[3].innerText;
					j++;
				}
     }
	setStoredParam('ManualSweepSelectedList',selectedList);
document.getElementById("refreshbutton").innerHTML = document.getElementById("refreshdisablebutton").innerHTML;
	setTabInfo();
	var checkbox = document.getElementById('cutoffCheckbox');
	 var shouldHideRow = checkbox.checked;
	 document.forms[0].hideAccountsAfterCutOff.value = shouldHideRow;
	document.forms[0].method.value = methodName;
	document.forms[0].submit();
}
/**
  * @param methodName
  *
  * This method is used to submit the form while changing the current entity.
  */
function submitEntityForm(methodName){

document.getElementById("refreshbutton").innerHTML = document.getElementById("refreshdisablebutton").innerHTML;
document.forms[0].entityId.value != document.forms[0].elements["movement.id.entityId"].value;
{
  document.forms[0].elements["entityIdChanged"].value=true;
}
	document.forms[0].method.value = methodName;
	//Submit the elements form details
	var checkbox = document.getElementById('cutoffCheckbox');
	 var shouldHideRow = checkbox.checked;
	 document.forms[0].hideAccountsAfterCutOff.value = shouldHideRow;
	document.forms[0].submit();
}
 

function bodyOnLoad()
{
	var abc=  '${requestScope.SaveButton}';
	var dropBox1 = new SwSelectBox(document.forms[0].elements["movement.id.entityId"],document.getElementById("entityDesc"));
	var dropBox2 = new SwSelectBox(document.forms[0].elements["movement.currencyCode"],document.getElementById("currencyDesc"));
	var dropBox3 = new SwSelectBox(document.forms[0].elements["movement.accttype"],document.getElementById("accountDesc"));
	var dropBox1 = new SwSelectBox(document.forms[0].elements["movement.againstAccountEntityId"],document.getElementById("entity2Desc"));
	document.forms[0].elements["entityIdChanged"].value=false;
	var currencyFormat = '<s:property value="#request.session.CDM.currencyFormat"/>';
	var dateFormat = '<s:property value="#request.session.CDM.dateFormat" />';
	
	
	table1 = new  XLSheet("sweepDetailsToday","table_1",[dateFormat, "String","String","String","String",currencyFormat,currencyFormat,currencyFormat,"String","String"],"**********");
	//Start:code modified by sandeepkumar for mantis 2039 -  Visually indicate accounts past cut-off or subject to non-working day
	updateGridColor();
	//End:code modified by sandeepkumar for mantis 2039 -  Visually indicate accounts past cut-off or subject to non-working day
	document.forms[0].menuAccessId.value = "${requestScope.menuAccessId}";
	
	 
	highlightMultiTableRows("sweepDetailsToday");
	
	document.getElementById("sweepbutton").innerHTML = document.getElementById("sweepdisablebutton").innerHTML;
	
	document.getElementById("lastRefTime").innerText = lastRefTime;

	var listSelected = new Array();	
    listSelected= getStoredParam('ManualSweepSelectedList');
		var rows = table1.dataTable.tBody.rows;	
		var l = rows.length;
		if(listSelected){
		try {
			var listSelectedSize= listSelected.length;
		
		for (var i=0; i<l; i++)
		{		
			for (var j=0; j<listSelectedSize; j++)
				
				if(listSelected[j]==rows[i].cells[3].innerText) 
				{
					highLightTableRow(rows[i]);
					rowElement=rows[i];
				}
				
	     }
	
		if(listSelected.length == 2 )
		{
			// enable sweeping 
			onMultiSelectTableRow(rowElement,false);	
		}
		else
		{
			// disable sweeping
			document.getElementById("sweepbutton").innerHTML=document.getElementById("sweepdisablebutton").innerHTML;
		}
		
		}catch(Exception){
			   
			}
	}

		
	var record=	'${requestScope.listSize}';
	var headerData = [];
	var dataprovider = new Array();
	
	var newElement1 = {};
	newElement1[headerData[0]] = 'Pdf';
	dataprovider.push(newElement1);
			
	var newElement2 = {};
	newElement2[headerData[0]] = 'Excel';
	dataprovider.push(newElement2);
			
	var newElement3 = {};
	newElement3[headerData[0]] = 'Csv';
	dataprovider.push(newElement3);
	
    $("#exportReport").exportselect ({
    	dataprovider: dataprovider,
    	change: exportReport,
		selectedIndex:0
	  });
   
    if(record==0)
    	 $("#exportReport").disabled(true);
    else
    	 $("#exportReport").disabled(false);	

    
    getSelectedListForDisplay();
    refreshGridHideShowAccountsCutOff();
    if(!document.getElementById("selectedAccount1").textContent)
    	document.getElementById("clearbutton").innerHTML = document.getElementById("clearButtondisabled").innerHTML;
    else 
    	document.getElementById("clearbutton").innerHTML = document.getElementById("clearButtonEnabled").innerHTML;
	}

function exportReport(){
	var type=$("#exportReport").getSelected(0);
	return exportData(type.toLowerCase());
}
function onMultiSelectTableRow(rowElement,isSelected)
{	
	
	var hiddenElement = rowElement.getElementsByTagName("input")[0];
	var noOfRows=getCountRowsSelected(rowElement);
	
    if(noOfRows < 2 ){
		if(isSelected)
			unHighLightTableRow(rowElement);
		else
			highLightTableRow(rowElement);
	}

	if(noOfRows == 2 && isSelected){
		if(isSelected)
			unHighLightTableRow(rowElement);
		else
			highLightTableRow(rowElement);
	}

	noOfRows=getCountRowsSelected(rowElement);
	var isSweepButtonEnable = false;
   

	if(noOfRows == 2)
	{
		document.forms[0].selectedList.value = getSelectedList("forCheckSweep");
		var url = "";
		var oXMLHTTP = new XMLHttpRequest();
		
		var sURL1=requestURL + appName+"/sweep.do?method=checkAccess";
		sURL1 = sURL1 + "&entityCode="+document.forms[0].elements['movement.id.entityId'].value;
		sURL1 = sURL1 + "&selectedList="+document.forms[0].selectedList.value;
		
		sURL1 = sURL1 + "&menuAccessId="+ document.forms[0].menuAccessId.value;
		
		oXMLHTTP.open( "POST", sURL1, false );
		oXMLHTTP.send();
		var str1=new String(oXMLHTTP.responseText);
		
		if(str1 != "true")
		{
			
		}else{
			
			var selrowsColl = getRowsSelected(rowElement);
			if(checkValueDate(selrowsColl) )
			isSweepButtonEnable = true;				
			
		}
	}
	getSelectedListForDisplay();
	if(noOfRows > 0)
    	document.getElementById("clearbutton").innerHTML = document.getElementById("clearButtonEnabled").innerHTML;
    else 
    	document.getElementById("clearbutton").innerHTML = document.getElementById("clearButtondisabled").innerHTML;

	if(isSweepButtonEnable == true){
		document.getElementById("sweepbutton").innerHTML = document.getElementById("sweepenablebutton").innerHTML;

	}else{
		
		document.getElementById("sweepbutton").innerHTML = document.getElementById("sweepdisablebutton").innerHTML;
	}
		
	accountAccess();
		
}


function accountAccess(){
    
	var table = document.getElementById("sweepDetailsToday");  
	var tbody = table.getElementsByTagName("tbody")[0];    
	var rows = tbody.getElementsByTagName("tr");
	var selectedList = "";
    var flag = true
	var entity = document.forms[0].elements["movement.id.entityId"].value;
	
	for (i=0; i < rows.length; i++) 
	 {
		if( isRowSelected(rows[i])){
		    var hiddenElements = rows[i].getElementsByTagName("input");
			var accountId = rows[i].cells[3].innerText;
			var entityId1 = hiddenElements[2].value;
			flag=accountAccessConfirm(accountId.trim(),entityId1)
			if(flag=="false")
			{
				document.getElementById("sweepbutton").innerHTML = document.getElementById("sweepdisablebutton").innerHTML;
			}
			}			
		}
	 }  		
	
function accountAccessConfirm(accountId,entity){

	var oXMLHTTP = new XMLHttpRequest();
	var sURL = requestURL + appName+"/accountAccess.do?method=acctAccessConfirm";
	sURL = sURL + "&accountId="+accountId;
	sURL = sURL + "&entityId="+entity;
	sURL = sURL + "&status=Sweeping";
	oXMLHTTP.open( "POST", sURL, false );
	
	oXMLHTTP.send();
	var str=oXMLHTTP.responseText;
	
	return str;
}	
 

function checkValueDate(rowColl)
{
	var retValue = false;
	if(rowColl != null && rowColl.length != 2){
		
		return retValue;
	}
	if(rowColl[0].cells[0].innerText == rowColl[1].cells[0].innerText)
		retValue = true;

	return retValue;

}

function sweep(){
		document.forms[0].selectedList.value = getSelectedList();
		
		var param='sweepdetail.do?method=manual&entityCode=';
		param +=document.forms[0].elements['movement.id.entityId'].value;
		param +='&selectedList='+document.forms[0].selectedList.value;
		param +='&calledFrom='+"manualsweeping";
		return  param;
}

//This function is called when date tabs are pressed.
function checkTabIndex(obj, tabName) {
	changeselected(tabName);
	var tabIndex = getSelectedTabIndex();
	var originalTabIndex = "${requestScope.selectedTabIndex}";
	
	if(tabIndex != originalTabIndex) {		
		expandcontent('sweepTodayParent', obj);
		submitForm("displayListByEntity", tabIndex);		
	}
}

//This function is called when export button is pressed.
function exportData(exportType) 
{
	document.forms[0].elements["selectedTabIndex"].value = getSelectedTabIndex();
	document.forms[0].method.value = 'exportManualSweepingScreen';
	document.forms[0].exportType.value = exportType.trim();
	document.forms[0].entityId.value = document.forms[0].elements["movement.id.entityId"].value;
	document.forms[0].ccyCode.value = document.forms[0].elements["movement.currencyCode"].value;
	document.forms[0].acctType.value = document.forms[0].elements["movement.accttype"].value;
	var checkbox = document.getElementById('cutoffCheckbox');
	 var shouldHideRow = checkbox.checked;
	 document.forms[0].hideAccountsAfterCutOff.value = shouldHideRow;
	if(record() > 0) {
		document.forms[0].submit();
	}
}
function resetSelectedValues(){
		setStoredParam('ManualSweepSelectedList','');
}

//Mantis 6705
//The check box will be checked by default on entry to the screen and will serve to limit 
//the grid to displaying only those accounts which have not yet reached their sweep cut-off time.
function refreshGridHideShowAccountsCutOff() {
	  var checkbox = document.getElementById('cutoffCheckbox');
	  var tableRows = table1.dataTable.tBody.rows;
	  var l = tableRows.length;

	  for (var i = 0; i < l; i++) {
	    var isValueDateAchievable = tableRows[i].cells[9].innerText.trim();
	    var shouldHideRow = checkbox.checked && isValueDateAchievable === 'N';
	    if( isRowSelected(tableRows[i]) && shouldHideRow){
			unHighLightTableRow(tableRows[i]);
		}
	    tableRows[i].style.display = shouldHideRow ? 'none' : ''; // Hide the row if shouldHideRow is true, otherwise show the row
	  }
	  
	  getSelectedListForDisplay();
	  
	}
	
function clearSelectedAccounts(){
	 setStoredParam('ManualSweepSelectedList','');
	 document.forms[0].selectedList.value = "";
	 document.getElementById("selectedAccount2").textContent =  "";
	 document.getElementById("selectedAccount1").textContent =  "";
	 document.getElementById("sweepbutton").innerHTML = document.getElementById("sweepdisablebutton").innerHTML;
	 
	 
	 var table = document.getElementById("sweepDetailsToday");  
	 var tbody = table.getElementsByTagName("tbody")[0];    
	 var rows = tbody.getElementsByTagName("tr");
	 var selectedList = "";
	 for (i=0; i < rows.length; i++) 
	 {
		if( isRowSelected(rows[i])){
			unHighLightTableRow(rows[i]);
		}
	}
}

</script>
</head>

<body leftmargin="0" topmargin="0" marginheight="0"
	onLoad="setParentChildsFocus();bodyOnLoad();setFocus(document.forms[0]); ShowErrMsgWindow('${actionError}');"
	onunload="call()">

	<s:form action="sweep.do">
		<s:set var="CDM" value="#request.session.CDM" />
		<input name="method" type="hidden" value="">
		<input name="entityId" type="hidden" value="">
		<input name="exportType" type="hidden" value="">
		<input type="hidden" name="screen" id="exportDataScreen"
			value="ManualSweeping-SmartPredict" />
		<input name="ccyCode" type="hidden" value="">
		<input name="acctType" type="hidden" value="">
		<input name="selectedCurrencyCode" type="hidden" value="GPB">
		<input name="selectedList" type="hidden" value="">
		<input name="selectedTabIndex" type="hidden" value="1">
		<input name="selectedTabName" type="hidden" value="sweepTodayParent">
		<input name="menuAccessId" type="hidden">
		<input name="entityIdChanged" type="hidden" value="">
		<input name="hideAccountsAfterCutOff" type="hidden" value="">
		
		<div id="ManualSweep"
			style="position: absolute; left: 20; top: 20; width: 1300px; height: 86px; visibility: visible; border: 2px outset;"
			color="#7E97AF">
			<div id="ManualSweep"
				style="position: absolute; left: 8px; top: 4px; width: 1100px; height: 10px;">
				<table width="1100px" border="0" cellpadding="0" cellspacing="0"
					height="70px">
					<tr height="25px">
						<td width="95px"><b><s:text name="party.entityId" /></b></td>
						<td width="28px">&nbsp;</td>
						<td width="140px"><s:select cssClass="htmlTextAlpha" tabindex="1" titleKey="tooltip.selectEntityid" id="movement.id.entityId" name="movement.id.entityId" onchange="submitEntityForm('displayListByEntity');" cssStyle="width:140px" list="#request.entityList" listKey="value" listValue="label" /></td>
						<td width="20px">&nbsp;</td>
						<td width="280px"><span id="entityDesc" name="entityDesc"
							class="spantext"></td>
						<td width="30px">&nbsp;</td>

						<td width="125px"><b><s:text name="movement.secondEntity" /></b></td>
						<td width="28px">&nbsp;</td>
						<td width="140px"><s:select cssClass="htmlTextAlpha" tabindex="1" titleKey="tooltip.selectEntityid" id="movement.againstAccountEntityId" name="movement.againstAccountEntityId" onchange="submitEntityForm('displayListByEntity');" cssStyle="width:140px" list="#request.againstEntityList" listKey="value" listValue="label" /></td>
						<td width="20px">&nbsp;</td>
						<td width="280px"><span id="entity2Desc" name="entity2Desc" class="spantext"></span></td>

						<td width="280px" style="padding-bottom: 5px;">
						   <b><s:text name="tooltip.HideAccountsAfterCutoff" /></b>
						</td>
						<td width="25px" style="width:13px;margin-bottom: 5px;">
						<s:if test='true==#request.hideAccountsAfterCutOff' >
						    <input type="checkbox" id="cutoffCheckbox" checked title="<s:text name='button.tooltip.hidecutoffcutoff' />"
						           onclick="refreshGridHideShowAccountsCutOff()">
						</s:if>
						<s:if test='true!=#request.hideAccountsAfterCutOff' >
						    <input type="checkbox" id="cutoffCheckbox"
						           onclick="refreshGridHideShowAccountsCutOff()">
						</s:if>
						</td>
					</tr>
					<tr height="25px">
						<td width="95px"><b><s:text name="sweepsearch.currency" /></b></td>
						<td width="28px">&nbsp;</td>
						<td width="120px"><s:select id="movement.currencyCode" name="movement.currencyCode" cssClass="htmlTextAlpha" tabindex="2" titleKey="tooltip.selectCurrencyCode" cssStyle="width:55px" onchange="submitForm('displayListByEntity')" list="#request.currencyList" listKey="value" listValue="label" /></td>
						<td width="20px">&nbsp;</td>
						<td width="280px"><span id="currencyDesc" name="currencyDesc"
							class="spantext"></td>
					</tr>
					<tr height="25px">
						<td width="125px"><b><s:text name="sweepsearch.accType" /></b></td>
						<td width="28px">&nbsp;</td>
						<td width="120px"><s:select id="movement.accttype" name="movement.accttype" onchange="submitForm('displayListByEntity')" cssClass="htmlTextAlpha" tabindex="3" titleKey="tooltip.selectAccountType" cssStyle="width:42px" list="#request.accountList" listKey="value" listValue="label" /></td>
						<td width="20px">&nbsp;</td>
						<td width="280px"><span id="accountDesc" name="accountDesc"
							class="spantext"></td>
					</tr>
				</table>
			</div>
		</div>

		<div id="ddimagetabs"
			style="position: absolute; left: 20px; top: 114px; width: 750px; height: 30px;">
			<a id="todayTab"
				style="<%=!(Boolean) request.getAttribute("isBusinessDayForToday") ? "color:#A0A0A0" : ""%>"
				tabindex="4" onmouseout="revertback('sweepTodayParent',this);"
				onmouseover="changecontent('sweepTodayParent',this)"
				onClick="checkTabIndex(this,'sweepTodayParent');countIt(1)"><b><s:property value='#request.todayTablabel' /></b></a> <a id="todayTabPlusOne"
				style="<%=!(Boolean) request.getAttribute("isBusinessDayForTodayPlusOne") ? "color:#A0A0A0" : ""%>"
				tabindex="5"
				onmouseout="revertback('sweepTodayPlusOneParent',this);"
				onmouseover="changecontent('sweepTodayPlusOneParent',this)"
				onClick="checkTabIndex(this,'sweepTodayPlusOneParent');countIt(2)"><b><s:property value='#request.todayTabPlusOnelabel' /></b></a> <a id="todayTabPlusTwo"
				style="<%=!(Boolean) request.getAttribute("isBusinessDayForTodayPlusTwo") ? "color:#A0A0A0" : ""%>"
				tabindex="6"
				onmouseout="revertback('sweepTodayPlusTwoParent',this);"
				onmouseover="changecontent('sweepTodayPlusTwoParent',this)"
				onClick="checkTabIndex(this,'sweepTodayPlusTwoParent');countIt(3)"><b><s:property value='#request.todayTabPlusTwolabel' /></b></a> <a id="todayTabPlusThree"
				style="<%=!(Boolean) request.getAttribute("isBusinessDayForTodayPlusThree") ? "color:#A0A0A0" : ""%>"
				tabindex="7"
				onmouseout="revertback('sweepTodayPlusThreeParent',this);"
				onmouseover="changecontent('sweepTodayPlusThreeParent',this)"
				onClick="checkTabIndex(this,'sweepTodayPlusThreeParent');countIt(4)"><b><s:property value='#request.todayTabPlusThreelabel' /></b></a> <a id="todayTabPlusFour"
				style="<%=!(Boolean) request.getAttribute("isBusinessDayForTodayPlusFour") ? "color:#A0A0A0" : ""%>"
				tabindex="8"
				onmouseout="revertback('sweepTodayPlusFourParent',this);"
				onmouseover="changecontent('sweepTodayPlusFourParent',this)"
				onClick="checkTabIndex(this,'sweepTodayPlusFourParent');countIt(5)"><b><s:property value='#request.todayTabPlusFourlabel' /></b></a> <a id="todayTabPlusFive"
				style="<%=!(Boolean) request.getAttribute("isBusinessDayForTodayPlusFive") ? "color:#A0A0A0" : ""%>"
				tabindex="9"
				onmouseout="revertback('sweepTodayPlusFiveParent',this);"
				onmouseover="changecontent('sweepTodayPlusFiveParent',this)"
				onClick="checkTabIndex(this,'sweepTodayPlusFiveParent');countIt(6)"><b><s:property value='#request.todayTabPlusFivelabel' /></b></a> <a id="todayTabPlusSix"
				style="<%=!(Boolean) request.getAttribute("isBusinessDayForTodayPlusSix") ? "color:#A0A0A0" : ""%>"
				tabindex="10"
				onmouseout="revertback('sweepTodayPlusSixParent',this);"
				onmouseover="changecontent('sweepTodayPlusSixParent',this)"
				onClick="checkTabIndex(this,'sweepTodayPlusSixParent');countIt(7)"><b><s:property value='#request.todayTabPlusSixlabel' /></b></a> <a id="todayTabPlusSeven"
				tabindex="11"
				onmouseout="revertback('sweepTodayPlusSevenParent',this);"
				onmouseover="changecontent('sweepTodayPlusSevenParent',this)"
				onClick="checkTabIndex(this,'sweepTodayPlusSevenParent');countIt(8)"><b><s:text name="outstanding.tabs.today1" /></a> <a id="todayTabPlusEight"
				tabindex="12"
				onmouseout="revertback('sweepTodayPlusEightParent',this);"
				onmouseover="changecontent('sweepTodayPlusEightParent',this)"
				onClick="checkTabIndex(this,'sweepTodayPlusEightParent');countIt(9)"><b><s:text name="outstanding.tabs.today2" /></a>
		</div>

		<div id="Line"
			style="position: absolute; left: 349px; top: 132px; width: 290px; height: 20px;">
			<table width="590px">
				<tr>
					<td><img src="images/tabline.gif" width="790px" height="1"></td>
				</tr>
			</table>
		</div>

		<div id="tabcontentcontainer"
			style="position: absolute; left: 20px; top: 136px; width: 1300px; height: 450px;">
			<div id="sweepTodayParent" class="tabcontent">
				<div id="sweepTodayParent"
					style="position: absolute; z-index: 99; left: 0px; top: 0px; width: 1285px; height: 10px;">

					<table class="sort-table" id="table_1" bgcolor="#B0AFAF"
						width="1280px" style="margin-top: 0px;" border="0" cellspacing="1"
						cellpadding="0" height="25px">
						<thead>
							<tr>
								<td width="100" align="center"
									title='<s:text name="tooltip.sortValueDate"/>'><b><s:text name="movement.valueDate" /></b></td>
								<td width="70" align="center"
									title='<s:text name="tooltip.sortMvmLevel"/>'><b><s:text name="movement.level" /></b></td>
								<td width="120" align="center"
									title='<s:text name="tooltip.sortEntityId"/>'><b><s:text name="acctMaintenance.entityId" /></b></td>
								<td width="228" align="center"
									title='<s:text name="tooltip.sortAccountId"/>'><b><s:text name="movement.accountId" /></b></td>


								<!-- added for rabo bank customization  starts-->
								<td width="230px" align="center"
									title='<s:text name="tooltip.accName"/>'><b><s:text name="movement.Name" /></b></td>
								<!-- added for rabo bank customization  ends-->


								<td width="150px" align="center"
									title='<s:text name="tooltip.sortPredictBalance"/>'><b><s:text name="movement.balances" /></b></td>

								<td width="130px" align="center"
									title='<s:text name="tooltip.sortSweepAmount"/>'><b><s:text name="movement.sweepAmount" /></b></td>
								<td width="144px" align="center"
									title='<s:text name="tooltip.sortTargetBalance"/>'><b><s:text name="movement.targetbalance" /></b></td>
								<td width="70" align="center"
									title='<s:text name="tooltip.sortCutOffTime"/>'><b><s:text name="movement.cutOff" /></b></td>
							</tr>
						</thead>
					</table>
				</div>

				<div id="ddscrolltable"
					style="position: absolute; left: 0px; top: 0px; width: 1297px; height: 450px;">
					<div id="sweepTodayParent1"
						style="position: absolute; z-index: 99; left: 0px; top: 22px; width: 1275px; height: 10px;">
						<table class="sort-table" id="sweepDetailsToday" width="1278px"
							border="0" cellspacing="1" cellpadding="0" height="410">
							<!--Start::code modified by sandeepkumar for mantis 2039 -  Visually indicate accounts past cut-off or subject to non-working day . -->
							<tbody>
								<%
									int count = 0;
										String rowClass = null;
								%>
								<s:iterator value='#request.sweepDetailsToday' var='sweepDetailsToday' >

										<s:set var="isValueDateAchievable" value="#sweepDetailsToday.isValueDateAchievable" />
 										<jsp:useBean id="isValueDateAchievable" type="java.lang.String" />
									<%
										if (isValueDateAchievable.equals("N")) {

													rowClass = "accn";
												} else {
													rowClass = (count++ % 2 == 0 ? "even" : "odd");
												}
									%>
									<tr class="<%=rowClass%>">
										<s:hidden name="id.entityId" value="%{#sweepDetailsToday.id.entityId}"
											disabled="true" />
										<s:hidden name="currencyCode" value="%{#sweepDetailsToday.currencyCode}"
											disabled="true" />

										<s:hidden name="movementEntityId" value="%{#sweepDetailsToday.movementEntityId}" disabled="true" />

										<s:hidden name="displayLevel" value="%{#sweepDetailsToday.displayLevel}"
											disabled="true" />
										<td width="100"><s:property value='#sweepDetailsToday.id.valueDateAsString' />&nbsp;</td>
										<td width="70" align="left"><s:property value='#sweepDetailsToday.displayLevel' />&nbsp;</td>
										<td width="120"><s:property value='#sweepDetailsToday.movementEntityId' />&nbsp;</td>
										<td width="228"><s:property value='#sweepDetailsToday.id.accId' />&nbsp;</td>

										<!--starts for Rabo bank -->
										<td width="230px"><s:property value='#sweepDetailsToday.accName' />&nbsp;</td>
										<!--ends for Rabo bank -->

										<td width="150px" align="right"
											<s:if test='"P"==#sweepDetailsToday.sweepFrmbal' >
							<s:if test='true==#sweepDetailsToday.predictedBalanceNegative' >
							style="color:red"
							</s:if>><s:property value='#sweepDetailsToday.predictedBalanceAsString' />&nbsp;
											</s:if> <s:if test='"E"==#sweepDetailsToday.sweepFrmbal' >
												<s:if test='true==#sweepDetailsToday.externalBalanceNegative' >
							style="color:red"
							</s:if>><s:property value='#sweepDetailsToday.externalBalanceAsString' />&nbsp;
							</s:if></td>

										<td width="130px" align="right"
											<s:if test='"Y"==#sweepDetailsToday.signFlagForSweepAmount' >
							style="color:red"
							</s:if>><s:property value='#sweepDetailsToday.sweepAmountAsString' />&nbsp;</td>
										<td width="144px" align="right"
											<s:if test='true==#sweepDetailsToday.targetBalanceNegative' >
							style="color:red"
							</s:if>><s:property value='#sweepDetailsToday.targetBalanceAsString' />&nbsp;</td>
										<td width="80" align="center"><s:property value='#sweepDetailsToday.cuttOff' />&nbsp;</td>
										<td style="display: none"><s:property value='#sweepDetailsToday.isValueDateAchievable' /></td>
									</tr>
									<!--End::code modified by sandeepkumar for mantis 2039 -  Visually indicate accounts past cut-off or subject to non-working day -->
								</s:iterator>
							</tbody>
							<tfoot>
								<tr>
									<td colspan="9"></td>
								</tr>
							</tfoot>
						</table>

					</div>
					</div>
				<div style="padding-top:3px">
				<b><s:text name="label.selected" />&nbsp;<s:text name="role.roleAccounts" />:</b> <span id="selectedAccount1" name="selectedAccount1"				class="spantext"></span>&nbsp;<span id="selectedAccount2" name="selectedAccount2"				class="spantext"></span>
				</div>
			</div>

		</div>

		<div id="ManualSweep"
			style="position: absolute; left: 1190; top: 614; width: 50; height: 15px; z-index: 5; visibility: visible;">
			<table>
				<div id="exportReport" />
			</table>
		</div>
		<div
			style="position: absolute; left: 1250; top: 613; width: 30; height: 15px; visibility: visible; z-index: 100">
			<a tabindex="19" href=#
				onclick="javascript:openWindow(buildPrintURL('print','Manual Sweep '),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')"
				onMouseOut="MM_swapImgRestore()"
				onMouseOver="MM_swapImage('Help','','images/help_default.GIF ',1)"><img
				src="images/help_default.GIF " name="Help" border="0"
				title='<s:text name="tooltip.helpScreen"/>'></a>
		</div>
		<div id="ddimagebuttons" color="#7E97AF"
			style="position: absolute; border: 2px outset; left: 20; top: 605; width: 1300px; height: 39px; visibility: visible;">
			<div id="manual"
				style="position: absolute; left: 6; top: 4; width: 467px; height: 15px; visibility: visible;">
				<table width="210" border="0" cellspacing="0" cellpadding="0"
					height="20">
					<tr>
						<td id="refreshbutton"><a width="70px" tabindex="13"
							title='<s:text name="tooltip.refreshWindow"/>'
							onMouseOut="collapsebutton(this)"
							onMouseOver="highlightbutton(this)"
							onMouseDown="expandbutton(this)"
							onMouseUp="highlightbutton(this)"
							onClick="javascript:submitForm('displayListByEntity');"><s:text name="button.Refresh" /></a></td>

						<td id="sweepbutton"><a tabindex="14"><s:text name="button.sweep" /></a></td>
					
						<td id="clearbutton"><a tabindex="15"><s:text name="button.clear" /></a></td>


						<td width="70px"><a
							title='<s:text name="tooltip.close"/>' tabindex="16"
							onMouseOut="collapsebutton(this)"
							onMouseOver="highlightbutton(this)"
							onMouseDown="expandbutton(this)"
							onMouseUp="highlightbutton(this)"
							onclick="confirmClose('C');resetSelectedValues();"><s:text name="button.close" /></a></td>
					</tr>
				</table>
			</div>

			<table height="33">
				<tr>
					<td id="lastRefTimeLable" width="1020px" align="right"><s:text name="label.lastRefTime" /></td>
					<td id="lastRefTime"><input class="textAlpha"
						style="background: transparent; border: 0;" tabindex="-1" readonly
						name="maxPageNo" value="" size="14"></td>
				</tr>
			</table>

			<div
				style="position: absolute; left: 6; top: 4; width: 80px; height: 15px; visibility: hidden;">
				<table width="70" border="0" cellspacing="0" cellpadding="0"
					height="20 " style="visibility: hidden">
					<tr>

						<td id="refreshdisablebutton"><a class="disabled"
							disabled="disabled"><s:text name="button.Refresh" /></a></td>

						<td width="70px" id="sweepenablebutton"><a tabindex="14"
							title='<s:text name="tooltip.Sweep"/>'
							onMouseOut="collapsebutton(this)"
							onMouseOver="highlightbutton(this)"
							onMouseDown="expandbutton(this)"
							onMouseUp="highlightbutton(this)"
							onClick="openSweepDetailWindow('sweepdetailsWindow','left=50,top=190,width=1021,height=635,toolbar=0, resizable=yes, status=yes, scrollbars=yes','true')";><s:text name="button.sweep" /></a></td>
						<td id="sweepdisablebutton" width="70px"><a class="disabled"
							disabled="disabled"><s:text name="button.sweep" /></a></td>
							
					<td id="clearButtondisabled" width="70px"><a class="disabled"
							disabled="disabled"><s:text name="button.clear" /></a></td>	
					<td id="clearButtonEnabled"><a width="70px" tabindex="15"
							title='<s:text name="button.tooltip.clearAccounts"/>'
							onMouseOut="collapsebutton(this)"
							onMouseOver="highlightbutton(this)"
							onMouseDown="expandbutton(this)"
							onMouseUp="highlightbutton(this)"
							onClick="javascript:clearSelectedAccounts();"><s:text name="button.clear" /></a></td>
									
					</tr>
				</table>
			</div>
		</div>
		<blockquote>&nbsp;</blockquote>
		<p>&nbsp;</p>
	</s:form>
</body>
</html>