<?xml version="1.0" encoding="UTF-8"?>
<%@ page contentType="text/xml" %>
<%@ page import="java.util.*"  %>
<%@ page import="org.swallow.util.OpTimer"  %>
<%@ taglib prefix="s" uri="/struts-tags" %>
<%@ page import="org.swallow.util.SwtConstants"  %>

<s:set var="recordCount" value="#request.currencyMonitor.balanceDetails.size()"/>

<currencymonitor 
		databuilding="<%=request.getAttribute("jobFlagStatus")!=null?"true":"false"%>" 
		currencyFormat="<s:property value="#request.session.CDM.currencyFormat" />" 
		refresh="<s:property value="#request.autoRefreshRate" />" 
    	hideLoro="<s:property value="#request.hideLoro" />" 
	   	 to="<s:property value="#request.currencyMonitor.toDateAsString" />" 
	    from="<s:property value="#request.currencyMonitor.fromDateAsString" />" 
	    sysDateFrmSession="<s:property value="#request.sysDateFrmSession" />" 
	    lastRefTime="<s:property value="#request.lastRefTime" />"
	    dateComparing= "<%if ( SwtConstants.YES.equals(request.getAttribute("dateComparing"))) {	%>true<%} else {%>false<%}%>" 
		currfontsize="<s:property value="#request.fontSize" />" 
		>

	<%-- Response status - OK --%>
 <request_reply>
        <status_ok><s:property value="#request.reply_status_ok" /></status_ok>
        <message><s:property value="#request.reply_message" /></message>
        <location />
    </request_reply>
	<%-- Job timing --%>
    <timing>
        <s:iterator value="#request.opTimes" var="opTime">
            <operation id="${opTime.key}"><s:property value="#opTime.value" /></operation>
        </s:iterator>
    </timing>
	<%-- Currency Monitor gird data --%>
	<grid>
		<%-- Gird column metadata --%>
		<metadata>
			<columns>
			   <%-- Alerting --%>
				<column 
					heading=""
					draggable="false"					
					filterable="false"
					type="str"
					dataelement="alerting"
					width="10"
					sort="true"
					clickable="true"
					resizable ="false"
				/>
				<%-- Currency code --%>
				<column 
						heading="<s:text name="sweep.currencyCode"/>"
						draggable="false"					
						filterable="true"
						type="str"
						dataelement="ccy"
						sort="true"
						width="<s:property value='#request.column_width.ccy' />" />
				<%-- Loro balance --%>
				  <s:if test='#request.hideLoro != "Y"'>
					<column 
							heading="<s:text name="acc.lorotype"/>"
							draggable="false"					
							filterable="false"
							type="num"
							dataelement="loro"
							sort="true"
							 width="<s:property value='#request.column_width.loro' />" />
				</s:if>
				<%-- Predicted balance --%>
				<% int headerCount=0; %>
				<s:iterator value="#request.currencyMonitor.headerDetails" var="headerRecord">
					<% headerCount++; %>
					<column 
							heading="<s:property value='#headerRecord.balanceDateAsString' />"
							draggable="false"
							filterable="false"
							holiday="<s:if test='#headerRecord.holiday != "N"'>true</s:if><s:else>false</s:else>"
							type="num"
							sort="true"
							dataelement="predictdate<%=headerCount%>"
							width="<%=((HashMap)request.getAttribute("column_width")).get("predictdate"+headerCount)%>"
					/>
				</s:iterator>
			</columns>
		</metadata>
		<rows size="${recordCount}">
          <s:iterator value="#request.currencyMonitor.balanceDetails" var="currencyRecord">
               <row>
                    <ccy clickable="false"><s:property value="#currencyRecord.currCode" /></ccy>
                    <alerting clickable="false"><s:property value="#currencyRecord.scenarioHighlighted" /></alerting>
                    <s:if test='#request.hideLoro != "Y"'>
                        <loro
                            negative="<s:if test='#currencyRecord.loroBalanceNegative == true'>true</s:if><s:else>false</s:else>"
                            clickable="<s:if test='#request.currencyMonitor.entityId != "All"'>true</s:if><s:else>false</s:else>"
                            ><s:property value="#currencyRecord.loroBalanceAsString" />
                        </loro>
                    </s:if>
                    <% int balanceCount=0; %>
                    <s:iterator value="#currencyRecord.balanceDateRecords" var="balanceRecord">
                    <% balanceCount++; %>
                       <predictdate<%=balanceCount%> 
                            date="<s:property value="#balanceRecord.balanceOrgDateAsString" />"
                            holiday="<s:if test='#balanceRecord.holiday != "N"'>true</s:if><s:else>false</s:else>"
                            negative="<s:if test='#balanceRecord.predBalanceNegative == true'>true</s:if><s:else>false</s:else>"
                            clickable="<s:if test='#request.currencyMonitor.entityId != "All"'>true</s:if><s:else>false</s:else>"
                            ><s:property value="#balanceRecord.predBalanceAsString.trim()" /></predictdate<%=balanceCount%>>
                    </s:iterator>
                </row>
            </s:iterator>
          	</rows>
          	<s:set var="totalRecord" value="#request.currencyMonitor.balanceTotal"/>
          	   <totals>
	            <total>
	                <ccy clickable="false"><s:text name="positionlevel.total" /> <s:property value="#totalRecord.currCode" /></ccy>
	                <s:if test='#request.hideLoro != "Y"'>
	                    <loro
	                        negative="<s:if test='#totalRecord.loroBalanceNegative == true'>true</s:if><s:else>false</s:else>"
	                        ><s:property value="#totalRecord.loroBalanceAsString" />
	                    </loro>
	                </s:if>
	                <% int totalCount=0; %>
	                <s:iterator value="#totalRecord.balanceDateRecords" var="totalBalanceRecord">
	                	<% totalCount++; %>
	                    <predictdate<%=totalCount%> 
	                        negative="<s:if test='#totalBalanceRecord.predBalanceNegative == true'>true</s:if><s:else>false</s:else>"
	                        ><s:property value="#totalBalanceRecord.predBalanceAsString" />
	                    </predictdate<%=totalCount%>>
	                </s:iterator>
	            </total>
	        </totals>
    </grid>
		
		<selects>
		
		<select id="entity">
		
				<s:iterator value="#request.entities" var="entity">
				<option 
					value="<s:property value="#entity.value" />" 
					selected="<s:if test="#request.currencyMonitor.entityId==#entity.value">1</s:if><s:else>0</s:else>"
				><s:property value="#entity.label" /></option>
			</s:iterator>
			</select>
			
			
		<select id="currencygroup">
		
				<s:iterator value="#request.currencyGroupList" var="group">
				<option 
					value="<s:property value="#group.value" />" 
					selected="<s:if test="#request.currencyMonitor.currGrp==#group.value">1</s:if><s:else>0</s:else>"
				><s:property value="#group.label" /></option>
			</s:iterator>
			</select>
		</selects>
	
    
</currencymonitor>