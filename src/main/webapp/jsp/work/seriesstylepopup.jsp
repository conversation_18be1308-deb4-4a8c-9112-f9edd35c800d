<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">

<html>
<head>
<script type="text/javascript" src="js/preloadtabs.js"></script>
<script type="text/javascript" src="js/commonJS.js"></script>
<script type="text/javascript" src="js/common.js"></script>
<script type="text/javascript" src="js/sortabletable.js"></script>
<script type="text/javascript" src="js/select.js"></script>
<script type="text/javascript" src="js/filter.js"></script>
<script type="text/javascript" src="js/dialog.js"></script>
<script type="text/javascript" src="js/excel.js"></script>
<script type="text/javascript" src="js/datavalidation.js"></script>
<script type="text/javascript" src="js/calendar.js"></script>
<script type="text/javascript" src="js/tabnew.js"></script>
<script type="text/javascript" src="js/zxml.js"></script>
<%--<script type="text/javascript" src="js/jquery-1.10.0.min.js"></script>--%>
<script type="text/javascript" src="js/jquery-3.6.0.min.js"></script>
<script type="text/javascript" src="js/swt-jquery.extension.js"></script>
<script type="text/javascript" src="js/moment.min.js"></script>
<link rel="stylesheet" type="text/css" href="style/Style.css">
<link rel="stylesheet" type="text/css" href="style/excel.css">
<link rel="stylesheet" type="text/css" href="style/tabstylenew.css">
<link rel="stylesheet" type="text/css" href="style/calendar.css">
<!-- <msdropdown> -->
<link rel="stylesheet" type="text/css" href="ILMHighcharts/combonoxComponent/css/dd.css" />
<script src="ILMHighcharts/combonoxComponent/js/jquery.dd.js"></script>
<!-- </msdropdown> -->
<script type="text/javascript">

	var LINE_SOLID = 1;
	var LINE_DOTTED = 2;
	var LINE_DASHED = 3;
	
	var AREA = 10;

	var lineSolidStyleList = [{id:"CONT_SEGMENT_BLACK",label:"Black Line"},{id:"CONT_SEGMENT_RED",label:"Red Line"},{id:"CONT_SEGMENT_BOLD_RED",label:"Bold Red Line"},
		{id:"CONT_SEGMENT_YELLOW",label:"Yellow Line"},{id:"CONT_SEGMENT_GREEN",label:"Green Line"},{id:"CONT_SEGMENT_BLUE",label:"Blue Line"},
		{id:"CONT_SEGMENT_ORANGE",label:"Orange Line"},{id:"CONT_SEGMENT_MAGENTA",label:"Magenta Line"},{id:"CONT_SEGMENT_PURPLE",label:"Purple Line"}];
	var lineDottedStyleList = [{id:"DOTTED_SEGMENT_BLACK",label:"Black Dotted Line"},{id:"DOTTED_SEGMENT_RED",label:"Red Dotted Line"},{id:"DOTTED_SEGMENT_YELLOW",label:"Yellow Dotted Line"},
			{id:"DOTTED_SEGMENT_GREEN",label:"Green Dotted Line"},{id:"DOTTED_SEGMENT_BLUE",label:"Blue Dotted Line"},{id:"DOTTED_SEGMENT_ORANGE",label:"Orange Dotted Line"},
			{id:"DOTTED_SEGMENT_MAGENTA",label:"Magenta Dotted Line"},{id:"DOTTED_SEGMENT_PURPLE",label:"Purple Dotted Line"}];
	var lineDashedStyleList = [{id:"DASHED_SEGMENT_BLACK",label:"Black Dashed Line"},{id:"DASHED_SEGMENT_RED",label:"Red Dashed Line"},{id:"DASHED_SEGMENT_YELLOW",label:"Yellow Dashed Line"},
			{id:"DASHED_SEGMENT_GREEN",label:"Green Dashed Line"},{id:"DASHED_SEGMENT_BLUE",label:"Blue Dashed Line"},{id:"DASHED_SEGMENT_ORANGE",label:"Orange Dashed Line"},
			{id:"DASHED_SEGMENT_MAGENTA",label:"Magenta Dashed Line"},{id:"DASHED_SEGMENT_PURPLE",label:"Purple Dashed Line"}];
	var areasStyleList = [{id:"CONT_AREA_BLIZZARD_BLUE",label:"Area Blizzed Blue"},{id:"CONT_AREA_COTTON_CANDY",label:"Area Cotton Candy"},
	{id:"CONT_AREA_BLACK",label:"Black Area"},{id:"CONT_AREA_GREY",label:"Grey Area"},{id:"CONT_AREA_LIGHT_GREY",label:"Light Grey Area"},{id:"CONT_AREA_RED",label:"Red Area"},
	{id:"CONT_AREA_INDIAN_RED",label:"Red Indian Area"},{id:"CONT_AREA_PINK",label:"Pink Area"},{id:"CONT_AREA_ORANGE",label:"Orange Area"},{id:"CONT_AREA_PEACH_PUFF",label:"Peach Puff Area"},
	{id:"CONT_AREA_YELLOW",label:"Yellow Area"},{id:"CONT_AREA_GREEN",label:"Green Area"},{id:"CONT_AREA_LIME",label:"Lime Area"},{id:"CONT_AREA_LIGHT_GREEN",label:"Light Green Area"},
	{id:"CONT_AREA_BLUE",label:"Blue Area"},{id:"CONT_AREA_STEEL_BLUE",label:"Steel Blue Area"},{id:"CONT_AREA_LIGHT_BLUE",label:"Light Blue Area"},{id:"CONT_AREA_LIGHT_CYAN",label:"Light Cyan Area"},
	{id:"CONT_AREA_MAGENTA",label:"Magenta Area"},{id:"CONT_AREA_PURPLE",label:"Purple Area"},{id:"CONT_AREA_ANTIQUE_WHITE",label:"Antique White Area"},
	{id:"CONT_AREA_APRICOT_PEACH",label:"Apricot Peach Area"},{id:"CONT_AREA_NAVAJO_WHITE",label:"Navajo White Area"},{id:"CONT_AREA_ROSE_FOG",label:"Rose Fog Area"},
	{id:"CONT_AREA_DARK_SALMON",label:"Dark Salmon Area"},{id:"CONT_AREA_VIOLET",label:"Violet Area"},{id:"DASHED_CORAL_AREA",label:"Dashed Coral Area"},
	{id:"DASHED_BLUE_AREA",label:"Dashed Blue Area"},{id:"DASHED_AQUA_AREA",label:"Dashed Aqua Area"},{id:"DASHED_DEEP_PINK_AREA",label:"Dashed Deep Pink Area"},
	{id:"DASHED_GOLDEN_ROD_AREA",label:"Dashed Golden Rod Area"},{id:"DASHED_GREEN_AREA",label:"Dashed Green Area"},{id:"DOTTED_GREEN_YELLOW_AREA",label:"Dotted Green Yellow Area"},
	{id:"DOTTED_INDIAN_RED_AREA",label:"Dotted Indian Red Area"},{id:"DOTTED_MAGENTA_AREA",label:"Dotted Magenta Area"},{id:"DASHED_AREA_PERANO",label:"Dashed Area Perano"}];
	
	function closeAlertPopup() {
		parent.getFlashObject("mySwf").closeAlertWindowFromCallBack();
	}
	
	function GetURLParameter(sParam)
	{
	    var sPageURL = window.location.search.substring(1);
	    var sURLVariables = sPageURL.split('&');
	    for (var i = 0; i < sURLVariables.length; i++) 
	    {
	        var sParameterName = sURLVariables[i].split('=');
	        if (sParameterName[0] == sParam) 
	        {
	            return sParameterName[1];
	        }
	    }
	}
	
	var selectedTab = '';
	var areasLineCharts;
	var segmentsLineCharts;
	function bodyOnLoad()
	{
		setParentChildsFocus();	
		selectedTab = window.opener.selectedTab;
		if(selectedTab === "GlobalView") {
			segmentsLineCharts = window.opener.globalSegmentsLineCharts ;
			areasLineCharts = window.opener.globalareasLineCharts;
		}else {
			segmentsLineCharts = window.opener.analysisSegmentsLineCharts;
			areasLineCharts = window.opener.analysisareasLineCharts ;
		}
		
		addComboBoxes(segmentsLineCharts, areasLineCharts);
	}
	
	// 
	function setILMStylesValues(segmentsLineCharts, areasLineCharts) {
		addComboBoxes(segmentsLineCharts, areasLineCharts);
	}
	
	// Code for dynamic comboboxes
	function addComboBoxes(segmentsLineCharts, areasLineCharts) {
        var table = document.getElementById("comboboxesContainer");
        table.innerHTML = "";
        for (var i = 0; i < segmentsLineCharts.length; i++) {
        	var rowCount = table.rows.length;
            var row = table.insertRow(rowCount);
            row.setAttribute("style", "height:35px;");
			var obj = segmentsLineCharts[i];
            var cell1 = row.insertCell(0);
            var select = document.createElement("select");
            select.name = obj.id;
            select.setAttribute("style", "width:250px;");
            if (obj.type == LINE_SOLID) {
	            addOptions(select, obj.value, lineSolidStyleList);
            } else if (obj.type == LINE_DOTTED) {
	            addOptions(select, obj.value, lineDottedStyleList);
            } else if (obj.type == LINE_DASHED) {
	            addOptions(select, obj.value, lineDashedStyleList);
            }
          	var label = document.createElement("label");
          	label.innerHTML = obj.label;
          	label.setAttribute("style", "width:250px;display:inline-block;margin-left:20px;font-weight:bold");
            cell1.appendChild(label);
            cell1.appendChild(select);
		}
        
		var rowCount = table.rows.length;
		var row = table.insertRow(rowCount);
		row.setAttribute("style", "height:15px;");
		
		for (var i = 0; i < areasLineCharts.length; i++) {
			var rowCount = table.rows.length;
			var row = table.insertRow(rowCount);
			row.setAttribute("style", "height:35px;");
			var obj = areasLineCharts[i];
			var cell1 = row.insertCell(0);
			var select = document.createElement("select");
			select.name = obj.id;
			select.setAttribute("style", "width:250px;");
			addOptions(select, obj.value, areasStyleList);
			var label = document.createElement("label");
			label.innerHTML = obj.label;
			label.setAttribute("style", "left:50px;width:250px;display:inline-block;margin-left:20px;font-weight:bold");
			cell1.appendChild(label);
			cell1.appendChild(select);
		}
		
        $("select").msDropdown({roundedBorder:false});
    }
	
	function addOptions(element, selectedElement, styleList) {
		for (var i = 0; i < styleList.length; i++) {
			var option = document.createElement("option");
	        option.value=styleList[i].id;
	        option.innerHTML=styleList[i].label;
	        option.setAttribute("data-image", "ILMHighcharts/combonoxComponent/images/" + styleList[i].id + ".png");
	        if (styleList[i].id == selectedElement) {
		        option.selected="selected";
	        }
	        element.appendChild(option);
		}
	}
	
	function minimisePopup() {
		parent.getFlashObject("mySwf").minimisePopupById("analysisPopupStylePopup");
	}
	
	function saveChanges() {
		var comboboxValues =[];
		var comboboxElement = {};
		var selects = document.getElementsByTagName('select');
		var sel;
		var value;
		for(var z=0; z<selects.length; z++){
		     sel = selects[z];
		     value = sel.options[sel.selectedIndex].value;
		     comboboxElement = {id: sel.name, value: value};
		     comboboxValues.push(comboboxElement);
		}
// 		parent.getFlashObject("mySwf").saveChangesForILMStyles(false, comboboxValues, selects.length);
		window.opener.Main.saveChangesForILMStyles(selectedTab === "GlobalView", comboboxValues, selects.length);
		
		setTimeout(() => {
			window.close();
		}, 0);
	}
	
</script>
<style type="text/css">
#buttonsDiv a {
	display: block;
	text-decoration: none;
	font: 11px Verdana; /* tab font */
	color: black; /* font color */
	width: 70px; /* width of tab image */
	height: 21px; /* height of tab image */
	margin-left: 3px; /* spacing between tabs */
	margin-right: 3px;
	margin-top: 3px;
	padding-top: 3px; /* vertical offset of tab text from top of tab */
	background-image: url(images/skyButtonUp.png) !important;
	background-repeat: no-repeat;
	border: 2px;
	text-align: center;
}

#buttonsDiv a.hover {
	background-image: url(images/skyButtonOver.png) !important;
	/* URL to tab image */
	color: darkblue; /* font color */
	cursor: hand;
	cursor: pointer;
}

#buttonsDiv a.current {
	background-image: url(images/skyButtonDown.png) !important;
	/* URL to tab image */
	color: darkblue; /* font color */
	padding-left: 3px;
	padding-top: 3px; /* vertical offset of tab text from top of tab */
	cursor: hand;
	cursor: pointer;
}

#buttonsDiv a.disabled {
	background-image: url(images/skyButtonDisabled.png) !important;
	/* URL to tab image */
}

.mochaLoadingIcon {
display: none !important;
}

div[style*="outset"]{
	-moz-box-sizing: border-box !important;
	box-sizing:border-box;
	border-color: #FFFFFF !important;
	border-left-style: double !important;
	border-left-color: #FFFFFF !important;
	border-left-width: 2px !important;
	border-top-style: double !important;
	border-top-color: #FFFFFF !important;
	border-top-width: 2px !important;
	border-right-style: outset !important;
	border-right-color: #FFFFFF !important;
	border-right-width: 2px !important;
	border-bottom-style: outset !important;
	border-bottom-color: #FFFFFF !important;
	border-bottom-width: 2px !important;
}

body {
	margin-top: 25px; margin-bottom: 5px; margin-left: 10px;
} 

</style>
</head>
<body style="overflow: hidden;padding-bottom: 49px;" onLoad="bodyOnLoad();">
	<div id="ddimagebuttons" style="border:2px outset; left:20; width:100%; height:100%">
		<div style="width: 100%; height: 100%; overflow:auto;">
			<table id="comboboxesContainer">
			</table>
		</div>
	</div>
  	<div id="ddimagebuttons" style="border:2px outset;height:39px;position: fixed !important;top: auto !important;bottom: 10 !important;left:9;right:9;">
		<div id="ScenarioMaintenance" style="left:2; top:6; height:15px; visibility:visible;">
			<table width="100" border="0" cellspacing="0" cellpadding="0" height="20" style="padding-top:5px;padding-left:10px;">
				<tr>
					<td align="left" id="okButton"><a
						tabindex="1" title="OK" onMouseOut="collapsebutton(this)"
						onMouseOver="highlightbutton(this)"
						onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)"
						onclick="javascript:saveChanges();">OK</a></td>
					<td align="left" id="cancelButton"><a
					tabindex="2" title="Cancel" onMouseOut="collapsebutton(this)"
					onMouseOver="highlightbutton(this)"
					onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)"
					onclick="javascript:window.close();">Cancel</a></td>
				</tr>
			</table>
		</div>
	</div>
</body>
</html>