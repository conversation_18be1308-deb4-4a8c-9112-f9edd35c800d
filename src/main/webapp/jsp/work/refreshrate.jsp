<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%--
 	refreshrate.jsp
 	
 	<AUTHOR> R / 15-Mar-2012
 	
 	This is auto-refresh rate window. The user can change the auto 
 	refresh rate of the screen.
--%>
<%@ page import="org.swallow.util.SwtConstants" %>
<%@ taglib prefix="s" uri="/struts-tags" %>
<style type="text/css">
	#blanket {
	   background-color :#d9e0ed;
	   opacity: 0.6;
	   filter: alpha(opacity=60);
	   position: absolute;
	   z-index: 9001;
	   top: 0px;
	   left: 0px;
	   width: 100%;
	}
	
	#pnlRate {
		position: absolute;
		background-color: #D6E3FE;
		width: 300px;
		height: 300px;
		z-index: 9002;
	}
</style>
<script type="text/javascript">
	/**
	 * This function displays auto-refresh window
	 */
	function showRatePopup() {
		//Stop refresh timer. This method should be implemented in parent
		stopRefreshTimer();
		//Show popup
		togglePopup();
		// Auto refresh rate value
		document.getElementById("screenOption.propertyValue").value = refreshRate;
		document.getElementById("screenOption.propertyValue").focus();
	}
	
	/**
	 * This function displays auto-refresh window
	 */
	function closeRatePopup() {
		//Show popup
		togglePopup();
		//Start refresh timer. This method should be implemented in parent
		startRefreshTimer();
	}
	
	/**
	 * This function toggles rate popup and blanket window
	 */
	function togglePopup() {
		toggle("pnlRate");
		toggle("blanket");
	}
	
	/**
	 * This function toggles panels
	 * 
	 * @param pnlId
	 */
	function toggle(pnlId) {
		var objPanel = document.getElementById(pnlId);
		objPanel.style.display = ((objPanel.style.display == "none") ? "block" : "none")
	}
	
	/**
	 * This function validates refresh rate
	 * 
	 * @return boolean - validation result
	 */
	function validateRate() {
		//Set default result value
		var validateResult = false;
		//Get refresh rate object
		var objRefRate = document.getElementById("screenOption.propertyValue");
		//Validate whether the rate value is integer or not
		if (validateField(objRefRate, 'screenOption.propertyValue', 'numberPat')) {
			//Refresh rate value should not be less than 5
			if (objRefRate.value < 5) {
				//Entered value is below 5. So make it 5.
				var refreshRateConfirm = confirm ('<s:text name="alert.refreshRate.confirmMinRate" />')
				if (refreshRateConfirm) {
					objRefRate.value = 5;
					validateResult = true;
				}
			} else {
				validateResult = true;
			}
		} else {
			objRefRate.focus();
		}
		return validateResult;
	}
	
	
	/**
	 * This function saves auto refresh rate
	 */
	function saveRefreshRate() {
		if (validateRate()) {
			//Create AJAX request object
			oXHR = zXmlHttp.createRequest();
			//Set request properties, like request method, url, content type
			oXHR.open("POST", "screenOption.do?method=save", true);
			oXHR.setRequestHeader("Content-Type", "application/x-www-form-urlencoded");
			//Register callback method to handle response
			oXHR.onreadystatechange = saveRateCallBack;
			//Send request parameters in a body of the request as this is a POST request
			oXHR.send(getRequestBody(document.forms["frmRefreshRate"]));
		}
	}
	
	/**
	 * This is a callback function for AJAX request. It just closes 
	 * this popup when get response from server 
	 */
	function saveRateCallBack() {
		if (oXHR.readyState == 4) {
			if (oXHR.status == 200 || oXHR.status == 304) {
				//Got response, so close this popup
				togglePopup();
				//Set refresh rate
				refreshRate = document.getElementById("screenOption.propertyValue").value;
				//Refresh parent screen
				refresh();
			} else {
		    	//statusText is not always accurate
				alert(oXHR.statusText);
		    }
		}
	}
</script>
<html>
	<div id="pnlRate" style="position: absolute; left: 420px; top: 275px; width:360px; height:150px; border: 1px outset; display:none;">
		<div id="pnlRateTitle" style="position: absolute; left: 10px; top: 10px; width:300px; height:25;">
			<b><s:text name="refreshRate.title.window" /></b>
		</div>
		<form name="frmRefreshRate">
			<div id="pnlRateInput" style="position: absolute; left: 10px; top: 40px; width:340px; height:35px; border: 2px outset;">
				<table style="width: 335px;">
					<tr height="35px" valign="center">
						<td width="5px">&nbsp;</td>
						<td width="125px"><b><s:text name="autoRefreshRate" /></b></td>
						<td width="35px">
							<input type="text" name="screenOption.propertyValue" maxlength="3"
									style="width:35px;" class="htmlTextNumeric" 
									title='<s:text name="tooltip.enterRate" />'>
							<input type="hidden" name="screenOption.id.hostId" value="${requestScope.autoRefreshRate.id.hostId}" />
							<input type="hidden" name="screenOption.id.userId" value="${requestScope.autoRefreshRate.id.userId}" />
							<input type="hidden" name="screenOption.id.screenId" value="<%= SwtConstants.ACCOUNT_BREAKDOWN_MONITOR_ID %>" />
						</td>
						<td width="165px"><b>&nbsp;<s:text name="label.refreshRate.seconds" /></b></td>
					</tr>
				</table>
			</div>
		</form>
		<div id="ddimagebuttons" color="#7E97AF"
			style="position: absolute; border: 2px outset; left: 10px; top: 100px; width: 340px; height: 32px; visibility: visible;">
			<div id="btnRateSave" style="position: absolute; width: 55px; height: 15px;">
				<a title='<s:text name="tooltip.save" />'
					onMouseOut="collapsebutton(this)"
					onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)"
					onMouseUp="highlightbutton(this)" onKeyDown="submitEnter(this,event)"
					onClick="javascript:saveRefreshRate()"><s:text name="button.save" /></a>
			</div>
			<div id="btnRateClose" style="position: absolute; left: 80px;  width: 55px; height: 15px;">
				<a title='<s:text name="tooltip.close" />'
					onMouseOut="collapsebutton(this)"
					onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)"
					onMouseUp="highlightbutton(this)" onKeyDown="submitEnter(this,event)"
					onClick="javascript:closeRatePopup()"><s:text name="button.cancel" /></a>
			</div>
		</div>
	</div>
</html>