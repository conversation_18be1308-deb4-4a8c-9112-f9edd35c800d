<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@ include file="/taglib.jsp"%>
<%@ page import="org.swallow.util.SwtConstants"%>
<%@ page import="org.swallow.util.CommonDataManager"%>
<%@ page import="org.swallow.util.SwtUtil"%>
<html>
<head>
<title><s:text name="archiveSearch.title.mainWindow" /></title>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
<style type="text/css">
#partyLink {
	width:20px !important;

}</style>
<SCRIPT language="JAVASCRIPT">
var appName = "<%=SwtUtil.appName%>";
var dateSelected = false;
var initialscreen = "${requestScope.initialinputscreen}";


var extraFilter="";
var cancelcloseElements = new Array(1);
	cancelcloseElements[0]="closebutton";

var currencyFormat = '<s:property value="#request.session.CDM.currencyFormat"/>';
var dateFormat = '<s:property value="#request.session.CDM.dateFormat" />';
var sysDateAsstring = "${requestScope.sysDateAsstring}";
mandatoryFieldsArray = ["movementsearchValueFromDateAsString"] ; 
function submitForm(methodName){

	document.forms[0].method.value = methodName;
	document.forms[0].extraFilter.value = extraFilter;
	document.forms[0].submit();

	}

function getAmtValue(amt){
var amtvalue='';
 for(var idx=0; idx < amt.length; idx++){
	el = amt.charAt(idx);	
	if(el!=',' && el!='.'){	
	 amtvalue += el;
	}	 
 }
 return amtvalue; 
}

function validateAmount(amtover,amtunder){

  if(amtover !="" && amtunder !="")
    {
	var amountOver = new Number(getAmtValue(amtover)) ;
	var amountUnder = new Number(getAmtValue(amtunder)) ;	
	if(amountUnder >= amountOver){
	return true;
	} 
	else{
	return false;
	}

   }
   else{
	return true;
   }

}

function checkAccountType(methodName)
{
	enableFields();
	document.forms[0].method.value = methodName;
	
	document.forms[0].submit();

}

function enableFields(){
	document.forms[0].elements["movementsearch.id.entityId"].disabled = "";
	document.forms[0].elements['movementsearch.valueFromDateAsString'].disabled="";
	document.forms[0].elements['movementsearch.valueToDateAsString'].disabled="";
	document.forms[0].elements["movementsearch.matchStatus"].disabled = "";
	document.forms[0].elements["movementsearch.accountClass"].disabled = "";
	document.forms[0].elements['movementsearch.currencyCode'].disabled="";
	
}
function getRadioButtonValue(button){
var d = button;
var len = d.length ;
for(i=0;i<len;i++)
{
	if(d[i].checked)
	 return d[i].value;
}
if(i == len)
 return "null";
}
/**
* This method is used to open the Change Movement screen with corresponding movement details, when click the change button.
* 
* @param methodName
* @return param
*/
function getMovementDetails(methodName){
    if (document.forms[0].elements['movementsearch.valueFromDateAsString'].value != "") {	

        var retValue="true"; 

		if(document.forms[0].elements['movementsearch.archiveId'].value == "")
		{
				alert("<s:text name='alert.enterValidPageNumber'/>");
				retValue=false;
		}
	
		if(retValue=="true"){   
		 
			if(validateCurrency(document.forms[0].elements['movementsearch.amountoverasstring'],'movementsearch.movementsearch.amountoverasstring',currencyFormat))
			{
     
				if(validateCurrency(document.forms[0].elements['movementsearch.amountunderasstring'],'movementsearch.movementsearch.amountunderasstring',currencyFormat))
				{

     
   
				var amountover = document.forms[0].elements['movementsearch.amountoverasstring'].value ;
				var amountunder = document.forms[0].elements['movementsearch.amountunderasstring'].value;
				if(validateAmount(amountover,amountunder)){
				if(validateDateField(document.forms[0].elements['movementsearch.valueFromDateAsString'],'movementsearch.valuefrom',dateFormat)){	
				if( comparedates(document.forms[0].elements['movementsearch.valueFromDateAsString'].value,document.forms[0].elements['movementsearch.valueToDateAsString'].value,dateFormat,'From Date','To Date')) 
				{
					
		
					if(validateField(document.forms[0].elements['movementsearch.timefrom'],'movementsearch.timefrom','timePat'))
					{
						if(validateField(document.forms[0].elements['movementsearch.timeto'],'movementsearch.timeto','timePat'))
						{

							if(validateField(document.forms[0].elements['movementsearch.reference'],'movementsearch.reference','alphaNumPatWithoutPercentage'))
							{	
								if(validateField(document.forms[0].elements['movementsearch.productType'],'movementsearch.productType','alphaNumPatWithoutPercentage'))
								{	
									
									if(validateField(document.forms[0].elements['movementsearch.matchingParty'],'movementsearch.matchingParty','alphaNumPatWithoutPercentage'))
									{	
								
								
										elementTrim(document.forms[0]);	
										var movementType = getRadioButtonValue(document.forms[0].elements['movementsearch.movementType']);
										var sign = getRadioButtonValue(document.forms[0].elements['movementsearch.sign']);
										var predictStatus = getRadioButtonValue(document.forms[0].elements['movementsearch.predictStatus']);
				
										var extBalStatus = getRadioButtonValue(document.forms[0].elements['movementsearch.extBalStatus']);
										var currencyCode = document.forms[0].elements["movementsearch.currencyCode"].value;
				
										
										var beneficiaryId = document.forms[0].elements["movementsearch.beneficiaryId"].value;
										var custodianId = document.forms[0].elements["movementsearch.custodianId"].value;
											
										var positionlevel = document.forms[0].elements["movementsearch.positionLevelAsString"].value;
										
										var accountId = document.getElementById("accountId").value;
											
										var group = document.getElementById("groupId").value ;
											
										var metaGroup = document.getElementById("metagroupId").value;
										
										var bookCode =document.getElementById("bookId").value;
												
										var valueFromDateAsString = document.forms[0].elements['movementsearch.valueFromDateAsString'].value;
										var valueToDateAsString = document.forms[0].elements['movementsearch.valueToDateAsString'].value;
										 var timefrom = document.forms[0].elements['movementsearch.timefrom'].value; 
										var timeto = document.forms[0].elements['movementsearch.timeto'].value; 
										var reference = document.forms[0].elements['movementsearch.reference'].value;
										var messageId = document.forms[0].elements['movementsearch.messageId'].value;
										var inputDateAsString = document.forms[0].elements['movementsearch.inputDateAsString'].value;
										var counterPartyId = document.forms[0].elements['movementsearch.counterPartyId'].value;
										var matchStatus = getRadioButtonValue(document.forms[0].elements['movementsearch.matchStatus']);
										var accountClass = getRadioButtonValue(document.forms[0].elements['movementsearch.accountClass']);
										if(document.forms[0].elements['movementsearch.referenceflag'].checked == false){
											 referenceFlag = "N";
										 }
										 else{
											 referenceFlag = "Y";
										 }
								
										if (inputDateAsString != "") {
											if(validateDateField(document.forms[0].elements['movementsearch.inputDateAsString'],'movementsearch.inputdate',dateFormat)){
											} else {
												document.forms[0].elements['movementsearch.inputDateAsString'].focus();
												return false;										
												}
										}
									
										var openFlag=getRadioButtonValue(document.forms[0].elements["movementsearch.openFlag"]);
										
										var userid="<%=SwtUtil.getCurrentUserId(request.getSession())%>";
										if (currencyCode=="All")
											currencyCode="All:"+userid;
										
										var uetr = document.forms[0].elements['movementsearch.uetr'].value;
										var matchingParty = document.forms[0].elements['movementsearch.matchingParty'].value;
										var productType = document.forms[0].elements['movementsearch.productType'].value;
										var postingDateFrom = document.forms[0].elements['movementsearch.postingFromDateAsString'].value;
										var postingDateTo = document.forms[0].elements['movementsearch.postingToDateAsString'].value;
												
										var compare_date = 0;
																
										if (postingDateFrom == "" && postingDateTo == ""){	
											compare_date = 1;
										}
										else{
											if(validateDateField(document.forms[0].elements['movementsearch.postingFromDateAsString'],'fromDateAsString',dateFormat)){
											if(document.forms[0].elements['movementsearch.postingToDateAsString'].value != ""){
												if (validateDateField(document.forms[0].elements['movementsearch.postingToDateAsString'],'toDateAsString',dateFormat)){
													compare_date = comparedates(document.forms[0].elements['movementsearch.postingFromDateAsString'].value,document.forms[0].elements['movementsearch.postingToDateAsString'].value,dateFormat,'From Date','To Date');
												}
											}else if(document.forms[0].elements['movementsearch.postingToDateAsString'].value == ""){
												compare_date =1;
											}
										}
										}
										if (document.forms[0].elements['movementsearch.uetr'].value){
						                 	if(!validateUETR(document.forms[0].elements['movementsearch.uetr'].value))
						                 	return ;
						                 	} 
										if (compare_date == 1) {
											document.forms[0].method.value = methodName;
											var param = 'outstandingmovement.do?method='+methodName;
											param += '&entityId=' + document.forms[0].elements['movementsearch.id.entityId'].value;
											param += '&movementType=' + movementType ;
											param += '&sign=' + sign ;
											param += '&predictStatus=' + predictStatus ;
					
											param += '&extBalStatus=' + extBalStatus ;
											param += '&amountover=' + amountover ;
											param += '&amountunder=' + amountunder;
											param += '&currencyCode=' + currencyCode  ;
											param += '&beneficiaryId=' + beneficiaryId ;
											param += '&custodianId=' + custodianId ;
											param += '&positionlevel=' + positionlevel ;
											param += '&accountId=' + accountId ;
											param += '&group=' + group  ;
											param += '&metaGroup=' + metaGroup ;
											param += '&bookCode=' + bookCode ;
											param += '&valueFromDateAsString=' + valueFromDateAsString ;
											param += '&valueToDateAsString=' + valueToDateAsString
											 param += '&timefrom=' + timefrom  ; 
											 param += '&timeto=' + timeto ;  
											param += '&reference=' + escape(encodeURIComponent(reference));
											param += '&messageId=' + messageId ;
											param += '&inputDateAsString=' + inputDateAsString ;
											param += '&counterPartyId=' + counterPartyId ;
											param += '&matchStatus=' + matchStatus  ;
											param += '&initialinputscreen=' + initialscreen  ;
											param += '&accountClass=' + accountClass  ;
											param += '&archiveId='+ document.forms[0].elements['movementsearch.archiveId'].value;
											param += '&referenceFlag=' + referenceFlag ;
									
											param += '&uetr=' + uetr;
											param += '&matchingParty=' + matchingParty;
											param += '&productType=' + productType;
											param += '&postingDateFrom=' + postingDateFrom;
											param += '&postingDateTo=' + postingDateTo;
											param += '&extraFilter=' + extraFilter;

													param += '&openFlag=' + openFlag ;								
										
											openWindow(param,'movementsearchlistWindow','left=50,top=190,width=1280,height=685,toolbar=0, resizable=yes, scrollbars=yes,status=yes');
											setParentChildsFocus();
											return  param;
										}
									}else
									{
										document.forms[0].elements['movementsearch.matchingParty'].focus();
									}
								}else
								{
									document.forms[0].elements['movementsearch.productType'].focus();
								}
							}else
							{
								document.forms[0].elements['movementsearch.reference'].focus();
							}
						}else
						{
						document.forms[0].elements['movementsearch.timeto'].focus();
						}
					}else
					{
					document.forms[0].elements['movementsearch.timefrom'].focus();
					}		
				}
			}
			}else
			{
				alert('<s:text name="movSearch.alert.amountunder"/>') ;
				document.forms[0].elements['movementsearch.amountunderasstring'].focus();
			}
			}else
			{
			document.forms[0].elements['movementsearch.amountunderasstring'].focus();	  
			}
		}else
		{
		document.forms[0].elements['movementsearch.amountoverasstring'].focus();
	 
		}
		}
	} else {
		alert("<s:text name='alert.acctbreakdownmonitor.date'/>");
		document.forms[0].elements['movementsearch.valueFromDateAsString'].focus();
	}
}

function validateFormAmount(objForm){
  var elementsRef = new Array(2);

 
  elementsRef[0] = objForm.elements["movementsearch.amountoverasstring"];
  elementsRef[1] = objForm.elements["movementsearch.amountunderasstring"];


  if(elementsRef[0].value != "" && elementsRef[1].value != "")
{
  var amountover=parseFloat(elementsRef[0].value);
  var amountunder=parseFloat(elementsRef[1].value);
 
  if(amountover > amountunder)
	{
	  alert('<s:text name="movSearch.alert.amountoverLess"/>');
	  return false;
	}
	else
    return true;
}
else
	return true;
}



function checkDate()
{

var enteredDate = document.forms[0].elements['movementsearch.valueFromDateAsString'].value

if(enteredDate!=""){

if(compareTwoDates(sysDateAsstring,enteredDate,dateFormat)== true)
		{
		
		getMovementDetails('flex');
		}
	else 
		{
		alert('<s:text name="movSearch.alert.valueFrom"/>');
		return false;
		}
	}
else{
	document.forms[0].elements['movementsearch.valueFromDateAsString'].value = sysDateAsstring ;
	getMovementDetails('flex')
}
}

/**
* This method is used to call when load the file, set the default values.
* 
* @return none
*/
function bodyOnLoad()
{
extraFilter= "${requestScope.extraFilter}";
var entityDropBoxElement = new SwSelectBox(document.forms[0].elements["movementsearch.id.entityId"],document.getElementById("entityName"));
var archiveDropBoxElement = new SwSelectBox(document.forms[0].elements["movementsearch.archiveId"],document.getElementById("archiveName"));
var currencyCodeDropBoxElement = new SwSelectBox(document.forms[0].elements["movementsearch.currencyCode"],document.getElementById("currencyName"));

var positionLevelDropBoxElement= new SwSelectBox(document.forms[0].elements["movementsearch.positionLevelAsString"],document.getElementById("positionName")); 
var divElement = document.getElementById("dropdowndiv_5");
	var selectElement = document.forms[0].elements["movementsearch.bookCode"];
	var idElement = document.forms[0].elements["bookId"];
	var descElement = document.forms[0].elements["bookCodeName"];
	var arrowElement = document.forms[0].elements["dropdownbutton_5"];
	var idLength = 12;
	var descLength = 30;

	var bookCodeDropBoxElement = new SwMainSelectBox(divElement,selectElement,idElement,descElement,arrowElement,idLength,descLength);

	bookCodeDropBoxElement.idElementMouseDown = null;
	bookCodeDropBoxElement.idElementKeyDown = null;

	bookCodeDropBoxElement.idElement.onmousedown = null;
	bookCodeDropBoxElement.idElement.onkeydown = null;


var divElement = document.getElementById("dropdowndiv_6");
	var selectElement = document.forms[0].elements["movementsearch.group"];
	var idElement = document.forms[0].elements["groupId"];
	var descElement = document.forms[0].elements["groupName"];
	var arrowElement = document.forms[0].elements["dropdownbutton_6"];
	var idLength = 12;
	var descLength = 50;

	var groupDropBoxElement = new SwMainSelectBox(divElement,selectElement,idElement,descElement,arrowElement,idLength,descLength,"true");

	groupDropBoxElement.idElementMouseDown = null;
	groupDropBoxElement.idElementKeyDown = null;

	groupDropBoxElement.idElement.onmousedown = null;
	groupDropBoxElement.idElement.onkeydown = null;

var divElement = document.getElementById("dropdowndiv_7");
	var selectElement = document.forms[0].elements["movementsearch.metaGroup"];
	var idElement = document.forms[0].elements["metagroupId"];
	var descElement = document.forms[0].elements["metaGroupName"];
	var arrowElement = document.forms[0].elements["dropdownbutton_7"];
	var idLength = 12;
	var descLength = 50;
	var metaGroupDropBoxElement = new SwMainSelectBox(divElement,selectElement,idElement,descElement,arrowElement,idLength,descLength,"true");

    document.getElementById("metaGroupName").value=document.getElementById("metaGroupName").value.replace(new RegExp("(&nbsp;)","g")," ");
    document.getElementById("groupName").value=document.getElementById("groupName").value.replace(new RegExp("(&nbsp;)","g")," ");

	metaGroupDropBoxElement.idElementMouseDown = null;
	metaGroupDropBoxElement.idElementKeyDown = null;

	metaGroupDropBoxElement.idElement.onmousedown = null;
	metaGroupDropBoxElement.idElement.onkeydown = null;

var divElement = document.getElementById("dropdowndiv_8");
	var selectElement = document.forms[0].elements["movementsearch.accountId"];
	var idElement = document.forms[0].elements["accountId"];
	var descElement = document.forms[0].elements["Acctname"];
	var arrowElement = document.forms[0].elements["dropdownbutton_8"];
	var idLength = 12;
	var descLength = 30;
	var accountDropBoxElement = new SwMainSelectBox(divElement,selectElement,idElement,descElement,arrowElement,idLength,descLength);

	accountDropBoxElement.idElementMouseDown = null;
	accountDropBoxElement.idElementKeyDown = null;

	accountDropBoxElement.idElement.onmousedown = null;
	accountDropBoxElement.idElement.onkeydown = null;

document.forms[0].initialinputscreen.value = initialscreen ;
if(initialscreen == "matchSearch"){
	<s:if test='"searchAccFromMatch"!=#request.methodName' > 
		document.forms[0].elements["movementsearch.sign"][2].checked = true;
		document.forms[0].elements["movementsearch.predictStatus"][3].checked = true;
		document.forms[0].elements["movementsearch.movementType"][2].checked = true;
		document.forms[0].elements["movementsearch.matchStatus"][0].checked = true;
		document.forms[0].elements["movementsearch.accountClass"][5].checked = true;
	</s:if>
		<s:if test='"searchAccFromMatch"==#request.methodName' > 
			document.forms[0].elements["movementsearch.matchStatus"][0].checked = true;
	     </s:if>
			

}
else
	{

	<s:if test='"populateAcc"!=#request.methodName' > 
	
		document.forms[0].elements["movementsearch.sign"][2].checked = true;
		document.forms[0].elements["movementsearch.predictStatus"][3].checked = true;

		//default value of extbalstatus set to all. 
		document.forms[0].elements['movementsearch.extBalStatus'][2].checked = true;
		document.forms[0].elements["movementsearch.movementType"][2].checked = true;
		document.forms[0].elements["movementsearch.openFlag"][1].checked = true;
		
		 document.forms[0].elements["movementsearch.matchStatus"][8].checked = true; 
		 
		 document.forms[0].elements["movementsearch.accountClass"][5].checked = true; 
   </s:if>
	}
	

var selectedEntityId= document.forms[0].elements['movementsearch.id.entityId'].value;
if(selectedEntityId=="All"){
	//disable the party search and only allow the user to enter free text for the party field
	document.getElementById('partyLink1').disabled= "true";
	document.getElementById('partyLink2').disabled= "true";
	document.getElementById('partyLink3').disabled= "true";
	
	//disable the Book, Group, Metagroup, and Account fields
	document.forms[0].elements["bookId"].disabled= "true";
	document.forms[0].elements["groupId"].disabled= "true";
	document.forms[0].elements["metagroupId"].disabled= "true";
	document.forms[0].elements["accountId"].disabled= "true";
	document.getElementById('dropdownbutton_5').disabled= "true";
	document.getElementById('dropdownbutton_6').disabled= "true";
	document.getElementById('dropdownbutton_7').disabled= "true";
	document.getElementById('dropdownbutton_8').disabled= "true";
}


var selectedEntityId= document.forms[0].elements['movementsearch.id.entityId'].value;
if(selectedEntityId=="All"){
	//disable the party search and only allow the user to enter free text for the party field
	document.getElementById('partyLink1').disabled= "true";
	document.getElementById('partyLink2').disabled= "true";
	document.getElementById('partyLink3').disabled= "true";

	//disable the Book, Group, Metagroup, and Account fields
	document.forms[0].elements["bookId"].disabled= "true";
	document.forms[0].elements["groupId"].disabled= "true";
	document.forms[0].elements["metagroupId"].disabled= "true";
	document.forms[0].elements["accountId"].disabled= "true";
	document.getElementById('dropdownbutton_5').disabled= "true";
	document.getElementById('dropdownbutton_6').disabled= "true";
	document.getElementById('dropdownbutton_7').disabled= "true";
	document.getElementById('dropdownbutton_8').disabled= "true";
}

}


function populateDropBoxes()
{
	ShowErrMsgWindow('${actionError}');
	bodyOnLoad();
}

function comparedates(date1,date2,format,date1Caption,date2Caption)
{
	var retValue = "true";
	var strdate1 = new String(date1);
	var strdate2 = new String(date2);

	if( typeof strdate1 != 'undefined' && strdate1 != null && typeof strdate2 != 'undefined' && strdate2 != null)
	{
		strdate1 = strdate1.trim();
		strdate2 = strdate2.trim();
		if(strdate1.length > 0 && strdate2.length > 0)
		{
			if(format == "datePat1")
			{
				var parts = date1.split("/");
				date1 = new Date(0);
				date1.setFullYear(parts[2]);
				date1.setDate(parts[0]);
				date1.setMonth(parts[1] - 1);

				var parts = date2.split("/");
				date2 = new Date(0);
				date2.setFullYear(parts[2]);
				date2.setDate(parts[0]);
				date2.setMonth(parts[1] - 1);

				if(date2 < date1)
					retValue = "false";
			}
			if(format == "datePat2")
			{
				var parts = date1.split("/");

				date1 = new Date(0);
				date1.setFullYear(parts[2]);
				date1.setDate(parts[1]);
				date1.setMonth(parts[0] - 1);

				var parts = date2.split("/");
				date2 = new Date(0);
				date2.setFullYear(parts[2]);
				date2.setDate(parts[1]);
				date2.setMonth(parts[0] - 1);

				if(date2 < date1)
					retValue = "false";

			}
		}

	}
	if(retValue =="false"){
	alert('' + date2Caption + " " + '<s:text name="movSearch.alert.dateComparison"/>' + " "+ date1Caption+'');
	 return false;
	 }else{
	 return true;
	 }
}


function party(flag,elementId,elementName){
var entityId = document.forms[0].elements['movementsearch.id.entityId'].value;
var url='party.do?method=preSearchParties&entityId='+entityId;
url += '&custodianFlag='+flag;
url += '&idElementName='+elementId;
url += '&descElementName='+elementName;
openWindow(url,'SearchParty','left=50,top=190,width=509,height=579,toolbar=0,resizable=yes,scrollbars=yes','true');
}

function clearCounterPartyDesc()
{
 document.getElementById("partyName").innerText = "";

}

function clearBeneficiaryDesc()
{
 document.getElementById("partyName1").innerText = "";

}
function clearCustodianDesc()
{
 document.getElementById("partyName2").innerText = "";

}

function clearBookCodeDesc()
{
 document.getElementById("bookCodeName").innerText = "";

}
function clearGroupDesc()
{
 document.getElementById("groupName").innerText = "";

}
function clearMetaGroupDesc()
{
 document.getElementById("metaGroupName").innerText = "";

}
function clearAccountDesc()
{
 document.getElementById("Acctname").innerText = "";
}
function clearCcyDesc()
{
 document.getElementById("currencyName").innerText = "";
}



function clearPositionDesc()
{
 document.getElementById("positionName").innerText = "";
}

//  This function is called when we have 3 columns in drop down and on selection only first two columns to be displayed
function populateValues_Jsp(mainobject)
{
	var selectedIndex = mainobject.selectElement.selectedIndex;
	if(selectedIndex < 0 ) selectedIndex = 0;

	var selectedOption = mainobject.selectElement.options[selectedIndex];
	var showText = selectedOption.text ;
	showText = showText.substring(12,42);
	mainobject.idElement.value = selectedOption.value;

	mainobject.descElement.value = showText ;

	return mainobject ;
}

function validateUETR() {
	  var pattern = /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-(8|9|a|b)[0-9a-f]{3}-[0-9a-f]{12}$/;
	  if (pattern.test(document.getElementById("movementsearch.uetr").value)) {
	    return true ;
	  }
	  else
	  {
		  alert("Invalid UETR format!");
		  return false;
	  }
}

</SCRIPT>

<SCRIPT language="JAVASCRIPT">
  var dateFormatValue = '<s:property value="#request.session.CDM.dateFormatValue" />';
	var cal = new CalendarPopup("caldiv",false,"calFrame");
    cal.setCssPrefix("CAL");
	cal.offsetX = 18;
    cal.offsetY = 0;

	var cal2 = new CalendarPopup("caldiv",false,"calFrame");
    cal2.setCssPrefix("CAL");
    cal2.offsetX = 18;
    cal2.offsetY = 0;

	var cal3 = new CalendarPopup("caldiv",false,"calFrame");
    cal3.setCssPrefix("CAL");
    cal3.offsetX = 18;
    cal3.offsetY = 0;

	var cal4 = new CalendarPopup("caldiv",false,"calFrame");
    cal4.setCssPrefix("CAL");
    cal4.offsetX = 20;
    cal4.offsetY = -88;

	var cal5 = new CalendarPopup("caldiv",false,"calFrame");
    cal5.setCssPrefix("CAL");
    cal5.offsetX = 20;
    cal5.offsetY = -88;
function validateToDateField(){
	if(document.forms[0].elements['movementsearch.valueToDateAsString']!=null && document.forms[0].elements['movementsearch.valueToDateAsString'].value != ""){
		if(validateDateField(document.forms[0].elements['movementsearch.valueToDateAsString'],'movementsearch.valueto',dateFormat )){
		}
	}
}

function validateFromDateField(){
	if(document.forms[0].elements['movementsearch.valueFromDateAsString']!=null && document.forms[0].elements['movementsearch.valueFromDateAsString'].value != ""){
		if(validateDateField(document.forms[0].elements['movementsearch.valueFromDateAsString'],'movementsearch.valueFrom',dateFormat )){
		}
	}
}

function validateArchiveDateField(){
	if(document.forms[0].elements['movementsearch.inputDateAsString']!=null && document.forms[0].elements['movementsearch.inputDateAsString'].value != ""){
		if(validateDateField(document.forms[0].elements['movementsearch.inputDateAsString'],'movementsearch.inputdate',dateFormat )){
		}
	}
}

function validateForm(objForm){
  var elementsRef = new Array(1);
  elementsRef[0] = objForm.elements["movementsearch.valueFromDateAsString"];

  return validate(elementsRef);
}

function openAddColsScreen(methodName){
	var param = '/' + appName + '/outstandingmovement.do?method='+methodName;
	var mainWindow = openWindow (param, 'addColumns','left=10,top=230,width=710,height=500,toolbar=0, resizable=yes, //status=yes, scrollbars=yes');
	return false;
}

</SCRIPT>

</head>

<body leftmargin="0" topmargin="0" marginheight="0"
	onLoad="setParentChildsFocus();setFocus(document.forms[0]);populateDropBoxes();"
	onUnload="call()">



<s:form action="movementsearch.do">
	<input name="method" type="hidden" value="display">
	<input name="selectedEntityName" type="hidden" value="">
	<input name="initialinputscreen" type="hidden" value="">
    <input name="extraFilter" type="hidden" value="">
	<bean:define id="CDM" name="CDM"
		type="org.swallow.util.CommonDataManager" scope="session" />


	<!----------------------------------caldiv----------------------------------------------------------------->
	<div id="caldiv"
		style="z-index: 99; position: absolute; visibility: visible; background-color: white; layer-background-color: white;"></div>
	<iframe id="calFrame" src="javascript:false;" scrolling="no"
		frameborder="0"
		style="position: absolute; top: 0px; left: 0px; display: none;">
	</iframe>
	<!-----------------------------------end of caldiv----------------------------------------------->



	<div id="dropdowndiv_5"
		style="z-index: 99; position: absolute; width: 200px; left: 367px; top: 339px; visibility: hidden"
		class="swdropdown"><s:select cssClass="htmlTextFixed" id="movementsearch.bookCode" name="movementsearch.bookCode" size="10" cssStyle="width:329px;z-index:99;" list="#request.bookdetails" listKey="value" listValue="label" /></div>
	<div id="dropdowndiv_6"
		style="z-index: 99; position: absolute; width: 200px; left: 367px; top: 370px; visibility: hidden"
		class="swdropdown"><s:select cssClass="htmlTextFixed" id="movementsearch.group" name="movementsearch.group" size="10" cssStyle="width:473px;z-index:99;" list="#request.groupdetails" listKey="value" listValue="label" /></div>
	<div id="dropdowndiv_7"
		style="z-index: 99; position: absolute; width: 200px; left: 367px; top: 403px; visibility: hidden"
		class="swdropdown"><s:select cssClass="htmlTextFixed" id="movementsearch.metaGroup" name="movementsearch.metaGroup" size="10" cssStyle="width:473px;z-index:99;" list="#request.metagroupdetails" listKey="value" listValue="label" /></div>
	<div id="dropdowndiv_8"
		style="z-index: 99; position: absolute; width: 200px; left: 367px; top: 463px; visibility: hidden"
		class="swdropdown"><s:select cssClass="htmlTextFixed" id="movementsearch.accountId" name="movementsearch.accountId" size="10" cssStyle="width:329px;z-index:99;" list="#request.accountdetails" listKey="value" listValue="label" /></div>

	<!--------------entity drop down---------------->

	<div id="MovementSearch"
		style="position: absolute; left: 20px; top: 19px; width: 941px; height: 62px; border: 2px outset;"
		color="#7E97AF">
	<div id="MovementSearch"
		style="position: absolute; left: -5px; top: 4px; width: 780px; height: 300;">
	<table width="725" border="0" cellpadding="0" cellspacing="0"
		height="50">
		<tr>
			<td width="47"><B>&nbsp;&nbsp;&nbsp;<s:text name="movementsearch.archive" /></B></td>
			<td width="28">&nbsp;</td>
			<td width="140"><s:select titleKey="tooltip.selectArchiveId" cssClass="htmlTextAlpha" tabindex="1" id="movementsearch.archiveId" name="movementsearch.archiveId" cssStyle="width:140px" list="#request.archives" listKey="value" listValue="label" /></td>
			<td width="20">&nbsp;</td>
			<td width="490"><span id="archiveName" class="spantext"></td>
		</tr>
		<tr>
			<td width="47"><B>&nbsp;&nbsp;&nbsp;<s:text name="movementsearch.entity" /></B></td>
			<td width="28">&nbsp;</td>
			<td width="140"><s:select titleKey="tooltip.selectEntityId" cssClass="htmlTextAlpha" tabindex="2" id="movementsearch.id.entityId" name="movementsearch.id.entityId" onchange="submitForm('archiveSearch')" cssStyle="width:140px" list="#request.entities" listKey="value" listValue="label" /></td>
			<td width="20">&nbsp;</td>
			<td width="490"><span id="entityName" class="spantext"></td>
		</tr>
	</table>
	</div>
	</div>

	<!----------------end of entity drop down------------------>

	<div id="movementsearchparam"
		style="position: absolute; left: 20px; top: 85px; width: 941px; height: 542px; border: 2px outset;"
		color="#7E97AF">
	<div id="movementsearchparam"
		style="position: absolute; left: 0px; top: 0px; width: 591px; height: 38px;">


	<!--------------------start of extra fieldset------------------------------------------>
	<div style="position: absolute; left: 187px; height: 525px; top: 10px;">
	<fieldset style="width: 622px; border: 2px groove; height: 525px;">
	<div style="position: absolute; left: 8px; top: 5px;">
	<table width="569px" border="0" cellpadding="0" cellspacing="1"
		height="">
		<!------value date---->
		<tr height="27px">
			<td width="120px"><B><s:text name="movementsearch.amountover" /></B></td>
			<td width="28px">&nbsp;</td>
			<td width="160px"><s:textfield tabindex="5"
				name="movementsearch.amountoverasstring" style="width:160px;height: 22px;"
				titleKey="tooltip.amountFrom"
				onchange="return validateCurrency(this,'movementsearch.amountover',currencyFormat, document.forms[0].elements['movementsearch.currencyCode'].value)"
				cssClass="htmlTextNumeric" maxlength="28" /></td>

			<td width="20px">&nbsp;</td>
			<td width="45px"><B><s:text name="movementsearch.valueto" /></B></td>
			<td width="28px">&nbsp;</td>
			<td width="160px" height="28"><s:textfield titleKey="tooltip.amountTo" tabindex="6"
				cssClass="htmlTextNumeric"
				name="movementsearch.amountunderasstring" style="width:160px;height: 22px;"
				onchange="return validateCurrency(this,'movementsearch.amountunder',currencyFormat, document.forms[0].elements['movementsearch.currencyCode'].value)"
				maxlength="28"  /></td>
		</tr>
	</table>

	<table width="622px" border="0" cellpadding="0" cellspacing="1"
		height="80">
		<tr height="27">
			<td width="127px"><B><s:text name="movementsearch.valuefrom" />*</B></td>
			<td width="30px">&nbsp;</td>
			<td width="160px" id="movementsearchValueFromDateAsString"><s:if test='"matchSearch"!=#request.initialinputscreen' >
				<s:textfield titleKey="tooltip.enterValueDateFrom" readonly="false"
					maxlength="10" tabindex="7" cssClass="htmlTextAlpha"
					name="movementsearch.valueFromDateAsString"  style="width:80px;margin-bottom: 5px;height: 22px;"
					onchange="if(validateForm(document.forms[0]) ){validateFromDateField();}"
					onmouseout="dateSelected=false;" />
				<A title='<s:text name="tooltip.selectFromDate"/>' tabindex="8"
					name="datelink" ID="datelink"
					onClick="cal.select(document.forms[0].elements['movementsearch.valueFromDateAsString'],'datelink',dateFormatValue);dateSelected=true;return false;"><img
					src="images/calendar-16.gif"></A>
			</s:if> <s:if test='"matchSearch"==#request.initialinputscreen' >
				<s:textfield titleKey="tooltip.enterValueDateFrom" readonly="false"
					maxlength="10" tabindex="7" cssClass="htmlTextAlpha"
					name="movementsearch.valueFromDateAsString" style="width:80px;margin-bottom: 5px;height: 22px;"
					onchange="if(validateForm(document.forms[0]) ){validateFromDateField();}"
					onmouseout="dateSelected=false;" />
				<A title='<s:text name="tooltip.selectFromDate"/>' tabindex="8"
					name="datelink" ID="datelink"
					onClick="cal.select(document.forms[0].elements['movementsearch.valueFromDateAsString'],'datelink',dateFormatValue);dateSelected=true;return false;"><img
					src="images/calendar-16.gif"></A>
			</s:if></td>
			<td width="20px">&nbsp;</td>
			<td width="79px"><B><s:text name="movementsearch.valueto" /></B></td>
			<td width="220px"><s:if test='"matchSearch"!=#request.initialinputscreen' >
				<s:textfield titleKey="tooltip.enterValueDateTo" readonly="false"
					maxlength="10" tabindex="9" cssClass="htmlTextAlpha"
					name="movementsearch.valueToDateAsString"  style="width:80px;margin-bottom: 5px;height: 22px;"
					onchange="return validateField(this,'movementsearch.valueto',dateFormat);validateToDateField();"
					onmouseout="dateSelected=false;" />
				<A title='<s:text name="tooltip.selectToDate"/>' tabindex="10"
					name="datelink2" ID="datelink2"
					onClick="cal2.select(document.forms[0].elements['movementsearch.valueToDateAsString'],'datelink2',dateFormatValue);dateSelected=true;return false;"><img
					src="images/calendar-16.gif"></A>
			</s:if> <s:if test='"matchSearch"==#request.initialinputscreen' >
				<s:textfield titleKey="tooltip.enterValueDateTo" readonly="false"
					maxlength="10" tabindex="9" cssClass="htmlTextAlpha"
					name="movementsearch.valueToDateAsString"  style="width:80px;margin-bottom: 5px;height: 22px;"
					onchange="return validateField(this,'movementsearch.valueto',dateFormat);validateToDateField();"
					onmouseout="dateSelected=false;" />
				<td width="152px">&nbsp; <A
					title='<s:text name="tooltip.selectToDate"/>' tabindex="10"
					name="datelink2" ID="datelink2"
					onClick="cal2.select(document.forms[0].elements['movementsearch.valueToDateAsString'],'datelink2',dateFormatValue);dateSelected=true;return false;"><img
					src="images/calendar-16.gif"></A></td>
			</s:if></td>
		</tr>


		<!-------------currency------>
		<tr height="25">
			<td width="120px"><B><s:text name="movementsearch.currency" /></B></td>
			<td width="28px">&nbsp;</td>
			<!-- Code Modified For Mantis 2072 by Sudhakar on 31-10-2012:Currency change should not change Status and Account class to All,The parameter of submitForm method is changed as archivePopulateAcc instead of archiveSearch  -->
			<td width="140px"><s:select titleKey="tooltip.movCurrency" cssClass="htmlTextAlpha" tabindex="11" id="movementsearch.currencyCode" name="movementsearch.currencyCode" cssStyle="width:56px" onchange="submitForm('archivePopulateAcc');" list="#request.currencydetails" listKey="value" listValue="label" /></td>
			<td width="20px">&nbsp;</td>
			<td width="280px" colspan="4"><span id="currencyName"
				class="spantext"></td>
		</tr>

		<!-----Swift message type------->
		<tr height="25">
			<td width="120px"><B><s:text name="movementsearch.messageId" /></B></td>
			<td width="28px">&nbsp;</td>
			<td width="140px"><s:select titleKey="tooltip.selectMsgFormat" cssClass="htmlTextAlpha" tabindex="12" id="movementsearch.messageId" name="movementsearch.messageId" cssStyle="width:140px;" list="#request.swiftMessages" listKey="value" listValue="label" /></td>

			<td width="20px">&nbsp;</td>
			<td width="280px" colspan="4">&nbsp;</td>
		</tr>
		<div style="z-index: 99; position: absolute; left: 0px; top: 0px;">
		<table width="620px" border="0" cellpadding="0" cellspacing="1"
			height="190">

			<tr height="25">
				<td width="123px"><B><s:text name="movementsearch.counterparty" /></B></td>
				<td width="30px">&nbsp;</td>

				<td  width="150px" style="padding-left: 10px;"><s:textfield cssClass="htmlTextAlpha"
					name="movementsearch.counterPartyId"
					onchange="clearCounterPartyDesc()" tabindex="14"
					style="width:120px; margin-bottom: 2px;margin-right: 5px;height: 22px;"  titleKey="tooltip.counterId" /><input
					title='<s:text name="tooltip.clickSelCounterId"/>'
					id="partyLink1" type="button" value="..." tabindex="15"
					onClick="javascript:party('N','movementsearch.counterPartyId','partyName')">
				</td>


				<td width="47px">&nbsp;</td>

				<td width="280px"><span id="partyName" name="partyName"
					class="spantext"></td>




			</tr>

			<tr height="25">
				<td width="123px"><B><s:text name="movementsearch.beneficiary" /></B></td>
				<td width="30px">&nbsp;</td>

				<td  width="150px" style="padding-left: 10px;"><s:textfield cssClass="htmlTextAlpha"
					name="movementsearch.beneficiaryId"
					onchange="clearBeneficiaryDesc()" tabindex="16"
					style="width:120px; margin-bottom: 2px;margin-right: 5px;height: 22px;"  titleKey="tooltip.BeneficiaryID" /><input
					title='<s:text name="tooltip.movBeneficiary"/>' id="partyLink2"
					type="button" value="..." tabindex="17"
					onClick="javascript:party('N','movementsearch.beneficiaryId','partyName1')">
				</td>

				<td width="47px">&nbsp;</td>
				<td width="280px"><span id="partyName1" name="partyName1"
					class="spantext"></td>


			</tr>

			<tr height="25">
				<td width="123px"><B><s:text name="movementsearch.custodian" /></B></td>
				<td width="30px">&nbsp;</td>

				<td width="152px" style="padding-left: 10px;"><s:textfield cssClass="htmlTextAlpha"
					name="movementsearch.custodianId" tabindex="18"
					onchange="clearCustodianDesc()" style="width:120px; margin-bottom: 2px;margin-right: 5px;height: 22px;"
					titleKey="tooltip.custId" /><input
					title='<s:text name="tooltip.movCustodian"/>' id="partyLink3"
					type="button" value="..." tabindex="19" 
					onClick="javascript:party('Y','movementsearch.custodianId','partyName2')">
				</td>

				<td width="47px">&nbsp;</td>
				<td width="280px"><span id="partyName2" name="partyName2"
					class="spantext"></td>

			</tr>

			<tr height="25">
				<td width="123px"><B><s:text name="movementsearch.bookcode" /></B></td>
				<td width="28px">&nbsp;</td>
				<td width="150px" style="padding-left: 10px;"><input styleclass="htmlTextAlpha" id="bookId"
					name="bookId" style="width:120px; margin-bottom: 2px;margin-right: 5px;height: 22px;"  onchange="clearBookCodeDesc()"
					title='<s:text name="tooltip.bookId"/>' readonly="readonly" /><input
					title='<s:text name="tooltip.clickSelBookId"/>'
					id="dropdownbutton_5" type="button" style="width:20px;" tabindex="20" value="...">

				</td>
				<td width="7px">&nbsp;</td>
				<td><input styleclass="textAlpha" tabindex="-1"
					style="width: 280px; background: transparent; border: thin;"
					name="bookCodeName" size="20" readonly="true"></td>

			</tr>

			<tr height="25">
				<td width="123px"><B><s:text name="movementsearch.group" /></B></td>
				<td width="28px">&nbsp;</td>
				<td width="150px" style="padding-left: 10px;"><input styleclass="textAlpha" id="groupId"
					name="groupId" style="width:120px; margin-bottom: 2px;margin-right: 5px;height: 22px;"  onchange="clearGroupDesc()"
					title='<s:text name="tooltip.groupId"/>' readonly="readonly"><input
					title='<s:text name="tooltip.clickSelGroupId"/>'
					id="dropdownbutton_6" type="button" tabindex="21" style="width:20px;" value="..."></td>
				<td width="7px">&nbsp;</td>
				<td><input styleclass="textAlpha" tabindex="-1"
					style="width: 280px; background: transparent; border: thin;"
					name="groupName" size="20" readonly="true"></td>

			</tr>

			<tr height="25">
				<td width="123px"><B><s:text name="movementsearch.metagroup" /></B></td>
				<td width="28px">&nbsp;</td>
				<td width="150px" style="padding-left: 10px;"><input styleclass="textAlpha"
					onchange="clearMetaGroupDesc()" id="metagroupId" name="metagroupId"
					style="width:120px; margin-bottom: 2px;margin-right: 5px;height: 22px;" 
					title='<s:text name="tooltip.metaGroupId"/>' readonly="readonly"><input
					title='<s:text name="tooltip.clickSelMetaGroupId"/>'
					id="dropdownbutton_7" type="button" tabindex="22" style="width:20px;" value="..."></td>
				<td width="17px">&nbsp;</td>
				<td><input styleclass="textAlpha" tabindex="-1"
					style="width: 280px; background: transparent; border: thin;"
					name="metaGroupName" size="20" readonly="true"></td>

			</tr>
		</table>
		<table width="680px" cellspacing="0" cellpadding="0" border="0">

			<tr height="25">
				<td width="125px"><B><s:text name="movementsearch.inputdate" /></B></td>
				<td width="21px">&nbsp;</td>
				<td width="160px" style="padding-left: 7px;"><s:textfield name="movementsearch.inputDateAsString" readonly="false"
					maxlength="10" cssClass="htmlTextAlpha"
					titleKey="tooltip.enterInputDate" tabindex="23" style="width:80px; margin-bottom: 5px;height: 22px;"
					onchange="return validateField(this,'movementsearch.inputdate',dateFormat );validateArchiveDateField();"
					onmouseout="dateSelected=false;"  />&nbsp; <A
					title='<s:text name="tooltip.selectInputDate"/>' tabindex="24"
					name="datelink3" ID="datelink3"
					onClick="cal3.select(document.forms[0].elements['movementsearch.inputDateAsString'],'datelink3',dateFormatValue);dateSelected=true;return false;"><img
					src="images/calendar-16.gif"></A></td>
				<td width="80px" colspan="5">
				<td width="80px"><B><s:text name="movsearch.timefrom" /></B>
				</td>
				<td width="10px">&nbsp;</td>
				<td width="44px"><s:textfield titleKey="tooltip.enterTomeFrom"
					tabindex="25" name="movementsearch.timefrom"
					cssClass="htmlTextNumeric" style="width:44px;height: 22px;"
					onchange="return validateField(this,'movementsearch.timefrom','timePat')" />
				</td>
				<td width="10px">&nbsp;</td>
				<td width="15px"><B><s:text name="movementsearch.timeto" /></B></td>
				<td width="10px">&nbsp;</td>
				<td width="30px"><s:textfield titleKey="tooltip.enterTimeTo"
					tabindex="26" name="movementsearch.timeto" style="width:44px;height: 22px;"
					cssClass="htmlTextNumeric"
					onchange="return validateField(this,'movementsearch.timeto','timePat')" /></td>
				<td width="120px">&nbsp;</td>
				</td>
			</tr>
		</table>
		<table width="614px" border="0" cellpadding="0" cellspacing="1"
			height="">

			<tr height="25">
				<td width="150px"><B><s:text name="movementsearch.accountid" /></B></td>
				<td width="240px" style="width: 272px; padding-left: 45px;"><input styleclass="textAlpha"
					onchange="clearAccountDesc()" id="accountId" name="accountId"
					style="width:220px; margin-right: 5px;height: 22px;"
					title='<s:text name="tooltip.accountID"/>' readonly="readonly"><input
					title='<s:text name="tooltip.clickSelAcId"/>'
					id="dropdownbutton_8" type="button" tabindex="27" style="width:20px;"" value="..."></td>
				<td width="10px">&nbsp;</td>
				<td><input tabindex="-1" styleclass="textAlpha"
					style="width: 200px; background: transparent; border: thin;"
					name="Acctname" size="16" readonly="true"></td>

			</tr>
		</table>
		<table width="630px" border="0" cellpadding="0" cellspacing="1"
			height="">
			<tr height="25">
				<td width="170px"><B><s:text name="movementsearch.reference" /></B></td>
				<td width="18px">&nbsp;</td>
				<td width="450px" colspan="6"><s:textfield name="movementsearch.reference" cssClass="htmlTextAlpha"
					titleKey="tooltip.enterRef" tabindex="28" style="width:320px;height: 22px;" maxlength="35"
					onchange="return validateField(this,'movementsearch.reference','alphaNumPatWithoutPercentage');"  />
				</td>
				<td width="28px">&nbsp;</td>
				<td width="50px"><B><s:text name="movementsearch.like" /></B></td>
				<td width="0px">&nbsp;</td>
				<td width="120px"><s:checkbox name="movementsearch.referenceflag" fieldValue="N" value='%{#request.movementsearch.referenceflag == "Y"}'  cssStyle="width:13px;" titleKey="tooltip.selectReference" cssClass="htmlTextAlpha" tabindex="28"  /></td>
			</tr>
		</table>
				<table width="475px" border="0" cellpadding="0" cellspacing="1" height="">  
			<tr height="25">
				<td width="155px"><B><s:text name="movementsearch.uetr"/></B></td>
				<td width="320px">
					<s:textfield name="movementsearch.uetr" cssClass="htmlTextAlpha"
					titleKey="tooltip.movementSearch.uetr" tabindex="29" style="width:320px; height: 22px;" /> 
				</td>
			</tr>
		</table>
		<table width="402" border="0" cellpadding="0" cellspacing="1"height"">
			<tr height="25">
				<td width="100px"><B><s:text name="movementsearch.positionlevel" /></B></td>
				<td width="28px">&nbsp;</td>
				<td width="51px"><s:select cssClass="htmlTextAlpha" id="movementsearch.positionLevelAsString" name="movementsearch.positionLevelAsString" titleKey="tooltip.selectPostionLevel" tabindex="29" cssStyle="width:38px;" list="#request.collLang" listKey="value" listValue="label" /></td>
				<td width="28px">&nbsp;</td>
				<td width="133px"><span id="positionName" class="spantext"></td>
			</tr>
		</table>
		<table width="348px" border="0" cellpadding="0" cellspacing="1"
			height="">
			<tr height="25">
				<td width="115px"><B><s:text name="movementsearch.matchingParty" /></B></td>
				<td width="150px"><s:textfield name="movementsearch.matchingParty" cssClass="htmlTextAlpha"
					titleKey="tooltip.enterMatchingParty" tabindex="30"
					style="width:150px;height: 22px;"
					onchange="return validateField(this,'movementsearch.matchingParty','alphaNumPatWithoutPercentage');"  />
				</td>
			</tr>
			<tr height="25">
				<td width="115px"><B><s:text name="movementsearch.productType" /></B></td>
				<td width="150px"><s:textfield name="movementsearch.productType" cssClass="htmlTextAlpha"
					titleKey="tooltip.enterProductType" tabindex="31"
					style="width:150px;height: 22px;"
					onchange="return validateField(this,'movementsearch.productType','alphaNumPatWithoutPercentage');"  />
				</td>
			</tr>
		</table>
		<table width="540px" border="0" cellpadding="0" cellspacing="1"
			height="">
			<tr height="25">
				<td width="122px" style="padding-bottom: 5px;"><B><s:text name="movementsearch.postingDateFrom" /></B></td>
				<td width="180px" style="padding-left: 15px;"><s:textfield titleKey="tooltip.enterPostingDateFrom" maxlength="10"
					tabindex="32" readonly="false" cssClass="htmlTextAlpha"
					name="movementsearch.postingFromDateAsString"
				 style="width:80px;margin-bottom: 5px;height: 21px;"
					onchange="return validateField(this,'movementsearch.valuefrom',dateFormat);validateFromDateField();"
					onmouseout="dateSelected=false;"  /> <A
					title='<s:text name="tooltip.selectFromDate"/>' tabindex="32"
					name="datelink4" ID="datelink4"
					onClick="cal4.select(document.forms[0].elements['movementsearch.postingFromDateAsString'],'datelink4',dateFormatValue);dateSelected=true;return false;"><img
					src="images/calendar-16.gif"></A></td>
				<td width="30px"><B><s:text name="movementsearch.valueto" /></B></td>
				<td width="135px"><s:textfield titleKey="tooltip.enterPostingDateTo" maxlength="10" tabindex="32" 
					readonly="false" cssClass="htmlTextAlpha"
					name="movementsearch.postingToDateAsString"  style="width:80px;margin-bottom: 5px;height: 21px;"
					onchange="return validateField(this,'movementsearch.valueto',dateFormat);validateToDateField();"
					onmouseout="dateSelected=false;"  /> <A
					title='<s:text name="tooltip.selectToDate"/>' tabindex="32"
					name="datelink5" ID="datelink5"
					onClick="cal5.select(document.forms[0].elements['movementsearch.postingToDateAsString'],'datelink5',dateFormatValue);dateSelected=true;return false;"><img
					src="images/calendar-16.gif"></A></td>
			</tr>
		</table>
	</table>
	</div>
	</fieldset>
	</div>
	<!---------------------end of extra fieldset------------------------------------------>

	<!--------------Cash/Sec fieldset--------------------------------------------------->
	<div style="position: absolute; left: 817px; top: 110px;">
	<fieldset style="width: 113px; border: 2px groove; height: 86px;">
	<legend><s:text name="movementsearch.cash/sec" /> </legend>
	<table width="100" border="0" cellpadding="0" cellspacing="0"
		height="66">
		<tr height="22">
			<td><s:radio tabindex="34" id="43" cssStyle="width:25px" titleKey="tooltip.movType" name="movementsearch.movementType" list="#{'C':''}"  /> <label title='<s:text name="tooltip.movType"/>'
				for="43"> <s:text name="movementsearch.cash" /> </label></td>
		</tr>
		<tr height="22">
			<td><s:radio tabindex="34" cssStyle="width:25px" id="44" titleKey="tooltip.movType" name="movementsearch.movementType" list="#{'U':''}"  /> <label title='<s:text name="tooltip.movType"/>'
				for="44"> <s:text name="movementsearch.sec" /> </label></td>
		</tr>
		<tr height="22">
			<td><s:radio tabindex="34" cssStyle="width:25px" id="45" titleKey="tooltip.movType" name="movementsearch.movementType" list="#{'B':''}"  /> <label title='<s:text name="tooltip.movType"/>'
				for="45"> <s:text name="movementsearch.both" /> </label></td>
		</tr>
	</table>
	</fieldset>
	</div>
	<!------------------end of cash/sec fieldset--------------------------------------->


	<!----------------------Debit/Credit fieldset--------------------------->

	<div style="position: absolute; left: 817px; top: 5px;">
	<fieldset style="width: 113px; border: 2px groove; height: 80px;">
	<legend><s:text name="movementsearch.debit/credit" /> </legend>
	<table width="80" border="0" cellpadding="0" cellspacing="1"
		height="30">
		<tr height="10">
			<td><s:radio tabindex="33" cssStyle="width:25px" id="40" titleKey="tooltip.movSign" name="movementsearch.sign" list="#{'D':''}"  />
			<label title='<s:text name="tooltip.movSign"/>' for="40">
			<s:text name="movementsearch.debit" /> </label></td>
		</tr>
		<tr height="10">
			<td><s:radio tabindex="33" cssStyle="width:25px" id="41" titleKey="tooltip.movSign" name="movementsearch.sign" list="#{'C':''}"  />
			<label title='<s:text name="tooltip.movSign"/>' for="41">
			<s:text name="movementsearch.credit" /> </label></td>
		</tr>
		<tr height="10">
			<td><s:radio tabindex="33" cssStyle="width:25px" id="42" titleKey="tooltip.movSign" name="movementsearch.sign" list="#{'B':''}"  />
			<label title='<s:text name="tooltip.movSign"/>' for="42">
			<s:text name="movementsearch.both" /> </label></td>
		</tr>
	</table>
	</fieldset>
	</div>
	<!----------------------end of debit/credit fieldset------------------->

	<!-----------------------Predict status fieldset---------------------->

	<div style="position: absolute; left: 817px; top: 225px;">
	<fieldset style="width: 113px; border: 2px groove; height: 107px;">

	<legend><s:text name="movementsearch.predictstatus" /> </legend>
	<table width="100" border="0" cellpadding="0" cellspacing="0"
		height="88">
		<tr height="22">
			<td><s:radio tabindex="35" cssStyle="width:25px" id="46" titleKey="tooltip.movPredict" name="movementsearch.predictStatus" list="#{'I':''}"  /> <label
				title='<s:text name="tooltip.movPredict"/>' for="46"> <s:text name="movementsearch.included" /> </label></td>
		</tr>
		<tr height="22">
			<td><s:radio tabindex="35" cssStyle="width:25px" id="47" titleKey="tooltip.movPredict" name="movementsearch.predictStatus" list="#{'E':''}"  /> <label
				title='<s:text name="tooltip.movPredict"/>' for="47"> <s:text name="movementsearch.excluded" /> </label></td>
		</tr>
		<tr height="22">
			<td><s:radio tabindex="35" cssStyle="width:25px" id="48" titleKey="tooltip.movPredict" name="movementsearch.predictStatus" list="#{'C':''}"  /> <label
				title='<s:text name="tooltip.movPredict"/>' for="48"> <s:text name="movementsearch.cancelled" /> </label></td>
		</tr>
		<tr height="22">
			<td><s:radio tabindex="35" cssStyle="width:25px" id="49" titleKey="tooltip.movPredict" name="movementsearch.predictStatus" list="#{'A':''}"  /> <label
				title='<s:text name="tooltip.movPredict"/>' for="49"> <s:text name="movementsearch.all" /> </label></td>
		</tr>
	</table>
	</fieldset>
	</div>
	<!------------------------end of predict status fieldset--------------->

	<!-----------------------Start External Balance status fieldset---------------------->

	<div style="position: absolute; left: 817px; top: 355px;">
	<fieldset style="width: 113px; border: 2px groove; height: 92px;">

	<legend><s:text name="label.movementsearch.externalBalanceStatus" /> </legend>
	<table width="100" border="0" cellpadding="0" cellspacing="0"
		height="66">
		<tr height="22">
			<td><s:radio tabindex="36" cssStyle="width:25px" id="50" titleKey="tooltip.movExternalBalanceStatus" name="movementsearch.extBalStatus" list="#{'I':''}"  /> <label
				title='<s:text name="tooltip.movExternalBalanceStatus"/>'
				for="50"> <s:text name="movementsearch.included" /> </label></td>
		</tr>
		<tr height="22">
			<td><s:radio tabindex="36" cssStyle="width:25px" id="51" titleKey="tooltip.movExternalBalanceStatus" name="movementsearch.extBalStatus" list="#{'E':''}"  /> <label
				title='<s:text name="tooltip.movExternalBalanceStatus"/>'
				for="51"> <s:text name="movementsearch.excluded" /> </label></td>
		</tr>
		<tr height="22">
			<td><s:radio tabindex="36" cssStyle="width:25px" id="52" titleKey="tooltip.movExternalBalanceStatus" name="movementsearch.extBalStatus" list="#{'A':''}"  /> <label
				title='<s:text name="tooltip.movExternalBalanceStatus"/>'
				for="52"> <s:text name="movementsearch.all" /> </label></td>
		</tr>
	</table>
	</fieldset>
	</div>
	<!------------------------End External Balance status fieldset--------------->

	<!--------------open movements fieldset------------>
	<div style="position: absolute; left: 817px; top: 470px;">
	<fieldset style="width: 113px; border: 2px groove; height: 65px;">

	<legend><s:text name="movementsearch.open" /> </legend>
	<table width="100" border="0" cellpadding="0" cellspacing="0"
		height="47">
		<tr height="22">
			<td><s:radio tabindex="37" cssStyle="width:25px" id="53" titleKey="tooltip.open" name="movementsearch.openFlag" list="#{'O':''}"  />
			<label title='<s:text name="tooltip.open"/>' for="53"> <s:text name="movementsearch.open" /> </label></td>
		</tr>
		<tr height="22">
			<td><s:radio tabindex="37" cssStyle="width:25px" id="54" titleKey="tooltip.open" name="movementsearch.openFlag" list="#{'A':''}"  />
			<label title='<s:text name="tooltip.open"/>' for="54"> <s:text name="movementsearch.all" /> </label></td>
		</tr>
	</table>
	</fieldset>
	</div>
	<!--------------End open movements fieldset ------------> <!----------------------second fieldset--------------------------------------------------->
	<div style="position: absolute; left: 8px; top: 6px;">
	<fieldset style="width: 125px; border: 2px groove; height: 143px;">
	<legend><s:text name="movementsearch.status" /> </legend>
	<table width="170" border="0" cellpadding="0" cellspacing="2"
		height="154">
		<tr height="22">
			<!-- Authorise and Referred added into match status -->
			<td><s:if test='"matchSearch"!=#request.initialinputscreen' >
				<s:radio tabindex="3" id="3" titleKey="tooltip.movStatus" name="movementsearch.matchStatus" list="#{'A':''}"  />
			</s:if> <s:if test='"matchSearch"==#request.initialinputscreen' >
				<s:radio tabindex="3" id="3" titleKey="tooltip.movStatus" name="movementsearch.matchStatus" list="#{'A':''}" disabled="true"  />
			</s:if> <label title='<s:text name="tooltip.movStatus"/>' for="3">
			<s:text name="movementsearch.status.authorise" /> </label></td>
		</tr>
		<tr height="22">
			<!-- Authorise and Referred added into match status -->
			<td><s:if test='"matchSearch"!=#request.initialinputscreen' >
				<s:radio tabindex="3" id="5" titleKey="tooltip.movStatus" name="movementsearch.matchStatus" list="#{'R':''}"  />
			</s:if> <s:if test='"matchSearch"==#request.initialinputscreen' >
				<s:radio tabindex="3" id="5" titleKey="tooltip.movStatus" name="movementsearch.matchStatus" list="#{'R':''}" disabled="true"  />
			</s:if> <label title='<s:text name="tooltip.movStatus"/>' for="5">
			<s:text name="movementsearch.status.referred" /> </label></td>
		</tr>

		<td><s:if test='"matchSearch"!=#request.initialinputscreen' >
			<s:radio tabindex="3" id="6" titleKey="tooltip.movStatus" name="movementsearch.matchStatus" list="#{'L':''}"  />
		</s:if> <s:if test='"matchSearch"==#request.initialinputscreen' >
			<s:radio tabindex="3" id="6" titleKey="tooltip.movStatus" name="movementsearch.matchStatus" list="#{'L':''}" disabled="true"  />
		</s:if> <label title='<s:text name="tooltip.movStatus"/>' for="6">
		<s:text name="movementsearch.outstanding" /> </label></td>
		</tr>
		<tr height="22">
			<td><s:if test='"matchSearch"!=#request.initialinputscreen' >
				<s:radio tabindex="3" id="7" titleKey="tooltip.movStatus" name="movementsearch.matchStatus" list="#{'M':''}"  />
			</s:if> <s:if test='"matchSearch"==#request.initialinputscreen' >
				<s:radio tabindex="3" id="7" titleKey="tooltip.movStatus" name="movementsearch.matchStatus" list="#{'M':''}" disabled="true"  />
			</s:if> <label title='<s:text name="tooltip.movStatus"/>' for="7">
			<s:text name="movementsearch.offered" /> </label></td>
		</tr>
		<tr height="22">
			<td><s:if test='"matchSearch"!=#request.initialinputscreen' >
				<s:radio tabindex="3" id="8" titleKey="tooltip.movStatus" name="movementsearch.matchStatus" list="#{'S':''}"  />
			</s:if> <s:if test='"matchSearch"==#request.initialinputscreen' >
				<s:radio tabindex="3" id="8" titleKey="tooltip.movStatus" name="movementsearch.matchStatus" list="#{'S':''}" disabled="true"  />
			</s:if> <label title='<s:text name="tooltip.movStatus"/>' for="8">
			<s:text name="movementsearch.suspended" /> </label></td>
		</tr>
		<tr height="22">
			<td><s:if test='"matchSearch"!=#request.initialinputscreen' >
				<s:radio tabindex="3" id="9" titleKey="tooltip.movStatus" name="movementsearch.matchStatus" list="#{'C':''}"  />
			</s:if> <s:if test='"matchSearch"==#request.initialinputscreen' >
				<s:radio tabindex="3" id="9" titleKey="tooltip.movStatus" name="movementsearch.matchStatus" list="#{'C':''}" disabled="true"  />
			</s:if> <label title='<s:text name="tooltip.movStatus"/>' for="9">
			<s:text name="movementsearch.confirmed" /> </label></td>
		</tr>
		<!-- Changed Review filed into Reconcilled -->
		<tr height="22">
			<td><s:if test='"matchSearch"!=#request.initialinputscreen' >
				<s:radio tabindex="3" id="10" titleKey="tooltip.movStatus" name="movementsearch.matchStatus" list="#{'E':''}"  />
			</s:if> <s:if test='"matchSearch"==#request.initialinputscreen' >
				<s:radio tabindex="3" id="10" titleKey="tooltip.movStatus" name="movementsearch.matchStatus" list="#{'E':''}" disabled="true"  />
			</s:if> <label
				title='<s:text name="movementsearch.status.reconcilled"/>'
				for="10"> <s:text name="movementsearch.status.reconcilled" /> </label></td>
		</tr>
		<tr height="22">
			<td><s:if test='"matchSearch"!=#request.initialinputscreen' >
				<s:radio tabindex="3" id="11" titleKey="tooltip.movStatus" name="movementsearch.matchStatus" list="#{'D':''}"  />
			</s:if> <s:if test='"matchSearch"==#request.initialinputscreen' >
				<s:radio tabindex="3" id="11" titleKey="tooltip.movStatus" name="movementsearch.matchStatus" list="#{'D':''}" disabled="true"  />
			</s:if> <label title='<s:text name="tooltip.movStatus"/>' for="11">
			<s:text name="movementsearch.allmatched" /> </label></td>
		</tr>
		<tr height="22">
			<td><s:if test='"matchSearch"!=#request.initialinputscreen' >
				<s:radio tabindex="3" id="12" titleKey="tooltip.movStatus" name="movementsearch.matchStatus" list="#{'X':''}"  />
			</s:if> <s:if test='"matchSearch"==#request.initialinputscreen' >
				<s:radio tabindex="3" id="12" titleKey="tooltip.movStatus" name="movementsearch.matchStatus" list="#{'X':''}" disabled="true"  />
			</s:if> <label title='<s:text name="tooltip.movStatus"/>' for="12">
			<s:text name="movementsearch.allstatuses" /> </label></td>
		</tr>
	</table>
	</fieldset>
	</div>
	<!-------------------------end of second fieldset--------------------------------------->

	<!------------------------- Account Class fieldset added----------------------------------->

	<div style="position: absolute; left: 8px; top: 290px;">
	<fieldset style="width: 125px; border: 2px groove; height: 120px;">
	<legend><s:text name="movementsearch.account.fielset" /> </legend>
	<table width="170" border="0" cellpadding="0" cellspacing="2"
		height="88">
		<tr height="22">

			<td><s:if test='"matchSearch"!=#request.initialinputscreen' >
				<s:radio tabindex="4" id="13" titleKey="tooltip.accountClass" name="movementsearch.accountClass" list="#{'N':''}" onclick="checkAccountType('archivePopulateAcc')"  />
			</s:if> <s:if test='"matchSearch"==#request.initialinputscreen' >
				<s:radio tabindex="4" id="13" titleKey="tooltip.accountclass" name="movementsearch.accountClass" list="#{'N':''}" onclick="checkAccountType('searchAccFromMatch')"  />
			</s:if> <label title='<s:text name="movementsearch.account.nostro"/>'
				for="13"> <s:text name="movementsearch.account.nostro" />
			</label></td>
		</tr>

		<tr height="22">

			<td><s:if test='"matchSearch"!=#request.initialinputscreen' >
				<s:radio tabindex="4" id="14" titleKey="tooltip.accountClass" name="movementsearch.accountClass" list="#{'C':''}" onclick="checkAccountType('archivePopulateAcc')"  />
			</s:if> <s:if test='"matchSearch"==#request.initialinputscreen' >
				<s:radio tabindex="4" id="14" titleKey="tooltip.accountclass" name="movementsearch.accountClass" list="#{'C':''}" onclick="checkAccountType('searchAccFromMatch')"  />
			</s:if> <label title='<s:text name="movementsearch.account.current"/>'
				for="14"> <s:text name="movementsearch.account.current" />
			</label></td>
		</tr>
		<tr height="22px">

			<td><s:if test='"matchSearch"!=#request.initialinputscreen' >
				<s:radio tabindex="4" id="15" titleKey="tooltip.accountClass" name="movementsearch.accountClass" list="#{'L':''}" onclick="checkAccountType('archivePopulateAcc')"  />
			</s:if> <s:if test='"matchSearch"==#request.initialinputscreen' >
				<s:radio tabindex="4" id="15" titleKey="tooltip.accountClass" name="movementsearch.accountClass" list="#{'L':''}" onclick="checkAccountType('searchAccFromMatch')"  />
			</s:if> <label title='<s:text name="movementsearch.account.loro"/>'
				for="15"> <s:text name="movementsearch.account.loro" />
			</label></td>
		</tr>
		<td><s:if test='"matchSearch"!=#request.initialinputscreen' >
			<s:radio tabindex="4" id="16" titleKey="tooltip.accountClass" name="movementsearch.accountClass" list="#{'E':''}" onclick="checkAccountType('archivePopulateAcc')"  />
		</s:if> <s:if test='"matchSearch"==#request.initialinputscreen' >
			<s:radio tabindex="4" id="16" titleKey="tooltip.accountClass" name="movementsearch.accountClass" list="#{'E':''}" onclick="checkAccountType('searchAccFromMatch')"  />
		</s:if> <label title='<s:text name="acc.netting"/>' for="16"> <s:text name="acc.netting" /> </label></td>
		</tr>
		<tr height="22">
			<td><s:if test='"matchSearch"!=#request.initialinputscreen' >
				<s:radio tabindex="4" id="17" titleKey="tooltip.accountClass" name="movementsearch.accountClass" list="#{'O':''}" onclick="checkAccountType('archivePopulateAcc')"  />
			</s:if> <label title='<s:text name="tooltip.accountClass"/>' for="17">
			<s:text name="movementsearch.account.other" /> </label></td>
		</tr>
		<tr height="22">
			<td><s:if test='"matchSearch"!=#request.initialinputscreen' >
				<!-- Code Modified For Mantis 2072 by Sudhakar on 31-10-2012:Currency change should not change Status and Account class to All,The onlick  method is changed -->
				<s:radio tabindex="4" id="18" titleKey="tooltip.accountClass" name="movementsearch.accountClass" list="#{'All':''}" onclick="checkAccountType('archivePopulateAcc')"  />
			</s:if> <label title='<s:text name="tooltip.accountClass"/>' for="18">
			<s:text name="qualityTab.all" /> </label></td>
		</tr>



	</table>
	</fieldset>
	</div>
	<!------------------ End of Account class fieldset--------------------------------------------------------->
	
			
		
		<!------------------ Start of additional search criteria button--------------------------------------------------------->
		<div id="ddimagebuttons" color="#7E97AF" style="position:absolute; left:8px; top:460px; visibility:visible;">
		<fieldset style="width:175px;border:2px groove;height:140px;">
		<legend><s:text name="movementsearch.criteria.fielset"/></legend>
		<table width="60px"  height= "50px" border="0" cellspacing="0" cellpadding="0" height="20">
		   <tr>
		
		    <td width="30px">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</td>
			<td width="70" id="advacedButton">		
				<a title='<s:text name="tooltip.criteria"/>' tabindex="53" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onKeyDown="submitEnter(this,event)" onClick="openAddColsScreen('openAddColsScreen');"><s:text name="button.search.criteria"/></a>			
			</td>
			
			</tr>
		</table>
		</fieldset>	
	   </div>
	   <!------------------ End of additional search criteria button--------------------------------------------------------->
	
	</div>
	</div>

	<!-----------------------Print button------------------------->
	<div id="MovementSearch"
		style="position: absolute; left: 880; top: 640px; width: 70; height: 39px; visibility: visible;">
	<table width="60px" border="0" cellspacing="0" cellpadding="0"
		height="20">
		<tr>

			<td align="Right"><a tabindex="55" href=#
				onKeyDown="submitEnter(this,event)"
				onClick="javascript:openWindow(buildPrintURL('print','Archieve Search '),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')"
				onMouseOut="MM_swapImgRestore()"
				onMouseOver="MM_swapImage('Help','','images/help_default.GIF ',1)"><img
				src="images/help_default.GIF " name="Help" border="0"
				title='<s:text name="tooltip.help"/>'></a></td>

			<td align="right" id="Print"><a tabindex="55"
				onKeyDown="submitEnter(this,event)" onClick="printPage();"
				onMouseOut="MM_swapImgRestore()"
				onMouseOver="MM_swapImage('Print','','images/Print_R.gif ',1)"><img
				src="images/Print.gif " name="Print" border="0"
				title='<s:text name="tooltip.printScreen"/>'</a></td>
		</tr>
	</table>
	</div>
		
	<!--------------------------end of print button-------------------------->


	<!-----------------Ok and close buttons-------------------------------->

	<div id="ddimagebuttons" color="#7E97AF"
		style="position: absolute; border: 2px outset; left: 20; top: 631; width: 941px; height: 39px; visibility: visible;">
	<div id="MovementSearchParam"
		style="position: absolute; left: 6; top: 4; width: 810; height: 15px; visibility: visible;">
	<table width="140" border="0" cellspacing="0" cellpadding="0"
		height="20">
		<tr>
			<td width="70" id="okbutton"><s:if test='"matchSearch"!=#request.initialinputscreen' >
				<a title='<s:text name="tooltip.executeSearch"/>' tabindex="53"
					onMouseOut="collapsebutton(this)"
					onMouseOver="highlightbutton(this)"
					onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)"
					onKeyDown="submitEnter(this,event)"
					onClick="javascript:getMovementDetails('flex')"><s:text name="button.search" /></a>
			</s:if> <s:if test='"matchSearch"==#request.initialinputscreen' >
				<a title='<s:text name="tooltip.executeSearch"/>' tabindex="53"
					onMouseOut="collapsebutton(this)"
					onMouseOver="highlightbutton(this)"
					onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)"
					onClick="javascript:checkDate()"><s:text name="button.search" /></a>
			</s:if></td>
			<td width="70" id="closebutton"><a
				title='<s:text name="tooltip.close"/>' tabindex="54"
				onMouseOut="collapsebutton(this)"
				onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)"
				onMouseUp="highlightbutton(this)" onKeyDown="submitEnter(this,event)"
				onClick="confirmClose('P');"><s:text name="button.close" /></a>
			</td>
		</tr>
	</table>
	</div>
	<div><!-----------------------end buttons---------------------------------->
</s:form>
</body>
</html>