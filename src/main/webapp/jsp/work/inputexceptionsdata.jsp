<?xml version="1.0" encoding="UTF-8"?>

<%@ page contentType="text/xml" %>
<%@ page import="org.swallow.util.CommonDataManager"%>
<%@ taglib prefix="s" uri="/struts-tags" %>

<s:set var="recordCount" value="#request.result.size()" />
<%@ page import="org.swallow.util.SwtConstants"  %>


<inputexceptions 
    from="<s:property value='#request.fromDate' />"
    refresh="<s:property value='#request.autoRefreshRate' />"
    to="<s:property value='#request.toDate' />"
    dateformat="<s:property value='#request.session.CDM.dateFormatValue' />"
    dateComparing="<%if ( SwtConstants.YES.equals(request.getAttribute("dateComparing"))) {	%>true<%} else {%>false<%}%>"
    sysDateFrmSession="<s:property value='#request.sysDateFrmSession' />"
    lastRefTime="<s:property value='#request.lastRefTime' />"
    sessionToDate="<s:property value='#request.sessionToDate' />"
>

	<request_reply>
		<status_ok><s:property value="#request.reply_status_ok" /></status_ok>
		<message><s:property value="#request.reply_message" /></message>
		<location />
	</request_reply>

 <timing>
        <s:iterator value="#request.opTimes" var="opTime">
            <operation id="${opTime.key}"><s:property value="#opTime.value" /></operation>
        </s:iterator>
    </timing>

	<grid>
		<metadata>
		    <columns>
		        <!-- columns is an ordered collection -->
		        <column 
		            heading="<s:text name='inputexceptions.header.interface' />"
		            draggable="false"                    
		            filterable="false"
		            sort="true"
		            type="str"
		            dataelement="interface"
		            width="<s:property value='#request.column_width.interface' />"
		        />
		        <column 
		            heading="<s:text name='inputexceptions.header.awaiting' />"
		            draggable="false"                    
		            filterable="false"
		            sort="true"
		            type="num"
		            dataelement="awaiting"
		            width="<s:property value='#request.column_width.awaiting' />"
		        />
		        <column 
		            heading="<s:text name='inputexceptions.header.accepted' />"
		            draggable="false"                    
		            filterable="false"
		            sort="true"
		            type="num"
		            dataelement="accepted"
		            width="<s:property value='#request.column_width.accepted' />"
		        />
		        <column 
		            heading="<s:text name='inputexceptions.header.rejected' />"
		            draggable="false"                    
		            filterable="false"
		            sort="true"
		            type="num"
		            dataelement="rejected"
		            width="<s:property value='#request.column_width.rejected' />"
		        />
		        <column 
		            heading="<s:text name='inputexceptions.header.submitted' />"
		            draggable="false"                    
		            filterable="false"
		            sort="true"
		            type="num"
		            dataelement="submitted"
		            width="<s:property value='#request.column_width.submitted' />"
		        />
		        <column 
		            heading="<s:text name='inputexceptions.header.suppressed' />"
		            draggable="false"                    
		            filterable="false"
		            sort="true"
		            type="num"
		            dataelement="supressed"
		            width="<s:property value='#request.column_width.supressed' />"
		        />
		        <s:if test='#request.fromPCM == "yes"'> 
		            <column 
		                heading="<s:text name='inputexceptions.header.repair' />"
		                draggable="false"                    
		                filterable="false"
		                sort="true"
		                type="num"
		                dataelement="repair"
		                width="<s:property value='#request.column_width.repair' />"
		            />
		        </s:if> 
		    </columns>
		</metadata>

		<rows size="<s:property value='#request.recordCount' />">
    	<s:iterator value="#request.result" var="record">
	         <s:set var="value" value="#record.value" />
	        <row>
	            <interface clickable="false"><s:property value="#record.key" /></interface>
	            <accepted status="3" clickable="<s:if test='#value["3"] == "0"'>false</s:if><s:else>true</s:else>"><s:property value='#value["3"]' /></accepted>
	            <submitted status="7" clickable="<s:if test='#value["7"] == "0"'>false</s:if><s:else>true</s:else>"><s:property value='#value["7"]' /></submitted>
	            <supressed status="9" clickable="<s:if test='#value["9"] == "0"'>false</s:if><s:else>true</s:else>"><s:property value='#value["9"]' /></supressed>
	            <rejected status="4" clickable="<s:if test='#value["4"] == "0"'>false</s:if><s:else>true</s:else>"><s:property value='#value["4"]' /></rejected>
	            <!-- Start: code added by venkat on 20-Oct-2011 for mantis 1446(1053_STL_090). -->
	            <awaiting status="1" clickable="false"><s:property value='#value["1"]' /></awaiting><!-- End: code added by venkat on 20-Oct-2011 for mantis 1446(1053_STL_090). -->
	            <repair status="10" clickable="<s:if test='#value["10"] == "0"'>false</s:if><s:else>true</s:else>"><s:property value='#value["10"]' /></repair>
	        </row>
	    </s:iterator>
	</rows>

		<totals />
	</grid>
</inputexceptions>