<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@ include file="/taglib.jsp"%>
<html>
<head>
<style type="text/css">
/* img { */
/*     max-width: 100%; */
/*     max-height: 100%; */
/* } */


</style>
<title><s:text name="sweepDetails.title.window" /></title>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
<script type="text/javascript" src="js/datavalidation.js"></script>
<SCRIPT language="JAVASCRIPT">


<% 
boolean valEqualsOne = false;
%>
<s:if test='"org"==#request.sweep' >
valEqualsOne = true;
</s:if>
<s:if test='"auth"==#request.sweep' >
valEqualsOne = true;
</s:if>
<s:if test='"submit"==#request.sweep' >
valEqualsOne = true;
</s:if>



var arrowflag=false;
var checkflag=false;
var globalAmtAsString;
var globalAmt;
var globalSweep;
var cancelcloseElements = new Array(1);
cancelcloseElements[0] = "cancelbutton";


 
var currencyFormat = '<s:property value="#request.session.CDM.currencyFormat"/>';
var sweepLevel = "${requestScope.sweep}";
var sweepId = "${requestScope.sweepId}";
var newSweepId = "${requestScope.sweepIdGenerated}";
var currencyAccessInd="${requestScope.currGrpAccess}";
var parentMethod = "${requestScope.parentMethod}";

var bookCodeMain ="${requestScope.bookcodeFirst}"; //document.forms[0].elements["sweepAccount1.bookcode"].value;
var bookCodeSecond ="${requestScope.bookCodeSecond}"; //document.forms[0].elements["sweepAccount1.bookcode"].value;

var isManualSweeping = (sweepLevel=="org" && parentMethod == "manualsweeping");

var scheduleSweepExist = false;
var scheduleSweepApplyed = false;
var lastSelectedScheduleSweepOption = "";
var scheduleSweepDetailsAsString = "";


function CheckBoxRadio(checkBoxColl)
{
this.checkBoxColl = checkBoxColl;
	var count = this.checkBoxColl.length;
	for(var idx = 0 ; idx < count ; ++idx)
	{
		this.checkBoxColl[idx].onclick = this.onClickCheckBoxRadio;
		this.checkBoxColl[idx].parent = this;
	}

}

function validateThis(object,amnt,format,value){

	
	checkflag=true;
	var emptyFlag = false;
	
	if(sweepLevel=="org"){
		if(document.forms[0].elements['sweepAmount.origSweepAmtAsString'].value=="")	
		{
			emptyFlag = true;
		}
	
	}else if(sweepLevel=="submit"){
		if(document.forms[0].elements['sweepAmount.subSweepAmtAsString'].value=="")	
		{
			emptyFlag = true;
		}
	
	}else {
		if(document.forms[0].elements['sweepAmount.authSweepAmtAsString'].value=="")	
		{
			emptyFlag = true;
		}
	
	}
	
	if(emptyFlag != true)	
	{
	
	if(validateCurrency(object,amnt,format,value)){		
	
			changeAmount(object);
		}
	}
	
	
}
/**
 *
 *This method is used to change the sweep arrow direction of an account Credit to Debit vice versa.
 */
function sweepArrow(){
	//Set the arrowflag,checkflag value 
	arrowflag =true;
	checkflag=false;
	if(!(document.forms[0].elements[ "sweepAccount1.alignToTarget"].disabled==true&&document.forms[0].elements[ "sweepAccount2.alignToTarget"].disabled==true)){
		document.forms[0].elements[ "sweepAccount1.alignToTarget"].disabled = false;
		document.forms[0].elements[ "sweepAccount2.alignToTarget"].disabled = false;
		document.forms[0].elements[ "sweepAccount2.alignToTarget"].checked = false;
		document.forms[0].elements[ "sweepAccount1.alignToTarget"].checked = false;
		//Change the credit direction and message type based on  alignedSign of an account
		
		if (document.forms[0].elements["sweepAccount1.alignedSign"].value=="Credit"){
			document.forms[0].elements['sweepAmount.origSweepAmtAsString'].value="0.0";	
			validateThis(document.forms[0].elements['sweepAmount.origSweepAmtAsString'],'orgAmt',currencyFormat,document.forms[0].elements['sweepAccount1.currCode'].value);
			document.getElementById("arrowbuttonimage").src = "images/rightarrowred.GIF";
			document.forms[0].elements["sweepAccount2.alignedSign"].value="Credit";
			document.forms[0].elements["sweepAccount1.alignedSign"].value="Debit";
			/* Start:Code modified by sudhakar For Mantis 1838:Message format not displayed accordingly when sweep direction changed*/
			document.forms[0].elements["sweepAccount1.message"].value=document.forms[0].elements["sweepAccount1.messageTypeDr"].value;
			document.forms[0].elements["sweepAccount2.message"].value=document.forms[0].elements["sweepAccount2.messageTypeCr"].value;
			
			var authFlagDrValue = document.forms[0].elements["sweepAccount1.authFlagDr"].value;
			var authFlagCrValue = document.forms[0].elements["sweepAccount2.authFlagCr"].value;
			
			if (authFlagDrValue !== null && authFlagDrValue !== undefined && authFlagDrValue !== '') {
			  document.forms[0].elements["sweepAccount1.authQueue"].value = authFlagDrValue;
			}
			
			if (authFlagCrValue !== null && authFlagCrValue !== undefined && authFlagCrValue !== '') {
			  document.forms[0].elements["sweepAccount2.authQueue"].value = authFlagCrValue;
			}
			
			//Assign tooltip messageType form Account1,Account2
			document.getElementById("messageTypeAccount1").title=document.forms[0].elements["sweepAccount1.messageTypeDr"].value;
			document.getElementById("messageTypeAccount2").title=document.forms[0].elements["sweepAccount2.messageTypeCr"].value;
			
			document.forms[0].elements["sweepAccount1.defaultSettleMethod"].value=document.forms[0].elements["sweepAccount1.defaultSettleMethodDr"].value;
			document.forms[0].elements["sweepAccount2.defaultSettleMethod"].value=document.forms[0].elements["sweepAccount2.defaultSettleMethodCr"].value;
			
			document.forms[0].elements["sweepAccount1.bookcode"].value=document.forms[0].elements["sweepAccount1.bookcodeDr"].value;
			document.forms[0].elements["sweepAccount2.bookcode"].value=document.forms[0].elements["sweepAccount2.bookcodeCr"].value;
			
			$('#bookCodeId').val(document.forms[0].elements["sweepAccount1.bookcodeDr"].value);
			$('#bookCodeIdOther').val(document.forms[0].elements["sweepAccount2.bookcodeCr"].value);	
		}
		else{		
			document.forms[0].elements['sweepAmount.origSweepAmtAsString'].value="0.0";
			validateThis(document.forms[0].elements['sweepAmount.origSweepAmtAsString'],'orgAmt',currencyFormat,document.forms[0].elements['sweepAccount1.currCode'].value);
			document.getElementById("arrowbuttonimage").src = "images/leftarrowred.GIF";
			document.forms[0].elements["sweepAccount1.alignedSign"].value="Credit";
			document.forms[0].elements["sweepAccount2.alignedSign"].value="Debit";
			document.forms[0].elements["sweepAccount1.message"].value=document.forms[0].elements["sweepAccount1.messageTypeCr"].value;
			document.forms[0].elements["sweepAccount2.message"].value=document.forms[0].elements["sweepAccount2.messageTypeDr"].value;
			
			
// 			document.forms[0].elements["sweepAccount1.authQueue"].value=document.forms[0].elements["sweepAccount1.authFlagCr"].value;
// 			document.forms[0].elements["sweepAccount2.authQueue"].value=document.forms[0].elements["sweepAccount2.authFlagDr"].value;
			
			var authFlagCrValue = document.forms[0].elements["sweepAccount1.authFlagCr"].value;
			var authFlagDrValue = document.forms[0].elements["sweepAccount2.authFlagDr"].value;

			if (authFlagCrValue !== null && authFlagCrValue !== undefined && authFlagCrValue !== '') {
			  document.forms[0].elements["sweepAccount1.authQueue"].value = authFlagCrValue;
			}

			if (authFlagDrValue !== null && authFlagDrValue !== undefined && authFlagDrValue !== '') {
			  document.forms[0].elements["sweepAccount2.authQueue"].value = authFlagDrValue;
			}
			
			
			
			//Assign tooltip messageType form Account1,Account2
			document.getElementById("messageTypeAccount1").title=document.forms[0].elements["sweepAccount1.messageTypeCr"].value;
			document.getElementById("messageTypeAccount2").title=document.forms[0].elements["sweepAccount2.messageTypeDr"].value;
			/* End:Code modified by sudhakar For Mantis 1838:Message format not displayed accordingly when sweep direction changed*/
			
			document.forms[0].elements["sweepAccount1.defaultSettleMethod"].value=document.forms[0].elements["sweepAccount1.defaultSettleMethodCr"].value;
			document.forms[0].elements["sweepAccount2.defaultSettleMethod"].value=document.forms[0].elements["sweepAccount2.defaultSettleMethodDr"].value;
			document.forms[0].elements["sweepAccount1.bookcode"].value=document.forms[0].elements["sweepAccount1.bookcodeCr"].value;
			document.forms[0].elements["sweepAccount2.bookcode"].value=document.forms[0].elements["sweepAccount2.bookcodeDr"].value;
			
			$('#bookCodeId').val(document.forms[0].elements["sweepAccount1.bookcodeCr"].value);
			$('#bookCodeIdOther').val(document.forms[0].elements["sweepAccount2.bookcodeDr"].value);	
		}
	}
}
CheckBoxRadio.prototype.onClickCheckBoxRadio = function(e)
{
	var event = (window.event|| e);
	var target = (event.srcElement || event.target);
	var element = target;

	arrowflag =false;
	checkflag =true;

	if(element.checked == true)
	{
		var parent = element.parent;

		for(var idx = 0 ; idx < parent.checkBoxColl.length ; ++idx)
		{
			if(parent.checkBoxColl[idx] != element)
				parent.checkBoxColl[idx].checked = false;
		}
	}
		if(document.forms[0].elements["sweepAccount1.alignToTarget"].checked==true){
			alignIndex=1;
		}
		else alignIndex=2;


		calculateSweep(alignIndex);

}

 
function validateCurrencyOriginal(strField, strLabel, strPat, currCode){

   var thePat = PatternsDict[strPat]; 
   var gotIt = thePat.exec(strField.value); 
   var strVal = strField.value;
   var strDecimals = '2';
   
   if(currCode != 'undefined' && currCode != null ){
	   strDecimals = getCurrencyDecimal(currCode);
   }
    
   var strVal1 = new String(expandMBTValue(strField, thePat, strDecimals, strVal));
  return strVal1;
}


function validateDisplayOriginal(strField, strLabel, strPat, currCode){
	var thePat = PatternsDict[strPat]; 
	var strVal = strField.value;
	var strDecimals = '2';
	if(currCode != 'undefined' && currCode != null ){
	   strDecimals = getCurrencyDecimal(currCode);
	}
	strVal = round_decimals(strVal, strDecimals);
	if(strVal.length>0){
		var strVal1 = new String(expandMBTValue(strField, thePat, strDecimals, strVal));
		strField.value = insertChars(strVal,strDecimals,thePat);
	}
	return strField.value;
}

function validateDisplayOriginalWithoutModifiyingContent(strField, strLabel, strPat, currCode){
	var thePat = PatternsDict[strPat]; 
	var strVal = strField.value;
	var strDecimals = '2';
	var result;
	if(currCode != 'undefined' && currCode != null ){
	   strDecimals = getCurrencyDecimal(currCode);
	}
	strVal = round_decimals(strVal, strDecimals);
	if(strVal.length>0){
		var strVal1 = new String(expandMBTValue(strField, thePat, strDecimals, strVal));
		result = insertChars(strVal,strDecimals,thePat);
	}
	return result;
}

/**
  * bodyOnLoad
  * 			
  * This method is called when the screen is onloaded to set the status for elements.  
  */
function bodyonload(){
	  
	  
  scheduleSweepExist = document.forms[0].elements['sweepAmount.scheduleSweepExist'].value == "true"?true:false;
  scheduleSweepApplyed = document.forms[0].elements['sweepAmount.scheduleSweepApplyed'].value == "true"?true:false;
  scheduleSweepDetailsAsString = document.forms[0].elements['sweepAmount.scheduleSweepDetailsAsString'].value;
  lastSelectedScheduleSweepOption = document.forms[0].elements['sweepAmount.selectedScheduleSweepOption'].value;
  if(isManualSweeping){
	  const fieldSet = document.getElementById('fieldSetSchedule');
	  fieldSet.style.display = "block"; 
	if(scheduleSweepExist){
		document.querySelector("input[id^='useAccountDefaultRadioButon']").disabled = false;
		document.querySelector("input[id^='useScheduleRadioButon']").disabled = false;
	    const label = document.getElementById('scheduleLabel');
		// Change (replace) the text of the label
		label.textContent = '<s:text name="sweepDetail.sweepSetting.useSchedule"/>'+scheduleSweepDetailsAsString;
	
	 }else{
		 const label = document.getElementById('scheduleLabel');
			// Change (replace) the text of the label
			label.textContent = '<s:text name="sweepDetail.sweepSetting.noUseSchedule"/>';
		 
	 }
	}
  
  
	
	if(document.forms[0].elements["sweepAccount1.accountType"].value =="Main" && document.forms[0].elements["sweepAccount2.accountType"].value == "Sub"){
		if(document.forms[0].elements["sweepAccount1.subAcctim"].value == "Y"){
			document.forms[0].elements["sweepAccount1.cutOffTime"].disabled = true;
		}
	}
	//Disable the externalBalance,reexAlignedBalance,predictBalance for account1 based on sweepFrmbal as External 
	if(document.forms[0].elements["sweepAccount1.sweepFrmbal"].value == "External"){
		document.forms[0].elements["sweepAccount1.predictBalanceAsString"].disabled = true;
		document.forms[0].elements["sweepAccount1.predictBalanceSign"].disabled = true;
	}else{
		document.forms[0].elements["sweepAccount1.externalBalanceAsString"].disabled = true;
		document.forms[0].elements["sweepAccount1.externalBalanceSign"].disabled = true;
		document.forms[0].elements["sweepAccount1.reexAlignedBalanceAsString"].disabled = true;
		document.forms[0].elements["sweepAccount1.reexAlignedBalanceSign"].disabled = true;
	}
	//Disable the externalBalance,reexAlignedBalance,predictBalance for account2 based on sweepFrmbal as External
	if(document.forms[0].elements["sweepAccount2.sweepFrmbal"].value == "External"){
		document.forms[0].elements["sweepAccount2.predictBalanceAsString"].disabled = true;
		document.forms[0].elements["sweepAccount2.predictBalanceSign"].disabled = true;
	}else{
		document.forms[0].elements["sweepAccount2.externalBalanceAsString"].disabled = true;
		document.forms[0].elements["sweepAccount2.externalBalanceSign"].disabled = true;
		document.forms[0].elements["sweepAccount2.reexAlignedBalanceAsString"].disabled = true;
		document.forms[0].elements["sweepAccount2.reexAlignedBalanceSign"].disabled = true;
	}
	var arr = new Array(2);
	document.forms[0].currentSweepId.value=sweepId;
	arr[0] = document.forms[0].elements["sweepAccount1.alignToTarget"];
	arr[1] = document.forms[0].elements["sweepAccount2.alignToTarget"];
	var var1 = new CheckBoxRadio(arr);
	if(document.forms[0].elements[ "sweepAccount1.alignToTarget"].checked){				
		document.forms[0].elements["sweepAccount1.alignToTarget"].disabled = true;	
	}else{				
		document.forms[0].elements["sweepAccount2.alignToTarget"].disabled = true;	
	}	
	/* Start:Code modified by sudhakar For Mantis 1838:Message format not displayed accordingly when sweep direction changed*/
	if(document.forms[0].elements["sweepAccount1.alignedSign"].value=="Credit"){
		document.getElementById("arrowbuttonimage").src = "images/leftarrowblue.GIF";
		document.getElementById("messageTypeAccount1").title=document.forms[0].elements["sweepAccount1.messageTypeCr"].value;
		document.getElementById("messageTypeAccount2").title=document.forms[0].elements["sweepAccount2.messageTypeDr"].value;
		
		document.forms[0].elements["sweepAccount1.message"].value=document.forms[0].elements["sweepAccount1.messageTypeCr"].value;
		document.forms[0].elements["sweepAccount2.message"].value=document.forms[0].elements["sweepAccount2.messageTypeDr"].value;
		
		var authFlagCrValue = document.forms[0].elements["sweepAccount1.authFlagCr"].value;
		var authFlagDrValue = document.forms[0].elements["sweepAccount2.authFlagDr"].value;

		if (authFlagCrValue !== null && authFlagCrValue !== undefined && authFlagCrValue !== '') {
		  document.forms[0].elements["sweepAccount1.authQueue"].value = authFlagCrValue;
		}

		if (authFlagDrValue !== null && authFlagDrValue !== undefined && authFlagDrValue !== '') {
		  document.forms[0].elements["sweepAccount2.authQueue"].value = authFlagDrValue;
		}
		
		
// 		document.forms[0].elements["sweepAccount1.authQueue"].value=document.forms[0].elements["sweepAccount1.authFlagCr"].value;
// 		document.forms[0].elements["sweepAccount2.authQueue"].value=document.forms[0].elements["sweepAccount2.authFlagDr"].value;
	}else{
		document.getElementById("arrowbuttonimage").src = "images/rightarrowblue.GIF";	
		document.getElementById("messageTypeAccount1").title=document.forms[0].elements["sweepAccount1.messageTypeDr"].value;			
		document.getElementById("messageTypeAccount2").title=document.forms[0].elements["sweepAccount2.messageTypeCr"].value;
		
		document.forms[0].elements["sweepAccount1.message"].value=document.forms[0].elements["sweepAccount1.messageTypeDr"].value;
		document.forms[0].elements["sweepAccount2.message"].value=document.forms[0].elements["sweepAccount2.messageTypeCr"].value;
		
		var authFlagDrValue = document.forms[0].elements["sweepAccount1.authFlagDr"].value;
		var authFlagCrValue = document.forms[0].elements["sweepAccount2.authFlagCr"].value;
		
		if (authFlagDrValue !== null && authFlagDrValue !== undefined && authFlagDrValue !== '') {
		  document.forms[0].elements["sweepAccount1.authQueue"].value = authFlagDrValue;
		}
		
		if (authFlagCrValue !== null && authFlagCrValue !== undefined && authFlagCrValue !== '') {
		  document.forms[0].elements["sweepAccount2.authQueue"].value = authFlagCrValue;
		}
	}
	
	$("#amountInput").attr('type', 'search'); 
	
// 	var bookCodeMain ="${requestScope.bookcodeFirst}"; //document.forms[0].elements["sweepAccount1.bookcode"].value;
// 	var bookCodeSecond

	
// 	if(bookCodeSecond !="" && bookCodeSecond !="null"){
		bookSelectElement = document.forms[0].elements["sweepAccount2.bookcode"];
		var opt = document.createElement("option");
		opt.text = bookCodeSecond;	
		opt.value = bookCodeSecond;
		bookSelectElement.options.add(opt);	
		bookSelectElement.selectedIndex = 0;
		document.getElementById("bookCodeIdOther").value =bookCodeSecond;
// 	}
	
	
	
// 	if(bookCodeMain !="" && bookCodeMain !="null"){
		bookSelectElement = document.forms[0].elements["sweepAccount1.bookcode"];
		var opt = document.createElement("option");
		opt.text = bookCodeMain;	
		opt.value = bookCodeMain;
		bookSelectElement.options.add(opt);	
		bookSelectElement.selectedIndex = 0;
		document.getElementById("bookCodeId").value =bookCodeMain; 

// 	}
	
	
<%-- 	<%  if (valEqualsOne) {  %> --%>
if((sweepLevel=="submit") || (sweepLevel=="auth") || (sweepLevel=="org")){
	

	document.forms[0].elements["dropdownbutton_2"].disabled =  "" ;	
	document.forms[0].elements["dropdownbutton_3"].disabled =  "" ;	
	
	document.forms[0].elements["sweepAccount1.defaultSettleMethod"].disabled =  "" ;	
	document.forms[0].elements["sweepAccount2.defaultSettleMethod"].disabled =  "" ;
	
	document.forms[0].elements["sweepAmount.additionalReference"].disabled =  "" ;
	document.forms[0].elements["sweepAmount.additionalReference"].style.backgroundColor="";
	
	

	
	
}else {		
<%-- 	<%  }  else { %> --%>
	document.forms[0].elements["dropdownbutton_2"].disabled =  "true" ;	
	document.forms[0].elements["dropdownbutton_3"].disabled =  "true" ;	
	
	document.forms[0].elements["sweepAccount1.defaultSettleMethod"].disabled =  "true" ;	
	document.forms[0].elements["sweepAccount2.defaultSettleMethod"].disabled =  "true" ;
	
	document.forms[0].elements["sweepAmount.additionalReference"].disabled =  "true" ;
	
	document.getElementById('bookCodeIdOther').disabled =  "true" ;
	document.getElementById('bookCodeId').disabled =  "true" ;
	document.forms[0].elements["sweepAmount.additionalReference"].style.backgroundColor="transparent";
	
	document.forms[0].elements["sweepAccount1.defaultSettleMethod"].style.backgroundColor="transparent";
	document.forms[0].elements["sweepAccount2.defaultSettleMethod"].style.backgroundColor="transparent";
	document.getElementById('bookCodeId').style.backgroundColor="transparent";
	document.getElementById('bookCodeIdOther').style.backgroundColor="transparent";
	
	$('#lockUnlockImgSettSecond').hide();
	$('#lockUnlockImgBookOther').hide();
	$('#lockUnlockImgBookMain').hide();
	$('#lockUnlockImgSettMethod').hide();
	$('#lockUnlockImgAddInfo').hide();
        $('#lockUnlockAll').hide();
	
	

	
	
}
<%-- 	<%  }  %> --%>
	
	/* End:Code modified by sudhakar For Mantis 1838:Message format not displayed accordingly when sweep direction changed*/
	
	settMethod1InitialValue	 = document.forms[0].elements["sweepAccount1.defaultSettleMethod"].value;
	settMethod2InitialValue = document.forms[0].elements["sweepAccount2.defaultSettleMethod"].value;
	aditionalRefInitialValue = document.forms[0].elements["sweepAmount.additionalReference"].value;
	
	document.forms[0].elements["sweepAccount1.defaultSettleMethod"].disabled = true;
	document.forms[0].elements["sweepAccount2.defaultSettleMethod"].disabled = true;
	document.forms[0].elements["sweepAmount.additionalReference"].disabled = true;

	document.forms[0].elements["sweepAccount2.bookcode"].disabled = true;
	document.forms[0].elements["sweepAccount1.bookcode"].disabled = true;
	
	document.forms[0].elements["dropdownbutton_2"].disabled =  "true" ;	
	document.forms[0].elements["dropdownbutton_3"].disabled =  "true" ;
	
	document.getElementById('bookCodeIdOther').disabled =  "true" ;
	document.getElementById('bookCodeId').disabled =  "true" ;

}
var settMethod1InitialValue = '';
var settMethod2InitialValue  = '';
var aditionalRefInitialValue = '';

/**
  * getBookCodeList
 * 			
 * This method is used to get the collection of book code list.  
 */
function getBookCodeList(event) {
	if(bookCodeclk) {
		//get the all elements for selectbox
		var divElement = document.getElementById("dropdowndiv_3");
		var idElement = document.forms[0].elements["bookCodeId"];
		var descElement = document.forms[0].elements["bookCodeNameDisp"];
		var arrowElement = document.forms[0].elements["dropdownbutton_3"];
		bookSelectElement = document.forms[0].elements["sweepAccount1.bookcode"];
		var idLength = 12;
		var descLength = 30;
	
	
		var appName = "<%=SwtUtil.appName%>"; //Application name is picked up from property file
		var entityId = document.forms[0].elements["sweepAccount1.entityId"].value;
		var currencyCode = document.forms[0].elements['sweepAccount1.currCode'].value;
		
		//get the requestURL
		var requestURL = new String('<%=request.getRequestURL()%>');
		var idy = requestURL.indexOf('/'+appName+'/');
		//frame the requestURL
		requestURL=requestURL.substring(0,idy+1) ;
		requestURL = requestURL + appName+"/acctMaintenance.do?method=getBookCodeList";
		requestURL = requestURL + "&entityId=" + entityId;
		requestURL = requestURL + "&currencyCode=" + currencyCode;
		var oXMLHTTP = new XMLHttpRequest();
		oXMLHTTP.open( "POST", requestURL, false );
		//send the request for list
		oXMLHTTP.send();
		//get the response text
		var listValue=new String(oXMLHTTP.responseText);
		listValue = listValue.split('\n');
		if(bookSelectElement.options.length > 0){
			$(bookSelectElement).empty();
		}
		var index = 0;
		//add the book code into option
		for(var i=0;i < listValue.length -1;i++){
			var lblValue = listValue[i].split('~~~');
			var opt = document.createElement("option");
			opt.text = lblValue[0];	
			opt.value = lblValue[1];
			if(opt.value == bookCodeMain){
				index = i;				
			}	
			bookSelectElement.options.add(opt);
		}
		//set the selected index
		bookSelectElement.selectedIndex = index;
		//frame the selectbox component
		if(bookSwSel2 == null)
			bookSwSel2 = new SwMainSelectBox(divElement,bookSelectElement,idElement,descElement,arrowElement,idLength,descLength);
		bookSwSel2.setClickFlag();
		//call to populate the list in the list box
		bookSwSel2.arrowElementOnClick(event);
		
		bookCodeclk = false;
	}
}

var bookCodeclk= true;
var book2Codeclk= true;

/**
 * getBookCodeList
* 			
* This method is used to get the collection of book code list.  
*/
function getBookCodeListSecond(event) {
	if(book2Codeclk) {
		try{
			
		
		//get the all elements for selectbox
		var divElement = document.getElementById("dropdowndiv_2");
		var idElement = document.forms[0].elements["bookCodeIdOther"];
		var descElement = document.forms[0].elements["bookCodeNameDispOther"];
		var arrowElement = document.forms[0].elements["dropdownbutton_2"];
		bookSelectElement = document.forms[0].elements["sweepAccount2.bookcode"];
		
		var idLength = 12;
		var descLength = 30;
		var appName = "<%=SwtUtil.appName%>"; //Application name is picked up from property file
		var entityId = document.forms[0].elements["sweepAccount2.entityId"].value;
		var currencyCode = document.forms[0].elements['sweepAccount2.currCode'].value;
		//get the requestURL
		var requestURL = new String('<%=request.getRequestURL()%>');
		var idy = requestURL.indexOf('/'+appName+'/');
		//frame the requestURL
		requestURL=requestURL.substring(0,idy+1) ;
		requestURL = requestURL + appName+"/acctMaintenance.do?method=getBookCodeList";
		requestURL = requestURL + "&entityId=" + entityId;
		requestURL = requestURL + "&currencyCode=" + currencyCode;
		var oXMLHTTP = new XMLHttpRequest();
		oXMLHTTP.open( "POST", requestURL, false );
		//send the request for list
		oXMLHTTP.send();
		//get the response text
		var listValue=new String(oXMLHTTP.responseText);
		listValue = listValue.split('\n');
		if(bookSelectElement.options.length > 0){
			$(bookSelectElement).empty();
		}
		
		var index = 0;
		//add the book code into option
		for(var i=0;i < listValue.length -1;i++){
			var lblValue = listValue[i].split('~~~');
			var opt = document.createElement("option");
			opt.text = lblValue[0];
			opt.value = lblValue[1];
			if(opt.value == bookCodeMain){
				index = i;				
			}	
			bookSelectElement.options.add(opt);
		}
		
		//set the selected index
		bookSelectElement.selectedIndex = index;
		//frame the selectbox component
		if(bookSwSel == null)
			bookSwSel = new SwMainSelectBox(divElement,bookSelectElement,idElement,descElement,arrowElement,idLength,descLength);
		bookSwSel.setClickFlag();
		//call to populate the list in the list box
		bookSwSel.arrowElementOnClick(event);
		}catch(e){
			console.log(e)
		}
		book2Codeclk= false;
	}
}

var bookSwSel = null;
var bookSwSel2 = null;


function minSwpYes() {
	var orgSweep = document.forms[0].elements[globalSweep].value;
	var minsweep2 = document.forms[0].elements["sweepAccount2.minSweepAmt"].value;
	var AcctId2=document.forms[0].elements["sweepAccount2.accountId"].value;
	if(parseFloat(orgSweep)<parseFloat(minsweep2)){
		ShowErrMsgWindowWithBtn('', '<s:text name="sweepDetails.alert.sweepamount"/>'+" "+AcctId2+'<s:text name="sweepDetails.alert.sweeperror"/>', YES_NO, yesMin);

		}else {
			if((sweepLevel=="submit") || (sweepLevel=="auth")){
				document.forms[0].elements["sweepAccount1.alignToTarget"].disabled="";
       			document.forms[0].elements["sweepAccount2.alignToTarget"].disabled="";
			}	
			<s:if test='"org"==#request.sweep' >
				document.getElementById("sweepOkButton").innerHTML = document.getElementById("sweepOkdisablebutton").innerHTML;
			</s:if>
			document.forms[0].elements["sweepAccount1.cutOffTime"].disabled = "";
			enableFields();
			document.forms[0].submit();
		}
}
function yesMin() {
	if((sweepLevel=="submit") || (sweepLevel=="auth")){
		document.forms[0].elements["sweepAccount1.alignToTarget"].disabled="";
			 document.forms[0].elements["sweepAccount2.alignToTarget"].disabled="";
	}	
	
	<s:if test='"org"==#request.sweep' >
	document.getElementById("sweepOkButton").innerHTML = document.getElementById("sweepOkdisablebutton").innerHTML;
	</s:if>
								
	document.forms[0].elements["sweepAccount1.cutOffTime"].disabled = "";
	enableFields();
	document.forms[0].submit();
}
function minSwp2Yes() {
	if((sweepLevel=="submit") || (sweepLevel=="auth")){
		document.forms[0].elements["sweepAccount1.alignToTarget"].disabled="";
		document.forms[0].elements["sweepAccount2.alignToTarget"].disabled="";
	}
		
	<s:if test='"org"==#request.sweep' >
	document.getElementById("sweepOkButton").innerHTML = document.getElementById("sweepOkdisablebutton").innerHTML;
	</s:if>
	

	document.forms[0].elements["sweepAccount1.cutOffTime"].disabled = "";
	enableFields();
	document.forms[0].submit();
	
}

/*var eventSource = '';
document.addEventListener("comboBoxChanged", function(e) {
// 	console.log(e , e.detail[1].name)
	  	if(e &&  e.detail[1].name== 'sweepAccount1.bookcode' && bookCodeMain != document.forms[0].elements["sweepAccount1.bookcode"].value) {
			 ShowErrMsgWindowWithBtn('', '<s:text name="sweepDetails.alert.fieldChanged"/>', YES_NO, yesContinue, noRevert);
	  		 eventSource = 'bookcode1';
			 return;
		 }
		 if(e &&  e.detail[1].name== 'sweepAccount2.bookcode' && bookCodeSecond != document.forms[0].elements["sweepAccount2.bookcode"].value) {
			 ShowErrMsgWindowWithBtn('', '<s:text name="sweepDetails.alert.fieldChanged"/>', YES_NO, yesContinue, noRevert);
			 eventSource = 'bookcode2';
			 return;
		 }
});

function onChangeImportantFields(changedValue){
	if(changedValue == 'setMethod1' && settMethod1InitialValue != document.forms[0].elements["sweepAccount1.defaultSettleMethod"].value) {
		 ShowErrMsgWindowWithBtn('', '<s:text name="sweepDetails.alert.fieldChanged"/>', YES_NO, yesContinue, noRevert);
 		 eventSource = 'settMethod1';
		 return;
	 }
	 if(changedValue == 'setMethod2' &&  settMethod2InitialValue != document.forms[0].elements["sweepAccount2.defaultSettleMethod"].value) {
		 ShowErrMsgWindowWithBtn('', '<s:text name="sweepDetails.alert.fieldChanged"/>', YES_NO, yesContinue, noRevert);
		 eventSource = 'settMethod2';
		 return;
	 }
	 if(changedValue == 'addRef' && aditionalRefInitialValue != document.forms[0].elements["sweepAmount.additionalReference"].value) {
		 ShowErrMsgWindowWithBtn('', '<s:text name="sweepDetails.alert.fieldChanged"/>', YES_NO, yesContinue, noRevert);
		 eventSource = 'additionalRef';
		 return;
	 }
}

function yesContinue(){
}

function noRevert(){
	try{
	if(eventSource == 'bookcode1'){
		bookSelectElement = document.forms[0].elements["sweepAccount1.bookcode"];
		$(bookSelectElement).val(bookCodeMain);
		$('#bookCodeId').val(bookCodeMain);
		document.forms[0].elements["sweepAccount1.bookcode"].value = bookSelectElement;
	}else if(eventSource == 'bookcode2'){
		bookSelectElement = document.forms[0].elements["sweepAccount2.bookcode"];
		$(bookSelectElement).val(bookCodeSecond);
		$('#bookCodeIdOther').val(bookCodeSecond);
		document.forms[0].elements["sweepAccount2.bookcode"].value = bookCodeSecond;

	}else if(eventSource == 'settMethod1'){
		$('#defaultSettMethod1').val(settMethod1InitialValue);
		document.forms[0].elements["sweepAccount1.defaultSettleMethod"].value = settMethod1InitialValue;

	}else if(eventSource == 'settMethod2'){
		$('#defaultSettMethod2').val(settMethod2InitialValue);
		document.forms[0].elements["sweepAccount2.defaultSettleMethod"].value = settMethod2InitialValue;

	}else if(eventSource == 'additionalRef'){
		document.forms[0].elements["sweepAmount.additionalReference"].value = aditionalRefInitialValue;

	}
	
	
	}catch(e){
		console.log(e)
	}
}
*/	

 function submitForm(methodName){
	 var sweep;
	
	 var sweepValue=validateCurrency(document.forms[0].elements['sweepAmount.origSweepAmtAsString'],'sweepAmount.origSweepAmtAsString',currencyFormat);
	 if(sweepValue)
	 {
		
		 if(sweepLevel=="org" && parentMethod == "manualsweeping"){
			document.forms[0].method.value = "sweep";
			sweep="sweepAmount.origSweepAmt";
			
		 }
		 if(sweepLevel=="org" && parentMethod == "sweeppriorcutoff"){
			document.forms[0].method.value = "sweep";
			document.forms[0].calledFrom.value="sweeppriorcutoff";
			sweep="sweepAmount.origSweepAmt";
			
		 }
		
		 if(sweepLevel=="submit"){
			document.forms[0].method.value = "submit";
			document.forms[0].process.value = "submit";
			sweep="sweepAmount.subSweepAmt";
			 
		 }
		 if(sweepLevel=="auth"){
			
			document.forms[0].method.value = "updateSweepDetails";
			
			document.forms[0].process.value = "authorise";
			sweep="sweepAmount.authSweepAmt";
			
		 }
		 globalSweep = sweep;
		var orgSweep;
	 	orgSweep=document.forms[0].elements[sweep].value;
		if(orgSweep == 0){
			ShowErrMsgWindowWithBtn("", '<s:text name="sweepDetails.alert.amount"/>', null);
		}else{
	
			var minsweep1=document.forms[0].elements["sweepAccount1.minSweepAmt"].value;
			
			var minsweep2=document.forms[0].elements["sweepAccount2.minSweepAmt"].value;
			
			var maxsweep1=document.forms[0].elements["sweepAccount1.maxSweepAmt"].value;
			
			var maxsweep2=document.forms[0].elements["sweepAccount2.maxSweepAmt"].value;
			
			
			var AcctId1=document.forms[0].elements["sweepAccount1.accountId"].value;
			var AcctId2=document.forms[0].elements["sweepAccount2.accountId"].value;
			if(parseFloat(orgSweep)>parseFloat(maxsweep1) && parseFloat(maxsweep1) != 0.0){
				ShowErrMsgWindowWithBtn("",'<s:text name="sweepDetails.alert.amountLimits"/>' + " " + AcctId1, null);
			}else if(parseFloat(orgSweep)>parseFloat(maxsweep2) && parseFloat(maxsweep2) != 0.0)
			{	
				ShowErrMsgWindowWithBtn("", '<s:text name="sweepDetails.alert.amountLimits"/>' + " " + AcctId2, null);
			}
			else {
				if(parseFloat(orgSweep)<0)
					orgSweep = orgSweep *(-1);
					
				if(parseFloat(orgSweep)<parseFloat(minsweep1)){
					 ShowErrMsgWindowWithBtn('', '<s:text name="sweepDetails.alert.sweepamount"/>'+" "+AcctId1+'<s:text name="sweepDetails.alert.sweeperror"/>', YES_NO, minSwpYes);
				}
				else if(parseFloat(orgSweep)<parseFloat(minsweep2)){
						
					ShowErrMsgWindowWithBtn('<s:text name="sweepDetails.alert.microsoft"/>', '<s:text name="sweepDetails.alert.sweepamount"/>'+" "+AcctId2+'<s:text name="sweepDetails.alert.sweeperror"/>', YES_NO, minSwp2Yes);
						
				}
				else {
					 if((sweepLevel=="submit") || (sweepLevel=="auth")){
						document.forms[0].elements["sweepAccount1.alignToTarget"].disabled="";
						document.forms[0].elements["sweepAccount2.alignToTarget"].disabled="";
					 }
						
					<s:if test='"org"==#request.sweep' >
					document.getElementById("sweepOkButton").innerHTML = document.getElementById("sweepOkdisablebutton").innerHTML;
					</s:if>
					
					
					document.forms[0].elements["sweepAccount1.cutOffTime"].disabled = "";
					enableFields();
					document.forms[0].submit();
				}
				
			}
		}
	
	}else
	{
	  document.forms[0].elements['sweepAmount.origSweepAmtAsString'].focus();
	}
	
  }
  
  function enableFields(){
	  document.forms[0].elements["sweepAccount2.bookcode"].value = $('#bookCodeIdOther').val();
	  document.forms[0].elements["sweepAccount1.bookcode"].value = $('#bookCodeId').val();
	document.forms[0].elements["sweepAccount1.predictBalanceAsString"].disabled = false;
	document.forms[0].elements["sweepAccount1.predictBalanceSign"].disabled = false;
	document.forms[0].elements["sweepAccount1.externalBalanceAsString"].disabled = false;
	document.forms[0].elements["sweepAccount1.externalBalanceSign"].disabled = false;
	document.forms[0].elements["sweepAccount1.reexAlignedBalanceAsString"].disabled = false;
	document.forms[0].elements["sweepAccount1.reexAlignedBalanceSign"].disabled = false;
	document.forms[0].elements["sweepAccount2.predictBalanceAsString"].disabled = false;
	document.forms[0].elements["sweepAccount2.predictBalanceSign"].disabled = false;
	document.forms[0].elements["sweepAccount2.externalBalanceAsString"].disabled = false;
	document.forms[0].elements["sweepAccount2.externalBalanceSign"].disabled = false;
	document.forms[0].elements["sweepAccount2.reexAlignedBalanceAsString"].disabled = false;
	document.forms[0].elements["sweepAccount2.reexAlignedBalanceSign"].disabled = false;
	document.forms[0].elements["sweepAccount1.alignToTarget"].disabled=false;
	document.forms[0].elements["sweepAccount2.alignToTarget"].disabled=false;
	
	  document.getElementById('bookCodeIdOther').disabled =  false ;
	  document.getElementById('bookCodeId').disabled =  false ;

	document.forms[0].elements["sweepAccount1.defaultSettleMethod"].disabled = false;
	document.forms[0].elements["sweepAccount2.defaultSettleMethod"].disabled = false;
	document.forms[0].elements["sweepAmount.additionalReference"].disabled = false;

	document.forms[0].elements["sweepAccount2.bookcode"].disabled = false;
	document.forms[0].elements["sweepAccount1.bookcode"].disabled = false;
	
  }
    var mainSweepFromBalance = "";
    var originalSweepAmount = "";
	var originalSweepAmountSign = "";
	var mainRealignedPredictedBalance = "";
	var mainRealignedPredictedBalanceSign = "";
	var mainRealignedExternalBalance = "";
	var mainRealignedExternalBalanceSign = "";
	var mainPredictedBalance = "";
	var mainPredictedBalanceSign = "";
	var mainExternalBalance = "";
	var mainExternalBalanceSign = "";
	var mainTargetBalance = "";
	var mainTargetBalanceSign = "";
	var subSweepFromBalance = "";
	var subRealignedPredictedBalance = "";
	var subRealignedPredictedBalanceSign = "";
	var subRealignedExternalBalance = "";
	var subRealignedExternalBalanceSign = "";
	var subPredictedBalance = "";
	var subPredictedBalanceSign = "";
	var subExternalBalance = "";
	var subExternalBalanceSign = "";
	var subTargetBalance = "";
	var subTargetBalanceSign = "";
    var mainAccountSweepSign = "";
	var subAccountSweepSign = "";
	var maincurrencyCode="";
	var currencyCode="";
	
	/**
	  Function to calculate the Main account re-aligned predicted balance.
	  This function to be called after calculating the main account re-aligned external balance, 
	  which in turn will modify the main account re-aligned predicted balance.
	  @param originalSweepAmount
	**/
    function calculateMainRealignedPredictedBalance(orignalSweepAmount)
	{
		//Check if the sweep to be performed from main account predicted balance
       	if(mainSweepFromBalance == "Predicted"){
			//If so, check the whether the amount to be debitted or credited from the main account predicted balance
			if(mainAccountSweepSign == "Debit")
			    mainRealignedPredictedBalance = mainPredictedBalance - orignalSweepAmount;
			else
				mainRealignedPredictedBalance = mainPredictedBalance + orignalSweepAmount;
		//Else, add the difference between the main account external balance and re-aligned external balance to the predicted balance
		}else{
			mainRealignedPredictedBalance = mainPredictedBalance + (mainRealignedExternalBalance - mainExternalBalance );
		}
		//Check if the amount is greater than or equal to zero, set the sign to "C" else "D"
		if (mainRealignedPredictedBalance >= 0)
			mainRealignedPredictedBalanceSign = "C"
		else
			mainRealignedPredictedBalanceSign = "D"
		
		//Store the calculated balances and sign after formatting for display
		document.forms[0].elements["sweepAccount1.reAlignedBalance"].value = mainRealignedPredictedBalance;
		document.forms[0].elements["sweepAccount1.reAlignedBalanceAsString"].value=
			validateDisplayOriginalWithoutModifiyingContent(document.forms[0].elements["sweepAccount1.reAlignedBalance"],'orgAmt',currencyFormat,currencyCode);
		document.forms[0].elements["sweepAccount1.reAlignedBalanceSign"].value= mainRealignedPredictedBalanceSign;
    }
	/**
	  Function to calculate the Main account re-aligned external balance.	  
	  @param originalSweepAmount
	**/
	function calculateMainRealignedExternalBalance(orignalSweepAmount)
	{
		//Check if the sweep to be performed from main account external balance
		if(mainSweepFromBalance == "External"){
			//If so, check the whether the amount to be debitted or credited from the main account external balance
			if(mainAccountSweepSign == "Debit")
			    mainRealignedExternalBalance = mainExternalBalance - orignalSweepAmount;
			else
				mainRealignedExternalBalance = mainExternalBalance + orignalSweepAmount;
		//Else, set the external balance as the re-aligned external balance
		}else{
			mainRealignedExternalBalance = mainExternalBalance;
		}
		//Check if the amount is greater than or equal to zero, set the sign to "C" else "D"
		if (mainRealignedExternalBalance >= 0)
			mainRealignedExternalBalanceSign = "C"
		else
			mainRealignedExternalBalanceSign = "D"
		
		//Store the calculated balances and sign after formatting for display
		document.forms[0].elements["sweepAccount1.reexAlignedBalance"].value=mainRealignedExternalBalance;
		document.forms[0].elements["sweepAccount1.reexAlignedBalanceAsString"].value=
			validateDisplayOriginalWithoutModifiyingContent(document.forms[0].elements["sweepAccount1.reexAlignedBalance"],'orgAmt',currencyFormat,currencyCode);
		document.forms[0].elements["sweepAccount1.reexAlignedBalanceSign"].value= mainRealignedExternalBalanceSign;
	}
	/**
	  Function to calculate the Sub account re-aligned predicted balance.
	  This function to be called after calculating the main account re-aligned external balance, 
	  which in turn will modify the main account re-aligned predicted balance.
	  @param originalSweepAmount
	**/
	function calculateSubRealignedPredictedBalance(orignalSweepAmount)
	{
		//Check if the sweep to be performed from sub account predicted balance
		if(subSweepFromBalance == "Predicted"){
			//If so, check the whether the amount to be debitted or credited from the sub account predicted balance
			if(subAccountSweepSign == "Debit")
			    subRealignedPredictedBalance = subPredictedBalance - orignalSweepAmount;
			else
				subRealignedPredictedBalance = subPredictedBalance + orignalSweepAmount;
		//Else, add the difference between the sub account external balance and re-aligned external balance to the predicted balance
		}else{
			subRealignedPredictedBalance = subPredictedBalance + (subRealignedExternalBalance - subExternalBalance );
		}
		//Check if the amount is greater than or equal to zero, set the sign to "C" else "D"
		if (subRealignedPredictedBalance >= 0)
			subRealignedPredictedBalanceSign = "C"
		else
			subRealignedPredictedBalanceSign = "D"
		
		//Store the calculated balances and sign after formatting for display
		document.forms[0].elements["sweepAccount2.reAlignedBalance"].value = subRealignedPredictedBalance;
		document.forms[0].elements["sweepAccount2.reAlignedBalanceAsString"].value=
			validateDisplayOriginalWithoutModifiyingContent(document.forms[0].elements["sweepAccount2.reAlignedBalance"],'orgAmt',currencyFormat,currencyCode);
		document.forms[0].elements["sweepAccount2.reAlignedBalanceSign"].value= subRealignedPredictedBalanceSign;
	}
	/**
	  Function to calculate the sub account re-aligned external balance.	  
	  @param originalSweepAmount
	**/
	function calculateSubRealignedExternalBalance(orignalSweepAmount)
	{
		//Check if the sweep to be performed from sub account external balance
		if(subSweepFromBalance == "External"){
			//If so, check the whether the amount to be debitted or credited from the sub account external balance
			if(subAccountSweepSign == "Debit")
			    subRealignedExternalBalance = subExternalBalance - orignalSweepAmount;
			else
				subRealignedExternalBalance = subExternalBalance + orignalSweepAmount;
		//Else, set the external balance as the re-aligned external balance
		}else{
			subRealignedExternalBalance = subExternalBalance;
		}
		//Check if the amount is greater than or equal to zero, set the sign to "C" else "D"
		if (subRealignedExternalBalance >= 0)
			subRealignedExternalBalanceSign = "C"
		else
			subRealignedExternalBalanceSign = "D"
			
		//Store the calculated balances and sign after formatting for display
		document.forms[0].elements["sweepAccount2.reexAlignedBalance"].value=subRealignedExternalBalance;	
		document.forms[0].elements["sweepAccount2.reexAlignedBalanceAsString"].value=
			validateDisplayOriginalWithoutModifiyingContent(document.forms[0].elements["sweepAccount2.reexAlignedBalance"],'orgAmt',currencyFormat,currencyCode);
		document.forms[0].elements["sweepAccount2.reexAlignedBalanceSign"].value= subRealignedExternalBalanceSign;
	}
	/**
	  Function to calculate the original sweep amount based on the target selected.
	  Also calculates the sweep direction
	*/
	function originalSweepAmountCalculation()
	{
		//Temporary variables for holding the original sweep amount, target balance and align to target. 
		var tempBalanceForOriginalSweepAmount = 0;
		var tempTargetBalance = 0;
		var tempAlignToTarget = "";
		//Variables to set the direction sign for main and sub accounts
		var mainDirectionSign = "";
		var subDirectionSign = "";
		//Check if the balances to be aligned to main account
		if(document.forms[0].elements["sweepAccount1.alignToTarget"].value == "Y" && document.forms[0].elements[ "sweepAccount1.alignToTarget"].disabled == false){
			//If so, Check the balances to be calcutated on predicted or external balance and assign to the temp tempBalanceForOriginalSweepAmount variable
			if(mainSweepFromBalance == "Predicted")
				tempBalanceForOriginalSweepAmount = mainPredictedBalance ;				
			else
				tempBalanceForOriginalSweepAmount = mainExternalBalance ;
			//Set the temp target and align to target values as main account
			tempTargetBalance = mainTargetBalance;
			tempAlignToTarget = "M";
		}
		//Check if the balances to be aligned to sub account
		if(document.forms[0].elements["sweepAccount2.alignToTarget"].value == "Y"  && document.forms[0].elements[ "sweepAccount2.alignToTarget"].disabled == false){
			//If so, Check the balances to be calcutated on predicted or external balance and assign to the temp tempBalanceForOriginalSweepAmount variable
			if(subSweepFromBalance == "Predicted")
				tempBalanceForOriginalSweepAmount = subPredictedBalance ;
			else
				tempBalanceForOriginalSweepAmount = subExternalBalance ;
			
			//Set the temp target and align to target values as sub account
			tempTargetBalance = subTargetBalance;
			tempAlignToTarget = "S";
		}
		//If the target balance is greater or equal to sweep from balamce amount
		if(tempTargetBalance >= tempBalanceForOriginalSweepAmount){
			//Check if the align to target is to Main or Sub account
			if(tempAlignToTarget == "M"){
			    //If main, debit the sub and credit the main account and the direction to sub to main
				document.getElementById("arrowbuttonimage").src = "images/leftarrowblue.GIF";
				document.forms[0].elements["sweepAccount1.alignedSign"].value="Credit";
				document.forms[0].elements["sweepAccount2.alignedSign"].value="Debit";
				/* Start:Code modified by sudhakar For Mantis 1838:Message format not displayed accordingly when sweep direction changed*/
				document.forms[0].elements["sweepAccount1.message"].value=document.forms[0].elements["sweepAccount1.messageTypeCr"].value;
				document.forms[0].elements["sweepAccount2.message"].value=document.forms[0].elements["sweepAccount2.messageTypeDr"].value;
				
// 				document.forms[0].elements["sweepAccount1.authQueue"].value=document.forms[0].elements["sweepAccount1.authFlagCr"].value;
// 				document.forms[0].elements["sweepAccount2.authQueue"].value=document.forms[0].elements["sweepAccount2.authFlagDr"].value;
				
				
				var authFlagCrValue = document.forms[0].elements["sweepAccount1.authFlagCr"].value;
				var authFlagDrValue = document.forms[0].elements["sweepAccount2.authFlagDr"].value;

				if (authFlagCrValue !== null && authFlagCrValue !== undefined && authFlagCrValue !== '') {
				  document.forms[0].elements["sweepAccount1.authQueue"].value = authFlagCrValue;
				}

				if (authFlagDrValue !== null && authFlagDrValue !== undefined && authFlagDrValue !== '') {
				  document.forms[0].elements["sweepAccount2.authQueue"].value = authFlagDrValue;
				}
				
				
				//Assign tooltip messageType form Account1,Account2
				document.getElementById("messageTypeAccount1").title=document.forms[0].elements["sweepAccount1.messageTypeCr"].value;
				document.getElementById("messageTypeAccount2").title=document.forms[0].elements["sweepAccount2.messageTypeDr"].value;
				
				
				document.forms[0].elements["sweepAccount1.defaultSettleMethod"].value=document.forms[0].elements["sweepAccount1.defaultSettleMethodCr"].value;
				document.forms[0].elements["sweepAccount2.defaultSettleMethod"].value=document.forms[0].elements["sweepAccount2.defaultSettleMethodDr"].value;
				document.forms[0].elements["sweepAccount1.bookcode"].value=document.forms[0].elements["sweepAccount1.bookcodeCr"].value;
				document.forms[0].elements["sweepAccount2.bookcode"].value=document.forms[0].elements["sweepAccount2.bookcodeDr"].value;
				
				$('#bookCodeId').val(document.forms[0].elements["sweepAccount1.bookcodeCr"].value);
				$('#bookCodeIdOther').val(document.forms[0].elements["sweepAccount2.bookcodeDr"].value);
				
				
			}
			if(tempAlignToTarget == "S"){
				//If sub, debit the main and credit the sub account and the direction to main to sub
				document.getElementById("arrowbuttonimage").src = "images/rightarrowblue.GIF";
				document.forms[0].elements["sweepAccount1.alignedSign"].value="Debit";
				document.forms[0].elements["sweepAccount2.alignedSign"].value="Credit";
				document.forms[0].elements["sweepAccount1.message"].value=document.forms[0].elements["sweepAccount1.messageTypeDr"].value;
				document.forms[0].elements["sweepAccount2.message"].value=document.forms[0].elements["sweepAccount2.messageTypeCr"].value;
				
// 				document.forms[0].elements["sweepAccount1.authQueue"].value=document.forms[0].elements["sweepAccount1.authFlagDr"].value;
// 				document.forms[0].elements["sweepAccount2.authQueue"].value=document.forms[0].elements["sweepAccount2.authFlagCr"].value;
				
				var authFlagDrValue = document.forms[0].elements["sweepAccount1.authFlagDr"].value;
				var authFlagCrValue = document.forms[0].elements["sweepAccount2.authFlagCr"].value;
				
				if (authFlagDrValue !== null && authFlagDrValue !== undefined && authFlagDrValue !== '') {
				  document.forms[0].elements["sweepAccount1.authQueue"].value = authFlagDrValue;
				}
				
				if (authFlagCrValue !== null && authFlagCrValue !== undefined && authFlagCrValue !== '') {
				  document.forms[0].elements["sweepAccount2.authQueue"].value = authFlagCrValue;
				}
				
				
				//Assign tooltip messageType form Account1,Account2
				document.getElementById("messageTypeAccount1").title=document.forms[0].elements["sweepAccount1.messageTypeDr"].value;			
				document.getElementById("messageTypeAccount2").title=document.forms[0].elements["sweepAccount2.messageTypeCr"].value;
				
				
				document.forms[0].elements["sweepAccount1.defaultSettleMethod"].value=document.forms[0].elements["sweepAccount1.defaultSettleMethodDr"].value;
				document.forms[0].elements["sweepAccount2.defaultSettleMethod"].value=document.forms[0].elements["sweepAccount2.defaultSettleMethodCr"].value;
				
				document.forms[0].elements["sweepAccount1.bookcode"].value=document.forms[0].elements["sweepAccount1.bookcodeDr"].value;
				document.forms[0].elements["sweepAccount2.bookcode"].value=document.forms[0].elements["sweepAccount2.bookcodeCr"].value;
				
				$('#bookCodeId').val(document.forms[0].elements["sweepAccount1.bookcodeDr"].value);
				$('#bookCodeIdOther').val(document.forms[0].elements["sweepAccount2.bookcodeCr"].value);
				
			}
		//Else,the target balance is less than sweep from balamce amount
		}else{
			//Check if the align to target is to Main or Sub account
			if(tempAlignToTarget == "M"){
				//If main, debit the main and credit the sub account and the direction to main to sub
				document.getElementById("arrowbuttonimage").src = "images/rightarrowblue.GIF";
				document.forms[0].elements["sweepAccount1.alignedSign"].value="Debit";
				document.forms[0].elements["sweepAccount2.alignedSign"].value="Credit";
				document.forms[0].elements["sweepAccount1.message"].value=document.forms[0].elements["sweepAccount1.messageTypeDr"].value;
				document.forms[0].elements["sweepAccount2.message"].value=document.forms[0].elements["sweepAccount2.messageTypeCr"].value;
				
				var authFlagDrValue = document.forms[0].elements["sweepAccount1.authFlagDr"].value;
				var authFlagCrValue = document.forms[0].elements["sweepAccount2.authFlagCr"].value;
				
				if (authFlagDrValue !== null && authFlagDrValue !== undefined && authFlagDrValue !== '') {
				  document.forms[0].elements["sweepAccount1.authQueue"].value = authFlagDrValue;
				}
				
				if (authFlagCrValue !== null && authFlagCrValue !== undefined && authFlagCrValue !== '') {
				  document.forms[0].elements["sweepAccount2.authQueue"].value = authFlagCrValue;
				}
				//Assign tooltip messageType form Account1,Account2
				document.getElementById("messageTypeAccount1").title=document.forms[0].elements["sweepAccount1.messageTypeDr"].value;
				document.getElementById("messageTypeAccount2").title=document.forms[0].elements["sweepAccount2.messageTypeCr"].value;
				document.forms[0].elements["sweepAccount1.defaultSettleMethod"].value=document.forms[0].elements["sweepAccount1.defaultSettleMethodDr"].value;
				document.forms[0].elements["sweepAccount2.defaultSettleMethod"].value=document.forms[0].elements["sweepAccount2.defaultSettleMethodCr"].value;
				
				document.forms[0].elements["sweepAccount1.bookcode"].value=document.forms[0].elements["sweepAccount1.bookcodeDr"].value;
				document.forms[0].elements["sweepAccount2.bookcode"].value=document.forms[0].elements["sweepAccount2.bookcodeCr"].value;
				
				$('#bookCodeId').val(document.forms[0].elements["sweepAccount1.bookcodeDr"].value);
				$('#bookCodeIdOther').val(document.forms[0].elements["sweepAccount2.bookcodeCr"].value);	
			}
			if(tempAlignToTarget == "S"){
				//If sub, debit the sub and credit the main account and the direction to sub to main
				document.getElementById("arrowbuttonimage").src = "images/leftarrowblue.GIF";
				document.forms[0].elements["sweepAccount1.alignedSign"].value="Credit";
				document.forms[0].elements["sweepAccount2.alignedSign"].value="Debit";
				document.forms[0].elements["sweepAccount1.message"].value=document.forms[0].elements["sweepAccount1.messageTypeCr"].value;
				document.forms[0].elements["sweepAccount2.message"].value=document.forms[0].elements["sweepAccount2.messageTypeDr"].value;
				
// 				document.forms[0].elements["sweepAccount1.authQueue"].value=document.forms[0].elements["sweepAccount1.authFlagCr"].value;
// 				document.forms[0].elements["sweepAccount2.authQueue"].value=document.forms[0].elements["sweepAccount2.authFlagDr"].value;
				
				var authFlagCrValue = document.forms[0].elements["sweepAccount1.authFlagCr"].value;
				var authFlagDrValue = document.forms[0].elements["sweepAccount2.authFlagDr"].value;

				if (authFlagCrValue !== null && authFlagCrValue !== undefined && authFlagCrValue !== '') {
				  document.forms[0].elements["sweepAccount1.authQueue"].value = authFlagCrValue;
				}

				if (authFlagDrValue !== null && authFlagDrValue !== undefined && authFlagDrValue !== '') {
				  document.forms[0].elements["sweepAccount2.authQueue"].value = authFlagDrValue;
				}
				
				
				//Assign tooltip messageType form Account1,Account2
				document.getElementById("messageTypeAccount1").title=document.forms[0].elements["sweepAccount1.messageTypeCr"].value;
				document.getElementById("messageTypeAccount2").title=document.forms[0].elements["sweepAccount2.messageTypeDr"].value;
				/* End:Code modified by sudhakar For Mantis 1838:Message format not displayed accordingly when sweep direction changed*/
				
				document.forms[0].elements["sweepAccount1.defaultSettleMethod"].value=document.forms[0].elements["sweepAccount1.defaultSettleMethodCr"].value;
				document.forms[0].elements["sweepAccount2.defaultSettleMethod"].value=document.forms[0].elements["sweepAccount2.defaultSettleMethodDr"].value;
				document.forms[0].elements["sweepAccount1.bookcode"].value=document.forms[0].elements["sweepAccount1.bookcodeCr"].value;
				document.forms[0].elements["sweepAccount2.bookcode"].value=document.forms[0].elements["sweepAccount2.bookcodeDr"].value;
				
				$('#bookCodeId').val(document.forms[0].elements["sweepAccount1.bookcodeCr"].value);
				$('#bookCodeIdOther').val(document.forms[0].elements["sweepAccount2.bookcodeDr"].value);
				
			}
		}
		//Set the Original sweep amount as the difference between the sweep from balance and the target balance
		originalSweepAmount = tempBalanceForOriginalSweepAmount - tempTargetBalance;
	}

	/**
	 * Method to claculate the changed Swee amount
	 */
	function changeAmount(element)
	{
		currencyCode=document.forms[0].elements['sweepAccount1.currCode'].value;
		var alignIndex;
		var swepAmtAsString;
		var sweepAmt;
		if(element.name=="sweepAmount.origSweepAmtAsString"){
			swepAmtAsString="sweepAmount.origSweepAmtAsString";
			sweepAmt="sweepAmount.origSweepAmt";
		}else if(element.name=="sweepAmount.subSweepAmtAsString"){
			swepAmtAsString="sweepAmount.subSweepAmtAsString";
			sweepAmt="sweepAmount.subSweepAmt";
		}else {
			swepAmtAsString="sweepAmount.authSweepAmtAsString";
			sweepAmt="sweepAmount.authSweepAmt";
		}
		
		
	
		
		mainSweepFromBalance = document.forms[0].elements["sweepAccount1.sweepFrmbal"].value;
		mainPredictedBalance = parseFloat(document.forms[0].elements["sweepAccount1.predictBalance"].value);
		mainPredictedBalanceSign = document.forms[0].elements["sweepAccount1.predictBalanceSign"].value
		mainExternalBalance = parseFloat(document.forms[0].elements["sweepAccount1.externalBalance"].value);
		mainExternalBalanceSign = document.forms[0].elements["sweepAccount1.externalBalanceSign"].value;
		mainTargetBalance = parseFloat(document.forms[0].elements["sweepAccount1.targetBalance"].value);
		mainTargetBalanceSign = document.forms[0].elements["sweepAccount1.targetBalanceSign"].value;
		subSweepFromBalance = document.forms[0].elements["sweepAccount2.sweepFrmbal"].value;
		subPredictedBalance = parseFloat(document.forms[0].elements["sweepAccount2.predictBalance"].value);
		subPredictedBalanceSign = document.forms[0].elements["sweepAccount2.predictBalanceSign"].value;
		subExternalBalance = parseFloat(document.forms[0].elements["sweepAccount2.externalBalance"].value);
		subExternalBalanceSign = document.forms[0].elements["sweepAccount2.externalBalanceSign"].value;
		subTargetBalance = parseFloat(document.forms[0].elements["sweepAccount2.targetBalance"].value);
		subTargetBalanceSign = document.forms[0].elements["sweepAccount2.targetBalanceSign"].value;	
		mainAccountSweepSign = document.forms[0].elements["sweepAccount1.alignedSign"].value;
		subAccountSweepSign = document.forms[0].elements["sweepAccount2.alignedSign"].value;
		document.forms[0].elements[sweepAmt].value=validateCurrencyOriginal(document.forms[0].elements[swepAmtAsString], 'label1', currencyFormat, currencyCode);
		originalSweepAmount = parseFloat(document.forms[0].elements[sweepAmt].value);
  
		calculateMainRealignedExternalBalance(originalSweepAmount);
		calculateMainRealignedPredictedBalance(originalSweepAmount);
		
		calculateSubRealignedExternalBalance(originalSweepAmount);
		calculateSubRealignedPredictedBalance(originalSweepAmount);
		
		
	}

  
	/**
	* Method to handle Allign to target changes
	*/
	function calculateSweep(index){
		var AmtAsString;
		var Amt;
		var currencyCode=document.forms[0].elements['sweepAccount1.currCode'].value;
		if(sweepLevel=="org"){
			AmtAsString="sweepAmount.origSweepAmtAsString";
			Amt="sweepAmount.origSweepAmt";
		}else if(sweepLevel=="submit"){
			AmtAsString="sweepAmount.subSweepAmtAsString";
			Amt="sweepAmount.subSweepAmt";
		}else {
			AmtAsString="sweepAmount.authSweepAmtAsString";
			Amt="sweepAmount.authSweepAmt";
		}
		globalAmt = Amt;
		globalAmtAsString = AmtAsString;
		var i;
		var j;
		if(index==1){
			i=1;
			j=2;
		}else{
			i=2;
			j=1;
		}

		if(arrowflag==true)
		{
			var returnval =5;
		}
		else
			ShowErrMsgWindowWithBtn('', '<s:text name="sweepDetails.alert.sweeptarget"/>', YES_NO, alignYes, alignNo);
	
	}
	var lockedBookMain = true;
	var lockedBookSecond = true;
	var lockedBookSettMain = true;
	var lockedBookSettSecond = true;
	var lockedBookAddInfo = true;
	var lockedAll = true;
	
	
	function changeIcon(from) {
		
		if(from == 'addInfo'){
			if(lockedBookAddInfo)	{
				document.getElementById("lockUnlockImgAddInfo").src =  "images/unlock.png";
				lockedBookAddInfo = false;
				document.forms[0].elements["sweepAmount.additionalReference"].disabled = false;
			}else{
				document.getElementById("lockUnlockImgAddInfo").src =  "images/lock.png";
				lockedBookAddInfo = true;
				document.forms[0].elements["sweepAmount.additionalReference"].disabled = true;
			}
		}else if(from == 'bookMain'){
			if(lockedBookMain)	{
				document.getElementById("lockUnlockImgBookMain").src =  "images/unlock.png";
				document.forms[0].elements["sweepAccount1.bookcode"].disabled = false;
				document.forms[0].elements["dropdownbutton_3"].disabled =  "" ;	

				$('#bookCodeId').prop( "disabled", false );
				lockedBookMain = false;
			}else{
				document.getElementById("lockUnlockImgBookMain").src =  "images/lock.png";
				document.forms[0].elements["sweepAccount1.bookcode"].disabled = true;
				document.forms[0].elements["dropdownbutton_3"].disabled =  "true" ;	

				lockedBookMain = true;
				
				$('#bookCodeId').prop( "disabled", true );
			}
		}else if(from == 'settMethodFirst'){
			if(lockedBookSettMain)	{
				document.getElementById("lockUnlockImgSettMethod").src =  "images/unlock.png";
				lockedBookSettMain = false;
				document.forms[0].elements["sweepAccount1.defaultSettleMethod"].disabled = false;
			}else{
				document.getElementById("lockUnlockImgSettMethod").src =  "images/lock.png";
				document.forms[0].elements["sweepAccount1.defaultSettleMethod"].disabled = true;
				lockedBookSettMain = true;
			}
		}else if(from == 'bookOther'){
			if(lockedBookSecond)	{
				document.getElementById("lockUnlockImgBookOther").src =  "images/unlock.png";
				document.forms[0].elements["sweepAccount2.bookcode"].disabled = false;
				$('#bookCodeIdOther').prop( "disabled", false );
				
				document.forms[0].elements["dropdownbutton_2"].disabled =  "" ;
				lockedBookSecond = false;
			}else{
				document.getElementById("lockUnlockImgBookOther").src =  "images/lock.png";
				document.forms[0].elements["sweepAccount2.bookcode"].disabled = true;
				document.forms[0].elements["dropdownbutton_2"].disabled =  "true" ;
				lockedBookSecond = true;
				$('#bookCodeIdOther').prop( "disabled", true );
			}
		}else if(from == 'settMethodSecond'){
			if(lockedBookSettSecond)	{
				document.getElementById("lockUnlockImgSettSecond").src =  "images/unlock.png";
				document.forms[0].elements["sweepAccount2.defaultSettleMethod"].disabled = false;
				lockedBookSettSecond = false;
			}else{
				document.getElementById("lockUnlockImgSettSecond").src =  "images/lock.png";
				document.forms[0].elements["sweepAccount2.defaultSettleMethod"].disabled = true;
				lockedBookSettSecond = true;
			}
		}else if(from == 'all'){
			if(lockedAll)	{
				document.getElementById("lockUnlockAll").src =  "images/unlock.png";
				lockedAll = false;
				
				
				document.getElementById("lockUnlockImgSettSecond").src =  "images/unlock.png";
				document.forms[0].elements["sweepAccount2.defaultSettleMethod"].disabled = false;
				lockedBookSettSecond = false;
				
				document.getElementById("lockUnlockImgSettMethod").src =  "images/unlock.png";
				lockedBookSettMain = false;
				document.forms[0].elements["sweepAccount1.defaultSettleMethod"].disabled = false;
				
				
				document.getElementById("lockUnlockImgBookOther").src =  "images/unlock.png";
				document.forms[0].elements["sweepAccount2.bookcode"].disabled = false;
				$('#bookCodeIdOther').prop( "disabled", false );
				
				document.forms[0].elements["dropdownbutton_2"].disabled =  "" ;
				lockedBookSecond = false;
				
				
				
				document.getElementById("lockUnlockImgBookMain").src =  "images/unlock.png";
				document.forms[0].elements["sweepAccount1.bookcode"].disabled = false;
				document.forms[0].elements["dropdownbutton_3"].disabled =  "" ;	

				$('#bookCodeId').prop( "disabled", false );
				lockedBookMain = false;
				
				document.getElementById("lockUnlockImgAddInfo").src =  "images/unlock.png";
				lockedBookAddInfo = false;
				document.forms[0].elements["sweepAmount.additionalReference"].disabled = false;
				
				
			}else{
				document.getElementById("lockUnlockAll").src =  "images/lock.png";
				lockedAll = true;
				
				
				document.getElementById("lockUnlockImgBookOther").src =  "images/lock.png";
				document.forms[0].elements["sweepAccount2.bookcode"].disabled = true;
				document.forms[0].elements["dropdownbutton_2"].disabled =  "true" ;
				lockedBookSecond = true;
				$('#bookCodeIdOther').prop( "disabled", true );
				
				
				document.getElementById("lockUnlockImgSettMethod").src =  "images/lock.png";
				document.forms[0].elements["sweepAccount1.defaultSettleMethod"].disabled = true;
				lockedBookSettMain = true;
				
				
				document.getElementById("lockUnlockImgSettSecond").src =  "images/lock.png";
				document.forms[0].elements["sweepAccount2.defaultSettleMethod"].disabled = true;
				lockedBookSettSecond = true;
				
				
				document.getElementById("lockUnlockImgBookMain").src =  "images/lock.png";
				document.forms[0].elements["sweepAccount1.bookcode"].disabled = true;
				document.forms[0].elements["dropdownbutton_3"].disabled =  "true" ;	

				lockedBookMain = true;
				
				$('#bookCodeId').prop( "disabled", true );
				
				document.getElementById("lockUnlockImgAddInfo").src =  "images/lock.png";
				lockedBookAddInfo = true;
				document.forms[0].elements["sweepAmount.additionalReference"].disabled = true;
				
				
				
				
			}
		}
		
		if(!lockedBookMain && !lockedBookSecond && !lockedBookSettMain &&  !lockedBookSettSecond &&  !lockedBookAddInfo){
			lockedAll = false;
			document.getElementById("lockUnlockAll").src =  "images/unlock.png";
		}else if(lockedBookMain && lockedBookSecond && lockedBookSettMain &&  lockedBookSettSecond &&  lockedBookAddInfo){
			lockedAll = true;
			document.getElementById("lockUnlockAll").src =  "images/lock.png";
		}
			
			
			

	}
  
  
  function alignYes() {
	    mainSweepFromBalance = document.forms[0].elements["sweepAccount1.sweepFrmbal"].value;
		mainPredictedBalance = parseFloat(document.forms[0].elements["sweepAccount1.predictBalance"].value);
		mainPredictedBalanceSign = document.forms[0].elements["sweepAccount1.predictBalanceSign"].value
		mainExternalBalance = parseFloat(document.forms[0].elements["sweepAccount1.externalBalance"].value);
		mainExternalBalanceSign = document.forms[0].elements["sweepAccount1.externalBalanceSign"].value;
		mainTargetBalance = parseFloat(document.forms[0].elements["sweepAccount1.targetBalance"].value);
		mainTargetBalanceSign = document.forms[0].elements["sweepAccount1.targetBalanceSign"].value;
		subSweepFromBalance = document.forms[0].elements["sweepAccount2.sweepFrmbal"].value;
		subPredictedBalance = parseFloat(document.forms[0].elements["sweepAccount2.predictBalance"].value);
		subPredictedBalanceSign = document.forms[0].elements["sweepAccount2.predictBalanceSign"].value;
		subExternalBalance = parseFloat(document.forms[0].elements["sweepAccount2.externalBalance"].value);
		subExternalBalanceSign = document.forms[0].elements["sweepAccount2.externalBalanceSign"].value;
		subTargetBalance = parseFloat(document.forms[0].elements["sweepAccount2.targetBalance"].value);
		subTargetBalanceSign = document.forms[0].elements["sweepAccount2.targetBalanceSign"].value;	
		
					
		originalSweepAmountCalculation();	
		
		mainAccountSweepSign = document.forms[0].elements["sweepAccount1.alignedSign"].value;
		subAccountSweepSign = document.forms[0].elements["sweepAccount2.alignedSign"].value;
		
		if (originalSweepAmount < 0) {
			originalSweepAmount = (originalSweepAmount * (-1));
			document.forms[0].elements[globalAmt].value = (originalSweepAmount * (-1));
		} else {
			document.forms[0].elements[globalAmt].value = originalSweepAmount;
		}
		document.forms[0].elements[globalAmtAsString].value=originalSweepAmount;			
		validateDisplayOriginal(document.forms[0].elements[globalAmtAsString],'label1', currencyFormat, currencyCode);
		calculateMainRealignedExternalBalance(originalSweepAmount);
		calculateMainRealignedPredictedBalance(originalSweepAmount);
		
		calculateSubRealignedExternalBalance(originalSweepAmount);
		calculateSubRealignedPredictedBalance(originalSweepAmount);
		if(arrowflag==false)
			enableDisableCheckbox();
	  
  }
  function alignNo() {
	  if(arrowflag==false)
			enableDisableCheckboxForNo();
	  
  }

function enableDisableCheckbox()
{	
		
		if(document.forms[0].elements["sweepAccount1.alignedSign"].value=="Credit")
			document.getElementById("arrowbuttonimage").src = "images/leftarrowblue.GIF";
		else
			document.getElementById("arrowbuttonimage").src = "images/rightarrowblue.GIF";	
		
			
		if(document.forms[0].elements[ "sweepAccount1.alignToTarget"].checked)
		{
			
			
			if(checkflag==false)
			{
			document.forms[0].elements["sweepAccount1.alignedSign"].value="Credit";
			document.forms[0].elements["sweepAccount2.alignedSign"].value="Debit";
			}
				

			document.forms[0].elements[ "sweepAccount1.alignToTarget"].disabled = true;
			document.forms[0].elements[ "sweepAccount2.alignToTarget"].disabled = false;
		}
		
		if(document.forms[0].elements[ "sweepAccount2.alignToTarget"].checked)
		{
				
			
			if(checkflag==false)
			{
			document.forms[0].elements["sweepAccount2.alignedSign"].value="Credit"
			document.forms[0].elements["sweepAccount1.alignedSign"].value="Debit"
			}
			
			document.forms[0].elements[ "sweepAccount2.alignToTarget"].disabled = true;
			document.forms[0].elements[ "sweepAccount1.alignToTarget"].disabled = false;
		}			

}




	function enableDisableCheckboxForNo()
		{

			var imgvalue = document.getElementById("arrowbuttonimage").src.substring(50)
     
			if(imgvalue=="red.GIF"||imgvalue=="wred.GIF"){
				document.forms[0].elements[ "sweepAccount2.alignToTarget"].checked = false;
				document.forms[0].elements[ "sweepAccount1.alignToTarget"].checked = false;
			}
			if(document.forms[0].elements[ "sweepAccount1.alignToTarget"].checked &&imgvalue!="red.GIF"&&imgvalue!="wred.GIF")
			{
				document.getElementById("arrowbuttonimage").src = "images/leftarrowblue.GIF";
				if(checkflag==false)
				{		
					document.getElementById("arrowbuttonimage").src = "images/rightarrowblue.GIF";	
					document.forms[0].elements["sweepAccount2.alignedSign"].value="Credit";
					document.forms[0].elements["sweepAccount1.alignedSign"].value="Debit"
				}
				document.forms[0].elements[ "sweepAccount2.alignToTarget"].checked = true;	
				document.forms[0].elements[ "sweepAccount1.alignToTarget"].checked = false;					
			
			}
			else if(imgvalue!="red.GIF" && imgvalue!="wred.GIF")
			{
			
				document.getElementById("arrowbuttonimage").src = "images/rightarrowblue.GIF";	
				if(checkflag==false)
				{	
					document.getElementById("arrowbuttonimage").src = "images/leftarrowblue.GIF";
					document.forms[0].elements["sweepAccount1.alignedSign"].value="Credit";
					document.forms[0].elements["sweepAccount2.alignedSign"].value="Debit";
				}
				document.forms[0].elements[ "sweepAccount1.alignToTarget"].checked = true;	
				document.forms[0].elements[ "sweepAccount2.alignToTarget"].checked = false;	
			}	

			

		}

  
  
function showSweepNotes(methodName){

document.forms[0].selectedSweepId.value = sweepId;
var param = 'notes.do?method='+methodName+'&selectedSweepId=';			 
param += document.forms[0].selectedSweepId.value;
param += '&currencyAccess=' + currencyAccessInd;
param += '&entityCode=' + document.forms[0].elements['sweepAmount.entityId'].value;
param += '&archiveId='+ document.forms[0].archiveId.value;

return  param;
	
}
function cutOffViolationYes() {
	if(sweepLevel=="org" && parentMethod == "manualsweeping"){
		document.forms[0].method.value = "sweep";
		document.forms[0].checkCutOff.value = "no";
		sweep="sweepAmount.origSweepAmt";
	 }
	 if(sweepLevel=="org" && parentMethod == "sweeppriorcutoff"){
		document.forms[0].method.value = "sweep";
		document.forms[0].calledFrom.value="sweeppriorcutoff";
		document.forms[0].checkCutOff.value = "no";
		sweep="sweepAmount.origSweepAmt";
	 }
	 if(sweepLevel=="submit"){
		 document.forms[0].currentSweepId.value=sweepId;
		document.forms[0].method.value = "submit";
		document.forms[0].checkCutOff.value = "no";
		document.forms[0].process.value = "submit";
		sweep="sweepAmount.subSweepAmt";
	 }

		
	 if(sweepLevel=="auth"){
		 document.forms[0].currentSweepId.value=sweepId;
		document.forms[0].method.value = "submit";
		document.forms[0].checkCutOff.value = "no";
		document.forms[0].process.value = "authorise";
		sweep="sweepAmount.authSweepAmt";
	 }
		if((sweepLevel=="submit") || (sweepLevel=="auth")){
						document.forms[0].elements["sweepAccount1.alignToTarget"].disabled="";
               			 document.forms[0].elements["sweepAccount2.alignToTarget"].disabled="";
		}
		
		<s:if test='"org"==#request.sweep' >
		document.getElementById("sweepOkButton").innerHTML = document.getElementById("sweepOkdisablebutton").innerHTML;
		</s:if>
		enableFields();
		document.forms[0].submit();
	
}
function displayList() {
	window.opener.document.forms[0].method.value="displayListByEntity";
	window.opener.setTabInfo();
	window.opener.document.forms[0].submit();
	self.close();
	
}
function displaySweep() {
	window.opener.document.forms[0].method.value="displaySweepDetails";
	window.opener.document.forms[0].submit();
	self.close();
}
function closeWindow() {
	self.close();
}


function getRadioButtonValue(button){
	var d = button;
	var len = d.length;
	for(i=0;i<len;i++)
	{
		if(d[i].checked)
		{
		 return d[i].value;
		}
	}
	if(i == len)
	 return "null";
	}
	
function onSweepScheduleRadioButtonClick(){
	var selectedType = getRadioButtonValue(document.forms[0].elements['sweepAmount.selectedScheduleSweepOption']);
	if(lastSelectedScheduleSweepOption != selectedType){
		ShowErrMsgWindowWithBtn('', '<s:text name="sweepDetail.sweepSetting.warning.bookandsettReverted"/>', YES_NO, yesAcceptRadioChange,noAcceptRadioChange);
	}
	
}
	
function yesAcceptRadioChange(){
	var selectedType = getRadioButtonValue(document.forms[0].elements['sweepAmount.selectedScheduleSweepOption']);
	document.forms[0].calledFrom.value="manualsweeping";
	document.forms[0].method.value = "manual";
	document.forms[0].selectedSweepScheduleSetting.value = selectedType;
	enableFields();
	document.forms[0].selectedList.value= document.forms[0].elements['sweepAmount.selectedList'].value;
	document.forms[0].submit();
					
}
function noAcceptRadioChange(){
	document.forms[0].elements['sweepAmount.selectedScheduleSweepOption'].value = lastSelectedScheduleSweepOption; 
}

</SCRIPT>
</head>

<body leftmargin="0" topmargin="0" marginheight="0"
	onLoad="bodyonload();setParentChildsFocus();ShowErrMsgWindow('${actionError}');setTitleSuffix(document.forms[0]);"
	onunload="call()">
	
	
<s:form action="sweepdetail.do">
	<input name="method" type="hidden" value="">
	<input name="currentSweepId" type="hidden" value="">
	<input name="process" type="hidden" value="">
	<input name="checkCutOff" type="hidden" value="yes">
	<input name="selectedSweepId" type="hidden" value="yes">
	<input name="selectedList" type="hidden" value="">
	<input name="calledFrom" type="hidden" value="">
	<input name="selectedSweepScheduleSetting" type="hidden" value="">
	<input name="archiveId" type="hidden" value="${archiveId}">
	


<%-- 	<s:hidden name='sweepAccount1.entityId' /> --%>
<%-- 	<s:hidden name='sweepAccount2.entityId' /> --%>
	<s:hidden name='sweepAccount1.targetBalance' />
	<s:hidden name='sweepAccount2.targetBalance' />
	<s:hidden name='sweepAccount1.predictBalance' />
	<s:hidden name='sweepAccount2.predictBalance' />
	<s:hidden name='sweepAccount1.minSweepAmt' />
	<s:hidden name='sweepAccount2.minSweepAmt' />
	<s:hidden name='sweepAccount1.maxSweepAmt' />
	<s:hidden name='sweepAccount2.maxSweepAmt' />
	<s:hidden name='sweepAccount1.messageTypeCr' />
	<s:hidden name='sweepAccount2.messageTypeCr' />
	<s:hidden name='sweepAccount1.messageTypeDr' />
	<s:hidden name='sweepAccount2.messageTypeDr' />
	<s:hidden name='sweepAccount1.authFlagCr' />
	<s:hidden name='sweepAccount2.authFlagCr' />
	<s:hidden name='sweepAccount1.authFlagDr' />
	<s:hidden name='sweepAccount2.authFlagDr' />
	<s:hidden name='sweepAccount1.subAcctim' />
	<s:hidden name='sweepAccount2.subAcctim' />
	<s:hidden name='sweepAccount1.externalBalance' />
	<s:hidden name='sweepAccount2.externalBalance' />
	
	<s:hidden name='sweepAccount1.defaultSettleMethodDr' />
	<s:hidden name='sweepAccount2.defaultSettleMethodDr' />
	<s:hidden name='sweepAccount1.defaultSettleMethodCr' />
	<s:hidden name='sweepAccount2.defaultSettleMethodCr' />
	
	<s:hidden name='sweepAccount1.bookcodeDr' />
	<s:hidden name='sweepAccount2.bookcodeDr' />
	<s:hidden name='sweepAccount1.bookcodeCr' />
	<s:hidden name='sweepAccount2.bookcodeCr' />
	
	<s:set var="CDM" value="#request.session.CDM" />
	<div id="dropdowndiv_2"
		style="position: absolute; width: 223px; left: 675px; top: 234px; visibility: hidden; z-index: 100"
		class="swdropdown"><s:select cssClass="htmlTextFixed" id="sweepAccount2.bookcode" name="sweepAccount2.bookcode" size="7" cssStyle="width:265px;z-index:99" list="#{'':''}" listKey="value" listValue="label" /></div>
	<div id="dropdowndiv_3"
		style="position: absolute; width: 265px; left: 185px; top: 234px; visibility: hidden; z-index: 100"
		class="swdropdown"><s:select cssClass="htmlTextFixed" id="sweepAccount1.bookcode" name="sweepAccount1.bookcode" size="7" cssStyle="width:265px;z-index:99" list="#{'':''}" listKey="value" listValue="label" /></div>


	<div id="MovementMatch"
		style="position: absolute; left: 5px; top: 10px; width: 980px; height: 570px; border: 2px outset;"
		color="#7E97AF">


<div style="left:8px; top:4px;">
	<table width="100%">
		<tr>
			<td>

			<div style="left:8px; top:0px; height: 312px;" >
			<fieldset style="border: 2px groove;"><legend><s:text name="tooltip.sweepAccount" /></legend>
			<table width="100%" border="0" cellpadding="0" cellspacing="1">
				<tr>

					<td width="15%" height="10px" align="left"><b>&nbsp;<s:text name="account.entityId" /></b></td>
					<td height="10px" width="16%" align="left"><s:textfield tabindex="-1" cssClass="textlabelalpha"
						name="sweepAccount1.entityId" style="width:220px;"
						readonly="true"  /></td>


				</tr>
				<tr>

					<td width="15%" height="10px" align="left"><b>&nbsp;<s:text name="acctMaintenance.accountId" /></b></td>
					<td height="10px" width="16%" align="left"><s:textfield tabindex="-1" cssClass="textlabelalpha"
						name="sweepAccount1.accountId" style="width:220px;"
						readonly="true"  /></td>
					<td width="3%">&nbsp;&nbsp;</td>
					<td width="5%"><s:textfield tabindex="-1"
						cssClass="textlabelalpha" name="sweepAccount1.accountType"
						style="width:35px;" readonly="true" /></td>


				</tr>


				<tr>
					<td width="15%" height="10px" align="left"><b>&nbsp;<s:text name="acctMaintenance.acctname" /></b></td>

					<td height="10px" width="25%" align="left"><s:textfield tabindex="-1" cssClass="textlabelalpha"
						name="sweepAccount1.acctname" style="width:250px;"
						readonly="true"  /></td>


				</tr>
				<tr>
					<td width="50%" height="10px" align="left"><b>&nbsp;<s:text name="SweepFromBalance" /></b></td>

					<td height="10px" width="25%" align="left"><s:textfield tabindex="-1" cssClass="textlabelalpha"
						name="sweepAccount1.sweepFrmbal" style="width:250px;"
						readonly="true"  /></td>


				</tr>


				<tr>


					<td width="18%" height="10px" align="left"><b>&nbsp;<s:text name="accountmonitor.prbalance" /></b></td>
					<td height="10px" width="16%" align="left"><s:textfield tabindex="-1" cssClass="textlabelalpha"
						name="sweepAccount1.predictBalanceAsString"
						style="width:160px;text-align:right" readonly="true"  /> <s:textfield tabindex="-1" cssClass="textlabelalpha"
						name="sweepAccount1.predictBalanceSign" style="width:20px;"
						readonly="true"  /></td>

				</tr>

				<tr>


					<td width="18%" height="10px" align="left"><b>&nbsp;<s:text name="accountmonitor.erBalance" /></b></td>
					<td height="10px" width="16%" align="left"><s:textfield tabindex="-1" cssClass="textlabelalpha"
						name="sweepAccount1.externalBalanceAsString"
						style="width:160px;text-align:right" readonly="true"  /> <s:textfield tabindex="-1" cssClass="textlabelalpha"
						name="sweepAccount1.externalBalanceSign" style="width:20px;"
						readonly="true"  /></td>

				</tr>


				<tr>

					<td width="15%" height="10px" align="left"><b>&nbsp;<s:text name="sweepDetail.minSweepAmt" /></b></td>
					<td height="10px" width="16%" align="left"><s:textfield tabindex="-1" cssClass="textlabelalpha"
						name="sweepAccount1.minSweepAmtAsString"
						style="width:160px;text-align:right" readonly="true"  /></td>
				</tr>
				<tr>
					<td width="15%" height="10px" align="left"><b>&nbsp;<s:text name="sweepDetail.maxSweepAmt" /></b></td>
					<td height="10px" width="16%" align="left"><s:textfield tabindex="-1" cssClass="textlabelalpha"
						name="sweepAccount1.maxSweepAmtAsString"
						style="width:160px;text-align:right" readonly="true"  /></td>
				</tr>
				<tr>

					<td width="18%" height="10px" align="left"><b>&nbsp;<s:text name="sweepDetail.trbalancetype" /></b></td>
					<td height="10px" width="16%" align="left"><s:textfield tabindex="-1" cssClass="textlabelalpha"
						name="sweepAccount1.targetBalanceTypeAsString"
						style="width:65px;text-align:left" readonly="true"  /> <s:textfield tabindex="-1" cssClass="textlabelalpha"
						name="sweepAccount1.targetBalanceTypeId" style="width:170px;"
						readonly="true"  /></td>

				</tr>
				<tr>

					<td width="18%" height="10px" align="left"><b>&nbsp;<s:text name="sweepDetail.trbalance" /></b></td>
					<td height="10px" width="16%" align="left"><s:textfield tabindex="-1" cssClass="textlabelalpha"
						name="sweepAccount1.targetBalanceAsString"
						style="width:160px;text-align:left" readonly="true"  /> <s:textfield tabindex="-1" cssClass="textlabelalpha"
						name="sweepAccount1.targetBalanceSign" style="width:20px;"
						readonly="true"  /></td>

				</tr>

				<tr>

					<td width="15%" height="10px" align="left"><b>&nbsp;<s:text name="currency.cutOffTime1" /></b></td>
					<td height="10px" width="16%" align="left"><s:textfield tabindex="-1" cssClass="textlabelalpha"
						name="sweepAccount1.cutOffTime" style="width:100px;"
						readonly="true"  /></td>

				</tr>
				<!--Start:Code modified by sudhakar For Mantis 1838:Message format not displayed accordingly when sweep direction changed-->
				<tr>

					<td width="15%" height="10px" align="left"><b>&nbsp;<s:text name="sweep.messageType" /></b></td>
					<td id="messageTypeAccount1" colspan="3" height="10px" width="16%"
						align="left"><s:textfield tabindex="-1"
						cssClass="textlabelalpha" name="sweepAccount1.message"
						style="width:297px;" readonly="true" /></td>




				</tr>
				</table>
				<table width="100%" border="0" cellpadding="0" cellspacing="1">
				<tr>

					<td width="150px" height="10px" align="left"><b>&nbsp;<s:text name="movement.bookcode" /></b></td>
					<td height="10px" width="270px" align="left">
					
<%-- 					<s:textfield --%>
<%-- 						tabindex="-1" cssClass="textlabelalpha" --%>
<%-- 						name="sweepAccount1.bookcode" style="width:120px;" --%>
<%-- 						readonly="true" /> --%>
 <img  id="lockUnlockImgBookMain" onclick="changeIcon('bookMain')" width="18" height="18" src='images/lock.png'	style="position: absolute; top: 250px; left: 161px;"		border="0" title="">
<input id="bookCodeId" name="bookCodeId" class="textAlpha" style="margin-right: 5px; height: 22px;"
				readonly="readonly" size="12" title='<s:text name = "tooltip.bookCode"/>'><input
				tabindex="20" id="dropdownbutton_3" type="button" value="..."
				onclick="getBookCodeList(event)" disabled=""
				title='<s:text name = "tooltip.bookCode"/>'>
							<input class="textAlpha"
				style="background: transparent; width:160px; border: thin; text-align: top; height: 19;"
				name="bookCodeNameDisp" size="25">
						</td>



				</tr>
				<tr>

					<td width="150px" height="10px" align="left"><b>&nbsp;<s:text name="sweepDetail.authQueue" /></b></td>
					<td height="10px" width="270px" align="left"><s:textfield tabindex="-1" cssClass="textlabelalpha"
						name="sweepAccount1.authQueue" style="width:20px;"
						readonly="true"  /></td>



				</tr>
				<tr>

					<td width="150px" height="10px" align="left"><b>&nbsp;<s:text name="account.settleMethodSweep" /></b></td>
					<td width="270px">
					<img  id="lockUnlockImgSettMethod" onclick="changeIcon('settMethodFirst')" width="18" height="18" src='images/lock.png'	style="position: absolute; top: 290px; left: 161px;"		border="0" title="">
					<s:select id="sweepAccount1.defaultSettleMethod" name="sweepAccount1.defaultSettleMethod" tabindex="25" styledI="defaultSettMethod1" titleKey="accountmaintenanceadd.defaultSettleMethodTooltip" cssStyle="width:165px;" list="#request.defaultSettleMethodList1" listKey="value" listValue="label" /></td>

				</tr>
			</table>
			</fieldset>
			</div>
			</td>
			<td>

			<div style="left:8px; top:4px;height: 312px;">

			<fieldset style="border: 2px groove;padding-top: 12px !important"><legend></legend>
			<table width="100%" border="0" cellpadding="0" cellspacing="1">
			
			
			
				<tr>
					<td width="18%" height="10px" align="left"><b>&nbsp;<s:text name="account.entityId" /></b></td>
					<td height="10px" width="16%" align="left"><s:textfield tabindex="-1" cssClass="textlabelalpha"
						name="sweepAccount2.entityId" style="width:220px;"
						readonly="true"  /></td>

				</tr>
				<tr>
					<td width="18%" height="10px" align="left"><b>&nbsp;<s:text name="acctMaintenance.accountId" /></b></td>
					<td height="10px" width="16%" align="left"><s:textfield tabindex="-1" cssClass="textlabelalpha"
						name="sweepAccount2.accountId" style="width:220px;"
						readonly="true"  /></td>
					<td width="3%">&nbsp;&nbsp;</td>
					<td width="5%"><s:textfield tabindex="-1"
						cssClass="textlabelalpha" name="sweepAccount2.accountType"
						style="width:35px;" readonly="true" /></td>

				</tr>


				<tr>
					<td height="10px" width="16%" align="left"><b>&nbsp;<s:text name="acctMaintenance.acctname" /></b></td>
					<td height="10px" width="25%" align="left"><s:textfield tabindex="-1" cssClass="textlabelalpha"
						name="sweepAccount2.acctname" style="width:250px;"
						readonly="true"  /></td>

				</tr>


				<tr>
					<td width="50%" height="10px" align="left"><b>&nbsp;<s:text name="SweepFromBalance" /></b></td>

					<td height="10px" width="25%" align="left"><s:textfield tabindex="-1" cssClass="textlabelalpha"
						name="sweepAccount2.sweepFrmbal" style="width:250px;"
						readonly="true"  /></td>


				</tr>

				<tr>
					<td width="15%" height="10px" align="left"><b>&nbsp;<s:text name="accountmonitor.prbalance" /></b></td>
					<td height="10px" width="16%" align="left"><s:textfield tabindex="-1" cssClass="textlabelalpha"
						name="sweepAccount2.predictBalanceAsString"
						style="width:160px;text-align:right" readonly="true"  /> <s:textfield tabindex="-1" cssClass="textlabelalpha"
						name="sweepAccount2.predictBalanceSign" style="width:20px;"
						readonly="true"  /></td>
				</tr>



				<tr>
					<td width="15%" height="10px" align="left"><b>&nbsp;<s:text name="accountmonitor.erBalance" /></b></td>
					<td height="10px" width="16%" align="left"><s:textfield tabindex="-1" cssClass="textlabelalpha"
						name="sweepAccount2.externalBalanceAsString"
						style="width:160px;text-align:right" readonly="true"  /> <s:textfield tabindex="-1" cssClass="textlabelalpha"
						name="sweepAccount2.externalBalanceSign" style="width:20px;"
						readonly="true"  /></td>
				</tr>



				<tr>
					<td width="15%" height="10px" align="left"><b>&nbsp;<s:text name="sweepDetail.minSweepAmt" /></b></td>
					<td height="10px" width="15%" align="left"><s:textfield tabindex="-1" cssClass="textlabelalpha"
						name="sweepAccount2.minSweepAmtAsString"
						style="width:160px;text-align:right" readonly="true"  /></td>
				</tr>
				<tr>
					<td width="15%" height="10px" align="left"><b>&nbsp;<s:text name="sweepDetail.maxSweepAmt" /></b></td>
					<td height="10px" width="15%" align="left"><s:textfield tabindex="-1" cssClass="textlabelalpha"
						name="sweepAccount2.maxSweepAmtAsString"
						style="width:160px;text-align:right" readonly="true"  />
				</tr>

				<tr>

					<td width="18%" height="10px" align="left"><b>&nbsp;<s:text name="sweepDetail.trbalancetype" /></b></td>
					<td height="10px" width="16%" align="left"><s:textfield tabindex="-1" cssClass="textlabelalpha"
						name="sweepAccount2.targetBalanceTypeAsString"
						style="width:65px;text-align:left" readonly="true"  /> <s:textfield tabindex="-1" cssClass="textlabelalpha"
						name="sweepAccount2.targetBalanceTypeId" style="width:170px;"
						readonly="true"  /></td>

				</tr>
				<tr>

					<td width="18%" height="10px" align="left"><b>&nbsp;<s:text name="sweepDetail.trbalance" /></b></td>
					<td height="10px" width="16%" align="left"><s:textfield tabindex="-1" cssClass="textlabelalpha"
						name="sweepAccount2.targetBalanceAsString"
						style="width:160px;text-align:left" readonly="true"  /> <s:textfield tabindex="-1" cssClass="textlabelalpha"
						name="sweepAccount2.targetBalanceSign" style="width:20px;"
						readonly="true"  /></td>

				</tr>


				<tr>
					<td width="15%" height="10px" align="left"><b>&nbsp;<s:text name="currency.cutOffTime1" /></b></td>
					<td height="10px" width="15%" align="left"><s:textfield tabindex="-1" cssClass="textlabelalpha"
						name="sweepAccount2.cutOffTime" style="width:100px;"
						readonly="true"  /></td>

				</tr>

				<tr>
					<td width="15%" height="10px" align="left"><b>&nbsp;<s:text name="sweep.messageType" /></b></td>
					<td id="messageTypeAccount2" colspan="3" height="10px" width="15%"
						align="left"><s:textfield tabindex="-1"
						cssClass="textlabelalpha" name="sweepAccount2.message"
						style="width:297px;" readonly="true" /></td>

				</tr>
				<!--End:Code modified by sudhakar For Mantis 1838:Message format not displayed accordingly when sweep direction changed-->
				
				</table>
				<table width="100%" border="0" cellpadding="0" cellspacing="1">
				<tr>



					<td width="150px" height="10px" align="left"><b>&nbsp;<s:text name="movement.bookcode" /></b></td>
					<td height="10px" width="270px" align="left">
					
<%-- 					<s:textfield --%>
<%-- 						tabindex="-1" cssClass="textlabelalpha" --%>
<%-- 						name="sweepAccount2.bookcode" style="width:120px;" --%>
<%-- 						readonly="true" /> --%>
							<img  id="lockUnlockImgBookOther" onclick="changeIcon('bookOther')" width="18" height="18" src='images/lock.png'	style="position: absolute; top: 250px; left: 647px;"		border="0" title="">
						<input id="bookCodeIdOther" name="bookCodeIdOther" class="textAlpha" style="margin-right: 5px; height: 22px;"
				readonly="readonly" size="12" title='<s:text name = "tooltip.bookCode"/>'><input
				tabindex="20" id="dropdownbutton_2" type="button" value="..."
				onclick="getBookCodeListSecond(event)" disabled=""
				title='<s:text name = "tooltip.bookCode"/>'>
						<input class="textAlpha"
				style="background: transparent; width:160px; border: thin; text-align: top; height: 19;"
				name="bookCodeNameDispOther" size="25">
						
						
						</td>

				</tr>
				<tr>



					<td width="150px" height="10px" align="left"><b>&nbsp;<s:text name="sweepDetail.authQueue" /></b></td>
					<td height="10px" width="270px" align="left"><s:textfield tabindex="-1" cssClass="textlabelalpha"
						name="sweepAccount2.authQueue" style="width:20px;"
						readonly="true"  /></td>

				</tr>

				<tr>

					<td width="150px" height="10px" align="left"><b>&nbsp;<s:text name="account.settleMethodSweep" /></b></td>
					<td width="270px">
					<img  id="lockUnlockImgSettSecond" onclick="changeIcon('settMethodSecond')" width="18" height="18" src='images/lock.png'	style="position: absolute; top: 290px; left: 647px;"		border="0" title="">
					
					<s:select id="defaultSettMethod2"  name="sweepAccount2.defaultSettleMethod" tabindex="25" titleKey="accountmaintenanceadd.defaultSettleMethodTooltip" cssStyle="width:165px;" list="#request.defaultSettleMethodList2" listKey="value" listValue="label" /></td>

				</tr>
			</table>
			</fieldset>
			</div>
			</td>
		</tr>
	</table>
	</div>



	<div id="MovementMatch"
		style="position: absolute;  top: 315px; width: 980px; height: 40px;"
		color="#7E97AF">

	<table width="975" border="0">
		<tr>
			<td>
			<div style="left:8px; top:4px;">
			<fieldset style="border: 2px groove;">

			
			<table width="100%" border="0">
				<tr>
					<s:hidden name='sweepAccount1.reAlignedBalance' />
					<s:hidden name='sweepAccount2.reAlignedBalance' />
					<td width="47%" height="10px" align="left"><b><s:text name="sweepDetail.realignBal" /></b></td>
					<td height="10px" width="53%" align="left">
					<div
						style="position: relative; left: 0px; top: 0px; width: 200px; height: 20px; border: 2px outset;">
					<s:textfield tabindex="-1" cssClass="textlabelalpha"
						name="sweepAccount1.reAlignedBalanceAsString"
						style="width:160px;text-align:right" readonly="true" /> <s:textfield tabindex="-1" cssClass="textlabelalpha"
						name="sweepAccount1.reAlignedBalanceSign" style="width:20px;"
						readonly="true"  /></div>
					</td>
				<tr>
					<s:hidden name='sweepAccount1.reexAlignedBalance' />
					<s:hidden name='sweepAccount2.reexAlignedBalance' />
					<td width="47%" height="10px" align="left"><b><s:text name="sweepDetail.exalignToTarget" /></b></td>
					<td height="10px" width="53%" align="left">
					<div
						style="position: relative; left: 0px; top: 0px; width: 200px; height: 20px; border: 2px outset;">
					<s:textfield tabindex="-1" cssClass="textlabelalpha"
						name="sweepAccount1.reexAlignedBalanceAsString"
						style="width:160px;text-align:right" readonly="true" /> <s:textfield tabindex="-1" cssClass="textlabelalpha"
						name="sweepAccount1.reexAlignedBalanceSign"
						style="width:20px;" readonly="true"  /></div>
					</td>


				</tr>


			</table>
			</fieldset>
			</div>

			</td>
			<td>
			<div style="left:8px; top:4px;">
			<fieldset style="border: 2px groove;">

			
			<table width="100%" border="0">
				<tr>
					<td width="47%" height="10px" align="left"><b><s:text name="sweepDetail.realignBal" /></b></td>
					<td height="10px" width="53%" align="left">
					<div
						style="position: relative; left: 0px; top: 0px; width: 200px; height: 20px; border: 2px outset;">
					<s:textfield tabindex="-1" cssClass="textlabelalpha"
						name="sweepAccount2.reAlignedBalanceAsString"
						style="width:160px;text-align:right" readonly="true" /> <s:textfield tabindex="-1" cssClass="textlabelalpha"
						name="sweepAccount2.reAlignedBalanceSign" style="width:20px;"
						readonly="true"  /></div>
					</td>

				</tr>
				<tr>
					<td width="47%" height="10px" align="left"><b><s:text name="sweepDetail.exalignToTarget" /></b></td>
					<td height="10px" width="53%" align="left">
					<div
						style="position: relative; left: 0px; top: 0px; width: 200px; height: 20px; border: 2px outset;">
					<s:textfield tabindex="-1" cssClass="textlabelalpha"
						name="sweepAccount2.reexAlignedBalanceAsString"
						style="width:160px;text-align:right" readonly="true" /> <s:textfield tabindex="-1" cssClass="textlabelalpha"
						name="sweepAccount2.reexAlignedBalanceSign"
						style="width:20px;" readonly="true"  /></div>
					</td>

				</tr>

			</table>
			</fieldset>
			</div>
			</td>
		</tr>
	</table>
	</div>





	<div id="MovementMatch"
		style="position: absolute; top: 374px; width: 980px; height: 40px;"
		color="#7E97AF">

	<table width="977" border="0">
		<tr>
			<td>

			<div style="left:8px; top:4px;">
			<fieldset style="border: 2px groove;">

			<table width="98%" border="0">
				<tr>
					<td width="40%"><b><s:text name="sweepDetail.alignToTarget" /></b></td>
					<td width="45%"><s:if test='"org"==#request.sweep' >
						<s:checkbox titleKey="tooltip.sweepCheck" name="sweepAccount1.alignToTarget" fieldValue="Y" value='%{#request.sweepAccount1.alignToTarget == "Y"}'  />
					</s:if> <s:if test='"org"!=#request.sweep' >
						<s:checkbox name="sweepAccount1.alignToTarget" fieldValue="Y" value='%{#request.sweepAccount1.alignToTarget == "Y"}' disabled="true"  />
					</s:if></td>
					<td width="15%" align="right"></td>


				</tr>

			</table>
			</fieldset>
			</div>
			</td>

			<td>

			<div style="left:8px; top:4px;">
			<fieldset style="border: 2px groove;">

			<table width="98%" border="0">
				<tr>

					<td width="40%"><b><s:text name="sweepDetail.alignToTarget" /></b></td>
					<td width="45%"><s:if test='"org"==#request.sweep' >
						<s:checkbox titleKey="tooltip.sweepCheck" name="sweepAccount2.alignToTarget" fieldValue="Y" value='%{#request.sweepAccount2.alignToTarget == "Y"}'  />
					</s:if> <s:if test='"org"!=#request.sweep' >
						<s:checkbox name="sweepAccount2.alignToTarget" fieldValue="Y" value='%{#request.sweepAccount2.alignToTarget == "Y"}' disabled="true"  />
					</s:if></td>
					<td width="15%" align="right"></td>

				</tr>

			</table>
			</fieldset>
			</div>
			</td>
		</tr>


	</table>

	<table border="0" width="100%">
		<tr>
			<td width="25%" align="left" style="padding-left:9px" valign="middle">
				<b><s:text name="sweepDetails.unlockAll" /></b>
				<img  id="lockUnlockAll" onclick="changeIcon('all')" width="18" height="18" src='images/lock.png'	style="position: absolute; top: 36px; left: 84px;"		border="0" title="">
			</td>
			<td width="24%" align="right" valign="middle"><s:textfield tabindex="-1" cssClass="textlabelalpha"
				name="sweepAccount1.alignedSign" style="width:45px;"
				readonly="true"  /></td>

			<td><a tabindex="1" href=# onclick="javascript:sweepArrow()"
				onMouseOut="MM_swapImgRestore()"><img id="arrowbuttonimage"
				src="images/leftarrowblue.GIF" name="" border="0"></a></td>
			<td width="49%">&nbsp;&nbsp;<s:textfield tabindex="-1"
				cssClass="textlabelalpha" name="sweepAccount2.alignedSign"
				style="width:45px;" readonly="true" /></td>
		</tr>
	</table>

	</div>



	<div id="MovementMatch"
		style="position: absolute; left: 4px; top: 435px; width: 970px; height: 127px;"
		color="#7E97AF">
		<div style="left:8px; top:4px;">
	<fieldset style="border: 2px groove;">
	<table width="100%" border="0">
		<tr>
			<td width="45%">
	
	<table  border="0">
		<s:hidden name='sweepAccount1.currCode' />
		<s:hidden name='sweepAccount2.currCode' />
		<s:hidden name='sweepAmount.entityId' />
		<s:hidden name='sweepAmount.scheduleSweepExist' />
		<s:hidden name='sweepAmount.scheduleSweepApplyed' />
		<s:hidden name='sweepAmount.scheduleSweepDetailsAsString' />
		<s:hidden name='sweepAmount.selectedScheduleSweepOption' />
		<s:hidden name='sweepAmount.selectedList' />
		<tr>
			<s:hidden name='sweepAmount.origSweepAmt' />
			<td width="10%" height="10px" align="left"><b><s:text name="sweepDetail.orgSweepAmt" /></b></td>
			<td height="10px" width="10%" align="left"><s:if test='"org"==#request.sweep' >
				<s:textfield titleKey="tooltip.changeOriginalSweepAmount"
					maxlength="28" tabindex="1" cssClass="textalpha"
					name="sweepAmount.origSweepAmtAsString"
					style="width:165px;text-align:right"
					id="amountInput"
					onchange="validateThis(this,'orgAmt',currencyFormat,document.forms[0].elements['sweepAccount1.currCode'].value);" />
			</s:if> <s:if test='"org"!=#request.sweep' >
				<s:textfield tabindex="-1" cssClass="textalpha"
					name="sweepAmount.origSweepAmtAsString"
					style="width:165px;background-color:transparent;text-align:right"
					id="amountInput"
					readonly="true" />
			</s:if></td>
		</tr>
		<tr>
			<s:hidden name='sweepAmount.subSweepAmt' />

			<td width="15%" height="10px" align="left"><b><s:text name="sweepDetail.subSweepAmt" /></b></td>
			<td height="10px" width="10%" align="left"><s:if test='"submit"==#request.sweep' >
				<s:textfield titleKey="tooltip.changeSubmitSweepAmount" tabindex="1"
					maxlength="28" cssClass="textalpha"
					name="sweepAmount.subSweepAmtAsString"
					style="width:165px;text-align:right"
					onchange="validateThis(this,'orgAmt',currencyFormat,document.forms[0].elements['sweepAccount1.currCode'].value);" />
			</s:if> <s:if test='"submit"!=#request.sweep' >
				<s:textfield tabindex="-1" cssClass="textalpha"
					name="sweepAmount.subSweepAmtAsString"
					style="width:165px;background-color:transparent;text-align:right"
					readonly="true" />
			</s:if></td>
		</tr>
		<tr>
			<s:hidden name='sweepAmount.authSweepAmt' />
			<td width="15%" height="10px" align="left"><b><s:text name="sweepDetail.authSwepAmt" /></b></td>
			<td height="10px" width="10%" align="left"><s:if test='"auth"==#request.sweep' >
				<s:textfield titleKey="tooltip.changeAuthSweepAmount" tabindex="1"
					maxlength="28" cssClass="textalpha"
					name="sweepAmount.authSweepAmtAsString"
					style="width:165px;text-align:right"
					onchange="validateThis(this,'orgAmt',currencyFormat,document.forms[0].elements['sweepAccount1.currCode'].value);" />
			</s:if> <s:if test='"auth"!=#request.sweep' >
				<s:textfield tabindex="-1" cssClass="textalpha"
					name="sweepAmount.authSweepAmtAsString"
					style="width:165px;text-align:right;background-color:transparent"
					readonly="true" />
			</s:if></td>
		</tr>
		<tr>
			<td width="15%" height="10px" align="left"><b><s:text name="sweepDetail.valueDate" /></b></td>
			<td height="10px" width="10%" align="left"><s:textfield tabindex="-1" cssClass="textlabelalpha"
				name="sweepAmount.valueDateAsString"  style="width:100px;"
				readonly="true"  /></td>
		</tr>
		
	</table>
	</td><td style="    display: block;">
<%-- 	<s:if test='"true"==#request.scheduleSweepExist' > --%>
		<fieldset id="fieldSetSchedule"  style="border: 2px groove;display:none"><legend><s:text name="sweepDetail.sweepSetting" /></legend>
	<table border="0">
	

	<tr>
	<td>
	<s:radio style="margin-right:10" titleKey="sweepDetail.sweepSetting.useSchedule.tooltip"
						name="sweepAmount.selectedScheduleSweepOption" disabled="true" id="useScheduleRadioButon" list="#{'Account':''}" tabindex="5" onclick="onSweepScheduleRadioButtonClick();"></s:radio></td>
						
	<td>
	<label id="scheduleLabel" title='<s:text name="sweepDetail.sweepSetting.useSchedule.tooltip"/>'
							for="4"><s:text name="sweepDetail.sweepSetting.useSchedule" /></label>
							
	</td>
	</tr>
	<tr>
	<td>				
	<s:radio style="margin-right:10" id="useAccountDefaultRadioButon" titleKey="sweepDetail.sweepSetting.useSchedule.tooltip"
						name="sweepAmount.selectedScheduleSweepOption" disabled="true"  list="#{'Default':''}"  tabindex="5" onclick="onSweepScheduleRadioButtonClick();"></s:radio>


	</td>
						
	<td>
	<label title='<s:text name="sweepDetail.sweepSetting.useSchedule.tooltip"/>'
							for="4"><s:text name="sweepDetail.sweepSetting.useAccountDefault" /></label>
							
	</td>
	</tr>	
	</table>
	</fieldset>
<%-- 		</s:if> --%>
	</td></tr>
	</table>

	<table width="100%" style="    margin-top: -7px" border="0">
		<tr>
		
		
			<td width="182px" height="10px" align="left"><b><s:text name="sweepDetail.additionalRef" /></b></td>
			<td height="10px"  align="left">
<%-- 			<%  if (valEqualsOne) {  %> --%>
			<img  id="lockUnlockImgAddInfo" onclick="changeIcon('addInfo')" width="18" height="18" src='images/lock.png'	style="position: absolute;  left: 176px;"		border="0" title="">
			<s:textfield tabindex="-1" cssClass="htmlTextAlpha" 
				name="sweepAmount.additionalReference" style="width:400px;" />
			
<%-- 			<% } else { %> --%>
<%-- 				<s:textfield tabindex="-1" cssClass="htmlTextAlpha" --%>
<%-- 				 readonly="true" name="sweepAmount.additionalReference" style="width:165px;background-color:transparent;" /> --%>
				 
<%-- 			 <% } %> --%>
			 </td>
		</tr>

	</table>
	</fieldset>
	</div>
	</div>
	</div>



	<div id="MovementMatch"
		style="position: absolute; left: 760; top: 593px; width: 220; height: 15px; visibility: visible;">

	<table align="right" border="0" cellspacing="0" cellpadding="0"
		height="20">

		<tr>

			<td align="Right"><a tabindex="5" href=#
				onclick="javascript:openWindow(buildPrintURL('print','Sweep Details'),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')"
				onMouseOut="MM_swapImgRestore()"
				onMouseOver="MM_swapImage('Help','','images/help_default.GIF ',1)"><img
				src="images/help_default.GIF " name="Help" border="0"
				title='<s:text name="tooltip.helpScreen"/>'></a></td>

			<td align="right" id="Print"><a tabindex="5"
				onclick="printPage();" onMouseOut="MM_swapImgRestore()"
				onMouseOver="MM_swapImage('Print','','images/Print_R.gif ',1)"><img
				src="images/Print.gif " name="Print" border="0"
				title='<s:text name="tooltip.printScreen"/>' /></a></td>
		</tr>
	</table>
	</div>



	<div id="ddimagebuttons" color="#7E97AF"
		style="position: absolute; border: 2px outset; left: 5; top: 585; width: 980; height: 39px; visibility: visible;">


	<div id="Currency"
		style="position: absolute; left: 6; top: 4; width: 730; height: 15px; visibility: visible;">
	<s:if test='"display"!=#request.sweep' >
		<table width="210" border="0" cellspacing="0" cellpadding="0"
			height="20">
			</s:if>
			<s:if test='"display"==#request.sweep' >
				<table width="140" border="0" cellspacing="0" cellpadding="0"
					height="20">
					</s:if>

					<tr>
						<s:if test='"org"==#request.sweep' >

							<td width="70" id="sweepOkButton"><a
								title='<s:text name="tooltip.sweepOk"/>' tabindex="2"
								onMouseOut="collapsebutton(this)"
								onMouseOver="highlightbutton(this)"
								onMouseDown="expandbutton(this)"
								onMouseUp="highlightbutton(this)"
								onClick="javascript:submitForm('sweep')"><s:text name="button.ok" /></a></td>

						</s:if>
						<s:if test='"submit"==#request.sweep' >
							<td width="70"><a
								title='<s:text name="tooltip.submit"/>' tabindex="2"
								onMouseOut="collapsebutton(this)"
								onMouseOver="highlightbutton(this)"
								onMouseDown="expandbutton(this)"
								onMouseUp="highlightbutton(this)"
								onClick="javascript:submitForm('sweep')"><s:text name="button.submit" /></a></td>
						</s:if>
						<s:if test='"auth"==#request.sweep' >
							<td width="70"><a
								title='<s:text name="tooltip.SubmitSelSweep"/>' tabindex="2"
								onMouseOut="collapsebutton(this)"
								onMouseOver="highlightbutton(this)"
								onMouseDown="expandbutton(this)"
								onMouseUp="highlightbutton(this)"
								onClick="javascript:submitForm('sweep')"><s:text name="button.save" /></a></td>
						</s:if>

						<td width="70"><a
							title='<s:text name="tooltip.enterNotes"/>' tabindex="3"
							onMouseOut="collapsebutton(this)"
							onMouseOver="highlightbutton(this)"
							onMouseDown="expandbutton(this)"
							onMouseUp="highlightbutton(this)"
							onClick="javascript:openWindow(showSweepNotes('showSweepNotes'),'sweepNotesWindow','left=170,top=210,width=585,height=275,toolbar=0, resizable=yes, scrollbars=yes')"><s:text name="button.notes" /></a></td>



						<td id="cancelbutton" width="70"><a
							title='<s:text name="tooltip.close"/>' tabindex="4"
							onMouseOut="collapsebutton(this)"
							onMouseOver="highlightbutton(this)"
							onMouseDown="expandbutton(this)"
							onMouseUp="highlightbutton(this)"
							onClick="javascript:window.close();"><s:text name="button.close" /></a></td>
					</tr>
				</table>

				<div
					style="position: absolute; left: 6; top: 4; width: 80px; height: 15px; visibility: hidden;">
				<table width="70" border="0" cellspacing="0" cellpadding="0"
					height="20 " style="visibility: hidden">
					<tr>
						<td id="sweepOkdisablebutton" width="70px"><a
							class="disabled" disabled="disabled"><s:text name="button.ok" /></a></td>
					</tr>
				</table>
				</div>


				</div>

				</s:form>
</body>

<script>

<s:if test='"no"==#request.parentFormRefresh' >
	<s:if test='"org"==#request.sweep' >
		<s:if test='"sweeppriorcutoff"==#request.parentMethod' >
		ShowErrMsgWindowWithBtn("", '<s:text name="alert.sweepDetail.saved"/>'+newSweepId, null, displaySweep);
		</s:if>
		<s:if test='"sweeppriorcutoff"!=#request.parentMethod' >
		ShowErrMsgWindowWithBtn("", '<s:text name="alert.sweepDetail.saved"/>'+newSweepId, null, displayList);

		</s:if>
	</s:if>
	<s:if test='"submit"==#request.sweep' >
	ShowErrMsgWindowWithBtn("", "Sweep ID "+newSweepId+'<s:text name="alert.sweepDetail.submitted"/>', null, closeWindow);
	</s:if>
	<s:if test='"auth"==#request.sweep' >
	ShowErrMsgWindowWithBtn("", "Sweep ID "+newSweepId+'<s:text name="alert.sweepDetail.authorised"/>', null, closeWindow);
	</s:if>
	<s:if test='"org"!=#request.sweep' >
		window.opener.document.forms[0].method.value="displayList";
		window.opener.document.forms[0].submit();
		self.close();
	</s:if>

</s:if>


<s:if test='"yes"==#request.cuttOff' >
	ShowErrMsgWindowWithBtn('', '<s:text name="sweepDetails.alert.sweeperror1"/>'+
	'<s:text name="recovery.confrim.continue"/>', YES_NO, cutOffViolationYes );
	
</s:if>
</script>
</html>