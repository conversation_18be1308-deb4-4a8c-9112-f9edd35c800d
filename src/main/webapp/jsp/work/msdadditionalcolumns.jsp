<!doctype html>
<%@ include file="/taglib.jsp"%>
<%@ page import="org.swallow.util.SwtConstants"%>
<%

	//variable declaration
	String profileId = "";

	//get the all params from request attribute 
	if (request.getAttribute("profileId") != null) {
		profileId = request.getAttribute("profileId").toString();
	}
	
	//variable declaration
	String source = "";

	//get the all params from request attribute 
	if (request.getAttribute("source") != null) {
		source = request.getAttribute("source").toString();
	}
%>

<html lang="en">
<head>
<meta charset="utf-8">
<title><s:text name = "msd.addCols.title"/></title>
<!-- <base href="./"> -->
<meta name="viewport" content="width=device-width, initial-scale=1">
<link rel="icon" type="image/x-icon" href="favicon.ico">
<link rel="stylesheet" href="angularSources/styles.css?version=<%= SwtUtil.appVersion %>">
</head>
<body onunload="closeAndUpdate()">
<%@ include file="/angularJSUtils.jsp"%>

<script type="text/javascript">
var appName = "<%=SwtUtil.appName%>";
var baseURL = new String('<%=request.getRequestURL()%>');
var profileId = "<%= profileId %>";
var source = "<%= source %>";
var screenRoute = "msdAdditionalColumns";

function closeHandler(){
	self.close();
}

function closeAndUpdate() {
	 Main.closeAndUpdate();
	}
</script>
<%@ include file="/angularscripts.jsp"%>
</body>
</html>