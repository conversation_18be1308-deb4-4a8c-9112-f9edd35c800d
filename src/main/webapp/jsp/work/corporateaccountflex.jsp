<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@ page import="org.swallow.util.SwtUtil"%>
<%@ include file="/taglib.jsp"%>
<%@ include file="/angularJSUtils.jsp"%>
<html>
<head>
<title><s:text name="corporateAccount.title.window" /></title>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
<link rel="icon" type="image/x-icon" href="favicon.ico">
<link rel="stylesheet" href="angularSources/styles.css?version=<%= SwtUtil.appVersion %>">

</head>
<style>
body {
	margin: 0px;
	overflow: hidden
}
</style>
<script type="text/javascript">

   window.onload = function () {
   }
  	 window.onunload = call;
  	 var refreshPending = false;
			
	var appName = "<%=SwtUtil.appName%>";
	var requestURL = new String('<%=request.getRequestURL()%>');
	var idy = requestURL.indexOf('/'+appName+'/');
	requestURL=requestURL.substring(0,idy+1) ;
	var dateFormat = '<s:property value="#request.session.CDM.dateFormatValue" />';
	var currencyFormat = '<s:property value="#request.session.CDM.currencyFormat"/>';
	var testDate= "<%=SwtUtil.getSystemDateString() %>";
	var selectedDate ="${requestScope.selectedDate}";
	var selectedEntityId ="${requestScope.selectedEntityId}";
	var selectedEntityName ="${requestScope.selectedEntityName}";
	var menuAccessIdParent = getMenuAccessIdOfChildWindow("centralBankMonitor.do");
	var screenRoute = "corporateAccount";
	/**
      * formatCurrency
      * 			
	  * This method is used to format the amount in system currency format (called by flex) 
	 */
	function formatCurrency(amount){
		var strAmount=formatCurrency_centralMonitor(amount, currencyFormat, null);
		return strAmount;
	 }
	 
	/*Start : Added by Imed B on 20-02-2014*/
	var label = new Array ();
	label["text"] = new Array ();
	label["tip"] = new Array ();

	label["text"]["label-corporateEntries"] = "<s:text name="corporateAccount.title.corporateEntries"/>";
	label["text"]["label-showXML"] = "<s:text name="screen.showXML"/>";
	label["text"]["label-recordExists"] = "<s:text name="corporateAccount.recordExists"/>";
	label["text"]["alert-warning"] = "<s:text name="screen.warning"/>";
	label["text"]["label-addCorporateEntries"] = "<s:text name="corporateAccount.addCorporateEntries"/>";
	label["text"]["label-wantToDelete"] = "<s:text name="corporateAccount.wantToDelete"/>";
	label["text"]["alert-delete"] = "<s:text name="corporateAccount.alertDelete"/>";
	label["text"]["label-changeCorporateEntries"] = "<s:text name="corporateAccount.changeCorporateEntries"/>";
	label["text"]["label-fillMondatoryFields"] = "<s:text name="corporateAccount.fillMondatoryFields"/>";
	label["text"]["alert-mandatory"] = "<s:text name="corporateAccount.alert.mandatory"/>";
	label["text"]["button-save"] = "<s:text name="corporateAccount.label.save"/>";
	label["tip"]["button-save"] = "<s:text name="corporateAccount.tooltip.save"/>";
	label["text"]["button-close"] = "<s:text name="corporateAccount.label.close"/>";
	label["tip"]["button-close"] = "<s:text name="corporateAccount.tooltip.close"/>";
	label["text"]["label-corporateName"] = "<s:text name="corporateAccount.corporateName"/>";
	label["text"]["label-amount"] = "<s:text name="corporateAccount.amount"/>";
	label["text"]["label-validAmount"] = "<s:text name="corporateAccount.validAmount"/>";
	label["text"]["alert-error"] = "<s:text name="screen.error"/>";
	label["text"]["button-add"] = "<s:text name="corporateAccount.label.add"/>";
	label["tip"]["button-add"] = "<s:text name="corporateAccount.tooltip.add"/>";
	label["text"]["button-change"] = "<s:text name="corporateAccount.label.change"/>";
	label["tip"]["button-change"] = "<s:text name="corporateAccount.tooltip.change"/>";
	label["text"]["button-delete"] = "<s:text name="corporateAccount.labelDelete"/>";
	label["tip"]["button-delete"] = "<s:text name="corporateAccount.tooltipDelete"/>";
	label["text"]["label-connectionError"] = "<s:text name="screen.connectionError"/>";
	label["text"]["label-buildInProgress"] = "<s:text name="screen.buildInProgress"/>";
	label["text"]["label-valueDate"] = "<s:text name="sweepDetail.valueDate"/>";						
	/*End : Added by Imed B on 20-02-2014*/		

	 /**
      * help
      * 			
	  * This method is used to open the help window for central bank monitor    
	  */
		function help(){
				openWindow(buildPrintURL('print','Corporate Account'),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')
          }
          
          function CallBackApp(){
  			window.opener.CallBackApp();
		  } 

</script>
<%@ include file="/angularscripts.jsp"%>

<body scroll="no" style="overflow:hidden" leftmargin="0" topmargin="0" marginheight="0" onLoad="setParentChildsFocus();" onunload="CallBackApp();">
<iframe name="tmp" width="0%" height="0%" src="#" />
</body>
</html>