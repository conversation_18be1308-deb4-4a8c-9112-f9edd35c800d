
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@ include file="/taglib.jsp"%>
<%@ page import="org.swallow.util.CommonDataManager"%>

<%@ page import="org.swallow.util.SwtUtil"%>

<html>
<head>
<s:if test='"N"==#request.sweepQueue.queueName' >
	<title><s:text name="sweepSubmitQueue.title.window" /></title>
</s:if>
<s:if test='"U"==#request.sweepQueue.queueName' >
	<title><s:text name="sweepAuthQueue.title.window" /></title>
</s:if>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">

<SCRIPT language="JAVASCRIPT">
var countrow=0;
var selectedList="";
var errorSweepAmount = '${requestScope.IS_ERROR_SWEEP_AMOUNT}';
var errorCutOff = '${requestScope.IS_ERROR_CUT_OFF}';
var errorAccountBreach = '${requestScope.IS_ERROR_ACCOUNT_BREACH}';
var errorSweeps = '${requestScope.ERROR_SWEEPS}';
var dateFormat = '<s:property value="#request.session.CDM.dateFormat" />';

var lastRefTime = "${requestScope.lastRefTime}";

var requestURL = new String('<%=request.getRequestURL()%>');
var appName = "<%=SwtUtil.appName%>"; 
var idy = requestURL.indexOf('/'+appName+'/');	
requestURL=requestURL.substring(0,idy+1);


 var currEntityAccess="";

/*Added to apply style on mandatory field ,if it's equal to undefined then no field will be colored*/
 mandatoryFieldsArray= "undefined" ;


function submitForm(methodName){

	
   document.getElementById("refreshbutton").innerHTML = document.getElementById("refreshdisablebutton").innerHTML;
	
	document.forms[0].method.value = methodName;
	if(methodName == "submit"){
		
	    document.forms[0].selectedList.value = getSelectedList();
		setStoredParam("selectedList",null);
	}
	
	else if (getStoredParam("entityId")==document.forms[0].elements["sweepQueue.entityId"].value && 
			     getStoredParam("currencyCode")==document.forms[0].elements["sweepQueue.currencyCode"].value &&
			            getStoredParam("accountType")==document.forms[0].elements['sweepQueue.accountType'].value )
				// Store the selected rows list
		        setStoredParam("selectedList",getSelectedList());
	
		else
				// Set selected row list as null when changing combo box data 
		   		setStoredParam("selectedList",null);
	
	    document.forms[0].submit();
	 

}



function onFilterandSort2()
{
	updateColors2();
}


function onFilter(){
	disableAllButtons();
	updateColors();
}


var x2;

function updateColors2()
{
	var rows = x2.dataTable.tBody.rows;
	var l = rows.length;
	var count = 0;
	for (var i=0; i<l; i++)
	{
		if (x2.isRowVisible(i))
		{
			removeClassName(rows[i], count % 2 ? "odd" : "even");
			addClassName(rows[i], count % 2 ? "even" : "odd");
			count++;
		}
	}	
}

function accountAccess(){
    
	var table = document.getElementById("sweepDetailList");  
	var tbody = table.getElementsByTagName("tbody")[0];    
	var rows = tbody.getElementsByTagName("tr");
	var selectedList = "";
    var flag = true
	
	var entity = document.forms[0].elements['sweepQueue.entityId'].value
	for (i=0; i < rows.length; i++) 
	 {    
		if( isRowSelected(rows[i])){
			
			var accountId = rows[i].cells[5].innerText;
			var entityIdCr = $(rows[i]).find('td.entityIdCr')[0].innerText;
			var entityIdDr = $(rows[i]).find('td.entityIdDr')[0].innerText;
			flag=accountAccessConfirm(accountId.trim(),entityIdCr)
			if(flag=="false")
			{
			
				<s:if test='"N"==#request.sweepQueue.queueName' >
					document.getElementById("submitbutton").innerHTML = document.getElementById("submitdisablebutton").innerHTML;
					document.getElementById("changebutton").innerHTML = document.getElementById("changedisablebutton").innerHTML;
				</s:if>
				<s:if test='"U"==#request.sweepQueue.queueName' >
					document.getElementById("authorizebutton").innerHTML = document.getElementById("authorizedisablebutton").innerHTML;
					document.getElementById("changebutton").innerHTML = document.getElementById("changedisablebutton").innerHTML;
				</s:if>

			
				}
			accountId = rows[i].cells[8].innerText;
			flag=accountAccessConfirm(accountId.trim(),entityIdDr)
			if(flag=="false")
			{
				<s:if test='"N"==#request.sweepQueue.queueName' >
					document.getElementById("submitbutton").innerHTML = document.getElementById("submitdisablebutton").innerHTML;
					document.getElementById("changebutton").innerHTML = document.getElementById("changedisablebutton").innerHTML;
				</s:if>
				<s:if test='"U"==#request.sweepQueue.queueName' >
					document.getElementById("authorizebutton").innerHTML = document.getElementById("authorizedisablebutton").innerHTML;
					document.getElementById("changebutton").innerHTML = document.getElementById("changedisablebutton").innerHTML;
				</s:if>
			
				}
			}			
		}
	 }  		
function accountAccessConfirm(accountId,entity){

	var oXMLHTTP = new XMLHttpRequest();
	var sURL = requestURL + appName+"/accountAccess.do?method=acctAccessConfirm";

	sURL = sURL + "&accountId="+accountId;
	sURL = sURL + "&entityId="+entity;
	sURL = sURL + "&status=Sweeping";
	oXMLHTTP.open( "POST", sURL, false );
	oXMLHTTP.send();
	var str=oXMLHTTP.responseText;
	
	return str;
}	

	
function disableAllButtons()
{
	
	<s:if test='"N"==#request.sweepQueue.queueName' >
		
		document.getElementById("submitbutton").innerHTML = document.getElementById("submitdisablebutton").innerHTML;
	</s:if>

	<s:if test='"U"==#request.sweepQueue.queueName' >
		document.getElementById("authorizebutton").innerHTML = document.getElementById("authorizedisablebutton").innerHTML;
	</s:if>
	countrow=0;

	document.getElementById("changebutton").innerHTML = document.getElementById("changedisablebutton").innerHTML;
}

<%-- Changed to get vaue of sweep id from 9th column--%>
function getSelectedList(){
 var table = document.getElementById("sweepDetailList"); 
 var tbody = table.getElementsByTagName("tbody")[0];    
 var rows = tbody.getElementsByTagName("tr");
 var selectedList = "";
 var selectedAmount = "";
 for (i=0; i < rows.length; i++) 
 {
	if( isRowSelected(rows[i])){
	
     	selectedList = selectedList + rows[i].cells[10].innerText + ",";
    }
 }  		

 return selectedList;
}
/*This function is used for to display sweepsearch details
 *param methodName
 *
 */
function openSearch(methodName){
	    // variable declared the menu access
	    var menuAccessIdOfChildWindow = getMenuAccessIdOfChildWindow("Sweep Search");       
		// variable declared the Menu Name
		var menuName = new String('<s:text name="sweepSearch.title.window"/>');
		//variable declared the smart predict
	    var smrtPredPos = menuName.search('<s:text name="alert.SmartPredict"/>');
	    menuName = menuName.substr(0,smrtPredPos-3);
		if (menuAccessIdOfChildWindow == 2) {
			alert('<s:text name="alert.AccessNotAvl"/>' + menuName + '<s:text name="alert.ContactSysAdm"/>');
			
		} else {
		    var param='sweepsearch.do?method='+methodName+'&entityCode=';
		    param +=document.forms[0].elements['sweepQueue.entityId'].value;
		    param +='&actType='+document.forms[0].elements['sweepQueue.accountType'].value;
		    param +='&status='+document.forms[0].elements['sweepQueue.queueName'].value;
		    param +='&currency='+document.forms[0].elements['sweepQueue.currencyCode'].value;
		    param +='&menuAccessId='+menuAccessIdOfChildWindow;

            <s:if test='"N"==#request.sweepQueue.queueName' >
            param+='&parentScreen=sweepsubmitqueue';	
   	     </s:if>
   	
   	      <s:if test='"U"==#request.sweepQueue.queueName' >
   	       param+='&parentScreen=sweepauthorisequeue';
   	    </s:if>
		          /*Start:Code Modified for Mantis 1577 on 19-01-2011:The purpose Alignment width and height */
            openWindow(param,'SweepSearchWindow','width=911,height=488,toolbar=0, status=yes,resizable=yes, scrollbars=yes','true');
                      /*End:Code Modified for Mantis 1577 on 19-01-2011:The purpose Alignment width and height */
		}
}

/* this function return the row index */
function getRowindex(item)
{
	var index=-1;
	var rows = xl.dataTable.tBody.rows;
	var l=rows.length;
	var i=0;
	
	while ( i<l && index ==-1)
	 {
		 if(rows[i].cells[10].innerText == item)
			{	index=i;
				break;
			}
		 i++;
	  }
	
	return index;	
	
}


/*This function is used to highlight the selected rows in the data grid and changes as per the selection*/
function onMultiSelectTableRow(rowElement,isSelected)
{	
	

var menuAccess="${requestScope.menuAccessId}";

//codition for to wwather the rows are selected
	if(isSelected)
	{  //deselect the rows	
		unHighLightTableRow(rowElement);
		countrow--;
		getCurrencyAccess();

	}
	else
	{
		//highlight Rows
		highLightTableRow(rowElement);
		countrow++;
		getCurrencyAccess();
	
	}

	//This function is used to detrmine weather the selected currency is in Full access or not  
	function getCurrencyAccess()
	{
	
		//getting entity id
		var entityId = document.forms[0].elements['sweepQueue.entityId'].value;
		//getting selected currency
		var selectedCurrency = rowElement.cells[1].innerText;
		var oXMLHTTP = new XMLHttpRequest();
		var sURL = requestURL + appName +"/sweepqueue.do?method=setCurrencyAccess"
		sURL= sURL+"&selectedCurrency="+selectedCurrency;
		sURL=sURL+"&entityId="+entityId;
		oXMLHTTP.open( "POST", sURL, false );
		//sending request
		oXMLHTTP.send();
		//getting response
		 currEntityAccess=oXMLHTTP.responseText;
		}
	
	<%-- Enable Change button when one row is selected--%>
	if(currEntityAccess == "true" && countrow ==1 && menuAccess == 0)
	{
	document.getElementById("changebutton").innerHTML = document.getElementById("changeenablebutton").innerHTML;
	<%-- get entity id as a hidden element --%>
	var hiddenElement = rowElement.getElementsByTagName("input")[0].value;
	document.forms[0].entityId.value=hiddenElement;
	}
    else{  
    		document.getElementById("changebutton").innerHTML = document.getElementById("changedisablebutton").innerHTML;
		
	}
	
	//Enabling or Disabling the Sumbit button based on the menu access
	<s:if test='"N"==#request.sweepQueue.queueName' >
	if(currEntityAccess=="true" && menuAccess == 0 && countrow > 0)
	{
	    document.getElementById("submitbutton").innerHTML = document.getElementById("submitenablebutton").innerHTML;
	}
	else
	{
	 document.getElementById("submitbutton").innerHTML = document.getElementById("submitdisablebutton").innerHTML;	
	 
	} 
	
	    
	</s:if>
	//Enabling or Disabling the authorise button based on the menu access
	<s:if test='"U"==#request.sweepQueue.queueName' >
	if(currEntityAccess == "true" && menuAccess == 0 && countrow > 0)
	{
		 document.getElementById("authorizebutton").innerHTML = document.getElementById("authorizeenablebutton").innerHTML;
		}
		
		  else
	  {
	   document.getElementById("authorizebutton").innerHTML = document.getElementById("authorizedisablebutton").innerHTML;
		}
	    
		
		
	</s:if>
	
	accountAccess()
	
	
}



<%-- Function highlights the unsubmitted sweep rows--%>
function load(){

var selectedSweepIds = errorSweeps;
var selectedSweepIdArr =selectedSweepIds.split(",");

var tableId =  document.getElementById("sweepDetailList");
resetTableRowsStyle(tableId);
var rowsColl = tableId.tBodies[0].rows;


for(var idx = 0 ; idx < rowsColl.length; ++idx){
    for(var aidx1 = 0 ; aidx1 < selectedSweepIdArr.length; ++aidx1)
	{
         if(rowsColl[idx].cells[10].innerText == selectedSweepIdArr[aidx1])
		 {
			
 			if ("createEvent" in document) {
				var evt = document.createEvent("HTMLEvents");
				evt.initEvent("onclick", false, true);
				rowsColl[idx].cells[0].dispatchEvent(evt);
			} else
				 rowsColl[idx].cells[0].fireEvent("onclick");
 			
		 }
	}
	
	}
}

function bodyOnLoad()
{
	var currencyFormat = '<s:property value="#request.session.CDM.currencyFormat"/>';
	
	xl = new XLSheet("sweepDetailList","table_1", [dateFormat, "String", currencyFormat, currencyFormat, "String", "String", "String","String", "String", "String", "Number", "String","String","String", "String","String","String"],"11111101101111111");
	
	x2 = new XLSheet("othersDetailList","table_2", [dateFormat, "String", currencyFormat, currencyFormat, "String", "String", "String", "String","String", "String", "Number", "String","String","String", "String","String","String"],"11111101101111");

	xl.onsort = xl.onfilter = onFilter;
	x2.onsort = x2.onfilter = onFilterandSort2;
	highlightMultiTableRows("sweepDetailList");
	x2.dataTable.tBody.style.cursor="";
	
	var dropBox1 = new SwSelectBox(document.forms[0].elements["sweepQueue.entityId"],document.getElementById("entityName"));
	var dropBox2 = new SwSelectBox(document.forms[0].elements["sweepQueue.currencyCode"],document.getElementById("currencyName"));

	<s:if test='"N"==#request.sweepQueue.queueName' >
	     document.getElementById("submitbutton").innerHTML = document.getElementById("submitdisablebutton").innerHTML;	
	</s:if>
	
	<s:if test='"U"==#request.sweepQueue.queueName' >
	    document.getElementById("authorizebutton").innerHTML = document.getElementById("authorizedisablebutton").innerHTML;
	</s:if>

	<%-- disable change button on body load--%>
	document.getElementById("changebutton").innerHTML = document.getElementById("changedisablebutton").innerHTML;

	<%-- setting default value of bypassChangedSweep--%>
	document.forms[0].bypassChangedSweep.value = "N";

	document.forms[0].menuAccessId.value = "${requestScope.menuAccessId}";	
	
	document.getElementById("lastRefTime").innerText = lastRefTime;
	//Store entity id , currency code and account type 
	setStoredParam("entityId",document.forms[0].elements["sweepQueue.entityId"].value);
	setStoredParam("currencyCode",document.forms[0].elements["sweepQueue.currencyCode"].value);
	setStoredParam("accountType",document.forms[0].elements['sweepQueue.accountType'].value);
	
	//Get the selected row list.
	var selectedSweepQueue = getStoredParam('selectedList');
	
	if(selectedSweepQueue != null)
	  {
		var rows = xl.dataTable.tBody.rows;
		selectedSweepQueue=selectedSweepQueue.trim().split(",");
		var l=selectedSweepQueue.length;
		countrow=l-1;
		for (var i=0; i<l-1; i++)
			{	
		        var rowindex =getRowindex(selectedSweepQueue[i]);
				highLightTableRow(rows[rowindex]);
				// enable authorise button 
				document.getElementById("authorizebutton").innerHTML = document.getElementById("authorizeenablebutton").innerHTML;
		
			}
	
		if(countrow ==1)
			// enable change button if only one row is selected
		    document.getElementById("changebutton").innerHTML = document.getElementById("changeenablebutton").innerHTML;
     }

}


<%-- Function displays error(s) in message box--%>
<%-- It also forwards the control to submit sweep if user does not wish to review the changed sweeps--%>
function yesFunc() {
	load();
}
function noFunc() {
	var queueName = '${queueName}' ;
	document.forms[0].method.value = 'submit';
	document.forms[0].selectedList.value = errorSweeps + " ";
	document.forms[0].bypassCutOff.value = "Y";
	document.forms[0].submit();
}
function no2Func() {
	var queueName = '${queueName}' ;
	document.forms[0].method.value = 'submit';
	document.forms[0].selectedList.value = errorSweeps + " ";
	document.forms[0].bypassChangedSweep.value = "Y";
	document.forms[0].bypassCutOff.value = "${bypassCutOff}";
	document.forms[0].submit();
}
function no3Func() {
	var queueName = '${queueName}' ;
	document.forms[0].method.value = 'submit';
	document.forms[0].selectedList.value = errorSweeps + " ";
	document.forms[0].bypassAccountBreach.value = "Y";
	document.forms[0].bypassChangedSweep.value = "${bypassChangedSweep}";
	document.forms[0].bypassCutOff.value = "${bypassCutOff}";
	document.forms[0].submit();
}

function selectPopUpErrorMessageType()
{
var selectedSweepIdArray;
if(errorSweeps!=null && errorSweeps.length>0) {
  selectedSweepIdArray =errorSweeps.split(",");
}

if(errorCutOff!=null && errorCutOff=="Y")
{

	ShowErrMsgWindowWithBtn("Error",'${actionError}',YES_NO, yesFunc, noFunc);


}else if(errorSweepAmount!=null && errorSweepAmount=="Y")
{

	ShowErrMsgWindowWithBtn("Error",'${actionError}',YES_NO, yesFunc, no2Func);

}else if(errorAccountBreach!=null && errorAccountBreach=="Y")
{
  ShowErrMsgWindowWithBtn("Error",'${actionError}',YES_NO, yesFunc, no3Func);


}else{

ShowErrMsgWindow('${actionError}')
}
}

<%-- Function returns a single selected sweep id --%>
function getSelectedSweepID()
{
 
 var table = document.getElementById("sweepDetailList"); 
 var tbody = table.getElementsByTagName("tbody")[0];    
 var rows = tbody.getElementsByTagName("tr");
 var selectedSweepId = "";
 
 for (i=0; i < rows.length; i++) 
 {
	if( isRowSelected(rows[i])){
	// sweep id present at 9th coulmn
     	selectedSweepId = rows[i].cells[10].innerText  ;
    }
 }  		


 return selectedSweepId;

}


<%-- Function opens sweepqueuedetails screen --%>
function openSweepQueueDetail()
{
	openWindow('sweepdetail.do?method=displayQueue&swpid='+getSelectedSweepID()+'&entid='+document.forms[0].entityId.value+'&qname=<s:property value='#request.sweepQueue.queueName' />','SweepDtlWindow','width=1021,height=635,toolbar=0, status=yes,resizable=yes, scrollbars=yes','true');
}

</script>
</head>

<body leftmargin="0" topmargin="0" marginheight="0"
	onLoad="bodyOnLoad();setParentChildsFocus();setFocus(document.forms[0]);selectPopUpErrorMessageType();"
	onunload="call()">

<s:form action="sweepqueue.do">
	<s:hidden name='sweepQueue.queueName' />
	<s:hidden name="method" value="display" />
	<s:hidden name="selectedList" value="" />
	<%-- Added to set the status- if changed sweeps to be submitted--%>
	<s:hidden name="bypassChangedSweep" value="N" />
	<%-- Added to get the value of entity id--%>
	<input name="entityId" type="hidden" value="">
	<s:hidden name="bypassCutOff" value="N" />
	<s:hidden name="bypassAccountBreach" value="N" />

	<input name="menuAccessId" type="hidden">
        <input name="parentScreen" type="hidden">
	<s:set var="CDM" value="#request.session.CDM" />

	<div id="SweepingQueues"
		style="position: absolute; left: 2; top: 2; width: 1236; height: 85px; visibility: visible; border: 2px outset;"
		color="#7E97AF">
	<div id="SweepingQueues"
		style="position: absolute; z-index: 99; left: 8px; top: 5px; width: 700px; height: 85px;">

	<table width="552" border="0" cellspacing="0" cellpadding="0"
		class="content">

		<tr height="24" color="black" border="0">
			<td width="90"><b><s:text name="sweep.entity" /></b></td>
			<td width="28">&nbsp;</td>
			<td width="135"><s:select id="sweepQueue.entityId" name="sweepQueue.entityId" onchange="submitForm('displayList')" cssStyle="width:140px" tabindex="1" titleKey="tooltip.selectEntityid" list="#request.entityList" listKey="value" listValue="label" /></td>
			<td width="20">&nbsp;</td>
			<td width="280"><span id="entityName" name="entityName"
				class="spantext"></td>
		</tr>

		<tr height="24">
			<td width="180"><b><s:text name="sweep.currencyGroup" /></b></td>
			<td width="28">&nbsp;</td>
			<td width="135"><s:select id="sweepQueue.currencyCode" name="sweepQueue.currencyCode" onchange="submitForm('displayList')" cssStyle="width:140px" tabindex="2" titleKey="tooltip.selectCurrencyCode" list="#request.currencyGroupList" listKey="value" listValue="label" /></td>
			<td width="20">&nbsp;</td>
			<td width="280"><span id="currencyName" name="currencyName"
				class="spantext"></td>
		</tr>
		<tr height="24">
			<td width="120"><b><s:text name="sweep.accountType" /></b></td>
			<td width="28">&nbsp;</td>
			<td width="135"><s:select id="sweepQueue.accountType" name="sweepQueue.accountType" onchange="submitForm('displayList')" cssStyle="width:92px" tabindex="3" titleKey="tooltip.selectAccountType" list="#request.accountList" listKey="value" listValue="label" /></td>
			<td width="20">&nbsp;</td>
			<td width="280px"><span
				name="AccountDesp"
				id="AccountDesp">${AccountDesp}</span></td>
		</tr>
	</table>
	</div>
	</div>

	<div id="SweepingQueues" color="#7E97AF"
		style="position: absolute;  left: 2px; top: 92px; width: 1236px; height: 325px;">
	<div id="SweepingQueues"
		style="position: absolute; left: 0; top: 1; width: 1236; height: 1px; visibility: visible;">
	<table width="1236" border="0" cellspacing="0" cellpadding="0">
		<tr>
			<td width="13%" height="10px" class="topbar" align="left"><s:if test='"N"==#request.sweepQueue.queueName' >
				<b>&nbsp;&nbsp;<s:text name="sweep.submitPanel" /></b>
			</s:if> <s:if test='"U"==#request.sweepQueue.queueName' >
				<b>&nbsp;&nbsp;<s:text name="sweep.authorizePanel" /></b>
			</s:if></td>

		</tr>
	</table>
	</div>
	
	<div id="SweepingQueues"
		style="position: absolute; left: 0; top: 18; width: 1216px; height: 275px; visibility: visible;">
	<div id="SweepingQueues"
		style="position: absolute; z-index: 99; left: 0px; width: 1219px; height: 10px;">
	<table class="sort-table" id="table_1" bgcolor="#B0AFAF" width="2147"
		border="0" cellspacing="1" cellpadding="0" height="15">
		<thead>
			<tr>
				<td width="80" height="20px" align="center"
					title='<s:text name = "tooltip.sortValueDate"/>'><b><s:text name="sweep.valueDate" /></b></td>

				<td width="67" height="20px" align="center"
					title='<s:text name = "tooltip.sortCurrencyCode"/>'><b><s:text name="sweep.currencyCode" /></b></td>


				<!--  position of Sweep Amount changed to 2nd starts-->
				<td width="150" height="20px" align="center"
					title='<s:text name = "tooltip.sortSweepAmount"/>'><b><s:text name="sweep.currentAmt" /></b></td>
				<!--  position of Sweep Amount changed to 2nd ends-->

				<!--new field added for 'New Field' at the 4th position starts -->
				<td width="150" height="20px" align="center"
					title='<s:text name = "tooltip.sortSweepAmount"/>'><b><s:text name="sweep.NewAmt" /></b></td>
					
				<!--new field added for 'New Field' at the 4th position ends -->
				<td width="100" height="20px" align="center"
					title='<s:text name = "tooltip.sortEntityId"/>'><b><s:text name="sweepsearch.entityCr" /></b></td>
				<td width="220" height="20px" align="center"
					title='<s:text name = "tooltip.sortCrAccID"/>'><b><s:text name="sweep.accountIdCr" /></b></td>
				<!--new field added for 'New Field' at the 4th position ends -->

				<!-- added for Rabo bank starts -->
				<td width="180" height="20px" align="center"
					title='<s:text name = "tooltip.accName"/>'><b><s:text name="movement.Name" /></b></td>
				<!-- added for Rabo bank ends -->
				<td width="100" height="20px" align="center"
					title='<s:text name = "tooltip.sortEntityId"/>'><b><s:text name="sweepsearch.entityDr" /></b></td>

				<td width="220" height="20px" align="center"
					title='<s:text name = "tooltip.sortDrAccID"/>'><b><s:text name="sweep.accountIdDr" /></b></td>

				<!-- added for Rabo bank starts -->
				<td width="180" height="20px" align="center"
					title='<s:text name = "tooltip.accName"/>'><b><s:text name="movement.Name" /></b></td>
				<!-- added for Rabo bank ends -->




				<td width="120" height="20px" align="center"
					title='<s:text name = "tooltip.sortSweepID"/>'><b><s:text name="sweep.sweepId" /></b></td>



				

				<!--4 td tags added for rabo bank customization starts-->
				<td width="140" height="20px" align="center"
					title='<s:text name = "tooltip.crdIntMsg"/>'><b><s:text name="sweep.crdIntMsg" /></b></td>

				<td width="140" height="20px" align="center"
					title='<s:text name = "tooltip.crdExtMsg"/>'><b><s:text name="sweep.crdExtMsg" /></b></td>

				<td width="140" height="20px" align="center"
					title='<s:text name = "tooltip.drIntMsg"/>'><b><s:text name="sweep.drIntMsg" /></b></td>

				<td width="140" height="20px" align="center"
					title='<s:text name = "tooltip.drExtMsg"/>'><b><s:text name="sweep.drExtMsg" /></b></td>
				<!--4 td tags added for rabo bank customization ends-->

				<td width="76" height="20px" align="center"
					title='<s:text name = "tooltip.sortSweeptype"/>'><b><s:text name="sweep.sweepType" /></b></td>
				<td width="144" height="20px" align="center"
					title='<s:text name = "tooltip.sortSweepUser"/>'><b><s:text name="sweep.sweepUser" /></b></td>
			</tr>
		</thead>
	</table>
	</div>
	<div id="ddscrolltable"
		style="position: absolute; left: 0px; top: 2px; width: 1236px; height: 297px; overflow: scroll">
	<div id="SweepingQueues"
		style="position: absolute; left: 0px; top: 20px; width: 1216px; height: 10px;">
	<table class="sort-table" id="sweepDetailList" width="2347" border="0"
		cellspacing="1" cellpadding="0" height="260">
		<tbody>
			<%int count = 0; %>
			<s:iterator value='#request.sweepDetailList' var='sweepDetailList' >
				<% if( count%2 == 0 ) { %><tr class="even">
					<% }else  {  %>
				
				<tr class="odd">
					<%}++count; %>
					<s:hidden  name="sweepDetailList.entityId" value="%{#sweepDetailList.entityIdCr}" />
					<s:if test='"Y"==#sweepDetailList.cutOffExceeded' >


						<td width="80" style="color: red" align="center"><s:property value='#sweepDetailList.valueDateAsString' /></td>



						<td width="67" style="color: red"><s:property value='#sweepDetailList.currencyCode' /></td>

						<!--  position of Sweep Amount changed to 3rd starts-->
						<td align="right" width="150" style="color: red"><s:property value='#sweepDetailList.displaySweepAmount' /></td>

						<!--  position of Sweep Amount changed to 3rd ends-->

						<!--new field added for 'New Field' at the 4th position starts -->
						<td align="right" width="150" style="color: red"><s:property value='#sweepDetailList.newCalulatedAmount' /></td>
						<!--new field added for 'New Field' at the 4th position ends -->

						<td width="100" style="color: red"><s:property value='#sweepDetailList.entityIdCr' /></td>
						<td width="220" style="color: red"><s:property value='#sweepDetailList.accountIdCr' /></td>
						<!-- added for rabo bank starts -->
						<td width="180" style="color: red"><s:property value='#sweepDetailList.accountCr.acctname' /></td>
						<!-- added for rabo bank ends -->
						<td width="100" style="color: red"><s:property value='#sweepDetailList.entityIdDr' /></td>

						<td width="220" style="color: red"><s:property value='#sweepDetailList.accountIdDr' /></td>


						<!-- added for rabo bank starts -->
						<td width="180" style="color: red"><s:property value='#sweepDetailList.accountDr.acctname' /></td>
						<!-- added for rabo bank ends -->




						<td align="right" width="120" style="color: red"><s:property value='#sweepDetailList.id.sweepId' /></td>



						
						<!--4 td tags added for rabo bank customization starts-->
						<td width="140" style="color: red"><s:property value='#sweepDetailList.accountCr.acctNewCrInternal' /></td>
						<td width="140" style="color: red"><s:property value='#sweepDetailList.accountCr.acctNewCrExternal' /></td>
						<td width="140" style="color: red"><s:property value='#sweepDetailList.accountDr.acctNewDrInternal' /></td>
						<td width="140" style="color: red"><s:property value='#sweepDetailList.accountDr.acctNewDrExternal' /></td>
						<!--4 td tags added for rabo bank customization ends-->


						<s:if test='"A"==#sweepDetailList.sweepType' >
							<td width="76" style="color: red"><s:text name="sweepSubmit.colValue.Auto" /></td>
						</s:if>
						<s:if test='"M"==#sweepDetailList.sweepType' >
							<td width="76" style="color: red"><s:text name="sweepSubmit.colValue.Man" /></td>
						</s:if>
						<td width="144" style="color: red"><s:property value='#sweepDetailList.displayUser' /></td>
							
						<td class="entityIdCr" style="display: none"><s:property value='#sweepDetailList.entityIdCr' /></td>	
						<td class="entityIdDr" style="display: none"><s:property value='#sweepDetailList.entityIdDr' /></td>	

					</s:if>


					<s:if test='"Y"!=#sweepDetailList.cutOffExceeded' >

						<td width="80" align="center"><s:property value='#sweepDetailList.valueDateAsString' /></td>



						<td width="67"><s:property value='#sweepDetailList.currencyCode' /></td>

						<!--  position of Sweep Amount changed to 3rd starts-->
						<td align="right" width="150"><s:property value='#sweepDetailList.displaySweepAmount' /></td>
						<!--  position of Sweep Amount changed to 3rd starts-->

						<!--new field added for 'New Field' at the 4th position starts -->
						<td align="right" width="150"><s:property value='#sweepDetailList.newCalulatedAmount' /></td>
						<!--new field added for 'New Field' at the 4th position ends -->
						<td width="100" ><s:property value='#sweepDetailList.entityIdCr' /></td>
						<td width="220"><s:property value='#sweepDetailList.accountIdCr' /></td>



						<!-- added for rabo bank starts -->
						<td width="180" style="color: red"><s:property value='#sweepDetailList.accountCr.acctname' /></td>
						<!-- added for rabo bank ends -->
						<td width="100" ><s:property value='#sweepDetailList.entityIdDr' /></td>
						<td width="220"><s:property value='#sweepDetailList.accountIdDr' /></td>

						<!-- added for rabo bank starts -->
						<td width="180" style="color: red"><s:property value='#sweepDetailList.accountDr.acctname' /></td>
						<!-- added for rabo bank ends -->
						<td align="right" width="120"><s:property value='#sweepDetailList.id.sweepId' /></td>



						

						<!--4 td tags added for rabo bank customization starts-->
						<td width="140"><s:property value='#sweepDetailList.accountCr.acctNewCrInternal' /></td>
						<td width="140"><s:property value='#sweepDetailList.accountCr.acctNewCrExternal' /></td>
						<td width="140"><s:property value='#sweepDetailList.accountDr.acctNewDrInternal' /></td>
						<td width="140"><s:property value='#sweepDetailList.accountDr.acctNewDrExternal' /></td>

						<!--4 td tags added for rabo bank customization ends-->

						<s:if test='"A"==#sweepDetailList.sweepType' >
							<td width="76"><s:text name="sweepSubmit.colValue.Auto" /></td>
						</s:if>
						<s:if test='"M"==#sweepDetailList.sweepType' >
							<td width="76"><s:text name="sweepSubmit.colValue.Man" /></td>
						</s:if>
						<td width="144"><s:property value='#sweepDetailList.displayUser' /></td>
						<td class="entityIdCr" style="display: none"><s:property value='#sweepDetailList.entityIdCr' /></td>	
						<td class="entityIdDr" style="display: none"><s:property value='#sweepDetailList.entityIdDr' /></td>	

					</s:if>

				</tr>
			</s:iterator>
		</tbody>
		<tfoot>
			<tr>
				<td colspan="17"></td>
			</tr>
		</tfoot>
	</table>
	</div>
	</div>
	</div>
	</div>
	<div id="SweepingQueues" color="#7E97AF"
		style="position: absolute; border: 2px outset; left: 2px; top: 417px; width: 1236px; height: 155px;">
	<div id="SweepingQueues"
		style="position: absolute; left: 0; top: 1; width: 1234; height: 1px; visibility: visible;">
	<table width="1234" border="0" cellspacing="0" cellpadding="0">
		<tr>
			<td align="center" width="13%" height="10px" class="topbar"><b>&nbsp;&nbsp;<s:text name="sweep.otherPanel" /></b></td>
		</tr>
	</table>
	</div>
	<div id="SweepingQueues"
		style="position: absolute; left: 0; top: 18px; width: 1221px; height: 100px; visibility: visible;">
	<div id="SweepingQueues"
		style="position: absolute; z-index: 99; left: 0px; width: 1216px; height: 10px;">
	<table class="sort-table" id="table_2" bgcolor="#B0AFAF" width="2147"
		border="0" cellspacing="1" cellpadding="0" height="15">
		<thead>
			<tr>
				<td width="80" height="20px" align="center"
					title='<s:text name = "tooltip.sortValueDate"/>'><b><s:text name="sweep.valueDate" /></b></td>

				<td width="67" height="20px" align="center"
					title='<s:text name = "tooltip.sortCurrencyCode"/>'><b><s:text name="sweep.currencyCode" /></b></td>



				<!--  position of Sweep Amount changed to 3rd starts-->
				<td width="150" height="20px" align="center"
					title='<s:text name = "tooltip.sortSweepAmount"/>'><b><s:text name="sweep.currentAmt" /></b></td>
				<!--  position of Sweep Amount changed to 3rd starts-->

				<!--new field added for 'New Field' at the 4th position starts -->
				<td width="150" height="20px" align="center"
					title='<s:text name = "tooltip.sortSweepAmount"/>'><b><s:text name="sweep.NewAmt" /></b></td>
				<!--new field added for 'New Field' at the 4th position ends -->
				<td width="100" height="20px" align="center"
					title='<s:text name = "tooltip.sortEntityId"/>'><b><s:text name="sweepsearch.entityCr" /></b></td>
				<td width="220" height="20px" align="center"
					title='<s:text name = "tooltip.sortCrAccID"/>'><b><s:text name="sweep.accountIdCr" /></b></td>

				<!-- added for Rabo bank starts -->
				<td width="180" height="20px" align="center"
					title='<s:text name = "tooltip.accName"/>'><b><s:text name="movement.Name" /></b></td>
				<!-- added for Rabo bank ends -->
				<td width="100" height="20px" align="center"
					title='<s:text name = "tooltip.sortEntityId"/>'><b><s:text name="sweepsearch.entityDr" /></b></td>
				<td width="220" height="20px" align="center"
					title='<s:text name = "tooltip.sortDrAccID"/>'><b><s:text name="sweep.accountIdDr" /></b></td>

				<!-- added for Rabo bank starts -->
				<td width="180" height="20px" align="center"
					title='<s:text name = "tooltip.accName"/>'><b><s:text name="movement.Name" /></b></td>
				<!-- added for Rabo bank ends -->




				<td width="120" height="20px" align="center"
					title='<s:text name = "tooltip.sortSweepID"/>'><b><s:text name="sweep.sweepId" /></b></td>

				
				<!--4 td tags added for rabo bank customization starts -->
				<td width="140" height="20px" align="center"
					title='<s:text name = "tooltip.crdIntMsg"/>'><b><s:text name="sweep.crdIntMsg" /></b></td>
				<td width="140" height="20px" align="center"
					title='<s:text name = "tooltip.crdExtMsg"/>'><b><s:text name="sweep.crdExtMsg" /></b></td>
				<td width="140" height="20px" align="center"
					title='<s:text name = "tooltip.drIntMsg"/>'><b><s:text name="sweep.drIntMsg" /></b></td>
				<td width="140" height="20px" align="center"
					title='<s:text name = "tooltip.drExtMsg"/>'><b><s:text name="sweep.drExtMsg" /></b></td>
				<!--4 td tags added for rabo bank customization ends -->

				<td width="66" height="20px" align="center"
					title='<s:text name = "tooltip.sortSweeptype"/>'><b><s:text name="sweep.sweepType" /></b></td>
				<td width="144" height="20px" align="center"
					title='<s:text name = "tooltip.sortSweepUser"/>'><b><s:text name="sweep.sweepUser" /></b></td>

			</tr>
		</thead>
	</table>
	</div>
	<div id="ddscrolltable"
		style="position: relative; left: 0px; width: 1233px; height: 130px; overflow: scroll">
	<div id="SweepingQueues"
		style="position: relative; left: 0px; top: 20px; width: 1216px; height: 10px;">

	<table class="sort-table" id="othersDetailList" width="2147" border="0"
		cellspacing="1" cellpadding="0" height="90">
		<tbody>
			<% count = 0; %>
			<s:iterator value='#request.othersDetailList' var='othersDetailList' >

				<% if( count%2 == 0 ) {%><tr class="even">
					<% }else  { %>
				
				<tr class="odd">
					<%}++count; %>

					<s:if test='"Y"==#othersDetailList.cutOffExceeded' >
						<td width="80" style="color: red"><s:property value='#othersDetailList.valueDateAsString' /></td>



						<td width="67" style="color: red"><s:property value='#othersDetailList.currencyCode' /></td>

						<!--  position of Sweep Amount changed to 3rd starts-->
						<td align="right" width="150" style="color: red"><s:property value='#othersDetailList.displaySweepAmount' /></td>
						<!--  position of Sweep Amount changed to 3rd ends-->
						<td align="right" width="150" style="color: red"><s:property value='#othersDetailList.newCalulatedAmount' /></td>

						<td  style="color: red" width="100" ><s:property value='#othersDetailList.entityIdCr' /></td>
						<!--new field added for 'New Field' at the 4th position starts -->
						<td width="220" style="color: red"><s:property value='#othersDetailList.accountIdCr' /></td>
						<!--new field added for 'New Field' at the 4th position ends -->

						<!-- added for rabo bank starts -->
						<td width="180" style="color: red"><s:property value='#othersDetailList.accountCr.acctname' /></td>
						<!-- added for rabo bank ends -->
						<td style="color: red"  width="100" ><s:property value='#othersDetailList.entityIdDr' /></td>
						<td width="220" style="color: red"><s:property value='#othersDetailList.accountIdDr' /></td>

						<!-- added for rabo bank starts -->
						<td width="180" style="color: red"><s:property value='#othersDetailList.accountDr.acctname' /></td>
						<!-- added for rabo bank ends -->



						<td align="right" width="120" style="color: red"><s:property value='#othersDetailList.id.sweepId' /></td>
						
						<!--4 td tags added for rabo bank customization starts -->
						<td width="140" style="color: red"><s:property value='#othersDetailList.accountCr.acctNewCrInternal' /></td>
						<td width="140" style="color: red"><s:property value='#othersDetailList.accountCr.acctNewCrExternal' /></td>
						<td width="140" style="color: red"><s:property value='#othersDetailList.accountDr.acctNewDrInternal' /></td>
						<td width="140" style="color: red"><s:property value='#othersDetailList.accountDr.acctNewDrExternal' /></td>
						<!--4 td tags added for rabo bank customization ends -->

						<s:if test='"A"==#othersDetailList.sweepType' >
							<td width="76" style="color: red"><s:text name="sweepSubmit.colValue.Auto" /></td>
						</s:if>
						<s:if test='"M"==#othersDetailList.sweepType' >
							<td width="76" style="color: red"><s:text name="sweepSubmit.colValue.Man" /></td>
						</s:if>
						<td width="144" style="color: red"><s:property value='#othersDetailList.displayUser' /></td>
						<td class="entityIdCr" style="display: none"><s:property value='#othersDetailList.entityIdCr' /></td>	
						<td class="entityIdDr" style="display: none"><s:property value='#othersDetailList.entityIdDr' /></td>	

					</s:if>

					<s:if test='"Y"!=#othersDetailList.cutOffExceeded' >
						<td width="80"><s:property value='#othersDetailList.valueDateAsString' />&nbsp;</td>



						<td width="67"><s:property value='#othersDetailList.currencyCode' /></td>

						<!--  position of Sweep Amount changed to 3rd starts-->
						<td align="right" width="150"><s:property value='#othersDetailList.displaySweepAmount' /></td>
						<!--  position of Sweep Amount changed to 3rd ends-->


						<!--new field added for 'New Field' at the 4th position starts -->
						<td align="right" width="150"><s:property value='#othersDetailList.newCalulatedAmount' /></td>
						<!--new field added for 'New Field' at the 4th position ends -->
						<td width="100"><s:property value='#othersDetailList.entityIdCr' /></td>
						<td width="220"><s:property value='#othersDetailList.accountIdCr' /></td>


						<!-- added for rabo bank starts -->
						<td width="180" style="color: red"><s:property value='#othersDetailList.accountCr.acctname' /></td>
						<!-- added for rabo bank ends -->
						<td width="100" ><s:property value='#othersDetailList.entityIdDr' /></td>
						<td width="220"><s:property value='#othersDetailList.accountIdDr' /></td>

						<!-- added for rabo bank starts -->
						<td width="180" style="color: red"><s:property value='#othersDetailList.accountDr.acctname' /></td>
						<!-- added for rabo bank ends -->



						<td align="right" width="120"><s:property value='#othersDetailList.id.sweepId' /></td>
						
						<!--4 td tags added for rabo bank customization starts -->

						<td width="140"><s:property value='#othersDetailList.accountCr.acctNewCrInternal' /></td>
						<td width="140"><s:property value='#othersDetailList.accountCr.acctNewCrExternal' /></td>
						<td width="140"><s:property value='#othersDetailList.accountDr.acctNewDrInternal' /></td>
						<td width="140"><s:property value='#othersDetailList.accountDr.acctNewDrExternal' /></td>
						<!--4 td tags added for rabo bank customization ends -->

						<s:if test='"A"==#othersDetailList.sweepType' >
							<td width="76"><s:text name="sweepSubmit.colValue.Auto" /></td>
						</s:if>
						<s:if test='"M"==#othersDetailList.sweepType' >
							<td width="76"><s:text name="sweepSubmit.colValue.Man" /></td>
						</s:if>
						<td width="144"><s:property value='#othersDetailList.displayUser' />&nbsp;</td>
							<td class="entityIdCr" style="display: none"><s:property value='#othersDetailList.entityIdCr' /></td>	
						<td class="entityIdDr" style="display: none"><s:property value='#othersDetailList.entityIdDr' /></td>	

					</s:if>
				</tr>
			</s:iterator>
		</tbody>
		<tfoot>
			<tr>
				<td colspan="17"></td>
			</tr>
		</tfoot>
	</table>
	</div>
	</div>
	</div>
	</div>
	
	<div id="SweepingQueues"
		style="position: absolute; left: 1140; top: 590; width: 70; height: 15px; visibility: visible;">
	<table width="60" cellspacing="0" cellpadding="0" height="20"
		border="0">
		<tr>

			<td align="Right"><a tabindex="8" href=#
				onclick="javascript:openWindow(buildPrintURL('print','Sweep Queue'),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')"
				onMouseOut="MM_swapImgRestore()"
				onMouseOver="MM_swapImage('Help','','images/help_default.GIF ',1)"><img
				src="images/help_default.GIF " name="Help" border="0"></a></td>

			<td align="right" valign="middle" id="Print"><a tabindex="8"
				onclick="printPage();" onMouseOut="MM_swapImgRestore()"
				onMouseOver="MM_swapImage('Print','','images/Print_R.gif ',1)"><img
				src="images/Print.gif" name="Print" border="0"
				title='<s:text name = "tooltip.printScreen"/>' tabindex="8"></a>
			</td>
		</tr>
	</table>
	</div>

	<div id="ddimagebuttons" color="#7E97AF"
		style="position: absolute; border: 2px outset; left: 2; top: 582; width: 1236px; height: 39px; visibility: visible;">
	<div id="SweepingQueues"
		style="position: absolute; left: 6; top: 4; width: 280px; height: 15px; visibility: visible;">
	<table width="350" border="0" cellspacing="0" cellpadding="0"
		height="20">
		<tr>
			<td id="submitbutton"></td>
			<td id="authorizebutton"></td>
			<%-- Change button --%>
			<td id="changebutton"></td>
			<td id="refreshbutton"><a tabindex="5"
				title='<s:text name = "tooltip.refreshScreen"/>'
				onMouseOut="collapsebutton(this)"
				onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)"
				onMouseUp="highlightbutton(this)"
				onclick="submitForm('displayList')"><s:text name="sweep.refresh" /></a></td>
			<td id="searchbutton"><a tabindex="6"
				title='<s:text name = "tooltip.searchSweep"/>'
				onMouseOut="collapsebutton(this)"
				onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)"
				onMouseUp="highlightbutton(this)"
				onclick="openSearch('displaysearch')"><s:text name="sweep.search" /></a></td>
			<td id="closebutton"><a tabindex="7"
				title='<s:text name = "tooltip.close"/>'
				onMouseOut="collapsebutton(this)"
				onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)"
				onMouseUp="highlightbutton(this)"
				onclick="javascript:window.close();"><s:text name="sweep.close" /></a></td>
		</tr>
	</table>
	</div>
	
	<table height="33"><tr>
		<td id="lastRefTimeLable" width="1004px" align="right" >
		<s:text name="label.lastRefTime"/>
		</td>			
		<td id="lastRefTime" >
		<input class="textAlpha" style="background:transparent;border: 0;" tabindex="-1" readonly name="maxPageNo" value="" size="14">				
		</td>
		</tr>
	</table>
	
	<div
		style="position: absolute; left: 6; top: 4; width: 683px; height: 15px; visibility: hidden;">
	<table width="350" border="0" cellspacing="0" cellpadding="0"
		height="20" style="visibility: hidden">
		<tr>
			<td id="submitenablebutton"><a tabindex="4"
				title='<s:text name = "tooltip.SubmitSelSweep"/>'
				onMouseOut="collapsebutton(this)"
				onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)"
				onMouseUp="highlightbutton(this)" onClick="submitForm('submit')"><s:text name="sweep.submit" /></a></td>
			<td id="submitdisablebutton"><a class="disabled"
				disabled="disabled"><s:text name="sweep.submit" /></a></td>
			<td id="authorizeenablebutton"><a tabindex="4"
				title='<s:text name = "tooltip.AuthorizeSelSweep"/>'
				onMouseOut="collapsebutton(this)"
				onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)"
				onMouseUp="highlightbutton(this)" onClick="submitForm('submit')"><s:text name="sweep.authorize" /></a></td>
			<td id="authorizedisablebutton"><a class="disabled"
				disabled="disabled"><s:text name="sweep.authorize" /></a></td>
			<%-- Change enable button --%>
			<td id="changeenablebutton"><a tabindex="4"
				title='<s:text name = "tooltip.refreshScreen"/>'
				onMouseOut="collapsebutton(this)"
				onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)"
				onMouseUp="highlightbutton(this)" onclick="openSweepQueueDetail();"><s:text name="button.change" /></a></td>
			<%-- Change disable button --%>
			<td id="changedisablebutton"><a class="disabled"
				disabled="disabled"><s:text name="button.change" /></a></td>
			
			<td id="refreshdisablebutton"><a class="disabled"
				disabled="disabled"><s:text name="button.Refresh" /></a></td>
			
		</tr>
	</table>
	</div>
	</div>
	<blockquote>&nbsp;</blockquote>
	<p>&nbsp;</p>
	<blockquote>&nbsp;</blockquote>
	<p>&nbsp;</p>
</s:form>
</body>

</html>