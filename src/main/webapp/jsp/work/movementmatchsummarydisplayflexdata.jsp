<?xml version="1.0" encoding="UTF-8"?>
<%@ page contentType="text/xml" %>
<%@ page import="org.swallow.util.CommonDataManager"%>
<%@ page import="java.util.*"  %>
<%@ page import="org.swallow.util.OpTimer"  %>
<%@ page import="org.swallow.util.SwtConstants"  %>
<%@ taglib prefix="s" uri="/struts-tags" %>
<!-- Start: Source modified for Mantis 1691:"Movement Match Summary Display: poor performance in Offered queue" by <PERSON> on 20-June-2012 -->
<movementmatchsummarydisplay 
fullAccess="${requestScope.fullAccess}"
entityid="<s:property value='#request.movement.id.entityId' />"
entityname= "<s:property value='#request.movementDetail.entityName' />"
matchid = "<s:property value='#request.movement.matchId' />"
matchindex = "<s:property value='#request.movementDetail.matchIndex' />"
totalmatches = "<s:property value='#request.movementDetail.totalMatches' />"
mvmntstatus = "<s:property value='#request.movement.matchStatus' />"
matchstatus = "<s:property value='#request.movementDetail.matchStatus' />"
matchquality="<s:property value='#request.movementDetail.matchQuality' />"
updatedate= "<s:property value='#request.movement.updateDateAsString' />"
updateuser = "<s:property value='#request.movement.updateUser' />"
currencycode = "<s:property value='#request.movement.currencyCode' />"
usernotes = "<s:property value='#request.movementDetail.notes' />"
posintlvl = "<s:property value='#request.movementDetail.posLevelInternal' />"
posextlvl = "<s:property value='#request.movementDetail.posLevelExternal' />"
scenarioAlerting = "<s:property value='#request.movement.matchcenarioHighlighted' />"
currfontsize="<s:property value='#request.fontSize' />"
acctaccessstatus="${requestScope.accountAccessStatus}"
matchHash="${requestScope.matchHash}"
ccyTolerance="${requestScope.ccyTolerance}"
suppMatchDiffWarning="${requestScope.suppMatchDiffWarning}"
currencyPattern="${requestScope.currencyPattern}"
>
	<request_reply>
			<status_ok><s:property value="#request.reply_status_ok" /></status_ok>
			<message><s:property value="#request.reply_message" /></message>
	</request_reply>

	<grid>
		<metadata>
			<columns>
			  <%-- Alerting --%>
				<column 
					heading=""
					draggable="false"					
					filterable="false"
					type="str"
					dataelement="alerting"
					width="10"
					sort="true"
					clickable="true"
					resizable ="false"
				/>
			<!-- columns is an ordered collection -->
			<s:iterator value='#request.column_order' var='order' >
				<s:if test='"pos"==#order' >
					<column 
						heading="<s:text name="movement.position"/>"
						draggable="false"					
						filterable="true"
						columnNumber = "0"
						type="str"
						sort="true"
						dataelement="pos"
						width="<s:property value='#request.column_width.pos' />"
					/>
</s:if>
				<s:if test='"value"==#order' >
					<column 
						heading="<s:text name="movement.value"/>"
						draggable="true"					
						filterable="true"
						columnNumber = "1"
						type="str"
						sort="true"
						dataelement="value"
						width="<s:property value='#request.column_width.value' />"
					/>
</s:if>
				<s:if test='"amount"==#order' >
					<column 
						heading="<s:text name="movement.amount1"/>"
						draggable="true"					
						filterable="false"
						columnNumber = "2"
						balancetype="<%=SwtConstants.ACCT_MONITOR_BALTYPE_PREDICTED%>"
						type="num"
						sort="true"
						dataelement="amount"
						width="<s:property value='#request.column_width.amount' />"
					/>
</s:if>
				<s:if test='"sign"==#order' >
					<column 
						heading="<s:text name="movement.sign"/>"					
						draggable="true"					
						filterable="true"
						columnNumber = "3"
						type="str"
						sort="true"
						dataelement="sign"
						width="<s:property value='#request.column_width.sign' />"
					/>
</s:if>
				<s:if test='"ref1"==#order' >
					<column 
						heading="<s:text name="movement.reference1"/>"					
						draggable="true"					
						filterable="false"
						columnNumber = "4"
						balancetype="<%=SwtConstants.ACCT_MONITOR_BALTYPE_PREDICTED%>"
						type="str"
						sort="true"
						dataelement="ref1"
						width="<s:property value='#request.column_width.ref1' />"
					/>
</s:if>
				<s:if test='"ref2"==#order' >
					<column 
						heading="<s:text name="movement.reference2"/>"					
						draggable="true"					
						filterable="false"
						columnNumber = "12"
						balancetype="<%=SwtConstants.ACCT_MONITOR_BALTYPE_PREDICTED%>"
						type="str"
						sort="true"
						dataelement="ref2"
						width="<s:property value='#request.column_width.ref2' />"
					/>
</s:if>
				<s:if test='"ref3"==#order' >
					<column 
						heading="<s:text name="movement.reference3"/>"					
						draggable="true"					
						filterable="false"
						columnNumber = "13"
						balancetype="<%=SwtConstants.ACCT_MONITOR_BALTYPE_PREDICTED%>"
						type="str"
						sort="true"
						dataelement="ref3"
						width="<s:property value='#request.column_width.ref3' />"
					/>
</s:if>
				<s:if test='"account"==#order' >
					<column 
						heading="<s:text name="movement.account"/>"					
						draggable="true"					
						filterable="true"
						columnNumber = "5"
						balancetype="<%=SwtConstants.ACCT_MONITOR_BALTYPE_PREDICTED%>"
						type="str"
						sort="true"
						dataelement="account"
						width="<s:property value='#request.column_width.account' />"
					/>
</s:if>
				<s:if test='"cparty"==#order' >
					<column 
						heading="<s:text name="movement.counterPartyId"/>"					
						draggable="true"					
						filterable="true"
						columnNumber = "6"
						balancetype="<%=SwtConstants.ACCT_MONITOR_BALTYPE_PREDICTED%>"
						type="str"
						sort="true"
						dataelement="cparty"
						width="<s:property value='#request.column_width.cparty' />"
					/>
</s:if>
				<s:if test='"pred"==#order' >
					<column 
						heading="<s:text name="movement.pred"/>"					
						draggable="true"					
						filterable="true"
						columnNumber = "7"
						balancetype="<%=SwtConstants.ACCT_MONITOR_BALTYPE_PREDICTED%>"
						type="str"
						sort="true"
						dataelement="pred"
						width="<s:property value='#request.column_width.pred' />"
					/>
</s:if>
				<s:if test='"notes"==#order' >
					<column 
						heading="<s:text name="movement.notes"/>"					
						draggable="true"					
						filterable="false"
						columnNumber = "8"
						balancetype="<%=SwtConstants.ACCT_MONITOR_BALTYPE_PREDICTED%>"
						type="str"
						sort="true"
						dataelement="notes"
						width="<s:property value='#request.column_width.notes' />"
					/>
</s:if>
				<s:if test='"source"==#order' >
					<column 
						heading="<s:text name="movement.source"/>"					
						draggable="true"					
						filterable="true"
						columnNumber = "9"
						balancetype="<%=SwtConstants.ACCT_MONITOR_BALTYPE_PREDICTED%>"
						type="str"
						sort="true"
						dataelement="source"
						width="<s:property value='#request.column_width.source' />"
					/>
</s:if>
				<s:if test='"input"==#order' >
					<column 
						heading="<s:text name="movement.input"/>"					
						draggable="true"					
						filterable="true"
						columnNumber = "10"
						balancetype="<%=SwtConstants.ACCT_MONITOR_BALTYPE_PREDICTED%>"
						type="str"
						sort="true"
						dataelement="input"
						width="<s:property value='#request.column_width.input' />"
					/>
</s:if>
				<s:if test='"beneficiary"==#order' >
					<column 
						heading="<s:text name="movement.beneficiary"/>"					
						draggable="true"					
						filterable="true"
						columnNumber = "11"
						balancetype="<%=SwtConstants.ACCT_MONITOR_BALTYPE_PREDICTED%>"
						type="str"
						sort="true"
						dataelement="beneficiary"
						width="<s:property value='#request.column_width.beneficiary' />"
					/>
</s:if>
				
				<s:if test='"movement"==#order' >
					<column 
						heading="<s:text name="movement.movementId"/>"					
						draggable="true"					
						filterable="false"
						columnNumber = "14"
						balancetype="<%=SwtConstants.ACCT_MONITOR_BALTYPE_PREDICTED%>"
						type="num"
						sort="true"
						dataelement="movement"
						width="<s:property value='#request.column_width.movement' />"
					/>
</s:if>
				<s:if test='"book"==#order' >
					<column 
						heading="<s:text name="movement.bookcode"/>"					
						draggable="true"					
						filterable="true"
						columnNumber = "15"
						balancetype="<%=SwtConstants.ACCT_MONITOR_BALTYPE_PREDICTED%>"
						type="str"
						sort="true"
						dataelement="book"
						width="<s:property value='#request.column_width.book' />"
					/>
</s:if>
				<s:if test='"custodian"==#order' >
					<column 
						heading="<s:text name="movement.custodian"/>"					
						draggable="true"					
						filterable="true"
						columnNumber = "16"
						balancetype="<%=SwtConstants.ACCT_MONITOR_BALTYPE_PREDICTED%>"
						type="str"
						sort="true"
						dataelement="custodian"
						width="<s:property value='#request.column_width.custodian' />"
					/>
</s:if>
				<s:if test='"matchingparty"==#order' >
				<column 
					heading="<s:text name="movement.matchingParty"/>"					
					draggable="true"					
					filterable="true"
					columnNumber = "18"
					balancetype="<%=SwtConstants.ACCT_MONITOR_BALTYPE_PREDICTED%>"
					type="str"
					sort="true"
					dataelement="matchingparty"
					width="<s:property value='#request.column_width.matchingparty' />"
					/>
</s:if>
				<s:if test='"producttype"==#order' >
					<column 
						heading="<s:text name="movement.productType"/>"					
						draggable="true"					
						filterable="true"
						columnNumber = "19"
						balancetype="<%=SwtConstants.ACCT_MONITOR_BALTYPE_PREDICTED%>"
						type="str"
						sort="true"
						dataelement="producttype"
						width="<s:property value='#request.column_width.producttype' />"
					/>
</s:if>
				
				<s:if test='"postingdate"==#order' >
					<column 
						heading="<s:text name="movement.postingDate"/>"					
						draggable="true"					
						filterable="true"
						columnNumber = "20"
						balancetype="<%=SwtConstants.ACCT_MONITOR_BALTYPE_PREDICTED%>"
						type="str"
						sort="true"
						dataelement="postingdate"
						width="<s:property value='#request.column_width.postingdate' />"
					/>
				</s:if>			
								
			</s:iterator>
			</columns>
		</metadata>
		<rows size="${recordCount}" >
		<s:iterator value='#request.movementSummaryDetails' var='movementSummaryDetails' >
		<row>
		
		        <alerting clickable="false"><s:property value='#movementSummaryDetails.scenarioHighlighted' /></alerting>
		        
				<pos
					clickable="false"
					positionlevel = "<s:property value='#movementSummaryDetails.positionLevel' />"
				><s:property value='#movementSummaryDetails.positionLevelName' /></pos>
				<value
					clickable="false"
				><s:property value='#movementSummaryDetails.valueDateAsString' /></value>
				<amount
					negative="<s:if test='"C"==#movementSummaryDetails.sign' >false</s:if><s:if test='#movementSummaryDetails.sign == "D"'>true</s:if>"
					clickable="false"
				><s:property value='#movementSummaryDetails.amountAsString' /></amount>
				<sign
					clickable="false"
				><s:property value='#movementSummaryDetails.sign' /></sign>
				<ref1
					clickable="false"
				><s:property value='#movementSummaryDetails.reference1' /></ref1>
				<ref2
					clickable="false"
				><s:property value='#movementSummaryDetails.reference2' /></ref2>
				<ref3
					clickable="false"
				><s:property value='#movementSummaryDetails.reference3' /></ref3>
				<account
					clickable="false"
				><s:property value='#movementSummaryDetails.accountId' /></account>
				<cparty
					clickable="false"
				><s:property value='#movementSummaryDetails.counterPartyId' /></cparty>
				<pred
					clickable="false"
				><s:property value='#movementSummaryDetails.predictStatus' /></pred>
				<notes
					clickable="false"
				><s:property value='#movementSummaryDetails.noOfNotesAttached' /></notes>
				<source
					clickable="false"
				><s:property value='#movementSummaryDetails.inputSource' /></source>
				<input
					clickable="false"
				><s:property value='#movementSummaryDetails.inputDateAsString' /></input>
				<beneficiary
					clickable="false"
				><s:property value='#movementSummaryDetails.beneficiaryId' /></beneficiary>
				<movement
					clickable="false"
				><s:property value='#movementSummaryDetails.id.movementId' /></movement>
				<book
					clickable="false"
				><s:property value='#movementSummaryDetails.bookCode' /></book>
				<custodian
					clickable="false"
				><s:property value='#movementSummaryDetails.custodianId' /></custodian>
				<matchingparty
					clickable="false"
				><s:property value='#movementSummaryDetails.matchingParty' /></matchingparty>
				
				<producttype
					clickable="false"
				><s:property value='#movementSummaryDetails.productType' /></producttype>
				
				<postingdate
					clickable="false"
				><s:property value='#movementSummaryDetails.postingDateAsString' /></postingdate>
				
				<externalBalStatus
					clickable="false"
				><s:property value='#movementSummaryDetails.extBalStatus' /></externalBalStatus>
			</row>
		</s:iterator>
		</rows>
	</grid>
<!-- End: Source modified for Mantis 1691:"Movement Match Summary Display: poor performance in Offered queue" by Marshal on 20-June-2012 -->
	  <selects>
		<select id="entity">
		<s:iterator value='#request.entities' var='entity' >
			<option 			value="<s:property value='#entity.value' />" 
				selected="<s:if test='#request.movement.id.entityId == #entity.value'>1</s:if><s:else>0</s:else>"
			><s:property value='#entity.label' /></option>
		</s:iterator>
		</select>
	</selects> 
</movementmatchsummarydisplay>