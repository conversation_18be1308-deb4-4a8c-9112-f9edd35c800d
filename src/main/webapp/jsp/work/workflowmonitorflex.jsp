<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">

<%@ page import="org.swallow.util.CommonDataManager"%>
<%@ page import="org.swallow.util.SwtConstants"%>
<%@ page import="org.swallow.util.SwtUtil"%>
<%@ include file="/taglib.jsp"%>
<%@ include file="/angularJSUtils.jsp"%>

<html>
	<head>
		<link rel="stylesheet" href="angularSources/styles.css?version=<%= SwtUtil.appVersion %>">
		<title><s:text name="workflowmonitor.title.window"/></title>
	</head>
	<body>
		<style>
			body { margin: 0px; overflow:hidden }
		</style>

		<script type="text/javascript" src="jsp/work/assets/swfobject.js"></script>
		<script type="text/javascript">
		var screenRoute = "workFlowMonitor";
			window.onload = function () {
				
				setParentChildsFocus();
				setTitleSuffix(document.forms[0]); 
				// var so = new SWFObject ("jsp/work/workflowmonitor.swf?version=<%= SwtUtil.appVersion %>", "accountmonitor", "100%", "100%", "9", "#D6E3FE");
				// so.write("swf");
			};
			isDoNotCloseMyChilds = true;
			window.onunload = call;
			
			var calledFromParent = <%=(""+request.getAttribute("callFromParent")).equalsIgnoreCase("Y")?"true":"false"%>;
			
			/* var label = new Array ();
			label["text"] = new Array ();
			label["tip"] = new Array ();

			label["text"]["entity"] = "<s:text name="workflowmonitor.entity.title"/>";	
			label["tip"]["entity"] = "<s:text name="tooltip.selectEntityId"/>";
			
			label["text"]["currency"] = "<s:text name="workflowmonitor.curGrp.title"/>";
			label["tip"]["currency"] = "<s:text name="tooltip.selectCurrencyCode"/>";
									
			label["text"]["tab1"] = "<s:text name="accountmonitor.today"/>";
			
			label["text"]["tab2"] = "<s:text name="accountmonitor.today1"/>";
			
			label["text"]["tab3"] = "<s:text name="accountmonitor.today2"/>";					
			label["text"]["threshold"] = "<s:text name="mvmt.applyCurrencyThreshold"/>";
			label["tip"]["threshold"] = "<s:text name="tooltip.applyCurrencyThreshold"/>";
						
			
			label["text"]["button-refresh"] = "<s:text name="button.Refresh"/>";
			label["tip"]["button-refresh"] = "<s:text name="tooltip.refreshWindow"/>";
			
			label["text"]["button-rate"] = "<s:text name="accountmonitorbutton.Rate"/>";
			label["tip"]["button-rate"] = "<s:text name="tooltip.rateButton"/>";			
			
			label["text"]["button-close"] = "<s:text name="button.close"/>";
			label["tip"]["button-close"] = "<s:text name="tooltip.close"/>";			

			// Movement Titles
			label["text"]["movement"] = "<s:text name="workflowmonitor.movMent.title"/>";
			label["text"]["movement-unsettled"] = "<s:text name="workflowmonitor.unsYestday.title"/>";
			label["text"]["movement-backval"] = "<s:text name="workflowmonitor.bkVal.title"/>";

			// Input Titles
			label["text"]["input"] = "<s:text name="workflowmonitor.input.title"/>";
			label["text"]["input-exception"] = "<s:text name="workflowmonitor.excep.title"/>";
			label["text"]["input-auth"] = "<s:text name="workflowmonitor.auth.title"/>";
			label["text"]["input-referred"] = "<s:text name="workflowmonitor.reff.title"/>";

			// Matches Titles
			label["text"]["matches"] = "<s:text name="workflowmonitor.matches.title"/>";
			label["text"]["matches-offered"] = "<s:text name="workflowmonitor.offered.title"/>";
			label["text"]["matches-suspended"] = "<s:text name="workflowmonitor.suspended.title"/>";
			label["text"]["matches-confirmed"] = "<s:text name="workflowmonitor.confirmed.title"/>";

			// Sweeps Titles
			label["text"]["sweep"] = "<s:text name="workflowmonitor.sweeps.title"/>";
			label["text"]["sweep-submit"] = "<s:text name="workflowmonitor.submit.title"/>";
			label["text"]["sweep-authorise"] = "<s:text name="workflowmonitor.authorise.title"/>";
			label["text"]["sweep-exception"] = "<s:text name="workflowmonitor.submit.Exception"/>";

			//  Included Movements Titles
			label["text"]["incmovements"] = "<s:text name="workflowmonitor.incMovmnts.title"/>";
			label["text"]["totalmovements"] = "<s:text name="workflowmonitor.totalMov.title"/>";

			//  Excluded Outstandings Titles
			label["text"]["execout"] = "<s:text name="workflowmonitor.exclOut.title"/>";
			
			// System Titles
			label["text"]["system"] = "<s:text name="workflowmonitor.system.title"/>";
			label["text"]["system-logon"] = "<s:text name="workflowmonitor.logon.title"/>";
			label["text"]["system-error"] = "<s:text name="workflowmonitor.errors.title"/>";
			
			
			label["text"]["excluded"] = "<s:text name="workflowmonitor.excluded"/>";
			label["text"]["outstandings"] = "<s:text name="workflowmonitor.outstandings"/>";
			label["text"]["included"] = "<s:text name="workflowmonitor.included"/>";
			label["text"]["movements"] = "<s:text name="workflowmonitor.movements"/>";
			label["text"]["total"] = "<s:text name="workflowmonitor.total"/>";
			label["text"]["label-selectedScenLastRan"] = "<s:text name="scenarioSummary.selectedScenLastRan"/>"; 
			label["text"]["label-selectedScen"] = "<s:text name="scenarioSummary.selectedScen"/>"; 
			label["text"]["label-scenTotals"] = "<s:text name="scenarioSummary.scenTotals"/>";
			label["tip"]["label-scenTotals"] = "<s:text name="tooltip.scenTotals"/>";
			label["tip"]["label-selectedScen"] = "<s:text name="tooltip.selectedScen"/>"
			
			label["text"]["alert.currencyAccess"] = "<s:text name="alert.currencyAccess"/>";

			label["text"]["label-workflowShowXML"] = "<s:text name="workflowmonitor.context.xml"/>";
			label["text"]["label-workflowXML"] = "<s:text name="workflowmonitor.title.xml"/>";
			label["text"]["label-summaryShowXML"] = "<s:text name="scenarioSummary.context.xml"/>";
			label["text"]["label-summaryXML"] = "<s:text name="scenarioSummary.title.xml"/>";
			label["text"]["label-changeColors"] = "<s:text name="colors.context.xml"/>";
			label["text"]["label-lastRefTime"] = "<s:text name="screen.lastRefresh"/>";
			label["text"]["label-workflowMonitor"] = "<s:text name="label.workflowMonitor"/>";
			label["text"]["label-noData"] = "<s:text name="alert.interfaceMonitor.noData"/>";
			label["text"]["label-sumNoDataTitle"] = "<s:text name="alert.Summary.noData.title"/>"; 
			label["text"]["label-refreshRateSelectedMonimum"] = "<s:text name="alert.refreshRateSelectedMonimum"/>"; 	
			label["text"]["label-workflowXML"] = "<s:text name="label.workflowXML"/>";
			label["text"]["label-exportPDF"] = "<s:text name="label.exportPDF"/>";
			label["text"]["label-taking"] = "<s:text name="label.taking"/>";
			label["text"]["label-seconds"] = "<s:text name="label.seconds"/>";*/
			
			var itemId = '${requestScope.itemId}';
			var hostId = '${requestScope.hostId}';
			var userId = '${requestScope.userId}';	
			var roleId = '${requestScope.roleId}';
			var systemDate = '${requestScope.sysDateAsString}';
		
			
			var refreshPending = false;
			
			var appName = "<%=SwtUtil.appName%>";
			var testDate= "<%=SwtUtil.getSystemDateString() %>";
			var dateFormat = '<s:property value="#request.session.CDM.dateFormatValue" />';
			var currencyFormat = '<s:property value="#request.session.CDM.dateFormatValue" />';												

			//flash uses this to determine what to request to save a new refresh rate
			function getUpdateRefreshRequest (rate) {
				
				return "screenOption.do?method=save&screenOption.id.hostId=" + hostId + "&screenOption.id.userId=" + userId + "&screenOption.id.screenId=" + itemId + "&screenOption.propertyValue=" + rate;
			}
			
/*Start:Code Modified by Chinniah on 15-Sep-2011 for Mantis 1483: Grant the role only view access to this menu option, I find that I am still able to 'Auth' and 'Change', where the buttons ought to be disabled*/
/**
*This method is used to find the Menuaccess Id for Input athorise queue and Sweep Authorise queue using their Menu itemId
*@return menuAccessIdOfChildWindow
**/
	function getMenuAccessIdByItemId(a) {
	//get the Menu accesss ID
		var menuAccessIdOfChildWindow = getMenuAccessIdOfChildWindowByItemId(a);
		var fwdSlashPos = new String(a).search("/"); 
		var gifPos = new String(a).search("gif");
		//to get the menu name
		var menuName = new String(a).substr(fwdSlashPos+1,gifPos-fwdSlashPos-2);
		//to get the position of the Menu name
		var QPos = new String(menuName).search("Queue"); 
		var SPos = new String(menuName).search("Status"); 
		var EPos = new String(menuName).search("Exception");
		if (QPos != -1) {
		    menuName = new String(menuName).substr(0,QPos)+" "+new String(menuName).substr(QPos);
		} else if (SPos != -1) {
		    menuName = new String(menuName).substr(0,SPos)+" "+new String(menuName).substr(SPos);
		} else if (EPos != -1) {
		    menuName = new String(menuName).substr(0,EPos)+" "+new String(menuName).substr(EPos);
		}
		//condition for checking the MenuAccess is available or not
		if (menuAccessIdOfChildWindow == 2) {
			alert('<s:text name="alert.AccessNotAvl"/>' + menuName + '<s:text name="alert.ContactSysAdm"/>');
		}

		return menuAccessIdOfChildWindow;
    }
	/*End:Code Modified by Chinniah on 15-Sep-2011 for Mantis 1483: Grant the role only view access to this menu option, I find that I am still able to 'Auth' and 'Change', where the buttons ought to be disabled*/
	function getMenuAccessId(a) {
		var menuAccessIdOfChildWindow = getMenuAccessIdOfChildWindow(a);
		var fwdSlashPos = new String(a).search("/"); 
		var gifPos = new String(a).search("gif");
		var menuName = new String(a).substr(fwdSlashPos+1,gifPos-fwdSlashPos-2);
		var QPos = new String(menuName).search("Queue"); 
		var SPos = new String(menuName).search("Status"); 
		var EPos = new String(menuName).search("Exception");
		if (QPos != -1) {
		    menuName = new String(menuName).substr(0,QPos)+" "+new String(menuName).substr(QPos);
		} else if (SPos != -1) {
		    menuName = new String(menuName).substr(0,SPos)+" "+new String(menuName).substr(SPos);
		} else if (EPos != -1) {
		    menuName = new String(menuName).substr(0,EPos)+" "+new String(menuName).substr(EPos);
		}
		if (menuAccessIdOfChildWindow == 2) {
			alert('<s:text name="alert.AccessNotAvl"/>' + menuName + '<s:text name="alert.ContactSysAdm"/>');
		}

		return menuAccessIdOfChildWindow;
    }
	

function sendRequest(sURL)
{
	var oXMLHTTP = new XMLHttpRequest();

	oXMLHTTP.open( "POST", sURL, false );
	oXMLHTTP.send();
	var count=new String(oXMLHTTP.responseText);
	
	return count;
}

	
		function openJavaWindow(a, left,top,width,height) {

        openWindow(a,'rolemaintenancechangeWindow','left='+left+',top='+top+',width='+width+',height='+height+',toolbar=0, resizable=yes, scrollbars=yes,status=yes');
    }

		/**
		 * Close the window 
		 **/
    function closeWindow(a) {
		window.close();
    }

	function openRefreshRateWindow(methodName){
		var param = 'screenOption.do?method='+methodName;
		param +='&parentRefreshMethod=unspecified';
		param +='&screenId='+<%=SwtConstants.WORKFLOW_MONITOR_ID%>;
		openWindow(param,'rolemaintenancechangeWindow','left=50,top=190,width=360,height=150,toolbar=0, resizable=yes, scrollbars=yes,status=yes');
		
		return param;
	}
	
	function help(){

		openWindow(buildPrintURL('print','Workflow Monitor'),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')
         }

	function openHelpWindow(methodName){
		
		openWindow(buildPrintURL('print','Workflow Monitor '),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true');
	}
	

	function submitForm(method, flag) {

	document.forms[0].submit();
	}

		/**
		 * Added for mantis 1443 
		 * Open the facility screen. It depends on scenario and the related properties of the selected count
		 *
		 **/
		function openFacility(scenarioTitle, useGeneric, facilityId,facilityName, scenarioId, entityId, currencyId, applyCurrencyThreshold,count,selectedCurrencyGroup){

			var appName = "<%=SwtUtil.appName%>";
			var requestURL = new String('<%=request.getRequestURL()%>');
			var idy = requestURL.indexOf('/'+appName+'/');
			requestURL=requestURL.substring(0,idy+1); 
			// If the use geneic is 'Y' then we have to display the generic display else a facility screen will be opened
			if (useGeneric == 'Y'){
				// Get the base query of the scenario id from data base through AJAX
					requestURL = requestURL + appName+"/scenMaintenance.do?method=getScenPropertiesForGeneric";
				requestURL = requestURL + "&scenarioId=" + scenarioId;
				var oXMLHTTP = new XMLHttpRequest();
				oXMLHTTP.open( "POST", requestURL, false );
				oXMLHTTP.send();
					var scenProperties = new String(oXMLHTTP.responseText);
					var baseQuery = scenProperties.split('<%=SwtConstants.SEPARATOR_RECORD%>')[0];
					var entityColumn = scenProperties.split('<%=SwtConstants.SEPARATOR_RECORD%>')[1];
					var ccyColumn = scenProperties.split('<%=SwtConstants.SEPARATOR_RECORD%>')[2];
					var refColumns = scenProperties.split('<%=SwtConstants.SEPARATOR_RECORD%>')[3];
					var facilityrefColumns = scenProperties.split('<%=SwtConstants.SEPARATOR_RECORD%>')[4];
					var refParams = scenProperties.split('<%=SwtConstants.SEPARATOR_RECORD%>')[5];
					var facilityRefParams = scenProperties.split('<%=SwtConstants.SEPARATOR_RECORD%>')[6];
				// Encode the base query do not have problems with special characters
					baseQuery = getMenuWindow().encode64(baseQuery);
					
					// We have to filter the data in the opened window, generic display screen
					var filter = "";
					if (entityId.toUpperCase() == 'ALL')
						filter == 'All' + '|';
					else
						filter = entityColumn + "<%=SwtConstants.SEPARATOR_RECORD%>" + entityId +"<%=SwtConstants.SEPARATOR_RECORD%>"+ 'str|';
					
					if (currencyId.toUpperCase() == 'ALL')
						filter += 'All';
					else
						filter += ccyColumn + "<%=SwtConstants.SEPARATOR_RECORD%>" + currencyId+"<%=SwtConstants.SEPARATOR_RECORD%>"+ 'str';
					
					// Encode the defined filter value as it may contains double quote (")
					filter = getMenuWindow().encode64(filter);
				  	var param = 'genericdisplay.do?method=genericDisplay';
					param+='&scenarioID='+scenarioId;
					param+='&fromSummaryScreen='+"true";
					param+='&scenarioTitle='+scenarioTitle;		
					param+='&refColumns='+refColumns;
					param+='&facilityRefColumns='+ facilityrefColumns;
					param+='&refParams='+ refParams;
					param+='&facilityRefParams='+ facilityRefParams;
					param+='&facilityID='+facilityId;
					param+='&facilityName='+facilityName;
					param+='&basequery='+baseQuery;
					param+= '&filter=' + filter;
					param+= '&selectedCurrencyGroup=' + selectedCurrencyGroup;
					param+='&applyCurrencyThreshold='+ applyCurrencyThreshold;

				
				// Open the generic screen
				openWindow(param,'genericdisplay','left=50,top=190,width=1230,height=480,toolbar=0, resizable=yes, scrollbars=yes','true');
			}else{
				// Open the movement summary screen if the facility id is 'MSD'
				if (facilityId == "MSD"){
					var sysdate = "<%=SwtUtil.getSystemDateString()%>";
					var url="outstandingmovement.do?";
					url += "method=flex";
					// Define the initial input screen as X for scenario summary
					url += "&initialinputscreen=X";
					url += "&totalFlag=Y";
					// the position level is 9, the hightest level. 
					url += "&posLvlId=9";
					url += "&currencyCode="+currencyId;
					url += "&entityId=" + entityId;
					url += "&currGrp="+selectedCurrencyGroup;
					url += "&date=" + sysdate;
					url += "&applyCurrencyThreshold=" + applyCurrencyThreshold;
					url += "&workflow=";
					url += "&scenarioId=" + scenarioId;
					openWindow(url, 'mvntSummaryScreenWindow','left=50,top=190,width=1280,height=680,toolbar=0, resizable=yes, scrollbars=yes,status=yes');
				}// Open the movement match summary display screen if the facility id is 'MATCH_DISPLAY_MANY'
				else if (facilityId == "MATCH_DISPLAY_MANY"){
					var sysdate = "<%=SwtUtil.getSystemDateString()%>";
					var url="movementmatchdisplay.do?";
					url += "&status=M";
					url += "&quality=D";
					url += "&matchCount="+count;
					url += "&currencyCode="+currencyId;
					url += "&entityId=" + entityId;
					url += "&date=";
					url += "&applyCurrencyThreshold=" + applyCurrencyThreshold;
					url += "&day=NotAll";
					url += "&currGrp="+selectedCurrencyGroup;
					url += "&scenarioId=" + scenarioId;
					url += "&dateTabIndFlag=N";
					url += "&dateTabInd=0";
					openWindow(url, 'mvntSummaryScreenWindow','left=50,top=190,width=1280,height=680,toolbar=0, resizable=yes, scrollbars=yes,status=yes');
				}else {
						 appName = "<%=SwtUtil.appName%>";
						 requestURL = new String('<%=request.getRequestURL()%>');
						 idy = requestURL.indexOf('/'+appName+'/');
						requestURL=requestURL.substring(0,idy+1); 
					requestURL = requestURL + appName+"/genericdisplay.do?method=getScreenDetails";
					requestURL = requestURL + "&facilityId=" + facilityId;
					var oXMLHTTP = new XMLHttpRequest();
					oXMLHTTP.open( "POST", requestURL, false );
					oXMLHTTP.send();
					var screenDetails=new String(oXMLHTTP.responseText);
					if(screenDetails == ""){
						return;
					}
					var screenDetailsList = screenDetails.split("<%=SwtConstants.SEPARATOR_SCREEN_DETAILS%>");
					programName = screenDetailsList[0];
					var actionPathStr = programName.replace('?', '.');
					var menuAccessIdChild = getMenuAccessIdOfChildWindow(actionPathStr);
					if (programName.indexOf("?") == -1)
						programName += '?';
					width = screenDetailsList[1];
					height = screenDetailsList[2];
						appName = "<%=SwtUtil.appName%>";
						requestURL = new String('<%=request.getRequestURL()%>');
						idy = requestURL.indexOf('/'+appName+'/');
						requestURL=requestURL.substring(0,idy+1); 
						requestURL = requestURL + appName+"/scenMaintenance.do?method=getAdditionalInfo";
						requestURL = requestURL + "&scenarioId=" + scenarioId;
						var oXMLHTTP = new XMLHttpRequest();
						oXMLHTTP.open( "POST", requestURL, false );
						oXMLHTTP.send();
						var additionalParams=new String(oXMLHTTP.responseText);
					var key = "&entityId="+ entityId +"&selectedEntityId=" + entityId;
					key += "&currencyId=" + currencyId;
						key += "&applyCurrencyThreshold=" + applyCurrencyThreshold;
					key += "&calledFrom=generic";
					key += "&menuAccessId="+menuAccessIdChild;
						key +=additionalParams;
					// Open the facility screen 
					openWindow(programName + key, 'facilityScreenWindow','left=0,top=55,width='+width+',height='+height+',toolbar=0, resizable=yes, scrollbars=yes,status=yes');
				}
			}
		}
		
		/**
		 * method to open the facility screen related to the facility id of a selected scenario
		 **/
		function goTo(facilityID, hostID, entityID, matchIdKey, currencyCodeKey, movementIdKey, sweepIdKey,additionalParams){
			
			if (hostID == "" || entityID == "") {
				alert("<s:text name='alert.FacilityMissingValues'/>");
			} else {
				if (!facilityAccess(hostID, entityID, facilityID, currencyCodeKey))
					alert("<s:text name='alert.accessToFacility'/>");
				else{							
					var appName = "<%=SwtUtil.appName%>";
					var requestURL = new String('<%=request.getRequestURL()%>');
					var idy = requestURL.indexOf('/'+appName+'/');
					requestURL=requestURL.substring(0,idy+1) ; 
					requestURL = requestURL + appName+"/genericdisplay.do?method=getScreenDetails";
					requestURL = requestURL + "&facilityId=" + facilityID;
					var oXMLHTTP = new XMLHttpRequest();
					oXMLHTTP.open( "POST", requestURL, false );
					oXMLHTTP.send();
					var screenDetails=new String(oXMLHTTP.responseText);
					if(screenDetails == ""){
						return;
					}
					var screenDetailsList = screenDetails.split("<%=SwtConstants.SEPARATOR_SCREEN_DETAILS%>");
					var programName = screenDetailsList[0];
					if (programName.indexOf("?") == -1)
						programName += '?';
					var width = screenDetailsList[1];
					var height = screenDetailsList[2];
					
					var key = "&hostId=" + hostID + "&entityId="+ entityID +"&selectedEntityId=" + entityID;
					key += "&matchId=" + matchIdKey; 
					key += "&calledFrom=generic";
					key += "&currencyId=" + currencyCodeKey;
					key += "&selectedMovementId=" + movementIdKey;
					key += "&selectedSweepId=" + sweepIdKey;
					key +=getMenuWindow().decode64(additionalParams);
					openWindow(programName + key, 'facilityScreenWindow','left=0,top=55,width='+width+',height='+height+',toolbar=0, resizable=yes, scrollbars=yes,status=yes');
				}
			}
		}
		
		/**
		 * Return true or false, if the user has access to a facility screen or not
		 * 
		 * @param hostId
		 * @param entityId
		 * @param facilityId
		 * @param currencyCode
		 **/
		function facilityAccess(hostId, entityId, facilityId, currencyCode){


			var appName = "<%=SwtUtil.appName%>";
			var requestURL = new String('<%=request.getRequestURL()%>');
			var idy = requestURL.indexOf('/'+appName+'/');
			requestURL=requestURL.substring(0,idy+1) ; 
			requestURL = requestURL + appName+"/genericdisplay.do?method=getFacilityAccess";
			requestURL = requestURL + "&hostId=" + hostId;
			requestURL = requestURL + "&entityId=" + entityId;
			requestURL = requestURL + "&facilityId=" + facilityId;
			requestURL = requestURL + "&currencyCode=" + currencyCode;
			var oXMLHTTP = new XMLHttpRequest();
			oXMLHTTP.open( "POST", requestURL, false );
			oXMLHTTP.send();
			var access=new String(oXMLHTTP.responseText);
			return (parseInt(access) == <%=SwtConstants.FACILITY_NO_ACCESS%>) ? false: true;
		}
		
		function openInstDetails(methodName, params){
			var param = '/' + appName + '/scenarioSummary.do?method='+methodName;
			param += '&allParams=' + params;
			var 	mainWindow = openWindow (param, 'alertInstanceDisplay','left=10,top=230,width=900,height=920,toolbar=0, resizable=yes, //status=yes, scrollbars=yes');	
			return false;
		}
		</script>
        <%@ include file="/angularscripts.jsp"%>
		<form id="exportDataForm" target="tmp" method="post">
		<input type="hidden" name="data" id="exportData"/>
		<input type="hidden" name="screen" id="exportDataScreen" value="<s:text name="workflowmonitor.title.window"/>"/>
		</form>
		<iframe name="tmp" width="0%" height="0%" src="#" />
	</body>
</html>