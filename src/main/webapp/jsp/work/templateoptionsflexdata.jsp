<?xml version="1.0" encoding="UTF-8" ?>
<!--
  - The main purpose of this jsp file is to load the resultant xml data for Forecast Template Options screen.
  - 
  - Author(s): Bala .D
  - Date: 08-05-2011
  -->
  
<%@ page contentType="text/xml" %>
<%@ taglib prefix="s" uri="/struts-tags" %>


<monitoroptions>
    <request_reply>
        <status_ok><s:property value="#request.reply_status_ok" /></status_ok>
        <message><s:property value="#request.reply_message" /></message>
        <location />
    </request_reply>

	<s:set var="templateOptions" value="#request.forecastTemplateOptions" />
	
    <selects>
        <select id="currency">
            <option value="*" selected="0"> </option>
            <s:iterator var="currency" value="#request.currencyList">
                <option 
                    value="<s:property value="#currency.value" />" 
                    selected="<s:if test="#templateOptions.id.currencyCode == #currency.value">1</s:if><s:else>0</s:else>"
                ><s:property value="#currency.label" /></option>
            </s:iterator>
        </select>

        <select id="entity">
            <s:iterator var="entity" value="#request.entities">
                <option 
                    value="<s:property value="#entity.value" />" 
                    selected="<s:if test="#forecastMonitorForm.forecastTemplateOptions.id.entityId == #entity.value">1</s:if><s:else>0</s:else>"
                ><s:property value="#entity.label" /></option>
            </s:iterator>
        </select>

        <select id="template">
            <s:iterator var="template" value="#request.templateList">
                <option 
                    value="<s:property value="#template.value" />" 
                    selected="<s:if test="#forecastMonitorForm.forecastTemplateOptions.templateId == #template.value">1</s:if><s:else>0</s:else>"
                ><s:property value="#template.label" /></option>
            </s:iterator>
        </select>
    </selects>    
</monitoroptions>
