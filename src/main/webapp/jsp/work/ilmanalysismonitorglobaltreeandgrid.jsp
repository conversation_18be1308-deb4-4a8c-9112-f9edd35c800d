<?xml version="1.0" encoding="UTF-8"?>
<%@ page contentType="text/xml"%>
<%@ page import="org.swallow.work.model.InputExceptionsDataModel"%>
<%@ page import="org.swallow.util.OpTimer"%>
<%@ page import="java.text.NumberFormat"%>
<%@ taglib prefix="s" uri="/struts-tags" %>

<intradayliquidity
		    timeframe="${timeframe}"
		    currencyDecimalPlaces="${currencyDecimalPlaces}"
		    currencyMutiplierValue="${currencyMutiplierValue}"
		    profileTreeData="${profileTreeData}"
		    profileGlobalGroupGrid="${profileGlobalGroupGrid}"
		    lastRefTime="<s:property value='#request.lastRefTime' />"
		    selectedDateTimeFame="<s:property value='#request.selectedDateTimeFame' />"
		    sysDateWithCcyTimeFrame="<s:property value='#request.sysDateWithCcyTimeFrame' />"
		    currencyId="<s:property value='#request.currencyId' />"
		    entityId="<s:property value='#request.entityId' />"
		    includeOpenMvnts="${includeOpenMvnts}"
		    sumByCutOff="${sumByCutOff}">

    <request_reply>
        <status_ok><s:property value="#request.reply_status_ok" /></status_ok>
        <message><s:property value="#request.reply_message" /></message>
        <location />
    </request_reply>
	
	
	<group>
		<grid>
			      <metadata>
			        <columns>
			            <s:iterator value="#request.grpColOrder" var="order">
			                <s:if test='#order == "use"'>
			                    <column heading="<s:text name='ilmanalysismonitor.grid.use'/>"
			                            tooltip="<s:text name='ilmanalysismonitor.grid.use.tooltip'/>"
			                            dataelement="use"
			                            width="<s:property value='#request.grpColWidth.use'/>"
			                            draggable="false"
			                            filterable="false"
			                            sort="true"
			                            type="checkbox"
			                            editable="true"/>
			                </s:if>
			                <s:if test='#order == "group"'>
			                    <column heading="<s:text name='ilmanalysismonitor.grid.group'/>"
			                            tooltip="<s:text name='ilmanalysismonitor.grid.group.tooltip'/>"
			                            dataelement="group"
			                            width="<s:property value='#request.grpColWidth.group'/>"
			                            draggable="true"
			                            filterable="false"
			                            sort="true"
			                            type="str"
			                            editable="false"/>
			                </s:if>
			                <s:if test='#order == "thresholds"'>
			                    <column heading="<s:text name='ilmanalysismonitor.grid.thresholds'/>"
			                            tooltip="<s:text name='ilmanalysismonitor.grid.thresholds.tooltip'/>"
			                            dataelement="thresholds"
			                            width="<s:property value='#request.grpColWidth.thresholds'/>"
			                            draggable="true"
			                            filterable="false"
			                            sort="true"
			                            type="str"
			                            editable="false"/>
			                </s:if>
			                <s:if test='#order == "type"'>
			                    <column heading="<s:text name='ilmanalysismonitor.grid.type'/>"
			                            tooltip="<s:text name='ilmanalysismonitor.grid.type.tooltip'/>"
			                            dataelement="type"
			                            width="<s:property value='#request.grpColWidth.type'/>"
			                            draggable="true"
			                            filterable="false"
			                            sort="true"
			                            type="str"
			                            editable="false"/>
			                </s:if>
			                <s:if test='#order == "creator"'>
			                    <column heading="<s:text name='ilmanalysismonitor.grid.creator'/>"
			                            tooltip="<s:text name='ilmanalysismonitor.grid.creator.tooltip'/>"
			                            dataelement="creator"
			                            width="<s:property value='#request.grpColWidth.creator'/>"
			                            draggable="true"
			                            filterable="false"
			                            sort="true"
			                            type="str"
			                            editable="false"/>
			                </s:if>
			            </s:iterator>
			        </columns>
			    </metadata>

			
			<rows size="${grpRowSize}">
				<s:iterator value="#request.grpsDetaillist" var="grpsDetaillist">
					 <row>
		                <use enabled="true">
		                	<s:if test='#grpsDetaillist.global == "Y"'>true</s:if><s:else>false</s:else>
		          		 </use>
						
		                <group editable="false">
		                    <s:property value="#grpsDetaillist.id.ilmGroupId"/> : <s:property value="#grpsDetaillist.ilmGroupName"/>
		                </group>
		                <group_name editable="false">
		                    <s:property value="#grpsDetaillist.ilmGroupName"/>
		                </group_name>
		                <group_id editable="false">
		                    <s:property value="#grpsDetaillist.id.ilmGroupId"/>
		                </group_id>
		                <default_legend_text editable="false">
		                    <s:property value="#grpsDetaillist.defaultLegendText"/>
		                </default_legend_text>
		                <extsod editable="false"><s:property value="#grpsDetaillist.externalSOD"/></extsod>
		                <exteod editable="false"><s:property value="#grpsDetaillist.externalEOD"/></exteod>
		                <fcastsod editable="false"><s:property value="#grpsDetaillist.forecastSOD"/></fcastsod>
		                <fcasteod editable="false"><s:property value="#grpsDetaillist.forecastEOD"/></fcasteod>
		                <thresholds editable="false">
		                    Min1:<s:property value="#grpsDetaillist.thresholdMin1"/>; Min2:<s:property value="#grpsDetaillist.thresholdMin2"/>; Max1:<s:property value="#grpsDetaillist.thresholdMax1"/>; Max2:<s:property value="#grpsDetaillist.thresholdMax2"/>
		                </thresholds>
		                <type editable="false">
		                    <s:property value="#grpsDetaillist.publicPrivate"/>
		                </type>
		                <creator editable="false">
		                    <s:property value="#grpsDetaillist.createdByUser"/>
		                </creator>
		            </row>
		        </s:iterator>
			</rows>
			
		</grid>
	</group>
	<scenario>
    <grid>
        <metadata>
            <columns>
                <s:iterator value="#request.scenColOrder" var="order">
                    <s:if test='#order == "use"'>
                        <column heading="<s:text name='ilmanalysismonitor.grid.use'/>"
                                tooltip="<s:text name='ilmanalysismonitor.grid.use.tooltip'/>"
                                dataelement="use"
                                width="<s:property value='#request.scenColWidth.use'/>"
                                draggable="false"
                                filterable="false"
                                sort="true"
                                type="checkbox"
                                editable="true"/>
                    </s:if>
                    <s:if test='#order == "scenario"'>
                        <column heading="<s:text name='ilmanalysismonitor.grid.scenario'/>"
                                tooltip="<s:text name='ilmanalysismonitor.grid.scenario.tooltip'/>"
                                dataelement="scenario"
                                width="<s:property value='#request.scenColWidth.scenario'/>"
                                draggable="true"
                                filterable="false"
                                sort="true"
                                type="str"
                                editable="false"/>
                    </s:if>
                    <s:if test='#order == "name"'>
                        <column heading="<s:text name='ilmanalysismonitor.grid.name'/>"
                                tooltip="<s:text name='ilmanalysismonitor.grid.name.tooltip'/>"
                                dataelement="name"
                                width="<s:property value='#request.scenColWidth.name'/>"
                                draggable="true"
                                filterable="false"
                                sort="true"
                                type="str"
                                editable="false"/>
                    </s:if>
                    <s:if test='#order == "type"'>
                        <column heading="<s:text name='ilmanalysismonitor.grid.type'/>"
                                tooltip="<s:text name='ilmanalysismonitor.grid.type.tooltip'/>"
                                dataelement="type"
                                width="<s:property value='#request.scenColWidth.type'/>"
                                draggable="true"
                                filterable="false"
                                sort="true"
                                type="str"
                                editable="false"/>
                    </s:if>
                    <s:if test='#order == "creator"'>
                        <column heading="<s:text name='ilmanalysismonitor.grid.creator'/>"
                                tooltip="<s:text name='ilmanalysismonitor.grid.creator.tooltip'/>"
                                dataelement="creator"
                                width="<s:property value='#request.scenColWidth.creator'/>"
                                draggable="true"
                                filterable="false"
                                sort="true"
                                type="str"
                                editable="false"/>
                    </s:if>
                </s:iterator>
            </columns>
        </metadata>

        <rows size="${scnRowSize}">
	            <s:iterator value="#request.scenariosDetailList" var="scenariosDetailList">
	                <row>
	                    <use enabled="false">
	                        <s:if test='#scenariosDetailList.id.ilmScenarioId == "Standard"'>true</s:if><s:else>false</s:else>
	                    </use>
	                    <scenario editable="false">
	                        <s:property value="#scenariosDetailList.id.ilmScenarioId"/>
	                    </scenario>
	                    <name editable="false">
	                        <s:property value="#scenariosDetailList.ilmScenarioName"/>
	                    </name>
	                    <type editable="false">
	                        <s:property value="#scenariosDetailList.publicPrivate"/>
	                    </type>
	                    <creator editable="false">
	                        <s:property value="#scenariosDetailList.createdByUser"/>
	                    </creator>
	                </row>
	            </s:iterator>
	        </rows>
	    </grid>
	</scenario>
	<balance>
    <grid>
        <metadata>
            <columns>
                <s:iterator value="#request.balColOrder" var="order">
                    <s:if test='#order == "use"'>
                        <column heading="<s:text name='ilmanalysismonitor.grid.use'/>"
                                tooltip="<s:text name='ilmanalysismonitor.grid.use.tooltip'/>"
                                dataelement="use"
                                width="<s:property value='#request.balColWidth.use'/>"
                                draggable="false"
                                filterable="false"
                                sort="true"
                                type="checkbox"
                                editable="false"/>
                    </s:if>
                    <s:if test='#order == "group"'>
                        <column heading="<s:text name='ilmanalysismonitor.grid.group'/>"
                                tooltip="<s:text name='ilmanalysismonitor.grid.group.tooltip'/>"
                                dataelement="group"
                                width="<s:property value='#request.balColWidth.group'/>"
                                draggable="true"
                                filterable="false"
                                sort="true"
                                type="str"
                                editable="false"/>
                    </s:if>
                    <s:if test='#order == "extsod"'>
                        <column heading="<s:text name='ilmanalysismonitor.grid.extsod'/>"
                                tooltip="<s:text name='ilmanalysismonitor.grid.extsod.tooltip'/>"
                                dataelement="extsod"
                                width="<s:property value='#request.balColWidth.extsod'/>"
                                draggable="true"
                                filterable="false"
                                sort="true"
                                type="num"
                                editable="false"/>
                    </s:if>
                    <s:if test='#order == "exteod"'>
                        <column heading="<s:text name='ilmanalysismonitor.grid.exteod'/>"
                                tooltip="<s:text name='ilmanalysismonitor.grid.exteod.tooltip'/>"
                                dataelement="exteod"
                                width="<s:property value='#request.balColWidth.exteod'/>"
                                draggable="true"
                                filterable="false"
                                sort="true"
                                type="num"
                                editable="false"/>
                    </s:if>
                    <s:if test='#order == "fcastsod"'>
                        <column heading="<s:text name='ilmanalysismonitor.grid.fcastsod'/>"
                                tooltip="<s:text name='ilmanalysismonitor.grid.fcastsod.tooltip'/>"
                                dataelement="fcastsod"
                                width="<s:property value='#request.balColWidth.fcastsod'/>"
                                draggable="true"
                                filterable="false"
                                sort="true"
                                type="num"
                                editable="false"/>
                    </s:if>
                    <s:if test='#order == "fcasteod"'>
                        <column heading="<s:text name='ilmanalysismonitor.grid.fcasteod'/>"
                                tooltip="<s:text name='ilmanalysismonitor.grid.fcasteod.tooltip'/>"
                                dataelement="fcasteod"
                                width="<s:property value='#request.balColWidth.fcasteod'/>"
                                draggable="true"
                                filterable="false"
                                sort="true"
                                type="num"
                                editable="false"/>
                    </s:if>
                    <s:if test='#order == "openunexp"'>
                        <column heading="<s:text name='ilmanalysismonitor.grid.openunexp'/>"
                                tooltip="<s:text name='ilmanalysismonitor.grid.openunexp.tooltip'/>"
                                dataelement="openunexp"
                                width="<s:property value='#request.balColWidth.openunexp'/>"
                                draggable="true"
                                filterable="false"
                                sort="true"
                                type="num"
                                editable="false"/>
                    </s:if>
                    <s:if test='#order == "openunsett"'>
                        <column heading="<s:text name='ilmanalysismonitor.grid.openunsett'/>"
                                tooltip="<s:text name='ilmanalysismonitor.grid.openunsett.tooltip'/>"
                                dataelement="openunsett"
                                width="<s:property value='#request.balColWidth.openunsett'/>"
                                draggable="true"
                                filterable="false"
                                sort="true"
                                type="num"
                                editable="false"/>
                    </s:if>
                    <s:if test='#order == "lastupdate"'>
                        <column heading="<s:text name='ilmanalysismonitor.grid.lastupdate'/>"
                                tooltip="<s:text name='ilmanalysismonitor.grid.lastupdate.tooltip'/>"
                                dataelement="lastupdate"
                                width="<s:property value='#request.balColWidth.lastupdate'/>"
                                draggable="true"
                                filterable="false"
                                sort="true"
                                type="date"
                                editable="false"/>
                    </s:if>
                </s:iterator>
            </columns>
        </metadata>

        <rows size="${grpRowSize}">
            <s:iterator value="#request.grpsDetaillistForBalance" var="grpsDetaillistForBalance">
                <row>
                    <use enabled="true">
                        <s:if test='#grpsDetaillistForBalance.global == "Y"'>true</s:if><s:else>false</s:else>
                    </use>
                    <group editable="false">
                        <s:property value="#grpsDetaillistForBalance.id.ilmGroupId"/> : <s:property value="#grpsDetaillistForBalance.ilmGroupName"/>
                    </group>
                    <group_id editable="false">
                        <s:property value="#grpsDetaillistForBalance.id.ilmGroupId"/>
                    </group_id>
                    <extsod editable="false"><s:property value="#grpsDetaillistForBalance.externalSOD"/></extsod>
                    <exteod editable="false"><s:property value="#grpsDetaillistForBalance.externalEOD"/></exteod>
                    <fcastsod editable="false"><s:property value="#grpsDetaillistForBalance.forecastSOD"/></fcastsod>
                    <fcasteod editable="false"><s:property value="#grpsDetaillistForBalance.forecastEOD"/></fcasteod>
                    <openunexp editable="false"><s:property value="#grpsDetaillistForBalance.openUnexp"/></openunexp>
                    <openunsett editable="false"><s:property value="#grpsDetaillistForBalance.openUnsett"/></openunsett>
                    <lastupdate editable="false"><s:property value="#grpsDetaillistForBalance.lastUpdated"/></lastupdate>
                </row>
            </s:iterator>
        </rows>
    </grid>
</balance>
	<!-- Data for tree -->
<tree label="">
    <s:iterator value="#request.treeGrpsDetail" var="group">
        <node type="group"
              label="<s:property value='#group.id.ilmGroupId'/>"
              tooltip="<s:property value='#group.ilmGroupName'/>"
              id="<s:property value='#group.id.ilmGroupId'/>.grp"
              isBranch="true"
              visible="<s:if test='#group.global == "Y"'>true</s:if><s:else>false</s:else>">
              <node type="scenario"
					      label="<s:text name='ilmanalysismonitor.tree.thresholds'/>"
					      tooltip="<s:text name='ilmanalysismonitor.tree.thresholds'/>"
					      isBranch="false"
					      selected="true"
					      visible="true"
					      yField="<s:property value='#group.id.ilmGroupId'/>.Thresholds"
					/>
            <s:iterator value="#request.treeScenariosDetail" var="scenario">
                <node type="scenario"
                      label="<s:property value='#scenario.id.ilmScenarioId'/>"
                      tooltip="<s:property value='#scenario.ilmScenarioName'/>"
                      id="<s:property value='#group.id.ilmGroupId'/>.<s:property value='#scenario.id.ilmScenarioId'/>.scen"
                      isBranch="true"
                      selected="<s:if test='#scenario.id.ilmScenarioId == "Standard"'>true</s:if><s:else>false</s:else>"
                      visible="<s:if test='#scenario.id.ilmScenarioId == "Standard"'>true</s:if><s:else>false</s:else>">
                    <node label="<s:text name='ilmanalysismonitor.tree.actBalance'/>"
                          selected="true"
                          isBranch="false"
                          yField="<s:property value='#group.id.ilmGroupId'/>.<s:property value='#scenario.id.ilmScenarioId'/>.ab"/>
                    <node label="<s:text name='ilmanalysismonitor.tree.forebasic'/>"
                          selected="true"
                          isBranch="false"
                          yField="<s:property value='#group.id.ilmGroupId'/>.<s:property value='#scenario.id.ilmScenarioId'/>.fbb"/>
                    <node label="<s:text name='ilmanalysismonitor.tree.forecIncludeAct'/>"
                          selected="true"
                          isBranch="false"
                          yField="<s:property value='#group.id.ilmGroupId'/>.<s:property value='#scenario.id.ilmScenarioId'/>.fbia"/>
                    <node label="<s:text name='ilmanalysismonitor.tree.accTotals'/>"
                          id="<s:property value='#group.id.ilmGroupId'/>.<s:property value='#scenario.id.ilmScenarioId'/>.tot"
                          visible="true"
                          isBranch="true">
                        <node label="<s:text name='ilmanalysismonitor.tree.accActualCD'/>"
                              selected="true"
                              isBranch="false"
                              yField="<s:property value='#group.id.ilmGroupId'/>.<s:property value='#scenario.id.ilmScenarioId'/>.aac,<s:property value='#group.id.ilmGroupId'/>.<s:property value='#scenario.id.ilmScenarioId'/>.aad"/>
                        <node label="<s:text name='ilmanalysismonitor.tree.accForeCD'/>"
                              selected="true"
                              isBranch="false"
                              yField="<s:property value='#group.id.ilmGroupId'/>.<s:property value='#scenario.id.ilmScenarioId'/>.afc,<s:property value='#group.id.ilmGroupId'/>.<s:property value='#scenario.id.ilmScenarioId'/>.afd"/>
                    </node>
                </node>
            </s:iterator>
        </node>
    </s:iterator>
</tree>

</intradayliquidity>