<?xml version="1.0" encoding="UTF-8"?>
<%@ page contentType="text/xml"%>
<%@ page import="org.swallow.util.OpTimer"%>
<%@ page import="java.text.NumberFormat"%>


<scenarioSummaryScreen
		currencythreshold="<s:property value='#request.currencythreshold' />"
		popupScenarios="<s:property value='#request.popupScenarios' />"
		flashScenarios="<s:property value='#request.flashScenarios' />"
		emailScenarios="<s:property value='#request.emailScenarios' />"
		hidezerocounts="<s:property value='#request.hidezerocounts' />"
		alertablescenarios="<s:property value='#request.alertablescenarios' />"
		lastRefTime="<s:property value='#request.lastRefTime' />"
		dateFormat="<s:property value='#request.dateFormat' />"
		currencyFormat="<s:property value='#request.currencyFormat' />"
		autoRefreshRate="<s:property value='#request.autoRefreshRate' />"
		displayedDate="<s:property value='#request.displayedDate' />"
>
	<request_reply>
		<status_ok>
			<s:property value="#request.reply_status_ok" />
		</status_ok>
		<message><s:property value="#request.reply_message" /></message>
		<location />
	</request_reply>
	<timing>
		<s:iterator value='#request.opTimes' var='opTime' >
			<operation id="${opTime.key}">${opTime.value}</operation>
		</s:iterator>
	</timing>
	<selects>
		<select id="entity">
			<s:iterator value='#request.entities' var='entity' >
				<option
						value="<s:property value='#request.entity.value' />"
						selected="${entity.value eq entityId ? '1': '0'}"
				><s:property value='#request.entity.label' /></option>
			</s:iterator>
		</select>
	</selects>

	<tabsCategory  size="${tabSize}">
		<s:iterator value='#request.tabCategoryDetails' var='tab' >
			<displaytab count="<s:property value='#request.tab.count' />"
						tabName="<s:property value='#request.tab.tabName' />"
						tabId="<s:property value='#request.tab.tabId' />"
			><s:property value='#request.tab.tabNameAsString' /></displaytab>
		</s:iterator>
		<selectedIndex
				tabindex="<s:property value='#request.selectedTabIndexCategory' />" index="<s:property value='#request.selectedIndexCategory' />"
		></selectedIndex>
	</tabsCategory>

</scenarioSummaryScreen>
