 <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@ include file="/taglib.jsp"  %>
<%@ page import="org.swallow.util.SwtConstants" %>
<%@ page import="org.swallow.util.CommonDataManager" %>
<%@ page import="org.swallow.util.SwtUtil" %>
<html>
<head>
<title><s:text name="movSearch.title.mainWindow"/></title>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">

<SCRIPT language="JAVASCRIPT">
var dateSelected=false;
var initialscreen = "${requestScope.initialinputscreen}";

var cancelcloseElements = new Array(1);
	cancelcloseElements[0]="closebutton";

var currencyFormat = '<s:property value="#request.session.CDM.currencyFormat"/>';
var dateFormat = '<s:property value="#request.session.CDM.dateFormat" />';
var sysDateAsstring = "${requestScope.sysDateAsstring}";
mandatoryFieldsArray = ["movementsearchValuefrom"] ;
var includeRef;
var excludeRef; 
var includeRefFlag;
var excludeRefFlag;
var includeRef1Flag;
var excludeRef1Flag;
var includeRef2Flag;
var excludeRef2Flag;
var includeRef3Flag;
var excludeRef3Flag;
var includeRef4Flag;
var excludeRef4Flag;
var extraFilter="";
var appName = "<%=SwtUtil.appName%>"; 
function submitForm(methodName){
	document.forms[0].method.value = methodName;
	document.forms[0].extraFilter.value = extraFilter;
	document.forms[0].submit();
}

function getAmtValue(amt){
var amtvalue='';
 for(var idx=0; idx < amt.length; idx++){
	el = amt.charAt(idx);	
	if(el!=',' && el!='.'){	
	 amtvalue += el;
	}	 
 }
 return amtvalue; 
}

function validateAmount(amtover,amtunder){

  if(amtover !="" && amtunder !="")
    {
	var amountOver = new Number(getAmtValue(amtover)) ;
	var amountUnder = new Number(getAmtValue(amtunder)) ;	
	if(amountUnder >= amountOver){
	return true;
	} 
	else{
	return false;
	}

   }
   else{
	return true;
   }

}

function checkAccountType(methodName)
{
	enableFields();
	document.forms[0].method.value = methodName;
	document.forms[0].isAmountDiffer.value = '${requestScope.isAmountDiffer}';
	document.forms[0].selectedMovementsAmount.value = '${requestScope.selectedMovementsAmount}';
	
	document.forms[0].submit();

}
function checkAccount(methodName)
{
	enableFields();
	document.forms[0].method.value = methodName;	
	document.forms[0].submit();

}

function enableFields(){
	document.forms[0].elements["movementsearch.id.entityId"].disabled = "";
	document.forms[0].elements['movementsearch.valueFromDateAsString'].disabled="";
	document.forms[0].elements['movementsearch.valueToDateAsString'].disabled="";
	document.forms[0].elements["movementsearch.matchStatus"].disabled = "";
	document.forms[0].elements["movementsearch.accountClass"].disabled = "";
	document.forms[0].elements['movementsearch.currencyCode'].disabled="";
	
}
function getRadioButtonValue(button){
var d = button;
var len = d.length ;
for(i=0;i<len;i++)
{
	if(d[i].checked)
	 return d[i].value;
}
if(i == len)
 return "null";
}
/**
* This Methos is used to submit the form, when save button click.
* 
* @param methodName
* @return param
*/
var globalMethodName
function getMovementDetails(methodName){
	
	globalMethodName = methodName
	var compare_Date = 0;
	if(validateDateField(document.forms[0].elements['movementsearch.valueFromDateAsString'],'fromDateAsString',dateFormat)){
		if(document.forms[0].elements['movementsearch.valueToDateAsString'].value != ""){
			if (validateDateField(document.forms[0].elements['movementsearch.valueToDateAsString'],'toDateAsString',dateFormat)){
				compare_Date = comparedates(document.forms[0].elements['movementsearch.valueFromDateAsString'].value,document.forms[0].elements['movementsearch.valueToDateAsString'].value,dateFormat,'From Date','To Date');
			}
		}else if(document.forms[0].elements['movementsearch.valueToDateAsString'].value == ""){
			compare_Date =1;
		}
	}
	if(compare_Date == 1 ){
		if(validateCurrency(document.forms[0].elements['movementsearch.amountoverasstring'],'movementsearch.amountoverasstring',currencyFormat)){
			if(validateCurrency(document.forms[0].elements['movementsearch.amountunderasstring'],'movementsearch.amountunderasstring',currencyFormat)){
				var amountover = document.forms[0].elements['movementsearch.amountoverasstring'].value ;
				var amountunder = document.forms[0].elements['movementsearch.amountunderasstring'].value;
				if(validateAmount(amountover,amountunder)){
					var fromTimeValue=validateField(document.forms[0].elements['movementsearch.timefrom'],'Number 2','timePat'); 
					if(fromTimeValue){
						var toTimeValue=validateField(document.forms[0].elements['movementsearch.timeto'],'Number 2','timePat'); 
						if(toTimeValue){
						    var includeRefName = validateField(document.forms[0].elements['referencemap.includeRef'], 'referencemap.includeRef', 'alphaNumPatWithoutPercentage');
						    var excludeRefName = validateField(document.forms[0].elements['referencemap.excludeRef'], 'referencemap.excludeRef', 'alphaNumPatWithoutPercentage');
							if(includeRefName && excludeRefName){
								includeRef = document.forms[0].elements['referencemap.includeRef'].value;
								excludeRef = document.forms[0].elements['referencemap.excludeRef'].value;
								if (document.forms[0].elements['referencemap.includeRefFlag'].checked == false) {
									includeRefFlag = "N";
						        } else {
						        	includeRefFlag = "Y";
						        }
								if (document.forms[0].elements['referencemap.includeRef1Flag'].checked == false) {
									includeRef1Flag = "N";
						        } else {
						        	includeRef1Flag = "Y";
						        }
								if (document.forms[0].elements['referencemap.includeRef2Flag'].checked == false) {
									includeRef2Flag = "N";
						        } else {
						        	includeRef2Flag = "Y";
						        }
								if (document.forms[0].elements['referencemap.includeRef3Flag'].checked == false) {
									includeRef3Flag = "N";
						        } else {
						        	includeRef3Flag = "Y";
						        }
								if (document.forms[0].elements['referencemap.includeRef4Flag'].checked == false) {
									includeRef4Flag = "N";
						        } else {
						        	includeRef4Flag = "Y";
						        }
								
								if (document.forms[0].elements['referencemap.excludeRefFlag'].checked == false) {
									excludeRefFlag = "N";
						        } else {
						        	excludeRefFlag = "Y";
						        }
								if (document.forms[0].elements['referencemap.excludeRef1Flag'].checked == false) {
									excludeRef1Flag = "N";
						        } else {
						        	excludeRef1Flag = "Y";
						        }
								if (document.forms[0].elements['referencemap.excludeRef2Flag'].checked == false) {
									excludeRef2Flag = "N";
						        } else {
						        	excludeRef2Flag = "Y";
						        }
								if (document.forms[0].elements['referencemap.excludeRef3Flag'].checked == false) {
									excludeRef3Flag = "N";
						        } else {
						        	excludeRef3Flag = "Y";
						        }
								if (document.forms[0].elements['referencemap.excludeRef4Flag'].checked == false) {
									excludeRef4Flag = "N";
						        } else {
						        	excludeRef4Flag = "Y";
						        }
								var prodType=validateField(document.forms[0].elements['movementsearch.productType'],'movementsearch.productType','alphaNumPatWithoutPercentage');
								if (includeRef != '' || excludeRef != '')
								{
									var buttonId = null;
									if (includeRefFlag == 'N' &&  excludeRefFlag == 'N' && includeRef1Flag == 'N' && excludeRef1Flag == 'N' 
											&& includeRef2Flag == 'N' && excludeRef2Flag == 'N' && includeRef3Flag == 'N' && excludeRef3Flag 
											== 'N' && includeRef4Flag == 'N' && excludeRef4Flag == 'N') 
									{
										ShowErrMsgWindowWithBtn('' ,'<s:text name="alert.movementSearch.reference"/>' , YES_NO, yesContinue, noContinue);
								   return;
									}
								}
								
								if(prodType){								
									var matchParty=validateField(document.forms[0].elements['movementsearch.matchingParty'],'movementsearch.matchingParty','alphaNumPatWithoutPercentage');
									if(matchParty){
										elementTrim(document.forms[0]);
										var movementType = getRadioButtonValue(document.forms[0].elements['movementsearch.movementType']);
										var sign = getRadioButtonValue(document.forms[0].elements['movementsearch.sign']);
										var predictStatus = getRadioButtonValue(document.forms[0].elements['movementsearch.predictStatus']);
										
										var extBalStatus = getRadioButtonValue(document.forms[0].elements['movementsearch.extBalStatus']);								
										var currencyCode = document.forms[0].elements['movementsearch.currencyCode'].value
										var beneficiaryId = document.forms[0].elements["movementsearch.beneficiaryId"].value;
										var custodianId = document.forms[0].elements["movementsearch.custodianId"].value;
										var positionlevel = document.forms[0].elements["movementsearch.positionLevelAsString"].value;
										var accountId = document.forms[0].elements["movementsearch.accountId"].value ;
										var group = document.forms[0].elements["movementsearch.group"].value ;
										var metaGroup = document.forms[0].elements["movementsearch.metaGroup"].value ;
										var bookCode = document.forms[0].elements["movementsearch.bookCode"].value ;
										var valueFromDateAsString = document.forms[0].elements['movementsearch.valueFromDateAsString'].value;
										var valueToDateAsString = document.forms[0].elements['movementsearch.valueToDateAsString'].value;
										var timefrom = document.forms[0].elements['movementsearch.timefrom'].value; 
										var timeto = document.forms[0].elements['movementsearch.timeto'].value; 
										var messageId = document.forms[0].elements['movementsearch.messageId'].value;
										var inputDateAsString = document.forms[0].elements['movementsearch.inputDateAsString'].value;
										var counterPartyId = document.forms[0].elements['movementsearch.counterPartyId'].value;
										var matchStatus = getRadioButtonValue(document.forms[0].elements['movementsearch.matchStatus']);
										var accountClass = getRadioButtonValue(document.forms[0].elements['movementsearch.accountClass']);
										var openFlag=getRadioButtonValue(document.forms[0].elements["movementsearch.openFlag"]);
										var uetr = document.forms[0].elements['movementsearch.uetr'].value;
										var matchingParty = document.forms[0].elements['movementsearch.matchingParty'].value;
										var productType = document.forms[0].elements['movementsearch.productType'].value;
										var postingDateFrom = document.forms[0].elements['movementsearch.postingFromDateAsString'].value;
										var postingDateTo = document.forms[0].elements['movementsearch.postingToDateAsString'].value;
										 
										 if (inputDateAsString != "") {
											if(validateDateField(document.forms[0].elements['movementsearch.inputDateAsString'],'movementsearch.inputdate',dateFormat)){
											} else {
												document.forms[0].elements['movementsearch.inputDateAsString'].focus();
												return false;										
											}
										 }
										 
										var comparePostingDate = 0;
										
										if (postingDateFrom == "" && postingDateTo == ""){	
											comparePostingDate = 1;
										}
										else{
											if(validateDateField(document.forms[0].elements['movementsearch.postingFromDateAsString'],'fromDateAsString',dateFormat)){
												if(document.forms[0].elements['movementsearch.postingToDateAsString'].value != ""){
													if (validateDateField(document.forms[0].elements['movementsearch.postingToDateAsString'],'toDateAsString',dateFormat)){
														comparePostingDate = comparedates(document.forms[0].elements['movementsearch.postingFromDateAsString'].value,document.forms[0].elements['movementsearch.postingToDateAsString'].value,dateFormat,'From Date','To Date');													
													}
												}else if(document.forms[0].elements['movementsearch.postingToDateAsString'].value == "")
												{
													comparePostingDate =1;
												}										
											}
										}
										if (document.forms[0].elements['movementsearch.uetr'].value){
						                 	if(!validateUETR(document.forms[0].elements['movementsearch.uetr'].value))
						                 	return ;
						                 	} 
										if (comparePostingDate == 1) {
										
											var userid="<%=SwtUtil.getCurrentUserId(request.getSession())%>";
											if (currencyCode=="All")
												currencyCode="All:"+userid;
											
											document.forms[0].method.value = methodName;
											var param = 'outstandingmovement.do?method=flex';
											param += '&entityId=' + document.forms[0].elements['movementsearch.id.entityId'].value;
											param += '&movementType=' + movementType ;
											param += '&sign=' + sign ;
											param += '&predictStatus=' + predictStatus ;
											
											param += '&extBalStatus=' + extBalStatus ;									
											param += '&amountover=' + amountover ;
											param += '&amountunder=' + amountunder;
											param += '&currencyCode=' + currencyCode  ;
											param += '&beneficiaryId=' + beneficiaryId ;
											param += '&custodianId=' + custodianId ;
											param += '&positionlevel=' + positionlevel ;
											param += '&accountId=' + accountId ;
											param += '&group=' + group  ;
											param += '&metaGroup=' + metaGroup ;
											param += '&bookCode=' + bookCode ;
											param += '&valueFromDateAsString=' + valueFromDateAsString ;
											param += '&valueToDateAsString=' + valueToDateAsString
											 param += '&timefrom=' + timefrom  ; 
											 param += '&timeto=' + timeto ;  
											param += '&reference=' + getMenuWindow().encode64(getxmlRef());
											param += '&messageId=' + messageId.replace(/&/g,"amp;").replace(/\+/g,"plus;");
											param += '&inputDateAsString=' + inputDateAsString ;
											param += '&counterPartyId=' + counterPartyId ;
											param += '&matchStatus=' + matchStatus  ;
											param += '&initialinputscreen=' + initialscreen  ;
											param += '&accountClass=' + accountClass  ;
											
											param += '&isAmountDiffer=' + '${requestScope.isAmountDiffer}' ;
											param += '&selectedMovementsAmount=' + '${requestScope.selectedMovementsAmount}' ;
											param += '&openFlag=' + openFlag ;								
											param += '&uetr=' + uetr;
											param += '&matchingParty=' + matchingParty;
											param += '&productType=' + productType;
											param += '&postingDateFrom=' + postingDateFrom;
											param += '&postingDateTo=' + postingDateTo;
											param += '&extraFilter=' + extraFilter;
											openWindow(param,'movementsearchlistWindow','left=50,top=190,width=1280,height=685,toolbar=0, resizable=yes, scrollbars=yes,status=yes');
											setParentChildsFocus();
											return  param;
										}
								
									}else{
										document.forms[0].elements['movementsearch.matchingParty'].focus();
									} 
								}else{
									document.forms[0].elements['movementsearch.productType'].focus();
								} 
								
							}else{
								document.forms[0].elements['referencemap.includeRef'].focus();
							} 
						}else{
							document.forms[0].elements['movementsearch.timeto'].focus();
						}
					}else{
						document.forms[0].elements['movementsearch.timefrom'].focus();
					}
			}else{

				alert('<s:text name="movSearch.alert.amountunder"/>') ;
			}
		}else{
			document.forms[0].elements['movementsearch.amountunderasstring'].focus();
		}
		
	  }else{
		document.forms[0].elements['movementsearch.amountoverasstring'].focus();

	}

	}

}
function noContinue() {
	document.forms[0].elements['referencemap.includeRef'].focus();
		return;
	
}
function yesContinue() {
	var prodType=validateField(document.forms[0].elements['movementsearch.productType'],'movementsearch.productType','alphaNumPatWithoutPercentage');
	if(prodType){								
		var matchParty=validateField(document.forms[0].elements['movementsearch.matchingParty'],'movementsearch.matchingParty','alphaNumPatWithoutPercentage');
		if(matchParty){
			elementTrim(document.forms[0]);
			var movementType = getRadioButtonValue(document.forms[0].elements['movementsearch.movementType']);
			var sign = getRadioButtonValue(document.forms[0].elements['movementsearch.sign']);
			var predictStatus = getRadioButtonValue(document.forms[0].elements['movementsearch.predictStatus']);
			var amountover = document.forms[0].elements['movementsearch.amountoverasstring'].value ;
			var amountunder = document.forms[0].elements['movementsearch.amountunderasstring'].value;
			var extBalStatus = getRadioButtonValue(document.forms[0].elements['movementsearch.extBalStatus']);								
			var currencyCode = document.forms[0].elements['movementsearch.currencyCode'].value
			var beneficiaryId = document.forms[0].elements["movementsearch.beneficiaryId"].value;
			var custodianId = document.forms[0].elements["movementsearch.custodianId"].value;
			var positionlevel = document.forms[0].elements["movementsearch.positionLevelAsString"].value;
			var accountId = document.forms[0].elements["movementsearch.accountId"].value ;
			var group = document.forms[0].elements["movementsearch.group"].value ;
			var metaGroup = document.forms[0].elements["movementsearch.metaGroup"].value ;
			var bookCode = document.forms[0].elements["movementsearch.bookCode"].value ;
			var valueFromDateAsString = document.forms[0].elements['movementsearch.valueFromDateAsString'].value;
			var valueToDateAsString = document.forms[0].elements['movementsearch.valueToDateAsString'].value;
			var timefrom = document.forms[0].elements['movementsearch.timefrom'].value; 
			var timeto = document.forms[0].elements['movementsearch.timeto'].value; 
			var messageId = document.forms[0].elements['movementsearch.messageId'].value;
			var inputDateAsString = document.forms[0].elements['movementsearch.inputDateAsString'].value;
			var counterPartyId = document.forms[0].elements['movementsearch.counterPartyId'].value;
			var matchStatus = getRadioButtonValue(document.forms[0].elements['movementsearch.matchStatus']);
			var accountClass = getRadioButtonValue(document.forms[0].elements['movementsearch.accountClass']);
			var openFlag=getRadioButtonValue(document.forms[0].elements["movementsearch.openFlag"]);
			var uetr = document.forms[0].elements['movementsearch.uetr'].value;
			var matchingParty = document.forms[0].elements['movementsearch.matchingParty'].value;
			var productType = document.forms[0].elements['movementsearch.productType'].value;
			var postingDateFrom = document.forms[0].elements['movementsearch.postingFromDateAsString'].value;
			var postingDateTo = document.forms[0].elements['movementsearch.postingToDateAsString'].value;
			var extraFilter= extraFilter;
			 if (inputDateAsString != "") {
				if(validateDateField(document.forms[0].elements['movementsearch.inputDateAsString'],'movementsearch.inputdate',dateFormat)){
				} else {
					document.forms[0].elements['movementsearch.inputDateAsString'].focus();
					return false;										
				}
			 }
			 
			var comparePostingDate = 0;
			
			if (postingDateFrom == "" && postingDateTo == ""){	
				comparePostingDate = 1;
			}
			else{
				if(validateDateField(document.forms[0].elements['movementsearch.postingFromDateAsString'],'fromDateAsString',dateFormat)){
					if(document.forms[0].elements['movementsearch.postingToDateAsString'].value != ""){
						if (validateDateField(document.forms[0].elements['movementsearch.postingToDateAsString'],'toDateAsString',dateFormat)){
							comparePostingDate = comparedates(document.forms[0].elements['movementsearch.postingFromDateAsString'].value,document.forms[0].elements['movementsearch.postingToDateAsString'].value,dateFormat,'From Date','To Date');													
						}
					}else if(document.forms[0].elements['movementsearch.postingToDateAsString'].value == "")
					{
						comparePostingDate =1;
					}										
				}
			}
			
			if (comparePostingDate == 1) {
			
				var userid="<%=SwtUtil.getCurrentUserId(request.getSession())%>";
				if (currencyCode=="All")
					currencyCode="All:"+userid;
				
				document.forms[0].method.value = globalMethodName;
				var param = 'outstandingmovement.do?method=flex';
				param += '&entityId=' + document.forms[0].elements['movementsearch.id.entityId'].value;
				param += '&movementType=' + movementType ;
				param += '&sign=' + sign ;
				param += '&predictStatus=' + predictStatus ;
				
				param += '&extBalStatus=' + extBalStatus ;									
				param += '&amountover=' + amountover ;
				param += '&amountunder=' + amountunder;
				param += '&currencyCode=' + currencyCode  ;
				param += '&beneficiaryId=' + beneficiaryId ;
				param += '&custodianId=' + custodianId ;
				param += '&positionlevel=' + positionlevel ;
				param += '&accountId=' + accountId ;
				param += '&group=' + group  ;
				param += '&metaGroup=' + metaGroup ;
				param += '&bookCode=' + bookCode ;
				param += '&valueFromDateAsString=' + valueFromDateAsString ;
				param += '&valueToDateAsString=' + valueToDateAsString
				 param += '&timefrom=' + timefrom  ; 
				 param += '&timeto=' + timeto ;  
				param += '&reference=' + getMenuWindow().encode64(getxmlRef());
				param += '&messageId=' + messageId.replace(/&/g,"amp;").replace(/\+/g,"plus;");
				param += '&inputDateAsString=' + inputDateAsString ;
				param += '&counterPartyId=' + counterPartyId ;
				param += '&matchStatus=' + matchStatus  ;
				param += '&initialinputscreen=' + initialscreen  ;
				param += '&accountClass=' + accountClass  ;
				
				param += '&isAmountDiffer=' + '${requestScope.isAmountDiffer}' ;
				param += '&selectedMovementsAmount=' + '${requestScope.selectedMovementsAmount}' ;
				param += '&openFlag=' + openFlag ;								
				param += '&uetr=' + uetr;
				param += '&matchingParty=' + matchingParty;
				param += '&productType=' + productType;
				param += '&postingDateFrom=' + postingDateFrom;
				param += '&postingDateTo=' + postingDateTo;
                param += '&extraFilter=' + extraFilter;
				openWindow(param,'movementsearchlistWindow','left=50,top=190,width=1280,height=685,toolbar=0, resizable=yes, scrollbars=yes,status=yes');
				setParentChildsFocus();
				return  param;
			}
	
		}else{
			document.forms[0].elements['movementsearch.matchingParty'].focus();
		} 
	}
}

function validateFormAmount(objForm){
  var elementsRef = new Array(2);

 
  elementsRef[0] = objForm.elements["movementsearch.amountoverasstring"];
  elementsRef[1] = objForm.elements["movementsearch.amountunderasstring"];

  if(elementsRef[0].value != "" && elementsRef[1].value != "")
{
  var amountover=parseFloat(elementsRef[0].value);
  var amountunder=parseFloat(elementsRef[1].value);
 
  if(amountover > amountunder)
	{
	  alert('<s:text name="movSearch.alert.amountoverLess"/>');
	  return false;
	}
	else
    return true;
}
else
	return true;
}



function checkDate()
{

var enteredDate = document.forms[0].elements['movementsearch.valueFromDateAsString'].value

if(enteredDate!=""){

if(compareTwoDates(sysDateAsstring,enteredDate,dateFormat)== true)
		{
		getMovementDetails('search');
		}
	else 
		{
		alert('<s:text name="movSearch.alert.valueFrom"/>');
		return false;
		}
	}
else{
	document.forms[0].elements['movementsearch.valueFromDateAsString'].value = sysDateAsstring ;
	getMovementDetails('search')
}

}

/**
* This method is used to call when load the file, set the default values.
* 
* @return none
*/
function bodyOnLoad()
{
extraFilter= "${requestScope.extraFilter}";
var currencyDropBoxElement = new SwSelectBox(document.forms[0].elements["movementsearch.currencyCode"],document.getElementById("currencyName"));

var entityDropBoxElement = new SwSelectBox(document.forms[0].elements["movementsearch.id.entityId"],document.getElementById("entityName"));


var dropBox1 = new SwSelectBox(document.forms[0].elements["movementsearch.positionLevelAsString"],document.getElementById("positionName"));
	

var divElement = document.getElementById("dropdowndiv_5");
	var selectElement = document.forms[0].elements["movementsearch.bookCode"];
	var idElement = document.forms[0].elements["bookId"];
	var descElement = document.forms[0].elements["bookCodeName"];
	var arrowElement = document.forms[0].elements["dropdownbutton_5"];
	var idLength = 12;
	var descLength = 30;

	var bookCodeDropBoxElement = new SwMainSelectBox(divElement,selectElement,idElement,descElement,arrowElement,idLength,descLength);

var divElement = document.getElementById("dropdowndiv_6");
	var selectElement = document.forms[0].elements["movementsearch.group"];
	var idElement = document.forms[0].elements["groupId"];
	var descElement = document.forms[0].elements["groupName"];
	var arrowElement = document.forms[0].elements["dropdownbutton_6"];
	var idLength = 12;
	var descLength = 50;

	var groupDropBoxElement = new SwMainSelectBox(divElement,selectElement,idElement,descElement,arrowElement,idLength,descLength,"true");

var divElement = document.getElementById("dropdowndiv_7");
	var selectElement = document.forms[0].elements["movementsearch.metaGroup"];
	var idElement = document.forms[0].elements["metagroupId"];
	var descElement = document.forms[0].elements["metaGroupName"];
	var arrowElement = document.forms[0].elements["dropdownbutton_7"];
	var idLength = 12;
	var descLength = 50;
	var metaGroupDropBoxElement = new SwMainSelectBox(divElement,selectElement,idElement,descElement,arrowElement,idLength,descLength,"true");

var divElement = document.getElementById("dropdowndiv_8");
	var selectElement = document.forms[0].elements["movementsearch.accountId"];
	var idElement = document.forms[0].elements["accountId"];
	var descElement = document.forms[0].elements["Acctname"];
	var arrowElement = document.forms[0].elements["dropdownbutton_8"];
	var idLength = 12;
	var descLength = 30;
	var accountDropBoxElement = new SwMainSelectBox(divElement,selectElement,idElement,descElement,arrowElement,idLength,descLength);
	document.getElementById("metaGroupName").value=document.getElementById("metaGroupName").value.replace(new RegExp("(&nbsp;)","g")," ");
    document.getElementById("groupName").value=document.getElementById("groupName").value.replace(new RegExp("(&nbsp;)","g")," ");
document.forms[0].initialinputscreen.value = initialscreen ;

if(initialscreen == "matchSearch"){
	<s:if test='"searchAccFromMatch"!=#request.methodName' > 
		document.forms[0].elements["movementsearch.sign"][2].checked = true;
		document.forms[0].elements["movementsearch.predictStatus"][3].checked = true;
		document.forms[0].elements["movementsearch.movementType"][2].checked = true;
		document.forms[0].elements["movementsearch.matchStatus"][2].checked = true; 
		document.forms[0].elements["movementsearch.accountClass"][5].checked = true;
	</s:if>
		<s:if test='"searchAccFromMatch"==#request.methodName' > 
			document.forms[0].elements["movementsearch.matchStatus"][3].checked = true;
	     </s:if>
			

}
else
	{
	<s:if test='"populateAcc"!=#request.methodName' > 
		document.forms[0].elements["movementsearch.sign"][2].checked = true;
		document.forms[0].elements["movementsearch.predictStatus"][3].checked = true;
		
		document.forms[0].elements['movementsearch.extBalStatus'][2].checked = true;
		document.forms[0].elements["movementsearch.movementType"][2].checked = true;
		document.forms[0].elements["movementsearch.openFlag"][1].checked = true;
		document.forms[0].elements["movementsearch.matchStatus"][8].checked = true;
		 document.forms[0].elements["movementsearch.accountClass"][5].checked = true; 
   </s:if>
	}
	
	var selectedEntityId= document.forms[0].elements['movementsearch.id.entityId'].value;
	if(selectedEntityId=="All"){
		//disable the party search and only allow the user to enter free text for the party field
		document.getElementById('partyLink1').disabled= "true";
		document.getElementById('partyLink2').disabled= "true";
		document.getElementById('partyLink3').disabled= "true";
		
		//disable the Book, Group, Metagroup, and Account fields
		document.forms[0].elements["bookId"].disabled= "true";
		document.forms[0].elements["groupId"].disabled= "true";
		document.forms[0].elements["metagroupId"].disabled= "true";
		document.forms[0].elements["accountId"].disabled= "true";
		document.getElementById('dropdownbutton_5').disabled= "true";
		document.getElementById('dropdownbutton_6').disabled= "true";
		document.getElementById('dropdownbutton_7').disabled= "true";
		document.getElementById('dropdownbutton_8').disabled= "true";
	}
 

}


function populateDropBoxes()
{
	ShowErrMsgWindow('${actionError}');	
	bodyOnLoad();	
}

function comparedates(date1,date2,format,date1Caption,date2Caption)
{
	var retValue = "true";
	var strdate1 = new String(date1);
	var strdate2 = new String(date2);
	
	if( typeof strdate1 != 'undefined' && strdate1 != null && typeof strdate2 != 'undefined' && strdate2 != null)
	{
		strdate1 = strdate1.trim();
		strdate2 = strdate2.trim();
		if(strdate1.length > 0 && strdate2.length > 0)
		{
			if(format == "datePat1")
			{
				var parts = date1.split("/");
				date1 = new Date(0);
				date1.setFullYear(parts[2]);
				date1.setDate(parts[0]);
				date1.setMonth(parts[1] - 1);			

				var parts = date2.split("/");
				date2 = new Date(0);
				date2.setFullYear(parts[2]);
				date2.setDate(parts[0]);
				date2.setMonth(parts[1] - 1);			

				if(date2 < date1)
					retValue = "false";
			}
			if(format == "datePat2")
			{
				var parts = date1.split("/");

				date1 = new Date(0);
				date1.setFullYear(parts[2]);
				date1.setDate(parts[1]);
				date1.setMonth(parts[0] - 1);			

				var parts = date2.split("/");
				date2 = new Date(0);
				date2.setFullYear(parts[2]);
				date2.setDate(parts[1]);
				date2.setMonth(parts[0] - 1);			

				if(date2 < date1)
					retValue = "false";

			}
		}

	}
	if(retValue =="false"){
	alert('' + date2Caption + " " + '<s:text name="movSearch.alert.dateComparison"/>' + " " + date1Caption+'');
	 return 2;
	 }else{
	 return 1;
	 }
}
function validateToDateField(){
	if(document.forms[0].elements['movementsearch.valueToDateAsString']!=null && document.forms[0].elements['movementsearch.valueToDateAsString'].value != ""){
		if(validateDateField(document.forms[0].elements['movementsearch.valueToDateAsString'],'movementsearch.valueto',dateFormat )){
		}
	}	
}

function validateFromDateField(){
	if(document.forms[0].elements['movementsearch.valueFromDateAsString']!=null && document.forms[0].elements['movementsearch.valueFromDateAsString'].value != ""){
		if(validateDateField(document.forms[0].elements['movementsearch.valueFromDateAsString'],'movementsearch.valueFrom',dateFormat )){
		}
	}
}


function party(flag,elementId,elementName){
var entityId = document.forms[0].elements['movementsearch.id.entityId'].value;
var url='party.do?method=preSearchParties&entityId='+entityId;
url += '&custodianFlag='+flag;
url += '&idElementName='+elementId;
url += '&descElementName='+elementName;
openWindow(url,'SearchParty','left=50,top=190,width=509,height=579,toolbar=0,resizable=yes,scrollbars=yes','true');
}

function clearCounterPartyDesc()
{
 document.getElementById("partyName").innerText = "";

}

function clearBeneficiaryDesc()
{
 document.getElementById("partyName1").innerText = "";

}
function clearCustodianDesc()
{
 document.getElementById("partyName2").innerText = "";

}

//  This function is called when we have 3 columns in drop down and on selection only first two columns to be displayed
function populateValues_Jsp(mainobject)
{
	var selectedIndex = mainobject.selectElement.selectedIndex;
	if(selectedIndex < 0 ) selectedIndex = 0;

	var selectedOption = mainobject.selectElement.options[selectedIndex];
	var showText = selectedOption.text ;
	showText = showText.substring(12,42);
	mainobject.idElement.value = selectedOption.value;
	
	mainobject.descElement.value = showText ;

	return mainobject ;
}
function display(methodName) {
	enableFields();
	document.forms[0].method.value = methodName;
	document.forms[0].extraFilter.value = extraFilter;
	document.forms[0].submit();
}

function validateUETR() {
	  var pattern = /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-(8|9|a|b)[0-9a-f]{3}-[0-9a-f]{12}$/;
	  if (pattern.test(document.getElementById("movementsearch.uetr").value)) {
	    return true ;
	  }
	  else 
	  {
		  alert("Invalid UETR format!");
		  return false;
	  }
}
</SCRIPT>

<SCRIPT language="JAVASCRIPT">
  var dateFormatValue = '<s:property value="#request.session.CDM.dateFormatValue" />';
	var cal = new CalendarPopup("caldiv",false,"calFrame"); 
    cal.setCssPrefix("CAL");
	cal.offsetX = 18;
    cal.offsetY = 0;
	
	var cal2 = new CalendarPopup("caldiv",false,"calFrame"); 
    cal2.setCssPrefix("CAL");
    cal2.offsetX = 18;
    cal2.offsetY = 0;

	var cal3 = new CalendarPopup("caldiv",false,"calFrame"); 
    cal3.setCssPrefix("CAL");
    cal3.offsetX = 18;
    cal3.offsetY = 0;
	var cal4 = new CalendarPopup("caldiv",false,"calFrame"); 
    cal4.setCssPrefix("CAL");
    cal4.offsetX = 20;
    cal4.offsetY = -76;
	
	var cal5 = new CalendarPopup("caldiv",false,"calFrame"); 
    cal5.setCssPrefix("CAL");
    cal5.offsetX = 20;
    cal5.offsetY = -76;
	
	
function validateInputDateField(){
	if(document.forms[0].elements['movementsearch.inputDateAsString']!=null && document.forms[0].elements['movementsearch.inputDateAsString'].value != ""){
		if(validateDateField(document.forms[0].elements['movementsearch.inputDateAsString'],'movementsearch.inputdate',dateFormat )){
		}
	}
}

function validateForm(objForm){
  var elementsRef = new Array(1);
  elementsRef[0] = objForm.elements["movementsearch.valueFromDateAsString"];

  return validate(elementsRef);
}

function changeBorderColor() {
    
	     var elementDateValue=document.forms[0].elements['movementsearch.valueFromDateAsString'];
	     if(elementDateValue!=null&&typeof elementDateValue!='undefined')
	    	 elementDateValue.style.border = elementDateValue.value==""?"red 1px solid":"";
    }
    
function openAddColsScreen(methodName){
	var param = '/' + appName + '/outstandingmovement.do?method='+methodName;
	var mainWindow = openWindow (param, 'addColumns','left=10,top=230,width=710,height=500,toolbar=0, resizable=yes, //status=yes, scrollbars=yes');	
	return false;
}
    
//Return a string that contains a full reference chosen by user (includes and excludes)
// It will be like <refparams><include ref1="N" ref2="Y" ref3="Y" ref4="Y" like="Y">ABCD</include><exclude ref1="Y" ref2="Y" ref3="Y" ref4="Y" like="Y">EFGH</exclude></refparams>
function getxmlRef() {
	var includeArray = [includeRef1Flag, includeRef2Flag, includeRef3Flag, includeRef4Flag];
	var excludeArray = [excludeRef1Flag, excludeRef2Flag, excludeRef3Flag, excludeRef4Flag];

	var xmlDocument = "<refparams>";
	var includeNode = "<include ";
	var excludeNode = "<exclude ";
	for (var i=0;i<4;i++) {
	 	includeNode +=  "ref" + (i+1) + "=\"" + includeArray[i] + "\" ";
	    excludeNode +=  "ref" + (i+1) + "=\"" + excludeArray[i] + "\" ";
	}
	if (includeRefFlag == 'Y') {
		includeNode += "like=\"Y\" ><![CDATA[" + includeRef + "]]></include>";
	} else {
		includeNode += "like=\"N\" ><![CDATA[" + includeRef + "]]></include>";
	}
	if (excludeRefFlag == 'Y') {
		excludeNode += "like=\"Y\" ><![CDATA[" + excludeRef + "]]></exclude>";
	} else {
		excludeNode += "like=\"N\" ><![CDATA[" + excludeRef + "]]></exclude>";
	}
	xmlDocument += includeNode + excludeNode;
	return (xmlDocument + "</refparams>");
}
</SCRIPT>

</head>

	<body leftmargin="0" topmargin="0" marginheight="0" onLoad="setParentChildsFocus();setFocus(document.forms[0]);populateDropBoxes();" onUnload="call()">



<s:form action="movementsearch.do">
<input name="method" type="hidden" value="display">
<input name="selectedEntityName" type="hidden" value="">
<input name="initialinputscreen" type="hidden" value="">

<input name="isAmountDiffer" type="hidden" value="">
<input name="selectedMovementsAmount" type="hidden" value="">
<input name="extraFilter" type="hidden" value="">

<bean:define id="CDM" name="CDM" type="org.swallow.util.CommonDataManager" scope="session"/>


<!----------------------------------caldiv----------------------------------------------------------------->
<div id="caldiv" style="z-index:100;position:absolute;visibility:visible;background-color:white;layer-background-color:white;"></div>
	<iframe id="calFrame" src="javascript:false;" scrolling="no" frameborder="0" style="position:absolute; top:0px; left:0px; display:none;">
	</iframe>
<!-----------------------------------end of caldiv----------------------------------------------->



<div id="dropdowndiv_5" style="z-index:150;position:absolute;width:200px;left:367px;top:303px;visibility:hidden" class="swdropdown">
			<s:select cssClass="htmlTextFixed" id="movementsearch.bookCode" name="movementsearch.bookCode" size="10" cssStyle="width:329px;z-index:99;" list="#request.bookdetails" listKey="value" listValue="label" />
</div>
<div id="dropdowndiv_6" style="z-index:150;position:absolute;width:200px;left:367px;top:330px;visibility:hidden" class="swdropdown">
			<s:select cssClass="htmlTextFixed" id="movementsearch.group" name="movementsearch.group" size="10" cssStyle="width:473px;z-index:99;" list="#request.groupdetails" listKey="value" listValue="label" />
</div>
<div id="dropdowndiv_7" style="z-index:150;position:absolute;width:200px;left:367px;top:359px;visibility:hidden" class="swdropdown">
			<s:select cssClass="htmlTextFixed" id="movementsearch.metaGroup" name="movementsearch.metaGroup" size="10" cssStyle="width:473px;z-index:99;" list="#request.metagroupdetails" listKey="value" listValue="label" />
</div> 
<div id="dropdowndiv_8" style="z-index:150;position:absolute;width:200px;left:367px;top:417px;visibility:hidden" class="swdropdown">
			<s:select cssClass="htmlTextFixed" id="movementsearch.accountId" name="movementsearch.accountId" size="10" cssStyle="width:329px;z-index:99;" list="#request.accountdetails" listKey="value" listValue="label" />
</div> 

<!--------------entity drop down---------------->
<div id="MovementSearch" style="position:absolute; left:10px; top:10px; width:941px; height:37px; border:2px outset;" color="#7E97AF">
<div id="MovementSearch" style="position:absolute; left:-5px; top:4px; width:780px; height:300;">
<table width="750" border="0" cellpadding="0" cellspacing="0" height="25">
	<tr>
	  <td width="47"><B>&nbsp;&nbsp;&nbsp;<s:text name="movementsearch.entity"/></B></td>
	  <td width="28">&nbsp;</td>
	  <td width="140">
	 <s:if test='"matchSearch"!=#request.initialinputscreen' >
	 <s:select titleKey="tooltip.selectEntityId" cssClass="htmlTextAlpha" tabindex="1" id="movementsearch.id.entityId" name="movementsearch.id.entityId" onchange="submitForm('populate')" cssStyle="width:140px" list="#request.entities" listKey="value" listValue="label" />
	 </s:if>
	 <s:if test='"matchSearch"==#request.initialinputscreen' >
	 <s:select cssClass="htmlTextAlpha" titleKey="holiday.entityId" tabindex="1" id="movementsearch.id.entityId" name="movementsearch.id.entityId" onchange="submitForm('populate')" cssStyle="width:140px" disabled="true" list="#request.entities" listKey="value" listValue="label" />
	  </s:if>
	  </td>
	   <td width="20">&nbsp;</td>
	  <td width="490">
		 <span id="entityName" class="spantext">
	   </td>
	 </tr>
</table>
</div>
</div>

<!----------------end of entity drop down------------------>

<div id="movementsearchparam" style="position:absolute; left:10px; top:54px; width:941px; height:575px; border:2px outset;" color="#7E97AF">
	<div id="movementsearchparam" style="position:absolute; left:0px; top:0px; width:591px; height:38px;">
		<!--------------------start of extra fieldset------------------------------------------>
		<div style="position:absolute;height:555; z-index:99;left:187px; top:10px;">
			<fieldset style="width:625px;border:2px groove;height:530px;">
				<div style="position:absolute; z-index:99;left:8px; top:5px;width: 625px">
					<table width="569px" border="0" cellpadding="0" cellspacing="1" height="">  
						<!------value date---->
						<tr height="25px">						
						<td width="120px">
						<B><s:text name="movementsearch.amountover"/></B>
						</td>
						<td width="28px">&nbsp;</td>
						<td width="160px">

							<s:textfield tabindex="4"  name="movementsearch.amountoverasstring" style="width:160px; height: 22px;" titleKey="tooltip.amountFrom"
							onchange="validateCurrency(this,'movementsearch.amountoverasstring',currencyFormat, document.forms[0].elements['movementsearch.currencyCode'].value)"  cssClass="htmlTextNumeric" maxlength="28" />

						</td>
				
					
						<td width="20px">&nbsp;</td>
						<td width="45px">
							<B><s:text name="movementsearch.valueto"/></B>
						</td>
						<td width="28px">&nbsp;</td>
						<td width="160px" height="28">
							<s:textfield titleKey="tooltip.amountTo" tabindex="4" cssClass="htmlTextNumeric" name="movementsearch.amountunderasstring" style="width:160px; height: 22px;" onchange="return validateCurrency(this,'movementsearch.amountunder',currencyFormat, document.forms[0].elements['movementsearch.currencyCode'].value)" maxlength="28" />
						</td>
						</tr>
						</table>

						<table width="600px" border="0" cellpadding="0" cellspacing="1" height="80">


						<tr height="25">
							<td width="126px">
							<B><s:text name="movementsearch.valuefrom"/>*</B>
							</td>
							<td width="30px">&nbsp;</td>
							<td width="160px" id="movementsearchValuefrom">
								<s:if test='"matchSearch"!=#request.initialinputscreen' >
									<s:textfield titleKey="tooltip.enterValueDateFrom" tabindex="5" cssClass="htmlTextAlpha" name="movementsearch.valueFromDateAsString"  style="width:80px;margin-bottom: 5px; height: 22px;" maxlength="10" onchange="if(validateForm(document.forms[0]) ){validateFromDateField();changeBorderColor();}" onmouseout="dateSelected=false" />
										<A  title='<s:text name="tooltip.selectFromDate"/>' tabindex="6" name="datelink" ID="datelink" onClick="cal.select(document.forms[0].elements['movementsearch.valueFromDateAsString'],'datelink',dateFormatValue);dateSelected=true;return false;"><img src="images/calendar-16.gif"></A>
								</s:if>
								<s:if test='"matchSearch"==#request.initialinputscreen' >
									<s:textfield titleKey="tooltip.enterValueDateFrom" tabindex="5" cssClass="htmlTextAlpha" name="movementsearch.valueFromDateAsString"  style="width:80px;margin-bottom: 5px; height: 22px;" maxlength="10" onchange="if(validateForm(document.forms[0]) ){validateFromDateField();}" onmouseout="dateSelected=false" />
										<A  title='<s:text name="tooltip.selectFromDate"/>'  tabindex="6" name="datelink" ID="datelink" onClick="cal.select(document.forms[0].elements['movementsearch.valueFromDateAsString'],'datelink',dateFormatValue);dateSelected=true;return false;"><img src="images/calendar-16.gif"></A>
								</s:if>
							</td>
							<td width="20px">&nbsp;</td>
							<td width="44px" style="padding-left: 1px;"><B><s:text name="movementsearch.valueto"/></B></td>
							<td width="225px">
								<s:if test='"matchSearch"!=#request.initialinputscreen' >
									<s:textfield titleKey="tooltip.enterValueDateTo" tabindex="7" cssClass="htmlTextAlpha" name="movementsearch.valueToDateAsString" style="width:80px;margin-bottom: 5px; height: 22px;margin-left: 30px" maxlength="10" onchange="validateToDateField();" onmouseout="dateSelected=false" />
											<A  title='<s:text name="tooltip.selectToDate"/>'   tabindex="8" name="datelink2" ID="datelink2" onClick="cal2.select(document.forms[0].elements['movementsearch.valueToDateAsString'],'datelink2',dateFormatValue);dateSelected=true;return false;"><img src="images/calendar-16.gif"></A>
								</s:if>
								<s:if test='"matchSearch"==#request.initialinputscreen' >
									<s:textfield titleKey="tooltip.enterValueDateTo" tabindex="7" cssClass="htmlTextAlpha" name="movementsearch.valueToDateAsString" style="width:80px;margin-bottom: 5px; height: 22px;margin-left: 30px" maxlength="10" onchange="validateToDateField();" onmouseout="dateSelected=false" />
											<A  title='<s:text name="tooltip.selectToDate"/>'   tabindex="8" name="datelink2" ID="datelink2" onClick="cal2.select(document.forms[0].elements['movementsearch.valueToDateAsString'],'datelink2',dateFormatValue);dateSelected=true;return false;"><img src="images/calendar-16.gif"></A>
								</s:if>
							</td>
						</tr>

						<!-------------currency------>
						<tr height="25">
							<td width="120px">
								<B><s:text name="movementsearch.currency"/></B>
							</td>
							<td width="8px">&nbsp;</td>
							<td width="140px">
								<s:if test='"matchSearch"!=#request.initialinputscreen' >
								<!-- Code Modified For Mantis 2072 by Sudhakar on 31-10-2012:Currency change should not change Status and Account class to All,The parameter of display method is changed as populateAcc instead of populate  -->
									<s:select titleKey="tooltip.movCurrency" cssClass="htmlTextAlpha" tabindex="9" id="movementsearch.currencyCode" name="movementsearch.currencyCode" cssStyle="width:56px" onchange="display('populateAcc');" list="#request.currencydetails" listKey="value" listValue="label" />
								</s:if>
								<s:if test='"matchSearch"==#request.initialinputscreen' >
									<s:select titleKey="tooltip.movCurrency" tabindex="9" cssClass="htmlTextAlpha" id="movementsearch.currencyCode" name="movementsearch.currencyCode" cssStyle="width:56px" disabled="true" list="#request.currencydetails" listKey="value" listValue="label" />
								</s:if>
							</td>
							<td width="8px">&nbsp;</td>
							<td width="280px" colspan="4" style="padding-left: 1px;">
								<span id="currencyName" class="spantext">
							</td>
						</tr>
						<!-----Swift message type------->
						<tr height="25">
							<td width="120px">
								<B><s:text name="movementsearch.messageId"/></B></td>
							<td width="8px">&nbsp;</td>
							<td width="140px">
							
							<s:select titleKey="tooltip.enterMsgType" cssClass="htmlTextAlpha" tabindex="10" id="movementsearch.messageId" name="movementsearch.messageId" cssStyle="width:140px;" list="#request.swiftMessages" listKey="value" listValue="label" />
							</td>

							<td width="8px">&nbsp;</td>
							<td width="280px" colspan="4">&nbsp;</td>
						</tr>
					
						<div style="z-index:99;position:absolute; left:0px; top:0px;">
							<table width="620px" border="0" cellpadding="0" cellspacing="1" height="170">  
								<tr height="25">
									<td width="123px"><B><s:text name="movementsearch.counterparty"/></B></td>
									<td width="30px">&nbsp;</td>
									<td width="150px" style="padding-left: 10px;">
									    <s:textfield cssClass="htmlTextAlpha" name="movementsearch.counterPartyId" onchange = "clearCounterPartyDesc()" tabindex="12" style="width:120px; margin-bottom: 2px;margin-right: 5px; height: 22px;" titleKey="tooltip.counterId" /><input title='<s:text name="tooltip.clickSelCounterId"/>' id="partyLink1" type="button" value="..." tabindex="13" onClick="javascript:party('N','movementsearch.counterPartyId','partyName')">
									 </td>
									 <td width="47px">&nbsp;</td>
									 <td width = "280px">
									     <span id="partyName" name="partyName" class="spantext">						
									</td> 
								</tr>
								<tr height="25">
									<td width="123px">
										<B><s:text name="movementsearch.beneficiary"/></B>
									</td>
									<td width="30px">&nbsp;</td>
									<td width="155px" style="padding-left: 10px;">
									 <s:textfield cssClass="htmlTextAlpha" name="movementsearch.beneficiaryId" onchange = "clearBeneficiaryDesc()" tabindex="14" style="width:120px; margin-bottom: 2px;margin-right: 5px; height: 22px;" titleKey="tooltip.benID" /><input title='<s:text name="tooltip.movBeneficiary"/>' id="partyLink2" type="button" value="..." tabindex="15" onClick="javascript:party('N','movementsearch.beneficiaryId','partyName1')">
								    </td>
									<td width="47px">&nbsp;</td>
									<td width = "280px">
									   <span id="partyName1" name="partyName1" class="spantext">						
									</td> 
									
								</tr>
								<tr height="25">
									<td width="123px"><B><s:text name="movementsearch.custodian"/></B></td>
									<td width="30px">&nbsp;</td>
		
									<td width="150px" style="padding-left: 10px;">
									 <s:textfield cssClass="htmlTextAlpha" name="movementsearch.custodianId" tabindex="16" onchange = "clearCustodianDesc()" style="width:120px; margin-bottom: 2px;margin-right: 5px; height: 22px;" titleKey="tooltip.custId" /><input title='<s:text name="tooltip.movCustodian"/>'id="partyLink3" type="button" value="..." tabindex="17" onClick="javascript:party('Y','movementsearch.custodianId','partyName2')">
									</td>
		
									<td width="47px">&nbsp;</td>
									<td width = "280px">
									  <span id="partyName2" name="partyName2" class="spantext">						
									</td>
								</tr>
								<tr height="25">
									<td width="123px">
										<B><s:text name="movementsearch.bookcode"/></B></td>
									<td width="28px">&nbsp;</td>
									<td width="150px" style="padding-left: 10px;">							
										<input styleclass="textAlpha" name="bookId"  style="width:120px; margin-bottom: 2px;margin-right: 5px; height: 22px;"  title='<s:text name="tooltip.bookId"/>' readonly="readonly"/><input title='<s:text name="tooltip.clickSelBookId"/>' id="dropdownbutton_5" tabindex="18" type="button" value="..." >
									</td>
									<td width="7px">&nbsp;</td>
									<td>
									    <input styleclass="textAlpha" style="width:280px;background:transparent; border: thin;" name="bookCodeName" size="20" tabindex="-1" readonly = "true">
									</td>
								</tr>
								<tr height="25">
									<td width="123px"><B><s:text name="movementsearch.group"/></B></td>
									<td width="28px">&nbsp;</td>
									<td width="150px" style="padding-left: 10px;">
									   <input styleclass="textAlpha" name="groupId"  style="width:120px; margin-bottom: 2px;margin-right: 5px; height: 22px;"  title='<s:text name="tooltip.groupId"/>' readonly="readonly"><input title='<s:text name="tooltip.clickSelGroupId"/>' id="dropdownbutton_6"  tabindex="19" type="button" value="..." >
									</td>
									<td width="7px">&nbsp;</td>
									<td>
										<input styleclass="textAlpha" style="width:280px;background:transparent; border: thin;" name="groupName" size="20" tabindex="-1" readonly = "true">
									</td>
								</tr>
								<tr height="25">
									<td width="123px"><B><s:text name="movementsearch.metagroup"/></B></td>
									<td width="28px">&nbsp;</td>
									<td width="150px" style="padding-left: 10px;">
									   <input styleclass="textAlpha" name="metagroupId" style="width:120px; margin-bottom: 2px;margin-right: 5px; height: 22px;"  title='<s:text name="metaGroup.id.mgroupId1"/>' readonly="readonly"><input title='<s:text name="tooltip.clickSelMetaGroupId"/>' id="dropdownbutton_7" type="button" tabindex="20" value="..." ></td>
									<td width="17px">&nbsp;</td>
									<td>
										<input styleclass="textAlpha" style="width:280px;height:19px;background:transparent; border: thin;" name="metaGroupName" size="20" tabindex="-1" readonly = "true">  
									</td>
								</tr>
							</table>
							<table width="620px" cellspacing="0" cellpadding="0" border="0">
								<tr height="25">
									<td width="123px">
										<B><s:text name="movementsearch.inputdate"/></B></td>
									<td width="100px">&nbsp;</td>
									<td width="160px">
										<s:textfield name="movementsearch.inputDateAsString" readonly="false" cssClass="htmlTextAlpha" titleKey="tooltip.enterInputDate" tabindex="21" style="width:80px; margin-bottom: 5px; height: 22px;" maxlength="10" onchange="return validateField(this,'movementsearch.inputdate',dateFormat );validateInputDateField();" onmouseout="dateSelected=false" />&nbsp;
											<A  title='<s:text name="tooltip.selectInputDate"/>'tabindex="22" name="datelink3" ID="datelink3" onClick="cal3.select(document.forms[0].elements['movementsearch.inputDateAsString'],'datelink3',dateFormatValue);dateSelected=true;return false;"><img src="images/calendar-16.gif"></A>
									</td>
									<td width="122px" align="right" style="padding-right: 2px;"><B><s:text name="movsearch.timefrom"/></B>
									</td>
									<td width="20px">&nbsp;</td>
									<td width="44px">
										 <s:textfield titleKey="tooltip.enterTomeFrom"  tabindex="23" name="movementsearch.timefrom" cssClass="htmlTextNumeric" style="width:44px; height: 22px;" onchange="return validateField(this,'movementsearch.timefrom','timePat')" />
									</td>
									<td width="20px">&nbsp;</td>
									<td width="35px" align="right"><B><s:text name="movementsearch.timeto"/></B></td>
									<td width="28px">&nbsp;</td>
									<td width="44px"> 
										<s:textfield titleKey="tooltip.enterTimeTo" tabindex="24" name="movementsearch.timeto" style="width:44px; height: 22px;" cssClass="htmlTextNumeric" onchange="return validateField(this,'movementsearch.timeto','timePat')" /></td>
									<td width="137px">&nbsp;</td>
								</tr>
							</table>
							<table width="615px" border="0" cellpadding="0" cellspacing="1" height="">  
		
								<tr height="25">
									<td width="145px"><B><s:text name="movementsearch.accountid"/></B></td>
									<td width="280px">
									 <input styleclass="textAlpha" name="accountId"  style="width: 220px; padding-left: 25px; margin-left: 22px; margin-right: 5px; height: 22px;" title='<s:text name="sweep.accountId"/>' readonly="readonly"><input  title='<s:text name="tooltip.clickSelAcId"/>' id="dropdownbutton_8" tabindex="25" type="button" value="..." ></td>
									   <td width="20px">&nbsp;</td>
									   <td><input styleclass="textAlpha" style="width:190px;background:transparent; border: thin;" name="Acctname" size="20" tabindex="-1" readonly = "true">  
								
									</td>
								
								</tr>
							</table>
							<table width="630px" border="0" cellpadding="0" cellspacing="1" height="">  
								<tr height="30px">
							<td width="191px"><B><s:text name="movementsearch.reference"/></B></td>
							<td width="3px">&nbsp;</td>
							<td width="102px" colspan="6">
							</td>
							<td width="5px">&nbsp;</td>
							<td width="40px"><B><s:text name="movementsearch.like"/></B></td>
							<td width="40px"><B><s:text name="movementsearch.ref1"/></B></td>
							<td width="40px"><B><s:text name="movementsearch.ref2"/></B></td>
							<td width="40px"><B><s:text name="movementsearch.ref3"/></B></td>
							<td width="40px"><B><s:text name="movementsearch.extra"/></B></td>
						</tr>
						<tr height="20px">
							<td width="191px" align="right"><B><s:text name="movementsearch.include"/>&nbsp;&nbsp;&nbsp;</B></td>
							<td width="3px">&nbsp;</td>
							<td width="102px" colspan="6">
								<s:textfield name="referencemap.includeRef" cssClass="htmlTextAlpha" titleKey="tooltip.enterRefIncl" tabindex="33" style="width:190px;" onchange="return validateField(this,'movementsearch.includeRef','alphaNumPatWithoutPercentage');" />  
							</td>
							<td width="5px">&nbsp;</td>
							<td  width="70px">
							<s:checkbox name="referencemap.includeRefFlag" fieldValue="Y" value='%{#request.referencemap.includeRefFlag == "Y"}'  cssStyle="width:13px;margin-left: 5px;" titleKey="tooltip.likeFlag" cssClass="htmlTextAlpha" tabindex="34"  />
							</td>
							<td  width="70px">
							<s:checkbox name="referencemap.includeRef1Flag" fieldValue="Y" value='%{#request.referencemap.includeRef1Flag == "Y"}'  cssStyle="width:13px;margin-left: 6px;" titleKey="tooltip.refFlag" cssClass="htmlTextAlpha" tabindex="34"  />
							</td>
							<td  width="70px">
							<s:checkbox name="referencemap.includeRef2Flag" fieldValue="Y" value='%{#request.referencemap.includeRef2Flag == "Y"}'  cssStyle="width:13px;margin-left: 6px;" titleKey="tooltip.refFlag" cssClass="htmlTextAlpha" tabindex="34"  />
							</td>
							<td  width="70px">
							<s:checkbox name="referencemap.includeRef3Flag" fieldValue="Y" value='%{#request.referencemap.includeRef3Flag == "Y"}'  cssStyle="width:13px;margin-left: 6px;" titleKey="tooltip.refFlag" cssClass="htmlTextAlpha" tabindex="34"  />
							</td>
							<td  width="70px">
							<s:checkbox name="referencemap.includeRef4Flag" fieldValue="Y" value='%{#request.referencemap.includeRef4Flag == "Y"}'  cssStyle="width:13px;margin-left: 8px;" titleKey="tooltip.refFlag" cssClass="htmlTextAlpha" tabindex="34"  />
							</td>
						</tr>
						<tr height="20px">
							<td width="191px" align="right"><B><s:text name="movementsearch.exclude"/>&nbsp;&nbsp;&nbsp;</B></td>
							<td width="3px">&nbsp;</td>
							<td width="102px" colspan="6">
								<s:textfield name="referencemap.excludeRef" cssClass="htmlTextAlpha" titleKey="tooltip.enterRefExcl" tabindex="33" style="width:190px;" onchange="return validateField(this,'movementsearch.excludeRef','alphaNumPatWithoutPercentage');" />  
							</td>
							<td width="5px">&nbsp;</td>
							<td  width="70px">
							<s:checkbox name="referencemap.excludeRefFlag" fieldValue="Y" value='%{#request.referencemap.excludeRefFlag == "Y"}'  cssStyle="width:13px;margin-left: 5px;" titleKey="tooltip.likeFlag" cssClass="htmlTextAlpha" tabindex="34"  />
							</td>
							<td  width="70px">
							<s:checkbox name="referencemap.excludeRef1Flag" fieldValue="Y" value='%{#request.referencemap.excludeRef1Flag == "Y"}'  cssStyle="width:13px;margin-left: 6px;" titleKey="tooltip.refFlag" cssClass="htmlTextAlpha" tabindex="34"  />
							</td>
							<td  width="70px">
							<s:checkbox name="referencemap.excludeRef2Flag" fieldValue="Y" value='%{#request.referencemap.excludeRef2Flag == "Y"}'  cssStyle="width:13px;margin-left: 6px;" titleKey="tooltip.refFlag" cssClass="htmlTextAlpha" tabindex="34"  />
							</td>
							<td  width="70px">
							<s:checkbox name="referencemap.excludeRef3Flag" fieldValue="Y" value='%{#request.referencemap.excludeRef3Flag == "Y"}'  cssStyle="width:13px;margin-left: 6px;" titleKey="tooltip.refFlag" cssClass="htmlTextAlpha" tabindex="34"  />
							</td>
							<td  width="70px">
							<s:checkbox name="referencemap.excludeRef4Flag" fieldValue="Y" value='%{#request.referencemap.excludeRef4Flag == "Y"}'  cssStyle="width:13px;margin-left: 8px;" titleKey="tooltip.refFlag" cssClass="htmlTextAlpha" tabindex="34"  />
							</td>
						</tr>
							</table>
							
							<table width="475px" border="0" cellpadding="0" cellspacing="1" height="">  
										<tr height="25">
											<td width="155px"><B><s:text name="movementsearch.uetr"/></B></td>
											<td width="320px">
												<s:textfield name="movementsearch.uetr" cssClass="htmlTextAlpha"
												titleKey="tooltip.movement.uetr" tabindex="29" style="width:320px; height: 22px;" /> 
											</td>
										</tr>
									</table>
							
						   <table width="392" border="0" cellpadding="0" cellspacing="1" height"">
	
							<tr height="25">
								<td width="100px"><B><s:text name="movementsearch.positionlevel"/></B></td>
								<td width="32px">&nbsp;</td>
								<td width="40px">
									<s:select cssClass="htmlTextAlpha" id="movementsearch.positionLevelAsString" name="movementsearch.positionLevelAsString" titleKey="tooltip.selectPostionLevel" tabindex="28" cssStyle="width:38px;" list="#request.collLang" listKey="value" listValue="label" />
								</td>
								 <td width="28px" >&nbsp;</td>
						           <td width="143px">
							    <span id="positionName" name="positionName" class="spantext">
						   </td>
							</tr>
							</table>
							
							<table width="348px" border="0" cellpadding="0" cellspacing="1" height="">  
								<tr height="25">
									<td width="115px"><B><s:text name="movementsearch.matchingParty"/></B></td>
									<td width="150px">
										<s:textfield name="movementsearch.matchingParty" cssClass="htmlTextAlpha" titleKey="tooltip.enterMatchingParty" tabindex="29" style="width:150px; height: 22px;" onchange="return validateField(this,'movementsearch.matchingParty','alphaNumPatWithoutPercentage');" /> 
									</td>
								</tr>
								<tr height="25">
									<td width="115px"><B><s:text name="movementsearch.productType"/></B></td>
									<td width="150px">
										<s:textfield name="movementsearch.productType" cssClass="htmlTextAlpha" titleKey="tooltip.enterProductType" tabindex="30" style="width:150px; height: 22px;" onchange="return validateField(this,'movementsearch.productType','alphaNumPatWithoutPercentage');" /> 
									</td>
								</tr>
							</table> 
							<table width="540px" border="0" cellpadding="0" cellspacing="1" height="">  
								<tr height="25">
									<td width="122px">
										<B><s:text name="movementsearch.postingDateFrom"/></B></td>
									<td width="162px" style="padding-left: 5px;">
									<s:if test='"matchSearch"!=#request.initialinputscreen' >
										<s:textfield titleKey="tooltip.enterPostingDateFrom" tabindex="31" cssClass="htmlTextAlpha" name="movementsearch.postingFromDateAsString"  style="width:80px;margin-bottom: 5px; height: 22px;" readonly="false" maxlength="10" onchange="return validateField(this,'movementsearch.postingDateFrom',dateFormat);validateFromDateField();" onmouseout="dateSelected=false;" />
										<A  title='<s:text name="tooltip.selectFromDate"/>'  tabindex="31" name="datelink4" ID="datelink4" onClick="cal4.select(document.forms[0].elements['movementsearch.postingFromDateAsString'],'datelink4',dateFormatValue);dateSelected=true;return false;"><img src="images/calendar-16.gif"></A>

									</s:if>
									
									<s:if test='"matchSearch"==#request.initialinputscreen' >
										<s:textfield titleKey="tooltip.enterPostingDateFrom" tabindex="32" cssClass="htmlTextAlpha" name="movementsearch.postingFromDateAsString"  style="width:80px;margin-bottom: 5px; height: 22px;" readonly="false" maxlength="10" onchange="return validateField(this,'movementsearch.postingDateFrom',dateFormat);validateFromDateField();" onmouseout="dateSelected=false;" />
										<A  title='<s:text name="tooltip.selectFromDate"/>'  tabindex="32" name="datelink4" ID="datelink4" onClick="cal4.select(document.forms[0].elements['movementsearch.postingFromDateAsString'],'datelink4',dateFormatValue);dateSelected=true;return false;"><img src="images/calendar-16.gif"></A>
									</s:if>
									
									</td>
									<td width="30px"><B><s:text name="movementsearch.valueto"/></B></td>
									<td width="130px;">
										<s:if test='"matchSearch"!=#request.initialinputscreen' >
										<s:textfield titleKey="tooltip.enterPostingDateTo" tabindex="33" cssClass="htmlTextAlpha" name="movementsearch.postingToDateAsString" style="width:80px;margin-bottom: 5px; height: 22px;" readonly="false" maxlength="10" onchange="return validateField(this,'movementsearch.valueto',dateFormat);validateToDateField();" onmouseout="dateSelected=false;" />
										<A  title='<s:text name="tooltip.selectToDate"/>'   tabindex="33" name="datelink5" ID="datelink5" onClick="cal5.select(document.forms[0].elements['movementsearch.postingToDateAsString'],'datelink5',dateFormatValue);dateSelected=true;return false;"><img src="images/calendar-16.gif"></A>
										</s:if>
										
										<s:if test='"matchSearch"==#request.initialinputscreen' >
										<s:textfield titleKey="tooltip.enterPostingDateTo" tabindex="34" cssClass="htmlTextAlpha" name="movementsearch.postingToDateAsString" style="width:80px;margin-bottom: 5px; height: 22px;" readonly="false" maxlength="10" onchange="return validateField(this,'movementsearch.valueto',dateFormat);validateToDateField();" onmouseout="dateSelected=false;" />
										<A  title='<s:text name="tooltip.selectToDate"/>'   tabindex="34" name="datelink5" ID="datelink5" onClick="cal5.select(document.forms[0].elements['movementsearch.postingToDateAsString'],'datelink5',dateFormatValue);dateSelected=true;return false;"><img src="images/calendar-16.gif"></A>
										</s:if>
										
									</td>									
								</tr>	
						  	</table>
					</table> 
				</div>
			</fieldset>
		</div>
		<!---------------------end of extra fieldset------------------------------------------>

		<!--------------Cash/Sec fieldset--------------------------------------------------->
		<div style="position:absolute; left:817px; top:107px;">
			<fieldset style="width:113px;border:2px groove;height:86px;">
				<legend><s:text name="movementsearch.cash/sec"/>
				</legend>
					<table width="100" border="0" cellpadding="0" cellspacing="0" height="66">  
						<tr height="25">
							<td>
								<s:radio tabindex="37" id="42" cssStyle="width:25px" titleKey="tooltip.movType" name="movementsearch.movementType" list="#{'C':''}"  />
								<label title='<s:text name="tooltip.movType"/>' for="42">
									<s:text name="movementsearch.cash"/>
								</label>
								
							</td>
						</tr>
						<tr height="25">
							<td>
								<s:radio tabindex="37" id="43" cssStyle="width:25px" titleKey="tooltip.movType" name="movementsearch.movementType" list="#{'U':''}"  />
								<label title='<s:text name="tooltip.movType"/>'  for="43">
									<s:text name="movementsearch.sec"/>
								</label>
							</td>
						</tr>
						<tr height="25">
							<td>
								<s:radio tabindex="37" cssStyle="width:25px" id="44" titleKey="tooltip.movType" name="movementsearch.movementType" list="#{'B':''}"  />
								<label title='<s:text name="tooltip.movType"/>' for="44">
									<s:text name="movementsearch.both"/>
								</label>
							</td>
						</tr>
					</table>
			</fieldset>
		</div>
		<!------------------end of cash/sec fieldset--------------------------------------->

		<!----------------------Debit/Credit fieldset--------------------------->

		<div style="position:absolute; left:817px; top:5px;">
			<fieldset style="width:113px;border:2px groove;height:80px;">
				<legend><s:text name="movementsearch.debit/credit"/>
				</legend>
					<table width="80" border="0" cellpadding="0" cellspacing="1" height="30">  
						<tr height="10">
							<td>
								<s:radio tabindex="36" cssStyle="width:25px" id="39" titleKey="tooltip.movSign" name="movementsearch.sign" list="#{'D':''}"  />
								<label title='<s:text name="tooltip.movSign"/>'  for="39">
									<s:text name="movementsearch.debit"/>
								</label>
							</td>
						</tr>
						<tr height="10">
							<td>
								<s:radio tabindex="36" cssStyle="width:25px" id="40" titleKey="tooltip.movSign" name="movementsearch.sign" list="#{'C':''}"  />
								<label title='<s:text name="tooltip.movSign"/>' for="40">
									<s:text name="movementsearch.credit"/>
								</label>
							</td>
						</tr>
						<tr height="10">
							<td>
								<s:radio tabindex="36" cssStyle="width:25px" id="41" titleKey="tooltip.movSign" name="movementsearch.sign" list="#{'B':''}"  />
								<label title='<s:text name="tooltip.movSign"/>'  for="41">
									<s:text name="movementsearch.both"/>
								</label>
							</td>
						</tr>
				</table>
			</fieldset>
		</div>
		<!----------------------end of debit/credit fieldset------------------->

		<!-----------------------Predict status fieldset---------------------->

		<div style="position:absolute; left:817px; top:220px;">
				<fieldset style="width:113px;border:2px groove;height:107px;">

				<legend><s:text name="movementsearch.predictstatus"/>
				</legend>
				<table width="100" border="0" cellpadding="0" cellspacing="0" height="88">  
					<tr height="22">
						<td>
							<s:radio tabindex="38" cssStyle="width:25px" id="45" titleKey="tooltip.movPredict" name="movementsearch.predictStatus" list="#{'I':''}"  />
							<label title='<s:text name="tooltip.movPredict"/>' for="45">
								<s:text name="movementsearch.included"/>
							</label>
						</td>
					</tr>
					<tr height="22">
						<td>
							<s:radio tabindex="38" cssStyle="width:25px" id="46" titleKey="tooltip.movPredict" name="movementsearch.predictStatus" list="#{'E':''}"  />
							<label title='<s:text name="tooltip.movPredict"/>' for="46">
								<s:text name="movementsearch.excluded"/>
							</label>
						</td>
					</tr>
					<tr height="22">
						<td>
							<s:radio tabindex="38" cssStyle="width:25px" id="47" titleKey="tooltip.movPredict" name="movementsearch.predictStatus" list="#{'C':''}"  />
							<label title='<s:text name="tooltip.movPredict"/>' for="47">
								<s:text name="movementsearch.cancelled"/>
							</label>
						</td>
					</tr>
					<tr height="22">
						<td>
							<s:radio tabindex="38" cssStyle="width:25px" id="48" titleKey="tooltip.movPredict" name="movementsearch.predictStatus" list="#{'A':''}"  />
							<label title='<s:text name="tooltip.movPredict"/>' for="48">
								<s:text name="movementsearch.all"/>
							</label>
						</td>
					</tr>
				</table>
			</fieldset>
		</div>
		<!------------------------end of predict status fieldset--------------->
		
		<!-----------------------Start External Balance status fieldset---------------------->
		<div style="position:absolute; left:817px; top:349px;">
				<fieldset style="width:113px;border:2px groove;height:107px;">

				<legend><s:text name="label.movementsearch.externalBalanceStatus"/>
				</legend>
				<table width="100" border="0" cellpadding="0" cellspacing="0" height="88">  
					<tr height="22">
						<td>
							<s:radio tabindex="39" cssStyle="width:25px" id="49" titleKey="tooltip.movExternalBalanceStatus" name="movementsearch.extBalStatus" list="#{'I':''}"  />
							<label title='<s:text name="tooltip.movExternalBalanceStatus"/>' for="49">
								<s:text name="movementsearch.included"/>
							</label>
						</td>
					</tr>
					<tr height="22">
						<td>
							<s:radio tabindex="39" cssStyle="width:25px" id="50" titleKey="tooltip.movExternalBalanceStatus" name="movementsearch.extBalStatus" list="#{'E':''}"  />
							<label title='<s:text name="tooltip.movExternalBalanceStatus"/>' for="50">
								<s:text name="movementsearch.excluded"/>
							</label>
						</td>
					</tr>
					<tr height="22">
						<td>
							<s:radio tabindex="39" cssStyle="width:25px" id="51" titleKey="tooltip.movExternalBalanceStatus" name="movementsearch.extBalStatus" list="#{'A':''}"  />
							<label title='<s:text name="tooltip.movExternalBalanceStatus"/>' for="51">
								<s:text name="movementsearch.all"/>
							</label>
						</td>
					</tr>
				</table>
			</fieldset>
		</div>
		<!------------------------End of External Balance status fieldset--------------->
		
			<!--------------------open movements fieldset------------>
		<div style="position:absolute; left:817px; top:478px;">
				<fieldset style="width:113px;border:2px groove;height:57px;">

				<legend><s:text name="movementsearch.open"/>
				</legend>
				<table width="100" border="0" cellpadding="0" cellspacing="0" height="47">  
					<tr height="22">
						<td>
							<s:radio tabindex="40" cssStyle="width:25px" id="52" titleKey="tooltip.open" name="movementsearch.openFlag" list="#{'O':''}"  />
							<label title='<s:text name="tooltip.open"/>' for="52">
								<s:text name="movementsearch.open"/>
							</label>
						</td>
					</tr>							
					<tr height="22">
						<td>
							<s:radio tabindex="40" cssStyle="width:25px" id="53" titleKey="tooltip.open" name="movementsearch.openFlag" list="#{'A':''}"  />
							<label title='<s:text name="tooltip.open"/>' for="53">
								<s:text name="movementsearch.all"/>
							</label>
						</td>
					</tr>
				</table>
			</fieldset>
		</div>
		   <!--------------open movements fieldset------------>
		<!----------------------second fieldset--------------------------------------------------->
		<div style="position:absolute; left:8px; top:6px;">
			<fieldset style="width:125px;border:2px groove;height:143px;">
				<legend><s:text name="movementsearch.status"/>
				</legend>
					<table width="170" border="0" cellpadding="0" cellspacing="2" height="154">  
						<tr height="22">
							<!-- Authorise and Referred added into match status -->
							<td>
								<s:if test='"matchSearch"!=#request.initialinputscreen' >
									<s:radio tabindex="2" id="2" titleKey="tooltip.movStatus" name="movementsearch.matchStatus" list="#{'A':''}"  />
								</s:if>
								<s:if test='"matchSearch"==#request.initialinputscreen' >
									<s:radio tabindex="2" id="2" titleKey="tooltip.movStatus" name="movementsearch.matchStatus" list="#{'A':''}" disabled="true"  />
								</s:if>
									<label title='<s:text name="tooltip.movStatus"/>'  for="2">
										<s:text name="movementsearch.status.authorise"/>
									</label>
								</td>
							</tr>

								<tr height="22">
							<!-- Authorise and Referred added into match status -->
							<td>
								<s:if test='"matchSearch"!=#request.initialinputscreen' >
									<s:radio tabindex="2" id="4" titleKey="tooltip.movStatus" name="movementsearch.matchStatus" list="#{'R':''}"  />
								</s:if>
								<s:if test='"matchSearch"==#request.initialinputscreen' >
									<s:radio tabindex="2" id="4" titleKey="tooltip.movStatus" name="movementsearch.matchStatus" list="#{'R':''}" disabled="true"  />
								</s:if>
									<label title='<s:text name="tooltip.movStatus"/>' for="4">
										<s:text name="movementsearch.status.referred"/>
									</label>
							</td>
							</tr>

							<td>
								<s:if test='"matchSearch"!=#request.initialinputscreen' >
									<s:radio tabindex="2" id="5" titleKey="tooltip.movStatus" name="movementsearch.matchStatus" list="#{'L':''}"  />
								</s:if>
								<s:if test='"matchSearch"==#request.initialinputscreen' >
									<s:radio tabindex="2" id="5" titleKey="tooltip.movStatus" name="movementsearch.matchStatus" list="#{'L':''}" disabled="true"  />
								</s:if>
									<label title='<s:text name="tooltip.movStatus"/>' for="5">
										<s:text name="movementsearch.outstanding"/>
									</label>
							</td>
						</tr>
						<tr height="22">
							<td>
								<s:if test='"matchSearch"!=#request.initialinputscreen' >
									<s:radio tabindex="2" id="6" titleKey="tooltip.movStatus" name="movementsearch.matchStatus" list="#{'M':''}"  />
								</s:if>
								<s:if test='"matchSearch"==#request.initialinputscreen' >
									<s:radio tabindex="2" id="6" titleKey="tooltip.movStatus" name="movementsearch.matchStatus" list="#{'M':''}" disabled="true"  />
								</s:if>
									<label title='<s:text name="tooltip.movStatus"/>' for="6">
										<s:text name="movementsearch.offered"/>
								</label>
							</td>
						</tr>
						<tr height="22">
							<td>
								<s:if test='"matchSearch"!=#request.initialinputscreen' >
									<s:radio tabindex="2" id="7" titleKey="tooltip.movStatus" name="movementsearch.matchStatus" list="#{'S':''}"  />
								</s:if>
								<s:if test='"matchSearch"==#request.initialinputscreen' >
									<s:radio tabindex="2" id="7" titleKey="tooltip.movStatus" name="movementsearch.matchStatus" list="#{'S':''}" disabled="true"  />
								</s:if>
									<label title='<s:text name="tooltip.movStatus"/>' for="7">
										<s:text name="movementsearch.suspended"/>
									</label>
							</td>
						</tr>
						<tr height="22">
							<td>
								<s:if test='"matchSearch"!=#request.initialinputscreen' >
									<s:radio tabindex="2" id="8" titleKey="tooltip.movStatus" name="movementsearch.matchStatus" list="#{'C':''}"  />
								</s:if>
								<s:if test='"matchSearch"==#request.initialinputscreen' >
									<s:radio tabindex="2" id="8" titleKey="tooltip.movStatus" name="movementsearch.matchStatus" list="#{'C':''}" disabled="true"  />
								</s:if>
									<label title='<s:text name="tooltip.movStatus"/>'  for="8">
										<s:text name="movementsearch.confirmed"/>
									</label>
							</td>
						</tr>
						<!-- Changed Review filed into Reconcilled -->
						<tr height="22">
							<td>
							<s:if test='"matchSearch"!=#request.initialinputscreen' >
								<s:radio tabindex="2" id="9" titleKey="movementsearch.status.reconcilled" name="movementsearch.matchStatus" list="#{'E':''}"  />
							</s:if>
							<s:if test='"matchSearch"==#request.initialinputscreen' >
								<s:radio tabindex="2" id="9" titleKey="movementsearch.status.reconcilled" name="movementsearch.matchStatus" list="#{'E':''}" disabled="true"  />
							</s:if>
								<label title='<s:text name="movementsearch.status.reconcilled"/>' for="9">
									<s:text name="movementsearch.status.reconcilled"/>
								</label>
							</td>
						</tr>
						<tr height="22">
							<td>
								<s:if test='"matchSearch"!=#request.initialinputscreen' >
									<s:radio tabindex="2" id="10" titleKey="tooltip.movStatus" name="movementsearch.matchStatus" list="#{'D':''}"  />
								</s:if>
								<s:if test='"matchSearch"==#request.initialinputscreen' >
									<s:radio tabindex="2" id="10" titleKey="tooltip.movStatus" name="movementsearch.matchStatus" list="#{'D':''}" disabled="true"  />
								</s:if>
									<label title='<s:text name="tooltip.movStatus"/>' for="10">
										<s:text name="movementsearch.allmatched"/>
									</label>
							</td>
						</tr>
						<tr height="22">
							<td>
								<s:if test='"matchSearch"!=#request.initialinputscreen' >
									<s:radio tabindex="2" id="11" titleKey="tooltip.movStatus" name="movementsearch.matchStatus" list="#{'X':''}"  />
								</s:if>
								<s:if test='"matchSearch"==#request.initialinputscreen' >
									<s:radio tabindex="2" id="11" titleKey="tooltip.movStatus" name="movementsearch.matchStatus" list="#{'X':''}" disabled="true"  />
								</s:if>
									<label title='<s:text name="tooltip.movStatus"/>'  for="11">
										<s:text name="movementsearch.allstatuses"/>
									</label>
							</td>
						</tr>
					</table>
			</fieldset>
		</div>
		<!-------------------------end of second fieldset--------------------------------------->

		<!------------------------- Account Class fieldset added----------------------------------->

		<div style="position:absolute; left:8px; top:270px;">
			<fieldset style="width:125px;border:2px groove;height:120px;">
				<legend><s:text name="movementsearch.account.fielset"/>
				</legend>
					<table width="170" border="0" cellpadding="0" cellspacing="2" height="88">  
						<tr height="22">
						
							<td>
								<s:if test='"matchSearch"!=#request.initialinputscreen' >
									<s:radio tabindex="3" id="12" titleKey="movementsearch.account.selectaccountclass" name="movementsearch.accountClass" list="#{'N':''}" onclick="checkAccountType('populateAcc')"  />
								</s:if>
								<s:if test='"matchSearch"==#request.initialinputscreen' >
									<s:radio tabindex="3" id="12" titleKey="movementsearch.account.selectaccountclass" name="movementsearch.accountClass" list="#{'N':''}" onclick="checkAccountType('searchAccFromMatch')"  />
								</s:if>
									<label title='<s:text name="movementsearch.account.nostro"/>'  for="12">
										<s:text name="movementsearch.account.nostro"/>
									</label>
								</td>
							</tr>

								<tr height="22">
						
							 <td>
								<s:if test='"matchSearch"!=#request.initialinputscreen' >
									<s:radio tabindex="3" id="13" titleKey="movementsearch.account.selectaccountclass" name="movementsearch.accountClass" list="#{'C':''}" onclick="checkAccountType('populateAcc')"  />
								</s:if>
								<s:if test='"matchSearch"==#request.initialinputscreen' >
									<s:radio tabindex="3" id="13" titleKey="movementsearch.account.selectaccountclass" name="movementsearch.accountClass" list="#{'C':''}" onclick="checkAccountType('searchAccFromMatch')"  />
								</s:if>
									<label title='<s:text name="movementsearch.account.current"/>' for="13">
										<s:text name="movementsearch.account.current"/>
									</label>
							 </td>
							</tr>
							<tr height="22px">

							<td>
								<s:if test='"matchSearch"!=#request.initialinputscreen' >
									<s:radio tabindex="3" id="14" titleKey="movementsearch.account.selectaccountclass" name="movementsearch.accountClass" list="#{'L':''}" onclick="checkAccountType('populateAcc')"  />
								</s:if>
								<s:if test='"matchSearch"==#request.initialinputscreen' >
									<s:radio tabindex="3" id="14" titleKey="movementsearch.account.selectaccountclass" name="movementsearch.accountClass" list="#{'L':''}" onclick="checkAccountType('searchAccFromMatch')"  />
								</s:if>
									<label title='<s:text name="movementsearch.account.loro"/>'  for="14">
										<s:text name="movementsearch.account.loro"/>
									</label>
							</td>
						</tr>
						<td>
								<s:if test='"matchSearch"!=#request.initialinputscreen' >
									<s:radio tabindex="3" id="15" titleKey="movementsearch.account.selectaccountclass" name="movementsearch.accountClass" list="#{'E':''}" onclick="checkAccountType('populateAcc')"  />
								</s:if>
								<s:if test='"matchSearch"==#request.initialinputscreen' >
									<s:radio tabindex="3" id="15" titleKey="movementsearch.account.selectaccountclass" name="movementsearch.accountClass" list="#{'E':''}" onclick="checkAccountType('searchAccFromMatch')"  />
								</s:if>
									<label title='<s:text name="acc.netting"/>' for="15">
										<s:text name="acc.netting"/>
									</label>
						</td>
						</tr>
						<tr height="22">
							<td>
								<s:if test='"matchSearch"!=#request.initialinputscreen' >
									<s:radio tabindex="3" id="16" titleKey="movementsearch.account.selectaccountclass" name="movementsearch.accountClass" list="#{'O':''}" onclick="checkAccountType('populateAcc')"  />
								</s:if>
								<s:if test='"matchSearch"==#request.initialinputscreen' >
									<s:radio tabindex="3" id="16" titleKey="movementsearch.account.selectaccountclass" name="movementsearch.accountClass" list="#{'O':''}" onclick="checkAccountType('searchAccFromMatch')"  />
								</s:if>
									<label title='<s:text name="movementsearch.account.other"/>' for="16">
										<s:text name="movementsearch.account.other"/>
								</label>
							</td>
						</tr>
						<tr height="22">
							<td>
								<s:if test='"matchSearch"!=#request.initialinputscreen' >
								<!-- Code Modified For Mantis 2072 by Sudhakar on 31-10-2012:Currency change should not change Status and Account class to All,The parameter of checkAccount method is changed as populateAcc instead of populate  -->
									<s:radio tabindex="3" id="17" titleKey="movementsearch.account.selectaccountclass" name="movementsearch.accountClass" list="#{'All':''}" onclick="checkAccount('populateAcc')"  />
								</s:if>
								<s:if test='"matchSearch"==#request.initialinputscreen' >
									<s:radio tabindex="3" id="17" titleKey="movementsearch.account.selectaccountclass" name="movementsearch.accountClass" list="#{'All':''}" onclick="checkAccount('searchAccFromMatch')"  />
								</s:if>
									<label title='<s:text name="qualityTab.all"/>' for="17">
										<s:text name="qualityTab.all"/>
								</label>
							</td>
						</tr>
					
						
					
					</table>
			</fieldset>
		</div>
		<!------------------ End of Account class fieldset--------------------------------------------------------->
		
		
		<!------------------ Start of additional search criteria button--------------------------------------------------------->
		<div id="ddimagebuttons" color="#7E97AF" style="position:absolute; left:8px; top:460px; visibility:visible;">
		<fieldset style="width:175px;border:2px groove;height:140px;">
		<legend><s:text name="movementsearch.criteria.fielset"/></legend>
		<table width="60px"  height= "50px" border="0" cellspacing="0" cellpadding="0" height="20">
		   <tr>
		
		    <td width="30px">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</td>
			<td width="70" id="advacedButton">		
				<a title='<s:text name="tooltip.criteria"/>' tabindex="53" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onKeyDown="submitEnter(this,event)" onClick="openAddColsScreen('openAddColsScreen');"><s:text name="button.search.criteria"/></a>			
			</td>
			
			</tr>
		</table>
		</fieldset>	
	   </div>
	   <!------------------ End of additional search criteria button--------------------------------------------------------->
	</div>
</div>

<!-----------------------Print button------------------------->
<div id="MovementSearch" style="position:absolute; left:880; top:620px; width:70; height:39px; visibility:visible;">
	<table width="60px" border="0" cellspacing="0" cellpadding="0" height="70">
		<tr>

		<td align="Right">
				<a tabindex="54"  href=# onKeyDown="submitEnter(this,event)" onClick="javascript:openWindow(buildPrintURL('print','Movement Search '),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')" onMouseOut="MM_swapImgRestore()" onMouseOver="MM_swapImage('Help','','images/help_default.GIF ',1)"><img src="images/help_default.GIF " name="Help"  border="0" ></a> 
	   </td>

			<td align="right" id="Print">
				<a tabindex="54" onKeyDown="submitEnter(this,event)" onClick="printPage();" onMouseOut="MM_swapImgRestore()" onMouseOver="MM_swapImage('Print','','images/Print_R.gif ',1)"><img src="images/Print.gif " name="Print"  border="0"  title='<s:text name="tooltip.printScreen"/>'/></a>	
			</td>
		</tr>
	</table>
</div>
<!--------------------------end of print button-------------------------->


<!-----------------Ok and close buttons-------------------------------->

<div id="ddimagebuttons" color="#7E97AF" style="position:absolute; border:2px outset; left:10; top:636; width:941px; height:39px; visibility:visible;">
  <div id="MovementSearchParam" style="position:absolute; left:6; top:4; width:810; height:15px; visibility:visible;">
  <table width="140" border="0" cellspacing="0" cellpadding="0" height="20">
		<tr>
			<td width="70" id="okbutton"> 
			<s:if test='"matchSearch"!=#request.initialinputscreen' >
			<a title='<s:text name="tooltip.executeSearch"/>'  tabindex="52" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onKeyDown="submitEnter(this,event)" onClick="javascript:getMovementDetails('search')"><s:text name="button.search"/></a>			
			</s:if>
			<s:if test='"matchSearch"==#request.initialinputscreen' >
			<a title='<s:text name="tooltip.executeSearch"/>' tabindex="52" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onClick="javascript:checkDate()"><s:text name="button.search"/></a>			
			</s:if>			  
			</td>
			<td width="70" id="closebutton">		
				<a title='<s:text name="tooltip.close"/>' tabindex="53" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onKeyDown="submitEnter(this,event)" onClick="confirmClose('P');"><s:text name="button.close"/></a>			
			</td>
  	   </tr>
	  </table>
</div>
<div>

<!-----------------------end buttons---------------------------------->

</s:form>
</body>
</html>