<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@ include file="/taglib.jsp"%>
<%@ page import="org.swallow.util.CommonDataManager"%>
<%@ page import="org.swallow.util.SwtConstants"%>
<%@ page import="org.swallow.util.SwtUtil"%>
<%@ include file="/angularJSUtils.jsp"%>

<html lang="en">
<head>
<title><s:text name="inputexceptionsmessages.title.window" /><%="yes".equals(request.getAttribute("fromPCM")) ? " (PCM)":""%></title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<link rel="icon" type="image/x-icon" href="favicon.ico">
<link rel="stylesheet" href="angularSources/styles.css?version=<%= SwtUtil.appVersion %>">
</head>
<script type="text/javascript">


	function openJavaWindow(a, left,top,width,height) {
		openWindow(a,'rolemaintenancechangeWindow','left='+left+',top='+top+',width='+width+',height='+height+',toolbar=0, resizable=yes, scrollbars=yes,status=yes');
	}
	 
	window.onload = function () {
		setParentChildsFocus();
		//Aliveni: Calling setTitleSuffix() function in CommonJS.js file to distinguish multiple 
		//Implementations (Mantis 857), on 27-AUG-2010
		setTitleSuffix(document.forms[0]); 
	}
	 
	function refreshParent () {
		opener.refresh = true;	 
	}
</script>

<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<title></title>

<style>
body {
	margin: 0px;
	overflow: hidden
}

#workFlowMonitorcontent {
	border: solid 0px #000;
	width: 100%;
	height: 100%;
	float: left;
	margin: 0px 0px;
}
</style>
<script type="text/javascript" src="jsp/work/assets/swfobject.js"></script>


<body scroll="no" style="overflow:hidden" leftmargin="0" topmargin="0" marginheight="0">
<s:form action="inputexceptions.do">
	<input name="method" type="hidden" value="">
</s:form>

<script type="text/javascript">
		 /* Start : Added by Vivekanandan For Matis 1364 : Input Exceptiosn Issue on 22-02-2011 */
		
		var screenRoute = "MessageDetails";	
		var label = new Array ();
		label["text"] = new Array ();
		label["tip"] = new Array ();
		
		label["text"]["label_message_type"] = "<s:text name="inputexceptions.label.message_type"/>"			
		label["text"]["label_message_status"] = "<s:text name="inputexceptions.label.message_status"/>"
		label["text"]["label-close"] = "<s:text name="inputException.close"/>"
		label["tip"]["close_button"] = "<s:text name="tooltip.close"/>"		
		label["text"]["label-of"] = "<s:text name="genericDisplayMonitor.labelOf"/>"	
		label["text"]["label-rep"] = "<s:text name="inputException.reprocess"/>"
		label["tip"]["rep_button"] = "<s:text name="inputexceptions.tooltip.button_rep"/>"
		label["text"]["label-rej"] = "<s:text name="inputException.reject"/>"
		label["tip"]["rej_button"] = "<s:text name="inputexceptions.tooltip.button_rej"/>"
		label["text"]["label-supp"] = "<s:text name="inputException.suppress"/>"
		label["tip"]["supp_button"] = "<s:text name="inputexceptions.tooltip.button_supp"/>"
		label["text"]["label-inputExceptionMessage"] = "<s:text name="inputexceptionsmessages.title.window" />"
		label["text"]["label-noMessage"] = "<s:text name="alert.interfaceExceptions.noMessage" />"
		label["text"]["label-multipleMessageSelected"] = "<s:text name="label.multipleMessageSelected" />"
		label["text"]["label-messageID"] = "<s:text name="movement.messageId" />"
		label["text"]["label-noMessageSelected"] = "<s:text name="label.noMessageSelected" />"
		label["text"]["label-updateResponse"] = "<s:text name="label.updateResponse" />"
		label["text"]["label-areyouSure"] = "<s:text name="label.areyouSure" />"
		label["text"]["label-deleteMessages"] = "<s:text name="label.deleteMessages" />"
		label["text"]["label-page"] = "<s:text name="genericDisplayMonitor.labelPage"/>";
		label["text"]["label-autoFormat"] = "<s:text name="inputexceptions.label.autoFormatXML"/>";
		label["tip"]["label-autoFormat"] = "<s:text name="inputexceptions.tooltip.autoFormatXML"/>";
		var fromPCM = '${requestScope.fromPCM}';
		
		var fromDashboard = '${requestScope.fromDashboard}';
		var m = '${requestScope.m}';
		/* End : Added by Vivekanandan For Matis 1364 : Input Exceptiosn Issue on 22-02-2011 */
	</script>
<%@ include file="/angularscripts.jsp"%>
</body>
</html>
