<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@ include file="/taglib.jsp"%>
<%@ page import="org.swallow.util.SwtConstants"%>
<%@ page import="org.swallow.util.SwtUtil"%>

<html>
<head>
<title>Sweep Search List - SMART-Predict</title>
<%@ include file="/angularJSUtils.jsp"%>
<link rel="stylesheet"
	href="angularSources/styles.css?version=<%=SwtUtil.appVersion%>">
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
</head>
<SCRIPT language="JAVASCRIPT">
	var requestURL = new String('<%=request.getRequestURL()%>');
	var appName = "<%=SwtUtil.appName%>"; 
	var idy = requestURL.indexOf('/'+appName+'/');	
	requestURL=requestURL.substring(0,idy+1);
	var oXMLHTTP = new XMLHttpRequest();
 	var archiveId = "${requestScope.archiveId}";
 	var entityId = "${requestScope.entityId}";
 	var amountover = "${requestScope.amountover}";
 	var amountunder = "${requestScope.amountunder}";
 	var currencyCode = "${requestScope.currencyCode}";
 	var currencyGroup = "${requestScope.currencyGroup}";
 	var accountId = "${requestScope.accountId}";
 	var bookCode = "${requestScope.bookCode}";
 	var valueFromDateAsString = "${requestScope.valueFromDateAsString}";
 	var valueToDateAsString = "${requestScope.valueToDateAsString}";
 	var generatedby = "${requestScope.generatedby}";
 	var postcutoff = "${requestScope.postcutoff}";
 	var submittedby = "${requestScope.submittedby}";
 	var authorisedby = "${requestScope.authorisedby}";
 	var accounttype = "${requestScope.accounttype}";
 	var sweeptype = "${requestScope.sweeptype}";
 	var status = "${requestScope.status}";
 	var msgFormat = "${requestScope.msgFormat}";
	 
	var screenRoute = "SweepSearchList";

 	/**
  	*	This section is used to handle calender button on the screen and is used to set the position of the same.
  	*/
	var dateFormatValue = '<s:property value="#request.session.CDM.dateFormatValue" />';
	var menuAccessId = "${requestScope.menuAccessId}";

	
	function subSweepSearchList(screenName, params) {
		
		var param = 'sweepsearch.do?method=OpenSubSweepSearchList'+'&sweepId=';
	    param +=params[0].sweepId;
		param +='&valueDate=';
		param +=params[0].selectedvalueDate;
        param +='&accountIdCr=';
		param +=params[0].selectedaccountIdCr;
        param +='&accountIdDr=';
		param +=params[0].selectedaccountIdDr;
        param +='&originalamt=';
		param +=params[0].selectedoriginalamt;
        param +='&sweepType=';
		param +=params[0].selectedsweepType;
        param +='&sweepStatus=';
		param +=params[0].selectedsweepStatus;
    	param +='&inputUser=';
		param +=params[0].selectedinputUser;
        param +='&submitUser=';
		param +=params[0].selectedsubmitUser;
        param +='&authorizedUser=';
		param +=params[0].selectedauthorizedUser;
        param +='&movementIdCr=';
		param +=params[0].selectedmovementIdCr;
        param +='&movementIdDr=';
		param +=params[0].selectedmovementIdDr;
		param +='&submitamt=';
   		param +=params[0].selectedsweepamt;
		param +='&authorizeamt=';
		param +=params[0].selectedauthorizedamt;
		param +='&entityid=';
		param +=params[0].selectedentityId;

		param += '&archiveId='+ params[0].archiveId;
		
		
// 		var param = "sweepsearch.do?method=OpenSubSweepSearchList&screenName="+screenName;
// 		param += '&allParams=' + params;
		
		
		var 	mainWindow = openWindow (param, 'subSweepSearchList','left=10,top=230,width=850,height=880,toolbar=0, resizable=yes, status=yes, scrollbars=yes');
        return false;
	}
	
// 	function openLogScreen(methodName){
// 		var param = "accountPeriod.do?method="+methodName;
// 		var 	mainWindow = openWindow (param, 'openLogScreen','left=10,top=230,width=850,height=550,toolbar=0, resizable=yes, //status=yes, scrollbars=yes');	
// 		return false;
// 	}
	
	
</SCRIPT>


<body scroll="no" style="overflow: hidden" leftmargin="0" topmargin="0"
	marginheight="0">


	<%@ include file="/angularscripts.jsp"%>

	<form id="exportDataForm" target="tmp" method="post">
		<input type="hidden" name="data" id="exportData" /> <input
			type="hidden" name="screen" id="exportDataScreen"
			value="<s:text name="PreAdviceInput.title.window"/>" />
	</form>
	<iframe name="tmp" width="0%" height="0%" src="#" />
</body>
</html>
