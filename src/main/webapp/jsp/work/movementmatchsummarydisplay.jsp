<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@ include file="/taglib.jsp"  %>
<%@ page import="org.swallow.util.SwtConstants" %>
<%@ page import="org.swallow.util.SwtUtil" %>
<html>
<head>
<s:if test='"manual"!=#request.match' >
	<title><s:text name="mvmMatchSummDisplay.title.window"/></title>
</s:if>

<s:if test='"manual"==#request.match' >
	<title><s:text name="manualMatch.title.window"/></title>
</s:if>

<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
<script language="JAVASCRIPT" src="js/movementsummary.js"></script>
<script language="JAVASCRIPT" >
/* Start: UAT defect sheet 23/11/2006  -- refreshing MovementDisplay screen if movement is removed from match */
<s:if test='"Y"==#request.refreshMovementDisplayScreen' >
		//alert(window.opener.document.forms[0].elements["movement.id.movementIdAsString"]);
		if(window.opener.document.forms[0].elements["movement.id.movementIdAsString"] != null){
			var movId =  window.opener.document.forms[0].elements["movement.id.movementIdAsString"].value;
			//alert("The movId is==>" + movId + "===");
			window.opener.document.forms[0].selectedMovementId.value = movId;
			window.opener.document.forms[0].method.value = "displayMovement";
			window.opener.document.forms[0].buttonDiv.value = "movementDisplay";
			window.opener.document.forms[0].submit();
		}
</s:if>
/* End:  UAT defect sheet 23/11/2006 */

/* START: Code changed as par ING requirements to the Roles - Menu Access, 13-JUN-2007 */
var menuEntityCurrGrpAccess = "${requestScope.menuEntityCurrGrpAccess}";
/* END: Code changed as par ING requirements to the Roles - Menu Access, 13-JUN-2007 */
var matches = "${requestScope.matchList}";
var currentIndex = "${requestScope.currentIndex}";
var manualMatch = "${requestScope.screen}";
var selectedtab="${requestScope.selectedTab}";
//alert(selectedtab);
var valueDate = "${requestScope.valueDate}";
var day="${requestScope.queueTabSelected}";
var inptMethod="${requestScope.method}";

var appName = "<%=SwtUtil.appName%>";
var requestURL = new String('<%=request.getRequestURL()%>');
var idy = requestURL.indexOf('/'+appName+'/');
requestURL=requestURL.substring(0,idy+1) ;
//Start:Mantis 832-Acquring Lock while change-Code added by Saminathan at onsite on 31-Dec-2008
var initialscreen="movementmatchsummarydisplay";
var alertOrangeImage = "images/Alert/scenario/normal.png";
var alertRedImage = "images/Alert/scenario/critical.png";

function checkStatus(process){
	
	/* START: Code fixed to open the correct match queue from workflow monitor */
	document.forms[0].status.value = '${requestScope.status}';
	/* END: Code fixed to open the correct match queue from workflow monitor */
	
	var matchId=document.forms[0].elements["movement.matchId"].value;	
	var entityId=document.forms[0].elements["movement.id.entityId"].value;
	
	var status=document.forms[0].elements["movementDetail.matchStatus"].value;

	var rowCount= getRowCount();	

	/* Start:Mantis 886-Match operation not working for match that contains large number of movements
	* Code modified by saminathan at onsite on 22-Jan-2009
	*/
	var table = document.getElementById("movementSummaryDetails"); 
	var tbody = table.getElementsByTagName("tbody")[0];    
	var rows = tbody.getElementsByTagName("tr");
	var movementList = "";
	var length;
	var columns;
	var str;
	var  minValue=0;
	var maxValue=0;
	var count=1;//To check  whether  movement's list is huge.ie.more than 100.
	do
    {		
		if (rowCount-minValue>100)
		{
			maxValue=minValue+100;
		}     
		else
		{
			maxValue=rowCount;
		}		
		for (i=minValue; (i < maxValue &&  i < rowCount); i++) 
		{
		     movementList = movementList+rows[i].cells[14].innerText+",";
		}
		var oXMLHTTP = new XMLHttpRequest();
		var sURL = requestURL + appName+"/movementmatchdisplay.do?method=checkMatchStatus";
		sURL = sURL + "&matchId="+matchId;
		sURL = sURL + "&entityId="+entityId;
		sURL = sURL + "&status="+status;
		sURL = sURL + "&movementList="+movementList;
		sURL = sURL + "&rowCount="+rowCount;
		sURL = sURL + "&maxValue="+maxValue;
		sURL = sURL + "&count="+count;
		oXMLHTTP.open( "POST", sURL, false );
		oXMLHTTP.send();		
		str=new String(oXMLHTTP.responseText);				  			 	
		 minValue=maxValue;
		 movementList = "";
		 count++;
   }while(maxValue < rowCount);
    //End:Mantis 886-Match operation not working for match that contains large number of movements
   
	
	if(str != "true"){
		alert('<s:text name="tooltip.alertMnmtDislay"/>');
	}else{				
		
		/*Mantis Issue	:	735*/
		/*Subject 		:	warning mechanism  implementation to help prevent the
							user  on  	click of confirm / Suspend / Reconcile / 
							unmatch buttons*/
		/*Description 	:	Previously in the manual match screen on click of confirm 
							/ suspend / unmatch / reconcile there was no warning 
							mechanism for the user 
							1.If there is a difference in the totals among the position levels and
							2.If more than one position level in the prospective match has 
							movements with PREDICT_STATUS�=�"I".							
							Now a warning mechanism is being implemented to avoid this situation.
							To fix that below code changes has been done*/
		/* code modified by selva Kumar on 15/10/2008 code fix starts: */			
		
		var submitFlag=false;	
						
		if(process == "remove"){
			removeMvnt('remove');
		}	
		if(process == "unmatch"){		
			lockMatchOnServer(process);	
		}		
		if(process == "confirm" || process == "suspend"  || process == "reconcile" )
		{
		
			
			
			submitFlag=false;
			var PredictFlag=false;
			
			var highestPosLvl = getPosLvl("movementSummaryDetails");			
			
			var amtTotals = new Array();
			var amtTotalstring = "";

			
			var confirmFlag = false;
			
			
			for(var i=1;i<highestPosLvl.length;i++)
			{				
				if( highestPosLvl[i-1] != highestPosLvl[i])
				{					
					confirmFlag = true;
				}			
			}
			
			if(confirmFlag)
			{	
					
				if(PosLvlCorrAmts(highestPosLvl))
				{
						
					var title = '<s:text name="accountmonitor.confirm.title"/>';
					var testMessageY ='<s:text name="manualMatch.warning.messageForAmtTotals"/>';
					buttonId = ShowErrMsgWindowWithBtn(title  ,testMessageY , OK_CANCEL);								
					if(buttonId == 3)
					{
						PredictFlag=true;											
					}				
					
				}
				else
				{
					PredictFlag=true;
				}
				
				
				if(PredictFlag)
				{
					if(PosLvlCorrPreductStatus(highestPosLvl))
					{
					
					var title = '<s:text name="accountmonitor.confirm.title"/>';
					var testMessageY ='<s:text name="manualMatch.warning.messageForPredictStatus"/>';
					buttonId = ShowErrMsgWindowWithBtn(title  ,testMessageY , OK_CANCEL);								

						if(buttonId == 3)
						{
							submitFlag=true;											
												
						}
					}
					else
					{
						submitFlag=true;;
					}
				}				
				
			}
			else
			{
				
				var title = '<s:text name="accountmonitor.confirm.title"/>';
				var testMessageY ='<s:text name="manualMatch.warning.messageforOnePositionLevel"/>';
				buttonId = ShowErrMsgWindowWithBtn(title  ,testMessageY , OK_CANCEL);
				
				if(buttonId == 3)
				{
					submitFlag=true;
					
				}
			}				
			
		}			
		
		if(submitFlag)
		{		
			lockMatchOnServer(process);
		}	
	
	}
	/*Code fix for Mantis 735 ends:*/	

}


function PredictStatusAmtTotalWarning()
{
	
	
	
	var table = document.getElementById("movementSummaryDetails");
	var tbody = table.getElementsByTagName("tbody")[0];
	var rows = tbody.getElementsByTagName("tr"); 
	
	//alert("Row Size -->" + rows.length);
	
	
	if(rows.length > 0)
	{
	
		var highestPosLvl = getPosLvl("movementSummaryDetails");
	
		//var predict = PosLvlCorrAmts(highestPosLvl);
		
		//var amt = PosLvlCorrPreductStatus(highestPosLvl);
			
	
		if(PosLvlCorrAmts(highestPosLvl))
		{

			alert("<s:text name="manualMatch.warning.messageForAmtTotalsOnLoad"/>");
		}

		if(PosLvlCorrPreductStatus(highestPosLvl))
		{

			alert("<s:text name="manualMatch.warning.messageForPredictStatusOnLoad"/>");

		}
		
		
		
	}
	
}






/*Mantis Issue		:	735*/
/*Subject 		:	warning mechanism  implementation to help prevent the
					user  on  	click of confirm / Suspend / Reconcile / 
					unmatch buttons*/
/*Description 		:	Previously in the manual match screen on click of confirm 
							/ suspend / unmatch / reconcile there was no warning 
							mechanism for the user 
							1.If there is a difference in the totals among the position levels and
							2.If more than one position level in the prospective match has 
							movements with PREDICT_STATUS�=�"I".							
							Now a warning mechanism is being implemented to avoid this situation.
							To fix that below code changes has been done*/
/* code modified by selva Kumar on 15/10/2008 code fix starts: */	



function PosLvlCorrPreductStatus(highestPosLvl)
{
	

	var prevPosLvl = "";
	var currPosLvl = "";				
	var predictStatus="";
	var predictStatusArray= new Array();
	
	var amtFlag = false;
	
	for(var i=0;i<highestPosLvl.length;i++ )
	{		

		currPosLvl = highestPosLvl[i];

		if(currPosLvl != prevPosLvl){						

			predictStatus = predictStatus + getPredictStatus("movementSummaryDetails",highestPosLvl[i])+",";		

		}
		prevPosLvl = currPosLvl;						
	}	

	predictStatus = predictStatus.substring(0,predictStatus.length-1);
	
	
	predictStatusArray = predictStatus.split(",");
	
	
	
	var predictI;	
	var Ivalue="E";
	
	
	for(var k=1;k<predictStatusArray.length;k++)
	{		
		
		
		if(predictStatusArray[k-1] == predictStatusArray[k])
		{			
			amtFlag=true;
			predictI = predictStatusArray[k];
			break;			
			
		}	
		
			
	}	
	
	
	if(amtFlag)
	{
		if(predictI.trim() != "E")
		{
			amtFlag=true;						
		}
		else
		{
			amtFlag=false;			
		}
		
	}
	
	
	if(amtFlag)
	{
		return true;	
	
	}	
	
	else
	{	
		return false;
	}	

}











function PosLvlCorrAmts(highestPosLvl){



	var amtTotals = new Array();
	var amtTotalstring = "";

	var prevPosLvl = "";
	var currPosLvl = "";
	
	var amtFlag=false;			
			
	for(var i=0;i<highestPosLvl.length;i++ )
	{		
		currPosLvl = highestPosLvl[i];	
		

		if(currPosLvl != prevPosLvl){
			amtTotalstring =   amtTotalstring + getPosLvlTotalAmount("movementSummaryDetails",highestPosLvl[i]) + ",";
		}
		prevPosLvl = currPosLvl;

	}			
			
	amtTotalstring = amtTotalstring.substring(0,amtTotalstring.length-1);
	amtTotals = amtTotalstring.split(",");

	for(var j=1;j<amtTotals.length;j++)
	{			
		if(amtTotals[j-1] != amtTotals[j]){			
			
			amtFlag=true;
			break;			
			
		}
	}
	
		if(amtFlag)
		{	
			return true;
		}
		else
		{
			return false;
		}
		
}
/*Code fix for Mantis 735 ends:*/





function getRowCount(){
	var table = document.getElementById("movementSummaryDetails"); 
	var tbody = table.getElementsByTagName("tbody")[0];    
	var rows = tbody.getElementsByTagName("tr");
	var count=rows.length
	return count;
}


function getAllMovementList(){
	 var table = document.getElementById("movementSummaryDetails"); 
	var tbody = table.getElementsByTagName("tbody")[0];    
	var rows = tbody.getElementsByTagName("tr");
	var movementList = "";
	var columns;
	
	for (i=0; i < rows.length; i++) 
	{
		movementList = movementList+rows[i].cells[14].innerText+",";
		
	 }  		
	 return movementList;
	
}

function openAddWindow(method,windowName,attr,isCascade)
{
    /* START: Code fixed to open the correct match queue from workflow monitor */
	document.forms[0].status.value = '${requestScope.status}';
	/* END: Code fixed to open the correct match queue from workflow monitor */
	var matchId=document.forms[0].elements["movement.matchId"].value;
	var entityId=document.forms[0].elements["movement.id.entityId"].value;
	var status=document.forms[0].elements["movementDetail.matchStatus"].value;
	
	var rowCount= getRowCount();

	/* Start:Mantis 886-Match operation not working for match that contains large number of movements
	* Code modified by saminathan at onsite on 26-Jan-2009
	*/
	var table = document.getElementById("movementSummaryDetails"); 
	var tbody = table.getElementsByTagName("tbody")[0];    
	var rows = tbody.getElementsByTagName("tr");
	var movementList = "";
	var length;
	var columns;
	var str;
	var  minValue=0;
	var maxValue=0;
	var count=1;//To check  whether  movement's list is huge.ie.more than 100.
	do
    {		
		if (rowCount-minValue>100)
		{
			maxValue=minValue+100;
		}     
		else
		{
			maxValue=rowCount;
		}		
		for (i=minValue; (i < maxValue &&  i < rowCount); i++) 
		{
		     movementList = movementList+rows[i].cells[14].innerText+",";
		}
		var oXMLHTTP = new XMLHttpRequest();
		var sURL = requestURL + appName+"/movementmatchdisplay.do?method=checkMatchStatus";
		sURL = sURL + "&matchId="+matchId;
		sURL = sURL + "&entityId="+entityId;
		sURL = sURL + "&status="+status;
		sURL = sURL + "&movementList="+movementList;
		sURL = sURL + "&rowCount="+rowCount;
		sURL = sURL + "&maxValue="+maxValue;
		sURL = sURL + "&count="+count;
		oXMLHTTP.open( "POST", sURL, false );
		oXMLHTTP.send();		
		str=new String(oXMLHTTP.responseText);				  			 	
		 minValue=maxValue;
		 movementList = "";
		 count++;
   }while(maxValue < rowCount);
    //End:Mantis 886-Match operation not working for match that contains large number of movements
	
	if(str != "true"){
		alert('<s:text name="tooltip.alertMnmtDislay"/>');
	}else{
				url = openAddScreen(method);
				openWindow(url,windowName,attr,isCascade);
		}
	
	
}


function lockMatchOnServer(process)
{
	
	var matchId=document.forms[0].elements["movement.matchId"].value;
	var entityId=document.forms[0].elements["movement.id.entityId"].value;
	var oXMLHTTP = new XMLHttpRequest();
	var sURL = requestURL + appName+"/movementLock.do?method=lockMatch";
//	var sURL = "http://localhost:8080/swallowtech/movementLock.do?method=lockMatch";

	sURL = sURL + "&matchId="+matchId;
	sURL = sURL + "&entityId="+entityId;

	oXMLHTTP.open( "POST", sURL, false );
	oXMLHTTP.send();
	var str=new String(oXMLHTTP.responseText);
	if(str != "true"){
		alert('<s:text name="tooltip.alertMatchAnotherProcess"/>');
	}else{

		//Start: Closing manual match screen and refreshing Ex-out screen when 'Unmatch' button is pressed 
				document.forms[0].currentPage.value =  '${requestScope.currentPage}';
			//End: Closing manual match screen and refreshing Ex-out screen when 'Unmatch' button is pressed 
		if(process == "unmatch"){
			
			showNextRecord('unmatch');
		}
		if(process == "confirm"){	
			showNextRecord('confirm');
		}
		if(process == "suspend"){
	
			showNextRecord('suspend');
		}if(process == "remove"){
			document.forms[0].submit();
		}
// start Added for Smart Predict II SRS Recon button
		if(process == "reconcile"){
	
			showNextRecord('reconcile');
		}
// end for Smart Predict II SRS Recon button
	}
	return str;
}


function lockMovementOnServer(movementId)
{
	var oXMLHTTP = new XMLHttpRequest();
	var sURL = requestURL + appName+"/movementLock.do?method=lockMovement";
	//var sURL = "http://localhost:8080/swallowtech/movementLock.do?method=lockMovement";
	sURL = sURL + "&movementId="+movementId;

	oXMLHTTP.open( "POST", sURL, false );
	oXMLHTTP.send();
	var str=new String(oXMLHTTP.responseText);
	if(str != "true"){
		alert('<s:text name = "alert.MovementInUse"/>'+str);
	}
	return str;
}

function unlockMovementOnServer(movementId)
{
	var oXMLHTTP = new XMLHttpRequest();
	var sURL = requestURL + appName+"/movementLock.do?method=unlockMovement";
	//var sURL = "http://localhost:8080/swallowtech/movementLock.do?method=unlockMovement";
	sURL = sURL + "&movementId="+movementId;

	oXMLHTTP.open( "POST", sURL, false );
	oXMLHTTP.send();
	var str=new String(oXMLHTTP.responseText);
	if(str != "true")
		alert('<s:text name ="alert.MvmUnlockSysAdm"/>');
	return str;
}
function onFilter(){
	unlockSelected();
	disableButtons();
	updateColors();
}

function disableButtons(){
	document.getElementById("mvmntbutton").innerHTML = document.getElementById("mvmntdisablebutton").innerHTML;
	document.getElementById("removebutton").innerHTML = document.getElementById("removedisablebutton").innerHTML;
}

function bodyOnLoad()
{	
	
	document.forms[0].matchList.value=matches;
	document.forms[0].currentIndex.value=currentIndex;	
	document.forms[0].matchType.value=manualMatch;
	document.forms[0].inputMethod.value=inptMethod;
	document.forms[0].day.value=day;
	document.forms[0].applyCurrencyThreshold.value = '${requestScope.applyCurrencyThreshold}';
	document.forms[0].dateTabInd.value = '${requestScope.dateTabInd}';
			
	 xl = new XLSheet("movementSummaryDetails","table_1", ["String", "Date", "String","String", "String","String","String", "String", , "String","Date", "String", "String", "String","Number", "String", "String", "String"],"112121112111222111",false);


	xl.onsort = xl.onfilter = onFilter;

	highlightMultiTableRows("movementSummaryDetails");
<s:if test='"movementDisplay"==#request.method' >
	document.getElementById("notesbutton").innerHTML = document.getElementById("notesenablebutton").innerHTML;
	document.getElementById("mvmntbutton").innerHTML = document.getElementById("mvmntdisablebutton").innerHTML;
</s:if>
<s:if test='"movementDisplay"!=#request.method' >
<s:if test='"manual"!=#request.match' >
<%if ( (SwtConstants.STR_TRUE).equals(request.getAttribute(SwtConstants.PREV_BUT_STS)) ) {%>
			document.getElementById("prevbutton").innerHTML = document.getElementById("prevenablebutton").innerHTML;
<%}%>

<%if ( !(SwtConstants.STR_TRUE).equals(request.getAttribute(SwtConstants.PREV_BUT_STS)) ) {%>
			document.getElementById("prevbutton").innerHTML = document.getElementById("prevdisablebutton").innerHTML;
<%}%>
	

<%if ( (SwtConstants.STR_TRUE).equals(request.getAttribute(SwtConstants.NXT_BUT_STS)) ) {%>
			document.getElementById("nextbutton").innerHTML = document.getElementById("nextenablebutton").innerHTML;
<%}%>

<%if ( !(SwtConstants.STR_TRUE).equals(request.getAttribute(SwtConstants.NXT_BUT_STS)) ) {%>
			document.getElementById("nextbutton").innerHTML = document.getElementById("nextdisablebutton").innerHTML;
<%}%>
</s:if>
</s:if>
	
<%if ( (SwtConstants.STR_TRUE).equals(request.getAttribute(SwtConstants.UNMATCH_BUT_STS)) ) {%>
		document.getElementById("unmatchbutton").innerHTML = document.getElementById("unmatchenablebutton").innerHTML;
<%}%>

<%if ( !(SwtConstants.STR_TRUE).equals(request.getAttribute(SwtConstants.UNMATCH_BUT_STS)) ) {%>
			document.getElementById("unmatchbutton").innerHTML = document.getElementById("unmatchdisablebutton").innerHTML;
<%}%>
	

<s:if test='"S"!=#request.movement.matchStatus' >
<%if ( (SwtConstants.STR_TRUE).equals(request.getAttribute(SwtConstants.SUSPND_BUT_STS)) ) {%>
			document.getElementById("suspendbutton").innerHTML = document.getElementById("suspendenablebutton").innerHTML;
<%}%>

<%if ( !(SwtConstants.STR_TRUE).equals(request.getAttribute(SwtConstants.SUSPND_BUT_STS)) ) {%>
			document.getElementById("suspendbutton").innerHTML = document.getElementById("suspenddisablebutton").innerHTML;
<%}%>

</s:if>

	
	
<s:if test='"C"!=#request.movement.matchStatus' >
<%if ( (SwtConstants.STR_TRUE).equals(request.getAttribute(SwtConstants.CONF_BUT_STS)) ) {%>
			document.getElementById("confirmbutton").innerHTML = document.getElementById("confirmenablebutton").innerHTML;
<%}%>

<%if ( !(SwtConstants.STR_TRUE).equals(request.getAttribute(SwtConstants.CONF_BUT_STS)) ) {%>
			document.getElementById("confirmbutton").innerHTML = document.getElementById("confirmdisablebutton").innerHTML;
<%}%>
		
	
</s:if>

<s:if test='"E"!=#request.movement.matchStatus' >
<%if ( (SwtConstants.STR_TRUE).equals(request.getAttribute(SwtConstants.RECON_BUT_STS)) ) {%>
			document.getElementById("reconbutton").innerHTML = document.getElementById("reconenablebutton").innerHTML;
<%}%>

<%if ( !(SwtConstants.STR_TRUE).equals(request.getAttribute(SwtConstants.RECON_BUT_STS)) ) {%>
			document.getElementById("reconbutton").innerHTML = document.getElementById("recondisablebutton").innerHTML;
<%}%>
</s:if>

<%if ( (SwtConstants.STR_TRUE).equals(request.getAttribute(SwtConstants.REMV_BUT_STS)) ) {%>
			document.getElementById("removebutton").innerHTML = document.getElementById("removeenablebutton").innerHTML;
<%}%>

<%if ( !(SwtConstants.STR_TRUE).equals(request.getAttribute(SwtConstants.REMV_BUT_STS)) ) {%>
			document.getElementById("removebutton").innerHTML = document.getElementById("removedisablebutton").innerHTML;
<%}%>
	
<%if ( (SwtConstants.STR_TRUE).equals(request.getAttribute(SwtConstants.ADD_BUT_STS)) ) {%>
			document.getElementById("addbutton").innerHTML = document.getElementById("addenablebutton").innerHTML;
<%}%>

<%if ( !(SwtConstants.STR_TRUE).equals(request.getAttribute(SwtConstants.ADD_BUT_STS)) ) {%>
			document.getElementById("addbutton").innerHTML = document.getElementById("adddisablebutton").innerHTML;
<%}%>

		 document.getElementById("mvmntbutton").innerHTML = document.getElementById("mvmntdisablebutton").innerHTML;

		 document.getElementById("logbutton").innerHTML = document.getElementById("logdisablebutton").innerHTML;
		 document.getElementById("notesbutton").innerHTML = document.getElementById("notesdisablebutton").innerHTML;

	if(document.forms[0].elements["movement.matchId"].value != null && !(document.forms[0].elements["movement.matchId"].value =="")) {
			document.getElementById("logbutton").innerHTML = document.getElementById("logenablebutton").innerHTML;

			document.getElementById("notesbutton").innerHTML = document.getElementById("notesenablebutton").innerHTML;



		 }
      
<s:if test='"close"==#request.parentFormRefresh1' >
	/* START: Method name change from displayListByCurrency to display by Mayank on 1/11/2008 */
	window.opener.document.forms[0].method.value="display";
	/* END: Method name modified Mayank on 1/11/2008 */
	/* START: Code fixed to open the correct match queue from workflow monitor */
	window.opener.document.forms[0].status.value = '${requestScope.status}';
    /* END: Code fixed to open the correct match queue from workflow monitor */
	window.opener.document.forms[0].submit();
	self.close();
</s:if>

<s:if test='"yes"==#request.parentFormRefresh' >
	window.opener.isRefresfFromParent = "true";
	//window.opener.document.forms[0].method.value="displayListByEntity";
	//window.opener.document.forms[0].submit();
</s:if>

<s:if test='"no"==#request.parentFormRefresh' >
/* Start: UAT defect sheet 23/11/2006  -- refreshing MovementDisplay screen if movement is removed from match */
if (window.opener.document.forms[0].elements["movement.id.movementIdAsString"] != null) {	
	  var movId =  window.opener.document.forms[0].elements["movement.id.movementIdAsString"].value;	 
		if (movId != null && movId != "") {
		window.opener.document.forms[0].selectedMovementId.value = movId;
		window.opener.document.forms[0].method.value = "displayMovement";
		window.opener.document.forms[0].buttonDiv.value = "movementDisplay";
		window.opener.document.forms[0].submit();
		self.close();
	 } 
 }else {
 /* End: UAT defect sheet 23/11/2006  -- refreshing MovementDisplay screen if movement is removed from match */
	/** Start: Closing manual match screen and refreshing Ex-out screen when 'Unmatch' button is pressed */
	//alert("closing window");
	/*window.opener.isRefresfFromChild = "true";
	window.opener.document.forms[0].method.value="refreshScreen";	
	//window.opener.document.forms[0].selectedTab.value = '${requestScope.selectedTab}';	
	window.opener.document.forms[0].submit();
	self.close();*/
	//alert("The current page isisisis"  +'${requestScope.currentPage}');
	if ('${requestScope.currentPage}' != null && '${requestScope.currentPage}' != "") {
	window.opener.isRefresfFromChild = "true";
	window.opener.document.forms[0].method.value="displayOpenMovements";	
	window.opener.document.forms[0].selectedList.value = "";	
	window.opener.document.forms[0].currentPage.value = '${requestScope.currentPage}';	
	window.opener.document.forms[0].refreshFromMatchScreen.value = 'Y';	
	window.opener.document.forms[0].tableScrollbarLeft.value = '${requestScope.tableScrollbarLeft}';
	window.opener.document.forms[0].tableScrollbarTop.value = '${requestScope.tableScrollbarTop}';
	window.opener.document.forms[0].scrollbarLeft.value = '${requestScope.scrollbarLeft}';
	window.opener.document.forms[0].scrollbarTop.value = '${requestScope.scrollbarTop}';
	window.opener.document.forms[0].submit();
	self.close();
	}
	/** End: Closing manual match screen and refreshing Ex-out screen when 'Unmatch' button is pressed */
}
</s:if>

var notesPresent = document.forms[0].elements["movementDetail.notes"].value;
if(notesPresent == "Y")
	{
		document.getElementById("notesIndicator").src =  "images/notes.gif";
		document.getElementById("notesIndicator").title = '<s:text name = "msg.title.notesAvailable"/>';
		document.forms[0].elements["movementDetail.notes"].value = "";
	}

    /* START: Code changed as par ING requirements to the Roles - Menu Access, 13-JUN-2007 */
	document.forms[0].menuAccessId.value = "${requestScope.menuAccessId}";
	/* END: Code changed as par ING requirements to the Roles - Menu Access, 13-JUN-2007 */

	/* START: Code changed as par SRS - Currency Threshold for ING, 09-JUL-2007 */
	document.forms[0].applyCurrencyThreshold.value = "${requestScope.applyCurrencyThreshold}";
document.forms[0].dateTabInd.value = "${requestScope.dateTabInd}";
	/* END: Code changed as par SRS - Currency Threshold for ING, 09-JUL-2007 */

	/* START: Code changed as par SRS - Match Exceptions for ING, 13-NOV-2007 */
	document.forms[0].noIncludedMovementMatches.value = "${requestScope.noIncludedMovementMatches}";
	/* END: Code changed as par SRS - Match Exceptions for ING, 13-NOV-2007 */
}

function closeWindow(){


<s:if test='"movementDisplay"!=#request.method' >
<s:if test='"manual"!=#request.match' >
		window.opener.setTabInfo();
	/* START: Method name change from displayListByCurrency to display by Mayank on 1/11/2008 */
	window.opener.document.forms[0].method.value="display";
	/* END: Method name modified Mayank on 1/11/2008 */

	/* START: Code fixed to open the correct match queue from workflow monitor */
	window.opener.document.forms[0].status.value = '${requestScope.status}';
    /* END: Code fixed to open the correct match queue from workflow monitor */
	window.opener.document.forms[0].submit();
</s:if>
</s:if>
	self.close();
}


function openAddScreen(methodName){
	var param='movementmatchdisplay.do?method='+methodName;
	param+='&entityId='+document.forms[0].elements["movement.id.entityId"].value;
	param+='&currencyCode='+document.forms[0].elements["movement.currencyCode"].value;
	
	//Start : Refer to Smart-Predict_SRS_Open_Movements_0.2.doc
	var isAmountDiffer = "N";
	
	var table = document.getElementById("movementSummaryDetails"); 
	 var tbody = table.getElementsByTagName("tbody")[0];    
	var rows = tbody.getElementsByTagName("tr");	
		
	var amt0 = rows[0].cells[2].innerText.trim();
	var sign0 = rows[0].cells[3].innerText.trim();

	 for (i=1; i < rows.length; i++){
		var amtTemp = rows[i].cells[2].innerText.trim();
		var signTemp = rows[i].cells[3].innerText.trim();
		if (amtTemp != amt0 || sign0 != signTemp) {
		isAmountDiffer = "Y";
		break;
		}
	 }
	
	param = param + "&isAmountDiffer="+isAmountDiffer;
	param = param + "&selectedMovementsAmount="+amt0+sign0;// appending absolute value of amount and its sign also
	//End : Refer to Smart-Predict_SRS_Open_Movements_0.2.doc

	return param;
}
function showNextRecord(methodName){
	
	//Subject		:Manually not Confirming a match while going from Offered  
	//Queues(Reported by Raghav thru chat)
	//Description	:While  manually  unmatching / confirm / suspend / reconcile  a match the selected 	match id should go to their respective  match status  coming from offered queues 
	// Code Modified on 28/11/2008 by SelvaKumar.A . Start of code	
	
	document.forms[0].matchId.value = document.forms[0].elements["movement.matchId"].value;
	//End of code
	
	/* START: Code fixed to open the correct match queue from workflow monitor */
	document.forms[0].status.value = '${requestScope.status}';
	/* END: Code fixed to open the correct match queue from workflow monitor */
	document.forms[0].applyCurrencyThreshold.value = '${requestScope.applyCurrencyThreshold}';
	document.forms[0].dateTabInd.value = '${requestScope.dateTabInd}';	
	unlockSelected();
	enableFields();
	document.forms[0].method.value = methodName;
	document.forms[0].matchType.value=manualMatch;
	document.forms[0].inputMethod.value=inptMethod;		
	document.forms[0].nextRecord.value="next";
	document.forms[0].submit();

}

function showPrevRecord(methodName){
	/* START: Code fixed to open the correct match queue from workflow monitor */
	document.forms[0].status.value = '${requestScope.status}';
	/* END: Code fixed to open the correct match queue from workflow monitor */
	unlockSelected();
	enableFields();
	document.forms[0].method.value = methodName;
	document.forms[0].nextRecord.value="previous";
	document.forms[0].submit();

}

function unlockSelected(){
	 var table = document.getElementById("movementSummaryDetails"); 
	// alert(table);
	 var tbody = table.getElementsByTagName("tbody")[0];    
	 var rows = tbody.getElementsByTagName("tr");
	 var selectedList = "";
	 var columns;

	 for (i=0; i < rows.length; i++) 
	 {
		if( isRowSelected(rows[i])){
			unlockMovementOnServer(rows[i].cells[14].innerText);
		//	alert("unlocked-->"+rows[i].cells[0].innerText);
		}
	 }  		
	 
	 //return selectedList;
}

function showMvmnt(methodName){
    /* START: Code changed as par ING requirements to the Roles - Menu Access, 14-JUN-2007 */
    /* START: Code modified by krishna on 13-oct-2010 for mantis 1217*/
    var menuAccessIdOfChildWindow = getMenuAccessIdOfChildWindow("Movement Display");
    /* END: Code modified by krishna on 13-oct-2010 for mantis 1217*
    
    var menuName = new String('<s:text name="mvmDisplay.title.window"/>');
	var smrtPredPos = menuName.search('<s:text name="alert.SmartPredict"/>');
	menuName = menuName.substr(0,smrtPredPos-3);
	if (menuAccessIdOfChildWindow == 2) {
	    alert('<s:text name="alert.AccessNotAvl"/>' + menuName + '<s:text name="alert.ContactSysAdm"/>');
	    //return false;
    } else {
        getSelectedMovementId(); 
        var param = 'movement.do?method='+methodName;
        param += '&entityCode=' + document.forms[0].elements['movement.id.entityId'].value;
        param += '&movementId=' + document.forms[0].selectedMovementId.value;
        /* START: Code changed as par ING requirements to the Roles - Menu Access, 13-JUN-2007 */
        param += '&menuAccessId=' + menuAccessIdOfChildWindow;
        /* END: Code changed as par ING requirements to the Roles - Menu Access, 13-JUN-2007 */
        //return param;
        openWindow(param,'movementWindow','left=50,top=190,width=998,height=653,toolbar=0, resizable=yes, scrollbars=yes','true');
	}

	return false;
	/* END: Code changed as par ING requirements to the Roles - Menu Access, 14-JUN-2007 */
}

function getSelectedMovementId(){
 var table = document.getElementById("movementSummaryDetails"); 
 var tbody = table.getElementsByTagName("tbody")[0];    
 var rows = tbody.getElementsByTagName("tr");
 var selectedList = "";
 var columns;
 for (i=0; i < rows.length; i++) 
 {
	if( isRowSelected(rows[i])){		
		document.forms[0].selectedMovementId.value = rows[i].cells[14].innerText;
	}
 }  		
 return selectedList;
}

function showManual(methodName)
{	
    /* START: Code changed as par ING requirements to the Roles - Menu Access, 14-JUN-2007 */
	document.forms[0].menuAccessId.value = "${requestScope.menuAccessId}";
	/* END: Code changed as par ING requirements to the Roles - Menu Access, 14-JUN-2007 */

	var obj = document.forms[0].elements["movement.matchId"];
	if(obj.value.length > 0 && (event.keyCode == 9 || event.keyCode == 13)){
		if( validateField(obj,'movement.matchId','numberPat'))
		{
			document.forms[0].method.value = methodName;
			document.forms[0].matchIds.value="'"+document.forms[0].elements["movement.matchId"].value+"'";
			document.forms[0].matchType.value="manual";
			document.forms[0].submit();
		}
	}
}

function enableFields(){
	document.forms[0].elements["movement.id.entityId"].disabled = "";
	document.forms[0].elements["movement.matchStatus"].disabled = "";

}

function removeMvnt(methodName){
	
	document.forms[0].matchType.value=manualMatch;
	document.forms[0].inputMethod.value=inptMethod;
	if(calculateRowsSelected()){

		document.forms[0].method.value = "unmatch";
		
		var yourstate=window.confirm('<s:text name = "alertRemovingDeletematch"/>');
		if (yourstate==true){ 
			lockMatchOnServer('remove');
			
		}
		
	}
	else{
		document.forms[0].method.value = methodName;
		
		document.forms[0].selectedList.value = getSelectedList();
		lockMatchOnServer('remove');
		
		
	}
}

function getSelectedList(){
 var table = document.getElementById("movementSummaryDetails"); 
 var tbody = table.getElementsByTagName("tbody")[0];    
 var rows = tbody.getElementsByTagName("tr");
 var selectedList = "";
 var columns;

 for (i=0; i < rows.length; i++) 
 {
	if( isRowSelected(rows[i])){
		selectedList = selectedList + "'"+rows[i].cells[14].innerText+"',";
	}
 }  		
 return selectedList;
}

function calculateRowsSelected(){
 var table = document.getElementById("movementSummaryDetails"); 
 var tbody = table.getElementsByTagName("tbody")[0];    
 var rows = tbody.getElementsByTagName("tr");
 var columns;
 var rowsSelected=0;
 for (i=0; i < rows.length; i++) 
 {
	if( isRowSelected(rows[i])){
		rowsSelected=rowsSelected+1;
	}
 }  		

 if(rowsSelected==(rows.length))
	{
	 return true;
	}
	else return false;
 
}

function onMultiSelectTableRow(rowElement,isSelected)
{
	

	var hiddenElement = rowElement.getElementsByTagName("input")[0];
	var movementId = hiddenElement.value;

<s:if test='"movementDispla"==#request.method' >
	if(isSelected){	
	unHighLightTableRow(rowElement);			
	}
	else{			
	highLightTableRow(rowElement);
	}
	var noOfRows=getCountRowsSelected(rowElement);
	
	/*if(noOfRows=="1"){
		document.getElementById("notesbutton").innerHTML = document.getElementById("notesenablebutton").innerHTML;
	}else{
		document.getElementById("notesbutton").innerHTML = document.getElementById("notesdisablebutton").innerHTML;
	}*/

	var noOfRows=getCountRowsSelected(rowElement);
	//alert(noOfRows);
	
	if(noOfRows=="1"){	
		document.getElementById("mvmntbutton").innerHTML = document.getElementById("mvmntenablebutton").innerHTML;
	}else{
		document.getElementById("mvmntbutton").innerHTML = document.getElementById("mvmntdisablebutton").innerHTML;
	}
</s:if>

	
	if(isSelected)
	{			
		// try to unlock the movement on server
		if(unlockMovementOnServer(movementId) == "true")
		{	
			// movement is unlocked , now unhighlight the row 
			
			unHighLightTableRow(rowElement);
		}
	}
	else
	{	
		// try to lock the movement on server
		if(lockMovementOnServer(movementId) == "true")
		{
			// movement is locked , now highlight the row 
			
			highLightTableRow(rowElement);
		}
	}
	var noOfRows=getCountRowsSelected(rowElement);
	
	if(noOfRows=="1"){		
		document.getElementById("mvmntbutton").innerHTML = document.getElementById("mvmntenablebutton").innerHTML;
	}else{
		document.getElementById("mvmntbutton").innerHTML = document.getElementById("mvmntdisablebutton").innerHTML;
	}

	if(noOfRows >="1"){				
		/* START: Code changed as par ING requirements to the Roles - Menu Access, 13-JUN-2007 */
		if(menuEntityCurrGrpAccess == "0") {
		/* END: Code changed as par ING requirements to the Roles - Menu Access, 13-JUN-2007 */
			document.getElementById("removebutton").innerHTML = document.getElementById("removeenablebutton").innerHTML;

<s:if test='"C"!=#request.movement.matchStatus' >
			document.getElementById("confirmbutton").innerHTML = document.getElementById("confirmdisablebutton").innerHTML;
</s:if>

<s:if test='"S"!=#request.movement.matchStatus' >
			document.getElementById("suspendbutton").innerHTML = document.getElementById("suspenddisablebutton").innerHTML;
</s:if>

<s:if test='"E"!=#request.movement.matchStatus' >
			document.getElementById("reconbutton").innerHTML = document.getElementById("recondisablebutton").innerHTML;
</s:if>

			document.getElementById("unmatchbutton").innerHTML = document.getElementById("unmatchdisablebutton").innerHTML;
			/* START: Code changed as par ING requirements to the Roles - Menu Access, 14-JUN-2007 */
			document.getElementById("logbutton").innerHTML = document.getElementById("logdisablebutton").innerHTML;
			document.getElementById("notesbutton").innerHTML =document.getElementById("notesdisablebutton").innerHTML;
			/* END: Code changed as par ING requirements to the Roles - Menu Access, 14-JUN-2007 */
		} 
		/* START: Code changed as par ING requirements to the Roles - Menu Access, 14-JUN-2007 */
		else {
			document.getElementById("logbutton").innerHTML = document.getElementById("logdisablebutton").innerHTML;
			document.getElementById("notesbutton").innerHTML =document.getElementById("notesdisablebutton").innerHTML;
		}
		/* END: Code changed as par ING requirements to the Roles - Menu Access, 14-JUN-2007 */
	} else{
		/* START: Code changed as par ING requirements to the Roles - Menu Access, 13-JUN-2007 */
		if(menuEntityCurrGrpAccess == "0") {
		    document.getElementById("removebutton").innerHTML = document.getElementById("removedisablebutton").innerHTML;

<s:if test='"C"!=#request.movement.matchStatus' >
		    document.getElementById("confirmbutton").innerHTML = document.getElementById("confirmenablebutton").innerHTML;
</s:if>

<s:if test='"S"!=#request.movement.matchStatus' >
		    document.getElementById("suspendbutton").innerHTML = document.getElementById("suspendenablebutton").innerHTML;
</s:if>

<s:if test='"E"!=#request.movement.matchStatus' >
		    document.getElementById("reconbutton").innerHTML = document.getElementById("reconenablebutton").innerHTML;
</s:if>

		    document.getElementById("unmatchbutton").innerHTML = document.getElementById("unmatchenablebutton").innerHTML;
            document.getElementById("logbutton").innerHTML = document.getElementById("logenablebutton").innerHTML;
            document.getElementById("notesbutton").innerHTML = document.getElementById("notesenablebutton").innerHTML;
		}
        else {
<s:if test='"C"!=#request.movement.matchStatus' >
		    document.getElementById("confirmbutton").innerHTML = document.getElementById("confirmdisablebutton").innerHTML;
</s:if>

<s:if test='"S"!=#request.movement.matchStatus' >
		    document.getElementById("suspendbutton").innerHTML = document.getElementById("suspenddisablebutton").innerHTML;
</s:if>

<s:if test='"E"!=#request.movement.matchStatus' >
		    document.getElementById("reconbutton").innerHTML = document.getElementById("recondisablebutton").innerHTML;
</s:if>

		    document.getElementById("unmatchbutton").innerHTML = document.getElementById("unmatchdisablebutton").innerHTML;
            document.getElementById("logbutton").innerHTML = document.getElementById("logenablebutton").innerHTML;
            document.getElementById("notesbutton").innerHTML = document.getElementById("notesenablebutton").innerHTML;
		}
        /* END: Code changed as par ING requirements to the Roles - Menu Access, 13-JUN-2007 */
	}
	
}

function showMatchNotes(methodName){
//alert("methodName " + methodName);
document.forms[0].selectedMatchId.value = document.forms[0].elements["movement.matchId"].value;
 var param = 'notes.do?method='+methodName+'&matchId=';			 
param += document.forms[0].selectedMatchId.value;
param += '&entityCode=' + document.forms[0].elements['movement.id.entityId'].value;
param += '&screenName=' + 'matchScreen'; 
//alert("param " + param);
return  param;
	
}


function refreshMatchNoteImage()
{ 
	
 var isNotesPresent = document.forms[0].isNotesPresent.value;
 var notesIndicator =  document.getElementById("notesIndicator");
 if(isNotesPresent == 'Y')
	{
		notesIndicator.src = "images/notes.gif"
		notesIndicator.title = '<s:text name = "msg.title.notesAvailable"/>';
	}

	if(isNotesPresent != 'Y')
	{
		notesIndicator.src = "images/blank.png"
		notesIndicator.title = "";
	}	
		
}

function matchLog(methodName) {
 var param = 'auditlog.do?method='+methodName;
	param += '&selectedMatchId=';
	param += document.forms[0].elements['movement.matchId'].value;
	/* Modified to fix the Match log screen issue.
	 * Status date for the selected Match is fetched and passed as a parameter to audit log 
	 * action for setting this as the from date to fetch the log details of the selected match
	 * on 03-07-2008 by Balaji.
	 */
	param += '&statusDate='+document.forms[0].elements['movement.updateDateAsString'].value;
	return param;

 }

/* Start: UAT Phase2Defects20061024_STL Defect No: 35 */

 function call3(){
	 unlockSelected();
	 call();
 }

/* End: UAT Phase2Defects20061024_STL Defect No: */


  <%-- Start: Code Edited for Mantis issue 0000813 by Thirumurugan  on 05-Dec-08  --%>
  function addMovement() {
	//unlockSelected();
	  var openwindow=true;
	  if(getSelectedList()=="") {
		var matchId=document.forms[0].elements["movement.matchId"].value;
		var entityId=document.forms[0].elements["movement.id.entityId"].value;
		var oXMLHTTP = new XMLHttpRequest();
		var sURL = requestURL + appName+"/movementLock.do?method=lockMatch";
		sURL = sURL + "&matchId="+matchId;
		sURL = sURL + "&entityId="+entityId;

		oXMLHTTP.open( "POST", sURL, false );
		oXMLHTTP.send();
		var str=new String(oXMLHTTP.responseText);
		if(str != "true"){
			alert('<s:text name="tooltip.alertMatchAnotherProcess"/>');
			openwindow=false;
		 }	
		}
     if(openwindow)
			openAddWindow('addMvmnt','currencymaintenanceaddWindow','left=50,top=190,width=290,height=110,toolbar=0, resizable=yes, scrollbars=yes','true')
		
	}
  <%-- End: Code Edited for Mantis issue 0000813 by Thirumurugan  on 05-Dec-08  --%>	
	

 /* Start: Code added for Mantis issue 0000719 by Kalidass G on 22-Sep-08 */
function exportData(exportType) {
	document.forms[0].method.value = 'exportMovementMatchScreen';
	document.forms[0].exportType.value = exportType;
	document.forms[0].entityId.value = document.forms[0].elements['movement.id.entityId'].value;
	document.forms[0].matchIds.value = document.forms[0].elements['movement.matchId'].value;
	document.forms[0].selectedMatchQuality.value = document.forms[0].elements['movementDetail.matchQuality'].value;
	document.forms[0].selectedCurrencyCode.value = document.forms[0].elements['movement.currencyCode'].value;
	document.forms[0].status.value = document.forms[0].elements['movementDetail.matchStatus'].value;
	document.forms[0].updateDate.value = document.forms[0].elements['movement.updateDateAsString'].value;
	document.forms[0].updateUser.value = document.forms[0].elements['movement.updateUser'].value;
	document.forms[0].posLevelInternal.value = document.forms[0].elements['movementDetail.posLevelInternal'].value;
	document.forms[0].posLevelExternal.value = document.forms[0].elements['movementDetail.posLevelExternal'].value;
	if( getRowCount() > 0) {
	document.forms[0].submit();
	/* Start:Vivekanandan:03-02-2009:Modified: setParentChildsFocus Method called to 
	   close the window when parent window is closed or application is log off For Mantis 891 */
	setParentChildsFocus();
	/* End:Vivekanandan:03-02-2009:Modified: setParentChildsFocus Method called to 
	   close the window when parent window is closed or application is log off For Mantis 891 */
	}
}	
 /* End: Code added for Mantis issue 0000719 by Kalidass G on 22-Sep-08 */
 
  <%-- Start: Code added for Mantis issue 0000813 by Thirumurugan  on 05-Dec-08  --%>
  function unlockallmovements(){
   if(getSelectedList()=="") {
		var table = document.getElementById("movementSummaryDetails"); 
		var tbody = table.getElementsByTagName("tbody")[0];    
		var rows = tbody.getElementsByTagName("tr");
		var MovementId;
		   for (i=0; i < rows.length; i++){ 
				MovementId= rows[i].cells[14].innerText.trim();
				unlockMovementOnServer(MovementId); 
			}	
		} 	
	 }  
    <%-- End: Code added for Mantis issue 0000813 by Thirumurugan  on 05-Dec-08  --%>

</script>

</head>
 
<body leftmargin="0" topmargin="0" marginheight="0" onLoad="bodyOnLoad();setParentChildsFocus();setFocus(document.forms[0]);ShowErrMsgWindow('${actionError}');PredictStatusAmtTotalWarning();" onUnload="call3();">
 
<s:form action="movementmatchdisplay.do">
		   
<!--Subject		:Manually not Confirming a match while going from Offered  -->
<!--  Queues(Reported by Raghav thru chat)-->
<!--  Description	:While  manually  unmatching / confirm / suspend / reconcile  a match the selected 	match id should go to their respective  match status  coming from offered queues -->
<!--  Code Modified on 28/11/2008 by SelvaKumar.A . Start of code -->   

<input name="matchId" type="hidden" value="">

<!-- End of code -->
<input name="method" type="hidden" value="">
<input name="matchList" type="hidden" value="">
<input name="currentIndex" type="hidden" value="">
<input name="selectedList" type="hidden" value="">
<input name="nextRecord" type="hidden" value="">
<input name="copiedMovementId" type="hidden" value="">
<input name="entityId" type="hidden" value="">
<input name="matchIds" type="hidden" value="">
<input name="matchType" type="hidden" value="">
<input name="selectedMovementId" type="hidden" value="">
<input name="selectedMatchId" type="hidden" value="">
<input name="day" type="hidden" value="">
<input name="movementList" type="hidden" value="">
<input name="rowCount" type="hidden" value="">
<input name="inputMethod" type="hidden" value="">
<input name="isNotesPresent" type="hidden" value="">
<!-- START: Code fixed to open the correct match queue from workflow monitor -->
<input name="status" type="hidden" value="">
<!-- END: Code fixed to open the correct match queue from workflow monitor -->

<input name="selectedtab" type="hidden" value='${selectedTab}'>

<!-- START: Code changed as par ING requirements to the Roles - Menu Access, 13-JUN-2007 -->
<input name="menuAccessId" type="hidden" >
<!-- END: Code changed as par ING requirements to the Roles - Menu Access, 13-JUN-2007 -->

<!-- Start: Closing manual match screen and refreshing Ex-out screen when 'Unmatch' button is pressed -->
<input name="currentPage" type="hidden" value="">
<!-- End: Closing manual match screen and refreshing Ex-out screen when 'Unmatch' button is pressed -->

<!-- START: Code changed as par SRS - Currency Threshold for ING, 09-JUL-2007 -->
<input name="applyCurrencyThreshold" type="hidden" >
<input name="dateTabInd" type="hidden" >
<!-- END: Code changed as par SRS - Currency Threshold for ING, 09-JUL-2007 -->

<!-- START: Code changed as par SRS - Match Exceptions for ING, 13-NOV-2007 -->
<input name="noIncludedMovementMatches" type="hidden" >
<!-- END: Code changed as par SRS - Match Exceptions for ING, 13-NOV-2007 -->

<!-- START: Code added for Mantis issue 0000719 by Kalidass G on 22-Sep-08 -->
<input name="selectedMatchQuality" type="hidden" value="">
<input name="selectedCurrencyCode" type="hidden" value="">
<input name="updateDate" type="hidden" value="">
<input name="updateUser" type="hidden" value="">
<input name="posLevelInternal" type="hidden" value="">
<input name="posLevelExternal" type="hidden" value="">
<input name="exportType" type="hidden" value="">
<s:if test='"manual"!=#request.match' >
	<input type="hidden" name="screen" id="exportDataScreen" value="MvmntMatchSummary_SmartPredict"/>
</s:if>

<s:if test='"manual"==#request.match' >
	<input type="hidden" name="screen" id="exportDataScreen" value="ManualMatch_SmartPredict"/>
</s:if>

<!-- END: Code added for Mantis issue 0000719 by Kalidass G on 22-Sep-08 -->

<div id="OfferedMatchQueue" style="position:absolute; left:20; top:20; width:1220; height:70px;border:2px outset; visibility:visible;">
<div id="matchsummary" style="position:absolute; left:8px; top:2px; width:1220px; height:200;">
<table>
<tr><td>
<table width="540" border="0" cellspacing="0" cellpadding="0" >
		<tr height="24">
		  <td width=90px" height="10px" align="left"><b><s:text name="matchQuality.entityId"/></b></td>
		  
		  <td height="10px" width="143px" align="left">
			<s:textfield tabindex="-1" cssClass="textlabelalpha" name="movement.id.entityId" style="width:140px;" readonly="true"  /> 
		  </td>
	<!--	  <td width="15%" height="10px" align="left"><b><s:text name="entity.entityName"/></b></td> -->
		   <td height="10px" width="307" align="left">
			<s:textfield tabindex="-1" cssClass="textlabelalpha" name="movementDetail.entityName" style="width:281px;" readonly="true" /> 
		  </td>

	</tr>
  </table>

<s:if test='"movementDisplay"!=#request.method' >
<s:if test='"manual"!=#request.match' >
    <table width="659" border="0" cellspacing="0" cellpadding="0">
		<tr height="24">
		  <td width="10%" height="10px"><b><s:text name="matchQuality.matchId"/></b></td>
		  <td height="12%" width="26" align="left">&nbsp;
		   </td>
		   <td height="10%" align="left">
			<s:textfield tabindex="-1"  cssClass="htmlTextNumeric" name="movement.matchId" style="width:120px;" readonly="true"  /> 
		
		   </td>		    
		    <!-- Start : Modified for increasing the Width displaying the total number of Matches . Mantis Issue 428 on 21-02-2008 -->
		    
		   <td height="10%" align="center" width="15%">
			<s:textfield tabindex="-1"  cssClass="textlabelalpha"  name="movementDetail.matchIndex" style="width:20px;text-align:right;color:green;" readonly="true" /><font color="green">/</font><s:textfield tabindex="-1"  cssClass="textlabelalpha"   name="movementDetail.totalMatches" style="width:50px;color:green;" readonly="true"   />
		   </td>
  		    <!--End   : Modified for increasing the Width displaying the total number of Matches . Mantis Issue 428  on 21-02-2008 -->
		    <td height="10%" align="left">
		   </td>
		   <td height="3%" align="left"><s:hidden name='movement.matchStatus' />
			<s:textfield tabindex="-1" cssClass="textlabelalpha" name="movementDetail.matchStatus" style="width:90px;color:green;"  /> 
		   </td>
		   <td height="1%" align="left">
			<s:textfield tabindex="-1" cssClass="textlabelalpha" name="movementDetail.matchQuality" style="width:10px;color:green;" readonly="true"  /> &nbsp;&nbsp;
     	   </td>
		   <td height="1%" align="left">
			<s:textfield tabindex="-1" cssClass="textlabelalpha" name="movement.updateDateAsString" style="width:80px;color:green;" readonly="true"  /> 
		   </td>
		   <td height="1%" align="left">
			<s:textfield tabindex="-1" cssClass="textlabelalpha" name="movement.updateUser" style="width:130px;color:green;" readonly="true"  /> 
           </td>
           <!--Start : Modified for increasing the Width displaying the total number of Matches . Mantis Issue 428 on 21-02-2008  -->
		   <td height="1%" align="left" width = "1px">
			<img id="notesIndicator" src='images/blank.png' border="0" title="">
		   </td>
		   <!--End   : Modified for increasing the Width displaying the total number of Matches . Mantis Issue 428  on 21-02-2008 -->
		   </tr>
	
	</table>
</s:if>
</s:if>

	
<s:if test='"manual"==#request.match' >
			<table width="659" border="0" cellspacing="0" cellpadding="0">
		<tr height="24">
		  <td width="10%" height="10px"><b><s:text name="matchQuality.matchId"/></b></td>
		  <td height="12%" width="26" align="left">&nbsp;
		   </td>
		   <td height="10%" width="140px" align="left">
<s:if test='"manual"==#request.match' >
				<s:textfield tabindex="1" cssClass="htmlTextNumeric" name="movement.matchId" titleKey="tooltip.enterMatchId" style="width:120px;" onkeydown="javascript:showManual('display');"  /> 
</s:if>
<s:if test='"manual"!=#request.match' >
				<s:textfield tabindex="-1"   cssClass="htmlTextNumeric"   name="movement.matchId" style="width:120px;" readonly="true"  /> 
</s:if>
		   </td>
		     <td height="10%" align="left">
		   </td>
		   <td height="3%" align="left"><s:hidden name='movement.matchStatus' />
			<s:textfield tabindex="-1" cssClass="textlabelalpha" name="movementDetail.matchStatus" style="width:90px;color:green;"  /> 
		   </td>
		   <td height="1%" align="left">
			<s:textfield tabindex="-1" cssClass="textlabelalpha" name="movementDetail.matchQuality" style="width:20px;color:green;" readonly="true"  /> &nbsp;&nbsp;
     	   </td>
		   <td height="1%" align="left">
			<s:textfield tabindex="-1" cssClass="textlabelalpha" name="movement.updateDateAsString" style="width:80px;color:green;" readonly="true"  /> 
		   </td>
		   <td height="1%" align="left">
			<s:textfield tabindex="-1" cssClass="textlabelalpha" name="movement.updateUser" style="width:130px;color:green;" readonly="true"  /> 
           </td>		   
		   <td height="1%" align="left" width = "30px">
			<img id="notesIndicator" src='images/blank.png' border="0" title="">
		   </td>
		   </tr>
	
	</table>
</s:if>

<s:if test='"movementDisplay"==#request.method' >
			<table width="659" border="0" cellspacing="0" cellpadding="0">
		<tr height="24">
		  <td width="10%" height="10px"><b><s:text name="matchQuality.matchId"/></b></td>
		  <td height="12%" width="26" align="left">&nbsp;
		   </td>
		   <td height="10%" align="left">
<s:if test='"manual"==#request.match' >
				<s:textfield tabindex="-1" cssClass="htmlTextNumeric"  name="movement.matchId" titleKey="tooltip.enterMatchId" style="width:120px;" onkeydown="javascript:showManual('display');" /> 
</s:if>
<s:if test='"manual"!=#request.match' >
				<s:textfield tabindex="-1"  cssClass="htmlTextNumeric"  name="movement.matchId" style="width:120px;" readonly="true"  /> 
</s:if>
		   </td>
		     <td height="10%" align="left">
		   </td>
		   <td height="3%" align="left"><s:hidden name='movement.matchStatus' />
			<s:textfield tabindex="-1" cssClass="textlabelalpha" name="movementDetail.matchStatus" style="width:90px;color:green;"  /> 
		   </td>
		   <td height="1%" align="left">
			<s:textfield tabindex="-1" cssClass="textlabelalpha" name="movementDetail.matchQuality" style="width:20px;color:green;" readonly="true"  /> &nbsp;&nbsp;
     	   </td>
		   <td height="1%" align="left">
			<s:textfield tabindex="-1" cssClass="textlabelalpha" name="movement.updateDateAsString" style="width:80px;color:green;" readonly="true"  /> 
		   </td>
		   <td height="1%" align="left">
			<s:textfield tabindex="-1" cssClass="textlabelalpha" name="movement.updateUser" style="width:130px;color:green;" readonly="true"  /> 
           </td>
		   <td height="1%" align="left" width = "30px">
			<img id="notesIndicator" src='images/blank.png' border="0" title="">
		   </td>
		   </tr>
	
	</table>
</s:if>

	<table width="640" border="0" cellspacing="0" cellpadding="0" >
		
			<tr>
		  <td width="88px" height="10px" align="left"><b>Currency</b>&nbsp;&nbsp;&nbsp;</td>
		   <td height="10px" width="492px" align="left" colspan="2">
			<s:textfield tabindex="-1" cssClass="textlabelalpha" name="movement.currencyCode" style="width:120px;" readonly="true" /> 
		  </td>
		  <td height="1%" align="right">
			 <s:textfield tabindex="-1" cssClass="textlabelalpha" name="movementDetail.notes" style="width:160px;color:green;text-align:right;" readonly="true"  />
           </td>
		   </tr>
		  
  </table>
  </td>
				</tr></table>

   <!--   -->
<div id="MovementDisplay" style="position:absolute;left:815px; top:0px; width:100px; height:180px;">
	<td align = "right" ><fieldset style="border:2px groove;"><legend><s:text name="matchQuality.posTotal"/></legend>
			<table width="100px" border="0" cellspacing="0" cellpadding="0" >
			<tr height="24">
				<td width="17%" height="10px" align="left">&nbsp;&nbsp;<b><s:text name="matchQuality.posTotalInternal"/>&nbsp;&nbsp;</td>
				<td height="10px" width="5%" align="right">
				<s:textfield tabindex="-1" cssClass="textlabel1" name="movementDetail.posLevelInternal" style="width:50px;" readonly="true"  /> 
				
				</td>
				<td>&nbsp;</td>
				
			</tr>

			<tr height="24">
				<td width="17%" height="10px" align="left">&nbsp;&nbsp;<b><s:text name="matchQuality.posTotalExternal"/>&nbsp;&nbsp;</td>
				<td height="10px" width="5%" align="right">
				<s:textfield tabindex="-1" cssClass="textlabel1" name="movementDetail.posLevelExternal" style="width:50px;" readonly="true"  /> 
				
				</td>
				<td>&nbsp;</td>
			</tr>

			</table>
				</fieldset></td>
</div>

  </div>
  </div>

<div id="MatchSummaryParent" color="#7E97AF" style="position:absolute; border:2px outset; left:20px; top:95px; width:1220px; height:405px;">
<div id="MatchSummary" style="position:absolute;z-index:99;left:0px; top:0px; width:1198px; height:10px;overflowX:scroll;">
<table  class="sort-table" id="table_1" bgcolor="#B0AFAF" width="2236" border="0" cellspacing="1" cellpadding="0"  height="23">
	<thead>
		<tr>
			<td  height="23px"  width="150" title='<s:text name="tooltip.sortPositionLevel"/>' align="left"><b><s:text name="movement.pos1"/></b></td>
			<td height="23px" width="82" title='<s:text name="tooltip.sortValueDate"/>' align="left"><b><s:text name="movement.date"/></b></td>
			<td height="23px"  width="165" title='<s:text name="tooltip.sortAmount"/>' align="left"><b><s:text name="movement.amount1"/></b></td>
			<td height="23px"  width="76" title='<s:text name="tooltip.signField"/>' align="left"><b><s:text name="movement.sign"/></b></td>
			<td  height="23px" width="152"  title='<s:text name="tooltip.referenceField"/>' align="left"><b><s:text name="movement.newreference1"/></b></td>
			<td  height="23px" width="120" title='<s:text name="tooltip.accountField"/>' align="left"><b><s:text name="movement.account"/></b></td>
			<td height="23px"  width="120" title='<s:text name="tooltip.partyField"/>' align="left"><b><s:text name="movement.counterPartyId"/></b></td>
			<td  height="23px" width="80" title='<s:text name="tooltip.predictField"/>' align="left"><b><s:text name="movement.pred"/></b></td>
			<td width="83px"  title="Sort by notes"><b><s:text name="movement.notes"/></b></td>

			<td width="130px" title='<s:text name="tooltip.sortInputSource"/>' align="left"><b><s:text name="positionLevel.id.inputSource"/></b></td>

			<td height="23px" width="82" title='<s:text name="tooltip.sortByInputDate"/>' align="left"><b><s:text name="entity.predict.retentionParam.input"/></b></td>
			
			<td width="120px"  title='<s:text name="tooltip.sortbybeneficiaryid"/>' align="centre"><b><s:text name="movement.beneficiary"/></b></td>
			<td width="143px"  title='<s:text name="tooltip.sortbyreference2"/>' align="center"><b><s:text name="movement.reference2"/></b></td>
			<td width="143px"  title='<s:text name="tooltip.sortbyreference3"/>' align="center"><b><s:text name="movement.reference3"/></b></td>	
			

			<td  height="23px"  width="120" title='<s:text name="tooltip.sortMvmId"/>' align="center"><b><s:text name="movement.movementId"/></b></td>
			
			<td  height="23px"  width="120" title='<s:text name="tooltip.sortBookcode"/>' align="center"><b><s:text name="book.bookCode"/></b></td>
				<td width="120px" title='<s:text name="tooltip.sortbycustodianid"/>' align="center"><b><s:text name="movement.custodian"/></b></td>
			
		</tr>
	</thead>
</table>
</div>

<div id="ddscrolltable"  style="position:absolute; left:0px; top:1px; width:1215px; height:400px;overflow:scroll">
<div id="MatchSummary" style="position:absolute;z-index:99;left:1px; top:22px; width:1198px; height:10px;">
<table class="sort-table" id="movementSummaryDetails" width="2236" border="0" cellspacing="1" cellpadding="0" height="360">
		
	<tbody> 	
	
	<% int count = 0; %>  
	<s:iterator name='#request.movementSummaryDetails' var='movementSummaryDetails' />  
	
		<% if( count%2 == 0 ) {%><tr height="20px" class="even"><% }else  { %> <tr height="21px" class="odd"> <%}++count; %>
		
			<s:hidden name='movementSummaryDetails.id.movementId' />
			<td  align="left" width="150"><s:property name='#request.movementSummaryDetails.positionLevelName' />&nbsp;</td>
			
			<s:hidden name='movementSummaryDetails.positionLevel' />

			<!-- Start: Refer toSmart-Predict_SRS_Open_Movements_0.2.doc -->
			<!-- <td align="center"  width="82"><s:property name='#request.movementSummaryDetails.valueDateAsString' />&nbsp;</td> -->
<s:if test='"Y"==#movementSummaryDetails.openFlag' >
			<td align="center" style="color:red"  width="82"><s:property name='#request.movementSummaryDetails.valueDateAsString' />&nbsp;</td>
</s:if>

<s:if test='"Y"!=#movementSummaryDetails.openFlag' >
			<td align="center"  width="82"><s:property name='#request.movementSummaryDetails.valueDateAsString' />&nbsp;</td>
</s:if>
			<!-- End: Refer toSmart-Predict_SRS_Open_Movements_0.2.doc -->

			<!-- Start: Vivekanandan:06/09/2008:added code for Mantis issue 0000719 to display the amounts in red color if sign is D -->
<s:if test='"D"==#movementSummaryDetails.sign' >
				<td align="right" style = "color:red" width="165"><s:property name='#request.movementSummaryDetails.amountAsString' />&nbsp;</td>
</s:if>
<s:if test='"C"==#movementSummaryDetails.sign' >
				<td align="right" width="165"><s:property name='#request.movementSummaryDetails.amountAsString' />&nbsp;</td>
</s:if>
			<!-- End: Vivekanandan:06/09/2008:added code for Mantis issue 0000719 to display the amounts in red color if sign is D -->
			<td width="76" align="center"><s:property name='#request.movementSummaryDetails.sign' />&nbsp;</td>			
	
			<td width="152"><s:property name='#request.movementSummaryDetails.reference1' />&nbsp;</td>
			<td  width="120"><s:property name='#request.movementSummaryDetails.accountId' />&nbsp;</td>
			<td  width="120"><s:property name='#request.movementSummaryDetails.counterPartyId' />&nbsp;</td>
			<td width="80" align="center"><s:property name='#request.movementSummaryDetails.predictStatus' />&nbsp;</td>
			<td width="83px" align="center"><s:property name='#request.movementSummaryDetails.noOfNotesAttached' /></td>
			<td width="130px" align="center"><s:property name='#request.movementSummaryDetails.inputSource' /></td>

			<td align="center"  width="162"><s:property name='#request.movementSummaryDetails.inputDateAsString' />&nbsp;</td>

			<td width="120px"><s:property name='#request.movementSummaryDetails.beneficiaryId' />&nbsp;</td>
			<td width="143px"><s:property name='#request.movementSummaryDetails.reference2' />&nbsp;</td>
			<td width="143px"><s:property name='#request.movementSummaryDetails.reference3' />&nbsp;</td>

			<td align="right"  width="120"><s:property name='#request.movementSummaryDetails.id.movementId' />&nbsp;</td>

			
			
			<td  width="120"><s:property name='#request.movementSummaryDetails.bookCode' />&nbsp;</td>
		
			<td width="120px"><s:property name='#request.movementSummaryDetails.custodianId' />&nbsp;</td>
			
		</tr>
</s:iterator>  
	</tbody>
	<tfoot><tr><td colspan="18" ></td></tr></tfoot>
</table>
</div></div>
</div>

<div id="MovementMatch" style="position:absolute; left:1150; top:515; width:70; height:15px; visibility:visible;">
	<table width="60" border="0" cellspacing="0" cellpadding="0" height="20">
		<tr>
		<!-- START: Code added for Mantis issue 0000719 by Kalidass G on 22-Sep-08 -->
		<td align="Right">
				<a title='<s:text name="tooltip.exportErrors_csv"/>' tabindex="7" href=# onclick="exportData('csv');" 
				onMouseOver="MM_swapImage('csv','','images/csvOver.jpg',1)" 
				onMouseDown="MM_swapImage('csv','','images/csvDown.jpg',1)"
				onMouseOut="MM_swapImage('csv','','images/csvUp.jpg',1)">
				<img src="images/csvUp.jpg" name="csv"  border="0" >
				</a> 
	    </td>
		  <td width="28px">&nbsp;</td>
		   <td align="Right">
				<a title='<s:text name="tooltip.exportErrors_excel"/>' tabindex="7" href=# onclick="exportData('excel');" 
				onMouseOver="MM_swapImage('excel','','images/excelOver.jpg',1)" 
				onMouseDown="MM_swapImage('excel','','images/excelDown.jpg',1)"
				onMouseOut="MM_swapImage('excel','','images/excelUp.jpg',1)">
				<img src="images/excelUp.jpg" name="excel"  border="0" >
				</a> 
		  </td>
		  <td width="28px">&nbsp;</td>
		  <td align="Right">
				<a title='<s:text name="tooltip.exportErrors_pdf"/>' tabindex="7" href=# onclick="exportData('pdf');" 
				onMouseOver="MM_swapImage('pdf','','images/pdfOver.jpg',1)" 
				onMouseDown="MM_swapImage('pdf','','images/pdfDown.jpg',1)"
				onMouseOut="MM_swapImage('pdf','','images/pdfUp.jpg',1)">
				<img src="images/pdfUp.jpg" name="pdf"  border="0" >
				</a> 
		  </td>
		  <!-- END: Code added for Mantis issue 0000719 by Kalidass G on 22-Sep-08 -->
		<td align="Right">
				<a  tabindex="13" href=# onclick="javascript:openWindow(buildPrintURL('print','Movement Match Summary Display '),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')" onMouseOut="MM_swapImgRestore()" onMouseOver="MM_swapImage('Help','','images/help_default.GIF ',1)"><img src="images/help_default.GIF " name="Help"  border="0" ></a> 
	    </td>
	    <!-- START: Commented the code for Mantis issue 0000719 to export the data in defined format by Kalidass G on 22-Sep-08 -->
	   <!--	<td align="right" id="Print">
				<a tabindex="14" onclick="printPage();" onMouseOut="MM_swapImgRestore()" onMouseOver="MM_swapImage('Print','','images/Print_R.gif ',1)"><img src="images/Print.gif " name="Print"  border="0" title='<s:text name="tooltip.printScreen"/>'</a>	
		</td> -->
		<!-- END: Commented the code for Mantis issue 0000719 by Kalidass G on 22-Sep-08 -->	
		</tr>
	</table>
</div>

<div id="ddimagebuttons" color="#7E97AF" style="position:absolute; border:2px outset; left:20; top:505; width:1220; height:39px; visibility:visible;">
 
  <div id="Currency" style="position:absolute; left:6; top:4; width:970; height:15px; visibility:visible;">
  	
<s:if test='"M"!=#request.movement.matchStatus' >
  	  <table width="770" border="0" cellspacing="0" cellpadding="0" height="20">
</s:if>
<s:if test='"M"==#request.movement.matchStatus' >
	  <table width="830" border="0" cellspacing="0" cellpadding="0" height="20">
</s:if>
<s:if test='"manual"==#request.match' >
<s:if test='"M"!=#request.movement.matchStatus' >
<s:if test='""!=#request.movement.matchStatus' >
			<table width="630" border="0" cellspacing="0" cellpadding="0" height="20">
</s:if>
<s:if test='""==#request.movement.matchStatus' >
  			<table width="700" border="0" cellspacing="0" cellpadding="0" height="20">
</s:if>
</s:if>
<s:if test='"M"==#request.movement.matchStatus' >
			 <table width="700" border="0" cellspacing="0" cellpadding="0" height="20">
</s:if>
</s:if>
<s:if test='"movementDisplay"==#request.method' >
	<table width="700" border="0" cellspacing="0" cellpadding="0" height="20">
</s:if>
		<tr>
<s:if test='"movementDisplay"!=#request.match' >
<s:if test='"manual"!=#request.match' >
			<td id="prevbutton">		
			</td>
			<td id="nextbutton">		
			</td>
</s:if>
</s:if>
			
			<td id="notesbutton">		
			</td>
			
			
			<td id="logbutton">
			</td>
			
			<td id="mvmntbutton">
			</td>
			<td id="unmatchbutton">		
			</td>
			<td id="suspendbutton">		
			</td>
			<td id="confirmbutton">		
			</td>
			<td id="reconbutton">		
			</td>
			<td id="removebutton">		
			</td>
			<td id="addbutton">		
			</td>
			<td id="closebutton">		
				<a tabindex="12" title='<s:text name="tooltip.close"/>'  onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onclick="javascript:unlockSelected();closeWindow();"><s:text name="button.close"/></a>			
			</td>
			<td id="nobutton" >		
			</td>
			
		</tr>
		</table>
	</div>

	
<div style="position:absolute; left:6; top:0; width:290; height:15px; visibility:hidden;">  	
   
    <table width="280" border="0" cellspacing="0" cellpadding="0" height="20" style="visibility:hidden">
	<tr>
		<td id="prevenablebutton" width="70">		
		<a tabindex="1" title='<s:text name="tooltip.previousMatch"/>' onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onClick="javascript:showPrevRecord('next')"><s:text name="button.previous"/></a>
		</td>		
		<td id="prevdisablebutton" width="70">
			<a  class="disabled" disabled="disabled"><s:text name="button.previous"/></a>
		</td>
		<td id="nextenablebutton" width="70">		
		<a tabindex="2" title='<s:text name="tooltip.nextMatch"/>'  onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onClick="javascript:showNextRecord('next')"><s:text name="button.next"/></a>
		</td>		
		<td id="nextdisablebutton" width="70">
			<a  class="disabled" disabled="disabled"><s:text name="button.next"/></a>
		</td>
		<td id="notesdisablebutton">
				<a  class="disabled" disabled="disabled"><s:text name="button.notes"/></a>
		</td>

	<td id="notesenablebutton">		
	<a tabindex="3" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onClick="javascript:openWindow(showMatchNotes('showMatchNotes'),'matchNotesWindow','left=170,top=210,width=588,height=272,toolbar=0, resizable=yes, scrollbars=yes')" title='<s:text name="tooltip.matchNotes"/>' ><s:text name="button.notes"/></a>					
			</td>
		<td id = "logenablebutton">
			
			<a tabindex="4" title='<s:text name="tooltip.logSelMvm"/>' onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onClick="javascript:openWindow(matchLog('matchLog'),'matchLogWindow','left=50,top=190,width=615,height=565,toolbar=0, resizable=yes, scrollbars=yes','true')"><s:text name="button.log"/></a>	
		</td>

		<td id="logdisablebutton" width="70">
			<a  class="disabled" disabled="disabled"><s:text name="button.log"/></a>
		</td>



		<td id="mvmntenablebutton" width="70">		
		<a tabindex="5" title='<s:text name="tooltip.showSelMovDetail"/>'  onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onClick="showMvmnt('showMovementDetails')";><s:text name="button.mvmnt"/></a>		
		</td>		
		<td id="mvmntdisablebutton" width="70">
			<a  class="disabled" disabled="disabled"><s:text name="button.mvmnt"/></a>
		</td>
</tr><tr>
		<td   id="suspendenablebutton" width="70">		
			<a tabindex="7" title='<s:text name="tooltip.suspMatch"/>' onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onClick="javascript:checkStatus('suspend');"><s:text name="button.suspend"/></a>
		</td>	
		
		<td id="suspenddisablebutton" width="70">
			<a  class="disabled" disabled="disabled"><s:text name="button.suspend"/></a>
		</td>
	
		<td id="unmatchenablebutton" width="70">		
			<a tabindex="6" title='<s:text name="tooltip.unMatch"/>'  onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onClick="javascript:checkStatus('unmatch');"><s:text name="button.unmatch"/></a>
		</td>
		<td id="unmatchdisablebutton" width="70">
			<a  class="disabled" disabled="disabled"><s:text name="button.unmatch"/></a>
		</td>
		
		<td id="confirmenablebutton" width="70">		
			<a  tabindex="8" title='<s:text name="tooltip.ConfMatch"/>' onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onClick="javascript:checkStatus('confirm')"><s:text name="button.confirm"/></a>
		</td>		
		<td id="confirmdisablebutton" width="70">
			<a  class="disabled" disabled="disabled"><s:text name="button.confirm"/></a>
		</td>
		<td id="reconenablebutton" width="70">		
			<a  tabindex="9" title='<s:text name="tooltip.reconMatch"/>' onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onClick="javascript:checkStatus('reconcile')"><s:text name="button.reconcile"/></a>
		</td>		
		<td id="recondisablebutton" width="70">
			<a  class="disabled" disabled="disabled"><s:text name="button.reconcile"/></a>
		</td>
		<td  id="removeenablebutton" width="70">		
		<a tabindex="10" title='<s:text name="tooltip.removeSelMov"/>'  onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onClick="javascript:checkStatus('remove')"><s:text name="button.remove"/></a>
		</td>		
		<td id="removedisablebutton" width="70">
			<a  class="disabled" disabled="disabled"><s:text name="button.remove"/></a>
		</td>

		<td id="addenablebutton" width="70">		
		<a tabindex="11" title='<s:text name="tooltip.addMov"/>'  onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onClick="addMovement();"><s:text name="button.add"/></a>
		</td>		
		<td id="adddisablebutton" width="70">
			<a  class="disabled" disabled="disabled"><s:text name="button.add"/></a>
		</td>
		<td id="noenablebutton" width="70" >&nbsp;&nbsp;&nbsp;		
			</td>
		
	</tr>
    </table>
   
  </div>
  
<s:if test='"movementDispla"==#request.method' >
 <div id="Currency" style="position:absolute; left:6; top:4; width:150px; height:15px; visibility:visible;">
  	
  	  <table width="280px" border="0" cellspacing="0" cellpadding="0" height="20">
		<tr>			
			<td id="notesbutton">		
				<a  title='<s:text name="tooltip.matchNotes"/>'  onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" ><s:text name="button.notes"/></a>			
			</td>

			<td id="logbutton">
			</td>

			<td id="mvmntbutton">
			</td>
			<td id="closebutton">		
				<a  title='<s:text name="tooltip.close"/>' onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onclick="javascript:unlockSelected();closeWindow();"><s:text name="button.close"/></a>			
			</td>			
			
		</tr>
		</table>
	</div>
<div style="position:absolute; left:6; top:4; width:80px; height:15px; visibility:hidden;">  	
   
    <table width="70" border="0" cellspacing="0" cellpadding="0" height="20" style="visibility:hidden">
	<tr>
		<td id="notesenablebutton">		
		<a tabindex="3"  onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onClick="javascript:openWindow(showMatchNotes('showMatchNotes'),'matchNotesWindow','left=170,top=210,width=588,height=272,toolbar=0, resizable=yes, scrollbars=yes')" title='<s:text name="tooltip.matchNotes"/>'><s:text name="button.notes"/></a>
		</td>	
		
		<td id = "logenablebutton">
			<a tabindex="51" title='<s:text name="tooltip.logSelMvm"/>' onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onClick="javascript:openWindow(matchLog('matchLog'),'matchLogWindow','left=50,top=190,width=615,height=565,toolbar=0, resizable=yes, scrollbars=yes','true')"><s:text name="button.log"/></a>	
		</td>

		<td id="logdisablebutton" width="70">
			<a  class="disabled" disabled="disabled"><s:text name="button.log"/></a>
		</td>

		<td id="notesdisablebutton">
			<a  class="disabled" disabled="disabled"><s:text name="button.notes"/></a>
		</td>
		<td id="mvmntenablebutton" width="70">		
		<a tabindex="4" title='<s:text name="tooltip.showSelMovDetail"/>'  onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onClick="showMvmnt('showMovementDetails')";><s:text name="button.mvmnt"/></a>		
		</td>		
		<td id="mvmntdisablebutton" width="70">
			<a  class="disabled" disabled="disabled"><s:text name="button.mvmnt"/></a>
		</td>
	</tr>
    </table>
</div>
</s:if>
</div>
<blockquote>&nbsp;</blockquote>
<p>&nbsp;</p>
	
</s:form>
<script>
<s:if test='"offeredMatch"==#request.inputFrom' >

//	alert("in input ");

//	alert("window.opener.isRefresfFromChild - " + window.opener.isRefresfFromChild);
	window.opener.isRefresfFromChild = "true";
//	alert("window.opener.isRefresfFromChild - " + window.opener.isRefresfFromChild);


	window.opener.document.forms[0].method.value="displayOpenMovements";	
	//window.opener.document.forms[0].selectedTab.value = selectedtab;
//	window.opener.document.forms[0].refreshScreen.value = "true";
	window.opener.document.forms[0].selectedList.value = "";
	
	/* Start: Refer to mail From: Antony Harris Sent: Thursday, March 29, 2007 2:59 PM
        To: Singh, Mantosh   Subject: DEFECTS/ENHANCEMENTS: Excluded Outstandings */
	window.opener.document.forms[0].currentPage.value = '${requestScope.currentPage}';	
	window.opener.document.forms[0].refreshFromMatchScreen.value = 'Y';	
	window.opener.document.forms[0].tableScrollbarLeft.value = '${requestScope.tableScrollbarLeft}';
	window.opener.document.forms[0].tableScrollbarTop.value = '${requestScope.tableScrollbarTop}';
	window.opener.document.forms[0].scrollbarLeft.value = '${requestScope.scrollbarLeft}';
	window.opener.document.forms[0].scrollbarTop.value = '${requestScope.scrollbarTop}';
	/* End: Refer to mail From: Antony Harris Sent: Thursday, March 29, 2007 2:59 PM
        To: Singh, Mantosh   Subject: DEFECTS/ENHANCEMENTS: Excluded Outstandings*/
	
	window.opener.document.forms[0].submit();
</s:if>
</script>
</body>
</html>