<?xml version="1.0" encoding="UTF-8"?>
<%@ page contentType="text/xml" %>
<%@ taglib prefix="s" uri="/struts-tags" %>
<request_reply>
	<status_ok><s:property value="#request.reply_status_ok" /></status_ok>
	<message><s:property value="#request.reply_message" /></message>
	<location><s:property value="#request.reply_location" /></location>
	
	<s:if test="#request.opTimes != null">
    <timing>
        <s:iterator value="#request.opTimes" var="opTime">
            <operation id="${opTime.key}"><s:property value="#opTime.value" /></operation>
        </s:iterator>
    </timing>
	</s:if>
</request_reply>