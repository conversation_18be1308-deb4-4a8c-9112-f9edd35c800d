<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@ taglib uri="http://java.sun.com/jstl/fmt_rt" prefix="fmt" %>
<%
	response.setHeader("Pragma","no-cache"); //HTTP 1.0
	response.setHeader("Cache-Control","no-cache"); //HTTP 1.1
	// Deny Cross Frame scripting (security issue)
	response.setHeader("X-Frame-Options","DENY");
	// Content Sniffing should be disabled (security issue)
	response.setHeader("X-Content-Type-Options","nosniff");
	response.setDateHeader ("Expires", 0); //prevents caching at the proxy server
%>
<html>
<head>

<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">

<script type="text/javascript" src="../js/commonJS.js"></script>


<script>
   var inputArg ;
   inputArg = dialogArguments;
   var paramArr = inputArg.split(";");
   if(paramArr.length > 1)
   {
   	document.write("<TITLE>" + paramArr[0] + "</TITLE>");
   }else
   {
   	document.write("<TITLE>Microsoft Internet Explorer</TITLE>");
   }
</script>


<SCRIPT language="JavaScript">

function closewin(retVal)
{	
   window.returnValue = retVal;   
   window.close();
}
function changetext()
{
    var paramArr = inputArg.split(";");
	if(paramArr.length > 1)
	{
		var title = paramArr[0];
		var errmsg = paramArr[1];
		var btnMenu = paramArr[2];
		<%-- START: variable added for Mantis 864(Authorize sweep Enhancement) by Thirumurugan on 12-Jan-2009 --%> 
		var authQueue = paramArr[3];
        <%-- END: variable added for Mantis 864(Authorize sweep Enhancement) by Thirumurugan on 12-Jan-2009 --%>

		document.getElementById("errMessage").innerHTML = errmsg;
		if(btnMenu == YES_NO )
		{
					  <%-- START: Code added for Mantis 864(Authorize sweep Enhancement) by Thirumurugan on 12-Jan-2009 --%> 
			 if(authQueue !=null) {
			   document.getElementById('divYesNo').style.top='110px'
			 }
		  <%-- END: Code added for Mantis 864(Authorize sweep Enhancement) by Thirumurugan on 12-Jan-2009 --%>  
			document.getElementById('divOK').style.visibility = 'hidden';
			document.getElementById('divYesNo').style.visibility = 'visible';
		}else if(btnMenu == INCLUDE_EXCLUDE_CANCEL ){
			document.getElementById('divOK').style.visibility = 'hidden';
			document.getElementById('divYesNo').style.visibility = 'hidden';
			document.getElementById('divIncExCancel').style.visibility = 'visible';			
		}else if (btnMenu == OK_CANCEL) {
			document.getElementById('divOK').style.visibility = 'hidden';
			document.getElementById('divYesNo').style.visibility = 'hidden';
			document.getElementById('divIncExCancel').style.visibility = 'hidden';	
			document.getElementById('divOkCancel').style.visibility = 'visible';	
		}
	}else{
	document.getElementById("errMessage").innerHTML = dialogArguments;
	}
}



</SCRIPT>
</head>
<body onload = "changetext()" style="background:#ECE9DB">
<form name = "frmError">

<!--START:code changed by Kalidass G on 22-Jan-2010 for Mantis 881 : 
	Attribute: "overflow" is added and value is set to auto in the below <div> tag to place the buttons underneath the scrolling section of the dialogue box and 
	Attribute: "width" is changed from 370px to 100% and "height" is changed from 50px to 60% and table's style attribute "left" is changed from 15px to 0px-->

<div  style="position:absolute;left:0px;top:0px;width:100%;height:60%;overflow: auto;" >
    <table  style="position:absolute;left:0px;width:100%;bgcolor:red">
    
<!--END:code changed by Kalidass G on 22-Jan-2010 for Mantis 881 : 
	Attribute: "overflow" is added and value is set to auto in the above <div> tag to place the buttons underneath the scrolling section of the dialogue box and 
	Attribute: "width" is changed from 370px to 100% and "height" is changed from 50px to 60% and table's style attribute "left" is changed from 15px to 0px-->

	<tr><td>&nbsp;&nbsp;</td></tr>
        <tr><td width="100%" id='errMessage' style="text-align:center;font-family:Tahoma; font-size :8pt" ></td></tr>
    </table>
</div>

<div id='divOK' style="visibility:visible;position:absolute;left:85px;top:80px;">
<table width="200px"  cellpadding="0">
  <tr>
    <td >
						<center><input type="button" value='<s:text name="button.ok" />' name="btnOK" class="bolButton" style="width: 60px; height: 21px; font-family: Tahoma; font-size: 8pt" onclick="javascript:window.close()"></center>
    </td>
  </tr>
</table>
</div>
<!--START:code changed by Kalidass G on 22-Jan-2010 for Mantis 881 : top value is changed from 80 to 70% in the below <div> tag to place the buttons underneath the scrolling section of the dialogue box. -->
<div id='divYesNo' style="visibility:hidden;position:absolute;left:95px;top:70%;">
<!--END:code changed by Kalidass G on 22-Jan-2010 for Mantis 881 : top value is changed from 80 to 70% in the above <div> tag to place the buttons underneath the scrolling section of the dialogue box. -->
<table  width="200px" border="0" cellspacing="0" cellspacing="0">
  <tr>
    <td width="15%">&nbsp;</td>
  <td width="40%">
						<input type="button" value="<s:text name='button.yes'/>" name="btnYes" class="bolButton" style="width: 60px; height: 21px; font-family: Tahoma; font-size: 8pt" onclick="closewin(ID_YES);">
        </td>
    <td width="45%">
						<input type="button" value="<s:text name='button.no'/>" name="btnNo" class="bolButton" style="width: 60px; height: 21px; font-family: Tahoma; font-size: 8pt" onclick="closewin(ID_NO);">
    </td>
  </tr>
</table>
</div>

<div id='divOkCancel' style="visibility:hidden;position:absolute;left:95px;top:80px;">
<table  width="200px" border="0" cellspacing="0" cellspacing="0">
  <tr>
    <td width="15%">&nbsp;</td>
  <td width="40%">
                                            <input type="button" value="<s:text name='button.ok' />" name="btnOk" class="bolButton" style="width: 60px; height: 21px; font-family: Tahoma; font-size: 8pt" onclick="closewin(ID_OK);">
        </td>
    <td width="45%">
                                                <input type="button" value="<s:text name='button.cancel' />" name="btnCancel" class="bolButton" style="width: 60px; height: 21px; font-family: Tahoma; font-size: 8pt" onclick="closewin(ID_CANCEL);">
  </tr>
</table>
</div>


<div id='divIncExCancel' style="visibility:hidden;position:absolute;left:55px;top:80px;">
<table  width="300px" border="0" cellspacing="0" cellspacing="0">
  <tr>
    <td width="10%">&nbsp;</td>
  <td width="30%">
						<input type="button" value="<s:text name='button.include' />" name="btnInclude" class="bolButton" style="width: 60px; height: 21px; font-family: Tahoma; font-size: 8pt" onclick="closewin(ID_INCLUDE);">
        </td>
    <td width="30%">
                                                <input type="button" value="<s:text name='button.exclude' />" name="btnExclude" class="bolButton" style="width: 60px; height: 21px; font-family: Tahoma; font-size: 8pt" onclick="closewin(ID_EXCLUDE);">
    <td width="30%">
                                                <input type="button" value="<s:text name='button.cancel' />" name="btnCancel" class="bolButton" style="width: 60px; height: 21px; font-family: Tahoma; font-size: 8pt" onclick="closewin(ID_CANCEL);">
    </td>
  </tr>
</table>
</div>

</form>



</body>
</html>


