<?xml version="1.0" encoding="UTF-8"?>

<%@ page contentType="text/xml"%>
<%@ page import="org.swallow.util.SwtConstants"%>
<%@ taglib prefix="s" uri="/struts-tags" %>



<ilmaccountgroupdetails
	name="ilmaccountgroupdetails" >

<request_reply> <status_ok><s:property value="#request.reply_status_ok" /></status_ok> 
<message><s:property value="#request.reply_status_ok" /></message> 
<location /> </request_reply> 
<timing> 
	<s:iterator value="#request.opTimes" var="opTime">    
			<operation id="<s:property value="#opTime.key" />"><s:property value="#opTime.value" /></operation>
	</s:iterator> 
</timing> 
<singletons> 
	<publicprivate>
		<s:property value="#request.accountGroup.publicPrivate" />
	</publicprivate>
	<grouptype>
	<s:property value="#request.accountGroup.groupType" />	
	</grouptype>
	<groupname>
		<s:property value="#request.accountGroup.ilmGroupName" />	
	</groupname>
	<groupdescription>
		<s:property value="#request.accountGroup.ilmGroupDescription" />	
	</groupdescription>
	<idname>
		<s:property value="#request.accountGroup.defaultLegendText" />
	</idname>
	<mainAgent>
		<s:property value="#request.accountGroup.mainAgent" />	
	</mainAgent>
	<allowReporting>
		<s:property value="#request.accountGroup.allowReporting" />	
	</allowReporting>
	<collectNetCumPos>
		<s:property value="#request.accountGroup.collectNetCumPos" />	
	</collectNetCumPos>
	<correspondentBank>
		<s:property value="#request.accountGroup.correspondentBank" />
	</correspondentBank>
	<firstminimum> 
		<s:property value="#request.accountGroup.thresholdMin1" />	
	</firstminimum>
	<firstmaximum> 
		<s:property value="#request.accountGroup.thresholdMax1" />
	</firstmaximum>
	<secondminimum>
	  	<s:property value="#request.accountGroup.thresholdMin2" />
	</secondminimum>
	<secondmaximum > 
		<s:property value="#request.accountGroup.thresholdMax2" />
	</secondmaximum>
	<filtercondition >
		<s:property value="#request.accountGroup.filterCondition" />
			
	</filtercondition>
	<minNcpThreshold >
		<s:property value="#request.accountGroup.minNcpThreshold" />
	</minNcpThreshold>
	<maxNcpThreshold >
		<s:property value="#request.accountGroup.maxNcpThreshold" />	
	</maxNcpThreshold>
	<createDate>
		${createdDate}
	</createDate>
	<maintainAnyGroup>
		${maintainAnyGroup}
	</maintainAnyGroup>
	<global>
		${global}
	</global>
	<central>
		${central}
	</central>
	<createdByUser >
		<s:property value="#request.accountGroup.createdByUser" />
	</createdByUser>
	<thresh1Percent >
		<s:property value="#request.accountGroup.thresh1Percent" />
		
	</thresh1Percent>
	<thresh2Percent >
		<s:property value="#request.accountGroup.cthresh2Percent" />
		
	</thresh2Percent>
	<thresh1Time >
		<s:property value="#request.accountGroup.thresh1Time" />
		
	</thresh1Time>
	<thresh2Time >
		<s:property value="#request.accountGroup.thresh2Time" />
		
	</thresh2Time>
	<createThroughputRatio >
		<s:property value="#request.accountGroup.createThroughputRatio" />
		
	</createThroughputRatio>
</singletons>

<grid>
	<metadata>
		<columns>
			<column heading="<s:text name="ilmAccountGroupDetails.accountIdName" />"
					draggable="false"					
					filterable="true"
					type="str"
					dataelement="account_id_name"
					sort="false"
					width="<s:property value="#request.column_width.accountIdName"/>"/>
			<column heading="<s:text name="ilmAccountGroupDetails.type" />"
					draggable="false"					
					filterable="true"
					type="str"
					dataelement="type"
					sort="false"
					width="<s:property value="#request.column_width.type"/>"/>
			<column heading="<s:text name="ilmAccountGroupDetails.class" />"
					draggable="false"					
					filterable="true"
					type="str"
					dataelement="class"
					sort="true"
					width="<s:property value="#request.column_width.class"/>"/>
			<column heading="<s:text name="ilmAccountGroupDetails.level" />"
					draggable="false"					
					filterable="true"
					type="str"
					dataelement="level"
					sort="true"
					width="<s:property value="#request.column_width.level"/>"/>			
		</columns>	
	</metadata>

	<rows size="${rowSize}">	
		<s:iterator value="#request.accountInGroup" var="accountInGroup">		
			<row left_hidden="true" right_hidden="false" original="true" filtred="true" shared="false"  >
				<account_id_name clickable="false">
					<s:property value="#accountInGroup.id.account.id.accountId"/> - <s:property value="#accountInGroup.id.account.acctname"/>
				</account_id_name>
				<type clickable="false">
					<s:if test='#accountInGroup.id.account.accttype == "C"'>Cash</s:if>
					<s:if test='#accountInGroup.id.account.accttype == "U"'>Custodian</s:if>
					
				</type>
				<class clickable="false">
					<s:if test='#accountInGroup.id.account.acctClass == "C"'>Current</s:if>
					<s:if test='#accountInGroup.id.account.acctClass == "L"'>Loro</s:if>					
					<s:if test='#accountInGroup.id.account.acctClass == "E"'>Netting</s:if>
					<s:if test='#accountInGroup.id.account.acctClass == "N"'>Nostro</s:if>
					<s:if test='#accountInGroup.id.account.acctClass == "O"'>Others</s:if>
				</class>
				<level clickable="false">
					<s:if test='#accountInGroup.id.account.acctlevel == "M"'>Main</s:if>
					<s:if test='#accountInGroup.id.account.acctlevel != "M"'>Sub</s:if>
					<s:else></s:else>
				</level>
				<entity_id clickable="false">
					<s:property value="#accountInGroup.id.account.id.entityId"/>
				</entity_id>
				<account_id	clickable="false">
					<s:property value="#accountInGroup.id.account.id.accountId"/>
				</account_id>
			</row>
		</s:iterator>
	</rows>
</grid>

<selects> 
	<select id="entity"> 	
		<s:iterator value="#request.entities" var="entity">
			<option value="<s:property value="#entity.value" />" 
				selected="<s:if test="#request.accountGroup.entityId==#entity.value">1</s:if><s:else>0</s:else>"
			><s:property value="#entity.label" /></option>
		</s:iterator> 
	</select> 
				
	<select id="currency"> 
		<s:iterator value="#request.currencies" var="currency"> 		 	
		 	<option value="<s:property value="#currency.value" />" 
				selected="<s:if test="#request.accountGroup.currencyCode==#currency.value">1</s:if><s:else>0</s:else>"
			><s:property value="#currency.label" /></option>					
	 	</s:iterator>
	 </select>
	<select id="accountgroups"> 
		<s:iterator value="#request.accountGroups" var="group"> 
		 	<option value="<s:property value="#group.value" />" 
				selected="<s:if test="#request.accountGroup.id.ilmGroupId==#group.value">1</s:if><s:else>0</s:else>"
			><s:property value="#group.label" /></option>
		</s:iterator> 
	</select>
</selects>
 </ilmaccountgroupdetails>