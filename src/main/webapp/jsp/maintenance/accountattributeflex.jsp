<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">

<%@ page import="org.swallow.util.SwtUtil"%>
<%@ include file="/taglib.jsp"%>
<%@ include file="/angularJSUtils.jsp"%>
<html>
<head>
<meta name="viewport" content="width=device-width, initial-scale=1">
<link rel="icon" type="image/x-icon" href="favicon.ico">
<link rel="stylesheet" href="angularSources/styles.css?version=<%= SwtUtil.appVersion %>">
</head>
<body>

<script type="text/javascript" src="jsp/work/assets/swfobject.js"></script>
<script type="text/javascript">
var screenTitle = "";
screenTitle = getMessage("label.accountattribute.title.window", null);
document.title = screenTitle;
var appName = "<%=SwtUtil.appName%>";
var baseURL = new String('<%=request.getRequestURL()%>');
var screenRoute = "AccountAttributeMaintenance";

var requestURL = new String('<%=request.getRequestURL()%>');
var idy = requestURL.indexOf('/'+appName+'/');
var mainWindow= null;
requestURL=requestURL.substring(0,idy+1) ;
					
			// This method is called when onload
			window.onload = function () {
			    setTitleSuffix(document.forms[0]);
				setParentChildsFocus();	
			};
			window.onunload = call;
			var dbDate = '${requestScope.sysDateAsString}';
			var menuAccessId = '${requestScope.menuAccessId}';
			var selectedAccountGroup = '${selectedAccountGroup}';
			var methodName = '${methodName}';
			var currencyCode = '${currencyCode}';
			var entityId = '${entityId}';
			var accountId = '${accountId}';
			var attributeId = '${attributeId}';
			var dateFrom = '${dateFrom}';
			var dateTo = '${dateTo}';
			var effectiveDate = '${effectiveDate}';
			var attributeEffectiveDateRequired = '${attributeEffectiveDateRequired}'
				
			var parentScreen='${parentScreen}';
			var maintainAnyGroup = '${maintainAnyGroup}';
			var dateFormat = '<s:property value="#request.session.CDM.dateFormatValue" />';
			var currencyFormat = '<s:property value="#request.session.CDM.currencyFormat"/>';
			var ccyPattern = 0;
				
			if(currencyFormat.indexOf("1")!=-1)
				ccyPattern = 0;
			else if(currencyFormat.indexOf("2")!=-1)
				ccyPattern = 1;
			
			
			
			/**
			 * openChildWindow
			 * @param methodName
			 * Method to load child screens
			 */
			function openChildWindow(methodName){
				var param = '/' + appName + '/accountAttribute.do?method='+methodName;
				mainWindow = window.open (param, 'accountAttributesValuesAdd','left=10,top=230,width=670,height=270,toolbar=0, resizable=yes, //status=yes, scrollbars=yes','true');	
				return false;
			}
			
			/**
			 * Close window
			 */
			function updateData(){
				//refresh data grid in parent screen
				Main.populateGrid();
			}
			
			/**
			 * help
			 * This function opens the help screen 
			 * @return none
			 */
			function help(){
				openWindow(buildPrintURL('print','Account Attribute Maintenance Screen'),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')
			}
			
			function isEffectiveDateRequired(accountAttributeId){
				var appName = "<%=SwtUtil.appName%>";
				var requestURL = new String('<%=request.getRequestURL()%>');
				var idy = requestURL.indexOf('/'+appName+'/');
				requestURL=requestURL.substring(0,idy+1) ; 
				requestURL = requestURL + appName+"/accountAttribute.do?method=checkEffectiveDate";
				requestURL = requestURL + "&accountAttributeId=" + accountAttributeId;
				var oXMLHTTP = new XMLHttpRequest();
				oXMLHTTP.open( "POST", requestURL, false );
				oXMLHTTP.send();
				var isRequired =new String(oXMLHTTP.responseText);

				return (isRequired == "Y");
				
			} 
			
			
			
</script>
<%@ include file="/angularscripts.jsp"%>
	<form id="exportDataForm" method="post" target="tmp">
		<input type="hidden" name="data" id="exportData" /> 
	    <input type="hidden" name="screen" id="exportDataScreen" value="<s:text name="label.accountattributedefinition.title.window"/>" />
	</form>
		<iframe name="tmp" width="0%" height="0%" src="#" />
</body>
</html>