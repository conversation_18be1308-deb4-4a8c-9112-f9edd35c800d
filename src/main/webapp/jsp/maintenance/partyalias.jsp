<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@ include file="/taglib.jsp"%>
<%@ page import="org.swallow.util.SwtConstants"%>
<html>
<head>
<title><s:text name="partyalias.title.mainWindow" /></title>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">

<SCRIPT language="JAVASCRIPT">



var cancelcloseElements = new Array(1);
	cancelcloseElements[0]="closebutton";


var menuEntityCurrGrpAccess = "${requestScope.menuEntityCurrGrpAccess}";

 

function disableAllButtons()
{
	document.getElementById("deletebutton").innerHTML = document.getElementById("deletedisablebutton").innerHTML; 
}

function onFilterandSort()
{
	updateColors();
	disableAllButtons();

}

function submitFormDelete(methodName){
	document.forms[0].method.value = methodName;	
	var yourstate=window.confirm('<s:text name="confirm.delete"/>');
	if (yourstate==true){ 
	enableFields();
	document.forms[0].entityDesc.value = document.getElementById("entityDescription").innerText;
	document.forms[0].partyName.value = document.getElementById("partyDescription").innerText;
	document.forms[0].parentRefreshRequired.value = "No";
	document.forms[0].submit();	
	}
}
/**
* This method is used to load the Party Alias screen
*/
function bodyOnLoad()
{
	//for loading data grid
	xl = new XLSheet("partyAliasList","table_2", ["String"],"1");
	xl.onsort = xl.onfilter = onFilterandSort;
	highlightTableRows("partyAliasList");
	//set inttial button status	
	document.getElementById("deletebutton").innerHTML = document.getElementById("deletedisablebutton").innerHTML; 

	//Code Modified by Chinna on 7-Nov-2012 for Mantis
	//1961:INTERNAL: Refactor frame party_list procedure including
	//removing duplication to improve maintainability
	document.getElementById("partyDescription").innerText = "${requestScope.partyName}";
	document.getElementById("entityDescription").innerText = '${requestScope.entityDesc}';
	if(menuEntityCurrGrpAccess == "0"){
		document.getElementById("addbutton").innerHTML = document.getElementById("addenablebutton").innerHTML;
	} else {
		document.getElementById("addbutton").innerHTML = document.getElementById("adddisablebutton").innerHTML;
	}
	document.forms[0].parentRefreshRequired.value = "${requestScope.parentRefreshRequired}";
	document.forms[0].menuAccessId.value = "${requestScope.menuAccessId}";
}

function submitForm(methodName){
	 
	document.forms[0].method.value = methodName;
	document.forms[0].submit();
}


function buildURL(methodName) {
		
		var param = 'party.do?method='+methodName;
		return  param;
	}
function onSelectTableRow(rowElement, isSelected)
{
		
	var hiddenElement = rowElement.getElementsByTagName("input")[0];

	document.forms[0].selectedPartyAlias.value = (new String(hiddenElement.value)).trim();

	if (menuEntityCurrGrpAccess == "0" & isSelected) {
		document.getElementById("deletebutton").innerHTML = document.getElementById("deleteenablebutton").innerHTML;
	} else {
		document.getElementById("deletebutton").innerHTML = document.getElementById("deletedisablebutton").innerHTML;
	}
	
}
	
function buildAddAliasURL(methodName){
	
	 enableFields();
	 var param = 'party.do?method='+methodName+'&selectedEntityId=';
	    param +=document.forms[0].elements['party.id.entityId'].value;
	    param +='&selectedPartyId=';
	    param +=document.forms[0].elements['party.id.partyId'].value;
	    param +='&partyName=';
	    param +=document.getElementById("partyDescription").innerText;
	    param +='&entityDesc='+document.getElementById("entityDescription").innerText;
		disableFields();
		return  param;
	}
 
function enableFields()
{
	document.forms[0].elements['party.id.entityId'].disabled = "";
	document.forms[0].elements['party.id.partyId'].disabled = "";
}

function disableFields()
{
	document.forms[0].elements['party.id.entityId'].disabled = "true";
	document.forms[0].elements['party.id.partyId'].disabled = "true";
}

function onBodyUnload()
{
	if (document.forms[0].parentRefreshRequired.value != "No") {
	parentScreenRefresh(); 
	}
}



function parentScreenRefresh()
{
	var parentWindow = window.opener;
	if ( parentWindow.refreshAliasDetails != 'undefined')
	{
	enableFields();
	var parId = document.forms[0].elements['party.id.partyId'].value;
		
		var noOfAliases = 0;
		if(document.getElementById("partyAliasList").tBodies[0].rows.length > 0)
			{
				
				noOfAliases = document.getElementById("partyAliasList").tBodies[0].rows.length;
				
			}
		
		parentWindow.refreshAliasDetails(parId, noOfAliases);
	}
}
</SCRIPT>

</head>
<body onmousemove="reportMove()" leftmargin="0" topmargin="0"
	marginheight="0"
	onLoad="setParentChildsFocus();setFocus(document.forms[0]);bodyOnLoad();ShowErrMsgWindow('${actionError}');"
	onunload="onBodyUnload()">

<s:form action="party.do">
	<input name="method" type="hidden" value="">
	<input name="selectedPartyAlias" type="hidden">
	<input name="partyName" type="hidden">
	<input name="entityDesc" type="hidden">
	<input name="selectedPartyId" type="hidden">
	<input name="selectedEntityId" type="hidden">
	<input name="parentRefreshRequired" type="hidden">
	
	<input name="menuAccessId" type="hidden">
	

	<div id="PartyAliasMaintenance"
		style="position: absolute; left: 20px; top: 20px; width: 722px; height: 61px; border: 2px outset;"
		color="#7E97AF">
	<div id="PartyAliasMaintenance"
		style="position: absolute; left: 8px; top: 4px; width: 510px; height: 38px;">

	<table width="510" border="0" cellpadding="0" cellspacing="0"
		height="25">
		<tr height="25">
			<td width="30"><b><s:text name="party.entityId" /></b></td>
			<td width="28">&nbsp;</td>
			<td width="28"><s:textfield name="party.id.entityId"
			     titleKey="tooltip.entityId"
				disabled="true" style="width:120px;" cssClass="htmlTextAlpha" />
			</td>
			<td width="20">&nbsp;</td>
			<td width="300"><span id="entityDescription"
				name="entityDescription" class="spantext"></td>
		</tr>

		<tr height="25">
			<td width="30"><b><s:text name="party.partyId" /></b></td>
			<td width="28">&nbsp;</td>
			<td width="28">
			
			<s:textfield name="party.id.partyId" disabled="true"
			     titleKey="tooltip.partyId"
				style="width:120px" tabindex="1" /></td>
			<td width="20">&nbsp;</td>
			<td width="300"><span id="partyDescription"
				name="partyDescription" class="spantext"></td>
		</tr>
	</table>

	</div>
	</div>


	<div id="CustodianMaintenance" color="#7E97AF"
		style="position: absolute; border-left: 2px outset; left: 20px; top: 85px; width: 722px; height: 375px;">
	<div id="CustodianMaintenance"
		style="position: absolute; z-index: 99; left: 0px; top: 0px; width: 700px; height: 10px;">
	<table class="sort-table" id="table_2" bgcolor="#B0AFAF" width="700px"
		border="0" cellspacing="1" cellpadding="0" height="23px">
		<thead>
			<tr>
				
				<td title='<s:text name="tooltip.sortPartyAlias"/>'
					align="right" width="700px" height="20px" style="border-left-width: 0px;"><b><s:text name="partyAlias.alias" /></b></td>


			</tr>
		</thead>
	</table>
	</div>


	<div id="ddscrolltable"
		style="position: absolute; left: 0px; top: 0px; width: 717; height: 371px;overflow-x:hidden">
	<div id="paryMaintenance"
		style="position: absolute; z-index: 99; left: 0px; top: 21px; width: 700; height: 10px;">
	<table class="sort-table" id="partyAliasList" width="700px" border="0"
		cellspacing="1" cellpadding="0" height="348px">
		<tbody>
			<%int count = 0; %>
			<s:iterator value='#request.partyAliasList' var='partyAliasList' >
				<% if( count%2 == 0 ) {%><tr class="even">
					<% }else  { %>
				
				<tr class="odd">
					<%}++count; %>
					<td width="700px"><s:hidden name="partyAliasList.id.partyAlias" value="%{#partyAliasList.id.partyAlias}" /><s:property value='#partyAliasList.id.partyAlias' />&nbsp;</td>

				</tr>
			</s:iterator>
		</tbody>
		<tfoot>
			<tr>
				<td colspan="1"></td>
			</tr>
		</tfoot>
	</table>
	</div>
	</div>
	</div>


	<div id="PartyMain"
		style="position: absolute; left: 658; top: 473px; width: 80px; height: 39px; visibility: visible;">
	<table width="70" border=0 cellspacing="0" cellpadding="0" height="20">
		<tr>
			<td align="Right">
			
			<a tabindex="6" tabindex="6" href=#
				onclick="javascript:openWindow(buildPrintURL('print','PartyAlias Maintenance'),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')"
				onMouseOut="MM_swapImgRestore()"
				onMouseOver="MM_swapImage('Help','','images/help_default.GIF ',1)"><img
				src="images/help_default.GIF " name="Help" border="0"
				title='<s:text name="tooltip.helpScreen"/>'></a></td>

			<td align="right" id="Print"><a tabindex="6"
				onclick="printPage();" onMouseOut="MM_swapImgRestore()"
				onMouseOver="MM_swapImage('Print','','images/Print_R.gif ',1)"><img
				src="images/Print.gif " name="Print" border="0"
				title='<s:text name="tooltip.printScreen"/>'></a></td>
		</tr>
	</table>
	</div>
	

	<div id="ddimagebuttons" color="#7E97AF"
		style="position: absolute; border: 2px outset; left: 20; top: 466; width: 722; height: 39px; visibility: visible;">
	<div id="CustodianMaintenance"
		style="position: absolute; left: 6; top: 4; width: 539; height: 15px; visibility: visible;">
	<table width="210" border="0" cellspacing="0" cellpadding="0"
		height="20">
		<tr>
			<td id="addbutton"></td>
			<td id="deletebutton"></td>
		
			<td id="closebutton"><a tabindex="5"
				title='<s:text name="tooltip.close"/>'
				onMouseOut="collapsebutton(this)"
				onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)"
				onMouseUp="highlightbutton(this)" onclick="confirmClose('P')"><s:text name="button.close" /></a></td>
				
		</tr>
	</table>
	</div>
	<div
		style="position: absolute; left: 6; top: 4; width: 512; height: 15px; visibility: hidden;">
	<table width="280" border="1" cellspacing="0" cellpadding="0"
		height="20" style="visibility: hidden">
		<tr>
			<td id="addenablebutton">
			
			<a tabindex="2" title='<s:text name="tooltip.addnewpartyAlias"/>'
				onMouseOut="collapsebutton(this)"
				onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)"
				onMouseUp="highlightbutton(this)"
				onClick="javascript:openWindow(buildAddAliasURL('addAlias'),'partymaintenanceaddWindow','left=50,top=190,width=860,height=175,toolbar=0, resizable = yes, scrollbars = yes','true')"><s:text name="button.add" /></a></td>
			<td id="adddisablebutton">
			
			<a class="disabled" disabled="disabled"><s:text name="button.add" /></a></td>


			<td id="deleteenablebutton"><a tabindex="4"
				title='<s:text name="tooltip.deleteSelectedPartyAlias"/>'
				onMouseOut="collapsebutton(this)"
				onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)"
				onMouseUp="highlightbutton(this)"
				onClick="javascript:submitFormDelete('deleteAliasRecord')"><s:text name="button.delete" /></a></td>
			<td id="deletedisablebutton">
			
			<a class="disabled" disabled="disabled" title='<s:text name="tooltip.deleteSelectedPartyAlias"/>'><s:text name="button.delete" /></a></td>
		</tr>
	</table>
	</div>
	</div>

	<blockquote>&nbsp;</blockquote>
	<p>&nbsp;</p>


	<script type="text/javascript">
</script>
</s:form>
</body>
</html>
