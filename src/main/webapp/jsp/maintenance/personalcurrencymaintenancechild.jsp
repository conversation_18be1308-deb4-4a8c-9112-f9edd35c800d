<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@ include file="/taglib.jsp"  %>
<%@ page import="org.swallow.util.SwtConstants" %>
<html>
<head>
	<title><s:text name="personalCurrency.childscreenName"/></title>

<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
<SCRIPT language="JAVASCRIPT">
<!--Betcy::12/01/2009:Added for Mantis 774 to close the screen(start)-->
var cancelcloseElements = new Array(1);
cancelcloseElements[0] = "cancelbutton";
<!--Betcy::12/01/2009:Added for Mantis 774 to close the screen(end)-->
mandatoryFieldsArray = ["*"]; 
<s:if test='#request.parentFormRefresh == "Y"'>
window.opener.document.forms[0].method.value="displayPersonalCurrencyList";
window.opener.document.forms[0].submit();
self.close();
</s:if>

function isEqualToZero(input){

	if (input.value=="0"||input.value=="00"){
		alert("<s:text name='alert.priorityNotBeZero'/>");
		input.value="";
		input.focus();
		return false;
	}
	return true;
}
function submitForm(methodName){
/*Betcy:12/01/09:Added for Mantis issue 751 to check the validation in "SAVE" button click(Start)*/
    var priorityValue=validateField(document.forms[0].elements['personalCurrency.priorityOrder'],'personalCurrency.priorityOrder','numberPat');
	/*Betcy:12/01/09:Added for Mantis issue 751 to check the validation in "SAVE" button click(End)*/
	var methodNameVar = '${requestScope.methodName}';

	if (methodNameVar == "save") {
	document.forms[0].saveOrUpdateFlag.value = "save";
	} else {
	document.forms[0].saveOrUpdateFlag.value = "update";
	}
  /*Betcy:12/01/09:Added for Mantis issue 751 to check the validation in "SAVE" button click(Start)*/
  if(priorityValue)
  {
  /*Betcy:12/01/09:Added for Mantis issue 751 to check the validation in "SAVE" button click(End)*/
  if(validateForm(document.forms[0]) )
  {
	  //personalCurrency.alert.priority
	  enableFields();
	document.forms[0].method.value = methodName;
	document.forms[0].submit();
  }
  /*Betcy:12/01/09:Added for Mantis issue 751 to check the validation in "SAVE" button click(Start)*/
  }
  else
  {
    document.forms[0].elements['personalCurrency.priorityOrder'].focus();
  }
  /*Betcy:12/01/09:Added for Mantis issue 751 to check the validation in "SAVE" button click(End)*/
}

function enableFields(){
	document.forms[0].elements["personalCurrency.id.currencyCode"].disabled = "";
}

function bodyOnLoad()
{
    <s:if test="#request.methodName == 'save'">
 		var currencydropBox = new SwSelectBox(document.forms[0].elements["personalCurrency.id.currencyCode"],document.getElementById("currencyName"));
		</s:if>
	<s:if test="#request.methodName != 'save'">
		document.getElementById("currencyName").innerText = '${requestScope.currencyName}'
	</s:if>

	
	}

function validateForm(objForm){
  var elementsRef = new Array();
  elementsRef[0] = objForm.elements["personalCurrency.priorityOrder"]; 

  return validate(elementsRef);
}

var currencyFormat = '<s:property value="#request.session.CDM.currencyFormat" />';
</SCRIPT>

<body leftmargin="0" topmargin="0" marginheight="0" onLoad="bodyOnLoad();setParentChildsFocus();setFocus(document.forms[0]); ShowErrMsgWindow('${actionError}');" onunload="call()">
<s:form action="personalcurrency.do"  onsubmit="validate(this);">
<input name="method" type="hidden" value="">
<input name="saveOrUpdateFlag" type="hidden" value="">
<input name="dummy" value="" style="visibility:hidden">
<%-- Start : Vivekanandan :12/11/2008: Modified
	 Modified width to display currency name elaborately with out fluctuation --%>
<div id="PersonalCurrencyChild" style="position:absolute; left:20px; top:20px; width:390px; height:61px; border:2px outset;" color="#7E97AF">
<%-- End : Vivekanandan :12/11/2008: Modified
	 Modified width to display currency name elaborately with out fluctuation --%>
	
	<div id="PersonalCurrencyChild" style="position:absolute;z-index:99;left:8px; top:4px; width:350px; height:35px;">
	
	<%-- Start : Vivekanandan :12/11/2008: Modified
		 Modified width to display currency name elaborately with out fluctuation --%>
		<table width="380px" border="0" cellpadding="0" cellspacing="0" height="50">
	<%-- End : Vivekanandan :12/11/2008: Modified
		 Modified width to display currency name elaborately with out fluctuation --%>
	
		<tr>				 
			<td width="93px"><b><s:text name="role.perCurrrency.currency"/></b></td>			
			  <td width="370px">
				<s:if test="#request.methodName == 'save'">
				<%-- Start : Vivekanandan :12/11/2008: Modified
					 Modified width to display currency name elaborately with out fluctuation --%>
					 
					 <s:select
			    id="personalCurrency.id.currencyCode"
			    name="personalCurrency.id.currencyCode"
			    cssStyle="width:60px"
			    titleKey="tooltip.selectCurrencyId"
			    tabindex="1"
			    list="#request.perCurrList"
			    listKey="value"
			    listValue="label"
			/>
					 
					 
					 
				</s:if>
				<s:if test="#request.methodName == 'update'">
				 <s:textfield tabindex="1" name="personalCurrency.id.currencyCode" cssClass="htmlTextAlpha"  cssStyle="width:40px;" disabled="true" /> 
				
				</s:if>&nbsp;&nbsp;&nbsp;
					<span id="currencyName" name="currencyName" class="spantext">
			</td>
		</tr>
		<tr>
			<s:if test="#request.methodName == 'save'">
							<td width="93px"><b><s:text name="role.perCurrrency.priority"/></b>*</td>
					</s:if>
					
          			 <s:if test="#request.methodName == 'update'">
						 	<td width="93px"><b><s:text name="role.perCurrrency.priority"/></b>*</td>
					</s:if>
							
			<%-- Start : Vivekanandan :12/11/2008: Modified
				 Modified width to display currency name elaborately with out fluctuation --%>       	  
			<td width="370px">
			<%-- End : Vivekanandan :12/11/2008: Modified
				 Modified width to display currency name elaborately with out fluctuation --%>
				<s:textfield tabindex="2" titleKey="tooltip.enterPriority" cssClass="htmlTextNumeric" name="personalCurrency.priorityOrder" maxlength="2"   cssStyle="width:25px;" onchange="return validateField(this,'personalCurrency.priorityOrder','numberPat');"  onblur="isEqualToZero(this)"/> 
			</td>
			   
		</tr>
	</table>
</div>
</div>
<%-- Start : Vivekanandan :12/11/2008: Modified
	 Modified left to display currency name elaborately with out fluctuation --%>
<div id="PersonalCurrencyChild" style="position:absolute; left:340; top:94px; width:70px; height:15px; visibility:visible;">
<%-- End : Vivekanandan :12/11/2008: Modified
	 Modified left to display currency name elaborately with out fluctuation --%>
	<table width="60px" border="0" cellspacing="0" cellpadding="0" height="20">
		<tr>

		<td align="Right">
				<a  tabindex="6" href=# onclick="javascript:openWindow(buildPrintURL('print','Add Personal Currency'),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')" onMouseOut="MM_swapImgRestore()" onMouseOver="MM_swapImage('Help','','images/help_default.GIF ',1)"><img src="images/help_default.GIF " name="Help" border="0" title='<s:text name="tooltip.helpScreen"/>'></a> 
	    </td>

			<td align="right" id="Print">
				<a tabindex="6" onclick="printPage();" onMouseOut="MM_swapImgRestore()" onMouseOver="MM_swapImage('Print','','images/Print_R.gif ',1)"><img src="images/Print.gif " name="Print"  border="0" title='<s:text name="tooltip.printScreen"/>'></a>	
			</td>
		</tr>
	</table>
</div>
<%-- Start : Vivekanandan :12/11/2008: Modified
	 Modified width to display currency name elaborately with out fluctuation --%>
<div id="ddimagebuttons" color="#7E97AF" style="position:absolute; border:2px outset; left:20; top:85px; width:390px; height:39px; visibility:visible;">
<%-- End : Vivekanandan :12/11/2008: Modified
	 Modified width to display currency name elaborately with out fluctuation --%>
<div id="PersonalCurrencyChild" style="position:absolute; left:6; top:4; width:150px; height:15px; visibility:visible;">
  <table width="140px" border="0" cellspacing="0" cellpadding="0" height="20">
  
	<tr>
	 <!-- Save Button  -->
		  <td width="70px">
		  <a title='<s:text name = "tooltip.save"/>' tabindex="3" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onClick="javascript:submitForm('saveOrUpdate');"><s:text name="button.save"/></a>
		  </td>

	 <!-- Cancel Button  -->
		  <!--Betcy:15/01/2009:Added the id value to close the screen-->
		  <td id="cancelbutton" width="70px">						 
			<a title='<s:text name="tooltip.cancel"/>' tabindex="4" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onclick="confirmClose('C');"><s:text name="button.cancel"/></a>
		  </td>
		</tr>	

	</table>
  </div>
</div>

</s:form>
</body>
</html>