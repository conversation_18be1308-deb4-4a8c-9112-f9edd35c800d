<?xml version="1.0" encoding="UTF-8" ?>
  
<%@ page contentType="text/xml" %>
<%@ taglib prefix="s" uri="/struts-tags" %>
<%@ taglib uri="http://java.sun.com/jstl/fmt_rt" prefix="fmt" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%
	//variable declaration
	String minValue="";
    String maxValue="";
    if (request.getAttribute("minValue") != null)
	//get the minValue   from request
	minValue = request.getAttribute("minValue")
			.toString();
    
    if (request.getAttribute("maxValue") != null)
	//get the maxValue from request
	maxValue = request.getAttribute("maxValue")
			.toString();

%>
<accoutattributehdr>
	<request_reply>
		<status_ok><s:property value='#request.reply_status_ok' /></status_ok>
		<message><s:property value='#request.reply_message' /></message>
		<location />
	</request_reply>

	<singletons>
		<s:if test='"addScreen"!=#request.screenName' >
			<attributeid><s:property value='#request.accountAttributeHDR.id.attributeId' /></attributeid>
			<attibutename><s:property value='#request.accountAttributeHDR.attributeName' /></attibutename>
		    <tooltip><s:property value='#request.accountAttributeHDR.tooltipText' /></tooltip>
		    <effectivedaterequired><s:property value='#request.accountAttributeHDR.effectiveDateRequired' /></effectivedaterequired>
		    <effectivedateallowtime><s:property value='#request.accountAttributeHDR.effectiveDateAllowTime' /></effectivedateallowtime>
		    <minvalue>${minValue}</minvalue>
		    <maxvalue>${maxValue}</maxvalue>
		    <minlength><s:property value='#request.accountAttributeHDR.validateTextMinLen' /></minlength>
		    <maxlength><s:property value='#request.accountAttributeHDR.validateTextMaxLen' /></maxlength>
		    <regexvalidation><s:property value='#request.accountAttributeHDR.validateTextRegex' /></regexvalidation>
		    <validationmessage><s:property value='#request.accountAttributeHDR.validateTextRegexMsg' /></validationmessage>
		    <dateValue><s:property value='#request.accountAttributeHDR.validateDateAllowTime' /></dateValue>
		    <currencyformat>${currencyFormat}</currencyformat>
		</s:if>  
	</singletons>

	<selects>
		<select id="types">
			<s:iterator value='#request.typesList' var='types' >
				<option 
					value="<s:property value='#types' />" 
					selected="<s:if test='#request.selectedType ==#types' >1</s:if><s:if test='#request.selectedType !=#types' >0</s:if>">
				</option>
			</s:iterator>
		</select>
	</selects>	
</accoutattributehdr>
