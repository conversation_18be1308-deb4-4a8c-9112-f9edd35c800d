<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@ include file="/taglib.jsp"%>
<%@ page import="org.swallow.util.SwtConstants"%>


<html>
<head>
<title><s:text name="ilmccyparams.title.window" /></title>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">


<SCRIPT language="JAVASCRIPT">

	var menuEntityCurrGrpAccess = "${menuAccessId}";
	
/*Added to apply style on mandatory field ,if it's equal to undefined then no field will be colored*/
 mandatoryFieldsArray= "undefined" ;
	
	/**
	 * Called on first load of the screen
	 */
	function bodyOnLoad()
	{
		var dropBox1 = new SwSelectBox(document.forms[0].elements["ilmCcyParams.id.entityId"],document.getElementById("entityName"));
		xl = new XLSheet("currencyParameterDetails","tableCCyParams", ["String", "String", "String", "String", "String", "String", "String", "String"],"11111111");
		
		xl.onsort = xl.onfilter = disableButtons;
		
		highlightTableRows("currencyParameterDetails");	
		document.getElementById("addbutton").innerHTML = document.getElementById("adddisablebutton").innerHTML;
		document.getElementById("changebutton").innerHTML = document.getElementById("changedisablebutton").innerHTML;
		document.getElementById("deletebutton").innerHTML = document.getElementById("deletedisablebutton").innerHTML;
		document.getElementById("viewbutton").innerHTML = document.getElementById("viewdisablebutton").innerHTML;
		
	
		var buttonStatus = "<%=request.getAttribute(SwtConstants.ADD_BUT_STS)%>";
		document.getElementById("addbutton").innerHTML = (buttonStatus=="<%=SwtConstants.STR_TRUE%>"?document.getElementById("addenablebutton").innerHTML:document.getElementById("adddisablebutton").innerHTML);	
		
		buttonStatus = "<%=request.getAttribute(SwtConstants.CHG_BUT_STS)%>";	
		document.getElementById("changebutton").innerHTML = (buttonStatus=="<%=SwtConstants.STR_TRUE%>"?document.getElementById("changeenablebutton").innerHTML:document.getElementById("changedisablebutton").innerHTML);	
				
		buttonStatus = "<%=request.getAttribute(SwtConstants.DEL_BUT_STS)%>";	
		document.getElementById("deletebutton").innerHTML = (buttonStatus=="<%=SwtConstants.STR_TRUE%>"?document.getElementById("deleteenablebutton").innerHTML:document.getElementById("deletedisablebutton").innerHTML);
			
		buttonStatus = "<%=request.getAttribute(SwtConstants.VIEW_BUT_STS)%>";	
		document.getElementById("viewbutton").innerHTML = (buttonStatus=="<%=SwtConstants.STR_TRUE%>"?document.getElementById("viewenablebutton").innerHTML:document.getElementById("viewdisablebutton").innerHTML);	
						
		document.forms[0].menuAccessId.value = "${requestScope.menuAccessId}";
	
		
	}
	
	/**
	 * Dispatched when the user selects a row in the grid 
	 */
	function selectTableRow(e)
	{
        var event = (window.event|| e);
		var target = (event.srcElement || event.target)
		var srcEl = target;
		while(srcEl.tagName != 'TD')
		{
			srcEl = srcEl.parentElement;
		}
		
		var rowElement = srcEl.parentElement;
		var tblElement = srcEl.parentElement.parentElement.parentElement;
	
		var isRowSel = isRowSelected(rowElement);
		resetTableRowsStyle(tblElement);
		
		if(isRowSel == false)
			rowElement.className = 'selectrow' ;
	
		onSelectTableRow(rowElement,!isRowSel);
	}
	
	/**
	 * This function is called when the user selects a row, it will change the visual of (change,delete) buttons
	 * @ rowElement
	 * @ isSelected 
	 */
	function onSelectTableRow(rowElement , isSelected){  
		var entityId = document.forms[0].elements["ilmCcyParams.id.entityId"].value; 
			
		var currencyCode = rowElement.cells[0].innerText;
		document.forms[0].selectedEntityId.value = entityId.trim();
		document.forms[0].selectedCurrencyCode.value = currencyCode.trim();
		var appName = "<%=SwtUtil.appName%>";
		var requestURL = new String('<%=request.getRequestURL()%>');
		var idy = requestURL.indexOf('/' + appName + '/');

		requestURL = requestURL.substring(0, idy + 1);
		requestURL = requestURL + appName
				+ "/intraDayLiquidity.do?method=checkILMCurrencyAccess";
		requestURL = requestURL + "&selectedEntityId=" + entityId.trim()
				+ "&selectedCurrencyCode=" + currencyCode.trim();
		var oXMLHTTP = new XMLHttpRequest();
		oXMLHTTP.open("POST", requestURL, false);
		oXMLHTTP.send();
		menuEntityCurrGrpAccess = new String(oXMLHTTP.responseText);
		document.forms[0].menuAccessId.value = "${requestScope.menuAccessId}";
		if(document.forms[0].menuAccessId.value == "0"){
		document.getElementById("changebutton").innerHTML = (isSelected && menuEntityCurrGrpAccess == "0" ? 
				document.getElementById("changeenablebutton").innerHTML : 
				document.getElementById("changedisablebutton").innerHTML);
		document.getElementById("deletebutton").innerHTML = (isSelected && menuEntityCurrGrpAccess == "0" ? 
				document.getElementById("deleteenablebutton").innerHTML : 
				document.getElementById("deletedisablebutton").innerHTML);
		document.getElementById("viewbutton").innerHTML = (isSelected && (menuEntityCurrGrpAccess == "1" || menuEntityCurrGrpAccess == "0") ? 
				document.getElementById("viewenablebutton").innerHTML : 
				document.getElementById("viewdisablebutton").innerHTML);
		}else if(document.forms[0].menuAccessId.value == "1"){
			
		document.getElementById("viewbutton").innerHTML = (isSelected && (menuEntityCurrGrpAccess == "1" || menuEntityCurrGrpAccess == "0") ? 
				document.getElementById("viewenablebutton").innerHTML : 
				document.getElementById("viewdisablebutton").innerHTML);
		}
			
	}

	/**
	 * Disable buttons and colors when the user does not select any row in the grid
	 */

	function disableButtons() {
		document.getElementById("changebutton").innerHTML = document
				.getElementById("changedisablebutton").innerHTML;
		document.getElementById("deletebutton").innerHTML = document.getElementById("deletedisablebutton").innerHTML;
		document.getElementById("viewbutton").innerHTML = document.getElementById("viewdisablebutton").innerHTML;
		updateColors();
	}

	/**
	 * delete the selected role after clicking on delete button
	 * @param methodName
	 */
	function submitDeleteForm(methodName) {
		document.forms[0].method.value = methodName;

		var yourstate = window.confirm('<s:text name="confirm.delete"/>');
		if (yourstate == true) {
			document.forms[0].submit();
		}
	}

	/**
	 * Build URL wich will open the Add/Change the ILAAP Currency Parameters screens for ILM monitoring
	 */

	function buildAddCurrencyURL(methodName) {

		var param = 'intraDayLiquidity.do?method=' + methodName;
			param = param + "&selectedEntityId="
					+ document.forms[0].elements["ilmCcyParams.id.entityId"].value
					+ "&selectedEntityName="
					+ document.getElementById('entityName').innerText;
		if (methodName != "addCurrency")
			param = param + "&selectedCurrencyCode="
					+ document.forms[0].selectedCurrencyCode.value;
		return param;
	}

	function submitForm(methodName) {
		document.forms[0].method.value = methodName;
		document.forms[0].submit();
	}
</SCRIPT>
</head>
<s:form action="intraDayLiquidity.do">
	<input name="method" type="hidden">
	<input name="menuAccessId" type="hidden">
	<input name="selectedEntityId" type="hidden">
	<input name="selectedCurrencyCode" type="hidden">
	<body onmousemove="reportMove()" leftmargin="0" topmargin="0"
		marginheight="0"
		onLoad="bodyOnLoad();setParentChildsFocus();ShowErrMsgWindow('${actionError}');setFocus(document.forms[0]);"
		onunload="call()">

		<div id="ilmccyParamsMaintenance"
			style="position: absolute; left: 10px; top: 10px; width: 978px; height: 42px; border: 2px outset;"
			color="#7E97AF">
			<div id="ilmccyParams"
				style="position: absolute; left: 8px; top: 4px; width: 973px; height: 40;">
				<table width="506" border="0" cellpadding="0" cellspacing="0"
					height="30">
					<tr color="black" border="0">
						<td width="38"><b><s:text name="bookCode.entity" /></b></td>
						<td width="28">&nbsp;</td>
						<td width="140px">
						
							<select name="ilmCcyParams.id.entityId"
									tabindex="1" onchange="submitForm('listCurrencyParams')"
									titleKey="tooltip.selectEntityid" class="htmlTextAlpha"
									style="width: 140px">
										<s:iterator value="#request.entities">

											<option value="<s:property value='value'/>"
												<s:if test="%{ilmCcyParams.id.entityId == value}">selected</s:if>><s:property
													value='label' /></option>
										</s:iterator>
							</select>
							
							</td>
						<td width="20">&nbsp;</td>
						<td width="280"><span id="entityName" name="entityName"
							class="spantext"></td>
					</tr>
				</table>
			</div>
		</div>

		<div id="ilmccyParamsMaintenance"
			style="position: absolute; border: 2px outset; left: 10px; top: 60px; width: 978px; height: 305px;">

			<div id="ilmccyParams"
				style="position: absolute; z-index: 99; left: 0px; top: 0px; width: 958px; height: 10px;">
				<table class="sort-table" id="tableCCyParams" bgcolor="#B0AFAF"
					width="900" border="0" cellspacing="1" cellpadding="0">
					<thead>
						<tr height="22px">
							<td width="80" align="left"
								title='<s:text name="tooltip.sortCurrency"/>'><b><s:text name="ilaapccyparams.currencyGrid" /></b></td>
							<td width="180" align="left"
								title='<s:text name="tooltip.globalCcyAcctGrp"/>'><b><s:text name="ilaapccyparams.globalGrp" /></b></td>
							<td width="90" align="left"
								title='<s:text name="tooltip.defaultMapTime"/>'><b><s:text name="ilaapccyparams.defaultMapTimeGrid" /></b></td>
							<td width="102" align="left"
								title='<s:text name="tooltip.LVPSGrid"/>'><b><s:text name="ilaapccyparams.LVPSGrid" /></b></td>
							<td width="185" align="left"
								title='<s:text name="tooltip.CBGroupId"/>'><b><s:text name="ilaapccyparams.CBGroupId" /></b></td>
							<td width="150" align="left"
								title='<s:text name="tooltip.primaryAccId"/>'><b><s:text name="ilaapccyparams.primaryAccId" /></b></td>
							<td width="80" align="left"
								title='<s:text name="tooltip.clearingStartGrid"/>'><b><s:text name="ilaapccyparams.clearingStartGrid" /></b></td>
							<td width="80" align="left"
								title='<s:text name="tooltip.clearingEndGrid"/>'><b><s:text name="ilaapccyparams.clearingEndGrid" /></b></td>
						</tr>
					</thead>
				</table>
			</div>
			<div id="ddscrolltable"
				style="position: absolute; left: 0px; top: 2px; width: 974px; height: 298; overflowY: scroll">
				<div id="ilmccyParamsMaintenance"
					style="position: absolute; z-index: 99; left: 1px; top: 22px; width: 956px; height: 10px;">
					<table class="sort-table" id="currencyParameterDetails"
						width="900px" border="0" cellspacing="1" cellpadding="0"
						height="275">
						<tbody>
							<%
								int count = 0;
							%>
							<s:iterator value="#request.currencyParamsDetails">
								<%
									if (count % 2 == 0) {
								%><tr height="20px" class="even">
									<%
										} else {
									%>
								
								<tr height="20px" class="odd">
									<%
										}
												++count;
									%>


									<td width="80" align="left"><s:property value="id.currencyCode" />&nbsp;</td>
									<td width="180" align="left"><s:property value="globalGroupId" />&nbsp;</td>
									<td width="90" align="center"><s:property value="defaultMapTime" />&nbsp;</td>
									<td width="102" align="left"><s:property value="lvpsName" />&nbsp;</td>
									<td width="185" align="left"><s:property value="centralBankGroupId" />&nbsp;</td>
									<td width="150" align="left"><s:property value="primaryAccountId" />&nbsp;</td>
									<td width="80" align="center"><s:property value="clearingStartTime" />&nbsp;</td>
									<td width="80" align="center"><s:property value="clearingEndTime" />&nbsp;</td>

								</tr>
							</s:iterator>
						</tbody>
						<tfoot>
							<tr>
								<td colspan="8"></td>
							</tr>
						</tfoot>
					</table>
				</div>
			</div>
			<div id="ilmccyParamsMaintenance"
				style="position: absolute; left: 890; top: 320; width: 70px; height: 29px; visibility: visible;">
				<table width="60px" border="0" cellspacing="0" cellpadding="0"
					height="20">
					<tr>
						<td align="Right" style="padding-top: -2px"><a
							title='<s:text name="tooltip.helpScreen"/>' tabindex="5"
							href=#
							onclick="javascript:openWindow(buildPrintURL('print','ILM Currency Parameter Maintenance'),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')"
							onMouseOut="MM_swapImgRestore()"
							onMouseOver="MM_swapImage('Help','','images/help_default.GIF ',1)"><img
								src="images/help_default.GIF " name="Help" border="0"></a></td>
						<td align="right" id="Print" style="padding-top: 2px">&nbsp; <a tabindex="6"
							onclick="printPage();" onMouseOut="MM_swapImgRestore()"
							onMouseOver="MM_swapImage('Print','','images/Print_R.gif ',1)"><img
								src="images/Print.gif " name="Print" border="0"
								title='<s:text name="tooltip.printScreen"/>'></a>
						</td>
					</tr>
				</table>
			</div>
		</div>
		<div id="ddimagebuttons"
			style="position: absolute; border: 2px outset; left: 10; top: 375; width: 978px; height: 39px; visibility: visible;">
			<div id="ilmccyParamsMaintenance"
				style="position: absolute; left: 2; top: 4; width: 973px; height: 15px; visibility: visible;">
				<table width="285" border="0" cellspacing="0" cellpadding="0"
					height="20">
					<tr>
						<td id="addbutton"></td>
						<td id="changebutton"></td>
						<td id="viewbutton"></td>
						<td id="deletebutton"></td>
						<td id="closebutton" width="70px"><a
							title='<s:text name="tooltip.close"/>' tabindex="4"
							onMouseOut="collapsebutton(this)"
							onMouseOver="highlightbutton(this)"
							onMouseDown="expandbutton(this)"
							onMouseUp="highlightbutton(this)" onclick="confirmClose('P');"><s:text name="button.close" /></a></td>
					</tr>
				</table>
			</div>
			<div
				style="position: absolute; left: 6; top: 4; width: 975px; height: 15px; visibility: hidden; display: none;">
				<table width="350" border="0" cellspacing="0" cellpadding="0"
					height="20" style="visibility: hidden">
					<tr>
						<td id="addenablebutton"><a
							title='<s:text name="tooltip.addILMCcy"/>' tabindex="1"
							onMouseOut="collapsebutton(this)"
							onMouseOver="highlightbutton(this)"
							onMouseDown="expandbutton(this)"
							onMouseUp="highlightbutton(this)"
							onClick="javascript:openWindow(buildAddCurrencyURL('addCurrency'),'addCurrency','left=50,top=190,width=805,height=360,toolbar=0, resizable=yes scrollbars=yes','true')"><s:text name="button.add" /></a></td>
						<td id="adddisablebutton"><a class="disabled"
							disabled="disabled"><s:text name="button.add" /></a></td>
						<td id="changeenablebutton"><a
							title='<s:text name="tooltip.changeILMCcy"/>' tabindex="2"
							onMouseOut="collapsebutton(this)"
							onMouseOver="highlightbutton(this)"
							onMouseDown="expandbutton(this)"
							onMouseUp="highlightbutton(this)"
							onClick="javascript:openWindow(buildAddCurrencyURL('changeCurrency'),'changeCurrency','left=50,top=190,width=805,height=360,toolbar=0, resizable=yes, scrollbars=yes','true')"><s:text name="button.change" /></a></td>
						<td id="changedisablebutton"><a class="disabled"
							disabled="disabled"><s:text name="button.change" /></a></td>
						<td id="viewenablebutton"><a
							title='<s:text name="tooltip.viewILMCcy"/>' tabindex="2"
							onMouseOut="collapsebutton(this)"
							onMouseOver="highlightbutton(this)"
							onMouseDown="expandbutton(this)"
							onMouseUp="highlightbutton(this)"
							onClick="javascript:openWindow(buildAddCurrencyURL('viewCurrency'),'viewCurrency','left=50,top=190,width=805,height=360,toolbar=0, resizable=yes, scrollbars=yes','true')"><s:text name="button.view" /></a></td>
						<td id="viewdisablebutton"><a class="disabled"
							disabled="disabled"><s:text name="button.view" /></a></td>
						<td id="deleteenablebutton"><a
							title='<s:text name="tooltip.deleteILMCcy"/>' tabindex="3"
							onMouseOut="collapsebutton(this)"
							onMouseOver="highlightbutton(this)"
							onMouseDown="expandbutton(this)"
							onMouseUp="highlightbutton(this)"
							onClick="javascript:submitDeleteForm('deleteCurrency');"><s:text name="button.delete" /></a></td>
						<td id="deletedisablebutton"><a class="disabled"
							disabled="disabled"><s:text name="button.delete" /></a></td>
					</tr>
				</table>
			</div>
		</div>

		<blockquote>&nbsp;</blockquote>
		<p>&nbsp;</p>
	</body>
</s:form>
</html>
