<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@ include file="/taglib.jsp"%>
<%@ page import="org.swallow.util.CommonDataManager"%>
<%@ page import="org.swallow.util.SwtConstants"%>
<%@ page import="org.swallow.util.SwtUtil"%>

<html lang="en">
	<head>
<link rel="icon" type="image/x-icon" href="favicon.ico">
<link rel="stylesheet" href="angularSources/styles.css?version=<%= SwtUtil.appVersion %>">
		<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
		<title><s:if test="#request.methodName == 'add'">
			<s:text name="ilmAccountGrpAdd.title.window.addScreen" />
		</s:if> <s:if test="#request.methodName == 'change'">
			<s:text name="ilmAccountGrpAdd.title.window.changeScreen" />
		</s:if> <s:if test="#request.methodName == 'view'">
			<s:text name="ilmAccountGrpAdd.title.window.viewScreen" />
		</s:if><s:if test="#request.methodName == 'addfromILM'">
		<s:text name="ilmAccountGrpAdd.title.window.addScreen" /></s:if>
		</title>
</head>
<body>
<%@ include file="/angularJSUtils.jsp"%>


		<script type="text/javascript">
			var selectedAccountGroup = '${selectedAccountGroup}';
			var methodName = '${methodName}';
			var currencyCode = '${currencyCode}';
			var entityId = '${entityId}';
			var description ='${description}';
			var createdBy = '${createdBy}';
			var createdOn = '${createdOn}';
			var groupDefaultName='${groupDefaultName}';
			var filter ='${filter}';
			var screenRoute = "ilmAccountGroupDetail";
			var parentScreen='${parentScreen}';
			var maintainAnyGroup = '${maintainAnyGroup}';
			var dateFormat ='<s:property value="#request.session.CDM.dateFormat" />';
			var currencyFormat = '<s:property value="#request.session.CDM.currencyFormat" />';
			var ccyPattern = 0;
		
				if(currencyFormat.indexOf("1")!=-1)
					ccyPattern = 0;
				else if(currencyFormat.indexOf("2")!=-1)
					ccyPattern = 1;
 			var label = new Array ();
 			label["text"] = new Array ();
			label["tip"] = new Array ();
			label["text"]["button-save"] = "<s:text name="button.save"/>";
 			label["text"]["button-test"] = "<s:text name="button.test"/>";
 			label["text"]["button-cancel"] = "<s:text name="button.cancel"/>";
 			
 			label["text"]["label-entity"] = "<s:text name="ilmAccountGroupDetails.entity"/>";
 			label["text"]["label-currency"] = "<s:text name="ilmAccountGroupDetails.currency"/>";
 			label["text"]["label-groupId"] = "<s:text name="ilmAccountGroupDetails.grpId"/>";
 			label["text"]["label-pubPriv"] = "<s:text name="ilmAccountGroupDetails.privPub"/>";
 			label["text"]["label-type"] = "<s:text name="ilmAccountGroupDetails.type"/>";
 			label["text"]["label-name"] = "<s:text name="ilmAccountGroupDetails.name"/>";
 			label["text"]["label-description"] = "<s:text name="ilmAccountGroupDetails.description"/>";
 			label["text"]["label-firstMin"] = "<s:text name="ilmAccountGroupDetails.firstMin"/>";
 			label["text"]["label-secondMin"] = "<s:text name="ilmAccountGroupDetails.secondMin"/>";
 			label["text"]["label-firstMax"] = "<s:text name="ilmAccountGroupDetails.firstMax"/>";
 			label["text"]["label-secondMax"] = "<s:text name="ilmAccountGroupDetails.secondMax"/>";
 			label["text"]["label-filterCondition"] = "<s:text name="ilmAccountGroupDetails.filterCondition"/>";
 			label["text"]["label-grpMbrAcct"] = "<s:text name="ilmAccountGroupDetails.grpMemberAccount"/>";
 			label["text"]["label-acctInGrp"] = "<s:text name="ilmAccountGroupDetails.accountInGrp"/>";
 			label["text"]["label-acctNotInGrp"] = "<s:text name="ilmAccountGroupDetails.accountNotInGrp"/>";
 			label["text"]["label-quickSearch"] = "<s:text name="ilmAccountGroupDetails.quickSearch"/>";
 			label["text"]["label-listFromGrp"] = "<s:text name="ilmAccountGroupDetails.listFromGrp"/>";
 			label["text"]["label-createdBy"] = "<s:text name="ilmAccountGroupDetails.createdBy"/>";
 			label["text"]["label-createdOn"] = "<s:text name="ilmAccountGroupDetails.createdOn"/>";
 			label["text"]["label-public"] = "<s:text name="ilmAccountGroupDetails.pub"/>";
 			label["text"]["label-private"] = "<s:text name="ilmAccountGroupDetails.priv"/>";
 			label["text"]["label-dynamic"] = "<s:text name="ilmAccountGroupDetails.dynamic"/>";
 			label["text"]["label-fixed"] = "<s:text name="ilmAccountGroupDetails.fixed"/>";
 			
 			label["tip"]["label-entity"] = "<s:text name="tooltip.selectEntityid"/>";
 			label["tip"]["label-currency"] = "<s:text name="tooltip.selectCurrencyId"/>";
 			label["tip"]["button-cancel"] = "<s:text name="tooltip.cancel"/>";
 			label["tip"]["button-save"] = "<s:text name="tooltip.save"/>";
 			label["tip"]["button-test"] = "<s:text name="tooltip.ILMTestButton"/>";
 			label["tip"]["label-groupId"] = "<s:text name="tooltip.ILMgroupId"/>";
 			label["tip"]["label-pubPriv"] = "<s:text name="tooltip.privPub"/>";
 			label["tip"]["label-type"] = "<s:text name="tooltip.ilmGrpType"/>";
 			label["tip"]["label-name"] = "<s:text name="tooltip.name"/>";
 			label["tip"]["label-description"] = "<s:text name="tooltip.description"/>";
 			label["text"]["label-absoulte-thresholds"] = "<s:text name="ilmAccountGroupDetails.absoluteThresholds"/>";
 			label["text"]["label-net-thresholds"] = "<s:text name="ilmAccountGroupDetails.netThresholds"/>";
 			label["text"]["label-maximum"] = "<s:text name="ilmAccountGroupDetails.maximum"/>";
 			label["text"]["label-minimum"] = "<s:text name="ilmAccountGroupDetails.minimum"/>";
 			label["tip"]["label-firstMin"] = "<s:text name="tooltip.firstMin"/>";
 			label["tip"]["label-secondMin"] = "<s:text name="tooltip.secondMin"/>";
 			label["tip"]["label-firstMax"] = "<s:text name="ilmAccountGroupDetails.firstMax"/>";
 			label["tip"]["label-secondMax"] = "<s:text name="ilmAccountGroupDetails.secondMax"/>";
 			label["tip"]["label-Max"] = "<s:text name="tooltip.Max"/>";
 			label["tip"]["label-Min"] = "<s:text name="tooltip.Min"/>";
 			label["tip"]["label-filterCondition"] = "<s:text name="tooltip.acctGrpFilter"/>";
 			label["tip"]["label-quickSearch"] = "<s:text name="tooltip.quickSearch"/>";
 			label["tip"]["label-listFromGrp"] = "<s:text name="tooltip.listFromGrp"/>";
 			label["tip"]["label-legendText"] = "<s:text name="ilmAccountGroupDetails.tootlipLegendText"/>";
 			
 			label["text"]["label-ilmAccountgrpdetailsTitle"] = "<s:text name="ilmAccountGroupDetails.titleScreen"/>";  			
 			label["text"]["label-accntInGrp"] = "<s:text name="ilmAccountGroupDetails.accntInGrp"/>";
 			label["text"]["label-accntNotInGrp"] = "<s:text name="ilmAccountGroupDetails.accntNotInGrp"/>";
 			label["text"]["label-ilmAccountgrpdetailsTitle"] = "<s:text name="ilmAccountGroupDetails.ilmAccountgrpdetailsTitle"/>";
 			label["text"]["label-showXMLRightGrid"] = "<s:text name="ilmAccountGroupDetails.showXMLRightGrid"/>";
 			label["text"]["label-showXMLLeftGrid"] = "<s:text name="ilmAccountGroupDetails.showXMLLeftGrid"/>";
 			label["text"]["label-invalidAmountAlert"] = "<s:text name="ilmAccountGroupDetails.invalidAmountAlert"/>";
 			label["text"]["label-recordExistsAlert"] = "<s:text name="ilmAccountGroupDetails.recordExistsAlert"/>";
 			label["text"]["label-syntaxAlert"] = "<s:text name="ilmAccountGroupDetails.syntaxAlert"/>";
 			label["text"]["label-syntaxAlertTitle"] = "<s:text name="ilmAccountGroupDetails.syntaxAlertTitle"/>";
 			label["text"]["label-nbOfRecords"] = "<s:text name="ilmAccountGroupDetails.nbOfRecords"/>";
 			label["text"]["label-acctGrpRequired"] = "<s:text name="ilmAccountGroupDetails.acctGrpRequired"/>";
	
 			label["text"]["label-legendText"] = "<s:text name="ilmAccountGroupDetails.labelLegendText"/>";
 			label["text"]["label-id"] = "<s:text name="ilmAccountGroupDetails.labelId"/>";
 			label["text"]["label-name"] = "<s:text name="ilmAccountGroupDetails.labelName"/>";
 			
 			label["text"]["label-allowReporting"] = "<s:text name="ilmAccountGroupDetails.allowReporting"/>";
 			label["tip"]["label-allowReporting"] = "<s:text name="ilmAccountGroupDetails.tootlip.allowReporting"/>";
 			label["text"]["label-netCum"] = "<s:text name="ilmAccountGroupDetails.netCum"/>";
 			label["tip"]["label-netCum"] = "<s:text name="ilmAccountGroupDetails.tootlip.netCum"/>";
 			label["text"]["label-correspBank"] = "<s:text name="ilmAccountGroupDetails.correspBank"/>";
 			label["tip"]["label-correspBank"] = "<s:text name="ilmAccountGroupDetails.tootlip.correspBank"/>";
 			
 			label["text"]["alert-allowReporting"] = "<s:text name="ilmScenario.alert.allowReporting"/>";
 			label["text"]["alert-netMinimum"] = "<s:text name="ilmAccountGroupDetails.alert.netMinimum"/>";
 			label["text"]["alert-netMaximum"] = "<s:text name="ilmAccountGroupDetails.alert.netMaximum"/>";
 			
 			label["text"]["label-mainAgent"] = "<s:text name="ilmAccountGroupDetails.mainAgentText"/>";
 			label["tip"]["label-mainAgent"] = "<s:text name="ilmAccountGroupDetails.tootlip.mainAgentText"/>";
 			
			/**
             * formatCurrency
             * 			
 			 * This method is used to format the amount in system currency format (called by flex) 
			 */
			function formatCurrency(amount){
			
				return validateCurrency(document.forms[0].elements["acctMaintenance.minseepamtasString"],'creditintrates',currencyFormat, document.forms[0].elements['acctMaintenance.currcode'].value);
			}
			
			function validateCurrencyOriginal(strField, currCode){
				var thePat = PatternsDict[currencyFormat]; 
				var strVal = strField;
				var strDecimals = '2';
								   
				if(currCode != 'undefined' && currCode != null ){
					strDecimals = getCurrencyDecimal(currCode);
				}
				return expandMBTValue(strField, thePat, strDecimals, strVal);
			}

			
			function help(){
				  openWindow(buildPrintURL('print','ILM Account Group Details Maintenance'),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true');
			}
			
		
			/**
			* Used to export report as  pdf, xls,csv format
			**/
			function onExport(accountGrpId, exportType){
				
				document.getElementById('exportDataForm').action = 'intraDayLiquidity.do?method=exportAccountGrpDetails';
				document.getElementById('accountGrpId').value = accountGrpId;
				document.getElementById('exportType').value = exportType;
			
				document.forms[0].submit();
		
			}
			
			function refreshParent(){
				<s:if test="#request.parentScreen == 'addIlmCurrencyParameter'">
				 	window.opener.document.forms[0].method.value=window.opener.getStoredParam("methodNameParent");
					window.opener.document.forms[0].selectedEntityId.value = window.opener.document.forms[0].elements["ilmCcyParams.id.entityId"].value;
					window.opener.document.forms[0].currencyCode.value=window.opener.document.forms[0].elements["ilmCcyParams.id.currencyCode"].value;
					window.opener.document.forms[0].selectedEntityName.value=window.opener.document.getElementById("entityName").textContent;
				 	window.opener.document.forms[0].submit();
				 </s:if>
				<s:if test="#request.parentScreen == 'IlmAccountGroupMaintenance'">
					window.opener.document.forms[0].method.value="listAccountGroups";
	 				window.opener.document.forms[0].submit();
				</s:if>
				<s:if test="#request.parentScreen == 'liquidityMonitor'">
					window.opener.refreshGridData();
				</s:if>
 				
			}
		</script>
        <%@ include file="/angularscripts.jsp"%>
		<form id="exportDataForm" target="tmp" method="post">
			<input name="method" type="hidden" value="">
			<input name="accountGrpId" type="hidden"  value="" >
			<input name="exportType" type="hidden"  value="" >
			<input type="hidden" name="screen" value="<s:text name="ilmAccountGrpAdd.title.window.viewScreen" />" />
			<iframe name="tmp" width="0%" height="0%" src="#" />
		</form>
		
		
	</body>
</html>