<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@ include file="/taglib.jsp"  %>
<%@ page import="org.swallow.util.SwtConstants" %>
<html>
<head>
<title>
<s:if test='"save" == #request.methodName' >
	<s:text name="messagefieldformat.addScreen"/>
</s:if>
<s:if test='"save" != #request.methodName' > 
	<s:if test='"update" == #request.methodName' >
		<s:text name="messagefieldformat.changeScreen"/>
	</s:if>
</s:if>

<s:if test='"save" != #request.methodName' > 
<s:if test='"update" != #request.methodName' > 
<s:text name="messagefieldFormats.viewScreen"/>
</s:if>
</s:if>
</title>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">

<SCRIPT language="JAVASCRIPT">
/**
 * When document is loaded, we add 'is-disabled' class for each input disabled.
 * Find each 'tr' that contains a 'td'  has an input
 * which value contains  '*', 
 * then apply the style 'required' for each empty input 
 *
 *Added by Med Amine Ben Ahmed
 **/
 $(window).on("load",function(){	
   	$("input[disabled='disabled'][type='text']").addClass('is-disabled ');
   	$("tr:has(td:has(input[value*= '*']))").each(function()
			{
				var tr = $(this);
				tr.find('input').css('border-color',function(index){
											if ($(this).val() == '')
												return 'red';
										}).change(function(){
											if ($(this).val() == '')
												$(this).css('border','red 1px solid');
									  		else
											{$(this).css('border-color','');
											$(this).addClass('inputText');}
										});	
			});
});
/**
 * When document is ready, we attach a handler to a click event for the radio input
 * to update style and find each 'tr' that contains a 'td' which text ends with '*'
 * then apply the style 'required' for each empty input
 * 
 * Added by Med Amine Ben Ahmed
 **/
$(document).ready(function(){
	
	   $("input[type='radio']").on('click', function(){  
		 	$("input[type='text']").removeClass('is-disabled ');
		   	$("input[disabled='disabled'][type='text']").addClass('is-disabled ');
		    $("input[type='text']").css('border-color','');
		
		    $("tr:has(td:endsWith('*')),tr:has(th:endsWith('*'))").each(function()
			{
				var tr = $(this);
				tr.find('input').css('border-color',function(index){
											if ($(this).val() == '')
												return 'red';
										}).change(function(){
											if ($(this).val() == '')
												$(this).css('border','red 1px solid');
									  		else
									  		{$(this).css('border-color','');
											$(this).addClass('inputText');}
										});	
				 
			});
		});
	
	
});

mandatoryFieldsArray=["*"];
var cancelcloseElements = new Array(1);
cancelcloseElements[0] = "cancelbutton";

var fieldType = "";
var formatId = '${requestScope.formatId}';
var entityId = '${requestScope.entityId}';
var formatType = '${sessionScope.formatTypeInSession}';
var selectedSeqNo = '${requestScope.selectedSeqNo}';
var selectedLineNo = '${requestScope.selectedLineNo}';
var isSeqNoExisting = '${requestScope.isSeqNoExisting}';
var isLineNoExisting = '${requestScope.isLineNoExisting}';
var selectedStartPos = '${requestScope.selectedStartPos}';
var selectedEndPos = '${requestScope.selectedEndPos}';
<s:if test='"yes" == #request.parentFormRefresh' >
	window.opener.document.forms[0].method.value="display";
	window.opener.document.forms[0].isSeqNoExisting.value = isSeqNoExisting;
	window.opener.document.forms[0].submit();
self.close();
</s:if>

function submitForm(methodName){

 <s:if test='"Y" == #request.showPosition' > 
	  <s:if test='"Y" == #request.showLineNo' > 
		<s:if test='"Y" == #request.disableEndPos' >  {
        
			if (validateFormFormatTypeTaggedVariable(document.forms[0])) {
				 
			if(validateField(document.forms[0].elements['messageFields.lineNoAsString'],'message.SequenceNumber','numberPat'))
			{  	
				if(validateField(document.forms[0].elements['messageFields.startPos'],'messageFields.endPos','numberPat'))
				{				
				if(!(checkLineNoForPositiveValue()))
				{
					alert('<s:text name="messagefieldadd.alert.LineNumber"/>');
					document.forms[0].elements["messageFields.lineNoAsString"].focus();
		
				}
				else
				  {
					document.forms[0].method.value = methodName;
					document.forms[0].elements["messageFields.lineNoAsString"].disabled = false;
					document.forms[0].elements["messageFields.seqNoAsString"].disabled = false;
					document.forms[0].selectedSeqNo.value = selectedSeqNo;
					document.forms[0].selectedLineNo.value = selectedLineNo;
					document.forms[0].selectedStartPos.value = selectedStartPos;
					document.forms[0].selectedEndPos.value = selectedEndPos;
				  document.forms[0].elements["messageFields.value"].value =  getMenuWindow().encode64(document.forms[0].elements["messageFields.value"].value);
					document.forms[0].submit();
				} 
                }else{
				document.forms[0].elements['messageFields.startPos'].focus();  
                }
				}else{
				document.forms[0].elements['messageFields.lineNoAsString'].focus();
				}
			}

			} 
		</s:if>
			<s:if test='"Y" != #request.disableEndPos' > 
			if(validateFormFormatTypeTagged(document.forms[0]))
			{
			if(validateField(document.forms[0].elements['messageFields.lineNoAsString'],'message.SequenceNumber','numberPat'))
			{
				if(validateField(document.forms[0].elements['messageFields.startPos'],'messageFields.endPos','numberPat'))
				{
				if(validateField(document.forms[0].elements['messageFields.endPos'],'messageFields.endPos','numberPat'))
				{
				if(!(checkLineNoForPositiveValue()))
				{
					alert('<s:text name="messagefieldadd.alert.LineNumber"/>');
					document.forms[0].elements["messageFields.lineNoAsString"].focus();
		
				}else if(comparePosition() == "0")
				{
					alert('<s:text name="messagefieldadd.alert.StartPosition"/>');
					document.forms[0].elements["messageFields.startPos"].focus();
					
				}else if(comparePosition() == "1")
				{
					alert('<s:text name="messagefieldadd.alert.EndPosition"/>');
					document.forms[0].elements["messageFields.endPos"].focus();
					
				}else if(comparePosition() == "2")
				{
					
					alert('<s:text name="messagefieldadd.alert.EPSP"/>');
				}else if(!isHex()) {
					alert('<s:text name="alert.comboBox.invalidValue"/>');
					document.forms[0].elements["messageFields.value"].focus();
				}
				else if(!valueLengthValidate())
				{
					alert('<s:text name="messagefieldadd.alert.space"/>');
					if(fieldType != "<%=SwtConstants.FIELD_TYPE_KEYWORD%>")
						document.forms[0].elements["messageFields.value"].focus();

				}else {
					document.forms[0].method.value = methodName;
					document.forms[0].selectedSeqNo.value = selectedSeqNo;
					document.forms[0].elements["messageFields.lineNoAsString"].disabled = false;
					document.forms[0].selectedLineNo.value = selectedLineNo;
					
					document.forms[0].selectedStartPos.value = selectedStartPos;

					document.forms[0].selectedEndPos.value = selectedEndPos;
					document.forms[0].elements["messageFields.value"].value =  getMenuWindow().encode64(document.forms[0].elements["messageFields.value"].value);
					document.forms[0].submit();
				} 
		}else{
		document.forms[0].elements['messageFields.endPos'].focus();  
        }				
        }else{
		document.forms[0].elements['messageFields.startPos'].focus();  
        }	
		}else{
		document.forms[0].elements['messageFields.lineNoAsString'].focus();
		}		
		}
		 </s:if>
	  </s:if>

	  <s:if test='"Y" != #request.showLineNo' > 
		if(validateFormFixed(document.forms[0]))
			{
					if( validateField(document.forms[0].elements['messageFields.startPos'],'message.StartPosition','numberPat'))
					{
					if( validateField(document.forms[0].elements['messageFields.endPos'],'messageFields.endPos','numberPat'))
					{
				if(comparePosition() == "0")
				{
					alert('<s:text name="messagefieldadd.alert.StartPosition"/>');
					document.forms[0].elements["messageFields.startPos"].focus();
				}else
				if(comparePosition() == "1")
				{
					alert('<s:text name="messagefieldadd.alert.EndPosition"/>');
					document.forms[0].elements["messageFields.endPos"].focus();
				}else				
				if(comparePosition() == "2")
				{
					alert('<s:text name="messagefieldadd.alert.EPSP"/>');
				}
				else if(!isHex()) {
					alert('<s:text name="alert.comboBox.invalidValue"/>');
					document.forms[0].elements["messageFields.value"].focus();
				}else if(!valueLengthValidate()) {
					alert('<s:text name="messagefieldadd.alert.space"/>');
					if(fieldType != "<%=SwtConstants.FIELD_TYPE_KEYWORD%>")
						document.forms[0].elements["messageFields.value"].focus();
				}else
					
				  {					
					document.forms[0].method.value = methodName;
					document.forms[0].selectedSeqNo.value = selectedSeqNo;
					document.forms[0].selectedLineNo.value = selectedLineNo;
					document.forms[0].elements["messageFields.startPos"].disabled = false;					
					document.forms[0].selectedStartPos.value = selectedStartPos;
					document.forms[0].selectedEndPos.value = selectedEndPos;
					  document.forms[0].elements["messageFields.value"].value =  getMenuWindow().encode64(document.forms[0].elements["messageFields.value"].value);
					document.forms[0].submit();
				}  
				}else
				{
				document.forms[0].elements['messageFields.endPos'].focus();
				
				}
				}else
				{
					document.forms[0].elements['messageFields.startPos'].focus();
				}
		}
		</s:if>
  </s:if>
	
<s:if test='"Y" != #request.showPosition' > 
	if(validateFormFormatTypeDelimited(document.forms[0]))
	{
	 if(validateField(document.forms[0].elements['messageFields.seqNoAsString'],'message.SequenceNumber','numberPat'))
	 {
	   
		if(!(checkSeqNoForPositiveValue()))
		{
			alert('<s:text name="messagefieldadd.alert.sequenceNo"/>');
			document.forms[0].elements["messageFields.seqNoAsString"].focus();

		}else
		{
			document.forms[0].method.value = methodName;
			document.forms[0].elements["messageFields.seqNoAsString"].disabled = false; 
			document.forms[0].selectedSeqNo.value = selectedSeqNo;			
			document.forms[0].selectedLineNo.value = selectedLineNo;
			document.forms[0].selectedStartPos.value = selectedStartPos;
			document.forms[0].selectedEndPos.value = selectedEndPos;
			document.forms[0].elements["messageFields.value"].value =  getMenuWindow().encode64(document.forms[0].elements["messageFields.value"].value);
			document.forms[0].submit();
		}
	
	}else
	{
		document.forms[0].elements['messageFields.seqNoAsString'].focus();
	}
	}

  </s:if>
  
}

 function checkFieldType()
 {
	

	fieldType = '${requestScope.fieldType}'
	
	if( fieldType == "<%=SwtConstants.FIELD_TYPE_KEYWORD%>")
	{
		document.getElementById("MessageFieldsAddDropDown").style.visibility = "visible";
		document.forms[0].elements["messageFields.value"].value = "";
		document.getElementById("MessageFieldsAddTextField").style.visibility = "hidden";
	}
 }

function insertValidate() {
	document.forms[0].insertFlag.value = '${requestScope.insertOperation}';

	if (document.forms[0].elements["insertFlag"].value == "Y") {

	 if(formatType == "<%=SwtConstants.FORMAT_TYPE_FIXED%>") {	
	 document.forms[0].elements["messageFields.startPos"].disabled = true;

	}else if (formatType == "<%=SwtConstants.FORMAT_TYPE_TAGGED%>") {
				document.forms[0].elements["sequenceNoMandatory"].value = "";	
				document.forms[0].elements["messageFields.lineNoAsString"].disabled = "true";
				document.forms[0].elements["lineNoMandatory"].value = "";				
				
		} else if( formatType == "<%=SwtConstants.FORMAT_TYPE_TAGGED_VARIABLE%>"){
				document.forms[0].elements["messageFields.lineNoAsString"].disabled = "true";
				document.forms[0].elements["lineNoMandatory"].value = "";
		} else{

				document.forms[0].elements["messageFields.seqNoAsString"].disabled = "true"; 
				document.forms[0].elements["sequenceNoMandatory"].value = "";
	} 
 }
}
function validateFormFixed(objForm){
  var elementsRef = new Array(3);
  elementsRef[0] = objForm.elements["messageFields.startPos"];
  elementsRef[1] = objForm.elements["messageFields.endPos"];
	if(fieldType != "<%=SwtConstants.FIELD_TYPE_KEYWORD%>")
	 elementsRef[2] = objForm.elements["messageFields.value"];
  else
	 elementsRef[2] = objForm.elements["messageFields.valueKeyWord"];
	
  return validate(elementsRef);
}


function validateFormFormatTypeDelimited(objForm){

  var elementsRef = new Array(2);
 
  elementsRef[0] = objForm.elements["messageFields.seqNoAsString"];
  if(fieldType != "<%=SwtConstants.FIELD_TYPE_KEYWORD%>")
	 elementsRef[1] = objForm.elements["messageFields.value"];
  else
	 elementsRef[1] = objForm.elements["messageFields.valueKeyWord"];
  
  
  return validate(elementsRef);
}

function validateFormFormatTypeTagged(objForm){
	
  var elementsRef = new Array(4);
  elementsRef[0] = objForm.elements["messageFields.startPos"];
  elementsRef[1] = objForm.elements["messageFields.endPos"];
  elementsRef[2] = objForm.elements["messageFields.lineNoAsString"];
  if(fieldType != "<%=SwtConstants.FIELD_TYPE_KEYWORD%>")
	 elementsRef[3] = objForm.elements["messageFields.value"];
  else
	 elementsRef[3] = objForm.elements["messageFields.valueKeyWord"];
  
  return validate(elementsRef);
}

function validateFormFormatTypeTaggedVariable(objForm){

  var elementsRef = new Array(3);
  elementsRef[0] = objForm.elements["messageFields.startPos"];  
  elementsRef[1] = objForm.elements["messageFields.lineNoAsString"];
  if(fieldType != "<%=SwtConstants.FIELD_TYPE_KEYWORD%>")
	 elementsRef[2] = objForm.elements["messageFields.value"];
  else
	 elementsRef[2] = objForm.elements["messageFields.valueKeyWord"];
  
  return validate(elementsRef);
}

function showKeyWordsDropDown(element)
{
	
	fieldType = element.value;
	
	if(element.value == "<%=SwtConstants.FIELD_TYPE_KEYWORD%>")
	{
		document.getElementById("MessageFieldsAddDropDown").style.visibility = "visible";
		document.getElementById("MessageFieldsAddTextField").style.visibility = "hidden";
	}else
	{
		document.getElementById("MessageFieldsAddDropDown").style.visibility = "hidden";
		document.getElementById("MessageFieldsAddTextField").style.visibility = "visible";
		document.forms[0].elements["messageFields.value"].value = "";
	}
}

function valueLengthValidate()
{
	var temp = document.forms[0].elements["messageFields.startPos"].value;
	//Removing zeros which are present before first non zero digits in the start position field
	var i = 0;
	for(i=0; i<temp.length ; i++)
			{
				if (temp.charAt(i) != '0')
				{
						break;			
				}
			}
	temp = temp.substring(i);
	var startPosition = parseInt(temp);
	temp = document.forms[0].elements["messageFields.endPos"].value;
	//Removing zeros which are present before first non zero digits in the end position field
	var i = 0;
	for(i=0; i<temp.length ; i++)
			{
				if (temp.charAt(i) != '0')
				{
						break;			
				}
			}
	temp = temp.substring(i);
	var endPosition = parseInt(temp);
	 var diff = endPosition - startPosition +1;
	 var textLength ;
	 
	 if(fieldType != "<%=SwtConstants.FIELD_TYPE_KEYWORD%>")
		   textLength = document.forms[0].elements["messageFields.value"].value;
	 else{
		if(startPosition > 0){
			return true;
		}else{
			return false;
		}
	 }
	 if(fieldType == "<%=SwtConstants.FIELD_TYPE_HEXADECIMAL%>") {
		 if( diff >= (textLength.length/2))
		 {
			
			return true;
		 }
		 else
		 {
			 return false;
		 }
	 }else {
	 if( diff >= textLength.length)
	 {
		return true;
	 }
	 else
	 {
		 return false;
	 }
	}
}


function isHex() {
	 var textValue ;
	 textValue = document.forms[0].elements["messageFields.value"].value;
	 if(fieldType == "<%=SwtConstants.FIELD_TYPE_HEXADECIMAL%>"){
		 if((textValue.length)%2 == 0) {
			var a = parseInt(textValue,16);
			return (a.toString(16).toUpperCase() === textValue.toUpperCase())
		 }else{
			return false;
		 }
		 
	}else {
		return true;
	}
}

function comparePosition()
{

var temp = document.forms[0].elements["messageFields.startPos"].value;
//Removing zeros which are present before first non zero digits in the start position field
var i = 0;
for(i=0; i<temp.length ; i++)
		{
			if (temp.charAt(i) != '0')
			{
					break;			
			}
		}
temp = temp.substring(i);
var startPosition = parseInt(temp);
temp = document.forms[0].elements["messageFields.endPos"].value;
//Removing zeros which are present before first non zero digits in the end position field
var i = 0;
for(i=0; i<temp.length ; i++)
		{
			if (temp.charAt(i) != '0')
			{
					break;			
			}
		}
temp = temp.substring(i);
var endPosition = parseInt(temp);

   if(startPosition == 0)
	{
	return "0";
	}else if(endPosition == 0)
	 {
	return "1";
	}else if(startPosition > endPosition)
	{
			 return "2";		 
	}
	else
	{
		return "3";
	}
}

function checkLineNoForPositiveValue()
{
	var lineNo = document.forms[0].elements["messageFields.lineNoAsString"].value;
	if(lineNo != "0")
	  return true;
	 else 
	  return false;
}

function checkSeqNoForPositiveValue()
{
	 var seqNo = document.forms[0].elements["messageFields.seqNoAsString"].value;
	  if(seqNo != "0")
		return true;
	  else 
		return false;
}

function checkformatType()
{	
		 <s:if test='"yes" != #request.isViewField' >			  
			 if(formatType == "<%=SwtConstants.FORMAT_TYPE_FIXED%>" || formatType == "<%=SwtConstants.FORMAT_TYPE_TAGGED%>"
			  ||formatType == "<%=SwtConstants.FORMAT_TYPE_TAGGED_VARIABLE%>")
			 {
				document.forms[0].elements["messageFields.seqNoAsString"].disabled = "true"; 				
				document.forms[0].elements["sequenceNoMandatory"].value = "";
			 }else
				document.forms[0].elements["sequenceNoMandatory"].value = "*";	
		</s:if>
}
</SCRIPT>

</head>
<!--code modified by sandeepkumar for mantis 2093:value changed while range overlapping -->
<body leftmargin="0" topmargin="0" marginheight="0"  onLoad="setParentChildsFocus();setFocus(document.forms[0]);ShowErrMsgWindow('${actionError}'); checkFieldType(); checkformatType();insertValidate();" onunload="call()">
<s:form action="messagefields.do"  onsubmit="return validate(this);">
<input name="oldValue" type="hidden" value= "">
<input name="method" type="hidden" value="save">
<input name="formatId" type="hidden" value="save">
<input name="formatType" type="hidden" value="save">
<input name="entityId" type="hidden" value="save">
<input name="selectedSeqNo" type="hidden" value="">
<input name="selectedLineNo" type="hidden" value="">
<input name="selectedStartPos" type="hidden" value="">
<input name="selectedEndPos" type="hidden" value="">
<input name="insertFlag" type="hidden" value="">

<div id="MessageFieldsAdd" color="#7E97AF" style="position:absolute; border:2px outset; left:20px; top:20px; width:640px; height:133px;">
	<div id="MessageFieldsAdd" style="position:absolute;z-index:99;left:8px; top:4px; width:457px; height:10px;">
	  <table width="475px" height="70px" border="0" cellpadding="0" cellspacing="1" class="content">
		<tr heigth="25px">
		 <%if ( SwtConstants.YES.equals(request.getAttribute("showLineNo"))	) {	%>
		   <s:if test='"yes" == #request.isViewField' >
			 <td width="128px"><b><s:text name="messageFields.lineNoDisplay"/></b></td>
		   </s:if>

		    <s:if test='"yes" != #request.isViewField' >
			 <td width="128px"><b><s:text name="messageFields.lineNoDisplay"/></b></b><input  value = "*" class="textAlpha" style="background:transparent; border: thin;" class="textAlpha" readonly name="lineNoMandatory" size="1"></td>
		    </s:if>
		  <%} else {%>
			 <td width="128px"><b><s:text name="messageFields.lineNoDisplay"/></b></td>
		  <%}%>

		 <%if ( SwtConstants.YES.equals(request.getAttribute("showLineNo"))	) {	%>
			  <td width="22px"><s:textfield tabindex="1" titleKey="tooltip.selectLine" name="messageFields.lineNoAsString" cssStyle="width:45px;" cssClass="htmlTextNumeric" maxlength = "5" disabled ="%{#attr.screenFieldsStatus}" onchange="return validateField(this,'message.SequenceNumber','numberPat');"/> </td>
		 <%} else {%>
			  <td width="22px"><s:textfield tabindex="1"  cssClass="htmlTextNumeric" disabled = "true" name="messageFields.lineNoAsString"  titleKey="tooltip.selectLine" cssStyle="width:45px;" maxlength = "5" onchange="return validateField(this,'message.SequenceNumber','numberPat');"/> </td>
		 <%}%>

	   </tr>
		 	<tr heigth="25px">
		    <td width="128px"><nobr><b><s:text name="messageFields.sequenceNo"/></b><input  class="textAlpha" style="background:transparent; border: thin;" class="textAlpha" readonly name="sequenceNoMandatory" size="1"></nobr></td>
		    <td ><s:textfield tabindex="2" titleKey="tooltip.enterSeqNo" cssClass="htmlTextNumeric" maxlength = "5" name="messageFields.seqNoAsString" cssStyle="width:45px;" disabled ="%{#attr.screenFieldsStatus}" onchange="return validateField(this,'message.SequenceNumber','numberPat');"/></td>
		</tr>
		
	<tr heigth="25px">
			  <td width="128px"><b><s:text name="message.FieldType"/></b></td>
			  <td width="250px">
				<s:radio titleKey="tooltip.selectText" id="1" cssStyle="width:13;" tabindex="3" name="messageFields.fieldType" list="#{@org.swallow.util.SwtConstants@FIELD_TYPE_TEXT:''}" disabled ="%{#attr.screenFieldsStatus}" onclick = "showKeyWordsDropDown(this)" />
					<label tabindex="3" title='<s:text name="tooltip.selectText"/>' Select text" for="1"><s:text name="messageFields.fieldType.text"/></label>&nbsp;&nbsp;&nbsp;
				<s:radio titleKey="tooltip.selectKeyword" id="2" cssStyle="width:13;" tabindex="4" name="messageFields.fieldType" list="#{@org.swallow.util.SwtConstants@FIELD_TYPE_KEYWORD:''}" disabled ="%{#attr.screenFieldsStatus}" onclick = "showKeyWordsDropDown(this)" />
					<label tabindex="4" title='<s:text name="tooltip.selectKeyword"/>' for="2"><s:text name="messageFields.fieldType.keyword"/></label>&nbsp;&nbsp;&nbsp;
				<s:radio titleKey="tooltip.selectHexa" id="3" cssStyle="width:13;" tabindex="5" name="messageFields.fieldType" list="#{@org.swallow.util.SwtConstants@FIELD_TYPE_HEXADECIMAL:''}" disabled ="%{#attr.screenFieldsStatus}" onclick = "showKeyWordsDropDown(this)" />
					<label tabindex="5" title='<s:text name="tooltip.selectHexa"/>' for="3"><s:text name="messageFields.fieldType.hexaDecimal"/></label></td> 
			</tr> 			
	  </table>
	</div>
	

	<div id="MessageFieldsAddTextField" style="position:absolute;z-index:99;left:8px; top:73px; width:457px; height:5px; visibility:visible ">
		 <table width="626px" height="20px" border="0" cellpadding="0" cellspacing="1" class="content">
			<tr height="25px" >
			<s:if test='"yes" == #request.isViewField' >
			  <td width="128px"><b><s:text name="message.Value"/></b></td>
			</s:if>

			<s:if test='"yes" != #request.isViewField' >
			  <td width="128px"><b><s:text name="message.Value"/></b>*</td>
			</s:if>

			  <td width="45px">&nbsp;</td>
			  <td width="445px">
				<s:textfield tabindex="6" titleKey="tooltip.enterValue" disabled ="%{#attr.screenFieldsStatus}" maxlength = "50" cssClass="htmlTextAlpha" name="messageFields.value" cssStyle="width:440px;" /> 
			  </td>
			</tr> 
		 </table>
	</div>

<div id="MessageFieldsAddDropDown" style="position:absolute;z-index:99;left:8px; top:73px; width:457px; height:5px; visibility:hidden">
		 <table width="437px" height="20px" border="0" cellpadding="0" cellspacing="1" class="content">
			<tr  height="25px">
			 <s:if test='"yes" == #request.isViewField' >
			  <td width="120px"><b><s:text name="message.Value"/></b></td>
			 </s:if>

			<s:if test='"yes" != #request.isViewField' >
			  <td width="120px"><b><s:text name="message.Value"/></b>*</td>
			</s:if>
			  <td width="36px">&nbsp;</td>
			  <td width="230px">
			<s:select id="messageFields.valueKeyWord" name="messageFields.valueKeyWord" disabled ="%{#attr.screenFieldsStatus}" tabindex="3" titleKey="tooltip.enterValue"  cssStyle="width:190px " list="#request.keyWords" listKey="value" listValue="label" />
			  
			  </td>
			</tr> 
		 </table>
</div>
<div id="MessageFieldsAdd" style="position:absolute;z-index:99;left:8px; top:96px; width:457px; height:10px;">
 <table width="438px" height="20px" border="0" cellpadding="0" cellspacing="1" class="content">
	<tr height="25px">
		 <%if ( SwtConstants.YES.equals(request.getAttribute("showPosition"))	) {	%>
			<s:if test='"yes" == #request.isViewField' >
				<td width="120px"><b><s:text name="message.StartPosition"/></b></td>
			</s:if>

			<s:if test='"yes" != #request.isViewField' >
				<td width="120px"><b><s:text name="message.StartPosition"/></b>*</td>
			</s:if>

		 <%} else {%>
			 <td width="120px"><b><s:text name="message.StartPosition"/></b></td>
		 <%}%>	

		  <td width="36px">&nbsp;</td>	
		  
		  <%if ( SwtConstants.YES.equals(request.getAttribute("showPosition"))	) {	%>
			 <td width="38px"> <s:textfield titleKey="tooltip.enterStartPosition" disabled ="%{#attr.screenFieldsStatus}" cssClass="htmlTextNumeric" name="messageFields.startPos" maxlength = "4"  cssStyle="width:37px;" tabindex="7"  onchange="return validateField(this,'message.StartPosition','numberPat');"/></td>
		  <%} else {%>
			  <td width="38px"> <s:textfield  titleKey="tooltip.enterStartPosition" cssClass="htmlTextNumeric" maxlength = "4" disabled = "true" name="messageFields.startPos"  tabindex="7" cssStyle="width:37px;" onchange="return validateField(this,'message.StartPosition','numberPat');"/></td>
		  <%}%>
		 
		  <td width="28px">&nbsp;</td>	
		   <%if ( SwtConstants.YES.equals(request.getAttribute("showPosition"))	) {	%>
			 <s:if test='"yes" == #request.isViewField' >
				<td width="90px"><b><s:text name="message.EndPosition"/></b></td>
			</s:if>

			<s:if test='"yes" != #request.isViewField' >
			  <s:if test='"Y" == #request.disableEndPos' >
				<td width="90px"><b><s:text name="message.EndPosition"/></b></td>
			  </s:if>

			  <s:if test='"Y" != #request.disableEndPos' >
				<td width="90px"><b><s:text name="message.EndPosition"/></b>*</td>
			  </s:if>

			</s:if>
		   <%} else {%>
			 <td width="90px"><b><s:text name="message.EndPosition"/></b></td>		
		   <%}%>

		  <td width="25px">&nbsp;</td>	
		  <%if ( SwtConstants.YES.equals(request.getAttribute("showPosition"))	) {	%> 
		    <s:if test='"Y" == #request.disableEndPos' >
				 <td width="47px"><s:textfield  titleKey="tooltip.enterEndPosition" disabled = "true" cssClass="htmlTextNumeric" maxlength = "4"  name="messageFields.endPos" tabindex="8" cssStyle="width:37px;" onchange="return validateField(this,'message.EndPosition','numberPat');"/></td>
			</s:if>

			<s:if test='"Y" != #request.disableEndPos' >
				 <td width="47px"><s:textfield  titleKey="tooltip.enterEndPosition" disabled ="%{#attr.screenFieldsStatus}" cssClass="htmlTextNumeric" maxlength = "4"  name="messageFields.endPos" tabindex="8" cssStyle="width:37px;" onchange="return validateField(this,'message.EndPosition','numberPat');"/></td>
			</s:if>

		  <%} else {%>
			 <td width="47px"><s:textfield  titleKey="tooltip.enterEndPosition" cssClass="htmlTextNumeric" maxlength = "4" disabled = "true" name="messageFields.endPos" tabindex="8" cssStyle="width:37px;" onchange="return validateField(this,'message.EndPosition','numberPat');"/></td>
		  <%}%>
	   </tr>
 </table>
 <div>

</div>
</div>
<div id="MessageFieldsAdd" style="position:absolute; left:563; top:146px; width:70px; height:15px; visibility:visible;">
	<table width="60px" border="0" cellspacing="0" cellpadding="0" height="20">
		<tr>
			<td align="Right">
				 <a tabindex="11" href=# onclick="javascript:openWindow(buildPrintURL('print','Add Message Field Maintenance'),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')" onMouseOut="MM_swapImgRestore()" onMouseOver="MM_swapImage('Help','','images/help_default.GIF ',1)"><img src="images/help_default.GIF " name="Help"  border="0" title='<s:text name="tooltip.helpScreen"/>'></a> 
			 </td>

			<td align="right" id="Print">
				<a tabindex="11" onclick="printPage();" onMouseOut="MM_swapImgRestore()" onMouseOver="MM_swapImage('Print','','images/Print_R.gif ',1)"><img src="images/Print.gif " name="Print"  border="0" title='<s:text name="tooltip.printScreen"/>'></a>	
			</td>
		</tr>
	</table>
</div>


<div id="ddimagebuttons" color="#7E97AF" style="position:absolute; border:2px outset; left:-2px; top:136px; width:640px; height:39px; visibility:visible;">
 <div id="MessageFieldsAdd" style="position:absolute; left:0px; top:4; width:140px; height:15px; visibility:visible;"> 
   <s:if test='"yes" != #request.isViewField' >
		<table width="140" border="0" cellspacing="0" cellpadding="0" height="20">
		<tr>
			<td id="savebutton" width="70px" title='<s:text name="tooltip.ok"/>' >		
				<a tabindex="9" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onclick="javascript:submitForm('${requestScope.methodName}');"><s:text name="button.ok"/></a>
			</td>		
			<td id="cancelbutton" width="70px" title='<s:text name="tooltip.cancel"/>' >		
				<a tabindex="10" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onclick="javascript:window.close();"><s:text name="button.cancel"/></a>			
				</td>
		</tr>
	   </table>
  </s:if>

  <s:if test='"yes" == #request.isViewField' >
		<table width="70" border="0" cellspacing="0" cellpadding="0" height="20">
		<tr>
			<td id="cancelbutton" width="70px" title='<s:text name="tooltip.close"/>'>		
				<a tabindex="10" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onclick="confirmClose('C');"><s:text name="button.close"/></a>			
				</td>
		</tr>
	   </table>
  </s:if>

  </div>
 
 <script language = "javascript">
<s:if test='"yes" == #request.isSeqNoExisting' >
	checkformatType();
	alert('<s:text name="messagefieldadd.alert.duplicateSeqNo"/>');
</s:if>

<s:if test='"yes" == #request.isRangeOverlapping' >
	alert('<s:text name="messagefieldadd.alert.rangeOverlapping"/>');
</s:if>

<s:if test='"Y" == #request.recordExists' >
	alert('<s:text name="messagefieldadd.alert.isStartPosExisting"/>');
</s:if>

</script>

</s:form>
</body>
</html>