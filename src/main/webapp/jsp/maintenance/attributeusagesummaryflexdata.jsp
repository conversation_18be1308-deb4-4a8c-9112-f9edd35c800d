<?xml version="1.0" encoding="UTF-8"?>

<%@ page contentType="text/xml"%>
<%@ page import="org.swallow.util.SwtConstants"%>
<%@ taglib prefix="s" uri="/struts-tags" %>
<%@ taglib uri="http://java.sun.com/jstl/fmt_rt" prefix="fmt"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>



<attributeusagesummary name="attributeusagesummary" restriction="${restriction}">

	<request_reply> 
		<status_ok>
			<s:property value='#request.reply_status_ok' />
		</status_ok> 
		<message>
			<s:property value='#request.reply_message' />
		</message>
		<location /> 
	</request_reply> 

<grid>
	<metadata>
		<columns>
		 <s:iterator value='#request.column_order' var='order' >
		 <s:if test='"attribute_id"==#order' >
			<column heading="<s:text name="attributeusagesummary.attributeId" />"
					draggable="true"					
					filterable="true"
					type="str"
					dataelement="attribute_id"
					sort="false"
					width="<s:property value='#request.column_width.attribute_id' />"/>
			</s:if>
			<s:if test='"name"==#order' >
			<column heading="<s:text name="attributeusagesummary.name" />"
					draggable="true"					
					filterable="false"
					type="str"
					dataelement="name"
					sort="false"
					width="<s:property value='#request.column_width.name' />"/>
			</s:if>
			<s:if test='"type"==#order' >
			<column heading="<s:text name="attributeusagesummary.type" />"
					draggable="true"					
					filterable="false"
					type="str"
					dataelement="type"
					sort="true"
					width="<s:property value='#request.column_width.type' />"/>
			</s:if>
			<s:if test='"display"==#order' >
			<column heading="<s:text name="attributeusagesummary.display" />"
					draggable="true"					
					filterable="false"
					type="num"
					dataelement="display"
					sort="true"
					width="<s:property value='#request.column_width.display' />"/>	
			</s:if>
				</s:iterator>		
		</columns>	
	</metadata>

	<rows size="${rowSize}">
		<s:iterator value='#request.acctAttrFunGroupList' var='acctAttrFunGroupList' >
			<row left_hidden="true" right_hidden="false" original="true" filtred="true" shared="false"  >
				<attribute_id clickable="false">
					<s:property value='#acctAttrFunGroupList.id.attributeId' />
				</attribute_id>
				<name clickable="false">
					<s:property value='#acctAttrFunGroupList.accountAttributeHDR.attributeName' />
				</name>
				<type clickable="false">
					<s:if test='"T"==#acctAttrFunGroupList.accountAttributeHDR.valueType' ><s:text name="type.text" /></s:if>
					<s:if test='"N"==#acctAttrFunGroupList.accountAttributeHDR.valueType' ><s:text name="type.numeric" /></s:if>
					<s:if test='"D"==#acctAttrFunGroupList.accountAttributeHDR.valueType' ><s:text name="type.date" /></s:if>
				</type>
				<display clickable="false">
					<s:property value='#acctAttrFunGroupList.displayOrder' />
				</display>
			</row>
		</s:iterator>
	</rows>
</grid>

<selects> 
	<select id="functionalGrpList"> 
		<s:iterator value='#request.functionalGrpList' var='functionalGrps' >
		
			<option value="<s:property value='#functionalGrps.id.functionalGroup' />"
					selected="<s:if test='#request.selectedFunctionalGrp==#functionalGrps.id.functionalGroup' >1</s:if><s:if test='#request.selectedFunctionalGrp!=#functionalGrps.id.functionalGroup' >0</s:if>"><s:property value='#functionalGrps.functionalGroupDesc' />
			</option>
		</s:iterator> 
	</select> 
</selects>
</attributeusagesummary>
