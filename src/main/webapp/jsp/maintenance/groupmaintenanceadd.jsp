<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@ include file="/taglib.jsp"%>
<%@ page import="org.swallow.util.SwtConstants" %>
<html>
<head>
<title>
<s:if test="#request.methodName == 'add'">
	<s:text name="group.addScreen"/>
</s:if>
<s:else> 
	<s:if test="#request.methodName == 'change'">
		<s:text name="group.changeScreen"/>
	</s:if>
</s:else>
</title>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">


<SCRIPT language="JAVASCRIPT">

var cancelcloseElements = new Array(1);
	cancelcloseElements[0]="cancelbutton";
	mandatoryFieldsArray=["*"];
	<s:if test="#request.parentFormRefresh == 'yes'">
window.opener.document.forms[0].method.value="displayList";
window.opener.document.forms[0].submit();
self.close();
</s:if>

var entityAccess = "${requestScope.EntityAccess}";
/**
* This method is used to submit the form when click the save button.
*
* @param methodName
* @return none
**/

function submitForm(methodName){

	var groupId=validateField(document.forms[0].elements['group.id.groupId'],'group.groupIdadd','alphaNumPat');
	
	
	if(groupId)
	{

	  var groupName=validateField(document.forms[0].elements['group.groupName'],'group.groupNameadd','alphaNumPatExtended');
	  if(groupName)
	 { 
	 var cutOffTime=validateField(document.forms[0].elements['group.cutoffOffset'],'cutofftime','timePat');

	 if(cutOffTime)
	{
	  if(validateForm(document.forms[0]) ){
	  document.forms[0].method.value = methodName;
	  enableFields();
	  document.forms[0].submit();
	}
	}
	
	else
	{
	  document.forms[0].elements['group.cutoffOffset'].focus();
	}
	}
	else
	{
    alert("<s:text name="interfacerulesmaintenance.alert.ruleValidation"/>");
	  document.forms[0].elements['group.groupName'].focus();

	}
	}
	else
	{
	  document.forms[0].elements['group.id.groupId'].focus();
	}

}
/* This method is user to validate for symbolic characters and throws alert message. */
function symbolicCharactersValidation(strField, strLabel,strPat) {
	var validText = false;
	if(validateField(strField,strLabel,strPat)){
		validText = true;
		}	
	if(validText == false){
	alert("<s:text name="interfacerulesmaintenance.alert.ruleValidation"/>");
	}
}
/* This Method is used for save or update the data and close the screen when enter key pressed */
function onKeyEnterSubmit(method,e){
	var event = (window.event|| e);
	 if(event.keyCode == 13){
	 if(method == "update"){
		submitForm('update');
		}else if(method == "save"){
		submitForm('save');
		}else if(method == "C"){
		confirmClose('C');
		}
	}
}

function submitFormLevel(methodName){
 
	document.forms[0].selectedEntityName.value='${entityName}';
	document.forms[0].method.value = methodName;
	enableFields();
	document.forms[0].submit();
	
}
function enableFields(){
	document.forms[0].elements["group.id.entityId"].disabled = "";
	document.forms[0].elements["group.id.groupId"].disabled = "";	
	document.getElementById("entityName").innerText.disabled = "";	
	
	document.forms[0].elements["group.groupLvlCode"].disabled = "";	
	document.getElementById("grpLvlName").innerText.disabled = "";	
	document.forms[0].elements["group.mgroupId"].disabled = "";	

}
/**
* This method is used to validate  the mandatory field the form when click the save button.
*
* @param methodName
* @return validate
**/
function validateForm(objForm){
  var elementsRef = new Array(2);
 
  elementsRef[0] = objForm.elements["group.id.groupId"];
  /* code Added  by Nageswara Rao on 03_Jan_2012 for mantis 1580:" Spaces should not be saved to end of inputted values " */
  elementTrim(document.forms[0]);
  elementsRef[1] = objForm.elements["group.groupName"];

  
  return validate(elementsRef);
}
function populateErrors()
{
	ShowErrMsgWindow('${actionError}');
	setParentChildsFocus();
	setFocus(document.forms[0]);
	var divElement = document.getElementById("dropdowndiv_2");
	var selectElement = document.forms[0].elements["group.mgroupId"];
	var idElement = document.forms[0].elements["metaGroupId"];
	var descElement = document.forms[0].elements["metaGroupName"];
	var arrowElement = document.forms[0].elements["dropdownbutton_2"];
	var idLength = 12;
	var descLength = 30;
	
	var dropBox1 = new SwMainSelectBox(divElement,selectElement,idElement,descElement,arrowElement,idLength,descLength);
	
	document.getElementById("entityName").innerText = '${entityName}';
	document.getElementById("grpLvlName").innerText = '${grpLvlName}';
    
}

function checkIdWithSpecialCharacters(element)
{
	if( isCancelorCloseButtonPressed() == false)
	 {
		var charsAllowed="~#!@$%^&*()-_=+[]:;'\",<.>/?";
		charsAllowed +="0123456789";
		charsAllowed +="abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ";
		var charsAllowedString = new String(charsAllowed);
		var idField = new Array(charsAllowedString.length);
		
		var inputId =  new String(element.value);
		var inputIdArray = new Array(inputId.length);
		
		var idNotValid = "false";
		
		for(var i=0; i<charsAllowedString.length ; i++)
		{
			idField[i] = charsAllowedString.charAt(i);		
		}

		for(i = 0; i<inputId.length;i++)
		{
			inputIdArray[i] = inputId.charAt(i);
		}
		
		for(i = 0; i<inputId.length ; i++)
		{
			for(var j=0; j<charsAllowedString.length ;j++)
				{
					if(inputIdArray[i] == idField[j])
						{
							idNotValid = "false";
							break;
						 }

						idNotValid = "true";
				}
				if(idNotValid == "true")
				{
					alert('<s:text name="groupmaintenance.alert.validString"/>');
					if (window.event){
						window.event.returnValue = false;
					}
					return false;
					
				}
		}
	  return true;
	}
	return true;
 }


</SCRIPT>
</head>

<s:form action="group.do" onsubmit="validate(this);">
<input name="method" type="hidden" value="save">
<input name="oldValue" type="hidden" value= "${oldValue}">
<input name="selectedEntityName" type="hidden" value="ENT001">


<body leftmargin="0" topmargin="0" marginheight="0" onLoad="populateErrors();" onunload="call()" ;style="overflow: scroll;">
<div id="dropdowndiv_2" style="z-index:99;position:absolute;width:200px;left:265px;top:25px;visibility:hidden" 	class="swdropdown">
	<select  cssClass="htmlTextFixed" name="group.mgroupId" size="10" style="width:329px;z-index:99;">
		 <s:iterator value="#request.metaGroup">       						 
       		<option value="<s:property value='value'/>" <s:if test="%{group.mgroupId == value}">selected</s:if>><s:property value='label'/></option>     						 
    	 </s:iterator>
	</select>
</div>
<div id="GroupAdd" style="position:absolute; left:20px; top:15px; width:570px; height:160px; border:2px outset;" color="#7E97AF">
<div id="GroupAdd" style="position:absolute;left:8px; top:4px; width:500px; height:150px;">
	<table width="510" border="0" cellpadding="0" cellspacing="0" height="72">
       <tr height="24px">
		  <td width="100px" ><b><s:text name="bookCode.entity"/></b></td>
		  <td width="28px" >&nbsp;</td>
		  <td width="400px" style="padding-left: 1px;">
         		 <s:textfield cssClass="htmlTextAlpha" name="group.id.entityId"  disabled="%{#request.screenFieldsStatus == 'true' ? 'true': ''}"  style="width:120px;height:22px;padding-top:3px"/>
         &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
		         <span id="entityName" name="entityName" class="spantext">
	      </td>
      </tr>
      
 <tr height="24px">
        
         <s:if test="#request.methodName == 'change'"> 
		  <td width="100px" ><b><s:text name="group.groupIdadd"/></b></td>
			<td width="28px" >&nbsp;</td>
			<td width="120px" style="padding-left: 1px;">
		 	<s:textfield name="group.id.groupId" cssClass="htmlTextAlpha" titleKey="tooltip.enterGroupIdentifier" tabindex="1" disabled="%{#request.screenFieldsStatus == 'true' ? 'true': ''}"  style="width:120px;height:22px;padding-top:3px" />
			</td>
	     </s:if> 
		 <s:if test="#request.methodName == 'add'"> 
		  <td width="100px" ><b><s:text name="group.groupIdadd"/></b>*</td>
        <td width="28px" >&nbsp;</td>
		  <td width="120px" style="padding-left: 1px;">
		 	<s:textfield name="group.id.groupId" titleKey="tooltip.enterGroupIdentifier" cssClass="htmlTextAlpha" tabindex="1" maxlength="12"  style="width:120px;height:22px;padding-top:3px" onchange="return validateField(this,'group.groupIdadd','alphaNumPat');"/>	
			 </td>
		 </s:if> 
           
      	  </tr>
	
		<tr height="24px">
         <td width="100px" ><b><s:text name="group.groupNameadd"/></b>*</td>
       <td width="28px" >&nbsp;</td>
		  <td width="280px" style="padding-left: 1px;">

			<s:textfield name="group.groupName" onchange="return symbolicCharactersValidation(this,'group.groupNameadd','alphaNumPatExtended');" titleKey="tooltip.enterGroupName" cssClass="htmlTextAlpha" tabindex="2" maxlength="30" style="width:280px;height:22px;" /> 	

          </td>
        </tr>
		</table>
		<table width="532" border="0" cellpadding="0" cellspacing="0">

		<tr height="15px">
          <td width="100px" ><b><s:text name="group.addgroupLvlCode"/></b>*</td>
          <td width="28px" >&nbsp;</td>
		  <td width="120px" style="padding-left: 1px;">
		  <s:textfield cssClass="htmlTextAlpha" name="group.groupLvlCode"  disabled="%{#request.screenFieldsStatus == 'true' ? 'true': ''}"  style="width:25px;height:22px;"/>
		    <td width="40px" >&nbsp;</td>
         <td width="280px">
		         <span id="grpLvlName" name="grpLvlName" class="spantext"> 
				 

	
          </td>
        </tr>
		</table>
			<table width="550" border="0" cellpadding="0" cellspacing="0" height="48">
		<tr height="23px">
          <td width="100px" ><b><s:text name="group.mgroupId"/></b></td>
         <td width="25px" >&nbsp;</td>
		  <td width="152px">
		    <input cssClass="textAlpha" name="metaGroupId" tabindex="3" style="width:120px;height:22px;" titleKey="tooltip.enterMGID" disabled>
		    <input titleKey="tooltip.selectMetaGroupLevel" tabindex="3" id="dropdownbutton_2" type="button" value="..." >				
		
			 <td width="280px">
		   <input cssClass="textAlpha" tabindex="-1" style="width:280px;background:transparent; border: thin;" name="metaGroupName" size="20" >             	
		  </td>
        </tr>
		
		<tr height="24px">
           <td width="100px" ><b><s:text name="group.add.cutoffOffset"/></b></td>
         <td width="28px" >&nbsp;</td>
		  <td width="45" align="left">
			<s:textfield name="group.cutoffOffset" cssClass="htmlTextNumeric" titleKey="tooltip.enterCutOffHM" tabindex="5" maxlength="5" style="width:45px;height:22px;" onchange="return validateField(this,'group.cutoffOffset','timePat');"/> 	
          </td>
        </tr>
   	</table>
</div>
</div>

<div id="Group" style="position:absolute; left:515; top:195px; width:70px; height:39px; visibility:visible;">
	<table width="60" border="0" cellspacing="0" cellpadding="0" height="20">
		<tr height="25">

			<td align="Right">
			    <a tabindex="8" href=# onclick="javascript:openWindow(buildPrintURL('print','Add Group Maintenance'),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')" onMouseOut="MM_swapImgRestore()" onMouseOver="MM_swapImage('Help','','images/help_default.GIF ',1)"><img src="images/help_default.GIF " name="Help"  border="0" title='<s:text name="tooltip.helpScreen"/>'></a> 
		   </td>

			<td align="right" id="Print">
				<a tabindex="8" onclick="printPage();" onMouseOut="MM_swapImgRestore()" onMouseOver="MM_swapImage('Print','','images/Print_R.gif ',1)"><img src="images/Print.gif " name="Print"  border="0" title='<s:text name="tooltip.printScreen"/>'></a>	
			</td>
		</tr>
	</table>
</div>

<div id="ddimagebuttons" color="#7E97AF" style="position:absolute; border:2px outset; left:20; top:184; width:570px; height:39px; visibility:visible;">
<div id="GroupAdd" style="position:absolute; left:6; top:2; width:382px; height:15px; visibility:visible;">
  <table width="145" border="0" cellspacing="2" cellpadding="0">
  
    <tr>
		<s:if test="#request.methodName == 'add'"> 		
		  <td><a title='<s:text name="tooltip.save"/>' tabindex="6" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onClick="javascript:submitForm('save');" onKeydown = "onKeyEnterSubmit('save',event);"><s:text name="button.save"/></a></td>
		</s:if> 
		<s:if test="#request.methodName == 'change'"> 		
		  <td><a title='<s:text name="tooltip.save"/>' tabindex="6" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onClick="javascript:submitForm('update');" onKeydown = "onKeyEnterSubmit('update',event);"><s:text name="button.save"/></a></td>
		</s:if>
		  <td
		   id="cancelbutton"><a title='<s:text name="tooltip.cancel"/>' tabindex="7"onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onClick="confirmClose('C');" onKeydown = "onKeyEnterSubmit('C',event);"><s:text name="button.cancel"/></a></td>
	</tr>

  </table>
</div>
</div>
	<blockquote>&nbsp;</blockquote>
	<p>&nbsp;</p>
</s:form>	
</body>
</html>