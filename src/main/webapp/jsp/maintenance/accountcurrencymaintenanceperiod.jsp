<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@ include file="/taglib.jsp"%>
<%@ page import="org.swallow.util.SwtConstants"%>
<%@ page import="org.swallow.util.SwtUtil"%>

<html>
<head>
<title>Account Currency Maintenance Period - SMART-Predict</title>
<%@ include file="/angularJSUtils.jsp"%>
<link rel="stylesheet" href="angularSources/styles.css?version=<%= SwtUtil.appVersion %>">
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
</head>
<SCRIPT language="JAVASCRIPT">
	var requestURL = new String('<%=request.getRequestURL()%>');
	var appName = "<%=SwtUtil.appName%>"; 
	var idy = requestURL.indexOf('/'+appName+'/');	
	requestURL=requestURL.substring(0,idy+1);
	var oXMLHTTP = new XMLHttpRequest();
	var screenRoute = "AcctCcyPeriodMaint";

 	/**
  	*	This section is used to handle calender button on the screen and is used to set the position of the same.
  	*/
	var dateFormatValue = '<s:property value="#request.session.CDM.dateFormatValue" />';
	var menuAccessId = "${requestScope.menuAccessId}";

	
	
	function subAcctCcyPeriodMaint(screenName, params) {
		var param = "accountPeriod.do?method=OpenSubAcctCcyPeriodMaint&screenName="+screenName;
		param += '&allParams=' + params;
		var 	mainWindow = openWindow (param, 'subAcctCcyPeriodMaint','left=10,top=230,width=600,height=430,toolbar=0, resizable=yes, status=yes, scrollbars=yes');
        return false;
	}
	
	function openLogScreen(methodName){
		var param = "accountPeriod.do?method="+methodName;
		var 	mainWindow = openWindow (param, 'openLogScreen','left=10,top=230,width=850,height=550,toolbar=0, resizable=yes, //status=yes, scrollbars=yes');	
		return false;
	}
	
	
</SCRIPT>


<body scroll="no" style="overflow:hidden" leftmargin="0" topmargin="0" marginheight="0">


<%@ include file="/angularscripts.jsp"%>

<form id="exportDataForm" target="tmp" method="post">
<input type="hidden" name="data" id="exportData" /> <input
	type="hidden" name="screen" id="exportDataScreen"
	value="<s:text name="PreAdviceInput.title.window"/>" /></form>
<iframe name="tmp" width="0%" height="0%" src="#" />
</body>
</html>
