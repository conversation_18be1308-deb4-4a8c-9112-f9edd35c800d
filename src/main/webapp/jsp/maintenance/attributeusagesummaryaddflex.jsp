<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">

<%@ include file="/taglib.jsp"%>
<%@ page import="org.swallow.util.CommonDataManager"%>
<%@ page import="org.swallow.util.SwtConstants"%>
<%@ page import="org.swallow.util.SwtUtil"%>

<html>
<head>
<meta name="viewport" content="width=device-width, initial-scale=1">
<link rel="icon" type="image/x-icon" href="favicon.ico">
<link rel="stylesheet" href="angularSources/styles.css?version=<%= SwtUtil.appVersion %>">
</head>
<body>
<%@ include file="/angularJSUtils.jsp"%>
	<script type="text/javascript" src="jsp/work/assets/swfobject.js"></script>
	<script type="text/javascript">
		var screenTitle = "";
		screenTitle = getMessage("attributeusagesummaryadd.title.window", null);
		document.title = screenTitle;
		var appName = "<%=SwtUtil.appName%>";
		var testDate= "<%=SwtUtil.getSystemDateString()%>";
		var screenRoute = "AttributeUsageAdd";
		
		window.onload = function () {
				setTitleSuffix(document.forms[0]);
				setParentChildsFocus();
		};

		window.onunload = call;
		var menuAccessId = '${requestScope.menuAccessId}';

		var calledFrom = '${requestScope.calledFrom}';
		var functionalGrp = '${requestScope.functionalGrp}';
		var attributeId = '${requestScope.attributeId}';

		/**
		 * Close window
		 */
		function closeWindow() {
			window.close();
		}

		function help() {
			openWindow(
					buildPrintURL('print', 'Define Attribute Usage'),
					'sectionprintdwindow',
					'left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no',
					'true')
		}
	</script>
<%@ include file="/angularscripts.jsp"%>
	<form id="exportDataForm" target="tmp" method="post">
		<input type="hidden" name="data" id="exportData" />
		<input type="hidden" name="screen" id="exportDataScreen" value="<s:text name="attributeusagesummaryadd.title.window"/>" />
	</form>
	<iframe name="tmp" width="0%" height="0%" src="#" />
</body>
</html>
