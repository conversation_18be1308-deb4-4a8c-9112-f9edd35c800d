<?xml version="1.0" encoding="utf-8"?>
<!--
  - The main purpose of this jsp file is to load the resultant xml data for Forecast Monitor Template Add Normal pop up screen.
  - 
  - Author(s): Vivekanandan A
  - Date: 24-05-2011
  -->
  
<%@ page contentType="text/xml" %>
<%@ page import="org.swallow.util.SwtConstants"%>
<%@ taglib prefix="s" uri="/struts-tags" %>
<%@ taglib uri="http://java.sun.com/jstl/fmt_rt" prefix="fmt" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>


<forecastmonitor databuilding="false" refresh="30" hideLoro="N" to="07/10/2008" from="01/10/2008" sysDateFrmSession="01/10/2008" dateComparing="false">
	<request_reply>
		<status_ok><s:property value="#request.reply_status_ok" /></status_ok>
		<message><s:property value="#request.reply_message" /></message>
		<location />
	</request_reply>
	<grid/>
	<selects>
		<select id="templates"> 
			<s:iterator var="listMonitorTemplateDetails" value="#request.listMonitorTemplateDetails">
			<option value="<s:property value='#listMonitorTemplateDetails.userId' />"	
				selected="1"><s:property value="#listMonitorTemplateDetails.id.templateId" /> </option>
			
			</s:iterator> 
		</select> 
	</selects>		
</forecastmonitor>