<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@page import="org.swallow.maintenance.model.PartySummary"%>
<%@ include file="/taglib.jsp"%>
<%@ page import="org.swallow.util.SwtConstants"%>
<%@ page import="org.swallow.util.SwtUtil"%>
<%@ page import="org.swallow.util.CommonDataManager"%>
<%@ page import="org.apache.commons.lang.StringEscapeUtils" %>

<%@page import="org.swallow.maintenance.model.Party"%>
<html>
<head>
<title><s:text name="party.title.mainWindow" /></title>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
<script type="text/javascript" src="js/partysummary.js"></script>

<SCRIPT language="JAVASCRIPT">
var totalCount = '${totalCount}';
var currentFilter="${requestScope.selectedFilter}";
var currentSort = "${requestScope.selectedSort}";
var filterstatus= "${requestScope.filterStatus}";
var sortStatus="${requestScope.sortStatus}";
var sortDescending="${requestScope.sortDescending}";
var selectedMvmtList="${requestScope.selectedList}";
var nextMovIdFocus="${requestScope.nextMovIdFocus}";
var initialscreen = "${requestScope.initialinputscreen}";
var maxPage = "${requestScope.maxPage}";
var partyName="${requestScope.partyName}";
var partyType="${requestScope.partyType}";
var isRefresfFromChild = "false";
var pageNoValue='${pageNoValue}';
var currPage = '${requestScope.currentPage}';

var appName = "<%=SwtUtil.appName%>";
var requestURL = new String('<%=request.getRequestURL()%>');
var idy = requestURL.indexOf('/'+appName+'/');
requestURL=requestURL.substring(0,idy+1) ;
var oXMLHTTP=new XMLHttpRequest();


var currentFilterValues = currentFilter.split("|");

var sortingValues = currentSort.split("|");

var filterValues=new Array();
var calledFromNext;
var sortedValues = new Array();
sortedValues[0] = sortingValues[0];
sortedValues[1] = sortingValues[1];
var cancelcloseElements = new Array(1);
	cancelcloseElements[0]="closebutton";

/*Added to apply style on mandatory field ,if it's equal to undefined then no field will be colored*/
 mandatoryFieldsArray= "undefined" ;

 <s:if test='"yes" == #request.parentFormRefresh' >
window.opener.document.forms[0].method.value="displayList";
document.forms[0].selectedCustodianName.disabled = true;

window.opener.document.forms[0].submit();
self.close();
</s:if>
var menuEntityCurrGrpAccess = "${requestScope.menuEntityCurrGrpAccess}";
function onFilterandSort()
{
    updateColors();
}

/**
 * This method is used to delete the seleted party.
 * @param methodName
 */
function submitFormDelete(methodName){
	//check for,is the party is eligible to delete
	if(checkPartyUse()){
		//values assigned to sumbit the details of the deleted partys
	document.forms[0].method.value = methodName;
	document.forms[0].selectedFilter.value ="${requestScope.selectedFilter}";
	document.forms[0].selectedSort.value = "${requestScope.selectedSort}";
	document.forms[0].filterCriteria.value = "${requestScope.filterCriteria}";
	document.forms[0].selectedFilterStatus.value = "${requestScope.filterStatus}";
	document.forms[0].filterFromSerach.value = "true";  
	document.forms[0].currentPage.value = '${requestScope.currentPage}';
	<!-- Start:Code modified by Prasenjit Maji for Mantis 1961 on 10-10-2012:Re frame party_list procedure -->	
	//trimming the partyId & partyName
	document.forms[0].partyId.value  = (document.forms[0].elements['party.id.partyId'].value).trim();
	document.forms[0].partyName.value  = (document.forms[0].elements['party.partyName'].value).trim();
	<!-- End:Code modified by Prasenjit Maji for Mantis 1961 on 10-10-2012:Re frame party_list procedure -->	
	document.forms[0].maxPages.value = '${requestScope.maxPage}';
		//check to confirm the delete
	var yourstate=window.confirm('<s:text name="confirm.delete"/>');
		//subit the form based upon delete confirmation
	if (yourstate==true){
		document.forms[0].selectedCustodianName.disabled = true;

		document.forms[0].submit();
	}
	} else {
		alert('<s:text name="alert.checkParty"/>')
}
}
/*
* This function will execute on click server Filter option
*@param index
*@param value
*@param action
*/
function optionClick_server_filter_JSP(index,value,action){
	
	if(action == "filter"){

	value = replace(value,'&nbsp;',' ');
	
	var filterValue ="";
	

	if(currentFilter =="all" || currentFilter=="undefined"){

		for(var idx = 0 ; idx < xl.numColumns ; ++idx)
		{

			if(idx == index)
				filterValue +=  value + "|";
			else
				filterValue +=  "All" + "|";
			
		}
	}else{
            
		var filter=currentFilter.split("|");
		filter[index]=value;
		for(var idx = 0 ; idx < xl.numColumns ; ++idx)
		{
			filterValue+=filter[idx] + "|";
		}
	}	
	
	document.forms[0].selectedSort.value=currentSort;
	document.forms[0].selectedFilter.value =filterValue ;
}else{
	var	sortColum=index;
	var sortDesc=value;
	document.forms[0].selectedSort.value=sortColum + "|" +sortDesc + "|";
	document.forms[0].selectedFilter.value =currentFilter ;
	}
<!-- Start:Code modified by Prasenjit Maji for Mantis 1961 on 08-10-2012:Re frame party_list procedure -->	
    //trimming partyId and partyName
	document.forms[0].partyId.value = (document.forms[0].elements['party.id.partyId'].value).trim();
	document.forms[0].partyName.value = (document.forms[0].elements['party.partyName'].value).trim();	
	getSelectedList();	
	document.forms[0].method.value = "displayList";
	document.forms[0].filterFromSerach.value = "true";    
	document.forms[0].currentPage.value = 1;	
	document.forms[0].filterCriteria.value=filterValue;
	document.forms[0].selectedCustodianName.disabled = true;

	document.forms[0].submit();
<!-- End:Code modified by Prasenjit Maji for Mantis 1961 on 08-10-2012:Re frame party_list procedure -->	
}
/** 
* This method is used to load the screen when the screen opens
* @return none
*/	
function bodyOnLoad()
{
	<s:if test='"yes" == #request.criteriaNotMatch' >
		alert('<s:text name="party.alert.criteriaNotMatch"/>');
	</s:if>



<!-- Start:Code modified by Prasenjit Maji for Mantis 1961 on 05-10-2012:Re frame party_list procedure -->
//passing parameter for filter
	xl = new XLSheet("custodianColl","table_2", ["String","String","String", "String", "Number"],"21112","true",currentFilterValues,sortedValues);
<!-- End:Code modified by Prasenjit Maji for Mantis 1961 on 05-10-2012:Re frame party_list procedure -->
	//variable declare the for entityID
	var dropBox1 = new SwSelectBox(document.forms[0].elements["party.id.entityId"],document.getElementById("entityDesc"));
   // variable declare for Value date
	var defaultSortingOnValueDate = true;
	//variable declare for Filter
	var isFilterSortStatus = true;
	 if(filterstatus !="")
	 {
		var filterStatus1 ;
		filterStatus1 = filterstatus.split(",");
		if(typeof xl != 'undefined' && xl != null && typeof xl.setFilterStatus != 'undefined')
			xl.setFilterStatus(filterStatus1);
		isFilterSortStatus = true;
	 }
	if(sortDescending !="" && sortDescending !="null" && sortDescending == "true")
	{	
		xl.dataTable.defaultDescending = sortDescending;

		isFilterSortStatus = true;
	}

	if(sortDescending !="" && sortDescending !="null" && sortDescending == "false")
	{	
	  
		xl.dataTable.doManualSorting(sortStatus);	
		isFilterSortStatus = true;
	}

	if(nextMovIdFocus !="" && nextMovIdFocus !="-1")
	{	
	 	xl.getNextScrollIntoView(nextMovIdFocus);
		isFilterSortStatus = true;
	}	
	updateColors();

	xl.onsort = xl.onfilter = onFilter;
	  
	if(menuEntityCurrGrpAccess == "0"){

		
		highlightMultiTableRows("custodianColl");
	}else{
				
		<s:if test='"search" != #request.method' >
		highlightMultiTableRows("custodianColl");
		</s:if>

		<s:else>	
			highlightMultiTableRows("custodianColl");
		</s:else>
	}
	
	highlightTableRows("custodianColl");
	document.forms[0].totalCount.value = '${requestScope.totalCount}';
	document.getElementById("aliasbutton").innerHTML = document.getElementById("aliasdisablebutton").innerHTML; 
 	<%if ( (SwtConstants.STR_TRUE).equals(request.getAttribute(SwtConstants.ADD_BUT_STS)) ) {%>
		document.getElementById("addbutton").innerHTML = document.getElementById("addenablebutton").innerHTML;
		<%}%>
	 <%if ( !(SwtConstants.STR_TRUE).equals(request.getAttribute(SwtConstants.ADD_BUT_STS)) ) {%>	     
	   document.getElementById("addbutton").innerHTML = document.getElementById("adddisablebutton").innerHTML;
	   <%}%>
	 <%if ( (SwtConstants.STR_TRUE).equals(request.getAttribute(SwtConstants.CHG_BUT_STS)) ) {%>
		document.getElementById("changebutton").innerHTML = document.getElementById("changeenablebutton").innerHTML;
		<%}%>

	 <%if ( !(SwtConstants.STR_TRUE).equals(request.getAttribute(SwtConstants.CHG_BUT_STS)) ) {%>
		document.getElementById("changebutton").innerHTML = document.getElementById("changedisablebutton").innerHTML;
		<%}%>

	<%if ( (SwtConstants.STR_TRUE).equals(request.getAttribute(SwtConstants.DEL_BUT_STS)) ) {%>
		document.getElementById("deletebutton").innerHTML = document.getElementById("deleteenablebutton").innerHTML;
		<%}%>

	 <%if ( !(SwtConstants.STR_TRUE).equals(request.getAttribute(SwtConstants.DEL_BUT_STS)) ) {%>
		document.getElementById("deletebutton").innerHTML = document.getElementById("deletedisablebutton").innerHTML;
		<%}%>
  // variable declare for partyID
	var partyId = document.forms[0].elements["party.id.partyId"].value;
	//Variable declare for partyName
	var partyName = document.forms[0].elements["party.partyName"].value;
	// variable declare for search button
	var searchBut = document.getElementById("searchButton");
	var searchButDisabled = document.getElementById("searchButtondisabled");

	if(partyId != "" || partyName != "")
	{
        searchBut.title ='<s:text name = "tooltip.executeSearch"/>';
	searchBut.style.display = "inline";
	searchButDisabled.style.display = "none";
	}
	else{
	 searchBut.title = '<s:text name = "tooltip.executeSearch"/>';
	 searchBut.style.display = "none";
	 searchButDisabled.style.display = "inline";
	}
	document.forms[0].menuAccessId.value = "${requestScope.menuAccessId}";
	
}
	
<!-- Start:Code modified by Prasenjit Maji for Mantis 1961 on 08-10-2012:Re frame party_list procedure -->	
/*
* This function will execute on change entity from Entity combo box
*@param methodName
*/
function submitForm(methodName){
	document.forms[0].method.value = methodName;
	document.forms[0].selectedFilter.value ="All|All|All|All|All";
	document.forms[0].selectedSort.value = "0|false";
	document.forms[0].filterCriteria.value = "All|All|All|All|All";
	document.forms[0].elements["party.id.partyId"].value="";
	document.forms[0].elements['party.partyName'].value="";
	document.forms[0].selectedCustodianName.disabled = true;

	document.forms[0].submit();
}

/*
* This function will execute when search button is clicked
*@param methodName
*/
function submitFormSearch(methodName)
{	    
	var partyId = document.forms[0].elements["party.id.partyId"].value;
	var partyName = document.forms[0].elements['party.partyName'].value;
	//trimming partyId & partyName 
	var trimmedPartyId = (document.forms[0].elements["party.id.partyId"].value).trim();
	var trimmedPartyName = (document.forms[0].elements['party.partyName'].value).trim();
	// variable declare for search button
	var searchBut = document.getElementById("searchButton");
	var searchButDisabled = document.getElementById("searchButtondisabled");

	if(trimmedPartyId != "" || trimmedPartyName != "")
	{
	document.forms[0].method.value = methodName;
	//form value will trim the empty spaces before submit
    elementTrim(document.forms[0]);
		document.forms[0].selectedCustodianName.disabled = true;

		document.forms[0].submit();
	}
	//condition for checking partyId is empty
	else if(partyId!="")
	{    
		alert('<s:text name="party.alert.partyid"/>');
		//setting form value
		document.forms[0].elements['party.id.partyId'].value =trimmedPartyId;		
		document.forms[0].elements['party.id.partyId'].focus();	
		//disable the search button
		searchBut.style.display = "none";
		searchButDisabled.style.display = "inline";
	}
	//condition for checking partyName is empty
    else if (partyName!="")
	    {
	    alert('<s:text name="party.alert.partyname"/>');
	    //setting form value
		document.forms[0].elements['party.partyName'].value =trimmedPartyName;
		document.forms[0].elements['party.partyName'].focus();	
		//disable the search button
		searchBut.style.display = "none";
		searchButDisabled.style.display = "inline";
		}
}

/*
* This function will execute when change button is clicked
*@param methodName
*/
function buildChangeCustodianURL(methodName){
	var param = 'party.do?method='+methodName+'&entityCode=';
	    param +=document.forms[0].elements['party.id.entityId'].value;
	    param +='&partyId=';
		param +=escape(encodeURIComponent(document.forms[0].selectedCustodianCode.value));
	    param +='&currentPage=';
	    param +=currPage;
	    param +='&filterCriteria=';
	    param +=document.forms[0].filterCriteria.value;	
	    param +='&selectedSort=';
	    param +=document.forms[0].selectedSort.value;	
	    param +='&selectedFilter=';
	    param +=document.forms[0].selectedFilter.value;	
	    param +='&maxPage=';
	   	param +=maxPage;
	    param +='&partyName=';
		param +=escape(encodeURIComponent(document.forms[0].selectedCustodianName.value));	
	    param +='&entityName='+document.getElementById("entityDesc").innerText;
		param +='&parentScreenPartyId=';
		//trimming partyId
	    param +=(document.forms[0].elements['party.id.partyId'].value).trim();
	    param +='&parentScreenPartyName=';
	    //trimming partyName
	    param +=(document.forms[0].elements['party.partyName'].value).trim();
		return  param;
	}
<!-- End:Code modified by Prasenjit Maji for Mantis 1961 on 08-10-2012:Re frame party_list procedure -->
function buildURL(methodName) {
		var param = 'party.do?method='+methodName;
		return  param;
	}
function onSelectTableRow(rowElement, isSelected)
{

	if(rowElement && rowElement.cells.length == 3)
	{
		var cell1 = rowElement.cells[0];
		var cell2 = rowElement.cells[1];
		var cell3 = rowElement.cells[2];				
		var hiddenEl1 = cell1.getElementsByTagName("input")[0];
		var hiddenEl2 = cell2.getElementsByTagName("input")[0];
		var hiddenEl3 = cell3.getElementsByTagName("input")[0];	
	}
	
	var hiddenElement = rowElement.getElementsByTagName("input")[0];
	var hiddenEl2 = rowElement.getElementsByTagName("input")[1];
	var noOfAliases = rowElement.getElementsByTagName("input")[3];
	document.forms[0].selectedCustodianCode.value = hiddenElement.value;

	document.forms[0].selectedCustodianName.value = hiddenEl2.value;

	 
	if (isSelected) {
		document.getElementById("aliasbutton").innerHTML = document.getElementById("aliasenablebutton").innerHTML;
		setStoredParam('partySelectedRow',rowElement.cells[0].innerText);
	} else {
		document.getElementById("aliasbutton").innerHTML = document.getElementById("aliasdisablebutton").innerHTML;
		setStoredParam('partySelectedRow','');
	}
	
	if(menuEntityCurrGrpAccess == "0" && isSelected)
	
	{
 	    document.getElementById("addbutton").innerHTML = document.getElementById("addenablebutton").innerHTML;    
		document.getElementById("changebutton").innerHTML = document.getElementById("changeenablebutton").innerHTML;		
		document.getElementById("deletebutton").innerHTML = document.getElementById("deleteenablebutton").innerHTML;		
		
	}else{
		
		if(menuEntityCurrGrpAccess == "0")
		{
		document.getElementById("addbutton").innerHTML = document.getElementById("addenablebutton").innerHTML;
		}
		else
		   document.getElementById("addbutton").innerHTML = document.getElementById("adddisablebutton").innerHTML;
		  document.getElementById("changebutton").innerHTML = document.getElementById("changedisablebutton").innerHTML;
		  document.getElementById("deletebutton").innerHTML = document.getElementById("deletedisablebutton").innerHTML;
	}

}
/*
* This function will execute when add button is clicked
*@param methodName
*/
function buildAddCustodianURL(methodName){
	   var param = 'party.do?method='+methodName+'&entityCode=';	   
	   param +=document.forms[0].elements['party.id.entityId'].value;
	   param +='&partyId=';
	   param +=document.forms[0].selectedCustodianCode.value;
	   param +='&currentPage=';
	   param +=currPage;
	   param +='&filterCriteria=';
	   param +=document.forms[0].filterCriteria.value;	
	   param +='&selectedSort=';
	   param +=document.forms[0].selectedSort.value;	
	   param +='&selectedFilter=';
	   param +=document.forms[0].selectedFilter.value;
	   param +='&maxPage=';
	   param +=maxPage;
	   param +='&partyName=';
	   param +=document.forms[0].selectedCustodianName.value;
	   param +='&parentScreenPartyId=';
<!-- Start:Code modified by Prasenjit Maji for Mantis 1961 on 10-10-2012:Re frame party_list procedure -->
	   //trimming partyId
	   param +=(document.forms[0].elements['party.id.partyId'].value).trim();
	   param +='&parentScreenPartyName=';
	   //trimming partyName
	   param +=(document.forms[0].elements['party.partyName'].value).trim();
<!-- End:Code modified by Prasenjit Maji for Mantis 1961 on 10-10-2012:Re frame party_list procedure -->
	   param +='&entityName='+document.getElementById("entityDesc").innerText;
	   return  param;
	}
 function assignCheckBoxId(tableId)
	{
		var tableElement = document.getElementById(tableId);
		var tbodyElement = tableElement.getElementsByTagName("tbody")[0];
		var rowElements = tbodyElement.getElementsByTagName("tr");

		for( var rowIdx = 0 ; rowIdx < rowElements.length ; ++rowIdx)
		{
			var paramCode = rowElements[rowIdx].getAttribute("paramCode");
			var colls = rowElements[rowIdx].getElementsByTagName("td");

			addHiddenElement("id.partyId_"+rowIdx,id.partyId);

			for( var colIdx = 1 ; colIdx < colls.length ; ++colIdx)
			{
				var checkBoxElement = colls[colIdx].getElementsByTagName("input")[0];
				var isChecked = checkBoxElement.checked;
				var hiddenElId = "custodian_"+id.partyId +"_"+colIdx;
				checkBoxElement.id = hiddenElId;
				checkBoxElement.name = hiddenElId;
			}			
		}
	}
	function addHiddenElement(id,value)
	{
		var el = document.createElement("input");
		el.type="hidden";
		el.value=value;
		el.id = id;
		el.name = id;
		document.forms[0].appendChild(el);
	}
	

   function enableFields(){
	document.getElementById("entityDesc").disabled = "";	
}

/*This function is used to execute the search button  
 *@param element
 */

function searchButtonStatusForId(element)
{
	//variable declare the search
	var searchBut = document.getElementById("searchButton");
	var searchButDisabled = document.getElementById("searchButtondisabled");

   //variable declare partyid
	var partyId = document.forms[0].elements["party.id.partyId"].value;
	//variable party Name
	var partyName = document.forms[0].elements["party.partyName"].value;

	if(partyId != "" || partyName != "")
	{
		searchBut.style.display = "inline";
		searchButDisabled.style.display = "none";
                searchBut.title ='<s:text name = "tooltip.executeSearch"/>';
	}
	else{
		
		searchBut.title ='<s:text name = "tooltip.executeSearch"/>';
		searchBut.style.display = "none";
		searchButDisabled.style.display = "inline";
	}
	
	
}

function searchButtonStatusForName(element)
{
	var searchBut = document.getElementById("searchButton");	
	var searchButDisabled = document.getElementById("searchButtondisabled");

	var partyId = document.forms[0].elements["party.id.partyId"].value;
	var partyName = document.forms[0].elements["party.partyName"].value;
	if(partyId != "" || partyName != "")
	{
		searchBut.style.display = "inline";
		searchButDisabled.style.display = "none";
	}
	else{
		searchBut.style.display = "none";
		searchButDisabled.style.display = "inline";
	}
	
}
function printParty(){
	var oXMLHTTP = new XMLHttpRequest();
	document.forms[0].selectedFilter.value ="${requestScope.selectedFilter}";
	document.forms[0].selectedSort.value = "${requestScope.selectedSort}";
	document.forms[0].filterCriteria.value = "${requestScope.filterCriteria}";
	document.forms[0].selectedFilterStatus.value = "${requestScope.filterStatus}";
	document.forms[0].filterFromSerach.value = "true";  
	document.forms[0].currentPage.value = '${requestScope.currentPage}';
	document.forms[0].pageNoValue.value = '${requestScope.currentPage}';
	document.forms[0].maxPages.value = '${requestScope.maxPage}';
	var sURL = requestURL + appName+"/print.do?";
	sURL = sURL + "selectedFilter="+document.forms[0].selectedFilter.value;
	sURL = sURL + "&selectedSort="+document.forms[0].selectedSort.value;
	sURL = sURL + "&partyId="+document.forms[0].elements['party.id.partyId'].value;
	sURL = sURL + "&partyName="+document.forms[0].elements['party.partyName'].value;
	sURL = sURL + "&filterCriteria="+document.forms[0].filterCriteria.value;
	sURL = sURL + "&selectedFilterStatus="+document.forms[0].selectedFilterStatus.value;
	sURL = sURL + "&filterFromSerach="+document.forms[0].filterFromSerach.value;
	sURL = sURL + "&currentPage="+document.forms[0].currentPage.value;
	sURL = sURL + "&pageNoValue="+document.forms[0].pageNoValue.value;
	sURL = sURL + "&maxPages="+document.forms[0].maxPages.value;
	oXMLHTTP.open( "POST", sURL, false );
	oXMLHTTP.send();
	var str=new String(oXMLHTTP.responseText);
	
}
function clickLink(element)
{

	var currPage = "${requestScope.currentPage}";	
	element.href +='&method=next';
	element.href +='&selectedSort=';
	element.href +=currentSort;
	element.href +='&selectedFilter=';
	element.href +=currentFilter;
	element.href +='&entityId=';
	element.href +=document.forms[0].elements["party.id.entityId"].value;
	element.href +='&filterCriteria=';
	element.href +=document.forms[0].filterCriteria.value;
	element.href +='&partyId=';
	element.href +=document.forms[0].elements['party.id.partyId'].value;
	element.href +='&partyName=';
	element.href +=document.forms[0].elements['party.partyName'].value;
	
}
function showAliasDetails(methodName) {
var param = 'party.do?method='+methodName+'&selectedEntityId=';
        document.forms[0].menuAccessId.value = "${requestScope.menuAccessId}";
        param +=document.forms[0].elements['party.id.entityId'].value;
	    param +='&selectedPartyId=';
	    param +=document.forms[0].selectedCustodianCode.value;	
	    param +='&partyName=';
	    param +=document.forms[0].selectedCustodianName.value;	
	    param +='&entityDesc='+document.getElementById("entityDesc").innerText;
		param +='&menuAccessId='+document.forms[0].menuAccessId.value;	    
		return  param;

}
//This function refreshes no. of aliases corresponding to a particular party Id
/*
* This function to refresh the screen after closing the party alias screen
*@param parId
*@param noOfAliases
*/
function refreshAliasDetails(parId, noOfAliases)
{
//get the datgrid details
	var dataTable = document.getElementById("custodianColl");
	if(dataTable != null && dataTable != 'undefined')
	{
		if(dataTable.tBodies != null && dataTable.tBodies != 'undefined')
		{
		//get the party ID
			var elements = document.getElementsByName("id.partyId");
			//loop to run the rows in thr data grid
			for(var idx = 0 ; idx < elements.length; ++idx)
			{
			//get the party id rows
				var element = elements[idx];
				//chaking th epartyId
				if(element.value == parId)
				{
					
					var trElement = element.parentElement;
					
					while(trElement.tagName != 'TR')
						trElement = trElement.parentElement;

					if(trElement != null && trElement != 'undefined')
					{
						if (trElement.cells[4].innerText != noOfAliases) {
					
					
						//assingning no of values to the grid
							trElement.cells[4].innerText = noOfAliases+'  ';
						
						}
					}
					break;
				}
			}
			
		}
	}

}
<!-- Start:Code modified by Prasenjit Maji for Mantis 1961 on 09-10-2012:Re frame party_list procedure -->
/*
* This function will validate entered page no for input text
*@param strObject
*/
function validatePageNumber(strObject)
{
    var re = /^\d+$/;
	if (strObject && (re.test(strObject.value) && strObject.value != 0)) 
	{
		if(parseInt(strObject.value) > maxPage)
		{
			strObject.value = maxPage;
		}
		goToResultsPage(strObject.value);
	}
	else
	{
		
		alert('<s:text name="party.alert.pagination"/>');
		
		strObject.value = currPage;
	}
}

/*
* This function will execute after entering page no and press enter
*@param goToPageNo
*/
function goToResultsPage(goToPageNo)
{
	var currPage = "${requestScope.currentPage}";	
	document.forms[0].method.value = "next";
	document.forms[0].goToPageNo.value=goToPageNo;
	document.forms[0].currentPage.value=currPage;
	document.forms[0].selectedSort.value=currentSort;
	document.forms[0].selectedFilter.value=currentFilter;
	document.forms[0].entityId.value=document.forms[0].elements["party.id.entityId"].value;
	document.forms[0].filterCriteria.value=document.forms[0].filterCriteria.value;
	// party id is trimmed
	document.forms[0].partyId.value=(document.forms[0].elements["party.id.partyId"].value).trim();
	// party id is trimmed
	document.forms[0].partyName.value=(document.forms[0].elements["party.partyName"].value).trim();
	document.forms[0].selectedCustodianName.disabled = true;
	document.forms[0].submit();
}

/**
* Modified by Mefteh Bouazizi for Mantis 1534
* This method is called while click on Next page/Previous Page in Pagination Icon
* @param goToPageNo
 */
function clickLink(goToPageNo)
{
	var currPage = "${requestScope.currentPage}";
			 var baseUrl = '<%=SwtUtil.convertBeanToUrlParams((PartySummary)((ArrayList<PartySummary>)request.getAttribute("pageSummaryList")).get(0), "partySummary") %>';
			 var url='party.do?'+baseUrl;	
			 url +='goToPageNo='+goToPageNo+'&method=next'+'&selectedSort='+currentSort+'&selectedFilter='+currentFilter+'&entityId='+document.forms[0].elements["party.id.entityId"].value;
			 url +='&filterCriteria='+document.forms[0].filterCriteria.value+'&partyId='+(document.forms[0].elements['party.id.partyId'].value).trim();
			 url +='&partyName='+(document.forms[0].elements['party.partyName'].value).trim()+'&menuAccessId='+document.forms[0].menuAccessId.value
			 submitFormFromURL(url,window); 		
}
<!-- End:Code modified by Prasenjit Maji for Mantis 1961 on 09-10-2012:Re frame party_list procedure -->

/**
* This method is used to check whether the given party is referred by any
* child party in the same entity or not, while deleting the party. If it is
* referred system will not allow deleting the party.
* @return boolean
 */
function checkPartyUse()
{
	//get the selected row
	var rows=document.getElementById("custodianColl").getElementsByTagName("tbody")[0].getElementsByTagName("tr");
	//get the party from selected row
	for (i = 0; i < rows.length; i++) {
		if (isRowSelected(rows[i])) {
			var party = rows[i].cells[0].innerText;
		}
	}
	var entityId=document.forms[0].elements['party.id.entityId'].value;
	//framing url
	var sURL = requestURL + appName +"/party.do?method=checkPartyUse";
	sURL=sURL+"&entityId="+entityId+"&partyId="+party;
	//sending request 
	oXMLHTTP.open( "POST", sURL, false );
	oXMLHTTP.send();
	//response from Ajax
	var partyCheckFlag=oXMLHTTP.responseText;
	//check for party flag
	if(partyCheckFlag=="true")
		return true;
	return;
}
/**
* This method is used to check the maximum character length
* 
* @param field
* @param maxChars
* @return none	 
*/
function maxLengthTextArea (field,maxChars)
{
	if(field.value.length > maxChars)
		field.value = field.value.substring(0, maxChars);
} 

</SCRIPT>

</head>
<body onmousemove="reportMove()" leftmargin="0" topmargin="0"
	marginheight="0"
	onLoad="setParentChildsFocus();setFocus(document.forms[0]);bodyOnLoad();ShowErrMsgWindow('${actionError}');"
	onunload="call()">

<s:form action="party.do">

	<input name="selectedCustodianCode" type="hidden">
	<input name="selectedCustodianName" type="hidden">
	<input name="filterCriteria" type="hidden" value='${filterCriteria}'>
	<input name="selectedFilter" type="hidden" value='${selectedFilter}'>
	<input name="selectedSort" type="hidden" value='${selectedSort}'>
	<input name="totalCount" type="hidden" value="">
	<input name="selectedList" type="hidden" value="">
	<input name="selectedSortDescending" type="hidden" value="">
	<input name="selectedFilterStatus" type="hidden" value="">
	<input name="selectedSortStatus" type="hidden" value="">
	<input name="currentPage" type="hidden" value='${currPage}'>
	<input name="prevEnabled" type="hidden" value="">
	<input name="nextEnabled" type="hidden" value="">
	<input name="maxPages" type="hidden" value='${maxPage}'>
	<input name="initialinputscreen" type="hidden"
		value='${initialinputscreen}'>
	<input name="partyId" type="hidden" value='${partyId}'>
	<input name="partyName" type="hidden" value='${partyName}'>
	<input name="partyType" type="hidden" value='${partyType}'>
	<input name="entityId" type="hidden" value="">
	<input name="pageNoValue" type="hidden" value=''>
	<input name="method" type="hidden" value="">
	<input name="inputFrom" type="hidden" value="">
	<input name="filterFromSerach" type="hidden" value="">
	<input name="linkFlag" type="hidden" value="">
	<input name="refreshScreen" type="hidden" value="">
	<input name="tabIndicator" type="hidden" value="">
	<input name="goToPageNo" type="hidden" value="">
	<input name="menuAccessId" type="hidden">
	<div id="CustodianMaintenance"
		style="position: absolute; left: 10px; top: 10px; width: 1092px; height: 90px; border: 2px outset;"
		color="#7E97AF">
	<div id="CustodianMaintenance"
		style="position: absolute; left: 8px; top: 4px; width: 510px; height: 38px;">

	<table width="610" border="0" cellpadding="0" cellspacing="0"
		height="25">
		<tr height="25">
			<td width="30"><b><s:text name="party.entityId" /></b></td>
			<td width="28">&nbsp;</td>
			<td  width="28">
	       <div  class="styled-select">
			<s:select tabindex="1" titleKey="tooltip.selectEntityid" 
			id="party.id.entityId" name="party.id.entityId" 
			onchange="submitForm('displayList');" cssStyle="width:140px ; " 
			list="#request.entities" listKey="value" listValue="label" />
			
			</div>
			</td>
		<td width="310"><span id="entityDesc" name="entityDesc"
				class="spantext"></td>
		</tr>
		<tr height="25">
			<td width="30"><b><s:text name="party.partyId" /></b></td>
			<td width="28">&nbsp;</td>
			<td width="28"><s:textfield name="party.id.partyId"
				style="width:140px ; " titleKey="tooltip.enterPartyId"
				onkeyup="searchButtonStatusForId(this)" tabindex="1" maxlength="12" /></td>
			<td width="300"><span id="entityDesc" name="entityDesc"
				class="spantext"></td>
		</tr>
	</table>
	<table width="450" border="0" cellpadding="0" cellspacing="0" style="width: 668px;"
		height="25">
		<tr>
			<td width="55"><b><s:text name="party.partyName" /></b></td>
			<td width="61">&nbsp;</td>
			<td width="280"><s:textfield name="party.partyName"
				style="width:500px;margin-left: 3px;" onkeydown="maxLengthTextArea(this,60);"
				titleKey="tooltip.enterPartyName"
				onkeyup=" searchButtonStatusForName(this)" tabindex="2" /></td>
			<td width="30">&nbsp;</td>
			<td width="60" align="right" id="ddimagebuttons">
					<a  id="searchButtondisabled" style="display: none" class="disabled" disabled="disabled" >
						<s:text name = "button.search"/>
					</a>
					<a  id="searchButton" style="display: none" title='<s:text name = "tooltip.executeSearch"/>'tabindex="3"
					onMouseOut="collapsebutton(this)" 
					onMouseOver="highlightbutton(this)" 
					onMouseDown="expandbutton(this)" 
					onMouseUp="highlightbutton(this)"
					onClick="submitFormSearch('search')"><s:text name = "button.search"/></a>	
					</td>

		</tr>
	</table>

	<div  id="pageSummaryList" style="position:absolute; left:870px; top:1px; width:225px; height:25px;border:2px;">
	<s:if test='"true" != #request.hidePagination' >
		<table  border="0" cellpadding="0" cellspacing="1" height="25px">
		  <tr height="25px">
		  <%String currentPageAsString = (String)request.getAttribute("currentPage");%> 
		  <%String maxPageAsString = (String)request.getAttribute("maxPage");%>
		  <%int currentPage = Integer.parseInt(currentPageAsString);%> 
		  <%int countPageNo = 1;%>	
		   <%int maxPage = Integer.parseInt(maxPageAsString);%>
		  <s:iterator value="#request.pageSummaryList" var="pageSummaryList">			  
			<% if( countPageNo <=12) {++countPageNo; %>	
			<td height="34"><b>Page</b>&nbsp;&nbsp;
	  		<input class="htmlTextNumeric" title='<s:text name="tooltip.enterPageNo"/>' id="pageNoText" name="pageNo" size="5"  style="height: 21px;" align="top" value="<%=currentPageAsString %>" onchange="validatePageNumber(this);" onkeydown="if (event.keyCode == 13)validatePageNumber(this);">
	  		</td>
	  		<td>
			    <s:if test='"true" == #request.nextEnabled' >
				 <a href="#"  onclick="clickLink(-1);">
			    <img alt="Next page" src="images/page_up.png" align="top" border="0" width="18" style="padding-top:9px; height: 11px;"></img><br />
			     </a>
			    </s:if>
			    <s:else>
			    <img alt="Next page" src="images/page_up.png" align="top" border="0" width="18" style="padding-top:9px; height: 11px;"></img><br />
			    </s:else>
		       	<s:if test='"true" == #request.prevEnabled' >
			   <a href="#"  onclick="clickLink(-2);">
		    	<img alt="Previous page" src="images/page_down.png" align="bottom" width="18" border="0" style=" padding-bottom:9px; height: 11px;"></img><br />
				</a>
		    	</s:if>
		     	<s:else>
		    	<img alt="Previous page" src="images/page_down.png" align="bottom" width="18" border="0" style="padding-bottom:9px; height: 11px;"></img><br />
		    	</s:else>
		    </td>
		    <td style="text-align: center;">&nbsp;&nbsp;<s:text name="genericDisplayMonitor.labelOf"/>&nbsp;&nbsp;
		    <input class="textAlpha" style="background:transparent;border: 0; height:17;" readonly name="maxPageNo" value="<%=maxPageAsString %>" size="5">
		  	</td>
			<%}%>	
		</s:iterator>		
		  </tr>
		</table>
		</s:if>
	</div>
	
	</div>
	</div>


	<div id="CustodianMaintenance" color="#7E97AF"
		style="position: absolute; border-left: 2px outset; left: 10px; top: 104px; width: 864px; height: 375px;">
	<div id="CustodianMaintenance"
		style="position: absolute; z-index: 99; left: 0px; top: 0px; width: 1073px; height: 10px;overflow-x: hidden">
	<table class="sort-table" id="table_2" bgcolor="#B0AFAF" width="1072px"  
		border="0" cellspacing="1" cellpadding="0" height="20px">
		<thead>
			<tr>
				<td title='<s:text name="tooltip.sortParty"/>' width="135px" style="border-left-width: 0px;"
					height="20px"><b><s:text name="party.partyId" /></b></td>
				<td title='<s:text name="tooltip.sortname"/>' width="597px"
					height="20px"><b><s:text name="party.partyName" /></b></td>
				<td title='<s:text name="tooltip.sortByType"/>'
					width="75px" height="20px"><b><s:text name="party.header" /></b></td>
				<td title='<s:text name="tooltip.sortbyParentParty"/>'
					width="135px" height="20px"><b><s:text name="party.parentParty" /></b></td>
				<td title='<s:text name="tooltip.numberOfAliases"/>' align="center"
					width="115px" height="20px"><b><s:text name="party.alias" /></b></td>

			</tr>
		</thead>
	</table>
	</div>
	<div id="ddscrolltable"
		style="position: absolute; left: 0px; top: 0px; width: 1090px; height: 371px;overflow: auto ">
	<div id="paryMaintenance"
		style="position: absolute; z-index: 99; left: 0px; top: 21px; width: 855; height: 10px;">
	<table class="sort-table" id="custodianColl" width="1067px" border="0"
		cellspacing="1" cellpadding="0" height="348px">
		<tbody>
			<%
				int count = 0;
			%>

			<s:iterator value="#request.custodianColl" var="custodianColl">
				<%
					if (count % 2 == 0) {
				%><tr class="even">
					<%
						} else {
					%>
				
				<tr class="odd">
					<%
						}
							++count;
					%>
					<s:set var="custodianColl" value="#custodianColl"/>
					<jsp:useBean id="custodianColl" class="org.swallow.maintenance.model.Party" />					
					<%Party party=(Party)custodianColl; %>
					<td width="135px"><s:hidden name="id.partyId" /><s:property value="id.partyId" />&nbsp;</td>
				<td width="607px">
					<s:hidden name="partyName" />
					 <%= StringEscapeUtils.escapeHtml(party.getPartyName() != null ? party.getPartyName() : "") %>
				</td>
					<td align="left" width="75px"><s:hidden
						name="partyDescription" /><s:property value="partyDescription" />&nbsp;</td>
					<td align="left" width="135px"><s:hidden
						name="parentParty" /><s:property value="parentParty" />&nbsp;</td>
					<td align="right" width="115px"><s:hidden
						name="noOfAliasesAsString" /><s:property value="noOfAliasesAsString" />&nbsp;</td>

				</tr>
			</s:iterator>
		</tbody>
		<tfoot>
			<tr>
				<td colspan="5"></td>
			</tr>
		</tfoot>
	</table>
	</div>
	</div>
	</div>



	<div id="PartyMain"
		style="position: absolute; left: 1000px; top: 493px; width: 80px; height: 39px; visibility: visible;">
	<table width="70" border=0 cellspacing="0" cellpadding="0" height="20">
		<tr>
			<td align="Right"><a tabindex="6" tabindex="6" href=#
				onclick="javascript:openWindow(buildPrintURL('print','Party Maintenance'),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')"
				onMouseOut="MM_swapImgRestore()"
				onMouseOver="MM_swapImage('Help','','images/help_default.GIF ',1)"><img
				src="images/help_default.GIF " name="Help" border="0"
				title='<s:text name="tooltip.helpScreen"/>'></a></td>

			<td align="right" id="Print"><a></a><a tabindex="6"
				onclick="printPage();" onMouseOut="MM_swapImgRestore()"
				onMouseOver="MM_swapImage('Print','','images/Print_R.gif ',1)"><img
				src="images/Print.gif " name="Print" border="0"
				title='<s:text name="tooltip.printScreen"/>'></a></td>
		</tr>
	</table>
	</div>



	<div id="ddimagebuttons" color="#7E97AF"
		style="position: absolute; border: 2px outset; left: 10; top: 484; width:1093px; height: 39px; visibility: visible;">
	<div id="CustodianMaintenance"
		style="position: absolute; left: 6; top: 4; width: 539; height: 15px; visibility: visible;">
	<table width="350" border="0" cellspacing="0" cellpadding="0"
		height="20">
		<tr>
			<td id="addbutton"></td>
			<td id="changebutton"></td>
			<td id="deletebutton"></td>
			<td id="aliasbutton"></td>
			<td id="closebutton"><a tabindex="5"
				title='<s:text name="tooltip.close"/>'
				onMouseOut="collapsebutton(this)"
				onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)"
				onMouseUp="highlightbutton(this)" onclick="confirmClose('P');"><s:text name="button.close" /></a></td>
		</tr>
	</table>
	</div>
	<div
		style="position: absolute; left: 6; top: 4; width: 512; height: 15px; visibility: hidden;">
	<table width="320" border="1" cellspacing="0" cellpadding="0"
		height="20" style="visibility: hidden">
		<tr>
			<td id="addenablebutton"><a tabindex="2"
				title='<s:text name="tooltip.addnewparty"/>'
				onMouseOut="collapsebutton(this)"
				onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)"
				onMouseUp="highlightbutton(this)"
				onClick="javascript:openWindow(buildAddCustodianURL('add'),'partymaintenanceaddWindow','left=50,top=190,width=700,height=290,toolbar=0,status=1','true')"><s:text name="button.add" /></a></td>
			<td id="adddisablebutton"><a class="disabled"
				disabled="disabled" title='<s:text name="tooltip.addnewparty"/>'><s:text name="button.add" /></a></td>


			<td id="changeenablebutton"><a tabindex="3"
				title='<s:text name="tooltip.changeSelParty"/>'
				onMouseOut="collapsebutton(this)"
				onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)"
				onMouseUp="highlightbutton(this)"
				onClick="javascript:openWindow(buildChangeCustodianURL('change'),'partymaintenanceaddWindow','left=50,top=190,width=700,height=290,toolbar=0,status=1','true')"><s:text name="button.change" /></a></td>

			<td id="changedisablebutton"><a class="disabled"
				disabled="disabled" title='<s:text name="tooltip.changeSelParty"/>'><s:text name="button.change" /></a></td>

			<td id="deleteenablebutton"><a tabindex="4"
				title='<s:text name="tooltip.deleteSeletedParty"/>'
				onMouseOut="collapsebutton(this)"
				onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)"
				onMouseUp="highlightbutton(this)"
				onClick="javascript:submitFormDelete('delete')"><s:text name="button.delete" /></a></td>
			<td id="deletedisablebutton"><a class="disabled"
				disabled="disabled" title='<s:text name="tooltip.deleteSeletedParty"/>'><s:text name="button.delete" /></a></td>

			<td id="aliasenablebutton"><a tabindex="4"
				title='<s:text name="tooltip.partyAlias"/>'
				onMouseOut="collapsebutton(this)"
				onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)"
				onMouseUp="highlightbutton(this)"
				onClick="javascript:openWindow(showAliasDetails('displayAliasDetails'),'partymaintenanceaddWindow','left=50,top=190,width=785,height=525,toolbar=0, resizable = yes, scrollbars=yes','true')"><s:text name="button.alias" /></a></td>
			<td id="aliasdisablebutton"><a class="disabled"
				disabled="disabled" title='<s:text name="tooltip.partyAlias"/>'><s:text name="button.alias" /></a></td>
		</tr>
	</table>
	</div>
	</div>
	<blockquote>&nbsp;</blockquote>
	<p>&nbsp;</p>
	<script type="text/javascript">
</script>
</s:form>
</body>
</html>