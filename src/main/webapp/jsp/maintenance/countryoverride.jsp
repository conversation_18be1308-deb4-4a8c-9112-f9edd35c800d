<!--
countryoverride.jsp 
JSP File to display Country Maintenance Screen, to display the
Default Weekend and Override Weekend for each country 
  -->
<%@ include file="/taglib.jsp"%>
<%@ page import="org.swallow.util.SwtConstants"%>
<%@ page import="org.swallow.util.CommonDataManager"%>
<html>
<head>
<title><s:text name="country.title.mainWindow" /></title>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
<SCRIPT language="JAVASCRIPT">

/* Get Menu Entity Currency group for this screen*/
var menuEntityCurrGrpAccess = "${requestScope.menuEntityCurrGrpAccess}";
var previousElement=""
/*Added to apply style on mandatory field ,if it's equal to undefined then no field will be colored*/
 mandatoryFieldsArray= "undefined" ;

/**
 * onFilterandSort
 * Method to update the filter color, sort icon , 
 * and enabling and disabling the buttons while sorting and filtering the grid
 */
function onFilterandSort(){
	updateColors();
	disableAllButtons();
}

/**
 * bodyOnLoad()
 * Method called on body onload
 * to load the grid and enable the buttons based on the access 
 */
function bodyOnLoad(){
	xl = new XLSheet("countryOverrideList","table_2", ["String","String", "String","String","String","String"],"211111");
	xl.onsort = xl.onfilter = onFilterandSort;
	highlightTableRows("countryOverrideList");
	var dropBox1 = new SwSelectBox(document.forms[0].elements["countryOverride.id.entityId"],document.getElementById("entityName"));
	
	document.forms[0].menuAccessId.value = "${requestScope.menuAccessId}";
	document.getElementById("changebutton").innerHTML = document.getElementById("changedisablebutton").innerHTML;
	document.forms[0].menuAccessId.value = "${requestScope.menuAccessId}";
}

/**
 * submitForm()
 * @param methodName
 * Method to submit the form
 */
function submitForm(methodName){
     document.forms[0].method.value = methodName;
	 document.forms[0].submit();
}

/**
 * buildChangeCountryURL()
 * @param methodName
 * Method to build change screen URL to open the change screen with selected parameters
 */
function buildChangeCountryURL(methodName){

	var param = 'countryoverride.do?method='+methodName+'&entityId=';
	    param +=document.forms[0].elements['countryOverride.id.entityId'].value;
	    param +='&countryCode=';
	    param +=previousElement.getElementsByTagName("td")[0].innerText;
	    param +='&EntityName=';
		param +=document.getElementById('entityName').innerText;
		param +='&countryName=';
	    param +=previousElement.getElementsByTagName("td")[1].innerText;
		param +='&weekend1=';
	    param +=previousElement.getElementsByTagName("td")[2].innerText;
		param +='&weekend2=';
	    param +=previousElement.getElementsByTagName("td")[3].innerText;
		param +='&overrideWeekend1=';
	    param +=previousElement.getElementsByTagName("td")[4].innerText;
		param +='&overrideWeekend2=';
	    param +=previousElement.getElementsByTagName("td")[5].innerText;
	    return param;
	    
}


/**
 * disableAllButtons()
 * Method to disable  buttons when the grid unselected
 */
function disableAllButtons()	{
	
	var overrideweek1 ="";
	var overrideweek2 ="";
	if(previousElement!=""){
		overrideweek1 =new String(previousElement.getElementsByTagName("td")[4].innerText);
		overrideweek2 = new String(previousElement.getElementsByTagName("td")[5].innerText);
	}	
	if(overrideweek1.trim()!="" || overrideweek2.trim()!=""){		
		previousElement.getElementsByTagName("td")[2].bgColor="#C5C7C7";	
		previousElement.getElementsByTagName("td")[3].bgColor="#C5C7C7";	
		document.getElementById("changebutton").innerHTML = document.getElementById("changedisablebutton").innerHTML;
	}
	else{		
		if(previousElement!=""){
			previousElement.getElementsByTagName("td")[2].bgColor="";	
			previousElement.getElementsByTagName("td")[3].bgColor="";	
		}
		document.getElementById("changebutton").innerHTML = document.getElementById("changedisablebutton").innerHTML;
	}
}

/**
 * onSelectTableRow()
 * @param rowElement
 * @param isSelected
 * Method called when a row in data grid selected, to enable buttons
 */
function onSelectTableRow(rowElement, isSelected){
	var overrideweek1 = new String(rowElement.getElementsByTagName("td")[4].innerText);
	var overrideweek2 = new String(rowElement.getElementsByTagName("td")[5].innerText);
		
	if(isSelected){		
		if(previousElement!=""){
			var preoverrideweek1 = new String(previousElement.getElementsByTagName("td")[4].innerText);		
			var preoverrideweek2 = new String(previousElement.getElementsByTagName("td")[5].innerText);
			if(preoverrideweek1.trim()!="" || preoverrideweek2.trim()!=""){			
				previousElement.getElementsByTagName("td")[2].bgColor="#C5C7C7";	
				previousElement.getElementsByTagName("td")[3].bgColor="#C5C7C7";
				}
			else{		
				
				previousElement.getElementsByTagName("td")[2].bgColor="";	
				previousElement.getElementsByTagName("td")[3].bgColor="";
			}
	
		}
		if(overrideweek1.trim()!="" || overrideweek2.trim()!=""){
		rowElement.getElementsByTagName("td")[2].bgColor="#FFC134";	
		rowElement.getElementsByTagName("td")[3].bgColor="#FFC134";
		}
		
		
		if(menuEntityCurrGrpAccess == "0"){
		document.getElementById("changebutton").innerHTML = document.getElementById("changeenablebutton").innerHTML;
			}
	}else{
		
		if(overrideweek1.trim()!="" || overrideweek2.trim()!=""){
			rowElement.getElementsByTagName("td")[2].bgColor="#C5C7C7";	
			rowElement.getElementsByTagName("td")[3].bgColor="#C5C7C7";
			}
		else{
			rowElement.getElementsByTagName("td")[2].bgColor="";	
			rowElement.getElementsByTagName("td")[3].bgColor="";
		}
		
		document.getElementById("changebutton").innerHTML = document.getElementById("changedisablebutton").innerHTML;
	}
	previousElement=rowElement;	
}



</script>
</head>
<body leftmargin="0" topmargin="0" marginheight="0"
	onLoad="ShowErrMsgWindow('${actionError}');setParentChildsFocus();setFocus(document.forms[0]);bodyOnLoad();"
	onunload="call();">

<s:form action="/countryoverride.do">
	<input name="method" type="hidden" value="display">
	<input name="entityId" type="hidden" value=" ">
	<input name="menuAccessId" type="hidden">
	<div id="country"
		style="position: absolute; left: 10px; top: 10px; width: 1046px; height: 36px; border: 2px outset;"
		color="#7E97AF">
	<div id="Country"
		style="position: absolute; left: 8px; top: 4px; width: 960px; height: 38;">
	<table width="450" border="0" cellpadding="0" cellspacing="0"
		height="25">
		<tr height="24">
			<td width="56px"><b><s:text name="bookCode.entity" /></b></td>
			<td width="28px">&nbsp;</td>
			<td width="120px">
			
				<select id="countryOverrideId" name="countryOverride.id.entityId" tabindex="1" titleKey="tooltip.selectEntityid" class="htmlTextAlpha" onchange="submitForm('showDetails')" style="width:120px">
       		               <s:iterator value="#request.entities">
       						 <option value="<s:property value='value'/>"<s:if test="%{countryOverride.id.entityId == value}">selected</s:if>>
       						 <s:property value='label'/></option>
    						</s:iterator>
				</select>
				
			</td>
			<td width="20">&nbsp;</td>
			<td width="280"><span id="entityName" name="#request.entityName"
				class="spantext"></td>
		</tr>
	</table>
	</div>
	</div>
	<div id="Country" color="#7E97AF"
		style="word-wrap: break-word; position: absolute; border-left: 2px outset; left: 10px; top: 50px; width: 1045px; height: 422px;">
	<div id="Country"
		style="word-wrap: break-word; position: absolute; z-index: 99; left: 0px; top: 0px; width: 1029px; height: 37px !important; ">
	<table class="sort-table" id="table_2" bgcolor="#B0AFAF" width="1025"
		border="1" cellspacing="0" cellpadding="0" height="40px">
		<thead>
			<tr height="40px">
				<td width="90px" align="center" style="border-left-width: 0px;"
					title='<s:text name="tooltip.sortCountryCode"/>'><b><br><s:text name="label.country.countryCode" /></b></td>
				<td width="445px" align="center"
					title='<s:text name="tooltip.sortCountryName"/>'><b><br/><s:text name="label.country.countryName" /></b></td>
				<td width="120px" align="center"
					title='<s:text name="tooltip.sortDefaultWeekend1"/>'><b><s:text name="label.country.defaultWeekend1" /></b></td>
				<td width="120px" align="center"
					title='<s:text name="tooltip.sortDefaultWeekend2"/>'><b><s:text name="label.country.defaultWeekend2" /></b></td>
				<td width="122px" align="center"
					title='<s:text name="tooltip.sortOverrideWeekend1"/>'><b><s:text name="label.country.overrideWeekend1" /></b></td>
				<td width="122px" align="center"
					title='<s:text name="tooltip.sortOverrideWeekend2"/>'><b><s:text name="label.country.overrideWeekend2" /></b></td>
			</tr>
		</thead>
	</table>
	</div>
	<div id="ddscrolltable"
		style="position: absolute; left: 0px; top: 0px; width: 1043px; height: 419px; overflowY: scroll">
	<div id="country"
		style="position: absolute; z-index: 99; left: 0px; top: 41px; width: 1025px; height: 10px;">

	<table class="sort-table" id="countryOverrideList" width="1025"
		border="0" cellspacing="1" cellpadding="0" height="395">
		<tbody>
			<%
				int count = 0;
			%>
			<s:iterator value = "#request.countryList">
				<%
					if (count % 2 == 0) {
				%><tr class="even">
					<%
						} else {
					%>
				
				<tr class="odd">
					<%
						}
							++count;
					%>
					<td width="90px" align="center"><s:property value="id.countryCode" />&nbsp;</td>
					<td width="445px" align="left"><s:property value="countryName" />&nbsp;</td>

					<s:if test="overrideWeekend1 != null && overrideWeekend1 != ''">
						<td width="120px" align="center" bgColor="#C5C7C7">
					</s:if>

					<s:else>

					<s:if test="overrideWeekend2 != null && overrideWeekend2 != ''">
							<td width="120px" align="center" bgColor="#C5C7C7">
					</s:if>
						<s:else> 
							<td width="120px" align="center">
						</s:else>
					</s:else>
					<s:property value="weekend1" />
					&nbsp;
					</td>




					<s:if test="overrideWeekend1 != null && overrideWeekend1 != ''">
						<td width="120px" align="center" bgColor="#C5C7C7">
					</s:if>

					<s:else>
						<s:if test="overrideWeekend2 != null && overrideWeekend2 != ''">
							<td width="120px" align="center" bgColor="#C5C7C7">
						</s:if>
						<s:else>
							<td width="120px" align="center">
						</s:else>
					</s:else>
					<s:property value="weekend2" />
					&nbsp;
					</td>
					<td width="122px" align="center"><s:property value="overrideWeekend1" />&nbsp;</td>
					<td width="122px" align="center"><s:property value="overrideWeekend2" />&nbsp;</td>
				</tr>
			</s:iterator> 
		</tbody>
		<tfoot>
			<tr>
				<td colspan="6"></td>
			</tr>
		</tfoot>
	</table>
	</div>
	</div>
	</div>
	<div id="country"
		style="position: absolute; left: 987; top: 488px; width: 70px; height: 39px; visibility: visible;">
	<table width="60" border="0" cellspacing="0" cellpadding="0"
		height="20">
		<tr height="25">

			<td align="Right"><a tabindex="5" href=#
				onclick="javascript:openWindow(buildPrintURL('print','Country Override Maintenance'),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')"
				onMouseOut="MM_swapImgRestore()"
				onMouseOver="MM_swapImage('Help','','images/help_default.GIF ',1)"><img
				src="images/help_default.GIF " name="Help" border="0"
				title='<s:text name="tooltip.helpScreen"/>'></a></td>
			<!--Start Code modified by  Chidambaranathan for Mantis 1461 for Tab Navigation on 12-May-2011-->
			<td align="right" id="Print"><a tabindex="5"
				onclick="printPage();" 
				onKeyDown="submitEnter(this,event)"
				onMouseOut="MM_swapImgRestore()"
				onMouseOver="MM_swapImage('Print','','images/Print_R.gif ',1)"><img
				src="images/Print.gif " name="Print" border="0"
				title='<s:text name="tooltip.printScreen"/>'></a></td>
		</tr>
	</table>
	</div>
	<div id="ddimagebuttons" color="#7E97AF"
		style="position: absolute; border: 2px outset; left: 10; top: 480px; width: 1046px; height: 39px; visibility: visible;">
	<div id="Holiday"
		style="position: absolute; left: 6; top: 4; width: 220; height: 15px; visibility: visible;">
	<table width="140" border="0" cellspacing="0" cellpadding="0"
		height="20">
		<tr>

			<td id="changebutton" width="70px"></td>
			<td id="closebutton" width="70px"><a
				title='<s:text name="tooltip.close"/>' tabindex="4"
				onMouseOut="collapsebutton(this)"
				onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)"
				onMouseUp="highlightbutton(this)" 
				onKeyDown="submitEnter(this,event)"
				onclick="confirmClose('P');"><s:text name="button.close" /></a></td>
		</tr>
	</table>
	</div>
	<div
		style="position: absolute; left: 6; top: 4; width: 140; height: 15px; visibility: hidden;">
	<table border="0" cellspacing="0" cellpadding="0" height="20"
		style="visibility: hidden">
		<tr>
			<td id="changeenablebutton"><a
				title='<s:text name="tooltip.changeSelCountry"/>' tabindex="3"
				onMouseOut="collapsebutton(this)"
				onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)"
				onMouseUp="highlightbutton(this)"
				onKeyDown="submitEnter(this,event)"
				onClick="javascript:openWindow(buildChangeCountryURL('change'),'','left=50,top=190,width=792,height=235,toolbar=0, resizable=yes scrollbars=yes','true')"><s:text name="button.change" /></a></td>
			<td id="changedisablebutton"><a class="disabled"
				disabled="disabled"><s:text name="button.change" /></a></td>

		</tr>
	</table>
	</div>
	</div>
	<!--End Code modified by  Chidambaranathan  for Mantis 1461 for Tab Navigation on 12-May-2011-->
</s:form>
</body>
</html>