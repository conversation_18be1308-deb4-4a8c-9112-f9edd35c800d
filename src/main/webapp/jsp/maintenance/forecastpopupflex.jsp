<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<!--
  - The main purpose of this jsp file is to load the findpopup.swf to load Add / Find pop up 
  -	And to embed the Add Forecast Monitor Template flex screen. 
  - Also, to load the label values for this screen.	
  -
  - Author(s): Vivekanandan A
  - Date: 25-05-2011
  -->
  

<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@ page import="org.swallow.util.SwtConstants"%>
<%@ page import="org.swallow.util.SwtUtil"%>
<%@ include file="/taglib.jsp"%>
<%@ include file="/angularJSUtils.jsp"%>

<html>
<head>
<title><s:text name="label.findoraddpopup.title.window" /></title>
<style>
body {
	margin: 0px;
	overflow: hidden
}
</style>
<meta name="viewport" content="width=device-width, initial-scale=1">
<link rel="icon" type="image/x-icon" href="favicon.ico">
<link rel="stylesheet" href="angularSources/styles.css?version=<%= SwtUtil.appVersion %>">
</head>
<script type="text/javascript" src="jsp/work/assets/swfobject.js"></script>
<script type="text/javascript">
			var callRefreshDetail= '${requestScope.callRefreshDetail}';
			if(callRefreshDetail == "true"){
			 refreshParent();
			}
			var appName = "<%=SwtUtil.appName%>";
			var requestURL = new String('<%=request.getRequestURL()%>');
			var idy = requestURL.indexOf('/'+appName+'/');
			requestURL=requestURL.substring(0,idy+1) ;

			window.onunload = call;
			
			var templateId = '${requestScope.templateId}';
			var templateName = '${requestScope.templateName}';
			var userId = '${requestScope.userId}';
			var isPublic = '${requestScope.isPublic}';
			var screenName = '${requestScope.screenName}';
			var menuAccessId = '${requestScope.menuAccessId}';
			var	shortName = '${requestScope.shortName}';
			var	description = '${requestScope.description}';
			var	type = '${requestScope.type}';
			var	columnId = '${requestScope.columnId}';
			var ordinalPos = '${requestScope.ordinalPos}';
			var screenRoute = "findPopUp";
			
			var optionWindow= null;
			// This method is called when onload
			window.onload = function () {
			    setTitleSuffix(document.forms[0]);
				setParentChildsFocus();		
			};
			
			
			// Set the label values
			var label = new Array ();
			label["text"] = new Array ();
			label["tip"] = new Array ();
			
			//Type 
			label["text"]["type"] = "<s:text name="label.findoraddpopup.type"/>";
			label["tip"]["type"] = "<s:text name="tooltip.findoraddpopup.type"/>";	
			
			// Entity
			label["text"]["entity"] = "<s:text name="label.findoraddpopup.entity"/>";
			label["tip"]["entity"] = "<s:text name="tooltip.findoraddpopup.entity"/>";
			
			// Search Id
			label["text"]["id"] = "<s:text name="label.findoraddpopup.id"/>";
			label["tip"]["id"] = "<s:text name="tooltip.findoraddpopup.id"/>";
			
			// Search name
			label["text"]["name"] = "<s:text name="label.findoraddpopup.name"/>";
			label["tip"]["name"] = "<s:text name="tooltip.findoraddpopup.name"/>";
			
			// Searchbutton
			label["text"]["button-search"] = "<s:text name="button.search"/>";
			label["tip"]["button-search"] = "<s:text name="tooltip.findoraddpopup.Search"/>";
			
			
			// Add button
			label["text"]["button-add"] = "<s:text name="button.add"/>";
			label["tip"]["button-add"] = "<s:text name="button.add"/>";	
			
			label["text"]["button-cancel"] = "<s:text name="button.cancel"/>";
			label["tip"]["button-cancel"] = "<s:text name="button.cancel"/>";	
			
			/**
			 * refreshParent
			 * Method to refresh parent
			 */
			 function refreshParent(){
			 	window.opener.refreshDetail();
				window.close();
				
			 }
			
			/**
			 * openChildWindow
			 * @param methodName
			 * Method to load child screens
			 */
			function openChildWindow(methodName){
    			var param = 'forecastMonitorTemplate.do?method='+methodName;
	    		optionWindow = window.open(param,'movementWindow','left=50,top=190,width=670,height=500,toolbar=0, resizable=yes, status=yes, scrollbars=yes','true');
					
				return false;
			}
			
			/**
             * help
             * This function opens the help screen 
 			 * @return none
			 */
			function help(){
				openWindow(buildPrintURL('print','Find pop up Normal'),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')
            }
			
			  /**
			  * closeChild
			  * This function used to close the child window
			  */
			function closeChild(){
				// Added By KaisBS : issue 1054_STL_038 : avoid the javascript error if we aren't open the option screen
				if(typeof(optionWindow) == window)   
				optionWindow.close();
			}
			
		</script>
<%@ include file="/angularscripts.jsp"%>
<body scroll="no" style="overflow:hidden" leftmargin="0" topmargin="0" marginheight="0"
	onLoad="setParentChildsFocus();" onunload="closeChild()">
<form id="exportDataForm" target="tmp" method="post">

<!-- <div id="swf"><object id='mySwf' -->
<!-- 	classid='clsid:D27CDB6E-AE6D-11cf-96B8-444553540000' height='100%' -->
<!-- 	width='100%'> -->
<%-- 	<param name='src' value='jsp/maintenance/findpopup.swf?version=<%= SwtUtil.appVersion %>' /> --%>
<!-- 	<param name='flashVars' value='' /> -->
<%-- 	<embed name='mySwf' src='jsp/maintenance/findpopup.swf?version=<%= SwtUtil.appVersion %>' height='100%' --%>
<!-- 		width='100%' flashVars='' /></object></div> -->
<input type="hidden" name="data" id="exportData" /> <input type="hidden"
	name="screen" id="exportDataScreen"
	value="<s:text name="label.findoraddpopup.title.window"/>" /></form>
<iframe name="tmp" width="0%" height="0%" src="#" />
</body>
</html>
