<?xml version="1.0" encoding="utf-8"?>

<%@ page contentType="text/xml" %>
<%@ taglib prefix="s" uri="/struts-tags" %>
<%@ taglib uri="http://java.sun.com/jstl/fmt_rt" prefix="fmt" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>

<accountspecificsweepformat >
	<singletons>
		<menuAccessId><s:property value='#request.menuAccessId' /></menuAccessId>
	</singletons>
	<request_reply>
		<status_ok><s:property value='#request.reply_status_ok' /></status_ok>
		<message><s:property value='#request.reply_message' /></message>
		<location />
	</request_reply>

	<grid>
		<metadata>
			<columns>
				<s:iterator value='#request.column_order' var='order' >
				   <s:if test='"specifiedEntityId"==#request.order' >
						<column heading="<s:text name="label.accountspecificsweepformat.column.entityId"/>"
						 clickable="true" 
						 draggable="true"
						 sort="true"
						 filterable="true" 
						 type="str" 
						 dataelement="specifiedEntityId"
						 width="<s:property value='#request.column_width.specifiedEntityId' />"/>
					</s:if>
					<s:if test='"specifiedAccountId"==#request.order' >
						<column heading="<s:text name="label.accountspecificsweepformat.column.accountId"/>"
						 clickable="true" 
						 draggable="true"
						 sort="true"
						 filterable="true" 
						 type="str" 
						 dataelement="specifiedAccountId"
						 width="<s:property value='#request.column_width.specifiedAccountId' />"/>
					</s:if>
					<s:if test='"accountName"==#request.order' >
						<column heading="<s:text name="label.accountspecificsweepformat.column.accountName"/>"
						 clickable="true" 
						 draggable="true"
						  sort="true"
						 filterable="true" 
						 type="str" 
						 dataelement="accountName"
						 width="<s:property value='#request.column_width.accountName' />"/>
					</s:if>
					<s:if test='"newInternalCrFormat"==#request.order' >
						<column heading="<s:text name="label.accountspecificsweepformat.column.newInternalCrFormat"/>"
						 clickable="true" 
						 draggable="true"
						 filterable="true"
						  sort="true" 
						 type="str" 
						 dataelement="newInternalCrFormat"
						 width="<s:property value='#request.column_width.newInternalCrFormat' />"/>
					</s:if>
					<s:if test='"newInternalDrFormat"==#request.order' >
						<column heading="<s:text name="label.accountspecificsweepformat.column.newInternalDrFormat"/>"
						 clickable="true" 
						 draggable="true"
						 filterable="true" 
						  sort="true"
						 type="str" 
						 dataelement="newInternalDrFormat"
						 width="<s:property value='#request.column_width.newInternalDrFormat' />"/>
					</s:if>
					<s:if test='"newExternalCrFormat"==#request.order' >
						<column heading="<s:text name="label.accountspecificsweepformat.column.newExternalCrFormat"/>"
						 clickable="true" 
						 draggable="true"
						 filterable="true" 
						 type="str" 
						  sort="true"
						 dataelement="newExternalCrFormat"
						 width="<s:property value='#request.column_width.newExternalCrFormat' />"/>
					</s:if>
					<s:if test='"newExternalDrFormat"==#request.order' >
						<column heading="<s:text name="label.accountspecificsweepformat.column.newExternalDrFormat"/>"
						 clickable="true" 
						 draggable="true"
						 filterable="true" 
						 type="str" 
						  sort="true"
						 dataelement="newExternalDrFormat"
						 width="<s:property value='#request.column_width.newExternalDrFormat' />"/>
					</s:if>
					<s:if test='"newExternalCrFormatInt"==#request.order' >
						<column heading="<s:text name="label.accountspecificsweepformat.column.newExternalCrFormatInt"/>"
						 clickable="true" 
						 draggable="true"
						 filterable="true" 
						 type="str" 
						  sort="true"
						 dataelement="newExternalCrFormatInt"
						 width="<s:property value='#request.column_width.newExternalCrFormatInt' />"/>
					</s:if>
					<s:if test='"newExternalDrFormatINt"==#request.order' >
						<column heading="<s:text name="label.accountspecificsweepformat.column.newExternalDrFormatINt"/>"
						 clickable="true" 
						 draggable="true"
						 filterable="true" 
						  sort="true"
						 type="str" 
						 dataelement="newExternalDrFormatINt"
						 width="<s:property value='#request.column_width.newExternalDrFormatINt' />"/>
					</s:if>
				</s:iterator>
			</columns>
		</metadata>
		<rows size="<s:property value='#request.recordCount' />">
			<s:iterator value='#request.accountSpecificSweepFormatList' var='accountSpecificSweepFormatList' >
				<row>
					<hostId clickable="false"><s:property value='#accountSpecificSweepFormatList.id.hostId' /></hostId>
					<entityId clickable="false"><s:property value='#accountSpecificSweepFormatList.id.entityId' /></entityId>
					<accountId clickable="false"><s:property value='#accountSpecificSweepFormatList.id.accountId' /></accountId>
					<accountName clickable="false"><s:property value='#accountSpecificSweepFormatList.accountName' /></accountName>
					<specifiedEntityId clickable="false"><s:property value='#accountSpecificSweepFormatList.id.specifiedEntityId' /></specifiedEntityId>			
					<specifiedAccountId clickable="false"><s:property value='#accountSpecificSweepFormatList.id.specifiedAccountId' /></specifiedAccountId>
					<newInternalCrFormat clickable="false"><s:property value='#accountSpecificSweepFormatList.newInternalCrFormat' /></newInternalCrFormat>
					<newInternalDrFormat clickable="false"><s:property value='#accountSpecificSweepFormatList.newInternalDrFormat' /></newInternalDrFormat>
					<newExternalCrFormat clickable="false"><s:property value='#accountSpecificSweepFormatList.newExternalCrFormat' /></newExternalCrFormat>
					<newExternalDrFormat clickable="false"><s:property value='#accountSpecificSweepFormatList.newExternalDrFormat' /></newExternalDrFormat>
					<newExternalCrFormatInt clickable="false"><s:property value='#accountSpecificSweepFormatList.newExternalCrFormatInt' /></newExternalCrFormatInt>
					<newExternalDrFormatINt clickable="false"><s:property value='#accountSpecificSweepFormatList.newExternalDrFormatINt' /></newExternalDrFormatINt>
				</row>
			</s:iterator>
		</rows>					
	</grid>	
	
	<selects> 
		<select id="entity"> 
			<s:iterator value='#request.entities' var='entity' >
				<option value="<s:property value='#request.entity.value' />"
					selected="<s:if test='#entity.value==#request.entityId'>1</s:if><s:if test='#entity.value!=#request.entityId' >0</s:if>"><s:property value='#entity.label' /></option>					
			</s:iterator> 
		</select> 
		<select id="currency"> 
			<s:iterator value='#request.currencies' var='currency' > 
			 	<option value="<s:property value='#request.currency.value' />" 
					selected="<s:if test='#currency.value==#request.currencyId'>1</s:if><s:if test='#currency.value!=#request.currencyId'>0</s:if>"><s:property value='#currency.label' />					
				</option> 
		 	</s:iterator> 
		</select>
		<select id="accounts"> 
			<s:iterator value='#request.accounts' var='account' > 
				<option value="<s:property value='#request.account.value' />" 
					selected="<s:if test='#account.value==#request.accountId'>1</s:if><s:if test='#account.value!=#request.accountId'>0</s:if>"><s:property value='#account.label' />										
				</option> 
			</s:iterator> 
		</select>
	</selects>		
							
</accountspecificsweepformat>
