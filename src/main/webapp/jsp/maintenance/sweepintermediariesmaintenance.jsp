<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@ include file="/taglib.jsp"%>
<%@ page import="org.swallow.util.SwtConstants" %>
<html>
<head>
<title><s:text name="sweepInter.mainScreen"/></title>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
<SCRIPT language="JAVASCRIPT">

var menuEntityCurrGrpAccess = "${requestScope.menuEntityCurrGrpAccess}";
/*Added to apply style on mandatory field ,if it's equal to undefined then no field will be colored*/
 mandatoryFieldsArray= "undefined" ;

function bodyOnLoad()
{   
    
    setParentChildsFocus();
    setFocus(document.forms[0]);
    ShowErrMsgWindow('${actionError}');
	/*START:code modified by <PERSON><PERSON> on 22-Feb-2010 for Mantis 1121: added Account column in UI screen  */
    xl = new XLSheet("SweepIntermediariesList","table_2", ["String","String","String","String","String"],"22222",false);
	/*END:code modified by Bala on 22-Feb-2010 for Mantis 1121: added Account column in UI screen  */
    xl.onsort = xl.onfilter = onFilterandSort;

    
    highlightTableRows("SweepIntermediariesList");

    var entityBox = new SwSelectBox(document.forms[0].elements["sweepintermediaries.id.entityId"],document.getElementById("entityDesc"));
    if(menuEntityCurrGrpAccess == "0") {
	    document.getElementById("addbutton").innerHTML = document.getElementById("addenablebutton").innerHTML;
	} else {
		document.getElementById("addbutton").innerHTML = document.getElementById("adddisablebutton").innerHTML;
	}
	/*START:code uncommented by Mahesh on 07-Oct-2009 for Mantis 1028: added change button in UI screen  */
    document.getElementById("changebutton").innerHTML = document.getElementById("changedisablebutton").innerHTML;
	/*END:code uncommented by Mahesh on 07-Oct-2009 for Mantis 1028: added change button in UI screen  */	
	document.getElementById("deletebutton").innerHTML = document.getElementById("deletedisablebutton").innerHTML;
	
	document.forms[0].menuAccessId.value = "${requestScope.menuAccessId}";
}
function onSelectTableRow(rowElement, isSelected)
{   
    document.forms[0].selectedEntityId.value = document.forms[0].elements['sweepintermediaries.id.entityId'].value;
	document.forms[0].selectedCurrencyCode.value = rowElement.cells[0].innerText.trim();
	/*START:code added by Mahesh on 07-Oct-2009 for Mantis 1028: added change button in UI screen  */
	document.forms[0].selectedCurrencyName.value = rowElement.cells[1].innerText.trim();
	document.forms[0].selectedIntermediaryBic.value = rowElement.cells[3].innerText.trim();
	/*END:code added by Mahesh on 07-Oct-2009 for Mantis 1028: added change button in UI screen  */
	document.forms[0].selectedTargetBic.value = rowElement.cells[2].innerText.trim();
	/*START:code added by Bala on 22-Feb-2010 for Mantis 1121: added Account column in UI screen  */
	document.forms[0].selectedAccountId.value = rowElement.cells[4].innerText.trim();
	/*END:code added by Bala on 22-Feb-2010 for Mantis 1121: added Account column in UI screen  */
    var count = getCountRowsSelected(rowElement);
    if(menuEntityCurrGrpAccess == "0" && count == 1)
    {   
		/*START:code uncommented by Mahesh on 07-Oct-2009 for Mantis 1028: added change button in UI screen  */
	    document.getElementById("changebutton").innerHTML = document.getElementById("changeenablebutton").innerHTML;
        /*END:code uncommented by Mahesh on 07-Oct-2009 for Mantis 1028: added change button in UI screen  */
		document.getElementById("deletebutton").innerHTML = document.getElementById("deleteenablebutton").innerHTML;
            
    }else{
        /*START:code uncommented by Mahesh on 07-Oct-2009 for Mantis 1028: added change button in UI screen  */
		document.getElementById("changebutton").innerHTML = document.getElementById("changedisablebutton").innerHTML;
        /*END:code uncommented by Mahesh on 07-Oct-2009 for Mantis 1028: added change button in UI screen  */
		document.getElementById("deletebutton").innerHTML = document.getElementById("deletedisablebutton").innerHTML;
    }

} 
function onFilterandSort(){
    updateColors();
	disableAllButtons();
}

function disableAllButtons()
{
	/*START:code uncommented by Mahesh on 07-Oct-2009 for Mantis 1028: added change button in UI screen  */
	document.getElementById("changebutton").innerHTML = document.getElementById("changedisablebutton").innerHTML;
	/*END:code uncommented by Mahesh on 07-Oct-2009 for Mantis 1028: added change button in UI screen  */
	document.getElementById("deletebutton").innerHTML = document.getElementById("deletedisablebutton").innerHTML; 

}
function submitForm(methodName){
	if(methodName == 'displayList') {
		document.forms[0].method.value = methodName;
		document.forms[0].submit();
	}
	else {
		var yourstate=window.confirm('<s:text name="confirm.delete"/>');
		if (yourstate==true){
			document.forms[0].method.value = methodName;
			document.forms[0].submit();
		}
	}
}

function addSweepIntermediaries(methodName){
    document.forms[0].method.value = methodName;
	document.forms[0].selectedEntityCode.value=document.getElementById("entityDesc").innerText;

	var param = 'sweepintermediaries.do?method='+methodName+'&selectedEntityId=';
	param +=  document.forms[0].elements['sweepintermediaries.id.entityId'].value;
	param += '&selectedEntityCode=' + document.getElementById('entityDesc').innerText;
		
	return  param;
}

/*START:code added by Mahesh on 07-Oct-2009 for Mantis 1028: added change button in UI screen  */
function changeSweepIntermediaries(methodName){

    document.forms[0].method.value = methodName;
    document.forms[0].selectedEntityCode.value=document.getElementById("entityDesc").innerText;
	/*START:code modified by Bala on 22-Feb-2010 for Mantis 1121: added Account column in UI screen*/
	var param = 'sweepintermediaries.do?method='+methodName+'&selectedEntityId=';
	param +=  document.forms[0].elements['sweepintermediaries.id.entityId'].value;
	param += '&selectedEntityCode=' + document.getElementById('entityDesc').innerText;	
	param += '&selectedCurrencyCode=' + document.forms[0].selectedCurrencyCode.value;
	param += '&selectedCurrencyName=' + document.forms[0].selectedCurrencyName.value;	
	param += '&selectedTargetBic=' + document.forms[0].selectedTargetBic.value;	
	param += '&selectedIntermediaryBic=' + document.forms[0].selectedIntermediaryBic.value;
	param += '&selectedAccountId=' + document.forms[0].selectedAccountId.value;
	/*END:code modified by Bala on 22-Feb-2010 for Mantis 1121: added Account column in UI screen*/
	
	return  param;
}
/*END:code added by Mahesh on 07-Oct-2009 for Mantis 1028: added change button in UI screen  */

</SCRIPT>
</head>

<body leftmargin="0" topmargin="0" marginheight="0" onLoad="bodyOnLoad();" onunload="call()">
<s:form action="sweepintermediaries.do">

<input name="method" type="hidden" value="display">
<input name="selectedCurrencyCode" type="hidden">
<!--START:code added by Mahesh on 07-Oct-2009 for Mantis 1028: added change button in UI screen  -->
<input name="selectedCurrencyName" type="hidden">
<input name="selectedIntermediaryBic" type="hidden">
<!--END:code added by Mahesh on 07-Oct-2009 for Mantis 1028: added change button in UI screen  -->
<input name="selectedEntityId" type="hidden">
<input name="selectedTargetBic" type="hidden">
<input name="selectedEntityCode" type="hidden">
<input name="menuAccessId" type="hidden" >
<!--START:code added by Bala on 22-Feb-2010 for Mantis 1121: added Account column in UI screen   -->
<input name="selectedAccountId" type="hidden" >

<div id="SweepInter" style="position:absolute; left:20px; top:20px; width:910px; height:42px; border:2px outset;" color="#7E97AF">
<!--END:code added by Bala on 22-Feb-2010 for Mantis 1121: added Account column in UI screen   -->
<div id="SweepInter" style="position:absolute; left:8px; top:4px; width:704px; height:400;">
<!--START:code added by Mahesh on 09-Oct-2009 for Mantis 1028: Increase the table width  -->
<table width="460" border="0" cellpadding="0" cellspacing="0" height="30">
<!--START:code added by Mahesh on 09-Oct-2009 for Mantis 1028: Increase the table width -->
    <tr color="black" border="0">
      <td width="38"><b><s:text name="entity.id"/></b></td>
      <td width="28">&nbsp;</td>
      <td width="140px">
          <s:select id="sweepintermediaries.id.entityId" name="sweepintermediaries.id.entityId" titleKey="tooltip.selectEntity" onchange="submitForm('displayList');" cssStyle="width:140px" list="#request.entities" listKey="value" listValue="label" />
          
      </td>
       <td width="20">&nbsp;</td>
      <td width="280">
        <span id="entityDesc" name="entityDesc" class="spantext">
       </td>
     </tr>
</table>
</div>
</div>
<!--START:code modified by Bala on 22-Feb-2010 for Mantis 1121: added Account column in UI screen   -->
<div id="SweepInter" style="position:absolute; left:20px; top:67px; width:910px; height:469px; border-left:2px outset;" color="#7E97AF">
    <div id="SWEEPInter" style="position:absolute;z-index:99;left:0px; top:0px; width:890px; height:10px;">
        <table  class="sort-table" id="table_2" bgcolor="#B0AFAF" width="891px" border="0" cellspacing="1" cellpadding="0"  height="20px">
<!--END:code modified by Bala on 22-Feb-2010 for Mantis 1121: added Account column in UI screen   -->	
            <thead>
                <tr>
					<!--START:code modified by Bala on 09-Mar-2010 for Mantis 1121: added Account column in UI screen and increasing the size of the column  -->
                    <td width="70px" height="23px"  style="border-left-width: 0px;" title='<s:text name="tooltip.currency"/>' align="left"><b><s:text name="sweepInter.currency"/></b></td>
                    <td width="210px" height="23px" title='<s:text name="tooltip.curName"/>' align="left"><b><s:text name="sweepInter.curName"/></b></td>
					 <td width="100px" height="23px" title='<s:text name="tooltip.targetBIC"/>' align="left"><b><s:text name="sweepInter.targetBIC"/></b></td>
                    <td width="118px" height="23px" title='<s:text name="tooltip.InterBIC"/>' align="left"><b><s:text name="sweepInter.InterBIC"/></b></td>
					
					<td width="246px" height="23px" title='<s:text name="tooltip.sortByAccount"/>' align="left"><b><s:text name="sweepIntermediaries.accountId"/></b></td>
					<!--END:code modified by Bala on 09-Mar-2010 for Mantis 1121: added Account column in UI screen and increasing the size of the column   -->
                </tr>
            </thead>
        </table>
    </div>
<!--START:code modified by Bala on 22-Feb-2010 for Mantis 1121: added Account column in UI screen   -->
    <div id="ddscrolltable" style="position:absolute; left:0px; top:0px; width:906px; height:465px;overflowY:scroll">
        <div id="SWEEPInter" style="position:absolute;z-index:99;left:1px; top:22px; width:889px; height:10px;">
            <table class="sort-table" id="SweepIntermediariesList" width="888px" border="0" cellspacing="1" cellpadding="0" height="443px" >
<!--END:code modified by Bala on 22-Feb-2010 for Mantis 1121: added Account column in UI screen   -->		
                <tbody>         
                
                  <%int count = 0; %>  
                    <s:iterator value="#request.SweepIntermediariesList" var="SweepIntermediariesList" >          
                        <% if( count%2 == 0 ) {%><tr class="even"><% }else  { %> <tr class="odd"> <%}++count; %>
						<!--START:code modified by Bala on 09-Mar-2010 for Mantis 1121: added Account column in UI screen and increasing the size of the column  -->
                             <s:hidden name="#SweepIntermediariesList.id.currencyCode"/>
                             <td  width="70px" align="left"><s:property value="#SweepIntermediariesList.id.currencyCode" />&nbsp;</td>
                            <td  width="210px"><s:property value="#SweepIntermediariesList.currencyName" />&nbsp;</td>
                             <td  width="100px" align="left"><s:property value="#SweepIntermediariesList.id.targetBic" />&nbsp;</td>
                            <td  width="118px"><s:property value="#SweepIntermediariesList.intermediary" />&nbsp;</td>

							<td  width="246px"><s:property value="#SweepIntermediariesList.accountId" />&nbsp;</td>
						<!--END:code modified by Bala on 09-Mar-2010 for Mantis 1121: added Account column in UI screen and increasing the size of the column   -->						
						</tr>
                    </s:iterator>
                    </tbody>
<!--START:code modified by Bala on 22-Feb-2010 for Mantis 1121: added Account column in UI screen   -->			
                    <tfoot><tr><td colspan="5"></td></tr></tfoot>
<!--END:code modified by Bala on 22-Feb-2010 for Mantis 1121: added Account column in UI screen   -->			
            </table>
        </div>
    </div>
</div>
<!-- Button and Help icon Starting -->
<!--START:code modified by Bala on 22-Feb-2010 for Mantis 1121: added Account column in UI screen   -->
<div id="SweepInter" style="position:absolute; left:855; top:550px; width:70px; height:15px; visibility:visible;">
<!--END:code modified by Bala on 22-Feb-2010 for Mantis 1121: added Account column in UI screen   -->
    <table width="60px" border="0" cellspacing="0" cellpadding="0" height="20">
        <tr>
        <td align="Right">
                <a title='<s:text name="tooltip.helpScreen"/>' tabindex="6" href=# onclick="javascript:openWindow(buildPrintURL('print','Sweep Intermediary Setup'),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')" onMouseOut="MM_swapImgRestore()" onMouseOver="MM_swapImage('Help','','images/help_default.GIF ',1)"><img src="images/help_default.GIF " name="Help"  border="0" title='<s:text name="tooltip.helpScreen"/>' ></a> 
        </td>

            <td align="right" id="Print">
                <a tabindex="6" onclick="printPage();" onMouseOut="MM_swapImgRestore()" onMouseOver="MM_swapImage('Print','','images/Print_R.gif ',1)"><img src="images/Print.gif " name="Print"  border="0" title='<s:text name="tooltip.printScreen"/>'></a>   
            </td>
        </tr>
    </table>
</div>
<!--START:code modified by Bala on 22-Feb-2010 for Mantis 1121: added Account column in UI screen  -->
<div id="ddimagebuttons" color="#7E97AF" style="position:absolute; border:2px outset; left:20; top:540px; width:910px; height:39px; visibility:visible;">
<!--END:code modified by Bala on 22-Feb-2010 for Mantis 1121: added Account column in UI screen  -->
<div id="SweepInter" style="position:absolute; left:6; top:4; width:360pxpx; height:15px; visibility:visible;">
  <!--START:code added by Mahesh on 14-Oct-2009 for Mantis 1031: adjusted width between buttons  -->
  <table width="250px" border="0" cellspacing="0" cellpadding="0" height="20">
  <!--END:code added by Mahesh on 14-Oct-2009 for Mantis 1031: adjusted width between buttons  -->
    <tr>
        <td width="70px" id="addbutton" ></td>
		<!--START:code uncommented by Mahesh on 07-Oct-2009 for Mantis 1028: added change button in UI screen  -->
		<td id="changebutton"  width="70px"></td> 
        <!--END:code uncommented by Mahesh on 07-Oct-2009 for Mantis 1028: added change button in UI screen  -->
		<td id="deletebutton" width="70px"></td>
        <td id="closebutton" width="70px" title='<s:text name="tooltip.close"/>'>       
            <a tabindex="5" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onclick="confirmClose('P');"><s:text name="button.close"/></a>            
        </td>
    </tr>
    </table>
</div>
    <div style="position:absolute; left:6; top:4; width:440px; height:15px; visibility:hidden;">    
    <table width="280px" border="0" cellspacing="0" cellpadding="0" height="20" style="visibility:hidden">
    <tr>
         <td id="addenablebutton">
         <!--START:code modified by Bala on 01-Mar-2010 for Mantis 1121: added Account column in UI screen  -->
            <a  tabindex="1"  title='<s:text name="tooltip.addNewSweepIntermediary"/>' onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onClick="javascript:openWindow(addSweepIntermediaries('add'),'sweepIntermediariesaddWindow','left=50,top=190,width=595,height=235,toolbar=0, resizable=yes, scrollbars=yes','true')"><s:text name="button.add"/></a>
         <!--END:code modified by Bala on 01-Mar-2010 for Mantis 1121: added Account column in UI screen  -->
        </td>
        <td id="adddisablebutton">
            <a  class="disabled" disabled="disabled" ><s:text name="button.add"/></a>
        </td>
		<!--START:code added by Mahesh on 07-Oct-2009 for Mantis 1028: added change button in UI screen  -->
		<td id="changeenablebutton">
		<!--START:code modified by Bala on 01-Mar-2010 for Mantis 1121: added Account column in UI screen  -->
            <a  tabindex="1"  title='<s:text name="tooltip.changeNewSweepIntermediary"/>' onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onClick="javascript:openWindow(changeSweepIntermediaries('change'),'sweepIntermediarieschangeWindow','left=50,top=190,width=595,height=235,toolbar=0, resizable=yes, scrollbars=yes','true')"><s:text name="button.change"/></a>
        <!--END:code modified by Bala on 01-Mar-2010 for Mantis 1121: added Account column in UI screen  -->
        </td>
        <td id="changedisablebutton">
            <a  class="disabled" disabled="disabled" ><s:text name="button.change"/></a>
        </td>
		<!--END:code added by Mahesh on 07-Oct-2009 for Mantis 1028: added change button in UI screen  -->
        <td id="deleteenablebutton" >       
            <a tabindex="4" title='<s:text name="tooltip.deleteSelectedSweepIntermediary"/>' onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onClick="javascript:submitForm('delete')"><s:text name="button.delete"/></a>
        </td>       
        <td id="deletedisablebutton">
            <a class="disabled" disabled="disabled"><s:text name="button.delete"/></a>
        </td>                             
    </tr>
    </table>
  </div>
</div>
</s:form>
</body>
</html>