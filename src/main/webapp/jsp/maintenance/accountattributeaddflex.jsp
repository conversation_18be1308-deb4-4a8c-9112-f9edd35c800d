<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">

<%@ page import="org.swallow.util.SwtUtil"%>
<%@ include file="/taglib.jsp"%>
<%@ include file="/angularJSUtils.jsp"%>

<html>
<head>
<meta name="viewport" content="width=device-width, initial-scale=1">
<link rel="icon" type="image/x-icon" href="favicon.ico">
<link rel="stylesheet" href="angularSources/styles.css?version=<%= SwtUtil.appVersion %>">
</head>
<body>
	<style>
body {
	margin: 0px;
	overflow: hidden
}
</style>
	<script type="text/javascript" src="jsp/work/assets/swfobject.js"></script>
	<script type="text/javascript">
		var screenTitle = "";
		screenTitle = getMessage("label.accountattributeadd.title.window", null);
		document.title = screenTitle;
		var appName = "<%=SwtUtil.appName%>";
		var requestURL = new String('<%=request.getRequestURL()%>');
		var idy = requestURL.indexOf('/' + appName + '/');
		var mainWindow = null;
		requestURL = requestURL.substring(0, idy + 1);
		var menuAccessId = '${requestScope.menuAccessId}';
		var screenRoute = "AccountAttributeMaintenanceAdd";
		// This method is called when onload
		window.onload = function() {
			setTitleSuffix(document.forms[0]);
			setParentChildsFocus();
		};

		window.onunload = call;
		var menuAccessId = '${requestScope.menuAccessId}';
		var dbDate = '${requestScope.sysDateAsString}';
		var selectedAccountGroup = '${selectedAccountGroup}';
		var methodName = '${methodName}';
		var currencyCode = '${currencyCode}';
		var entityId = '${entityId}';
		var accountId = '${accountId}';
		var attributeId = '${attributeId}';
		var effectivedateTime = '${effectivedateTime}';
		var valueStr = '${value}';
		var seqKey = '${seqKey}';

		var parentScreen = '${parentScreen}';
		var maintainAnyGroup = '${maintainAnyGroup}';
		var dateFormat = '<s:property value="#request.session.CDM.dateFormatValue" />';
		var currencyFormat = '<s:property value="#request.session.CDM.currencyFormat"/>';
		var ccyPattern = 0;

		if (currencyFormat.indexOf("1") != -1)
			ccyPattern = 0;
		else if (currencyFormat.indexOf("2") != -1)
			ccyPattern = 1;

		/**
		 * Close window
		 */
		function closeWindow() {//refresh data grid in parent screen
			window.opener.updateData();
			window.close();
		}

		/**
		 * help
		 * This function opens the help screen 
		 * @return none
		 */
		function help() {
			openWindow(
					buildPrintURL('print',
							'Account Attribute Maintenance Screen Details'),
					'sectionprintdwindow',
					'left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no',
					'true')
		}

		/**
		 * closeChild
		 * This function used to close the child window
		 */
		function closeChild() {
			window.close();
		}
	</script>
<%@ include file="/angularscripts.jsp"%>
	<form id="exportDataForm" target="tmp" method="post">
		<input type="hidden" name="data" id="exportData" /> <input
			type="hidden" name="screen" id="exportDataScreen"
			value="<s:text name="label.accountattributedefinition.title.window"/>" />
	</form>
	<iframe name="tmp" width="0%" height="0%" src="#" />
</body>
</html>