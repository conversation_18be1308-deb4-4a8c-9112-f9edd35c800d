<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">

<%@ page import="org.swallow.util.SwtUtil"%>
<%@ include file="/taglib.jsp"%>
<%@ include file="/angularJSUtils.jsp"%>
<html>
<head>
<meta name="viewport" content="width=device-width, initial-scale=1">
<link rel="icon" type="image/x-icon" href="favicon.ico">
<link rel="stylesheet" href="angularSources/styles.css?version=<%= SwtUtil.appVersion %>">
</head>
<body>
	<style>
body {
	margin: 0px;
	overflow: hidden
}
</style>
	<script type="text/javascript" src="jsp/work/assets/swfobject.js"></script>
	<script type="text/javascript">
		var screenTitle = "";
		screenTitle = getMessage("label.accountschedulesweepdetails.title.window", null);
		document.title = screenTitle;
		
			var appName = "<%=SwtUtil.appName%>";
			var requestURL = new String('<%=request.getRequestURL()%>');
			var idy = requestURL.indexOf('/'+appName+'/');
			var mainWindow= null;
			requestURL=requestURL.substring(0,idy+1) ;
			var screenRoute = "AccountScheduleSweepDetails";
		
			var menuAccessId = '${requestScope.menuAccessId}';
			var attributeId = '${requestScope.selectedAttributeId}';
			var screenName = '${requestScope.screenName}';
			
			var methodName= '${requestScope.method}';
			var entityId = '${requestScope.entityId}';
			var accountId= '${requestScope.accountId}';
			var seqNumber= '${requestScope.seqNumber}';
			var currencyCode ='${requestScope.currencyCode}';
			var parentMethodName = '${requestScope.parentMethodName}';
			var defaultTargetBalance = '${requestScope.defaultTargetBalance}';
			
			var defaultTargetBalanceType = '${requestScope.defaultTargetBalanceType}';
			var defaultBookCode = '${requestScope.defaultBookCode}';
			var defaultSettlementMethod = '${requestScope.defaultSettlementMethod}';
			
			
			var defaultMinAmount = '${requestScope.defaultMinAmount}';
			var defaultFromBalanceType = '${requestScope.defaultFromBalanceType}';
			var defaultOtherFromBalType = '${requestScope.defaultOtherFromBalType}';
			var maxAmoutValue = '${requestScope.maxAmoutValue}';
			
			
			
			
			// This method is called when onload
			window.onload = function () {
			    setTitleSuffix(document.forms[0]);
				setParentChildsFocus();									
			};
		
			window.onunload = call;
			var menuAccessId = '${requestScope.menuAccessId}';
			
	
			// Used to check the regex expression's syntax 
			function checkRegex(regexStr) {
				try {
					var regex = new RegExp(regexStr);
					return  true;
				} catch (error) {
					return false;
				}
			}
			// Used to check if an account attribute have an existing data
			function checkExistingAttributeData(attributeId){
				
				var appName = "<%=SwtUtil.appName%>";
				var requestURL = new String('<%=request.getRequestURL()%>');
				var idy = requestURL.indexOf('/' + appName + '/');

					requestURL = requestURL.substring(0, idy + 1);
					requestURL = requestURL + appName
							+ "/accountAttribute.do?method=checkExistingAttributeData";
					requestURL = requestURL + "&attributeId="+attributeId;
					var oXMLHTTP = new XMLHttpRequest();
					oXMLHTTP.open("POST", requestURL, false);
					oXMLHTTP.send();
					var res = new String(oXMLHTTP.responseText);
				return res == "N" ? false : true;
			}
			
			
			/**
			 * Close window
			 */
			function closeWindow() {
				window.close();
			}
		
			/**
			 * help
			 * This function opens the help screen 
			 */
			function help() {
				openWindow(
						buildPrintURL('print', 'Define Attribute'),
						'sectionprintdwindow',
						'left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no',
						'true')
			}
			
			function getRecordsTiming(){
				return window.opener.getRecordsTiming();
			}
	</script>
<%@ include file="/angularscripts.jsp"%>
	<form id="exportDataForm" target="tmp" method="post">
		<input type="hidden" name="data" id="exportData" /> <input
			type="hidden" name="screen" id="exportDataScreen"
			value="<s:text name="label.forecastMonitorTemplate.title.window"/>" />
	</form>
	<iframe name="tmp" width="0%" height="0%" src="#" />
</body>
</html>