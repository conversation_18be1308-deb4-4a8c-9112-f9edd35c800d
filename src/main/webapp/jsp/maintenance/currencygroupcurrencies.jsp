<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@ include file="/taglib.jsp"  %>
<%@ page import="org.swallow.util.SwtConstants" %>
<html>
<head>
<title><s:text name="currencyGroupMaintenance.title.currencies"/></title>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
<SCRIPT language="JAVASCRIPT">

function onFilterandSort()
{
	updateColors();
}

function bodyOnLoad()
{
	xl = new XLSheet("currencyGroupCurrencies","table_2", ["String","String"],"11");
	xl.onsort = xl.onfilter = onFilterandSort;
}

</SCRIPT>
</head>

<s:form action="currencygroup.do">

<body onmousemove="reportMove()" leftmargin="0" topmargin="0" marginheight="0" onLoad="bodyOnLoad();setParentChildsFocus();setTitleSuffix(document.forms[0]);" onunload="call()">

<div id="CurrencyGroup" color="#7E97AF" style="position:absolute; border:2px outset; left:20px; top:20px; width:373px; height:459px;">
	<div id="CurrencyGroup" style="position:absolute;z-index:50;left:0px; top:0px; width:353px; height:10px;">
	<table  class="sort-table" id="table_2" bgcolor="#B0AFAF" width="348px" border="0" cellspacing="1" cellpadding="0"  height="20px">
	<thead>
		<tr height="20px">
		    <td  title ='<s:text name="tooltip.sortCurrencyId"/>' width="68px"><b><s:text name="currency.id"/></b></td>
			<td title ='<s:text name="tooltip.sortCurrencyName"/>' width="280px"><b><s:text name="currency.name"/></b></td>	
		</tr>
	</thead>
</table>
 </div>

<div id="ddscrolltable" style="position:absolute; left:0px; top:0px; width:368px; height:455px; overflowY:scroll;">
	<div id="CurrencyGroup" style="position:absolute;z-index:99;left:0px; top:22px; width:348px; height:10px;">
	<table class="sort-table" id="currencyGroupCurrencies" width="348px" border="0" cellspacing="1" cellpadding="0" height="433">
		<tbody> 		
			<%int count = 0; %>  
				<s:iterator  value="#request.currencyGroupCurrencies" >          
				<% if( count%2 == 0 ) {%><tr  class="even"><% }else  { %> <tr  class="odd"> <%}++count; %>
					<td align="left" width="68px">
						    <s:property value="id.currencyCode" />&nbsp;
					</td>
					<td width="280px" align="left">
						 <s:property value="currencyMaster.currencyName"/>&nbsp;
					</td>						
				</tr>
				</s:iterator>  
		</tbody>
	<tfoot><tr><td colspan="2" ></td></tr></tfoot>
</table>
</div>
</div>
</div>


<div id="Currency" style="position:absolute; left:316; top:495; width:70px; height:15px; visibility:visible;">
	<table width="60px" border="0" cellspacing="0" cellpadding="0" height="20">
		<tr>
		    <td align="Right">
		    	<!-- Start:Viveakanandan 30/07/2008 opens separate help dialog for currencies -->
			    <a tabindex="7" href=# onclick="javascript:openWindow(buildPrintURL('print','Currency Group Currencies Maintenance'),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')" onMouseOut="MM_swapImgRestore()" onMouseOver="MM_swapImage('Help','','images/help_default.GIF ',1)"><img src="images/help_default.GIF " name="Help"  border="0" title='<s:text name="tooltip.helpScreen"/>'></a>
			    <!-- End:Viveakanandan 30/07/2008 opens separate help dialog for currencies --> 
            </td>

			<td align="right" id="Print">
				<a tabindex="7" onclick="printPage();" onMouseOut="MM_swapImgRestore()" onMouseOver="MM_swapImage('Print','','images/Print_R.gif ',1)"><img src="images/Print.gif " name="Print"  border="0" title='<s:text name="tooltip.printScreen"/>'></a>	
			</td>
		</tr>
	</table>
</div>
<div id="ddimagebuttons" color="#7E97AF" style="position:absolute; border:2px outset; left:20; top:487; width:373px; height:39px; visibility:visible;">
  <div id="Currency" style="position:absolute; left:6; top:4; width:375; height:15px; visibility:visible;">
  	  <table width="70" border="0" cellspacing="0" cellpadding="0" height="20">
	<tr>
		<td id="closebutton" width="70" title='<s:text name="tooltip.close"/>'>	<a tabindex="6" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onclick="confirmClose('P');"><s:text name="button.close"/></a></td>
	</tr>
	</table>
</div>

</div>
</s:form>
</body>
</html>