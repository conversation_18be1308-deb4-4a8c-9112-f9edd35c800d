<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@ include file="/taglib.jsp"  %>
<%@ page import="org.swallow.util.SwtConstants" %>

<%@page import="org.swallow.maintenance.model.CurrencyGroup"%>
<html>

<head>
<title><s:text name="currencyGroupMaintenance.title.mainWindow"/></title>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">

<SCRIPT language="JAVASCRIPT">



var menuEntityCurrGrpAccess = "${requestScope.menuEntityCurrGrpAccess}";

/*Added to apply style on mandatory field ,if it's equal to undefined then no field will be colored*/
 mandatoryFieldsArray= "undefined" ;


function disableAllButtons()
{
	document.getElementById("changebutton").innerHTML = document.getElementById("changedisablebutton").innerHTML;
	document.getElementById("deletebutton").innerHTML = document.getElementById("deletedisablebutton").innerHTML; 
	
	document.getElementById("currencybutton").innerHTML = document.getElementById("currencydisablebutton").innerHTML;
	
}

function onFilterandSort()
{
	updateColors();
	disableAllButtons();

}

function bodyOnLoad()
{
	xl = new XLSheet("currencyGroupDetails","table_2", ["String","String", "Number"],"111");
	xl.onsort = xl.onfilter = onFilterandSort;
	highlightTableRows("currencyGroupDetails");	
	var dropBox1 = new SwSelectBox(document.forms[0].elements["currencyGroup.id.entityId"],document.getElementById("entityName"));	
	
	
	if(menuEntityCurrGrpAccess == "0"){
	
		document.getElementById("addbutton").innerHTML = document.getElementById("addenablebutton").innerHTML;	
	}else{
		document.getElementById("addbutton").innerHTML = document.getElementById("adddisablebutton").innerHTML;	
	}
	document.getElementById("changebutton").innerHTML = document.getElementById("changedisablebutton").innerHTML;
	document.getElementById("deletebutton").innerHTML = document.getElementById("deletedisablebutton").innerHTML;
	document.getElementById("currencybutton").innerHTML = document.getElementById("currencydisablebutton").innerHTML;
		
	
	document.forms[0].menuAccessId.value = "${requestScope.menuAccessId}";
	
}

function submitForm(methodName){
	document.forms[0].method.value = methodName;
	document.forms[0].submit();
}

function submitDeleteForm(methodName){
	document.forms[0].method.value = methodName;	
	var yourstate=window.confirm('<s:text name="confirm.delete"/>');
		if (yourstate==true){ 
		document.forms[0].entityCode.value = document.forms[0].elements["currencyGroup.id.entityId"].value;
		document.forms[0].submit();
		}
}

function buildAddCurrencyGroup(methodName){
	var param = 'currencygroup.do?method='+methodName+'&entityCode=';
	param +=  document.forms[0].elements["currencyGroup.id.entityId"].value;
	param += '&entityText=' + document.getElementById('entityName').innerText;
	return  param;	
}

function buildChangeCurrencyGroup(methodName){	
	var param = 'currencygroup.do?method='+methodName+'&entityCode=';
	param +=  document.forms[0].elements["currencyGroup.id.entityId"].value;
	param += '&entityText=' + document.getElementById('entityName').innerText;
	param += '&selectedCurrencyGroupId=' + document.forms[0].selectedCurrencyGroupId.value;
	param += '&selectedCurrencyGroupName=' + document.forms[0].selectedCurrencyGroupName.value;
	return  param;
}

function currencies(methodName){	
	var param = 'currencygroup.do?method='+methodName+'&entityCode=';
	param +=  document.forms[0].elements["currencyGroup.id.entityId"].value;	
	param += '&selectedCurrencyGroupId=' + document.forms[0].selectedCurrencyGroupId.value;	
	return  param;
}

function onSelectTableRow(rowElement, isSelected)
{	
	var hiddenElement0 = rowElement.cells[0].innerText;	
	var hiddenElement1 = rowElement.cells[1].innerText;	
	document.forms[0].selectedCurrencyGroupId.value = hiddenElement0.trim();
	document.forms[0].selectedCurrencyGroupName.value = hiddenElement1.trim();	
	var count = getCountRowsSelected(rowElement);
	
	
	if(menuEntityCurrGrpAccess == "0" && isSelected)
	
	{		
		document.getElementById("changebutton").innerHTML = document.getElementById("changeenablebutton").innerHTML;
		document.getElementById("deletebutton").innerHTML = document.getElementById("deleteenablebutton").innerHTML;		
		
	}else{		
		document.getElementById("changebutton").innerHTML = document.getElementById("changedisablebutton").innerHTML;
		document.getElementById("deletebutton").innerHTML = document.getElementById("deletedisablebutton").innerHTML;
		
	}

		if(isSelected && rowElement.cells[2].innerText.trim() != '0'){
	
		document.getElementById("currencybutton").innerHTML = document.getElementById("currencyenablebutton").innerHTML;
	}else{
		document.getElementById("currencybutton").innerHTML = document.getElementById("currencydisablebutton").innerHTML;
	}
}

</SCRIPT>
</head>

<s:form action="currencygroup.do">
<input name="method" type="hidden" value="display">
<input name="entityCode" type="hidden" >
<input name="selectedCurrencyGroupId" type="hidden" >
<input name="selectedCurrencyGroupName" type="hidden" >


<input name="menuAccessId" type="hidden" >


<body onmousemove="reportMove()" leftmargin="0" topmargin="0" marginheight="0" onLoad="bodyOnLoad();ShowErrMsgWindow('${actionError}');setParentChildsFocus();setFocus(document.forms[0]);" onunload="call()">

<div id="CurrencyGroup" style="position:absolute; left:20px; top:20px; width:513px; height:42px; border:2px outset;" color="#7E97AF">

<div id="CurrencyGroup" style="position:absolute; left:8px; top:4px; width:470px; height:400;">
<table width="468" border="0" cellpadding="0" cellspacing="0" height="30">
	<tr color="black" border="0">
	  <td width="38"><b><s:text name="bookCode.entity"/></b></td>
	  <td width="28">&nbsp;</td>
	  <td width="140px">
	  
	  		<select id="ccyGrpId" name="currencyGroup.id.entityId"
			tabindex="1" titleKey="tooltip.selectEntityid" class="htmlTextAlpha" onchange="submitForm('displayListByEntity')"
			styleClass="htmlTextAlpha" style="width: 140px" titleKey="tooltip.entityId">
			<s:iterator value="#request.entityList">
			<option value="<s:property value='value'/>"<s:if test="%{currencyGroup.id.entityId == value}">selected</s:if>>
			<s:property value='label' /></option>
			</s:iterator>
			</select>
	  </td>
	   <td width="20">&nbsp;</td>
	  <td width="280">
		<span id="entityName" name="#request.entityName" class="spantext">
	   </td>
	 </tr>
</table>
</div>

</div>

<div id="CurrencyGroup" color="#7E97AF" style="position:absolute; border-left:2px outset; left:20px; top:65px; width:513px; height:459px;">
	<div id="CurrencyGroup" style="position:absolute;z-index:50;left:0px; top:0px; width:493px; height:10px;">
	<table  class="sort-table" id="table_2" bgcolor="#B0AFAF" width="488x" border="0" cellspacing="1" cellpadding="0"  height="23px">
	<thead>
		<tr height="20px">
		    <td  title ='<s:text name="tooltip.sortCurrencygroup"/>' width="120px" style="border-left-width: 0px;"><b><s:text name="currencygroup.group"/></b></td>
			<td title ='<s:text name="tooltip.sortCurrencyGroupName"/>' width="280px"><b><s:text name="section.sectionName"/></b></td>
			<td title ='<s:text name="tooltip.sortCurrency"/>' width="88px"><b><s:text name="currencygroup.ccy"/></b></td>
	
		</tr>
	</thead>
</table>
 </div>

<div id="ddscrolltable" style="position:absolute; left:0px; top:0px; width:509px; height:455px; overflowY:scroll;">
	<div id="CurrencyGroup" style="position:absolute;z-index:99;left:0px; top:22px; width:488px; height:10px;">
		<table class="sort-table" id="currencyGroupDetails" width="488px" border="0" cellspacing="1" cellpadding="0" height="433">
		<tbody> 		
			<%int count = 0; %>  
				<s:iterator value="#request.currencyGroupDetails">          
				<% if( count%2 == 0 ) {%><tr  class="even"><% }else  { %> <tr  class="odd"> <%}++count; %>
					<!-- Start:Code added by Nageswara Rao on 04-Jan-2012 for mantis 1580:"Spaces should not be saved to end of input values ." -->
					<td align="left" width="120px">
					<s:hidden name="id.currencyGroupId"/> <s:property value="id.currencyGroupId"/>&nbsp;
					</td>
					<td width="280px" align="left"><s:hidden name="currencyGroupName"/><s:property value="currencyGroupName" escapeHtml="false"/>&nbsp;</td>
			    
			    <!-- END:Code added by Nageswara Rao on 04-Jan-2012 for mantis 1580:"Spaces should not be saved to end of input values ." -->
					<td width="88px" align="left">
					<s:property value ="noOfCurrencies"/>&nbsp;
					</td>			
				</tr>
				</s:iterator>  
		</tbody>
	<tfoot><tr><td colspan="3" ></td></tr></tfoot>
</table>
</div>
</div>
</div>

</div>
<div id="Currency" style="position:absolute; left:456; top:537; width:70px; height:15px; visibility:visible;">
	<table width="60px" border="0" cellspacing="0" cellpadding="0" height="20">
		<tr>
		    <td align="Right">
			    <a tabindex="7" href=# onclick="javascript:openWindow(buildPrintURL('print','Currency Group Maintenance'),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')" onMouseOut="MM_swapImgRestore()" onMouseOver="MM_swapImage('Help','','images/help_default.GIF ',1)"><img src="images/help_default.GIF " name="Help"  border="0" title='<s:text name="tooltip.helpScreen"/>' ></a> 
            </td>

			<td align="right" id="Print">
				<a tabindex="7" onclick="printPage();" onMouseOut="MM_swapImgRestore()" onMouseOver="MM_swapImage('Print','','images/Print_R.gif ',1)"><img src="images/Print.gif " name="Print"  border="0" title='<s:text name="tooltip.printScreen"/>'></a>	
			</td>
		</tr>
	</table>
</div>
<div id="ddimagebuttons" color="#7E97AF" style="position:absolute; border:2px outset; left:20; top:528; width:513px; height:39px; visibility:visible;">
  <div id="Currency" style="position:absolute; left:6; top:4; width:513; height:15px; visibility:visible;">
  	  <table width="350" border="0" cellspacing="0" cellpadding="0" height="20">
	<tr>
		<td id="addbutton" width="70"> </td>
		<td id="changebutton" width="70" ></td>
		<td id="deletebutton" width="70" ></td>
		<td id="currencybutton" width="70"></td>
		
		<td id="closebutton" width="70" title='<s:text name="tooltip.close"/>'>	<a tabindex="6" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onclick="confirmClose('P');"><s:text name="button.close"/></a></td>
	</tr>
	</table>
</div>
<div style="position:absolute; left:6; top:4; width:513; height:15px; visibility:hidden; display:none;">  	
    <table width="350" border="0" cellspacing="0" cellpadding="0" height="20" style="visibility:hidden">
	<tr>
		<td id="addenablebutton" >		
		<a tabindex="2" title='<s:text name="tooltip.AddNewCurrencyGroup"/>' onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onClick="javascript:openWindow(buildAddCurrencyGroup('add'),'currencygroupmaintenancechildWindow','left=50,top=190,width=580,height=205,toolbar=0, resizable=yes, scrollbars=yes','true')"><s:text name="button.add"/></a>
		</td>		
		<td id="adddisablebutton">
			<a class="disabled" disabled="disabled"><s:text name="button.add"/></a>
		</td>
		
		<td id="changeenablebutton" >		
			<a  tabindex="3" title='<s:text name="tooltip.ChangeSelectedCurrencyGroup"/>' onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onClick="javascript:openWindow(buildChangeCurrencyGroup('change'),'currencygroupmaintenancechildWindow','left=50,top=190,width=580,height=205,toolbar=0, resizable=yes, scrollbars=yes','true')"><s:text name="button.change"/></a>
		</td>		
		<td id="changedisablebutton">
			<a class="disabled" disabled="disabled"><s:text name="button.change"/></a>
		</td>

		<td id="deleteenablebutton"  >		
			<a  tabindex="4" title='<s:text name="tooltip.DeleteSelectedCurrencyGroup"/>' onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onClick="javascript:submitDeleteForm('delete')"><s:text name="button.delete"/></a>
		</td>		
		<td id="deletedisablebutton">
			<a  class="disabled" disabled="disabled"><s:text name="button.delete"/></a>
		</td>	
		<td id="currencyenablebutton"  >		
			<a  tabindex="5" title='<s:text name="tooltip.Currencies"/>' onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onClick="javascript:openWindow(currencies('currencies'),'currencymaintenancecurrrenciesWindow','left=50,top=190,width=436,height=550,toolbar=0, resizable=yes, scrollbars=yes','true')" ><s:text name="button.currency"/></a>
		</td>
		<td id="currencydisablebutton">
			<a  class="disabled" disabled="disabled"><s:text name="button.currency"/></a>
		</td>
	</tr>
    </table>
  </div>
</div>
<blockquote>&nbsp;</blockquote>
		<p>&nbsp;</p>
<script type="text/javascript">
</script>
</s:form>
</body>
</html>