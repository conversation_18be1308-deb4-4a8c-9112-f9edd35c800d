<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@ include file="/taglib.jsp"  %>
<%@ page import="org.swallow.util.SwtConstants" %>
<html>
<head>
<title><s:text name="messageField.Title.mainWindow"/></title>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">

<SCRIPT language="JAVASCRIPT">
var formatId = '${requestScope.formatId}'
var scenarioId = '${requestScope.scenarioId}'
var formatType = '${sessionScope.formatTypeInSession}';
var scenarioMessageFieldDetailsInSessionSize = '${sessionScope.scenarioMessageFieldDetailsInSessionSize}';


var messageValue;

mandatoryFieldsArray ="undefined" ; 

function setParentFormFormatType(from)
{
 window.opener.document.forms[0].sessionFormatType.value = formatType;
 window.opener.document.forms[0].scenarioMessageFieldDetailsInSessionSize.value = scenarioMessageFieldDetailsInSessionSize;
 if(from == 'close') {
	 confirmClose('P');
 }
}
var isSeqNoExisting = '${requestScope.isSeqNoExisting}';
var isViewFormat = "${requestScope.isViewFormat}";

var entityAccess = "${requestScope.EntityAccess}";
function submitForm(methodName){
var yourstate=window.confirm('<s:text name="confirm.delete"/>');
	if (yourstate==true){ 
	document.forms[0].method.value = methodName;
	document.forms[0].submit();
	}
}
/**
 * buildAddMessageField
 * 			
 * This method is used to open the subscreen of message filed maintenance screen with all details  
 * @param methodName
 */
function buildAddMessageField(methodName){
    var param = 'messagefields.do?method='+methodName;
   	param += '&selectedSeqNo=' + document.forms[0].selectedSeqNo.value;
	param += '&fldType=' + document.forms[0].selectedFieldType.value;
	param += '&startPos=' + document.forms[0].selectedStartPos.value;
	param += '&endPos=' + document.forms[0].selectedEndPos.value;
   	param += '&formatId=' + formatId;
	param += '&scenarioId=' + scenarioId;
	param += '&selectedLineNo=' +document.forms[0].selectedLineNo.value;
	param += '&isViewFormat=' +isViewFormat;
	param += '&value=' + document.forms[0].selectedValue.value;
	messageValue=document.forms[0].selectedValue.value;
	messageValue=messageValue.trim();
	return param;
}



function disableAllButtons()
{

	<s:if test='"yes" != #request.isViewFormat' >
	    document.getElementById("addbutton").innerHTML =  document.getElementById("addEnableButton").innerHTML; 
		document.getElementById("changebutton").innerHTML = document.getElementById("changedisablebutton").innerHTML;
		document.getElementById("viewbutton").innerHTML = document.getElementById("viewdisablebutton").innerHTML;	
		document.getElementById("deletebutton").innerHTML = document.getElementById("deletedisablebutton").innerHTML; 
	</s:if>

	    
	<s:if test='"yes" == #request.isViewFormat' >
		document.getElementById("viewbutton").innerHTML = document.getElementById("viewdisablebutton").innerHTML;		
	</s:if>
	
}

function onFilterandSort(){
   	updateColors();
	disableAllButtons();
}


function replaceNbsps(str) {
	  var re = new RegExp(String.fromCharCode(160), "g");
	  return str.replace(re, " ");
	}
	
	
	
function onSelectTableRow(rowElement, isSelected)
{
    
	if(rowElement && rowElement.cells.length == 6)
	{
		var cell1 = rowElement.cells[0];
		var cell2 = rowElement.cells[1];
		var cell3 = rowElement.cells[2];	
		var cell4 = rowElement.cells[3];	
		var cell5 = rowElement.cells[4];
		var cell6 = rowElement.cells[5];
		var hiddenEl1 = cell1.getElementsByTagName("input")[0];
		var hiddenEl2 = cell2.getElementsByTagName("input")[0];
		var hiddenEl3 = cell3.getElementsByTagName("input")[0];	
		var hiddenEl4 = cell4.getElementsByTagName("input")[0];		
		var hiddenEl5 = cell5.getElementsByTagName("input")[0];					
	}
		
		document.forms[0].selectedSeqNo.value = replaceNbsps(cell1.innerText);
		document.forms[0].selectedLineNo.value  = replaceNbsps(cell2.innerText);			
		document.forms[0].selectedStartPos.value = replaceNbsps(cell3.innerText);
		document.forms[0].selectedEndPos.value = replaceNbsps(cell4.innerText);
		document.forms[0].selectedFieldType.value = new String(replaceNbsps(cell5.innerText)).trim().valueOf();
		
		document.forms[0].selectedValue.value = getMenuWindow().encode64(cell6.childNodes[0].value);

	var count = getCountRowsSelected(rowElement);
	if(isSelected)
	{
			
		<s:if test='"yes" != #request.isViewFormat' >
		    document.getElementById("addbutton").innerHTML =  document.getElementById("insertEnableButton").innerHTML;
			document.getElementById("changebutton").innerHTML = document.getElementById("changeenablebutton").innerHTML;
			document.getElementById("viewbutton").innerHTML = document.getElementById("viewenablebutton").innerHTML;
			document.getElementById("deletebutton").innerHTML = document.getElementById("deleteenablebutton").innerHTML;
	    </s:if>

		<s:if test='"yes" == #request.isViewFormat' >
		    document.getElementById("viewbutton").innerHTML = document.getElementById("viewenablebutton").innerHTML;
		</s:if>
	}else
	{
		<s:if test='"yes" != #request.isViewFormat' >
		    document.getElementById("addbutton").innerHTML = document.getElementById("addenablebutton").innerHTML;	
			document.getElementById("changebutton").innerHTML = document.getElementById("changedisablebutton").innerHTML;
			document.getElementById("viewbutton").innerHTML = document.getElementById("viewdisablebutton").innerHTML;
			document.getElementById("deletebutton").innerHTML = document.getElementById("deletedisablebutton").innerHTML;
		</s:if>

		<s:if test='"yes" == #request.isViewFormat' >
			document.getElementById("viewbutton").innerHTML = document.getElementById("viewdisablebutton").innerHTML;
		</s:if>
	}
}

function bodyOnLoad() {
		
		if(formatType == "<%=SwtConstants.FORMAT_TYPE_TAGGED%>")
		{
			xl = new XLSheet("msgFieldDetails","table_2", ["Number","Number","Number","Number","String","String"],"211111","false");
			xl.onsort = xl.onfilter = onFilterandSort;			
		}else if(formatType == "<%=SwtConstants.FORMAT_TYPE_DELIMITED%>")
		{
			xl = new XLSheet("msgFieldDetails","table_2", ["Number","Number","Number","Number","String","String"],"122211","false");
			xl.onsort = xl.onfilter = onFilterandSort;		
		}else if(formatType == "<%=SwtConstants.FORMAT_TYPE_FIXED%>")
		{
			xl = new XLSheet("msgFieldDetails","table_2", ["Number","Number","Number","Number","String","String"],"221111","false");
			xl.onsort = xl.onfilter = onFilterandSort;				
		}
		else {
			xl = new XLSheet("msgFieldDetails","table_2", ["Number","Number","Number","Number","String","String"],"111211","false");
			xl.onsort = xl.onfilter = onFilterandSort;			
			
		}

		if(document.getElementById("msgFieldDetails").tBodies[0].rows.length > 0 )
		{			
			if(formatType == "<%=SwtConstants.FORMAT_TYPE_TAGGED%>")
			{
				xl.dataTable.doManualSorting(1);
			}else if(formatType == "<%=SwtConstants.FORMAT_TYPE_DELIMITED%>")
			{
				xl.dataTable.doManualSorting(0);
			}else if(formatType == "<%=SwtConstants.FORMAT_TYPE_FIXED%>")
			{
				xl.dataTable.doManualSorting(2);
			}
		}

	highlightTableRows("msgFieldDetails");
	var cancelcloseElements = new Array(1);
	cancelcloseElements[0] = "closebutton";
	<s:if test='"yes" == #request.isViewFormat' >
		document.getElementById("viewbutton").innerHTML = document.getElementById("viewdisablebutton").innerHTML;
	</s:if>

	<s:if test='"yes" != #request.isViewFormat' >		
			document.getElementById("addbutton").innerHTML = document.getElementById("addenablebutton").innerHTML;	
			document.getElementById("changebutton").innerHTML = document.getElementById("changedisablebutton").innerHTML;
			document.getElementById("viewbutton").innerHTML = document.getElementById("viewdisablebutton").innerHTML;	
			document.getElementById("deletebutton").innerHTML = document.getElementById("deletedisablebutton").innerHTML;		
	</s:if>
}

</SCRIPT> 

</head>

<body onmousemove="reportMove()" leftmargin="0" topmargin="0" marginheight="0" onLoad="setParentChildsFocus();setFocus(document.forms[0]);bodyOnLoad();ShowErrMsgWindow('${actionError}');" onunload="setParentFormFormatType(); call()">

<s:form action="messagefields.do">
<input name="scenarioId" type="hidden" value="">
<input name="method" type="hidden" value="add">
<input name="selectedSeqNo" type="hidden" value="">
<input name="selectedFieldType" type="hidden" value="">
<input name="selectedStartPos" type="hidden" value="">
<input name="selectedEndPos" type="hidden" value="">
<input name="selectedValue" type="hidden" value="">
<input name="isSeqNoExisting" type="hidden" value="">
<input name="selectedLineNo" type="hidden" value="">


<div id="MessageFields" color="#7E97AF" style="position:absolute; border:0px ; left:10px; top:20px; width:945px; height:458px;">
   	<div id="MessageFields" style="position:absolute;z-index:99;left:0px; top:0px; width:945px; height:10px;">
		<table  class="sort-table" id="table_2" bgcolor="#B0AFAF" width="920px" border="0" cellspacing="1" cellpadding="0" >
	
			<thead>
				<tr height="20px">
				    <td width="95px" title='<s:text name="tooltip.sortSeqNo"/>'><b><s:text name="messageFields.sequenceNoDisplay"/></b></td>
					<td width="95px" title='<s:text name="tooltip.sortLineNo"/>'><b><s:text name="messageFormats.lineNoDisplay"/></b></td>			
					<td width="110px" title='<s:text name="tooltip.sortStartPosition"/>'><b><s:text name="messageFields.startPos1"/></b></td>
					<td width="100px" title='<s:text name="tooltip.sortEndPosition"/>'><b><s:text name="messageFields.endPos1"/></b></td>
					<td width="125px" title='<s:text name="tooltip.sortFieldType"/>'><b><s:text name="messageFields.fieldType"/></b></td>
					<td width="395px" title='<s:text name="tooltip.sortValue"/>'><b><s:text name="messageFields.value"/></b></td>
				</tr>
			</thead>
		</table>
	</div>
	<div id="ddscrolltable"  style="position:absolute; left:0px; top:0px; width:944px; height:455px;">
	<div id="MessageFields" style="position:absolute;z-index:99;left:0px; top:20px; width:900px; height:10px;">
	<!-- Code Modified by sandeepkumar for Mantis 2093 : TO remove unwanted scroll bar -->
		<table class="sort-table" id="msgFieldDetails" width="920px" border="0" cellspacing="1" cellpadding="0" height="433px">
			<tbody> 
			<%int count = 0; %>  
			 <s:iterator value="#request.msgFieldDetails" var="msgFieldDetails" >          
					<% if( count%2 == 0 ) {%><tr class="even"><% }else  { %> <tr class="odd"> <%}++count; %>

						<td width="95px" align="right"><s:hidden disabled="true" name="#msgFieldDetails.seqNoAsString"/><s:property value="#msgFieldDetails.seqNoAsString" />&nbsp;</td>
						<td width="95px" align="right"><s:hidden disabled="true" name="#msgFieldDetails.lineNoAsString"/><s:property value="#msgFieldDetails.lineNoAsString" />&nbsp;</td>
						<td width="110px" align="right"><s:hidden disabled="true" name="#msgFieldDetails.startPos"/><s:property value="#msgFieldDetails.startPos" />&nbsp;</td>
						<td width="100px" align="right"><s:hidden disabled="true" name="#msgFieldDetails.endPos"/><s:property value="#msgFieldDetails.endPos" />&nbsp;</td>
						<td width="125px"><s:hidden disabled="true" name="#msgFieldDetails.fieldTypeDisplay"/><s:property value="#msgFieldDetails.fieldTypeDisplay" />&nbsp;</td>
						<td width="395px"><s:hidden disabled="true" name="#msgFieldDetails.value"/><s:property value="#msgFieldDetails.value" />&nbsp;</td>
					</tr>
					</s:iterator>  
				</tbody>
			<tfoot><tr><td colspan="6" ></td></tr></tfoot>
		</table>
	</div>
</div>
</div>
<div id="MessageFields" style="position:absolute; left:880; top:498; width:70px; height:15px; visibility:visible;">
	<table width="60px" border="0" cellspacing="0" cellpadding="0" height="20">
		<tr>
			<td align="Right">
			    <a tabindex="5" tabindex="6" href=# onclick="javascript:openWindow(buildPrintURL('print','Message Fields Maintenance'),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')" onMouseOut="MM_swapImgRestore()" onMouseOver="MM_swapImage('Help','','images/help_default.GIF ',1)"><img src="images/help_default.GIF " name="Help"  border="0" title='<s:text name="tooltip.helpScreen"/>'></a> 
		   </td>

			<td align="right" id="Print">
				<a tabindex="5" onclick="printPage();" onMouseOut="MM_swapImgRestore()" onMouseOver="MM_swapImage('Print','','images/Print_R.gif ',1)"><img src="images/Print.gif " name="Print"  border="0" title='<s:text name="tooltip.printScreen"/>'></a>	
			</td>
		</tr>
	</table>
</div>

<div id="ddimagebuttons" color="#7E97AF" style="position:absolute; border:2px outset; left:10px; top:491px; width:945px; height:39px; visibility:visible;">

  <div id="MessageFields" style="position:absolute; left:6; top:4; width:290px; height:15px; visibility:visible;">
  <s:if test='"yes" != #request.isViewFormat' >
	  <table width="350px" border="0" cellspacing="0" cellpadding="0" height="20">
			<tr>
				<td id="addbutton" title='<s:text name="tooltip.addNewField"/>'>		
				</td>
				<td id="changebutton" title='<s:text name="tooltip.changemsgfield"/>'>		
				</td>
				<td id="viewbutton" title='<s:text name="tooltip.viewselectedmsgfield"/>'>		
				</td>
				<td id="deletebutton" title='<s:text name="tooltip.deleteselectedmsgfield"/>'>		
				</td>
				<td id="closebutton" title='<s:text name="tooltip.close"/>'>	
					<a  tabindex="4" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onclick="setParentFormFormatType('close');"><s:text name="button.close"/></a>			
				</td>
			</tr>
			</table>
  </s:if>

  <s:if test='"yes" == #request.isViewFormat' >
	  <table width="140px" border="0" cellspacing="0" cellpadding="0" height="20">
			<tr>
				<td id="viewbutton" title='<s:text name="tooltip.viewselectedmsgfield"/>'>		
				</td>
				<td id="closebutton" title='<s:text name="tooltip.close"/>'>	
					<a  tabindex="4" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onclick="setParentFormFormatType('close');"><s:text name="button.close"/></a>			
				</td>
			</tr>
			</table>
  </s:if>

	</div>
<div style="position:absolute; left:6; top:4; width:736px; height:15px; visibility:hidden;">  	
    <table width="350" border="0" cellspacing="0" cellpadding="0" height="20" style="visibility:hidden">
	<tr>
		<td id="addenablebutton">		
		<a tabindex="1" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onClick="javascript:openWindow(buildAddMessageField('scenarioFormatAdd'),'','left=50,top=190,width=688,height=220,toolbar=0, resizable=yes, status=yes, scrollbars=yes','true')"><s:text name="button.add"/></a>
		</td>		
		<td id="adddisablebutton">
			<a class="disabled" disabled="disabled"><s:text name="button.add"/></a>
		</td>

		<td id="insertEnableButton">		
		<a tabindex="1" onMouseOut="collapsebutton(this)" title='<s:text name="tooltip.InsertNewField"/>' onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onClick="javascript:openWindow(buildAddMessageField('insertScenario'),'','left=50,top=190,width=688,height=220,toolbar=0, resizable=yes, status=yes, scrollbars=yes','true')">Insert</a>
		</td>


		
		<td id="changeenablebutton">		
			<a tabindex="2" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onClick="javascript:openWindow(buildAddMessageField('scenarioFormatChange'),'','left=50,top=190,width=688,height=220,toolbar=0, resizable=yes, status=yes, scrollbars=yes','true')"><s:text name="button.change"/></a>
		</td>		
		<td id="changedisablebutton">
			<a class="disabled" disabled="disabled"><s:text name="button.change"/></a>
		</td>
		<td id="viewenablebutton">		
			<a tabindex="2" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onClick="javascript:openWindow(buildAddMessageField('scenarioFormatView'),'','left=50,top=190,width=688,height=220,toolbar=0, resizable=yes, status=yes, scrollbars=yes','true')"><s:text name="button.view"/></a>
		</td>		
		<td id="viewdisablebutton">
			<a class="disabled" disabled="disabled"><s:text name="button.view"/></a>
		</td>

		<td id="deleteenablebutton">		
			<a tabindex="3" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onClick="javascript:submitForm('scenarioFormatDelete')"><s:text name="button.delete"/></a>
		</td>		
		<td id="deletedisablebutton">
			<a class="disabled" disabled="disabled"><s:text name="button.delete"/></a>
		</td>						
		
	</tr>
    </table>
  </div>
</div>

<blockquote>&nbsp;</blockquote>
		<p>&nbsp;</p>

<SCRIPT language="JAVASCRIPT">
if(isSeqNoExisting == "yes")
alert('<s:text name="messagefieldadd.alert.duplicateRecord"/>');
</script>
</s:form>
</body>
</html>