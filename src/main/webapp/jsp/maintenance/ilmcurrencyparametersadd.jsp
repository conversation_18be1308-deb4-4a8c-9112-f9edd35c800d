<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@ include file="/taglib.jsp"%>
<%@ page import="org.swallow.util.SwtConstants"%>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=ISO-8859-1">
<title>
	 <s:if test="#request.methodName == 'addCurrency'">
		<s:text name="ilmccyparamsAdd.title.window.addScreen" />
	</s:if> 
	<s:if test="#request.methodName == 'changeCurrency'">
		<s:text name="ilmccyparamsAdd.title.window.changeScreen" />
	</s:if>
	<s:if test="#request.methodName == 'viewCurrency'">
		<s:text name="ilmccyparamsAdd.title.window.viewScreen" />
	</s:if>
</title>
<script language="JAVASCRIPT">
   <s:if test="#request.parentFormRefresh == 'yes'">
		window.opener.document.forms[0].method.value="listCurrencyParams";
		window.opener.document.forms[0].submit();
		self.close();
	</s:if>
	var possibleToMaintain ='${possibleToMaintain}';
	var addGroupAccess ='${addGroupAccess}';
	var methodName ='${methodName}';
	
	/**
	 * Called on first load of the screen
	 */
	function bodyOnLoad()
	{
		if(getStoredParam("GlobalGroupId")){
				var el=document.getElementById("accountGroupsList").firstChild;
				el.options.value = getStoredParam("GlobalGroupId");

		}
		if(getStoredParam("CentralGroupId")) {
				el=document.getElementById("centralBankGroupList").firstChild;
				el.options.value = getStoredParam("CentralGroupId");

		}

		//Enable or disable save button depending in save button request value
		var saveButton = "<%=request.getAttribute(SwtConstants.SAV_BUT_STS)%>";	
		document.getElementById("savebutton").innerHTML = saveButton=='true'?document.getElementById("saveenablebutton").innerHTML:document.getElementById("savedisablebutton").innerHTML;			
		 <s:if test="#request.methodName == 'addCurrency'">
			var entityDropBoxElement = new SwSelectBox(document.forms[0].elements["ilmCcyParams.id.entityId"],document.getElementById("entityName"));
			var currencyGrpDropBoxElement = new SwSelectBox(document.forms[0].elements["ilmCcyParams.id.currencyCode"],document.getElementById("currencyDesc"));
			document.forms[0].elements["ilmCcyParams.primaryAccountId"].value="";
		 </s:if>
		 <s:if test="#request.methodName != 'addCurrency'">
			 document.getElementById("currencyDesc").innerText = '${currencyCodeText}';
			 document.getElementById("entityName").innerText = '${entityText}';
			 document.getElementById("acctName").innerHTML = document.forms[0].elements["ilmCcyParams.primaryAccountName"].value;
			 var option = document.createElement("option");
			 option.setAttribute("value",'<%=request.getAttribute("primaryAccountId")%>');
			 var text = document.createTextNode('<%=request.getAttribute("primaryAccountId")%>');
			 option.appendChild(text)
			 option.selected = 'selected';
			 document.getElementById("primaryAccountIdSelect").appendChild(option);
		 </s:if>
		 
		var globalAcctGrpDropBoxElement = new SwSelectBox(document.forms[0].elements["ilmCcyParams.globalGroupId"],document.getElementById("globalGroupName"));
		var globalAcctGrpDropBoxElement = new SwSelectBox(document.forms[0].elements["ilmCcyParams.altGlobalGroupId"],document.getElementById("altGlobalGroupName"));
		var centranBankAcctGrpDropBoxElement = new SwSelectBox(document.forms[0].elements["ilmCcyParams.centralBankGroupId"],document.getElementById("centralBankGroupName"));
		if(possibleToMaintain=="false" || saveButton!='true' || addGroupAccess == "false"){
			 document.getElementById("globalAccountGroup").style.visibility="hidden";
			 document.getElementById("centralBankAccountGroup").style.visibility="hidden";

		}
		
	}
	
	function submitForm(methodName){
		
		var validMapTime = validateField(document.forms[0].elements["ilmCcyParams.defaultMapTime"],'ilmCcyParams.defaultMapTime','timePat');
		var validStartTime = validateField(document.forms[0].elements["ilmCcyParams.clearingStartTime"],'ilmCcyParams.clearingStartTime','timePat');
		var validEndTime = validateField(document.forms[0].elements["ilmCcyParams.clearingEndTime"],'ilmCcyParams.clearingEndTime','timePat');

		if ((document.forms[0].elements["ilmCcyParams.globalGroupId"].value == ""
				|| document.forms[0].elements["ilmCcyParams.clearingStartTime"].value == ""
				|| document.forms[0].elements["ilmCcyParams.clearingEndTime"].value == "") && methodName != 'addCurrency'){
			alert("<s:text name='alert.pleaseFillAllMandatoryFields'/>");
			return;			
		}
		
		var defaultMapTime = document.forms[0].elements["ilmCcyParams.defaultMapTime"].value;
		var clearingStartTime = document.forms[0].elements["ilmCcyParams.clearingStartTime"].value;
		var clearingEndTime = document.forms[0].elements["ilmCcyParams.clearingEndTime"].value;
		
		if (methodName != 'addCurrency') {
			if (clearingStartTime >= clearingEndTime) {
				alert("<s:text name='ilmccyparamsAdd.alert.clearingTime'/>");
				return;
			} else if (defaultMapTime != "" && !(defaultMapTime >= clearingStartTime && defaultMapTime <= clearingEndTime)) {
				alert("<s:text name='ilmccyparamsAdd.alert.defaultMapTime'/>");
				return;
			}
		}
		
		if(validMapTime && validStartTime && validEndTime){		
			if(chekCurrencyOffset() == "true"){
				document.forms[0].method.value = methodName;
				document.forms[0].selectedEntityId.value = document.forms[0].elements["ilmCcyParams.id.entityId"].value;
				document.forms[0].currencyCode.value = document.forms[0].elements["ilmCcyParams.id.currencyCode"].value;
				document.forms[0].submit();
			}else {
				alert("<s:text name='ilmccyparamsAdd.alert.noCcyOffset'/>");
			}
		}
		
		else return;
	}
	
	function updateCurrency(methodName){
		document.forms[0].method.value = methodName;
		document.forms[0].selectedEntityId.value = document.forms[0].elements["ilmCcyParams.id.entityId"].value;
		document.forms[0].currencyCode.value = document.forms[0].elements["ilmCcyParams.id.currencyCode"].value;
		document.forms[0].submit();
	
		if(chekCurrencyOffset() == "false"){
			alert("<s:text name='ilmccyparamsAdd.alert.noCcyOffset'/>");
		}	
	}
	
	function chekCurrencyOffset() {	
		var appName = "<%=SwtUtil.appName%>";
		var requestURL = new String('<%=request.getRequestURL()%>');
		var idy = requestURL.indexOf('/'+appName+'/');
		requestURL=requestURL.substring(0,idy+1) ; 
		requestURL = requestURL + appName+"/intraDayLiquidity.do?method=getCurrencyGMTOffset";
		requestURL = requestURL + "&entityId=" + document.forms[0].elements["ilmCcyParams.id.entityId"].value;
		requestURL = requestURL + "&currencyCode=" + document.forms[0].elements["ilmCcyParams.id.currencyCode"].value;
		var oXMLHTTP = new XMLHttpRequest();
		oXMLHTTP.open( "POST", requestURL, false );
		oXMLHTTP.send();
		
		return new String(oXMLHTTP.responseText);
	} 
	function accountGroup(groupId,groupDefaultName) {
		setStoredParam("methodNameParent",'${methodName}');
		var entityId = document.forms[0].elements["ilmCcyParams.id.entityId"].value;
		var currencyCode= document.forms[0].elements["ilmCcyParams.id.currencyCode"].value;
		var selectedAccountGroup=(entityId+"_"+currencyCode+"_"+groupId).substring(0,20);
		var url="intraDayLiquidity.do?method=accountGroupDetailsFlex&selectedAccountGroup="+selectedAccountGroup;
		url +="&entityId="+entityId+"&currencyCode="+currencyCode+"&groupDefaultName="+groupDefaultName;
		url +="&methodName=addfromILM";
		if(groupId=="Global"){
			var description  = "<s:text name='ilmAccountGroupDetails.defaultDynamic.descriptionGlobalCurrencyGroup'/>";
			url+="&description="+getMenuWindow().encode64(description);
			url+="&filter="+getMenuWindow().encode64("<%=SwtConstants.IS_ILM_LIQ_CONTRIBUTOR%>");
		}
		else {
			var description  = "<s:text name='ilmAccountGroupDetails.defaultDynamic.descriptionCentralBankGroup'/>";
			url+= "&description="+getMenuWindow().encode64(description);
			url+="&filter="+getMenuWindow().encode64("<%=SwtConstants.IS_ILM_CENTRAL_BANK_MEMBER%>");
		} 
		openWindow(url,"ilmaccountgroupdetailsflex",'left=50,top=190,width=995,height=735,toolbar=0, resizable=yes, scrollbars=yes,status=yes');
	}
	function openAccountListScreen() {
		var entityId     = document.forms[0].elements["ilmCcyParams.id.entityId"].value;
		var currencyCode = document.forms[0].elements["ilmCcyParams.id.currencyCode"].value;
		var ccyName   = document.getElementById("currencyDesc").innerText;
		var entityName      = document.getElementById("entityName").innerText;
		
		var url = "acctMaintenance.do?method=displayListAccountMaintenance&entityId="+entityId
				+"&ccyCode="+currencyCode+"&entityName="+entityName+"&ccyName="+ccyName;
		
		openWindow(url,"29Window",'width=985,height=600,toolbar=0,status=yes,resizable=yes, scrollbars=yes',true,29);
		
	}
	
 </script>
</head>
<body leftmargin="0" topmargin="0" marginheight="0" height="0" style="overflow:auto"
	onLoad="bodyOnLoad();setParentChildsFocus();setFocus(document.forms[0]);ShowErrMsgWindow('${actionError}');"
	onunload="call()">
	<s:form action="intraDayLiquidity.do">
		<input name="method" type="hidden" value="">
		<input name="currencyCode" type="hidden" value="">
	    <input name="selectedEntityId" type="hidden">
	    <input name="selectedEntityName" type="hidden">
	    <s:hidden name="ilmCcyParams.primaryAccountName"/>
		<div id="ilmccyParamsMaintenance"
			style="position: absolute; left: 10px; top: 10px; width: 783px; height: 290px; border: 2px outset;"
			color="#7E97AF">
			<div id="ilmccyParamsMaintenance"
				style="position: absolute; left: 0px; top: 5px; width: 753px; height: 247px;">
				<table width="720px" border="0" cellpadding="0" cellspacing="0"
					height="220px" class="content">
					 <s:if test="#request.methodName == 'addCurrency'">
						<tr height="28">
							<td width="65" style="padding-left: 10px;"><b><s:text name="ilaapccyparams.entity" /></b></td>
							<td width="28">&nbsp;</td>
							<td width="150px"><select
									name="ilmCcyParams.id.entityId"
									onchange="updateCurrency('addCurrency');"
									titleKey="tooltip.selectEntity" style="width:150px"
									tabindex="1">
									<s:iterator value="#request.entities">
								<option value="<s:property value='value'/>"<s:if test="%{ilmCcyParams.id.entityId == value}">selected</s:if>>
								<s:property value='label' /></option>
								</s:iterator></select></td>
							<td width="2">&nbsp;</td>
							<td width="2">&nbsp;</td>	
							<td width="180"><div style="padding-left: 8px"><span id="entityName" name="entityName"
								class="spantext"/></div></td>
						</tr>
					</s:if>
					 <s:if test="#request.methodName != 'addCurrency'">
						<tr height="28">
							<td width="65" style="padding-left: 10px;"><b><s:text name="ilaapccyparams.entity" /></b></td>
							<td width="28">&nbsp;</td>
							<td width="120px"><s:textfield cssClass="htmlTextAlpha"
									name="ilmCcyParams.id.entityId" style="width:120px"
									disabled="true" /></td>
							
							<td width="2">&nbsp;</td>
							<td width="2">&nbsp;</td>
							<td width="180"><div style="padding-left: 9px"><span id="entityName" name="entityName"
								class="spantext"/></div></td>
						</tr>
					</s:if>
					 <s:if test="#request.methodName == 'addCurrency'">
						<tr height="28">
							<td width="60" style="padding-left: 10px;"><b><s:text name="ilaapccyparams.currency" /></b></td>
							<td width="28">&nbsp;</td>
							<td width="120"><select
									name="ilmCcyParams.id.currencyCode"
									titleKey="tooltip.selectCurrencyCode" style="width:55px;"
									onchange="submitForm('addCurrency')" tabindex="2">
									<s:iterator value="#request.currencyList">
								<option value="<s:property value='value'/>"<s:if test="%{ilmCcyParams.id.currencyCode == value}">selected</s:if>>
								<s:property value='label' /></option>
								</s:iterator>
								</select>
							</td>

							<td width="2">&nbsp;</td>
							<td width="2">&nbsp;</td>	
							<td width="180"><div style="padding-left: 8px"><span id="currencyDesc" class="spantext"/>
							</div>
							</td>
						</tr>
					</s:if>
					 <s:if test="#request.methodName != 'addCurrency'">
						<tr height="28">
							<td width="60" style="padding-left: 10px;"><b><s:text name="ilaapccyparams.currency" /></b></td>
							<td width="28">&nbsp;</td>
							<td width="120"><s:textfield cssClass="htmlTextAlpha"
									name="ilmCcyParams.id.currencyCode" style="width:55px"
									disabled="true" /></td>

							<td width="2">&nbsp;</td>
							<td width="2">&nbsp;</td>
							<td width="180"><div style="padding-left: 5px"><span id="currencyDesc" class="spantext"></div></td>
						</tr>
					</s:if>
					<tr height="28">
						<td width="315px" style="padding-left: 10px;"><b
							style="width: 220px"><s:text name="ilaapccyparams.globalCcyAcctGrp" />*</b></td>
						<td width="28">&nbsp;</td>
						<td width="190" id="accountGroupsList"><select
								name="ilmCcyParams.globalGroupId"
								<s:if test='%{"viewCurrency" == #request.methodName}'>disabled</s:if> 
								titleKey="ilmccyparamsAdd.toolip.globalCcyAcctGrpSelect" style="width:190px;"
								tabindex="3">
								<s:iterator value="#request.accountGroupsList">
								<option value="<s:property value='value'/>"<s:if test="%{ilmCcyParams.globalGroupId == value}">selected</s:if>>
								<s:property value='label' /></option>
								</s:iterator>
							</select></td>

						<td width="2">&nbsp;</td>
						<td width="25">
 					 <input title='<s:text name="tooltip.addGlobalAccountGroup"/>' 
 					        id="globalAccountGroup" type="button" value="+" tabindex="15"
 					        style="height: 17px; width: 18px; padding-bottom: 2px; text-align: center; padding-top: 0px; 
 					        			padding-left: 0px; line-height: 100%; padding-right: 0px;"
 					        onClick="javascript:accountGroup('Global','Global');">
 					 
 					 </td>
					 <td width="215" style ="position:relative; left:3px; word-wrap:break-word";>
 					    	<div style="padding-left: 5px;width: 212px;padding-right:10px">
 					    	    <span id="globalGroupName" class="spantext">
 					    	</div>
					 </td>
					</tr>
					<tr height="28">
						<td width="315px" style="padding-left: 10px;"><b
							style="width: 220px"><s:text name="ilaapccyparams.altGlobalGrp" /></b></td>
						<td width="28">&nbsp;</td>
						<td width="190" id="accountGroupsList"><select
								name="ilmCcyParams.altGlobalGroupId"
						        <s:if test='%{"viewCurrency" == #request.methodName}'>disabled</s:if> 
								titleKey="ilmccyparamsAdd.toolip.altGlobalGroup" style="width:190px;"
								tabindex="3">
								<s:iterator value="#request.accountGroupsList">
								<option value="<s:property value='value'/>"<s:if test="%{ilmCcyParams.altGlobalGroupId == value}">selected</s:if>>
								<s:property value='label' /></option>
								</s:iterator>
							</select></td>

						<td width="2">&nbsp;</td>
						<td width="25"> </td>
					 <td width="215" style ="position:relative; left:3px; word-wrap:break-word";>
 					    	<div style="padding-left: 5px;width: 212px;padding-right:10px">
 					    	    <span id="altGlobalGroupName" class="spantext">
 					    	</div>
					 </td>
					</tr>
					<tr height="28">
						<td width="220px" style="padding-left: 10px;"><b><s:text name="ilaapccyparams.defaultMapTime" /></b></td>
						<td width="5px">&nbsp;</td>
						<td width="120px"><s:textfield cssClass="htmlTextNumeric"
								maxlength="5" cssStyle="width:60px" tabindex="4"
								name="ilmCcyParams.defaultMapTime" 
								titleKey="ilmccyparamsAdd.toolip.defaultMapTime"
								disabled="%{#request.methodName == 'viewCurrency' ? 'true': ''}"
								onchange="return validateField(this,'ilmCcyParams.defaultMapTime','timePat');" />
						</td>
					</tr>
					<tr height="28"> 
					   	<td width="220" style="padding-left: 10px;"><b
							style="width: 240px"><s:text name="ilaapccyparams.lvpsName" /></b></td>
					    <td width="28">&nbsp;</td>
    					<td width="120"><s:textfield
								maxlength="20" style="width:210px" tabindex="4"
								disabled="%{#request.methodName == 'viewCurrency' ? 'true': ''}"
								titleKey="ilmccyparamsAdd.toolip.LVPSName"
								name="ilmCcyParams.lvpsName"/></td>
                     </tr>
                     <tr height="28"> 
                         	<td width="240" style="padding-left: 10px;"><b
 							style="width: 240px"><s:text name="ilaapccyparams.centralBankGroupId" /></b></td> 
 					    <td width="28">&nbsp;</td> 
    					<td width="190" id ="centralBankGroupList"><select
								name="ilmCcyParams.centralBankGroupId"
								<s:if test='%{"viewCurrency" == #request.methodName}'>disabled</s:if> 
								titleKey="ilmccyparamsAdd.toolip.centralBankGrpId" style="width:190px;" 
								tabindex="3"> 
								<s:iterator value="#request.accountGroupsList">
								<option value="<s:property value='value'/>"<s:if test="%{ilmCcyParams.centralBankGroupId == value}">selected</s:if>>
								<s:property value='label' /></option>
								</s:iterator>
 							</select></td>
 						
						<td width="2">&nbsp;</td>
 							
 					    <td><input title='<s:text name="tooltip.addCentralAccountGroup"/>' 
 					        id="centralBankAccountGroup" type="button" value="+" tabindex="15"
 					        style="height: 17px; width: 18px; padding-bottom: 2px; text-align: center; padding-top: 0px; 
 					        			padding-left: 0px; line-height: 100%; padding-right: 0px;"
 					        			disabled="%{#request.methodName == 'viewCurrency' ? 'true': ''}"
 					        onClick="javascript:accountGroup('CBANK','Central Bank');"></td>
 					       
 					    <td width="215" style ="position:relative; left:3px; word-wrap:break-word";>
 					    	<div style="padding-left: 5px;width: 212px;padding-right:10px">
 					        	<span id="centralBankGroupName" class="spantext">
 					        </div>
 					    </td>
 					 </tr>
					 <tr height="28">
					   	<td width="240" style="padding-left: 10px;"><b
							style="width: 240px"><s:text name="ilaapccyparams.primaryAccountId" /></b></td> 
					    <td width="28">&nbsp;</td>
     					<td width="185" id="primaryAccountId">
     					<select
     							id="primaryAccountIdSelect"
     							<s:if test='%{"viewCurrency" == #request.methodName}'>disabled</s:if> 
								style="width:160px;" tabindex="4"
								name="ilmCcyParams.primaryAccountId" 
								titleKey="ilmccyparamsAdd.toolip.primaryAccountId"
								onclick="openAccountListScreen();"> 
								<option value =""></option>
							</select>
														
						</td> 
 						<td width="2">&nbsp;</td>
 						<td></td>
 					      <td width="220" style="word-wrap: break-word;">
 					      <div style="padding-left: 5px;width: 212px;padding-right:10px">
 					    	    <span id="acctName" class="spantext">
 					    	</div>
 								</td> 
					 </tr>
					 <tr height="28">
						<td width="220px" style="padding-left: 10px;"><b><s:text name="ilaapccyparams.clearingStartTime" />*</b></td>
						<td width="5px">&nbsp;</td>
						<td width="120px"><s:textfield cssClass="htmlTextNumeric"
								maxlength="5" style="width:60px" tabindex="4"
								name="ilmCcyParams.clearingStartTime"
								titleKey="ilmccyparamsAdd.toolip.clearingStartTime"
								disabled="%{#request.methodName == 'viewCurrency' ? 'true': ''}"
								onchange="return validateField(this,'ilmCcyParams.clearingStartTime','timePat');" />
						</td>
					</tr>
					<tr height="28">
						<td width="220px" style="padding-left: 10px;"><b><s:text name="ilaapccyparams.clearingEndTime" />*</b></td>
						<td width="5px">&nbsp;</td>
						<td width="120px"><s:textfield cssClass="htmlTextNumeric"
								maxlength="5" style="width:60px" tabindex="4"
								name="ilmCcyParams.clearingEndTime" 
								titleKey="ilmccyparamsAdd.toolip.clearingEndTime"
								disabled="%{#request.methodName == 'viewCurrency' ? 'true': ''}"
								onchange="return validateField(this,'ilmCcyParams.clearingEndTime','timePat');" />
						</td>
					</tr>
				</table>
			</div>
		</div>

		<div id="ilmccyParamsMaintenance"
			style="position: absolute; left: 710px; top: 318; width: 70px; height: 29px; visibility: visible;">
			<table width="60px" border="0" cellspacing="0" cellpadding="0"
				height="20">
				<tr>
				 <s:if test="#request.methodName == 'addCurrency'">
					<td align="Right"><a tabindex="7" href=#
						onclick="javascript:openWindow(buildPrintURL('print','Add ILM Currency Parameter Maintenance'),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')"
						onMouseOut="MM_swapImgRestore()"
						onMouseOver="MM_swapImage('Help','','images/help_default.GIF ',1)"><img
							style=" margin-bottom: 4px;" src="images/help_default.GIF " name="Help" border="0"
							title='<s:text name="tooltip.helpScreen"/>'></a></td>
					</s:if>		
					 <s:if test="#request.methodName != 'addCurrency'">
					<td align="Right" ><a tabindex="7" href=#
						onclick="javascript:openWindow(buildPrintURL('print','Change ILM Currency Parameters Maintenance'),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')"
						onMouseOut="MM_swapImgRestore()"
						onMouseOver="MM_swapImage('Help','','images/help_default.GIF ',1)"><img
							style=" margin-bottom: 4px;" src="images/help_default.GIF " name="Help" border="0"
							title='<s:text name="tooltip.helpScreen"/>'></a></td>
					</s:if>
					<td align="right" id="Print">&nbsp; <a tabindex="8"
						onclick="printPage();" onMouseOut="MM_swapImgRestore()"
						onMouseOver="MM_swapImage('Print','','images/Print_R.gif ',1)"><img
							src="images/Print.gif " name="Print" border="0"
							title='<s:text name="tooltip.printScreen"/>'></a>
					</td>
				</tr>
			</table>
		</div>

		<div id="ddimagebuttons" color="#7E97AF"
			style="position: absolute; border: 2px outset; left: 10; top: 310px; width: 783px; height: 39px; visibility: visible;">
			<div id="ScenarioCategory"
				style="position: absolute; left: 2; top: 4; width: 200px; height: 15px; visibility: visible;">
				<table width="140" border="0" cellspacing="0" cellpadding="0"
					height="20">
					<tr>
						<td id="savebutton"></td>
						<td id="cancelbutton" width="70px"><a
							title='<s:text name="tooltip.cancel"/>' tabindex="6"
							onMouseOut="collapsebutton(this)"
							onMouseOver="highlightbutton(this)"
							onMouseDown="expandbutton(this)"
							onMouseUp="highlightbutton(this)" onclick="confirmClose('P');"><s:text name="button.cancel" /></a></td>
					</tr>
				</table>
			</div>
			<div
				style="position: absolute; left: 6; top: 4; width: 554px; height: 15px; visibility: hidden; display: none;">
				<table width="350" border="0" cellspacing="0" cellpadding="0"
					height="20" style="visibility: hidden">
					<tr>
						<td id="saveenablebutton" width="70"><a tabindex="5"
							title='<s:text name="tooltip.save"/>'
							onMouseOut="collapsebutton(this)"
							onMouseOver="highlightbutton(this)"
							onMouseDown="expandbutton(this)"
							onMouseUp="highlightbutton(this)"
							onclick="javascript:submitForm('${methodName eq 'addCurrency' ? 'saveCurrency': 'updateCurrency'}');"><s:text name="button.save" /></a></td>
						<td id="savedisablebutton"><a class="disabled"
							disabled="disabled"><s:text name="button.save" /></a></td>
					</tr>	
				</table>
			</div>
		</div>
			
	</s:form>

</body>
</html>