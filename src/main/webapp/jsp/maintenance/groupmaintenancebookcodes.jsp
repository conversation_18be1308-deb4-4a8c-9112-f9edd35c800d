<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@ include file="/taglib.jsp"  %>
<%@ page import="org.swallow.util.SwtConstants" %>
<html>
<head>
<title><s:text name="groupmaintenance.title.bookMain"/></title>


<SCRIPT language="JAVASCRIPT">

var entityAccess = "${requestScope.EntityAccess}";


function bodyOnLoad()
{
	xl = new XLSheet("bookCode","table_2", ["String","String"],"11");
	xl.onsort = xl.onfilter = updateColors;

	xl.dataTable.tBody.style.cursor="";
}

function onSelectTableRow(rowElement)
{
	document.forms[0].selectedgroupId.value = rowElement.getAttribute("id.bookCode");
}
/* Start: code modified by venkat on 18_mar_2011 for mantis 1385:"Support for symbolic characters in book, group and metagroup names." */
/* This Method is used for save or update the data and close the screen when enter key pressed */
function onKeyEnterSubmit(method,e){
	var event = (window.event|| e);
	 if(event.keyCode == 13){
	 if(method == "close"){
		confirmClose('C');
		}
	}
}
/* End: code modified by venkat on 18_mar_2011 for mantis 1385:"Support for symbolic characters in book, group and metagroup names." */
</SCRIPT>
</head>
<body onmousemove="reportMove()" leftmargin="0" topmargin="0" marginheight="0" onLoad="bodyOnLoad();setParentChildsFocus(); setFocus(document.forms[0]); ShowErrMsgWindow('${actionError}');" onunload="call()">

<s:form action="group.do">
<input name="method" type="hidden" value="save">

<div id="GroupBookCodes" style="position:absolute; left:20px; top:20px; width:426px; height:430px; border:2px outset;" color="#7E97AF">
<div id="GroupBookCodes" style="position:absolute;z-index:99;left:0px; top:0px; width:435px; height:10px;">
<table  class="sort-table" id="table_2" bgcolor="#B0AFAF" width="407" border="0" cellspacing="1" cellpadding="0" height="20px">
	<thead>
		<tr>
			<td width="120px" height="20px" align="middle" title='<s:text name="tooltip.sortBookId"/>'><b>&nbsp;<s:text name="book.bookCode"/></b></td>
			<td width="280px" height="20px" align="middle" title='<s:text name="tooltip.sortBookName"/>'><b><s:text name="book.bookName"/></b></td>
		</tr>
	</thead>
</table>
</div>
<div id="ddscrolltable"  style="position:absolute;left:0px; top:0px; width:421px; height:425px; overflowY:scroll;overflow-x:hidden; ">
<div id="GroupBookCodes" style="position:absolute;left:1px;z-index:99; top:22px; width:394px; height:10px;">
<table class="sort-table" id="bookCode" width="405" border="0" cellspacing="1" cellpadding="0" height="402">
	<tbody> 		
	<%int count = 0; %>  
	<s:iterator value="#request.bookCode" var="bookCode">          
		<% if( count%2 == 0 ) {%><tr  class="even"><% }else  { %> <tr class="odd"> <%}++count; %>
			<td width="120px" align="left">&nbsp;<s:property value="#bookCode.id.bookCode"/>&nbsp;</td>
			<td width="280px"><s:property value="#bookCode.bookName"/>&nbsp;</td>
		</tr>
	</s:iterator>  
	</tbody>
	<tfoot><tr><td colspan="2" ></td></tr></tfoot>
</table>
</div>
</div>
</div>

<div id="Group" style="position:absolute; left:372; top:463; width:70px; height:39px; visibility:visible;">
	<table width="60px" border="0" cellspacing="0" cellpadding="0" height="20">
		<tr height="25">
			<td align="Right">
				  <a tabindex="5" href=# onclick="javascript:openWindow(buildPrintURL('print','Book Maintenance'),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')" onMouseOut="MM_swapImgRestore()" onMouseOver="MM_swapImage('Help','','images/help_default.GIF ',1)"><img src="images/help_default.GIF " name="Help"  border="0" title='<s:text name="tooltip.helpScreen"/>'></a> 
		    </td>

			<td align="right" id="Print">
				<a tabindex="5" onclick="printPage();" onMouseOut="MM_swapImgRestore()" onMouseOver="MM_swapImage('Print','','images/Print_R.gif ',1)"><img src="images/Print.gif " name="Print"  border="0" title='<s:text name="tooltip.printScreen"/>'></a>	
			</td>
		</tr>
	</table>
</div>

<div id="ddimagebuttons" color="#7E97AF" style="position:absolute; border:2px outset; left:20px; top:454; width:426px; height:39px; visibility:visible;">
<div id="GroupBookCodes" style="position:absolute; left:6px; top:4; width:420px; height:15px; visibility:visible;">
  <table width="135" border="0" cellspacing="2" cellpadding="0">
    <tr>
<!-- Code modified by Venkat on 18-mar-2011 for Mantis 1385:"Support for symbolic characters in book, group and metagroup names."  -->
	  <td><a title='<s:text name="tooltip.close"/>' tabindex="1" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onClick="confirmClose('C');" onKeydown = "onKeyEnterSubmit('close',event);" ><s:text name="button.close"/></a></td>
	</tr>
  </table>
</div>
</div>
</div>

</s:form>
</body>
</html>  