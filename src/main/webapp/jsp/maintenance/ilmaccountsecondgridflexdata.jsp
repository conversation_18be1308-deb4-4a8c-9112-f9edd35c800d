<?xml version="1.0" encoding="UTF-8"?>

<%@ page contentType="text/xml"%>
<%@ page import="org.swallow.util.SwtConstants"%>
<%@ taglib prefix="s" uri="/struts-tags" %>




<ilmaccountgroupdetails
	name="ilmaccountgroupdetails" >

<request_reply> 
	<status_ok>
		<s:property value="#request.reply_status_ok" />		
	</status_ok> 
	<message>
		<s:property value="#request.reply_message" />		
	</message> 
	<location /> 
</request_reply> 
<timing> 
	<s:iterator value="#request.opTimes" var="opTime">    
			<operation id="<s:property value="#opTime.key" />"><s:property value="#opTime.value" /></operation>
	</s:iterator> 
</timing> 
<singletons> 
	<exceptioninquery>
		${exceptionInQuery}
	</exceptioninquery>
	<selectedGroup>
		${secondGridAccountGroup}
	</selectedGroup>
</singletons>

<grid>
	<metadata>
		<columns>
			<column heading="<s:text name="ilmAccountGroupDetails.accountIdName" />"
					draggable="false"					
					filterable="true"
					type="str"
					dataelement="account_id_name"
					sort="false"
					width="<s:property value="#request.column_width.accountIdName"/>"/>
			<column heading="<s:text name="ilmAccountGroupDetails.type" />"
					draggable="false"					
					filterable="true"
					type="str"
					dataelement="type"
					sort="false"
					width="<s:property value="#request.column_width.type"/>"/>
			<column heading="<s:text name="ilmAccountGroupDetails.class" />"
					draggable="false"					
					filterable="true"
					type="str"
					dataelement="class"
					sort="true"
					width="<s:property value="#request.column_width.class"/>"/>>
			<column heading="<s:text name="ilmAccountGroupDetails.level" />"
					draggable="false"					
					filterable="true"
					type="str"
					dataelement="level"
					sort="true"
					width="<s:property value="#request.column_width.level"/>"/>			
		</columns>	
	</metadata>

	<rows size="${rowSize}">	
		<s:iterator value="#request.accountInGroup" var="accountInGroup">		
			<row left_hidden="true" right_hidden="false" original="true" filtred="true" shared="false"  >
			
				<account_id_name clickable="false">
					<s:property value="#accountInGroup.id.accountId"/> - <s:property value="#accountInGroup.acctname"/>
				</account_id_name>
				<type clickable="false">
					<s:if test='#accountInGroup.accttype == "C"'>Cash</s:if>
					<s:if test='#accountInGroup.accttype == "U"'>Custodian</s:if>
					
				</type>
				<class clickable="false">
					<s:if test='#accountInGroup.acctClass == "C"'>Current</s:if>
					<s:if test='#accountInGroup.acctClass == "L"'>Loro</s:if>					
					<s:if test='#accountInGroup.acctClass == "E"'>Netting</s:if>
					<s:if test='#accountInGroup.acctClass == "N"'>Nostro</s:if>
					<s:if test='#accountInGroup.acctClass == "O"'>Others</s:if>
				</class>
				<level clickable="false">
					<s:if test='#accountInGroup.acctlevel == "M"'>Main</s:if>
					<s:if test='#accountInGroup.acctlevel != "M"'>Sub</s:if>
					<s:else></s:else>
				</level>
				<entity_id clickable="false">
					<s:property value="#accountInGroup.id.entityId"/>
				</entity_id>
				<account_id	clickable="false">
					<s:property value="#accountInGroup.id.accountId"/>
				</account_id>
			</row>
		</s:iterator>
	</rows>
</grid>

 <selects> 
	<select id="secondgridgroups"> 
	<s:if test='#request.dynamic != "true"'>	
		<s:iterator value="#request.accountGroups" var="group"> 
		<s:if test='#group.value != "#request.selectedAccountGroup"'>		
		 	<option value="<s:property value="#group.value" />" 
				selected="<s:if test="#request.secondGridAccountGroup==#group.value">1</s:if><s:else>0</s:else>"
			><s:property value="#group.label" /></option>
</s:if> 
		 </s:iterator> 
	</s:if>
	</select>
	
 </selects>
 </ilmaccountgroupdetails>