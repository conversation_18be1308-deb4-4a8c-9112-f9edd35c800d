<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@ include file="/taglib.jsp"%>
<html>
<head>
<s:if test="'add'!= #request.method">
	<title><s:text name="title.rates.change" /></title>
</s:if>
<s:else>
	<title><s:text name="title.rates.add" /></title>
</s:else>

<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">


<SCRIPT language="JAVASCRIPT">
// get the duplicate entry status from the request
var dupStatus = "${requestScope.dupstatus}";
var cancelcloseElements = new Array(1);
cancelcloseElements[0] = "cancelbutton";
mandatoryFieldsArray=["*"];
var dateFormatValue = '<s:property value="#request.session.CDM.dateFormatValue" />'; /*  Calendar control added*/
var cal = new CalendarPopup("caldiv", true, "calFrame");
// var dateFormat used to validate the date in the form  
var dateFormat = '<s:property value="#request.session.CDM.dateFormat" />'; 
cal.setCssPrefix("CAL"); /*Calendar component X & Y location changed*/
cal.offsetX = -70;
cal.offsetY = -50; 
<s:if test="'yes'== #request.parentFormRefresh">
window.opener.document.forms[0].method.value = "displayAccInterestRate";
window.opener.document.forms[0].submit();
self.close(); 
</s:if>


function reset(){
<s:if test="'true'== #request.dupstatus">
ShowErrMsgWindowWithBtn('', '<s:text name="alert.templateOption.recordExist"/>', null, okCall);
	
</s:if>
}
function okCall() {
	document.forms[0].elements['acctInterestRate.interestDateRateAsString'].value="";
	document.forms[0].elements['acctInterestRate.creditRate'].value="";
	document.forms[0].elements['acctInterestRate.overdraftRate'].value="";
}
var creditRate, overdraftRate;

function submitForm(methodName) {
    // check the duplicate entry status and set the creditRate & overdraftRate value as empty.
    if (dupStatus == "true") {
        creditRate = "";
        overdraftRate = "";
    }
    // Start :Nithiyananthan 23/12/2011 Mantis 1559: Account Interest Rate :
	// Displays "infinite" for infinite numbers
    //Validates the creditValue
    var creditValue = validateFieldAccRate(document.forms[0].elements['acctInterestRate.creditRate'], 'acctInterestRate.creditRate', 'negativeWithDigits');
    if (creditValue) {
        var overdraftValue = validateFieldAccDraft(document.forms[0].elements['acctInterestRate.overdraftRate'], 'acctInterestRate.overdraftRate', 'negativeWithDigits');
        //Validates the overdraftValue
        if (overdraftValue) {
            if (creditRate != document.forms[0].elements['acctInterestRate.creditRate'].value || overdraftRate != document.forms[0].elements['acctInterestRate.overdraftRate'].value || creditRate == "" || overdraftRate == "") {
            	// Adds the zero before the decimal point
            	document.forms[0].elements['acctInterestRate.creditRate'].value = formatPrecisionDecimal(document.forms[0].elements['acctInterestRate.creditRate'].value);
            	document.forms[0].elements['acctInterestRate.overdraftRate'].value = formatPrecisionDecimal(document.forms[0].elements['acctInterestRate.overdraftRate'].value);
    // End :Nithiyananthan 23/12/2011 Mantis 1559: Account Interest Rate :
	// Displays "infinite" for infinite numbers        	            	
                if (methodName == "updateAcctInterestRate") {
                    if (validateForm(document.forms[0])) {
                        document.forms[0].method.value = methodName;
                        document.forms[0].elements["acctInterestRate.interestDateRateAsString"].disabled = "";
                        document.forms[0].submit();
                    }
                } else {
                    if (validateForm(document.forms[0])) {
                        document.forms[0].method.value = methodName;
                        document.forms[0].submit();
                    }
                }
            } else {
                self.close();
            }
        } else {
            document.forms[0].elements['acctInterestRate.overdraftRate'].focus();
        }
    } else {
        document.forms[0].elements['acctInterestRate.creditRate'].focus();
    }
}

function validateFieldAccRate(strField, strLabel, strPat, maxValue, minValue) {
	var creditRate = parseInt(strField.value);
    if(!isNaN(creditRate) && creditRate > 0) {
    	ShowErrMsgWindowWithBtn('','<s:text name="alert.currencyExchangeRate.invalidCreditRate"/>', null);
//     	 strField.focus();
//     	 strField.select();
// 	      return false;
  }else
	// If we here, then validate with common validation method
	return validateField(strField, strLabel, strPat, maxValue, minValue);
}
function validateFieldAccDraft(strField, strLabel, strPat, maxValue, minValue) {
	 var overDraft = parseInt(strField.value);
 if (!isNaN(overDraft) && overDraft <0) {
	 ShowErrMsgWindowWithBtn('', '<s:text name="alert.currencyExchangeRate.invalidDraftRate"/>', null);
// 	  strField.focus();
// 	  strField.select();
//       return false;
 }else
	// If we here, then validate with common validation method
	return validateField(strField, strLabel, strPat, maxValue, minValue);
}
// Start :Nithiyananthan 23/12/2011 Mantis 1559: Account Interest Rate :
// Displays "infinite" for infinite numbers
// Adds the zero before the decimal point
function formatPrecisionDecimal(decimalValue){
	if (decimalValue.indexOf(".") == 0)
    	decimalValue = "0" + decimalValue;
	/* Start: Code Added for Mantis 2130 by M.BOURAOUI on 27/11/2012 */
	if (decimalValue.indexOf("-.") == 0) {
		 decimalValue=decimalValue.replace("-", "-0");
		}
	/* End: Code Added for Mantis 2130 by M.BOURAOUI on 27/11/2012 */
    return decimalValue;
}
// End :Nithiyananthan 23/12/2011 Mantis 1559: Account Interest Rate :
// Displays "infinite" for infinite numbers
function validateForm(objForm) {
    var elementsRef = new Array(2);
    elementsRef[0] = objForm.elements["acctInterestRate.creditRate"];
    elementsRef[1] = objForm.elements["acctInterestRate.overdraftRate"];
    elementsRef[2] = objForm.elements["acctInterestRate.interestDateRateAsString"];
    return validate(elementsRef);
}

function bodyOnLoad() {
    creditRate = document.forms[0].elements['acctInterestRate.creditRate'].value;
    overdraftRate = document.forms[0].elements['acctInterestRate.overdraftRate'].value;
    document.forms[0].callerMethod.value = window.opener.screenStatus;
    document.forms[0].relatedToaccountId.value = window.opener.document.forms[0].relatedToaccountId.value;
}
</SCRIPT>
</head>
<body leftmargin="0" topmargin="0" marginheight="0"
	onLoad="bodyOnLoad();setParentChildsFocus();setFocus(document.forms[0]);reset();"
	onunload="call()">
<s:form action="acctMaintenance.do">
	<input name="method" type="hidden" value="">
	<input name="callerMethod" type="hidden" value="">
	<input name="relatedToaccountId" type="hidden" value="">
	
	<s:if test="'add'!= #request.method">
		<!-- Start : Nithiyananthan 14/12/2011 Mantis 1559: Account Interest Rate : Displays "infinite" for infinite numbers -->
		<div id="InterestRateAddAcc" color="#7E97AF"
			style="position: absolute; left: 20px; border: 2px outset; top: 20px; width: 360px; height: 110px;">
		<div
			style="position: absolute; left: 8px; top: 4px; width: 280px; height: 97px;">
		<fieldset
			style="width: 336px; border: 2px groove; height: 95px!important; left: 8px; top: 8px;">
		<legend><s:text name="contact.fieldet" /></legend>
		 <div id="caldiv"
			style="z-index: 99; position: absolute; visibility: visible; background-color: white; layer-background-color: white;"></div>
		<iframe id="calFrame" src="javascript:false;" scrolling="no"
			frameborder="0"
			style="position: absolute; top: 0px; left: 0px; display: none;">
		</iframe>
		<table width="320" border="0" cellpadding="0" cellspacing="0"
			height="75">
			<tr height="25px">
				<td width="45px">&nbsp;<b><s:text name="accIntRate.Date" /></b></td>
				<td width="28px">&nbsp;</td>
				<td width="80px"><s:textfield
					name="acctInterestRate.interestDateRateAsString" maxlength="10"
					titleKey="tooltip.accIntRateDate" cssClass="htmlTextNumeric"
					tabindex="1" style="width:80px;" disabled="true" />	
					</td>
			</tr>
			<tr height="25px">
				<td width="115px">&nbsp;<b><s:text name="accIntRate.Credit" /></b>*</td>
				<td width="48px">&nbsp;</td>
				<td width="170px"><s:textfield
					name="acctInterestRate.creditRate"
					titleKey="tooltip.enteraccIntRateCredit"
					cssClass="htmlTextNumeric" tabindex="2" style="width:170px;"
					maxlength="21"
					onchange="return validateFieldAccRate(this,'message.StartPosition','negativeWithDigits');" />
				<!-- End : Nithiyananthan 14/12/2011 Mantis 1559: Account Interest Rate : Displays "infinite" for infinite numbers -->
				</td>
			</tr>
			<tr height="25px">

				<td width="115px">&nbsp;<b><s:text name="accIntRate.OverDraft" /></b>*</td>
				<td width="48px">&nbsp;</td>
				<!-- Start : Nithiyananthan 14/12/2011 Mantis 1559: Account Interest Rate : Displays "infinite" for infinite numbers -->
				<td width="170px"><s:textfield
					name="acctInterestRate.overdraftRate"
					titleKey="tooltip.enteraccIntRateOverDraft"
					cssClass="htmlTextNumeric" tabindex="3" style="width:170px;"
					maxlength="21"
					onchange="return validateFieldAccDraft(this,'message.end','negativeWithDigits');" />
				<!-- End : Nithiyananthan 14/12/2011 Mantis 1559: Account Interest Rate : Displays "infinite" for infinite numbers -->
				</td>
			</tr>
		</table>
		</fieldset>
		</div>
		<div id="EntityMaintenance"
			style="position: absolute; left: 290; top: 115; width: 70px; height: 39px; visibility: visible;">
		<table width="60px" border="0" cellspacing="0" cellpadding="0"
			height="20">
			<tr>
				<td align="Right"><a tabindex="6" href=#
					onclick="javascript:openWindow(buildPrintURL('print','Account InterestRateChange Maintenance'),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')"
					onMouseOut="MM_swapImgRestore()"
					onMouseOver="MM_swapImage('Help','','images/help_default.GIF ',1)"><img
					src="images/help_default.GIF " name="Help" border="0"
					title='<s:text name="tooltip.helpScreen"/>'></a></td>

				<td align="right" id="Print"><a tabindex="6"
					onclick="printPage();" onMouseOut="MM_swapImgRestore()"
					onMouseOver="MM_swapImage('Print','','images/Print_R.gif ',1)"><img
					src="images/Print.gif " name="Print" border="0"
					title='<s:text name="tooltip.printScreen"/>' /></a></td>
			</tr>
		</table>
		</div>
		<div id="ddimagebuttons" color="#7E97AF"
			style="position: absolute; border: 2px outset; left: -2px; top: 110px; width: 360px; height: 39px; visibility: visible;">
		<s:if test="'readonly'== #request.screenStatus">
			<div id="AccountMaintenanceContact"
				style="position: absolute; left: 6; top: 10; width: 360px; height: 15px; visibility: visible;">
			<table width="140" border="0" cellspacing="0" cellpadding="0"
				height="20">
				<tr>
					<td id="closebutton" width="70"
						title='<s:text name="tooltip.close"/>'><a
						onMouseOut="collapsebutton(this)"
						onMouseOver="highlightbutton(this)"
						onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)"
						onclick="window.close();"><s:text name="button.close" /></a></td>
				</tr>
			</table>
			</div>
		</s:if>
	</s:if>
	<s:else>
		<div id="InterestRateAddAcc" color="#7E97AF"
			style="z-index: 99; position: absolute; left: 20px; border: 2px outset; top: 20px; width: 360px; height: 110px;">
		<div
			style="z-index: 99; position: absolute; left: 8px; top: 4px; width: 280px; height: 75px;">

		<div id="caldiv"
			style="z-index: 99; position: absolute; visibility: visible; background-color: white; layer-background-color: white;"></div>
		<iframe id="calFrame" src="javascript:false;" scrolling="no"
			frameborder="0"
			style="position: absolute; top: 0px; left: 0px; display: none;">
		</iframe>
		<div style="left:8px; top:4px;">
		<fieldset
			style="width: 336px; border: 2px groove; height: 95px!important; left: 8 px; top: 8px;">
		<legend><s:text name="contact.fieldet" /></legend>
		<table width="320" border="0" cellpadding="0" cellspacing="0"
			height="50">
			<tr height="25px">
				<td width="45px">&nbsp;<b><s:text name="accIntRate.Date" /></b>*</td>
				<td width="28px">&nbsp;</td>
				<td width="160px"><s:textfield cssClass="htmlTextAlpha"
					maxlength="10" name="acctInterestRate.interestDateRateAsString"
					style="width:80px;margin-bottom: 5px;height:20px;" titleKey="tooltip.accIntRateDate" tabindex="1"
					readonly="false" onchange="return validateField(this,'acctInterestRate.interestDateRateAsString',dateFormat);" /> &nbsp; <A
					title='<s:text name="tooltip.selectInterestRateDate"/>'
					name="datelink" ID="datelink" tabindex="1"
					onClick="cal.select(document.forms[0].elements['acctInterestRate.interestDateRateAsString'],'datelink',dateFormatValue);dateSelected=true;return false;">
				<img title='<s:text name="tooltip.selectInterestRateDate"/>'
					src="images/calendar-16.gif"></A></td>
			</tr>
			<tr height="25px">
				<td width="115px">&nbsp;<b><s:text name="accIntRate.Credit" /></b>*</td>
				<td width="48px">&nbsp;</td>
				<!-- Start : Nithiyananthan 14/12/2011 Mantis 1559: Account Interest Rate : Displays "infinite" for infinite numbers -->
				<td width="170px"><s:textfield
					name="acctInterestRate.creditRate"
					titleKey="tooltip.enteraccIntRateCredit"
					cssClass="htmlTextNumeric" tabindex="2" style="width:170px;"
					maxlength="21"
					onchange="return validateFieldAccRate(this,'message.StartPosition','negativeWithDigits');" />
				<!-- End : Nithiyananthan 14/12/2011 Mantis 1559: Account Interest Rate : Displays "infinite" for infinite numbers -->
				</td>
			</tr>
			<tr height="25px">
				<td width="115px">&nbsp;<b><s:text name="accIntRate.OverDraft" /></b>*</td>
				<td width="48px">&nbsp;</td>
				<!-- Start : Nithiyananthan 12/12/2011 Mantis 1559: Account Interest Rate : Displays "infinite" for infinite numbers -->
				<td width="170px"><s:textfield
					name="acctInterestRate.overdraftRate"
					titleKey="tooltip.enteraccIntRateOverDraft"
					cssClass="htmlTextNumeric" tabindex="3" style="width:170px;"
					maxlength="21"
					onchange="return validateFieldAccDraft(this,'message.end','negativeWithDigits');" />
				<!-- End : Nithiyananthan 12/12/2011 Mantis 1559: Account Interest Rate : Displays "infinite" for infinite numbers -->
				</td>
			</tr>
		</table>
		</fieldset>
		</div>
		</div>

		<div id="EntityMaintenance"
			style="position: absolute; left: 290; top: 115; width: 70px; height: 39px; visibility: visible;">
		<table width="60px" border="0" cellspacing="0" cellpadding="0"
			height="20">
			<tr>
				<td align="Right"><a tabindex="6" href=#
					onclick="javascript:openWindow(buildPrintURL('print','Account InterestRateAdd Maintenance'),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')"
					onMouseOut="MM_swapImgRestore()"
					onMouseOver="MM_swapImage('Help','','images/help_default.GIF ',1)"><img
					src="images/help_default.GIF " name="Help" border="0"
					title='<s:text name="tooltip.helpScreen"/>'></a></td>
				<td align="right" id="Print"><a tabindex="6"
					onclick="printPage();" onMouseOut="MM_swapImgRestore()"
					onMouseOver="MM_swapImage('Print','','images/Print_R.gif ',1)"><img
					src="images/Print.gif " name="Print" border="0"
					title='<s:text name="tooltip.printScreen"/>'></a></td>
			</tr>
		</table>
		</div>
		<div id="ddimagebuttons" color="#7E97AF"
			style="position: absolute; border: 2px outset; left: -2px; top: 110px; width: 360px; height: 39px; visibility: visible;">
		<s:if test="'readonly'== #request.screenStatus">
			<div id="AccountMaintenanceContact"
				style="position: absolute; left: 6; top: 4; width: 360px; height: 15px; visibility: visible;">
			<table width="140" border="0" cellspacing="0" cellpadding="0"
				height="20">
				<tr>
					<td id="closebutton" width="70"
						title='<s:text name="tooltip.close"/>'><a
						onMouseOut="collapsebutton(this)"
						onMouseOver="highlightbutton(this)"
						onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)"
						onclick="window.close();"><s:text name="button.close" /></a></td>
				</tr>
			</table>
			</div>
		</s:if>
	</s:else>
	<s:if test="'readonly'!= #request.screenStatus">
		<div id="AccountMaintenanceContact"
			style="position: absolute; left: 6; top: 4; width: 360px; height: 15px; visibility: visible;">
		<table width="140" border="0" cellspacing="0" cellpadding="0"
			height="20">
			<s:if test="'change'== #request.method">
				<tr>
					<td id="okbutton" width="70"><a tabindex="4"
						title='<s:text name="tooltip.ok"/>'
						onMouseOut="collapsebutton(this)"
						onMouseOver="highlightbutton(this)"
						onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)"
						onclick="javascript:submitForm('updateAcctInterestRate');"><s:text name="button.ok" /></a></td>
					<td id="cancelbutton" width="70"><a tabindex="5"
						title='<s:text name="tooltip.cancel"/>'
						onMouseOut="collapsebutton(this)"
						onMouseOver="highlightbutton(this)"
						onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)"
						onclick="window.close();"><s:text name="button.cancel" /></a>
					</td>
				</tr>
			</s:if>
			<s:else>
				<tr>
					<td id="okbutton" width="70"><a tabindex="4"
						title='<s:text name="tooltip.ok"/>'
						onMouseOut="collapsebutton(this)"
						onMouseOver="highlightbutton(this)"
						onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)"
						onclick="javascript:submitForm('saveAcctInterestRate');"><s:text name="button.ok" /></a></td>
					<td id="cancelbutton" width="70"><a tabindex="5"
						title='<s:text name="tooltip.cancel"/>'
						onMouseOut="collapsebutton(this)"
						onMouseOver="highlightbutton(this)"
						onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)"
						onclick="window.close();"><s:text name="button.cancel" /></a>
					</td>
				</tr>
			</s:else>
		</table>
		</div>
	</s:if>
</s:form>
</body>
</html>