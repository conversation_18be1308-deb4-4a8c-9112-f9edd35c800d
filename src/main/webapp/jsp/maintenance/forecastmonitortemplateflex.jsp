<!doctype html>
<%@ page import="org.swallow.util.SwtUtil"%>
<%@ include file="/taglib.jsp"%>
<%@ page import="org.swallow.util.SwtConstants"%>
<%@ page import="org.swallow.util.CommonDataManager"%>
<%@ include file="/angularJSUtils.jsp"%>
<html>
<head>
<meta name="viewport" content="width=device-width, initial-scale=1">
<link rel="icon" type="image/x-icon" href="favicon.ico">
<link rel="stylesheet" href="angularSources/styles.css?version=<%= SwtUtil.appVersion %>">
</head>
<title><s:text name="label.forecastMonitorTemplate.title.window" /></title>

<style>
body {
	margin: 0px;
	overflow: hidden
}
</style>
<script type="text/javascript" src="jsp/work/assets/swfobject.js"></script>
<script type="text/javascript">
			var screenTitle = "";
			screenTitle = "Forecast";
			var appName = "<%=SwtUtil.appName%>";
			var requestURL = new String('<%=request.getRequestURL()%>');
			var idy = requestURL.indexOf('/'+appName+'/');
			var mainWindow= null;
			var screenRoute = "forecastMonitorTemplate";
			requestURL=requestURL.substring(0,idy+1) ;

			var menuAccessId = '${requestScope.menuAccessId}';
		
			// This method is called when onload
			window.onload = function () {
			    setTitleSuffix(document.forms[0]);
				setParentChildsFocus();									
			};

			window.onunload = call;
			var menuAccessId = '${requestScope.menuAccessId}';
			// Set the label values
			/*var label = new Array ();
			label["text"] = new Array ();
			label["tip"] = new Array ();
			label["alert"] = new Array ();
			
			// Copy From button
			label["text"]["button-copy"] = "<s:text name="button.cpyFrom"/>";
			label["tip"]["button-copy"] = "<s:text name="button.cpyFrom"/>";
			
			// Add button
			label["text"]["button-add"] = "<s:text name="button.add"/>";
			label["tip"]["button-add"] = "<s:text name="button.add"/>";	
			
			// Change button
			label["text"]["button-change"] = "<s:text name="button.change"/>";
			label["tip"]["button-change"] = "<s:text name="button.change"/>";	
			
			// Delete button
			label["text"]["button-delete"] = "<s:text name="button.delete"/>";
			label["tip"]["button-delete"] = "<s:text name="button.delete"/>";	
			
			
			// Close button
			label["text"]["button-close"] = "<s:text name="button.close"/>";
			label["tip"]["button-close"] = "<s:text name="tooltip.close"/>";
			
			// Alert messages
			
			label["alert"]["template-locked"] = "<s:text name="alert.forecasttemplate.templatelocked"/>";
			label["alert"]["template-delete"] = "<s:text name="alert.forecasttemplate.templatedelete"/>";*/
				
			
			/**
			 * openChildWindow
			 * @param methodName
			 * Method to load child screens
			 */
			function openChildWindow(methodName){
					
				var param = '/' + appName + '/forecastMonitorTemplate.do?method='+methodName;
				mainWindow = window.open (param, 'forecastMonitorTemplate','left=10,top=230,width=670,height=500,toolbar=0, resizable=yes, //status=yes, scrollbars=yes','true');	
				return false;
			}



			window.addEventListener("message", receiveMessage, false);


			function receiveMessage(e) {
				console.log(e);
				if(e && e.data){
					var methodName = e.data.data;
					if(methodName === "unLockTemplateOnUnload"){
							// Check if the string is not null and not empty
							if (e.data.templateId && e.data.templateId.trim() !== "") {
								// Remove trailing comma and then split the string into an array
								// Loop through the array and call unlockMovementOnServer for each value
								unLockTemplateOnUnload(e.data.templateId, e.data.templateName, e.data.userId,e.data.isPublic);
							} else {
								// Handle the case where the string is null or empty
								console.log("String is null or empty. No action taken.");
							}



					}
				}

			}



			function unLockTemplateOnUnload(templateId, templateName, userId,isPublic){
				try {
					console.log("wwazee",templateId, templateName, userId,isPublic);
					var param = "/forecastMonitorTemplate.do?method=unLockTemplate";
					param = param+"&templateId="+templateId;
					param = param+"&templateName="+templateName;
					param = param+"&userId="+userId;
					param = param+"&isPublic="+isPublic;

					var oXMLHTTP = new XMLHttpRequest();
					var sURL = requestURL + appName+param;

					oXMLHTTP.open( "POST", sURL, false );
					oXMLHTTP.send();
					var str=oXMLHTTP.responseText;
					return str;
				}catch ( e){
					console.log(e);
				}


			}

			/**
             * help
             * This function opens the help screen 
 			 * @return none
			 */
			function help(){
				openWindow(buildPrintURL('print','Forecast Monitor Template Maintenance'),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')
            }
			
			/**
			 * lockTemplate
			 * @param templateId
			 * @param templateName
			 * @param userId
			 * @param isPublic
			 * Method to load child screens
			 */
			function lockTemplate(templateId,templateName,userId,isPublic){
    			var param = "/forecastMonitorTemplate.do?method=lockTemplate";
				param = param+"&templateId="+templateId;
				param = param+"&templateName="+templateName;
				param = param+"&userId="+userId;
				param = param+"&isPublic="+isPublic;
				
				var oXMLHTTP = new XMLHttpRequest();
				var sURL = requestURL + appName+param;
				
				oXMLHTTP.open( "POST", sURL, false );
				oXMLHTTP.send();
				var str=oXMLHTTP.responseText;
				return str;
				
			}
			
			 /**
			  * callBack
			  * This function used refresh data grid
			  */
			function callBack(){
				 Main.refreshdetails();
			}
			/**
			 * clearSessionInstance
			 * Method to clear session
			 */
			function clearSessionInstance(){
			
    			var param = "/forecastMonitorTemplate.do?method=clearSessionInstance";
				
				var oXMLHTTP = new XMLHttpRequest();
				var sURL = requestURL + appName+param;
				
				oXMLHTTP.open( "POST", sURL, false );
				oXMLHTTP.send();
				var str=oXMLHTTP.responseText;
				return str;
				
			}
			
			  /**
			  * closeChild
			  * This function used to close the child window
			  */
			function closeChild(){
				clearSessionInstance();
				// Added By KaisBS : issue 1054_STL_038 : avoid the javascript error if we aren't open the main screen
				if(typeof(mainWindow) == window)  
				mainWindow.close();
				
			}
			
			
			
		</script>
<%@ include file="/angularscripts.jsp"%>
<body scroll="no" style="overflow:hidden" leftmargin="0" topmargin="0" marginheight="0"
	onunload="closeChild()">
<form id="exportDataForm" target="tmp" method="post">
<input type="hidden" name="data" id="exportData" /> <input
	type="hidden" name="screen" id="exportDataScreen"
	value="<s:text name="label.forecastMonitorTemplate.title.window"/>" /></form>
<iframe name="tmp" width="0%" height="0%" src="#" />
</body>
</html>
