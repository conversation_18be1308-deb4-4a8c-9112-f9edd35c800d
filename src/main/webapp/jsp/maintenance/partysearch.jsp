<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@ include file="/taglib.jsp"  %>
<%@ page import="org.swallow.util.SwtConstants" %>
<html>
<head>
<title><s:text name = "partySearch.title.mainWindow"/> </title>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">

<SCRIPT language="JAVASCRIPT">
<!-- Start:Code modified by <PERSON><PERSON><PERSON><PERSON> for Mantis 1961 on 04-10-2012:Re frame party_list procedure -->
/*
* This function will execute when search button is clicked
*/
function submitFrom()
{
//trimming the form value before submitting
elementTrim(document.forms[0]);
document.forms[0].submit();
}
<!-- End:Code modified by <PERSON><PERSON><PERSON><PERSON> for Mantis 1961 on 04-10-2012:Re frame party_list procedure -->
function attachDoubleClickHandler()
{
	var table = document.getElementById("PartyDetails");
    var tbody = table.getElementsByTagName("tbody")[0];
    var rows = tbody.getElementsByTagName("tr");
    // add event handlers so rows light up and are clickable
    for (i=0; i < rows.length; i++) 
	{
   		rows[i].ondblclick = ondblclickrow
    }
}

function onSelectTableRow(rowElement,isRowSel)
{
	if(isRowSel == true)
	{
		var partyId = rowElement.cells[0].innerText;
		var partyName = rowElement.cells[1].innerText;

		document.forms[0].selectedPartyId.value = partyId;
		document.forms[0].selectedPartyName.value = partyName;
		
	}
	else
	{
		document.forms[0].selectedPartyId.innerText = "";
		document.forms[0].selectedPartyName.value = "";
	}
}

function ondblclickrow(e)
{
	var event = (window.event|| e);
	var target = (event.srcElement || event.target);
	var rowElement = target.parentElement;
	var partyId = rowElement.cells[0].innerText;
	var partyName = rowElement.cells[1].innerText;

	document.forms[0].selectedPartyId.value = partyId;
	document.forms[0].selectedPartyName.value = partyName;
	alert('<s:text name = "doc.formSelect"/>' );

	self.close();
}
function bodyOnLoad()
{
	xl = new XLSheet("PartyDetails","table_2", ["String","String", "String", "String", "String"],"11");
	xl.onsort = xl.onfilter = updateColors;

	highlightTableRows("PartyDetails");
	window.onbeforeunload = closewindow;

	var partyId = document.forms[0].elements["party.id.partyId"].value;
	var partyName = document.forms[0].elements["party.partyName"].value;
	
	var searchBut = document.getElementById("searchButton");
	var searchButDisabled = document.getElementById("searchButtondisabled");
	if(partyId != "" || partyName != "")
	{
		searchBut.style.display = "inline";
		searchButDisabled.style.display = "none";
	}
	else{
		searchBut.style.display = "none";
		searchButDisabled.style.display = "inline";
	}

}

function closewindow()
{
	var partyId = document.forms[0].selectedPartyId.value;
	var partyName = document.forms[0].selectedPartyName.value
	if(partyId != "" )
	{
		var idElementName = document.forms[0].idElementName.value;
		var descElementName = document.forms[0].descElementName.value;

		var idElement =  window.opener.document.getElementById(idElementName);
		if(typeof idElement == 'undefined' || idElement == null)
			idElement = window.opener.document.forms[0].elements[idElementName];

		var descElement =  window.opener.document.getElementById(descElementName);
		if(typeof descElement == 'undefined' || descElement == null)
			descElement = window.opener.document.forms[0].elements[descElementName];

		idElement.value = new String(partyId).valueOf().trim();
		descElement.innerText = new String(partyName).valueOf().trim();
	}
	call2();
}


function submitFormSearch(methodName)
{
	var v1 =  validateField(document.forms[0].elements['party.id.partyId'],'party.id.partyId','alphaNumPat'); 
	var partyId = document.forms[0].elements["party.id.partyId"].value;
	var partyName = document.forms[0].elements['party.partyName'].value;

	partyNameTrimmed = new String(partyName).trim();
	
	var v2 = true;
	if (partyNameTrimmed != "") {
	 v2 =  validateField(document.forms[0].elements['party.partyName'],'party.partyName','alphaNumPatWithSpace');	
	}
	if (v1 == false)
		document.forms[0].elements['party.id.partyId'].focus();

	if (v2 == false)
		document.forms[0].elements['party.partyName'].focus();		
	
	if(partyId != "" || partyNameTrimmed != "")
	{
		if(v1 == true && v2 == true)
		{
			document.forms[0].method.value = methodName;
	         document.forms[0].submit();
		}
	}
	else if(partyId == "" && partyNameTrimmed == "")
	{
		alert('<s:text name="party.alert.partyname"/>');
		document.forms[0].elements['party.partyName'].value = partyName;
		document.forms[0].elements['party.partyName'].focus();	
	}
}


function searchButtonStatusForId(element)
{
	
	var searchBut = document.getElementById("searchButton");	
	var searchButDisabled = document.getElementById("searchButtondisabled");

	var partyId = document.forms[0].elements["party.id.partyId"].value;
	var partyName = document.forms[0].elements["party.partyName"].value;
	if(partyId != "" || partyName != "")
	{
		searchBut.style.display = "inline";
		searchButDisabled.style.display = "none";
	}
	else{
		searchBut.style.display = "none";
		searchButDisabled.style.display = "inline";
	}
	
}



function searchButtonStatusForName(element)
{

	var searchBut = document.getElementById("searchButton");	
	var searchButDisabled = document.getElementById("searchButtondisabled");
	var partyId = document.forms[0].elements["party.id.partyId"].value;
	var partyName = document.forms[0].elements["party.partyName"].value;

	if(partyId != "" || partyName != "")
	{
		searchBut.style.display = "inline";
		searchButDisabled.style.display = "none";
	}
	else{
		searchBut.style.display = "none";
		searchButDisabled.style.display = "inline";
	}
	
}

</SCRIPT>
</head>
   
<body onmousemove="reportMove()" leftmargin="0" topmargin="0" marginheight="0" onLoad="setParentChildsFocus();setFocus(document.forms[0]);bodyOnLoad();" onunload="call()">

<s:form action="party.do">
 
 <input name="method" type="hidden" value="searchParties">
 
 <s:hidden name="party.id.entityId"/> 
 <s:hidden name="party.custodianFlag"/> 
 <input type="hidden" name="selectedPartyId"/> 
 <input type="hidden" name="selectedPartyName"/> 
 <input type="hidden" name="idElementName" value="${idElementName}"/> 
 <input type="hidden" name="descElementName" value="${descElementName}"/> 

<div style="position:absolute; left:20px; top:20px; width:453px; height:63px; border:2px outset;" color="#7E97AF">
	<div style="position:absolute; left:8px; top:4px; width:445px; height:10;">
		<table width="428px" border="0" cellpadding="0" cellspacing="0" height="30">
			<tr>
				<td width="40px"><b><s:text name="party.partyId"/></b></td>
				<td width="28px">&nbsp;</td>
				<td width="280px" >
				
					<s:textfield name="party.id.partyId" style="width:120px" onkeyup="searchButtonStatusForId(this)" titleKey="tooltip.enterPartyId" tabindex="1"/>
				</td>
				<td width="20px">&nbsp;</td>
		    </tr>
			   
			<tr>
				<td width="40px"><b><s:text name="party.partyName"/></b></td>
				<td width="28px">&nbsp;</td>
				<td  width="280px" >
				<s:textfield name="party.partyName" style="width:280px" onkeyup="searchButtonStatusForName(this)" titleKey="tooltip.enterPartyName"  tabindex="2"/>
				<td width="20px">&nbsp;</td>
				<td width="60px" id="ddimagebuttons">
					<a  id="searchButtondisabled" style="display: none" class="disabled" disabled="disabled" ><s:text name = "button.search"/></a>
					<a  id="searchButton" style="display: none" title='<s:text name = "tooltip.executeSearch"/>'tabindex="3" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onKeyDown="submitEnter(this,event)" onClick="submitFrom()"><s:text name = "button.search"/></a>	</td>
			 </tr>
		</table>
	</div>

</div>
<div color="#7E97AF" style="position:absolute; border:2px outset; left:20px; top:87px; width:453px; height:425px;">
	<div style="position:absolute;z-index:50;left:0px; top:0px; width:432px; height:10px;">
		<table  class="sort-table" id="table_2" bgcolor="#B0AFAF" width="430px" border="0" cellspacing="1" cellpadding="0"  height="20px">
			<thead>
				<tr height="20px">
					<td title ='<s:text name="tooltip.sortbyParty"/>' width="135px"><b><s:text name="party.partyId"/></b></td>
					<td title ='<s:text name="tooltip.sortbyPname"/>' width="295px"><b><s:text name="party.partyName"/></b></td>
				</tr>
			</thead>
		</table>
	</div>
	<div id="ddscrolltable" style="position:absolute; left:0px; top:0px; width:449px; height:420px; overflowY:scroll;overflow-x:hidden; ">
		<div  style="position:absolute;z-index:99;left:0px; top:22px; width:431px; height:10px;">
			<table class="sort-table" id="PartyDetails" width="432px" border="0" cellspacing="1" cellpadding="0" height="398">
				<tbody> 		
					<%int count = 0; %>  
						<s:iterator value='#request.partyList' var='partyList'>         
						<% if( count%2 == 0 ) {%><tr  class="even"><% }else  { %> <tr  class="odd"> <%}++count; %>
							<td align="left" width="135px"><s:property value="#partyList.id.partyId" />&nbsp;</td>
							<td width="295px" align="left"><s:property value="#partyList.partyName" />&nbsp;</td>
						</tr>
						</s:iterator>
				</tbody>
				<tfoot><tr><td colspan="2" ></td></tr></tfoot>
			</table>
		</div>
	</div>
</div>
<div style="position:absolute; left:398; top:528; width:70px; height:15px; visibility:visible;">
	<table width="60px" border="0" cellspacing="0" cellpadding="0" height="20">
		<tr>
			<td align="Right">
				<a tabindex="6" href=# onclick="javascript:openWindow(buildPrintURL('print','Party Search Maintenance'),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')" onMouseOut="MM_swapImgRestore()" onMouseOver="MM_swapImage('Help','','images/help_default.GIF ',1)"><img src="images/help_default.GIF " name="Help"  border="0" title='<s:text name="tooltip.helpScreen"/>' ></a>	
			</td>
			<td align="right" id="Print">
				<a tabindex="6" onKeyDown="submitEnter(this,event)" onclick="printPage();" onMouseOut="MM_swapImgRestore()" onMouseOver="MM_swapImage('Print','','images/Print_R.gif ',1)"><img src="images/Print.gif " name="Print"  border="0" title='<s:text name = "tooltip.printScreen"/>'></a>	
			</td>
		</tr>
	</table>
</div>
<div id="ddimagebuttons" color="#7E97AF" style="position:absolute; border:2px outset; left:20; top:518; width:453px; height:39px; visibility:visible;">
  <div style="position:absolute; left:6; top:4; width:80px; height:15px; visibility:visible;">
  	  <table width="70px" border="0" cellspacing="0" cellpadding="0" height="20">
	<tr>
		<td id="closebutton" width="70px" title='<s:text name = "tooltip.close"/>' >	<a tabindex="5" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onKeyDown="submitEnter(this,event)"  onclick="self.close()"><s:text name = "button.ok"/></a></td>
	</tr>
	</table>
</div>
</s:form>
</body>
</html>