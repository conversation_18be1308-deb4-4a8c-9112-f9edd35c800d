<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="BlockedPaySubReport" pageWidth="555" pageHeight="802" columnWidth="555" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0" uuid="9e978007-ec2a-4d5c-8c6a-181b4f399f51">
	<property name="ireport.zoom" value="1.0"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<queryString language="SQL">
		<![CDATA[select a.CURRENCY_CODE, a.ACC_GRP_ID, 'Cut-Off: '|| A.COB_CUTOFF as CUTOFF_TIME from PC_ACCOUNT_GROUP a, PC_CCY c  WHERE to_char(pkg_pc_tools.SYS_DATE,'HH24:MI')<= COB_CUTOFF AND C.CURRENCY_CODE = A.CURRENCY_CODE order by C.ORDINAL, C.CURRENCY_CODE, A.ACC_GRP_ID]]>

    </queryString>
	<field name="CURRENCY_CODE" class="java.lang.String"/>
	<field name="ACC_GRP_ID" class="java.lang.String"/>
	<field name="CUTOFF_TIME" class="java.lang.String"/>
	<group name="CURRENCY_CODE">
		<groupExpression><![CDATA[$F{CURRENCY_CODE}]]></groupExpression>
		<groupHeader>
			<band height="23">
				<rectangle>
					<reportElement uuid="f7ab6983-2b61-4048-9d60-cea2397ace1b" mode="Opaque" x="-1" y="0" width="802" height="23" forecolor="#D6E3FE" backcolor="#D6E3FE"/>
				</rectangle>
				<textField evaluationTime="Group" evaluationGroup="CURRENCY_CODE">
					<reportElement uuid="401c7b3b-af73-4d40-8982-9c1692eb7085" x="0" y="0" width="219" height="20"/>
					<textElement verticalAlignment="Middle">
						<font isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{CURRENCY_CODE}]]></textFieldExpression>
				</textField>
			</band>
		</groupHeader>
	</group>
	<background>
		<band splitType="Stretch"/>
	</background>
	<detail>
		<band height="20" splitType="Stretch">
			<textField>
				<reportElement uuid="539e9ec2-a9c9-4a3c-9a88-fb8e1aaa1ee6" x="0" y="0" width="277" height="20"/>
				<textElement/>
				<textFieldExpression><![CDATA[$F{ACC_GRP_ID}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement uuid="f883a4e9-2c56-49ee-b9e8-36929addefa6" x="277" y="0" width="277" height="20"/>
				<textElement/>
				<textFieldExpression><![CDATA[$F{CUTOFF_TIME}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
</jasperReport>
