<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="CurrencyDashbord" pageWidth="1190" pageHeight="842" orientation="Landscape" columnWidth="1150" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20" uuid="2c06d4d6-1065-463a-a0d4-f1a912e4e11e">
	<property name="ireport.zoom" value="1.0"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<parameter name="p_Dictionary_Data" class="java.util.HashMap"/>
	<parameter name="p_selectedCurrency" class="java.lang.String"/>
	<parameter name="p_selectedCurrencyName" class="java.lang.String"/>
	<parameter name="p_ApplyCcyThreshold" class="java.lang.String"/>
	<parameter name="p_ApplyCcyMultiplier" class="java.lang.String"/>
	<parameter name="p_selectedEntity" class="java.lang.String"/>
	<parameter name="p_SpreadOnly" class="java.lang.String"/>
	<parameter name="p_selectedValueVolume" class="java.lang.String"/>
	<parameter name="p_ApplyCcyThresholdAsString" class="java.lang.String"/>
	<parameter name="p_ApplyCcyMultiplierAsString" class="java.lang.String"/>
	<parameter name="p_selectedValueVolumeAsString" class="java.lang.String"/>
	<parameter name="p_SpreadOnlyAsString" class="java.lang.String"/>
	<parameter name="p_selectedUserId" class="java.lang.String"/>
	<parameter name="p_selectedEntityName" class="java.lang.String"/>
	<parameter name="p_ReportDateTime" class="java.lang.String"/>
	<parameter name="p_selectedFromDate" class="java.lang.String"/>
	<parameter name="p_selectedFromDateAsISO" class="java.lang.String"/>
	<parameter name="swtUtil" class="org.swallow.util.SwtUtil"/>
	<queryString>
		<![CDATA[SELECT ENTITY_ID,
       VALUE_DATE,
       CURRENCY_CODE,
       ACC_GRP_ID,
       ACCOUNT_ID,
       TOTAL_PAYMENTS,
       AVAILABLE_BAL,
       WAITING,
       STOPPED,
       BLOCKED,
       BACK_VAL,
       CANCELLED,
       RELEASED,
       REPAIR,
       SUPPRESSED,
       REJECTED,
       ORDINAL,
       DECIMAL_PLACES,
       DISPLAY_MULTIPLIER,
       STATUS
  FROM TABLE (pkg_pc_dashboard.fn_ccy_dash_report ($P{p_selectedEntity},
                                                    $P{p_selectedFromDateAsISO},
                                                   $P{p_ApplyCcyThreshold},
                                                   $P{p_SpreadOnly},
                                                   $P{p_selectedUserId},
                                                   $P{p_ApplyCcyMultiplier}, $P{p_selectedValueVolume}))]]>
	</queryString>
	<field name="ENTITY_ID" class="java.lang.String"/>
	<field name="VALUE_DATE" class="java.lang.String"/>
	<field name="CURRENCY_CODE" class="java.lang.String"/>
	<field name="ACC_GRP_ID" class="java.lang.String"/>
	<field name="ACCOUNT_ID" class="java.lang.String"/>
	<field name="TOTAL_PAYMENTS" class="java.math.BigDecimal"/>
	<field name="AVAILABLE_BAL" class="java.math.BigDecimal"/>
	<field name="WAITING" class="java.math.BigDecimal"/>
	<field name="STOPPED" class="java.math.BigDecimal"/>
	<field name="BLOCKED" class="java.math.BigDecimal"/>
	<field name="BACK_VAL" class="java.math.BigDecimal"/>
	<field name="CANCELLED" class="java.math.BigDecimal"/>
	<field name="RELEASED" class="java.math.BigDecimal"/>
	<field name="REPAIR" class="java.math.BigDecimal"/>
	<field name="SUPPRESSED" class="java.math.BigDecimal"/>
	<field name="REJECTED" class="java.math.BigDecimal"/>
	<field name="ORDINAL" class="java.math.BigDecimal"/>
	<field name="DECIMAL_PLACES" class="java.math.BigDecimal"/>
	<field name="DISPLAY_MULTIPLIER" class="java.lang.String"/>
	<field name="STATUS" class="java.lang.String"/>
	<variable name="totalTotalPayPerCcy" class="java.math.BigDecimal" resetType="Group" resetGroup="CURRENCY_CODE" calculation="Sum">
		<variableExpression><![CDATA[$F{TOTAL_PAYMENTS}]]></variableExpression>
	</variable>
	<variable name="totalAvailableBalPerCcy" class="java.math.BigDecimal" resetType="Group" resetGroup="CURRENCY_CODE" calculation="Sum">
		<variableExpression><![CDATA[$F{AVAILABLE_BAL}]]></variableExpression>
	</variable>
	<variable name="totalWaitingPerCcy" class="java.math.BigDecimal" resetType="Group" resetGroup="CURRENCY_CODE" calculation="Sum">
		<variableExpression><![CDATA[$F{WAITING}]]></variableExpression>
	</variable>
	<variable name="totalStoppedPerCcy" class="java.math.BigDecimal" resetType="Group" resetGroup="CURRENCY_CODE" calculation="Sum">
		<variableExpression><![CDATA[$F{STOPPED}]]></variableExpression>
	</variable>
	<variable name="totalBlockedPerCcy" class="java.math.BigDecimal" resetType="Group" resetGroup="CURRENCY_CODE" calculation="Sum">
		<variableExpression><![CDATA[$F{BLOCKED}]]></variableExpression>
	</variable>
	<variable name="totalBackValuePerCcy" class="java.math.BigDecimal" resetType="Group" resetGroup="CURRENCY_CODE" calculation="Sum">
		<variableExpression><![CDATA[$F{BACK_VAL}]]></variableExpression>
	</variable>
	<variable name="totalCancelledPerCcy" class="java.math.BigDecimal" resetType="Group" resetGroup="CURRENCY_CODE" calculation="Sum">
		<variableExpression><![CDATA[$F{CANCELLED}]]></variableExpression>
	</variable>
	<variable name="totalReleasedPerCcy" class="java.math.BigDecimal" resetType="Group" resetGroup="CURRENCY_CODE" calculation="Sum">
		<variableExpression><![CDATA[$F{RELEASED}]]></variableExpression>
	</variable>
	<variable name="totalRepairPerCcy" class="java.math.BigDecimal" resetType="Group" resetGroup="CURRENCY_CODE" calculation="Highest">
		<variableExpression><![CDATA[$F{REPAIR}]]></variableExpression>
	</variable>
	<variable name="totalSuppressedPerCcy" class="java.math.BigDecimal" resetType="Group" resetGroup="CURRENCY_CODE" calculation="Highest">
		<variableExpression><![CDATA[$F{SUPPRESSED}]]></variableExpression>
	</variable>
	<variable name="totalRejectedPerCcy" class="java.math.BigDecimal" resetType="Group" resetGroup="CURRENCY_CODE" calculation="Highest">
		<variableExpression><![CDATA[$F{REJECTED}]]></variableExpression>
	</variable>
	<variable name="totalTotalPayPerAcctGrp" class="java.math.BigDecimal" resetType="Group" resetGroup="ACC_GRP_ID" calculation="Sum">
		<variableExpression><![CDATA[$F{TOTAL_PAYMENTS}]]></variableExpression>
	</variable>
	<variable name="totalAvailableBalPerAcctGrp" class="java.math.BigDecimal" resetType="Group" resetGroup="ACC_GRP_ID" calculation="Sum">
		<variableExpression><![CDATA[$F{AVAILABLE_BAL}]]></variableExpression>
	</variable>
	<variable name="totalWaitingPerAcctGrp" class="java.math.BigDecimal" resetType="Group" resetGroup="ACC_GRP_ID" calculation="Sum">
		<variableExpression><![CDATA[$F{WAITING}]]></variableExpression>
	</variable>
	<variable name="totalStoppedPerAcctGrp" class="java.math.BigDecimal" resetType="Group" resetGroup="ACC_GRP_ID" calculation="Sum">
		<variableExpression><![CDATA[$F{STOPPED}]]></variableExpression>
	</variable>
	<variable name="totalBlockedPerAcctGrp" class="java.math.BigDecimal" resetType="Group" resetGroup="ACC_GRP_ID" calculation="Sum">
		<variableExpression><![CDATA[$F{BLOCKED}]]></variableExpression>
	</variable>
	<variable name="totalBackValuePerAcctGrp" class="java.math.BigDecimal" resetType="Group" resetGroup="ACC_GRP_ID" calculation="Sum">
		<variableExpression><![CDATA[$F{BACK_VAL}]]></variableExpression>
	</variable>
	<variable name="totalCancelledPerAcctGrp" class="java.math.BigDecimal" resetType="Group" resetGroup="ACC_GRP_ID" calculation="Sum">
		<variableExpression><![CDATA[$F{CANCELLED}]]></variableExpression>
	</variable>
	<variable name="totalReleasedPerAcctGrp" class="java.math.BigDecimal" resetType="Group" resetGroup="ACC_GRP_ID" calculation="Sum">
		<variableExpression><![CDATA[$F{RELEASED}]]></variableExpression>
	</variable>
	<variable name="totalRepairPerAcctGrp" class="java.math.BigDecimal" resetType="Group" resetGroup="ACC_GRP_ID" calculation="Highest">
		<variableExpression><![CDATA[$F{REPAIR}]]></variableExpression>
	</variable>
	<variable name="totalSuppressedPerAcctGrp" class="java.math.BigDecimal" resetType="Group" resetGroup="ACC_GRP_ID" calculation="Highest">
		<variableExpression><![CDATA[$F{SUPPRESSED}]]></variableExpression>
	</variable>
	<variable name="totalRejectedPerAcctGrp" class="java.math.BigDecimal" resetType="Group" resetGroup="ACC_GRP_ID" calculation="Highest">
		<variableExpression><![CDATA[$F{REJECTED}]]></variableExpression>
	</variable>
	<group name="CURRENCY_CODE">
		<groupExpression><![CDATA[$F{CURRENCY_CODE}]]></groupExpression>
		<groupHeader>
			<band height="23">
				<rectangle>
					<reportElement uuid="f7ab6983-2b61-4048-9d60-cea2397ace1b" mode="Opaque" x="-1" y="0" width="1150" height="23" forecolor="#CCCCCC" backcolor="#CCCCCC"/>
				</rectangle>
				<textField evaluationTime="Group" evaluationGroup="CURRENCY_CODE">
					<reportElement uuid="401c7b3b-af73-4d40-8982-9c1692eb7085" x="235" y="0" width="120" height="23"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font isBold="true" isItalic="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$P{swtUtil}.formatCurrency($F{CURRENCY_CODE},$V{totalAvailableBalPerCcy})]]></textFieldExpression>
				</textField>
				<textField evaluationTime="Group" evaluationGroup="CURRENCY_CODE">
					<reportElement uuid="401c7b3b-af73-4d40-8982-9c1692eb7085" x="355" y="0" width="110" height="23"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font isBold="true" isItalic="false"/>
					</textElement>
					<textFieldExpression><![CDATA[("Y".equals($P{p_selectedValueVolume})? $P{swtUtil}.formatCurrency($F{CURRENCY_CODE},$V{totalWaitingPerCcy}):""+$V{totalWaitingPerCcy})]]></textFieldExpression>
				</textField>
				<textField evaluationTime="Group" evaluationGroup="CURRENCY_CODE">
					<reportElement uuid="401c7b3b-af73-4d40-8982-9c1692eb7085" x="585" y="0" width="100" height="23"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font isBold="true" isItalic="false"/>
					</textElement>
					<textFieldExpression><![CDATA[("Y".equals($P{p_selectedValueVolume})? $P{swtUtil}.formatCurrency($F{CURRENCY_CODE},$V{totalBlockedPerCcy}):""+$V{totalBlockedPerCcy})]]></textFieldExpression>
				</textField>
				<textField evaluationTime="Group" evaluationGroup="CURRENCY_CODE">
					<reportElement uuid="401c7b3b-af73-4d40-8982-9c1692eb7085" x="910" y="0" width="60" height="23"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font isBold="true" isItalic="false"/>
					</textElement>
					<textFieldExpression><![CDATA[("Y".equals($P{p_selectedValueVolume})? $P{swtUtil}.formatCurrency($F{CURRENCY_CODE},$V{totalCancelledPerCcy}):""+$V{totalCancelledPerCcy})]]></textFieldExpression>
				</textField>
				<textField evaluationTime="Group" evaluationGroup="CURRENCY_CODE">
					<reportElement uuid="401c7b3b-af73-4d40-8982-9c1692eb7085" x="799" y="0" width="110" height="23"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font isBold="true" isItalic="false"/>
					</textElement>
					<textFieldExpression><![CDATA[("Y".equals($P{p_selectedValueVolume})? $P{swtUtil}.formatCurrency($F{CURRENCY_CODE},$V{totalReleasedPerCcy}):""+$V{totalReleasedPerCcy})]]></textFieldExpression>
				</textField>
				<textField evaluationTime="Group" evaluationGroup="CURRENCY_CODE">
					<reportElement uuid="401c7b3b-af73-4d40-8982-9c1692eb7085" x="969" y="0" width="60" height="23"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font isBold="true" isItalic="false"/>
					</textElement>
					<textFieldExpression><![CDATA[("Y".equals($P{p_selectedValueVolume})? $P{swtUtil}.formatCurrency($F{CURRENCY_CODE},$V{totalRepairPerCcy}):""+$V{totalRepairPerCcy})]]></textFieldExpression>
				</textField>
				<textField evaluationTime="Group" evaluationGroup="CURRENCY_CODE">
					<reportElement uuid="401c7b3b-af73-4d40-8982-9c1692eb7085" x="1029" y="0" width="60" height="23"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font isBold="true" isItalic="false"/>
					</textElement>
					<textFieldExpression><![CDATA[("Y".equals($P{p_selectedValueVolume})? $P{swtUtil}.formatCurrency($F{CURRENCY_CODE},$V{totalSuppressedPerCcy}):""+$V{totalSuppressedPerCcy})]]></textFieldExpression>
				</textField>
				<textField evaluationTime="Group" evaluationGroup="CURRENCY_CODE">
					<reportElement uuid="401c7b3b-af73-4d40-8982-9c1692eb7085" x="1089" y="0" width="60" height="23"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font isBold="true" isItalic="false"/>
					</textElement>
					<textFieldExpression><![CDATA[("Y".equals($P{p_selectedValueVolume})? $P{swtUtil}.formatCurrency($F{CURRENCY_CODE},$V{totalRejectedPerCcy}):""+$V{totalRejectedPerCcy})]]></textFieldExpression>
				</textField>
				<textField evaluationTime="Group" evaluationGroup="CURRENCY_CODE">
					<reportElement uuid="401c7b3b-af73-4d40-8982-9c1692eb7085" x="0" y="0" width="140" height="23"/>
					<textElement verticalAlignment="Middle">
						<font isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA["- "+$F{CURRENCY_CODE} + ("Y".equals($P{p_ApplyCcyMultiplier})?( $F{DISPLAY_MULTIPLIER} != null? "("+ $F{DISPLAY_MULTIPLIER} +")" :"" ):"")]]></textFieldExpression>
				</textField>
				<textField evaluationTime="Group" evaluationGroup="CURRENCY_CODE">
					<reportElement uuid="401c7b3b-af73-4d40-8982-9c1692eb7085" x="143" y="0" width="90" height="23"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font isBold="true" isItalic="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$P{swtUtil}.formatCurrency($F{CURRENCY_CODE},$V{totalTotalPayPerCcy})]]></textFieldExpression>
				</textField>
				<textField evaluationTime="Group" evaluationGroup="CURRENCY_CODE">
					<reportElement uuid="401c7b3b-af73-4d40-8982-9c1692eb7085" x="465" y="0" width="120" height="23"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font isBold="true" isItalic="false"/>
					</textElement>
					<textFieldExpression><![CDATA[("Y".equals($P{p_selectedValueVolume})? $P{swtUtil}.formatCurrency($F{CURRENCY_CODE},$V{totalStoppedPerCcy}):""+$V{totalStoppedPerCcy})]]></textFieldExpression>
				</textField>
				<textField evaluationTime="Group" evaluationGroup="CURRENCY_CODE">
					<reportElement uuid="401c7b3b-af73-4d40-8982-9c1692eb7085" x="685" y="0" width="114" height="23"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font isBold="true" isItalic="false"/>
					</textElement>
					<textFieldExpression><![CDATA[("Y".equals($P{p_selectedValueVolume})? $P{swtUtil}.formatCurrency($F{CURRENCY_CODE},$V{totalBackValuePerCcy}):""+$V{totalBackValuePerCcy})]]></textFieldExpression>
				</textField>
			</band>
		</groupHeader>
	</group>
	<group name="ACC_GRP_ID">
		<groupExpression><![CDATA[$F{ACC_GRP_ID}]]></groupExpression>
		<groupHeader>
			<band height="23">
				<rectangle>
					<reportElement uuid="f7ab6983-2b61-4048-9d60-cea2397ace1b" mode="Opaque" x="-1" y="0" width="1150" height="23" forecolor="#D6E3FE" backcolor="#D6E3FE"/>
				</rectangle>
				<textField evaluationTime="Group" evaluationGroup="ACC_GRP_ID">
					<reportElement uuid="401c7b3b-af73-4d40-8982-9c1692eb7085" x="969" y="0" width="60" height="23"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font isBold="true" isItalic="false"/>
					</textElement>
					<textFieldExpression><![CDATA[("Y".equals($P{p_selectedValueVolume})? "0":"0")]]></textFieldExpression>
				</textField>
				<textField evaluationTime="Group" evaluationGroup="ACC_GRP_ID">
					<reportElement uuid="401c7b3b-af73-4d40-8982-9c1692eb7085" x="584" y="0" width="100" height="23"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font isBold="true" isItalic="false"/>
					</textElement>
					<textFieldExpression><![CDATA[("Y".equals($P{p_selectedValueVolume})? $P{swtUtil}.formatCurrency($F{CURRENCY_CODE},$V{totalBlockedPerAcctGrp}):""+$V{totalBlockedPerAcctGrp})]]></textFieldExpression>
				</textField>
				<textField evaluationTime="Group" evaluationGroup="ACC_GRP_ID">
					<reportElement uuid="401c7b3b-af73-4d40-8982-9c1692eb7085" x="142" y="0" width="90" height="23"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font isBold="true" isItalic="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$P{swtUtil}.formatCurrency($F{CURRENCY_CODE},$V{totalTotalPayPerAcctGrp})]]></textFieldExpression>
				</textField>
				<textField evaluationTime="Group" evaluationGroup="ACC_GRP_ID">
					<reportElement uuid="401c7b3b-af73-4d40-8982-9c1692eb7085" x="909" y="0" width="60" height="23"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font isBold="true" isItalic="false"/>
					</textElement>
					<textFieldExpression><![CDATA[("Y".equals($P{p_selectedValueVolume})? $P{swtUtil}.formatCurrency($F{CURRENCY_CODE},$V{totalCancelledPerAcctGrp}):""+$V{totalCancelledPerAcctGrp})]]></textFieldExpression>
				</textField>
				<textField evaluationTime="Group" evaluationGroup="ACC_GRP_ID">
					<reportElement uuid="401c7b3b-af73-4d40-8982-9c1692eb7085" x="355" y="0" width="110" height="23"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font isBold="true" isItalic="false"/>
					</textElement>
					<textFieldExpression><![CDATA[("Y".equals($P{p_selectedValueVolume})? $P{swtUtil}.formatCurrency($F{CURRENCY_CODE},$V{totalWaitingPerAcctGrp}):""+$V{totalWaitingPerAcctGrp})]]></textFieldExpression>
				</textField>
				<textField evaluationTime="Group" evaluationGroup="ACC_GRP_ID">
					<reportElement uuid="401c7b3b-af73-4d40-8982-9c1692eb7085" stretchType="RelativeToTallestObject" x="0" y="0" width="140" height="23" isPrintWhenDetailOverflows="true"/>
					<textElement verticalAlignment="Middle">
						<font isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA["- "+$F{ACC_GRP_ID}]]></textFieldExpression>
				</textField>
				<textField evaluationTime="Group" evaluationGroup="ACC_GRP_ID">
					<reportElement uuid="401c7b3b-af73-4d40-8982-9c1692eb7085" x="799" y="0" width="110" height="23"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font isBold="true" isItalic="false"/>
					</textElement>
					<textFieldExpression><![CDATA[("Y".equals($P{p_selectedValueVolume})? $P{swtUtil}.formatCurrency($F{CURRENCY_CODE},$V{totalReleasedPerAcctGrp}):""+$V{totalReleasedPerAcctGrp})]]></textFieldExpression>
				</textField>
				<textField evaluationTime="Group" evaluationGroup="ACC_GRP_ID">
					<reportElement uuid="401c7b3b-af73-4d40-8982-9c1692eb7085" x="1029" y="0" width="60" height="23"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font isBold="true" isItalic="false"/>
					</textElement>
					<textFieldExpression><![CDATA[("Y".equals($P{p_selectedValueVolume})? "0":"0")]]></textFieldExpression>
				</textField>
				<textField evaluationTime="Group" evaluationGroup="ACC_GRP_ID">
					<reportElement uuid="401c7b3b-af73-4d40-8982-9c1692eb7085" x="234" y="0" width="120" height="23"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font isBold="true" isItalic="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$P{swtUtil}.formatCurrency($F{CURRENCY_CODE},$V{totalAvailableBalPerAcctGrp})]]></textFieldExpression>
				</textField>
				<textField evaluationTime="Group" evaluationGroup="ACC_GRP_ID">
					<reportElement uuid="401c7b3b-af73-4d40-8982-9c1692eb7085" x="1089" y="0" width="60" height="23"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font isBold="true" isItalic="false"/>
					</textElement>
					<textFieldExpression><![CDATA[("Y".equals($P{p_selectedValueVolume})? "0":"0")]]></textFieldExpression>
				</textField>
				<textField evaluationTime="Group" evaluationGroup="ACC_GRP_ID">
					<reportElement uuid="401c7b3b-af73-4d40-8982-9c1692eb7085" x="464" y="0" width="120" height="23"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font isBold="true" isItalic="false"/>
					</textElement>
					<textFieldExpression><![CDATA[("Y".equals($P{p_selectedValueVolume})? $P{swtUtil}.formatCurrency($F{CURRENCY_CODE},$V{totalStoppedPerAcctGrp}):""+$V{totalStoppedPerAcctGrp})]]></textFieldExpression>
				</textField>
				<textField evaluationTime="Group" evaluationGroup="ACC_GRP_ID">
					<reportElement uuid="401c7b3b-af73-4d40-8982-9c1692eb7085" x="685" y="0" width="114" height="23"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font isBold="true" isItalic="false"/>
					</textElement>
					<textFieldExpression><![CDATA[("Y".equals($P{p_selectedValueVolume})? $P{swtUtil}.formatCurrency($F{CURRENCY_CODE},$V{totalBackValuePerAcctGrp}):""+$V{totalBackValuePerAcctGrp})]]></textFieldExpression>
				</textField>
			</band>
		</groupHeader>
	</group>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band height="111" splitType="Stretch">
			<textField>
				<reportElement uuid="be368d8c-309f-4fa7-b290-dbb3429be748" mode="Opaque" x="0" y="10" width="1149" height="26" backcolor="#D6E3FE"/>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
					<font size="14" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{p_Dictionary_Data}.get("pReportTitle")]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement uuid="2c3a0aa0-6b0f-4c1b-9479-5348d1134f18" x="585" y="47" width="100" height="20"/>
				<textElement/>
				<textFieldExpression><![CDATA[$P{p_ApplyCcyThresholdAsString}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement uuid="c84f16be-2f5d-4120-9e46-b18f99f8ffdd" x="419" y="67" width="166" height="20"/>
				<textElement/>
				<textFieldExpression><![CDATA[$P{p_Dictionary_Data}.get("pApplyCcyMultiplier")]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement uuid="2c3a0aa0-6b0f-4c1b-9479-5348d1134f18" x="1052" y="47" width="100" height="20"/>
				<textElement/>
				<textFieldExpression><![CDATA[$P{p_ReportDateTime}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement uuid="37b92e0d-a801-44f5-8a47-fcddf7633c35" x="419" y="87" width="166" height="24"/>
				<textElement/>
				<textFieldExpression><![CDATA[$P{p_Dictionary_Data}.get("pSpreadOnly")]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement uuid="2c3a0aa0-6b0f-4c1b-9479-5348d1134f18" x="585" y="67" width="100" height="20"/>
				<textElement/>
				<textFieldExpression><![CDATA[$P{p_ApplyCcyMultiplierAsString}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement uuid="37b92e0d-a801-44f5-8a47-fcddf7633c35" x="969" y="67" width="83" height="20"/>
				<textElement/>
				<textFieldExpression><![CDATA[$P{p_Dictionary_Data}.get("valueDate")]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement uuid="3ea258d1-3316-484f-afbc-04bde25446e7" x="82" y="47" width="100" height="20"/>
				<textElement/>
				<textFieldExpression><![CDATA[$P{p_selectedEntity}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement uuid="3ea258d1-3316-484f-afbc-04bde25446e7" x="1052" y="67" width="100" height="20"/>
				<textElement/>
				<textFieldExpression><![CDATA[$P{p_selectedFromDate}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement uuid="37b92e0d-a801-44f5-8a47-fcddf7633c35" x="419" y="47" width="166" height="20"/>
				<textElement/>
				<textFieldExpression><![CDATA[$P{p_Dictionary_Data}.get("pApplyCcyThreshold")]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement uuid="3ea258d1-3316-484f-afbc-04bde25446e7" x="585" y="87" width="100" height="24"/>
				<textElement/>
				<textFieldExpression><![CDATA[$P{p_SpreadOnlyAsString}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement uuid="c84f16be-2f5d-4120-9e46-b18f99f8ffdd" x="0" y="47" width="83" height="20"/>
				<textElement/>
				<textFieldExpression><![CDATA[$P{p_Dictionary_Data}.get("pEntity")]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement uuid="2c3a0aa0-6b0f-4c1b-9479-5348d1134f18" x="182" y="47" width="100" height="20"/>
				<textElement/>
				<textFieldExpression><![CDATA[$P{p_selectedEntityName}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement uuid="c84f16be-2f5d-4120-9e46-b18f99f8ffdd" x="969" y="47" width="83" height="20"/>
				<textElement/>
				<textFieldExpression><![CDATA[$P{p_Dictionary_Data}.get("pReportDateTime")]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement uuid="37b92e0d-a801-44f5-8a47-fcddf7633c35" x="0" y="67" width="83" height="20"/>
				<textElement/>
				<textFieldExpression><![CDATA[$P{p_Dictionary_Data}.get("pValueVolume")]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement uuid="3ea258d1-3316-484f-afbc-04bde25446e7" x="83" y="67" width="100" height="20"/>
				<textElement/>
				<textFieldExpression><![CDATA[$P{p_selectedValueVolumeAsString}]]></textFieldExpression>
			</textField>
		</band>
	</title>
	<pageHeader>
		<band height="35" splitType="Stretch"/>
	</pageHeader>
	<columnHeader>
		<band height="24" splitType="Stretch">
			<textField>
				<reportElement uuid="21d8702e-a2e3-41ca-bbea-12a97678ee3e" x="0" y="0" width="139" height="20"/>
				<textElement markup="none"/>
				<textFieldExpression><![CDATA[$P{p_Dictionary_Data}.get("pCurrency")]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement uuid="861750e1-7a1a-42de-88f5-a809aa30f5a0" x="141" y="0" width="90" height="20"/>
				<textElement markup="none"/>
				<textFieldExpression><![CDATA[$P{p_Dictionary_Data}.get("pTotalPay")]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement uuid="83ab5331-99c5-4556-9f94-2f52aace71a6" x="233" y="0" width="120" height="20"/>
				<textElement markup="none"/>
				<textFieldExpression><![CDATA[$P{p_Dictionary_Data}.get("pAvailableBal")]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement uuid="5daf33f7-e04f-4ccb-bcfd-12403727d731" x="355" y="0" width="110" height="20"/>
				<textElement markup="none"/>
				<textFieldExpression><![CDATA[$P{p_Dictionary_Data}.get("pWaiting")]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement uuid="5ff1588e-50cd-48b7-8b20-273672006ba2" x="584" y="0" width="100" height="20"/>
				<textElement markup="none"/>
				<textFieldExpression><![CDATA[$P{p_Dictionary_Data}.get("pBlocked")]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement uuid="152a59d5-8ccb-44e0-80a7-0db4635fe3f1" x="909" y="0" width="60" height="20"/>
				<textElement markup="none"/>
				<textFieldExpression><![CDATA[$P{p_Dictionary_Data}.get("pCancelled")]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement uuid="da51486c-a837-4e00-bc02-eb413fedfa3e" x="799" y="0" width="110" height="20"/>
				<textElement markup="none"/>
				<textFieldExpression><![CDATA[$P{p_Dictionary_Data}.get("pReleased")]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement uuid="20557185-8c61-42c8-8cec-23fb4b509cde" x="969" y="0" width="60" height="20"/>
				<textElement markup="none"/>
				<textFieldExpression><![CDATA[$P{p_Dictionary_Data}.get("pRepair")]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement uuid="cf008f24-cc1b-45c6-8fcd-c6a7fbcadf83" x="1029" y="0" width="60" height="20"/>
				<textElement markup="none"/>
				<textFieldExpression><![CDATA[$P{p_Dictionary_Data}.get("pSuppressed")]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement uuid="70d1f632-7361-4243-84d2-9b68e90af011" x="1089" y="0" width="60" height="20"/>
				<textElement markup="none"/>
				<textFieldExpression><![CDATA[$P{p_Dictionary_Data}.get("pRejected")]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement uuid="bd0bf21b-9666-4956-bfdb-c7d1b450e643" x="464" y="0" width="120" height="20"/>
				<textElement markup="none"/>
				<textFieldExpression><![CDATA[$P{p_Dictionary_Data}.get("pStopped")]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement uuid="5ff1588e-50cd-48b7-8b20-273672006ba2" x="685" y="0" width="114" height="20"/>
				<textElement markup="none"/>
				<textFieldExpression><![CDATA[$P{p_Dictionary_Data}.get("pBackValue")]]></textFieldExpression>
			</textField>
		</band>
	</columnHeader>
	<detail>
		<band height="23" splitType="Stretch">
			<textField isStretchWithOverflow="true">
				<reportElement uuid="a2b0bb10-8903-40f9-8ce4-242c8c75e800" x="0" y="0" width="141" height="20">
					<property name="net.sf.jasperreports.print.keep.full.text" value="true"/>
					<property name="net.sf.jasperreports.export.xls.wrap.text" value="false"/>
					<property name="net.sf.jasperreports.export.xls.auto.fit.column" value="true"/>
				</reportElement>
				<textElement verticalAlignment="Middle"/>
				<textFieldExpression><![CDATA["  "+$F{ACCOUNT_ID}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true">
				<reportElement uuid="7d2563b6-cfd5-4e6e-aa69-571d755ad425" x="143" y="0" width="90" height="20">
					<property name="net.sf.jasperreports.print.keep.full.text" value="true"/>
					<property name="net.sf.jasperreports.export.xls.wrap.text" value="false"/>
					<property name="net.sf.jasperreports.export.xls.auto.fit.column" value="true"/>
				</reportElement>
				<textElement verticalAlignment="Middle"/>
				<textFieldExpression><![CDATA[$P{swtUtil}.formatCurrency($F{CURRENCY_CODE},$F{TOTAL_PAYMENTS})]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true">
				<reportElement uuid="e03c2e65-3d8f-48bf-93e8-62b5402e08c7" x="235" y="0" width="120" height="20">
					<property name="net.sf.jasperreports.print.keep.full.text" value="true"/>
					<property name="net.sf.jasperreports.export.xls.wrap.text" value="false"/>
					<property name="net.sf.jasperreports.export.xls.auto.fit.column" value="true"/>
				</reportElement>
				<textElement verticalAlignment="Middle"/>
				<textFieldExpression><![CDATA[$P{swtUtil}.formatCurrency($F{CURRENCY_CODE},$F{AVAILABLE_BAL})]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true">
				<reportElement uuid="48f9b7f7-86dc-4a9d-bbfd-02ece8425dfa" x="355" y="0" width="110" height="20">
					<property name="net.sf.jasperreports.print.keep.full.text" value="true"/>
					<property name="net.sf.jasperreports.export.xls.wrap.text" value="false"/>
					<property name="net.sf.jasperreports.export.xls.auto.fit.column" value="true"/>
				</reportElement>
				<textElement verticalAlignment="Middle"/>
				<textFieldExpression><![CDATA[("Y".equals($P{p_selectedValueVolume})? $P{swtUtil}.formatCurrency($F{CURRENCY_CODE},$F{WAITING}):""+$F{WAITING})]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true">
				<reportElement uuid="344dcfd5-190e-403c-a683-7e0e80d384ce" x="585" y="0" width="100" height="20">
					<property name="net.sf.jasperreports.print.keep.full.text" value="true"/>
					<property name="net.sf.jasperreports.export.xls.wrap.text" value="false"/>
					<property name="net.sf.jasperreports.export.xls.auto.fit.column" value="true"/>
				</reportElement>
				<textElement verticalAlignment="Middle"/>
				<textFieldExpression><![CDATA[("Y".equals($P{p_selectedValueVolume})? $P{swtUtil}.formatCurrency($F{CURRENCY_CODE},$F{BLOCKED}):""+$F{BLOCKED})]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true">
				<reportElement uuid="d2570a0b-8b49-4ea9-adaf-8eacf6685a52" x="910" y="0" width="60" height="20">
					<property name="net.sf.jasperreports.print.keep.full.text" value="true"/>
					<property name="net.sf.jasperreports.export.xls.wrap.text" value="false"/>
					<property name="net.sf.jasperreports.export.xls.auto.fit.column" value="true"/>
				</reportElement>
				<textElement verticalAlignment="Middle"/>
				<textFieldExpression><![CDATA[("Y".equals($P{p_selectedValueVolume})? $P{swtUtil}.formatCurrency($F{CURRENCY_CODE},$F{CANCELLED}):""+$F{CANCELLED})]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true">
				<reportElement uuid="8a91c8da-11f1-4bcb-b387-879b2911269b" x="799" y="0" width="110" height="20">
					<property name="net.sf.jasperreports.print.keep.full.text" value="true"/>
					<property name="net.sf.jasperreports.export.xls.wrap.text" value="false"/>
					<property name="net.sf.jasperreports.export.xls.auto.fit.column" value="true"/>
				</reportElement>
				<textElement verticalAlignment="Middle"/>
				<textFieldExpression><![CDATA[("Y".equals($P{p_selectedValueVolume})? $P{swtUtil}.formatCurrency($F{CURRENCY_CODE},$F{RELEASED}):""+$F{RELEASED})]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true">
				<reportElement uuid="4da0bdd3-4857-4cb7-90cf-9e4914cb2ea1" x="969" y="0" width="60" height="20">
					<property name="net.sf.jasperreports.print.keep.full.text" value="true"/>
					<property name="net.sf.jasperreports.export.xls.wrap.text" value="false"/>
					<property name="net.sf.jasperreports.export.xls.auto.fit.column" value="true"/>
				</reportElement>
				<textElement verticalAlignment="Middle"/>
				<textFieldExpression><![CDATA[("Y".equals($P{p_selectedValueVolume})? "0":"0")]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true">
				<reportElement uuid="931cdb9a-65da-49b9-8962-0d5eaa142cfe" x="1029" y="0" width="60" height="20">
					<property name="net.sf.jasperreports.print.keep.full.text" value="true"/>
					<property name="net.sf.jasperreports.export.xls.wrap.text" value="false"/>
					<property name="net.sf.jasperreports.export.xls.auto.fit.column" value="true"/>
				</reportElement>
				<textElement verticalAlignment="Middle"/>
				<textFieldExpression><![CDATA[("Y".equals($P{p_selectedValueVolume})? "0":"0")]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true">
				<reportElement uuid="81900af2-b804-42b3-9747-4df5a4b98f01" x="1089" y="0" width="60" height="20">
					<property name="net.sf.jasperreports.print.keep.full.text" value="true"/>
					<property name="net.sf.jasperreports.export.xls.wrap.text" value="false"/>
					<property name="net.sf.jasperreports.export.xls.auto.fit.column" value="true"/>
				</reportElement>
				<textElement verticalAlignment="Middle"/>
				<textFieldExpression><![CDATA[("Y".equals($P{p_selectedValueVolume})? "0":"0")]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true">
				<reportElement uuid="862abbd5-767c-4590-b185-d443248336ee" x="465" y="0" width="120" height="20">
					<property name="net.sf.jasperreports.print.keep.full.text" value="true"/>
					<property name="net.sf.jasperreports.export.xls.wrap.text" value="false"/>
					<property name="net.sf.jasperreports.export.xls.auto.fit.column" value="true"/>
				</reportElement>
				<textElement verticalAlignment="Middle"/>
				<textFieldExpression><![CDATA[("Y".equals($P{p_selectedValueVolume})? $P{swtUtil}.formatCurrency($F{CURRENCY_CODE},$F{STOPPED}):""+$F{STOPPED})]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true">
				<reportElement uuid="344dcfd5-190e-403c-a683-7e0e80d384ce" x="685" y="0" width="114" height="20">
					<property name="net.sf.jasperreports.print.keep.full.text" value="true"/>
					<property name="net.sf.jasperreports.export.xls.wrap.text" value="false"/>
					<property name="net.sf.jasperreports.export.xls.auto.fit.column" value="true"/>
				</reportElement>
				<textElement verticalAlignment="Middle"/>
				<textFieldExpression><![CDATA[("Y".equals($P{p_selectedValueVolume})? $P{swtUtil}.formatCurrency($F{CURRENCY_CODE},$F{BACK_VAL}):""+$F{BACK_VAL})]]></textFieldExpression>
			</textField>
		</band>
	</detail>
</jasperReport>
