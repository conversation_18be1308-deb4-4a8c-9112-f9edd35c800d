<!doctype html>
<%@ include file="/taglib.jsp"%>
<%@ page import="org.swallow.util.SwtConstants"%>
<html lang="en">
<head>
<meta charset="utf-8">
<title><s:if test='"add" == #request.screenName' >
	Spread Profiles Maintenance Add - SMART-Predict
</s:if> <s:if test='"add" != #request.screenName' >
		<s:if test='"change" == #request.screenName' >
		Spread Profiles Maintenance Change - SMART-Predict
	</s:if>
		<s:if test='"change" != #request.screenName' >
		Spread Profiles Maintenance View - SMART-Predict
	</s:if>
	</s:if></title>
<!-- <base href="./"> -->
<meta name="viewport" content="width=device-width, initial-scale=1">
<link rel="icon" type="image/x-icon" href="favicon.ico">
<link rel="stylesheet" href="angularSources/styles.css?version=<%= SwtUtil.appVersion %>">
</head>
<body>
<%@ include file="/angularJSUtils.jsp"%>
	<script type="text/javascript">
		var screenRoute = "spreadProfilesAdd";
		var appName = "<%=SwtUtil.appName%>";
		var requestURL = new String('<%=request.getRequestURL()%>');

		/**
		  * help
		  * This function opens the help screen 
		  * @return none
		  */
		function help(){
			openWindow(buildPrintURL('print','Spread Profiles Maintenance Amend'),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')
		}
	</script>
    <%@ include file="/angularscripts.jsp"%>
</body>
</html>
