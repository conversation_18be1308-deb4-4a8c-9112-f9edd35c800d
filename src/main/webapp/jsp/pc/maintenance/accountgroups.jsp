<!doctype html>
<%@ include file="/taglib.jsp"%>
<%@ page import="org.swallow.util.SwtConstants"%>
<html lang="en">
<head>
<meta charset="utf-8">
<title>Account Groups Maintenance - SMART-Predict</title>
<!-- <base href="./"> -->
<meta name="viewport" content="width=device-width, initial-scale=1">
<link rel="icon" type="image/x-icon" href="favicon.ico">
<link rel="stylesheet" href="angularSources/styles.css?version=<%= SwtUtil.appVersion %>">
</head>
<body>
<%@ include file="/angularJSUtils.jsp"%>
<script type="text/javascript">

var appName = "<%=SwtUtil.appName%>";
var baseURL = new String('<%=request.getRequestURL()%>');
var screenRoute = "AccountsGroup";


function openChildWindow(methodName, screenName){
	var param = '/' + appName + '/accountGroupsPCM.do?method='+methodName
	param += "&screenName=" + screenName;
	var 	mainWindow = openWindow (param, 'AccountGroupDetail','left=10,top=230,width=1200,height=600,toolbar=0, resizable=yes, //status=yes, scrollbars=yes');	
	return false;
}

function help() {
    openWindow(buildPrintURL('print','Account Groups Maintenance'),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')

}
 

 </script>
<%@ include file="/angularscripts.jsp"%>

</body>
</html>