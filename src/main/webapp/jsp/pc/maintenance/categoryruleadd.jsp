<!doctype html>
<%@ include file="/taglib.jsp"%>
<%@ page import="org.swallow.util.SwtConstants"%>
<html lang="en">
<head>
<meta charset="utf-8">
<!-- <base href="./"> -->
<meta name="viewport" content="width=device-width, initial-scale=1">
<link rel="icon" type="image/x-icon" href="favicon.ico">
<link rel="stylesheet" href="angularSources/styles.css?version=<%= SwtUtil.appVersion %>">
</head>
<body>
<%@ include file="/angularJSUtils.jsp"%>

<script type="text/javascript">
var screenTitle = "";
screenTitle = getMessage("categoryRuleDetails.title.window", null);
document.title = screenTitle;
var appName = "<%=SwtUtil.appName%>";
var appName = "<%=SwtUtil.appName%>";
var baseURL = new String('<%=request.getRequestURL()%>');
var screenRoute = "categoryRuleAdd";
	function openChildWindow() {
		var param = '/' + appName + '/expressionBuilderPCM.do?method=expressionBuilder&calledFrom=categoryRuleAdd';
		var mainWindow = openWindow(
						param,
						'Expression Builder',
						'left=10,top=230,width=1100,height=550,toolbar=0, resizable=yes, //status=yes, scrollbars=yes');
		return false;
	}
</script>
<%@ include file="/angularscripts.jsp"%>
</body>
</html>