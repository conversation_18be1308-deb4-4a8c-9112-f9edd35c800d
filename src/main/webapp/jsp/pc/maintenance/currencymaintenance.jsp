<!doctype html>
<%@ include file="/taglib.jsp"%>
<%@ page import="org.swallow.util.SwtConstants"%>
<html lang="en">
<head>
<meta charset="utf-8">
<!-- <base href="./"> -->
<meta name="viewport" content="width=device-width, initial-scale=1">
<link rel="icon" type="image/x-icon" href="favicon.ico">
<link rel="stylesheet" href="angularSources/styles.css?version=<%= SwtUtil.appVersion %>">
</head>
<body>
<%@ include file="/angularJSUtils.jsp"%>

<script type="text/javascript">
var screenTitle = "";
screenTitle = getMessage("currencyMaintenance.title.window", null);
document.title = screenTitle;
var appName = "<%=SwtUtil.appName%>";
var baseURL = new String('<%=request.getRequestURL()%>');
var screenRoute = "currency";
	/**
	 * help
	 * This function opens the help screen 
	 * @return none
	 */
	function help() {
		openWindow(
				buildPrintURL('print', 'Currency Maintenance'),
				'sectionprintdwindow',
				'left=10,top=230,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no')
	}
</script>
<%@ include file="/angularscripts.jsp"%>
</body>
</html>