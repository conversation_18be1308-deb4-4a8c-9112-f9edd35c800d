<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@ include file="/taglib.jsp"  %>
<%@ page import="org.swallow.util.SwtConstants" %>
<%@ page import="org.swallow.util.SwtUtil" %>
<html>
<head>
<title><s:text name="scenario.distributionlist"/></title>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">

<SCRIPT language="JAVASCRIPT">

	/**
	 * Called on first load of the screen
	 *
	 **/
	function bodyOnLoad(){

		xl = new XLSheet("usersDistributionList","table_2", ["String","String", "String"],"111");
		xl.onsort = xl.onfilter = updateColors;
		
		var email_users ="";
		var allCheckBoxList = document.getElementsByName('checkDistList');
		for (var i = 0; i < allCheckBoxList.length; i++) {
			if(allCheckBoxList[i].type=="checkbox"){
		      if(allCheckBoxList[i].checked==true){
		        email_users+=allCheckBoxList[i].value+";";
		      }
		    }
		}
		// test if the value stored in the opener window is different of the original value or no
		if(window.opener.document.forms[0].emailusers.value != email_users.substring(0, email_users.length - 1)){
			var openerWindowDistList = window.opener.document.forms[0].emailusers.value.split(";");
			for (var i = 0; i < allCheckBoxList.length; i++) {
				if(allCheckBoxList[i].type=="checkbox"){
					if(openerWindowDistList.indexOf(allCheckBoxList[i].value) > -1){
						allCheckBoxList[i].setAttribute("checked", "true");
						allCheckBoxList[i].checked = true;
					}else{
						allCheckBoxList[i].setAttribute("checked", "false");
						allCheckBoxList[i].checked = false;
					}
			    }
			}
		}else{
			window.opener.document.forms[0].emailusers.value = email_users.substring(0, email_users.length - 1);
		}
		checkIfAllCheckboxChecked();
	}

	/**
	 * This method is used to send the "email_users" to the parent screen while clicking on the save button. 
	 *
	 **/
	function submitForm(){
		var email_users ="";
		var allCheckBoxList = document.getElementsByName('checkDistList');
		for (var i = 0; i < allCheckBoxList.length; i++) {
			if(allCheckBoxList[i].type=="checkbox"){
		      if(allCheckBoxList[i].checked==true){
		        email_users+=allCheckBoxList[i].value+";";
		      }
		    }
		}
		window.opener.document.forms[0].emailusers.value = email_users.substring(0, email_users.length - 1);
		window.close();
	}

	/**
	 * Called when click on checkbox
	 */
	function handleClick(cb) {
	  if(cb.checked){
		  cb.setAttribute("checked", "true");
		  cb.checked = true;
		  }else{
			  cb.setAttribute("checked", "false");
			  cb.checked = false;
		  }
	  checkIfAllCheckboxChecked();
	}

	/**
	 * Called when click the select all checkbox
	 */
	function selectedallInput(cb)
	{	
		var allCheckBoxList = document.getElementsByName('checkDistList');
		if(cb.checked){
			for (var i = 0; i < allCheckBoxList.length; i++) {
				if(allCheckBoxList[i].type=="checkbox"){
					allCheckBoxList[i].setAttribute("checked", "true");
					allCheckBoxList[i].checked = true;
			    }
			}
		  }else{
			  for (var i = 0; i < allCheckBoxList.length; i++) {
					if(allCheckBoxList[i].type=="checkbox"){
						allCheckBoxList[i].checked = false;
				    }
				}
		  }
	}

	/**
	 * this method is used to check if all checkbox are cheked or no
	 */
	function checkIfAllCheckboxChecked()
	{
		var checkall = document.getElementsByName('inputcheckall');
		var allCheckBoxList = document.getElementsByName('checkDistList');
		if(allCheckBoxList.length!=0){
			var test = true;
			for (var i = 0; i < allCheckBoxList.length; i++) {
				if(allCheckBoxList[i].type=="checkbox"){
			      if(allCheckBoxList[i].checked==false){
			       test = false
			      }
			    }
			}
			if(test == true){
				  checkall[0].setAttribute("checked", "true");
				  checkall[0].checked = true;
			}else{
				checkall[0].setAttribute("checked", "false");
				  checkall[0].checked = false;
				}
		}else{
			checkall[0].disabled = 'true';
			}
	}

</SCRIPT>
</head>

<div bgcolor="#E8F3FE" style="position:absolute;  left:20px; top:2px; width:720px; height:10px;">
		<div bgcolor="#E8F3FE" id="DistributionListTitle" style="position:absolute;z-index:99;left:1px; top:5px; width:720px; height:28px;" >
			<table id="tableTitle" width="720px" border="0" cellspacing="1" cellpadding="0"  height="28" >
				<tr >
						<td width="200" height="28px" align="left"><b>${roleId} <s:text name="scenario.distributionlist.tilte"/></b></td>
				</tr>
			</table>
		</div>
</div>
	
<s:form action="scenMaintenance.do" onsubmit="return validate(this);">
<input name="method" type="hidden" value="display">
<input name="hostId" type="hidden" value="${hostId}">
<input name="entityId" type="hidden" value="${entityId}">
<input name="roleId" type="hidden" value="${roleId}">
<input name="scenarioID" type="hidden" value="${scenarioID}">
				
<body onmousemove="reportMove()" leftmargin="0" topmargin="0" marginheight="0" onLoad="setParentChildsFocus();bodyOnLoad();setFocus(document.forms[0]);ShowErrMsgWindow('${actionError}');" onunload="call()">
<div id="DistributionList" style="position:absolute; border:2px outset; left:20px; top:35px; width:720px; height:336px; border-left:0px outset;" color="#7E97AF">
	<div id="DistributionList" style="position:absolute;z-index:99;left:0px; top:0px; width:700px; height:10px;">
		<table class="sort-table" id="table_2" width="700px" border="0" cellspacing="1" cellpadding="0"  height="20px">
			<thead>
				<tr height="20px" >
					<td width="148px" title='<s:text name="tooltip.sortUserId"/>' class="topbar" style="border-left-width: 0px;"><b><s:text name="usermaintenance.userId"/></b></td>
					<td width="207px" title='<s:text name="tooltip.sortUserName"/>' class="topbar"><b><s:text name="usermaintenance.userName"/></b></td>
					<td width="297px" title='<s:text name="tooltip.sendMail"/>' class="topbar"><b><s:text name="usermaintenance.emailId"/></b></td>
					<td width="48px" title='<s:text name="auditLog.to"/>' class="topbar"><b><s:text name="auditLog.to"/></b></td>
				</tr>
			</thead>
		</table>
	</div>
	<div id="ddscrolltable" style="position:absolute; left:0px; top:0px; width:716px; height:331px; overflow-x:hidden;">
		<div id="Role" style="position:absolute;z-index:99;left:0px; width:700px; height:10px;">
			<table id="usersDistributionList" class="sort-table" width="700px" border="0" cellspacing="1" cellpadding="0" height="309px">
				<tbody> 		
					<%int count = 0; %>
					<s:iterator value ="#request.usersDistributionList" var="usersDistributionList">          
						<% if( count%2 == 0 ) {%><tr class="even"><% }else  { %> <tr class="odd"> <%}++count; %>
								<s:hidden name="entityId"/>
								<td width="148px"><s:property value="user_id"/>&nbsp;</td>
								<td width="207px"><s:property value="user_name"/>&nbsp;</td>
								<td width="297px"><s:property value="email_address"/>&nbsp;</td>
								
								<td width="48" height="20" align="center">
								 <s:if test='"Y"== #usersDistributionList.checked'>
									<input type="checkbox" value='<s:property value="user_id"/>' name="checkDistList" onclick='handleClick(this);' checked title='<s:text name="tooltip.selectUsers"/>'>
								</s:if>
								<s:else>
									<input type="checkbox" value='<s:property value="user_id"/>' name="checkDistList" onclick='handleClick(this);'  title='<s:text name="tooltip.selectUsers"/>'>
								</s:else>
							&nbsp;</td>
						</tr>
					</s:iterator>
				</tbody>
				<tfoot><tr><td colspan="5" ></td></tr></tfoot>
			</table>
		</div>
	</div>
</div>


<div style="position:absolute; left:20px; top:370px; width:686px; height:25px; visibility:visible;">
	<table width="686px" border="0" cellspacing="0" cellpadding="0" height="25">
		<tr>
			<td width="508" align="left">
			</td>
			<td width="140" align="left">
				<b><s:text name="scenario.distributionlist.selectall"/></b> 
			</td>
			<td width="14" align="left">
				<input type="checkbox" name="inputcheckall"  onclick ="selectedallInput(this);" title='<s:text name="tooltip.selectAllUsers"/>'>	
			</td>
		</tr>
	</table>
</div>
	
<div id="DistributionListHelp" style="left: 640px; top: 408px; width: 70px; height: 29px; visibility: visible; position: absolute; z-index: 150;">
	<table width="60" height="20" border="0" cellspacing="0" cellpadding="0">
		<tbody>
			<tr>
				<td align="Right">
					<a tabindex="10" title="Help screen content" onmouseover="MM_swapImage('Help','','images/help_default.GIF ',1)" onmouseout="MM_swapImgRestore()" onclick="javascript:openWindow(buildPrintURL('print','Distribution List'),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')" href="#"><img name="Help" src="images/help_default.GIF " border="0"></a> 
				</td>
				<td align="right" id="Print">&nbsp;
					<a tabindex="11" onmouseover="MM_swapImage('Print','','images/Print_R.gif ',1)" onmouseout="MM_swapImgRestore()" onclick="printPage();"><img name="Print" title="Print screen content" src="images/Print.gif " border="0"></a>	
				</td>
			</tr>
		</tbody>
	</table>
</div>
<div id="ddimagebuttons" color="#7E97AF" style="position:absolute; border:2px outset; left:20; top:400; width:720px; height:39px; visibility:visible;">
	<div id="ScenarioMaintenance" style="position:absolute; left:2; top:4; width:200px; height:15px; visibility:visible;">
		<table width="140" border="0" cellspacing="0" cellpadding="0" height="20">
			<tr>
				<td id="savebutton">
					<a title='<s:text name="tooltip.ok"/>' tabindex="9" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onclick="javascript:submitForm();"><s:text name="button.ok"/></a>			
				</td>
				<td id="cancelbutton" width="70px">		
					<a title='<s:text name="tooltip.cancel"/>' tabindex="9" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onclick="confirmClose('P');"><s:text name="button.cancel"/></a>			
				</td>
			</tr>
		</table>
	</div>
</div>
<blockquote>&nbsp;</blockquote>
		<p>&nbsp;</p>
<script type="text/javascript">
</script>
</s:form>
</body>
</html>