<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@ include file="/taglib.jsp"  %>
<%@ page import="org.swallow.util.SwtConstants" %>
<html>
<head>

<s:if test='"save" == #request.methodName' >
	<title><s:text name="workqueueaccess.addScreen"/></title>
</s:if>

<s:if test='"update" == #request.methodName' >
	<title><s:text name="workqueueaccess.changeScreen"/></title>
</s:if>

<s:if test='"view" == #request.methodName' >
	<title><s:text name="workqueueaccess.viewScreen"/></title>
</s:if>

<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">

<SCRIPT language="JAVASCRIPT">

<s:if test='"yes" == #request.parentFormRefresh' >
window.opener.document.forms[0].method.value="displayDetails";
window.opener.document.forms[0].submit();
self.close();
</s:if>

function submitForm(methodName){
if(methodName == 'displayCurrencyList'){
		document.forms[0].method.value = methodName;
	document.forms[0].submit();
	}
 else
 if(validateForm(document.forms[0]) ){
	if(checkQueueStatus())
	{
		enableFields();
		document.forms[0].method.value = methodName;
		document.forms[0].submit();
	}else
	{
		alert('<s:text name="workqueueaccess.alert.access"/>');
	}
 }
}
function enableFields(){
	document.forms[0].elements["workQAccess.id.entityId"].disabled = "";
	document.forms[0].elements["workQAccess.id.currencyCode"].disabled = "";
}

function bodyOnLoad()
{
	//var entitydropBox = new SwSelectBox(document.forms[0].elements["workQAccess.id.entityId"],document.getElementById("entityDesc"));
	<s:if test='"save" == #request.methodName' >
		var currencydropBox = new SwSelectBox(document.forms[0].elements["workQAccess.id.currencyCode"],document.getElementById("currencyDesc"));
	</s:if>

	<s:if test='"save" != #request.methodName' >
	document.getElementById("currencyName").innerText = '${selectedCurrencyName}';
	</s:if>
	document.getElementById("entityName").innerText = '${entityName}';

	<s:if test='"yes" == #request.queueAccessExiting' >
		alert('<s:text name="workqueueaccess.recordPresent"/>');
	</s:if>

}

function validateForm(objForm){
  var elementsRef = new Array(2);
 
  elementsRef[0] = objForm.elements["workQAccess.id.entityId"]; 
  elementsRef[1] = objForm.elements["workQAccess.id.currencyCode"];
  return validate(elementsRef);
}

function checkQueueStatus()
{
		    var checkBoxArray = new Array(18);
			checkBoxArray[0]= document.forms[0].elements['workQAccess.offerMatchQualA'];
			checkBoxArray[1]= document.forms[0].elements['workQAccess.offerMatchQualB'];
			checkBoxArray[2]= document.forms[0].elements['workQAccess.offerMatchQualC'];
			checkBoxArray[3]= document.forms[0].elements['workQAccess.offerMatchQualD'];
			checkBoxArray[4]= document.forms[0].elements['workQAccess.offerMatchQualE'];

			checkBoxArray[5]= document.forms[0].elements['workQAccess.suspMatchQualA'];
			checkBoxArray[6]= document.forms[0].elements['workQAccess.suspMatchQualB'];
			checkBoxArray[7]= document.forms[0].elements['workQAccess.suspMatchQualC'];
			checkBoxArray[8]= document.forms[0].elements['workQAccess.suspMatchQualD'];
			checkBoxArray[9]= document.forms[0].elements['workQAccess.suspMatchQualE'];
			checkBoxArray[10]= document.forms[0].elements['workQAccess.suspMatchQualZ'];

			checkBoxArray[11]= document.forms[0].elements['workQAccess.confirmMatchQualA'];
			checkBoxArray[12]= document.forms[0].elements['workQAccess.confirmMatchQualB'];
			checkBoxArray[13]= document.forms[0].elements['workQAccess.confirmMatchQualC'];
			checkBoxArray[14]= document.forms[0].elements['workQAccess.confirmMatchQualD'];
			checkBoxArray[15]= document.forms[0].elements['workQAccess.confirmMatchQualE'];
			checkBoxArray[16]= document.forms[0].elements['workQAccess.confirmMatchQualZ'];

			checkBoxArray[17]= document.forms[0].elements['workQAccess.outstanding'];
			
			for (var i = 0; i <= 17; i++) {
				if(checkBoxArray[i].checked)
				return true;
			 }
	return false;		
		
}
</SCRIPT>

<s:form action="workQAccess.do" onsubmit="return validate(this);">
<input name="method" type="hidden" value="display">

<body leftmargin="0" topmargin="0" marginheight="0" onLoad="setParentChildsFocus();setFocus(document.forms[0]);bodyOnLoad(); ShowErrMsgWindow('${actionError}');" onunload="call()">

<div id="WorkQueueAccessAdd" style="position:absolute; left:20px; top:20px; width:500px; height:59px; border:2px outset;" color="#7E97AF">
	<div id="WorkQueueAccessAdd" style="position:absolute;z-index:99;left:8px; top:4px; width:430px; height:20px;">
		<table width="488px" border="0" cellpadding="0" cellspacing="0" height="48px">
			<tr height="23px">
			  <td width="40px"><b><s:text name="role.workQueueAccess.entity1"/></b></td>
			  <td width="28px">&nbsp;</td>
			  <td width="120px">
				<s:if test='"save" == #request.methodName' >
				   <s:textfield name="workQAccess.id.entityId" cssClass="htmlTextAlpha"  cssStyle="width:120px;" disabled="true" /></td>
				</s:if>
				<s:if test='"update" == #request.methodName' > 
				  <s:textfield name="workQAccess.id.entityId" cssClass="htmlTextAlpha"  cssStyle="width:120px;" disabled="true" /></td>
				</s:if>  
				<s:if test='"view" == #request.methodName' >
				   <s:textfield name="workQAccess.id.entityId" cssClass="htmlTextAlpha"  cssStyle="width:120px;" disabled="true" /></td>
				</s:if>  
			  </td>
			  <td width="20px">&nbsp;</td>
			  <td width="280px">
					<span id="entityName" name="entityName" class="spantext">
			  </td>
		  </tr>
		  <tr>
					
          			 <s:if test='"view" == #request.methodName' >
						   <td width="40px"><b><s:text name="role.workQueueAccess.currency1"/></b></td>
					</s:if>
					 <s:if test='"save" == #request.methodName' >
						   <td width="40px"><b><s:text name="role.workQueueAccess.currency1"/></b>*</td>
					</s:if>
					 <s:if test='"update" == #request.methodName' >
						   <td width="40px"><b><s:text name="role.workQueueAccess.currency1"/></b></td>
					</s:if>
			
			  <td width="28px">&nbsp;</td>
			  <td width="120px">
				<s:if test='"save" == #request.methodName' >
				  <s:select tabindex="2" titleKey="tooltip.selectCurrencyId" id="workQAccess.id.currencyCode" name="workQAccess.id.currencyCode" disabled="%{#attr.screenFieldsStatus}" cssStyle="width:55px" list="#request.currencyMaster" listKey="value" listValue="label" />
				  
				  </s:if>
				  <s:if test='"update" == #request.methodName' > 
				   <s:textfield name="workQAccess.id.currencyCode" cssClass="htmlTextAlpha" titleKey="tooltip.selectCurrencyId"  cssStyle="width:38px;" disabled="true" />
					
				  </s:if>  
				  <s:if test='"view" == #request.methodName' >
				   <s:textfield name="workQAccess.id.currencyCode" cssClass="htmlTextAlpha" titleKey="tooltip.selectCurrencyId" cssStyle="width:38px;" disabled="true" />
					
				  </s:if>  
			  </td>
			  <td width="20px">&nbsp;</td>
			  <td width="280px">

				<s:if test='"save" == #request.methodName' >
					<span id="currencyDesc" name="currencyDesc" class="spantext">
				 </s:if>  


			<s:if test='"save" != #request.methodName' >
				<span id="currencyName" name="currencyName" class="spantext">
			 </s:if>  


			  </td>
			 </tr>
		</table>
	</div>
</div>
<div id="WorkQueueAccessAdd" style="position:absolute; left:20px; top:83px; width:500px; height:110px; border:2px outset;" color="#7E97AF">
<div id="WorkQueueAccessAddCheckBox" style="position:absolute;z-index:99;left:0px; top:0px; width:496px; height:120px;">
<table id = checkBoxTable  bgcolor="#DEDFE0" width="496px" border="0" cellspacing="1" cellpadding="0">
	<thead>
		<tr height="20px">
			<td class="topbar" title="<s:text name="tooltip.workQueueAccess"/>"><b><s:text name="role.workQueueAccess.matchStatus"/></b></td>
			<td class="topbar" title='<s:text name="tooltip.qualityA"/>'><b><s:text name="role.workQueueAccess.qualityA"/></b></td>
			<td class="topbar" title='<s:text name="tooltip.qualityB"/>'><b><s:text name="role.workQueueAccess.qualityB"/></b></td>
			<td class="topbar" title='<s:text name="tooltip.qualityC"/>'><b><s:text name="role.workQueueAccess.qualityC"/></b></td>
			<td class="topbar" title='<s:text name="tooltip.qualityD"/>'><b><s:text name="role.workQueueAccess.qualityD"/></b></td>
			<td class="topbar" title='<s:text name="tooltip.qualityE"/>'><b><s:text name="role.workQueueAccess.qualityE"/></b></td>
			<td class="topbar" title='<s:text name="tooltip.qualityZ"/>'><b><s:text name="role.workQueueAccess.qualityZ"/></b></td>
			<td class="topbar">&nbsp;</td>
		</tr>
		<tr height="17px" class="even">
			<td align="left"><b><s:text name="role.workQueueAccessAdd.offered"/></b></td>
			  <td align="center"><s:checkbox tabindex="3" id ="1" name="workQAccess.offerMatchQualA" fieldValue="Y" value='%{#request.workQAccess.offerMatchQualA == "Y"}' disabled="%{#attr.screenFieldsStatus}"  /></td>
			  <td align="center"><s:checkbox tabindex="4" id ="2"  name="workQAccess.offerMatchQualB" fieldValue="Y" value='%{#request.workQAccess.offerMatchQualB == "Y"}' disabled="%{#attr.screenFieldsStatus}" /></td>
			   <td align="center"><s:checkbox tabindex="5" id="3"  name="workQAccess.offerMatchQualC" fieldValue="Y" value='%{#request.workQAccess.offerMatchQualC == "Y"}' disabled="%{#attr.screenFieldsStatus}" /></td>
			  <td align="center" ><s:checkbox tabindex="6" id="4"   name="workQAccess.offerMatchQualD" fieldValue="Y" value='%{#request.workQAccess.offerMatchQualD == "Y"}' disabled="%{#attr.screenFieldsStatus}"  /></td>
			  <td align="center"><s:checkbox tabindex="7" id="5"   name="workQAccess.offerMatchQualE" fieldValue="Y" value='%{#request.workQAccess.offerMatchQualE == "Y"}' disabled="%{#attr.screenFieldsStatus}"/></td>
			  <td align="center">&nbsp;</td>
			  <td>&nbsp;</td>
		</tr>
		<tr height="17px" class="odd">
		  <td ><b><s:text name="role.workQueueAccessAdd.suspended"/></b></td>
          <td align="center"><s:checkbox tabindex="8"  name="workQAccess.suspMatchQualA" fieldValue="Y" value='%{#request.workQAccess.suspMatchQualA == "Y"}' disabled="%{#attr.screenFieldsStatus}"/></td>
          <td align="center"><s:checkbox tabindex="9"  name="workQAccess.suspMatchQualB" fieldValue="Y" value='%{#request.workQAccess.suspMatchQualB == "Y"}' disabled="%{#attr.screenFieldsStatus}"/></td>
		  <td align="center"><s:checkbox tabindex="10"  name="workQAccess.suspMatchQualC" fieldValue="Y" value='%{#request.workQAccess.suspMatchQualC == "Y"}' disabled="%{#attr.screenFieldsStatus}"/></td>
          <td align="center"><s:checkbox tabindex="11"  name="workQAccess.suspMatchQualD" fieldValue="Y" value='%{#request.workQAccess.suspMatchQualD == "Y"}' disabled="%{#attr.screenFieldsStatus}"/></td>
		  <td align="center"><s:checkbox tabindex="12"  name="workQAccess.suspMatchQualE" fieldValue="Y" value='%{#request.workQAccess.suspMatchQualE == "Y"}' disabled="%{#attr.screenFieldsStatus}"/></td>
          <td align="center"><s:checkbox tabindex="13"  name="workQAccess.suspMatchQualZ" fieldValue="Y" value='%{#request.workQAccess.suspMatchQualZ == "Y"}' disabled="%{#attr.screenFieldsStatus}"/></td>
          <td >&nbsp;</td>
	   </tr>

		<tr height="17px" class="even">
		  <td ><b><s:text name="role.workQueueAccessAdd.confirmed"/></b></td>
          <td align="center"><s:checkbox tabindex="14"  name="workQAccess.confirmMatchQualA" fieldValue="Y" value='%{#request.workQAccess.confirmMatchQualA == "Y"}' disabled="%{#attr.screenFieldsStatus}"/></td>
          <td align="center"><s:checkbox tabindex="15"  name="workQAccess.confirmMatchQualB" fieldValue="Y" value='%{#request.workQAccess.confirmMatchQualB == "Y"}' disabled="%{#attr.screenFieldsStatus}"/></td>
		  <td align="center"><s:checkbox tabindex="16"  name="workQAccess.confirmMatchQualC" fieldValue="Y" value='%{#request.workQAccess.confirmMatchQualC == "Y"}' disabled="%{#attr.screenFieldsStatus}"/></td>
          <td align="center"><s:checkbox tabindex="17"  name="workQAccess.confirmMatchQualD" fieldValue="Y" value='%{#request.workQAccess.confirmMatchQualD == "Y"}' disabled="%{#attr.screenFieldsStatus}"/></td>
		  <td align="center"><s:checkbox tabindex="18"  name="workQAccess.confirmMatchQualE" fieldValue="Y" value='%{#request.workQAccess.confirmMatchQualE == "Y"}' disabled="%{#attr.screenFieldsStatus}"/></td>
          <td align="center"><s:checkbox tabindex="19"  name="workQAccess.confirmMatchQualZ" fieldValue="Y" value='%{#request.workQAccess.confirmMatchQualZ == "Y"}' disabled="%{#attr.screenFieldsStatus}"/></td>
          <td >&nbsp;</td>
	   </tr>
		<tr height="17px" class="odd">
		  <td ><b><s:text name="role.workQueueAccessAdd.outstanding"/></b></td>
          <td align="center">&nbsp;</td>
          <td align="center">&nbsp;</td>
		   <td align="center">&nbsp;</td>
          <td align="center">&nbsp;</td>
		  <td align="center">&nbsp;</td>
		  <td align="center">&nbsp;</td>
          <td align="center"><html:checkbox tabindex="20" property="workQAccess.outstanding"   value='Y' 
		  disabled="%{#attr.screenFieldsStatus}"/></td>
	   </tr>
	</thead>
</table>
</div>
</div>
<div id="WorkQueueAccessAdd" style="position:absolute; left:1px; top:206; width:500px; height:15px; visibility:visible;">
	<table width="505px" border="0" cellspacing="0" cellpadding="0" height="20px">
		<tr>
			<td align="right" id="Print">
				<a tabindex="24" onclick="printPage();" onMouseOut="MM_swapImgRestore()" onMouseOver="MM_swapImage('Print','','images/Print_R.gif ',1)"><img src="images/Print.gif " name="Print"  border="0" title='<s:text name="tooltip.printScreen"/>'></a>	
			</td>
		</tr>
	</table>
</div>
<div id="ddimagebuttons" color="#7E97AF" style="position:absolute; border:2px outset; left:20; top:197px; width:500px; height:39px; visibility:visible;">
<div id="WorkQueueAccessAdd" style="position:absolute; left:6; top:4; width:150px; height:15px; visibility:visible;">
  <table width="140" border="0" cellspacing="0" cellpadding="0" height="20">
   <s:if test='"view" != #request.viewButtonStatus' >
	<tr>
	 <!-- Save Button  -->
		  <td title='<s:text name="tooltip.ok"/>'>
		  <a onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" tabindex="21" onClick="javascript:submitForm('${methodName}');"><s:text name="button.ok"/></a>
		  </td>

	 <!-- Cancel Button  -->
		  <td title='<s:text name="tooltip.cancel"/>'>						 
			<a onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" tabindex="22" onclick="javascript:window.close();"><s:text name="button.cancel"/></a>
		  </td>
		</tr>

	</s:if>
	 <s:if test='"view" == #request.viewButtonStatus' >
	 <tr>
	 <!-- View Button  -->
	<td id="closebutton" title='<s:text name="tooltip.close"/>'>
		<a tabindex="23" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onclick="javascript:window.close();"><s:text name="button.close"/></a></td>
		</tr>
	 </s:if>

	</table>
  </div>
</div>
<blockquote>&nbsp;</blockquote>
		<p>&nbsp;</p>
<script type="text/javascript">
</script>

</s:form>
</body>
</html>