<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@ include file="/taglib.jsp"  %>
<%@ page import="org.swallow.util.SwtConstants" %>
<html>
<head>
<title><s:text name="sweepinglimits.mainScreen"/></title>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">

<SCRIPT language="JAVASCRIPT">

function disableAllButtons()
{
	<s:if test='"yes" == #request.isViewRole' >		
	document.getElementById("viewbutton").innerHTML = document.getElementById("viewdisablebutton").innerHTML; 	
	</s:if>

	<s:if test='"yes" != #request.isViewRole' >	
	document.getElementById("changebutton").innerHTML = document.getElementById("changedisablebutton").innerHTML;
	document.getElementById("viewbutton").innerHTML = document.getElementById("viewdisablebutton").innerHTML; 
	document.getElementById("deletebutton").innerHTML = document.getElementById("deletedisablebutton").innerHTML; 
	</s:if>



}

function onFilterandSort()
{
	updateColors();
	disableAllButtons();
}

function bodyOnLoad()
{
	xl = new XLSheet("sweepLimitDetails","table_2", ["String","String"],"22");
	xl.onsort = xl.onfilter = onFilterandSort;
	highlightTableRows("sweepLimitDetails");

	var cancelcloseElements = new Array(1);
	cancelcloseElements[0] = "closebutton";

	<s:if test='"yes" == #request.isViewRole' >	
		document.getElementById("viewbutton").innerHTML = document.getElementById("viewdisablebutton").innerHTML;	
	</s:if>

	<s:if test='"yes" != #request.isViewRole' >
		document.getElementById("changebutton").innerHTML = document.getElementById("changedisablebutton").innerHTML;
		document.getElementById("viewbutton").innerHTML = document.getElementById("viewdisablebutton").innerHTML;
		document.getElementById("deletebutton").innerHTML = document.getElementById("deletedisablebutton").innerHTML;
	</s:if>

}

function submitForm(methodName){
 var yourstate=window.confirm('<s:text name="confirm.delete"/>')
	if (yourstate==true)
	{ //Boolean variable. Sets to true if user pressed "OK" versus "Cancel."
		document.forms[0].method.value = methodName;	
		document.forms[0].submit();
	}
}

function buildAddSweepLimit(methodName){
	var param = 'sweepLimits.do?method='+methodName+'&selectedCurrencyCode=';
	param += document.forms[0].selectedCurrencyCode.value;	
	
	param += '&selectedSweepLimit=' + document.forms[0].selectedSweepLimit.value;
	return  param;

}

function onSelectTableRow(rowElement, isSelected)
{
	document.forms[0].selectedCurrencyCode.value = rowElement.cells[0].innerText.trim();
	document.forms[0].selectedSweepLimit.value = rowElement.cells[1].innerText.trim();

	var count = getCountRowsSelected(rowElement);
	if(count ==1)
	{
		<s:if test='"yes" == #request.isViewRole' >	
		document.getElementById("viewbutton").innerHTML = document.getElementById("viewenablebutton").innerHTML;		
		</s:if>
		<s:if test='"yes" != #request.isViewRole' >
		document.getElementById("changebutton").innerHTML = document.getElementById("changeenablebutton").innerHTML;
		document.getElementById("viewbutton").innerHTML = document.getElementById("viewenablebutton").innerHTML;
		document.getElementById("deletebutton").innerHTML = document.getElementById("deleteenablebutton").innerHTML;
		</s:if>
	}
	else
	{
	   <s:if test='"yes" == #request.isViewRole' >	
		document.getElementById("viewbutton").innerHTML = document.getElementById("viewdisablebutton").innerHTML;		
		</s:if>
		<s:if test='"yes" != #request.isViewRole' >
		document.getElementById("changebutton").innerHTML = document.getElementById("changedisablebutton").innerHTML;
		document.getElementById("viewbutton").innerHTML = document.getElementById("viewdisablebutton").innerHTML;
		document.getElementById("deletebutton").innerHTML = document.getElementById("deletedisablebutton").innerHTML;
		</s:if>
	}
}

</SCRIPT>
</head>

<s:form action="sweepLimits.do">
<input name="method" type="hidden" value="display">
  
<input name="selectedCurrencyCode" type="hidden">
<input name="selectedSweepLimit" type="hidden" > 

<body onmousemove="reportMove()" leftmargin="0" topmargin="0" marginheight="0" onLoad="setParentChildsFocus();setFocus(document.forms[0]);bodyOnLoad();ShowErrMsgWindow('${actionError}');" onunload="call()">

<div id="sweepLimit" style="position:absolute; left:20px; top:20px; width:450px; height:375px; border:2px outset;" color="#7E97AF">
	<div id="sweepLimit" style="position:absolute;z-index:99;left:0px; top:0px; width:428px; height:10px;">
		<table  class="sort-table" id="table_2" bgcolor="#B0AFAF" width="433px" border="0" cellspacing="1" cellpadding="0"  height="20px">
			<thead>
				<tr height="20px">
					<td width="140px" title='<s:text name="tooltip.sortCurrencyCode"/>'><b><s:text name="role.sweepLimits.currencyCode"/></b></td>
					<td width="285px" title='<s:text name="tooltip.sortSweepLimit"/>'><b><s:text name="role.sweepLimits.sweepLimit"/></b></td>
				</tr>
			</thead>
		</table>
	</div>
	<div id="ddscrolltable"  style="position:absolute; left:0px; top:0px; width:445px; height:371px;overflow-x:hidden">
		<div id="sweepLimit" style="position:absolute;z-index:99;left:0px; top:21px; width:433px; height:10px;">
			<table class="sort-table" id="sweepLimitDetails" width="428px" border="0" cellspacing="1" cellpadding="0" height="347px">
				<tbody> 		
					<%int count = 0; %>  
					<s:iterator value="#request.sweepLimitDetails" var="sweepLimitDetails" >          
						<% if( count%2 == 0 ) {%><tr class="even"><% }else  { %> <tr class="odd"> <%}++count; %>
							<s:hidden name="#sweepLimitDetails.id.currencyCode"/>
							<td width="140px" align="center"><s:property value="#sweepLimitDetails.id.currencyCode" />&nbsp;</td>
							<td width="285px" align="right"><s:property value="#sweepLimitDetails.sweepLimitAsString" />&nbsp;</td>
						</tr>
					</s:iterator>  
				</tbody>
				<tfoot><tr><td colspan="2" ></td></tr></tfoot>
			</table>
		</div>
	</div>
</div>
<div id="sweepLimit" style="position:absolute; left:400; top:408px; width:70px; height:15px; visibility:visible;">
	<table width="60px" border="0" cellspacing="0" cellpadding="0" height="20">
		<tr>

		<td align="Right">
				<a tabindex="6" href=# onclick="javascript:openWindow(buildPrintURL('print','Sweeping Limits '),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')" onMouseOut="MM_swapImgRestore()" onMouseOver="MM_swapImage('Help','','images/help_default.GIF ',1)"><img src="images/help_default.GIF " name="Help"  border="0" title='<s:text name="tooltip.helpScreen"/>' ></a> 
		</td>

			<td align="right" id="Print">
				<a tabindex="6" onclick="printPage();" onMouseOut="MM_swapImgRestore()" onMouseOver="MM_swapImage('Print','','images/Print_R.gif ',1)"><img src="images/Print.gif " name="Print"  border="0" title='<s:text name="tooltip.printScreen"/>'></a>	
			</td>
		</tr>
	</table>
</div>
<div id="ddimagebuttons" color="#7E97AF" style="position:absolute; border:2px outset; left:20; top:399px; width:450px; height:39px; visibility:visible;">
<div id="sweepLimit" style="position:absolute; left:6; top:4; width:360px; height:15px; visibility:visible;">
	<s:if test='"yes" == #request.isViewRole' >
	<table width="140px" border="0" cellspacing="0" cellpadding="0" height="20">
		<tr>
				<td width="70px" id="viewbutton"></td>

				<td width="70px" id="closebutton">
					<a title='<s:text name="tooltip.close"/>' tabindex="5" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onclick="confirmClose('P');"><s:text name="button.close"/></a>
				</td>
				</tr>
				</table>
			</s:if>

			<s:if test='"yes" != #request.isViewRole' >
			<table width="350px" border="0" cellspacing="0" cellpadding="0" height="20">
			<tr >
				<td id="addbutton" width="70px" >
					<a title='<s:text name="tooltip.addSwpLmtCurr"/>' tabindex="1" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onClick="javascript:openWindow(buildAddSweepLimit('add'),'sweeplimitsaddWindow','left=50,top=190,width=618,height=143,toolbar=0, resizable=yes, status=yes, scrollbars=yes','true')"><s:text name="button.add"/></a>
				</td>
				<td id="changebutton" width="70px" ></td>
				<td id="viewbutton" width="70px" ></td>
				<td id="deletebutton" width="70px" ></td>
				<td id="closebutton" width="70px" >
					<a title='<s:text name="tooltip.close"/>' tabindex="5" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onclick="confirmClose('P');"><s:text name="button.close"/></a>
				</td>
				</tr>
				</table>
			</s:if>
		
	
</div>

<div style="position:absolute; left:6; top:4; width:360px; height:15px; visibility:hidden;">
    <table  border="0" cellspacing="0" cellpadding="0" height="20" style="visibility:hidden">
	<tr>
		<td id="changeenablebutton">
			<a title='<s:text name="tooltip.ChngSwpLmtCurr"/>' tabindex="2" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onClick="javascript:openWindow(buildAddSweepLimit('change'),'sweeplimitschangeWindow','left=50,top=190,width=618,height=142,toolbar=0, resizable=yes, status=yes, scrollbars=yes','true')"><s:text name="button.change"/></a>
		</td>
		<!-- Start:Based on Mantis 1549 modified  by sunil.The purpose added Meaningful tooltip related actions  -->
		<td id="changedisablebutton">
			<a class="disabled" disabled="disabled" title='<s:text name="tooltip.ChngSwpLmtCurr"/>'><s:text name="button.change"/></a>
		</td>

		<td id="viewenablebutton">
			<a title='<s:text name="tooltip.viewSwpLmtCurr"/>' tabindex="3" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onClick="javascript:openWindow(buildAddSweepLimit('view'),'sweeplimitsviewWindow','left=50,top=190,width=600,height=140,toolbar=0, resizable=yes, status=yes, scrollbars=yes','true')"><s:text name="button.view"/></a>
		</td>
		<td id="viewdisablebutton">
			<a class="disabled" disabled="disabled" title='<s:text name="tooltip.viewSwpLmtCurr"/>'><s:text name="button.view"/></a>
		</td>

		<td id="deleteenablebutton">
			<a title='<s:text name="tooltip.delSwpLmtCurr"/>' tabindex="4" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onClick="javascript:submitForm('delete')"><s:text name="button.delete"/></a>
		</td>
		<td id="deletedisablebutton">
			<a  class="disabled" disabled="disabled" title='<s:text name="tooltip.delSwpLmtCurr"/>'><s:text name="button.delete"/></a>
		</td>
<!--End :Based on Mantis 1549 modified  by sunil.The purpose added Meaningful tooltip related actions  -->		

		
	</tr>
    </table>
  </div>
</div>
<blockquote>&nbsp;</blockquote>
		<p>&nbsp;</p>

</s:form>
</body>
</html>