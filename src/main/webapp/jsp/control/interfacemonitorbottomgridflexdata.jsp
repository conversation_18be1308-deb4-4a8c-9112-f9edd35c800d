<?xml version="1.0" encoding="UTF-8"?>
<!--
  - The main purpose of this JSP file is to load the resultant XML data for Interface Monitor screen.
  - Interface Monitor screen consists of two data grids (i) Top Grid and (ii) Bottom Grid
  - The top grid shows the List of Interfaces, their enabled status, Total messages:Processed, Awaiting, Filtered and Bad.
  - The bottom grid shows the detailed information of the selected Interface.
  - This JSP is implicitly acts as XML to load data in the Bottom Grid in the Interface Monitor screen.
  - 
  - Author(s): Marshal .I
  - Date: 20-June-2011
  -->
  
<%@ page contentType="text/xml" %>
<%@ taglib prefix="s" uri="/struts-tags" %>


<!-- Defining bean for Mapping the Column field with the property -->
<s:set var="bottomRecord" value="#request.bottomGridInfo.size()"/>
<!-- Defining Root tag -->
<interfacemonitor>
	<!-- Defining XML status here -->
	<request_reply>
		<status_ok><s:property value="#request.reply_status_ok" /></status_ok>
		<message><s:property value="#request.reply_message" /></message>
		<location />
	
	<!-- Definging opTimer -->
	<timing>
	<s:iterator value="#request.opTimes" var="opTime" >
		<operation id="${opTime.key}">${opTime.value}</operation>
	</s:iterator>
	</timing>
	</request_reply>
	
	<!-- Defining main grid -->
	<grid>
		<metadata>
			<!-- Defining columns -->
			<columns>
				<s:iterator value="#request.column_order_bottom" var="columnOrder" >
					<!-- Defining Interface ID -->
					<s:if test='"interfaceid" == #request.columnOrder' >
						<column
						heading="<s:text name="label.interfaceMonitor.header.interfaceId"/>"
						tooltip = "<s:text name="tooltip.interfaceMonitor.interfaceId"/>"
						draggable="false" 
						filterable="true" 
						type="str" 
						sort="true"
						dataelement="interfaceid"
						width="150" />
					</s:if>
					<!-- Defining Message Type -->
					<s:if test='"messageType" == #request.columnOrder' >
						<column
						heading="<s:text name="label.interfaceMonitor.bottomGrid.header.msgType"/>"
						tooltip="<s:text name="tooltip.interfaceMonitor.messageType"/>"
						draggable="true" 
						filterable="true" 
						type="str"
						gridtype="bottom"
						sort="true" 
						dataelement="messageType"
						width="200" />
					</s:if>
					<!-- Defining Message Status -->
					<s:if test='"messageStatus" == #request.columnOrder' >
						<column 
						heading="<s:text name="label.interfaceMonitor.bottomGrid.header.msgStatus"/>"
						tooltip="<s:text name="tooltip.interfaceMonitor.messageStatus"/>"
						draggable="true"
						filterable="true"
						type="str"
						gridtype="bottom"
						sort="true"
						dataelement="messageStatus"
						width="200" />
					</s:if>
					<!-- Defining Last Execution -->
					<s:if test='"lastExecution" == #request.columnOrder' >
						<column 
						heading="<s:text name="label.interfaceMonitor.bottomGrid.header.lastExecution"/>"
						tooltip="<s:text name="tooltip.interfaceMonitor.lastExecution"/>"
						draggable="true"
						filterable="true"
						type="num"
						gridtype="bottom"
						sort="true"
						dataelement="lastExecution"
						width="170" />
					</s:if>
				</s:iterator>
			</columns>
		</metadata>
		<!-- Defining Rows -->
		<rows size="${bottomRecord}"> 
			<s:iterator value="#request.bottomGridInfo" var="bottomOrder" >
				<row>
					<!-- Defining Interface ID -->
					<interfaceid clickable="false">
						<s:property value="#bottomOrder.interfaceId" />
					</interfaceid>
					<!-- Defining Message Type -->
					<messageType clickable="false">
						<s:property value="#bottomOrder.messageType" />
					</messageType>
					<!-- Defining Message Status -->
					<messageStatus clickable="false">
						<s:property value="#bottomOrder.messageStatus" />
					</messageStatus>
					<!-- Defining Last Execution -->
					<lastExecution clickable="false">
						<s:property value="#bottomOrder.lastRecievedDate" />
					</lastExecution>
				</row>
			</s:iterator> 
		</rows>
	</grid>
</interfacemonitor>