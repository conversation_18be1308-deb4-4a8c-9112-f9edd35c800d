<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@ include file="/taglib.jsp"  %>
<%@ page import="org.swallow.util.SwtConstants" %>
<html>
<head>
<title>
<s:if test='"add" == #request.methodName' >
	<s:text name="addJobDetails.title.Window"/>
</s:if>

<s:if test='"add" != #request.methodName' > 
	<s:if test='"change" == #request.methodName' >
		<s:text name="changeJob.title.Window"/>
	</s:if>
	<s:if test='"change" != #request.methodName' >
		<s:text name="viewJob.title.Window"/>
	</s:if>
</s:if>
</title>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">

<SCRIPT language="JAVASCRIPT">
/**
 * When window is load, find each 'tr' that contains a 'td'  has an input
 * which value contains  '*', 
 * then apply the style 'required' for each empty input   
 * Added by Med Amine <PERSON>
 * 
 **/
mandatoryFieldsArray = ["*"];
var reportSchedulerData = new Object();
var newILMReportSchedConfig = "true";
var newOpportunityCostReportSchedConfig = "true";
var newTurnoverReportSchedConfig = "true";
var newCurrencyFundingReportSchedConfig = "true";
var newInterestChargesReportSchedConfig = "true";
var parametersStatusValue;
var selectedRoleBeforeChange;
var selectedRoleNameBeforeChange;
var selectedReportTypeBeforeChange;
var selectedReportNameBeforeChange;
var reportTypePreviousSelectedIndex;

$(window).on("load",function(){
	
	callOnBodyLoad();
	
	$("tr:has(td:has(input))").each(function()
		{
				var tr = $(this);
				if(tr.find("input").val() == '*' || tr.find("input").attr("name")=="scheduledReportParams.reportName")
				{
					tr.find('input').css('border-color',function(index)
					{
						if ($(this).val() == ''){
							return 'red';
						}else{
							return '#a9a9a9';
					    }
					}).on('blur keyup',function()
					{
						if ($(this).val() == '')
							$(this).css('border','red 1px solid');
				  		else{ 
						  		$(this).css('border-color','');
								$(this).addClass('inputText');
							}
					});		
				}
		});
		
	$("input[type='radio']").on('click', function(){
	 	$("input[type='text']").removeClass('is-disabled');
	   	$("input[disabled='disabled'][type='text']").addClass('is-disabled');
	    $("input[type='text']").css('border-color','');
	    $("input[type='text']").not('[disabled="disabled"],[tabIndex="-1"]').css('background','white');
	    $("input[disabled='disabled'][type='text']").not('[tabIndex="-1"]').css('background','#e5e5e5');
	    $("input[disabled=''][type='text']").not('[tabIndex="-1"]').css('background','#e5e5e5');
		$("input[type='text']").not("[name*='Mandatory'],[name*='mandatory']").css('border','1px solid');
		$("input[type='text']").not("[name*='Mandatory'],[name*='mandatory']").css('border-color','#a9a9a9');
		$("tr:has(td:has(input))").each(function()
		{
			var tr = $(this);
			var exist = (tr.find('input').val() == '*' || tr.find("input").attr("name")=="scheduledReportParams.reportName" );
			if(exist){
				tr.find('input').css('border-color',function(index)
				{
					if ($(this).val() == '')
						return 'red';
				}).on('blur keyup',function()
					{
						if ($(this).val() == '')
							$(this).css('border','red 1px solid');
				  		else
				  		{
				  			$(this).css('border-color','');
							$(this).addClass('inputText');
						}
					});	
			}
		});
	});
	
	if (document.forms[0].elements["job.jobType"].value == 'R' || document.forms[0].elements["job.jobType"].value == 'Report') {
		document.getElementById("firstsc").style.color = "black";
	} else {
		document.getElementById("firstsc").style.color = "grey";
	}
	
});
var cancelcloseElements = new Array(1);
cancelcloseElements[0] = "cancelbutton";


function checkWeekly(){

 var checkAll = document.forms[0].elements["scheduler.scheduleDayAll"].checked;
 var checkSun = document.forms[0].elements["scheduler.scheduleDaySun"].checked;
 var checkMon = document.forms[0].elements["scheduler.scheduleDayMon"].checked;
 var checkTue = document.forms[0].elements["scheduler.scheduleDayTue"].checked;
 var checkWed = document.forms[0].elements["scheduler.scheduleDayWed"].checked;
 var checkThu = document.forms[0].elements["scheduler.scheduleDayThr"].checked;
 var checkFri = document.forms[0].elements["scheduler.scheduleDayFri"].checked;
 var checkSat = document.forms[0].elements["scheduler.scheduleDaySat"].checked;
 if (checkAll == true  || checkSun == true|| checkMon == true|| checkTue == true|| checkWed == true|| checkThu == true|| checkFri == true|| checkSat == true)
	return true;
 else 
	return false;
}

function checkMonthly(){
	var checkDay = document.forms[0].elements["scheduler.monthDateAsString"].value;
	var checkFirst = document.forms[0].elements["scheduler.monthFirst"].checked;
	var checkLast = document.forms[0].elements["scheduler.monthLast"].checked;

if (checkDay.length>0 || checkFirst== true || checkLast == true)
	return true;
 else
	return false;
}

<s:if test='"yes" == #request.parentFormRefresh' >
try {
	window.opener.submitFormFromAddJob("display", "${requestScope.scheduledJobType}");
}catch (e) {
	
}
self.close();
</s:if>



var systemDate= '${systemDate}'; 
var systemTime= '${systemTime}'; 
var dateFormat = '<s:property value="#request.session.CDM.dateFormat" />';
var schedReportTypeParams;
var schedReportTypeParamsAsJson;
var mapDateMethod;
var outputFormat;
var menuItemId;
var configIncorrectCause;
function callOnBodyLoad(){
	configIncorrectCause='${requestScope.configIncorrectCause}';
	document.forms[0].selectedUsers.value = '${requestScope.selectedUsers}'; 
	document.forms[0].selectedRoles.value = '${requestScope.selectedRoles}'; 
	document.forms[0].selectedEmailUsers.value = '${requestScope.selectedEmailUsers}'; 
	document.forms[0].selectedEmailRoles.value = '${requestScope.selectedEmailRoles}'; 
	document.forms[0].elements["scheduledReportParams.outputFormat"].disabled="true";
	if (document.forms[0].elements["job.jobType"].value == 'R' || document.forms[0].elements["job.jobType"].value == 'Report') {
		if ('${methodName}' == "change") {
			document.forms[0].selectedjobId.value = '${requestScope.selectedjobId}'; 
			parametersStatusValue = '${requestScope.parametersStatusValue}';
			document.forms[0].elements["scheduledReportParams.reportTypeId"].className='is-disabled';
			document.forms[0].elements["scheduledReportParams.reportTypeId"].disabled="true";
			newILMReportSchedConfig = false;
			newOpportunityCostReportSchedConfig = false;
			newTurnoverReportSchedConfig = false;
			newCurrencyFundingReportSchedConfig = false;
			newInterestChargesReportSchedConfig = false;
			document.forms[0].schedulerConfigXML.value = document.forms[0].elements['scheduledReportParams.reportConfig'].value;
		} else if ('${methodName}' == "add") {
			if ('${requestScope.schedulerConfigXML}' != "") {
				parametersStatusValue = '${requestScope.parametersStatusValue}';
				newILMReportSchedConfig = false;
				newOpportunityCostReportSchedConfig = false
				newTurnoverReportSchedConfig = false;
				newCurrencyFundingReportSchedConfig = false;
				newInterestChargesReportSchedConfig = false;
				reportTypePreviousSelectedIndex = '${requestScope.reportTypePreviousSelectedIndex}';
			} else {
				
				parametersStatusValue = "noConfig";
				var e = document.forms[0].elements["scheduledReportParams.reportTypeId"];
				reportTypePreviousSelectedIndex = e.selectedIndex;
				document.forms[0].elements["scheduledReportParams.reportName"].value = e.options[e.selectedIndex].text;
			}
			document.forms[0].selectedjobId.value = document.forms[0].elements["job.jobDescription"].value;
			selectedReportTypeBeforeChange = document.forms[0].elements["scheduledReportParams.reportTypeId"].value;
			selectedReportNameBeforeChange = document.forms[0].elements["scheduledReportParams.reportName"].value;
			document.forms[0].schedulerConfigXML.value = '${requestScope.schedulerConfigXML}';
		}
		schedReportTypeParams = '${requestScope.schedReportTypeParams}';
		schedReportTypeParamsAsJson =  JSON.parse(schedReportTypeParams);
	    setJobReportTypes();
		var roleDropBoxElement = new SwSelectBox(document.forms[0].elements["scheduledReportParams.executionRole"],document.getElementById("roleName"));
		selectedRoleBeforeChange = document.forms[0].elements["scheduledReportParams.executionRole"].value;
		selectedRoleNameBeforeChange = document.getElementById("roleName").innerHTML;
		updateParametersStatus(parametersStatusValue);
	}
	
	var jobType;
	var radioElements = document.forms[0].elements["scheduler.jobType"];
	
	for(var idx = 0 ; idx < radioElements.length; ++idx)
	{
		if(radioElements[idx].checked) 
		jobType = radioElements[idx].value;
	}
	if (jobType == 'C')
	{
	<s:if test='"view" != #request.methodName' > 
			onceDisable();
			dailyDisable();
			weeklyDisable();
			monthlyDisable();
	</s:if>    
	document.forms[0].elements["CyclicDurationmandatory"].value = "*";
	}
	else if (jobType == 'O')
	{
	<s:if test='"view" != #request.methodName' > 
			cyclicDisable();
			dailyDisable();
			weeklyDisable();
			monthlyDisable();
	</s:if>    
	<s:if test='"view" == #request.methodName' > 
			document.forms[0].elements["scheduler.scheduleTime"][1].value="";
			document.forms[0].elements["scheduler.scheduleTime"][2].value="";
			document.forms[0].elements["scheduler.scheduleTime"][3].value="";
	</s:if>
	document.forms[0].elements["OnceDateMandatory"].value = "*";
	document.forms[0].elements["OnceTimeMandatory"].value = "*";
	}
	else if (jobType == 'D')
	{
	<s:if test='"view" != #request.methodName' > 
			cyclicDisable();
			onceDisable();
			weeklyDisable();
			monthlyDisable();
	</s:if>
    <s:if test='"view" == #request.methodName' > 
			document.forms[0].elements["scheduler.scheduleTime"][0].value="";
			document.forms[0].elements["scheduler.scheduleTime"][2].value="";
			document.forms[0].elements["scheduler.scheduleTime"][3].value="";
    </s:if>
	document.forms[0].elements["dailyTimeMandatory"].value = "*";
	}
	else if (jobType == 'W')
	{
	<s:if test='"view" != #request.methodName' > 
			cyclicDisable();
			onceDisable();
			dailyDisable();
			monthlyDisable();
	</s:if>
	<s:if test='"view" == #request.methodName' > 
			document.forms[0].elements["scheduler.scheduleTime"][0].value="";
			document.forms[0].elements["scheduler.scheduleTime"][1].value="";
			document.forms[0].elements["scheduler.scheduleTime"][3].value="";
	</s:if>
	document.forms[0].elements["weeklyTimeMandatory"].value = "*";
	}
	else if (jobType == 'M')
	{
	<s:if test='"view" != #request.methodName' > 
			cyclicDisable();
			onceDisable();
			dailyDisable();
			weeklyDisable();
		
	</s:if>
    <s:if test='"view" == #request.methodName' > 
			document.forms[0].elements["scheduler.scheduleTime"][0].value="";
			document.forms[0].elements["scheduler.scheduleTime"][1].value="";
			document.forms[0].elements["scheduler.scheduleTime"][2].value="";
	</s:if>
	document.forms[0].elements["monthlyDayMandatory"].value = "*";
	document.forms[0].elements["monthlyTimeMandatory"].value = "*";
	}else if (jobType == 'N')
	{
			cyclicDisable();
			onceDisable();
			dailyDisable();
			weeklyDisable();
			monthlyDisable();	
			
	}

	
	
	

}

function buildAddURL(methodName){
	
	var param = 'scheduler.do?method='+methodName;
	return param;
}

function submitForm(methodName){
 var jobType;
 var siguiente= false;
 var validComplete= false;

	  document.forms[0].elements["scheduledReportParams.outputFormat"].disabled="";
 
		var radioElements = document.forms[0].elements["scheduler.jobType"];
		for(var idx = 0 ; idx < radioElements.length; ++idx)
		{
			if(radioElements[idx].checked) 
			jobType = radioElements[idx].value;
		}
	var typeJob= document.forms[0].elements["job.jobType"].value
	 var scheduleDate = document.forms[0].elements['scheduler.scheduleDateAsString'].value;
	 var scheduleTime = document.forms[0].elements['scheduler.scheduleTime'][0].value; 	
	 var reportName = document.forms[0].elements['scheduledReportParams.reportName'].value; 
	 var reportTypeId = document.forms[0].elements['scheduledReportParams.reportTypeId'].value; 
	 var days = document.forms[0]["scheduledReportParams.retainDays"].value;
	 var outputLocation = document.forms[0]["scheduledReportParams.outputLocation"].value.trim();
	 var fileNamePrefix = document.forms[0]["scheduledReportParams.fileNamePrefix"].value.trim();
	 document.forms[0].scheduledJobType.value = document.forms[0].elements["job.jobType"].value;
	 document.forms[0].reportTypePreviousSelectedIndex.value = reportTypePreviousSelectedIndex;
	 if (validateFrecuencyData(jobType,methodName,"add")){
		if (document.forms[0].elements["job.jobType"].value == 'R' || document.forms[0].elements["job.jobType"].value == 'Report') {
			if(validateReportName(reportName)){
				if(outputLocation == "" || validateOutputLocation(outputLocation)){
					if(fileNamePrefix == "" || validatePrefix(fileNamePrefix)){
						if(days == "" || validateRetainDays(days)){
							document.forms[0]["scheduledReportParams.outputLocation"].value = outputLocation;
							document.forms[0]["scheduledReportParams.fileNamePrefix"].value = fileNamePrefix;
							document.forms[0].submit();
						}
					}
						
				}			
			 }
		}else{
			document.forms[0].submit();
		}
	}

}

function validateOutputLocation(path){
	var reg = new RegExp(/^(?:[\w]\:|[\/])?([\\|\/]?[\w\-\s0-9]+)+$/);

	if(!reg.test(path)){
		alert('<s:text name="addjob.alert.invalidOutputFileLocation"/>');
	} 
	return reg.test(path);
}


function validatePrefix(prefix){
	var reg = new RegExp(/^[-_a-zA-Z0-9]+$/);
	if(!reg.test(prefix)){
		alert('<s:text name="addjob.alert.invalidFileNamePrefix"/>');
	} 
	return reg.test(prefix);
}

function validateReportName(reportName) {
	
	if(reportName == "")
	{
		alert('<s:text name="alert.pleaseFillAllMandatoryFields"/>');
		document.forms[0].elements["scheduledReportParams.outputFormat"].disabled="true";

		return false;
	} else {
		return true;
	}
}

function validateRetainDays(days){
	
    if(!days.match(/^\d+$/))
     {
    	alert('<s:text name="addjob.alert.invalidRetainFiles"/>');
        return false ;
     }
  	else
  		 return true;
}



function validateFrecuencyData(jobType, methodName, cameFrom) {
	var isValid = true;
	 var checkforzero=true;
	if (validateForm(document.forms[0])) {
		if (jobType == 'W') {
			isValid = checkWeekly();
		}
		if (jobType == 'M') {
			isValid = checkMonthly();
		}
		if (isValid == false) {
			alert('<s:text name="addJob.alert.selectDays"/>');
		}

		if (isValid == true) {

			if (jobType == 'D') {
				if (validateField(
						document.forms[0].elements['scheduler.scheduleTime'][1],
						'Number 2', 'timePat')) {

					document.forms[0].method.value = methodName;
					if (cameFrom == "change") {
						document.forms[0].selectedjobId.value = '${selectedjobId}';
						enableFields();
					} else {
						document.forms[0].selectedjobId.value = document.forms[0].elements["job.jobDescription"].value;
					}
					return true;
				} else {
					document.forms[0].elements['scheduler.scheduleTime'][1]
							.focus();
					return false;
				}

			} else if (jobType == 'O') {
				if (validateField(
						document.forms[0].elements['scheduler.scheduleDateAsString'],
						'scheduler.scheduleDateAsString', dateFormat)) {
					if (validateField(
							document.forms[0].elements['scheduler.scheduleTime'][0],
							'Number 2', 'timePat')) {
						document.forms[0].method.value = methodName;
						if (cameFrom == "change") {
							document.forms[0].selectedjobId.value = '${selectedjobId}';
							enableFields();
						} else {
							document.forms[0].selectedjobId.value = document.forms[0].elements["job.jobDescription"].value;
						}
						return true;
					} else {
						document.forms[0].elements['scheduler.scheduleTime'][0]
								.focus();
						return false;
					}
				} else {
					document.forms[0].elements['scheduler.scheduleDateAsString']
							.focus();
					return false;
				}

			} else if (jobType == 'C' || jobType == 'c') {
				if (validateField(
						document.forms[0].elements['scheduler.durationHoursasString'],
						'Number 2', 'numberPatExpand', 23, 0)) {
					if (validateField(
							document.forms[0].elements['scheduler.durationMinsasString'],
							'Number 2', 'numberPatExpand', 59, 0)) {
						if (validateField(
								document.forms[0].elements['scheduler.durationSecsasString'],
								'Number 2', 'numberPatExpand', 59, 0)) {
							checkforzero = checkZero();
							if (checkforzero == true) {
								document.forms[0].method.value = methodName;
								if (cameFrom == "change") {
									document.forms[0].selectedjobId.value = '${selectedjobId}';
									enableFields();
								} else {
									document.forms[0].selectedjobId.value = document.forms[0].elements["job.jobDescription"].value;
								}
								return true;
							}
						} else {
							document.forms[0].elements['scheduler.durationSecsasString']
									.focus();
							return false;
						}
					} else {
						document.forms[0].elements['scheduler.durationMinsasString']
								.focus();
						return false;
					}
				} else {
					document.forms[0].elements['scheduler.durationHoursasString']
							.focus();
					return false;
				}

			} else if (jobType == 'W') {
				if (validateField(
						document.forms[0].elements['scheduler.scheduleTime'][2],
						'Number 2', 'timePat')) {
					document.forms[0].method.value = methodName;
					if (cameFrom == "change") {
						document.forms[0].selectedjobId.value = '${selectedjobId}';
						enableFields();
					} else {
						document.forms[0].selectedjobId.value = document.forms[0].elements["job.jobDescription"].value;
					}
					return true;
				} else {
					document.forms[0].elements['scheduler.scheduleTime'][2]
							.focus();
					return false;
				}

			} else if (jobType == 'M') {

				if (validateField(
						document.forms[0].elements['scheduler.monthDateAsString'],
						'Number 2', 'numberPat', 31, 1)) {
					if (validateField(
							document.forms[0].elements['scheduler.scheduleTime'][3],
							'Number 2', 'timePat')) {

						document.forms[0].method.value = methodName;
						if (cameFrom == "change") {
							document.forms[0].selectedjobId.value = '${selectedjobId}';
							enableFields();
						} else {
							document.forms[0].selectedjobId.value = document.forms[0].elements["job.jobDescription"].value;
						}
						return true;
					} else {
						document.forms[0].elements['scheduler.scheduleTime'][3]
								.focus();
						return false;
					}
				} else {
					document.forms[0].elements['scheduler.monthDateAsString']
							.focus();
					return false;
				}

			} else {
				document.forms[0].method.value = methodName;
				if (cameFrom == "change") {
					document.forms[0].selectedjobId.value = '${selectedjobId}';
					enableFields();
				} else {
					document.forms[0].selectedjobId.value = document.forms[0].elements["job.jobDescription"].value;
				}
				return true;
			}
		}
	}
}



function checkZero()
{	

		var hrs=document.forms[0].elements["scheduler.durationHoursasString"].value;
		var min=document.forms[0].elements["scheduler.durationMinsasString"].value;
		var sec=document.forms[0].elements["scheduler.durationSecsasString"].value;
		
		if(hrs == "00" && min == "00" && sec =="00")
		{
			alert('<s:text name="alert.cyclicInervalCannotBeZero"/>');
			document.forms[0].elements["scheduler.durationHoursasString"].value="";
			document.forms[0].elements["scheduler.durationMinsasString"].value="";
			document.forms[0].elements["scheduler.durationSecsasString"].value="";
			document.forms[0].elements["scheduler.durationHoursasString"].focus();	
			return false;
		}			
		else
		{
			return true;
		}
}	
// Code modification for Server Log Issue Ends 	  
	
	  

function updateSubmitForm(methodName){
	var jobType;
	var isValid = true;
	var checkforzero=true;
	document.forms[0].elements["scheduledReportParams.outputFormat"].disabled="";
	var radioElements = document.forms[0].elements["scheduler.jobType"];
	for(var idx = 0 ; idx < radioElements.length; ++idx)
	{
		if(radioElements[idx].checked) 
		jobType = radioElements[idx].value;
	}
	var scheduleDate = document.forms[0].elements['scheduler.scheduleDateAsString'].value;
	var scheduleTime = document.forms[0].elements['scheduler.scheduleTime'][0].value;	
	 
	 
	var typeJob = document.forms[0].elements["job.jobType"].value
	var reportName = document.forms[0].elements['scheduledReportParams.reportName'].value;
	var days = document.forms[0]["scheduledReportParams.retainDays"].value;
	var outputLocation = document.forms[0]["scheduledReportParams.outputLocation"].value.trim();
	var fileNamePrefix = document.forms[0]["scheduledReportParams.fileNamePrefix"].value.trim();
	document.forms[0].scheduledJobType.value = document.forms[0].elements["job.jobType"].value;
	document.forms[0].reportTypePreviousSelectedIndex.value = reportTypePreviousSelectedIndex;
	if (validateFrecuencyData(jobType, methodName, "change")) {
		if (document.forms[0].elements["job.jobType"].value == 'R'
				|| document.forms[0].elements["job.jobType"].value == 'Report') {
			if (validateReportName(reportName)) {
				if(outputLocation == "" || validateOutputLocation(outputLocation)){
					if(fileNamePrefix == "" || validatePrefix(fileNamePrefix)){
						if (days == "" || validateRetainDays(days)) {
							document.forms[0]["scheduledReportParams.outputLocation"].value = outputLocation;
							document.forms[0]["scheduledReportParams.fileNamePrefix"].value = fileNamePrefix;
							if("E" == document.forms[0].elements["scheduler.jobStatus"].value && 'N' != document.forms[0].elements["scheduler.jobType"].value && 'O' != document.forms[0].elements["scheduler.jobType"].value){
								var message = "<s:text name="addjob.alert.configChanged"/>";
								ShowErrMsgWindowWithBtn("", message, YES_NO, deleteYes,deleteNo );
									
							}else{
								document.forms[0].submit();
							}
						}
					}
				}
			}
		} else {
			document.forms[0].submit();
		}
	}
}


function deleteNo(){
	document.forms[0].changeAndRunNow.value = false;
	document.forms[0].submit();
}
function deleteYes(){
	document.forms[0].changeAndRunNow.value = true;
	document.forms[0].submit();
}

function cyclic(){

    onceDisable();
	dailyDisable();
	weeklyDisable();
	monthlyDisable();
	document.forms[0].elements["CyclicDurationmandatory"].value = "*";
	document.forms[0].elements["scheduler.durationHoursasString"].disabled="";
	document.forms[0].elements["scheduler.durationMinsasString"].disabled="";
	document.forms[0].elements["scheduler.durationSecsasString"].disabled="";
	
	
}
function once(){

	cyclicDisable();
    dailyDisable();
	weeklyDisable();
	monthlyDisable();
	document.forms[0].elements["OnceDateMandatory"].value = "*";
	document.forms[0].elements["OnceTimeMandatory"].value = "*";

	document.forms[0].elements["scheduler.scheduleDateAsString"].disabled="";
	document.forms[0].elements["scheduler.scheduleTime"][0].disabled="";
	
}


function manual()
{
	cyclicDisable();
    onceDisable();
	weeklyDisable();
	monthlyDisable();
	dailyDisable();
}

function daily(){

	cyclicDisable();
    onceDisable();
	weeklyDisable();
	monthlyDisable();
	document.forms[0].elements["dailyTimeMandatory"].value = "*";
	document.forms[0].elements["scheduler.scheduleTime"][1].disabled="";
	
}
function weekly(){

	cyclicDisable();
	onceDisable();
    dailyDisable();
	monthlyDisable();
   document.forms[0].elements["weeklyTimeMandatory"].value = "*";
	document.forms[0].elements["scheduler.scheduleDayAll"].disabled="";
	document.forms[0].elements["scheduler.scheduleDayMon"].disabled="";
	document.forms[0].elements["scheduler.scheduleDayTue"].disabled="";
	document.forms[0].elements["scheduler.scheduleDayWed"].disabled="";
	document.forms[0].elements["scheduler.scheduleDayThr"].disabled="";
	document.forms[0].elements["scheduler.scheduleDayFri"].disabled="";
	document.forms[0].elements["scheduler.scheduleDaySat"].disabled="";
	document.forms[0].elements["scheduler.scheduleDaySun"].disabled="";
	document.forms[0].elements["scheduler.scheduleTime"][2].disabled="";
}
function monthly(){

	cyclicDisable();
	onceDisable();
    dailyDisable();
	weeklyDisable();
	document.forms[0].elements["monthlyDayMandatory"].value = "*";
	document.forms[0].elements["monthlyTimeMandatory"].value = "*";
	document.forms[0].elements["scheduler.monthFirst"].disabled="";
	document.forms[0].elements["scheduler.monthLast"].disabled="";
	document.forms[0].elements["scheduler.monthDateAsString"].disabled="";
	document.forms[0].elements["scheduler.scheduleTime"][3].disabled="";
}

function cyclicDisable(){

	 document.forms[0].elements["CyclicDurationmandatory"].value = "";
	document.forms[0].elements["scheduler.durationHoursasString"].value="";
	document.forms[0].elements["scheduler.durationMinsasString"].value="";
	document.forms[0].elements["scheduler.durationSecsasString"].value="";
	document.forms[0].elements["scheduler.durationHoursasString"].disabled="true";
	document.forms[0].elements["scheduler.durationHoursasString"].className='is-disabled';
	document.forms[0].elements["scheduler.durationMinsasString"].disabled="true";
	document.forms[0].elements["scheduler.durationMinsasString"].className='is-disabled';
	document.forms[0].elements["scheduler.durationSecsasString"].disabled="true";
	document.forms[0].elements["scheduler.durationSecsasString"].className='is-disabled';
	
}
function onceDisable(){

	document.forms[0].elements["OnceDateMandatory"].value = "";
	document.forms[0].elements["OnceTimeMandatory"].value = "";
	document.forms[0].elements["scheduler.scheduleDateAsString"].value="";
	document.forms[0].elements["scheduler.scheduleTime"][0].value="";
	document.forms[0].elements["scheduler.scheduleDateAsString"].disabled="true";
	document.forms[0].elements["scheduler.scheduleDateAsString"].className='is-disabled';
	document.forms[0].elements["scheduler.scheduleTime"][0].disabled="true";
	document.forms[0].elements["scheduler.scheduleTime"][0].className='is-disabled';
	document.forms[0].elements["scheduledReportParams.outputFormat"].disabled="true";
}
function dailyDisable(){
	document.forms[0].elements["dailyTimeMandatory"].value = "";
	document.forms[0].elements["scheduler.scheduleTime"][1].value="";
	document.forms[0].elements["scheduler.scheduleTime"][1].disabled="true";
	document.forms[0].elements["scheduler.scheduleTime"][1].className='is-disabled';
	document.forms[0].elements["scheduledReportParams.outputFormat"].disabled="true";
}
function weeklyDisable(){
	weeklyDisableAll(0);
    weeklyDisableOthers(0);
	 document.forms[0].elements["weeklyTimeMandatory"].value = "";
	document.forms[0].elements["scheduler.scheduleTime"][2].value="";
	document.forms[0].elements["scheduler.scheduleTime"][2].disabled="true";
	document.forms[0].elements["scheduler.scheduleTime"][2].className='is-disabled';
	document.forms[0].elements["scheduledReportParams.outputFormat"].disabled="true";
}
function weeklyDisableAll(flag){

 var checkSun = document.forms[0].elements["scheduler.scheduleDaySun"].checked;
 var checkMon = document.forms[0].elements["scheduler.scheduleDayMon"].checked;
 var checkTue = document.forms[0].elements["scheduler.scheduleDayTue"].checked;
 var checkWed = document.forms[0].elements["scheduler.scheduleDayWed"].checked;
 var checkThu = document.forms[0].elements["scheduler.scheduleDayThr"].checked;
 var checkFri = document.forms[0].elements["scheduler.scheduleDayFri"].checked;
 var checkSat = document.forms[0].elements["scheduler.scheduleDaySat"].checked;

if (checkSun == true|| checkMon == true|| checkTue == true|| checkWed == true|| checkThu == true|| checkFri == true|| checkSat == true || (flag != 'undefined' && flag == '0') ){
	document.forms[0].elements["scheduler.scheduleDayAll"].checked="";
	document.forms[0].elements["scheduler.scheduleDayAll"].disabled="true";
  }else{
    document.forms[0].elements["scheduler.scheduleDayAll"].disabled="";
  }
}
function weeklyDisableOthers(flag){
if ( document.forms[0].elements["scheduler.scheduleDayAll"].checked == true || (flag != 'undefined' && flag == '0')){
	
	document.forms[0].elements["scheduler.scheduleDayMon"].checked="";
	document.forms[0].elements["scheduler.scheduleDayTue"].checked="";
	document.forms[0].elements["scheduler.scheduleDayWed"].checked="";
	document.forms[0].elements["scheduler.scheduleDayThr"].checked="";
	document.forms[0].elements["scheduler.scheduleDayFri"].checked="";
	document.forms[0].elements["scheduler.scheduleDaySat"].checked="";
	document.forms[0].elements["scheduler.scheduleDaySun"].checked="";
	document.forms[0].elements["scheduler.scheduleDayMon"].disabled="true";
	document.forms[0].elements["scheduler.scheduleDayTue"].disabled="true";
	document.forms[0].elements["scheduler.scheduleDayWed"].disabled="true";
	document.forms[0].elements["scheduler.scheduleDayThr"].disabled="true";
	document.forms[0].elements["scheduler.scheduleDayFri"].disabled="true";
	document.forms[0].elements["scheduler.scheduleDaySat"].disabled="true";
	document.forms[0].elements["scheduler.scheduleDaySun"].disabled="true";
  }else{
	    weeklyEnableOthers();
  }
}
function weeklyEnableOthers(){

	document.forms[0].elements["scheduler.scheduleDayMon"].disabled="";
	document.forms[0].elements["scheduler.scheduleDayTue"].disabled="";
	document.forms[0].elements["scheduler.scheduleDayWed"].disabled="";
	document.forms[0].elements["scheduler.scheduleDayThr"].disabled="";
	document.forms[0].elements["scheduler.scheduleDayFri"].disabled="";
	document.forms[0].elements["scheduler.scheduleDaySat"].disabled="";
	document.forms[0].elements["scheduler.scheduleDaySun"].disabled="";
}

function monthlyDisable(){
	document.forms[0].elements["monthlyDayMandatory"].value = "";
	document.forms[0].elements["monthlyTimeMandatory"].value = "";
	document.forms[0].elements["scheduler.monthFirst"].checked="";
	document.forms[0].elements["scheduler.monthLast"].checked="";
	document.forms[0].elements["scheduler.scheduleTime"][3].value="";
	document.forms[0].elements["scheduler.monthDateAsString"].value="";
	document.forms[0].elements["scheduler.monthFirst"].disabled="true";
	document.forms[0].elements["scheduler.monthLast"].disabled="true";
	document.forms[0].elements["scheduler.scheduleTime"][3].disabled="true";
	document.forms[0].elements["scheduler.scheduleTime"][3].className='is-disabled';
	document.forms[0].elements["scheduler.monthDateAsString"].disabled="true";
	document.forms[0].elements["scheduler.monthDateAsString"].className='is-disabled';
	document.forms[0].elements["scheduledReportParams.outputFormat"].disabled="true";
}

function validateForm(objForm){
  var elementsRef;

	    var jobType;
		var radioElements = document.forms[0].elements["scheduler.jobType"];
		for(var idx = 0 ; idx < radioElements.length; ++idx)
		{
			if(radioElements[idx].checked) 
			jobType = radioElements[idx].value;
		}
		if (jobType == 'O') {
			
			<s:if test='"add" == #request.methodName' > 
			 elementsRef  = new Array(3);
			</s:if>
				<s:if test='"add" != #request.methodName' > 
				elementsRef  = new Array(2);
			</s:if>
			elementsRef[0] = objForm.elements["scheduler.scheduleTime"][0];
				
			elementsRef[1] = objForm.elements["scheduler.scheduleDateAsString"];
			
			<s:if test='"add" == #request.methodName' > 
			 elementsRef[2] = objForm.elements["job.jobDescription"];
			</s:if>
				
			
		}
		else if (jobType == 'C') {
		
			<s:if test='"add" == #request.methodName' > 
			 elementsRef  = new Array(4);
			</s:if>
				<s:if test='"add" != #request.methodName' > 
				elementsRef  = new Array(3);
			</s:if>
			elementsRef[0] = objForm.elements["scheduler.durationHoursasString"];
			elementsRef[1] = objForm.elements["scheduler.durationMinsasString"];
			elementsRef[2] = objForm.elements["scheduler.durationSecsasString"];
			
			<s:if test='"add" == #request.methodName' > 
			 elementsRef[3] = objForm.elements["job.jobDescription"];
			</s:if>
		}
		else if (jobType == 'D') {
			
			<s:if test='"add" == #request.methodName' > 
			 elementsRef  = new Array(2);
			</s:if>
				<s:if test='"add" != #request.methodName' > 
				elementsRef  = new Array(1);
			</s:if>
			elementsRef[0] = objForm.elements["scheduler.scheduleTime"][1];

			<s:if test='"add" == #request.methodName' > 
				 elementsRef[1] = objForm.elements["job.jobDescription"];
			</s:if>
		}
		else if (jobType == 'W') {
			<s:if test='"add" == #request.methodName' > 
			 elementsRef  = new Array(2);
			</s:if>
				<s:if test='"add" != #request.methodName' > 
				elementsRef  = new Array(1);
			</s:if>
			elementsRef[0] = objForm.elements["scheduler.scheduleTime"][2];
			<s:if test='"add" == #request.methodName' > 
			 elementsRef[1] = objForm.elements["job.jobDescription"];
		</s:if>
		}
		else if (jobType == 'M') {
			<s:if test='"add" == #request.methodName' > 
			 elementsRef  = new Array(2);
			</s:if>
				<s:if test='"add" != #request.methodName' > 
				elementsRef  = new Array(1);
			</s:if>
				elementsRef[0] = objForm.elements["scheduler.scheduleTime"][3];

			<s:if test='"add" == #request.methodName' > 
				 elementsRef[1] = objForm.elements["job.jobDescription"];
			</s:if>
		}

		else if (jobType == 'N') {
			
			<s:if test='"add" == #request.methodName' > 
			
			elementsRef  = new Array(1);
			elementsRef[0] = objForm.elements["job.jobDescription"];
			</s:if>

			<s:if test='"add" != #request.methodName' > 
				elementsRef  = new Array(0);
			</s:if>
		}
  return validate(elementsRef);
}

function validationForMonthTypeFirstDay()
{

		document.forms[0].elements["scheduler.monthLast"].checked = "";
		document.forms[0].elements["scheduler.monthDateAsString"].value = "";
}


function validationForMonthTypeLastDay()
{
	document.forms[0].elements["scheduler.monthFirst"].checked = "";
	document.forms[0].elements["scheduler.monthDateAsString"].value = "";

}
function validationForMonthTypeDate()
{
	document.forms[0].elements["scheduler.monthFirst"].checked = "";
	document.forms[0].elements["scheduler.monthLast"].checked = "";

}

/**
* This method is used to check the maximum character length
* 
* @param field
* @param maxChars
* @return none	 
*/
function maxLengthTextArea (field,maxChars)
{
	if(field.value.length > maxChars)
		field.value = field.value.substring(0, maxChars);
}

function jobTypeChange() 
{
	submitFormJobType("add");
}

function submitFormJobType(methodName)
{
	document.forms[0].method.value = methodName;
	document.forms[0].selectedJobType.value = document.forms[0].elements["job.jobType"].value;
	document.forms[0].submit();
}


function jobDescriptionChange() 
{
	submitFormJobDescription("add");
}

function submitFormJobDescription(methodName)
{
	document.forms[0].method.value = methodName;
	document.forms[0].selectedJobType.value = document.forms[0].elements["job.jobType"].value;
	document.forms[0].selectedJobDescription.value = document.forms[0].elements["job.jobDescription"].value;
	document.forms[0].submit();
}

function onmouseoutfirstsc(tab, element) 
{
	if (document.forms[0].elements["job.jobType"].value == 'R' || document.forms[0].elements["job.jobType"].value == 'Report') {
		revertback(tab,element);
		
	}
}

function onmouseoverfirstsc(tab, element) 
{
	if (document.forms[0].elements["job.jobType"].value == 'R' || document.forms[0].elements["job.jobType"].value == 'Report') {
		changecontent(tab,element);
	}
}

function onClickfirstsc(tab, element) 
{
	if (document.forms[0].elements["job.jobType"].value == 'R' || document.forms[0].elements["job.jobType"].value == 'Report') {
		changeselected(tab);
		expandcontent(tab, this);
	}
}

/** This function is passes  url base on  screen and display the subscreens 
* @ param MethodName
* @ param screenName
* @ return param
*/
function buildDistList(methodName)
{
	var param = 'scheduler.do?method='+methodName;
	param+= '&selectedUsers='+document.forms[0].selectedUsers.value;
	param+= '&selectedRoles='+document.forms[0].selectedRoles.value;
    return param;  
}

/** This function is passes  url base on  screen and display the subscreens 
* @ param MethodName
* @ param screenName
* @ return param
*/
function buildAccessList(methodName)
{
	var param = 'scheduler.do?method='+methodName;
    return param;  
}

function buildConfigParams()
{
	
	var appName = "<%=SwtUtil.appName%>";
	var requestURL = new String('<%=request.getRequestURL()%>');
	var idy = requestURL.indexOf('/'+appName+'/');
	requestURL=requestURL.substring(0,idy+1) ; 
	requestURL = requestURL + appName+"/scheduler.do?method=getScreenDetails";
	requestURL = requestURL + "&menuItemId=" + menuItemId;
	var oXMLHTTP = new XMLHttpRequest();
	oXMLHTTP.open( "POST", requestURL, false );
	oXMLHTTP.send();
	var screenDetails=new String(oXMLHTTP.responseText);
	
	var screenDetailsList = screenDetails.split("<%=SwtConstants.SEPARATOR_SCREEN_DETAILS%>");
	var programName = screenDetailsList[0];
	if (programName.indexOf("?") == -1)
		programName += '?';
	var width = screenDetailsList[1];
	var height = screenDetailsList[2];
	
	param= '&configScheduler=true';
	param+= '&newILMReportSchedConfig='+newILMReportSchedConfig;
	param+= '&newOpportunityCostReportSchedConfig='+newOpportunityCostReportSchedConfig;
	param+= '&newTurnoverReportSchedConfig='+newTurnoverReportSchedConfig;
	param+= '&newCurrencyFundingReportSchedConfig='+newCurrencyFundingReportSchedConfig;
	param+= '&newInterestChargesReportSchedConfig='+newInterestChargesReportSchedConfig;
	param+= '&jobId='+document.forms[0].selectedjobId.value;
	param+= '&roleId='+document.forms[0].elements["scheduledReportParams.executionRole"].value;
	param+= '&reportType='+document.forms[0].elements["scheduledReportParams.reportTypeId"].value;
	param+= '&schedulerConfigXML='+document.forms[0].schedulerConfigXML.value;
	openWindow(programName + param, 'confiParamsScreenWindow','left=0,top=55,width='+width+',height='+height+',toolbar=0, resizable=yes, scrollbars=yes,status=yes');
}

function showXML() {
	var param = 'scheduler.do?method=showXMLWindow';
	param+="&schedulerConfigXML="+document.forms[0].schedulerConfigXML.value;
	openWindow(param,'showXMLScreenWindow','left=50,top=190,width=560,height=455,toolbar=0, resizable=yes, scrollbars=yes','true');
	
}

function updateSchedulerConfigParams(outputFormat)
{
	if(outputFormat!=""){
	document.forms[0].elements["scheduledReportParams.outputFormat"].value=outputFormat;
	}
	newILMReportSchedConfig = "false";
	newOpportunityCostReportSchedConfig = "false";
	newTurnoverReportSchedConfig = "false";
	newCurrencyFundingReportSchedConfig = "false";
	newInterestChargesReportSchedConfig = "false"
	if(checkIfConfigParamsIsCorrect())
		parametersStatusValue = 'configCorrect';
	else 
		parametersStatusValue = 'configIncorrect';
	
	updateParametersStatus(parametersStatusValue);
}

function updateParametersStatus(parametersStatusValue) {
	if (parametersStatusValue == 'noConfig') {
		document.getElementById("showXMLButton").disabled=true;
		document.getElementById("showXMLButton").className = "disabled";
		document.getElementById("parametersStatusInfo").innerHTML = "<s:text name="addjob.label.configParamStatusNoConfig"/>";
		document.getElementById("parametersStatusInfo").style.color = "black";
	} else if (parametersStatusValue == 'configCorrect') {
		document.getElementById("showXMLButton").disabled = "";
		document.getElementById("showXMLButton").className = "";
		document.getElementById("parametersStatusInfo").innerHTML = "<s:text name="addjob.label.configParamStatusCorrectConfig"/>";
		document.getElementById("parametersStatusInfo").style.color = "green";
	} else if (parametersStatusValue == 'configIncorrect') {
		document.getElementById("showXMLButton").disabled = "";
		document.getElementById("showXMLButton").className = "";
		document.getElementById("parametersStatusInfo").innerHTML = "<s:text name="addjob.label.configParamStatusIncorrectConfig"/>";
		document.getElementById("parametersStatusInfo").style.color = "red";
	}
}

function checkParametersConifBeforeSubmit(cameFrom) {
	
	if (document.forms[0].elements["job.jobType"].value == 'R' || document.forms[0].elements["job.jobType"].value == 'Report') {
		if (parametersStatusValue == 'noConfig') {
			alert('<s:text name="addjob.alert.noConfigBeforeSave"/>');
		} else {
			var selectedJobId = document.forms[0].selectedjobId.value;
			if (selectedJobId == 100){
				if ( checkIfConfigParamsIsCorrect()) {
					if (cameFrom == "add") {
						submitForm('Scheduleradd');
					} else if (cameFrom == "change") {
						updateSubmitForm('schedulerChange');
					}
				} else {
					alert("<s:text name="addjob.alert.paramConfigNotCorrectBeforeSave"/>" + configIncorrectCause);
				}
			}else {
				if (cameFrom == "add") {
					submitForm('Scheduleradd');
				} else if (cameFrom == "change") {
					updateSubmitForm('schedulerChange');
				}
			}
		}
	} else {
		if (cameFrom == "add") {
			submitForm('Scheduleradd');
		} else if (cameFrom == "change") {
			updateSubmitForm('schedulerChange');
		}
	}
}

function onRoleChange() {
	if (parametersStatusValue != 'noConfig') {
		var yourstate=window.confirm('By changing the selected role, the existing report job parameters will be reset. Are you sure to continue?');
		if (yourstate!=true) {
			document.forms[0].elements["scheduledReportParams.executionRole"].value = selectedRoleBeforeChange;
			document.getElementById("roleName").innerHTML = selectedRoleNameBeforeChange;
		} else {
			selectedRoleBeforeChange = document.forms[0].elements["scheduledReportParams.executionRole"].value;
			selectedRoleNameBeforeChange = document.getElementById("roleName").innerHTML;
			initParametersConfig();
		}
	} else {
		selectedRoleBeforeChange = document.forms[0].elements["scheduledReportParams.executionRole"].value;
		selectedRoleNameBeforeChange = document.getElementById("roleName").innerHTML;
	}
}

function onReportTypeChange() {
	if ('${methodName}' == "add") {
		var e = document.forms[0].elements["scheduledReportParams.reportTypeId"];
		if (e.options[reportTypePreviousSelectedIndex].text == document.forms[0].elements["scheduledReportParams.reportName"].value) {
			document.forms[0].elements["scheduledReportParams.reportName"].value = e.options[e.selectedIndex].text;
			reportTypePreviousSelectedIndex = e.selectedIndex;
		}
		if (parametersStatusValue != 'noConfig') {
			var yourstate=window.confirm('By changing the selected report type, the existing report parameter configuration values will be reset. Are you sure to continue?');
			if (yourstate!=true) {
				document.forms[0].elements["scheduledReportParams.reportTypeId"].value = selectedReportTypeBeforeChange;
				document.forms[0].elements["scheduledReportParams.reportName"].value =selectedReportNameBeforeChange;
			} else {
				selectedReportTypeBeforeChange = document.forms[0].elements["scheduledReportParams.reportTypeId"].value;
				selectedReportNameBeforeChange = document.forms[0].elements["scheduledReportParams.reportName"].value;
				initParametersConfig();
			}
		} else {
			selectedReportTypeBeforeChange = document.forms[0].elements["scheduledReportParams.reportTypeId"].value;
			selectedReportNameBeforeChange = document.forms[0].elements["scheduledReportParams.reportName"].value;
		}
		setJobReportTypes();
	}
}

function initParametersConfig() {
	reportSchedulerData = new Object();
	parametersStatusValue = "noConfig";
	newILMReportSchedConfig = true;
	newOpportunityCostReportSchedConfig = true;
	newTurnoverReportSchedConfig = true;
	newCurrencyFundingReportSchedConfig = true;
	updateParametersStatus(parametersStatusValue);
}

function setJobReportTypes() {
	for (var j = 0; j < schedReportTypeParamsAsJson.length; j++) {
    	if (schedReportTypeParamsAsJson[j].reportTypeId == document.forms[0].elements["scheduledReportParams.reportTypeId"].value) {
    		mapDateMethod = schedReportTypeParamsAsJson[j].mapDateMethod;
    		outputFormat = schedReportTypeParamsAsJson[j].outputFormat.split(',')[0];
    		menuItemId = schedReportTypeParamsAsJson[j].menuItemId;
    		break;
    	}
    }
	
	if ('${methodName}' == "add") {
	document.forms[0].elements["scheduledReportParams.outputFormat"].value= outputFormat;
	}
	// Update the run date as value in the screen 
	if (mapDateMethod == 'S') {
		document.forms[0].elements["scheduledReportParams.mapDateMethod"][1].className='is-disabled';
		document.forms[0].elements["scheduledReportParams.mapDateMethod"][0].disabled=false;
		document.forms[0].elements["scheduledReportParams.mapDateMethod"][1].disabled="true";
		document.forms[0].elements["scheduledReportParams.mapDateMethod"][0].checked = true;
	} else if (mapDateMethod == 'E') {
		document.forms[0].elements["scheduledReportParams.mapDateMethod"][0].className='is-disabled';
		document.forms[0].elements["scheduledReportParams.mapDateMethod"][0].disabled="true";
		document.forms[0].elements["scheduledReportParams.mapDateMethod"][1].disabled=false;
		document.forms[0].elements["scheduledReportParams.mapDateMethod"][1].checked = true;
	} else if (mapDateMethod == 'S,E') {
		if ('${methodName}' == "add") {
			document.forms[0].elements["scheduledReportParams.mapDateMethod"][0].checked = true;
			document.forms[0].elements["scheduledReportParams.mapDateMethod"][0].disabled=false;
			document.forms[0].elements["scheduledReportParams.mapDateMethod"][1].disabled=false;
		}
	} else if (mapDateMethod == 'E,S') {
		if ('${methodName}' == "add") {
			document.forms[0].elements["scheduledReportParams.mapDateMethod"][1].checked = true;
			document.forms[0].elements["scheduledReportParams.mapDateMethod"][0].disabled=false;
			document.forms[0].elements["scheduledReportParams.mapDateMethod"][1].disabled=false;
		}
	}
	// Update output format provide
	/*var outputFormatAsArray = outputFormat.split(",");
	var outputFormatSelect = document.forms[0].elements["scheduledReportParams.outputFormat"];
	outputFormatSelect.innerHTML = "";
	for (var k = 0; k < outputFormatAsArray.length; k++) {
		 var option = document.createElement("option");
		 option.value = outputFormatAsArray[k];
		 option.text = outputFormatAsArray[k];
		 outputFormatSelect.appendChild(option);
	}*/
}

function showAlertConfigIncorrectCause() {
	if (parametersStatusValue == "configIncorrect") {
		alert(configIncorrectCause);
	}
}

function onMouseOverPararmStatus() {
	if (parametersStatusValue == "configIncorrect") {
		document.getElementById("parametersStatusInfo").style.cursor  = "pointer";
		document.getElementById("parametersStatusInfo").style.textDecoration  = "underline";
	}
}
function onMouseOutParamStatus() {
	if (parametersStatusValue == "configIncorrect") {
		document.getElementById("parametersStatusInfo").style.cursor  = "";
		document.getElementById("parametersStatusInfo").style.textDecoration  = "none";
	}
}

function checkIfConfigParamsIsCorrect() {
	var appName = "<%=SwtUtil.appName%>";
	var requestURL = new String('<%=request.getRequestURL()%>');
	var idy = requestURL.indexOf('/'+appName+'/');
	requestURL=requestURL.substring(0,idy+1) ; 
	requestURL = requestURL + appName+"/scheduler.do?method=checkIfConfigParamsIsCorrect";
	requestURL += "&reportConfig=" + document.forms[0].schedulerConfigXML.value;
	requestURL += "&executionRole=" + document.forms[0].elements["scheduledReportParams.executionRole"].value;
	var oXMLHTTP = new XMLHttpRequest();
	oXMLHTTP.open( "POST", requestURL, false );
	oXMLHTTP.send();
	var result=new String(oXMLHTTP.responseText);
	var resultList = result.split("<%=SwtConstants.SEPARATOR_FACILITIES%>");
	var configIsCorrect = resultList[0];
	if (configIsCorrect == "false") {
		parametersStatusValue = "configIncorrect";
		updateParametersStatus(parametersStatusValue);
		configIncorrectCause = resultList[1];
		return false;
	} else {
		return true;
	}
}

function enableFields(){
	document.forms[0].elements["scheduledReportParams.reportTypeId"].disabled = "";
}

</SCRIPT>
  <script language="JAVASCRIPT">
      var cal3 = new CalendarPopup("caldiv",true); 
	   cal3.offsetX = 5;
      cal3.offsetY = -115;
      var cal4 = new CalendarPopup("caldiv"); 

  </script>
  
<style>
  
#ddimagetabs #firstsc{
   background-image: url(images/new_bluetaboverBIS.gif); /* URL to tab image */
}

#ddimagetabs #firstsc.default{
   background-image: url(images/new_bluetaboverBIS.gif); /* URL to tab image */
} 

#ddimagetabs #firstsc.current{
   background-image: url(images/new_bluetabBIS.gif); /* URL to tab image */
} 

#ddimagetabs #firstsc.hoverall{
   background-image: url(images/new_bluetabover_glowBIS.gif); /* URL to tab image */
}

#parametersStatusInfo.hover{
	text-decoration: underline;
}
  
</style> 
</head>
<!--Start:Code Modified by Chinniah on 4-JUL-2011 for Mantis 1488:For Toll tip and alignment issues -->
<body leftmargin="0" topmargin="0" marginheight="0" onLoad="setParentChildsFocus();setFocus(document.forms[0]);ShowErrMsgWindow('${actionError}');" onunload="call()">

<s:form action="scheduler.do" onsubmit="validate(this);">
<input name="selectedjobId" type="hidden" value="GA">
<input name="selectedJobType" type="hidden" value="">
<input name="changeAndRunNow" type="hidden" value="false">
<input name="selectedJobDescription" type="hidden" value="">
<input name="selectedUsers" type="hidden" value="">
<input name="selectedRoles" type="hidden" value="">
<input name="selectedEmailUsers" type="hidden" value="">
<input name="selectedEmailRoles" type="hidden" value="">
<input name="method" type="hidden" value="displaySchedule">
<input name="sourceScreen" type="hidden" value="addJob">
<input name="schedulerConfigXML" type="hidden" value="">
<input name="scheduledJobType" type="hidden" value="">
<input name="reportTypePreviousSelectedIndex" type="hidden" value="">

<div id="JobDetails" style="z-index:99;position:absolute; left:10px; top:10px; width:615px; height:72px; border: 2px outset ; " color="#7E97AF"> 
	<div id="JobDetails" style="position:absolute; z-index:99; left:8; top:4px; width:608px; height:20px;">
		<s:hidden name="scheduler.scheduleId"/>
		 <table width="610" border="0" cellpadding="2" cellspacing="0" height="22" style="padding-top: 3px;">
	
			  <tr height="22px">
			  	 <s:if test='"add" == #request.methodName' > 
					<td   width="73px" align="left"><b><s:text name="addJob.title.jobType"/></b>*</td> 
				   </s:if>
				   <s:if test='"change" == #request.methodName' > 
					<td   width="73px" align="left"><b><s:text name="addJob.title.jobType"/></b></td> 
				   </s:if>
				    <s:if test='"view" == #request.methodName' > 
					<td   width="73px" align="left"><b><s:text name="addJob.title.jobType"/></b></td> 
				   </s:if>
				  <td width="110px">
				 <s:if test='"add" == #request.methodName' > 
					 <s:if test='"R" == #request.scheduledJobType' > 
						 <div style="width:100px">
							  <s:textfield name="job.jobType"  tabindex="1" cssStyle="width:100px;" readonly="true" />
					    </div>
					 </s:if>
					 <s:if test='"R" != #request.scheduledJobType' > 
						 <div style="width:100px">
							  <s:select id="job.jobType" name="job.jobType" titleKey="tooltip.selectJobType" tabindex="1" disabled="%{#attr.screenFieldsStatus}" size="1" cssStyle="width:100px" onchange="jobTypeChange()" list="#request.jobTypeList" listKey="value" listValue="label" />
							  
					    </div>
					 </s:if>
				 </s:if>
				 <s:if test='"change" == #request.methodName' > 
					<s:textfield name="job.jobType"  tabindex="1" cssStyle="width:100px;" readonly="true" />
				 </s:if>
				 <s:if test='"view" == #request.methodName' > 
					 <s:textfield name="job.jobType" tabindex="1" cssStyle="width:100px;" disabled="%{#attr.screenFieldsStatus}" />
				 </s:if>
				  </td>
				  
			  	 <s:if test='"add" == #request.methodName' > 
					<td   width="73px" align="left"><b><s:text name="Jobmaintenance.JobName"/></b>*</td> 
				   </s:if>
				   <s:if test='"change" == #request.methodName' > 
					<td   width="73px" align="left"><b><s:text name="Jobmaintenance.JobName"/></b></td> 
				   </s:if>
				    <s:if test='"view" == #request.methodName' > 
					<td   width="73px" align="left"><b><s:text name="Jobmaintenance.JobName"/></b></td> 
				   </s:if>
				  <td width="93px">
				 <s:if test='"add" == #request.methodName' > 
					 <div style="width:290px">
						  <s:select id="job.jobDescription" name="job.jobDescription" titleKey="tooltip.selectJobName" tabindex="1" disabled="%{#attr.screenFieldsStatus}" size="1" cssStyle="width:290px" onchange="jobDescriptionChange()" list="#request.jobNameList" listKey="value" listValue="label" />
						  
				    </div>
				 </s:if>
				 <s:if test='"change" == #request.methodName' > 
					<s:textfield name="scheduler.job.jobDescription"  tabindex="1" cssStyle="width:290px;" readonly="true" />
				 </s:if>
				 <s:if test='"view" == #request.methodName' > 
					 <s:textfield name="scheduler.job.jobDescription" tabindex="1" cssStyle="width:290px;" disabled="%{#attr.screenFieldsStatus}" />
				 </s:if>
				  </td>
				</tr>
			</table>
				
				
			 <table width="344" border="0" cellpadding="0" cellspacing="1" height="40">

			 <tr height="22px">
				 <td  width="75px"><b><s:text name="addjob.jobStatus"/></b></td>
				
				 <td width="13px">&nbsp;</td>
				
				 <td width="100px">
					 
					 <s:radio id="2" tabindex="2"  cssStyle="width:13px;" titleKey="tooltip.enable" name="scheduler.jobStatus" list="#{@org.swallow.util.SwtConstants@ADDJOB_ENABLE:''}"/><label  tabindex="2" title='<s:text name="tooltip.enable"/>' for="2" >&nbsp;&nbsp;<s:text name="addjob.enable"/>&nbsp;&nbsp;&nbsp;</label>
					 
				 </td>
				
				 <td width="100px"><s:radio id="3" tabindex="3" cssStyle="width:13px;" titleKey="tooltip.disable" name="scheduler.jobStatus" list="#{@org.swallow.util.SwtConstants@ADDJOB_DISABLE:''}"/><label tabindex="3" title='<s:text name="tooltip.disable"/>' for="3" >&nbsp;&nbsp;<s:text name="addjob.disable"/></label></td>
				
				 <td width="28px">&nbsp;</td>		
				
				 <td  width="55px">&nbsp;</td>
				
			</tr>

			</table>

				</div>
	 
</div>

	<div id="ddimagetabs"
		style=" left:10px; position: absolute;  top: 90px; width: 320px; height: 20px;">
	
		<a href="#" onmouseout="revertback('sc1',this);" tabindex="3" 
							onmouseover="changecontent('sc1',this)"
							onClick="changeselected('sc1');expandcontent('sc1', this)">
					<b><s:text name="addJob.tab.scheduling"/></b>
		</a>
		<a href="#" id="firstsc" onmouseout="onmouseoutfirstsc('sc2',this);" tabindex="4"
					onmouseover="onmouseoverfirstsc('sc2',this)"
					onClick="onClickfirstsc('sc2', this)" style="width: 120px;leftmargin:-20px;">
			<b><s:text name="addJob.tab.reportSettings"/></b>
      	</a> 
	</div>
	<div id="Line"
		style="position: absolute; left: 210px; top: 108px; width: 417px; height: 20px;">
	<table width="100%">
		<tr>
			<td><img src="images/tabline.gif" width="100%" height="1"></td>
		</tr>
	</table>
	</div>
<div id="sc1" class="tabcontent">
 <div id="JobDetails" style="z-index:99; position:absolute;left:10px; top:112px; width:615px; height:380px; border: 2px outset ; " color="#7E97AF">
 <div id="JobDetails" style="z-index:99;position:absolute; left:2px; top:6px; width:560px; height:22px; " color="#7E97AF">
	  <table width="550" border="0" cellspacing="1" cellpadding="0" height="22" style="margin-left: 10px;">
	    <tr height="22px">
		  <td  width="95" height="10px" align="left">&nbsp;<b><s:text name="addJob.title.frequency"/></b></td>
		   <td width="23px">&nbsp;</td>
		  <td width="375px" style="word-spacing: 2.9px;">
	
		
		  <s:radio id="4" tabindex="4" cssStyle="width:13px;" titleKey="tooltip.once" name="scheduler.jobType" list="#{'O':''}" disabled="%{#attr.screenFieldsStatus}" onclick="once();"/> 
		  <label title='<s:text name="tooltip.once"/>' for="4"><s:text name="addjob.Once"/></label>&nbsp;

		   <s:radio id="5" cssStyle="width:13px;" tabindex="5" titleKey="tooltip.cyclic" name="scheduler.jobType" list="#{'C':''}" disabled="%{#attr.screenFieldsStatus}" onclick="cyclic();"/>
			<label title='<s:text name="tooltip.cyclic"/>' for="5"><s:text name="addjob.Cyclic"/></label>&nbsp;

		  <s:radio id="6" tabindex="6" cssStyle="width:13px;" titleKey="tooltip.daily" name="scheduler.jobType" list="#{'D':''}" disabled="%{#attr.screenFieldsStatus}" onclick="daily();"/>
		  <label title='<s:text name="tooltip.daily"/>' for="6"><s:text name="addjob.Daily2"/></label>&nbsp;

		   <s:radio id="7" tabindex="7" cssStyle="width:13px;" titleKey="tooltip.weekly" name="scheduler.jobType" list="#{'W':''}" disabled="%{#attr.screenFieldsStatus}" onclick="weekly();"/> 
		  <label title='<s:text name="tooltip.weekly"/>'  for="7"><s:text name="addjob.Weekly2"/></label>&nbsp;

		  <s:radio id="8" tabindex="8" cssStyle="width:13px;" titleKey="tooltip.monthly" name="scheduler.jobType" list="#{'M':''}" disabled="%{#attr.screenFieldsStatus}"  onclick="monthly();"/>
		  <label title='<s:text name="tooltip.monthly"/>' for="8"><s:text name="addjob.Monthly2"/></label>
		  
		  
		  <s:radio id="9" tabindex="9" cssStyle="width:13px;" titleKey="tooltip.manual" name="scheduler.jobType" list="#{@org.swallow.util.SwtConstants@JOB_TYPE_MANUAL:''}" disabled="%{#attr.screenFieldsStatus}"  onclick="manual()"/>
		  <label title='<s:text name="tooltip.manual"/>' for="9"><s:text name="addjob.Manual"/></label>
		

		 
		  </td>
	  </tr>
	  </table>
	  
	  
	  <div style="left:8px; top:4px;height:40px;margin-left: 10px;">
	     <fieldset style="width:583px;border:2px groove;">
		<legend><s:text name="addjob.Once"/></legend>
		  <table width="381" border="0" cellspacing="0" cellpadding="0" height="22">
		  <tr height="22px">
		
		    <td width="100px" align="left">&nbsp;<b><s:text name="addjob.Date"/></b><input tabindex="-1" class="textAlpha" style="background:transparent; border: thin;" class="textAlpha" readonly name="OnceDateMandatory" size="1"></td>
		
			  <td width="50px" style="padding-right:25px;">&nbsp;</td>
			
		   <td  width="150px"  align="left">
		 
			<s:textfield  name="scheduler.scheduleDateAsString" disabled="%{#attr.screenFieldsStatus}" maxlength="10" readonly="false" onchange="validateField(this,'scheduler.scheduleDateAsString',dateFormat);" tabindex="11" cssStyle="width:78px;height: 21px;" titleKey="tooltip.enterDate"/>
		 

			<A title='<s:text name="tooltip.accountMonitorNew.date"/>'  name="datelink3" ID="datelink3" onClick="cal3.select(document.forms[0].elements['scheduler.scheduleDateAsString'],'datelink3','<s:property value="#request.session.CDM.dateFormatValue" />'); return false;"><img style="margin-bottom: -4px;" src="images/calendar-16.gif"></A>
			                          
		        </td>
				  <td width="28px">&nbsp;</td>
				  <s:if test='"view" != #request.methodName' > 
						
						   <td width="40px"   align="left"><nobr><b><s:text name="addjob.Time"/></b><input tabindex="-1"  class="textAlpha" style="background:transparent; border: thin;" class="textAlpha" readonly name="OnceTimeMandatory" size="1"></nobr></td>
					 </s:if>
          			 <s:if test='"view" == #request.methodName' >
						  <td width="40px"   align="left"><b><s:text name="addjob.Time"/></b></td>
					</s:if>
				 
				     <td width="28px">&nbsp;</td>
		  <td  width="45px">        
			<s:textfield  name="scheduler.scheduleTime" disabled="%{#attr.screenFieldsStatus}" tabindex="10"  maxlength="5" onchange="return validateField(this,'Number 2','timePat')"   cssStyle="width:43px;" titleKey= "tooltip.enterTime"/>
		  </td>
		  </tr>
		  </table>
	    </fieldset>
		</div>
		 <div style="left:8px; top:4px;height:67px;margin-left: 10px;">
		
	 
	  <fieldset style="width:583px;border:2px groove;">
		<legend><s:text name="tooltip.cyclic"/></legend>
		  <table width="286" border="0" cellspacing="0" cellpadding="0" height="50">
         <tr height="22px">
		
		  <td  width="135px"  align="left">&nbsp;<b><s:text name="addjob.CycleDuration"/></b><input tabindex="-1"  class="textAlpha" style="background:transparent; border: thin;" class="textAlpha" readonly name="CyclicDurationmandatory" size="1"></td>
		  
		   <td   width="155px" height="20px" align="left">
		    <s:textfield  name="scheduler.durationHoursasString" disabled="%{#attr.screenFieldsStatus}" maxlength="2"  tabindex="13" cssStyle="width:35px;" onchange="return validateField(this,'Number 2','numberPatExpand',23,0);" titleKey="tooltip.durationHours"/> : 
			<s:textfield  name="scheduler.durationMinsasString" disabled="%{#attr.screenFieldsStatus}" tabindex="11" maxlength="2"  cssStyle="width:35px;" onchange="return validateField(this,'Number 2','numberPatExpand',59,0);" titleKey="tooltip.durationMins" /> : 
			
			<s:textfield  name="scheduler.durationSecsasString"   disabled="%{#attr.screenFieldsStatus}" tabindex="12" maxlength="2"  cssStyle="width:35px;"  onkeydown ="return checkZero();"  onchange="return validateField(this,'Number 2','numberPatExpand',59,0);" titleKey="tooltip.durationSecs"/>
 			</td>
			</tr>
			<tr height="22px">
			 <td  width="133px"> </td>
     
			  <td   width="155px">&nbsp;<s:text name="addjob.Hours"/>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<s:text name="addjob.Minutes"/>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<s:text name="addjob.Seconds"/>
			  </td>
			</tr> 
	    </table>
	   </fieldset>
	   </div>
	  
		  <div style="left:8px; top:4px;height:41px;margin-left: 10px;">
	     <fieldset style="width:583px;border:2px groove;">
		<legend><s:text name="addjob.Daily"/></legend>
		  <table width="178" border="0" cellspacing="1" cellpadding="0" height="22">
		  <tr height="22px">
					<s:if test='"view" != #request.methodName' > 
							
						    <td width="100px" align="left">&nbsp;<b><s:text name="addjob.Time"/></b><input tabindex="-1"  class="textAlpha" style="background:transparent; border: thin;" class="textAlpha" readonly name="dailyTimeMandatory" size="1"></td>
					 </s:if>
          			 <s:if test='"view" == #request.methodName' >
						   <td width="100px" align="left">&nbsp;<b><s:text name="addjob.Time"/></b></td>
					</s:if>
		 
			  <td width="28px">&nbsp;</td>
		 <td width="45px">
				<s:textfield name="scheduler.scheduleTime" disabled="%{#attr.screenFieldsStatus}"  tabindex="13" maxlength="5" onchange="return validateField(this,'Number 2','timePat');"   cssStyle="width:45px;" titleKey="tooltip.enterTime"/></td>
				</tr>
		</table>
		 </fieldset>
		 </div>

		    <div style="left:8px; top:4px;height:127px;margin-left: 10px;">
		 <fieldset style="width:583;border:2px groove;">
		<legend><s:text name="addjob.Weekly"/></legend>
		  <table width="367" border="0" cellspacing="0" cellpadding="0" height="110">
		   
			  <tr height="22px">
					<s:if test='"view" != #request.methodName' > 
							
						     <td width="100px">&nbsp;<b><s:text name="addjob.Time"/></b><input  tabindex="-1" class="textAlpha" style="background:transparent; border: thin;" class="textAlpha" readonly name="weeklyTimeMandatory" size="1"></td>
					 </s:if>
          			 <s:if test='"view" == #request.methodName' >
						  <td width="100px">&nbsp;<b><s:text name="addjob.Time"/></b></td>
					</s:if>
			   
				  <td width="28px">&nbsp;</td>
				 

				 <td width="100px">
					<s:textfield name="scheduler.scheduleTime" disabled="%{#attr.screenFieldsStatus}"  tabindex="14" maxlength="5" onchange="return validateField(this,'Number 2','timePat');"  cssStyle="width:45px;" titleKey="tooltip.enterTime"/>
					</td>
					</tr>
					<tr height="22px">
					  <td width="100px" ></td>
					    <td width="28px">&nbsp;</td>
			        <td width="100px">
					<s:checkbox cssStyle="width:13px;" name="scheduler.scheduleDayAll" tabindex="15"  disabled="%{#attr.screenFieldsStatus}"  fieldValue="ALL" value='%{#request.scheduler.scheduleDayAll == "ALL"}' onclick="weeklyDisableOthers();" titleKey="tooltip.all"/>&nbsp;&nbsp;<s:text name="addjob.All"/>  
				    </td>
					<td width="28px">&nbsp;</td>
			    
			   
				<td width="100px">
				<s:checkbox cssStyle="width:13px;" name="scheduler.scheduleDaySun" tabindex="16" disabled="%{#attr.screenFieldsStatus}" fieldValue="SUN" value='%{#request.scheduler.scheduleDaySun == "SUN"}' onclick="weeklyDisableAll();" titleKey="tooltip.sunday"/>&nbsp;&nbsp;<s:text name="addjob.Sunday"/> 
				</td>
				</tr>
				<tr height="22px">
				 <td width="100px" ></td>
				 <td width="28px">&nbsp;</td>
			
				 <td width="100px">
				<s:checkbox cssStyle="width:13px;" name="scheduler.scheduleDayMon" tabindex="17" disabled="%{#attr.screenFieldsStatus}" fieldValue="MON" value='%{#request.scheduler.scheduleDayMon == "MON"}' onclick="weeklyDisableAll();" titleKey="tooltip.monday"/> &nbsp;<s:text name="addjob.Monday"/> 
			  </td>
			  <td width="28px">&nbsp;</td>
			<td width="100px">
				<s:checkbox cssStyle="width:13px;" name="scheduler.scheduleDayTue" tabindex="18" disabled="%{#attr.screenFieldsStatus}" fieldValue="TUE" value='%{#request.scheduler.scheduleDayTue == "TUE"}' onclick="weeklyDisableAll();" titleKey="tooltip.tuesday"/>&nbsp;&nbsp;<s:text name="addjob.Tuesday"/> 
				</td>
				</tr>
				<tr height="22px">
				 <td width="100px" ></td>
				 <td width="28px">&nbsp;</td>
			
				  <td width="100px">
					
			
				<s:checkbox cssStyle="width:13px;" name="scheduler.scheduleDayWed" tabindex="19" disabled="%{#attr.screenFieldsStatus}" fieldValue="WED" value='%{#request.scheduler.scheduleDayWed == "WED"}' onclick="weeklyDisableAll();" titleKey="tooltip.wednesday"/> &nbsp;<s:text name="addjob.Wednesday"/> 
			  </td>
			  <td width="28px">&nbsp;</td>
			<td width="100px">
				<s:checkbox cssStyle="width:13px;" name="scheduler.scheduleDayThr" tabindex="20" disabled="%{#attr.screenFieldsStatus}" fieldValue="THU" value='%{#request.scheduler.scheduleDayThr == "THU"}' onclick="weeklyDisableAll();" titleKey="tooltip.thursday"/>&nbsp;&nbsp;<s:text name="addjob.Thursday"/> 
			  </td>
		  </tr>
		<tr height="22px">
				 <td width="100px" ></td>
				 <td width="28px">&nbsp;</td>
			
				 <td width="100px">
					
				<s:checkbox cssStyle="width:13px;" name="scheduler.scheduleDayFri" tabindex="21" disabled="%{#attr.screenFieldsStatus}" fieldValue="FRI" value='%{#request.scheduler.scheduleDayFri == "FRI"}' onclick="weeklyDisableAll();" titleKey="tooltip.friday"/>&nbsp;&nbsp;<s:text name="addjob.Friday"/> 
			  </td>
			  <td width="28px">&nbsp;</td>
			   <td width="100px">
				<s:checkbox cssStyle="width:13px;" name="scheduler.scheduleDaySat" tabindex="22" disabled="%{#attr.screenFieldsStatus}" fieldValue="SAT" value='%{#request.scheduler.scheduleDaySat == "SAT"}'  onclick="weeklyDisableAll();" titleKey="tooltip.saturday"/>&nbsp;&nbsp;<s:text name="addjob.Saturday"/>  
				
			  </td>
		  </tr>
		 
		 </table>
			
			 
		  </fieldset>
		  </div>
		   <div style="left:8px; top:4px;height:61px;margin-left: 10px;">
		 <fieldset style="width:583;border:2px groove;">
		<legend><s:text name="addjob.Monthly"/></legend>
		  <table width="385" border="0" cellspacing="0" cellpadding="0" height="22">
		 <tr height="22px">
		  
		   <td width="100px"  >&nbsp;<nobr><b><s:text name="addjob.DayMonth"/></b><input  tabindex="-1" class="textAlpha" style="background:transparent; border: thin;" class="textAlpha" readonly name="monthlyDayMandatory" size="1"></nobr></td>
		  
		     <td width="11px">&nbsp;</td>
			
		    <td width="112px">
					<s:textfield name="scheduler.monthDateAsString" disabled="%{#attr.screenFieldsStatus}" tabindex="23"  maxlength="2" onchange="return validateField(this,'Number 2','numberPat',31, 1);" onkeyup = "validationForMonthTypeDate()" cssStyle="width:20px;" titleKey="tooltip.enterDayMonth"/>
					</td>
				 <td width="28px">&nbsp;</td>
				 <s:if test='"view" != #request.methodName' > 
						
						   <td width="45px"><nobr><b><s:text name="addjob.Time"/><input tabindex="-1" class="textAlpha" style="background:transparent; border: thin;" class="textAlpha" readonly name="monthlyTimeMandatory" size="1"></b></nobr></td>
					 </s:if>
          			 <s:if test='"view" == #request.methodName' >
						 <td width="45px"><b><s:text name="addjob.Time"/></b></td>
					</s:if>
			 
			  
			    <td width="28px">&nbsp;</td>
				 <td width="50px">			   
					<s:textfield name="scheduler.scheduleTime" disabled="%{#attr.screenFieldsStatus}" tabindex="24" maxlength="5" cssStyle="width:43px;"  onchange="return validateField(this,'Number 2','timePat');" titleKey="tooltip.enterTime"/>
				 </td>
				</tr>
				</table>
				 <table width="356" border="0" cellspacing="0" cellpadding="0" height="22">
				<tr height="22px">
				  <td width="104px">&nbsp;</td>
				   <td width="28px">&nbsp;</td>
		          <td width="104px">
					
					
				<s:checkbox cssStyle="width:13px;" name="scheduler.monthFirst" tabindex="25" disabled="%{#attr.screenFieldsStatus}" onclick = "validationForMonthTypeFirstDay()" fieldValue="Y" value='%{#request.scheduler.monthFirst == "Y"}' titleKey="tooltip.firstDay"/> &nbsp;<s:text name="addjob.FirstDate"/> </td>
				  
				   <td width="20px">&nbsp;</td>
				  
				     <td width="100px">
					
			
			  
			<s:checkbox cssStyle="width:13px;" name="scheduler.monthLast" tabindex="26" disabled="%{#attr.screenFieldsStatus}" fieldValue="Y" value='%{#request.scheduler.monthLast == "Y"}' onclick = "validationForMonthTypeLastDay()" titleKey="tooltip.lastDay"/> &nbsp<s:text name="addjob.LastDate"/> 
		  </td>
			  </tr>
			 
			
			  </table>
		    </fieldset>
			</div>
	 
	  </div>
</div>
</div>
<div id="sc2" class="tabcontent">
	<div id="JobDetails" style="z-index:99; position:absolute;left:10px; top:112px; width:615px; height:380px; border: 2px outset ; " color="#7E97AF">
		<div id="ddimagebuttons" style="z-index:99;position:absolute; left:2px; top:6px; width:560px; height:22px; " color="#7E97AF">
			<table width="610" border="0" cellpadding="2" cellspacing="0" height="22" style="padding-top: 3px; padding-left: 10px;">
				<tr height="25px">	
				 	<s:hidden name="scheduledReportParams.reportConfig" /> 
					<td    align="left"><b><s:text name="addJob.title.reportType"/></b>*</td> 
				  	<td width="93px">
						<div style="width:290px">
							<s:select id="scheduledReportParams.reportTypeId" name="scheduledReportParams.reportTypeId" titleKey="tooltip.SelectReportType" tabindex="1" disabled="%{#attr.screenFieldsStatus}" size="1" cssStyle="width:290px" onchange="onReportTypeChange()" list="#request.reportTypeList" listKey="value" listValue="label" />
				  			
					    </div>
				 	</td>
				</tr>
				<tr height="25px">	
					<td    align="left"><b><s:text name="addJob.title.reportName"/></b>*</td> 
				  	<td width="93px">
						<div style="width:290px">
							<s:textfield name="scheduledReportParams.reportName" titleKey="tooltip.selectJobName" tabindex="1" maxlength="60" cssStyle="width:395px;" onblur="maxLengthTextArea(this,60);" onkeydown="maxLengthTextArea(this,60);" onkeyup="maxLengthTextArea(this,60);"/>
					    </div>
				 	</td>
				</tr>
				<tr height="25px">	
					<td    style="width: 165px !important" align="left"><b><s:text name="addJob.title.reportDecription"/></b></td> 
				  	<td width="93px">
						<div style="">
							<s:textarea name="scheduledReportParams.reportDesc" titleKey="tooltip.jobDescription" tabindex="1" cssStyle="width:395px;height: 70px"  onchange="maxLengthTextArea(this,250);" onblur="maxLengthTextArea(this,250);" onkeyup="maxLengthTextArea(this,250);" onkeydown="maxLengthTextArea(this,250);" />
					    </div>
				 	</td>
				</tr>
				<tr height="25px">	
					<td   align="left"><b><s:text name="addJob.title.evaluateRunDateAs"/></b></td> 
				  	<td width="40px">
						<s:radio id="10" tabindex="10" cssStyle="width:13px;" titleKey="tooltip.jobEvaluateRunDate" name="scheduledReportParams.mapDateMethod" list="#{@org.swallow.util.SwtConstants@MAP_DATE_SYSTEM:''}"/>
		  				<label title='<s:text name="addJob.title.evaluateRunDateAs.value1"/>' for="10"><s:text name="addJob.title.evaluateRunDateAs.value1"/></label>&nbsp;&nbsp;&nbsp; 
				 		<s:radio id="11" tabindex="11" cssStyle="width:13px;" titleKey="tooltip.jobEvaluateRunDate" name="scheduledReportParams.mapDateMethod" list="#{@org.swallow.util.SwtConstants@MAP_DATE_ENTITY:''}"/>
		  				<label title='<s:text name="addJob.title.evaluateRunDateAs.value2"/>' for="11"><s:text name="addJob.title.evaluateRunDateAs.value2"/></label>
				 	</td>
				</tr>
				<tr height="25px">	
					<td    align="left"><b><s:text name="addJob.title.executeAsRole"/>*</b></td> 
				  	<td width="93px">
						<div style="width:290px">
							 <div style="display: inline-table;width:120px; height: 22px;position:relative;float:left">
								<s:select id="scheduledReportParams.executionRole" name="scheduledReportParams.executionRole" tabindex="4" cssClass="htmlTextAlpha" cssStyle="width:140px" onchange="onRoleChange()" titleKey="tooltip.jobExecuteAsRole" disabled="%{#attr.screenFieldsStatus}" list="#request.roleList" listKey="value" listValue="label" />
								
						  	</div> &nbsp;&nbsp;&nbsp; 
							<span  style="display: inline;position: absolute;margin-top: 2px; " id="roleName" name="roleName" class="spantext">
					    </div>
				 	</td>
				</tr>
				<tr height="25px">	
				  	<td align="left"><b><s:text name="addJob.title.parameters"/>*</b></td>
					<td width="70" id="configureButton">
						<a  tabindex="8"
							title='<s:text name="addJob.button.configure"/>'
							onMouseOut="collapsebutton(this)"
							onMouseOver="highlightbutton(this)"
							onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)"
							onclick="javascript:buildConfigParams()"><s:text name="addJob.button.configure"/>
						</a>
						<a  tabindex="8"
							id="showXMLButton"
							title='<s:text name="screen.showXML"/>'
							onMouseOut="collapsebutton(this)"
							onMouseOver="highlightbutton(this)"
							onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)"
							onclick="javascript:showXML()"><s:text name="screen.showXML"/>
						</a>
						<label id="parametersStatusInfo" style="font-style:italic; left: 340px; top: 189px; width:240px; position: absolute;" onclick="showAlertConfigIncorrectCause()" onmouseout="onMouseOutParamStatus()" onmouseover="onMouseOverPararmStatus()"></label>
					</td>
				</tr>
				<tr height="25px">	
					<td    align="left"><b><s:text name="addJob.title.outputFileType"/></b></td> 
				  	<td width="93px">
						<div style="width:290px">
								<s:textfield name="scheduledReportParams.outputFormat" titleKey="tooltip.jobOutputFileType" tabindex="1" cssStyle="width:80px;"/>								
					    </div>
				 	</td>
				</tr>
				<tr height="25px">	
					<td    align="left"><b><s:text name="addJob.title.outputFileLocation"/></b></td> 
				  	<td width="93px">
						<div style="width:140px">
							<s:textfield name="scheduledReportParams.outputLocation" titleKey="tooltip.jobFileLocation" tabindex="1" cssStyle="width:395px;"/>
					    </div>
				 	</td>
				</tr>
				<tr height="25px">	
					<td  align="left"><b><s:text name="addJob.title.fileNamePrefix"/></b></td> 
				  	<td width="93px">
						<div style="width:290px">
							<s:textfield name="scheduledReportParams.fileNamePrefix"  titleKey="tooltip.jobNamePrefix" tabindex="1" cssStyle="width:395px;"/>
					    </div>
				 	</td>
				</tr>
				<tr height="25px">	
					<td align="left"><b><s:text name="addJob.title.retainFilesFor"/></b></td>
				  	<td width="150px">
						<div style="width:80px">
							<s:textfield name="scheduledReportParams.retainDays" titleKey="tooltip.jobRetainFilesFor" onchange="return validateField(this,'pwd.specialChar','numberPat');" onkeydown="maxLengthTextArea(this,4);" tabindex="1" cssStyle="width:80px;"/>
					    </div>
				 	</td>
				 	
					<td width="30px" align="left">
					<div style="left: 275px; top: 290px; position: absolute;">
						<b><s:text name="addJob.title.days"/></b>
					</div>
					</td>
				</tr>
				<tr height="25px">	
					<td   align="left"><b><s:text name="addJob.title.accessList"/></b></td> 
				  	<td width="70">
						<a  tabindex="8"
							title='<s:text name="tooltip.jobAccessList"/>'
							onMouseOut="collapsebutton(this)"
							onMouseOver="highlightbutton(this)"
							onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)"
							onclick="javascript:openWindow(buildAccessList('displayAccessList'),'alertmessagechange','left=50,top=190,width=560,height=455,toolbar=0, resizable=yes, scrollbars=yes','true')"><s:text name="addJob.title.accessList"/>
						</a>
					</td>
				</tr>
			</table>
		</div>
	</div>
</div>
<div id="SchedulerMaintenance" style="position:absolute; left:540; top:510; width:70; height:25px; visibility:visible;">
	<table width="60" border="0" cellspacing="0" cellpadding="0" height="20">
		<tr>

		<td align="Right">
		
			<s:if test='"add" == #request.methodName' >
				<td align="Right"><a
					title='<s:text name="tooltip.helpScreen"/>' tabindex="29"
					href=#
					onclick="javascript:openWindow(buildPrintURL('print','Add Job Details'),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')"
					onKeyDown="submitEnter(this,event)"
					onMouseOut="MM_swapImgRestore()"
					onMouseOver="MM_swapImage('Help','','images/help_default.GIF ',1)"><img
					src="images/help_default.GIF " name="Help" border="0"></a></td>
			</s:if>
			<s:if test='"change" == #request.methodName' >
				<td align="Right"><a
					title='<s:text name="tooltip.helpScreen"/>' tabindex="29"
					href=#
					onclick="javascript:openWindow(buildPrintURL('print','Change Job Details'),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')"
					onKeyDown="submitEnter(this,event)"
					onMouseOut="MM_swapImgRestore()"
					onMouseOver="MM_swapImage('Help','','images/help_default.GIF ',1)"><img
					src="images/help_default.GIF " name="Help" border="0"></a></td>
			</s:if>
			<s:if test='"view" == #request.methodName' >
				<td align="Right"><a
					title='<s:text name="tooltip.helpScreen"/>' tabindex="29"
					href=#
					onclick="javascript:openWindow(buildPrintURL('print','View Job Details'),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')"
					onKeyDown="submitEnter(this,event)"
					onMouseOut="MM_swapImgRestore()"
					onMouseOver="MM_swapImage('Help','','images/help_default.GIF ',1)"><img
					src="images/help_default.GIF " name="Help" border="0"></a></td>
			</s:if>
			
	   </td>

			<td align="right" id="Print">
				<a tabindex="30" onclick="printPage();"  onMouseOut="MM_swapImgRestore()" onKeyDown="submitEnter(this,event)" onMouseOver="MM_swapImage('Print','','images/Print_R.gif ',1)"><img src="images/Print.gif " name="Print"  border="0" title='<s:text name="tooltip.printScreen"/>'/> </a>	
			</td>
		</tr>
	</table>
</div>
<DIV ID="caldiv" STYLE="z-index:99;position:absolute;visibility:visible;background-color:white;layer-background-color:white;"></DIV>

<!------------------------ BUTTONS STARTS ----------------------------------------------------------------------->

<div id="ddimagebuttons" color="#7E97AF" style="position:absolute; border:2px outset; left:10; top:500px; width:615px; height:39px; visibility:visible;">
	<div id="addJobMaintenance" style="position:absolute; left:4; top:4; width:615px; height:15px; visibility:visible;">
		<table width="140" border="0" cellspacing="0" cellpadding="0" height="20">
			<tr>
				<s:if test='"add" == #request.methodName' > 
				<td width="70" title='<s:text name="tooltip.save"/>' id="savebutton">
				<a tabindex="27" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onClick="javascript:checkParametersConifBeforeSubmit('add')"><s:text name="button.save"/></a>
				</td>
				</s:if>
	
				<s:if test='"change" == #request.methodName' > 
				<td  width="70" title='<s:text name="tooltip.save"/>' id="savebutton">
				<a tabindex="27" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onKeyDown="submitEnter(this,event)"  onClick="javascript:checkParametersConifBeforeSubmit('change')"><s:text name="button.save"/></a>
				</td>
				</s:if>
	
				<s:if test='"change" == #request.methodName' > 
							<td width="70" id="cancelbutton">		
								<a tabindex="28" title='<s:text name="tooltip.cancel"/>' onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onKeyDown="submitEnter(this,event)" onclick="confirmClose('C');"><s:text name="button.cancel"/></a>			
							</td>
				</s:if>
				<s:if test='"add" == #request.methodName' > 
							<td width="70" id="cancelbutton">		
								<a tabindex="28" title='<s:text name="tooltip.cancel"/>'onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onKeyDown="submitEnter(this,event)" onclick="confirmClose('C');"><s:text name="button.cancel"/></a>			
							</td>
				</s:if>
				
				<s:if test='"view" == #request.methodName' > 
							<td width="70" title='<s:text name="tooltip.close"/>' id="closebutton">		
								<a tabindex="27" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onKeyDown="submitEnter(this,event)" onclick="javascript:window.close();"><s:text name="button.close"/></a>			
							</td>
				</s:if>
			</tr>
		</table>
	</div>
</div>

<!------------------------ BUTTONS ENDS ----------------------------------------------------------------------->

<blockquote>&nbsp;</blockquote>
		<p>&nbsp;</p>
<script type="text/javascript">
</script>
</s:form>
</body>
</html>