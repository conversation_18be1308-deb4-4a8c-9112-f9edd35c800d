 <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@ include file="/taglib.jsp"  %>
<%@ page import="org.swallow.util.SwtConstants" %>
<html>
<head>
<title><s:text name="screenProfile.title.window"/></title>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
 
</head>

<SCRIPT language="JAVASCRIPT">

//START: Code changed/added by Arumugam on 15-Jul-2010 for Mantis 1177 :To validate the profile name text box	
function validateForm(objForm){
  var elementsRef = new Array(1);
  elementsRef[0] = objForm.elements["userprofiles.userProfileName"];
  
  return validate(elementsRef);
}
function submitForm(methodName){
if(methodName!='displayProfile')	
{	var flag = validateField(document.forms[0].elements['userprofiles.userProfileName'],'userprofiles.userProfileName','alphaNumPatWithSpace');
	if(flag  )
		{
		document.forms[0].method.value = methodName;
		if(validateForm(document.forms[0]))
			document.forms[0].submit();
		}	

	}
	else{
			document.forms[0].method.value = methodName;
			document.forms[0].submit();
	
	}
	
	}

//END: Code changed/added by Arumugam on 15-Jul-2010 for Mantis 1177 :To validate the profile name text box
//closeWindow
function bodyonload()
{
setParentChildsFocus();
setFocus(document.forms[0]);
<s:if test='"yes" == #request.closeWindow' >

	var optionColl = getMenuWindow().document.forms[0].elements["user.profileId"].options;
	var profileId = document.forms[0].elements["userprofiles.id.profileId"].value;
	var profileName = document.forms[0].elements["userprofiles.userProfileName"].value;

	for(var idx = 0 ; idx < optionColl.length; ++idx)
	{
		if(optionColl[idx].value == profileId )
			optionColl[idx].text = profileName;
	}

	alert('<s:text name="screenProfile.alert.profileChanged"/>');
	self.close();
</s:if>




}

</SCRIPT>
<body leftmargin="0" topmargin="0" marginheight="0" onLoad="bodyonload()" onunload="call()">

<s:form action="userprofiles.do">

<input name="method" type="hidden" value="display">
<!-- <s:hidden property="userprofiles.userProfileName"/>  -->
 

<div id="UserProfile" style="position:absolute; left:20px; top:20px; width:414px; height:85px; border:2px outset;"color="#7E97AF">

  <div id="UserProfile" style="position:absolute; left:8px; top:4px; width:395px; height:38px;">
	<table width="390" border="0" cellpadding="0" cellspacing="0" height="25">
   		<tr>
		  <td width="179"align="left"><b><s:text name="userprofile.option"/></b></td>
		  <td width="28">&nbsp;</td>
		  <!--START: Code changed/added by Arumugam on 15-Jul-2010 for Mantis 1177 :To display tool tip-->
		  <td height="23" width="188" ><s:select id="userprofiles.id.profileId" name="userprofiles.id.profileId"  titleKey="tooltip.enterProfileOption" tabindex="1" cssStyle="width: 152" onchange="submitForm('displayProfile')" list="#request.userProfileList" listKey="value" listValue="label" />
 	   		
			
		<!--END: Code changed/added by Arumugam on 15-Jul-2010 for Mantis 1177 :To display tool tip-->
		</td>		
		</tr>

		<tr style="height:30">	
		<!--START: Code changed/added by Arumugam on 15-Jul-2010 for Mantis 1177 :To validate the profile name text box-->
            <td height="23" width="179"><b><s:text name="userprofile.name"/></b>*</td>
		<!--END: Code changed/added by Arumugam on 15-Jul-2010 for Mantis 1177 :To validate the profile name text box-->
            <td width="28">&nbsp;</td>
		    <td width="188">
		<!--START: Code changed/added by Arumugam on 15-Jul-2010 for Mantis 1177 :To validate the profile name text box-->
		     <s:textfield titleKey="tooltip.enterProfileName" cssClass="htmlTextAlpha" tabindex="2" name="userprofiles.userProfileName" cssStyle="width:188px;" maxlength="20" onchange="return validateField(this,'userprofiles.userProfileName','alphaNumPatWithSpace');"/> 
		<!--END: Code changed/added by Arumugam on 15-Jul-2010 for Mantis 1177 :To validate the profile name text box-->	 
		 </td>
		</tr>

		<tr>
		   <td width="179"><b><s:text name="userprofile.default"/></b></td>
		   <td width="28">&nbsp;</td>
		   <td width="188">
		   <s:checkbox titleKey="tooltip.defaultProfile" tabindex="2" cssStyle="width:13;" name="userprofiles.currentProfile" fieldValue="Y" value='%{#request.userprofiles.currentProfile == "Y"}'/>
    		</td>
    	</tr>

		<!-- <tr>	
            <td width="179"><b><s:text name="userprofile.overwrite"/></b></td>
            <td width="28">&nbsp;</td>
		    <td width="188">
		      <s:if test='"save" == #request.methodName' > 
			      <s:checkbox titleKey="tooltip.saveProfile" tabindex="3" cssStyle="width:13;" name="userprofiles.overWriteFlag" disabled="true"/>
			 </s:if>
			  <s:if test='"update" == #request.methodName' > 
			      <s:checkbox titleKey="tooltip.overwriteExistingProfile" tabindex="3" cssStyle="width:13;" name="userprofiles.overWriteFlag"  fieldValue="Y" value='%{#request.userprofiles.overWriteFlag == "Y"}'/>
			</s:if> 
		 </td>
		</tr> -->

          
	</table>
  </div>
</div>
 

 <div id="user" style="position:absolute ;left:360; top:119px; width:70px; height:39px; visibility:visible;">
	<table width="60" border="0" cellspacing="0" cellpadding="0" height="20">
		<tr>

		<td align="Right">
			<a tabindex="6" href=# onclick="javascript:openWindow(buildPrintURL('print','Screen Profile '),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')" onMouseOut="MM_swapImgRestore()" onMouseOver="MM_swapImage('Help','','images/help_default.GIF ',1)"><img src="images/help_default.GIF " name="Help"  border="0" title='<s:text name="tooltip.helpScreen"/>' ></a> 
		</td>

			<td align="right" id="Print">
				<a tabindex="6" onclick="printPage();" onMouseOut="MM_swapImgRestore()" onMouseOver="MM_swapImage('Print','','images/Print_R.gif ',1)"><img src="images/Print.gif " name="Print"  border="0" title='<s:text name="tooltip.printScreen"/>'></a>	
			</td>
		</tr>
	</table>
</div>
 
 
 <div id="ddimagebuttons" color="#7E97AF" style="position:absolute; border:2px outset; left:20; top:110; width:414px; height:39px; visibility:visible;">
<div id="UserProfile" style="position:absolute; left:6; top:4; width:350px; height:15px; visibility:visible;">
  <table width="140" border="0" cellspacing="0" cellpadding="0" height="20">
    <tr>
	  <td>
	     <a title='<s:text name="tooltip.save"/>' tabindex="4"onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onClick="javascript:submitForm('${methodName}');"><s:text name="button.save"/></a></td>

	    <td>
	        <a title='<s:text name="tooltip.cancel"/>' tabindex="5" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onClick="javascript:window.close();"><s:text name="button.cancel"/></a></td>
	</tr>
  </table>
</div>
</div>
	<blockquote>&nbsp;</blockquote>
	<p>&nbsp;</p>
<input type="hidden" name="wndsetting" value='${wndsetting}'/>	



</s:form>	
</body>
</html>