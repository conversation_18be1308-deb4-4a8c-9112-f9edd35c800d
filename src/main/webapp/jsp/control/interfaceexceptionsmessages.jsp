<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<!--
  - The main purpose of this jsp file is to load the resultant xml data for Interface Exceptions screen.
  - 
  - Author(s): Marshal <PERSON> .I
  - Date: 20-June-2011
  -->
<%@ include file="/taglib.jsp"%>
<%@ page import="org.swallow.util.CommonDataManager"%>
<%@ page import="org.swallow.util.SwtConstants"%>
<%@ page import="org.swallow.util.SwtUtil"%>
<%@ include file="/angularJSUtils.jsp"%>


<html lang="en">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
<link rel="stylesheet" href="angularSources/styles.css?version=<%= SwtUtil.appVersion %>">
</head>
<body>	
<script type="text/javascript" src="jsp/work/assets/swfobject.js"></script>
<script type="text/javascript">

	var fromPCM = '${requestScope.fromPCM}';
	var PCM = new String('<%="yes".equals(request.getAttribute("fromPCM")) ? " (PCM)":""%>');
	var screenTitle = "";
	screenTitle = getMessage("interfaceExceptionsMonitor.title.window", null) + PCM;
	document.title = screenTitle;
	var baseURL = new String('<%=request.getRequestURL()%>');
	var screenRoute = "InterfaceException";
	/**
	* This function is used to open the Interface Exceptions Message screen.
	*
	* @param a
	* @param left
	* @param top
	* @param width
	* @param height
	*/
	function openJavaWindow(a, left, top, width, height) {
		openWindow(a,'rolemaintenancechangeWindow','left='+left+',top='+top+',width='+width+',height='+height+',toolbar=0, resizable=yes, scrollbars=yes,status=yes');
	}
	
	/**
	* This function is called on load of the screen
	*
	*/
	window.onload = function () {
		// Calls the function to focus on child
		setParentChildsFocus();
		// Calls the function to set the title suffix
		setTitleSuffix(document.forms[0]); 
	}
	 
	/**
	* This function is used to refresh the parent screen
 	*/ 
	function refreshParent () {
		opener.refresh = true;	 
	}
	
	/**
	* This function opens the Help pop-up window for Interface Exceptions screen.			
	*/
	function help(){
		openWindow(buildPrintURL('print','Interface Exceptions'),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')
    }
</script>


<style>
body {
	margin: 0px;
	overflow: hidden
}

#workFlowMonitorcontent {
	border: solid 0px #000;
	width: 100%;
	height: 100%;
	float: left;
	margin: 0px 0px;
}
</style>


<%@ include file="/angularscripts.jsp"%>
<html:form action="interfacemonitor.do">
	<input name="method" type="hidden" value="">
</html:form>
</body>
</html>