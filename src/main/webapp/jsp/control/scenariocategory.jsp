<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@ include file="/taglib.jsp"  %>
<%@ page import="org.swallow.util.SwtConstants" %>
<%@page import="org.swallow.control.model.ScenarioCategory"%>

<html>
<head>
<title><s:text name="scenarioCategory.title.window"/></title>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">


<SCRIPT language="JAVASCRIPT">

	var menuEntityCurrGrpAccess = "${menuAccessId}";
	
	/**
	 * Called on first load of the screen
	 */
	function bodyOnLoad()
	{
		xl = new XLSheet("scenarioCategoryDetails","table_2", ["String","String", "String", "String","String","Number"],"111111");
		
		xl.onsort = xl.onfilter = disableButtons;
		
		highlightTableRows("scenarioCategoryDetails");	
		document.getElementById("addbutton").innerHTML = document.getElementById("adddisablebutton").innerHTML;
		document.getElementById("changebutton").innerHTML = document.getElementById("changedisablebutton").innerHTML;
		document.getElementById("deletebutton").innerHTML = document.getElementById("deletedisablebutton").innerHTML;
		
	
		var buttonStatus = "<%= request.getAttribute(SwtConstants.ADD_BUT_STS) %>";	
		document.getElementById("addbutton").innerHTML = (buttonStatus=="<%=SwtConstants.STR_TRUE%>"?document.getElementById("addenablebutton").innerHTML:document.getElementById("adddisablebutton").innerHTML);	
		
		 buttonStatus = "<%= request.getAttribute(SwtConstants.CHG_BUT_STS) %>";	
		document.getElementById("changebutton").innerHTML = (buttonStatus=="<%=SwtConstants.STR_TRUE%>"?document.getElementById("changeenablebutton").innerHTML:document.getElementById("changedisablebutton").innerHTML);	
				
		 buttonStatus = "<%= request.getAttribute(SwtConstants.DEL_BUT_STS) %>";	
		document.getElementById("deletebutton").innerHTML = (buttonStatus=="<%=SwtConstants.STR_TRUE%>"?document.getElementById("deleteenablebutton").innerHTML:document.getElementById("deletedisablebutton").innerHTML);	
						
		document.forms[0].menuAccessId.value = "${requestScope.menuAccessId}";
		
	}
	
	/**
	 * Dispatched when the user selects a row in the grid 
	 */
	function selectTableRow(e)
	{
              var event = (window.event|| e);
		var target = (event.srcElement || event.target)
		var srcEl = target;
		while(srcEl.tagName != 'TD')
		{
			srcEl = srcEl.parentElement;
		}
		
		var rowElement = srcEl.parentElement;
		var tblElement = srcEl.parentElement.parentElement.parentElement;
	
		var isRowSel = isRowSelected(rowElement);
		resetTableRowsStyle(tblElement);
		
		if(isRowSel == false)
			rowElement.className = 'selectrow' ;
	
		onSelectTableRow(rowElement,!isRowSel);
	}
	
	/**
	 * This function is called when the user selects a row, it will change the visual of buttons
	 * @ rowElement
	 * @ isSelected 
	 */
	function onSelectTableRow(rowElement , isSelected)
	{
		var categoryId = rowElement.getElementsByTagName("input")[0];
		var systemFlag = rowElement.getElementsByTagName("input")[1].value;
		document.forms[0].selectedCategoryId.value = categoryId.value;
		document.forms[0].categorySystemFlag.value = systemFlag;
		document.getElementById("changebutton").innerHTML = (isSelected&&menuEntityCurrGrpAccess == "0"?document.getElementById("changeenablebutton").innerHTML:document.getElementById("changedisablebutton").innerHTML);
		document.getElementById("deletebutton").innerHTML =(isSelected&&menuEntityCurrGrpAccess == "0"&&systemFlag=="N"?document.getElementById("deleteenablebutton").innerHTML:document.getElementById("deletedisablebutton").innerHTML);
		
	 }
	
	/**
	 * Disable buttons and colors when the user does not select any row in the grid
	 */
	function disableButtons(){
		document.getElementById("changebutton").innerHTML = document.getElementById("changedisablebutton").innerHTML;
		updateColors();
	}
	
	/**
	 * delete the selected role after clicking on delete button
	 * @param methodName
	 */
	function submitDeleteForm(methodName){
		var appName = "<%=SwtUtil.appName%>";
		var requestURL = new String('<%=request.getRequestURL()%>');
		var idy = requestURL.indexOf('/'+appName+'/');
		requestURL=requestURL.substring(0,idy+1) ; 
		requestURL = requestURL + appName+"/scenMaintenance.do?method=categoryHasChildren";
		requestURL = requestURL + "&categoryId="+document.forms[0].selectedCategoryId.value;
		var oXMLHTTP = new XMLHttpRequest();
		oXMLHTTP.open( "POST", requestURL, false );
		oXMLHTTP.send();
		hasChildren=new String(oXMLHTTP.responseText);
		if(hasChildren=="true")
			alert('<s:text name="alert.categoryAssociatedScenarios"/>');
		else{
			document.forms[0].method.value = methodName;
			var yourstate=window.confirm("<s:text name='confirm.delete'/>");
			if (yourstate==true) 
				document.forms[0].submit();
		}
	}
	
	/**
	 * Build URL wichi will open the add a new role assignement for a scenario
	 */
	function buildAddRoleURL(methodName){
		var param = 'scenMaintenance.do?method='+methodName;
		param+="&selectedCategoryId="+document.forms[0].selectedCategoryId.value;
		param+="&categorySystemFlag="+document.forms[0].categorySystemFlag.value;
		return  param;
	}
</SCRIPT>
</head>
<s:form action="scenMaintenance.do">
	<input name="method" type="hidden" value="display">
	<input name="selectedCategoryId" type="hidden" value="">
	<input name="categorySystemFlag" type="hidden" value="">
	<input name="menuAccessId" type="hidden" >
	<body onmousemove="reportMove()" leftmargin="0" topmargin="0" marginheight="0" onLoad="bodyOnLoad();setParentChildsFocus();ShowErrMsgWindow('${actionError}');setFocus(document.forms[0]);" onunload="call()">
		<div id="ScenarioCategory" style="position:absolute; border:2px outset; left:20px; top:15px; width:898px; height:300px;">
			<div id="ScenarioCategory" style="position:absolute;z-index:99;left:0px; top:0px; width:885; height:10px;">
				<table  class="sort-table" id="table_2" bgcolor="#B0AFAF" width="875" border="0" cellspacing="1" cellpadding="0">
					<thead>
						<tr height="22px">
							<td width="170" align="left"  title='<s:text name="tooltip.sortCategoryId"/>'><b><s:text name="scenarioCategory.categoryId"/></b></td>
							<td width="150" align="left" title='<s:text name="tooltip.sortCategoryTitle"/>'><b><s:text name="scenarioCategory.categoryTitle"/></b></td>
							<td width="200" align="left" title='<s:text name="tooltip.sortCategoryDescription"/>'><b><s:text name="scenarioCategory.categoryDescription"/></b></td>
							<td width="100" align="left" title='<s:text name="tooltip.sortCategorySystemFlag"/>'><b><s:text name="scenarioCategory.categorySystemFlag"/></b></td>
							<td width="150" align="left" title='<s:text name="tooltip.sortCategoryDisplayName"/>'><b><s:text name="scenarioCategory.categoryDisplayTabName"/></b></td>
							<td width="100" align="left" title='<s:text name="tooltip.sortCategoryDisplayorder"/>'><b><s:text name="scenarioCategory.categoryDisplayorder"/></b></td>
						</tr>
					</thead>
				</table>
			</div>
			<div id="ddscrolltable"  style="position:absolute; left:0px; top:2px; width:894px; height:292;overflowY:scroll">
				<div id="ScenarioCategory" style="position:absolute;z-index:99;left:1px; top:22px; width:877px; height:10px;">
					<table class="sort-table" id="scenarioCategoryDetails" width="875px" border="0" cellspacing="1" cellpadding="0" height="270">
						<tbody> 		
							<%int count = 0; %>  
							<s:iterator value ="#request.scenarioCategoryDetails" var="scenarioCategoryDetails">          
								<% if( count%2 == 0 ) {%><tr height="20px" class="even"><% }else  { %> <tr height="20px" class="odd"> <%}++count; %>
									<s:set var="scenarioCategoryDetails" value="#scenarioCategoryDetails"/>
									<jsp:useBean id="scenarioCategoryDetails" class="org.swallow.control.model.ScenarioCategory" />
									<%ScenarioCategory scenNotify=(ScenarioCategory) scenarioCategoryDetails; %>
									<s:hidden name= "id.categoryid"/>
									<s:hidden name= "systemflag"/>
									<td width="170" align="left"><s:property value="id.categoryid"/>&nbsp;</td>
									<td width="150" align="left"><s:property value="title"/>&nbsp;</td>
									<td width="200" align="left"><s:property value="description"/>&nbsp;</td>
									<td width="100" align="center"><s:property value="systemflag"/>&nbsp;</td>
									<td width="150" align="center"><s:property value="displayTabName"/>&nbsp;</td>
									<td width="100" align="center"><s:property value="displayorder"/>&nbsp;</td>
								</tr>
							</s:iterator>  
						</tbody>
						<tfoot><tr><td colspan="5" ></td></tr></tfoot>
					</table>
				</div>
			</div>
						<div id="ScenarioMaintenance" style="position:absolute; left:814; top:315; width:70px; height:29px; visibility:visible;">
				<table width="60px" border="0" cellspacing="0" cellpadding="0" height="20">
					<tr>
						<td align="Right">
							<a title='<s:text name="tooltip.helpScreen"/>' tabindex="5" href=# onclick="javascript:openWindow(buildPrintURL('print','Scenario Category'),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')" onMouseOut="MM_swapImgRestore()" onMouseOver="MM_swapImage('Help','','images/help_default.GIF ',1)"><img src="images/help_default.GIF " name="Help"  border="0" ></a> 
						</td>
						<td align="right" id="Print">&nbsp;
							<a tabindex="6" onclick="printPage();" onMouseOut="MM_swapImgRestore()" onMouseOver="MM_swapImage('Print','','images/Print_R.gif ',1)"><img src="images/Print.gif " name="Print"  border="0" title='<s:text name="tooltip.printScreen"/>'></a>	
						</td>
					</tr>
				</table>
			</div>
		</div>
			<div id="ddimagebuttons" style="position:absolute; border:2px outset; left:20; top:323; width:892px; height:39px; visibility:visible;">
				<div id="ScenarioMaintenance" style="position:absolute; left:2; top:4; width:740px; height:15px; visibility:visible;">
					<table width="285" border="0" cellspacing="0" cellpadding="0" height="20">	    
						<tr>
							<td id="addbutton"></td>
							<td id="changebutton"></td>
							<td id="deletebutton"></td>
							<td id="closebutton" width="70px">		
								<a title='<s:text name="tooltip.close"/>' tabindex="4" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onclick="confirmClose('P');"><s:text name="button.close"/></a>			
							</td>
						</tr>
					</table>
				</div>
				<div style="position:absolute; left:6; top:4; width:654px; height:15px; visibility:hidden;display: none;">  	
					<table width="350" border="0" cellspacing="0" cellpadding="0" height="20" style="visibility:hidden">
						<tr>			
				            <td id="addenablebutton">		
								<a title='<s:text name="tooltip.addCategory"/>' tabindex="1" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onClick="javascript:openWindow(buildAddRoleURL('addCategory'),'addCategory','left=50,top=190,width=700,height=260,toolbar=0, resizable=yes scrollbars=yes','true')"><s:text name="button.add"/></a>
							</td>				
							<td id="adddisablebutton">
								<a class="disabled" disabled="disabled"><s:text name="button.add"/></a>
							</td>
							<td id="changeenablebutton">		
								<a title='<s:text name="tooltip.changeCategory"/>' tabindex="2" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onClick="javascript:openWindow(buildAddRoleURL('changeCategory'),'changecategory','left=50,top=190,width=700,height=260,toolbar=0, resizable=yes, scrollbars=yes','true')"><s:text name="button.change"/></a>
							</td>		
							<td id="changedisablebutton">
								<a class="disabled" disabled="disabled"><s:text name="button.change"/></a>
							</td>
							<td id="deleteenablebutton">		
								<a  title='<s:text name="tooltip.deleteCategory"/>' tabindex="3" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" 	onClick="javascript:submitDeleteForm('deleteCategory');" ><s:text name="button.delete"/></a>
							</td>		
							<td id="deletedisablebutton">
								<a class="disabled" disabled="disabled"><s:text name="button.delete"/></a>
							</td>
						</tr>
					</table>
				</div>
			</div>
	
		<blockquote>&nbsp;</blockquote>
		<p>&nbsp;</p>
	</body>
</s:form>
</html>            