<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<!--
  - The main purpose of this jsp file is to load the Interface Monitor screen.
  - 
  - Modified By: Marshal .I
  - Date: 20-June-2011
  -"yes".equals(request.getAttribute("fromPCM")) ? " (PCM)":""
  -->
<%@ page import="org.swallow.util.SwtConstants"%>
<%@ page import="org.swallow.util.SwtUtil"%>
<%@ include file="/taglib.jsp"%>
<%@ include file="/angularJSUtils.jsp"%>
<html>
<head>


<style>
body {
	margin: 0px;
	overflow: hidden
}
</style>
<meta name="viewport" content="width=device-width, initial-scale=1">
<link rel="icon" type="image/x-icon" href="favicon.ico">
<link rel="stylesheet" href="angularSources/styles.css?version=<%= SwtUtil.appVersion %>">
</head>
<body>		
		<script type="text/javascript" src="jsp/work/assets/swfobject.js"></script>
		<script type="text/javascript">
		var PCM = new String('<%="yes".equals(request.getAttribute("fromPCM")) ? " (PCM)":""%>');
		var screenTitle = "";
		screenTitle = getMessage("label.interfaceMonitor.title", null) + PCM;
		document.title = screenTitle;
		var baseURL = new String('<%=request.getRequestURL()%>');
		var screenRoute = "interfaceMonitor";
		var appName = "<%=SwtUtil.appName%>";	
		window.onload = function () {
			setParentChildsFocus();
			setTitleSuffix(document.forms[0]); 
		};
		
		window.onunload = call;
					
		var screenId;
		var hostId = '${requestScope.hostId}';
		var userId = '${requestScope.userId}';	
		var refreshPending = false;
		var testDate= "<%=SwtUtil.getSystemDateString() %>";
        // get the system date
		var dbDate="<%=SwtUtil.getSysDateWithFmt(SwtUtil.getSystemDateFromDB())%>";
		var dateFormat = '<s:property value="#request.session.CDM.dateFormatValue" />';
		var winObject = null;
		var fromPCM = '${requestScope.fromPCM}';

		function getUpdateRefreshRequest (rate) {
			if (fromPCM == "true" || fromPCM == "yes" ) {
				screenId = "<%=SwtConstants.INTERFACEMONITOR_PCM_ID%>";
			} else {
				screenId = "<%=SwtConstants.INTERFACEMONITOR_ID%>";
			}
			return "screenOption.do?method=save&screenOption.id.hostId=" + hostId + "&screenOption.id.userId=" + userId + "&screenOption.id.screenId=" + screenId + "&screenOption.propertyValue=" + rate;
		}
		
		/**
		* This function opens the Help pop-up window for Interface Monitor screen.			
		*/
		function help(){
			openWindow(buildPrintURL('print','Interface Monitor'),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')
           }
           
		/**
	 	  * This method is used to open the interface exceptions monitor screen			
	    */
		function clickMessages(urlString) {
			var param = '/' +  appName + '/' + urlString
        	var winTop = window.screenTop ? window.screenTop : window.screenY;
        	var winLeft = window.screenLeft ? window.screenLeft : window.screenX;
        	var mainWindow = openWindow(
        			param,
 					'exceptions',
 					'left='+winLeft+',top='+winTop+',width=960,height=680,toolbar=0, resizable=yes, status=yes,scrollbars=yes', 'true');
			// winObject = window.open(urlString,'exceptions','left='+winLeft+',top='+winTop+',width=960,height=680,toolbar=0, resizable=yes, status=yes,scrollbars=yes', 'true');	
		}
		
	
	</script>
<%@ include file="/angularscripts.jsp"%>
		<form id="exportDataForm" target="tmp" method="post">
			<input type="hidden" name="data" id="exportData" /> 
			<input type="hidden" name="screen" id="exportDataScreen"
				value="<s:text name="interfaceMonitor.title.window"/>" />
		</form>	
		<iframe name="tmp" width="0%" height="0%" src="#" /> 
</body>
</html>
