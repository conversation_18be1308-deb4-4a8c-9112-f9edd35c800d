<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@ include file="/taglib.jsp"  %>
<%@ page import="org.swallow.util.SwtConstants" %>
<%@page import="org.swallow.control.model.Scenario"%>
<%@page import="org.swallow.control.model.ScenarioCategory"%>
<html>
<head>
<title><s:text name="scenario.title.window"/></title>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">


<SCRIPT language="JAVASCRIPT">

	var menuEntityCurrGrpAccess = "${requestScope.menuEntityCurrGrpAccess}";
	mandatoryFieldsArray = "undefined";
	/**
	 * Called on first load of the screen
	 *
	 **/
	function bodyOnLoad()
	{
		xl = new XLSheet("scenariosMaintenanceDetails","table_2", ["String","String", "String", "String","String","String","String"],"2212111");
		
		xl.onsort = xl.onfilter = disableButtons;
		
		highlightTableRows("scenariosMaintenanceDetails");
		
		var dropBox1 = new SwSelectBox(document.forms[0].elements["alertMessage.id.alertstage"], document.getElementById("alertName"));
	
		document.getElementById("addbutton").innerHTML = document.getElementById("adddisablebutton").innerHTML;
		document.getElementById("changebutton").innerHTML = document.getElementById("changedisablebutton").innerHTML;
		document.getElementById("viewbutton").innerHTML = document.getElementById("viewdisablebutton").innerHTML;
		document.getElementById("deletebutton").innerHTML = document.getElementById("deletedisablebutton").innerHTML;
		
	
		var buttonStatus = "<%= request.getAttribute(SwtConstants.ADD_BUT_STS) %>";	
		document.getElementById("addbutton").innerHTML = (buttonStatus=="<%=SwtConstants.STR_TRUE%>"?document.getElementById("addenablebutton").innerHTML:document.getElementById("adddisablebutton").innerHTML);	
	
		 buttonStatus = "<%= request.getAttribute(SwtConstants.CHG_BUT_STS) %>";	
		document.getElementById("changebutton").innerHTML = (buttonStatus=="<%=SwtConstants.STR_TRUE%>"?document.getElementById("changeenablebutton").innerHTML:document.getElementById("changedisablebutton").innerHTML);	
	
		 buttonStatus = "<%= request.getAttribute(SwtConstants.VIEW_BUT_STS) %>";	
		document.getElementById("viewbutton").innerHTML = (buttonStatus=="<%=SwtConstants.STR_TRUE%>"?document.getElementById("viewenablebutton").innerHTML:document.getElementById("viewdisablebutton").innerHTML);	
	
		 buttonStatus = "<%= request.getAttribute(SwtConstants.DEL_BUT_STS) %>";	
		document.getElementById("deletebutton").innerHTML = (buttonStatus=="<%=SwtConstants.STR_TRUE%>"?document.getElementById("deleteenablebutton").innerHTML:document.getElementById("deletedisablebutton").innerHTML);	
	
		document.forms[0].menuAccessId.value = "${requestScope.menuAccessId}";
	}
	
	/**
	 * Dispatched when the user selects a row in the grid
	 *
	 **/
	function selectTableRow(e)
	{
              var event = (window.event|| e);
		var target = (event.srcElement || event.target)
		var srcEl = target;
		while(srcEl.tagName != 'TD')
		{
			srcEl = srcEl.parentElement;
		}
		
		var rowElement = srcEl.parentElement;
		var tblElement = srcEl.parentElement.parentElement.parentElement;
	
		var isRowSel = isRowSelected(rowElement);
		resetTableRowsStyle(tblElement);
		
		if(isRowSel == false)
			rowElement.className = 'selectrow' ;
	
		onSelectTableRow(rowElement,!isRowSel);
	}
	
	/**
	 * Disable buttons and colors when the user does not select any row in the grid
	 *
	 **/
	function disableButtons(){
	
		document.getElementById("changebutton").innerHTML = document.getElementById("changedisablebutton").innerHTML;
		document.getElementById("viewbutton").innerHTML = document.getElementById("viewdisablebutton").innerHTML;
		updateColors();
	}
	
	/**
	 * Triggered when the user deletes a scenario via delete button
	 * @ param methodName
	 **/
	function submitDeleteForm(methodName){
		
		var systemFlag=document.forms[0].selectedSystemFlag.value;
		if(systemFlag!="Y")
		{	
			document.forms[0].method.value = methodName;	
			var yourstate=window.confirm("<s:text name="confirm.delete"/>");
			if (yourstate==true) 	
				document.forms[0].submit();
		}else	
			alert("<s:text name="alert.deleteSystemScenario"/>");
	}
	
	/**
	 * Build URL that will open the add scenario screen
	 * @ param methodName
	 **/
	function buildAddScenarioURL(methodName){
		var param = 'scenMaintenance.do?method=addScreen&screenName='+methodName;
		param +='&selectedScenarioID='+document.forms[0].selectedScenarioID.value;
		param +='&selectedSystemFlag='+document.forms[0].selectedSystemFlag.value;
		param +='&menuAccessId='+ menuEntityCurrGrpAccess;
		param +='&fromAdvanced='+'false';
		return  param;
	}
	
	
	
	/**
	 * This function is called when the user selects a row, it will change the visual of buttons
	 * @ rowElement
	 * @ isSelected 
	 **/
	function onSelectTableRow(rowElement , isSelected)
	{
		
		var hiddenElement = rowElement.getElementsByTagName("input")[0];
		var hiddenElement2 = rowElement.getElementsByTagName("input")[1];
		
		document.forms[0].selectedScenarioID.value = hiddenElement.value;
		document.forms[0].selectedSystemFlag.value = hiddenElement2.value;
		document.getElementById("viewbutton").innerHTML = document.getElementById("viewenablebutton").innerHTML;
	
		document.getElementById("viewbutton").innerHTML = (isSelected?document.getElementById("viewenablebutton").innerHTML:document.getElementById("viewdisablebutton").innerHTML);
		document.getElementById("changebutton").innerHTML = (isSelected&&menuEntityCurrGrpAccess == "0"?document.getElementById("changeenablebutton").innerHTML:document.getElementById("changedisablebutton").innerHTML);
		document.getElementById("deletebutton").innerHTML =(isSelected&&menuEntityCurrGrpAccess == "0"&&hiddenElement2.value=='N'?document.getElementById("deleteenablebutton").innerHTML:document.getElementById("deletedisablebutton").innerHTML);
	}
	
</SCRIPT>
</head>

<s:form action="scenMaintenance.do">
<input name="method" type="hidden" value="display">
<input name="selectedScenarioID" type="hidden" value="">
<input name="selectedSystemFlag" type="hidden" value="">
<input name="menuAccessId" type="hidden" >

<body onmousemove="reportMove()" leftmargin="0" topmargin="0" marginheight="0" onLoad="bodyOnLoad();setParentChildsFocus();ShowErrMsgWindow('${actionError}');setFocus(document.forms[0]);" onunload="call()">
	<div id="ScenarioMaintenance" style="position:absolute; border:2px outset; left:20px; top:15px; width:1275px; height:400;">
		<div id="ScenarioMaintenance" style="position:absolute;z-index:99;left:0px; top:0px; width:1250; height:10px;">
			<table  class="sort-table" id="table_2" bgcolor="#B0AFAF" width="1245" border="0" cellspacing="1" cellpadding="0">
				<thead>
					<tr height="22px">
						<td width="175" align="left" title='<s:text name="tooltip.scenarioID"/>'><b><s:text name="scenario.id"/></b></td>
						<td width="280" align="left" title='<s:text name="tooltip.scenarioTitle"/>'><b><s:text name="scenario.title"/></b></td>
						<td width="140" align="left"  title='<s:text name="tooltip.scenarioCategory"/>'><b><s:text name="scenario.category"/></b></td>
						<td width="330" align="left" title='<s:text name="tooltip.scenarioType"/>'><b><s:text name="scenario.fieldSet.legendText"/></b></td>
						<td width="100" align="left" title='<s:text name="tooltip.scenarioSystem"/>'><b><s:text name="scenario.system"/></b></td>
						<td width="100" align="left" title='<s:text name="tooltip.scenarioActive"/>'><b><s:text name="scenario.active"/></b></td>
						<td width="120" align="left" title='<s:text name="scenario.recordInsLbl"/>'><b><s:text name="scenario.tab.instances"/></b></td>
					</tr>
				</thead>
			</table>
		</div>
		<div id="ddscrolltable"  style="position:absolute; left:0px; top:2px; width:1270px; height:390;overflowY:scroll">
			<div id="ScenarioMaintenance" style="position:absolute;z-index:99;left:0px; top:22px; width:1250px; height:10px;">
				<table class="sort-table" id="scenariosMaintenanceDetails" width="1245px" border="0" cellspacing="1" cellpadding="0" height="365">
					<tbody> 		
						<%int count = 0; %>  
						<s:iterator  value ="#request.scenariosMaintenanceDetails"  var="scenariosMaintenanceDetails">          
							<% if( count%2 == 0 ) {%><tr height="20px" class="even"><% }else  { %> <tr height="20px" class="odd"> <%}++count; %>
					<s:set var="scenariosMaintenanceDetails" value="#scenariosMaintenanceDetails"/>
					<jsp:useBean id="scenariosMaintenanceDetails" class="org.swallow.control.model.Scenario" />
					<%Scenario scenario=(Scenario)scenariosMaintenanceDetails; %>
								<s:hidden name="id.scenarioId"/>
								<s:hidden name="systemFlag"/>
								<td width="175" align="left"><s:property value="id.scenarioId"/>&nbsp;</td>
								<td width="280" align="left"><s:property value="title"/>&nbsp;</td>
								<td width="140" align="left"><s:property value="categoryTitle"/>&nbsp;</td>
								<td width="330" align="left"><s:property value="runEvery"/>&nbsp;</td>
								<td width="100" align="center"><s:property value="systemFlag"/>&nbsp;</td>
								<td width="100" align="center"><s:property value="activeFlag"/>&nbsp;</td>
								<td width="120" align="center"><s:property value="recordScenarioInstance"/>&nbsp;</td>
							</tr>
						</s:iterator>  
					</tbody>
					<tfoot>
						<tr><td colspan="6" ></td></tr>
					</tfoot>
				</table>
			</div>
		</div>
		</div>
		<div id="ScenarioMaintenance" style="position:absolute; left:1190; top:430; width:70px; height:29px; visibility:visible;">
			<table width="60px" border="0" cellspacing="0" cellpadding="0" height="20">
				<tr>
					<td align="Right">
						<a title='<s:text name="tooltip.helpScreen"/>' tabindex="6" href=# onclick="javascript:openWindow(buildPrintURL('print','Scenario Maintenance'),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')" onMouseOut="MM_swapImgRestore()" onMouseOver="MM_swapImage('Help','','images/help_default.GIF ',1)"><img src="images/help_default.GIF " name="Help"  border="0" ></a> 
					</td>
					<td align="right" id="Print">&nbsp;
						<a tabindex="7" onclick="printPage();" onMouseOut="MM_swapImgRestore()" onMouseOver="MM_swapImage('Print','','images/Print_R.gif ',1)"><img src="images/Print.gif " name="Print"  border="0" title='<s:text name="tooltip.printScreen"/>'></a>	
					</td>
				</tr>
			</table>
		</div>
		<div id="ddimagebuttons" color="#7E97AF" style="position:absolute; border:2px outset; left:20; top:425; width:1275px; height:39px; visibility:visible;">
			<div id="ScenarioMaintenance" style="position:absolute; left:2; top:4; width:955px; height:15px; visibility:visible;">
				<table width="410" border="0" cellspacing="0" cellpadding="0" height="20">
					<tr>
						<td id="addbutton"></td>
						<td id="changebutton"></td>
						<td id="viewbutton"></td>
						<td id="deletebutton"></td>
						<td id="closebutton" width="70px">		
							<a title='<s:text name="tooltip.close"/>' tabindex="5" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onclick="confirmClose('P');"><s:text name="button.close"/></a>			
						</td>
					</tr>
				</table>
			</div>
			<div style="position:absolute; left:6; top:4; width:654px; height:15px; visibility:hidden; display:none;">  	
				<table width="350" border="0" cellspacing="0" cellpadding="0" height="20" style="visibility:hidden;">
					<tr>
			            <td id="addenablebutton">		
							<a title='<s:text name="tooltip.addScenario"/>' tabindex="1" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onClick="javascript:openWindow(buildAddScenarioURL('add'),'scenarioadd','left=50,top=190,width=1000,height=750,toolbar=0, resizable=yes scrollbars=yes','true')"><s:text name="button.add"/></a>
						</td>				
						<td id="adddisablebutton">
							<a class="disabled" disabled="disabled"><s:text name="button.add"/></a>
						</td>
						<td id="changeenablebutton">		
							<a title='<s:text name="tooltip.changeScenario"/>' tabindex="2" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onClick="javascript:openWindow(buildAddScenarioURL('change'),'scenariochange','left=50,top=190,width=1000,height=750,toolbar=0, resizable=yes, scrollbars=yes','true')"><s:text name="button.change"/></a>
						</td>		
						<td id="changedisablebutton">
							<a class="disabled" disabled="disabled"><s:text name="button.change"/></a>
						</td>
						<td id="viewenablebutton">		
							<a  title='<s:text name="tooltip.viewScenario"/>' tabindex="3" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onClick="javascript:openWindow(buildAddScenarioURL('view'),'scenarioview','left=50,top=190,width=1000,height=750,toolbar=0, resizable=yes, scrollbars=yes','true')"><s:text name="button.view"/></a>
						</td>		
						<td id="viewdisablebutton">
							<a class="disabled" disabled="disabled"><s:text name="button.view"/></a>
						</td>
						<td id="deleteenablebutton">		
							<a  title='<s:text name="tooltip.deleteScenario"/>' tabindex="4" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" 	onClick="javascript:submitDeleteForm('delete');" ><s:text name="button.delete"/></a>
						</td>		
						<td id="deletedisablebutton">
							<a class="disabled" disabled="disabled"><s:text name="button.delete"/></a>
						</td>
						<td id="roleenablebutton">		
							
						</td>		
						<td id="roledisablebutton">
							<a class="disabled" disabled="disabled"><s:text name="button.role"/></a>
						</td>
					</tr>
				</table>
			  </div>
			</div>
		
		<blockquote>&nbsp;</blockquote>
		<p>&nbsp;</p>
</body>
</s:form>
</html>            