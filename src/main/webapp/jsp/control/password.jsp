<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@ include file="/taglib.jsp"  %>
<%@ page import="org.swallow.util.SwtConstants" %>
<html>
<head>
<title>
 <s:if test='"true" == #request.screenFieldsStatus' >
	<s:text name="passwordRules.title.window"/>
 </s:if>

 <s:if test='"true" != #request.screenFieldsStatus' >
	<s:text name="changepasswordRules.title.window"/>
 </s:if>
</title>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
 

<script type="text/javascript">

var cancelcloseElements = new Array(2);

cancelcloseElements[0] = "cancelbutton";
cancelcloseElements[1] = "closebutton";
mandatoryFieldsArray = ["*"];
/* START: Code changed as par ING requirements to the Roles - Menu Access, 11-JUN-2007 */
var menuEntityCurrGrpAccess = "${requestScope.menuEntityCurrGrpAccess}";
/* END: Code changed as par ING requirements to the Roles - Menu Access, 11-JUN-2007 */

function closeWindow(){
	window.close();
}

function changeForm(methodName){
		enableFields();
		document.forms[0].method.value = methodName;
		document.forms[0].submit();	
}

function submitForm(methodName){
/*Betcy:12/01/2009:Added to check the validation in "SAVE" button click(start)*/
 var alphacharValue=validateField(document.forms[0].elements['password.alphaChar'],'pwd.alphaChar','numberPat');
  
 if(alphacharValue)
 { 
    var numericValue=validateField(document.forms[0].elements['password.numericChar'],'password.numericChar','numberPat');
    if(numericValue)
	{
	  var specialCharValue=validateField(document.forms[0].elements['password.specialChar'],'pwd.alphaChar','numberPat');
	  if(specialCharValue)
	  {
	    var minlengthValue=validateField(document.forms[0].elements['password.minLength'],'password.minLength','numberPat');
	    if(minlengthValue)
		{
		  var expireinValue=validateField(document.forms[0].elements['password.expireDays'],'password.expireDays','numberPat');
		  if(expireinValue)
		  {
		    var recentlyusedValue=validateField(document.forms[0].elements['password.recentUserpwd'],'password.recentUserpwd','numberPat');
		    if(recentlyusedValue)
			{
			      var invalidattemptsValue=validateField(document.forms[0].elements['password.unsuccLoginAttempt'],'password.unsuccLoginAttempt','numberPat');
	              if(invalidattemptsValue)
				  {
				  /*Betcy:12/01/2009:Added to check the validation in "SAVE" button click(end)*/
 if(validateForm(document.forms[0])){     
	       enableFields();
		  document.forms[0].method.value = methodName;
		  document.forms[0].submit();
	    }
		}
		
			/*Code Modified by Betcy on 28/01/2009:Added to check the validation in "SAVE" button click for Mantis 890 (start)*/	
        else
        {
		  document.forms[0].elements['password.unsuccLoginAttempt'].focus();
        }		
		}
		else
		{
		  document.forms[0].elements['password.recentUserpwd'].focus();
		}
		}
		else
		{
		  document.forms[0].elements['password.expireDays'].focus();
		} 
		}
		else
		{
		  document.forms[0].elements['password.minLength'].focus();
		}
		
 }
 else
 {
   document.forms[0].elements['password.specialChar'].focus();
 } 
 }
 else
 {
   document.forms[0].elements['password.numericChar'].focus();
 }
 }
 else
 {
   document.forms[0].elements['password.alphaChar'].focus();
  } 
	  /*Code Modified by Betcy on 28/01/2009:Added to check the validation in "SAVE" button click for Mantis 890 (End)*/	
		
}

function submitFormCancel(methodName){     
	       enableFields();
		  document.forms[0].method.value = methodName;
		  document.forms[0].submit();
}

function enableFields(){
	document.forms[0].elements["password.numericChar"].disabled = "";
	document.forms[0].elements["password.specialChar"].disabled = "";	
	document.forms[0].elements["password.alphaChar"].disabled = "";	
	document.forms[0].elements["password.minLength"].disabled = "";	
	document.forms[0].elements["password.expireDays"].disabled = "";	
	document.forms[0].elements["password.recentUserpwd"].disabled = "";	
	document.forms[0].elements["password.unsuccLoginAttempt"].disabled = "";
	document.getElementById('mixedCase').disabled = "";
}

function closeWindow(){
	window.close();
}

function bodyOnLoad() {
	document.getElementById("savebutton").innerHTML = document.getElementById("savedisablebutton").innerHTML;
	document.getElementById("cancelbutton").innerHTML = document.getElementById("canceldisablebutton").innerHTML; 

 <%if ( (SwtConstants.STR_TRUE).equals(request.getAttribute(SwtConstants.SAV_BUT_STS)) ) {%>
		document.getElementById("savebutton").innerHTML = document.getElementById("saveenablebutton").innerHTML;
	<%}%>

	 <%if ( !(SwtConstants.STR_TRUE).equals(request.getAttribute(SwtConstants.SAV_BUT_STS)) ) {%>
		document.getElementById("savebutton").innerHTML = document.getElementById("savedisablebutton").innerHTML;
	<%}%>

	 <%if ( (SwtConstants.STR_TRUE).equals(request.getAttribute(SwtConstants.CHG_BUT_STS)) ) {%>
		document.getElementById("changebutton").innerHTML = document.getElementById("changeenablebutton").innerHTML;
	<%}%>

	 <%if ( !(SwtConstants.STR_TRUE).equals(request.getAttribute(SwtConstants.CHG_BUT_STS)) ) {%>
		document.getElementById("changebutton").innerHTML = document.getElementById("changedisablebutton").innerHTML;
	<%}%>

	<%if ( (SwtConstants.STR_TRUE).equals(request.getAttribute(SwtConstants.CAN_BUT_STS)) ) {%>
		document.getElementById("cancelbutton").innerHTML = document.getElementById("cancelenablebutton").innerHTML;
	<%}%>

	 <%if ( !(SwtConstants.STR_TRUE).equals(request.getAttribute(SwtConstants.CAN_BUT_STS)) ) {%>
		document.getElementById("cancelbutton").innerHTML = document.getElementById("canceldisablebutton").innerHTML;
	<%}%>
	
	/* START: Code changed as par ING requirements to the Roles - Menu Access, 11-JUN-2007 */
	if(menuEntityCurrGrpAccess == "0") {
		document.getElementById("changebutton").innerHTML = document.getElementById("changeenablebutton").innerHTML;
	} else {
		document.getElementById("changebutton").innerHTML = document.getElementById("changedisablebutton").innerHTML;
	}	
	document.forms[0].menuAccessId.value = "${requestScope.menuAccessId}";
	/* END: Code changed as par ING requirements to the Roles - Menu Access, 11-JUN-2007 */
	
	<%if ( !("true").equals(request.getAttribute("screenFieldsStatus")) ) {%>
	setMixedCaseState ();
	<%}%>
}

function setMixedCaseState () {
	if (parseInt (document.getElementById('alphaChar').value) < 2) {
		document.getElementById('mixedCase').checked = false;
		document.getElementById('mixedCase').disabled = true;
	} else {
		document.getElementById('mixedCase').disabled = false;
	}
}

function submitCancel(methodName){
	<%if ( (SwtConstants.STR_TRUE).equals(request.getAttribute(SwtConstants.CAN_BUT_STS)) ) {%>
	ShowErrMsgWindowWithBtn('', '<s:text name="passwordRules.confirm.close"/>', YES_NO, yesClose, null);	  
	<%}%>
	<%if ( !(SwtConstants.STR_TRUE).equals(request.getAttribute(SwtConstants.CAN_BUT_STS)) ) {%>
	document.forms[0].method.value = methodName;
	 window.close();
	 <%}%>
}
function yesClose() {
	window.close();
}

function validateForm(objForm){

  var isFormValid = false;
  var elementsRef = new Array(3);
 
 
  elementsRef[0] = objForm.elements["password.alphaChar"];
  elementsRef[1] = objForm.elements["password.numericChar"];
  elementsRef[2] = objForm.elements["password.specialChar"];
  
  isFormValid = validate(elementsRef);
  if(isFormValid)
  {
  	// check the min value of password field
	
	var minValueObjColl = new Array(4);

	minValueObjColl[0] = new MinimumValue(elementsRef[0],1);
	minValueObjColl[1] = new MinimumValue(elementsRef[1],0);
	minValueObjColl[2] = new MinimumValue(elementsRef[2],0);
    minValueObjColl[3] = new MinimumValue(objForm.elements["password.minLength"],8);

	isFormValid = validateMinValueObjects(minValueObjColl);    
	
	return isFormValid;	
  }
}

document.onmousemove = reportMove;
window.onload = function () {
	setParentChildsFocus();
	setFocus(document.forms[0]);
	bodyOnLoad();
	ShowErrMsgWindow('${actionError}');
}
window.onunload = call;
</script>

</head>

<body>
<s:form action="password.do" onsubmit="return validate(this);">
 <input name="selectedAlphaCode" type="hidden">
 <input type="hidden" name="method" >
 
<!-- START: Code changed as par ING requirements to the Roles - Menu Access, 11-JUN-2007 -->
<input name="menuAccessId" type="hidden" >
<!-- END: Code changed as par ING requirements to the Roles - Menu Access, 11-JUN-2007 -->
<div id="useroptions" style="position:absolute; left:10px; top:10px; width:356px; height:267px; border:2px outset;" color="#7E97AF">
<div id="useroptions" style="position:absolute; left:8px; top:14px; width:340px; height:38px;">

<div style="left:8px; top:4px; height: 125px;">
	<fieldset style="border: 2px groove; height:125px" >
		<legend><s:text name="passwordRules.legend.MinNum"/></legend>
		<table cellspacing="0" class="content" style="margin-left: 3px">
			<tr>  
				<td width="120">&nbsp;<b style="font-size:9pt;"><s:text name="pwd.alphaChar"/>*</b></td>
				<td width="90"><s:text name="passwordRules.label.aToz"/>&nbsp;</td>
				<td width="27">
					<s:if test='"true" == #request.screenFieldsStatus' >
					<s:textfield id="alphaChar" tabindex="1" titleKey="tooltip.minAlphaChars" cssClass="htmlTextNumeric" name="password.alphaChar" maxlength="2" cssStyle="width:27px;" disabled="%{#attr.screenFieldsStatus}" onchange="setMixedCaseState(); return validateField(this,'pwd.alphaChar','numberPat');"/> 
					</s:if>

					<s:if test='"true" != #request.screenFieldsStatus' >
					<s:textfield id="alphaChar" tabindex="1" titleKey="tooltip.minAlphaChars" cssClass="htmlTextNumeric" name="password.alphaChar" maxlength="2" cssStyle="width:27px;" disabled="%{#attr.screenFieldsStatus}" onchange="setMixedCaseState(); return validateField(this,'pwd.alphaChar','numberPat');"/> 
					</s:if>
				</td>
			</tr>
			<tr>  
				<td colspan="2">&nbsp;<b style="font-size:9pt;"><s:text name="pwd.mixedCase"/>&nbsp;</b></td>		
				<td width="27">
					<s:if test='"true" == #request.screenFieldsStatus' >
					<input type="checkbox" value="Y" name="mixedCase" id="mixedCase" title='<s:text name="tooltip.mixedCase"/>' class="htmlTextNumeric" style="width:27px;"<%=request.getAttribute ("mixedCase")!=null && ((String)request.getAttribute ("mixedCase")).equalsIgnoreCase("Y")?" checked":""%> disabled="true"/>
					</s:if>

					<s:if test='"true" != #request.screenFieldsStatus' >
					<input type="checkbox" value="Y" name="mixedCase" id="mixedCase" title='<s:text name="tooltip.mixedCase"/>' class="htmlTextNumeric" style="width:27px;"<%=request.getAttribute ("mixedCase")!=null && ((String)request.getAttribute ("mixedCase")).equalsIgnoreCase("Y")?" checked":""%>/>
					</s:if>
				</td>
			</tr>
			<tr>  
				<td width="120">&nbsp;<b style="font-size:9pt;"><s:text name="pwd.numericChar"/></b>*</td>		
				<td width="90"><s:text name="passwordRules.label.0to9"/></td>
				<td width="27">
					<s:if test='"true" == #request.screenFieldsStatus' >
					<s:textfield tabindex="2" titleKey="tooltip.minNumChars" cssClass="htmlTextNumeric" name="password.numericChar" maxlength="2" cssStyle="width:27px;" disabled="%{#attr.screenFieldsStatus}" onchange="return validateField(this,'pwd.numericChar','numberPat');"/>
					</s:if>

					<s:if test='"true" != #request.screenFieldsStatus' >
					<s:textfield tabindex="2" titleKey="tooltip.minNumChars" cssClass="htmlTextNumeric" name="password.numericChar" maxlength="2" cssStyle="width:27px;" disabled="%{#attr.screenFieldsStatus}" onchange="return validateField(this,'pwd.numericChar','numberPat');"/>
					</s:if>
				</td>
			</tr>
			<tr>  
				<td colspan="2">&nbsp;<b style="font-size:9pt;"><s:text name="pwd.specialChar"/>*</b></td>	
				<td width="27">
					<s:if test='"true" == #request.screenFieldsStatus' >
					<s:textfield tabindex="3" titleKey="tooltip.minSpecialChars" cssClass="htmlTextNumeric" name="password.specialChar" maxlength="2" cssStyle="width:27px;" disabled="%{#attr.screenFieldsStatus}" onchange="return validateField(this,'pwd.specialChar','numberPat');"/>
					 </s:if>

					<s:if test='"true" != #request.screenFieldsStatus' >
					<s:textfield tabindex="3" titleKey="tooltip.minSpecialChars" cssClass="htmlTextNumeric" name="password.specialChar" maxlength="2" cssStyle="width:27px;" disabled="%{#attr.screenFieldsStatus}" onchange="return validateField(this,'pwd.specialChar','numberPat');"/>
					</s:if>
				</td>
			</tr>
			<tr>
				<td colspan="3"><s:text name="passwordRules.label.spChars"/>&nbsp;</td>
			</tr>
		</table>
	</fieldset>
</div>
<div style="left:8px; top:4px; height: 108px; padding-top: 0px;" >
	<fieldset style="border: 2px groove; padding:3px">
		<legend><s:text name="passwordRules.legend.passParams"/></legend>
		<table cellspacing="0" class="content" style="margin-left: 3px">
			<tr>
				<td width="210">&nbsp;<b style="font-size:9pt;"><s:text name="pwd.minPasswordLen"/>&nbsp;</b></td>
				<td width="60">
					<s:if test='"true" == #request.screenFieldsStatus' >
					<!--Betcy:06/01/2009:Added validation for Mantis 774(Start)-->
					<s:textfield tabindex="4" titleKey="tooltip.minPwdlength"  cssClass="htmlTextNumeric" name="password.minLength" maxlength="2" cssStyle="width:27px;" disabled="%{#attr.screenFieldsStatus}" onchange="return validateField(this,'pwd.specialChar','numberPat');"/>
					<!--Betcy:06/01/2009:Added Validation for Mantis 774(End)-->
					</s:if>

					<s:if test='"true" != #request.screenFieldsStatus' >
					<!--Betcy:06/01/2009:Modified Added validation for Mantis 774(Start)-->
					<s:textfield tabindex="4" titleKey="tooltip.minPwdlength" cssClass="htmlTextNumeric" name="password.minLength" maxlength="2" cssStyle="width:27px;" disabled="%{#attr.screenFieldsStatus}" onchange="return validateField(this,'pwd.specialChar','numberPat');"/>
					<!--Betcy:06/01/2009:Modified Added validation for Mantis 774(End)-->
					</s:if>

				</td>
				<td class="bold" width="40"><s:text name="pwd.chars"/>&nbsp;</td>
			</tr>
			<tr>
				<td width="210">&nbsp;<b style="font-size:9pt;"><s:text name="pwd.ExpireDays"/>&nbsp;</b></td>
				<td width="60">
					<s:if test='"true" == #request.screenFieldsStatus' >
					<!--Betcy:06/01/2009:Added validation for Mantis 774(Start)-->
					<s:textfield tabindex="5" titleKey="tooltip.pwdExpDays" cssClass="htmlTextNumeric" name="password.expireDays" maxlength="3"  cssStyle="width:27px;"  disabled="%{#attr.screenFieldsStatus}" onchange="return validateField(this,'pwd.specialChar','numberPat');"/>
					<!--Betcy:06/01/2009:Added validation for Mantis 774(end)-->
					</s:if>

					<s:if test='"true" != #request.screenFieldsStatus' >
					<!--Betcy:06/01/2009:Added validation for Mantis 774(Start)-->
					<s:textfield tabindex="5" titleKey="tooltip.pwdExpDays" cssClass="htmlTextNumeric" name="password.expireDays" maxlength="3"  cssStyle="width:27px;"  disabled="%{#attr.screenFieldsStatus}" onchange="return validateField(this,'pwd.specialChar','numberPat');"/>
					<!--Betcy:06/01/2009:Added validation for Mantis 774(End)-->
					</s:if>
				</td>
				<td class="bold" width="40"><s:text name="pwd.days"/>&nbsp;</td>
			</tr>
			<tr>
				<td width="210">&nbsp;<b style="font-size:9pt;"><s:text name="pwd.recentuserdPwd"/>&nbsp;</b></td>
				<td colspan="2">
		  			<s:if test='"true" == #request.screenFieldsStatus' >
					<!--Betcy:06/01/2009:Added validation for Mantis 774(Start)-->
					<s:textfield tabindex="6" titleKey="tooltip.noRecentlyUsedPwds" cssClass="htmlTextNumeric" name="password.recentUserpwd" maxlength="2" cssStyle="width:27px;" disabled="%{#attr.screenFieldsStatus}" onchange="return validateField(this,'pwd.specialChar','numberPat');"/>
					<!--Betcy:06/01/2009:Added validation for Mantis 774(End)-->
					</s:if>

					<s:if test='"true" != #request.screenFieldsStatus' >
					<!--Betcy:06/01/2009:Added validation for Mantis 774(Start)-->
					<s:textfield tabindex="6" titleKey="tooltip.noRecentlyUsedPwds" cssClass="htmlTextNumeric" name="password.recentUserpwd" maxlength="2" cssStyle="width:27px;" disabled="%{#attr.screenFieldsStatus}" onchange="return validateField(this,'pwd.specialChar','numberPat');"/> 
					<!--Betcy:06/01/2009:Added validation for Mantis 774(End)-->
					</s:if>           
				</td>
			</tr>
			<tr>
				<td width="210">&nbsp;<b style="font-size:9pt;"><s:text name="pwd.unsuccLogin"/>&nbsp;</b></td>
				<td colspan="2">
					<s:if test='"true" == #request.screenFieldsStatus' >
					<!--Betcy:06/01/2009:Added validation for Mantis 774(Start)-->
					<s:textfield tabindex="7" titleKey="tooltip.noInvalidAttempts" cssClass="htmlTextNumeric" name="password.unsuccLoginAttempt" maxlength="2"  cssStyle="width:27px;"  disabled="%{#attr.screenFieldsStatus}" onchange="return validateField(this,'pwd.specialChar','numberPat');"/>  
                    <!--Betcy:06/01/2009:Added validation for Mantis 774(End)-->
					</s:if>

					<s:if test='"true" != #request.screenFieldsStatus' >
			        <s:textfield tabindex="7" titleKey="tooltip.noInvalidAttempts" cssClass="htmlTextNumeric" name="password.unsuccLoginAttempt" maxlength="2"  cssStyle="width:27px;"  disabled="%{#attr.screenFieldsStatus}" onchange="return validateField(this,'pwd.specialChar','numberPat');"/>  
					</s:if>
				</td>
			</tr>
		</table> 
	</fieldset>
</div>
</div>
</div>
<div id="message" style="position:absolute ;left:301; top:293px; width:40px; height:29px; visibility:visible;">
	<table width="60" border="0" cellspacing="0" cellpadding="0" height="20">
		<tr>

		  <td align="Right">
				<a title='<s:text name="tooltip.helpScreen"/>' tabindex="8" href=# onclick="javascript:openWindow(buildPrintURL('print','Internal Message '),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')" onMouseOut="MM_swapImgRestore()" onMouseOver="MM_swapImage('Help','','images/help_default.GIF ',1)"><img src="images/help_default.GIF " name="Help"  border="0" ></a> 
		  </td>
   

			<td align="right" id="Print">
				<a title='<s:text name="tooltip.printScreen"/>' tabindex="8" onclick="printPage();" onMouseOut="MM_swapImgRestore()" onMouseOver="MM_swapImage('Print','','images/Print_R.gif ',1)"><img src="images/Print.gif " name="Print"  border="0" ></a>	
			</td>
		</tr>
	</table>
</div>
<div id="ddimagebuttons" color="#7E97AF" style="position:absolute; border:2px outset; left:10; top:285px; width:356px ;height:39px; visibility:visible;">
  <div id="UserOptions" style="position:absolute; left:5; top:4; width:300px; height:15px; visibility:visible;">
		<span id="changebutton"></span>
		<span id="savebutton"></span>
		<span id="cancelbutton"></span>
		<span id="closebutton" title='<s:text name="tooltip.close"/>'>		
			<a  tabindex="11" title='<s:text name="tooltip.close"/>' style="margin-left: 0px; " onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onClick="submitCancel('close')"><s:text name="button.close"/></a>		
		</span>
	</div>
	
	<div style="display:none">
		<span id="changeenablebutton">		
			<a tabindex="8" title='<s:text name="tooltip.changePwdRules"/>' style="margin-right: 3px;" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onClick="javascript:changeForm('change')"><s:text name="button.change"/></a>
		</span>		
		<span id="changedisablebutton">
			<a  class="disabled" title='<s:text name="tooltip.changePwdRules"/>'style="margin-right: 3px;" disabled="disabled"><s:text name="button.change"/></a>
		</span>
        <span id="saveenablebutton">		
		<a tabindex="9" title='<s:text name="tooltip.SaveChanges"/>'style="margin-left: 0px; margin-right: 0px;" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onClick="javascript:submitForm('save')"><s:text name="button.save"/></a>
		</span>		
		<span id="savedisablebutton">
			<a  class="disabled" title='<s:text name="tooltip.SaveChanges"/>'style="margin-left: 0px; margin-right: 0px;" disabled="disabled" ><s:text name="button.save"/></a>
		</span>
		<span id="cancelenablebutton">		
			<a tabindex="10" title='<s:text name="tooltip.CancelChanges"/>' style="margin-right: 3px;"onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onclick="javascript:submitFormCancel('cancel');"><s:text name="button.cancel"/></a>			
		</span>		
		<span id="canceldisablebutton">
			<a  class="disabled" title='<s:text name="tooltip.CancelChanges"/>' style="margin-right: 3px;" disabled="disabled"><s:text name="button.cancel"/></a>
		</span>
	</div>
	
</div>

</s:form>
</body>
</html>