<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@ include file="/taglib.jsp"%>
<%@ page import="org.swallow.util.SwtConstants"%>
<html>

<head>
<title>
<s:if test='#request.viewUserLog == "viewUserLog"'>
    <s:text name="auditLog.viewScreen" />
</s:if>
<s:else>
    <s:text name="userLog.viewScreen" />
</s:else>
</title>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">


<SCRIPT language="JAVASCRIPT">
/*Added to apply style on mandatory field ,if it's equal to undefined then no field will be colored*/
mandatoryFieldsArray= "undefined" ;

function bodyOnLoad(){
	xl = new XLSheet("auditLogList","table_2",["String", "String", "String"],"111");
	xl.onsort = xl.onfilter = updateColors;
	xl.dataTable.tBody.style.cursor="";
}


function closeWindow(){
	window.close();
}

</SCRIPT>
</head>
<body onmousemove="reportMove();"
	onload="bodyOnLoad();setParentChildsFocus();setFocus(document.forms[0]);"
	leftmargin="0" topmargin="0" marginheight="0" onunload="call();">
<s:form action="auditlog.do">

	<!--  Start Modified height of the screen to restrict filter drop down hit close button 
	  	  for mantis 2069 by Vivekanandan A on 10-Oct-2012 -->

	<div id="AuditLog" color="#7E97AF"
		style="position: absolute; border: 0px outset; left: 20px; top: 20px; width: 777px; height: 275px; overflowY: scroll">
	<div id="AuditLog"
		style="position: absolute; z-index: 99; left: 0px; top: 0px; width: 755px; height: 10px;">


	<table class="sort-table" id="table_2" bgcolor="#B0AFAF" width="400"
		border="0" cellspacing="1" cellpadding="0" height="20">
		<thead>
			<tr>
				<td title='<s:text name= "tooltip.sortField"/>' width="190px"
					height="20px" align="center" style="border-left-width: 0px;"><b><s:text name="maintenanceLog.columnName" /></b></td>
				<td title='<s:text name= "tooltip.sortOldValue"/>'
					width="280px" height="20px" align="center"><b><s:text name="maintenanceLog.oldValue" /></b></td>
				<td title='<s:text name= "tooltip.sortNewValue"/>'
					width="280px" height="20px" align="center"><b><s:text name="maintenanceLog.newValue" /></b></td>
			</tr>
		</thead>
	</table>
	</div>
	<div id="ddscrolltable"
		style="position: absolute; left: 0px; top: 0px; width: 772px; height: 270px; overflowY: scroll">
	<div id="AuditLog"
		style="position: absolute; z-index: 99; left: 1px; top: 22px; width: 735px; height: 10px;">
	<table class="sort-table" id="auditLogList" width="755" border="0"
		cellspacing="1" cellpadding="0" height="248">
		<tbody>
			<%
				int count = 0;
			%>
			<s:iterator value="#request.auditLogList">     
				<%
					if (count % 2 == 0) {
				%><tr class="even">
					<%
						} else {
					%>
				
				<tr class="odd">
					<%
						}
							++count;
					%>

					<td width="190px" align="left"><s:property value="id.columnName" />&nbsp;</td>
					<td width="280px"><s:property value="id.oldValue" />&nbsp;</td>
					<td width="280px"><s:property value="id.newValue" />&nbsp;</td>
			</s:iterator>
		</tbody>
		<tfoot>
			<tr>
				<td colspan="3"></td>
			</tr>
		</tfoot>
	</table>
	</div>
	</div>
	</div>
	<div id="UserAuditLog"
		style="position: absolute; left: 720; top: 314px; width: 70px; height: 39px; visibility: visible;">
	<table width="60" border="0" cellspacing="0" cellpadding="0"
		height="20">
		<tr>

			<td align="Right"><a
				title='<s:text name= "tooltip.helpScreen"/>' tabindex="8"
				href=#
				onclick="javascript:openWindow(buildPrintURL('print','View User Audit Log '),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')"
				onMouseOut="MM_swapImgRestore()"
				onMouseOver="MM_swapImage('Help','','images/help_default.GIF ',1)"><img
				src="images/help_default.GIF " name="Help" border="0"></a></td>


			<td align="right" id="Print"><a tabindex="8"
				onclick="printPage();" onMouseOut="MM_swapImgRestore()"
				onMouseOver="MM_swapImage('Print','','images/Print_R.gif ',1)"><img
				src="images/Print.gif " name="Print" border="0"
				title='<s:text name= "tooltip.printScreen"/>'></a></td>
		</tr>
	</table>
	</div>

	<div id="ddimagebuttons" color="#7E97AF"
		style="position: absolute; border: 2px outset; left: 20px; top: 305; width: 777; height: 39px; visibility: visible;">
	<!--  End Modified height of the screen to restrict filter drop down hit close button 
		  for mantis 2069 by Vivekanandan A on 10-Oct-2012 -->
	<div id="UserLog"
		style="position: absolute; left: 6; top: 4; width: 400; height: 15px; visibility: visible;">
	<table width="700" border="0" cellspacing="0" cellpadding="0"
		height="20">
		<tr>

			<a title='<s:text name= "tooltip.close"/>' tabindex="1"
				onMouseOut="collapsebutton(this)"
				onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)"
				onMouseUp="highlightbutton(this)" onclick="confirmClose('C');"><s:text name="button.close" /></a>

		</tr>
	</table>
	</div>
	</div>



</s:form>
</body>
</html>
