<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@ include file="/taglib.jsp"%>
<%@ page import="org.swallow.util.SwtConstants"%>
<%@ page import="org.swallow.util.SwtUtil"%>
<%@page import="org.swallow.work.model.Movement"%>
<%
	//variable declaration
	String selectedMvtIdsList = "";
	String fromMenu = "";
	String entityId = "";

	//get the selectedRows from request attribute
	if (request.getAttribute("selectedMvtIdsList") != null) {
		selectedMvtIdsList = request.getAttribute("selectedMvtIdsList")
				.toString();
	}
	//get the fromMenu from request attribute
	if (request.getAttribute("fromMenu") != null) {
		fromMenu = request.getAttribute("fromMenu")
				.toString();
	}
	//get the entityId from request attribute
	if (request.getAttribute("entityId") != null) {
		entityId = request.getAttribute("entityId")
				.toString();
	}

%>
<html>
<head>
	<title><s:text name="MultiMvtActions.title.window"/> </title>
	<%@ include file="/angularJSUtils.jsp"%>
	<link rel="stylesheet" href="angularSources/styles.css?version=<%= SwtUtil.appVersion %>">
	<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
</head>
<SCRIPT language="JAVASCRIPT">
	var requestURL = new String('<%=request.getRequestURL()%>');
	var appName = "<%=SwtUtil.appName%>";
	var idy = requestURL.indexOf('/'+appName+'/');
	requestURL=requestURL.substring(0,idy+1);
	var oXMLHTTP = new XMLHttpRequest();
	var screenRoute = "MultipleMvtActions";
	var uploadFileImage = "images/open_up.png";
	var selectedMvtIdsList = "<%= selectedMvtIdsList %>";
	var entityId = "<%= entityId %>";
	var fromMenu = "<%= fromMenu %>";
	/**
	 *	This section is used to handle calender button on the screen && is used to set the position of the same.
	 */
	var dateFormatValue = '${sessionScope.CDM.dateFormatValue}';
	var menuAccessId = "${requestScope.menuAccessId}";

	var selectedMovementForLock="";
	function setSelectedMovementForLock(selected){
		selectedMovementForLock = selected;
		console.log("selectedMovementForLock: ", selectedMovementForLock);
	}


	function deleteLock()
	{
		console.log("selectedMovementForLock: ", selectedMovementForLock);
		beaconUnlock(selectedMovementForLock);
		if (window.opener && !window.opener.closed) {
			window.opener.postMessage({ type: 'callbackApp', data: { } }, '*');
		}
	}



	// For page unload cases
	function beaconUnlock(movementIds) {
		try {
			var appName = "<%=SwtUtil.appName%>";
			var requestURL = new String('<%=request.getRequestURL()%>');
			var idy = requestURL.indexOf('/' + appName + '/');
			requestURL = requestURL.substring(0, idy + 1);
			const sURL = requestURL + appName + "/movementLock.do?method=unlockMovements";

			// Create the data to be sent
			const formData = new FormData();
			formData.append('movementIds', movementIds);

			// Send using beacon API
			const success = navigator.sendBeacon(sURL, formData);
			return success ? "success" : "error";
		} catch (error) {
			console.error('Error during beacon unlock:', error);
			return "error";
		}
	}
</SCRIPT>


<body scroll="no" style="overflow:hidden" leftmargin="0" topmargin="0" marginheight="0" onunload="deleteLock()">
<%@ include file="/angularscripts.jsp"%>
<form id="exportDataForm" target="tmp" method="post">
	<input type="hidden" name="data" id="exportData" /> <input
		type="hidden" name="screen" id="exportDataScreen"
		value="<s:text name="PreAdviceInput.title.window"/>" /></form>
<iframe name="tmp" width="0%" height="0%" src="#" />
</body>
</html>
