<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<!--
  - auditlog.jsp
  -
  - The main purpose of this jsp file is to load the Movement Audit Log and User Audit Log screen.<br>
  - 
  - Modified By: Marshal .I
  - Date: 06-September-2011
  -->
<%@page import="org.swallow.util.PageDetails"%>
<%@ include file="/taglib.jsp"  %>
<%@ page import="org.swallow.util.SwtConstants" %>
<%@ page import="org.swallow.util.CommonDataManager" %>
<%@page import="org.swallow.control.model.AuditLog"%>
<html>
<head>


<title>
<s:if test='"movementLog" == #request.methodName' >
	<s:text name="movement.screen"/>
</s:if>
<s:if test='"movementLog" != #request.methodName' > 
	<s:text name="user.screen"/>
</s:if>
</title>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">



<script type="text/javascript" src="js/jquery.exportselect.js"></script>
<SCRIPT language="JAVASCRIPT">
var dateFlag=true;
mandatoryFieldsArray = ["userLogFromDate","userLogToDate","auditLogFromDate","auditLogToDate"] ;
var dateFormat = '<s:property value="#request.session.CDM.dateFormat" />';
var cancelcloseElements = new Array(1);
	cancelcloseElements[0]="closebutton";

var movId="${requestScope.selectedMovementId}";
var currentFilter="${requestScope.selectedFilter}";
var currentSort = "${requestScope.selectedSort}";
var dateFormat = '<s:property value="#request.session.CDM.dateFormat" />';
var filterValues=new Array();
var currentFilterValues = currentFilter.split("|");
var sortingValues = currentSort.split("|");
var sortedValues = new Array();
var totalCount = '${totalCount}';
var dateSelected = false;
sortedValues[0] = sortingValues[0];
sortedValues[1] = sortingValues[1];
var lastRefTime = "${requestScope.lastRefTime}";
var maxPage = "${requestScope.maxPage}";
var currPage = '${requestScope.currentPage}';
// Added by Vivekanandan A for mantis 1993 on 26-Oct-2012
var fromDateValue="";
var toDateValue="";
	 
var entityId='${requestScope.entityId}';
function sortDateValidation() {
if(validateForm(document.forms[0]) ){
	if(validateField(document.forms[0].elements['auditLog.fromDateAsString'],'auditLog.from',dateFormat) && validateField(document.forms[0].elements['auditLog.toDateAsString'],'auditLog.to',dateFormat)){
			dateFlag = true;
			return dateFlag;
			} else {
				dateFlag = false;
				return dateFlag;
				}	
		}
}

/**
* This method is used for  view table for non movementLog screen

* @return none
*/

function disableAllButtons()
{
<s:if test='"movementLog" != #request.methodName' >
document.getElementById("viewbutton").innerHTML = document.getElementById("viewdisablebutton").innerHTML; 
</s:if>
}
function onFilterandSort()
{
	updateColors();
	disableAllButtons();

}

	<s:if test='"movementLog" == #request.methodName' >
		/** 
		 * This method is used to load the Movement log screen when the screen opens
		 * @return none
		 */	
		function bodyOnLoad(){
		    var dropBox1 = new SwSelectBox(document.forms[0].elements["auditLog.dropDownUserId"],document.getElementById("userName"));
		    // Filter for newly added field for Movement Audit Log screen
			xl = new XLSheet("auditLogList","table_2", [dateFormat, "String", "String", "String","String", "String", "String"],"1111111","false",currentFilterValues,sortedValues);
		
			xl.onsort = xl.onfilter = onFilterandSort;
			highlightTableRows("auditLogList");
		
			<s:if test='"mLog" == #request.mLog' >
			var dropBox1 = new SwSelectBox(document.forms[0].elements["auditLog.dropDownUserId"],document.getElementById("userName"));
			</s:if>
		    document.forms[0].movementId.value="${requestScope.selectedMovementId}";
			document.forms[0].archiveId.value = "${archiveId}";
			
			document.getElementById("mvmtId").innerText = movId;
			// Start Added by Vivekanandan A for mantis 1993 on 26-Oct-2012
			fromDateValue = document.forms[0].elements['auditLog.fromDateAsString'].value;
			toDateValue = document.forms[0].elements['auditLog.toDateAsString'].value;
			// End Added by Vivekanandan A for mantis 1993 on 26-Oct-2012
			var headerData = [];
			var dataprovider = new Array();
			var newElement1 = {};
			newElement1[headerData[0]] = 'Pdf';
			dataprovider.push(newElement1);
					
			var newElement2 = {};
			newElement2[headerData[0]] = 'Excel';
			dataprovider.push(newElement2);
					
			var newElement3 = {};
			newElement3[headerData[0]] = 'Csv';
			dataprovider.push(newElement3);
			
			$("#exportReport").exportselect ({
				dataprovider: dataprovider,
				change: exportReport,
				selectedIndex:0
			  });
			if(totalCount==0)
			 $("#exportReport").disabled(true); 
			else
			 $("#exportReport").disabled(false); 
		}

	</s:if>

	<s:if test='"movementLog" != #request.methodName' >
		/** 
	 	 * This method is used to load the User log screen when the screen opens
	     * @return none
	     */
		function bodyOnLoad(){
	
		    var dropBox1 = new SwSelectBox(document.forms[0].elements["auditLog.dropDownUserId"],document.getElementById("userName"));
			xl = new XLSheet("auditLogList","table_2", [dateFormat, "String", "String", "String", "Number", "String"],"111111","false",currentFilterValues,sortedValues);
			xl.onsort = xl.onfilter = onFilterandSort;
			highlightTableRows("auditLogList");
			disableAllButtons();
			<s:if test='"mLog" == #request.mLog' >
			var dropBox1 = new SwSelectBox(document.forms[0].elements["auditLog.dropDownUserId"],document.getElementById("userName"));
			</s:if>
		
		    var v1 = document.getElementById("UserLog");
		    var v2  = v1.getElementsByTagName("input")[0];
		    v2.focus();
	            var rows = xl.dataTable.tBody.rows;	
                    var l = rows.length;
                    var selectedReference = getStoredParam('selectedReference');        
	              for (var i=0; i<l; i++)
                        {
			if(rows[i].cells[4].innerText.trim()== selectedReference) {
					highLightTableRow(rows[i]);
					rows[i].className = 'selectrow';
                                        break;
			}
		        }
			document.getElementById("lastRefTime").innerText = lastRefTime;
			// Start Added by Vivekanandan A for mantis 1993 on 26-Oct-2012
			fromDateValue = document.forms[0].elements['auditLog.fromDateAsString'].value;
			toDateValue = document.forms[0].elements['auditLog.toDateAsString'].value;
			// End Added by Vivekanandan A for mantis 1993 on 26-Oct-2012
		}
	</s:if>
	
	
	
	function exportReport(){
		var type=$("#exportReport").getSelected(0);
		return submitForm('exportAuditLogs',type.toLowerCase());
	}
/**
* Submitting form while refreshing screen
* @param methodName
*/
function submitForm(methodName){
	//disable the regfresh button
	document.getElementById("refreshbutton").innerHTML = document.getElementById("refreshdisablebutton").innerHTML;
	//validate the form
	if(validateForm(document.forms[0]) ){
	//compare dates and submitting form   
	if(comparedates(document.forms[0].elements['auditLog.fromDateAsString'].value,document.forms[0].elements['auditLog.toDateAsString'].value,dateFormat,'From Date','To Date')){
	document.forms[0].method.value = methodName;
	document.forms[0].entityCode.value = entityId;
	<s:if test='"mLog" != #request.mLog' >
	document.forms[0].selectedDropDownUserId.value = document.forms[0].elements["auditLog.dropDownUserId"].value;
	enableFields();
	</s:if>
	document.forms[0].totalCount.value = '${requestScope.totalCount}';
	document.forms[0].currentPage.value = '${currentPage}';
	document.forms[0].submit();
	}
	}
}
/**
*	This function used to show the movement log after date change
*/
function showMovementDetails(methodName){
//disabling the refresh button
document.getElementById("refreshbutton").innerHTML = document.getElementById("refreshdisablebutton").innerHTML;
//Comparing Dates and submitting form
if(comparedates(document.forms[0].elements['auditLog.fromDateAsString'].value,document.forms[0].elements['auditLog.toDateAsString'].value,dateFormat,'From Date','To Date')){
document.forms[0].method.value = methodName;
document.forms[0].entityCode.value = entityId;
document.forms[0].selectedMovementId.value = "${requestScope.selectedMovementId}";
document.forms[0].archiveId.value = "${archiveId}";
document.forms[0].submit();
}
}
function enableFields(){
<s:if test='"mLog" == #request.mLog' >
	document.forms[0].elements["auditLog.dropDownUserId"].disabled = "";
</s:if>
}


	
function onDateKeyPress(obj,e){
	
	var event = (window.event|| e);

	if(event.keyCode == 9){   // tab
		if(comparedates(document.forms[0].elements['auditLog.fromDateAsString'].value,document.forms[0].elements['auditLog.toDateAsString'].value,dateFormat,'From Date','To Date')){
		
		if(validateField(obj,'To',dateFormat)){
		
			document.getElementById("refreshbutton").innerHTML = document.getElementById("refreshdisablebutton").innerHTML;

			<s:if test='"movementLog" == #request.methodName' >
				showMovementDetails('showMovementDetails')
			</s:if>

			<s:if test='"movementLog" != #request.methodName' >
				submitForm('showDetails');
			</s:if>
			
			}
		else
		{
			return false;
			}
	}
	}
	if(event.keyCode == 13){  //enter
	if(comparedates(document.forms[0].elements['auditLog.fromDateAsString'].value,document.forms[0].elements['auditLog.toDateAsString'].value,dateFormat,'From Date','To Date')){
		if(validateField(obj,'To',dateFormat)){

			<s:if test='"movementLog" == #request.methodName' >
				showMovementDetails('showMovementDetails')
			</s:if>

			<s:if test='"movementLog" != #request.methodName' >
				submitForm('showDetails');
			</s:if>
	
			
			}
		else
		{
			return false;
			}
		}
		}
}


/**
 * Method to opeb View log screen
 * @param methodName
 */
function buildReferenceIdURL(methodName){
dateSelected = false;
if(validateDateField(document.forms[0].elements['auditLog.fromDateAsString'],'auditLog.from',dateFormat)) {		
			if(validateDateField(document.forms[0].elements['auditLog.toDateAsString'],'auditLog.to',dateFormat)) {			
				if(comparedates(document.forms[0].elements['auditLog.fromDateAsString'].value,document.forms[0].elements['auditLog.toDateAsString'].value,dateFormat,'From','To')) {
		var param = 'auditlog.do?method='+methodName+'&selectedReferenceId=';
	    param +=document.forms[0].selectedReferenceId.value;

		param +='&selectedUserId=';
		param += document.forms[0].selectedUserId.value;
		param +='&selectedDate=';
		param +=document.forms[0].selectedDate.value;
		param +='&selectedTime=';
		param +=document.forms[0].selectedTime.value;
		param +='&selectedReference='; 
		param +=document.forms[0].selectedReference.value;
		param +='&selectedAction='; 
		param +=document.forms[0].selectedAction.value;
		param +='&logList='; 
		param +='${LogList}';
		param += '&archiveId=${archiveId}';
							window.openWindow(param,'','left=50,top=190,width=843,height=368,toolbar=0, resizable=yes scrollbars=yes','true');
				
	   }else{
			return false;
				}
			}else{			
				return false;
			}
		}else{		
			return false;
		}	
}

function onSelectTableRow(rowElement,isSelected)
	{

	document.forms[0].selectedReference.value = rowElement.getElementsByTagName("input")[0].value;
	document.forms[0].selectedReferenceId.value = rowElement.getElementsByTagName("input")[1].value;
	if(isSelected){
			setStoredParam('selectedReference',rowElement.cells[4].innerText.trim());
		}else 
		   setStoredParam('selectedReference','');
	  
	document.forms[0].selectedDate.value = rowElement.getElementsByTagName("input")[2].value;
	document.forms[0].selectedTime.value = rowElement.getElementsByTagName("input")[3].value;
	document.forms[0].selectedUserId.value = rowElement.getElementsByTagName("input")[4].value;
	var selectedAction = rowElement.getElementsByTagName("input")[5];
		document.forms[0].selectedAction.value = selectedAction.value;

	if((selectedAction.value=="Changed" || selectedAction.value=="Matched" || selectedAction.value=="Unmatched" ) && isSelected &&  (selectedAction.value!="Input" || selectedAction.value!="Deleted" ))
	{
	// enabling the view button when screen is not a Movement Audit Log
	<s:if test='"movementLog" != #request.methodName' >
	document.getElementById("viewbutton").innerHTML = document.getElementById("viewenablebutton").innerHTML;
	</s:if>
	}
	else
	{
	 disableAllButtons();
	}

}

function storeToDate(){
document.forms[0].elements['preUserToDateAsString'].value = document.forms[0].elements['auditLog.toDateAsString'].value;

}
/** 
* This method is called when change Todate and validate date field
* @return none
*/
function onToDateChange(){
	if(dateSelected){

	var preToDate = document.forms[0].elements['preUserToDateAsString'].value;
	var from_date=document.forms[0].elements['auditLog.fromDateAsString'].value;
	var to_date=document.forms[0].elements['auditLog.toDateAsString'].value;
	if(from_date != "" && to_date != "") {
	if(validateField(document.forms[0].elements['auditLog.toDateAsString'],'auditLog.to',dateFormat)){
	cal2.hideCalendar()
	if(validateField(document.forms[0].elements['auditLog.fromDateAsString'],'auditLog.from',dateFormat)){	
	var dateFormatValue = '<s:property value="#request.session.CDM.dateFormatValue" />';
	var compare_date=compareDates(from_date,dateFormatValue,to_date,dateFormatValue);
	if(compare_date == 1)
		{
			document.forms[0].elements['auditLog.toDateAsString'].value=preToDate;
			cal2.hideCalendar()
			alert('<s:text name="alert.dateShouldBeGreater"/>');			
			
		}else{
      document.getElementById("refreshbutton").innerHTML = document.getElementById("refreshdisablebutton").innerHTML;
     // filter should not retain when changing the date
	  document.forms[0].selectedFilter.value="";

			<s:if test='"movementLog" == #request.methodName' >
				showMovementDetails('showMovementDetails')
			</s:if>

			<s:if test='"movementLog" != #request.methodName' >
				document.forms[0].method.value = 'showDetails';
				document.forms[0].submit();
			</s:if>
		}
		dateSelected = false;
		return true;
	} else {
			dateSelected = false;
			return false;
		}
		} else {
			dateSelected = false;
			return false;
		}
		} else {
				if (from_date == "") {
					document.forms[0].elements['auditLog.fromDateAsString'].focus();
					dateSelected = false;
				}
				if (to_date == "") {
					document.forms[0].elements['auditLog.toDateAsString'].focus();
					dateSelected = false;
				}
			}
	} else {
			return false;
		}
}




function storeFromDate(){

document.forms[0].elements['preUserFromDateAsString'].value = document.forms[0].elements['auditLog.fromDateAsString'].value;
}

/**
* This method is called when change Fromdate and validate date field
* @return none
*/
function onFromDateChange(){
	if(dateSelected){
	
	var preFromDate = document.forms[0].elements['preUserFromDateAsString'].value;
	var from_date=document.forms[0].elements['auditLog.fromDateAsString'].value;
	var to_date=document.forms[0].elements['auditLog.toDateAsString'].value;
	if(from_date != "" && to_date != "") {
	if(validateField(document.forms[0].elements['auditLog.fromDateAsString'],'auditLog.from',dateFormat)){
	cal.hideCalendar()
	if(validateField(document.forms[0].elements['auditLog.toDateAsString'],'auditLog.to',dateFormat)){	
	var dateFormatValue = '<s:property value="#request.session.CDM.dateFormatValue" />';
	var compare_date=compareDates(from_date,dateFormatValue,to_date,dateFormatValue);
	if(compare_date == 1)
		{
			document.forms[0].elements['auditLog.fromDateAsString'].value=preFromDate;
			cal.hideCalendar()
			alert("From Date should be less than or equal to To Date");
		}else { 
		
      document.getElementById("refreshbutton").innerHTML = document.getElementById("refreshdisablebutton").innerHTML;
	  // filter should not retain when changing the date
	  document.forms[0].selectedFilter.value="";

			<s:if test='"movementLog" == #request.methodName' >
				showMovementDetails('showMovementDetails')
			</s:if>

			<s:if test='"movementLog" != #request.methodName' >
				document.forms[0].method.value = 'showDetails';
				document.forms[0].submit();
			</s:if>	 
		}
		dateSelected = false;
		return true;
		} else {
			dateSelected = false;
			return false;
			}
			} else {
				dateSelected = false;
				return false;
			}
		} else {
				document.forms[0].elements['auditLog.toDateAsString'].focus();
				dateSelected = false;
			}		

	}
}


/*This function validates the To Date fielf when 'Enter or Tab' is been pressed.*/	 
function onToDateKeyPress(obj,e){
	dateSelected = false;
	var event = (window.event|| e);
	if(event.keyCode == 9){   // tab
	if(validateForm(document.forms[0])) {
	if (document.forms[0].elements['auditLog.fromDateAsString'].value != "" && document.forms[0].elements['auditLog.toDateAsString'].value != "") {
		if(validateField(obj,'auditLog.to',dateFormat) && validateField(document.forms[0].elements['auditLog.fromDateAsString'],'auditLog.from',dateFormat)){
		if(comparedates(document.forms[0].elements['auditLog.fromDateAsString'].value,document.forms[0].elements['auditLog.toDateAsString'].value,dateFormat,'From Date','To Date')) {
			<s:if test='"movementLog" == #request.methodName' >
				showMovementDetails('showMovementDetails')
			</s:if>

			<s:if test='"movementLog" != #request.methodName' >
				document.forms[0].method.value = 'showDetails';
				document.forms[0].submit();
			</s:if>
			} else {			
			return false;
			}
			} else {			
			return false;
			}			
			} else {
					alert("<s:text name="alert.acctBreakdown.date"/>");
				if (document.forms[0].elements['auditLog.fromDateAsString'].value == "") {					
					document.forms[0].elements['auditLog.fromDateAsString'].focus();
					dateSelected = false;
				} else {									
					document.forms[0].elements['auditLog.toDateAsString'].focus();
					dateSelected = false;				
				}		
			}
			}
	}
	if(event.keyCode == 13){  //enter
	if(validateForm(document.forms[0])) {
	if (document.forms[0].elements['auditLog.fromDateAsString'].value != "" && document.forms[0].elements['auditLog.toDateAsString'].value != "") {
		if(validateField(obj,'auditLog.to',dateFormat) && validateField(document.forms[0].elements['auditLog.fromDateAsString'],'auditLog.from',dateFormat)){
			if(comparedates(document.forms[0].elements['auditLog.fromDateAsString'].value,document.forms[0].elements['auditLog.toDateAsString'].value,dateFormat,'From Date','To Date')) {
			<s:if test='"movementLog" == #request.methodName' >
				showMovementDetails('showMovementDetails')
			</s:if>

			<s:if test='"movementLog" != #request.methodName' >
				document.forms[0].method.value = 'showDetails';
				document.forms[0].submit();
			</s:if>
			} else {			
			return false;
			}
			} else {			
			return false;
			}
			} else {
					alert("<s:text name="alert.acctBreakdown.date"/>");
				if (document.forms[0].elements['auditLog.fromDateAsString'].value == "") {					
					document.forms[0].elements['auditLog.fromDateAsString'].focus();
					dateSelected = false;
				} else {									
					document.forms[0].elements['auditLog.toDateAsString'].focus();
					dateSelected = false;				
				}		
			}
			}
		}
}

/*This function validates the From Date fielf when 'Enter or Tab' is been pressed.*/
function onFromDateKeyPress(obj,e){
	dateSelected = false;		
	var event = (window.event|| e);
	if(event.keyCode == 9){   // tab
	if(validateForm(document.forms[0])) {
		if(document.forms[0].elements['auditLog.fromDateAsString'].value != "" && document.forms[0].elements['auditLog.toDateAsString'].value != "") {
		if(validateField(obj,'auditLog.from',dateFormat) && validateField(document.forms[0].elements['auditLog.toDateAsString'],'auditLog.to',dateFormat)){
			if(comparedates(document.forms[0].elements['auditLog.fromDateAsString'].value,document.forms[0].elements['auditLog.toDateAsString'].value,dateFormat,'From Date','To Date')){
			<s:if test='"movementLog" == #request.methodName' >
				showMovementDetails('showMovementDetails')
			</s:if>

			<s:if test='"movementLog" != #request.methodName' >
				document.forms[0].method.value = 'showDetails';
				document.forms[0].submit();
			</s:if>
			} else {			
				return false;
				}
			} else {			
				return false;
			}
		} else {
			alert("Please enter valid date");
			document.forms[0].elements['auditLog.fromDateAsString'].focus();
			dateSelected = false;
		}
		}
		
		 
	}
	if(event.keyCode == 13){  
	if(validateForm(document.forms[0])) {
		if(document.forms[0].elements['auditLog.fromDateAsString'].value != "" && document.forms[0].elements['auditLog.toDateAsString'].value != "") {
		if(validateField(obj,'auditLog.from',dateFormat) && validateField(document.forms[0].elements['auditLog.toDateAsString'],'auditLog.to',dateFormat)){
			if(comparedates(document.forms[0].elements['auditLog.fromDateAsString'].value,document.forms[0].elements['auditLog.toDateAsString'].value,dateFormat,'From Date','To Date')) {
			<s:if test='"movementLog" == #request.methodName' >
				showMovementDetails('showMovementDetails')
			</s:if>

			<s:if test='"movementLog" != #request.methodName' >
				document.forms[0].method.value = 'showDetails';
				document.forms[0].submit();
			</s:if>			
			} else {			
				return false;
				}
			} else {			
				return false;
				}
		} else {
			alert("<s:text name="alert.acctBreakdown.date"/>");
			document.forms[0].elements['auditLog.fromDateAsString'].focus();
			dateSelected = false;
			}
			}
		}		
}

/*This function validates the Date fields when the refresh button is pressed*/
function refreshWindow(){	
	dateSelected = false;
	var dateFormat = '<s:property value="#request.session.CDM.dateFormat" />';		
		if(validateDateField(document.forms[0].elements['auditLog.fromDateAsString'],'auditLog.from',dateFormat)) {		
			if(validateDateField(document.forms[0].elements['auditLog.toDateAsString'],'auditLog.to',dateFormat)) {			
				if(comparedates(document.forms[0].elements['auditLog.fromDateAsString'].value,document.forms[0].elements['auditLog.toDateAsString'].value,dateFormat,'From','To')) {
					<s:if test='"movementLog" == #request.methodName' >
				showMovementDetails('showMovementDetails')
			</s:if>

			<s:if test='"movementLog" != #request.methodName' >
				document.forms[0].method.value = 'showDetails';
				document.forms[0].submit();
			</s:if>
				}else {
					return false;
				}
			} else {			
				return false;
			}
		} else {		
			return false;
		}	
}


function validateForm(objForm){
  var elementsRef = new Array(2);
  elementsRef[0] = objForm.elements["auditLog.fromDateAsString"];
  elementsRef[1] = objForm.elements["auditLog.toDateAsString"];
  if(validate(elementsRef))
  {
    if(comparedates(document.forms[0].elements['auditLog.fromDateAsString'].value,document.forms[0].elements['auditLog.toDateAsString'].value,dateFormat,'From Date','To Date'))
     {
      return true;
     }    
   }
   return false;
}	
 function clickMessageFields(element)
 {
     
	if(validateForm(document.forms[0]))
	{
        openWindow(buildURL('showDetails'),'','left=20,top=150,width=600,height=400,toolbar=0, resizable=yes, scrollbars=yes','true');
		
	}


}




/* Start Code:Modified by Naseema.Sd for Mantis 1993*/

/*
 * This function is used to validate the page Number
 * @param strObject
 */
function validatePageNumber(strObject) {
	dateSelected = false;
	// Condition to check from date is empty to set on load from date
	if (document.forms[0].elements['auditLog.fromDateAsString'].value == ""){
		document.forms[0].elements['auditLog.fromDateAsString'].value = fromDateValue;
	}
	// Condition to check to date is empty to set on load to date
	if (document.forms[0].elements['auditLog.toDateAsString'].value == ""){
		document.forms[0].elements['auditLog.toDateAsString'].value = toDateValue;
	}
	// Validate from and to date
	if(validateField(document.forms[0].elements['auditLog.fromDateAsString'],'auditLog.from',dateFormat)){
		if(validateField(document.forms[0].elements['auditLog.toDateAsString'],'auditLog.to',dateFormat)){
			// compare from and to date and set on load from and to date if range differs
			if(!comparedates(document.forms[0].elements['auditLog.fromDateAsString'].value,document.forms[0].elements['auditLog.toDateAsString'].value,dateFormat,'From Date','To Date')){
				document.forms[0].elements['auditLog.fromDateAsString'].value = fromDateValue;
				document.forms[0].elements['auditLog.toDateAsString'].value = toDateValue;
			}
   			var re = /^\d+$/;
			if (strObject && (re.test(strObject.value) && strObject.value != 0)){
				if(Number(strObject.value) > maxPage){
					strObject.value = maxPage;
				}
				goToResultsPage(strObject.value);
			}else{
				alert("<s:text name="alert.acctBreakdown.date"/>");
				strObject.value = currPage;
			}
		} else {
			return false;
		}
	} else {
		return false;
	}
	
}
/* End Code:Modified by Naseema.Sd for Mantis 1993*/

/* Start Code:Modified by Naseema.Sd for Mantis 1993*/

/* This method is used to go to the result page by clicking the Enter Key
*@param goToPageNo
*/
function goToResultsPage(goToPageNo)
{

	document.getElementById("refreshbutton").innerHTML = document.getElementById("refreshdisablebutton").innerHTML;
	var currPage = "${requestScope.currentPage}";
	document.forms[0].method.value = "next";
	document.forms[0].goToPageNo.value=goToPageNo;
	document.forms[0].currentPage.value=currPage;
	document.forms[0].maxPages.value=maxPage;
	document.forms[0].selectedSort.value=currentSort;
	document.forms[0].selectedFilter.value=currentFilter;
	document.forms[0].fromDate.value=document.forms[0].elements['auditLog.fromDateAsString'].value;
	document.forms[0].toDate.value=document.forms[0].elements['auditLog.toDateAsString'].value;
	document.forms[0].movId.value=movId;
	<s:if test='"movementLog" != #request.methodName' > 
	document.forms[0].selectedDropDownUserId.value=document.forms[0].elements["auditLog.dropDownUserId"].value;
	 </s:if>
    document.forms[0].submit();
}
/* End Code:Modified by Naseema.Sd for Mantis 1993*/

/* Start Code:Modified by Naseema.Sd for Mantis 1993*/
/**
* This method is used to validate the pagination
* @return none
*/
function clickLink(goToPageNo) {

	dateSelected = false;
	
	// Condition to check from date is empty to set on load from date
	if (document.forms[0].elements['auditLog.fromDateAsString'].value == ""){
		document.forms[0].elements['auditLog.fromDateAsString'].value = fromDateValue;
	}
	// Condition to check to date is empty to set on load to date
	if (document.forms[0].elements['auditLog.toDateAsString'].value == ""){
		document.forms[0].elements['auditLog.toDateAsString'].value = toDateValue;
	}
	// Validate from and to date
	if(validateField(document.forms[0].elements['auditLog.fromDateAsString'],'auditLog.from',dateFormat)){
		if(validateField(document.forms[0].elements['auditLog.toDateAsString'],'auditLog.to',dateFormat)){
			// compare from and to date and set on load from and to date if range differs  
			if(!comparedates(document.forms[0].elements['auditLog.fromDateAsString'].value,document.forms[0].elements['auditLog.toDateAsString'].value,dateFormat,'From Date','To Date')){
				document.forms[0].elements['auditLog.fromDateAsString'].value = fromDateValue;
				document.forms[0].elements['auditLog.toDateAsString'].value = toDateValue;
			}
			/*  START:Modified by Mefteh Bouazizi for Mantis_1534 to convert GET URL to POST URL using a form */
			 var baseUrl = '<%=SwtUtil.convertBeanToUrlParams((PageDetails)((ArrayList<PageDetails>)request.getAttribute("pageSummaryList")).get(0), "pageDetails") %>';
        	 var url='auditlog.do?'+baseUrl;
        	 url +='method=next&goToPageNo='+goToPageNo+'&selectedSort='+currentSort+'&selectedFilter='+currentFilter+'&fromDate='+document.forms[0].elements['auditLog.fromDateAsString'].value;
        	 url +='&toDate='+document.forms[0].elements['auditLog.toDateAsString'].value+'&movId='+movId;
			<s:if test='"movementLog" != #request.methodName' > 
			 url +='&selectedDropDownUserId='+document.forms[0].elements["auditLog.dropDownUserId"].value;
			</s:if>
   	         submitFormFromURL(url,window); 
			/*  END:Modified by Mefteh Bouazizi for Mantis_1534 to convert GET URL to POST URL using a form */
		} else {
			return false;
		}
	} else {
		return false;
	}
	
}
/* End Code:Modified by Naseema.Sd for Mantis 1993 */




/**
* This function is used to sort the data grid based on the given filter criteria.<br> 
*
* @param index - Column index from where the filter is triggered.
* @param value - Column value used for filter
* @param action - Which determines whether it's Filter sort of Column sort
* 
*/
function optionClick_server_filter_JSP(index,value,action) {	
	// Checks whether the action is Filter or Column sort
	if(action == "filter"){
		value = replace(value,'&nbsp;',' ');
		value = value.trim();
				
		var filterValue ="";
		//replace variable "row" by 0 previous value of "row" is undefined in this location
		var tempFilterValue = document.getElementById('auditLogList').rows[0].cells[parseInt(index)].innerText;
		var modifiedFilterValue = document.getElementById('auditLogList').rows[0].cells[parseInt(index)].title;
		// Checks the current filter (Default filter value once the page loaded)
		if(currentFilter =="all" || currentFilter=="undefined") {
			// Iterates through the grid data till the length of the total columns
			for(var idx = 0 ; idx < xl.numColumns ; ++idx) {
				// Checks the index of the column to be filtered
				if(idx == index) {
					// Iterates through the data grid rows to validate the characters in the filter value
					for (var row=0 ;row<document.getElementById('auditLogList').rows.length;row++) {
						// Validate the filter value if it is not 'All', 'Not empty' and 'Empty'
						if(value!="All" && value!= "(Not empty)" && value!= "(Empty)" && document.getElementById('auditLogList').rows[row].cells.length>2) {
							// Calling the generic validation function to validate the filter value
							filterValidation(value, tempFilterValue, modifiedFilterValue);
							break;
						}
					}
					// Appends the filterValue based on the index
					filterValue +=  value + "|";
					} else { 
					// Appends the filterValue with 'All' for all criteria other than the filter value index
					filterValue +=  "All" + "|";
					}
			}
		}else{
			for (var row=0 ;row<document.getElementById('auditLogList').rows.length;row++) {
				// Checks the number of rows for the filter value
				if(value!="All" && document.getElementById('auditLogList').rows[row].cells.length>2) {
					// Calling the generic validation function to validate the filter value
					filterValidation(value, tempFilterValue, modifiedFilterValue);
					break;
				}
			}
			// Gets the filter value by spliting the currentFilter
			var filter=currentFilter.split("|");
			// Gets the filter value from the current filter
			filter[index]=value;
			for(var idx = 0 ; idx < xl.numColumns ; ++idx) {
				// Appends the filter value for filter sort
				filterValue+=filter[idx] + "|";
			}
		}
		// Sets the current sort value
		document.forms[0].selectedSort.value=currentSort;
		// Sets the current filter value
		document.forms[0].selectedFilter.value =filterValue ;
		// Sets the current page
		document.forms[0].currentPage.value = '${currentPage}';
	}else{
		// Gets the column index
		var sortColum=index;
		// Holds the sort value
		var sortDesc=value;
		// Sets the selected sort value
		document.forms[0].selectedSort.value=sortColum + "|" +sortDesc;
		// Sets the selected filter
		document.forms[0].selectedFilter.value =currentFilter ;
		// Sets the current page value
		document.forms[0].currentPage.value = '${currentPage}';
	     }
	// Sets the ddscrolltable value
	document.getElementById('ddscrolltable').innerHTML='';
	// Sets the action method name for performing filter sort
	document.forms[0].method.value='showDetails';
	document.forms[0].entityCode.value=entityId;
	// Start code modified by Vivekanandan A for mantis 1993 on 26-10-2012
	<s:if test='"movementLog" == #request.methodName' >
		document.forms[0].movementsource.value='S';
		document.forms[0].method.value='showMovementDetails';
		document.forms[0].movementId.value="${requestScope.selectedMovementId}";
		document.forms[0].selectedMovementId.value="${requestScope.selectedMovementId}";
	</s:if>
	// End code modified by Vivekanandan A for mantis 1993 on 26-10-2012
	// Submits the form
	document.forms[0].submit();
  }


function submitFormFromUserBox(methodName){
	dateSelected = false;
	if(validateForm(document.forms[0]) ){
		if(validateField(document.forms[0].elements['auditLog.fromDateAsString'],'auditLog.from',dateFormat)){
			if(validateField(document.forms[0].elements['auditLog.toDateAsString'],'auditLog.to',dateFormat)){
	document.forms[0].method.value = methodName;

	<s:if test='"mLog" != #request.mLog' >
	document.forms[0].selectedDropDownUserId.value = document.forms[0].elements["auditLog.dropDownUserId"].value;
	enableFields();
	</s:if>
	document.forms[0].submitFromUser.value = "true";
	document.forms[0].fromDate.value = document.forms[0].elements['auditLog.fromDateAsString'].value;
    document.forms[0].toDate.value = document.forms[0].elements['auditLog.toDateAsString'].value;
	document.forms[0].submit();
	} else {
		return false;
	}
	} else {
		return false;
	}
	}
}
/**
 * This function used to submit the form while exporting screen
 * @param methodName
 * @param fileType
 */
function submitForm(methodName,fileType) {
	dateSelected = false;
	//validating form
  if(validateForm(document.forms[0])){
  	//validating date field and submiting the form
	if(validateField(document.forms[0].elements['auditLog.fromDateAsString'],'auditLog.from',dateFormat)){
		if(validateField(document.forms[0].elements['auditLog.toDateAsString'],'auditLog.to',dateFormat)){
			document.forms[0].method.value=methodName;
			document.forms[0].entityCode.value = entityId;
			document.forms[0].exportType.value = fileType.trim();
			document.forms[0].movId.value = movId;
			// Start code modified by Vivekanandan A for mantis 1993 on 26-10-2012
			document.forms[0].totalCount.value = '${requestScope.totalCount}';
			document.forms[0].currentPage.value = '${currentPage}';
			// End code modified by Vivekanandan A for mantis 1993 on 26-10-2012
			document.forms[0].submit();	
			}
		}
	}	
}

</SCRIPT>

 <SCRIPT language="JAVASCRIPT">
  var dateFormatValue = '<s:property value="#request.session.CDM.dateFormatValue" />';

      var cal = new CalendarPopup("caldiv",true); 

      cal.offsetX = -82;
      cal.offsetY = 22;
	  var cal2 = new CalendarPopup("caldiv",true); 

      cal2.offsetX = -82;
      cal2.offsetY = 22;

 </SCRIPT>


</head>



<!--For closing the child screen when parant screen closed-->
<body onmousemove="reportMove()" leftmargin="0" topmargin="0" marginheight="0" onLoad="setParentChildsFocus(document.forms[0]);setTitleSuffix(document.forms[0]);bodyOnLoad();" onunload="call()">

<s:form action="auditlog.do" onsubmit="return validate(this);"> 

<input name="method" type="hidden" value="display">
<input name="exportType" type="hidden" value="">
<input name="movId" type="hidden" value="">
<input name="screen" type="hidden" value="MovementAuditLog-Smart Predict">
<input name="selectedReferenceId" type="hidden" value="viewDetails">
<input name="selectedReference" type="hidden" value="viewDetails">
<input name="selectedDate" type="hidden" value="display">
<input name="selectedTime" type="hidden" value="display">
<input name="selectedUserId" type="hidden" value="viewDetails">
<input name="selectedAction" type="hidden" value="display">
<input name="selectedMovementId" type="hidden" value="">
<input name="logList" type="hidden" value="">
<input name="archiveId" type="hidden" value="">
<input name="currentPage" type="hidden" value="">
<input name="selectedDropDownUserId" type="hidden" value="display">
<input name="selectedFilter" type="hidden" value='${selectedFilter}'>
<input name="selectedSort" type="hidden" value='${selectedSort}'>
<input name="submitFromUser" type="hidden" value="">
<input name="totalCount" type="hidden" value="">
<input name="movementsource" type="hidden" value="">
<input name="movementId" type="hidden" value="">
<input name="fromDate" type="hidden" value="">
<input name="toDate" type="hidden" value="">
<input name="preUserFromDateAsString" type="hidden" value="">
<input name="preUserToDateAsString" type="hidden" value="">
<input name="entityCode" type="hidden" value="">
<input name="goToPageNo" type="hidden" value="">
<input name="maxPages" type="hidden" value="">

<s:set var="CDM" value="#request.session.CDM" />
<s:if test='"mLog" != #request.mLog' >
<div id="UserLog" style="position:absolute; left:20px; top:20px; width:634px; height:64px; border:2px outset;" color="#7E97AF">
<div id="UserLog" style="position:absolute; left:8px; top:4px; width:390; height:15px; visibility:visible;">
<table width="400px" border="0" cellpadding="0" cellspacing="0" height="54">
<tr height="24">
			  <td width="35px" style="padding-top: 3px"><b><s:text name="auditLog.userId"/></b></td>
			  <td width="28px">&nbsp;</td>
			  <td width="144px">
			  <s:select id="auditLog.dropDownUserId" name="auditLog.dropDownUserId" titleKey="tooltip.selectUserId"  cssStyle="width:144px" tabindex="1" styleClass="htmlTextAlpha" onchange="javascript:submitFormFromUserBox('defaultDetails')" list="#request.users" listKey="value" listValue="label" />
			  </td>
			   <td width="15px">&nbsp;</td>
			  <td width="180px" align="left">
			   <spam class="textAlpha" style="background:transparent; border:thin;width:188px;" readonly name="userName" size="20"></td>
			   </td>
			 </tr>
<tr height="24">
		  <td width="35px" align = "left"><b><s:text name="auditLog.from"/></b>*</td>
		  <td width="26px">&nbsp;</td>
		  <td width="127px" align = "left" id="auditLogFromDate">	
			<s:textfield titleKey="tooltip.fromDate" tabindex="1" cssClass="htmlTextAlpha"  name="auditLog.fromDateAsString" readonly="false" maxlength="10" style="width:80px; height:20px;" onkeydown="onFromDateKeyPress(this,event)" onblur="onFromDateChange();" onmouseout="dateSelected=false;"/>
		 <A  title='<s:text name="tooltip.selectFromDate"/>' tabindex="2" name="datelink" ID="datelink" onClick="cal.select(document.forms[0].elements['auditLog.fromDateAsString'],'datelink',dateFormatValue);storeFromDate();dateSelected = true; return false;"><img src="images/calendar-16.gif" style="margin-bottom: -5px;"/></A></td>
		 <td width="15px">
		  <td width="180px" id="auditLogToDate"><b><s:text name="auditLog.to"/></b>*&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
		  	<s:textfield titleKey="tooltip.toDate" tabindex="3" cssClass="htmlTextAlpha"  name="auditLog.toDateAsString" readonly="false" maxlength="10" style="width:80px; height:20px;" onkeydown="onToDateKeyPress(this,event)" onblur="onToDateChange();" onmouseout="dateSelected=false;"/>
		  <A  title='<s:text name="tooltip.selectToDate"/>' tabindex="4" name="datelink2" ID="datelink2" onClick="cal2.select(document.forms[0].elements['auditLog.toDateAsString'],'datelink2',dateFormatValue);storeToDate();dateSelected =true; return false;" ><img src="images/calendar-16.gif" style="margin-bottom: -5px;"/></A></td>
		  <td>
        </tr>
</table>
</div>


<div  id="pageSummaryList" style="position:absolute; left:410px; top:0px; height:25px;border:2px;">
		<s:if test='"true" != #request.hidePagination' >
		<table border="0" cellpadding="0" cellspacing="1" height="25px">			
		  <tr height="25px">
		  <%String currentPageAsString = (String)request.getAttribute("currentPage");%>
		  <%String maxPageAsString = (String)request.getAttribute("maxPage");%>
		  <%int countPageNo = 1;%>
		  <s:iterator value="#request.pageSummaryList" var="pageSummaryList" >			  		  
			<% if( countPageNo <=12) {++countPageNo; %>
			<td height="34" style="padding-bottom: 2px"><b>Page</b>&nbsp;&nbsp;
	  		<input class="htmlTextNumeric" id="pageNoText" name="pageNo" size="5"  style="height: 21px" align="top" value="<%=currentPageAsString %>" onkeydown="if (event.keyCode == 9 || event.keyCode == 13)validatePageNumber(this);">
	  		</td>
	  		<td>
		  		<s:if test='"true" == #request.nextEnabled' >
    			<a href="#"  onclick="clickLink(-1);">
				    <img alt="Next page" src="images/page_up.png" align="top" border="0" height="12"style="padding-top:6px;width="18"></img><br />
			    </a>
			    </s:if>
			    
			    <s:if test='"true" != #request.nextEnabled' >
			    	<img alt="Next page" src="images/page_up.png" align="top" border="0" width="18" style="padding-top:6px;height: 11px;"></img><br />
			    </s:if>
	  			
	  			<s:if test='"true" == #request.prevEnabled' >
	  			<a href="#"  onclick="clickLink(-2);">
				    <img alt="Previous page" src="images/page_down.png" align="bottom" height="12" width="18" style="padding-bottom:6px;" border="0"></img><br />
			    </a>				  
			    </s:if>
			    <s:if test='"true" != #request.prevEnabled' >
			    	<img alt="Previous page" src="images/page_down.png" align="bottom" width="18" border="0" style="padding-bottom:6px;height: 11px;"></img><br />
			    </s:if>
	  		</td>
	  		<td style="text-align: center;"><s:text name="genericDisplayMonitor.labelOf"/>&nbsp;&nbsp;
	    		<input class="textAlpha" style="background:transparent;border: 0; height: 17;" readonly name="maxPageNo" value="<%=maxPageAsString %>" size="5">
	  		</td>
			<%}%>	
		</s:iterator>		
		  </tr>
		</table>
		</s:if>
	</div>



</div>

</s:if>

<s:if test='"mLog" == #request.mLog' >
<div id="UserLog" style="position:absolute; left:20px; top:10px; width:1182px; height:37px; border:2px outset;" color="#7E97AF">
<div id="UserLog" style="position:absolute; left:8px; top:4px; width:1165px; height:15px; visibility:visible;">

<table width="410px" border="0" cellpadding="0" cellspacing="0" height="24">
<tr height="24">

 <td width="40px" align = "left"><b style="font-size: 12px"><s:text name="auditLog.from"/></b>*	</td>
		  <td width="26px">&nbsp;</td>
		  <td width="147px" align = "left" id="userLogFromDate">	
			<s:textfield titleKey="tooltip.fromDate" cssClass="htmlTextAlpha"  tabindex="1" name="auditLog.fromDateAsString" style="width:80px;height:20px" readonly="false" maxlength="10" onkeydown="onFromDateKeyPress(this,event)" onblur="onFromDateChange();" onmouseout="dateSelected=false;"/>
		 <A  title='<s:text name="tooltip.selectFromDate"/>' tabindex="2" name="datelink" ID="datelink" onClick="cal.select(document.forms[0].elements['auditLog.fromDateAsString'],'datelink',dateFormatValue);storeFromDate();dateSelected = true; return false;"><img src="images/calendar-16.gif" style="margin-bottom: -5px;"/></A></td>

		  <td width="280px" id="userLogToDate"><b style="font-size: 12px""><s:text name="auditLog.to"/></b>*
			<s:textfield titleKey="tooltip.toDate" tabindex="3" cssClass="htmlTextAlpha"  name="auditLog.toDateAsString" style="width:80px;height:20px" readonly="false" maxlength="10" onkeydown="onToDateKeyPress(this,event)" onblur="onToDateChange();" onmouseout="dateSelected=false;"/>
			<A  title='<s:text name="tooltip.selectToDate"/>' tabindex="4" name="datelink2" ID="datelink2" onClick="cal2.select(document.forms[0].elements['auditLog.toDateAsString'],'datelink2',dateFormatValue);storeToDate();dateSelected = true; return false;" ><img src="images/calendar-16.gif" style="margin-bottom: -5px;"/></A></td>
		  <td>
        </tr>
</table>
</div>

<%-- Start Code:Modified by Naseema.Sd for Mantis 1993 --%>
<div  id="pageSummaryListMLog" style="position:absolute; left:800px; top:0px; width:110px; height:25px;border:2px;">
		<s:if test='"true" != #request.hidePagination' >
		<table width="100px" border="0" cellpadding="0" cellspacing="1" height="25px">			
		  <tr height="25px">
		  <%String currentPageAsString1 = (String)request.getAttribute("currentPage");%>
		  <%String maxPageAsString1= (String)request.getAttribute("maxPage");%>
		  <%int countPageNo1 = 1;%>
		  <s:iterator value="#request.pageSummaryList" var="pageSummaryList" >			  		  
			<% if( countPageNo1 <=12) {++countPageNo1; %>
			<td height="34"><b>Page</b>&nbsp;&nbsp;
	  		<input class="htmlTextNumeric" title='<s:text name="tooltip.enterPageNo"/>'id="pageNoText" name="pageNo"  style="height: 22px" maxlength="5" size="5" align="top" value="<%=currentPageAsString1 %>" onkeydown="if (event.keyCode == 9 || event.keyCode == 13)validatePageNumber(this);">
	  		</td>
	  		<td>
		  		<s:if test='"true" == #request.nextEnabled' >
		  		<a href="#"  onclick="clickLink(-1);">
				    <img alt="Next page" src="images/page_up.png" align="top" border="0" height="12" width="18"></img><br />
			    </a>
			    </s:if>
			    
			    <s:if test='"true" != #request.nextEnabled' >
			    	<img alt="Next page" src="images/page_up.png" align="top" border="0" height="12" width="18" style=" height: 11px;"></img><br />
			    </s:if>
	  			
	  			<s:if test='"true" == #request.prevEnabled' >
	  			<a href="#"  onclick="clickLink(-2);">
				    <img alt="Previous page" src="images/page_down.png" align="bottom" height="12" width="18" border="0"></img><br />
			    </a>	
			    </s:if>
			    <s:if test='"true" != #request.prevEnabled' >
			    	<img alt="Previous page" src="images/page_down.png" align="bottom" height="12" width="18" border="0"  style=" height: 11px;"></img><br />
			    </s:if>
	  		</td>
	  		<td style="text-align: center;">&nbsp;&nbsp;of&nbsp;&nbsp;
	    		<input class="textAlpha" style="background:transparent;border: 0;" readonly name="maxPageNo" value="<%=maxPageAsString1 %>" size="5">
	  		</td>
			<%}%>	
		</s:iterator>		
		  </tr>
		</table>
		</s:if>
	</div>
 <%-- End Code:Modified by Naseema.Sd for Mantis 1993 --%>
	
	<s:if test='"movementLog" == #request.methodName' >
	<table height="36"><tr>
		<td id="movIdLabel" width="1050px" align="right" >
		<b style="font-size: 12px"><s:text name="manualInput.id.movementId"/>:</b>
		</td>			
		<td id="mvmtId" >
		<input class="textAlpha" style="background:transparent;border: 0;" tabindex="-1">				
		</td>
		</tr>
	</table>
	</s:if>
</div>
</s:if>


<DIV ID="caldiv" STYLE="z-index:99;position:absolute;visibility:visible;background-color:white;layer-background-color:white;"></DIV>
  
 <s:if test='"movementLog" != #request.methodName' >
<div id="AuditLog" color="#7E97AF" style="position:absolute; border-left:2px outset; left:20px; top:89px; width:633px; height:430;">
<div id="AuditLog" style="position:absolute;z-index:99;left:0px; top:0px; width:614px; height:10px;">
<table  class="sort-table" id="table_2" bgcolor="#B0AFAF" width="605" border="0" cellspacing="1" cellpadding="0"  >

</s:if>



<s:if test='"movementLog" == #request.methodName' >
<div id="AuditLog" color="#7E97AF" style="position:absolute; border-left:2px outset; left:20px; top:61px; width:1182px; height:430;">
<div id="AuditLog" style="position:absolute;z-index:99;left:0px; top:0px; width:1200px; height:10px;">

<table  class="sort-table" id="table_2" bgcolor="#B0AFAF" width="1164" border="0" cellspacing="1" cellpadding="0"  >

</s:if>
	<thead>
		<tr height="23px">
			<td title='<s:text name="tooltip.sortLogDate"/>' width="85px" style="border-left-width: 0px;" align="center"><b><s:text name="auditLog.logDate_Date"/></b></td>
			<td title='<s:text name="tooltip.sortLogTime"/>' width="80px"  align="center"><b><s:text name="auditLog.logDate_Time"/></b></td>
			
			<td title='<s:text name="tooltip.sortUserId"/>' width="135px" align="center"><b><s:text name="auditLog.userId"/></b></td>
			<s:if test='"movementLog" != #request.methodName' >
			<td title='<s:text name="tooltip.sortItem"/>' width="78px"  align="center"><b><s:text name="auditLog.id.reference"/></b></td>
			<td title='<s:text name="tooltip.sortItemNum"/>' width="140px"  align="center"><b><s:text name="auditLog.id.referenceId"/></b></td>
			</s:if>
			
			<td title='<s:text name="tooltip.sortAction"/>' width="85px"  align="center"><b><s:text name="auditLog.id.action"/></td>

			<s:if test='"movementLog" == #request.methodName' >
			<td  title='<s:text name="tooltip.sortField"/>' width="190px"  align="center"><b><s:text name="maintenanceLog.columnName"/></b></td>
			<td title='<s:text name="tooltip.sortOldValue"/>' width="280px"  align="center"><b><s:text name="maintenanceLog.oldValue"/></b></td>
			<td title='<s:text name="tooltip.sortNewValue"/>' width="280px"  align="center"><b><s:text name="maintenanceLog.newValue"/></b></td>		
		</s:if>
		</tr>
	</thead>
</table>
</div>


<s:if test='"movementLog" != #request.methodName' >
<div id="ddscrolltable"  style="position:absolute; left:0px; top:1px; width:628px; height:425;overflowY:scroll">
<div id="AuditLog" style="position:absolute;z-index:99;left:1px; top:22px; width:611px; height:10px;">
<table class="sort-table" id="auditLogList" width="610" border="0" cellspacing="1" cellpadding="0" height="403">
</s:if>

<s:if test='"movementLog" == #request.methodName' >

<div id="ddscrolltable"  style="position:absolute; left:0px; top:0px; width:1181px; height:426;overflowY:scroll">
<div id="AuditLog" style="position:absolute;z-index:99;left:1px; top:22px; width:1164px; height:10px;">

<table class="sort-table" id="auditLogList" width="1161" border="0" cellspacing="1" cellpadding="0" height="403">
</s:if>

	<tbody> 		
	<%int count = 0; %>  
	<s:iterator value="#request.auditLogList" var="auditLogList" >          
		<% if( count%2 == 0 ) {%><tr  class="even"><% }else  { %> <tr  class="odd"> <%}++count; %>

		<s:hidden name="#auditLogList.id.reference" disabled="true"/>
		<s:hidden name="#auditLogList.id.referenceId" disabled="true"/>
		<s:hidden name="#auditLogList.logDate_Date" disabled="true"/>
		<s:hidden name="#auditLogList.logDate_Time" disabled="true"/>
			<s:hidden name="#auditLogList.id.userId" disabled="true"/>
			<s:hidden name="#auditLogList.id.action" disabled="true"/>

			<s:if test='"movementLog" == #request.methodName' >
			<s:hidden name="#auditLogList.id.columnName" disabled="true"/>
			<s:hidden name="#auditLogList.id.oldValue" disabled="true"/>
			<s:hidden name="#auditLogList.id.newValue" disabled="true"/>
			</s:if>
			<s:set var="auditLogList" value="#auditLogList"/>
			<jsp:useBean id="auditLogList" class="org.swallow.control.model.AuditLog" />			
			<% AuditLog auditLog = (AuditLog)auditLogList; %>
			<td width="85px" align="left" >
			<s:property value="#auditLogList.logDate_Date" />&nbsp;</td>
			<td width="80px" align="left"><s:property value="#auditLogList.logDate_Time" />&nbsp;</td>
			<td width="135px" align="left"><s:property value="#auditLogList.id.userId" />&nbsp;</td>
			<s:if test='"movementLog" != #request.methodName' >
			<td width="78px" align="left"><s:property value="#auditLogList.id.reference" />&nbsp;</td>
			<td width="140px" align="right"><s:property value="#auditLogList.id.referenceId" />&nbsp;</td>
			</s:if>
			<td width="85px" align="left"><s:property value="#auditLogList.id.action" />&nbsp;</td>
				<s:if test='"movementLog" == #request.methodName' >
			<td  width="190px" align="left" >
			<s:property value="#auditLogList.id.columnName" />&nbsp;</td>
		      <td  width="280px"><%= auditLog.getId().getOldValue() != null ?  auditLog.getId().getOldValue().replaceAll(" ","&nbsp;")
			    : "" %>&nbsp;</td>
			    
			<td  width="280px"><%= auditLog.getId().getNewValue() != null ?  auditLog.getId().getNewValue().replaceAll(" ","&nbsp;")
			    : "" %>&nbsp;</td>
			 		
			
	</s:if>	
		</tr>
</s:iterator>  
	</tbody>
	<s:if test='"movementLog" != #request.methodName' >
	<tfoot><tr><td colspan="6" ></td></tr></tfoot>
	</s:if>
	<s:if test='"movementLog" == #request.methodName' >
	<tfoot><tr><td colspan="7" ></td></tr></tfoot>
	</s:if>
</table>
</div>
</div>
 </div>

 <s:if test='"movementLog" != #request.methodName' >
<div id="UserAuditLog" style="position:absolute; left:570; top:532px; width:70px; height:39px; visibility:visible;">
	<table width="60" border="0" cellspacing="0" cellpadding="0" height="20">
</s:if>
 <s:if test='"movementLog" == #request.methodName' >

 <div id="UserAuditLog" style="position:absolute; left:1100; top:505px; width:70px; height:39px;z-index:5; visibility:visible;">
	<table >
		 
		</s:if>
  <div id="exportReport"/>
	</table>
</div>
		  
		<s:if test='"movementLog" == #request.methodName' >	
		<div style="position:absolute; left:1150; top:505px; width:30px; height:30px; visibility:visible;z-index:5;">	
				<a  title= '<s:text name="tooltip.helpScreen"/>' tabindex="8" href=# onclick="javascript:openWindow(buildPrintURL('print','Audit  Log'),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')" onMouseOut="MM_swapImgRestore()" onMouseOver="MM_swapImage('Help','','images/help_default.GIF ',1)"><img src="images/help_default.GIF " name="Help"  border="0" ></a> 
	</div>
		</s:if>
		<s:if test='"movementLog" != #request.methodName' >
			<div style="position:absolute; left:570; top:535px; width:70px; height:30px; visibility:visible;">
				<a style="float:left;margin-right: 10px;" title= '<s:text name="tooltip.helpScreen"/>' tabindex="8" href=# onclick="javascript:openWindow(buildPrintURL('print','User Log'),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')" onMouseOut="MM_swapImgRestore()" onMouseOver="MM_swapImage('Help','','images/help_default.GIF ',1)"><img src="images/help_default.GIF " name="Help"  border="0" ></a> 
			 
		 </s:if>
		
		<s:if test='"mLog" != #request.mLog' >
		
	
				<a style="float:left"tabindex="8" onclick="printPage();" onMouseOut="MM_swapImgRestore()" onMouseOver="MM_swapImage('Print','','images/Print_R.gif ',1)"><img src="images/Print.gif " name="Print"  border="0" title='<s:text name="tooltip.printScreen"/>' ></a>	
			</div>
		</s:if>




<s:if test='"movementLog" != #request.methodName' >
 <div id="ddimagebuttons" color="#7E97AF" style="position:absolute; border:2px outset; left:20px; top:525px; width:633px; height:39px; visibility:visible;">
   <div id="AuditLog" style="position:absolute; left:6; top:4; width:625; height:15px; visibility:visible;">
  	 <table width="210" border="0" cellspacing="0" cellpadding="0" height="20">
 </s:if>

<s:if test='"movementLog" == #request.methodName' >
 <div id="ddimagebuttons" color="#7E97AF" style="position:absolute; border:2px outset; left:20px; top:495px; width:1182px; height:39px; visibility:visible;">
   <div id="AuditLog" style="position:absolute; left:6; top:4; width:200px; height:15px; visibility:visible;">

  	 <table width="210" border="0" cellspacing="0" cellpadding="0" height="20">
 </s:if>

		<tr>
		<td id="refreshbutton"> 
		<s:if test='"movementLog" != #request.methodName' >
			<a  title='<s:text name="tooltip.refreshUserLog"/>' tabindex="5" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onClick="if(validateForm(document.forms[0]) ){refreshWindow();}"><s:text name="button.Refresh"/></a>
            </s:if>
			<s:if test='"movementLog" == #request.methodName' >
			<a  title='<s:text name="tooltip.refreshScreen"/>' tabindex="5" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onClick="if(validateForm(document.forms[0]) ){refreshWindow();}"><s:text name="button.Refresh"/></a>
            </s:if>
			</td>

			<s:if test='"movementLog" != #request.methodName' >
			<td id="viewbutton">		
			</td>
			 </s:if>
			<s:if test='"movementLog" == #request.methodName' >
			<td  style="position:absolute; left:70; top:1; id="closebutton">
			<a  title='<s:text name="tooltip.close"/>' tabindex="7" tabindex="4" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onclick="setParentChildsFocus();confirmClose('P');" style="margin-left: 10px;"><s:text name="button.close"/></a>			
			</td>
			</s:if>
			<s:if test='"movementLog" != #request.methodName' >
			<td id="closebutton">
			<a  title='<s:text name="tooltip.close"/>'style=" margin-left: 10px;"tabindex="7" tabindex="4" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onclick="setParentChildsFocus();confirmClose('P');"><s:text name="button.close"/></a>			
			</td>
			</s:if>
		</tr>
		</table>
	</div>
	<s:if test='"movementLog" != #request.methodName' >	
	<table height="33"><tr>
		<td id="lastRefTimeLable" width="430px" align="right" >
		<s:text name="label.lastRefTime"/>
		</td>			
		<td id="lastRefTime" >
		<input class="textAlpha" style="background:transparent;border: 0;" tabindex="-1" readonly name="maxPageNo" value="" size="14">				
		</td>
		</tr>
	</table>
	</s:if>	
  <div  style="position:absolute; left:6; top:4; width:410; height:15px; visibility:hidden;">
  	 <table width="210" border="0" cellspacing="0" cellpadding="0" height="20">
		<tr>
		<td id="refreshdisablebutton">
			<a  class="disabled"  disabled="disabled"><s:text name="button.Refresh"/></a>
		</td>
		<td id="viewenablebutton">
			<a  title='<s:text name="tooltip.viewLogDetails"/>' tabindex="6" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onClick="if(validateForm(document.forms[0]) ){buildReferenceIdURL('viewDetails');}"><s:text name="button.view"/></a>
			</td>
			<td id="viewdisablebutton">
			<a  class="disabled" disabled="disabled"><s:text name="button.view"/></a>
		</td>
		</tr>
		</table>
	</div>
 </div>

<blockquote>&nbsp;</blockquote>
</s:form>
</body>
</html>