<?xml version="1.0" encoding="UTF-8"?>
<!--
- The main purpose of this JSP file is to load the resultant XML data for Interface Monitor screen.
- Interface Monitor screen consists of two data grids (i) Top Grid and (ii) Bottom Grid
- The top grid shows the List of Interfaces, their enabled status, Total messages:Processed, Awaiting, Filtered and Bad.
- The bottom grid shows the detailed information of the selected Interface.
-
- Author(s): Marshal .I
- Date: 20-June-2011
-->

<%@ page contentType="text/xml"%>
<%@ taglib prefix="s" uri="/struts-tags" %>

<%@ page import="org.swallow.util.SwtConstants"%>
<%@ page import="java.util.HashMap" %>

<s:set var="FIELD_INTERFACEID" value="#request.flexFieldMapping.mapping.interfaceId" />
<!-- Start: Modified for Mantis 1676[Interface Monitor: Simplified to make more user friendly] by Marshal on 22-Dec-2011 -->
<s:set var="FIELD_ENABLED" value="#request.flexFieldMapping.mapping.enabled" />
<s:set var="FIELD_ENGINESTATUS" value="#request.flexFieldMapping.mapping.enginestatus" />
<!-- End: Modified for Mantis 1676[Interface Monitor: Simplified to make more user friendly] by Marshal on 22-Dec-2011 -->
<s:set var="FIELD_LASTMESSAGE" value="#request.flexFieldMapping.mapping.lastMessage" />
<s:set var="FIELD_TOTAL" value="#request.flexFieldMapping.mapping.total" />
<s:set var="FIELD_PROCESSED" value="#request.flexFieldMapping.mapping.processed" />
<s:set var="FIELD_AWAITING" value="#request.flexFieldMapping.mapping.awaiting" />
<s:set var="FIELD_FILTERED" value="#request.flexFieldMapping.mapping.filtered" />
<s:set var="FIELD_BAD" value="#request.flexFieldMapping.mapping.bad" />

<jsp:useBean id="FIELD_INTERFACEID" type="java.lang.String" />
<jsp:useBean id="FIELD_ENABLED" type="java.lang.String" />
<jsp:useBean id="FIELD_ENGINESTATUS" type="java.lang.String" />
<jsp:useBean id="FIELD_LASTMESSAGE" type="java.lang.String" />
<jsp:useBean id="FIELD_TOTAL" type="java.lang.String" />
<jsp:useBean id="FIELD_PROCESSED" type="java.lang.String" />
<jsp:useBean id="FIELD_AWAITING" type="java.lang.String" />
<jsp:useBean id="FIELD_FILTERED" type="java.lang.String" />
<jsp:useBean id="FIELD_BAD" type="java.lang.String" />









<s:set var="recordCount" value="#request.result.size()"/>
<!-- Added for Mantis 1928 by KaisBS: Interface Monitor: Add 'Show XML - Internal' menu option on context menu -->
<s:if test='"true" != #request.hideForNotInternal' >
	<interfacemonitor refresh="<s:property value="#request.refresh_period" />"
	lastRefTime="<s:property value="#request.lastRefTime" />"
	from="<s:property value="#request.fromDate" />" to="<s:property value="#request.fromDate" />"
	dateformat="<s:property value="#request.session.CDM.dateFormatValue" />"
	dateComparing="<s:if test='"Y" == #request.dateComparing' >true</s:if><s:if test='"Y" != #request.dateComparing' >false</s:if>"
	sysDateFrmSession="<s:property value="#request.sysDateFrmSession" />"
	sessionToDate="<s:property value="#request.sessionToDate" />">
	<request_reply>
		<status_ok><s:property value="#request.reply_status_ok" /></status_ok>
		<message><s:property value="#request.reply_message" /></message>
		<location />
		<timing>
			<s:iterator value="#request.opTimes" var="opTime">
				<operation id="${opTime.key}">${opTime.value}</operation>
			</s:iterator>
		</timing>
	</request_reply>
	<!-- Main Grid data goes here -->
	<grid>
	<metadata>
	<columns> <!-- columns is an ordered collection -->
	<s:iterator value="#request.column_order" var="order">
	<s:if test='#order == #FIELD_INTERFACEID' >
	<column
			heading="<s:text name="label.interfaceMonitor.header.interfaceId"/>"
			tooltip = "<s:text name="tooltip.interfaceMonitor.interfaceId"/>"
			draggable="false" filterable="true" type="str" sort="true"
			dataelement="<%=FIELD_INTERFACEID%>"
			width="<%=((HashMap)request.getAttribute("column_width")).get(FIELD_INTERFACEID)%>"
			 />
</s:if>
<!-- Start: Modified for Mantis 1676[Interface Monitor: Simplified to make more user friendly] by Marshal on 22-Dec-2011 -->
	<s:if test='#order == #FIELD_ENABLED' >
	<column
			heading="<s:text name="label.interfaceMonitor.header.activeInterface"/>"
			tooltip = "<s:text name="tooltip.interfaceMonitor.active"/>"
			draggable="true" filterable="true" type="bool" sort="true"
			dataelement="<%=FIELD_ENABLED%>"
			width="<%=((HashMap)request.getAttribute("column_width")).get(FIELD_ENABLED)%>"
	/>
</s:if>
	<s:if test='#order == #FIELD_ENGINESTATUS' >
	<column
			heading="<s:text name="label.interfaceMonitor.header.interfaceStatus"/>"
			tooltip = "<s:text name="tooltip.interfaceMonitor.status"/>"
			draggable="true" filterable="true" type="str" sort="true"
			dataelement="<%=FIELD_ENGINESTATUS%>"
			width="<%=((HashMap)request.getAttribute("column_width")).get(FIELD_ENGINESTATUS)%>"
	/>
</s:if>
<!-- End: Modified for Mantis 1676[Interface Monitor: Simplified to make more user friendly] by Marshal on 22-Dec-2011 -->
	<s:if test='#order == #FIELD_LASTMESSAGE' >
	<column
			heading="<s:text name="label.interfaceMonitor.header.lastMessageRecieved"/>"
			tooltip = "<s:text name="tooltip.interfaceMonitor.lastMessage"/>"
			draggable="true" filterable="true" type="num" sort="true"
			dataelement="<%=FIELD_LASTMESSAGE%>"
			width="<%=((HashMap)request.getAttribute("column_width")).get(FIELD_LASTMESSAGE)%>"
	/>
</s:if>
	<s:if test='#order == #FIELD_TOTAL' >
	<column
			heading="<s:text name="label.interfaceMonitor.header.totalMessages"/>"
			tooltip = "<s:text name="tooltip.interfaceMonitor.total"/>"
			draggable="true" filterable="true" type="num" sort="true"
			dataelement="<%=FIELD_TOTAL%>"
			width="<%=((HashMap)request.getAttribute("column_width")).get(FIELD_TOTAL)%>"
	/>
</s:if>
	<s:if test='#order == #FIELD_PROCESSED' >
	<column
			heading="<s:text name="label.interfaceMonitor.header.totalMessagesProcessed"/>"
			tooltip = "<s:text name="tooltip.interfaceMonitor.processed"/>"
			draggable="true" filterable="true" type="num" sort="true"
	dataelement="<%=FIELD_PROCESSED%>"
	width="<%=((HashMap)request.getAttribute("column_width")).get(FIELD_PROCESSED)%>"
	/>
</s:if>
	<s:if test='#order == #FIELD_AWAITING' >
	<column
			heading="<s:text name="label.interfaceMonitor.header.totalMessagesAwaiting"/>"
			tooltip = "<s:text name="tooltip.interfaceMonitor.awaiting"/>"
			draggable="true" filterable="true" type="num" sort="true"
			dataelement="<%=FIELD_AWAITING%>"
			width="<%=((HashMap)request.getAttribute("column_width")).get(FIELD_AWAITING)%>"
	/>
</s:if>
	<s:if test='#order == #FIELD_FILTERED' >
	<column
			heading="<s:text name="label.interfaceMonitor.header.totalFilteredMessages"/>"
			tooltip = "<s:text name="tooltip.interfaceMonitor.filtered"/>"
			draggable="true" filterable="true" type="num" sort="true"
			dataelement="<%=FIELD_FILTERED%>"
			width="<%=((HashMap)request.getAttribute("column_width")).get(FIELD_FILTERED)%>"
	/>
</s:if>
<s:if test='#order == #FIELD_BAD' >
	<column
			heading="<s:text name="label.interfaceMonitor.header.totalBadMessages"/>"
			tooltip = "<s:text name="tooltip.interfaceMonitor.bad"/>"
			draggable="true" filterable="true" type="num" sort="true"
			dataelement="<%=FIELD_BAD%>"
			width="<%=((HashMap)request.getAttribute("column_width")).get(FIELD_BAD)%>"
	/>
</s:if>
</s:iterator>
</columns>
</metadata>
<rows size="${recordCount}">
<s:iterator value="#request.result" var="bean" >

	<s:set var="value" value="#bean.value" />
	<jsp:useBean id="value" type="java.util.TreeMap" />
	<row>
	<<%=FIELD_INTERFACEID%> clickable="false">
		<s:property value="#bean.key" />
	</<%=FIELD_INTERFACEID%>>





	<!-- Start: Modified for Mantis 1676[Interface Monitor: Simplified to make more user friendly] by Marshal on 22-Dec-2011 -->
	<<%=FIELD_ENABLED%> clickable="false"> <% if(1== Integer.parseInt(String.valueOf(value.get(SwtConstants.INTERFACE_ACTIVE)).trim())) { %> ENABLED <% }else {%>DISABLED <%}%>
	</<%=FIELD_ENABLED%>>

	<<%=FIELD_ENGINESTATUS%> clickable="false">
		<%=value.get(SwtConstants.INTERFACE_STATUS)%>
	</<%=FIELD_ENGINESTATUS%>>
	<!-- End: Modified for Mantis 1676[Interface Monitor: Simplified to make more user friendly] by Marshal on 22-Dec-2011 -->

	<<%=FIELD_LASTMESSAGE%> clickable="false">
		<%=value.get(SwtConstants.INTERFACE_LAST_MESSAGE)%>
	</<%=FIELD_LASTMESSAGE%>>
		<<%=FIELD_TOTAL%> clickable="false">
	<% if(1 > Integer.parseInt(String.valueOf(value.get(SwtConstants.INTERFACE_TOTAL_COUNT)).trim())) {%>0<% }else {%><%=value.get(SwtConstants.INTERFACE_TOTAL_COUNT)%><%}%>
	</<%=FIELD_TOTAL%>>

		<<%=FIELD_PROCESSED%> clickable="false">

	<% if(1 > Integer.parseInt(String.valueOf(value.get(SwtConstants.INTERFACE_PROCESSED)).trim())) {%>0<% }else {%><%=value.get(SwtConstants.INTERFACE_PROCESSED)%><%}%>
	</<%=FIELD_PROCESSED%>>

		<<%=FIELD_AWAITING%> clickable="false">

	<% if(1 > Integer.parseInt(String.valueOf(value.get(SwtConstants.INTERFACE_AWAITING)).trim())) {%>0<% }else {%><%=value.get(SwtConstants.INTERFACE_AWAITING)%><%}%>
	</<%=FIELD_AWAITING%>>


	<<%=FIELD_FILTERED%> status="1" clickable="<% if(1 > Integer.parseInt(String.valueOf(value.get(SwtConstants.INTERFACE_FILTERED)).trim())) {%>false<% }else {%>true<%}%>">
		<% if(1 > Integer.parseInt(String.valueOf(value.get(SwtConstants.INTERFACE_FILTERED)).trim())) {%>0<% }else {%><%=value.get(SwtConstants.INTERFACE_FILTERED)%><%}%>
	</<%=FIELD_FILTERED%>>

	<<%=FIELD_BAD%> status="2" clickable="<% if(1 > Integer.parseInt(String.valueOf(value.get(SwtConstants.INTERFACE_BAD)).trim())) {%>false<% }else {%>true<%}%>">
			<% if(1 > Integer.parseInt(String.valueOf(value.get(SwtConstants.INTERFACE_BAD)).trim())) {%>0<% }else {%><%=value.get(SwtConstants.INTERFACE_BAD)%><%}%>
	</<%=FIELD_BAD%>>
	</row>
</s:iterator>
</rows>
	<totals />
</grid>
<!-- Start:  Added for Mantis 1928 by KaisBS: Interface Monitor: Add 'Show XML - Internal' menu option on context menu  -->
</s:if>
<s:if test='"true" == #request.internal' ><s:property value="#request.heartbeat" escapeHtml="false" /></s:if>
<s:if test='"true" != #request.hideForNotInternal' >
	<!-- End -->
	</interfacemonitor>
	<!-- Added for Mantis 1928 by KaisBS: Interface Monitor: Add 'Show XML - Internal' menu option on context menu  -->
</s:if>