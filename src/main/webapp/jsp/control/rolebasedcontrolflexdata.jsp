<?xml version="1.0" encoding="UTF-8"?>
<!--
  - The main purpose of this jsp file is to load the Role Based Control List screen.
  - 
  - Author(s): Bala .D
  - Date: 08-03-2011
  -->
  
<%@ page contentType="text/xml" %>
<%@ taglib prefix="s" uri="/struts-tags" %>

<s:set var="recordCount" value="#request.roleBasedControlList.size()"/>
<rolebasedcontrol>
	<request_reply>
		<status_ok><s:property value="#request.reply_status_ok" /></status_ok>
		<message><s:property value="#request.reply_message" /></message>
		<location />
	</request_reply>
	
	<singletons>
		<configExistFlag><s:property value="#request.configExistFlag" /></configExistFlag>
		<operation><s:property value="#request.operation" /></operation>
	</singletons>
		
		
	<grid>
		<metadata>
			<columns>
					<column 
						heading="<s:text name="label.roleBasedControl.facility"/>"
						draggable="false"
						sort="true"
						type="str"
						dataelement="facilityId"
						width="0"
						sortable = "true"
						visible= "false"
					/>
					
					<column 
						heading="<s:text name="label.roleBasedControl.facility"/>"
						draggable="false"
						sort="true"
						type="str"
						headerTooltip="<s:text name="roleBasedControl.column.facility.tooltip"/>"
						dataelement="facilityDesc"
						width="350"
						sortable = "true"
					/>

					<column 
						heading="<s:text name="label.roleBasedControl.ReqAuth"/>"
						draggable="false"
						type="bool"
						headerTooltip="<s:text name="roleBasedControl.column.reqAuth.tooltip"/>"
						dataelement="reqAuth"
						width="125"
					/>
					<column 
						heading="<s:text name="label.roleBasedControl.ReqOthers"/>"
						draggable="false"
						type="bool"
						dataelement="reqOthers"
						headerTooltip="<s:text name="roleBasedControl.column.authOther.tooltip"/>"
						width="125"
					/>
					
			</columns>
		</metadata>
		
		<rows size="${recordCount}">
		<s:iterator value="#request.roleBasedControlList" var="rowrecord"> 
			<row>
				<facilityId aggregate="<s:property value="#rowrecord.facilityId" />"><s:property value="#rowrecord.facilityId" /></facilityId>			
				<facilityDesc aggregate="<s:property value="#rowrecord.facilityDesc" />"><s:property value="#rowrecord.facilityDesc" /></facilityDesc>
				<reqAuth selected="<s:if test='"Y" == #rowrecord.reqAuth' >true</s:if><s:if test='"Y" != #request.rowrecord.reqAuth' >false</s:if>"><s:if test='"Y" == #rowrecord.reqAuth' >Y</s:if><s:if test='"Y" != #request.rowrecord.reqAuth' >N</s:if></reqAuth>
				<reqOthers selected="<s:if test='"Y" == #rowrecord.reqOthers' >true</s:if><s:if test='"Y" != #request.rowrecord.reqOthers' >false</s:if>"><s:if test='"Y" == #rowrecord.reqOthers' >Y</s:if><s:if test='"Y" != #request.rowrecord.reqOthers' >N</s:if></reqOthers>

			</row>
		</s:iterator>
		</rows>	
		
	</grid>
</rolebasedcontrol>