<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<!--
  - The main purpose of this JSP file is to configure Interface Exceptions screen.
  - 
  - Author(s): Marshal .I
  - Date: 20-June-2011
  -->
<%@ include file="/taglib.jsp"%>

<html lang="en">
<head>
<title><s:text name="label.interfaceExceptions.movement.message"/></title>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
<script type="text/javascript">
	/**
	* This function is used to open the java window.
	*
	* @param a
	* @param left
	* @param top
	* @param width
	* @param height
 	*/
	function openJavaWindow(a, left,top,width,height) {
		openWindow(a,'rolemaintenancechangeWindow','left='+left+',top='+top+',width='+width+',height='+height+',toolbar=0, resizable=yes, scrollbars=yes,status=yes');
	}
	
	/**
	* This function is used to refresh the parent screen.
	*/
	function refreshParent () {
		opener.refresh = true;	 
	}
</script>

<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<title></title>
<style>
body { margin: 0px; overflow:hidden }
#workFlowMonitorcontent {
		border: solid 0px #000;
		width: 100%;
		height: 100%;
		float: left;
		margin: 0px 0px;
	}
</style>
<script type="text/javascript" src="jsp/work/assets/swfobject.js"></script>
</head>
<body scroll="no" style="overflow:hidden" leftmargin="0" topmargin="0" marginheight="0" onload="setParentChildsFocus();setTitleSuffix(document.forms[0]);">
<html:form action="interfacemonitor.do">
	<input name="method" type="hidden" value="">
</html:form>
	<div id="swf"></div>

	<script type="text/javascript">
		// <![CDATA[
		var so = new SWFObject("jsp/work/plainmessage.swf?version=<%= SwtUtil.appVersion %>", "interfaceExceptionsSingleMessage", "100%", "100%", "9", "#D6E3FE");
		so.write("swf");
		// ]]>
	</script> 
</body>
</html>
