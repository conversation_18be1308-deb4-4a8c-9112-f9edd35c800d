<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@ include file="/taglib.jsp"  %>
<%@ page import="org.swallow.util.SwtConstants" %>
<html>
<head>
<s:if test='"sweepLimitSave" == #request.methodName' > 
	<title><s:text name="sweepinglimits.addScreen"/></title>
</s:if>

<s:if test='"update" == #request.methodName' > 
	<title><s:text name="sweepinglimits.changeScreen"/></title>
</s:if>

<s:if test='"view" == #request.methodName' > 
	<title><s:text name="sweepinglimits.viewScreen"/></title>
</s:if>

<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
<script type="text/javascript" src="js/datavalidation.js"></script>
<SCRIPT language="JAVASCRIPT">

var cancelcloseElements = new Array(1);
cancelcloseElements[0] = "cancelbutton";

var sweepLimitCurrName = '${requestScope.sweepLimitCurrName}';
<s:if test='"yes" == #request.parentFormRefresh' >
window.opener.document.forms[0].method.value="sweepLimit";
window.opener.document.forms[0].submit();
self.close();
</s:if>

/**
	 * submitForm
	 *
	 * This method is used to submit the form while perform button operation on ok button.
	 **/

function submitForm(methodName){

<!-- start: code modified by nageswararao for mantis 1597 on 20120306-->
var amount=validateCurrency(document.forms[0].elements['sweepLimits.sweepLimitAsStringAnother'],'sweepLimits.sweepLimitAsStringAnother',currencyFormat,document.forms[0].elements['sweepLimits.id.currencyCode'].value);
<!--End: code modified by nageswararao for mantis 1597 on 20120306-->

if(amount)
{


  if(validateForm(document.forms[0]) )
  {
  enableFields();
	document.forms[0].method.value = methodName;
	document.forms[0].submit();
  }
	
  }
  else
  {
     document.forms[0].elements['sweepLimits.sweepLimitAsStringAnother'].focus();
  }

}

      /**
 	  * onChangeCurrency
      * 			
      * This method is called while changing the currency code to the collection in list box.  
      */  
<!-- start: code modified by nageswararao for mantis 1597 on 20120306-->	  
	function onChangeCurrency() {

	if(document.forms[0].elements['sweepLimits.sweepLimitAsStringAnother'].value !=""){
		    var amount = validateCurrency(document.forms[0].elements['sweepLimits.sweepLimitAsStringAnother'], 'movement.amountAsString', currencyFormat,document.forms[0].elements['sweepLimits.id.currencyCode'].value);
		}	
		}
	<!--End: code modified by nageswararao for mantis 1597 on 20120306-->	  
function enableFields(){
	document.forms[0].elements["sweepLimits.id.currencyCode"].disabled = "";
}


function bodyOnLoad()
{
    <s:if test='"sweepLimitSave" == #request.methodName' > 
 		var currencydropBox = new SwSelectBox(document.forms[0].elements["sweepLimits.id.currencyCode"],document.getElementById("currencyName"));
	</s:if>

	 <s:if test='"sweepLimitSave" != #request.methodName' > 
 		document.getElementById("currencyName").innerText = '${requestScope.sweepLimitCurrName}'
	</s:if>

	<s:if test='"yes" == #request.sweepLimitExiting' >
	  alert("<s:text name="alert.recordAlreadyExists"/>");
    </s:if>
}

function validateForm(objForm){
  var elementsRef = new Array();
  elementsRef[0] = objForm.elements["sweepLimits.id.currencyCode"]; 
  elementsRef[1] = objForm.elements["sweepLimits.sweepLimitAsStringAnother"]; 

  return validate(elementsRef);
}

var currencyFormat = '<s:property value="#request.session.CDM.currencyFormat"/>';
</SCRIPT>

<body leftmargin="0" topmargin="0" marginheight="0" onLoad="bodyOnLoad();setParentChildsFocus();setFocus(document.forms[0]); ShowErrMsgWindow('${actionError}');" onunload="call()">
<s:form action="sweepLimits.do"  onsubmit="validate(this);">
<input name="method" type="hidden" value="display">
<input name="dummy" value="" style="visibility:hidden">

<div id="sweepLimitsAdd" style="position:absolute; left:20px; top:20px; width:559px; height:61px; border:2px outset;" color="#7E97AF">
	<div id="sweepLimitsAdd" style="position:absolute;z-index:99;left:8px; top:4px; width:430px; height:35px;">
		<table width="541px" border="0" cellpadding="0" cellspacing="0" height="50">
		  <tr>
				 <s:if test='"sweepLimitSave" == #request.methodName' >
						  <td width="93px"><b><s:text name="role.sweepLimits.currencyCode"/></b>*</td>
					</s:if>
					
          			 <s:if test='"update" == #request.methodName' >
						  <td width="93px"><b><s:text name="role.sweepLimits.currencyCode"/></b></td>
					</s:if>
					
          			 <s:if test='"view" == #request.methodName' >
						  <td width="93px"><b><s:text name="role.sweepLimits.currencyCode"/></b></td>
					</s:if>
			
			  <td width="28px">&nbsp;</td>
			  <td width="420px">
				<s:if test='"sweepLimitSave" == #request.methodName' > 
					<s:select tabindex="1" titleKey="tooltip.selectCurrencyId" id="sweepLimits.id.currencyCode" name="sweepLimits.id.currencyCode" onchange="onChangeCurrency()" disabled="%{#attr.screenFieldsStatus}" cssStyle="width:55px" list="#request.currencyMaster" listKey="value" listValue="label" />
					
				</s:if> 
					<s:if test='"update" == #request.methodName' > 
				 <s:textfield tabindex="1" name="sweepLimits.id.currencyCode"  titleKey="tooltip.selectCurrencyId" cssClass="htmlTextAlpha"  cssStyle="width:40px;" disabled="true" /> 
					
				</s:if>  
				<s:if test='"view" == #request.methodName' >
					 <s:textfield tabindex="1" name="sweepLimits.id.currencyCode"  titleKey="tooltip.selectCurrencyId" cssClass="htmlTextAlpha"  cssStyle="width:40px;" disabled="true" /> 
					
				</s:if>&nbsp;&nbsp;&nbsp;
					<span id="currencyName" name="currencyName" class="spantext">
			</td>
		</tr>
		<tr>
			 <s:if test='"sweepLimitSave" == #request.methodName' >
							<td width="93px"><b><s:text name="role.sweepLimits.sweepLimit"/></b>*</td>
					</s:if>
					
          			 <s:if test='"update" == #request.methodName' >
						 	<td width="93px"><b><s:text name="role.sweepLimits.sweepLimit"/></b>*</td>
					</s:if>
					
          			 <s:if test='"view" == #request.methodName' >
							<td width="93px"><b><s:text name="role.sweepLimits.sweepLimit"/></b></td>
					</s:if>
		
			<td width="28px">&nbsp;</td>
			<s:if test='"view" == #request.methodName' > 
		        	  
			<td width="420px">
				<s:textfield titleKey="tooltip.enterSwpLimit" name="sweepLimits.sweepLimitAsStringAnother"  cssClass="htmlTextNumeric" cssStyle="width:195px;" maxlength="28" onchange="return validateCurrency(this,'movement.amountAsString',currencyFormat, document.forms[0].elements['sweepLimits.id.currencyCode'].value)" disabled='%{#attr.screenFieldStatus}' />
			</td>
			</s:if>
			<s:if test='"view" != #request.methodName' >          	  
			<td width="420px">
				<s:textfield tabindex="2" titleKey="tooltip.enterSwpLimit" cssClass="htmlTextNumeric" name="sweepLimits.sweepLimitAsStringAnother" maxlength="28"   cssStyle="width:195px;" onchange="return validateCurrency(this,'movement.amountAsString',currencyFormat, document.forms[0].elements['sweepLimits.id.currencyCode'].value)" /> 
			</td>
			</s:if>	
				   
		</tr>
	</table>
</div>
</div>

<div id="sweepLimitsAdd" style="position:absolute; left:510; top:94px; width:70px; height:15px; visibility:visible;">
	<table width="60px" border="0" cellspacing="0" cellpadding="0" height="20">
		<tr>
     
		<td align="Right">
		<s:if test='"sweepLimitSave" == #request.methodName' >
				<a  tabindex="6" href=# onclick="javascript:openWindow(buildPrintURL('print','Add Sweeping Limits '),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')" onMouseOut="MM_swapImgRestore()" onMouseOver="MM_swapImage('Help','','images/help_default.GIF ',1)"><img src="images/help_default.GIF " name="Help" border="0" title='<s:text name="tooltip.helpScreen"/>'></a> 
		</s:if>
		<s:if test='"update" == #request.methodName' >
				<a  tabindex="6" href=# onclick="javascript:openWindow(buildPrintURL('print','Change Sweeping Limits '),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')" onMouseOut="MM_swapImgRestore()" onMouseOver="MM_swapImage('Help','','images/help_default.GIF ',1)"><img src="images/help_default.GIF " name="Help" border="0" title='<s:text name="tooltip.helpScreen"/>'></a> 
		</s:if>
		<s:if test='"view" == #request.methodName' >
				<a  tabindex="6" href=# onclick="javascript:openWindow(buildPrintURL('print','View Sweeping Limits '),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')" onMouseOut="MM_swapImgRestore()" onMouseOver="MM_swapImage('Help','','images/help_default.GIF ',1)"><img src="images/help_default.GIF " name="Help" border="0" title='<s:text name="tooltip.helpScreen"/>'></a> 
		</s:if>
	
	    </td>

			<td align="right" id="Print">
				<a tabindex="6" onclick="printPage();" onMouseOut="MM_swapImgRestore()" onMouseOver="MM_swapImage('Print','','images/Print_R.gif ',1)"><img src="images/Print.gif " name="Print"  border="0" title='<s:text name="tooltip.printScreen"/>'></a>	
			</td>
		</tr>
	</table>
</div>
<div id="ddimagebuttons" color="#7E97AF" style="position:absolute; border:2px outset; left:20; top:85px; width:559px; height:39px; visibility:visible;">
<div id="sweepLimitsAdd" style="position:absolute; left:6; top:4; width:150px; height:15px; visibility:visible;">
  <table width="140px" border="0" cellspacing="0" cellpadding="0" height="20">
   <s:if test='"view" != #request.viewButtonStatus' >
	<tr>
	 <!-- Save Button  -->
		  <td width="70px">
		  <a title='<s:text name="tooltip.ok"/>' tabindex="3" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onClick="javascript:submitForm('${methodName}');"><s:text name="button.ok"/></a>
		  </td>

	 <!-- Cancel Button  -->
	
		  <td id="cancelbutton" width="70px">						 
			<a title='<s:text name="tooltip.cancel"/>' tabindex="4" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onclick="javascript:window.close();"><s:text name="button.cancel"/></a>
		  </td>
		</tr>

	</s:if>
	 <s:if test='"view" == #request.viewButtonStatus' >
	 <tr>
	 <!-- View Button  -->
	<td id="closebutton" width="70px">
		<a title='<s:text name="tooltip.close"/>' tabindex="5" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onclick="confirmClose('P');"><s:text name="button.close"/></a></td>
		</tr>
	 </s:if>

	</table>
  </div>
</div>
<blockquote>&nbsp;</blockquote>
		<p>&nbsp;</p>
<script type="text/javascript">
</script>
</s:form>
</body>
</html>