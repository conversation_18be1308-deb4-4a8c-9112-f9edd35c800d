<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@ include file="/taglib.jsp"  %>
<%@ page import="org.swallow.util.SwtConstants" %>
<%@ taglib uri="/struts-tags" prefix="s" %>

<html>
<head>
 <s:if test='"update" != #request.methodName' > 
<title><s:text name="myUserDetails.title.window"/></title>
</s:if>

<s:if test='"update" == #request.methodName' > 
<title><s:text name="changeUserDetails.title.window"/></title>
</s:if>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
<script type="text/javascript" src="js/jquery.exportselect.js"></script>

<SCRIPT language="JAVASCRIPT">

<s:if test='"updated" == #request.updated' >

self.close();
</s:if>

var cancelcloseElements = new Array(2);
cancelcloseElements[0] = "cancelbutton";
cancelcloseElements[1] = "closebutton";

var menuEntityCurrGrpAccess = "${requestScope.menuEntityCurrGrpAccess}";



function bodyOnLoad(){	 
	var roleDropBoxElement = new SwSelectBox(document.forms[0].elements["useroptions.roleId"],document.getElementById("roleName"));

	var entityDropBoxElement = new SwSelectBox(document.forms[0].elements["useroptions.currententity"],document.getElementById("entityName"));

	var currGrpDropBoxElement = new SwSelectBox(document.forms[0].elements["useroptions.currentCcyGrpId"],document.getElementById("currGrpName"));

	var sectionDropBoxElement = new SwSelectBox(document.forms[0].elements["useroptions.sectionid"],document.getElementById("sectionDesc"));

    document.getElementById("savebutton").innerHTML = document.getElementById("savedisablebutton").innerHTML;
	document.getElementById("cancelbutton").innerHTML = document.getElementById("canceldisablebutton").innerHTML; 

	
 	<%if ( (SwtConstants.STR_TRUE).equals(request.getAttribute(SwtConstants.SAV_BUT_STS)) ) {%>
		document.getElementById("savebutton").innerHTML = document.getElementById("saveenablebutton").innerHTML;
	<%}%>

	 <%if ( !(SwtConstants.STR_TRUE).equals(request.getAttribute(SwtConstants.SAV_BUT_STS)) ) {%>
		document.getElementById("savebutton").innerHTML = document.getElementById("savedisablebutton").innerHTML;
	<%}%>

	 <%if ( (SwtConstants.STR_TRUE).equals(request.getAttribute(SwtConstants.CHG_BUT_STS)) ) {%>
		document.getElementById("changebutton").innerHTML = document.getElementById("changeenablebutton").innerHTML;
	<%}%>

	 <%if ( !(SwtConstants.STR_TRUE).equals(request.getAttribute(SwtConstants.CHG_BUT_STS)) ) {%>
		document.getElementById("changebutton").innerHTML = document.getElementById("changedisablebutton").innerHTML;
	<%}%>

	<%if ( (SwtConstants.STR_TRUE).equals(request.getAttribute(SwtConstants.CAN_BUT_STS)) ) {%>
		document.getElementById("cancelbutton").innerHTML = document.getElementById("cancelenablebutton").innerHTML;
	<%}%>

	 <%if ( !(SwtConstants.STR_TRUE).equals(request.getAttribute(SwtConstants.CAN_BUT_STS)) ) {%>
		document.getElementById("cancelbutton").innerHTML = document.getElementById("canceldisablebutton").innerHTML;
	<%}%>

	
	if(menuEntityCurrGrpAccess == "0")
	{
		document.getElementById("changebutton").innerHTML = document.getElementById("changeenablebutton").innerHTML;
	}else{
		document.getElementById("changebutton").innerHTML = document.getElementById("changedisablebutton").innerHTML;
	}
	
	document.forms[0].menuAccessId.value = "${requestScope.menuAccessId}";
	
	var headerData = [];
	var dataprovider = new Array();
	
	var newElement1 = {};
	newElement1[headerData[0]] = 'Pdf';
	dataprovider.push(newElement1);
			
	var newElement2 = {};
	newElement2[headerData[0]] = 'Excel';
	dataprovider.push(newElement2);
	
	$("#exportReport").exportselect ({
		dataprovider: dataprovider,
		change: exportReport,
		selectedIndex:0
	  });
	
	document.forms[0].formatAltered.value = '${requestScope.formatAltered}';
	if (document.forms[0].formatAltered.value == 'Y')
	{
		
		alert('<s:text name="alert.DateAmountFormatChanged"/>');
	}	
}

function exportReport(){
	var type=$("#exportReport").getSelected(0);
	return submitPrint('report',type.toLowerCase());
}

<s:if test='"yes" == #request.parentFormRefresh' >

window.opener.document.forms[0].method.value="display";
window.opener.document.forms[0].submit();
self.close();
</s:if>

function submitRoleForm(methodName){
	document.forms[0].method.value = methodName;
	document.forms[0].submit();
	alert('<s:text name="userOptions.alert.me"/>');
}
function submitCancel(methodName){
	
	<%if ( (SwtConstants.STR_TRUE).equals(request.getAttribute(SwtConstants.CAN_BUT_STS)) ) {%>
	
	
	     var yourstate=window.confirm('<s:text name="userOptions.confirm.close"/>');
		    if (yourstate==true){ 
	                 window.close();
			}
  
	<%}%>
	<%if ( !(SwtConstants.STR_TRUE).equals(request.getAttribute(SwtConstants.CAN_BUT_STS)) ) {%>
	document.forms[0].method.value = methodName;
	 window.close();
	 <%}%>
	
}
/*Start:Code Modified by Alibasha on 19-Mar-2012 For TPR_530 related to the Mantis 1557 */
/**
* This method is used to submit the form when click the save button.
*
* @param methodName
* @return none
**/
function submitForm(methodName){

    var phoneValue=validateField(document.forms[0].elements['useroptions.phonenumber'],'useroptions.phonenumber','numberPat');
    document.forms[0].currententity.value = document.forms[0].elements["useroptions.currententity"].value;	
	if(phoneValue)
	{
	  var emailValue=validateField(document.forms[0].elements['useroptions.emailId'],'useroptions.emailId','emailPat');
	  if(emailValue)
	  {
	  
        elementTrim(document.forms[0]);
	

	    if(validateForm(document.forms[0]) ){
			document.forms[0].method.value = methodName;
			document.forms[0].language.value = document.forms[0].elements["useroptions.language"].value;
			document.forms[0].phonenumber.value = document.forms[0].elements["useroptions.phonenumber"].value;
			document.forms[0].emailId.value = document.forms[0].elements["useroptions.emailId"].value;
			document.forms[0].dateFormat.value = document.forms[0].elements["useroptions.dateFormat"].value;
			document.forms[0].amountDelimiter.value = document.forms[0].elements["useroptions.amountDelimiter"].value;
			enableFields();
			document.forms[0].submit();
	
		}
	
	}
	else
	{
	  document.forms[0].elements['useroptions.emailId'].focus();
	}
	}else
	{
	  document.forms[0].elements['useroptions.phonenumber'].focus();
	}  	

}
/*End:Code Modified by Alibasha on 19-Mar-2012 For TPR_530 related to the Mantis 1557  */

function submitPrint(methodName,fileType){
	document.forms[0].fileType.value = fileType.trim();
	document.forms[0].method.value = methodName;
	document.forms[0].selectedUserCodeId.value = document.forms[0].elements["useroptions.id.userId"].value;
	document.forms[0].submit();
	
	setParentChildsFocus();
	

}



function onEntityChange(methodName){
	document.forms[0].currententity.value = document.forms[0].elements["useroptions.currententity"].value;
	document.forms[0].language.value = document.forms[0].elements["useroptions.language"].value;
	document.forms[0].phonenumber.value = document.forms[0].elements["useroptions.phonenumber"].value;
	document.forms[0].emailId.value = document.forms[0].elements["useroptions.emailId"].value;	
	document.forms[0].dateFormat.value = document.forms[0].elements["useroptions.dateFormat"].value;
	document.forms[0].amountDelimiter.value = document.forms[0].elements["useroptions.amountDelimiter"].value;
	document.forms[0].entityChange.value = "Y";
   	document.forms[0].method.value = methodName;
	enableFields();
	document.forms[0].submit();
}

 function submitFormCancel(methodName){
 
     
	       enableFields();
		  document.forms[0].method.value = methodName;
		  document.forms[0].submit();
	 
	
		
}
function enableFields(){
	document.forms[0].elements["useroptions.id.userId"].disabled = "";
	document.forms[0].elements["useroptions.username"].disabled = "";
	document.forms[0].elements["useroptions.sectionid"].disabled = "";
	document.forms[0].elements["useroptions.language"].disabled = "";
	document.forms[0].elements["useroptions.phonenumber"].disabled = "";
	document.forms[0].elements["useroptions.emailId"].disabled = "";
	document.forms[0].elements["useroptions.roleId"].disabled = "";
	document.forms[0].elements["useroptions.currententity"].disabled = "";
	document.forms[0].elements["useroptions.currentCcyGrpId"].disabled = "";
	document.forms[0].elements["useroptions.dateFormat"].disabled = "";
	document.forms[0].elements["useroptions.amountDelimiter"].disabled = "";

}
function validateForm(objForm){
  var elementsRef = new Array(7);
  elementsRef[0] = objForm.elements["useroptions.id.userId"];
  elementsRef[1] = objForm.elements["useroptions.username"];
  elementsRef[2] = objForm.elements["useroptions.language"];
  elementsRef[3] = objForm.elements["useroptions.roleId"];
  elementsRef[4] = objForm.elements["useroptions.dummyPassword"];
  elementsRef[5] = objForm.elements["useroptions.currententity"];
  elementsRef[6] = objForm.elements["useroptions.currentCcyGrpId"];  
  return validate(elementsRef);
 }

</SCRIPT>
</head>
<body leftmargin="0" topmargin="0" marginheight="0"  onLoad="setParentChildsFocus();bodyOnLoad();setFocus(document.forms[0]);" onunload="call()">
<s:form action="useroptions.do" method="post" onsubmit="return validate(this);">
<s:hidden name="useroptions.invPassAttempt"/>
<input name="method" type="hidden" value="roleEntityAccess">
<input name="oldValue" type="hidden" value= "${oldValue}">

<input name="menuAccessId" type="hidden" >

<input name="selectedUserCodeId" type="hidden" >
<input name="fileType" type="hidden" value="">
<input name="currententity" type="hidden" value="">
<input name="language" type="hidden" value="">
<input name="phonenumber" type="hidden" value="">
<input name="emailId" type="hidden" value="">
<input name="entityChange" type="hidden" value="">
<input name="formatAltered" type="hidden" value="">
<input name="dateFormat" type="hidden" value="">
<input name="amountDelimiter" type="hidden" value="">

<div id="useroptions" style="position:absolute; left:20px; top:20px; width:603px; height:568px; border:2px outset;" color="#7E97AF">
<div id="useroptions" style="position:absolute; left:8px; top:4px; width:590px; height:38px;">


<!--------------------------first fieldset---------------------------------------------------->
<div style="left:8px; top:4px;height:120px">
<fieldset style="width:585px;border:2px groove;">

<legend>
<s:text name="usermaintenance.user"/>
</legend>

<table width="517" border="0" cellpadding="0" cellspacing="1" height="100">      
	 
	 <tr height="24">
	  <td width="163px">&nbsp;<b  style="font-size:9pt;" ><s:text name="usermaintenance.userId*"/></b></td>
	   <td width="28px">&nbsp;</td>
	  <td width="330px" height="25">
	  
	  <s:if test='"view" == #request.methodName' > 
         <s:textfield name="useroptions.id.userId" titleKey="tooltip.user.id" cssClass="htmlTextAlpha" cssStyle="width:140px;" disabled="%{#attr.screenFieldsStatus}"/>
	  </s:if>
        
        <s:if test='"update" == #request.methodName' > 
         <s:textfield name="useroptions.id.userId" cssClass="htmlTextAlpha" cssStyle="width:140px;" onchange="return validateField(this,'useroptions.id.userId','alphaNumPat');" disabled="true" titleKey="tooltip.user.id"/>      
         </s:if>

		</td>
		</tr>


	<tr height="24">
	  <td width="163px">&nbsp;<b  style="font-size:9pt;"><s:text name="usermaintenance.userName"/>*</b></td>
	   <td width="28px">&nbsp;</td>
	  <td width="330px">
	  
	  <s:if test='"view" == #request.methodName' > 
         <s:textfield name="useroptions.username" style="width:280px;" styleClass="htmlTextAlpha" disabled="%{#attr.screenFieldsStatus}" titleKey="tooltip.user.name"/>      
         </s:if>

          <s:if test='"update" == #request.methodName' > 
         <s:textfield name="useroptions.username" style="width:280px;" styleClass="htmlTextAlpha" disabled="%{#attr.screenFieldsStatus}" titleKey="tooltip.user.name"/>      
         </s:if>
		 </td>
		 </tr>


	<tr height="24">
	  <td width="163px">&nbsp;<b  style="font-size:9pt;"><s:text name="usermaintenance.password"/></b></td>
	  <td width="28px">&nbsp;</td>
	  <td width="330px">
	   <s:if test='"update" == #request.methodName' > 
         <s:textfield type="password" name="useroptions.dummyPassword" cssStyle="width:140px;" cssClass="htmlTextAlpha" disabled="%{#attr.screenFieldsStatus}" titleKey="tooltip.userpassword" />      
       </s:if>
          
        <s:if test='"view" == #request.methodName' > 
        <s:textfield type="password" name="useroptions.dummyPassword" cssStyle="width:140px;" cssClass="htmlTextAlpha" disabled="%{#attr.screenFieldsStatus}"  titleKey="tooltip.userpassword" />      
        </s:if>
		 </td>
	 </tr>

	  <tr height="24">
	   <td width="163px" > &nbsp;<b  style="font-size:9pt;"><s:text name="usermaintenance.status"/></b></td>
	   <td width="28px">&nbsp;</td>
		<td width="330px">
		    <s:if test='"update" == #request.methodName' >  
		        <s:radio  name="useroptions.status" list="#{'1':''}" cssStyle="width:13;" titleKey="tooltip.selectUserStatus" disabled="%{#attr.screenFieldsStatus}" />&nbsp;&nbsp;<s:text name="userOptions.Label.enabled"/>
		        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
		        <s:radio  name="useroptions.status" list="#{'2':''}" cssStyle="width:13;" titleKey="tooltip.selectUserStatus" disabled="%{#attr.screenFieldsStatus}"/>&nbsp;&nbsp;<s:text name="userOptions.Label.disabled"/>
		    </s:if>
		
		    <s:if test='"view" == #request.methodName' >  
		        <s:radio  name="useroptions.status" list="#{'1':''}" cssStyle="width:13;" titleKey="tooltip.selectUserStatus" disabled="%{#attr.screenFieldsStatus}"/>&nbsp;&nbsp;<s:text name="userOptions.Label.enabled"/>
		        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
		        <s:radio  name="useroptions.status" list="#{'2':''}" cssStyle="width:13;" titleKey="tooltip.selectUserStatus" disabled="%{#attr.screenFieldsStatus}"/>&nbsp;&nbsp;<s:text name="userOptions.Label.disabled"/>
		    </s:if>
		</td>
	 </tr>



</table>
</fieldset>
</div>

<!--------------------------------------end of first fieldset---------------------------------------------->

<!-----------------------------------second fieldset-------------------------------------------->
<div style="left:8px; top:4px;height:120px;">
<fieldset style="width:585px;border:2px groove;">

<legend>
<s:text name="usermaintenance.profile"/>
</legend>

<table width="580" border="0" cellpadding="0" cellspacing="1" height="75px"> 

<tr height="24">
	  <td width="163px">&nbsp;<b style="font-size:9pt;"><s:text name="alertMessage.roleId"/>*</b></td>
	  <td width="28px">&nbsp;</td>
	  <td width="404px">

     <s:if test='"view" == #request.methodName' > 
	<s:select id="useroptions.roleId" name="useroptions.roleId" disabled="%{#attr.screenFieldsStatus}"  onchange="submitRoleForm('roleEntityAccess');" styleClass="htmlTextAlpha" cssStyle="width:140px;" titleKey="tooltip.userrole" list="#request.roleIdList" listKey="value" listValue="label" />
	
	 </s:if>

		<s:if test='"update" == #request.methodName' > 
	<s:select id="useroptions.roleId" name="useroptions.roleId" disabled="%{#attr.screenFieldsStatus}" onchange="submitRoleForm('roleEntityAccess');" styleClass="htmlTextAlpha" cssStyle="width:140px" titleKey="tooltip.userrole" list="#request.roleIdList" listKey="value" listValue="label" />
	
		 </s:if>&nbsp;&nbsp;
		 <span id="roleName" class="spantext">

	  </td>
</tr>

<tr height="24">
	  <td width="163px">&nbsp;<b style="font-size:9pt;"><s:text name="usermaintenance.entity"/>*</b></td>
	  <td width="28px">&nbsp;</td>
	  <td width="404px">	
			<s:if test='"view" == #request.methodName' > 
			      <s:select id="useroptions.currententity" name="useroptions.currententity" tabindex="1" disabled="%{#attr.screenFieldsStatus}" cssStyle="width:140px" styleClass="htmlTextAlpha" titleKey="tooltip.selectEntity" list="#request.roleEntityList" listKey="value" listValue="label" />
					

			</s:if>

			<s:if test='"update" == #request.methodName' > 
			  <s:select id="useroptions.currententity" name="useroptions.currententity" tabindex="2" disabled="false" cssStyle="width:140px" styleClass="htmlTextAlpha" onchange="javascript:onEntityChange('change')" titleKey="tooltip.selectEntity" list="#request.roleEntityList" listKey="value" listValue="label" />
			
			</s:if>&nbsp;&nbsp;
					 <span id="entityName" class="spantext">

	  </td>
</tr>

<tr height="24">
	  <td width="163px">&nbsp;<b style="font-size:9pt;"><s:text name="usermaintenance.currGrp"/>*</b></td>
	  <td width="28px">&nbsp;</td>
	  <td width="404px">	
			<s:if test='"view" == #request.methodName' > 
			      <s:select id="useroptions.currentCcyGrpId" name="useroptions.currentCcyGrpId" tabindex="1" disabled="%{#attr.screenFieldsStatus}" cssStyle="width:140px" styleClass="htmlTextAlpha" titleKey="tooltip.selectCuurencyGrp" list="#request.currencyGroupList" listKey="value" listValue="label" />
					

			</s:if>

			<s:if test='"update" == #request.methodName' > 
			  <s:select id="useroptions.currentCcyGrpId" name="useroptions.currentCcyGrpId" tabindex="2" disabled="false" cssStyle="width:140px" styleClass="htmlTextAlpha" titleKey="tooltip.selectCuurencyGrp" list="#request.currencyGroupList" listKey="value" listValue="label" />
			
			</s:if>&nbsp;&nbsp;
					 <span id="currGrpName" class="spantext">

	  </td>
</tr>

<tr height="24">
<td width="163px">&nbsp;<b style="font-size:9pt;"><s:text name="usermaintenance.section"/></b></td>
	   <td width="28px">&nbsp;</td>
	  <td width="404px">
	  <s:if test='"view" == #request.methodName' > 
          <s:select id="useroptions.sectionid" name="useroptions.sectionid" cssStyle="width:140px" styleClass="htmlTextAlpha" disabled="%{#attr.screenFieldsStatus}" list="#request.sectiondetails" listKey="value" listValue="label" />
          </s:if>
          
            
          <s:if test='"update" == #request.methodName' > 
          <s:select id="useroptions.sectionid" name="useroptions.sectionid" disabled="%{#attr.screenFieldsStatus}" cssStyle="width:140px" styleClass="htmlTextAlpha" list="#request.sectiondetails" listKey="value" listValue="label" />
          
          </s:if>&nbsp;&nbsp;&nbsp;<span id="sectionDesc" name="sectionDesc" class="spantext"></span>
		</td>
</tr>

</table>
</fieldset>
</div>

<!--------------------------------------end of second fieldset-------------------------------------------->

<!-----------------------------------third fieldset------------------------------------------------------->
<div style="left:8px; top:4px;height:95px;">
<fieldset style="width:585px;border:2px groove;">

<legend>
<s:text name="usermaintenance.Personal"/>
</legend>

<table width="517" border="0" cellpadding="0" cellspacing="1" height="75px"> 

<tr height="24">
          <td width="163px">&nbsp;<b style="font-size:9pt;"><s:text name="usermaintenance.language"/></b></td>
		  <td width="28px">&nbsp;</td>
          <td width="330px">
		   <s:if test='"update" == #request.methodName' > 
          <s:select id="useroptions.language" name="useroptions.language" titleKey="tooltip.selectLanguage" tabindex="2" cssStyle="width:140px" disabled="false" styleClass="htmlTextAlpha" list="#request.languagedetails" listKey="value" listValue="label" />
          
          </s:if>
           
           <s:if test='"view" == #request.methodName' > 
           <s:select id="useroptions.language" name="useroptions.language" titleKey="tooltip.enterLanguage" cssStyle="width:140px" disabled="%{#attr.screenFieldsStatus}" styleClass="htmlTextAlpha" list="#request.languagedetails" listKey="value" listValue="label" />
          
          </s:if>
		  </td>
</tr>


<tr height="24">
	  <td width="163px">&nbsp;<b style="font-size:9pt;"><s:text name="usermaintenance.phoneNo"/></b></td>
	  <td width="28px">&nbsp;</td>
	  <td width="330px">
	           
          <s:if test='"view" == #request.methodName' > 
         <s:textfield name="useroptions.phonenumber" titleKey="tooltip.enterPhNo" style="width:140px;" styleClass="htmlTextAlpha" disabled="%{#attr.screenFieldsStatus}"/>      
         </s:if>
          
          <s:if test='"update" == #request.methodName' > 
         <s:textfield name="useroptions.phonenumber" titleKey="tooltip.enterPhNo" style="width:140px;" maxlength="20" onchange="return validateField(this,'useroptions.phonenumber','numberPat');" styleClass="htmlTextAlpha" />      
         </s:if>
         </td>
	</tr>

<tr height="24">
	  <td width="163px">&nbsp;<b style="font-size:9pt;"><s:text name="usermaintenance.emailId"/></b></td>
	   <td width="28px">&nbsp;</td>
	  <td width="330px">
         <s:if test='"view" == #request.methodName' > 
         <s:textfield name="useroptions.emailId" maxlength="50" style="width:300px;" styleClass="htmlTextAlpha" titleKey="tooltip.emailId" disabled="%{#attr.screenFieldsStatus}"/>      
         </s:if>
         
         <s:if test='"update" == #request.methodName' > 
         <s:textfield name="useroptions.emailId" maxlength="50" titleKey="tooltip.emailId" style="width:300px;" onchange="return validateField(this,'useroptions.emailId','emailPat');" styleClass="htmlTextAlpha" />      
         </s:if>
	</td>
</tr>


</table>
</fieldset>
</div>

<!-----------------------------end of third fieldset------------------------------------------------>

<!------------------------------------fourth fieldset------------------------------------------------->
<div style="left:8px; top:4px;height:143px;">
	  	
<fieldset style="width:585px;border:2px groove;">

<legend>
<s:text name="usermaintenance.info"/>
</legend>

<table width="505" border="0" cellpadding="0" cellspacing="1" height="100px"> 
	

	
	<tr height="24">
	  <td  width="163px">&nbsp;<b style="font-size:9pt;"><s:text name="usermaintenance.lastLogin"/></b></td>
	   <td width="32px">&nbsp;</td>
	  <td  width="160px">
		  <input value="${loginDate}" name="loginDate" class="textNumeric" style="width:145px;" disabled="true" title='<s:text name="tooltip.user.lastlog"/>' />
	 </td>
	 	  <td  width="160px">
		  <input value="${lastLoginIP}" name="loginDate" class="textNumeric" style="width:145px;" disabled="true" title='<s:text name="login.notification.tooltip.lastLoginIp"/>' />
	 </td>
    </tr>



	<tr height="24">
          <td width="250px">&nbsp;<b style="font-size:9pt;"><s:text name="usermaintenance.pwdDate"/></b></td>
		  <td width="32px">&nbsp;</td>
          <td width="160px">
		   <s:if test='"update" == #request.methodName' > 
			<input value="${uDate}" name="uDate"  class="textNumeric" style="width:145px;" disabled="true" title='<s:text name="tooltip.user.lastpass"/>' />       
         </s:if>
          
	  
		  <s:if test='"view" == #request.methodName' > 
          <input value="${uDate}" name="uDate"  class="textNumeric" style="width:145px;" disabled="true" title='<s:text name="tooltip.user.lastpass"/>' /> 
         </s:if>
		 </td>
	</tr>

	<tr height="24">
          <td width="163px">&nbsp;<b style="font-size:9pt;"><s:text name="usermaintenance.lastLogout"/></b></td>
		  <td width="32px">&nbsp;</td>
          <td width="160px">
		     <s:if test='"view" == #request.methodName' > 
		  <input value="${logoutDate}" name="logoutDate"  class="textNumeric" style="width:145px;" disabled="true" title='<s:text name="tooltip.user.lastlogout"/>' />
          </s:if>
          
		  <s:if test='"update" == #request.methodName' > 
        <input value="${logoutDate}" name="logoutDate"  class="textNumeric"style="width:145px;" disabled="true" title='<s:text name="tooltip.user.lastlogout"/>' /> 
         </s:if>
		 </td>
		 </tr>
		 
	 	<tr height="24">
          <td width="163px">&nbsp;<b style="font-size:9pt;"><s:text name="login.notification.lastFailedLogin"/></b></td>
		  <td width="32px">&nbsp;</td>
          <td width="160px">
        	<input value="${lastFaileddate}" name="lastFailedLogin"  class="textNumeric"style="width:145px;" disabled="true" title='<s:text name="login.notification.lastFailedLogin"/>' /> 
	 	  </td>
	 	   <td width="160px">
        	<input value="${lastFailedIP}" name="lastFailedLogin"  class="textNumeric"style="width:145px;" disabled="true" title='<s:text name="login.notification.tooltip.lastFailedLoginIp"/>' /> 
	 	  </td>
		 </tr>
		 
		 
		 <tr height="24">
          <td width="163px">&nbsp;<b style="font-size:9pt;"><s:text name="usermaintenance.extAuthId"/></b></td>
		  <td width="32px">&nbsp;</td>
          <td width="160px" colspan="3">
		     <s:if test='"view" == #request.methodName' > 
		  <input value="${extAuthId}" name="extAuthId"  class="textAlpha" style="width:300px;" disabled="true" title='<s:text name="tooltip.extAuthId"/>' />
          </s:if>
         
		  <s:if test='"update" == #request.methodName' > 
        <input value="${extAuthId}" name="extAuthId"  class="textAlpha"style="width:300px;" disabled="true" title='<s:text name="tooltip.extAuthId"/>' /> 
         </s:if>
		 </td>
		 </tr>

	</table>
	</fieldset>
	</div>
<!---------------------------------end of fourth fieldset-------------------------------->
<!-----------------------------------five fieldset-------------------------------------------->
<div style="left: 8px; top: 4px; height: 70px;">
    <fieldset style="width: 585px; border: 2px groove;">
        <legend>
            <s:text name="generalsystem.generalSettings" />
        </legend>
        <table width="383px" border="0" cellspacing="0" cellpadding="0">
            <tr height="24">
                <td width="163px"><b style="font-size: 9pt;">
                        <s:text name="dateFormat" /></b></td>
                <td width="28px">&nbsp;</td>
                <td width="115px">
                    <s:if test='"view" == #request.methodName'>
                        <s:radio id="id1" titleKey="tooltip.selectDateFormatDMY" cssStyle="width:13px;margin-bottom: 5px;" name="useroptions.dateFormat" list="#{'1':''}" disabled="%{#attr.screenFieldsStatus}" />
                        <label titleKey="tooltip.selectDateFormatDMY" style="height: 19px;">
                            <s:text name="dateFormatDDMMYY" />
                        </label>
                    </s:if>
                    <s:if test='"update" == #request.methodName'>
                        <s:radio id="id1" titleKey="tooltip.selectDateFormatDMY" cssStyle="width:13px;margin-bottom: 5px;" name="useroptions.dateFormat" list="#{'1':''}" disabled="false" />
                        <label titleKey="tooltip.selectDateFormatDMY" style="height: 19px;">
                            <s:text name="dateFormatDDMMYY" />
                        </label>
                    </s:if>
                </td>
                <td width="112px" style="margin-bottom: 8px; padding-bottom: 5px;">
                    <s:if test='"view" == #request.methodName'>
                        <s:radio name="useroptions.dateFormat" id="id2" titleKey="tooltip.selectDateFormatMDY" cssStyle="width:13px;" list="#{'2':''}" disabled="%{#attr.screenFieldsStatus}" />
                        <label titleKey="tooltip.selectDateFormatMDY">
                            <s:text name="dateFormatMMDDYY" />
                        </label>
                    </s:if>
                    <s:if test='"update" == #request.methodName'>
                        <s:radio name="useroptions.dateFormat" id="id2" titleKey="tooltip.selectDateFormatMDY" cssStyle="width:13px;" list="#{'2':''}" disabled="false" />
                        <label titleKey="tooltip.selectDateFormatMDY">
                            <s:text name="dateFormatMMDDYY" />
                        </label>
                    </s:if>
                </td>
            </tr>
            <tr height="24">
                <td width="128px"><b style="font-size: 9pt;">
                        <s:text name="amountDelimiter" /></b></td>
                <td width="28px">&nbsp;</td>
                <td width="115px">
                    <s:if test='"view" == #request.methodName'>
                        <s:radio id="1" titleKey="tooltip.selectAmountFormat" cssStyle="width:13px;margin-bottom: 5px;" name="useroptions.amountDelimiter" list="#{'1':''}" disabled="%{#attr.screenFieldsStatus}" />
                        <label titleKey="tooltip.selectAmountFormat" style="height: 19px;">
                            <s:text name="amountcomabeforedecimal" />
                        </label>
                    </s:if>
                    <s:if test='"update" == #request.methodName'>
                        <s:radio id="1" titleKey="tooltip.selectAmountFormat" cssStyle="width:13px;margin-bottom: 5px;" name="useroptions.amountDelimiter" list="#{'1':''}" disabled="false" />
                        <label titleKey="tooltip.selectAmountFormat" style="height: 19px;">
                            <s:text name="amountcomabeforedecimal" />
                        </label>
                    </s:if>
                </td>
                <td width="112px" style="margin-bottom: 8px; padding-bottom: 5px;">
                    <s:if test='"view" == #request.methodName'>
                        <s:radio name="useroptions.amountDelimiter" id="2" titleKey="tooltip.selectAmountFormat" cssStyle="width:13px;" list="#{'2':''}" disabled="%{#attr.screenFieldsStatus}" />
                        <label titleKey="tooltip.selectAmountFormat">
                            <s:text name="amountcomaafterdecimal" />
                        </label>
                    </s:if>
                    <s:if test='"update" == #request.methodName'>
                        <s:radio name="useroptions.amountDelimiter" id="2" titleKey="tooltip.selectAmountFormat" cssStyle="width:13px;" list="#{'2':''}" disabled="false" />
                        <label titleKey="tooltip.selectAmountFormat">
                            <s:text name="amountcomaafterdecimal" />
                        </label>
                    </s:if>
                </td>
            </tr>
        </table>
    </fieldset>
</div>

				<!---------------------------------end of five fieldset-------------------------------->

</div>
</div>
<!--Start: Code modified by Med Amine on 29-jan-2013 for Mantis:2096 -->
<div id="UserOptions" style="position:absolute; left:540; top:609px; width:70; height:25px; z-index:5; visibility:visible;">
    <table >
	  	<div id="exportReport" />
	</table>
</div>

 <div id ="helpIcon" style="position:absolute; left:575; top:607px; width:30; height:39px;">
			<s:if test='"update" == #request.methodName' >
					<a tabindex="7" href=# onclick="javascript:openWindow(buildPrintURL('print','Change User Detail '),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')" onMouseOut="MM_swapImgRestore()" onMouseOver="MM_swapImage('Help','','images/help_default.GIF ',1)"><img src="images/help_default.GIF " name="Help"  border="0" title='<s:text name="tooltip.helpScreen"/>'></a> 
        </s:if>
	   <s:if test='"update" != #request.methodName' >
	            <a tabindex="7" href=# onclick="javascript:openWindow(buildPrintURL('print','My User Detail '),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')" onMouseOut="MM_swapImgRestore()" onMouseOver="MM_swapImage('Help','','images/help_default.GIF ',1)"><img src="images/help_default.GIF " name="Help"  border="0" title='<s:text name="tooltip.helpScreen"/>'></a> 
        </s:if>
</div>
<!--End: Code modified by Med Amine on 29-jan-2013 for Mantis:2096 -->

<div id="ddimagebuttons" color="#7E97AF" style="position:absolute; border:2px outset; left:20; top:599px; width:603px; height:39px; visibility:visible;">
  <div id="UserOptions" style="position:absolute; left:8; top:4; width:603px; height:15px; visibility:visible;">
  <table width="280" border="0" cellspacing="0" cellpadding="0" height="20">
		<tr>
			<td width="70" id="changebutton">		
			</td>
			<td width="70" id="savebutton">		
			</td>
			<td width="70" id="cancelbutton">		
			</td>
			<td width="70" id="closebutton">	
			  <a  tabindex="6" title='<s:text name="tooltip.close"/>'  onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onClick="javascript:submitCancel('close')"><s:text name="button.close"/></a>
						
			</td>
		</tr>
		</table>
	</div>
    <div style="position:absolute; left:6; top:4; width:603; height:15px; visibility:hidden;">  	
    <table width="280" border="0" cellspacing="0" cellpadding="0" height="20" style="visibility:hidden">
	<tr>
		
		
		<td  id="changeenablebutton">		
			<a tabindex="3" title='<s:text name="tooltip.changeUserDetails"/>' onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onClick="javascript:submitForm('change')"><s:text name="button.change"/></a>
		</td>		
		<td id="changedisablebutton">
			<a  class="disabled" disabled="disabled"><s:text name="button.change"/></a>
		</td>
	
        <td  id="saveenablebutton">		
		<a  tabindex="4" title='<s:text name="tooltip.save"/>' onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onClick="javascript:submitForm('update')"><s:text name="button.save"/></a>
		</td>		
		<td id="savedisablebutton">
			<a  class="disabled" disabled="disabled" ><s:text name="button.save"/></a>
		</td>
		<td id="cancelenablebutton">		
			<a tabindex="5" title='<s:text name="tooltip.cancel"/>' onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onClick="javascript:submitFormCancel('display')"><s:text name="button.cancel"/></a>			
		</td>		
		<td id="canceldisablebutton">
			<a  class="disabled" disabled="disabled"><s:text name="button.cancel"/></a>
		</td>						
		
	</tr>
    </table>
  </div>
</div>



</s:form>
</body>
</html>