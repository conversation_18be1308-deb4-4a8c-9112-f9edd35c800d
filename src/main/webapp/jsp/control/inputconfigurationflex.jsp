<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@ include file="/taglib.jsp"%>
<%@ page import="org.swallow.util.CommonDataManager"%>
<%@ page import="org.swallow.util.SwtConstants"%>
<%@ page import="org.swallow.util.SwtUtil"%>
<%@ include file="/angularJSUtils.jsp"%>

<html>
	<head>
		<meta name="viewport" content="width=device-width, initial-scale=1">
<link rel="icon" type="image/x-icon" href="favicon.ico">
<link rel="stylesheet" href="angularSources/styles.css?version=<%= SwtUtil.appVersion %>">
		<title><s:text name="inputconfig.title.window"/><%="yes".equals(request.getAttribute("fromPCM")) ? " (PCM)":""%></title>
		
		<style>
			body { margin: 0px; overflow:hidden }
		</style>
</head>
		<script type="text/javascript" src="jsp/work/assets/swfobject.js"></script>
		<script type="text/javascript">

			window.onunload = call;
			var screenRoute = "inputConfig";
			var fromPCM = '${requestScope.fromPCM}';
		</script>
		
		<script type="text/javascript">			
			var refreshPending = false;
			 /* Varaible declared to get MenuAccessId to 
			 * Disable change button if the screen set view acces */
			var menuAccessId = <%=request.getParameter("menuAccessId")%>;
			 /* Varaible declared to get MenuAccessId to 
			 * Disable change button if the screen set view acces */
			var label = new Array ();
			label["text"] = new Array ();
			label["tip"] = new Array ();
			
			label["text"]["button-refresh"] = "<s:text name="button.Refresh"/>";
			label["tip"]["button-refresh"] = "<s:text name="tooltip.refreshWindow"/>";
			
			label["text"]["button-rate"] = "<s:text name="accountmonitorbutton.Rate"/>";
			label["tip"]["button-rate"] = "<s:text name="tooltip.rateButton"/>";
			
			label["text"]["button-close"] = "<s:text name="button.close"/>";
			label["tip"]["button-close"] = "<s:text name="tooltip.close"/>";
			
			label["text"]["button-save"] = "<s:text name="button.save"/>";
			label["tip"]["button-save"] = "<s:text name="tooltip.SaveChanges"/>";
			
			label["text"]["button-cancel"] = "<s:text name="button.cancel"/>";
			label["tip"]["button-cancel"] = "<s:text name="tooltip.CancelChanges"/>";
			
			label["text"]["button-change"] = "<s:text name="button.change"/>";
			label["tip"]["button-change"] = "<s:text name="inputconfig.tooltip.change"/>";
			/* Start: code added by krishna on 24-Jul-2011 for mantis 1446:GUI changes in Predict for Smart Input v6  */
			label["text"]["alert-save"] = "<s:text name="alert.interfaceSettings.save"/>";
			label["text"]["alert-emaillogsto"] = "<s:text name="alert.interfaceSettings.emaillogsto"/>";
			label["text"]["alert-number" ] = "<s:text name="alert.interfaceSettings.number"/>";
			label["text"]["alert-posnumber"] = "<s:text name="alert.interfaceSettings.posnumber"/>";
			label["text"]["alert-emails"] = "<s:text name="alert.interfaceSettings.emails"/>";
			label["text"]["alert-emaillog"] = "<s:text name="alert.interfaceSettings.emaillog"/>";
			label["text"]["alert-emails"] = "<s:text name="alert.interfaceSettings.emails"/>";
			label["text"]["alert-text"] = "<s:text name="alert.interfaceSettings.text"/>";
			label["text"]["alert-time"] = "<s:text name="alert.interfaceSettings.time"/>";
			label["text"]["alert-sec"] = "<s:text name="alert.interfaceSettings.sec"/>";
			label["text"]["alert-directory"] = "<s:text name="alert.interfaceSettings.directory"/>";
			label["text"]["alert-url"] = "<s:text name="alert.interfaceSettings.url"/>";
			label["text"]["alert-password"] = "<s:text name="alert.interfaceSettings.password"/>";
			label["text"]["alert-startendtime"] = "<s:text name="alert.interfaceSettings.startendtime"/>";
			label["text"]["alert-savecancelconfirmation"] = "<s:text name="alert.interfaceSettings.savecancelconfirmation"/>";
			label["text"]["alert-requiresEmailLogsTo"] = "<s:text name="alert.interfaceSettings.requiresEmailLogsTo"/>";
			label["text"]["alert-requiresChannelName"] = "<s:text name="alert.interfaceSettings.requiresChannelName"/>";
			label["text"]["alert-requiresHostName"] = "<s:text name="alert.interfaceSettings.requiresHostName"/>";
			label["text"]["alert-requiresPortNumber"] = "<s:text name="alert.interfaceSettings.requiresPortNumber"/>";
			label["text"]["alert-requiresAlertThreshold"] = "<s:text name="alert.interfaceSettings.requiresAlertThreshold"/>";
			label["text"]["alert-alertThresholdRange"] = "<s:text name="alert.interfaceSettings.alertThresholdRange"/>";;
			label["text"]["label-buildProgress"] = "<s:text name="screen.buildInProgress"/>";
			/* End: code added by krishna on 24-Jul-2011 for mantis 1446:GUI changes in Predict for Smart Input v6  */
                        /*Start : Added by Imed B on 18-02-2014*/
			label["text"]["label-inputConfiguration"] = "<s:text name="interfaceSettings.title"/>";
			label["text"]["alert-warning"] = "<s:text name="screen.warning"/>";
			label["text"]["label-save"] = "<s:text name="interfaceSettings.save"/>";
			label["text"]["label-summaryDetails"] = "<s:text name="interfaceSettings.summaryDetails"/>";
			label["text"]["label-bottomDetails"] = "<s:text name="interfaceSettings.bottomDetails"/>";
			label["text"]["alert-error"] = "<s:text name="screen.error"/>";
			label["text"]["alert-nothingToSave"] = "<s:text name="alert.interfaceSettings.nothingToSave"/>";
			label["text"]["label-dataBuildProgress"] = "<s:text name="screen.buildInProgress"/>";
			/*End : Added by Imed B on 18-02-2014*/
			function help(){
					openWindow(buildPrintURL('print','Interface Setting'),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')
                      }
			/* Added to check the time  */ 
			
			function checkTime(strValue){
			    var PatternsDict = new Object();
			    PatternsDict.timePat = /^([01]?[0-9]|[2][0-3])(:[0-5][0-9])?$/;
			    var thePat = PatternsDict["timePat"];
			    var gotIt;
			    if(strValue.length > 0){
			    	gotIt = thePat.exec(strValue); 
			    }
			    if(!gotIt) {
			    	return false;
			   }
			   return true;
			}
			

						


		</script>
        <%@ include file="/angularscripts.jsp"%>

	    <body scroll="no" style="overflow:hidden" leftmargin="0" topmargin="0" marginheight="0">
<!-- 		<div id="swf"></div> -->
		<form id="exportDataForm" target="tmp" method="post">
		<input type="hidden" name="data" id="exportData"/>
		<input type="hidden" name="screen" id="exportDataScreen" value="<s:text name="inputconfig.title.window"/>"/>
		</form>
		<!-- Start : code added by krishna for Mantis 1446 on 14-Jul-2011 for implementing export -->
		<iframe name="tmp" width="0%" height="0%" src="#" />
		<!-- End : code added by krishna for Mantis 1446 on 14-Jul-2011 for implementing export -->
	</body>
	</html>
