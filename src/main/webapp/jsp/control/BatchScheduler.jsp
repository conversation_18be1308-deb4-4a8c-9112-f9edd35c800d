<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@ include file="/taglib.jsp"  %>
<%@ page import="org.swallow.util.SwtConstants" %>
<%@ page import="org.swallow.util.SwtUtil" %>
<html>
<head>
<title>
<s:if test='"R" == #request.scheduledJobType' >
	<s:text name="reportScheduler.title.window"/>
</s:if>
<s:if test='"R" != #request.scheduledJobType' > 
	<s:if test='"P" == #request.scheduledJobType' >
		<s:text name="processScheduler.title.window"/>
	</s:if>
	<s:if test='"P" != #request.scheduledJobType' >
		<s:text name="batchScheduler.title.window"/>
	</s:if>
</s:if>
</title>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
<SCRIPT language="JAVASCRIPT">
<s:if test='"yes" == #request.parentFormRefresh' >
window.opener.document.forms[0].method.value="displayList";
window.opener.document.forms[0].submit();
self.close();
</s:if>

var filterstatus= "${requestScope.filterStatus}";
var sortStatus="${requestScope.sortStatus}";
var sortDescending="${requestScope.sortDescending}";


var lastRefTime = "${requestScope.lastRefTime}";

var menuEntityCurrGrpAccess = "${requestScope.menuEntityCurrGrpAccess}";
/*Added to apply style on mandatory field ,if it's equal to undefined then no field will be colored*/
mandatoryFieldsArray= "undefined" ;
var scheduledJobType = "${requestScope.scheduledJobType}";
// scroll position
var gridScrollTop="${requestScope.gridScrollTop}";
function onFilterandSort()
{
    updateColors();
    disableAllButtons();
}

function buildJobMaintenance(methodName){

	
	if (scheduledJobType == 'B') {
	    var e = document.getElementById("jobTypeList");
	    var selectedScheduledJobType;
	    if(e){
		    selectedScheduledJobType = e.options[e.selectedIndex].value;
		    document.forms[0].selectedScheduledJobType.value = selectedScheduledJobType;
	    }else {
	    	selectedScheduledJobType = 'R';
	    }
	}else {
		selectedScheduledJobType = 'R';
	}
	
	
    var param = 'scheduler.do?method='+methodName+'&selectedjobId=';
    param += document.forms[0].selectedjobId.value;
    param +='&selectedScheduleId='+ document.forms[0].selectedScheduleId.value;
    param +='&selectedJobType='+ selectedScheduledJobType;
    param +='&scheduledJobType='+ scheduledJobType;
    return  param;
}

/**
* This method used to bulid the parameeters
*
* @return param
*/
function buildEntityProcess(){
	/* To frame the url and parameters */
    var param = 'entityprocess.do?menuAccessId='+document.forms[0].menuAccessId.value; 		
    return  param;
}


function submitDeleteForm(methodName){
	document.forms[0].method.value = methodName;
	var selectedScheduledJobType= "R";
	reportsByTheJob();
	selectedScheduleReports= document.forms[0].selectedScheduleReports.value;
	if(selectedScheduleReports== ""){
		selectedScheduleReports=0;
	}
	var message='<s:text name="batchScheduler.confirm.removeJob"/>';
	if (scheduledJobType == 'B') {
	    var e = document.getElementById("jobTypeList");
	    selectedScheduledJobType = e.options[e.selectedIndex].value;
	    document.forms[0].selectedScheduledJobType.value = selectedScheduledJobType;
	}	
	if(selectedScheduledJobType== 'R'){
		message = message +'\n'+selectedScheduleReports + ' <s:text name="batchScheduler.confirm.removeJobInfo"/>';
	}
	ShowErrMsgWindowWithBtn("", message, YES_NO, deleteYes,deleteNo );
}

function deleteNo(){
	if(selectedScheduledJobType== 'R'){
        document.getElementById("refreshbutton").innerHTML = document.getElementById("refreshdisablebutton").innerHTML;
    	maintainSortFilterStatus();
    	document.forms[0].enableOrDisable.value = "Disable";
    	document.forms[0].method.value = "enableOrDisableJob";
        if (scheduledJobType == 'B') {
    		 var e = document.getElementById("jobTypeList");
    		 var selectedScheduledJobType = e.options[e.selectedIndex].value;
    		 document.forms[0].selectedScheduledJobType.value = selectedScheduledJobType;
         }
    	  document.forms[0].scheduledJobType.value = scheduledJobType;
//           document.forms[0].submit();	       
    }
}
function deleteYes(){
	document.getElementById("refreshbutton").innerHTML = document.getElementById("refreshdisablebutton").innerHTML;
	document.forms[0].scheduledJobType.value = scheduledJobType;
	document.forms[0].submit();    
}
/**
*  This function is used to load the Scheduler screen
*/
function bodyOnLoad(){
	//to load the data grid
	xl = new XLSheet("jobDetails","table_2",["Number","String","String", "String", "String", "String", "String", "String"],"22212111","false");
	xl.onsort = xl.onfilter  =clearJobId;
	//this block is to sel filter status on loading time
	if(filterstatus !=""){
		var	filterStatus1 = filterstatus.split(",");
		if(document.getElementById("jobDetails").innerText != "")
			xl.setFilterStatus(filterStatus1);
	}
	/*	Start:Code Modified by Chinniah on 8-Oct-2012 for Mantis 1999:Scheduler: Some buttons disappear on removal of jobs */
	//this block is to sel filter status on loading time
	if(sortDescending !="" && sortDescending !="null"){
		if(sortDescending=="true"){
			xl.dataTable.defaultDescending = true;
		} else {
			xl.dataTable.defaultDescending =false;
		}
		if(record()!=0)
			xl.dataTable.doManualSorting(sortStatus);
		
	} else {
		xl.dataTable.defaultDescending =false;
		if(record()!=0)
			xl.dataTable.doManualSorting(0);
	}
		
	//Set scroll position of the grid
	if (gridScrollTop != null && gridScrollTop != undefined) {		
		setTimeout(() => {
			document.getElementById('ddscrolltable').scrollTop=gridScrollTop;
		}, 0);
	}
	
	/*	End:Code Modified by Chinniah on 8-Oct-2012 for Mantis 1999:Scheduler: Some buttons disappear on removal of jobs */
	highlightTableRows("jobDetails");
	//determning the status of the buttons in the screen while loading 
	document.getElementById("executebutton").innerHTML = document.getElementById("executedisablebutton").innerHTML; 
	document.getElementById("changebutton").innerHTML = document.getElementById("changedisablebutton").innerHTML; 
	document.getElementById("removebutton").innerHTML = document.getElementById("removedisablebutton").innerHTML; 
	<%if ( (SwtConstants.STR_TRUE).equals(request.getAttribute(SwtConstants.VIEW_BUT_STS)) ) {%>
	document.getElementById("executebutton").innerHTML = document.getElementById("executeenablebutton").innerHTML;
	<%}%>
	<%if ( !(SwtConstants.STR_TRUE).equals(request.getAttribute(SwtConstants.VIEW_BUT_STS)) ) {%>
	document.getElementById("executebutton").innerHTML = document.getElementById("executedisablebutton").innerHTML;
	<%}%>
	<%if ( (SwtConstants.STR_TRUE).equals(request.getAttribute(SwtConstants.KILL_BUT_STS)) ) {%>
	document.getElementById("changebutton").innerHTML = document.getElementById("changeenablebutton").innerHTML;
	<%}%>
	<%if ( !(SwtConstants.STR_TRUE).equals(request.getAttribute(SwtConstants.KILL_BUT_STS)) ) {%>
	document.getElementById("changebutton").innerHTML = document.getElementById("changedisablebutton").innerHTML;
	<%}%>
	<%if ( (SwtConstants.STR_TRUE).equals(request.getAttribute(SwtConstants.KILL_BUT_STS)) ) {%>
	document.getElementById("removebutton").innerHTML = document.getElementById("removeenablebutton").innerHTML;
	<%}%>
	<%if ( !(SwtConstants.STR_TRUE).equals(request.getAttribute(SwtConstants.KILL_BUT_STS)) ) {%>
	document.getElementById("removebutton").innerHTML = document.getElementById("removedisablebutton").innerHTML;
	<%}%>
	document.getElementById("Enbutton").innerHTML = document.getElementById("Endisablebutton").innerHTML;
	document.getElementById("Dibutton").innerHTML = document.getElementById("Didisablebutton").innerHTML;     
	// determining alert to be thrown when excecute,terminate and delete scenarois failed 
	<s:if test='"exec" == #request.methodName' >
	if('${exec}'== 'false')
		alert('<s:text name="batchScheduler.alert.executeDenied"/>');
	</s:if>
	<s:if test='"terminate" == #request.methodName' >
	if('${term}'== 'false')
		alert('<s:text name="batchScheduler.alert.terminateDenied"/>');
	</s:if>
	<s:if test='"SchedulerDelete" == #request.methodName' >
	if('${delete}'== 'false')
		alert('<s:text name="batchScheduler.alert.removeDenied"/>');
	</s:if>
	document.forms[0].selectedjobId.value = "${requestScope.jobIdSelected}";
	document.forms[0].selectedScheduleId.value = "${requestScope.selectedScheduleId}";
	document.forms[0].selectedJobType.value = "${requestScope.selectedJobType}";
	document.forms[0].selectedScheduleReports.value =  "${requestScope.selectedScheduleReports}";
	document.forms[0].changeButtonPressed.value = "";
	<s:if test='"Y" == #request.isSelectedJobRunning' >
	alert('<s:text name="changeJob.alert"/>');
	</s:if>
	<s:if test='"N" == #request.isSelectedJobRunning' >
	javascript:openWindow(buildJobMaintenance('displaySchedule'),'','left=50,top=190,width=650,height=545,toolbar=0,scrollbars=yes,status=yes, resizable=yes');
	</s:if>
	var table = document.getElementById("jobDetails");
	var tbody = table.getElementsByTagName("tbody")[0];
	var rows = tbody.getElementsByTagName("tr");
	var jobIdSelected = "${requestScope.jobIdSelected}";
	var selectedScheduleId = "${requestScope.selectedScheduleId}";
	for (i=0; i < rows.length; i++) {
		var hiddenElement = (rows[i].cells[0].childNodes[0].data).trim();       
		if(hiddenElement == selectedScheduleId) {   
			rows[i].className = 'selectrow' ;
			var jobStatus  = rows[i].getElementsByTagName("input")[1];  
			var jobType  = rows[i].cells[6].getElementsByTagName("input")[0];   
			var hiddenElement = rows[i].getElementsByTagName("input")[0];
			document.forms[0].selectedjobId.value = hiddenElement.value;
			//detrmining button status
			document.getElementById("changebutton").innerHTML = document.getElementById("changeenablebutton").innerHTML;
			document.getElementById("removebutton").innerHTML = document.getElementById("removeenablebutton").innerHTML;
			document.getElementById("Enbutton").innerHTML = document.getElementById("Endisablebutton").innerHTML;
			document.getElementById("Dibutton").innerHTML = document.getElementById("Didisablebutton").innerHTML;     
			document.getElementById("executebutton").innerHTML = document.getElementById("executedisablebutton").innerHTML;
			if(jobType.value == "<%=SwtConstants.JOB_TYPE_MANUAL_DESC%>") {
				if (jobStatus.value != "<%=SwtConstants.JOB_STATUS_DISABLE%>")
					document.getElementById("executebutton").innerHTML = document.getElementById("executeenablebutton").innerHTML;       
			}
			if(jobStatus.value != "<%=SwtConstants.JOB_STATUS_DISABLE%>") {
				document.getElementById("Dibutton").innerHTML = document.getElementById("Dienablebutton").innerHTML;
			} else {
				document.getElementById("Enbutton").innerHTML = document.getElementById("Enenablebutton").innerHTML;
			}
			if(jobStatus.value == "<%=SwtConstants.JOB_STATUS_CLOSING_TEXT%>") {
				document.getElementById("changebutton").innerHTML = document.getElementById("changedisablebutton").innerHTML;
				document.getElementById("removebutton").innerHTML = document.getElementById("removedisablebutton").innerHTML;
			}

		}
	}
	setTimeout("refreshStatus()",10000); 
	//enable or disable button based on the menu, entity access levels
	if(menuEntityCurrGrpAccess == "0") {
		document.getElementById("addjobbutton").innerHTML = document.getElementById("addenablebutton").innerHTML;
	} else {
		document.getElementById("addjobbutton").innerHTML = document.getElementById("adddisablebutton").innerHTML;
	}	
	document.forms[0].menuAccessId.value = "${requestScope.menuAccessId}";
	document.getElementById("lastRefTime").innerText = lastRefTime.replace('Server time','<s:text name="alert.serverTime"/>');
}
function refreshStatus()
{
    var appName = "<%=SwtUtil.appName%>";
    var requestURL = new String('<%=request.getRequestURL()%>');
    var idy = requestURL.indexOf('/'+appName+'/');
    requestURL=requestURL.substring(0,idy+1) ;
    requestURL = requestURL + appName+"/scheduler.do?method=getJobStatus";
    requestURL = requestURL + "&scheduleId=";
    var table = document.getElementById("jobDetails");
    var tbody = table.getElementsByTagName("tbody")[0];
    var rows = tbody.getElementsByTagName("tr");
    var scheduleId;
    for (i=0; i < rows.length; i++) {
     var oldstatus=rows[i].getElementsByTagName("input")[1].value;
     jobid=rows[i].getElementsByTagName("input")[0].value;
     scheduleId=(rows[i].cells[0].childNodes[0].data).trim(); 
     if(oldstatus!="<%=SwtConstants.JOB_STATUS_DISABLE%>")
	 {
     execTime=rows[i].getElementsByTagName("td")[2].childNodes[0].data;
     var newStatus= send_request(requestURL+scheduleId+"&LastExecTime="+execTime);
     if(newStatus!="@false"){
        if(newStatus=="@true"){
            autorefresh();
        }
        else if(oldstatus!=newStatus){  
            if(rows[i].getElementsByTagName("input")[2].value!="<%=SwtConstants.JOB_TYPE_MANUAL_DESC%>"){      
             rows[i].getElementsByTagName("input")[1].value=newStatus;
             autorefresh();
            }
            else if(newStatus!="<%=SwtConstants.JOB_STATUS_PENDING_TEXT%>"){
            rows[i].getElementsByTagName("input")[1].value=newStatus;
            autorefresh();
            }
        }
     }
	 }
    }
    setTimeout("refreshStatus()",10000); 
}
function send_request(requestURL){
            var oXMLHTTP = new XMLHttpRequest();
            oXMLHTTP.open( "POST", requestURL,false);
            oXMLHTTP.setRequestHeader("Pragma", "no-cache")
            oXMLHTTP.send();
            return  new String(oXMLHTTP.responseText);
}

function changeRecord()
{
    document.forms[0].changeButtonPressed.value = "Y";
    submitForm('display');
}

function clearJobId()
{
document.forms[0].selectedjobId.value="";
updateColors();
maintainSortFilterStatus();
document.getElementById("changebutton").innerHTML = document.getElementById("changedisablebutton").innerHTML;
document.getElementById("executebutton").innerHTML = document.getElementById("executedisablebutton").innerHTML;
document.getElementById("Enbutton").innerHTML = document.getElementById("Endisablebutton").innerHTML;
document.getElementById("Dibutton").innerHTML = document.getElementById("Didisablebutton").innerHTML;
document.getElementById("removebutton").innerHTML = document.getElementById("removedisablebutton").innerHTML;
maintainSortFilterStatus();
}
function autorefresh()
{
isDoNotCloseMyChilds= true;
submitForm('display');
}

function maintainSortFilterStatus(){

    try {
        var sortColumn = xl.dataTable.sortColumn;
        document.forms[0].selectedSortStatus.value = sortColumn;
        var sSortDescending = xl.dataTable.descending;
        document.forms[0].selectedSortDescending.value = sSortDescending;
        var filterArr = new Array(9);
        if (document.getElementById("jobDetails").innerText != "")
            filterArr = xl.getFilterStatus();

        for (var idy = 0; idy < filterArr.length - 1; ++idy)
            document.forms[0].selectedFilterStatus.value += filterArr[idy] + ",";
    }catch (e) {
        console.log(e);
    }
}

function submitForm(methodName){

	document.getElementById("refreshbutton").innerHTML = document.getElementById("refreshdisablebutton").innerHTML;
    isDoNotCloseMyChilds= true;
    maintainSortFilterStatus();
    document.forms[0].gridScrollTop.value = document.getElementById('ddscrolltable').scrollTop;;
    document.forms[0].method.value = methodName;
    if (scheduledJobType == 'B') {
	    var e = document.getElementById("jobTypeList");
	    var selectedScheduledJobType = e.options[e.selectedIndex].value;
	    document.forms[0].selectedScheduledJobType.value = selectedScheduledJobType;
    }
    document.forms[0].scheduledJobType.value = scheduledJobType;
    document.forms[0].submit();
    
}

function submitFormFromAddJob(methodName, selectedSchedJobType){

	document.getElementById("refreshbutton").innerHTML = document.getElementById("refreshdisablebutton").innerHTML;
    isDoNotCloseMyChilds= true;
    maintainSortFilterStatus();
    document.forms[0].method.value = methodName;
    if (scheduledJobType == 'B') {
	    document.forms[0].selectedScheduledJobType.value = selectedSchedJobType;
    }
    document.forms[0].scheduledJobType.value = scheduledJobType;
    document.forms[0].submit();
    
}
/**
* submitExecute()
* 
* This method used to while click the execute to call this method and submit the data
*
* @param methodname
* @return none
*/	
function submitExecute(methodName) { 
	
	isDoNotCloseMyChilds= true;
	
    document.forms[0].method.value = methodName;    
    var yourstate=window.confirm('<s:text name="batchScheduler.confirm.execute"/>');
    if (yourstate==true){
		document.getElementById("refreshbutton").innerHTML = document.getElementById("refreshdisablebutton").innerHTML;
	
		maintainSortFilterStatus();
		if (scheduledJobType == 'B') {
		    var e = document.getElementById("jobTypeList");
		    var selectedScheduledJobType = e.options[e.selectedIndex].value;
		    document.forms[0].selectedScheduledJobType.value = selectedScheduledJobType;
		}
		document.forms[0].scheduledJobType.value = scheduledJobType;
        document.forms[0].submit();
    }
}
function onSelectTableRow(rowElement, isSelected)
{
    var jobStatus  = rowElement.getElementsByTagName("input")[1];   
    var jobType  = rowElement.cells[6].getElementsByTagName("input")[0];    
    var hiddenElement = rowElement.getElementsByTagName("input")[0];
    document.forms[0].selectedjobId.value = hiddenElement.value;
    document.forms[0].selectedScheduleId.value = (rowElement.cells[0].childNodes[0].data).trim();
    document.forms[0].selectedJobType.value = rowElement.getElementsByTagName("input")[4].value;
	if(menuEntityCurrGrpAccess=="0" && isSelected)
    {      
          if(jobStatus.value == "<%=SwtConstants.JOB_STATUS_CLOSING_TEXT%>")
          {
           document.getElementById("changebutton").innerHTML = document.getElementById("changedisablebutton").innerHTML;
           document.getElementById("removebutton").innerHTML = document.getElementById("removedisablebutton").innerHTML;
          }
          else
          {
           document.getElementById("changebutton").innerHTML = document.getElementById("changeenablebutton").innerHTML;
           document.getElementById("removebutton").innerHTML = document.getElementById("removeenablebutton").innerHTML;
          }
          document.getElementById("Enbutton").innerHTML = document.getElementById("Endisablebutton").innerHTML;
          document.getElementById("Dibutton").innerHTML = document.getElementById("Didisablebutton").innerHTML;     
          document.getElementById("executebutton").innerHTML = document.getElementById("executedisablebutton").innerHTML;
         
          if(jobType.value == "<%=SwtConstants.JOB_TYPE_MANUAL_DESC%>")
          {
           if (jobStatus.value != "<%=SwtConstants.JOB_STATUS_DISABLE%>")
             if(jobStatus.value != "<%=SwtConstants.JOB_STATUS_CLOSING_TEXT%>")
             document.getElementById("executebutton").innerHTML = document.getElementById("executeenablebutton").innerHTML;
          }
         if(jobStatus.value != "<%=SwtConstants.JOB_STATUS_DISABLE%>")
          {
             if(jobStatus.value != "<%=SwtConstants.JOB_STATUS_CLOSING_TEXT%>")
             {
              document.getElementById("Dibutton").innerHTML = document.getElementById("Dienablebutton").innerHTML;
              }
         }else
         {
            document.getElementById("Enbutton").innerHTML = document.getElementById("Enenablebutton").innerHTML;
         }
    }else
    {   document.forms[0].selectedjobId.value="";
        document.getElementById("changebutton").innerHTML = document.getElementById("changedisablebutton").innerHTML;
        document.getElementById("executebutton").innerHTML = document.getElementById("executedisablebutton").innerHTML;
        document.getElementById("Enbutton").innerHTML = document.getElementById("Endisablebutton").innerHTML;
        document.getElementById("Dibutton").innerHTML = document.getElementById("Didisablebutton").innerHTML;
        document.getElementById("removebutton").innerHTML = document.getElementById("removedisablebutton").innerHTML;
    }
}

/**
 * reportsByTheJob
 *
 * This method is called when selecting a row and is used to  
 * obtain the reports created with the corresponding job
 **/
function reportsByTheJob() {
	var selectedJobId = document.forms[0].selectedjobId.value;
	var selectedScheduleId = document.forms[0].selectedScheduleId.value;
	if (selectedScheduleId != null) {
		var requestURL = new String('<%=request.getRequestURL()%>');
		var appName = "<%=SwtUtil.appName%>"; //Application name is picked up from property file
		var idy = requestURL.indexOf('/'+appName+'/');
		requestURL = requestURL.substring(0, idy+1);
		requestURL = requestURL + appName + "/scheduler.do?method=getreportsJobForAjax";
		requestURL = requestURL + "&selectedJobId=" + selectedJobId;
		requestURL = requestURL + "&selectedScheduleId=" + selectedScheduleId;
		var oXMLHTTP = new XMLHttpRequest();
		oXMLHTTP.open( "POST", requestURL, false );
		oXMLHTTP.send();
		document.forms[0].selectedScheduleReports.value= oXMLHTTP.responseText;
	}

}



function jobEnable(methodName)
{
    var yourstate=window.confirm('<s:text name="batchScheduler.confirm.enable"/>');
    if (yourstate==true){ 
	
document.getElementById("refreshbutton").innerHTML = document.getElementById("refreshdisablebutton").innerHTML;

	maintainSortFilterStatus();
    document.forms[0].enableOrDisable.value = "Enable";
    document.forms[0].method.value = "enableOrDisableJob";
    if (scheduledJobType == 'B') {
	    var e = document.getElementById("jobTypeList");
	    var selectedScheduledJobType = e.options[e.selectedIndex].value;
	    document.forms[0].selectedScheduledJobType.value = selectedScheduledJobType;
	}
	document.forms[0].scheduledJobType.value = scheduledJobType;
    document.forms[0].submit();
    }
	}

function jobDisable(methodName)
{

    var yourstate=window.confirm('<s:text name="batchScheduler.confirm.disable"/>');
    if (yourstate==true){ 

document.getElementById("refreshbutton").innerHTML = document.getElementById("refreshdisablebutton").innerHTML;


	maintainSortFilterStatus();
    document.forms[0].enableOrDisable.value = "Disable";
    document.forms[0].method.value = "enableOrDisableJob";
    if (scheduledJobType == 'B') {
	    var e = document.getElementById("jobTypeList");
	    var selectedScheduledJobType = e.options[e.selectedIndex].value;
	    document.forms[0].selectedScheduledJobType.value = selectedScheduledJobType;
	}
	document.forms[0].scheduledJobType.value = scheduledJobType;
    document.forms[0].submit();
    }
	}
</SCRIPT>

</head>
<body onmousemove="reportMove()" leftmargin="0" topmargin="0" marginheight="0" onLoad="bodyOnLoad();setParentChildsFocus();setFocus(document.forms[0]);" onunload="call()">
<s:form action="scheduler.do">
<input name="method" type="hidden" value="display">
<input name="gridScrollTop" type="hidden" value="">
<input name="selectedScheduleReports" type="hidden" value="">
<input name="selectedjobId" type="hidden" value="GA">
<input name="selectedScheduleId" type="hidden" value="">
<input name="selectedJobType" type="hidden" value="">
<input name="selectedcurrentStatus" type="hidden" value="GA">
<input name="exec" type="hidden" value= "${exec}">
<input name="term" type="hidden" value= "${term}">
<input name="changeButtonPressed" type="hidden" value= "">
<input name="enableOrDisable" type="hidden" value= "">
<input name="jobstatus" type="hidden" value= "">


<input name="selectedFilterStatus" type="hidden" value="">
<input name="selectedSortDescending" type="hidden" value="">
<input name="selectedSortStatus" type="hidden" value="">
<input name="scheduledJobType" type="hidden" value="">
<input name="selectedScheduledJobType" type="hidden" value="">

<input name="menuAccessId" type="hidden" >

<div id="EntityMaintenance"
		style="position: absolute; left: 20px; top: 20px; width:1215px; height: 40px; border: 2px outset;"
		color="#7E97AF">
	<div id="EntityMaintenance"
		style="position: absolute; left: 8px; top: 4px; width: 1215px; height: 420;">
	<table width="300px" border="0" cellpadding="0" cellspacing="0"
		height="30">
		<tr>
		
			<s:if test='"R" == #request.scheduledJobType' >
				<td width="50px"><b><s:text name="batchScheduler.scheduledJobType"/> :</b>&nbsp;&nbsp;&nbsp;Report</td>
			</s:if>
			<s:if test='"P" == #request.scheduledJobType' >
				<td width="50px"><b><s:text name="batchScheduler.scheduledJobType"/> :</b>&nbsp;&nbsp;&nbsp;Process</td>
			</s:if>
			<s:if test='"B" == #request.scheduledJobType' >
			<td width="120px"><b><s:text name="batchScheduler.scheduledJobType"/></b></td>
			<td width="120">
				<select id="jobTypeList" onchange="submitForm('display')"> 
					<s:iterator value="#request.jobTypeList" var="type" >
						<option value="<s:property value="#type.value" />"
							<s:if test="#type.value == #request.selectedScheduledJobType">selected="selected"</s:if>>
							<s:property value="#type.label" />
						</option>
					</s:iterator> 
				</select> 
			</td>
			</s:if>
					
		</tr>
	</table>
	</div>
	</div>

<div id="SchedulerMaintenance" color="#7E97AF" style="position:absolute; border-left:2px outset; left:20px; top:70px; width:1215px; height:435;">
<div id="SchedulerMaintenance" style="position:absolute;z-index:99;left:0px; top:0px; width:1192px; height:10px;">
  <table  class="sort-table" id="table_2" bgcolor="#B0AFAF" width="1221px" border="0" cellspacing="1" cellpadding="0"  height="23">
    <thead>
        <tr>
         <td  width="45"  style="border-left-width: 0px;" height="20px" align="left" title='<s:text name="tooltip.sortScheduledId"/>'><b><s:text name="batchScheduler.header.scheduledId"/></b></td>
            <td  width="290" height="20px" align="left" title='<s:text name="tooltip.sortJobName"/>'><b><s:text name="batchScheduler.header.jobName"/></b></td>
            <td  width="160" height="20px" align="middle" title='<s:text name="tooltip.sortLastExeTime"/>'><b><s:text name="batchScheduler.header.LastExe"/> </b></td>
            <td  width="150" height="20px" align="left" title='<s:text name="tooltip.sortLastExeStatus"/>'><b><s:text name="batchScheduler.header.LastExeStatus"/></b></td>
            <td  width="160" height="20px" align="middle" title='<s:text name="tooltip.sortNextExeStatus"/>'><b><s:text name="batchScheduler.header.nextExeTime"/></b></td>      
            <td  width="140" height="20px" align="left" title='<s:text name="tooltip.sortBycurrentStatus"/>'><b><s:text name="tooltip.currentstatus"/></b></td>
            <td  width="105" height="20px" align="left" title='<s:text name="tooltip.sortByFrequency"/>'><b><s:text name="addJob.title.frequency"/></b></td>
            <td  width="180" height="20px" align="left" title='<s:text name="tooltip.sortByjobDetail"/>'><b><s:text name="tooltip.jobdetail"/></b></td>

        </tr>
    </thead>
 </table>
 </div>
<div id="ddscrolltable"  style="position:absolute; left:0px; top:2px; width:1210px; height:430;overflow:scroll">
<div id="SchedulerMaintenance" style="position:absolute;z-index:99;left:0px; top:20px; width:1190px; height:10px;">

<table class="sort-table" id="jobDetails" width="1219px" border="0" cellspacing="1" cellpadding="0" height="393">
    <tbody> 
    <%int count = 0; %>  
    <s:iterator value="#request.jobDetails" var="jobDetails" >          
        <% if( count%2 == 0 ) {%><tr  class="even"><% }else  { %> <tr  class="odd"> <%}++count; %>
            
            <s:hidden name="#jobDetails.jobId"/>
            <s:if test='"D" == #jobDetails.jobStatus' >
                <td width="45" style="text-align:right;color:red"><s:property value="#jobDetails.scheduleId" />&nbsp;</td>
                <td width="290" style="color:red" title="<s:property value="#jobDetails.job.jobDescription" />"><s:property value="#jobDetails.job.jobDescription" />&nbsp;</td>
                <td width="160" style="color:red"><s:property value="#jobDetails.lastExecTimeAsString" />&nbsp;</td>
                <td width="158" style="color:red"><s:property value="#jobDetails.lastExecStatusName" />&nbsp;</td>
                <td width="160" style="color:red">&nbsp;</td>
                <td width="140" style="color:red"><s:property value="#jobDetails.currentStatusName" />&nbsp;</td><s:hidden name="#jobDetails.currentStatusName"/>
                <td width="105" style="color:red" ><s:hidden name="#jobDetails.jobTypeFullDescription"/><s:property value="#jobDetails.jobTypeFullDescription" />&nbsp;</td>
                <td width="200" style="color:red" ><s:hidden name="#jobDetails.jobDetails"/><s:property value="#jobDetails.jobDetails" />&nbsp;</td>
            </s:if>
            <s:if test='"D" != #jobDetails.jobStatus' >
                <td width="45" style="text-align:right"><s:property value="#jobDetails.scheduleId" />&nbsp;</td>
                <td width="290"  title="<s:property value="#jobDetails.job.jobDescription" />"><s:property value="#jobDetails.job.jobDescription" />&nbsp;</td>
                <td width="160"><s:property value="#jobDetails.lastExecTimeAsString" />&nbsp;</td>
                <td width="158"><s:property value="#jobDetails.lastExecStatusName" />&nbsp;</td>
                <td width="160"><s:property value="#jobDetails.nextExecTimeAsString" />&nbsp;</td>
                <td width="140"><s:property value="#jobDetails.currentStatusName" />&nbsp;</td><s:hidden name="#jobDetails.currentStatusName"/>
                <td width="105"><s:hidden name="#jobDetails.jobTypeFullDescription"/><s:property value="#jobDetails.jobTypeFullDescription" />&nbsp;</td>
                <td width="200"><s:hidden name="#jobDetails.jobDetails"/><s:property value="#jobDetails.jobDetails" />&nbsp;</td>
   			</s:if>
            <s:hidden name="#jobDetails.jobTypeProcessOrReport"/>
        </tr>
    </s:iterator>
    </tbody>
<tfoot><tr><td colspan="8" ></td></tr></tfoot>
</table>
</div>
</div>
</div>
  <!--Start Code modified by Chidambaranathan for Mantis_1373 for tab Navigation on 01-July-2011-->

<div id="SchedulerMaintenance" style="position:absolute; left:1170px; top:518px; width:70px; height:39px; visibility:visible;">
    <table width="60px" border="0" cellspacing="0" cellpadding="0" height="20">
        <tr>

          <td align="Right">
                <a title='<s:text name="tooltip.helpScreen"/>' tabindex="7" href=# onclick="javascript:openWindow(buildPrintURL('print','Scheduler '),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')" onMouseOut="MM_swapImgRestore()" onMouseOver="MM_swapImage('Help','','images/help_default.GIF ',1)"><img src="images/help_default.GIF " name="Help"  border="0" ></a> 
        </td>
            <td align="right" id="Print">
                <a tabindex="7" onclick="printPage();" onKeyDown="submitEnter(this,event)"onMouseOut="MM_swapImgRestore()" onMouseOver="MM_swapImage('Print','','images/Print_R.gif ',1)"><img src="images/Print.gif " name="Print"  border="0" title='<s:text name="tooltip.printScreen"/>'></a>   
            </td>
        </tr>
    </table>
</div>

       
<div id="ddimagebuttons" color="#7E97AF" style="position:absolute; border:2px outset; left:20; top:510; width:1217px; height:39px; visibility:visible;">
  <div id="scheduler" style="position:absolute; left:6; top:4; width:1089; height:15px; visibility:visible;">
  <table width="1150" border="0" cellspacing="0" cellpadding="0" height="20">
        <tr>

            <td id="refreshbutton" width="70px">        
            <a  title='<s:text name="tooltip.refreshJobDetail"/>' tabindex="1" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)"     onMouseUp="highlightbutton(this)" onKeyDown="submitEnter(this,event)" onClick="javascript:submitForm('display')"><s:text name="button.Refresh"/></a>  
			</td>       
            <td id="executebutton" width="70px">        
            </td>
            <td id="Enbutton" width="70px"> 
            <td id="Dibutton" width="70px"> 
            <td id="addjobbutton" width="70px"> 
			  <a title='<s:text name="tooltip.addJob"/>' tabindex="3" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onClick="javascript:openWindow(buildJobMaintenance('add'),'','left=50,top=190,width=650,height=545,toolbar=0,status=yes, resizable=yes, scrollbars=yes','true');"><s:text name="button.add"/></a>
             </td>      
             <td id="changebutton" width="70px">        
            </td>
            <td id="removebutton" width="70px">     			
            </td>
            <s:if test='"R" != #request.scheduledJobType' >
				<td id="entitybutton" width="70px">      
                <a title='<s:text name="tooltip.entityProcess"/>' tabindex="6" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onclick="javascript:openWindow(buildEntityProcess(),'','left=50,top=190,width=1240,height=533,toolbar=0,status=yes, resizable=yes, scrollbars=yes','true');"><s:text name="button.entityProcess"/></a>

            </td>
			</s:if>
		    <td id="closebutton" width="70px">      
                <a title='<s:text name="tooltip.close"/>' tabindex="6" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onKeyDown="submitEnter(this,event)" onclick="confirmClose('P');"><s:text name="button.close"/></a>
            </td>
			<td width="200">
				&nbsp;
			</td>			
			<td id="lastRefTimeLable" width="100" align="right"  style="padding-top: 4px;">
                                                          <s:text name="label.lastRefTime2"/>&nbsp;
			</td>			
			<td id="lastRefTime"  width="275"  style="padding-top: 4px;">
			<input class="textAlpha" style="background:transparent;border: 0;" tabindex="-1" readonly name="maxPageNo" value="" size="14">	
			</td>
        </tr>
	</table>
    </div>
	
<div style="position:absolute; left:6; top:4; width:937; height:15px; visibility:hidden;">      
    <table width="560" border="0" cellspacing="0" cellpadding="0" height="20" style="visibility:hidden">
    <tr>
    
		<td id="refreshdisablebutton">
			<a  class="disabled"  disabled="disabled"><s:text name="button.Refresh"/></a>
		</td>
		
		<td id="addenablebutton">       
            <a title='<s:text name="tooltip.addJob"/>' tabindex="3" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onKeyDown="submitEnter(this,event)" onClick="javascript:openWindow(buildJobMaintenance('add'),'','left=50,top=190,width=650,height=545,toolbar=0,status=yes, resizable=yes, scrollbars=yes','true');"><s:text name="button.add"/></a>      
        </td>       
        <td id="adddisablebutton">
            <a  class="disabled" disabled="disabled"><s:text name="button.add"/></a>
        </td>        
		<td id="executeenablebutton">       
            <a title='<s:text name="tooltip.exeJob"/>' tabindex="2" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onKeyDown="submitEnter(this,event)" onClick="javascript:submitExecute('exec');"><s:text name="button.execute"/></a>
        </td>       
        <td id="executedisablebutton">
            <a  class="disabled" disabled="disabled"><s:text name="button.execute"/></a>
        </td>
    
        <td id="Enenablebutton">        
            <a title='<s:text name="tooltip.enable"/>' tabindex="2" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)"onKeyDown="submitEnter(this,event)" onClick="javascript:jobEnable('enableJob');"><s:text name="button.enable"/></a>
        </td>       
        <td id="Endisablebutton">
            <a  class="disabled" disabled="disabled"><s:text name="button.enable"/></a>
        </td>
        
        <td id="Dienablebutton">        
            <a title='<s:text name="tooltip.disable"/>' tabindex="2" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onKeyDown="submitEnter(this,event)" onClick="javascript:jobDisable('disableJob');"><s:text name="button.disable"/></a>
        </td>       
        <td id="Didisablebutton">
            <a  class="disabled" disabled="disabled"><s:text name="button.disable"/></a>
        </td>


        <td id="changeenablebutton">        
            <a   title='<s:text name="tooltip.changeJob"/>' tabindex="4" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)"onKeyDown="submitEnter(this,event)" onClick="changeRecord()"><s:text name="button.change"/></a>

        </td>       
        <td id="changedisablebutton">
            <a  class="disabled" disabled="disabled"><s:text name="button.change"/></a>
        </td>

        <td id="removeenablebutton">        
            <a   title='<s:text name="tooltip.removeJob"/>' tabindex="5" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onKeyDown="submitEnter(this,event)" onClick="javascript:submitDeleteForm('SchedulerDelete');"><s:text name="button.remove"/></a>
        </td>       
        <td id="removedisablebutton">
            <a  class="disabled" disabled="disabled"><s:text name="button.remove"/></a>
        </td>
    </tr>
    </table>
  </div>
</div>
<blockquote>&nbsp;</blockquote>
        <p>&nbsp;</p>
<script type="text/javascript">
</script>
</s:form>
</body>
</html>