<!doctype html>
<%@ include file="/taglib.jsp"%>
<%@ page import="org.swallow.util.SwtConstants"%>
<html lang="en">
<head>
<meta charset="utf-8">
<title><s:if test='"add" == #request.screenName' >
	Events to launch Add
</s:if> <s:else>
		<s:if test='"change" == #request.screenName' >
		Events to launch Change
	</s:if>
		
	<s:if test='"view" == #request.screenName' >
		Events to launch View
	</s:if>
	
	</s:else></title>
<!-- <base href="./"> -->
<meta name="viewport" content="width=device-width, initial-scale=1">
<link rel="icon" type="image/x-icon" href="favicon.ico">
<link rel="stylesheet" href="angularSources/styles.css?version=<%= SwtUtil.appVersion %>">
</head>
<body>
<%@ include file="/angularJSUtils.jsp"%>

<script type="text/javascript">
var appName = "<%=SwtUtil.appName%>";
var baseURL = new String('<%=request.getRequestURL()%>');
var screenRoute = "eventsSub";

function openMsgFormatScreen(methodName,scenarioId){
	var param = '/' + appName + '/messageformats.do?method='+methodName;
	param += '&isScenarioFormat='+"Y";
	param += '&from='+"eventsSub";
	param += '&scenarioId='+ scenarioId;
	var 	mainWindow = openWindow (param, 'ScenMsgFormat','left=10,top=230,width=1020,height=500,toolbar=0, resizable=yes, //status=yes, scrollbars=yes');	
	return false;
}

function getMsgFormats(){
Main.updateMsgFormatCombo();
}

 </script>
<%@ include file="/angularscripts.jsp"%>
</body>
</html>