    <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@ include file="/taglib.jsp"%>
<%@ page import="org.swallow.util.SwtConstants" %>
<%@page import="org.swallow.control.model.Section"%>
<html>
<head>
<title><s:text name="section.title.window"/> </title>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">

<SCRIPT language="JAVASCRIPT">

<s:if test="%{parentFormRefresh.equals('yes')}">
window.opener.document.forms[0].method.value="displayList";
window.opener.document.forms[0].submit();
self.close();
</s:if>
// </logic:equal>

var menuEntityCurrGrpAccess = "${requestScope.menuEntityCurrGrpAccess}";


var entityAccess = "0";
/*Added to apply style on mandatory field ,if it's equal to undefined then no field will be colored*/
 mandatoryFieldsArray= "undefined" ;

function onFilterandSort()
{
	 
	updateColors();
	disableAllButtons();
}
function disableAllButtons()
{
	 
	document.getElementById("changebutton").innerHTML = document.getElementById("changedisablebutton").innerHTML;
	document.getElementById("deletebutton").innerHTML = document.getElementById("deletedisablebutton").innerHTML; 
}

function bodyOnLoad()
{
	
	var tableElm = document.getElementById("sectionCollTable");
	
	xl = new XLSheet("sectionCollTable","table_2", ["String","String"],"22");
	xl.onsort = xl.onfilter = onFilterandSort;
	highlightTableRows("sectionCollTable");
	 
	document.getElementById("addbutton").innerHTML = document.getElementById("adddisablebutton").innerHTML;
	document.getElementById("changebutton").innerHTML = document.getElementById("changedisablebutton").innerHTML;
	document.getElementById("deletebutton").innerHTML = document.getElementById("deletedisablebutton").innerHTML; 

	 <%if (request.getAttribute(SwtConstants.ADD_BUT_STS).equals(SwtConstants.STR_TRUE)) {%>
		document.getElementById("addbutton").innerHTML = document.getElementById("addenablebutton").innerHTML;
 <%}else{ %>

		document.getElementById("addbutton").innerHTML = document.getElementById("adddisablebutton").innerHTML;
	<%}%>

	
	 <%if (request.getAttribute(SwtConstants.CHG_BUT_STS).equals(SwtConstants.STR_TRUE)) {%>
		document.getElementById("changebutton").innerHTML = document.getElementById("changeenablebutton").innerHTML;
 <%}else{ %>

		document.getElementById("changebutton").innerHTML = document.getElementById("changedisablebutton").innerHTML;
	<%}%>

	
	 <%if (request.getAttribute(SwtConstants.DEL_BUT_STS).equals(SwtConstants.STR_TRUE)) {%>
		document.getElementById("deletebutton").innerHTML = document.getElementById("deleteenablebutton").innerHTML;
 <%}else{ %>

		document.getElementById("deletebutton").innerHTML = document.getElementById("deletedisablebutton").innerHTML;
	<%}%>
		
	if(menuEntityCurrGrpAccess == "0")
	{
		document.getElementById("addbutton").innerHTML = document.getElementById("addenablebutton").innerHTML;
	}else{
		document.getElementById("addbutton").innerHTML = document.getElementById("adddisablebutton").innerHTML;
	}	
	
	document.forms[0].menuAccessId.value = "${requestScope.menuAccessId}";
	
}

function submitForm1(methodName1){
document.forms[0].method1.value = methodName1;
var yourstate=window.confirm('<s:text name="confirm.delete"/>');
	if (yourstate==true){  
	document.forms[0].submit();
	}
}
function submitForm(methodName){
document.forms[0].method.value = methodName;
document.forms[0].submit();
}
	
function buildChangeSectionURL(methodName){	
var param = 'section.do?method='+methodName+'&sectionId=';
	param +=document.forms[0].selectedSectionId.value;	
	 param +='&sectionName=';
	param +=document.forms[0].selectedSectionName.value;	

	return  param;
}
function onSelectTableRow(rowElement, isSelected)
{
 
	if(rowElement && rowElement.cells.length == 2)
	{
		var cell1 = rowElement.cells[0];
		var cell2 = rowElement.cells[1];
		
		var hiddenEl1 = cell1.getElementsByTagName("input")[0];
		var hiddenEl2 = cell2.getElementsByTagName("input")[0];
	
	}
	
	var hiddenElement = rowElement.getElementsByTagName("input")[0];
	document.forms[0].selectedSectionId.value = hiddenElement.value;
	document.forms[0].selectedSectionName.value = hiddenEl2.value;
	 
	
	if(menuEntityCurrGrpAccess=="0" && isSelected)
	
	{
	document.getElementById("changebutton").innerHTML = document.getElementById("changeenablebutton").innerHTML;
	document.getElementById("deletebutton").innerHTML = document.getElementById("deleteenablebutton").innerHTML;
	}
	else
	{
	document.getElementById("changebutton").innerHTML = document.getElementById("changedisablebutton").innerHTML;
	document.getElementById("deletebutton").innerHTML = document.getElementById("deletedisablebutton").innerHTML;
	}
	
	}
	function buildAddSectionURL(methodName){
	var param = 'section.do?method='+methodName;
	return param;
	}
function submitFormDelete(methodName){
	document.forms[0].method.value = methodName;
	
	var yourstate=window.confirm('<s:text name="confirm.delete"/>');
	if (yourstate==true){ 
	
	document.forms[0].submit();
	
	}
}
</SCRIPT>
</head>
<body onmousemove="reportMove()" leftmargin="0" topmargin="0" marginheight="0" onLoad="bodyOnLoad();setParentChildsFocus();setFocus(document.forms[0]);ShowErrMsgWindow('${actionError}');" onunload="call()">

<s:form action="section.do" >
<input name="method" type="hidden" value="displayList">
<input name="method1" type="hidden" value="displayList">
<input name="selectedSectionId" type="hidden" >
<input name="selectedSectionName" type="hidden">

<input name="menuAccessId" type="hidden" >


<div color="#7E97AF" style="position:absolute; border-left:2px outset; left:20px; top:20px; width:455px; height:430px;">
<div id="SectionMaintenance" style="position:absolute;z-index:99; left:0px; top:0px; width:432px; height:10px;">
<table  class="sort-table" id="table_2" bgcolor="#B0AFAF" width="433px" border="0" cellspacing="1" cellpadding="0" height="20">
	<thead>
		<tr>
			<td valign="middle" height="20px"  style="border-left-width: 0px;"title='<s:text name="tooltip.sortSection"/>'width="143px"><b><s:text name="section.sectionId"/></b></td>
			<td  valign="middle" height="20px" title='<s:text name="tooltip.sortname"/>' width="280px"><b><s:text name="section.sectionName"/></b></td>
		</tr>
	</thead>
</table>
</div>

<div id="ddscrolltable"  style="position:absolute; left:0px; top:0px; width:450px; height:425px">
<div id="ScreenMaintenance" style="position:absolute;left:0px; top:21px; width:402px; height:10px;">
<table class="sort-table" id="sectionCollTable" width="432" border="0" cellspacing="1" cellpadding="0" height="404">
 <tbody> 
	<%int count = 0; %>  
	 <s:iterator value="#request.sectionColl">           
		<% if( count%2 == 0 ) {%><tr class="even"><% }else  { %> <tr class="odd"> <%}++count; %>
		<!-- Start:Code added by Nageswara Rao on 03-Jan-2012 for mantis 1580:"Spaces should not be saved to end of input values ." -->
		<td width="143px"> <s:hidden name="id.sectionId" /><s:property value="id.sectionId" />&nbsp;</td>
			<td width="280px"><s:hidden name="sectionName" /><s:property value="sectionName" escapeXml="false"  />&nbsp;</td>
		<!-- End:Code added by Nageswara Rao on 03-Jan-2012 for mantis 1580:"Spaces should not be saved to end of input values ." --> 
		</tr>
  </s:iterator>
	</tbody>
<tfoot><tr><td colspan="2"  width="430px"></td></tr></tfoot>
</table>
</div>
</div>
</div>

<div id="sectionMain" style="position:absolute ;left:400; top:464px; width:70px; height:39px; visibility:visible;">
	<table width="60" border="0" cellspacing="0" cellpadding="0" height="20">
		<tr>

		<td align="Right">
				<a  tabindex="6" href=# onclick="javascript:openWindow(buildPrintURL('print','Section Setup'),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')" onMouseOut="MM_swapImgRestore()" onMouseOver="MM_swapImage('Help','','images/help_default.GIF ',1)"><img src="images/help_default.GIF " name="Help"  border="0" title='<s:text name="tooltip.helpScreen"/>' ></a> 
		</td>

			<td align="right" id="Print">
				<a tabindex="6" onclick="printPage();" onMouseOut="MM_swapImgRestore()" onMouseOver="MM_swapImage('Print','','images/Print_R.gif ',1)"><img src="images/Print.gif " name="Print"  border="0" title='<s:text name="tooltip.printScreen"/>'></a>	
			</td>
		</tr>
	</table>
</div>



<div id="ddimagebuttons" color="#7E97AF" style="position:absolute; border:2px outset; left:20; top:455; width:451; height:39px; visibility:visible;">
  <div id="SectionMaintenance" style="position:absolute; left:6; top:4; width:425; height:15px; visibility:visible;">
  <table width="280" border="0" cellspacing="0" cellpadding="0" height="20">
		<tr>
			<td id="addbutton">		
			</td>
			<td id="changebutton">		
			</td>
			<td id="deletebutton">		
			</td>
			<td title='<s:text name="tooltip.close"/>'id="closebutton">		
				<a tabindex="4" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onclick="confirmClose('P');"><s:text name="button.close"/></a>			
			</td>
		</tr>
		</table>
	</div>
<div style="position:absolute; left:6; top:4; width:425; height:15px; visibility:hidden;">  	
    <table width="280" border="0" cellspacing="0" cellpadding="0" height="20" style="visibility:hidden">
	<tr>
		<td id="addenablebutton">	
		<a  tabindex="1" title='<s:text name="tooltip.addSection"/>'onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onClick="javascript:openWindow(buildAddSectionURL('add'),'sectionaddwindow','left=50,top=190,width=422,height=149,toolbar=0','true')"><s:text name="button.add"/></a>	
		</td>		
		<td id="adddisablebutton">
			
			<a  class="disabled" disabled="disabled"><s:text name="button.add"/></a>
			
		</td>
		
		<td id="changeenablebutton">		
			<a tabindex="2" title='<s:text name="tooltip.changeSelectedSection"/>'onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onClick="javascript:openWindow(buildChangeSectionURL('change'),'sectionchangewindow','left=50,top=190,width=422,height=149,toolbar=0','true')"><s:text name="button.change"/></a>
		</td>		
		<td id="changedisablebutton">
			<a  class="disabled" disabled="disabled" ><s:text name="button.change"/></a>
		</td>

		<td tabindex="3" id="deleteenablebutton">		
			<a title='<s:text name="tooltip.deleteSelectedSection"/>'onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onClick="javascript:submitFormDelete('delete')"><s:text name="button.delete"/></a>
		</td>		
		<td id="deletedisablebutton">
			<a  class="disabled" disabled="disabled"><s:text name="button.delete"/></a>
		</td>						
		
	</tr>
    </table>
  </div>
</div>

<blockquote>&nbsp;</blockquote>
		<p>&nbsp;</p>


<script type="text/javascript">
</script>
</s:form>
</body>
</html>


 