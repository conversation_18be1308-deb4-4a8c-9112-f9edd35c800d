<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@page import="org.swallow.util.PageDetails"%>
<%@ include file="/taglib.jsp"  %>
<%@ page import="org.swallow.util.SwtConstants" %>
<%@ page import="org.swallow.util.CommonDataManager" %>

<html>
<head>
<title><s:text name="ErrorLog.title.window"/></title>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
<script type="text/javascript" src="js/jquery.exportselect.js"></script>
<SCRIPT language="JAVASCRIPT">
/*Start : variable added for Mantis 1262 by Marshal on 03-12-2010*/
var dateFlag=true;
mandatoryFieldsArray = ["errorLogFromDate","errorLogToDate"] ;
/*End : variable added for Mantis 1262 by <PERSON> on 03-12-2010*/
var cancelcloseElements = new Array(1);
	cancelcloseElements[0]="closebutton";
var dateFormat = '<s:property value="#request.session.CDM.dateFormat" />';
/* Start: Code added by venkatesan on 28-mar-2011 for Mantis 1308 :"Show 'Last refresh' time in screens that have refresh function." */
var lastRefTime = "${requestScope.lastRefTime}";
/* End: Code added by venkatesan on 28-mar-2011 for Mantis 1308 :"Show 'Last refresh' time in screens that have refresh function." */
//Start: Modified code by Aliveni for Mantis 837, to use the new page navigation system , on 12-Aug-2010.
var maxPage = "${requestScope.maxPage}";
var currPage = '${requestScope.currentPage}';
//End: Modified code by Aliveni for Mantis 837, to use the new page navigation system , on 12-Aug-2010.
var currentFilter="${requestScope.selectedFilter}";
var currentSort = "${requestScope.selectedSort}";
var filterValues=new Array();
var currentFilterValues = currentFilter.split("|");
var sortingValues = currentSort.split("|");
var sortedValues = new Array();
var totalCount = '${totalCount}';
var dateSelected = false;
sortedValues[0] = sortingValues[0];
sortedValues[1] = sortingValues[1];


function bodyOnLoad(){
	
	xl = new XLSheet("errorLogList","table_2", [dateFormat, "String", "String", "String", "String",],"11111","false",currentFilterValues,sortedValues);
	xl.onsort = xl.onfilter = updateColors;
	xl.dataTable.tBody.style.cursor="";
	/* Start: Code added by venkatesan on 28-mar-2011 for Mantis 1308 :"Show 'Last refresh' time in screens that have refresh function." */
	document.getElementById("lastRefTime").innerText = lastRefTime;
	/* End: Code added by venkatesan on 28-mar-2011 for Mantis 1308 :"Show 'Last refresh' time in screens that have refresh function." */
		var headerData = [];
		var dataprovider = new Array();
			
		var newElement1 = {};
		newElement1[headerData[0]] = 'Pdf';
		dataprovider.push(newElement1);
					
		var newElement2 = {};
		newElement2[headerData[0]] = 'Excel';
		dataprovider.push(newElement2);
					
		var newElement3 = {};
		newElement3[headerData[0]] = 'Csv';
		dataprovider.push(newElement3);
			
	  $("#exportReport").exportselect ({
		    dataprovider: dataprovider,
      	    change: exportReport,
			selectedIndex:0
		  });
}


 function exportReport()
 {var type=$("#exportReport").getSelected(0);
 buildExportErrorsURL('exportErrors',type.toLowerCase());
 }
function submitForm(methodName){
/*  START:Modified the code to Disable the refreshbutton.Modified by Chinniah on 3-Dec-10 for Mantis_1283 */
document.getElementById("refreshbutton").innerHTML = document.getElementById("refreshdisablebutton").innerHTML;
/*  END:Modified the code to Disable the refreshbutton.Modified by Chinniah on 3-Dec-10 for Mantis_1283 */
	
  if(validateForm(document.forms[0])){   
	document.forms[0].method.value = methodName;
	document.forms[0].submit();
	}
}

/*Start : Function added for Mantis 1262 - to validate the date field when clicking on the sort button*/
function sortDateValidation() {
if(validateForm(document.forms[0]) ){
	if(validateField(document.forms[0].elements['errorLog.fromDateAsString'],'auditLog.from',dateFormat) && validateField(document.forms[0].elements['errorLog.toDateAsString'],'auditLog.to',dateFormat)){
			dateFlag = true;
			return dateFlag;
			} else {
			dateFlag = false;
			return dateFlag;
			}
		}
}
/*End : Function added for Mantis 1262 - to validate the date field when clicking on the sort button*/

function onDateKeyPress(obj,e){
	var event = (window.event|| e);
	if(event.keyCode == 9){   // tab
		if(validateField(obj,'To',dateFormat)){
			submitForm('errorLogDetails');
			}
		else
		{
			return false;
			}
	}
	if(event.keyCode == 13){  //enter
		if(validateField(obj,'To',dateFormat)){
			submitForm('errorLogDetails');
			}
		else
		{
			return false;
			}
		}
}
/*Start : Code modified for issues found on V1051 beta testing by Marshal on 21-12-2010*/ 
function buildExportErrorsURL(methodName,exportType) {
	if(validateForm(document.forms[0]) ){
		if(validateField(document.forms[0].elements['errorLog.toDateAsString'],'auditLog.to',dateFormat) && validateField(document.forms[0].elements['errorLog.fromDateAsString'],'auditLog.from',dateFormat)){
			if(comparedates(document.forms[0].elements['errorLog.fromDateAsString'].value,document.forms[0].elements['errorLog.toDateAsString'].value,dateFormat,'From Date','To Date')) {
				var param ='errorlog.do?method='+methodName+'&doExport=no&exportType='+exportType+'&screen='+"<s:text name="ErrorLog.title.window"/>";	
				window.openWindow(param,'','left=50,top=190,width=570,height=215,toolbar=0, resizable=yes scrollbars=yes','true');
				//return  param;
			}
		}
	}
}
/*End : Code modified for issues found on V1051 beta testing by Marshal on 21-12-2010*/ 

function validateForm(objForm){
  var elementsRef = new Array(2);
  elementsRef[0] = objForm.elements["errorLog.fromDateAsString"];
  elementsRef[1] = objForm.elements["errorLog.toDateAsString"];
  if(validate(elementsRef))
  {
    if(comparedates(document.forms[0].elements['errorLog.fromDateAsString'].value,document.forms[0].elements['errorLog.toDateAsString'].value,dateFormat,'From Date','To Date'))
     {
      return true;
     }    
   }
   
   return false;
}	

 /*-Start- Code modified for Defect No. 116 in Mantiss on 03/11/2007 - */

function clickLink(element) {	
/*Start : Variable added for Mantis 1262 - to edit and validate the date field by Marshal on 06-12-2010*/
	dateSelected=false;
/*End : Variable added for Mantis 1262 - to edit and validate the date field by Marshal on 06-12-2010*/
	element.href +='&method=next';
	element.href +='&selectedSort=';
	element.href +=currentSort;
	element.href +='&selectedFilter=';
	element.href +=currentFilter;
	element.href +='&fromDate='
	element.href +=document.forms[0].elements['errorLog.fromDateAsString'].value;
	element.href +='&toDate='
	element.href +=document.forms[0].elements['errorLog.toDateAsString'].value;
	
}

function storeToDate(){
document.forms[0].elements['preErrorToDateAsString'].value = document.forms[0].elements['errorLog.toDateAsString'].value;
}

/*Start : Code modified for Mantis 1262 - to have the date field editable by Marshal on 20-11-2010*/
function onToDateChange(){
	if(dateSelected){

	
	var preToDate = document.forms[0].elements['preErrorToDateAsString'].value;
	var from_date=document.forms[0].elements['errorLog.fromDateAsString'].value;
	var to_date=document.forms[0].elements['errorLog.toDateAsString'].value;
	if(from_date != "" && to_date != "") {
	if(validateField(document.forms[0].elements['errorLog.toDateAsString'],'auditLog.to',dateFormat)){
	cal2.hideCalendar()
	if(validateField(document.forms[0].elements['errorLog.fromDateAsString'],'auditLog.from',dateFormat)){	
	var dateFormatValue = '<s:property value="#request.session.CDM.dateFormatValue" />';
	var compare_date=compareDates(from_date,dateFormatValue,to_date,dateFormatValue);
	if(compare_date == 1)
		{
			document.forms[0].elements['errorLog.toDateAsString'].value=preToDate;
			cal2.hideCalendar()
			alert('<s:text name="alert.interestCharges.toDate"/>');
			
		}else{
	/*  START:Modified the code to Disable the refreshbutton.Modified by Chinniah on 3-Dec-10 for Mantis_1283 */
	document.getElementById("refreshbutton").innerHTML = document.getElementById("refreshdisablebutton").innerHTML;
/*  END:Modified the code to Disable the refreshbutton.Modified by Chinniah on 3-Dec-10 for Mantis_1283 */
			
			document.forms[0].method.value = 'defaultDetails';		    		   	
		    document.forms[0].submit();	 
		}
		dateSelected = false;
		return true;
		} else {
			dateSelected = false;
			return false;
		}
		} else {
			dateSelected = false;
			return false;
		}
		} else {
				if (from_date == "") {
					document.forms[0].elements['errorLog.fromDateAsString'].focus();
					dateSelected = false;
				}
				if (to_date == "") {
					document.forms[0].elements['errorLog.toDateAsString'].focus();
					dateSelected = false;
				}
			}		
	} else {
		return false;
	 }
}

/*End : Code modified for Mantis 1262 - to have the date field editable by Marshal on 20-11-2010*/

function storeFromDate(){
document.forms[0].elements['preErrorFromDateAsString'].value = document.forms[0].elements['errorLog.fromDateAsString'].value;
}

/*Start : Code modified for Mantis 1262 - to have the date field editable by Marshal on 20-11-2010*/
function onFromDateChange(){
	if(dateSelected){
	
	var preFromDate = document.forms[0].elements['preErrorFromDateAsString'].value;
	var from_date=document.forms[0].elements['errorLog.fromDateAsString'].value;
	var to_date=document.forms[0].elements['errorLog.toDateAsString'].value;
	if(from_date != "" && to_date != "") {
		if(validateField(document.forms[0].elements['errorLog.fromDateAsString'],'auditLog.from',dateFormat)){
		cal.hideCalendar()
		if(validateField(document.forms[0].elements['errorLog.toDateAsString'],'auditLog.to',dateFormat)){		
	var dateFormatValue = '<s:property value="#request.session.CDM.dateFormatValue" />';	
	var compare_date=compareDates(from_date,dateFormatValue,to_date,dateFormatValue);
	if(compare_date == 1)
		{
			document.forms[0].elements['errorLog.fromDateAsString'].value=preFromDate;
			cal.hideCalendar()
			alert('<s:text name="alert.fromDateShouldBeLess"/>');
			
		}else{	
/*  START:Modified the code to Disable the refreshbutton.Modified by Chinniah on 3-Dec-10 for Mantis_1283 */
	document.getElementById("refreshbutton").innerHTML = document.getElementById("refreshdisablebutton").innerHTML;
/*  END:Modified the code to Disable the refreshbutton.Modified by Chinniah on 3-Dec-10 for Mantis_1283 */
		
			document.forms[0].method.value = 'defaultDetails';		    		   	
		    document.forms[0].submit();			
			}
			dateSelected = false;
			return true;
			} else {
				dateSelected = false;
				return false;
			}
			} else {
				dateSelected = false;
				return false;
			}
		} else {
				document.forms[0].elements['errorLog.toDateAsString'].focus();
				dateSelected = false;
			}		
	} else {
			return false;
		}
}

/*This function validates the To Date fielf when 'Enter or Tab' is been pressed.*/
function onToDateKeyPress(obj,e){
	var event = (window.event|| e);
	dateSelected=false;
	if(event.keyCode == 9) {   // tab
		if(validateField(obj,'auditLog.to',dateFormat) && validateField(document.forms[0].elements['errorLog.fromDateAsString'],'auditLog.from',dateFormat)){
		if(comparedates(document.forms[0].elements['errorLog.fromDateAsString'].value,document.forms[0].elements['errorLog.toDateAsString'].value,dateFormat,'From Date','To Date')) {
			submitForm('defaultDetails');
			} else {			
			return false;
			}
			} else {			
			return false;
		}
	}
	if(event.keyCode == 13){  //enter
		if(validateField(obj,'auditLog.to',dateFormat) && validateField(document.forms[0].elements['errorLog.fromDateAsString'],'auditLog.from',dateFormat)){
			if(comparedates(document.forms[0].elements['errorLog.fromDateAsString'].value,document.forms[0].elements['errorLog.toDateAsString'].value,dateFormat,'From Date','To Date')) {
			submitForm('defaultDetails');
			} else {			
			return false;
			}
			} else {			
			return false;
			}
		}
}

/*This function validates the From Date fielf when 'Enter or Tab' is been pressed.*/
function onFromDateKeyPress(obj,e){
	var event = (window.event|| e);
	dateSelected=false;
	if(event.keyCode == 9){   // tab	
		if(validateField(obj,'auditLog.from',dateFormat) && validateField(document.forms[0].elements['errorLog.toDateAsString'],'auditLog.to',dateFormat)){
			if(comparedates(document.forms[0].elements['errorLog.fromDateAsString'].value,document.forms[0].elements['errorLog.toDateAsString'].value,dateFormat,'From Date','To Date')) {
			submitForm('defaultDetails');
			} else {			
				return false;
				}
			} else {			
				return false;
		}				
	}
	if(event.keyCode == 13){  //enter
		if(validateField(obj,'auditLog.from',dateFormat) && validateField(document.forms[0].elements['errorLog.toDateAsString'],'auditLog.to',dateFormat)){
			if(comparedates(document.forms[0].elements['errorLog.fromDateAsString'].value,document.forms[0].elements['errorLog.toDateAsString'].value,dateFormat,'From Date','To Date')) {
			submitForm('defaultDetails');
			} else {			
				return false;
				}
		} else {			
				return false;
			}
	}
}

function fromMouseIn(){
	
		if(validateField(document.forms[0].elements['errorLog.fromDateAsString'],'auditLog.from',dateFormat)){
			
			}
		else
		{	
			document.forms[0].elements['errorLog.fromDateAsString'].focus();
			return false;
			}		
}

/*This function validates the Date fields when the refresh button is pressed*/
function refreshWindow(){
dateSelected = false;	
	var dateFormat = '<s:property value="#request.session.CDM.dateFormat" />';		
		if(validateDateField(document.forms[0].elements['errorLog.fromDateAsString'],'auditLog.from',dateFormat)) {		
			if(validateDateField(document.forms[0].elements['errorLog.toDateAsString'],'auditLog.to',dateFormat)) {			
				if(comparedates(document.forms[0].elements['errorLog.fromDateAsString'].value,document.forms[0].elements['errorLog.toDateAsString'].value,dateFormat,'From','To')){
					submitForm('defaultDetails'); 
				} else {
					return false;
				}
			} else {			
				return false;
			}
		} else {		
			return false;
		}	
}
/*End : Code modified for Mantis 1262 - to have the date field editable by Marshal on 20-11-2010*/

function optionClick_server_filter_JSP(index,value,action)
{	
	
if(action == "filter"){

	value = replace(value,'&nbsp;',' ');
	
	var filterValue ="";
	

	if(currentFilter =="all" || currentFilter=="undefined"){
		
		
		for(var idx = 0 ; idx < xl.numColumns ; ++idx)
		{

			if(idx == index)
			{	
				for (var row=0 ;row<document.getElementById('errorLogList').rows.length;row++)
				{
				if(value!="All" && value!= "(Not empty)" && value!= "(Empty)")
				{
					var valueOne=document.getElementById('errorLogList').rows[row].cells[parseInt(index)].innerText;
					valueOne = replace(valueOne,'>','&gt;');
					valueOne = valueOne.trim()
					value = value.trim();
				
					if (value==valueOne && document.getElementById('errorLogList').rows[row].cells.length>2)
					{
						valueTwo=document.getElementById('errorLogList').rows[row].cells[parseInt(index)].title;
						valueTwo=replace(valueTwo,'\n','');
						if (valueTwo!="")
							value = valueTwo;
						break;
						}
					}
				}
				
				filterValue +=  value + "|";
			
				}
			else
				filterValue +=  "All" + "|";
			
		}
	}else{
			for (var row=0 ;row<document.getElementById('errorLogList').rows.length;row++)
				{
				
					if(value!="All" && document.getElementById('errorLogList').rows[row].cells.length>2)
					{
						var valueOne=document.getElementById('errorLogList').rows[row].cells[parseInt(index)].innerText;
						valueOne = replace(valueOne,'>','&gt;');
						valueOne = valueOne.trim();
						value = value.trim();
						if (value== valueOne)
						{
							valueTwo=document.getElementById('errorLogList').rows[row].cells[parseInt(index)].title;
							valueTwo=replace(valueTwo,'\n','');
							if (valueTwo!="")
								value = valueTwo;
							break;
						}
					}
				}
				

		var filter=currentFilter.split("|");
		
		filter[index]=value;
		
		for(var idx = 0 ; idx < xl.numColumns ; ++idx)
		{
			filterValue+=filter[idx] + "|";
		}
	}	
		document.forms[0].selectedSort.value=currentSort;
		document.forms[0].selectedFilter.value =filterValue ;
		
	}else{
		var sortColum=index;
		var sortDesc=value;
		document.forms[0].selectedSort.value=sortColum + "|" +sortDesc;
		document.forms[0].selectedFilter.value =currentFilter ;
	   
		 }
	document.getElementById('ddscrolltable').innerHTML='';
	document.forms[0].method.value='defaultDetails';
	
	document.forms[0].submit();
	
	
  }

//Start: Modified code by Aliveni for Mantis 837, to use the new page navigation system , on 12-Aug-2010.
/*Start : Code modified for Mantis 1262 - to have the date field editable by Marshal on 20-11-2010*/
function validatePageNumber(strObject) {	
	dateSelected=false;
	if (document.forms[0].elements['errorLog.fromDateAsString'].value != "" && document.forms[0].elements['errorLog.toDateAsString'].value != "") {
	if(validateField(document.forms[0].elements['errorLog.fromDateAsString'],'auditLog.from',dateFormat)){	
	if(validateField(document.forms[0].elements['errorLog.toDateAsString'],'auditLog.to',dateFormat)){	
    var re = /^\d+$/;
	if (strObject && (re.test(strObject.value) && strObject.value != 0)) 
	{
		if(parseInt(strObject.value) > maxPage)
		{
			strObject.value = maxPage;
		}
		goToResultsPage(strObject.value);
	}
	else
	{
		alert('<s:text name="alert.enterValidPageNumber"/>');
		strObject.value = currPage;
	}
	} else {		
		return false;
	}
	} else {		
		return false;
	}
	} else {
		alert('<s:text name="alert.enterValidDate"/>');		
	}	
}
/*End : Code modified for Mantis 1262 - to have the date field editable by Marshal on 20-11-2010*/

function goToResultsPage(goToPageNo)
{
/*Start : Variable added for Mantis 1262 - to edit and validate the date field by Marshal on 06-12-2010*/
	dateSelected=false;
/*End : Variable added for Mantis 1262 - to edit and validate the date field by Marshal on 06-12-2010*/
		/*  START:Modified the code to Disable the refreshbutton.Modified by Chinniah on 3-Dec-10 for Mantis_1283 */
	document.getElementById("refreshbutton").innerHTML = document.getElementById("refreshdisablebutton").innerHTML;
/*  END:Modified the code to Disable the refreshbutton.Modified by Chinniah on 3-Dec-10 for Mantis_1283 */

	var currPage = "${requestScope.currentPage}";
	document.forms[0].method.value = "next";
	document.forms[0].goToPageNo.value=goToPageNo;
	document.forms[0].currentPage.value=currPage;
	document.forms[0].maxPages.value=maxPage;
	document.forms[0].selectedSort.value=currentSort;
	document.forms[0].selectedFilter.value=currentFilter;
	document.forms[0].fromDate.value='${fromDate}';
	document.forms[0].toDate.value='${toDate}';
	document.forms[0].submit();
}

/*Start : Code modified for Mantis 1262 - to have the date field editable by Marshal on 20-11-2010*/
function clickLink(goToPageNo)
{
	dateSelected=false;
	if (document.forms[0].elements['errorLog.fromDateAsString'].value != "" && document.forms[0].elements['errorLog.toDateAsString'].value != "") {
	if(validateField(document.forms[0].elements['errorLog.fromDateAsString'],'auditLog.from',dateFormat)){
	if(validateField(document.forms[0].elements['errorLog.toDateAsString'],'auditLog.to',dateFormat)){	
/*  START:Modified the code to Disable the refreshbutton.Modified by Chinniah on 3-Dec-10 for Mantis_1283 */
	document.getElementById("refreshbutton").innerHTML = document.getElementById("refreshdisablebutton").innerHTML;
/*  END:Modified the code to Disable the refreshbutton.Modified by Chinniah on 3-Dec-10 for Mantis_1283 */
	/*  START:Modified by Mefteh Bouazizi for Mantis_1534 to convert GET URL to POST URL using a form */
	var baseUrl = '<%=SwtUtil.convertBeanToUrlParams((PageDetails)((ArrayList<PageDetails>)request.getAttribute("pageSummaryList")).get(0), "pageDetails") %>';
	var url='errorlog.do?'+baseUrl;			
	url +='method=next&goToPageNo='+goToPageNo+'&selectedSort='+currentSort+'&selectedFilter='+currentFilter+'&fromDate='+'${fromDate}'+'&toDate='+'${toDate}';
    submitFormFromURL(url,window); 
	/*  END:Modified by Mefteh Bouazizi for Mantis_1534 to convert GET URL to POST URL using a form */
	} else {
		return false;
	} 
	} else {
		return false;
	}
	} else {
		alert('<s:text name="alert.enterValidDate"/>');
	}
}
/*End : Code modified for Mantis 1262 - to have the date field editable by Marshal on 20-11-2010*/
//End: Modified code by Aliveni for Mantis 837, to use the new page navigation system , on 12-Aug-2010. 
</SCRIPT>
 <script language="JAVASCRIPT">
 var dateFormatValue = '<s:property value="#request.session.CDM.dateFormatValue" />';
      <%-- Start:Vivekanandan:22-12-2008: Modified
	   		Mantis 821: To display records in data grid if from date or to date is changed --%>	
      var cal = new CalendarPopup("caldiv",true); 
 	  <%-- End:Vivekanandan:22-12-2008: Modified
	       Mantis 821: To display records in data grid if from date or to date is changed --%>	

      cal.offsetX = 22;
      cal.offsetY = 0;
	  
	  <%-- Start:Vivekanandan:22-12-2008: Modified
	   	    Mantis 821: To display records in data grid if from date or to date is changed --%>	
	  var cal2 = new CalendarPopup("caldiv",true); 
	   <%-- End:Vivekanandan:22-12-2008: Modified
	        Mantis 821: To display records in data grid if from date or to date is changed --%>	

      cal2.offsetX = 22;
      cal2.offsetY = 0;
  </script> 
</head>

<body onmousemove="reportMove()" leftmargin="0" topmargin="0" marginheight="0" onLoad="bodyOnLoad();setParentChildsFocus(); setFocus(document.forms[0]);" onunload="call();">

<s:form action="errorlog.do" method="post" onsubmit="return validate(this);"> 
<s:set var="CDM" value="#request.session.CDM" />
<input name="method" type="hidden" value="display">
<input name="selectedFilter" type="hidden" value='${selectedFilter}'>
<input name="selectedSort" type="hidden" value='${selectedSort}'>
<input name="currentPage" type="hidden" value="">
<input name="totalCount" type="hidden" value="">
<input name="preErrorFromDateAsString" type="hidden" value="">
<input name="preErrorToDateAsString" type="hidden" value="">

<!-- Start: Modified code by Aliveni for Mantis 837, to use the new page navigation system , on 12-Aug-2010. -->
<input name="goToPageNo" type="hidden" value="">
<input name="fromDate" type="hidden" value="">
<input name="toDate" type="hidden" value="">
<input name="maxPages" type="hidden" value="">
<!-- End: Modified code by Aliveni for Mantis 837, to use the new page navigation system , on 12-Aug-2010. -->

<div id="ErrorLog" color="#7E97AF" style="position:absolute; border:2px outset; left:20px; top:20px; width:1214px; height:37px;">
<div id="ErrorLog" style="position:absolute; left:8; top:4; width:1200px; height:54px; visibility:visible;">
  <table width="450" border="0" cellpadding="0" cellspacing="0" class="content">
 <tr >
		  <td width="15"><b style="font-size: 12px"><s:text name="auditLog.from"/></b>*</td>
		  <td width="28px" >&nbsp;</td>
		   <td width="80px" id="errorLogFromDate">
<%-- Start:Vivekanandan:22-12-2008: Modified	Mantis 821: To display records in data grid if from date or to date is changed --%>
 <!--Betcy:20/12/2008:Added a property for Mantis 774 to make the text box as readonly-->
 <!--START:Modified the readonly property of From date text field to false by Marshal on 26-10-2010 for Mantis 1262 -->
			<s:textfield titleKey="tooltip.fromDate" tabindex="1" name="errorLog.fromDateAsString" cssClass="htmlTextAlpha" readonly="false" maxlength="10" cssStyle="width:80px;height=20px;" onkeydown="onFromDateKeyPress(this,event);" onblur="onFromDateChange();" onmouseout="dateSelected=false"/>
<!--END:Modified the readonly property of From date text field to false by Marshal on 26-10-2010 for Mantis 1262 -->
		  <td width="28px"> &nbsp;<A  title='<s:text name="tooltip.selectFromDate"/>' tabindex="2" name="datelink" ID="datelink" onClick="cal.select(document.forms[0].elements['errorLog.fromDateAsString'],'datelink',dateFormatValue); storeFromDate();dateSelected=true; return true;"><img src="images/calendar-16.gif" style="margin-bottom: -2px!important;"></A></td>
		  

		 <td width="28px" >&nbsp;</td>
			    <td width="15"><b style="font-size: 12px"><s:text name="auditLog.to"/></b>*</td>
				  <td width="28px" >&nbsp;</td>
		  <td width="80px" id="errorLogToDate">
		        <!--Betcy:20/12/2008:Added a property for Mantis 774 to make the text box as readonly-->
		        <!--START:Modified the readonly property of To date text field to false by Marshal on 26-10-2010 for Mantis 1262 -->
		  	<s:textfield titleKey="tooltip.toDate" tabindex="3" name="errorLog.toDateAsString" cssClass="htmlTextAlpha" cssStyle="width:80px;height=20px;" readonly="false" maxlength="10" onkeydown="onToDateKeyPress(this,event);" onblur="onToDateChange();" onmouseout="dateSelected=false"/>
		  	<!--END:Modified the readonly property of To date text field to false by Marshal on 26-10-2010 for Mantis 1262 -->
		   <td> &nbsp;<A  title='<s:text name="tooltip.selectToDate"/>' tabindex="4" name="datelink2" ID="datelink2" onClick="cal2.select(document.forms[0].elements['errorLog.toDateAsString'],'datelink2',dateFormatValue); storeToDate();dateSelected=true;return false;").focus();" ><img src="images/calendar-16.gif" style="margin-bottom: -2px!important;"></A></td>
		   <%-- End:Vivekanandan:22-12-2008 : Modified Mantis 821: To display records in data grid if from date or to date is changed --%>
		   
		  <td>
        </tr>
</table>
</div>
 <!--Start- Code modified for Defect No. 116 in Mantiss on 03/11/2007 -->

 <!--End- Code modified for Defect No. 116 in Mantiss on 03/11/2007 -->
 
 <!-- Start: Modified code by Aliveni for Mantis 837, to use the new page navigation system , on 12-Aug-2010. -->
 <div id="pageSummaryList" style="position:absolute; left:1000px; top:1px; width:212px; height:25px;border:6px;">
 <s:if test='#request.hidePagination != "true"'>
		<table border="0" cellpadding="0" cellspacing="1" height="25px" style="padding-bottom: 4px;">		
		  <tr height="25px">
		  <%String currentPageAsString = (String)request.getAttribute("currentPage");%>
		  <%String maxPageAsString = (String)request.getAttribute("maxPage");%>
		  <%int countPageNo = 1;%>
		  <s:iterator value="#request.pageSummaryList">			  		  
			<% if( countPageNo <=12) {++countPageNo; %>
			<td height="30"><b style="font-size: 12px">Page</b>&nbsp;&nbsp;
			<!-- Start : Event function signature has been modified for Mantis 1262 by Marshal on 20-11-2010 -->
	  		<input class="htmlTextNumeric" id="pageNoText" name="pageNo" size="5" style="height: 21px;padding-top: 1px" align="top" value="<%=currentPageAsString %>" onkeydown="if (event.keyCode == 9 || event.keyCode == 13)validatePageNumber(this);">
			<!-- Start : Event function signature has been modified for Mantis 1262 by Marshal on 20-11-2010 -->
	  		</td>
	  		<td>
				<s:if test='#request.nextEnabled == "true"'>
				    <a href="#" onclick="clickLink(-1);">
				        <img alt="Next page" src="images/page_up.png" align="top" border="0" height="12" width="18" style="padding-top:6px;margin-left: -1px;"></img><br />
				    </a>
				</s:if>
				<s:else>
				    <img alt="Next page" src="images/page_up.png" align="top" border="0" height="12" width="18" style="padding-top:6px;margin-left: -1px;"></img><br />
				</s:else>
				
				<s:if test='#request.prevEnabled == "true"'>
				    <a href="#" onclick="clickLink(-2);">
				        <img alt="Previous page" src="images/page_down.png" align="bottom" height="12" width="18" border="0" style="padding-bottom:6px;margin-left: -1px;"></img><br />
				    </a>
				</s:if>
				<s:else>
				    <img alt="Previous page" src="images/page_down.png" align="bottom" width="18" border="0" style="padding-bottom:6px;margin-left: -1px;"></img><br />
				</s:else>
	  		</td>
	  		<td style="text-align: center;font-size:9pt;"><s:text name="genericDisplayMonitor.labelOf"/>&nbsp;&nbsp;
	    		<input class="textAlpha" style="background:transparent;border: 0; height: 17;" readonly name="maxPageNo" value="<%=maxPageAsString %>" size="5">
	  		</td>
			<%}%>	
		</s:iterator>  		
		  </tr>
		</table>
	</s:if>
	</div>
<!-- End: Modified code by Aliveni for Mantis 837, to use the new page navigation system , on 12-Aug-2010. -->
</div>

<DIV ID="caldiv" style="z-index:99;position:absolute;visibility:visible;background-color:white;layer-background-color:white;"></DIV>
 <div id="ErrorLog" color="#7E97AF" style="position:absolute; border:2px outset; left:20px; top:62px; width:1212px; height:477px;">
<div id="ErrorLog" style="z-index:99;position:absolute;left:0px; top:0px; width:1193px;height:10px; ">
<table  class="sort-table" id="table_2" bgcolor="#B0AFAF" width="1603" border="0" cellspacing="1" cellpadding="0"  height="20" style="margin-top: 0px;">
	<thead>
		<tr>
	<td title='<s:text name="tooltip.sortLogDate"/>' width="80px" height="20px" align="center"><b><s:text name="errorLog.errorDate_Date"/></b></td>
	<td title='<s:text name="tooltip.sortLogTime"/>' width="78px" height="20px" align="center"><b><s:text name="errorLog.errorDate_Time"/></b></td>
	<td title='<s:text name="tooltip.sortUserId"/>' width="144px" height="20px" align="center"><b><s:text name="errorLog.userId"/></b></td>
	<td title='<s:text name="tooltip.sortFile"/>' width="280px" height="20px" align="center"><b><s:text name="errorLog.source"/></b></td>
	<td title='<s:text name="tooltip.errorDesc"/>' width="1020px" height="20px"   align="center"><b><s:text name="errorLog.error"/></b></td>

		</tr>
	</thead>
</table>
</div>

<div id="ddscrolltable"  style="position:absolute; left:0px; top:0px; width:1211px; height:473px;overflow:scroll">
<div id="AuditLog" style="position:absolute;z-index:99;left:1px; top:18px; width:1217px; height:10px;">

<table class="sort-table" id="errorLogList" width="1614" border="0" cellspacing="1" cellpadding="0" height="438">

	<tbody> 		
	<%int count = 0; %>  
	<s:iterator value="#request.errorLogList">  
		<% if( count%2 == 0 ) {%><tr height="20px" class="even"><% }else  { %> <tr height="20px" class="odd"> <%}++count; %>
			<td width="80px" align="left"><s:property value="errorDate_Date" />&nbsp;</td>
			<td width="78px" align="left"><s:property value="errorDate_Time" />&nbsp;</td>
			<td width="144px" align="left"><s:property value="userId" />&nbsp;</td>

			<!--  START:Code Added by Arumugam on 03-APR-2009 for Mantis 748:Added a new attributed  title and replaced "<quot;>" to "'"   -->
			<td width="280px" align="left" title="<s:property value="source" />">
			    <s:set var="status" value="source" />
			    <jsp:useBean id="status" type="java.lang.String" />
			    <%String  Source =status.toString(); %><%Source = Source.replace("<quot;>","'");%><%=Source%>&nbsp;</td>
			<td width="1020px" align="left" title="<s:property value="errorDesc" />">
			    <s:set var="statusErrorDesc" value="errorDesc" />
			    <jsp:useBean id="statusErrorDesc" type="java.lang.String" />
			    <%String  ErrorDesc =statusErrorDesc.toString(); %><%ErrorDesc = ErrorDesc.replace("<quot;>","'");%><%=ErrorDesc%>&nbsp;
			</td>

			<!--  END:Code Added by Arumugam on 03-APR-2009 for Mantis 748:Added a new attributed  title and replaced "<quot;>" to "'"  -->
			</tr>
	</s:iterator>   
	</tbody>
	<tfoot><tr><td colspan="5" ></td></tr></tfoot>
</table>
</div>
</div>
</div>

<div id="MaintenanceLog" style="position:absolute; left:1190; top:555px; width:40px; height:20px; visibility:visible;">
	<a title='<s:text name="tooltip.helpScreen"/>' tabindex="7" href=# onclick="javascript:openWindow(buildPrintURL('print','Error Log'),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')" onMouseOut="MM_swapImgRestore()" onMouseOver="MM_swapImage('Help','','images/help_default.GIF ',1)">
	<img src="images/help_default.GIF " name="Help"  border="0" ></a>	
</div>

   <div id="ddimagebuttons" color="#7E97AF" style="position:absolute; border:2px outset; left:20; top:545; width:1213px; height:39px; visibility:visible;">
<div id="ErrorLog" style="position:absolute; left:6; top:4; width:965; height:15px; visibility:visible;">
  	 <table width="142" border="0" cellspacing="0" cellpadding="0" height="20">
		<tr>
		<td>
			<td id="refreshbutton">	
			<!-- Start : Code modified for Mantis 1262 - to have the date field editable by Marshal on 20-11-2010 -->
			<a  title='<s:text name="tooltip.refreshErrorLog"/>' tabindex="5" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onClick="if(validateForm(document.forms[0]) ){refreshWindow();}"><s:text name="button.Refresh"/></a>
			<!-- End : Code modified for Mantis 1262 - to have the date field editable by Marshal on 20-11-2010 -->
			</td>
			
    		<td id="closebutton">
			<a title='<s:text name="tooltip.close"/>' tabindex="6" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onclick="confirmClose('P');"><s:text name="button.close"/></a>			
			</td>
		</tr>
		</table>
	</div>
	<!-- Start: Code added by venkatesan on 28-mar-2011 for Mantis 1308 :"Show 'Last refresh' time in screens that have refresh function." -->
	<table height="36" style ="float: left;width: 1100px;"><tr>
		<td id="lastRefTimeLable" width="960px" align="right" style="font-size:12" >
		<s:text name="label.lastRefTime" />
		</td>			
		<td id="lastRefTime" style="font-size:12">
		<input class="textAlpha" style="background:transparent;border: 0;" tabindex="-1" readonly name="maxPageNo" value="" size="14">				
		</td>
		</tr>
	</table>
	<!-- End: Code added by venkatesan on 28-mar-2011 for Mantis 1308 :"Show 'Last refresh' time in screens that have refresh function." -->
	<!--  START:Modified the code to Disable the refreshbutton.Modified by Chinniah on 3-Dec-10 for Mantis_1283 -->
	<div id="Role" style="position:absolute; left:1115; top:11; width:70px; height:15px; z-index:5;visibility:visible;">
	
	<table style ="float: left; ">
	<div id="exportReport" /> 
	</table>
	</div>
	
  <div style="position:absolute; left:6; top:4; width:705px; height:15px; visibility:hidden;">  	
		<table border="0" cellspacing="0" cellpadding="0" height="20" style="visibility:hidden">
			<tr>
			<td id="refreshdisablebutton">
			<a  class="disabled"  disabled="disabled"><s:text name="button.Refresh"/></a>
		</td>
		</tr>
		</table>
	</div>
	<!--  END:Modified the code to Disable the refreshbutton.Modified by Chinniah on 3-Dec-10 for Mantis_1283 -->

</s:form >
</body>
</html>