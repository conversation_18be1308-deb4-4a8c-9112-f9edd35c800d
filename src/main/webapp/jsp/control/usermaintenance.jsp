<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@ include file="/taglib.jsp"  %>
<%@ page import="org.swallow.util.SwtConstants" %>
<%@ page import="org.swallow.util.SwtUtil" %>

<%@page import="org.swallow.control.model.UserMaintenance"%>
<html>
<head>
<title><s:text name="userSetup.title.window"/></title>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
<script type="text/javascript" src="js/jquery.exportselect.js"></script>
<SCRIPT language="JAVASCRIPT">

var requestURL = new String('<%=request.getRequestURL()%>');
var appName = "<%=SwtUtil.appName%>";
var idy = requestURL.indexOf('/'+appName+'/');
requestURL=requestURL.substring(0,idy+1) ;
/*This variable is used to hold the advanced user rights */
var advanceduser="${requestScope.advancedUser}";

var menuEntityCurrGrpAccess = "${requestScope.menuEntityCurrGrpAccess}";
/*Added to apply style on mandatory field ,if it's equal to undefined then no field will be colored*/
 mandatoryFieldsArray= "undefined" ;

function bodyOnLoad(){
	xl = new XLSheet("entities","table_2", ["String", "String", "String"],"221");
	xl.onsort = xl.onfilter = onFilterandSort;
	highlightTableRows("entities");
	document.getElementById("changebutton").innerHTML = document.getElementById("changedisablebutton").innerHTML;
	document.getElementById("deletebutton").innerHTML = document.getElementById("deletedisablebutton").innerHTML;
	document.getElementById("viewbutton").innerHTML = document.getElementById("viewdisablebutton").innerHTML;
	if(menuEntityCurrGrpAccess == "0")
	{
		document.getElementById("addbutton").innerHTML = document.getElementById("addenablebutton").innerHTML;
	}else{
		document.getElementById("addbutton").innerHTML = document.getElementById("adddisablebutton").innerHTML;
	}	
	if(advanceduser == 'N')
	{			
		document.getElementById("addbutton").innerHTML = document.getElementById("adddisablebutton").innerHTML;
	}
	document.forms[0].menuAccessId.value = "${requestScope.menuAccessId}";	
	
	var headerData = [];
	var dataprovider = new Array();
	
	var newElement1 = {};
	newElement1[headerData[0]] = 'Pdf';
	dataprovider.push(newElement1);
			
	var newElement2 = {};
	newElement2[headerData[0]] = 'Excel';
	dataprovider.push(newElement2);
	
	$("#exportReport").exportselect ({
		dataprovider: dataprovider,
		change: exportReport,
		selectedIndex:0
	  });	
}

function exportReport(){
	var type=$("#exportReport").getSelected(0);
	return submitPrintAll('report',type.toLowerCase());
}
<s:if test='"yes" == #request.parentFormRefresh' >
window.opener.document.forms[0].method.value="display";
window.opener.document.forms[0].submit();
self.close();
</s:if>

function submitPrint(methodName){
	document.forms[0].method.value = methodName;
	document.forms[0].selectedUserCodeId.value=document.forms[0].selectedUserCode.value;
	document.forms[0].submit();
	setParentChildsFocus();
}

function submitPrintAll(methodName,fileType){
		if(document.forms[0].selectedRow.value=='yes'){
		document.forms[0].selectedUserCodeId.value=document.forms[0].selectedUserCode.value;	
	}else{
		document.forms[0].selectedUserCodeId.value="";
	}
	document.forms[0].method.value = methodName;
	document.forms[0].fileType.value = fileType.trim();
	document.forms[0].submit();
	setParentChildsFocus();
}


function onFilterandSort()
{
	updateColors();
	disableAllButtons();

}
function disableAllButtons()
{
	document.getElementById("changebutton").innerHTML = document.getElementById("changedisablebutton").innerHTML;
	document.getElementById("deletebutton").innerHTML = document.getElementById("deletedisablebutton").innerHTML;
	document.getElementById("viewbutton").innerHTML = document.getElementById("viewdisablebutton").innerHTML;
	
}

function buildUserMaintenance(methodName){
	
	
	var param = 'usermaintenance.do?method='+methodName+'&selectedUserCode=';
	param += document.forms[0].selectedUserCode.value;
	
		/*If the user has the advanced user rights the screen will display all the user details selected for view or change -->
		Else the user can change or view only the passwords and user status*/
	if (methodName != 'add'){
			var oXMLHTTP = new XMLHttpRequest();
		
			var sURL = requestURL + appName+"/usermaintenance.do?method=getAdvancedUserRights";
		
			oXMLHTTP.open( "POST", sURL, false );
			oXMLHTTP.send();
			var str=new String(oXMLHTTP.responseText);
			if (str == 'Y'){
				openWindow(param,'','left=50,top=190,width=659,height=652,toolbar=0,status=yes, resizable=yes');
			}else if (str == 'N'){
				openWindow(param,'','left=50,top=190,width=659,height=240,toolbar=0,status=yes, resizable=yes');		
			}
	}else{
			openWindow(param,'','left=50,top=190,width=659,height=652,toolbar=0,status=yes, resizable=yes');
	}
	
	
}

function onSelectTableRow(rowElement,isSelected)
{
	var hiddenElement = rowElement.getElementsByTagName("input")[0];
	document.forms[0].selectedUserCode.value = hiddenElement.value;
	
  if(isSelected)
	{
			document.forms[0].selectedRow.value ='yes';
		
		document.getElementById("viewbutton").innerHTML = document.getElementById("viewenablebutton").innerHTML;
		
		if(menuEntityCurrGrpAccess == "0") {
		
		document.getElementById("changebutton").innerHTML = document.getElementById("changeenablebutton").innerHTML;
		
			/* Delete  button has been disabled when the show advanced user 
		configuration status is not set*/ 
		if (advanceduser == 'N'){
			document.getElementById("deletebutton").innerHTML = document.getElementById("deletedisablebutton").innerHTML;
		}else{
			document.getElementById("deletebutton").innerHTML = document.getElementById("deleteenablebutton").innerHTML;
		}
			
		
		}
	
	}

	else
	{
				document.forms[0].selectedRow.value ='no';
			document.getElementById("changebutton").innerHTML = document.getElementById("changedisablebutton").innerHTML;
		document.getElementById("deletebutton").innerHTML = document.getElementById("deletedisablebutton").innerHTML;
		document.getElementById("viewbutton").innerHTML = document.getElementById("viewdisablebutton").innerHTML;
		

	}
}

function submitDeleteForm(methodName){
	document.forms[0].method.value = methodName;
	
	var currentUserId = '${requestScope.currentUserId}';	
	
	if(currentUserId == document.forms[0].selectedUserCode.value){
		alert('<s:text name="cannotdelete.message"/>');
	}else{

		if(document.forms[0].selectedUserCode.value == "ADMIN"){
			alert('<s:text name="cannotdelete.admin"/>');
		}else{
				var yourstate=window.confirm('<s:text name="confirm.delete"/>');
				if (yourstate==true){ 
					document.forms[0].submit();
				}
		}
	}
	
}

</SCRIPT>
</head>
<s:form action="usermaintenance.do">
<body onmousemove="reportMove()" leftmargin="0" topmargin="0" marginheight="0" onLoad="setParentChildsFocus();setFocus(document.forms[0]);bodyOnLoad();ShowErrMsgWindow('${actionError}');setParentChildsFocus();" onunload="call()">
<input name="method" type="hidden" value="display">
<input name="selectedUserCode" type="hidden" value="">
<input name="selectedUserCodeId" type="hidden" value="">

<input name="fileType" type="hidden" value="">
<input name="selectedRow" type="hidden" value="">

<input name="menuAccessId" type="hidden" >


<div id="UserMaintenance" color="#7E97AF" style="position:absolute; border-left:2px outset; left:20px; top:20px; width:605px; height:430px;">

<div id="UserMaintenance" style="position:absolute;z-index:99;left:0px; top:0px; width:590px; height:10px;">
<table  class="sort-table" id="table_2" bgcolor="#B0AFAF" width="582px" border="0" cellspacing="1" cellpadding="0"  height="23">
	<thead>
		<tr>
			<td  title='<s:text name="tooltip.sortUserId"/>' width="140px" style="border-left-width: 0px;"><b><s:text name="usermaintenance.userId"/></b></td>
			<td  title='<s:text name="tooltip.sortUserName"/>'width="302px" ><b><s:text name="usermaintenance.userName"/></b></td>
			<td  title='<s:text name="tooltip.sortRoleId"/>' width="140px" ><b><s:text name="usermaintenance.role"/></b></td>
		</tr>
	</thead>
</table>
</div>



<div id="ddscrolltable"  style="position:absolute; left:0px; top:1px; width:602px; height:425px;overflowY:scroll">
<div id="UserMaintenance" style="position:absolute;z-index:99;left:1px; top:20px; width:582px; height:10px;">
<table class="sort-table" id="entities" width="580" border="0" cellspacing="1" cellpadding="0" height="405">
	<tbody> 		
	<%int count = 0; %>  
	<s:iterator value="#request.entities" var="entities" >          
		<% if( count%2 == 0 ) {%><tr class="even"><% }else  { %> <tr class="odd"> <%}++count; %>
		 <s:set var="entities" value="#entities"/>
		 <jsp:useBean id="entities" class="org.swallow.control.model.UserMaintenance" />		
		<%UserMaintenance userMaintenance=(UserMaintenance) entities; %>
			<s:hidden name="#entities.id.userId"/>
			<td width="140px" align="left" >
			<s:property value="#entities.id.userId" />&nbsp;</td>
			<td width="300px" >
			<%= userMaintenance.getUsername() != null ?  userMaintenance.getUsername().replaceAll(" ","&nbsp;"): "" %>
			&nbsp;</td>
			<td width="140px" ><s:property value="#entities.roleId" />&nbsp;</td>
		</tr>
	</s:iterator>  
	</tbody>
	<tfoot><tr><td colspan="3" ></td></tr></tfoot>
</table>
</div>
</div>

</div>

<div id="UserMaintenance" style="position:absolute; left:535; top:468; width:70; height:39px; z-index:5;visibility:visible;">
	<table>
	  <div id="exportReport"/>
	</table>
</div>
<div id="helpIcon" style="position:absolute; left:580; top:467; width:30; height:39px;"> 
<a tabindex="6" href=# onclick="javascript:openWindow(buildPrintURL('print','User Setup '),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')" onMouseOut="MM_swapImgRestore()" onMouseOver="MM_swapImage('Help','','images/help_default.GIF ',1)">
<img src="images/help_default.GIF " name="Help"  border="0" title='<s:text name="tooltip.helpScreen"/>' ></a> 
</div>
 <div id="ddimagebuttons" color="#7E97AF" style="position:absolute; border:2px outset; left:20; top:455; width:605; height:39px; visibility:visible;">
  
  <div id="UserLog" style="position:absolute; left:6; top:4; width:440; height:15px; visibility:visible;">
  	 <table width="350" border="0" cellspacing="0" cellpadding="0" height="20">
		<tr>
		
		<td width="70px" id="addbutton" >
			
			<a  tabindex="1" title='<s:text name="tooltip.addNewUser"/>'
			onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onClick="buildUserMaintenance('add')"><s:text name="button.add"/></a>
            </td>
			<td width="70" id="changebutton">		
			</td>
			<td width="70" id="viewbutton">		
			</td>
			<td width="70" id="deletebutton">		
			</td>
			<td width="70">
			<a tabindex="5" title='<s:text name="tooltip.close"/>' onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onclick="confirmClose('P');"><s:text name="button.close"/></a>
		</td>
		</tr>
		</table>
	</div>
  
  <div  style="position:absolute; left:6; top:4; width:440; height:15px; visibility:hidden;">
  	 <table width="350" border="0" cellspacing="0" cellpadding="0" height="20">
		<tr>
	    
		<td id="addenablebutton">
		<!--start:modified the mantis 1549 by sunil on 10-02-2012.The purpose to display the meaningful tooltip -->
			<a  tabindex="1" title='<s:text name="tooltip.addNewUser"/>'
			onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onClick="buildUserMaintenance('add')"><s:text name="button.add"/></a>
		</td>
		<td id="adddisablebutton">
			<a  class="disabled" disabled="disabled" ><s:text name="button.add"/></a>
		</td>
			<%String strAdvancedUser = (String)request.getAttribute("advancedUser");%>
		<td id="changeenablebutton">
		 <a  tabindex="2" title='<s:text name="tooltip.changeSelectedUser"/>'
			 onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onClick="buildUserMaintenance('change')"><s:text name="button.change"/></a>
		</td>
		<td id="changedisablebutton">
			<a  class="disabled" disabled="disabled"title='<s:text name="tooltip.changeSelectedUser"/>'><s:text name="button.change"/></a>
		</td>

		<td id="viewenablebutton">
		<a  tabindex="3" title='<s:text name="tooltip.viewSelectedUser"/>' onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onClick="buildUserMaintenance('view')"><s:text name="button.view"/></a>
			</td>
		<td id="viewdisablebutton">
			<a  class="disabled" disabled="disabled"title='<s:text name="tooltip.viewSelectedUser"/>'><s:text name="button.view"/></a>
		</td>

		<td id="deleteenablebutton">
			<a  tabindex="4" title='<s:text name="tooltip.delSelectedUser"/>'
				onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onClick="javascript:submitDeleteForm('delete');"><s:text name="button.delete"/></a>
			</td>
			<td id="deletedisablebutton">
			<a  class="disabled" disabled="disabled"title='<s:text name="tooltip.delSelectedUser"/>'><s:text name="button.delete"/></a>
		</td>
		</tr>
		</table>
			<!--End:modified the mantis 1549 by sunil on 10-02-2012.The purpose to display the meaningful tooltip -->
	</div>
 </div>

<blockquote>&nbsp;</blockquote>


</s:form>
</body>
</html>
