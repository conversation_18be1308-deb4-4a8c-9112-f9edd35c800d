<html>

<head>
<meta charset="UTF-8">
<meta http-equiv=Content-Type content="text/html; charset=UTF-8">
<meta name=Generator content="Microsoft Word 15 (filtered)">
<style>
<!-- /* Font Definitions */
@font-face {
	font-family: "Cambria Math";
	panose-1: 2 4 5 3 5 4 6 3 2 4;
}

@font-face {
	font-family: Calibri;
	panose-1: 2 15 5 2 2 2 4 3 2 4;
}

@font-face {
	font-family: Consolas;
	panose-1: 2 11 6 9 2 2 4 3 2 4;
}
/* Style Definitions */
p.<PERSON><PERSON><PERSON><PERSON>,li.<PERSON><PERSON><PERSON><PERSON>,div.Mso<PERSON>ormal {
	margin-top: 0cm;
	margin-right: 0cm;
	margin-bottom: 8.0pt;
	margin-left: 0cm;
	line-height: 107%;
	font-size: 11.0pt;
	font-family: "Calibri", "sans-serif";
}

.MsoChpDefault {
	font-family: "<PERSON><PERSON>ri", "sans-serif";
}

.MsoPapDefault {
	margin-bottom: 8.0pt;
	line-height: 107%;
}

@page WordSection1 {
	size: 841.9pt 595.3pt;
	margin: 70.85pt 70.85pt 70.85pt 70.85pt;
}

div.WordSection1 {
	page: WordSection1;
}
-->
</style>

</head>

<body lang=FR>

	<div class=WordSection1>

		<p class=MsoNormal>
			<span lang=EN-GB style='color: #002060'>${recipient_name},</span>
		</p>

		<p class=MsoNormal>
			<span style='color: #002060'>Veuillez trouver ci-dessous, une
				liste détaillée des scenarios d'alertes pour laquelle votre rôle
				comme étant (${role_id}) est configuré pour recevoir.</span>
		</p>
	<br>
	<br>
		<table class=MsoTableGrid border=1 cellspacing=0 cellpadding=0
			width=861
			style='width: 645.45pt; border-collapse: collapse; border: none'>
			<tr style='height: 25.45pt'>
				<td width=115 valign=top
					style='width: 86.4pt; border: solid windowtext 1.0pt; background: #00B0F0; padding: 0.1cm 5.4pt 0cm 5.4pt; height: 25.45pt'>
					<p class=MsoNormal align=center
						style='margin-bottom: 0cm; margin-bottom: .0001pt; text-align: center; line-height: normal'>
						<span lang=EN-GB style='color: #E7E6E6'>Titre du Scenario</span>
					</p>
				</td>
				<td width=262 valign=top
					style='width: 196.7pt; border: solid windowtext 1.0pt; border-left: none; background: #00B0F0; padding: 0.1cm 5.4pt 0cm 5.4pt; height: 25.45pt'>
					<p class=MsoNormal align=center
						style='margin-bottom: 0cm; margin-bottom: .0001pt; text-align: center; line-height: normal'>
						<span lang=EN-GB style='color: #E7E6E6'>Description</span>
					</p>
				</td>
				<td width=79 valign=top
					style='width: 58.95pt; border: solid windowtext 1.0pt; border-left: none; background: #00B0F0; padding: 0.1cm 5.4pt 0cm 5.4pt; height: 25.45pt'>
					<p class=MsoNormal align=center
						style='margin-bottom: 0cm; margin-bottom: .0001pt; text-align: center; line-height: normal'>
						<span lang=EN-GB style='color: #E7E6E6'>Catégorie</span>
					</p>
				</td>
				<td width=84 valign=top
					style='width: 62.7pt; border: solid windowtext 1.0pt; border-left: none; background: #00B0F0; padding: 0.1cm 5.4pt 0cm 5.4pt; height: 25.45pt'>
					<p class=MsoNormal align=center
						style='margin-bottom: 0cm; margin-bottom: .0001pt; text-align: center; line-height: normal'>
						<span lang=EN-GB style='color: #E7E6E6'>Entité</span>
					</p>
				</td>
				<td width=79 valign=top
					style='width: 58.9pt; border: solid windowtext 1.0pt; border-left: none; background: #00B0F0; padding: 0.1cm 5.4pt 0cm 5.4pt; height: 25.45pt'>
					<p class=MsoNormal align=center
						style='margin-bottom: 0cm; margin-bottom: .0001pt; text-align: center; line-height: normal'>
						<span lang=EN-GB style='color: #E7E6E6'>Devise</span>
					</p>
				</td>
				<td width=72 valign=top
					style='width: 53.9pt; border: solid windowtext 1.0pt; border-left: none; background: #00B0F0; padding: 0.1cm 5.4pt 0cm 5.4pt; height: 25.45pt'>
					<p class=MsoNormal align=center
						style='margin-bottom: 0cm; margin-bottom: .0001pt; text-align: center; line-height: normal'>
						<span lang=EN-GB style='color: #E7E6E6'>Compteur</span>
					</p>
				</td>
				<td width=85 valign=top
					style='width: 63.95pt; border: solid windowtext 1.0pt; border-left: none; background: #00B0F0; padding: 0.1cm 5.4pt 0cm 5.4pt; height: 25.45pt'>
					<p class=MsoNormal align=center
						style='margin-bottom: 0cm; margin-bottom: .0001pt; text-align: center; line-height: normal'>
						<span lang=EN-GB style='color: #E7E6E6'>Compteur avec Seuil</span>
					</p>
				</td>
				<td width=85 valign=top
					style='width: 63.95pt; border: solid windowtext 1.0pt; border-left: none; background: #00B0F0; padding: 0.1cm 5.4pt 0cm 5.4pt; height: 25.45pt'>
					<p class=MsoNormal align=center
						style='margin-bottom: 0cm; margin-bottom: .0001pt; text-align: center; line-height: normal'>
						<span lang=EN-GB style='color: #E7E6E6'>Dernière Exécution</span>
					</p>
				</td>
			</tr>
<#list scenarios as scenario>
			<tr style='height: 33.6pt'>
				<td width=115 valign=top
					style='width: 86.4pt; border: dotted windowtext 1.0pt; border-top: none; padding: 0.1cm 5.4pt 0cm 5.4pt; height: 33.6pt'>
					<p class=MsoNormal align=center
						style='margin-bottom: 0cm; margin-bottom: .0001pt; text-align: center; line-height: normal'>
						<span lang=EN-GB>${scenario.scenarioTitle}</span>
					</p>
				</td>
				<td width=262 valign=top
					style='width: 196.7pt; border-top: none; border-left: none; border-bottom: dotted windowtext 1.0pt; border-right: dotted windowtext 1.0pt; padding: 0.1cm 5.4pt 0cm 5.4pt; height: 33.6pt'>
					<p class=MsoNormal align=center
						style='margin-bottom: 0cm; margin-bottom: .0001pt; text-align: center; line-height: normal'>
						<span lang=EN-US
							style='font-family: Consolas; color: black; background: white'>${scenario.scenarioDescription}</span>
					</p>
				</td>
				<td width=79 valign=top
					style='width: 58.95pt; border-top: none; border-left: none; border-bottom: dotted windowtext 1.0pt; border-right: dotted windowtext 1.0pt; padding: 0.1cm 5.4pt 0cm 5.4pt; height: 33.6pt'>
					<p class=MsoNormal align=center
						style='margin-bottom: 0cm; margin-bottom: .0001pt; text-align: center; line-height: normal'>
						<span lang=EN-GB>${scenario.scenarioCategory}</span>
					</p>
				</td>
				<td width=84 valign=top
					style='width: 62.7pt; border-top: none; border-left: none; border-bottom: dotted windowtext 1.0pt; border-right: dotted windowtext 1.0pt; padding: 0.1cm 5.4pt 0cm 5.4pt; height: 33.6pt'>
					<p class=MsoNormal align=center
						style='margin-bottom: 0cm; margin-bottom: .0001pt; text-align: center; line-height: normal'>
						<span lang=EN-GB>${scenario.entityId}</span>
					</p>
				</td>
				<td width=79 valign=top
					style='width: 58.9pt; border-top: none; border-left: none; border-bottom: dotted windowtext 1.0pt; border-right: dotted windowtext 1.0pt; padding: 0.1cm 5.4pt 0cm 5.4pt; height: 33.6pt'>
					<p class=MsoNormal align=center
						style='margin-bottom: 0cm; margin-bottom: .0001pt; text-align: center; line-height: normal'>
						<span lang=EN-GB>${scenario.currencyCode}</span>
					</p>
				</td>
				<td width=72 valign=top
					style='width: 53.9pt; border-top: none; border-left: none; border-bottom: dotted windowtext 1.0pt; border-right: dotted windowtext 1.0pt; padding: 0.1cm 5.4pt 0cm 5.4pt; height: 33.6pt'>
					<p class=MsoNormal align=center
						style='margin-bottom: 0cm; margin-bottom: .0001pt; text-align: center; line-height: normal'>
						<span lang=EN-GB>${scenario.scenarioCount}</span>
					</p>
				</td>
				<td width=85 valign=top
					style='width: 63.95pt; border-top: none; border-left: none; border-bottom: dotted windowtext 1.0pt; border-right: dotted windowtext 1.0pt; padding: 0.1cm 5.4pt 0cm 5.4pt; height: 33.6pt'>
					<p class=MsoNormal align=center
						style='margin-bottom: 0cm; margin-bottom: .0001pt; text-align: center; line-height: normal'>
						<span lang=EN-GB>${scenario.scenarioCountOverT}</span>
					</p>
				</td>
				<td width=85 valign=top
					style='width: 63.95pt; border-top: none; border-left: none; border-bottom: dotted windowtext 1.0pt; border-right: dotted windowtext 1.0pt; padding: 0.1cm 5.4pt 0cm 5.4pt; height: 33.6pt'>
					<p class=MsoNormal align=center
						style='margin-bottom: 0cm; margin-bottom: .0001pt; text-align: center; line-height: normal'>
						<span lang=EN-GB>${scenario.lastRun}</span>
					</p>
				</td>
			</tr>
		
</#list>
		</table>

		<p class=MsoNormal>
			<span lang=EN-GB>&nbsp;</span>
		</p>

		<p class=MsoNormal>
			<span lang=EN-GB>&nbsp;</span>
		</p>

		<p class=MsoNormal>
			<span style='color: #002060'>Cet email est envoyé
				automatiquement par l'application Smart Predict. Si vous voulez vous
				désinscrire, veuillez consulter votre administrateur de
				l'application.</span>
		</p>

		<p class=MsoNormal
			style='margin-bottom: 0cm; margin-bottom: .0001pt; line-height: normal'>
			<span
				style='font-size: 12.0pt; font-family: "Times New Roman", "serif"'>&nbsp;</span>
		</p>

		<div class=MsoNormal align=center
			style='margin-bottom: 0cm; margin-bottom: .0001pt; text-align: center; line-height: normal'>
			<span
				style='font-size: 7.0pt; font-family: "Arial", "sans-serif"; color: #AEAAAA'>

				<hr size=2 width="861" align=left>

			</span>
		</div>

		<p class=MsoNormal
			style='margin-bottom: 0cm; margin-bottom: .0001pt; line-height: normal'>
			<span
				style='font-size: 7.0pt; font-family: "Arial", "sans-serif"; color: #AEAAAA'>Notice
				Légale:<br> Les informations contenues dans ce message
				électronique généré par Smart Predict sont privés, confidentiels et
				uniquement destinés à leur destinataire.
			</span>
		</p>

		<p class=MsoNormal
			style='margin-bottom: 0cm; margin-bottom: .0001pt; line-height: normal'>
			<span
				style='font-size: 7.0pt; font-family: "Arial", "sans-serif"; color: #AEAAAA'>Si
				vous recevez ce message par erreur, vous êtes avisé que toute
				divulgation, reproduction, distribution ou utilisation de ce message
				est strictement interdite.</span>
		</p>

		<p class=MsoNormal
			style='margin-bottom: 0cm; margin-bottom: .0001pt; line-height: normal'>
			<span
				style='font-size: 7.0pt; font-family: "Arial", "sans-serif"; color: #AEAAAA'>S'il
				vous plaît informer l'expéditeur par transmission de la réponse et
				de supprimer le message sans les copier ou de l'ouvrir.</span>
		</p>

	</div>

</body>

</html>
