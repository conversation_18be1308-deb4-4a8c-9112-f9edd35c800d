<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@page import="org.swallow.util.SwtConstants"%>
<%@ include file="/taglib.jsp"  %>
<%@ include file="/angularJSUtils.jsp"%>
<%@ taglib uri="/WEB-INF/tags-swallow" prefix="swallow" %>
<%@ page import="org.swallow.mfa.RegisterMFA"  %>
<meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
<meta http-equiv="Pragma" content="no-cache">
<meta http-equiv="Expires" content="0">
<header>
<script>
	// Clear session storage at the start of the page load
	function clearSessionStorage() {
		sessionStorage.removeItem('passwordChangeStatus');
		sessionStorage.removeItem('changepasswordadlertsent');
		sessionStorage.removeItem('changepassworddays');
	}

	
	function on_init(){
		<% 
			Boolean dfaEnabled = false;
			String dfaEnabledProperty = PropertiesFileLoader.getInstance().
						getPropertiesValue(SwtConstants.PROPERTY_DFA_ENABLED);
			if (dfaEnabledProperty != null) {
				dfaEnabled = dfaEnabledProperty.equalsIgnoreCase("TRUE");
			}
		%>
	}
</script>

</header>
<html>
<head>
<title>Login&nbsp;-&nbsp;Smart Predict </title>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">

<style>
table{
   top:90px !important;
}
</style>
<style id="antiClickjack">body{display:none !important;}</style>
<script type="text/javascript">
   if (self === top) {
       var antiClickjack = document.getElementById("antiClickjack");
       antiClickjack.parentNode.removeChild(antiClickjack);
   } else {
       top.location = self.location;
   }
</script>
<SCRIPT language="JAVASCRIPT">
var errorsAtLogin = '${actionError}';

/* START : Code modified at onsight to resolve the issue of 'Already Loged-in' date 4/6/2007 */
// Code for new flag submitStatus has been added in following block. 
var submitStatus = 0;
/*Added to apply style on mandatory field ,if it's equal to undefined then no field will be colored*/
mandatoryFieldsArray= "undefined" ;
var screenRoute = "jsAngularBridge";
function submitForm(){
	<% session=request.getSession(); %>
	var sessionId='<%=session.getId()%>';	
	if(submitStatus == 0 ){
	//<!-- START:- CODE ADDED FOR DEFECT NO.675 IN MANTIS TO SOLVE THE NULL POINTER EXCEPTION IN LOGINACTION BY THIRUMURUGAN ON 21-08-08-->
		var elementsRef = new Array(2);
		elementsRef[0]=document.forms[0].elements["user.id.userId"];
		elementsRef[1]=document.forms[0].elements["user.password"];
		<% if (dfaEnabled) {%>
		<!-- Added by Saber Chebka for Mantis 1671: Enabling DFA -->
		elementsRef[2]=document.forms[0].elements["user.secureId"];
		<%}%>
		
		instanceElement.prelogin(document.forms[0].elements["user.id.userId"].value);
		
		if(validate(elementsRef))
		{
			document.forms[0].elements['user.id.userId'].disabled=false;
			document.forms[0].elements['user.password'].disabled=false;
			//<!-- Mantis 2077: encrypt the password and set it in the hidden variable-->
			   try {
					document.forms[0].elements["encpasswd"].value=instanceElement.encrypt(sessionId,document.forms[0].elements["user.id.userId"].value,document.forms[0].elements["user.password"].value);
					document.forms[0].elements["clientSession"].value=sessionId;
					  history.pushState(null, '', window.location.href);
			document.forms[0].submit();
			   }catch(e){
				    alert('<s:text name="login.html.internalerror" />'+' '+e.message);				
			    }	

			<s:if test='"C"==#request.dfastatus' >
				document.forms[0].elements['user.id.userId'].disabled=true;
				document.forms[0].elements['user.password'].disabled=true;
			</s:if>
		 //TODO: submitStatus = 1;
		 //TODO: document.forms[0].submit();
		
}
	 //<!-- END:- CODE ADDED FOR DEFECT NO.675 IN MANTIS -->
	}
}

function focusPassword(e){
	var event = (window.event || e);
	<% if (dfaEnabled) {%>
	if(event.keyCode == 13){
		document.forms[0].elements['user.secureId'].focus();
	}
	<%} else {%>
	 if(event.keyCode == 13 && submitStatus == 0){
		 submitForm();
		}
	<%}%>
}

function focusOnPassword(e){
	var event = (window.event || e);
	if(event.keyCode == 13){
		document.forms[0].elements['user.password'].focus();
	}
}

/* END : Code modified at onsight to resolve the issue of 'Already Logged-in' date 4/6/2007 */

//<!-- Added by Saber Chebka for Mantis 1671: Enabling DFA -->
function focusOnSecureId(e)
{
	var event = (window.event|| e);
	if(event.keyCode == 13 && submitStatus == 0){
		 submitForm();
	}
}

function enableFields()
{
	if (document.forms[0].elements['user.password'].disabled==true) {
		document.forms[0].elements["user.password"].value="";
		document.forms[0].elements['user.id.userId'].disabled=false;
		document.forms[0].elements['user.password'].disabled=false;
	} else {
		window.close();
	}
	
}



// Modified bodyOnload to handle initialization
function bodyOnload() {
    if (!window.performance.navigation.type) {
        initializePageState();
    }
}

// Separated initialization logic
function initializePageState() {
    clearSessionStorage();

    var appName = "<%=SwtUtil.appName%>";
    var requestURL = new String('<%=request.getRequestURL()%>');
    var idy = requestURL.indexOf('/'+appName+'/');
    requestURL=requestURL.substring(0,idy+1)
    var oXMLHTTP = new XMLHttpRequest();
    var sURL = requestURL + appName + "/logon.do?method=preLoginScreenData";
    oXMLHTTP.onreadystatechange = function() {
        if (this.readyState == 4 && this.status == 200) {
            var str=this.responseText;
        }
    };
    oXMLHTTP.open("POST", sURL, false);
    oXMLHTTP.send();

    if ("${isExpiredSession}" == "true" && "${message}" != "undefined") {
        alert("${message}");
    }
}

// Add event listeners for page show and navigation
window.addEventListener('pageshow', function(event) {
    // Check if the page is being shown from bfcache
    if (event.persisted) {
        clearSessionStorage();
        initializePageState();
    }
});

// Handle back/forward navigation
window.addEventListener('popstate', function(event) {
    clearSessionStorage();
    initializePageState();
});

function killYes() {
	   document.forms[0].elements['killSession'].value='Y';
	submitForm(); 
}
function killNo() {
	   document.forms[0].elements['user.id.userId'].disabled=false;
		document.forms[0].elements['user.password'].disabled=false;	
		document.forms[0].elements["user.id.userId"].value="";
		document.forms[0].elements["user.password"].value ="";
		<% if (dfaEnabled) {%>
			document.forms[0].elements["user.secureId"].value="";
		<% }%>
}


function doRedirectToPreLogin(isLogoutRedirect){
	var appName = "<%=SwtUtil.appName%>"; //Application name is picked up from property file
	var requestURL = new String('<%=request.getRequestURL()%>');
	var idy = requestURL.indexOf('/'+appName+'/');
	requestURL=requestURL.substring(0,idy+1) ;
	
	var sURL = requestURL + appName+"/logon.do?method=preLoginScreen&isLogoutRedirect="+isLogoutRedirect+"&killSessionStatus="+"${killSessionStatus}"+"&mfaUserId="+"${mfaUserId}"+"&errorsAtLogin="+errorsAtLogin;
	window.location.replace(sURL);
}
var isUseSmartAuthenticator = "<%=RegisterMFA.getInstance().isUseSmartAuthenticator()%>";

if(isUseSmartAuthenticator == "true"){
	var isFromPreLoginScreen = "${isFromPreLoginScreen}";
	var isLogoutRedirect = "${isLogoutRedirect}";
	if(isFromPreLoginScreen != "true"){
		doRedirectToPreLogin(isLogoutRedirect);
	}
}


</script>
</head> 
<body class="login" leftmargin="0" topmargin="0" marginwidth="0" marginheight="0"
    onLoad="clearPreLogin();ShowErrMsgWindow('${actionError}');setFocus(document.forms[0]);bodyOnload();"
    onpageshow="if (event.persisted) window.location.reload();">
<%@ include file="/angularscripts.jsp"%>
<form action="logon.do?">
	<!-- Mantis 2183: Added by M.Bouraoui to pass the hidden session id  "clientSession" -->
    <input type="hidden" name="clientSession"/>
                   
	<input type=hidden name="method" value="login" />
	
	<!-- Added by Saber Chebka to pass the hidden variable "challenge" -->
	<input type="hidden" name="challenge" value="N"/>
	
	<!-- Added by Meftah Bouazizi to pass the hidden password "encpasswd" -->
    <input type="hidden" name="encpasswd" />
	
	<!-- Added by Meftah & Kais to pass the hidden variable "killSession" -->
	<input type="hidden" name="killSession" value="N"/>
   <input type="hidden" name="auth"  value="${mfaToken}" />
    <input type="hidden" name="mfaLogin"  value="${mfaLogin}" />
	
  <table width="100%" cellpadding="0" cellspacing="0" border="0" style="position:absolute; top:20px;">
	<tr >
	  <td align="left" width="30%" valign="top">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<img src="images/swallow2.gif" ></td>
	  <td align="center" width="40%">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</td>
	  <td align="right" width="30%" valign="bottom">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</td>
    </tr>
  	</table>
<%-- Height attribute changed to accomodate added text message --%>
<!--Start Code change  for  login screen center allignment-->
<div align=center>
	<% if (dfaEnabled) {%>
	<div  id="Login" border="1" style="position:relative; background:#E8F3FE;  top:167px; width:400px; height:265px;border: 2px outset;" color="#BAE7FE" >
	<% } else {%>
    <div  id="Login" border="1" style="position:relative; background:#E8F3FE;  top:167px; width:400px; height:240px;border: 2px outset;" color="#BAE7FE" >
	<%}%>

                <div  id="Login" style="position:absolute; left:7px; top:20px; width:385px; height:220px;">
                 <table class="content" width="363" height="20" bgcolor="#1F63AA" border="0">
                        <tr align="center" >
                            <td  width="100%" colspan=3>
                         <b><font color="#FFFFFF" size=2>&nbsp;</font></b></td>
                        </tr>

					<!-- Title -->
                        <tr align="left" border="white">
                            <td colspan=3>
                         <b><font color="#FFFFFF" size=4>&nbsp;&nbsp;&nbsp;&nbsp;<s:text name="user.titletext"/></font></b><br><br><br>
                            </td>
                        </tr>
                        
	                <!-- User ID label and text input -->
                        <tr>
                        <td align="left" width="6%">&nbsp;</td>
                        <td align="left" width="30%"><font color="#FFFFFF" size=2><b><s:text name="user.id"/>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</b></font></td>
                            <td  width="59%" align="left">
                            	<% if (dfaEnabled) { %>
									<s:textfield cssStyle="width:76%" name="user.id.userId" id="user.id.userId" label="user.askForUserID" tabindex="1" onkeydown="focusOnPassword(event)" />
								<% } else { %>
									<s:textfield cssStyle="width:69%;height:22px;" name="user.id.userId" id="user.id.userId" label="user.askForUserID" tabindex="1" onkeydown="focusOnPassword(event)" />
								<% } %>
                            </td>
                        </tr>
					
					<!-- password label and text input -->
                        <tr>
                        <td align="left" width="6%">&nbsp;</td>
                        <td align="left" width="30%">
                        <font color="#FFFFFF" size=2><b><label for="user.password"><s:text name="user.password"/></label>&nbsp;</b></font></td>
                        <td  width="59%">
                        	<!-- Mantis 2077: Change the password tag from html:password into input tag, Reason: If we don't give a name for the input element, it will not be submitted into the form, then the clear password will not be sent to server. START-->
                        	<% if (dfaEnabled) { %>
                        		<input type="password" id="user.password" style="width:76%"  title="<s:text name='user.askForPassword'/>" tabindex="2" onkeydown="focusPassword(event)"/>
                        	<% } else { %>
                        	    <input type="password" id="user.password" style="width:69%;height:22px;" title="<s:text name='user.askForPassword'/>" tabindex="2"  onkeydown="focusPassword(event)">
                        	<% } %>
                       		<!-- Mantis 2077: Change the password tag from html:password into input tag, Reason: If we don't give a name for the input element, it will not be submitted into the form, then the clear password will not be sent to server. END-->                         	
                        </td>
                    </tr >
					
					<% if (dfaEnabled) { %>
					<!-- secure ID label and text input -->
					<tr id="secureID">
						<td align="left" width="6%">&nbsp;</td>
						<td align="left" width="25%">
							<font color="#FFFFFF" size=2><b>
								<label for="user.secureId"><s:text name="user.secureId"/>
								</label>
								&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
							</b></font>
						</td>
						<td width="59%">
							<s:password style="width:76%"
										name="user.secureId"
										titleKey="login.user.secureId"
										tabindex="3"
										onkeydown="focusOnSecureId(event)"/>

						</td>
					</tr>
					<%} %>
                    <!-- Code changed -->
                    <tr>
                    <td>&nbsp;
                    </td>
                    </tr>
                    <tr>
                        <td align="left" width="6%">&nbsp;</td>
                        <td align="center" colspan=2>
                          <font color="#FFFFFF" size=2><b><s:text name="user.accessmessage"/><b></font>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<br>
                      </td>
                    </tr>
                  </table>
                </div>
			<!-- buttons -->
			<% if (dfaEnabled) {%>
			<div id="ddimagebuttons" style="position:absolute; right: 117px; top:222px; width:147px; height:10px;  ">
	    	<% } else {%>
                <div id="ddimagebuttons" style="position:absolute; right: 117px; top:198px; width:147px; height:10px;  ">
	    	<% } %>
                        <table border="0" cellspacing="0"  cellpadding="0"  width="138">
                        <tr align="center" >
                            <td width="49%"><a title="<s:text name='tooltip.clickLogin'/>" tabindex="3" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" name="submit" onClick="submitForm()"><s:text name="user.login"/></a></td>
							<td width="51%">
								<s:if test='"C"==#request.secureidchallenged' >
								<a title="<s:text name='tooltip.clickBack'/>" tabindex="4"
									onMouseOut="collapsebutton(this)"
									onMouseOver="highlightbutton(this)"
									onMouseDown="expandbutton(this)"
									onMouseUp="highlightbutton(this)"
									onClick="enableFields()"><s:text name="button.cancel"/></a>
							</s:if>
							<s:if test='"C"!=#request.secureidchallenged' >
								<a title="<s:text name='tooltip.closeWindow'/>" tabindex="4"
									onMouseOut="collapsebutton(this)"
									onMouseOver="highlightbutton(this)"
									onMouseDown="expandbutton(this)"
									onMouseUp="highlightbutton(this)"
									onClick="javascript:window.close()"><s:text name="button.cancel"/></a>
							</s:if>
						</td>
                        </tr>
                  </table>
               </div>
              <div style="position:absolute;bottom:-15px ;left:30px;">
                  <font size=1><b><s:text name="user.copyright"/> 
              </div>
              <div style="position:absolute;bottom:-100px;right:-280px;float:right;" > 
               <img src="images/banklogo.gif">
      </div>
    </div>
</div>
<!--End Code change  for  login screen center allignment-->
</form>
</body>
<script>
document.forms[0].elements['user.password'].value="";
	<% if (dfaEnabled) {%>
		document.forms[0].elements['user.secureId'].value="";

	<% }%>

</script>

<s:if test='"Y"==#request.changepassword' >
    <script>
        clearSessionStorage();
        sessionStorage.setItem('passwordChangeStatus', 'Y');
        sessionStorage.setItem('changepasswordadlertsent', 'Y');
        alert('<s:text name="alert.passwordExpired" />');
        document.forms[0].elements['user.password'].value = "";
        window.open('changepassword.do?screen=logon', '_self');
    </script>
</s:if>


<s:if test='"E"==#request.changepassword' >
    <script>
        clearSessionStorage();
        sessionStorage.setItem('changepasswordadlertsent', 'Y');
        sessionStorage.setItem('passwordChangeStatus', 'E');
        sessionStorage.setItem('changepassworddays', ${changepassworddays});
        if (window.confirm('<s:text name="alert.passwordExpiresInFewDaysPart1" />'+${changepassworddays}+'<s:text name="alert.passwordExpiresInFewDaysPart2" />')){
            document.forms[0].elements['user.password'].value="";
            window.open('changepassword.do?screen=logon','_self');
        } else {
            clearSessionStorage();
            window.open('logon.do?method=login&logonflag=afterlogon','_self');
        }
    </script>
</s:if>

<!-- START:- CODE CHANGED FOR DEFECT NO.39 IN MENTISS -->

<s:if test='"N"==#request.changepassword' >
    <script>
        clearSessionStorage();
        sessionStorage.setItem('changepasswordadlertsent', 'Y');
        sessionStorage.setItem('passwordChangeStatus', 'N');
        alert('<s:text name="login.newUSer.continue"/>');
        document.forms[0].elements['user.password'].value="";
        window.open('changepassword.do?screen=logon','_self');
    </script>
</s:if>



<!-- START:- CODE CHANGED FOR DEFECT NO.39 IN MENTISS -->

<s:if test='"Y"==#request.redirectToLogin' ><!--For New User-->
	<script>
alert('<s:text name="login.newPage.generated"/>');
</script>
</s:if>




<!-- Start: Refer to Mantis issue : 0000391: Various login issues -->
<!-- If password is 'M'odified using control/user option then on next login this code will be executed -->

<s:if test='"M"==#request.changepassword' >
    <script>
        clearSessionStorage();
        sessionStorage.setItem('changepasswordadlertsent', 'Y');
        sessionStorage.setItem('passwordChangeStatus', 'M');
        alert('<s:text name="login.password.modified"/>');
        document.forms[0].elements['user.password'].value="";
        window.open('changepassword.do?screen=logon&deletePwdHst=y','_self');
    </script>
</s:if>
<!-- End: Refer to Mantis issue : 0000391: Various login issues -->

<s:if test='"F"==#request.dfastatus' >
<script>
document.forms[0].elements["user.password"].value="";
</script>
</s:if>

<!-- Added by Mansour Blanco & Saber Chebka for Mantis 1671, DFA implementation: BEGIN -->
<s:if test='"C"==#request.dfastatus' >
	<!--On this case the secureId has been challenged-->
	<script>
		document.forms[0].elements['user.password'].disabled=true;
		document.forms[0].elements['user.id.userId'].disabled=true;
		document.forms[0].elements['challenge'].value='Y';
		document.forms[0].elements["user.password"].value = "<%=request.getAttribute("dummypass")%>";
		if (!window.confirm('<s:text name="secureid.challenged"/>'))
		{
			enableFields();
		}
	</script>
</s:if>
<!-- Added by Mansour Blanco & Saber Chebka for Mantis 1671, DFA implementation: END -->

<!-- Added by Meftah & KaisBS for Mantis 1650 :killing the existing session and new session will be created on Orphaned or dead sessions are detected     -->
<!-- If killSessionStatus is 'K', the value of killSession variable become Y then on next login the existing session will be killed and new session will be created -->


	<s:if test='"K"==#request.killSessionStatus' >
    <!--On this case the session will be killed and  new session will be created-->
    <script>
    //set the password with the dummy password recuved and make userId and pasword Field disabled:
    document.forms[0].elements['user.password'].disabled=true;
	document.forms[0].elements['user.id.userId'].disabled=true;
	document.forms[0].elements["user.password"].value = "<%=request.getAttribute("dummypass1")%>";
	
	<% if (dfaEnabled) {%>
	document.forms[0].elements["user.secureId"].value="<%=request.getAttribute("dummySecureId")%>";
	<% }%>
	
     ShowErrMsgWindowWithBtn("", "<s:text name='alert.killSession'/>", YES_NO, killYes, killNo);	
   </script>

</s:if>
</html>
