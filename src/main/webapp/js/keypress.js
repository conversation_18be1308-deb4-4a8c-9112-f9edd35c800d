/*
   Milonic DHTML Menu Keypress Module  keypress.js version 1.3 - February 2005
   Used for accessibility of the menu. This module will allow the user to navigate Milonic menus via the keyboard.
   
   This module is only compatible with the Milonic DHTML Menu version 5.16 or higher

   Copyright 2005 (c) Milonic Solutions Limited. All Rights Reserved.
   This is a commercial software product, please visit http://www.milonic.com/ for more information.
*/

function keyAction(_inc)
{
	_itemRef+=_inc
	_popi(_itemRef)
}



function getNextKeyItem()
{
	_Kar=_KcM[0]
	_output=0;
	for(_a=0;_a<_Kar.length;_a++)
	{
		if(_Kar[_a]==_itemRef)_output=_Kar[_a+1]
		if(_output+" "=="undefined ")return _Kar[0]		
	}
	return _output	
	
}

function getPreKeyItem()
{
	_Kar=_KcM[0]
	_output=0;
	for(_a=0;_a<_Kar.length;_a++)
	{
		if(_Kar[_a]==_itemRef)_output=_Kar[_a-1]
		if(_output+" "=="undefined ")return _Kar[_Kar.length-1]		
	}
	return _output
}


function getKeyItem()
{	
	_KcM=_m[_mi[_itemRef][0]]; // this is the array of menu items for the current menu
	_KcI=_mi[_itemRef]
	
	if(jskey==27) // Escape
	{
		closeAllMenus();
		return false;
	}
	
	if(_KcM[9]) // Horizontal
	{	
		if(jskey==37)_popi(getPreKeyItem()) // left
		if(jskey==39)_popi(getNextKeyItem()) //right
		
		if(jskey==38)
		{
			_mn=getMenuByName(_mi[_itemRef][3])
			_popi(_m[_mn][0][_m[_mn][0].length-1])

		}
		if(jskey==40) // down
		{
			_mn=getMenuByName(_mi[_itemRef][3])
			_popi(_m[_mn][0][0])
		}
		
	}
	else       // Vertical
	{
		if(jskey==38)_popi(getPreKeyItem()) // up
		if(jskey==40)_popi(getNextKeyItem()) //down

		if(jskey==37) // left
		{
			//alert(_mi[_itemRef][3])
			if(_mi[_itemRef][3])
			{
				_mn=getMenuByName(_mi[_itemRef][3])
				_popi(_m[_mn][0][_m[_mn][0].length-1])
			}
			else
			{
				_itemRef=getParentItemByItem(_itemRef);
				_KcM=_m[_mi[_itemRef][0]];
				_popi(getPreKeyItem())
				_popi(getNextKeyItem())
				//_popi(_itemRef)
			}
		}
		if(jskey==39) // right
		{
			if(_mi[_itemRef][3])
			{
				_mn=getMenuByName(_mi[_itemRef][3])
				_popi(_m[_mn][0][0])
			}
			else
			{
				_mni=_itemRef
				cnt=0;
				while(_mni!=-1)
				{
					if(_mni)_mni=getParentItemByItem(_mni); else _mni=-1
					if(_mni>-1)_itemRef=_mni					
				}
				_KcM=_m[_mi[_itemRef][0]];
				_popi(getNextKeyItem())
			}
		}
	}
}


KPgChildren="";
function KPcrawlChildren(_mn)
{
	var _ar=_m[_mn][0]
	for(var _am=0;_am<_ar.length;_am++)	
	{
		KPgChildren+=_ar[_am]+",";
		if(_mi[_ar[_am]][3])
		{
			KPcrawlChildren(getMenuByName(_mi[_ar[_am]][3]))
		}
	}
}

KPcrawlChildren(0)
KPgChildren=KPgChildren.split(",");

function getNextInRow()
{
	for(_k=0;_k<KPgChildren.length;_k++)
	{
		if(_itemRef==KPgChildren[_k])
		{
			if(KPgChildren[_k+1])return KPgChildren[_k+1]
		}
	}
}

function getPreInRow()
{
	
	
	for(_k=0;_k<KPgChildren.length;_k++)
	{
		if(_itemRef==KPgChildren[_k])
		{
			if(KPgChildren[_k-1])return KPgChildren[_k-1]
		}
	}
}


_Omo=_menuOpenDelay
_Cmo=_menuCloseDelay

_OiR=-1

KShift=0
KCtrl=0
function getMenuByKey(e)
{
	_ofMT=1
	_menuOpenDelay=0
	_menuCloseDelay=0
	
	if(ns6||ns4){jskey=e.which}else{jskey=event.keyCode}
	if(ns4){jskey=String.fromCharCode(jskey).toUpperCase();jskey=jskey.charCodeAt()}
	
	if(jskey==16)KShift=1
	if(jskey==17)KCtrl=1
	
	//if(KCtrl&&jskey==9)
	//{
		//if(_itemRef==-1)_itemRef=0
		//_itemRef=getPreInRow()
		//jskey=9
	//}	
	
	
	if(jskey==9)
	{
		if(_itemRef<-1)
		{
			return false;
		}
		else
		{
				if(KShift)
				{
					if(_itemRef>-1)
					{
						_itemRef=getPreInRow()
					}
					if(_itemRef==0)closeAllMenus()
				}
				else
				{
					_itemRef=getNextInRow()
				}
				
				
				if(_itemRef>-1)
				{
					_popi(_itemRef);
					_OiR=_itemRef
				}
				else
				{
					itemOff(_OiR)
					return true;
				}
		}
	}
	
	if(_itemRef==-1||_itemRef+" "==$u)
	{
		_rsMD()
		return
	}
	if(_mi[_itemRef][34]=="form")
	{
		_rsMD()
		return
	}


	_cm3()
	getKeyItem()

	if(_itemRef==-1)
	{
		_rsMD()
		return
	}
	

	if(!ns4)
	{
		hrgm=gmobj("mmlink"+_mi[_itemRef][0])
		if(hrgm.style.visibility="visible")hrgm.focus()	
	}
	
	if(ie)
	{
		if(jskey==13)hrgm.click()
		_rsMD()		
		return false
	}
}


function gMBK(e)
{
	if(ns6||ns4){jskey=e.which}else{jskey=event.keyCode}
	if(ns4){jskey=String.fromCharCode(jskey).toUpperCase();jskey=jskey.charCodeAt()}
	if(jskey==13)return true	
	if(_itemRef>-1)if(_mi[_itemRef][34]!="form")return false
}


function getMenuByKeyU(e)
{	
	if(ns6||ns4){jskey=e.which}else{jskey=event.keyCode}
	if(ns4){jskey=String.fromCharCode(jskey).toUpperCase();jskey=jskey.charCodeAt()}
	if(jskey==16)KShift=0
	if(jskey==17)KCtrl=0
	_rsMD()
}

function _rsMD()
{
	_ofMT=0
	_menuOpenDelay=_Omo
	_menuCloseDelay=_Cmo
}


function _iF0C(_i)
{
	_popi(_i)
}

if(ns4)document.captureEvents(Event.KEYDOWN);
_d.onkeydown=getMenuByKey;
_d.onkeypress=gMBK;
_d.onkeyup=getMenuByKeyU;
