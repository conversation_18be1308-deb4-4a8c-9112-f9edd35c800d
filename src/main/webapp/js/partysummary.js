function unlockRemaining(mvmntList){
	var list=mvmntList.split(",");
	if(mvmntList != ""){
		for(var i=0;i<list.length;i++){
			unlockMovementOnServer(list[i]);
		}
	}
}

function unloadMovementWindow()
{
	//alert("inside unloadMovementWindow");
	if(calledFromNext != "true"){
		unlockRemaining(selectedMvmtList);
		unlockSelected();
	}
	//alert("isRefresfFromChild - " + isRefresfFromChild);
	if(isRefresfFromChild != "false")
	{
		isDoNotCloseMyChilds = true;
	}
	unloadMyWindow();
}
function call2(){
	unlockSelected();
	if(isRefresfFromChild == "false")
	{
		call();
	}
}
function refreshMovmentNote(movementId,noteValue)
{
	document.forms[0].isNotesPresent.value = noteValue;
	var dataTable = document.getElementById("custodianColl");
	if(dataTable != null && dataTable != 'undefined')
	{
		if(dataTable.tBodies != null && dataTable.tBodies != 'undefined')
		{
			var elements = document.getElementsByName("id.movementId");
			for(var idx = 0 ; idx < elements.length; ++idx)
			{
				var element = elements[idx];
				if(element.value == movementId)
				{
					var trElement = element.parentElement;
					while(trElement.tagName != 'TR')
						trElement = trElement.parentElement;
					if(trElement != null && trElement != 'undefined')
					{
						//trElement.cells[14].innerText =noteValue;
						trElement.cells[1].innerText =noteValue;
						var selectId = "custodianColl__select_9";
						var selectElm = document.getElementById(selectId);

						//xl.sortedData[8] = sortValues(xl.dataTable, 8);
						//selectElm.populate(xl.sortedData[8], 8);					
					}
					break;
				}
			}
			
		}
	}

}
function movementNotes(methodName){
	getSelectedMovementId() ;
	var param = 'notes.do?method='+methodName+'&movId=';			 
     param += document.forms[0].selectedMovementId.value;
     param += '&entityCode=' + document.forms[0].elements['movement.id.entityId'].value;
	 param += '&screenName='+'outstanding';
	 param += '&isNotesPresent='+document.forms[0].isNotesPresent.value;
	 param += '&currencyCode='+currencyId;
	 param += '&date='+dateStr;
     return  param;
}

function onFilter(){
	unlockSelected();	
	updateColors();
}
function enableFields(){
	document.forms[0].elements["movement.id.entityId"].disabled = "";
}
function unlockSelected(){
	 var table = document.getElementById("custodianColl"); 
	
	 var tbody = table.getElementsByTagName("tbody")[0];    
	 var rows = tbody.getElementsByTagName("tr");
	 var selectedList = "";
	 var columns;

	 for (i=0; i < rows.length; i++) 
	 {
		if( isRowSelected(rows[i])){
		
		unlockMovementOnServer(rows[i].cells[2].innerText);
		
		}
	 } 		
}
function getSelectedList(){
	var table = document.getElementById("custodianColl");  
	var tbody = table.getElementsByTagName("tbody")[0];    
	var rows = tbody.getElementsByTagName("tr");
	var selectedList = "";
	var columns;
	for (i=0; i < rows.length; i++) 
 	{
		selectedList = selectedList + rows[i].cells[2].innerText.trim()+",";
 	}  	
 	document.forms[0].selectedList.value = selectedList; 	
}
function calculateRowsSelected(){
	var table = document.getElementById("custodianColl"); 
	var tbody = table.getElementsByTagName("tbody")[0];    
	var rows = tbody.getElementsByTagName("tr");
	var columns;
	var rowsSelected=0;
	for (i=0; i < rows.length; i++) 
	{
		if( isRowSelected(rows[i])){
		rowsSelected=rowsSelected+1;
	}
}  		
	 if(rowsSelected>=(rows.length-1))
	{
		return true;
	}else
		return false;
}
function calculateMultipleRowsSelected(){
	var table = document.getElementById("custodianColl"); 
	var tbody = table.getElementsByTagName("tbody")[0];    
	var rows = tbody.getElementsByTagName("tr");
	var columns;
	var rowsSelected=0;
	for (i=0; i < rows.length; i++) 
	{
		if( isRowSelected(rows[i])){
		rowsSelected=rowsSelected+1;
	}
}  		
	if(rowsSelected=='1'){
		return true;
	}else
	{
		return false;
	}
}


function enableDisableMovementNotesBtns(count)
{
	if(count == 1) // enable buttons
	{
		document.getElementById("notesbutton").innerHTML = document.getElementById("notesenablebutton").innerHTML;
		document.getElementById("mvmntbutton").innerHTML = document.getElementById("mvmntenablebutton").innerHTML;
		<!--Start: Refer to Mantis issue no.135 -->
		setMsgButtonStatus();
		<!--End: Refer to Mantis issue no.135 -->
	}
	else // disable buttons
	{
		document.getElementById("notesbutton").innerHTML = document.getElementById("notesdisablebutton").innerHTML;
		document.getElementById("mvmntbutton").innerHTML = document.getElementById("mvmntdisablebutton").innerHTML;
		document.getElementById("msgbutton").innerHTML = document.getElementById("msgdisablebutton").innerHTML;

	}
	
	var table = document.getElementById("custodianColl"); 
	var tbody = table.getElementsByTagName("tbody")[0];    
	var rows = tbody.getElementsByTagName("tr");
	var columns;
	var rowsSelected=0;
	for (i=0; i < rows.length; i++) 
	{
		if( isRowSelected(rows[i]))
		{
			document.forms[0].isNotesPresent.value = new String(rows[i].cells[1].innerText).trim().valueOf();
			rowsSelected=rowsSelected+1;
		}

	}
  		
	if(rowsSelected !='1'){
		document.forms[0].isNotesPresent.value = "";

	}


}


function onSelectTableRow(rowElement,isSelected){

if(!isSelected){	
	document.getElementById("removebutton").innerHTML = document.getElementById("removedisablebutton").innerHTML;
}else{
	document.getElementById("removebutton").innerHTML = document.getElementById("removeenablebutton").innerHTML;
}

}

function remove(){
//alert("in remove");
var rowElement;
var mvmnt;
	var table = document.getElementById("custodianColl");  
	var tbody = table.getElementsByTagName("tbody")[0];    
	var rows = tbody.getElementsByTagName("tr");
	 

	 for(var i = 0 ; i < rows.length ; ++i)
	{
	   if(isRowSelected(rows[i]) ){
		 rowElement	=rows[i];
		mvmnt= rows[i].cells[2].innerText.trim();
//		alert(mvmnt);

			
			deleteRowFromSelection("custodianColl", rowElement);
	   }
	}
	

var table1 = document.getElementById("custodianColl");  
	var tbody1 = table1.getElementsByTagName("tbody")[0];    
	var rows1 = tbody1.getElementsByTagName("tr");
	 
	for(var k = 0 ; k < rows1.length ; ++k)
	{
	
	   if(mvmnt==rows1[k].cells[2].innerText.trim()){
		unHighLightTableRow(rows1[k]);
	   }
	}
	unlockMovementOnServer(mvmnt);
	document.getElementById("removebutton").innerHTML = document.getElementById("removedisablebutton").innerHTML;
}


function addRowToSelection(tableId, rowElement){
	
	

	var table1 = document.getElementById(tableId); 
	var tbody1 = table1.getElementsByTagName("tbody")[0];    
	newrow = tbody1.insertRow();
	//alert('rows.length '+tbody1.rows.length);

	if(tbody1.rows.length%2 == 0){
		newrow.className = "odd";
	}else {
		newrow.className = "even";
	}
	/* Add Hidden elements in tr while adding a new row in the lower table */
	var mvmntId = rowElement.getElementsByTagName("input")[0].value;	
	var hiddenElement1 = document.createElement("<INPUT type='hidden' name='id.movementId' value='"+ mvmntId+"'>");
	document.body.insertBefore(hiddenElement1);
	var posLvlId = rowElement.getElementsByTagName("input")[1].value;
	var hiddenElement2 = document.createElement("<INPUT type='hidden' name='positionLevel' value='"+ posLvlId+"'>");
	document.body.insertBefore(hiddenElement2);
	
	newrow.appendChild(hiddenElement1);
	newrow.appendChild(hiddenElement2);
	
	
	for (var j = 0; j < 4; j++) 
	{
		var cell = newrow.insertCell(j);
		cell.innerHTML = rowElement.cells[j].innerHTML;
		cell.width = rowElement.cells[j].width;
		cell.style.color = rowElement.cells[j].style.color;
		cell.align = rowElement.cells[j].align;
	}
	
	
	var previousClass = null;
    var table = document.getElementById(tableId); 
    var tbody = table.getElementsByTagName("tbody")[0];
    var rows = tbody.getElementsByTagName("tr");
    // add event handlers so rows light up and are clickable
    for (i=0; i < rows.length; i++) 
	{
		  		rows[i].onclick = selectTableRow
		
    }
}

function deleteRowFromSelection(tableId, rowElement){
	//alert();
	var table1 = document.getElementById(tableId); 
	var tbody1 = table1.getElementsByTagName("tbody")[0];
	var rows = tbody1.getElementsByTagName("tr");
	var mvmtId = rowElement.cells[2].innerText;
	var index = -1;
	for(var i=0 ; i<rows.length; i++){
	   if(rows[i].cells[2].innerText == mvmtId){
	      index = i;
		  break;
	   }
	}
	tbody1.deleteRow(index);
	for( j=index ; j<rows.length; j++){
	   if(j%2 == 0){
	      rows[j].className = "even";
	   }else{
	   	  rows[j].className = "odd";
	   }
	}
}

function getCountRowsSelectedFromTable(tableId, checkCondition){
 var table1 = document.getElementById(tableId); 
 var tbody1 = table1.getElementsByTagName("tbody")[0];   
 var rows = tbody1.rows;
 var count = 0 ; 
 for(var i = 0 ; i < rows.length ; ++i)
	{

	 if(tableId == "custodianColl"){
		if(isRowSelected(rows[i])){
			 ++count;
			 
		 }
	 }else{
	   if(checkCondition == true || isRowSelected(rows[i]) ){
			++count;
	   }
	 }
	}
 return count;
}

function maintainSortFilterStatus(){
	var sortColumn = xl.dataTable.sortColumn ;
	document.forms[0].selectedSortStatus.value = sortColumn ;

	var sSortDescending =  xl.dataTable.descending;		
	document.forms[0].selectedSortDescending.value = sSortDescending ;
	
	var filterArr = new Array(16);
	filterArr = xl.getFilterStatus();

	for(var idy = 0 ; idy < filterArr.length-1; ++idy)	
	document.forms[0].selectedFilterStatus.value += filterArr[idy] + "," ;
	var movId = xl.getNextUnselectedRow();
	document.forms[0].selectedNextMovId.value = movId ;
}


/* Start  Added for Smart Predict Phase II SRS 'Recon' button */
function reconcile(methodName){		
		enableFields();
		document.forms[0].method.value = methodName;				
		getSelectedList();
		document.forms[0].selectedTab.value=tabPressed;
		document.forms[0].selectedCurrencyCode.value=currencyId;
	
	/* Start: Refer to mail From: Antony Harris Sent: Thursday, March 29, 2007 2:59 PM
        To: Singh, Mantosh   Subject: DEFECTS/ENHANCEMENTS: Excluded Outstandings */
		var scrollTable=document.getElementById('ddscrolltable');
		document.forms[0].tableScrollbarLeft.value = scrollTable.scrollLeft;
		document.forms[0].tableScrollbarTop.value = scrollTable.scrollTop;

		document.forms[0].scrollbarLeft.value = document.body.scrollLeft;
		document.forms[0].scrollbarTop.value = document.body.scrollTop;
	/* End: Refer to mail From: Antony Harris Sent: Thursday, March 29, 2007 2:59 PM
        To: Singh, Mantosh   Subject: DEFECTS/ENHANCEMENTS: Excluded Outstandings */

		document.forms[0].submit();
} 
/* End  Added for Smart Predict Phase II SRS 'Recon' button */

function offeredMatch(methodName){
		//maintainSortFilterStatus();
			var scrollTable=document.getElementById('ddscrolltable');	
		enableFields();
		document.forms[0].method.value = methodName;
		//document.forms[0].selectedList.value = getSelectedList();
		getSelectedList();
		document.forms[0].selectedTab.value=tabPressed;
		document.forms[0].selectedCurrencyCode.value=currencyId;
		var inputFrom="offeredMatch";
		var param = 'movementmatchdisplay.do?method='+methodName+'&selectedList=';
		param += document.forms[0].selectedList.value;
		param += '&selectedTab=' + document.forms[0].selectedTab.value;
		param +='&selectedCurrencyCode=';
		param +=document.forms[0].selectedCurrencyCode.value;
		param += '&entityCode=' + document.forms[0].elements['movement.id.entityId'].value;

		/*Start: Refer to mail From: Antony Harris Sent: Thursday, March 29, 2007 2:59 PM
        To: Singh, Mantosh   Subject: DEFECTS/ENHANCEMENTS: Excluded Outstandings */
		param += '&currentPage=' + document.forms[0].elements['currentPage'].value;	
		param += '&tableScrollbarLeft=' + scrollTable.scrollLeft;
		param += '&tableScrollbarTop=' + scrollTable.scrollTop;
		param += '&scrollbarLeft=' + document.body.scrollLeft;
		param += '&scrollbarTop=' + document.body.scrollTop;
		/* End: Refer to mail From: Antony Harris Sent: Thursday, March 29, 2007 2:59 PM
        To: Singh, Mantosh   Subject: DEFECTS/ENHANCEMENTS: Excluded Outstandings */
		
		return  param;
} 

function suspend(methodName){
	//	maintainSortFilterStatus();
		enableFields();
		document.forms[0].method.value = methodName;		
		//document.forms[0].selectedList.value = getSelectedList();
		getSelectedList();
		document.forms[0].selectedTab.value=tabPressed;
		document.forms[0].selectedCurrencyCode.value=currencyId;
		
		/* Start: Refer to mail From: Antony Harris Sent: Thursday, March 29, 2007 2:59 PM
        To: Singh, Mantosh   Subject: DEFECTS/ENHANCEMENTS: Excluded Outstandings */
		var scrollTable=document.getElementById('ddscrolltable');	
		document.forms[0].tableScrollbarLeft.value = scrollTable.scrollLeft;
		document.forms[0].tableScrollbarTop.value = scrollTable.scrollTop;

		document.forms[0].scrollbarLeft.value = document.body.scrollLeft;
		document.forms[0].scrollbarTop.value = document.body.scrollTop;
		/* End: Refer to mail From: Antony Harris Sent: Thursday, March 29, 2007 2:59 PM
        To: Singh, Mantosh   Subject: DEFECTS/ENHANCEMENTS: Excluded Outstandings */	
		
	document.forms[0].submit();
		
}

function getMovementId(){
	//var table = document.getElementById("custodianColl");  
	var table = document.getElementById("custodianColl");  
	var tbody = table.getElementsByTagName("tbody")[0];    
	var rows = tbody.getElementsByTagName("tr");
	var selectedList ;

 //for (i=0; i < rows.length; i++) 
// {
//	if( isRowSelected(rows[i])){
		selectedList = rows[0].cells[2].innerText;
//	}
 //}  		
 return selectedList;
}

function addForMatch(methodName){
//Start : Refer to Smart-Predict_SRS_Open_Movements_0.2.doc
    var table = document.getElementById("custodianColl");  
	var tbody = table.getElementsByTagName("tbody")[0];    
	var rows = tbody.getElementsByTagName("tr");
	var selectedAmountsList = "";
	var columns;
	var str = true; // match allowed
	
	var isAmountDiffer = document.forms[0].isAmountDiffer.value;
	var selectedMovementsAmount = document.forms[0].selectedMovementsAmount.value;
	
	if (isAmountDiffer == "Y")
	{
		str = false;
	} else {
		
		for (var i=0; i < rows.length; i++) 
		{
			amtTemp = rows[i].cells[2].innerText.trim() + rows[i].cells[3].innerText.trim();
			if (amtTemp != selectedMovementsAmount)
			{
				str = false;
				break;
			}
		} 
		
	}
	//alert("str" + str);		
	giveWarningForAmtDifference(str, methodName);
	//End : Refer to Smart-Predict_SRS_Open_Movements_0.2.doc	

}

function addForMovement(methodName){
document.forms[0].method.value = methodName;		
document.forms[0].selectedList.value = getMovementId();

document.forms[0].submit();
}



function getHighestPosLvl(tableId) {

	var table = document.getElementById(tableId);  
	var tbody = table.getElementsByTagName("tbody")[0];    
	var rows = tbody.getElementsByTagName("tr");
	var selectedList = new Array();
	
for (i=0; i < rows.length; i++) 
 {
			
			selectedList[i] = rows[i].getElementsByTagName("input")[1].value;
			
 } 
 
 
var higestPosLvl = selectedList[0];

for(j=1; j < selectedList.length; j++ )
  {


	if (higestPosLvl < selectedList[j])
	{
		higestPosLvl = selectedList[j]
	}

  }

 return higestPosLvl;	

}

function getNextHighestPosLvl(tableId, highestPosLvl) {

var table = document.getElementById(tableId);  
var tbody = table.getElementsByTagName("tbody")[0];    
var rows = tbody.getElementsByTagName("tr");
var selectedList = new Array();

for (i=0; i < rows.length; i++) 
 {
			var k=0;
			var temp = rows[i].getElementsByTagName("input")[1].value;
			
			if (temp != highestPosLvl)
			{
				
				selectedList[k] = rows[i].getElementsByTagName("input")[1].value;
				k++;
			}
				
 } 
 
 
var nextHighestPosLvl = selectedList[0];

for(j=1; j < selectedList.length; j++ )
  {
	if ( nextHighestPosLvl < selectedList[j] )
		{
			nextHighestPosLvl = selectedList[j]
		}

  }

 return nextHighestPosLvl;
}

function getPosLvlTotalAmount(tableId, posLvl) {

var table = document.getElementById(tableId);  
var tbody = table.getElementsByTagName("tbody")[0];    
var rows = tbody.getElementsByTagName("tr");

var amountPosLvlTotal = 0;
	for (i=0; i < rows.length; i++) 
	 {
			
			var temp = rows[i].getElementsByTagName("input")[1].value;
			if (temp == posLvl)
			{
				
				var amount = new Number(getAmtValue(rows[i].cells[2].innerText)) ;
				 var sign = rows[i].cells[3].innerText;
				 
				 if ( sign.trim() == 'C')
				 {
				 
					amountPosLvlTotal += amount;
				 }
				 else if (sign.trim() == 'D')
				 {
					amountPosLvlTotal -= amount;
				 }
			}
					
	 } 
 return amountPosLvlTotal;
}

function getAmtValue(amt){
var amtvalue='';
 for(var idx=0; idx < amt.length; idx++){
	el = amt.charAt(idx);	
	if(el!=',' && el!='.'){	
	 amtvalue += el;
	}	 
 }
 return amtvalue; 
}

function getSelMvmntsMatchStatus(tableId) {

  var table = document.getElementById(tableId); 
  var tbody = table.getElementsByTagName("tbody")[0];
  var rows = tbody.getElementsByTagName("tr");
  
  var selectedList = new Array();
  var outstandingCount = 0	
  var outStandingflag = false;

  for (i=0; i < rows.length; i++) 
  {

	 //selectedList[i] = rows[i].cells[10].innerText.trim();
selectedList[i] = rows[i].cells[1].innerText.trim();
		
		if(selectedList[i] != 'OUTSTANDING') {
			outStandingflag = false;
			break;	
		} else {
		outstandingCount++;
		}
  }
  if(rows.length > 0 &&  outstandingCount == rows.length) {
	outStandingflag = true;
  }

	return outStandingflag;
}



function enabledisableReconButton(tableId){
	if(getSelMvmntsMatchStatus(tableId)){
	document.getElementById("reconbutton").innerHTML = document.getElementById("reconenablebutton").innerHTML;
	} else {
	document.getElementById("reconbutton").innerHTML = document.getElementById("recondisablebutton").innerHTML;
	}
}

function checkSelectedMovementsAmounts() {
	var table = document.getElementById("custodianColl");  
	var tbody = table.getElementsByTagName("tbody")[0];    
	var rows = tbody.getElementsByTagName("tr");
	
	var allowMatch = true;
	var amt0 = rows[0].cells[2].innerText.trim();
	var sign0 = rows[0].cells[3].innerText.trim()
		
	for (var i=1; i < rows.length; i++) 
	{
		var amt1 = rows[i].cells[2].innerText.trim();
		var sign1 = rows[i].cells[3].innerText.trim();
		if (amt0 != amt1 || sign0 != sign1) {
			allowMatch = false;
		}

	}  	
	return allowMatch;
}

/* function getPosLevelList(tableId,rowElement) {

	var table = document.getElementById(tableId); 
	var tbody = table.getElementsByTagName("tbody")[0];    
	var rows = tbody.getElementsByTagName("tr");
	var columns;
	var rowsSelected=0;
	var posLvlSelectedList = "";
	for (i=0; i < rows.length; i++) 
	{
		if( isRowSelected(rows[i])){
		var posLvlId = rowElement.getElementsByTagName("input")[1].value;
		//alert("posLvlId:::" + posLvlId);
		posLvlSelectedList = posLvlSelectedList + posLvlId + "#" + rows[i].cells[2].innerText.trim()+ "#" + rows[i].cells[3].innerText.trim() + "|";
	}
}  		
	alert("posLvlSelectedList:::" + posLvlSelectedList);
 	document.forms[0].posLvlSelectedList.value = posLvlSelectedList;	
	
} */

