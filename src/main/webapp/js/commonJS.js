var OK = 1;
var YES_NO = 2;
var ID_OK = 3;
var ID_YES = 5;
var ID_NO = 6;
var INCLUDE_EXCLUDE_CANCEL = 7;
var ID_INCLUDE = 8;
var ID_EXCLUDE = 9;
var ID_CANCEL = 10;
var OK_CANCEL = 11;
var isOpera = !!window.opera || navigator.userAgent.indexOf(' OPR/') >= 0;
// Opera 8.0+ (UA detection to detect Blink/v8-powered Opera)
var isFirefox = typeof InstallTrigger !== 'undefined';   // Firefox 1.0+
var isSafari = Object.prototype.toString.call(window.HTMLElement).indexOf('Constructor') > 0;
// At least Safari 3+: "[object HTMLElementConstructor]"
var isChrome = !!window.chrome && !isOpera;              // Chrome 1+
var isIEup10 = (navigator.userAgent.indexOf("rv:11.0") != -1 || navigator.userAgent.indexOf("MSIE 10.0") != -1);   // At least IE6
var isIEunder10 = (!isOpera && !isFirefox  && !isSafari &&  !isChrome && !isIEup10);


if(isFirefox){
		HTMLElement.prototype.__defineSetter__("innerText", function (sText) {
			   this.innerHTML = sText.replace(/\&/g, "&amp;").replace(/</g, "&lt;").replace(/>/g, "&gt;");
			});
		
		HTMLElement.prototype.__defineGetter__("innerText", function () {
			   var r = this.ownerDocument.createRange();
			   r.selectNodeContents(this);
			   return r.toString();
			});
}

//Supporting ECMAScript array index of when it is not (eg, IE8)
if(!Array.indexOf){
    Array.prototype.indexOf = function(obj){
        for(var i=0; i<this.length; i++){
            if(this[i]==obj){
                return i;
            }
        }
        return -1;
    }
}
//
function getBundle(type, key, defaultValue)
{
	try{
		var labelValue = new String(label[type][key]);
		if(labelValue=="undefined"|| labelValue==null){
			return defaultValue;
		}else{
			return ""+labelValue.replace(/&nbsp;/g, ' ').replace(/&#39;/g, "'");
		}
	}catch(err){
		return defaultValue;
	}
}



function ShowErrMsgWindowWithBtn(title, errmsg, btnMenu, callbackOne, callbackTwo, callbackThree) {
	let labelButtonOne; 
	let labelButtonTwo; 
	let labelButtonThree; 
	let showTwoButtons = true;
		if(!btnMenu){
			labelButtonOne = alertButtonLabels['ok'];
			showTwoButtons = false;
		}else if(btnMenu == YES_NO ){
			labelButtonOne =alertButtonLabels['yes'];
			labelButtonTwo = alertButtonLabels['no'];
		}
		else if(btnMenu == OK_CANCEL ){
			labelButtonOne = alertButtonLabels['ok'];
			labelButtonTwo = alertButtonLabels['cancel'];
		}
		else if(btnMenu == INCLUDE_EXCLUDE_CANCEL ){
			labelButtonOne = alertButtonLabels['include'];
			labelButtonTwo = alertButtonLabels['exclude'];
			labelButtonThree = alertButtonLabels['cancel'];
			return swThreeButton(errmsg, labelButtonOne, labelButtonTwo, labelButtonThree, callbackOne, callbackTwo, callbackThree);
		}
	
		try{
			Swal.fire({
			  title: title,
			  html: errmsg,
			  showCancelButton: showTwoButtons,
			  confirmButtonText: labelButtonOne,
			  cancelButtonText: labelButtonTwo,
			  reverseButtons: false,
			  allowOutsideClick: false,
			  showClass: {
				    popup: '',
				    icon: ''
				  },
				  hideClass: {
				    popup: '',
				  },
			  width:'450px',
			  heightAuto: false,
			}).then(function(result) {
			  if (result.value) {
				  Swal.close();
				  if(callbackOne)
					  callbackOne();
			  } else {
				  Swal.close();
				  if(callbackTwo)
					  callbackTwo();
			  }
			})

		}catch(e){
			console.log(e);
		}
		
}


//function ShowErrMsgWindowWithBtn(title, errmsg, btnMenu) {
//    var dlgParam = title + ';' + errmsg + ';' + btnMenu;
//    if (errmsg != null && errmsg != "" && window.showModalDialog) {
//        var str = "center:yes;edge:raised;resizable:no;help:no;" + "dialogWidth=" + 400 + "px;dialogHeight=" + 150 + "px;status:no";
//        return window.showModalDialog('jsp/errorDisplay.jsp', dlgParam, str);
//    }
//}


const setCookie = (name, value, days = 7, path = '/') => {
	  const expires = new Date(Date.now() + days * 864e5).toUTCString()
	  document.cookie = name + '=' + encodeURIComponent(value) + '; expires=' + expires + '; path=' + path
	}

	const getCookie = (name) => {
	  return document.cookie.split('; ').reduce((r, v) => {
	    const parts = v.split('=')
	    return parts[0] === name ? decodeURIComponent(parts[1]) : r
	  }, '')
	}
	
function ShowErrMsgWindowForSweepAuthorization(title, errmsg, btnMenu) {
    var dlgParam = title + ';' + errmsg + ';' + btnMenu + ';' + 'Y';
    if (errmsg != null && errmsg != "" && window.showModalDialog) {
        var str = "center:yes;edge:raised;resizable:no;scroll:yes;help:no;" + "dialogWidth=" + 400 + "px;dialogHeight=" + 180 + "px;status:no";
        return window.showModalDialog('jsp/errorDisplay.jsp', dlgParam, str);
    }
}



function createButton(text, id) {
    return $('<button class="modalButton" id="'+id+'">'+text+'</button>');
}

function createMessage(text) {
    return $('<div class="swal2-content" style="display: block;">'+text+'</div>');
}

function swThreeButton(msg, textOne, textTwo, textThree, callbackOne, callbackTwo, callbackThree) {

    var buttonsOnly = $('<div class="centerButtons">')
            .append(createButton(textOne,'sw_butt1'))
            .append(createButton(textTwo,'sw_butt2'))
            .append(createButton(textThree,'sw_butt3'));
    var buttonsPlus = $('<div>')
            .append(createMessage(msg))
            .append(buttonsOnly);
    	

    Swal.fire({
        title: 'Select Option',
        html: buttonsPlus,
        showCancelButton: false,
        showConfirmButton: false,
        animation: false,
        customClass: "animated zoomIn",
        onOpen: function (dObj) {
            $('#sw_butt1').on('click',function () {
               swal.close();
               callbackOne();
            });
            $('#sw_butt2').on('click',function () {
                swal.close();
                callbackTwo();
            });
            $('#sw_butt3').on('click',function () {
                swal.close();
                callbackThree();
            });
        }
    });

};


function getCurrencyDecimal(currCode) {
    var decimals = getMenuWindow().CurrencyDecimalMetaData[currCode];
    if (decimals != 'undefined' && decimals != null) {
        return decimals;
    }
    return "2";
}

/**
 * Method to refresh the data grid
 */
function call() {
    xl = null;
    var openerWnd = window.parentwindow;
    if (typeof openerWnd != 'undefined' && openerWnd != null && openerWnd.closed != true) {
        openerWnd.refreshArray(window);
    }
    closeall();
    SwSelectBox(null, null);
    var tableArr = document.getElementsByTagName("table");
    for (var p = 0; p < tableArr.length; p++) {
        if (tableArr[p].className != null) {
            if (tableArr[p].className == "sort-table") {
                if (tableArr[p].id != null && tableArr[p].id != "" && tableArr[p].id != "table_2" && tableArr[p].id != "table_1") {
                    var tableId = tableArr[p].id;
                    var tableElm = document.getElementById(tableId);
                    var tbody = tableElm.getElementsByTagName("tbody")[0];
                    if (tbody == null)
                    	return;
                    var rows = tbody.getElementsByTagName("tr");
                    // Start: Modified by Nithiyananthan for mantis 1617 on 06-02-2012
                    // Condition to chcek rows size is greater than 0					
					if(rows.length>0){
						var colls = rows[0].getElementsByTagName("td");						
						var noOfCols = colls.length;
						for (var i = 0; i < noOfCols; i++) {
							var selectId = tableId + "__select_" + i;
							var selectElm = document.getElementById(selectId);
							if (selectElm) {
								selectElm.xl = null;
							}
						}
					}
					// End: Modified by Nithiyananthan for mantis 1617 on 06-02-2012
                }
            }
        }
    }
}

function call1() {}

function call2() {
    call();
}

function MM_findObj(n, d) { //v4.01
    var p, i, x;
    if (!d) d = document;
    if ((p = n.indexOf("?")) > 0 && parent.frames.length) {
        d = parent.frames[n.substring(p + 1)].document;
        n = n.substring(0, p);
    }
    if (!(x = d[n]) && d.all) x = d.all[n];
    for (i = 0; !x && i < d.forms.length; i++) x = d.forms[i][n];
    for (i = 0; !x && d.layers && i < d.layers.length; i++) x = MM_findObj(n, d.layers[i].document);
    if (!x && d.getElementById) x = d.getElementById(n);
    return x;
}

function MM_showHideLayers() { //v6.0
    var i, p, v, obj, args = MM_showHideLayers.arguments;
    for (i = 0; i < (args.length - 2); i += 3) if ((obj = MM_findObj(args[i])) != null) {
        v = args[i + 2];
        if (obj.style) {
            obj = obj.style;
            v = (v == 'show') ? 'visible' : (v == 'hide') ? 'hidden' : v;
        }
        obj.visibility = v;
    }
}

function MM_preloadImages() { //v3.0
    var d = document;
    if (d.images) {
        if (!d.MM_p) d.MM_p = new Array();
        var i, j = d.MM_p.length,
            a = MM_preloadImages.arguments;
        for (i = 0; i < a.length; i++)
        if (a[i].indexOf("#") != 0) {
            d.MM_p[j] = new Image;
            d.MM_p[j++].src = a[i];
        }
    }
}

function MM_swapImgRestore() { //v3.0
    var i, x, a = document.MM_sr;
    for (i = 0; a && i < a.length && (x = a[i]) && x.oSrc; i++) x.src = x.oSrc;
}

function MM_swapImage() { //v3.0
    var i, j = 0,
        x, a = MM_swapImage.arguments;
    document.MM_sr = new Array;
    for (i = 0; i < (a.length - 2); i += 3)
    if ((x = MM_findObj(a[i])) != null) {
        document.MM_sr[j++] = x;
        if (!x.oSrc) x.oSrc = x.src;
        x.src = a[i + 2];
    }
}
var task = "None";
var windowsref = new Array();
var resetCount = 10;
var openCount = 0;

var windowParameters = new Array();

/**
 * This function sets a stored param before screen refresh
 * Any passed couple of (key, value) will be stored inside opener screen.
 * @added by Saber Chebka
 */
function setStoredParam(key, value){
	var parentParams = window.opener.windowParameters;
	var paramKey = window.name+"-"+key;
	if(typeof parentParams != 'undefined' && parentParams != null){  
		parentParams[paramKey] = value;
	}
}

/**
 * This function gets a stored param before the screen refreshes
 * Parent screens (opener screens), holds always the stored params of their children
 * Attention: main.jsp screen should not support this function.
 * 
 * @added by Saber Chebka
 */
function getStoredParam(key){
	if(window.opener && !window.opener.closed) {
		var parentParams = window.opener.windowParameters;	
		var paramKey = window.name+"-"+key;
		if(typeof parentParams != 'undefined' && parentParams != null){
			return parentParams[paramKey];
		}
	}
	return "";
	

}


/**
 * Code Added by Saber Chebka on 16-Oct-2012 for Mantis 1534: Get the persisted url 
 **/
function document_location_href(){
	return getStoredParam('href');
}

var isOpenedWindowField,stackUrl=null;
/*Start:Code Added by Saber Chebka & Mefteh Bouazizi on 17-Oct-2012 for Mantis 1534: Overriding the window.open function to change it to POST*/
/**
 * Very important note: Please make sure commonJS.js is imported ONLY ONCE, otherwise we will fall on recursive call to function window.open
 * one case is found on file <b>fdxxxxxxxxxxxxxxxfddf.js</> where commonJS.js is imported explicitly and also on file <b>taglib.jsp</b> (which is imported)
 * */
    window.open_ = window.open;
    window.open = function(url, name, props) {
	var vars = getUrlVars(url);
	if(vars.length>0){		
		
		var form = document.createElement("form");
		form.setAttribute("action", url.slice(0,url.indexOf('?')));
		form.setAttribute("method", "POST");
		form.setAttribute("acceptCharset", "utf-8");
		form.setAttribute("target", name);
		form.setAttribute("style", 'display:none;');
		
		// Loop inside the url params and construct hidden input fields of the form
		for (var i =0 ; i < vars.length; i++) {
			var hiddenField = document.createElement("input");		
			var nameInputForm = new String(vars[i]);
			var valueInputForm = new String(vars[vars[i]]); 
			hiddenField.setAttribute("name", nameInputForm.trim());
			hiddenField.setAttribute("value", valueInputForm.trim());
			form.appendChild(hiddenField);
		}		
		
		// Add parameter user_lang1234
		try {
			var hiddenField = document.createElement("input");
			hiddenField.setAttribute("name", "user_lang1234");
			hiddenField.setAttribute("value", getMenuWindow().getUserLanguage());
			form.appendChild(hiddenField);
		} catch (e) {
			// TODO: handle exception
		}

		
		// Append the form to document's body
		document.body.appendChild(form);	
		// Open the new window
		
	//alert(isOpenedWindowField);
	
		
		var newOpenedWindow = window.open_('', name, props);  

			    // Register the opned window url on opener window: Saber Chebka
		if (typeof newOpenedWindow != 'undefined'
				&& newOpenedWindow != null
				&& newOpenedWindow.opener != 'undefined'
				&& newOpenedWindow.opener != null) {
			
			var hrefKey = newOpenedWindow.name + '-' + 'href';
			newOpenedWindow.opener.windowParameters[hrefKey] = url;
		}
		
	    form.submit();
	    
		document.body.removeChild(form);
		return newOpenedWindow;
		
	}else if(url!=null && url!='' && url.indexOf("=")==-1) {		
		return window.open_(url, name, props);  
	}
		
 };

// overriding window.alert
	window.alert_ = window.alert;  
	window.alert = function(message) {
		if(typeof message != 'undefined' && typeof message == "string"){
			message = message.replace(/\u2019/g,'\'').replace(/&nbsp;/g,' ');
		}	
//		ShowErrMsgWindowWithBtn("", message, null);
		window.alert_(message);
	} 

//overriding window.confirm
	window.confirm_ = window.confirm;  
	window.confirm = function(message) {
		message = message.replace('&#39;','\'').replace(/&nbsp;/g,' ');
		return window.confirm_(message);
	}
//overriding document.getElementById for IE 10 and 11 usage
	document._oldGetElementById = document.getElementById;
	document.getElementById = function(elemIdOrName) {
	    var result = document._oldGetElementById(elemIdOrName);
	    if (! result) {
	        var elems = document.getElementsByName(elemIdOrName); 
	        if (elems && elems.length > 0) {
	            result = elems[0];
	        }
	    }

	    return result;
	};

/*Start:Code Added by Mefteh Bouazizi on 12-Oct-2012 for Mantis 1534 */
function getUrlVars(url) {	
	var decodedUrl=decodeURIComponent(url);	
	var vars = [], hash,hashes='';   
    if(decodedUrl.indexOf("?")!=-1) 
       hashes = decodedUrl.slice(decodedUrl.indexOf('?') + 1).split('&'); 
    for (var i = 0; i < hashes.length; i++) {
        hash = hashes[i].split('=');
        vars.push(hash[0]);
        vars[hash[0]] = hash[1];
    }
   
    return vars;
}
/* Modified to avoid flicker/flash phenomenon when opening screens by M.Bouraoui on 26/03/2013 */
function openWindow(url, windowname, attr, isCascade, menuItemId) {
    var menuWindow = getMenuWindow();
    var wndName = menuWindow.windowContainer.getNextWindowName(windowname);
 
    //windowName,windowObject,parentWindowName,parentWindowObject
    if (isCascade == 'undefined' || isCascade != "false") {
    	 
    	if (typeof window != 'undefined' && window != null) {
            var noOfBrothers = menuWindow.windowContainer.getCountMyParentChilds(wndName, window.name);
            if (window != null) {
	           	var winLeft = window.screenLeft ? window.screenLeft : window.screenX;
	        	var winTop = window.screenTop ? window.screenTop : window.screenY;
                var finalX = winLeft + menuWindow.windowContainer.cascadeWidth * (noOfBrothers + 2);
                var finalY = winTop + menuWindow.windowContainer.cascadeHeight * (noOfBrothers + 2);
                var offsetX = 0;
                var offsetY = 0;
                if (window.name == "") {
                    offsetY = 35;
                }
                finalX = finalX + offsetX;
                finalY = finalY + offsetY;
             
                wndObj = window.open(url, wndName, attr+",left="+ finalX +",top="+ finalY);
            }
    	}
    }
    else
    {
    	wndObj = window.open(url, wndName, attr + ",left=-6 ,top=-22");
//        wndObj.resizeBy(+8, +12);
       // wndObj.moveBy(-6, -22);
    }
    return wndObj;
    
    var wndObj;
    var indexwindow = windowsref.length;
    var wndname = windowname + indexwindow;
    if (window.opener != null) 
    	wndname = windowname + indexwindow + window.opener.name;
    else 
    	wndname = windowname + indexwindow;
    wndObj = window.open(url, wndname, attr);
    wndObj.parentwindow = window;
    if (typeof menuItemId != 'undefined' && menuItemId != null) wndObj.menuItemId = menuItemId;
    windowsref[indexwindow] = wndObj;
    ++openCount;
    if (isCascade == 'true') {
        if (window.opener != null && indexwindow == 0) {
            windowX = window.opener.windowX + 30;
            windowY = window.opener.windowY + 30;
        } else {
            windowX = windowX + 30;
            windowY = windowY + 30;
        }
        windowsref[indexwindow].moveTo(windowX, windowY);
    }
    if (openCount == resetCount) {
        openCount = 0;
        windowX = initialX;
        windowY = initialY;
    }
}

function extractMenuItemId(windowName) {
    var name = new String(windowName);
    var idx = name.indexOf("Window");
    var menuItemId = -1;
    if (idx >= 1) {
        menuItemId = new Number(name.substring(0, idx));
        if (menuItemId == null || menuItemId == 'NaN') menuItemId = -1;
    }
    return menuItemId;
}

function extractWindowName(windowName) {
    var name = new String(windowName);
    var idx = name.indexOf("Window");
    var winName = 'undefined';
    if (idx >= 1) {
    	winName = new String(name.substring(0, idx));
        if (winName == null || winName == 'undefined') winName = 'undefined';
    }
    return winName;
}

function getBaseWindows() {
    var urlStr = new String();
    var menuWindow = getMenuWindow().windowContainer;
    for (idx = 0; idx < menuWindow.windowsCollection.length; ++idx) {
        var SwtWindowObj = menuWindow.windowsCollection[idx];
        if (SwtWindowObj != null) {
            var wnd = SwtWindowObj.windowObject;
            if (typeof SwtWindowObj.windowObject != 'undefined' && SwtWindowObj.windowObject != null && SwtWindowObj.windowObject.closed != true) {
                var menuItemId = extractMenuItemId(SwtWindowObj.windowName);
                if (menuItemId >= 0) {
                    urlStr = urlStr + menuItemId + ";";
                    var winLeft = wnd.screenLeft ? wnd.screenLeft : wnd.screenX;
                    if (winLeft == -32000) {
                        urlStr = urlStr + 0 + ";";
                    } else {
                        urlStr = urlStr + winLeft + ";";
                    }
                    var winTop = wnd.screenTop ? wnd.screenTop : wnd.screenY;
                    if (winTop== -32000) {
                        urlStr = urlStr + 0 + ";";
                    } else {
                    	
                        urlStr = urlStr + winTop + ";";
                    }
                    urlStr = urlStr + wnd.document.body.clientWidth + ";";
                    urlStr = urlStr + wnd.document.body.clientHeight;
                    urlStr = urlStr + "~";
                }
            }
        }
    }
    return urlStr;
}

function registeredWindowInParent() {
    var wndName = window.name;
    var parentWndArray = window.opener.windowsref;
    if (typeof parentWndArray != 'undefined' && parentWndArray != null) {
        var index = 0;
        for (index = 0; index < parentWndArray.length; ++index) {
            if (typeof parentWndArray[index] != 'undefined' && !parentWndArray[index].closed) {
                if (parentWndArray[index].name == wndName) {
                    window.opener.windowsref[index] = window.self;
                    break;
                }
            }
        }
        if (index == parentWndArray.length) {
            window.opener.windowsref[index] = window.self;
            window.parentwindow = window.opener;
        }
    }
}

function unloadMyWindow() {
	try {
		var menuWindow = getMenuWindow().windowContainer;
		if (typeof menuWindow != 'undefined' && menuWindow != null) {
			if (typeof window.isDoNotCloseMyChilds == 'undefined'
					|| window.isDoNotCloseMyChilds != true) {
				menuWindow.closeMyChilds(window.name);
			}
			menuWindow.unregisterWindow(window.name);
		}
	} catch (err) {
	}
}

/*Start:Code Added by Mefteh Bouazizi on 13-Novembre-2012 for Mantis 1534: submit form from url */
function submitFormFromURL(url,window){
	 var paramsUrl = getUrlVars(url);
		var form = document.createElement("form");
		//form.setAttribute("action") are replaced with form.action to avoid XSS DOM Attack
		form.action= url.slice(0,url.indexOf('?'));
		form.setAttribute("method", "POST");
		form.setAttribute("acceptCharset", "utf-8");
		//form.setAttribute("target") are replaced with form.target to avoid XSS DOM Attack
		form.target = window.name;
		
		// Loop inside the url params and construct hidden input fields of the form
		for (var i =0 ; i < paramsUrl.length; i++) {
			var hiddenField = document.createElement("input");		
			var nameInputForm = new String(paramsUrl[i]);
			var valueInputForm = new String(paramsUrl[paramsUrl[i]]); 
			hiddenField.setAttribute("name", nameInputForm.trim());
			hiddenField.setAttribute("value", valueInputForm.trim());
			form.appendChild(hiddenField);
		}		

		// Append the form to document's body
	   window.document.body.appendChild(form);	
	   // submit the form 
	    form.submit();
	   // remove the form to document's body
	    window.document.body.removeChild(form);		
	    
}

function setParentChildsFocus(wndObject) {
    var menuWindow = getMenuWindow().windowContainer;
    if (window.opener != null) {
        menuWindow.registerWindow(window.name, window.opener.name, window);
        menuWindow.getNofChilds(window.name);
    }
    window.onbeforeunload = unloadMyWindow;
    return;
    window.onunload = call1;
    window.onbeforeunload = call2;
    registeredWindowInParent();
    var windArray = window.opener.windowsref;
    if (typeof windArray != 'undefined' && windArray != null) {
        for (var index = 0; index < windArray.length; ++index) {
            if (typeof windArray[index] != 'undefined' && windArray[index] != null && !windArray[index].closed) {
                windArray[index].focus();
                if (windArray[index].setChildsFocus) windArray[index].setChildsFocus();
            }
        }
    }
}

function setParentFocus() {
    var windArray = window.windowsref;
    if (typeof windArray != 'undefined' && windArray != null) {
        for (var index = 0; index < windArray.length; ++index) {
            if (windArray[index] && !windArray[index].closed) {
                windArray[index].focus();
                if (windArray[index].setChildsFocus != null) windArray[index].setChildsFocus();
            }
        }
    }
}

function setChildsFocus() {
    for (var index = 0; index < windowsref.length; ++index) {
        if (typeof windowsref[index] != 'undefined' && !windowsref[index].closed) {
            (windowsref[index]).focus();
        }
    }
}

function closeall() {
    if (typeof windowsref != 'undefined' && windowsref != null) {
        for (var index = 0; index < windowsref.length; ++index) {
            if (typeof windowsref[index] != 'undefined' && !windowsref[index].closed) {
                (windowsref[index]).close();
            }
        }
    }
}

function refreshArray(windowIns) {
    var position = findMyPosition(windowIns);
    if (position >= 0) {
        for (var index = position; index < windowsref.length - 1; ++index) {
            windowsref[index] = windowsref[index + 1];
        }
        windowsref.splice(windowsref.length, 1);
        --windowsref.length;
    }
    if (windowsref.length == 0) {
        windowX = initialX;
        windowY = initialY;
    }
}

function findMyPosition(windowIns) {
    for (var index = 0; index < windowsref.length; ++index) {
        if (windowsref[index] == windowIns) {
            return index;
        }
    }
    return -1;
}

function ShowErrMsgWindow(errmsg) {
    var dlgParam = errmsg;
    if (errmsg != null && errmsg != "") {
    	ShowErrMsgWindowWithBtn("", errmsg, null);
    }
}

function selectTableRow(event) {
	var event = event || window.event;
	var target = (event.srcElement||event.target);
    if (target.tagName == "TR" || target.tagName == "TD") {
        var rowElement = target.parentElement;
        var tblElement = target.parentElement.parentElement.parentElement;
        var isRowSel = isRowSelected(rowElement);
        resetTableRowsStyle(tblElement);
        if (isRowSel == false) rowElement.className = 'selectrow'; //rowElement.className+' over';
        onSelectTableRow(rowElement, !isRowSel);
    }else if (target.tagName == "IMG"){
    	  var rowElement = target.parentElement.parentElement;
          var tblElement = target.parentElement.parentElement.parentElement.parentElement;
          var isRowSel = isRowSelected(rowElement);
    	  console.log(isRowSel)
    	  highLightTableRow(rowElement)
    	  try{
    		  resetTableRowsStyle(tblElement);
    	  }catch(e){
    		  console.log(e);
    	  }
         
          if (isRowSel == false) rowElement.className = 'selectrow'; //rowElement.className+' over';
          onSelectTableRow(rowElement, !isRowSel);
    }
}

function resetTableRowsStyle(table) {
    var tbodyColl = table.getElementsByTagName("tbody");
    
    if (typeof tbodyColl != 'undefined' && tbodyColl != null && tbodyColl.length > 0) {
        tbody = tbodyColl[0];
        var rows = tbody.getElementsByTagName("tr");
        var count = 0;
        for (i = 0; i < rows.length; i++) {
            if (isElementVisibile(rows[i])) {
                setClassName(rows[i], count % 2 ? "odd" : "even");
                count++;
            }
        }
        return true;
    }
    return false;
}

function highlightTableRows(tableId) {
    var previousClass = null;
    var table = document.getElementById(tableId);
    var tbody = table.getElementsByTagName("tbody")[0];
    var rows = tbody.getElementsByTagName("tr");
    // add event handlers so rows light up and are clickable
    for (i = 0; i < rows.length; i++) {
        rows[i].onclick = selectTableRow
    }
}

function highlightMultiTableRows(tableId) {
    var previousClass = null;
    var table = document.getElementById(tableId);
    var tbody = table.getElementsByTagName("tbody")[0];
    var rows = tbody.getElementsByTagName("tr");
    // add event handlers so rows light up and are clickable
    for (i = 0; i < rows.length; i++) {
        rows[i].onclick = selectMulTableRow
    }
}

var selectedTable = "";
function selectMulTableRow(e) {
	var event = (window.event|| e);
	var target = (event.srcElement || event.target);
    if (target.tagName == "TR" || target.tagName == "TD") {
        var rowElement = target.parentElement;
        var tblElement = target.parentElement.parentElement.parentElement;
        selectedTable = tblElement.id;
        onMultiSelectTableRow(rowElement, isRowSelected(rowElement));
    }
}

function getCountRowsSelected(rowElement) {
    var tbody = rowElement.parentElement;
    var rows = tbody.rows;
    var count = 0;
    for (var i = 0; i < rows.length; ++i) {
        if (isRowSelected(rows[i])) {
            ++count;
        }
    }
    return count;
}

function isRowSelected(rowElement) {
    var className = new String(rowElement.className);
    if (className.indexOf("select", 0) >= 0) return true;
    else return false;
}

function unHighLightTableRow(rowElement) {
    var className = new String(rowElement.className);
    className = className.replace("select", "");
    rowElement.className = className;
}

function highLightTableRow(rowElement) {
    rowElement.className = rowElement.className + "select";
}

function hiddenTableColumns(tableId, columnIndex, attributeName) {
    var previousClass = null;
    var table = document.getElementById(tableId);
    var tbody = table.getElementsByTagName("tbody")[0];
    var rows = tbody.getElementsByTagName("tr");
    for (i = 0; i < rows.length; i++) {
        var columns = rows[i].getElementsByTagName("td");
        var colObj = columns[columnIndex];
        var herfString = colObj.getElementsByTagName("a")[0].href;
        var calColl = herfString.split("?");
        if (calColl.length > 1) herfString = calColl[1];
        calColl = herfString.split("&");
        for (var idx = 0; idx < calColl.length; ++idx) {
            var codeValues = (calColl[idx]).split("=");
            if (codeValues.length > 1 && codeValues[0] == attributeName) {
                rows[i].setAttribute(attributeName, codeValues[1]);
                break;
            }
        }
        colObj.innerHTML = colObj.innerText;
    }
}

function reportMove() {}

function fillSpaces(text, minLength) {
    var actualLength = text.length;
    for (var idx = 0; idx < minLength - actualLength; ++idx)
    text = text.concat(" ");
    return text.valueOf();
}

function expandbutton(aobject) {
    aobject.className = "current"
}

function collapsebutton(aobject) {
    aobject.className = ""
}

function highlightbutton(aobject) {
    aobject.className = "hover"
}
var xl;

function updateColors() {
    var rows = xl.dataTable.tBody.rows;
    var l = rows.length;
    var count = 0;
    for (var i = 0; i < l; i++) {
        if (xl.isRowVisible(i)) {
            setClassName(rows[i], count % 2 ? "odd" : "even");
            count++;
        }
    }
}

function setClassName(element, className) {
    element.className = className;
}

function isElementVisibile(element) {
    return (element.style.display != "none");
}

// Override the trim() function if not implemented
if (!String.prototype.trim) {
	String.prototype.trim = function () {
	    var x = this;
	    x = x.replace(/^\s*(.*)/, "$1");
	    x = x.replace(/(.*?)\s*$/, "$1");
	    return x;
	}
}

//Override the right trim() function if not implemented, parameter trimCharacter if provided will make the trim based on it: Exemple 'test...'.rTrim(".") gives 'test'
if (!String.prototype.rTrim) {
  String.prototype.rTrim = function (trimCharacter) {
	  if(trimCharacter=="undefined" || trimCharacter==null)
		  trimCharacter = "\\s";
	  else if (trimCharacter == ".")
		  trimCharacter = "\\.";
	  var re = new RegExp("("+trimCharacter+")*$");
      return this.replace(re, "");
  };
}

//Override the String's startsWith method, avaiable since ECMAScript 6 specification
if (!String.prototype.startsWith) {
  String.prototype.startsWith = function (searchString, position) {
      position = position || 0;
      return this.indexOf(searchString, position) === position;
  };
}


function substring(str1, str2) {
    var string1 = new String(str1);
    var string2 = new String(str2);
    var index = string2.indexOf(string1);
    if (index >= 0) {
        var str1Len = string1.length;
        string2 = string2.substring(index + str1Len);
    }
    string2 = string2.trim();
    return string2.toString();
}

function validateMinValueObjects(objects) {
    var isAllFieldsValid = true;
    for (var i = 0; i < objects.length; i++) {
        if (objects[i].isValidValue() == false) {
            isAllFieldsValid = false;
            break;
        }
    }
    return isAllFieldsValid;
}

function SwDropBox(idElement, valueElement, dropBoxElement, idTextLength, valueTextLength) {
    this.idElement = idElement;
    this.valueElement = valueElement;
    this.dropBoxElement = dropBoxElement;
    this.idTextLength = idTextLength;
    this.valueTextLength = valueTextLength;
}

SwDropBox.prototype.repopulate = function () {
    var selectedIndex = this.dropBoxElement.selectedIndex;
    if (selectedIndex < 0) selectedIndex = 0;
    var selectedOption = this.dropBoxElement.options[selectedIndex];
    this.idElement.value = selectedOption.value;
    this.valueElement.value = selectedOption.text;
    for (var idx = 0; idx < this.dropBoxElement.options.length; ++idx) {
        var text = new String(this.dropBoxElement.options[idx].text);
        var value = new String(this.dropBoxElement.options[idx].value);
        this.dropBoxElement.options[idx].text = fillSpaces(value, this.idTextLength) + " " + fillSpaces(text, this.valueTextLength);
    }
}

function SwSelectBox(selectElement, descElement) {
    if (typeof selectElement != 'undefined' && selectElement != null && typeof descElement != 'undefined' && descElement != null && selectElement.tagName == 'SELECT') {
        this.selectElement = selectElement;
        this.descElement = descElement;
        this.init();
        this.onuserchange = this.selectElement.onchange;
        this.selectElement.onchange = this.onselectchange;
        this.selectElement.parent = this;
        this.onselectchange();
    } else {
        for (i = 0; i < document.getElementsByTagName("SELECT").length; i++) {
            obj = document.getElementsByTagName("SELECT")[i];
            obj.parent = null;
            obj.onchange = null;
            obj = null;
        }
    }
}
SwSelectBox.prototype.onuserchange = function () {}

SwSelectBox.prototype.init = function () {
    var optionsArray = this.selectElement.options;
    var noofoptions = optionsArray.length;
    for (var i = 0; i < noofoptions; ++i) {
        optionsArray[i].setAttribute("str", optionsArray[i].text);
        optionsArray[i].text = optionsArray[i].value;
    }
}

SwSelectBox.prototype.onselectchange = function (event) {
	var event = window.event || event;
    var selectelement = null;
    var target= null;
    if(event!=null && event!= undefined)
    	target = (event.srcElement||event.target);
    if (this.selectElement != undefined && this.selectElement != null) {
        selectelement = this.selectElement;
    } else {
        selectelement = target;
    }
    var mainobject = selectelement.parent;
    var optionsArray = mainobject.selectElement.options;
    var noofoptions = optionsArray.length;
    var selectedIndex = mainobject.selectElement.selectedIndex;
    if (selectedIndex >= 0) {
        var selectedOption = optionsArray[selectedIndex];
        mainobject.descElement.innerText = selectedOption.getAttribute("str");
    }
    var srcElementNull = false;
    if(event !=undefined && target != null){
    	srcElementNull = (target.innerHTML != undefined);
    }
    if (event !=undefined && srcElementNull != false && target != null && mainobject.onuserchange != null && mainobject.onuserchange != undefined) {
    	mainobject.onuserchange();
        
    }
}

function setFocus(formObj) {
    setTitleSuffix(formObj);
    if (window.document.hasFocus()) {
        var elements = formObj.elements;
        for (var idx = 0; idx < elements.length; ++idx) {
            if (!elements[idx].disabled && elements[idx].tabIndex > 0) {
                elements[idx].focus();
                break;
            }
        }
    }
    var printElement = document.getElementsByName("Print");
    if (printElement != 'undefined' && printElement != null) {
        if (printElement[0] != 'undefined' && printElement[0] != null) {
            var printTd = printElement[0].children;
            if (printTd != 'undefined' && printTd != null) {
            	if (printTd[0] != 'undefined' && printTd[0] != null) {
            		printTd[0].style.cursor = "pointer;";
            	}
            }
        }
    }
}

function setTitleSuffix(formObj) {
    var titleSuffix = getTitleSuffix();
    document.title = document.title + " " + titleSuffix;
}

function confirmClose(windowType) {
        window.close();
}

function confirmCancel() {
    alert("You have chosen to cancel the window. All unsaved changes will be lost. Are you sure?");
    window.close();
}

function printPage() {
    window.print();
}

function checkforZeroes(ss) {
    var flag = "true";
    for (var idx = 0; idx < ss.length - 1; idx++) {
        var el = ss.charAt(idx);
        if (el != ',' && el != '.' && el != '0' && el != " ") {
            flag = false;
            break;
        }
    }
    return flag;
}

/**
 * SwMainSelectBox
 * 			
 * This method is used to set the element for making the selectbox and assign the events to that elements.
 */
function SwMainSelectBox(divElement, selectElement, idElement, descElement, arrowElement, idLength, descLength) {
    // assign the elements for select box   
    this.divElement = divElement;
    this.selectElement = selectElement;
    this.idElement = idElement;
    this.descElement = descElement;
    this.arrowElement = arrowElement;
    this.idLength = idLength;
    this.descLength = descLength;
   
    //denote for clicking 
    this.clickFlag = false;
   
    // descElement2 = "true"  when we have to show more tahn 2 columns in the drop downs like in group/metaGroup in Movement Search Screen
    if (typeof descElement2 != 'undefined') {
        this.descElement2 = descElement2;
        this.descElement2.parent = this;
    }
    //assign the parent for selectbox
    this.divElement.parent = this;
    this.selectElement.parent = this;
    this.idElement.parent = this;
    this.descElement.parent = this;
    this.arrowElement.parent = this;
    //call the initialization method.
    this.init();
    //assign the event for the select box elements 
    this.selectElementClick = this.selectElement.onclick;
    this.selectElementKeyDown = this.selectElement.onkeydown;
    this.selectElementChange = this.selectElement.onchange;
    this.selectElement.onclick = this.selectElementOnClick
    this.selectElement.onkeydown = this.selectElementOnKeyDown
    this.selectElement.onchange = this.selectElementOnChange;
    this.idElementMouseDown = this.idElement.onmousedown;
    this.idElementKeyDown = this.idElement.onkeydown;
    this.idElement.onmousedown = this.idElementOnMouseDown;
    this.idElement.onkeydown = this.idElementOnKeyDown;
    this.arrowElementClick = this.arrowElement.onclick;
    this.arrowElementKeyDown = this.arrowElement.onkeydown;
    this.arrowElement.onclick = this.arrowElementOnClick;
    this.arrowElement.onkeydown = this.arrowElementOnKeyDown;
}

SwMainSelectBox.prototype.selectElementClick = function () {}
SwMainSelectBox.prototype.selectElementKeyDown = function () {}
SwMainSelectBox.prototype.selectElementChange = function () {}
SwMainSelectBox.prototype.idElementMouseDown = function () {}
SwMainSelectBox.prototype.idElementKeyDown = function () {}
SwMainSelectBox.prototype.arrowElementClick = function () {}
SwMainSelectBox.prototype.arrowElementKeyDown = function () {}

SwMainSelectBox.prototype.init = function () {
    var selectedIndex = this.selectElement.selectedIndex;
    if (selectedIndex < 0) selectedIndex = 0;
    var selectedOption = this.selectElement.options[selectedIndex];
    this.idElement.value = selectedOption.value;
    this.descElement.value = selectedOption.text;
    for (var idx = 0; idx < this.selectElement.options.length; ++idx) {
        var text = new String(this.selectElement.options[idx].text);
        var value = new String(this.selectElement.options[idx].value);
        text = replace(text, '&nbsp;', ' ');
        this.selectElement.options[idx].text = fillSpaces(value, this.idLength) + " " + fillSpaces(text, this.descLength);
    }
}



/**
 * setClickFlag
 * 			
 * This method is used to set clickflag to true
 */
SwMainSelectBox.prototype.setClickFlag = function () {
	//based on the flag to restrict the firing events for select box.
    this.clickFlag = true;
}



/**
 * selectElementOnClick
 * 			
 * This method is called when clicking the select element for selectbox 
 */
SwMainSelectBox.prototype.selectElementOnClick = function (event) {
	var event = (window.event || event);
	var target = (event.srcElement || event.target);
	//get the target element from window object
    var selectelement = target;
    //get the parent element for selectelement
    var mainobject = target.parent;
    //used to populate value in the list box
    if(typeof mainobject != 'undefined') {
    	if (typeof mainobject.descElement2 != 'undefined') {
            // This function is added to handle the extra column added in the metagroup drop down in movement search screen 
            mainobject = populateValues_Jsp(mainobject);
        } else {
            mainobject.populateValues(mainobject);
        }
        //set focus on arrowElement
        mainobject.arrowElement.focus();
        //set the visibility  for divElement 
        mainobject.divElement.style.visibility = 'hidden';
       
        if (target != undefined && target != null && mainobject.selectElementClick != null && mainobject.selectElementClick != undefined && this.clickFlag != true) {
        	//call the function which is assigned for select element  on click
            mainobject.selectElementClick();
        }
    }
    
    return true;
}

/**
 * selectElementOnKeyDown
 * 			
 * This method is called when perform the keydown on the select element for selectbox 
 */
SwMainSelectBox.prototype.selectElementOnKeyDown = function (e) {
	var event = (window.event|| e);
	var target = (event.srcElement || event.target);
	//get the target element from window object
    var selectelement = target;
    //get the parent element for selectelement
    var mainobject = selectelement.parent;
    //set the focus for the select element
    if (mainobject.divElement.style.visibility == "visible") {
        mainobject.selectElement.focus();
        if (event.keyCode == 13) mainobject.selectElementOnClick();
    }
    
    if (target != undefined && target != null && mainobject.selectElementKeyDown != null && mainobject.selectElementKeyDown != undefined && this.clickFlag != true) {
    	//call the function which is assigned for select element on keydown
        mainobject.selectElementKeyDown();
    }
    
    return true;
}

/**
 * selectElementOnChange
 * 			
 * This method is called when changing the value for select element for selectbox 
 */
SwMainSelectBox.prototype.selectElementOnChange = function (e) {
	var event = (window.event || e);
	var target ;
	if(event)
		target = (event.srcElement || event.target);
	//get the target element from window object
    var selectelement = target;
    //get the parent element for selectelement
    var mainobject = selectelement.parent;
    //get the selected index
    var selectedIndex = mainobject.selectElement.selectedIndex;
    //if it has then,set the value for relevant selected index
    if (selectedIndex >= 0) {
        var selectedOption = mainobject.selectElement.options[selectedIndex];
        mainobject.idElement.value = selectedOption.value;
        mainobject.descElement.value = substring(selectedOption.value, selectedOption.text);
    }
   
    if (target != undefined && target != null && mainobject.selectElementChange != null && mainobject.selectElementChange != undefined && this.clickFlag != true) {
    	//call the function which is assigned for select element on change
        mainobject.selectElementChange();
    }
    
    var eventDispatch = new CustomEvent("comboBoxChanged", { "detail": [event,target]});
 // Dispatch/Trigger/Fire the event
    document.dispatchEvent(eventDispatch);
    return true;
}


SwMainSelectBox.prototype.populateValues = function (mainobject) {
    var selectedIndex = mainobject.selectElement.selectedIndex;
    if (selectedIndex < 0) selectedIndex = 0;
    var selectedOption = mainobject.selectElement.options[selectedIndex];
    mainobject.idElement.value = selectedOption.value;
    mainobject.descElement.value = substring(selectedOption.value, selectedOption.text);
}

/**
 * idElementOnMouseDown
 * 			
 * This method is called when doing the mousedown on the idElement for selectbox 
 */
SwMainSelectBox.prototype.idElementOnMouseDown = function (e) {
	var event = (window.event|| e);
	var target = (event.srcElement || event.target);
	//get the target element from window object
    var idelement = target;
    //get the parent element for idelement
    var mainobject = idelement.parent;
    //set the visibility and focus
    if (mainobject.divElement.style.visibility == "visible") {
        mainobject.divElement.style.visibility = "hidden";
    } else {
        mainobject.divElement.style.visibility = "visible";
        mainobject.selectElement.focus();
    }
    
    if (target != undefined && target != null && mainobject.idElementMouseDown != null && mainobject.idElementMouseDown != undefined && this.clickFlag != true) {
    	//call the function which is assigned for id element on mouse down
        mainobject.idElementMouseDown();
    }
    
    return true;
}

/**
 * idElementOnKeyDown
 * 			
 * This method is called when doing the keydown on the idElement for selectbox 
 */
SwMainSelectBox.prototype.idElementOnKeyDown = function (e) {
	var event = (window.event|| e);
	var target = (event.srcElement || event.target);
	//get the target element from window object
    var idelement = target;
    //get the parent element for idelement	
    var mainobject = idelement.parent;
    //based on the visibility perform some operation.
    if (mainobject.divElement.style.visibility == "visible") {
    	if (document.createEvent) { // all browsers except IE before version 9
			var event = document.createEvent("HTMLEvents");
			event.initEvent("keydown", true, false);
			mainobject.dispatchEvent(event);
		} else { // IE browsers before version 9
			mainobject.fireEvent("onkeydown");
		}
    } else {
        mainobject.divElement.style.visibility = "hidden";
        if (event.keyCode == 40) {
            changeSelection(mainobject.selectElement, 1);
        } else if (event.keyCode == 38) {
            changeSelection(mainobject.selectElement, -1);
        }
    }
   
    if (target != undefined && target != null && mainobject.idElementKeyDown != null && mainobject.idElementKeyDown != undefined && this.clickFlag != true) {
    	//call the function which is assigned for id element on key down
        mainobject.idElementKeyDown();
    }
   
    return true;
}

/**
 * arrowElementOnClick 
 * 			
 * This method is called when clicking the arrowElement for selectbox 
 */
SwMainSelectBox.prototype.arrowElementOnClick = function (event) {
	var event = (window.event || event);
	var target = (event.srcElement||event.target);
	//get the target element from window object
    var arrowelement = target;
    //get the parent element for idelement
    var mainobject = arrowelement.parent;
    //set the visibility and focus
    if (mainobject.divElement.style.visibility == "visible") {
        mainobject.divElement.style.visibility = "hidden";
    } else {
        mainobject.divElement.style.visibility = "visible";
    }
    
    if (target != undefined && target != null && mainobject.arrowElementClick != null && mainobject.arrowElementClick != undefined && this.clickFlag != true) {
    	//call the function which is assigned for arrow element on click
        mainobject.arrowElementClick(event);
    }
  
    return true;
}

/**
 * arrowElementOnKeyDown 
 * 			
 * This method is called when doing keydown on the arrowElement for selectbox 
 */
SwMainSelectBox.prototype.arrowElementOnKeyDown = function (event) {
	var event = (window.event || event);
	var target = (event.srcElement||event.target)
	//get the target element from window object
    var arrowelement = target;
    //get the parent element for idelement
    var mainobject = arrowelement.parent;
    var idelement = target;
    var mainobject = idelement.parent;
     //based on the visibility perform some operation.	
    if (mainobject.divElement.style.visibility == "visible") {
        if (document.createEvent) { // all browsers except IE before version 9
			var event = document.createEvent("HTMLEvents");
			event.initEvent("keydown", true, false);
			mainobject.selectElement.dispatchEvent(event);
		} else { // IE browsers before version 9
			mainobject.selectElement.fireEvent("onkeydown");
		}
    } else {
        mainobject.divElement.style.visibility = "hidden";
        if (event.keyCode == 40) {
            mainobject.changeSelection(mainobject.selectElement, 1);
        } else if (event.keyCode == 38) {
            mainobject.changeSelection(mainobject.selectElement, -1);
        }
    }
  
    if (target.srcElement != undefined && target.srcElement != null && mainobject.arrowElementOnKeyDown != null && mainobject.arrowElementClick != undefined && this.clickFlag != true) {
    	//call the function which is assigned for arrow element on click
        mainobject.arrowElementClick();
    }
  
    return true;
}

SwMainSelectBox.prototype.changeSelection = function (element, incr) {
    var currenctIdx = element.selectedIndex;
    var maxlength = element.options.length - 1;
    var minlength = 0;
    var newIdx = new Number(currenctIdx).valueOf() + new Number(incr).valueOf();
    if (newIdx > maxlength) newIdx = maxlength;
    else if (newIdx < minlength) newIdx = minlength;
    element.selectedIndex = newIdx;
    if (document.createEvent) { // all browsers except IE before version 9
		var event = document.createEvent("HTMLEvents");
		event.initEvent("change", true, false);
		element.dispatchEvent(event);
	} else { // IE browsers before version 9
		element.fireEvent("onchange");
	}
}

function getRowsSelected(rowElement) {
    var tbody = rowElement.parentElement;
    var rows = tbody.rows;
    var collRows = new Array();
    var count = 0;
    var count = 0;
    for (var i = 0; i < rows.length; ++i) {
        if (isRowSelected(rows[i])) {
            collRows[count++] = rows[i];
        }
    }
    return collRows;
}

function comparedates(date1, date2, format, date1Caption, date2Caption, time1, time2) {
    var retValue = "true";
    var strdate1 = new String(date1);
    var strdate2 = new String(date2);
    var strTime1 = new String("00:00");
    var strTime2 = new String("00:00");
    if (comparedates.arguments.length == 7) {
        strTime1 = new String(time1);
        strTime2 = new String(time2);
    }
    if (typeof strdate1 != 'undefined' && strdate1 != null && typeof strdate2 != 'undefined' && strdate2 != null) {
        strdate1 = strdate1.trim();
        strdate2 = strdate2.trim();
        strTime1 = strTime1.trim();
        strTime2 = strTime2.trim();
        if (strdate1.length > 0 && strdate2.length > 0) {
            if (format == "datePat1") {
                // Take the format: dd/MM/yyyy
                var parts = date1.split("/");
                date1 = new Date(0);
                date1.setFullYear(parts[2]);
                date1.setDate(parts[0]);
                date1.setMonth(parts[1] - 1);
                parts = strTime1.split(":");
                date1.setHours(parts[0]);
                date1.setMinutes(parts[1]);
                var parts = date2.split("/");
                date2 = new Date(0);
                date2.setFullYear(parts[2]);
                date2.setDate(parts[0]);
                date2.setMonth(parts[1] - 1);
                parts = strTime2.split(":");
                date2.setHours(parts[0]);
                date2.setMinutes(parts[1]);
                if (date2 < date1) retValue = "false";
            }
            if (format == "datePat2") {
                // Take the format: dd/MM/yyyy
                var parts = date1.split("/");
                date1 = new Date(0);
                date1.setFullYear(parts[2]);
                date1.setDate(parts[1]);
                date1.setMonth(parts[0] - 1);
                parts = strTime1.split(":");
                date1.setHours(parts[0]);
                date1.setMinutes(parts[1]);
                var parts = date2.split("/");
                date2 = new Date(0);
                date2.setFullYear(parts[2]);
                date2.setDate(parts[1]);
                date2.setMonth(parts[0] - 1);
                parts = strTime2.split(":");
                date2.setHours(parts[0]);
                date2.setMinutes(parts[1]);
                if (date2 < date1) retValue = "false";
            }
        }
    }
    if (retValue == "false") {
    	
    	alert(date2Caption + " should not be earlier than " + date1Caption);
        return false;
    } else {
        return true;
    }
}

function compareTwoDates(date1, date2, format, time1, time2) {
    var retValue = "true";
    var strdate1 = new String(date1);
    var strdate2 = new String(date2);
    var strTime1 = new String("00:00");
    var strTime2 = new String("00:00");
    if (compareTwoDates.arguments.length == 5) {
        strTime1 = new String(time1);
        strTime2 = new String(time2);
    }
    if (typeof strdate1 != 'undefined' && strdate1 != null && typeof strdate2 != 'undefined' && strdate2 != null) {
        strdate1 = strdate1.trim();
        strdate2 = strdate2.trim();
        strTime1 = strTime1.trim();
        strTime2 = strTime2.trim();
        if (strdate1.length > 0 && strdate2.length > 0) {
            if (format == "datePat1") {
                // Take the format: dd/MM/yyyy
                var parts = date1.split("/");
                date1 = new Date(0);
                date1.setFullYear(parts[2]);
                date1.setDate(parts[0]);
                date1.setMonth(parts[1] - 1);
                parts = strTime1.split(":");
                date1.setHours(parts[0]);
                date1.setMinutes(parts[1]);
                var parts = date2.split("/");
                date2 = new Date(0);
                date2.setFullYear(parts[2]);
                date2.setDate(parts[0]);
                date2.setMonth(parts[1] - 1);
                parts = strTime2.split(":");
                date2.setHours(parts[0]);
                date2.setMinutes(parts[1]);
                if (date2 < date1) retValue = "false";
            }
            if (format == "datePat2") {
                // Take the format: MM/dd/yyyy
                var parts = date1.split("/");
                date1 = new Date(0);
                date1.setFullYear(parts[2]);
                date1.setDate(parts[1]);
                date1.setMonth(parts[0] - 1);
                parts = strTime1.split(":");
                date1.setHours(parts[0]);
                date1.setMinutes(parts[1]);
                var parts = date2.split("/");
                date2 = new Date(0);
                date2.setFullYear(parts[2]);
                date2.setDate(parts[1]);
                date2.setMonth(parts[0] - 1);
                parts = strTime2.split(":");
                date2.setHours(parts[0]);
                date2.setMinutes(parts[1]);
                if (date2 < date1) retValue = "false";
            }
        }
    }
    if (retValue == "false") {
        return false;
    } else {
        return true;
    }
}

function getMenuWindow() {
    var menuWindow = null;
    var childWnd = window.self;
    var parentWnd = childWnd.opener;
    while (parentWnd != childWnd && typeof parentWnd != 'undefined' && parentWnd != null) {
        childWnd = parentWnd;
        parentWnd = childWnd.opener;
    }
    menuWindow = childWnd;
    return menuWindow;
}

function SwtWindow(windowName, parentWindowName, windowObject) {
    this.windowName = windowName;
    this.parentWindowName = parentWindowName;
    this.windowObject = windowObject;
}

function SwtWindowsContainer() {
    this.windowsCollection = new Array();
    this.windowIndex = 0;
    this.cascadeWidth = 0;
    this.cascadeHeight = 0;
    this.windowCaptionHeight = 20;
}

SwtWindowsContainer.prototype.getNextWindowName = function (windowName) {
    if (typeof this.windowsCollection != 'undefined' && this.windowsCollection != null) {
        windowName = windowName + this.windowIndex;
        ++this.windowIndex;
    }
    return windowName;
}

SwtWindowsContainer.prototype.registerWindow = function (windowName, parentWindowName, windowObject) {
    var loc = this.isMyWindowExists(windowName);
    if (loc == -1) {
        var SwtWindowObj = new SwtWindow(windowName, parentWindowName, windowObject);
        this.windowsCollection[this.windowsCollection.length] = SwtWindowObj;
    } else {
        var SwtWindowObj = this.windowsCollection[loc];
        SwtWindowObj.parentWindowName = parentWindowName;
        SwtWindowObj.windowObject = windowObject;
        this.windowsCollection[loc] = SwtWindowObj;
    }
}

SwtWindowsContainer.prototype.getNofChilds = function (windowName) {
    var length = this.windowsCollection;
    var count = 0;
    for (idx = 0; idx < this.windowsCollection.length; ++idx) {
        var SwtWindowObj = this.windowsCollection[idx];
        if (typeof SwtWindowObj.windowObject != 'undefined' && SwtWindowObj.windowObject != null && !SwtWindowObj.windowObject.closed && SwtWindowObj.parentWindowName == windowName)++count;
    }
    return count;
}

SwtWindowsContainer.prototype.isMyWindowExists = function (windowName) {
    var length = this.windowsCollection;
    var loc = -1;
    for (idx = 0; idx < this.windowsCollection.length; ++idx) {
        var SwtWindowObj = this.windowsCollection[idx];
        if (SwtWindowObj.windowName == windowName) loc = idx;
    }
    return loc;
}

SwtWindowsContainer.prototype.isInsidePredict = function () {
	
	try{
		
	    for (idx = 0; idx < this.windowsCollection.length; ++idx) {
	        var SwtWindowObj = this.windowsCollection[idx];
	      
	        if (typeof SwtWindowObj.windowObject != 'undefined' && SwtWindowObj.windowObject != null 
	        			&& SwtWindowObj.windowObject.closed != true
	        			&& SwtWindowObj.windowObject.document.hasFocus ()){
	        	
	        	return true;
	        }
	    }
	 
	}catch(error){
		
	}

    return false;
}

SwtWindowsContainer.prototype.unregisterWindow = function (windowName) {
    var idx = this.isMyWindowExists(windowName);
    if (idx != -1) {
        this.windowsCollection.splice(idx, 1);
    }
}

SwtWindowsContainer.prototype.closeMyChilds = function (windowName) {
    var length = this.windowsCollection;
    for (idx = 0; idx < this.windowsCollection.length; ++idx) {
        var SwtWindowObj = this.windowsCollection[idx];
        if (SwtWindowObj.parentWindowName == windowName) {
            SwtWindowObj.windowObject.close();
        }
    }
}

SwtWindowsContainer.prototype.getMyParentWindowName = function (windowName) {
    var loc = this.isMyWindowExists(windowName);
    return this.windowsCollection[loc].parentWindowName;
}

SwtWindowsContainer.prototype.cascadeWindows = function (windowName) {
    var parentWindowName = this.getMyParentWindowName(windowName);
    var length = this.windowsCollection;
    for (idx = 0; idx < this.windowsCollection.length; ++idx) {
        var SwtWindowObj = this.windowsCollection[idx];
        if (SwtWindowObj.parentWindowName == parentWindowName) {
        	var winTop = SwtWindowObj.windowObject.screenTop ? SwtWindowObj.windowObject.screenTop : SwtWindowObj.windowObject.screenY; 
            if (typeof SwtWindowObj.windowObject != 'undefined' && SwtWindowObj.windowObject != null && SwtWindowObj.windowObject.closed != true && winTop >= 0) SwtWindowObj.windowObject.focus();
        }
    }
}

SwtWindowsContainer.prototype.bringAllWindowsOnTop = function () {
    var length = this.windowsCollection;
    for (idx = 0; idx < this.windowsCollection.length; ++idx) {
        var SwtWindowObj = this.windowsCollection[idx]; {
            if (typeof SwtWindowObj.windowObject != 'undefined' && SwtWindowObj.windowObject != null && SwtWindowObj.windowObject.closed != true) SwtWindowObj.windowObject.focus();
        }
    }
}

SwtWindowsContainer.prototype.getCountMyParentChilds = function (windowName, parentWindowName) {
    var count = 0;
    var length = this.windowsCollection;
    for (idx = 0; idx < this.windowsCollection.length; ++idx) {
        var SwtWindowObj = this.windowsCollection[idx];
        if (SwtWindowObj.parentWindowName == parentWindowName) {
            if (typeof SwtWindowObj.windowObject != 'undefined' && SwtWindowObj.windowObject != null && SwtWindowObj.windowObject.closed != true)++count;
        }
    }
    return count;
}

SwtWindowsContainer.prototype.relocateMyWindow = function (windowName, windowObject, parentWindowName, parentWindowObject) {
    if (typeof windowObject != 'undefined' && windowObject != null && typeof parentWindowObject != 'undefined' && parentWindowObject != null) {
        var noOfBrothers = this.getCountMyParentChilds(windowName, parentWindowName);
        if (parentWindowObject != null) {
        	var winLeft = parentWindowObject.screenLeft ? parentWindowObject.screenLeft : parentWindowObject.screenX;
        	var winTop = parentWindowObject.screenTop ? parentWindowObject.screenTop : parentWindowObject.screenY;
            var finalX = winLeft + this.cascadeWidth * (noOfBrothers + 1);
            var finalY = winTop + this.cascadeHeight * (noOfBrothers + 1);
            var offsetX = 0;
            var offsetY = 0;
            if (parentWindowName == "") {
                offsetY = 35;
            }
            finalX = finalX + offsetX;
            finalY = finalY + offsetY;
            windowObject.moveTo(finalX, finalY);
        }
    }
}

function valueClosed() {
    return CLOSECHILD;
}
CLOSECHILD = 0;
SwtWindowsContainer.prototype.closeAllWindows = function() {
	CLOSECHILD = 1;

	var length = this.windowsCollection.length;
	while (length >= 0) {
		var SwtWindowObj = this.windowsCollection[length];
		if (SwtWindowObj != null
			&& typeof SwtWindowObj.windowObject != 'undefined'
			&& SwtWindowObj.windowObject != null
			&& SwtWindowObj.windowObject.closed != true)
			 this.windowsCollection[length].windowObject.close();
		length = length - 1;
	}
	this.windowsCollection.length = 0;

}
SwtWindowsContainer.prototype.bringChildsOnTop = function (windowName) {
    var count = 0;
    var length = this.windowsCollection;
    for (idx = 0; idx < this.windowsCollection.length; ++idx) {
        var SwtWindowObj = this.windowsCollection[idx];
        if (SwtWindowObj.parentWindowName == windowName) {
            if (typeof SwtWindowObj.windowObject != 'undefined' && SwtWindowObj.windowObject != null && SwtWindowObj.windowObject.closed != true) {
                SwtWindowObj.windowObject.focus();
                this.bringChildsOnTop(SwtWindowObj.windowObject.name);
            }
        }
    }
}

SwtWindowsContainer.prototype.isMenuItemIdExist= function(menuItemIdParam) {
   var menuItemIExist = false;
   for (idx = 0; idx < this.windowsCollection.length; ++idx) {
       var SwtWindowObj = this.windowsCollection[idx];
      if (SwtWindowObj != null) {
           var wnd = SwtWindowObj.windowObject;
           if (typeof SwtWindowObj.windowObject != 'undefined' && SwtWindowObj.windowObject != null && SwtWindowObj.windowObject.closed != true) {
                var menuItemId = extractMenuItemId(SwtWindowObj.windowName);
                if (menuItemId == menuItemIdParam){
                	menuItemIExist= true;	                		
 					break;
                }
		     }
		}
	}
	return menuItemIExist;
}

SwtWindowsContainer.prototype.isWindowNameExist=function(windowName) {
   var windowNameExist = false;
   for (idx = 0; idx < this.windowsCollection.length; ++idx) {
       var SwtWindowObj = this.windowsCollection[idx];
	      if (SwtWindowObj != null) {
	           var wnd = SwtWindowObj.windowObject;
	           if (typeof SwtWindowObj.windowObject != 'undefined' && SwtWindowObj.windowObject != null && SwtWindowObj.windowObject.closed != true) {
	                var wName = extractWindowName(SwtWindowObj.windowName);
	                if (wName == windowName){
	                	
	                	windowNameExist= true;
	                	break;
	                }
	          }
	       }
	  }
	 return windowNameExist;
}

function bringMyChildOnTop(windowName) {
    var menuWindow = getMenuWindow();
    var wndName = menuWindow.windowContainer.bringChildsOnTop(windowName);
}

function PopupMessage(msg) {
    this.statusCode = new String("");
    this.message = new String("");
    if (msg != null && msg.trim().valueOf() != "") {
        var idx = msg.indexOf("\n");
        if (idx > 0) {
            this.statusCode = new String(msg.substring(0, idx));
            this.message = new String(msg.substring(idx + 1));
        }
    }
}

PopupMessage.prototype.isErrorStatus = function () {
    if (this.statusCode.trim() == "ERROR") return true;
    else return false;
}

PopupMessage.prototype.getMessage = function () {
    return this.message;
}

function buildPrintURL(methodName, screenName) {
    var param = 'help.do?method=' + methodName + '&screenName=' + screenName;
    return param;
} /*This function finds accessId of child window, by searching from main menu*/

function getMenuAccessIdOfChildWindow(reqURLdoName) {
    //reqURLdoName - is the *.do name for the child window
    
    var menuAccessId = "2";
    var tmpWindow;
    if (window.opener != null) {
        tmpWindow = window.opener;
    } else {
        tmpWindow = window;
    }
    var windowObj;
    while (tmpWindow != null) {
        windowObj = tmpWindow;
        tmpWindow = tmpWindow.opener;
    }
    var objMainWindowBody = windowObj.document.body;
    var innerHtmlStr = new String(objMainWindowBody.innerHTML); // gets innerHtml of body of main window
    var reqURLdoNamePos = innerHtmlStr.search(reqURLdoName); // gets position of requestURL *.do name
    if (reqURLdoNamePos != -1) {
        var subStrReqURLdoName = innerHtmlStr.substr(reqURLdoNamePos, 200); // gets sub-String of first 200 character from *.do
        var menuAccessIdSubStrPos = new String(subStrReqURLdoName).search("menuAccessId"); // gets position of 'menuAccessId' parameter
        if (menuAccessIdSubStrPos != -1) {
            menuAccessId = subStrReqURLdoName.substr(menuAccessIdSubStrPos + (new String("menuAccessId").length + 1), 1); // gets value of AccessId        
        }
    }
    return new String(menuAccessId).trim();
}

/*Start:Code Modified by Chinniah on 15-Sep-2011 for Mantis 1483: Grant the role only view access to this menu option, I find that I am still able to 'Auth' and 'Change', where the buttons ought to be disabled*/
/**
*This method is used to find the Menuaccess Id for Input athorise queue and Sweep Authorise queue using their Menu itemId
@param reqURLdoName
*@return menuAccessId
**/
function getMenuAccessIdOfChildWindowByItemId(reqURLdoName) {
    //reqURLdoName - is the *.do name for the child window
    reqURLdoName = reqURLdoName + "Window";
	//intializing the menuAccessId
    var menuAccessId = "2";
	var tmpWindow;
    if (window.opener != null) {
        tmpWindow = window.opener;
    } else {
        tmpWindow = window;
    }
    var windowObj;
    while (tmpWindow != null) {
        windowObj = tmpWindow;
        tmpWindow = tmpWindow.opener;
    }
    var objMainWindowBody = windowObj.document.body;
    var innerHtmlStr = new String(objMainWindowBody.innerHTML); // gets innerHtml of body of main window
	//get the position of the selected child window in html of the main window
    var reqURLdoNamePos = innerHtmlStr.search(reqURLdoName); // gets position of requestURL *.do name
    if (reqURLdoNamePos != -1) {
        var subStrReqURLdoName = innerHtmlStr.substr(reqURLdoNamePos-200, 200); // gets sub-String of first 200 character from *.do
        var menuAccessIdSubStrPos = new String(subStrReqURLdoName).search("menuAccessId"); // gets position of 'menuAccessId' parameter
		//get the menuAccessId of the selected child window
        if (menuAccessIdSubStrPos != -1) {
		    menuAccessId = subStrReqURLdoName.substr(menuAccessIdSubStrPos + (new String("menuAccessId").length + 1), 1); // gets value of AccessId        
        }
    }
    return new String(menuAccessId).trim();
}
/*End:Code Modified by Chinniah on 15-Sep-2011 for Mantis 1483: Grant the role only view access to this menu option, I find that I am still able to 'Auth' and 'Change', where the buttons ought to be disabled*/

/**
 * submitEnter
 * 			
 * This method is used to submit the element while press on enter key.
 */

function submitEnter(element,event) {
	var event = (window.event|| event);
    if (event.keyCode == 13) 
    	element.click();
}


/**
 * setFocusOnElement
 * 			
 * This method is used to set the focus for given element.
 */

function setFocusOnElement(element) {
    if (document.forms[0].elements[element] != null) document.forms[0].elements[element].focus();
}


/**This function is added by Atef to load SWF flash object and to fit the 
* compatiblity problemes with IE10 IE11 chrome and Firefox
*/
function getFlashObject(fileName){
	if (window.document[fileName]){
		return window.document[fileName];
	}
	if (navigator.appName.indexOf("Microsoft Internet")==-1){
		if (document.embeds && document.embeds[fileName])
			return document.embeds[fileName];
	}
	else{
		return document.getElementById(fileName);
	}
}
/**
 * This function is created to get the format of the Date 
 * @returns String
 */
function getDateFormat(){
	var format = getMenuWindow().dateFormat;
	return format== (null || undefined) ? 'undefined':format
}
/**
 * This function is created to convert the Date object as String using the ISO standard.
 * @param dateString
 * @param pattern
 * @returns
 */
function dateStringToISO(dateString, pattern){
	 var moment_ = moment(dateString, pattern);
	 return moment_.format("YYYY-MM-DD HH:mm:ss");
}


var openAjaxRequestMethod = window.XMLHttpRequest.prototype.open,  
sendAjaxRequestMethod = window.XMLHttpRequest.prototype.send;

function openReplacement(method, url, async, user, password) {
	if(url.indexOf('?')== -1) {
		var sessionKey =   getCrsfMetaTagContent();
		// add isAjax
		url= url+'?isAjax=true';
	}
	else {
		var sessionKey =   getCrsfMetaTagContent();
		// add isAjax
		url= url+'&isAjax=true';
	}
	//this._url = url;
	return openAjaxRequestMethod.apply(this, arguments);
}

function sendReplacement(data) {  
	if(this.onreadystatechange) {
	  this._onreadystatechange = this.onreadystatechange;
	}
	
	
	this.onreadystatechange = onReadyStateChangeReplacement;
	this.setRequestHeader('csrf', getCrsfMetaTagContent());
	return sendAjaxRequestMethod.apply(this, arguments);
}

function onReadyStateChangeReplacement() {  


if(this._onreadystatechange) {
  return this._onreadystatechange.apply(this, arguments);
}
}

window.XMLHttpRequest.prototype.open = openReplacement;  
window.XMLHttpRequest.prototype.send = sendReplacement;



//overriding XMLHttpRequest() for IE 8 and above
//window.old_XMLHttpRequest = window.XMLHttpRequest;
//window.XMLHttpRequest = function() {
//	if (isIEup10 && window.ActiveXObject)// ActiveX version
//	{
//		return new ActiveXObject("Microsoft.XMLHTTP"); // Internet Explorer 
//	}else {
//		return new old_XMLHttpRequest(); // Firefox, Safari,Google Chrome ...
//	} 
//}


function getCrsfMetaTagContent() { 
	   var metas = document.getElementsByTagName('meta'); 

	   for (var i=0; i<metas.length; i++) { 
	      if (metas[i].getAttribute("name") == "csrf_token") { 
	         return metas[i].getAttribute("content"); 
	      } 
	   } 

	    return "";
	}


function encrypt(clearText) {
	return getMenuWindow().encrypt1(clearText);
}

function encryptPass(userId, clearText) {
	return getMenuWindow().encryptPass(userId, clearText);
}

function ShowAutoLogoutAlert() {
	let timerInterval
	try {
		Swal.fire({
			timer: 60000,
			html: loggedoffIn + '<br> <b></b> <b>' + time + '</b> <br>' + stayLoggedIn,
			timerProgressBar: true,
			confirmButtonText: 'OK',
			cancelButtonText: 'Logout',
			showCancelButton: true,
			showCloseButton: true,
			preConfirm: () => {
				raiseAlert();
			},
			onOpen: () => {
				timerInterval = setInterval(() => {
					const content = Swal.getContent();
					if (content) {
						const b = content.querySelector('b');
						if (b) {
							b.textContent = (Swal.getTimerLeft() / 1000).toFixed();
						}
					}
				}, 100)
			},
			onClose: () => {
				raiseAlert();
			}
		}).then((result) => {
			if (result.dismiss === Swal.DismissReason.timer) {
				onUnLoad();
			} else if (
				result.dismiss === Swal.DismissReason.cancel) {
				onUnLoad();
			}

		})

	} catch (e) {
		console.log(e);
	}

}

function raiseAlert() {
	let maxPeriod = maxSessionTimeOutPeriod * 60 * 1000;
	setTimeout(function() {
		ShowAutoLogoutAlert();
	}, maxPeriod)
}
function removeCookie(name){
	document.cookie = name + '=; Max-Age=0'
}
function clearPreLogin() {
    removeCookie('dXNl-cm5h-bWU');
    removeCookie('JSESSIONID');
    
}