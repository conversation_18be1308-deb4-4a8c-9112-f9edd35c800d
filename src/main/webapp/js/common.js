//
function trim(unTexto)
{
	var unString = new String(unTexto);
	return unString.replace(/(^\s*)|(\s*$)/g, "");
}

//
function setSelectValue(sel, val)
{
	for (var i=0; i<sel.options.length; i++)
	{
		if (sel.options[i].value == val)
		{
			sel.selectedIndex = i;
			return;
		}
	}
}

//
function searchParentByClass(elm, className)
{
	if ((elm == null) || (elm.className == className))
	{
		return elm;
	}
	return searchParentByClass(elm.parentElement, className);
}

//
function searchChildByClass(elm, className)
{
	if ((elm == null) || (elm.className == className))
	{
		return elm;
	}
	var arr = elm.children;
	if (arr)
	{
		var arrlen = arr.length;
		for (var i=0; i<arrlen; i++)
		{
			var result = searchChildByClass(arr[i], className);
			if (result != null)
			{
				return result;
			}
		}
	}
	return null;
}

//
Array.prototype.contains = function(elm)
{
	for (var i=0; i<this.length; i++)
	{
		if (this[i] == elm)
		{
			return true;
		}
	}
	return false;
}

//
Array.prototype.remove = function(elm)
{
	var i=0;
	while (i<this.length)
	{
		if (this[i] == elm)
		{
			this.splice(i, 1);
		}
		else
		{
			i++;
		}
	}
}

//
function Option(html, value, css, selected)
{
	this.html = html;
	this.value = value;
	this.css = css;
	this.selected = selected;
}

//
function addClassName(el, sClassName) {
	var s = el.className;
	var p = s.split(" ");
	var l = p.length;
	for (var i = 0; i < l; i++) {
		if (p[i] == sClassName)
			return;
	}
	p[p.length] = sClassName;
	el.className = p.join(" ").replace( /(^\s+)|(\s+$)/g, "" );
}

//
function removeClassName(el, sClassName) {
	var s = el.className;
	var p = s.split(" ");
	var np = [];
	var l = p.length;
	var j = 0;
	for (var i = 0; i < l; i++) {
		if (p[i] != sClassName)
			np[j++] = p[i];
	}
	el.className = np.join(" ").replace( /(^\s+)|(\s+$)/g, "" );
}
function getFilterTitle(title)
{
	var sortTitle = new String(title);
	var filterTitle = sortTitle.replace("Sort by","Filter on");
	return filterTitle;
}

function isRowSelected(rowElement)
{
	var className = new String(rowElement.className);
	if(className.indexOf("select",0) >= 0 ) 
		return true;
	else 
		return false;
}
// Start : Mantis 1615 Added by Nithiyananthan on 02-02-2012
//Trims all text and textarea fields in the form
function elementTrim(theForm)
{
	// Gets the elements in the form 
	for(var i=0;i < theForm.elements.length;i++)	{
		var formField = theForm.elements[i];
		//Checks form field is null and text field
		if(formField !=null &&( formField.type == "text" || formField.type== "textarea")) {
			//Trims the field 
			formField.value=formField.value.trim();		
   }
	}
}
// End : Mantis 1615 Added by Nithiyananthan on 02-02-2012

