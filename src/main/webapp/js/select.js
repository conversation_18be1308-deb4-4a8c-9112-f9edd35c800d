var overOptionCss = "background: highlight; color: highlighttext";
var sizedBorderCss = "2 inset buttonhighlight";

var globalSelect;	//This is used when calling an unnamed selectbox with onclick="this.PROPERTY"

var ie4 = (document.all != null);

var q = 0;


function initSelectBox(el) {
	

	var size = el.getAttribute("size");

// These two lines combined with execution in optionClick() allow you to write:

	el.options = el.children[1].children;
	el.selectedIndex = findSelected(el);	//Set the index now!
// Some methods that are supported on the real SELECT box
	el.remove = new Function("i", "int_remove(this,i)");
	el.item   = new Function("i", "return this.options[i]");
	el.add    = new Function("e", "i", "int_add(this, e, i)");
	el.addOption = new Function("o", "i", "int_addOption(this, o, i)");
	
	el.setDropdownHTML = new Function("o", "i", "setDropdown_HTML(this, o, i)");
	
	el.addOptionHTML = new Function("o", "i", "return addOption_getHTML(this, o, i)");
	el.changeStyle = new Function("f", "int_changeStyle(this, f)");
	el.populate = int_populate;
	el.populate_server = int_populate_server;
	el.clear = int_clear;
// The real select box let you have lot of options with the same NAME. In that case the item
// needs two arguments. When using DIVs you can't have two with the same NAME (or ID) and
// then there is no need for the second argument

	el.dropdown = el.children[1];

	if (size != null) {
		if (size > 1) {
			el.size = size;
			el.dropdown.style.zIndex = 0;
			initSized(el);
		}
		else {
			el.size = 1;
			el.dropdown.style.zIndex = 99;
			if (el.dropdown.offsetHeight > 200) {
				el.dropdown.style.height = "200";
				el.dropdown.style.overflow = "auto";
			}
		}
	}

	if (el.options.length > 0)
	{
		el.options[el.selectedIndex].selected = true;
		highlightSelected(el,true);
	}
}

function int_clear()
{
	this.dropdown.innerHTML = "";
}

/**
  * this method is used to populate the filter values.
  * @param value
  * @param columnIndex
  **/
function int_populate(values, columnIdx)
{
	var curValue = null;
	if (this.options.length > 0)
	{
		curValue = this.options[this.selectedIndex].getAttribute('value');
	}

	this.clear();
	var optionsHTML = "";
	
	optionsHTML+=this.addOptionHTML(new Option("All", "special_all"));

	var lastValue = null;
	var empties = false;
	var numberOfOptions = 1;
	
	for (var i=0; i<values.length; i++)
	{
		var row = values[i].element;
		var numFilters = row.filteredby?row.filteredby.length:0;
		if ((numFilters == 0) || ((numFilters == 1) && (row.filteredby[0] == columnIdx)))
		{
			var text = row.cells[columnIdx].innerText;
			if (text.trim() == "")
			{
				empties = true;
			}
			else
			{
				var value = values[i].value;
				if (value != lastValue)
				{
				/* code Added  by Nageswara Rao on 2_Feb_2012 for mantis 1580:"Filter is working properly ." */
//					this.addOption(new Option(text.replace(/ /g ,'&nbsp;'), "value_" + value));
					optionsHTML+=this.addOptionHTML(new Option(text.replace(/ /g ,'&nbsp;'), "value_" + value));
					numberOfOptions++;
					
					lastValue = value;
				}
			}
		}
	}

	
//if the the length greater than 1 and executing and empties and not empty.
	if (empties && numberOfOptions>1 )
	{
		optionsHTML+=this.addOptionHTML(new Option("(Empty)", "special_empties"));
		optionsHTML+=this.addOptionHTML(new Option("(Not empty)", "special_nonempties"));
	}
	// if the length is equals to one then only executing empties.
	if(empties &&numberOfOptions==1){
		optionsHTML+=this.addOptionHTML(new Option("(Empty)", "special_empties"));
	}
	
	this.setDropdownHTML(optionsHTML)

	var sel = -1;
	for (var i=0; i<this.options.length; i++)
	{
		if (this.options[i].getAttribute('value') == curValue)
		{
			sel = i;
		}
	}
	if (sel == -1)
	{
		sel = 0;
	}

	this.selectedIndex = sel;
	this.options[this.selectedIndex].selected = true;
	highlightSelected(this, true);
}

function int_populate_server(values, columnIdx)
{
	
	var curValue = null;

	if (this.options.length > 0)
	{
		curValue = this.options[this.selectedIndex].getAttribute('value');
	}

	this.clear();
	
	for (var i=0; i<values.length; i++)
	{
		this.addOption(new Option(values[i],values[i]) );
	}
	
	
}

function int_changeStyle(el, filtered)
{
	var color = filtered?"black":"white";
	var backgroundcolor = filtered?"#FFC134":"#B8DAFB"; 

	
	var buttonElm = searchChildByClass(el, "button").children[0];
	buttonElm.style.color = color;
	buttonElm.parentElement.style.backgroundColor = backgroundcolor;
}

function int_remove(el,i) {
	if (el.options[i] != null)
		el.options[i].outerHTML = "";
}

function addHTMLOption(el, html, i)
{
	if ((i == null) || (i >= el.options.length))
		i = el.options.length-1;


	if (el.options.length == 0)
	{
		el.dropdown.innerHTML = html;
	}
	else
	{
		el.options[i].insertAdjacentHTML("AfterEnd", html);
	}
}

function setDropdown_HTML(el, html, i)
{
	el.dropdown.innerHTML = html;
}

function int_add(el, e, i) {
	var html = "<div class='option' noWrap";
	if (e.value != null)
		html += " value='" + e.value + "'";
	if (e.style.cssText != null)
		html += " style='" + e.style.cssText + "'";
	html += ">";
	if (e.text != null)
		html += e.text;
	html += "</div>\n"

	addHTMLOption(el, html, i);
}

function int_addOption(el, o, i) {
	var str = '<div class="option"';
	if (o.value != null)
		str += ' value="' + o.value + '"';
	if (o.css != null)
		str += ' style="' + o.css + '"';
	if (o.selected != null)
		str += ' selected';
	str += '>\n';
	str += o.html;
	str += '</div>\n';

	addHTMLOption(el, str, i);
}

function addOption_getHTML(el, o, i) {
	var str = '<div class="option"';
	if (o.value != null)
		str += ' value="' + o.value + '"';
	if (o.css != null)
		str += ' style="' + o.css + '"';
	if (o.selected != null)
		str += ' selected';
	str += '>\n';
	str += o.html;
	str += '</div>\n';
	return str;
}

function initSized(el) {

	var h = 0;
	el.children[0].style.display = "none";

	el.dropdown.style.visibility = "visible";

	if (el.dropdown.children.length > el.size) {
		el.dropdown.style.overflow = "auto";
		for (var i=0; i<el.size; i++) {
			h += el.dropdown.children[i].offsetHeight;
		}

		if (el.dropdown.style.borderWidth != null) {
			el.dropdown.style.pixelHeight = h + 4; 
		}

		else
			el.dropdown.style.height = h;

	}

	el.dropdown.style.border = sizedBorderCss;


	el.style.height = el.dropdown.style.pixelHeight;
}



// This function returns the first selected option and resets the rest
// in case some idiot has set more than one to selcted :-)
function findSelected(el) {
	var selected = null;
	if (typeof(el.children[1]) == "undefined")
	{
		alert("NoSuchElementException: select.js: findSelected");
		return;
	}

	var ec = el.children[1].children;	//the table is the first child
	var ecl = ec.length;

	for (var i=0; i<ecl; i++) {
		if (ec[i].getAttribute("selected") != null) {
			if (selected == null) {	// Found first selected
				selected = i;
			}
			else
				ec[i].removeAttribute("selected");	//Like I said. Only one selected!
		}
	}
	if (selected == null)
		selected = 0;	//When starting this is the most logic start value if none is present

	
	return selected;
}
// New Function Added by Atef to find the selected value in the dropdown list
function findSelected2(el,e2) {
	
	var selected = null;
	if (typeof(el.children[1]) == "undefined")
	{
		alert("NoSuchElementException: select.js: findSelected");
		return;
	}
	var ec = el.children[1].children;	//the table is the first child
	var ecl = ec.length;
	
	for (var i=0; i<ecl; i++) {
		if (ec[i].innerText == e2.innerText) {
				selected = i;
		}
	}
	if (selected == null)
		selected = 0;	//When starting this is the most logic start value if none is present
	
	return selected;
}

function GetOffset (object, offset) {
    if (!object)
        return;
    offset.x += object.offsetLeft;
    offset.y += object.offsetTop;
    if(object.offsetLeft != 0){
    	offset.left = object.offsetLeft;
    }

    GetOffset (object.offsetParent, offset);
}

function GetScrolled (object, scrolled) {
    if (!object)
        return;
    scrolled.x += object.scrollLeft;
    scrolled.y += object.scrollTop;

    if (object.tagName.toLowerCase () != "html") {
        GetScrolled (object.parentNode, scrolled);
    }
}

function getOffsetXtoWindow(el){
	
	  var offset = {x : 0, y : 0, left :0};
      GetOffset (el, offset);

      var scrolled = {x : 0, y : 0};
      GetScrolled (el.parentNode, scrolled);

      var posX = offset.x - scrolled.x;
      
      return posX-offset.left;
}

function getOffsetYtoWindow(el){
	
	var offset = {x : 0, y : 0};
	GetOffset (el, offset);
	
	var scrolled = {x : 0, y : 0};
	GetScrolled (el.parentNode, scrolled);
	
	var posY = offset.y - scrolled.y;
	
	return posY;
}



function toggleDropDown(el) {
	
	if (window.dateFlag === undefined) {  
		showDropDown(el.dropdown);
		if(!isIEunder10){
			var posX = getOffsetXtoWindow(el)
		    el.dropdown.style.left = posX;
		}
	} else {
	if (dateFlag == true) {
		var flag = sortDateValidation()
		if (flag == true) {		
			if (el.size == 1) {
		if (el.dropdown.style.visibility == "")
			el.dropdown.style.visibility = "hidden";

		if (el.dropdown.style.visibility == "hidden"){
			showDropDown(el.dropdown);
			if(!isIEunder10){
				var posX = getOffsetXtoWindow(el)
		        el.dropdown.style.left = posX;
			}
		}
		else{
			hideDropDown(el.dropdown);
		}
	}
		} else {
			hideDropDown(el.dropdown);
		}
	}
	}
}


function optionClick_server_filter(e){
	var event = (window.event|| e);
	var target = (event.srcElement || event.target);
	el = getReal(target, "className", "option");
	/* Related to Mantis 2073: code Added to avoid javascript error when  using the dropDown filter by M.Bouraoui on 29/03/2013 */  
	if (el.className != "option") return;	
	selectBox = el.parentElement.parentElement;
	
	var filtervalue = target.innerHTML;
	var colindex = selectBox.id.split("__")[1].split("_")[1];
	
	if (el.backupCss != null)
		el.style.cssText = el.backupCss;		
		toggleDropDown(selectBox);
		highlightSelected(selectBox, true);
		
	
	filtervalue = filtervalue.replace(/^[\r\n]+/g, "");	
	optionClick_server_filter_JSP(colindex,filtervalue,"filter");
	return true;

	
}




function optionClick(event) {
	var event = (event||window.event);
	var target = event.target || event.srcElement;
	if(typeof filterValues != 'undefined'){
		optionClick_server_filter(event);

	}else{

	el = getReal(target, "className", "option");
	
	

	if (el.className != "option") return;

	if (el.className == "option") {
	
		selectBox = el.parentElement.parentElement;

//Code commented by Atef doesn't work in IE10 and IE11
//		oldSelected = selectBox.dropdown.children[findSelected(selectBox)]
//		if(oldSelected != el) {
//			oldSelected.removeAttribute("selected");
//			el.setAttribute("selected", 1);
//			selectBox.selectedIndex = findSelected(selectBox);
//		}
		//Added to make the filter work in IE 10 and 11
		selectBox.selectedIndex = findSelected2(selectBox,el);

			if (selectBox.id != "") {// For this to work you need to replace this with an ID or name
		
			var column = selectBox.id.split("__")[1].split("_")[1];
			var onChangeFunction = new String(selectBox.getAttribute("onchange")); 

			onChangeFunction = onChangeFunction.replace(/this/g, selectBox.id);
			selectBox.onchange = onChangeFunction;
			eval(onChangeFunction);
			}
			else {
				globalSelect = selectBox;
				var onChangeFunction = new String(selectBox.getAttribute("onchange")); 
				onChangeFunction = onChangeFunction.replace(/this/g, "globalSelect");
				selectBox.onchange = onChangeFunction;
				eval(onChangeFunction);
			}

		if (el.backupCss != null)
			el.style.cssText = el.backupCss;		
		
		
		toggleDropDown(selectBox);
		
		highlightSelected(selectBox, true);
		
	}
	}
}

function optionClickManual(element) {


	var optionElement = element;

	el = getReal(optionElement, "className", "option");
	

	if (el.className != "option") return;

	if (el.className == "option") {
		selectBox = el.parentElement.parentElement;

		oldSelected = selectBox.dropdown.children[findSelected(selectBox)]


		if(oldSelected != el) {
			oldSelected.removeAttribute("selected");
			el.setAttribute("selected", 1);
			selectBox.selectedIndex = findSelected(selectBox);

		}

		if (selectBox.onchange != null) {	// This executes the onchange when you chnage the option
			if (selectBox.id != "") {		// For this to work you need to replace this with an ID or name
				
				var onChangeFunction = new String(selectBox.getAttribute("onchange")); 
				onChangeFunction = onChangeFunction.replace(/this/g, selectBox.id);
				selectBox.onchange = onChangeFunction;
				eval(onChangeFunction);
			}
			else {
				globalSelect = selectBox;
				var onChangeFunction = new String(selectBox.getAttribute("onchange")); 
				onChangeFunction = onChangeFunction.replace(/this/g, "globalSelect");
				selectBox.onchange = onChangeFunction;
				eval(onChangeFunction);
			}
		}

		if (el.backupCss != null)
			el.style.cssText = el.backupCss;
	
		
		highlightSelected(selectBox, true);
	}
}

function keyPressed(element){
var keyValue = 0;
var myCharCapsArr = new Array('A','B','C','D','E','F','G','H','I','J','K','L','M','N','O','P','Q','R','S','T','U','V','W','X','Y','Z');

if (document.layers)
        Key = e.which;  //Which holds the ascii value of the key pressed by user
    else
        Key = window.event.keyCode;
    if (Key >= 48 && Key <= 57){   //Gets the key press values only for Alphabets and numeric
    	keyValue = Key; 
    }else if (Key >= 65 && Key <= 90){
        keyValue = Key;
    }else if (Key >= 97 && Key <= 122){
    	keyValue = Key;
    }
	var j = 0;
	
	var KeyChar = '';
	var keyNum = 0;
	var flag = false;
	
	for (var num=48;num<=57;num++)  
	{
		if (keyValue == num){
			keyNum = j;
			flag = true;
		}
		j++;
	}
	j = 0;
	for (var i=65;i<=90;i++)
	{
		if (keyValue == i){
			KeyChar = myCharCapsArr[j];
		}
		j++;
	}
	j = 0;
	for (var i=97;i<=122;i++)
	{
		if (keyValue == i){
			KeyChar = myCharCapsArr[j];
		}
		j++;
	}
	
	srcElement = element.children[1];
	var charLength = 0;
	var selectedPosition = 0;
	for (var a = 1;a<srcElement.children.length;a++){ //To get the current position of the filter criteria
		if(flag && ((srcElement.children[a].innerHTML).trim().charAt(0) == ''+keyNum)){ // If the key is numeric and key is equal to the first character of the filter criteria
			charLength = charLength + 1;    // Gets the total no of filter criterias starting with the same character as of key press
			if (srcElement.children[a].backupCss != null){   // If the pressed key is already selected then the next filter criteria will be selected with the same key
				selectedPosition = charLength;    //Stores the selected position of the filter criteria if the previous and current key press values are same
			}
		}else if ((srcElement.children[a].value).toUpperCase().charAt(6) == KeyChar) // If the key is alphabet and key is equal to the first character of the filter criteria
		{
			charLength = charLength + 1;   // Gets the total no of filter criterias starting with the same character as of key press
			if (srcElement.children[a].backupCss != null){ // If the pressed key is already selected then the next filter criteria will be selected with the same key
				selectedPosition = charLength;    //Stores the selected position of the filter criteria if the previous and current key press values are same
			}
		}
	}
	if (selectedPosition >= charLength) //If the selected position and total no of filter criterias starting with the same character as of key press are equal then selected position will be made as zero to select the first one again. 
		selectedPosition = 0;
	var selectedKeyCharLength = 1;
	for (var k = 1;k<srcElement.children.length;k++){
		if(flag && ((srcElement.children[k].innerHTML).trim().charAt(0) == ''+keyNum)){ // If the key is numeric and key is equal to the first character of the filter criteria
			
			if (selectedPosition < selectedKeyCharLength || selectedPosition == 0){ // To set the selected index visible
				if(k > 5)
					srcElement.scrollTop = (k * 16) - 90;
				else
					srcElement.scrollTop = 0;
				for (var l = 1;l<srcElement.children.length;l++){ //Removing Highlighting from the previously selected filter criteria.
					if (srcElement.children[l].className == "option") {
						if (srcElement.children[l].backupCss != null){
							srcElement.children[l].style.cssText = srcElement.children[l].backupCss;
							srcElement.children[l].backupCss = null;
						}
					}
				}
				if (srcElement.children[k].className == "option") { // Setting the highlighting for the current selected filter criteria.
					if (srcElement.children[k].backupCss == null)
						srcElement.children[k].backupCss = srcElement.children[k].style.cssText;
					highlightSelected(srcElement.children[k].parentElement.parentElement, false);
					srcElement.children[k].style.cssText = srcElement.children[k].backupCss + "; " + overOptionCss;
					this.highlighted = true;					
					break;
				}
			}
			selectedKeyCharLength = selectedKeyCharLength + 1;
		}else if ((srcElement.children[k].value).toUpperCase().charAt(6) == KeyChar) // If the key is Alphabet- and key is equal to the first character of the filter criteria
		{
			if (selectedPosition < selectedKeyCharLength || selectedPosition == 0){  // To set the selected index visible
				if(k > 5)
					srcElement.scrollTop = (k * 16) - 90;
				else
					srcElement.scrollTop = 0;
				for (var l = 1;l<srcElement.children.length;l++){
					if (srcElement.children[l].className == "option") {  //Removing Highlighting from the previously selected filter criteria.
						if (srcElement.children[l].backupCss != null){
							srcElement.children[l].style.cssText = srcElement.children[l].backupCss;
							srcElement.children[l].backupCss = null;
						}
					}
				}
				if (srcElement.children[k].className == "option") {  // Setting the highlighting for the current selected filter criteria.
					if (srcElement.children[k].backupCss == null)
						srcElement.children[k].backupCss = srcElement.children[k].style.cssText;
					highlightSelected(srcElement.children[k].parentElement.parentElement, false);
					srcElement.children[k].style.cssText = srcElement.children[k].backupCss + "; " + overOptionCss;
					this.highlighted = true;					
					break;
				}
			}
			selectedKeyCharLength = selectedKeyCharLength + 1;
		}
	}
}

function optionOver(event) {
	var toEl = getReal(event.target ? event.target : event.toElement, "className", "option");
	if (toEl.className != "option") return;
	var fromEl = getReal(event.relatedTarget ? event.relatedTarget : event.fromElement, "className", "option");
	if (toEl == fromEl) return;
	var el = toEl;

	if (el.className == "option") {
		if (el.backupCss == null){
			el.backupCss = el.style.cssText;
		}
		highlightSelected(el.parentElement.parentElement, false);
		el.style.cssText = el.backupCss + "; " + overOptionCss;
		this.highlighted = true;
	}
}

/* Mantis 2073: function Added to prevent drag Drop events on dropDown Filter by M.Bouraoui on 28/03/2013 */
function optionDown(e)
{
	var target;
	// cross-Browser event
	var event = e || window.event;
	// set the target of the occured event 
	target = event.srcElement||event.target;
	// check wether the scroll bar is not clicked
	if (target.className != 'dropDown')
	{
		if (typeof target.onselectstart != "undefined") //IE
			target.parentElement.onselectstart=function(){ return false; };
	}
}

function optionOut(event) {
	
	var toEl = getReal(event.relatedTarget ? event.relatedTarget : event.toElement, "className", "option");
	var fromEl = getReal(event.target ? event.target :event.fromElement , "className", "option");
	if (fromEl.className != "option") return;

	if (fromEl == fromEl.parentElement.children[findSelected(fromEl.parentElement.parentElement)]) {
		if (toEl == null)
			return;
		if (toEl.className != "option")
			return;
	}

	if (toEl != null) {
		if (toEl.className != "option") {
			if (fromEl.className == "option")
				highlightSelected(fromEl.parentElement.parentElement, true);
		}
	}

	if (toEl == fromEl) return;
	var el = fromEl;

	if (el.className == "option") {
		if (el.backupCss != null)
			el.style.cssText = el.backupCss;
	}

}

function highlightSelected(el,add) {
	var selectedIndex = findSelected(el);

	selected = el.children[1].children[selectedIndex];

	if (add) {
		if (selected.backupCss == null)
			selected.backupCss = selected.style.cssText;
		selected.style.cssText = selected.backupCss + "; " + overOptionCss;
	}
	else if (!add) {
		if (selected.backupCss != null)
			selected.style.cssText = selected.backupCss;
	}
}


function hideShownDropDowns(e) {
	var event=  (e||window.event);
	var el;
	if(event !=null)
		el = getReal((event.srcElement||event.target), "className", "select");
	var spans = document.getElementsByTagName("SPAN");
	var selects = new Array();
	var index = 0;

	for (var i=0; i<spans.length; i++) {
		if ((spans[i].className == "select") && (spans[i] != el)) {
			dropdown = spans[i].dropdown;
			if ((spans[i].size == 1) && (dropdown.style.visibility == "visible"))
				selects[index++] = dropdown;
		}
	}

	for (var j=0; j<selects.length; j++) {
		hideDropDown(selects[j]);
	}

	hideswcomboboxes(event);
}

function getDivElement(srcElement)
{
	var comboBoxDivId = "";

	
	if(srcElement && srcElement.tagName =="INPUT")
	{

		if(srcElement.id)
		{

			
			var srcElementId = new String(srcElement.id);

			if(srcElementId)
			{

				var underscoreIdx = srcElementId.indexOf("_");
				if(underscoreIdx >= 0 )
				{
					var elementIndex = srcElementId.substring(underscoreIdx,srcElementId.length);
					comboBoxDivId = "dropdowndiv"+elementIndex;
				}
			}
		}
		
	}
	
	return comboBoxDivId;

}


function hideswcomboboxes(event)
{
	
	var dropdowndivid;
	if(event != null)
		dropdowndivid = getDivElement((event.srcElement||event.target));

	
	var divs = document.getElementsByTagName("DIV");
	var selects = new Array();
	var index = 0;

	for (var i=0; i<divs.length; i++) {
		if ((divs[i].className == "swdropdown") && (divs[i].id) && (divs[i].id != dropdowndivid)) {
			dropdown = divs[i];
			if (dropdown.style.visibility == "visible")
				selects[index++] = dropdown;
		}
	}
	
	for (var j=0; j<selects.length; j++) {
		hideDropDown(selects[j]);
	}


}
function hideDropDown(el) {
	if (typeof(fade) == "function")
		fade(el, false);
	else
		el.style.visibility = "hidden";
}

function showDropDown(el) {
	if (typeof(fade) == "function")
		fade(el, true);
	else if (typeof(swipe) == "function")
		swipe(el, 2);
	else{
		el.style.visibility = "visible";
		
	}
}

function initSelectBoxes() {
	var spans = document.getElementsByTagName("SPAN");
	var selects = new Array();
	var index = 0;

	for (var i=0; i<spans.length; i++) {
		if (spans[i].className == "select")
			selects[index++] = spans[i];
	}

	for (var j=0; j<selects.length; j++) {
		initSelectBox(selects[j]);
	}
}

function getReal(el, type, value) {
	temp = el;
	while ((temp != null) && (temp.tagName != "BODY")) {
		if (eval("temp." + type) == value) {
			el = temp;
			return el;
		}
		temp = temp.parentElement;
	}
	return el;
}


// global hook
document.onclick = hideShownDropDowns;
