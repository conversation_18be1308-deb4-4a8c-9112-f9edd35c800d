//alert("25");

//Function to submit form 
function submitForm(methodName, cacheSearchFlag){
	
	setTabInfo();
	
	document.forms[0].method.value = methodName;
	document.forms[0].isCacheSearch.value = cacheSearchFlag; 
	document.forms[0].callFromParent.value = callFromParent; 
	// send scroll params to server  --Amit 
	setScrollParam(document.forms[0].scrollLeft,document.forms[0].scrollTop,document.forms[0].scrollTableLeft,document.forms[0].scrollTableTop);
	//alert("The value of the Flag is==>" + document.forms[0].isCacheSearch.value + "===");

	/* Start: Refer to Smart-Predict_SRS_PhaseII_Open_Unexpected_0.1.doc */
	var accountBreakDownScreen = document.getElementById("screenSelector").getElementsByTagName("input")[0];
	var movementSummaryScreen = document.getElementById("screenSelector1").getElementsByTagName("input")[0];

	if(accountBreakDownScreen.checked == true)
	{
		document.forms[0].screenSelectorFlag.value = accountBreakDownScreen.value;
	} else {
		document.forms[0].screenSelectorFlag.value = movementSummaryScreen.value;
	}
	/* End: Refer to Smart-Predict_SRS_PhaseII_Open_Unexpected_0.1.doc */

	document.forms[0].submit();
}


// Start UAT Defect 
//This finction is to send the scroll bar parameters to server -- Amit
function setScrollParam(scrollLeftObj,scrollTopObj,scrollTableLeftObj,scrollTableTopObj) {
	if (document.documentElement && document.documentElement.scrollTop){
		
			scrollTopObj.value = document.documentElement.scrollTop;
			scrollLeftObj.value = document.documentElement.scrollLeft;
			
	}
	else if (document.body){
		//alert("inside set")
		//alert("before=document.forms[0].scrollTop="+scrollTopObj.value);
		//alert("document.forms[0].scrollLeft="+scrollLeftObj.value);
		scrollTopObj.value = document.body.scrollTop;
		scrollLeftObj.value = document.body.scrollLeft;
		//alert("After= document.forms[0].scrollTop="+scrollTopObj.value);
		//alert("document.forms[0].scrollLeft="+scrollLeftObj.value);
		//alert("scrollTopObj.value="+scrollTopObj.value+", scrollLeftObj.value="+scrollLeftObj.value)
	}
	var scrollTable=document.getElementById('ddscrolltable');
	scrollTableLeftObj.value=scrollTable.scrollLeft;
	scrollTableTopObj.value=scrollTable.scrollTop;
	
}
// This function is to restore the scroll bar to its previous positions -- Amit
function scrollToPosition(scrollLeftVal,scrollTopVal,scrollTableLeftVal,scrollTableTopVal){	
	if (document.documentElement && document.documentElement.scrollTop){
			document.documentElement.scrollTop = scrollTopVal;
			document.documentElement.scrollLeft = scrollLeftVal;
			
	}
	else if (document.body){
		//alert("onload --scroll To Positon")
		//alert("scrollTopVal="+scrollTopVal)
		//alert("scrollLeftVal="+scrollLeftVal)
		document.body.scrollTop = scrollTopVal;
		document.body.scrollLeft = scrollLeftVal;
		//alert("document.body.scrollTop="+document.body.scrollTop)
		//alert("document.body.scrollLeft="+document.body.scrollLeft)
		//alert("scrollTopVal="+scrollTopVal+", scrollTopVal="+scrollLeftVal)
	}
	var scrollTable=document.getElementById('ddscrolltable');
	scrollTable.scrollLeft=scrollTableLeftVal;
	scrollTable.scrollTop=scrollTableTopVal;
}
// End UAT Defect


//Function to send tab information
function setTabInfo()
{
	document.forms[0].elements["selectedTabIndex"].value = getSelectedTabIndex();
	document.forms[0].elements["selectedTabName"].value = getselectedtab();
}

var table1;
function updateColors1(){	
	
	var rows = table1.dataTable.tBody.rows;	
	var l = rows.length;
	var count = 0;
	var excludeRows = 0;
	var selectedRowIndex = -1;
	for (var i=0; i<l; i++)
	{
		if (table1.isRowVisible(i)){

				if(rows[i].className == "selectrow") {
					selectedRowIndex = i;
					rows[i].className = "even"; 
				}

			
			if(rows[i].className =="even" || rows[i].className =="odd") {			
			if( ((count-excludeRows) % 2) == 0 ){
				removeClassName(rows[i],"odd");
				addClassName(rows[i],"even");
			}
			else{
				removeClassName(rows[i],"even");
				addClassName(rows[i],"odd");
			}
				
			}
			else{
				++excludeRows;
				
			
			}				
				count++;				
				
		}
	}


	for (var i=0; i<l; i++) {	
		/* Start: Showing 'All' in Currency dropdown */
		var sumFlagTemp = "";		
		sumFlagTemp = rows[i].cells[8].innerText;	
		/* End: Showing 'All' in Currency dropdown */
		
				if(sumFlagTemp == "P ")
				{
					rows[i].className ="accp"
				}

				if(sumFlagTemp == "N ")
				{
					rows[i].className ="accn"
				}
	}

	if (selectedRowIndex != -1) {
		
		rows[selectedRowIndex].className =  "selectrow";
	}
	
}




function onSelectTableRow(rowElement){
	
	var hiddenElement1 = rowElement.getElementsByTagName("input")[0];
	var hiddenElement2 = rowElement.getElementsByTagName("input")[1];
	
	sumFlag = rowElement.cells[8].innerText;
	loroToPredictedFlag = hiddenElement2.value;
	//alert("hiddenElement1==" + hiddenElement1.value + "===");
	//alert("hiddenElement2==" + hiddenElement2.value + "===");
	updateColors1();

	//document.forms[0].accountId.value = rowElement.cells[0].innerText;
	//document.forms[0].accountId.value = new String(rowElement.cells[0].innerText).trim();
	document.forms[0].accountId.value = getAccountId(rowElement);

	var count = getCountRowsSelected(rowElement);
	if (count == 1) {
		
		/* START: Code changed as par ING requirements to the Roles - Menu Access, 21-JUN-2007 */
		if(menuEntityCurrGrpAccess == "0") {
		/* END: Code changed as par ING requirements to the Roles - Menu Access, 21-JUN-2007 */
		document.getElementById("sumbutton").innerHTML = document.getElementById("sumenablebutton").innerHTML;	
		
		if(hiddenElement1.value == "L" || hiddenElement1.value == "C" ) {
			document.getElementById("movebutton").innerHTML = document.getElementById("moveenablebutton").innerHTML;
		} else {
			document.getElementById("movebutton").innerHTML = document.getElementById("movedisablebutton").innerHTML;
		}		
	  }else {		
		document.getElementById("sumbutton").innerHTML = document.getElementById("sumdisablebutton").innerHTML;
		document.getElementById("movebutton").innerHTML = document.getElementById("movedisablebutton").innerHTML;
	  }
		
	} else {
		document.getElementById("sumbutton").innerHTML = document.getElementById("sumdisablebutton").innerHTML;
		document.getElementById("movebutton").innerHTML = document.getElementById("movedisablebutton").innerHTML;
	}

		
}

var cal = new CalendarPopup("caldiv",false,"calFrame"); 
      cal.showNavigationDropdowns();
      cal.setCssPrefix("CAL");
      cal.offsetX = 20;
      cal.offsetY = 2;



function onDateKeyPress(obj,e){
	var event = (window.event|| e);
	if(event.keyCode == 9){
	// tab  
	   if(validateField(obj,'date',dateFormat)){
		   if(validateDateRange()){
				 submitForm('displayDetailsByDate');
		   }
	   }else{
		  return false;
	   }
	}if(event.keyCode == 13){  //enter
	  
	   if(validateField(obj,'date',dateFormat)){
		   if(validateDateRange()){
				submitForm('displayDetailsByDate');
		   }
	   }else{
		  return false;
	   }
	}
  
}

function onDateChange(obj){
if(validateDateRange()){
	 if(dateSelected == true)
	 {
	   if(validateField(obj,'date',dateFormat)){
		submitForm('displayDetailsByDate');
		dateSelected = false;
		return true;

	}
	else{
		  return false;
	   }
	 }
	}
}

