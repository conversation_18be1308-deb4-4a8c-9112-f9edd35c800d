/*!-----------------------------------------------------------
 * Copyright (c) Microsoft Corporation. All rights reserved.
 * Version: 0.18.1(d7a26172c5955d29d2a8cca4377b53b28925c766)
 * Released under the MIT license
 * https://github.com/Microsoft/vscode/blob/master/LICENSE.txt
 *-----------------------------------------------------------*/
define("vs/editor/editor.main.nls.de",{"vs/base/browser/ui/actionbar/actionbar":["{0} ({1})"],"vs/base/browser/ui/aria/aria":["{0} (erneut aufgetreten)","{0} ({1} mal aufgetretenen)"],"vs/base/browser/ui/findinput/findInput":["Eingabe"],"vs/base/browser/ui/findinput/findInputCheckboxes":["Groß-/Kleinschreibung beachten","Nur ganzes Wort suchen","Regulären Ausdruck verwenden"],"vs/base/browser/ui/findinput/replaceInput":["Eingabe","Groß-/Kleinschreibung beibehalten"],"vs/base/browser/ui/inputbox/inputBox":["Fehler: {0}","Warnung: {0}","Info: {0}"],"vs/base/browser/ui/keybindingLabel/keybindingLabel":["Ungebunden"],"vs/base/browser/ui/list/listWidget":["{0}. Verwenden Sie die Navigationstasten, um zu navigieren."],"vs/base/browser/ui/menu/menu":["{0} ({1})"],"vs/base/browser/ui/tree/abstractTree":["Löschen","Typfilter deaktivieren","Typfilter aktivieren","Keine Elemente gefunden","{0} von {1} Elementen stimmen überein"],
"vs/base/common/keybindingLabels":["STRG","UMSCHALTTASTE","ALT","Windows","STRG","UMSCHALTTASTE","ALT","Super","Steuern","UMSCHALTTASTE","ALT","Befehl","Steuern","UMSCHALTTASTE","ALT","Windows","Steuern","UMSCHALTTASTE","ALT","Super"],"vs/base/common/severity":["Fehler","Warnung","Info"],"vs/base/parts/quickopen/browser/quickOpenModel":["{0}, Auswahl","Auswahl"],"vs/base/parts/quickopen/browser/quickOpenWidget":["Schnellauswahl. Nehmen Sie eine Eingabe vor, um die Ergebnisse einzugrenzen.","Schnellauswahl","{0} Ergebnisse"],"vs/editor/browser/controller/coreCommands":["&&Alles auswählen","&&Rückgängig","&&Wiederholen"],"vs/editor/browser/widget/codeEditorWidget":["Die Anzahl der Cursors wurde auf {0} beschränkt."],"vs/editor/browser/widget/diffEditorWidget":["Kann die Dateien nicht vergleichen, da eine Datei zu groß ist."],
"vs/editor/browser/widget/diffReview":["Schließen","keine Zeilen","1 Zeile","{0} Zeilen","Unterschied von {0} zu {1}: Original: {2}, {3}, geändert: {4}, {5}","leer","Original {0}, geändert {1}: {2}","+ geändert {0}: {1}","- Original {0}: {1}","Zum nächsten Unterschied wechseln","Zum vorherigen Unterschied wechseln"],"vs/editor/browser/widget/inlineDiffMargin":["Gelöschte Zeilen kopieren","Gelöschte Zeile kopieren","Gelöschte Zeile kopieren ({0})","Diese Änderung rückgängig machen","Gelöschte Zeile kopieren ({0})"],
"vs/editor/common/config/commonEditorConfig":["Editor","Steuert die Schriftfamilie.","Steuert die Schriftbreite.","Steuert den Schriftgrad in Pixeln.","Steuert die Zeilenhöhe. Verwenden Sie 0, um die Zeilenhöhe aus der Schriftgröße zu berechnen.","Steuert den Zeichenabstand in Pixeln.","Zeilennummern werden nicht dargestellt.","Zeilennummern werden als absolute Zahl dargestellt.","Zeilennummern werden als Abstand in Zeilen an Cursorposition dargestellt.","Zeilennummern werden alle 10 Zeilen dargestellt.","Steuert die Anzeige von Zeilennummern.",'Steuert die Mindestanzahl sichtbarer vorangehender und nachfolgender Zeilen, die den Cursor umgeben. Wird in anderen Editoren als "scrollOff" oder "scrollOffset" bezeichnet.',"Letzte Zeilennummer rendern, wenn die Datei mit einem Zeilenumbruch endet.","Vertikale Linien nach einer bestimmten Anzahl von Monospacezeichen rendern. Verwenden Sie mehrere Werte für mehrere Linien. Wenn das Array leer ist, werden keine Linien gerendert.","Zeichen, die als Worttrennzeichen verwendet werden, wenn wortbezogene Navigationen oder Vorgänge ausgeführt werden.",'Die Anzahl der Leerzeichen, denen ein Tabstopp entspricht. Diese Einstellung wird basierend auf dem Inhalt der Datei überschrieben, wenn "#editor.detectIndentation#" aktiviert ist.','Fügt beim Drücken der TAB-Taste Leerzeichen ein. Diese Einstellung wird basierend auf dem Inhalt der Datei überschrieben, wenn "#editor.detectIndentation#" aktiviert ist.','Steuert, ob "#editor.tabSize#" und "#editor.insertSpaces#" automatisch erkannt werden, wenn eine Datei basierend auf dem Dateiinhalt geöffnet wird.',"Steuert, ob eine Auswahl abgerundete Ecken aufweisen soll.","Steuert, ob der Editor jenseits der letzten Zeile scrollen wird.","Steuert die Anzahl der zusätzlichen Zeichen, nach denen der Editor horizontal scrollt.","Legt fest, ob der Editor Bildläufe animiert ausführt.","Steuert, ob die Minimap angezeigt wird.","Steuert die Seite, wo die Minimap gerendert wird.","Steuert, ob der Minimap-Schieberegler automatisch ausgeblendet wird.","Die tatsächlichen Zeichen in einer Zeile rendern im Gegensatz zu Farbblöcken.","Begrenzen Sie die Breite der Minimap, um nur eine bestimmte Anzahl von Spalten zu rendern.","Steuert, ob die Hovermarkierung angezeigt wird.","Steuert die Verzögerung in Millisekunden, nach der die Hovermarkierung angezeigt wird.","Steuert, ob die Hovermarkierung sichtbar bleiben soll, wenn der Mauszeiger darüber bewegt wird.",'Steuert, ob für die Suchzeichenfolge im Widget "Suche" ein Seeding aus der Auswahl des Editors ausgeführt wird.',"Steuert, ob der Suchvorgang für den ausgewählten Text oder die gesamte Datei im Editor ausgeführt wird.",'Steuert, ob das Widget "Suche" die freigegebene Suchzwischenablage unter macOS lesen oder bearbeiten soll.','Steuert, ob das Suchwidget zusätzliche Zeilen im oberen Bereich des Editors hinzufügen soll. Wenn die Option auf "true" festgelegt ist, können Sie über die erste Zeile hinaus scrollen, wenn das Suchwidget angezeigt wird.',"Zeilenumbrüche erfolgen nie.","Der Zeilenumbruch erfolgt an der Breite des Anzeigebereichs.",'Der Zeilenumbruch erfolgt bei "#editor.wordWrapColumn#".','Der Zeilenumbruch erfolgt beim Mindestanzeigebereich und "#editor.wordWrapColumn".',"Steuert, wie der Zeilenumbruch durchgeführt werden soll.",'Steuert die umschließende Spalte des Editors, wenn "#editor.wordWrap#" den Wert "wordWrapColumn" oder "bounded" aufweist.',"Kein Einzug. Umbrochene Zeilen beginnen bei Spalte 1.","Umbrochene Zeilen erhalten den gleichen Einzug wie das übergeordnete Element.","Umbrochene Zeilen erhalten + 1 Einzug auf das übergeordnete Element.","Umgebrochene Zeilen werden im Vergleich zum übergeordneten Element +2 eingerückt.","Steuert die Einrückung der umbrochenen Zeilen.",'Ein Multiplikator, der für die Mausrad-Bildlaufereignisse "deltaX" und "deltaY" verwendet werden soll.',"Multiplikator für Scrollgeschwindigkeit bei Drücken von ALT.","Ist unter Windows und Linux der STRG-Taste und unter macOSX der Befehlstaste zugeordnet.","Ist unter Windows und Linux der ALT-Taste und unter macOSX der Wahltaste zugeordnet.",'Der Modifizierer, der zum Hinzufügen mehrerer Cursor mit der Maus verwendet wird. Die Mausbewegungen "Gehe zu Definition" und "Link öffnen" werden so angepasst, dass kein Konflikt mit dem Multi-Cursor-Modifizierer entsteht.  [Read more] (https://code.visualstudio.com/docs/editor/codebasics#_multicursor-modifier).',"Mehrere Cursor zusammenführen, wenn sie sich überlappen.","Schnellvorschläge innerhalb von Zeichenfolgen aktivieren.","Schnellvorschläge innerhalb von Kommentaren aktivieren.","Schnellvorschläge außerhalb von Zeichenfolgen und Kommentaren aktivieren.","Steuert, ob Vorschläge automatisch während der Eingabe angezeigt werden sollen.","Steuert die Verzögerung in Millisekunden nach der Schnellvorschläge angezeigt werden.","Aktiviert ein Pop-up, das Dokumentation und Typ eines Parameters anzeigt während Sie tippen.","Steuert, ob das Menü mit Parameterhinweisen zyklisch ist oder sich am Ende der Liste schließt.","Verwenden Sie Sprachkonfigurationen, um zu bestimmen, wann Klammern automatisch geschlossen werden sollen.","Schließe Klammern nur automatisch, wenn der Cursor sich links von einem Leerzeichen befindet.","Steuert, ob der Editor automatisch Klammern schließen soll, nachdem der Benutzer eine öffnende Klammer hinzugefügt hat.","Verwende die Sprachkonfiguration, um zu ermitteln, wann Anführungsstriche automatisch geschlossen werden.","Schließende Anführungszeichen nur dann automatisch ergänzen, wenn der Cursor sich links von einem Leerzeichen befindet.","Steuert, ob der Editor Anführungszeichen automatisch schließen soll, nachdem der Benutzer ein öffnendes Anführungszeichen hinzugefügt hat.","Hiermit werden schließende Anführungszeichen oder Klammern immer überschrieben.","Schließende Anführungszeichen oder Klammern werden nur überschrieben, wenn sie automatisch eingefügt wurden.","Hiermit werden schließende Anführungszeichen oder Klammern niemals überschrieben.","Steuert, ob der Editor schließende Anführungszeichen oder Klammern überschreiben soll.","Sprachkonfigurationen verwenden, um zu bestimmen, wann eine Auswahl automatisch umschlossen werden soll.","Mit Klammern, nicht mit Anführungszeichen umschließen.","Mit Anführungszeichen, nicht mit Klammern umschließen.","Steuert, ob der Editor eine Auswahl automatisch umschließen soll.","Steuert, ob der Editor die Zeile nach der Eingabe automatisch formatieren soll.","Steuert, ob der Editor den eingefügten Inhalt automatisch formatieren soll. Es muss ein Formatierer vorhanden sein, der in der Lage ist, auch Dokumentbereiche zu formatieren.","Steuert, ob der Editor den Einzug automatisch anpassen soll, wenn Benutzer Zeilen eingeben, einfügen oder verschieben. Es müssen Erweiterungen mit Einzugsregeln für die entsprechende Sprache verfügbar sein.","Steuert, ob Vorschläge automatisch angezeigt werden sollen, wenn Triggerzeichen eingegeben werden.","Einen Vorschlag nur mit der EINGABETASTE akzeptieren, wenn dieser eine Änderung am Text vornimmt.","Steuert, ob Vorschläge mit der EINGABETASTE (zusätzlich zur TAB-Taste) akzeptiert werden sollen. Vermeidet Mehrdeutigkeit zwischen dem Einfügen neuer Zeilen oder dem Annehmen von Vorschlägen.",'Steuert, ob Vorschläge über Commitzeichen angenommen werden sollen. In JavaScript kann ein Semikolon (";") beispielsweise ein Commitzeichen sein, das einen Vorschlag annimmt und dieses Zeichen eingibt.',"Zeige Snippet Vorschläge über den anderen Vorschlägen.","Snippet Vorschläge unter anderen Vorschlägen anzeigen.","Zeige Snippet Vorschläge mit anderen Vorschlägen.","Keine Ausschnittvorschläge anzeigen.","Steuert, ob Codeausschnitte mit anderen Vorschlägen angezeigt und wie diese sortiert werden.","Steuert, ob ein Kopiervorgang ohne Auswahl die aktuelle Zeile kopiert.","Steuert, ob Syntax-Highlighting in die Zwischenablage kopiert wird.","Steuert, ob Vervollständigungen auf Grundlage der Wörter im Dokument berechnet werden sollen.","Immer den ersten Vorschlag auswählen.",'Wählen Sie die aktuellsten Vorschläge aus, es sei denn, es wird ein Vorschlag durch eine weitere Eingabe ausgewählt, z.B. "console.| -> console.log", weil "log" vor Kurzem abgeschlossen wurde.','Wählen Sie Vorschläge basierend auf früheren Präfixen aus, die diese Vorschläge abgeschlossen haben, z.B. "co -> console" und "con ->" const".',"Steuert, wie Vorschläge bei Anzeige der Vorschlagsliste vorab ausgewählt werden.",'Schriftgröße für das vorgeschlagene Widget. Bei Festlegung auf 0 wird der Wert von "#editor.fontSize#" verwendet.','Zeilenhöhe für das vorgeschlagene Widget. Bei Festlegung auf 0 wird der Wert von "#editor.lineHeight#" verwendet.',"Die Tab-Vervollständigung fügt den passendsten Vorschlag ein, wenn auf Tab gedrückt wird.","Tab-Vervollständigungen deaktivieren.",'Codeausschnitte per Tab vervollständigen, wenn die Präfixe übereinstimmen. Funktioniert am besten, wenn "quickSuggestions" deaktiviert sind.',"Tab-Vervollständigungen aktivieren.","Steuert, ob Filter- und Suchvorschläge geringfügige Tippfehler berücksichtigen.","Steuert, ob bei der Suche Wörter eine höhere Trefferquote erhalten, die in der Nähe des Cursors stehen.",'Steuert, ob gespeicherte Vorschlagauswahlen in verschiedenen Arbeitsbereichen und Fenstern gemeinsam verwendet werden (dafür ist "#editor.suggestSelection#" erforderlich).',"Steuert, ob ein aktiver Ausschnitt Schnellvorschläge verhindert.","Steuert, ob Symbole in Vorschlägen ein- oder ausgeblendet werden.","Steuert, wie viele Vorschläge IntelliSense anzeigt, bevor eine Scrollleiste eingeblendet wird (maximal 15).","Steuert, ob einige Vorschlagstypen von IntelliSense gefiltert werden. Eine Liste mit Vorschlagstypen finden Sie hier: https://code.visualstudio.com/docs/editor/intellisense#_types-of-completions.",'Wenn auf "false" eingestellt, nie "Methode"-Vorschläge in IntelliSense anzeigen.','Bei der Einstellung "false" zeigt IntelliSense für "function" keine Vorschläge an.','Bei der Einstellung "false" zeigt IntelliSense für "constructor" keine Vorschläge an.','Bei der Einstellung "false" zeigt IntelliSense für "field" keine Vorschläge an.','Bei der Einstellung "false" zeigt IntelliSense für "variable" keine Vorschläge an.','Bei der Einstellung "false" zeigt IntelliSense für "class" keine Vorschläge an.','Bei der Einstellung "false" zeigt IntelliSense für "struct" keine Vorschläge an.','Bei der Einstellung "false" zeigt IntelliSense für "interface" keine Vorschläge an.','Bei der Einstellung "false" zeigt IntelliSense für "module" keine Vorschläge an.','Bei der Einstellung "false“ zeigt IntelliSense für "property" keine Vorschläge an.','Bei der Einstellung "false" zeigt IntelliSense für "event" keine Vorschläge an.','Bei der Einstellung "false" zeigt IntelliSense für "operator" keine Vorschläge an.','Bei der Einstellung "false" zeigt IntelliSense für "unit" keine Vorschläge an.','Bei der Einstellung "false" zeigt IntelliSense für "value" keine Vorschläge an.','Bei der Einstellung "false" zeigt IntelliSense für "constant" keine Vorschläge an.','Bei der Einstellung "false" zeigt IntelliSense für "enum" keine Vorschläge an.','Bei der Einstellung "false" zeigt IntelliSense für "enumMember" keine Vorschläge an.','Bei der Einstellung "false" zeigt IntelliSense für "keyword" keine Vorschläge an.','Bei der Einstellung "false" zeigt IntelliSense für "text" keine Vorschläge an.','Bei der Einstellung "false" zeigt IntelliSense für "color" keine Vorschläge an.','Bei der Einstellung "false" zeigt IntelliSense für "file" keine Vorschläge an.','Bei der Einstellung "false" zeigt IntelliSense für "reference" keine Vorschläge an.','Bei der Einstellung "false" zeigt IntelliSense für "customcolor" keine Ergebnisse an.','Bei der Einstellung "false" zeigt IntelliSense für "folder" keine Vorschläge an.','Bei der Einstellung "false“ zeigt IntelliSense für "typeParameter" keine Vorschläge an.','Bei der Einstellung "false" zeigt IntelliSense für "snippet" keine Vorschläge an.','Steuert das Verhalten von "Gehe zu"-Befehlen (z.B. "Zur Definition wechseln"), wenn mehrere Zielspeicherorte vorhanden sind.',"Vorschauansicht der Ergebnisse anzeigen (Standardeinstellung)","Zum Hauptergebnis gehen und Vorschauansicht anzeigen","Wechseln Sie zum primären Ergebnis, und aktivieren Sie die Navigation ohne Vorschau zu anderen Ergebnissen.","Steuert, ob der Editor Übereinstimmungen hervorheben soll, die der Auswahl ähneln.","Steuert, ob der Editor das Vorkommen semantischer Symbole hervorheben soll.","Steuert die Anzahl von Dekorationen, die an derselben Position im Übersichtslineal angezeigt werden.","Steuert, ob um das Übersichtslineal ein Rahmen gezeichnet werden soll.","Steuert den Cursoranimationsstil.","Schriftart des Editors vergrößern, wenn das Mausrad verwendet und die STRG-TASTE gedrückt wird.","Steuert, ob die weiche Cursoranimation aktiviert werden soll.","Steuert den Cursor-Stil.","Steuert die Breite des Cursors, wenn `#editor.cursorStyle#` auf `line` festgelegt ist.","Aktiviert/deaktiviert Schriftartligaturen.","Steuert, ob der Cursor im Übersichtslineal ausgeblendet werden soll.","Render whitespace characters except for single spaces between words.","Hiermit werden Leerraumzeichen nur für ausgewählten Text gerendert.","Steuert, wie der Editor Leerzeichen rendern soll.","Steuert, ob der Editor Steuerzeichen rendern soll.","Steuert, ob der Editor Einzugsführungslinien rendern soll.","Steuert, ob der Editor die aktive Einzugsführungslinie hevorheben soll.","Hebt den Bundsteg und die aktuelle Zeile hervor.","Steuert, wie der Editor die aktuelle Zeilenhervorhebung rendern soll.","Steuert, ob der Editor CodeLens anzeigt.","Steuert, ob Codefaltung im Editor aktiviert ist.",'Steuert die Strategie für die Berechnung von Faltungsbereichen. "auto" verwendet eine sprachspezifische Faltungsstrategie (falls verfügbar). "indentation" verwendet die einzugsbasierte Faltungsstrategie.',"Steuert, ob die Falt-Steuerelemente an der Leiste automatisch ausgeblendet werden.","Übereinstimmende Klammern hervorheben, wenn eine davon ausgewählt wird.","Steuert, ob der Editor den vertikalen Glyphenrand rendert. Der Glyphenrand wird hauptsächlich zum Debuggen verwendet.","Das Einfügen und Löschen von Leerzeichen erfolgt nach Tabstopps.","Nachfolgende automatisch eingefügte Leerzeichen entfernen","Peek-Editoren geöffnet lassen, auch wenn auf den Inhalt doppelgeklickt oder die ESC-TASTE gedrückt wird.","Steuert, ob der Editor das Verschieben einer Auswahl per Drag and Drop zulässt.","Der Editor verwendet Plattform-APIs, um zu erkennen, wenn eine Sprachausgabe angefügt wird.","Der Editor wird durchgehend für die Verwendung mit einer Sprachausgabe optimiert.","Der Editor wird nie für die Verwendung mit einer Sprachausgabe optimiert.","Steuert, ob der Editor in einem Modus ausgeführt werden soll, in dem er für die Sprachausgabe optimiert wird.","Steuert das Ausblenden von nicht verwendetem Code.","Steuert, ob der Editor Links erkennen und anklickbar machen soll.","Steuert, ob der Editor die Inline-Farbdecorators und die Farbauswahl rendern soll.","Aktiviert das Glühbirnensymbol für Codeaktionen im Editor.","Zeilen, die diese Länge überschreiten, werden aus Leistungsgründen nicht tokenisiert",'Steuert, ob die Aktion "Importe organisieren" beim Speichern einer Datei ausgeführt werden soll.',"Legt fest, ob beim Speichern einer Datei automatische Korrekturen vorgenommen werden sollen.","Arten von Codeaktionen, die beim Speichern ausgeführt werden sollen.","Timeout in Millisekunden, nach dem Codeaktionen, die beim Speichern ausgeführt werden, abgebrochen werden.","Steuert, ob die primäre Linux-Zwischenablage unterstützt werden soll.","Steuert, ob der Diff-Editor die Unterschiede nebeneinander oder im Text anzeigt.","Steuert, ob der Diff-Editor Änderungen an führenden oder nachgestellten Leerzeichen als Diffs anzeigt.","Spezielle Behandlung für große Dateien zum Deaktivieren bestimmter speicherintensiver Funktionen.",'Steuert, ob der Diff-Editor die Indikatoren "+" und "-" für hinzugefügte/entfernte Änderungen anzeigt.'],
"vs/editor/common/config/editorOptions":["Der Editor ist zurzeit nicht verfügbar. Drücken Sie Alt+F1 für Optionen.","Editor-Inhalt"],"vs/editor/common/modes/modesRegistry":["Nur-Text"],
"vs/editor/common/standaloneStrings":["Keine Auswahl","Zeile {0}, Spalte {1} ({2} ausgewählt)","Zeile {0}, Spalte {1}","{0} Auswahlen ({1} Zeichen ausgewählt)","{0} Auswahlen",'Die Einstellung "accessibilitySupport" wird jetzt in "on" geändert.',"Die Dokumentationsseite zur Barrierefreiheit des Editors wird geöffnet.","in einem schreibgeschützten Bereich eines Diff-Editors.","in einem Bereich eines Diff-Editors.","in einem schreibgeschützten Code-Editor","in einem Code-Editor","Drücken Sie BEFEHLSTASTE + E, um den Editor für eine optimierte Verwendung mit Sprachausgabe zu konfigurieren.","Drücken Sie STRG + E, um den Editor für eine optimierte Verwendung mit Sprachausgabe zu konfigurieren.","Der Editor ist auf eine optimale Verwendung mit Sprachausgabe konfiguriert.","Der Editor ist so konfiguriert, dass er nie auf die Verwendung mit Sprachausgabe hin optimiert wird. Dies ist zu diesem Zeitpunkt nicht der Fall.","Durch Drücken der TAB-TASTE im aktuellen Editor wird der Fokus in das nächste Element verschoben, das den Fokus erhalten kann. Schalten Sie dieses Verhalten um, indem Sie {0} drücken.","Durch Drücken der TAB-TASTE im aktuellen Editor wird der Fokus in das nächste Element verschoben, das den Fokus erhalten kann. Der {0}-Befehl kann zurzeit nicht durch eine Tastenzuordnung ausgelöst werden.","Durch Drücken der TAB-TASTE im aktuellen Editor wird das Tabstoppzeichen eingefügt. Schalten Sie dieses Verhalten um, indem Sie {0} drücken.","Durch Drücken der TAB-TASTE im aktuellen Editor wird das Tabstoppzeichen eingefügt. Der {0}-Befehl kann zurzeit nicht durch eine Tastenzuordnung ausgelöst werden.","Drücken Sie BEFEHLSTASTE + H, um ein Browserfenster mit weiteren Informationen zur Barrierefreiheit des Editors zu öffnen.","Drücken Sie STRG + H, um ein Browserfenster mit weiteren Informationen zur Barrierefreiheit des Editors zu öffnen.","Sie können diese QuickInfo schließen und durch Drücken von ESC oder UMSCHALT+ESC zum Editor zurückkehren.","Hilfe zur Barrierefreiheit anzeigen","Entwickler: Token überprüfen","Zu Zeile {0} und Zeichen {1} wechseln","Gehe zu Zeile {0}","Zeilennummer zwischen 1 und {0} eingeben, zu der navigiert werden soll","Ein Zeichen zwischen 1 und {0} eingeben, um dorthin zu navigieren","Aktuelle Zeile: {0}. gehe zu Zeile {1}.","Eine Zeilennummer eingeben, gefolgt von einem optionalen Doppelpunkt und einer Zeichennummer, um dorthin zu navigieren","Gehe zu Zeile...","{0}, {1}, Befehle","{0}, Befehle","Name der auszuführenden Aktion eingeben","Befehlspalette","{0}, Symbole","Geben Sie den Namen eines Bezeichners ein, zu dem Sie navigieren möchten.","Gehe zu Symbol ...","Symbole ({0})","Module ({0})","Klassen ({0})","Schnittstellen ({0})","Methoden ({0})","Funktionen ({0})","Eigenschaften ({0})","Variablen ({0})","Variablen ({0})","Konstruktoren ({0})","Aufrufe ({0})","Editor-Inhalt","Drücken Sie STRG + F1, um die Barrierefreiheitsoptionen aufzurufen.","Drücken Sie ALT + F1, um die Barrierefreiheitsoptionen aufzurufen.","Zu Design mit hohem Kontrast umschalten","{0} Bearbeitungen in {1} Dateien durchgeführt"],
"vs/editor/common/view/editorColorRegistry":["Hintergrundfarbe zur Hervorhebung der Zeile an der Cursorposition.","Hintergrundfarbe für den Rahmen um die Zeile an der Cursorposition.","Hintergrundfarbe der markierten Bereiche, wie z.B. Quick Open oder die Suche. Die Farbe darf nicht deckend sein, weil sie sonst die zugrunde liegenden Dekorationen verdeckt.","Hintergrundfarbe für den Rahmen um hervorgehobene Bereiche.","Farbe des Cursors im Editor.","Hintergrundfarbe vom Editor-Cursor. Erlaubt die Anpassung der Farbe von einem Zeichen, welches von einem Block-Cursor überdeckt wird.","Farbe der Leerzeichen im Editor.","Farbe der Führungslinien für Einzüge im Editor.","Farbe der Führungslinien für Einzüge im aktiven Editor.","Zeilennummernfarbe im Editor.","Zeilennummernfarbe der aktiven Editorzeile.",'Die ID ist veraltet. Verwenden Sie stattdessen "editorLineNumber.activeForeground".',"Zeilennummernfarbe der aktiven Editorzeile.","Farbe des Editor-Lineals.","Vordergrundfarbe der CodeLens-Links im Editor","Hintergrundfarbe für zusammengehörige Klammern","Farbe für zusammengehörige Klammern","Farbe des Rahmens für das Übersicht-Lineal.","Hintergrundfarbe der Editorleiste. Die Leiste enthält die Glyphenränder und die Zeilennummern.","Rahmenfarbe unnötigen (nicht genutzten) Quellcodes im Editor.",'Deckkraft des unnötigen (nicht genutzten) Quellcodes im Editor. "#000000c0" rendert z.B. den Code mit einer Deckkraft von 75%. Verwenden Sie für Designs mit hohem Kontrast das Farbdesign "editorUnnecessaryCode.border", um unnötigen Code zu unterstreichen statt ihn abzublenden.',"Übersichtslineal-Markierungsfarbe für Fehler.","Übersichtslineal-Markierungsfarbe für Warnungen.","Übersichtslineal-Markierungsfarbe für Informationen."],
"vs/editor/contrib/bracketMatching/bracketMatching":["Übersichtslineal-Markierungsfarbe für zusammengehörige Klammern.","Gehe zu Klammer","Auswählen bis Klammer","Gehe zu &&Klammer"],"vs/editor/contrib/caretOperations/caretOperations":["Caretzeichen nach links verschieben","Caretzeichen nach rechts verschieben"],"vs/editor/contrib/caretOperations/transpose":["Buchstaben austauschen"],"vs/editor/contrib/clipboard/clipboard":["Ausschneiden","&&Ausschneiden","Kopieren","&&Kopieren","Einfügen","&&Einfügen","Mit Syntaxhervorhebung kopieren"],"vs/editor/contrib/codeAction/codeActionCommands":["Schnelle Problembehebung …","Keine Codeaktionen verfügbar","Keine Codeaktionen verfügbar","Refactoring durchführen...","Keine Refactorings verfügbar","Quellaktion…","Keine Quellaktionen verfügbar","Importe organisieren","Keine Aktion zum Organisieren von Importen verfügbar","Alle korrigieren",'Aktion "Alle korrigieren" nicht verfügbar',"Automatisch korrigieren...","Keine automatischen Korrekturen verfügbar"],
"vs/editor/contrib/codeAction/lightBulbWidget":["Korrekturen anzeigen ({0})","Korrekturen anzeigen"],"vs/editor/contrib/comment/comment":["Zeilenkommentar umschalten","Zeilenkommen&&tar umschalten","Zeilenkommentar hinzufügen","Zeilenkommentar entfernen","Blockkommentar umschalten","&&Blockkommentar umschalten"],"vs/editor/contrib/contextmenu/contextmenu":["Editor-Kontextmenü anzeigen"],"vs/editor/contrib/cursorUndo/cursorUndo":["Vorläufig rückgängig machen"],"vs/editor/contrib/find/findController":["Suchen","&&Suchen","Mit Auswahl suchen","Weitersuchen","Weitersuchen","Vorheriges Element suchen","Vorheriges Element suchen","Nächste Auswahl suchen","Vorherige Auswahl suchen","Ersetzen","&&Ersetzen"],
"vs/editor/contrib/find/findWidget":["Suchen","Suchen","Vorheriger Treffer","Nächste Übereinstimmung","In Auswahl suchen","Schließen","Ersetzen","Ersetzen","Ersetzen","Alle ersetzen","Ersetzen-Modus wechseln","Nur die ersten {0} Ergebnisse wurden hervorgehoben, aber alle Suchoperationen werden auf dem gesamten Text durchgeführt.","{0} von {1}","Keine Ergebnisse","{0} gefunden","{0} gefunden für {1}","{0} gefunden für {1} bei {2}","{0} gefunden für {1}",'STRG+EINGABE fügt jetzt einen Zeilenumbruch ein, statt alles zu ersetzen. Sie können die Tastenzuordnung für "editor.action.replaceAll" ändern, um dieses Verhalten außer Kraft zu setzen.'],"vs/editor/contrib/folding/folding":["Auffalten","Faltung rekursiv aufheben","Falten","Rekursiv falten","Alle Blockkommentare falten","Alle Regionen falten","Alle Regionen auffalten","Alle falten","Alle auffalten","Faltebene {0}"],
"vs/editor/contrib/fontZoom/fontZoom":["Editorschriftart vergrößern","Editorschriftart verkleinern","Editor Schriftart Vergrößerung zurücksetzen"],"vs/editor/contrib/format/format":["1 Formatierung in Zeile {0} vorgenommen","{0} Formatierungen in Zeile {1} vorgenommen","1 Formatierung zwischen Zeilen {0} und {1} vorgenommen","{0} Formatierungen zwischen Zeilen {1} und {2} vorgenommen"],"vs/editor/contrib/format/formatActions":["Dokument formatieren","Auswahl formatieren"],
"vs/editor/contrib/goToDefinition/goToDefinitionCommands":['Keine Definition gefunden für "{0}".',"Keine Definition gefunden"," – {0} Definitionen","Gehe zu Definition","Definition an der Seite öffnen","Peek-Definition",'Keine Deklaration für "{0}" gefunden.',"Keine Deklaration gefunden."," – {0} Deklarationen","Zur Deklaration wechseln",'Keine Deklaration für "{0}" gefunden.',"Keine Deklaration gefunden."," – {0} Deklarationen","Vorschau für Deklaration anzeigen",'Keine Implementierung gefunden für "{0}"',"Keine Implementierung gefunden","– {0} Implementierungen","Zur Implementierung wechseln","Vorschau der Implementierung anzeigen",'Keine Typendefinition gefunden für "{0}"',"Keine Typendefinition gefunden"," – {0} Typdefinitionen","Zur Typdefinition wechseln","Vorschau der Typdefinition anzeigen","Gehe &&zu Definition","Zur &&Typdefinition wechseln","Zur &&Implementierung wechseln"],"vs/editor/contrib/goToDefinition/goToDefinitionMouse":["Klicken Sie, um {0} Definitionen anzuzeigen."],
"vs/editor/contrib/goToDefinition/goToDefinitionResultsNavigation":["Symbol {0} von {1}, {2} für nächstes","Symbol {0} von {1}"],"vs/editor/contrib/gotoError/gotoError":["Gehe zu nächstem Problem (Fehler, Warnung, Information)","Gehe zu vorigem Problem (Fehler, Warnung, Information)","Gehe zu dem nächsten Problem in den Dateien (Fehler, Warnung, Info)","Gehe zu dem vorherigen Problem in den Dateien (Fehler, Warnung, Info)","Nächstes &&Problem","Vorheriges &&Problem"],"vs/editor/contrib/gotoError/gotoErrorWidget":["{0} von {1} Problemen","{0} von {1} Problemen","Editormarkierung: Farbe bei Fehler des Navigationswidgets.","Editormarkierung: Farbe bei Warnung des Navigationswidgets.","Editormarkierung: Farbe bei Warnung des Navigationswidgets.","Editormarkierung: Hintergrund des Navigationswidgets."],"vs/editor/contrib/hover/hover":["Hovern anzeigen"],
"vs/editor/contrib/hover/modesContentHover":["Wird geladen...","Vorschauproblem","Es wird nach Schnellkorrekturen gesucht...","Keine Schnellkorrekturen verfügbar","Schnelle Problembehebung …"],"vs/editor/contrib/inPlaceReplace/inPlaceReplace":["Durch vorherigen Wert ersetzen","Durch nächsten Wert ersetzen"],"vs/editor/contrib/linesOperations/linesOperations":["Zeile nach oben kopieren","Zeile nach oben &&kopieren","Zeile nach unten kopieren","Zeile nach unten ko&&pieren","Zeile nach oben verschieben","Zeile nach oben &&verschieben","Zeile nach unten verschieben","Zeile nach &&unten verschieben","Zeilen aufsteigend sortieren","Zeilen absteigend sortieren","Nachgestelltes Leerzeichen kürzen","Zeile löschen","Zeileneinzug","Zeile ausrücken","Zeile oben einfügen","Zeile unten einfügen","Alle übrigen löschen","Alle rechts löschen","Zeilen verknüpfen","Zeichen um den Cursor herum transponieren","In Großbuchstaben umwandeln","In Kleinbuchstaben umwandeln","In große Anfangsbuchstaben umwandeln"],
"vs/editor/contrib/links/links":["Befehl ausführen","Link folgen","BEFEHL + Klicken","STRG + Klicken","OPTION + Klicken","alt + klicken","Fehler beim Öffnen dieses Links, weil er nicht wohlgeformt ist: {0}","Fehler beim Öffnen dieses Links, weil das Ziel fehlt.","Link öffnen"],"vs/editor/contrib/message/messageController":["Ein Bearbeiten ist im schreibgeschützten Editor nicht möglich"],
"vs/editor/contrib/multicursor/multicursor":["Cursor oberhalb hinzufügen","Cursor oberh&&alb hinzufügen","Cursor unterhalb hinzufügen","Cursor unterhal&&b hinzufügen","Cursor an Zeilenenden hinzufügen","C&&ursor an Zeilenenden hinzufügen","Cursor am Ende hinzufügen","Cursor am Anfang hinzufügen","Auswahl zur nächsten Übereinstimmungssuche hinzufügen","&&Nächstes Vorkommen hinzufügen","Letzte Auswahl zu vorheriger Übereinstimmungssuche hinzufügen","Vo&&rheriges Vorkommen hinzufügen","Letzte Auswahl in nächste Übereinstimmungssuche verschieben","Letzte Auswahl in vorherige Übereinstimmungssuche verschieben","Alle Vorkommen auswählen und Übereinstimmung suchen","Alle V&&orkommen auswählen","Alle Vorkommen ändern"],"vs/editor/contrib/parameterHints/parameterHints":["Parameterhinweise auslösen"],"vs/editor/contrib/parameterHints/parameterHintsWidget":["{0}, Hinweis"],"vs/editor/contrib/referenceSearch/peekViewWidget":["Schließen"],
"vs/editor/contrib/referenceSearch/referenceSearch":[" – {0} Verweise","Vorschau für Verweise anzeigen"],"vs/editor/contrib/referenceSearch/referencesController":["Wird geladen..."],"vs/editor/contrib/referenceSearch/referencesModel":["Symbol in {0} in Zeile {1}, Spalte {2}","1 Symbol in {0}, vollständiger Pfad {1}","{0} Symbole in {1}, vollständiger Pfad {2}","Es wurden keine Ergebnisse gefunden.","1 Symbol in {0} gefunden","{0} Symbole in {1} gefunden","{0} Symbole in {1} Dateien gefunden"],"vs/editor/contrib/referenceSearch/referencesTree":["Fehler beim Auflösen der Datei.","{0} Verweise","{0} Verweis"],
"vs/editor/contrib/referenceSearch/referencesWidget":["Keine Vorschau verfügbar.","Verweise","Keine Ergebnisse","Verweise","Hintergrundfarbe des Titelbereichs der Peek-Ansicht.","Farbe des Titels in der Peek-Ansicht.","Farbe der Titelinformationen in der Peek-Ansicht.","Farbe der Peek-Ansichtsränder und des Pfeils.","Hintergrundfarbe der Ergebnisliste in der Peek-Ansicht.","Vordergrundfarbe für Zeilenknoten in der Ergebnisliste der Peek-Ansicht.","Vordergrundfarbe für Dateiknoten in der Ergebnisliste der Peek-Ansicht.","Hintergrundfarbe des ausgewählten Eintrags in der Ergebnisliste der Peek-Ansicht.","Vordergrundfarbe des ausgewählten Eintrags in der Ergebnisliste der Peek-Ansicht.","Hintergrundfarbe des Peek-Editors.","Hintergrundfarbe der Leiste im Peek-Editor.","Farbe für Übereinstimmungsmarkierungen in der Ergebnisliste der Peek-Ansicht.","Farbe für Übereinstimmungsmarkierungen im Peek-Editor.","Rahmen für Übereinstimmungsmarkierungen im Peek-Editor."],
"vs/editor/contrib/rename/rename":["Kein Ergebnis.","Ein unbekannter Fehler ist beim Auflösen der Umbenennung eines Ortes aufgetreten.",'"{0}" erfolgreich in "{1}" umbenannt. Zusammenfassung: {2}',"Fehler beim Ausführen der Umbenennung.","Symbol umbenennen"],"vs/editor/contrib/rename/renameInputField":["Benennen Sie die Eingabe um. Geben Sie einen neuen Namen ein, und drücken Sie die EINGABETASTE, um den Commit auszuführen."],"vs/editor/contrib/smartSelect/smartSelect":["Auswahl aufklappen","Auswahl &&erweitern","Markierung verkleinern","Au&&swahl verkleinern"],"vs/editor/contrib/snippet/snippetVariables":["Sonntag","Montag","Dienstag","Mittwoch","Donnerstag","Freitag","Samstag","So","Mo","Di","Mi","Do","Fr","Sa","Januar","Februar","März","April","Mai","Juni","Juli","August","September","Oktober","November","Dezember","Jan","Feb","Mär","Apr","Mai","Jun","Jul","Aug","Sep","Okt","Nov","Dez"],
"vs/editor/contrib/suggest/suggestController":['Das Akzeptieren von "{0}" ergab {1} zusätzliche Bearbeitungen.',"Vorschlag auslösen"],"vs/editor/contrib/suggest/suggestWidget":["Hintergrundfarbe des Vorschlagswidgets.","Rahmenfarbe des Vorschlagswidgets.","Vordergrundfarbe des Vorschlagswidgets.","Hintergrundfarbe des ausgewählten Eintrags im Vorschlagswidget.","Farbe der Trefferhervorhebung im Vorschlagswidget.","Mehr anzeigen...{0}","Weniger anzeigen...{0}","Wird geladen...","Wird geladen...","Keine Vorschläge.","Element {0}, Dok.: {1}"],"vs/editor/contrib/toggleTabFocusMode/toggleTabFocusMode":["TAB-Umschalttaste verschiebt Fokus","Beim Drücken auf Tab wird der Fokus jetzt auf das nächste fokussierbare Element verschoben","Beim Drücken von Tab wird jetzt das Tabulator-Zeichen eingefügt"],"vs/editor/contrib/tokenization/tokenization":["Entwickler: Force Retokenize"],
"vs/editor/contrib/wordHighlighter/wordHighlighter":["Hintergrundfarbe eines Symbols beim Lesezugriff, z.B. beim Lesen einer Variablen. Die Farbe darf nicht deckend sein, damit sie nicht die zugrunde liegenden Dekorationen verdeckt.","Hintergrundfarbe eines Symbols bei Schreibzugriff, z.B. beim Schreiben in eine Variable. Die Farbe darf nicht deckend sein, weil sie sonst die zugrunde liegenden Dekorationen verdeckt.","Randfarbe eines Symbols beim Lesezugriff, wie etwa beim Lesen einer Variablen.","Randfarbe eines Symbols beim Schreibzugriff, wie etwa beim Schreiben einer Variablen.","Übersichtslinealmarkerfarbd für das Hervorheben von Symbolen. Die Farbe darf nicht deckend sein, weil sie sonst die zugrunde liegenden Dekorationen verdeckt.","Übersichtslinealmarkerfarbe für Symbolhervorhebungen bei Schreibzugriff. Die Farbe darf nicht deckend sein, weil sie sonst die zugrunde liegenden Dekorationen verdeckt.","Gehe zur nächsten Symbolhervorhebungen","Gehe zur vorherigen Symbolhervorhebungen","Symbol-Hervorhebung ein-/ausschalten"],
"vs/platform/configuration/common/configurationRegistry":["Standard-Konfiguration überschreibt","Zu überschreibende Editor-Einstellungen für eine Sprache konfigurieren.",'"{0}" kann nicht registriert werden. Stimmt mit dem Eigenschaftsmuster "\\\\[.*\\\\]$" zum Beschreiben sprachspezifischer Editor-Einstellungen überein. Verwenden Sie den Beitrag "configurationDefaults".','{0}" kann nicht registriert werden. Diese Eigenschaft ist bereits registriert.'],"vs/platform/keybinding/common/abstractKeybindingService":["({0}) wurde gedrückt. Es wird auf die zweite Taste der Kombination gewartet...","Die Tastenkombination ({0}, {1}) ist kein Befehl."],
"vs/platform/list/browser/listService":["Workbench","Ist unter Windows und Linux der STRG-Taste und unter macOSX der Befehlstaste zugeordnet.","Ist unter Windows und Linux der ALT-Taste und unter macOSX der Wahltaste zugeordnet.",'Der Modifizierer zum Hinzufügen eines Elements in Bäumen und Listen zu einer Mehrfachauswahl mit der Maus (zum Beispiel im Explorer, in geöffneten Editoren und in der SCM-Ansicht). Die Mausbewegung "Seitlich öffnen" wird – sofern unterstützt – so angepasst, dass kein Konflikt mit dem Modifizierer für Mehrfachauswahl entsteht.',"Steuert, wie Elemente in Strukturen und Listen mithilfe der Maus geöffnet werden (sofern unterstützt). Bei übergeordneten Elementen, deren untergeordnete Elemente sich in Strukturen befinden, steuert diese Einstellung, ob ein Einfachklick oder ein Doppelklick das übergeordnete Elemente erweitert. Beachten Sie, dass einige Strukturen und Listen diese Einstellung ggf. ignorieren, wenn sie nicht zutrifft.","Legt fest, ob Listen und Strukturen horizontales Scrollen in der Workbench unterstützen.","Steuert, ob Bäume horizontales Scrollen in der Workbench unterstützen.",'Diese Einstellung ist veraltet. Verwenden Sie stattdessen "{0}".',"Steuert den Struktureinzug in Pixeln.","Steuert, ob die Struktur Einzugsführungslinien rendern soll.","Bei der einfachen Tastaturnavigation werden Elemente in den Fokus genommen, die mit der Tastatureingabe übereinstimmen. Die Übereinstimmungen gelten nur für Präfixe.","Hervorheben von Tastaturnavigationshervorgebungselemente, die mit der Tastatureingabe übereinstimmen. Beim nach oben und nach unten Navigieren werden nur die hervorgehobenen Elemente durchlaufen.","Durch das Filtern der Tastaturnavigation werden alle Elemente herausgefiltert und ausgeblendet, die nicht mit der Tastatureingabe übereinstimmen.",'Steuert die Tastaturnavigation in Listen und Strukturen in der Workbench. Kann "simple" (einfach), "highlight" (hervorheben) und "filter" (filtern) sein.','Legt fest, ob die Tastaturnavigation in Listen und Strukturen automatisch durch Eingaben ausgelöst wird. Wenn der Wert auf "false" festgelegt ist, wird die Tastaturnavigation nur ausgelöst, wenn der Befehl "list.toggleKeyboardNavigation" ausgeführt wird. Diesem Befehl können Sie eine Tastenkombination zuweisen.'],
"vs/platform/markers/common/markers":["Fehler","Warnung","Info"],
"vs/platform/theme/common/colorRegistry":["Allgemeine Vordergrundfarbe. Diese Farbe wird nur verwendet, wenn sie nicht durch eine Komponente überschrieben wird.","Allgemeine Vordergrundfarbe. Diese Farbe wird nur verwendet, wenn sie nicht durch eine Komponente überschrieben wird.","Allgemeine Rahmenfarbe für fokussierte Elemente. Diese Farbe wird nur verwendet, wenn sie nicht durch eine Komponente überschrieben wird.","Ein zusätzlicher Rahmen um Elemente, mit dem diese von anderen getrennt werden, um einen größeren Kontrast zu erreichen.","Ein zusätzlicher Rahmen um aktive Elemente, mit dem diese von anderen getrennt werden, um einen größeren Kontrast zu erreichen.","Vordergrundfarbe für Links im Text.","Hintergrundfarbe für Codeblöcke im Text.","Schattenfarbe von Widgets wie zum Beispiel Suchen/Ersetzen innerhalb des Editors.","Hintergrund für Eingabefeld.","Vordergrund für Eingabefeld.","Rahmen für Eingabefeld.","Rahmenfarbe für aktivierte Optionen in Eingabefeldern.","Hintergrundfarbe für aktivierte Optionen in Eingabefeldern.","Hintergrundfarbe bei der Eingabevalidierung für den Schweregrad der Information.","Vordergrundfarbe bei der Eingabevalidierung für den Schweregrad der Information.","Rahmenfarbe bei der Eingabevalidierung für den Schweregrad der Information.","Hintergrundfarbe bei der Eingabevalidierung für den Schweregrad der Warnung.","Vordergrundfarbe bei der Eingabevalidierung für den Schweregrad der Warnung.","Rahmenfarbe bei der Eingabevalidierung für den Schweregrad der Warnung.","Hintergrundfarbe bei der Eingabevalidierung für den Schweregrad des Fehlers.","Vordergrundfarbe bei der Eingabevalidierung für den Schweregrad des Fehlers.","Rahmenfarbe bei der Eingabevalidierung für den Schweregrad des Fehlers.","Hintergrund für Dropdown.","Vordergrund für Dropdown.","Hintergrundfarbe der Liste/Struktur für das fokussierte Element, wenn die Liste/Struktur aktiv ist. Eine aktive Liste/Struktur hat Tastaturfokus, eine inaktive hingegen nicht.","Vordergrundfarbe der Liste/Struktur für das fokussierte Element, wenn die Liste/Struktur aktiv ist. Eine aktive Liste/Struktur hat Tastaturfokus, eine inaktive hingegen nicht.","Hintergrundfarbe der Liste/Struktur für das ausgewählte Element, wenn die Liste/Struktur aktiv ist. Eine aktive Liste/Struktur hat Tastaturfokus, eine inaktive hingegen nicht.","Vordergrundfarbe der Liste/Struktur für das ausgewählte Element, wenn die Liste/Struktur aktiv ist. Eine aktive Liste/Struktur hat Tastaturfokus, eine inaktive hingegen nicht.","Hintergrundfarbe der Liste/Struktur für das ausgewählte Element, wenn die Liste/Struktur inaktiv ist. Eine aktive Liste/Struktur hat Tastaturfokus, eine inaktive hingegen nicht.","Vordergrundfarbe der Liste/Struktur für das ausgewählte Element, wenn die Liste/Baumstruktur inaktiv ist. Eine aktive Liste/Baumstruktur hat Tastaturfokus, eine inaktive hingegen nicht.","Hintergrundfarbe der Liste/Struktur für das fokussierte Element, wenn die Liste/Struktur aktiv ist. Eine aktive Liste/Struktur hat Tastaturfokus, eine inaktive hingegen nicht.","Hintergrund der Liste/Struktur, wenn mit der Maus auf Elemente gezeigt wird.","Vordergrund der Liste/Struktur, wenn mit der Maus auf Elemente gezeigt wird.","Drag & Drop-Hintergrund der Liste/Struktur, wenn Elemente mithilfe der Maus verschoben werden.","Vordergrundfarbe der Liste/Struktur zur Trefferhervorhebung beim Suchen innerhalb der Liste/Struktur.","Hintergrundfarbe des Typfilterwidgets in Listen und Strukturen.","Konturfarbe des Typfilterwidgets in Listen und Strukturen.","Konturfarbe des Typfilterwidgets in Listen und Strukturen, wenn es keine Übereinstimmungen gibt.","Strukturstrichfarbe für die Einzugsführungslinien.","Schnellauswahlfarbe für das Gruppieren von Bezeichnungen.","Schnellauswahlfarbe für das Gruppieren von Rahmen.","Hintergrundfarbe für Badge. Badges sind kurze Info-Texte, z.B. für Anzahl Suchergebnisse.","Vordergrundfarbe für Badge. Badges sind kurze Info-Texte, z.B. für Anzahl Suchergebnisse.","Schatten der Scrollleiste, um anzuzeigen, dass die Ansicht gescrollt wird.","Hintergrundfarbe vom Scrollbar-Schieber","Hintergrundfarbe des Schiebereglers, wenn darauf gezeigt wird.","Hintergrundfarbe des Schiebereglers, wenn darauf geklickt wird.","Hintergrundfarbe des Fortschrittbalkens, der für zeitintensive Vorgänge angezeigt werden kann.","Rahmenfarbe von Menüs.","Vordergrundfarbe von Menüelementen.","Hintergrundfarbe von Menüelementen.","Vordergrundfarbe des ausgewählten Menüelements im Menü.","Hintergrundfarbe des ausgewählten Menüelements im Menü.","Rahmenfarbe des ausgewählten Menüelements im Menü.","Farbe eines Trenner-Menüelements in Menüs.","Vordergrundfarbe von Fehlerunterstreichungen im Editor.","Randfarbe von Fehlerfeldern im Editor.","Vordergrundfarbe von Warnungsunterstreichungen im Editor.","Randfarbe der Warnfelder im Editor.","Vordergrundfarbe von Informationsunterstreichungen im Editor.","Randfarbe der Infofelder im Editor.","Vordergrundfarbe der Hinweisunterstreichungen im Editor.","Randfarbe der Hinweisfelder im Editor.","Hintergrundfarbe des Editors.","Standardvordergrundfarbe des Editors.","Hintergrundfarbe von Editor-Widgets wie zum Beispiel Suchen/Ersetzen.","Vordergrundfarbe für Editorwidgets wie Suchen/Ersetzen.","Rahmenfarbe von Editorwigdets. Die Farbe wird nur verwendet, wenn für das Widget ein Rahmen verwendet wird und die Farbe nicht von einem Widget überschrieben wird.","Rahmenfarbe der Größenanpassungsleiste von Editorwigdets. Die Farbe wird nur verwendet, wenn für das Widget ein Größenanpassungsrahmen verwendet wird und die Farbe nicht von einem Widget außer Kraft gesetzt wird.","Farbe der Editor-Auswahl.","Farbe des gewählten Text für einen hohen Kontrast","Die Farbe der Auswahl befindet sich in einem inaktiven Editor. Die Farbe darf nicht deckend sein, weil sie sonst die zugrunde liegende Dekorationen verdeckt.","Farbe für Bereiche mit dem gleichen Inhalt wie die Auswahl. Die Farbe darf nicht deckend sein, weil sie sonst die zugrunde liegenden Dekorationen verdeckt.","Randfarbe für Bereiche, deren Inhalt der Auswahl entspricht.","Farbe des aktuellen Suchergebnisses.","Farbe der anderen Suchergebnisse. Die Farbe darf nicht deckend sein, weil sie sonst die zugrunde liegenden Dekorationen verdeckt.","Farbe des Bereichs, der die Suche eingrenzt. Die Farbe darf nicht deckend sein, damit sie nicht die zugrunde liegenden Dekorationen verdeckt.","Randfarbe des aktuellen Suchergebnisses.","Randfarbe der anderen Suchtreffer.","Rahmenfarbe des Bereichs, der die Suche eingrenzt. Die Farbe darf nicht deckend sein, weil sie sonst die zugrunde liegenden Dekorationen verdeckt.","Hervorhebung unterhalb des Worts, für das ein Hoverelement angezeigt wird. Die Farbe darf nicht deckend sein, weil sie sonst die zugrunde liegenden Dekorationen verdeckt.","Background color of the editor hover.","Rahmenfarbe des Editor-Mauszeigers.","Hintergrundfarbe der Hoverstatusleiste des Editors.","Farbe der aktiven Links.","Hintergrundfarbe für eingefügten Text. Die Farbe darf nicht deckend sein, weil sie sonst die zugrunde liegenden Dekorationen verdeckt.","Hintergrundfarbe für Text, der entfernt wurde. Die Farbe darf nicht deckend sein, weil sie sonst die zugrunde liegenden Dekorationen verdeckt.","Konturfarbe für eingefügten Text.","Konturfarbe für entfernten Text.","Die Rahmenfarbe zwischen zwei Text-Editoren.","Hervorhebungs-Hintergrundfarbe eines Codeausschnitt-Tabstopps.","Hervorhebungs-Rahmenfarbe eines Codeausschnitt-Tabstopps.","Hervorhebungs-Hintergrundfarbe des letzten Tabstopps eines Codeausschnitts.","Hervorhebungs-Rahmenfarbe des letzten Tabstopps eines Codeausschnitts.","Übersichtslinealmarkerfarbe für das Suchen von Übereinstimmungen. Die Farbe darf nicht deckend sein, weil sie sonst die zugrunde liegenden Dekorationen verdeckt.","Übersichtslinealmarkerfarbe für das Hervorheben der Auswahl. Die Farbe darf nicht deckend sein, weil sie sonst die zugrunde liegenden Dekorationen verdeckt.","Minimap-Markerfarbe für gefundene Übereinstimmungen."]
});
//# sourceMappingURL=../../../min-maps/vs/editor/editor.main.nls.de.js.map