/* MOCHA UI */
html {
	height:100%;
	overflow:hidden;
}

body {
	margin: 0px; /* Required */
	height: 100%;
	overflow: hidden; 
	font-family: Arial; 
}

#mochaDesktop {
	position: relative;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	min-width: 750px; /* Helps keep header content from wrapping */		
	overflow: hidden;
}		

#mochaDesktopHeader {
	
}

#mochaDesktopTitlebar {
	padding: 5px 8px 7px 8px;
	background: #f0f0f0;
	border-top: 1px solid #fff;
	border-bottom: 1px solid #bbb;
	height: 25px;
}	
	
#mochaDesktopTitlebar h1 {
	margin: 0;
	padding: 4px 0 0 0;
	font-size: 18px;
	font-weight: bold;
	color: #e60;
}
	
#mochaDesktopTitlebar h1 .version {
	font-size: 12px;
	color: #333;	
}

.mochaTitleBarLeft {
	background-image: url(../images/headerTitleWindow_left_blue.png);
	background-repeat: no-repeat;
	height: 15px;
	position: absolute;
	width: 14px;
}

.mochaTitleBarCenter {
	background-color : #8dc4fb;
	height: 15px; /* also set this in scripts/mocha.js line 34 */
	left: 14px;
	position: absolute;
}

.mochaTitleBarRight {
	background-image: url(../images/headerTitleWindow_right_blue.png);
	background-repeat: no-repeat;
	height: 15px;
	position: absolute;
	right: 6px;
	width: 14px;
}

.mochaFooterBar {
	height: 15px;
}

.mochaFooterBarLeft {
	background-image: url(../images/footerTitleWindow_left.png);
	background-repeat: no-repeat;
	height: 15px;
	position: absolute;
	width: 14px;
}

.mochaFooterBarCenter {
	background-image: url(../images/footerTitleWindow_center.png);
	background-repeat: repeat-x;
	height: 15px; /* also set this in scripts/mocha.js line 34 */
	left: 14px;
	position: absolute;
}

.mochaFooterBarRight {
	background-image: url(../images/footerTitleWindow_right.png);
	background-repeat: no-repeat;
	height: 15px;
	position: absolute;
	right: 6px;
	width: 14px;
}

#mochaDesktopNavbar {
	padding: 0;
	background: #f0f0f0;
	border-top: 1px solid #fff;
	border-bottom: 1px solid #bbb;
	height: 22px;
}

#mochaDesktopNavbar ul {	
	padding: 0;
	margin: 0;
	list-style: none;
	font-size: 12px;
}

#mochaDesktopNavbar li {
	float: left;			
}

#mochaDesktopNavbar a {
	display: block;
}	
	
#mochaDesktopNavbar ul li a {
	padding: 2px 9px 2px 9px;	
	color: #000;
}

#mochaDesktopNavbar ul li a:hover {
	background: #e4e4e4;
}
	
#mochaDesktopNavbar li ul {
	padding: 2px;
	border: 1px solid #999;
	background: #fff;
	position: absolute;
	width: 148px;
	left: -999em;
	z-index: 8000;
}

#mochaDesktopNavbar li ul li {
} 

#mochaDesktopNavbar li ul li a {
	padding: 1px 9px 1px 9px;
	width: 130px;
}

#mochaDesktopNavbar li:hover ul, #mochaDesktopNavbar li.ieHover ul { /* lists nested under hovered list items */
	left: auto;
}

#mochaDesktopNavbar li:hover, #mochaDesktopNavbar li.hover {
    position: static;
}

.divider {
	margin-top: 2px;
	padding-top: 3px;	
	border-top: 1px solid #ddd;
}	

#mochaPageWrapper {
	clear: both;
	width: 100%;
	overflow: auto; /* This can be set to hidden if you want to treat it more like a desktop than a web page */
}

#mochaPage {
	padding: 20px 30px 30px 30px;
}

/* Windows */
div.mocha {
	position: absolute;
	top: 0;
	left: 0;
	display: none;
	overflow: hidden;	
}

div.mochaOverlay {
	left: 0px;
	position: absolute;
	top: 0px;
}

.mochaShadow {
	left: 0px;
	position: absolute;
	top: 0px;
	/*width: 100%;
	height: 100%;*/
	z-index: -1;
}

.mochaShadowImage {
	border: 0px none;
	position: absolute;
	top: 0px;
	left: 0px;
	display: none; /* using canvas */
}

.dropshadowtop {
	background-image:url(../images/dropshadow_topright.png);
	position:absolute;
	right:0px;
	top:0px;
}

.dropshadowleft, .dropshadowtop {
	background-position:center;
	background-repeat:no-repeat;
	border:1px none black;
	height:10px;
	line-height:10px;
	margin:0pt;
	padding:0pt;
	width:10px;
}

div.mocha .mochaTitlebar {
	width: 100%;
	overflow: hidden;
}

div.mocha .mochaTitlebar h3 {
	color: #ffffff;
	font-size: 10px;
	font-weight: bold;
	line-height: 15px;
	margin: 0px;
	padding-left: 10px;
	text-align: left;
	font-family	: verdana,helvetica;
	position: absolute;
	/*cursor:move;*/
}

div.mocha .mochaTitlebar a {
	color: #444;
}

div.mocha .mochaContentBorder {
	border-top: 1px solid #bdbdbd;	
	border-bottom: 1px solid #d7d7d7;
}
	
div.mocha .mochaContentWrapper { /* Has a fixed height and scrollbars if required */
	font-size : 9pt;
	overflow: auto;	
	font-family	: verdana,helvetica;
	color : #000000;
	background-color : #ccecff;
}
/*#7DB4EC*/
div.mocha .mochaContent {
	padding: 10px 12px;
	text-align: center;
}

.resizeHandle {
	position: absolute;
	bottom: 4px;
	right: 5px;
	width: 15px;
	height: 15px;
	cursor: se-resize;
	opacity: .0;
	/* filter: alpha(opacity=0); removed for ie - wont show cursor */
	-moz-opacity: 0;	
}


/* CUSTOMIZE BUTTONS HERE */
.mochaCanvas {
	display: block; /* set to none to use graphical skinning */
}

.mochaControls {
	position: absolute;
	right: 14px;
	top: 11px;
	width: 80px;
}

/* container for alert buttons */
.mochaAlertButtons {
	position: absolute;
	bottom:27px;
	/*right:20px;*/
	width : 100%;
	text-align : center;
	z-index:100;
	
	
}


/* onMouseOut="collapsebutton(this)" */
/* 					onMouseOver="highlightbutton(this)" */
/* 					onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" */

/* function expandbutton(aobject) { */
/*     aobject.className = "current" */
/* } */

/* function collapsebutton(aobject) { */
/*     aobject.className = "" */
/* } */

/* function highlightbutton(aobject) { */
/*     aobject.className = "hover" */
/* } */

.mochaAlertButton {
	text-decoration: none;
	font: 11px Verdana; /* tab font */
	color: black; /* font color */
	width: 70px; /* width of tab image */
	height: 21px; /* height of tab image */
	margin-left: 3px; /* spacing between tabs */
	margin-right: 3px;
	margin-top: 3px;
	padding-top: 3px; /* vertical offset of tab text from top of tab */
	background-image: url(../../images/skyButtonUp.png) !important;
	background-repeat: no-repeat;
	border: 2px;
	text-align: center;
}

.mochaAlertButtonHover{
	
	
	text-decoration: none;
	font: 11px Verdana; /* tab font */
	width: 70px; /* width of tab image */
	height: 21px; /* height of tab image */
	margin-left: 3px; /* spacing between tabs */
	margin-right: 3px;
	margin-top: 3px;
	padding-top: 3px; /* vertical offset of tab text from top of tab */
  	 background-image: url(../../images/skyButtonOver.png); /* URL to tab image */
	background-repeat: no-repeat;
	border: 2px;
	text-align: center;
   	color: darkblue; /* font color */
   cursor: hand;
   cursor: pointer;
}

.mochaAlertButtonCurrent{
	
	text-decoration: none;
	font: 11px Verdana; /* tab font */
	width: 70px; /* width of tab image */
	height: 21px; /* height of tab image */
	margin-left: 3px; /* spacing between tabs */
	margin-right: 3px;
	margin-top: 3px;
	padding-top: 3px; /* vertical offset of tab text from top of tab */
    background-image: url(../../images/skyButtonDown.png); /* URL to tab image */
	background-repeat: no-repeat;
	border: 2px;
	text-align: center;
   color: darkblue; /* font color */
  
   padding-left: 3px;
   padding-top: 3px; /* vertical offset of tab text from top of tab */
   cursor: hand;
   cursor: pointer;
}

.minimizeToggle, .maximizeToggle, .mochaClose {
	background-repeat:no-repeat;
	font-family: Verdana, Arial, Helvetica, sans-serif;
	float: right;
	width: 17px;
	height: 17px;
	font-size: 1px;
	cursor: pointer;	
}

/* MINIMIZE BUTTON */
.minimizeToggle {
	background-image: url(../images/icoMinTitleWindow.png);
}
.minimizeToggle:link {
	background-image: url(../images/icoMinTitleWindow.png);
}
.minimizeToggle:hover {
	background-image: url(../images/icoMinTitleWindow_ovr.png);
}

/* MAXIMIZE BUTTON */
.maximizeToggle {
	background-image: url(../images/icoMaxTitleWindow.png);
}
.maximizeToggle:link {
	background-image: url(../images/icoMaxTitleWindow.png);
}
.maximizeToggle:hover {
	background-image: url(../images/icoMaxTitleWindow_ovr.png);
}

/* CLOSE BUTTON */
.mochaClose {
	background-image: url(../images/icoCloseTitleWindow.png);
}
 .mochaClose:link {
	background-image: url(../images/icoCloseTitleWindow.png);
}
.mochaClose:hover {
	background-image: url(../images/icoCloseTitleWindow_ovr.png);
} 

.mochaLoadingIcon {
	position: absolute;
	bottom: 6px;
	left: 5px;
	width: 12px;
	height: 12px;
}

.mochaIframe {
	width: 100%;
	height: inherit;
}  
	
/* Slider */

#slider {
	position: relative;
	font-size: 12px;
	font-weight: bold;
	width: 200px;
	padding-top: 18px;	
}

#sliderarea {
	clear: both;
	position: relative;
	height: 10px;
	width: 200px;
	font-size: 1px;	
	background: #ddd;
	margin: 2px 0 10px 0;
}
 
#sliderknob {
	position: absolute;
	height: 10px;
	width: 20px;
	font-size: 1px;
	background: #e60;
	cursor: pointer;
}
	
#update {
	position: absolute;
	top: 0;
	left: 0;	
}	
	
.clear {
	clear: both;
}
	
/* Window Builder Form Elements */
	
#mochaDesktop form {
	margin: 0 0 0 0;
	padding: 5px 0 0 0;
	width: 320px;
}

#mochaDesktop textarea, #mochaDesktop input {
	color: #333;
	font-family: Arial, Helvetica, sans-serif;
	font-size: 12px;		
}
	
#mochaDesktop .input {
	background: #fbfbfb;
	width: 225px;
	padding: 1px 0 1px 3px;
	border: 1px solid #ccc;	
}

#mochaDesktop textarea {
	background: #fbfbfb;
	width: 225px;
	height: 100px;
	padding: 1px 0 1px 3px;
	border: 1px solid #ccc;	
}		

#mochaDesktop .formLabel {
	float: left;	
	text-align: right;
	width: 80px;
	margin: 0 0 5px 0;	
}
	
#mochaDesktop .formField {
	float: right;
	margin: 0 0 5px 0;
	padding: 0 0 0 0;
	width: 230px;
}
	
#mochaDesktop form .number {
	width: 40px;
}
	
/* Modal Windows */

#mochaModalOverlay {
	display: none;
	position: fixed; /* This is fixed rather than absolute to fix Mac FF2 issues */
	top: 0;
	left: 0;
	width: 100%;
	background: #000;
	opacity: 0;
	filter: alpha(opacity=0);
	-moz-opacity: 0;
	z-index: 10000;
}

* html 	#mochaModalOverlay {
	position: absolute;
}	
	
/* fix for IE6 select z-index issue */

iframe.zIndexFix {
	display: none;
	display: block;
	position: absolute;
	top: 0;
	left: 0;
	z-index: -1;
	filter: mask();
	width: 100px;
	height: 100px;
	border: 1px solid transparent;
}
	
/* Window Minimize Dock */

#mochaDock {
	display: none;
	position: relative;
	background: #fff;
	padding: 2px 15px;
	bottom: 0;
	left: 0;
	width: 100%;
	background-color: #f0f0f0;
	border-top: 1px solid #bbb;
	border-bottom: 1px solid #fff;
	min-height: 20px;
	height: auto;
   _height: 20px; /* IE 6.0 hack, for not supporting min-height SFF 12/07/2007*/
}
	
.mochaDockButton {
	width: 120px;
	height: 20px;
	font-family: Verdana, Arial, Helvetica, sans-serif;
    text-align: left;
	font-size: 10px;
	background-color: #f0f0f0;
	border-top: 1px solid #fff;
	border-right: 1px solid #777;
	border-bottom: 1px solid #777;
	border-left: 1px solid #fff;
	margin: 0 1px 0 0;			 
}

#mochaDockPlacement {
	position: absolute;
	top: 3px;
	left: 2px;
	width: 10px;
	height: 9px;
	opacity: 0;
	filter: alpha(opacity=0);
	-moz-opacity: 0;
	background: #f00; /* for troubleshooting */
	cursor: pointer;
	z-index: 3; /* for IE */	
}

#mochaDockAutoHide {
	position: absolute;
	top: 13px;
	left: 2px;
	width: 10px;
	height: 9px;
	opacity: 0;
	filter: alpha(opacity=0);
	-moz-opacity: 0;
	background: #f00; /* for troubleshooting */
	cursor: pointer;
	z-index: 3; /* for IE */		
}

/* Accordian */

.accordianToggler {
	margin: 0;
	padding: 4px 10px;
	background: #eee;
	border-bottom: 1px solid #ddd;
	border-top: 1px solid #f5f5f5;
	font-size: 11px;
	cursor: pointer;
}

.accordianContent {
	padding: 10px 10px 5px 10px;
}

.windowImagePreloader {
	position:absolute;
	top:-1000px;
	height:100px;
	width:100px;
	overflow:hidden;
}

/* Screens/Workspaces */

.screen {
	display: none;
}
