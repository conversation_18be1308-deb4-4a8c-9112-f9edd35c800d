<!-- saved from url=(0014)about:internet -->
<html lang="en">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<title>${title}</title>
<script src="AC_OETags.js" language="javascript"></script>
<style>
body { margin: 0px; overflow:hidden }
</style>
<script language="JavaScript" type="text/javascript">
<!--
// -----------------------------------------------------------------------------
// Globals
// Major version of Flash required
var requiredMajorVersion = ${version_major};
// Minor version of Flash required
var requiredMinorVersion = ${version_minor};
// Minor version of Flash required
var requiredRevision = ${version_revision};
// -----------------------------------------------------------------------------
// -->
</script>

<!--  BEGIN HTML Component required section -->
<!--  Be sure to add wmode = opaque or wmode = transparent to the swf embed code below -->
<script language="JavaScript" type="text/javascript" src="htmlcomponent.js"></script>
<!--  END HTML Component required section -->

<!--  BEGIN HTML Component optionals includes section -->
<!-- Read the documentation for each of these for their usage -->
<!--<script language="JavaScript" type="text/javascript" src="fckeditor/fckeditor.js"></script>-->
<!--<script language="JavaScript" type="text/javascript" src="tinymce/jscripts/tiny_mce/tiny_mce.js"></script>-->
<!--<script language="JavaScript" type="text/javascript" src="http://maps.google.com/maps?file=api&v=2.x&key=ABQIAAAAf1SuwLn4YbCOUSQ7hPKNwBQT4nbo22VCFxUQ51_kxEXNPVNhvBS_aGQPnWK8u0IpeBP9Tc1o1ebU_A"></script>-->
<!--  END HTML Component optionals includes section -->

</head>

<script language="JavaScript" type="text/javascript" src="htmlcomponent.js"></script>
<body scroll="no" id="myBody" onunload="GUnload()">

<script language="JavaScript" type="text/javascript" src="history.js"></script>
<script language="JavaScript" type="text/javascript">
<!--
// Version check for the Flash Player that has the ability to start Player Product Install (6.0r65)
var hasProductInstall = DetectFlashVer(6, 0, 65);

// Version check based upon the values defined in globals
var hasRequestedVersion = DetectFlashVer(requiredMajorVersion, requiredMinorVersion, requiredRevision);


// Check to see if a player with Flash Product Install is available and the version does not meet the requirements for playback
if ( hasProductInstall && !hasRequestedVersion ) {
	// MMdoctitle is the stored document.title value used by the installation process to close the window that started the process
	// This is necessary in order to close browser windows that are still utilizing the older version of the player after installation has completed
	// DO NOT MODIFY THE FOLLOWING FOUR LINES
	// Location visited after installation is complete if installation is required
	var MMPlayerType = (isIE == true) ? "ActiveX" : "PlugIn";
	var MMredirectURL = window.location;
    document.title = document.title.slice(0, 47) + " - Flash Player Installation";
    var MMdoctitle = document.title;

	AC_FL_RunContent(
		"src", "playerProductInstall",
		"FlashVars", "MMredirectURL="+MMredirectURL+'&MMplayerType='+MMPlayerType+'&MMdoctitle='+MMdoctitle+"",
		"width", "${width}",
		"height", "${height}",
		"align", "middle",
		"id", "${application}",
		"quality", "high",
		"bgcolor", "${bgcolor}",
		"name", "${application}",
		"allowScriptAccess","sameDomain",
		"type", "application/x-shockwave-flash",
		"pluginspage", "http://www.adobe.com/go/getflashplayer"
	);
} else if (hasRequestedVersion) {
	// if we've detected an acceptable version
	// embed the Flash Content SWF when all tests are passed
	AC_FL_RunContent(
			"src", "${swf}",
			"width", "${width}",
			"height", "${height}",
			"align", "middle",
			"id", "${application}",
			"quality", "high",
			"bgcolor", "${bgcolor}",
			"name", "${application}",
			"wmode", "opaque",
			"flashvars",'historyUrl=history.htm%3F&lconid=' + lc_id + '',
			"allowScriptAccess","sameDomain",
			"type", "application/x-shockwave-flash",
			"pluginspage", "http://www.adobe.com/go/getflashplayer"
	);
  } else {  // flash is too old or we can't detect the plugin
    var alternateContent = 'Alternate HTML content should be placed here. '
  	+ 'This content requires the Adobe Flash Player. '
   	+ '<a href=http://www.adobe.com/go/getflash/>Get Flash</a>';
    document.write(alternateContent);  // insert non-flash content
  }
// -->
</script>
<noscript>
  	<object classid="clsid:D27CDB6E-AE6D-11cf-96B8-444553540000"
			id="${application}" width="${width}" height="${height}"
			codebase="http://fpdownload.macromedia.com/get/flashplayer/current/swflash.cab">
			<param name="movie" value="${swf}.swf" />
			<param name="quality" value="high" />
			<param name="bgcolor" value="${bgcolor}" />
			<param name="allowScriptAccess" value="sameDomain" />
			<embed src="${swf}.swf" quality="high" bgcolor="${bgcolor}"
				width="${width}" height="${height}" name="${application}" align="middle"
				play="true"
				loop="false"
				quality="high"
				allowScriptAccess="sameDomain"
				type="application/x-shockwave-flash"
				pluginspage="http://www.adobe.com/go/getflashplayer">
			</embed>
	</object>
</noscript>
<iframe name="_history" src="history.htm" frameborder="0" scrolling="no" width="22" height="0"></iframe>

</body>
</html>
