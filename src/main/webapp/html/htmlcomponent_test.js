// Copyright 2008 www.flexcapacitor.com, www.drumbeatinsight.com 

fcElementIndex = 0;
fcYOffset = 0;

function fcHTMLTest(elementType) {
	o = fcGetDefault();
	fcYOffset += 30;
	o.y = fcYOffset;
	o.id = "html_" + elementType + fcElementIndex;
	o.type = elementType;
	
	if (elementType=="division") {
		o.htmlText = "<b>Element "+ o.id + "</b>";
		//o.height = "200";
		o.chrome = true;
		o.title = "This is...";
	}
	else if (elementType=="iframe") {
		o.source = "http://www.google.com";
		o.chrome = true;
	}
	else if (elementType=="editor") {
		o.editorPath = "../bin/fckeditor/";
		//o.configPath = "../../myconfig.js";
		o.width = 700;
		o.height = 400;
		o.chrome = true;
	}
	else if (elementType=="popup") {
		o.htmlText = "<b>Element "+ o.id + "</b>";
		o.source = "http://www.google.com";
		o.width = 400;
		o.height = 400;
		o.chrome = true;
	}
	else if (elementType=="popupIframe") {
		o.loadMethod = "iframe";
		o.source = "http://www.google.com";
		o.width = 400;
		o.height = 400;
		o.chrome = true;
	}
	else {
		alert("Element Type not defined in htmlcomponent_test.js = " + elementType);
	}	
	
	fcAddChild(o);
}

function fcGetDefault() {
	var o = new Object();
	o.frameborder = 0;
	
	// we set as string so we can pass percentages
	o.width = "200";
	o.height = "200";
	
	o.x = 10;
	o.y = 30;
	
	o.source = "about:blank";
	o.htmlText = "";
	o.type = "iframe";
	o.name = "";
	o.id = "html" + fcElementIndex++;
	o.backgroundColor = "transparent";
	o.border = "0px";
	o.htmlScrollPolicy = "auto";
	o.fontSize = 10;
	o.movieId = "HTMLInPopUp";
	o.position = "absolute";
	o.editorPath = "fckeditor/";
	o.configPath = "";
	o.includesBefore = [];
	o.includesAfter = [];
	o.javascript = "";
	o.editorType = "fckeditor";
	o.editorOptions = "simple";
	o.visible = true;
	o.chrome = false;
	o.title = "some title";
	
	return o;
}