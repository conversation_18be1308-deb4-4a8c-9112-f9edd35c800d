<jboss-deployment-structure name="swallowtech.war">
	<deployment>
		<!-- Exclusions allow you to prevent the server from automatically adding some dependencies -->
		<exclusions>
			<!-- java.lang.ClassCastException: org.slf4j.impl.Slf4jLoggerFactory cannot be cast to ch.qos.logback.classic.LoggerContext -->
			<module name="org.slf4j" />
			<module name="org.slf4j.impl" />
			<module name="org.xnio" />
			<module name="org.apache.commons.logging" />
			<module name="org.apache.log4j" />
			<!--
			<module name="org.jboss.logging" />
			<module name="org.jboss.logging.jul-to-slf4j-stub" />
			<module name="org.jboss.logmanager" />
			<module name="org.jboss.logmanager.log4j" />
			-->
		</exclusions>
		
		<!-- Inclusion of libraries from smartauth lib folder-->
		<resources>
		  <!-- Using the Bouncy Castle to support PBEWITHSHA256AND128BITAES-CBC-BC algorithm -->
		  <resource-root path="WEB-INF/lib/bcprov-ext-jdk15on-1.70.jar" use-physical-code-source="true"/>
		  <resource-root path="WEB-INF/lib/bcprov-jdk15on-1.70.jar" use-physical-code-source="true"/>
		  <resource-root path="WEB-INF/classes"/>
		  <resource-root path="WEB-INF/classes/org/swallow/util"/>
		</resources>
	</deployment>
</jboss-deployment-structure>