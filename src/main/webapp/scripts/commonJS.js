function call(){
closeall();
window.opener.regreshArray(window);
}

function MM_findObj(n, d) { //v4.01
  var p,i,x;  if(!d) d=document; if((p=n.indexOf("?"))>0&&parent.frames.length) {
    d=parent.frames[n.substring(p+1)].document; n=n.substring(0,p);}
  if(!(x=d[n])&&d.all) x=d.all[n]; for (i=0;!x&&i<d.forms.length;i++) x=d.forms[i][n];
  for(i=0;!x&&d.layers&&i<d.layers.length;i++) x=MM_findObj(n,d.layers[i].document);
  if(!x && d.getElementById) x=d.getElementById(n); return x;
}

function MM_showHideLayers() { //v6.0
  var i,p,v,obj,args=MM_showHideLayers.arguments;
  for (i=0; i<(args.length-2); i+=3) if ((obj=MM_findObj(args[i]))!=null) { v=args[i+2];
    if (obj.style) { obj=obj.style; v=(v=='show')?'visible':(v=='hide')?'hidden':v; }
    obj.visibility=v; }
}

function MM_preloadImages() { //v3.0
  var d=document; if(d.images){ if(!d.MM_p) d.MM_p=new Array();
    var i,j=d.MM_p.length,a=MM_preloadImages.arguments; for(i=0; i<a.length; i++)
    if (a[i].indexOf("#")!=0){ d.MM_p[j]=new Image; d.MM_p[j++].src=a[i];}}
}

function MM_swapImgRestore() { //v3.0
  var i,x,a=document.MM_sr; for(i=0;a&&i<a.length&&(x=a[i])&&x.oSrc;i++) x.src=x.oSrc;
}

function MM_swapImage() { //v3.0
  var i,j=0,x,a=MM_swapImage.arguments; document.MM_sr=new Array; for(i=0;i<(a.length-2);i+=3)
   if ((x=MM_findObj(a[i]))!=null){document.MM_sr[j++]=x; if(!x.oSrc) x.oSrc=x.src; x.src=a[i+2];}
}

var task = "None";
var windowsref = new Array(10);
var windowsname = new Array(10);


var indexwindow = 0;

function openWindow( url,windowname, attr)
{
var wndname = windowname + indexwindow;
windowsref[indexwindow] = window.open(url, wndname, attr);
windowsname[indexwindow++] = wndname;
// window.open('ManualInput.html','ManualInputWindow','left=50,top=190,width=700,height=315,toolbar=0, resizable=yes')
}

function getChilds()
{
	alert(indexwindow);
}

function setParentChildsFocus()
{
	var windArray  =  window.opener.windowsref;
	//alert(windArray.length);
	
	for( var index = 0; index < windArray.length; ++index)
	{
		if(windArray[index] && !windArray[index].closed)
		{
			//alert(windArray[index].name);
			windArray[index].focus();
			windArray[index].setChildsFocus();
		}
	}
	
				

}

function setChildsFocus()
{
	for( var index = 0; index < indexwindow; ++index)
	{
		if(!windowsref[index].closed)
		{
			(windowsref[index]).focus();
		}
	}

}


function closeall()
{
	//alert("Inside closeall");
	for( var index = 0; index < indexwindow; ++index)
	{
		if(!windowsref[index].closed)
		{
			//alert("Inside Left");
			//alert("Left - " + (windowsref[index]).screenLeft );	
			//alert("Left - " + windowsname[index]);	
			(windowsref[index]).close();
		}
	}
			
}



function regreshArray(windowIns)
{
	var position = findMyPosition(windowIns);
	//alert("My Position = " + position)  ; 
	if(position >= 0)
	{
		for( var index = position; index < indexwindow-1 ; ++index)
		{
			windowsref[index] = windowsref[index+1];
		}
		--indexwindow;
	}
}

function findMyPosition(windowIns){

	for( var index = 0; index < indexwindow; ++index)
	{
		if ( windowsref[index]  == windowIns )
		{
			return index;
		}
	}
	return -1;
}


function closemywindow(windowIns)
{
// For positioning
	alert("inside closemywindow " + windowIns);
	alert("indexwindow - " + indexwindow);
	
	
	for( var index = 0; index < indexwindow; ++index)
	{
		
		alert( windowsref[index]  == windowIns );
		if ( windowsref[index]  == windowIns )
		{
			 var winLeft = windowIns.screenLeft ? windowIns.screenLeft : windowIns.screenX;
			 alert("Left - " + winLeft );
		}
	}

}





