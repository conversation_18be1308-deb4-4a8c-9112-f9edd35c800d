package org.swallow;

import java.util.Arrays;

import org.springframework.beans.BeanUtils;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;
import org.springframework.util.ClassUtils;

/**
 * utilitaire des beans spring
 * <AUTHOR>
 */
@Component
public class BeanUtil implements ApplicationContextAware {

    @Autowired
    private static ApplicationContext context;

    public BeanUtil() {
		
	}
    
    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        context = applicationContext;
    }

    public static <T> T getBean(Class<T> beanClass) {
        return context.getBean(beanClass);
    }

    public static Object getBean(String beanName) {
        return context.getBean(beanName);
    }
    
    public static String[] listBeanNames() {
        String[] beans = context.getBeanDefinitionNames();
        Arrays.sort(beans);
        return beans;
    }

    /**
     * Retourner une instance de la class passee en argument
     * 	 
     *
     * @param className
     * @return
     */
    public static Object createClassInstance(String className) {
        try {
            Class<?> dataSourceClass = ClassUtils.forName(className, BeanUtil.class.getClassLoader());
            Object instance = BeanUtils.instantiateClass(dataSourceClass);
            return instance;
        } catch (Exception ex) {
            throw new IllegalStateException(
                    "Unable to create instance from '" + className + "'");
        }
    }

}
