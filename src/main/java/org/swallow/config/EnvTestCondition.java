package org.swallow.config;

import org.swallow.util.SwtUtil;
import org.springframework.context.annotation.Condition;
import org.springframework.context.annotation.ConditionContext;
import org.springframework.core.type.AnnotatedTypeMetadata;
/**
 * Test class for Env test enabled/disabled
 */
class EnvTestCondition {

    /**
     * For enabled test environment
     */
    public static class Enabled implements Condition {
        @Override
        public boolean matches(ConditionContext context, AnnotatedTypeMetadata metadata) {
            return SwtUtil.envTestEnabled();
        }
    }
    
    /**
     * For disabled test environment
     */
    public static class Disabled implements Condition {
        @Override
        public boolean matches(ConditionContext context, AnnotatedTypeMetadata metadata) {
            return !SwtUtil.envTestEnabled();
        }
    }
}
