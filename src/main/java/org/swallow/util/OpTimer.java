package org.swallow.util;

import java.util.Date;
import java.util.HashMap;

public class OpTimer {
	private HashMap<String, Long> opTimes = new HashMap<String, Long> ();
	private HashMap<String, Date> startTimes = new HashMap<String, Date> ();
	
	public boolean start (String name) {
		if (startTimes.containsKey (name))
			return false;
		startTimes.put (name, new Date ());
		return true;
	}
	
	public boolean stop (String name) {
		if (! startTimes.containsKey (name)) {
			return false;
		}
		
		Date start = startTimes.get (name);
		Date stop = new Date ();
		opTimes.put (name, stop.getTime()-start.getTime());
		startTimes.remove (name);
		
		return true;
	}
	
	public long getOpTime (String name) {
		if (opTimes.containsKey(name))
			return opTimes.get (name);
		
		return 0;
	}
	
	public HashMap<String, Long> getOpTimes () {
		return opTimes;
	}
}
