/**
 * @(#)InterfaceProcessor.java 1.0 
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.util;

import java.io.IOException;
import java.io.InputStream;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import org.apache.xerces.parsers.SAXParser;
import org.xml.sax.Attributes;
import org.xml.sax.InputSource;
import org.xml.sax.SAXException;
import org.xml.sax.helpers.DefaultHandler;

/**
 * InterfaceProcessor.java
 * 
 * This class parses given xml and gets interface monitor details
 * 
 * <AUTHOR> R / Jul 25, 2011
 */
public class InterfaceProcessor extends Defa<PERSON><PERSON><PERSON><PERSON> {

	// Interface tag name
	private final String INTERFACE = "interface";
	// Bean tag name
	private final String BEAN = "bean";
	// Attribute - Interface Id
	private final String ID = "id";
	// Engine tag name
	private final String ENGINE = "engine";
	// Attribute - Message status
	private final String MSG_STATUS = "active";
	// Attribute - Last message date
	private final String LAST_MSG_DATE = "lastmsg_date";
	// Attribute - Engine start date
	private final String ENGINE_START_DATE = "start_date";
	// Attribute - Engine current date
	private final String ENGINE_CURR_DATE = "curr_date";
	// Date format
	private final String DATE_FORMAT = "dd/MM/yyyy hh:mm:ss";

	// To hold list of Interface monitor details
	private List<Object[]> _lstInterfaceMonitor = null;
	// To hold detail of a interface
	private ArrayList<Object> _record = null;
	// To hold the engine current date
	private Date engineCurrDate = null;
	// To hold the engine start date
	private Date engineStartDate = null;
	// Attribute - Bean name
	private final String BEAN_NAME = "name";
	private boolean _isInterfaceActive;

	/**
	 * <pre>
	 * This method parses the given xml and gets interface monitor details.
	 * The output will be an arraylist inside an arraylist.
	 * The inner arraylist contains details of an interface
	 *  - Index 0: Interface Id
	 *  - Index 1: Message Status(Active Flag)
	 *  - Index 2: Last Message Date
	 * </pre>
	 * 
	 * @param source
	 *            Source xml file
	 * @return List<Object[]> - Interface monitor details
	 * @throws Exception
	 */
	public List<Object[]> process(InputStream source) throws Exception {
		// To parse the xml
		SAXParser parser = null;
		try {
			// Log debug message
			// logger.debug("Process request xml - Start");
			// Initialize list to store interface monitor details
			_lstInterfaceMonitor = new ArrayList<Object[]>();
			// Initialize SAX parser
			parser = new SAXParser();
			// Set content handler to parse the xml
			parser.setContentHandler(this);
			// Parse the xml file
			parser.parse(new InputSource(source));
			// Return interface monitor details
			return this._lstInterfaceMonitor;
		} finally {
			// close input stream
			try {
				source.close();
			} catch (Exception ex) {
				// Log error message
				// logger.error("Error while close xml stream", ex);
			}
			// nullify object(s)
			parser = null;
			// Log debug message
			// logger.debug("Process request xml - End");
		}
	}

	/**
	 * Override method, receive notification of the start of an element. This
	 * method initialize reads xml and gets inteface details
	 * 
	 * @param uri
	 *            The Namespace URI, or the empty string if the element has no
	 *            Namespace URI or if Namespace processing is not being
	 *            performed.
	 * @param localName
	 *            The local name (without prefix), or the empty string if
	 *            Namespace processing is not being performed.
	 * @param name
	 *            The qualified name (with prefix), or the empty string if
	 *            qualified names are not available.
	 * @param attributes
	 *            The attributes attached to the element. If there are no
	 *            attributes, it shall be an empty Attributes object.
	 * @throws SAXException
	 *             Any SAX exception, possibly wrapping another exception.
	 */
	public void startElement(String uri, String localName, String name,
			Attributes attributes) throws SAXException {
		try {
			if (localName.equals(INTERFACE)) {
				_record = new ArrayList<Object>();
				// Get Interface id from XML and set the same
				_record.add(attributes.getValue(ID));
				// Get Interface message status from XML and set the same
				_record.add(attributes.getValue(MSG_STATUS));
					_isInterfaceActive = attributes.getValue(MSG_STATUS)!=null?attributes.getValue(MSG_STATUS).equals("1"):false;
			} else if (localName.equals(BEAN)) {
				// Get last message date from XML
				String lastMessageDate = attributes.getValue(LAST_MSG_DATE);
				Date lastMsgDate = null;
				// Mantis 1748: updaterxx (xx is a random number) bean is always present and is the final stage of messages on SI6 EAI.
				// A message is considered to be successfully loaded only if it passes through updaterxx bean
				// By Saber Chebka, on 13-03-2012
				if (attributes.getValue(BEAN_NAME).startsWith("updater")
						&& _record.size() <3) {
					// Parse and set last message date
					SimpleDateFormat sdf = new SimpleDateFormat(DATE_FORMAT);
					// If lastMessageDate is not null, then adopt it
					if(lastMessageDate!=null&&lastMessageDate.length()>0)
						lastMsgDate = sdf.parse(lastMessageDate);
					
					// If no message has been received, then set the lastMessageDate as the startup date of the engine
					if(lastMsgDate==null&&engineStartDate!=null){
						_record.add(lastMsgDate); // Add null lastMsgDate
						lastMsgDate = engineStartDate;
					}else{
						// Set the last message date
						_record.add(lastMsgDate);
					}

					//Set the elapsed time, Note: The engineCurrDate is always provided !
					_record.add(engineCurrDate.getTime() - lastMsgDate.getTime());
				}
			} else if(localName.equals(ENGINE)){ // Added by Saber Chebka on 13-03-2012 for Mantis 1748
				// Get the engine infos
				// Parse and set last message date
				// Tip: On the usage XML returned by Smart Input engine, the <engine> tag figures before <interface> tag, 
				// so engineCurrDate and engineStartDate will be set at first (We are using the SAX parser)
				SimpleDateFormat sdf = new SimpleDateFormat(DATE_FORMAT);
				String startDateStr = attributes.getValue(ENGINE_START_DATE);
				String currDateStr = attributes.getValue(ENGINE_CURR_DATE);
				if(currDateStr != null
						&& currDateStr.trim().length() > 0)
					engineCurrDate = sdf.parse(currDateStr);
				if(startDateStr != null
						&& startDateStr.trim().length() > 0)
					engineStartDate = sdf.parse(startDateStr);
			}
		} catch (Exception ex) {
			// logger.error("Error in startElement", ex);
			throw new SAXException(ex);
		}
	}

	/**
	 * Override method, receive notification of the end of an element. This
	 * method adds interface monitor details to the list
	 * 
	 * @param uri
	 *            The Namespace URI, or the empty string if the element has no
	 *            Namespace URI or if Namespace processing is not being
	 *            performed.
	 * @param localName
	 *            The local name (without prefix), or the empty string if
	 *            Namespace processing is not being performed.
	 * @param name
	 *            The qualified name (with prefix), or the empty string if
	 *            qualified names are not available.
	 * @exception org.xml.sax.SAXException
	 *                Any SAX exception, possibly wrapping another exception.
	 */
	public void endElement(String uri, String localName, String name)
			throws SAXException {
		try {
			if (localName.equals(INTERFACE)) {
				// If last message date is empty then add null value
				if (_record.size() != 3) {
					_record.add(null);
				}
				// Add interface monitor details
				if (_isInterfaceActive){
				this._lstInterfaceMonitor.add(this._record.toArray());
				}
				// Nullify object
				this._record = null;
			} else {
				// Do nothing
			}
		} catch (Exception ex) {
			// Log error message
			// logger.error("Error in endElement", ex);
			throw new SAXException(ex);
		} finally {
			// do nothing;
		}
	}
	
	/**
	 * Returns a list of array of objects that contains the error message
	 * The returned list must have the structure of: {[null,"errorMessage text",null]}
	 * @param errorMessage
	 * @return
	 */
	public List<Object[]> processError(String errorMessage){
		_record = new ArrayList<Object>();
		_lstInterfaceMonitor = new ArrayList<Object[]>();
		
		// Set the first element as null string to inform about an exception
		_record.add(null);
		// Add error message
		_record.add(errorMessage);
		// Add the 3rd element
		_record.add(null);

		// Add interface monitor details
		this._lstInterfaceMonitor.add(this._record.toArray());
		// Nullify object
		this._record = null;
		// Return interface monitor details
		return this._lstInterfaceMonitor;
	}
}