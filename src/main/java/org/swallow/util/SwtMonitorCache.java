/**
 * @(#)SwtMonitorCache.java 1.0 / Aug 31, 2006
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.util;

import java.util.ArrayList;
import java.util.Collection;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.swallow.work.model.AccountMonitorCacheKey;
import org.swallow.work.model.AccountMonitorCacheValue;
import org.swallow.work.model.AccountMonitorTotalCacheKey;
import org.swallow.work.model.AccountMonitorTotalCacheValue;
import org.swallow.work.model.BookMonitorCurrencyBalanceTO;
import org.swallow.work.model.BookMonitorCurrencyDateTO;
import org.swallow.work.model.CurrencyDateTO;
import org.swallow.work.model.EntityBookCodeTO;
import org.swallow.work.model.PredictedBalanceTO;

import com.whirlycott.cache.Cache;
import com.whirlycott.cache.CacheException;

/**
 * This acts as Cache for Monitor Screens
 * 
 * <AUTHOR>
 */
public class SwtMonitorCache extends SwtCache {

	private Cache cacheObj = null;
	/** stores the instance of log object */
	private final Log log = LogFactory.getLog(SwtMonitorCache.class);

	public SwtMonitorCache() {
		super();
		try {
			cacheObj = cacheManager.getCache("monitor");
		} catch (CacheException ex) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [SwtMonitorCache] method : - "
					+ ex.getMessage());
		}
	}

	/**
	 * This function remove an object from the cache hold by cacheManager.
	 * 
	 * @param key
	 *            Key Object
	 * 
	 * @return Object Retuns the removed object.
	 */
	public Object remove(Object key) {
		return remove(cacheObj, key);
	}

	/**
	 * This function stores an object into the cache.
	 * 
	 * @param key
	 *            Key Object
	 * @param value
	 *            Value Object.
	 */
	public void store(Object key, Object value) {
		store(cacheObj, key, value);
	}

	/**
	 * This function finds the object into cache , if found returns it ,
	 * otherwise return null
	 * 
	 * @param key
	 *            Key Object
	 * 
	 * @return Object Value Object
	 */
	public Object isExistsInCache(Object key) {
		return isExistsInCache(cacheObj, key);
	}

	/**
	 * This function returns the collection of CurrencyBalanceTO objects by
	 * checking the collection in cache.
	 * 
	 * @param currencyDateObj
	 *            CurrencyDateTO Object
	 * 
	 * @return Collection of CurrencyBalanceTO Object
	 */
	public Collection getCurrenyBalances(CurrencyDateTO currencyDateObj) {
		log.debug("entering 'getCurrenyBalances' method");

		Collection coll = new ArrayList();

		if (currencyDateObj != null) {
			log.debug("Checking in session");

			Object value = isExistsInCache(currencyDateObj);

			if ((value == null) || !(value instanceof Collection)) {
				log.debug("CurrencyBalanceTO doesn't exists for "
						+ currencyDateObj);
			} else {
				log.debug("CurrencyBalanceTO exists for " + currencyDateObj);
				log.debug("Value in the cache is---->" + value);
				coll = (Collection) value;
			}
		}

		log.debug("exiting 'getCurrencies' method");

		return coll;
	}

	/**
	 * This function returns the Value Object corresponding to the Key object
	 * provided to it The key object is of the type AccountMonitorCacheKey.java
	 * 
	 * @param accountMonitorCacheKeyObj -
	 *            AccountMonitorCacheKey object
	 * @return - AccountMonitorCacheValue object
	 */
	public AccountMonitorCacheValue getAccountMonitorBalances(
			AccountMonitorCacheKey accountMonitorCacheKeyObj) {

		log.debug("Entering 'getAccountMonitorBalances' method");

		AccountMonitorCacheValue valueObj = new AccountMonitorCacheValue();

		if (accountMonitorCacheKeyObj != null) {
			log.debug("Checking in session");

			valueObj = (AccountMonitorCacheValue) isExistsInCache(accountMonitorCacheKeyObj);

			if ((valueObj == null)) {
				log.debug("accountMonitorCacheKeyObj doesn't exists for "
						+ accountMonitorCacheKeyObj);
			} else {
				log.debug("accountMonitorCacheKeyObj exists for "
						+ accountMonitorCacheKeyObj);
				log.debug("Value in the cache is---->" + valueObj);

			}
		}

		log.debug("Exiting 'getAccountMonitorBalances' method");

		return valueObj;
	}

	/**
	 * This function returns the Value Object corresponding to the Key object
	 * provided to it The key object is of the type
	 * AccountMonitorTotalCacheKey.java
	 * 
	 * @param totalCacheKeyObj -
	 *            AccountMonitorTotalCacheKey object
	 * @return - AccountMonitorTotalCacheValue object
	 */
	public AccountMonitorTotalCacheValue getAccountMonitorBalancesTotals(
			AccountMonitorTotalCacheKey totalCacheKeyObj) {

		log.debug("Entering 'getAccountMonitorBalancesTotals' method");

		// AccountMonitorCacheValue valueObj = new AccountMonitorCacheValue();
		AccountMonitorTotalCacheValue totalValueObj = new AccountMonitorTotalCacheValue();
		if (totalCacheKeyObj != null) {
			log.debug("Checking in session");

			totalValueObj = (AccountMonitorTotalCacheValue) isExistsInCache(totalCacheKeyObj);

			if ((totalValueObj == null)) {
				log.debug("accountMonitorTotalCacheKeyObj doesn't exists for "
						+ totalCacheKeyObj);
			} else {
				log.debug("accountMonitorTotalCacheKeyObj exists for "
						+ totalCacheKeyObj);
				log.debug("Value in the cache is---->" + totalValueObj);

			}
		}

		log.debug("Exiting 'getAccountMonitorBalancesTotals' method");

		return totalValueObj;
	}

	/**
	 * This function returns the collection of CurrencyBalanceTO objects by
	 * checking the collection in cache.
	 * 
	 * @param currencyDateObj
	 *            CurrencyDateTO Object
	 * 
	 * @return Collection of CurrencyBalanceTO Object
	 */
	public BookMonitorCurrencyBalanceTO getTotalValue(
			BookMonitorCurrencyDateTO currencyDateObj) {
		log.debug("entering 'getCurrenyBalances' method");

		BookMonitorCurrencyBalanceTO coll = null;

		if (currencyDateObj != null) {
			log.debug("Checking in session");

			Object value = isExistsInCache(currencyDateObj);

			if ((value == null)
					|| !(value instanceof BookMonitorCurrencyBalanceTO)) {
				log.debug("CurrencyBalanceTO doesn't exists for "
						+ currencyDateObj);
				return null;
			} else {
				log.debug("CurrencyBalanceTO exists for " + currencyDateObj);
				log.debug("Value in the cache is---->" + value);
				coll = (BookMonitorCurrencyBalanceTO) value;
			}
		}

		log.debug("exiting 'getCurrenyBalances' method");

		return coll;
	}

	/**
	 * This method returns the predicted balance records from the cache
	 * 
	 * @param entityBookCode
	 *            the key for which the value has to searched
	 * @return value in cache
	 */
	public Object getPredictedBalanceForBookCodeFromCache(
			EntityBookCodeTO entityBookCode) {
		log.debug("entering getPredictedBalanceForBookCode method");

		Object value = null;
		if (entityBookCode != null) {
			value = isExistsInCache(entityBookCode);
			if ((value == null) || !(value instanceof PredictedBalanceTO)) {
				log
						.debug("Exiting getPredictedBalanceForBookCode method. No value found in cache");
				return null;
			}
		}
		log
				.debug("Exiting getPredictedBalanceForBookCode method. Value found in cache");
		return value;
	}
}
