package org.swallow.util;

import java.awt.BasicStroke;

import net.sf.jasperreports.engine.JRAbstractChartCustomizer;
import net.sf.jasperreports.engine.JRChart;

import org.jfree.chart.JFreeChart;
import org.jfree.chart.plot.Plot;
import org.jfree.chart.plot.XYPlot;
import org.jfree.chart.renderer.xy.XYLineAndShapeRenderer;

/**
 * This class serves as charts customizer, it supports customization for all jfreechart elements used on jasper reports
 * 
 * When the plot is an instance of XYPlot, we customize the TimeSeries as follow:
 * Credit and Debit should have line stoke width equals to 1.5 whereas Balance has line stoke width equals to 1
 * Also Credit is Dashed and Debit is Dotted
 * 
 * <AUTHOR> SwallowTech Tunsia
 * @version 1.0
 */
public class SwtChartCustomizer extends JRAbstractChartCustomizer{
    public void customize(JFreeChart chart, JRChart jasperChart)
    {
        // Customise the plot
        Plot plot = chart.getPlot();
        if (plot instanceof XYPlot) {
        	XYPlot xyPlot = (XYPlot)plot;
        	XYLineAndShapeRenderer xyRenderer=(XYLineAndShapeRenderer)xyPlot.getRenderer();
        	// customise the renderers stroke...
            xyRenderer.setSeriesStroke(
                0, new BasicStroke(
                    1.5f, BasicStroke.CAP_ROUND, BasicStroke.JOIN_ROUND,
                    1.0f, new float[] {10.0f, 4.0f}, 0.0f
                )
            );
            xyRenderer.setSeriesStroke(
                1, new BasicStroke(
                    1.5f, BasicStroke.CAP_ROUND, BasicStroke.JOIN_ROUND,
                    1.0f, new float[] {3.0f, 3.0f}, 0.0f
                )
            );      
            // OPTIONAL CUSTOMISATION COMPLETED.
        }
    }
}
