package org.swallow.util;

import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;

import javax.sql.DataSource;


import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.hibernate.HibernateException;
import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;

/**
 * Returns a 'unique' message sequence composed as follows:
 *    tTTTTTTTTSSSSSS
 *    
 * where: - TTTTTTTT is the time in milliseconds converted to 8-digit Base-36
 *        - SSSSSS is a 6-digit Base-36 sequence (left padded with zeros)
 *        - t is an extra digit of the time element when Base-36 conversion exceeds 8 digits
 *        
 * Algorithm Summary:       
 *   1. Sequence of composed of 2 parts: time and sequence.       
 *   2. Get current time in milliseconds, and store its value.      
 *   3. Start sequence from 1 and increment up to <max sequence>.       
 *   4. When exceeded <max sequence>:       
 *        Pause then fetch the current time.       
 *        Recycle the sequence from 1.     
 *         
 * This algorithm means that up to <max sequence> sequences can be fetched in a one millisecond  
 * slot without a collision. Therefore, setting <max sequence> to a very large value is desirable       
 * as it decreases the chances of a non-unique sequence, especially at the end of daylight saving       
 * when the time is moved back one hour. By reducing the dependency on time, the chance of a       
 * collision is reduced.
 * 
 * This class should be thread safe and has been tested with 3 threads started at the same time       
 * generating 5000 sequences each with no collisions.       
 * 
 * For reference, some significant Base-10 to Base-36 conversions:
 * 
 *    Base-10          Base-36    Date
 *    -------------  ---------  ----------
 *                0          0  1970-01-01
 *        999999999     GJDGXR  1970-01-12               
 *       2176782335     ZZZZZZ  1970-01-26               
 *    1000000000000   CRE66I9S  2001-09-09
 *    2821109907455   ZZZZZZZZ  2059-05-25                  
 *    9999999999999  3JLXPT2PR  2286-11-20               
 * 
 * <AUTHOR> Luc
 * @version 1.0
 */

public abstract class SequenceFactory {
	private static final int RADIX = 36;
	private static long timeSnapshot = getCurrentTime();

	private static final long SEQUENCE_MIN = 1;
	private static final long SEQUENCE_MAX = 999999999;
	private static long sequenceCounter = SEQUENCE_MIN - 1;

	// The delay needs to be at least 16 milliseconds because on old OSes like XP,
	// the timer is updated only 64 times a second.
	private static int sequenceRecycleDelayMsec = 100;
	/**
	 * Log object
	 */
	private static Log log = LogFactory.getLog(SequenceFactory.class);
	// Convert a number to different base
	private static String convertLongToRadix(Long number) {
		return Long.toString(number, RADIX);
	}
	
	// Left zero pad the sequence counter to 6 characters
	private static String leftZeroPadSequence(String str) {
		String paddedStr = String.format("%6s", str).replace(' ', '0');
		return paddedStr;
	}
	
	// Get the current Java time in milliseconds
	private static Long getCurrentTime() {
		return java.lang.System.currentTimeMillis();
	}
	
	// Return the current sequence counter; if reached maximum then pause and recycle
	private static long getSequenceCounter() {
		if (++sequenceCounter > SEQUENCE_MAX) {
			pauseOnSequenceRecycle();
			sequenceCounter = SEQUENCE_MIN;
		}
		return sequenceCounter;
	}
	
	// Before starting next cycle, pause a tiny bit to allow the time to tick over.
	private static void pauseOnSequenceRecycle() {
		try {
			// Sleep for a few milliseconds to advance the time snapshot
			Thread.sleep(sequenceRecycleDelayMsec);
			timeSnapshot = getCurrentTime();
		} catch (InterruptedException e) {
			// FAILSAFE: if cannot sleep, just increment by 1 millisecond to ensure
			// the time element is different
			timeSnapshot++;
		};
	}
		
	/**
	 * Get the next unique sequence number
	 */
	public synchronized static String getSequence() {
		String seqElement = leftZeroPadSequence(convertLongToRadix(getSequenceCounter()));
		return (convertLongToRadix(timeSnapshot) + seqElement).toUpperCase();
	}

	
	/**
	 * Get the next unique sequence number
	 * @throws SwtException 
	 * @throws SQLException 
	 */
	public synchronized static String getSequenceFromDb(String sequenceName) throws SwtException {
		Connection conn = null;
		// String variable to hold the hostId
		String hostId = null;
		// Varibale to hold dataSource
		DataSource dataSource = null;
		// prepared Statement Instance
		Statement stmt = null;
		ResultSet rs = null;
		String seqElement = null;
		String sequenceQuery = null;
		dataSource = (DataSource) SwtUtil.getBean("dataSource");
		long id;
		try {
			log.debug("SequenceFactory - [ getSequenceFromDb ]- Entry");
			conn = dataSource.getConnection();
			sequenceQuery = "SELECT " + sequenceName
					+ ".nextval FROM dual";
			stmt = conn.createStatement();
			rs = stmt.executeQuery(sequenceQuery);
			rs.next();
			id = rs.getLong(1);
			seqElement = convertLongToRadix(id);
		} catch (SQLException sqlException) {
			log.error("SequenceFactory"
					+ " - Exception Catched in [ getSequenceFromDb ] method : - "
					+ sqlException.getMessage());
			throw SwtErrorHandler.getInstance().handleException(sqlException,
					"recalculateData", SequenceFactory.class);
		} catch (Exception exp) {
			log.error("SequenceFactory"
					+ " - Exception Catched in [ getSequenceFromDb ] method : - "
					+ exp.getMessage());
			throw new SwtException(exp.getMessage());
		} finally {
			SwtException thrownException = null;
			Object[] exceptions = JDBCCloser.close(rs, stmt, conn, null);
			
			if (exceptions[0] != null)
				thrownException = SwtErrorHandler.getInstance().handleException((SQLException) exceptions[0], "getSequenceFromDb",SequenceFactory.class);
			
			if (thrownException == null && exceptions[1] !=null) 
				thrownException = SwtErrorHandler.getInstance().handleException((HibernateException) exceptions[1], "getSequenceFromDb",SequenceFactory.class);
			
			if (thrownException != null)
				throw thrownException;
		}
		return seqElement;
	}
	/**
	 * Get the next unique sequence number
	 * @throws SwtException 
	 * @throws SQLException 
	 */
	public synchronized static Long getSequenceFromDbAsLong(String sequenceName) throws SwtException {
		Connection conn = null;
		// Varibale to hold dataSource
		DataSource dataSource = null;
		// prepared Statement Instance
		Statement stmt = null;
		ResultSet rs = null;
		String sequenceQuery = null;
		dataSource = (DataSource) SwtUtil.getBean("dataSource");
		long id;
		try {
			log.debug("SequenceFactory - [ getSequenceFromDb ]- Entry");
			conn = dataSource.getConnection();
			sequenceQuery = "SELECT " + sequenceName
					+ ".nextval FROM dual";
			stmt = conn.createStatement();
			rs = stmt.executeQuery(sequenceQuery);
			rs.next();
			id = rs.getLong(1);
		} catch (SQLException sqlException) {
			log.error("SequenceFactory"
					+ " - Exception Catched in [ getSequenceFromDb ] method : - "
					+ sqlException.getMessage());
			throw SwtErrorHandler.getInstance().handleException(sqlException,
					"recalculateData", SequenceFactory.class);
		} catch (Exception exp) {
			log.error("SequenceFactory"
					+ " - Exception Catched in [ getSequenceFromDb ] method : - "
					+ exp.getMessage());
			throw new SwtException(exp.getMessage());
		} finally {
			SwtException thrownException = null;
			Object[] exceptions = JDBCCloser.close(rs, stmt, conn, null);
			
			if (exceptions[0] != null)
				thrownException = SwtErrorHandler.getInstance().handleException((SQLException) exceptions[0], "getSequenceFromDb",SequenceFactory.class);
			
			if (thrownException == null && exceptions[1] !=null) 
				thrownException = SwtErrorHandler.getInstance().handleException((HibernateException) exceptions[1], "getSequenceFromDb",SequenceFactory.class);
			
			if (thrownException != null)
				throw thrownException;
		}
		return id;
	}

	/**
	 * Gives a hash code by combining date+host ip
	 * The access for this method is synchronized
	 * @param hostIP
	 * @return
	 */
	public static synchronized String getSessionNumber(String hostIP, String interfaceId){
		int radix = 36;
		String millis = ""+java.lang.System.currentTimeMillis() ;
		String rtn = Long.toString((millis+hostIP+interfaceId).hashCode(), radix);		
		rtn = String.format("%1$7s",rtn);
		rtn = rtn.replaceAll("-","Z");
		rtn = rtn.replaceAll(" ","0");
		return rtn.toUpperCase();	
	}
	
}
