package org.swallow.util.struts;

import javax.servlet.http.HttpServletRequest;

import org.swallow.util.SwtUtil;

import com.opensymphony.xwork2.ActionSupport;

import java.util.Iterator;

/**
 * Custom ActionSupport class
 * It adds support for legacy Errors saving
 * 
 * <AUTHOR> 2023
 *
 */
public class CustomActionSupport extends ActionSupport{

	private static final long serialVersionUID = 1L;

	// Common request paramateres
	protected String response;
	protected String nocache;
	protected String isAjax;
	protected String csrf;
	protected String isForm;
	protected String mfaToken;
	protected String method;
	protected String user_lang1234;
	
	
	public String getActionError() {
        return getActionErrors().stream().findFirst().orElse("");
    }
    
	public String getHtmlErrors() {
        return getActionError();
    }
	
	public String getActionMessage() {
        return getActionMessages().stream().findFirst().orElse("");
    }
	
    /**
     * <p>Save the specified error messages keys into the appropriate request
     * attribute for use by the &lt;html:errors&gt; tag, if any messages are
     * required. Otherwise, ensure that the request attribute is not
     * created.</p>
     *
     * @param request The servlet request we are processing
     * @param errors  Error messages object
     * @since Struts 1.2
     */
	public void saveErrors(HttpServletRequest request, ActionMessages errors) {
		// Remove any error messages attribute if none are required
		if (errors == null || errors.isEmpty()) {
			clearActionErrors();
			return;
		}

		// Get the last error message
		Iterator<ActionMessage> errorIterator = errors.get();
		ActionMessage lastError = null;
		while (errorIterator.hasNext()) {
			lastError = errorIterator.next();
		}

		if (lastError != null) {
			String key = lastError.key;
			Object[] values = lastError.values;

			String translated = SwtUtil.getMessage(key, request, values);
			addActionError(translated);
		}
	}
    
    /**
     * <p>Save the specified messages keys into the appropriate request
     * attribute for use by the &lt;html:messages&gt; tag (if messages="true"
     * is set), if any messages are required. Otherwise, ensure that the
     * request attribute is not created.</p>
     *
     * @param request  The servlet request we are processing.
     * @param messages The messages to save. <code>null</code> or empty
     *                 messages removes any existing ActionMessages in the
     *                 request.
     * @since Struts 1.1
     */
    protected void saveMessages(HttpServletRequest request, ActionMessages messages) {
        // Remove any error messages attribute if none are required
        if ((messages == null) || messages.isEmpty()) {
            clearMessages();
            return;
        }
        messages.get().forEachRemaining(message->{
        	String key = ((ActionMessage)message).key;
        	Object[] values = ((ActionMessage)message).values;
        	
        	String translated = SwtUtil.getMessage(key, request, values);
        	addActionMessage(translated);
        });
    }

	public String getResponse() {
		return response;
	}

	public void setResponse(String response) {
		this.response = response;
	}

	public String getNocache() {
		return nocache;
	}

	public void setNocache(String nocache) {
		this.nocache = nocache;
	}

	public String getIsAjax() {
		return isAjax;
	}

	public void setIsAjax(String isAjax) {
		this.isAjax = isAjax;
	}

	public String getCsrf() {
		return csrf;
	}

	public void setCsrf(String csrf) {
		this.csrf = csrf;
	}

	public String getIsForm() {
		return isForm;
	}

	public void setIsForm(String isForm) {
		this.isForm = isForm;
	}

	public String getMfaToken() {
		return mfaToken;
	}

	public void setMfaToken(String mfaToken) {
		this.mfaToken = mfaToken;
	}

	public String getMethod() {
		return method;
	}

	public void setMethod(String method) {
		this.method = method;
	}

	public String getUser_lang1234() {
		return user_lang1234;
	}

	public void setUser_lang1234(String user_lang1234) {
		this.user_lang1234 = user_lang1234;
	}
    
    
    
    
}
