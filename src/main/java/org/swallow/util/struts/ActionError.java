package org.swallow.util.struts;

import java.io.Serializable;

public class ActionError extends ActionMessage implements Serializable {
	public ActionError(String key) {
		super(key);
	}

	public ActionError(String key, Object value0) {
		super(key, value0);
	}

	public ActionError(String key, Object value0, Object value1) {
		super(key, value0, value1);
	}

	public ActionError(String key, Object value0, Object value1, Object value2) {
		super(key, value0, value1, value2);
	}

	public ActionError(String key, Object value0, Object value1, Object value2, Object value3) {
		super(key, value0, value1, value2, value3);
	}

	public ActionError(String key, Object[] values) {
		super(key, values);
	}
}
