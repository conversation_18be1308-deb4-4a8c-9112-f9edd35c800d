package org.swallow.util.struts;

import java.io.Serializable;

public class ActionErrors extends ActionMessages implements Serializable {

	public static final String GLOBAL_ERROR = "org.apache.struts.action.GLOBAL_ERROR";

	public ActionErrors() {
	}

	public ActionErrors(ActionErrors messages) {
		super(messages);
	}

	public void add(String property, ActionError error) {
		super.add(property, error);
	}
}
