package org.swallow.util.pcm;

public final class PCMConstant {
	// String variable to hold root node for search
	public static final String SEARCH = "search";
	// typeId tag
	public static final String SEARCH_TYPEID = "typeId";
	// valueId tag
	public static final String SEARCH_VALUEID = "valueId";
	// typeCode tag
	public static final String SEARCH_TYPECODE = "typeCode";
	// typeEnum tag
	public static final String SEARCH_TYPEENUM = "typeEnum";
	// code tag
	public static final String SEARCH_CODE = "code";
	// label tag
	public static final String SEARCH_LABEL = "label";
	// profilefield tag
	public static final String SEARCH_PROFILEFIELD = "profileField";
	// expression tag
	public static final String SEARCH_EXPRESSION = "expression";
	// fieldName tag
	public static final String SEARCH_FIELDNAME = "fieldName";
	// tableName tag
	public static final String SEARCH_TABLENAME = "tableName";
	// aliasTable tag
	public static final String SEARCH_ALIAS_TABLE = "aliasTable";
	// label tag
	public static final String SEARCH_COLUMN_VALUE_UPPER = "columnValueUpper";
	//typeList tag
	public static final String SEARCH_TYPELIST = "typeList";
	//hiddenOperator tag
	public static final String SEARCH_HIDDEN_OPERATOR = "hiddenOperator";
    //is index function tag 
    public static final String IS_INDEX_FUNC = "isIndexFunction";
  //hiddenColumn tag
    public static final String SEARCH_FIELD_VALUE = "fieldValue";
    public static final String SEARCH_MACRO_COLUMN = "macroColumn";
    
	// Sort type - alphabetic
	public static final String SORT_ALPHA = "alphabetic";
	
	// HELP_URL
		public static final String HELP_URL = "helpurl";
		
	// status list tag
	public static final String CURRENCYLIST = "currencyList";
	public static final String MULTIPLIERLIST = "multiplierList";
	public static final String COUNTRYLIST = "country";
	public static final String SOURCESLIST = "source";
	public static final String ENTITYLIST = "entityList";
	public static final String MESSAGE_TYPESLIST = "messageType";
	public static final String ACCOUNT_GROUPSLIST = "AcctGrpList";
	public static final String ACCOUNT_SCENARIOLIST = "scenarioList";
	public static final String ACCOUNT_OPERATORLIST = "amountOperatorList";
	public static final String PROCESS_CATEGORY_LIST = "processCategoryList";
	
	

	/***
	 * 
	 * 
	 * 
	 * */
	
	// String variable to hold rules component id
		public static final String RULE_COMPONENT_ID = "ruleDefinition";
		// String variable to hold Rule programId
		public static final String RULE_PROGRAM_ID = "221";
		// set detail url tag
		public static final String RULEDETAILURL = "ruledetailurl";
		// Rule Definiton details screen popup
		public static final String RULE_DETAIL_URL = "swf/aml/maintenance/RulesDefinitionAdd.swf";
		// set add rule url tag
		public static final String ADDRULEURL = "addRuleUrl";
		// add rule screen popup
		public static final String ADD_RULE_URL = "swf/aml/maintenance/RulesDefinitionAddRule.swf";
		// String variable to hold root node for rules
		public static final String RULES = "rules";

		// String variable to hold ruleId label hearder
		public static final String RULEID_COLUMN_HEADER = "rulesDefinitionScreen.column.ruleId";
		// String variable to hold ruleName label hearder
		public static final String RULENAME_COLUMN_HEADER = "rulesDefinitionScreen.column.ruleName";
		// String variable to hold ruleStatus label hearder
		public static final String RULESTATUS_COLUMN_HEADER = "rulesDefinitionScreen.column.ruleStatus";
		// String variable to hold ruleType label hearder
		public static final String RULETYPE_COLUMN_HEADER = "rulesDefinitionScreen.column.ruleType";
		// String variable to hold levelCode label hearder
		public static final String LEVELCODE_COLUMN_HEADER = "rulesDefinitionScreen.column.levelCode";
		// String variable to hold description label hearder
		public static final String DESCRIPTION_COLUMN_HEADER = "rulesDefinitionScreen.column.description";
		// String variable to hold periodType label hearder
		public static final String PERIODETYPE_COLUMN_HEADER = "rulesDefinitionScreen.column.periodType";
		// String variable to hold periodeMultiple label hearder
		public static final String PERIODEMULTIPLE_COLUMN_HEADER = "rulesDefinitionScreen.column.periodeMultiple";
		// String variable to hold periodValue label hearder
		public static final String PERIODEVALUE_COLUMN_HEADER = "rulesDefinitionScreen.column.periodValue";
		// String variable to hold updateUser label hearder
		public static final String UPDATE_USER_COLUMN_HEADER = "rulesDefinitionScreen.column.updateUser";
		// String variable to hold updateDate label hearder
		public static final String UPDATEDATE_COLUMN_HEADER = "rulesDefinitionScreen.column.updateDate";
		// String variable to hold level type header
		public static final String LEVEL_TYPE_COLUMN_HEADER = "rulesDefinitionScreen.column.levelType";

		// ruleId tag
		public static final String RULEID = "ruleId";
		// ruleName tag
		public static final String RULENAME = "ruleName";
		// ruleStatus tag
		public static final String RULESTATUS = "ruleStatus";
		// ruleStatusLabel tag
		public static final String RULESTATUSLABEL = "ruleStatusLabel";
		// ruleType tag
		public static final String RULETYPE = "ruleType";
		// ruleType tag
		public static final String RULETYPELABEL = "ruleTypeLabel";
		// levelCode tag
		public static final String LEVELCODE = "levelCode";
		// levelCode tag
		public static final String LEVELLABEL = "levelLabel";
		// description tag
		public static final String RULE_DESCRIPTION = "description";
		// periodType tag
		public static final String PERIODTYPE = "periodType";
		// periodTypeLabel tag
		public static final String PERIODTYPELABEL = "periodTypeLabel";
		// periodeMultiple tag
		public static final String PERIODMULTIPLE = "periodMultiple";
		// periodValue tag
		public static final String PERIODVALUE = "periodValue";
		// editable tag
		public static final String EDITABLE = "editable";
		// ruleQuery tag
		public static final String RULEQUERY = "ruleQuery";
		// ruleText tag
		public static final String RULETEXT = "ruleText";
		// rule query to be modified tag
		public static final String MODIFIABLE_RULE_QUERY = "modifiableRuleQuery";
		// rule Text to be modified tag
		public static final String MODIFIABLE_RULE_TEXT = "modifiableRuleText";
		// block tag
		public static final String BLOCKED = "blocked";
		// insert alert tag
		public static final String UPDATE_ALERT = "updateAlert";
		// rule status list tag
		public static final String RULESTATUSLIST = "ruleStatusList";
		// period type list tag
		public static final String PERIODETYPELIST = "periodTypeList";
		// level code list tag
		public static final String LEVELCODELIST = "levelCodeList";
		// rule type list tag
		public static final String RULETYPELIST = "ruleTypeList";
		// rule type view list tag
		public static final String VIEWRULETYPELIST = "viewRuleTypeList";
		// checkLimit tag
		public static final String CHECK_LIMIT = "checkLimit";
		// expiryDate tag
		public static final String EXPIRY_DATE = "expiryDate";
		// ruleTextId tag
		public static final String RULE_TEXT_ID = "ruleTextId";
		// ruleDescId tag
		public static final String RULE_DESC_ID = "ruleDescId";
		// sign tag
		public static final String RULE_SIGN = "sign";
		// sign label tag
		public static final String RULE_SIGN_LABEL = "signLabel";
		// sign profile tag
		public static final String RULE_SIGN_LIST = "signList";
		// allCltAccount rule tag
		public static final String ALL_CLT_ACCOUNT = "allCltAccount";
		// handleProcessedAlerts rule tag
			public static final String HANDLE_PROCESSED_ALERTS = "handleProcessedAlerts";
		
		// missing Info tag
		public static final String MISSING_INFO = "missingInfo";
		// level type tag
		public static final String LEVEL_TYPE = "levelType";
		// sign profile tag
		public static final String RULE_LIST_SECONDARY_RULES = "lstSecondaryRules";
		// secondary rule tag
		public static final String SECONDARY_RULE = "secondaryRule";
		// sign profile tag
		public static final String RULE_LIST_SUB_TYPE_FACTOR= "lstSubTypeFactor";

		/**
		 * Added for rulesDefinition screen by Amani on 10/03/2011 - END
		 *
		 */
		
		
		
		/**
		 * Added for Currency screen by YElFakih on 19/03/2019 - INI
		 *
		 */ 
		public static final String DASHBOARD="dashboard";
		public static final String CURRENCYTHRESHOLD="applyCurrencyThreshold";
		public static final String CURRENCYMULTIPLIER="applyCurrencyMultiplier";
		public static final String SPREADONLY="spreadOnly";
		public static final String VOLUME="volume";
		public static final String SYSTEM_STATUS="systemStatus";
		public static final String DATE_FROM_SESSION="sysDateFrmSession";
		public static final String TAB_IS_CHANGED="tabIsChanged";
		public static final String TESTDATE="dbDate";
		public static final String BUSINESSDAY ="businessday";
		public static final String DATELABEL ="dateLabel";
		public static final String CONTENT ="content";
		public static final String LAST_REF_TIME="lastRefTime";
		public static final String REF_RATE="refresh";
		public static final String SELECTED_ENTITY="selectedEntity";
		
	
		
		/**
		 * Added for Currency screen by YElFakih on 19/03/2019 - END
		 *
		 */ 
		
		
		/**
		 * Added for Currency screen by YElFakih on 19/03/2019 - INI
		 *
		 */ 
		public static final String CURRENCY="currency";
		public static final String CURRENCYCODE="ccy";
		public static final String CURRENCYNAME="ccyName";
		public static final String ORDINAL="ordinal";
		public static final String MULTIPLIER="multiplier";
		public static final String LARGE_AMOUNT_THRESHOLD="largeAmountThr";
		public static final String LARGE_AMOUNT_COLUMN_HEADER = "Large Amt. Threshold";
		public static final String MULTIPLIER_COLUMN_HEADER = "Multiplier";
		public static final String ORDINAL_COLUMN_HEADER = "Ordinal";
		public static final String CURRENCY_NAME_COLUMN_HEADER = "Currency Name";
		/**
		 * Added for Currency screen by YElFakih on 19/03/2019 - END
		 *
		 */ 
		
		
		
		/**
		 * Added for CATEGORY screen by YElFakih on 19/03/2019 - INI
		 *
		 */ 
		public static final String CATEGORY="category";
		public static final String CATEGORY_ID="categoryId";
		public static final String CATEGORY_ID_HEADER="Category Id";
		public static final String ID_COLUMN_HEADER ="ID";
		public static final String CATEGORY_NAME="categoryName";
		public static final String CATEGORY_NAME_HEADER="Category Name";
		public static final String NAME_COLUMN_HEADER = "Name";
		public static final String SOURCE_COLUMN_HEADER= "Source";
		public static final String IS_ACTIVE="isActive";
		public static final String URGENT_SPREAD_IND="type";
		public static final String USE_LIQ_CHECK="useLiqCheck";
		public static final String ASSIGN_METHOD = "assignMethod";
		public static final String INCL_TARGET_COLUMN_HEADER= "Inc. in Target";
		public static final String INCL_TARGET_PAY_RELEASED="inclTargetPercent";
		public static final String INCL_AVAILABLE_LIQ_COLUMN_HEADER= "Inc. in Av. Liquidity";
		public static final String INCL_IN_AVAILABLE_LIQ_CALC="inclInAvailableLiq";
		public static final String RULE_ASSIGN_PRIORITY="ruleAssignPriority"; 
		public static final String RULE_ASSIGN_PRIORITY_HEADER="Rule Assignment Priority";
		public static final String SET_RELEASE_TIME="setReleaseTime";
		public static final String SPECIFIED_RELEASE_TIME="specifiedTime";
		
		/**
		 * Added for CATEGORY screen by YElFakih on 19/03/2019 - END
		 *
		 */ 
		
		
		/**
		 * Added for CATEGORY RULE screen by YElFakih on 19/03/2019 - INI
		 *
		 */ 
		public static final String CATEGORY_RULE="categoryRule";
		public static final String CATEGORY_RULE_ID="categoryRuleId";
		public static final String CATEGORY_RULE_NAME="categoryRuleName";
		public static final String RULE="rule";
		public static final String RULE_ID="ruleId";
		public static final String APPLY_ONLY_TO_SOURCE="toSource";
		public static final String APPLY_ONLY_TO_CCY="toCcy";
		public static final String APPLY_ONLY_TO_ENTITY="toEntity";
		public static final String SP_ID_COLUMN_HEADER = "ID";
		public static final String SP_NAME_COLUMN_HEADER = "Name";

		/**
		 * Added for CATEGORY RULE screen by YElFakih on 19/03/2019 - END
		 *
		 */ 
		
		// String variable to hold risk factor component id
		public static final String RISK_FACTOR_COMPONENT_ID = "riskFactor";
		// String variable to hold Rule programId
		public static final String RISK_FACTOR_PROGRAM_ID = "254";
		
		
		// Current user
		public static final String SCREEN_ID = "screenid";
		
		// Current user tag
		public static final String CURRUSER = "currentuser";
		// Column type - String
		public static final String COLUMN_TYPE_STRING = "str";
		// Column type - Number
		public static final String COLUMN_TYPE_NUMBER = "num";
		// Column type - Number
		public static final String COLUMN_TYPE_BOOLEAN = "bool";
		// Column type - checkbox
		public static final String COLUMN_TYPE_CHECK= "checkbox";
		// Column type - date
		public static final String COLUMN_TYPE_DATE= "date";
		// Column type - link
		public static final String COLUMN_TYPE_LINK= "link";
		
		// Column type - link-num
		public static final String COLUMN_TYPE_LINK_NUM= "link:num";
		// Test date tag
		public static final String TEST_DATE = "testdate";
		// String variable to hold exception
		public static final String EXCEPTION_TAG = "exception";
		/******Constants for account groups screen**************/
		public static final String ACCOUNT_MAINTENANCE = "PCAccountMaintenance";
		public static final String ACC_GP_ID = "AccGpId";
		public static final String ACC_GP_NAME = "accountGpName";
		public static final String NO_ACCOUNTS = "numberOfAccounts";
		public static final String SPREAD = "spreadProfileName";
		public static final String KICK_OFF_TIME = "kickoffTime";
		public static final String TARGET_CALCULATION = "targetCalculation";
		public static final String EOD_TIME = "EODTime";
		public static final String COB_TIME = "COBTime";
		public static final String ONLY_CENTRAL_BANK = "IsOnlyCentralBank";
		public static final String SPREAD_LIST = "spreadList";
		public static final String GROUP_ID_LIST = "groupIdList";
		public static final String ORDINAL_LIST = "ordinaList";
		public static final String PCM_INPUT = "pcmInputFlag";
		public static final String PRIORITY_LIST = "priorityList";
		public static final String MAX_ORDINAL = "maxOrdinal";
		public static final String ORDINAL_LIST_CR = "ordinaListCR";
		public static final String MAX_ORDINAL_CR = "maxOrdinalCR";
		public static final String EXIST_PAY_REQ = "existPayReq";
		public static final String TARGET_CALCULATION_LIST = "targtCalculationList";
		public static final String CATEGORY_LIST = "categoryList";
		public static final String ARCHIVE_LIST = "archiveList";
		public static final String DEFAULT_CATEGORY = "defaultCategory";
		public static final String QUICK_CATEGORY = "quickCategory";
		public static final String OFFSET_DAYS = "offsetDays";
		
	
		public static final String CURRENCY_COLUMN_HEADER = "Currency";
		public static final String ORDER_COLUMN_HEADER = "Order";
		public static final String ACC_GP_ID_COLUMN_HEADER = "Account Group ID";
		public static final String ACC_GP_NAME_COLUMN_HEADER = "Account Group Name";
		public static final String NO_ACCOUNTS_COLUMN_HEADER = "No of Accs";
		public static final String SPREAD_COLUMN_HEADER = "Spread Profile Name";
		public static final String KICK_OFF_TIME_COLUMN_HEADER = "Kickoff Time";
		public static final String EOD_TIME_COLUMN_HEADER = "End Intra. Rel. Phase";
		public static final String COB_TIME_COLUMN_HEADER = "COB Time";
		/******Constants for Spread Profiles screen**************/
		public static final String SPREAD_PROFILES_MAINTENANCE = "SpreadProfilesMaintenance";
		public static final String SPREAD_PROFILES_MAINTENANCE_ADD = "SpreadProfilesMaintenanceAdd";
		public static final String SPREAD_PROCESS_POINT = "SpreadProcessPoints";
		public static final String SPREAD_ID = "spreadId";
		public static final String SPREAD_NAME = "spreadName";
		public static final String CURRENCY_CODE = "ccy";
		public static final String SPREAD_ID_COLUMN_HEADER = "Spread Profile ID";
		public static final String SPREAD_NAME_COLUMN_HEADER = "Spread Profile Name";
		public static final String CURRENCY_CODE_COLUMN_HEADER = "Currency Code";
		public static final String CHECKED = "Checked";
		/*General Tab*/
		public static final String ACCT_ID_NAME = "account_id_name";
		public static final String ACCT_ID_NAME_COLUMN_HEADER = "Account ID - Name";
		public static final String ENTITY = "entity";
		public static final String ENTITY_COLUMN_HEADER = "Entity";
		public static final String TYPE = "type";
		public static final String TYPE_COLUMN_HEADER = "Type";
		public static final String CLASS = "class";
		public static final String CLASS_COLUMN_HEADER = "Class";
		public static final String LEVEL = "level";
		public static final String LEVEL_COLUMN_HEADER = "Level"; 
		public static final String ILMCB = "ilmCB";
		public static final String ILMCB_COLUMN_HEADER = "ILM CB";
		public static final String BIC = "bic";
		public static final String BIC_COLUMN_HEADER = "Account BIC";
		/*Liquidity Tab*/
		public static final String ID_RESERVE = "idReserve";
		public static final String ID_RESERVE_COLUMN_HEADER = "idReserve";
		public static final String RESERVE_BALANCE = "reserve";
		public static final String RESERVE_BALANCE_COLUMN_HEADER = "Reserve Balance";
		public static final String USECL = "useCL";
		public static final String USECL_COLUMN_HEADER = "Use C/L";
		
		public static final String IS_ACCOUNT_IN_PAY= "isAccountInPayReq";
		/****CutOff Grid*/
		public static final String CUT_OFF_RULE_ID = "cutoffRuleId";
		public static final String TESTORDER = "testOrder";
		public static final String TESTORDER_COLUMN_HEADER = "Test Order";
		public static final String CUTTOFFTIME = "cutOffTime";
		public static final String CUTTOFFTIME_COLUMN_HEADER = "Cut-off Time";
		public static final String RULETEXT_COLUMN_HEADER = "Rule Expression";
		public static final String RULEQUERY_COLUMN_HEADER = "Rule";
		public static final String LOGTEXT = "logText";
		public static final String LOGTEXT_COLUMN_HEADER = "Log Text";
		public static final String RULE_CONDITIONS = "ruleConditions";
		public static final String RULE_CONDITION = "ruleCondition";
		public static final String RULE_CONDITION_COLUMN_HEADER = "ruleCondition";
		
		/**************Constants for process point Grid***************/
		public static final String PROCESS_ID = "processId";
		public static final String PROCESS_NAME = "processName";
		public static final String PROCESS_NAME_HEADER = "Process Name";
		public static final String TIME = "time";
		public static final String TIME_COLUMN_HEADER = "Time";
		public static final String TARGET = "target";
		public static final String TARGET_COLUMN_HEADER = "Target %";
		public static final String CATEGORIES = "categories";
		public static final String CATEGORIES_COLUMN_HEADER = "Categories";
		public static final String PROCESS = "process";
		public static final String PROCESS_COLUMN_HEADER = "Process";
		
		
		/*Stop rule details columns */
		public static final String STOPRULE_STOP_RULE_ID_HEADING = "Stop Id";
		public static final String STOPRULE_ACTIVE_STATUS_HEADING = "Active"; 
		public static final String STOPRULE_ACTION_ON_DEACTIVATION_HEADING = "Deactivation Action"; 
		public static final String STOPRULE_STATUS_HEADING = "Status"; 
		public static final String STOPRULE_STOP_RULE_NAME_HEADING = "Rule Name";
		public static final String STOPRULE_START_DATE_HEADING = "Start Date";
		public static final String STOPRULE_END_DATE_HEADING = "End Date";
		public static final String STOPRULE_SIMPLE_LABEL = "Simple";
		public static final String STOPRULE_COMPLEX_LABEL = "Advanced";
		public static final String STOPRULE_ACTIVE_LABEL = "Active";
		public static final String STOPRULE_INACTIVE_LABEL = "Inactive";
		
		public static final String STOPRULE_ACTION_WAITING_LABEL = "Set To Initial Status";
		public static final String STOPRULE_ACTION_LEAVE_LABEL = "Leave Stopped ";
		
		
		
		
		
		/*Stop rule details columns */
		public static final String STOPRULE_STOP_RULE_ID_TAGNAME = "stopRuleId";
		public static final String STOPRULE_ACTIVE_STATUS_TAGNAME = "isActive"; 
		public static final String STOPRULE_STATUS_TAGNAME = "status"; 
		public static final String STOPRULE_STOP_RULE_NAME_TAGNAME = "stopReasonText";
		public static final String STOPRULE_ACTION_ON_DEACTIVATION_TAGNAME = "actionOnDeactivation";
		public static final String STOPRULE_ACTION_ON_DEACTIVATION_STATUS_TAGNAME = "actionOnDeactivationStatus";
		public static final String STOPRULE_IS_CHANGABLE_TAGNAME = "isChangable";
		public static final String STOPRULE_START_DATE_TAGNAME = "activateOnDate";
		public static final String STOPRULE_ACTIVATED_ON_DATE_TAGNAME = "activatedOnDate";
		public static final String STOPRULE_NUMBER_OF_LINKED_PR_TAGNAME = "numberOfLinkedPR";
		public static final String STOPRULE_END_DATE_TAGNAME = "deactivateOnDate";
		
		
		
		
		/*  Stop rule Add */
		
		public static final String STOPRULEADD_ROOT_TAG = "stopRuleAdd";
		
		/***systFomrat************/
		public static final String CURRENCYPATTERN = "currencyPattern";
		public static final String FOUR_EYES_REQUIRED = "fourEyesRequired";
		
		
		
		public static final String REQUIRE_AUTHORISATION = "requireAuthorisation";
		public static final String FACILITY_ID = "faciltiyId";
		
		public static final String AUTHORISATION_STOP_RULE_SCREEN_ID = "STOP_RULE";
		public static final String AUTHORISATION_STOP_ACCOUNT_GROUP_ID = "ACCT_GRP";
		public static final String AUTHORISATION_SPREAD_PROFILE_GROUP_ID = "SPREAD_PROFILES";
		
		
		/*Payment Request Message Out summary columns */
		public static final String PR_MESSAGE_OUT_SUMMARY_DATE_TAGNAME = "inputDate";
		public static final String PR_MESSAGE_OUT_SUMMARY_SOURCE_TAGNAME = "inputSource";
		public static final String PR_MESSAGE_OUT_SUMMARY_MESSAGE_TAGNAME = "messageId";
		public static final String PR_MESSAGE_OUT_SUMMARY_MESSAGE_ARC_TAGNAME = "messageArcId";
		public static final String PR_MESSAGE_OUT_SUMMARY_STATUS_TAGNAME = "status";
		public static final String PR_MESSAGE_OUT_SUMMARY_MESSAGE_BODY_TAGNAME = "messageBody";
		
		/*Payment Request Message Out summary columns */
		public static final String PR_MESSAGE_OUT_SUMMARY_DATE_HEADING = "Date";
		public static final String PR_MESSAGE_OUT_SUMMARY_SOURCE_HEADING = "Source";
		public static final String PR_MESSAGE_OUT_SUMMARY_MESSAGE_HEADING = "Message";
		public static final String PR_MESSAGE_OUT_SUMMARY_MESSAGE_ARC_HEADING = "Message Arc";
		public static final String PR_MESSAGE_OUT_SUMMARY_STATUS_HEADING = "Status";
		public static final String MESSAGE_OUT = "messageout";
		
		
		/*Payment Request Message summary columns */
		public static final String PR_MESSAGE_SUMMARY_INPUTDATE_TAGNAME = "inputDate";
		public static final String PR_MESSAGE_SUMMARY_MESSAGE_TAGNAME = "messageId";
		public static final String PR_MESSAGE_SUMMARY_STATUS_TAGNAME = "status";
		public static final String PR_MESSAGE_SUMMARY_MESSAGE_BODY_TAGNAME = "messageBody";
		
		/*Payment Request Message summary columns */
		public static final String PR_MESSAGE_SUMMARY_INPUTDATE_HEADING = "Date";
		public static final String PR_MESSAGE_SUMMARY_MESSAGE_HEADING = "Message";
		public static final String PR_MESSAGE_SUMMARY_STATUS_HEADING = "Status";
		public static final String MESSAGE = "messageout";
		
		/*Payment Request Stop summary columns */
		public static final String PR_STOP_SUMMARY_STOP_RULE_ID_TAGNAME = "stopRuleId";
		public static final String PR_STOP_SUMMARY_STOP_RULE_NAME_TAGNAME = "stopRuleName";
		public static final String PR_STOP_SUMMARY_STOP_DATE_TAGNAME = "stopDate";
		public static final String PR_STOP_SUMMARY_UNSTOP_DATE_TAGNAME = "unstopDate";
		public static final String PR_STOP_SUMMARY_UNSTOP_BY_TAGNAME = "unstopBy";
		public static final String PR_STOP_SUMMARY_UNSTOP_BY_PAY_REQ_TAGNAME = "payReq";
		/*Payment Request Stop summary columns */
		public static final String PR_STOP_SUMMARY_STOP_RULE_ID_HEADING = "Rule Id";
		public static final String PR_STOP_SUMMARY_STOP_RULE_NAME_HEADING = "Rule Name";
		public static final String PR_STOP_SUMMARY_STOP_DATE_HEADING = "Stop Date";
		public static final String PR_STOP_SUMMARY_UNSTOP_DATE_HEADING = "Unstop Date";
		public static final String PR_STOP_SUMMARY_UNSTOP_BY_HEADING = "Unstop By";
		public static final String PR_STOP_RULES = "prStopRules";
		
		
		/*Payment Request Logs columns */
		public static final String PR_LOG_SUMMARY_LOG_SEQ_TAGNAME = "logSeq";
		public static final String PR_LOG_SUMMARY_LOG_DATE_TAGNAME = "logDate";
		public static final String PR_LOG_SUMMARY_LOG_USER_TAGNAME = "logUser";
		public static final String PR_LOG_SUMMARY_LOG_DETAILS_TAGNAME = "logDetails";
		/*Payment Request Logs columns */
		public static final String PR_LOG_SUMMARY_LOG_SEQ_HEADING = "Id";
		public static final String PR_LOG_SUMMARY_LOG_DATE_HEADING = "Date";
		public static final String PR_LOG_SUMMARY_LOG_USER_HEADING = "User";
		public static final String PR_LOG_SUMMARY_LOG_DETAILS_HEADING = "Details";
		
		public static final String PR_LOG_SUMMARY_FROMDATE = "fromdate";
		public static final String PR_LOG_SUMMARY_TODATE = "todate";

		
		public static final String PAYMENT_REQUEST_ROOT_TAG = "payRequest";
		
		
            /********Dashboard************/
		
		
		public static final String WAITING_STATUS = "Waiting";
		public static final String BLOCKED_STATUS = "Blocked";
		public static final String RELEASED_STATUS= "Released";
		public static final String CANCELLED_STATUS = "Cancelled";
		public static final String STOPPED_STATUS = "Stopped";
		
		public static final String BACK_VALUED = " Input as Back Valued";
		public static final String BLOCKED_INPUT = "Input after Cut-off";
		public static final String BLOCKED_STOPPED = "Stopped and cut-off passed";
		public static final String BLOCKED_CUTOFF = "Cut-off passed";
		
		
		public static final String DASHBOARD_LEVEL2 = "DashboardLevel2";
		
		public static final String PAGE_SIZE = "pageSize";
		public static final String STATUS = "status";
		public static final String STATUS_COLUMN_HEADER = "Status";
		public static final String PAYMENT_ID = "paymentId";
		public static final String PAYMENT_ID_COLUMN_HEADER = "Payment ID";
		public static final String SOURCE = "source";
		public static final String ACCOUNT = "account";
		public static final String ACCOUNT_COLUMN_HEADER = "Account";
		public static final String AMOUNT = "amount";
		public static final String AMOUNT_COLUMN_HEADER = "Amount";
		public static final String VALUE_DATE = "valueDate";
		public static final String INPUT_SINCE_DATE = "inputSince";
		public static final String VALUE_DATE_COLUMN_HEADER = "Value Date";
		public static final String SOD_BALANCE = "sodBalance";
		public static final String CONFIRMED_CREDIT = "confirmedCredit";
		public static final String CREDIT_LINE = "creditLine";
		public static final String RELEASE_PAYMENT = "releasePayment";
		public static final String OTHER_PAYMENTS = "otherPayments";
		public static final String EX_CREDIT_LINE = "exCreditLine";
		public static final String INC_CREDIT_LINE = "incCreditLine";
		public static final String RESERVE_BALANCED = "reserveBalanced";
		public static final String REASON = "reason";
		public static final String REASON_COLUMN_HEADER  = "Reason";
		
		public static final String ACCOUNT_LIST = "AcctList";
		public static final String STATUS_LIST = "statusList";
		public static final String BLOCKED_LIST = "blockedList";
		public static final String PAYID_RULE="payIdRule";
		public static final String IS_STOPPROCESS_RUNNING="isStopProcRun";
		
		/********Party Search************/
		public static final String PARTY_SEARCH = "PartySearch";
		public static final String PARTY_ID = "PartyId";
		public static final String PARTY_ID_COLUMN_HEADER = "Party Id";
		public static final String PARTY_NAME = "PartyName";
		public static final String PARTY_NAME_COLUMN_HEADER = "Party Name";
		
		public static final String PAYMENT_SEARCH_ROOT_TAG = "paymentSearch";
		
		/*******PCM Report***************/
		public static final String PCM_REPORT_ROOT_TAG = "pcmReport";
		public static final String REPORT_TYPE_LIST = "reportTypeList";
		public static final String PCM_REPORT_BLOCKED_PAYMENTS = "blockedPayments";
		public static final String FROM_DATE = "fromDate";
		public static final String FIRST_DATE_OF_PREVIOUS_MONTH = "firstDateOfPreviousMonth";
		public static final String LAST_DATE_OF_PREVIOUS_MONTH = "lastDateOfPreviousMonth";
		public static final String SINGLE_OR_RANGE = "singleOrRange";
		public static final String USE_CCY_MULTIPLIER = "useCcyMultiplier";
		public static final String REPORT_TYPE = "reportType";
		
		
		
		/** CODES IN I_DIC_MSG***/
		public static final String DIC_CODE_ERR = "ERR#";
		public static final String DIC_CODE_BLOCKED = "BLK#";
		public static final String DIC_CODE_RELEASED = "REL#";
		
		
		/* get the jasper object path  */
		public static final String PCM_BLOCK_PAYMENT_REPORTS_FILE = "/jsp/pc/report/BlockedPayment.jrxml";
		public static final String PCM_BLOCK_PAYMENT_SUB_REPORTS_FILE = "/jsp/pc/report/BlockedPaySubReport.jrxml";
		public static final String PCM_CURRENCY_DASHBORD_REPORTS_FILE = "/jsp/pc/report/CurrencyDashbord.jrxml";
		
		
		public static final String CHANGES_GRID_UNIQUESEQ_TAGNAME = "uniqueSeq";
		public static final String CHANGES_GRID_FACILTY_TAGNAME = "facilty";
		public static final String CHANGES_GRID_REFERENCE_TAGNAME = "reference";
		public static final String CHANGES_GRID_ACTION_TAGNAME = "action";
		/*Payment Request Logs columns */
		public static final String CHANGES_GRID_FACILTY_HEADING = "Facilty";
		public static final String CHANGES_GRID_REFERENCE_HEADING = "Reference";
		public static final String CHANGES_GRID_ACTION_HEADING = "Action";
		
		public static final String CHANGES_DETAILS_GRID_UNIQUESEQ_TAGNAME = "uniqueSeq";
		public static final String CHANGES_DETAILS_GRID_COLUMN_NAME_TAGNAME = "columnName";
		public static final String CHANGES_DETAILS_GRID_OLDVALUE_TAGNAME = "oldValue";
		public static final String CHANGES_DETAILS_GRID_NEWVALUE_TAGNAME = "newValue";
		
		public static final String CHANGES_DETAILS_GRID_COLUMN_NAME_HEADING = "column Name";
		public static final String CHANGES_DETAILS_GRID_OLDVALUE_HEADING = "Old";
		public static final String CHANGES_DETAILS_GRID_NEWVALUE_HEADING = "New";
		
		public static final String USER_DEFAULT_ENTITY = "userDefaultEntity";
}