/**
 * @(#)CacheManager.java 1.0 / Dec 5, 2005
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.util;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Hashtable;
import java.util.Iterator;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.swallow.dao.LogonDAO;
import org.swallow.exception.SwtException;
import org.swallow.maintenance.model.CurrencyMaster;
import org.swallow.maintenance.model.CurrencyMasterVO;
import org.swallow.maintenance.model.MiscParams;
import org.swallow.maintenance.service.CountryManager;
import org.swallow.maintenance.service.CurrencyMasterManager;
import org.swallow.maintenance.service.MiscParamsManager;
import org.swallow.maintenance.service.WeekdaysManager;
import org.swallow.work.service.SweepManager;

/**
 * CacheManager.java.<br>
 * 
 * This Singeton class has methods that holds references to Caches and manages
 * their creation and lifecycle.<br>
 */
public class CacheManager {
	// log instance for CacheManager
	private final static Log log = LogFactory.getLog(CacheManager.class);
	// Holds Collection of Currency Master
	private Collection<CurrencyMaster> currCol = null;
	// Holds Collection of country
	private Collection cntrCol = null;
	// Holds Collection of weekdays
	private Collection wkdysCol = null;
	// Holds Collection of misc params
	private Collection miscParamsCol = null;
	// Holds hashtable of currency decimal
	private Hashtable currDecimalHashTable = null;
	// CacheManager instance
	private static CacheManager cacheManagerInst = null;
	/*
	 * Code added by karthik on 20110817 for Mantis 1525 - ING connection
	 * exhaust issue
	 */
	// To maintain the hostId
	private static String hostId = null;

	public static CacheManager getInstance() throws SwtException {
		log.debug("entering 'getInstance()' method ");
		if (cacheManagerInst == null) {
			log.debug("Singelton Instance is null");
			cacheManagerInst = new CacheManager();
		}
		log.debug("exiting 'getInstance()' method ");
		return cacheManagerInst;
	}

	private CacheManager() throws SwtException {
		log.debug("entering 'CacheManager()' method ");
		CurrencyMasterManager curMgr = (CurrencyMasterManager) (SwtUtil
				.getBean("currencyMasterManager"));
		CountryManager cntMgr = (CountryManager) (SwtUtil
				.getBean("countryManager"));
		WeekdaysManager wkdMgr = (WeekdaysManager) (SwtUtil
				.getBean("weekdaysManager"));
		MiscParamsManager miscParamsManager = (MiscParamsManager) (SwtUtil
				.getBean("miscParamsManager"));
		// Get Currency list
		CurrencyMasterVO currVo = curMgr.getCurrenciesFromMst();
		currCol = currVo.getCurrencyMasterLVLList();
		currDecimalHashTable = currVo.getCurrencyDecimalHashTable();
		// Get Country list
		cntrCol = cntMgr.getCountries();
		// Get Holidays List
		wkdysCol = wkdMgr.getWeekdays();
		// Get Misc Params List
		String hostId = PropertiesFileLoader.getInstance().getPropertiesValue(
				SwtConstants.SWT_HOST_ID);
		miscParamsCol = miscParamsManager.getAllValues(hostId);
		log.debug("exiting 'CacheManager()' method ");
	}

	synchronized public Collection getCountries() throws SwtException {
		return cntrCol;
	}

	/**
	 * Gets the collection of Currencies
	 */
	synchronized public Collection<CurrencyMaster> getCurrencies()
			throws SwtException {
		return currCol;
	}

	synchronized public Hashtable getCurrencyDecimalHashTable()
			throws SwtException {
		return currDecimalHashTable;
	}

	synchronized public Collection getWeekdays() throws SwtException {
		return wkdysCol;
	}

	synchronized public Collection getMiscParamsDetails(String key1,
			String entityId) {
		Collection defaultList = new ArrayList();
		Collection miscEntityList = new ArrayList();
		Collection coll = null;
		Iterator itr1 = miscParamsCol.iterator();
		while (itr1.hasNext()) {
			MiscParams miscParams = (MiscParams) itr1.next();
			String paramkey1 = miscParams.getId().getKey1();
			String miscEntityId = miscParams.getId().getEntityId();
			if (entityId != null && paramkey1.equalsIgnoreCase(key1)
					&& miscEntityId.equalsIgnoreCase(entityId)
					&& (!entityId.trim().equals(""))) {
				miscEntityList.add(miscParams);
			} else if (paramkey1.equalsIgnoreCase(key1)
					&& miscEntityId.equalsIgnoreCase("*DEFAULT*")
					&& (entityId == null || (!miscEntityId.equals(entityId)))) {
				defaultList.add(miscParams);
			}
		}
		if (miscEntityList.size() > 0) {
			coll = miscEntityList;
		} else {
			coll = defaultList;
		}
		return coll;
	}

	/*
	 * Start: Code modified by RK on 09-Mar-2012 for Mantis 1645
	 */
	/**
	 * This method gets MiscParams value of given key, then convert them as
	 * LabelValueBean list and returns the same
	 * 
	 * @param key
	 * @param entityId
	 * @return Collection<LabelValueBean>
	 */
	synchronized public Collection<LabelValueBean> getMiscParamsLVL(String key,
			String entityId) {
		if(SwtConstants.SWEEP_SETTLE_METHOD_MISC_PARAM.equals(key)) {
			Collection<LabelValueBean> collection = new ArrayList<LabelValueBean>();
			SweepManager sweep = (SweepManager) (SwtUtil
					.getBean("sweepManager"));
			try {
				collection = sweep.getSweepSettlementMethodList(entityId);
			} catch (SwtException e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
			}
			
			return collection;
			
			
			
		}else {
			return putMiscParamsKey1inCacheLVL(key, entityId);
		}
	}

	/*
	 * End: Code modified by RK on 09-Mar-2012 for Mantis 1645
	 */

	private Collection putMiscParamsKey1inCacheLVL(String key1, String entityId) {
		Collection defaultList = new ArrayList();
		Collection miscEntityList = new ArrayList();
		Iterator itr1 = miscParamsCol.iterator();
		while (itr1.hasNext()) {
			MiscParams miscParams = (MiscParams) itr1.next();
			String paramkey1 = miscParams.getId().getKey1();
			String miscEntityId = miscParams.getId().getEntityId();
			if (entityId != null && paramkey1.equalsIgnoreCase(key1)
					&& miscEntityId.equalsIgnoreCase(entityId)
					&& (!entityId.trim().equals(""))) {
				miscEntityList.add(miscParams);
			} else if (paramkey1.equalsIgnoreCase(key1)
					&& miscEntityId.equalsIgnoreCase("*DEFAULT*")
					&& (entityId == null || (!miscEntityId.equals(entityId)))) {
				defaultList.add(miscParams);
			}
		}
		Collection outColl = new ArrayList();
		if (!key1.equals("POSLEVEL") && !key1.equals("LANG")) {
			outColl.add(new LabelValueBean("", ""));
		}
		Iterator itr = null;
		if (miscEntityList.size() > 0) {
			itr = miscEntityList.iterator();
		} else {
			itr = defaultList.iterator();
		}
		while (itr != null && itr.hasNext()) {
			MiscParams miscParams = (MiscParams) itr.next();
			String paramkey1 = miscParams.getId().getKey1();
			if (paramkey1.equalsIgnoreCase(key1)) {
				outColl.add(convertMiscParamtoValueBean(miscParams));
			}
		}
		return outColl;
	}

	private LabelValueBean convertMiscParamtoValueBean(MiscParams miscParamsObj) {
		return new LabelValueBean(miscParamsObj.getParValue(), miscParamsObj
				.getId().getKey2());
	}

	synchronized public Collection getMiscParamsLVL(String key1,
			String parValue, String entityId) {
		Collection outColl = new ArrayList();
		Collection coll = getMiscParamsLVL(key1, entityId);
		Iterator itr = coll.iterator();
		LabelValueBean obj = null;
		while (itr.hasNext()) {
			obj = (LabelValueBean) itr.next();
			if (obj.getValue().equals(parValue))
				outColl.add(obj);
		}
		return outColl;
	}

	synchronized public Collection getMiscParams(String key1, String entityId) {
		Collection outColl = new ArrayList();
		Collection<MiscParams> defaultList = new ArrayList<MiscParams>();
		Collection<MiscParams> miscEntityList = new ArrayList<MiscParams>();
		Iterator itr1 = miscParamsCol.iterator();
		while (itr1.hasNext()) {
			MiscParams miscParams = (MiscParams) itr1.next();
			String paramkey1 = miscParams.getId().getKey1();
			String miscEntityId = miscParams.getId().getEntityId();
			if (entityId != null && paramkey1.equalsIgnoreCase(key1)
					&& miscEntityId.equalsIgnoreCase(entityId)
					&& (!entityId.equals(""))) {
				miscEntityList.add(miscParams);
			} else if (paramkey1.equalsIgnoreCase(key1)
					&& miscEntityId.equalsIgnoreCase("*DEFAULT*")
					&& (entityId == null || (!miscEntityId.equals(entityId)))) {
				defaultList.add(miscParams);
			}
		}
		if (miscEntityList.size() > 0) {
			outColl = miscEntityList;
		} else {
			outColl = defaultList;
		}
		return outColl;
	}

	synchronized public Collection getMiscParams(String key1, String key2,
			String entityId) {
		Collection outColl = new ArrayList();
		Collection coll = getMiscParams(key1, entityId);
		Iterator itr = coll.iterator();
		MiscParams miscParamsObj = null;
		while (itr.hasNext()) {
			miscParamsObj = (MiscParams) itr.next();
			if (miscParamsObj.getId().getKey1().equals(key1)
					&& miscParamsObj.getId().getKey2().equals(key2))
				outColl.add(miscParamsObj);
		}
		return outColl;
	}

	/**
	 * 
	 * This function is used to get the host id and once got then it maintain it
	 * for whole process.
	 * 
	 * @return String
	 * @throws SwtException
	 */
	public String getHostId() throws SwtException {
		// Instance for LogonDAO
		LogonDAO logonDAO = null;
		try {
			log.debug("CacheManager - [getHostId] - " + "Entry");
			// check the host id is null or empty
			if (SwtUtil.isEmptyOrNull(hostId)) {
				// create the instance for the logonDAO
				logonDAO = (LogonDAO) (SwtUtil.getBean("logonDAO"));
				// get the hostid from database
				hostId = logonDAO.getHostIdFromDB();
			}
			log.debug("CacheManager - [getHostId] - " + "Exit");
		} catch (Exception ex) {
			log.error("Exception Catched in CacheManager - [getHostId] "
					+ ex.getMessage());
			throw new SwtException(ex.getMessage());
		} finally {
			// nullify objects
			logonDAO = null;
		}
		return hostId;
	}

	public String getmaxUser() {
		return PropertiesFileLoader.getInstance().getPropertiesLicenseValue(
				SwtConstants.SWT_MAX_USER_ID);
	}

	public String getexpiryDate() {
		return PropertiesFileLoader.getInstance().getPropertiesLicenseValue(
				SwtConstants.SWT_EXPIRY_DATE_ID);
	}

	public String getHost() {
		return PropertiesFileLoader.getInstance().getPropertiesLicenseValue(
				SwtConstants.SWT_HOST);
	}

	public String getLicenseHashCode() {
		return PropertiesFileLoader.getInstance().getPropertiesLicenseValue(
				SwtConstants.LICENSE_HASHCODE);
	}

	public ArrayList<String> getserverName() {
		ArrayList<String> HostnameList = new ArrayList<String>();
		for (int ipCounter = 1; ; ipCounter++) {
			String Hostname = PropertiesFileLoader.getInstance()
					.getPropertiesLicenseValue(
							SwtConstants.SWT_SERVER_ID + "" + ipCounter);
			if (Hostname != null) {
				if (Hostname.trim().equalsIgnoreCase("")) {
					break;
				}
				HostnameList.add(Hostname);
			} else {
				break;
			}
		}
		return HostnameList;
	}
}
