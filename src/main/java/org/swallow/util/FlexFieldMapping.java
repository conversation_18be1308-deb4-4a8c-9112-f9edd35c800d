package org.swallow.util;

import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.Iterator;
import java.util.Set;
import java.util.Map.Entry;

public class FlexFieldMapping {
	private HashMap<String, String> mapping = new HashMap<String, String> ();
	
	public HashMap<String, String> getMapping() {
		return mapping;
	}

	public void setMapping(HashMap<String, String> mapping) {
		this.mapping = mapping;
	}

	public void map (String key, String tag) {
		mapping.put (key, tag);
	}
	
	public String getTag (String key) {
		if (mapping.containsKey (key)) {
			return mapping.get (key);
		}
		
		return null;
	}
	
	public String getKey (String tag) {
		Iterator<Entry<String, String>> itr = mapping.entrySet().iterator();
		while (itr.hasNext()) {
			Entry<String, String> entry = itr.next();
			if (tag.equalsIgnoreCase (entry.getValue()))
				return entry.getKey();
		}
		
		return null;
	}
	
	public Set<String> getNames () {
		return mapping.keySet();
	}
	
	public Collection<String> getTags () {
		return mapping.values();
	}
}