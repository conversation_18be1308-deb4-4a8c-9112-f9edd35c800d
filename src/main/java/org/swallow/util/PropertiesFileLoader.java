/*
 * Created on Feb 7, 2006
 *
 * TODO To change the template for this generated file go to
 * Window - Preferences - Java - Code Style - Code Templates
 */
package org.swallow.util;

import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.Hashtable;
import java.util.Properties;
import java.util.Set;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.swallow.cluster.ClusteredConfigService;
import org.swallow.cluster.ZkUtils;

/**
 * <AUTHOR>
 * 
 * <pre>
 * Class to load properties files 
 *  - predict.properties
 *  - default_predict.properties
 *  - messagetype.properties
 *  - license.properties
 *  - nwd_facility.properties
 *  - ldap.properties
 *  - mail.properties
 * </pre>
 */
public class PropertiesFileLoader {

	/**
	 * Initializing logger object for this class
	 */
	private final Log log = LogFactory.getLog(this.getClass());

	public static Properties predictProp = new Properties();
	public static Properties defaultPredictProp = new Properties();
	public static Properties swiftProp = new Properties();
	public static Properties licenseProp = new Properties();
	public static Properties versionProp = new Properties();
	public static Properties facilityProp = new Properties();
	public static Hashtable defaultValues = new Hashtable();
	public static Properties xssFilterProp = new Properties();
	public static Properties mailProp = new Properties();
	
	public static Properties connectionProp = new Properties();
	public static Properties connectionPropPCM = new Properties();

	public static String sessionTimeOutPeriod_default = "5";
	public static PropertiesFileLoader propertiesFileLoader = null;

	// Ldap properties: Used for RSA Dual-Factor Authentication, Mantis 1671
	public static Properties ldapProp = new Properties();

	public static PropertiesFileLoader getInstance() {
		if (propertiesFileLoader == null) {
			propertiesFileLoader = new PropertiesFileLoader();
			defaultValues.put(SwtConstants.SESSION_TIMEOUT_PERIOD, "5");
			defaultValues.put(SwtConstants.SWT_HOST_ID, "SWALLOWTECH");
			// following code added for maxConcurrent User
			defaultValues.put(SwtConstants.CONCURRENT_MATCHING_PROCESS_USER,
					"5");
			defaultValues.put(SwtConstants.INACTIVE_DISABLE, "5");
			defaultValues.put(SwtConstants.DEFAULT_REFRESH_RATE, "30");
			defaultValues.put(SwtConstants.CACHE_EXPIRY_TIME, "30");

		}
		return propertiesFileLoader;
	}

	private PropertiesFileLoader() {
		InputStream pio=null;
		InputStream dpio=null;
		InputStream newio=null;
		InputStream licenseio=null;
		InputStream versionio=null;
		InputStream facilityio=null;
		InputStream ldapio=null;
		InputStream xssFilterio = null;
		InputStream mailPropio = null;
		
		InputStream connectionPropStream = null;
		InputStream connectionPropPCMStream = null;
		
		InputStream frDicPropio = null;
		InputStream enDicPropio = null;
		String pcmEnabled = null;
		
		try {
			log.debug(this.getClass().getName()
					+ "-[PropertiesFileLoader()]-Entry");

			 pio = this.getClass().getResourceAsStream(
					SwtConstants.PREDICT_PROPERTIES);
			predictProp.load(pio);

			 dpio = this.getClass().getResourceAsStream(
					SwtConstants.DEFAULT_PREDICT_PROPERTIES);
			defaultPredictProp.load(dpio);

			 newio = this.getClass().getResourceAsStream(
					SwtConstants.SWIFT_MESSAGE_PROPERTIES);
			swiftProp.load(newio);
		
			 licenseio = this.getClass().getResourceAsStream(
					SwtConstants.LICENSE_PROPERTIES);
			licenseProp.load(licenseio);
			
			versionio = this.getClass().getResourceAsStream(
					SwtConstants.VERSION_PROPERTIES);
			versionProp.load(versionio);
			// To read properties file
			 facilityio = this.getClass().getResourceAsStream(
					SwtConstants.FACILITY_PROPERTIES);
			facilityProp.load(facilityio);

			// Load ldap properties: Mantis 1671
			 ldapio = this.getClass().getResourceAsStream(
					SwtConstants.LDAP_PROPERTIES);
			ldapProp.load(ldapio);
			
			xssFilterio = this.getClass().getResourceAsStream(
					SwtConstants.XSS_FILTER_PROPERTIES);
			xssFilterProp.load(xssFilterio);
			// Load mail properties
			mailPropio = this.getClass().getResourceAsStream(
					SwtConstants.MAIL_PROPERTIES);
			mailProp.load(mailPropio);
			
			connectionPropStream = this.getClass().getResourceAsStream(
					SwtConstants.CONNECTION_PROPERTIES);
			connectionProp.load(connectionPropStream);
			
			
			// Update from clustered configurations (Needs static access to avoid cyclic dependencies)
			ClusteredConfigService.updateLiveClasspathConfigsFromClustered();
			
			
			pcmEnabled = getPropertiesValue(SwtConstants.PCM_ENABLED);
			
			if ("true".equalsIgnoreCase(pcmEnabled)) {
				connectionPropPCMStream = this.getClass().getResourceAsStream(
						SwtConstants.CONNECTION_PCM_PROPERTIES);
				connectionPropPCM.load(connectionPropPCMStream);
			}
			
			
			
			 HashMap<String, String> dicMap= new HashMap<>();
			 HashMap<String, String> dicMapFr= new HashMap<>();
			 
			 Properties dictionary = new Properties();
			 enDicPropio=null;
			 
			 //Commented as PCM dictionary is used aswell in predict screens (we will load all dictioniries)
//			 if ("true".equals(pcmEnabled)) {
				 enDicPropio = this.getClass().getResourceAsStream(
							SwtConstants.DIC_PCM_EN_PROPERTIES);
				 dictionary.load(enDicPropio);
				 
				 for (HashMap.Entry<Object, Object> entry : dictionary.entrySet()) {
					    Object key = entry.getKey();
					    Object value = entry.getValue();
					    dicMap.put(""+key , ""+value);
				}
//			 }
			 
			 enDicPropio = this.getClass().getResourceAsStream(
						SwtConstants.DIC_EN_PROPERTIES);
			 dictionary.load(enDicPropio);
			 
			 for (HashMap.Entry<Object, Object> entry : dictionary.entrySet()) {
				    Object key = entry.getKey();
				    Object value = entry.getValue();
				    dicMap.put(""+key , ""+value);
			}
			 
			 
			 Properties dictionaryfr = new Properties();
			 frDicPropio=null;
			 if ("true".equals(pcmEnabled)) {
				 frDicPropio = this.getClass().getResourceAsStream(
						 SwtConstants.DIC_PCM_FR_PROPERTIES);
				 dictionaryfr.load(frDicPropio);
				 
				 for (HashMap.Entry<Object, Object> entry : dictionaryfr.entrySet()) {
					 Object key = entry.getKey();
					 Object value = entry.getValue();
					 dicMapFr.put(""+key , ""+value);
				 }
			 }
			 
			 
			 frDicPropio = this.getClass().getResourceAsStream(
					 SwtConstants.DIC_FR_PROPERTIES);
			 dictionaryfr.load(frDicPropio);
			 
			 for (HashMap.Entry<Object, Object> entry : dictionaryfr.entrySet()) {
				 Object key = entry.getKey();
				 Object value = entry.getValue();
				 dicMapFr.put(""+key , ""+value);
			 }
			SwtUtil.dictionary_en = dicMap;
			SwtUtil.dictionary_fr = dicMapFr;
			
			// Override properties when running from a Docker image
			if("YES".equalsIgnoreCase(System.getenv(ZkUtils.PROPERTY_ENV_DOCKER))) {
				SwtUtil.overridePropertiesFromEnvironement(predictProp);
				//SwtUtil.overridePropertiesFromEnvironement(defaultPredictProp);
				SwtUtil.overridePropertiesFromEnvironement(swiftProp);
				//SwtUtil.overridePropertiesFromEnvironement(licenseProp);
				SwtUtil.overridePropertiesFromEnvironement(versionProp);
				//SwtUtil.overridePropertiesFromEnvironement(facilityProp);
				//SwtUtil.overridePropertiesFromEnvironement(xssFilterProp);
				SwtUtil.overridePropertiesFromEnvironement(mailProp);
			}
			
			log.debug(this.getClass().getName()
					+ "-[PropertiesFileLoader()]-Exit");
		} catch (Exception e) {
			log.error("Exception got in " + this.getClass().getName()
					+ "-[PropertiesFileLoader()]-" + e.getMessage());

		}
		finally{
			if (pio!=null)
				closeInputStream(pio);
			if (dpio!=null)
				closeInputStream(dpio);
			if (newio!=null)
				closeInputStream(newio);
			if (licenseio!=null)
				closeInputStream(licenseio);
			if (facilityio!=null)
				closeInputStream(facilityio);
			if (ldapio!=null)
				closeInputStream(ldapio);
			if (versionio!=null)
				closeInputStream(versionio);
			if (xssFilterio!=null)
				closeInputStream(xssFilterio);
			if (mailPropio!=null)
				closeInputStream(mailPropio);
		
		}
	}
   // Used to close inputStream
	private void closeInputStream(InputStream input) {
		if (input != null)
			try {
				input.close();
			} catch (IOException e) {
				log.error("Exception got in " + this.getClass().getName()
						+ "-[closeInputStream ]-" + e.getMessage());
			}
	}
	/**
	 * @param keyName
	 * @return
	 */
	public String getPropertiesValue(String keyName) {
		String keyValue = null;
		/*
		 * Start : Code modified by sandeepkumar for Mantis 1609 -
		 * default_predict.properties can be overridden through
		 * predict.properties
		 */

		try {
			log.debug(this.getClass().getName()
					+ "-[getPropertiesValue()]-Entry");
			keyValue = predictProp.getProperty(keyName);
			/*
			 * End : Code modified by sandeepkumar for Mantis 1609 -
			 * default_predict.properties can be overridden through
			 * predict.properties
			 */

			if (SwtUtil.isEmptyOrNull(keyValue)) {
				keyValue = defaultPredictProp.getProperty(keyName,
						(String) defaultValues.get(keyName));
			}
			log.debug(this.getClass().getName()
					+ "-[getPropertiesValue()]-Exit");
			return keyValue;
		} catch (Exception e) {
			log.error("Exception got in " + this.getClass().getName()
					+ "-[getPropertiesValue()]-" + e.getMessage());
			return null;
		}

	}

	/**
	 * @param keyName
	 * @return
	 */
	public String getPropertiesLicenseValue(String keyName) {
		return licenseProp.getProperty(keyName);
	}

	/**
	 * @returns swiftproperties values
	 */
	public Collection getSwiftMessageValues() {
		return swiftProp.values();
	}

	/**
	 * Method to get Facility Property
	 * 
	 * @param keyName
	 * @return
	 * 
	 */
	public String getFacilityPropertiesValue(String keyName) {
		String keyValue = null;
		if (SwtUtil.isEmptyOrNull(keyValue)) {
			keyValue = facilityProp.getProperty(keyName, (String) defaultValues
					.get(keyName));
		}
		return keyValue;
	}

	/**
	 * Method to get Facility Collection
	 * 
	 * @return
	 */
	public Collection<Object> getFacilityMessageValues() {
		log.debug(this.getClass().getName()
						+ "-[getFacilityMessageValues()]- Read  nwd_facility.properties");
		return facilityProp.values();
	}


	/**
	 * Return an ldap property by keyName
	 * @param keyName
	 * @return
	 */
	public String getLdapValue(String keyName) {
		return ldapProp.getProperty(keyName);
	}

	/**
	 * Returns full list of ldap properties
	 * @return
	 */
	public Collection getLdapValues() {
		return ldapProp.values();
	}
	
	/**
	 * @param keyName
	 * @return
	 */
	public String getPropertiesVersionValue(String keyName) {
		return versionProp.getProperty(keyName);
	}

	/**
	 * 
	 * @param keyName
	 * @return
	 */
	public String getXssFilterValue(String keyName){
		return xssFilterProp.getProperty(keyName);
	}
	/**
	 * 
	 * @return full list of xss filter properties
	 */
	public Collection getXssFilterValues(){
		  Collection<String> filteredValues = new ArrayList();

	        // Define your filter prefix
	        String filterPrefix = "filter.";

	        // Get all keys from the Properties object
	        Set<String> keys = xssFilterProp.stringPropertyNames();

	        // Iterate through the keys and filter based on the prefix
	        for (String key : keys) {
	            if (key.startsWith(filterPrefix)) {
	                // Filtered key found, retrieve its value
	                String value = xssFilterProp.getProperty(key);
	                filteredValues.add(value);
	            }
	        }

	        return filteredValues;
   }
	/**
	 * 
	 * @param keyName
	 * @return
	 */
	public String getMailPropertyValue(String keyName){
		return mailProp.getProperty(keyName);
	}
	/**
	 * 
	 * @return full list of mailProp properties
	 */
	public Collection getMailPropertiesValues(){
		return mailProp.values();
	}


	
	

}