/*
 * @(#)PageDetails.java
 * 
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 * 
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.util;

import java.util.HashMap;
import java.util.Map;

/**i
 * 
 * This util class contains the general page details required for pagination  
 * 
 */
public class PageDetails {

	
	private int pageValue;

	private int currentPageNo;

	private int maxPages;

	private Map pageDetails;
	//START: added property to display the total records on the screen on 30-SEP-2010 by Aliveni
	private int totalCount;
	//END: added property to display the total records on the screen on 30-SEP-2010 by Aliveni

	public int getCurrentPageNo() {
		return currentPageNo;
	}

	public void setCurrentPageNo(int currentPageNo) {
		this.currentPageNo = currentPageNo;
	}

	public int getMaxPages() {
		return maxPages;
	}

	public void setMaxPages(int maxPages) {
		this.maxPages = maxPages;
	}

	public int getPageValue() {
		return pageValue;
	}

	public void setPageValue(int pageValue) {
		this.pageValue = pageValue;
	}

	public Map getPageDetails() {
		pageDetails = new HashMap();
		pageDetails.put("pageNoValue", Integer.toString(pageValue));
		pageDetails.put("currentPage", Integer.toString(currentPageNo));
		pageDetails.put("maxPages", Integer.toString(maxPages));
		pageDetails.put("totalCount", Integer.toString(totalCount));
		return pageDetails;
	}

	public void setPageDetails(Map pageDetails) {
		this.pageDetails = pageDetails;
	}

	/**
	 * @return the totalCount
	 */
	public int getTotalCount() {
		return totalCount;
	}

	/**
	 * @param totalCount the totalCount to set
	 */
	public void setTotalCount(int totalCount) {
		this.totalCount = totalCount;
	}
	
} // End of class PageDetails