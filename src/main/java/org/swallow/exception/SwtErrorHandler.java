/*
 * Created on Dec 20, 2005
 *
 * TODO To change the template for this generated file go to
 * Window - Preferences - Java - Code Style - Code Templates
 */
package org.swallow.exception;

import javax.persistence.PersistenceException;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.dao.DataAccessException;
import org.springframework.dao.DataAccessResourceFailureException;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.dao.DataRetrievalFailureException;
import org.springframework.dao.InvalidDataAccessApiUsageException;
import org.springframework.dao.InvalidDataAccessResourceUsageException;
import org.swallow.util.SwtUtil;


/**
 * <AUTHOR>
 *
 * TODO To change the template for this generated type comment go to
 * Window - Preferences - Java - Code Style - Code Templates
 * 
 * @Modified by <PERSON><PERSON> Chebka for Mantis 2269:[Internal]: Enhancing predict's logging
 */
public class SwtErrorHandler {
    static SwtErrorHandler swtErrorHandler;
    private final Log log = LogFactory.getLog(SwtErrorHandler.class);
    // This is the filter message to be ignored when logging to console
    public static String SEVERE = "SEVERE";
    // The Jboss7 actual console log handler: please see <jboss-home>/standalone/configuration/logging.properties
    public static String CONSOLE_HANDLER_CLASSNAME = "org.jboss.logmanager.handlers.ConsoleHandler"; 
    public static String FILE_HANDLER_CLASSNAME = "org.jboss.logmanager.handlers.PeriodicRotatingFileHandler";
    // This is the filter message to be ignored when logging to console
    public static String STACK_TRACE = "[STACK_TRACE]";
    public static String DATA_VALIDATION_ERROR = "errors.DataIntegrityViolationExceptioninAdd";
    public static String JOB_SAME_LOCATION_ERROR = "errors.CouldNotSaveJobReportWithSameLocationPrefixExceptioninAdd";
    public static String JOB_SAME_NAME_ERROR = "errors.CouldNotSaveJobReportWithSameNameExceptioninAdd";
    public static String DATA_VALIDATION_DELETE_ERROR = "errors.DataIntegrityViolationExceptioninDelete";
    public static String CHILD_RECORD_FOUND_ERROR = "ORA-02292";
    public static String COULD_NOT_SYNCHRONIZE_ERROR = "Could not synchronize database state with session";
    public static String VALIDATORRESOURCES_ERROR = "not found for locale '";
    public static String FACES_CONFIG_CONFIGURELISTENER_ERROR = "UT015005";
    
    
    public static SwtErrorHandler getInstance() {
        if (swtErrorHandler == null) {
            swtErrorHandler = new SwtErrorHandler();
        }
        return swtErrorHandler;
    }

    /**
     * This is called in Exception Block of service layer to wrap exception in SwtException.
     * @param Exception
     * @param String
     * @param Class
     * @return a Result of type SwtException
     * <AUTHOR> Kumar has re-written this method on 22th Aug 07
     */
    public synchronized SwtException handleException(Throwable e,
        String methodName, Class className) {
    	
   	 if (e instanceof PersistenceException) {
		 PersistenceException pExp = (PersistenceException) e;
		 if(pExp.getMessage() != null  &&
				 pExp.getMessage().indexOf("ConstraintViolationException") > -1) {
			 return new SwtException("errors.DataIntegrityViolationExceptioninDelete");
		 }					  

	 }
    	// Log into File only by specifying the message as $stackTrace , this will be filtered by the console handler, Mantis 2269
    	if(e == null || (!SwtUtil.isEmptyOrNull(e.getMessage()) && !(e.getMessage().indexOf("DataIntegrityViolation")>-1) && !e.getMessage().equals(JOB_SAME_LOCATION_ERROR) && !e.getMessage().equals(JOB_SAME_NAME_ERROR)))  {
    		boolean isDataIntegrityException = false;
    		try {
	    		if(e.getCause().getLocalizedMessage() != null && e.getCause().getLocalizedMessage().indexOf("ORA-")>-1) {
	    		    if (e.getCause().getLocalizedMessage().indexOf("ORA-02292") > -1 || (e.getCause().getLocalizedMessage().indexOf("ORA-00001") > -1)) {
	    		    	isDataIntegrityException = true;
	    		    }
	    		}
    		}catch(Exception internalError) {
    			
    		}
    		if(!isDataIntegrityException)
    			log.error(STACK_TRACE,e);
    	}
    	    	
    	/* For handling the situation when we pass swtexception to ExceptionHandler */
    	if(e instanceof SwtException){
    		return (SwtException)e;
    	}
        String errorMsg = "";
        StringBuffer errMsg = new StringBuffer(className.getName().substring(className.getName()
                                                                                      .lastIndexOf(".") +
                    1));
        errMsg.append("-").append(e.getMessage());

        if (errMsg.length() > 100) {
            errorMsg = errMsg.substring(0, 99);
        } else {
            errorMsg = errMsg.toString();
        }

        Class superClass = e.getClass().getSuperclass();
        String erroId = "";
        String erroDesc = "";
        String codeLocation = null;

        if (e.getCause() != null) {
            codeLocation = SwtUtil.getCodeLocation(e.getCause());
        } else {
            codeLocation = SwtUtil.getCodeLocation(e);
            erroDesc = e + "";
        }

        if ((superClass != null) &&
                (superClass.getName().equals("org.springframework.dao.DataAccessException") || (e.getCause() != null && e.getCause().getLocalizedMessage() != null && e.getCause().getLocalizedMessage().indexOf("ORA-")>-1))) {
            String localStr = e.getCause().getLocalizedMessage();
            int oraIndex = localStr.indexOf("ORA-");
            int colIndex = localStr.indexOf(":", oraIndex);

            if ((oraIndex >= 0) && (colIndex >= 0) && (colIndex > oraIndex)) {
                erroId = localStr.substring(oraIndex, colIndex);
                erroDesc = localStr.substring(colIndex + 1);

                if (erroId != null) {
                    erroId = erroId.trim();

                    if (erroId.length() > 15) {
                        erroId = erroId.substring(0, 15);
                    }
                }

                if (erroDesc != null) {
                    erroDesc = erroDesc.trim();

                    if (erroDesc.length() > 100) {
                        erroDesc = erroDesc.substring(0, 100);
                    }
                }
            }
        } else {
            erroDesc = "" + e;
            return new SwtException("errors.javaRuntimeException", "Y", erroId,
                erroDesc, codeLocation);
        }

        if (e instanceof DataAccessResourceFailureException) {
            return new SwtException("errors.DataAccessResourceFailureException",
                "Y", erroId, erroDesc, codeLocation);
        } else if (e instanceof DataIntegrityViolationException) {
            String errorMessage = e.getMessage();
            String errorCode = "";

            if (errorMessage.indexOf("ORA-02292") > 0) {
                errorCode = "errors.DataIntegrityViolationExceptioninDelete";
            } else if (errorMessage.indexOf("ORA-00001") > 0) {
                errorCode = "errors.DataIntegrityViolationExceptioninAdd";
            } else {
                errorCode = "errors.DataIntegrityViolationException";
            }

            return new SwtException(errorCode, "Y", erroId, erroDesc,
                codeLocation);
        } else if (e instanceof DataRetrievalFailureException) {
            return new SwtException("errors.DataRetrievalFailureException",
                "Y", erroId, erroDesc, codeLocation);
        } else if (e instanceof InvalidDataAccessApiUsageException) {
            return new SwtException("errors.InvalidDataAccessApiUsageException",
                "Y", erroId, erroDesc, codeLocation);
        } else if (e instanceof InvalidDataAccessResourceUsageException) {
            return new SwtException("errors.InvalidDataAccessResourceUsageException",
                "Y", erroId, erroDesc, codeLocation);
        } else if (e instanceof DataAccessException) {
            return new SwtException("errors.DataAccessException", "Y", erroId,
                erroDesc, codeLocation);
        } else if (e instanceof SwtRecordNotExist) {
            return new SwtException("errors.SwtRecordNotExist", "Y", erroId,
                erroDesc, codeLocation);
        } else {
        	
            String errorMessage = "";
            if(e.getCause() != null && e.getCause().getLocalizedMessage() != null)
            	errorMessage =  e.getCause().getLocalizedMessage();
            
            String errorCode = "";
            if(errorMessage.indexOf("ORA-")>-1) {
	            if (errorMessage.indexOf("ORA-02292") > -1) {
	                errorCode = "errors.DataIntegrityViolationExceptioninDelete";
	            } else if (errorMessage.indexOf("ORA-00001") > -1) {
	                errorCode = "errors.DataIntegrityViolationExceptioninAdd";
	            }
	            return new SwtException(errorCode, "Y", erroId, erroDesc,
	                codeLocation);
            
            }else {
	            return new SwtException("errors.databaseexp", "Y", erroId,
	                erroDesc, codeLocation);
            }
        }
    }
}
