package org.swallow.cluster.cmd;

import org.swallow.util.pcm.SwtPCDataSource;
import org.swallow.util.SessionManager;
import org.swallow.util.SwtDataSource;
import org.swallow.util.SwtUtil;
import org.wildfly.clustering.dispatcher.Command;
/**
 * Wildfly cluster commands executor across the nodes
 * 
 * We execute in the current node and return result to the node that dispatched the command call
 * 
 * <AUTHOR> 2022
 *
 */
public class ClusterCommandExecutor implements Command<CommandModel, LocalContext> {
	private static final long serialVersionUID = 1L;
	private final CommandModel command;

    public ClusterCommandExecutor(CommandModel command) {
        this.command = command;
    }

    @Override
    public CommandModel execute(LocalContext context) {
//    	System.err.println(String.format("Received message: %s", command));
    	String moduleId = null;
    	String hostId = null;
		String userId = null;
		String killedBy = null;
		String connectionId = null;
		String connectionIds = null;
		String sessionId = null, message = null;
		switch (command.commandName) {
		case NEW_SESSION:
			command.setExecutedBy(context.getLocalMember()).setResult("Welcome to sessionId: "+command.parameters[0]+" connected at '"+command.parameters[1]+"' from Node: "+context.getLocalMember().getName());
			
			break;
		case LIST_USERS:
			command.setExecutedBy(context.getLocalMember()).setResult(SessionManager.getInstance().getLoggedUserStatusList());
			
			break;
			
		case LOGGED_LIST_USERS:
			command.setExecutedBy(context.getLocalMember()).setResult(SessionManager.getInstance().getLoggedUserList());
			
			break;
		case SESSION_MAP:
			command.setExecutedBy(context.getLocalMember()).setResult(SessionManager.getInstance().getSessionMap());
			
			break;
		case KILL_USER:
			
			if(command.parameters != null) {
				if(command.parameters.length == 3) {
					hostId = String.valueOf(command.parameters[0]);
					userId = String.valueOf(command.parameters[1]);
					killedBy = String.valueOf(command.parameters[2]);
					
				}
			}
			command.setExecutedBy(context.getLocalMember()).setResult(SessionManager.getInstance().killSession(hostId, userId, killedBy));
			
			break;
		case LIST_CONNECTION_POOL:
			
			if(command.parameters != null) {
				if(command.parameters.length == 1) {
					moduleId = String.valueOf(command.parameters[0]);
				}
				if(!SwtUtil.isEmptyOrNull(moduleId)){
					if (moduleId.equals("PREDICT")) {
						command.setExecutedBy(context.getLocalMember()).setResult(((SwtDataSource) SwtDataSource.getInstance()).getLastActiveConnections());
					} else if (moduleId.equalsIgnoreCase("PCM")){
						command.setExecutedBy(context.getLocalMember()).setResult(((SwtPCDataSource) SwtPCDataSource.getInstance()).getLastActiveConnections());

					}
				}
			}
			break;
		case LAST_CONNECTION_POOL_STATS:
			
			moduleId = null;
			if(command.parameters != null) {
				if(command.parameters.length == 1) {
					moduleId = String.valueOf(command.parameters[0]);
				}
				if(!SwtUtil.isEmptyOrNull(moduleId)){
					if (moduleId.equals("PREDICT")) {
						command.setExecutedBy(context.getLocalMember()).setResult(((SwtDataSource) SwtDataSource.getInstance()).getLastConnectionPoolStats());
					} else if (moduleId.equalsIgnoreCase("PCM")){
						command.setExecutedBy(context.getLocalMember()).setResult(((SwtPCDataSource) SwtPCDataSource.getInstance()).getLastConnectionPoolStats());

					}
				}
			}
			break;
		case KILL_DB_CONNECTION:
			
			
			if(command.parameters != null) {
				if(command.parameters.length == 2) {
					moduleId = String.valueOf(command.parameters[0]);
					connectionIds = String.valueOf(command.parameters[1]);
					
				}
			}
			if (moduleId.equalsIgnoreCase("PREDICT")) {
				command.setExecutedBy(context.getLocalMember()).setResult(((SwtDataSource) SwtDataSource.getInstance()).closeConnections(connectionIds));
			}else {
				command.setExecutedBy(context.getLocalMember()).setResult(((SwtPCDataSource) SwtPCDataSource.getInstance()).closeConnections(connectionIds));
			}
			break;
		case GET_DB_CONNECTION:
			if(command.parameters != null) {
				if(command.parameters.length == 2) {
					moduleId = String.valueOf(command.parameters[0]);
					connectionId = String.valueOf(command.parameters[1]);
					
				}
			}
			if (moduleId.equalsIgnoreCase("PREDICT")) {
				command.setExecutedBy(context.getLocalMember()).setResult(((SwtDataSource) SwtDataSource.getInstance()).getConnectionPoolById(connectionId));
			}else {
				command.setExecutedBy(context.getLocalMember()).setResult(((SwtPCDataSource) SwtPCDataSource.getInstance()).getConnectionPoolById(connectionId));
			}
			
			
			break;	
		case SEND_INTERNAL_MESSAGE:
			if(command.parameters != null) {
				if(command.parameters.length == 2) {
					sessionId = String.valueOf(command.parameters[0]);
					message = String.valueOf(command.parameters[1]);
					
				}
			}
			if (sessionId != null && message != null && message.trim().length() > 0) {
				command.setExecutedBy(context.getLocalMember()).setResult(SessionManager.getInstance().sendMessage(sessionId, message));
			}
			break;
		default:
			command.setExecutedBy(context.getLocalMember()).setResult(context.getLocalMember().getName() + " : Unsupported Command ! "+command.commandName.getValue());

			break;
		}
		
		
        return command;
    }
}