package org.swallow.cluster.cmd;

import java.io.Serializable;
import java.util.Arrays;
import org.wildfly.clustering.group.Node;

/**
 * Serializable object that represents a command
 * 
 * <AUTHOR> 2022
 *
 */
public class CommandModel implements Serializable{
	private static final long serialVersionUID = 1L;

	CommandNames commandName;
	
	Object[] parameters = new Object[] {};
	
	Node executedBy = null;
	
	Object result = null;
	
	/**
	 * Creates a command object
	 * @param commandName
	 * @param params
	 * @return
	 */
	public static CommandModel of(CommandNames commandName, Object...params) {
		CommandModel command = new CommandModel();
		command.commandName = commandName;
		command.parameters = params;
		return command;
	}
	
	/**
	 * Pushes a result to the command
	 * @param result
	 * @return
	 */
	public CommandModel setResult(Object result){
		this.result = result;
		return this;
	}

	/**
	 * @return the result
	 */
	public Object getResult() {
		return result;
	}

	
	
	/**
	 * @return the executedBy
	 */
	public Node getExecutedBy() {
		return executedBy;
	}

	/**
	 * @param executedBy the executedBy to set
	 */
	public CommandModel setExecutedBy(Node executedBy) {
		this.executedBy = executedBy;
		return this;
	}

	@Override
	public String toString() {
		return "CommandModel [commandName=" + commandName + ", parameters=" + Arrays.toString(parameters)
				+ ", executedBy=" + executedBy + ", result=" + result + "]";
	}

	
}
