package org.swallow.cluster;


import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.apache.curator.framework.CuratorFramework;
import org.apache.curator.framework.recipes.cache.TreeCacheEvent;
import org.apache.curator.framework.recipes.cache.TreeCacheListener;
import org.apache.curator.utils.ZKPaths;

/**
 * Tree cache listener that listens to node events
 * <AUTHOR>
 *
 */
public class NodeListener implements TreeCacheListener {
	
	private Log log = LogFactory.getLog(this.getClass());

	@Override
	public void childEvent(CuratorFramework client, TreeCacheEvent event)
			throws Exception {
		switch (event.getType()) {
		case NODE_ADDED: {
			log.warn("[THIS IS NOT A REAL ERROR]:TreeNode added: "
					+ ZKPaths.getNodeFromPath(event.getData().getPath()));
			break;
		}
		case NODE_UPDATED: {
			log.warn("[THIS IS NOT A REAL ERROR]:TreeNode changed: "
					+ ZKPaths.getNodeFromPath(event.getData().getPath()));
			break;
		}
		case NODE_REMOVED: {
			log.warn("[THIS IS NOT A REAL ERROR]:TreeNode removed: "
					+ ZKPaths.getNodeFromPath(event.getData().getPath()));
			break;
		}
		default:
			log.warn("[THIS IS NOT A REAL ERROR]:Other event: " + event.getType().name());
		}
	}
}