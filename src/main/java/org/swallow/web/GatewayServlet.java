package org.swallow.web;

import java.io.IOException;
import java.util.Hashtable;

import javax.servlet.RequestDispatcher;
import javax.servlet.ServletContext;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;

import org.swallow.util.CommonDataManager;
import org.swallow.util.SwtConstants;
import org.swallow.util.SwtUtil;



/**
 * This Servlet has been introduced for integration of Input System with Predict System. Whenever We need to open 
 * any Input Screen from Predict System, we just redirect the request to this servlet along with the parameter
 * 'screenName' which decides the URL of Input System.
 * <AUTHOR> Tripathi
 *
 */
public class GatewayServlet extends HttpServlet{
	
	/**
	 * This is the method to handle the predict request redirecting it to Input URL.
	 * We prepare the URL based on the screenName parameter and redirect the request to that URL.
	 */
	public void doGet (HttpServletRequest req, HttpServletResponse res)
	throws ServletException, IOException{
		
		ServletContext inputArchiveServletContext	=  getServletContext().getContext("/InputArchive");
		RequestDispatcher reqdisp = null;
		String screenName = req.getParameter("screenName");
		/* If User is log-in only give access to the shared data( Coz In absence of this data Input URL will not work) */
		if(checkLogin(req)){
			storeDataInContext(inputArchiveServletContext,req);
		}
		
		if(screenName.equals("inputIntegrate")){
			/* START : Code has been modified as per Shyamal's mail on 12/11/2006 */
			/*reqdisp = inputArchiveServletContext
	        		.getRequestDispatcher("/Archive.do?archiveView=true");*/
			reqdisp = inputArchiveServletContext
    				.getRequestDispatcher("/AuditSelection.do");
			/* END : Code has been modified as per Shyamal's mail on 12/11/2006 */
		}
		if(screenName.equals("fromWorkflowMonitor")){
			StringBuffer inputURL = new StringBuffer("");
			inputURL.append("/AuditView.do?frm_status=4&CreateMap=true");
			inputURL.append("&user=");
			inputURL.append(req.getAttribute("userId"));
			inputURL.append("&ReceiveDateFrom=");
			inputURL.append(req.getParameter("startDate"));
			inputURL.append("&ReceiveDateTo=");
			inputURL.append(req.getParameter("startDate"));
			reqdisp = inputArchiveServletContext
	        								.getRequestDispatcher(inputURL.toString());
		}
		if(screenName.equals("messageView")){
			String messageId=req.getParameter("messageId");
			reqdisp = inputArchiveServletContext
	        		.getRequestDispatcher("/messageDisplay.do?Message_Id="+messageId);
		}
		if(screenName.equals("inputJobSummary")){
			reqdisp = inputArchiveServletContext
	        								.getRequestDispatcher("/getJobSummary.do");
		}
		if(screenName.equals("inputJobEnableOrDisable")){
			StringBuffer inputURL = new StringBuffer("");
			inputURL.append("/executeJob");
			inputURL.append("?selectedJobId=");
			inputURL.append(req.getAttribute("selectedJobId"));
			inputURL.append("&action=");
			inputURL.append(req.getAttribute("action"));
			inputURL.append("&user=");
			inputURL.append(req.getAttribute("userId"));
			reqdisp = inputArchiveServletContext
	        								.getRequestDispatcher(inputURL.toString());
		}
			
		reqdisp.forward(req,res);
	}

	/**
	 * This method is used to store the shared data into session in form of a hashtable.
	 * Input system has to check the integrity of this data before responding to the request.
	 * @param inputArchiveServletContext
	 * @param request
	 */
	private synchronized void storeDataInContext(ServletContext inputArchiveServletContext,HttpServletRequest request)
	{
		System.out.println("entering storeDataInContext method");
		
		HttpSession httpsession = request.getSession();
		Hashtable hashtable = (Hashtable)inputArchiveServletContext.getAttribute("shared_data");
		
		if(hashtable == null){
			hashtable=new Hashtable();
			long sessionTimeOutPeriod = SwtUtil.getSessionTimeOutPeriod()*60*1000;
			//Putting the elements to be shared in the hashtable
			hashtable.put("SESSION", httpsession);
			hashtable.put(SwtConstants.SESSION_TIMEOUT_PERIOD, new Long(sessionTimeOutPeriod));
			inputArchiveServletContext.setAttribute("shared_data", hashtable);
		}
	}
	
	
	/**
	 * This method is a part of Srvlet Life cycle.
	 */
	public void doPost (HttpServletRequest req, HttpServletResponse res)
	throws ServletException, IOException
    {
		doGet(req,res);
    }
	
	/**
	 * This method checks for the userlogin. If user is already loggedin it will return true otherwise false.
	 * @param req
	 * @return
	 */
	private boolean checkLogin(HttpServletRequest req){
		boolean loginFlag = false;
		HttpSession session = req.getSession();
		if(session != null){
			CommonDataManager cdm = (CommonDataManager)(session.getAttribute(SwtConstants.CDM_BEAN));
			loginFlag = cdm != null ? true : loginFlag;
		}
		return loginFlag;
	}

}