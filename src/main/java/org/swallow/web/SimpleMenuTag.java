/*
 * Created on Dec 14, 2005
 *
 * TODO To change the template for this generated file go to
 * Window - Preferences - Java - Code Style - Code Templates
 */
package org.swallow.web;

/**
 * Simple menu tag used to test with Selenium IDE
 * 
 * <AUTHOR> 2024
 */


import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.jsp.JspException;
import javax.servlet.jsp.JspWriter;
import javax.servlet.jsp.tagext.TagSupport;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.swallow.model.MenuAccess;
import org.swallow.model.MenuItem;
import org.swallow.model.User;
import org.swallow.service.LogonManager;
import org.swallow.util.SwtConstants;
import org.swallow.util.SwtUtil;

public class SimpleMenuTag extends TagSupport {

	/** the user id of the current user */
	private String roleId;
	private final Log log = LogFactory.getLog(SimpleMenuTag.class);
	
	private LogonManager logonManager = null;
	
	private static HashMap<String, String> excludeList  = new HashMap<String, String>() ;
	static {
		excludeList.put(SwtConstants.MENU_ITEM_ALERT_SUMMARY_DISPLAY, "Alert Instance Summary");
		
		
	}

	public void setLogonManager(LogonManager logonManager)
	{
	     this.logonManager = logonManager;
	}

	/**
	 * Performs the processing of this tag.
	 */
	public int doStartTag() throws JspException {
	JspWriter out =  pageContext.getOut();
	 try {
		  writeHTMLString();
	 } catch (IOException ioe) {
	 	log.debug("IOException in doStartTag "+ioe.getMessage());
	 	log.error("Error : "+ioe);
		//throw new JspException(ioe.getMessage());
	 }catch(Exception ex){
	 	log.error("Error : "+ex);
	 	log.debug("Exception in doStartTag "+ex.getMessage());
	  	//throw new JspException(ex.getMessage());
	 }
	  return SKIP_BODY;
	}

	private void writeHTMLString() throws Exception{
		StringBuffer html = new StringBuffer();
		MenuItem menuItem = null;
		MenuAccess menuAccess = null;
		ArrayList subMenuList = null;
		log.debug("Into writeHTMLString()");
		if(logonManager == null) {
		    logonManager = SwtUtil.getBean(LogonManager.class);
		}
		User user = SwtUtil.getCurrentUser(pageContext.getSession());
        ArrayList menuList = new ArrayList<>(logonManager.getMenuList(user));
        log.debug("Into writeHTMLString() menu list "+menuList);

		int size=0;

		if(menuList != null){
			size = menuList.size();
		}

		html.append("<div class=\"navbar\"><div class=\"row\">\n");
		  
		for (int i=0; i<size ; i++){
            menuItem = (MenuItem)menuList.get(i);
            //menuItem = menuAccess.getMenuItem();
            String menuLabel = SwtUtil.getMessage("menulabel.itemid." + menuItem.getItemId(), (HttpServletRequest)pageContext.getRequest());
            if(SwtUtil.isEmptyOrNull(menuLabel)) {
                menuLabel = menuItem.getDescription(); 
            }


            //<img src="path/to/some/image" style="float:right;" /
            html.append("<div class=\"column\">"); 
            //html.append("<img src=\"image=images/" + menuLabel + ".gif;overimage=images/" + menuLabel + "_R.gif;/>");
            html.append("<h3>"+menuLabel+"</h3>");
            html.append(writeSubMenuList(menuItem));
            html.append("</div>");
        }

		html.append("</div></div>");
		
		
		pageContext.getOut().write(html.toString());

	}
	
	private String writeSubMenuList(MenuItem menuItem) {
        StringBuffer html = new StringBuffer();
        try{
            if(menuItem.isParent()){
                ArrayList menuList = (ArrayList)menuItem.getSubMenuList();
                MenuItem item = null;
                int size = menuList.size();
                int prevGrpOrder = 1;

                for(int i=0; i<size; i++){
                    item = (MenuItem) menuList.get(i);
                    if(excludeList.containsKey(item.getItemId()))
                        continue;

                    String menuLabel = SwtUtil.getMessage("menulabel.itemid." + item.getItemId(), (HttpServletRequest)pageContext.getRequest());
                    if(SwtUtil.isEmptyOrNull(menuLabel)) 
                    {
                        menuLabel = item.getDescription(); 
                    }
                    
                    
                    String jsMethod = "";
    
                    if(item.isParent()){
                        html.append("<br/><br/>");
                        menuLabel += ":";
                    }
                    else if(item.getWidth().equals(new Integer(0))){

                    }else{
                       /* START: Code changed as par ING requirements to the Roles - Menu Access, 13-JUN-2007 */
                       if (item.getProgramName() != null && item.getProgramName().contains("?")) {
                           jsMethod = " openWindow('"+item.getProgramName()+ "&menuAccessId=" +item.getAccessId() +"&menuItemId=" +item.getItemId()+ "&ismenuItem=true"+"',";
                       } else {
                           jsMethod = " openWindow('"+item.getProgramName()+ "?menuAccessId=" +item.getAccessId() +"&menuItemId=" +item.getItemId()+"&ismenuItem=true"+ "',";
                       }
                       jsMethod +="'"+item.getItemId()+"Window',";
                       jsMethod +="'width="+item.getWidth();
                       jsMethod +=",height="+item.getHeight();
                       jsMethod +=",toolbar=0, status=yes,resizable=yes, scrollbars=yes','true','"+item.getItemId()+"');\");";
                    }
                    
                    html.append("<a href='#' onclick=\""+jsMethod+"\">"+menuLabel+" </a>");
                    html.append("&nbsp;&nbsp;&nbsp;");
                    
                    if(item.isParent()){
                        html.append("<br/>&nbsp;&nbsp;&nbsp;");
                        html.append(writeSubMenuList(item));
                    }
                }

                
                //log.debug("written from writeSubMenuList  "+html);
                /*for(int i=0; i<size; i++){
                    item = (MenuItem) menuList.get(i);
                    if(item.isParent()){
                        html.append(writeSubMenuList(item));
                    }
                }*/
                
            }
            return html.toString();
        }catch(Exception e){
            System.err.println("================>>>>>> writeSubMenuList: ERROR: "+e.getMessage());
            e.printStackTrace();
            return "ERROR: "+e.getMessage();
        }	    
	}

}
