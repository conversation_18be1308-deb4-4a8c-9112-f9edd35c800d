/*
 * Created on Jan 27, 2006
 *
 * TODO To change the template for this generated file go to
 * Window - Preferences - Java - Code Style - Code Templates
 */
package org.swallow.web;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Date;
import java.util.Enumeration;
import java.util.Hashtable;
import java.util.Locale;

import javax.servlet.http.HttpServletRequest;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.swallow.control.model.UserStatus;
import org.swallow.control.service.UserStatusManager;
import org.swallow.exception.SwtException;
import org.swallow.maintenance.model.MiscParams;
import org.swallow.maintenance.model.SysParams;
import org.swallow.maintenance.service.SysParamsManager;
import org.swallow.model.User;
import org.swallow.model.UserProfileDetail;
import org.swallow.service.LogonManager;
import org.swallow.util.CacheManager;
import org.swallow.util.CommonDataManager;
import org.swallow.util.Globals;
import org.swallow.util.SwtConstants;
import org.swallow.util.SwtUtil;
import org.swallow.util.SystemFormats;

import com.google.gson.Gson;

/**
 * <AUTHOR>
 *
 * TODO To change the template for this generated type comment go to
 * Window - Preferences - Java - Code Style - Code Templates
 */
public class LogonBean {
	private final Log log = LogFactory.getLog(LogonBean.class);
	public void afterLogonProcess(HttpServletRequest request, User user, LogonManager mgr)
	{
		UserStatus userStatus = null;
		try{
			CommonDataManager CDM = (CommonDataManager)request.getSession().getAttribute(SwtConstants.CDM_BEAN);
			userStatus  = ((UserStatusManager)SwtUtil.getBean("userStatusManager")).getUserStatusRecord(CacheManager.getInstance().getHostId(), user.getId().getUserId());
			
			if(userStatus == null) {
				userStatus = new UserStatus();
				userStatus.getId().setHostId(CacheManager.getInstance().getHostId());
				/* Start: Code Chnaged by Mayank for Making User Login and Add User working */
				Date date = SwtUtil.getTestDateFromParams(userStatus.getId().getHostId());
				/* End: Code Chnaged by Mayank for Making User Login and Add User working */
				Timestamp tm = new Timestamp(date.getTime());
				
				//tm.setSeconds(0);
				tm.setNanos(0);
				userStatus.getId().setLogOnTime(tm);
							
				String month = (date.getMonth()<10) ? ("0"+date.getMonth()) : ""+date.getMonth();
				
				String dateAsString =(date.getDate()<10) ? ("0"+date.getDate()) : ""+date.getDate();
				
				String displayTime = dateAsString +"/"+month+"/"+ (date.getYear()+1900)+ " "+date.getHours()
									+":"+date.getMinutes()+":"+date.getSeconds();
				
				userStatus.setDisplayDateTime(displayTime);
				userStatus.getId().setUserId(user.getId().getUserId());
				userStatus.setUsers(user);
				userStatus.setIpAddress(request.getRemoteAddr());
		
				request.getSession().setAttribute("userstatus",userStatus);
				
				log.debug("userstatus>>" + userStatus);
				mgr.saveUserStatus(userStatus);
			}else {
				request.getSession().setAttribute("userstatus",userStatus);
			}
			
			CDM.setProfileList(mgr.getProfileList(user));
			log.debug("calling getMenuList 2 ");
			CDM.setShortcutList(mgr.getShortcutList(user));			
			CDM.setScreenList(mgr.getScreenList(user));	
			//Collection colEntity = mgr.getEntityAccessList(user.getRoleId(),CacheManager.getInstance().getHostId());
			//request.getSession().setAttribute(SwtConstants.USER_ENTITY_ACCESS_LIST, colEntity);			
			request.setAttribute("profileList",CDM.getProfileList());
			request.getSession().setAttribute("profileList",CDM.getProfileList());
			log.debug("shortcut list in CDM===>"+CDM.getShortcutList());
			request.setAttribute("shortcutList",CDM.getShortcutList());
			request.getSession().setAttribute("shortcutList",CDM.getShortcutList());
			ArrayList profilelList = (ArrayList)mgr.getUserProfileDetails(user);
			request.setAttribute("userProfileList",profilelList);
			request.getSession().setAttribute("userProfileList",profilelList);
			if(profilelList.size()>0){
				user.setProfileId(((UserProfileDetail)profilelList.get(0)).getId().getProfileId());
				log.debug("afterLogonProcess users defaul profile id : "+user.getProfileId());
				request.setAttribute("defaultProfile",user.getProfileId());
			}
			
			log.debug("Before Getting The MenuList Detaill");
			//request.setAttribute("menuList",mgr.getMenuList(user));
			request.getSession().setAttribute("menuList",mgr.getMenuList(user));
			log.debug("Got The MenuList Detaill");
			log.debug("Before Getting The Entity List>>" + user.getRoleId() + "<<>>" + CacheManager.getInstance().getHostId());
			//Collection colEntityAccessList = mgr.getEntityAccessList(user.getRoleId(), CacheManager.getInstance().getHostId());
			//log.debug("colEntityAccessListcolEntityAccessList" + colEntityAccessList);
			//request.getSession().setAttribute(SwtConstants.USER_ENTITY_ACCESS_LIST,colEntityAccessList);
			log.debug("After setting The Entity List in session request.getSession().getAttribute(SwtConstants.USER_ENTITY_ACCESS_LIST)" + request.getSession().getAttribute(SwtConstants.USER_ENTITY_ACCESS_LIST));
			/*Start: code Added/Modified by Arumugam on 03-Dec-2010 for Mantis:0001296- Misc Param to added the entityId field in table and check the entityId */
			String entityId=SwtUtil.getUserCurrentEntity(request.getSession());
			putGeneralFormatsInSession(CDM,entityId);
			/*End: code Added/Modified by Arumugam on 03-Dec-2010 for Mantis:0001296- Misc Param to added the entityId field in table and check the entityId */
			putCurrencyDecimalInRequest(request);
			//storeDataInContext(request);
			
			request.getSession().setAttribute("user_lang", user.getLanguage());
			 Gson gson = new Gson();
			request.getSession().setAttribute("dic_en", gson.toJson(SwtUtil.dictionary_en));
			request.getSession().setAttribute("dic_fr", gson.toJson(SwtUtil.dictionary_fr));

			if (user.getLanguage().equalsIgnoreCase("EN"))
				request.getSession().setAttribute(Globals.LOCALE_KEY, Locale.ENGLISH);
			else
				request.getSession().setAttribute(Globals.LOCALE_KEY, Locale.FRENCH);
			
		}catch(Exception e){
			log.error("Exception in Logon Process " + e, e);
		}finally{
			Date currentTime=new Date();
			request.getSession().setAttribute(SwtConstants.LAST_REQUEST_ACCESSED,currentTime);
		}
	}
	
	/*Start: code Added/Modified by Arumugam on 03-Dec-2010 for Mantis:0001296- Misc Param to added the entityId field in table and check the entityId */
	public void putGeneralFormatsInSession(CommonDataManager CDM,String entityId) throws SwtException{
		/*End: code Added/Modified by Arumugam on 03-Dec-2010 for Mantis:0001296- Misc Param to added the entityId field in table and check the entityId */
		SysParamsManager sysParamsManager =  (SysParamsManager)SwtUtil.getBean("sysParamsManager");
		SysParams sysParams = sysParamsManager.getSysParamsDetail(CDM.getUser().getId().getHostId());
		String dateFormat= null;
		String dateFormatValue= null;
		String currencyFormat= null;
		String currencyFormatValue= null;
	
		User user = CDM.getUser();
		if( user != null) {
			if(!SwtUtil.isEmptyOrNull(user.getDateFormat())) {
				dateFormat = user.getDateFormat();
				dateFormatValue = SwtConstants.DATE_PAT + user.getDateFormat();
			}

			if(!SwtUtil.isEmptyOrNull(user.getAmountDelimiter())) {
				currencyFormat= user.getAmountDelimiter();
				currencyFormatValue=SwtConstants.CURRENCY_PAT + user.getAmountDelimiter();
			}
		}

		if(dateFormat==null) {
			dateFormatValue = SwtConstants.DATE_PAT + sysParams.getDateFormat();
			dateFormat=sysParams.getDateFormat();
			user.setDateFormat(dateFormat);
		}
		
		if(currencyFormat== null) {
		  currencyFormat= sysParams.getAmountDelimiter();
		  currencyFormatValue=SwtConstants.CURRENCY_PAT+sysParams.getAmountDelimiter();
		  user.setAmountDelimiter(currencyFormat);
		}
		Date systemDate = sysParams.getTestDate();
		
		log.debug("systemDate - " + systemDate);
		if(systemDate == null)
			systemDate = new Date();
		
		log.debug("systemDate - " + systemDate);		
		
		SystemFormats systemFormats = new SystemFormats();
		systemFormats.setCurrencyFormat(currencyFormatValue);
		CDM.setSystemFormats(systemFormats);
		
		CDM.setSystemDate(systemDate);
		CDM.setDateFormat(dateFormatValue);
		/*Start: code Added/Modified by Arumugam on 03-Dec-2010 for Mantis:0001296- Misc Param to added the entityId field in table and check the entityId */
		ArrayList list = (ArrayList)CacheManager.getInstance().getMiscParams("DATEFORMAT",dateFormat,entityId);		
		MiscParams miscParams = null;
		if(list.size()>0){
			miscParams = (MiscParams)list.get(0);
			/*Start code modified by Betcy for mantis 1220 on 22-09-10 Change primary key of P_MISC_PARAMS*/
			CDM.setDateFormatValue(miscParams.getParValue());
			/*End code modified by Betcy for mantis 1220 on 22-09-10 Change primary key of P_MISC_PARAMS*/
		}
		
		log.debug("login set dateformat value list: "+CDM.getDateFormatValue());		
		list = (ArrayList)CacheManager.getInstance().getMiscParams("CURRENCYFORMAT",currencyFormat,entityId);
		/*End: code Added/Modified by Arumugam on 03-Dec-2010 for Mantis:0001296- Misc Param to added the entityId field in table and check the entityId */
		if(list.size()>0){
			miscParams = (MiscParams)list.get(0);
			/*Start code modified by Betcy for mantis 1220 on 22-09-10 Change primary key of P_MISC_PARAMS*/
			CDM.setCurrencyFormatValue(miscParams.getId().getKey2());
			/*End code modified by Betcy for mantis 1220 on 22-09-10 Change primary key of P_MISC_PARAMS*/
		}
		systemFormats.setDateFormatValue(CDM.getDateFormatValue());
		log.debug("login set currencyformat value list: "+CDM.getCurrencyFormatValue()); 
		log.debug("login set currencyformat in session: "+CDM.getCurrencyFormat());
		log.debug("login set dateformat in session: "+CDM.getDateFormat());
	}
	
	public void putCurrencyDecimalInRequest(HttpServletRequest request) throws SwtException{
		StringBuffer buf = new StringBuffer();
		Hashtable decimalTable = CacheManager.getInstance().getCurrencyDecimalHashTable();
		Enumeration enumElement = decimalTable.keys();
		String currCode = "";
		while(enumElement.hasMoreElements()){
			currCode = (String)enumElement.nextElement();
			buf.append("CurrencyDecimalMetaData."+ currCode +" = "+ decimalTable.get(currCode) +";\n");
		}
		log.debug("in putCurrencyDecimalInRequest got the decimal data "+ buf);
		request.setAttribute("CurrencyDecimalMetaData", buf.toString());
	}
    
}
