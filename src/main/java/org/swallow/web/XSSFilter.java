package org.swallow.web;

import java.io.IOException;
import java.net.URI;
import java.net.URISyntaxException;
import java.net.URLEncoder;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import javax.servlet.Filter;
import javax.servlet.FilterChain;
import javax.servlet.FilterConfig;
import javax.servlet.RequestDispatcher;
import javax.servlet.ServletContext;
import javax.servlet.ServletException;
import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.http.*;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Node;
import org.jsoup.select.Elements;
import org.swallow.dao.LogonDAO;
import org.swallow.model.MenuItem;
import org.swallow.util.CommonDataManager;
import org.swallow.util.PropertiesFileLoader;
import org.swallow.util.SwtConstants;
import org.swallow.util.SwtUtil;
import com.google.common.net.HttpHeaders;

public class XSSFilter implements Filter {
	private String fail = null;
	private String error = null;
	private String login = null;
	private FilterConfig config;
	private final Log log = LogFactory.getLog(XSSFilter.class);
	private Collection<String> xssFilterRegex = null;
	private ArrayList<Pattern> patternList = null;
	private String[]  notallowedTags ;
	static ArrayList<String> ajaxRequestsList = null;
	static ArrayList<String> urlsToExcludeList = null;

	public String[] ajaxUrlsArr = {
			"ilmTransScenario.do?method=testFilterCondtionClause",
			"archive.do?method=testConnection"
	};

	public static String[] urlsToExcludeArr = {
			"flexdataexport.do?method=",
			"ilmAnalysisMonitor.do?method=exportLineChartDetails",
			"checknewmsgs.do",
			"ilmAnalysisMonitor.do?method=getCurrentPorcessState",
			"scenarioAlerts.do",
			"interfaceinterruption.do"
	};

	/**
	 * Constructor
	 */
	public XSSFilter() {
		ajaxRequestsList = new ArrayList(Arrays.asList(ajaxUrlsArr));
		urlsToExcludeList = new ArrayList(Arrays.asList(urlsToExcludeArr));
	}

	/**
	 * On init
	 */
	public void init(FilterConfig filterConfig) throws ServletException {
		log.debug("entering 'init' method");

		config = filterConfig;
		initRoute();
		// load the "xss_filter" file to get the regex pattern
		xssFilterRegex = PropertiesFileLoader.getInstance().getXssFilterValues();

		// compile the regex pattern
		patternList = getCompiledRegex(xssFilterRegex);
		String notallowedTagsAsString = PropertiesFileLoader.getInstance().getXssFilterValue("notallowedTags");
		notallowedTags = notallowedTagsAsString.split(",");

		log.debug("exiting 'init' method");
	}

	/**
	 * Define routes for dispatching results
	 */
	public void initRoute() {
		ServletContext servletcontext = config.getServletContext();
		fail = config.getInitParameter("fail");
		error = config.getInitParameter("error");
		login = config.getInitParameter("login");
		if (fail == null) {
			log.warn("Request Filter:could not get an initial parameter fail");
		}
		if (error == null) {
			log.warn("Request Filter:could not get an initial parameter error");
		}
		if (login == null) {
			log.warn("Request Filter:could not get an initial parameter error");
		}
	}

	/**
	 * Destroy the filter
	 */
	public void destroy() {
		config = null;
	}

	/**
	 * Set the filter configs
	 * @param filterconfig
	 */
	public void setFilterConfig(FilterConfig filterconfig) {
		log.debug("entering 'setFilterConfig' method");
		config = filterconfig;
		initRoute();
		log.debug("exiting 'setFilterConfig' method");
	}


	private String extractOrigin(String originHeader) {
		try {
			URI uri = URI.create(originHeader);
			String scheme = uri.getScheme();
			String host = uri.getHost();
			int port = uri.getPort();
			if (port == -1) {
				port = "https".equalsIgnoreCase(scheme) ? 443 : 80;
			}

			return scheme + "://" + host + ((port == 80 && "http".equals(scheme)) || (port == 443 && "https".equals(scheme)) ? "" : ":" + port);
		} catch (Exception e) {
			return "invalid-origin";
		}
	}

	private Set<String> loadTrustedOrigins() {
		String originsCsv = PropertiesFileLoader.getInstance().getPropertiesValue(SwtConstants.ALLOWED_CORS_ORIGINS);
		if (originsCsv == null || originsCsv.trim().isEmpty()) {
			return Collections.emptySet(); // fallback to no restriction or handle it however you like
		}
		String[] parts = originsCsv.split(",");
		Set<String> result = new HashSet<>();
		for (String origin : parts) {
			result.add(origin.trim());
		}
		return result;
	}


	/**
	 * Main filter routine
	 */
	public void doFilter(ServletRequest servletrequest,
						 ServletResponse servletresponse, FilterChain filterchain)
			throws IOException, ServletException {
		log.debug("entering 'doFilter' method");
		CommonDataManager cdm = null;


		//Avoid CRLF Injection attacks (request side)
		HttpServletRequest httpservletrequest = (HttpServletRequest) servletrequest;

		Cookie[] cookies = ((HttpServletRequest)servletrequest).getCookies();
		ServletContext servletcontext = config.getServletContext();




		if (cookies != null) {
			for (Cookie cookie : cookies) {
				String cookieValue = cookie.getValue();
				int endIndex = cookieValue.indexOf("\r\n");
				if (endIndex != -1) {
					cookieValue = cookieValue.substring(0, endIndex);
				}
				cookie.setValue(cookieValue);
			}
		}

		if(httpservletrequest.isRequestedSessionIdValid()) {

			boolean isAjax  =  httpservletrequest.getParameter("isAjax")!=null && httpservletrequest.getParameter("isAjax").equals("true");
			boolean isForm  =  httpservletrequest.getParameter("isForm")!=null && httpservletrequest.getParameter("isForm").equals("true");
			boolean isLogonRequest = XSSFilter.getPathToFilter(httpservletrequest).startsWith("logon.do");
			boolean isLogoutRequest = XSSFilter.getPathToFilter(httpservletrequest).startsWith("logout.do");
			String csrfCompareResult = null;
			boolean allowFilter = false;
			cdm = ((CommonDataManager) httpservletrequest.getSession().getAttribute(
					SwtConstants.CDM_BEAN));
			try{
	        	/* Test against the CSRF attack which is made in 2 phases 
	        	1- check the CSRF token for AJAX and Forms
	        	2- Check referer if it's the same as the one he connected with
	        	*/
				if(!SwtUtil.isDeveloperMode()) {
					String filteredPath = getPathToFilter(httpservletrequest);
					// if an excluded path
					if (SwtUtil.isEmptyOrNull(filteredPath) || isExludedPath(filteredPath, httpservletrequest.getRequestURI())) {
						filterchain.doFilter(new XSSHttpServletRequestWrapper((HttpServletRequest)servletrequest), new XSSServletResponseWrapper((HttpServletResponse) servletresponse));
						return;
					}else  if (httpservletrequest.getMethod().equals("POST") && !httpservletrequest.getRequestURI().contains("invalidatedata.jsp") && !httpservletrequest.getRequestURI().equals(httpservletrequest.getContextPath()+"/error") && (isAjax || isForm) ) {

						// Check CSRF token if valid and stored in server for the selected user
						String csrfToken = null;
						if(isAjax) {
							csrfToken = httpservletrequest.getHeader("csrf");
						}else if(isForm) {
							csrfToken = httpservletrequest.getParameter("csrf");
						}



						if (csrfToken == null) {
							csrfCompareResult = SwtConstants.CSRF_TOKEN_EMTPTY;
						} else if ((cdm!=null || isLogonRequest) && !SwtUtil.checkToken(csrfToken, cdm, isLogonRequest)) {
							csrfCompareResult = SwtConstants.CSRF_TOKEN_WRONG;

						}

						if(SwtConstants.CSRF_TOKEN_EMTPTY.equals(csrfCompareResult)) {
							servletrequest.setAttribute("errordesc",
									SwtUtil.getMessage("errors.csrf.attack", httpservletrequest));
							log.error(SwtUtil.getMessage("errors.csrf.attack.log", httpservletrequest) + "\n" +
									  "{" + SwtUtil.getMessage("errors.user.log", httpservletrequest) + ": " + cdm.getUser().getId().getUserId() +
									  ", " + SwtUtil.getMessage("errors.ipAddress.log", httpservletrequest) + ": " + httpservletrequest.getRemoteAddr() +
									  ", " + SwtUtil.getMessage("errors.requestURI.log", httpservletrequest) + ": " + httpservletrequest.getRequestURI() + ", "+
									  SwtUtil.getMessage("errors.csrf.attack.emptyToken", httpservletrequest)+"}");
							servletrequest.setAttribute("errorCause", "");
							// If the request is Ajax, immediately return content as erred
							if (isAjax) {
								servletresponse.getWriter().print(SwtUtil.getMessage("errors.csrf.attack", httpservletrequest));
								return;
							} else {
								// In your CSRF filter or interceptor
								if (servletrequest.getAttribute("csrf.error.processed") != null) {
									// Skip CSRF check for error pages
									filterchain.doFilter(new XSSHttpServletRequestWrapper((HttpServletRequest)servletrequest), new XSSServletResponseWrapper((HttpServletResponse) servletresponse));
									return;
								}

								servletrequest.setAttribute("csrf.error.processed", true);
								RequestDispatcher requestdispatcher = servletcontext.getRequestDispatcher(error);
								requestdispatcher.forward(new XSSHttpServletRequestWrapper((HttpServletRequest)servletrequest), new XSSServletResponseWrapper((HttpServletResponse) servletresponse));
								return;
							}
						}else if(SwtConstants.CSRF_TOKEN_WRONG.equals(csrfCompareResult)) {
							if(isLogonRequest) {
								servletrequest.setAttribute("redirectToLogin","Y");
								RequestDispatcher requestdispatcher = servletcontext.getRequestDispatcher(login);
								requestdispatcher.forward(new XSSHttpServletRequestWrapper((HttpServletRequest)servletrequest), new XSSServletResponseWrapper((HttpServletResponse) servletresponse));
								return;
							}
							servletrequest.setAttribute("errordesc",
									SwtUtil.getMessage("errors.csrf.attack.wrongToken", httpservletrequest));
							servletrequest.setAttribute("errorCause", "");
							log.error(SwtUtil.getMessage("errors.csrf.attack.wrongToken.log", httpservletrequest) + "\n" +
									  "{" + SwtUtil.getMessage("errors.user.log", httpservletrequest) + ": " + cdm.getUser().getId().getUserId() +
									  ", " + SwtUtil.getMessage("errors.ipAddress.log", httpservletrequest) + ": " + httpservletrequest.getRemoteAddr() +
									  ", " + SwtUtil.getMessage("errors.requestURI.log", httpservletrequest) + ": " + httpservletrequest.getRequestURI() + "}");
							// If the request is Ajax, immediately return content as erred
							if (isAjax) {
								servletresponse.getWriter().print(SwtUtil.getMessage("errors.csrf.attack.wrongToken", httpservletrequest));
								return;
							} else {
								// In your CSRF filter or interceptor
								if (servletrequest.getAttribute("csrf.error.processed") != null) {
									// Skip CSRF check for error pages
									filterchain.doFilter(new XSSHttpServletRequestWrapper((HttpServletRequest)servletrequest), new XSSServletResponseWrapper((HttpServletResponse) servletresponse));
									return;
								}

								servletrequest.setAttribute("csrf.error.processed", true);
								RequestDispatcher requestdispatcher = servletcontext.getRequestDispatcher(error);
								requestdispatcher.forward(new XSSHttpServletRequestWrapper((HttpServletRequest)servletrequest), new XSSServletResponseWrapper((HttpServletResponse) servletresponse));
								return;
							}
						}
					}
				}
			}catch(Exception e){
				// Empty exception to not block the application when any security test exception was rised
			}


			if(!SwtUtil.isDeveloperMode()) {
				try{
					if(cdm != null) {

						if(!SwtUtil.isDeveloperMode()) {

							String origin = httpservletrequest.getHeader("Origin");

							// Load trusted origins from property
							Set<String> trustedOrigins = loadTrustedOrigins();

							if (origin != null) {
								String fullOrigin = extractOrigin(origin);
								if(trustedOrigins.isEmpty()) {
									((HttpServletResponse) servletresponse).setHeader("Vary", "Origin"); // Avoid cache poisoning
								}
								else if (!trustedOrigins.contains(fullOrigin)) {
									log.error("Blocked CORS request from untrusted origin: " + fullOrigin +
											  ", Requested URI: " + httpservletrequest.getRequestURI() +
											  ", IP: " + httpservletrequest.getRemoteAddr());
									((HttpServletResponse) servletresponse).sendError(HttpServletResponse.SC_FORBIDDEN,
											"Blocked untrusted CORS origin");
									return;
								}

								((HttpServletResponse) servletresponse).setHeader("Vary", "Origin"); // Avoid cache poisoning
							} else {
								if (!"GET".equalsIgnoreCase(httpservletrequest.getMethod())) {
									log.warn("Missing Origin on potentially sensitive request: " + httpservletrequest.getRequestURI() +
											 ", IP: " + httpservletrequest.getRemoteAddr());
									((HttpServletResponse) servletresponse).sendError(HttpServletResponse.SC_FORBIDDEN, "Origin required for non-GET requests");
									return;
								}
							}
						}


						// Check if the referer is valid and the same one the user has used when connecting to the application
						if((httpservletrequest.getHeader("referer")!=null && isCorrectReferer(httpservletrequest.getHeader("referer") ,  cdm.getReferer() ) || isLogoutRequest || isLogonRequest)) {
							ArrayList<String> requestParameters = new ArrayList<String>();
							Enumeration params = httpservletrequest.getParameterNames();
							while(params.hasMoreElements()){
								String paramName = (String)params.nextElement();
								requestParameters.add(paramName+"="+httpservletrequest.getParameter(paramName));
							}
							boolean found = false;
							boolean foundWithParams = false;
							MenuItem item = null;
							MenuItem menuItemFromRequest = null;
							LogonDAO logonDAO =  (LogonDAO)SwtUtil.getBean("logonDAO");

							try{
								httpservletrequest.setAttribute("orginalURL", httpservletrequest.getRequestURI());
								// When a user open screen with different menuAccessId other then the one he has an authroisation bypass id detected
								boolean isMenuItemId = (!SwtUtil.isEmptyOrNull(httpservletrequest.getParameter("ismenuItem")) && httpservletrequest.getParameter("ismenuItem").equals("true"));
								//start mantis 5785
								if(isMenuItemId) {
									//menuItemFromRequest= get it from the list and filter it by menu item id
									//if found then verify that it is the exact menu item by cheking the program name , the method..
									//if test passed keep the value, else nullify it (menuItemFromRequest)
									final String menuItemIdFromRequest = httpservletrequest.getParameter("menuItemId");

									List<MenuItem> userMenuItems = SwtUtil.roleMenuIdsMap.get(cdm.getUser().getRoleId());
									if (userMenuItems == null) {
										ArrayList<MenuItem> menuList = (ArrayList) logonDAO.getMenuListUpdated(cdm.getUser());
										SwtUtil.roleMenuIdsMap.put(cdm.getUser().getRoleId(), menuList);
										userMenuItems = menuList;
									}

									Optional<MenuItem>  options = userMenuItems.stream()
											.filter(x -> x.getItemId().equals(menuItemIdFromRequest)).findFirst();
									MenuItem itemFromFromId = null;
									if (options.isPresent()) {
										itemFromFromId = options.get();
									}

									menuItemFromRequest = SwtUtil.getMenuItem(httpservletrequest);
									if(!(menuItemFromRequest != null && itemFromFromId != null && menuItemFromRequest.getItemId().equals(itemFromFromId.getItemId()))) {
										menuItemFromRequest = null;
									}
								}

								if(menuItemFromRequest==null ) {
									//start logic mentionned in mantis 5785 in (ELSE::) point
									if(httpservletrequest.getHeader("referer").indexOf("logon.do")>=0)
										menuItemFromRequest = SwtUtil.getMenuItem(httpservletrequest);
								}

								if (menuItemFromRequest != null) {
									// check menu item access (from the menu object) if 1 or 0 then allowFilter is
									// true else throw error
									if (Integer.parseInt(menuItemFromRequest.getAccessId()) < 2) {
										// check if parent has access
										if (!SwtUtil.isEmptyOrNull(menuItemFromRequest.getParentId())
											&& Integer.parseInt(menuItemFromRequest.getParentId()) != 0) {

											MenuItem parentMenuItem = logonDAO
													.getMenuItem(menuItemFromRequest.getParentId(), cdm.getUser());

											int prentAccessId = Integer.parseInt(parentMenuItem.getAccessId());

											if (prentAccessId < 2) {
												allowFilter = true;
											} else {
												allowFilter = false;
											}
										} else {
											allowFilter = true;
										}

									} else {
										allowFilter = false;
									}
									//end mantis 5785
								}else {
									//pass it  through xss
									// check if the user has the sufficient authorisation for the current action by getting his role and checking the ACL_list
									if(!SwtUtil.isEmptyOrNull(httpservletrequest.getParameter("menuAccessId")) && isMenuItemId) {
										//LogonDAO logonDAO =  (LogonDAO)SwtUtil.getBean("logonDAO");
										// get user menu access for all menus then compare the url with program ids if it's the same then check the menuAccessId for the current menu Item id
										ArrayList<MenuItem> menuList = (ArrayList) logonDAO.getMenuList(cdm.getUser());
										int i = 0;
										while( i < menuList.size() && foundWithParams == false) {
											item = menuList.get(i);
											if(item.getProgramName() != null && item.getProgramName().startsWith(SwtUtil.getPathToFilter(httpservletrequest))){
												if(item.getProgramName().indexOf("?") != -1) {
													String parametersStr = item.getProgramName().split("\\?")[1];
													if(parametersStr!=null && parametersStr.length()>0){
														String[] list = parametersStr.split("&");
														foundWithParams = true;
														for(int j = 0; j < list.length ; j++){
															if(requestParameters.indexOf(list[j]) == -1) {
																foundWithParams = false;
															}
														}
													}
												}
											}
											i++;
										}

										int h = 0;
										if(foundWithParams == false) {
											while( h < menuList.size() && found == false) {
												item = menuList.get(h);
												if(item.getProgramName() != null && item.getProgramName().equals(SwtUtil.getPathToFilter(httpservletrequest))){
													found = true;
												}
												h++;
											}
										}else
											found = true;

										if(found) {
											int accessId  = -1;
											if(item.getParentId() != null){
												MenuItem parentMenuItem = logonDAO.getMenuItem(item.getItemId(), cdm.getUser());

												int prentAccessId = Integer.parseInt(parentMenuItem.getAccessId());
												accessId = Integer.parseInt(item.getAccessId());

												if(prentAccessId > accessId)
													accessId = prentAccessId;

												while(!SwtUtil.isEmptyOrNull(parentMenuItem.getParentId()) && !parentMenuItem.getParentId().equals("0")){
													parentMenuItem = logonDAO.getMenuItem(parentMenuItem.getParentId(),cdm.getUser());
													prentAccessId = Integer.parseInt(parentMenuItem.getAccessId());

													if(prentAccessId > accessId)
														accessId = prentAccessId;
												}
											}

											if(httpservletrequest.getParameter("menuAccessId").equals(""+accessId)){
												allowFilter = true;
											}else {
												servletrequest.setAttribute("errordesc",
														SwtUtil.getMessage("errors.authorization.attack", httpservletrequest));
												servletrequest.setAttribute("errorCause", "");
												log.error(SwtUtil.getMessage("errors.authorization.attack.log", httpservletrequest) + "\n" +
														  "{" + SwtUtil.getMessage("errors.user.log", httpservletrequest) + ": " + cdm.getUser().getId().getUserId() +
														  ", " + SwtUtil.getMessage("errors.ipAddress.log", httpservletrequest) + ": " + httpservletrequest.getRemoteAddr() +
														  ", " + SwtUtil.getMessage("errors.requestURI.log", httpservletrequest) + ": " + httpservletrequest.getRequestURI() +
														  ", Url Path : "+XSSFilter.getPathToFilter(httpservletrequest) + "}");
												// If the request is Ajax, immediately return content as erred
												if (isAjax) {
													servletresponse.getWriter().print(SwtUtil.getMessage("errors.authorization.attack", httpservletrequest));
													return;
												} else {
													// In your CSRF filter or interceptor
													if (servletrequest.getAttribute("csrf.error.processed") != null) {
														// Skip CSRF check for error pages
														filterchain.doFilter(new XSSHttpServletRequestWrapper((HttpServletRequest)servletrequest), new XSSServletResponseWrapper((HttpServletResponse) servletresponse));
														return;
													}

													servletrequest.setAttribute("csrf.error.processed", true);
													RequestDispatcher requestdispatcher = servletcontext.getRequestDispatcher(error);
													requestdispatcher.forward(new XSSHttpServletRequestWrapper((HttpServletRequest)servletrequest), new XSSServletResponseWrapper((HttpServletResponse) servletresponse));
													return;
												}
											}

										}else {
											allowFilter = true;
										}


									}else {
										// check if the user has the sufficient authorisation for the current action by getting his role and checking the ACL_list
										if(SwtUtil.getAuthorizationAccess(httpservletrequest)) {
											allowFilter = true;
										}else {
											httpservletrequest.setAttribute("errordesc", SwtUtil.getMessage("errors.authorization.attack", httpservletrequest));
											httpservletrequest.setAttribute("errorCause", "");
											log.error(SwtUtil.getMessage("errors.authorization.attack.log", httpservletrequest) + "\n" +
													  "{" + SwtUtil.getMessage("errors.user.log", httpservletrequest) + ": " + cdm.getUser().getId().getUserId() +
													  ", " + SwtUtil.getMessage("errors.ipAddress.log", httpservletrequest) + ": " + httpservletrequest.getRemoteAddr() +
													  ", " + SwtUtil.getMessage("errors.requestURI.log", httpservletrequest) + ": " + httpservletrequest.getRequestURI() + "}");
											// In your CSRF filter or interceptor
											if (servletrequest.getAttribute("csrf.error.processed") != null) {
												// Skip CSRF check for error pages
												filterchain.doFilter(new XSSHttpServletRequestWrapper((HttpServletRequest)servletrequest), new XSSServletResponseWrapper((HttpServletResponse) servletresponse));
												return;
											}

											servletrequest.setAttribute("csrf.error.processed", true);
											RequestDispatcher requestdispatcher = servletcontext.getRequestDispatcher(error);
											requestdispatcher.forward(new XSSHttpServletRequestWrapper((HttpServletRequest)servletrequest), new XSSServletResponseWrapper((HttpServletResponse) servletresponse));
											return;
										}
									}
								}
							}catch(Exception e){
								// When exception during the security checks then allow to proceed the actions 
								allowFilter = true;
							}


						}else {

							boolean isLogonRequestOrRelogin = XSSFilter.getPathToFilter(httpservletrequest).startsWith("login.jsp")
															  || XSSFilter.getPathToFilter(httpservletrequest).startsWith("main.jsp");
							boolean isLogoutRequestForXSS = XSSFilter.getPathToFilter(httpservletrequest).startsWith("logout.do");
							String method = httpservletrequest.getParameter("method")!=null?httpservletrequest.getParameter("method"):"";

							if(isLogoutRequestForXSS || isLogonRequestOrRelogin || method.equals("reLogin")) {
								// Invalidate the session if it exists
								HttpSession session = httpservletrequest.getSession(false);
								if(session != null) {
									session.invalidate();
								}

								// Redirect to login page
								HttpServletResponse response = (HttpServletResponse) servletresponse;
								response.sendRedirect(httpservletrequest.getContextPath() + "/login.jsp");
								return;

							}
							servletrequest.setAttribute("errordesc",
									SwtUtil.getMessage("errors.csrf.attack", httpservletrequest));
							servletrequest.setAttribute("errorCause", "");
							log.error(SwtUtil.getMessage("errors.csrf.attack.log", httpservletrequest) + "\n" +
									  "{" + SwtUtil.getMessage("errors.user.log", httpservletrequest) + ": " + cdm.getUser().getId().getUserId() +
									  ", " + SwtUtil.getMessage("errors.ipAddress.log", httpservletrequest) + ": " + httpservletrequest.getRemoteAddr() +
									  ", " + SwtUtil.getMessage("errors.requestURI.log", httpservletrequest) + ": " + httpservletrequest.getRequestURI() +
									  ", Request Referer: " + httpservletrequest.getHeader("referer") +
									  ", User Referer" + ": " + cdm.getReferer() +
									  ", "+SwtUtil.getMessage("errors.csrf.attack.RefererError", httpservletrequest)+"}");
							// If the request is Ajax, immediately return content as erred
							if (isAjax) {
								servletresponse.getWriter().print(SwtUtil.getMessage("errors.csrf.attack", httpservletrequest));
								return;
							} else {

								// In your CSRF filter or interceptor
								if (servletrequest.getAttribute("csrf.error.processed") != null) {
									// Skip CSRF check for error pages
									filterchain.doFilter(new XSSHttpServletRequestWrapper((HttpServletRequest)servletrequest), new XSSServletResponseWrapper((HttpServletResponse) servletresponse));
									return;
								}

								servletrequest.setAttribute("csrf.error.processed", true);
								RequestDispatcher requestdispatcher = servletcontext.getRequestDispatcher(error);
								requestdispatcher.forward(new XSSHttpServletRequestWrapper((HttpServletRequest)servletrequest), new XSSServletResponseWrapper((HttpServletResponse) servletresponse));
								return;
							}
						}
					}else {
						allowFilter = true;
					}
				}catch(Exception e){
					// When exception during the security checks then allow to proceed the actions 
					allowFilter = true;
				}
			}else {
				allowFilter = true;
			}

			if(httpservletrequest.getRequestURI().contains("invalidatedata.jsp")  || httpservletrequest.getRequestURI().contains("invalidRequest.jsp")
			   || httpservletrequest.getRequestURI().contains("error.jsp") ) {
				filterchain.doFilter(new XSSHttpServletRequestWrapper((HttpServletRequest)servletrequest), new XSSServletResponseWrapper((HttpServletResponse) servletresponse));
				return;
			}
			if(allowFilter)  {
				// No Regex Patern have been declared for XSS filter
				if (patternList.size() == 0) {
					filterchain.doFilter(new XSSHttpServletRequestWrapper((HttpServletRequest)servletrequest), new XSSServletResponseWrapper((HttpServletResponse) servletresponse));
				} else {

					String filteredPath = getPathToFilter(httpservletrequest);

					// if an excluded path
					if (SwtUtil.isEmptyOrNull(filteredPath) || isExludedPath(filteredPath, httpservletrequest.getRequestURI())) {
						filterchain.doFilter(new XSSHttpServletRequestWrapper((HttpServletRequest)servletrequest), new XSSServletResponseWrapper((HttpServletResponse) servletresponse));
						return;
					} else {
						// If the path is not to be excluded, then check for malicious code
						ArrayList<String> paramValues = getParametersValues(httpservletrequest);
						ArrayList<String> maliciousContents = new ArrayList<String>();

						// Check attributes for any malicious contents
						for (Pattern pattern : patternList) {
							maliciousContents.addAll(getMatchingStrings(paramValues, pattern));
						}

						maliciousContents.addAll(getHTMLInjectionStrings(paramValues));
						// If malicious code is detected
						if (maliciousContents.size() > 0) {
							String fromFlex = httpservletrequest.getParameter("fromFlex");
							servletrequest.setAttribute("fromFlex", fromFlex);
							servletrequest.setAttribute("maliciousContents", maliciousContents);

							servletrequest.setAttribute("ErrorMessage",
									SwtUtil.getMessage("errors.content.notAllowed", httpservletrequest));
							// If the request is Ajax, immediately return content as erred
							if (isAjaxPath(filteredPath)) {
								servletresponse.getWriter().print(SwtUtil.getMessage("errors.content.notAllowed", httpservletrequest));
								return;
							} else {

								RequestDispatcher requestdispatcher = servletcontext.getRequestDispatcher(fail);
								requestdispatcher.forward(new XSSHttpServletRequestWrapper((HttpServletRequest)servletrequest), new XSSServletResponseWrapper((HttpServletResponse) servletresponse));
								return;
							}
						} else{
							String queryString = httpservletrequest.getQueryString();
							if(!SwtUtil.isEmptyOrNull(queryString)) {
								String[] params = httpservletrequest.getQueryString().split("&");
								for (String param : params)
								{
									if(!SwtUtil.isEmptyOrNull(param)) {

										String [] p=param.split("=");
//								        String name = p[0];
										if(param.indexOf("=") == -1)  {
											HttpServletResponse httpservletresponse = (HttpServletResponse) servletresponse;
											httpservletresponse.addHeader("Cache-Control", "no-cache, no-store, max-age=0, must-revalidate");
											httpservletresponse.addDateHeader ("Expires", -1);

											httpservletresponse.sendRedirect("invalidRequest.jsp");
											return;


										}
									}
								}
							}


							filterchain.doFilter(new XSSHttpServletRequestWrapper((HttpServletRequest)servletrequest), new XSSServletResponseWrapper((HttpServletResponse) servletresponse));
						}
					}
				}
			}else {
				servletrequest.setAttribute("errordesc",
						SwtUtil.getMessage("errors.csrf.attack", httpservletrequest));
				servletrequest.setAttribute("errorCause", "");
				log.error(SwtUtil.getMessage("errors.csrf.attack.log", httpservletrequest) + "\n" +
						  "{" + SwtUtil.getMessage("errors.user.log", httpservletrequest) + ": " + cdm.getUser().getId().getUserId() +
						  ", " + SwtUtil.getMessage("errors.ipAddress.log", httpservletrequest) + ": " + httpservletrequest.getRemoteAddr() +
						  ", " + SwtUtil.getMessage("errors.requestURI.log", httpservletrequest) + ": " + httpservletrequest.getRequestURI() + ","
						  + " "+SwtUtil.getMessage("errors.csrf.attack.invalidSession", httpservletrequest)+"}");
				// If the request is Ajax, immediately return content as erred
				if (isAjax) {
					servletresponse.getWriter().print(SwtUtil.getMessage("errors.csrf.attack", httpservletrequest));
					return;
				} else {

					// In your CSRF filter or interceptor
					if (servletrequest.getAttribute("csrf.error.processed") != null) {
						// Skip CSRF check for error pages
						filterchain.doFilter(new XSSHttpServletRequestWrapper((HttpServletRequest)servletrequest), new XSSServletResponseWrapper((HttpServletResponse) servletresponse));
						return;
					}

					servletrequest.setAttribute("csrf.error.processed", true);
					RequestDispatcher requestdispatcher = servletcontext.getRequestDispatcher(error);
					requestdispatcher.forward(new XSSHttpServletRequestWrapper((HttpServletRequest)servletrequest), new XSSServletResponseWrapper((HttpServletResponse) servletresponse));
					return;
				}
			}
		}else {
			filterchain.doFilter(new XSSHttpServletRequestWrapper((HttpServletRequest)servletrequest), new XSSServletResponseWrapper((HttpServletResponse) servletresponse));
		}
		log.debug("exiting 'doFilter' method");
	}

	public boolean isCorrectReferer(String newReferer, String savedReferer) {

		boolean goodReferer = false;
		URI uri = null;
		URI uri2 = null;
		try {
			uri = URI.create(savedReferer);
			uri = new URI(uri.getScheme(), uri.getHost(), uri.getPath(), uri.getFragment());
			String savedUrlWithoutPort = uri.toString();

			uri2 = URI.create(newReferer);
			uri2 = new URI(uri2.getScheme(), uri2.getHost(), uri2.getPath(), uri2.getFragment());
			String newUrlWithoutPort = uri2.toString();
			if(newReferer.indexOf(savedReferer) == 0 || newReferer.indexOf(savedUrlWithoutPort) == 0) {
				goodReferer = true;
			}else {
				if(newUrlWithoutPort.indexOf(savedUrlWithoutPort) == 0) {
					goodReferer = true;
				}
			}


		} catch (URISyntaxException e) {
			e.printStackTrace();
		}finally {
			uri = null;
			uri2 = null;
		}

		return goodReferer;
	}

	/**
	 * Compile regexs into patterns
	 * @param listRegex
	 * @return
	 */
	public ArrayList<Pattern> getCompiledRegex(Collection<String> listRegex) {
		ArrayList<Pattern> pattern_list = new ArrayList<Pattern>();
		for (String regex : listRegex) {
			Pattern p = Pattern.compile(".*"+"("+regex+")"+".*", Pattern.DOTALL|Pattern.MULTILINE|Pattern.CASE_INSENSITIVE);
			pattern_list.add(p);
		}
		return pattern_list;
	}

	/**
	 * Returns the list of attribute values that matches a given pattern
	 * @param values
	 * @param p
	 * @return
	 */
	public ArrayList<String> getMatchingStrings(List<String> values, Pattern p) {
		ArrayList<String> matches = new ArrayList<String>();
		long start = System.currentTimeMillis();
		for (String s : values) {
			Matcher matcher = p.matcher(s);
			if (matcher.matches()){
				// Get the first group because the regex is already wrapped into group 1, see method @getCompiledRegex
				matches.add(matcher.group(1));
			}
		}
		return matches;
	}

	/**
	 * Get parameter values from request
	 * @param httpservletrequest
	 * @return
	 */
	public ArrayList<String> getParametersValues(HttpServletRequest httpservletrequest) {
		Enumeration e = httpservletrequest.getParameterNames();
		ArrayList<String> parametersValues = new ArrayList<String>();
		while (e.hasMoreElements()) {
			String key = (String) e.nextElement();
			parametersValues.add(httpservletrequest.getParameter(key));
		}
		return parametersValues;
	}

	/**
	 * Is an ajax request
	 * @param path
	 * @return
	 */
	public boolean isAjaxPath(String path) {
		for (String ajaxPath : ajaxRequestsList) {
			if (path.startsWith(ajaxPath))
				return true;
		}
		return false;
	}

	/**
	 * Is this request to be excluded from xss check
	 * @param path
	 * @return
	 */
	public static boolean isExludedPath(String path, String requestURI) {

		for (String excludedPath : urlsToExcludeList) {
			if (path.startsWith(excludedPath) ||  path.endsWith(excludedPath))
				return true;
		}
		for (String excludedPath : ajaxRequestsList) {
			if (path.startsWith(excludedPath) ||  path.endsWith(excludedPath))
				return true;
		}
		for (String excludedPath : ajaxRequestsList) {
			if (requestURI.startsWith(excludedPath) ||  requestURI.endsWith(excludedPath))
				return true;
		}

		for (String excludedPath : urlsToExcludeList) {
			if (requestURI.startsWith(excludedPath) ||  requestURI.endsWith(excludedPath))
				return true;
		}

		return false;
	}

	/**
	 *
	 * @param httpservletrequest
	 * @return
	 */
	public static String getPathToFilter(HttpServletRequest httpservletrequest) {
		String pathStr = "";
		pathStr = httpservletrequest.getServletPath() +
				  (httpservletrequest.getParameter("method")!=null ? ("?method="+httpservletrequest.getParameter("method")):"");
		return pathStr.startsWith("/")?pathStr.substring(1):pathStr;
	}

	public Collection<String> getXssFilterWords() {
		return xssFilterRegex;
	}

	public void setXssFilterWords(Collection<String> xssFilterWords) {
		this.xssFilterRegex = xssFilterWords;
	}

	public ArrayList<Pattern> getPatternList() {
		return patternList;
	}

	public void setPatternList(ArrayList<Pattern> patternList) {
		this.patternList = patternList;
	}

	/**
	 * Returns the list of attribute values that matches a given pattern
	 * @param values
	 * @param p
	 * @return
	 */
	public ArrayList<String> getHTMLInjectionStrings(List<String> values) {
		ArrayList<String> matches = new ArrayList<String>();
		for (String s : values) {
			Document doc = Jsoup.parse(s);
			boolean retur = findcomments(doc);
			for (String tag : this.notallowedTags) {
				if (doc.body().select(tag).size() > 0) {
					matches.add("HTML Tag <"+tag+">");
				}
			}
			if(retur) {
				matches.add("HTML Tag #comment");
			}
		}

		return matches;
	}
	private static boolean findcomments(Node node) {
		for (int i = 0; i < node.childNodeSize();) {
			Node child = node.childNode(i);
			if (child.nodeName().equals("#comment")) {
				return true;
			}
			else {
				boolean result = findcomments(child);
				if(result) return true;
				i++;
			}
		}
		return false;
	}

	/**
	 * Get the filter configs
	 * @return
	 */
	public FilterConfig getFilterConfig() {
		return config;
	}

	private static class XSSServletResponseWrapper extends HttpServletResponseWrapper {

		public XSSServletResponseWrapper(HttpServletResponse response) {

			super(response);
		}

		@Override
		public void addCookie(Cookie cookie) {
			String cookieValue = cookie.getValue();
			// Remove any CRLF and lines after CRLF, ie keep the cookie value in
			// a single line
			int endIndex = cookieValue.indexOf("\r\n");
			if (endIndex != -1) {
				cookieValue = cookieValue.substring(0, endIndex);
			}
			cookie.setValue(cookieValue);
			super.addCookie(cookie);
		}

		@Override
		public void setHeader(String name, String value){
			// Avoid HTTP response splitting by replacing \r by %0D and \n by %0A
			if(value != null){
				super.setHeader(name, value.replaceAll("\r", "%0D").replaceAll("\n", "%0A"));
			}else{
				super.setHeader(name, value);
			}
		}

	}

	private static class XSSHttpServletRequestWrapper extends HttpServletRequestWrapper {
		private ArrayList<String> parameters = new ArrayList<String>();

		public XSSHttpServletRequestWrapper(HttpServletRequest paramHttpServletRequest) {
			super(paramHttpServletRequest);
		}

		@Override
		public String[] getParameterValues(String paramString) {
			// Register the parameter for any possibilities to XSS-Reflected cleanup
			if(!parameters.contains(paramString)){
				parameters.add(paramString);
			}
			return super.getParameterValues(paramString);
		}

		@Override
		public void setAttribute(String name, Object o) {
			// If parameter is reflected the clean it
			if (o instanceof String && parameters.contains(name) && !parameters.contains("fromFlex") && !parameters.contains("isAjax")) {
				super.setAttribute(name, cleanXSSReflected((String)o));
			}
			else if("errordesc".equalsIgnoreCase(name)||"errorCause".equalsIgnoreCase(name)){
				// Specific case: parameters "errordesc" and "errorCause" are filled in case of an excpetion, they should be cleaned as well
				//TODO: Ensure that the call comes from : org.swallow.util.SwtUtil.logException(SwtUtil.java:1396)
				super.setAttribute(name, cleanXSSReflected((String)o));
			}
			else{
				super.setAttribute(name, o);
			}
		}


		// everything else just passes through
		@Override
		public String getParameter(String paramString)
		{

			if(SwtUtil.getCallTrace(5)) {
				String requestQueryStr = super.getQueryString();
				boolean responseJson = "json".equalsIgnoreCase(super.getParameter("response")) || (requestQueryStr!=null && requestQueryStr.indexOf("response=json")!=-1);
				if(responseJson) {
					try {
						return super.getParameter(paramString) != null?super.getParameter(paramString).replace("%2B", "+"):null;
					}catch(Exception e) {
						return super.getParameter(paramString);
					}
				}
				return super.getParameter(paramString);
			}
			else {
				boolean isAjax  =  super.getParameter("isAjax")!=null && super.getParameter("isAjax").equals("true");
				boolean fromFlex  =  super.getParameter("fromFlex")!=null && super.getParameter("fromFlex").equals("true");
				if( fromFlex || isAjax)
					return super.getParameter(paramString);
				else
					return cleanXSSReflected(super.getParameter(paramString));
			}

		}

		@Override
		public Object getAttribute(String name)
		{
			if(super.getAttribute(name) instanceof String ) {
				if(SwtUtil.getCallTrace(5)) {
					return super.getAttribute(name);
				}else {
					boolean isAjax  =  super.getParameter("isAjax")!=null && super.getParameter("isAjax").equals("true");
					boolean fromFlex  =  super.getParameter("fromFlex")!=null && super.getParameter("fromFlex").equals("true");
					if(fromFlex || isAjax)
						return super.getAttribute(name);
					else
						return (Object) (cleanXSSReflected((String)super.getAttribute(name)));
				}
			}
			else {
				return super.getAttribute(name);
			}
		}


		// TODO: Possible ways on how a parameter is reflected could be detected by overriding other methods.
		/**
		 * Cleans XSS Reflected possible intrusions
		 * @param paramString
		 * @return
		 */
		private String cleanXSSReflected(String paramString) {
			String str = null;
			try{
				if (paramString == null) {
					return null;
				}
				str = paramString;
				//str = str.replaceAll("\000", "");
				str = str.replaceAll("\\(", "\\\\u0028").replaceAll("\\)", "\\\\u0029");
				str = str.replaceAll("'", "\\\\u0027").replaceAll("\"", "\\\\u0022");
				str = str.replaceAll("<", "\\\\u003C").replaceAll(">", "\\\\u003E");
				//str = str.replaceAll(";", "\\\\u003B");

				return str;
			}catch(Exception e) {
			}
			return str;
		}

		/**
		 * Override getRequestURL() in test env so that it will reflect the Spring Cloud Gateway address
		 */
		@Override
		public StringBuffer getRequestURL() {
			if(!SwtUtil.envTestEnabled()) {
				return super.getRequestURL();
			}
			String forwaredHost = this.getHeader(HttpHeaders.X_FORWARDED_HOST);
			String localHost = this.getHeader(HttpHeaders.HOST);

			if(!SwtUtil.isEmptyOrNull(localHost) && !SwtUtil.isEmptyOrNull(forwaredHost)) {
				return new StringBuffer(super.getRequestURL().toString().replace(localHost, forwaredHost));
			} else {
				return super.getRequestURL();
			}
		}

		/**
		 * Override getRequestURI() in test env so that it will reflect the Spring Cloud Gateway address
		 */
		@Override
		public String getRequestURI() {
			if(!SwtUtil.envTestEnabled()) {
				return super.getRequestURI();
			}
			String forwaredHost = this.getHeader(HttpHeaders.X_FORWARDED_FOR);
			String localHost = this.getHeader(HttpHeaders.HOST);
			if(!SwtUtil.isEmptyOrNull(localHost) && !SwtUtil.isEmptyOrNull(forwaredHost)) {
				return super.getRequestURI().replace(localHost, forwaredHost);
			} else {
				return super.getRequestURI();
			}
		}
	}

}