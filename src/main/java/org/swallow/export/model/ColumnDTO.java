/**
 * @(#)ColumnDTO.java 1.0
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.export.model;

import java.io.Serializable;

/**
 * ColumnDTO.java
 * 
 * This java bean has getters and setters for column details
 * 
 * @modifiedby <PERSON>krishnan R / Mar 20, 2012 / SmartPredict-1054
 */
public class ColumnDTO implements Serializable{
	// Column Header
	private String heading = null;
	// Column datatype
	private String type = null;
	// Data element
	private String dataElement = null;

	/**
	 * Default constructor
	 */
	public ColumnDTO() {

	}

	/**
	 * Constructor
	 * 
	 * @param heading
	 * @param type
	 * @param dataElement
	 */
	public ColumnDTO(String heading, String type, String dataElement) {
		this.heading = heading;
		this.type = type;
		this.dataElement = dataElement;
	}

	/**
	 * Getter method of heading
	 * 
	 * @return the heading
	 */
	public String getHeading() {
		return heading;
	}

	/**
	 * Setter method of heading
	 * 
	 * @param heading
	 *            the heading to set
	 */
	public void setHeading(String heading) {
		this.heading = heading;
	}

	/**
	 * Getter method of type
	 * 
	 * @return the type
	 */
	public String getType() {
		return type;
	}

	/**
	 * Setter method of type
	 * 
	 * @param type
	 *            the type to set
	 */
	public void setType(String type) {
		this.type = type;
	}

	/**
	 * Getter method of dataElement
	 * 
	 * @return the dataElement
	 */
	public String getDataElement() {
		return dataElement;
	}

	/**
	 * Setter method of dataElement
	 * 
	 * @param dataElement
	 *            the dataElement to set
	 */
	public void setDataElement(String dataElement) {
		this.dataElement = dataElement;
	}
}
