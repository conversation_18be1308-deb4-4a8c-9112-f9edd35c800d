/*
 * @(#)BookCodeColumnData.java 1.0 ,10/10/10
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */

package org.swallow.export.model;

import java.io.Serializable;
import java.util.ArrayList;

import org.swallow.export.model.ColumnDTO;

/*
 * Used to form the Column Headers in PDF,Excel and CSV format for Book
 * Maintenace Screen
 */
public class BookCodeColumnData implements Serializable {
	private String groupLevel1="";
	private String groupLevel2="";
	private String groupLevel3="";	
	public ArrayList<ColumnDTO> getColumnData() {

		ArrayList<ColumnDTO> rtn = new ArrayList<ColumnDTO>();

		ColumnDTO cDTO = new ColumnDTO();
		cDTO.setHeading("Book");
		cDTO.setType("str");
		cDTO.setDataElement("book");
		rtn.add(cDTO);

		cDTO = new ColumnDTO();
		cDTO.setHeading("Name");
		cDTO.setType("str");
		cDTO.setDataElement("name");
		rtn.add(cDTO);

		cDTO = new ColumnDTO();
		cDTO.setHeading("Location");
		cDTO.setType("str");
		cDTO.setDataElement("loc");
		rtn.add(cDTO);

		cDTO = new ColumnDTO();
		cDTO.setHeading(this.groupLevel1);
		cDTO.setType("str");
		cDTO.setDataElement("gl1");
		rtn.add(cDTO);

		cDTO = new ColumnDTO();
		cDTO.setHeading(this.groupLevel2);
		cDTO.setType("str");
		cDTO.setDataElement("gl2");
		rtn.add(cDTO);

		cDTO = new ColumnDTO();
		cDTO.setHeading(this.groupLevel3);
		cDTO.setType("str");
		cDTO.setDataElement("gl3");
		rtn.add(cDTO);
		return rtn;

	}
	public BookCodeColumnData(String _groupLevel1,String _groupLevel2,String _groupLevel3)
	{
		this.groupLevel1 = _groupLevel1;
		this.groupLevel2 = _groupLevel2;
		this.groupLevel3 = _groupLevel3;
	}
	/**
	 * @return the groupLevel1
	 */
	public String getGroupLevel1() {
		return groupLevel1;
	}
	/**
	 * @param groupLevel1 the groupLevel1 to set
	 */
	public void setGroupLevel1(String groupLevel1) {
		this.groupLevel1 = groupLevel1;
	}
	/**
	 * @return the groupLevel2
	 */
	public String getGroupLevel2() {
		return groupLevel2;
	}
	/**
	 * @param groupLevel2 the groupLevel2 to set
	 */
	public void setGroupLevel2(String groupLevel2) {
		this.groupLevel2 = groupLevel2;
	}
	/**
	 * @return the groupLevel3
	 */
	public String getGroupLevel3() {
		return groupLevel3;
	}
	/**
	 * @param groupLevel3 the groupLevel3 to set
	 */
	public void setGroupLevel3(String groupLevel3) {
		this.groupLevel3 = groupLevel3;
	}

}
