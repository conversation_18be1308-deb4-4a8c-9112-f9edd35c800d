package org.swallow.export.service;

import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.DefaultIndexedColorMap;
import org.apache.poi.xssf.usermodel.XSSFCellStyle;
import org.apache.poi.xssf.usermodel.XSSFColor;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.swallow.exception.SwtException;
import org.swallow.util.SwtUtil;
import org.swallow.util.SystemFormats;
import org.swallow.work.model.Movement;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.List;


public class MultipleMovementExcelReportGenerator {
    static String amountFormat= null;
    static SystemFormats sysFormat=null;
    static String dateFormat= null;

    private static final String[] HEADERS = {
            "Movement ID", "Entity", "Currency", "Value Date", "Account", "Amount",
            "Sign", "Prediction Status", "Extract Status", "ILM Forecast", "Match Status",
            "Ref1", "Ref2", "Extra Ref", "Book Code", "Match ID", "Source",
            "Format", "Counterparty", "Ordering Institution", "Expected Settlement", "Actual Settlement", "Critical Payment Type"
    };

    private static final String[] FAILURE_HEADERS = {
            "Movement ID", "Entity", "Currency", "Value Date", "Account", "Amount",
            "Sign", "Prediction Status", "Extract Status", "ILM Forecast", "Match Status",
            "Ref1", "Ref2", "Extra Ref", "Book Code", "Match ID", "Source",
            "Format", "Counterparty", "Ordering Institution", "Expected Settlement", "Actual Settlement", "Critical Payment Type",
            "Failing Cause"
    };

    public static byte[] generateExcelReport(List<Movement> movements, HttpServletRequest request) throws IOException, SwtException {
        Workbook workbook = new XSSFWorkbook();
        Sheet summarySheet = workbook.createSheet("Summary");
        Sheet failureSheet = workbook.createSheet("Failed Movements");
        Sheet successSheet = workbook.createSheet("Success Movements");

        // Create styles
        CellStyle headerStyle = createHeaderStyle(workbook);
        CellStyle borderStyle = createBorderStyle(workbook);
        CellStyle alternateRowStyle = createAlternateRowStyle(workbook);
        CellStyle failingCauseStyle = createFailingCauseStyle(workbook);

        // Create headers
        createHeaderRow(successSheet, HEADERS, headerStyle);
        createHeaderRow(failureSheet, FAILURE_HEADERS, headerStyle);


        int successRowNum = 1;
        int failureRowNum = 1;
        int successCount = 0;
        int failureCount = 0;

        for (Movement movement : movements) {
            Row row;
            boolean isEvenRow = (successRowNum % 2 == 0);


            if(movement
                    .getExpectedSettlementDateTime() != null) {
                movement.setExpectedSettlementDateTimeAsString((SwtUtil.formatDate(movement
                        .getExpectedSettlementDateTime(), SwtUtil.getCurrentSystemFormats(
                        request.getSession()).getDateFormatValue().concat(" HH:mm:ss"))));
            }
            if(movement
                    .getSettlementDateTime() != null) {
                movement.setSettlementDateTimeAsString((SwtUtil.formatDate(movement
                        .getSettlementDateTime(), SwtUtil.getCurrentSystemFormats(
                        request.getSession()).getDateFormatValue().concat(" HH:mm:ss"))));
            }

            if (movement.isSuccessful()) {
                row = successSheet.createRow(successRowNum++);
                fillRow(row, movement,  isEvenRow ? alternateRowStyle : borderStyle);
                successCount++;
            } else {
                row = failureSheet.createRow(failureRowNum++);
                boolean isEvenFailedRow = (failureRowNum % 2 == 0);
                fillRow(row, movement,  isEvenFailedRow ? alternateRowStyle : borderStyle);

                // Apply failing cause styling
                Cell failReasonCell = row.createCell(HEADERS.length);
                failReasonCell.setCellValue(movement.getFailingCause());
                failReasonCell.setCellStyle(failingCauseStyle);

                failureCount++;
            }
        }

        // Summary Sheet
        createSummarySheet(summarySheet, successCount, failureCount, request, headerStyle, borderStyle);

        // **Auto-size columns to fit content**
        autoSizeColumns(successSheet, HEADERS.length);
        autoSizeColumns(failureSheet, FAILURE_HEADERS.length);
        autoSizeColumns(summarySheet, 2);

        // Convert to byte array
        ByteArrayOutputStream out = new ByteArrayOutputStream();
        workbook.write(out);
        workbook.close();

        return out.toByteArray();
    }

    private static void createHeaderRow(Sheet sheet, String[] headers, CellStyle headerStyle) {
        Row headerRow = sheet.createRow(0);
        for (int i = 0; i < headers.length; i++) {
            Cell cell = headerRow.createCell(i);
            cell.setCellValue(headers[i]);
            cell.setCellStyle(headerStyle);
        }
    }

    private static void fillRow(Row row, Movement movement, CellStyle style) throws SwtException {
        Cell cell;


        cell = row.createCell(0); cell.setCellValue(movement.getId().getMovementId().toString()); cell.setCellStyle(style);
        cell = row.createCell(1); cell.setCellValue(movement.getId().getEntityId()); cell.setCellStyle(style);
        cell = row.createCell(2); cell.setCellValue(movement.getCurrencyCode()); cell.setCellStyle(style);
        cell = row.createCell(3); cell.setCellValue(SwtUtil.formatDate(movement.getValueDate(), dateFormat )); cell.setCellStyle(style);
        cell = row.createCell(4); cell.setCellValue(movement.getAccountId()); cell.setCellStyle(style);
        cell = row.createCell(5); cell.setCellValue(SwtUtil.formatCurrency(movement
                .getCurrencyCode(), new BigDecimal(movement.getAmount()),amountFormat));cell.setCellStyle(style);
        cell = row.createCell(6); cell.setCellValue(movement.getSign()); cell.setCellStyle(style);
        cell = row.createCell(7); cell.setCellValue(movement.getPredictStatus()); cell.setCellStyle(style);
        cell = row.createCell(8); cell.setCellValue(movement.getExtractStatus()); cell.setCellStyle(style);
        cell = row.createCell(9); cell.setCellValue(movement.getIlmFcastStatus()); cell.setCellStyle(style);
        cell = row.createCell(10); cell.setCellValue(movement.getMatchStatus()); cell.setCellStyle(style);
        cell = row.createCell(11); cell.setCellValue(movement.getReference1()); cell.setCellStyle(style);
        cell = row.createCell(12); cell.setCellValue(movement.getReference2()); cell.setCellStyle(style);
        cell = row.createCell(13); cell.setCellValue(movement.getReference4()); cell.setCellStyle(style);
        cell = row.createCell(14); cell.setCellValue(movement.getBookCode()); cell.setCellStyle(style);
        cell = row.createCell(15); cell.setCellValue(movement.getMatchId() != null ? String.valueOf(movement.getMatchId()) : ""); cell.setCellStyle(style);
        cell = row.createCell(16); cell.setCellValue(movement.getInputSource()); cell.setCellStyle(style);
        cell = row.createCell(17); cell.setCellValue(movement.getMessageFormat()); cell.setCellStyle(style);
        cell = row.createCell(18); cell.setCellValue(movement.getCounterPartyId()); cell.setCellStyle(style);
        cell = row.createCell(19); cell.setCellValue(movement.getOrderingInstitutionId()); cell.setCellStyle(style);
        cell = row.createCell(20); cell.setCellValue(movement.getExpectedSettlementDateTimeAsString()); cell.setCellStyle(style);
        cell = row.createCell(21); cell.setCellValue(movement.getSettlementDateTimeAsString()); cell.setCellStyle(style);
        cell = row.createCell(22); cell.setCellValue(movement.getCriticalPaymentType()); cell.setCellStyle(style);
    }

    private static void createSummarySheet(Sheet sheet, int successCount, int failureCount, HttpServletRequest request, CellStyle headerStyle, CellStyle borderStyle) {
        Row row;
        int rowIndex = 0;

        // Extract request parameters for processing summary
        String action = request.getParameter("action");
        String movementsList = request.getParameter("movementsList");
        String note = request.getParameter("note");

        String predictStatus = request.getParameter("predictStatus");
        String externalStatus = request.getParameter("externalStatus");
        String ilmFcastStatus = request.getParameter("ilmFcastStatus");

        String book = request.getParameter("book");
        String orderingInst = request.getParameter("orderingInst");
        String critPayType = request.getParameter("critPayType");
        String counterParty = request.getParameter("counterParty");
        String expSettlAsString = request.getParameter("expSettlAsString");
        String actSettlAsString = request.getParameter("actSettlAsString");

        String sequenceNumber = request.getParameter("seq");

        // Header Row
        row = sheet.createRow(rowIndex++);
        row.createCell(0).setCellValue("Processing Summary");
        row.getCell(0).setCellStyle(headerStyle);

        // Selected Action
        row = sheet.createRow(rowIndex++);
        row.createCell(0).setCellValue("Selected Action:");
        row.createCell(1).setCellValue(action != null ? action : "N/A");
        row.getCell(0).setCellStyle(borderStyle);
        row.getCell(1).setCellStyle(borderStyle);



        // Movements List
        row = sheet.createRow(rowIndex++);
        row.createCell(0).setCellValue("");
        row.createCell(1).setCellValue( (successCount+ failureCount) );
        row.getCell(0).setCellStyle(borderStyle);
        row.getCell(1).setCellStyle(borderStyle);


        // Required Parameters
        row = sheet.createRow(rowIndex++);
        row.createCell(0).setCellValue("Note:");
        row.createCell(1).setCellValue(note != null ? note : "N/A");
        row.getCell(0).setCellStyle(borderStyle);
        row.getCell(1).setCellStyle(borderStyle);

        // Optional Parameters (only if present)
        if (predictStatus != null) {
            row = sheet.createRow(rowIndex++);
            row.createCell(0).setCellValue("Predict Status:");
            row.createCell(1).setCellValue(predictStatus);
            row.getCell(0).setCellStyle(borderStyle);
            row.getCell(1).setCellStyle(borderStyle);
        }

        if (externalStatus != null) {
            row = sheet.createRow(rowIndex++);
            row.createCell(0).setCellValue("External Status:");
            row.createCell(1).setCellValue(externalStatus);
            row.getCell(0).setCellStyle(borderStyle);
            row.getCell(1).setCellStyle(borderStyle);
        }

        if (ilmFcastStatus != null) {
            row = sheet.createRow(rowIndex++);
            row.createCell(0).setCellValue("ILM Forecast Status:");
            row.createCell(1).setCellValue(ilmFcastStatus);
            row.getCell(0).setCellStyle(borderStyle);
            row.getCell(1).setCellStyle(borderStyle);
        }

        if (book != null) {
            row = sheet.createRow(rowIndex++);
            row.createCell(0).setCellValue("Book:");
            row.createCell(1).setCellValue(book);
            row.getCell(0).setCellStyle(borderStyle);
            row.getCell(1).setCellStyle(borderStyle);
        }

        if (orderingInst != null) {
            row = sheet.createRow(rowIndex++);
            row.createCell(0).setCellValue("Ordering Institution:");
            row.createCell(1).setCellValue(orderingInst);
            row.getCell(0).setCellStyle(borderStyle);
            row.getCell(1).setCellStyle(borderStyle);
        }

        if (critPayType != null) {
            row = sheet.createRow(rowIndex++);
            row.createCell(0).setCellValue("Critical Payment Type:");
            row.createCell(1).setCellValue(critPayType);
            row.getCell(0).setCellStyle(borderStyle);
            row.getCell(1).setCellStyle(borderStyle);
        }

        if (counterParty != null) {
            row = sheet.createRow(rowIndex++);
            row.createCell(0).setCellValue("Counterparty:");
            row.createCell(1).setCellValue(counterParty);
            row.getCell(0).setCellStyle(borderStyle);
            row.getCell(1).setCellStyle(borderStyle);
        }

        if (expSettlAsString != null) {
            row = sheet.createRow(rowIndex++);
            row.createCell(0).setCellValue("Expected Settlement:");
            row.createCell(1).setCellValue(expSettlAsString);
            row.getCell(0).setCellStyle(borderStyle);
            row.getCell(1).setCellStyle(borderStyle);
        }

        if (actSettlAsString != null) {
            row = sheet.createRow(rowIndex++);
            row.createCell(0).setCellValue("Actual Settlement:");
            row.createCell(1).setCellValue(actSettlAsString);
            row.getCell(0).setCellStyle(borderStyle);
            row.getCell(1).setCellStyle(borderStyle);
        }

        // Summary of Success/Failure
        row = sheet.createRow(rowIndex++);
        row.createCell(0).setCellValue("Total Successful Movements:");
        row.createCell(1).setCellValue(successCount);
        row.getCell(0).setCellStyle(headerStyle);
        row.getCell(1).setCellStyle(borderStyle);

        row = sheet.createRow(rowIndex++);
        row.createCell(0).setCellValue("Total Failed Movements:");
        row.createCell(1).setCellValue(failureCount);
        row.getCell(0).setCellStyle(headerStyle);
        row.getCell(1).setCellStyle(borderStyle);

        row = sheet.createRow(rowIndex++);
        row.createCell(0).setCellValue("Tracking Sequence Number:");
        row.createCell(1).setCellValue(Integer.parseInt(sequenceNumber));
        row.getCell(0).setCellStyle(headerStyle);
        row.getCell(1).setCellStyle(borderStyle);




    }


    private static void autoSizeColumns(Sheet sheet, int columnCount) {
        for (int i = 0; i < columnCount; i++) {
            sheet.autoSizeColumn(i);
        }
    }

    private static CellStyle createHeaderStyle(Workbook workbook) {
        CellStyle style = workbook.createCellStyle();
        Font font = workbook.createFont();
        font.setBold(true);
        font.setColor(IndexedColors.WHITE.getIndex());
        style.setFont(font);
        style.setFillForegroundColor(IndexedColors.BLUE.getIndex());
        style.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        return style;
    }

    private static CellStyle createBorderStyle(Workbook workbook) {
        CellStyle style = workbook.createCellStyle();
        style.setBorderTop(BorderStyle.THIN);
        style.setBorderBottom(BorderStyle.THIN);
        style.setBorderLeft(BorderStyle.THIN);
        style.setBorderRight(BorderStyle.THIN);
        return style;
    }

    private static CellStyle createAlternateRowStyle(Workbook workbook) {
        CellStyle style = createBorderStyle(workbook);

        // Create custom light cyan color (#CCFFFF)
        byte[] rgb = new byte[]{(byte) 204, (byte) 255, (byte) 255}; // RGB for #CCFFFF
        XSSFColor lightCyan = new XSSFColor(rgb, new DefaultIndexedColorMap());

        ((XSSFCellStyle) style).setFillForegroundColor(lightCyan);
        style.setFillPattern(FillPatternType.SOLID_FOREGROUND);

        return style;
    }

    private static CellStyle createFailingCauseStyle(Workbook workbook) {
        CellStyle style = createBorderStyle(workbook);
        style.setFillForegroundColor(IndexedColors.RED.getIndex());
        style.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        Font font = workbook.createFont();
        font.setColor(IndexedColors.WHITE.getIndex());
        font.setBold(true);
        style.setFont(font);
        return style;
    }

    public static void exportReport(HttpServletResponse response, HttpServletRequest request, List<Movement> movements) throws IOException, SwtException {
        try {
            sysFormat = SwtUtil.getCurrentSystemFormats(request.getSession());
            amountFormat=sysFormat.getCurrencyFormat();
            dateFormat= SwtUtil.getCurrentDateFormat(request.getSession());
        } catch (SwtException e) {
            throw new RuntimeException(e);
        }
        byte[] excelData = generateExcelReport(movements, request);
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setHeader("Content-Disposition", "attachment; filename=Movement_Report.xlsx");
        response.getOutputStream().write(excelData);
        response.getOutputStream().flush();
    }
}
