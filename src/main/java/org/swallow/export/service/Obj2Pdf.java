/*
 * @(#)Obj2Pdf.java 1.0
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.export.service;

import java.util.ArrayList;
import java.util.Collection;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.swallow.exception.SwtException;
import org.swallow.export.model.ColumnDTO;
import org.swallow.export.model.FilterDTO;
import org.swallow.model.ExportObject;
import org.swallow.model.GraphObject;

public interface Obj2Pdf {
	/**
	 * 
	 * @param request
	 * @param response
	 * @param columnData
	 * @param filterData
	 * @param rowData
	 * @param totalData
	 * @param graphData
	 * @param screenName
	 * @throws SwtException
	 */
	public void convertObject(HttpServletRequest request, HttpServletResponse response, ArrayList<ColumnDTO> columnData,
			ArrayList<FilterDTO> filterData,
			ArrayList<ArrayList<ExportObject>> rowData, Collection totalData,
			ArrayList<ArrayList<GraphObject>> graphData,
			String screenName) throws SwtException;
}
