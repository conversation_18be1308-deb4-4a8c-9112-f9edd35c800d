/**
 * @(#)MovementReportCSV.java / 1.0 / 4 Jan 2010 / SEL Software
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard, Tremadoc Road, London UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.export.service.impl;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.util.ArrayList;

import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.swallow.exception.SwtException;
import org.swallow.export.model.ColumnDTO;
import org.swallow.export.model.FilterDTO;
import org.swallow.util.CommonDataManager;
import org.swallow.util.SwtUtil;
import org.swallow.util.UserThreadLocalHolder;
import org.swallow.work.model.Movement;
import org.w3c.dom.Node;
import org.w3c.dom.NodeList;

/**
 * MovementReportCSV.java
 * 
 * This class is used to generate movement summary report in CSV format
 * 
 * <AUTHOR> R
 * @version Predict / 4 Jan 2010
 */
public class MovementReportCSV extends MovementReport {

	// Logger object
	private Log log = LogFactory.getLog(this.getClass());

	/**
	 * This method is used to generate movement summary report in CSV format
	 * 
	 * @param columnData -
	 *            Column data list
	 * @param filterData -
	 *            Filter data list
	 * @param rowData -
	 *            Movement data list
	 * @param out -
	 *            Output stream
	 * @param screenName -
	 *            Name of the screen
	 * @throws SwtException
	 */
	public void generateReport(ArrayList<ColumnDTO> columnData,
			ArrayList<FilterDTO> filterData, ArrayList<Movement> rowData,
			String screenName) throws SwtException {
		/* Variable Declaration for CommonDataManager */
		CommonDataManager CDM = null;
		try {
			// log debug message
			log.debug("Movement CSV generation - Entry");

			// log debug message
			log.debug("Add title");
			// Report title
			_outputStream.write("\n\"".getBytes());
			_outputStream.write(screenName.getBytes());
			_outputStream.write(" - ".getBytes());
			_outputStream.write(String.valueOf(SwtUtil.getSystemDatewithTime())
					.getBytes());
			_outputStream.write("\"\n\n".getBytes());
			// log debug message
			log.debug("Add filter data");
			// Add filter data
			for (int i = 0; i < filterData.size(); i++) {
				_outputStream.write("\"".getBytes());
				_outputStream.write(filterData.get(i).getName().getBytes());
				_outputStream.write("\",\"".getBytes());
				_outputStream.write(filterData.get(i).getValue().getBytes());
				_outputStream.write("\"\n".getBytes());
			}
			// Add new line
			_outputStream.write("\n".getBytes());

			// Create the table and initial params
			if (columnData != null) {
				// log debug message
				log.debug("Populate grid header");
				// Populate Headers
				for (int i = 0; i < columnData.size(); i++) {
					_outputStream.write("\"".getBytes());
					_outputStream.write(columnData.get(i).getHeading()
							.getBytes());
					_outputStream.write("\"".getBytes());
					if (i != columnData.size() - 1)
						_outputStream.write(",".getBytes());
				}
				// Add new line
				_outputStream.write("\n".getBytes());
				// log debug message
				log.debug("Populate grid data");
				CDM = (CommonDataManager)UserThreadLocalHolder.getUserSession().getAttribute("CDM");
				// Populate the rows of the grid
				for (int i = 0; i < rowData.size(); i++) {
					
					if(CDM != null) {
						if(!SwtUtil.isEmptyOrNull(CDM.getCancelMSDExport()) && "true".equals(CDM.getCancelMSDExport())) {
							throw new SwtException("generatedException");
						}
					}
					
					// Get movement record
					Movement movement = rowData.get(i);

					_outputStream.write("\"".getBytes());
					// Position level
					_outputStream.write(cleanString(
							movement.getPositionLevelName()).getBytes());
					_outputStream.write("\",\"".getBytes());


					// Entity ID
					_outputStream.write(cleanString(movement.getId().getEntityId())
							.getBytes());
					_outputStream.write("\",\"".getBytes());

					// Value date
					_outputStream.write(cleanString(
							movement.getValueDateAsString()).getBytes());
					_outputStream.write("\",\"".getBytes());
					
					// Amount
					_outputStream.write(cleanString(
							movement.getAmountAsString()).getBytes());
					_outputStream.write("\",\"".getBytes());
					
					// Sign
					_outputStream.write(cleanString(movement.getSign())
							.getBytes());
					_outputStream.write("\",\"".getBytes());
					
					// Currency code
					_outputStream.write(cleanString(movement.getCurrencyCode())
							.getBytes());
					_outputStream.write("\",\"".getBytes());
					
					// Predict status
					_outputStream
							.write(cleanString(movement.getPredictStatus())
									.getBytes());
					_outputStream.write("\",\"".getBytes());
					
					// Match Status
					_outputStream.write(cleanString(
							movement.getMatchStatusDesc()).getBytes());
					_outputStream.write("\",\"".getBytes());
					
					// Account id
					_outputStream.write(cleanString(movement.getAccountId())
							.getBytes());
					_outputStream.write("\",\"".getBytes());
					
					// Ref 1
					_outputStream.write(cleanString(movement.getReference1())
							.getBytes());
					_outputStream.write("\",\"".getBytes());
					
					// Ref 2
					_outputStream.write(cleanString(movement.getReference2())
							.getBytes());
					_outputStream.write("\",\"".getBytes());
					
					// Ref 3
					_outputStream.write(cleanString(movement.getReference3())
							.getBytes());
					_outputStream.write("\",\"".getBytes());
					
					/*
					 * Start : Code modified by Balaji on 17-Jul-2012 for Mantis
					 * 1998: Movement Summary Display: Add 'Extra Ref' column to
					 * the MSD grid
					 */
					// Extra ref
					_outputStream.write(cleanString(movement.getReference4())
							.getBytes());
					_outputStream.write("\",\"".getBytes());
					
					// Extra Text1
					_outputStream.write(cleanString(
							movement.getUetr()).getBytes());
					_outputStream.write("\",\"".getBytes());
					
					/*
					 * End : Code modified by Balaji on 17-Jul-2012 for Mantis
					 * 1998: Movement Summary Display: Add 'Extra Ref' column to
					 * the MSD grid
					 */
					
					// Input date
					_outputStream.write(cleanString(
							movement.getInputDateAsString()).getBytes());
					_outputStream.write("\",\"".getBytes());
					
					// External Balance status
					_outputStream.write(cleanString(movement.getExtBalStatus())
							.getBytes());
					_outputStream.write("\",\"".getBytes());
					
					// Book code
					_outputStream.write(cleanString(movement.getBookCode())
							.getBytes());
					_outputStream.write("\",\"".getBytes());
					
					// Counter Party id
					_outputStream.write(cleanString(
							movement.getCounterPartyId()).getBytes());
					_outputStream.write("\",\"".getBytes());
					
					// Beneficiary id
					_outputStream
							.write(cleanString(movement.getBeneficiaryId())
									.getBytes());
					_outputStream.write("\",\"".getBytes());
					
					// Custodian id
					_outputStream.write(cleanString(movement.getCustodianId())
							.getBytes());
					_outputStream.write("\",\"".getBytes());
					
					// Matching Party
					_outputStream
							.write(cleanString(movement.getMatchingParty())
									.getBytes());
					_outputStream.write("\",\"".getBytes());
					
					// Product Type
					_outputStream.write(cleanString(movement.getProductType())
							.getBytes());
					_outputStream.write("\",\"".getBytes());
					
					// Posting date
					_outputStream.write(cleanString(
							movement.getPostingDateAsString()).getBytes());
					_outputStream.write("\",\"".getBytes());
					
					// Match Id
					_outputStream.write(cleanString(movement.getMatchId())
							.getBytes());
					_outputStream.write("\",\"".getBytes());
					
					// Movement id
					_outputStream.write(cleanString(
							movement.getId().getMovementId()).getBytes());
					_outputStream.write("\",\"".getBytes());
					
					// Input Source
					_outputStream.write(cleanString(movement.getInputSource())
							.getBytes());
					_outputStream.write("\",\"".getBytes());
					
					// Message Format
					_outputStream
							.write(cleanString(movement.getMessageFormat())
									.getBytes());
					_outputStream.write("\",\"".getBytes());
					
					// Update date
					_outputStream.write(cleanString(
							movement.getUpdateDateAsString()).getBytes());
					_outputStream.write("\",\"".getBytes());
					
					// Notes
					_outputStream.write(cleanString(movement.getHasNotes())
							.getBytes());
					_outputStream.write("\",\"".getBytes());
					
					// Extra Text1
					_outputStream.write(cleanString(
							movement.getExtraText1()).getBytes());
					_outputStream.write("\",\"".getBytes());
					
					// ILM FCast
					_outputStream.write(cleanString(movement.getIlmFcastStatus())
							.getBytes());
					//if (SwtUtil.isEmptyOrNull(movement.getAttributeXml())) {
					_outputStream.write("\",\"".getBytes());
					
					//we need to read the xml coming from DB--> to be checked with Atef
					if(!SwtUtil.isEmptyOrNull(movement.getAttributeXml())){
					 String xmlString = !SwtUtil.isEmptyOrNull(movement.getAttributeXml())? "<columns>"+movement.getAttributeXml()+"</columns>":"";
		        		
				        //"<columns><msd227 clickable=\"false\"><![CDATA[I]]></msd227><msd228 clickable=\"false\"><![CDATA[RABO]]></msd228></columns>";
				        
				        DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance();
				        DocumentBuilder builder = factory.newDocumentBuilder();
				        org.w3c.dom.Document document1 = (org.w3c.dom.Document) builder.parse(new ByteArrayInputStream(xmlString.getBytes()));
				        
				        NodeList nodeList = ((org.w3c.dom.Document) document1).getElementsByTagName("*");
				        for (int j = 0; j < nodeList.getLength(); j++) {
				            Node node = nodeList.item(j);
				            if (node.getNodeType() == Node.ELEMENT_NODE) {
				            	org.w3c.dom.Element element = (org.w3c.dom.Element) node;
				                String name = ((Node) element).getNodeName();
				                String value = ((Node) element).getTextContent();			                
				                if(!"columns".equalsIgnoreCase(name)) {

									_outputStream.write(cleanString(value)
											.getBytes());
									if(j<(nodeList.getLength())-1) {
									_outputStream.write("\",\"".getBytes());
									}
				                }
				            }
				        }
					}
					_outputStream.write("\"\n".getBytes());
				}
				// log debug message
				log.debug("Populated grid successfully");
			}
			// log debug message
			log.debug("Report generated successfully");
		} catch (IOException ex) {
			// log error message
			log.error("Error while generate movement report [CSV]."
					+ ex.getMessage());
			throw new SwtException(ex.getMessage());
		} catch (Exception ex) {
			// log error message
			log.error("Error while generate movement report [CSV]."
					+ ex.getMessage());
			throw new SwtException(ex.getMessage());
		} finally {
			// log debug message
			log.debug("Movement CSV generation - Exit");
		}
	}

	/**
	 * This method replaces special characters like (&, < and >) with
	 * corresponding html entities
	 * 
	 * @param input
	 * @return String
	 */
	protected String cleanString(String input) {
		String rtn = super.cleanString(input);
		rtn = rtn.replaceAll("\"", "\'");
		return rtn;
	}
}