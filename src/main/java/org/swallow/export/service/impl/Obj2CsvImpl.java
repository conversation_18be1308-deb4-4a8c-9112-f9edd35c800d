/*
 * @(#)Obj2CsvImpl.java 1.0
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.export.service.impl;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Iterator;

import javax.servlet.http.HttpServletRequest;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.swallow.exception.SwtException;
import org.swallow.export.model.ColumnDTO;
import org.swallow.export.model.FilterDTO;
import org.swallow.export.service.Obj2Csv;
import org.swallow.model.ExportObject;
import org.swallow.model.GraphObject;
import org.swallow.util.CommonDataManager;
import org.swallow.util.SwtUtil;
import org.swallow.util.UserThreadLocalHolder;
import org.w3c.dom.DOMException;

public class Obj2CsvImpl implements Obj2Csv {

	// Initializes Log
	private final Log log = LogFactory.getLog(Obj2CsvImpl.class);

	/**
	 * This method is used to convert the resulting elements (columnData, rowData, totalData and graphData) to corresponding
	 * CSV format.<br>
	 * 
	 * @param request
	 * @param response
	 * @param columnData
	 * @param filterData
	 * @param rowData
	 * @param totalData
	 * @param graphData
	 * @param screenName
	 * @throws SwtException
	 */
	public String convertObject(HttpServletRequest request,
			ArrayList<ColumnDTO> columnData, ArrayList<FilterDTO> filterData,
			ArrayList<ArrayList<ExportObject>> rowData, Collection totalData,
			ArrayList<ArrayList<GraphObject>> graphData, String screenName)
			throws SwtException {

		StringBuffer sbResultantData = null;
		// common data manager from the session
		CommonDataManager CDM = null;
		// hold the cancelExport value in CDM
		String cancelExport = ""; 
		try {
			// log debug message
			log.debug(this.getClass().getName() + " - [convertXML] Begins");
			CDM = (CommonDataManager) request.getSession()
					.getAttribute("CDM");
			// Initializes String buffer to build result
			sbResultantData = new StringBuffer();
			// Appending the screen name, document's title and date with the
			// resulting data
			sbResultantData.append(screenName);
			sbResultantData.append(",");
			sbResultantData.append(SwtUtil.getWindowsTitleSuffix());
			sbResultantData.append(SwtUtil.getSysParamDate());
			sbResultantData.append(",\n\n");

			// Appending the filter values with the resulting data
			sbResultantData.append(getFilterData(filterData));
			// Appending the column values with the resulting data
			sbResultantData.append(getColumnMetaData(columnData));
			// Appending the row and column values with the resulting data
			if(rowData!=null)
			sbResultantData.append(getRowData(rowData, columnData));
			// Appending the total details (if any) in the totals column
			if(totalData!=null)
			sbResultantData.append(getTotalData(totalData, columnData));
			
			// get cancelExport from CDM
			cancelExport = CDM.getCancelExport();
			if(SwtUtil.isEmptyOrNull(cancelExport)||(!cancelExport.equals("true")))
				cancelExport="false";
			// if the export is cancelled, then return true. 
			// We will not send any file which will be treated in action part
			if (cancelExport.equals("true"))
				return "true";
			
			return sbResultantData.toString();
		}catch(OutOfMemoryError exp){
			throw new SwtException("errors.OutOfMemoryError");
		} catch (Exception ex) {
			// log error message
			log.error("Exception occured in " + this.getClass().getName()
					+ " - [convertXML]. Cause: " + ex.getMessage());
			// Re-throw as SwtException
			throw new SwtException(ex.getMessage());
		}finally{
			// Set the cancelExport attribute to false. 
			// The user could export again before changing this attribute from Flex part
			if (CDM != null) {
				CDM.setCancelExport("false");
			}
			// Cleaning unreferenced objects goes here
			sbResultantData = null;
			cancelExport = null;
		}

	}

	/**
	 * This method is used to get the string value that holds the data after
	 * enabling filter sort in Flex screens.<br>
	 * 
	 * @param filterData
	 * @return String - Filter data
	 * @throws SwtException
	 */
	public String getFilterData(ArrayList<FilterDTO> filterData)
			throws SwtException {
		// Variable to hold the filter data
		StringBuffer sbFilterDataCsv = null;

		try {
			// log debug message
			log.debug(this.getClass().getName() + " - [getFilterData] Begins");
			// Initialize String buffer to build filter data
			sbFilterDataCsv = new StringBuffer();
			// loops through the filterData to form the given data in a CSV
			// format
			Iterator it = filterData.iterator();

			while (it.hasNext()) {
				// Gets the filtered value from the NodeList
				FilterDTO filter = (FilterDTO) it.next();
				if (filter != null) {
					sbFilterDataCsv.append("\"");
					sbFilterDataCsv.append(filter.getName().replaceAll("\"",
							"\'"));
					sbFilterDataCsv.append("\",\"");
					sbFilterDataCsv.append((filter.getValue() == null ? ""
							: filter.getValue().replaceAll("\"", "\'")));
					sbFilterDataCsv.append("\",\n");
				}
			}
			// Adds a line break
			sbFilterDataCsv.append("\n");
			return sbFilterDataCsv.toString();
		} catch (DOMException ex) {
			// log error message
			log.error("DOMException occured in " + this.getClass().getName()
					+ " - [getFilterData]. Cause: " + ex.getMessage());
			// Re-throw as SwtException
			throw new SwtException(ex.getMessage());
		} catch (Exception ex) {
			// log error message
			log.error("Exception occured in " + this.getClass().getName()
					+ " - [getFilterData]. Cause: " + ex.getMessage());
			// Re-throw as SwtException
			throw new SwtException(ex.getMessage());
		} finally {
			// log debug message
			log.debug(this.getClass().getName() + " - [getFilterData] Ends");
		}
	}

	/**
	 * This method is used to get the column meta data.<br>
	 * 
	 * @param columnData
	 * @return String - Column meta data
	 * @throws SwtException
	 */
	public String getColumnMetaData(ArrayList<ColumnDTO> columnData)
			throws SwtException {
		// Holds the resultant column meta data
		StringBuffer sbColumnMetaData = null;

		try {
			// log debug message
			log.debug(this.getClass().getName()
					+ " - [getColumnMetaData] Begins");
			// Initializes columnMetaData to build column metadata
			sbColumnMetaData = new StringBuffer();
			// Iterate through columns node and get column metadata
			Iterator it = columnData.iterator();

			while (it.hasNext()) {
				ColumnDTO column = (ColumnDTO) it.next();
				sbColumnMetaData.append("\"");
				sbColumnMetaData.append(column.getHeading().replaceAll("\"",
						"\'"));
				sbColumnMetaData.append("\",");
			}
			sbColumnMetaData.append("\n");
			return sbColumnMetaData.toString();
		} catch (DOMException ex) {
			// log error message
			log.error("DOMException occured in " + this.getClass().getName()
					+ " - [getColumnMetaData]. Cause: " + ex.getMessage());
			// Re-throw as SwtException
			throw new SwtException(ex.getMessage());
		} catch (Exception ex) {
			// log error message
			log.error("Exception occured in " + this.getClass().getName()
					+ " - [getColumnMetaData]. Cause: " + ex.getMessage());
			// Re-throw as SwtException
			throw new SwtException(ex.getMessage());
		} finally {
			// log debug message
			log.debug(this.getClass().getName() + " - [getColumnMetaData] Ends");
		}
	}

	/**
	 * This method is used to get the row data.<br>
	 * 
	 * @param rowData
	 * @param columnData
	 * @return String - Row Data
	 * @throws SwtException
	 */
	public String getRowData(ArrayList<ArrayList<ExportObject>> rowData,
			ArrayList<ColumnDTO> columnData) throws SwtException {
		// Holds the row data
		StringBuffer sbResultRowData = null;
		// Declares the subNodes object that gets the child nodes from the
		// rowData
		ArrayList<ExportObject> subNodes = null;
		// common data manager from the session
		CommonDataManager CDM = null;
		// hold the cancelExport value in CDM
		String cancelExport = ""; 
		try {
			// log debug message
			log.debug(this.getClass().getName() + " - [getRowData] Begins");
			// Initializes resultRowData to build row data
			sbResultRowData = new StringBuffer();
			CDM = (CommonDataManager)	UserThreadLocalHolder.getUserSession().getAttribute("CDM");
			cancelExport = CDM.getCancelExport();
			// Iterates row and column nodes and get values for matched nodes
			Iterator it = rowData.iterator();

			while (it.hasNext()) {
				// Row sub nodes
				subNodes = (ArrayList<ExportObject>) it.next();
				if (subNodes != null) {
					Iterator itSubNodes = subNodes.iterator();
					while (itSubNodes.hasNext()) {
						
						cancelExport = CDM.getCancelExport();
						// Row sub nodes
						ExportObject data = (ExportObject) itSubNodes.next();

						if (data != null) {
							sbResultRowData.append("\"");
							if (data.getValue() != null) {
								sbResultRowData.append(data.getValue()
										.replaceAll("\"", "\'").trim());
							}
							sbResultRowData.append("\",");
						}
					}
				}
				sbResultRowData.append("\n");
			}
			return sbResultRowData.toString();
		}catch(OutOfMemoryError exp){
			throw new SwtException("errors.OutOfMemoryError");
		} catch (DOMException ex) {
			// log error message
			log.error("DOMException occured in " + this.getClass().getName()
					+ " - [getColumnMetaData]. Cause: " + ex.getMessage());
			// Re-throw as SwtException
			throw new SwtException(ex.getMessage());
		} catch (Exception ex) {
			// log error message
			log.error("Exception occured in " + this.getClass().getName()
					+ " - [getColumnMetaData]. Cause: " + ex.getMessage());
			// Re-throw as SwtException
			throw new SwtException(ex.getMessage());
		} finally {
			// log debug message
			log.debug(this.getClass().getName() + " - [getRowData] Ends");
		}
	}

	/**
	 * This method is used to get the total data.<br>
	 * 
	 * @param totalData
	 * @param columnData
	 * @return String - Total Data
	 * @throws SwtException
	 */
	public String getTotalData(Collection totalData,
			ArrayList<ColumnDTO> columnData) throws SwtException {
		// Holds the total data
		StringBuffer sbResultTotalData = null;
		// Declares the subNodes object that gets the child nodes from the

		try {
			// log debug message
			log.debug(this.getClass().getName() + " - [getTotalData] Begins");
			// Initializes resultTotalData to build totals data
			sbResultTotalData = new StringBuffer();
			// Iterates total and column nodes and get values for matched nodes

			if(totalData!=null){

				// Add totals column title
				sbResultTotalData.append("\"");
				sbResultTotalData.append("total");
				sbResultTotalData.append("\",");

				Iterator it = totalData.iterator();
				ExportObject row = (ExportObject) it.next();;
				// Add totals value
				// Main population of totals data
				for (int colCount = 1; colCount < columnData.size()
						; colCount++) {
					if(columnData.get(colCount).getDataElement().
							equalsIgnoreCase(row.getColumnName()))
					{	


					if (row.getValue() != null) {

						// If field matches, then get value (total data)
						if (columnData.get(colCount).getDataElement() != null) {
							sbResultTotalData.append("\"");
							sbResultTotalData.append(row.getValue().replaceAll("\"",
									"\'"));
						}
						sbResultTotalData.append("\",");
					}
					if(it.hasNext())
						row = (ExportObject) it.next();
					}else {						
						sbResultTotalData.append("\" \",");
				}

				
				}
			}
			return sbResultTotalData.toString();
		} catch (DOMException ex) {
			// log error message
			log.error("DOMException occured in " + this.getClass().getName()
					+ " - [getTotalData]. Cause: " + ex.getMessage());
			// Re-throw as SwtException
			throw new SwtException(ex.getMessage());
		} catch (Exception ex) {
			// log error message
			log.error("Exception occured in " + this.getClass().getName()
					+ " - [getTotalData]. Cause: " + ex.getMessage());
			// Re-throw as SwtException
			throw new SwtException(ex.getMessage());
		} finally {
			// log debug message
			log.debug(this.getClass().getName() + " - [getTotalData] Ends");
		}
	}
}