<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version last-->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="Data" pageWidth="595" pageHeight="842" whenNoDataType="NoDataSection" columnWidth="575" columnSpacing="5" leftMargin="10" rightMargin="10" topMargin="10" bottomMargin="10" scriptletClass="ar.com.fdvs.dj.core.DJDefaultScriptlet" whenResourceMissingType="Key" uuid="dd519195-158a-4da6-b086-6a58fc8ead81">
	<property name="net.sf.jasperreports.awt.ignore.missing.font" value="true"/>
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="One Empty Record"/>
	<style name="dj_style_1_" mode="Transparent" forecolor="#000000" backcolor="#FFFFFF" radius="0" hAlign="Left" vAlign="Bottom" rotation="None" isBlankWhenNull="true" fontName="Arial" fontSize="11" isBold="true" isItalic="false" isUnderline="false" isPdfEmbedded="false">
		<box padding="2">
			<pen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
		</box>
	</style>
	<style name="reportSubtitleStyle" mode="Transparent" forecolor="#000000" backcolor="#FFFFFF" radius="0" hAlign="Left" vAlign="Bottom" rotation="None" isBlankWhenNull="true" fontName="Arial" fontSize="10" isBold="false" isItalic="false" isUnderline="false" isPdfEmbedded="false">
		<box padding="2">
			<pen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
		</box>
	</style>
	<style name="dj_style_2_" mode="Opaque" forecolor="#FFFFFF" backcolor="#187EE7" radius="0" hAlign="Center" vAlign="Top" rotation="None" isBlankWhenNull="true" fontName="Arial" fontSize="10" isBold="false" isItalic="false" isUnderline="false" isPdfEmbedded="false">
		<box padding="0" topPadding="2" bottomPadding="2">
			<pen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
		</box>
	</style>
	<style name="dj_style_3_" mode="Opaque" forecolor="#000000" backcolor="#FFFFFF" radius="0" hAlign="Center" vAlign="Top" rotation="None" isBlankWhenNull="true" fontName="Arial" fontSize="10" isBold="false" isItalic="false" isUnderline="false" isPdfEmbedded="false">
		<box padding="2" leftPadding="1">
			<pen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
		</box>
	</style>
	<style name="Alternate_Verdana_for_detail_sample" mode="Opaque" forecolor="#000000" backcolor="#FFFFFF" radius="0" hAlign="Center" vAlign="Top" rotation="None" isBlankWhenNull="true" fontName="Arial" fontSize="10" isBold="false" isItalic="false" isUnderline="false" isPdfEmbedded="false">
		<box padding="2" leftPadding="1">
			<pen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
		</box>
		<conditionalStyle>
			<conditionExpression><![CDATA[new java.lang.Boolean(((Number)$V{REPORT_COUNT}).doubleValue() % 2 == 0)]]></conditionExpression>
			<style mode="Opaque" backcolor="#E0F0FF"/>
		</conditionalStyle>
	</style>
	<style name="dj_style_4_" mode="Transparent" forecolor="#000000" backcolor="#FFFFFF" radius="0" hAlign="Center" vAlign="Top" rotation="None" isBlankWhenNull="true" fontName="Arial" fontSize="10" isBold="false" isItalic="false" isUnderline="false" isPdfEmbedded="false">
		<box padding="2">
			<pen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
		</box>
	</style>
	<style name="dj_style_5_" mode="Opaque" forecolor="#FFFFFF" backcolor="#187EE7" radius="0" hAlign="Center" vAlign="Top" rotation="None" isBlankWhenNull="true" fontName="Arial" fontSize="10" isBold="false" isItalic="false" isUnderline="false" isPdfEmbedded="false">
		<box padding="2">
			<pen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
		</box>
	</style>
	<parameter name="chartImageBase64" class="java.lang.String" isForPrompting="false"/>
	<parameter name="legendImageBase64" class="java.lang.String" isForPrompting="false"/>
	<field name="field_sample" class="java.lang.String"/>
	<pageHeader>
		<band/>
	</pageHeader>
	<columnHeader>
		<band height="60">
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement uuid="3900b1ed-**************-fee5160a3f23" key="header_tf_grp_scenario" style="dj_style_5_" positionType="Float" x="0" y="5" width="575" height="25" isPrintWhenDetailOverflows="true"/>
				<textElement/>
				<textFieldExpression><![CDATA["Header Group Scenario"]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement uuid="38b825db-0910-4256-a6c1-424adbf96e02" key="header_tf_sample" style="dj_style_2_" positionType="Float" stretchType="RelativeToTallestObject" x="0" y="30" width="575" height="30" isPrintWhenDetailOverflows="true"/>
				<textElement/>
				<textFieldExpression><![CDATA["Header Sample"]]></textFieldExpression>
			</textField>
		</band>
	</columnHeader>
	<detail>
		<band height="8" splitType="Immediate">
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement uuid="4a469409-8c84-4c48-80ef-5124e1bb503d" key="detail_tf_sample" style="Alternate_Verdana_for_detail_sample" positionType="Float" stretchType="RelativeToTallestObject" x="0" y="0" width="575" height="8" isPrintWhenDetailOverflows="true"/>
				<textElement markup="styled"/>
				<textFieldExpression><![CDATA[$F{field_sample}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<pageFooter>
		<band height="31" splitType="Stretch">
			<textField pattern="" isBlankWhenNull="false">
				<reportElement uuid="c9f15137-34a4-4faa-b84f-f601c86c0772" key="dateField" x="2" y="8" width="100" height="19" forecolor="#000000" backcolor="#FFFFFF"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement>
					<font size="10"/>
				</textElement>
				<textFieldExpression><![CDATA[new Date()]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="false">
				<reportElement uuid="132b776e-6c2d-4eaa-9e64-374f47ddc7b9" key="pageNumber1" x="380" y="8" width="170" height="19" forecolor="#000000" backcolor="#FFFFFF"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right">
					<font size="10"/>
				</textElement>
				<textFieldExpression><![CDATA["Page " + $V{PAGE_NUMBER} + " of "+" "]]></textFieldExpression>
			</textField>
			<textField evaluationTime="Report" pattern="" isBlankWhenNull="false">
				<reportElement uuid="8fbed5b5-d943-4475-a200-1ce98e90ca16" key="pageNumber2" x="548" y="8" width="36" height="19" forecolor="#000000" backcolor="#FFFFFF"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement>
					<font size="10"/>
				</textElement>
				<textFieldExpression><![CDATA["  " + $V{PAGE_NUMBER}]]></textFieldExpression>
			</textField>
		</band>
	</pageFooter>
	<noData>
		<band height="130">
			<textField isStretchWithOverflow="true">
				<reportElement uuid="863fe889-151a-414e-be1f-e5a65ba67a90" style="dj_style_4_" positionType="Float" stretchType="RelativeToTallestObject" x="0" y="80" width="575" height="50"/>
				<textElement/>
				<textFieldExpression><![CDATA["NO_DATA_FOUND"]]></textFieldExpression>
			</textField>
		</band>
	</noData>
</jasperReport>
