package org.swallow.export.service.impl;

import java.awt.Color;
import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.text.DecimalFormat;
import java.text.DecimalFormatSymbols;
import java.util.AbstractMap;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Map.Entry;

import javax.imageio.ImageIO;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.thoughtworks.xstream.io.xml.StaxDriver;
import net.sf.jasperreports.engine.type.*;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.export.model.FilterDTO;
import org.swallow.util.CommonDataManager;
import org.swallow.util.SwtConstants;
import org.swallow.util.SwtUtil;

import com.thoughtworks.xstream.XStream;
import com.thoughtworks.xstream.converters.Converter;
import com.thoughtworks.xstream.converters.MarshallingContext;
import com.thoughtworks.xstream.converters.UnmarshallingContext;
import com.thoughtworks.xstream.io.HierarchicalStreamReader;
import com.thoughtworks.xstream.io.HierarchicalStreamWriter;

import net.sf.jasperreports.engine.JRBand;
import net.sf.jasperreports.engine.JRDataSource;
import net.sf.jasperreports.engine.JRElement;
import net.sf.jasperreports.engine.JREmptyDataSource;
import net.sf.jasperreports.engine.JRException;
import net.sf.jasperreports.engine.JRExporterParameter;
import net.sf.jasperreports.engine.JRLineBox;
import net.sf.jasperreports.engine.JRParameter;
import net.sf.jasperreports.engine.JasperCompileManager;
import net.sf.jasperreports.engine.JasperFillManager;
import net.sf.jasperreports.engine.JasperPrint;
import net.sf.jasperreports.engine.JasperReport;
import net.sf.jasperreports.engine.data.JRMapArrayDataSource;
import net.sf.jasperreports.engine.design.JRDesignBand;
import net.sf.jasperreports.engine.design.JRDesignConditionalStyle;
import net.sf.jasperreports.engine.design.JRDesignExpression;
import net.sf.jasperreports.engine.design.JRDesignField;
import net.sf.jasperreports.engine.design.JRDesignStyle;
import net.sf.jasperreports.engine.design.JRDesignTextField;
import net.sf.jasperreports.engine.design.JasperDesign;
import net.sf.jasperreports.engine.export.JRCsvExporter;
import net.sf.jasperreports.engine.export.JRCsvExporterParameter;
import net.sf.jasperreports.engine.export.JRPdfExporter;
import net.sf.jasperreports.engine.export.JRXlsExporterParameter;
import net.sf.jasperreports.engine.export.ooxml.JRXlsxExporter;
import net.sf.jasperreports.engine.xml.JRXmlLoader;
/**
 * ILMReporting.java, this class is responsible on reporting in the ILM facility
 * 
 * <AUTHOR> Chebka, SwallowTech Tunisia
 *
 */
public class ILMReporting {
	public static final String SERIES_COVER_TEMPLATE_PATH = "/org/swallow/export/service/impl/SeriesExport_CoverTemplate.jrxml";
	public static final String SERIES_DATA_TEMPLATE_PATH = "/org/swallow/export/service/impl/SeriesExport_DataTemplate.jrxml";
	
	public static  String PDF_EXPORT = "pdf";
	public static  String CSV_EXPORT = "csv";
	public static  String XLS_EXPORT = "excel";
	private  String exportType = null;
	protected static CommonDataManager CDM;
	private static String cancelExport = "";
	private String ccyPattern;
	private static Log log = LogFactory.getLog(ILMReporting.class);
	
	/**
	 * Exports the group analysis screen
	 * @param response
	 * @param exportType
	 * @param filePrefix
	 * @throws SwtException
	 */
	public void exportGroupAnalysis(HttpServletRequest request, HttpServletResponse response , List<ColumnInfo> columns, ArrayList<FilterDTO> filterData, List<String> data, String exportType, String filePrefix) throws SwtException {
		
		String chartImageDataBase64 = data.get(0);
		String legendImageDataBase64 = data.get(1);
		String reportData = data.get(2);
		String styledSubTitle = null;
		JasperReport jrData = null;
		JasperReport jrCover = null;
		JasperDesign dataDesign = null;
		JasperPrint jpData = null;
		
		try{
			//get common data from request
			CDM = (CommonDataManager) request.getSession()
					.getAttribute("CDM");
			this.exportType = exportType;
			// Get the design
			
			HashMap parameters = new HashMap();
//			parameters.put("rightHeader", "Right Header?");
//			parameters.put("leftHeader", "Left Header?");
			parameters.put("chartImageBase64", chartImageDataBase64);
			if (!SwtUtil.isEmptyOrNull(legendImageDataBase64)) {
				parameters.put("legendImageBase64", legendImageDataBase64);
			}
			if(!exportType.equalsIgnoreCase("pdf"))
				parameters.put(JRParameter.IS_IGNORE_PAGINATION, Boolean.TRUE);
			
		
			
			ccyPattern = SwtUtil.getCurrentCurrencyFormat(request.getSession());
			if(!exportType.equals(PDF_EXPORT))
				{
					dataDesign = alterDataDesign(columns);
					jrData =  JasperCompileManager.compileReport(dataDesign);
		 			List<Map> dataList = xmlDataToMapList(reportData);
		 			JRDataSource dataSource = new JRMapArrayDataSource(dataList.toArray());
		 			jpData = JasperFillManager.fillReport(jrData, parameters, dataSource);
		 			if(columns.size()>7){
		 				jpData.setOrientation(OrientationEnum.LANDSCAPE);
		 			}
				}
			
			//TODO: We should get the design if we want to modify it
			jrCover =  JasperCompileManager.compileReport(ILMReporting.class.getResourceAsStream(SERIES_COVER_TEMPLATE_PATH));
			
			
			JRBand band = jrCover.getDetailSection().getBands()[0];
			JRElement column = band.getElementByKey("imageChartKey");
			
			if(exportType.equalsIgnoreCase(XLS_EXPORT)) {
				byte[] imageBytes = Base64.decodeBase64(chartImageDataBase64);
				BufferedImage image = ImageIO.read(new ByteArrayInputStream(imageBytes));
				int imageHeight = image.getHeight();
				int imageWidth = image.getWidth();
				column.setWidth(imageWidth);
			}
			
	
	
		
			
			styledSubTitle = new String();
			for (int i = 0; i < filterData.size(); i++) {
				// Adds the filter data to the paragraph object
				if(exportType.equals(CSV_EXPORT))
					styledSubTitle+= filterData.get(i).getName() +": "+SwtUtil.convertXMLChars(filterData.get(i).getValue())+"";
				else{
					if(SwtUtil.isEmptyOrNull(filterData.get(i).getValue()))
							filterData.get(i).setValue("");
					styledSubTitle+="<style isBold='true' pdfFontName='Helvetica-Bold'>"+filterData.get(i).getName() +": </style>"+SwtUtil.convertXMLChars(filterData.get(i).getValue().replace("\n", "").replace("\r", ""))+"     ";
				
				}
			}
			parameters.put("styledSubTitle", styledSubTitle);

 			JasperPrint jp_cover = JasperFillManager.fillReport(jrCover, parameters, new JREmptyDataSource());
 			jp_cover.setOrientation(OrientationEnum.LANDSCAPE);
 			jp_cover.setPageHeight(595);
 			jp_cover.setPageWidth(842);
 			
 			List<JasperPrint> jasperPrints = new ArrayList<JasperPrint>();
 		    if(!exportType.equalsIgnoreCase("csv"))
 		    	if(!exportType.equalsIgnoreCase("pdf"))
 		    		jasperPrints.addAll(Arrays.asList(jp_cover,jpData));
 		    	else
 		    		jasperPrints.addAll(Arrays.asList(jp_cover));
 		    
 		    else
 		    	jasperPrints.addAll(Arrays.asList(jpData));
 		    	
 			String tokenForDownload = null;
 			// .......... Export PDF Report ......//
 			// downloadTokenValue will have been provided in the form submit via the hidden input field
			// Source scan tools may report a "HTTP response splitting" vulnerability. A generic solution of
			// ignoring text after CRLFs is implemented in XSSFilter class, so can safely ignore the report.
 			tokenForDownload = request.getParameter("tokenForDownload");
 			response.addCookie(new Cookie("fileDownloadToken", tokenForDownload+"|OK")); 
 		    writeResponse(response, jasperPrints, exportType, filePrefix);
 		   
		} catch (Exception e) {
			if (e.getMessage().contains("addCancelExportFunction")) {
				throw new SwtException("generatedException");
			} else
				throw SwtErrorHandler.getInstance().handleException(e,
						"exportGroupAnalysis", ILMReporting.class);
		} 
		
	}
	
	/**
	 * Writes exported report to servlet output stream
	 * @param response
	 * @param report
	 */
	private void writeResponse(HttpServletResponse response, List<JasperPrint> jasperPrints, String exportType, String filePrefix) throws JRException, IOException, SwtException{
		ServletOutputStream out = response.getOutputStream();
		// Token for download corresponding file
		
		if (exportType.equalsIgnoreCase(PDF_EXPORT)) {
			// To set the output type as PDF file
			response.setContentType("application/pdf");
			// To set the content as attachment
			response.setHeader("Content-disposition",
					"attachment; filename=" + filePrefix + "_"
							+ SwtUtil.formatDate(new Date(), SwtConstants.SwtFormatDate)
							+ ".pdf");
			// Intialising th ePDFExporter.
			JRPdfExporter exporter = new JRPdfExporter();
			exporter.setParameter(JRExporterParameter.JASPER_PRINT_LIST, jasperPrints);
			// Providing the output stream
			exporter.setParameter(JRExporterParameter.OUTPUT_STREAM, out);

			// Exporting as UTF-8
			exporter.setParameter(JRExporterParameter.CHARACTER_ENCODING,
					"UTF-8");
			exporter.exportReport();
			log.info("Report PDF file");
		}
		
		// ......... Export XLS Report ........//
		if (exportType.equalsIgnoreCase(XLS_EXPORT)) {
			log.info("Report XLS file");
			// To set the output type as PDF file
			response.setContentType("application/xls");
			// To set the content as attachment
			response.setHeader("Content-disposition",
					"attachment; filename=" + filePrefix + "_"
							+ SwtUtil.formatDate(new Date(), SwtConstants.SwtFormatDate)
							+ ".xlsx");

			
			
			JRXlsxExporter exportXls = new JRXlsxExporter();
			// to display all page in per sheet
			exportXls.setParameter(
					JRXlsExporterParameter.IS_ONE_PAGE_PER_SHEET,
					Boolean.FALSE);
			exportXls.setParameter(
					JRXlsExporterParameter.IS_DETECT_CELL_TYPE,
					Boolean.TRUE);

			exportXls.setParameter(
					JRXlsExporterParameter.IS_WHITE_PAGE_BACKGROUND,
					Boolean.FALSE);
			exportXls
					.setParameter(
							JRXlsExporterParameter.IS_REMOVE_EMPTY_SPACE_BETWEEN_ROWS,
							Boolean.TRUE);
			
			exportXls.setParameter(
					JRXlsExporterParameter.IS_COLLAPSE_ROW_SPAN,
					Boolean.TRUE);
			exportXls.setParameter(JRXlsExporterParameter.JASPER_PRINT_LIST, jasperPrints);
			exportXls.setParameter(JRXlsExporterParameter.OUTPUT_STREAM,
					out);

			exportXls.exportReport();
		}
		// ........ Export CSV Report ...........//
		if (exportType.equalsIgnoreCase(CSV_EXPORT)) {

			// To set the output type as CSV file
			response.setContentType("application/csv");
			// To set the content as attachment
			response.setHeader("Content-disposition",
					"attachment; filename=" + filePrefix + "_"
							+ SwtUtil.formatDate(new Date(), SwtConstants.SwtFormatDate)
							+ ".csv");

			QuotedCsvExporter exportCSV = new QuotedCsvExporter();
			exportCSV.setParameter(JRCsvExporterParameter.OUTPUT_STREAM,
					out);
			// Added the field delimiter (;) for the right display in Excel
			exportCSV.setParameter(JRCsvExporterParameter.FIELD_DELIMITER, ",");
			exportCSV.setParameter(JRCsvExporterParameter.JASPER_PRINT_LIST, jasperPrints);
			
			exportCSV
					.setParameter(
							JRCsvExporterParameter.CHARACTER_ENCODING,
							"ISO-8859-1");
			exportCSV.exportReport();
		}
	}
	
	
	private JRLineBox setSolidBorderForBox (JRLineBox box) {
		
		box.getTopPen().setLineStyle(LineStyleEnum.SOLID);
		box.getBottomPen().setLineStyle(LineStyleEnum.SOLID);
		box.getRightPen().setLineStyle(LineStyleEnum.SOLID);
		box.getLeftPen().setLineStyle(LineStyleEnum.SOLID);
		box.getTopPen().setLineWidth(new Float("0.5"));
		box.getBottomPen().setLineWidth(new Float("0.5"));
		box.getRightPen().setLineWidth(new Float("0.5"));
		box.getLeftPen().setLineWidth(new Float("0.5"));
		box.getTopPen().setLineColor(Color.black);
		box.getBottomPen().setLineColor(Color.black);
		box.getRightPen().setLineColor(Color.black);
		box.getLeftPen().setLineColor(Color.black);
		
		return box;
		
	}
	
	
	public JasperDesign alterDataDesign(List<ColumnInfo> columns) throws Exception{
		JasperDesign design = JRXmlLoader.load(ILMReporting.class.getResourceAsStream(SERIES_DATA_TEMPLATE_PATH));
		// Get bands
		JRDesignBand column_header_band = (JRDesignBand)design.getColumnHeader();  
		JRDesignBand detail_band = (JRDesignBand)design.getDetailSection().getBands()[0];
		
		JRDesignTextField header_tf_grp_scenario = (JRDesignTextField)column_header_band.getElementByKey("header_tf_grp_scenario");
		JRDesignTextField header_tf_sample = (JRDesignTextField)column_header_band.getElementByKey("header_tf_sample");
		JRDesignTextField detail_tf_sample = (JRDesignTextField)detail_band.getElementByKey("detail_tf_sample");
		JRDesignTextField pageNumber1 = (JRDesignTextField)design.getPageFooter().getElementByKey("pageNumber1");
		JRDesignTextField pageNumber2 = (JRDesignTextField)design.getPageFooter().getElementByKey("pageNumber2");
		JRDesignTextField DateField = (JRDesignTextField)design.getPageFooter().getElementByKey("dateField");
		// Remove sample elements
		column_header_band.removeElement(header_tf_grp_scenario);
		column_header_band.removeElement(header_tf_sample);
		detail_band.removeElement(detail_tf_sample);
		design.removeField("field_sample");
		int columnWidthes = 0;
		
		int x = header_tf_sample.getX();
		int xgroup_sc = header_tf_grp_scenario.getX()+SwtConstants.ILM_EXPORT_COLUMN_WIDTH;
		int pageWidth = 0;
		JRDesignExpression expression;
		String groupId =columns.get(0).getGroupId();
		String scenarioId =columns.get(0).getScenarioId();
		ArrayList<Integer> arrX = new ArrayList<Integer>();
		ArrayList<Integer> arrW = new ArrayList<Integer>();
		ArrayList<String> arrName = new ArrayList<String>();
		arrX.add(xgroup_sc);
		arrName.add(groupId+" "+scenarioId);
		
		// Loop on all columns and construct the new items
		for(ColumnInfo column:columns){
			if(!groupId.equalsIgnoreCase(column.getGroupId())|| !scenarioId.equals(column.getScenarioId())){
				groupId = column.getGroupId();
				scenarioId = column.getScenarioId();
				//store X 
				arrX.add(x);
				arrW.add(columnWidthes);
				arrName.add(groupId+" "+scenarioId);
				columnWidthes = 0;
			}
			// Clone the header_tf_sample element
			JRDesignTextField header_tf_sample_clone = (JRDesignTextField)header_tf_sample.clone();
			header_tf_sample_clone.setX(x);
			header_tf_sample_clone.setWidth(column.getFieldLength());
			// Add an expression to the text field
			expression = new JRDesignExpression();
			// Sets the type of data for the rows of data
			expression.setText("\""+column.getFieldLabel()+"\"");
			header_tf_sample_clone.setExpression(expression);
			
			column_header_band.addElement(header_tf_sample_clone);
			// Clone the detail_tf_sample element
			JRDesignTextField detail_tf_sample_clone = (JRDesignTextField)detail_tf_sample.clone();

			detail_tf_sample_clone.setX(x);
			detail_tf_sample_clone.setWidth(column.getFieldLength());
			
			// Add an expression to the text field
			expression = new JRDesignExpression();
			// Sets the type of data for the rows of data
			if(!"timeSlot".equalsIgnoreCase(column.getFieldName()))
				expression.setValueClassName(Double.class.getName());
			else 
				expression.setValueClassName(String.class.getName());
			//expression.setText("$F{" + column.getFieldName() + "}");
			String patt = CDM.getCurrencyFormat();
			//expression.setText("(($F{" + column.getFieldName() + "}!=null) ?new String(\""+patt+"\").format($F{" + column.getFieldName() + "}):\"\")");
			
			// Adding a conditional style when column is not timeStlot
			if(!exportType.equals(CSV_EXPORT)){
				if(!"timeSlot".equalsIgnoreCase(column.getFieldName())){
					detail_tf_sample_clone.setHorizontalTextAlign(HorizontalTextAlignEnum.RIGHT);
					JRLineBox box = detail_tf_sample_clone.getLineBox();
					
					box = setSolidBorderForBox(box);
					
					// Note: We are not able to move this part of code into jrxml because jasper report does not support a variable that denotes the current field content (comment is added by Saber Chebka on 14-05-2014)
					expression.setText("$F{" + column.getFieldName() + "}.equals(\"\")?null:Double.parseDouble (new "+getClass().getName()+".ReportFormatter().addCancelExportFunction($F{" + column.getFieldName() + "}))");
					JRDesignStyle fieldStyle = new JRDesignStyle();
					fieldStyle.setName(column.getFieldName() + "Style");
					fieldStyle.setFontName("Arial");
					JRDesignExpression conditionExpression = new JRDesignExpression();
					JRDesignConditionalStyle cs = new JRDesignConditionalStyle();
					conditionExpression.setValueClass(java.lang.Boolean.class);
					conditionExpression.setText("new Boolean($F{"
							+ column.getFieldName() + "}.startsWith(\"-\"))");
					cs.setConditionExpression(conditionExpression);
					cs.setForecolor(Color.red);
					fieldStyle.addConditionalStyle(cs);
					JRDesignExpression conditionExpression2 = new JRDesignExpression();
					JRDesignConditionalStyle cs1 = new JRDesignConditionalStyle();
					conditionExpression2
							.setText("new java.lang.Boolean(((Number)$V{REPORT_COUNT}).doubleValue() % 2 == 0)");
					cs1.setConditionExpression(conditionExpression2);
					cs1.setMode(ModeEnum.OPAQUE);
					cs1.setBackcolor(new Color(224, 240, 255));
					fieldStyle.addConditionalStyle(cs1);
					design.addStyle(fieldStyle);
	
				    detail_tf_sample_clone.setStyle(fieldStyle);
				}else{
					expression.setText("$F{" + column.getFieldName() + "}");
				}
			}else {
				if(!"timeSlot".equalsIgnoreCase(column.getFieldName()))
					expression.setText("new "+getClass().getName()+".ReportFormatter().formatNumber(\""+column.getFieldName()+"\",\""+ccyPattern+"\",$F{" + column.getFieldName() + "})");
				else
					expression.setText("$F{" + column.getFieldName() + "}");
			}
			
			detail_tf_sample_clone.setExpression(expression);
		
			
			detail_band.addElement(detail_tf_sample_clone);

			// Add fields to the design
			JRDesignField field = new JRDesignField();
			field.setName(column.getFieldName() );
			field.setValueClassName(String.class.getName());
			design.addField(field);
			
			// Calculte x and columnWidthes
			x = x + column.getFieldLength();
			columnWidthes+=column.getFieldLength();
			pageWidth+=column.getFieldLength();
			
		}
		arrW.add(columnWidthes);
		
	
		for (int i=0;i<arrName.size();i++){
			JRDesignTextField header_tf_grp_scenario_clone = (JRDesignTextField)header_tf_grp_scenario.clone();
			header_tf_grp_scenario_clone.setX(arrX.get(i));
			header_tf_grp_scenario_clone.setWidth(arrW.get(i));
			// Add an expression to the text field
			expression = new JRDesignExpression();
			// Sets the type of data for the rows of data
			expression.setText("\""+arrName.get(i)+"\"");
			header_tf_grp_scenario_clone.setExpression(expression);
			column_header_band.addElement(header_tf_grp_scenario_clone);
		}
		if (pageWidth < 575) 
			design.setPageWidth(595);
		else 
			design.setPageWidth(pageWidth+20);
		if(exportType.equals(PDF_EXPORT)){
			pageNumber1.setX(design.getPageWidth() - 215);
			pageNumber2.setX(design.getPageWidth() - 47);
		}else {
			((JRDesignBand)design.getPageFooter()).removeElement(pageNumber1);
			((JRDesignBand)design.getPageFooter()).removeElement(pageNumber2);
			((JRDesignBand)design.getPageFooter()).removeElement(DateField);
		}
		
		
		return design;
	} 
/**
	 * Report data formatter class
	 * 
	 * <AUTHOR> Chebka, Med Amine B.ahmed
	 * @version 1.0
	 */
	public static class ReportFormatter{

		Locale locale = new Locale("en", "UK");
		String patternbb = "#,##0.###";
		DecimalFormatSymbols symbols = new DecimalFormatSymbols(locale);

		public String formatNumber(String textField, String pattern, Object data) {
			String formattedResult = "";
			String dataStr = null;
			try {
				if(data!=null){
					dataStr = (java.lang.String)data;
					double dataDbl ;
					// Format the double values
					if("currencyPat1".equalsIgnoreCase(pattern)){
						dataDbl = Double.parseDouble(dataStr);
						symbols.setDecimalSeparator('.');
						symbols.setGroupingSeparator(',');
						DecimalFormat decimalFormat = new DecimalFormat(patternbb, symbols);
						formattedResult = decimalFormat.format(dataDbl);				
					}else{
						dataDbl = Double.parseDouble(dataStr);
						symbols.setDecimalSeparator(',');
						symbols.setGroupingSeparator('.');
						DecimalFormat decimalFormat = new DecimalFormat(patternbb, symbols);
						formattedResult = decimalFormat.format(dataDbl);
					}
				}
				
			} catch (Exception e) {
				log.error(this.getClass().getName() + "- [formatNumber] - Exception when formating "+data.toString()+" for "+textField+" column using "+pattern+" pattern.");
			}
			
			return formattedResult;
		}

		public String addCancelExportFunction(Object data) throws SwtException {
			cancelExport = CDM.getCancelExport();
			if (cancelExport.equals(SwtConstants.STR_TRUE)){
				throw new SwtException("generatedException");
			}
			return data.toString();
		}
	}
	
	
	
	/**
	 * Converts data from XML string into a Map list
	 * @param data
	 * @return
	 */
	public List<Map> xmlDataToMapList(String data){
		XStream xstream = new XStream(new StaxDriver());
        xstream.alias("result", Map.class);
        xstream.alias("dataprovider", List.class);
        xstream.registerConverter(new MapEntryConverter());  
        return (ArrayList)xstream.fromXML(data);
	}
	
	public static class MapEntryConverter implements Converter{
		public boolean canConvert(Class clazz) {
		    return AbstractMap.class.isAssignableFrom(clazz);
		}

		public void marshal(Object value, HierarchicalStreamWriter writer, MarshallingContext context) {
		    AbstractMap<String,String> map = (AbstractMap<String,String>) value;
		    for (Entry<String,String> entry : map.entrySet()) {
		        writer.startNode(entry.getKey().toString());
		        writer.setValue(entry.getValue().toString());
		        writer.endNode();
		    }
		}

		public Object unmarshal(HierarchicalStreamReader reader, UnmarshallingContext context) {
		    Map<String, String> map = new HashMap<String, String>();

		    while(reader.hasMoreChildren()) {
		        reader.moveDown();
		        map.put(reader.getNodeName(), reader.getValue());
		        reader.moveUp();
		    }
		    return map;
		}
	}
	
	/**
	 * Model class holding fields metadata
	 * <AUTHOR>
	 *
	 */
	public static class ColumnInfo implements Comparable<ColumnInfo>{
		String fieldName;
		String fieldId;
		String fieldLabel;
		int fieldLength = SwtConstants.ILM_EXPORT_COLUMN_WIDTH;
		String groupId = "";
		String scenarioId = "";
		
		Map<String, String> seriesLabels = new HashMap<String, String>() {
			{
				put("ab", "Act Bal");
				put("aac", "Accum Actual C");
				put("aad", "Accum Actual D");
				put("afc", "Accum Fcast C");
				put("afd", "Accum Fcast D");
				put("fbb", "Fcast (basic)");
				put("fbia","Fcast (incl actuals)");
				put("timeSlot","Timeslot");
			}
		};
		Map<String, Integer> seriesOrder = new HashMap<String, Integer>() {
			{
				put("timeSlot",1);
				put("ab", 2);
				put("fbb", 3);
				put("fbia",4);
				put("aac", 5);
				put("aad", 6);
				put("afc", 7);
				put("afd", 8);
				put("NO_MORE_LIQUIDITY_AVAILABLE", 9);
				put("SUM_OTHER_TOTAL", 10);
				put("SUM_COLLATERAL", 11);
				put("SUM_CREDIT_LINE_TOTAL", 12);
				put("SUM_UN_LIQUID_ASSETS", 13);
			}
		};
				
		public ColumnInfo(String yField) {
			super();
			if (yField.equals("timeSlot"))
			{
				this.fieldId = yField ;
				this.fieldName = yField; 
			}
			else {
				String[] stringList = null;
				stringList = yField.split("\\.");
				this.groupId = stringList[0];
				this.scenarioId = stringList[1];
				this.fieldId = stringList[2];
				this.fieldName = yField;
			}
			
			this.fieldLabel = seriesLabels.get(fieldId);
		}
		public String getFieldName() {
			return fieldName;
		}
		public void setFieldName(String fieldName) {
			this.fieldName = fieldName;
		}
		public String getFieldLabel() {
			return fieldLabel;
		}
		public void setFieldLabel(String fieldLabel) {
			this.fieldLabel = fieldLabel;
		}
		public int getFieldLength() {
			return fieldLength;
		}
		public void setFieldLength(int fieldLength) {
			this.fieldLength = fieldLength;
		}

		public String getGroupId() {
			return groupId;
		}
		public void setGroupId(String groupId) {
			this.groupId = groupId;
		}
		public String getScenarioId() {
			return scenarioId;
		}
		public void setScenarioId(String scenarioId) {
			this.scenarioId = scenarioId;
		}
		/**
		 * @return the fieldId
		 */
		public String getFieldId() {
			return fieldId;
		}
		/**
		 * @param fieldId the fieldId to set
		 */
		public void setFieldId(String fieldId) {
			this.fieldId = fieldId;
		}
		private int compareColumns(ColumnInfo column){
			return seriesOrder.get(fieldId)-column.seriesOrder.get(column.getFieldId());
		}
		private int compareGroupId(ColumnInfo column){
			return groupId.compareTo(column.getGroupId());
		}
		private int compareScenarioId(ColumnInfo column){
			return scenarioId.compareTo(column.getScenarioId());
		}
		
		public int compareTo(ColumnInfo column) {
			if (groupId == null
					|| (this.groupId.equals(column.getGroupId()) && this.scenarioId
							.equals(column.getScenarioId())))
				return compareColumns(column);
			else if (this.groupId.equals(column.getGroupId())) {
				if (scenarioId.equals("Standard")) 
					return 1;
				else return compareScenarioId(column);
			}  
			else
				return compareGroupId(column);

		}
	}
	
	/**
	 * This class override the JRCsvExporter to allow exporting with quotes added before and after the value
	 * Here is an example of the output
	 * "2008-10-01 00:00","0","78.417.217,52","78.417.217,52","0","0","479.153.000","-400.735.782,48"
	 * <AUTHOR>
	 *
	 */
	public class QuotedCsvExporter extends JRCsvExporter
	{
		private final String QUOTE_CHAR = "\"";
		private  String ccyPattern = "\"";
	
		public String getCcyPattern() {
			return ccyPattern;
		}
		public void setCcyPattern(String ccyPattern) {
			this.ccyPattern = ccyPattern;
		}
		@Override
		    public String prepareText(String source) {
		        // this will take care of the case where there are embedded quotes,
		        // delimeters or other control characters
				String _return = super.prepareText(source);
		        // already has quotes so don't bother
		        if (_return.startsWith(QUOTE_CHAR) && _return.endsWith(QUOTE_CHAR)) {
	                return _return;
		        } else {
	                StringBuffer sb = new StringBuffer(_return);
	                sb.insert(0, QUOTE_CHAR);
	                sb.append(QUOTE_CHAR);
		 
		            return sb.toString();
		            }
		    }
	}
}