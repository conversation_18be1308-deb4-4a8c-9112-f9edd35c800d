/**
 * @(#)Obj2XmlAcctBreakdown.java 1.0 ,01/08/08
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.export.service.impl;

import java.util.ArrayList;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.swallow.exception.SwtException;
import org.swallow.export.model.ColumnDTO;
import org.swallow.export.model.FilterDTO;
import org.swallow.model.ExportObject;
import org.swallow.util.SwtConstants;
import org.swallow.util.SwtUtil;
import org.swallow.work.model.AcctBreakdownModel;
import org.swallow.work.model.AcctBreakdownMonitor;

/**
 * Obj2XmlAcctBreakdown.java
 * 
 * Obj2XmlAcctBreakdown class is used to convert the object into row data and
 * total data xml
 */
public class Obj2XmlAcctBreakdown extends Obj2XmlImpl {

	/**
	 * Reference variable of log factory
	 */
	private Log log = LogFactory.getLog(Obj2XmlAcctBreakdown.class);

	/**
	 * Default constructor
	 */
	public Obj2XmlAcctBreakdown() {

	}

	/**
	 * This method is used to form the whole data in XML format
	 * 
	 * @param columnData
	 *            ArrayList<ColumnDTO>
	 * @param filterData
	 *            ArrayList<FilterDTO>
	 * @param rowData
	 *            ArrayList
	 * @param acctBDTotal
	 *            AcctBreakdownMonitor
	 * @return String
	 * @throws Exception
	 */
	public String convertObj(ArrayList<ColumnDTO> columnData,
			ArrayList<FilterDTO> filterData,
			ArrayList<AcctBreakdownModel> rowData,
			AcctBreakdownMonitor acctBDTotal) throws Exception {
		// AcctBreakdownMonitor XML
		String acctBreakDownXML = null;
		try {
			// set the xml header
			acctBreakDownXML = "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n<data>\n";
			// set the column data
			acctBreakDownXML += getColumnData(columnData);
			// set the row data
			acctBreakDownXML += getRowData(rowData);
			// set the total data
			acctBreakDownXML += getTotalDatas(acctBDTotal);
			// set the filter data
			acctBreakDownXML += getFilterData(filterData);
			acctBreakDownXML += "</data>\n";
		} catch (Exception e) {
			// log error
			log.error(this.getClass().getName()
					+ "- [Obj2XmlAcctBreakdown] - Exception " + e.getMessage());
			throw e;
		}
		// return the acctBreakDown data
		return acctBreakDownXML;
	}

	/**
	 * This method is used to get the row data and form in XML format
	 * 
	 * @param rowData
	 *            ArrayList
	 * @return String
	 * @throws Exception
	 */
	private String getRowData(ArrayList<AcctBreakdownModel> rowData)
			throws Exception {
		// row data XML
		String rowDataXML = null;
		// AcctBreakdownModel
		AcctBreakdownModel acctBreakdownModel = null;
		try {
			// start rows tag
			rowDataXML = "<rows>\n";
			for (int i = 0; i < rowData.size(); i++) {
				// get the acctBreakdownModel
				acctBreakdownModel = (AcctBreakdownModel) rowData.get(i);
				// start row tag
				rowDataXML += "<row>\n";
				// start ccy tag
				rowDataXML += "<ccy >"
						+ (acctBreakdownModel.getCurrencyCode() == null ? "-"
								: acctBreakdownModel.getCurrencyCode() + " ")
						+ "</ccy>\n";
				// Pdf not working if account contains special character start
				// account tag
				rowDataXML += "<account><![CDATA["
						+ (acctBreakdownModel.getAcctId() == null ? "-"
								: acctBreakdownModel.getAcctId() + " ")
						+ "]]></account>\n";
				rowDataXML += "<name><![CDATA["
						+ (acctBreakdownModel.getAcctName() == null ? "-"
								: acctBreakdownModel.getAcctName() + " ")
						+ "]]></name>\n";
				if (acctBreakdownModel.getPredBalance() != null
						&& Double.valueOf(acctBreakdownModel.getPredBalance()
								.replace(",", "").replace(".", "")) >= 0) {
					rowDataXML += "<balance negative=\"false\">"
							+ (acctBreakdownModel.getPredBalance() == null ? "-"
									: acctBreakdownModel.getPredBalance() + " ")
							+ "</balance>\n";
				} else if (acctBreakdownModel.getPredBalance() != null
						&& Double.valueOf(acctBreakdownModel.getPredBalance()
								.replace(",", "").replace(".", "")) < 0) {
					rowDataXML += "<balance negative=\"true\">"
							+ (acctBreakdownModel.getPredBalance() == null ? "-"
									: acctBreakdownModel.getPredBalance() + " ")
							+ "</balance>\n";
				} else {
					rowDataXML += "<balance negative=\"false\">"
							+ (acctBreakdownModel.getPredBalance() == null ? "-"
									: acctBreakdownModel.getPredBalance() + " ")
							+ "</balance>\n";
				}
				// start openunexpected tag
				if (acctBreakdownModel.getUnexpectedBalance() != null
						&& Double.valueOf(acctBreakdownModel
								.getUnexpectedBalance().replace(",", "")
								.replace(".", "")) >= 0) {
					rowDataXML += "<openunexpected negative=\"false\">"
							+ (acctBreakdownModel.getUnexpectedBalance() == null ? "-"
									: acctBreakdownModel.getUnexpectedBalance()
											+ " ") + "</openunexpected>\n";
				} else if (acctBreakdownModel.getUnexpectedBalance() != null
						&& Double.valueOf(acctBreakdownModel
								.getUnexpectedBalance().replace(",", "")
								.replace(".", "")) < 0) {
					rowDataXML += "<openunexpected negative=\"true\">"
							+ (acctBreakdownModel.getUnexpectedBalance() == null ? "-"
									: acctBreakdownModel.getUnexpectedBalance()
											+ " ") + "</openunexpected>\n";
				} else {
					rowDataXML += "<openunexpected negative=\"false\">"
							+ (acctBreakdownModel.getUnexpectedBalance() == null ? "-"
									: acctBreakdownModel.getUnexpectedBalance()
											+ " ") + "</openunexpected>\n";
				}
				// start startbalance tag
				if (acctBreakdownModel.getStartingBalance() != null
						&& Double.valueOf(acctBreakdownModel
								.getStartingBalance().replace(",", "").replace(
										".", "")) >= 0) {
					rowDataXML += "<startbalance negative=\"false\">"
							+ (acctBreakdownModel.getStartingBalance() == null ? "-"
									: acctBreakdownModel.getStartingBalance()
											+ " ") + "</startbalance>\n";
				} else if (acctBreakdownModel.getStartingBalance() != null
						&& Double.valueOf(acctBreakdownModel
								.getStartingBalance().replace(",", "").replace(
										".", "")) < 0) {
					rowDataXML += "<startbalance negative=\"true\">"
							+ (acctBreakdownModel.getStartingBalance() == null ? "-"
									: acctBreakdownModel.getStartingBalance()
											+ " ") + "</startbalance>\n";
				} else {
					rowDataXML += "<startbalance negative=\"false\">"
							+ (acctBreakdownModel.getStartingBalance() == null ? "-"
									: acctBreakdownModel.getStartingBalance()
											+ " ") + "</startbalance>\n";
				}
				// start sum tag
				rowDataXML += "<sum>"
						+ (SwtUtil.isEmptyOrNull(acctBreakdownModel.getSum()) ? "-"
								: acctBreakdownModel.getSum().contains(
										SwtConstants.CUTOFF_SUM_FLAG) ? SwtConstants.CUTOFF_SUM_FLAG
										: acctBreakdownModel.getSum() + " ")
						+ "</sum>\n";
				// end row tag
				rowDataXML += "</row>\n";
			}
			// end rows tag
			rowDataXML += "</rows>\n";

		} catch (Exception e) {
			// log error
			log.error(this.getClass().getName()
					+ "- [Obj2XmlAcctBreakdown] - Exception " + e.getMessage());
			throw e;
		} finally {
			// nullify objects
			acctBreakdownModel = null;
		}
		// return the row data
		return rowDataXML;
	}
	/**
	 * Used to create a standard form to make export
	 * 
	 * @param columnData
	 * @param filterData
	 * @param rowData
	 * @return String
	 * @throws SwtException
	 */
	public ArrayList<ArrayList<ExportObject>> getExportData(ArrayList<ColumnDTO> columnData,
			ArrayList<FilterDTO> filterData, ArrayList<AcctBreakdownModel> rowData) throws SwtException {
			/*
			 * To Generate the list of object to export
			 */
			ArrayList<ArrayList<ExportObject>> result = null;
			ArrayList<ExportObject> data  = null;
			ExportObject export  = null;
			
			// AcctBreakdownModel
			AcctBreakdownModel acctBreakdownModel = null;
			// To iterate the Default Account list
			int i;

			try {
				log.debug(this.getClass().getName() + " - [getRowData] - "
						+ "Entering");
				result = new ArrayList();
				// Iterates the rowData and get the default account object
				for (i = 0; i < rowData.size(); i++) {
					
					acctBreakdownModel = (AcctBreakdownModel) rowData.get(i);;		
					data = new ArrayList();
					
					// Sets the currency tag from currency code
					export = new ExportObject();
					export.setValue(acctBreakdownModel.getCurrencyCode() == null ? "-"
									: acctBreakdownModel.getCurrencyCode());
					export.setType(columnData.get(0).getType());
					export.setColumnName(columnData.get(0).getDataElement());					
					data.add(export);					
					// Sets the account id 
					export = new ExportObject();					
					export.setValue(acctBreakdownModel.getAcctId() == null ? "-"
									: acctBreakdownModel.getAcctId());
					export.setType(columnData.get(1).getType());
					export.setColumnName(columnData.get(1).getDataElement());
					data.add(export);
					// Set the account tag from account name
					export = new ExportObject();					
					export.setValue((acctBreakdownModel.getAcctName() == null ? "-"
							: acctBreakdownModel.getAcctName()));
					export.setType(columnData.get(2).getType());
					export.setColumnName(columnData.get(2).getDataElement());
					data.add(export);
					// Set the name tag from predict balance
					export = new ExportObject();
					export.setType(columnData.get(3).getType());
					export.setColumnName(columnData.get(3).getDataElement());
					if (acctBreakdownModel.getPredBalance() != null
							&& Double.valueOf(acctBreakdownModel.getPredBalance()
									.replace(",", "").replace(".", "")) >= 0) {
					export.setValue((acctBreakdownModel.getPredBalance() == null ? "-"
							: acctBreakdownModel.getPredBalance()+" "));
					export.setNegative(false);
					} else if (acctBreakdownModel.getPredBalance() != null
							&& Double.valueOf(acctBreakdownModel.getPredBalance()
									.replace(",", "").replace(".", "")) < 0) {
						export.setValue((acctBreakdownModel.getPredBalance() == null ? "-"
								: acctBreakdownModel.getPredBalance()+" "));
						export.setNegative(true);
					}else {
						export.setValue((acctBreakdownModel.getPredBalance() == null ? "-"
								: acctBreakdownModel.getPredBalance()+" "));
						export.setNegative(false);
					}
					data.add(export);
					// Set the name tag from Unexpected Balance
					export = new ExportObject();
					export.setType(columnData.get(4).getType());
					export.setColumnName(columnData.get(4).getDataElement());
					if (acctBreakdownModel.getUnexpectedBalance() != null
							&& Double.valueOf(acctBreakdownModel.getUnexpectedBalance()
									.replace(",", "").replace(".", "")) >= 0) {
						export.setValue((acctBreakdownModel.getUnexpectedBalance() == null ? "-"
								: acctBreakdownModel.getUnexpectedBalance()+" "));
						export.setNegative(false);
					} else if (acctBreakdownModel.getUnexpectedBalance() != null
							&& Double.valueOf(acctBreakdownModel.getUnexpectedBalance()
									.replace(",", "").replace(".", "")) < 0) {
						export.setValue((acctBreakdownModel.getUnexpectedBalance() == null ? "-"
								: acctBreakdownModel.getUnexpectedBalance()+" "));
						export.setNegative(true);
					}else {
						export.setValue((acctBreakdownModel.getUnexpectedBalance() == null ? "-"
								: acctBreakdownModel.getUnexpectedBalance()+" "));
						export.setNegative(false);
					}
					data.add(export);
					// Set the name tag from Starting Balance
					export = new ExportObject();
					export.setType(columnData.get(5).getType());
					export.setColumnName(columnData.get(5).getDataElement());
					if (acctBreakdownModel.getStartingBalance() != null
							&& Double.valueOf(acctBreakdownModel.getStartingBalance()
									.replace(",", "").replace(".", "")) >= 0) {
						export.setValue((acctBreakdownModel.getStartingBalance() == null ? "-"
								: acctBreakdownModel.getStartingBalance()+" "));
						export.setNegative(false);
					} else if (acctBreakdownModel.getStartingBalance() != null
							&& Double.valueOf(acctBreakdownModel.getStartingBalance()
									.replace(",", "").replace(".", "")) < 0) {
						export.setValue((acctBreakdownModel.getStartingBalance() == null ? "-"
								: acctBreakdownModel.getStartingBalance()+" "));
						export.setNegative(true);
					}else {
						export.setValue((acctBreakdownModel.getStartingBalance() == null ? "-"
								: acctBreakdownModel.getStartingBalance()+" "));
						export.setNegative(false);
					}
					data.add(export);
					// Set the name tag sum
					export = new ExportObject();
					export.setValue(SwtUtil.isEmptyOrNull(acctBreakdownModel.getSum()) ? "-"
							: acctBreakdownModel.getSum().contains(
									SwtConstants.CUTOFF_SUM_FLAG) ? SwtConstants.CUTOFF_SUM_FLAG
									: acctBreakdownModel.getSum() + " ");
					export.setType(columnData.get(6).getType());
					export.setColumnName(columnData.get(6).getDataElement());					
					data.add(export);					
					
					
					//add row constructed to list
					result.add(data);

				}
				// Close the rows xml tag
				log.debug(this.getClass().getName() + " - [getRowData] - "
						+ "Existing");
		} catch (Exception e) {
			log.error(this.getClass().getName()
					+ " - [convertObj] - Exception -" + e.getMessage());
			throw new SwtException(e.getMessage());
		} finally {
			// Nullify objects
		}
		return result;
	}
	/**
	 * This method is used to get the total data and form in XML format
	 * 
	 * @param acctBDTotal
	 *            AcctBreakdownMonitor
	 * @return String
	 * @throws Exception
	 */
	private String getTotalDatas(AcctBreakdownMonitor acctBDTotal)
			throws Exception {
		// total data XML
		String totalDataXML = null;
		try {
			// start total tag
			totalDataXML = "<total>";
			if (acctBDTotal != null) {
				// start & end header tag
				totalDataXML += "<header>Total</header>";
				// start balance tag
				if (acctBDTotal.getPredBalanceTotal() != null
						&& Double.valueOf(acctBDTotal.getPredBalanceTotal()
								.replace(",", "").replace(".", "")) >= 0) {
					totalDataXML += "<balance negative=\"false\">"
							+ (acctBDTotal.getPredBalanceTotal() == null ? "-"
									: acctBDTotal.getPredBalanceTotal() + " ")
							+ "</balance>";
				} else if (acctBDTotal.getPredBalanceTotal() != null
						&& Double.valueOf(acctBDTotal.getPredBalanceTotal()
								.replace(",", "").replace(".", "")) < 0) {
					totalDataXML += "<balance negative=\"true\">"
							+ (acctBDTotal.getPredBalanceTotal() == null ? "-"
									: acctBDTotal.getPredBalanceTotal() + " ")
							+ "</balance>";
				} else {
					totalDataXML += "<balance negative=\"false\">"
							+ (acctBDTotal.getPredBalanceTotal() == null ? "-"
									: acctBDTotal.getPredBalanceTotal() + " ")
							+ "</balance>";
				}
				// start openunexpected tag
				if (acctBDTotal.getUnexpectedBalanceTotal() != null
						&& Double.valueOf(acctBDTotal
								.getUnexpectedBalanceTotal().replace(",", "")
								.replace(".", "")) >= 0) {
					totalDataXML += "<openunexpected negative=\"false\">"
							+ (acctBDTotal.getUnexpectedBalanceTotal() == null ? "-"
									: acctBDTotal.getUnexpectedBalanceTotal()
											+ " ") + "</openunexpected>";
				} else if (acctBDTotal.getUnexpectedBalanceTotal() != null
						&& Double.valueOf(acctBDTotal
								.getUnexpectedBalanceTotal().replace(",", "")
								.replace(".", "")) < 0) {
					totalDataXML += "<openunexpected negative=\"true\">"
							+ (acctBDTotal.getUnexpectedBalanceTotal() == null ? "-"
									: acctBDTotal.getUnexpectedBalanceTotal()
											+ " ") + "</openunexpected>";
				} else {
					totalDataXML += "<openunexpected negative=\"false\">"
							+ (acctBDTotal.getUnexpectedBalanceTotal() == null ? "-"
									: acctBDTotal.getUnexpectedBalanceTotal()
											+ " ") + "</openunexpected>";
				}
				// start startbalance tag
				if (acctBDTotal.getStartingBalanceTotal() != null
						&& Double.valueOf(acctBDTotal.getStartingBalanceTotal()
								.replace(",", "").replace(".", "")) >= 0) {
					totalDataXML += "<startbalance negative=\"false\">"
							+ (acctBDTotal.getStartingBalanceTotal() == null ? "-"
									: acctBDTotal.getStartingBalanceTotal()
											+ " ") + "</startbalance>";
				} else if (acctBDTotal.getStartingBalanceTotal() != null
						&& Double.valueOf(acctBDTotal.getStartingBalanceTotal()
								.replace(",", "").replace(".", "")) < 0) {
					totalDataXML += "<startbalance negative=\"true\">"
							+ (acctBDTotal.getStartingBalanceTotal() == null ? "-"
									: acctBDTotal.getStartingBalanceTotal()
											+ " ") + "</startbalance>";
				} else {
					totalDataXML += "<startbalance negative=\"false\">"
							+ (acctBDTotal.getStartingBalanceTotal() == null ? "-"
									: acctBDTotal.getStartingBalanceTotal()
											+ " ") + "</startbalance>";
				}
				// start sum tag
				totalDataXML += "<sum>" + "-" + "</sum>";
			}
			// end total tag
			totalDataXML += "</total>\n";
		} catch (Exception e) {
			// log error
			log.error(this.getClass().getName()
					+ "- [Obj2XmlAcctBreakdown] - Exception " + e.getMessage());
			throw e;
		}
		// return the total data
		return totalDataXML;
	}
	/**
	 * This method is used to get the total data and form in export object format
	 * 
	 * @param acctBDTotal
	 *            AcctBreakdownMonitor
	 * @return String
	 * @throws Exception
	 */
	public ArrayList<ExportObject> getExportTotalDatas(AcctBreakdownMonitor acctBDTotal,ArrayList<ColumnDTO> columnData)
			throws Exception {
		/*
		 * To Generate the list of object to export
		 */
		ArrayList<ExportObject> data  = null;
		ExportObject export  = null;
		
		try {
			// start total tag
			if (acctBDTotal != null) {
				
				
				
				data = new ArrayList();
				
				// Set the name tag from predict balance
				export = new ExportObject();
				export.setType(columnData.get(3).getType());
				export.setColumnName(columnData.get(3).getDataElement());
				if (acctBDTotal.getPredBalanceTotal() != null
						&& Double.valueOf(acctBDTotal.getPredBalanceTotal()
								.replace(",", "").replace(".", "")) >= 0) {
				export.setValue((acctBDTotal.getPredBalanceTotal() == null ? "-"
						: acctBDTotal.getPredBalanceTotal()+" "));
				export.setNegative(false);
				} else if (acctBDTotal.getPredBalanceTotal() != null
						&& Double.valueOf(acctBDTotal.getPredBalanceTotal()
								.replace(",", "").replace(".", "")) < 0) {
					export.setValue((acctBDTotal.getPredBalanceTotal() == null ? "-"
							: acctBDTotal.getPredBalanceTotal()+" "));
					export.setNegative(true);
				}else {
					export.setValue((acctBDTotal.getPredBalanceTotal() == null ? "-"
							: acctBDTotal.getPredBalanceTotal()+" "));
					export.setNegative(false);
				}
				data.add(export);
				// Set the name tag from Unexpected Balance
				export = new ExportObject();
				export.setType(columnData.get(4).getType());
				export.setColumnName(columnData.get(4).getDataElement());
				if (acctBDTotal.getUnexpectedBalanceTotal() != null
						&& Double.valueOf(acctBDTotal.getUnexpectedBalanceTotal()
								.replace(",", "").replace(".", "")) >= 0) {
					export.setValue((acctBDTotal.getUnexpectedBalanceTotal() == null ? "-"
							: acctBDTotal.getUnexpectedBalanceTotal()+" "));
					export.setNegative(false);
				} else if (acctBDTotal.getUnexpectedBalanceTotal() != null
						&& Double.valueOf(acctBDTotal.getUnexpectedBalanceTotal()
								.replace(",", "").replace(".", "")) < 0) {
					export.setValue((acctBDTotal.getUnexpectedBalanceTotal() == null ? "-"
							: acctBDTotal.getUnexpectedBalanceTotal()+" "));
					export.setNegative(true);
				}else {
					export.setValue((acctBDTotal.getUnexpectedBalanceTotal() == null ? "-"
							: acctBDTotal.getUnexpectedBalanceTotal()+" "));
					export.setNegative(false);
				}
				data.add(export);
				// Set the name tag from Starting Balance
				export = new ExportObject();
				export.setType(columnData.get(5).getType());
				export.setColumnName(columnData.get(5).getDataElement());
				if (acctBDTotal.getStartingBalanceTotal() != null
						&& Double.valueOf(acctBDTotal.getStartingBalanceTotal()
								.replace(",", "").replace(".", "")) >= 0) {
					export.setValue((acctBDTotal.getStartingBalanceTotal() == null ? "-"
							: acctBDTotal.getStartingBalanceTotal()+" "));
					export.setNegative(false);
				} else if (acctBDTotal.getStartingBalanceTotal() != null
						&& Double.valueOf(acctBDTotal.getStartingBalanceTotal()
								.replace(",", "").replace(".", "")) < 0) {
					export.setValue((acctBDTotal.getStartingBalanceTotal() == null ? "-"
							: acctBDTotal.getStartingBalanceTotal()+" "));
					export.setNegative(true);
				}else {
					export.setValue((acctBDTotal.getStartingBalanceTotal() == null ? "-"
							: acctBDTotal.getStartingBalanceTotal()+" "));
					export.setNegative(false);
				}
				data.add(export);
				// start sum tag
			}
			// end total tag
		} catch (Exception e) {
			// log error
			log.error(this.getClass().getName()
					+ "- [Obj2XmlAcctBreakdown] - Exception " + e.getMessage());
			throw e;
		}
		// return the total data
		return data;
	}

}
