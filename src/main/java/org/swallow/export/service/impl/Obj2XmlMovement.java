package org.swallow.export.service.impl;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Iterator;

import org.swallow.control.model.ErrorLog;
import org.swallow.export.model.ColumnDTO;
import org.swallow.export.model.FilterDTO;
import org.swallow.export.service.Obj2Xml;
import org.swallow.util.SwtUtil;
import org.swallow.util.SystemFormats;
import org.swallow.work.model.Movement;

public class Obj2XmlMovement extends Obj2XmlImpl {
	
	
	public Obj2XmlMovement() {
	
	}
	
	public String convertObj(ArrayList<ColumnDTO> columnData, ArrayList<FilterDTO> filterData, ArrayList rowData, Collection totalData) {
		String rtn = "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n<data>\n";
		
		rtn += getColumnData(columnData);
		
		rtn += getRowData(rowData);		
		rtn += getTotalData(totalData);
		rtn += getFilterData(filterData);
		

		rtn += "</data>\n";
		
		return rtn;
	}
	

	
	private String getRowData ( ArrayList rowData ) {
		String rtn = "<rows>\n";
		
		
		Movement movement = null;
		
		
		for (int i =0 ; i < rowData.size(); i++) {
			
			movement = (Movement) rowData.get(i);
			rtn += "<row>\n";
			
			
			
			rtn += "<pos>"+(movement.getPositionLevelName()==null?"-":cleanString(movement.getPositionLevelName())+" ")+"</pos>\n";
			rtn += "<value>"+(movement.getValueDateAsString()==null?"-":cleanString(movement.getValueDateAsString())+" ")+"</value>\n";
			rtn += "<amount>"+(movement.getAmountAsString()==null?"-":cleanString(movement.getAmountAsString())+" ")+"</amount>\n";
			rtn += "<sign>"+(movement.getSign()==null?"-":cleanString(movement.getSign())+" ")+"</sign>\n";
			rtn += "<ccy>"+(movement.getCurrencyCode()==null?"-":cleanString(movement.getCurrencyCode())+" ")+"</ccy>\n";
			rtn += "<ref1>"+(movement.getReference1()==null?"-":cleanString(movement.getReference1())+" ")+"</ref1>\n";
			rtn += "<account>"+(movement.getAccountId()==null?"-":cleanString(movement.getAccountId())+" ")+"</account>\n";
			rtn += "<input>"+(movement.getInputDateAsString()==null?"-":cleanString(movement.getInputDateAsString())+" ")+"</input>\n";
			rtn += "<cparty>"+(movement.getCounterPartyId()==null?"-":cleanString(movement.getCounterPartyId())+" ")+"</cparty>\n";
			rtn += "<pred>"+(movement.getPredictStatus()==null?"-":cleanString(movement.getPredictStatus())+" ")+"</pred>\n";
			rtn += "<status>"+(movement.getMatchStatusDesc()==null?"-":cleanString(movement.getMatchStatusDesc())+" ")+"</status>\n";
			/* Start : Commented and Modified the  code to display the Match Id While export the data in defined format.
	         * Modified by Kalidass on 02-Aug-08.
	         */
			//rtn += "<matchid>"+(movement.getMessageId()==null?"-":cleanString(movement.getMessageId())+" ")+"</matchid>\n";
			rtn += "<matchid>"+(movement.getMatchId()==null?"-":cleanString(""+movement.getMatchId())+" ")+"</matchid>\n";
			/* End : Commented and Modified the  code to display the Match Id While export the data in defined format.
	         * Modified by Kalidass on 02-Aug-08.
	         */
			rtn += "<source>"+(movement.getInputSource()==null?"-":cleanString(movement.getInputSource())+" ")+"</source>\n";
			rtn += "<format>"+(movement.getMessageFormat()==null?"-":cleanString(movement.getMessageFormat())+" ")+"</format>\n";
			rtn += "<notes>"+(movement.getHasNotes()==null?"-":cleanString(movement.getHasNotes())+" ")+"</notes>\n";
			rtn += "<beneficiary>"+(movement.getBeneficiaryId()==null?"-":cleanString(movement.getBeneficiaryId())+" ")+"</beneficiary>\n";
			rtn += "<ref2>"+(movement.getReference2()==null?"-":cleanString(movement.getReference2())+" ")+"</ref2>\n";
			rtn += "<ref3>"+(movement.getReference3()==null?"-":cleanString(movement.getReference3())+" ")+"</ref3>\n";
			rtn += "<movement>"+(movement.getId().getMovementId()==null?"-":cleanString(""+movement.getId().getMovementId())+" ")+"</movement>\n";
			rtn += "<book>"+(movement.getBookCode()==null?"-":cleanString(movement.getBookCode())+" ")+"</book>\n";
			rtn += "<custodian>"+(movement.getCustodianId()==null?"-":cleanString(movement.getCustodianId())+" ")+"</custodian>\n";
			
			rtn += "</row>\n";
		}
		
		
		rtn += "</rows>\n";
		
		
		
		
		
		return rtn;
	}
	


}
