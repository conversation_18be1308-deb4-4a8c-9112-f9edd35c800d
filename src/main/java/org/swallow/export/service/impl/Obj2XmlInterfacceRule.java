package org.swallow.export.service.impl;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Iterator;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.swallow.exception.SwtException;
import org.swallow.export.model.ColumnDTO;
import org.swallow.export.model.FilterDTO;
import org.swallow.export.service.Obj2Xml;
import org.swallow.maintenance.model.InterfaceRule;
import org.swallow.model.ExportObject;
import org.swallow.util.SwtConstants;
import org.swallow.util.SystemFormats;

public class Obj2XmlInterfacceRule extends Obj2XmlImpl implements Obj2Xml  {

	SystemFormats sysFormat = null;
	public Obj2XmlInterfacceRule()
	{
		
	}
	
	/**
	 * Initializing logger object for this class
	 */
	private final Log log = LogFactory.getLog(Obj2XmlInterfacceRule.class);
	
	public String convertObj(ArrayList<ColumnDTO> columnData, ArrayList<FilterDTO> filterData, Collection rowData, Collection totalData) {
		String rtn = "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n<data>\n";
		
		rtn += getColumnData(columnData);
		rtn += getRowData(rowData);		
		rtn += getTotalData(totalData);
		rtn += getFilterData(filterData);
		
		rtn += "</data>\n";
		return rtn;
	}
	private String getRowData ( Collection<InterfaceRule> rowData ) {
		String rtn = "<rows>\n";
		
		Iterator<InterfaceRule> iter = rowData.iterator();
		InterfaceRule interfaceRule = null;
		String ruleVal = SwtConstants.EMPTY_STRING;
		while (iter.hasNext()) {
	        	
			interfaceRule = (InterfaceRule) (iter.next());
			ruleVal = interfaceRule.getRuleValue();
			if(ruleVal == null)
			{
				ruleVal = SwtConstants.EMPTY_STRING;
			}
			/* Start:code modified by venkat on 22_jan_2011 for issues found on V1051 beta testing- interface rules issues. */			
			rtn += "<row>\n";
			rtn += "<messagetype><![CDATA["+interfaceRule.getId().getMessageType()+"]]></messagetype>\n";
			rtn += "<ruleid><![CDATA["+interfaceRule.getId().getRuleId()+"]]></ruleid>\n";
			rtn += "<ruleKey><![CDATA["+interfaceRule.getId().getRuleKey()+"]]></ruleKey>\n";	
			rtn += "<rulevalue><![CDATA[" + ruleVal+"]]></rulevalue>\n";
			rtn += "</row>\n";
			/* End:code modified by venkat on 22_jan_2011 for issues found on V1051 beta testing- interface rules issues. */
		}
		rtn += "</rows>\n";
		return rtn;
	}
	
	/**
	 * Used to form the whole data in XML format and return as string
	 * 
	 * @param columnData
	 * @param filterData
	 * @param rowData
	 * @return String
	 * @throws SwtException
	 */
	public ArrayList<ArrayList<ExportObject>> getExportData(ArrayList<ColumnDTO> columnData,
			ArrayList<FilterDTO> filterData, ArrayList<InterfaceRule> rowData) throws SwtException {
			/*
			 * To Generate the list of object to export
			 */
			ArrayList<ArrayList<ExportObject>> result = null;
			ArrayList<ExportObject> data  = null;
			ExportObject export  = null;
			
			// To get the default account from list
			InterfaceRule interfaceRule = null;
			// To iterate the Default Account list
			int rowIndex;

			try {
				log.debug(this.getClass().getName() + " - [getExportData] - "
						+ "Enter");
				result = new ArrayList();
				// Iterates the rowData and get the default account object
				for (rowIndex = 0; rowIndex < rowData.size(); rowIndex++) {
					
					interfaceRule = (InterfaceRule) rowData.get(rowIndex);		
					data = new ArrayList();
					
					// Sets the message type tag
					export = new ExportObject();
					export.setValue(interfaceRule.getId().getMessageType() == null ? "-"
									: interfaceRule.getId().getMessageType());
					export.setType(columnData.get(0).getType());
					export.setColumnName(columnData.get(0).getDataElement());					
					data.add(export);					
					// Sets the rule id tag
					export = new ExportObject();					
					export.setValue(interfaceRule.getId().getRuleId() == null ? "-"
									: interfaceRule.getId().getRuleId());
					export.setType(columnData.get(1).getType());
					export.setColumnName(columnData.get(1).getDataElement());
					data.add(export);
					// Set the rule key tag
					export = new ExportObject();					
					export.setValue((interfaceRule.getId().getRuleKey() == null ? "-"
							: interfaceRule.getId().getRuleKey()));
					export.setType(columnData.get(2).getType());
					export.setColumnName(columnData.get(2).getDataElement());
					data.add(export);
					// Set the rule value tag
					export = new ExportObject();			
					export.setValue((interfaceRule.getRuleValue() == null ? "-"
							: interfaceRule.getRuleValue()));
					export.setType(columnData.get(3).getType());
					export.setColumnName(columnData.get(3).getDataElement());
					data.add(export);
					
					//add row constructed to list
					result.add(data);
					
				}
				// Close the rows xml tag
				log.debug(this.getClass().getName() + " - [getExportData] - "
						+ "Exit");
		} catch (Exception e) {
			e.printStackTrace();
			log.error(this.getClass().getName()
					+ " - [getExportData] - Exception -" + e.getMessage());
			throw new SwtException(e.getMessage());
		} finally {
			// Nullify objects
		}
		return result;
	}
	
	
}