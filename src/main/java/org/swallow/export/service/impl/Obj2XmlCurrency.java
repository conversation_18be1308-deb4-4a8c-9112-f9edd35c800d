/*
 * @(#)Obj2XmlCurrency.java 1.0 ,01/08/08
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.export.service.impl;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Iterator;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.swallow.exception.SwtException;
import org.swallow.export.model.ColumnDTO;
import org.swallow.export.model.FilterDTO;
import org.swallow.maintenance.model.Currency;
import org.swallow.maintenance.model.CurrencyInterest;
import org.swallow.model.ExportObject;
import org.swallow.util.SwtUtil;
import org.swallow.work.model.Movement;

/**
 * Obj2XmlCurrency class Converts the currency object to XML String. This is
 * used to generate the CSV,PDF and Excel reports For Currency Maintenance
 */
public class Obj2XmlCurrency extends Obj2XmlImpl {

	/**
	 * Initializing logger object for this class
	 */
	private final Log log = LogFactory.getLog(Obj2XmlCurrency.class);

	/**
	 * Default Constructor
	 */
	public Obj2XmlCurrency() {

	}

	/**
	 * Used to form the whole data in XML format and return as string
	 * 
	 * @param columnData
	 * @param filterData
	 * @param rowData
	 * @param total
	 * @return String
	 * @throws SwtException
	 */
	public String convertObj(ArrayList<ColumnDTO> columnData,
			ArrayList<FilterDTO> filterData, Collection<Currency> rowData,
			Movement total) throws SwtException {
		StringBuffer resultXML = null;
		try {
			log.debug(this.getClass().getName() + " - [convertObj] - "
					+ "Entering");
			resultXML = new StringBuffer();
			resultXML
					.append("<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n<data>\n");
			resultXML.append(getColumnData(columnData));
			resultXML.append(getRowData(rowData));
			resultXML.append(getTotalDatas(total));
			resultXML.append(getFilterData(filterData));
			resultXML.append("</data>\n");
			log.debug(this.getClass().getName() + " - [convertObj] - "
					+ "Exiting");
		} catch (Exception e) {
			log.error(this.getClass().getName()
					+ " - [convertObj] - Exception -" + e.getMessage());
			throw new SwtException(e.getMessage());
		} finally {
			// Nullify objects
		}
		return resultXML.toString();
	}

	/**
	 * Used to get the row data and form in XML format and return as string
	 * 
	 * @param currencyList
	 * @return String
	 * @throws SwtException
	 * 
	 */
	private String getRowData(Collection<Currency> currencyList)
			throws SwtException {
		/*
		 * To Generate XML result of rows in the Currency list
		 */
		StringBuffer resultXML = null;
		// To iterate the currency list
		Iterator<Currency> itrCurrencyList = null;
		// Currency object to get the details from currency list
		Currency currency = null;
		try {
			log.debug(this.getClass().getName() + " - [getRowData] - "
					+ "Entering");
			resultXML = new StringBuffer();
			resultXML.append("<rows>\n");
			itrCurrencyList = currencyList.iterator();
			// Iterates the each record in the currency list
			while (itrCurrencyList.hasNext()) {
				// Gets the currency in the currency list
				currency = (itrCurrencyList.next());
				resultXML.append("<row>\n");
				// Sets the currency tag from currency code
				resultXML.append("<ccy>");
				resultXML.append(currency.getId().getCurrencyCode());
				resultXML.append("</ccy>\n");
				// Sets the currency name tag
				resultXML.append("<name>");
				resultXML
						.append(currency.getCurrencyMaster().getCurrencyName());
				resultXML.append("</name>\n");
				// Sets the currency group tag
				resultXML.append("<ccygroup>");
				resultXML.append(currency.getCurrencyGroupId());
				resultXML.append("</ccygroup>\n");
				// Sets the priority order tag
				resultXML.append("<order>");
				resultXML.append(currency.getPriorityOrder());
				resultXML.append("</order>\n");
				/*
				 * Checks the exchange rate value or not, if exchange rate is
				 * null it will add the empty <exchrate> tag else not null then
				 * adds the exchange rate to the xml tag
				 */
				resultXML.append("<exchrate>");
				resultXML.append(currency.getExchangeRate() == null ? " "
						: currency.getExchangeRate());
				resultXML.append("</exchrate>\n");
				/*
				 * Checks the Multiplier value or not, if Multiplier is null it
				 * will add the empty <multiplier> tag else not null then adds
				 * the Multiplier to the xml tag
				 */
				/*
				 * Start Code modified by Nithiyananthan for include multiplier
				 * in reports in the getRowData function for Mantis_1544 on
				 * 01-Dec-2011
				 */
				resultXML.append("<multiplier>");
				resultXML.append(currency.getMultiplierDesc() == null ? " "
						: currency.getMultiplierDesc());
				resultXML.append("</multiplier>\n");
				/*
				 * End Code modified by Nithiyananthan for include multiplier in
				 * reports in the getRowData function for Mantis_1544 on
				 * 01-Dec-2011
				 */
				// Sets the threshold tag
				resultXML.append("<threshold>");
				resultXML.append(currency.getThreshold());
				resultXML.append("</threshold>\n");
				// Sets the interest basis tag
				resultXML.append("<intbas>");
				resultXML.append(currency.getInterestBasis());
				resultXML.append("</intbas>\n");
				// Sets the Tolerance tag
				resultXML.append("<tol>");
				resultXML.append(currency.getTolerance());
				resultXML.append("</tol>\n");
				/*
				 * Checks the cutoff value or not, if cutoff is null it will add
				 * the empty <cutoff> tag else not null then adds the cutoff to
				 * the xml tag
				 */
				resultXML.append("<cutoff>");
				resultXML.append(currency.getCutOffTime() == null ? " "
						: currency.getCutOffTime());
				resultXML.append("</cutoff>\n");
				// Sets the predict tag
				resultXML.append("<predict>");
				resultXML.append(currency.getPreFlag());
				resultXML.append("</predict>\n");
				// Close the row xml tag
				resultXML.append("</row>\n");
			}
			// Close the rows xml tag
			resultXML.append("</rows>\n");
			log.debug(this.getClass().getName() + " - [getRowData] - "
					+ "Existing");
		} catch (Exception e) {
			log.error(this.getClass().getName()
					+ " - [getRowData] - Exception -" + e.getMessage());
			throw new SwtException(e.getMessage());
		} finally {
			// nullify objects
			currency = null;
			itrCurrencyList = null;
		}
		return resultXML.toString();
	}

	/**
	 * Used to get the total data and form in XML format and return as string
	 * 
	 * @param acctBDTotal
	 * @return String
	 * @throws SwtException
	 */
	private String getTotalDatas(Movement acctBDTotal) throws SwtException {
		StringBuffer resultXML = null;
		try {
			log.debug(this.getClass().getName() + " - [getTotalDatas] - "
					+ "Entering");
			resultXML = new StringBuffer();
			resultXML.append("<total>");
			resultXML.append("</total>\n");
			log.debug(this.getClass().getName() + " - [getTotalDatas] - "
					+ "Exiting");
		} catch (Exception e) {
			log.error(this.getClass().getName()
					+ " - [getTotalDatas] - Exception -" + e.getMessage());
			throw new SwtException(e.getMessage());
		} finally {
			// nullify objects
		}
		return resultXML.toString();
	}
	
	/**
	 * Used to form the whole data in XML format and return as string
	 * 
	 * @param columnData
	 * @param filterData
	 * @param rowData
	 * @return String
	 * @throws SwtException
	 */
	public ArrayList<ArrayList<ExportObject>> getExportData(ArrayList<ColumnDTO> columnData,
			ArrayList<FilterDTO> filterData, ArrayList<Currency> rowData) throws SwtException {
			/*
			 * To Generate the list of object to export
			 */
			ArrayList<ArrayList<ExportObject>> result = null;
			ArrayList<ExportObject> data  = null;
			ExportObject export  = null;
			
			// To get the Currency from list
			Currency currency = null;
			// To iterate the Currency list
			int rowIndex;

			try {
				log.debug(this.getClass().getName() + " - [getExportData] - "
						+ "Enter");
				result = new ArrayList();
				// Iterates the rowData and get the Currency object
				for (rowIndex = 0; rowIndex < rowData.size(); rowIndex++) {
					
					currency = (Currency) rowData.get(rowIndex);		
					data = new ArrayList();
					
					// Sets the currency tag from currency code
					export = new ExportObject();
					export.setValue(SwtUtil.isEmptyOrNull(currency.getId().getCurrencyCode()) ? "-"
									: currency.getId().getCurrencyCode());
					export.setType(columnData.get(0).getType());
					export.setColumnName(columnData.get(0).getDataElement());					
					data.add(export);					
					// Sets the name tag
					export = new ExportObject();					
					export.setValue(currency.getCurrencyMaster() == null ? "-"
									: currency.getCurrencyMaster().getCurrencyName());
					export.setType(columnData.get(1).getType());
					export.setColumnName(columnData.get(1).getDataElement());
					data.add(export);
					// Set the currency group tag
					export = new ExportObject();			
					export.setValue((SwtUtil.isEmptyOrNull(currency.getCurrencyGroupId())? "-"
							: currency.getCurrencyGroupId()));
					export.setType(columnData.get(2).getType());
					export.setColumnName(columnData.get(2).getDataElement());
					data.add(export);
					// Set the order tag
					export = new ExportObject();			
					export.setValue((currency.getPriorityOrder() == null ? "-"
							: currency.getPriorityOrder().toString()));
					export.setType(columnData.get(3).getType());
					export.setColumnName(columnData.get(3).getDataElement());
					data.add(export);
					// Set the exchange ratetag
					export = new ExportObject();			
					export.setValue((currency.getExchangeRate() == null ? "-"
							: currency.getExchangeRate().toString()));
					export.setType(columnData.get(4).getType());
					export.setColumnName(columnData.get(4).getDataElement());
					data.add(export);
					// Set the multiplier tag
					export = new ExportObject();			
					export.setValue((SwtUtil.isEmptyOrNull(currency.getMultiplierDesc()) ? "-"
							: currency.getMultiplierDesc()));
					export.setType(columnData.get(5).getType());
					export.setColumnName(columnData.get(5).getDataElement());
					data.add(export);
					// Set the threshold tag
					export = new ExportObject();			
					export.setValue((currency.getThreshold() == null ? "-"
							: currency.getThreshold().toString()));
					export.setType(columnData.get(6).getType());
					export.setColumnName(columnData.get(6).getDataElement());
					data.add(export);
					// Set the interest basis tag
					export = new ExportObject();			
					export.setValue((currency.getInterestBasis() == null ? "-"
							: currency.getInterestBasis().toString()));
					export.setType(columnData.get(7).getType());
					export.setColumnName(columnData.get(7).getDataElement());
					data.add(export);
					// Set the tolerence tag
					export = new ExportObject();			
					export.setValue((currency.getTolerance() == null ? "-"
							: currency.getTolerance().toString()));
					export.setType(columnData.get(8).getType());
					export.setColumnName(columnData.get(8).getDataElement());
					data.add(export);
					// Set the cut off time tag
					export = new ExportObject();			
					export.setValue((SwtUtil.isEmptyOrNull(currency.getCutOffTime())? "-"
							: currency.getCutOffTime()));
					export.setType(columnData.get(9).getType());
					export.setColumnName(columnData.get(9).getDataElement());
					data.add(export);
					// Set the predict tag
					export = new ExportObject();			
					export.setValue((SwtUtil.isEmptyOrNull(currency.getPreFlag())? "-"
							: currency.getPreFlag()));
					export.setType(columnData.get(10).getType());
					export.setColumnName(columnData.get(10).getDataElement());
					data.add(export);
					
					export = new ExportObject();			
					export.setValue((SwtUtil.isEmptyOrNull(currency.getCurrencyMaster().getCcyTimeZone())? "-"
							: currency.getCurrencyMaster().getCcyTimeZone()));
					export.setType(columnData.get(11).getType());
					export.setColumnName(columnData.get(11).getDataElement());
					data.add(export);
					
					export = new ExportObject();			
					export.setValue((SwtUtil.isEmptyOrNull(currency.getGmtOffset())? "-"
							: currency.getGmtOffset()));
					export.setType(columnData.get(12).getType());
					export.setColumnName(columnData.get(12).getDataElement());
					data.add(export);
					
					
					//add row constructed to list
					result.add(data);
					
				}
				// Close the rows xml tag
				log.debug(this.getClass().getName() + " - [getExportData] - "
						+ "Exit");
		} catch (Exception e) {
			e.printStackTrace();
			log.error(this.getClass().getName()
					+ " - [getExportData] - Exception -" + e.getMessage());
			throw new SwtException(e.getMessage());
		} finally {
			// Nullify objects
		}
		return result;
	}
}
