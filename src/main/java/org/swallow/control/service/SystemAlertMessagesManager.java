/*
 * @(#)SystemAlertMessagesManager.java 1.0 23/08/06
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.control.service;

import java.util.Collection;

import org.swallow.control.model.SystemAlertMesages;
import org.swallow.exception.SwtException;

public interface SystemAlertMessagesManager {
	/**
	 * @param hostId
	 * @param alertstage
	 * @return SystemAlertMessagesDetailVO
	 * @throws SwtException
	 */
	public SystemAlertMessagesDetailVO getAlertMsgDetailList(String hostId,
			String alertstage) throws SwtException;

	/**
	 * @param hostId
	 * @return Collection
	 * @throws SwtException
	 */
	public Collection getRollList(String hostId) throws SwtException;

	/**
	 * @param alertmsg
	 * @throws SwtException
	 */
	public void updateAlertMsgDetail(SystemAlertMesages alertmsg)
			throws SwtException;

	/**
	 * @param hostId
	 * @param alertstage
	 * @return SystemAlertMesages
	 * @throws SwtException
	 */
	public SystemAlertMesages getEditableDataDetailList(String hostId,
			String alertstage) throws SwtException;

	
}
