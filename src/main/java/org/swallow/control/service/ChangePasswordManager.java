/*
 * Created on Dec 20, 2005
 *
 * TODO To change the template for this generated file go to
 * Window - Preferences - Java - Code Style - Code Templates
 */
package org.swallow.control.service;

import java.util.Collection;

import org.swallow.control.dao.*;
import org.swallow.control.model.PasswordHistory;
import org.swallow.exception.*;
import org.swallow.model.User;




/**
 * <AUTHOR>
 *
 * TODO To change the template for this generated type comment go to
 * Window - Preferences - Java - Code Style - Code Templates
 */
public interface ChangePasswordManager {
	/**
	 * @param user
	 * @return
	 * @throws SwtException
	 */
	public boolean setNewPassword(User	user,String newPwd)  throws SwtException;	
	/**
	 * @param changePasswordDAO
	 * @throws SwtException
	 */
	public void setChangePasswordDAO(ChangePasswordDAO changePasswordDAO) throws SwtException;
	/**
	 * @param pwdhis
	 * @throws SwtException
	 */
	public void updatePasswordHistory(PasswordHistory pwdhis)throws SwtException;
	
	public Collection getPasswordRules(User user)throws SwtException;
	public String checkPassword(User user) throws SwtException ;
	/**
	 * Delete the row from s_password_history table with seq no 0
	 * @param hostId
	 * @param userId
	 */
	/*Start: Refer to Mantis issue : 0000391: Various login issues */
	public void deletePasswordHistryObject(PasswordHistory pwdhis) 
	throws SwtException;
	/*End: Refer to Mantis issue : 0000391: Various login issues */
}