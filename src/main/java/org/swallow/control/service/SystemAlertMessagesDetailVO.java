/*
 * Created on Dec 24, 2005
 *
 * TODO To change the template for this generated file go to
 * Window - Preferences - Java - Code Style - Code Templates
 */
package org.swallow.control.service;

import java.util.Collection;

/**
 * <AUTHOR>
 *
 * TODO To change the template for this generated type comment go to
 * Window - Preferences - Java - Code Style - Code Templates
 */
public class SystemAlertMessagesDetailVO {
	
	private Collection alertmessegeslist;
	private Collection alertmessagesDetails;

	/**
	 * @return Returns the alertmessagesDetails.
	 */
	public Collection getAlertmessagesDetails() {
		return alertmessagesDetails;
	}
	/**
	 * @param alertmessagesDetails The alertmessagesDetails to set.
	 */
	public void setAlertmessagesDetails(Collection alertmessagesDetails) {
		this.alertmessagesDetails = alertmessagesDetails;
	}
	/**
	 * @return Returns the alertmessegeslist.
	 */
	public Collection getAlertmessegeslist() {
		return alertmessegeslist;
	}
	/**
	 * @param alertmessegeslist The alertmessegeslist to set.
	 */
	public void setAlertmessegeslist(Collection alertmessegeslist) {
		this.alertmessegeslist = alertmessegeslist;
	}
}
