/*
 * @(#)InterfaceExceptionsManager.java 1.0 20/06/2011
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.control.service;

import org.swallow.control.dao.InterfaceExceptionsDAO;
import org.swallow.control.model.InterfaceExceptionsData;
import org.swallow.exception.SwtException;
import org.swallow.util.OpTimer;
import org.swallow.util.SystemFormats;

/**
 * This interface has methods that are related to Interface Exceptions screen.<br>
 * 
 * Author: Marshal <PERSON> .I<br>
 * Date: 20-June-2011
 * 
 */
public interface InterfaceExceptionsManager {

	/**
	 * Method to set InterfaceExceptionsDAO
	 * 
	 * @param interfaceExceptionsDAO
	 */
	public void setInterfaceExceptionsDAO(
			InterfaceExceptionsDAO interfaceExceptionsDAO);

	/**
	 * Method to get exception messages for message status and message type
	 * 
	 * @param page
	 * @param opTimer
	 * @param dateFormat
	 * @param format
	 * @param fromPCM
	 * @return InputExceptionsDataPageDTO
	 * @throws SwtException
	 */
	public InterfaceExceptionsData getMessages(InterfaceExceptionsData page,
			OpTimer opTimer, String dateFormat, SystemFormats format, boolean fromPCM)
			throws SwtException;

	/**
	 * Method to get message of selected sequence id
	 * 
	 * @param seqId
	 * @param opTimer
	 * @return String
	 * @return fromPCM
	 * @throws SwtException
	 */
	public String getMessageBody(String seqId, OpTimer opTimer, boolean fromPCM)
			throws SwtException;

	/**
	 * Method to re-process exception message for selected sequence id.<br>
	 * 
	 * @param messageIds
	 * @param opTimer
	 * @param fromPCM
	 * @return
	 * @throws SwtException
	 */
	public boolean reprocessMessage(String[] messageIds, OpTimer opTimer, boolean fromPCM)
			throws SwtException;

}
