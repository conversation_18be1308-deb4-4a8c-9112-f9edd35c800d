package org.swallow.control.service;

import java.util.ArrayList;
import java.util.List;

import org.swallow.control.model.ConnectionPool;
import org.swallow.exception.SwtException;

public interface ConnectionPoolControlManager {

	public ConnectionPool getConnectionPool(ConnectionPool conn, String moduleId)
			throws SwtException;

	public ArrayList<ConnectionPool> getConnectionPoolList(ArrayList<ConnectionPool> openConnections, String moduleId)
			throws SwtException;
	
	public ArrayList<ConnectionPool> getConnectionPoolListByModule(String moduleId)
			throws SwtException;
	
	public void killDBSessionConnections(String moduleId, String connectionsIds)
			throws SwtException;
	
	public boolean checkDBViewsGrant(String moduleId)	throws SwtException;
	
	public void updatePoolStatsTable(String moduleId, int numActive, int numIdle, int maxActive, int maxIdle ) throws SwtException ;

}