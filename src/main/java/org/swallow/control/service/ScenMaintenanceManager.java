/*
 * @(#)ScenMainteanceManager.java 1.0 12/11/12
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.control.service;

import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;

import org.swallow.control.model.Facility;
import org.swallow.control.model.Scenario;
import org.swallow.control.model.ScenarioCategory;
import org.swallow.control.model.ScenarioEventFacility;
import org.swallow.control.model.ScenarioEventMapping;
import org.swallow.control.model.ScenarioGuiAlertFacility;
import org.swallow.control.model.ScenarioGuiAlertMapping;
import org.swallow.control.model.ScenarioNotify;
import org.swallow.control.model.ScenarioSchedule;
import org.swallow.exception.SwtException;
import org.swallow.util.LabelValueBean;

public interface ScenMaintenanceManager {

	/**
	 * @return SystemAlertMessagesDetailVO
	 * @throws SwtException
	 */
	public ArrayList getScenariosDetailList(String category) throws SwtException;

	/**
	 * @return ArrayList<ScenarioNotify>
	 * @throws SwtException
	 */
	public ArrayList<ScenarioNotify> getScenariosNotificationDetailList() throws SwtException;

	/**
	 * @return Collection
	 * @throws SwtException
	 */
	public Collection getCategoryList() throws SwtException;

	/**
	 * 
	 * @return
	 * @throws SwtException
	 */
	Collection getCategoryDetailedList() throws SwtException;

	/**
	 * @param scenario
	 * @throws SwtException
	 */
	public void updateScenarioDetail(Scenario scenario) throws SwtException;

	/**
	 * 
	 * @param scenario
	 * @throws SwtException
	 */
	public void saveScenarioDetails(Scenario scenario) throws SwtException;

	/**
	 * @return Collection
	 * @throws SwtException
	 */
	public Collection getFacilityList() throws SwtException;

	/**
	 * @param boolean
	 * @return Collection
	 * @throws SwtException
	 */
	public Collection getFacilityListForEdit(boolean genericDisplaySelected) throws SwtException;

	/**
	 * @return Collection
	 * @throws SwtException
	 */
	public Collection getSummaryGroupingList() throws SwtException;

	/**
	 * @param scenarioID
	 * @return Scenario
	 * @throws SwtException
	 */
	public Scenario getEditableDataDetailList(String scenarioID) throws SwtException;

	/**
	 * 
	 * 
	 * @param categoryID
	 * @return ScenarioCategory
	 * @throws SwtException
	 */
	public ScenarioCategory getcategoryDetails(String categoryID) throws SwtException;

	/**
	 * This is used to delete the scenario details from P_SCENARIO table
	 * 
	 * @param scenario
	 * @return
	 * @exception SwtException
	 */
	public void deleteScenarioDetail(Scenario scenario) throws SwtException;

	/**
	 * 
	 * @param FacilityID
	 * @return Facility
	 * @throws SwtException
	 */
	public Facility getFacilityDetails(String FacilityID) throws SwtException;

	/**
	 * 
	 * @return Collection
	 * @throws SwtException
	 */
	public Collection getScenarioList() throws SwtException;

	/**
	 * 
	 * @param scenarioNot
	 * @throws SwtException
	 */
	public void updateScenarioNotificationDetail(ScenarioNotify scenarioNot) throws SwtException;

	/**
	 * 
	 * @param scenarioCat
	 * @throws SwtException
	 */
	public void updateScenarioCategoryDetails(ScenarioCategory scenarioCat) throws SwtException;

	/**
	 * 
	 * @param scenarioID
	 * @return ScenarioNotify
	 * @throws SwtException
	 */
	public ScenarioNotify getRoleAssignmentEditableData(String hostId, String scenarioID, String entityId,
			String roleId) throws SwtException;

	/**
	 * 
	 * @param scenarioNot
	 * @throws SwtException
	 */
	public void saveScenarioNotificationDetails(ScenarioNotify scenarioNot) throws SwtException;

	/**
	 * 
	 * @param scenarioNot
	 * @throws SwtException
	 */
	public void deleteScenarioNotificationDetail(ScenarioNotify scenarioNot) throws SwtException;

	/**
	 * 
	 * 
	 * @param hostId
	 * @return Collection
	 * @throws SwtException
	 */
	public Collection getRoleList(String hostId) throws SwtException;

	/**
	 * 
	 * @return
	 * @throws SwtException
	 */
	public ArrayList<ScenarioCategory> getCategoryDetailList() throws SwtException;

	/**
	 * 
	 * @param scenarioCat
	 * @throws SwtException
	 */
	public void saveScenarioCategoryDetails(ScenarioCategory scenarioCat) throws SwtException;

	/**
	 * 
	 * @param scenarioCat
	 * @throws SwtException
	 */
	public ScenarioCategory getCategoryEditableData(String scenarioCatId) throws SwtException;

	/**
	 * 
	 * @param scenarioCatId
	 * @throws SwtException
	 */
	public void deleteScenarioCategoryDetails(ScenarioCategory scenarioCat) throws SwtException;

	/**
	 * 
	 * @param categoryId
	 * @return
	 * @throws SwtExceptions
	 */
	public boolean categoryHasChildren(String categoryId) throws SwtException;

	/**
	 * @param hostId
	 * @param entityId
	 * @param roleId
	 * @param scenarioID
	 * @return Collection
	 * @throws SwtExceptions
	 */
	public Collection getDistListByRole(String hostId, String entityId, String roleId, String scenarioID)
			throws SwtException;

	public Collection getGuiFacilityList() throws SwtException;
	
	public Collection getGuiFacilityGrid() throws SwtException;

	public ScenarioGuiAlertFacility getGuiFacilityDetails(String guiFacilityId) throws SwtException;

	public Collection getEventFacilityList() throws SwtException;

	public ScenarioEventFacility getEventFacilityDetails(String guiFacilityId) throws SwtException;

	public void crudGuiHighlight(List<ScenarioGuiAlertMapping> listGuiAdd, List<ScenarioGuiAlertMapping> listGuiUpdate,
			List<ScenarioGuiAlertMapping> listGuiDelete) throws SwtException;

	public Collection getGuiHiglightMapping(String scenarioId) throws SwtException;

	public void crudEventMapping(List<ScenarioEventMapping> listEventAdd, List<ScenarioEventMapping> listEventUpdate,
			List<ScenarioEventMapping> listEventDelete) throws SwtException;

	public Collection getEventMapping(String scenarioId) throws SwtException;
	
	public void saveScenSchedule(List<ScenarioSchedule> listScenScheduleToAdd, List<ScenarioSchedule> listScenScheduleToUpdate, List<ScenarioSchedule> listScenSchedToDelete) throws SwtException ;
	
	public Collection getScenarioSchedule(String scenarioId) throws SwtException ;
	
	public void deleteScenSchedule(String  scenarioId) throws SwtException;
	
	public Long getEventMappingKey(String  scenarioId , String eventFacilityId, Integer ordinal) throws SwtException;
	
	public Collection<LabelValueBean>  getOtherIdTypes() throws SwtException;
	
	public HashMap<String, String>  getFacilityMappingOtherId(String scenarioId) throws SwtException ;
	
	public List getScenGuiFacilityMappingList(String scenarioId) throws SwtException ;
	
	public void deleteFacilitiesMapping(String scenarioId) throws SwtException ;
	
	/**
	 * This method is used to get RECORD_SCENARIO_INSTANCES from P_SCENARIO table
	 */
	public String getScenRcdInstance(String scenarioId) throws SwtException;
	
	public String checkIfInstExist(String scenarioId) throws SwtException;
	
	public void deleteEventMapping(String scenarioId) throws SwtException;
	
	public ScenarioSchedule getSchedulerById(String scenarioId, String scheduledId) throws SwtException;

	public void updateScenarioQueryExecutionListColumns(String scenarioId ,String queryExecutionListOfcolumns)throws SwtException;

}
