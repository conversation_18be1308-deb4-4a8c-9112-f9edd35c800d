/*
 * @(#)AccountAccessManager.java 1.0 02/01/2010
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.control.service;

import java.util.Collection;
import java.util.List;

import org.swallow.control.dao.AccountAccessDAO;
import org.swallow.control.model.AccountAccess;
import org.swallow.exception.SwtException;

/**
 * 
 * <AUTHOR> This interface, contains methods to be used in
 *         AccountAccessManager class
 */
//unnessary code Removed by Arumugam on 01-Nov-2010 for Mantis:0001281- Account Access Control screen takes long time to save
public interface AccountAccessManager {

	/**
	 * This is used to set Account access DAO
	 * 
	 * @param AccountAccessDAO
	 * @return none
	 */
	public void setAccountAccessDAO(AccountAccessDAO accountAccessDAO);
	

	/**
	 * This is used to fetches account access details
	 * 
	 * @param hostId
	 * @param roleId
	 * @param entityId
	 * @param Ccy
	 * @return Collection
	 * @throws SwtException
	 */

	public Collection accountAccessDetails(String hostId, String roleId,
			String entityId, String Ccy) throws SwtException;

	/**
	 * This is used to delete account access details based on role id
	 * 
	 * @param roleId
	 * @return none
	 * @throws SwtException
	 */

	public void deleteAcctAccessDetails(String roleId) throws SwtException;

	/**
	 * This is used to delete account access details based on account id
	 * 
	 * @param accountId
	 * @return
	 * @throws SwtException
	 */

	public void deleteAcctDetails(String accountId) throws SwtException;

	/**
	 * This is used to fetches role details based on host id
	 * 
	 * @param hostId
	 * @return List
	 * @throws SwtException
	 */
	public List getRoleDetails(String hostId) throws SwtException;

	/**
	 * This is used to check the status of account.
	 * 
	 * @param accountAccess
	 * @param status
	 * @return boolean
	 * @throws SwtException
	 */
	public boolean checkAcctAccess(AccountAccess accountAccess, String status)
			throws SwtException;
	
	/**
	 * This is used to check the status of account for MSD screen.
	 * 
	 * @param movementId
	 * @param roleId
	 * @param entityId
	 * @return boolean
	 * @throws SwtException
	 */
	public boolean checkAcctAccessMSD(String movementId, String roleId, String entityId, String status)
			throws SwtException;
	
	/**
	 * This is used to check the status of account.
	 * 
	 * @param RoleId
	 * @return int
	 * @throws SwtException
	 */
	public int checkMenuAccess(String roleId) throws SwtException;
	
	
	/**
	 * This is used copying one role account access details to another role
	 * 
	 * @param copiedRoleId
	 * @param newRoleId
	 * @return 
	 * @throws SwtException
	 */
	public void copyAccessDetails(String copiedRoleId, String newRoleId
			) throws SwtException;
	/**
	 * 
	 * @param AccountAccess
	 * @param userId
	 * @return none
	 * @throws SwtException
	 */
	public void saveAccountAccessControlDetails(Collection accountAccess, String userId)
			throws SwtException;
	/**
	 * This is used to check role account access details
	 * 
	 * @param accountAccess
	 * @return boolean
	 * @throws SwtException
	 */
	public boolean getRoleAccessDetails(AccountAccess accountAccess) throws SwtException;
	
}
