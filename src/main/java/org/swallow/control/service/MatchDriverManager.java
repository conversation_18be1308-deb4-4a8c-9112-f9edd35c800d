/*
 * Created on Apr 28, 2006
 *
 * TODO To change the template for this generated file go to
 * Window - Preferences - Java - Code Style - Code Templates
 */
package org.swallow.control.service;

import java.util.Collection;

import org.swallow.control.dao.MatchDriverDAO;
import org.swallow.exception.SwtException;
import org.swallow.work.model.MatchDriver;

/**
 * <AUTHOR>
 * 
 * TODO To change the template for this generated type comment go to Window -
 * Preferences - Java - Code Style - Code Templates
 */
public interface MatchDriverManager {
	public void setMatchDriverDAO(MatchDriverDAO matchDriverDAO);

	public Collection<MatchDriver> getMatchDriverList(String hostId,
			String entityId) throws SwtException;

	public void updateMatchDriverDetail(MatchDriver matchDriver)
			throws SwtException;

}
