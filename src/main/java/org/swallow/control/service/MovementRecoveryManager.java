/*
 * @(#)MovementRecoveryManager .java 03/05/06
 * 
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 * 
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */

package org.swallow.control.service;

import java.util.Collection;
import org.swallow.control.dao.MovementRecoveryDAO;
import org.swallow.control.dao.RoleDAO;
import org.swallow.exception.SwtException;
import org.swallow.util.SystemFormats;
import org.swallow.util.SystemInfo;
import org.swallow.work.model.MovementLock;


public interface MovementRecoveryManager {
	
	/**
	 * 
	 */
	public void setMovementRecoveryDAO(MovementRecoveryDAO dao);
	/**
	 * 
	 * @param hostId 
	 * @param entityId
	 * @return
	 * @throws SwtException
	 */
	public Collection getMovementLockDetails(String hostId,String entityId,SystemInfo systemInfo, SystemFormats systemFormats) throws SwtException;
	/**
	 * 
	 * @param hostId
	 * @param movementId
	 * @throws SwtException
	 */
	public void unlockMovement(MovementLock movLock)throws SwtException;
}
