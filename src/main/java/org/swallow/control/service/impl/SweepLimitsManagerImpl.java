/*
 * Created on Jan 9, 2006
 *
 * TODO To change the template for this generated file go to
 * Window - Preferences - Java - Code Style - Code Templates
 */
package org.swallow.control.service.impl;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Iterator;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.swallow.control.dao.SweepLimitsDAO;
import org.swallow.control.model.SweepLimits;
import org.swallow.control.service.SweepLimitsManager;
import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.util.SwtUtil;
import org.swallow.util.SystemFormats;
import org.swallow.util.SystemInfo;

/**
 * <AUTHOR>
 * 
 * TODO To change the template for this generated type comment go to Window -
 * Preferences - Java - Code Style - Code Templates
 */
@Component("sweepLimitsManager")
public class SweepLimitsManagerImpl implements SweepLimitsManager {

	private final Log log = LogFactory.getLog(SweepLimitsManagerImpl.class);
	@Autowired
	private SweepLimitsDAO sweepLimitsDAO;

	public void setSweepLimitsDAO(SweepLimitsDAO slDAO) {
		this.sweepLimitsDAO = slDAO;
	}

	public Collection getSweepLimitsDetails(String roleId,
			SystemInfo systemInfo, SystemFormats systemFormats)
			throws SwtException {
		log.debug("Entering 'getSweepLimitsDetails' Method");
		Collection sweepLimitsDetailsList = new ArrayList();
		try {
			sweepLimitsDetailsList = sweepLimitsDAO.getSweepLimitsDetails(roleId);
			if (sweepLimitsDetailsList != null) {
				Iterator itr = sweepLimitsDetailsList.iterator();
				while (itr.hasNext()) {
					SweepLimits sweepLimits = (SweepLimits) (itr.next());
					sweepLimits.setSweepLimitAsString(SwtUtil.formatCurrency(
							sweepLimits.getId().getCurrencyCode(), sweepLimits
									.getSweepLimitBig()));
					sweepLimits.setSweepLimitAsStringAnother(SwtUtil
							.formatCurrency(sweepLimits.getId()
									.getCurrencyCode(), sweepLimits
									.getSweepLimitBig()));
				}
			}
			log.debug("Exiting 'getSweepLimitsDetails' Method");
		} catch (Exception e) {
			log
					.debug("Exception occurred in SweepLimitsManagerImpl.getSweepLimitsDetails() method");
			e.printStackTrace();
			throw SwtErrorHandler.getInstance().handleException(e,
					"getSweepLimitsDetails", SweepLimitsManagerImpl.class);
		}
		return sweepLimitsDetailsList;
	}

	public Collection getCurrencyDetails(String hostId, String entityId,
			String ccyGrpId) throws SwtException {
		log.debug("Entering 'getCurrencyDetails' Method");
		Collection coll = new ArrayList();
		try {
			coll = sweepLimitsDAO.getCurrencyDetails(hostId, entityId, ccyGrpId);
			log.debug("Exiting 'getCurrencyDetails' Method");
		} catch (Exception e) {
			log
					.debug("Exception occurred in SweepLimitsManagerImpl.getCurrencyDetails() method");
			e.printStackTrace();
			throw SwtErrorHandler.getInstance().handleException(e,
					"getCurrencyDetails", SweepLimitsManagerImpl.class);
		}
		return coll;
	}

}