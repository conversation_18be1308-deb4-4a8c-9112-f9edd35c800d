/*
 * Created on Dec 26, 2005
 *
 * TODO To change the template for this generated file go to
 * Window - Preferences - Java - Code Style - Code Templates
 */
package org.swallow.control.service.impl;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.swallow.util.LabelValueBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.swallow.control.dao.*;
import org.swallow.control.model.*;
import org.swallow.control.service.*;


import org.swallow.exception.*;
import org.swallow.maintenance.model.Currency;



import java.util.ArrayList;
import java.util.Collection;
import java.util.Iterator;
/**
 * <AUTHOR>
 *
 * TODO To change the template for this generated type comment go to
 * Window - Preferences - Java - Code Style - Code Templates
 */
@Component("workQAccessManager")
public class WorkQAccessManagerImpl implements WorkQAccessManager{
	
	private final Log log = LogFactory.getLog(WorkQAccessManagerImpl.class);
	@Autowired
	private WorkQAccessDAO dao;

	public void setWorkQAccessDAO(WorkQAccessDAO wqaDAO) {
        this.dao = wqaDAO;
    }
	
	
	
	public WorkQAccessDetailVO getCurrencyDetailList(String entityId,String hostId,String roleId,String currencyCode) throws SwtException
	{
		//CurrencyDetailVO currDetailVO = new CurrencyDetailVO();
		log.debug("Entering  getCurrencyDetailList Method");
		WorkQAccessDetailVO wqaDetailvO = new WorkQAccessDetailVO();
		
		ArrayList  currencyList = new ArrayList();
		ArrayList  workQAccessDetails = new ArrayList();		
		
		//Collection collCurre = dao.getCurrencyDetailList(entityId,hostId);
		Collection collCurre =dao.getCurrencyList(entityId,hostId);
		
		Iterator itr = collCurre.iterator();
		
		currencyList.add(new LabelValueBean("All","All"));
		Currency cur = new Currency();
		
		WorkQAccess wqa=new WorkQAccess();
		Collection collWQA= dao.getWorkQAccessDetails(entityId,hostId,roleId);
		Iterator itr1 = collWQA.iterator();
		log.debug("wqa details are as follows==>"+collWQA);
		while(itr.hasNext())
		{
			cur = (Currency)(itr.next());
		   //wqa= (WorkQAccess)(itr1.next());
			log.debug("Currency Name " + cur.getCurrencyMaster().getCurrencyName());
			currencyList.add(new LabelValueBean(cur.getCurrencyMaster().getCurrencyName(),cur.getId().getCurrencyCode()));
		}
		while(itr1.hasNext()){
			wqa= (WorkQAccess)(itr1.next());
		if(currencyCode == null || 
				currencyCode.equals("All") || 
		   (wqa.getId().getCurrencyCode()).equals(currencyCode)
		 )
			workQAccessDetails.add(wqa);	
		}
		log.debug(currencyList);
		log.debug("Exiting  getCurrencyDetailList Method");
		
		wqaDetailvO.setCurrencyList(currencyList);
		wqaDetailvO.setWorkQAccessDetails(workQAccessDetails);
		
		return wqaDetailvO;
	}
	public Collection getDetailsByRoleId(String hostId,String roleId)throws Exception{
		log.debug("Entering 'getDetailsByRoleId' method");
    	Collection detailsByRoleId =dao.getDetailsByRoleId(hostId,roleId);
    	log.debug("Exiting 'getDetailsByRoleId' method"); 
    	return (detailsByRoleId);
	}
}
