/*
 * @(#)SectionManagerImpl.java 04/11/05
 * 
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 * 
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */

package org.swallow.control.service.impl;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Iterator;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.swallow.util.LabelValueBean;
import org.swallow.control.dao.SectionDAO;
import org.swallow.control.model.Section;
import org.swallow.control.service.SectionManager;
import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.util.SystemInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
/*
 * This is manager class for section screen
 * 
 */
@Component ("sectionManager")
public class SectionManagerImpl implements SectionManager {
	
	@Autowired
	private SectionDAO dao;

	/**
	 * Comment for <code>log</code>
	 */
	private final Log log = LogFactory.getLog(SectionManagerImpl.class);

	/**
	 * @param dao
	 */
	public void setSectionDAO(SectionDAO dao) {
		this.dao = dao;
	}

	/**
	 * @param hostId
	 * @return Collection
	 */
	public Collection getSectionList(String hostId) throws SwtException {
		log.debug("entering 'getSectionList' method");
		Collection collCust = dao.getSectionList(hostId);
		log.debug("exiting 'getSectionList' method");
		return collCust;
	}

	/**This is used to save the records in database by calling DAO class
	 * 
	 * @param section
	 * @param systemInfo
	 * @return none
	 * 
	 */
	public void saveSectionDetail(Section section, SystemInfo systemInfo)
			throws SwtException {
		try {
			log.debug(this.getClass().getName() + "- [saveSectionDetail] - Entering ");
			/*Save the section details by calling DAO class*/
			dao.saveSectionDetail(section);
			log.debug(this.getClass().getName() + "- [saveSectionDetail] - Exiting ");
		} catch (Exception e) {
			/*
			 * Start : Modified for Mantis 1366-Remove entry to log if duplicate
			 * records added by betcy on 18-02-2011
			 */
			log.error(this.getClass().getName()
					+ " - Exception Catched in [saveSectionDetail] method : - "
					+ e.getMessage());
			/*
			 * End : Modified for Mantis 1366-Remove entry to log if duplicate
			 * records added by betcy on 18-02-2011
			 */
			throw SwtErrorHandler.getInstance().handleException(e,
			"saveSectionDetail", SectionManagerImpl.class);
		}
	}

	/**
	 * @param section
	 */
	public void updateSectionDetail(Section section, SystemInfo systemInfo)
			throws SwtException {
		try {
			log.debug("entering 'updateSectionDetail' method");
			dao.updateSectionDetail(section);
			log.debug("exiting 'updateSectionyDetail' method");
		} catch (Exception e) {
			log.debug("Exception occurred in SectionManagerImpl.updateSectionDetail() method");
			e.printStackTrace();
			throw SwtErrorHandler.getInstance().handleException(e,
					"updateSectionDetail", SectionManagerImpl.class);

		}
	}

	/**
	 * @param section -
	 *            Object
	 * 
	 */
	public void deleteSectionDetail(Section section, SystemInfo systemInfo)
			throws SwtException {
		try {
			log.debug("entering deleteSectionDetail"
							+ section.getSectionName());
			dao.deleteSectionDetail(section);
			log.debug("exiting deleteSectionnDetail");
		} catch (Exception e) {
			log.debug("Exception occurred in SectionManagerImpl.deleteSectionDetail() method");
			e.printStackTrace();
			throw SwtErrorHandler.getInstance().handleException(e,
					"deleteSectionDetail", SectionManagerImpl.class);
		}
	}

	/**
	 * @param hostId
	 * @return Collection
	 */
	public Collection getSectionListAsLebelBean(String hostId)
			throws SwtException {
		ArrayList selectionList = new ArrayList();
		try {
			Section section;
			log.debug("entering getSectionListAsLebelBean");
			Iterator itr = (getSectionList(hostId)).iterator();
			selectionList.add(new LabelValueBean("", ""));
			while (itr.hasNext()) {
				section = (Section) (itr.next());
				selectionList.add(new LabelValueBean(section.getSectionName(),
						section.getId().getSectionId()));
			}
			log.debug("exiting 'getSectionListAsLebelBean' method");
		} catch (Exception e) {
			log.debug("Exception occurred in SectionManagerImpl.getSectionListAsLebelBean() method");
			e.printStackTrace();
			throw SwtErrorHandler.getInstance().handleException(e,
					"getSectionListAsLebelBean", SectionManagerImpl.class);
		}
		return selectionList;

	}
}