package org.swallow.control.service.impl;

import java.util.ArrayList;
import java.util.List;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.swallow.control.dao.ConnectionPoolControlDAO;
import org.swallow.control.model.ConnectionPool;
import org.swallow.control.service.ConnectionPoolControlManager;
import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
@Component("connectionPoolControlManager")
public class ConnectionPoolControlManagerImpl
		implements
			ConnectionPoolControlManager {

	private final Log log = LogFactory
			.getLog(ConnectionPoolControlManagerImpl.class);
	@Autowired
	private ConnectionPoolControlDAO connectionPoolControlDAO;

	public void setConnectionPoolControlDAO(
			ConnectionPoolControlDAO connectionPoolControlDAO) {
		this.connectionPoolControlDAO = connectionPoolControlDAO;
	}


	public ConnectionPool getConnectionPool(ConnectionPool conn ,String moduleId)
			throws SwtException {
		ConnectionPool result;
		try {
			result = connectionPoolControlDAO.getConnectionPool(conn, moduleId);
		} catch (Exception e) {
			log.error(this.getClass().getName()
					+ "- [getConnectionPool] - Exception " + e.getMessage());
			throw SwtErrorHandler.getInstance()
					.handleException(e, "getConnectionPool",
							ConnectionPoolControlManagerImpl.class);
		}
		return result;
	}

	public ArrayList<ConnectionPool> getConnectionPoolList(ArrayList<ConnectionPool> openConnections, String moduleId)
			throws SwtException {
		ArrayList<ConnectionPool> result;
		try {
			result = connectionPoolControlDAO.getConnectionPoolList(openConnections, moduleId);
		} catch (Exception e) {
			log.error(this.getClass().getName()
					+ "- [getConnectionPoolList] - Exception " + e.getMessage());
			throw SwtErrorHandler.getInstance().handleException(e,
					"getConnectionPoolList",
					ConnectionPoolControlManagerImpl.class);
		}
		return result;
	}
	public ArrayList<ConnectionPool> getConnectionPoolListByModule(String moduleId)
			throws SwtException {
		ArrayList<ConnectionPool> result;
		try {
			result = connectionPoolControlDAO.getConnectionPoolListByModule(moduleId);
		} catch (Exception e) {
			log.error(this.getClass().getName()
					+ "- [getConnectionPoolListByModule] - Exception " + e.getMessage());
			throw SwtErrorHandler.getInstance().handleException(e,
					"getConnectionPoolListByModule",
					ConnectionPoolControlManagerImpl.class);
		}
		return result;
	}
	
	/**
	 * This method is used to kill DB session using connection identifiers
	 */
	public void killDBSessionConnections(String moduleId, String connectionsIds)
			throws SwtException{
		try {
			connectionPoolControlDAO.killDBSessionConnections(moduleId, connectionsIds);
		} catch (Exception e) {
			log.error(this.getClass().getName()
					+ "- [getConnectionPoolList] - Exception " + e.getMessage());
		}
	}
	/**
	 * This method is used to Check if the Predict user has access to v$session and v$active_session_history
	 */
	public boolean checkDBViewsGrant(String moduleId)	throws SwtException{
		try {
			return connectionPoolControlDAO.checkDBViewsGrant(moduleId);
		} catch (Exception e) {
			log.error(this.getClass().getName()
					+ "- [checkDBViewsGrant] - Exception " + e.getMessage());
		}
		return false;
	}
	
	
	/**
	 * This method is used to update S_POOL_STATS table with the latest connection pool details
	 *
	 * @param connectionsIds
	 * @return
	 * @throws SwtException
	 */
	public void updatePoolStatsTable(String moduleId, int numActive, int numIdle, int maxActive, int maxIdle ) throws SwtException {
		try {
			connectionPoolControlDAO.updatePoolStatsTable(moduleId, numActive, numIdle, maxActive, maxIdle);
		} catch (Exception e) {
			log.error(this.getClass().getName()
					+ "- [updatePoolStatsTable] - Exception " + e.getMessage());
		}
		
	}
	
	
}