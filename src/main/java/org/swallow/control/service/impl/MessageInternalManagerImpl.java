/*
 * @(#)MessageInternalManagerImpl.java 04/11/05
 * 
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 * 
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */

package org.swallow.control.service.impl;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Iterator;
import java.util.List;


import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.control.dao.MessageInternalDAO;
import org.swallow.model.User;
import org.swallow.util.LabelValueBean;
import org.swallow.control.model.Role;
import org.swallow.control.model.MessageInternal;
import org.swallow.control.model.MessageInternalUser;
import org.swallow.control.service.MessageInternalManager;

@Component("messageInternalManager")
public class MessageInternalManagerImpl implements MessageInternalManager {
	
	private final Log log = LogFactory.getLog(MessageInternalManagerImpl.class);
	@Autowired
	private MessageInternalDAO dao;
	
	
	/**
	 * @param messageInternalDAO
	 * @throws SwtException
	 */
	public void setMessageInternalDAO(MessageInternalDAO dao) {
        this.dao = dao;
    }
	
	/**
	 * @param hostId
	 * @return
	 * @throws SwtException
	 */
	public Collection getUserList(String hostId) throws SwtException{
		
		try{
			log.debug("entering 'getUserList' method");
			
			ArrayList userList = new ArrayList();
			User user=null;
			Iterator itr = (dao.getUserList(hostId)).iterator(); 
			userList.add(new LabelValueBean("ALL","ALL"));
			log.debug("in iterator");
			while(itr.hasNext())
			{
				user = (User)(itr.next());
				userList.add(new LabelValueBean(user.getId().getUserId(),user.getId().getUserId()));
			}
			log.debug("userList->"+userList);
			log.debug("exiting 'getUserList' method");
			
			return userList;
		}catch(Exception e) {
            throw SwtErrorHandler.getInstance().handleException(e,"getUserList",MessageInternalManagerImpl.class);

    	}
		
	}
	
	/**
	 * @param hostId
	 * @return
	 * @throws SwtException
	 */
	public List getAllUsers(String hostId) throws SwtException{
		try{
			log.debug("entering 'getAllUsers' method");
			List allusers=(List) dao.getAllUsers(hostId);
			log.debug("exiting 'getAllUsers' method");
			return allusers;
		}catch(Exception e) {
            throw SwtErrorHandler.getInstance().handleException(e,"getAllUsers",MessageInternalManagerImpl.class);

    	}
	}
	
	
	/**
	 * @return
	 * @throws SwtException
	 */
	public List getAllRoles() throws SwtException{
		try{
			log.debug("entering 'getAllRoles' method");
			List allroles=(List) dao.getAllRoles();
			log.debug("exiting 'getAllRoles' method");
			return allroles;
		}catch(Exception e) {
            throw SwtErrorHandler.getInstance().handleException(e,"getAllRoles",MessageInternalManagerImpl.class);

    	}
	}
	
	/**
	 * @return
	 * @throws SwtException
	 */
	public Collection getRoleList() throws SwtException{
		
		try{
			log.debug("entering 'getRoleList' method");
			
			ArrayList roleList = new ArrayList();
			Role role=null;
			Iterator itr = (dao.getRoleList()).iterator(); 
			log.debug("iterator---->"+itr.toString());
			roleList.add(new LabelValueBean("ALL","ALL"));
			log.debug("in iterator");
			while(itr.hasNext())
			{
				role = (Role)(itr.next());
				roleList.add(new LabelValueBean(role.getRoleId(),role.getRoleId()));
			}
			log.debug("roleList->"+roleList);
			log.debug("exiting 'getRoleList' method");
			
			return roleList; 
		}catch(Exception e) {
            throw SwtErrorHandler.getInstance().handleException(e,"getRoleList",MessageInternalManagerImpl.class);

    	}
	}
	
	/**
	 * @param msgint
	 * @param msgintusr
	 * @throws SwtException
	 */
	public void saveMessage(MessageInternal msgint,MessageInternalUser msgintusr) throws SwtException{
		try{
			log.debug("entering 'saveMessage' method");
			int msgId=0;
			dao.saveMessage(msgint);
			List msgList=dao.getMessageId();
			msgId=Integer.parseInt(msgList.get(0).toString());
			log.debug("message id returned===>"+msgId);
			msgintusr.setMessageId(msgId);
			if(msgint.getUserRoleFlag().equals("U")){
				String[] users=msgintusr.getUserId();
				
				for (int i=0;i<users.length;i++){
					
					msgintusr.setUser(users[i]);
					log.debug("msgintuser------------>"+msgintusr);
					dao.saveMessageRecords(msgintusr);
					msgintusr= new MessageInternalUser();
					msgintusr.setMessageId(msgId);
					
				}
			}
			else{
				String[] roles=msgintusr.getRoleId();
				
				for (int i=0;i<roles.length;i++){
					
					msgintusr.setRole(roles[i]);
					log.debug("msgintuser------------>"+msgintusr);
					dao.saveMessageRecords(msgintusr);
					msgintusr= new MessageInternalUser();
					msgintusr.setMessageId(msgId);
					
					
				}
			}		
			
			
			log.debug("exiting 'saveMessage' method");
		}catch(Exception e) {
            throw SwtErrorHandler.getInstance().handleException(e,"saveMessage",MessageInternalManagerImpl.class);

    	}
			
	}
	
	/**
	 * @param msgintuser
	 * @throws SwtException
	 */
	public void saveMessageRecords(MessageInternalUser msgintuser)throws SwtException{
		try{
			log.debug("entering 'saveMessageRecords' method");
			dao.saveMessageRecords(msgintuser);
			log.debug("exiting 'saveMessageRecords' method");
		}catch(Exception e) {
            throw SwtErrorHandler.getInstance().handleException(e,"saveMessageRecords",MessageInternalManagerImpl.class);

    	}
	}
	
	
	/**
	 * @param hostId
	 * @param userList
	 * @return
	 * @throws SwtException
	 */
	public Collection getUserStatus(String hostId, String userList)throws SwtException{
		try{
			log.debug("entering 'getUserStatus' method");
			Collection collUserStatus= dao.getUserStatus(hostId, userList);
			log.debug("exiting 'getUserStatus' method");
			return collUserStatus;
		}catch(Exception e) {
            throw SwtErrorHandler.getInstance().handleException(e,"getUserStatus",MessageInternalManagerImpl.class);

    	}
	}
	
	/**
	 * @param hostId
	 * @param roleList
	 * @return
	 * @throws SwtException
	 */
	public List getUsersforRoles(String hostId,String roleList) throws SwtException{
		try{
			log.debug("entering 'getUsersforRoles' method");
			List collUsersList= (List) dao.getUsersforRoles(hostId, roleList);
			log.debug("exiting 'getUsersforRoles' method");
			return collUsersList;
		}catch(Exception e) {
            throw SwtErrorHandler.getInstance().handleException(e,"getUsersforRoles",MessageInternalManagerImpl.class);

    	}
	}
}