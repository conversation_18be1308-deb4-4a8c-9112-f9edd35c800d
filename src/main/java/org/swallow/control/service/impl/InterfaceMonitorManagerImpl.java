/*
 * @(#)InterfaceMonitorManagerImpl.java 1.0 20/06/2011
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.control.service.impl;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.Iterator;
import java.util.List;
import java.util.TreeMap;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.swallow.control.dao.InterfaceMonitorDAO;
import org.swallow.control.dao.soap.Heartbeat;
import org.swallow.control.dao.soap.Heartbeat.InterfaceInfo.Interface;
import org.swallow.control.dao.soap.Heartbeat.InterfaceInfo.Interface.Beans.Bean;
import org.swallow.control.model.InterfaceMonitor;
import org.swallow.control.service.InterfaceMonitorManager;
import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.util.CacheManager;
import org.swallow.util.OpTimer;
import org.swallow.util.SwtConstants;
import org.swallow.util.SwtUtil;
import org.swallow.util.SystemFormats;

/**
 * This class has methods that are related to Interface Monitor screen.<br>
 * Interface Monitor displays the list of Interfaces and it's enabled status and
 * the total number of messages<br>
 * Awaiting, Processing, Filtered, Bad.<br>
 * 
 * Modified By: Marshal Joel Sudhan .I<br>
 * Date: 20-June-2011
 * 
 */
@Component("interfaceMonitorManager")
public class InterfaceMonitorManagerImpl implements InterfaceMonitorManager {

	// This reference is used for logging purpose
	private final Log log = LogFactory
			.getLog(InterfaceMonitorManagerImpl.class);
	@Autowired
	private InterfaceMonitorDAO interfaceMonitorDAO = null;

	public void setInterfaceMonitorDAO(InterfaceMonitorDAO interfaceMonitorDAO) {
		this.interfaceMonitorDAO = interfaceMonitorDAO;
	}

	public TreeMap<String, TreeMap<String, String>> getInterfaceMonitorDetails(
			String fromDate, String toDate, OpTimer opTimer,
			SystemFormats format, boolean fromPCM) throws SwtException {
		// Holds the Interface active value
		String active = null;
		// Holds the Interface status
		String status = null;
		// Holds the Interface Id
		String interfaceId = null;
		// Holds the Start Date
		Date startDate = null;
		// Holds the End Date
		Date endDate = null;
		// Holds the string value of last message date
		String lastMsgDateStr = null;
		// Holds the Message Status Order
		String[] msgStatusOrder = null;
		// Holds the List of Interface details
		List<InterfaceMonitor> interfaceDetails = null;
		// Holds the temporary interface info in the Map
		TreeMap<String, String> tempResult = null;
		// Holds the resultant interface details in the Map
		TreeMap<String, TreeMap<String, String>> result = null;
		// Holds the temporary HeartBeat info in a TreeMap
		TreeMap<String, String> tempHeartBeatMap = null;
		// Holds the count of processed messages
		String processedMessages = null;
		// Holds the count of awaiting messages
		String awaitingMessages = null;
		// Holds the count of filtered messages
		String filteredMessages = null;
		// Holds the count of bad messages
		String badMessages = null;
		// Holds the total number of messages for a given Interface
		String totalMessages = null;
		// Dates are provided by SI on ISO format
		SimpleDateFormat sdf = new SimpleDateFormat (SwtConstants.ISO_DATE_FORMAT); 
		
        Date engineStartDate = null;
		Date engineCurrentDate = null;		
		//Heartbeat object created using jaxb
		Heartbeat heartbeatInfo = null;
		try {
			log.debug(this.getClass().getName()
					+ " - [getInterfaceMonitorDetails] Begins");
			// Instantiates the Map
			result = new TreeMap<String, TreeMap<String, String>>();
			// Instantiates tempHeartBeatMap
			tempHeartBeatMap = new TreeMap<String, String>();
			// Instantiates the ArrayList
			interfaceDetails = new ArrayList<InterfaceMonitor>();
			// Assigns the Start date value as Date object
			startDate = SwtUtil
					.parseDate(fromDate, format.getDateFormatValue());
			// Assigns the End date value as Date object
			endDate = SwtUtil.parseDate(toDate, format.getDateFormatValue());
			// Calls the method to get the Interface Monitor Details
			interfaceDetails = interfaceMonitorDAO(fromPCM).getInterfaceMonitorDetails(
					startDate, endDate, opTimer, format, fromPCM);
			// Stores the Interface Monitor fields in an Array
			msgStatusOrder = new String[] { SwtConstants.INTERFACE_ACTIVE,
					SwtConstants.INTERFACE_STATUS,
					SwtConstants.INTERFACE_LAST_MESSAGE,
					SwtConstants.INTERFACE_TOTAL_COUNT,
					SwtConstants.INTERFACE_PROCESSED,
					SwtConstants.INTERFACE_AWAITING,
					SwtConstants.INTERFACE_FILTERED, SwtConstants.INTERFACE_BAD };	
			
			//un marshal the XML content into java Heartbeat object
			try{
				//un marshal the XML content into java Heartbeat object
				heartbeatInfo = SwtUtil.getHeartBeat(fromPCM);
			}catch(SwtException e){
				return result;
			} 
			if(heartbeatInfo != null){
			// Get the Engine Start/Current Date
			 engineStartDate = sdf.parse(heartbeatInfo.getEngine().getStartDate());
			 engineCurrentDate = sdf.parse(heartbeatInfo.getEngine().getCurrDate());			 
			 Calendar nowCal = Calendar.getInstance();
			 Calendar engineCurrentDateCal = Calendar.getInstance();  
			 engineCurrentDateCal.setTime(engineCurrentDate);
			
			// Get the list of running interfaces on SI engine
			 List<Interface> interfaces = heartbeatInfo.getInterfaceInfo().getInterface();
			 // Loop on interfaces
			 for(Iterator<Interface> iIt = interfaces.iterator();iIt.hasNext();){
				 Interface iface = iIt.next();
				 String ifaceId = iface.getId();
				 int ifaceStatus = iface.getActive();
				 // Last message date relative to Interface
				 Date lastMsgDate=null;
				 // List of beans in the interface
				 List<Bean> beans  = null;						 
				 // Loop on the list of beans
				 if(iface.getBeans() != null && iface.getBeans().getBean() != null ){
					 beans  = iface.getBeans().getBean();
					 for(Iterator<Bean> bIt = beans.iterator();bIt.hasNext();){
						 Bean bean = bIt.next();
						 if (bean.getName().contains("updater")){
							 if(!SwtUtil.isEmptyOrNull(bean.getLastmsgDate())){
								 lastMsgDate=sdf.parse(bean.getLastmsgDate());							
							 }									 
						 }
					 }
				 }
				 
				lastMsgDateStr = getLastMessageDate(lastMsgDate,format);
				tempHeartBeatMap.put(ifaceId,String.valueOf(ifaceStatus)+","+lastMsgDateStr);
		 }
		}
			// Checks the interfaceDetails and assigns the details in the Map
			if (interfaceDetails.size() > 0) {
				// Iterates through the List and sets the values in the
				// corresponding primitives
				for (InterfaceMonitor monitor : interfaceDetails) {
					// Modified for Mantis1676[Simplified to make more user
					// friendly] by Marshal on 01-Feb-2012
					// Clear the Last Message date value for every iteration so
					// as to avoid duplication to the consecutive interfaces
					lastMsgDateStr = SwtConstants.EMPTY_STRING;
					// Sets the Interface Id
					interfaceId = monitor.getInterfaceId();
					// Sets the Interface active value
					active = monitor.getIsActiveInterface();
					// Gets the total number of processed messages for the given
					// Interface
					processedMessages = monitor.getProcessedMsgs();
					// Gets the total number of messages awaiting for the given
					// Interface
					awaitingMessages = monitor.getAwaitingMsgs();
					// Gets the total number of filtered messages for the given
					// Interface
					filteredMessages = monitor.getFilteredMsgs();
					// Gets the total unparsed messages for the given Interface
					badMessages = monitor.getBadMessages();
					// Calls the method to get the total messages count for the
					// given Interface
					totalMessages = getTotalMessages(monitor);
					// Checks whether the Map has the given key.
					if (!result.containsKey(interfaceId)) {
						// Instantiates the Map to store the result temporarily
						tempResult = new TreeMap<String, String>();
						// Iterates the Array values
						for (String order : msgStatusOrder) {
							// Sets the values as empty string for the given key
							// in the temporary Map
							// Added for Mantis1676[Simplified to make more
							// user friendly] by Marshal on 01-Feb-2012
							tempResult.put(order, SwtConstants.EMPTY_STRING);
						}
						// Sets the values in the actual resultant Map
						result.put(interfaceId, tempResult);
					}
					// Sets the Interface Active value for the respective key
					result.get(interfaceId).put(msgStatusOrder[0], active);
					// Sets the total messages count value for the respective
					// key
					result.get(interfaceId).put(msgStatusOrder[3],
							totalMessages);
					// Sets the processed messages count value for the
					// respective key
					result.get(interfaceId).put(msgStatusOrder[4],
							processedMessages);
					// Sets the awaiting messages count value for the
					// respective key
					result.get(interfaceId).put(msgStatusOrder[5],
							awaitingMessages);
					// Sets the filtered messages count value for the
					// respective key
					result.get(interfaceId).put(msgStatusOrder[6],
							filteredMessages);
					// Sets the bad messages count value for the
					// respective key
					result.get(interfaceId).put(msgStatusOrder[7], badMessages);
					// If the given interfaceId is available in
					// tempHeartBeatMap then get the status based on the
					// active value
					if (tempHeartBeatMap.containsKey(interfaceId) && tempHeartBeatMap.get(interfaceId).startsWith("1")) {
						// Set the status of the Interface as RUNNING
						status = SwtConstants.INTERFACE_STATUS_RUNNING;
						// Sets the Last Message for the respective key
						if (tempHeartBeatMap.get(interfaceId).split(",").length > 1) {
							// Sets the lastMsgDateStr value
							lastMsgDateStr = tempHeartBeatMap.get(interfaceId)
									.split(",")[1].toString();
						} else {
							// Modified for Mantis1676[Simplified to make more
							// user friendly] by Marshal on 01-Feb-2012
							lastMsgDateStr = SwtConstants.EMPTY_STRING;
						}
					} else { // Else get the status as STOPPED
						status = SwtConstants.INTERFACE_STATUS_STOPPED;
					}
					// Sets the engine status of the given Interface
					result.get(interfaceId).put(msgStatusOrder[1], status);
					// Sets the Update Date for the respective key
					result.get(interfaceId).put(msgStatusOrder[2],
							lastMsgDateStr);
				}
			}
			log.debug(this.getClass().getName()
					+ " - [getInterfaceMonitorDetails] Ends");
		} catch (Exception exp) {
			/*--Re-throwing SwtException is restricted here so as to avoid RuntimeException
			 * if there is any exception occur while retrieving HeartBeat details from Smart Input engine --*/
			log
					.warn(this.getClass().getName()
							+ " - Exception Catched in [getInterfaceMonitorDetails] method : - "
							+ exp.getMessage());			
		} finally {
			// Cleaning unreferenced objects
			startDate = null;
			endDate = null;
			active = null;
			status = null;
			interfaceId = null;
			lastMsgDateStr = null;
			msgStatusOrder = null;
		}
		return result;
	}

	public String getDefaultDate(String date, String formatAsString) throws SwtException {
		// Declares the date format
		DateFormat dateFormat = null;
		try {
			log.debug(this.getClass().getName() + " - [getDefaultDate] - "
					+ "Begins");
			dateFormat = new SimpleDateFormat(formatAsString);
			// Checks the date and sets the format to it
			if (date == null || date.equalsIgnoreCase("")
					|| date.equalsIgnoreCase("null")) {
				// Get the test date from system parameters in the given date
				// format
				date = dateFormat.format(SwtUtil
						.getTestDateFromParams(CacheManager.getInstance()
								.getHostId()));
			}
			log.debug(this.getClass().getName() + " - [getDefaultDate] - "
					+ "Ends");
		} catch (Exception exp) {
			log.error(this.getClass().getName() + " - [getDefaultDate] - "
					+ exp.getMessage());
			// Re-throwing SwtException
			throw SwtErrorHandler.getInstance().handleException(exp,
					"getDefaultDate", InterfaceMonitorManagerImpl.class);
		} finally {
			// Cleaning the unreferenced objects
			dateFormat = null;
		}
		return date;
	}

	
	public ArrayList<InterfaceMonitor> getStoredProcedureDetails(
			OpTimer opTimer, SystemFormats format, boolean fromPCM) throws SwtException {
		// Declares the ArrayList object
		ArrayList<InterfaceMonitor> strdProcList = null;
		try {
			log.debug(this.getClass().getName()
					+ " - [getStoredProcedureDetails] Begins");
			// Instantiates the ArrayList
			strdProcList = new ArrayList<InterfaceMonitor>();
			// Calls the method to get the Stored Procedure Details
			strdProcList = interfaceMonitorDAO(fromPCM).getStoredProcedureDetails(
					opTimer, format, fromPCM);
			log.debug(this.getClass().getName()
					+ " - [getStoredProcedureDetails] Ends");
		} catch (Exception exp) {
			log
					.error(this.getClass().getName()
							+ " - Exception Catched in [getStoredProcedureDetails] method : - "
							+ exp.getMessage());
			// Re-throwing SwtException
			throw SwtErrorHandler.getInstance().handleException(exp,
					"getStoredProcedureDetails",
					InterfaceMonitorManagerImpl.class);
		}
		return strdProcList;
	}

	/**
	 * This method is used to calculate the total number of messages for the
	 * given Interface.<br>
	 * The total messages is the summation of processed, awaiting, filtered and
	 * bad messages.<br>
	 * All the four messages' will be fetched from the database package and the
	 * total of them will be calculated.<br>
	 * 
	 * @param monitor
	 *            - InterfaceMonitor object
	 * @return total messages count
	 * @throws SwtException
	 */
	private String getTotalMessages(InterfaceMonitor monitor)
			throws SwtException {
		// Holds the total count of processed messages for the given Interface
		int processedMsgs = 0;
		// Holds the total count of messages to be processed for the given
		// Interface
		int awaitingMsgs = 0;
		// Holds the total count of filtered messages for the given Interface
		int filteredMsgs = 0;
		// Holds the total count of bad messages for the given Interface
		int badMsgs = 0;
		// Holds the total count of messages for the given Interface
		int totalMsgsCount = 0;
		try {
			log.debug(this.getClass().getName()
					+ " - [getTotalMessages] Begins");
			// Gets the integer value of processed messages
			processedMsgs = monitor.getProcessedMsgs() == null ? 0 : Integer
					.parseInt(monitor.getProcessedMsgs());
			// Gets the integer value of awaiting messages
			awaitingMsgs = monitor.getAwaitingMsgs() == null ? 0 : Integer
					.parseInt(monitor.getAwaitingMsgs());
			// Gets the integer value of filtered messages
			filteredMsgs = monitor.getFilteredMsgs() == null ? 0 : Integer
					.parseInt(monitor.getFilteredMsgs());
			// Gets the integer value of bad messages
			badMsgs = monitor.getBadMessages() == null ? 0 : Integer
					.parseInt(monitor.getBadMessages());
			// Gets the summation of all the messages
			totalMsgsCount = processedMsgs + awaitingMsgs + filteredMsgs
					+ badMsgs;
			log.debug(this.getClass().getName() + " - [getTotalMessages] Ends");
		} catch (Exception e) {
			log.error(this.getClass().getName()
					+ " - Exception cought in [getTotalMessages] method : - "
					+ e.getMessage());
			// Re-throwing SwtException
			throw SwtErrorHandler.getInstance().handleException(e,
					"getTotalMessages", InterfaceMonitorManagerImpl.class);
		}
		return String.valueOf(totalMsgsCount);
	}

	/**
	 * This method is used to get the time stamp a message for the Interfaces
	 * lastly executed.<br>
	 * Whenever messages get executed by the Input engine, that execution time
	 * will be displayed in the Last Message column in the Interface Monitor
	 * header.<br>
	 * 
	 * @param messageDate
	 * @param format
	 *            - SystemFormats to format the date value as pet General System
	 *            Parameters settings.
	 * @return Last Message Date
	 */
	private String getLastMessageDate(Date messageDate,
			SystemFormats format) throws SwtException {
		// Holds the last execution date time of the messages
		String lastMsgDateStr = null;
		// Used to format the last message date as per General System Parameters
		// setting
		SimpleDateFormat lastMessageFormat = null;
		try {
			log.debug(this.getClass().getName()
					+ " - [getLastMessageDate] Begins");
			// If the Last Message Date from HeartBeat is Null, show it
			// as blank in the screen
			if (messageDate == null) {
				// Leave lastMsgDateStr as blank if the LastMessageDate is null
				lastMsgDateStr = "";
			} else {
				// Sets the String format of the Date object
				lastMsgDateStr = SwtUtil.formatDate(messageDate, format
						.getDateFormatValue());
				// Instantiates the SimpleDateFormat to set the date
				// value with time stamp in the given format
				lastMessageFormat = new SimpleDateFormat("HH:mm:ss");
				// Sets the last message date as per the General System
				// Parameter along with the time stamp
				lastMsgDateStr = lastMsgDateStr + " "
						+ lastMessageFormat.format(messageDate);
			}
			log.debug(this.getClass().getName()
					+ " - [getLastMessageDate] Ends");
		} catch (Exception e) {
			log.error(this.getClass().getName()
					+ " - Exception cought in [getLastMessageDate] method : - "
					+ e.getMessage());
			// Re-throwing SwtException
			throw SwtErrorHandler.getInstance().handleException(e,
					"getLastMessageDate", InterfaceMonitorManagerImpl.class);
		} finally {
			// Nullifying unreferenced objects
			lastMessageFormat = null;
		}
		return lastMsgDateStr;
	}
	
	/**
	 * Get the correct DAO object from spring context
	 * 
	 * @param fromPCM
	 * @return InterfaceMonitorDAO
	 */
	private InterfaceMonitorDAO interfaceMonitorDAO(boolean fromPCM) {
		if (!fromPCM) {
			return interfaceMonitorDAO;
		} else {
			return (InterfaceMonitorDAO) SwtUtil.getBean("interfaceMonitorDAOPCM");
		}
	}
}