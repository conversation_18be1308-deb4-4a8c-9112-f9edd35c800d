/* @(#)SchedulerManagerImpl.java 1.0 27/01/06
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */

package org.swallow.control.service.impl;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collection;
import java.util.Date;
import java.util.Iterator;
import java.util.StringTokenizer;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.swallow.util.LabelValueBean;
import org.quartz.SchedulerException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.swallow.batchScheduler.SwtJobScheduler;
import org.swallow.cluster.ZkUtils;
import org.swallow.control.dao.InterfaceInterruptDAO;
import org.swallow.control.dao.SchedulerDAO;
import org.swallow.control.dao.hibernate.SchedulerDAOHibernate;
import org.swallow.control.model.Job;
import org.swallow.control.model.JobStatus;
import org.swallow.control.model.Role;
import org.swallow.control.model.ScheduledReportParams;
import org.swallow.control.model.ScheduledReportType;
import org.swallow.control.model.Scheduler;
import org.swallow.control.service.SchedulerManager;
import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.maintenance.model.MiscParams;
import org.swallow.maintenance.service.impl.CurrencyManagerImpl;
import org.swallow.model.User;
import org.swallow.util.CacheManager;
import org.swallow.util.SwtConstants;
import org.swallow.util.SwtUtil;
import org.swallow.util.SystemFormats;
import org.swallow.util.SystemInfo;

/*
 * This is manager class for Scheduler screen .used to add New Job, update
 * Previous job,Delete Existing Job and get job details
 */
@Component("schedulerManager")
@Transactional
public class SchedulerManagerImpl implements SchedulerManager {
	private static final String CYCLE_DURATION = "C";

	private static final String ONCE = "O";

	private static final String MONTHLY = "M";

	private static final String WEEKLY = "W";

	private static final String DAILY = "D";

	public static final String JOB_TYPE_MANUAL = "N";

	private final Log log = LogFactory.getLog(SchedulerManagerImpl.class);

	@Autowired
	private SchedulerDAO dao;

	private InterfaceInterruptDAO interfaceInterruptDAO;

	public void setSchedulerDAO(SchedulerDAO dao) {
		this.dao = dao;
	}

	public Collection getJobStatusDetails(String hostId,
			SystemFormats systemFormats, String entityId, String scheduledJobType) throws SwtException {
		log.debug("Entering 'getJobStatusDetails' Method");
		Collection jobStatusDetails = new ArrayList();
		try {
			JobStatus jobStatusItems = null;
			jobStatusDetails = dao.getJobStatusDetails(hostId, scheduledJobType);

			Iterator itr = jobStatusDetails.iterator();
			String dateFormat = systemFormats.getDateFormatValue();
			String jobDetailDesc = "";
			while (itr.hasNext()) {
				jobDetailDesc = "";
				jobStatusItems = (JobStatus) itr.next();

				if ((jobStatusItems.getLastExecTime() != null)) {
					SimpleDateFormat sdf = new SimpleDateFormat(dateFormat
							+ " HH:mm:ss");
					jobStatusItems.setLastExecTimeAsString(sdf
							.format(jobStatusItems.getLastExecTime()));
				}

				if ((jobStatusItems.getNextExecTime() != null)) {
					SimpleDateFormat sdf = new SimpleDateFormat(dateFormat
							+ " HH:mm:ss");
					jobStatusItems.setNextExecTimeAsString(sdf
							.format(jobStatusItems.getNextExecTime()));
				}

				if (jobStatusItems.getCurrentStatus() != null) {
					Collection coll = (Collection) CacheManager.getInstance()
							.getMiscParams("CURRENTSTATUS", entityId);

					Iterator itr1 = coll.iterator();

					while (itr1.hasNext()) {
						MiscParams mp = (MiscParams) (itr1.next());
						String temp = mp.getParValue();

						if (jobStatusItems.getCurrentStatus().equals(
								mp.getId().getKey2())) {
							jobStatusItems.setCurrentStatusName(mp
									.getParValue());

						}
					}
				}
				if (jobStatusItems.getLastExecStatus() != null) {
					Collection coll = (Collection) CacheManager.getInstance()
							.getMiscParams("LASTSTATUS", entityId);

					Iterator itr1 = coll.iterator();

					while (itr1.hasNext()) {
						MiscParams mp = (MiscParams) (itr1.next());
						String temp = mp.getId().getKey2();

						if (jobStatusItems.getLastExecStatus().equals(
								mp.getId().getKey2())) {
							jobStatusItems.setLastExecStatusName(mp
									.getParValue());
						}
					}
				}
				if (jobStatusItems.getJobType().equals(
						SwtConstants.JOB_TYPE_ONCE)) {
					if ((jobStatusItems.getScheduleDate() != null)
							&& (jobStatusItems.getScheduleTime() != null)) {
						jobStatusItems.setScheduleDateAsString(SwtUtil
								.formatDate(jobStatusItems.getScheduleDate(),
										systemFormats.getDateFormatValue()));
						jobDetailDesc = jobDetailDesc
								+ jobStatusItems.getScheduleDateAsString()
								+ " " + jobStatusItems.getScheduleTime();
					}
				} else if (jobStatusItems.getJobType().equals(
						SwtConstants.JOB_TYPE_CYLIC)) {
					String hrs = "";
					String mins = "";
					String secs = "";

					if ((jobStatusItems.getDurationHrs() != null)
							&& (jobStatusItems.getDurationMins() != null)
							&& (jobStatusItems.getDurationSecs() != null)) {
						if (jobStatusItems.getDurationHrs().intValue() < 10) {
							hrs = "0"
									+ jobStatusItems.getDurationHrs()
											.toString();
						} else {
							hrs = jobStatusItems.getDurationHrs().toString();
						}

						if (jobStatusItems.getDurationMins().intValue() < 10) {
							mins = "0"
									+ jobStatusItems.getDurationMins()
											.toString();
						} else {
							mins = jobStatusItems.getDurationMins().toString();
						}

						if (jobStatusItems.getDurationSecs().intValue() < 10) {
							secs = "0"
									+ jobStatusItems.getDurationSecs()
											.toString();
						} else {
							secs = jobStatusItems.getDurationSecs().toString();
						}
					}

					jobDetailDesc = jobDetailDesc + hrs + ":" + mins + ":"
							+ secs;
				} else if (jobStatusItems.getJobType().equals(
						SwtConstants.JOB_TYPE_DAILY)) {
					if (jobStatusItems.getScheduleTime() != null) {
						jobDetailDesc = jobDetailDesc
								+ jobStatusItems.getScheduleTime();
					}
				} else if (jobStatusItems.getJobType().equals(
						SwtConstants.JOB_TYPE_WEEKLY)) {
					if (jobStatusItems.getScheduleDay() != null) {
						String schDay = jobStatusItems.getScheduleDay();

						if (schDay.equals("MON,TUE,WED,THU,FRI,SAT,SUN")) {
							jobDetailDesc = "ALL";
						} else {
							jobDetailDesc = schDay;
						}
					}
				} else if (jobStatusItems.getJobType().equals(
						SwtConstants.JOB_TYPE_MONTHLY)) {
					if ((jobStatusItems.getMonthFirst() != null)
							&& (jobStatusItems.getScheduleTime() != null)) {
						jobDetailDesc = "First Day" + " "
								+ jobStatusItems.getScheduleTime();
					} else if ((jobStatusItems.getMonthLast() != null)
							&& (jobStatusItems.getScheduleTime() != null)) {
						jobDetailDesc = "Last Day" + " "
								+ jobStatusItems.getScheduleTime();
					} else if ((jobStatusItems.getMonthDate() != null)
							&& (jobStatusItems.getScheduleTime() != null)) {
						jobDetailDesc = jobStatusItems.getMonthDate()
								.toString()
								+ " " + jobStatusItems.getScheduleTime();

						if (jobStatusItems.getMonthDate().intValue() < 10) {
							jobDetailDesc = "0" + jobDetailDesc;
						}
					}
				}

				jobStatusItems.setJobDetails(jobDetailDesc);

				if (jobStatusItems.getJobType().equals(
						SwtConstants.JOB_TYPE_MANUAL)
						&& jobStatusItems.getJobStatus().equals(
								SwtConstants.ADDJOB_ENABLE)) {
					if ((jobStatusItems.getCurrentStatus() != null)
							&& jobStatusItems.getCurrentStatus().equals(
									SwtConstants.JOB_STATUS_PENDING)) {
						jobStatusItems.setCurrentStatusName("");
						jobStatusItems.setCurrentStatus("");
					}
				}
			}

			log.debug("Exiting 'getJobStatusDetails' Method");
		} catch (Exception e) {
			log
					.error("Exception occurred in SchedulerManagerImpl.getJobStatusDetails()");
			throw SwtErrorHandler.getInstance().handleException(e,
					"getJobStatusDetails", SchedulerManagerImpl.class);
		}
		return jobStatusDetails;
	}

	public Collection getJobNameList(String hostId) throws SwtException {
		log.debug("Entering 'getJobNameList' Method");

		ArrayList jobNameList = new ArrayList();
		try {
			Iterator itr = (dao.getJobNameList(hostId)).iterator();
			Job job = null;
			while (itr.hasNext()) {
				job = (Job) (itr.next());
				jobNameList.add(new LabelValueBean(job.getJobDescription(), job
						.getId().getJobId()));
			}

			log.debug("Exiting 'getJobNameList' Method");
		} catch (Exception e) {
			log
					.error("Exception occurred in SchedulerManagerImpl.getJobNameList()");
			throw SwtErrorHandler.getInstance().handleException(e,
					"getJobNameList", SchedulerManagerImpl.class);
		}
		return jobNameList;
	}

	public Collection getJobNames(String hostId) throws SwtException {
		log.debug("Entering 'getJobNames' Method");
		Collection jobNames = new ArrayList();
		try {
			jobNames = dao.getJobNames(hostId);
			log.debug("Returning from 'getJobNames' Method");
		} catch (Exception e) {
			log
					.error("Exception occurred in SchedulerManagerImpl.getJobNames()");
			throw SwtErrorHandler.getInstance().handleException(e,
					"getJobNames", SchedulerManagerImpl.class);
		}
		return jobNames;
	}

	public Collection getSchedulerDetails(Integer scheduleId,
			SystemFormats systemFormats) throws SwtException {
		log.debug("Entering 'getSchedulerDetails' Method");

		Collection jobSchedelurDetails = new ArrayList();
		ScheduledReportParams scheduledReportParams = null;
		try {
			Scheduler schedulerItems = null;

			jobSchedelurDetails = dao.getSchedulerDetails(scheduleId);
			scheduledReportParams = dao.getScheduledReportParams(scheduleId);
			Iterator itr = jobSchedelurDetails.iterator();

			while (itr.hasNext()) {
				schedulerItems = (Scheduler) itr.next();
				schedulerItems.setScheduledReportParams(scheduledReportParams);
				if(systemFormats != null) {
					schedulerItems.setStartingDateAsString(SwtUtil.formatDate(
							schedulerItems.getStartingDate(), systemFormats
									.getDateFormatValue()));
					schedulerItems.setEndingDateAsString(SwtUtil.formatDate(
							schedulerItems.getEndingDate(), systemFormats
									.getDateFormatValue()));
					schedulerItems.setScheduleDateAsString(SwtUtil.formatDate(
							schedulerItems.getScheduleDate(), systemFormats
									.getDateFormatValue()));
				}

				schedulerItems.setMonthDateAsString((schedulerItems
						.getMonthDate() == null) ? "" : ("" + schedulerItems
						.getMonthDate().intValue()));

				String scheduleDayConcat = schedulerItems.getScheduleDay();

				if (scheduleDayConcat != null) {
					StringTokenizer st = new StringTokenizer(scheduleDayConcat,
							",");

					while (st.hasMoreTokens()) {
						String temp = st.nextToken();
						if (temp.equalsIgnoreCase(SwtConstants.WEEKDAY_MON)) {
							schedulerItems.setScheduleDayMon(temp);
						} else if (temp
								.equalsIgnoreCase(SwtConstants.WEEKDAY_TUE)) {
							schedulerItems.setScheduleDayTue(temp);
						} else if (temp
								.equalsIgnoreCase(SwtConstants.WEEKDAY_WED)) {
							schedulerItems.setScheduleDayWed(temp);
						} else if (temp
								.equalsIgnoreCase(SwtConstants.WEEKDAY_THU)) {
							schedulerItems.setScheduleDayThr(temp);
						} else if (temp
								.equalsIgnoreCase(SwtConstants.WEEKDAY_FRI)) {
							schedulerItems.setScheduleDayFri(temp);
						} else if (temp
								.equalsIgnoreCase(SwtConstants.WEEKDAY_SAT)) {
							schedulerItems.setScheduleDaySat(temp);
						} else if (temp
								.equalsIgnoreCase(SwtConstants.WEEKDAY_SUN)) {
							schedulerItems.setScheduleDaySun(temp);
						}

					}
				}
			}

			log.debug("Exiting 'getSchedulerDetails' Method");
		} catch (Exception e) {
			e.printStackTrace();
			log
					.error("Exception occurred in SchedulerManagerImpl.getSchedulerDetails()");
			throw SwtErrorHandler.getInstance().handleException(e,
					"getSchedulerDetails", SchedulerManagerImpl.class);
		}
		return jobSchedelurDetails;
	}

	/**
	 * This Method is use to save and register the new job detail
	 * 
	 * @param scheduler
	 * @param systemFormats
	 * @throws SwtException
	 */
	public void saveSchedulerDetail(Scheduler scheduler,
			SystemFormats systemFormats) throws SwtException {
		// Declared to format end date
		SimpleDateFormat sdf = null;
		// declared to hold the Job End Date as string
		String endDate = null;
		// declared to hold the Job End Date
		Date date = null;
		// declared to hold the Start Date
		Date startDate = null;
		// declared hold the Schedule Day
		StringBuilder scheduleDayConcat = null;
		// declared to hold the StringTokenizer
		StringTokenizer strToken = null;
		// declared to hold the values of strToken
		String splitByNull = null;
		// declared to hold the values of strToken
		String splitByComma = null;
		// declared hold the length of the splitByComma
		int len = 0;
		// Declared to hold the scheduleDay
		String scheduleDay = null;
		try {
			log.debug(this.getClass().getName()
					+ "- [saveSchedulerDetail] - Entering ");
			if (scheduler.getJobType().equals(SwtConstants.JOB_TYPE_ONCE)) {
				Date d = new Date();
				String scheduleTime = scheduler.getScheduleTime();
				String scheduleHrs = scheduleTime.substring(0, 2);
				String scheduleMin = scheduleTime.substring(3, 5);
				int scHrs = new Integer(scheduleHrs).intValue();
				int scMin = new Integer(scheduleMin).intValue();
				Date schDate = SwtUtil.parseDate(scheduler
						.getScheduleDateAsString(), systemFormats
						.getDateFormatValue());
				Calendar cal = Calendar.getInstance();
				cal.setTime(schDate);
				
				cal.set(Calendar.HOUR, scHrs);
				cal.set(Calendar.MINUTE, scMin);
				
				if (d.after(cal.getTime())) {
					throw new SchedulerException();
				}
			}
			/*
			 * Ending date is set to 12/12/2059. This is not entered through
			 * front End
			 */
			sdf = new SimpleDateFormat(SwtConstants.FULL_DATE_FORMAT);
			endDate = SwtConstants.JOB_END_DATE;
			date = sdf.parse(endDate);
			scheduler.setEndingDate(date);
			scheduler.setEndingTime(SwtConstants.JOB_END_TIME);

			/* Starting date is set to same as present date */
			sdf = new SimpleDateFormat("HH:mm");
			startDate = new Date();
			scheduler.setStartingDate(startDate);
			scheduler.setStartingTime(sdf.format(startDate));
			// Parsing String to Date and set to bean
			if (((scheduler.getScheduleDateAsString() != null) && (scheduler
					.getScheduleDateAsString().length() > 0))) {

				scheduler.setScheduleDate(SwtUtil.parseDate(scheduler
						.getScheduleDateAsString(), systemFormats
						.getDateFormatValue()));
			}

			if ((scheduler.getMonthDateAsString() != null)
					&& (scheduler.getMonthDateAsString().length() > 0)) {
				scheduler.setMonthDate(new Integer(scheduler
						.getMonthDateAsString()));
			}
			// Concatenating scheduler days
			scheduleDayConcat = new StringBuilder();
			scheduleDayConcat.append(scheduler.getScheduleDayMon()).append(",")
					.append(scheduler.getScheduleDayTue()).append(",").append(
							scheduler.getScheduleDayWed()).append(",").append(
							scheduler.getScheduleDayThr()).append(",").append(
							scheduler.getScheduleDayFri()).append(",").append(
							scheduler.getScheduleDaySat()).append(",").append(
							scheduler.getScheduleDaySun());

			// splitting Scheduler days using String tokenizer and set Schedule
			// Day in bean
			if (scheduleDayConcat.toString().length() != 34) {

				strToken = new StringTokenizer(scheduleDayConcat.toString(),
						"null");
				splitByNull = new String();

				while (strToken.hasMoreTokens()) {
					splitByNull = splitByNull + strToken.nextToken();
				}

				splitByComma = new String();
				strToken = new StringTokenizer(splitByNull, ",");

				while (strToken.hasMoreTokens()) {
					splitByComma = splitByComma + strToken.nextToken() + ",";
				}

				len = splitByComma.length();
				scheduleDay = splitByComma.substring(0, len - 1);
			} else if ((scheduler.getScheduleDayAll() != null)
					&& (scheduler.getScheduleDayAll().equals("ALL"))) {
				scheduleDay = "MON,TUE,WED,THU,FRI,SAT,SUN";
			} else {
				scheduleDay = "";
			}
			scheduler.setScheduleDay(scheduleDay);
			// Code Modified for Mantis 2079 by Chinna on 1-Nov-2012:Next
			// execution time should not display for manual Job
			dao.saveSchedulerDetail(scheduler);
			// register the job details
			if (scheduler.isUpdateUserNeedUpdated()) {
				SwtJobScheduler swtJobScheduler = SwtJobScheduler.getInstance();
				swtJobScheduler.addUpdate(scheduler, "A");
				SwtUtil.addJobToList(""+scheduler.getScheduleId());
				ZkUtils.setSerializableProperty(ZkUtils.PROPERTY_ADD_SCHEDULER+"---"+ZkUtils.instanceUuid+"---"+scheduler.getScheduleId(),scheduler);

				
			} else {
				saveJobStatus(getJobStatus(scheduler));
			}

		} catch (SchedulerException se) {
			log
					.error(this.getClass().getName()
							+ " - Exception Catched in [saveSchedulerDetail] method : - "
							+ se.getMessage());
			throw new SwtException("scheduler.rangeNotCovered", "N");
		} catch (ArithmeticException aeex) {
			log
					.error(this.getClass().getName()
							+ " - Exception Catched in [saveSchedulerDetail] method : - "
							+ aeex.getMessage());
		} catch (SwtException exp) {
			if(!SwtErrorHandler.JOB_SAME_LOCATION_ERROR.equals(exp.getErrorCode()) && !SwtErrorHandler.JOB_SAME_NAME_ERROR.equals(exp.getErrorCode())) {
				log.error(this.getClass().getName()
								+ "- [saveSchedulerDetail] - Exception "
								+ exp.getMessage());
			}
			throw SwtErrorHandler.getInstance().handleException(exp,
					"saveSchedulerDetail", SchedulerDAOHibernate.class);
		} catch (Exception e) {
			log
					.error(this.getClass().getName()
							+ " - Exception Catched in [saveSchedulerDetail] method : - "
							+ e.getMessage());
			throw SwtErrorHandler.getInstance().handleException(e,
					"saveSchedulerDetail", CurrencyManagerImpl.class);
		} finally {
			// Nullify
			sdf = null;
			endDate = null;
			date = null;
			startDate = null;
			scheduleDayConcat = null;
			strToken = null;
			splitByNull = null;
			splitByComma = null;
			scheduleDay = null;
			log.debug(this.getClass().getName()
					+ "- [saveSchedulerDetail] - Exit ");
		}

	} // End of saveSchedulerDetail() method

	/**
	 * @param scheduler -
	 *            Scheduler
	 * @exception SwtException
	 */
	public void updateSchedulerDetail(Scheduler scheduler,
			SystemInfo systemInfo, SystemFormats systemFormats,
			boolean systemJobFlag) throws SwtException {
		try {
			log.debug("entering 'updateSchedulerDetail' method");

			/*
			 * For jobType = Once if we try to change a job and set its
			 * scheduled time of a previous time, which has passed then quartz
			 * procedure does not throw the exception. To deal with that we are
			 * here validating that scheduled period has not already passed, and
			 * throwing an exception if that happens.
			 */
			if (scheduler.getJobType().equals(SwtConstants.JOB_TYPE_ONCE)) {
				Date d = new Date();
				String scheduleTime = scheduler.getScheduleTime();
				String scheduleHrs = scheduleTime.substring(0, 2);
				String scheduleMin = scheduleTime.substring(3, 5);
				int scHrs = new Integer(scheduleHrs).intValue();
				int scMin = new Integer(scheduleMin).intValue();
				Date schDate = SwtUtil.parseDate(scheduler
						.getScheduleDateAsString(), systemFormats
						.getDateFormatValue());
				Calendar cal = Calendar.getInstance();
				cal.setTime(schDate);
				
				cal.set(Calendar.HOUR, scHrs);
				cal.set(Calendar.MINUTE, scMin);
				
				if (d.after(cal.getTime())) {
					throw new SchedulerException();
				}
			}

			if ((scheduler.getMonthDateAsString() != null)
					&& (scheduler.getMonthDateAsString().length() > 0)) {
				scheduler.setMonthDate(new Integer(scheduler
						.getMonthDateAsString()));
			}

			if ((scheduler.getScheduleDateAsString() != null)
					&& !(scheduler.getScheduleDateAsString()
							.equalsIgnoreCase(""))) {
				scheduler.setScheduleDate(SwtUtil.parseDate(scheduler
						.getScheduleDateAsString(), systemFormats
						.getDateFormatValue()));
			}

			if (scheduler.getJobType().equalsIgnoreCase("W")) {
				scheduler.setDurationHours(null);
				scheduler.setDurationMins(null);
				scheduler.setDurationSecs(null);
				scheduler.setMonthDate(null);
			}

			String scheduleDayConcat = null;
			scheduleDayConcat = scheduler.getScheduleDayMon() + ","
					+ scheduler.getScheduleDayTue() + ","
					+ scheduler.getScheduleDayWed() + ","
					+ scheduler.getScheduleDayThr() + ","
					+ scheduler.getScheduleDayFri() + ","
					+ scheduler.getScheduleDaySat() + ","
					+ scheduler.getScheduleDaySun();

			if (scheduleDayConcat.length() != 34) {
				StringTokenizer st = new StringTokenizer(scheduleDayConcat,
						"null");
				String temp = new String();

				while (st.hasMoreTokens()) {
					temp = temp + st.nextToken();
				}

				String temp2 = new String();
				st = new StringTokenizer(temp, ",");

				while (st.hasMoreTokens()) {
					temp2 = temp2 + st.nextToken() + ",";
				}

				int len = temp2.length();
				scheduleDayConcat = temp2.substring(0, len - 1);
			} else if ((scheduler.getScheduleDayAll() != null)
					&& (scheduler.getScheduleDayAll().equals("ALL"))) {
				scheduleDayConcat = "MON,TUE,WED,THU,FRI,SAT,SUN";
			} else {
				scheduleDayConcat = "";
			}

			scheduler.setScheduleDay(scheduleDayConcat);
			
			dao.updateSchedulerDetail(scheduler);
			
			if (systemJobFlag) {
				SwtJobScheduler swtJobScheduler = SwtJobScheduler.getInstance();
				swtJobScheduler.addUpdate(scheduler, "U");
				ZkUtils.setSerializableProperty(ZkUtils.PROPERTY_UPDATE_SCHEDULER+"---"+ZkUtils.instanceUuid+"---"+scheduler.getScheduleId(),scheduler);
				
			}


		} catch (SchedulerException se) {
			se.printStackTrace();
			log
					.error(this.getClass().getName()
							+ " - Exception Catched in [updateSchedulerDetail] method : - "
							+ se.getMessage());

			throw new SwtException("scheduler.rangeNotCovered", "N");
		} catch (SwtException exp) {
			if(!SwtErrorHandler.JOB_SAME_LOCATION_ERROR.equals(exp.getErrorCode()) && !SwtErrorHandler.JOB_SAME_NAME_ERROR.equals(exp.getErrorCode())) {
				log.error(this.getClass().getName()
								+ "- [saveSchedulerDetail] - Exception "
								+ exp.getMessage());
			}
			throw SwtErrorHandler.getInstance().handleException(exp,
					"saveSchedulerDetail", SchedulerDAOHibernate.class);
		} catch (Exception e) {
			e.printStackTrace();
			log
					.error(this.getClass().getName()
							+ " - Exception Catched in [updateSchedulerDetail] method : - "
							+ e.getMessage());
			throw SwtErrorHandler.getInstance().handleException(e,
					"updateSchedulerDetail", SchedulerManagerImpl.class);
		}
	} // End of updateSchedulerDetail() method

	/**
	 * @param scheduler -
	 *            Scheduler
	 * @exception SwtException
	 */
	public void deleteSchedulerDetail(Scheduler scheduler,
			SystemInfo systemInfo, SystemFormats sysforma) throws SwtException {
		try {
			log.debug("entering deleteSchedulerDetail");
			scheduler.setJobStatus("E");
			dao.deleteSchedulerDetail(scheduler);
			log.debug("exiting 'deleteSchedulerDetail' method");
		} catch (Exception e) {
			log
					.error("Exception occurred in SchedulerManagerImpl.deleteSchedulerDetail()");

			throw SwtErrorHandler.getInstance().handleException(e,
					"deleteSchedulerDetail", SchedulerManagerImpl.class);
		}
	} // End of deleteSchedulerDetail() method

	/**
	 * This method used to get the job's details.
	 * 
	 * @param hostId
	 * @param jobId
	 * @return
	 * @throws SwtException
	 */
	public Job getJobDetail(String hostId, String jobId) throws SwtException {
		log.debug("Inside SchedulerManagerImpl.getJobDetial Method");
		Job jobObj = new Job();
		try {
			jobObj = dao.getJobDetail(hostId, jobId);
		} catch (Exception e) {
			log
					.error("Exception occurred in SchedulerManagerImpl.getJobDetail()");
			throw SwtErrorHandler.getInstance().handleException(e,
					"getJobDetail", SchedulerManagerImpl.class);
		}
		return jobObj;
	}

	/**
	 * This method is added for integration of Input System. as when we schedule
	 * an input job one row has to be inserted into S_JOB_STATUS table. This
	 * method is used for that purpose. Incase of System job we dont use this
	 * function as the is wrtten in the Scheduler's code.
	 * 
	 * @param jobStatus
	 * @throws SwtException
	 */
	public void saveJobStatus(JobStatus jobStatus) throws SwtException {
		log.debug("Adding record into S_JOB_STATUS table");
		try {
			dao.saveJobStatus(jobStatus);
			log.debug("INSERT into S_JOB_STATUS successful");
		} catch (Exception e) {
			log
					.error("Exception occurred in SchedulerManagerImpl.saveJobStatus()");

			throw SwtErrorHandler.getInstance().handleException(e,
					"saveJobStatus", SchedulerManagerImpl.class);
		}
	}

	/**
	 * This method used to get the JobStatus details.
	 * 
	 * @param scheduleId
	 * @return
	 * @throws SwtException
	 */
	public JobStatus getJobStatus(Integer scheduleId)
			throws SwtException {
		JobStatus jobStatusObj = new JobStatus();
		try {
			jobStatusObj = dao.getJobStatus(scheduleId);
		} catch (Exception e) {
			log
					.error("Exception occurred in SchedulerManagerImpl.getJobStatus()");

			throw SwtErrorHandler.getInstance().handleException(e,
					"getJobStatus", SchedulerManagerImpl.class);
		}
		return jobStatusObj;
	}

	/**
	 * This method is added for integration of Input System. as when we enable
	 * or disable an input job one row has to be inserted into S_JOB_STATUS
	 * table. This method is used for that purpose. Incase of System job we dont
	 * use this function as the is wrtten in the Scheduler's code.
	 * 
	 * @param jobStatus
	 * @throws SwtException
	 */
	public void updateJobStatus(JobStatus jobStatus) throws SwtException {
		log.debug("updating record into S_JOB_STATUS table");
		try {
			dao.updateJobStatus(jobStatus);
			log.debug("update on S_JOB_STATUS successful");
		} catch (Exception e) {
			log
					.error("Exception occurred in SchedulerManagerImpl.updateJobStatus()");

			throw SwtErrorHandler.getInstance().handleException(e,
					"updateJobStatus", SchedulerManagerImpl.class);
		}
	}

	/**
	 * Create the instance of JobStatus object from scheduler details.
	 * 
	 * @param scheduler
	 * @return
	 * @throws SwtException
	 */
	private JobStatus getJobStatus(Scheduler scheduler) throws SwtException {
		JobStatus jobStatus = new JobStatus();
		try {
			jobStatus.setScheduleId(scheduler.getScheduleId());
			jobStatus.setHostId(scheduler.getHostId());
			jobStatus.setJobId((scheduler.getJobId()));

			if (scheduler.getJobStatus().equalsIgnoreCase("E")) {
				jobStatus.setCurrentStatus("P");
			} else {
				jobStatus.setCurrentStatus("D");
			}

		} catch (Exception e) {
			log
					.error("Exception occurred in SchedulerManagerImpl.getJobStatus(Scheduler scheduler)");
			throw SwtErrorHandler.getInstance().handleException(e,
					"getJobStatus", SchedulerManagerImpl.class);
		}
		return jobStatus;
	}

	/**
	 * Deletes the notifications while removing the notifications scheduler.
	 * 
	 * @param jobId
	 * @throws SwtException
	 */
	public void deleteNotifications(String jobId) throws SwtException {
		log
				.debug(this.getClass().getName()
						+ "- [deleteNotifications] - Entry");
		interfaceInterruptDAO = (InterfaceInterruptDAO) SwtUtil
				.getBean("interfaceInterruptDAO");
		if (jobId.equals(SwtConstants.PCM_INTERFACE_NOTIFICATION_JOBID)){
			interfaceInterruptDAO
			.deleteNotificationDisable(SwtConstants.INTERFACE_NOTIFICATION_TYPE, true);
			interfaceInterruptDAO
			.deleteNotificationDisable(SwtConstants.MESSAGE_NOTIFICATION_TYPE, true);
		} else {
			interfaceInterruptDAO
			.deleteNotificationDisable(SwtConstants.INTERFACE_NOTIFICATION_TYPE, false);
			interfaceInterruptDAO
			.deleteNotificationDisable(SwtConstants.MESSAGE_NOTIFICATION_TYPE, false);
		}
		log
				.debug(this.getClass().getName()
						+ "- [deleteNotifications] - Entry");
	}

	/**
	 * Get the scheduler job type details
	 * 
	 * @return Scheduler object
	 * @throws SwtException
	 */
	public Scheduler getJobType() throws SwtException {
		// Declare the Scheduler object
		Scheduler scheduler = null;
		// variable to hold the host Id
		String hostId = null;
		try {
			// Create instance for Scheduler
			scheduler = new Scheduler();
			// get the currenct host Id
			hostId = SwtUtil.getCurrentHostId();
			// get the job type
			scheduler = dao.getJobType(hostId);
		} catch (Exception e) {
			log.error(this.getClass().getName() + " - [getJobType] - "
					+ e.getMessage());
			throw SwtErrorHandler.getInstance().handleException(e,
					"getJobType", SchedulerManagerImpl.class);
		}
		return scheduler;
	}
	
	/**
	 * This method returns the list of report jobs
	 * @param hostId
	 * @return
	 * @throws SwtException
	 */
	public 	ArrayList<Job> getReportsJobList(String hostId) throws SwtException {
		ArrayList<Job> list;
		try {
			list = dao.getReportsJobList(hostId);
		} catch (Exception e) {
			e.printStackTrace();
			log
					.error("Exception occurred in SchedulerManagerImpl.getReportsJobList()");

			throw SwtErrorHandler.getInstance().handleException(e,
					"getReportsJobList", SchedulerManagerImpl.class);
		}
		return list;
	}
	
	/**
	 * This method used to get the reports type list for the selected Job id
	 * 
	 * @param hostId
	 * @return
	 * @throws SwtException
	 */
	public 	ArrayList<ScheduledReportType> getReportTypes(String hostId, String jobId) throws SwtException {
		ArrayList<ScheduledReportType> list;
		try {
			list = dao.getReportTypes(hostId, jobId);
		} catch (Exception e) {
			e.printStackTrace();
			log
					.error("Exception occurred in SchedulerManagerImpl.getReportsJobList()");

			throw SwtErrorHandler.getInstance().handleException(e,
					"getReportsJobList", SchedulerManagerImpl.class);
		}
		return list;
	}
	
	public Collection getJobTypeList(String hostId) throws SwtException {
		log.debug("Entering 'getJobTypeList' Method");

		Collection jobTypeList;
		try {
			jobTypeList= dao.getJobTypeList(hostId);

			log.debug("Exiting 'getJobTypeList' Method");
		} catch (Exception e) {
			log
					.error("Exception occurred in SchedulerManagerImpl.getJobTypeList()");
			throw SwtErrorHandler.getInstance().handleException(e,
					"getJobTypeList", SchedulerManagerImpl.class);
		}
		return jobTypeList;
	}

	public Collection getJobNameList(String hostId, String selectedJobType) throws SwtException {
		ArrayList jobNameList = new ArrayList();
		try {
			Iterator itr = (dao.getJobNameList(hostId, selectedJobType)).iterator();
			Job job = null;
			while (itr.hasNext()) {
				job = (Job) (itr.next());
				jobNameList.add(new LabelValueBean(job.getJobDescription(), job
						.getId().getJobId()));
			}

			log.debug("Exiting 'getJobNameList' Method");
		} catch (Exception e) {
			log
					.error("Exception occurred in SchedulerManagerImpl.getJobNameList()");
			throw SwtErrorHandler.getInstance().handleException(e,
					"getJobNameList", SchedulerManagerImpl.class);
		}
		return jobNameList;
	}
	
	public Collection getUserList(String hostId, String userList) throws SwtException{
	
		try{
			log.debug("entering 'getUserList' method");
			ArrayList returnUserList = new ArrayList();
			Iterator itr = (dao.getUserList(hostId, userList)).iterator(); 
			while(itr.hasNext())
			{
				returnUserList.add(itr.next());
			}
			log.debug("exiting 'getUserList' method");
			return returnUserList;
		}catch(Exception e) {
            throw SwtErrorHandler.getInstance().handleException(e,"getUserList", SchedulerManagerImpl.class);

    	}
	}

	public Collection getRoleList(String hostId, String roleList) throws SwtException {
		try{
			log.debug("entering 'getRoleList' method");
			ArrayList returnRoleList = new ArrayList();
			Role role =null;
			Iterator itr = (dao.getRoleList(hostId, roleList)).iterator(); 
			while(itr.hasNext())
			{
				returnRoleList.add(itr.next());
			}
			log.debug("exiting 'getRoleList' method");
			return returnRoleList;
		}catch(Exception e) {
            throw SwtErrorHandler.getInstance().handleException(e,"getRoleList", SchedulerManagerImpl.class);

    	}
	}
	
	
	public String getScreenDetails(String menuItemId) throws SwtException {
		
		String screenDetails = "";
		ArrayList screenDetailsArray = null;
		
		try {
			log.debug(this.getClass().getName() + "- [getScreenDetails] - Entry");
			
			screenDetailsArray = dao.getScreenDetails(menuItemId);
			
			screenDetails = screenDetailsArray.get(0)
					+ SwtConstants.SEPARATOR_SCREEN_DETAILS
					+ screenDetailsArray.get(1)
					+ SwtConstants.SEPARATOR_SCREEN_DETAILS
					+ screenDetailsArray.get(2);
			
			log.debug(this.getClass().getName() + "- [getScreenDetails] - Exit");
			return screenDetails;
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [getScreenDetails] method : - "
					+ exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp,
					"getScreenDetails", SchedulerManagerImpl.class);
		}
	}

	public String getScheduledReportTypeConfig(String selectedJobId) throws SwtException {
		String schedReportTypeParams = "[";
		ArrayList schedReportTypeParamsArray = null;
		
		try {
			log.debug(this.getClass().getName() + "- [getScheduledReportTypeConfig] - Entry");
			schedReportTypeParamsArray = dao.getScheduledReportTypeConfig(selectedJobId);
			
			for (int i = 0; i < schedReportTypeParamsArray.size(); i++) {
				ScheduledReportType scheduledReportType = (ScheduledReportType)  schedReportTypeParamsArray.get(i);
				if (i == 0) {
					schedReportTypeParams += "{\"reportTypeId\":\"" + scheduledReportType.getReportTypeId() + "\""
							+ ",\"mapDateMethod\":\"" + scheduledReportType.getMapDateMethod() + "\""
							+ ",\"outputFormat\":\"" + scheduledReportType.getOutputFormat() + "\""
							+ ",\"menuItemId\":\"" + scheduledReportType.getMenuItemId() + "\"}";
				} else {
					schedReportTypeParams += ",{\"reportTypeId\":\"" + scheduledReportType.getReportTypeId() + "\""
							+ ",\"mapDateMethod\":\"" + scheduledReportType.getMapDateMethod() + "\""
							+ ",\"outputFormat\":\"" + scheduledReportType.getOutputFormat() + "\""
							+ ",\"menuItemId\":\"" + scheduledReportType.getMenuItemId() + "\"}";
				}
			}
			schedReportTypeParams += "]";
			log.debug(this.getClass().getName() + "- [getScheduledReportTypeConfig] - Exit");
			return schedReportTypeParams;
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [getScheduledReportTypeConfig] method : - "
					+ exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp,
					"getScheduledReportTypeConfig", SchedulerManagerImpl.class);
		}
	}
}