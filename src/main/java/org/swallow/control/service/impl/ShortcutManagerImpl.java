/*
 * @(#) ShortcutManagerImpl .java 21/12/05
 * 
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 * 
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
/*
 * This is manager class for shortcut screen
 * 
 */
package org.swallow.control.service.impl;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.Enumeration;
import java.util.Hashtable;
import java.util.Iterator;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.swallow.control.dao.ShortcutDAO;
import org.swallow.control.model.Shortcut;
import org.swallow.control.service.ShortcutManager;
import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.model.MenuItem;
import org.swallow.model.User;
import org.swallow.service.LogonManager;
import org.swallow.util.LabelValueBean;
import org.swallow.util.SwtUtil;
import org.swallow.util.SystemFormats;
import org.swallow.util.SystemInfo;

@Component ("shortcutManager")
public class ShortcutManagerImpl implements ShortcutManager {

	private final Log log = LogFactory.getLog(ShortcutManagerImpl.class);

	@Autowired
	private ShortcutDAO shortcutDAO;

	/**
	 * @param shortcutDAO
	 * @throws SwtException
	 */
	public void setShortcutDAO(ShortcutDAO shortcutDAO) {
		this.shortcutDAO = shortcutDAO;
	}

	/**
	 * @param hostId
	 * @param userId
	 * @return @throws
	 *         SwtException
	 */
	public Collection getShortcutDetailList(String hostId, String userId)
			throws SwtException {
		log.debug("entering getShortcutDetailList  method");
		ArrayList shrtcutDetails = new ArrayList();
		try {
			Shortcut shrtcut = new Shortcut();

			Collection collShrtDetaisl = shortcutDAO.getShortcutDetailList(hostId,
					userId);
			Iterator itr = collShrtDetaisl.iterator();

			while (itr.hasNext()) {
				shrtcut = (Shortcut) (itr.next());
				shrtcutDetails.add(shrtcut);
			}
		} catch (Exception e) {
			log
					.debug("Exception occurred in ShortcutManagerImpl.getShortcutDetailList()");
			e.printStackTrace();
			throw SwtErrorHandler.getInstance().handleException(e,
					"getShortcutDetailList", ShortcutManagerImpl.class);
		}
		return shrtcutDetails;
	}

	Hashtable ht = new Hashtable();

	ArrayList arr = new ArrayList();

	/**
	 * @return @throws
	 *         SwtException
	 */
	public Collection getMenuList(User user) throws SwtException {
		log.debug("entering getMenuList  method");
		ArrayList menuDetails = new ArrayList();
		try {
			MenuItem menuitem = new MenuItem();
			/* Code added by Girish Bal */
			// Start: Code modified and commented by Karthik Ramasamy S on
			// 28-05-2010 for the issue with Add Shortcut Screen to avoid saving
			// the values without properly filling the Mandatory fields.
			// LogonDAO logonDAO = (LogonDAO) (SwtUtil.getBean("logonDAO"));
			ArrayList menuDetailstree = (ArrayList) shortcutDAO.getMenuList();
			// End: Code modified and commented by Karthik Ramasamy S on 28-05-2010 for the issue with Add Shortcut Screen to avoid saving the values without properly filling the Mandatory fields.
			int size = menuDetailstree.size();
			for (int i = 0; i < size; i++) {
				getMenuItemTree((MenuItem) menuDetailstree.get(i));
				Enumeration e = ht.keys();
				while (e.hasMoreElements()) {
					Object obj = e.nextElement();
					StringBuffer stf = (StringBuffer) ht.get(obj);
					menuDetails.add(new LabelValueBean(stf.toString(),
							(String) obj));
				}
				ht = new Hashtable();
				arr = new ArrayList();
			}
			Collections.sort(menuDetails);
		} catch (Exception e) {
			log
					.debug("Exception occurred in ShortcutManagerImpl.getMenuList()");
			e.printStackTrace();
			throw SwtErrorHandler.getInstance().handleException(e,
					"getMenuList", ShortcutManagerImpl.class);
		}
		return menuDetails;

	}

	/**
	 * 
	 * @param menuItem
	 */
	private void getMenuItemTree(MenuItem menuItem) throws SwtException {
		//log.debug("The input to the getMenuItemTree() is ==>" + menuItem);
		try {
			if (menuItem != null) {
				ArrayList tempArr = new ArrayList();
				if (menuItem.getProgramId() != null
						&& !(menuItem.getProgramId().equals(""))) {
					StringBuffer itemNameWithParentHierarchy = new StringBuffer(
							"");
					if (arr != null) {
						Iterator itrArr = arr.iterator();

						while (itrArr.hasNext()) {
							MenuItem menuItemTemp = (MenuItem) (itrArr.next());
							tempArr.add(menuItemTemp);
							if (menuItemTemp.getItemId() != null
									&& menuItem.getParentId() != null
									&& menuItemTemp.getItemId().equals(
											menuItem.getParentId()))
								break;
						}
					}
					//building hashtable to form the menuItem tree
					ht.put(menuItem.getItemId(), itemNameWithParentHierarchy);
					arr = tempArr;
					//appending the tree with all the sublevels
					if (tempArr != null) {
						Iterator itr = tempArr.iterator();
						while (itr.hasNext()) {
							MenuItem mi = (MenuItem) (itr.next());
							itemNameWithParentHierarchy.append(
									mi.getDescription()).append("-->");
						}
					}
					//putting the menuId and the tree strcture in the hashtable
					ht.put(menuItem.getItemId(), itemNameWithParentHierarchy
							.append(menuItem.getDescription()));
				} else {
					if (arr != null && arr.size() > 0) {
						Iterator itrArr = arr.iterator();
						while (itrArr.hasNext()) {
							MenuItem menuItemTemp = (MenuItem) (itrArr.next());
							tempArr.add(menuItemTemp);
							if (menuItemTemp.getItemId() != null
									&& menuItem.getParentId() != null
									&& menuItemTemp.getItemId().equals(
											menuItem.getParentId()))
								break;
						}
						tempArr.add(menuItem);
					} else
						tempArr.add(menuItem);
					arr = tempArr;
				}
			}
			if (menuItem.getSubMenuList() != null) {
				int siz = menuItem.getSubMenuList().size();
				if (siz > 0) {
					Collection temp = menuItem.getSubMenuList();
					Iterator itr = temp.iterator();
					while (itr.hasNext()) {
						MenuItem nextItem = (MenuItem) (itr.next());
						getMenuItemTree(nextItem);
					}
				}
			}
		} catch (Exception e) {
			log
					.debug("Exception occurred in ShortcutManagerImpl.getMenuItemTree()");
			e.printStackTrace();
			throw SwtErrorHandler.getInstance().handleException(e,
					"getMenuItemTree", ShortcutManagerImpl.class);
		}
	}

	/**
	 * @param menuDesc
	 * @return @throws
	 *         SwtException
	 */
	public String getMenuId(String menuDesc) throws SwtException {
		log.debug("entering getMenuId  method");
		ArrayList menuDetails = new ArrayList();
		String menuId = null;
		try {
			MenuItem menuitem = new MenuItem();
			Collection collMenuDetails = shortcutDAO.getMenuList();
			Iterator itr = collMenuDetails.iterator();

			while (itr.hasNext()) {
				menuitem = (MenuItem) (itr.next());

				if (menuitem.getDescription().equals(menuDesc)) {
					menuId = menuitem.getItemId();
					return menuId;
				}
			}
		} catch (Exception e) {
			log.debug("Exception occurred in ShortcutManagerImpl.getMenuId()");
			e.printStackTrace();
			throw SwtErrorHandler.getInstance().handleException(e, "getMenuId",
					ShortcutManagerImpl.class);
		}
		return menuId;

	}

	/**
	 * @param menuId
	 * @return @throws
	 *         SwtException
	 */
	public String getMenuName(String menuId) throws SwtException {
		log.debug("entering getMenuName  method");
		ArrayList menuDetails = new ArrayList();
		String menuName = null;
		try {
			MenuItem menuitem = new MenuItem();
			Collection collMenuDetails = shortcutDAO.getMenuList();
			Iterator itr = collMenuDetails.iterator();
			while (itr.hasNext()) {
				menuitem = (MenuItem) (itr.next());
				if (menuitem.getItemId().equals(menuId)) {
					menuName = menuitem.getDescription();
					return menuName;
				}
			}
		} catch (Exception e) {
			log
					.debug("Exception occurred in ShortcutManagerImpl.getMenuName()");
			e.printStackTrace();
			throw SwtErrorHandler.getInstance().handleException(e,
					"getMenuName", ShortcutManagerImpl.class);
		}
		return menuName;

	}

	/**
	 * This is used to save the shortcut details by calling DAO class
	 * 
	 * @param shrtcut
	 * @return none
	 * @throws SwtException
	 */
	public void saveShortcutDetails(Shortcut shrtcut, SystemFormats sysforma,
			SystemInfo systeminfo) throws SwtException {
		try {
			log.debug(this.getClass().getName() + "- [saveShortcutDetails] - Entering ");
			shortcutDAO.saveShortcutDetails(shrtcut);
			log.debug(this.getClass().getName() + "- [saveShortcutDetails] - Exiting ");
		} catch (Exception e) {
			/*
			 * Start : Modified for Mantis 1366-Remove entry to log if duplicate
			 * records added by betcy on 18-02-2011
			 */
			log.error(this.getClass().getName()
							+ " - Exception Catched in [saveShortcutDetails] method : - "
							+ e.getMessage());
			/*
			 * End : Modified for Mantis 1366-Remove entry to log if duplicate
			 * records added by betcy on 18-02-2011
			 */
			throw SwtErrorHandler.getInstance().handleException(e,
					"saveShortcutDetails", ShortcutManagerImpl.class);
		}
	}


	/**
	 * @param shrtcut
	 * @throws SwtException
	 */
	public void updateShortcutDetails(Shortcut shrtcut, SystemFormats sysforma,
			SystemInfo systeminfo) throws SwtException {
		try {
			log.debug("entering 'updateShortcutDetails' method");
			shortcutDAO.updateShortcutDetails(shrtcut);
		} catch (Exception e) {
			log
					.debug("Exception occurred in ShortcutManagerImpl.updateShortcutDetails()");
			e.printStackTrace();
			throw SwtErrorHandler.getInstance().handleException(e,
					"updateShortcutDetails", ShortcutManagerImpl.class);
		}
	}

	/**
	 * @param shrtcut
	 * @throws SwtException
	 */

	public void deleteShortcutDetail(Shortcut shrtcut, SystemFormats sysforma,
			SystemInfo systeminfo) throws SwtException {
		try {
			log.debug("entering 'deleteShortcutDetail' method");
			shortcutDAO.deleteShortcutDetail(shrtcut);
		} catch (Exception e) {
			log
					.debug("Exception occurred in ShortcutManagerImpl.deleteShortcutDetail()");
			e.printStackTrace();
			throw SwtErrorHandler.getInstance().handleException(e,
					"deleteShortcutDetail", ShortcutManagerImpl.class);
		}
	}

	public Shortcut editShortcutDetails(String userId, String shortcutId,
			SystemInfo systeminfo, String hostId, SystemFormats sysforma)
			throws SwtException {
		try {
			return shortcutDAO.editShortcutDetails(userId, shortcutId, hostId);
		} catch (Exception e) {
			log
					.debug("Exception occurred in ShortcutManagerImpl.editShortcutDetails()");
			e.printStackTrace();
			throw SwtErrorHandler.getInstance().handleException(e,
					"editShortcutDetails", ShortcutManagerImpl.class);
		}
	}

	/**
	 * @param User
	 * @throws SwtException
	 */
	/*
	 * Start Main page shortcuts refresh on shortcuts add change and delete
	 * --(Disscused with Mantosh) <sumit>, date :04/05/07
	 */
	public Collection getShortcutListfromLogon(User user) throws SwtException {
		Collection List = new ArrayList();
		try {
			LogonManager logon = (LogonManager) (SwtUtil
					.getBean("logonManager"));
			List = logon.getShortcutList(user);
		} catch (SwtException e) {
			e.printStackTrace();
		} catch (Exception e) {
			log
					.debug("Exception occurred in ShortcutManagerImpl.getShortcutListfromLogon()");
			e.printStackTrace();
			throw SwtErrorHandler.getInstance().handleException(e,
					"getShortcutListfromLogon", ShortcutManagerImpl.class);
		}
		return List;

	}
	/*
	 * END Main page shortcuts refresh on shortcuts add change and delete
	 * --(Disscused with Mantosh) <sumit>, date :04/05/07
	 */
}