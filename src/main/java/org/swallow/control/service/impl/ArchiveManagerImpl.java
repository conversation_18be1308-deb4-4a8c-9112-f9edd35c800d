/*
 * @(#)ArchiveManagerImpl.java 1.0 21/12/05
 * 
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 * 
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */

package org.swallow.control.service.impl;

import java.util.Collection;
import java.util.List;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.swallow.control.dao.ArchiveDAO;
import org.swallow.control.model.Archive;
import org.swallow.control.service.ArchiveManager;
import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.util.SwtUtil;


/** 
 * This is manager class for Archive Maintenance screen used to get and
 * Add/Change/Delete the Archive Id details and test database link.
 * 
 */
@Component("archiveManager")
public class ArchiveManagerImpl implements ArchiveManager {
	private final Log log = LogFactory.getLog(ArchiveManagerImpl.class);
	@Autowired
	private ArchiveDAO archiveDAO;

	/**
	 * @param archiveDAO
	 *            The archiveDAO to set.
	 */
	public void setArchiveDAO(ArchiveDAO archiveDAO) {
		this.archiveDAO = archiveDAO;
	}

	/**
	 * @param hostId
	 * @throws SwtException
	 */
	public Collection getArchiveList(String hostId) throws SwtException {
		try {
			log
					.debug("Entering Manager Implementation of  'getArchiveList' method");

			Collection collArchive = archiveDAO.getArchiveList(hostId);
			log.debug("Size of collArchive--->" + collArchive.size());
			log.debug("Exiting 'getArchivelist' Method");

			return collArchive;
		} catch (Exception e) {
			log.error(this.getClass().getName()
					+ "- [getArchiveList] - Exiting " + e.getMessage());
			SwtErrorHandler swtErrorHandler = SwtErrorHandler.getInstance();
			SwtException swtexp = swtErrorHandler.handleException(e,
					"saveArchive", ArchiveManagerImpl.class);

			throw swtexp;
		}
	}

	/**
	 * @param hostId
	 * @throws SwtException
	 */
	public Collection getcurrentDbList(String hostId, String moduleId) throws SwtException {
		try {
			log
					.debug("Entering Manager Implementation of  'getcurrentDbList' method");

			Collection collDb = archiveDAO.getcurrentDbList(hostId, moduleId);

			log.debug("Exiting 'getArchivelist' Method");

			return collDb;
		} catch (Exception e) {
			log.error(this.getClass().getName()
					+ "- [getcurrentDbList] - Exception " + e.getMessage());
			SwtErrorHandler swtErrorHandler = SwtErrorHandler.getInstance();
			SwtException swtexp = swtErrorHandler.handleException(e,
					"saveArchive", ArchiveManagerImpl.class);

			throw swtexp;
		}
	}
	
	public String getActiveDBLink(String hostId, String moduleId) throws SwtException {
		try {
			log
			.debug("Entering Manager Implementation of  'getActiveDBLink' method");
			
			String dbLink = archiveDAO.getActiveDBLink(hostId, moduleId);
			
			log.debug("Exiting 'getArchivelist' Method");
			
			return dbLink;
		} catch (Exception e) {
			log.error(this.getClass().getName()
					+ "- [getActiveDBLink] - Exception " + e.getMessage());
			SwtErrorHandler swtErrorHandler = SwtErrorHandler.getInstance();
			SwtException swtexp = swtErrorHandler.handleException(e,
					"getActiveDBLink", ArchiveManagerImpl.class);
			
			throw swtexp;
		}
	}

	/**
	 * @param archive
	 * @throws SwtException
	 */
	public void saveArchive(Archive archive) throws SwtException {
		try {
			log.debug("entering saveArchive of ArchiveManagerImpl");
			archiveDAO.saveArchive(archive);
			log.debug("exiting saveArchive of ArchiveManagerImpl");
		} catch (Exception e) {

			log.error(this.getClass().getName()
					+ "- [saveArchive] - Exception " + e.getMessage());
			SwtErrorHandler swtErrorHandler = SwtErrorHandler.getInstance();
			SwtException swtexp = swtErrorHandler.handleException(e,
					"saveArchive", ArchiveManagerImpl.class);

			throw swtexp;
		}
	}

	/**
	 * @param archive
	 * @throws SwtException
	 */
	public void deleteArchive(Archive archive) throws SwtException {
		try {
			log.debug("inside deleteArchive--->" + archive.toString());
			archiveDAO.deleteArchive(archive);
			log.debug("exiting deleteArchive-->");
		} catch (SwtException e) {
			log.error(this.getClass().getName()
					+ "- [deleteArchive] - Exception " + e.getMessage());
			SwtErrorHandler swtErrorHandler = SwtErrorHandler.getInstance();
			SwtException swtexp = swtErrorHandler.handleException(e,
					"deleteArchive", ArchiveManagerImpl.class);

			throw swtexp;
		}
	}

	/**
	 * @param archive
	 * @throws SwtException
	 */
	public void updateArchive(Archive archive) throws SwtException {
		try {
			log.debug("entering updateArchive of ArchiveManagerImpl");
			log.debug("The archive record before update iss== >>" + archive);
			archiveDAO.updateArchive(archive);
		} catch (SwtException e) {
			log.error(this.getClass().getName()
					+ "- [updateArchive] - Exception " + e.getMessage());
			SwtErrorHandler swtErrorHandler = SwtErrorHandler.getInstance();
			SwtException swtexp = swtErrorHandler.handleException(e,
					"deleteArchive", ArchiveManagerImpl.class);

			throw swtexp;
		}
	}

	/**
	 * @param archiveID
	 * @throws SwtException
	 */
	public String getDBlink(String ArchiveID) throws SwtException {

		try {
			String DBlink = archiveDAO.getDBlink(ArchiveID);
			return DBlink;
		} catch (SwtException e) {
			log.error(this.getClass().getName() + "- [getDBLink] - Exception "
					+ e.getMessage());
			SwtErrorHandler swtErrorHandler = SwtErrorHandler.getInstance();
			SwtException swtexp = swtErrorHandler.handleException(e,
					"deleteArchive", ArchiveManagerImpl.class);

			throw swtexp;
		}

	}

	/*
	 * Start:Code Modified for Mantis 1270 by Chinniah on 29-Feb-2012:Data
	 * Archive setup: Remove redundant fields from Archive setup screen
	 */
	/**
	 * This method is used to test the connection of the DB Link to which user
	 * connecting
	 * 
	 * @param dbLink
	 * @return connFlag
	 * @throws SwtException
	 */
	public boolean testConnection(String dbLink, String moduleId, String archiveType) throws SwtException {
		// declaration to test connection flag
		try {
			log.debug(this.getClass().getName()
					+ "- [testConnection] - Entering ");
			// get the database link connection status from the DAO class
			if ("PCM".equals(moduleId)) {
				ArchiveDAO archiveDAOPCM = (ArchiveDAO) SwtUtil.getBean("archiveDAOPCM");
				return archiveDAOPCM.testConnection(dbLink, archiveType);
			}
			return archiveDAO.testConnection(dbLink, archiveType);
		} catch (SwtException e) {
			log.error(this.getClass().getName()
					+ "- [testConnection] - Exiting " + e.getMessage());
			throw e;
		} catch (Exception ex) {
			log.error(this.getClass().getName()
					+ "- [testConnection] - Exiting " + ex.getMessage());
			throw SwtErrorHandler.getInstance().handleException(ex,
					"testConnection", ArchiveManagerImpl.class);
		} finally {
			log.debug(this.getClass().getName() + "- [testConnection] - Exit ");
		}

	}
	/*
	 * End:Code Modified for Mantis 1270 by Chinniah on 29-Feb-2012:Data Archive
	 * setup: Remove redundant fields from Archive setup screen
	 */

	public Archive getCurrentArchiveDb(String hostId) throws SwtException {
		try {
			Archive archive = archiveDAO.getCurrentArchiveDb(hostId);
			return archive;
		} catch (SwtException e) {
			log.error(this.getClass().getName() + "- [getCurrentArchvieDb] - Exception "
					  + e.getMessage());
			SwtErrorHandler swtErrorHandler = SwtErrorHandler.getInstance();
			SwtException swtexp = swtErrorHandler.handleException(e,
					"getCurrentArchvieDb", ArchiveManagerImpl.class);

			throw swtexp;
		}
	}

	public Archive getCurrentArchiveDb(String hostId, String archiveId) throws SwtException {
		try {
			log.debug("Entering Manager Implementation of  'getCurrentArchiveDb with archiveId' method");

			Archive archive = archiveDAO.getCurrentArchiveDb(hostId, archiveId);

			log.debug("Exiting 'getCurrentArchiveDb with archiveId' Method");

			return archive;
		} catch (Exception e) {
			log.error(this.getClass().getName()
					+ "- [getCurrentArchiveDb with archiveId] - Exception " + e.getMessage());
			SwtErrorHandler swtErrorHandler = SwtErrorHandler.getInstance();
			SwtException swtexp = swtErrorHandler.handleException(e,
					"getCurrentArchiveDb", ArchiveManagerImpl.class);

			throw swtexp;
		}
	}

	public Archive getArchiveById(String hostId, String archiveId) throws SwtException {
		try {
			log.debug("Entering Manager Implementation of  'getArchiveById' method");

			Archive archive = archiveDAO.getArchiveById(hostId, archiveId);

			log.debug("Exiting 'getArchiveById' Method");

			return archive;
		} catch (Exception e) {
			log.error(this.getClass().getName()
					+ "- [getArchiveById] - Exception " + e.getMessage());
			SwtErrorHandler swtErrorHandler = SwtErrorHandler.getInstance();
			SwtException swtexp = swtErrorHandler.handleException(e,
					"getArchiveById", ArchiveManagerImpl.class);

			throw swtexp;
		}
	}
}
