/*
 * @(#)SystemLogManagerImpl.java  16/12/05
 * 
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 * 
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.control.service.impl;

import java.util.Date;
import java.util.List;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.swallow.control.dao.SystemLogDAO;
import org.swallow.control.model.SystemLog;
import org.swallow.control.service.SystemLogManager;
import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.util.SystemFormats;

@Component("systemLogManager")
@Transactional
public class SystemLogManagerImpl implements SystemLogManager {
	private final Log log = LogFactory.getLog(SystemLogManagerImpl.class);
	@Autowired
	private SystemLogDAO dao;

	public void setSystemLogDAO(SystemLogDAO dao) {
		this.dao = dao;
	}

	/**
	 * 
	 * @param hostId
	 * @param fromDate
	 * @param toDate
	 * @param currentPage
	 * @param sysLogList
	 * @param filterSortStatus
	 * @return @throws
	 *         SwtException
	 */
	public int getSystemLogList(String hostId, Date fromDate, Date toDate,
			int currentPage, int maxPage, List sysLogList,
			String filterSortStatus, SystemFormats formats) throws SwtException {
		log.debug("Entering 'getSystemLogList' method");
		int sysLogListsize = 0;
		try {
			sysLogListsize = dao
					.getSystemLogList(hostId, fromDate, toDate, currentPage,
							maxPage, sysLogList, filterSortStatus, formats);
		} catch (Exception e) {
			log
					.debug("Exception occurred in SystemLogManagerImpl.getSystemLogList() method");
			e.printStackTrace();
			throw SwtErrorHandler.getInstance().handleException(e,
					"getSystemLogList", SystemLogManagerImpl.class);

		}
		return sysLogListsize;
	}
	
	/**
	 * This function is used to save a system log in the database
	 * @param systemLog
	 * @throws SwtException
	 */
	
	public void saveSystemLog(SystemLog systemLog) throws SwtException{
		log.debug("Entering 'getSystemLogList' method");
		try {
			dao.saveSystemLog(systemLog);
		} catch (Exception e) {
			log
					.debug("Exception occurred in SystemLogManagerImpl.getSystemLogList() method");
			e.printStackTrace();
			throw SwtErrorHandler.getInstance().handleException(e,
					"getSystemLogList", SystemLogManagerImpl.class);

		}
	}

}