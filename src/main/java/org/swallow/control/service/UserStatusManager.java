/*
 * @(#)UserStatusManager.java 26/12/05
 * 
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 * 
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */

package org.swallow.control.service;
import java.util.Collection;

import org.swallow.control.dao.UserStatusDAO;
import org.swallow.control.model.UserStatus;
import org.swallow.exception.SwtException;


public interface UserStatusManager {
	/**
	 * @param userStatusDAO
	 */
	public void setUserStatusDAO(UserStatusDAO userStatusDAO);
	/**
	 * @param hostId
	 * @return
	 * @throws SwtException
	 */
	public Collection  getUserStatusList(String hostId) throws SwtException;
	/**
	 * @param userId
	 * @return
	 * @throws SwtException
	 */
	public Collection  getUserList(String userId) throws SwtException;
	
	
	/**
	 *  Clean S_USER_STATUS table
	 * @return 
	 * @throws SwtException
	 */
	public boolean  truncateUserStatus();
	
	/**
	 * 
	 * @param hostId
	 * @param userId
	 * @return
	 * @throws SwtException
	 */
	public UserStatus getUserStatusRecord(String hostId, String userId)  throws SwtException;
}
