/*
 * @(#)UserProfilesManager.java 15/12/05
 * 
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 * 
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.control.service;
import java.util.Collection;
import java.util.List;
import org.swallow.model.UserProfile;
import org.swallow.model.UserProfileDetail;
import org.swallow.exception.SwtException;


public interface UserProfilesManager {
	/**
	 * @param hostId
	 * @param userId
	 * @param profileId
	 * @return
	 */
	public List fetchDetails(String hostId,String userId,String profileId);
	
	/**
	 * @param userprofiles
	 * @param userProfileDetailsColl
	 */
	public void saveuserProfileDetails(UserProfile userprofiles,Collection userProfileDetailsColl);
	/**
	 * @param userprofiles
	 */
	public void updateuserProfileDetails(UserProfile userprofiles);
	/**
	 * @param userprofiles
	 * @param userprofilesdtl
	 */
	public void deleteuserProfileDetails(UserProfile userprofiles,UserProfileDetail userprofilesdtl);
	/**
	 * @param hostId
	 * @param userId
	 * @param profileId
	 * @return
	 */
	public List fetchMenuDetails(String hostId,String userId,String profileId);
	
	public void updateuserProfileDetails(UserProfile userprofiles,Collection userProfileDetailsColl);

	/* Start: Code changed/added by Arumugam on 15-Jul-2010 for Mantis 1177 : Should Open number of screens with full/view access on the role and save the profile 28-JUN-2010 */
	/**
	 * @param hostId
	 * @param userId
	 * @param profileId
	 * @param roleId
	 * @return
	 */
	public List getProfileDetails(String hostId, String userId, String profileId,String roleId)throws SwtException;
	/* End: Code changed/added by Arumugam on 15-Jul-2010 for Mantis 1177 : Should Open number of screens with full/view access on the role and save the profile 28-JUN-2010 */
}
