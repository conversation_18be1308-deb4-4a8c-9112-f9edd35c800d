/*
 * @(#)EntityProcessManager.java 1.0 31/05/2011
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.control.service;

import java.util.Collection;

import org.swallow.control.model.EntityProcessStatus;
import org.swallow.control.model.Process;
import org.swallow.exception.SwtException;

/**
 * <AUTHOR>
 * 
 * This interface, contains methods to be used in EntityProcessManager class
 */
public interface EntityProcessManager {

	/**
	 * This is used to get process name list from DAO
	 * 
	 * @return Collection
	 * @throws SwtException
	 */

	public Collection<Process> getProcessNameDetails() throws SwtException;

	/**
	 * This is used to get entity process status details from DAO.
	 * 
	 * @param processname
	 * @return collection
	 * @throws SwtException
	 */

	public Collection<EntityProcessStatus> getEntityProcessStatusDetails(
			String processName) throws SwtException;

	/**
	 * This is used to save the details for entity process status
	 * 
	 * @param entityprocessstatus
	 * @param process
	 * @return none
	 * @throws SwtException
	 */

	public void save(Collection<EntityProcessStatus> entProStatusColl,
			Process process) throws SwtException;

	/**
	 * This is used to save the details for entity process status.
	 * 
	 * @param entityprocessstatus
	 * @return none
	 * @throws SwtException
	 */

	public void savestatus(EntityProcessStatus entProStatus)
			throws SwtException;

}
