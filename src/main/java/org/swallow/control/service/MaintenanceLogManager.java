/*
 * @(#)MaintenanceLogManager.java 1.0 23/08/06
 * 
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 * 
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.control.service;

import java.util.Collection;
import java.util.Date;
import java.util.List;

import org.swallow.control.dao.MaintenanceLogDAO;
import org.swallow.exception.SwtException;
import org.swallow.util.SystemFormats;

public interface MaintenanceLogManager {
	/**
	 * This method sets MaintenanceLogDAO instance
	 * @param maintenanceLogDAO
	 */
	public void setMaintenanceLogDAO(MaintenanceLogDAO maintenanceLogDAO);

	/**
	 * This method does retrieve log details which are logged by particular system 
	 * @param hostId
	 * @param logDate
	 * @param userId
	 * @param ipAddress
	 * @param tableName
	 * @param reference
	 * @param action
	 * @return Collection
	 * @throws SwtException
	 */

	public Collection getSystemLogDetails(String hostId, String logDate,
			String userId, String ipAddress, String tableName,
			String reference, String action) throws SwtException;

	/**
	 * This method is used to fetch maintenance log details under different
	 * criteria
	 * 
	 * @param hostId
	 * @param fromDate
	 * @param toDate
	 * @param currentPage
	 * @param sysLogList
	 * @param filterSortStatus
	 * @return int
	 * @throws SwtException
	 */
	public int getMaintenanceLogList(String hostId, Date fromDate, Date toDate,
			int currentPage, int maxPage, List maintenanceList,
			String filterSortStatus, SystemFormats formats) throws SwtException;
	
	/**
	 * This method is used to get  user name from s_users table
	 */
	public String getUserName(String userId) throws SwtException;
}
