/*
 * @(#)ErrorLogManager.java 21/12/05
 * 
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 * 
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */

package org.swallow.control.service;
import org.swallow.control.dao.*;
import org.swallow.control.model.*;
import org.swallow.exception.*;
import org.swallow.util.SystemFormats;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;

public interface ErrorLogManager {
	 /**
	 * @param errorLogDAO
	 */
	public void setErrorLogDAO(ErrorLogDAO errorLogDAO);
	 /**
	 * @param fromDate
	 * @param toDate
	 * @return
	 * @throws SwtException
	 */
	public Collection getErrorLogList(String hostId, Date fromDate, Date toDate) throws SwtException;
	/**
     * @param fromDate
     * @param toDate
     * @return Collection
     */
    public Collection getErrorLogList(String hostId,String userId , Date fromDate, Date toDate) throws SwtException;
	 /**
	 * @param errorLog
	 */
	public void logError(ErrorLog errorLog);

	 /*-Start- Code modified for Defect No. 116 in Mantiss on 03/11/2007 - */
	public int getErrorLogListUsingStoredProc(Date fromDate, Date toDate, int currentPage, int maxPage, ArrayList errorLogList, String filterSortStatus, SystemFormats sysformat) throws SwtException;
	 /*-End- Code modified for Defect No. 116 in Mantiss on 03/11/2007 - */
}
