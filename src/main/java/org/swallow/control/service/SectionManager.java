/*
 * Created on Nov 4, 2005
 *
 * TODO To change the template for this generated file go to
 * Window - Preferences - Java - Code Style - Code Templates
 */
package org.swallow.control.service;

import org.swallow.control.dao.*;
import org.swallow.control.model.*;
import org.swallow.exception.*;
import org.swallow.util.SystemInfo;

import java.util.Collection;


public interface SectionManager {
	/**
	 * @param sectionDAO
	 */
	public void setSectionDAO(SectionDAO sectionDAO);
	/**
	 * @param hostId
	 * @return
	 * @throws SwtException
	 */
	public Collection  getSectionList(String hostId) throws SwtException;
	/**
	 * @param section
	 * @throws SwtException
	 */
	public void saveSectionDetail(Section section,SystemInfo systemInfo)  throws SwtException;
	/**
	 * @param section
	 * @throws SwtException
	 */
	public void updateSectionDetail(Section section,SystemInfo systemInfo)  throws SwtException;
	/**
	 * @param section
	 * @throws SwtException
	 */
	public void deleteSectionDetail(Section section,SystemInfo systemInfo)  throws SwtException;
	/**
	 * @param hostId
	 * @return
	 * @throws SwtException
	 */
	public Collection getSectionListAsLebelBean(String hostId) throws SwtException;

	
	
	
}