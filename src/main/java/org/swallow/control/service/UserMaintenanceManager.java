/*
 * @(#)UserMaintenanceManager.java 15/12/05
 * 
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 * 
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */

package org.swallow.control.service;
import java.util.Collection;
import java.util.Date;
import javax.servlet.http.HttpServletRequest;
import net.sf.jasperreports.engine.JasperPrint;
import org.swallow.control.model.PasswordHistory;
import org.swallow.control.model.UserMaintenance;
import org.swallow.exception.SwtException;
import org.swallow.model.User;
import org.swallow.util.SystemFormats;
import org.swallow.util.SystemInfo;


public interface UserMaintenanceManager {
	/*
	 * Mantis 660 : User maintenance screen does not populate currency 
	 * group when adding new user
	 * Start : Modified for fetching the user details of the entities 
	 * for which the current user has access rights
	 * Modified by <PERSON><PERSON>ji on 19-07-2008
	 */
	/**
	 * This method fetches the users details of the entities for 
	 * which the current user has access rights  
	 * @param hostId
	 * @param roleId 
	 * @return Collection
	 * @throws SwtException
	 */
	public Collection getUserList(String hostId,String roleId) throws SwtException;
	/* End : Mantis 660 : User maintenance screen does not populate currency group when adding new user */
	/**
	 * @param user
	 * @param pwdhist
	 * @throws SwtException
	 */
	public void saveUserDetail(User user, SystemInfo systemInfo, SystemFormats sysforma, PasswordHistory pwdhist)  throws SwtException;
	/**
	 * @param usermaintenance
	 * @param pwdhist
	 * @throws SwtException
	 */
	public void deleteUserDetail(UserMaintenance usermaintenance, SystemInfo systemInfo, SystemFormats sysforma, PasswordHistory pwdhist)  throws SwtException;
	/**
	 * @param hostId
	 * @param userId
	 * @return
	 * @throws SwtException
	 */
	public UserMaintenance fetchUserDetail(String hostId,String userId)  throws SwtException;
	/**
	 * @param usermaintenance
	 * @param changePwdFlag
	 * @throws SwtException
	 */
	public void updateUserDetail(UserMaintenance usermaintenance,User user, PasswordHistory pwdhist,SystemInfo systemInfo,SystemFormats sysforma, String changePwdFlag)  throws SwtException;
	/* Start:Vivekanandan:15-12-2008:Added
	 * as per given in SRS_Jasper_UserReport_1.0_in_progress */
	
	/**
	 * Method to get User Report
	 * @param request
	 * @param userId
	 * @return JasperPrint
	 * @throws SwtException
	 */
	public JasperPrint getUserReport(HttpServletRequest request,String userId)throws SwtException;
	/* End:Vivekanandan:15-12-2008:Added
	 * as per given in SRS_Jasper_UserReport_1.0_in_progress */
	/**
	 * @param hostId
	 * @return
	 * @throws SwtException
	 */
	public Collection getRoleList(String hostId)  throws SwtException;
	/*
	 * Mantis 660 : User maintenance screen does not populate currency 
	 * group when adding new user
	 * Start : Modified for fetching the roles and entities 
	 * for which the current user has access rights
	 * Modified by Balaji on 19-07-2008
	 */
	/**
	 * This method fetches role and entities for which the current 
	 * user has access rights
	 * @param hostId
	 * @param entityId
	 * @return Collection
	 * @throws SwtException
	 */
	public Collection getRoleList(String hostId,String entityId)  throws SwtException;
	/* End : Mantis 660 : User maintenance screen does not populate currency group when adding new user */
	/**
	 * @param roleId
	 * @return
	 * @throws SwtException
	 */
	public Collection getRoleEntityAccess(String roleId)  throws SwtException;
	/**
	 * @param hostId
	 * @param UserId
	 * @return
	 * @throws SwtException
	 */
	public Collection getLastPasswordChangeDate(String hostId, String userId)  throws SwtException;
	/**
	 * @param hostId
	 * @param UserId
	 * @return
	 * @throws SwtException
	 */
	public Collection getLoginDateInfo(String hostId, String userId)  throws SwtException;
	
	public Date getLastLogoutDate(String hostId, String userId)  throws SwtException;	
	
	/*Code modified as per mail from Steve and forwarded by JP for Amending the User Profile Changes on 17-APR-2008 -->
	  Reference SRS : Smart-Predict_SRS_USER_PROFILE_0.2.doc by James Cook and David 
	  Description : It is required to have a user whose sole purpose to be able to reset user passwords. 
	              This requires a change to the "Change Role" window. */
	//Start : Method added to retrieve whether the user has Advanced user rights or not on 21-04-2008
	public String getAdvancedUser(String strHostId, String strUserId)throws SwtException;
	//End   : Method added to retrieve whether the user has Advanced user rights or not on 21-04-2008
}
