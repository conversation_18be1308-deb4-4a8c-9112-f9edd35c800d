/*
 * Copyright (c) 2006-2015 SwallowTech, Inc.
 * 14 Lion Yard, Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 *
 * Developed by Perot Systems.
 */

/*
 * Created on Dec 16, 2005
 */
package org.swallow.control.web;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

import org.springframework.context.ApplicationContext;
import org.swallow.control.model.MaintenanceLog;
import org.swallow.control.model.SystemLog;
import org.swallow.control.service.SystemLogManager;
import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.util.CacheManager;
import org.swallow.util.PageDetails;
import org.swallow.util.PropertiesFileLoader;
import org.swallow.util.SwtConstants;
import org.swallow.util.SwtUtil;
import org.swallow.util.SystemFormats;
import org.swallow.util.struts.ActionMessages;
import org.swallow.util.struts.ActionMessage;
import org.swallow.util.struts.ActionErrors;
import org.swallow.util.LabelValueBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.apache.struts2.convention.annotation.Result;
import org.apache.struts2.convention.annotation.AllowedMethods;
import org.apache.struts2.convention.annotation.Action;
import org.apache.struts2.ServletActionContext;
import org.swallow.util.struts.CustomActionSupport;

/**
 * <AUTHOR> This class is used for System Log Screen
 * 
 * this class is used to Maintain the System Log details.
 */
@Action(value = "/systemlog", results = {
	@Result(name = "fail", location = "/error.jsp"),
	@Result(name = "success", location = "/jsp/control/systemlog.jsp"),
	@Result(name = "refresh", location = "/jsp/control/systemlog.jsp"),
})

@AllowedMethods ({"defaultDetails" ,"showDetails" ,"next" })
public class SystemLogAction extends CustomActionSupport {

HttpServletRequest request = ServletActionContext.getRequest();
HttpServletResponse response = ServletActionContext.getResponse();

private SystemLog systemLog;
public SystemLog getSystemLog() {
	if (systemLog == null) {
		systemLog = new SystemLog();
	}
	return systemLog;
}
public void setSystemLog(SystemLog systemLog) {
	this.systemLog = systemLog;
	HttpServletRequest request = ServletActionContext.getRequest();
	request.setAttribute("systemLog", systemLog);
}

public String execute() throws Exception {
    HttpServletRequest request = ServletActionContext.getRequest();

    // List of methods 
    String method = String.valueOf(request.getParameter("method"));

    switch (method) {
        case "unspecified":
            return unspecified();
        case "defaultDetails":
            return defaultDetails();
        case "showDetails":
            return showDetails();
        case "next":
            return next();
        default:
            break;
    }

    return unspecified();
}




	/** The log variable is used for store the log object. */
	private final Log log = LogFactory.getLog(SystemLogAction.class);
	@Autowired
private SystemLogManager systemLogManager = null;
	private ApplicationContext ctx = null;

	/**
	 * 
	 * @param systemLogManager
	 */
	public void setSystemLogManager(SystemLogManager systemLogManager) {
		this.systemLogManager = systemLogManager;
	}

	/**
	 * @desc This method is called when the System Log screen is loaded for the
	 *       first time
	 */
	public String unspecified()
			throws Exception {
		request.setAttribute("systemLogList", new ArrayList());
		log.debug("exiting 'unspecified' method");
		return defaultDetails();
	}

	/**
	 * @param request
	 * @return
	 * @throws SwtException
	 */
	private String putHostIdListInReq(HttpServletRequest request)
			throws SwtException {

		CacheManager cacheManagerInst = CacheManager.getInstance();
		String hostId = cacheManagerInst.getHostId();
		return hostId;

	}

	/**
	 * @param request
	 * @return
	 * @throws Exception
	 */
	private String putSystemDateInRequest(HttpServletRequest request)
			throws Exception {
		String systemDate = SwtUtil.getSystemDateString();
		return systemDate;
	}

	/**
	 * @desc This method fetches the System Log details for the Predict System
	 *       Date
	 * 
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return
	 * @throws Exception
	 */
	public String defaultDetails()
			throws Exception {
		/* Variable Declaration for systemDate */
		String systemDate = null;
		/* Variable Declaration for filterSortStatus */
		String filterSortStatus = null;
		/* Variable Declaration for currentFilter */
		String currentFilter = null;
		/* Variable Declaration for currentSort */
		String currentSort = null;
		/* Variable Declaration for sysformat */
		SystemFormats sysformat = null;
		/* Variable Declaration for pageSize */
		int pageSize = 0;
		/* Variable Declaration for currentPage */
		int currentPage = 0;
		/* Variable Declaration for initialPageCount */
		int initialPageCount = 0;
		/* Variable Declaration for isNext */
		boolean isNext = false;
		try {
			log.debug("entering defaultDetails  method");

			systemDate = putSystemDateInRequest(request);

// To remove: 			DynaValidatorForm dyForm = (DynaValidatorForm) form;
			SystemLog systemLog = (SystemLog) (getSystemLog());
			systemLog.setFromDateAsString(systemDate);
			systemLog.setToDateAsString(systemDate);

			currentFilter = request.getParameter("selectedFilter");
			currentSort = request.getParameter("selectedSort");
			if (currentFilter == null) {
				currentFilter = "all";
			}
			if (currentSort == null) {
				currentSort = "none";
			}
			filterSortStatus = currentFilter + "," + currentSort;
			sysformat = SwtUtil.getCurrentSystemFormats(request.getSession());
			systemLog.setFromDate(SwtUtil.parseDate(systemLog
					.getFromDateAsString(), sysformat.getDateFormatValue()));

			systemLog.setToDate(SwtUtil.parseDate(
					systemLog.getToDateAsString(), sysformat
							.getDateFormatValue()));
			String hostId = putHostIdListInReq(request);

			currentPage = 1;
			ArrayList sysLogList = new ArrayList();

			int maxPage = 0;
			int totalCount = 0;
			totalCount = systemLogManager.getSystemLogList(hostId, systemLog
					.getFromDate(), systemLog.getToDate(), currentPage - 1,
					initialPageCount, sysLogList, filterSortStatus, sysformat);

			/*
			 * this Block reads PageSize from the property file and calculates
			 * the maxPages
			 */
			pageSize = SwtUtil.getPageSizeFromProperty(SwtConstants.SYSTEM_LOG_SCREEN_PAGE_SIZE);
			maxPage = setMaxPageAttribute(totalCount, pageSize);

			PageDetails pSummary = new PageDetails();
			/*
			 * pageSummaryList contains the Page Link (only max 10 Pages are
			 * allowed at one time) and details shown on the screen
			 */
			ArrayList<PageDetails> pageSummaryList = new ArrayList<PageDetails>();
			pSummary.setCurrentPageNo(currentPage);
			pSummary.setMaxPages(maxPage);
			pSummary.setTotalCount(totalCount);
			pageSummaryList.add(pSummary);

			// Hide pagination if no of records are "0" or records can be
			// displayed in one page.
			if (maxPage <= 1) {
				request.setAttribute("hidePagination", true);
			}
			if (maxPage > 1)
				isNext = true;

			setSystemLog(systemLog);
			Iterator itr = sysLogList.iterator();
			while (itr.hasNext()) {
				systemLog = (SystemLog) (itr.next());
				systemLog.setLogDate_Date(SwtUtil.formatDate(systemLog
						.getLogDate(), sysformat.getDateFormatValue()));
			}
			if ("none".equals(currentSort)) {
				currentSort = "0|true"; // default sorting column
			}
			request.setAttribute("currentPage", Integer.toString(currentPage));
			request.setAttribute("prevEnabled", "false");
			request.setAttribute("nextEnabled", "" + isNext);
			request.setAttribute("maxPage", Integer.toString(maxPage));
			request.setAttribute("selectedFilter", currentFilter);
			request.setAttribute("selectedSort", currentSort);
			request.setAttribute("pageSummaryList", pageSummaryList);
			request.setAttribute("systemLogList", sysLogList);
			request.setAttribute("fromDate", systemDate);
			request.setAttribute("toDate", systemDate);
			request.setAttribute("totalCount", totalCount);
			/*
			 * Code added by venkat on 28-mar-2011 for Mantis 1308:"Show 'Last
			 * refresh' time in screens that have refresh function"
			 */
			request.setAttribute("lastRefTime", SwtUtil.getLastRefTimeOnGMTOffset(request,
					SwtUtil.getUserCurrentEntity(request.getSession())));
			log.debug("exiting 'defaultDetails' method");

			return ("refresh");
		} catch (SwtException swtexp) {
			log
					.error("SwtException Catch in SystemLogAction.'defaultDetails' method : "
							+ swtexp.getMessage());
			SwtUtil.logException(swtexp, request, "");
			return ("fail");
		} catch (Exception e) {
			log
					.error("Exception Catch in SystemLogAction.'defaultDetails' method : "
							+ e.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					e, "defaultDetails", SystemLogAction.class), request, "");
			return ("fail");
		} finally {
			systemDate = null;
			filterSortStatus = null;
			currentFilter = null;
			currentSort = null;
			sysformat = null;
		}
	}

	/**
	 * @desc This method fetches the System Log details for all the dates
	 *       starting "From Date" till "To Date"
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return
	 * @throws Exception
	 */
	public String showDetails()
			throws Exception {
		/* Variable Declaration for filterSortStatus */
		String filterSortStatus = null;
		/* Variable Declaration for fromDateAsString */
		String fromDateAsString = null;
		/* Variable Declaration for toDateAsString */
		String toDateAsString = null;
		/* Variable Declaration for currentFilter */
		String currentFilter = null;
		/* Variable Declaration for currentSort */
		String currentSort = null;
		/* Variable Declaration for hostId */
		String hostId = null;
		/* Variable Declaration for sysformat */
		SystemFormats sysformat = null;
		/* Variable Declaration for isNext */
		boolean isNext = false;
		/* Variable Declaration for currentPage */
		int currentPage = 0;
		/* Variable Declaration for initialPageCount */
		int initialPageCount = 0;
		/* Variable Declaration for pageSize */
		int pageSize = 0;
		try {
			log.debug("entering 'showDetails' method");

// To remove: 			DynaValidatorForm dyForm = (DynaValidatorForm) form;
			SystemLog systemLog = (SystemLog) (getSystemLog());
			fromDateAsString = systemLog.getFromDateAsString();

			toDateAsString = systemLog.getToDateAsString();

			/* gets all the filter values specified for all columns */
			currentFilter = request.getParameter("selectedFilter");
			/* gets the sort column specified */
			currentSort = request.getParameter("selectedSort");

			if (currentFilter == null) {
				currentFilter = "all";
			}
			if (currentSort == null) {
				currentSort = "none";
			}
			filterSortStatus = currentFilter + "," + currentSort;

			sysformat = SwtUtil.getCurrentSystemFormats(request.getSession());
			systemLog.setFromDate(SwtUtil.parseDate(systemLog
					.getFromDateAsString(), sysformat.getDateFormatValue()));

			systemLog.setToDate(SwtUtil.parseDate(
					systemLog.getToDateAsString(), sysformat
							.getDateFormatValue()));
			hostId = putHostIdListInReq(request);
			currentPage = 1;
			List sysLogList = new ArrayList();

			// START: Modified code by Aliveni to set totalCount attribute in
			// request to display the
			// total number of records on screen, on 30-SEP-2010
			int maxPage = 0;
			int totalCount = 0;
			totalCount = systemLogManager.getSystemLogList(hostId, systemLog
					.getFromDate(), systemLog.getToDate(), currentPage - 1,
					initialPageCount, sysLogList, filterSortStatus, sysformat);

			/*
			 * this Block reads PageSize from the property file and calculates
			 * the maxPages
			 */
			pageSize = SwtUtil.getPageSizeFromProperty(SwtConstants.SYSTEM_LOG_SCREEN_PAGE_SIZE);
			maxPage = setMaxPageAttribute(totalCount, pageSize);
			// END: Modified code by Aliveni to set totalCount attribute in
			// request to display the
			// total number of records on screen, on 30-SEP-2010

			PageDetails pSummary = new PageDetails();
			/*
			 * pageSummaryList contains the Page Link (only max 10 Pages are
			 * allowed at one time) and details shown on the screen
			 */
			ArrayList<PageDetails> pageSummaryList = new ArrayList<PageDetails>();
			pSummary.setCurrentPageNo(currentPage);
			pSummary.setMaxPages(maxPage);
			pSummary.setTotalCount(totalCount);
			pageSummaryList.add(pSummary);

			// Hide pagination if no of records are "0" or records can be
			// displayed in one page.
			if (maxPage <= 1) {
				request.setAttribute("hidePagination", true);
			}
			if (maxPage > 1)
				isNext = true;

			setSystemLog(systemLog);
			Iterator itr = sysLogList.iterator();
			while (itr.hasNext()) {
				systemLog = (SystemLog) (itr.next());
				systemLog.setLogDate_Date(SwtUtil.formatDate(systemLog
						.getLogDate(), sysformat.getDateFormatValue()));
			}
			if ("none".equals(currentSort)) {
				currentSort = "0|true"; // default sorting column
			}
			request.setAttribute("currentPage", Integer.toString(currentPage));
			request.setAttribute("prevEnabled", "false");
			request.setAttribute("nextEnabled", "" + isNext);
			request.setAttribute("maxPage", Integer.toString(maxPage));
			request.setAttribute("selectedFilter", currentFilter);
			request.setAttribute("selectedSort", currentSort);
			request.setAttribute("pageSummaryList", pageSummaryList);
			request.setAttribute("systemLogList", sysLogList);
			request.setAttribute("fromDate", fromDateAsString);
			request.setAttribute("toDate", toDateAsString);
			request.setAttribute("totalCount", totalCount);
			/*
			 * Code added by venkat on 28-mar-2011 for Mantis 1308:"Show 'Last
			 * refresh' time in screens that have refresh function"
			 */
			request.setAttribute("lastRefTime", SwtUtil.getLastRefTimeOnGMTOffset(request,
					SwtUtil.getUserCurrentEntity(request.getSession())));
			log.debug("exiting 'showDetails' method");
			return ("refresh");
		} catch (SwtException swtexp) {
			log
					.error("SwtException Catch in SystemLogAction.'showDetails' method : "
							+ swtexp.getMessage());
			SwtUtil.logException(swtexp, request, "");
			return ("fail");
		} catch (Exception e) {
			log
					.error("Exception Catch in SystemLogAction.'showDetails' method : "
							+ e.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					e, "showDetails", SystemLogAction.class), request, "");
			return ("fail");
		} finally {
			filterSortStatus = null;
			fromDateAsString = null;
			toDateAsString = null;
			currentFilter = null;
			currentSort = null;
			hostId = null;
			sysformat = null;
		}
	} // End of showDetails method

	/**
	 * @desc This method is called when PREVIOUS LINK, NEXT LINK or any Page no
	 *       is clicked. It fetches the system Log details for that page.
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return
	 * @throws Exception
	 */
	public String next()
			throws Exception {
		/* Variable Declaration for systemLog */
		SystemLog systemLog = null;
		/* Variable Declaration for fromDateAsString */
		String fromDateAsString = null;
		/* Variable Declaration for toDateAsString */
		String toDateAsString = null;
		/* Variable Declaration for currentFilter */
		String currentFilter = null;
		/* Variable Declaration for currentSort */
		String currentSort = null;
		/* Variable Declaration for filterSortStatus */
		String filterSortStatus = null;
		/* Variable Declaration for hostId */
		String hostId = null;
		int currentPage = 0;
		int clickedPage = 0;
		int maxPage = 0;

		try {
			log.debug("entering next method");

// To remove: 			DynaValidatorForm dyForm = (DynaValidatorForm) form;
			systemLog = new SystemLog();
			fromDateAsString = request.getParameter("fromDate");

			toDateAsString = request.getParameter("toDate");
			systemLog.setFromDateAsString(fromDateAsString);
			systemLog.setToDateAsString(toDateAsString);
			SystemFormats sysformat = SwtUtil.getCurrentSystemFormats(request
					.getSession());

			systemLog.setFromDate(SwtUtil.parseDate(systemLog
					.getFromDateAsString(), sysformat.getDateFormatValue()));
			systemLog.setToDate(SwtUtil.parseDate(
					systemLog.getToDateAsString(), sysformat
							.getDateFormatValue()));

			/* gets all the filter values specified for all columns */
			currentFilter = request.getParameter("selectedFilter");
			/* gets the sort column specified */
			currentSort = request.getParameter("selectedSort");
			if (currentFilter == null) {
				currentFilter = "all";
			}
			if (currentSort == null) {
				currentSort = "1|false";
			}
			filterSortStatus = currentFilter + "," + currentSort;
			currentPage = Integer.parseInt(request.getParameter("currentPage"));
			clickedPage = Integer.parseInt(request.getParameter("goToPageNo"));

			if (clickedPage == -2) { // Previous Link Clicked
				currentPage--;
				clickedPage = currentPage;

			} else {
				if (clickedPage == -1) { // Next Link Clicked
					currentPage++;
					clickedPage = currentPage;
				} else {
					currentPage = clickedPage;
				}
			}

			maxPage = Integer.parseInt(request.getParameter("maxPages"));

			hostId = putHostIdListInReq(request);

			List sysLogList = new ArrayList();

			int totalCount = 0;
			totalCount = systemLogManager.getSystemLogList(hostId, systemLog
					.getFromDate(), systemLog.getToDate(), currentPage - 1,
					maxPage, sysLogList, filterSortStatus, sysformat);
			// START: Modified code by Aliveni to set totalCount attribute in
			// request to display the
			// total number of records on screen, on 30-SEP-2010
			/*
			 * this Block reads PageSize from the property file and calculates
			 * the maxPages
			 */
			int pageSize = SwtUtil.getPageSizeFromProperty(SwtConstants.SYSTEM_LOG_SCREEN_PAGE_SIZE);
			maxPage = setMaxPageAttribute(totalCount, pageSize);
			// END: Modified code by Aliveni to set totalCount attribute in
			// request to display the
			// total number of records on screen, on 30-SEP-2010

			PageDetails pSummary = new PageDetails();
			ArrayList<PageDetails> pageSummaryList = new ArrayList<PageDetails>();
			pSummary.setCurrentPageNo(currentPage);
			pSummary.setMaxPages(maxPage);
			pSummary.setTotalCount(totalCount);
			pageSummaryList.add(pSummary);

			// Hide pagination if no of records are "0" or records can be
			// displayed in one page.
			if (maxPage <= 1) {
				request.setAttribute("hidePagination", true);
			}
			String nextLinkStatus = null;
			String prevLinkStatus = null;

			if (clickedPage > 1) // PREVIOUS LINK is enabled if clickedPage
				// is greater than 1
				prevLinkStatus = "true";
			else
				prevLinkStatus = "false";

			if (clickedPage < maxPage) // NEXT LINK is enabled if clickedPage
				// is less than maximun Page No.
				nextLinkStatus = "true";
			else
				nextLinkStatus = "false";

			setSystemLog(systemLog);
			Iterator itr = sysLogList.iterator();
			while (itr.hasNext()) {
				systemLog = (SystemLog) (itr.next());
				systemLog.setLogDate_Date(SwtUtil.formatDate(systemLog
						.getLogDate(), sysformat.getDateFormatValue()));
			}

			request.setAttribute("currentPage", Integer.toString(currentPage));
			request.setAttribute("prevEnabled", prevLinkStatus);
			request.setAttribute("nextEnabled", nextLinkStatus);
			request.setAttribute("maxPage", Integer.toString(maxPage));
			request.setAttribute("selectedFilter", currentFilter);
			request.setAttribute("selectedSort", currentSort);
			request.setAttribute("pageSummaryList", pageSummaryList);
			request.setAttribute("systemLogList", sysLogList);
			request.setAttribute("fromDate", fromDateAsString);
			request.setAttribute("toDate", toDateAsString);
			request.setAttribute("totalCount", totalCount);
			/*
			 * Code added by venkat on 28-mar-2011 for Mantis 1308:"Show 'Last
			 * refresh' time in screens that have refresh function"
			 */
			request.setAttribute("lastRefTime", SwtUtil.getLastRefTimeOnGMTOffset(request,
					SwtUtil.getUserCurrentEntity(request.getSession())));
			log.debug("exiting next method");

			return ("refresh");
		} catch (SwtException swtexp) {
			log.error("SwtException Catch in SystemLogAction.'next' method : "
					+ swtexp.getMessage());
			SwtUtil.logException(swtexp, request, "");
			return ("fail");
		} catch (Exception e) {
			log.error("Exception Catch in SystemLogAction.'next' method : "
					+ e.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					e, "next", SystemLogAction.class), request, "");
			return ("fail");
		} finally {
			systemLog = null;
			fromDateAsString = null;
			toDateAsString = null;
			currentFilter = null;
			currentSort = null;
			filterSortStatus = null;
			hostId = null;
		}
	} // End of next method

	/**
	 * This function is used to sets maximum Page The maximum page count is done
	 * by calculation ie. The total record value got from the DB and the
	 * pageSize is got from the properties file.
	 * 
	 * @param totalCount
	 * @param pageSize
	 * @return int
	 */
	private int setMaxPageAttribute(int totalCount, int pageSize) {
		log.debug(this.getClass().getName()
				+ "- [setMaxPageAttribute] - Entering");
		/* Class instance declaration */
		int maxPage;
		int remainder;
		maxPage = (totalCount) / (pageSize);
		remainder = totalCount % pageSize;
		if (remainder > 0) {
			maxPage++;
		}
		log.debug(this.getClass().getName()
				+ "- [setMaxPageAttribute] - Exiting");
		return maxPage;
	}

} // End of Class SystemLogAction