package org.swallow.control.web.form;

import java.text.SimpleDateFormat;
import java.util.Date;

import org.swallow.control.model.ThreadDTO;

public class InterfaceMonitorSummaryBean {
	private String interfaceId;
	private String fileManagerCount;
	private String commits;
	private Date last;
	private int procedureRunning = -1;
	private int procedureResult = -1;
	private String health;
	private int fullHealth = -1;
	private int active = -1;
	private int busy = -1;
	private int hasErrors = -1;
	
	public int getHasErrors() {
		return hasErrors;
	}
	public void setHasErrors(int hasErrors) {
		this.hasErrors = hasErrors;
	}
	public int getActive() {
		return active;
	}
	public void setActive(int active) {
		this.active = active;
	}
	public int getBusy() {
		return busy;
	}
	public void setBusy(int busy) {
		this.busy = busy;
	}
	public String getInterfaceId() {
		return interfaceId;
	}
	public void setInterfaceId(String interfaceId) {
		this.interfaceId = interfaceId;
	}
	public String getFileManagerCount() {
		return fileManagerCount;
	}
	public void setFileManagerCount(String fileManagerCount) {
		this.fileManagerCount = fileManagerCount;
	}
	public String getCommits() {
		return commits;
	}
	public void setCommits(String commits) {
		this.commits = commits;
	}
	public Date getLast() {
		return last;
	}
	public void setLast(Date last) {
		this.last = last;
	}
	public String getLastString () {
		SimpleDateFormat sdf = new SimpleDateFormat ("yyyy/MM/dd kk:mm:ss");
		return last==null?"":sdf.format (last);
	}
	public int getProcedureRunning() {
		return procedureRunning;
	}
	public void setProcedureRunning(int procedureRunning) {
		this.procedureRunning = procedureRunning;
	}
	public int getProcedureResult() {
		return procedureResult;
	}
	public void setProcedureResult(int procedureResult) {
		this.procedureResult = procedureResult;
	}
	public String getHealth() {
		return health;
	}
	public void setHealth(String health) {
		this.health = health;
	}
	public int getFullHealth() {
		return fullHealth;
	}
	public void setFullHealth(int fullHealth) {
		this.fullHealth = fullHealth;
	}
	
}
