package org.swallow.control.web.form;


import java.util.ArrayList;

import org.swallow.control.model.ThreadDTO;

public class InterfaceMonitorBreakdownBean {
	private ThreadDTO procedureThread;
	private ArrayList<ThreadDTO> fileManagerThreads = new ArrayList<ThreadDTO> ();
	private ArrayList<ThreadDTO> generalThreads = new ArrayList<ThreadDTO> ();
	private ThreadDTO dirManagerThread;
	private ThreadDTO engineThread;
	
	public ThreadDTO getProcedureThread() {
		return procedureThread;
	}
	public void setProcedureThread (ThreadDTO procedure) {
		this.procedureThread = procedure;
	}
	public ArrayList<ThreadDTO> getFileManagerThreads () {
		return fileManagerThreads;
	}
	public ArrayList<ThreadDTO> getGeneralThreads () {
		return generalThreads;
	}
	public ThreadDTO getDirManagerThread() {
		return dirManagerThread;
	}
	public void setDirManagerThread(ThreadDTO dirManagerThread) {
		this.dirManagerThread = dirManagerThread;
	}
	public ThreadDTO getEngineThread() {
		return engineThread;
	}
	public void setEngineThread(ThreadDTO engineThread) {
		this.engineThread = engineThread;
	}
	public void setFileManagerThreads(ArrayList<ThreadDTO> fileManagerThreads) {
		this.fileManagerThreads = fileManagerThreads;
	}
	public void setGeneralThreads(ArrayList<ThreadDTO> generalThreads) {
		this.generalThreads = generalThreads;
	}
	
}
