/*
 * @(#)AccountAccessAction.java 1.0 02/01/2010
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.control.web;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Enumeration;
import java.util.Iterator;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;






import org.swallow.control.model.AccountAccess;
import org.swallow.control.model.AccountAccessDetails;
import org.swallow.control.service.AccountAccessManager;
import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.model.User;
import org.swallow.util.CacheManager;
import org.swallow.util.SwtConstants;
import org.swallow.util.SwtUtil;
import org.swallow.util.LabelValueBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.apache.struts2.convention.annotation.Result;
import org.apache.struts2.convention.annotation.AllowedMethods;
import org.apache.struts2.convention.annotation.Action;
import org.apache.struts2.ServletActionContext;
import org.swallow.util.struts.CustomActionSupport;

/**
 * <AUTHOR>
 * 
 * TODO To change the template for this generated type comment go to Window -
 * Preferences - Java - Code Style - Code Templates
 */
//unnessary code Removed by Arumugam on 01-Nov-2010 for Mantis:0001281- Account Access Control screen takes long time to save
@Action(value = "/accountAccess", results = {
	@Result(name = "success", location = "/jsp/control/accountAccess.jsp"),
	@Result(name = "fail", location = "/error.jsp"),
})

@AllowedMethods ({"display" ,"save" ,"acctAccessConfirm" ,"acctAccessConfirmMSD" })
public class AccountAccessAction extends CustomActionSupport {
public String execute() throws Exception {
    HttpServletRequest request = ServletActionContext.getRequest();

    // List of methods 
    String method = String.valueOf(request.getParameter("method"));

    switch (method) {
        case "unspecified":
            return unspecified();
        case "display":
            return display();
        case "save":
            return save();
        case "acctAccessConfirm":
            return acctAccessConfirm();
        case "acctAccessConfirmMSD":
            return acctAccessConfirmMSD();
        default:
            break;
    }

    return unspecified();
}


private AccountAccess accountAccess;
public AccountAccess getAccountAccess() {
	if (accountAccess == null) {
		accountAccess = new AccountAccess();
	}
	return accountAccess;
}
public void setAccountAccess(AccountAccess accountAccess) {
	this.accountAccess = accountAccess;
	HttpServletRequest request = ServletActionContext.getRequest();
	request.setAttribute("accountAccess", accountAccess);
}


	/**
	 * Used to hold Account access Manager reference object
	 */
	@Autowired
private AccountAccessManager accountAccessManager = null;

	/**
	 * Initializing logger object for this class
	 */
	private final static Log log = LogFactory.getLog(AccountAccessAction.class);

	/**
	 * Set Currency manager object
	 * 
	 * @param accountAccessManager
	 * @return none
	 */
	public void setAccountAccessManager(
			AccountAccessManager accountAccessManager) {
		this.accountAccessManager = accountAccessManager;
	}

	/**
	 * This is the default method for this class returns display method
	 * 
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return ActionForward
	 * @throws SwtException
	 * 
	 */
	public String unspecified()
			throws SwtException {
HttpServletRequest request = ServletActionContext.getRequest();
HttpServletResponse response = ServletActionContext.getResponse();

		log.debug(this.getClass().getName() + " - [Unspecified] - "
				+ "returns to display");
		/* Returns display method */
		return display();

	}

	/**
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return ActionForward
	 * @throws Exception
	 */
	public String display()
			throws SwtException {
HttpServletRequest request = ServletActionContext.getRequest();
HttpServletResponse response = ServletActionContext.getResponse();

		log.debug(this.getClass().getName() + " - [display] - "
				+ "Entry");
		
		/* Local variable declaration and initialization */
		// DynaValidatorForm dyForm = null;
		AccountAccess accountAccess = null;
		CacheManager cacheManagerInst = null;
		User userFromCDM = null;
		 Collection entitycoll = null;
		Collection acctmaintenanceDetails = null;

		String entityId = null;
		String roleId = null;
		String hostId = null;
		int accessId=0;
		int entityAccess = 0;
		boolean matchingAllFlag=true;
		boolean sweepingAllFlag=true;
		boolean manualInputAllFlag=true;
		try {
			/*
			 * Struts Framework built-in Class used to set and get the Form(JSP)
			 * values
			 */
			/* Gets the instance from struts-config.xml file */
			accountAccess = (AccountAccess) getAccountAccess();
			/* sets the bean object in dynaForm */
			setAccountAccess(accountAccess);
			/* Getting role id using bean class */
			roleId = accountAccess.getId().getRoleId();			
			putRoleListInReq(request);
			/* Used to put the role list in request */
			if (roleId==null)
			{
				roleId = getRoleId(request, roleId);
				accountAccess.getId().setRoleId(roleId);
			}			
			/* Used to put the entity list in request */
			entityId = putEntityListInReq(request,roleId);						
			if (entityId.length()==0)
			{
				/* Getting entity id using bean class */
				entityId = accountAccess.getId().getEntityId();
			}
			else{
				/* Setting entity id using bean class */
				accountAccess.getId().setEntityId(entityId);
			}			
			/* Condition to check entity id is null */
			if (((entityId == null) || (entityId.trim().length() <= 0))&& roleId!=null) {
				/* Getting current user's entity frm request */
				entityId = SwtUtil.getUserCurrentEntity(request.getSession());
				/* Setting entity id using bean class */
				accountAccess.getId().setEntityId(entityId);
			}			
			/* Gett he current user from swtutil file */
			userFromCDM = SwtUtil.getCurrentUser(request.getSession());
		
			cacheManagerInst = CacheManager.getInstance();
			/* Getting host id using bean class */
			hostId = cacheManagerInst.getHostId();
			if (roleId != null){
			entitycoll =SwtUtil.getUserEntityAccessListForWorkFlow(roleId);
	        entityAccess = SwtUtil.getUserEntityAccess(entitycoll, entityId);	         
			if (entityAccess == 0) {
				request.setAttribute("EntityAccess",
						SwtConstants.ENTITY_FULL_ACCESS + "");
			}
			else{
				request.setAttribute("EntityAccess",
						SwtConstants.ENTITY_READ_ACCESS + "");
			}
			}
			/*
			 * Fetches account access details from manager class based on host
			 * id,role id and entity id
			 */		

			if(roleId!=null && entityId!=null)
			{	acctmaintenanceDetails = accountAccessManager.accountAccessDetails(
					hostId, roleId, entityId,null);
				
			}
			else
			{
				acctmaintenanceDetails= new ArrayList();
			}			
			if (acctmaintenanceDetails==null)
			{
				acctmaintenanceDetails = new ArrayList();
			}
			
			
			Iterator itr = acctmaintenanceDetails.iterator();
			if(acctmaintenanceDetails!=null && acctmaintenanceDetails.size()>0)
			while (itr.hasNext()) {
				AccountAccessDetails acctAccess = (AccountAccessDetails) (itr.next());				
				if(acctAccess.getAllowManualInput()!=null&&acctAccess.getAllowManualInput().equals("Y")&& manualInputAllFlag==true)
					manualInputAllFlag=true;
				else
					manualInputAllFlag=false;
				if(acctAccess.getAllowMatching()!=null&&acctAccess.getAllowMatching().equals("Y")&& matchingAllFlag==true)
					matchingAllFlag=true;
				else
					matchingAllFlag=false;
				if(acctAccess.getAllowSweeping()!=null&&acctAccess.getAllowSweeping().equals("Y")&& sweepingAllFlag==true)
					sweepingAllFlag=true;
				else
					sweepingAllFlag=false;
			}
			if(acctmaintenanceDetails!=null && acctmaintenanceDetails.size()==0)
			{
				matchingAllFlag=false;
				sweepingAllFlag=false;
				manualInputAllFlag=false;
			}
			
			request.setAttribute("sweepingAllFlag",
					sweepingAllFlag);
			request.setAttribute("matchingAllFlag",
					matchingAllFlag);
			request.setAttribute("manualInputAllFlag",
					manualInputAllFlag);
			
			
			/* Setting account access details in request */
			request.setAttribute("AccountAccessDetails",
					acctmaintenanceDetails);
			accessId = accountAccessManager.checkMenuAccess(roleId);
			request.setAttribute("accessId", accessId);			
			log.debug(this.getClass().getName() + " - [display] - "
					+ "Exit");
			return ("success");
		} catch (SwtException swtexp) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [display] method : - "
					+ swtexp.getMessage());
			SwtUtil.logException(swtexp, request, "");
			
			return ("fail");
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [display] method : - "
					+ exp.getMessage());
			exp.printStackTrace();
			SwtUtil.logException(SwtErrorHandler.getInstance()
							.handleException(exp, "display",
									AccountAccessAction.class), request, "");
			
			return ("fail");
			
		}

	}
	
	/**
	 * Method to get the list of entities from SwtUtil that are accessed by the
	 * user and set them in to the request attribute
	 * 
	 * @param request
	 * @param roleId 	
	 * @return String
	 * @throws SwtException
	 */

	private String getRoleId(HttpServletRequest request,String roleId)
			throws SwtException {
		log.debug(this.getClass().getName() + " - [getRoleId] - "
				+ "Entry");		 
		/* Method's local variable declaration */
		User userFromCDM = null;		
		LabelValueBean labelValueBeanObj= null;		
		Collection roleList = null;
		String hostId = null;
		CacheManager cacheManagerInst = null;
		boolean roleFlag = true;
		Iterator itr = null;
		
		cacheManagerInst = CacheManager.getInstance();
		hostId = cacheManagerInst.getHostId();
		/* Fetching role details from manger file */
		roleList = accountAccessManager.getRoleDetails(hostId);		
		/* Gett he current user from swtutil file */
		userFromCDM = SwtUtil.getCurrentUser(request.getSession());
		if ((roleId == null || (roleId.length() <= 0)) && roleList.size()>0) {
			/* Getting from user bean class */
			roleId = userFromCDM.getRoleId();
			itr = roleList.iterator();
			
			while(itr.hasNext()){
				labelValueBeanObj =(LabelValueBean)itr.next();
				if (roleId.equalsIgnoreCase(labelValueBeanObj.getValue()))
				{
					roleFlag = false;
					break;
				}				
			}
			if (roleFlag){
				labelValueBeanObj =(LabelValueBean)roleList.iterator().next();
				roleId = labelValueBeanObj.getValue();
			}		
			
		}
		log.debug(this.getClass().getName() + " - [getRoleId] - "+ "Exit");	
				return roleId;
	}


	/**
	 * Method to get the list of entities from SwtUtil that are accessed by the
	 * user and set them in to the request attribute
	 * 
	 * @param request
	 * @param roleId
	 * @return String
	 * @throws SwtException
	 */

	private String putEntityListInReq(HttpServletRequest request,String roleId)
			throws SwtException {
		log.debug(this.getClass().getName() + " - [putEntityListInReq] - "
				+ "Entry");
		 
		/* Method's local variable declaration */
		HttpSession session = null;
		Collection coll = null;
		User userFromCDM = null;
		session = request.getSession();
		LabelValueBean labelValueBeanObj= null;
		String entityId="";
		Collection roleList = null;
		String hostId = null;
		CacheManager cacheManagerInst = null;
		boolean roleFlag = true;
		Iterator itr = null;
		
		cacheManagerInst = CacheManager.getInstance();
		hostId = cacheManagerInst.getHostId();
		/* Fetching role details from manger file */
		roleList = accountAccessManager.getRoleDetails(hostId);		
		/* Gett he current user from swtutil file */
		userFromCDM = SwtUtil.getCurrentUser(request.getSession());			
		if ((roleId == null || (roleId.length() <= 0)) && roleList.size()>0) {
			/* Getting from user bean class */
			roleId = userFromCDM.getRoleId();
			itr = roleList.iterator();
			
			while(itr.hasNext()){
				labelValueBeanObj =(LabelValueBean)itr.next();
				if (roleId.equalsIgnoreCase(labelValueBeanObj.getValue()))
				{
					roleFlag = false;
					break;
				}				
			}
			if (roleFlag){
				labelValueBeanObj =(LabelValueBean)roleList.iterator().next();
				roleId = labelValueBeanObj.getValue();
			}	
		}
		/* Collects the list of entity for the user */
		if (roleId!=null)
		{
		coll = SwtUtil.getUserEntityAccessListForWorkFlow(roleId);
		coll = SwtUtil.convertEntityAcessCollectionLVL(coll, session);
		if(request.getParameter("roleChange")!=null)
			if(request.getParameter("roleChange").equals("Y"))
			{ 
				if(coll.size()!=0)
				{
					labelValueBeanObj =(LabelValueBean)coll.iterator().next();
					entityId = labelValueBeanObj.getValue();
					}
			}
		request.setAttribute("entities", coll);
		log.debug(this.getClass().getName() + " - [putEntityListInReq] - "
				+ "Exit");
		}
		else{
			coll = new ArrayList();
			request.setAttribute("entities", coll);
		}
		return entityId;
	}

	/**
	 * Method to put the role details in request
	 * 
	 * @param request
	 * @return none
	 * @throws SwtException
	 */
	private void putRoleListInReq(HttpServletRequest request)
			throws SwtException {
		log.debug(this.getClass().getName() + " - [putRoleListInReq] - "
				+ "Entry");
		/* Method's local variable declaration */
		CacheManager cacheManagerInst = null;
		String hostId = null;
		Collection roleList = null;

		cacheManagerInst = CacheManager.getInstance();
		hostId = cacheManagerInst.getHostId();
		/* Fetching role details from manger file */
		roleList = accountAccessManager.getRoleDetails(hostId);
		/* Setting role details in request */
		request.setAttribute("roleIdList", roleList);
		log.debug(this.getClass().getName() + " - [putRoleListInReq] - "
				+ "Exit");
	}

	/**
	 * Method to save the account access details in database.
	 * 
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return ActionForward
	 * @throws SwtException
	 */
	
	public String save()
			throws SwtException {
HttpServletRequest request = ServletActionContext.getRequest();
HttpServletResponse response = ServletActionContext.getResponse();

		
		log.debug(this.getClass().getName() + " - [save] - "
				+ "Entry");
		/* Local variable declaration and initialization */
		String hostId = null;
		String roleId=null;
		CacheManager cacheManagerInst = null;
		Collection collAcctAcess = new ArrayList();		
		String userId = SwtUtil.getCurrentUserId(request.getSession());	
		// DynaValidatorForm dyForm = null;
		Collection acctmaintenanceDetails = null;

	try {		
		cacheManagerInst = CacheManager.getInstance();
		/* Getting host id using cache manager */
		hostId = cacheManagerInst.getHostId();
		roleId = request.getParameter("roleId");		
		String entityId = request.getParameter("entityId");
		// Start: code added by Arumugam on 11-NOV-2010 for Mantis:0001281- to get the screen value and set the bean object
		
		acctmaintenanceDetails = accountAccessManager.accountAccessDetails(
				hostId, roleId, entityId,null);
		Iterator itr = acctmaintenanceDetails.iterator();
		while (itr.hasNext()) {
			AccountAccessDetails acctAccess = (AccountAccessDetails) (itr.next());
			String inputcheck =request.getParameter("inputcheck#"+acctAccess.getAccountId());
			if(inputcheck==null){
				acctAccess.setAllowManualInput("N");
			}else{
				acctAccess.setAllowManualInput("Y");
			}
			String matchingcheck =request.getParameter("matchingcheck#"+acctAccess.getAccountId());
			if(matchingcheck==null){
				acctAccess.setAllowMatching("N");
			}else{
				acctAccess.setAllowMatching("Y");
			}
			String sweepingcheck =request.getParameter("sweepingcheck#"+acctAccess.getAccountId());
			if(sweepingcheck==null){
				acctAccess.setAllowSweeping("N");
			}else{
				acctAccess.setAllowSweeping("Y");
			}
			acctAccess.setRoleId(roleId);
			acctAccess.setHostId(hostId);
			acctAccess.setEntityId(entityId);
			collAcctAcess.add(acctAccess);	
			}
		
		accountAccessManager.saveAccountAccessControlDetails(collAcctAcess, userId);
		// End: code added by Arumugam on 11-NOV-2010 for Mantis:0001281- to get the screen value and set the bean object
		} catch (SwtException swtexp) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [save] method : - "
					+ swtexp.getMessage());
			SwtUtil.logException(swtexp, request, "");
			return ("fail");

		} catch (Exception exp) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [save] method : - "
					+ exp.getMessage());
			exp.printStackTrace();
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "save", AccountAccessAction.class), request, "");

		}
		log.debug(this.getClass().getName() + " - [save] - "
				+ "Exit");
		
		return null;

	}
	/**
	 * This is used to get the account access confirmation
	 * 
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return ActionForward
	 * @throws SwtException
	 */

	public String acctAccessConfirm() throws SwtException {
HttpServletRequest request = ServletActionContext.getRequest();
HttpServletResponse response = ServletActionContext.getResponse();

		
		/* Local variable declaration and initialization */
		String roleId=null;
		String hostId=null;
		String entityId=null;
		String accountId=null;
		Collection acct = null;
		String status =null;
		boolean flag = false;		
		AccountAccess acctAccess = null;
		CacheManager cacheManagerInst=null;
		User userFromCDM =null;
		boolean accessFlag = false;
		
		try {
			log.debug(this.getClass().getName() + " - [acctAccessConfirm] - " + "Entry");
			acct = new ArrayList();
			acctAccess = new AccountAccess();
			cacheManagerInst = CacheManager.getInstance();
			/*Read the current user from SwtUtil file*/
			userFromCDM = SwtUtil.getCurrentUser(request.getSession());
			/*Getting host id from cache manager file*/
			hostId = cacheManagerInst.getHostId();
			/*Getting role id using bean class*/
			roleId = userFromCDM.getRoleId();
			/*Getting account id,entity id,status from request*/
			entityId = request.getParameter("entityId");
			accountId = request.getParameter("accountId");
			status = request.getParameter("status");
			/*Setting role id using bean class*/
			acctAccess.getId().setRoleId(roleId);
			/*Setting entity id using bean class*/
			acctAccess.getId().setEntityId(entityId);
			/*Setting account id using bean class*/
			acctAccess.getId().setAccountId(accountId);
			/*Setting host id using bean class*/
			acctAccess.getId().setHostId(hostId);
			/*Checking role account access flag by calling manager file*/
			accessFlag = accountAccessManager.getRoleAccessDetails(acctAccess);
			/*Checking account access by calling manager file*/
			if(accessFlag){
				flag = accountAccessManager.checkAcctAccess(acctAccess,status);
			}
			else{
			    flag = true;
			}
			response.getWriter().print(flag);
			log.debug(this.getClass().getName() + " - [acctAccessConfirm] - " + "Exit");
		} catch (SwtException swtexp) {			
			log.error(this.getClass().getName()
					+ " - Exception Catched in [acctAccessConfirm] method : - "
					+ swtexp.getMessage());

			SwtUtil.logException(swtexp,request,"");
	        return ("fail");
		} catch (Exception exp) {

			exp.printStackTrace();
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "acctAccessConfirm", AccountAccessAction.class), request, "");
		} finally {
			acctAccess = null;
		}

		return null;

	}
	
	/**
	 * This is used to get the account access confirmation
	 * 
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return ActionForward
	 * @throws SwtException
	 */

	public String acctAccessConfirmMSD() throws SwtException {
HttpServletRequest request = ServletActionContext.getRequest();
HttpServletResponse response = ServletActionContext.getResponse();

		
		/* Local variable declaration and initialization */
		String roleId=null;
		String entityId=null;
		String movementId=null;
		String status = null;
		boolean flag = false;		
		User userFromCDM =null;
		boolean accessFlag = false;
		// variable to get the host id
		String hostId=null;
		// used to get the host id
		CacheManager cacheManagerInst=null;
		// Declare the acctAccess
		AccountAccess acctAccess = null;
		try {
			log.debug(this.getClass().getName() + " - [acctAccessConfirmMSD] - " + "Entry");
			/*Read the current user from SwtUtil file*/
			userFromCDM = SwtUtil.getCurrentUser(request.getSession());
			cacheManagerInst = CacheManager.getInstance();
			/*Getting role id using bean class*/
			roleId = userFromCDM.getRoleId();
			/*Getting account id,entity id,status from request*/
			entityId = request.getParameter("entityId");
			movementId = request.getParameter("movementId");
			status = request.getParameter("status");
			/*Getting host id from cache manager file*/
			hostId = cacheManagerInst.getHostId();
			// create the acctAccess instance
			acctAccess = new AccountAccess();
			/*Setting role id using bean class*/
			acctAccess.getId().setRoleId(roleId);
			/*Setting host id using bean class*/
			acctAccess.getId().setHostId(hostId);
			/*Checking role account access flag by calling manager file*/
			accessFlag = accountAccessManager.getRoleAccessDetails(acctAccess);
			/*Checking account access by calling manager file*/
			if(accessFlag){
				flag = accountAccessManager.checkAcctAccessMSD(movementId, roleId, entityId, status);
			}
			else{
			    flag = true;
			}
			response.getWriter().print(flag);
			log.debug(this.getClass().getName() + " - [acctAccessConfirmMSD] - " + "Exit");
		} catch (SwtException swtexp) {			
			log.error(this.getClass().getName()
					+ " - Exception Catched in [acctAccessConfirmMSD] method : - "
					+ swtexp.getMessage());

			SwtUtil.logException(swtexp,request,"");
	        return ("fail");
		} catch (Exception exp) {

			exp.printStackTrace();
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "acctAccessConfirmMSD", AccountAccessAction.class), request, "");
		} finally {
		}

		return null;

	}

}
