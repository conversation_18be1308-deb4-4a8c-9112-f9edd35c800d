/*
 * @(#)MatchDriverAction.java 1.0 28/04/06
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.control.web;

import java.text.SimpleDateFormat;
import java.util.Collection;
import java.util.Iterator;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;





import org.swallow.control.service.MatchDriverManager;
import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.util.CacheManager;
import org.swallow.util.SwtConstants;
import org.swallow.util.SwtUtil;
import org.swallow.util.SystemFormats;
import org.swallow.work.model.MatchDriver;
import org.apache.struts2.convention.annotation.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.apache.struts2.convention.annotation.AllowedMethods;
import org.apache.struts2.convention.annotation.Action;
import org.apache.struts2.ServletActionContext;
import org.swallow.util.struts.CustomActionSupport;

/**
 * <AUTHOR>
 * 
 * TODO To change the template for this generated type comment go to Window -
 * Preferences - Java - Code Style - Code Templates
 */
@Action(value = "/matchDriver", results = {
	@Result(name = "success", location = "/jsp/control/recovery.jsp"),
	@Result(name = "fail", location = "/error.jsp"),
})

@AllowedMethods ({"displayList" ,"setRun" })
public class MatchDriverAction extends CustomActionSupport {



private MatchDriver matchDriver;
public MatchDriver getMatchDriver() {
	if (matchDriver == null) {
		matchDriver = new MatchDriver();
	}
	return matchDriver;
}
public void setMatchDriver(MatchDriver matchDriver) {
	this.matchDriver = matchDriver;
	HttpServletRequest request = ServletActionContext.getRequest();
	request.setAttribute("matchDriver", matchDriver);
}

	@Autowired
	private MatchDriverManager matchDriverManager;

	private final Log log = LogFactory.getLog(MatchDriverAction.class);

	public void setMatchDriverManager(MatchDriverManager matchDriverManager) {
		this.matchDriverManager = matchDriverManager;
	}
public String execute() throws Exception {
    HttpServletRequest request = ServletActionContext.getRequest();

    // List of methods 
    String method = String.valueOf(request.getParameter("method"));

    switch (method) {
        case "unspecified":
            return unspecified();
        case "displayList":
            return displayList();
        case "setRun":
            return setRun();
        default:
            break;
    }

    return unspecified();
}
	/**
	 * Default method called when method parameter is null in request.
	 */
	public String unspecified()
			throws SwtException {
HttpServletRequest request = ServletActionContext.getRequest();
HttpServletResponse response = ServletActionContext.getResponse();

		return displayList();
	}

	// Start: Code modified by Bala for Mantis 1420 - Test date situation can
	// lead to matching process failing to start on 14-Apr-2011
	/**
	 * Method to display the list of currencies with the status and last run
	 * date time while loading the screen
	 * 
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return
	 * @throws SwtException
	 */
	public String displayList()
			throws SwtException {
HttpServletRequest request = ServletActionContext.getRequest();
HttpServletResponse response = ServletActionContext.getResponse();


// To remove: 		// Declare DynaValidatorForm object
// To remove: 		DynaValidatorForm dyForm = null;
		// Variable to hold hostId
		String hostId = null;
		// Declare MatchDriver object
		MatchDriver matchDriver = null;
		// Variable to hold entityId
		String entityId = null;
		// Declare SystemFormats object
		SystemFormats sysformat = null;
		// Collection to hold match driver details
		Collection<MatchDriver> CollmatchDriver = null;
		// Used to iterate the collection
		Iterator<MatchDriver> itr = null;
		// Variable to hold dateFormat
		String dateFormat = null;
		// Declare SimpleDateFormat object
		SimpleDateFormat sdf = null;
		try {
			log.debug(this.getClass().getName() + " - [displayList] - Enter");
			// get the form object
// To remove: 			dyForm = (DynaValidatorForm) form;
			// get the host Id
			hostId = CacheManager.getInstance().getHostId();
			// entity list
			putEntityListInReq(request);
			// get the match driver object
			matchDriver = (MatchDriver) getMatchDriver();
			// get the entity Id
			entityId = matchDriver.getId().getEntityId();
			if (entityId == null || entityId.trim().length() <= 0) {
				entityId = SwtUtil.getUserCurrentEntity(request.getSession());
				matchDriver.getId().setEntityId(entityId);
			}
			// get the entity currency group access
			int menuEntityCurrGrpAcess = SwtUtil.getMenuEntityCurrGrpAccess(
					request, entityId, null);
			if (menuEntityCurrGrpAcess == 0) {
				setButtonStatus(request, SwtConstants.STR_FALSE);
				request.setAttribute("EntityAccess",
						SwtConstants.ENTITY_FULL_ACCESS + "");
			} else {
				setButtonStatus(request, SwtConstants.STR_FALSE);
				request.setAttribute("EntityAccess",
						SwtConstants.ENTITY_READ_ACCESS + "");
			}
			// get the system format
			sysformat = SwtUtil.getCurrentSystemFormats(request.getSession());
			// get the match driver list
			CollmatchDriver = matchDriverManager.getMatchDriverList(hostId,
					entityId);
			// set the collection to request
			request.setAttribute("CollmatchDriver", CollmatchDriver);
			// iterate the collection
			itr = CollmatchDriver.iterator();
			// get the date format
			dateFormat = sysformat.getDateFormatValue();
			// create SimpleDateFormat
			sdf = new SimpleDateFormat(dateFormat + " HH:mm:ss");
			while (itr.hasNext()) {
				matchDriver = (MatchDriver) itr.next();
				if (matchDriver.getLastStarted() != null) {
					matchDriver.setUpdateDateAsString(sdf.format(matchDriver
							.getLastStarted()));
				}
			}
			log.debug(this.getClass().getName() + " - [displayList] - Exit");
		} catch (SwtException swtexp) {
			log
					.error(this.getClass().getName()
							+ " - [displayList] - SwtException -"
							+ swtexp.getMessage());
			SwtUtil.logException(swtexp, request, "");
			return ("fail");
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					+ " - [displayList] - GenericException -"
					+ exp.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "displayList", MatchDriverAction.class), request, "");
			return ("fail");
		}
		return ("success");
	}

	/**
	 * Method to set the status of the selected currency to 'To Run'.
	 * 
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return
	 * @throws SwtException
	 */
	public String setRun()
			throws SwtException {
HttpServletRequest request = ServletActionContext.getRequest();
HttpServletResponse response = ServletActionContext.getResponse();


// To remove: 		// Declare DynaValidatorForm object
// To remove: 		DynaValidatorForm dyForm = null;
		// Variable to hold hostId
		String hostId = null;
		// Declare MatchDriver object
		MatchDriver matchDriver = null;
		// Variable to hold entityId
		String entityId = null;
		// Variable to hold currency
		String currency = null;
		// Collection to hold match driver details
		Collection<MatchDriver> CollmatchDriver = null;
		// Used to iterate the collection
		Iterator<MatchDriver> itr = null;
		// Declare SystemFormats object
		SystemFormats sysformat = null;
		// Variable to hold dateFormat
		String dateFormat = null;
		// Declare SimpleDateFormat object
		SimpleDateFormat sdf = null;
		try {
			log.debug(this.getClass().getName() + " - [setRun] - Enter");
			// get the hostId
			hostId = CacheManager.getInstance().getHostId();
			// get the form object
// To remove: 			dyForm = (DynaValidatorForm) form;
			// entity list
			putEntityListInReq(request);
			// get the matchDriver object
			matchDriver = (MatchDriver) getMatchDriver();
			// get the entity Id
			entityId = matchDriver.getId().getEntityId();
			// set the hostId to object
			matchDriver.getId().setHostId(hostId);
			// get the currency
			currency = request.getParameter("selectedCurrency");
			// set the values to object
			matchDriver.setNewMoveFlag("Y");
			matchDriver.setProcessingFlag("N");
			matchDriver.getId().setCurrencyCode(currency);
			// update the match driver details
			matchDriverManager.updateMatchDriverDetail(matchDriver);
			// get the match driver details
			CollmatchDriver = matchDriverManager.getMatchDriverList(hostId,
					entityId);
			// set the collection to request
			request.setAttribute("CollmatchDriver", CollmatchDriver);
			// iterate the collection
			itr = CollmatchDriver.iterator();
			// get the system format
			sysformat = SwtUtil.getCurrentSystemFormats(request.getSession());
			// get the date format
			dateFormat = sysformat.getDateFormatValue();
			// create SimpleDateFormat
			sdf = new SimpleDateFormat(dateFormat + " HH:mm:ss");
			while (itr.hasNext()) {
				matchDriver = (MatchDriver) itr.next();
				if (matchDriver.getLastStarted() != null) {
					matchDriver.setUpdateDateAsString(sdf.format(matchDriver
							.getLastStarted()));
				}
			}
			// get the entity currency group access
			int menuEntityCurrGrpAcess = SwtUtil.getMenuEntityCurrGrpAccess(
					request, entityId, null);
			if (menuEntityCurrGrpAcess == 0) {
				setButtonStatus(request, SwtConstants.STR_FALSE);
				request.setAttribute("EntityAccess",
						SwtConstants.ENTITY_FULL_ACCESS + "");
			} else {
				setButtonStatus(request, SwtConstants.STR_FALSE);
				request.setAttribute("EntityAccess",
						SwtConstants.ENTITY_READ_ACCESS + "");
			}
			log.debug(this.getClass().getName() + " - [setRun] - Exit");
		} catch (SwtException swtexp) {
			log.error(this.getClass().getName()
					+ " - [setRun] - SwtException -" + swtexp.getMessage());
			SwtUtil.logException(swtexp, request, "");
			return ("fail");
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					+ " - [setRun] - GenericException -" + exp.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "setRun", MatchDriverAction.class), request, "");
			return ("fail");
		}
		return ("success");
	}

	// End: Code modified by Bala for Mantis 1420 - Test date situation can
	// lead to matching process failing to start on 14-Apr-2011

	/**
	 * 
	 * @param req
	 * @param runStatus
	 */
	private void setButtonStatus(HttpServletRequest req, String runStatus) {
		req.setAttribute(SwtConstants.ADD_BUT_STS, runStatus);
	}

	/**
	 * Method called to populate request with entityList
	 * 
	 * @param request
	 * @throws SwtException
	 */
	private void putEntityListInReq(HttpServletRequest request)
			throws SwtException {
		HttpSession session = request.getSession();
		Collection coll = SwtUtil.getUserEntityAccessList(session);
		coll = SwtUtil.convertEntityAcessCollectionLVL(coll, session);
		request.setAttribute("entities", coll);
	}

}