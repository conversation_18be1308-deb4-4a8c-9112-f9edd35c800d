/*
 * @(#)InternalMessageCheckAction .java  23/12/05
 * 
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 * 
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */


package org.swallow.control.web;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Iterator;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.apache.struts2.ServletActionContext;
import org.apache.struts2.convention.annotation.Action;
import org.apache.struts2.convention.annotation.Result;
import org.swallow.cluster.RegisterZookeeper;
import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.model.User;
import org.swallow.security.jwt.TokensProvider;
import org.swallow.util.CommonDataManager;
import org.swallow.util.SessionManager;
import org.swallow.util.SwtConstants;
import org.swallow.util.SwtUtil;
import org.swallow.util.struts.CustomActionSupport;

import com.opensymphony.xwork2.ActionSupport;

@Action(value = "/checknewmsgs", results = {
	    @Result(name = "fail", location = "/error.jsp"),
	})
public class InternalMessageCheckAction extends CustomActionSupport{
	private final Log log = LogFactory.getLog(InternalMessageCheckAction.class);
	/* (non-Javadoc)
	 * @see org.apache.struts.actions.DispatchAction#unspecified(org.apache.struts.action.ActionMapping, org.apache.struts.action.ActionForm, javax.servlet.http.HttpServletRequest, javax.servlet.http.HttpServletResponse)
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return a Result of type Action forward
	 */
	@Override
	public String execute()
	throws Exception {
		return checkNewInternalMessages();
	}
	
	/* (non-Javadoc)
	 * @see org.apache.struts.actions.DispatchAction#unspecified(org.apache.struts.action.ActionMapping, org.apache.struts.action.ActionForm, javax.servlet.http.HttpServletRequest, javax.servlet.http.HttpServletResponse)
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return a Result of type Action forward
	 */
	public String checkNewInternalMessages()
	throws SwtException{
		HttpServletRequest request = ServletActionContext.getRequest();
    	HttpServletResponse response = ServletActionContext.getResponse();
		try{
			HttpSession session = request.getSession(false);
			if (session != null && (SessionManager.getInstance().getSessionMap().containsKey(session.getId())
					|| RegisterZookeeper.getInstance().isClusterEnabled())) {
				Collection messages = (Collection)session.getAttribute("INTERNAL_MESSAGES");
				CommonDataManager cdm = (CommonDataManager)session.getAttribute(SwtConstants.CDM_BEAN);
				String userId = "";
				if(cdm != null){
					User usr = cdm.getUser();
					if(usr != null){
						User.Id userIdObj = usr.getId();
						if(userIdObj != null)
							userId = userIdObj.getUserId();
					}
				}
				else if(TokensProvider.getInstance().validateToken(request)) {
					userId = TokensProvider.getInstance().getUserIdFromToken(request);
					log.info(this.getClass().getName()+ " ----> RECOVERED SESSION FROM TOKEN: in InternalMessageCheckAction, the session "+session.getId()+",  userId="+userId+" is recovered from token");
				} 
				else{
					log.info(this.getClass().getName()+" ----> RECOVERING SESSION FROM TOKEN WAS NOT POSSIBLE in ScenarioAlertAction !!!! cdm is null ? "+(cdm == null)+" ,,, Token is valid ? "+TokensProvider.getInstance().validateToken(request));

					response.getWriter().println("ERROR");
					response.getWriter().print(SwtUtil.getMessage("sessionTimeOutMessage", request));
					return null;
				}
				if( userId != null){
					if(messages != null ){
						String msgStr = "";
						StringBuffer buf = new StringBuffer();
						Iterator itr = messages.iterator();
						while(itr.hasNext()){
							String str = (String)itr.next();
							buf.append(str).append("\n");
						}
						if(buf.length() > 0){
							int idx = buf.lastIndexOf("\n");
							buf.deleteCharAt(idx);
						}
						msgStr = buf.toString();
						response.getWriter().println("OK");
						response.getWriter().print(msgStr);
						session.setAttribute("INTERNAL_MESSAGES",new ArrayList());
					}
				}
			}else{
				log.info(this.getClass().getName()+ " ----> RECOVERING SESSION FROM TOKEN WAS NOT POSSIBLE in InternalMessageCheckAction !!!! session is null "+(session == null)+" ,,, Session is registered ? "+(session != null ? SessionManager.getInstance().getSessionMap().containsKey(session.getId()) : "SESSION IN NULL !"));
				response.getWriter().println("ERROR");
				response.getWriter().print(SwtUtil.getMessage("sessionTimeOutMessage", request));
				return null;
			}
			
			return null;
	    }catch(Exception e){
	        e.printStackTrace();
	        SwtUtil. logException (SwtErrorHandler.getInstance().handleException(e,"checkNewInternalMessages",InternalMessageCheckAction.class), request,"");
	        return "fail";

	    }
	}
	}

