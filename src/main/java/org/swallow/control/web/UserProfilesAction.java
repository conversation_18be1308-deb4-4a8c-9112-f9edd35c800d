/*
 * @(#)UserProfilesAction.java 1.0 23/08/06
 *

 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.control.web;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Iterator;
import java.util.List;
import java.util.StringTokenizer;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

import org.swallow.control.service.UserProfilesManager;
import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.model.User;
import org.swallow.model.UserProfile;
import org.swallow.model.UserProfileDetail;
import org.swallow.util.CacheManager;
import org.swallow.util.CommonDataManager;
import org.swallow.util.SwtConstants;
import org.swallow.util.SwtUtil;
import org.swallow.util.UserThreadLocalHolder;
import org.swallow.util.LabelValueBean;
import org.apache.struts2.convention.annotation.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.apache.struts2.convention.annotation.AllowedMethods;
import org.apache.struts2.convention.annotation.Action;
import org.apache.struts2.ServletActionContext;
import org.swallow.util.struts.CustomActionSupport;

/**
 * <AUTHOR>
 * 
 * This Class is used to add and display the user profile
 */
@Action(value = "/userprofiles", results = {
	@Result(name = "fail", location = "/error.jsp"),
	@Result(name = "add", location = "/jsp/control/userprofiles.jsp"),
})

@AllowedMethods ({"add" ,"getProfileDetails" ,"update" ,"displayProfile" })
public class UserProfilesAction extends CustomActionSupport {
	HttpServletRequest request = ServletActionContext.getRequest();
	HttpServletResponse response = ServletActionContext.getResponse();

private UserProfile userprofiles;
public UserProfile getUserprofiles() {
	if (userprofiles == null) {
		userprofiles = new UserProfile();
	}
	return userprofiles;
}
public void setUserprofiles(UserProfile userprofiles) {
	this.userprofiles = userprofiles;
	HttpServletRequest request = ServletActionContext.getRequest();
	request.setAttribute("userprofiles", userprofiles);
}


	private final Log log = LogFactory.getLog(UserProfilesAction.class);
	@Autowired
	private UserProfilesManager userprofilesManager;

	public void setUserprofilesManager(UserProfilesManager userprofilesManager) {
		this.userprofilesManager = userprofilesManager;
	}

	private String putHostIdListInReq(HttpServletRequest request)
			throws SwtException {

		CacheManager cacheManagerInst = CacheManager.getInstance();
		String hostId = cacheManagerInst.getHostId();

		return hostId;

	}
	public String execute() throws Exception {
	    HttpServletRequest request = ServletActionContext.getRequest();

	    // List of methods 
	    String method = String.valueOf(request.getParameter("method"));

	    switch (method) {
	        case "unspecified":
	            return unspecified();
	        case "add":
	            return add();
	        case "getProfileDetails":
	            return getProfileDetails();
	        case "update":
	            return update();
	        case "displayProfile":
	            return displayProfile();
	        default:
	            break;
	    }

	    return unspecified();
	}
	// Start Code modified by Chidambaranathan for removing of display method
	// due to non-usage ,for Mantis_1217 on 01-Jun-2011
	/**
	 * This is the default method for this class returns add method
	 * 
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return
	 * @throws Exception
	 */

	public String unspecified()
			throws Exception {
		log.debug(this.getClass().getName() + " - [Unspecified] - "
				+ "returns to add");
		// This will call the add method instead of display when user profiles
		// screen onload
		return add();
	}

	// End Code modified by Chidambaranathan for removing of display method due
	// to non-usage, for Mantis_1217 on 01-Jun-2011

	/**
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return
	 * @throws Exception
	 */
	public String add()
			throws Exception {
		try {

			log.debug("entering 'add' method");

			String hostId = CacheManager.getInstance().getHostId();

			String userId = SwtUtil.getCurrentUserId(request.getSession(false));

			String profileId = "";

			// Setting the dynavalidator form
// To remove: 			DynaValidatorForm dyForm = (DynaValidatorForm) form;

			UserProfile userprofiles = new UserProfile();

			userprofiles.getId().setHostId(hostId);

			userprofiles.getId().setUserId(userId);

			putProfileListInRequest(request);

			Collection collProfile = (Collection) request
					.getAttribute("userProfileList");
			// Getting the first element of the dropdown and populating dynaForm
			// with its detail
			if (collProfile != null) {
				Iterator itr = collProfile.iterator();
				while (itr.hasNext()) {
					LabelValueBean lvb = (LabelValueBean) (itr.next());
					profileId = lvb.getValue();
					break;
				}
			}
			userprofiles.getId().setProfileId(profileId);

			List dataList = userprofilesManager.fetchDetails(hostId, userId,
					profileId);

			Iterator itr = dataList.iterator();
			if (dataList.size() > 0) {
				if (itr.hasNext()) {
					userprofiles = (UserProfile) itr.next();
				}
			}

			setUserprofiles(userprofiles);

			request.setAttribute("methodName", "update");

			String windowsSetting = request.getParameter("wndsetting");

			request.setAttribute("wndsetting", windowsSetting);

			return ("add");
		} catch (SwtException swtexp) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [add] method : - "
					+ swtexp.getMessage());
			SwtUtil.logException(swtexp, request, "");
			return ("fail");
		} catch (Exception e) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [add] method : - "
					+ e.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					e, "add", UserProfilesAction.class), request, "");
			return ("fail");
		}
	}

	private Collection parseProfileDetails(String wndSetting, String profileId)
			throws SwtException {
		log.debug("entering 'parseProfileDetails' method");
		String hostId = CacheManager.getInstance().getHostId();
		String userId = UserThreadLocalHolder.getUser();

		ArrayList coll = new ArrayList();
		if (wndSetting != null && wndSetting.trim().length() > 0) {
			StringTokenizer tokenizer = new StringTokenizer(wndSetting, "~",
					false);
			while (tokenizer.hasMoreTokens()) {
				String wnd = tokenizer.nextToken();
				if (wnd != null && wnd.length() > 0) {
					StringTokenizer token = new StringTokenizer(wnd, ";", false);
					if (token.countTokens() == 5) {
						String menuItemId = token.nextToken();
						String left = token.nextToken();
						String top = token.nextToken();
						String width = token.nextToken();
						String height = token.nextToken();
						UserProfileDetail userProfileDetObj = new UserProfileDetail();

						userProfileDetObj.getId().setHostId(hostId);
						userProfileDetObj.getId().setProfileId(profileId);
						userProfileDetObj.getId().setMenuItemId(menuItemId);
						userProfileDetObj.getId().setUserId(userId);

						userProfileDetObj.setHeight(Integer.valueOf(height));
						userProfileDetObj.setLeftX(Integer.valueOf(left));
						userProfileDetObj.setLeftY(Integer.valueOf(top));
						userProfileDetObj.setWidth(Integer.valueOf(width));
						coll.add(userProfileDetObj);
					}
				}
			}
		}

		return coll;
	}

	/**
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return
	 * @throws Exception
	 */

	public String getProfileDetails() throws Exception {
		try {

			log.debug("entering 'getProfileDetails' method");
			CommonDataManager CDM = (CommonDataManager) request.getSession()
					.getAttribute("CDM");
			StringBuffer str = new StringBuffer();

			User userinsession = CDM.getUser();
			String userId = userinsession.getId().getUserId();
			String profileId = request.getParameter("profileId");
			String hostId = CacheManager.getInstance().getHostId();
			String roleId = userinsession.getRoleId();
			org.swallow.model.MenuItem menuItem = null;
			List profileDetails = userprofilesManager.getProfileDetails(hostId,
					userId, profileId, roleId);
			int size = profileDetails.size();
			if (profileDetails != null && profileDetails.size() > 0) {

				UserProfileDetail profileDtl = new UserProfileDetail();

				Iterator itr = profileDetails.iterator();

				for (int i = 0; i < size; i++) {
					profileDtl = (UserProfileDetail) profileDetails.get(i);
					menuItem = profileDtl.getMenuItem();

					if (menuItem.getProgram().getProgramName().indexOf("?") > 0) {
						str.append(menuItem.getProgram().getProgramName()
								+ "&menuAccessId=" + menuItem.getAccessId()
								+ "|");
					} else {
						str.append(menuItem.getProgram().getProgramName()
								+ "?menuAccessId=" + menuItem.getAccessId()
								+ "|");
					}
					str.append(menuItem.getItemId() + "Window|");
					str.append("width=" + (profileDtl.getWidth()+8));
					str.append(",height=" + (profileDtl.getHeight()+12));
					if (profileDtl.getLeftX().intValue() > 0) {
						str.append(",left=" + profileDtl.getLeftX());
					}
					if (profileDtl.getLeftY().intValue() > 0) {
						str.append(",top=" + profileDtl.getLeftY());
					}
					str
							.append(",toolbar=0, status=yes,resizable=yes, scrollbars=yes|false|"
									+ menuItem.getItemId());

					if (i != (size - 1)) {
						str.append("*");
					}

				}

				while (itr.hasNext()) {
					profileDtl = (UserProfileDetail) itr.next();
				}
				response.getWriter().print(str);
			} else {
				response.getWriter().print(false);
			}
			putProfileListInRequest(request);
			return null;

		} catch (SwtException swtexp) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [getProfileDetails] method : - "
					+ swtexp.getMessage());
			SwtUtil.logException(swtexp, request, "");
			return ("fail");
		} catch (Exception e) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [getProfileDetails] method : - "
					+ e.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					e, "getProfileDetails", UserProfilesAction.class), request,
					"");
			return ("fail");
		}
	}

	/**
	 * This function adds a record in the database or updates a record already
	 * present depending upon whether that record is present in the database
	 * already of not.
	 * 
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return
	 * @throws Exception
	 */
	public String update()
			throws Exception {
		try {

			log.debug("entering 'update' method");

// To remove: 			DynaValidatorForm dyForm = (DynaValidatorForm) form;

			UserProfile userprofiles = (UserProfile) getUserprofiles();

			String hostId = CacheManager.getInstance().getHostId();

			String profileId = userprofiles.getId().getProfileId();

			String userId = SwtUtil.getCurrentUserId(request.getSession(false));

			userprofiles.getId().setHostId(hostId);

			userprofiles.getId().setUserId(userId);

			String isCurrentProfile = userprofiles.getCurrentProfile();

			String isOverWriteProfile = userprofiles.getOverWriteFlag();

			if (isCurrentProfile != null) {
				if (isCurrentProfile.equalsIgnoreCase("on")
						|| isCurrentProfile.equalsIgnoreCase("Y")) {
					isCurrentProfile = "Y";
				}
			} else {
				isCurrentProfile = "N";
			}

			userprofiles.setCurrentProfile(isCurrentProfile);

			String windowsSetting = request.getParameter("wndsetting");

			Collection coll = parseProfileDetails(windowsSetting, profileId);

			List dataList = userprofilesManager.fetchDetails(hostId, userId,
					profileId);

			Iterator itr = dataList.iterator();
			if (dataList.size() > 0) {
				userprofilesManager
						.updateuserProfileDetails(userprofiles, coll);
			} else {
				userprofilesManager.saveuserProfileDetails(userprofiles, coll);
			}

			putProfileListInRequest(request);

			request.setAttribute("methodName", "update");

			request.setAttribute("closeWindow", "yes");

			log.debug("exiting update method");

			return ("add");
		} catch (SwtException swtexp) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [update] method : - "
					+ swtexp.getMessage());
			SwtUtil.logException(swtexp, request, "");
			return ("fail");
		} catch (Exception e) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [update] method : - "
					+ e.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					e, "update", UserProfilesAction.class), request, "");
			return ("fail");
		}

	}

	/**
	 * 
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return
	 * @throws Exception
	 */

	public String displayProfile()
			throws Exception {
		try {

			log.debug("entering 'displayProfile' method");

// To remove: 			DynaValidatorForm dyForm = (DynaValidatorForm) form;

			UserProfile userprofiles = (UserProfile) getUserprofiles();

			String hostId = CacheManager.getInstance().getHostId();

			String profileId = userprofiles.getId().getProfileId();

			String userId = SwtUtil.getCurrentUserId(request.getSession(false));

			userprofiles = new UserProfile();
			userprofiles.getId().setProfileId(profileId);

			List dataList = userprofilesManager.fetchDetails(hostId, userId,
					profileId);

			Iterator itr = dataList.iterator();
			if (dataList.size() > 0) {
				if (itr.hasNext()) {
					userprofiles = (UserProfile) itr.next();
				}
			}

			setUserprofiles(userprofiles);

			String windowsSetting = request.getParameter("wndsetting");

			request.setAttribute("wndsetting", windowsSetting);

			putProfileListInRequest(request);

			request.setAttribute("methodName", "update");

			log.debug("exiting displayProfile method");
			return ("add");
		} catch (SwtException swtexp) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [displayProfile] method : - "
					+ swtexp.getMessage());
			SwtUtil.logException(swtexp, request, "");
			return ("fail");
		} catch (Exception e) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [displayProfile] method : - "
					+ e.getMessage());
			SwtUtil
					.logException(SwtErrorHandler.getInstance()
							.handleException(e, "displayProfile",
									UserProfilesAction.class), request, "");
			return ("fail");
		}
	}

	/**
	 * This function puts the LabelValueBean of the profileId and profileName in
	 * the request. The element LabelValueBean("Select Profile","") is
	 * removed from this collection.
	 * 
	 * @param request
	 */
	private void putProfileListInRequest(HttpServletRequest request) {
		log.debug("Entering into the putProfileListInRequest() ");

		CommonDataManager CDM = (CommonDataManager) request.getSession()
				.getAttribute(SwtConstants.CDM_BEAN);

		Collection collProfile = CDM.getProfileList();

		Collection profileDropDown = new ArrayList();

		if (collProfile != null) {
			Iterator itr = collProfile.iterator();
			while (itr.hasNext()) {
				LabelValueBean lvb = (LabelValueBean) (itr.next());
				if (!(lvb.getValue().equalsIgnoreCase("")))
					profileDropDown.add(lvb);
			}

		}

		request.setAttribute("userProfileList", profileDropDown);

		log.debug("Exiting into the putProfileListInRequest() ");
	}

}