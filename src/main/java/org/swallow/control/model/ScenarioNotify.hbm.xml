<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE hibernate-mapping PUBLIC
        "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
        "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">

<hibernate-mapping>
    <class name="org.swallow.control.model.ScenarioNotify" table="P_NOTIFY_SCENARIO" >
			  <composite-id class="org.swallow.control.model.ScenarioNotify$Id" name="id" unsaved-value="any">
			   <key-property name="hostId" access="field" column="HOST_ID"/>
			   <key-property name="roleId" access="field" column="ROLE_ID"/>
			   <key-property name="entityId" access="field" column="ENTITY_ID"/>
			   <key-property name="scenarioId" access="field" column="SCENARIO_ID"/>
			  </composite-id>
		<property name="accessrequired" column="ACCESS_REQUIRED" not-null="false"/>
		<property name="deliverpopups" column="DELIVER_POPUPS" not-null="false"/>
		<property name="flashicon" column="FLASH_ICON" not-null="false"/>
		<property name="sendemail" column="SEND_EMAIL" not-null="false"/>
		<property name="emailusers" column="EMAIL_USERS" not-null="false"/>
		<property name="fullinstanceaccess" column="FULL_INSTANCE_ACCESS" not-null="false"/>
    </class>
</hibernate-mapping>
