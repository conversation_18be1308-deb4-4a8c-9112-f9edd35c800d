/*
 * @(#)AccountAccessDetails.java 1.0 02/01/2010
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.control.model;

import org.swallow.model.BaseObject;

/**
 * <AUTHOR>
 *  
 * Class contain setter,getter methods. These methods used to getting 
 * and setting the values.
 */
public class AccountAccessDetails extends BaseObject {
	//unnessary code Removed by Arumugam on 01-Nov-2010 for Mantis:0001281- Account Access Control screen takes long time to save
	private String accountId;
	private String accountName;
	private String accountCcy;
	private String accountType;
	private String accountLevel;
	private String accountClass;	
	private String allowManualInput;
	private String allowSweeping;
	private String allowMatching;
	private String entityId;
	private String roleId;
	private String hostId;
	
	private static final long serialVersionUID = 1L;
	
	/**
	 * Returns the RoleId.
	 * 
	 * @param none
	 * @return String
	 */	
	public String getRoleId() {
		return roleId;
	}
	
	/**
	 * The RoleId to set.
	 * 
	 * @param roleId
	 * @return noe
	 *            
	 */
	public void setRoleId(String roleId) {
		this.roleId = roleId;
	}
	
	/**
	 * Returns the AccountId.
	 * 
	 * @param none
	 * @return String
	 */
	public String getAccountId() {
		return accountId;
	}
	
	/**
	 * The AccountId to set.
	 * 
	 * @param accountId
	 * @return none
	 *            
	 */
	public void setAccountId(String accountId) {
		this.accountId = accountId;
	}
	
	/**
	 * Returns the AccountName.
	 * 
	 * @param none
	 * @return String
	 */
	public String getAccountName() {
		return accountName;
	}
	
	/**
	 * The AccountName to set.
	 * 
	 * @param accountName
	 * @return none
	 */
	public void setAccountName(String accountName) {
		this.accountName = accountName;
	}
	
	/**
	 * @return Returns the AccountCcy.
	 */
	public String getAccountCcy() {
		return accountCcy;
	}
	
	/**
	 * The AccountCcy to set.
	 * 
	 * @param accountCcy
	 * @return none
	 */
	public void setAccountCcy(String accountCcy) {
		this.accountCcy = accountCcy;
	}
	
	/**
	 * Returns the AccountType.
	 * 
	 * @param none
	 * @return String
	 */
	public String getAccountType() {
		return accountType;
	}
	
	/**
	 * The AccountType to set.
	 * 
	 * @param accountType
	 * @return none
	 *            
	 */
	public void setAccountType(String accountType) {
		this.accountType = accountType;
	}
	
	/**
	 * Returns the AccountLevel.
	 * 
	 * @param none
	 * @return String
	 */
	public String getAccountLevel() {
		return accountLevel;
	}
	
	/**
	 * The AccountLevel to set.
	 * 
	 * @param accountLevel
	 * @return none          
	 */
	public void setAccountLevel(String accountLevel) {
		this.accountLevel = accountLevel;
	}
	
	/**
	 * Returns the AccountClass.
	 * 
	 * @param none
	 * @return String
	 */
	public String getAccountClass() {
		return accountClass;
	}
	
	/**
	 * The AccountClass to set.
	 * 
	 * @param accountClass
	 * @param none
	 */
	public void setAccountClass(String accountClass) {
		this.accountClass = accountClass;
	}
	
	/**
	 * Returns the AllowManualInput.
	 * 
	 * @param none
	 * @return String
	 */
	public String getAllowManualInput() {
		return allowManualInput;
	}
	
	/**
	 * The AllowManualInput to set.
	 * 
	 * @param allowManualInput
	 * @return none        
	 */
	public void setAllowManualInput(String allowManualInput) {
		this.allowManualInput = allowManualInput;
	}
	
	/**
	 * Returns the AllowSweeping.
	 * 
	 * @param none
	 * @return String
	 */
	public String getAllowSweeping() {
		return allowSweeping;
	}
	
	/**
	 * The AllowSweeping to set.
	 * 
	 * @param allowSweeping
	 * @return none           
	 */
	public void setAllowSweeping(String allowSweeping) {
		this.allowSweeping = allowSweeping;
	}
	
	/**
	 * Returns the AllowMatching.
	 * 
	 * @param none
	 * @return String
	 */
	public String getAllowMatching() {
		return allowMatching;
	}
	
	/**
	 * The AllowMatching to set.
	 * 
	 * @param allowMatching
	 * @return none           
	 */
	public void setAllowMatching(String allowMatching) {
		this.allowMatching = allowMatching;
	}
	
	/**
	 * Returns the EntityId.
	 * 
	 * @param none
	 * @return String
	 */
	public String getEntityId() {
		return entityId;
	}
	
	/**
	 * The EntityId to set.
	 * 
	 * @param EntityId
	 * @return none         
	 */
	public void setEntityId(String entityId) {
		this.entityId = entityId;
	}

	public String getHostId() {
		return hostId;
	}

	public void setHostId(String hostId) {
		this.hostId = hostId;
	}
	}
