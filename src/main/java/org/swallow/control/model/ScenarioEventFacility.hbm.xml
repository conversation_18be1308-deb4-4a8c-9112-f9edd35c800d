<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE hibernate-mapping PUBLIC
        "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
        "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">

<hibernate-mapping>
    <class name="org.swallow.control.model.ScenarioEventFacility" table="P_SCENARIO_EVENT_FACILITY" >
		<composite-id class="org.swallow.control.model.ScenarioEventFacility$Id" name="id" unsaved-value="any">
		   <key-property name="eventFacilityId" access="field" column="ID"/>
		</composite-id>
			
		<property name="description" column="DESCRIPTION" not-null="true"/>
		<property name="programId" column="PROGRAM_ID" not-null="false"/>
		<property name="parameterFacility" column="PARAMETER_MAINT_FACTILITY" not-null="false"/>
		<property name="requiredParameters" column="REQUIRED_PARAMETERS" not-null="false"/>
			
	</class>
</hibernate-mapping>
