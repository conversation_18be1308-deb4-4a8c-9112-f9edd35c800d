/*
 * @(#)ErrorLog.java 23/12/05
 * 
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 * 
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */

package org.swallow.control.model;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import org.swallow.model.BaseObject;


public class ErrorLog extends BaseObject{

	/**
	 * @param fromDateAsString The fromDateAsString to set.
	 */
	public void setFromDateAsString(String fromDateAsString) {
		this.fromDateAsString = fromDateAsString;
	}
	/**
	 * @param toDateAsString The toDateAsString to set.
	 */
	public void setToDateAsString(String toDateAsString) {
		this.toDateAsString = toDateAsString;
	}
	private Date fromDate;
	private Date toDate;
	private String fromDateAsString;
	private String toDateAsString;

	private String errorDate_Date;
	private String errorDate_Time;
	private String error;
	private String hostId ;
	private Date errorDate;
	private String userId;
	private String ipAddress;
	private String source;
	private String errorId;
	private String errorDesc;
	private Long 	errSeq;
	private int noOfErrors;
	private String dropDownUserId;
	private String fromTime;
	private String toTime;


	public Long getErrSeq() {
		return errSeq;
	}

	public void setErrSeq(Long errSeq) {
		this.errSeq = errSeq;
	}

	public String getHostId() {
		return hostId;
	}

	public void setHostId(String hostId) {
		this.hostId = hostId;
	}

	public String getErrorId() {
		return errorId;
	}

	public void setErrorId(String errorId) {
		this.errorId = errorId;
	}

	public String getErrorDesc() {
		return errorDesc;
	}

	public void setErrorDesc(String errorDesc) {
		this.errorDesc = errorDesc;
	}

	public Date getErrorDate() {
		return errorDate;
	}

	public void setErrorDate(Date errorDate) {
		this.errorDate = errorDate;
	}

	public Date getFromDate() {
		return fromDate;
	}

	public void setFromDate(Date fromDate) {
		this.fromDate = fromDate;
	}

	public String getFromDateAsString() {
		return fromDateAsString;
	}





	public void setErrorDate_Date(String errorDate_Date) {
		this.errorDate_Date = errorDate_Date;
	}

	public String getErrorDate_Time() {
		SimpleDateFormat  sdf = new SimpleDateFormat("HH:mm:ss");
		return sdf.format(getErrorDate());
		}

	public void setErrorDate_Time(String errorDate_Time) {
		this.errorDate_Time = errorDate_Time;
	}

	public String getIpAddress() {
		return ipAddress;
	}

	public void setIpAddress(String ipAddress) {
		this.ipAddress = ipAddress;
	}

	public String getSource() {
		return source;
	}

	public void setSource(String source) {
		this.source = source;
	}

	public String getError() {
		error = getErrorId() + getErrorDesc();
		return error;
	}

	public void setError(String error) {
		this.error = error;
	}

	public Date getToDate() {
		return toDate;
	}

	public void setToDate(Date toDate) {
		this.toDate = toDate;
	}

	public String getToDateAsString() {
		return toDateAsString;
	}



	public String getUserId() {
		return userId;
	}

	public void setUserId(String userId) {
		this.userId = userId;
	}


	/**
	 * @return Returns the errorDate_Date.
	 */
	public String getErrorDate_Date() {
		return errorDate_Date;
	}
	/**
	 * @return Returns the noOfErrors.
	 */
	public int getNoOfErrors() {
		return noOfErrors;
	}
	/**
	 * @param noOfErrors The noOfErrors to set.
	 */
	public void setNoOfErrors(int noOfErrors) {
		this.noOfErrors = noOfErrors;
	}
	/**
	 * @return Returns the dropDownUserId.
	 */
	public String getDropDownUserId() {
		return dropDownUserId;
	}
	/**
	 * @param dropDownUserId The dropDownUserId to set.
	 */
	public void setDropDownUserId(String dropDownUserId) {
		this.dropDownUserId = dropDownUserId;
	}
	/**
	 * @return Returns the fromTime.
	 */
	public String getFromTime() {
		return fromTime;
	}
	/**
	 * @param fromTime The fromTime to set.
	 */
	public void setFromTime(String fromTime) {
		this.fromTime = fromTime;
	}
	/**
	 * @return Returns the toTime.
	 */
	public String getToTime() {
		return toTime;
	}
	/**
	 * @param toTime The toTime to set.
	 */
	public void setToTime(String toTime) {
		this.toTime = toTime;
	}
}
