<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE hibernate-mapping PUBLIC
        "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
        "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">

<hibernate-mapping>
 <class name="org.swallow.control.model.MaintenanceLogView" table="VW_S_MAINTENANCE_LOG">
        

 <composite-id class="org.swallow.control.model.MaintenanceLogView$Id"
   name="id" unsaved-value="any">

  
  <key-property name="sequence" access="field" column="Main_Sequence"/>
  <key-property name="logDate" access="field" column="LOG_DATE"/>
  <key-property name="userId" access="field" column="USER_ID"/>
  <key-property name="ipAddress" access="field" column="IP_ADDRESS"/>
	<key-property name="tableName" access="field" column="TABLE_NAME"/>	
  <key-property name="reference" access="field" column="REFERENCE"/>
  
	
  <key-property name="action" access="field" column="ACTION"/>

  
  </composite-id>

 </class>
</hibernate-mapping>