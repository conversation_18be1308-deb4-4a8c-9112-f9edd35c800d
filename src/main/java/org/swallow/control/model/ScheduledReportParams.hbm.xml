<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE hibernate-mapping PUBLIC
        "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
        "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">

<hibernate-mapping>
    <class name="org.swallow.control.model.ScheduledReportParams" table="S_SCHEDULED_REPORT_PARAMETERS">
		<id name="scheduleId" column="SCHEDULE_ID">
			<generator class="assigned" />
		</id>
	
		<property name="hostId" column="HOST_ID" not-null="true"/>		
		<property name="jobId" column="JOB_ID" not-null="true"/>		
		<property name="reportTypeId" column="REPORT_TYPE_ID" not-null="false"/>		
		<property name="reportName" column="REPORT_NAME" not-null="false"/>
		<property name="reportDesc" column="REPORT_DESC" not-null="false"/>
		<property name="executionRole" column="EXECUTION_ROLE" not-null="false"/>
		<property name="mapDateMethod" column="MAP_DATE_METHOD" not-null="false"/>
		<property name="outputFormat" column="OUTPUT_FORMAT" not-null="false"/>
		<property name="outputLocation" column="OUTPUT_LOCATION" not-null="false"/>
		<property name="fileNamePrefix" column="FILE_NAME_PREFIX" not-null="false"/>
		<property name="reportConfig" column="REPORT_CONFIG" not-null="false"/>
		<property name="accessListRoles" column="ACCESS_LIST_ROLES" not-null="false"/>
		<property name="accessListUsers" column="ACCESS_LIST_USERS" not-null="false"/>
		<property name="emailDistListRoles" column="EMAIL_DIST_LIST_ROLES" not-null="false"/>
		<property name="emailDistListUsers" column="EMAIL_DIST_LIST_USERS" not-null="false"/>
		<property name="retainDays" column="RETAIN_DAYS" not-null="false"/>

    </class>
</hibernate-mapping>