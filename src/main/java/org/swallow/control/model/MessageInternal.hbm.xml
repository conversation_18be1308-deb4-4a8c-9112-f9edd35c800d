<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE hibernate-mapping PUBLIC
        "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
        "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">

<hibernate-mapping>
    <class name="org.swallow.control.model.MessageInternal" table="P_MESSAGE_INTERNAL">
		
		
		<id name="messageId" type="long" column="MESSAGE_ID">
			<generator class="sequence">
				<param name="sequence_name">P_MESSAGE_INTERNAL_SEQUENCE</param>
			 </generator>
		</id>	
        	
		<property name="messageText" column="MESSAGE_TEXT" />	
		<property name="userRoleFlag" column="USER_ROLE_FLAG" />	
		<property name="updateDate" column="UPDATE_DATE" not-null="false"/>	
		<property name="updateUser" column="UPDATE_USER" not-null="false"/>	
		

    </class>
</hibernate-mapping>
