/*
 * Created on Jan 4, 2006
 *
 * TODO To change the template for this generated file go to
 * Window - Preferences - Java - Code Style - Code Templates
 */
package org.swallow.control.model;
import org.swallow.control.model.Section.Id;
import org.swallow.model.BaseObject;
import org.swallow.util.*;
import java.util.*;
/**
 * <AUTHOR>
 *
 * TODO To change the template for this generated type comment go to
 * Window - Preferences - Java - Code Style - Code Templates
 */
public class MenuAccessOptionsGui extends BaseObject{
	private String programId;
	private String parentId;
	/**
	 * @return Returns the parentId.
	 */
	public String getParentId() {
		return parentId;
	}
	/**
	 * @param parentId The parentId to set.
	 */
	public void setParentId(String parentId) {
		this.parentId = parentId;
	}
	private String itemId;
	private String description;
	/* START: Code changed as par ING requirements to the Roles - Menu Access, 11-JUN-2007 */
	private String menuAccessHTMLFullAccess;
	private String menuAccessHTMLViewAccess;
	private String menuAccessHTMLNoAccess;
	/* END: Code changed as par ING requirements to the Roles - Menu Access, 11-JUN-2007 */
	private String descriptionLevel1;
	private String descriptionLevel2;
	private String descriptionLevel3;








	/**
	 * @return Returns the programId.
	 */
	public String getProgramId() {
		return programId;
	}
	/**
	 * @param programId The programId to set.
	 */
	public void setProgramId(String programId) {
		this.programId = programId;
	}
	/**
	 * @return Returns the descriptionLevel1.
	 */
	public String getDescriptionLevel1() {
		return descriptionLevel1;
	}
	/**
	 * @param descriptionLevel1 The descriptionLevel1 to set.
	 */
	public void setDescriptionLevel1(String descriptionLevel1) {
		this.descriptionLevel1 = descriptionLevel1;
	}
	/**
	 * @return Returns the descriptionLevel2.
	 */
	public String getDescriptionLevel2() {
		return descriptionLevel2;
	}
	/**
	 * @param descriptionLevel2 The descriptionLevel2 to set.
	 */
	public void setDescriptionLevel2(String descriptionLevel2) {
		this.descriptionLevel2 = descriptionLevel2;
	}
	/**
	 * @return Returns the descriptionLevel3.
	 */
	public String getDescriptionLevel3() {
		return descriptionLevel3;
	}
	/**
	 * @param descriptionLevel3 The descriptionLevel3 to set.
	 */
	public void setDescriptionLevel3(String descriptionLevel3) {
		this.descriptionLevel3 = descriptionLevel3;
	}

	/**
	 * @return Returns the description.
	 */
	public String getDescription() {
		return description;
	}
	/**
	 * @param description The description to set.
	 */
	public void setDescription(String description) {
		this.description = description;
	}
	/**
	 * @return Returns the itemId.
	 */
	public String getItemId() {
		return itemId;
	}
	/**
	 * @param itemId The itemId to set.
	 */
	public void setItemId(String itemId) {
		this.itemId = itemId;
	}

	/* START: Code changed as par ING requirements to the Roles - Menu Access, 11-JUN-2007 */
	/**
	 * @return Returns the menuAccessHTMLFullAccess.
	 */
	public String getMenuAccessHTMLFullAccess() {
		return menuAccessHTMLFullAccess;
	}
	/**
	 * @param menuAccessHTMLFullAccess The menuAccessHTMLFullAccess to set.
	 */
	public void setMenuAccessHTMLFullAccess(String menuAccessHTMLFullAccess) {
		this.menuAccessHTMLFullAccess = menuAccessHTMLFullAccess;
	}
	/**
	 * @return Returns the menuAccessHTMLNoAccess.
	 */
	public String getMenuAccessHTMLNoAccess() {
		return menuAccessHTMLNoAccess;
	}
	/**
	 * @param menuAccessHTMLNoAccess The menuAccessHTMLNoAccess to set.
	 */
	public void setMenuAccessHTMLNoAccess(String menuAccessHTMLNoAccess) {
		this.menuAccessHTMLNoAccess = menuAccessHTMLNoAccess;
	}
	/**
	 * @return Returns the menuAccessHTMLViewAccess.
	 */
	public String getMenuAccessHTMLViewAccess() {
		return menuAccessHTMLViewAccess;
	}
	/**
	 * @param menuAccessHTMLViewAccess The menuAccessHTMLViewAccess to set.
	 */
	public void setMenuAccessHTMLViewAccess(String menuAccessHTMLViewAccess) {
		this.menuAccessHTMLViewAccess = menuAccessHTMLViewAccess;
	}
	/* END: Code changed as par ING requirements to the Roles - Menu Access, 11-JUN-2007 */
}


