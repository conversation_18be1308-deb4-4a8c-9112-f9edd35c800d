/*
 * @(#)SystemLog.java  16/12/05
 * 
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 * 
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */

package org.swallow.control.model;

import java.text.SimpleDateFormat;
import java.util.Date;
import org.swallow.model.BaseObject;


public class SystemLog extends BaseObject {

	private Date fromDate;
	/**
	 * @return Returns the logDate_Date.
	 */
	public String getLogDate_Date() {
		return logDate_Date;
	}
	/**
	 * @param fromDateAsString The fromDateAsString to set.
	 */
	public void setFromDateAsString(String fromDateAsString) {
		this.fromDateAsString = fromDateAsString;
	}
	/**
	 * @param toDateAsString The toDateAsString to set.
	 */
	public void setToDateAsString(String toDateAsString) {
		this.toDateAsString = toDateAsString;
	}
	private Date toDate;
	private String fromDateAsString;
	private String toDateAsString;
	private String logDate_Date;
	private String logDate_Time;
	
	private String hostId ;
	private Date logDate;
	private String userId;
	private String ipAddress;
	private String process;
	private String action;
	private Date updateDate;
	private String updateUser;
	//private Id id = new Id();
	private Long systemSeqNo;
	
	
	/**
	 * @return Returns the logDate.
	 */
	public Date getLogDate() {
		return logDate;
	}
	/**
	 * @param logDate The logDate to set.
	 */
	public void setLogDate(Date logDate) {
		this.logDate = logDate;
	}
	

	
	/**
	 * @return Returns the usr.
	 */
	public String getUserId() {
		return userId;
	}
	/**
	 * @param usr The usr to set.
	 */
	public void setUserId(String userId) {
		this.userId = userId;
	}


	
	/**
	 * @return Returns the errDate_Date.
	 */

	/**
	 * @param errDate_Date The errDate_Date to set.
	 */
	public void setLogDate_Date(String logDate_Date) {
		this.logDate_Date = logDate_Date;
	}
	/**
	 * @return Returns the logDate_Time.
	 */
	public String getLogDate_Time() {
		SimpleDateFormat  sdf = new SimpleDateFormat("HH:mm:ss");
		return sdf.format(getLogDate());
		}
	/**
	 * @param logDate_Time The logDate_Time to set.
	 */
	public void setLogDate_Time(String logDate_Time) {
		this.logDate_Time = logDate_Time;
	}
	
	/**
	 * @return Returns the action.
	 */
	public String getAction() {
		return action;
	}
	/**
	 * @param action The action to set.
	 */
	public void setAction(String action) {
		this.action = action;
	}
	/**
	 * @return Returns the ipAddress.
	 */
	public String getIpAddress() {
		return ipAddress;
	}
	/**
	 * @param ipAddress The ipAddress to set.
	 */
	public void setIpAddress(String ipAddress) {
		this.ipAddress = ipAddress;
	}
	/**
	 * @return Returns the process.
	 */
	public String getProcess() {
		return process;
	}
	/**
	 * @param process The process to set.
	 */
	public void setProcess(String process) {
		this.process = process;
	}
	/**
	 * @return Returns the updateDate.
	 */
	public Date getUpdateDate() {
		return updateDate;
	}
	/**
	 * @param updateDate The updateDate to set.
	 */
	public void setUpdateDate(Date updateDate) {
		this.updateDate = updateDate;
	}
	/**
	 * @return Returns the updateUser.
	 */
	public String getUpdateUser() {
		return updateUser;
	}
	/**
	 * @param updateUser The updateUser to set.
	 */
	public void setUpdateUser(String updateUser) {
		this.updateUser = updateUser;
	}
//	/**
//	 * @return Returns the id.
//	 */
//	public Id getId() {
//		return id;
//	}
//	/**
//	 * @param id The id to set.
//	 */
//	public void setId(Id id) {
//		this.id = id;
//	}
//
//	public static class Id extends BaseObject{
//		private Integer systemSeqNo;
//		public Id() {}
//		public Id(Integer systemSeqNo) {
//			this.systemSeqNo = systemSeqNo;
//		}
//		/**
//		 * @return Returns the systemSeqNo.
//		 */
//		public Integer getSystemSeqNo() {
//			return systemSeqNo;
//		}
//		/**
//		 * @param systemSeqNo The systemSeqNo to set.
//		 */
//		public void setSystemSeqNo(Integer systemSeqNo) {
//			this.systemSeqNo = systemSeqNo;
//		}
//	}


	/**
	 * @return Returns the fromDate.
	 */
	public Date getFromDate() {
		return fromDate;
	}
	/**
	 * @param fromDate The fromDate to set.
	 */
	public void setFromDate(Date fromDate) {
		this.fromDate = fromDate;
	}
	/**
	 * @return Returns the toDate.
	 */
	public Date getToDate() {
		return toDate;
	}
	/**
	 * @param toDate The toDate to set.
	 */
	public void setToDate(Date toDate) {
		this.toDate = toDate;
	}
	/**
	 * @return Returns the fromDateAsString.
	 */
	public String getFromDateAsString() {
		return fromDateAsString;
	}
	

	
	/**
	 * @return Returns the toDateAsString.
	 */
	public String getToDateAsString() {
		return toDateAsString;
	}
	


	/**
	 * @return Returns the hostId.
	 */
	public String getHostId() {
		return hostId;
	}
	/**
	 * @param hostId The hostId to set.
	 */
	public void setHostId(String hostId) {
		this.hostId = hostId;
	}
	/**
	 * @return Returns the systemSeqNo.
	 */
	public Long getSystemSeqNo() {
		return systemSeqNo;
	}
	/**
	 * @param systemSeqNo The systemSeqNo to set.
	 */
	public void setSystemSeqNo(Long systemSeqNo) {
		this.systemSeqNo = systemSeqNo;
	}
}
