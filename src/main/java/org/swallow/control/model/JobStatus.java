/*
 * Created on Jan 27, 2006
 *
 * TODO To change the template for this generated file go to
 * Window - Preferences - Java - Code Style - Code Templates
 */
package org.swallow.control.model;

import java.util.Date;


import org.swallow.model.BaseObject;
import org.swallow.util.SwtConstants;
import org.swallow.control.model.Job;

/**
 * <AUTHOR>
 *
 * TODO To change the template for this generated type comment go to
 * Window - Preferences - Java - Code Style - Code Templates
 */
public class JobStatus extends BaseObject {
	
	private Integer scheduleId;
	private String hostId;							// HOST_ID 
	private String jobId;							// JOB_ID 	
	private Date lastExecTime;						// LAST_EXECUTION_TIME
	private String lastExecTimeAsString;						
	private String lastExecStatus;					// LAST_EXECUTION_STATUS
	private String lastExecStatusName;		
	private Date nextExecTime;						// NEXT_EXECUTION_TIME
	private String nextExecTimeAsString;						
	private String currentStatus;					// CURRENT_STATUS
	private String currentStatusName;						
	private Job job;								// Job Class
	private Date updateDate = new Date();			// UPDATE_DATE
	private String updateUser;// UPDATE_USER
	// Actually is named as Frequency in screens and will have values as Once, Cyclic, Daily, Weekly, Monthly or Manual
	private String jobType;
	private String jobTypeFullDescription;
	private String jobStatus;
	
	private String jobDetails;
	private Date scheduleDate;
	private String scheduleDateAsString;
	private String scheduleTime;
	private Integer durationHrs;
	private Integer durationMins;
	private Integer durationSecs;
	private String scheduleDay;
	private String monthFirst;
	private String monthLast;
	private Integer monthDate;
	// Actually is named as job type in screens and will have values as Report or Process
	private String jobTypeProcessOrReport;
	// will be set in case when we have a report type job
	private String reportName;
	
	/**
	 * @return Returns the jobStatus.
	 */
	public String getJobStatus() {
		return jobStatus;
	}
	/**
	 * @param jobStatus The jobStatus to set.
	 */
	public void setJobStatus(String jobStatus) {
		this.jobStatus = jobStatus;
	}
	/**
	 * @return Returns the jobTypeFullDescription.
	 */
	public String getJobTypeFullDescription() {
		 if(jobType != null)
		 {
		 	String jobTyp = getJobType();
			if(jobTyp.equals(SwtConstants.JOB_TYPE_ONCE))
					jobTypeFullDescription = SwtConstants.JOB_TYPE_ONCE_DESC;
			else if(jobTyp.equals(SwtConstants.JOB_TYPE_CYLIC))
					jobTypeFullDescription = SwtConstants.JOB_TYPE_CYLIC_DESC;
			else if(jobTyp.equals(SwtConstants.JOB_TYPE_DAILY))
					jobTypeFullDescription = SwtConstants.JOB_TYPE_DAILY_DESC;
			else if(jobTyp.equals(SwtConstants.JOB_TYPE_MONTHLY))
					jobTypeFullDescription = SwtConstants.JOB_TYPE_MONTHLY_DESC;
			else if(jobTyp.equals(SwtConstants.JOB_TYPE_WEEKLY))
					jobTypeFullDescription = SwtConstants.JOB_TYPE_WEEKLY_DESC;	
			else if(jobTyp.equals(SwtConstants.JOB_TYPE_MANUAL))
				jobTypeFullDescription = SwtConstants.JOB_TYPE_MANUAL_DESC;
		 }
		return jobTypeFullDescription;
	}
	/**
	 * @param jobTypeFullDescription The jobTypeFullDescription to set.
	 */
	public void setJobTypeFullDescription(String jobTypeFullDescription) {
		this.jobTypeFullDescription = jobTypeFullDescription;
				//	this.jobTypeFullDescription = SwtConstants.JOB_TYPE_WEEKLY_DESC;			
			
	}
	/**
	 * @return Returns the jobType.
	 */
	public String getJobType() {
		return jobType;
	}
	/**
	 * @param jobType The jobType to set.
	 */
	public void setJobType(String jobType) {
		this.jobType = jobType;
	}
	
	public String getLastExecTimeAsString() {
		return lastExecTimeAsString;
	}
	public void setLastExecTimeAsString(String lastExecTimeAsString) {
		this.lastExecTimeAsString = lastExecTimeAsString;
	}
	public String getNextExecTimeAsString() {
		return nextExecTimeAsString;
	}
	public void setNextExecTimeAsString(String nextExecTimeAsString) {
		this.nextExecTimeAsString = nextExecTimeAsString;
	}
	
	/**
	 * @return Returns the currentStatusName.
	 */
	public String getCurrentStatusName() {
		return currentStatusName;
	}
	/**
	 * @param currentStatusName The currentStatusName to set.
	 */
	public void setCurrentStatusName(String currentStatusName) {
		this.currentStatusName = currentStatusName;
	}
	/**
	 * @return Returns the lastExecStatusName.
	 */
	public String getLastExecStatusName() {
		return lastExecStatusName;
	}
	/**
	 * @param lastExecStatusName The lastExecStatusName to set.
	 */
	public void setLastExecStatusName(String lastExecStatusName) {
		this.lastExecStatusName = lastExecStatusName;
	}
	
	/**
	 * @return Returns the currentStatus.
	 */
	public String getCurrentStatus() {
		return currentStatus;
	}
	/**
	 * @param currentStatus The currentStatus to set.
	 */
	public void setCurrentStatus(String currentStatus) {
		this.currentStatus = currentStatus;
	}
	/**
	 * @return Returns the lastExecStatus.
	 */
	public String getLastExecStatus() {
		return lastExecStatus;
	}
	/**
	 * @param lastExecStatus The lastExecStatus to set.
	 */
	public void setLastExecStatus(String lastExecStatus) {
		this.lastExecStatus = lastExecStatus;
	}
	/**
	 * @return Returns the lastExecTime.
	 */
	public Date getLastExecTime() {
		return lastExecTime;
	}
	/**
	 * @param lastExecTime The lastExecTime to set.
	 */
	public void setLastExecTime(Date lastExecTime) {
		this.lastExecTime = lastExecTime;
	}
	/**
	 * @return Returns the nextExecTime.
	 */
	public Date getNextExecTime() {
		return nextExecTime;
	}
	/**
	 * @param nextExecTime The nextExecTime to set.
	 */
	public void setNextExecTime(Date nextExecTime) {
		this.nextExecTime = nextExecTime;
	}
	/**
	 * @return Returns the updateDate.
	 */
	public Date getUpdateDate() {
		return updateDate;
	}
	/**
	 * @param updateDate The updateDate to set.
	 */
	public void setUpdateDate(Date updateDate) {
		this.updateDate = updateDate;
	}
	/**
	 * @return Returns the updateUser.
	 */
	public String getUpdateUser() {
		return updateUser;
	}
	/**
	 * @param updateUser The updateUser to set.
	 */
	public void setUpdateUser(String updateUser) {
		this.updateUser = updateUser;
	}
	
	/**
	 * @return Returns the job.
	 */
	public Job getJob() {
		return job;
	}
	/**
	 * @param job The job to set.
	 */
	public void setJob(Job job) {
		this.job = job;
	}
	/**
	 * @return Returns the durationHrs.
	 */
	public Integer getDurationHrs() {
		return durationHrs;
	}
	/**
	 * @param durationHrs The durationHrs to set.
	 */
	public void setDurationHrs(Integer durationHrs) {
		this.durationHrs = durationHrs;
	}
	/**
	 * @return Returns the durationMins.
	 */
	public Integer getDurationMins() {
		return durationMins;
	}
	/**
	 * @param durationMins The durationMins to set.
	 */
	public void setDurationMins(Integer durationMins) {
		this.durationMins = durationMins;
	}
	/**
	 * @return Returns the durationSecs.
	 */
	public Integer getDurationSecs() {
		return durationSecs;
	}
	/**
	 * @param durationSecs The durationSecs to set.
	 */
	public void setDurationSecs(Integer durationSecs) {
		this.durationSecs = durationSecs;
	}
	/**
	 * @return Returns the jobDetails.
	 */
	public String getJobDetails() {
		return jobDetails;
	}
	/**
	 * @param jobDetails The jobDetails to set.
	 */
	public void setJobDetails(String jobDetails) {
		this.jobDetails = jobDetails;
	}
	/**
	 * @return Returns the monthDate.
	 */
	public Integer getMonthDate() {
		return monthDate;
	}
	/**
	 * @param monthDate The monthDate to set.
	 */
	public void setMonthDate(Integer monthDate) {
		this.monthDate = monthDate;
	}
	/**
	 * @return Returns the monthFirst.
	 */
	public String getMonthFirst() {
		return monthFirst;
	}
	/**
	 * @param monthFirst The monthFirst to set.
	 */
	public void setMonthFirst(String monthFirst) {
		this.monthFirst = monthFirst;
	}
	/**
	 * @return Returns the monthLast.
	 */
	public String getMonthLast() {
		return monthLast;
	}
	/**
	 * @param monthLast The monthLast to set.
	 */
	public void setMonthLast(String monthLast) {
		this.monthLast = monthLast;
	}
	/**
	 * @return Returns the scheduleDate.
	 */
	public Date getScheduleDate() {
		return scheduleDate;
	}
	/**
	 * @param scheduleDate The scheduleDate to set.
	 */
	public void setScheduleDate(Date scheduleDate) {
		this.scheduleDate = scheduleDate;
	}
	/**
	 * @return Returns the scheduleDay.
	 */
	public String getScheduleDay() {
		return scheduleDay;
	}
	/**
	 * @param scheduleDay The scheduleDay to set.
	 */
	public void setScheduleDay(String scheduleDay) {
		this.scheduleDay = scheduleDay;
	}
	/**
	 * @return Returns the scheduleTime.
	 */
	public String getScheduleTime() {
		return scheduleTime;
	}
	/**
	 * @param scheduleTime The scheduleTime to set.
	 */
	public void setScheduleTime(String scheduleTime) {
		this.scheduleTime = scheduleTime;
	}
	/**
	 * @return Returns the scheduleDateAsString.
	 */
	public String getScheduleDateAsString() {
		return scheduleDateAsString;
	}
	/**
	 * @param scheduleDateAsString The scheduleDateAsString to set.
	 */
	public void setScheduleDateAsString(String scheduleDateAsString) {
		this.scheduleDateAsString = scheduleDateAsString;
	}
	/**
	 * @return the scheduleId
	 */
	public Integer getScheduleId() {
		return scheduleId;
	}
	/**
	 * @param scheduleId the scheduleId to set
	 */
	public void setScheduleId(Integer scheduleId) {
		this.scheduleId = scheduleId;
	}
	/**
	 * @return the jobTypeProcessOrReport
	 */
	public String getJobTypeProcessOrReport() {
		return jobTypeProcessOrReport;
	}
	/**
	 * @param jobTypeProcessOrReport the jobTypeProcessOrReport to set
	 */
	public void setJobTypeProcessOrReport(String jobTypeProcessOrReport) {
		this.jobTypeProcessOrReport = jobTypeProcessOrReport;
	}
	/**
	 * @return Returns the hostId.
	 */
	public String getHostId() {
		return hostId;
	}
	/**
	 * @param hostId The hostId to set.
	 */
	public void setHostId(String hostId) {
		this.hostId = hostId;
	}
	/**
	 * @return Returns the jobId.
	 */
	public String getJobId() {
		return jobId;
	}
	/**
	 * @param jobId The jobId to set.
	 */
	public void setJobId(String jobId) {
		this.jobId = jobId;
	}
	/**
	 * @return the reportName
	 */
	public String getReportName() {
		return reportName;
	}
	/**
	 * @param reportName the reportName to set
	 */
	public void setReportName(String reportName) {
		this.reportName = reportName;
	}
}
