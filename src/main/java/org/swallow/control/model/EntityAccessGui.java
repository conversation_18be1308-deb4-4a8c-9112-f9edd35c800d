/*
 * Created on Dec 29, 2005
 *
 * TODO To change the template for this generated file go to
 * Window - Preferences - Java - Code Style - Code Templates
 */
package org.swallow.control.model;

import org.swallow.maintenance.model.Entity;
import org.swallow.model.BaseObject;

/**
 * <AUTHOR>
 *
 * TODO To change the template for this generated type comment go to
 * Window - Preferences - Java - Code Style - Code Templates
 */
public class EntityAccessGui extends BaseObject{
	
	String hostId;
	String entityId;
	String entityName;
	String entityAccessHTML1;
	String entityAccessHTML2;
	String entityAccessHTML3;
	
	

	/**
	 * 
	 */
	public EntityAccessGui() {
		super();
		// TODO Auto-generated constructor stub
	}
	
	
	
	/**
	 * @param hostId
	 * @param entityId
	 * @param entityName
	 * @param entityAccessHTML1
	 * @param entityAccessHTML2
	 * @param entityAccessHTML3
	 */
	public EntityAccessGui(String hostId, String entityId, String entityName,
			String entityAccessHTML1, String entityAccessHTML2,
			String entityAccessHTML3) {
		super();
		this.hostId = hostId;
		this.entityId = entityId;
		this.entityName = entityName;
		this.entityAccessHTML1 = entityAccessHTML1;
		this.entityAccessHTML2 = entityAccessHTML2;
		this.entityAccessHTML3 = entityAccessHTML3;
	}
	
	
	/**
	 * @return Returns the entityAccessHTML2.
	 */
	public String getEntityAccessHTML2() {
		return entityAccessHTML2;
	}
	/**
	 * @param entityAccessHTML2 The entityAccessHTML2 to set.
	 */
	public void setEntityAccessHTML2(String entityAccessHTML2) {
		this.entityAccessHTML2 = entityAccessHTML2;
	}
	/**
	 * @return Returns the entityAccessHTML3.
	 */
	public String getEntityAccessHTML3() {
		return entityAccessHTML3;
	}
	/**
	 * @param entityAccessHTML3 The entityAccessHTML3 to set.
	 */
	public void setEntityAccessHTML3(String entityAccessHTML3) {
		this.entityAccessHTML3 = entityAccessHTML3;
	}
	/**
	 * @return Returns the entityId.
	 */
	public String getEntityId() {
		return entityId;
	}
	/**
	 * @param entityId The entityId to set.
	 */
	public void setEntityId(String entityId) {
		this.entityId = entityId;
	}
	/**
	 * @return Returns the entityName.
	 */
	public String getEntityName() {
		return entityName;
	}
	/**
	 * @param entityName The entityName to set.
	 */
	public void setEntityName(String entityName) {
		this.entityName = entityName;
	}
	/**
	 * @return Returns the hostId.
	 */
	public String getHostId() {
		return hostId;
	}
	/**
	 * @param hostId The hostId to set.
	 */
	public void setHostId(String hostId) {
		this.hostId = hostId;
	}
	
	/**
	 * @return Returns the entityAccessHTML1.
	 */
	public String getEntityAccessHTML1() {
		return entityAccessHTML1;
	}
	/**
	 * @param entityAccessHTML1 The entityAccessHTML1 to set.
	 */
	public void setEntityAccessHTML1(String entityAccessHTML1) {
		this.entityAccessHTML1 = entityAccessHTML1;
	}
}
