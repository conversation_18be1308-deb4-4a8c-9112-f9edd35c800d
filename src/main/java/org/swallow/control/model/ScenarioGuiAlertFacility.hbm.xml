<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE hibernate-mapping PUBLIC
        "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
        "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">

<hibernate-mapping>
    <class name="org.swallow.control.model.ScenarioGuiAlertFacility" table="P_SCENARIO_GUI_ALERT_FACILITY" >
		<composite-id class="org.swallow.control.model.ScenarioGuiAlertFacility$Id" name="id" unsaved-value="any">
		   <key-property name="guiFacilityId" access="field" column="ID"/>
		</composite-id>
			
		<property name="description" column="DESCRIPTION" not-null="true"/>
		<property name="requiresScenarioInstance" column="REQUIRES_SCENARIO_INSTANCE" not-null="false"/>
		<property name="programId" column="PROGRAM_ID" not-null="false"/>
		<property name="requiredParameters" column="REQUIRED_PARAMETERS" not-null="false"/>
		<property name="otherIdType" column="OTHER_ID_TYPE" not-null="false"/>
	</class>
</hibernate-mapping>
