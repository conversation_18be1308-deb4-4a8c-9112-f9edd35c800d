/*
 * Created on Apr 2, 2007
 *
 * TODO To change the template for this generated file go to
 * Window - Preferences - Java - Code Style - Code Templates
 */
package org.swallow.control.model;

import java.text.SimpleDateFormat;
import java.util.Date;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.swallow.control.model.AuditLog.Id;
import org.swallow.model.BaseObject;

/**
 * <AUTHOR>
 *
 * TODO To change the template for this generated type comment go to
 * Window - Preferences - Java - Code Style - Code Templates
 */
public class MaintenanceLogView extends BaseObject implements Comparable{
	private final Log log = LogFactory.getLog(MaintenanceLogView.class);
	/**
	 * @return Returns the logDate_Date.
	 */
	public String getLogDate_Date() {
		return LogDate_Date;
	}
	/**
	 * @param logDate_Date The logDate_Date to set.
	 */
	public void setLogDate_Date(String logDate_Date) {
		LogDate_Date = logDate_Date;
	}
	/**
	 * @return Returns the logDate_Time.
	 */
	public String getLogDate_Time() {
		SimpleDateFormat  sdf = new SimpleDateFormat("HH:mm:ss");
		return sdf.format(getId().getLogDate());
	}
	/**
	 * @param logDate_Time The logDate_Time to set.
	 */
	public void setLogDate_Time(String logDate_Time) {
		LogDate_Time = logDate_Time;
	}
	public String getLogDateIso() {
		SimpleDateFormat  sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		return sdf.format(getId().getLogDate());
	}

	public void setLogDateIso(String logDateIso) {
		LogDateIso = logDateIso;
	}
	private String LogDate_Date;
	private String LogDate_Time;



	private String LogDateIso;
	private String columnName;
	/**
	 * @return Returns the columnName.
	 */
	public String getColumnName() {
		return columnName;
	}
	/**
	 * @param columnName The columnName to set.
	 */
	public void setColumnName(String columnName) {
		this.columnName = columnName;
	}
	/**
	 * @return Returns the mainSeqNo.
	 */
	public Long getMainSeqNo() {
		return mainSeqNo;
	}
	/**
	 * @param mainSeqNo The mainSeqNo to set.
	 */
	public void setMainSeqNo(Long mainSeqNo) {
		this.mainSeqNo = mainSeqNo;
	}
	private Long mainSeqNo;
	
	/*    A negative integer if this is less than obj 
    Zero if this and obj are equivalent 
    A positive integer if this is greater than obj
   */

    public int compareTo(Object obj)
    {
        log.debug("Calling compareTo");
        int retValue = 0;
        
        if(obj instanceof MaintenanceLogView)
        {
        	MaintenanceLogView logObj = (MaintenanceLogView)obj;
            if(this.getId().getLogDate().getTime() > logObj.getId().getLogDate().getTime())
                retValue = 1;
            else if(this.getId().getLogDate().getTime() < logObj.getId().getLogDate().getTime())
            	retValue = -1;
        }
        return retValue;
    }
    
    public static class Id extends BaseObject{
		private final Log log = LogFactory.getLog(AuditLog.class);
		
		public void setUserId(String userId) {
			this.userId = userId;
		}
		
		private Date logDate;
		private String userId;
		//Variable added for Sequence number
		private long sequence;
		private String ipAddress;
		private String reference;
		
		private String action;
		private String tableName;
		private String dupaction;
		
		
		public Id() {}

		public Id(Date logDate, String userId, String ipAddress, String reference, String action,  String tableName) {
			
			this.logDate = logDate;
			this.userId = userId;
			this.ipAddress = ipAddress;
			this.reference = reference;
			
			this.action = action;
			this.tableName = tableName;
			

		}

		/**
		 * @return Returns the action.
		 */
		public String getAction() {
			log.debug("action======>"+action);
//		 if(action!=null){
////		 	
////		 	
////				if(action.equalsIgnoreCase("d")){
////					action="Deleted"; 
////					 
////				}else if ((action.equalsIgnoreCase("c")) || (action.equalsIgnoreCase("u"))){
////					action="Changed"; 
////					
////				}else if (action.equalsIgnoreCase("I")){
////					action="Input";
////					
////				}else if (action.equalsIgnoreCase("a")){
////					action="Added";
////				}
////				else if (action.equalsIgnoreCase("m")){
////					action="Matched";
////				}
////				else if (action.equalsIgnoreCase("n")){
////					action="Unmatched";
////				}
//    	   }else 
//	     	{
//    		action = "";
//		    }
		
			return action;
		}
		/**
		 * @param action The action to set.
		 */
		public void setAction(String action) {
			
			this.action = action; 
		}
		public String getDupaction() {
			//log.debug("dupaction======>"+action);
			 if( action!=null){
					if( action.equalsIgnoreCase("d")){
					 
						dupaction="Deleted"; 
						 
					}else if (( action.equalsIgnoreCase("c")) || ( action.equalsIgnoreCase("u"))){
					 
						dupaction="Changed"; 
						 
					}else if (action.equalsIgnoreCase("i")){
						 
						dupaction="Added";
					}		 
			   }
			// //log.debug("dupaction==1111111111111111111111====>"+dupaction);
			return dupaction;
		}
		/**
		 * @param dupaction The dupaction to set.
		 */
		public void setDupaction(String dupaction) {
			setAction(dupaction);
			this.dupaction = dupaction;
		}
		/**
		 * @return Returns the ipAddress.
		 */
		public String getIpAddress() {
			return ipAddress;
		}
		/**
		 * @param ipAddress The ipAddress to set.
		 */
		public void setIpAddress(String ipAddress) {
			this.ipAddress = ipAddress;
		}
		/**
		 * @return Returns the log.
		 */
		public Log getLog() {
			return log;
		}
		/**
		 * @return Returns the logDate.
		 */
		public Date getLogDate() {
			return logDate;
		}
		/**
		 * @param logDate The logDate to set.
		 */
		public void setLogDate(Date logDate) {
			this.logDate = logDate;
		}
		
		/**
		 * @return Returns the reference.
		 */
		public String getReference() {
			return reference;
		}
		/**
		 * @param reference The reference to set.
		 */
		public void setReference(String reference) {
			this.reference = reference;
		}
	
		/**
		 * @return Returns the userId.
		 */
		public String getUserId() {
			return userId;
		}
		/**
		 * @param userId The userId to set.
		 */
		

		
		/**
		 * @return Returns the tableName.
		 */
		public String getTableName() {
			return tableName;
		}
		/**
		 * @param tableName The tableName to set.
		 */
		public void setTableName(String tableName) {
			this.tableName = tableName;
		}
		//Code Starts : Added for including the sequence number in Sorting on 26-Mar-2008
		/**
		 * @return Returns the Sequence number.
		 */
		public long getSequence() {
			return sequence;
		}
		/**
		 * @param sets the Sequence number.
		 */
		public void setSequence(long sequence) {
			this.sequence = sequence;
		}
		//Code End : Added for including the sequence number in Sorting on 26-Mar-2008
	}
    
    private Id id = new Id();
	/**
	 * @return Returns the id.
	 */
	public Id getId() {
		return id;
	}
	/**
	 * @param id The id to set.
	 */
	public void setId(Id id) {
		this.id = id;
	}
}
