package org.swallow.control.model;

import org.swallow.model.BaseObject;
import org.swallow.model.User.*;

public class UserAuthDetails extends BaseObject {
	
	private String csrfTokens = null;
	private String[] csrfTokenArray  = null;
	private Id id = new Id();
	
	
	/**
	 * @return Returns the id.
	 */
	public Id getId() {
		return id;
	}
	/**
	 * @param id The id to set.
	 */
	public void setId(Id id) {
		this.id = id;
	}
	
	
	public String getCsrfTokens() {
		return csrfTokens;
	}
	public void setCsrfTokens(String csrfTokens) {
		this.csrfTokens = csrfTokens;
		
		if(csrfTokens != null && csrfTokens.length()>0) {
			csrfTokenArray = csrfTokens.split(",");
		}
	}
	public String[] getCsrfTokenArray() {
		if(csrfTokenArray == null) {
			if(csrfTokens != null && csrfTokens.length()>0) {
				csrfTokenArray = csrfTokens.split(",");
			}
		}
		return csrfTokenArray;
	}
	public void setCsrfTokenArray(String[] csrfTokenArray) {
		this.csrfTokenArray = csrfTokenArray;
	}
	
}
