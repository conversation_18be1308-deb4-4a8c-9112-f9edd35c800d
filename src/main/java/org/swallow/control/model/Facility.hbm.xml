<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE hibernate-mapping PUBLIC
        "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
        "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">

<hibernate-mapping>
    <class name="org.swallow.control.model.Facility" table="P_FACILITY">
		<id name="facilityid" column="FACILITY_ID">
			<generator class="assigned" />
		</id>
	
		<property name="facilityname" column="FACILITY_NAME" not-null="false"/>		
		<property name="reftable" column="REFERENCE_TABLE" not-null="false"/>		
		<property name="refcolumns" column="REFERENCE_COLUMNS" not-null="false"/>		
		<property name="refparam" column="REFERENCE_PARAMETER" not-null="false"/>		
		<property name="programid" column="PROGRAM_ID" not-null="false"/>
		<property name="secmenuitemid" column="SEC_MENU_ITEM_ID" not-null="false"/>
		<property name="userselectable" column="USER_SELECTABLE" not-null="false"/>
		<property name="supportAllCurrency" column="SUPPORTS_ALL_CCY" not-null="false"/>
		<property name="supportAllEntity" column="SUPPORTS_ALL_ENTITY" not-null="false"/>
					
    </class>
</hibernate-mapping>
