/*
 * @(#)AccountAccess.java 1.0 02/01/2010
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.control.model;

import java.util.Date;

import org.swallow.model.BaseObject;

/**
 * 
 * <AUTHOR> 
 * 
 * Class contain setter,getter methods. These methods used to getting 
 * and setting the values 
 * 
 */
public class RoleBasedControl extends BaseObject{
	/* Global variable declaration */
	private String facilityDesc;
	private String facilityId;
	private String reqAuth;	
	private String reqOthers;
	
	

	public String getFacilityDesc() {
		return facilityDesc;
	}
	public void setFacilityDesc(String facilityDesc) {
		this.facilityDesc = facilityDesc;
	}
	public String getFacilityId() {
		return facilityId;
	}
	public void setFacilityId(String facilityId) {
		this.facilityId = facilityId;
	}
	public String getReqAuth() {
		return reqAuth;
	}
	public void setReqAuth(String reqAuth) {
		this.reqAuth = reqAuth;
	}
	public String getReqOthers() {
		return reqOthers;
	}
	public void setReqOthers(String reqOthers) {
		this.reqOthers = reqOthers;
	}
	
	
	
}
