/*
 * @(#)EntityRoleTO.java 1.0 06/07/03
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.control.model;

import java.io.Serializable;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

/**
 * This class is immutable and used as transfer class for Entity Id and Role Id.
 *
 * <AUTHOR> Systems
 * @version 1.0
 */
public class EntityRoleTO implements Serializable {
    /** Holds the Role Id */
    private String roleId = "";

    /** Holds the Entity Id */
    private String entityId = "";
    
    /** stores the instance of log object */
    private final Log log = LogFactory.getLog(EntityRoleTO.class);
    

    /**
     * Creates a new EntityRoleTO object.
     *
     * @param roleId Role Id
     * @param entityId Entity Id
     */
    public EntityRoleTO(String roleId, String entityId) {
        this.roleId = roleId;
        this.entityId = entityId;
    }

    /**
     * Returns the Role Id.
     *
     * @return Returns the Role Id.
     */
    public String getRoleId() {
        return roleId;
    }

    /**
     * Returns the Entity Id.
     *
     * @return Returns the Entity Id.
     */
    public String getEntityId() {
        return entityId;
    }

    /**
     * It overrides the equals function of Object class.
     *
     * @param obj Object to be compared
     *
     * @return true/false
     */
    public boolean equals(Object obj) {
    	log.debug("entering equals method");
        boolean retValue = false;

        if ((obj != null) && obj instanceof EntityRoleTO) {
        	log.debug("obj - " + obj);
            retValue = roleId.equals((( EntityRoleTO ) obj).getRoleId())
                && entityId.equals((( EntityRoleTO ) obj).getEntityId());
        }
        log.debug("retValue - " + retValue);
    	log.debug("exiting equals method");
        return retValue;
    }

    /**
     * It overrides the hashCode function of Object class.
     *
     * @return Hash Code
     */
    public int hashCode() {
        return roleId.hashCode() + entityId.hashCode();
    }

    /**
     * It overrides the toString function of Object class.
     *
     * @return String repersentation of object
     */
    public String toString() {
        StringBuffer buf = new StringBuffer();
        buf.append(roleId).append(",").append(entityId);

        return buf.toString();
    }
}
