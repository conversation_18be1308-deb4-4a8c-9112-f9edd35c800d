<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE hibernate-mapping PUBLIC
        "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
        "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">

<hibernate-mapping>

   <class name="org.swallow.control.model.FacilityAccess" table="P_FACILITY_ACCESS">

		<composite-id name="id" class="org.swallow.control.model.FacilityAccess$Id" unsaved-value="any">
			<key-property name="roleId" access="field" column="ROLE_ID" />
	        <key-property name="facilityId" access="field" column="FACILITY_ID"/>
		</composite-id>
		
        <property name="updateUser"  column="UPDATE_USER" not-null="false"/>
		<property name="reqAuth" column="REQ_AUTH" not-null="false"/>	
		<property name="authOthers" column="AUTH_OTHERS" not-null="false"/>	
		<property name="updateDate" column="UPDATE_DATE" not-null="false"/>	

    </class>
</hibernate-mapping>
