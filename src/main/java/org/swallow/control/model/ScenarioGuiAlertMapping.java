package org.swallow.control.model;
import org.swallow.model.BaseObject;

public class ScenarioGuiAlertMapping extends BaseObject  {
	
	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	private Id id = new Id();
	private String guiFacilityDescription;
	
	public Id getId() {
		return id;
	}

	public void setId(Id id) {
		this.id = id;
	}


	public String getGuiFacilityDescription() {
		return guiFacilityDescription;
	}

	public void setGuiFacilityDescription(String guiFacilityDescription) {
		this.guiFacilityDescription = guiFacilityDescription;
	}

	public static class Id extends BaseObject{

		 
		private String scenarioId;
		private String guiFacilityId;
		public Id() {
		}
		
		public Id(String scenarioId, String guiFacilityId) {
			 
			this.setScenarioId(scenarioId);
			this.setGuiFacilityId(guiFacilityId);
			 
			
		}

		/**
		 * @return the scenarioid
		 */
		public String getScenarioId() {
			return scenarioId;
		}

		/**
		 * @param scenarioid the ScenarioId to set
		 */
		public void setScenarioId(String scenarioId) {
			this.scenarioId = scenarioId;
		}

		public String getGuiFacilityId() {
			return guiFacilityId;
		}

		public void setGuiFacilityId(String guiFacilityId) {
			this.guiFacilityId = guiFacilityId;
		}

		

	}

}
