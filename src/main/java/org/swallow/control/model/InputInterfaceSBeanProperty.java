package org.swallow.control.model;

/*
 * @(#)InputInterfaceSBeanProperty.java / 1.0 / Jul 15, 2011
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
import org.swallow.model.BaseObject;

/**
 * 
 * 
 * This class is used to display input interface sbean property configuration
 * details.
 */
 /* Start: code added by <PERSON><PERSON><PERSON><PERSON> on 14-Jul-2011 for mantis 1446:GUI changes in Predict for Smart Input v6  */
public class InputInterfaceSBeanProperty extends BaseObject {
	//instantiate the variable to hold id
	private Id id = new Id();
	//variable to hold value
	private String value = null;
	//variable to hold inputInterface
	private InputInterface inputInterface =null;

	/**
	 * Getter method for id
	 * 
	 * @return id as Id
	 */

	public Id getId() {
		return id;
	}

	/**
	 * Setter method for id
	 * 
	 * @param id
	 */

	public void setId(Id id) {
		this.id = id;
	}

	/**
	 * Getter method for value
	 * 
	 * @return value as String
	 */

	public String getValue() {
		return value;
	}

	/**
	 * Setter method for value
	 * 
	 * @param value
	 */

	public void setValue(String value) {
		this.value = value;
	}

	/**
	 * Getter method for inputInterface
	 * 
	 * @return inputInterface as InputInterface
	 */

	public InputInterface getInputInterface() {
		return inputInterface;
	}

	/**
	 * Setter method for inputInterface
	 * 
	 * @param inputInterface
	 */

	public void setInputInterface(InputInterface inputInterface) {
		this.inputInterface = inputInterface;
	}

	public static class Id extends BaseObject {
		//variable to hold interfaceId
		private String interfaceId = null;
		//variable to hold beanId
		private String beanId = null;
		//variable to hold name
		private String name = null;
		//variable to hold mandatory
		private String mandatory = null;
		//variable to hold propertyType
		private String propertyType = null;
		/**
		 * Getter method for mandatory
		 * @return mandatory as String
		 */
		
		public String getMandatory() {
			return mandatory;
		}

		/**
		 * Setter method for mandatory
		 * @param mandatory 
		 */
		
		public void setMandatory(String mandatory) {
			this.mandatory = mandatory;
		}

		/**
		 * Getter method for propertyType
		 * @return propertyType as String
		 */
		
		public String getPropertyType() {
			return propertyType;
		}

		/**
		 * Setter method for propertyType
		 * @param propertyType 
		 */
		
		public void setPropertyType(String propertyType) {
			this.propertyType = propertyType;
		}

		/**
		 * @return the beanId
		 */
		public String getBeanId() {
			return beanId;
		}

		/**
		 * @param beanId
		 *            the beanId to set
		 */
		public void setBeanId(String beanId) {
			this.beanId = beanId;
		}

		/**
		 * @return the interfaceId
		 */
		public String getInterfaceId() {
			return interfaceId;
		}

		/**
		 * @param interfaceId
		 *            the interfaceId to set
		 */
		public void setInterfaceId(String interfaceId) {
			this.interfaceId = interfaceId;
		}

		/**
		 * @return the name
		 */
		public String getName() {
			return name;
		}

		/**
		 * @param name
		 *            the name to set
		 */
		public void setName(String name) {
			this.name = name;
		}
	}

}
/* End: code added by krishna on 14-Jul-2011 for mantis 1446:GUI changes in Predict for Smart Input v6  */