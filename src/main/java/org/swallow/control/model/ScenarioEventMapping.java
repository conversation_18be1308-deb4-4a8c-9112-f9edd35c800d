package org.swallow.control.model;

import java.util.Hashtable;

import org.swallow.model.BaseObject;

public class ScenarioEventMapping  extends BaseObject implements org.swallow.model.AuditComponent {
	
	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	private Long 	mapKey;
	private String scenarioId;
	private String eventFacilityId;
	private int ordinal;
	private String executeWhen;
	private String parametersXml;
	private String repeatOnReraise;
	private String eventFacilityDescription;
	private String userDescription;
	public static Hashtable logTable = new Hashtable();
	static {
		logTable.put("mapKey", "Map Key");
		logTable.put("scenarioId", "Scenario Id");
		logTable.put("eventFacilityId", "Event Facility Id");
		logTable.put("ordinal", "Ordinal");
		logTable.put("executeWhen", "Execute When");
		logTable.put("parametersXml", "Parameter Xml");
		logTable.put("repeatOnReraise", "Repeat On Reraise");
		logTable.put("userDescription", "Description");
	}
	public Long getMapKey() {
		return mapKey;
	}

	public void setMapKey(Long mapKey) {
		this.mapKey = mapKey;
	}
	
	
	public int getOrdinal() {
		return ordinal;
	}
	public void setOrdinal(int ordinal) {
		this.ordinal = ordinal;
	}
	public String getParametersXml() {
		return parametersXml;
	}
	public void setParametersXml(String parametersXml) {
		this.parametersXml = parametersXml;
	}
	public String getExecuteWhen() {
		return executeWhen;
	}
	public void setExecuteWhen(String executeWhen) {
		this.executeWhen = executeWhen;
	}
	public String getRepeatOnReraise() {
		return repeatOnReraise;
	}
	public void setRepeatOnReraise(String repeatOnReraise) {
		this.repeatOnReraise = repeatOnReraise;
	}
	public String getEventFacilityDescription() {
		return eventFacilityDescription;
	}
	public void setEventFacilityDescription(String eventFacilityDescription) {
		this.eventFacilityDescription = eventFacilityDescription;
	}

		/**
		 * @return the scenarioid
		 */
		public String getScenarioId() {
			return scenarioId;
		}

		/**
		 * @param scenarioid the ScenarioId to set
		 */
		public void setScenarioId(String  scenarioId) {
			this.scenarioId = scenarioId;
		}

		public String  getEventFacilityId() {
			return eventFacilityId;
		}

		public void setEventFacilityId(String eventFacilityId) {
			this.eventFacilityId = eventFacilityId;
		}

		public String getUserDescription() {
			return userDescription;
		}

		public void setUserDescription(String userDescription) {
			this.userDescription = userDescription;
		}

		

}
