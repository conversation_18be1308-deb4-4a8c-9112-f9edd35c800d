<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE hibernate-mapping PUBLIC
        "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
        "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">

<hibernate-mapping>
    <class name="org.swallow.control.model.ScenarioSystem" table="P_SCENARIO_SYSTEM" >
		<id name="scenarioId" column="SCENARIO_ID" unsaved-value="null">
		<generator class="assigned"/>
		</id>
		<property name="lastrundate" column="LAST_RUN_DATE" not-null="false"/>
		<property name="lastrundurationsecs" column="LAST_RUN_DURATION_SECS" not-null="false"/>
    </class>
</hibernate-mapping>
