package org.swallow.control.model;

/*
 * Created on Dec 26, 2005
 *
 * TODO To change the template for this generated file go to
 * Window - Preferences - Java - Code Style - Code Templates
 */





import org.swallow.model.BaseObject;



public class UserLoginStatus extends BaseObject{
	private String userId;
	private String logOnTime;
		
	
	/**
	 * @return Returns the logOnTime.
	 */
	public String getLogOnTime() {
		return logOnTime;
	}
	/**
	 * @param logOnTime The logOnTime to set.
	 */
	public void setLogOnTime(String logOnTime) {
		this.logOnTime = logOnTime;
	}
	/**
	 * @return Returns the userId.
	 */
	public String getUserId() {
		return userId;
	}
	/**
	 * @param userId The userId to set.
	 */
	public void setUserId(String userId) {
		this.userId = userId;
	}
	  }
