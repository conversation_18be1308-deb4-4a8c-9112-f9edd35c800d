<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE hibernate-mapping PUBLIC
        "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
        "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">

<hibernate-mapping>
 <class name="org.swallow.control.model.Scheduler" table="S_SCHEDULER">
 	<id name="scheduleId" column="SCHEDULE_ID">
		<generator class="sequence">
			<param name="sequence_name">SEQ_S_SCHEDULER</param>
		</generator>
	</id>
  <property name="jobId" column="JOB_ID" not-null="true"/>
  <property name="hostId" column="HOST_ID" not-null="true"/>
  <property name="startingDate" column="STARTING_DATE" not-null="false"/>
  <property name="startingTime" column="STARTING_TIME" not-null="false"/>
  <property name="endingDate" column="ENDING_DATE" not-null="false"/>
  <property name="endingTime" column="ENDING_TIME" not-null="false"/>
  <property name="jobType" column="JOB_TYPE" not-null="false"/>
  <property name="durationHours" column="DURATION_HOURS" not-null="false"/>
  <property name="durationMins" column="DURATION_MINS" not-null="false"/>
  <property name="durationSecs" column="DURATION_SECS" not-null="false"/>
  <property name="scheduleDay" column="SCHEDULE_DAY" not-null="false"/>  
  <property name="scheduleDate" column="SCHEDULE_DATE" not-null="false"/>
  <property name="scheduleTime" column="SCHEDULE_TIME" not-null="false"/>
  <property name="filePath" column="FILE_PATH" not-null="false"/>
  <property name="reportNameConv" column="REPORT_NAME_CONV" not-null="false"/>
  <property name="monthFirst" column="MONTH_FIRST" not-null="false"/>
  <property name="monthLast" column="MONTH_LAST" not-null="false"/>
  <property name="monthDate" column="MONTH_DATE" not-null="false"/>
  <property name="jobStatus" column="JOB_STATUS" not-null="true"/>
  <property name="updateDate" column="UPDATE_DATE" not-null="false"/>
  <property name="updateUser" column="UPDATE_USER" not-null="false"/>
  
            <many-to-one lazy="false"
				name="job"
				update="false"
				insert="false"
				cascade="none"
				class="org.swallow.control.model.Job"
				not-null="true"
				outer-join="true"
				foreign-key="FK_S_SCHEDULER_S_JOB">
				<column name="HOST_ID"/>
				<column name="JOB_ID"/>
		</many-to-one>
  
 </class>
</hibernate-mapping>