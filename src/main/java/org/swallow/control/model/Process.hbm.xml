<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE hibernate-mapping PUBLIC
        "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
        "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">

<hibernate-mapping>
    <class name="org.swallow.control.model.Process" table="S_Process">
    	<composite-id name="id" class="org.swallow.control.model.Process$Id" >
			 <key-property name="processName" access="field" column="PROCESS_NAME"/>			 
		</composite-id>
		<property name="heartBeatEnabled" column="HEARTBEAT_ENABLED" not-null="false"/>	
		<property name="defaultRunTime" column="DEFAULT_RUN_TIME" not-null="false"/>	
		<property name="processType" column="PROCESS_TYPE" not-null="false"/>
	</class>
</hibernate-mapping>