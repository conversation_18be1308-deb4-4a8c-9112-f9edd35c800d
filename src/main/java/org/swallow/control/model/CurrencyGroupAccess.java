/*
 * @(#)CurrencyGroupAccess.java 1.0 06/07/03
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.control.model;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.swallow.maintenance.model.Currency;
import org.swallow.maintenance.model.CurrencyGroup;
import org.swallow.maintenance.model.EntityCurrencyGroupTO;
import org.swallow.model.BaseObject;
import org.swallow.util.SwtConstants;
import org.swallow.util.SwtUtil;

import java.util.Date;
import java.util.Hashtable;


/**
 * This class is model class that maps to P_CURRENCY_GROUP_ACCESS database
 * table.
 *
 * <AUTHOR> Systems
 * @version 1.0
 */
public class CurrencyGroupAccess extends BaseObject
    implements org.swallow.model.AuditComponent {
    /**
     * Holds the mapping of names to be inseterted into system log on auditing
     */
    public static Hashtable logTable = new Hashtable();

    static {
        logTable.put("entityId", "Entity Id");
        logTable.put("accessId", "Access Type");
    }
   // private final Log log = LogFactory.getLog(CurrencyGroupAccess.class);
    
    /** Holds the primary key object */
    private Id id = new Id();

    /** Holds the access id */
    private String accessId = SwtConstants.CURRENCYGRP_NO_ACCESS + "";

    /** Holds the update date */
    private Date updateDate = new Date();

    /** Holds the update user */
    private String updateUser;

    /**
     * Returns the Access Id.
     *
     * @return Returns the accessId.
     */
    public String getAccessId() {
        return accessId;
    }

    /**
     * Sets the access id.
     *
     * @param accessId The accessId to set.
     */
    public void setAccessId(String accessId) {
        this.accessId = accessId;
    }

    /**
     * Returns the id
     *
     * @return Returns the id.
     */
    public Id getId() {
        return id;
    }

    /**
     * Set the id
     *
     * @param id The id to set.
     */
    public void setId(Id id) {
        this.id = id;
    }

    /**
     * Returns the updateDate.
     *
     * @return Returns the updateDate.
     */
    public Date getUpdateDate() {
        return updateDate;
    }

    /**
     * Set the update date.
     *
     * @param updateDate The updateDate to set.
     */
    public void setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
    }

    /**
     * Returns the updateUser.
     *
     * @return Returns the updateUser.
     */
    public String getUpdateUser() {
        return updateUser;
    }

    /**
     * Set the update user.
     *
     * @param updateUser The updateUser to set.
     */
    public void setUpdateUser(String updateUser) {
        this.updateUser = updateUser;
    }
    
    public void onAdd(){
		//log.debug("entering onAdd method");		
		EntityRoleTO entityRoleTO = new EntityRoleTO(id.roleId, id.entityId);		
		SwtUtil.getSwtMaintenanceCache().remove(entityRoleTO);		
		//log.debug("exiting onAdd method");		
	}
	public void onUpdate(Object oldObject, Object newObject){
		//log.debug("entering onUpdate method");
		////log.debug("oldObject - " + oldObject);
		//log.debug("newObject - " + newObject);		
		if(oldObject != null && oldObject instanceof CurrencyGroupAccess){
			String roleId = ((CurrencyGroupAccess)oldObject).getId().getRoleId();
			String entityId = ((CurrencyGroupAccess)oldObject).getId().getEntityId();			
			EntityRoleTO entityRoleTO = new EntityRoleTO(roleId, entityId);		
			SwtUtil.getSwtMaintenanceCache().remove(entityRoleTO);		
		}		
		if(newObject != null && newObject instanceof CurrencyGroupAccess){
			String roleId = ((CurrencyGroupAccess)newObject).getId().getRoleId();
			String entityId = ((CurrencyGroupAccess)newObject).getId().getEntityId();			
			EntityRoleTO entityRoleTO = new EntityRoleTO(roleId, entityId);		
			SwtUtil.getSwtMaintenanceCache().remove(entityRoleTO);		
		}		
		//log.debug("exiting onUpdate method");		
	}
	
	public void onDelete(){
		//log.debug("entering onDelete method");
		EntityRoleTO entityRoleTO = new EntityRoleTO(id.roleId, id.entityId);		
		SwtUtil.getSwtMaintenanceCache().remove(entityRoleTO);		
		//log.debug("entering onDelete method");		
	}
    

    /**
     * THis is inner class of CurrencyGroupAccess class. It keeps the primary
     * key object.
     *
     * <AUTHOR> Systems
     * @version 1.0
     */
    public static class Id extends BaseObject {
        /** Holds the Host Id*/
        private String hostId;

        /** Holds the Role Id */
        private String roleId;

        /** Holds the Entity Id */
        private String entityId;

        /** Holds the Currency Group Id */
        private String currencyGroupId;

        /**
         * Creates a new Id object.
         */
        public Id() {
        }

        /**
         * Creates a new Id object.
         *
         * @param hostId Host Id
         * @param roleId Role Id
         * @param entityId Entity Id
         * @param currencyGroupId Currency Group Id
         */
        public Id(String hostId, String roleId, String entityId,
            String currencyGroupId) {
            this.hostId = hostId;
            this.roleId = roleId;
            this.entityId = entityId;
            this.currencyGroupId = currencyGroupId;
        }

        /**
         * Returns the Entity Id.
         *
         * @return Returns the entityId.
         */
        public String getEntityId() {
            return entityId;
        }

        /**
         * Set the Entity Id
         *
         * @param entityId The entityId to set.
         */
        public void setEntityId(String entityId) {
            this.entityId = entityId;
        }

        /**
         * Returns the Host Id.
         *
         * @return Returns the hostId.
         */
        public String getHostId() {
            return hostId;
        }

        /**
         * Set the Host Id
         *
         * @param hostId The hostId to set.
         */
        public void setHostId(String hostId) {
            this.hostId = hostId;
        }

        /**
         * Returns the Role Id.
         *
         * @return Returns the roleId.
         */
        public String getRoleId() {
            return roleId;
        }

        /**
         * Set the Role Id
         *
         * @param roleId The roleId to set.
         */
        public void setRoleId(String roleId) {
            this.roleId = roleId;
        }

        /**
         * Returns the Currency Group Id.
         *
         * @return Returns the currencyGroupId.
         */
        public String getCurrencyGroupId() {
            return currencyGroupId;
        }

        /**
         * Set the Currency Group Id.
         *
         * @param currencyGroupId The currencyGroupId to set.
         */
        public void setCurrencyGroupId(String currencyGroupId) {
            this.currencyGroupId = currencyGroupId;
        }
    }
	
    
}
