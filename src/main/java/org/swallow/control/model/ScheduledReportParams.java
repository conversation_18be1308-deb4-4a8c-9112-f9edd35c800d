/*
 * @(#)ScheduledReportParams.java 1.0 13/11/2018
 *
 * Copyright (c) 2006-2018 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.control.model;

import java.util.Hashtable;

import org.swallow.model.AuditComponent;
import org.swallow.model.BaseObject;

/**
 * <AUTHOR>
 *  
 * Class contain setter,getter methods. These methods used to getting 
 * and setting the values.
 */
public class ScheduledReportParams extends BaseObject implements AuditComponent {
	public static Hashtable  logTable = new Hashtable();
	static {
		logTable.put("scheduleId","Schedule Id");
		logTable.put("jobId","job Id");
		logTable.put("reportTypeId","report Type Id");
		logTable.put("reportName","report Name");
		logTable.put("reportDesc","report Desc");
		logTable.put("executionRole","execution Role");
		logTable.put("mapDateMethod","map Date Method");
		logTable.put("outputFormat","outputn Format");
		logTable.put("outputLocation","output Location");
		logTable.put("fileNamePrefix","fileName Prefix");
		logTable.put("reportConfig","report Config");
		logTable.put("accessListRoles","access List Roles");
		logTable.put("accessListUsers","access List Users");
		logTable.put("emailDistListRoles","email Dist List Roles");
		logTable.put("emailDistListUsers","email Dist List Users");
		logTable.put("retainDays","retain Days");
		logTable.put("jobStatus","Job Status");
	}
	private static final long serialVersionUID = 1L;
	private Integer scheduleId;
	private String hostId;
	private String jobId;
	private String reportTypeId;
	private String reportName;
	private String reportDesc;
	private String executionRole;
	private String mapDateMethod;
	private String outputFormat;
	private String outputLocation;
	private String fileNamePrefix;
	private String reportConfig;
	private String accessListRoles;
	private String accessListUsers;
	private String emailDistListRoles;
	private String emailDistListUsers;
	private String retainDays;
	/**
	 * @return the scheduleId
	 */
	public Integer getScheduleId() {
		return scheduleId;
	}
	/**
	 * @param scheduleId the scheduleId to set
	 */
	public void setScheduleId(Integer scheduleId) {
		this.scheduleId = scheduleId;
	}
	/**
	 * @return the hostId
	 */
	public String getHostId() {
		return hostId;
	}
	/**
	 * @param hostId the hostId to set
	 */
	public void setHostId(String hostId) {
		this.hostId = hostId;
	}
	/**
	 * @return the jobId
	 */
	public String getJobId() {
		return jobId;
	}
	/**
	 * @param jobId the jobId to set
	 */
	public void setJobId(String jobId) {
		this.jobId = jobId;
	}
	/**
	 * @return the reportTypeId
	 */
	public String getReportTypeId() {
		return reportTypeId;
	}
	/**
	 * @param reportTypeId the reportTypeId to set
	 */
	public void setReportTypeId(String reportTypeId) {
		this.reportTypeId = reportTypeId;
	}
	/**
	 * @return the reportName
	 */
	public String getReportName() {
		return reportName;
	}
	/**
	 * @param reportName the reportName to set
	 */
	public void setReportName(String reportName) {
		this.reportName = reportName;
	}
	/**
	 * @return the reportDesc
	 */
	public String getReportDesc() {
		return reportDesc;
	}
	/**
	 * @param reportDesc the reportDesc to set
	 */
	public void setReportDesc(String reportDesc) {
		this.reportDesc = reportDesc;
	}
	/**
	 * @return the executionRole
	 */
	public String getExecutionRole() {
		return executionRole;
	}
	/**
	 * @param executionRole the executionRole to set
	 */
	public void setExecutionRole(String executionRole) {
		this.executionRole = executionRole;
	}
	/**
	 * @return the mapDateMethod
	 */
	public String getMapDateMethod() {
		return mapDateMethod;
	}
	/**
	 * @param mapDateMethod the mapDateMethod to set
	 */
	public void setMapDateMethod(String mapDateMethod) {
		this.mapDateMethod = mapDateMethod;
	}
	/**
	 * @return the outputFormat
	 */
	public String getOutputFormat() {
		return outputFormat;
	}
	/**
	 * @param outputFormat the outputFormat to set
	 */
	public void setOutputFormat(String outputFormat) {
		this.outputFormat = outputFormat;
	}
	/**
	 * @return the outputLocation
	 */
	public String getOutputLocation() {
		return outputLocation;
	}
	/**
	 * @param outputLocation the outputLocation to set
	 */
	public void setOutputLocation(String outputLocation) {
		this.outputLocation = outputLocation;
	}
	/**
	 * @return the fileNamePrefix
	 */
	public String getFileNamePrefix() {
		return fileNamePrefix;
	}
	/**
	 * @param fileNamePrefix the fileNamePrefix to set
	 */
	public void setFileNamePrefix(String fileNamePrefix) {
		this.fileNamePrefix = fileNamePrefix;
	}
	/**
	 * @return the reportConfig
	 */
	public String getReportConfig() {
		return reportConfig;
	}
	/**
	 * @param reportConfig the reportConfig to set
	 */
	public void setReportConfig(String reportConfig) {
		this.reportConfig = reportConfig;
	}
	/**
	 * @return the accessListRoles
	 */
	public String getAccessListRoles() {
		return accessListRoles;
	}
	/**
	 * @param accessListRoles the accessListRoles to set
	 */
	public void setAccessListRoles(String accessListRoles) {
		this.accessListRoles = accessListRoles;
	}
	/**
	 * @return the accessListUsers
	 */
	public String getAccessListUsers() {
		return accessListUsers;
	}
	/**
	 * @param accessListUsers the accessListUsers to set
	 */
	public void setAccessListUsers(String accessListUsers) {
		this.accessListUsers = accessListUsers;
	}
	/**
	 * @return the emailDistListRoles
	 */
	public String getEmailDistListRoles() {
		return emailDistListRoles;
	}
	/**
	 * @param emailDistListRoles the emailDistListRoles to set
	 */
	public void setEmailDistListRoles(String emailDistListRoles) {
		this.emailDistListRoles = emailDistListRoles;
	}
	/**
	 * @return the emailDistListUsers
	 */
	public String getEmailDistListUsers() {
		return emailDistListUsers;
	}
	/**
	 * @param emailDistListUsers the emailDistListUsers to set
	 */
	public void setEmailDistListUsers(String emailDistListUsers) {
		this.emailDistListUsers = emailDistListUsers;
	}
	/**
	 * @return the retainDays
	 */
	public String getRetainDays() {
		return retainDays;
	}
	/**
	 * @param retainDays the retainDays to set
	 */
	public void setRetainDays(String retainDays) {
		this.retainDays = retainDays;
	}
}
