/*
 * Created on Dec 6, 2005
 *
 * TODO To change the template for this generated file go to
 * Window - Preferences - Java - Code Style - Code Templates
 */
package org.swallow.control.model;

import java.util.Date;
import java.util.Hashtable;

import org.swallow.model.BaseObject;
/**
 *
 * TODO To change the template for this generated type comment go to
 * Window - Preferences - Java - Code Style - Code Templates
 */
public class FacilityAccess extends BaseObject implements org.swallow.model.AuditComponent{
	
	private String reqAuth;
	private String authOthers;
	private Date updateDate;
	private String updateUser;
	private Id id = new Id();
	
	public static class Id extends BaseObject{
		private String roleId;
		private String facilityId;
		


		public Id() {}

		public Id(String roleId, String facilityId) {
			this.roleId = roleId;
			this.facilityId = facilityId;
		}
				

		/**
		 * @return Returns the roleId.
		 */
		public String getRoleId() {
			return roleId;
		}
		/**
		 * @param roleId The roleId to set.
		 */
		public void setRoleId(String roleId) {
			this.roleId = roleId;
		}
		
		/**
		 * @return Returns the roleId.
		 */
		public String getFacilityId() {
			return facilityId;
		}
		/**
		 * @param roleId The roleId to set.
		 */
		public void setFacilityId(String facilityId) {
			this.facilityId = facilityId;
		}
	}

	public static Hashtable  logTable = new Hashtable();
	static {
		logTable.put("facilityId","Facility Id");
		logTable.put("accessId","Access Type");
	}
	
	
	public String getReqAuth() {
		return reqAuth;
	}
	public void setReqAuth(String reqAuth) {
		this.reqAuth = reqAuth;
	}
	public String getAuthOthers() {
		return authOthers;
	}
	public void setAuthOthers(String authOthers) {
		this.authOthers = authOthers;
	}
	/**
	 * @return Returns the updateDate.
	 */
	public Date getUpdateDate() {
		return updateDate;
	}
	/**
	 * @param updateDate The updateDate to set.
	 */
	public void setUpdateDate(Date updateDate) {
		this.updateDate = updateDate;
	}
	/**
	 * @return Returns the id.
	 */
	public Id getId() {
		return id;
	}
	/**
	 * @param id The id to set.
	 */
	public void setId(Id id) {
		this.id = id;
	}
	
	/**
	 * @return Returns the updateUser.
	 */
	public String getUpdateUser() {
		return updateUser;
	}
	/**
	 * @param updateUser The updateUser to set.
	 */
	public void setUpdateUser(String updateUser) {
		this.updateUser = updateUser;
	}

	
}
