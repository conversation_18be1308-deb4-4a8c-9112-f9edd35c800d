<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE hibernate-mapping PUBLIC
        "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
        "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">

<hibernate-mapping>
    <class name="org.swallow.control.model.EntityProcess" table="S_ENTITY_PROCESS">
    	<composite-id name="id" class="org.swallow.control.model.EntityProcess$Id" >
			 <key-property name="processName" access="field" column="PROCESS_NAME"/>	
			 <key-property name="entityId" access="field" column="ENTITY_ID"/>			 
		</composite-id>
		<property name="runTime" column="RUN_TIME" not-null="false"/>
		<property name="runOrder" column="RUN_ORDER" not-null="false"/>
	</class>
</hibernate-mapping>