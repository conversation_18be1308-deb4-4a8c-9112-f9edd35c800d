/*
 * Created on Apr 2, 2007
 *
 * TODO To change the template for this generated file go to
 * Window - Preferences - Java - Code Style - Code Templates
 */
package org.swallow.control.model;

import java.text.SimpleDateFormat;
import java.util.Date;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.swallow.control.model.AuditLog.Id;
import org.swallow.model.BaseObject;

/**
 * <AUTHOR>
 *
 * TODO To change the template for this generated type comment go to
 * Window - Preferences - Java - Code Style - Code Templates
 */
public class AuditLogView extends BaseObject implements Comparable{
	private final Log log = LogFactory.getLog(AuditLogView.class);
	/**
	 * @return Returns the logDate_Date.
	 */
	public String getLogDate_Date() {
		return LogDate_Date;
	}
	/**
	 * @param logDate_Date The logDate_Date to set.
	 */
	public void setLogDate_Date(String logDate_Date) {
		LogDate_Date = logDate_Date;
	}
	/**
	 * @return Returns the logDate_Time.
	 */
	public String getLogDate_Time() {
		SimpleDateFormat  sdf = new SimpleDateFormat("HH:mm:ss");
		return sdf.format(getId().getLogDate());
	}
	/**
	 * @param logDate_Time The logDate_Time to set.
	 */
	public void setLogDate_Time(String logDate_Time) {
		LogDate_Time = logDate_Time;
	}
	
	private String LogDate_Date;
	private String LogDate_Time;
		
	/*    A negative integer if this is less than obj 
    Zero if this and obj are equivalent 
    A positive integer if this is greater than obj
   */

    public int compareTo(Object obj)
    {
        log.debug("Calling compareTo");
        int retValue = 0;
        
        if(obj instanceof AuditLogView)
        {
        	AuditLogView logObj = (AuditLogView)obj;
            if(this.getId().getLogDate().getTime() > logObj.getId().getLogDate().getTime())
                retValue = 1;
            else if(this.getId().getLogDate().getTime() < logObj.getId().getLogDate().getTime())
            	retValue = -1;
        }
        return retValue;
    }
    
    public static class Id extends BaseObject{
		private final Log log = LogFactory.getLog(AuditLog.class);
		
		public void setUserId(String userId) {
			this.userId = userId;
		}
		
		private Date logDate;
		private String userId;
		private String reference;
		private String referenceId;		
		private String action;
		
		
		
		public Id() {}

		public Id(Date logDate, String userId, String referenceId, String reference, String action) {
			
			this.logDate = logDate;
			this.userId = userId;
			this.reference = reference;
			this.referenceId = referenceId;
			this.action = action;
			
			

		}

		/**
		 * @return Returns the action.
		 */
		public String getAction() {
			log.debug("action======>"+action);
		 if(action!=null){
		 	
		 	
				if(action.equalsIgnoreCase("d")){
					action="Deleted"; 
					 
				}else if ((action.equalsIgnoreCase("c")) || (action.equalsIgnoreCase("u"))){
					action="Changed"; 
					
				}else if (action.equalsIgnoreCase("I")){
					action="Input";
					
				}else if (action.equalsIgnoreCase("a")){
					action="Added";
				}
				else if (action.equalsIgnoreCase("m")){
					action="Matched";
				}
				else if (action.equalsIgnoreCase("n")){
					action="Unmatched";
				}
				else if (action.equalsIgnoreCase("R")){
						action="Rollover";
				}
    	   }else 
	     	{
    		action = "";
		    }
		
			return action;
		}
		/**
		 * @param action The action to set.
		 */
		public void setAction(String action) {
			
			this.action = action; 
		}
		
		/**
		 * @return Returns the log.
		 */
		public Log getLog() {
			return log;
		}
		/**
		 * @return Returns the logDate.
		 */
		public Date getLogDate() {
			return logDate;
		}
		/**
		 * @param logDate The logDate to set.
		 */
		public void setLogDate(Date logDate) {
			this.logDate = logDate;
		}
		
		/**
		 * @return Returns the reference.
		 */
		public String getReference() {
			return reference;
		}
		/**
		 * @param reference The reference to set.
		 */
		public void setReference(String reference) {
			this.reference = reference;
		}
		/**
		 * @return Returns the referenceId.
		 */	
		public String getReferenceId() {
			return referenceId;
		}
		/**
		 * @param referenceId The referenceId to set.
		 */
		public void setReferenceId(String referenceId) {
			this.referenceId = referenceId;
		}
		/**
		 * @return Returns the userId.
		 */
		public String getUserId() {
			return userId;
		}
		/**
		 * @param userId The userId to set.
		 */
	

	}
    
    private Id id = new Id();
	/**
	 * @return Returns the id.
	 */
	public Id getId() {
		return id;
	}
	/**
	 * @param id The id to set.
	 */
	public void setId(Id id) {
		this.id = id;
	}
	public boolean equals(Object o) {
	        boolean retVal = true;
	        if(o instanceof AuditLog)
	         {
	            log.debug("Calling equals");
	            AuditLog oLog = (AuditLog)o;
	            
	            if(id.userId != null && oLog.getId().getUserId() != null  )
	            {
	                if (!(id.userId.equals(oLog.getId().getUserId())))
	                    retVal = false;
	            }
	            if(id.reference != null && oLog.getId().getReference() != null  )
	            {
	                if (!(id.reference.equals(oLog.getId().getReference())))
	                    retVal = false;
	            }
	            if(id.referenceId != null && oLog.getId().getReferenceId() != null  )
	            {
	                if (!(id.referenceId.equals(oLog.getId().getReferenceId())))
	                    retVal = false;
	            }	            	            
	            
	            /*
	            if(this.getId().getHostId() != null && oLog.getId().getHostId() != null && oLog.getId().getHostId().equals(this.getId().getHostId()) &&
	               id.ipAddress != null && oLog.getId().getIpAddress() != null && oLog.getId().getIpAddress().equals(id.ipAddress) &&               
	               id.userId != null && oLog.getId().getUserId() != null && oLog.getId().getUserId().equals(id.userId) &&
	               id.reference != null && oLog.getId().getReference() != null && oLog.getId().getReference().equals(id.reference) &&
	               id.referenceId != null && oLog.getId().getReferenceId() != null && oLog.getId().getReferenceId().equals(id.referenceId) &&
	               id.logDate != null && oLog.getId().getLogDate() != null && oLog.getId().getLogDate().equals(id.logDate)  
	               )
	            {
	                retVal = true;
	            }*/
	            
	         }  
	        
	        return retVal;
	            
	    }

	    public int hashCode() {
	        log.debug("Calling hashCode");
	        int hashCode =  0;
	        if(id.userId != null) hashCode += id.userId.hashCode();
	        if(id.reference != null) hashCode += id.reference.hashCode();
	        if(id.referenceId != null) hashCode += id.referenceId.hashCode();
	        if(id.logDate != null) hashCode += id.logDate.hashCode();    
	        if(id.action != null) hashCode += id.action.hashCode(); 
	        
	        return hashCode;
	            
	    }

}
