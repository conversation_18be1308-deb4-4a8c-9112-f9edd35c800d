package org.swallow.control.model;

import java.util.Date;

import org.swallow.model.BaseObject;

/**
 * <AUTHOR>
 *
 * TODO To change the template for this generated type comment go to
 * Window - Preferences - Java - Code Style - Code Templates
 */
public class PasswordHistory extends BaseObject {

	private String password;
	private Date updateDate=new Date();
	private String updateUser;
	private Id id = new Id();
	

	public static class Id extends BaseObject{
		private String hostId;
		private String userId;
		private String seqNo;
		
		public Id() {}

		public Id(String hostId, String seqNo,String userId) {
			this.hostId = hostId;
			this.seqNo=seqNo;
			this.userId=userId;
		}
		
				
		public String getHostId() {
			return hostId;
		}
		/**
		 * @param hostId The hostId to set.
		 */
		public void setHostId(String hostId) {
			this.hostId = hostId;
		
		}		
		/**
		 * @return Returns the seqNo.
		 */
		public String getSeqNo() {
			return seqNo;
		}
		/**
		 * @param seqNo The seqNo to set.
		 */
		public void setSeqNo(String seqNo) {
			this.seqNo = seqNo;
		}
		/**
		 * @return Returns the userId.
		 */
		public String getUserId() {
			return userId;
		}
		/**
		 * @param userId The userId to set.
		 */
		public void setUserId(String userId) {
			this.userId = userId;
		}
	}
	/**
	 * @return Returns the id.
	 */
	public Id getId() {
		return id;
	}
	/**
	 * @param id The id to set.
	 */
	public void setId(Id id) {
		this.id = id;
	}
	
	
	/**
	 * @return Returns the updateDate.
	 */
	public Date getUpdateDate() {
		return updateDate;
	}
	/**
	 * @param updateDate The updateDate to set.
	 */
	public void setUpdateDate(Date updateDate) {
		this.updateDate = updateDate;
	}
	/**
	 * @return Returns the updateUser.
	 */
	public String getUpdateUser() {
		return updateUser;
	}
	/**
	 * @param updateUser The updateUser to set.
	 */
	public void setUpdateUser(String updateUser) {
		this.updateUser = updateUser;
	}
	/**
	 * @return Returns the password.
	 */
	public String getPassword() {
		return password;
	}
	/**
	 * @param password The password to set.
	 */
	public void setPassword(String password) {
		this.password = password;
	}
}
