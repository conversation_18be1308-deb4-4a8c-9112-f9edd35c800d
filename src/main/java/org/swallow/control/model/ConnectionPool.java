package org.swallow.control.model;
import java.sql.Connection;
import java.util.Date;
import java.util.Hashtable;

import org.swallow.model.AuditComponent;
import org.swallow.model.BaseObject;

public class ConnectionPool extends BaseObject implements AuditComponent {

	private static final long serialVersionUID = 1L;
	public static Hashtable logTable = new Hashtable();
	private Id id = new Id();
	private String module;
	private Boolean isClosed;
	private Boolean isIdle;
	private Boolean isOpened;
	private Boolean highlight;
	private String stackTrace;
	private String action;
	private String sqlId;
	private String sqlStatement;
	private String ssId;
	private String audSid;
	private String status;
	private String lastJDBCstatus;
	private String sqlStatus;
	private Double duration;
	private Date startTime;
	private Date endTime;
	private Date lastActionTime;
	private Date sqlExecStartTime;
	private String userAction;
	private String instanceUiid;
	private Connection connection;

	private boolean connectionStarted = false;
	
	public Id getId() {
		return id;
	}

	public void setId(Id id) {
		this.id = id;
	}

	public String getModule() {
		return module;
	}

	public void setModule(String module) {
		this.module = module;
	}

	public Boolean getIsClosed() {
		return isClosed;
	}

	public void setIsClosed(Boolean isClosed) {
		this.isClosed = isClosed;
	}

	public Boolean getIsIdle() {
		return isIdle;
	}

	public void setIsIdle(Boolean isIdle) {
		this.isIdle = isIdle;
	}

	public Boolean getIsOpened() {
		return isOpened;
	}

	public void setIsOpened(Boolean isOpened) {
		this.isOpened = isOpened;
	}

	public Boolean getHighlight() {
		return highlight;
	}

	public void setHighlight(Boolean highlight) {
		this.highlight = highlight;
	}

	public String getStackTrace() {
		return stackTrace;
	}

	public void setStackTrace(String stackTrace) {
		this.stackTrace = stackTrace;
	}

	public String getAction() {
		return action;
	}

	public void setAction(String action) {
		this.action = action;
	}

	public String getSqlId() {
		return sqlId;
	}

	public void setSqlId(String sqlId) {
		this.sqlId = sqlId;
	}

	public String getSqlStatement() {
		return sqlStatement;
	}

	public void setSqlStatement(String sqlStatement) {
		this.sqlStatement = sqlStatement;
	}

	public String getSsId() {
		return ssId;
	}

	public void setSsId(String ssId) {
		this.ssId = ssId;
	}

	public String getAudSid() {
		return audSid;
	}

	public void setAudSid(String audSid) {
		this.audSid = audSid;
	}

	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	public Double getDuration() {
		return duration;
	}

	public void setDuration(Double duration) {
		this.duration = duration;
	}

	public Date getStartTime() {
		return startTime;
	}

	public void setStartTime(Date startTime) {
		this.startTime = startTime;
	}

	public Date getEndTime() {
		return endTime;
	}

	public void setEndTime(Date endTime) {
		this.endTime = endTime;
	}

	public Date getLastActionTime() {
		return lastActionTime;
	}

	public void setLastActionTime(Date lastActionTime) {
		this.lastActionTime = lastActionTime;
	}

	public String getUserAction() {
		return userAction;
	}

	public void setUserAction(String userAction) {
		this.userAction = userAction;
	}
	
	public Connection getConnection() {
		return connection;
	}

	public void setConnection(Connection connection) {
		this.connection = connection;
	}

	public String getSqlStatus() {
		return sqlStatus;
	}

	public void setSqlStatus(String sqlStatus) {
		this.sqlStatus = sqlStatus;
	}

	public String getLastJDBCstatus() {
		return lastJDBCstatus;
	}

	public void setLastJDBCstatus(String lastJDBCstatus) {
		this.lastJDBCstatus = lastJDBCstatus;
	}

	public boolean isConnectionStarted() {
		return connectionStarted;
	}

	public void setConnectionStarted(boolean connectionStarted) {
		this.connectionStarted = connectionStarted;
	}

	public Date getSqlExecStartTime() {
		return sqlExecStartTime;
	}

	public void setSqlExecStartTime(Date sqlExecStartTime) {
		this.sqlExecStartTime = sqlExecStartTime;
	}

	public String getInstanceUiid() {
		return instanceUiid;
	}

	public void setInstanceUiid(String instanceUiid) {
		this.instanceUiid = instanceUiid;
	}

	public static class Id extends BaseObject {
		private static final long serialVersionUID = 1L;
		private String connectionId;

		public Id() {
		}

		public String getConnectionId() {
			return connectionId;
		}

		public void setConnectionId(String connectionId) {
			this.connectionId = connectionId;
		}
	}
	
	@Override
	public String toString() {
		StringBuilder builder = new StringBuilder();
		builder.append("ConnectionPool [id=");
		builder.append(id);
		builder.append(", module=");
		builder.append(module);
		builder.append(", isClosed=");
		builder.append(isClosed);
		builder.append(", isIdle=");
		builder.append(isIdle);
		builder.append(", isOpened=");
		builder.append(isOpened);
		builder.append(", highlight=");
		builder.append(highlight);
		builder.append(", stackTrace=");
		builder.append(stackTrace);
		builder.append(", action=");
		builder.append(action);
		builder.append(", sqlId=");
		builder.append(sqlId);
		builder.append(", sqlStatement=");
		builder.append(sqlStatement);
		builder.append(", ssId=");
		builder.append(ssId);
		builder.append(", audSid=");
		builder.append(audSid);
		builder.append(", status=");
		builder.append(status);
		builder.append(", lastJDBCstatus=");
		builder.append(lastJDBCstatus);
		builder.append(", sqlStatus=");
		builder.append(sqlStatus);
		builder.append(", duration=");
		builder.append(duration);
		builder.append(", startTime=");
		builder.append(startTime);
		builder.append(", endTime=");
		builder.append(endTime);
		builder.append(", lastActionTime=");
		builder.append(lastActionTime);
		builder.append(", sqlExecStartTime=");
		builder.append(sqlExecStartTime);
		builder.append(", userAction=");
		builder.append(userAction);
		builder.append(", instanceUiid=");
		builder.append(instanceUiid);
		// THIS MAY LOCK !
		//builder.append(", connection=");
		//builder.append(connection);
		builder.append(", connectionStarted=");
		builder.append(connectionStarted);
		builder.append("]");
		return builder.toString();
	}
	
	
//	   @Override
//       public boolean equals(Object o) {
//           if (this == o) {
//               return true;
//           }
//           if (o == null || getClass() != o.getClass()) {
//               return false;
//           }
//           ConnectionPool connectionPool = (ConnectionPool) o;
//           if(connectionPool.getId().getConnectionId().equals(getId().getConnectionId())) {
//        	   
//        	   
// /*  			connectionId = request.getParameter("connectionId");
//   			moduleId = request.getParameter("moduleId");
//   			duration = request.getParameter("duration");
//   			sqlStatement = request.getParameter("sqlStatement");
//   			sqlStatus = request.getParameter("sqlStatus");
//   			audSid = request.getParameter("audSid");
//   			stackTrace = request.getParameter("stackTrace");
//   			ssid = request.getParameter("ssid");
//   			lastActionTime = request.getParameter("lastActionTime");
//   			status = request.getParameter("status");
//   			*/
//   			
//   			if(compare(module, connectionPool.getModule()) 
//   					&&  compare(sqlStatement,connectionPool.getSqlStatement())
//   					&&  compare(sqlStatus, connectionPool.getSqlStatus())
//   					&&  compare(audSid,connectionPool.getAudSid())
//   					&&  compare(stackTrace,connectionPool.getStackTrace())
//   					&&  compare(ssId, connectionPool.getSsId())
//   					&&  lastActionTime.equals(connectionPool.getLastActionTime())
//   					&&  compare(status,connectionPool.getStatus())
//   					&&  compare(sqlId,connectionPool.getSqlId())
//   					) {
//   				return true;
//   			}else {
//   				return false;
//   			}
//   			
//           }else {
//        	   return false;
//           }
//	   }
//	   
//	   public static boolean compare(String str1, String str2) {
//		    return (str1 == null ? str2 == null : str1.equals(str2));
//		}
//	   
}