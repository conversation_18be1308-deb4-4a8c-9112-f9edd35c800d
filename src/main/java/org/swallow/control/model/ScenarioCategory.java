/*
 * @(#)AccountAccessDetails.java 1.0 02/01/2010
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.control.model;

import java.util.Hashtable;

import org.swallow.model.BaseObject;

/**
 * <AUTHOR>
 *  
 * Class contain setter,getter methods. These methods used to getting 
 * and setting the values.
 */
public class ScenarioCategory extends BaseObject implements org.swallow.model.AuditComponent{
	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	private String description;
	private String title;
	private String systemflag;
	private String displayorder;
	private Integer displayTab;
	private String displayTabName;
	private Id id = new Id();

	public static Hashtable  logTable = new Hashtable();
	static {
		logTable.put("id.categoryid","Category Maintenance");
		logTable.put("displayorder","Display Order");
		logTable.put("displayTab","Display Tab");
	}
	
	/**
	 * Getter method for logTable
	 * 
	 * @return logTable as Hashtable
	 */

	public static Hashtable getLogTable() {
		return logTable;
	}

	/**
	 * Setter method for logTable
	 * 
	 * @param logTable
	 */

	public static void setLogTable(Hashtable logTable) {
		ScenarioCategory.logTable = logTable;
	}
	

	public String getDescription() {
		return description;
	}


	public void setDescription(String description) {
		this.description = description;
	}


	public String getTitle() {
		return title;
	}


	public void setTitle(String title) {
		this.title = title;
	}


	public String getSystemflag() {
		return systemflag;
	}


	public void setSystemflag(String systemflag) {
		this.systemflag = systemflag;
	}


	public String getDisplayorder() {
		return displayorder;
	}


	public void setDisplayorder(String displayorder) {
		this.displayorder = displayorder;
	}

	/**
	 * @return the id
	 */
	public Id getId() {
		return id;
	}

	/**
	 * @param id the id to set
	 */
	public void setId(Id id) {
		this.id = id;
	}

	public Integer getDisplayTab() {
		return displayTab;
	}

	public void setDisplayTab(Integer displayTab) {
		this.displayTab = displayTab;
	}

	public String getDisplayTabName() {
		return displayTabName;
	}

	public void setDisplayTabName(String displayTabName) {
		this.displayTabName = displayTabName;
	}

	public static class Id extends BaseObject{

		 
		private String categoryid;
		public Id() {
		}
		
		public Id(String categoryid) {
			 
			this.categoryid= categoryid;
			
		}

		/**
		 * @return the categoryid
		 */
		public String getCategoryid() {
			return categoryid;
		}

		/**
		 * @param categoryid the categoryid to set
		 */
		public void setCategoryid(String categoryid) {
			this.categoryid = categoryid;
		}

		

		

	}
}
