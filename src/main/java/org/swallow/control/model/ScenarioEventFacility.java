package org.swallow.control.model;

import java.util.Hashtable;

import org.swallow.control.model.Scenario.Id;
import org.swallow.model.BaseObject;

public class ScenarioEventFacility extends BaseObject implements org.swallow.model.AuditComponent {

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	private Id id = new Id();
	private String description;
	private String parameterFacility;
	private String programId;
	private String requiredParameters;
	
	public static Hashtable  logTable = new Hashtable();
	static {
		logTable.put("id","Scenario Gui Alert");
		logTable.put("description","Description");
	}
	
	
	public Id getId() {
		return id;
	}

	public void setId(Id id) {
		this.id = id;
	}

	public String getDescription() {
		return description;
	}

	public void setDescription(String description) {
		this.description = description;
	}

	public String getProgramId() {
		return programId;
	}

	public void setProgramId(String programId) {
		this.programId = programId;
	}

	public String getRequiredParameters() {
		return requiredParameters;
	}

	public void setRequiredParameters(String requiredParameters) {
		this.requiredParameters = requiredParameters;
	}

	public String getParameterFacility() {
		return parameterFacility;
	}

	public void setParameterFacility(String parameterFacility) {
		this.parameterFacility = parameterFacility;
	}
	
	public static class Id extends BaseObject{

		 
		private String eventFacilityId;
		public Id() {
		}
		
		public Id(String eventFacilityId) {
			 
			this.setEventFacilityId(eventFacilityId);
			 
			
		}

		/**
		 * @return the scenarioid
		 */
		public String getEventFacilityId() {
			return eventFacilityId;
		}

		/**
		 * @param scenarioid the eventFacilityId to set
		 */
		public void setEventFacilityId(String eventFacilityId) {
			this.eventFacilityId = eventFacilityId;
		}

		

	}


}
