package org.swallow.control.model;

import org.swallow.model.BaseObject;

public class InterfaceInterruption extends BaseObject {

	/**
	 * Variable declaration
	 */
	private static final long serialVersionUID = 1L;
	// Object to hold the inner class object
	private Id id = new Id();
	// Holds the Alert start time for the interface
	private String startAlertTime = null;
	// Holds the Alert end time for the interface
	private String endAlertTime = null;
	// Holds the Alert threshold time in seconds for the interface
	private String threshold = null;

	// This class used to set and get primary key value for table
	public static class Id extends BaseObject {
		/**
		 * 
		 */
		private static final long serialVersionUID = 1L;
		// Holds the Interface id
		private String interfaceId = null;

		// Variable to hold message type
		private String messageType = null;
		
		public Id() {
		}
		
		// This constructor used to set the interfaceId and messageType
		public Id(String interfaceId, String messageType) {
			this.interfaceId = interfaceId;
			this.messageType = messageType;
		}
		
	/**
	 * @return the interfaceId
	 */
	public String getInterfaceId() {
		return interfaceId;
	}

	/**
		 * @param interfaceId the interfaceId to set
	 */
	public void setInterfaceId(String interfaceId) {
		this.interfaceId = interfaceId;
	}

	/**
		 * @return the messageType
		 */
		public String getMessageType() {
			return messageType;
		}
		
		/**
		 * @param messageType the messageType to set
		 */
		public void setMessageType(String messageType) {
			this.messageType = messageType;
		}
	}
	
	/**
	 * @return the startAlertTime
	 */
	public String getStartAlertTime() {
		return startAlertTime;
	}

	/**
	 * @param startAlertTime
	 *            the startAlertTime to set
	 */
	public void setStartAlertTime(String startAlertTime) {
		this.startAlertTime = startAlertTime;
	}

	/**
	 * @return the endAlertTime
	 */
	public String getEndAlertTime() {
		return endAlertTime;
	}

	/**
	 * @param endAlertTime
	 *            the endAlertTime to set
	 */
	public void setEndAlertTime(String endAlertTime) {
		this.endAlertTime = endAlertTime;
	}

	/**
	 * @return the threshold
	 */
	public String getThreshold() {
		return threshold;
	}

	/**
	 * @param threshold
	 *            the threshold to set
	 */
	public void setThreshold(String threshold) {
		this.threshold = threshold;
	}
	
	/**
	 * Getter method for id
	 * 
	 * @return id as Id
	 */
	public Id getId() {
		return id;
	}

	/**
	 * Setter method for id
	 * 
	 * @param id
	 */
	public void setId(Id id) {
		this.id = id;
	}
}
