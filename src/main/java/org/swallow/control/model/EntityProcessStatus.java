/*
 * @(#)EntityProcessStatus.java 1.0 16/05/2011
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */

package org.swallow.control.model;

import java.util.Date;
import java.util.Hashtable;

import org.swallow.exception.SwtException;
import org.swallow.model.BaseObject;
import org.swallow.util.SwtConstants;
import org.swallow.util.SwtUtil;

/**
 * 
 * <AUTHOR>
 * 
 * Class contain setter,getter methods. These methods used to getting and
 * setting the values
 * 
 */
public class EntityProcessStatus extends BaseObject implements
		org.swallow.model.AuditComponent {

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	// Variable to hold process Status
	private String processStatus = null;
	// Variable to hold last Start Entity Time
	private Date lastStartEntityTime = null;
	// Variable to hold last End Entity Time
	private Date lastEndEntityTime = null;
	// Variable to hold last Start System Time
	private Date lastStartSystemTime = null;
	// Variable to hold last End System Time
	private Date lastEndSystemTime = null;
	// Variable to hold last Run Status
	private String lastRunStatus = null;
	// Variable to hold last Heart Beat Entity Time
	private Date lastHeartBeatEntityTime = null;
	// Variable to hold last Heart Beat System Time
	private Date lastHeartBeatSystemTime = null;
	// Variable to hold run Order
	private String runOrder = null;
	// Variable to hold database Session
	private String databaseSession = null;
	// Variable to hold default Run Time
	private String defaultRunTime = null;
	// Variable to hold run Time
	private String runTime = null;
	// Variable to hold time Display
	private String timeDisplay = null;
	// Variable to hold last Start Entity Time As String
	private String lastStartEntityTimeAsString = null;
	// Variable to hold last End Entity Time As String
	private String lastEndEntityTimeAsString = null;
	// Variable to hold last Start System Time As String
	private String lastStartSystemTimeAsString = null;
	// Variable to hold last End System Time As String
	private String lastEndSystemTimeAsString = null;
	// Variable to hold last Heart Beat Entity Time As String
	private String lastHeartBeatEntityTimeAsString = null;
	// Variable to hold last Heart Beat System Time As String
	private String lastHeartBeatSystemTimeAsString = null;
	// Variable to hold last Run Status Details
	private String lastRunStatusDetails = null;
	// Variable to hold process Status Details
	private String processStatusDetails = null;
	// Object to hold the inner class object
	private Id id = new Id();
	// Variable to hold Log Table
	public static Hashtable<String, String> logTable = new Hashtable<String, String>();
	static {
		logTable.put("processStatus", "Process Status");
	}

	// This class used to set and get primary key value for table
	public static class Id extends BaseObject {
		/**
		 * 
		 */
		private static final long serialVersionUID = 1L;
		// Variable to hold process Name
		private String processName = null;
		// Variable to hold entityId
		private String entityId = null;

		public Id() {
		}

		// This constructor used to set the process name and entityid
		public Id(String processName, String entityId) {
			this.processName = processName;
			this.entityId = entityId;
		}

		/**
		 * Getter method for processName
		 * 
		 * @return processName as String
		 */
		public String getProcessName() {
			return processName;
		}

		/**
		 * Setter method for processName
		 * 
		 * @param processName
		 */
		public void setProcessName(String processName) {
			this.processName = processName;
		}

		/**
		 * Getter method for entityId
		 * 
		 * @return entityId as String
		 */
		public String getEntityId() {
			return entityId;
		}

		/**
		 * Setter method for entityId
		 * 
		 * @param entityId
		 */
		public void setEntityId(String entityId) {
			this.entityId = entityId;
		}

	}

	/**
	 * Getter method for LastStartEntityTimeAsString
	 * 
	 * @return LastStartEntityTimeAsString as String
	 */
	public String getLastStartEntityTimeAsString() throws SwtException {
		/* to get the simple date format */
		this.lastStartEntityTimeAsString = SwtUtil
				.getDateAsString(lastStartEntityTime);

		return lastStartEntityTimeAsString;
	}

	/**
	 * Setter method for LastStartEntityTimeAsString
	 * 
	 * @param LastStartEntityTimeAsString
	 */
	public void setLastStartEntityTimeAsString(
			String lastStartEntityTimeAsString) {
		this.lastStartEntityTimeAsString = lastStartEntityTimeAsString;
	}

	/**
	 * Getter method for LastEndEntityTimeAsString
	 * 
	 * @return LastEndEntityTimeAsString as String
	 */
	public String getLastEndEntityTimeAsString() throws SwtException {
		/* to get the simple date format */
		this.lastEndEntityTimeAsString = SwtUtil
				.getDateAsString(lastEndEntityTime);

		return lastEndEntityTimeAsString;
	}

	/**
	 * Setter method for LastEndEntityTimeAsString
	 * 
	 * @param LastEndEntityTimeAsString
	 */
	public void setLastEndEntityTimeAsString(String lastEndEntityTimeAsString) {
		this.lastEndEntityTimeAsString = lastEndEntityTimeAsString;
	}

	/**
	 * Getter method for LastStartSystemTimeAsString
	 * 
	 * @return LastStartSystemTimeAsString as String
	 */
	public String getLastStartSystemTimeAsString() throws SwtException {
		/* to get the simple date format */
		this.lastStartSystemTimeAsString = SwtUtil
				.getDateAsString(lastStartSystemTime);
		return lastStartSystemTimeAsString;
	}

	/**
	 * Setter method for LastStartSystemTimeAsString
	 * 
	 * @param LastStartSystemTimeAsString
	 */
	public void setLastStartSystemTimeAsString(
			String lastStartSystemTimeAsString) {
		this.lastStartSystemTimeAsString = lastStartSystemTimeAsString;
	}

	/**
	 * Getter method for LastEndSystemTimeAsString
	 * 
	 * @return LastEndSystemTimeAsString as String
	 */
	public String getLastEndSystemTimeAsString() throws SwtException {
		/* to get the simple date format */
		this.lastEndSystemTimeAsString = SwtUtil
				.getDateAsString(lastEndSystemTime);

		return lastEndSystemTimeAsString;
	}

	/**
	 * Setter method for LastEndSystemTimeAsString
	 * 
	 * @param LastEndSystemTimeAsString
	 */
	public void setLastEndSystemTimeAsString(String lastEndSystemTimeAsString) {
		this.lastEndSystemTimeAsString = lastEndSystemTimeAsString;
	}

	/**
	 * Getter method for LastHeartBeatEntityTimeAsString
	 * 
	 * @return LastHeartBeatEntityTimeAsString as String
	 */
	public String getLastHeartBeatEntityTimeAsString() throws SwtException {
		/* to get the simple date format */
		this.lastHeartBeatEntityTimeAsString = SwtUtil
				.getDateAsString(lastHeartBeatEntityTime);
		return lastHeartBeatEntityTimeAsString;
	}

	/**
	 * Setter method for LastHeartBeatEntityTimeAsString
	 * 
	 * @param LastHeartBeatEntityTimeAsString
	 */
	public void setLastHeartBeatEntityTimeAsString(
			String lastHeartBeatEntityTimeAsString) {
		this.lastHeartBeatEntityTimeAsString = lastHeartBeatEntityTimeAsString;
	}

	/**
	 * Getter method for LastHeartBeatSystemTimeAsString
	 * 
	 * @return LastHeartBeatSystemTimeAsString as String
	 */
	public String getLastHeartBeatSystemTimeAsString() throws SwtException {
		/* to get the simple date format */
		this.lastHeartBeatSystemTimeAsString = SwtUtil
				.getDateAsString(lastHeartBeatSystemTime);
		return lastHeartBeatSystemTimeAsString;
	}

	/**
	 * Setter method for LastHeartBeatSystemTimeAsString
	 * 
	 * @param LastHeartBeatSystemTimeAsString
	 */
	public void setLastHeartBeatSystemTimeAsString(
			String lastHeartBeatSystemTimeAsString) {
		this.lastHeartBeatSystemTimeAsString = lastHeartBeatSystemTimeAsString;
	}

	/**
	 * Getter method for LastRunStatusDetails
	 * 
	 * @return LastRunStatusDetails as String
	 */
	public String getLastRunStatusDetails() throws SwtException {
		/* to get the Last Run Status for SwtConstants */
		this.lastRunStatusDetails = SwtConstants
				.getEntityprocessStatus(lastRunStatus);
		return lastRunStatusDetails;
	}

	/**
	 * Setter method for LastRunStatusDetails
	 * 
	 * @param LastRunStatusDetails
	 */
	public void setLastRunStatusDetails(String lastRunStatusDetails) {
		this.lastRunStatusDetails = lastRunStatusDetails;
	}

	/**
	 * Getter method for ProcessStatusDetails
	 * 
	 * @return ProcessStatusDetails as String
	 */
	public String getProcessStatusDetails() throws SwtException {
		/* to get the Current Status for SwtConstants */
		this.processStatusDetails = SwtConstants
				.getEntityprocessStatus(processStatus);
		return processStatusDetails;
	}

	/**
	 * Setter method for ProcessStatusDetails
	 * 
	 * @param ProcessStatusDetails
	 */
	public void setProcessStatusDetails(String processStatusDetails) {

		this.processStatusDetails = processStatusDetails;
	}

	/**
	 * Getter method for processStatus
	 * 
	 * @return processStatus as String
	 */
	public String getProcessStatus() {
		return processStatus;
	}

	/**
	 * Setter method for processStatus
	 * 
	 * @param processStatus
	 */
	public void setProcessStatus(String processStatus) {
		this.processStatus = processStatus;
	}

	/**
	 * Getter method for lastStartEntityTime
	 * 
	 * @return lastStartEntityTime as Date
	 */
	public Date getLastStartEntityTime() {
		return lastStartEntityTime;
	}

	/**
	 * Setter method for lastStartEntityTime
	 * 
	 * @param lastStartEntityTime
	 */
	public void setLastStartEntityTime(Date lastStartEntityTime) {
		this.lastStartEntityTime = lastStartEntityTime;
	}

	/**
	 * Getter method for lastEndEntityTime
	 * 
	 * @return lastEndEntityTime as Date
	 */
	public Date getLastEndEntityTime() {
		return lastEndEntityTime;
	}

	/**
	 * Setter method for lastEndEntityTime
	 * 
	 * @param lastEndEntityTime
	 */
	public void setLastEndEntityTime(Date lastEndEntityTime) {
		this.lastEndEntityTime = lastEndEntityTime;
	}

	/**
	 * Getter method for lastStartSystemTime
	 * 
	 * @return lastStartSystemTime as Date
	 */
	public Date getLastStartSystemTime() {
		return lastStartSystemTime;
	}

	/**
	 * Setter method for lastStartSystemTime
	 * 
	 * @param lastStartSystemTime
	 */
	public void setLastStartSystemTime(Date lastStartSystemTime) {
		this.lastStartSystemTime = lastStartSystemTime;
	}

	/**
	 * Getter method for lastEndSystemTime
	 * 
	 * @return lastEndSystemTime as Date
	 */
	public Date getLastEndSystemTime() {
		return lastEndSystemTime;
	}

	/**
	 * Setter method for lastEndSystemTime
	 * 
	 * @param lastEndSystemTime
	 */
	public void setLastEndSystemTime(Date lastEndSystemTime) {
		this.lastEndSystemTime = lastEndSystemTime;
	}

	/**
	 * Getter method for lastRunStatus
	 * 
	 * @return lastRunStatus as String
	 */
	public String getLastRunStatus() {
		return lastRunStatus;
	}

	/**
	 * Setter method for lastRunStatus
	 * 
	 * @param lastRunStatus
	 */
	public void setLastRunStatus(String lastRunStatus) {
		this.lastRunStatus = lastRunStatus;
	}

	/**
	 * Getter method for lastHeartBeatEntityTime
	 * 
	 * @return lastHeartBeatEntityTime as Date
	 */
	public Date getLastHeartBeatEntityTime() {
		return lastHeartBeatEntityTime;
	}

	/**
	 * Setter method for lastHeartBeatEntityTime
	 * 
	 * @param lastHeartBeatEntityTime
	 */
	public void setLastHeartBeatEntityTime(Date lastHeartBeatEntityTime) {
		this.lastHeartBeatEntityTime = lastHeartBeatEntityTime;
	}

	/**
	 * Getter method for lastHeartBeatSystemTime
	 * 
	 * @return lastHeartBeatSystemTime as Date
	 */
	public Date getLastHeartBeatSystemTime() {
		return lastHeartBeatSystemTime;
	}

	/**
	 * Setter method for lastHeartBeatSystemTime
	 * 
	 * @param lastHeartBeatSystemTime
	 */
	public void setLastHeartBeatSystemTime(Date lastHeartBeatSystemTime) {
		this.lastHeartBeatSystemTime = lastHeartBeatSystemTime;
	}

	/**
	 * Getter method for runOrder
	 * 
	 * @return runOrder as String
	 */
	public String getRunOrder() {
		return runOrder;
	}

	/**
	 * Setter method for runOrder
	 * 
	 * @param runOrder
	 */
	public void setRunOrder(String runOrder) {
		this.runOrder = runOrder;
	}

	/**
	 * Getter method for databaseSession
	 * 
	 * @return databaseSession as String
	 */
	public String getDatabaseSession() {
		return databaseSession;
	}

	/**
	 * Setter method for databaseSession
	 * 
	 * @param databaseSession
	 */
	public void setDatabaseSession(String databaseSession) {
		this.databaseSession = databaseSession;
	}

	/**
	 * Getter method for defaultRunTime
	 * 
	 * @return defaultRunTime as String
	 */
	public String getDefaultRunTime() {
		return defaultRunTime;
	}

	/**
	 * Setter method for defaultRunTime
	 * 
	 * @param defaultRunTime
	 */
	public void setDefaultRunTime(String defaultRunTime) {
		this.defaultRunTime = defaultRunTime;
	}

	/**
	 * Getter method for runTime
	 * 
	 * @return runTime as String
	 */
	public String getRunTime() {
		return runTime;
	}

	/**
	 * Setter method for runTime
	 * 
	 * @param runTime
	 */
	public void setRunTime(String runTime) {
		this.runTime = runTime;
	}

	/**
	 * Getter method for timeDisplay
	 * 
	 * @return timeDisplay as String
	 */
	public String getTimeDisplay() {
		return timeDisplay;
	}

	/**
	 * Setter method for timeDisplay
	 * 
	 * @param timeDisplay
	 */
	public void setTimeDisplay(String timeDisplay) {
		this.timeDisplay = timeDisplay;
	}

	/**
	 * Getter method for id
	 * 
	 * @return id as Id
	 */
	public Id getId() {
		return id;
	}

	/**
	 * Setter method for id
	 * 
	 * @param id
	 */
	public void setId(Id id) {
		this.id = id;
	}

}
