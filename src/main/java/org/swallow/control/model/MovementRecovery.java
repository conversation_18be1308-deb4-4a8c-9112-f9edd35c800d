/*
 * @(#)MovementRecovery.java 01/05/06
 * 
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 * 
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */

package org.swallow.control.model;

import java.util.Date;
import org.swallow.model.BaseObject;
import org.swallow.work.model.Movement;



public class MovementRecovery extends BaseObject implements org.swallow.model.AuditComponent{
	
	private Id id = new Id();	
	
	private String movmentIdAsString;
	private String updateUser;	
	private Date updateDate;
	private String lockTime;
	private Integer positionLevel;
	private String positionLevelAsString;
	private Date valueDate;
	private String valueDateAsString;
	private Double amount;
	private String amountAsString = "";
	private String currencyCode;
	private String accountId;
	private String bookCode;
	private String status;
	private String matchStatusDesc;
	private String entityId;
	private Date inputDate;
	private String inputDateAsString = "";
	/**
	 * @return Returns the matchStatusDesc.
	 */
	public String getMatchStatusDesc() {
		return matchStatusDesc;
	}
	/**
	 * @param matchStatusDesc The matchStatusDesc to set.
	 */
	public void setMatchStatusDesc(String matchStatusDesc) {
		this.matchStatusDesc = matchStatusDesc;
	}
	/**
	 * @return Returns the accountId.
	 */
	public String getAccountId() {
		return accountId;
	}
	/**
	 * @param accountId The accountId to set.
	 */
	public void setAccountId(String accountId) {
		this.accountId = accountId;
	}
	/**
	 * @return Returns the amount.
	 */
	public Double getAmount() {
		return amount;
	}
	/**
	 * @param amount The amount to set.
	 */
	public void setAmount(Double amount) {
		this.amount = amount;
	}
	/**
	 * @return Returns the amountAsString.
	 */
	public String getAmountAsString() {
		return amountAsString;
	}
	/**
	 * @param amountAsString The amountAsString to set.
	 */
	public void setAmountAsString(String amountAsString) {
		this.amountAsString = amountAsString;
	}
	/**
	 * @return Returns the bookCode.
	 */
	public String getBookCode() {
		return bookCode;
	}
	/**
	 * @param bookCode The bookCode to set.
	 */
	public void setBookCode(String bookCode) {
		this.bookCode = bookCode;
	}
	/**
	 * @return Returns the currencyCode.
	 */
	public String getCurrencyCode() {
		return currencyCode;
	}
	/**
	 * @param currencyCode The currencyCode to set.
	 */
	public void setCurrencyCode(String currencyCode) {
		this.currencyCode = currencyCode;
	}
	/**
	 * @return Returns the lockTime.
	 */
	public String getLockTime() {
		return lockTime;
	}
	/**
	 * @param lockTime The lockTime to set.
	 */
	public void setLockTime(String lockTime) {
		this.lockTime = lockTime;
	}
	/**
	 * @return Returns the movmentIdAsString.
	 */
	public String getMovmentIdAsString() {
		return movmentIdAsString;
	}
	/**
	 * @param movmentIdAsString The movmentIdAsString to set.
	 */
	public void setMovmentIdAsString(String movmentIdAsString) {
		this.movmentIdAsString = movmentIdAsString;
	}
	/**
	 * @return Returns the positionLevel.
	 */
	public Integer getPositionLevel() {
		return positionLevel;
	}
	/**
	 * @param positionLevel The positionLevel to set.
	 */
	public void setPositionLevel(Integer positionLevel) {
		this.positionLevel = positionLevel;
	}
	/**
	 * @return Returns the positionLevelAsString.
	 */
	public String getPositionLevelAsString() {
		return positionLevelAsString;
	}
	/**
	 * @param positionLevelAsString The positionLevelAsString to set.
	 */
	public void setPositionLevelAsString(String positionLevelAsString) {
		this.positionLevelAsString = positionLevelAsString;
	}
	/**
	 * @return Returns the status.
	 */
	public String getStatus() {
		return status;
	}
	/**
	 * @param status The status to set.
	 */
	public void setStatus(String status) {
		this.status = status;
	}
	/**
	 * @return Returns the valueDate.
	 */
	public Date getValueDate() {
		return valueDate;
	}
	/**
	 * @param valueDate The valueDate to set.
	 */
	public void setValueDate(Date valueDate) {
		this.valueDate = valueDate;
	}
	/**
	 * @return Returns the valueDateAsString.
	 */
	public String getValueDateAsString() {
		return valueDateAsString;
	}
	/**
	 * @param valueDateAsString The valueDateAsString to set.
	 */
	public void setValueDateAsString(String valueDateAsString) {
		this.valueDateAsString = valueDateAsString;
	}
	
	
	
	
	
	public static class Id extends BaseObject{
		
		private String hostId ;
		private Long movementId;
		
		
		
		
		/**
		 * @return Returns the hostId.
		 */
		public String getHostId() {
			return hostId;
		}
		/**
		 * @param hostId The hostId to set.
		 */
		public void setHostId(String hostId) {
			this.hostId = hostId;
		}
		/**
		 * @return Returns the movementId.
		 */
		public Long getMovementId() {
			return movementId;
		}
		/**
		 * @param movementId The movementId to set.
		 */
		public void setMovementId(Long movementId) {
			this.movementId = movementId;
		}
	}

	
	/**
	 * @return Returns the id.
	 */
	public Id getId() {
		return id;
	}
	/**
	 * @param id The id to set.
	 */
	public void setId(Id id) {
		this.id = id;
	}

	/**
	 * @return Returns the updateDate.
	 */
	public Date getUpdateDate() {
		return updateDate;
	}
	/**
	 * @param updateDate The updateDate to set.
	 */
	public void setUpdateDate(Date updateDate) {
		this.updateDate = updateDate;
	}
	/**
	 * @return Returns the updateUser.
	 */
	public String getUpdateUser() {
		return updateUser;
	}
	/**
	 * @param updateUser The updateUser to set.
	 */
	public void setUpdateUser(String updateUser) {
		this.updateUser = updateUser;
	}
	/**
	 * @return Returns the entityId.
	 */
	public String getEntityId() {
		return entityId;
	}
	/**
	 * @param entityId The entityId to set.
	 */
	public void setEntityId(String entityId) {
		this.entityId = entityId;
	}
	/**
	 * @return Returns the inputDate.
	 */
	public Date getInputDate() {
		return inputDate;
	}
	/**
	 * @param inputDate The inputDate to set.
	 */
	public void setInputDate(Date inputDate) {
		this.inputDate = inputDate;
	}
	/**
	 * @return Returns the inputDateAsString.
	 */
	public String getInputDateAsString() {
		return inputDateAsString;
	}
	/**
	 * @param inputDateAsString The inputDateAsString to set.
	 */
	public void setInputDateAsString(String inputDateAsString) {
		this.inputDateAsString = inputDateAsString;
	}
}
