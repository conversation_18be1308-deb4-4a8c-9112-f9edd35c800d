<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE hibernate-mapping PUBLIC
        "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
        "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">

<hibernate-mapping>
    <class name="org.swallow.control.model.WorkQAccess" table="P_WORKQ_ACCESS">
		<composite-id name="id" class="org.swallow.control.model.WorkQAccess$Id" unsaved-value="any">
		
		<key-property name="roleId" access="field" column="ROLE_ID" />
		<key-property name="hostId" access="field" column="HOST_ID"/>
        <key-property name="entityId" access="field" column="ENTITY_ID" />
        <key-property name="currencyCode" access="field" column="CURRENCY_CODE" />
		
		</composite-id>
	
		<property name="offerMatchQualA" column="OFFER_MATCH_QUALITY_A" not-null="false"/>	
		<property name="offerMatchQualB" column="OFFER_MATCH_QUALITY_B" not-null="false"/>	
		<property name="offerMatchQualC" column="OFFER_MATCH_QUALITY_C" not-null="false"/>	
		<property name="offerMatchQualD" column="OFFER_MATCH_QUALITY_D" not-null="false"/>	
		<property name="offerMatchQualE" column="OFFER_MATCH_QUALITY_E" not-null="false"/>	
		
		<property name="suspMatchQualA" column="SUSPPEND_MATCH_QUALITY_A" not-null="false"/>	
		<property name="suspMatchQualB" column="SUSPPEND_MATCH_QUALITY_B" not-null="false"/>	
		<property name="suspMatchQualC" column="SUSPPEND_MATCH_QUALITY_C" not-null="false"/>	
		<property name="suspMatchQualD" column="SUSPPEND_MATCH_QUALITY_D" not-null="false"/>	
		<property name="suspMatchQualE" column="SUSPPEND_MATCH_QUALITY_E" not-null="false"/>	
		<property name="suspMatchQualZ" column="SUSPPEND_MATCH_QUALITY_Z" not-null="false"/>	
		
		<property name="confirmMatchQualA" column="CONFIRM_MATCH_QUALITY_A" not-null="false"/>	
		<property name="confirmMatchQualB" column="CONFIRM_MATCH_QUALITY_B" not-null="false"/>	
		<property name="confirmMatchQualC" column="CONFIRM_MATCH_QUALITY_C" not-null="false"/>	
		<property name="confirmMatchQualD" column="CONFIRM_MATCH_QUALITY_D" not-null="false"/>	
		<property name="confirmMatchQualE" column="CONFIRM_MATCH_QUALITY_E" not-null="false"/>	
		<property name="confirmMatchQualZ" column="CONFIRM_MATCH_QUALITY_Z" not-null="false"/>	
		
		<property name="outstanding" column="OUTSTANDING" not-null="false"/>
		
		
		<property name="updateDate" column="UPDATE_DATE" not-null="false"/>	
		<property name="updateUser" column="UPDATE_USER" not-null="false"/>	
		



    </class>
</hibernate-mapping>
