<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE hibernate-mapping PUBLIC
        "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
        "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">

<hibernate-mapping>
 <class name="org.swallow.control.model.JobStatus" table="S_JOB_STATUS">
 
	<id name="scheduleId" column="SCHEDULE_ID">
			<generator class="assigned" />
	</id>
	
  <property name="hostId" column="HOST_ID" not-null="true"/>
  <property name="jobId" column="JOB_ID" not-null="true"/>
  <property name="lastExecTime" column="LAST_EXECUTION_TIME" not-null="false"/>
  <property name="lastExecStatus" column="LAST_EXECUTION_STATUS" not-null="false"/>
  <property name="nextExecTime" column="NEXT_EXECUTION_TIME" not-null="false"/>
  <property name="currentStatus" column="CURRENT_STATUS" not-null="false"/>  
  <property name="updateDate" column="UPDATE_DATE" not-null="false"/>
  <property name="updateUser" column="UPDATE_USER" not-null="false"/>
    
          <many-to-one lazy="false"
				name="job"
				update="false"
				insert="false"
				cascade="none"
				class="org.swallow.control.model.Job"
				not-null="true"
				outer-join="true"
				foreign-key="FK_S_JOB_STATUS_S_JOB">
				<column name="HOST_ID"/>
				<column name="JOB_ID"/>
		</many-to-one>		
		
		
 </class>
</hibernate-mapping>