<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE hibernate-mapping PUBLIC
        "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
        "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">

<hibernate-mapping>
    <class name="org.swallow.control.model.InputInterfaceSBeanProperty" table="I_INTERFACE_SBEAN_PROPERTIES">
		<composite-id class="org.swallow.control.model.InputInterfaceSBeanProperty$Id" name="id" >
		 	 	<key-property name="interfaceId" access="field" column="INTERFACEID"/>
		  	 	<key-property name="beanId" access="field" column="BEANID"/>
		  	 	<key-property name="name" access="field" column="PROPERTY_NAME"/>
		</composite-id>
		
		<property name="value" column="PROPERTY_VALUE" not-null="false" />
		
	  </class>
</hibernate-mapping>
