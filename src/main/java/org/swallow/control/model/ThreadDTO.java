package org.swallow.control.model;

import java.io.Serializable;
import java.util.Date;

import org.swallow.exception.SwtException;

/**
 * Thread Data Transfer Object bean
 * 
 * <AUTHOR>
 */
public class ThreadDTO implements Serializable {
	/**
	 * Tristates are implemented as -1/0/1 where -1 is the initialised state
	 */
	
	private boolean proc_implemented = false;
	private int proc_running = -1;
	private Date proc_lastStarted = null;
	private long proc_lastExecutionTime = -1;
	private int proc_lastResult = -1;
	private String proc_lastException;
	
	private boolean fm_implemented = false;
	private Date fm_lastMessageCommitted = null;
	private int fm_commitsPerHour = 0;
	private int fm_active = -1;
	private int fm_busy = -1;
	
	private boolean thread_implemented = false;
	private int thread_heartbeat = -1;
	private String thread_name = null;
	private String thread_interface = null;
	private String thread_functionalid = null;
	
	private boolean engine_implemented = false;
	private int engine_active = -1;
	
	public int getEngine_active() {
		return engine_active;
	}
	public void setEngine_active(int engine_active) {
		this.engine_active = engine_active;
	}
	public boolean isProc_implemented() {
		return proc_implemented;
	}
	public void setProc_implemented(boolean proc_implemented) {
		this.proc_implemented = proc_implemented;
	}
	public int getProc_running() {
		return proc_running;
	}
	public void setProc_running(int proc_isRunning) {
		this.proc_running = proc_isRunning;
	}
	public Date getProc_lastStarted() {
		return proc_lastStarted;
	}
	public void setProc_lastStarted(Date proc_lastStarted) {
		this.proc_lastStarted = proc_lastStarted;
	}
	public long getProc_lastExecutionTime() {
		return proc_lastExecutionTime;
	}
	public void setProc_lastExecutionTime(long proc_lastExecutionTime) {
		this.proc_lastExecutionTime = proc_lastExecutionTime;
	}
	public int getProc_lastResult() {
		return proc_lastResult;
	}
	public void setProc_lastResult(int proc_lastResult) {
		this.proc_lastResult = proc_lastResult;
	}
	public boolean isFm_implemented() {
		return fm_implemented;
	}
	public void setFm_implemented(boolean fm_implemented) {
		this.fm_implemented = fm_implemented;
	}
	public Date getFm_lastMessageCommitted() {
		return fm_lastMessageCommitted;
	}
	public void setFm_lastMessageCommitted(Date fm_lastMessageCommitted) {
		this.fm_lastMessageCommitted = fm_lastMessageCommitted;
	}
	public int getFm_commitsPerHour() {
		return fm_commitsPerHour;
	}
	public void setFm_commitsPerHour(int fm_commitsPerHour) {
		this.fm_commitsPerHour = fm_commitsPerHour;
	}
	public boolean isThread_implemented() {
		return thread_implemented;
	}
	public void setThread_implemented(boolean hb_implemented) {
		this.thread_implemented = hb_implemented;
	}
	public int getThread_heartbeat() {
		return thread_heartbeat;
	}
	public void setThread_heartbeat(int hb_check) {
		this.thread_heartbeat = hb_check;
	}
	public String getThread_name() {
		return thread_name;
	}
	public void setThread_name(String hb_name) {
		this.thread_name = hb_name;
	}
	public String getThread_interface() {
		return thread_interface;
	}
	public void setThread_interface(String hb_interface) {
		this.thread_interface = hb_interface;
	}
	public int getFm_active() {
		return fm_active;
	}
	public void setFm_active(int fm_active) {
		this.fm_active = fm_active;
	}
	public int getFm_busy() {
		return fm_busy;
	}
	public void setFm_busy(int fm_busy) {
		this.fm_busy = fm_busy;
	}
	public String getThread_functionalid() {
		return thread_functionalid;
	}
	public void setThread_functionalid(String thread_functionalid) {
		this.thread_functionalid = thread_functionalid;
	}
	public String getProc_lastException() {
		return proc_lastException;
	}
	public void setProc_lastException(String proc_lastException) {
		this.proc_lastException = proc_lastException;
	}
	public boolean isEngine_implemented() {
		return engine_implemented;
	}
	public void setEngine_implemented(boolean engine_implemented) {
		this.engine_implemented = engine_implemented;
	}
}