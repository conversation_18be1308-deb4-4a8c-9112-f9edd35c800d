<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE hibernate-mapping PUBLIC
        "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
        "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">

<hibernate-mapping>
    <class name="org.swallow.control.model.Password" table="S_PASSWORD_RULES">
		<composite-id name="id" class="org.swallow.control.model.Password$Id" unsaved-value="any">
        	<key-property name="hostId" access="field" column="HOST_ID"/>
		</composite-id>
		
		<property name="alphaChar" column="ALPHA_CHAR_MIN" not-null="false"/>	
		<property name="numericChar" column="NUMERIC_CHAR_MIN" not-null="false"/>	
		<property name="specialChar" column="SPECIAL_CHAR_MIN" not-null="false"/>	
		<property name="minLength" column="MIN_LENGTH" not-null="false"/>	
		<property name="maxLength" column="MAX_LENGTH" not-null="false"/>	
		<property name="expireDays" column="EXPIRE_DAYS" not-null="false"/>	
		<property name="recentUserpwd" column="RECENT_USED_PASSWD" not-null="false"/>	
		<property name="unsuccLoginAttempt" column="UNSUCC_LOGIN_ATTEMPT" not-null="false"/>	
		<property name="updateDate" column="UPDATE_DATE" not-null="false"/>	
		<property name="updateUser" column="UPDATE_USER" not-null="false"/>	
		<property name="expiryDaysNotice" column="EXPIRE_DAYS_NOTICE" not-null="false"/>
		<property name="mixedCase" column="MIXED_CASE" not-null="false"/>
		
    </class>
</hibernate-mapping>
