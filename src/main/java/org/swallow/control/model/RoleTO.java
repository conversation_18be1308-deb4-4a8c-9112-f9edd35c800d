/*
 * @(#)RoleTO.java 1.0 06/07/03
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.control.model;

import java.io.Serializable;

/**
 * This class is immutable class and contains the Role Id.
 *
 * <AUTHOR> Systems
 * @version 1.0
 */
public class RoleTO implements Serializable{
    /** Holds the Role Id */
    private String roleId = "";

    /**
     * Creates a new RoleTO object.
     *
     * @param roleId Role Id
     */
    public RoleTO(String roleId) {
        this.roleId = roleId;
    }

    /**
     * Returns the Role Id
     *
     * @return Returns the roleId
     */
    public String getRoleId() {
        return roleId;
    }

    /**
     * This function overrides the equals function of Object class.
     *
     * @param obj Object to be compared
     *
     * @return true/false
     */
    public boolean equals(Object obj) {
        boolean retValue = false;

        if ((obj != null) && obj instanceof RoleTO) {
            retValue = roleId.equals((( RoleTO ) obj).getRoleId());
        }

        return retValue;
    }

    /**
     * This function overrides the hashCode function of Object class.
     *
     * @return Retunrs the Hash Code of object.
     */
    public int hashCode() {
        return roleId.hashCode();
    }
}
