/*
 * @(#)InterfaceMonitor.java 1.0 15/06/2011
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.control.model;

import java.sql.Clob;
import java.util.Date;

import org.swallow.model.BaseObject;

/**
 * InterfaceMonitor.java<br>
 * 
 * This java bean class has getters and setters for the Interface Monitor screen
 * details.<br>
 * 
 * @extends BaseObject<br>
 * 
 * <AUTHOR> Marshal <PERSON>
 * @Date 23-June-2011
 */
public class InterfaceMonitor extends BaseObject {
	private Id id = new Id();
	// Holds the Message Type
	private String messageType = null;
	// Holds the Message Status
	private String messageStatus = null;
	// Holds the Message Text
	private Clob messageText = null;
	// Holds the Interface Id
	private String interfaceId = null;
	// Holds the Original Id
	private String originalId = null;
	// Holds the Input Date
	private Date inputDate = null;
	// Holds the Start Date
	private Date startDate = null;
	// Holds the End Date
	private Date endDate = null;
	// Holds the Input Session
	private String inputSession = null;
	// Holds the Current Session
	private String currentSession = null;
	// Holds the Unit
	private String unit = null;
	// Holds the Status Note
	private Clob statusNote = null;
	// Holds the Source Type
	private String sourceType = null;
	// Holds the Message Format
	private String messageFormat = null;
	// Holds the Message Status Count
	private String messageStatusCount = null;
	// Holds the Message Status
	private String status = null;
	// Holds the date the message received lastly
	private String lastRecievedDate = null;
	// Holds the Interface active value
	private String isActiveInterface = null;
	// Holds the HeartBeat result
	private String heartBeatResult = null;
	/* Start: Added for 1053_STL_069 by Marshal on 19-Oct-2011 */
	// Holds the count of total messages for the given Interface
	private String totalMessages = null;
	// Holds the count of messages that are processed
	private String processedMsgs = null;
	// Holds the count of messages that are in awaiting state
	private String awaitingMsgs = null;
	// Holds the count of messages that are in filtered state
	private String filteredMsgs = null;
	// Holds the count of messages that are in bad state (messages not parsed by
	// Input engine)
	private String badMessages = null;

	/* End: Added for 1053_STL_069 by Marshal on 19-Oct-2011 */

	// This is the static inner class of InterfaceMonitor class
	public static class Id extends BaseObject {
		// Holds the Module Id
		private String moduleId = null;
		// Holds the Message Id
		private String messageId = null;

		// Non parameterized Constructor
		public Id() {
		}

		// Parameterized Constructor for Id class
		public Id(String modId, String msgId) {
			// Assigns modId to moduleId
			this.moduleId = modId;
			// Assigns msgId to messageId
			this.messageId = msgId;
		}

		/**
		 * Getter method for moduleId
		 * 
		 * @return moduleId as String
		 */
		public String getModuleId() {
			return moduleId;
		}

		/**
		 * Setter method for moduleId
		 * 
		 * @param moduleId
		 */
		public void setModuleId(String moduleId) {
			this.moduleId = moduleId;
		}

		/**
		 * Getter method for messageId
		 * 
		 * @return messageId as String
		 */

		public String getMessageId() {
			return messageId;
		}

		/**
		 * Setter method for messageId
		 * 
		 * @param messageId
		 */
		public void setMessageId(String messageId) {
			this.messageId = messageId;
		}
	}

	public Id getId() {
		return id;
	}

	public void setId(Id id) {
		this.id = id;
	}

	/**
	 * Getter method for messageType
	 * 
	 * @return messageType as String
	 */

	public String getMessageType() {
		return messageType;
	}

	/**
	 * Setter method for messageType
	 * 
	 * @param messageType
	 */
	public void setMessageType(String messageType) {
		this.messageType = messageType;
	}

	/**
	 * Getter method for messageStatus
	 * 
	 * @return messageStatus as String
	 */

	public String getMessageStatus() {
		return messageStatus;
	}

	/**
	 * Setter method for messageStatus
	 * 
	 * @param messageStatus
	 */
	public void setMessageStatus(String messageStatus) {
		this.messageStatus = messageStatus;
	}

	/**
	 * Getter method for messageText
	 * 
	 * @return messageText as Clob
	 */

	public Clob getMessageText() {
		return messageText;
	}

	/**
	 * Setter method for messageText
	 * 
	 * @param messageText
	 */
	public void setMessageText(Clob messageText) {
		this.messageText = messageText;
	}

	/**
	 * Getter method for interfaceId
	 * 
	 * @return interfaceId as String
	 */

	public String getInterfaceId() {
		return interfaceId;
	}

	/**
	 * Setter method for interfaceId
	 * 
	 * @param interfaceId
	 */
	public void setInterfaceId(String interfaceId) {
		this.interfaceId = interfaceId;
	}

	/**
	 * Getter method for originalId
	 * 
	 * @return originalId as String
	 */

	public String getOriginalId() {
		return originalId;
	}

	/**
	 * Setter method for originalId
	 * 
	 * @param originalId
	 */
	public void setOriginalId(String originalId) {
		this.originalId = originalId;
	}

	/**
	 * Getter method for inputDate
	 * 
	 * @return inputDate as Date
	 */

	public Date getInputDate() {
		return inputDate;
	}

	/**
	 * Setter method for inputDate
	 * 
	 * @param inputDate
	 */
	public void setInputDate(Date inputDate) {
		this.inputDate = inputDate;
	}

	/**
	 * Getter method for startDate
	 * 
	 * @return startDate as Date
	 */

	public Date getStartDate() {
		return startDate;
	}

	/**
	 * Setter method for startDate
	 * 
	 * @param startDate
	 */
	public void setStartDate(Date startDate) {
		this.startDate = startDate;
	}

	/**
	 * Getter method for endDate
	 * 
	 * @return endDate as Date
	 */

	public Date getEndDate() {
		return endDate;
	}

	/**
	 * Setter method for endDate
	 * 
	 * @param endDate
	 */
	public void setEndDate(Date endDate) {
		this.endDate = endDate;
	}

	/**
	 * Getter method for inputSession
	 * 
	 * @return inputSession as String
	 */

	public String getInputSession() {
		return inputSession;
	}

	/**
	 * Setter method for inputSession
	 * 
	 * @param inputSession
	 */
	public void setInputSession(String inputSession) {
		this.inputSession = inputSession;
	}

	/**
	 * Getter method for currentSession
	 * 
	 * @return currentSession as String
	 */

	public String getCurrentSession() {
		return currentSession;
	}

	/**
	 * Setter method for currentSession
	 * 
	 * @param currentSession
	 */
	public void setCurrentSession(String currentSession) {
		this.currentSession = currentSession;
	}

	/**
	 * Getter method for unit
	 * 
	 * @return unit as String
	 */

	public String getUnit() {
		return unit;
	}

	/**
	 * Setter method for unit
	 * 
	 * @param unit
	 */
	public void setUnit(String unit) {
		this.unit = unit;
	}

	/**
	 * Getter method for statusNote
	 * 
	 * @return statusNote as Clob
	 */

	public Clob getStatusNote() {
		return statusNote;
	}

	/**
	 * Setter method for statusNote
	 * 
	 * @param statusNote
	 */
	public void setStatusNote(Clob statusNote) {
		this.statusNote = statusNote;
	}

	/**
	 * Getter method for sourceType
	 * 
	 * @return sourceType as String
	 */

	public String getSourceType() {
		return sourceType;
	}

	/**
	 * Setter method for sourceType
	 * 
	 * @param sourceType
	 */
	public void setSourceType(String sourceType) {
		this.sourceType = sourceType;
	}

	/**
	 * Getter method for messageFormat
	 * 
	 * @return messageFormat as String
	 */

	public String getMessageFormat() {
		return messageFormat;
	}

	/**
	 * Setter method for messageFormat
	 * 
	 * @param messageFormat
	 */
	public void setMessageFormat(String messageFormat) {
		this.messageFormat = messageFormat;
	}

	/**
	 * Getter method for messageStatusCount
	 * 
	 * @return messageStatusCount as String
	 */

	public String getMessageStatusCount() {
		return messageStatusCount;
	}

	/**
	 * Setter method for messageStatusCount
	 * 
	 * @param messageStatusCount
	 */
	public void setMessageStatusCount(String messageStatusCount) {
		this.messageStatusCount = messageStatusCount;
	}

	/**
	 * Getter method for status
	 * 
	 * @return status as String
	 */

	public String getStatus() {
		return status;
	}

	/**
	 * Setter method for status
	 * 
	 * @param status
	 */
	public void setStatus(String status) {
		this.status = status;
	}

	/**
	 * Getter method for lastRecievedDate
	 * 
	 * @return lastRecievedDate as String
	 */

	public String getLastRecievedDate() {
		return lastRecievedDate;
	}

	/**
	 * Setter method for lastRecievedDate
	 * 
	 * @param lastRecievedDate
	 */
	public void setLastRecievedDate(String lastRecievedDate) {
		this.lastRecievedDate = lastRecievedDate;
	}

	/**
	 * Getter method for isActiveInterface
	 * 
	 * @return isActiveInterface as String
	 */

	public String getIsActiveInterface() {
		return isActiveInterface;
	}

	/**
	 * Setter method for isActiveInterface
	 * 
	 * @param isActiveInterface
	 */
	public void setIsActiveInterface(String isActiveInterface) {
		this.isActiveInterface = isActiveInterface;
	}

	/**
	 * Getter method for heartBeatResult
	 * 
	 * @return heartBeatResult as String
	 */

	public String getHeartBeatResult() {
		return heartBeatResult;
	}

	/**
	 * Setter method for heartBeatResult
	 * 
	 * @param heartBeatResult
	 */
	public void setHeartBeatResult(String heartBeatResult) {
		this.heartBeatResult = heartBeatResult;
	}

	/* Start: Added for 1053_STL_069 by Marshal on 19-Oct-2011 */

	/**
	 * Getter method for totalMessages
	 * 
	 * @return the totalMessages
	 */
	public String getTotalMessages() {
		return totalMessages;
	}

	/**
	 * Setter method for totalMessages
	 * 
	 * @param totalMessages
	 *            the totalMessages to set
	 */
	public void setTotalMessages(String totalMessages) {
		this.totalMessages = totalMessages;
	}

	/**
	 * Getter method for processedMsgs
	 * 
	 * @return the processedMsgs
	 */
	public String getProcessedMsgs() {
		return processedMsgs;
	}

	/**
	 * Setter method for processedMsgs
	 * 
	 * @param processedMsgs
	 *            the processedMsgs to set
	 */
	public void setProcessedMsgs(String processedMsgs) {
		this.processedMsgs = processedMsgs;
	}

	/**
	 * Getter method for awaitingMsgs
	 * 
	 * @return the awaitingMsgs
	 */
	public String getAwaitingMsgs() {
		return awaitingMsgs;
	}

	/**
	 * Setter method for awaitingMsgs
	 * 
	 * @param awaitingMsgs
	 *            the awaitingMsgs to set
	 */
	public void setAwaitingMsgs(String awaitingMsgs) {
		this.awaitingMsgs = awaitingMsgs;
	}

	/**
	 * Getter method for filteredMsgs
	 * 
	 * @return the filteredMsgs
	 */
	public String getFilteredMsgs() {
		return filteredMsgs;
	}

	/**
	 * Setter method for filteredMsgs
	 * 
	 * @param filteredMsgs
	 *            the filteredMsgs to set
	 */
	public void setFilteredMsgs(String filteredMsgs) {
		this.filteredMsgs = filteredMsgs;
	}

	/**
	 * Getter method for badMessages
	 * 
	 * @return the badMessages
	 */
	public String getBadMessages() {
		return badMessages;
	}

	/**
	 * Setter method for badMessages
	 * 
	 * @param badMessages
	 *            the badMessages to set
	 */
	public void setBadMessages(String badMessages) {
		this.badMessages = badMessages;
	}
	/* End: Added for 1053_STL_069 by Marshal on 19-Oct-2011 */

}
