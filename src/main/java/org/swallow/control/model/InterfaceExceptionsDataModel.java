/*
 * @(#)InterfaceExceptionsDataModel.java 1.0 15/06/2011
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.control.model;

import java.sql.Clob;

import org.swallow.model.BaseObject;

/**
 * This class has getters and setters related to Interface Exceptions screen.<br>
 * 
 * Author I. Marshal<br>
 * Date: 25-June-2011
 */
public class InterfaceExceptionsDataModel extends BaseObject {

	// Holds the Message Id
	private String messageId = null;
	// Holds the Interface Exceptions Details
	private String exception = null;
	// Holds the Interface Exceptions Description
	private String description = null;
	// Holds the Input Date
	private String inputDate = null;
	// Holds the Message Body in CLOB format
	private Clob messageBody = null;

	/**
	 * Getter method for messageId
	 * 
	 * @return messageId as String
	 */

	public String getMessageId() {
		return messageId;
	}

	/**
	 * Setter method for messageId
	 * 
	 * @param messageId
	 */
	public void setMessageId(String messageId) {
		this.messageId = messageId;
	}

	/**
	 * Getter method for exception
	 * 
	 * @return exception as String
	 */

	public String getException() {
		return exception;
	}

	/**
	 * Setter method for exception
	 * 
	 * @param exception
	 */
	public void setException(String exception) {
		this.exception = exception;
	}

	/**
	 * Getter method for description
	 * 
	 * @return description as String
	 */

	public String getDescription() {
		return description;
	}

	/**
	 * Setter method for description
	 * 
	 * @param description
	 */
	public void setDescription(String description) {
		this.description = description;
	}

	/**
	 * Getter method for inputDate
	 * 
	 * @return inputDate as String
	 */

	public String getInputDate() {
		return inputDate;
	}

	/**
	 * Setter method for inputDate
	 * 
	 * @param inputDate
	 */
	public void setInputDate(String inputDate) {
		this.inputDate = inputDate;
	}

	/**
	 * Getter method for messageBody
	 * 
	 * @return messageBody as Clob
	 */

	public Clob getMessageBody() {
		return messageBody;
	}

	/**
	 * Setter method for messageBody
	 * 
	 * @param messageBody
	 */
	public void setMessageBody(Clob messageBody) {
		this.messageBody = messageBody;
	}

}
