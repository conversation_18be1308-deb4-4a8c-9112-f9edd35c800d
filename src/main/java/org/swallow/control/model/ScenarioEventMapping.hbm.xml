<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE hibernate-mapping PUBLIC
        "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
        "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">

<hibernate-mapping>
    <class name="org.swallow.control.model.ScenarioEventMapping" table="P_SCENARIO_EVENT_MAPPING" >

	<id name="mapKey" type="long" column="MAP_KEY">
		<generator class="sequence">
			<param name="sequence_name">SEQ_P_SCEN_MAP</param>
		</generator>
	</id>
		<property name="scenarioId" access="field" column="SCENARIO_ID"/>
		<property name="eventFacilityId" access="field" column="EVENT_FACILITY_ID"/>		
		<property name="ordinal" column="ORDINAL" not-null="false"/>
		<property name="executeWhen" column="EXECUTE_WHEN" not-null="false"/>
		<property name="parametersXml" column="PARAMETERS_XML" not-null="false"/>
		<property name="repeatOnReraise" column="REPEAT_ON_RERAISE" not-null="false"/>
	    <property name="userDescription" column="DESCRIPTION" not-null="false"/>
	</class>
</hibernate-mapping>
