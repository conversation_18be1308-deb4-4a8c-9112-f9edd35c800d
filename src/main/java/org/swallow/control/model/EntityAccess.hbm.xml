<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE hibernate-mapping PUBLIC
        "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
        "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">

<hibernate-mapping>
    <class name="org.swallow.control.model.EntityAccess" table="S_ENTITY_ACCESS">

		<composite-id name="id" class="org.swallow.control.model.EntityAccess$Id" unsaved-value="any">
			 <key-property name="roleId" access="field" column="ROLE_ID" />
			 <key-property name="hostId" access="field" column="HOST_ID"/>
			 <key-property name="entityId" access="field" column="ENTITY_ID"/>
		</composite-id>
		
		<property name="accessId" column="ACCESS_ID" not-null="false"/>	
		<property name="updateDate" column="UPDATE_DATE" not-null="false"/>	
		<property name="updateUser" column="UPDATE_USER" not-null="false"/>

	    </class>
</hibernate-mapping>
