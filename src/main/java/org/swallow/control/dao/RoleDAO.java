/*
 * Created on Dec 21, 2005
 *
 * TODO To change the template for this generated file go to
 * Window - Preferences - Java - Code Style - Code Templates
 */
package org.swallow.control.dao;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;

import javax.servlet.http.HttpServletRequest;

import org.swallow.control.model.CurrencyGroupAccess;
import org.swallow.control.model.EntityAccess;
import org.swallow.control.model.FacilityAccess;
import org.swallow.control.model.Role;
import org.swallow.control.model.SweepLimits;
import org.swallow.control.model.WorkQAccess;
import org.swallow.dao.DAO;
import org.swallow.exception.SwtException;
import org.swallow.maintenance.model.LocationAccess;
import org.swallow.model.MenuAccess;

import net.sf.jasperreports.engine.JasperPrint;
/**
 * <AUTHOR>
 *
 * TODO To change the template for this generated type comment go to
 * Window - Preferences - Java - Code Style - Code Templates
 */
public interface RoleDAO extends DAO{
	public Collection getRoleDetails()  throws SwtException;
	public void saveRoleDetails(Role role) throws SwtException;
	public void updateRoleDetails(Role role)throws SwtException;
	public void deleteRoleDetails(Role role)throws SwtException;
	public Collection getMenuItemDetails() throws SwtException;
	public Collection getEntityDetails(String hostId) throws SwtException;
	public void saveMenuAccessDetails(MenuAccess menuAccess)throws SwtException;
	public void saveEntityAccessDetails(EntityAccess entityAccess)throws SwtException;
	public void updateMenuAccessDetails(MenuAccess menuAccess)throws SwtException;
	public void updateEntityAccessDetails(EntityAccess entityAccess)throws SwtException;
	public Collection getMenuAccessDetails(String roleId)throws SwtException;
	public Collection getEntityAccessDetails(String hostId,String roleId)throws SwtException;
	public void saveWorkQAccessDetails(WorkQAccess workQAccess) throws SwtException;
	public void deleteWorkQAccessDetails(WorkQAccess workQAccess)throws SwtException;
	public void deleteEntityAccessDetails(EntityAccess entityAccess)throws SwtException;
	public void deleteMenuAccessDetails(MenuAccess menuAccess)throws SwtException;
	public void updateWorkQAccessDetails(WorkQAccess workQAccess) throws SwtException;
	public void saveSweepLimitsDetails(SweepLimits sweepLimits) throws SwtException;
	public void updateSweepLimitDetails(SweepLimits sweepLimits) throws SwtException;
	public void deleteSweepLimitDetails(SweepLimits sweepLimits) throws SwtException;
	public Collection getCurrencyGroupAccess(String roleId,String entityId)  throws SwtException;
	public Collection getCurrencyGroupDetailsByEntityId(String hostId,String entityId)throws SwtException;
	public Collection getCurrencyGroupAccessDetails(String hostId,String roleId)throws SwtException;
	public void saveCcyGrpAccessDetails(CurrencyGroupAccess ccyGrpAccess) throws SwtException;
	public void updateCcyGrpAccessDetails(CurrencyGroupAccess ccyGrpAccess) throws SwtException;
	public void deleteCcyGrpAccessDetails(CurrencyGroupAccess ccyGrpAccess) throws SwtException;
	
	/*--START: CODE ADDED FOR SRS CURRENCY MONITOR 1.3 ON 11 - JUL - 2007 BY AJESH SINGH--*/
	
	public void updateDeleteLocationAccessDetail(LocationAccess locationAccess)throws SwtException;
    public void updateAddLocationAccessDetail(LocationAccess locationAccess)throws SwtException;
	
    /*--END: CODE ADDED FOR SRS CURRENCY MONITOR 1.3 ON 11 - JUL - 2007 BY AJESH SINGH--*/
	 /*Subject		:Report Role (mantis 859).
	 *Description	:Generate the report for selected role, if the user clicked on the print button in 
	 *				 role maintenance screen.If the user clicked on printAll button in role maintanace 
	 *				 screen, the report will be generated for all roles present in the smart predict system.
	 *  
	 *Code Modified on 17-Dec-2008 by Thirumurugan.p Start of code
	 **/ 
    
    /**
	 * This method  is the interface for the RoleReportDAOHibernate
	 * @param request
	 * @param roleID
	 * @return Jasper Print
	 * @throws SwtException
	 */
	public JasperPrint getRoleReport(HttpServletRequest request,String  roleID) throws SwtException;

	/*End of Code for mantis 859 */
	
	
	 /*--START: CODE ADDED FOR SRS Intra-Day Liquidity Monitoring and Analysis 0.3 ON 02 - DEC - 2013 BY ATEF SRIDI --*/
	
	/**
	 * This function returns if the current user id has the possibility to modify 
	 * any scenario according to MAINTAIN_ANY_ILM_SCENARIO Column in S_ROLE
	 * @param roleId
	 * @return
	 * @throws SwtException
	 */
	public String getMaintainAnyILMScenario(String roleId)throws SwtException;
	/**
	 * This function returns if the current user id has the possibility to modify 
	 * any scenario according to MAINTAIN_ANY_REP_HIST Column in S_ROLE
	 * @param roleId
	 * @return
	 * @throws SwtException
	 */
	public String getMaintainAnyReportHist(String roleId)throws SwtException;
	
	
	/**
	 * This function returns if the current user id has the possibility to modify 
	 * any scenario according to MAINTAIN_ANY_ILM_GROUP Column in S_ROLE
	 *
	 * @param roleId
	 * @return
	 * @throws SwtException
	 */
	public String getMaintainAnyILMGroup(String roleId)throws SwtException;
	
	 /*--END: CODE ADDED FOR SRS Intra-Day Liquidity Monitoring and Analysis 0.3 ON 02 - DEC - 2013 BY ATEF SRIDI --*/
	/**
	 * This function returns if the current user id has the possibility to modify 
	 * any scenario according to MAINTAIN_ANY_PC_FEATURE Column in S_ROLE
	 * @param roleId
	 * @return
	 * @throws SwtException
	 */
	public String getMaintainAnyPCFeature(String roleId) throws SwtException;
	
	
	/**
	 * 
	 * @param roleId
	 * @return
	 * @throws SwtException
	 */
	public Collection<FacilityAccess> getFacilityAccessList(String roleId) throws SwtException;
	
	public HashMap<String, String> getAllFacilitiesList() throws SwtException;
	
	/**
	 * This method is used to get facility description from p_maint_facility table
	 */
	public String getFacilityDesc(String facilityId) throws SwtException;
	
	public void saveRoleFacilityAccess(ArrayList<FacilityAccess> facilityAccessList, String roleId) throws SwtException;
	
	public void updateRoleFacilityAccess(ArrayList<FacilityAccess> facilityAccessList, String roleId) throws SwtException;
	
	public ArrayList<FacilityAccess> getRoleFacilityAccess(String roleId) throws SwtException;
}
