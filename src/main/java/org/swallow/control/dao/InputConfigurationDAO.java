/*
 * @(#)InputConfigurationDAO.java 1.0 11/06/08
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.control.dao;

import java.util.ArrayList;

import org.swallow.control.model.InputInterface;
import org.swallow.control.model.InputInterfaceProperty;
import org.swallow.control.model.InputInterfaceSBeanProperty;
import org.swallow.control.model.InterfaceInterruption;
import org.swallow.exception.SwtException;
import org.swallow.util.OpTimer;

/**
 * 
 * 
 * This class is used to get the methods for input configuration details.
 */

public interface InputConfigurationDAO {
	/**
	 * This method is used to fetch the details for all the interfaces.
	 * 
	 * @param opTimer
	 * @param fromPCM
	 * @return ArrayList<InputInterface>
	 */
	public ArrayList<InputInterface> fetchData(OpTimer opTimer, boolean fromPCM, String filter, String sort)
			throws SwtException;

	/**
	 * This method is used to fetch the details of the passed interface
	 * 
	 * @param opTimer
	 * @param interfaceId
	 * @return InputInterface
	 */
	public InputInterface fetchRecord(OpTimer opTimer, String interfaceId);

	/**
	 * This method is used to save the changes made to the interfaces
	 * 
	 * @param opTimer
	 * @param dto
	 * @return
	 */
	public void saveRecord(OpTimer opTimer, InputInterface dto)
			throws SwtException;

	/**
	 * This method fetches the properties of the Input Interface Property
	 * 
	 * @param opTimer
	 * @param interfaceId
	 * @param logicalClass
	 * @param name
	 * @return InputInterfaceProperty
	 * @throws SwtException 
	 */
	public InputInterfaceProperty getPropertyForUpdate(OpTimer opTimer,
			String interfaceId, String logicalClass, String name) throws SwtException;

	/**
	 * This method saves the interface properties
	 * 
	 * @param opTimer
	 * @param dto
	 * @return InputInterfaceProperty
	 */
	public void saveProperty(OpTimer opTimer, InputInterfaceProperty dto)
			throws SwtException;

	/*
	 * Start: code added by krishna on 14-Jul-2011 for mantis 1446:GUI changes
	 * in Predict for Smart Input v6
	 */
	/**
	 * This method is used to fetch the bottom grid details for all the
	 * interfaces.
	 * 
	 * @param opTimer
	 * @param interfaceId
	 * @param fromPCM
	 * @return ArrayList<InputInterface>
	 */
	public ArrayList<InputInterface> fetchBottomGridData(OpTimer opTimer,
			String interfaceId, boolean fromPCM) throws SwtException;

	/**
	 * This method is used to update the changes made to the interfaces
	 * 
	 * @param opTimer
	 * @param dto
	 * @param fromPCM
	 * @return
	 */
	public void updateRecord(OpTimer opTimer, InputInterfaceSBeanProperty dto, boolean fromPCM)
			throws SwtException;

	/**
	 * This method is used to update the engine active changes made to the
	 * interfaces
	 * 
	 * @param opTimer
	 * @param dto
	 * @return
	 */
	public void updateImage(OpTimer opTimer, InputInterface dto)
			throws SwtException;
	
	/**
	 * Get the InterfaceInterruption object for a messageType from the table P_INTERFACE_EXTENSION if exists
	 * @param opTimer
	 * @param interfaceId
	 * @param messageType
	 * @return
	 */
	public InterfaceInterruption getMsgTypeExtension(OpTimer opTimer, String interfaceId, String messageType);
	
	/** 
	 * Save the values of InterfaceInterruption in the table P_INTERFACE_EXTENSION 
	 * @param opTimer
	 * @param interfaceInterrupt
	 */
	public void saveMessageTypeParams(OpTimer opTimer,InterfaceInterruption interfaceInterrupt)
			throws SwtException;
	/**
	 * Get the InputInterfaceSBeanProperty object from the table I_INTERFACE_SBEAN_PROPERTIES if exists
	 * @param interfaceId
	 * @param BeanId
	 * @param propertyName
	 * @return
	 * @throws SwtException
	 */
	public InputInterfaceSBeanProperty fetchInterfaceSBeanProperty(String interfaceId, String BeanId, String propertyName) throws SwtException;
}
/*
 * End: code added by krishna on 14-Jul-2011 for mantis 1446:GUI changes in
 * Predict for Smart Input v6
 */