/*
 * @(#)MovementRecoveryDAO.java 03/05/06
 * 
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 * 
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */

package org.swallow.control.dao;

import java.util.Collection;
import org.swallow.dao.DAO;
import org.swallow.exception.SwtException;
import org.swallow.work.model.MovementLock;

public interface MovementRecoveryDAO extends DAO {
	/**
	 * 
	 * @param hostId
	 * @param entityId
	 * @return
	 * @throws SwtException
	 */
	public Collection getMovementLockDetails(String hostId,String entityId) throws SwtException;
	/**
	 * 
	 * @param hostId
	 * @param movementId
	 * @throws SwtException
	 */
	public void unlockMovement(MovementLock movLock)throws SwtException;

}
