package org.swallow.control.dao;

import java.lang.*;
import java.util.*;
import org.swallow.exception.SwtException;
import org.swallow.control.model.ConnectionPool;
import org.swallow.dao.DAO;

public interface ConnectionPoolControlDAO extends DAO {


	public ConnectionPool getConnectionPool(ConnectionPool conn, String moduleId)
			throws SwtException;

	/**
	 * This method is used to retrieve informations related to connection ids
	 * @param openConnections
	 * @param moduleId
	 * @return
	 * @throws SwtException
	 */
	public ArrayList<ConnectionPool> getConnectionPoolList(ArrayList<ConnectionPool> openConnections, String moduleId)
			throws SwtException;
	
	/**
	 * Get session list by module
	 * @param moduleId
	 * @return
	 * @throws SwtException
	 */
	public ArrayList<ConnectionPool> getConnectionPoolListByModule(String moduleId)
			throws SwtException;
			
	/**
	 * This method is used to kill DB session using connection identifiers
	 *
	 * @param connectionsIds
	 * @return
	 * @throws SwtException
	 */
	public void killDBSessionConnections(String moduleId, String connectionsIds)
			throws SwtException;
	
	/**
	 * This method is used to Check if the Predict user has access to v$session and v$active_session_history
	 * @param moduleId
	 * @throws SwtException
	 */
	public boolean checkDBViewsGrant(String moduleId)	throws SwtException;
	
	
	/**
	 * This method is used to update S_POOL_STATS table with the latest connection pool details
	 *
	 * @param connectionsIds
	 * @return
	 * @throws SwtException
	 */
	public void updatePoolStatsTable(String moduleId, int numActive, int numIdle, int maxActive, int maxIdle ) throws SwtException ;


}