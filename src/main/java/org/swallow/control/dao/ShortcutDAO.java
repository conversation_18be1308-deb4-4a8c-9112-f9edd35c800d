/*
 * @(#)ShortcutDAO.java 23/12/05
 * 
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 * 
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */

package org.swallow.control.dao;

import org.swallow.control.model.*;
import java.util.Collection;
import org.swallow.dao.*;
import org.swallow.exception.SwtException;
import org.swallow.util.SystemFormats;
import org.swallow.util.SystemInfo;


public interface ShortcutDAO extends DAO{
	/**
	 * @param hostId
	 * @param userId
	 * @return
	 * @throws SwtException
	 */
	public Collection getShortcutDetailList(String hostId, String userId)  throws SwtException;
	/**
	 * @return
	 * @throws SwtException
	 */
	public Collection getMenuList() throws SwtException;
	/**
	 * @param shrtcut
	 * @throws SwtException
	 */
	public void  saveShortcutDetails(Shortcut shrtcut) throws SwtException;
	/**
	 * @param shrtcut
	 * @throws SwtException
	 */
	public void  updateShortcutDetails(Shortcut shrtcut) throws SwtException;
	/**
	 * @param shrtcut
	 * @throws SwtException
	 */
	public void  deleteShortcutDetail(Shortcut shrtcut) throws SwtException;
	
	/**
	 * @param userId
	 * @param shortcutId
	 * @param hostId
	 * @return
	 * @throws SwtException
	 */
	public Shortcut  editShortcutDetails(String userId,String shortcutId,String hostId) throws SwtException;
}