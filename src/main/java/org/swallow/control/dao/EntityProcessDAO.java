/*
 * @(#)EntityProcessDAO.java 1.0 31/05/2011
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.control.dao;

import java.util.Collection;

import org.swallow.control.model.EntityProcess;
import org.swallow.control.model.EntityProcessStatus;
import org.swallow.control.model.Process;
import org.swallow.dao.DAO;
import org.swallow.exception.SwtException;

/**
 * <AUTHOR>
 * 
 * EntityProcessDAO interface, contains collection of methods to be used in DAO
 * layer.
 */
public interface EntityProcessDAO extends DAO {

	/**
	 * This is used to fetches process name list s from S_Process table.
	 * 
	 * @return Collection
	 * @throws SwtException
	 */
	public Collection<Process> getProcessNameDetails() throws SwtException;

	/**
	 * This is used to fetches entity process details from s_ENTITY_PROCESS
	 * table.
	 * 
	 * @param processname
	 * @return collection
	 * @throws SwtException
	 */
	public Collection<EntityProcess> getEntityProcess(String processName)
			throws SwtException;

	/**
	 * This is used to fetches entity process status details from
	 * S_ENTITY_PROCESS_STATUS table.
	 * 
	 * @param processname
	 * @return collection
	 * @throws SwtException
	 */
	public Collection<EntityProcessStatus> getEntityProcessStatus(
			String processName) throws SwtException;

	/**
	 * This is used to save the details for entity process status and process
	 * 
	 * @param entityprocessstatus
	 * @param entityprocess
	 * @param process
	 * @return none
	 * @throws SwtException
	 */
	public void save(Collection<EntityProcess> entProColl, Process process)
			throws SwtException;

	/**
	 * This is used to save the details for entity process status from table.
	 * 
	 * @param entityprocessstatus
	 * @return none
	 * @throws SwtException
	 */
	public void savestatus(EntityProcessStatus entProStatus)
			throws SwtException;

	/*
	 * Start:code Added by Bala on 3-10-2011 for Mantis 1600:DataBuild in
	 * progress not coming properly
	 */

	/**
	 * This method is used to get the monitor job flag
	 * 
	 * @param entityId
	 * @param jobProcess
	 * @return boolean
	 * @throws SwtException
	 */
	public boolean getMonitorJobFlag(String entityId, String jobProcess)
			throws SwtException;

}
/*
 * End:code Added by Bala on 3-10-2011 for Mantis 1600:DataBuild in progress not
 * coming properly
 */
