/*
 * @(#)InputConfigurationDAOHibernate.java 1.0 11/06/08
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.control.dao.hibernate;

import java.sql.CallableStatement;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.Types;
import java.util.ArrayList;
import java.util.List;






import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

import org.swallow.control.dao.InputConfigurationDAO;
import org.swallow.control.model.InputInterface;
import org.swallow.control.model.InputInterfaceProperty;
import org.swallow.control.model.InputInterfaceSBeanProperty;
import org.swallow.control.model.InterfaceInterruption;
import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.util.JDBCCloser;
import org.swallow.util.OpTimer;
import org.swallow.util.SwtInterceptor;
import org.swallow.util.SwtUtil;
import org.springframework.context.annotation.Lazy;
import org.springframework.orm.hibernate5.support.HibernateDaoSupport;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.stereotype.Repository;
import org.springframework.beans.factory.annotation.Autowired;
import org.hibernate.HibernateException;
import org.hibernate.SessionFactory;
import org.hibernate.Transaction;
import org.hibernate.query.Query;
import org.hibernate.type.StringType;
import org.hibernate.Session;

/**
 * 
 * 
 * This class is used to get the input configuration details.
 */

@Repository ("inputConfigurationDAO")
@Transactional
public class InputConfigurationDAOHibernate extends HibernateDaoSupport
		implements InputConfigurationDAO {
	
	public InputConfigurationDAOHibernate(@Lazy SessionFactory sessionfactory) {
	    setSessionFactory(sessionfactory);
	}

	// Final log object for logging this class
	private final Log log = LogFactory
			.getLog(InputConfigurationDAOHibernate.class);

	/**
	 * This method is used to fetch the details for all the interfaces.
	 * 
	 * @param opTimer
	 * @return ArrayList<InputInterface>
	 */
	public ArrayList<InputInterface> fetchData(OpTimer opTimer, boolean fromPCM, String filter, String sort)
			throws SwtException {
		// Declares the Hibernate session object
		Session session = null;
		// Declares the SQL Connection object
		Connection conn = null;
		// Declares the CallableStatement object
		CallableStatement cstmt = null;
		// Declares the result set
		ResultSet rs = null;
		// variable for arraylist
		ArrayList<InputInterface> rtn = null;
		// variable for an input interface object
		InputInterface input = null;
		try {
			log.debug(this.getClass().getName() + " - [fetchData] - Entering");
			// instantiate an object
			rtn = new ArrayList<InputInterface>();
			// get input config data
			opTimer.start("fetch-data");
			// to get the session
			session = getHibernateTemplate().getSessionFactory().openSession();
			// create session connection
			conn = SwtUtil.connection(session);
			/* Using Callable statement to execute the Stored Procedure */
		
			if(!fromPCM) {
				cstmt = conn
						.prepareCall("{call pkg_interface_monitor.i_settings_header(?,?,?)}");	
			}
			else {
				cstmt = conn
						.prepareCall("{call pkg_pc_interface_monitor.i_settings_header(?,?,?)}");
			}
			cstmt.setString(1,filter);
			cstmt.setString(2,sort);
			cstmt.registerOutParameter(3, oracle.jdbc.OracleTypes.CURSOR);
			// execute the procedure
			cstmt.execute();
			/* Fetching the Result */
			rs = (ResultSet) cstmt.getObject(3);
			/* Condition to check result set has value */
			if (rs != null) {
				/* Loop to iterate result set */
				while (rs.next()) {
					// input interface object
					input = new InputInterface();
					// Set the expand value
					input.setExpand(rs.getString(1));
					// set the interfaceid
					input.setInterfaceId(rs.getString(2));
					// set the message type
					input.setMessageType(rs.getString(3));
					// set the emaillogs
					input.setEmailLogs(rs.getString(4));
					// set the emaillogsto
					input.setEmailLogsTo(rs.getString(5));
					// set the engine active
					input.setEngineActive(rs.getString(6));
					// set the start alert time
					input.setStartAlertTime(rs.getString(7));
					// set the end alert time
					input.setEndAlertTime(rs.getString(8));
					// set the threshold
					input.setThreshold(rs.getString(9));
					rtn.add(input);
				}
			}

			opTimer.stop("fetch-data");
			log.debug(this.getClass().getName() + " - [fetchData] - Exiting");
		} catch (Exception e) {

			log.error(this.getClass().getName() + " - [fetchData] - Exiting"
					+ e.getMessage());

			throw SwtErrorHandler.getInstance().handleException(e, "fetchData",
					InputConfigurationDAOHibernate.class);
		} finally {
			
			//edited by KaisBS for mantis 1745 : introducing the implementation of the JDBCCloser class
			JDBCCloser.close(rs, cstmt, conn, session);

		}
		return rtn;
	}

	/**
	 * This method is used to fetch the bottom grid details for all the
	 * interfaces.
	 * 
	 * @param opTimer
	 * @param interfaceId
	 * @return ArrayList<InputInterface>
	 */
	public ArrayList<InputInterface> fetchBottomGridData(OpTimer opTimer,
			String interfaceId, boolean fromPCM) throws SwtException {
		// Declares the Hibernate session object
		Session session = null;
		// Declares the SQL Connection object
		Connection conn = null;
		// Declares the CallableStatement object
		CallableStatement cstmt = null;
		// Declares the result set
		ResultSet rs = null;
		// Declares the result set
		ArrayList<InputInterface> rtn = null;
		// variable for input interface object
		InputInterface input = null;
		try {
			log.debug(this.getClass().getName()
					+ " - [fetchbottomgridData] - Entering");
			// instantiate an object
			rtn = new ArrayList<InputInterface>();
			// get input config data
			opTimer.start("fetch-data");
			// to open the session
			session = getHibernateTemplate().getSessionFactory().openSession();
			// get the session connection
			conn = SwtUtil.connection(session);
			/* Using Callable statement to execute the Stored Procedure */
			if(!fromPCM) {
				cstmt = conn
						.prepareCall("{call pkg_interface_monitor.i_settings_detail(?,?)}");
			}
			else {
				cstmt = conn
						.prepareCall("{call pkg_pc_interface_monitor.i_settings_detail(?,?)}");
			}
			// set the parameters
			cstmt.setString(1, interfaceId);
			cstmt.registerOutParameter(2, oracle.jdbc.OracleTypes.CURSOR);
			// execute the statement
			cstmt.execute();
			/* Fetching the Result */
			rs = (ResultSet) cstmt.getObject(2);
			/* Condition to check result set has value */
			if (rs != null) {
				/* Loop to iterate result set */
				while (rs.next()) {
					// Input interface object
					input = new InputInterface();
					// set the class
					input.setClasss(rs.getString(1));
					// set the property
					input.setProperty(rs.getString(2));
					// set the value
					input.setValue(rs.getString(3));
					// set the beanid
					input.setBeanId(rs.getString(4));
					// set the name
					input.setName(rs.getString(5));
					// set the mandatory
					input.setMandatory(rs.getString(6));
					// set the property type
					input.setPropertyType(rs.getString(7));
					if ((input.getProperty() != null) && input.getProperty().toLowerCase().contains("password")) {
						input.setIsPassword(true);
					} else {
						input.setIsPassword(false);
						
					}
					rtn.add(input);

				}
			}
			opTimer.stop("fetch-data");
			log.debug(this.getClass().getName()
					+ " - [fetchBottomGridData] - Exiting");

		} catch (Exception e) {
			log.error(this.getClass().getName()
					+ " - [fetchbottomgridData] - Exiting" + e.getMessage());
			throw SwtErrorHandler.getInstance()
					.handleException(e, "fetchbottomgridData",
							InputConfigurationDAOHibernate.class);
		} finally {
			
			//edited by KaisBS for mantis 1745 : introducing the implementation of the JDBCCloser class
			JDBCCloser.close(rs, cstmt, conn, session);
			
		}
		return rtn;
	}

	/**
	 * This method is used to fetch the details of the passed interface
	 * 
	 * @param opTimer
	 * @param interfaceId
	 * @return InputInterface
	 */
	@SuppressWarnings("unchecked")
	public InputInterface fetchRecord(OpTimer opTimer, String interfaceId) {
		log.debug(this.getClass().getName() + " - [fetchRecord] - Entering");
		// get input config data
		opTimer.start("fetch-record");

		// fetches the details of an interfaces from I_INTERFACE table
		List data = (List ) getHibernateTemplate().find(
				"from InputInterface i where i.interfaceId=?0 order by i.label",
				new Object[] { interfaceId });
		opTimer.stop("fetch-record");

		if (data.size() < 1)
			return null;
		log.debug(this.getClass().getName() + " - [fetchRecord] - Exiting");
		return (InputInterface) data.get(0);

	}

	// Start : Modified by Sutheendran Balaji on 01-11-2011
	/**
	 * This method is used to save the changes made to the interfaces
	 * 
	 * @param opTimer
	 * @param inputInterface
	 * @return
	 */
	public void saveRecord(OpTimer opTimer, InputInterface inputInterface)
			throws SwtException {		
		// Holds the hibernate session instance
		Session session = null;
		// Holds the hibernate Transaction instance
		Transaction transaction = null;
		// Holds the list of the interruptions associated with the selected
		// interface
		List data = null;
		try {
			opTimer.start("save-record");
			log.debug(this.getClass().getName() + " - [saveRecord] - Entering");
			// To open the session
			session = getHibernateTemplate().getSessionFactory().openSession();
			// Initiate the transaction instance
			transaction = session.beginTransaction();
			// Updates the details of the inputInterface
			session.update(inputInterface);
			//Fetch the interruption associated with the modified interface
			data = (List ) getHibernateTemplate().find(
					"from InterfaceInterruption i where i.id.interfaceId=?0 ",
					new Object[] { inputInterface.getInterfaceId() });
			//If the there are interruption associated, update else, save
			if (data != null && data.size() > 0) {
				session.update(inputInterface.getInterfaceInterruption(inputInterface.getInterfaceId(), inputInterface.getExtensions()));
			} else {
				session.save(inputInterface.getInterfaceInterruption(inputInterface.getInterfaceId(), inputInterface.getExtensions()));
			}			
			// Commits the transaction to the database
			transaction.commit();
			opTimer.stop("save-record");
			log.debug(this.getClass().getName() + " - [saveRecord] - Exiting");
		} catch (HibernateException e) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [saveRecord] method "
					+ e.getMessage());
			throw SwtErrorHandler.getInstance().handleException(e,
					"saveRecord", InputConfigurationDAOHibernate.class);
		} finally {
			
			//edited by KaisBS for mantis 1745 : introducing the implementation of the JDBCCloser class
			JDBCCloser.close(session);
			
			transaction = null;
			data = null;
		}
	}

	// End : Modified by Sutheendran Balaji on 01-11-2011
	/**
	 * This method is used to update the changes made to the interfaces
	 * 
	 * @param opTimer
	 * @param dto
	 * @return
	 */
	@SuppressWarnings("unchecked")
	public void updateRecord(OpTimer opTimer, InputInterfaceSBeanProperty dto, boolean fromPCM)
			throws SwtException {
		opTimer.start("save-record");
		// Holds the hibernate session instance
		Session session = null;
		// Declares the SQL Connection object
		Connection conn = null;
		// Declares the CallableStatement object
		CallableStatement cstmt = null;

		try {
			log.debug(this.getClass().getName()
					+ " - [updateRecord] - Entering");
			// start the optimer
			opTimer.start("fetch-data");
			// to open the session
			session = getHibernateTemplate().getSessionFactory().openSession();
			// get the session connection
			conn = SwtUtil.connection(session);
			/* Using Callable statement to execute the Stored Procedure */
			if(!fromPCM) {
				cstmt = conn
						.prepareCall("{call pkg_interface_monitor.UpdatePropertyValues(?,?,?,?)}");
			}
			else {
				cstmt = conn
						.prepareCall("{call pkg_pc_interface_monitor.UpdatePropertyValues(?,?,?,?)}");
			}
			// set the in parameters
			cstmt.setString(1, dto.getId().getInterfaceId());
			cstmt.setString(2, dto.getId().getName());
			cstmt.setString(3, dto.getId().getBeanId());
			cstmt.setString(4, dto.getValue());
			// execute the statement
			cstmt.execute();

			opTimer.stop("update-record");
			log
					.debug(this.getClass().getName()
							+ " - [updateRecord] - Exiting");
		} catch (Exception e) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [updateRecord] method "
					+ e.getMessage());
			throw SwtErrorHandler.getInstance().handleException(e,
					"updateRecord", InputConfigurationDAOHibernate.class);
		} finally {
			
			//edited by KaisBS for mantis 1745 : introducing the implementation of the JDBCCloser class
			JDBCCloser.close(null, cstmt, conn, session);
			
		}
	}

	/**
	 * This method is used to update the engine active made to the interfaces
	 * 
	 * @param opTimer
	 * @param dto
	 * @return
	 */
	@SuppressWarnings("unchecked")
	public void updateImage(OpTimer opTimer, InputInterface dto)
			throws SwtException {
		opTimer.start("update-image");
		// variable to list
//		List data = null;
		// variable for InputInterface object
		InputInterface inputInterface = null;
		Session session = null;
		Transaction tx = null;
		SwtInterceptor interceptor = null;
		try {
			log.debug(this.getClass().getName()
					+ " - [update image] - Entering");

			
			// check the size of the list
			interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");
			session = getHibernateTemplate().getSessionFactory().withOptions().interceptor(interceptor).openSession();
			
			// get the list to data: should use the same session, do not use "getHibernateTemplate().find(..)", an error will be raised instead
//			data = session.find(
//					"from InputInterface i where i.interfaceId=? ",
//					dto.getInterfaceId(), new net.sf.hibernate.type.StringType());
			Query<InputInterface> query = session.createQuery("from InputInterface i where i.interfaceId = :id", InputInterface.class);
			query.setParameter("id", dto.getInterfaceId(), StringType.INSTANCE);
			List<InputInterface> data = query.getResultList();
			tx = session.beginTransaction();
			tx = session.beginTransaction();
			if (data.size() != 0) {
				// set the object
				inputInterface = (InputInterface) data.get(0);
				// set the engine active value
				inputInterface.setEngineActive(dto.getEngineActive());
				// update
				session.update(inputInterface);
			}
			tx.commit();
			session.close();
			opTimer.stop("update-image");
			log.debug(this.getClass().getName() + " - [updateImage] - Exiting");
		} catch (Exception e) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [updateImage] method "
					+ e.getMessage());
			throw SwtErrorHandler.getInstance().handleException(e,
					"updateImage", InputConfigurationDAOHibernate.class);
		}
	}

	/**
	 * This method fetches the properties of the InputInterfaceProperty
	 * 
	 * @param opTimer
	 * @param interfaceId
	 * @param logicalClass
	 * @param name
	 * @return InputInterfaceProperty
	 * @throws SwtException 
	 */
	@SuppressWarnings("unchecked")
	public InputInterfaceProperty getPropertyForUpdate(OpTimer opTimer,
			String interfaceId, String logicalClass, String name) throws SwtException {
		// /variable for List
		List data = null;
		try {
			log.debug(this.getClass().getName()
					+ " - [getPropertyForUpdate] - Entering");
			// get input config data
			opTimer.start("fetch-record");
			// Fetches the property details for the interface
			data = (List ) getHibernateTemplate().find(
							"from InputInterfaceProperty i where i.id.interfaceId=?0 and i.id.logicalClass=?1 and i.id.name=?2",
							new Object[] { interfaceId, logicalClass, name });
			opTimer.stop("fetch-record");
			// check the size
			if (data.size() < 1)
				return null;
			log.debug(this.getClass().getName()
					+ " - [getPropertyForUpdate] - Exiting");

		} catch (Exception e) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [getPropertyForUpdate] method "
					+ e.getMessage());
			throw SwtErrorHandler.getInstance().handleException(e, "InputInterfaceProperty", InputConfigurationDAOHibernate.class);
		}
		return (InputInterfaceProperty) data.get(0);
	}

	/**
	 * This method saves the interface properties
	 * 
	 * @param opTimer
	 * @param dto
	 * @return InputInterfaceProperty
	 */
	public void saveProperty(OpTimer opTimer, InputInterfaceProperty dto)
			throws SwtException {
		opTimer.start("save-property");
		// Holds the hibernate session instance
		Session sesion = null;
		// Holds the hibernate Transaction instance
		Transaction tx = null;
		try {
			log.debug(this.getClass().getName()
					+ " - [saveProperty] - Entering");
			sesion = getHibernateTemplate().getSessionFactory().openSession();

			// updates the property details of the interface
			tx = sesion.beginTransaction();
			sesion.update(dto);
			// Commits the changes made to the properties of the interface
			tx.commit();
		} catch (HibernateException e) {

			log
					.error(this.getClass().getName()
							+ " - HibernateException Catched in [saveProperty] method - "
							+ e.getMessage());
			throw SwtErrorHandler.getInstance().handleException(e,
					"saveProperty", InputConfigurationDAOHibernate.class);
		} finally {
			
			//edited by KaisBS for mantis 1745 : introducing the implementation of the JDBCCloser class
			JDBCCloser.close(sesion);
			
			tx = null;
		}
		opTimer.stop("save-property");
		log.debug(this.getClass().getName() + " - [saveProperty] - Exiting");
	}
	

	public InterfaceInterruption getMsgTypeExtension(OpTimer opTimer, String interfaceId, String messageType) {
		log.debug(this.getClass().getName() + " - [getMsgTypeExtension] - Entering");
		// get input config data
		opTimer.start("get-Msg-Type-Exceptions");

		// fetches the details of an interfaces from I_INTERFACE table
		List data = (List ) getHibernateTemplate().find(
				"from InterfaceInterruption i where i.id.interfaceId=?0 and i.id.messageType=?1",
				new Object[] { interfaceId, messageType});
		opTimer.stop("fetch-record");

		if (data.size() < 1)
			return null;
		log.debug(this.getClass().getName() + " - [getMsgTypeExtension] - Exiting");
		return (InterfaceInterruption) data.get(0);
		
	}

	public void saveMessageTypeParams(OpTimer opTimer,
			InterfaceInterruption interfaceInterrupt) throws SwtException {
		// Holds the hibernate session instance
		Session session = null;
		// Holds the hibernate Transaction instance
		Transaction transaction = null;
		// Holds the list of the interruptions associated with the selected
		// interface
		List data = null;
		try {
			opTimer.start("save-messageType-params");
			log.debug(this.getClass().getName() + " - [saveMessageTypeParams] - Entering");
			// To open the session
			session = getHibernateTemplate().getSessionFactory().openSession();
			// Initiate the transaction instance
			transaction = session.beginTransaction();
			//Fetch the interruption associated with the modified interface
			data = (List ) getHibernateTemplate().find(
					"from InterfaceInterruption i where i.id.interfaceId=?0 and i.id.messageType=?1 ",
					new Object[] {interfaceInterrupt.getId().getInterfaceId(), interfaceInterrupt.getId().getMessageType()});
			// If the there are interruption associated, update else, save
			if(data != null && data.size() > 0)
				session.update(interfaceInterrupt);
			else 
				session.save(interfaceInterrupt);			
			// Commits the transaction to the database
			transaction.commit();
			opTimer.stop("save-messageType-params");
			log.debug(this.getClass().getName() + " - [saveMessageTypeParams] - Exiting");
		}catch(HibernateException e){
			log.error(this.getClass().getName()
					+ " - Exception Catched in [saveMessageTypeParams] method "
					+ e.getMessage());
			throw SwtErrorHandler.getInstance().handleException(e,
					"saveMessageTypeParams", InputConfigurationDAOHibernate.class);
		}finally{
			try{
				if (session != null)
					session.close();
			}catch (Exception e){
				log
						.error(this.getClass().getName()
								+ " - Exception Catched in [saveMessageTypeParams] method : - session not closed"
								+ e.getMessage());
			}
			transaction = null;
			data = null;
		}
	}

	@Override
	public InputInterfaceSBeanProperty fetchInterfaceSBeanProperty(
			String interfaceId, String BeanId, String propertyName)
			throws SwtException {
		List data = null;
		try {
			log.debug(this.getClass().getName() + " - [fetchInterfaceSBeanProperty] - Entering");
			
			// fetches the details of an interfaces from I_INTERFACE_SBEAN_PROPERTIES table
			data = (List ) getHibernateTemplate().find(
					"from InputInterfaceSBeanProperty i where i.id.interfaceId=?0 and i.id.beanId=?1 and i.id.name=?2 ",
					new Object[] { interfaceId, BeanId, propertyName });
			
			
		} catch (Exception e) {
			log.error(this.getClass().getName()
					+ " - [fetchInterfaceSBeanProperty] - Exiting" + e.getMessage());
			throw SwtErrorHandler.getInstance()
					.handleException(e, "fetchInterfaceSBeanProperty",
							InputConfigurationDAOHibernate.class);
		}
		

		log.debug(this.getClass().getName() + " - [fetchInterfaceSBeanProperty] - Exiting");
		if (data.size() < 1)
			return null;
		else
			return (InputInterfaceSBeanProperty)data.get(0);

	}
}