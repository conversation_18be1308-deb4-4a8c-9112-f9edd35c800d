/*
 * @(#)SystemLogDAOHibernate.java 1.0 16/15/05
 * 
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 * 
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */

package org.swallow.control.dao.hibernate;

import java.util.Date;
import java.util.List;
import java.util.StringTokenizer;



import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

import org.swallow.control.dao.SystemLogDAO;
import org.swallow.control.model.SystemLog;
import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.util.JDBCCloser;
import org.swallow.util.PropertiesFileLoader;
import org.swallow.util.SwtConstants;
import org.swallow.util.SwtInterceptor;
import org.swallow.util.SwtPager;
import org.swallow.util.SwtUtil;
import org.swallow.util.SystemFormats;
import org.springframework.context.annotation.Lazy;
import org.springframework.orm.hibernate5.support.HibernateDaoSupport;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.stereotype.Repository;
import org.springframework.beans.factory.annotation.Autowired;
import org.hibernate.HibernateException;
import org.hibernate.SessionFactory;
import org.hibernate.Transaction;
import org.hibernate.query.Query;
import org.hibernate.Session;

/**
 * <AUTHOR>
 * 
 * This class is dao layer of the Ssytem log screen na dit use dto get the
 * system log details of the application
 * 
 */
@Repository ("systemLogDAO")
@Transactional
public class SystemLogDAOHibernate extends HibernateDaoSupport implements
		SystemLogDAO {
	public SystemLogDAOHibernate(@Lazy SessionFactory sessionfactory) {
	    setSessionFactory(sessionfactory);
	}

	private final Log log = LogFactory.getLog(SystemLogDAOHibernate.class);

	/**
	 * @desc This method fetches the System Log details based in the Page
	 *       Clicked. For first time data for the first page is fetched.
	 * @param fromDate
	 * @param toDate
	 * @return
	 * @throws SwtException
	 */
	public int getSystemLogList(String hostId, Date fromDate, Date toDate,
			int currentPage, int maxPage, List sysLogList,
			String filterSortStatus, SystemFormats formats) throws SwtException {
		log.debug("Entering getSystemLogList Method");
		StringBuffer sysLogHQL = new StringBuffer();
		Query querySysLog = null;
		StringBuffer countsysLogHQL = new StringBuffer();
		Query querycountSysLog = null;
		int totalCount = 0;

		sysLogHQL
				.append("from SystemLog s where s.hostId=?0 and trunc(s.logDate) between ?1 and ?2 ");

		countsysLogHQL.append("select count(s.systemSeqNo) ");
		countsysLogHQL.append(sysLogHQL);
		Session sess = null;
		try {
			SessionFactory sf = (SessionFactory) SwtUtil
					.getBean("sessionFactory");

			sess = sf.openSession();

			StringBuffer QueryCountfilterSortMerged = formQuery(countsysLogHQL,
					filterSortStatus, formats);

			querycountSysLog = sess.createQuery(QueryCountfilterSortMerged
					.toString());
			querycountSysLog.setString(0, hostId);
			querycountSysLog.setDate(1, fromDate);
			querycountSysLog.setDate(2, toDate);

			// this Block reads PageSize from the property file and calculates
			// the maxPages

			int pageSize = SwtUtil.getPageSizeFromProperty(SwtConstants.SYSTEM_LOG_SCREEN_PAGE_SIZE);

			List sysLogCountList = querycountSysLog.list(); // count the
			// collection Size

			if (sysLogCountList != null && sysLogCountList.size() > 0
					&& sysLogCountList.get(0) != null) {

				totalCount = Integer
						.parseInt(sysLogCountList.get(0).toString());

			}
			if (maxPage == 0) { // maxPage is initially set to 0 when called for
				// the first time , yet to calculate maxPage

				maxPage = totalCount / pageSize;
				int remainder = totalCount % pageSize;

				if (remainder > 0) {
					maxPage++;
				}

			}

			/*
			 * formQuery is called to form the correct HQL based on all the
			 * filter and sorting conditions
			 */
			StringBuffer QueryfilterSortMerged = formQuery(sysLogHQL,
					filterSortStatus, formats);

			querySysLog = sess.createQuery(QueryfilterSortMerged.toString());

			querySysLog.setString(0, hostId);
			querySysLog.setDate(1, fromDate);
			querySysLog.setDate(2, toDate);

			SwtPager.next(querySysLog, currentPage, maxPage, pageSize,
					sysLogList); // fetches the data for the currentPage
		} catch (Exception e) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [getSystemLogList] method : - "
					+ e.getMessage());
		} finally {
			
		//edited by KaisBS for mantis 1745 : introducing the implementation of the JDBCCloser class
		   JDBCCloser.close(sess);
		   
		}
		log.debug("Exiting getSystemLogList Method");
		return totalCount;
	} // End of getSystemLogList method

	/**
	 * @desc This method used to frame the query to get the System log details
	 *       from the database based on the filter and sort criteria
	 * @param query
	 * @param filterSortStatus
	 * @param formats
	 * @return
	 */
	private StringBuffer formQuery(StringBuffer query, String filterSortStatus,
			SystemFormats formats) throws SwtException {
		// Variable declared to get the filtersor details
		String[] filterSort = null;
		// Varaiable declared to get filter values
		String filterStatus = null;
		// Variable declared to get the sort values
		String sortStatus = null;
		// variable to sort tokenized sort values
		String[] sortValues = null;
		// Variable declare dto split sort values
		StringTokenizer sortTokeneizer = null;
		// Variable to get the date format
		String format = null;
		// variable to get the value of the sorted column
		String sortColumn = null;
		// Variable to get the sort type
		String sortDesc = null;
		// variable decleared to get the filter values
		String[] filterValues = null;
		// Integer Array decleartion for filter sort value operation
		int[] nonString = null;
		// integer decleration for manipulating the filter sort values
		int filterSortValue1;
		int filterSortValue2;
		// Variable declared to split filter values
		StringTokenizer filterTokenezier = null;
		// variable decleration to find the availabilty of next token
		String nextVal = null;

		try {
			log
					.debug(this.getClass().getName() + " - [formQuery] - "
							+ "Entry");
			// get the filter sort values
			filterSort = filterSortStatus.split(",");
			// get the filter values alone
			filterStatus = filterSort[0].toString();
			// get the sort values alone
			sortStatus = filterSort[1].toString();

			sortValues = new String[3];
			// split the sort values
			sortValues = sortStatus.split("|");
			sortTokeneizer = new java.util.StringTokenizer(sortStatus, "|");
			// setting the date formats from system formats
			if (formats.getDateFormatValue().equalsIgnoreCase("MM/dd/yyyy")) {
				format = "M";
			} else {
				format = "D";
			}
			filterSortValue1 = 0;
			// storing sort values in a array
			while (sortTokeneizer.hasMoreTokens()) {

				sortValues[filterSortValue1] = sortTokeneizer.nextToken();

				filterSortValue1++;
			}
			// get the sorted columns and sort type
			sortColumn = sortValues[0];
			sortDesc = sortValues[1];

			filterValues = new String[6];
			nonString = new int[6];
			filterSortValue2 = -1;
			// split the filetr values of the column and store it in an array
			if (!(filterStatus.equals("all"))
					&& !(filterStatus.equals("undefined"))) {

				filterTokenezier = new java.util.StringTokenizer(filterStatus,
						"|");

				filterSortValue1 = 0;
				while (filterTokenezier.hasMoreTokens()) {

					nextVal = filterTokenezier.nextToken();

					// checking availability of next token
					if (nextVal.equals("(Empty)")) {
						filterValues[filterSortValue1] = "is null";
						nonString[filterSortValue1] = 1;
					} else if (nextVal.equals("(Not empty)")) {
						filterValues[filterSortValue1] = "is not null";
						nonString[filterSortValue1] = 1;
					} else
						filterValues[filterSortValue1] = nextVal;

					filterSortValue1++;
				}
				/*
				 * get the filter values from array of string then , it is
				 * checked one by one with "All"
				 */
				if (!filterValues[0].toString().equals("All")) {
					filterSortValue2++;
					// apending query based on the date format.while aplly ing
					// filter fopr column
					if ("D".equals(format)) {
						query
								.append(
										" and to_date(to_char(s.logDate,'DD-MM-YYYY'),'DD-MM-YYYY') = to_date('")
								.append(filterValues[0]).append(
										"','DD-MM-YYYY')");
					} else if ("M".equals(format)) {
						query
								.append(
										" and to_date(to_char(s.logDate,'MM-DD-YYYY'),'MM-DD-YYYY') = to_date('")
								.append(filterValues[0]).append(
										"','MM-DD-YYYY')");
					}

				}
				// append query based on column filter values if it is not "All"
				if (!filterValues[1].toString().equals("All")) {
					filterSortValue2++;
					query
							.append(
									" and ((to_date(to_char(s.logDate,'HH24:MI:SS'),'HH24:MI:SS') = to_date('")
							.append(filterValues[1])
							.append("','HH24:MI:SS')))");

				}
				// append query based on column filter values if it is not "All"
				if (!filterValues[2].toString().equals("All")) {
					filterSortValue2++;
					query.append(" and s.userId='").append(filterValues[2])
							.append("'");

				}
				// append query based on column filter values if it is not "All"
				if (!filterValues[3].toString().equals("All")) {

					filterSortValue2++;
					if (nonString[3] == 1) { // if IpAddress Filter value is
						// (Empty) and (Not empty)
						query.append(" and s.ipAddress ").append(
								filterValues[3]);
					} else {
						query.append(" and s.ipAddress='").append(
								filterValues[3]).append("'");
					}
				}
				// append query based on column filter values if it is not "All"
				if (!filterValues[4].toString().equals("All")) {
					filterSortValue2++;
					query.append(" and s.process='").append(filterValues[4])
							.append("'");

				}
				// append query based on column filter values if it is not "All"
				if (!filterValues[5].toString().equals("All")) {
					filterSortValue2++;
					if (nonString[5] == 1) { // if Action Filter value is
						// (Empty)
						// and (Not empty)
						query.append(" and s.action ").append(filterValues[5]);
					} else {
						query.append(" and s.action='").append(filterValues[5])
								.append("'");
					}

				}
			}
			// framing query based on the filter and sort values
			/*
			 * Start:Code Modified For Mantis 1566 by Chinniah on
			 * 28-Dec-2011:Log Display Screens: Sort by sequence number instead
			 * of date time
			 */
			if (!sortStatus.equals("none")) {
				query.append(" order by ");

				if (sortColumn.equals("2")) {
					query.append(" s.userId");
				} else if (sortColumn.equals("3")) {
					query.append(" s.ipAddress");
				} else if (sortColumn.equals("4")) {
					query.append(" s.process");
				} else if (sortColumn.equals("5")) {
					query.append(" s.action");
				}

				if (sortDesc.equalsIgnoreCase("true")) {
					if (sortColumn.equals("0") || sortColumn.equals("1")) {
						query.append(" s.systemSeqNo desc");
					} else {
						query.append(" desc,s.systemSeqNo desc");
					}
				} else {
					if (sortColumn.equals("0") || sortColumn.equals("1")) {

						query.append(" s.systemSeqNo asc");
					} else {

						query.append(" asc,s.systemSeqNo asc");

					}
				}

			} else {

				query.append(" order by s.systemSeqNo desc");

			}
			/*
			 * End:Code Modified For Mantis 1566 by Chinniah on 28-Dec-2011:Log
			 * Display Screens: Sort by sequence number instead of date time
			 */

		} catch (Exception e) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [formQuery] method : - "
					+ e.getMessage());
			throw SwtErrorHandler.getInstance().handleException(e, "formQuery",
					this.getClass());
		} finally {

			filterSort = null;
			sortValues = null;
			filterValues = null;
			nonString = null;
			sortTokeneizer = null;
			filterTokenezier = null;
			filterStatus = null;
			sortStatus = null;
			sortColumn = null;
			sortDesc = null;
			format = null;
			nextVal = null;
			log.debug(this.getClass().getName() + " - [formQuery] - " + "Exit");
		}
		return query;
	} // End of formQuery Method

	/**
	 * This function is used to save a system log in the database
	 * @param systemLog
	 * @throws SwtException
	 */
	public void saveSystemLog(SystemLog systemLog) throws SwtException {
		Session session = null;
		Transaction tx = null;
		SwtInterceptor interceptor = null;
		try {
			log.debug(this.getClass().getName() + " saveSystemLog() - Enter");
			interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");
			session = getHibernateTemplate()
						.getSessionFactory()
						.withOptions().interceptor(interceptor).openSession();
			tx = session.beginTransaction();
			session.save(systemLog);
			tx.commit();
			session.close();
			log.debug(this.getClass().getName() + " saveSystemLog() - Exit");
		} catch (Exception e) {
			log.error("Exception occured in saving system log."
					+ e.getMessage());
			throw SwtErrorHandler.getInstance().handleException(e,
					"saveSystemLog", SystemLogDAOHibernate.class);
		}
	}
} // End of class