/*
 * Created on Nov 4, 2005
 *
 * TODO To change the template for this generated file go to
 * Window - Preferences - Java - Code Style - Code Templates
 */
package org.swallow.control.dao.hibernate;

import java.util.Collection;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.orm.hibernate5.support.HibernateDaoSupport;
import org.swallow.control.dao.SectionDAO;
import org.swallow.control.model.Section;
import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.util.SwtInterceptor;
import org.swallow.util.SwtUtil;

import org.hibernate.Session;
import org.hibernate.Transaction;

import org.hibernate.SessionFactory;
import javax.transaction.Transactional;

import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Repository;


@Repository ("sectionDAO")
@Transactional
public class SectionDAOHibernate extends HibernateDaoSupport implements
		SectionDAO {
	/**
	 * Comment for <code>log</code>
	 */
	private final Log log = LogFactory.getLog(SectionDAOHibernate.class);

	
	public SectionDAOHibernate(@Lazy SessionFactory sessionfactory){
	    setSessionFactory(sessionfactory);
	}
	
	/**
	 * @param hostId
	 * @return Collection
	 */
	@SuppressWarnings("unchecked")
	public Collection getSectionList(String hostId) throws SwtException {
		log.debug("Entering getSectionList(Section sec): ");
		try {
			java.util.List list = getHibernateTemplate()
					.find(
							"from Section c where c.id.hostId=?0 order by c.id.sectionId",
							new Object[] { hostId });

			log.debug("noofRecords.size : " + list.size());
			log.debug("exiting getSectionList(Section sec): ");
			return list;
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					+ "- [getSectionList] - Exception " + exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp,
					"getSectionList", SectionDAOHibernate.class);
		}
	}

	/**
	 * @param section
	 *            -object
	 * @return
	 */
	@SuppressWarnings("unchecked")
	public void saveSectionDetail(Section section) throws SwtException {
		Session session = null;
		Transaction tx = null;
		SwtInterceptor interceptor = null;
		try {
			/*
			 * Start : Modified for Mantis 1366-Remove entry to log if duplicate
			 * records added by betcy on 18-02-2011
			 */
			java.util.List records = getHibernateTemplate().find(
					"from Section c where c.id.hostId=?0 and c.id.sectionId=?1",
					new Object[] { section.getId().getHostId(),
							section.getId().getSectionId() });

			if (records.size() == 0) {
				log.debug("entering saveSectionDetail");
				interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");
				session = getHibernateTemplate()
							.getSessionFactory()
							.withOptions().interceptor(interceptor).openSession();
				tx = session.beginTransaction();
				session.save(section);
				tx.commit();
				session.close();
				log.debug("exiting saveSectionDetail");
			} else {
				throw new SwtException(
						"errors.DataIntegrityViolationExceptioninAdd");
			}
			/*
			 * End : Modified for Mantis 1366-Remove entry to log if duplicate
			 * records added by betcy on 18-02-2011
			 */
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					+ "- [saveSectionDetail] - Exception " + exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp,
					"saveSectionDetail", SectionDAOHibernate.class);
		}
	}

	/**
	 * @param sec -
	 *            object
	 * @return
	 */
	public void updateSectionDetail(Section sec) throws SwtException {
		log.debug("Entring updateSectionDetail");
		Session session = null;
		Transaction tx = null;
		SwtInterceptor interceptor = null;
		try {
			interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");
			session = getHibernateTemplate().getSessionFactory().withOptions().interceptor(interceptor).openSession();
			tx = session.beginTransaction();
			session.update(sec);
			tx.commit();
			session.close();
			log.debug("exiting updateSectionDetail");
		} catch (Exception exp) {
			log
					.error(this.getClass().getName()
							+ "- [updateSectionDetail] - Exception "
							+ exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp,
					"updateSectionDetail", SectionDAOHibernate.class);
		}
	}

	/**
	 * @param
	 * 
	 */
	public void deleteSectionDetail(Section section) throws SwtException {
		log.debug("entering deleteSectionDetail");
		Session session = null;
		Transaction tx = null;
		SwtInterceptor interceptor = null;
		
		try {
			interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");
			session = getHibernateTemplate().getSessionFactory().withOptions().interceptor(interceptor).openSession();
			tx = session.beginTransaction();
			session.delete(section);
			tx.commit();
			session.close();
			log.debug("exiting deleteSectionDetail");
		} catch (Exception exp) {
			log
					.error(this.getClass().getName()
							+ "- [deleteSectionDetail] - Exception "
							+ exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp,
					"deleteSectionDetail", SectionDAOHibernate.class);
		}
	}

}
