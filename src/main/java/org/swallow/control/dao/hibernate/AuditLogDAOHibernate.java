/*
 * @(#)AuditLogDAOHibernate.java 23/12/05
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */

package org.swallow.control.dao.hibernate;

import java.sql.CallableStatement;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.Iterator;
import java.util.List;
import java.util.StringTokenizer;




import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.hibernate.*;
import org.springframework.dao.DataAccessResourceFailureException;

import org.swallow.batchScheduler.ConnectionManager;
import org.swallow.control.dao.AuditLogDAO;
import org.swallow.control.model.AuditLog;
import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.maintenance.model.MiscParams;
import org.swallow.util.*;
import org.springframework.context.annotation.Lazy;
import org.springframework.orm.hibernate5.support.HibernateDaoSupport;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.stereotype.Repository;
import org.springframework.beans.factory.annotation.Autowired;
import org.swallow.work.model.ScenarioInstanceLog;

/**
 * 
 * AuditLogDAOHibernate.java<br>
 * <br>
 * 
 * AuditLogDAOHibernate (Controller) class interfaces with model components and
 * prepares data and forwards control to the JSP (Movement Audit Log and User
 * Audit Log) screen.<br>
 * 
 */
@Repository ("auditLogDAO")
@Transactional
public class AuditLogDAOHibernate extends HibernateDaoSupport implements
		AuditLogDAO {
	public AuditLogDAOHibernate(@Lazy SessionFactory sessionfactory) {
	    setSessionFactory(sessionfactory);
	}

	// Initializes Log
	private final Log log = LogFactory.getLog(AuditLogDAOHibernate.class);

	/**
	 * This method is used for getting newly added fields- column name,old value
	 * new value for Movement Audit log screen.<br>
	 * 
	 * @param hostId
	 * @param userId
	 * @param fromDate
	 * @param toDate
	 * @param currentPage
	 * @param maxPage
	 * @param auditLogList
	 * @param filterSortStatus
	 * @param screenFlag
	 * @param source
	 * @param formats
	 * @param dbLink
	 * @param entityId
	 * @return int - Record Count
	 * @throws SwtException
	 */
	@SuppressWarnings( { "unchecked", "cast" })
	public int getAuditLogListUsingStoredProc(String hostId, String userId,
			Date fromDate, Date toDate, int currentPage, int maxPage,
			List<AuditLog> auditLogList, String filterSortStatus,
			String screenFlag, String source, SystemFormats formats,
			String dbLink, String entityId) throws SwtException {
		/* Local Variable declaration */
		// Variable to hold session
		Session session = null;
		// Variable to establish database connection
		Connection connection = null;
		// Variable to hold auditLogStatement
		CallableStatement auditLogStatement = null;
		// Variable to hold pageSize
		int pageSize = 0;
		// Variable to hold showdata
		String showData = null;
		// Variable to hold recordCount
		int recordCount = 0;
		// Variable to hold auditLogResultSet
		ResultSet auditLogResultSet = null;
		// Variable to hold auditLogTotalResultSet
		ResultSet auditLogTotalResultSet = null;
		// Variable to hold filterSortArr
		String[] filterSortArr = null;
		// Variable to hold filterCriteria
		String filterCriteria = null;
		// Variable to hold sortCriteria
		String sortCriteria = null;
		// Variable to hold filterCriteriaArray
		String[] filterCriteriaArray = null;
		// Variable to hold stt
		StringTokenizer strTokenFilterCriteria = null;
		// Variable to hold strTokenFilterCriteria
		StringTokenizer filterCriteriaToken = null;
		// Variable to hold tokenSortCriteria
		StringTokenizer tokenSortCriteria = null;
		// Variable to hold collMiscParams
		Collection collMiscParams = null;
		// Variable to hold itrMiscParams
		Iterator itrMiscParams = null;
		// Variable to hold filterCriteriaCountArray
		String filterCriteriaCountArray = null;
		// Variable to hold filterCriteriaCount
		int filterCriteriaCount = 0;
		// Variable to hold index
		int index = 0;
		// Variable to hold currentFilter
		String currentFilter = null;
		// Variable to hold auditLog
		AuditLog auditLog = null;
		// Variable to hold auditLogDate
		Date auditLogDate = null;
		// Variable to hold loggedInUser
		String loggedInUser = null;
		// Variable to hold reference
		String reference = null;
		// Variable to hold referenceId
		String referenceId = null;
		// Variable to hold action
		String action = null;
		// Variable to hold columnName
		String columnName = null;
		// Variable to hold oldValue
		String oldValue = null;
		// Variable to holdnewValue
		String newValue = null;
		// Variable to hold MiscParams
		MiscParams miscParams = null;
		// Variable to hold countArray
		String[] countArray = null;
		// Variable to hold countArrayIndex
		int countArrayIndex = 0;
		// Variable to filterCount
		int filterCount = 0;
		try {
			log.debug(this.getClass().getName()
					+ " - [getAuditLogListUsingStoredProc()] - " + "Entry");
			// Sets the showData value (Y - Yes and N - No)
			showData = "Y";
			// Code for mapping literals Input, Matched, Unmatched into I, M
			filterSortArr = filterSortStatus.split(",");
			// setting filterCriteria array
			filterCriteria = filterSortArr[0].replaceAll("&gt;", ">")
					.replaceAll("<quot;>", "\"").replaceAll("&amp;", "&")
					.replaceAll("&lt;", "<");
			// setting sortCriteria array
			sortCriteria = filterSortArr[1];
			// Separating the sortCriteria
			sortCriteria = sortCriteria + "|";
			// Setting into strTokenFilterCriteria
			strTokenFilterCriteria = new StringTokenizer(filterCriteria, "|");
			// Setting tokens
			filterCount = strTokenFilterCriteria.countTokens();
			// Creating new string
			countArray = new String[filterCount];
			// Gets the page size
			pageSize=SwtUtil.getPageSizeFromProperty(SwtConstants.SYSTEM_LOG_SCREEN_PAGE_SIZE);
			// counting the tokens
			if (filterCount > 1) {
				// Iterates the filter criteria to set the get the count
				while (strTokenFilterCriteria.hasMoreTokens()) {
					countArray[countArrayIndex] = strTokenFilterCriteria
							.nextToken();
					// Increments the index for each iteration
					countArrayIndex++;
				}
				// Gets the collection of Misc Params for the given entity id
				collMiscParams = (Collection) CacheManager.getInstance()
						.getMiscParams("MOVEMENTLOGACTION", entityId);
				// Iterating the tokens
				itrMiscParams = collMiscParams.iterator();
				// Iterates the misc params and sets the screen flag
				while (itrMiscParams.hasNext()) {
					miscParams = (MiscParams) (itrMiscParams.next());
					// Matching with the array length
					// If screen flag is YES set to Audit log screen
					if (screenFlag.equals(SwtConstants.YES)) {
						 //Start:Code modified by Nageswara Rao on 1-Feb-2012 for mantis 1580:" Spaces should not be saved to end of input values ." -->
						if ((countArray[countArray.length - 4].trim()).equals(miscParams
								.getParValue())) {
							countArray[countArray.length - 4] = miscParams
									.getId().getKey2();
						}
					}
					// If screen flag is NO set to User log screen
					if (screenFlag.equals(SwtConstants.NO)) {
						if (countArray[countArray.length - 1].equals(miscParams
								.getParValue())) {
							countArray[countArray.length - 1] = miscParams
									.getId().getKey2();
						}
					}
				}
				// Sets an empty value for filterCriteriaCountArray
				filterCriteriaCountArray = "";
				// Counting with the array length
				for (int j = 0; j < countArray.length; j++) {
					filterCriteriaCountArray += countArray[j] + "|";
				}
				// Setting array arr1 in to filtercriteria
				filterCriteria = filterCriteriaCountArray;
			}
			// Process further if the source is M (Movement Audit Log)
			if (source.equalsIgnoreCase("M")) {			
				// When the control comes here from the page My user Audit Log
				// Code for splitting the filter string and adding userId
				filterCriteriaToken = new StringTokenizer(filterCriteria, "|");
				// Gets the total count of filter criteria
				filterCriteriaCount = filterCriteriaToken.countTokens();
				// Instantiates the filterCriteriaArray
				filterCriteriaArray = new String[filterCriteriaCount + 1];
				// Sets the default index value
				index = 0;
				// Checks the filterCriteriaCount and sets the current filter
				if (filterCriteriaCount > 1) {
					// Iterates the string tokenizer and gets the user id
					while (filterCriteriaToken.hasMoreTokens()) {
						if (index == 2)
							filterCriteriaArray[index] = userId;
						else
							filterCriteriaArray[index] = filterCriteriaToken
									.nextToken();
						index++;
					}
					// Sets the current filter
					currentFilter = "";
					// Matching with the array length
					for (int j = 0; j < filterCriteriaArray.length; j++) {
						currentFilter += filterCriteriaArray[j] + "|";
					}
					// Setting array arr1 in to currentFilter
					filterCriteria = currentFilter;
				}

				// Code for making the column no. in sync wid backend... 5
				// at front n 6 at backend --*/
				filterCriteriaArray = new String[2];
				// New tonenizer
				tokenSortCriteria = new StringTokenizer(sortCriteria, "|");
				filterCriteriaCount = tokenSortCriteria.countTokens();

				// counting the filterCriteriaArray
				index = 0;
				while (tokenSortCriteria.hasMoreTokens()) {
					filterCriteriaArray[index] = tokenSortCriteria.nextToken();
					index++;
				}
				sortCriteria = "";
				// counting the filterCriteriaArray length
				for (int j = 0; j < filterCriteriaArray.length; j++) {
					if (j == 0) {
						int k = new Integer(filterCriteriaArray[j]).intValue();
						if (k >= 2)
							k = k + 1;
						filterCriteriaArray[j] = Integer.valueOf(k).toString();
					}
					sortCriteria += filterCriteriaArray[j] + "|";
				}
			}
			// Getting session
			session = getHibernateTemplate().getSessionFactory().openSession();
			connection = SwtUtil.connection(session);
			// Calls the stored procedure to get the audit log details
			auditLogStatement = connection
					.prepareCall("{call PK_USER_LOG.SP_USER_LOG(?,?,?,?,?,?,?,?,?,?,?,?,?)}");
			auditLogStatement.setDate(1, SwtUtil.truncateDateTime(fromDate));
			auditLogStatement.setDate(2, SwtUtil.truncateDateTime(toDate));
			auditLogStatement.setString(3, userId);
			auditLogStatement.setInt(4, pageSize);
			auditLogStatement.setInt(5, currentPage);
			auditLogStatement.setString(6, filterCriteria);
			auditLogStatement.setString(7, sortCriteria);
			auditLogStatement.setString(8, showData);
			auditLogStatement.setString(9, source);
			auditLogStatement.registerOutParameter(10,
					oracle.jdbc.OracleTypes.CURSOR);
			auditLogStatement.registerOutParameter(11,
					oracle.jdbc.OracleTypes.CURSOR);
			auditLogStatement.setString(12, dbLink);
			auditLogStatement.setString(13, screenFlag);
			auditLogStatement.execute();
			auditLogResultSet = (ResultSet) auditLogStatement.getObject(10);
			auditLogTotalResultSet = (ResultSet) auditLogStatement
					.getObject(11);
			if (auditLogResultSet != null) {
				while (auditLogResultSet.next()) {
					auditLog = new AuditLog();
					auditLogDate = (Date) auditLogResultSet.getObject(1);
					loggedInUser = auditLogResultSet.getString(2);
					reference = auditLogResultSet.getString(3);
					referenceId = auditLogResultSet.getString(4);
					action = auditLogResultSet.getString(5);
					
					if (action==null) {
						action="";
					}
					
					// Setting position for Newly added fields for Movement
					// audit log
					// If screen flag is YES set to Audit log screen
					if (screenFlag.equals(SwtConstants.YES)) {
						columnName = auditLogResultSet.getString(6);
						oldValue = auditLogResultSet.getString(7);
						newValue = auditLogResultSet.getString(8);
					}

					if (!action.equalsIgnoreCase("R")) { // If action is
						// not Rollover
						auditLog.getId().setLogDate(auditLogDate);
						auditLog.setLogDate_Date(SwtUtil.formatDate(auditLog
								.getId().getLogDate(), formats
								.getDateFormatValue()));
						auditLog.getId().setReference(reference);
						auditLog.getId().setReferenceId(referenceId);
						auditLog.getId().setAction(action);
						auditLog.getId().setUserId(loggedInUser);
						// Newly added fields for Movement audit log
						// If screen flag is YES set to Audit log screen
						if (screenFlag.equals(SwtConstants.YES)) {
							auditLog.getId().setColumnName(columnName);
							auditLog.getId().setOldValue(oldValue);
							auditLog.getId().setNewValue(newValue);

							auditLog.setColumnName(columnName);
							auditLog.setOldValue(oldValue);
							auditLog.setNewValue(newValue);
						} else {
							auditLog.getId().setColumnName("");
							auditLog.getId().setOldValue("");
							auditLog.getId().setNewValue("");

							auditLog.setColumnName("");
							auditLog.setOldValue("");
							auditLog.setNewValue("");
						}
						auditLogList.add(auditLog);
					}
				}
			}
			if (auditLogTotalResultSet != null) {
				while (auditLogTotalResultSet.next()) {
					recordCount = auditLogTotalResultSet.getInt(1);
				}
			}
			log.debug(this.getClass().getName()
					+ " - [getAuditLogListUsingStoredProc()] - " + "Exit");
		} catch (Exception exception) {
			log
					.error(this.getClass().getName()
							+ " - Exception Catched in [getAuditLogListUsingStoredProc] method : - "
							+ exception.getMessage());
			throw new SwtException(
					"Exception Catched in [getAuditLogListUsingStoredProc] method. The exception was '"
							+ exception.getMessage() + "'.");
		} finally {
			
			//edited by KaisBS for mantis 1745 : introducing the implementation of the JDBCCloser class
			SwtException thrownException = null;			
			SQLException sqlException = null;
			
			sqlException = JDBCCloser.close(auditLogResultSet, auditLogTotalResultSet);
			if (sqlException!=null)
				thrownException = new SwtException( "Exception Catched in [getAuditLogListUsingStoredProc] method while closing 'auditLogResultSet/auditLogTotalResultSet'." +
						" The exception was '" + sqlException.getMessage() + "'.");
			
			Object[] exceptions = JDBCCloser.close(null, auditLogStatement, connection, session);
			
			if (thrownException == null && exceptions[0] != null)
				thrownException = new SwtException( "Exception Catched in [getAuditLogListUsingStoredProc] method while closing 'auditLogStatement'. The exception was '"
						+((SQLException) exceptions[0]).getMessage() + "'.");
			
			if (thrownException == null && exceptions[1] !=null) 
				thrownException = new SwtException(	"Exception Catched in [getAuditLogListUsingStoredProc] method while closing session. The exception was '"
								+ ((HibernateException) exceptions[1]).getMessage() + "'.");
			
			if(thrownException!=null)
			throw thrownException;

			
			// Nullifying unreferenced objects goes here
			showData = null;
			filterSortArr = null;
			filterCriteria = null;
			sortCriteria = null;
			filterCriteriaArray = null;
			strTokenFilterCriteria = null;
			filterCriteriaToken = null;
			tokenSortCriteria = null;
			collMiscParams = null;
			itrMiscParams = null;
			filterCriteriaCountArray = null;
			currentFilter = null;
			auditLog = null;
			auditLogDate = null;
			loggedInUser = null;
			reference = null;
			referenceId = null;
			action = null;
			columnName = null;
			oldValue = null;
			newValue = null;
			miscParams = null;
		}
		return recordCount;
	}

	private String getActionKey(String actionPram) {
		String actionKey = "";
		if (actionPram.equalsIgnoreCase("Deleted")) {
			actionKey = "D";
		} else if (actionPram.equalsIgnoreCase("Unmatched")) {
			actionKey = "N";
		} else if (actionPram.equalsIgnoreCase("Rollover")) {
			actionKey = "R";
		} else if (actionPram.equalsIgnoreCase("Matched")) {
			actionKey = "M";
		} else if (actionPram.equalsIgnoreCase("Changed")) {
			actionKey = "U";
		} else if (actionPram.equalsIgnoreCase("Added")) {
			actionKey = "A";
		} else if (actionPram.equalsIgnoreCase("Input")) {
			actionKey = "I";
		} else {
			actionKey = "%";
		}

		return actionKey;
	}

	/**
	 * @param hostId
	 * @param userId
	 * @param referenceId
	 * @return
	 * @throws SwtException
	 */
	@SuppressWarnings("unchecked")
	public Collection getRefIdDetail(String hostId, String userId,
			String reference, String referenceId, Date date,
			String selectedAction) throws SwtException {
		List records = new ArrayList();
		if (selectedAction.equals("Changed")) {
			selectedAction = "U";
		} else if (selectedAction.equals("Matched")) {
			selectedAction = "M";
		} else if (selectedAction.equals("Unmatched")) {
			selectedAction = "N";
		}

		SwtDataSource dataSource = null;
		dataSource = (SwtDataSource) SwtUtil.getBean("dataSourceDb");
		String archDatabaseName = (String) dataSource.useDataSource.get();
		/* Local variable declaration */
		Session session = null;
		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		SimpleDateFormat simpleDateFormat = new SimpleDateFormat(
				"yyyy-MM-dd HH:mm:ss");
		String strDate = simpleDateFormat.format(date);
		if (archDatabaseName != null) {

			try {
				/* Establish the connection using connection manager */

				// Get the hibernate session
				session = getHibernateTemplate().getSessionFactory().openSession();
				// Get the database connection object
				conn = SwtUtil.connection(session);
				ps = conn
						.prepareCall("SELECT host_id, log_date, user_id, ip_address, 'Movement' AS REFERENCE,"
								+ " reference_id, column_name, action, old_value, new_value FROM p_movement_log  "
								+ "where host_Id=? and user_Id =? "
								+ "and reference_Id =? "
								+ "and action = ? and to_char(log_Date,'yyyy-mm-dd hh24:mi:ss') =?");

				ps.setString(1, hostId);
				ps.setString(2, userId);
				ps.setString(3, referenceId);
				ps.setString(4, selectedAction);
				ps.setString(5, strDate);

				rs = ps.executeQuery();

				while (rs.next()) {

					AuditLog auditLog = new AuditLog();
					auditLog.getId().setHostId(rs.getString(1));
					auditLog.getId().setLogDate(rs.getDate(2));
					auditLog.getId().setUserId(rs.getString(3));
					auditLog.getId().setIpAddress(rs.getString(4));
					auditLog.setIpAddress(rs.getString(4));
					auditLog.getId().setReference(rs.getString(5));
					auditLog.getId().setReferenceId(rs.getString(6));
					auditLog.getId().setColumnName(rs.getString(7));
					auditLog.setColumnName(rs.getString(7));
					auditLog.getId().setAction(rs.getString(8));
					auditLog.getId().setOldValue(rs.getString(9));
					auditLog.getId().setNewValue(rs.getString(10));
					auditLog.setOldValue(rs.getString(9));
					auditLog.setNewValue(rs.getString(10));

					records.add(auditLog);
				}

			} catch (Exception e) {

				log
						.error(this.getClass().getName()
								+ " - Exception Catched in [getRefIdDetail] method : - "
								+ e.getMessage());
			} finally {
				
				//edited by KaisBS for mantis 1745 : introducing the implementation of the JDBCCloser class
				JDBCCloser.close(rs, ps, conn, session);
			}
		}

		else {
	PreparedStatement preparedStatement = null;
	ResultSet resultSet =null;
	try {
			session = getHibernateTemplate().getSessionFactory().openSession();
			conn = SwtUtil.connection(session);
			preparedStatement = conn.prepareStatement(getQuery());
			long milliseconds = date.getTime();
			java.sql.Date sqlDate = new java.sql.Date(milliseconds);

			preparedStatement.setString(1, hostId);
			preparedStatement.setString(2, userId);
			preparedStatement.setString(3, reference);
			preparedStatement.setString(4, referenceId);
			preparedStatement.setDate(5, sqlDate);
			preparedStatement.setString(6, selectedAction);
			records = new ArrayList<>();
			resultSet = preparedStatement.executeQuery() ;
				while (resultSet.next()) {
					AuditLog auditLog = new AuditLog();
					auditLog.setHostId(resultSet.getString("HOST_ID"));
					auditLog.getId().setLogDate(resultSet.getDate("LOG_DATE"));
					auditLog.getId().setReference(resultSet.getString("REFERENCE"));
					auditLog.getId().setReferenceId(resultSet.getString("REFERENCE_ID"));
					auditLog.getId().setUserId(resultSet.getString("USER_ID"));
					auditLog.getId().setAction(resultSet.getString("ACTION"));
					auditLog.setIpAddress(resultSet.getString("IP_ADDRESS"));
					auditLog.setColumnName(resultSet.getString("COLUMN_NAME"));
					auditLog.setOldValue(resultSet.getString("OLD_VALUE"));
					auditLog.setNewValue(resultSet.getString("NEW_VALUE"));
					records.add(auditLog);

				}
		} catch (SQLException e) {
			e.printStackTrace();
		}finally {
			SwtException thrownException = null;
			Object[] exceptions = JDBCCloser.close(resultSet, preparedStatement, conn, session);

			if (thrownException == null && exceptions[0] != null)
				thrownException = new SwtException(((SQLException) exceptions[0]).getMessage());

			if (thrownException == null && exceptions[1] !=null)
				thrownException = new SwtException(((HibernateException) exceptions[1]).getMessage());

			if(thrownException!=null)
				throw thrownException;
		}

		}

		return records;
	}

	private static String getQuery() {
		return "SELECT HOST_ID, LOG_DATE, REFERENCE, REFERENCE_ID, USER_ID, ACTION, IP_ADDRESS, COLUMN_NAME, OLD_VALUE, NEW_VALUE FROM MVM_SWEEP_LOG_VIEW WHERE HOST_ID = ? AND USER_ID = ? AND REFERENCE = ? AND REFERENCE_ID = ? AND LOG_DATE = ? AND ACTION = ?";
	}

	@SuppressWarnings("unchecked")
	public int getMatchLogListUsingStoredproc(String matchId,
			List matchLogList, Date fromDate, Date toDate, SystemFormats formats)
			throws SwtException {
		log
				.debug("Entering getMatchLogListUsingStoredproc of AuditLogDAOHibernate");
		Session session = null;
		Connection conn = null;
		CallableStatement cstmt = null;
		int recordcount = 0;
		ResultSet rs = null;
		ResultSet rsTotal = null;

		try {
			session = getHibernateTemplate().getSessionFactory().openSession();
			conn = SwtUtil.connection(session);
			log.debug(" $$$$$$$$$  PK_USER_LOG.SP_MATCH_LOG Called at "
					+ SwtUtil.getTimeWithMilliseconds());
			cstmt = conn
					.prepareCall("{call PK_USER_LOG.SP_MATCH_LOG(?,?,?,?,?)}");

			cstmt.setString(1, matchId);
			cstmt.setDate(2, SwtUtil.truncateDateTime(fromDate));
			cstmt.setDate(3, SwtUtil.truncateDateTime(toDate));
			cstmt.registerOutParameter(4, oracle.jdbc.OracleTypes.CURSOR);
			cstmt.registerOutParameter(5, oracle.jdbc.OracleTypes.CURSOR);
			cstmt.execute();
			log.debug(" $$$$$$$$$  PK_USER_LOG.SP_MATCH_LOG Ended at "
					+ SwtUtil.getTimeWithMilliseconds());
			rs = (ResultSet) cstmt.getObject(4);
			rsTotal = (ResultSet) cstmt.getObject(5);

			if (rs != null) {
				while (rs.next()) {
					AuditLog auditLog = new AuditLog();
					Date date = (Date) rs.getObject(1);
					String user = rs.getString(2);
					String reference = rs.getString(3);
					String referenceId = rs.getString(4);
					String action = rs.getString(5);

					auditLog.getId().setLogDate(date);
					auditLog.setLogDate_Date(SwtUtil
							.formatDate(auditLog.getId().getLogDate(), formats
									.getDateFormatValue()));
					auditLog.getId().setReference(reference);
					auditLog.getId().setReferenceId(referenceId);
					auditLog.getId().setAction(action);
					auditLog.getId().setUserId(user);
					matchLogList.add(auditLog);

				}
			}
			if (rsTotal != null) {
				while (rsTotal.next()) {
					recordcount = rsTotal.getInt(1);
				}
			}

		} catch (DataAccessResourceFailureException dataAccessException) {
			log
					.error(this.getClass().getName()
							+ " - Exception Catched in [getMatchLogListUsingStoredproc] method : - "
							+ dataAccessException.getMessage());
			throw SwtErrorHandler.getInstance().handleException(
					dataAccessException, "getMatchLogListUsingStoredproc",
					AuditLogDAOHibernate.class);
		} catch (IllegalStateException illegalStateException) {
			log
					.error(this.getClass().getName()
							+ " - Exception Catched in [getMatchLogListUsingStoredproc] method : - "
							+ illegalStateException.getMessage());
			throw SwtErrorHandler.getInstance().handleException(
					illegalStateException, "getMatchLogListUsingStoredproc",
					AuditLogDAOHibernate.class);
		} catch (HibernateException hibernateException) {
			log
					.error(this.getClass().getName()
							+ " - Exception Catched in [getMatchLogListUsingStoredproc] method : - "
							+ hibernateException.getMessage());
			throw SwtErrorHandler.getInstance().handleException(
					hibernateException, "getMatchLogListUsingStoredproc",
					AuditLogDAOHibernate.class);
		} catch (SQLException sqlException) {
			log
					.error(this.getClass().getName()
							+ " - Exception Catched in [getMatchLogListUsingStoredproc] method : - "
							+ sqlException.getMessage());
			throw SwtErrorHandler.getInstance().handleException(sqlException,
					"getMatchLogListUsingStoredproc",
					AuditLogDAOHibernate.class);
		} finally {
			
			//edited by KaisBS for mantis 1745 : introducing the implementation of the JDBCCloser class
			SwtException thrownException = null;
			SQLException sqlException = null;
				
			sqlException = JDBCCloser.close(rs, rsTotal);
			if (sqlException!=null)
				thrownException = new SwtException(sqlException.getMessage());
			
			Object[] exceptions = JDBCCloser.close(null, cstmt, conn, session);
			
			if (thrownException == null && exceptions[0] != null)
				thrownException = new SwtException(((SQLException) exceptions[0]).getMessage());
			
			if (thrownException == null && exceptions[1] !=null) 
				thrownException = new SwtException(((HibernateException) exceptions[1]).getMessage());
			
			if(thrownException!=null)
			throw thrownException;
		}

		return recordcount;

	}

}