package org.swallow.control.dao.hibernate;

import org.swallow.cluster.ZkUtils;
import org.swallow.control.dao.ConnectionPoolControlDAO;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.Reader;
import java.lang.*;
import java.sql.CallableStatement;
import java.sql.Clob;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Types;
import java.util.*;
import org.swallow.exception.SwtException;
import org.swallow.exception.SwtErrorHandler;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.swallow.control.model.ConnectionPool;

import org.swallow.util.JDBCCloser;
import org.swallow.util.SwtUtil;
import org.springframework.context.annotation.Lazy;
import org.springframework.orm.hibernate5.support.HibernateDaoSupport;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.stereotype.Repository;
import org.springframework.beans.factory.annotation.Autowired;
import org.hibernate.HibernateException;
import org.hibernate.SessionFactory;
import org.hibernate.Transaction;
import org.hibernate.Session;



@Repository ("connectionPoolControlDAO")
@Transactional
public class ConnectionPoolControlDAOHibernate extends HibernateDaoSupport implements ConnectionPoolControlDAO {
	public ConnectionPoolControlDAOHibernate(@Lazy SessionFactory sessionfactory) {
	    setSessionFactory(sessionfactory);
	}


	private final Log log = LogFactory.getLog(ConnectionPoolControlDAOHibernate.class);

	public ConnectionPool getConnectionPool(ConnectionPool conn, String moduleId) throws SwtException {
		Iterator itr;
		CallableStatement cstmt = null;
		String call = null;
		Connection connection = null;
		Session session = null;
		String clientIdentifiersAsString = "";
		ResultSet rsConnectionPool = null;
		String connectionId;
		String status = null;
		try {
			log.debug(this.getClass().getName() + " - [getConnectionPoolList] - Enter");
			clientIdentifiersAsString = conn.getId().getConnectionId(); 
			if (!SwtUtil.isEmptyOrNull(moduleId) && moduleId.equals("PCM")) {
				session = SwtUtil.pcSessionFactory.openSession();

			} else {
				session = SwtUtil.sessionFactory.openSession();
			}
			connection = SwtUtil.connection(session);

			call = "{ ? = call STL_PKG_S_SESSIONS.FN_GET_SESSIONS(?)}";
			cstmt = connection.prepareCall(call);

			cstmt.registerOutParameter(1, oracle.jdbc.OracleTypes.CURSOR);
			cstmt.setString(2, clientIdentifiersAsString);
			cstmt.execute(); /* execute the callablestatement */

			rsConnectionPool = (ResultSet) cstmt.getObject(1);

			while (rsConnectionPool.next()) {
				connectionId = rsConnectionPool.getString("CLIENT_IDENTIFIER");
				conn.setAction("");
				conn.setAudSid(rsConnectionPool.getString("audsid"));
				conn.setSsId(rsConnectionPool.getString("sid"));

				conn.setHighlight(false);
				status = rsConnectionPool.getString("STATUS");
				conn.setIsIdle(!SwtUtil.isEmptyOrNull(status) && status.equals("INACTIVE"));
				conn.setIsOpened(!SwtUtil.isEmptyOrNull(status) && status.equals("ACTIVE"));
				conn.setSqlId(rsConnectionPool.getString("sql_id"));
				conn.setSqlStatement(rsConnectionPool.getString("sql_text"));
			}

			conn.setEndTime(new Date());
			conn.setDuration(conn.getStartTime() != null
					? new Double((conn.getEndTime().getTime() - conn.getStartTime().getTime()) / 1000)
					: 0);


			log.debug(this.getClass().getName() + " - [getConnectionPoolList] - Exit");
		} catch (Exception e) {
			log.error(this.getClass().getName() + " - Exception Catched in [getConnectionPoolList] method : - "
					+ e.getMessage());
			throw SwtErrorHandler.getInstance().handleException(e, "getConnectionPoolList",
					ConnectionPoolControlDAOHibernate.class);
		} finally {

			// edited by KaisBS for mantis 1745 : introducing the implementation of the
			// JDBCCloser class

			try {
				JDBCCloser.close(rsConnectionPool, cstmt, connection, session);
			} catch (Exception e) {
				log.error(this.getClass().getName() + " - Exception Catched in [getConnectionPoolList] method : - "
						+ e.getMessage());
			}
		}

		return conn;
	}

	public static ConnectionPool findConnectionById(ArrayList<ConnectionPool> listConnections, String connecitonId) {
		return listConnections.stream().filter(carnet -> connecitonId.equals(carnet.getId().getConnectionId()))
				.findFirst().orElse(null);
	}

	public static String clobStringConversion(Clob clb) throws IOException, SQLException {
		if (clb == null)
			return "";

		StringBuffer str = new StringBuffer();
		String strng;

		BufferedReader bufferRead = new BufferedReader(clb.getCharacterStream());

		while ((strng = bufferRead.readLine()) != null)
			str.append(strng);

		return str.toString();
	}

	public ArrayList<ConnectionPool> getConnectionPoolList(ArrayList<ConnectionPool> openConnections, String moduleId)
			throws SwtException {
		CallableStatement cstmt = null;
		String call = null;
		Connection connection = null;
		Session session = null;
		String clientIdentifiersAsString = "";
		ResultSet rsConnectionPool = null;
		ConnectionPool conn = null;
		String connectionId;
		String status = null;
		try {
			log.debug(this.getClass().getName() + " - [getConnectionPoolList] - Enter");
			for (int i = 0; i < openConnections.size(); i++) {
				clientIdentifiersAsString += openConnections.get(i).getId().getConnectionId() + ",";
			}
			if (clientIdentifiersAsString.length() > 0) {
				clientIdentifiersAsString = clientIdentifiersAsString.substring(0,
						clientIdentifiersAsString.length() - 1);
			}
			if (!SwtUtil.isEmptyOrNull(moduleId) && moduleId.equals("PCM")) {
				session = SwtUtil.pcSessionFactory.openSession();

			} else {
				session = SwtUtil.sessionFactory.openSession();
			}
			connection = SwtUtil.connection(session);

			call = "{ ? = call STL_PKG_S_SESSIONS.FN_GET_SESSIONS(?,?)}";
			cstmt = connection.prepareCall(call);

			cstmt.registerOutParameter(1, oracle.jdbc.OracleTypes.CURSOR);
			cstmt.setString(2, clientIdentifiersAsString);
			cstmt.setString(3, null);
			cstmt.execute(); /* execute the callablestatement */

			rsConnectionPool = (ResultSet) cstmt.getObject(1);

			while (rsConnectionPool.next()) {
				connectionId = rsConnectionPool.getString("CLIENT_IDENTIFIER");
				conn = findConnectionById(openConnections, connectionId);

				conn.setAction("");
				conn.setAudSid(rsConnectionPool.getString("audsid"));
				conn.setSsId(rsConnectionPool.getString("sid"));

				conn.setHighlight(false);
				conn.setSqlStatus(status);
				status = rsConnectionPool.getString("STATUS");
				conn.setSqlId(rsConnectionPool.getString("sql_id"));
				conn.setSqlStatement(rsConnectionPool.getString("sql_text"));
//				conn.setIsClosed(conn.getConnection().isClosed());
//				if (conn.getConnection() != null) {
//					if (conn.getConnection().isClosed()) {
//						conn.setStatus("CLOSED");
//						conn.setLastJDBCstatus("CLOSED");
//					}
//					else {
//						conn.setStatus("OPEN");
//						conn.setLastJDBCstatus("OPEN");
//					}
//				} else {
//					conn.setStatus(null);
//				}


				conn.setIsIdle(!SwtUtil.isEmptyOrNull(status) && status.equals("INACTIVE"));
				conn.setIsOpened(!SwtUtil.isEmptyOrNull(status) && status.equals("ACTIVE"));
//				conn.setLastActionTime(rsConnectionPool.getDate("sql_exec_start"));

			}

			for (int i = 0; i < openConnections.size(); i++) {
				conn = openConnections.get(i);
				conn.setEndTime(new Date());
				conn.setDuration(conn.getStartTime() != null
						? new Double((conn.getEndTime().getTime() - conn.getStartTime().getTime()) / 1000)
						: 0);

//				if (conn.getStatus() == null) {
//					if (conn.getConnection() != null) {
//						if (conn.getConnection().isClosed()) {
//							conn.setStatus("CLOSED");
//							conn.setLastJDBCstatus("CLOSED");
//						}
//						else {
//							conn.setStatus("OPEN");
//							conn.setLastJDBCstatus("OPEN");
//						}
//					} else {
//						conn.setStatus(null);
//					}
//				}
			}

			log.debug(this.getClass().getName() + " - [getConnectionPoolList] - Exit");
		} catch (Exception e) {
			log.error(this.getClass().getName() + " - Exception Catched in [getConnectionPoolList] method : - "
					+ e.getMessage());
			throw SwtErrorHandler.getInstance().handleException(e, "getConnectionPoolList",
					ConnectionPoolControlDAOHibernate.class);
		} finally {

			// edited by KaisBS for mantis 1745 : introducing the implementation of the
			// JDBCCloser class

			try {
				JDBCCloser.close(rsConnectionPool, cstmt, connection, session);
			} catch (Exception e) {
				log.error(this.getClass().getName() + " - Exception Catched in [getConnectionPoolList] method : - "
						+ e.getMessage());
			}
		}

		return openConnections;
	}
	
	
	public ArrayList<ConnectionPool> getConnectionPoolListByModule(String moduleId)
			throws SwtException {
		CallableStatement cstmt = null;
		String call = null;
		Connection connection = null;
		Session session = null;
		ResultSet rsConnectionPool = null;
		ConnectionPool conn = null;
		String status = null;
		ArrayList<ConnectionPool>  openConnections = new ArrayList<ConnectionPool>();
		try {
			
			
			String runningJobUid = ZkUtils.getProperty(ZkUtils.PROPERTY_CHOSEN_INSTANCE,10000);
			//System.err.println("getConnectionPoolListByModule runningJobUid ====="+runningJobUid);
			if(runningJobUid != null && !ZkUtils.instanceUuid.equals(runningJobUid)) {
				//System.err.println("it's not me connection pool runnder");
				return new ArrayList<ConnectionPool>();
			}
			//System.err.println("it's me connection pool runnder");
			log.debug(this.getClass().getName() + " - [getConnectionPoolList] - Enter");
			if (!SwtUtil.isEmptyOrNull(moduleId) && moduleId.equals("PCM")) {
				session = SwtUtil.pcSessionFactory.openSession();
				
			} else {
				session = SwtUtil.sessionFactory.openSession();
			}
			connection = SwtUtil.connection(session);
			
			call = "{ ? = call STL_PKG_S_SESSIONS.FN_GET_SESSIONS(?,?)}";
			cstmt = connection.prepareCall(call);
			
			cstmt.registerOutParameter(1, oracle.jdbc.OracleTypes.CURSOR);
			cstmt.setString(2, null);
			cstmt.setString(3, moduleId);
			cstmt.execute(); /* execute the callablestatement */
			
			rsConnectionPool = (ResultSet) cstmt.getObject(1);
			
			while (rsConnectionPool.next()) {
				conn = new ConnectionPool();
				conn.getId().setConnectionId(rsConnectionPool.getString("CLIENT_IDENTIFIER"));
				conn.setAction("");
				conn.setAudSid(rsConnectionPool.getString("audsid"));
				conn.setSsId(rsConnectionPool.getString("sid"));
				conn.setSqlExecStartTime(rsConnectionPool.getDate("last_time"));
				conn.setHighlight(false);
//				conn.setIsClosed(conn.getConnection().isClosed());
				status = rsConnectionPool.getString("STATUS");
				conn.setSqlStatus(status);
				conn.setIsIdle(!SwtUtil.isEmptyOrNull(status) && status.equals("INACTIVE"));
				conn.setIsOpened(!SwtUtil.isEmptyOrNull(status) && status.equals("ACTIVE"));
				conn.setSqlId(rsConnectionPool.getString("sql_id"));
				conn.setSqlStatement(rsConnectionPool.getString("sql_text"));
				openConnections.add(conn);
			}
			
			log.debug(this.getClass().getName() + " - [getConnectionPoolList] - Exit");
		} catch (Exception e) {
			log.error(this.getClass().getName() + " - Exception Catched in [getConnectionPoolList] method : - "
					+ e.getMessage());
			throw SwtErrorHandler.getInstance().handleException(e, "getConnectionPoolList",
					ConnectionPoolControlDAOHibernate.class);
		} finally {
			
			// edited by KaisBS for mantis 1745 : introducing the implementation of the
			// JDBCCloser class
			try {
				JDBCCloser.close(rsConnectionPool, cstmt, connection, session);
			} catch (Exception e) {
				log.error(this.getClass().getName() + " - Exception Catched in [getConnectionPoolList] method : - "
						+ e.getMessage());
			}
		}
		
		return openConnections;
	}

	/**
	 * This method is used to kill DB session using connection identifiers
	 *
	 * @param connectionsIds
	 * @return
	 * @throws SwtException
	 */
	public void killDBSessionConnections(String moduleId, String connectionsIds) throws SwtException {
		CallableStatement cstmt = null;
		String call = null;
		Connection connection = null;
		Session session = null;
		try {
			log.debug(this.getClass().getName() + " - [getConnectionPoolList] - Enter");
			if (!SwtUtil.isEmptyOrNull(moduleId) && moduleId.equals("PCM")) {
				session = SwtUtil.pcSessionFactory.openSession();

			} else {
				session = SwtUtil.sessionFactory.openSession();
			}
			connection = SwtUtil.connection(session);
			call = "{ call STL_PKG_S_SESSIONS.SP_KILL_SESSIONS(?,?)}";
			cstmt = connection.prepareCall(call);

			cstmt.setString(1, connectionsIds);
			cstmt.setString(2, moduleId);
			cstmt.execute(); /* execute the callablestatement */

			log.debug(this.getClass().getName() + " - [getConnectionPoolList] - Exit");
		} catch (Exception e) {
			log.error(this.getClass().getName() + " - Exception Catched in [getConnectionPoolList] method : - "
					+ e.getMessage());
		} finally {

			try {
				JDBCCloser.close(null, cstmt, connection, session);
			} catch (Exception e) {
				log.error(this.getClass().getName() + " - Exception Catched in [getConnectionPoolList] method : - "
						+ e.getMessage());
			}
		}

	}
	
	/**
	 * This method is used to Check if the Predict user has access to v$session and v$active_session_history
	 *
	 * @param connectionsIds
	 * @return
	 * @throws SwtException
	 */
	public boolean checkDBViewsGrant(String moduleId) throws SwtException {
		CallableStatement cstmt = null;
		String call = null;
		Connection connection = null;
		Session session = null;
		String rsResult = null;
		boolean result = false;
		try {
			log.debug(this.getClass().getName() + " - [getConnectionPoolList] - Enter");
			if (!SwtUtil.isEmptyOrNull(moduleId) && moduleId.equals("PCM")) {
				session = SwtUtil.pcSessionFactory.openSession();
				
			} else {
				session = SwtUtil.sessionFactory.openSession();
			}
			connection = SwtUtil.connection(session);
			
			call = "{ ? = call STL_PKG_S_SESSIONS.FN_IS_SESSION_GRANTED()}";
			
			cstmt = connection.prepareCall(call);
			cstmt.registerOutParameter(1, oracle.jdbc.OracleTypes.VARCHAR);
			
			cstmt.execute(); /* execute the callablestatement */
			rsResult =  cstmt.getString(1);
			log.debug(this.getClass().getName() + " - [getConnectionPoolList] - Exit");
			
			
			return !SwtUtil.isEmptyOrNull(rsResult) && rsResult.equals("Y");
		} catch (Exception e) {
			log.error(this.getClass().getName() + " - Exception Catched in [getConnectionPoolList] method : - "
					+ e.getMessage());
		} finally {
			
			try {
				JDBCCloser.close(null, cstmt, connection, session);
			} catch (Exception e) {
				log.error(this.getClass().getName() + " - Exception Catched in [getConnectionPoolList] method : - "
						+ e.getMessage());
			}
		}
		return result;
		
	}
	
	/**
	 * This method is used to update S_POOL_STATS table with the latest connection pool details
	 *
	 * @param connectionsIds
	 * @return
	 * @throws SwtException
	 */
	public void updatePoolStatsTable(String moduleId, int numActive, int numIdle, int maxActive, int maxIdle ) throws SwtException {
		CallableStatement cstmt = null;
		String call = null;
		Connection connection = null;
		Session session = null;
		ResultSet rset = null;
		try {
			log.debug(this.getClass().getName() + " - [getConnectionPoolList] - Enter");
			session = SwtUtil.sessionFactory.openSession();
			connection = SwtUtil.connection(session);
			 String sql1 = "SELECT POOL_NAME FROM S_POOL_STATS WHERE POOL_NAME = ?" ;
			 String sqlInsert = "INSERT INTO S_POOL_STATS (UPDATE_DATE, POOL_NAME, MAX_ACTIVE,MAX_IDLE,TOTAL_IDLE,TOTAL_ACTIVE) VALUES (?,?,?,?,?,?)";
			 String sqlUpdate = "UPDATE S_POOL_STATS SET UPDATE_DATE = ?, MAX_ACTIVE = ?, MAX_IDLE = ? ,TOTAL_IDLE = ?,TOTAL_ACTIVE = ?  WHERE POOL_NAME = ?";


			 PreparedStatement pstmt1 = connection.prepareStatement(sql1);
			 PreparedStatement pstmt2 = connection.prepareStatement(sqlUpdate);
			 PreparedStatement pstmt3 = connection.prepareStatement(sqlInsert);

			  pstmt1.setString(1, moduleId);
			  rset = pstmt1.executeQuery();
			  java.sql.Date startdate =  new java.sql.Date(new Date().getTime());
			  if (rset.next()) {
				   
				   pstmt2.setDate(1, startdate);
				   pstmt2.setInt(2,maxActive);
				   pstmt2.setInt(3,maxIdle);
				   pstmt2.setInt(4,numIdle);
				   pstmt2.setInt(5,numActive);
				   
				   pstmt2.setString(6,moduleId);
				   pstmt2.executeUpdate(); 
			  
			  }
			  else
			   {
				   pstmt3.setDate(1, startdate);
				   pstmt3.setString(2, moduleId);
				   pstmt3.setInt(3,maxActive);
				   pstmt3.setInt(4,maxIdle);
				   pstmt3.setInt(5,numIdle);
				   pstmt3.setInt(6,numActive);
				   pstmt3.executeUpdate();
			   }
			   // Commit the effect of all both the INSERT and UPDATE 
			   // statements together 
			   connection.commit();
			
			log.debug(this.getClass().getName() + " - [getConnectionPoolList] - Exit");
			
		} catch (Exception e) {
			log.error(this.getClass().getName() + " - Exception Catched in [getConnectionPoolList] method : - "
					+ e.getMessage());
		} finally {
			
			try {
				JDBCCloser.close(null, cstmt, connection, session);
			} catch (Exception e) {
				log.error(this.getClass().getName() + " - Exception Catched in [getConnectionPoolList] method : - "
						+ e.getMessage());
			}
		}
	}
}