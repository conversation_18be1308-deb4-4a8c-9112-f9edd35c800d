/*
 * @(#)InterfaceInterruptDAOHibernate.java 1.0 31/05/2007
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.control.dao.hibernate;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.Statement;
import java.text.DateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collection;
import java.util.Date;
import java.util.Iterator;
import java.util.List;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

import org.springframework.context.annotation.Primary;
import org.swallow.batchScheduler.ConnectionManager;
import org.swallow.control.dao.InterfaceInterruptDAO;
import org.swallow.control.model.Notifications;
import org.swallow.util.JDBCCloser;
import org.swallow.util.PropertiesFileLoader;
import org.swallow.util.SwtConstants;
import org.swallow.util.SwtUtil;
import org.springframework.context.annotation.Lazy;
import org.springframework.orm.hibernate5.support.HibernateDaoSupport;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.stereotype.Repository;
import org.springframework.beans.factory.annotation.Autowired;
import org.hibernate.HibernateException;
import org.hibernate.SessionFactory;
import org.hibernate.Transaction;
import org.hibernate.type.StringType;
import org.hibernate.type.Type;
import org.hibernate.Session;





/**
 *
 * <AUTHOR> Kannan Jeyapaul
 * Class that implements the InterfaceDAOHibernate and acts as DAO layer for all database operations
 *
 */
@Repository ("interfaceInterruptDAO")
@Primary
@Transactional
public class InterfaceInterruptDAOHibernate extends HibernateDaoSupport implements InterfaceInterruptDAO{
	public InterfaceInterruptDAOHibernate(@Lazy SessionFactory sessionfactory) {
	    setSessionFactory(sessionfactory);
	}


	/*
	 * Log instance
	 */
	private final Log log = LogFactory.getLog(InterfaceInterruptDAOHibernate.class);

	/**
	 * Get the end alert time for the interface from P_INTERFACE_EXTENSION table
	 * @param String interfaceId
	 * @return Date
	 */
	public Date getEndAlertTime(String interfaceId, boolean fromPCM) {

		//Collection object to store max alert time
		Collection<String> endAlertTimes = null;
		//Iterator to iterate through collection returned by query
		Iterator<String> itr = null;
		//String to hold the maximum alert time
		String endAlertTime = null;

		try{
			log.debug(this.getClass().getName() + "- [getEndAlertTime] - Entry ");

			//Get hibernate template and form query
			if (fromPCM) {
				endAlertTimes = (Collection<String> ) getHibernateTemplate().find("SELECT P.endAlertTime from PCInterfaceInterruption P where P.id.messageType = ?0",
								new Object[]{interfaceId});
			} else {
				endAlertTimes = (Collection<String> ) getHibernateTemplate().find("SELECT P.endAlertTime from InterfaceInterruption P where P.id.messageType = ?0",
								new Object[]{interfaceId});
			}
			/* If collection value is null then return null  */
			if(endAlertTimes.size()>0 && (endAlertTimes!=null)){
				itr = endAlertTimes.iterator();
				if(itr.hasNext()){
					endAlertTime = itr.next();

				}
				/* If the retrieved value is null,throw the null pointer exception */
				if(SwtUtil.isEmptyOrNull(endAlertTime)){
			      log.debug(this.getClass().getName() + " - [getEndAlertTime] - " +
					 "No value has been entered for EndAlertTime in Interface Monitor Screen");
					return null;
				}

			}else{
			  log.debug(this.getClass().getName() + " - [getEndAlertTime] - " +
				 "No value has been entered for EndAlertTime in Interface Monitor Screen");
				return null;
			}


			log.debug(this.getClass().getName() + " - [getEndAlertTime] - Exit");
		}catch(Exception e){
			log.debug(this.getClass().getName() + " - [getEndAlertTime] - Exception - "+ e.getMessage());
			log.error(this.getClass().getName() + " - [getEndAlertTime] - Exception - "+ e.getMessage());
			e.printStackTrace();
		}finally{

		}

		return getTime(endAlertTime);
	}

	/**
	 * Get the start alert time for the  interface from P_INTERFACE_EXTENSION table
	 * @param String interfaceId
	 * @return Date
	 */
	public Date getStartAlertTime(String interfaceId, boolean fromPCM){

		//Collection to store minimum alert times
		Collection<String> minAlertTimes = null;
		//Iterator to iterate through results of query execution
		Iterator<String> itr = null;
		//String value of minimum alert time
		String minAlertTime = null;


		try{
			log.debug(this.getClass().getName() + "- [getStartAlertTime] - Enttry ");

			//Get hibernate template and get the minimum of start alert time
			if (fromPCM) {
				minAlertTimes = (Collection<String> ) getHibernateTemplate().find("SELECT P.startAlertTime from PCInterfaceInterruption P where P.id.messageType = ?0",
								new Object[]{interfaceId});
			} else {
				minAlertTimes = (Collection<String> ) getHibernateTemplate().find("SELECT P.startAlertTime from InterfaceInterruption P where P.id.messageType = ?0",
								new Object[]{interfaceId});
			}
			if( (minAlertTimes.size()>0) && (minAlertTimes!=null)){
				itr = minAlertTimes.iterator();
				if(itr.hasNext()){
					//Get the minimum alert time
					minAlertTime = itr.next();
				}
				/* If the retrieved value is null,throw the null pointer exception */
				if(SwtUtil.isEmptyOrNull(minAlertTime)){
				 log.debug(this.getClass().getName() + " - [getStartAlertTime] - " +
						 "No value has been entered for StartAlertTime in Interface Monitor Screen");
					return null;
				}
			}else{
				 log.debug(this.getClass().getName() + " - [getStartAlertTime] - " +
				 "No value has been entered for StartAlertTime in Interface Monitor Screen");
				return null;
			}


			log.debug(this.getClass().getName() + " - [getStartAlertTime] - Exit ");
		}catch(Exception e){
			log.debug(this.getClass().getName() + " - [getStartAlertTime] - Exception - "+ e.getMessage());
			log.error(this.getClass().getName() + " - [getStartAlertTime] - Exception - "+ e.getMessage());
			e.printStackTrace();
		}finally{

		}
		return getTime(minAlertTime);
	}


	/**
	 * Insert the notification passed after checking
	 * the relational id for the purpose of inserting error
	 * message when service is unable to connect.
	 * @param Notifications notification
	 * @return boolean
	 */
	public boolean insertNotifications(Notifications notification, boolean fromPCM){
		//boolean variable to hold the result of insertion
		boolean result = false;
		// connection object to create db connection
		Connection conn=null;
		//sql statement object to execute query
		PreparedStatement pstmt=null;
		// string object for holding insert query
		String insert_Notifications="";
		//Collection object to get relational id
		Collection<String> notification_Alert=null;
		Session session = null;

		try{
			log.debug(this.getClass().getName() + " - [insertNotifications] - Entry ");
			//execute  db connection using common data manager instance
			if (fromPCM) {
				session = SwtUtil.pcSessionFactory.openSession();
				conn = SwtUtil.connection(session);
			} else {
				conn = ConnectionManager.getInstance().databaseCon();
			}
			//create statement ,assigned to sql statement object
			
			//check relational id is equal with constant value 'Alert'
			if(notification.getRelationId().equals(SwtConstants.INTERFACE_NOTIFICATION_ERRTYPE)){
				if (fromPCM) {
					//fetch relational id from P_Notifications table
					notification_Alert = (Collection<String> )getHibernateTemplate().find("SELECT n.relationId from PCNotifications n where n.relationId = ?0",new Object[]{notification.getRelationId()});
				} else {
					//fetch relational id from P_Notifications table
					notification_Alert = (Collection<String> ) getHibernateTemplate().find("SELECT n.relationId from Notifications n where n.relationId = ?0",new Object[]{notification.getRelationId()});
				}
			  /*If relational id value exists,nothing will be inserted
				  and control returns back with false */
				if(notification_Alert.size()>0){
					return false;
				}
			}
			// string for insertion
			if (fromPCM) {
				insert_Notifications="INSERT INTO PC_NOTIFICATIONS(HOST_ID, ENTITY_ID, NOTIFICATION_ID, RELATION_ID,NOTIFICATION_TYPE,PRIORITY, NOTIFICATION_MESSAGE) VALUES(?,?,SEQ_PC_NOTIFICATIONS.NEXTVAL,?,?,?,?)";
			} else {
				insert_Notifications="INSERT INTO P_NOTIFICATIONS(HOST_ID, ENTITY_ID, NOTIFICATION_ID, RELATION_ID,NOTIFICATION_TYPE,PRIORITY, NOTIFICATION_MESSAGE) VALUES(?,?,P_NOTIFICATIONS_SEQUENCE.NEXTVAL,?,?,?,?)";
			}
			pstmt=conn.prepareStatement(insert_Notifications);
			pstmt.setString(1, notification.getHostId());
			pstmt.setString(2, notification.getEntityId());
			pstmt.setString(3, notification.getRelationId());
			pstmt.setString(4, notification.getNotificationType());
			if(notification.getPriority()!=null)
				pstmt.setLong(5, notification.getPriority().longValue());
			else 
				pstmt.setNull(5, java.sql.Types.BIGINT);
				
			pstmt.setString(6, notification.getNotificationMessage());
			//execute the query
			pstmt.executeUpdate();
			//commit the transaction
			conn.commit();
			//Make the result as true
			result = true;
			log.debug(this.getClass().getName() + " - [insertNotifications] - Exit ");
		}catch(Exception e){
			log.debug(this.getClass().getName() + " - [insertNotifications] - Exception - "+ e.getMessage());
			log.error(this.getClass().getName() + " - [insertNotifications] - Exception - "+ e.getMessage());
			result = false;
			e.printStackTrace();
		}finally{
			
			//edited by KaisBS for mantis 1745 : introducing the implementation of the JDBCCloser class
			JDBCCloser.close(pstmt);
			
			try{
					JDBCCloser.close(null,pstmt,conn,session);
			}catch(Exception e){
				
				logger.error("org.swallow.control.dao.hibernate.InterfaceInterruptDAOHibernate - " +
						"[insertNotifications] - Exception - " + e.getMessage());         
			}
		}
		return result;
	}

	/**
	 * Fetches all the notification for the given notification type
	 * @param Object[] notificationsType
	 * @return Notifications
	 */
	public Collection getAllNotifications(Object[] notificationsType){

		//Collection to hold the notifications for host id, entity id and notification id combination
		Collection fetchedNotifications = null;
		Collection fetchedNotificationsPCM = null;
		Collection newfetchedNotificationsPCM = null;
		//Get the notification object
		String pcmEnabled = null;
		Session session = null;
		Class[] classList = null;
		
		try{
			log.debug(this.getClass().getName() + "- [getAllNotifications] - Entry");
			newfetchedNotificationsPCM = new ArrayList();
			//Fetch the notification with host id, entity id and notification id combination
			fetchedNotifications = (Collection ) getHibernateTemplate().find("from Notifications n where n.notificationType in (?0, ?1)",
											new Object[]{notificationsType[0], notificationsType[1]});
			pcmEnabled = PropertiesFileLoader.getInstance()
					.getPropertiesValue(SwtConstants.PCM_ENABLED);
			if ("true".equals(pcmEnabled)) {

				if(notificationsType.length> 0 ){
					classList = new Class[notificationsType.length];
					classList[0] = String.class;
					for(int i = 1; i < notificationsType.length ; i++){
						classList[i] = String.class;
					}
				}
				fetchedNotificationsPCM = (Collection) SwtUtil.invokeStaticMethod("org.swallow.pcm.util.PCMUtil",
						"getAllPCMNotifications", notificationsType, classList);
				fetchedNotifications.addAll(fetchedNotificationsPCM);
			}
			log.debug(this.getClass().getName() + " - [getAllNotifications] - Exit ");
		}catch(Exception e){
			log.debug(this.getClass().getName() + " - [getAllNotifications] - Exception - "+ e.getMessage());
			log.error(this.getClass().getName() + " - [getAllNotifications] - Exception - "+ e.getMessage());
			e.printStackTrace();
		} 
		return fetchedNotifications;
	}

	/**
	 * Delete the notification passed
	 * @param Notifications notification
	 * @return boolean
	 */
	public boolean deleteNotifications(Notifications notification, boolean fromPCM){
		//Boolean variable to hold deletion status
		boolean result = false;
		// This list is to store values of notification
		List data_Notification=null;
		//declare a connection object
		Connection conn=null;
		//statement object is initialized
		PreparedStatement pst=null;
		//String variable for holding delete query
		String delete_notifications="";
		Session session = null;

		try{
			log.debug(this.getClass().getName() + "- [deleteNotifications] - Entry");
			//  call connection manager object for establish the connection
			if (fromPCM) {
			session = SwtUtil.pcSessionFactory.openSession();
		    	conn = SwtUtil.connection(session);
			} else {
				conn=ConnectionManager.getInstance().databaseCon();
			}
			    
		    //delete query	under checking HOST_ID, ENTITY_ID,  RELATION_ID,NOTIFICATION_TYPE,
		    if(SwtUtil.isEmptyOrNull(notification.getRelationId())) {
		    	if (fromPCM) {
		    		delete_notifications="DELETE FROM PC_NOTIFICATIONS where HOST_ID=? and NOTIFICATION_TYPE in('"+SwtConstants.INTERFACE_NOTIFICATION_TYPE+"','"+SwtConstants.MESSAGE_NOTIFICATION_TYPE+"') ";
		    	} else {
		    		delete_notifications="DELETE FROM P_NOTIFICATIONS where HOST_ID=? and NOTIFICATION_TYPE in('"+SwtConstants.INTERFACE_NOTIFICATION_TYPE+"','"+SwtConstants.MESSAGE_NOTIFICATION_TYPE+"') ";
		    	}
		    	//declare statement for delete
		    	pst=conn.prepareStatement(delete_notifications);
		    	pst.setString(1, notification.getHostId());
		    }else {
		    	if (fromPCM) {
		    		delete_notifications="DELETE FROM PC_NOTIFICATIONS where HOST_ID=? and RELATION_ID=? and NOTIFICATION_TYPE=? ";
		    	} else {
		    		delete_notifications="DELETE FROM P_NOTIFICATIONS where HOST_ID=? and RELATION_ID=? and NOTIFICATION_TYPE=? ";
		    	}
		    	//declare statement for delete
		    	pst=conn.prepareStatement(delete_notifications);
		    	pst.setString(1, notification.getHostId());
		    	pst.setString(2, notification.getRelationId());
		    	pst.setString(3, notification.getNotificationType());
		    }
		    //execute delete query
		    pst.executeUpdate();
		  //committed the transaction
		    conn.commit();
		    result = true;
			log.debug(this.getClass().getName() + "- [ deleteNotifications] - Exit ");
		}catch(Exception e){
			result = false;
			log.debug(this.getClass().getName() + " - [deleteNotifications] - Exception - "+ e.getMessage());
			log.error(this.getClass().getName() + " - [deleteNotifications] - Exception - "+ e.getMessage());
			e.printStackTrace();
		}finally{
			
			//edited by KaisBS for mantis 1745 : introducing the implementation of the JDBCCloser class
			
			try{
				JDBCCloser.close(null, pst, conn, session);
			}catch(Exception e){
				logger.error("org.swallow.control.dao.hibernate.InterfaceInterruptDAOHibernate - " +
						"[deleteNotifications] - Exception - " + e.getMessage());
			}
		}

		return result;
	}


	/**
	 * Update the message of notification which already exists
	 * @param Notifications notification
	 * @return boolean
	 */
	public boolean updateNotifications(Notifications notification, boolean fromPCM){

		boolean result = false;
		//Notifications existingNotification = null;
		Connection conn=null;
		PreparedStatement pst=null;
		//For holding update query
		String update_notifications;
		Session session = null;
		try{
			log.debug(this.getClass().getName() + "- [updateNotifications] - Entry ");

			//get the connection using ConnectionManager instance
			if (fromPCM) {
				session = SwtUtil.pcSessionFactory.openSession();
		    	conn = SwtUtil.connection(session);
			} else {
				conn=ConnectionManager.getInstance().databaseCon();
			}
			//update query
			if (fromPCM) {
				update_notifications="update PC_NOTIFICATIONS set NOTIFICATION_MESSAGE=? where NOTIFICATION_ID=? ";
			} else {
				update_notifications="update P_NOTIFICATIONS set NOTIFICATION_MESSAGE=? where NOTIFICATION_ID=? ";
			}
			// create statement for execute query
			pst=conn.prepareStatement(update_notifications);
			pst.setString(1, notification.getNotificationMessage());
			pst.setInt(2, notification.getNotificationId());
			pst.executeUpdate();
			conn.commit();
			result = true;
			log.debug(this.getClass().getName() + " - [updateNotifications] - Exit ");
		}catch(Exception e){
			log.debug(this.getClass().getName() + " - [updateNotifications] - Exception - "+ e.getMessage());
			log.error(this.getClass().getName() + " - [updateNotifications] - Exception - "+ e.getMessage());
			result = false;
			e.printStackTrace();
		}finally{
			
			//edited by KaisBS for mantis 1745 : introducing the implementation of the JDBCCloser class
			try{
				JDBCCloser.close(null, pst, conn, session);
			}catch(Exception e){
				
				logger.error("org.swallow.control.dao.hibernate.InterfaceInterruptDAOHibernate - " +
						"[updateNotifications] - Exception - " + e.getMessage());
				
			}
		}
		return result;
	}


	/**
	 * Get the threshold time for interface name passed
	 * @param String interfaceName
	 * @return Integer
	 */
	public Integer getThresholdTime(String interfaceName, boolean fromPCM){

		//Collection to get the threshold times
		Collection<Integer> thresholds = null;
		//Iterator for iterating through query result
		Iterator itr = null;
		//String variable to hold the threshold
		String threshold = null;

		try{
			log.debug(this.getClass().getName() + "- [getThresholdTime] - Entry ");

			//Get hibernate template and get the threshold
			if (fromPCM) {
				thresholds = (Collection<Integer> ) getHibernateTemplate().find("SELECT P.threshold from PCInterfaceInterruption P where P.id.messageType = ?0",
								new Object[]{interfaceName});
			} else {
				thresholds = (Collection<Integer> ) getHibernateTemplate().find("SELECT P.threshold from InterfaceInterruption P where P.id.messageType = ?0",
								new Object[]{interfaceName});
			}
			if(thresholds.size()>0 && (thresholds!=null) ){
				itr = thresholds.iterator();
				if(itr.hasNext()){
					//Get the threshold
					threshold = (String)itr.next();
				}
				/* If the retrieved value is null,throw the null pointer exception */
				if(SwtUtil.isEmptyOrNull(threshold)){
				 log.debug(this.getClass().getName() + " - [getThresholdTime] - " +
					 "No value has been entered for Threshold in Interface Monitor Screen");
					return -1;
				}

			}
			else{
			 log.debug(this.getClass().getName() + " - [getThresholdTime] - " +
				 "No value has been entered for Threshold in Interface Monitor Screen");
				return -1;
			}
			log.debug(this.getClass().getName() + " - [getThresholdTime] - Exit ");
		}catch(Exception e){
			log.debug(this.getClass().getName() + " - [getThresholdTime] - Exception - "+ e.getMessage());
			log.error(this.getClass().getName() + " - [getThresholdTime] - Exception - "+ e.getMessage());
			e.printStackTrace();
		}finally{

		}
		//returns the threshold as Integer
		return Integer.parseInt(threshold);
	}


	/**
	 * Method to get the date which is passed as String
	 * @param String time
	 * @return Date
	 */
	private Date getTime(String time){

		//DateFormat object
		DateFormat format = null;
		//Date object which holds date
		Date date = null;
		//Calendar object to calculate the time
		Calendar calendar = null;

		try{
			log.debug(this.getClass().getName() + " - [getTime] - Entry ");
			//Get instance of calendar
			calendar = Calendar.getInstance();
			//Taking the date as today
			//Set the current date
			calendar.setTime(new Date());
			//Set the hour which is retrieved from the database
			calendar.set(Calendar.HOUR, Integer.parseInt(time.substring(0,2)));
			//Set the minute which is retrieved from the database
			calendar.set(Calendar.MINUTE, Integer.parseInt(time.substring(3,5)));
			//To set the time in 24 hours format
			calendar.set(Calendar.AM_PM, (Integer
					.parseInt(time.substring(0, 2)) <= 24) ? Calendar.AM
					: Calendar.PM);
			//Get the date
			date = calendar.getTime();
			log.debug(this.getClass().getName() + " - [getTime] - Exit ");
		}catch(Exception e){
			log.debug(this.getClass().getName() + " - [getTime] - Exception - "+ e.getMessage());
			log.error(this.getClass().getName() + " - [getTime] - Exception - "+ e.getMessage());
			e.printStackTrace();
		}finally{
			format = null;
		}
		return date;
	}

	/**
	 * Method to fetch notifications details
	 * @param Notifications notifications
	 * @return List
	 */
public List fetchNotifications(Notifications notifications, boolean fromPCM){

		//Collection to hold the notifications for host id, entity id and notification id combination
		List fetchedNotifications_dtl = null;
		//Get the notification object
		Notifications fetchedNotification = null;
		//Iterator object to iterate through notification list
		Iterator itr = null;
		String query = null;
		try{
			log.debug(this.getClass().getName() + " - [fetchNotification] - Entry ");


			//Fetch the notification with host id, entity id and notification id combination
			if (fromPCM) {
				query = "from PCNotifications n where n.hostId = ?0 and n.relationId = ?1 and n.notificationType = ?2";
			} else {
				query = "from Notifications n where n.hostId = ?0 and n.relationId = ?1 and n.notificationType = ?2";
			}
			fetchedNotifications_dtl = (List ) getHibernateTemplate().find(query,
											new Object[]{notifications.getHostId(), notifications.getRelationId(),notifications.getNotificationType()});/**/

			//Iterator to iterate through fetched notification
			log.debug(this.getClass().getName() + " - [fetchNotification] - Exit ");



		}catch(Exception e){
			log.debug(this.getClass().getName() + " - [fetchNotification] - Exception - "+ e.getMessage());
			log.error(this.getClass().getName() + " - [fetchNotification] - Exception - "+ e.getMessage());
			e.printStackTrace();
		}finally{
			fetchedNotification=null;
		}
		return fetchedNotifications_dtl;
	}


/**
 * Purpose of this method is to delete all the details
 * of notifications when error caused by unable to connect
 * the service from table P_Notifications as well as delete
 * the error detail when connection is established
 * @param Notifications notification
 * @return boolean
 */
public boolean deleteNotification(Notifications notification, boolean fromPCM){
	log.debug(this.getClass().getName() + "- [deleteNotification] - Entry ");
	//Boolean variable to hold deletion status
	boolean result = false;
	//declare a connection object
	Connection conn=null;
	//statement object is initialized
	Statement st=null;
	//String variable for holding delete query
	String delete_notifications="";
	/*Local variable declaration */
	Collection  notification_Alert=null;
	Iterator itr=null;
	String relational_id="";
	Session session = null;
	
	try{

		//call connection manager object for establish the connection
	    if (fromPCM) {
	    	session = SwtUtil.pcSessionFactory.openSession();
	    	conn = SwtUtil.connection(session);
		} else {
			conn = ConnectionManager.getInstance().databaseCon();
			session = getHibernateTemplate().getSessionFactory().openSession();
		}
		    //declare statement for deleting
		    st=conn.createStatement();

			 /* check the relational id is equal to 'Alert',
			  * the condition is satisfied,delete error details
			 */
		    if(notification.getRelationId().equals(SwtConstants.INTERFACE_NOTIFICATION_ERRTYPE)){
		    	/*select relational id from P_Notifications table */
		    	if (fromPCM) {
		    		notification_Alert = session.createQuery("SELECT n.relationId from PCNotifications n where n.relationId = ?0")
		    				.setParameters(new Object[]{SwtConstants.INTERFACE_NOTIFICATION_ERRTYPE},
							new Type[] { StringType.INSTANCE }).getResultList();
				} else {
					notification_Alert = session.createQuery("SELECT n.relationId from Notifications n where n.relationId = ?0")
							.setParameters(	new Object[]{SwtConstants.INTERFACE_NOTIFICATION_ERRTYPE},
							new Type[] { StringType.INSTANCE }).getResultList();
				}
				itr=notification_Alert.iterator();
				  if(itr.hasNext()){
					  relational_id=(String)itr.next();

				  }
				 /* Check relational id with error type 'Alert',it fails
				  * delete the notifications details */
				if(relational_id.equals(SwtConstants.INTERFACE_NOTIFICATION_ERRTYPE)){
					  return false;
				}else{
					if (fromPCM) {
						delete_notifications="DELETE FROM PC_NOTIFICATIONS ";
					} else {
						delete_notifications="DELETE FROM P_NOTIFICATIONS ";
					}
					 st.executeUpdate(delete_notifications);
					 conn.commit();
					return false;
				}
			}
		     /* check the relational id is equal to 'Interface',
			  * the condition is satisfied,delete error details
			  */
		    if(notification.getRelationId().equals(SwtConstants.INTERFACE_NOTIFICATION_TYPE)){
		        	/*select relational id from P_Notifications table */
			    	if (fromPCM) {
						notification_Alert = session.createQuery("SELECT n.relationId from PCNotifications n where n.relationId = ?0")
								.setParameters(new Object[]{SwtConstants.INTERFACE_NOTIFICATION_ERRTYPE},
								new Type[] { StringType.INSTANCE }).getResultList();
					} else {
						notification_Alert = session.createQuery("SELECT n.relationId from Notifications n where n.relationId = ?0")
								.setParameters(new Object[]{ SwtConstants.INTERFACE_NOTIFICATION_ERRTYPE},
								new Type[] { StringType.INSTANCE }).getResultList();
					}
		    		itr=notification_Alert.iterator();

				  if(itr.hasNext()){
					  relational_id=(String)itr.next();
				  }
				  /* Check relational id with error type 'Alert',it passes
				   * delete the notifications details */
				  if(relational_id.equals(SwtConstants.INTERFACE_NOTIFICATION_ERRTYPE)){
					  if (fromPCM) {
						  delete_notifications="DELETE FROM PC_NOTIFICATIONS ";
					  } else {
						  delete_notifications="DELETE FROM P_NOTIFICATIONS ";
					  }
					  st.executeUpdate(delete_notifications);
					  conn.commit();
					  return false;
				 }
		}

		result = true;
		log.debug(this.getClass().getName() + "- [ deleteNotification] - Exit ");
	}catch(Exception e){
		result = false;
		log.debug(this.getClass().getName() + " - [deleteNotification] - Exception - "+ e.getMessage());
		log.error(this.getClass().getName() + " - [deleteNotification] - Exception - "+ e.getMessage());
		e.printStackTrace();
	}finally{
		
		
		try{
			//edited by KaisBS for mantis 1745 : introducing the implementation of the JDBCCloser class
			JDBCCloser.close(null, st, conn, session);
		}catch(Exception e){
			logger.error("org.swallow.control.dao.hibernate.InterfaceInterruptDAOHibernate - " +
					"[deleteNotification] - Exception - " + e.getMessage());
		}
	}
	return result;
}


/**
 * This method is used to get the job_status value of notification
 * job from S_Job_status and call the method to delete the notification
 * if job_status is 'D'
 * @param programName
 * @return boolean
 */
public boolean notificationJobStatus(String programName, boolean fromPCM){
   log.debug(this.getClass().getName() + "- [notification_Job_Status] - Entry ");

	 Connection conn = null;
	 Statement stmt = null;
     ResultSet res = null;
     String currentStatus;

    try {
    	/* Establish the connection using connection manager */
		conn = ConnectionManager.getInstance().databaseCon();
		 stmt = conn.createStatement();
		 /* Query for selecting job_status from S_Job_Status */
		  res = stmt.executeQuery(
                 "Select j.CURRENT_STATUS from S_JOB_STATUS j,S_JOB s where s.JOB_ID = j.JOB_ID and s.PROGRAM_NAME='" +
                 programName + "'");
		/* If record set has value,getting status */
		  if(res.next()){
			  currentStatus=res.getString(1);
			  /* If status id is 'D',call to delete notifications */
			  if(currentStatus.equalsIgnoreCase("D")){
				 deleteNotificationDisable(SwtConstants.INTERFACE_NOTIFICATION_TYPE, fromPCM);
				 deleteNotificationDisable(SwtConstants.MESSAGE_NOTIFICATION_TYPE, fromPCM);
				 return true;
			  }
		  }else{
			  return false;
		  }
		  log.debug(this.getClass().getName() + "- [notificationJobStatus] - Exit ");

	} catch(Exception e){
		log.debug(this.getClass().getName() + " - [notificationJobStatus] - Exception - "+ e.getMessage());
		log.error(this.getClass().getName() + " - [notificationJobStatus] - Exception - "+ e.getMessage());
		e.printStackTrace();
	}finally{
		
		//edited by KaisBS for mantis 1745 : introducing the implementation of the JDBCCloser class
		JDBCCloser.close(res, stmt, null, null);
		
		try{
			// Start: Code modified by Kalidass G to check the not null condition on 26-Jan-10 for the Mantis 1114
			if((conn != null) && (!conn.isClosed()))
				ConnectionManager.getInstance().retrunConnection(conn);
			// End: Code modified by Kalidass G to check the not null condition on 26-Jan-10 for the Mantis 1114
		}catch(Exception e){
			logger.error("org.swallow.control.dao.hibernate.InterfaceInterruptDAOHibernate -" +
					" [notificationJobStatus] - Exception - " + e.getMessage());
		}
	}
	return false;


}
/**
 * This method is used to delete all the details from
 * P_Notifications table based on the notification type passed.
 * @param notificationType
 * @return boolean
 */
public boolean deleteNotificationDisable(String notificationType, boolean fromPCM){
	//Boolean variable to hold deletion status
	boolean result = false;
	// This list is to store values of notification
	List dataNotification=null;
	//declare a connection object
	Connection conn=null;
	//statement object is initialized
	PreparedStatement pst=null;
	//String variable for holding delete query
	String deletenotifications="";
	Session session = null;

	try{
		log.debug(this.getClass().getName() + "- [deleteNotificationDisable] - Entry ");
		//  call connection manager object for establish the connection
		if (fromPCM) {
			session = SwtUtil.pcSessionFactory.openSession(); 
	    	conn = SwtUtil.connection(session);
		} else {
			conn = ConnectionManager.getInstance().databaseCon();
		}
	    log.debug("kotak deleteNotification_Disable notification_Type " + notificationType);
	    //delete query	under checking HOST_ID, ENTITY_ID,  RELATION_ID,NOTIFICATION_TYPE
	    if (fromPCM) {
	    	deletenotifications="DELETE FROM PC_NOTIFICATIONS  where  NOTIFICATION_TYPE=? ";
		} else {
			deletenotifications="DELETE FROM P_NOTIFICATIONS  where  NOTIFICATION_TYPE=? ";
		}
	    //declare statement for delete
	    pst=conn.prepareStatement(deletenotifications);
	    pst.setString(1, notificationType);
	    //execute delete query
	    pst.executeUpdate();
	    //committed the transaction
	    conn.commit();
	    result = true;
		log.debug(this.getClass().getName() + "- [deleteNotificationDisable] - Exit ");
	}catch(Exception e){
		result = false;
		log.debug(this.getClass().getName() + " - [deleteNotificationDisable] - Exception - "+ e.getMessage());
		log.error(this.getClass().getName() + " - [deleteNotificationDisable] - Exception - "+ e.getMessage());
		e.printStackTrace();
	}finally{
		
		//edited by KaisBS for mantis 1745 : introducing the implementation of the JDBCCloser class
		JDBCCloser.close(null,pst,null, session);
		try{
			if((conn != null) && (!conn.isClosed())) 
				ConnectionManager.getInstance().retrunConnection(conn);
		}catch(Exception e){
			logger.error("org.swallow.control.dao.hibernate.InterfaceInterruptDAOHibernate - " +
					"[deleteNotificationDisable] - Exception - " + e.getMessage());
		}
	}

	return result;
}

public void enableSendingMails(boolean fromPCM) {
	//declare a connection object
	Connection conn=null;
	//statement object is initialized
	PreparedStatement prst=null;
	String query = null;
	Session session = null;
	
	try { 
		log.debug(this.getClass().getName() + "- [enableSendingMails] - Entry ");
		if (fromPCM) {
			session = SwtUtil.pcSessionFactory.openSession();
			conn = SwtUtil.connection(session);
		} else {
			conn = ConnectionManager.getInstance().databaseCon();
		}
	
		  if (fromPCM) {
			  query = "UPDATE PC_NOTIFICATIONS SET EMAIL_FLAG = 'N' ";  
		  } else {
			  query = "UPDATE P_NOTIFICATIONS SET EMAIL_FLAG = 'N' ";  
		  }
		  prst = conn.prepareStatement(query);
		  prst.execute();
		  conn.commit();
	} catch (Exception e) {
		log.error(this.getClass().getName() + " - [enableSendingMails] - Exception - "+ e.getMessage());

	} finally{
		// close statement and cnx.
		Object[] exceptions = JDBCCloser.close(null, prst, conn, session);
	}

	
}

}