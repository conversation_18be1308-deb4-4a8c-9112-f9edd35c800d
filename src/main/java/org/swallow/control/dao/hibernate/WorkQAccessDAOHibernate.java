/*
 * Created on Dec 26, 2005
 *
 * TODO To change the template for this generated file go to
 * Window - Preferences - Java - Code Style - Code Templates
 */
package org.swallow.control.dao.hibernate;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

import org.swallow.exception.SwtException;
import org.swallow.control.dao.*;


import java.util.ArrayList;
import java.util.Collection;
import org.springframework.context.annotation.Lazy;
import org.springframework.orm.hibernate5.support.HibernateDaoSupport;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.stereotype.Repository;
import org.hibernate.SessionFactory;

/**
 * <AUTHOR>
 *
 * TODO To change the template for this generated type comment go to
 * Window - Preferences - Java - Code Style - Code Templates
 */
@Repository ("workQAccessDAO")
@Transactional
public class WorkQAccessDAOHibernate extends HibernateDaoSupport implements WorkQAccessDAO{
	public WorkQAccessDAOHibernate(@Lazy SessionFactory sessionfactory) {
	    setSessionFactory(sessionfactory);
	}

	private final Log log = LogFactory.getLog(WorkQAccessDAOHibernate.class);

		
		public Collection getCurrencyList(String entityId, String hostId)  throws SwtException{
			log.debug("Entering getCurrencyList: ");
			java.util.List list = getHibernateTemplate().find("from Currency c where c.id.entityId =?0 and c.id.hostId=?1",
			        new Object[]{entityId, hostId});
			
			log.debug("noofRecords.size : " + list.size());
			return list;		
		}
		
		public Collection getWorkQAccessDetails(String entityId, String hostId,String roleId) throws SwtException{
			log.debug("Entering getWorkQAccessDetails Method");
			java.util.List list = new ArrayList();
			if(roleId.equalsIgnoreCase("All")){
				list = new ArrayList();//getHibernateTemplate().find("from WorkQAccess w where w.id.entityId =? and w.id.hostId=? ",
				        //new Object[]{entityId, hostId});	
			}
			
			list = new ArrayList();//getHibernateTemplate().find("from WorkQAccess w where w.id.entityId =? and w.id.hostId=? and w.id.roleId=?",
			        //new Object[]{entityId, hostId,roleId});
			
			
			//log.debug("noofRecords.size : " + list.size());
			return list;
		}
		
		public Collection  getDetailsByRoleId(String hostId,String roleId)throws SwtException{
			log.debug("Entering getDetailsByRoleId Method");
			
			java.util.List list = new ArrayList();//getHibernateTemplate().find("from WorkQAccess w where w.id.hostId =? and w.id.roleId=?",
			        //new Object[]{hostId, roleId});
			
			//log.debug("noofRecords.size : " + list.size());
			return list;
		}
		
	}
	
	
	
	
