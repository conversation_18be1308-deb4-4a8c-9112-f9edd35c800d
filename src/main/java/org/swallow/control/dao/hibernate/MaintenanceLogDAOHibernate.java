/*
 * @(#)MaintenanceLogDAOHibernate.java 1.0 23/08/06
 * 
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 * 
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.control.dao.hibernate;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.StringTokenizer;


import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.hibernate.HibernateException;
import org.hibernate.Query;
import org.hibernate.Session;
import org.hibernate.SessionFactory;
import org.hibernate.Transaction;
import org.springframework.context.annotation.Lazy;
import org.springframework.orm.hibernate5.support.HibernateDaoSupport;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Repository;
import org.swallow.batchScheduler.ConnectionManager;
import org.swallow.control.dao.MaintenanceLogDAO;
import org.swallow.control.model.MaintenanceLog;
import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.util.JDBCCloser;
import org.swallow.util.PropertiesFileLoader;
import org.swallow.util.SwtConstants;
import org.swallow.util.SwtPager;
import org.swallow.util.SwtUtil;
import org.swallow.util.SystemFormats;

/**
 * This class function as DAO layer for MAnityenace Log screen. Used to get all
 * the Maintenace log details form the tabel S_MAINTENACE_LOG tabel
 * 
 */
@Component ("maintenanceLogDAO")
@Repository
public class MaintenanceLogDAOHibernate extends HibernateDaoSupport implements
		MaintenanceLogDAO {
	
	public MaintenanceLogDAOHibernate(@Lazy SessionFactory sessionfactory){
	    setSessionFactory(sessionfactory);
	}
	
	
	/**
	 * Final log instance for logging this class
	 */
	private final Log log = LogFactory.getLog(MaintenanceLogDAOHibernate.class);

	/**
	 * This method gets the updated action string from maintenanceLog like
	 * add,delete or change then return a specific one character String
	 * according to that action string
	 * 
	 * @param maintenanceLog
	 */
	public void logMaintenanceAudit(MaintenanceLog maintenanceLog)
			throws SwtException {

		log.debug(this.getClass().getName() + " - [logMaintenanceAudit] - "
				+ "Entry");
		/* local variable declaration */
		String action = "";

		/* get action ie,add or delete or change from maitenance class */
		action = maintenanceLog.getAction();

		/* if action value is ot null ,checking either deleted or added etc. */
		if (action != null) {
			if (action.equalsIgnoreCase("Deleted")) {

				action = "d";

			} else if ((action.equalsIgnoreCase("Changed"))
					|| (action.equalsIgnoreCase("u"))) {

				action = "U";

			} else if (action.equalsIgnoreCase("Added")) {

				action = "a";
			}
			/* otherwise the action value is set to null */
		} else {

			action = "";
		}
		/* The updated action value is set */
		maintenanceLog.setAction(action);
		/* finally it is stored into Database */
		Session sesion = null;
		try {
			sesion = getHibernateTemplate().getSessionFactory().openSession();
			// Holds the hibernate Transaction instance
			Transaction tx = null;
			// Updates the details of the inputInterface
			tx = sesion.beginTransaction();
			sesion.save(maintenanceLog);
			// Commits the transaction to the database
			tx.commit();
		} catch (HibernateException he) {

			log
					.error(this.getClass().getName()
							+ " - HibernateException Catched in [logMaintenanceAudit] method - "
							+ he.getMessage());
			throw SwtErrorHandler.getInstance().handleException(he,
					"logMaintenanceAudit", MaintenanceLogDAOHibernate.class);
		}finally{
			
			//edited by KaisBS for mantis 1745 : introducing the implementation of the JDBCCloser class
			JDBCCloser.close(sesion);
			
		}

		log.debug(this.getClass().getName() + " - [logMaintenanceAudit] - "
				+ "Exit");
	}

	/**
	 * This method does retrive log details which are logged by particular
	 * system ip
	 * 
	 * @param hostId
	 * @param logDate
	 * @param userId
	 * @param ipAddress
	 * @param tableName
	 * @param reference
	 * @param action
	 * @return Collection
	 * @throws SwtException
	 */
	public Collection getSystemLogDetails(String hostId, String logDate,
			String userId, String ipAddress, String tableName,
			String reference, String action) throws SwtException {

		log.debug(this.getClass().getName() + " - [getSystemLogDetails] - "
				+ "Entry");
		List records = null;
		/* log date is formatted */
		//logDate = logDate.substring(0, logDate.length());

		/* Hibernate method to retrieve the details of systemlog */
		records = getHibernateTemplate()
				.find(
						" from MaintenanceLog log1  "
								+ "where log1.hostId = ?0 and to_date(to_char(log1.logDate,'YYYY-MM-DD HH24:MI:SS'),'YYYY-MM-DD HH24:MI:SS') = to_date(?1" +
								",'YYYY-MM-DD HH24:MI:SS') and log1.userId = ?2 "
								+ "and log1.ipAddress = ?3 and log1.tableName = ?4 and log1.reference = ?5 and log1.action = ?6",
						new Object[] { hostId, logDate, userId, ipAddress, tableName,
								reference, action });

		log.debug(this.getClass().getName() + " - [getSystemLogDetails] - "
				+ "Exit");

		return records;

	}

	/**
	 * This method fetches the System Log details based on the Page Clicked. For
	 * the very first first time data for the first page is fetched.
	 * 
	 * @param hostId
	 * @param fromDate
	 * @param toDate
	 * @return int
	 * @throws SwtException
	 */
	public int getMaintenanceLogList(String hostId, Date fromDate, Date toDate,
			int currentPage, int maxPage, List maintenanceList,
			String filterSortStatus, SystemFormats formats) throws SwtException {
		log.debug(this.getClass().getName() + " - [getMaintenanceLogList] - "
				+ "Entry");

		StringBuffer mainLogHQL = null;
		StringBuffer countmainLogHQL = null;
		StringBuffer QueryfilterSortMerged = null;
		StringBuffer QueryCountfilterSortMerged = null;
		Query querySysLog = null;
		List sysLogCountList = null;
		int totalCount = 0;

		/*
		 * These all string buffers are used to append the query string to
		 * select from maitenance view
		 */
		mainLogHQL = new StringBuffer();
		countmainLogHQL = new StringBuffer();
		Query querycountSysLog = null;
		mainLogHQL
				.append("from MaintenanceLogView m where TRUNC(m.id.logDate) between ?0 and ?1 and m.id.ipAddress is not null");
		countmainLogHQL.append("select count(*) ");
		countmainLogHQL.append(mainLogHQL);
		Session session = null;
		try {
			/*
			 * Instead of using hibernate template here, using session factory
			 * to execute the query
			 */
			SessionFactory sessionFactory = (SessionFactory) SwtUtil
					.getBean("sessionFactory");
			session = sessionFactory.openSession();
			/* merge all query strings into this buffer */
			QueryCountfilterSortMerged = formQuery(countmainLogHQL,
					filterSortStatus, formats);
			/* session for create query then stored the result in query object */

			querycountSysLog = session.createQuery(QueryCountfilterSortMerged
					.toString());

			querycountSysLog.setDate(0, fromDate);
			querycountSysLog.setDate(1, toDate);

			/*
			 * this Block reads PageSize from the property file and calculates
			 * the maxPages
			 */
			int pageSize=SwtUtil.getPageSizeFromProperty(SwtConstants.SYSTEM_LOG_SCREEN_PAGE_SIZE);

			sysLogCountList = querycountSysLog.list();

			totalCount = (sysLogCountList != null && sysLogCountList.size() > 0 && sysLogCountList
					.get(0) != null) ? Integer.parseInt(sysLogCountList.get(0)
					.toString()) : 0;

			// maxPage is initially set to 0 when called for the first time ,
			// yet to calculate maxPage
			if (maxPage == 0) {
				int size = totalCount;

				maxPage = (size % pageSize > 0) ? size / pageSize + 1 : size
						/ pageSize;
			}

			/*
			 * formQuery is called to form the correct HQL based on all the
			 * filter and sorting conditions
			 */
			QueryfilterSortMerged = formQuery(mainLogHQL, filterSortStatus,
					formats);
			querySysLog = session.createQuery(QueryfilterSortMerged.toString());
			querySysLog.setDate(0, fromDate);
			querySysLog.setDate(1, toDate);
			// fetches the data for the currentPage
			SwtPager.next(querySysLog, currentPage, maxPage, pageSize,
					maintenanceList);

			log.debug(this.getClass().getName()
					+ " - [getMaintenanceLogList] - " + "Exit");
		} catch (Exception e) {

			log
					.error(this.getClass().getName()
							+ " - Exception Catched in [getMaintenanceLogList] method : - "
							+ e.getMessage());

		}finally{
			
			//edited by KaisBS for mantis 1745 : introducing the implementation of the JDBCCloser class
			JDBCCloser.close(session);
			
		}

		return totalCount;
	}

	/**
	 * This method used to frame the query to get the maintenance log details
	 * from the database based on the filter and sort criteria
	 * 
	 * @param query
	 * @param filterSortStatus
	 * @param formats
	 * @return StringBuffer
	 * @throws SwtException
	 */
	private StringBuffer formQuery(StringBuffer query, String filterSortStatus,
			SystemFormats formats) throws SwtException {
		// variable declaration is to get the filter & sort values
		String[] filterSort = null;
		// Variable declaration to get the sort values
		String[] sortValues = null;
		// variable declaration to get the filter values
		String[] filterValues = null;
		// Integer Array declaration for filter sort value operation
		int[] nonString = null;
		// string Tokenizer declaration to separate the sort values
		StringTokenizer stringTokenizer = null;
		// stringTokenizerFilter declaration to separate the filter values
		StringTokenizer stringTokenizerFilter = null;
		// Variable declaration store the filter status
		String filterStatus = null;
		// Variable declaration store the sort status
		String sortStatus = null;
		// Variable declaration to get the value of sorted column
		String sortColumn = null;
		// Variable declaration to get the type of sorting
		String sortDesc = null;
		// Variable declaration to get the date format
		String format = null;
		// variable declaration to find the availability of next token
		String nextVal = null;
		// integer declaration for manipulating the filter sort values
		int filterSortValue1;
		int filterSortValue2;

		try {
			log
					.debug(this.getClass().getName() + " - [formQuery] - "
							+ "Entry");
			/*
			 * Extracting sort status and filter status from input
			 * filterSortStatus which are merged in manager layer
			 */
			filterSort = filterSortStatus.split(",");
			filterStatus = filterSort[0].toString();
			sortStatus = filterSort[1].toString();

			// Extracting sorting values from sortStatus
			sortValues = new String[3];
			sortValues = sortStatus.split("|");
			stringTokenizer = new StringTokenizer(sortStatus, "|");
			/*
			 * split the datevalues using string tokenizer then format the value
			 * as DD/MM/YY
			 */
			format = (formats.getDateFormatValue()
					.equalsIgnoreCase("MM/dd/yyyy")) ? "M" : "D";

			filterSortValue1 = 0;
			/*
			 * conditional loop for fetching values of sort status from
			 * tokenizer
			 */
			while (stringTokenizer.hasMoreTokens()) {
				sortValues[filterSortValue1] = stringTokenizer.nextToken();
				filterSortValue1++;
			}
			/*
			 * get sort values then stored separately as sort column and sort
			 * desc
			 */
			sortColumn = sortValues[0];

			sortDesc = sortValues[1];
			// initialize filter values to get filter values of all columns
			filterValues = new String[7];
			nonString = new int[7];
			filterSortValue2 = -1;
			/* check filter status is all and not equal to undefined */
			if (!(filterStatus.equals("all"))
					&& !(filterStatus.equals("undefined"))) {
				// separate the filter values of the columns
				stringTokenizerFilter = new java.util.StringTokenizer(
						filterStatus, "|");
				filterSortValue1 = 0;
				// checking for next token
				while (stringTokenizerFilter.hasMoreTokens()) {

					nextVal = stringTokenizerFilter.nextToken();

					if (nextVal.equals("(Empty)")) {
						filterValues[filterSortValue1] = "is null";
						nonString[filterSortValue1] = 1;
					} else if (nextVal.equals("(Not empty)")) {
						filterValues[filterSortValue1] = "is not null";
						nonString[filterSortValue1] = 1;
					} else {
						// Code modified by Sudhakar on 17-Feb-2012 for mantis
						// 1561:For Currency Access Issue
						filterValues[filterSortValue1] = nextVal.trim();
						filterSortValue1++;
					}
				}

				/*
				 * get the filter values from array of string then , it is
				 * checked one by one with "All"
				 */

				if (!filterValues[0].toString().equals("All")) {
					filterSortValue2++;
					// appending query based on the date format.while applying
					// filter for column
					if ("D".equals(format)) {
						query
								.append(
										" and to_date(to_char(m.id.logDate,'DD-MM-YYYY'),'DD-MM-YYYY') = to_date('")
								.append(filterValues[0]).append(
										"','DD-MM-YYYY')");
					} else if ("M".equals(format)) {
						query
								.append(
										" and to_date(to_char(m.id.logDate,'MM-DD-YYYY'),'MM-DD-YYYY') = to_date('")
								.append(filterValues[0]).append(
										"','MM-DD-YYYY')");
					}

				}
				// append query based on column filter values if it is not "All"
				if (!filterValues[1].toString().equals("All")) {
					filterSortValue2++;
					query
							.append(
									" and ((to_date(to_char(m.id.logDate,'HH24:MI:SS'),'HH24:MI:SS') = to_date('")
							.append(filterValues[1])
							.append("','HH24:MI:SS')))");

				}
				// append query based on column filter values if it is not "All"

				if (!filterValues[2].toString().equals("All")) {
					filterSortValue2++;
					query.append(" and m.id.userId='").append(filterValues[2])
							.append("'");
				}
				// append query based on column filter values if it is not "All"
				if (!filterValues[3].toString().equals("All")) {

					filterSortValue2++;
					// if IpAddress Filter value is (Empty) and (Not empty)
					if (nonString[3] == 1) {
						query.append(" and m.id.ipAddress ").append(
								filterValues[3]);
					} else {
						query.append(" and m.id.ipAddress='").append(
								filterValues[3]).append("'");
					}
				}
				// append query based on column filter values if it is not "All"
				if (!filterValues[4].toString().equals("All")) {
					filterSortValue2++;
					query.append(" and m.id.tableName='").append(
							filterValues[4]).append("'");

				}
				// append query based on column filter values if it is not "All"
				if (!filterValues[5].toString().equals("All")) {
					filterSortValue2++;
					query.append(" and m.id.reference='").append(
							filterValues[5]).append("'");

				}
				// append query based on column filter values if it is not "All"
				if (!filterValues[6].toString().equals("All")) {
					filterSortValue2++;

					if (filterValues[6].equalsIgnoreCase("Added")) {
						filterValues[6] = "I";
					}
					if (filterValues[6].equalsIgnoreCase("Deleted")) {
						filterValues[6] = SwtConstants.ACTION_DELETE;
					}
					if (filterValues[6].equalsIgnoreCase("Changed")) {
						filterValues[6] = SwtConstants.ACTION_UPDATE;
					}
					// if Action Filter value is (Empty) and (Not empty)
					if (nonString[6] == 1) {
						query.append(" and m.id.action ").append(
								filterValues[6]);
					} else {
						query.append(" and m.id.action='").append(
								filterValues[6]).append("'");
					}

				}
			}
			/*
			 * check the sort status value ,none or not it has value ,then data
			 * will be sorted using order by
			 */
			if (!sortStatus.equals("none")) {
				query.append(" order by ");

				if (sortColumn.equals("2")) {
					query.append(" m.id.userId");
				} else if (sortColumn.equals("3")) {
					query.append(" m.id.ipAddress");
				} else if (sortColumn.equals("4")) {
					query.append(" m.id.tableName");
				} else if (sortColumn.equals("5")) {
					query.append(" m.id.reference");
				} else if (sortColumn.equals("6")) {
					query.append(" m.id.action");
				}
				if (sortDesc.equalsIgnoreCase("true")) {
					if (sortColumn.equals("0") || sortColumn.equals("1")) {
						query.append(" m.id.sequence desc");
					} else {

						query.append(" desc ,m.id.sequence desc");

					}

				} else {

					if (sortColumn.equals("0") || sortColumn.equals("1")) {
						query.append(" m.id.sequence asc");
					} else {
						query.append(" asc ,m.id.sequence asc");
					}
				}

			} else {
				query.append(" order by ");

				query.append("m.id.sequence desc");
			}

		} catch (Exception e) {

			log.error(this.getClass().getName()
					+ " - Exception Catched in [formQuery] method : - "
					+ e.getMessage());
			throw SwtErrorHandler.getInstance().handleException(e, "formQuery",
					this.getClass());

		} finally {
			filterSort = null;
			sortValues = null;
			filterValues = null;
			nonString = null;
			stringTokenizer = null;
			stringTokenizerFilter = null;
			filterStatus = null;
			sortStatus = null;
			sortColumn = null;
			sortDesc = null;
			format = null;
			nextVal = null;
			log.debug(this.getClass().getName() + " - [formQuery] - " + "Exit");

		}
		return query;
	}
	
	/**
	 * This method is used to get  user name from s_users table
	 */
	public String getUserName(String userId) throws SwtException {
		log.debug(this.getClass().getName() + " - [getUserName] - "
				+ "Entry");
		/* Method's local variable declaration */
		Connection conn = null;
		Session session = null;
		PreparedStatement stmt = null;
		ResultSet rs = null;
		String userName = new String();
		try {
			conn = ConnectionManager.getInstance().databaseCon();
			String getAccountStatus = "select user_name from s_users u where u.user_id=?";
			stmt = conn.prepareStatement(getAccountStatus);
			stmt.setString(1, userId);
			stmt.execute();
			/* Fetching the Result */
			rs = (ResultSet) stmt.getResultSet();
			if (rs != null) {
				/* Loop to iterate result set */
				while (rs.next()) {
					userName = rs.getString(1);
				}
			}			
			log.debug(this.getClass().getName() + " - [getUserName] - "
					+ "Exit");
			//return mvtType;
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [getUserName] method : - "
					+ exp.getMessage());
		} finally {
			JDBCCloser.close(rs, stmt, conn, session);

		}
		return userName;
	}
}