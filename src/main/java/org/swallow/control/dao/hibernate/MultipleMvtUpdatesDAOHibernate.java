/*
 * @(#)MultipleMvtUpdateDAOHibernate.java 1.0 03/05/06
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */

package org.swallow.control.dao.hibernate;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.Reader;
import java.lang.*;
import java.sql.CallableStatement;
import java.sql.Clob;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Types;
import java.util.*;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.hibernate.Interceptor;
import org.hibernate.Session;
import org.hibernate.SessionFactory;
import org.hibernate.Transaction;
import org.hibernate.query.Query;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Lazy;
import org.springframework.orm.hibernate5.support.HibernateDaoSupport;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;
import org.swallow.batchScheduler.ConnectionManager;
import org.swallow.control.dao.MultipleMvtUpdatesDAO;
import org.swallow.control.model.ProcessStatus;
import org.swallow.control.model.Role;
import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.util.JDBCCloser;
import org.swallow.util.SwtInterceptor;
import org.swallow.util.SwtUtil;
import org.swallow.work.model.Movement;


@Repository ("multipleMvtUpdatesDAO")
@Transactional
public class MultipleMvtUpdatesDAOHibernate extends HibernateDaoSupport implements MultipleMvtUpdatesDAO {
	private final Log log = LogFactory.getLog(MultipleMvtUpdatesDAOHibernate.class);

	public MultipleMvtUpdatesDAOHibernate(@Lazy @Qualifier("sessionFactory") SessionFactory sessionfactory) {
	    setSessionFactory(sessionfactory);
	}


	public boolean checkIfMvtExists(String hostId, String entityId, String movementId) throws SwtException {
		try {
			log.debug("MultipleMvtUpdatesDAOHibernate - [checkIfMvtExists] - Enter");

			java.util.List list = getHibernateTemplate().find(
				"select count(mvt) from Movement mvt where mvt.id.hostId=?0 and mvt.id.entityId=?1 and mvt.id.movementId=?2",
				new Object[] { hostId, entityId, movementId });

			boolean result = false;
			if (list != null && !list.isEmpty()) {
				Long count = (Long) list.get(0);
				result = (count != null && count > 0);
			}

			log.debug("MultipleMvtUpdatesDAOHibernate - [checkIfMvtExists] - Exit");
			return result;
		} catch (Exception e) {
			log.error("An exception occurred in MultipleMvtUpdatesDAOHibernate: checkIfMvtExists " + e.getMessage());
			throw new SwtException(e.getMessage());
		}
	}


	public Collection<Movement> getMovementList(List<Long> movementIds, String hostId, String entityId) throws SwtException {
		try {
			log.debug(this.getClass().getName() + " - [getMovementList] - Entry");

			StringBuilder hql = new StringBuilder("from Movement m where m.id.entityId = ?0 and m.id.hostId = ?1 and m.id.movementId in (");

			// Build the IN clause with positional parameters
			for (int i = 0; i < movementIds.size(); i++) {
				if (i > 0) {
					hql.append(", ");
				}
				hql.append("?").append(i + 2);
			}
			hql.append(") order by m.id.movementId");

			// Create parameter array
			Object[] params = new Object[movementIds.size() + 2];
			params[0] = entityId;
			params[1] = hostId;
			for (int i = 0; i < movementIds.size(); i++) {
				params[i + 2] = movementIds.get(i);
			}

			@SuppressWarnings("unchecked")
			List<Movement> movements = (List<Movement>) getHibernateTemplate().find(hql.toString(), params);

			log.debug(this.getClass().getName() + " - [getMovementList] - Exit");
			return movements;
		} catch (Exception e) {
			log.error(this.getClass().getName() + " - Exception in [getMovementList] method: " + e.getMessage());
			throw SwtErrorHandler.getInstance().handleException(e, "getMovementList", MultipleMvtUpdatesDAOHibernate.class);
		}
	}


	public Collection<Movement> getMovementList(List<Long> movementIds) throws SwtException {
		try {
			log.debug(this.getClass().getName() + " - [getMovementList] - Entry");

			StringBuilder hql = new StringBuilder("from Movement m where m.id.movementId in (");

			// Build the IN clause with positional parameters
			for (int i = 0; i < movementIds.size(); i++) {
				if (i > 0) {
					hql.append(", ");
				}
				hql.append("?").append(i);
			}
			hql.append(") order by m.id.movementId");

			// Create parameter array
			Object[] params = new Object[movementIds.size()];
			for (int i = 0; i < movementIds.size(); i++) {
				params[i] = movementIds.get(i);
			}

			@SuppressWarnings("unchecked")
			List<Movement> movements = (List<Movement>) getHibernateTemplate().find(hql.toString(), params);

			log.debug(this.getClass().getName() + " - [getMovementList] - Exit");
			return movements;
		} catch (Exception e) {
			log.error(this.getClass().getName() + " - Exception in [getMovementList] method: " + e.getMessage());
			throw SwtErrorHandler.getInstance().handleException(e, "getMovementList", MultipleMvtUpdatesDAOHibernate.class);
		}
	}

	public boolean checkIfEntityExists(String hostId, String entityId)
			throws SwtException {
		Connection conn = null;
		ResultSet res = null;
		PreparedStatement stmt = null;
		ResultSet rs = null;
		boolean result = false;

		try {
			log.debug("MultipleMvtUpdatesDAOHibernate  - [checkIfEntityExists] - Enter");

			conn = ConnectionManager.getInstance().databaseCon();
			String checkEntityQuery =  "select 1 from s_entity ent where ent.host_id=? and ent.entity_id=?";

			stmt = conn.prepareStatement(checkEntityQuery);
			stmt.setString(1, hostId);
			stmt.setString(2, entityId);
			stmt.execute();
			/* Fetching the Result */
			rs = (ResultSet) stmt.getResultSet();
			if (rs != null) {
				if(rs.next() == false) {
					result=false;
				}else {
					result=true;
				}

			}

			log.debug("MultipleMvtUpdatesDAOHibernate  - [checkIfEntityExists] - Exit");
		} catch (SQLException ex) {
			try {
				log.error("MultipleMvtUpdatesDAOHibernate  - [checkIfEntityExists]  Exception: " + ex.getMessage());
				// rollBack
				conn.rollback();
			} catch (SQLException e) {

			}

		} catch (Exception e) {
			log.error("An exception occured in " + "MultipleMvtUpdatesDAOHibernate : checkIfEntityExists "
					+ e.getMessage());
			throw new SwtException(e.getMessage());

		} finally {
			// Close conn and nullify objects
			Object[] exceptions = JDBCCloser.close(res, stmt, conn, null);
		}

		return result;
	}


	public boolean getRoleAccessDetails(String hostId, String roleId) throws SwtException {
		try {
			log.debug(this.getClass().getName() + " - [getRoleAccessDetails] - Entry");

			java.util.List list = getHibernateTemplate().find(
				"FROM Role r WHERE r.allowMsdMultiMvtUpdates = 'Y' AND r.hostId = ?0 AND r.roleId = ?1",
				new Object[] { hostId, roleId });

			boolean accessFlag = list != null && !list.isEmpty();

			log.debug(this.getClass().getName() + " - [getRoleAccessDetails] - Exit with accessFlag: " + accessFlag);
			return accessFlag;
		} catch (Exception exp) {
			log.error(this.getClass().getName() + " - Exception in [getRoleAccessDetails] method: ", exp);
			throw SwtErrorHandler.getInstance().handleException(exp, "getRoleAccessDetails", MultipleMvtUpdatesDAOHibernate.class);
		}
	}


	@Override
	public void executeMainProcedure(String listMovements, String action, String jsonValues, String noteText, String userId, String seq) throws SwtException {
		Session session = null;
		Connection conn = null;
		CallableStatement cstmt = null;
		try {
			session = getSessionFactory().openSession();
			conn = SwtUtil.connection(session);
			cstmt = conn.prepareCall("{call PKG_MULTIPLE_MOVS_ACTIONS.SP_MAIN(?,?,?,?,?,?)}");

			cstmt.setString(1, seq);
			cstmt.setString(2, listMovements);
			cstmt.setString(3, action);
			cstmt.setString(4, jsonValues);
			cstmt.setString(5, noteText);
			cstmt.setString(6, userId);
			cstmt.execute();
		} catch (Exception e) {
			throw new SwtException("Error executing stored procedure SP_MAIN", e);
		} finally {
			try {
				JDBCCloser.close(null, cstmt, conn, session);
			} catch (Exception e) {
				log.error(this.getClass().getName() + " - Exception in [executeMainProcedure] method: " + e.getMessage());
			}
		}
	}

	@Override
	public int getProcessStatus(String seq) throws SwtException {
		Session session = null;
		Connection conn = null;
		CallableStatement cstmt = null;
		try {
			session = getSessionFactory().openSession();
			conn = SwtUtil.connection(session);
			cstmt = conn.prepareCall("{? = call PKG_MULTIPLE_MOVS_ACTIONS.FN_PROCESS_STATUS(?)}");

			cstmt.registerOutParameter(1, Types.INTEGER);
			cstmt.setString(2, seq);
			cstmt.execute();
			return cstmt.getInt(1);
		} catch (Exception e) {
			throw new SwtException("Error executing function FN_PROCESS_STATUS", e);
		} finally {
			try {
				JDBCCloser.close(null, cstmt, conn, session);
			} catch (Exception e) {
				log.error(this.getClass().getName() + " - Exception in [getProcessStatus] method: " + e.getMessage());
			}
		}
	}

	@Override
	public void cleanTempProcess(String seq) throws SwtException {
		Session session = null;
		Connection conn = null;
		CallableStatement cstmt = null;
		try {
			session = getSessionFactory().openSession();
			conn = SwtUtil.connection(session);
			cstmt = conn.prepareCall("{call PKG_MULTIPLE_MOVS_ACTIONS.CLEAN_TEMP_PROCESS(?)}");
			cstmt.setString(1, seq);
			cstmt.execute();
		} catch (Exception e) {
			throw new SwtException("Error executing stored procedure CLEAN_TEMP_PROCESS", e);
		} finally {
			try {
				JDBCCloser.close(null, cstmt, conn, session);
			} catch (Exception e) {
				log.error(this.getClass().getName() + " - Exception in [cleanTempProcess] method: " + e.getMessage());
			}
		}
	}

	@Override
	public ProcessStatus getProcessDetails(String seq, String includeMovements) throws SwtException {
		ProcessStatus processStatus = new ProcessStatus();
		Session session = null;
		Connection conn = null;
		CallableStatement cstmt = null;
		ResultSet failedRs = null;
		ResultSet successRs = null;
		try {
			session = getSessionFactory().openSession();
			conn = SwtUtil.connection(session);
			cstmt = conn.prepareCall("{call PKG_MULTIPLE_MOVS_ACTIONS.SP_GET_PROCESS_STATUS(?,?,?,?,?,?,?,?)}");

			cstmt.setString(1, seq);
			cstmt.setString(2, includeMovements); // New parameter to determine if movement details are fetched
			cstmt.registerOutParameter(3, Types.INTEGER);
			cstmt.registerOutParameter(4, Types.INTEGER);
			cstmt.registerOutParameter(5, Types.INTEGER);
			cstmt.registerOutParameter(6, Types.INTEGER);
			cstmt.registerOutParameter(7, Types.REF_CURSOR);
			cstmt.registerOutParameter(8, Types.REF_CURSOR);

			cstmt.execute();
			processStatus.setTotalCount(cstmt.getInt(3));
			processStatus.setSuccessCount(cstmt.getInt(4));
			processStatus.setFailedCount(cstmt.getInt(5));
			processStatus.setPendingCount(cstmt.getInt(6));

			if ("Y".equalsIgnoreCase(includeMovements)) {
				failedRs = (ResultSet) cstmt.getObject(8);
				while (failedRs.next()) {
					processStatus.getFailedMovements().add(mapResultSetToMovement(failedRs, true));
				}

				successRs = (ResultSet) cstmt.getObject(7);
				while (successRs.next()) {
					processStatus.getSuccessfulMovements().add(mapResultSetToMovement(successRs, false));
				}
			}
		} catch (Exception e) {
			throw new SwtException("Error executing stored procedure GET_PROCESS_STATUS", e);
		} finally {
			try {
				JDBCCloser.close(successRs);
				JDBCCloser.close(failedRs, cstmt, conn, session);
			} catch (Exception e) {
				log.error(this.getClass().getName() + " - Exception in [getProcessDetails] method: " + e.getMessage());
			}
		}
		return processStatus;
	}

	private Movement mapResultSetToMovement(ResultSet rs, boolean isFailedRecord) throws SQLException {
		Movement movement = new Movement();
		movement.getId().setMovementId(rs.getLong("MOVEMENT_ID"));
		movement.getId().setEntityId(rs.getString("ENTITY_ID"));
		movement.setPositionLevel(rs.getInt("POSITION_LEVEL"));
		movement.setAmount(rs.getDouble("AMOUNT"));
		movement.setSign(rs.getString("SIGN"));
		movement.setCurrencyCode(rs.getString("CURRENCY_CODE"));
		movement.setAccttype(rs.getString("MOVEMENT_TYPE"));
		movement.setAccountId(rs.getString("ACCOUNT_ID"));
		movement.setBookCode(rs.getString("BOOKCODE"));
		movement.setCounterPartyId(rs.getString("COUNTERPARTY_ID"));
		movement.setCounterPartyText1(rs.getString("COUNTERPARTY_TEXT1"));
		movement.setCounterPartyText2(rs.getString("COUNTERPARTY_TEXT2"));
		movement.setCounterPartyText3(rs.getString("COUNTERPARTY_TEXT3"));
		movement.setCounterPartyText4(rs.getString("COUNTERPARTY_TEXT4"));
		movement.setBeneficiaryId(rs.getString("BENEFICIARY_ID"));
		movement.setBeneficiaryText1(rs.getString("BENEFICIARY_TEXT1"));
		movement.setBeneficiaryText2(rs.getString("BENEFICIARY_TEXT2"));
		movement.setBeneficiaryText3(rs.getString("BENEFICIARY_TEXT3"));
		movement.setBeneficiaryText4(rs.getString("BENEFICIARY_TEXT4"));
		movement.setMatchingParty(rs.getString("MATCHING_PARTY"));
		movement.setProductType(rs.getString("PRODUCT_TYPE"));
		movement.setPostingDate(rs.getDate("POSTING_DATE"));
		movement.setInputSource(rs.getString("INPUT_SOURCE"));
		movement.setMessageFormat(rs.getString("MESSAGE_FORMAT"));
		movement.setPredictStatus(rs.getString("PREDICT_STATUS"));
		movement.setExtBalStatus(rs.getString("EXT_BAL_STATUS"));
		movement.setMatchStatus(rs.getString("MATCH_STATUS"));
		movement.setReference1(rs.getString("REFERENCE1"));
		movement.setReference2(rs.getString("REFERENCE2"));
		movement.setReference3(rs.getString("REFERENCE3"));
		movement.setReference4(rs.getString("REFERENCE4"));
		movement.setMatchId(rs.getLong("MATCH_ID") != 0 ? rs.getLong("MATCH_ID") : null);
		movement.setNotesCount(rs.getInt("NOTES_COUNT"));
		movement.setValueDate(rs.getDate("VALUE_DATE"));
		movement.setSettlementDateTime(rs.getTimestamp("SETTLEMENT_DATETIME"));
		movement.setExpectedSettlementDateTime(rs.getTimestamp("EXPECTED_SETTLEMENT_DATETIME"));
		movement.setCriticalPaymentType(rs.getString("CRITICAL_PAYMENT_TYPE"));
		movement.setCustodianId(rs.getString("CUSTODIAN_ID"));
		movement.setCustodianText1(rs.getString("CUSTODIAN_TEXT1"));
		movement.setCustodianText2(rs.getString("CUSTODIAN_TEXT2"));
		movement.setCustodianText3(rs.getString("CUSTODIAN_TEXT3"));
		movement.setCustodianText4(rs.getString("CUSTODIAN_TEXT4"));
		movement.setCustodianText5(rs.getString("CUSTODIAN_TEXT5"));
		movement.setCounterPartyText5(rs.getString("COUNTERPARTY_TEXT5"));
		movement.setBeneficiaryText5(rs.getString("BENEFICIARY_TEXT5"));
		movement.setOrderingCustomerId(rs.getString("ORDERING_CUSTOMER"));
		movement.setOrderingInstitutionId(rs.getString("ORDERING_INSTITUTION"));
		movement.setSenderCorrespondentId(rs.getString("SENDERS_CORRES"));
		movement.setReceiverCorrespondentId(rs.getString("RECEIVERS_CORRES"));
		movement.setIntermediaryInstitutionId(rs.getString("INTMDRY_INSTITUTION_ID"));
		movement.setAccountWithInstitutionId(rs.getString("ACC_WITH_INSTITUTION_ID"));
		movement.setBeneficiaryCustomerId(rs.getString("BENEFICIARY_CUST"));
		//movement.setExtraText1(rs.getString("EXTRA_TEXT1"));
		movement.setIlmFcastStatus(rs.getString("ILM_FCAST_STATUS"));
		movement.setUetr(rs.getString("UETR"));
		if(isFailedRecord) {
			movement.setFailingCause(rs.getString("ERROR_DESC"));
		}

		return movement;
	}




}
