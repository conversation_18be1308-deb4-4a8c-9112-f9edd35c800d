/*
 * @(#)SystemAlertMessagesDAOHibernate.java 1.0 23/08/06
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.control.dao.hibernate;

import java.util.Collection;
import java.util.Iterator;
import java.util.List;


import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

import org.hibernate.*;
import org.hibernate.criterion.Expression;
import org.swallow.control.dao.SystemAlertMessagesDAO;
import org.swallow.control.model.SystemAlertMesages;
import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.util.JDBCCloser;
import org.swallow.util.SwtInterceptor;
import org.swallow.util.SwtUtil;
import org.springframework.context.annotation.Lazy;
import org.springframework.orm.hibernate5.support.HibernateDaoSupport;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.stereotype.Repository;


@Repository ("sysAlertMessagesDAO")
@Transactional
public class SystemAlertMessagesDAOHibernate extends HibernateDaoSupport
		implements SystemAlertMessagesDAO {
	public SystemAlertMessagesDAOHibernate(@Lazy SessionFactory sessionfactory) {
	    setSessionFactory(sessionfactory);
	}


	/**
	 * Final log instance for logging this class
	 */
	private final Log log = LogFactory
			.getLog(SystemAlertMessagesDAOHibernate.class);

	/**
	 * Collects the Alert message detail list from Database table P_ALERT 
	 * @param hostId
	 * @return Collection
	 * @throws SwtException
	 */
	public Collection getAlertMsgDetailList(String hostId) throws SwtException {

		log.debug(this.getClass().getName() + " - [getAlertMsgDetailList] - "
				+ "Entry");

		List list = getHibernateTemplate().find(
				"from SystemAlertMesages alert where alert.id.hostId=?0",
				new Object[] { hostId });
		//Commented by Selva to avoid unnecessary postflush calls on  04-May
//		getHibernateTemplate().flush();
		
		log.debug(this.getClass().getName() + " - [getAlertMsgDetailList] - "
				+ "Exit");
		return list;
	}

	/**
	 * Collects the Role list from database table S_Role 
	 * @param hostId
	 * @return Collection
	 * @throws SwtException
	 */
	public Collection getRollList(String hostId) throws SwtException {
		log.debug(this.getClass().getName() + " - [getRollList] - " + "Entry");
		List noofRecords = getHibernateTemplate().find(
				"from Role role where role.hostId=?0 order by role.roleId",
				new Object[] { hostId });
		//Commented by Selva to avoid unnecessary postflush calls on  04-May
//		getHibernateTemplate().flush();
		log.debug(this.getClass().getName() + " - [getRollList] - " + "Exit");
		return noofRecords;
	}

	/**
	 * Update the values in SystemAlertMesages bean in the database table
	 * P_ALERT
	 * @param alertmsg
	 * @throws SwtException
	 */
	public void updateAlertMsgDetail(SystemAlertMesages alertmsg)
			throws SwtException {
		Session session = null;
		Transaction tx = null;
		SwtInterceptor interceptor = null;
		try {
			log.debug(this.getClass().getName() + " - [updateAlertMsgDetail] - "
					+ "Entry");
			interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");
			session = getHibernateTemplate().getSessionFactory().withOptions().interceptor(interceptor).openSession();
			tx = session.beginTransaction();
			session.update(alertmsg);
			tx.commit();
			session.close();
			log.debug(this.getClass().getName() + " - [updateAlertMsgDetail] - "
					+ "Exit");
			
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					+ "- [deleteMatchQuality] - Exception " + exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp,
					"deleteMatchQuality", SystemAlertMessagesDAOHibernate.class);
		}

	}

	/**
	 * Get the Editable fields from the Database table P_ALERT 
	 * @param hostId
	 * @param alertstage
	 * @return SystemAlertMesages
	 * @throws SwtException
	 */
	public SystemAlertMesages getEditableData(String hostId, String alertstage)
			throws SwtException {

		SystemAlertMesages systemAlert = null;
		log.debug(this.getClass().getName() + " - [getEditableData] - "
				+ "Entry");
		List alertMsgList = getHibernateTemplate()
				.find(
						"from SystemAlertMesages system where  system.id.hostId=?0 and system.id.alertstage=?1",
						new Object[] { hostId, alertstage });

		Iterator itr = alertMsgList.iterator();
		while (itr.hasNext()) {

			systemAlert = (SystemAlertMesages) itr.next();
		}
		//Commented by Selva to avoid unnecessary postflush calls on  04-May
//		getHibernateTemplate().flush();
		log.debug(this.getClass().getName() + " - [getEditableData] - "
				+ "Exit");
		return systemAlert;
	}

	/**
	 * This message checks whether the passed Alert Stage is exit with enable
	 * state or not.
	 * 
	 * @param hostId
	 * @param alertStage
	 * @return boolean
	 * @throws SwtException
	 */
	public boolean alertAllow(String hostId, String alertStage)
			throws SwtException {
		log.debug(this.getClass().getName() + " - [alertAllow] - " + "Entry");
		SystemAlertMesages systemAlertMesages = new SystemAlertMesages();
		boolean flag = false;
		Criteria criteria = null;
		Session session = null;
		session = getHibernateTemplate().getSessionFactory().openSession();

		criteria = session.createCriteria(SystemAlertMesages.class).add(
				Expression.like("id.hostId", hostId)).add(
				Expression.like("id.alertstage", alertStage)).add(
				Expression.eq("enableflg", "A"));
		// End : Mantis 1615 Added by Nithiyananthan on 02-02-2012
		// get the result from criteria
		try {
			systemAlertMesages =  (SystemAlertMesages) criteria.uniqueResult();
		} catch (Exception e) {
			log.debug(this.getClass().getName()
					+ " - Exception Catched in [alertAllow] method : - "
					+ e.getMessage());

			log.error(this.getClass().getName()
					+ " - Exception Catched in [alertAllow] method : - "
					+ e.getMessage());
			e.printStackTrace();
			throw new SwtException();
		}finally {

			//edited by KaisBS for mantis 1745 : introducing the implementation of the JDBCCloser class
			JDBCCloser.close(session);

		}

		flag = systemAlertMesages != null ? true : false;
		log.debug(this.getClass().getName() + " - [alertAllow] - " + "Exit");
		return flag;
	}

}