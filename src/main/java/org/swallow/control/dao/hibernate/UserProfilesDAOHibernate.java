/*
 * Created on Dec 15, 2005
 *
 * TODO To change the template for this generated file go to
 * Window - Preferences - Java - Code Style - Code Templates
 */
package org.swallow.control.dao.hibernate;

import java.util.Collection;
import java.util.Iterator;
import java.util.List;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

import org.swallow.control.dao.UserProfilesDAO;
import org.swallow.exception.SwtErrorHandler;
import org.swallow.model.UserProfile;
import org.swallow.util.SwtInterceptor;
import org.swallow.util.SwtUtil;
import org.springframework.context.annotation.Lazy;
import org.springframework.orm.hibernate5.support.HibernateDaoSupport;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.stereotype.Repository;
import org.hibernate.SessionFactory;
import org.hibernate.Transaction;
import org.hibernate.Session;




/**
 * <AUTHOR>
 *
 * TODO To change the template for this generated type comment go to
 * Window - Preferences - Java - Code Style - Code Templates
 */
@Repository ("userProfilesDAO")
@Transactional
public class UserProfilesDAOHibernate extends HibernateDaoSupport implements UserProfilesDAO {
	public UserProfilesDAOHibernate(@Lazy SessionFactory sessionfactory) {
	    setSessionFactory(sessionfactory);
	}


	private final Log log=LogFactory.getLog(UserProfilesDAOHibernate.class);
	
		
	public List fetchDetails(String hostId,String userId,String profileId)
	
	{
		log.debug("entering 'fetchDetails' method");
		UserProfile userprofiles = null;
		
		List userprofilelist=getHibernateTemplate().find("from UserProfile userpr where userpr.id.hostId=?0 and userpr.id.userId=?1 and userpr.id.profileId=?2",
				new Object[]{hostId,userId,profileId});
		
		log.debug("exiting 'fetchDetails' method");
		//Commented by Selva to avoid unnecessary postflush calls on  04-May
//		getHibernateTemplate().flush();
		
		return userprofilelist;
		
	}
	
	public void saveuserProfileDetails(UserProfile userprofiles)
	{
		Session session = null;
		Transaction tx = null;
		SwtInterceptor interceptor = null;
		
		try {
			log.debug("entering 'saveuserProfileDetails' method");
			String isCurrentProfile = userprofiles.getCurrentProfile();
			
			log.debug("isCurrentProfile - " + isCurrentProfile);
			
			if(isCurrentProfile != null && isCurrentProfile.equals("Y"))
			{
				resetCurrentProfile(userprofiles.getId().getHostId(),userprofiles.getId().getUserId(),userprofiles.getId().getProfileId());
			}
			
			
			interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");
			session = getHibernateTemplate()
						.getSessionFactory()
						.withOptions().interceptor(interceptor).openSession();
			tx = session.beginTransaction();
			session.save(userprofiles);
			tx.commit();
			session.close();
			log.debug("exiting 'saveuserProfileDetails' method");		
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					+ "- [saveuserProfileDetails] - Exception " + exp.getMessage());
			try {
				throw SwtErrorHandler.getInstance().handleException(exp,
						"saveuserProfileDetails", UserProfilesDAOHibernate.class);
			}catch(Exception e) {
				
			}
		}
	}
	
	public void updateuserProfileDetails(UserProfile userprofiles)
	{
		Session session = null;
		Transaction tx = null;
		SwtInterceptor interceptor = null;
		try {
			log.debug("entering 'updateuserProfileDetails' method");
			
			String isCurrentProfile = userprofiles.getCurrentProfile();
			
			log.debug("isCurrentProfile - " + isCurrentProfile);		
			if(isCurrentProfile != null && isCurrentProfile.equals("Y"))
			{
				resetCurrentProfile(userprofiles.getId().getHostId(),userprofiles.getId().getUserId(),userprofiles.getId().getProfileId());
			}
	
			interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");
			session = getHibernateTemplate().getSessionFactory().withOptions().interceptor(interceptor).openSession();
			tx = session.beginTransaction();
			session.update(userprofiles);
			tx.commit();
			session.close();
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					+ "- [updateuserProfileDetails] - Exception " + exp.getMessage());
			try {
				throw SwtErrorHandler.getInstance().handleException(exp,
						"updateuserProfileDetails", UserProfilesDAOHibernate.class);
			}catch(Exception e) {
							
			}
		}
		log.debug("exiting 'updateuserProfileDetails' method");		
	}
	
	public void deleteUserProfileDetails(UserProfile userprofiles)
	{
		log.debug("entering 'deleteuserProfileDetails' method");
		
		Session session = null;
		Transaction tx = null;
		SwtInterceptor interceptor = null;
		try {
			String hostId = userprofiles.getId().getHostId();
			String userId = userprofiles.getId().getUserId();
			String profileId = userprofiles.getId().getProfileId();
					
	
			List userprofileDetaillist = getHibernateTemplate().find("from UserProfileDetail userpr where userpr.id.hostId=?0 and userpr.id.userId=?1 and userpr.id.profileId=?2",
					new Object[]{hostId,userId,profileId});
			
			interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");
			session = getHibernateTemplate().getSessionFactory().withOptions().interceptor(interceptor).openSession();
			tx = session.beginTransaction();
			if(userprofileDetaillist != null)
			{
				Iterator itr = 	userprofileDetaillist.iterator();
				while(itr.hasNext())
				{
					Object obj = itr.next();
					session.delete(obj);
				}
			}
			
			tx.commit();
			session.close();
			log.debug("exiting 'deleteuserProfileDetails' method");	
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					+ "- [deleteUserProfileDetails] - Exception " + exp.getMessage());
			try {
				throw SwtErrorHandler.getInstance().handleException(exp,
						"deleteUserProfileDetails", UserProfilesDAOHibernate.class);
			}catch(Exception e) {
							
			}
		}
		
	}
	
	private void resetCurrentProfile(String hostId,String userId,String profileId)
	{
		log.debug("entering 'resetCurrentProfile' method");
		
		Session session = null;
		Transaction tx = null;
		SwtInterceptor interceptor = null;
		try {
			
			// first fetch the current profile
	
			UserProfile usr = new UserProfile();
			
			List currentProfiles = getHibernateTemplate().find("from UserProfile userpr where userpr.id.hostId=?0 and userpr.id.userId=?1 and userpr.currentProfile = 'Y' and userpr.id.profileId!=?2",
					new Object[]{hostId,userId,profileId});
			
			log.debug("Current Profile - " + currentProfiles);
	
			// reset the current profile flag of above profiles
			
			interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");
			session = getHibernateTemplate().getSessionFactory().withOptions().interceptor(interceptor).openSession();
			tx = session.beginTransaction();
			
			
			if(currentProfiles != null)
			{
				Iterator itr = currentProfiles.iterator();
				while(itr.hasNext())
				{
					UserProfile userProfileObj = (UserProfile)itr.next();
					userProfileObj.setCurrentProfile("N");
					session.update(userProfileObj);
				}
			}
			tx.commit();
			session.close();
			
			//Commented by Selva to avoid unnecessary postflush calls on  04-May
	//		getHibernateTemplate().flush();
			log.debug("exiting 'resetCurrentProfile' method");	
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					+ "- [deleteUserProfileDetails] - Exception " + exp.getMessage());
			try {
				throw SwtErrorHandler.getInstance().handleException(exp,
						"deleteUserProfileDetails", UserProfilesDAOHibernate.class);
			}catch(Exception e) {
							
			}
		}
	}
	
	public void updateuserProfileDetails(UserProfile userprofiles,Collection userProfileDetailsColl)
	{
		
		Session session = null;
		Transaction tx = null;
		SwtInterceptor interceptor = null;
		try {
			log.debug("entering 'updateuserProfileDetails' method ");
			interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");
			session = getHibernateTemplate().getSessionFactory().withOptions().interceptor(interceptor).openSession();
			tx = session.beginTransaction();
			
			//userprofilesDAO.deleteUserProfileDetails(userprofiles);
			
			//deleteUserProfileDetails(userprofiles);
			// Deleting the profile details 
			String hostId = userprofiles.getId().getHostId();
			String userId = userprofiles.getId().getUserId();
			String profileId = userprofiles.getId().getProfileId();
					
			
	//		updateuserProfileDetails(userprofiles);
			
			String isCurrentProfile = userprofiles.getCurrentProfile();
			
			log.debug("isCurrentProfile - " + isCurrentProfile);
			
			if(isCurrentProfile != null && isCurrentProfile.equals("Y"))
			{
				//resetCurrentProfile(userprofiles.getId().getHostId(),userprofiles.getId().getUserId(),userprofiles.getId().getProfileId());
	
				List currentProfiles = getHibernateTemplate().find("from UserProfile userpr where userpr.id.hostId=?0 and userpr.id.userId=?1 and userpr.currentProfile = 'Y' and userpr.id.profileId != ?2",
						new Object[]{hostId,userId,profileId});
				
				log.debug("Current Profile - " + currentProfiles);
	
				// reset the current profile flag of above profiles
				
				if(currentProfiles != null)
				{
					Iterator itr = currentProfiles.iterator();
					while(itr.hasNext())
					{
						UserProfile userProfileObj = (UserProfile)itr.next();
						userProfileObj.setCurrentProfile("N");
						session.update(userProfileObj);
					}
				}
				
			}
			UserProfile userprofilesNew = (UserProfile)SwtUtil.copy(userprofiles);
			
			session.update(userprofilesNew);
	
			List userprofileDetaillist = getHibernateTemplate().find("from UserProfileDetail userpr where userpr.id.hostId=?0 and userpr.id.userId=?1 and userpr.id.profileId=?2",
					new Object[]{hostId,userId,profileId});
			
			if(userprofileDetaillist != null)
			{
				Iterator itr = 	userprofileDetaillist.iterator();
				while(itr.hasNext())
				{
					Object obj = itr.next();
					session.delete(obj);
				}
			}
			
			
	
			
			if(userProfileDetailsColl != null)
			{
				Iterator itr = userProfileDetailsColl.iterator();
				while(itr.hasNext())
				{
					Object obj =  itr.next();
					session.save(obj);
				}
			}
			log.debug("exiting 'updateuserProfileDetails' method ");
			tx.commit();
			session.close();
	} catch (Exception exp) {
		log.error(this.getClass().getName()
				+ "- [deleteUserProfileDetails] - Exception " + exp.getMessage());
		try {
			throw SwtErrorHandler.getInstance().handleException(exp,
					"deleteUserProfileDetails", UserProfilesDAOHibernate.class);
		}catch(Exception e) {
						
		}
	}
	
	}
	
	
}