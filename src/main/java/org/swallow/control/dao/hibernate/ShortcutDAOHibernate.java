/*
 * @(#)ShortcutDAOHibernate.java 21/12/05
 * 
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 * 
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */

package org.swallow.control.dao.hibernate;

import java.util.Collection;
import java.util.Iterator;
import java.util.List;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.swallow.control.dao.ShortcutDAO;
import org.swallow.control.model.Shortcut;
import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.util.SwtInterceptor;
import org.swallow.util.SwtUtil;

import org.hibernate.Session;
import org.hibernate.SessionFactory;
import org.hibernate.Transaction;
import org.springframework.context.annotation.Lazy;
import org.springframework.orm.hibernate5.support.HibernateDaoSupport;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

@Repository ("shortcutDAO")
@Transactional

public class ShortcutDAOHibernate extends HibernateDaoSupport implements
		ShortcutDAO {
	private final Log log = LogFactory.getLog(ShortcutDAOHibernate.class);
	private final static String STRHQLQuery1 = "from Shortcut m where m.id.hostId=?0 and m.id.userId=?1";
	private final static String STRHQLQuery3 = "from MenuItem m order by m.description";
	private final static String STRHQLQuery4 = "from Shortcut m where m.id.hostId=?0 and m.id.userId=?1 and m.id.shortcutId=?2";

	public ShortcutDAOHibernate(@Lazy SessionFactory sessionfactory){
	    setSessionFactory(sessionfactory);
	}
	/**
	 * @param hostId
	 * @param userId
	 * @return
	 * @throws SwtException
	 */
	@SuppressWarnings("unchecked")
	public Collection getShortcutDetailList(String hostId, String userId)
			throws SwtException {
		log.debug("Entering getMsgFieldDetailList");
		java.util.List list = getHibernateTemplate().find(STRHQLQuery1,
				new Object[] { hostId, userId });
		// Commented by Selva to avoid unnecessary postflush calls on 04-May
		log.debug("noofRecords.size : " + list.size());

		return list;
	}

	/**
	 * @return
	 * @throws SwtException
	 */
	@SuppressWarnings("unchecked")
	public Collection getMenuList() throws SwtException {
		log.debug("Entering getShortcutList");
		java.util.List list = getHibernateTemplate().find(STRHQLQuery3);
		// Commented by Selva to avoid unnecessary postflush calls on 04-May
		log.debug("noofRecords.size : " + list.size());
		return list;
	}

	/**
	 * @param shrtcut
	 * @throws SwtException
	 */
	@SuppressWarnings("unchecked")
	public void saveShortcutDetails(Shortcut shrtcut) throws SwtException {
		List records = null;
		Session session = null;
		Transaction tx = null;
		SwtInterceptor interceptor = null;
		try{
		/*
		 * Start : Modified for Mantis 1366-Remove entry to log if duplicate
		 * records added by betcy on 18-02-2011
		 */
		records = getHibernateTemplate()
				.find(
						"from Shortcut m where m.id.hostId=?0 and m.id.userId=?1 and m.id.shortcutId=?2",
						new Object[] { shrtcut.getId().getHostId(),
								shrtcut.getId().getUserId(),
								shrtcut.getId().getShortcutId() });

		if (records.size() == 0) {
			interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");
			session = getHibernateTemplate()
						.getSessionFactory().withOptions().interceptor(interceptor).openSession();
			tx = session.beginTransaction();
			session.save(shrtcut);
			tx.commit();
			session.close();
			log.debug("exiting 'saveShortcutDetails' method");
		} else {
			throw new SwtException(
					"errors.DataIntegrityViolationExceptioninAdd");
		}
		log.debug(this.getClass().getName()
				+ "- [saveShortcutDetails] - Exiting");
		
		}catch (Exception exp) {
		log.error(this.getClass().getName()
					+ " - Exception Catched in [saveShortcutDetails] method : - "
					+ exp.getMessage());
		throw SwtErrorHandler.getInstance().handleException(exp,
				"saveShortcutDetails", this.getClass());	
		}
		finally
		{
			records=null;
		}
		/*
		 * End : Modified for Mantis 1366-Remove entry to log if duplicate
		 * records added by betcy on 18-02-2011
		 */
	}

	/**
	 * @param shrtcut
	 * @throws SwtException
	 */
	public void updateShortcutDetails(Shortcut shrtcut) throws SwtException {
		Session session = null;
		Transaction tx = null;
		SwtInterceptor interceptor = null;
		try {
			log.debug("entering 'updateShortcutDetails' method" + shrtcut);
			interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");
			session = getHibernateTemplate().getSessionFactory().withOptions().interceptor(interceptor).openSession();
			tx = session.beginTransaction();
			session.update(shrtcut);
			tx.commit();
			session.close();;
			log.debug("exiting 'updateShortcutDetails' method");
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					+ "- [updateShortcutDetails] - Exception " + exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp,
					"updateShortcutDetails", ShortcutDAOHibernate.class);
		}
	}

	/**
	 * @param shrtcut
	 * @throws SwtException
	 */
	public void deleteShortcutDetail(Shortcut shrtcut) throws SwtException {
		Session session = null;
		Transaction tx = null;
		SwtInterceptor interceptor = null;
		try {
			log.debug("entering 'deleteShortcutDetail' method");
			interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");
			session = getHibernateTemplate().getSessionFactory().withOptions().interceptor(interceptor).openSession();
			tx = session.beginTransaction();
			session.delete(shrtcut);
			tx.commit();
			session.close();
			log.debug("exiting 'deleteShortcutDetail' method");
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					+ "- [deleteShortcutDetail] - Exception " + exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp,
					"deleteShortcutDetail", ShortcutDAOHibernate.class);
		}
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see org.swallow.control.dao.ShortcutDAO#editShortcutDetails(java.lang.String,
	 *      java.lang.String, java.lang.String)
	 */
	@SuppressWarnings("unchecked")
	public Shortcut editShortcutDetails(String userId, String shortcutId,
			String hostId) throws SwtException {
		java.util.List list = getHibernateTemplate().find(STRHQLQuery4,
				new Object[] { hostId, userId, shortcutId });
		// Commented by Selva to avoid unnecessary postflush calls on 04-May
		log.debug("noofRecords.size : " + list.size());
		Shortcut shortcut = new Shortcut();
		Iterator itr = list.iterator();
		while (itr.hasNext()) {
			shortcut = (Shortcut) itr.next();

		}
		// Commented by Selva to avoid unnecessary postflush calls on 04-May
		return shortcut;
	}

}