/*
 * Created on Apr 28, 2006
 *
 * TODO To change the template for this generated file go to
 * Window - Preferences - Java - Code Style - Code Templates
 */
package org.swallow.control.dao.hibernate;

import java.util.Collection;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

import org.swallow.control.dao.MatchDriverDAO;
import org.swallow.control.model.SystemLog;
import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.util.SwtInterceptor;
import org.swallow.util.SwtUtil;
import org.swallow.work.model.MatchDriver;
import org.springframework.context.annotation.Lazy;
import org.springframework.orm.hibernate5.support.HibernateDaoSupport;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.stereotype.Repository;
import org.springframework.beans.factory.annotation.Autowired;
import org.hibernate.HibernateException;
import org.hibernate.SessionFactory;
import org.hibernate.Transaction;
import org.hibernate.Session;




/**
 * <AUTHOR>
 * 
 * TODO To change the template for this generated type comment go to Window -
 * Preferences - Java - Code Style - Code Templates
 */
@Repository ("matchDriverDAO")
@Transactional
public class MatchDriverDAOHibernate extends HibernateDaoSupport implements
		MatchDriverDAO {
	public MatchDriverDAOHibernate(@Lazy SessionFactory sessionfactory) {
	    setSessionFactory(sessionfactory);
	}

	private final Log log = LogFactory.getLog(MatchDriverDAOHibernate.class);

	public Collection getMatchDriverList(String hostId, String entityId)
			throws SwtException {
		/*
		 * Betcy:13/11/08:Modified Query to restrict * when Load the data's in a
		 * screen
		 */
		java.util.List list = getHibernateTemplate()
				.find(
						"from MatchDriver matchDriver where matchDriver.id.hostId=?0 and matchDriver.id.entityId=?1 and matchDriver.id.currencyCode!='*'",
						new Object[] { hostId, entityId });

		log.debug("noofRecords.size : " + list.size());
		log.debug("exiting getMatchDriverList: ");
		return list;
	}

	public void updateMatchDriverDetail(MatchDriver matchDriver)
			throws SwtException {
		Session session = null;
		Transaction tx = null;
		SwtInterceptor interceptor = null;
		try {
			log.debug("Entring updateMatchDriverDetail");
			// log.debug(matchDriver.toString());
			interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");
			session = getHibernateTemplate().getSessionFactory().withOptions().interceptor(interceptor).openSession();
			tx = session.beginTransaction();
			session.update(matchDriver);
			tx.commit();
			session.close();
			log.debug("exiting updateMatchDriverDetail");
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					+ "- [updateMatchDriverDetail] - Exception " + exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp,
					"updateMatchDriverDetail", MatchDriverDAOHibernate.class);
		}
	}

	/**
	 * 
	 * @param hostId
	 * @param entityId
	 * @param currencyCode
	 * @return
	 * @throws SwtException
	 */
	public Collection getMatchDriverList(String hostId, String entityId,
			String currencyCode) throws SwtException {
		java.util.List list = getHibernateTemplate().find(
				"from MatchDriver matchDriver where matchDriver.id.hostId=?0 "
						+ "and matchDriver.id.entityId = ?1"
						+ "and matchDriver.id.currencyCode = ?2",
				new Object[] { hostId, entityId, currencyCode });

		log.debug("noofRecords.size : " + list.size());
		log.debug("exiting getMatchDriverList: ");
		return list;
	}

	// Method added by Bala for Mantis 1420 - Test date situation can
	// lead to matching process failing to start on 14-Apr-2011
	/**
	 * This method is used to save the system log details
	 * 
	 * @param SystemLog
	 *            object
	 * @return
	 * @throws SwtException
	 */
	public void saveSystemLog(SystemLog systemLog) throws SwtException {
		Session session = null;
		Transaction tx = null;
		SwtInterceptor interceptor = null;
		try {
			log.debug(this.getClass().getName() + " saveSystemLog() - Enter");
			interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");
			session = getHibernateTemplate()
						.getSessionFactory()
						.withOptions().interceptor(interceptor).openSession();
			tx = session.beginTransaction();
			session.save(systemLog);
			tx.commit();
			session.close();
			
			log.debug(this.getClass().getName() + " saveSystemLog() - Exit");
		} catch (Exception e) {
			log.error("Exception occured in saving system log."
					+ e.getMessage());
			throw SwtErrorHandler.getInstance().handleException(e,
					"saveSystemLog", MatchDriverDAOHibernate.class);
		}
	}
}
