/*
 * @(#)MessageInternalDAO.java 29/12/2005
 * 
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 * 
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */

package org.swallow.control.dao;



import java.util.Collection;
import java.util.List;
import org.swallow.control.model.MessageInternal;
import org.swallow.control.model.MessageInternalUser;
import org.swallow.dao.*;
import org.swallow.exception.SwtException;
import org.swallow.model.User;


public interface MessageInternalDAO extends DAO{
	/**
	 * @param hostId
	 * @return
	 * @throws SwtException
	 */
	public Collection getUserList(String hostId) throws SwtException;
	/**
	 * @return
	 * @throws SwtException
	 */
	public Collection getRoleList() throws SwtException;
	/**
	 * @param msgint
	 * @throws SwtException
	 */
	public void saveMessage(MessageInternal msgint) throws SwtException;
	/**
	 * @return
	 * @throws SwtException
	 */
	public List getMessageId() throws SwtException;
	/**
	 * @param msgintuser
	 * @throws SwtException
	 */
	public void saveMessageRecords(MessageInternalUser msgintuser)throws SwtException;
	/**
	 * @param hostId
	 * @param userList
	 * @return
	 * @throws SwtException
	 */
	public Collection getUserStatus(String hostId, String userList)throws SwtException;
	/**
	 * @param hostId
	 * @param roleList
	 * @return
	 * @throws SwtException
	 */
	public List getUsersforRoles(String hostId,String roleList) throws SwtException;
	/**
	 * @param hostId
	 * @return
	 * @throws SwtException
	 */
	public List getAllUsers(String hostId) throws SwtException;
	/**
	 * @return
	 * @throws SwtException
	 */
	public List getAllRoles() throws SwtException;
}