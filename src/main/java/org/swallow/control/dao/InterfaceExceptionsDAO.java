/*
 * @(#)InterfaceExceptionsDAO.java 1.0 15/06/2011
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.control.dao;

import org.swallow.control.model.InterfaceExceptionsData;
import org.swallow.control.model.InterfaceExceptionsDataModel;
import org.swallow.exception.SwtException;
import org.swallow.util.OpTimer;
import org.swallow.util.SystemFormats;

/**
 * This class has methods that are used in accessing and manipulating the data
 * in the database related to Interface Exceptions screen.<br>
 * 
 * Author: Marshal<br>
 * Date: 24-June-2011<br>
 */
public interface InterfaceExceptionsDAO {
	/**
	 * Method to get exception messages list from data base for given message
	 * status and message type.<br>
	 * 
	 * @param page
	 * @param opTimer
	 * @param dateFormat
	 * @param format
	 * @param fromPCM
	 * @return InterfaceExceptionsData
	 * @throws SwtException
	 */
	public InterfaceExceptionsData getMessagePage(InterfaceExceptionsData page,
			OpTimer opTimer, String dateFormat, SystemFormats format, boolean fromPCM)
			throws SwtException;

	/**
	 * Method to get Archive message for selected sequence Id<br>
	 * 
	 * @param seqId
	 * @param opTimer
	 * @param fromPCM
	 * @return InterfaceExceptionsDataModel
	 * @throws SwtException
	 */
	public InterfaceExceptionsDataModel getArchiveMessage(String seqId,
			OpTimer opTimer, boolean fromPCM) throws SwtException;

	/**
	 * This method is used to re-process the invalid messages(say, Filtered and
	 * Bad messages).<br>
	 * After the re-processing, the status of the message is changed as
	 * 'Processed'.<br>
	 * 
	 * @param messageIds
	 * @param opTimer
	 * @param fromPCM
	 * @throws SwtException
	 */
	public void reprocessMessage(String[] messageIds, OpTimer opTimer, boolean fromPCM)
			throws SwtException;
}
