
/*
 * @(#)SystemAlertMessagesDAO.java 1.0 23/08/06
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.control.dao;

import java.util.Collection;

import org.swallow.control.model.SystemAlertMesages;
import org.swallow.exception.SwtException;


public interface SystemAlertMessagesDAO {

	/**
	 * Collects the Alert message detail list from Database table P_ALERT
	 * @param hostId
	 * @return Collection
	 * @throws SwtException
	 */
	public Collection getAlertMsgDetailList(String hostId)  throws SwtException;
	
	/**
	 * Collects the Role list from database table S_Role
	 * @param hostId
	 * @return Collection
	 * @throws SwtException
	 */
	public Collection getRollList(String hostId)  throws SwtException;
	
	/**
	 * Update the values in SystemAlertMesages bean in the database table
	 * P_ALERT
	 * @param alertmsg
	 * @throws SwtException
	 */
	public void updateAlertMsgDetail(SystemAlertMesages  alertmsg) throws SwtException;
	
	/**
	 * Get the Editable fields from the Database table P_ALERT
	 * @param hostId
	 * @param alertstage
	 * @return SystemAlertMesages
	 * @throws SwtException
	 */
	public SystemAlertMesages getEditableData(String hostId,String alertstage)  throws SwtException;
	
	/**
	 * This message checks whether the passed Alert Stage is exit with enable state or not.
	 * @param hostId
	 * @param alertStage
	 * @return boolean
	 * @throws SwtException
	 */
	public boolean alertAllow(String hostId , String alertStage )
	throws SwtException;
	
	
	
	 
}
