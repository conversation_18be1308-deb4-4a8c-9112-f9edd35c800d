/*
 * Created on Jan 27, 2006
 *
 * TODO To change the template for this generated file go to
 * Window - Preferences - Java - Code Style - Code Templates
 */
package org.swallow.control.dao;

import java.util.ArrayList;
import java.util.Collection;

import javax.management.relation.RoleList;

import org.swallow.control.model.Job;
import org.swallow.control.model.JobStatus;
import org.swallow.control.model.ScheduledReportParams;
import org.swallow.control.model.ScheduledReportType;
import org.swallow.control.model.Scheduler;
import org.swallow.dao.DAO;
import org.swallow.exception.SwtException;

/**
 * <AUTHOR>
 * 
 * TODO To change the template for this generated type comment go to Window -
 * Preferences - Java - Code Style - Code Templates
 */
public interface SchedulerDAO extends DAO {

	public Collection getJobStatusDetails(String hostId, String scheduledJobType) throws SwtException;

	public Collection getJobNameList(String hostId) throws SwtException;

	public Collection getJobNames(String hostId) throws SwtException;

	public Collection getSchedulerDetails(Integer scheduleId)
			throws SwtException;

	/**
	 * @param scheduler
	 * @throws SwtException
	 */
	public void saveSchedulerDetail(Scheduler scheduler) throws SwtException;

	/**
	 * @param scheduler
	 * @throws SwtException
	 */
	public void updateSchedulerDetail(Scheduler scheduler) throws SwtException;

	/**
	 * @param scheduler
	 * @throws SwtException
	 */
	public void deleteSchedulerDetail(Scheduler scheduler) throws SwtException;

	/* START : Code change at offshore for Integration */
	/**
	 * This method used to get the job's details.
	 * 
	 * @param hostId
	 * @param jobId
	 * @return
	 * @throws SwtException
	 */
	public Job getJobDetail(String hostId, String jobId) throws SwtException;

	/**
	 * This method used to get the JobStatus details.
	 * 
	 * @param scheduleId
	 * @return
	 * @throws SwtException
	 */
	public JobStatus getJobStatus(Integer scheduleId)
			throws SwtException;

	/**
	 * This method is added for integration of Input System. as when we schedule
	 * an input job one row has to be inserted into S_JOB_STATUS table. This
	 * method is used for that purpose. Incase of System job we dont use this
	 * function as the is wrtten in the Scheduler's code.
	 * 
	 * @param jobStatus
	 * @throws SwtException
	 */
	public void saveJobStatus(JobStatus jobStatus) throws SwtException;

	/**
	 * This method is added for integration of Input System. as when we enable
	 * or disable an input job one row has to be inserted into S_JOB_STATUS
	 * table. This method is used for that purpose. Incase of System job we dont
	 * use this function as the is wrtten in the Scheduler's code.
	 * 
	 * @param jobStatus
	 * @throws SwtException
	 */
	public void updateJobStatus(JobStatus jobStatus) throws SwtException;

	/* END : Code change at offshore for Integration */

	// Method added by Bala for Mantis 1420 Test date situation can lead to
	// matching process failing to start on 08-Apr-2011

	/**
	 * This method used to get the JobType details.
	 * 
	 * @param hostId
	 * @return Scheduler object
	 * @throws SwtException
	 */
	public Scheduler getJobType(String hostId) throws SwtException;
	/**
	 * This method returns the list of report jobs
	 * @param hostId
	 * @return
	 * @throws SwtException
	 */
	public 	ArrayList<Job> getReportsJobList(String hostId) throws SwtException ;
	
	/**
	 * This method used to get the reports type list for the selected Job id
	 * 
	 * @param hostId
	 * @return
	 * @throws SwtException
	 */
	public 	ArrayList<ScheduledReportType> getReportTypes(String hostId, String jobId) throws SwtException ;
	
	/**
	 * Get available job type list
	 * @param hostId
	 * @return
	 * @throws SwtException
	 */
	public Collection getJobTypeList(String hostId) throws SwtException;
	
	/**
	 * Get job name list according to the selected job type Process or Report
	 * @param hostId
	 * @param selectedJobType
	 * @return Collection
	 * @throws SwtException
	 */
	public Collection getJobNameList(String hostId, String selectedJobType) throws SwtException;

	/**
	 * Get user list
	 * @param hostId
	 * @param userList
	 * @return
	 * @throws SwtException
	 */
	public Collection getUserList(String hostId, String userList) throws SwtException;
	
	/**
	 * Get all role list available if {@link RoleList} is null and and selected role list if not
	 * @param hostId
	 * @param roleList
	 * @return
	 * @throws SwtException
	 */
	public Collection getRoleList(String hostId, String roleList) throws SwtException;
	
	public ScheduledReportParams getScheduledReportParams(Integer scheduleId) throws SwtException;

	/**
	 * Get the screen details of a defined menu item Id from data base.
	 * 
	 * @param menuItemId
	 * @return ArrayList
	 * @throws SwtException
	 */
	public ArrayList getScreenDetails(String menuItemId) throws SwtException;

	/**
	 * return Scheduled Report Type configuration for a specific job Id 
	 * 
	 * @param selectedJobId
	 * @return ArrayList
	 * @throws SwtException
	 */
	public ArrayList getScheduledReportTypeConfig(String selectedJobId) throws SwtException;
}
