/*
 * Created on Dec 15, 2005
 *
 * TODO To change the template for this generated file go to
 * Window - Preferences - Java - Code Style - Code Templates
 */
package org.swallow.control.dao;

import org.swallow.dao.DAO;
import java.util.*;

import org.swallow.model.UserProfile;


/**
 * <AUTHOR>
 *
 * TODO To change the template for this generated type comment go to
 * Window - Preferences - Java - Code Style - Code Templates
 */
public interface UserProfilesDAO extends DAO {
	public List fetchDetails(String hostId,String userId,String profileId);
	public void saveuserProfileDetails(UserProfile userprofiles);
	public void updateuserProfileDetails(UserProfile userprofiles);
	public void deleteUserProfileDetails(UserProfile userprofiles);	
	public void updateuserProfileDetails(UserProfile userprofiles,Collection userProfileDetailsColl);
}
