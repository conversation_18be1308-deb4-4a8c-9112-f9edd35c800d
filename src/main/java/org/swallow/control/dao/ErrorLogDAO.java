/*
 * @(#)ErrorLogDAO.java 21/12/05
 * 
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 * 
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */


package org.swallow.control.dao;

import java.util.Collection;
import java.util.Date;
import java.util.List;
import org.swallow.control.model.ErrorLog;
import org.swallow.exception.SwtException;
import org.swallow.util.SystemFormats;

public interface ErrorLogDAO {
	/**
	 * @param fromDate
	 * @param toDate
	 * @return
	 * @throws SwtException
	 */
	public Collection getErrorLogList(String hostId,Date fromDate, Date toDate) throws SwtException;
	 /**
	  * @param fromDate
	  * @param toDate
	  * @return Collection
	  */
	public Collection getErrorLogList(String hostId,String userId , Date fromDate, Date toDate)
	throws SwtException;
	/**
	 * @param errorLog
	 */
	public void logError(ErrorLog errorLog);

	 /*-Start- Code modified for Defect No. 116 in Mantiss on 03/11/2007 - */
	 public int getErrorLogListUsingStoredProc(Date fromDate, Date toDate, int currentPage, int maxPage,
            List errorLogList, String filterSortStatus, SystemFormats formats) throws SwtException;
	 /*-End- Code modified for Defect No. 116 in Mantiss on 03/11/2007 - */
}
