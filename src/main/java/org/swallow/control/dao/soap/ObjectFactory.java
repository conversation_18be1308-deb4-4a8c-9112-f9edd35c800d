//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, vhudson-jaxb-ri-2.1-792 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2014.02.23 at 12:36:39 AM GMT+01:00 
//


package org.swallow.control.dao.soap;

import javax.xml.bind.JAXBElement;
import javax.xml.bind.annotation.XmlElementDecl;
import javax.xml.bind.annotation.XmlRegistry;
import javax.xml.namespace.QName;


/**
 * This object contains factory methods for each 
 * Java content interface and Java element interface 
 * generated in the org.swallow.source.inputengine.soap.xml.jaxb package. 
 * <p>An ObjectFactory allows you to programatically 
 * construct new instances of the Java representation 
 * for XML content. The Java representation of XML 
 * content can consist of schema derived interfaces 
 * and classes representing the binding of schema 
 * type definitions, element declarations and model 
 * groups.  Factory methods for each of these are 
 * provided in this class.
 * 
 */
@XmlRegistry
public class ObjectFactory {

    private final static QName _Heartbeat_QNAME = new QName("", "heartbeat");

    /**
     * Create a new ObjectFactory that can be used to create new instances of schema derived classes for package: org.swallow.source.inputengine.soap.xml.jaxb
     * 
     */
    public ObjectFactory() {
    }

    /**
     * Create an instance of {@link Heartbeat.Engine }
     * 
     */
    public Heartbeat.Engine createHeartbeatEngine() {
        return new Heartbeat.Engine();
    }

    /**
     * Create an instance of {@link Heartbeat }
     * 
     */
    public Heartbeat createHeartbeat() {
        return new Heartbeat();
    }

    /**
     * Create an instance of {@link Heartbeat.InterfaceInfo.Interface.Beans.Bean }
     * 
     */
    public Heartbeat.InterfaceInfo.Interface.Beans.Bean createHeartbeatInterfaceInfoInterfaceBeansBean() {
        return new Heartbeat.InterfaceInfo.Interface.Beans.Bean();
    }

    /**
     * Create an instance of {@link Heartbeat.InterfaceInfo.Interface.Beans }
     * 
     */
    public Heartbeat.InterfaceInfo.Interface.Beans createHeartbeatInterfaceInfoInterfaceBeans() {
        return new Heartbeat.InterfaceInfo.Interface.Beans();
    }

    /**
     * Create an instance of {@link Heartbeat.InterfaceInfo }
     * 
     */
    public Heartbeat.InterfaceInfo createHeartbeatInterfaceInfo() {
        return new Heartbeat.InterfaceInfo();
    }

    /**
     * Create an instance of {@link Heartbeat.InterfaceInfo.Interface.Messages }
     * 
     */
    public Heartbeat.InterfaceInfo.Interface.Messages createHeartbeatInterfaceInfoInterfaceMessages() {
        return new Heartbeat.InterfaceInfo.Interface.Messages();
    }

    /**
     * Create an instance of {@link Heartbeat.InterfaceInfo.Interface }
     * 
     */
    public Heartbeat.InterfaceInfo.Interface createHeartbeatInterfaceInfoInterface() {
        return new Heartbeat.InterfaceInfo.Interface();
    }

    /**
     * Create an instance of {@link Heartbeat.InterfaceInfo.Interface.Messages.Message }
     * 
     */
    public Heartbeat.InterfaceInfo.Interface.Messages.Message createHeartbeatInterfaceInfoInterfaceMessagesMessage() {
        return new Heartbeat.InterfaceInfo.Interface.Messages.Message();
    }

    /**
     * Create an instance of {@link Heartbeat.InterfaceInfo.Interface.FileMgr }
     * 
     */
    public Heartbeat.InterfaceInfo.Interface.FileMgr createHeartbeatInterfaceInfoInterfaceFileMgr() {
        return new Heartbeat.InterfaceInfo.Interface.FileMgr();
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link Heartbeat }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "", name = "heartbeat")
    public JAXBElement<Heartbeat> createHeartbeat(Heartbeat value) {
        return new JAXBElement<Heartbeat>(_Heartbeat_QNAME, Heartbeat.class, null, value);
    }

}
