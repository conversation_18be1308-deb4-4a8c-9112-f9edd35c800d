/*
 * Created on Dec 6, 2005
 *
 * To change the template for this generated file go to
 * Window - Preferences - Java - Code Style - Code Templates
 */
package org.swallow.model;

import org.swallow.model.BaseObject;

import java.util.Date;
import java.util.Hashtable;
/**
 * <AUTHOR>
 *
 * To change the template for this generated type comment go to
 * Window - Preferences - Java - Code Style - Code Templates
 */
public class UserProfileDetail extends BaseObject implements org.swallow.model.AuditComponent {
	
	private Integer leftX;
    private Integer leftY;
    private Integer width;
    private Integer height;
	private Date updateDate;
	private String updateUser;
	
	private Id id = new Id();
	private MenuItem menuItem = new MenuItem();
	private UserProfile userProfile = new UserProfile();
	
	public static Hashtable  logTable = new Hashtable();
	static {
		logTable.put("leftX","Distance from left");
		logTable.put("leftY","Distance from top");
		logTable.put("width","Screen Width");
		logTable.put("height","Screen Height");
			
	}
	
	
	public static class Id extends BaseObject{
		private String hostId;
		private String userId;
		private String profileId;
		private String menuItemId;
		private Long sequenceNumber;
		public Id() {}

		public Id(String hostId, String userId, String profileId, String menuItemId,Long sequenceNumber) {
			this.hostId = hostId;
			this.userId = userId;
			this.profileId = profileId;
			this.menuItemId = menuItemId;
			this.sequenceNumber = sequenceNumber;
		}
				
		
		/**
		 * @return Returns the hostId.
		 */
		public String getHostId() {
			return hostId;
		}
		/**
		 * @param hostId The hostId to set.
		 */
		public void setHostId(String hostId) {
			this.hostId = hostId;
		}
		/**
		 * @return Returns the menuItemId.
		 */
		public String getMenuItemId() {
			return menuItemId;
		}
		/**
		 * @param menuItemId The menuItemId to set.
		 */
		public void setMenuItemId(String menuItemId) {
			this.menuItemId = menuItemId;
		}
		/**
		 * @return Returns the profileId.
		 */
		public String getProfileId() {
			return profileId;
		}
		/**
		 * @param profileId The profileId to set.
		 */
		public void setProfileId(String profileId) {
			this.profileId = profileId;
		}
		/**
		 * @return Returns the userId.
		 */
		public String getUserId() {
			return userId;
		}
		/**
		 * @param userId The userId to set.
		 */
		public void setUserId(String userId) {
			this.userId = userId;
		}
		/**
		 * @return Returns the sequenceNumber.
		 */
		public Long getSequenceNumber() {
			return sequenceNumber;
		}
		/**
		 * @param sequenceNumber The sequenceNumber to set.
		 */
		public void setSequenceNumber(Long sequenceNumber) {
			this.sequenceNumber = sequenceNumber;
		}
}

	
	/**
	 * @return Returns the height.
	 */
	public Integer getHeight() {
		return height;
	}
	/**
	 * @param height The height to set.
	 */
	public void setHeight(Integer height) {
		this.height = height;
	}
	/**
	 * @return Returns the id.
	 */
	public Id getId() {
		return id;
	}
	/**
	 * @param id The id to set.
	 */
	public void setId(Id id) {
		this.id = id;
	}
	/**
	 * @return Returns the leftX.
	 */
	public Integer getLeftX() {
		return leftX;
	}
	/**
	 * @param leftX The leftX to set.
	 */
	public void setLeftX(Integer leftX) {
		this.leftX = leftX;
	}
	/**
	 * @return Returns the leftY.
	 */
	public Integer getLeftY() {
		return leftY;
	}
	/**
	 * @param leftY The leftY to set.
	 */
	public void setLeftY(Integer leftY) {
		this.leftY = leftY;
	}
	/**
	 * @return Returns the menuItem.
	 */
	public MenuItem getMenuItem() {
		return menuItem;
	}
	/**
	 * @param menuItem The menuItem to set.
	 */
	public void setMenuItem(MenuItem menuItem) {
		this.menuItem = menuItem;
	}
	/**
	 * @return Returns the updateDate.
	 */
	public Date getUpdateDate() {
		return updateDate;
	}
	/**
	 * @param updateDate The updateDate to set.
	 */
	public void setUpdateDate(Date updateDate) {
		this.updateDate = updateDate;
	}
	/**
	 * @return Returns the updateUser.
	 */
	public String getUpdateUser() {
		return updateUser;
	}
	/**
	 * @param updateUser The updateUser to set.
	 */
	public void setUpdateUser(String updateUser) {
		this.updateUser = updateUser;
	}
	/**
	 * @return Returns the userProfile.
	 */
	public UserProfile getUserProfile() {
		return userProfile;
	}
	/**
	 * @param userProfile The userProfile to set.
	 */
	public void setUserProfile(UserProfile userProfile) {
		this.userProfile = userProfile;
	}
	/**
	 * @return Returns the width.
	 */
	public Integer getWidth() {
		return width;
	}
	/**
	 * @param width The width to set.
	 */
	public void setWidth(Integer width) {
		this.width = width;
	}
}
