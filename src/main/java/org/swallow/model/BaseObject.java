package org.swallow.model;

import org.apache.commons.lang.builder.EqualsBuilder;
import org.apache.commons.lang.builder.HashCodeBuilder;
import org.apache.commons.lang.builder.ToStringBuilder;
import org.apache.commons.lang.builder.ToStringStyle;

import java.io.Serializable;


/**
 * Base class for Model objects.  This is basically for the toString, equals
 * and hashCode methods.
 *
 */
public class BaseObject implements Serializable,DatabaseListener {
	
    private boolean isUpdateUserNeedUpdated = true;
    private boolean isUpdateDateNeedUpdated = true;
//    private boolean isRequireAuthorisation = false;
    private String mainEventId = null;
    private boolean forceNoLogs = false;
    
	public String toString() {
        return ToStringBuilder.reflectionToString(this,
                ToStringStyle.MULTI_LINE_STYLE);
    }

    public boolean equals(Object o) {
        return EqualsBuilder.reflectionEquals(this, o);
    }

    public int hashCode() {
        return HashCodeBuilder.reflectionHashCode(this);
    }
	public void onAdd(){
		// default implementation , do nothing
	}
	public void onUpdate(Object oldObject, Object newObject){
		// default implementation , do nothing				
	}
	public void onDelete(){
		// default implementation , do nothing		
	}
	/**
	 * @return Returns the isUpdateDateNeedUpdated.
	 */
	public boolean isUpdateDateNeedUpdated() {
		return isUpdateDateNeedUpdated;
	}
	/**
	 * @param isUpdateDateNeedUpdated The isUpdateDateNeedUpdated to set.
	 */
	public void setUpdateDateNeedUpdated(boolean isUpdateDateNeedUpdated) {
		this.isUpdateDateNeedUpdated = isUpdateDateNeedUpdated;
	}
	/**
	 * @return Returns the isUpdateUserNeedUpdated.
	 */
	public boolean isUpdateUserNeedUpdated() {
		return isUpdateUserNeedUpdated;
	}
	/**
	 * @param isUpdateUserNeedUpdated The isUpdateUserNeedUpdated to set.
	 */
	public void setUpdateUserNeedUpdated(boolean isUpdateUserNeedUpdated) {
		this.isUpdateUserNeedUpdated = isUpdateUserNeedUpdated;
	}


	public String getMainEventId() {
		return mainEventId;
	}

	public void setMainEventId(String mainEventId) {
		this.mainEventId = mainEventId;
	}

	public boolean isForceNoLogs() {
		return forceNoLogs;
	}

	public void setForceNoLogs(boolean forceNoLogs) {
		this.forceNoLogs = forceNoLogs;
	}
}
