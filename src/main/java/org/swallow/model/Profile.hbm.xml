<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE hibernate-mapping PUBLIC
        "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
        "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">

<hibernate-mapping>

   <class name="org.swallow.model.Profile" table="S_PROFILE">

		<composite-id name="id" class="org.swallow.model.Profile$Id" unsaved-value="any">
			<key-property name="hostId" access="field" column="HOST_ID" />
	        <key-property name="profileId" access="field" column="PROFILE_ID"/>
		</composite-id>
		
		<property name="profileName" column="PROFILE_NAME" not-null="false"/>	
		<property name="updateDate" column="UPDATE_DATE" not-null="false"/>	
		<property name="updateUser" column="UPDATE_USER" not-null="false"/>

     </class>
</hibernate-mapping>
