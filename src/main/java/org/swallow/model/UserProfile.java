/*
 * Created on Dec 6, 2005
 *
 * TODO To change the template for this generated file go to
 * Window - Preferences - Java - Code Style - Code Templates
 */
package org.swallow.model;

import org.swallow.model.BaseObject;

import java.util.Date;
import java.util.Hashtable;
/**
 * <AUTHOR>
 *
 * TODO To change the template for this generated type comment go to
 * Window - Preferences - Java - Code Style - Code Templates
 */
public class UserProfile extends BaseObject implements org.swallow.model.AuditComponent{
	
	private String userProfileName;
	private String currentProfile;
	private Date updateDate;
	private String updateUser;
	private String overWriteFlag;
	private Id id = new Id();
	private Profile profile = new Profile();
	
	public static Hashtable  logTable = new Hashtable();
	static {
		logTable.put("userProfileName","User Profile Name");
		logTable.put("currentProfile","Current Profile");
			
	}
	
	public static class Id extends BaseObject{
		private String hostId;
		private String userId;
		private String profileId;
		

		public Id() {}

		public Id(String hostId, String userId, String profileId) {
			this.hostId = hostId;
			this.userId = userId;
			this.profileId = profileId;
		}
				
		
		/**
		 * @return Returns the hostId.
		 */
		public String getHostId() {
			return hostId;
		}
		/**
		 * @param hostId The hostId to set.
		 */
		public void setHostId(String hostId) {
			this.hostId = hostId;
		}
		/**
		 * @return Returns the profileId.
		 */
		public String getProfileId() {
			return profileId;
		}
		/**
		 * @param profileId The profileId to set.
		 */
		public void setProfileId(String profileId) {
			this.profileId = profileId;
		}
		/**
		 * @return Returns the userId.
		 */
		public String getUserId() {
			return userId;
		}
		/**
		 * @param userId The userId to set.
		 */
		public void setUserId(String userId) {
			this.userId = userId;
		}
	}

	/**
	 * @return Returns the currentProfile.
	 */
	public String getCurrentProfile() {
		return currentProfile;
	}
	/**
	 * @param currentProfile The currentProfile to set.
	 */
	public void setCurrentProfile(String currentProfile) {
		this.currentProfile = currentProfile;
	}
	/**
	 * @return Returns the id.
	 */
	public Id getId() {
		return id;
	}
	/**
	 * @param id The id to set.
	 */
	public void setId(Id id) {
		this.id = id;
	}
	/**
	 * @return Returns the profile.
	 */
	public Profile getProfile() {
		return profile;
	}
	/**
	 * @param profile The profile to set.
	 */
	public void setProfile(Profile profile) {
		this.profile = profile;
	}
	/**
	 * @return Returns the updateDate.
	 */
	public Date getUpdateDate() {
		return updateDate;
	}
	/**
	 * @param updateDate The updateDate to set.
	 */
	public void setUpdateDate(Date updateDate) {
		this.updateDate = updateDate;
	}
	/**
	 * @return Returns the updateUser.
	 */
	public String getUpdateUser() {
		return updateUser;
	}
	/**
	 * @param updateUser The updateUser to set.
	 */
	public void setUpdateUser(String updateUser) {
		this.updateUser = updateUser;
	}
	/**
	 * @return Returns the userProfileName.
	 */
	public String getUserProfileName() {
		return userProfileName;
	}
	/**
	 * @param userProfileName The userProfileName to set.
	 */
	public void setUserProfileName(String userProfileName) {
		this.userProfileName = userProfileName;
	}
	/**
	 * @return Returns the overWriteFlag.
	 */
	public String getOverWriteFlag() {
		return overWriteFlag;
	}
	/**
	 * @param overWriteFlag The overWriteFlag to set.
	 */
	public void setOverWriteFlag(String overWriteFlag) {
		this.overWriteFlag = overWriteFlag;
	}
}
