/*
 * Created on Dec 23, 2005
 *
 * TODO To change the template for this generated file go to
 * Window - Preferences - Java - Code Style - Code Templates
 */
package org.swallow.model;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 *
 * TODO To change the template for this generated type comment go to
 * Window - Preferences - Java - Code Style - Code Templates
 */
public class Program implements Serializable {
 private static final long serialVersionUID = 1L;
 private String programId;
 private String programName;
 private String programDesc;
 private Date updateDate;
 private String updateUser;

 /**
 * @return Returns the programDesc.
 */
public String getProgramDesc() {
	return programDesc;
}
/**
 * @param programDesc The programDesc to set.
 */
public void setProgramDesc(String programDesc) {
	this.programDesc = programDesc;
}
/**
 * @return Returns the programId.
 */
public String getProgramId() {
	return programId;
}
/**
 * @param programId The programId to set.
 */
public void setProgramId(String programId) {
	this.programId = programId;
}
/**
 * @return Returns the programName.
 */
public String getProgramName() {
	return programName;
}
/**
 * @param programName The programName to set.
 */
public void setProgramName(String programName) {
	this.programName = programName;
}
/**
 * @return Returns the updateDate.
 */
public Date getUpdateDate() {
	return updateDate;
}
/**
 * @param updateDate The updateDate to set.
 */
public void setUpdateDate(Date updateDate) {
	this.updateDate = updateDate;
}
/**
 * @return Returns the updateUser.
 */
public String getUpdateUser() {
	return updateUser;
}
/**
 * @param updateUser The updateUser to set.
 */
public void setUpdateUser(String updateUser) {
	this.updateUser = updateUser;
}
}
