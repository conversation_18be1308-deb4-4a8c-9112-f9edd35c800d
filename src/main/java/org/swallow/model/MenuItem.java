/*
 * Created on Dec 14, 2005
 *
 * TODO To change the template for this generated file go to
 * Window - Preferences - Java - Code Style - Code Templates
 */
package org.swallow.model;

import java.util.Collection;
import java.util.Date;


/**
 * <AUTHOR>
 *
 * TODO To change the template for this generated type comment go to
 * Window - Preferences - Java - Code Style - Code Templates
 */
public class MenuItem extends BaseObject implements Comparable {
 private String itemId;
 private String parentId;
 private String description;
 private Integer menuOrder;
 private Integer menuGroupOrder;
 private String programId;
 private String programName;
 private String imageName;
 private Integer width;
 private Integer height;
 private String disabledFlag;
 private Date updateDate;
 private String updateUser;
 private Collection subMenuList;
 private MenuItem menuItem;
 /* START: Code changed as par ING requirements to the Roles - Menu Access, 13-JUN-2007 */
 private String accessId;
 /* END: Code changed as par ING requirements to the Roles - Menu Access, 13-JUN-2007 */

 private Program program = new Program();

 public MenuItem(){
 }

 public MenuItem(String itemId, String parentId, String desc, String programId, Integer menuOrder){
 	this.itemId = itemId;
 	this.parentId = parentId;
 	this.description = desc;
 	this.programId = programId;
 	this.menuOrder = menuOrder;
 	/*if(programId == null){
 		subMenuList = new ArrayList();
 	}*/
 }

/**
 * @return Returns the program.
 */
public Program getProgram() {
	return program;
}
/**
 * @param program The program to set.
 */
public void setProgram(Program program) {
	this.program = program;
}
/**
 * @return Returns the description.
 */
public String getDescription() {
	return description;
}
/**
 * @param description The description to set.
 */
public void setDescription(String description) {
	this.description = description;
}
/**
 * @return Returns the itemId.
 */
public String getItemId() {
	return itemId;
}
/**
 * @param itemId The itemId to set.
 */
public void setItemId(String itemId) {
	this.itemId = itemId;
}
/**
 * @return Returns the parentId.
 */
public String getParentId() {
	return parentId;
}
/**
 * @param parentId The parentId to set.
 */
public void setParentId(String parentId) {
	this.parentId = parentId;
}
/**
 * @return Returns the subMenuList.
 */
public Collection getSubMenuList() {
	return subMenuList;
}
/**
 * @param subMenuList The subMenuList to set.
 */
public void setSubMenuList(Collection subMenuList) {
	this.subMenuList = subMenuList;
}

public boolean isParent(){
 return ((subMenuList == null || subMenuList.size()==0)? false : true);
}

/**
 * @return Returns the menuOrder.
 */
public Integer getMenuOrder() {
	return menuOrder;
}
/**
 * @param menuOrder The menuOrder to set.
 */
public void setMenuOrder(Integer menuOrder) {
	this.menuOrder = menuOrder;
}

/**
 * @return Returns the menuGroupOrder.
 */
public Integer getMenuGroupOrder() {
	return menuGroupOrder;
}
/**
 * @param menuGroupOrder The menuGroupOrder to set.
 */
public void setMenuGroupOrder(Integer menuGroupOrder) {
	this.menuGroupOrder = menuGroupOrder;
}

/**
 * @return Returns the disabledFlag.
 */
public String getDisabledFlag() {
	return disabledFlag;
}
/**
 * @param disabledFlag The disabledFlag to set.
 */
public void setDisabledFlag(String disabledFlag) {
	this.disabledFlag = disabledFlag;
}
/**
 * @return Returns the programId.
 */
public String getProgramId() {
	return programId;
}
/**
 * @param programId The programId to set.
 */
public void setProgramId(String programId) {
	this.programId = programId;
}

/**
 * @return Returns the programName.
 */
public String getProgramName() {
	return programName;
}
/**
 * @param programName The programName to set.
 */
public void setProgramName(String programName) {
	this.programName = programName;
}
/**
 * @return Returns the updateDate.
 */
public Date getUpdateDate() {
	return updateDate;
}
/**
 * @param updateDate The updateDate to set.
 */
public void setUpdateDate(Date updateDate) {
	this.updateDate = updateDate;
}
/**
 * @return Returns the updateUser.
 */
public String getUpdateUser() {
	return updateUser;
}
/**
 * @param updateUser The updateUser to set.
 */
public void setUpdateUser(String updateUser) {
	this.updateUser = updateUser;
}
/**
 * @return Returns the menuItem.
 */
public MenuItem getMenuItem() {
	return menuItem;
}
/**
 * @param menuItem The menuItem to set.
 */
public void setMenuItem(MenuItem menuItem) {
	this.menuItem = menuItem;
}
/**
 * @return Returns the height.
 */
public Integer getHeight() {
	return height;
}
/**
 * @param height The height to set.
 */
public void setHeight(Integer height) {
	this.height = height;
}
/**
 * @return Returns the width.
 */
public Integer getWidth() {
	return width;
}
/**
 * @param width The width to set.
 */
public void setWidth(Integer width) {
	this.width = width;
}
/**
 * @return Returns the imageName.
 */
public String getImageName() {
	return imageName;
}
/**
 * @param imageName The imageName to set.
 */
public void setImageName(String imageName) {
	this.imageName = imageName;
}
/* START: Code changed as par ING requirements to the Roles - Menu Access, 13-JUN-2007 */
/**
 * @return Returns the accessId.
 */
public String getAccessId() {
	return accessId;
}
/**
 * @param accessId The accessId to set.
 */
public void setAccessId(String accessId) {
	this.accessId = accessId;
}
/* END: Code changed as par ING requirements to the Roles - Menu Access, 13-JUN-2007 */

/**
 * <pre>
 * START: Added by RK on 10-Dec-2009 
 * Mantis issue-1084: Menu not populated on main screen when No Access on
 * Menu Item Work Flow Control 
 * </pre>
 */
/**
 * This method compares MenuItem objects based on menu item id
 * 
 * @param Object menuItem
 * @return int
 */
public int compareTo(Object menuItem) {
	//Get integer values
	int value1 = Integer.parseInt(this.itemId);
	int value2 = Integer.parseInt(((MenuItem) menuItem).itemId);
	// compare the values
	if (value1 < value2) {
		return -1;
	} else if (value1 > value2) {
		return 1;
	} else {
		return 0;
	}
}
/**
 * END: Added by RK on 10-Dec-2009 for mantis issue-1084
 */
}
