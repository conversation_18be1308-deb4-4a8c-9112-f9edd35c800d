<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE hibernate-mapping PUBLIC
        "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
        "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">

<hibernate-mapping>

   <class name="org.swallow.model.MenuAccess" table="P_MENU_ACCESS">

		<composite-id name="id" class="org.swallow.model.MenuAccess$Id" unsaved-value="any">
			<key-property name="roleId" access="field" column="ROLE_ID" />
	        <key-property name="itemId" access="field" column="MENU_ITEM_ID"/>
		</composite-id>
		
		<property name="accessId" column="ACCESS_ID" not-null="false"/>	
		<property name="updateDate" column="UPDATE_DATE" not-null="false"/>	
		<property name="updateUser" column="UPDATE_USER" not-null="false"/>

	<many-to-one lazy="false"
				name="menuItem"
				column="MENU_ITEM_ID"
				update="false"
				insert="false"
				cascade="none"
				class="org.swallow.model.MenuItem"
				not-null="true"
				outer-join="true"
				 not-found="ignore"
				foreign-key="FK_P_MENU_ACCESS_P_MENU_ITEM"/>

    </class>
</hibernate-mapping>
