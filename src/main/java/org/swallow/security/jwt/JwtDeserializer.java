package org.swallow.security.jwt;

import java.nio.charset.StandardCharsets;
import java.util.Map;

import org.json.JSONObject;

import io.jsonwebtoken.io.DeserializationException;
import io.jsonwebtoken.io.Deserializer;

public class JwtDeserializer  implements Deserializer<Map<String, ?>> {

    @Override
    public Map<String, ?> deserialize(byte[] bytes) throws DeserializationException {
        try {
            JSONObject jo = new JSONObject(new String(bytes, StandardCharsets.UTF_8));
            return jo.toMap();
        } catch (Exception e) {
            String msg = "Unable to deserialize bytes : " + e.getMessage();
            throw new DeserializationException(msg, e);
        }
    }
}