/*
 * @(#)SwtPBEConfig.java 1.0 12/06/09
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.security;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.jasypt.encryption.pbe.config.SimplePBEConfig;
import org.swallow.util.SwtConstants;


/**
 * <p>
 * Bean implementation for {@link SimplePBEConfig}. This class allows 
 * us to set Jasypt password in a hardcoded way
 * Also it offers as the possibility of encrypting and decrypting a password 
 * and decrypting passwords from config files...
 * 
 */

public final class SwtPBEConfig extends SimplePBEConfig {
	
	private static final Log log = LogFactory.getLog(SwtPBEConfig.class);  
    /**
     * <p>
     * Creates a new <tt>SwtPBEConfig</tt> instance.
     * </p>
     */
    public SwtPBEConfig() {
        super();
        log.debug(this.getClass().getName() + "- [SwtPBEConfig] - Entering ");
        this.setPassword(SwtConstants.PSWD_SEPARATOR);
        log.debug(this.getClass().getName() + "- [SwtPBEConfig] - Exiting ");
        }
	
	
}
