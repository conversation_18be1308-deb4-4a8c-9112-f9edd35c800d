/*
 * @(#)TurnoverReportDAO.java 1.0 31/07/2009
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */

package org.swallow.reports.dao;

import java.util.ArrayList;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import net.sf.jasperreports.engine.JasperPrint;

import org.swallow.dao.DAO;
import org.swallow.exception.SwtException;

/**
 * <AUTHOR> 
 * 
 * TurnoverReportDAO interface, contains methods to be used in
 * DAO layer.
 * 
 */
public interface TurnoverReportDAO extends DAO {

	/**
	 * This method is used to compile and return the reports
	 * @param request
	 * @param hostId
	 * @param entityId
	 * @param entityName
	 * @param reportFromDate
	 * @param reportToDate
	 * @param showMain
	 * @param showSub
	 * @param dataSource
	 * @param dataSourceName
	 * @param dateFormatValue
	 * @return
	 * @throws SwtException
	 */
	public JasperPrint getTurnoverReport(HttpServletRequest request,
			String hostId, String entityId, String entityName,
			String reportFromDate,String reportToDate, String showMain, String showSub, String dataSource, String dataSourceName , String dateFormatValue )
			throws SwtException;

	/**
	 * This method is used to generate data for the turnover excel report
	 * @param request
	 * @param hostId
	 * @param entityId
	 * @param entityName
	 * @param reportFromDate
	 * @param reportToDate
	 * @param showMain
	 * @param showSub
	 * @param dataSource
	 * @param dataSourceName
	 * @param dateFormatValue
	 * @return
	 * @throws SwtException
	 */
	public ArrayList<Map<String, Object>> getExcelTurnoverReportData(HttpServletRequest request, String hostId,
			String entityId, String entityName, String reportFromDate, String reportToDate, String showMain,
			String showSub, String dataSource, String dataSourceName, String dateFormatValue)throws SwtException;
}
