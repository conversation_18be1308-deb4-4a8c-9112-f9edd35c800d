/*
 * @(#)IntradayBalanceDAO.java 1.0 09/09/2008
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.reports.dao;

import java.util.Collection;

import javax.servlet.http.HttpServletRequest;

import net.sf.jasperreports.engine.JasperPrint;
import net.sf.jasperreports.engine.fill.JRSwapFileVirtualizer;

import org.swallow.dao.DAO;
import org.swallow.exception.SwtException;

/**
 * 
 * <AUTHOR> G IntradayBalanceDAO interface, contains collection of
 *         methods to be used in DAO layer.
 * 
 */

public interface IntradayBalanceDAO extends DAO {

	/**
	 * Get the distinct AccountId from p_intraday_stats table
	 * 
	 * @param String
	 *            hostId
	 * @param String
	 *            entityId
	 * @param String
	 *            currencyCode
	 * @return Collection object
	 */
	public Collection getMainAccountDetails(String hostId, String entityId,
			String currencyCode) throws SwtException;

	/**
	 * This method is used to compile and return the reports
	 * 
	 * @param request -
	 *            HttpServletRequest
	 * @param hostId -
	 *            String
	 * @param entityId -
	 *            String
	 * @param entityName -
	 *            String
	 * @param reportDate -
	 *            String
	 * @param accountId -
	 *            String
	 * @param currencyCode -
	 *            String
	 * @param currencyName -
	 *            String
	 * @param rawData -
	 *            String
	 * @param virtualizer -
	 *            JRSwapFileVirtualizer
	 * @return JasperPrint
	 * @throws SwtException
	 * 
	 */
	/*
	 * Start code: Code added for Mantis 942 by Betcy on 12-Jun-10 (add
	 * parameter virtualizer)
	 */
	public JasperPrint getIntradayBalancesReport(HttpServletRequest request,
			String hostId, String entityId, String entityName,
			String reportDate, String accountId, String currencyCode,
			String currencyName, String rawData,
			JRSwapFileVirtualizer virtualizer) throws SwtException;
	/*
	 * End code: Code added for Mantis 942 by Betcy on 12-Jun-10 (add
	 * parameter virtualizer)
	 */

}
