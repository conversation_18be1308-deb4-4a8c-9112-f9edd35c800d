package org.swallow.reports.dao.hibernate.ilmexcel;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

import org.swallow.exception.SwtErrorHandler;
import org.swallow.util.SwtUtil;
import org.swallow.util.UserThreadLocalHolder;


public class MultipleCurrenciesDailyLiquidity extends MultipleCurrencies{
	

	public static Map<String, Object> getBeansMap(HashMap<String, Object> allData) throws Exception{
		Map<String, Object> beans = new HashMap<String, Object>();
		ArrayList<ReportItem> throughputData = new ArrayList<MultipleCurrenciesDailyLiquidity.ReportItem>();
		ArrayList<Date> thrtimes = new ArrayList<Date>();

		List<HashMap<String, Object>> allccy = (LinkedList<HashMap<String, Object>>) allData.get("allccy");
		List<HashMap<String, Object>> mainDataDaily = null;
		ConcurrentHashMap<String, LinkedList<HashMap<String, Object>>> mainDataDailyList = null; 
		ConcurrentHashMap<String, LinkedList<HashMap<String, Object>>> mainDataAvg = null;
		LinkedList<String> ccyList = new LinkedList<String>();
		ReportItem th = null;
		boolean fillTimes = true;
		try{
		HashMap<String, String> StyleMap = new HashMap<String, String>();
		StyleMap = SwtUtil.getStyleMap();
		
		// Dictionary
		HashMap<String, String> dictionary = new HashMap<String, String>(){
			{
				put("report_title", SwtUtil.getMessageFromSession("ilmExcelReport.titleIntradayLiquidityManagementReport", UserThreadLocalHolder.getUserSession()));
				put("entity_id", SwtUtil.getMessageFromSession("ilmExcelReport.entity", UserThreadLocalHolder.getUserSession()));
				put("date_from", SwtUtil.getMessageFromSession("ilmExcelReport.fromDate", UserThreadLocalHolder.getUserSession()));
				put("date_end", SwtUtil.getMessageFromSession("ilmExcelReport.toDate", UserThreadLocalHolder.getUserSession())); 
				put("ccy_multiplier", SwtUtil.getMessageFromSession("ilmExcelReport.multiplier", UserThreadLocalHolder.getUserSession()));
				put("scenario", SwtUtil.getMessageFromSession("ilmExcelReport.scenario", UserThreadLocalHolder.getUserSession()));
				put("group", SwtUtil.getMessageFromSession("ilmExcelReport.group", UserThreadLocalHolder.getUserSession()));
				
				put("larg_pos_net_cum_pos", SwtUtil.getMessageFromSession("ilmExcelReport.largestPositiveNetCumulativePosition", UserThreadLocalHolder.getUserSession()));
				put("corr_time", SwtUtil.getMessageFromSession("ilmExcelReport.correspondingTime", UserThreadLocalHolder.getUserSession()));
				put("larg_neg_net_cum_pos", SwtUtil.getMessageFromSession("ilmExcelReport.largestNegativeNetCumulativePosition", UserThreadLocalHolder.getUserSession()));
				
				put("maximum_balance", SwtUtil.getMessageFromSession("ilmExcelReport.dailyLiqMaximumbalance", UserThreadLocalHolder.getUserSession()));
				put("minimum_balance", SwtUtil.getMessageFromSession("ilmExcelReport.dailyLiqMinimumBalance", UserThreadLocalHolder.getUserSession()));
				put("avg", SwtUtil.getMessageFromSession("ilmExcelReport.average", UserThreadLocalHolder.getUserSession()));
				put("hi", SwtUtil.getMessageFromSession("ilmExcelReport.high", UserThreadLocalHolder.getUserSession()));
				put("low", SwtUtil.getMessageFromSession("ilmExcelReport.low", UserThreadLocalHolder.getUserSession()));
				put("sc_hi", SwtUtil.getMessageFromSession("ilmExcelReport.secondHigh", UserThreadLocalHolder.getUserSession()));
				put("th_hi", SwtUtil.getMessageFromSession("ilmExcelReport.thirdHigh", UserThreadLocalHolder.getUserSession()));
				put("sc_lw", SwtUtil.getMessageFromSession("ilmExcelReport.secondLow", UserThreadLocalHolder.getUserSession()));
				put("th_lw", SwtUtil.getMessageFromSession("ilmExcelReport.thirdLow", UserThreadLocalHolder.getUserSession()));
				put("at", SwtUtil.getMessageFromSession("ilmExcelReport.at", UserThreadLocalHolder.getUserSession()));
				
				put("subtitle", SwtUtil.getMessageFromSession("ilmExcelReport.tabNameDailyLiquidity", UserThreadLocalHolder.getUserSession()));
			}
		};
		beans.put("dic", dictionary);
		beans.put("styles", StyleMap);
		// Main data
		SimpleDateFormat sdf = new SimpleDateFormat(pDateFormat);
		final Date   pValue_Date = sdf.parse(dateFrom);
		final Date   pValue_Date_End = sdf.parse(dateTo);
		
		// Header
		HashMap<String, Object> header = new HashMap<String, Object>(){
			{
				put("entity_id", pEntity_Id);
				put("entity_name", pEntityName);
				put("date_from",pValue_Date);
				put("date_to",pValue_Date_End);
				put("scenario",pSeriesIdentifier);
				put("scenarioDesc",pSeriesIdentifierDesc);
				put("group",pGroup);
				put("groupName",pGroupName);
				put("ccy_multiplier","Y".equals(pUseCcyMultiplier)?SwtUtil.getMessageFromSession("ilmExcelReport.ccyMultiplierEnabled", null):SwtUtil.getMessageFromSession("ilmExcelReport.ccyMultiplierDisabled", UserThreadLocalHolder.getUserSession()));
				put("comment","Poc for mantis 2787: ILM Report: Option to produce a report in Excel displaying multiple currencies");
			}
		};
		beans.put("header", header);
		
		// Throughput

		int i = 0;
		mainDataDailyList = (ConcurrentHashMap) allData.get("mainDataDailyList");
		for(HashMap<String, Object> rec:allccy){
			ccyList.add((String)rec.get("ccy"));
			th = new ReportItem((String)rec.get("ccy"));
			if("Y".equals(pUseCcyMultiplier)) {
				th.setCurrencyLabel((String)rec.get("ccy_multiplier_label"));
			}
			else { 
				th.setCurrencyLabel((String)rec.get("ccy"));
			}
			
			mainDataDaily = mainDataDailyList.get(rec.get("ccy"));
			for(HashMap<String, Object> rec2:mainDataDaily){
				String timeKey = rec2.get("pos_net_cum_d")!=null?rec2.get("pos_net_cum_h_d").toString():null;
				String valueString = rec2.get("pos_net_cum_v")!=null?rec2.get("pos_net_cum_v").toString():null;
				Double valueDouble = null;
				if(!SwtUtil.isEmptyOrNull(valueString))
					 valueDouble = Double.valueOf(valueString);
				if(rec2.get("max_ncp_threshold") != null  && valueDouble != null && Double.valueOf(""+rec2.get("max_ncp_threshold")) <  valueDouble)
					th.pushLargestpositive(timeKey, valueDouble , "orange", "white");
				else if(!"N".equals(rec2.get("is_holiday"))){
					th.pushLargestpositive(timeKey, valueDouble , "#D1D1D1", "white");
				}else {
					th.pushLargestpositive(timeKey, valueDouble);
				}
					
				timeKey = rec2.get("neg_net_cum_d")!=null?rec2.get("neg_net_cum_h_d").toString():null;
				valueString = rec2.get("neg_net_cum_v")!=null?rec2.get("neg_net_cum_v").toString():null;
				valueDouble = null;
				if(!SwtUtil.isEmptyOrNull(valueString))
					 valueDouble = Double.valueOf(valueString);
				
				if(rec2.get("min1_avlbl_assets_v") != null &&  valueDouble != null  && getValueOf(rec2.get("min1_avlbl_assets_v")) < -valueDouble)
					th.pushLargestnegative(timeKey, valueDouble , "red", "white");
				else if(rec2.get("min_ncp_threshold") != null && valueDouble != null &&  valueDouble < Double.valueOf(rec2.get("min_ncp_threshold").toString()))
					th.pushLargestnegative(timeKey, valueDouble , "orange", "white");
				else  if(!"N".equals(rec2.get("is_holiday"))){
					th.pushLargestnegative(timeKey, valueDouble , "#D1D1D1", "white");
				}else 
					th.pushLargestnegative(timeKey, valueDouble);
				
				timeKey = rec2.get("max_balance_h_d")!=null?rec2.get("max_balance_h_d").toString():null;
				
				if(!"N".equals(rec2.get("is_holiday"))){
					th.pushMaximumBalances(timeKey, getValueOf(rec2.get("max_balance_v")), "#D1D1D1", "white");
				}else {
					th.pushMaximumBalances(timeKey, getValueOf(rec2.get("max_balance_v")), "white", "black");
				}
				
				timeKey = rec2.get("min_balance_h_d")!=null?rec2.get("min_balance_h_d").toString():null;
				
				if(!"N".equals(rec2.get("is_holiday"))){
					th.pushMinimumBalances(timeKey, getValueOf(rec2.get("min_balance_v")), "#D1D1D1", "white");
				}else {
					th.pushMinimumBalances(timeKey, getValueOf(rec2.get("min_balance_v")), "white", "black");
				}
				
				// If first currency, fill in times
				if(fillTimes){
					thrtimes.add((Date)rec2.get("value_date"));
				}
			}
			throughputData.add(th);
			
			fillTimes = false;
			i++;
		}
		
		
		List<HashMap<String, Object>> mainDataAvgSingle = new LinkedList<HashMap<String, Object>>();

		HashMap<String, Object> data = null;
		for(HashMap<String, Object> recAll:allccy){
			data  = new HashMap<String, Object>();
			String realKey = null;
			String dateKey = null;
			LinkedList<HashMap<String, Object>> rankedList = new LinkedList<HashMap<String,Object>>();
			rankedList = mainDataDailyList.get(recAll.get("ccy"));
			for(HashMap<String, Object> rec3:rankedList){
			for (Map.Entry<String, Object> entry : rec3.entrySet()) {
				String key = entry.getKey();
				if(key.indexOf("asc") != -1 || key.indexOf("desc") != -1){
				    Double value = getValueOf(entry.getValue());
				    if(key.indexOf("asc") != -1 && (value == 1 ||  value == 2 || value == 3)) {
				    	realKey = key.substring(0, key.length() - 4);
				    	dateKey = key.substring(0, key.length() - 6)+"_d";
				    	Double valueOf = getValueOf(rec3.get(realKey));
				    	String time =(String) rec3.get(dateKey);
				    	data.put(realKey+"_min"+value.intValue(), valueOf);
				    	data.put(dateKey+"_min"+value.intValue(), time);

				    }
				    if(key.indexOf("desc") != -1 && (value == 1 ||  value == 2 || value == 3)) {
				    	realKey = key.substring(0, key.length() - 5);
				    	dateKey = key.substring(0, key.length() - 7)+"_d";
				    	Double valueOf = getValueOf(rec3.get(realKey));
				    	String time =(String) rec3.get(dateKey);
				    	data.put(realKey+"_max"+value.intValue(), valueOf);
				    	data.put(dateKey+"_max"+value.intValue(), time);
				    }
				}else if(key.indexOf("avg") != -1 ){
					Double valueOf = getValueOf(rec3.get(key));
					data.put(key, valueOf);
				}
			}
			if("Y".equals(pUseCcyMultiplier)) {
				data.put("currencyLabel",(String)rec3.get("ccy_multiplier_label"));
			}
			else { 
				data.put("currencyLabel",(String)rec3.get("ccy"));
			}
			}
			mainDataAvgSingle.add(data);
		}
		
		
		beans.put("largestData", throughputData);
		beans.put("ccyList", ccyList);
		beans.put("mainDataAvg", mainDataAvgSingle);
		beans.put("dates", thrtimes);
		}catch (Exception e){
			e.printStackTrace();
			throw SwtErrorHandler.getInstance().handleException(e,
					"getBeansMap",
					MultipleCurrenciesDailyLiquidity.class);
		}finally {
			throughputData =  null;
			thrtimes = null;
			allccy = null;
			mainDataDaily = null;
			mainDataDailyList = null;
			mainDataAvg = null;
			ccyList = null;
		}
		return beans;
	}
	
	protected static Double getValueOf(Object tmp) throws Exception{
		if(tmp != null && tmp.toString().length() > 0) {
			return Double.parseDouble(tmp.toString());
		}
		else 
			return null;
	}
	
	
	public static class ReportItem{
		private String currency;
		private String currencyLabel;
		private Double minThreshold;
		private ArrayList<String> dates;
		private ArrayList<ValueTime> largesetpositive= new ArrayList<ValueTime>();
		private ArrayList<ValueTime> largesetnegative = new ArrayList<ValueTime>();
		
		private ArrayList<String> largesetpositivetime= new ArrayList<String>();
		private ArrayList<String> largesetnegativetime = new ArrayList<String>();
		
		private ArrayList<ValueTime> maximumBalances= new ArrayList<ValueTime>();
		private ArrayList<ValueTime> minimumBalances = new ArrayList<ValueTime>();
		
		
		
		
		
		public ArrayList<ValueTime> getMaximumBalances() {
			return maximumBalances;
		}

		public void setMaximumBalances(ArrayList<ValueTime> maximumBalances) {
			this.maximumBalances = maximumBalances;
		}

		public ArrayList<ValueTime> getMinimumBalances() {
			return minimumBalances;
		}

		public void setMinimumBalances(ArrayList<ValueTime> minimumBalances) {
			this.minimumBalances = minimumBalances;
		}

		public ArrayList<String> getLargesetpositivetime() {
			return largesetpositivetime;
		}

		public void setLargesetpositivetime(ArrayList<String> largesetpositivetime) {
			this.largesetpositivetime = largesetpositivetime;
		}

		public ArrayList<String> getLargesetnegativetime() {
			return largesetnegativetime;
		}

		public void setLargesetnegativetime(ArrayList<String> largesetnegativetime) {
			this.largesetnegativetime = largesetnegativetime;
		}

		public ArrayList<ValueTime> getLargesetpositive() {
			return largesetpositive;
		}

		public void setLargesetpositive(ArrayList<ValueTime> largesetpositive) {
			this.largesetpositive = largesetpositive;
		}

		public ArrayList<ValueTime> getLargesetnegative() {
			return largesetnegative;
		}

		public void setLargesetnegative(ArrayList<ValueTime> largesetnegative) {
			this.largesetnegative = largesetnegative;
		}

		public void setDates(ArrayList<String> dates) {
			this.dates = dates;
		}
		public ArrayList<String> getDates() {
			return dates;
		}

		
		public ReportItem(String currency) {
			this.currency = currency;
		}
		
		public void pushDate(String value){
			if(dates==null)
				dates = new ArrayList<String>();
			dates.add(value);
		}
		public void pushLargestpositive(String time, Double value, String backgroundColor, String fontColor){
			if(largesetpositive==null)
				largesetpositive = new ArrayList<ValueTime>();
			largesetpositive.add(new ValueTime(time, value, backgroundColor, fontColor));
		}
		
		public void pushLargestpositive(String time, Double value){
			if(largesetpositive==null)
				largesetpositive = new ArrayList<ValueTime>();
			largesetpositive.add(new ValueTime(time, value, "white", "black"));
		}
		
		public void pushLargestnegative(String time, Double value, String backgroundColor, String fontColor){
			if(largesetnegative==null)
				largesetnegative = new ArrayList<ValueTime>();
			largesetnegative.add(new ValueTime(time, value, backgroundColor, fontColor));
		}
		
		public void pushLargestnegative(String time, Double value){
			if(largesetnegative==null)
				largesetnegative = new ArrayList<ValueTime>();
			largesetnegative.add(new ValueTime(time, value, "white", "black"));
		}
		
		public void pushLargestpositiveTime(String value){
			if(largesetpositivetime==null)
				largesetpositivetime = new ArrayList<String>();
			largesetpositivetime.add(value);
		}
		public void pushLargestnegativeTime(String value){
			if(largesetnegativetime==null)
				largesetnegativetime = new ArrayList<String>();
			largesetnegativetime.add(value);
		}
		
		
		public void pushMaximumBalances(String time, Double value, String backgroundColor, String fontColor){
			if(maximumBalances==null)
				maximumBalances = new ArrayList<ValueTime>();
			maximumBalances.add(new ValueTime(time, value, backgroundColor, fontColor));
		}
		
		
		public void pushMinimumBalances(String time, Double value, String backgroundColor, String fontColor){
			if(minimumBalances==null)
				minimumBalances = new ArrayList<ValueTime>();
			minimumBalances.add(new ValueTime(time, value, backgroundColor, fontColor));
		}
		
		
		
		public String getCurrency() {
			return currency;
		}
		public void setCurrency(String currency) {
			this.currency = currency;
		}
		
		public Double getMinThreshold() {
			return minThreshold;
		}

		public void setMinThreshold(Double minThreshold) {
			this.minThreshold = minThreshold;
		}

		public String getCurrencyLabel() {
			return currencyLabel;
		}

		public void setCurrencyLabel(String currencyLabel) {
			this.currencyLabel = currencyLabel;
		}

		public static class ValueTime{
			private String time = null;
			private Double value = null;
			private String backGroundColor = null;
			private String fontColor = null;
			public String getTime() {
				return time;
			}
			public void setTime(String time) {
				this.time = time;
			}
			public Double getValue() {
				return value;
			}
			public void setValue(Double value) {
				this.value = value;
			}
			public ValueTime(String time, Double value, String backGroundColor, String fontColor) {
				super();
				this.time = time;
				this.value = value;
				this.backGroundColor = backGroundColor;
				this.fontColor = fontColor;
			}
			public String getBackGroundColor() {
				return backGroundColor;
			}
			public void setBackGroundColor(String backGroundColor) {
				this.backGroundColor = backGroundColor;
			}
			public String getFontColor() {
				return fontColor;
			}
			public void setFontGroundColor(String fontColor) {
				this.fontColor = fontColor;
			}
			
			
		}
	}
}
