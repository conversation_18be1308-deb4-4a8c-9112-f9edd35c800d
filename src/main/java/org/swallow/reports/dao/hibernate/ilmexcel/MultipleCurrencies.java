package org.swallow.reports.dao.hibernate.ilmexcel;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

import org.apache.poi.ss.usermodel.Workbook;
import org.swallow.exception.SwtErrorHandler;
import org.swallow.util.SwtUtil;
import org.swallow.util.UserThreadLocalHolder;



public class MultipleCurrencies {
	public static SimpleDateFormat sdf = null;
	// Main data
	public static String pHost_Id = null;
	public static String pEntity_Id = null;
	public static String pEntityName = null;
	public static String pCurrency_Code = null;
	public static String pSeriesIdentifier = null;
	public static String pSeriesIdentifierDesc = null;
	public static boolean isGlobalGroup = false;
	public static String pGroup = null;
	public static String pGroupName = null;
	public static String pRoleId = null;
	public static String pDBLink = null;
	public static String dateFrom = null;
	public static String dateTo = null;
	public static boolean isDateRange = true;
	public static boolean isShowAllCritPayment = true;
	public static Workbook workbook = null;
	
	public static String pDateFormat = null;
	public static String pUseCcyMultiplier = null;
	public static String inflowOrOutlfow = null;

	public static Map<String, Object> getBeansMap(HashMap<String, Object> allData) throws Exception{
		Map<String, Object> beans = new HashMap<String, Object>();
		ConcurrentHashMap<String ,HashMap<String, Object>> mainDataAvg = (ConcurrentHashMap) allData.get("mainDataAvg");
		List<HashMap<String, Object>> mainDataAvgSingle = null;
		LinkedList<HashMap<String, Object>> allccy = (LinkedList<HashMap<String, Object>>) allData.get("allccy");
		
		//Temp variables for each record
		HashMap<String, Double> outgoingPayement = new HashMap<String, Double>();
		HashMap<String, Double> incomingPayement = new HashMap<String, Double>();
		HashMap<String, Double> sourceOfLiquidity = new HashMap<String, Double>();
		HashMap<String, Double> maxCritOutflow = new HashMap<String, Double>();
		HashMap<String, Double> maxCritInflow = new HashMap<String, Double>();
		// Currency list
		LinkedList<String> ccyList = new LinkedList<String>();
		// Thresholds
		Double min_ncp_threshold = null;
		Double max_ncp_threshold = null;
		
		//-------------//
		HashMap<String, String> criticalPayTypesCategory = new HashMap<String, String>(){
			{
				put("V", SwtUtil.getMessageFromSession("ilmExcelReport.veryCritical", UserThreadLocalHolder.getUserSession()));
				put("C", SwtUtil.getMessageFromSession("ilmExcelReport.critical", UserThreadLocalHolder.getUserSession()));
				put("N",SwtUtil.getMessageFromSession("ilmExcelReport.other", UserThreadLocalHolder.getUserSession()));
			}
		};
		
		HashMap<String, String> StyleMap = new HashMap<String, String>();
		StyleMap = SwtUtil.getStyleMap();
		
		// Throughput
		LinkedList<HashMap<String, Object>> throughput = null;
		LinkedList<HashMap<String, Object>> assets = new LinkedList<HashMap<String,Object>>();
		LinkedList<HashMap<String, Object>> criticalPayementOutflowAvgData = new LinkedList<HashMap<String,Object>>();
		LinkedList<HashMap<String, Object>> criticalPayementInflowAvgData = new LinkedList<HashMap<String,Object>>();
		ArrayList<IntradayThroughput> throughputData = new ArrayList<MultipleCurrencies.IntradayThroughput>();
		ArrayList<String> thrtimes = new ArrayList<String>();
		ArrayList<ReportItem> availbleLiquidityAvg = new ArrayList<ReportItem>();
		String criticalPaymentCategory = null;
		String criticalPayementTypeDesc = null;
		LinkedList<CriticalPayementDetails> critcalListHashmap = new LinkedList<CriticalPayementDetails>();
		CriticalPayementDetails criticalDetails  = null;
		LinkedList<CurrencyCriticalDetails> dataForReportOutflow = new LinkedList<CurrencyCriticalDetails>();
		LinkedList<CurrencyCriticalDetails> dataForReportInflow = new LinkedList<CurrencyCriticalDetails>();
		LinkedList<CriticalDetailsList> criticalDetailsData = new LinkedList<CriticalDetailsList>();
		CriticalDetailsList criticalPayement  = null;
		LinkedList<Category> categoryList = new LinkedList<Category>();
		LinkedList<String> subcategoryList = new LinkedList<String>();
		
		
		ConcurrentHashMap<String, LinkedList<HashMap<String, Object>>> mainDataAvgList = (ConcurrentHashMap) allData.get("availableLiqAvgList");
		ConcurrentHashMap<String, LinkedList<HashMap<String, Object>>> throughputList = (ConcurrentHashMap) allData.get("throughputList");
		ConcurrentHashMap<String, LinkedList<HashMap<String, Object>>> assetsList = (ConcurrentHashMap) allData.get("assetsList");
		ConcurrentHashMap<String, LinkedList<HashMap<String, Object>>> criticalPayementOutflowAvgDataList = (ConcurrentHashMap) allData.get("criticalPayementAvgOutflowDataList");
		ConcurrentHashMap<String, LinkedList<HashMap<String, Object>>> criticalPayementInflowAvgDataList = (ConcurrentHashMap) allData.get("criticalPayementAvgInflowDataList");
		
		CurrencyCriticalDetails ccyCriticalPayement = null;
		
		
		ArrayList<String> assetsNames = new ArrayList<String>();
		//---------------//
		Double avg_external_sod = null;
		Double avg_collateral = null;
		Double avgbob = null;
		Double avgosc = null;
		Double avg_credit_line_secured = null;
		Double avg_credit_line_total = null;
		Double avg_credit_line_committed = null;
		Double avg_unencumbered_liquid_assets = null;
		Double avg_other_total = null;
		Double cp_catg_total = null;
		Double cp_grand_total = null;
		try{
			// Dictionary
			HashMap<String, String> dictionary = new HashMap<String, String>(){
				{
					put("report_title", SwtUtil.getMessageFromSession("ilmExcelReport.titleIntradayLiquidityManagementReport", UserThreadLocalHolder.getUserSession()));
					put("entity_id", SwtUtil.getMessageFromSession("ilmExcelReport.entity", UserThreadLocalHolder.getUserSession()));
					put("date_from", SwtUtil.getMessageFromSession("ilmExcelReport.fromDate", UserThreadLocalHolder.getUserSession()));
					put("date", SwtUtil.getMessageFromSession("ilmExcelReport.date", UserThreadLocalHolder.getUserSession()));
					put("date_end", SwtUtil.getMessageFromSession("ilmExcelReport.toDate", UserThreadLocalHolder.getUserSession()));
					put("ccy_multiplier", SwtUtil.getMessageFromSession("ilmExcelReport.multiplier", UserThreadLocalHolder.getUserSession()));
					put("scenario", SwtUtil.getMessageFromSession("ilmExcelReport.scenario", UserThreadLocalHolder.getUserSession()));
					/*put("party_info", SwtUtil.getMessageFromSession("ilmExcelReport.partyInfo", UserThreadLocalHolder.getUserSession()));
					put("first_largest_counter_party", SwtUtil.getMessageFromSession("ilmExcelReport.firstLargestCounterParty", UserThreadLocalHolder.getUserSession()));
					put("second_largest_counter_party", SwtUtil.getMessageFromSession("ilmExcelReport.secondLargestCounterParty", UserThreadLocalHolder.getUserSession()));
					put("third_largest_counter_party", SwtUtil.getMessageFromSession("ilmExcelReport.thirdLargestCounterParty", UserThreadLocalHolder.getUserSession()));
					put("first_largest_customer", SwtUtil.getMessageFromSession("ilmExcelReport.firstLargestCustomer", UserThreadLocalHolder.getUserSession()));
					put("second_largest_customer", SwtUtil.getMessageFromSession("ilmExcelReport.secondLargestCustomer", UserThreadLocalHolder.getUserSession()));
					put("third_largest_customer", SwtUtil.getMessageFromSession("ilmExcelReport.thirdLargestCustomer", UserThreadLocalHolder.getUserSession()));*/
					put("group", SwtUtil.getMessageFromSession("ilmExcelReport.group", UserThreadLocalHolder.getUserSession()));
					put("daily_maximum_liq_usage", SwtUtil.getMessageFromSession("ilmExcelReport.dailyMaximumLiquidityUsage", UserThreadLocalHolder.getUserSession()));
					put("largestpositive", SwtUtil.getMessageFromSession("ilmExcelReport.largestPositiveNetCumulativePosition", UserThreadLocalHolder.getUserSession()));
					put("corr_time", SwtUtil.getMessageFromSession("ilmExcelReport.correspondingTime", UserThreadLocalHolder.getUserSession()));
					put("thr", SwtUtil.getMessageFromSession("ilmExcelReport.threshold", UserThreadLocalHolder.getUserSession()));
					put("largestnegative", SwtUtil.getMessageFromSession("ilmExcelReport.largestNegativeNetCumulativePosition", UserThreadLocalHolder.getUserSession()));
					put("tot_payements", SwtUtil.getMessageFromSession("ilmExcelReport.totalPayments", UserThreadLocalHolder.getUserSession()));
				
					put("actuals_payements", SwtUtil.getMessageFromSession("ilmExcelReport.actuals", UserThreadLocalHolder.getUserSession()));
					put("unsettled_payements", SwtUtil.getMessageFromSession("ilmExcelReport.unsettled", UserThreadLocalHolder.getUserSession()));
					put("unsettled_credit_payements", SwtUtil.getMessageFromSession("ilmExcelReport.unsettledCredits", UserThreadLocalHolder.getUserSession()));
					put("unsettled_debit_payements", SwtUtil.getMessageFromSession("ilmExcelReport.unsettledDebits", UserThreadLocalHolder.getUserSession()));
					put("net_credit_debit_payements", SwtUtil.getMessageFromSession("ilmExcelReport.netCreditDebit", UserThreadLocalHolder.getUserSession()));
					
					put("day_tot_inflows", SwtUtil.getMessageFromSession("ilmExcelReport.summaryDailyTotalInflow", UserThreadLocalHolder.getUserSession()));
					put("day_tot_outflows", SwtUtil.getMessageFromSession("ilmExcelReport.summaryDailyTotalOutflow", UserThreadLocalHolder.getUserSession()));
					put("net_inflows_outflow", SwtUtil.getMessageFromSession("ilmExcelReport.summaryNetInflowsOutflows", UserThreadLocalHolder.getUserSession()));
					put("unconf_bal", SwtUtil.getMessageFromSession("ilmExcelReport.unconfirmedBalance", UserThreadLocalHolder.getUserSession()));
					put("avai_intra_liq", SwtUtil.getMessageFromSession("ilmExcelReport.availableIntradayLiquidity", UserThreadLocalHolder.getUserSession()));
					put("src_intr_liq_ex_inc", SwtUtil.getMessageFromSession("ilmExcelReport.sourcesOfIntradayLiquidityExclIncomingPayments", UserThreadLocalHolder.getUserSession()));
					put("cent_bank_bal", SwtUtil.getMessageFromSession("ilmExcelReport.centralBankBalance", UserThreadLocalHolder.getUserSession()));
					put("cent_bank_coll", SwtUtil.getMessageFromSession("ilmExcelReport.centralBankCollateral", UserThreadLocalHolder.getUserSession()));
					put("bal_oth_banks", SwtUtil.getMessageFromSession("ilmExcelReport.balancesOtherBanks", UserThreadLocalHolder.getUserSession()));
					put("bal", SwtUtil.getMessageFromSession("ilmReport.balances", UserThreadLocalHolder.getUserSession()));
					put("oth_sys_coll", SwtUtil.getMessageFromSession("ilmExcelReport.otherSystemsCollateral", UserThreadLocalHolder.getUserSession()));
					put("coll", SwtUtil.getMessageFromSession("ilmReport.collateral", UserThreadLocalHolder.getUserSession()));
					put("tot_cre_lin", SwtUtil.getMessageFromSession("ilmExcelReport.totalCreditLines", UserThreadLocalHolder.getUserSession()));
					put("ofw", SwtUtil.getMessageFromSession("ilmExcelReport.ofWhich", UserThreadLocalHolder.getUserSession()));
					put("crd_lin_scr", SwtUtil.getMessageFromSession("ilmExcelReport.creditLinesSecured", UserThreadLocalHolder.getUserSession()));
					put("crd_lin_com", SwtUtil.getMessageFromSession("ilmExcelReport.creditLinesCommitted", UserThreadLocalHolder.getUserSession()));
					put("unc_liq_ass", SwtUtil.getMessageFromSession("ilmExcelReport.unencumberedLiquidAssets", UserThreadLocalHolder.getUserSession()));
					put("oth", SwtUtil.getMessageFromSession("ilmExcelReport.other", UserThreadLocalHolder.getUserSession()));
					put("gold", SwtUtil.getMessageFromSession("ilmExcelReport.gold", UserThreadLocalHolder.getUserSession()));
					put("cent_bank_coll_blck_for_crit_pay", SwtUtil.getMessageFromSession("ilmExcelReport.centralBankCollateralBlockedForCriticalPayments", UserThreadLocalHolder.getUserSession()));
					put("tot_avai_intr_liq_inc_incom", SwtUtil.getMessageFromSession("ilmExcelReport.totalAvailableIntradayLiquidityInclIncomingPayments", UserThreadLocalHolder.getUserSession()));
					put("risk_appetite", SwtUtil.getMessageFromSession("ilmExcelReport.riskAppetite", UserThreadLocalHolder.getUserSession()));
					put("outg_paym_com_avai_intr_liq", SwtUtil.getMessageFromSession("ilmExcelReport.outgoingPaymentsComparedToAvailableIntradayLiquidity", UserThreadLocalHolder.getUserSession()));
					put("as_of_total_avai_liq", SwtUtil.getMessageFromSession("ilmExcelReport.asOfTotalAvailableLiquidity", UserThreadLocalHolder.getUserSession()));
					put("as_of_avai_liq_exl_incom", SwtUtil.getMessageFromSession("ilmExcelReport.asOfAvailableLiquidityExclIncomingPayments", UserThreadLocalHolder.getUserSession()));
					put("intr_throuh", SwtUtil.getMessageFromSession("ilmExcelReport.intradayThroughput", UserThreadLocalHolder.getUserSession()));
					put("inflows", SwtUtil.getMessageFromSession("ilmExcelReport.intradayThroughputInflowsCcyTimeframe", UserThreadLocalHolder.getUserSession()));
					put("outflows", SwtUtil.getMessageFromSession("ilmExcelReport.intradayThroughputOutflowsCcyTimeframe", UserThreadLocalHolder.getUserSession()));
					put("crit_outg_paym", SwtUtil.getMessageFromSession("ilmExcelReport.criticalOutgoingPayments", UserThreadLocalHolder.getUserSession()));
					put("tot_crit_paym_outflows", SwtUtil.getMessageFromSession("ilmExcelReport.totalCriticalPaymentsOutflows", UserThreadLocalHolder.getUserSession()));
					put("as_of_day_tot_outflow", SwtUtil.getMessageFromSession("ilmExcelReport.asOfDailyTotalOutflow", UserThreadLocalHolder.getUserSession()));
					put("as_of_inc_paym", SwtUtil.getMessageFromSession("ilmExcelReport.asOfDailyTotalInflow", UserThreadLocalHolder.getUserSession()));
					put("very_crit_paym", SwtUtil.getMessageFromSession("ilmExcelReport.veryCriticalPayments", UserThreadLocalHolder.getUserSession()));
					put("oth_crit_paym", SwtUtil.getMessageFromSession("ilmExcelReport.otherCriticalPayments", UserThreadLocalHolder.getUserSession()));
					put("crit_inc_paym", SwtUtil.getMessageFromSession("ilmExcelReport.criticalIncomingPayments", UserThreadLocalHolder.getUserSession()));
					put("tot_crit_paym_inflows", SwtUtil.getMessageFromSession("ilmExcelReport.totalCriticalPaymentsInflows", UserThreadLocalHolder.getUserSession()));
					put("summary_balance", SwtUtil.getMessageFromSession("ilmExcelReport.summaryBalance", UserThreadLocalHolder.getUserSession()));
					put("summary_maximum_balance", SwtUtil.getMessageFromSession("ilmExcelReport.summaryMaximumbalance", UserThreadLocalHolder.getUserSession()));
					put("summary_minimum_balance", SwtUtil.getMessageFromSession("ilmExcelReport.summaryMinimumBalance", UserThreadLocalHolder.getUserSession()));
					put("unmonitored_types", SwtUtil.getMessageFromSession("ilmExcelReport.unmonitoredTypes", UserThreadLocalHolder.getUserSession()));
					put("monitored_types", SwtUtil.getMessageFromSession("ilmExcelReport.monitoredTypes", UserThreadLocalHolder.getUserSession()));
					put("standard", SwtUtil.getMessageFromSession("ilmExcelReport.standard", UserThreadLocalHolder.getUserSession()));
					put("currencyGlobalGroup", SwtUtil.getMessageFromSession("ilmExcelReport.currencyGlobalGroup", UserThreadLocalHolder.getUserSession()));
					put("throughput_at", SwtUtil.getMessageFromSession("ilmExcelReport.throughputAt", UserThreadLocalHolder.getUserSession()));
					if(!isDateRange){
						put("subtitle", SwtUtil.getMessageFromSession("scenarioAdvanced.summary", UserThreadLocalHolder.getUserSession())+(!"Standard".equals(pSeriesIdentifier)?" "+pSeriesIdentifier:""));
					}else {
						put("subtitle", SwtUtil.getMessageFromSession("ilmExcelReport.tabNameAverageSummary", UserThreadLocalHolder.getUserSession())+(!"Standard".equals(pSeriesIdentifier)?" "+pSeriesIdentifier:""));
					}
	
				}
						
						
			};
			beans.put("dic", dictionary);
			beans.put("styles", StyleMap);
			// Format dates
			SimpleDateFormat sdf = new SimpleDateFormat(pDateFormat);
			final Date   pValue_Date = sdf.parse(dateFrom);
			final Date   pValue_Date_End = sdf.parse(dateTo);
			
			// Header values
			HashMap<String, Object> header = new HashMap<String, Object>(){
				{
					put("entity_id", pEntity_Id);
					put("entity_name", pEntityName);
					put("date_from",pValue_Date);
					put("date_to",pValue_Date_End);
					put("scenario",pSeriesIdentifier);
					put("scenarioDesc",pSeriesIdentifierDesc);
					put("group",pGroup);
					put("groupName",pGroupName);
					put("isGlobalGroup",isGlobalGroup);
					put("ccy_multiplier","Y".equals(pUseCcyMultiplier)?SwtUtil.getMessageFromSession("ilmExcelReport.ccyMultiplierEnabled", null):SwtUtil.getMessageFromSession("ilmExcelReport.ccyMultiplierDisabled", UserThreadLocalHolder.getUserSession()));
					put("isDateRange",isDateRange);
					put("isShowAllCritPayment",isShowAllCritPayment);
				}
			};
			beans.put("header", header);
			
			// Variables containing data from queries
			
			int h = 0;
			mainDataAvgSingle = new LinkedList<HashMap<String, Object>>();
			// Iterate main data report
			for(HashMap<String, Object> recAll:allccy){
				HashMap<String, Object> rec = mainDataAvg.get(recAll.get("ccy"));
				// Extract NCP threshold for coloration
				min_ncp_threshold = getValueOf(rec.get("min_ncp_threshold"));
				max_ncp_threshold = getValueOf(rec.get("max_ncp_threshold"));
				
				if(!isDateRange){
					//Extract outflow, inflow, assets, critical outflow and critical inflow values to use them later for percentages
					outgoingPayement.put((String)rec.get("ccy"), getValueOf(rec.get("max1_day_acc_outflow_v")));
					incomingPayement.put((String)rec.get("ccy"), getValueOf(rec.get("max1_day_acc_inflow_v")));
					sourceOfLiquidity.put((String)rec.get("ccy"), getValueOf(rec.get("min1_avlbl_assets_v")));
					maxCritOutflow.put((String)rec.get("ccy"), getValueOf(rec.get("max1_day_acc_crit_outflow_v")));
					maxCritInflow.put((String)rec.get("ccy"), getValueOf(rec.get("max1_day_acc_crit_inflow_v")));
					
					String time =(String) rec.get("min1_balance_d");
					if (!SwtUtil.isEmptyOrNull(time)){
						time = time.substring(time.length() -5 , time.length());
					}
					rec.put("min1_balance_d", time);
					
					time =(String) rec.get("max1_balance_d");
					if (!SwtUtil.isEmptyOrNull(time)){
						time = time.substring(time.length() -5 , time.length());
					}
					rec.put("max1_balance_d", time);
					// Calculate the netflow value which is inflow - outflow
					if(getValueOf(rec.get("max1_day_acc_inflow_v")) != null && getValueOf(rec.get("max1_day_acc_outflow_v")) != null)
						rec.put("max1_day_acc_netflow_v", getValueOf(rec.get("max1_day_acc_inflow_v")) - getValueOf(rec.get("max1_day_acc_outflow_v")));
					else
						rec.put("max1_day_acc_netflow_v", null);
					
					// Colorate 
					if(max_ncp_threshold != null){
						if(max_ncp_threshold != null && getValueOf(rec.get("max1_pos_net_cum_v")) != null && max_ncp_threshold <  getValueOf(rec.get("max1_pos_net_cum_v"))){
							rec.put("pncp_font_color",  "white" );
							rec.put("pncp_bg_color",  "orange" );
						}
						else { 
							rec.put("pncp_font_color",  "black" );
							rec.put("pncp_bg_color",  "white" );
						}
					}else {
						rec.put("pncp_font_color",  "black" );
						rec.put("pncp_bg_color",  "white" );
					}
					

					if(getValueOf(rec.get("min1_avlbl_assets_v")) != null &&  getValueOf(rec.get("max1_neg_net_cum_v")) != null  && getValueOf(rec.get("min1_avlbl_assets_v")) < Math.abs(getValueOf(rec.get("max1_neg_net_cum_v")))){
						rec.put("nncp_font_color",  "white" );
						rec.put("nncp_bg_color",  "red" );
					}
					else if(min_ncp_threshold != null){
						if(min_ncp_threshold != null && getValueOf(rec.get("max1_neg_net_cum_v")) != null && min_ncp_threshold >  getValueOf(rec.get("max1_neg_net_cum_v"))){
							rec.put("nncp_font_color",  "white" );
							rec.put("nncp_bg_color",  "orange" );
						}
						else {
							rec.put("nncp_font_color",  "black" );
							rec.put("nncp_bg_color",  "white" );
						}
					}else {
						rec.put("nncp_font_color",  "black" );
						rec.put("nncp_bg_color",  "white" );
					}
					
					String max1_pos_net_cum_d = (String) rec.get("max1_pos_net_cum_d");
					if (!SwtUtil.isEmptyOrNull(max1_pos_net_cum_d) && max1_pos_net_cum_d.split(" ").length == 2) {
						rec.put("max1_pos_net_cum_d", max1_pos_net_cum_d.split(" ")[1]);
					}
					
					String max1_neg_net_cum_d = (String) rec.get("max1_neg_net_cum_d");
					if (!SwtUtil.isEmptyOrNull(max1_neg_net_cum_d) && max1_neg_net_cum_d.split(" ").length == 2) {
						rec.put("max1_neg_net_cum_d", max1_neg_net_cum_d.split(" ")[1]);
					}
				}
				else {
					outgoingPayement.put((String)rec.get("ccy"), getValueOf(rec.get("avg_day_acc_outflow_v")));
					
					incomingPayement.put((String)rec.get("ccy"), getValueOf(rec.get("avg_day_acc_inflow_v")));
					sourceOfLiquidity.put((String)rec.get("ccy"), getValueOf(rec.get("avg_avlbl_assets_v")));
					maxCritOutflow.put((String)rec.get("ccy"), getValueOf(rec.get("avg_day_acc_crit_outflow_v")));
					maxCritInflow.put((String)rec.get("ccy"), getValueOf(rec.get("avg_day_acc_crit_inflow_v")));
					if(getValueOf(rec.get("avg_day_acc_inflow_v")) != null &&  getValueOf(rec.get("avg_day_acc_outflow_v")) != null)
						rec.put("avg_day_acc_netflow_v", getValueOf(rec.get("avg_day_acc_inflow_v")) - getValueOf(rec.get("avg_day_acc_outflow_v")));
					else
						rec.put("avg_day_acc_netflow_v", null);
					
					
					if(max_ncp_threshold != null){
						if(max_ncp_threshold != null && getValueOf(rec.get("avg_pos_net_cum_v")) != null && max_ncp_threshold <  getValueOf(rec.get("avg_pos_net_cum_v"))){
							rec.put("pncp_font_color",  "white" );
							rec.put("pncp_bg_color",  "orange" );
						}
						else { 
							rec.put("pncp_font_color",  "black" );
							rec.put("pncp_bg_color",  "white" );
						}
					}else {
						rec.put("pncp_font_color",  "black" );
						rec.put("pncp_bg_color",  "white" );
					}
					
					if(getValueOf(rec.get("avg_avlbl_assets_v")) != null &&  getValueOf(rec.get("avg_neg_net_cum_v")) != null  && getValueOf(rec.get("avg_avlbl_assets_v")) < Math.abs(getValueOf(rec.get("avg_neg_net_cum_v")))){
						rec.put("nncp_font_color",  "white" );
						rec.put("nncp_bg_color",  "red" );
					}
					else if(min_ncp_threshold != null){
						if(min_ncp_threshold != null && getValueOf(rec.get("avg_neg_net_cum_v")) != null && min_ncp_threshold >  getValueOf(rec.get("avg_neg_net_cum_v"))){
							rec.put("nncp_font_color",  "white" );
							rec.put("nncp_bg_color",  "orange" );
						}
						else {
							rec.put("nncp_font_color",  "black" );
							rec.put("nncp_bg_color",  "white" );
						}
					}else {
						rec.put("nncp_font_color",  "black" );
						rec.put("nncp_bg_color",  "white" );
					}
	
				}
				
				h++;
				mainDataAvgSingle.add(rec);
			}
			
			
			beans.put("mainrepdata", mainDataAvgSingle);
			
			int i = 0;
			for(HashMap<String, Object> rec:allccy){
				if("Y".equals(pUseCcyMultiplier)) 
					ccyList.add((String)rec.get("ccy_multiplier_label"));
				else 
					ccyList.add((String)rec.get("ccy"));
				
				IntradayThroughput th = new IntradayThroughput((String)rec.get("ccy"));
				if(i < throughputList.size())
					throughput = throughputList.get(rec.get("ccy"));
				else 
					throughput= new LinkedList<HashMap<String, Object>> ();
				
				if(i < mainDataAvgList.size())
					mainDataAvgSingle = mainDataAvgList.get(rec.get("ccy"));
				else 
					mainDataAvgSingle= new LinkedList<HashMap<String, Object>> ();
				
				
				if(i < assetsList.size())
					assets = assetsList.get(rec.get("ccy"));
				else 
					assets= new LinkedList<HashMap<String, Object>> ();
				
				if(i < criticalPayementOutflowAvgDataList.size())
					criticalPayementOutflowAvgData = criticalPayementOutflowAvgDataList.get(rec.get("ccy"));
				else 
					criticalPayementOutflowAvgData= new LinkedList<HashMap<String, Object>> ();
				
				if(i < criticalPayementInflowAvgDataList.size())
					criticalPayementInflowAvgData = criticalPayementInflowAvgDataList.get(rec.get("ccy"));
				else 
					criticalPayementInflowAvgData= new LinkedList<HashMap<String, Object>> ();
				for(HashMap<String, Object> rec2:throughput){
					String timeKey = "t"+((String)rec2.get("time")).replace(":", "");
					th.pushInflow((Double)rec2.get("inflow")/100);
					th.pushOutflow(timeKey, (Double)rec2.get("outflow")/100);
					// If first currency, fill in times
					if(!thrtimes.contains((String)rec2.get("time"))){
						thrtimes.add((String)rec2.get("time"));
					}
				}
				throughputData.add(th);
				
				ReportItem avg = new ReportItem((String)rec.get("ccy"));
				for(HashMap<String, Object> rec3:mainDataAvgSingle){

					avg_external_sod = getValueOf(rec3.get("avg_cb_external_sod"));
					avg_collateral = getValueOf(rec3.get("avg_cb_collateral"));
					avgbob = getValueOf(rec3.get("avgbob"));
					avgosc = getValueOf(rec3.get("avgosc"));
					avg_credit_line_total = getValueOf(rec3.get("avg_gc_cl_total"));
					avg_credit_line_secured = getValueOf(rec3.get("avg_gc_avg_secured"));
					avg_credit_line_committed = getValueOf(rec3.get("avg_gc_cl_committed"));
					avg_unencumbered_liquid_assets = getValueOf(rec3.get("avg_gc_unc_assets"));
					avg_other_total = getValueOf(rec3.get("avg_gc_other_total"));
					avg.setSourcesIntradayLiquidity(sourceOfLiquidity.get((String)rec.get("ccy")));
					avg.setCentBankBal(avg_external_sod);
					avg.setCentBankColl(avg_collateral);
					avg.setBallOtherBanks(avgbob);
					avg.setOthSysCollateral(avgosc);
					avg.setTotCreLines(avg_credit_line_total);
					avg.setCredLineSecured(avg_credit_line_secured);
					avg.setCredLIneCommited(avg_credit_line_committed);
					avg.setUnencumLiqAssets(avg_unencumbered_liquid_assets);
					avg.setOtherTot(avg_other_total);
					Double critPayementOutflow  = maxCritOutflow.get((String)rec.get("ccy"));
					Double critPayementInflow  = maxCritInflow.get((String)rec.get("ccy"));
					avg.setMaxCritOutflow(critPayementOutflow);
					avg.setMaxCritInflow(critPayementInflow);
					
					Double outgoing = outgoingPayement.get((String)rec.get("ccy"));
					Double incoming = incomingPayement.get((String)rec.get("ccy"));
					Double largestPositive = sourceOfLiquidity.get((String)rec.get("ccy"));
					
					if(critPayementOutflow != null && critPayementOutflow != 0) {
						avg.setPercentCritOutflowOfOutg((Double) (critPayementOutflow / outgoing));
						avg.setPercentCritOutflowOfAvailbleLiquidity((Double) (critPayementOutflow / (largestPositive + incoming)));
						avg.setPercentCritOutflowOfAvailbleLiquidityExclIncom((Double) (critPayementOutflow / (largestPositive)));
					}
					else { 
						avg.setPercentCritOutflowOfOutg(null);
						avg.setPercentCritOutflowOfAvailbleLiquidity(null);
						avg.setPercentCritOutflowOfAvailbleLiquidityExclIncom(null);
					}
					
					if(critPayementInflow != null && critPayementInflow != 0) {
						avg.setPercentCritInflowOfOutg((Double) (critPayementInflow / incoming));
						avg.setPercentCritInflowOfAvailbleLiquidity((Double) (critPayementInflow / (largestPositive + incoming)));
						avg.setPercentCritInflowOfAvailbleLiquidityExclIncom((Double) (critPayementInflow / (largestPositive)));
					}
					else { 
						avg.setPercentCritInflowOfOutg(null);
						avg.setPercentCritInflowOfAvailbleLiquidity(null);
						avg.setPercentCritInflowOfAvailbleLiquidityExclIncom(null);
					}
	
					if(largestPositive != null && incoming != null)
						avg.setTotalSourIncIncom(largestPositive + incoming);
					else 
						avg.setTotalSourIncIncom(null);
					
					
					if((largestPositive != null && outgoing != null && incoming != null) && (largestPositive + outgoing) != 0) 
						avg.setPercentOutOfTotalAv((Double) ((outgoing) / (largestPositive + incoming)));
					else
						avg.setPercentOutOfTotalAv(null);
					
					if(largestPositive != null && largestPositive != 0)
							avg.setPercentOutOfTotalAvExcIncom((Double) ((outgoing) / largestPositive));
					else
						avg.setPercentOutOfTotalAvExcIncom(null);
					
					// set assets List 
					if(assets != null && assets.size() > 0) {
						for(HashMap<String, Object> rec4:assets){
							
							if(!assetsNames.contains((String)rec4.get("attribute_name"))){
								assetsNames.add((String)rec4.get("attribute_name"));
							}
						}
					}else {
						assets = new LinkedList<HashMap<String,Object>>();
					}
					avg.setAssets(assets);
				}
				
				
				
				Double unmonitoredOutflowTotal = null;
				Double unmonitoredInflowTotal = null;
				
				for(HashMap<String, Object> rec3:criticalPayementOutflowAvgData){
					if(criticalPaymentCategory == null || !criticalPaymentCategory.equals(rec3.get("critical_payment_catg"))){
						if(criticalPaymentCategory != null){
							criticalPayement.setCategory(criticalPayTypesCategory.get(criticalPaymentCategory));
							criticalPayement.setDetails(critcalListHashmap);
							criticalPayement.setAmount(cp_catg_total);
							criticalDetailsData.add(criticalPayement);
							if(i == 0){
								Category cat = new Category();
								cat.setName(criticalPayTypesCategory.get(criticalPaymentCategory));
								cat.setSubCategoryList(subcategoryList);
								subcategoryList= new LinkedList<String>();
								categoryList.add(cat);
							}
						}
						criticalPayement = null;
						critcalListHashmap = null;
						criticalPayement = new CriticalDetailsList();
						critcalListHashmap = new LinkedList<CriticalPayementDetails>();
					}
					criticalPayementTypeDesc = rec3.get("cp_type_desc") != null ? rec3.get("cp_type_desc").toString():"";
					criticalDetails = new CriticalPayementDetails();
					criticalDetails.setMovementType(criticalPayementTypeDesc);
					criticalDetails.setAmount(getValueOf(rec3.get("cp_type_total")));
					critcalListHashmap.add(criticalDetails);
					cp_catg_total = getValueOf(rec3.get("cp_catg_total"));
					cp_grand_total = getValueOf(rec3.get("cp_grand_total"));
					
					criticalPaymentCategory = rec3.get("critical_payment_catg")!= null ?rec3.get("critical_payment_catg").toString():"";
					
					if(i == 0){
						subcategoryList.add(criticalPayementTypeDesc);
					}
				}
				
				if(criticalPaymentCategory != null){
					criticalPayement.setCategory(criticalPayTypesCategory.get(criticalPaymentCategory));
					criticalPayement.setDetails(critcalListHashmap);
					criticalPayement.setAmount(cp_catg_total);
					criticalDetailsData.add(criticalPayement);
					if(i == 0){
						Category cat = new Category();
						cat.setName(criticalPayTypesCategory.get(criticalPaymentCategory));
						cat.setSubCategoryList(subcategoryList);
						categoryList.add(cat);
					}
				}
				
	
				ccyCriticalPayement = new CurrencyCriticalDetails();
				ccyCriticalPayement.setCurrency((String)rec.get("ccy"));
				ccyCriticalPayement.setDetails(criticalDetailsData);;
				ccyCriticalPayement.setAmount(cp_grand_total!=null?cp_grand_total:new Double(0));
				unmonitoredOutflowTotal = cp_grand_total;
				dataForReportOutflow.add(ccyCriticalPayement);
				criticalDetailsData = new LinkedList<CriticalDetailsList>();
				critcalListHashmap = new LinkedList<CriticalPayementDetails>();
				criticalPaymentCategory = null;
				criticalPayementTypeDesc =null;
				
				
				cp_catg_total = null;
				for(HashMap<String, Object> rec3:criticalPayementInflowAvgData){
					if(criticalPaymentCategory == null || !criticalPaymentCategory.equals(rec3.get("critical_payment_catg"))){
						if(criticalPaymentCategory != null){
							criticalPayement.setCategory(criticalPayTypesCategory.get(criticalPaymentCategory));
							criticalPayement.setDetails(critcalListHashmap);
							criticalPayement.setAmount(cp_catg_total);
							criticalDetailsData.add(criticalPayement);
						}
						criticalPayement = null;
						critcalListHashmap = null;
						criticalPayement = new CriticalDetailsList();
						critcalListHashmap = new LinkedList<CriticalPayementDetails>();
						//	rec2.get("critical_payment_type_desc").toString()
					}
					criticalPayementTypeDesc = rec3.get("cp_type_desc") != null ? rec3.get("cp_type_desc").toString():"";;
					criticalDetails = new CriticalPayementDetails();
					criticalDetails.setMovementType(criticalPayementTypeDesc);
					criticalDetails.setAmount(getValueOf(rec3.get("cp_type_total")));
					critcalListHashmap.add(criticalDetails);
					cp_catg_total = getValueOf(rec3.get("cp_catg_total"));
					cp_grand_total = getValueOf(rec3.get("cp_grand_total"));
					
					unmonitoredInflowTotal = cp_grand_total;
					criticalPaymentCategory = rec3.get("critical_payment_catg")!= null ?rec3.get("critical_payment_catg").toString():"";
					
				}
				
				if(criticalPaymentCategory != null){
					criticalPayement.setCategory(criticalPayTypesCategory.get(criticalPaymentCategory));
					criticalPayement.setAmount(cp_catg_total);
					criticalPayement.setDetails(critcalListHashmap);
					criticalDetailsData.add(criticalPayement);
				}
				
				
	
				ccyCriticalPayement = new CurrencyCriticalDetails();
				ccyCriticalPayement.setCurrency((String)rec.get("ccy"));
				ccyCriticalPayement.setDetails(criticalDetailsData);;
				ccyCriticalPayement.setAmount(cp_grand_total!=null?cp_grand_total:new Double(0));
				dataForReportInflow.add(ccyCriticalPayement);
				criticalDetailsData = new LinkedList<CriticalDetailsList>();
				critcalListHashmap = new LinkedList<CriticalPayementDetails>();
				criticalPaymentCategory = null;
				criticalPayementTypeDesc =null;
				
				Double maxCritValue = null;
				
				maxCritValue = avg.getMaxCritOutflow() != null ? avg.getMaxCritOutflow() : 0;
				unmonitoredOutflowTotal = unmonitoredOutflowTotal != null ? unmonitoredOutflowTotal:0;
				
				avg.setUnmonitoredOutflowTotal((maxCritValue- unmonitoredOutflowTotal));
				
				maxCritValue = avg.getMaxCritInflow() != null ? avg.getMaxCritInflow() : 0;
				unmonitoredInflowTotal = unmonitoredInflowTotal != null ? unmonitoredInflowTotal:0;
				
				avg.setUnmonitoredInflowTotal((maxCritValue - unmonitoredInflowTotal));
				
				availbleLiquidityAvg.add(avg);
				
				i++;
			}
			beans.put("thrdata", throughputData);
			beans.put("categoryListSize", categoryList.size());
			if (categoryList.size() == 0) {
				Category cat = new Category();
				subcategoryList = new LinkedList<String>();
				subcategoryList.add("");
				cat.setSubCategoryList(subcategoryList);
				categoryList.add(cat);
			}
			beans.put("categoryList", categoryList);
			beans.put("availbleLiquidityAvg", availbleLiquidityAvg);
			beans.put("criticalDataOutflow", dataForReportOutflow);
			beans.put("criticalDataInflow", dataForReportInflow);
			if (thrtimes.size() == 0) {
				thrtimes.add("00:00");
			}
			beans.put("thrtimes", thrtimes);
			beans.put("assetsNames", assetsNames);
			beans.put("ccyList", ccyList);
		}catch (Exception e){
			e.printStackTrace();
			throw SwtErrorHandler.getInstance().handleException(e,
					"getBeansMap",
					MultipleCurrencies.class);
		}finally {
			mainDataAvg = null;
			allccy = null;
			outgoingPayement = null;
			incomingPayement = null;
			sourceOfLiquidity = null;
			maxCritOutflow = null;
			maxCritInflow = null;
			ccyList = new LinkedList<String>();
			// Thresholds
			min_ncp_threshold = null;
			max_ncp_threshold = null;
			// Throughput
			throughput = null;
			assets = null;
			criticalPayementOutflowAvgData = null;
			criticalPayementInflowAvgData = null;
			throughputData = null;
			thrtimes = null;
			availbleLiquidityAvg = null;
			criticalPaymentCategory = null;
			criticalPayementTypeDesc = null;
			critcalListHashmap = null;
			criticalDetails  = null;
			dataForReportOutflow = null;
			dataForReportInflow = null;
			criticalDetailsData = null;
			criticalPayement  = null;
			categoryList = null;
			subcategoryList = null;
			mainDataAvgList = null;
			throughputList = null;
			assetsList = null;
			criticalPayementOutflowAvgDataList = null;
			criticalPayementInflowAvgDataList = null;
			ccyCriticalPayement = null;
			assetsNames = new ArrayList<String>();
			avg_external_sod = null;
			avg_collateral = null;
			avgbob = null;
			avgosc = null;
			avg_credit_line_secured = null;
			avg_credit_line_total = null;
			avg_credit_line_committed = null;
			avg_unencumbered_liquid_assets = null;
			avg_other_total = null;
			cp_catg_total = null;
			cp_grand_total = null;
		}
		return beans;
	}
	
	protected static Double getValueOf(Object tmp) throws Exception{
		if(tmp != null && tmp.toString().length() > 0) {
			return Double.parseDouble(tmp.toString());
		}
		else 
			return null;
	}
	
	public static class IntradayThroughput{
		private String currency;
		private ArrayList<String> times;
		private ArrayList<Double> inflows = new ArrayList<Double>();
		private ArrayList<Double> outflows = new ArrayList<Double>();;
		
		public IntradayThroughput(String currency) {
			this.currency = currency;
		}
		
		public void pushTime(String value){
			if(times==null)
				times = new ArrayList<String>();
			times.add(value);
		}
		public void pushInflow(Double value){
			if(inflows==null)
				inflows = new ArrayList<Double>();
			inflows.add(value);
		}
		public void pushOutflow(String key, Double value){
			if(outflows==null)
				outflows = new ArrayList<Double>();
			outflows.add(value);
		}
		
		public ArrayList<String> getTimes() {
			return times;
		}
		public void setTimes(ArrayList<String> times) {
			this.times = times;
		}
		public String getCurrency() {
			return currency;
		}
		public void setCurrency(String currency) {
			this.currency = currency;
		}
		public ArrayList<Double> getInflows() {
			return inflows;
		}
		public void setInflows(ArrayList<Double> inflows) {
			this.inflows = inflows;
		}
		public ArrayList<Double> getOutflows() {
			return outflows;
		}
		public void setOutflows(ArrayList<Double> outflows) {
			this.outflows = outflows;
		}
	}
	
	public static class ReportItem{
		private String currency;
		private ArrayList<String> dates;
		private Double centBankBal= null;
		private Double centBankColl= null;
		private Double ballOtherBanks = null;
		private Double othSysCollateral = null;
		private Double totCreLines = null;
		private Double credLineSecured = null;
		private Double credLIneCommited = null;
		private Double unencumLiqAssets = null;
		private Double otherTot = null;
		private Double sourcesIntradayLiquidity = null; 
		private Double maxCritOutflow = null; 
		private Double maxCritInflow = null; 
		
		private Double totalSourExcIncom = null;
		private Double totalSourIncIncom = null;
		private Double percentOutOfTotalAv = null;
		private Double percentOutOfTotalAvExcIncom = null;
		private Double percentCritOutflowOfOutg = null;
		private Double percentCritOutflowOfAvailbleLiquidity = null;
		private Double percentCritOutflowOfAvailbleLiquidityExclIncom = null;
		private Double percentCritInflowOfOutg = null;
		private Double percentCritInflowOfAvailbleLiquidity = null;
		private Double percentCritInflowOfAvailbleLiquidityExclIncom = null;
		private Double unmonitoredOutflowTotal = null;
		private Double unmonitoredInflowTotal = null;
		
		private LinkedList<HashMap<String, Object>> assets = null;
		
		
		
		
		



		public Double getMaxCritInflow() {
			return maxCritInflow;
		}



		public void setMaxCritInflow(Double maxCritInflow) {
			this.maxCritInflow = maxCritInflow;
		}



		public Double getPercentCritOutflowOfOutg() {
			return percentCritOutflowOfOutg;
		}



		public void setPercentCritOutflowOfOutg(Double percentCritOutflowOfOutg) {
			this.percentCritOutflowOfOutg = percentCritOutflowOfOutg;
		}



		public Double getPercentCritOutflowOfAvailbleLiquidity() {
			return percentCritOutflowOfAvailbleLiquidity;
		}



		public void setPercentCritOutflowOfAvailbleLiquidity(Double percentCritOutflowOfAvailbleLiquidity) {
			this.percentCritOutflowOfAvailbleLiquidity = percentCritOutflowOfAvailbleLiquidity;
		}



		public Double getPercentCritOutflowOfAvailbleLiquidityExclIncom() {
			return percentCritOutflowOfAvailbleLiquidityExclIncom;
		}



		public void setPercentCritOutflowOfAvailbleLiquidityExclIncom(Double percentCritOutflowOfAvailbleLiquidityExclIncom) {
			this.percentCritOutflowOfAvailbleLiquidityExclIncom = percentCritOutflowOfAvailbleLiquidityExclIncom;
		}



		public Double getPercentCritInflowOfOutg() {
			return percentCritInflowOfOutg;
		}



		public void setPercentCritInflowOfOutg(Double percentCritInflowOfOutg) {
			this.percentCritInflowOfOutg = percentCritInflowOfOutg;
		}



		public Double getPercentCritInflowOfAvailbleLiquidity() {
			return percentCritInflowOfAvailbleLiquidity;
		}



		public void setPercentCritInflowOfAvailbleLiquidity(Double percentCritInflowOfAvailbleLiquidity) {
			this.percentCritInflowOfAvailbleLiquidity = percentCritInflowOfAvailbleLiquidity;
		}



		public Double getPercentCritInflowOfAvailbleLiquidityExclIncom() {
			return percentCritInflowOfAvailbleLiquidityExclIncom;
		}



		public void setPercentCritInflowOfAvailbleLiquidityExclIncom(Double percentCritInflowOfAvailbleLiquidityExclIncom) {
			this.percentCritInflowOfAvailbleLiquidityExclIncom = percentCritInflowOfAvailbleLiquidityExclIncom;
		}



		public Double getCentBankBal() {
			return centBankBal;
		}



		public void setCentBankBal(Double centBankBal) {
			this.centBankBal = centBankBal;
		}



		public Double getCentBankColl() {
			return centBankColl;
		}



		public void setCentBankColl(Double centBankColl) {
			this.centBankColl = centBankColl;
		}



		public Double getBallOtherBanks() {
			return ballOtherBanks;
		}



		public void setBallOtherBanks(Double ballOtherBanks) {
			this.ballOtherBanks = ballOtherBanks;
		}



		public Double getOthSysCollateral() {
			return othSysCollateral;
		}



		public void setOthSysCollateral(Double othSysCollateral) {
			this.othSysCollateral = othSysCollateral;
		}



		public Double getTotCreLines() {
			return totCreLines;
		}



		public void setTotCreLines(Double totCreLines) {
			this.totCreLines = totCreLines;
		}



		public Double getCredLineSecured() {
			return credLineSecured;
		}



		public void setCredLineSecured(Double credLineSecured) {
			this.credLineSecured = credLineSecured;
		}



		public Double getCredLIneCommited() {
			return credLIneCommited;
		}



		public void setCredLIneCommited(Double credLIneCommited) {
			this.credLIneCommited = credLIneCommited;
		}



		public Double getUnencumLiqAssets() {
			return unencumLiqAssets;
		}



		public void setUnencumLiqAssets(Double unencumLiqAssets) {
			this.unencumLiqAssets = unencumLiqAssets;
		}



		public Double getOtherTot() {
			return otherTot;
		}



		public void setOtherTot(Double otherTot) {
			this.otherTot = otherTot;
		}



		public Double getTotalSourExcIncom() {
			return totalSourExcIncom;
		}



		public void setTotalSourExcIncom(Double totalSourExcIncom) {
			this.totalSourExcIncom = totalSourExcIncom;
		}



		public Double getTotalSourIncIncom() {
			return totalSourIncIncom;
		}



		public void setTotalSourIncIncom(Double totalSourIncIncom) {
			this.totalSourIncIncom = totalSourIncIncom;
		}



		public void pushDate(String value){
			if(dates==null)
				dates = new ArrayList<String>();
			dates.add(value);
		}
		
		
		
		public ArrayList<String> getDates() {
			return dates;
		}

		public void setDates(ArrayList<String> dates) {
			this.dates = dates;
		}


		public String getCurrency() {
			return currency;
		}
		public void setCurrency(String currency) {
			this.currency = currency;
		}

		public Double getPercentOutOfTotalAv() {
			return percentOutOfTotalAv;
		}

		public void setPercentOutOfTotalAv(Double percentOutOfTotalAv) {
			this.percentOutOfTotalAv = percentOutOfTotalAv;
		}

		public Double getPercentOutOfTotalAvExcIncom() {
			return percentOutOfTotalAvExcIncom;
		}

		public void setPercentOutOfTotalAvExcIncom(Double percentOutOfTotalAvExcIncom) {
			this.percentOutOfTotalAvExcIncom = percentOutOfTotalAvExcIncom;
		}
		
		public ReportItem(String currency) {
			this.currency = currency;
		}



		public Double getSourcesIntradayLiquidity() {
			return sourcesIntradayLiquidity;
		}



		public void setSourcesIntradayLiquidity(Double sourcesIntradayLiquidity) {
			this.sourcesIntradayLiquidity = sourcesIntradayLiquidity;
		}



		public Double getMaxCritOutflow() {
			return maxCritOutflow;
		}



		public void setMaxCritOutflow(Double maxCritOutflow) {
			this.maxCritOutflow = maxCritOutflow;
		}



		public LinkedList<HashMap<String, Object>> getAssets() {
			return assets;
		}



		public void setAssets(LinkedList<HashMap<String, Object>> assets) {
			this.assets = assets;
		}



		public Double getUnmonitoredInflowTotal() {
			return unmonitoredInflowTotal;
		}



		public void setUnmonitoredInflowTotal(Double unmonitoredInflowTotal) {
			this.unmonitoredInflowTotal = unmonitoredInflowTotal;
		}



		public Double getUnmonitoredOutflowTotal() {
			return unmonitoredOutflowTotal;
		}



		public void setUnmonitoredOutflowTotal(Double unmonitoredOutflowTotal) {
			this.unmonitoredOutflowTotal = unmonitoredOutflowTotal;
		}
		

	}
	
	public static class CurrencyCriticalDetails{
		private String currency = null;
		private LinkedList<CriticalDetailsList> details = null;
		private Double amount = null;
		public String getCurrency() {
			return currency;
		}
		public void setCurrency(String currency) {
			this.currency = currency;
		}
		public LinkedList<CriticalDetailsList> getDetails() {
			return details;
		}
		public void setDetails(LinkedList<CriticalDetailsList> details) {
			this.details = details;
		}
		public Double getAmount() {
			return amount;
		}
		public void setAmount(Double amount) {
			this.amount = amount;
		}
		
		
	}
	
	public static class CriticalPayementDetails{
		private String movementType;
		private Double amount;
		
		public String getMovementType() {
			return movementType;
		}
		public void setMovementType(String movementType) {
			this.movementType = movementType;
		}
		public Double getAmount() {
			return amount;
		}
		public void setAmount(Double amount) {
			this.amount = amount;
		}


	}
	
	public static class CriticalDetailsList{
		private String category = null;
		private LinkedList<CriticalPayementDetails> details = null;
		Double amount = null;
		
		
		public Double getAmount() {
			return amount;
		}
		public void setAmount(Double amount) {
			this.amount = amount;
		}
		public String getCategory() {
			return category;
		}
		public void setCategory(String currency) {
			this.category = currency;
		}
		public LinkedList<CriticalPayementDetails> getDetails() {
			return details;
		}
		public void setDetails(LinkedList<CriticalPayementDetails> details) {
			this.details = details;
		}
		
		
	}
	
	public static class Category{
		String name = null;
		LinkedList<String> subCategoryList = null;
		public String getName() {
			return name;
		}
		public void setName(String name) {
			this.name = name;
		}
		public LinkedList<String> getSubCategoryList() {
			return subCategoryList;
		}
		public void setSubCategoryList(LinkedList<String> subCategoryList) {
			this.subCategoryList = subCategoryList;
		}
		
	}
	
	
	
}
