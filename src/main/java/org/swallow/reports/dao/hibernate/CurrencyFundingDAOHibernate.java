/*
 * @(#)CurrencyFundingDAOHibernate.java 1.0 10/08/2009
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.reports.dao.hibernate;

import java.sql.CallableStatement;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Types;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;
import javax.sql.DataSource;

import net.sf.jasperreports.engine.JREmptyDataSource;
import net.sf.jasperreports.engine.JasperCompileManager;
import net.sf.jasperreports.engine.JasperFillManager;
import net.sf.jasperreports.engine.JasperPrint;
import net.sf.jasperreports.engine.JasperReport;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

import org.swallow.batchScheduler.ConnectionManager;
import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.reports.dao.CurrencyFundingDAO;
import org.swallow.reports.model.CurrencyFunding;
import org.swallow.util.CommonDataManager;
import org.swallow.util.JDBCCloser;
import org.swallow.util.SwtConstants;
import org.swallow.util.SwtUtil;
import org.swallow.util.SystemFormats;
import org.swallow.util.UserThreadLocalHolder;
import org.springframework.context.annotation.Lazy;
import org.springframework.orm.hibernate5.support.HibernateDaoSupport;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.stereotype.Repository;
import org.hibernate.SessionFactory;

/**
 * 
 * <AUTHOR> Class that implements the CurrencyFundingDAO and acts as DAO
 *         layer for all database operations
 * 
 */
@Repository ("currencyFundingDAO")
@Transactional
public class CurrencyFundingDAOHibernate extends HibernateDaoSupport implements
		CurrencyFundingDAO {
	public CurrencyFundingDAOHibernate(@Lazy SessionFactory sessionfactory) {
	    setSessionFactory(sessionfactory);
	}


	/*
	 * Initializing Log variable for logging comments
	 */
	private static final Log log = LogFactory
			.getLog(CurrencyFundingDAOHibernate.class);

	/**
	 * Get the distinct AccountId from p_account table
	 * 
	 * @param String
	 *            hostId
	 * @param String
	 *            entityId
	 * @param String
	 *            currencyCode
	 * @return Collection
	 */
	public Collection getAccountDetails(String hostId, String entityId,
			String currencyCode) {

		log.debug(this.getClass().getName() + "- [getAccountDetails] - Entry ");
		/* Local variable declaration */
		Connection conn = null;
		PreparedStatement pstmt = null;
		ResultSet res = null;
		List currentStatus = null;
		StringBuffer acctDetailQuery = null;

		try {

			CurrencyFunding currencyfunding = new CurrencyFunding();
			/* Establish the connection using connection manager */
			conn = ConnectionManager.getInstance().databaseCon();
			

			/* Object initialization */
			currentStatus = new ArrayList();
			/* pass the query in string buffer */
			acctDetailQuery = new StringBuffer("select account_id,account_name from p_account where host_id=? and entity_id=? ");
			/*
			 * START/END:code modified by Mahesh on 19-Feb-2010 for Mantis 1111:
			 * modified the condition, removed the "All" currency option in UI
			 */
			if (!currencyCode.equals("All")) {
				acctDetailQuery.append("and currency_code=? ");
			}
			acctDetailQuery.append(" order by account_id, account_level ");
			pstmt = conn.prepareStatement(acctDetailQuery.toString());
			pstmt.setString(1, hostId);
			pstmt.setString(2, entityId);
			if (!currencyCode.equals("All")) {
				pstmt.setString(3, currencyCode);
			}
			/* execute the statement */
			res = pstmt.executeQuery();

			/* Getting the accountId,accountName and put it in List */

			while (res.next()) {
				currentStatus.add(res.getString(1));
				currentStatus.add(res.getString(2));
			}
		} catch (SQLException e) {

			log.debug(this.getClass().getName()
					+ "- [getAccountDetails] - Exception " + e.getMessage());
			log.error(this.getClass().getName()
					+ "- [getAccountDetails] - Exception " + e.getMessage());
			e.printStackTrace();
		} finally {
			
			//edited by KaisBS for mantis 1745 : introducing the implementation of the JDBCCloser class
			JDBCCloser.close(res, pstmt, null, null);
			
			try{				
				if ((conn != null) && (!conn.isClosed()))
					ConnectionManager.getInstance().retrunConnection(conn);
			} catch (Exception e) {
				logger.error("org.swallow.reports.dao.hibernate.CurrencyFundingDAOHibernate - " +
						"[getAccountDetails] - Exception - " + e.getMessage());         
			}
		}
		log.debug(this.getClass().getName() + "- [getAccountDetails] - Exit ");

		return currentStatus;
	}

	/**
	 * This method is used to compile and return the reports
	 * 
	 * @param request -
	 *            HttpServletRequest
	 * @param hostId -
	 *            String
	 * @param entityId -
	 *            String
	 * @param entityName -
	 *            String
	 * @param currencyCode -
	 *            String
	 * @param currencyName -
	 *            String
	 * @param valueDate -
	 *            String
	 * @param selectedAcctId -
	 *            String
	 * @param accountName -
	 *            String
	 * @param thresholdValue -
	 *            String
	 * @param showDR -
	 *            String
	 * @param showCR -
	 *            String
	 * @return JasperPrint
	 * @throws SwtException
	 * 
	 */
	public JasperPrint getCurrencyFundingReport(HttpServletRequest request,
			String hostId, String entityId, String entityName,
			String currencyCode, String currencyName, String selectedAcctId,
			String accountName, String valueDate, String thresholdValue,
			String showDR, String showCR, String savedDateFormat) throws SwtException {
		log.debug(this.getClass().getName()
				+ "- [getCurrencyFundingReport] - Entering ");

		/* Local variable declaration and initialization */
		Connection connection = null;
		DataSource ds = null;
		JasperReport jasperReport = null;
		JasperPrint jasperPrint = null;
		List pagesize = null;
		Date valueDate_1 = null;
		Date valueDate_2 = null;

		Double actualBalance = null;
		StringBuffer acctBalQuery = null;
		Double thresholdValuedouble = null;
		CallableStatement cstmt = null;
		Double predictedBalance = null;
		PreparedStatement stmt = null;
		ResultSet res = null;
		String bal_Type = null;
		String call = null;
		String predict_cr_dr = null;
		String actual_cr_dr = null;
		String pr_temp = null;
		String at_temp = null;
		String currencyFormat = null;
		/* START:Code added by Mahesh on 16-Feb-2010 for Mantis 1111 */
		String bv_adjust_basis_external = null;
		Double start_bal_external = null;
		Double predict_balance_ext = null;
		StringBuffer corrAcQuery = null;
		String corrAccId = null;
		String formatedThreshold = null;
		SystemFormats sysformat = null;
		/* END:Code added by Mahesh on 16-Feb-2010 for Mantis 1111 */

		HttpSession session = null;
		CommonDataManager cdm = null;
		String alternativeCcyFormat = null;
		try {
			/*
			 * START/END:code added by Mahesh on 22-Feb-2010 for Mantis 1111:
			 * Getting the currency format from the request
			 */
			if(request != null) {
				sysformat = SwtUtil.getCurrentSystemFormats(request.getSession());
				currencyFormat = sysformat.getCurrencyFormat();
			}else {
				currencyFormat = SwtUtil.getCurrentCurrencyFormat(null);
			}

			/* Declare Map to send the parameters to the Japser Engine */
			Map parms = new HashMap();
			/* compile the jasper file return response to action */
			jasperReport = JasperCompileManager.compileReport(SwtUtil.contextRealPath
					+ SwtConstants.CURRENCY_FUNDING_REPORT_FILE);
			/* Get the datasource and get the session */
			ds = (DataSource) SwtUtil.getBean("dataSource");
			connection = ds.getConnection();

			/* parse the report date as current system date format */
			valueDate_1 = savedDateFormat!=null ? SwtUtil.parseDate(valueDate, savedDateFormat):SwtUtil.parseDateGeneral(valueDate);
			valueDate_2 = savedDateFormat!=null ? SwtUtil.parseDate(valueDate, savedDateFormat):SwtUtil.parseDateGeneral(valueDate);
			/* parse the thresholdValue string to double */
			/*
			 * START/END:code modified by Mahesh on 22-Feb-2010 for Mantis 1111:
			 * Threshold value as with double format
			 */
			if("currencyPat1".equals(currencyFormat)) {
				alternativeCcyFormat = "currencyPat2";
			}else {
				alternativeCcyFormat = "currencyPat1";
			}
			
			try {
				thresholdValuedouble = SwtUtil.parseCurrency(thresholdValue,
						currencyFormat);
			} catch (Exception e) {
				thresholdValuedouble = SwtUtil.parseCurrency(thresholdValue,
						alternativeCcyFormat);
			}

			/* getting current user session */
			session = UserThreadLocalHolder.getUserSession();
			// Initialize the CommonDataManager for getting currencyFormat
			cdm = session !=null ?(CommonDataManager) session
					.getAttribute(SwtConstants.CDM_BEAN): null;
			if (cdm != null) {
				currencyFormat = cdm.getCurrencyFormat();
			}

			/*
			 * checking the condition for selected account id is not null then
			 * allowing into the loop
			 */
			if (!"".equals(selectedAcctId)) {
				/*
				 * calling the procedure to get the predict balance and pass the
				 * parameter in the jasper Engine
				 */
				log
						.debug(" PK_MONITORS.SP_GET_SWEEP_PREDICT_BALANCE Called at "
								+ SwtUtil.getTimeWithMilliseconds());
				cstmt = connection
						.prepareCall("{call PK_MONITORS.SP_GET_SWEEP_PREDICT_BALANCE(?,?,?,?,?,?,?)}");
				cstmt.setString(1, hostId);
				cstmt.setString(2, entityId);
				cstmt.setString(3, currencyCode);
				cstmt.setString(4, selectedAcctId);
				cstmt.setDate(5, SwtUtil.truncateDateTime(valueDate_1));
				cstmt.setDate(6, SwtUtil.truncateDateTime(valueDate_2));
				cstmt.registerOutParameter(7, Types.DOUBLE);
				cstmt.execute();
				log.debug(" PK_MONITORS.SP_GET_SWEEP_PREDICT_BALANCE Ended at "
						+ SwtUtil.getTimeWithMilliseconds());

				predictedBalance = new Double(cstmt.getDouble(7));

				/*
				 * START:Code modified by Mahesh on 17-Feb-2010 for Mantis 1111:
				 * Modified the query and parameter and passed those parameters
				 * to the report
				 */
				/* pass the query in string buffer */
				acctBalQuery = new StringBuffer(
						"select account_type, bv_adjust_basis_external,corres_acc_id from p_account where host_id=? and entity_id=? and account_id=? ");

				if (!currencyCode.equals("All")) {
					acctBalQuery.append("and currency_code=? ");
				}
				// create statement 
				stmt = connection.prepareStatement(acctBalQuery.toString());
				stmt.setString(1, hostId);
				stmt.setString(2, entityId);
				stmt.setString(3, selectedAcctId);
				
				if (!currencyCode.equals("All")) {
					stmt.setString(4, currencyCode);
				}
				// execute the statement 
				res = stmt.executeQuery();

				/* assign the account type to balance type */
				while (res.next()) {

					bal_Type = res.getString(1);
					bv_adjust_basis_external = res.getString(2);
					corrAccId = res.getString(3);
				}
				/*
				 * END:Code modified by Mahesh on 17-Feb-2010 for Mantis 1111:
				 * Modified the query and parameter and passed those parameters
				 * to the report
				 */

				/*
				 * START:Code added by Mahesh on 16-Feb-2010 for Mantis 1111:
				 * calling the functions and calculating the actual balance and
				 * passed those parameters to the report
				 */
				/* calling the FN_GET_START_BAl_EXTERNAL function */
				log
						.debug(" PKG_PREDICT_JOBS.FN_GET_START_BAl_EXTERNAL function Started at "
								+ SwtUtil.getTimeWithMilliseconds());

				// package name Modified for Mantis 1119 by VIvekanandan A on
				// 16-02-2012
				call = "{ ? = call PKG_PREDICT_JOBS.FN_GET_START_BAl_EXTERNAL(?,?,?,?,?,?,?,?)}";
				cstmt = connection.prepareCall(call);

				cstmt.registerOutParameter(1, Types.DOUBLE);
				cstmt.setString(2, hostId);
				cstmt.setString(3, entityId);
				cstmt.setString(4, currencyCode);
				cstmt.setString(5, selectedAcctId);
				cstmt.setString(6, bal_Type);
				cstmt.setString(7, bv_adjust_basis_external);
				cstmt.setDate(8, SwtUtil.truncateDateTime(valueDate_1));
				cstmt.setDate(9, SwtUtil.truncateDateTime(valueDate_1));
				cstmt.execute(); /* execute the callablestatement */
				log
						.debug(" PKG_PREDICT_JOBS.FN_GET_START_BAl_EXTERNAL function Ended at "
								+ SwtUtil.getTimeWithMilliseconds());

				start_bal_external = new Double(cstmt.getDouble(1));

				/* calling the FN_GET_PREDICT_BALANCE_EXT function */
				/* START:Code modified by Karthik on 19-Jan-2011 for Mantis 1303 */
				log
						.debug(" PKG_PREDICT_JOBS.FN_GET_PREDICT_BALANCE_EXT function Started at "
								+ SwtUtil.getTimeWithMilliseconds());
				// Parameter reduced for Mantis 1119 by VIvekanandan A on
				// 09-03-2012
				call = "{ ? = call PKG_PREDICT_JOBS.FN_GET_PREDICT_BALANCE_EXT(?,?,?,?,?)}";
				cstmt = connection.prepareCall(call);

				cstmt.registerOutParameter(1, Types.DOUBLE);
				cstmt.setString(2, hostId);
				cstmt.setString(3, entityId);
				cstmt.setString(4, currencyCode);
				cstmt.setString(5, selectedAcctId);
				cstmt.setDate(6, SwtUtil.truncateDateTime(valueDate_1));
				cstmt.execute(); /* execute the callablestatement */
				log
						.debug(" PKG_PREDICT_JOBS.FN_GET_PREDICT_BALANCE_EXT function Ended at "
								+ SwtUtil.getTimeWithMilliseconds());
				/* End:Code modified by Karthik on 19-Jan-2011 for Mantis 1303 */

				predict_balance_ext = new Double(cstmt.getDouble(1));

				// calculate the actual balance
				actualBalance = start_bal_external + predict_balance_ext;
				/*
				 * END:Code added by Mahesh on 16-Feb-2010 for Mantis 1111:
				 * calling the functions and calculating the actual balance and
				 * passed those parameters to the report
				 */

				/* START:Code commented by Mahesh on 16-Feb-2010 for Mantis 1111 */
				/*
				 * calling the function to get the actual balance and pass the
				 * parameter in the jasper Engine
				 */
				/*
				 * log.debug(" fn_get_actual_bal function Started at
				 * "+SwtUtil.getTimeWithMilliseconds()); call = "{ ? = call
				 * fn_get_actual_bal(?,?,?,?,?,?) }"; cstmt =
				 * connection.prepareCall(call);
				 * 
				 * cstmt.registerOutParameter(1, Types.DOUBLE);
				 * cstmt.setString(2, hostId); cstmt.setString(3, entityId);
				 * cstmt.setString(4, currencyCode); cstmt.setDate(5, new
				 * java.sql.Date(valueDate_1.getTime())); cstmt.setString(6,
				 * selectedAcctId); cstmt.setString(7, bal_Type);
				 */
				/* execute the callablestatement */
				// cstmt.execute();
				// log.debug(" fn_get_actual_bal function Ended at
				// "+SwtUtil.getTimeWithMilliseconds());
				// actualBalance=new Double(cstmt.getDouble(1));
				/* END:Code commented by Mahesh on 16-Feb-2010 for Mantis 1111 */

				/*
				 * concatenate the string C or D to the predict and actual
				 * balances and fill in the report
				 */
				if (predictedBalance > 0.0) {
					/* format the predict balance to currency format */
					pr_temp = SwtUtil.formatCurrency(currencyFormat,
							predictedBalance);
					pr_temp = pr_temp + "0";
					predict_cr_dr = pr_temp + "  " + "C";
				} else if (predictedBalance < 0.0) {
					/* format the predict balance to currency format */
					pr_temp = SwtUtil.formatCurrency(currencyFormat,
							predictedBalance);
					pr_temp = pr_temp.replaceAll("-", "");
					pr_temp = pr_temp + "0";
					predict_cr_dr = pr_temp + "  " + "D";
				} else if (predictedBalance == 0.0) {
					/* format the predict balance to currency format */
					pr_temp = SwtUtil.formatCurrency(currencyFormat,
							predictedBalance);
					pr_temp = pr_temp + "0";
					predict_cr_dr = pr_temp + "";
				}

				if (actualBalance > 0.0) {
					/* format the actual balance to currency format */
					at_temp = SwtUtil.formatCurrency(currencyFormat,
							actualBalance);
					at_temp = at_temp + "0";
					actual_cr_dr = at_temp + "  " + "C";
				} else if (actualBalance < 0.0) {
					/* format the actual balance to currency format */
					at_temp = SwtUtil.formatCurrency(currencyFormat,
							actualBalance);
					at_temp = at_temp.replaceAll("-", "");
					at_temp = at_temp + "0";
					actual_cr_dr = at_temp + "  " + "D";
				} else if (actualBalance == 0.0) {
					/* format the actual balance to currency format */
					at_temp = SwtUtil.formatCurrency(currencyFormat,
							actualBalance);
					at_temp = at_temp + "0";
					actual_cr_dr = at_temp + "";
				}
			}

			/*
			 * START:Code added by Mahesh on 19-Feb-2010 for Mantis 1111:
			 * Formated the Threshold value in currency format
			 */
			formatedThreshold = SwtUtil.formatCurrency(currencyFormat,
					thresholdValuedouble);
			formatedThreshold = formatedThreshold + "0";
			/*
			 * END:Code added by Mahesh on 19-Feb-2010 for Mantis 1111: Formated
			 * the Threshold value in currency format
			 */

			/* Preparing the parameters to be passed to Jasper Engine. */
			parms.put("pHost_Id", SwtUtil.getCurrentHostId());
			parms.put("pEntity_Id", entityId);
			parms.put("pEntity_Name", entityName);
			parms.put("pCurrency_Code", currencyCode);
			parms.put("pCurrency_Name", currencyName);
			parms.put("pAccount_Id", selectedAcctId);
			/*
			 * START:Code commented/added by Mahesh on 17-Feb-2010 for Mantis
			 * 1111: pass the corresponding values to the Jasper engine
			 */
			// parms.put("pAccount_Name", accountName);
			parms.put("pCorrAcc_Id", corrAccId);
			parms.put("pThresholdValue_format", formatedThreshold);
			parms.put("pCurrencyPattern", currencyFormat);
			/*
			 * END:Code commented/added by Mahesh on 17-Feb-2010 for Mantis
			 * 1111: pass the corresponding values to the Jasper engine
			 */
			parms.put("pReport_Date", valueDate_1);
			parms.put("pThreshold_Value", thresholdValuedouble);
			parms.put("pShow_DR", showDR);
			parms.put("pShow_CR", showCR);
			parms.put("pPredict_Balanace", predict_cr_dr);
			parms.put("pActual_Balanace", actual_cr_dr);

			/* Compiled Opportunity cost Report will be filled here. */
			jasperPrint = JasperFillManager.fillReport(jasperReport, parms,
					connection);
			/* To get the page size */
			pagesize = jasperPrint.getPages();
			/*
			 * If the page size is Zero Empty datasource will be passed to avoid
			 * the blank report.
			 */
			if (pagesize.size() == 0) {
				jasperPrint = JasperFillManager.fillReport(jasperReport, parms,
						new JREmptyDataSource(1));

			}

		} catch (Exception exp) {
			log.debug(this.getClass().getName()
					+ "- [getCurrencyFundingReport] - Exception "
					+ exp.getMessage());
			log.error(this.getClass().getName()
					+ "- [getCurrencyFundingReport] - Exception "
					+ exp.getMessage());
			exp.printStackTrace();
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "getCurrencyFundingReport",
					CurrencyFundingDAOHibernate.class), request, "");
			throw new SwtException(exp.getMessage());

		} finally {

			// Edited by KaisBS for mantis 1745 : introducing the implementation of the JDBCCloser class
			JDBCCloser.close(cstmt);
			JDBCCloser.close(res, stmt, connection, null);
		
		}

		log.debug(this.getClass().getName()
				+ "- [getCurrencyFundingReport] - Exiting ");
		return jasperPrint;
	}

}