/*
 * @(#)IntradayBalanceAction.java 1.0 07/09/08
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.reports.web;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collection;
import java.util.Date;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;

import net.sf.jasperreports.engine.JRExporterParameter;
import net.sf.jasperreports.engine.JasperPrint;
import net.sf.jasperreports.engine.export.JRPdfExporter;
import net.sf.jasperreports.engine.export.JRXlsExporter;
import net.sf.jasperreports.engine.export.JRXlsExporterParameter;
import net.sf.jasperreports.engine.fill.JRSwapFileVirtualizer;
import net.sf.jasperreports.engine.util.JRSwapFile;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.reports.model.IntradayBalances;
import org.swallow.reports.model.Reports;
import org.swallow.reports.service.IntradayBalanceManager;
import org.swallow.util.CacheManager;
import org.swallow.util.CommonDataManager;
import org.swallow.util.SwtConstants;
import org.swallow.util.SwtUtil;
import org.swallow.util.LabelValueBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.apache.struts2.convention.annotation.Result;
import org.apache.struts2.convention.annotation.AllowedMethods;
import org.apache.struts2.convention.annotation.Action;
import org.apache.struts2.ServletActionContext;
import org.swallow.util.struts.CustomActionSupport;

/**
 * 
 * <AUTHOR> G Class that extends the CustomActionSupport .
 * 
 */
@Action(value = "/intradayBalances", results = {
	@Result(name = "success", location = "/jsp/reports/intradaybalance.jsp"),
	@Result(name = "showacctdetails", location = "/jsp/reports/intradaybalanceaccount.jsp"),
	@Result(name = "fail", location = "/error.jsp"),
})

@AllowedMethods ({"displayList" ,"showAccountDetails" ,"report" ,"reportForGraph" })
public class IntradayBalanceAction extends CustomActionSupport {

private IntradayBalances intradaybalances;
public IntradayBalances getIntradaybalances() {
	if (intradaybalances == null) {
		intradaybalances = new IntradayBalances();
	}
	return intradaybalances;
}
public void setIntradaybalances(IntradayBalances intradaybalances) {
	this.intradaybalances = intradaybalances;
	HttpServletRequest request = ServletActionContext.getRequest();
	request.setAttribute("intradaybalances", intradaybalances);
}


	/*
	 * Log instance
	 */
	private final Log log = LogFactory.getLog(IntradayBalanceAction.class);

	/**
	 * Manager class instance
	 */
	@Autowired
private IntradayBalanceManager intradayBalanceManager = null;

	/**
	 * @param intradayBalanceManager
	 *            manager class instance to set
	 */
	public void setIntradayBalanceManager(
			IntradayBalanceManager intradayBalanceManager) {
		this.intradayBalanceManager = intradayBalanceManager;
	}
	public String execute() throws Exception {
	    HttpServletRequest request = ServletActionContext.getRequest();

	    // List of methods 
	    String method = String.valueOf(request.getParameter("method"));

	    switch (method) {
	        case "unspecified":
	            return unspecified();
	        case "displayList":
	            return displayList();
	        case "showAccountDetails":
	            return showAccountDetails();
	        case "report":
	            return report();
	        case "reportForGraph":
	            return reportForGraph();
	        default:
	            break;
	    }

	    return unspecified();
	}

	/**
	 * Default method which will be called
	 * 
	 * @param mapping
	 * 
	 * @param form
	 * 
	 * @param request
	 * 
	 * @param response
	 * 
	 * @return ActionForward which will specify the JSP to which the control
	 *         will be transferred using the mapping defined in the
	 *         struts-config.xml
	 * 
	 * @throws SwtException
	 *             Throw exception, if any
	 */
	public String unspecified()
			throws SwtException {
HttpServletRequest request = ServletActionContext.getRequest();
HttpServletResponse response = ServletActionContext.getResponse();

		log.debug(this.getClass().getName() + "- [unspecified] - Entering ");
		return displayList();
	}

	/**
	 * This method is used to display the Intraday Balance details
	 * 
	 * @param mapping
	 * 
	 * @param form
	 * 
	 * @param request
	 * 
	 * @param response
	 * 
	 * @return ActionForward which will specify the JSP to which the control
	 *         will be transferred using the mapping defined in the
	 *         struts-config.xml
	 * 
	 * @throws SwtException
	 *             Throw exception, if any
	 */
	public String displayList()
			throws SwtException {
HttpServletRequest request = ServletActionContext.getRequest();
HttpServletResponse response = ServletActionContext.getResponse();

		log.debug(this.getClass().getName() + "- [displayList] - Entering ");
		/* Local variable declaration and initialization */
		String hostId = null;
		String entityId = null;
		String reportDate = null;
		String currencyCode = null;
		String entityText = null;
		Calendar cal;
		Date date;
		/*
		 * Struts Framework built-in Class used to set and get the Form(JSP)
		 * values
		 */
// To remove: 		DynaValidatorForm dyForm = (DynaValidatorForm) form;
		/* Gets the instance from struts-config.xml file */
		IntradayBalances intradaybalances = (IntradayBalances) getIntradaybalances();

		try {
			/* Getting the hostId from swtcommon.properties file */
			hostId = CacheManager.getInstance().getHostId();
			/*
			 * Getting the entityId from request.If it is null,getting the User
			 * current entityId from session
			 */
			entityId = ((request.getParameter("entityId") != null) && (request
					.getParameter("entityId").trim().length() > 0)) ? request
					.getParameter("entityId") : SwtUtil
					.getUserCurrentEntity(request.getSession());
			entityText = request.getParameter("entityText");
			/* Sets the entityId in intradaybalances bean object */
			intradaybalances.getId().setEntityId(entityId);
			/* Puts the EntityAccess rights into request object */
			putEntityAccessInReq(request, entityId);
			/*
			 * Puts the list of currencies in request object and assign the
			 * default domestic currency
			 */
			currencyCode = putCurrencyListInReq(request, hostId, entityId);
			/* Sets the currencyCode in intradaybalances bean object */
			intradaybalances.setCurrencyCode(currencyCode);
			/* Sets the entity in request object */
			request.setAttribute("entityName", entityId);
			request.setAttribute("entityText", entityText);
			/* Puts the list of entities into request object */
			putEntityListInReq(request);
			/* Puts the today date into request object for date validation */
			putTodayDateInReq(request);
			/* Gets the yesterday date */
			cal = Calendar.getInstance();
			cal.setTime(SwtUtil.getSysParamDateWithEntityOffset(entityId));
			cal.add(Calendar.DATE, -1);
			date = cal.getTime();
			/* Format the date for Current System Formats */
			reportDate = SwtUtil.formatDate(date, SwtUtil
					.getCurrentSystemFormats(request.getSession())
					.getDateFormatValue());
			/* Sets the date in intradaybalances bean object */
			intradaybalances.setDateAsString(reportDate);
			/* sets the bean object in dynaForm */
			setIntradaybalances(intradaybalances);

			log.debug(this.getClass().getName() + "- [displayList] - Exiting ");

			return ("success");
		} catch (Exception exp) {
			log.debug(this.getClass().getName()
					+ "- [displayList] - Exception " + exp.getMessage());
			log.error(this.getClass().getName()
					+ "- [displayList] - Exception " + exp.getMessage());
			exp.printStackTrace();
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "displayList", IntradayBalanceAction.class), request,
					"");

			return ("fail");
		}
	}

	/**
	 * This method is used to display the Intraday Balance Child Screen
	 * 
	 * @param mapping
	 * 
	 * @param form
	 * 
	 * @param request
	 * 
	 * @param response
	 * 
	 * @return ActionForward which will specify the JSP to which the control
	 *         will be transferred using the mapping defined in the
	 *         struts-config.xml
	 * 
	 * @throws SwtException
	 *             Throw exception, if any
	 */
	public String showAccountDetails() throws SwtException {
HttpServletRequest request = ServletActionContext.getRequest();
HttpServletResponse response = ServletActionContext.getResponse();

		log.debug(this.getClass().getName()
				+ "- [showAccountDetails] - Entering ");
		/* Local variable declaration and initialization */
		String hostId = null;
		String entityId = null;
		String entityName = null;
		String currencyName = null;
		String selectedCurrencyCode = null;
		String selectedReportDate = null;
		Collection accountIdList = null;
		 /* Start: Code added by Kalidass G for Mantis 827 on 09-08-2010 */
		    ArrayList accountIdListGraph = null;
		    /* End: Code added by Kalidass G for Mantis 827 on 09-08-2010 */
		String rawData = null;
		/*
		 * Struts Framework built-in Class used to set and get the Form(JSP)
		 * values
		 */
// To remove: 		DynaValidatorForm dyForm = (DynaValidatorForm) form;
		/* Create the instance for ReasonMaintenance */
		IntradayBalances intradaybalances = new IntradayBalances();
		try {
			/* Getting the hostId from swtcommon.properties file */
			hostId = CacheManager.getInstance().getHostId();
			/*
			 * Getting the entityId , entityName , selectedCurrencyCode and
			 * selectedReportDate from request
			 */
			entityId = request.getParameter("entityId");
			entityName = request.getParameter("entityText");
			currencyName = request.getParameter("currencyText");
			selectedCurrencyCode = request.getParameter("selectedCurrencyCode");
			selectedReportDate = request.getParameter("selectedReportDate");
			rawData = request.getParameter("selectedRawdata");
			/* Sets the hostId in intradaybalances bean object */
			intradaybalances.getId().setHostId(hostId);
			/* Sets the entityId in intradaybalances bean object */
			intradaybalances.getId().setEntityId(entityId);
			/* Getting the collection of accountId from Manager */
			accountIdList = intradayBalanceManager.getMainAccountDetails(
					hostId, entityId, selectedCurrencyCode);
			request.setAttribute("accountIdListSize", accountIdList.size());
			 /* Start: Code modified by Kalidass G for Mantis 827 on 09-08-2010 */
		    	 if(rawData!= null && rawData.equals("Y")){
		    		 request.setAttribute("accountIdList", accountIdList);
		    	 }else{
		    		 accountIdListGraph = (ArrayList)accountIdList;
		    		 accountIdListGraph.add(0,new LabelValueBean(SwtConstants.ALL_LABEL,SwtConstants.ALL_VALUE));
		    		 request.setAttribute("accountIdList", accountIdListGraph);
		    	 }
		    	 /* End: Code modified by Kalidass G for Mantis 827 on 09-08-2010 */			
			/* Sets the bean object in dyna form */
			setIntradaybalances(intradaybalances);
			/*
			 * Sets the entityId ,selectedCurrencyCode and selectedReportDate in
			 * request object
			 */
			request.setAttribute("entityId", entityId);
			request.setAttribute("selectedCurrencyCode", selectedCurrencyCode);
			request.setAttribute("selectedReportDate", selectedReportDate);
			request.setAttribute("rawData", rawData);
			request.setAttribute("entityName", entityName);
			request.setAttribute("currencyName", currencyName);

		} catch (Exception exp) {
			log.debug(this.getClass().getName()
					+ "- [showAccountDetails] - Exception " + exp.getMessage());
			log.error(this.getClass().getName()
					+ "- [showAccountDetails] - Exception " + exp.getMessage());
			exp.printStackTrace();
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "add", IntradayBalanceAction.class), request, "");
			return ("fail");
		}
		log.debug(this.getClass().getName()
				+ "- [showAccountDetails] - Exiting ");
		return ("showacctdetails");
	}

	/**
	 * This method will accept the input from the user and send the generated
	 * report to the user
	 * 
	 * @param mapping
	 * 
	 * @param form
	 * 
	 * @param request
	 * 
	 * @param response
	 * 
	 * @return a Result of type Action forward
	 * 
	 * 
	 */
	public String report()
			throws Exception {

		log.debug(this.getClass().getName() + "- [report] - Entering ");
		// Declaring the Dynavalidator Form
// To remove: 		DynaValidatorForm dyForm;
		// Declaring the report
		Reports report;
		// Declaring the jasperPrint
		JasperPrint jasperPrint = null;
		// Declaring the local variables to get the value from Dynavalidator
		// Form
		String hostId;
		String entityId;
		String reportDate;
		String accountId;
		String currencyCode;
		String entityName;
		String currencyName;
		JRXlsExporter exporter;
		String rawData = null;
		JRPdfExporter pdfexporter;
		HttpServletRequest request = ServletActionContext.getRequest();
		HttpServletResponse response = ServletActionContext.getResponse();
		// Intialising the Dynavalidator Form
// To remove: 		dyForm = (DynaValidatorForm) form;
		/* Start code: Code added for Mantis 942 by Betcy on 12-Jun-10 */
		// Declare virtualizer
		JRSwapFileVirtualizer virtualizer = null;
		/* End code: Code added for Mantis 942 by Betcy on 12-Jun-10 */
		// Get the reports from Dynavalidator Form
		IntradayBalances intradaybalances = (IntradayBalances) getIntradaybalances();

		// Intiasing the outputstream
		ServletOutputStream out = response.getOutputStream();
		try {
			/* Getting the hostId from swtcommon.properties file */
			hostId = CacheManager.getInstance().getHostId();
			/*
			 * Getting the entityId ,reportDate,currencyCode and rawData from
			 * request
			 */
			entityId = request.getParameter("entityId");
			entityName = request.getParameter("entityText");
			currencyName = request.getParameter("currencyText");
			reportDate = request.getParameter("selectedReportDate");
			currencyCode = request.getParameter("selectedCurrencyCode");
			rawData = request.getParameter("selectedRawdata");
			/* Getting the accountId from intradaybalances bean object */
			accountId = intradaybalances.getId().getAccountId();
			/* Start code: Code added for Mantis 942 by Betcy on 12-Jun-10 */
			// create the swap file in temp dir folder when jasper report is
			// filling
			JRSwapFile swapFile = new JRSwapFile(System
					.getProperty("java.io.tmpdir"), 2048, 1024);
			// Instantiate the virtualizer
			virtualizer = new JRSwapFileVirtualizer(1, swapFile);
			// To get the filled report form reportsManager
			jasperPrint = intradayBalanceManager
					.getIntradayBalancesReport(request, hostId, entityId,
							entityName, reportDate, accountId, currencyCode,
							currencyName, rawData, virtualizer);
			/* End code: Code added for Mantis 942 by Betcy on 12-Jun-10 */
			// Initializing th JRXlsExporter.
			exporter = new JRXlsExporter();
			// To set the output type as xls file
			response.setContentType("application/xls");
			// To set the content as attachment
			/*
			 * Start code: Code modified for Mantis 763 by Kalidass G on
			 * 09-Jan-09
			 */
			response.setHeader("Content-disposition", "attachment; filename="
					+ jasperPrint.getName() + "-SmartPredict_"
					+ SwtUtil.formatDate(new Date(), "yyyyMMdd") + "_"
					+ SwtUtil.formatDate(new Date(), "HHmmss") + ".xls");
			/* End code: Code modified for Mantis 763 by Kalidass G on 09-Jan-09 */
			// To pass the filled report
			exporter
					.setParameter(JRExporterParameter.JASPER_PRINT, jasperPrint);
			// Exporting as UTF-8
			exporter.setParameter(JRExporterParameter.CHARACTER_ENCODING,
					"UTF-8");
			// Providing the output stream and exporting the report
			exporter.setParameter(JRExporterParameter.OUTPUT_STREAM, out);
			exporter.setParameter(JRXlsExporterParameter.IS_ONE_PAGE_PER_SHEET,
					Boolean.TRUE);
			exporter.setParameter(
					JRXlsExporterParameter.IS_REMOVE_EMPTY_SPACE_BETWEEN_ROWS,
					Boolean.TRUE);
			exporter
					.setParameter(
							JRXlsExporterParameter.IS_REMOVE_EMPTY_SPACE_BETWEEN_COLUMNS,
							Boolean.TRUE);
			//modified for jasper jar  upgrade-Mantis 1648
			exporter.setParameter(
					JRXlsExporterParameter.IS_DETECT_CELL_TYPE,
					Boolean.TRUE);
			exporter.exportReport();

		} catch (Exception exp) {

			log.debug(this.getClass().getName() + "- [report] - Exception "
					+ exp.getMessage());
			log.error(this.getClass().getName() + "- [report] - Exception "
					+ exp.getMessage());
			exp.printStackTrace();
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "report", IntradayBalanceAction.class), request, "");
			return ("fail");

		} finally {
			/* Start code: Code added for Mantis 942 by Betcy on 12-Jun-10 */
			// nullify objects
			jasperPrint = null;
			if (virtualizer != null) {
				// cleanup the virtualizer file
				virtualizer.cleanup();
				virtualizer = null;
			}
			/* End code: Code added for Mantis 942 by Betcy on 12-Jun-10 */
		}

		log.debug(this.getClass().getName() + "- [report] - Exiting ");
		return null;
	}

	/**
	 * This method will accept the input from the user and generate the graph
	 * report to the user
	 * 
	 * @param mapping
	 * 
	 * @param form
	 * 
	 * @param request
	 * 
	 * @param response
	 * 
	 * @return a Result of type Action forward
	 * 
	 * 
	 */
	public String reportForGraph()
			throws Exception {

		log.debug(this.getClass().getName() + "- [reportForGraph] - Entering ");
		// Declaring the Dynavalidator Form
// To remove: 		DynaValidatorForm dyForm;
		// Declaring the report
		// Reports report;
		// Declaring the jasperPrint
		JasperPrint jasperPrint = null;
		// Declaring the local variables to get the value from Dynavalidator
		// Form
		String hostId;
		String entityId;
		String reportDate;
		String accountId;
		String currencyCode;
		String entityName;
		String currencyName;
		JRPdfExporter exporter;
		String rawData = null;
		HttpServletRequest request = ServletActionContext.getRequest();
		HttpServletResponse response = ServletActionContext.getResponse();
		// Intialising the Dynavalidator Form
// To remove: 		dyForm = (DynaValidatorForm) form;
		/* Start code: Code added for Mantis 942 by Betcy on 12-Jun-10 */
		// Declare virtualizer
		JRSwapFileVirtualizer virtualizer = null;
		/* End code: Code added for Mantis 942 by Betcy on 12-Jun-10 */
		// Get the reports from Dynavalidator Form
		IntradayBalances intradaybalances = (IntradayBalances) getIntradaybalances();

		// Intiasing the outputstream
		ServletOutputStream out = response.getOutputStream();
		try {
			/* Getting the hostId from swtcommon.properties file */
			hostId = CacheManager.getInstance().getHostId();
			/*
			 * Getting the entityId ,reportDate,currencyCode and rawData from
			 * request
			 */
			entityId = request.getParameter("entityId");
			entityName = request.getParameter("entityText");
			currencyName = request.getParameter("currencyText");
			reportDate = request.getParameter("selectedReportDate");
			currencyCode = request.getParameter("selectedCurrencyCode");
			rawData = request.getParameter("selectedRawdata");
			/* Getting the accountId from intradaybalances bean object */
			accountId = intradaybalances.getId().getAccountId();
			/* Start code: Code added for Mantis 942 by Betcy on 12-Jun-10 */
			// create the swap file in temp dir folder when jasper report is
			// filling
			JRSwapFile swapFile = new JRSwapFile(System
					.getProperty("java.io.tmpdir"), 2048, 1024);
			// Instantiate the virtualizer
			virtualizer = new JRSwapFileVirtualizer(1, swapFile);
			// To get the filled report form reportsManager
			jasperPrint = intradayBalanceManager
					.getIntradayBalancesReport(request, hostId, entityId,
							entityName, reportDate, accountId, currencyCode,
							currencyName, rawData, virtualizer);
			/* End code: Code added for Mantis 942 by Betcy on 12-Jun-10 */
			// Intialising th ePDFExporter.
			exporter = new JRPdfExporter();
			// To set the output type as PDF file
			response.setContentType("application/pdf");
			// To set the content as attachment
			/*
			 * Start code: Code modified for Mantis 763 by Kalidass G on
			 * 09-Jan-09
			 */
			response.setHeader("Content-disposition", "attachment; filename="
					+ jasperPrint.getName() + "-SmartPredict_"
					+ SwtUtil.formatDate(new Date(), "yyyyMMdd") + "_"
					+ SwtUtil.formatDate(new Date(), "HHmmss") + ".pdf");
			/* End code: Code modified for Mantis 763 by Kalidass G on 09-Jan-09 */
			// To pass the fillled report
			exporter
					.setParameter(JRExporterParameter.JASPER_PRINT, jasperPrint);
			// Providing the output stream
			exporter.setParameter(JRExporterParameter.OUTPUT_STREAM, out);
			// Exporting as UTF-8
			exporter.setParameter(JRExporterParameter.CHARACTER_ENCODING,
					"UTF-8");
			// Export Report
			exporter.exportReport();

		} catch (Exception exp) {

			log.debug(this.getClass().getName()
					+ "- [reportForGraph] - Exception " + exp.getMessage());
			log.error(this.getClass().getName()
					+ "- [reportForGraph] - Exception " + exp.getMessage());
			exp.printStackTrace();
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "report", IntradayBalanceAction.class), request, "");
			return ("fail");

		} finally {
			/* Start code: Code added for Mantis 942 by Betcy on 12-Jun-10 */
			// nullify objects
			jasperPrint = null;
			if (virtualizer != null) {
				// cleanup the virtualizer files
				virtualizer.cleanup();
				virtualizer = null;
			}
			/* End code: Code added for Mantis 942 by Betcy on 12-Jun-10 */
		}

		log.debug(this.getClass().getName() + "- [reportForGraph] - Exiting ");
		return null;
	}

	/**
	 * This method is used to put today date into request object.
	 * 
	 * @param request
	 * @throws SwtException
	 */

	private void putTodayDateInReq(HttpServletRequest request)
			throws SwtException {
		log.debug(this.getClass().getName()
				+ "- [putTodayDateInReq] - Entering ");

		/* Date variable to hold the test date */
		Date testDate = null;
		/* String variable to hold the today date */
		String today = null;
		/* Returns the test date used by the system */
		testDate = SwtUtil.getTestDateFromParams(SwtUtil.getCurrentHostId());
		/* Format the date for currenct system parameter */
		today = SwtUtil.formatDate(testDate, SwtUtil.getCurrentSystemFormats(
				request.getSession()).getDateFormatValue());
		/* Sets the today date in request object */
		request.setAttribute("today", today);
		log.debug(this.getClass().getName()
				+ "- [putTodayDateInReq] - Exiting ");
	}

	/**
	 * This method is used to put the list of entities in request object
	 * 
	 * @param request
	 * 
	 * @throws SwtException
	 *             Throw exception, if any
	 */
	private void putEntityListInReq(HttpServletRequest request)
			throws SwtException {
		log.debug(this.getClass().getName()
				+ "- [putEntityListInReq] - Entering ");
		/* Gets the current session */
		HttpSession session = request.getSession();
		/* Create an instance for Collection */
		Collection entities = new ArrayList();
		/* Gets the list of entities */
		entities = SwtUtil.getUserEntityAccessList(session);
		/* Convert to Label Value bean */
		entities = SwtUtil.convertEntityAcessCollectionLVL(entities, session);
		/* Sets the entities in request object */
		request.setAttribute("entities", entities);
		log.debug(this.getClass().getName()
				+ "- [putEntityListInReq] - Exiting ");
	}

	/**
	 * This method is used to put the Entity Access rights in request object
	 * 
	 * @param request -
	 *            HttpServletRequest
	 * 
	 * @param entityId -
	 *            String
	 * 
	 * @return int
	 * 
	 * @throws SwtException
	 *             Throw exception, if any
	 */
	private int putEntityAccessInReq(HttpServletRequest request, String entityId)
			throws SwtException {
		log.debug(this.getClass().getName()
				+ "- [putEntityAccessInReq] - Entering ");
		/* Used to get the list of entity access */
		Collection coll = SwtUtil.getUserEntityAccessList(request.getSession());
		/* Used to get the Menu Entity Access */
		int accessInd = SwtUtil.getMenuEntityCurrGrpAccess(request, entityId,
				null);
		/* sets the EntityAccess in request object */
		if (accessInd == 0) {
			request.setAttribute("EntityAccess",
					SwtConstants.ENTITY_FULL_ACCESS + "");
		} else {
			request.setAttribute("EntityAccess",
					SwtConstants.ENTITY_READ_ACCESS + "");
		}
		log.debug(this.getClass().getName()
				+ "- [putEntityAccessInReq] - Exiting ");
		return accessInd;
	}

	/**
	 * This method is used to put the list of currency in request object
	 * 
	 * @param request -
	 *            HttpServletRequest
	 * 
	 * @param hostId -
	 *            String
	 * 
	 * @param entityId -
	 *            String
	 * 
	 * @return String
	 * 
	 * @throws SwtException
	 *             Throw exception, if any
	 */
	private String putCurrencyListInReq(HttpServletRequest request,
			String hostId, String entityId) throws SwtException {
		log.debug(this.getClass().getName()
				+ "- [putCurrencyListInReq] - Entering ");
		/* String variable to hold default currency */
		String defaultCurrency = null;
		/* String variable to hold the roldId */
		String roleId = null;
		/* String variable to hold the domesticCurrency */
		String domesticCurrency = null;
		/* Collection object to hold all currencies in dropdown box */
		Collection currencyDropDown = null;
		/* Collection object to hold all currencies with ALLin dropdown box */
		Collection currrencyListWithAll = null;
		/* Getting the user's roleId from session */
		roleId = ((CommonDataManager) request.getSession().getAttribute(
				SwtConstants.CDM_BEAN)).getUser().getRoleId();
		/* This is used to get all currencies in dropdown box */
		currencyDropDown = SwtUtil.getSwtMaintenanceCache()
				.getCurrencyViewORFullAcessLVL(roleId, entityId);
		/*
		 * If the currencies is not null,it will remove the default currency
		 * from the dropdown list
		 */
		if (currencyDropDown != null) {
			currencyDropDown.remove(new LabelValueBean("Default", "*"));
		}

		/* Creates an instance for an ArrayList */
		currrencyListWithAll = new ArrayList();
		/* Showing 'All' in Currency drop down */
		currrencyListWithAll.add(new LabelValueBean(SwtConstants.ALL_LABEL,
				SwtConstants.ALL_VALUE));

		/*
		 * If the currencies in the dropdown list is not equal to null, it will
		 * add all currencies with ALL .
		 */
		if (currencyDropDown != null) {
			currrencyListWithAll.addAll(currencyDropDown);
		}

		/* Sets the currencyList With All in request object */
		request.setAttribute("currencies", currrencyListWithAll);

		/* Getting the domestic currency for user from db */
		domesticCurrency = SwtUtil.getDomesticCurrencyForUser(request, hostId,
				entityId);
		/*
		 * If the domestic currency is not equal to null, it assigns the
		 * domestic currency to default currency for display to the user.
		 */
		if (domesticCurrency != null) {
			defaultCurrency = domesticCurrency;
		}

		log.debug(this.getClass().getName()
				+ "- [putCurrencyListInReq] - Exiting ");

		/* Returns the defaultCurrency */
		return defaultCurrency;
	}

}