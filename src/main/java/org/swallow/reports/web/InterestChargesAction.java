/*
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.reports.web;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collection;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;

import net.sf.jasperreports.engine.JRExporterParameter;
import net.sf.jasperreports.engine.JasperPrint;
import net.sf.jasperreports.engine.export.JRPdfExporter;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.maintenance.dao.hibernate.SysParamsDAOHibernate;
import org.swallow.reports.model.InterestCharges;
import org.swallow.reports.service.InterestChargesManager;
import org.swallow.util.CacheManager;
import org.swallow.util.CommonDataManager;
import org.swallow.util.SwtConstants;
import org.swallow.util.SwtUtil;

import com.thoughtworks.xstream.XStream;
import org.swallow.util.LabelValueBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.apache.struts2.convention.annotation.Result;
import org.apache.struts2.convention.annotation.AllowedMethods;
import org.apache.struts2.convention.annotation.Action;
import org.apache.struts2.ServletActionContext;
import org.swallow.util.struts.CustomActionSupport;
import com.thoughtworks.xstream.io.xml.StaxDriver;
/**
 * 
 * <AUTHOR> A. Class that extends the CustomActionSupport .
 * 
 */
@Action(value = "/interestCharges", results = {
	@Result(name = "success", location = "/jsp/reports/interestCharges.jsp"),
	@Result(name = "fail", location = "/error.jsp"),
})

@AllowedMethods ({"displayList" ,"report" })
public class InterestChargesAction extends CustomActionSupport {
public String execute() throws Exception {
    HttpServletRequest request = ServletActionContext.getRequest();

    // List of methods 
    String method = String.valueOf(request.getParameter("method"));

    switch (method) {
        case "unspecified":
            return unspecified();
        case "displayList":
            return displayList();
        case "report":
            return report();
        default:
            break;
    }

    return unspecified();
}


private InterestCharges interestcharges;
public InterestCharges getInterestcharges() {
	if (interestcharges == null) {
		interestcharges = new InterestCharges();
	}
	return interestcharges;
}
public void setInterestcharges(InterestCharges interestcharges) {
	this.interestcharges = interestcharges;
	HttpServletRequest request = ServletActionContext.getRequest();
	request.setAttribute("interestcharges", interestcharges);
}


	/*
	 * Final instance for Log
	 */
	private final Log log = LogFactory.getLog(InterestChargesAction.class);

	/**
	 * Manager class instance
	 */
	@Autowired
private InterestChargesManager interestChargesManager = null;

	/**
	 * Manager class instance to set
	 * 
	 * @param interestChargesManager
	 */
	public void setInterestChargesManager(
			InterestChargesManager interestChargesManager) {
		this.interestChargesManager = interestChargesManager;
	}

	/**
	 * Default method which will be called
	 * 
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return ActionForward which will specify the JSP to which the control
	 *         will be transferred using the mapping defined in the
	 *         struts-config.xml
	 * @throws SwtException
	 * 
	 */
	public String unspecified()
			throws SwtException {
HttpServletRequest request = ServletActionContext.getRequest();
HttpServletResponse response = ServletActionContext.getResponse();

		log.debug(this.getClass().getName() + "- [unspecified] - Entering ");
		return displayList();
	}

	/*
	 * Start:Code Modified For Mantis 1622 by Sudhakar on 20-09-2012: Interest
	 * Charges by Account Report: From and To date range instead of Month/Year
	 */
	/**
	 * This method is used to display the Interest Charges screen details
	 * 
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return ActionForward which will specify the JSP to which the control
	 *         will be transferred using the mapping defined in the
	 *         struts-config.xml
	 * @throws SwtException
	 * 
	 */
	public String displayList()
			throws SwtException {
HttpServletRequest request = ServletActionContext.getRequest();
HttpServletResponse response = ServletActionContext.getResponse();

		// To get form values from jsp
// To remove: 		DynaValidatorForm dyForm = null;
		// To hold interest charges per account screen details
		InterestCharges interestCharges = null;
		// To hold the current system date
		Calendar systemDate = null;
		// Application host id
		String hostId = null;
		// Logged-in user's entity id
		String entityId = null;
		// interest charges currency code
		String currencyCode = null;
		// To hold the list of entity account list
		Collection<LabelValueBean> accountList = null;
		// To get the current user session
		HttpSession session = null;
		// To hold the previous month From date
		Date firstDateOfPreviousMonth = null;
		// To hold the previous month To date
		Date lastDateOfPreviousMonth = null;
		// To hold the CommonDataManager object
		CommonDataManager commonDataManager = null;
		// To hold the selected entity/currency values
		String status = null;
		String configScheduler = null;
		String newInterestChargesReportSchedConfig = null;
		String schedulerConfigXML = null;
		String reportType ;
		String dateFormat = null;
		try {
			log
					.debug(this.getClass().getName()
							+ "- [displayList] - Entering ");
			/*
			 * Struts Framework built-in Class used to set and get the Form(JSP)
			 * values
			 */
			
			configScheduler = request.getParameter("configScheduler");
			newInterestChargesReportSchedConfig = request.getParameter("newInterestChargesReportSchedConfig");
			reportType = request.getParameter("reportType");
			schedulerConfigXML = request.getParameter("schedulerConfigXML");
// To remove: 			dyForm = (DynaValidatorForm) form;
			/* Gets the instance from struts-config.xml file */
			interestCharges = (InterestCharges) getInterestcharges();
			/* Getting the hostId from swtcommon.properties file */
			hostId = CacheManager.getInstance().getHostId();
			
			if (!SwtUtil.isEmptyOrNull(configScheduler) && ("true".equals(configScheduler))) {
				if (!SwtUtil.isEmptyOrNull(newInterestChargesReportSchedConfig) && "false".equals(newInterestChargesReportSchedConfig)) {
					HashMap<String, String> schedulerConfigMap;
					schedulerConfigXML = SwtUtil.decode64(request.getParameter("schedulerConfigXML"));
					schedulerConfigMap = convertschedulerConfigXMLtoHashmap(schedulerConfigXML);
					entityId = schedulerConfigMap.get("entityid");
					currencyCode = schedulerConfigMap.get("currencycode");
					dateFormat = schedulerConfigMap.get("dateformatasstring");
					
				}
				
			}
			
			
			/*
			 * Getting the entityId from request.If it is null,getting the User
			 * current entityId from session
			 */
			entityId = SwtUtil.isEmptyOrNull(request.getParameter("entityId"))
					? SwtUtil.isEmptyOrNull(entityId) ? SwtUtil.getUserCurrentEntity(request.getSession()) : entityId
					: request.getParameter("entityId");

			/* Sets the entityId in interestCharges bean object */
			interestCharges.getId().setEntityId(entityId);
			/* Puts the EntityAccess rights into request object */
			putEntityAccessInReq(request, entityId);
			/*
			 * Puts the list of currencies in request object and assign the
			 * default domestic currency
			 */
			
			/*
			 * Puts the list of currencies in request object and assign the
			 * default domestic currency
			 */
			if(SwtUtil.isEmptyOrNull(currencyCode)) {
				currencyCode = putCurrencyListInReq(request, hostId, entityId);
			}else {
				putCurrencyListInReq(request, hostId, entityId);
			}
			

			// Get the selected combo box value Entity/Currency in request
			status = request.getParameter("status");
			// if currency combo box is selected, Set the currency code
			if (!SwtUtil.isEmptyOrNull(status) && status.equals("currency")) {
				currencyCode = request.getParameter("currencyId");
			}
			
		
			
			/* Sets the currencyCode in interestCharges bean object */
			interestCharges.setCurrencyCode(currencyCode);
			/* Getting the current user session */
			session = request.getSession();
			commonDataManager = (CommonDataManager) session
					.getAttribute(SwtConstants.CDM_BEAN);
			systemDate = Calendar.getInstance();
			// Get the current system date
			
			systemDate.setTime(SwtUtil.getSysParamDateWithEntityOffset(entityId));
			// Subtract the calendar month to display previous month date(from,
			// to date) in
			// screen
			systemDate.add(Calendar.MONTH, -1);
			// Set first day of month
			systemDate.set(Calendar.DATE, 1);
			// Get the first Date Of PreviousMonth
			firstDateOfPreviousMonth = systemDate.getTime();
			// Set last day of month
			systemDate.set(Calendar.DATE, systemDate
					.getActualMaximum(systemDate.DATE));
			// Get the last Date Of PreviousMonth
			lastDateOfPreviousMonth = systemDate.getTime();
			/* Sets the From Date and To date in interestCharges bean object */
			interestCharges.setFromDate(SwtUtil.formatDate(
					firstDateOfPreviousMonth, commonDataManager
							.getDateFormatValue()));
			interestCharges.setToDate(SwtUtil.formatDate(
					lastDateOfPreviousMonth, commonDataManager
							.getDateFormatValue()));
			accountList = interestChargesManager.getMainAccountDetails(hostId,
					entityId, currencyCode);
		
			
			if (!SwtUtil.isEmptyOrNull(configScheduler) && ("true".equals(configScheduler))) {
				if (!SwtUtil.isEmptyOrNull(newInterestChargesReportSchedConfig) && "false".equals(newInterestChargesReportSchedConfig)) {
					HashMap<String, String> schedulerConfigMap;
					schedulerConfigXML = SwtUtil.decode64(request.getParameter("schedulerConfigXML"));
					schedulerConfigMap = convertschedulerConfigXMLtoHashmap(schedulerConfigXML);
					interestCharges = new InterestCharges();
					interestCharges.getId().setEntityId(schedulerConfigMap.get("entityid"));
					interestCharges.setCurrencyCode(schedulerConfigMap.get("currencycode"));
					interestCharges.setAccountName(schedulerConfigMap.get("accountid"));
					
					if(!SwtUtil.isEmptyOrNull(dateFormat) && !SwtUtil.isEmptyOrNull(schedulerConfigMap.get("fromdate"))) {
						String fromDateToSet = null;
						if(SysParamsDAOHibernate.keywordQuery.containsKey(schedulerConfigMap.get("fromdate"))) {
							fromDateToSet = schedulerConfigMap.get("fromdate");
						}else {
							Date fromDate = SwtUtil.parseDate(schedulerConfigMap.get("fromdate"), dateFormat);
							fromDateToSet = SwtUtil.formatDate(fromDate, SwtUtil.getCurrentDateFormat(request.getSession()));
						}
						interestCharges.setFromDate(fromDateToSet);
					}else {
						interestCharges.setFromDate(schedulerConfigMap.get("fromdate"));
					}
					
					
					if(!SwtUtil.isEmptyOrNull(dateFormat) && !SwtUtil.isEmptyOrNull(schedulerConfigMap.get("todate"))) {
						
						String toDateToSet = null;
						if(SysParamsDAOHibernate.keywordQuery.containsKey(schedulerConfigMap.get("todate"))) {
							toDateToSet = schedulerConfigMap.get("todate");
						}else {
							Date toDate = SwtUtil.parseDate(schedulerConfigMap.get("todate"), dateFormat);
							toDateToSet = SwtUtil.formatDate(toDate, SwtUtil.getCurrentDateFormat(request.getSession()));
						}
						
						interestCharges.setToDate(toDateToSet);
					}else {
						interestCharges.setToDate(schedulerConfigMap.get("todate"));
					}
					
					
				}
				
			}
			
			
			// adding into label bean
			request.setAttribute("keywords", SwtUtil.getKeywords(request));
			request.setAttribute("configScheduler", configScheduler);
			request.setAttribute("newInterestChargesReportSchedConfig", newInterestChargesReportSchedConfig);
			request.setAttribute("reportType",reportType);	
			request.setAttribute("schedulerConfigXML", schedulerConfigXML);
			request.setAttribute("jobId", request.getParameter("jobId"));
			
			/* Set the accountdetails in request object */
			request.setAttribute("accountdetails", accountList);
			/* Puts the list of entities into request object */
			putEntityListInReq(request);
			/* Sets the bean object in dynaForm */
			setInterestcharges(interestCharges);
			log.debug(this.getClass().getName() + "- [displayList] - Exiting ");
			return ("success");
		} catch (SwtException swtexp) {
			log.error(this.getClass().getName()
					+ "- [displayList] - Exception " + swtexp.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					swtexp, "displayList", InterestChargesAction.class),
					request, "");

			return ("fail");
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					+ "- [displayList] - Exception " + exp.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "displayList", InterestChargesAction.class), request,
					"");
			return ("fail");
		} finally {
			// nullify the objects
			interestCharges = null;
			systemDate = null;
			hostId = null;
			entityId = null;
			currencyCode = null;
			accountList = null;
			commonDataManager = null;
			lastDateOfPreviousMonth = null;
			firstDateOfPreviousMonth = null;
			status = null;
		}
	}

	/**
	 * This method will accept the input interest charges per account screen
	 * given by the user and generate the report to the user
	 * 
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return a Result of type Action forward
	 * @throws SwtException
	 * 
	 */
	public String report()
			throws SwtException {
HttpServletRequest request = ServletActionContext.getRequest();
HttpServletResponse response = ServletActionContext.getResponse();

		// To hold filled report form reportsManager
		JasperPrint jasperPrint = null;
		// Application host id
		String hostId = null;
		// To hold the selected entity Id
		String entityId = null;
		// To hold the selected entityName
		String entityName = null;
		// To hold the selected currencyId
		String currencyId = null;
		// To hold the selected currencyName
		String currencyName = null;
		// To hold the selected accountId
		String accountId = null;
		// To hold the selected accountName
		String accountName = null;
		// To hold the selected fromDate
		String fromDate = null;
		// To hold the selected toDate
		String toDate = null;
		// To hold to export the jasperPrint details
		JRPdfExporter exporter = null;
		// To hold the interest charges screen details
		ServletOutputStream servletOutput = null;
		try {
			log.debug(this.getClass().getName() + "- [report] - Entering ");
			/* Getting the hostId from swtcommon.properties file */
			hostId = CacheManager.getInstance().getHostId();
			// get the interest charges screen details
			servletOutput = response.getOutputStream();
			/*
			 * Getting the entityId ,currencyCode, accountId, fromdate and
			 * todate from request
			 */
			entityId = request.getParameter("entityId");
			entityName = request.getParameter("entityText");
			currencyId = request.getParameter("currencyId");
			currencyName = request.getParameter("currencyText");
			accountId = request.getParameter("accountId");
			accountName = request.getParameter("accountText");
			fromDate = request.getParameter("fromDate");
			toDate = request.getParameter("toDate");
			/* To get the filled report form reportsManager */
			jasperPrint = interestChargesManager.getInterestChargesReport(
					request, hostId, entityId, entityName, currencyId,
					currencyName, accountId, accountName, fromDate, toDate, null);
			/* Initializing the PDFExporter. */
			exporter = new JRPdfExporter();
			/* To set the output type as PDF file */
			response.setContentType("application/pdf");
			/* To set the content as attachment */
			response.setHeader("Content-disposition", "attachment; filename="
					+ jasperPrint.getName() + "-SmartPredict_"
					+ SwtUtil.formatDate(new Date(), "yyyyMMdd") + "_"
					+ SwtUtil.formatDate(new Date(), "HHmmss") + ".pdf");
			/* To pass the filled report */
			exporter
					.setParameter(JRExporterParameter.JASPER_PRINT, jasperPrint);
			/* Providing the output stream */
			exporter.setParameter(JRExporterParameter.OUTPUT_STREAM,
					servletOutput);
			/* Exporting as UTF-8 */
			exporter.setParameter(JRExporterParameter.CHARACTER_ENCODING,
					"UTF-8");
			/* Export Report */
			exporter.exportReport();
			log.debug(this.getClass().getName() + "- [report] - Exiting ");
		} catch (SwtException swtexp) {
			log.error(this.getClass().getName() + "- [report] - Exception "
					+ swtexp.getMessage());
			SwtUtil
					.logException(SwtErrorHandler.getInstance()
							.handleException(swtexp, "report",
									InterestChargesAction.class), request, "");

			return ("fail");
		} catch (Exception exp) {
			log.error(this.getClass().getName() + "- [report] - Exception "
					+ exp.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "report", InterestChargesAction.class), request, "");
			return ("fail");
		} finally {
			// nullify the objects
			jasperPrint = null;
			hostId = null;
			entityId = null;
			entityName = null;
			currencyId = null;
			currencyName = null;
			accountId = null;
			accountName = null;
			fromDate = null;
			toDate = null;
			exporter = null;
			servletOutput = null;
		}
		return null;
	}

	/*
	 * End:Code Modified For Mantis 1622 by Sudhakar on 20-09-2012: Interest
	 * Charges by Account Report: From and To date range instead of Month/Year
	 */
	/**
	 * This method is used to put the list of entities in request object
	 * 
	 * @param request
	 * @return none
	 * @throws SwtException
	 * 
	 */
	private void putEntityListInReq(HttpServletRequest request)
			throws SwtException {
		log.debug(this.getClass().getName()
				+ "- [putEntityListInReq] - Entering ");
		/* Method's local variable and class instance declaration */
		HttpSession session = null;
		Collection entities = null;

		/* Gets the current session */
		session = request.getSession();
		/* Create an instance for Collection */
		entities = new ArrayList();
		/* Gets the list of entities */
		entities = SwtUtil.getUserEntityAccessList(session);
		/* Convert to Label Value bean */
		entities = SwtUtil.convertEntityAcessCollectionLVL(entities, session);
		/* Sets the entities in request object */
		request.setAttribute("entities", entities);
		log.debug(this.getClass().getName()
				+ "- [putEntityListInReq] - Exiting ");
	}

	/**
	 * This method is used to put the Entity Access rights in request object
	 * 
	 * @param request -
	 *            HttpServletRequest
	 * @param entityId -
	 *            String
	 * @return int
	 * @throws SwtException
	 * 
	 */
	private int putEntityAccessInReq(HttpServletRequest request, String entityId)
			throws SwtException {
		log.debug(this.getClass().getName()
				+ "- [putEntityAccessInReq] - Entering ");
		/* Method's local variable and class instance declaration */
		Collection coll = null;
		int accessInd = 0;

		/* Used to get the list of entity access */
		coll = SwtUtil.getUserEntityAccessList(request.getSession());
		/* Used to get the Menu Entity Access */
		accessInd = SwtUtil.getMenuEntityCurrGrpAccess(request, entityId, null);
		/* Sets the EntityAccess in request object */
		if (accessInd == 0) {
			request.setAttribute("EntityAccess",
					SwtConstants.ENTITY_FULL_ACCESS + "");
		} else {
			request.setAttribute("EntityAccess",
					SwtConstants.ENTITY_READ_ACCESS + "");
		}
		log.debug(this.getClass().getName()
				+ "- [putEntityAccessInReq] - Exiting ");
		return accessInd;
	}

	/*
	 * Start:Code Modified For Mantis 1622 by Sudhakar on 03-09-2012: Interest
	 * Charges by Account Report: From and To date range instead of Month/Year
	 */
	/**
	 * This method is used to put the list of currency in request object and
	 * return domesticCurrency which will be displayed in currency combo box at
	 * launching the screen
	 * 
	 * @param request -
	 *            HttpServletRequest
	 * @param hostId -
	 *            String
	 * @param entityId -
	 *            String
	 * @return domesticCurrency- String
	 * @throws SwtException
	 * 
	 */
	private String putCurrencyListInReq(HttpServletRequest request,
			String hostId, String entityId) throws SwtException {

		// Login user role id
		String roleId = null;
		// To hold default currency which will be displayed in currency combo
		// box launching the screen
		String domesticCurrency = null;
		/* Collection object to hold all currencies in dropdown box */
		Collection<LabelValueBean> currencyDropDown = null;
		try {
			log.debug(this.getClass().getName()
					+ "- [putCurrencyListInReq] - Entering ");
			/* Getting the user's roleId from session */
			roleId = ((CommonDataManager) request.getSession().getAttribute(
					SwtConstants.CDM_BEAN)).getUser().getRoleId();

			/* This is used to get all currencies in dropdown box */
			currencyDropDown = SwtUtil.getSwtMaintenanceCache()
					.getCurrencyViewORFullAcessLVL(roleId, entityId);

			/*
			 * If the currencies is not null,it will remove the default currency
			 * from the dropdown list
			 */
			if (currencyDropDown != null) {
				currencyDropDown.remove(new LabelValueBean("Default", "*"));
			}
			/* Sets the currencyList in request object */
			request.setAttribute("currencies", currencyDropDown);
			/* Getting the domestic currency for user from db */
			domesticCurrency = SwtUtil.getDomesticCurrencyForUser(request,
					hostId, entityId);
			log.debug(this.getClass().getName()
					+ "- [putCurrencyListInReq] - Exiting ");
		} catch (SwtException swtexp) {
			log
					.error("SwtException Catch in InterestChargesAction.'putCurrencyListInReq' method : "
							+ swtexp.getMessage());
			SwtUtil.logException(swtexp, request, "");
			throw swtexp;
		} catch (Exception exp) {
			log
					.error("Exception Catch in InterestChargesAction.'putCurrencyListInReq' method : "
							+ exp.getMessage());
			throw new SwtException(exp.getMessage());

		} finally {
			// nullify objects
			roleId = null;
			currencyDropDown = null;
		}
		return domesticCurrency;
	}
	/*
	 * End:Code Modified For Mantis 1622 by Sudhakar on 03-09-2012: Interest
	 * Charges by Account Report: From and To date range instead of Month/Year
	 */
	
	public HashMap<String, String> convertschedulerConfigXMLtoHashmap(String schedulerConfigXML) {
		XStream xStream = new XStream(new StaxDriver());
		xStream.registerConverter(new SwtUtil.MapEntryConverter());
		xStream.alias("schedconfig", Map.class);
		xStream.setClassLoader(Thread.currentThread().getContextClassLoader());
		HashMap<String, String> extractedMap = (HashMap<String, String>) xStream.fromXML(schedulerConfigXML);
		return extractedMap;
	}

}