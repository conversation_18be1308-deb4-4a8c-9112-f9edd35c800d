/*
 * @(#)ReportsAction.java 1.0 23/08/06
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.reports.web;

import java.awt.*;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collection;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.thoughtworks.xstream.io.xml.StaxDriver;
import net.sf.jasperreports.engine.JRException;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFCellStyle;
import org.apache.poi.xssf.usermodel.XSSFColor;
import org.apache.poi.xssf.usermodel.XSSFSheet;

import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.exception.SystemExceptionHandler;
import org.swallow.maintenance.dao.HolidayDAO;
import org.swallow.maintenance.dao.hibernate.SysParamsDAOHibernate;
import org.swallow.maintenance.model.EntityUserAccess;
import org.swallow.maintenance.model.ILMCcyParameters;
import org.swallow.maintenance.model.ILMScenario;
import org.swallow.maintenance.service.ILMGeneralMaintenanceManager;
import org.swallow.maintenance.service.ILMTransScenarioMaintenanceManager;
import org.swallow.reports.model.Reports;
import org.swallow.reports.service.ReportsManager;
import org.swallow.util.CacheManager;
import org.swallow.util.CommonDataManager;
import org.swallow.util.PropertiesFileLoader;
import org.swallow.util.SwtConstants;
import org.swallow.util.SwtUtil;
import org.swallow.work.service.ILMAnalysisMonitorManager;

import com.thoughtworks.xstream.XStream;

import net.sf.jasperreports.engine.JRExporterParameter;
import net.sf.jasperreports.engine.JasperPrint;
import net.sf.jasperreports.engine.export.JRCsvExporter;
import net.sf.jasperreports.engine.export.JRPdfExporter;
import net.sf.jasperreports.engine.export.JRXlsExporter;
import net.sf.jasperreports.engine.export.JRXlsExporterParameter;
import net.sf.jett.transform.ExcelTransformer;
import org.swallow.util.struts.ActionMessages;
import org.swallow.util.struts.ActionMessage;
import org.swallow.util.LabelValueBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.apache.struts2.convention.annotation.Result;
import org.apache.struts2.convention.annotation.AllowedMethods;
import org.apache.struts2.convention.annotation.Action;
import org.apache.struts2.ServletActionContext;
import org.swallow.util.struts.CustomActionSupport;

@Action(value = "/ilmReport", results = {
@Result(name = "success", location = "/jsp/reports/ilmReport.jsp"),
@Result(name = "fail", location = "/error.jsp")
})

@AllowedMethods ({"OpportunityCostReport" ,"getOpportunityCostparameters" ,"displayList" ,"parameterReport" ,"isWeekend" ,"getMovementsParameters" ,"UnsettledMovementsReport" ,"ExcludedMovementsReport" ,"getILMReport" ,"ILMReport" ,"cancelILMExport" ,"isAllEntityAvailable" ,"reportInProgressValidation" ,"validateDates" ,"checkMissingData" })
public class ReportsAction extends CustomActionSupport {
	HttpServletRequest request = ServletActionContext.getRequest();
	HttpServletResponse response = ServletActionContext.getResponse();
public String execute() throws Exception {
    HttpServletRequest request = ServletActionContext.getRequest();

    // List of methods 
    String method = String.valueOf(request.getParameter("method"));

    switch (method) {
        case "OpportunityCostReport":
            return OpportunityCostReport();
        case "getOpportunityCostparameters":
            return getOpportunityCostparameters();
        case "unspecified":
            return unspecified();
        case "displayList":
            return displayList();
        case "parameterReport":
            return parameterReport();
        case "isWeekend":
            return isWeekend();
        case "getMovementsParameters":
            return getMovementsParameters();
        case "UnsettledMovementsReport":
            return UnsettledMovementsReport();
        case "ExcludedMovementsReport":
            return ExcludedMovementsReport();
        case "getILMReport":
            return getILMReport();
        case "ILMReport":
            return ILMReport();
        case "cancelILMExport":
            return cancelILMExport();
        case "isAllEntityAvailable":
            return isAllEntityAvailable();
        case "reportInProgressValidation":
            return reportInProgressValidation();
        case "validateDates":
            return validateDates();
        case "checkMissingData":
            return checkMissingData();
        default:
            break;
    }

    return unspecified();
}


private Reports reports;
public Reports getReports() {
	if (reports == null) {
		reports = new Reports();
	}
	return reports;
}
public void setReports(Reports reports) {
	this.reports = reports;
	HttpServletRequest request = ServletActionContext.getRequest();
	request.setAttribute("reports", reports);
}

	// Report manger declaration
	@Autowired
private ReportsManager reportsManager = null;
	private final Log log = LogFactory.getLog(ReportsAction.class);
	
	private volatile  ArrayList<Map<String, Object>> allBeansListVar = null;

	/**
	 * return host id from cache manager
	 * 
	 * @param request
	 * @return string
	 * @throws SwtException
	 */

	private String putHostIdListInReq(HttpServletRequest request)
			throws SwtException {
		log.debug(this.getClass().getName()
				+ "- [putHostIdListInReq] - Entering ");
		CacheManager cacheManagerInst = CacheManager.getInstance();
		String hostId = cacheManagerInst.getHostId();
		log.debug(this.getClass().getName()
				+ "- [putHostIdListInReq] - Exiting ");

		return hostId;

	}

	/* START : Modified by sandeepkumar for Mantis 1766 : Opportunity cost report: Improve performance */
	/**
	 * Result of type Action forward This method will accept the input from the
	 * user and send the generated report to the user
	 * 
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return ActionForward
	 * @throws SwtException
	 */
	public String OpportunityCostReport() throws Exception {
		// dyna validator form to get and set the form values
// To remove: 		DynaValidatorForm dyForm = null;
		// variable to hold report
		Reports report = null;
		// Declaring the jasperPrint
		JasperPrint jasperPrint = null;
		//varible to hold threshold 
		String threshold = null;
		// Initializing the outputstream
		ServletOutputStream out = null;
		// Initializing the PDF Exporter.
		JRPdfExporter pdfexporter = null;
		JRXlsExporter xlsxporter = null;
		JRCsvExporter csvexporter = null;
		String sheetName[] = new String[1];
		String selectedOutputFormat=null;
		String sqlQueryAsString = null;
		try {
			log.debug(this.getClass().getName()
					+ "- [OpportunityCostReport] - Entering ");
			// Initializing the Dynavalidator Form
// To remove: 			dyForm = (DynaValidatorForm) form;
			// Get the reports from Dynavalidator Form
			report = (Reports) getReports();
			// To get the parameters from reports.
			threshold = report.getThreshold();
			// If the threshold is null then N will be assigned
			if (threshold == null
					|| threshold.equals(SwtConstants.EMPTY_STRING)) {
				threshold = "0.0";
			}			
			/* Read selectedOutputFormat from request */	
			selectedOutputFormat = request.getParameter("selectedOutputFormat");
			
			//get output stream from servlet output stream
			out = response.getOutputStream();	
			/* To get the filled report form reportsManager */
			jasperPrint = reportsManager.getOpportunityCost(request, SwtUtil
					.getCurrentHostId(), report.getEntityId(), report
					.getFromDateAsString(), threshold, request
					.getParameter("currencyCode"), request
			.getParameter("currencyText"), null, selectedOutputFormat);
			/* Initializing the JRDFExporter */
			/* Condition to check filType is pdf */
			if(SwtConstants.OPPORTUNITY_COST_REPORT_OUTPUT_PDF.equals(selectedOutputFormat)) {
				// ........ Export PDF Report ...........//
				sqlQueryAsString = SwtUtil.getNamedQuery("turnover_report_query");
				pdfexporter = new JRPdfExporter();
			response.setContentType("application/pdf");
				response.setHeader("Content-disposition",
						"attachment; filename=" + jasperPrint.getName() + "-SmartPredict_"
					+ SwtUtil.formatDate(new Date(), "yyyyMMdd") + "_"
								+ SwtUtil.formatDate(new Date(), "HHmmss") + ".pdf");			
				pdfexporter.setParameter(JRExporterParameter.JASPER_PRINT, jasperPrint);
			
			// Providing the output stream
				pdfexporter.setParameter(JRExporterParameter.OUTPUT_STREAM, out);
			
			// Exporting as UTF-8
				pdfexporter.setParameter(JRExporterParameter.CHARACTER_ENCODING,"UTF-8");
			
				// Export Report to PDF
				pdfexporter.exportReport();
				
			/* Condition to check fileType is xls */
			}else if(SwtConstants.OPPORTUNITY_COST_REPORT_OUTPUT_CSV.equals(selectedOutputFormat)){
				// ........ Export CSV Report ...........//		

				csvexporter = new JRCsvExporter();
				response.setContentType("application/csv");
				response.setHeader("Content-disposition",
						"attachment; filename=" + jasperPrint.getName() + "-SmartPredict_"
								+ SwtUtil.formatDate(new Date(), "yyyyMMdd") + "_"
								+ SwtUtil.formatDate(new Date(), "HHmmss") + ".csv");

				csvexporter.setParameter(JRExporterParameter.JASPER_PRINT, jasperPrint);
				csvexporter.setParameter(JRExporterParameter.OUTPUT_STREAM, out);
				csvexporter.setParameter(JRXlsExporterParameter.IS_DETECT_CELL_TYPE, Boolean.TRUE);
				csvexporter.setParameter(JRXlsExporterParameter.IS_WHITE_PAGE_BACKGROUND, Boolean.FALSE);
				csvexporter.setParameter(JRXlsExporterParameter.IS_IGNORE_GRAPHICS, Boolean.FALSE);
				csvexporter.setParameter(JRXlsExporterParameter.IS_REMOVE_EMPTY_SPACE_BETWEEN_COLUMNS, Boolean.TRUE);
				// Set the column names wrap parameter to false
				csvexporter.exportReport();
				
				
				
			}else if(SwtConstants.OPPORTUNITY_COST_REPORT_OUTPUT_EXCEL.equals(selectedOutputFormat)){
					// ........ Export EXCEL Report ...........//
					xlsxporter = new JRXlsExporter();
					response.setContentType("application/xls");
					response.setHeader("Content-disposition",
							"attachment; filename=" + jasperPrint.getName() + "-SmartPredict_"
									+ SwtUtil.formatDate(new Date(), "yyyyMMdd") + "_"
									+ SwtUtil.formatDate(new Date(), "HHmmss") + ".xls");
					
					
					xlsxporter.setParameter(JRExporterParameter.JASPER_PRINT, jasperPrint);
					
					// Providing the output stream
					xlsxporter.setParameter(JRExporterParameter.OUTPUT_STREAM, out);
					
					// Exporting as UTF-8
					xlsxporter.setParameter(JRExporterParameter.CHARACTER_ENCODING,"UTF-8");
					xlsxporter.setParameter(JRXlsExporterParameter.IS_ONE_PAGE_PER_SHEET,Boolean.TRUE);
					xlsxporter.setParameter(JRXlsExporterParameter.IS_REMOVE_EMPTY_SPACE_BETWEEN_ROWS, Boolean.TRUE);
					xlsxporter.setParameter(JRXlsExporterParameter.IS_REMOVE_EMPTY_SPACE_BETWEEN_COLUMNS, Boolean.TRUE);
					xlsxporter.setParameter(JRXlsExporterParameter.IS_DETECT_CELL_TYPE, Boolean.TRUE);
					// Export Report to Excel
					xlsxporter.exportReport();
				}

		} catch (Exception exp) {

			log.debug(this.getClass().getName()
					+ "- [OpportunityCostReport] - Exception "
					+ exp.getMessage());
			log.error(this.getClass().getName()
					+ "- [OpportunityCostReport] - Exception "
					+ exp.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "save", ReportsAction.class), request, "");
			return ("fail");

		} finally {
			//nullyfing objects
			report = null;
			jasperPrint = null;
			threshold = null;
			out = null;
			pdfexporter = null;
			csvexporter = null;
			xlsxporter = null;
			
		}
		log.debug(this.getClass().getName()
				+ "- [OpportunityCostReport] - Exiting ");
		return null;
	}

	/* End : Modified by sandeepkumar for Mantis 1766 : Opportunity cost report: Improve performance*/
	/**
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return a Result of type Action forward
	 * @throws Exception
	 */
	public String getOpportunityCostparameters() throws Exception {

		log.debug(this.getClass().getName()
				+ "- [getOpportunityCostparameters] - Entering ");
		// Declaring the Dynavalidator Form
// To remove: 		DynaValidatorForm dyForm;
		// Declaring the report
		Reports report;
		// Declaring the local variables to pass to the user
		String userEntityId;
		String userEntityName = null;
		Collection userEntityList;
		EntityUserAccess entityObj;
		String entityChange = null;
		String currencyCode = null;
		String reportingCcy = null;
		String hostId = null;
		String configScheduler = null;
		String newOpportunityCostReportSchedConfig = null;
		String schedulerConfigXML = null;
		String reportType = null;
		boolean isCcyChanged = false;
		boolean isEntityChanged = false;
		String dateFormat = null;
		configScheduler = request.getParameter("configScheduler");
		newOpportunityCostReportSchedConfig = request.getParameter("newOpportunityCostReportSchedConfig");
		reportType = request.getParameter("reportType");
		// Intialising the Dynavalidator Form
// To remove: 		dyForm = (DynaValidatorForm) form;
		// Get the reports from Dynavalidator Form
		report = (Reports) getReports();
		// To get the current user Entity
		userEntityId = SwtUtil.getUserCurrentEntity(request.getSession());
		hostId = SwtUtil.getCurrentHostId();
		// To get the user accessible Entity list
		userEntityList = SwtUtil.getUserEntityAccessList(request.getSession());
		// Intialising the Dateformat
		SimpleDateFormat sdf = new SimpleDateFormat(SwtUtil
				.getCurrentDateFormat(request.getSession()));


  	        entityChange = request.getParameter("status");
		if ((entityChange != null)
				&& (entityChange.equalsIgnoreCase("onEntityChange"))) {
			userEntityId = report.getEntityId();
                        isEntityChanged = true;


		} else {
			if (userEntityId != null) {
				Iterator itr = userEntityList.iterator();
				while (itr.hasNext()) {
					entityObj = (EntityUserAccess) itr.next();
					if (entityObj.getEntityId().equals(userEntityId)) {
						userEntityName = entityObj.getEntityName();
						break;
					}
				}
			}
		}

		if (!SwtUtil.isEmptyOrNull(configScheduler) && ("true".equals(configScheduler))) {
			if (!SwtUtil.isEmptyOrNull(newOpportunityCostReportSchedConfig) && "false".equals(newOpportunityCostReportSchedConfig)) {
				HashMap<String, String> schedulerConfigMap;
				schedulerConfigXML = SwtUtil.decode64(request.getParameter("schedulerConfigXML"));
				schedulerConfigMap = convertschedulerConfigXMLtoHashmap(schedulerConfigXML);
				report = new Reports();
				if (SwtUtil.isEmptyOrNull(((Reports) getReports()).getEntityId())) {
					report.setEntityId(schedulerConfigMap.get("entityid"));
				} else {
					report.setEntityId(((Reports) getReports()).getEntityId());
				}
				if (SwtUtil.isEmptyOrNull(((Reports) getReports()).getCurrencyCode())) {
					report.getId().setCurrencyCode(schedulerConfigMap.get("currencycode"));
				} else {
					report.getId().setCurrencyCode(((Reports) getReports()).getCurrencyCode());
				}
				
				report.setThreshold(schedulerConfigMap.get("threshold"));
				report.setReportType(schedulerConfigMap.get("reporttype"));
				report.setPdfOrExcelorCSV(schedulerConfigMap.get("pdforexcelorcsv"));
				//dateFormat User
				//String dateFormat = SwtUtil.getCurrentDateFormat(request.getSession());

				userEntityId = schedulerConfigMap.get("entityid");
				currencyCode = schedulerConfigMap.get("currencycode");
				dateFormat = schedulerConfigMap.get("dateformatasstring");
								
				if(!SwtUtil.isEmptyOrNull(dateFormat) && !SwtUtil.isEmptyOrNull(schedulerConfigMap.get("fromdateasstring"))) {
					
					String valuedateToSet = null;
					if(SysParamsDAOHibernate.keywordQuery.containsKey(schedulerConfigMap.get("fromdateasstring"))) {
						valuedateToSet = schedulerConfigMap.get("fromdateasstring");
					}else {
						Date toDate = SwtUtil.parseDate(schedulerConfigMap.get("fromdateasstring"), dateFormat);
						valuedateToSet = SwtUtil.formatDate(toDate, SwtUtil.getCurrentDateFormat(request.getSession()));
					}
					
					report.setFromDateAsString(valuedateToSet);
				}else {
					report.setFromDateAsString(schedulerConfigMap.get("fromdateasstring"));
				}
			} 
		}
		
         	// To get the test date
		Date testDate = SwtUtil.getSysParamDateWithEntityOffset(userEntityId);
		// Formats for current system date formats
		String today = SwtUtil.formatDate(testDate, SwtUtil
				.getCurrentSystemFormats(request.getSession())
				.getDateFormatValue());
		/*
		 * Puts the list of currencies in request object and assign the default
		 * domestic currency
		 */
		
		if(SwtUtil.isEmptyOrNull(currencyCode)) {
			currencyCode = putCurrencyListInReq(request,
					SwtUtil.getCurrentHostId(), userEntityId);
			
			currencyCode = "All";
		}else {
			
			putCurrencyListInReq(request,
					SwtUtil.getCurrentHostId(), userEntityId);
			}
		reportingCcy = SwtUtil.getReportingCurrencyForEntity(hostId, userEntityId);
		
		// adding into label bean
		request.setAttribute("keywords", SwtUtil.getKeywords(request));
		
		// Set the useraccessible enetity list to request
		request.setAttribute("userEntity", userEntityList);
		request.setAttribute("reportingCcy", reportingCcy);
                // Set the parameters to be displayed to the user
		report.getId().setHostId(SwtUtil.getCurrentHostId());
		if(SwtUtil.isEmptyOrNull(report.getFromDateAsString())) {
			report.setFromDateAsString(today);
		}
		report.setEntityId(userEntityId);
		report.setEntityName(userEntityName);
		// to display in the JSP while on load
		report.getId().setCurrencyCode(currencyCode);
		request.setAttribute("configScheduler", configScheduler);
		request.setAttribute("newOpportunityCostReportSchedConfig", newOpportunityCostReportSchedConfig);
		request.setAttribute("reportType",reportType);	
		request.setAttribute("schedulerConfigXML", request.getParameter("schedulerConfigXML"));
		request.setAttribute("jobId", request.getParameter("jobId"));
		// Set the report object to Action form
		setReports(report);
		log.debug(this.getClass().getName()
				+ "- [getOpportunityCostparameters] - Exiting ");
		return ("success");
	}
		

	public String unspecified()
			throws Exception {
		return null;
		// return displayList(mapping,form,request,response);
	}

	/**
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return
	 * @throws Exception
	 */
	public String displayList()
			throws Exception {
		try {
			log
					.debug(this.getClass().getName()
							+ "- [displayList] - Entering ");
// To remove: 			DynaValidatorForm dyForm = (DynaValidatorForm) form;
			String hostId = putHostIdListInReq(request);
			String reportName = request.getParameter("name");
			Reports reports = (Reports) getReports();
			reports.setReportType(reportName);
			Collection userList = reportsManager.getUserList(hostId);
			request.setAttribute("methodName", reportName);
			request.setAttribute("userList", userList);
			log.debug(this.getClass().getName() + "- [displayList] - Exiting ");
			return ("success");
		} catch (Exception e) {
			log.debug("Exception Catch");
			e.printStackTrace();
			SystemExceptionHandler.logError(e);
			return ("fail");
		}
	}

	/**
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return ActionForward
	 * @throws Exception
	 */

	public String parameterReport() throws Exception {
		try {
			log.debug(this.getClass().getName()
					+ "- [parameterReport] - Entering ");
// To remove: 			DynaValidatorForm dyForm = (DynaValidatorForm) form;

			Reports reports = (Reports) getReports();
			String userName = reports.getId().getUserId();

			if (SwtConstants.ALL_VALUE.equalsIgnoreCase(userName)) {
				Collection userList = reportsManager.getUserList(SwtUtil
						.getCurrentHostId());
				userName = getUserNameString(userList);
			} else {
				userName = "'" + userName + "'";
			}

			request.setAttribute("userName", userName);

			String date1 = reports.getFromDateAsString();

			request.setAttribute("date1", date1);

			String date2 = reports.getToDateAsString();
			request.setAttribute("date2", date2);

			String reportName = reports.getReportType();

			if (SwtConstants.REPORT_TYPE_TURN_OVER.equalsIgnoreCase(reportName)) {
				request.setAttribute("methodName", "turnOver");
				request.setAttribute("reportName",
						SwtConstants.REPORT_RPTNAME_TURN_OVER);
			} else {
				request.setAttribute("methodName", "matchStatic");
				request.setAttribute("reportName",
						SwtConstants.REPORT_RPTNAME_MATCH_STATIC);
			}

			log.debug(this.getClass().getName()
					+ "- [parameterReport] - Exiting ");
			return ("success1");
		} catch (Exception e) {
			log.debug("Exception Catch");
			e.printStackTrace();
			SystemExceptionHandler.logError(e);
			return ("fail");
		}
	}

	/**
	 * @param userList
	 * @param response
	 * @return String
	 */

	private String getUserNameString(Collection userList) {
		log.debug(this.getClass().getName()
				+ "- [getUserNameString] - Entering ");
		String userStr = "";
		ArrayList list = (ArrayList) userList;
		list.remove(0);
		Iterator itr = list.iterator();
		LabelValueBean lblBean = null;
		while (itr.hasNext()) {
			lblBean = (LabelValueBean) itr.next();
			userStr = userStr + "'" + lblBean.getValue() + "',";
		}
		if (userStr.length() > 1) {
			userStr = userStr.substring(0, userStr.length() - 1);
		}
		log.debug(this.getClass().getName()
				+ "- [getUserNameString] - Exiting ");
		return userStr;
	}

	/**
	 * @param usersManager
	 *            The usersManager to set.
	 */
	public void setReportsManager(ReportsManager reportsManager) {
		this.reportsManager = reportsManager;
	}

	/**
	 * This method is used to put the list of currency in request object
	 * 
	 * @param request -
	 *            HttpServletRequest
	 * 
	 * @param hostId -
	 *            String
	 * 
	 * @param entityId -
	 *            String
	 * 
	 * @return String
	 * 
	 * @throws SwtException
	 *             Throw exception, if any
	 */
	private String putCurrencyListInReq(HttpServletRequest request,
			String hostId, String entityId) throws SwtException {
		log.debug(this.getClass().getName()
				+ "- [putCurrencyListInReq] - Entering ");
		/* String variable to hold default currency */
		String defaultCurrency = null;
		/* String variable to hold the roldId */
		String roleId = null;
		/* String variable to hold the domesticCurrency */
		String domesticCurrency = null;
		/* Collection object to hold all currencies in dropdown box */
		Collection currencyDropDown = null;
		/* Collection object to hold all currencies with ALLin dropdown box */
		Collection currrencyListWithAll = null;
		/* Getting the user's roleId from session */
		roleId = ((CommonDataManager) request.getSession().getAttribute(
				SwtConstants.CDM_BEAN)).getUser().getRoleId();
		/* This is used to get all currencies in dropdown box */
		currencyDropDown = SwtUtil.getSwtMaintenanceCache()
				.getCurrencyViewORFullAcessLVL(roleId, entityId);
		/*
		 * If the currencies is not null,it will remove the default currency
		 * from the dropdown list
		 */
		if (currencyDropDown != null) {
			currencyDropDown.remove(new LabelValueBean("Default", "*"));
		}

		/* Creates an instance for an ArrayList */
		currrencyListWithAll = new ArrayList();

		/* Showing 'All' in Currency drop down */
		currrencyListWithAll.add(new LabelValueBean(SwtConstants.ALL_LABEL,
				SwtConstants.ALL_VALUE));

		/*
		 * If the currencies in the dropdown list is not equal to null, it will
		 * add all currencies with ALL .
		 */
		if (currencyDropDown != null) {
			currrencyListWithAll.addAll(currencyDropDown);
		}

		/* Sets the currencyList With All in request object */
		request.setAttribute("currencies", currrencyListWithAll);

		/* Getting the domestic currency for user from db */
		domesticCurrency = SwtUtil.getDomesticCurrencyForUser(request, hostId,
				entityId);
		/*
		 * If the domestic currency is not equal to null, it assigns the
		 * domestic currency to default currency for display to the user.
		 */
		if (domesticCurrency != null) {
			defaultCurrency = domesticCurrency;
		}

		log.debug(this.getClass().getName()
				+ "- [putCurrencyListInReq] - Exiting ");

		/* Returns the defaultCurrency */
		return defaultCurrency;
	}

	/**
	 * This method will accept the input from the user and send boolean value
	 * whether selected date is weekend
	 * 
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return ActionForward
	 * @throws SwtException
	 */
	public String isWeekend()
			throws Exception {

		// Variable to hold hostId
		String hostId = null;
		// Variable to hold entityId
		String entityId = null;
		// Variable to hold reportDate
		String reportDate = null;
		/* Holiday DAO */
		HolidayDAO holidayDAO = null;
		// Variable to hold isHoliday
		boolean isHoliday;
		// Variable to hold calender instance
		Calendar calHoliday = null;
		// Variable to hold domestic currency
		String domesticCurrency = "";
		try {
			log.debug(this.getClass().getName() + "- [isWeekend] - Entering ");
			// read hostId
			hostId = SwtUtil.getCurrentHostId();
			// read entity Id from request
			entityId = request.getParameter("entityId");
			// Read report date from request
			reportDate = request.getParameter("selectedDate");
			if(!SysParamsDAOHibernate.keywordQuery.containsKey(reportDate)) {
			
				// Get caleder instance
				calHoliday = Calendar.getInstance();
				// set date in calender
				calHoliday.setTime(SwtUtil.parseDate(reportDate, SwtUtil
						.getCurrentSystemFormats(request.getSession())
						.getDateFormatValue()));
				// Get holiday dao instance
				holidayDAO = (HolidayDAO) SwtUtil.getBean("holidayDAO");
				// Get domestic currency of selected entity
				domesticCurrency = SwtUtil.getDomesticCurrencyForUser(request,
						hostId, entityId);
	
				// chcek whether the given date is weekend
	
				isHoliday = holidayDAO.isWeekend(PropertiesFileLoader.getInstance()
						.getPropertiesValue(
								SwtConstants.FACILITY_OPPORTUNITY_COST_REPORT),
						calHoliday, entityId, hostId, domesticCurrency);
	
				// set is holiday to response
				response.getWriter().print(isHoliday);
			}else {
				response.getWriter().print(true);
			}
			log.debug(this.getClass().getName() + "- [isWeekend] - Exiting ");
		} catch (Exception exp) {

			log.error(this.getClass().getName() + "- [isWeekend] - Exception "
					+ exp.getMessage());

			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "isWeekend", ReportsAction.class), request, "");
			return ("fail");

		}

		return null;
	}

	/**
	 * Added by Med Amine for Mantis 2145: Excluded/Unsettled Movements Report
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return a Result of type Action forward
	 * @throws Exception
	 */
	public String getMovementsParameters() throws Exception {
		log.debug(this.getClass().getName()
				+ "- [getMovementsParameters] - Entering ");
		// Declaring the Dynavalidator Form
// To remove: 		DynaValidatorForm dyForm;
		// Declaring the report
		Reports report;
		// Declaring the local variables to pass to the user
		String userEntityId;
		String userEntityName = null;
		Collection userEntityList;
		EntityUserAccess entityObj;
		// Added for mantis 1110 to display currency code
		String entityChange = null;
		String currencyCode = null;
		// Intialising the Dynavalidator Form
// To remove: 		dyForm = (DynaValidatorForm) form;
		// Get the reports from Dynavalidator Form
		report = (Reports) getReports();
		
		if(request.getParameter("entityId") != null) {
			userEntityId  = request.getParameter("entityId") ;
		}else {
			// To get the current user Entity
			userEntityId = SwtUtil.getUserCurrentEntity(request.getSession());
		}

		// To get the user accessible Entity list
		userEntityList = SwtUtil.getUserEntityAccessList(request.getSession());
		// Intialising the Dateformat
		SimpleDateFormat sdf = new SimpleDateFormat(SwtUtil
				.getCurrentDateFormat(request.getSession()));
		// To get the test date
		Date testDate = SwtUtil.getSysParamDateWithEntityOffset(userEntityId);
		// Formats for current system date formats
		String today = SwtUtil.formatDate(testDate, SwtUtil
				.getCurrentSystemFormats(request.getSession())
				.getDateFormatValue());
		entityChange = request.getParameter("status");
		if ((entityChange != null)
				&& (entityChange.equalsIgnoreCase("onEntityChange"))) {
			userEntityId = report.getEntityId();
		} else {
			if (userEntityId != null) {
				Iterator itr = userEntityList.iterator();
				while (itr.hasNext()) {
					entityObj = (EntityUserAccess) itr.next();
					if (entityObj.getEntityId().equals(userEntityId)) {
						userEntityName = entityObj.getEntityName();
						break;
					}
				}
			}
		}

		/*
		 * Puts the list of currencies in request object and assign the default
		 * domestic currency
		 */
		currencyCode = putCurrencyListInReq(request,
				SwtUtil.getCurrentHostId(), userEntityId);

		// Set the useraccessible enetity list to request
		request.setAttribute("userEntity", userEntityList);
		// Set the parameters to be displayed to the user
		report.getId().setHostId(SwtUtil.getCurrentHostId());
		report.setFromDateAsString(today);
		report.setEntityId(userEntityId);
		report.setEntityName(userEntityName);
		// to display in the JSP while on load
		report.getId().setCurrencyCode("All");
		// Set the report object to Action form
		setReports(report);

		log.debug(this.getClass().getName()
				+ "- [getMovementsParameters] - Exiting ");
		return ("success");
	}


	/**
	 * Added by Mefteh Bouazizi for Mantis 2145: Unsettled Movements Report
	 * Result of type Action forward This method will accept the input from the
	 * user and send the generated report to the user
	 * 
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return ActionForward
	 * @throws SwtException
	 */
	
	public String UnsettledMovementsReport() throws Exception {
		// dyna validator form to get and set the form values
// To remove: 		DynaValidatorForm dyForm = null;
		// variable to hold report
		Reports report = null;
		// Declaring the jasperPrint
		JasperPrint jasperPrint = null;
		//varible to hold threshold 
		String threshold = null;
		// Initializing the outputstream
		ServletOutputStream out = null;
		// Initializing the PDF Exporter.
		JRPdfExporter exporter = null;
		try {
			log.debug(this.getClass().getName()
					+ "- [UnsettledMovementsReport] - Entering ");
			// Initializing the Dynavalidator Form
// To remove: 			dyForm = (DynaValidatorForm) form;
			// Get the reports from Dynavalidator Form
			report = (Reports) getReports();
			// To get the parameters from reports.
			threshold = report.getThreshold();
			// If the threshold is null then N will be assigned
			if (threshold == null
					|| threshold.equals(SwtConstants.EMPTY_STRING)) {
				threshold = "0.0";
			}
			// To get the entity Name 
			String entityName=request.getParameter("entityText");
			if(entityName!=null)
				report.setEntityName(entityName);				  
			//get output stream from servlet output stream
			out = response.getOutputStream();
			//get the details for given host id,entity id .date,threshold,currency code and currency name
			jasperPrint = reportsManager.getUnsettledMovements(request, SwtUtil
					.getCurrentHostId(), report.getEntityId(),report.getEntityName(), report
					.getFromDateAsString(), threshold, report.getThresholdType());
			// Instance for JRPDFExporter.
			exporter = new JRPdfExporter();
			// To set the output type as PDF file
			response.setContentType("application/pdf");
			// To set the content as attachment
			response.setHeader("Content-disposition", "attachment; filename="
					+ jasperPrint.getName() + "-SmartPredict_"
					+ SwtUtil.formatDate(new Date(), "yyyyMMdd") + "_"
					+ SwtUtil.formatDate(new Date(), "HHmmss") + ".pdf");
			// To pass the filled report
			exporter
					.setParameter(JRExporterParameter.JASPER_PRINT, jasperPrint);
			// Providing the output stream
			exporter.setParameter(JRExporterParameter.OUTPUT_STREAM, out);
			// Exporting as UTF-8
			exporter.setParameter(JRExporterParameter.CHARACTER_ENCODING,
					"UTF-8");
			// Export Report
			exporter.exportReport();

		} catch (Exception exp) {

			log.debug(this.getClass().getName()
					+ "- [UnsettledMovementsReport] - Exception "
					+ exp.getMessage());
			log.error(this.getClass().getName()
					+ "- [UnsettledMovementsReport] - Exception "
					+ exp.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "save", ReportsAction.class), request, "");
			return ("fail");

		} finally {
			//nullyfing objects
			report = null;
			jasperPrint = null;
			threshold = null;
			out = null;
			exporter = null;
		}
		log.debug(this.getClass().getName()
				+ "- [UnsettledMovementsReport] - Exiting ");
		return null;
	} 
	 
	/**
	 * Added By Med Amine for Mantis 2145: Excluded Movements Reports
	 * Result of type Action forward This method will accept the input from the
	 * user and send the generated report to the user
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return ActionForward
	 * @throws SwtException
	 */

	public String ExcludedMovementsReport() throws Exception {
		// dyna validator form to get and set the form values
// To remove: 		DynaValidatorForm dyForm = null;
		// variable to hold report
		Reports report = null;
		// Declaring the jasperPrint
		JasperPrint jasperPrint = null;
		//varible to hold threshold 
		String threshold = null;
		//varible to hold threshold Time 
		String thresholdTime = null;
		// Initializing the outputstream
		ServletOutputStream out = null;
		// Initializing the PDF Exporter.
		JRPdfExporter exporter = null;
		try {
			log.debug(this.getClass().getName()
					+ "- [ExcludedMovementsReport] - Entering ");
			// Initializing the Dynavalidator Form
// To remove: 			dyForm = (DynaValidatorForm) form;
			// Get the reports from Dynavalidator Form
			report = (Reports) getReports();
			// To get the parameters from reports.
			threshold = report.getThreshold();
			// If the threshold is null then N will be assigned
			if (threshold == null
					|| threshold.equals(SwtConstants.EMPTY_STRING)) {
				threshold = "0.0";
			}
			// To get the parameters from reports.
			thresholdTime = report.getThresholdTime();
	     	report.setEntityName(request.getParameter("entityText"));
			jasperPrint = reportsManager.getExcludedMovements(request, SwtUtil
					.getCurrentHostId(), report.getEntityId(),report.getEntityName(), report
					.getFromDateAsString(), threshold, report.getThresholdType(), thresholdTime);
			// Instance for JRPDFExporter.
		
			exporter = new JRPdfExporter();
			// Intiasing the outputstream
			 out = response.getOutputStream();
			// To set the output type as PDF file
			response.setContentType("application/pdf");
			// To set the content as attachment
			response.setHeader("Content-disposition", "attachment; filename="
					+ jasperPrint.getName() + "-SmartPredict_"
					+ SwtUtil.formatDate(new Date(), "yyyyMMdd") + "_"
					+ SwtUtil.formatDate(new Date(), "HHmmss") + ".pdf");
			// To pass the filled report
			exporter.setParameter(JRExporterParameter.JASPER_PRINT, jasperPrint);
			// Providing the output stream
			exporter.setParameter(JRExporterParameter.OUTPUT_STREAM, out);
			// Exporting as UTF-8
			exporter.setParameter(JRExporterParameter.CHARACTER_ENCODING,
					"UTF-8");
			// Export Report
			exporter.exportReport();
			

		} catch (Exception exp) {
			exp.printStackTrace();
			log.debug(this.getClass().getName()
					+ "- [ExcludedMovementsReport] - Exception "
					+ exp.getMessage());
			log.error(this.getClass().getName()
					+ "- [ExcludedMovementsReport] - Exception "
					+ exp.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "save", ReportsAction.class), request, "");
			return ("fail");
		} finally {
			//nullyfing objects
			report = null;
			jasperPrint = null;
			threshold = null;
			out = null;
			exporter = null;
}
		log.debug(this.getClass().getName()
				+ "- [ExcludedMovementsReport] - Exiting ");
		return null;
	}
	
	public String getILMReport()
			throws SwtException {
HttpServletRequest request = ServletActionContext.getRequest();
HttpServletResponse response = ServletActionContext.getResponse();
	
// To remove: 		DynaValidatorForm dyForm;
		Reports report;
// To remove: 		dyForm = (DynaValidatorForm) form;
		String entityId = null;
		String currencyCode = null;
		String hostId = null;
		Collection ilmAccountGroups = null;
		String defaultEntityId = null;
		String defaultCcyId = null;
		Collection<LabelValueBean> entitiesLblValue = null;
		Collection<LabelValueBean> scenariosLblValue = null;
		Collection collUserEntityAccess = null;
		Collection<LabelValueBean> entityAccessLvlColl = null;
		Collection<LabelValueBean> ccyList = null;
		Collection<LabelValueBean> reportTypeList = null;
		Collection<LabelValueBean> ccyAccessColl = null;
		String roleId = null;
		boolean isCcyChanged = false;
		boolean isEntityChanged = false;
		boolean isCentralBankExist = true;
		String userId = null;
		String reportType = null;
		ArrayList scenarioListDetail;
		Date dateInCcyTimeframe = null;
		String listOfCcyAsString;
		Boolean defaultCcyInTheList = false;
		boolean entityExistInList = false;
		ArrayList<String> currencies = new ArrayList<String>();
		ArrayList<String> entities = new ArrayList<String>();
		String configScheduler = null;
		String newILMReportSchedConfig = null;
		String schedulerConfigXML = null;

		try {
			log.debug(this.getClass().getName()
				+ "- [getILMReport] - Entering ");
			configScheduler = request.getParameter("configScheduler");
			newILMReportSchedConfig = request.getParameter("newILMReportSchedConfig");
			
			scenarioListDetail = new ArrayList();
			isEntityChanged = SwtUtil.isEmptyOrNull(request.getParameter("changeEntity")) ? false : request.getParameter("changeEntity").equalsIgnoreCase("Y");
			isCcyChanged = SwtUtil.isEmptyOrNull(request.getParameter("changeCcy")) ? false : request.getParameter("changeCcy").equalsIgnoreCase("Y");
			
			if (!SwtUtil.isEmptyOrNull(configScheduler) && ("true".equals(configScheduler))) {
				roleId = request.getParameter("roleId");
				if (!SwtUtil.isEmptyOrNull(newILMReportSchedConfig) && "false".equals(newILMReportSchedConfig)) {
					HashMap<String, String> schedulerConfigMap;
					schedulerConfigXML = SwtUtil.decode64(request.getParameter("schedulerConfigXML"));
					schedulerConfigMap = convertschedulerConfigXMLtoHashmap(schedulerConfigXML);
					report = new Reports();
					if (SwtUtil.isEmptyOrNull(((Reports) getReports()).getEntityId())) {
						report.setEntityId(schedulerConfigMap.get("entityid"));
					} else {
						report.setEntityId(((Reports) getReports()).getEntityId());
					}
					if (SwtUtil.isEmptyOrNull(((Reports) getReports()).getCurrencyCode())) {
						report.setCurrencyCode(schedulerConfigMap.get("currencycode"));
					} else {
						report.setCurrencyCode(((Reports) getReports()).getCurrencyCode());
						
					}
					if (isEntityChanged || isCcyChanged) {
						report.setReportType((((Reports) getReports()).getReportType()));
						report.setSingleOrRange(((Reports) getReports()).getSingleOrRange());
						report.setFromDateAsString(((Reports) getReports()).getFromDateAsString());
						report.setToDateAsString(((Reports) getReports()).getToDateAsString());
						report.setSumInOutFlows(((Reports) getReports()).getSumInOutFlows());
						report.setUseCcyMultiplier(((Reports) getReports()).getUseCcyMultiplier());
						report.setCriticalTransactions(((Reports) getReports()).getCriticalTransactions());
						report.setCostumerPayments(((Reports) getReports()).getCostumerPayments());
						report.setLoroPayments(((Reports) getReports()).getLoroPayments());
						report.setBranchPayments(((Reports) getReports()).getBranchPayments());
						report.setOtherPayments(((Reports) getReports()).getOtherPayments());
						report.setCorporatePayments(((Reports) getReports()).getCorporatePayments());
						
					} else {
						report.setReportType(schedulerConfigMap.get("reporttype"));
						report.setSingleOrRange(schedulerConfigMap.get("singleorrange"));
						//dateFormat User
						String dateFormat = SwtUtil.getCurrentDateFormat(request.getSession());
						if (!SysParamsDAOHibernate.keywordQuery.containsKey(schedulerConfigMap.get("fromdateasstring"))) {
							String dateAsString = schedulerConfigMap.get("fromdateasstring");
							SimpleDateFormat xmlDateFormat = new SimpleDateFormat(schedulerConfigMap.get("dateformat"));
							String dateResult = SwtUtil.formatDate(xmlDateFormat.parse(dateAsString), dateFormat);
							report.setFromDateAsString(dateResult);
						} else {
							report.setFromDateAsString(schedulerConfigMap.get("fromdateasstring"));
						}
						
						if (!SwtUtil.isEmptyOrNull(schedulerConfigMap.get("todateasstring")) 
								&& !SysParamsDAOHibernate.keywordQuery.containsKey(schedulerConfigMap.get("todateasstring"))) {
							String dateAsString = schedulerConfigMap.get("todateasstring");
							SimpleDateFormat xmlDateFormat = new SimpleDateFormat(schedulerConfigMap.get("dateformat"));
							String dateResult = SwtUtil.formatDate(xmlDateFormat.parse(dateAsString), dateFormat);
							report.setToDateAsString(dateResult);
						} else {
							report.setToDateAsString(schedulerConfigMap.get("todateasstring"));
						}
						
						report.setSumInOutFlows(schedulerConfigMap.get("suminoutflows"));
						report.setUseCcyMultiplier(schedulerConfigMap.get("useccymultiplier"));
						report.setCriticalTransactions(schedulerConfigMap.get("criticaltransactions"));
						if (!SwtUtil.isEmptyOrNull(schedulerConfigMap.get("costumerpayments"))) {
							report.setCostumerPayments(schedulerConfigMap.get("costumerpayments"));
							
							if (!SwtUtil.isEmptyOrNull(schedulerConfigMap.get("loropayments"))) {
								report.setLoroPayments(schedulerConfigMap.get("loropayments"));
							}else {
								report.setLoroPayments("L");
							}
							if (!SwtUtil.isEmptyOrNull(schedulerConfigMap.get("corporatepayments"))) {
								report.setCorporatePayments(schedulerConfigMap.get("corporatepayments"));
							}else {
								// report.setCorporatePayments("C");
							}
							
							if (!SwtUtil.isEmptyOrNull(schedulerConfigMap.get("branchpayments"))) {
								report.setBranchPayments(schedulerConfigMap.get("branchpayments"));
							}
							
							if (!SwtUtil.isEmptyOrNull(schedulerConfigMap.get("otherpayments"))) {
								report.setOtherPayments(schedulerConfigMap.get("otherpayments"));
							}
							
							
						}else {
							report.setCostumerPayments("N");
						}
						
					}
					report.setScenario(schedulerConfigMap.get("scenarioid"));
					report.setIlmGroup(schedulerConfigMap.get("ilmgroup"));
					report.setDefaultIlmGrp(schedulerConfigMap.get("defaultilmgroupid"));
				} else {
					// Get the reports from Dynavalidator Form
					report = (Reports) getReports();
					report.setLoroPayments("true");
					// report.setCorporatePayments("true");
					reportType = request.getParameter("reportType");
					report.setReportType(reportType);
				}
			} else {
				// Get the reports from Dynavalidator Form
				report = (Reports) getReports();
				report.setLoroPayments("true");
				// report.setCorporatePayments("true");
				// Getting the User's Role Id from the session object 
				roleId = ((CommonDataManager) request.getSession().getAttribute(
						SwtConstants.CDM_BEAN)).getUser().getRoleId();
			}
					
			/* Retrieve Entity Id */
			entityId = report.getEntityId();
			// Retrieve Currency Code
			currencyCode = report.getCurrencyCode();
			// Retrieve Host Id 
			hostId = SwtUtil.getCurrentHostId();
			// Get user ID 
			userId = SwtUtil.getCurrentUserId(request.getSession());
			// Get the list of entities that will be figured in the entity combo
			entitiesLblValue = new ArrayList<LabelValueBean>();
			// Get the collection of user entity access
			ILMAnalysisMonitorManager ilmAnalysisMonitorManager = (ILMAnalysisMonitorManager)(SwtUtil.getBean("ilmAnalysisMonitorManager"));
			collUserEntityAccess = ilmAnalysisMonitorManager.getEntitiesHasCurrencies(hostId, roleId);
			
			reportType = report.getReportType();
			// Get the entity access collection
			entityAccessLvlColl = SwtUtil.convertEntityAcessCollectionLVLFullName (
					collUserEntityAccess, null);
			// Add the All option to the user as it could select All value in ILM screen
			if(SwtConstants.ILM_REPORT_TYPE_GROUP.equals(reportType) || SwtUtil.isEmptyOrNull(reportType) || SwtConstants.ILM_REPORT_TYPE_MULTI_CURRENCY_ILM_REPORTE_EXCEL.equals(reportType) || SwtConstants.ILM_REPORT_TYPE_INTRADAY_RISK_NET_CUM.equals(reportType)){
				entitiesLblValue.add(new LabelValueBean(SwtConstants.ALL_LABEL,SwtConstants.ALL_VALUE));
			}
			entitiesLblValue.addAll(entityAccessLvlColl);
			// Retrieve the entity id selected by the user, if first load then it will be default entity
			if (!SwtUtil.isEmptyOrNull(entityId)) {
				if(SwtConstants.ALL_VALUE.equals(entityId) && !(SwtConstants.ILM_REPORT_TYPE_GROUP).equals(report.getReportType()) && !SwtConstants.ILM_REPORT_TYPE_INTRADAY_RISK_NET_CUM.equals(reportType)&& !SwtConstants.ILM_REPORT_TYPE_MULTI_CURRENCY_ILM_REPORTE_EXCEL.equals(reportType) ){
					defaultEntityId = SwtUtil.getUserCurrentEntity(request.getSession());
					for (LabelValueBean o : entitiesLblValue){
					   if(o.getValue().equals(defaultEntityId))
						   entityExistInList = true;
					}
					if(!entityExistInList){
						if(entitiesLblValue.size() > 0)
							defaultEntityId = entitiesLblValue.iterator().next().getValue();
						else 
							defaultEntityId = null;
					}
				}
				else
				defaultEntityId = entityId;
			} else {
				String currentEntity = ((CommonDataManager) request.getSession()
						.getAttribute(SwtConstants.CDM_BEAN)).getUser()
						.getCurrentEntity();		
				defaultEntityId = SwtUtil.getUserCurrentEntity(request.getSession());
				// If the user don't have access to his default entity, then the function returns the first entity that has access
				if (!currentEntity.equals(defaultEntityId))
					defaultEntityId = "All";
				
				defaultEntityId = SwtUtil.getUserCurrentEntity(request.getSession());
				for (LabelValueBean o : entitiesLblValue){
				   if(o.getValue().equals(defaultEntityId))
					   entityExistInList = true;
				}
				if(!entityExistInList){
					if(entitiesLblValue.size() > 0)
						defaultEntityId = entitiesLblValue.iterator().next().getValue();
					else 
						defaultEntityId = null;
				}
			}
			if (!SwtUtil.isEmptyOrNull(configScheduler) && ("true".equals(configScheduler))
					&& !SwtUtil.isEmptyOrNull(newILMReportSchedConfig) && ("false".equals(newILMReportSchedConfig))) {
				defaultEntityId = report.getEntityId();
			}
			// Retrieve the default currency id for the user
			if ("true".equals(request.getParameter("isFromReportType"))) {
				defaultCcyId = currencyCode;
			} else {
				if (SwtConstants.ALL_VALUE.equals(defaultEntityId) && !isCcyChanged) {
					defaultCcyId = SwtUtil.getDomesticCurrencyForUser(request, hostId, SwtUtil.getUserCurrentEntity(request.getSession()));
				} else if (!isCcyChanged) {
					defaultCcyId = SwtUtil.getDomesticCurrencyForUser(request, hostId, defaultEntityId);
				} else {
					defaultCcyId = currencyCode;
				}
			}
			if (!SwtUtil.isEmptyOrNull(configScheduler) && ("true".equals(configScheduler))
					&& !SwtUtil.isEmptyOrNull(newILMReportSchedConfig) && ("false".equals(newILMReportSchedConfig))) {
				defaultCcyId = report.getCurrencyCode();
			}
                        // Set the date in currency timeframe based on entity and currency
                        dateInCcyTimeframe = reportsManager.getDateInCcyTimeframe(defaultEntityId,
                                        defaultCcyId, SwtUtil.getSystemDateFromDB());
			report.setEntityId(defaultEntityId);
			report.setCurrencyCode(defaultCcyId);
			if (SwtConstants.ALL_VALUE.equals(defaultEntityId)
							|| SwtConstants.ALL_VALUE.equals(defaultCcyId))
				request.setAttribute("screenFieldsStatus", SwtConstants.STR_TRUE);
			else
				request.setAttribute("screenFieldsStatus", SwtConstants.STR_FALSE);


			// Put the currencies list in the form
			ccyList = new ArrayList<LabelValueBean>();
			// add the all label into currency list
			ccyList.add(new LabelValueBean(SwtConstants.ALL_LABEL, SwtConstants.ALL_VALUE));
			// get the collection of currency access
			ccyAccessColl = SwtUtil.getSwtMaintenanceCache().getCurrencyViewORFullAccessLVL(ilmAnalysisMonitorManager.getEntityCurrency(defaultEntityId, roleId));
			if (collUserEntityAccess != null)
				ccyList.addAll(ccyAccessColl);


			// The case when the default ccy ID is not the ccy list, then select 'All' value, the first element in the list
			if (ccyAccessColl.size() > 0 && !defaultCcyId.equals("All")) {
				for (LabelValueBean ccy : ccyAccessColl) {
					if (ccy.getValue().equals(defaultCcyId)) {
						defaultCcyInTheList = true;
						break;
					}
				}
			}

			if(!defaultCcyInTheList){
				defaultCcyId = "All";
			}


			ILMGeneralMaintenanceManager ilmGenMgr=(ILMGeneralMaintenanceManager)(SwtUtil.getBean("ilmGeneralMaintenanceManager"));
			ilmAccountGroups = ilmGenMgr.getAllowedAccountGroupsListForUser(hostId, userId, defaultEntityId, defaultCcyId, reportType);
                        // Compute the maximum allowed report date (T-1) for the selected
                        // entity and currency. In case of multi-currency, use the earliest
                        // T-1 among available currencies.
						Calendar cal = Calendar.getInstance();
                        Date earliestTMinusOne = null;
                        Date earliestCcyDate = null;
                        if (SwtConstants.ALL_VALUE.equals(defaultCcyId)) {
                                if (ccyAccessColl != null) {
									Date dt = null;
                                        for (LabelValueBean ccy : ccyAccessColl) {
												dt = reportsManager.getDateInCcyTimeframe(defaultEntityId,
														ccy.getValue(), SwtUtil.getSystemDateFromDB());
                                                cal.setTime(dt);
                                                cal.add(Calendar.DATE, -1);
                                                Date tMinusOne = cal.getTime();
                                                if (earliestTMinusOne == null || tMinusOne.before(earliestTMinusOne)) {
                                                        earliestTMinusOne = tMinusOne;
                                                        earliestCcyDate = dt;
                                                }
                                        }
                                }
                        } else {
							Date inputDate = SwtUtil.getSystemDateFromDB();
							inputDate = reportsManager.getDateInCcyTimeframe(defaultEntityId, defaultCcyId, inputDate);


							cal.setTime(inputDate);
							cal.add(Calendar.DATE, -1);
							earliestTMinusOne = cal.getTime();
							earliestCcyDate = inputDate;
                        }
			
                        String ondayBefore = SwtUtil.formatDate(earliestTMinusOne,
                                        SwtUtil.getCurrentSystemFormats(request.getSession())
					.getDateFormatValue());
                        request.setAttribute("maxReportDate", ondayBefore);
			
                        cal.setTime(earliestCcyDate);
                        dateInCcyTimeframe = earliestCcyDate;
			//This line is hidden as When the current date is mid-Febraury 2020, the date range defaults to 1st to 31st December 2019, when it should actually be January.
//			if(cal.get(Calendar.MONTH)==1)
//				cal.set(cal.get(Calendar.YEAR)-1, 11, 1);
//			else
			cal.set(cal.get(Calendar.YEAR), cal.get(Calendar.MONTH)-1, 1);
			String firstDateOfPreviousMonth = null;
			String defaultFirstDateOfPreviousMonth = null;

			String fromDateAsString = report.getFromDateAsString();
			String dateFormat = SwtUtil.getCurrentSystemFormats(request.getSession()).getDateFormatValue();
			Date calDate = cal.getTime();

			if (SwtUtil.isEmptyOrNull(fromDateAsString)) {
				firstDateOfPreviousMonth = SwtUtil.formatDate(calDate, dateFormat);
			} else {
				Date fromDateAsDate = SwtUtil.parseDate(fromDateAsString, dateFormat);

				if (calDate.before(fromDateAsDate)) {
					firstDateOfPreviousMonth = SwtUtil.formatDate(calDate, dateFormat);
				} else {
					firstDateOfPreviousMonth = fromDateAsString;
				}
			}

// Optionally assign this value to defaultFirstDateOfPreviousMonth if needed:
			defaultFirstDateOfPreviousMonth = firstDateOfPreviousMonth;
					
			defaultFirstDateOfPreviousMonth = SwtUtil.formatDate(cal.getTime(), SwtUtil.getCurrentSystemFormats(request.getSession())
					.getDateFormatValue());
			cal.set(cal.get(Calendar.YEAR), cal.get(Calendar.MONTH), cal.getActualMaximum(Calendar.DATE));
			String lastDateOfPreviousMonth = SwtUtil.formatDate(cal.getTime(), SwtUtil
					.getCurrentSystemFormats(request.getSession())
					.getDateFormatValue());
			request.setAttribute("defaultLastDateOfPreviousMonth", lastDateOfPreviousMonth);
			request.setAttribute("yesterday", ondayBefore);
			request.setAttribute("dateInCcyTimeframe", SwtUtil.formatDate(dateInCcyTimeframe, "yyyy-MM-dd"));
			cal = Calendar.getInstance();
			cal.setTime(dateInCcyTimeframe);
			
			request.setAttribute("dateInCcyTimeframeAsString", SwtUtil.formatDate(cal.getTime(), SwtUtil
					.getCurrentSystemFormats(request.getSession())
					.getDateFormatValue()));
			request.setAttribute("firstDateOfPreviousMonth", firstDateOfPreviousMonth);
			request.setAttribute("defaultFirstDateOfPreviousMonth", defaultFirstDateOfPreviousMonth);
			if (SwtUtil.isEmptyOrNull(report.getToDateAsString())) {
				request.setAttribute("lastDateOfPreviousMonth", lastDateOfPreviousMonth);
			}


			if (SwtUtil.isEmptyOrNull(fromDateAsString)) {
				report.setFromDateAsString(ondayBefore);
			} else {
				Date fromDateAsDate = SwtUtil.parseDate(fromDateAsString, dateFormat);
				Date ondayBeforeAsDate = SwtUtil.parseDate(ondayBefore, dateFormat);

				if (ondayBeforeAsDate.before(fromDateAsDate)) {
					report.setFromDateAsString(ondayBefore);
				}
			}

			// Step 2: Compare with report.getToDateAsString()
			String toDateAsString = report.getToDateAsString();
			if (!SwtUtil.isEmptyOrNull(toDateAsString)) {
				Date toDateAsDate = SwtUtil.parseDate(toDateAsString, dateFormat);
				Date lastDayOfPreviousMonthAsDate = SwtUtil.parseDate(lastDateOfPreviousMonth, dateFormat);

				if (toDateAsDate.after(lastDayOfPreviousMonthAsDate)) {
					report.setToDateAsString(lastDateOfPreviousMonth);
					if(!"S".equals(report.getSingleOrRange())){
						report.setFromDateAsString(defaultFirstDateOfPreviousMonth);
					}
				}
				request.setAttribute("lastDateOfPreviousMonth", report.getToDateAsString());


			}

			// set attribute for entity label value
			request.setAttribute("entities", entitiesLblValue);

			scenariosLblValue = new ArrayList<LabelValueBean>();
			scenariosLblValue.add(new LabelValueBean("Standard",
					"Standard"));
			// Get the allowed scenarios that will be listed in the screen
			ILMTransScenarioMaintenanceManager ilmScenManager = (ILMTransScenarioMaintenanceManager)(SwtUtil.getBean("ilmTransScenarioMaintenanceManager"));
			

			
			entities.add("*");
			currencies.add("*");
			if(!SwtConstants.ALL_VALUE.equalsIgnoreCase(defaultEntityId))
				entities.add(defaultEntityId);
			if(!SwtConstants.ALL_VALUE.equalsIgnoreCase(defaultCcyId))
				currencies.add(defaultCcyId);
			
		     //get the active scenarios list that allow reporting .
			scenarioListDetail = (ArrayList) ilmScenManager.getILMScenarioForReporting(hostId, entities, currencies);
			for (int i = 0; i < scenarioListDetail.size(); i++) {
				ILMScenario ilmScen = (ILMScenario) scenarioListDetail.get(i);
				scenariosLblValue.add(new LabelValueBean(ilmScen.getIlmScenarioName(), ilmScen.getId().getIlmScenarioId()));
			}
			// Create a new list for the report type with static values
			reportTypeList = new ArrayList<LabelValueBean>();
			reportTypeList.add(new LabelValueBean(SwtUtil.getMessage("ilmReport.reportType.ilmGrpReport", request), SwtConstants.ILM_REPORT_TYPE_GROUP));
			reportTypeList.add(new LabelValueBean(SwtUtil.getMessage("ilmReport.reportType.multiCurrencyILMReportExcel", request), SwtConstants.ILM_REPORT_TYPE_MULTI_CURRENCY_ILM_REPORTE_EXCEL));
			reportTypeList.add(new LabelValueBean(SwtUtil.getMessage("ilmReport.reportType.intradayRisk.netCumulative", request), SwtConstants.ILM_REPORT_TYPE_INTRADAY_RISK_NET_CUM));
			reportTypeList.add(new LabelValueBean(SwtUtil.getMessage("ilmReport.reportType.baselA", request), SwtConstants.ILM_REPORT_TYPE_BASEL_A));
			reportTypeList.add(new LabelValueBean(SwtUtil.getMessage("ilmReport.reportType.baselB", request), SwtConstants.ILM_REPORT_TYPE_BASEL_B));
			reportTypeList.add(new LabelValueBean(SwtUtil.getMessage("ilmReport.reportType.baselC", request), SwtConstants.ILM_REPORT_TYPE_BASEL_C));
			// Set the date value, single or date range
			if (SwtUtil.isEmptyOrNull(report.getSingleOrRange())) {
				report.setSingleOrRange("S");
			}
			// Set the sum infows /outflows selection
			if (SwtUtil.isEmptyOrNull(report.getSumInOutFlows())) {
				if ((SwtConstants.ILM_REPORT_TYPE_BASEL_A).equals(report.getReportType())) {
					report.setSumInOutFlows("CB");
				} else if ((SwtConstants.ILM_REPORT_TYPE_BASEL_B).equals(report.getReportType())) {
					report.setSumInOutFlows("CORR");
				}
			}
			if ((SwtConstants.ILM_REPORT_TYPE_BASEL_A).equals(report.getReportType())) {
				listOfCcyAsString = new String();
				if(ccyAccessColl.size() > 0 && currencyCode != null && currencyCode.equals("All")){
					for (LabelValueBean ccy : ccyAccessColl)
						listOfCcyAsString+= "'"+ccy.getValue()+"',";
					
					listOfCcyAsString = listOfCcyAsString.substring(0, listOfCcyAsString.length() - 1);
				}else {
					listOfCcyAsString = "'"+currencyCode+"'";
				}
				//Check if the currency has a default central group 
				isCentralBankExist = ilmGenMgr.checkCentralBankGroupExists(hostId, userId, defaultEntityId, listOfCcyAsString);
			}else {
				isCentralBankExist = true;
			}
			// Set the ILM group selection
			if (SwtUtil.isEmptyOrNull(report.getDefaultIlmGrp())) {
				report.setDefaultIlmGrp("default");
			} 
			
			
			// adding into label bean
			request.setAttribute("keywords", SwtUtil.getKeywords(request));
			
			request.setAttribute("currencies", ccyList);
			request.setAttribute("reportTypes", reportTypeList);
			request.setAttribute("reportTypeInRequest", reportType);
			request.setAttribute("ilmAccountGroups", ilmAccountGroups);
			request.setAttribute("scenarios", scenariosLblValue);
			request.setAttribute("ilmReportType", report.getReportType());	
			request.setAttribute("isCentralBankExist", isCentralBankExist);
			request.setAttribute("changeEntity", request.getParameter("changeEntity"));
			request.setAttribute("configScheduler", configScheduler);
			request.setAttribute("newILMReportSchedConfig", newILMReportSchedConfig);
			request.setAttribute("schedulerConfigXML", request.getParameter("schedulerConfigXML"));
			request.setAttribute("jobId", request.getParameter("jobId"));
			request.setAttribute("roleId", roleId);
			request.setAttribute("costumerPayments", report.getCostumerPayments());	
			setReports(report);
		} catch (Exception exp) {
			exp.printStackTrace();
			log.error(this.getClass().getName()
					+ " - Exception Catched in [getILMReport] method : - "
					+ exp.getMessage());
			SwtUtil.logException(
					SwtErrorHandler.getInstance().handleException(exp,
							"getILMReport",
							ReportsAction.class), request, "");
			return ("fail");
		}

		log.debug(this.getClass().getName() + "- [getILMReport] - Exiting ");
		return ("success");
	}
	
	public String ILMReport() throws Exception {
		HttpServletResponse response = ServletActionContext.getResponse();
		HttpServletRequest request = ServletActionContext.getRequest();
		// dyna validator form to get and set the form values
// To remove: 		DynaValidatorForm dyForm = null;
		// variable to hold report
		Reports report = null;
		// Declaring the jasperPrint
		JasperPrint jasperPrint = null;
		// Initializing the outputstream
		ServletOutputStream out = null;
		// Initializing the PDF Exporter.
		JRPdfExporter exporter = null;
		String hostId = null;
		ILMGeneralMaintenanceManager ilmGeneralMaintenanceManager = null;
		ILMAnalysisMonitorManager ilmAnalysisManager = null;
		String dbLink = null;
		String roleId = null;
		String scenarioName = null;
		String documentName = null;
	    InputStream fileIn = null;
	    List<String>	sheetNamesList = null;
	    List<String>	templateSheetNamesList = null;
	    
	    ArrayList<Map<String, Object>> allBeansList = null;
	    String tokenForDownload = null;
	    CommonDataManager CDM = null;
		try {
			log.debug(this.getClass().getName()
					+ "- [ILMReport] - Entering ");
			// Initializing the Dynavalidator Form
// To remove: 			dyForm = (DynaValidatorForm) form;
			// Get the reports from Dynavalidator Form
			report = (Reports) getReports();
			/* Retrieve Host Id */
			hostId = SwtUtil.getCurrentHostId();
			// To get the parameters from reports.
			report.setEntityName(request.getParameter("entityText"));
			report.setUseCcyMultiplier(SwtUtil.isEmptyOrNull(report.getUseCcyMultiplier()) ? "N":"Y");
			report.setIlmGrpName(request.getParameter("ilmGrpName"));
		    if(SwtConstants.ILM_REPORT_TYPE_INTRADAY_RISK_NET_CUM.equals(report.getReportType())){
		    	report.setIlmGroup(null);
		    }
		    else if (SwtConstants.ALL_VALUE.equals(report.getEntityId())
					|| SwtConstants.ALL_VALUE.equals(report.getCurrencyCode()))
				if(!(SwtConstants.ILM_REPORT_TYPE_BASEL_B).equals(report.getReportType()))
					report.setIlmGroup(null);
			else if(request.getParameter("ilmGroup").equals("default")){
				ilmGeneralMaintenanceManager = (ILMGeneralMaintenanceManager)(SwtUtil.getBean("ilmGeneralMaintenanceManager"));
				ILMCcyParameters ilmCcy = ilmGeneralMaintenanceManager.getILMCcyParameterEditableData(hostId, report.getEntityId(), report.getCurrencyCode());
				report.setIlmGroup(ilmCcy.getGlobalGroupId());
			}
			
			ilmAnalysisManager = (ILMAnalysisMonitorManager)(SwtUtil.getBean("ilmAnalysisMonitorManager"));
			dbLink = ilmAnalysisManager.getCurrentDbLink(hostId);
			
			roleId = ((CommonDataManager) request.getSession().getAttribute(
					SwtConstants.CDM_BEAN)).getUser().getRoleId();
			
			scenarioName = report.getScenario();
			CDM = (CommonDataManager) request.getSession()
					.getAttribute("CDM");
			CDM.setExcelStyle(new HashMap<String, XSSFCellStyle>());
			
			if ((SwtConstants.ILM_REPORT_TYPE_BASEL_C).equals(report.getReportType())){
				if(!"Y".equals(report.getCostumerPayments())) {
					report.setLoroPayments(null);
					report.setBranchPayments(null);
					report.setOtherPayments(null);
					report.setCorporatePayments(null);
				
				}
			}
			if((SwtConstants.ILM_REPORT_TYPE_MULTI_CURRENCY_ILM_REPORTE_EXCEL).equals(report.getReportType())) {
				
				
				Reports reportTem = report;
				final String hostIdTemp = hostId;
				final String dbLinkTemp = dbLink;
				final String roleIdTemp = roleId;
				final String scenarioNameTemp = scenarioName;
				
				// Create a thread for the report generation process
				Thread reportGenerationThread = new Thread(new Runnable() {
				    public void run() {
				        try {
				        	
				        	ArrayList<Map<String, Object>> beansList = null;
				    	    ArrayList<Map<String, Object>> scenarioBeansList = null;
				    	    ArrayList<Map<String, Object>> allBeansListLocal = new ArrayList<Map<String,Object>>();
				    	    
				            // Fill the report
				        	scenarioBeansList = reportsManager.getILMReportExcelData(request, reportTem, hostIdTemp, dbLinkTemp, roleIdTemp);
							if(!"Standard".equals(scenarioNameTemp)){
								reportTem.setScenario("Standard");
								beansList = reportsManager.getILMReportExcelData(request, reportTem, hostIdTemp, dbLinkTemp, roleIdTemp);
								for(int i = 0; i < beansList.size() ; i++){
									allBeansListLocal.add(beansList.get(i));
									if(scenarioBeansList.size() > i )
										allBeansListLocal.add(scenarioBeansList.get(i));
								}
							}else {
								allBeansListLocal.addAll(scenarioBeansList);
							}

				            // Set the JasperPrint object to a volatile variable accessible from the main thread
							setAllBeansListVar(allBeansListLocal);

				        } catch (Exception e) {
				            // Handle the exception
				        }
				    }
				});

				
				reportGenerationThread.start();
				while (!reportGenerationThread.isInterrupted() && reportGenerationThread.isAlive()) {
					Thread.sleep(1000);
					CommonDataManager commonDataManager = request != null && request.getSession()!=null ?(CommonDataManager) request.getSession()
							.getAttribute(SwtConstants.CDM_BEAN):null;
					if(commonDataManager != null && "true".equalsIgnoreCase(commonDataManager.getCancelExport())) {
						reportGenerationThread.interrupt();
						setAllBeansListVar(null);
					}
				}


				// Wait for the report generation thread to finish
				reportGenerationThread.join();
				
				allBeansList = getAllBeansListVar();
				
					
				
				if("true".equals(CDM.getCancelExport())){
					tokenForDownload = request.getParameter("tokenForDownload");
					response.addCookie(new Cookie("fileDownloadToken", tokenForDownload+"|KO")); 
					response.setContentType("text/html");
					response.setHeader("Content-disposition","inline");
					return null;
				}
				
				
				String tabNameAvSummary = SwtUtil.getMessage("ilmExcelReport.tabNameAverageSummary", request);
				String tabNameDailyLiq = SwtUtil.getMessage("ilmExcelReport.tabNameDailyLiquidity", request);
				String tabNameTotPay = SwtUtil.getMessage("ilmExcelReport.tabNameTotalPayments", request);
				String tabNameavaiLiq = SwtUtil.getMessage("ilmExcelReport.tabNameAvailableLiquidity", request);
				String tabNameThrouIN = SwtUtil.getMessage("ilmExcelReport.tabNameThroughputIN", request);
				String tabNameThrouOut = SwtUtil.getMessage("ilmExcelReport.tabNameThroughputOUT", request);
				String tabNameCritOutf = SwtUtil.getMessage("ilmExcelReport.tabNameCriticalOutflows", request);
				String tabNameCritInf = SwtUtil.getMessage("ilmExcelReport.tabNameCriticalInflows", request);
				String tabNameCritInfDetails =SwtUtil.getMessage("ilmExcelReport.tabNameCriticalInflowsDetail", request);
				String tabNameCritOutfDetails =SwtUtil.getMessage("ilmExcelReport.tabNameCriticalOutflowsDetail", request);
				
				
				
				String tabNameAvSummaryEn = SwtUtil.getMessage("ilmExcelReport.tabNameAverageSummary", null);
				String tabNameDailyLiqEn = SwtUtil.getMessage("ilmExcelReport.tabNameDailyLiquidity", null);
				String tabNameTotPayEn = SwtUtil.getMessage("ilmExcelReport.tabNameTotalPayments", null);
				String tabNameavaiLiqEn = SwtUtil.getMessage("ilmExcelReport.tabNameAvailableLiquidity", null);
				String tabNameThrouINEn = SwtUtil.getMessage("ilmExcelReport.tabNameThroughputIN", null);
				String tabNameThrouOutEn = SwtUtil.getMessage("ilmExcelReport.tabNameThroughputOUT", null);
				String tabNameCritOutfEn = SwtUtil.getMessage("ilmExcelReport.tabNameCriticalOutflows", null);
				String tabNameCritInfEn = SwtUtil.getMessage("ilmExcelReport.tabNameCriticalInflows", null);
				String tabNameCritInfDetailsEn =SwtUtil.getMessage("ilmExcelReport.tabNameCriticalInflowsDetail", null);
				String tabNameCritOutfDetailsEn =SwtUtil.getMessage("ilmExcelReport.tabNameCriticalOutflowsDetail", null);
				
				sheetNamesList = new ArrayList<String>();
				templateSheetNamesList = new ArrayList<String>();
				if (report.getSingleOrRange().equalsIgnoreCase("S")) {
					//fileIn = ReportsAction.class.getResourceAsStream(SwtConstants.ILM_EXCEL_REPORTS_SINGLE_DAY_TEMPLATE);
					fileIn = new FileInputStream(request.getRealPath("/")+ SwtConstants.ILM_EXCEL_REPORTS_SINGLE_DAY_TEMPLATE);
					templateSheetNamesList.add(tabNameAvSummaryEn);
					if(!"Standard".equals(scenarioName)){
						templateSheetNamesList.add(tabNameAvSummaryEn);
					}
					templateSheetNamesList.add(tabNameCritOutfDetailsEn);
					if(!"Standard".equals(scenarioName)){
						templateSheetNamesList.add(tabNameCritOutfDetailsEn);
					}
					if("SHA".equals(report.getCriticalTransactions())) {
						templateSheetNamesList.add(tabNameCritInfDetailsEn);
					}

					sheetNamesList.add(tabNameAvSummary);
					if(!"Standard".equals(scenarioName)){
						sheetNamesList.add("SC_"+tabNameAvSummary);
					}
					sheetNamesList.add(tabNameCritOutfDetails);
					if(!"Standard".equals(scenarioName)){
						sheetNamesList.add("SC_"+tabNameCritOutfDetails);
					}
					
					if("SHA".equals(report.getCriticalTransactions())) {
						sheetNamesList.add(tabNameCritInfDetails);
					}
					
				}
				else {
					
					fileIn = new FileInputStream(request.getRealPath("/")+ SwtConstants.ILM_EXCEL_REPORTS_DATE_RANGE_TEMPLATE);
					
					templateSheetNamesList.add(tabNameAvSummaryEn);
					if(!"Standard".equals(scenarioName)){
						templateSheetNamesList.add(tabNameAvSummaryEn);
					}
					templateSheetNamesList.add(tabNameDailyLiqEn);
					if(!"Standard".equals(scenarioName)){
						templateSheetNamesList.add(tabNameDailyLiqEn);
					}
					templateSheetNamesList.add(tabNameTotPayEn);
					if(!"Standard".equals(scenarioName)){
						templateSheetNamesList.add(tabNameTotPayEn);
					}
					templateSheetNamesList.add(tabNameavaiLiqEn);
					if(!"Standard".equals(scenarioName)){
						templateSheetNamesList.add(tabNameavaiLiqEn);
					}
					templateSheetNamesList.add(tabNameThrouINEn);
					if(!"Standard".equals(scenarioName)){
						templateSheetNamesList.add(tabNameThrouINEn);
					}
					templateSheetNamesList.add(tabNameThrouOutEn);
					if(!"Standard".equals(scenarioName)){
						templateSheetNamesList.add(tabNameThrouOutEn);
					}
					templateSheetNamesList.add(tabNameCritOutfEn);
					//if show all (debit and credit) payement is selected from the radio button than add the critical inflow tab
					if("SHA".equals(report.getCriticalTransactions())){
						templateSheetNamesList.add(tabNameCritInfEn);
					}
					
					
					
					sheetNamesList.add(tabNameAvSummary);
					if(!"Standard".equals(scenarioName)){
						sheetNamesList.add("SC_"+tabNameAvSummary);
					}
					sheetNamesList.add(tabNameDailyLiq);
					if(!"Standard".equals(scenarioName)){
						sheetNamesList.add("SC_"+tabNameDailyLiq);
					}
					sheetNamesList.add(tabNameTotPay);
					if(!"Standard".equals(scenarioName)){
						sheetNamesList.add("SC_"+tabNameTotPay);
					}
					sheetNamesList.add(tabNameavaiLiq);
					if(!"Standard".equals(scenarioName)){
						sheetNamesList.add("SC_"+tabNameavaiLiq);
					}
					sheetNamesList.add(tabNameThrouIN);
					if(!"Standard".equals(scenarioName)){
						sheetNamesList.add("SC_"+tabNameThrouIN);
					}
					sheetNamesList.add(tabNameThrouOut);
					if(!"Standard".equals(scenarioName)){
						sheetNamesList.add("SC_"+tabNameThrouOut);
					}
					sheetNamesList.add(tabNameCritOutf);
					//if show all (debit and credit) payement is selected from the radio button than add the critical inflow tab
					if("SHA".equals(report.getCriticalTransactions())) {
						sheetNamesList.add(tabNameCritInf);
					}
					
				}
				ExcelTransformer transformer = new ExcelTransformer();
				
				// Silently ignore null values
				transformer.setSilent(true);
				transformer.setDebug(false);			
				Workbook workbook = transformer.transform(fileIn, templateSheetNamesList, sheetNamesList , allBeansList);				
				for(int i = 0; i < workbook.getNumberOfSheets() ; i++) {
					if(workbook.getSheetName(i).contains("SC_")){
						XSSFSheet sheet =(XSSFSheet) workbook.getSheetAt(i);
						// Define the color using XSSFColor
						XSSFColor darkBlueColor = new XSSFColor(new Color(0, 0, 139), null); // RGB for dark blue
						sheet.setTabColor(darkBlueColor);
					}
				}
				if(!"SHA".equals(report.getCriticalTransactions())) {
					if(report.getSingleOrRange().equalsIgnoreCase("S")){
						int sheetIndex = workbook.getSheetIndex(tabNameCritInfDetailsEn);
						if(sheetIndex != -1)
							workbook.removeSheetAt(sheetIndex);
					}else {
						int sheetIndex = workbook.getSheetIndex(tabNameCritInfEn);
						if(sheetIndex != -1)
							workbook.removeSheetAt(sheetIndex);
					}
					
				}

				response.setContentType("application/vnd.ms-excel");
				// Reinitialise used style to upgrade perfermance and do not get exception 
				CDM.setExcelStyle(new HashMap<String, XSSFCellStyle>());
	 			// .......... Export PDF Report ......//
	 			// downloadTokenValue will have been provided in the form submit via the hidden input field
				// Source scan tools may report a "HTTP response splitting" vulnerability. A generic solution of
				// ignoring text after CRLFs is implemented in XSSFilter class, so can safely ignore the report.
	 			tokenForDownload = request.getParameter("tokenForDownload");
	 			response.addCookie(new Cookie("fileDownloadToken", tokenForDownload+"|OK"));
				documentName = report.getSingleOrRange().equalsIgnoreCase("S") ? "SingleDate" : "DateRange";
				// To set the content as attachment
				response.setHeader("Content-disposition", "attachment; filename="
						+ documentName + "-SmartPredict_"
						+ SwtUtil.formatDate(new Date(), "yyyyMMdd") + "_"
						+ SwtUtil.formatDate(new Date(), "HHmmss") + ".xlsx");
				out = response.getOutputStream();
				
				workbook.write(out);
				//out.close();
				
		    }else {
				try {
					log.debug("[ILMReport] Starting report generation. Type: {}"+ report.getSingleOrRange());

					if ("S".equalsIgnoreCase(report.getSingleOrRange())) {
						log.debug("[ILMReport] Generating DAILY report for hostId: {}, roleId: {}"+ hostId+ roleId);
						jasperPrint = reportsManager.getILMReportDaily(request, report, hostId, dbLink, roleId);
					} else {
						log.debug("[ILMReport] Generating MONTHLY report for hostId: {}, roleId: {}"+ hostId+ roleId);
						jasperPrint = reportsManager.getILMReportMonthly(request, report, hostId, dbLink, roleId);
					}

					if (jasperPrint == null) {
						log.error("[ILMReport] JasperPrint is NULL. Report generation failed.");
						throw new SwtException("Failed to generate report: jasperPrint is null");
					}

					exporter = new JRPdfExporter();
					out = response.getOutputStream();

					log.info("[ILMReport] Setting response content type to PDF");
					response.setContentType("application/pdf");

					documentName = "S".equalsIgnoreCase(report.getSingleOrRange()) ? "Daily" : "DateRange";
					switch (report.getReportType()) {
						case SwtConstants.ILM_REPORT_TYPE_GROUP:
							documentName += "LiquidityManagementReport-ILMGroup";
							break;
						case SwtConstants.ILM_REPORT_TYPE_BASEL_A:
							documentName += "LiquidityManagementReport-BaselA";
							break;
						case SwtConstants.ILM_REPORT_TYPE_BASEL_B:
							documentName += "LiquidityManagementReport-BaselB";
							break;
						case SwtConstants.ILM_REPORT_TYPE_BASEL_C:
							documentName += "BCBS-248ReportC";
							break;
						case SwtConstants.ILM_REPORT_TYPE_INTRADAY_RISK_NET_CUM:
							documentName += "IntradayRisk-NetCumulativePositions";
							break;
						default:
							log.warn("[ILMReport] Unknown report type: {}"+ report.getReportType());
					}

					String fileName = documentName + "-SmartPredict_" +
									  SwtUtil.formatDate(new Date(), "yyyyMMdd") + "_" +
									  SwtUtil.formatDate(new Date(), "HHmmss") + ".pdf";

					log.debug("[ILMReport] Setting Content-disposition header with filename: {}"+ fileName);
					response.setHeader("Content-disposition", "attachment; filename=" + fileName);

					// Set exporter parameters
					log.debug("[ILMReport] Setting exporter parameters");
					exporter.setParameter(JRExporterParameter.JASPER_PRINT, jasperPrint);
					exporter.setParameter(JRExporterParameter.OUTPUT_STREAM, out);
					exporter.setParameter(JRExporterParameter.CHARACTER_ENCODING, "UTF-8");

					tokenForDownload = request.getParameter("tokenForDownload");
					log.debug("[ILMReport] Setting fileDownloadToken cookie: {}|OK"+  tokenForDownload);
					response.addCookie(new Cookie("fileDownloadToken", tokenForDownload + "|OK"));

					// Exporting report
					log.debug("[ILMReport] Calling exporter.exportReport()");
					exporter.exportReport();
					log.debug("[ILMReport] Report export completed successfully");

				} catch (JRException e) {
					log.error("[ILMReport] JasperReports export failed: {}", e);
					throw new SwtException("JasperReports export failed", e);
				} catch (IOException e) {
					log.error("[ILMReport] Error writing to response output stream: {}", e);
					throw new SwtException("Response I/O error", e);
				}

			}



		}catch(OutOfMemoryError exp){
			ActionMessages errors = new ActionMessages();
			errors.add("", new ActionMessage(exp.getMessage()));
			saveErrors(request, errors);
			// Source scan tools may report a "HTTP response splitting" vulnerability, a generic solution works by ignoring text after CRLFs is implemented XSSFilter class. 
			tokenForDownload = request.getParameter("tokenForDownload");
			response.addCookie(new Cookie("fileDownloadToken", tokenForDownload+"|MEM")); 
			response.setContentType("text/html");
			response.setHeader("Content-disposition","inline");
			log.debug(this.getClass().getName()
					+ "- [ILMReport] - Exception "
					+ exp.getMessage());
			log.error(this.getClass().getName()
					+ "- [ILMReport] - Exception "
					+ exp.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					new Exception("Out of memory exception") , "ILMReport", ReportsAction.class), request, "");
			
			return null;
		} catch (Exception exp) {
			ActionMessages errors = null;
			errors= new ActionMessages();
			errors.add("", new ActionMessage(exp.getMessage()));
			saveErrors(request, errors);
			// Source scan tools may report a "HTTP response splitting" vulnerability, a generic solution works by ignoring text after CRLFs is implemented XSSFilter class. 
			tokenForDownload = request.getParameter("tokenForDownload");
			if("true".equals(CDM.getCancelExport()))
				response.addCookie(new Cookie("fileDownloadToken", tokenForDownload+"|KO"));
			else
				response.addCookie(new Cookie("fileDownloadToken", tokenForDownload+"|ERR"));
			response.setContentType("text/html");
			response.setHeader("Content-disposition","inline");
			log.debug(this.getClass().getName()
					+ "- [ILMReport] - Exception "
					+ exp.getMessage());
			log.error(this.getClass().getName()
					+ "- [ILMReport] - Exception "
					+ exp.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "ILMReport", ReportsAction.class), request, "");
		} finally {
			CDM = (CommonDataManager) request.getSession()
					.getAttribute("CDM");
			CDM.setCancelExport("false");
			//nullyfing objects
// To remove: 			dyForm = null;
			report = null;
			jasperPrint = null;
			out = null;
			exporter = null;
			hostId = null;
			ilmGeneralMaintenanceManager = null;
			ilmAnalysisManager = null;
			dbLink = null;
			roleId = null;
			documentName = null;
		}
		log.debug(this.getClass().getName()
				+ "- [ILMReport] - Exiting ");
		return null;
	}
	
	/**
	 * 
	 * This function is called when the user click on the cancel button
	 * when the export is in progress.
	 * 
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return
	 * @throws SwtException
	 */
	public String cancelILMExport()
					throws SwtException {
HttpServletRequest request = ServletActionContext.getRequest();
HttpServletResponse response = ServletActionContext.getResponse();

		/* Set the cancelExport value from Flex part.
		   It will be 'true' only when the user cancels the export before accomplishment*/ 
		CommonDataManager CDM = (CommonDataManager) request.getSession()
				.getAttribute("CDM");
		CDM.setCancelExport("true");
		return null;
	}
	
	public String isAllEntityAvailable() throws SwtException {
HttpServletRequest request = ServletActionContext.getRequest();
HttpServletResponse response = ServletActionContext.getResponse();

		String hostId = null;
		String currencyId = null;
		String roleId = null;
		String isAllEntityAvailable = null;
		ILMAnalysisMonitorManager ilmAnalysisMonitorManager;
		try {
			log.debug(this.getClass().getName() + "- [isAllEntityAvailable] - starting ");
			hostId = SwtUtil.getCurrentHostId();
			currencyId = request.getParameter("selectedCurrencyCode");
			// Get the role ID from session 
			roleId = ((CommonDataManager) request.getSession().getAttribute(
					SwtConstants.CDM_BEAN)).getUser().getRoleId();
			ilmAnalysisMonitorManager = (ILMAnalysisMonitorManager)(SwtUtil.getBean("ilmAnalysisMonitorManager"));
			isAllEntityAvailable = ilmAnalysisMonitorManager.isAllEntityAvailable(hostId, currencyId, roleId);
			response.getWriter().print(isAllEntityAvailable.equalsIgnoreCase("y"));
			
			log.debug(this.getClass().getName() + " - [isAllEntityAvailable] - "
					+ "Exit");
			return null;
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [isAllEntityAvailable] method : - "
					+ exp.getMessage());
			SwtUtil.logException(
					SwtErrorHandler.getInstance().handleException(exp,
							"isAllEntityAvailable",
							ReportsAction.class), request,
					"");
			return ("fail");
		}
	}
	
	
	public String reportInProgressValidation() throws SwtException {
HttpServletRequest request = ServletActionContext.getRequest();
HttpServletResponse response = ServletActionContext.getResponse();

		try {
			log.debug(this.getClass().getName() + "- [isAllEntityAvailable] - starting ");
			response.getWriter().print(true);
			log.debug(this.getClass().getName() + " - [isAllEntityAvailable] - "
					+ "Exit");
			return null;
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [isAllEntityAvailable] method : - "
					+ exp.getMessage());
			SwtUtil.logException(
					SwtErrorHandler.getInstance().handleException(exp,
							"isAllEntityAvailable",
							ReportsAction.class), request,
					"");
			return ("fail");
		}
	}
	
	/**
	 * This method is used to validate dates from -> to if To Date is before the From Date
	 * It will return false otherwise true
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return
	 * @throws SwtException
	 */
	public String validateDates() throws SwtException {
HttpServletRequest request = ServletActionContext.getRequest();
HttpServletResponse response = ServletActionContext.getResponse();

		String toDate = null;
		String fromDate = null;
		String dateFormat = null;
		String entityId;
		String fromDateAsString = null; 
		String toDateAsString = null; 
		Date fromDateAsDate = null;
		Date toDateAsDate = null;
		
		try {
			log.debug(this.getClass().getName() + "- [validateDates] - starting ");
			
			
			dateFormat = SwtUtil.getCurrentDateFormat(request.getSession());
			fromDate = request.getParameter("fromDate");
			toDate = request.getParameter("toDate");
			entityId = request.getParameter("entityId");
			//fromDateAsString
			
			if(SysParamsDAOHibernate.keywordQuery.containsKey(fromDate)) {
				fromDateAsString = SwtUtil.convertKeywordToDate(request.getSession(),
						fromDate, false,
						entityId);
			}else {
				fromDateAsString = ""+fromDate;
			}
			if (!SwtUtil.isEmptyOrNull(fromDateAsString))
				fromDateAsDate = SwtUtil.parseDate(fromDateAsString, dateFormat);


			if(SysParamsDAOHibernate.keywordQuery.containsKey(toDate)) {
				toDateAsString = SwtUtil.convertKeywordToDate(request.getSession(),
						toDate, false,
						entityId);
			}else {
				toDateAsString = ""+toDate;
			}
			if (!SwtUtil.isEmptyOrNull(toDateAsString)) 
				toDateAsDate = SwtUtil.parseDate(toDateAsString, dateFormat);
				
			
			if(fromDateAsDate != null && toDateAsDate != null) {
				response.getWriter().print((toDateAsDate.after(fromDateAsDate) || toDateAsDate.equals(fromDateAsDate)));
			} else if(fromDateAsDate != null && toDateAsDate == null){
				response.getWriter().print(true);
			}else {
				response.getWriter().print(false);
			}
			
			log.debug(this.getClass().getName() + " - [validateDates] - "
					+ "Exit");
			return null;
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [validateDates] method : - "
					+ exp.getMessage());
			SwtUtil.logException(
					SwtErrorHandler.getInstance().handleException(exp,
							"validateDates",
							ReportsAction.class), request,
					"");
			return ("fail");
		}
	}
	
	public String checkMissingData()
			throws Exception {

		// Variable to hold hostId
		String hostId = null;
		String roleId = null;
		String reportType = null;
		String entityId = null;
		String currencyCode = null;
		String ilmGroup= null;
		String scenarioId = null;
		String startDateRange = null;
		String endDateRange = null;
		ArrayList<String> missingDates = null;
		ILMAnalysisMonitorManager ilmAnalysisManager = null;
		String dbLink = null;
		String resultStr = "S";
		try {
			log.debug(this.getClass().getName() + "- [checkMissingData] - Entering ");
			hostId = SwtUtil.getCurrentHostId();
			entityId = request.getParameter("entityId");
			currencyCode = request.getParameter("currencyCode");
			reportType = request.getParameter("reportType");
			ilmGroup = request.getParameter("ilmGroup");
			scenarioId = request.getParameter("scenarioId");
			startDateRange = request.getParameter("startDateRange");
			endDateRange = request.getParameter("endDateRange");
			ilmAnalysisManager = (ILMAnalysisMonitorManager)(SwtUtil.getBean("ilmAnalysisMonitorManager"));
			dbLink = ilmAnalysisManager.getCurrentDbLink(hostId);
			roleId = ((CommonDataManager) request.getSession().getAttribute(
					SwtConstants.CDM_BEAN)).getUser().getRoleId();
			missingDates = reportsManager.checkMissingData(request, reportType,
					hostId, entityId, currencyCode, ilmGroup, scenarioId, roleId,
					startDateRange, endDateRange, dbLink);
			if(missingDates != null){
				resultStr =  missingDates.get(0)+"#"+missingDates.get(1);	
			}
			response.getWriter().print(resultStr);
			log.debug(this.getClass().getName() + "- [checkMissingData] - Exiting ");
		} catch (Exception exp) {

			log.error(this.getClass().getName() + "- [checkMissingData] - Exception "
					+ exp.getMessage());
			response.getWriter().print("F");
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "checkMissingData", ReportsAction.class), request, "");
			//return ("fail");

		}

		return null;
	}

	public HashMap<String, String> convertschedulerConfigXMLtoHashmap(String schedulerConfigXML) {
		XStream xStream = new XStream(new StaxDriver());
		xStream.registerConverter(new SwtUtil.MapEntryConverter());
		xStream.alias("schedconfig", Map.class);
		xStream.setClassLoader(Thread.currentThread().getContextClassLoader());
		HashMap<String, String> extractedMap = (HashMap<String, String>) xStream.fromXML(schedulerConfigXML);
		return extractedMap;
	}

	public ArrayList<Map<String, Object>> getAllBeansListVar() {
		return allBeansListVar;
	}

	public void setAllBeansListVar(ArrayList<Map<String, Object>> allBeansListVar) {
		this.allBeansListVar = allBeansListVar;
	}

	
	

}
