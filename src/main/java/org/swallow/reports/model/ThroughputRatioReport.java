package org.swallow.reports.model;

import java.io.Serializable;
import java.util.Date;
import java.util.HashMap;

import javax.servlet.http.HttpServletRequest;

public class ThroughputRatioReport implements Serializable {
	private String reportType = null;
	private String outputFormat = null;
	private String entityId = null;
	private String entityName = null;
	private String currencyCode = null;
	private String currencyName = null;
	private String accountGroup = null;
	private String accountGroupName = null;
	private String dbLink = null;
	private String roleId = null;
	private String currencyFormat = null;

	private String scenarioId = null;
	private String scenarioName = null;
	private boolean singleGroup = true;
	private Date valueDate = null;
	private String useCurrencyMultiplier = null;
	private String applyCcyThreshold = null;
	private String userId = null;
	private String dateAsIso = null;
	private String initialFilter = null;
	private String CalculateAs = null; 
	private HttpServletRequest request;
	
	private HashMap<String, String> chartsData = new HashMap<String, String>();

	
	public String getDbLink() {
		return dbLink;
	}

	public void setDbLink(String dbLink) {
		this.dbLink = dbLink;
	}

	public String getRoleId() {
		return roleId;
	}

	public void setRoleId(String roleId) {
		this.roleId = roleId;
	}

	public HashMap<String, String> getChartsData() {
		return chartsData;
	}

	public void setChartsData(HashMap<String, String> chartsData) {
		this.chartsData = chartsData;
	}
	public String getReportType() {
		return reportType;
	}

	public void setReportType(String reportType) {
		this.reportType = reportType;
	}

	public String getOutputFormat() {
		return outputFormat;
	}

	public void setOutputFormat(String outputFormat) {
		this.outputFormat = outputFormat;
	}

	public String getEntityId() {
		return entityId;
	}

	public void setEntityId(String entityId) {
		this.entityId = entityId;
	}

	public String getEntityName() {
		return entityName;
	}

	public void setEntityName(String entityName) {
		this.entityName = entityName;
	}

	public String getCurrencyCode() {
		return currencyCode;
	}

	public void setCurrencyCode(String currencyCode) {
		this.currencyCode = currencyCode;
	}

	public String getCurrencyName() {
		return currencyName;
	}

	public void setCurrencyName(String currencyName) {
		this.currencyName = currencyName;
	}

	public String getAccountGroup() {
		return accountGroup;
	}

	public void setAccountGroup(String accountGroup) {
		this.accountGroup = accountGroup;
	}

	public String getAccountGroupName() {
		return accountGroupName;
	}

	public void setAccountGroupName(String accountGroupName) {
		this.accountGroupName = accountGroupName;
	}

	public String getScenarioId() {
		return scenarioId;
	}

	public void setScenarioId(String scenarioId) {
		this.scenarioId = scenarioId;
	}

	public String getScenarioName() {
		return scenarioName;
	}

	public void setScenarioName(String scenarioName) {
		this.scenarioName = scenarioName;
	}

	public boolean isSingleGroup() {
		return singleGroup;
	}

	public void setSingleGroup(boolean singleGroup) {
		this.singleGroup = singleGroup;
	}

	public Date getValueDate() {
		return valueDate;
	}

	public void setValueDate(Date valueDate) {
		this.valueDate = valueDate;
	}

	public String getUseCurrencyMultiplier() {
		return useCurrencyMultiplier;
	}

	public void setUseCurrencyMultiplier(String useCurrencyMultiplier) {
		this.useCurrencyMultiplier = useCurrencyMultiplier;
	}

	public String getApplyCcyThreshold() {
		return applyCcyThreshold;
	}

	public void setApplyCcyThreshold(String applyCcyThreshold) {
		this.applyCcyThreshold = applyCcyThreshold;
	}

	public String getUserId() {
		return userId;
	}

	public void setUserId(String userId) {
		this.userId = userId;
	}

	public String getDateAsIso() {
		return dateAsIso;
	}

	public void setDateAsIso(String dateAsIso) {
		this.dateAsIso = dateAsIso;
	}

	public String getInitialFilter() {
		return initialFilter;
	}

	public void setInitialFilter(String initialFilter) {
		this.initialFilter = initialFilter;
	}

	public HttpServletRequest getRequest() {
		return request;
	}

	public void setRequest(HttpServletRequest request) {
		this.request = request;
	}

	public String getCurrencyFormat() {
		return currencyFormat;
	}

	public void setCurrencyFormat(String currencyFormat) {
		this.currencyFormat = currencyFormat;
	}

	public String getCalculateAs() {
		return CalculateAs;
	}

	public void setCalculateAs(String calculateAs) {
		CalculateAs = calculateAs;
	}


}
