/*
 * @(#)CurrencyFunding.java 1.0 10/08/2009
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.reports.model;

import java.util.Collection;
import org.swallow.model.BaseObject;
/**
 * 
 * <AUTHOR> 
 * Class contain setter,getter methods. These methods useful to set the value and 
 * get value in other classes.
 * 
 */
public class CurrencyFunding extends BaseObject implements
		org.swallow.model.AuditComponent {
	/* Global variable declaration */
	private String dateAsString;
	private String currencyCode;
	private String threshold;
	private String showDR;
	private String showCR;	
	private double amount;
	private String accountId;
	private String acctName;


	private Id id = new Id();

	/* Static class for primary key */
	public static class Id extends BaseObject {
		/* Local variable declaration */
		private String hostId;
		private String entityId;
		
		/**
		 * getter method for entityId
		 * @return entityId
		 */
		public String getEntityId() {
			return entityId;
		}

		/**
		 * setter method for entityId
		 * @param entityId - String
		 */
		public void setEntityId(String entityId) {
			this.entityId = entityId;
		}

		/**
		 * getter method for hostId
		 * @return hostId
		 */
		public String getHostId() {
			return hostId;
		}

		/**
		 * setter method for hostId
		 * @param hostId
		 */
		public void setHostId(String hostId) {
			this.hostId = hostId;
		}

		

	}
	
	/**
	 * getter method for accountId
	 * @return accountId
	 */
	public String getAccountId() {
		return accountId;
	}

	/**
	 * setter method for accountId
	 * @param accountId - String
	 * 
	 */

	public void setAccountId(String accountId) {
		this.accountId = accountId;
	}
	
	/**
	 * getter method for accountName
	 * @return acctName
	 */
	public String getAccountName() {
		return acctName;
	}

	/**
	 * setter method for accountName
	 * @param acctName - String
	 * 
	 */

	public void setAccountName(String acctName) {
		this.acctName = acctName;
	}
	
	/**
	 * getter method for unique Id
	 * @return Id
	 */
	public Id getId() {
		return id;
	}

	/**
	 * setter method for id
	 * @param id - Id
	 */

	public void setId(Id id) {
		this.id = id;
	}

	/**
	 * getter method for dateAsString
	 * @return dateAsString
	 */
	public String getDateAsString() {
		return dateAsString;
	}

	/**
	 * setter method for dateAsString
	 * @param dateAsString - String
	 */

	public void setDateAsString(String dateAsString) {
		this.dateAsString = dateAsString;
	}

	/**
	 * getter method for currencyCode
	 * @return currencyCode
	 */
	public String getCurrencyCode() {
		return currencyCode;
	}

	/**
	 * setter method for currencyCode
	 * @param currencyCode - String
	 */

	public void setCurrencyCode(String currencyCode) {
		this.currencyCode = currencyCode;
	}

	/**
	 * getter method for Threshold
	 * @return threshold
	 */
	public String getThreshold() {
		return threshold;
	}

	/**
	 * setter method for Threshold
	 * @param threshold - String
	 */

	public void setThreshold(String threshold) {
		this.threshold = threshold;
	}

	/**
	 * getter method for ShowDR
	 * @return showDR
	 */
	public String getShowDR() {
		return showDR;
	}

	/**
	 * setter method for ShowDR
	 * @param showDR
	 */

	public void setShowDR(String showDR) {
		this.showDR = showDR;
	}

	/**
	 * getter method for ShowCR
	 * @return showDR
	 */
	public String getShowCR() {
		return showCR;
	}

	/**
	 * setter method for ShowCR
	 * @param showCR
	 */
	public void setShowCR(String showCR) {
		this.showCR = showCR;
	}
	
	/**
	 * getter method for amount
	 * @return amount.
	 */
	public Double getAmount() {
		return amount;
	}

	/**
	 * setter method for amount
	 * @param amount
	 */
	public void setAmount(Double amount) {
		this.amount = amount;
	}

}
