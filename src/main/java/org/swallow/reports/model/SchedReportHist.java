package org.swallow.reports.model;

import java.util.Date;
import java.util.Hashtable;

import org.swallow.model.BaseObject;

public class SchedReportHist extends BaseObject {

	private static final long serialVersionUID = 1L;
	public static Hashtable logTable = new Hashtable();
	static {
		logTable.put("jobId","job Id");
		logTable.put("reportTypeId","report Type Id");
		logTable.put("scheduleId","Schedule Id");
		logTable.put("runDate","run Date");
		logTable.put("elapsedTime","elapsed Time");
		logTable.put("fileName","file Name");
		logTable.put("outputLocation","output Location");
		logTable.put("fileSize","file Size");
		logTable.put("exportStatus","export Status");
		logTable.put("mailStatus","mail Status");
		logTable.put("exportError","export Error");
		logTable.put("mailRsult","mail Result");
		logTable.put("reportName","report Name");
		logTable.put("jobName","job Name");
		logTable.put("reportTypeName","repor tType Name");
		logTable.put("scheduleName","schedule Name");
		logTable.put("mailError","mail Error");
	}
	private Id id = new Id();
	private String hostId;
	private String jobId;
	private String reportTypeId;
	private Integer scheduleId;
	private Date runDate;
	private String runDateAsString;
	private Long elapsedTime;
	private String elapsedTimeAsString;
	private String fileName;
	private String outputLocation;
	private String fileSize;
	private String exportStatus;
	private String mailStatus;
	private String mailStatusAsString;
	private String exportError;
	private String mailRsult;
	private String reportName;
	private String jobName;
	private String reportTypeName;
	private String scheduleName;
	private String mailError;
	private String reportDescription;
	
	

	public Id getId() {
		return id;
	}

	public void setId(Id id) {
		this.id = id;
	}

	public String getHostId() {
		return hostId;
	}

	public void setHostId(String hostId) {
		this.hostId = hostId;
	}

	public String getJobId() {
		return jobId;
	}

	public void setJobId(String jobId) {
		this.jobId = jobId;
	}

	public String getReportTypeId() {
		return reportTypeId;
	}

	public void setReportTypeId(String reportTypeId) {
		this.reportTypeId = reportTypeId;
	}

	public Integer getScheduleId() {
		return scheduleId;
	}

	public void setScheduleId(Integer scheduleId) {
		this.scheduleId = scheduleId;
	}

	public Date getRunDate() {
		return runDate;
	}

	public void setRunDate(Date runDate) {
		this.runDate = runDate;
	}

	public Long getElapsedTime() {
		return elapsedTime;
	}

	public void setElapsedTime(Long elapsedTime) {
		this.elapsedTime = elapsedTime;
	}

	public String getFileName() {
		return fileName;
	}

	public void setFileName(String fileName) {
		this.fileName = fileName;
	}

	public String getOutputLocation() {
		return outputLocation;
	}

	public void setOutputLocation(String outputLocation) {
		this.outputLocation = outputLocation;
	}

	public String getFileSize() {
		return fileSize;
	}

	public void setFileSize(String fileSize) {
		this.fileSize = fileSize;
	}

	public String getExportStatus() {
		return exportStatus;
	}

	public void setExportStatus(String exportStatus) {
		this.exportStatus = exportStatus;
	}

	public String getMailStatus() {
		return mailStatus;
	}

	public void setMailStatus(String mailStatus) {
		this.mailStatus = mailStatus;
	}

	public String getExportError() {
		return exportError;
	}

	public void setExportError(String exportError) {
		this.exportError = exportError;
	}

	public String getMailRsult() {
		return mailRsult;
	}

	public void setMailRsult(String mailRsult) {
		this.mailRsult = mailRsult;
	}

	public String getRunDateAsString() {
		return runDateAsString;
	}

	public void setRunDateAsString(String runDateAsString) {
		this.runDateAsString = runDateAsString;
	}

	public String getElapsedTimeAsString() {
		return elapsedTimeAsString;
	}

	public void setElapsedTimeAsString(String elapsedTimeAsString) {
		this.elapsedTimeAsString = elapsedTimeAsString;
	}

	public String getReportName() {
		return reportName;
	}

	public void setReportName(String reportName) {
		this.reportName = reportName;
	}

	public String getReportTypeName() {
		return reportTypeName;
	}

	public void setReportTypeName(String reportTypeName) {
		this.reportTypeName = reportTypeName;
	}

	public String getScheduleName() {
		return scheduleName;
	}

	public void setScheduleName(String scheduleName) {
		this.scheduleName = scheduleName;
	}

	public String getJobName() {
		return jobName;
	}

	public void setJobName(String jobName) {
		this.jobName = jobName;
	}

	public String getMailStatusAsString() {
		return mailStatusAsString;
	}

	public void setMailStatusAsString(String mailStatusAsString) {
		this.mailStatusAsString = mailStatusAsString;
	}

	public String getMailError() {
		return mailError;
	}

	public void setMailError(String mailError) {
		this.mailError = mailError;
	}

	public String getReportDescription() {
		return reportDescription;
	}

	public void setReportDescription(String reportDescription) {
		this.reportDescription = reportDescription;
	}

	public static class Id extends BaseObject {
		private static final long serialVersionUID = 1L;
		private Long fileId;

		public Id() {
		}

		public Long getFileId() {
			return fileId;
		}

		public void setFileId(Long fileId) {
			this.fileId = fileId;
		}
	}
}