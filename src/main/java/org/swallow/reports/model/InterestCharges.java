/*
 * @(#)InterestCharges.java 1.0 10/08/09
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.reports.model;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.swallow.model.BaseObject;

public class InterestCharges extends BaseObject implements
		org.swallow.model.AuditComponent {

	/*
	 * Start:Code Modified For Mantis 1622 by Sudhakar on 03-09-2012: Interest
	 * Charges by Account Report: From and To date range instead of Month/Year
	 */
	// From Date
	private String fromDate;
	// Currency Code
	private String currencyCode;
	// Account Name
	private String accountName;
	// To Date
	private String toDate;
	/*
	 * End:Code Modified For Mantis 1622 by Sudhakar on 03-09-2012: Interest
	 * Charges by Account Report: From and To date range instead of Month/Year
	 */
	// Composite Id
	private Id id = new Id();
	private final Log log = LogFactory.getLog(InterestCharges.class);

	/* Static class for primary key */
	public static class Id extends BaseObject {
		// Host Id
		private String hostId;
		// Entity Id
		private String entityId;
		// Account Id
		private String accountId;

		/* getter method for entityId */
		public String getEntityId() {
			return entityId;
		}

		/**
		 * @param entityId -
		 *            String setter method for entityId
		 */

		public void setEntityId(String entityId) {
			this.entityId = entityId;
		}

		/* getter method for hostId */
		public String getHostId() {
			return hostId;
		}

		/**
		 * @param hostId -
		 *            String setter method for hostId
		 */

		public void setHostId(String hostId) {
			this.hostId = hostId;
		}

		/* getter method for accountId */
		public String getAccountId() {
			return accountId;
		}

		/**
		 * @param accountId -
		 *            String setter method for accountId
		 */

		public void setAccountId(String accountId) {
			this.accountId = accountId;
		}

	}

	/* getter method for unique Id */
	public Id getId() {
		return id;
	}

	/**
	 * @param id -
	 *            Id setter method for id
	 */

	public void setId(Id id) {
		this.id = id;
	}

	/* getter method for currencyCode */
	public String getCurrencyCode() {
		return currencyCode;
	}

	/**
	 * @param currencyCode -
	 *            String setter method for currencyCode
	 */

	public void setCurrencyCode(String currencyCode) {
		this.currencyCode = currencyCode;
	}

	/* getter method for accountName */
	public String getAccountName() {
		return accountName;
	}

	/**
	 * @param accountName -
	 *            String setter method for accountName
	 */

	public void setAccountName(String accountName) {
		this.accountName = accountName;
	}

	/*
	 * Start:Code Modified For Mantis 1622 by Sudhakar on 03-09-2012: Interest
	 * Charges by Account Report: From and To date range instead of Month/Year
	 */
	/**
	 * Getter method of FromDate
	 * 
	 * @return the fromDate
	 */
	public String getFromDate() {
		return fromDate;
	}

	/**
	 * Setter method of FromDate
	 * 
	 * fromDate the fromDate to set
	 */
	public void setFromDate(String fromDate) {
		this.fromDate = fromDate;
	}

	/**
	 * Getter method of ToDate
	 * 
	 * @return the ToDate
	 */
	public String getToDate() {
		return toDate;
	}

	/**
	 * Setter method of ToDate
	 * 
	 * toDate the toDate to set
	 */
	public void setToDate(String toDate) {
		this.toDate = toDate;
	}
	/*
	 * End:Code Modified For Mantis 1622 by Sudhakar on 03-09-2012: Interest
	 * Charges by Account Report: From and To date range instead of Month/Year
	 */
}
