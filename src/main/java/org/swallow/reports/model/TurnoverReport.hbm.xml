<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE hibernate-mapping PUBLIC
        "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
        "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">

<hibernate-mapping>
    <class name="org.swallow.reports.model.TurnoverReport" table="S_ENTITY">
		<composite-id name="id" class="org.swallow.reports.model.TurnoverReport$Id" unsaved-value="any">
        <key-property name="hostId" access="field" column="HOST_ID"/> 
        <key-property name="entityId" access="field" column="ENTITY_ID" />
               
        </composite-id>		
    </class>
</hibernate-mapping>