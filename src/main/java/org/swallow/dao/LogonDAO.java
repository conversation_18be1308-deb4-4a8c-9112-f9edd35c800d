/*
 * Created on Nov 4, 2005
 *
 * TODO To change the template for this generated file go to
 * Window - Preferences - Java - Code Style - Code Templates
 */
package org.swallow.dao;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

import org.swallow.control.model.UserStatus;
import org.swallow.exception.SwtException;
import org.swallow.model.MenuItem;
import org.swallow.model.User;


public interface LogonDAO extends DAO{

	public ArrayList verifyUser(User user,String ipAddress, int...processInt) throws SwtException;
	public Collection getMenuList(User user) throws SwtException;
	public Collection getScreenList(User user) throws SwtException;
	public Collection getShortcutList(User user) throws SwtException;
	public Collection getProfileList(User user) throws SwtException;
	public Collection getUserProfileDetails(User user) throws SwtException;
	public Collection getEntityAccessList(String hostId, String roleId) throws SwtException;
	public void saveUserStatus(UserStatus userStatus) throws SwtException;
	public Collection getMenuListUpdated(User user) throws SwtException;

	/*START:Code added by Mahesh on 03-Mar-2009 for Mantis 904 
     *Password change and invPassAttempt should have entries in system log.   
     */
	/**
	 *This method used to save the Password change and invPassAttempt events in system log.
	 * @param user
	 * @return
	 * @throws SwtException
	 */
	public  void saveChangePassword(User user)  throws SwtException;
	/*End:Code added by Mahesh on 03-Mar-2009 for Mantis 904*/
	public void updateUserStatus(UserStatus userStatus) throws SwtException;
	public Collection getDomesticCurrency(String hostId,String entityId) throws SwtException;
	// Method logs the status of already logged-in user
	public void enterLoggedInUserStatus(User user,String ipAddress) throws SwtException;
	public void saveUserStatusRow(UserStatus userStatus) throws SwtException;
	/*Start:Code added by Venkat on 03-Jan-2010 for Mantis 1267 */	
	public String getHostIdFromDB();
	/*END:Code added by Venkat on 03-Jan-2010 for Mantis 1267 */	

	/* Start: Code Chnaged by Mayank for Making User Login and Add User working */

    /**
     * This method update the user details to the database.
     * @param user
     * @throws SwtException
     */
    public void updateUserDetail(User user)
    throws SwtException;
    /* End: Code Chnaged by Mayank for Making User Login and Add User working */

    public Collection getAccessIdOfShortcut(String roleId, String menuItemId) throws SwtException;
    public User getUserDetail(String hostId, String userId) throws SwtException;
    public Collection getLocationAccessList(String roleId , String hostId, String entityId) throws SwtException;

    /* START: Code changed as par SRS - Group Monitors, 19-SEP-2007 */
    public Collection getMetagroupDetails(String hostId, String entityId) throws SwtException;

    public Collection getGroupDetails(String hostId, String entityId) throws SwtException;
    /* END: Code changed as par SRS - Group Monitors, 19-SEP-2007 */
    
    /*Start : Setting User Preference for Flex Screens Done on 12-02-2008*/
    void setUserPreference(List userPreferenceList, String hostId, String entityId, String userId) throws SwtException;
	List getUserPreference(String hostId, String entityId, String userId);
	/*End : Setting User Preference for Flex Screens Done on 12-02-2008*/   
	public MenuItem getMenuItem(String menuItemId, User user) throws SwtException ;
	public MenuItem getMenuItemForRole(String menuItemId, String roleId) throws SwtException ;
	public User getUserDetailByExtAuthId(String hostId, String extAuthId)  throws SwtException ;
	public MenuItem getMenuItem(String menuItemId) throws SwtException;
}