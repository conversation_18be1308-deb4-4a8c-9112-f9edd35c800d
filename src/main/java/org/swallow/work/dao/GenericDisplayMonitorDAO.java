/*
 * @(#)GenericDisplayMonitorDAO.java 1.0 30/11/12
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.work.dao;

import java.util.ArrayList;

import org.swallow.exception.SwtException;
import org.swallow.util.OpTimer;
import org.swallow.work.model.GenericDisplayPageDTO;
import org.swallow.work.model.QueryResult;

public interface GenericDisplayMonitorDAO {

	/**
	 * 
	 * @param page
	 * @param opTimer
	 * @param baseQuery
	 * @return QueryResult
	 * @throws SwtException
	 */
	public QueryResult getQueryResultPage(GenericDisplayPageDTO page, OpTimer opTimer,String baseQuery,String scenarioId, String roleId,String currencyGroup,String applyCurrencyThreshold,boolean...fromExport)
			throws SwtException ;

	/**
	 * 
	 * @param baseQuery
	 * @return String
	 * @throws SwtException
	 */
	public String getRecords(String baseQuery) throws SwtException ;
	
	/**
	 * 
	 * @param baseQuery
	 * @return QueryResult
	 * @throws SwtException
	 */
	public QueryResult getGenericDisplayData(String baseQuery) throws SwtException ;

	/**
	 * 
	 * @param facilityId
	 * @return ArrayList
	 */
	public ArrayList getScreenDetails(String facilityId) throws SwtException;

	/**
	 * 
	 * @param hostId
	 * @param roleId
	 * @param entityId
	 * @param currencyCode
	 * @param facilityId
	 * @return String
	 * @throws SwtException 
	 */
	public String getFacilityAccess(String hostId, String roleId,
			String entityId, String currencyCode, String facilityId) throws SwtException;
}
