/*
 * @(#)NotesDAO.java 1.0 27/01/06
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.work.dao;

import java.util.Collection;

import org.swallow.dao.DAO;
import org.swallow.exception.SwtException;
import org.swallow.work.model.MatchNote;
import org.swallow.work.model.MovementNote;
import org.swallow.work.model.SweepNote;

/**
 * <AUTHOR> This class is a DAO interface to get notes details and also
 *         add/view/delete notes
 * 
 */
public interface NotesDAO extends DAO {
	/**
	 * 
	 * @param hostId
	 * @param movementId
	 * @return
	 * @throws SwtException
	 */
	public Collection getNoteDetails(String hostId, Long movementId)
			throws SwtException;

	/**
	 * 
	 * @param movementNote
	 * @throws SwtException
	 */
	public void saveNotesDetails(MovementNote movementNote) throws SwtException;

	/**
	 * 
	 * @param movementNote
	 * @throws SwtException
	 */
	public void deleteNote(MovementNote movementNote) throws SwtException;

	/**
	 * 
	 * @param hostId
	 * @param matchId
	 * @return
	 * @throws SwtException
	 */
	public Collection getMatchNoteDetails(String hostId, Long matchId)
			throws SwtException;

	/**
	 * 
	 * @param matchNote
	 * @throws SwtException
	 */
	public void saveMatchNotesDetails(MatchNote matchNote) throws SwtException;

	/**
	 * 
	 * @param matchNote
	 * @throws SwtException
	 */
	public void deleteMatchNote(MatchNote matchNote) throws SwtException;

	/**
	 * 
	 * @param hostId
	 * @param sweepId
	 * @return
	 * @throws SwtException
	 */
	public Collection getSweepNoteDetails(String hostId, Long sweepId, String archiveId)
			throws SwtException;

	/**
	 * 
	 * @param sweepNote
	 * @throws SwtException
	 */
	public void saveSweepNotesDetails(SweepNote sweepNote) throws SwtException;

	/**
	 * 
	 * @param sweepNote
	 * @throws SwtException
	 */
	public void deleteSweepNote(SweepNote sweepNote) throws SwtException;

	/**
	 * @param hostId
	 * @param entityId
	 * @param matchId
	 * @return
	 */
	public boolean getMatchDetails(String hostId, String entityId,
			String matchId) throws SwtException;

	/**
	 * This method is used to get the currency for selected Match/Movement ID
	 * 
	 * @param entityId
	 * @param movementId
	 * @param matchId
	 * @return
	 * @throws SwtException
	 */
	public String getCurrency(String entityId, Long movementId, Long matchId)
			throws SwtException;
	/*
	 * Start:Code Modified for Mantis 1270 by Chinniah on 29-Feb-2012:Data
	 * Archive setup: Remove redundant fields from Archive setup screen
	 */
	/**
	 * This method is used to get the Movement notes details
	 * 
	 * @param entityId
	 * @param movementId
	 * @param archiveId
	 * @return Collection
	 * @throws SwtException
	 */
	public Collection getArchiveNoteDetails(String entityId, Long movementId,
			String archiveId) throws SwtException;

	/**
	 * This method is used to get the Match notes details
	 * 
	 * @param hostId
	 * @param matchId
	 * @param archiveId
	 * @param entityId
	 * @return Collection
	 * @throws SwtException
	 */
	public Collection<MatchNote> getArchiveMatchNoteDetails(String hostId,
			Long matchId, String archiveId, String entityId)
			throws SwtException;
	/*
	 * End:Code Modified for Mantis 1270 by Chinniah on 29-Feb-2012:Data
	 * Archive setup: Remove redundant fields from Archive setup screen
	 */
}
