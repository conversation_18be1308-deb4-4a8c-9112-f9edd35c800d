/*
 * @(#)CurrencyMonitorNewDAO.java 1.0 18/08/06
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.work.dao;

import java.util.Date;

import org.swallow.exception.SwtException;
import org.swallow.util.OpTimer;
import org.swallow.util.SystemFormats;
import org.swallow.work.model.CurrencyRecordVO;

/**
 * <pre>
 * DAO layer for Currency Monitor screen. This interface performs following
 * tasks 
 *  - Get currency based predicted balance
 * </pre>
 * 
 * <AUTHOR>
 */
public interface CurrencyMonitorNewDAO {

	/**
	 * This method gets currency based predicted/loro balances for the period
	 * specified (range of maximum of 14 value dates) by invoking the stored
	 * procedure PK_MONITORS.SP_CURRENCY_MONITOR_JOB
	 * 
	 * @param String
	 *            entityId
	 * @param String
	 *            currGrp
	 * @param String
	 *            userId
	 * @param String
	 *            roleId
	 * @param Date
	 *            startDate
	 * @param Date
	 *            endDate
	 * @param boolean
	 *            hideWeekends
	 * @param SystemFormats
	 *            systemFormat
	 * @param OpTimer
	 *            opTimer
	 * @return CurrencyRecordVO
	 * @throws SwtException
	 */
	public CurrencyRecordVO getAllBalancesUsingStoredProc(String entityId,
			String currGrp, String userId, String roleId, Date startDate,
			Date endDate, boolean hideWeekends, SystemFormats systemFormat,
			OpTimer opTimer) throws SwtException;
}