/*
 * @(#)MatchDisplayDAO.java 1.0 04/01/2006
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.work.dao;

import java.util.Collection;
import java.util.Date;
import java.util.List;

import org.swallow.dao.DAO;
import org.swallow.exception.SwtException;
import org.swallow.maintenance.model.EntityPositionLevel;
import org.swallow.work.model.Match;
import org.swallow.work.model.Movement;
import org.swallow.work.model.PrevMatch;

/**
 * This DAO class is used to get Movement details,Match details,Update
 * Movements,Update match,Check lock for Movement,get all position levels,get
 * Account Access status
 * 
 */
public interface MatchDisplayDAO extends DAO {

	/**
	 * Get the movement details for the given matchId from p_movement table
	 * 
	 * @param String
	 *            entityId
	 * @param String
	 *            hostId
	 * @param String
	 *            matchId
	 * 
	 * @return Collection object
	 */
	public Collection<Movement> getMovementDetailList(String entityId,
			String hostId, String matchId) throws SwtException;

	/**
	 * Get the entityName for the given entityId from s_entity table
	 * 
	 * @param String
	 *            entityId
	 * @param String
	 *            hostId
	 * @return List object
	 */
	public String getEntityName(String entityId, String hostId)
			throws SwtException;

	/**
	 * Get the match details for the given matchId from p_match table
	 * 
	 * @param String
	 *            entityId
	 * @param String
	 *            hostId
	 * @param String
	 *            matchId
	 * @return Collection object
	 */
	public Collection getMatchDetails(String entityId, String hostId,
			String matchId) throws SwtException;

	/**
	 * Update the movement details into p_movement table
	 * 
	 * @param Movement
	 *            mvt
	 * @return void
	 */
	public void updateMovements(Movement mvt) throws SwtException;

	/**
	 * @param PrevMatch
	 *            returns void
	 * @throws SwtException
	 */
	public void insertPrevMatch(PrevMatch prevMatch) throws SwtException;

	/**
	 * @param hostId
	 * @param entityId
	 * @param movementId
	 * @param matchFlag
	 *            returns Collection
	 * @throws SwtException
	 */
	public Collection getPrevMatch(String hostId, String entityId,
			String movementId, boolean matchFlag) throws SwtException;

	/**
	 * Gets the movement details from p_movement table
	 * 
	 * @param String
	 *            entityId
	 * @param String
	 *            hostId
	 * @param String
	 *            movementIds
	 * @return Collection object
	 */
	public Collection getMovementDetails(String entityId, String hostId,
			String movementIds) throws SwtException;

	/**
	 * Update the match details into p_match table
	 * 
	 * @param Match
	 *            match
	 * @return void
	 */
	public void updateMatch(Match match) throws SwtException;

	/**
	 * Gets the lock status from p_movement_lock table
	 * 
	 * @param String
	 *            hostId
	 * @param Long
	 *            movementId
	 * @return int
	 */
	public int checkLocked(String hostId, Long movementId) throws SwtException;

	/**
	 * Save/Update the movement details/Delete the match details from p_match
	 * table
	 * 
	 * @param Collection
	 *            addColl
	 * @param Collection
	 *            updateColl
	 * @param Collection
	 *            deleteColl
	 * @return void
	 */
	public void doDatabaseOperation(Collection addColl, Collection updateColl,
			Collection deleteColl) throws SwtException;

	/**
	 * Gets the Match Notes details from p_match_note table
	 * 
	 * @param String
	 *            entityId
	 * @param String
	 *            hostId
	 * @param String
	 *            matchId
	 * @return Collection object
	 */
	public Collection getMatchNotes(String entityId, String hostId,
			String matchId) throws SwtException;

	/**
	 * Gets the Match Notes details from p_match_note table
	 * 
	 * @param String
	 *            entityId
	 * @param String
	 *            hostId
	 * @param String
	 *            matchId
	 * @return boolean
	 */
	public boolean checkNotes(String entityId, String hostId, String matchId)
			throws SwtException;

	/**
	 * Gets the Position Level details from P_POSITION_LEVEL_NAME table
	 * 
	 * @param String
	 *            hostId
	 * @param String
	 *            entityId
	 * @param Integer
	 *            positionLevelId
	 * @return Collection object
	 */
	public Collection getPositionLevelRecord(String hostId, String entityId,
			Integer positionLevelId) throws SwtException;

	/**
	 * Gets the No.Of Notes from P_MOVEMENT_NOTE table
	 * 
	 * @param String
	 *            entityId
	 * @param String
	 *            hostId
	 * @param String
	 *            movementIdAsString
	 * @return Integer
	 */
	public Integer getNoOfNotes(String entityId, String hostId,
			String movementIdAsString) throws SwtException;

	/**
	 * 
	 * @param hostId
	 * @param entityId
	 * @param currCode
	 * @param status
	 * @param quality
	 * @param matchDate
	 * @param day
	 * @param applyCurrencyThreshold
	 * @param noIncludedMovementMatches
	 * @return
	 * @throws SwtException
	 */
	public List<Long> getLatestMatchIdAsList(String hostId, String entityId,
			String currCode, String status, String quality, Date matchDate,
			String day, String applyCurrencyThreshold,
			String noIncludedMovementMatches) throws SwtException;

	// Added for Mantis 1443
	/**
	 * 
	 * @param hostId
	 * @param entityId
	 * @param currencyCode
	 * @param scenarioId
	 * @param applyCurrencyThreshold
	 * @return
	 * @throws SwtException
	 */
	
	public List<Long> getMatchIdsPerScenarioAsList (String hostId, String  entityId,
			String  currencyCode,String  scenarioId,String  applyCurrencyThreshold,String currencyGroup)throws SwtException;
	
	/**
	 * This method is used to get all the position levels from
	 * P_POSITION_LEVEL_NAME table.<br>
	 * 
	 * @param hostId
	 * @param entityId
	 * @return Collection of all position levels.
	 * @throws SwtException
	 */
	public List<EntityPositionLevel> getAllPositionLevels(String hostId,
			String entityId) throws SwtException;

	/**
	 * This method is used to get the account access status for the given match
	 * Id.<br>
	 * 
	 * @param roleId
	 * @param entityId
	 * @param matchId
	 * @throws SwtException
	 * @return Account Access Status
	 */
	public String getAccountAccessStatus(String roleId, String entityId,

	/*
	 * Start:Code Modified for Mantis 1270 by Chinniah on 29-Feb-2012:Data
	 * Archive setup: Remove redundant fields from Archive setup screen
	 */			String matchId) throws SwtException;

	/**
	 * This method is used to get the archived movement from archive schema
	 * 
	 * @param entityId
	 * @param hostId
	 * @param matchId
	 * @param archiveId
	 * @return Collection
	 * @throws SwtException
	 */
	public Collection<Movement> getArchiveMovementDetailList(String entityId,
			String hostId, String matchId, String archiveId)
			throws SwtException;
	/*
	 * End:Code Modified for Mantis 1270 by Chinniah on 29-Feb-2012:Data
	 * Archive setup: Remove redundant fields from Archive setup screen
	 */
	
	// Added by Meftah for mantis 2156
	/**
	 * Gets the movement details from p_movement table by matchId
	 * This method implementation is temporary for Mantis 2156 (1054.2 & 1055) 
	 * @param String
	 *            entityId
	 * @param String
	 *            hostId
	 * @param String
	 *            movementIds
	 * @return Collection object
	 * @throws SwtException
	 */
	public Collection getMovementDetailsByMatch(String entityId, String hostId,
			Long matchId) throws SwtException;
	/**
	 * Gets the movement details from p_movement table by matchId
	 * This method implementation is temporary for Mantis 2156 (1054.2 & 1055) 
	 * @param String
	 *            entityId
	 * @param String
	 *            hostId
	 * @param String
	 *            movementIds
	 * @return Collection object
	 * @throws SwtException
	 */
	public String getMatchHash(String entityId, String hostId,
			Long matchId, String archiveId) throws SwtException;
	/**
	 * Remove Movement from a particular match
	 * @param hostId
	 * @param entityId
	 * @param listMovIds
	 * @param matchStatus
	 * @param matchId
	 * @param userId
	 * @throws SwtException
	 */
	public void removeMovementFromMatch(String hostId, String entityId, String listMovIds, String matchStatus,
			String matchId, String userId) throws SwtException;

	/**
	 * Add movement movements to a match
	 * @param entityId
	 * @param listMovIds
	 * @param matchStatus
	 * @param matchId
	 * @param userId
	 * @throws SwtException
	 */
	public void addMovementToMatch(String entityId, String listMovIds, String matchStatus, String matchId,
			String userId) throws SwtException;

	/**
	 * Delete a match
	 * @param hostId
	 * @param entityId
	 * @param matchId
	 * @param userId
	 * @throws SwtException
	 */
	public void deleteMatch(String hostId, String entityId, String matchId, String userId) throws SwtException;

	/**
	 * Reconcile a match
	 * @param hostId
	 * @param entityId
	 * @param matchId
	 * @param userId
	 * @throws SwtException
	 */
	public void reconcileMatch(String hostId, String entityId, String matchId, String userId) throws SwtException;
	/**
	 * Suspend a match
	 * @param hostId
	 * @param entityId
	 * @param matchId
	 * @param userId
	 * @throws SwtException
	 */
	public void suspendMatch(String hostId, String entityId, String matchId, String userId) throws SwtException;
	/**
	 * Confirm a match
	 * @param hostId
	 * @param entityId
	 * @param matchId
	 * @param userId
	 * @throws SwtException
	 */
	public void confirmMatch(String hostId, String entityId, String matchId, String userId) throws SwtException;
	/**
	 * Get match movement count
	 * @param hostId
	 * @param entityId
	 * @param matchId
	 * @return
	 * @throws SwtException
	 */
	public Integer getMatchCount(String hostId, String entityId, String matchId) throws SwtException;
	
}
