/*
 * Created on Jan 6, 2006
 *
 * TODO To change the template for this generated file go to
 * Window - Preferences - Java - Code Style - Code Templates
 */
package org.swallow.work.dao;

import org.swallow.dao.DAO;

import org.swallow.exception.SwtException;

import org.swallow.util.SystemFormats;

import org.swallow.work.model.SweepAlert;

import java.util.Collection;

/**
 * <AUTHOR>
 * 
 * TODO To change the template for this generated type comment go to Window -
 * Preferences - Java - Code Style - Code Templates
 */
public interface SweepAlertDAO extends DAO {
	public Collection checkSweepAlertMessages(String hostId, String roleId,
			SystemFormats formats) throws SwtException;

	/**
	 * Retreives the MovementAlert for given movementId and alertStage.
	 * 
	 * @param hostId
	 * @param entityId
	 * @param movementId
	 * @param alertStage
	 * @param status
	 * @return
	 * @throws SwtException
	 */
	public Collection getMovementAlert(String hostId, String entityId,
			Long movementId, String alertStage, String status)
			throws SwtException;

	/**
	 * Method use to create a new MovementAlert for a given movementId and
	 * stage.
	 * 
	 * @param sweepAlert
	 * @throws SwtException
	 */
	public void createMovementAlert(SweepAlert sweepAlert) throws SwtException;

	/**
	 * Method is used to update movement alert with given stage.
	 * 
	 * @param sweepAlert
	 * @throws SwtException
	 */
	public void updateMovementAlert(SweepAlert sweepAlert) throws SwtException;

	/**
	 * @param sweepId
	 * @return
	 */
	public String getSweepCurrency(Long sweepId) throws SwtException;

	/*
	 * START : Code Added for providing service used in defect number 223 on
	 * 25/04/2007
	 */

	/**
	 * This method deletes the given MovementAlert object from the database.
	 * 
	 * @param SweepAlert
	 * @throws SwtException
	 */
	public void deleteMovementAlert(SweepAlert movementAlert)
			throws SwtException;

	/*
	 * END : Code Added for providing service used in defect number 223 on
	 * 25/04/2007
	 */
	// Method added for Mantis 1197 by Sutheendran Balaji .A on 18-Aug-2010
	/**
	 * Fetches the url of the screens to be opened for each alert stage.
	 * 
	 * @param sweepAlert
	 * @return String
	 * @throws SwtException
	 */
	public String getMenuAccessId(SweepAlert sweepAlert) throws SwtException;
}
