
/*
 * @(#)InputExceptionsDataDAO.java 1.0 23/08/06
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.work.dao;

import java.util.List;
import org.swallow.exception.SwtException;
import org.swallow.util.OpTimer;

public interface InputExceptionsDataDAO {
	/**
	 * Method to get count of  input exceptions from data base
	 * @param fromDate
	 * @param toDate
	 * @param opTimer
	 * @param dateFormat
	 * @param fromPCM
	 * @return List
	 * @throws SwtException
	 */
	public List getInterfaceMessageList (String fromDate, String toDate, OpTimer opTimer, String dateFormat, boolean fromPCM)throws SwtException;
}