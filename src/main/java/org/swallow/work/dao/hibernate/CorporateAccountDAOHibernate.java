/*
 * @(#)CentralBankMonitorDAOHibernate.java 1.0 12/07/10
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */

package org.swallow.work.dao.hibernate;

import java.util.List;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.util.JDBCCloser;
import org.swallow.util.SwtInterceptor;
import org.swallow.util.SwtUtil;
import org.swallow.work.dao.CorporateAccountDAO;
import org.swallow.work.model.CorporateAccount;
import org.springframework.context.annotation.Lazy;
import org.springframework.orm.hibernate5.support.HibernateDaoSupport;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.stereotype.Repository;
import org.hibernate.SessionFactory;
import org.hibernate.Transaction;
import org.hibernate.Session;




/**
 * CorporateAccountDAOHibernate.java
 * 
 * Class that implements the CorporateAccountDAO and acts as DAO layer for all
 * database operations related to CorporateAccount
 * 
 * <AUTHOR> Balaji A
 * @date July 12, 2010
 */
@Repository ("corporateAccountDAO")
@Transactional
public class CorporateAccountDAOHibernate extends HibernateDaoSupport implements
		CorporateAccountDAO {
	public CorporateAccountDAOHibernate(@Lazy SessionFactory sessionfactory) {
	    setSessionFactory(sessionfactory);
	}

	/**
	 * Instance Of Log
	 */
	private static final Log log = LogFactory
			.getLog(CorporateAccountDAOHibernate.class);

	/**
	 * Method to get the corporate account details based on the value date
	 * passed
	 * 
	 * @param corporateAccount
	 * @return List<CorporateAccount>
	 * @throws SwtException
	 */
	@SuppressWarnings("unchecked")
	public List<CorporateAccount> getCorporateAccount(
			CorporateAccount corporateAccount) throws SwtException {
		log.debug(this.getClass().getName() + " - "
				+ "Entering into getCorporateAccount method");
		try {
			// Get the options for the specified hostId, userId and screenId
			/* start code for mantis 1256-entityid,valuedate removed from primary key by sami on 28-Sep-2010 */
			java.util.List corporateAccountColl = getHibernateTemplate()
					.find(
							"from CorporateAccount ca where ca.entityId=?0 and ca.valueDate=?1 ",
							new Object[] { corporateAccount.getEntityId(),corporateAccount.getValueDate()});
			
			/* End code for mantis 1256-entityid,valuedate removed from primary key by sami on 28-Sep-2010 */
			
			// Flush
			getHibernateTemplate().flush();
			log.debug(this.getClass().getName() + " - "
					+ "Leaving into getCorporateAccount method");
			return corporateAccountColl;
		} catch (Exception e) {
			log
					.error("An exception accured in "
							+ "CorporateAccountDAOHibernate : getCorporateAccount "
							+ e.getMessage());
			throw new SwtException(e.getMessage());
		}
		
	}

	/**
	 * Method to create or update the corporate account details for a value date
	 * 
	 * @param corporateAccount
	 * @param saveFlag
	 * @throws SwtException
	 */
	public void saveCorporateAccount(CorporateAccount corporateAccount,String saveFlag)
			throws SwtException {
		log.debug(this.getClass().getName() + " - "
				+ "Entering into saveCorporateAccount method");
		try {
			if(saveFlag != null && saveFlag.equalsIgnoreCase("true")){
				getHibernateTemplate().save(corporateAccount);
			}else{
				getHibernateTemplate().update(corporateAccount);
			}
			getHibernateTemplate().flush();
		} catch (Exception e) {
			log
					.error(this.getClass().getName()
							+ " - [saveCorporateAccount] - Exception - "
							+ e.getMessage());
			throw SwtErrorHandler.getInstance().handleException(
					e, "saveCorporateAccount",
					CorporateAccountDAOHibernate.class);
		}
		log.debug(this.getClass().getName() + " - "
				+ "Leaving into saveCorporateAccount method");
	}

	/**
	 * Method to delete the corporate account details for a value date
	 * 
	 * @param corporateAccount
	 * @throws SwtException
	 */
	public void deleteCorporateAccount(CorporateAccount corporateAccount)
			throws SwtException {
		Session session = null;
		Transaction tx = null;
		SwtInterceptor interceptor = null;
		try {
			log.debug(this.getClass().getName() + " - "
					+ "Entering into deleteCorporateAccount method");
			interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");
			session = getHibernateTemplate().getSessionFactory().withOptions().interceptor(interceptor).openSession();
			tx = session.beginTransaction();
			session.delete(corporateAccount);
			tx.commit();
		} catch (Exception e) {
			log
					.error(this.getClass().getName()
							+ " - [deleteCorporateAccount] - Exception - "
							+ e.getMessage());
			throw SwtErrorHandler.getInstance().handleException(
					e, "deleteCorporateAccount",
					CorporateAccountDAOHibernate.class);
		} finally {
			JDBCCloser.close(session);
		}
		log.debug(this.getClass().getName() + " - "
				+ "Leaving into deleteCorporateAccount method");
	}
}