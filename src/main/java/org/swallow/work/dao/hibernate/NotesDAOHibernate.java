/*
 * @(#)NotesDAOHibernate.java 1.0 27/01/06
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.work.dao.hibernate;

import java.sql.CallableStatement;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;






import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.exception.SwtRecordNotExist;
import org.swallow.util.CacheManager;
import org.swallow.util.JDBCCloser;
import org.swallow.util.SwtUtil;
import org.swallow.work.dao.NotesDAO;
import org.swallow.work.model.MatchNote;
import org.swallow.work.model.MovementNote;
import org.swallow.work.model.SweepNote;
import org.springframework.context.annotation.Lazy;
import org.springframework.orm.hibernate5.support.HibernateDaoSupport;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.stereotype.Repository;
import org.springframework.beans.factory.annotation.Autowired;
import org.hibernate.HibernateException;
import org.hibernate.SessionFactory;
import org.hibernate.Transaction;
import org.hibernate.Session;

/**
 * <AUTHOR>
 * 
 * This class is a DAO class to get notes details and also add/view/delete notes
 * 
 */
@Repository ("notesDAO")
@Transactional
public class NotesDAOHibernate extends HibernateDaoSupport implements NotesDAO {
	public NotesDAOHibernate(@Lazy SessionFactory sessionfactory) {
	    setSessionFactory(sessionfactory);
	}

	private static final String HQL_NOTELIST = "from MovementNote m where m.id.hostId=?0 and m.id.movementId=?1";
	private static final String HQL_MATCHNOTELIST = "from MatchNote m where m.id.hostId=?0 and m.id.matchId=?1";
	private static final String HQL_SWEEPNOTELIST = "from SweepNote m where m.id.hostId=?0 and m.id.sweepId=?1";

	private static final Log log = LogFactory.getLog(NotesDAOHibernate.class);

	public Collection getNoteDetails(String hostId, Long movementId)
			throws SwtException {
		log.debug("Entering into  getNoteDetails() method");

		List noteList = null;
		noteList = (List ) getHibernateTemplate().find(HQL_NOTELIST,
				new Object[] { hostId, movementId });
		log.debug("Inside getNoteDetails noteList is--->" + noteList);
		log.debug("Exiting from  getNoteDetails() method");

		return noteList;
	}

	public void saveNotesDetails(MovementNote movementNote) throws SwtException {
		log.debug("Entering saveNotesDetails");
		// Holds the hibernate session instance
		Session session = null;
		// Holds the hibernate Transaction instance
		Transaction transaction = null;
		session = getHibernateTemplate()
				.getSessionFactory()
				.withOptions().openSession();
		transaction = session.beginTransaction();
			session.save(movementNote);
		transaction.commit();
		session.close();
		log.debug("Exiting saveNotesDetails");
	}

	public void deleteNote(MovementNote movementNote) throws SwtException {
		log.debug("Entering deleteNote");

		String deleteHQL = "DELETE FROM MovementNote m " +
		        "WHERE m.id.hostId = :hostId " +
		        "AND m.id.entityId = :entityId " +
		        "AND m.id.movementId = :movementId " +
		        "AND m.id.updateDate = :updateDate";

		int deletedRecords;
		Session session = null;
		try {
		    log.debug("Before delete Query");
		    session = getHibernateTemplate().getSessionFactory().openSession();
		    deletedRecords = session.createQuery(deleteHQL)
		            .setParameter("hostId", movementNote.getId().getHostId())
		            .setParameter("entityId", movementNote.getId().getEntityId())
		            .setParameter("movementId", movementNote.getId().getMovementId())
		            .setParameter("updateDate", movementNote.getId().getUpdateDate())
		            .executeUpdate();

		    log.debug("Records deleted :::" + deletedRecords + "::::");

		    if (deletedRecords == 0) {
		        log.debug("Throw SwtRecordNotExist- ");
		        throw new SwtRecordNotExist();
		    }
		} catch (Exception e) {
		    // Handle the exception appropriately
		    log.error("Exception during delete operation: " + e.getMessage(), e);
		} finally {
			JDBCCloser.close(session);
		}

		// getHibernateTemplate().delete(movementNote);
		getHibernateTemplate().flush();
		log.debug("Exiting deleteNote");
	}

	// Functions for matchNotes
	public Collection getMatchNoteDetails(String hostId, Long matchId)
			throws SwtException {
		log.debug("Entering into  getMatchNoteDetails() method");

		List noteList = null;
		noteList = (List ) getHibernateTemplate().find(HQL_MATCHNOTELIST,
				new Object[] { hostId, matchId });
		log.debug("Inside getMatchNoteDetails noteList is--->" + noteList);
		log.debug("Exiting from  getMatchNoteDetails() method");

		return noteList;
	}

	public void saveMatchNotesDetails(MatchNote matchNote) throws SwtException {
		log.debug("Entering saveMatchNotesDetails");
		// Holds the hibernate session instance
		Session session = null;
		// Holds the hibernate Transaction instance
		Transaction transaction = null;
		session = getHibernateTemplate()
				.getSessionFactory()
				.withOptions().openSession();
		transaction = session.beginTransaction();
		session.save(matchNote);
		transaction.commit();
		session.close();
		log.debug("Exiting saveMatchNotesDetails");
	}

	public void deleteMatchNote(MatchNote matchNote) throws SwtException {
		log.debug("Entering deleteMatchNote");

		String deleteHQL = "DELETE FROM MatchNote m " +
		        "WHERE m.id.hostId = :hostId " +
		        "AND m.id.entityId = :entityId " +
		        "AND m.id.matchId = :matchId " +
		        "AND m.id.updateDate = :updateDate";
		Session session = null;
		int deletedRecords;
		try {
			log.debug("Before delete Query");
			session = getHibernateTemplate().getSessionFactory().openSession();
			deletedRecords = session.createQuery(deleteHQL)
					.setParameter("hostId", matchNote.getId().getHostId())
					.setParameter("entityId", matchNote.getId().getEntityId())
					.setParameter("matchId", matchNote.getId().getMatchId())
					.setParameter("updateDate", matchNote.getId().getUpdateDate())
					.executeUpdate();

			log.debug("Records deleted :::" + deletedRecords + "::::");

			if (deletedRecords == 0) {
				log.debug("Throw SwtRecordNotExist- ");
				throw new SwtRecordNotExist();
			}


		// getHibernateTemplate().delete(matchNote);
		getHibernateTemplate().flush();
		log.debug("Exiting deleteMatchNote");
		}catch(Exception e){
			log.error("Error in " + this.getClass().getName()
					+ " - [deleteMatchNote] - " + e.getMessage());

			throw SwtErrorHandler.getInstance().handleException(e,
					"deleteMatchNote", NotesDAOHibernate.class);
		}finally {
			JDBCCloser.close(session);
		}
	}

	// Functions for SweepNotes
	public Collection getSweepNoteDetails(String hostId, Long sweepId, String archiveId)
			throws SwtException {
		log.debug("Entering into  getSweepNoteDetails() method");

		List noteList = null;
		if(SwtUtil.isEmptyOrNull(archiveId)) {
			noteList = (List ) getHibernateTemplate().find(HQL_SWEEPNOTELIST,
					new Object[] { hostId, sweepId });
		}else {
			Session session = null;
			// declared to get connection
			Connection connection = null;
			// declared to execute procedure
			PreparedStatement statement = null;
			// declared to hold the db_link
			String dbLink = null;
			// declared to create statement
			// result set to get db_link
			ResultSet resultset = null;
			// result set to get movement notes
			ResultSet resultsetNotes = null;
			// Movement note object
			SweepNote sweepNote = null;
			// collection to hold the movement note details
			try {
				log.debug(this.getClass().getName()
						+ "+ [getArchiveNoteDetails] Enter");
				// open hibernate connection
				session = getHibernateTemplate().getSessionFactory().openSession();
				// Get the database connection object
				connection = SwtUtil.connection(session);
				// create statement
				String  query = "select * from P_SWEEP_NOTE@"+archiveId +" where sweep_id = ?";
				session = getHibernateTemplate().getSessionFactory().openSession();
				connection = SwtUtil.connection(session);
				statement = connection.prepareStatement(query);
				statement.setLong(1,sweepId);
				
				statement.execute();
				resultsetNotes = statement.getResultSet();
				noteList = new ArrayList<SweepNote>();
				if (resultsetNotes != null) {
					while (resultsetNotes.next()) {
						sweepNote = new SweepNote();
						sweepNote.setNoteText(resultsetNotes.getString("NOTE_TEXT"));
						sweepNote.getId().setUpdateDate(
								resultsetNotes.getTimestamp("UPDATE_DATE"));
						sweepNote.setUpdateUser(resultsetNotes.getString("UPDATE_USER"));
						sweepNote.getId().setSweepId(resultsetNotes.getLong("SWEEP_ID"));
						sweepNote.getId().setHostId(resultsetNotes.getString("HOST_ID"));
						// add movement object in collection
						noteList.add(sweepNote);

					}
				}

			} catch (Exception e) {
				log.error("Error in " + this.getClass().getName()
						+ " - [getSweepNoteDetails] - " + e.getMessage());

				throw SwtErrorHandler.getInstance().handleException(e,
						"getSweepNoteDetails", NotesDAOHibernate.class);

			} finally {
				// nullify
				// Declare SwtException instance
				SwtException thrownException = null;
				SQLException sqlException = null;
				HibernateException hException = null;
				try {
					if (connection != null)
						connection.commit();
				} catch (Exception exception) {
					log.error(this.getClass().getName()
							+ " - [getArchiveNoteDetails] - SQLException: "
							+ exception.getMessage());

					thrownException = SwtErrorHandler.getInstance()
							.handleException(exception, "getSweepNoteDetails",
									MovementDAOHibernate.class);
				}

				sqlException = JDBCCloser.close(resultset, resultsetNotes);
				if (thrownException == null && sqlException!=null)
					thrownException = SwtErrorHandler.getInstance().handleException(sqlException, "getSweepNoteDetails",	MovementDAOHibernate.class);
				
				sqlException = JDBCCloser.close(statement, statement);
				if (thrownException == null && sqlException!=null)
					thrownException = SwtErrorHandler.getInstance().handleException(sqlException, "getSweepNoteDetails",	MovementDAOHibernate.class);
				
				hException = JDBCCloser.close(session);
				if (thrownException == null && sqlException!=null)
					thrownException = SwtErrorHandler.getInstance().handleException(hException, "getSweepNoteDetails",	MovementDAOHibernate.class);

				JDBCCloser.close(connection);
				
				if (thrownException != null)
					throw thrownException;
				
				
				
				dbLink = null;
				log.debug(this.getClass().getName()
						+ "+ [getArchiveNoteDetails] Exit");
				}
			}
		log.debug("Inside getSweepNoteDetails noteList is--->" + noteList);
		log.debug("Exiting from  getSweepNoteDetails() method");

		return noteList;
	}

	public void saveSweepNotesDetails(SweepNote sweepNote) throws SwtException {
		log.debug("Entering saveNotesDetails");
		// Holds the hibernate session instance
		Session session = null;
		// Holds the hibernate Transaction instance
		Transaction transaction = null;
		session = getHibernateTemplate()
				.getSessionFactory()
				.withOptions().openSession();
		transaction = session.beginTransaction();
		session.save(sweepNote);
		transaction.commit();
		session.close();
		log.debug("Exiting saveNotesDetails");
	}

	public void deleteSweepNote(SweepNote sweepNote) throws SwtException {
		String deleteHQL = "DELETE FROM SweepNote m " +
		        "WHERE m.id.hostId = :hostId " +
		        "AND m.id.sweepId = :sweepId " +
		        "AND m.id.updateDate = :updateDate";


		    log.debug("Before delete Query");
		    Session session = null;
		    int deletedRecords;

			try {


				session = getHibernateTemplate().getSessionFactory().openSession();

				deletedRecords = session.createQuery(deleteHQL)
						.setParameter("hostId", sweepNote.getId().getHostId())
						.setParameter("sweepId", sweepNote.getId().getSweepId())
						.setParameter("updateDate", sweepNote.getId().getUpdateDate())
						.executeUpdate();

				log.debug("Records deleted :::" + deletedRecords + "::::");

				if (deletedRecords == 0) {
					log.debug("Throw SwtRecordNotExist- ");
					throw new SwtRecordNotExist();
				}

				// getHibernateTemplate().delete(sweepNote);
				getHibernateTemplate().flush();
				log.debug("Exiting deleteSweepNote");
			}catch(Exception e){
				log.error("Error in " + this.getClass().getName()
						+ " - [deleteSweepNote] - " + e.getMessage());

				throw SwtErrorHandler.getInstance().handleException(e,
						"deleteSweepNote", NotesDAOHibernate.class);
			}finally {
				JDBCCloser.close(session);
			}
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see org.swallow.work.dao.NotesDAO#getMatchDetails(java.lang.String,
	 *      java.lang.String, java.lang.String)
	 */
	public boolean getMatchDetails(String hostId, String entityId,
			String matchId) throws SwtException {
		log.debug("Entering getMatchDetails method");

		boolean matchPresent = false;
		List matchDetails = getHibernateTemplate().find(
		        "from Match m where m.id.hostId=?0 and m.id.entityId=?1"
		                + " and m.id.matchId=?2",
				new Object[] { hostId, entityId, new Long(matchId) });
		log.debug("Matchdetails.size()===>" + matchDetails.size());

		if ((matchDetails != null) && (matchDetails.size() > 0)) {
			matchPresent = true;
		}

		log.debug("match Present flag==>" + matchPresent);

		return matchPresent;
	}

	/**
	 * This method is used to get the currency for selected Match/Movement ID
	 * 
	 * @param entityId
	 * @param movementId
	 * @param matchId
	 * @return
	 * @throws SwtException
	 */
	public String getCurrency(String entityId, Long movementId, Long matchId)
			throws SwtException {
		// list to get the currency from the table
		List currency = null;
		// variable to hold the currency id from the
		String currencyId = null;
		try {
			log.debug(this.getClass().getName() + "+ [getCurrency] Enter");
			// null check for Movement id
			if (movementId != null) {
			    // get the currency for Movement id
			    currency = (List ) getHibernateTemplate().find(
			                    "select m.currencyCode from Movement m where m.id.entityId=?0 and m.id.movementId=?1",
			                    new Object[]{entityId, movementId});
			}
			// null check for Match id
			if (matchId != null) {
			    // get the currency for Match id
			    currency = (List ) getHibernateTemplate().find(
			                    "select m.currencyCode from Match m where m.id.entityId=?0 and m.id.matchId=?1",
			                    new Object[]{entityId, matchId});
			}
			// get the currency from list
			if (currency != null && !currency.isEmpty()) {
				currencyId = (String) currency.get(0);
			}

		} catch (Exception e) {
			log.error("Error in " + this.getClass().getName()
					+ " - [getCurrency] - " + e.getMessage());

			throw SwtErrorHandler.getInstance().handleException(e,
					"getCurrency", NotesDAOHibernate.class);

		} finally {
			log.debug(this.getClass().getName() + "+ [getCurrency] Exit");
			currency = null;
		}
		return currencyId;
	}

	/*
	 * Start:Code Modified for Mantis 1270 by Chinniah on 29-Feb-2012:Data
	 * Archive setup: Remove redundant fields from Archive setup screen
	 */
	/**
	 * This method is used to get the Movement notes details
	 * 
	 * @param entityId
	 * @param movementId
	 * @param archiveId
	 * @return Collection
	 * @throws SwtException
	 */
	public Collection getArchiveNoteDetails(String entityId, Long movementId,
			String archiveId) throws SwtException {
		// session declared to open hibernate session
		Session session = null;
		// declared to get connection
		Connection connection = null;
		// declared to execute procedure
		CallableStatement callableStatement = null;
		// declared to hold the db_link
		String dbLink = null;
		// declared to create statement
		Statement statement = null;
		// result set to get db_link
		ResultSet resultset = null;
		// result set to get movement notes
		ResultSet resultsetNotes = null;
		// Movement note object
		MovementNote movementNote = null;
		// collection to hold the movement note details
		Collection<MovementNote> notesDetails = null;
		try {
			log.debug(this.getClass().getName()
					+ "+ [getArchiveNoteDetails] Enter");
			// open hibernate connection
			session = getHibernateTemplate().getSessionFactory().openSession();
			// Get the database connection object
			connection = SwtUtil.connection(session);
			// create statement
			statement = connection.createStatement();
			// execute query
			statement
					.executeQuery("select DBLINK_SCHEMA_NAME from p_Archive where archive_id='"
							+ archiveId + "'");
			// get the result set
			resultset = statement.getResultSet();
			// get db_link from result set
			if (resultset != null) {
				while (resultset.next()) {
					dbLink = resultset.getString(1);
				}
			}
			// query to call porcedure
			callableStatement = connection
					.prepareCall("{call PKG_ARCHIVE_DETAILS.SPSHOWARCHMOVEMENTNOTE(?,?,?,?,?)}");
			// set values for procedure
			callableStatement.setString(1, CacheManager.getInstance()
					.getHostId());
			callableStatement.setString(2, entityId);
			callableStatement.setLong(3, movementId);
			callableStatement.setString(4, dbLink);
			callableStatement.registerOutParameter(5,
					oracle.jdbc.OracleTypes.CURSOR);
			callableStatement.execute();
			// get result set
			resultsetNotes = (ResultSet) callableStatement.getObject(5);
			notesDetails = new ArrayList<MovementNote>();

			// get movement details
			if (resultsetNotes != null) {
				movementNote = new MovementNote();
				while (resultsetNotes.next()) {
					movementNote.setNoteText(resultsetNotes.getString(4));

					movementNote.getId().setUpdateDate(
							resultsetNotes.getTimestamp(5));
					movementNote.setUpdateUser(resultsetNotes.getString(6));
					// add movement object in collection
					notesDetails.add(movementNote);

				}
			}

		} catch (Exception e) {
			log.error("Error in " + this.getClass().getName()
					+ " - [getArchiveNoteDetails] - " + e.getMessage());

			throw SwtErrorHandler.getInstance().handleException(e,
					"getArchiveNoteDetails", NotesDAOHibernate.class);

		} finally {
			// nullify
			// Declare SwtException instance
			SwtException thrownException = null;
			SQLException sqlException = null;
			HibernateException hException = null;
			try {
				if (connection != null)
					connection.commit();
			} catch (Exception exception) {
				log.error(this.getClass().getName()
						+ " - [getArchiveNoteDetails] - SQLException: "
						+ exception.getMessage());

				thrownException = SwtErrorHandler.getInstance()
						.handleException(exception, "getArchiveNoteDetails",
								MovementDAOHibernate.class);
			}

			sqlException = JDBCCloser.close(resultset, resultsetNotes);
			if (thrownException == null && sqlException!=null)
				thrownException = SwtErrorHandler.getInstance().handleException(sqlException, "getArchiveNoteDetails",	MovementDAOHibernate.class);
			
			sqlException = JDBCCloser.close(statement, callableStatement);
			if (thrownException == null && sqlException!=null)
				thrownException = SwtErrorHandler.getInstance().handleException(sqlException, "getArchiveNoteDetails",	MovementDAOHibernate.class);
			
			hException = JDBCCloser.close(session);
			if (thrownException == null && sqlException!=null)
				thrownException = SwtErrorHandler.getInstance().handleException(hException, "getArchiveNoteDetails",	MovementDAOHibernate.class);

			JDBCCloser.close(connection);
			
			if (thrownException != null)
				throw thrownException;
			
			
			
			dbLink = null;
			log.debug(this.getClass().getName()
					+ "+ [getArchiveNoteDetails] Exit");
		}
		return notesDetails;
	}

	/**
	 * This method is used to get the Match notes details
	 * 
	 * @param hostId
	 * @param matchId
	 * @param archiveId
	 * @param entityId
	 * @return notesDetails
	 * @throws SwtException
	 */
	public Collection<MatchNote> getArchiveMatchNoteDetails(String hostId,
			Long matchId, String archiveId, String entityId)
			throws SwtException {
		// session declared to open hibernate session
		Session session = null;
		// declared to get connection
		Connection connection = null;
		// declared to execute procedure
		CallableStatement callableStatement = null;
		// declared to hold the db_link
		String dbLink = null;
		// declared to create statement
		Statement statement = null;
		// result set to get db_link
		ResultSet resultset = null;
		// result set to get movement notes
		ResultSet resultsetNotes = null;
		// Match note object
		MatchNote matchNote = null;
		// collection to hold the Match note details
		Collection<MatchNote> notesDetails = null;
		try {
			log.debug(this.getClass().getName()
					+ "+ [getArchiveMatchNoteDetails] Enter");
			matchNote = new MatchNote();
			notesDetails = new ArrayList<MatchNote>();
			// open hibernate connection
			session = getHibernateTemplate().getSessionFactory().openSession();
			// Get the database connection object
			connection = SwtUtil.connection(session);
			// create statement
			statement = connection.createStatement();
			// execute query
			statement
					.executeQuery("select DBLINK_SCHEMA_NAME from p_Archive where archive_id='"
							+ archiveId + "'");
			// get the result set
			resultset = statement.getResultSet();
			// get db_link from result set
			if (resultset != null) {
				while (resultset.next()) {
					dbLink = resultset.getString(1);
				}
			}
			// query to call porcedure
			callableStatement = connection
					.prepareCall("{call PKG_ARCHIVE_DETAILS.SPSHOWARCHMATCHNOTE(?,?,?,?,?)}");
			// set values for procedure
			callableStatement.setString(1, CacheManager.getInstance()
					.getHostId());
			callableStatement.setString(2, entityId);
			callableStatement.setLong(3, matchId);
			callableStatement.setString(4, dbLink);
			callableStatement.registerOutParameter(5,
					oracle.jdbc.OracleTypes.CURSOR);
			callableStatement.execute();
			// get result set
			resultsetNotes = (ResultSet) callableStatement.getObject(5);
			if (resultsetNotes != null) {
				while (resultsetNotes.next()) {
					matchNote.setNoteText(resultsetNotes.getString(4));
					matchNote.getId().setUpdateDate(
							resultsetNotes.getTimestamp(5));
					matchNote.setUpdateUser(resultsetNotes.getString(6));
					notesDetails.add(matchNote);
				}
			}

			// add movement object in collection

		} catch (Exception e) {
			log.error("Error in " + this.getClass().getName()
					+ " - [getArchiveMatchNoteDetails] - " + e.getMessage());

			throw SwtErrorHandler.getInstance().handleException(e,
					"getArchiveMatchNoteDetails", NotesDAOHibernate.class);

		} finally {
			// nullify
			// Declare SwtException instance
			SwtException thrownException = null;
			SQLException sqlException = null;
			HibernateException hException = null;
			try {
				if (connection != null)
					connection.commit();
			} catch (Exception exception) {
				log.error(this.getClass().getName()
						+ " - [getArchiveMatchNoteDetails] - SQLException: "
						+ exception.getMessage());

				thrownException = SwtErrorHandler.getInstance()
						.handleException(exception,
								"getArchiveMatchNoteDetails",
								MovementDAOHibernate.class);
			}
			
			sqlException = JDBCCloser.close(resultset, resultsetNotes);
			if (thrownException == null && sqlException!=null)
				thrownException = SwtErrorHandler.getInstance().handleException(sqlException, "getArchiveMatchNoteDetails", MovementDAOHibernate.class);
			
			sqlException = JDBCCloser.close(statement, callableStatement);
			if (thrownException == null && sqlException!=null)
				thrownException = SwtErrorHandler.getInstance().handleException(sqlException, "getArchiveMatchNoteDetails", MovementDAOHibernate.class);
			
			hException = JDBCCloser.close(session);
			if (thrownException == null && sqlException!=null)
				thrownException = SwtErrorHandler.getInstance().handleException(hException, "getArchiveMatchNoteDetails", MovementDAOHibernate.class);

			JDBCCloser.close(connection);
			if (thrownException != null)
				throw thrownException;
			
			log.debug(this.getClass().getName()
					+ "+ [getArchiveMatchNoteDetails] Exit");
		}
		return notesDetails;
	}
	/*
	 * End:Code Modified for Mantis 1270 by Chinniah on 29-Feb-2012:Data Archive
	 * setup: Remove redundant fields from Archive setup screen
	 */
}
