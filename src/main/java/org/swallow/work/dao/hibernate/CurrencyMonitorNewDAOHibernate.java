/*
 * @(#)CurrencyMonitorNewDAOHibernate.java 1.0 18/08/06
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.work.dao.hibernate;

import java.math.BigDecimal;
import java.sql.CallableStatement;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.dao.DataAccessResourceFailureException;

import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.util.CacheManager;
import org.swallow.util.JDBCCloser;
import org.swallow.util.OpTimer;
import org.swallow.util.SwtConstants;
import org.swallow.util.SwtUtil;
import org.swallow.util.SystemFormats;
import org.swallow.work.dao.CurrencyMonitorNewDAO;
import org.swallow.work.model.CurrencyRecordVO;
import org.swallow.work.web.form.BalanceDateTO;
import org.swallow.work.web.form.CurrencyRecord;
import org.springframework.context.annotation.Lazy;
import org.springframework.orm.hibernate5.support.HibernateDaoSupport;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.stereotype.Repository;
import org.springframework.beans.factory.annotation.Autowired;
import org.hibernate.HibernateException;
import org.hibernate.SessionFactory;
import org.hibernate.Transaction;
import org.hibernate.Session;




/**
 * <pre>
 * DAO layer for Currency Monitor screen. This class performs following
 * tasks 
 *  - Get currency based predicted balance
 * </pre>
 * 
 * <AUTHOR>
 * <AUTHOR> R / 12-Dec-2011
 */
@Repository ("currmonitorNewDAO")
@Transactional
public class CurrencyMonitorNewDAOHibernate extends HibernateDaoSupport
		implements CurrencyMonitorNewDAO {
	public CurrencyMonitorNewDAOHibernate(@Lazy SessionFactory sessionfactory) {
	    setSessionFactory(sessionfactory);
	}

	/**
	 * Instance Of Log
	 */
	private static final Log log = LogFactory
			.getLog(CurrencyMonitorNewDAOHibernate.class);

	/**
	 * <pre>
	 * This method gets currency based predicted/loro balances for the period
	 * specified (range of maximum of 14 value dates) by invoking the stored
	 * procedure PK_MONITORS.SP_CURRENCY_MONITOR_JOB
	 * 
	 * The stored procedures returns 2 cursors. One for predicted and loro
	 * balance and another one for total predicted balance
	 * 
	 * Predicted Balance Cursor: This cursor returns currency code, predicted
	 * balance, balance date, holiday flag and loro balance. So iterate through
	 * this resultset and get currency based records. Then add currency based
	 * record in a collection.
	 * 
	 * Total Balance Cursor: This cursor returns calculated total value in a
	 * reporting currency. This result set contains balance date, total
	 * predicted balance, total loro balance, holiday flag, reporting currency
	 * and muliplier status. Iterate through this result set and get totals data
	 * as well as header details
	 * </pre>
	 * 
	 * @param String
	 *            entityId
	 * @param String
	 *            currGrp
	 * @param String
	 *            userId
	 * @param String
	 *            roleId
	 * @param Date
	 *            startDate
	 * @param Date
	 *            endDate
	 * @param boolean
	 *            hideWeekends
	 * @param SystemFormats
	 *            systemFormat
	 * @param OpTimer
	 *            opTimer
	 * @return CurrencyRecordVO
	 * @throws SwtException
	 */
	public CurrencyRecordVO getAllBalancesUsingStoredProc(String entityId,
			String currGrp, String userId, String roleId, Date startDate,
			Date endDate, boolean hideWeekends, SystemFormats systemFormat,
			OpTimer opTimer) throws SwtException {
		// Hibernate session to execute stored procedure
		Session session = null;
		// Database connection
		Connection conn = null;
		// CallableStatement instance to execute stored procedure
		CallableStatement cstmt = null;
		// ResultSet to get predicted balance for a date range and loro balance
		ResultSet rsPredictedBalance = null;
		// ResultSet to get total balance and grid header details
		ResultSet rsTotal = null;
		// This object holds all details of currency monitor screen
		CurrencyRecordVO currencyVO = null;
		// Collection to hold currency based predicted balance and loro balance
		Collection<CurrencyRecord> colCurrencyBalance = null;
		// Collection to hold grid column header details
		Collection<BalanceDateTO> colHeaderDetails = null;
		// To hold predicted balance for a date range and loro balance for a
		// currency
		CurrencyRecord currencyRecord = null;
		// To hold total value of predicted and loro balances
		CurrencyRecord balanceTotal = null;
		// Predicted balance on a particular date for a currency
		BalanceDateTO predictedBalance = null;
		// To hold grid column header details
		BalanceDateTO headerDetails = null;
		// To hold total balance on a particular date
		BalanceDateTO totalBalance = null;
		// Temporary variable to hold balance value (predicted/loro
		// balance)
		BigDecimal balance = null;
		// String to hold currency Multiplier
		String currencyMultiplierFlag = null;
		// Parameter index
		int paramIndex;
		// Code Added for Mantis:1929 by chinniah on 16-May-2012:Hide weekend
		// causes problem in syncing data grid header with data in currency
		// monitor screen
		// ArrayList to hold the Date in Column header
		ArrayList<java.sql.Date> headerDate = null;
		try {
			// log debug message
			log.debug(this.getClass().getName()
					+ " - [getAllBalancesUsingStoredProc] - Entry");
			headerDate = new ArrayList<java.sql.Date>();
			// Initialize object to hold currency monitor screen details
			currencyVO = new CurrencyRecordVO();
			// Start job timer - Fetching data from DB
			opTimer.start("data-fetch");
			// Get the hibernate session
			session = getHibernateTemplate().getSessionFactory().openSession();
			// Get the database connection object
			conn = SwtUtil.connection(session);
			// Make a callable statement for executing the procedure
			cstmt = conn
					.prepareCall("{call PKG_CURRENCY_MONITOR.SP_CURRENCY_MONITOR_JOB(?,?,?,?,?,?,?,?,?,?)}");
			// Set the parameters for stored procedure
			paramIndex = 1;
			cstmt.setString(paramIndex++, CacheManager.getInstance()
					.getHostId());

			cstmt.setString(paramIndex++, entityId);

			cstmt.setString(paramIndex++, currGrp);

			cstmt.setDate(paramIndex++, SwtUtil.truncateDateTime(startDate));
			cstmt.setDate(paramIndex++, SwtUtil.truncateDateTime(endDate));

			cstmt.setString(paramIndex++, roleId);

			cstmt.setString(paramIndex++, userId);

			cstmt.setString(paramIndex++, SwtConstants.CURRENCY_MONITOR_ID);

			// Register the output parameters one for predicted/loro balances
			// and another one for header/totals details
			cstmt.registerOutParameter(paramIndex++,
					oracle.jdbc.OracleTypes.CURSOR);
			cstmt.registerOutParameter(paramIndex++,
					oracle.jdbc.OracleTypes.CURSOR);

			// Execute the callable statement
			cstmt.execute();

			// Get the result set of loro balance
			rsPredictedBalance = (ResultSet) cstmt.getObject(9);

			// Get the result set of predicted balance
			rsTotal = (ResultSet) cstmt.getObject(10);
			// stop job timer - Fetching data from DB
			opTimer.stop("data-fetch");

			// Initialize collection to hold grid column header details
			colHeaderDetails = new ArrayList<BalanceDateTO>();
			// Initialize collection to hold currency based predicted and loro
			// balances
			colCurrencyBalance = new ArrayList<CurrencyRecord>();
			// start job timer - read record from result set
			opTimer.start("data-build");

			// Iterate through totals result set and get totals value as well as
			// grid column header details
			while (rsTotal.next()) {

				// If hide weekend data is enabled, then hide the column. That
				// means skip weekend records
				if (!(hideWeekends && rsTotal.getString(4).equals(
						SwtConstants.HOLIDAY_FLAG))) {
					// Initialize to hold grid column header details
					headerDetails = new BalanceDateTO();
					// Code Added for Mantis:1929 by chinniah on
					// 16-May-2012:Hide weekend
					// causes problem in syncing data grid header with data in
					// currency
					// monitor screen
					headerDate.add(rsTotal.getDate(1));

					headerDetails.setBalanceDateAsString(formatDateShortForm(
							systemFormat, rsTotal.getDate(1)));

					headerDetails.setHoliday(rsTotal.getString(4));
					colHeaderDetails.add(headerDetails);

					// Get sum of predicted/loro balances
					if (balanceTotal == null) {
						// One time initialization as totals will be in one row

						// Initialize to hold total value of predicted and loro
						// balances
						balanceTotal = new CurrencyRecord();
						// Reporting currency
						balanceTotal.setCurrCode(rsTotal.getString(5));
						// Get currency multiplier flag
						currencyMultiplierFlag = rsTotal.getString(6);

						// Set sum of loro balances (formatted value)
						balance = rsTotal.getBigDecimal(3);
						balance = (balance == null) ? new BigDecimal(0)
								: balance;
						balanceTotal.setLoroBalanceAsString(formatBalance(
								systemFormat, balanceTotal.getCurrCode(),
								currencyMultiplierFlag, balance));
						// Negative flag of loro balance
						balanceTotal.setLoroBalanceNegative(balance
								.doubleValue() < 0);
					}
					// Initialize to hold sum of predicted balance on a
					// particular
					// date
					totalBalance = new BalanceDateTO();
					// Set properties and add them to totals collection
					totalBalance.setHoliday(rsTotal.getString(4));
					// Set sum of predicted balances (formatted value)
					balance = rsTotal.getBigDecimal(2);
					balance = (balance == null) ? new BigDecimal(0) : balance;
					totalBalance.setPredBalanceAsString(formatBalance(
							systemFormat, balanceTotal.getCurrCode(),
							currencyMultiplierFlag, balance));
					totalBalance
							.setPredBalanceNegative(balance.doubleValue() < 0);
					balanceTotal.addBalanceDateRecord(totalBalance);
				}

			}

			// Iterate through predicted balance result set and get loro and
			// predicted balances for a date range

			while (rsPredictedBalance.next()) {
				// This check ensures that records belongs to a particular
				// currency should be go under one CurrencyRecord object
				if (currencyRecord == null
						|| !currencyRecord.getCurrCode().equals(
								rsPredictedBalance.getString(1))) {
					// One time initialization per currency as all currency
					// related balance will be displayed in a single row

					// Initialize to hold loro balance and predicted balances
					// for a date range
					currencyRecord = new CurrencyRecord();
					// Set properties
					currencyRecord.setCurrCode(rsPredictedBalance.getString(1));
					// set scenario highlighting
					currencyRecord.setScenarioHighlighted(rsPredictedBalance.getString(6));
					// Set loro balance (formatted value)
					balance = rsPredictedBalance.getBigDecimal(5);

					balance = (balance == null) ? new BigDecimal(0) : balance;

					currencyRecord.setLoroBalanceAsString(formatBalance(
							systemFormat, currencyRecord.getCurrCode(),
							currencyMultiplierFlag, balance));
					// Set negative flag of loro balance
					currencyRecord
							.setLoroBalanceNegative(balance.doubleValue() < 0);
					// Add currency data to the collection
					colCurrencyBalance.add(currencyRecord);
				}

				// Code Modified for Mantis:1929 by chinniah on
				// 16-May-2012:Hide weekend
				// causes problem in syncing data grid header with data in
				// currency
				// monitor screen
				if (headerDate.contains(rsPredictedBalance.getDate(2))) {
					// Initialize predicted balance object on a particular date
					// for a currency
					predictedBalance = new BalanceDateTO();
					// Set properties of a predicted balance
					predictedBalance
							.setBalanceDateAsString(formatDateShortForm(
									systemFormat, rsPredictedBalance.getDate(2)));
					predictedBalance.setBalanceOrgDateAsString(SwtUtil
							.formatDate(rsPredictedBalance.getDate(2),
									systemFormat.getDateFormatValue()));
					predictedBalance
							.setHoliday(rsPredictedBalance.getString(4));

					// Set predicted balance of a particular date(formatted
					// value)
					balance = rsPredictedBalance.getBigDecimal(3);

					balance = (balance == null) ? new BigDecimal(0) : balance;

					predictedBalance.setPredBalanceAsString(formatBalance(
							systemFormat, currencyRecord.getCurrCode(),
							currencyMultiplierFlag, balance));

					predictedBalance.setPredBalanceNegative(balance
							.doubleValue() < 0);
					// Add a predicted balance record under a currency
					currencyRecord.addBalanceDateRecord(predictedBalance);
				}
			}
			// stop job timer - read record from result set
			opTimer.stop("data-build");

			// Add results to currency balance vo object
			currencyVO.setHeaderDetails(colHeaderDetails);
			currencyVO.setBalanceDetails(colCurrencyBalance);
			currencyVO.setBalanceTotal(balanceTotal);
			return currencyVO;
		} catch (DataAccessResourceFailureException ex) {
			ex.printStackTrace();
			// log error message
			log
					.error(this.getClass().getName()
							+ " - [getAllBalancesUsingStoredProc] - DataAccessResourceFailureException -"
							+ ex.getMessage());
			// Re-throw as SwtException
			throw SwtErrorHandler.getInstance().handleException(ex,
					"getAllBalancesUsingStoredProc",
					CurrencyMonitorNewDAOHibernate.class);
		} catch (IllegalStateException ex) {
			ex.printStackTrace();
			// log error message
			log
					.error(this.getClass().getName()
							+ " - [getAllBalancesUsingStoredProc] - IllegalStateException -"
							+ ex.getMessage());
			// Re-throw as SwtException
			throw SwtErrorHandler.getInstance().handleException(ex,
					"getAllBalancesUsingStoredProc",
					CurrencyMonitorNewDAOHibernate.class);
		} catch (HibernateException ex) {
			ex.printStackTrace();
			// log error message
			log
					.error(this.getClass().getName()
							+ " - [getAllBalancesUsingStoredProc] - HibernateException -"
							+ ex.getMessage());
			// Re-throw as SwtException
			throw SwtErrorHandler.getInstance().handleException(ex,
					"getAllBalancesUsingStoredProc",
					CurrencyMonitorNewDAOHibernate.class);
		} catch (SQLException ex) {
			ex.printStackTrace();
			// log error message
			log.error(this.getClass().getName()
					+ " - [getAllBalancesUsingStoredProc] - SQLException -"
					+ ex.getMessage());
			// Re-throw as SwtException
			throw SwtErrorHandler.getInstance().handleException(ex,
					"getAllBalancesUsingStoredProc",
					CurrencyMonitorNewDAOHibernate.class);
		} catch (SwtException ex) {
			ex.printStackTrace();
			// log error message
			log.error(this.getClass().getName()
					+ " - [getAllBalancesUsingStoredProc] - SwtException -"
					+ ex.getMessage());
			// Re-throw SwtException
			throw ex;
		} catch (Exception ex) {
			ex.printStackTrace();
			// log error message
			log.error(this.getClass().getName()
					+ " - [getAllBalancesUsingStoredProc] - Exception -"
					+ ex.getMessage());
			// Re-throw as SwtException
			throw SwtErrorHandler.getInstance().handleException(ex,
					"getAllBalancesUsingStoredProc",
					CurrencyMonitorNewDAOHibernate.class);
		} finally {

			//edited by KaisBS for mantis 1745 : introducing the implementation of the JDBCCloser class
			SwtException thrownException = null;
			SQLException sqlException = null;

			sqlException = JDBCCloser.close(rsTotal, rsPredictedBalance);
			if (sqlException!=null)
				thrownException = new SwtException(sqlException.getMessage());

			Object[] exceptions = JDBCCloser.close(null,cstmt, conn, session);

			if (thrownException == null && exceptions[0] != null)
				thrownException = new SwtException(((SQLException) exceptions[0]).getMessage());

			if (thrownException == null && exceptions[1] !=null) 
				thrownException = new SwtException(((HibernateException) exceptions[1]).getMessage());

			if (thrownException != null)
				throw thrownException;

			// nullify objects
			session = null;
			conn = null;
			cstmt = null;
			rsPredictedBalance = null;
			rsTotal = null;
			colCurrencyBalance = null;
			colHeaderDetails = null;
			currencyRecord = null;
			balanceTotal = null;
			predictedBalance = null;
			headerDetails = null;
			totalBalance = null;
			balance = null;
			currencyMultiplierFlag = null;
			// log debug message
			log.debug(this.getClass().getName()
					+ " - [getAllBalancesUsingStoredProc] - Exit");
		}
	}

	/**
	 * This method formats the date based on date format selected by user, and
	 * returns short form (date and month)
	 * 
	 * @param SystemFormats
	 *            systemFormats
	 * @param Date
	 *            value
	 * @return String - Date in short form
	 * @throws SwtException
	 */
	private String formatDateShortForm(SystemFormats systemFormats, Date value)
			throws SwtException {
		// String format of date
		String strValue = null;

		try {
			// log debug message
			log.debug(this.getClass().getName()
					+ " - [formatDateShortForm] - Entry");
			// Format the given date with user defined format
			strValue = SwtUtil.formatDate(value, systemFormats
					.getDateFormatValue());
			// Returns the short form of given date
			return strValue.substring(0, strValue.lastIndexOf('/'));
		} catch (Exception ex) {
			// log error message
			log.error(this.getClass().getName()
					+ " - [formatDateShortForm] - Exception -"
					+ ex.getMessage());
			// Re-throw as SwtException
			throw new SwtException(ex.getMessage());
		} finally {
			// nullify object(s)
			strValue = null;
			// log debug message
			log.debug(this.getClass().getName()
					+ " - [formatDateShortForm] - Exit");
		}
	}

	/**
	 * This method formats the given predicted/loro balance and returns the same
	 * 
	 * @param SystemFormats
	 *            systemFormats
	 * @param String
	 *            currCode
	 * @param String
	 *            currencyMultiplierFlag
	 * @param BigDecimal
	 *            balanceValue
	 * @return String - Formated value
	 * @throws SwtException
	 */
	private String formatBalance(SystemFormats systemFormats, String currCode,
			String currencyMultiplierFlag, BigDecimal balanceValue)
			throws SwtException {
		// Currency separator
		String separator = null;
		// Decimal part separator
		String decimalSeparator = null;
		// String value of balance
		String strBalance = null;
		// Index of decimal separator
		int indexDecimalSeparator;

		try {
			// log debug message
			log.debug(this.getClass().getName() + " - [formatBalance] - Entry");
			// If either multiplier or balance is null do not proceed further,
			// returns null value
			if (currencyMultiplierFlag == null || balanceValue == null) {
				return null;
			}
			if (currencyMultiplierFlag.equals(SwtConstants.YES)) {
				// Get separator and decimal separator based on user preference
				if (systemFormats.getCurrencyFormat().equals(
						SwtConstants.CURRENCY_PAT + "1")) {
					separator = ",";
					decimalSeparator = ".";
				} else {
					separator = ".";
					decimalSeparator = ",";
				}
				// Based on the currency format selected by user, format the
				// given balance and returns the same
				strBalance = new DecimalFormat("#,##0.0").format(balanceValue)
						.replace(decimalSeparator, separator);
				indexDecimalSeparator = strBalance.lastIndexOf(separator);
				return strBalance.substring(0, indexDecimalSeparator)
						+ decimalSeparator
						+ strBalance.substring(indexDecimalSeparator + 1);
			} else {
				return SwtUtil.formatCurrency(currCode.substring(0, 3),
						balanceValue);
			}
		} catch (Exception ex) {
			// log error message
			log.error(this.getClass().getName()
					+ " - [formatBalance] - Exception -" + ex.getMessage());
			// Re-throw as SwtException
			throw new SwtException(ex.getMessage());
		} finally {
			// nullify object(s)
			separator = null;
			decimalSeparator = null;
			strBalance = null;
			// log debug message
			log.debug(this.getClass().getName() + " - [formatBalance] - Exit");
		}
	}
}