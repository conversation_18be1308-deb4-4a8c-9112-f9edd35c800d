/*
 * @(#)MatchDisplayDAOHibernate.java 1.0 04/01/2006
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
 
package org.swallow.work.dao.hibernate;

import java.sql.Array;
import java.sql.CallableStatement;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.Iterator;
import java.util.List;

import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.Root;
import javax.sql.DataSource;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.hibernate.query.Query;
import org.springframework.dao.DataAccessException;

import org.swallow.control.model.AccountAccess;
import org.swallow.control.model.Archive;
import org.swallow.control.service.ArchiveManager;
import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.maintenance.model.EntityPositionLevel;
import org.swallow.maintenance.service.ILMGeneralMaintenanceManager;
import org.swallow.util.CacheManager;
import org.swallow.util.JDBCCloser;
import org.swallow.util.SwtConstants;
import org.swallow.util.SwtDataSource;
import org.swallow.util.SwtInterceptor;
import org.swallow.util.SwtUtil;
import org.swallow.work.dao.MatchDisplayDAO;
import org.swallow.work.model.Match;
import org.swallow.work.model.MatchNote;
import org.swallow.work.model.Movement;
import org.swallow.work.model.MovementLock;
import org.swallow.work.model.MovementNote;
import org.swallow.work.model.PrevMatch;




import org.springframework.context.annotation.Lazy;
import org.springframework.orm.hibernate5.support.HibernateDaoSupport;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.stereotype.Repository;
import org.springframework.beans.factory.annotation.Autowired;
import org.hibernate.HibernateException;
import org.hibernate.SessionFactory;
import org.hibernate.Transaction;
import org.hibernate.Session;
import org.hibernate.criterion.Expression;
/**
 * 
 * Class that implements the MatchDisplayDAO and acts as DAO layer for all
 * database operations<br>
 * 
 * Modified by Marshal<br>
 * Modified on 13-June-2012<br>
 * 
 */
@Repository ("matchDisplayDAO")
@Transactional
public class MatchDisplayDAOHibernate extends HibernateDaoSupport implements
		MatchDisplayDAO {
	public MatchDisplayDAOHibernate(@Lazy SessionFactory sessionfactory) {
	    setSessionFactory(sessionfactory);
	}

	/*
	 * Initializing Log variable for logging comments
	 */
	private final Log log = LogFactory.getLog(MatchDisplayDAOHibernate.class);

	/**
	 * This method gets the list of movements along with the total number of
	 * movements for the given match Id.<br>
	 * 
	 * @param String
	 *            entityId
	 * @param String
	 *            hostId
	 * @param String
	 *            matchId
	 * 
	 * @return Collection object
	 * @throws SwtException
	 */
	@SuppressWarnings("unchecked")
	public Collection<Movement> getMovementDetailList(String entityId, String hostId, String matchId) throws SwtException {
		String archDatabaseName = null;
		List<Movement> collMovementDetail = null;
		SwtDataSource dataSource = null;
		Movement movement = null;
		Session session = null;
		Connection connection = null;
		CallableStatement statement = null;
		ResultSet resultSet = null;
		CriteriaQuery<Movement> queryMatch = null;
		try {
			log.debug(this.getClass().getName() + "- [getMovementDetailList] - Entering ");
			dataSource = (SwtDataSource) SwtUtil.getBean("dataSourceDb");
			archDatabaseName = (String) dataSource.useDataSource.get();
			session = getHibernateTemplate().getSessionFactory().openSession();
			connection = SwtUtil.connection(session);
			if (archDatabaseName != null) {
				session = getHibernateTemplate().getSessionFactory().openSession();
				String queryString = "FROM Movement m WHERE m.id.hostId = :hostId" +
						(entityId != null && entityId.trim().length() > 0 ? " AND m.id.entityId = :entityId" : "") +
						(matchId != null ? " AND m.matchId = :matchId" : "");
				Query<Movement> query = session.createQuery(queryString);
				query.setParameter("hostId", hostId);
				if (entityId != null && entityId.trim().length() > 0) {
					query.setParameter("entityId", entityId);
				}
				if (matchId != null) {
					query.setParameter("matchId", matchId);
				}
				collMovementDetail = query.list();
			} else {
				if(log.isInfoEnabled())
					log.info("/**-----Very Important Note: PK_APPLICATION.SPGETMOVEMENTDETAIL is used. Ensure that all columns are correctly mapped in HIBERNATE Movement object-----**/");
				statement = connection.prepareCall("{call PK_APPLICATION.SPGETMOVEMENTDETAIL(?, ?, ?, ?)}");
				statement.setString(1, hostId);
				statement.setString(2, entityId);
				statement.setLong(3, Long.valueOf(matchId));
				statement.registerOutParameter(4, oracle.jdbc.OracleTypes.CURSOR);
				statement.execute();
				resultSet = (ResultSet) statement.getObject(4);
				if (resultSet != null) {
					// Instantiates the ArrayList
					collMovementDetail = new ArrayList();
					while (resultSet.next()) {
						// Instantiates the Movement class to get a new bean for
						// each loop
						movement = new Movement();
						//Set scenario highlighted
						movement.setScenarioHighlighted(resultSet.getString("SCENARIO_HIGHLIGHTING")); 
						movement.setMatchcenarioHighlighted(resultSet.getString("MATCH_SCENARIO_HIGHLIGHTING")); 
						movement.getId().setHostId(resultSet.getString("HOST_ID"));
						movement.getId().setEntityId(resultSet.getString("ENTITY_ID"));
						movement.getId().setMovementId(resultSet.getLong("MOVEMENT_ID"));
						movement.setCurrencyCode(resultSet.getString("CURRENCY_CODE"));
						movement.setBookCode(resultSet.getString("BOOKCODE"));
						movement.setValueDate(resultSet.getDate("VALUE_DATE"));
						movement.setAmount(resultSet.getDouble("AMOUNT"));
						movement.setSign(resultSet.getString("SIGN"));
						movement.setMovementType(resultSet.getString("MOVEMENT_TYPE"));
						movement.setAccountId(resultSet.getString("ACCOUNT_ID"));
						movement.setReference1(resultSet.getString("REFERENCE1"));
						movement.setReference2(resultSet.getString("REFERENCE2"));
						movement.setReference3(resultSet.getString("REFERENCE3"));
						movement.setReference4(resultSet.getString("REFERENCE4"));
						movement.setCounterPartyId(resultSet.getString("COUNTERPARTY_ID"));
						movement.setCounterPartyText1(resultSet.getString("COUNTERPARTY_TEXT1"));
						movement.setCounterPartyText2(resultSet.getString("COUNTERPARTY_TEXT2"));
						movement.setCounterPartyText3(resultSet.getString("COUNTERPARTY_TEXT3"));
						movement.setCounterPartyText4(resultSet.getString("COUNTERPARTY_TEXT4"));
						movement.setBeneficiaryId(resultSet.getString("BENEFICIARY_ID"));
						movement.setBeneficiaryText1(resultSet.getString("BENEFICIARY_TEXT1"));
						movement.setBeneficiaryText2(resultSet.getString("BENEFICIARY_TEXT2"));
						movement.setBeneficiaryText3(resultSet.getString("BENEFICIARY_TEXT3"));
						movement.setBeneficiaryText4(resultSet.getString("BENEFICIARY_TEXT4"));
						movement.setCustodianId(resultSet.getString("CUSTODIAN_ID"));
						movement.setCustodianText1(resultSet.getString("CUSTODIAN_TEXT1"));
						movement.setCustodianText2(resultSet.getString("CUSTODIAN_TEXT2"));
						movement.setCustodianText3(resultSet.getString("CUSTODIAN_TEXT3"));
						movement.setCustodianText4(resultSet.getString("CUSTODIAN_TEXT4"));
						movement.setBookCodeAvail(resultSet.getString("BOOKCODE_AVAIL"));
						movement.setPositionLevel(resultSet.getInt("POSITION_LEVEL"));
						movement.setPredictStatus(resultSet.getString("PREDICT_STATUS"));
						movement.setExtractStatus(resultSet.getString("EXTRACT_STATUS"));
						movement.setMatchId(resultSet.getLong("MATCH_ID"));
						movement.setMatchStatus(resultSet.getString("MATCH_STATUS"));
						movement.setUpdateDate(resultSet.getTimestamp("UPDATE_DATE"));
						movement.setUpdateUser(resultSet.getString("UPDATE_USER"));
						movement.setInputDate(resultSet.getTimestamp("INPUT_DATE"));
						movement.setInputSource(resultSet.getString("INPUT_SOURCE"));
						movement.setMessageId(resultSet.getString("MESSAGE_ID"));
						movement.setMessageFormat(resultSet.getString("MESSAGE_FORMAT"));
						movement.setInitialPredStatus(resultSet.getString("INITIAL_PREDICT_STATUS"));
						movement.setInputRole(resultSet.getString("INPUT_ROLE"));
						movement.setInputUser(resultSet.getString("INPUT_USER"));
						movement.setNotesCount(resultSet.getInt("NOTES_COUNT"));
						movement.setOpenFlag(resultSet.getString("OPEN"));
						movement.setExtBalStatus(resultSet.getString("EXT_BAL_STATUS"));
						movement.setToMatch(resultSet.getString("TO_MATCH"));
						movement.setMatchingParty(resultSet.getString("MATCHING_PARTY"));
						movement.setProductType(resultSet.getString("PRODUCT_TYPE"));
						movement.setPostingDate(resultSet.getTimestamp("POSTING_DATE"));
						movement.setNoOfNotesAttached(resultSet.getInt("NOTES_COUNT_MVT_NOTE"));
						movement.setSettlementDateTime(resultSet.getTimestamp("SETTLEMENT_DATETIME"));
						movement.setExpectedSettlementDateTime(resultSet.getTimestamp("EXPECTED_SETTLEMENT_DATETIME"));
						movement.setCriticalPaymentType(resultSet.getString("CRITICAL_PAYMENT_TYPE"));

						movement.setCounterPartyText5(resultSet.getString("COUNTERPARTY_TEXT5"));
						movement.setBeneficiaryText5(resultSet.getString("BENEFICIARY_TEXT5"));
						movement.setCustodianText5(resultSet.getString("CUSTODIAN_TEXT5"));
						movement.setOrderingCustomerId(resultSet.getString("ORDERING_CUSTOMER"));
						movement.setOrderingInstitutionId(resultSet.getString("ORDERING_INSTITUTION"));
						movement.setSenderCorrespondentId(resultSet.getString("SENDERS_CORRES"));
						movement.setReceiverCorrespondentId(resultSet.getString("RECEIVERS_CORRES"));
						movement.setIntermediaryInstitutionId(resultSet.getString("INTMDRY_INSTITUTION_ID"));
						movement.setAccountWithInstitutionId(resultSet.getString("ACC_WITH_INSTITUTION_ID"));
						movement.setBeneficiaryCustomerId(resultSet.getString("BENEFICIARY_CUST"));
						movement.setExtraText1(resultSet.getString("EXTRA_TEXT1"));
						movement.setIlmFcastStatus(resultSet.getString("ILM_FCAST_STATUS"));
						// Adds the movement bean to the list
						collMovementDetail.add(movement);
					}
				}
			}
			log.debug(this.getClass().getName()
					+ "- [getMovementDetailList] - Exiting ");
		} catch (Exception exception) {
			log.error("Exception caught at " + this.getClass().getName()
					+ " - [getMovementDetailList]. Cause: "
					+ exception.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exception,
					"getMovementDetailList", MatchDisplayDAOHibernate.class);
		} finally {
			// Cleaning unreferenced objects
			archDatabaseName = null;
			dataSource = null;
			movement = null;
			// Edited by KaisBS for mantis 1745 : introducing the implementation
			// of the JDBCCloser class
			SwtException thrownException = null;
			Object[] exceptions = JDBCCloser.close(resultSet, statement, connection,
					session);

			if (exceptions[0] != null)
				thrownException = SwtErrorHandler.getInstance()
						.handleException((SQLException) exceptions[0],
								"getMovementDetailList",
								MatchDisplayDAOHibernate.class);

			if (thrownException == null && exceptions[1] != null)
				thrownException = SwtErrorHandler.getInstance()
						.handleException((HibernateException) exceptions[1],
								"getMovementDetailList",
								MatchDisplayDAOHibernate.class);

			if (thrownException != null)
				throw thrownException;
		}
		return collMovementDetail;
	}

	/**
	 * This method is used to get the entityName for the given entityId from
	 * S_ENTITY table.<br>
	 * 
	 * @param String
	 *            entityId
	 * @param String
	 *            hostId
	 * @return String - entity name
	 * @throws SwtException
	 */
	@SuppressWarnings("unchecked")
	public String getEntityName(String entityId, String hostId)
			throws SwtException {
		// The entity name to be retrieved from DB
		String entityName = null;
		// Holds the list of entities
		List entityList = null;
		try {
			log.debug(this.getClass().getName()
					+ "- [getEntityName] - Entering ");

			// Gets the entityList
			entityList = (List ) getHibernateTemplate().find("select e.entityName from Entity e where e.id.entityId =?0 and e.id.hostId=?1 ",
							new Object[] { entityId, hostId });
			// Gets the entityName from the list
			entityName = entityList.get(0).toString();
			log.debug(this.getClass().getName()
					+ "- [getEntityName] - Exiting ");
		} catch (Exception exception) {
			log.error("Exception caught in " + this.getClass().getName()
					+ " - [getEntityName]. Cause: " + exception.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exception,
					"getEntityName", MatchDisplayDAOHibernate.class);
		} finally {
			// Cleaning unreferenced objects
			entityList = null;
		}
		return entityName;
	}

	/**
	 * Get the match details for the given matchId from p_match table
	 * 
	 * @param String
	 *            entityId
	 * @param String
	 *            hostId
	 * @param String
	 *            matchId
	 * @return Collection object
	 * @throws SwtException
	 */
	@SuppressWarnings("unchecked")
	public Collection getMatchDetails(String entityId, String hostId,
			String matchId) throws SwtException {
		// Holds the collection of match details
		Collection matchDetails = null;
		try {
			log.debug(this.getClass().getName()
					+ "- [getMatchDetails] - Entering ");
			// Gets the match details
			matchDetails = (Collection ) getHibernateTemplate().find(
					"from Match m where m.id.matchId= ?0"
							+ " and m.id.entityId=?1" + " and m.id.hostId=?2",
					new Object[] { new Long(matchId), entityId, hostId });
			log.debug(this.getClass().getName()
					+ "- [getMatchDetails] - Exiting ");
		} catch (Exception e) {
			log.error("Exception caught in " + this.getClass().getName()
					+ " - [getMatchDetails]. Cause: " + e.getMessage());
			throw SwtErrorHandler.getInstance().handleException(e,
					"getMatchDetails", MatchDisplayDAOHibernate.class);
		}
		return matchDetails;

	}

	/**
	 * This method is used to insert into Previous match.
	 * 
	 * @param PrevMatch
	 *            returns void
	 * @throws SwtException
	 */
	@SuppressWarnings("unchecked")
	public void insertPrevMatch(PrevMatch prevMatch) throws SwtException {
		// Holds the movement Id to be passed in where clause
		Long movementId = null;
		// Holds the previous match Id
		Long prevMatchedWith = null;
		// Holds the list of previous matches
		List records = null;
		try {
			log.debug(this.getClass().getName()
					+ " -[insertPrevMatch]- Entering");
			// Gets the movement id
			movementId = prevMatch.getId().getMovementId();
			// Gets the previous match id
			prevMatchedWith = prevMatch.getId().getPrevMatchedWith();
			// Gets the list of previous match details
			records = (List ) getHibernateTemplate().find(
							"from PrevMatch m where m.id.movementId=?0 and m.id.prevMatchedWith=?1",
							new Object[] { movementId, prevMatchedWith });
			// Validates the list to insert the Previous Match details
			if (records.size() == 0) {
				getHibernateTemplate().save(prevMatch);
				getHibernateTemplate().flush();
			}
			log.debug(this.getClass().getName()
					+ " -[insertPrevMatch]- Exiting");
		} catch (DataAccessException e) {
			log.error("DataAccessException caught in "
					+ this.getClass().getName()
					+ " - [insertPrevMatch]. Cause: " + e.getMessage());
			throw SwtErrorHandler.getInstance().handleException(e,
					"insertPrevMatch", MatchDisplayDAOHibernate.class);
		} catch (Exception e) {
			log.error("Exception caught in " + this.getClass().getName()
					+ " - [insertPrevMatch]. Cause: " + e.getMessage());
			throw SwtErrorHandler.getInstance().handleException(e,
					"insertPrevMatch", MatchDisplayDAOHibernate.class);
		} finally {
			// Cleaning unreferenced objects
			movementId = null;
			prevMatchedWith = null;
			records = null;
		}
	}

	/**
	 * This method is used to get the previous match details.
	 * 
	 * @param hostId
	 * @param entityId
	 * @param movementId
	 * @param matchFlag
	 *            returns Collection
	 * @throws SwtException
	 */
	@SuppressWarnings("unchecked")
	public Collection getPrevMatch(String hostId, String entityId,
			String movementId, boolean matchFlag) throws SwtException {
		// Counter value used in loop
		int count = 0;
		// Holds the first part of query if match flag is false
		String query1 = null;
		// Holds the first part of query if match flag is true
		String query2 = null;
		// Holds the first part of query if match flag is true
		String query3 = null;
		// Holds the previous match value to be combined
		String strCombine = null;
		// Holds the movement id
		Long tempMovementId = null;
		// Holds the collection of Previous match details
		Collection removeList = null;
		// Holds the temporary list of previous match
		List tempMvntList1 = null;
		// Holds the temporary list of previous match
		List tempMvntList2 = null;
		
		try {
			log.debug(this.getClass().getName() + " -[getPrevMatch]- Entering");
			// Instantiates the list
			removeList = new ArrayList();
			// Validates the match flag and frame the first query
			if (matchFlag == false) {
				/*
				 * To get match details to store into p_prev_match table
				 * Removing movement(s) from a match
				 */
				query1 = "FROM Movement M"
						+ " WHERE M.matchId = (SELECT B.matchId  FROM Movement B   WHERE B.id.hostId = :hostId"
						+ "  AND B.id.entityId = :entityId"
						+ " AND B.id.movementId = :movementId)"
						+ " AND "
						+ " :movementId != M.id.movementId  AND M.positionLevel != (SELECT B.positionLevel FROM Movement B"
						+ " WHERE B.id.hostId = :hostId" 
						+ " AND B.id.entityId = :entityId" 
						+ " AND B.id.movementId = :movementId)";
				
				 String [] paramNames = new String [] {"hostId","entityId","movementId"};
				 String [] values = new String [] {hostId,  entityId, movementId}; 
				removeList = getHibernateTemplate().findByNamedParam(query1, paramNames,values);
				// Validates the match flag and frame the second query
			} else if (matchFlag == true) {
				// Sets the movement Id
				tempMovementId = Long.valueOf(movementId);
				query2 = "select A.id.movementId"
						+ " FROM Movement A ,Movement B "
						+ " WHERE A.id.entityId = ?0"
						+ " AND A.matchId = ?1 " 
						+ " AND A.id.hostId = B.id.hostId"
						+ " AND A.id.entityId = B.id.entityId"
						+ " AND A.currencyCode = B.currencyCode"
						+ " AND A.matchId = B.matchId"
						+ " AND A.id.movementId > B.id.movementId"
						+ " AND A.positionLevel != B.positionLevel";
				
				query3 = "select B.id.movementId"
						+ " FROM Movement A ,Movement B"
						+ " WHERE A.id.entityId = ?0" 
						+ " AND A.matchId = ?1 " 
						+ " AND A.id.hostId = B.id.hostId"
						+ " AND A.id.entityId = B.id.entityId"
						+ " AND A.currencyCode = B.currencyCode"
						+ " AND A.matchId = B.matchId"
						+ " AND A.id.movementId > B.id.movementId"
						+ " AND A.positionLevel != B.positionLevel";
				tempMvntList1 = getHibernateTemplate().find(query2, new Object[] {entityId, tempMovementId});
				tempMvntList2 = getHibernateTemplate().find(query3, new Object[] {entityId, tempMovementId});
			
				while (count < tempMvntList1.size()) {
					strCombine = tempMvntList1.get(count).toString() + ","
							+ tempMvntList2.get(count).toString();
					removeList.add(strCombine);
					count++;
				}
			}
			log.debug(this.getClass().getName() + " -[getPrevMatch]- Exiting");
		} catch (DataAccessException e) {
			log.error("DataAccessException caught in "
					+ this.getClass().getName() + " - [getPrevMatch]. Cause: "
					+ e.getMessage());
			throw SwtErrorHandler.getInstance().handleException(e,
					"getPrevMatch", MatchDisplayDAOHibernate.class);
		} catch (NumberFormatException e) {
			log.error("NumberFormatException caught in "
					+ this.getClass().getName() + " - [getPrevMatch]. Cause: "
					+ e.getMessage());
			throw SwtErrorHandler.getInstance().handleException(e,
					"getPrevMatch", MatchDisplayDAOHibernate.class);
		} catch (Exception e) {
			log.error("Exception caught in " + this.getClass().getName()
					+ " - [getPrevMatch]. Cause: " + e.getMessage());
			throw SwtErrorHandler.getInstance().handleException(e,
					"getPrevMatch", MatchDisplayDAOHibernate.class);
		} finally {
			// Cleaning unreferenced objects
			count = 0;
			query1 = null;
			query2 = null;
			query3 = null;
			strCombine = null;
			tempMovementId = null;
			tempMvntList1 = null;
			tempMvntList2 = null;
		}
		return removeList;
	}

	/**
	 * Update the movement details into p_movement table
	 * 
	 * @param Movement
	 *            mvt
	 * @return void
	 * @throws SwtException
	 */
	public void updateMovements(Movement mvt) throws SwtException {
		try {
			log.debug(this.getClass().getName()
					+ "- [updateMovements] - Entering ");
			/* Updates the given movement details */
			getHibernateTemplate().update(mvt);
			getHibernateTemplate().flush();
			log.debug(this.getClass().getName()
					+ "- [updateMovements] - Exiting ");
		} catch (DataAccessException e) {
			log.error("DataAccessException caught in "
					+ this.getClass().getName()
					+ " - [updateMovements]. Cause: " + e.getMessage());
			throw SwtErrorHandler.getInstance().handleException(e,
					"updateMovements", MatchDisplayDAOHibernate.class);
		} catch (Exception e) {
			log.error("Exception caught in " + this.getClass().getName()
					+ " - [updateMovements]. Cause: " + e.getMessage());
			throw SwtErrorHandler.getInstance().handleException(e,
					"updateMovements", MatchDisplayDAOHibernate.class);
		}
	}

	/**
	 * Gets the movement details from p_movement table
	 * 
	 * @param String
	 *            entityId
	 * @param String
	 *            hostId
	 * @param String
	 *            movementIds
	 * @return Collection object
	 * @throws SwtException
	 */
	@SuppressWarnings("unchecked")
	public Collection getMovementDetails(String entityId, String hostId,
			String movementIds) throws SwtException {
		// Holds the collection of movement details
		Collection collMvmntDetails = null;
		try {
			log.debug(this.getClass().getName()
					+ "- [getMovementDetails] - Entering ");
			// Gets the collection of movement details
			collMvmntDetails = (Collection ) getHibernateTemplate().find(
					"from Movement m where m.id.hostId=?0"
							+ " and  m.id.entityId=?1"
							+ " and m.id.movementId in("+movementIds+")", new Object[]{hostId, entityId});
			log.debug(this.getClass().getName()
					+ "- [getMovementDetails] - Exiting ");
		} catch (DataAccessException e) {
			log.error("DataAccessException caught in "
					+ this.getClass().getName()
					+ " - [getMovementDetails]. Cause: " + e.getMessage());
			throw SwtErrorHandler.getInstance().handleException(e,
					"getMovementDetails", MatchDisplayDAOHibernate.class);
		} catch (Exception e) {
			log.error("Exception caught in " + this.getClass().getName()
					+ " - [getMovementDetails]. Cause: " + e.getMessage());
			throw SwtErrorHandler.getInstance().handleException(e,
					"getMovementDetails", MatchDisplayDAOHibernate.class);
		}
		return collMvmntDetails;
	}

	/**
	 * Update the match details into p_match table
	 * 
	 * @param Match
	 *            match
	 * @return void
	 * @throws SwtException
	 */
	public void updateMatch(Match match) throws SwtException {
		try {
			log
					.debug(this.getClass().getName()
							+ "- [updateMatch] - Entering ");
			/* Updates the match details */
			getHibernateTemplate().update(match);
			getHibernateTemplate().flush();
			log.debug(this.getClass().getName() + "- [updateMatch] - Exiting ");
		} catch (DataAccessException e) {
			log.error("DataAccessException caught in "
					+ this.getClass().getName() + " - [updateMatch]. Cause: "
					+ e.getMessage());
			throw SwtErrorHandler.getInstance().handleException(e,
					"updateMatch", MatchDisplayDAOHibernate.class);
		} catch (Exception e) {
			log.error("Exception caught in " + this.getClass().getName()
					+ " - [updateMatch]. Cause: " + e.getMessage());
			throw SwtErrorHandler.getInstance().handleException(e,
					"updateMatch", MatchDisplayDAOHibernate.class);
		}
	}

	/**
	 * Gets the lock status from p_movement_lock table
	 * 
	 * @param String
	 *            hostId
	 * @param Long
	 *            movementId
	 * @return int
	 * @throws SwtException
	 */
	@SuppressWarnings("unchecked")
	public int checkLocked(String hostId, Long movementId) throws SwtException {
		// Resultant value tells whether the movement is locked (i.e. is it
		// already in use by any user)
		int isLocked = 0;
		// Holds the list of movement locks
		List checkLock = null;
		try {
			log
					.debug(this.getClass().getName()
							+ "- [updateMatch] - Entering ");
			/* Gets the movement lock details */
			checkLock = (List ) getHibernateTemplate().find(
					"from MovementLock m where m.id.hostId='" + hostId
							+ "' and m.id.movementId='" + movementId + "'");
			/* Condition checked based on the size */
			if (checkLock.size() != 0) {
				isLocked = 1;
			}
			log.debug(this.getClass().getName() + "- [updateMatch] - Exiting ");
		} catch (DataAccessException e) {
			log.error("DataAccessException caught in "
					+ this.getClass().getName() + " - [checkLocked]. Cause: "
					+ e.getMessage());
			throw SwtErrorHandler.getInstance().handleException(e,
					"checkLocked", MatchDisplayDAOHibernate.class);
		} catch (Exception e) {
			log.error("Exception caught in " + this.getClass().getName()
					+ " - [checkLocked]. Cause: " + e.getMessage());
			throw SwtErrorHandler.getInstance().handleException(e,
					"checkLocked", MatchDisplayDAOHibernate.class);
		} finally {
			// Cleaning unreferenced objects
			checkLock = null;
		}
		return isLocked;
	}

	/**
	 * Save/Update the movement details/Delete the match details from p_match
	 * table
	 * 
	 * @param Collection
	 *            addColl
	 * @param Collection
	 *            updateColl
	 * @param Collection
	 *            deleteColl
	 * @return void
	 * @throws SwtException
	 */
	@SuppressWarnings("unchecked")
	public void doDatabaseOperation(Collection addColl, Collection updateColl,
			Collection deleteColl) throws SwtException {
		
		Iterator itrAddColl = null;
		Iterator itrUpdateColl = null;
		Iterator itrDelColl = null;
		Object obj = null;
		Session session = null;
		Transaction tx = null;
		SwtInterceptor interceptor = null;
		PreparedStatement pstmt = null;
		Connection conn = null;
		try {
			log.debug(this.getClass().getName()
					+ "- [doDatabaseOperation] - Entering ");
			interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");
			session = getHibernateTemplate().getSessionFactory().withOptions().interceptor(interceptor).openSession();
			tx = session.beginTransaction();
			
			// Condition checked to save the collection object( match details )
			if (addColl != null) {
				itrAddColl = addColl.iterator();
				while (itrAddColl.hasNext()) {
					obj = itrAddColl.next();
					if (obj != null)
						session.save(obj);
				}
			}
			
			// Condition checked to update the collection object( match details FIRST
			// )
			if (updateColl != null) {
				itrUpdateColl = updateColl.iterator();
				while (itrUpdateColl.hasNext()) {
					obj = itrUpdateColl.next();
					if (obj != null && obj instanceof Match) {
						session.update(obj);
						itrUpdateColl.remove();
					}
				}
			}
			// Condition checked to update the collection object( other details second
			// )
			ArrayList<Movement> movListToUpdate = new ArrayList<Movement>();
			if (updateColl != null) {
				itrUpdateColl = updateColl.iterator();
				while (itrUpdateColl.hasNext()) {
					obj = itrUpdateColl.next();
					if (obj != null) {
						if (obj instanceof Movement && updateColl.size() > 50) {
							movListToUpdate.add((Movement) obj);
						}else {
							session.update(obj);
							
						}
					}
				}
			}
			
			
			if(!movListToUpdate.isEmpty()) {
				batchUpdateMovement(movListToUpdate);
			}
			
			DataSource dataSource = (DataSource) SwtUtil.getBean("dataSourceDb");
			conn = dataSource.getConnection();
			conn.setAutoCommit(false);
			// get the system date time
			long time = SwtUtil.getSystemDatewithTime().getTime();
			Timestamp updateDateTime = new Timestamp(time);
			/* Query to fetches account details based on account id */
			pstmt = conn
					.prepareStatement("DELETE FROM P_MOVEMENT_LOCK WHERE  MOVEMENT_ID in (?)");
		
				/* Iterate account access details */
			ArrayList<String> movList = new ArrayList<String>();
			if (deleteColl != null) {
				itrDelColl = deleteColl.iterator();
				while (itrDelColl.hasNext()) {
					obj = itrDelColl.next();
					if (obj != null) {
						if (obj instanceof MovementLock) {
							if (obj != null) {
								movList.add(""+((MovementLock) obj).getId().getMovementId());
							}
						} else {
							session.delete(obj);
						}
						
					}
				}
				StringBuilder sb = new StringBuilder();
				int batchSize = 500;
				int count = 0;
				for (int i = 0; i < movList.size(); i++) {
				    sb.append(movList.get(i));
				    if (i < movList.size() - 1) {
				        sb.append(",");
				    }
				    count++;
				    if (count % batchSize == 0 || i == movList.size() - 1) {
				        String inClause = sb.toString();
				        if (inClause.endsWith(",")) {
				        	inClause = inClause.substring(0, inClause.length() - 1);
			        	}
				        
				        pstmt = conn.prepareStatement("DELETE FROM P_MOVEMENT_LOCK WHERE MOVEMENT_ID in (" + inClause + ")");
				        pstmt.executeUpdate();
				        sb = new StringBuilder();
				    }
				}
			}
			tx.commit();
			conn.commit();
			log.debug(this.getClass().getName()
					+ "- [doDatabaseOperation] - Exiting ");
		} catch (DataAccessException e) {
			log.error("DataAccessException caught in "
					+ this.getClass().getName()
					+ " - [doDatabaseOperation]. Cause: " + e.getMessage());
			throw SwtErrorHandler.getInstance().handleException(e,
					"doDatabaseOperation", MatchDisplayDAOHibernate.class);
		} catch (Exception e) {
			e.printStackTrace();
			log.error("Exception caught in " + this.getClass().getName()
					+ " - [doDatabaseOperation]. Cause: " + e.getMessage());
			throw SwtErrorHandler.getInstance().handleException(e,
					"doDatabaseOperation", MatchDisplayDAOHibernate.class);
		} finally {
			JDBCCloser.close(null, pstmt, conn, null);
			// Cleaning unreferenced objects
			itrAddColl = null;
			itrUpdateColl = null;
			obj = null;
			JDBCCloser.close(session);
		}
	}

	/**
	 * Gets the Match Notes details from p_match_note table
	 * 
	 * @param String
	 *            entityId
	 * @param String
	 *            hostId
	 * @param String
	 *            matchId
	 * @return Collection object
	 * @throws SwtException
	 */
	@SuppressWarnings("unchecked")
	public Collection getMatchNotes(String entityId, String hostId,
			String matchId) throws SwtException {
		// Holds the collection of match notes
		Collection collNotes = null;
		try {
			log.debug(this.getClass().getName()
					+ "- [getMatchNotes] - Entering ");
			collNotes = (Collection ) getHibernateTemplate().find(
					"from MatchNote m where m.id.hostId=?0" 
							 + "and m.id.entityId=?1"
							 + " and m.id.matchId=?2", new Object[]{hostId, entityId, new Long(matchId)});
			log.debug(this.getClass().getName()
					+ "- [getMatchNotes] - Exiting ");
		} catch (DataAccessException e) {
			log.error("DataAccessException caught in "
					+ this.getClass().getName() + " - [getMatchNotes]. Cause: "
					+ e.getMessage());
			throw SwtErrorHandler.getInstance().handleException(e,
					"getMatchNotes", MatchDisplayDAOHibernate.class);
		} catch (Exception e) {
			log.error("Exception caught in " + this.getClass().getName()
					+ " - [getMatchNotes]. Cause: " + e.getMessage());
			throw SwtErrorHandler.getInstance().handleException(e,
					"getMatchNotes", MatchDisplayDAOHibernate.class);
		}
		return collNotes;
	}

	/**
	 * Gets the Match Notes details from p_match_note table
	 * 
	 * @param String
	 *            entityId
	 * @param String
	 *            hostId
	 * @param String
	 *            matchId
	 * @return boolean
	 * @throws SwtException
	 */
	@SuppressWarnings("unchecked")
	public boolean checkNotes(String entityId, String hostId, String matchId) throws SwtException {
		boolean notesPresent = false;
		String archDatabaseName = null;
		List<MatchNote> notes = null;
		SwtDataSource dataSource = null;
		Session session = null;
		try {
			log.debug(this.getClass().getName() + "- [checkNotes] - Entering ");
			dataSource = (SwtDataSource) SwtUtil.getBean("dataSourceDb");
			archDatabaseName = (String) dataSource.useDataSource.get();
			if (archDatabaseName != null) {
				session = getHibernateTemplate().getSessionFactory().openSession();
				String queryString = "FROM MatchNote mn WHERE mn.id.hostId = :hostId";
				if (entityId != null && entityId.trim().length() > 0) {
					queryString += " AND mn.id.entityId = :entityId";
				}
				if (matchId != null && matchId.trim().length() > 0) {
					queryString += " AND mn.id.matchId = :matchId";
				}

				Query<MatchNote> query = session.createQuery(queryString);
				query.setParameter("hostId", hostId);
				if (entityId != null && entityId.trim().length() > 0) {
					query.setParameter("entityId", entityId);
				}
				if (matchId != null && matchId.trim().length() > 0) {
					query.setParameter("matchId", new Long(matchId));
				}

				notes = query.list();
			} else {
				notes = (List<MatchNote>) getHibernateTemplate().find(
						"from MatchNote m where m.id.hostId=?0 and m.id.entityId=?1" +
								" and m.id.matchId=?2",
						new Object[] {hostId, entityId, new Long(matchId)});
			}
			if (notes.size() > 0) {
				notesPresent = true;
			}
			log.debug(this.getClass().getName() + "- [checkNotes] - Exiting ");
		} catch (DataAccessException e) {
			log.error("DataAccessException caught in " +
					this.getClass().getName() + " - [checkNotes]. Cause: " +
					e.getMessage());
			throw SwtErrorHandler.getInstance().handleException(e, "checkNotes", MatchDisplayDAOHibernate.class);
		} catch (HibernateException e) {
			log.error("HibernateException caught in " +
					this.getClass().getName() + " - [checkNotes]. Cause: " +
					e.getMessage());
			throw SwtErrorHandler.getInstance().handleException(e, "checkNotes", MatchDisplayDAOHibernate.class);
		} catch (Exception e) {
			log.error("Exception caught in " + this.getClass().getName() +
					" - [checkNotes]. Cause: " + e.getMessage());
			throw SwtErrorHandler.getInstance().handleException(e, "checkNotes", MatchDisplayDAOHibernate.class);
		} finally {
			archDatabaseName = null;
			notes = null;
			dataSource = null;
			JDBCCloser.close(session);
		}
		return notesPresent;
	}

	/**
	 * Gets the Position Level details from P_POSITION_LEVEL_NAME table
	 * 
	 * @param String
	 *            hostId
	 * @param String
	 *            entityId
	 * @param Integer
	 *            positionLevelId
	 * @return Collection object
	 * @throws SwtException
	 */
	@SuppressWarnings("unchecked")
	public Collection getPositionLevelRecord(String hostId, String entityId,
			Integer positionLevelId) throws SwtException {
		// Holds the list of position levels
		List posLevelColl = null;
		try {
			log.debug(this.getClass().getName()
					+ "- [getPositionLevelRecord] - Entering ");
			posLevelColl = (List ) getHibernateTemplate().find(
					"from EntityPositionLevel e where e.id.hostId=?0 and e.id.entityId=?1"
							+ " and e.id.positionLevel=?2",
					new Object[] { hostId, entityId, positionLevelId });
			log.debug(this.getClass().getName()
					+ "- [getPositionLevelRecord] - Exiting ");
		} catch (DataAccessException e) {
			log.error("DataAccessException caught in "
					+ this.getClass().getName()
					+ " - [getPositionLevelRecord]. Cause: " + e.getMessage());
			throw SwtErrorHandler.getInstance().handleException(e,
					"getPositionLevelRecord", MatchDisplayDAOHibernate.class);
		} catch (Exception e) {
			log.error("Exception caught in " + this.getClass().getName()
					+ " - [getPositionLevelRecord]. Cause: " + e.getMessage());
			throw SwtErrorHandler.getInstance().handleException(e,
					"getPositionLevelRecord", MatchDisplayDAOHibernate.class);
		}
		return posLevelColl;
	}
	
	private void batchUpdateMovement (ArrayList<Movement> movListToUpdate) throws SwtException {
		
		PreparedStatement pstmt = null;
		Connection conn = null;
		
		DataSource dataSource = (DataSource) SwtUtil.getBean("dataSourceDb");
		try {
		conn = dataSource.getConnection();
		conn.setAutoCommit(false);
		int countUpdate = 0;
		if(!movListToUpdate.isEmpty()) {
			String query = "UPDATE P_MOVEMENT SET CURRENCY_CODE = ?, BOOKCODE = ?, VALUE_DATE = ?, AMOUNT = ?, SIGN = ?, MOVEMENT_TYPE = ?, ACCOUNT_ID = ?, REFERENCE1 = ?, REFERENCE2 = ?, REFERENCE3 = ?, REFERENCE4 = ?,"
					+ " COUNTERPARTY_ID = ?, COUNTERPARTY_TEXT1 = ?, COUNTERPARTY_TEXT2 = ?, COUNTERPARTY_TEXT3 = ?, COUNTERPARTY_TEXT4 = ?, COUNTERPARTY_TEXT5 = ?, BENEFICIARY_ID = ?, BENEFICIARY_TEXT1 = ?, BENEFICIARY_TEXT2 = ?,"
					+ " BENEFICIARY_TEXT3 = ?, BENEFICIARY_TEXT4 = ?, BENEFICIARY_TEXT5 = ?, CUSTODIAN_ID = ?, CUSTODIAN_TEXT1 = ?, CUSTODIAN_TEXT2 = ?, CUSTODIAN_TEXT3 = ?, CUSTODIAN_TEXT4 = ?, CUSTODIAN_TEXT5 = ?, BOOKCODE_AVAIL = ?,"
					+ " POSITION_LEVEL = ?, PREDICT_STATUS = ?, EXTRACT_STATUS = ?, MATCH_ID = ?, MATCH_STATUS = ?, UPDATE_DATE = ?, UPDATE_USER = ?, INPUT_DATE = ?, INPUT_SOURCE = ?, MESSAGE_ID = ?, MESSAGE_FORMAT = ?, INITIAL_PREDICT_STATUS = ?,"
					+ " INPUT_USER = ?, INPUT_ROLE = ?, NOTES_COUNT = ?, OPEN = ?, EXT_BAL_STATUS = ? ,"
					 + "TO_MATCH = ?, MATCHING_PARTY = ?, PRODUCT_TYPE = ?, POSTING_DATE = ?, SETTLEMENT_DATETIME = ?, EXPECTED_SETTLEMENT_DATETIME = ?, CRITICAL_PAYMENT_TYPE = ?, ORDERING_CUSTOMER = ?, ORDERING_INSTITUTION = ?, SENDERS_CORRES = ?, "
				        + "RECEIVERS_CORRES = ?, INTMDRY_INSTITUTION_ID = ?, ACC_WITH_INSTITUTION_ID = ?, BENEFICIARY_CUST = ?, ILM_FCAST_STATUS = ? WHERE HOST_ID = ? AND ENTITY_ID = ? AND MOVEMENT_ID = ?";
			pstmt = conn.prepareStatement(query);
			for (Movement movement : movListToUpdate) {
			    pstmt.setString(1, movement.getCurrencyCode());
			    pstmt.setString(2, movement.getBookCode());
			    pstmt.setDate(3, movement.getValueDate() == null ? null : new java.sql.Date(movement.getValueDate().getTime()));
			    pstmt.setDouble(4, movement.getAmount());
			    pstmt.setString(5, movement.getSign());
			    pstmt.setString(6, movement.getMovementType());
			    pstmt.setString(7, movement.getAccountId());
			    pstmt.setString(8, movement.getReference1());
			    pstmt.setString(9, movement.getReference2());
			    pstmt.setString(10, movement.getReference3());
			    pstmt.setString(11, movement.getReference4());
			    pstmt.setString(12, movement.getCounterPartyId());
			    pstmt.setString(13, movement.getCounterPartyText1());
			    pstmt.setString(14, movement.getCounterPartyText2());
			    pstmt.setString(15, movement.getCounterPartyText3());
			    pstmt.setString(16, movement.getCounterPartyText4());
			    pstmt.setString(17, movement.getCounterPartyText5());
			    pstmt.setString(18, movement.getBeneficiaryId());
			    pstmt.setString(19, movement.getBeneficiaryText1());
			    pstmt.setString(20, movement.getBeneficiaryText2());
			    pstmt.setString(21, movement.getBeneficiaryText3());
			    pstmt.setString(22, movement.getBeneficiaryText4());
			    pstmt.setString(23, movement.getBeneficiaryText5());
			    pstmt.setString(24, movement.getCustodianId());
			    pstmt.setString(25, movement.getCustodianText1());
			    pstmt.setString(26, movement.getCustodianText2());
			    pstmt.setString(27, movement.getCustodianText3());
			    pstmt.setString(28, movement.getCustodianText4());
			    pstmt.setString(29, movement.getCustodianText5());
			    pstmt.setString(30, movement.getBookCodeAvail());
			    pstmt.setInt(31, movement.getPositionLevel());
			    pstmt.setString(32, movement.getPredictStatus());
			    pstmt.setString(33, movement.getExtractStatus());
			    pstmt.setLong(34, movement.getMatchId());
			    pstmt.setString(35, movement.getMatchStatus());
			    pstmt.setDate(36, movement.getUpdateDate() == null ? null : new java.sql.Date(movement.getUpdateDate().getTime()));
			    pstmt.setString(37, movement.getUpdateUser());
			    pstmt.setDate(38, movement.getInputDate() == null ? null : new java.sql.Date(movement.getInputDate().getTime()));
			    pstmt.setString(39, movement.getInputSource());
			    pstmt.setString(40, movement.getMessageId());
			    pstmt.setString(41, movement.getMessageFormat());
			    pstmt.setString(42, movement.getInitialPredStatus());
			    pstmt.setString(43, movement.getInputUser());
			    pstmt.setString(44, movement.getInputRole());
			    pstmt.setInt(45, movement.getNotesCount());
			    pstmt.setString(46, movement.getOpenFlag());
			    pstmt.setString(47, movement.getExtBalStatus());
			    
		        pstmt.setString(48, movement.getToMatch());
		        pstmt.setString(49, movement.getMatchingParty());
		        pstmt.setString(50, movement.getProductType());
		        pstmt.setDate(51, movement.getPostingDate() != null ? new java.sql.Date(movement.getPostingDate().getTime()) : null);
		        pstmt.setDate(52, movement.getSettlementDateTime() != null ? new java.sql.Date(movement.getSettlementDateTime().getTime()) : null);
		        pstmt.setDate(53, movement.getExpectedSettlementDateTime() != null ? new java.sql.Date(movement.getExpectedSettlementDateTime().getTime()) : null);
		        pstmt.setString(54, movement.getCriticalPaymentType());
		        pstmt.setString(55, movement.getOrderingCustomerId());
		        pstmt.setString(56, movement.getOrderingInstitutionId());
		        pstmt.setString(57, movement.getSenderCorrespondentId());
		        pstmt.setString(58, movement.getReceiverCorrespondentId());
		        pstmt.setString(59, movement.getIntermediaryInstitutionId());
		        pstmt.setString(60, movement.getAccountWithInstitutionId());
		        pstmt.setString(61, movement.getBeneficiaryCustomerId());
		        pstmt.setString(62, movement.getIlmFcastStatus());
		        
			    pstmt.setString(63, movement.getId().getHostId());
			    pstmt.setString(64, movement.getId().getEntityId());
			    pstmt.setLong(65, movement.getId().getMovementId());

			    pstmt.addBatch();
			    
			    countUpdate++;
			    if (countUpdate % 100 == 0) { // execute batch every 200 records
			        pstmt.executeBatch();
			    }
			}
			// execute the remaining batch
			pstmt.executeBatch();
			conn.commit();
		}
		} catch (Exception e) {
			e.printStackTrace();
			log.error("Exception caught in " + this.getClass().getName()
					+ " - [doDatabaseOperation]. Cause: " + e.getMessage());
			throw SwtErrorHandler.getInstance().handleException(e,
					"doDatabaseOperation", MatchDisplayDAOHibernate.class);
		} finally {
			JDBCCloser.close(null, pstmt, conn, null);
			// Cleaning unreferenced objects
		}
		
	}

	/**
	 * This method gets the total number of notes for the given movement Id from
	 * P_MOVEMENT_NOTE table.<br>
	 * 
	 * @param String
	 *            entityId
	 * @param String
	 *            hostId
	 * @param String
	 *            movementIdAsString
	 * @return Integer - Number of notes.
	 * @throws SwtException
	 */
	@SuppressWarnings("unchecked")
	public Integer getNoOfNotes(String entityId, String hostId, String movementIdAsString) throws SwtException {
		Integer noteSize = 0;
		String archDatabaseName = null;
		SwtDataSource dataSource = null;
		Session session = null;
		try {
			log.debug(this.getClass().getName() + " - [getNoOfNotes] - Begins ");
			dataSource = (SwtDataSource) SwtUtil.getBean("dataSourceDb");
			archDatabaseName = (String) dataSource.useDataSource.get();
			if (archDatabaseName != null) {
				session = getHibernateTemplate().getSessionFactory().openSession();
				String queryString = "SELECT COUNT(m) FROM MovementNote m WHERE m.id.hostId = :hostId";

				if (entityId != null && entityId.trim().length() > 0) {
					queryString += " AND m.id.entityId = :entityId";
				}

				if (movementIdAsString != null && movementIdAsString.trim().length() > 0) {
					queryString += " AND m.id.movementId = :movementId";
				}

				Query<Long> query = session.createQuery(queryString);
				query.setParameter("hostId", hostId);

				if (entityId != null && entityId.trim().length() > 0) {
					query.setParameter("entityId", entityId);
				}

				if (movementIdAsString != null && movementIdAsString.trim().length() > 0) {
					query.setParameter("movementId", Long.valueOf(movementIdAsString));
				}

				List<Long> results = query.getResultList();
				noteSize = results.get(0).intValue();
			} else {
				session = getHibernateTemplate().getSessionFactory().openSession();
				noteSize = ((Integer) session.createQuery("select count(*) from MovementNote m where m.id.hostId = '" + hostId + "' and m.id.entityId = '" + entityId + "' and m.id.movementId = '" + movementIdAsString + "'").iterate().next()).intValue();
			}
			log.debug(this.getClass().getName() + "- [getNoOfNotes] - Ends ");
		} catch (Exception exception) {
			log.error("Exception caught in " + this.getClass().getName() + " - [getNoOfNotes]. Cause: " + exception.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exception, "getNoOfNotes", MatchDisplayDAOHibernate.class);
		} finally {
			archDatabaseName = null;
			dataSource = null;
			JDBCCloser.close(session);
		}
		return noteSize;
	}

	/*
	 * Start:Code Modified For Mantis 2103 by Sudhakar on 26-10-2012: Match
	 * Queues: Drilldown does not work in certain case
	 */
	/**
	 * Method to displayMatch list in Movement Match Summary display screen, for
	 * the values selected in Movement match queue screen
	 * 
	 * @param hostId
	 * @param entityId
	 * @param currencyId
	 * @param status
	 * @param quality
	 * @param valueDate
	 * @param day
	 * @param applyCurrencyThreshold
	 * @param noIncludedMovementMatches
	 * 
	 * @return List - Collection of Movements for the selected match
	 * @throws SwtException
	 */
	@SuppressWarnings("unchecked")
	public List<Long> getLatestMatchIdAsList(String hostId, String entityId,
			String currencyId, String status, String quality, Date valueDate,
			String day, String applyCurrencyThreshold,
			String noIncludedMovementMatches) throws SwtException {
		// To hold list of Latestmatchid for selected date
		List matchIdList = null;
		// To hold the query to get the match id details
		StringBuffer prefixQuery = null;
		// To increase the input parameter index
		int paramIndex;
		// To hold the input parameter based on condition
		List<Object> queryParam = null;
		try {
			log.debug(this.getClass().getName()
					+ "- [getLatestMatchIdAsList] - Entry ");
			// Instantiate the StringBuffer to hold the query
			prefixQuery = new StringBuffer(
					"select distinct match.id.matchId from Match match where match.id.hostId=?0 and match.id.entityId =?1 "
							+ "and match.currencyCode=?2 and match.status =?3 and match.matchQuality =?4 ");
			// Check the value date if it is null,set the entity offset date
			if (valueDate == null)
				valueDate = SwtUtil.getSysParamDateWithEntityOffset(entityId);
			// assign the paramIndex as zero
			paramIndex = 0;
			// Instantiate the queryform object
			queryParam = new ArrayList<Object>();
			// Add the hostid,entityId,currencyId,status,quality in query
			// parameter
			queryParam.add(paramIndex++, hostId);
			queryParam.add(paramIndex++, entityId);
			queryParam.add(paramIndex++, currencyId);
			queryParam.add(paramIndex++, status);
			queryParam.add(paramIndex++, quality);
			// Append the matchdate in prefixQuery if selected tab date as All
			int nextParamIndexHiberante = 5;
			if (!day.equals("7")) {
				prefixQuery.append("and match.maxValueDate =?"+nextParamIndexHiberante+" ");
				queryParam.add(paramIndex++, SwtUtil.truncateDateTime(valueDate));
				nextParamIndexHiberante++;
			}
			if (noIncludedMovementMatches.equals(SwtConstants.YES)) {
				prefixQuery.append("and match.predictStatusFlag = 'N' ");
			}
			if (applyCurrencyThreshold.equals(SwtConstants.YES)) {
				prefixQuery
						.append("and match.maxAmount >= ( select nvl(c.thresholdProduct,0) from Currency c where c.id.hostId =?"+nextParamIndexHiberante+" and c.id.entityId =?"+(nextParamIndexHiberante+1)+" "
								+ "and c.id.currencyCode =?"+(nextParamIndexHiberante+2)+")");
				queryParam.add(paramIndex++, hostId);
				queryParam.add(paramIndex++, entityId);
				queryParam.add(paramIndex++, currencyId);
				nextParamIndexHiberante +=3;
			}
			prefixQuery.append(" order by match.id.matchId desc");
			// Get the matchid details
			matchIdList = getHibernateTemplate().find(prefixQuery.toString(),
					queryParam.toArray());
			log.debug(this.getClass().getName()
					+ "- [getLatestMatchIdAsList] - Exit ");
		} catch (Exception exp) {
			log
					.error(this.getClass().getName()
							+ " - Exception Catched in [getLatestMatchIdAsList] method : - "
							+ exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp,
					"getLatestMatchIdAsList", this.getClass());
		} finally {
			// Cleaning unreferenced objects
			prefixQuery = null;
			queryParam = null;
		}
		return matchIdList;
	}

	// Added for Mantis 1443
	/**
	 * Method to displayMatch list in Movement Match Summary display screen, for
	 * the values selected Scenario ID
	 */
	
	public List<Long> getMatchIdsPerScenarioAsList (String hostId, String  entityId,
			String  currencyCode,String  scenarioId,String  applyCurrencyThreshold,String currencyGroup)throws SwtException {
		
		Session session = null;
		Connection conn = null;
		CallableStatement cstmt = null;
		ResultSet rs = null;
		List<Long>  list = null;

		try {
			
			log.debug(this.getClass().getName()
					+ "-[getMatchIdsPerScenarioAsList()]- Entry");
			session = getHibernateTemplate().getSessionFactory().openSession();
			conn = SwtUtil.connection(session);
			cstmt = conn
					.prepareCall("{call PKG_ALERT.proc_get_list_match(?,?,?,?,?,?)}");
			cstmt.setString(1, scenarioId);
			cstmt.setString(2, entityId);
			cstmt.setString(3, currencyCode);
			cstmt.setString(4, applyCurrencyThreshold);
			cstmt.setString(5, currencyGroup);
			cstmt.registerOutParameter(6, oracle.jdbc.OracleTypes.CURSOR);
			cstmt.execute();
			rs = (ResultSet) cstmt.getObject(6);
			list = new ArrayList();
			if(rs!=null){
			while (rs.next()) {
				list.add(rs.getLong(1));
				}
			}
			log.debug(this.getClass().getName() + " - [getMatchIdsPerScenarioAsList] - "
					+ "Exit");
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [getMatchIdsPerScenarioAsList] method : - "
					+ exp.getMessage());
		} finally {
			SwtException thrownException = null;
			Object[] exceptions = JDBCCloser.close(rs, cstmt, conn, session);
			
			if (exceptions[0] != null)
				thrownException = SwtErrorHandler.getInstance().handleException((SQLException) exceptions[0], "getGenericDisplayData",WorkflowMonitorDAOHibernate.class);
			
			if (thrownException == null && exceptions[1] !=null) 
				thrownException = SwtErrorHandler.getInstance().handleException((HibernateException) exceptions[1], "getGenericDisplayData",WorkflowMonitorDAOHibernate.class);
			
			if (thrownException != null)
				throw thrownException;
			
		}
		
		
		return list;
	}
	

	/*
	 * End:Code Modified For Mantis 2103 by Sudhakar on 26-10-2012: Match
	 * Queues: Drilldown does not work in certain case
	 */

	/**
	 * This method is used to get all the position levels from
	 * P_POSITION_LEVEL_NAME table.<br>
	 * 
	 * @param hostId
	 * @param entityId
	 * @return Collection of all position levels.
	 * @throws SwtException
	 */
	@SuppressWarnings("unchecked")
	public List<EntityPositionLevel> getAllPositionLevels(String hostId,
			String entityId) throws SwtException {
		// Holds the list of position levels from p_position_level_name table
		List<EntityPositionLevel> listPositionLevels = null;
		try {
			log.debug(this.getClass().getName()
					+ "- [getAllPositionLevels] - Begins ");
			// Gets the position level details
			listPositionLevels = (List<EntityPositionLevel> ) getHibernateTemplate().find(
							"from EntityPositionLevel e where e.id.hostId=?0 and e.id.entityId=?1",
							new Object[] { hostId, entityId });
			log.debug(this.getClass().getName()
					+ "- [getAllPositionLevels] - Ends ");
		} catch (Exception exp) {
			log.error("Exception caught in " + this.getClass().getName()
					+ " - [getAllPositionLevels]. Cause: " + exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp,
					"getAllPositionLevels", this.getClass());
		}
		return listPositionLevels;
	}

	/**
	 * This method is used to get the account access status for the given match
	 * Id.<br>
	 * 
	 * @param roleId
	 * @param entityId
	 * @param matchId
	 * @throws SwtException
	 * @return Account Access Status
	 */
	public String getAccountAccessStatus(String roleId, String entityId,
			String matchId) throws SwtException {
		String accessStatus = null;
		Session session = null;
		Connection connection = null;
		PreparedStatement preparedStatement = null;
		ResultSet rsAccountAccess = null;
		try {
			log.debug(this.getClass().getName()
					+ " - [getAccountAccessStatus()] Begins");
			session = getHibernateTemplate().getSessionFactory().openSession();
			connection = SwtUtil.connection(session);
			preparedStatement = connection
					.prepareStatement("SELECT PK_APPLICATION.FNGETMATCHACCOUNTACCESS(?, ?, ?) FROM DUAL");
			preparedStatement.setString(1, roleId);
			preparedStatement.setString(2, entityId);
			preparedStatement.setString(3, matchId);
			rsAccountAccess = preparedStatement.executeQuery();
			if (rsAccountAccess.next()) {
				accessStatus = rsAccountAccess.getString(1);
			}
			log.debug(this.getClass().getName()
					+ " - [getAccountAccessStatus()] Ends");
		} catch (Exception e) {
			log.error("Exception caught in " + this.getClass().getName()
					+ " - [getAccountAccessStatus]. Cause: " + e.getMessage());
			throw SwtErrorHandler.getInstance().handleException(e,
					"getAccountAccessStatus", this.getClass());
		} finally {
			// edited by KaisBS for mantis 1745 : introducing the implementation
			// of the JDBCCloser class
			SwtException thrownException = null;
			Object[] exceptions = JDBCCloser.close(rsAccountAccess,
					preparedStatement, connection, session);

			if (exceptions[0] != null)
				thrownException = SwtErrorHandler.getInstance()
						.handleException((SQLException) exceptions[0],
								"getAccountAccessStatus",
								MatchDisplayDAOHibernate.class);

			if (thrownException == null && exceptions[1] != null)
				thrownException = SwtErrorHandler.getInstance()
						.handleException((HibernateException) exceptions[1],
								"getAccountAccessStatus",
								MatchDisplayDAOHibernate.class);

			if (thrownException != null)
				throw thrownException;

		}
		return accessStatus;
	}

	/*
	 * Start:Code Modified for Mantis 1270 by Chinniah on 29-Feb-2012:Data
	 * Archive setup: Remove redundant fields from Archive setup screen
	 */

	/**
	 * This method is used to get the archived movement from archive schema
	 * 
	 * @param entityId
	 * @param hostId
	 * @param matchId
	 * @param archiveId
	 * @return collMovementDetail
	 * @throws SwtException
	 */
	public Collection<Movement> getArchiveMovementDetailList(String entityId,
			String hostId, String matchId, String archiveId)
			throws SwtException {
		// declared to open hebernate session
		Session session = null;
		// declared open connection
		Connection connection = null;
		// declared to execute query to get db_link
		Statement statement = null;
		// result set to get the db_link
		ResultSet resultSetDbLink = null;
		// result set to get Movement details from the procedure
		ResultSet resultSet = null;
		// to hold the db_link
		String dbLink = null;
		// declared to execute the Procedure
		CallableStatement callableStatement = null;
		// declared to hold the Movement
		Movement movement = null;
		// collections to hopld the Movement details
		Collection<Movement> collMovementDetail = null;
		try {
			log.debug(this.getClass().getName()
					+ "- [getArchiveMovementDetailList] - Entering ");
			// get hibernate session
			session = getHibernateTemplate().getSessionFactory().openSession();
			// Get the database connection object
			connection = SwtUtil.connection(session);
			// create statement
			statement = connection.createStatement();
			// execute query to get Db_link
			statement
					.executeQuery("select DBLINK_SCHEMA_NAME from p_Archive where archive_id='"
							+ archiveId + "'");
			resultSetDbLink = statement.getResultSet();
			// getting db_link from resultset
			if (resultSetDbLink != null) {
				while (resultSetDbLink.next()) {
					dbLink = resultSetDbLink.getString(1);
				}
			}
			// Procedure call
			callableStatement = connection
					.prepareCall("{call PKG_ARCHIVE_DETAILS.SPSHOWARCHMOVEMENT(?,?,?,?,?,?)}");
			// set values to procedure
			callableStatement.setString(1, CacheManager.getInstance()
					.getHostId());
			callableStatement.setString(2, entityId);
			callableStatement.setLong(3, new Long(matchId));
			callableStatement.setString(4, "Y");
			callableStatement.setString(5, dbLink);
			callableStatement.registerOutParameter(6,
					oracle.jdbc.OracleTypes.CURSOR);
			// execute procedure
			callableStatement.execute();
			resultSet = (ResultSet) callableStatement.getObject(6);
			// Validates the ResultSet to get the movement details
			if (resultSet != null) {
				// Instantiates the ArrayList
				collMovementDetail = new ArrayList<Movement>();
				while (resultSet.next()) {
					// Instantiates the Movement class to get a new bean for
					// each loop
					movement = new Movement();
					movement.getId().setHostId(resultSet.getString("HOST_ID"));
					movement.getId().setEntityId(resultSet.getString("ENTITY_ID"));
					movement.getId().setMovementId(resultSet.getLong("MOVEMENT_ID"));
					movement.setCurrencyCode(resultSet.getString("CURRENCY_CODE"));
					movement.setBookCode(resultSet.getString("BOOKCODE"));
					movement.setValueDate(resultSet.getDate("VALUE_DATE"));
					movement.setAmount(resultSet.getDouble("AMOUNT"));
					movement.setSign(resultSet.getString("SIGN"));
					movement.setMovementType(resultSet.getString("MOVEMENT_TYPE"));
					movement.setAccountId(resultSet.getString("ACCOUNT_ID"));
					movement.setReference1(resultSet.getString("REFERENCE1"));
					movement.setReference2(resultSet.getString("REFERENCE2"));
					movement.setReference3(resultSet.getString("REFERENCE3"));
					movement.setReference4(resultSet.getString("REFERENCE4"));
					movement.setCounterPartyId(resultSet.getString("COUNTERPARTY_ID"));
					movement.setCounterPartyText1(resultSet.getString("COUNTERPARTY_TEXT1"));
					movement.setCounterPartyText2(resultSet.getString("COUNTERPARTY_TEXT2"));
					movement.setCounterPartyText3(resultSet.getString("COUNTERPARTY_TEXT3"));
					movement.setCounterPartyText4(resultSet.getString("COUNTERPARTY_TEXT4"));
					movement.setBeneficiaryId(resultSet.getString("BENEFICIARY_ID"));
					movement.setBeneficiaryText1(resultSet.getString("BENEFICIARY_TEXT1"));
					movement.setBeneficiaryText2(resultSet.getString("BENEFICIARY_TEXT2"));
					movement.setBeneficiaryText3(resultSet.getString("BENEFICIARY_TEXT3"));
					movement.setBeneficiaryText4(resultSet.getString("BENEFICIARY_TEXT4"));
					movement.setCustodianId(resultSet.getString("CUSTODIAN_ID"));
					movement.setCustodianText1(resultSet.getString("CUSTODIAN_TEXT1"));
					movement.setCustodianText2(resultSet.getString("CUSTODIAN_TEXT2"));
					movement.setCustodianText3(resultSet.getString("CUSTODIAN_TEXT3"));
					movement.setCustodianText4(resultSet.getString("CUSTODIAN_TEXT4"));
					movement.setBookCodeAvail(resultSet.getString("BOOKCODE_AVAIL"));
					movement.setPositionLevel(resultSet.getInt("POSITION_LEVEL"));
					movement.setPredictStatus(resultSet.getString("PREDICT_STATUS"));
					movement.setExtractStatus(resultSet.getString("EXTRACT_STATUS"));
					movement.setMatchId(resultSet.getLong("MATCH_ID"));
					movement.setMatchStatus(resultSet.getString("MATCH_STATUS"));
					movement.setUpdateDate(resultSet.getTimestamp("UPDATE_DATE"));
					movement.setUpdateUser(resultSet.getString("UPDATE_USER"));
					movement.setInputDate(resultSet.getTimestamp("INPUT_DATE"));
					movement.setInputSource(resultSet.getString("INPUT_SOURCE"));
					movement.setMessageId(resultSet.getString("MESSAGE_ID"));
					movement.setMessageFormat(resultSet.getString("MESSAGE_FORMAT"));
					movement.setInitialPredStatus(resultSet.getString("INITIAL_PREDICT_STATUS"));
					movement.setInputRole(resultSet.getString("INPUT_ROLE"));
					movement.setInputUser(resultSet.getString("INPUT_USER"));
					movement.setNotesCount(resultSet.getInt("NOTES_COUNT"));
					movement.setOpenFlag(resultSet.getString("OPEN"));
					movement.setExtBalStatus(resultSet.getString("EXT_BAL_STATUS"));
					movement.setToMatch(resultSet.getString("TO_MATCH"));
					movement.setMatchingParty(resultSet.getString("MATCHING_PARTY"));
					movement.setProductType(resultSet.getString("PRODUCT_TYPE"));
					movement.setPostingDate(resultSet.getTimestamp("POSTING_DATE"));
					movement.setNoOfNotesAttached(resultSet.getInt("NOTES_COUNT"));
					movement.setExtraText1(resultSet.getString("EXTRA_TEXT1"));
					movement.setIlmFcastStatus(resultSet.getString("ILM_FCAST_STATUS"));
					collMovementDetail.add(movement);
				}
			}

		} catch (Exception e) {
			log.error("Exception caught in " + this.getClass().getName()
					+ " - [getArchiveMovementDetailList]. Cause: "
					+ e.getMessage());
			throw SwtErrorHandler.getInstance().handleException(e,
					"getArchiveMovementDetailList", this.getClass());
		} finally {
			// edited by KaisBS for mantis 1745 : introducing the implementation
			// of the JDBCCloser class
			SwtException thrownException = null;
			SQLException sqlException = null;
			HibernateException hException = null;
			try {
				if (connection != null)
					connection.commit();
			} catch (Exception exception) {
				log.error(this.getClass().getName()
						+ " - [getArchiveMovementDetailList] - SQLException: "
						+ exception.getMessage());

				thrownException = SwtErrorHandler.getInstance()
						.handleException(exception,
								"getArchiveMovementDetailList",
								MovementDAOHibernate.class);
			}

			sqlException = JDBCCloser.close(resultSet, resultSetDbLink);
			if (thrownException == null && sqlException != null)
				thrownException = SwtErrorHandler.getInstance()
						.handleException(sqlException,
								"getArchiveMovementDetailList",
								MovementDAOHibernate.class);

			sqlException = JDBCCloser.close(statement, callableStatement);
			if (thrownException == null && sqlException != null)
				thrownException = SwtErrorHandler.getInstance()
						.handleException(sqlException,
								"getArchiveMovementDetailList",
								MovementDAOHibernate.class);

			hException = JDBCCloser.close(session);
			if (thrownException == null && hException != null)
				thrownException = SwtErrorHandler.getInstance()
						.handleException(hException,
								"getArchiveMovementDetailList",
								MovementDAOHibernate.class);

			JDBCCloser.close(connection);
			
			if (thrownException != null)
				throw thrownException;

			dbLink = null;
			log.debug(this.getClass().getName()
					+ "- [getArchiveMovementDetailList] - Exiting ");
		}
		return collMovementDetail;
	}
	/*
	 * End:Code Modified for Mantis 1270 by Chinniah on 29-Feb-2012:Data Archive
	 * setup: Remove redundant fields from Archive setup screen
	 */
	
	// Added by Meftah for Mantis 2156
	/**
	 * Gets the movement details from p_movement table by matchId
	 * This method implementation is temporary for Mantis 2156 (1054.2 & 1055) 
	 * @param String
	 *            entityId
	 * @param String
	 *            hostId
	 * @param String
	 *            movementIds
	 * @return Collection object
	 * @throws SwtException
	 */
	@SuppressWarnings("unchecked")
	public Collection getMovementDetailsByMatch(String entityId, String hostId,
			Long matchId) throws SwtException {
		// Holds the collection of movement details
		Collection collMvmntDetails = null;
		try {
			log.debug(this.getClass().getName()
					+ "- [getMovementDetailsByMatch] - Entering ");
			// Gets the collection of movement details
			collMvmntDetails = (Collection ) getHibernateTemplate().find(
					"from Movement m where m.id.hostId=?0"
							+ " and  m.id.entityId=?1"
							+ " and m.matchId=?2", new Object[]{hostId, entityId, new Long(matchId)});
			log.debug(this.getClass().getName()
					+ "- [getMovementDetailsByMatch] - Exiting ");
		} catch (DataAccessException e) {
			log.error("DataAccessException caught in "
					+ this.getClass().getName()
					+ " - [getMovementDetailsByMatch]. Cause: " + e.getMessage());
			throw SwtErrorHandler.getInstance().handleException(e,
					" getMovementDetailsByMatch ", MatchDisplayDAOHibernate.class);
		} catch (Exception e) {
			log.error("Exception caught in " + this.getClass().getName()
					+ " - [getMovementDetailsByMatch]. Cause: " + e.getMessage());
			throw SwtErrorHandler.getInstance().handleException(e,
					" getMovementDetailsByMatch ", MatchDisplayDAOHibernate.class);
		}
		return collMvmntDetails;
	}

	/**
	 * Gets the movement details from p_movement table by matchId
	 * This method implementation is temporary for Mantis 2156 (1054.2 & 1055) 
	 * @param String
	 *            entityId
	 * @param String
	 *            hostId
	 * @param String
	 *            movementIds
	 * @return Collection object
	 * @throws SwtException
	 */
	public String getMatchHash(String entityId, String hostId,
			Long matchId, String archiveId) throws SwtException {
		
		log.debug(this.getClass().getName()
				+ " - [ getMatchHash ] - Entry ");
		
		Session session = null;
		Connection conn = null;
		PreparedStatement statement = null;
		ResultSet resultSet = null;
		String matchHash = null;
		String  query = null;
		try {
			// This query is used to help determine if a match has changed.
			// The idea is to capture its 'state' before some update, and then compare its
			// new state after the update. The state is simplified to a hash code of a
			// string of notable fields that are are modified if the match changes.
			//
			// The string consists of the following fields:
			// <statusOfMatch>,<matchQuality>,<matchStatusOfMov(n)>||<Mov(n)>
			// where Mov(n) represents Movement 1,2,3,etc. in the match.
			//
			// Example of string: M,D,M1001,M1002,M1003
			// where M=Match status, D=match quality, 1001...1003 are the movements.
			//
			// This string is then hashed to give a hash code number, e.g. 1234567890
			// This hash code is used to represent the state of the match.
			
			
			
			
//			
			
			String archiveType = null;
			String archiveMatchString = "";
			String archiveMovementString = "";
			String dbLink = null;
			
			
			if(!SwtUtil.isEmptyOrNull(archiveId)) {
				String HQL_DBDETAILS = "  from Archive a where a.id.hostId = ?0 and a.moduleId = ?1 and a.defaultDb = 'Y'";
				List<Archive> dbColl = (List<Archive>) getHibernateTemplate().find(HQL_DBDETAILS,
						new Object[] { hostId, "Predict" });
				if (!dbColl.isEmpty()) {
					dbLink =  dbColl.get(0).getDb_link();
					archiveType = dbColl.get(0).getArchiveType();
					if("S".equalsIgnoreCase(archiveType)) {
						archiveMatchString=dbLink+".P_MATCH";
						archiveMovementString=dbLink+".P_MOVEMENT";
					}else {
						archiveMatchString="P_MATCH@"+dbLink;
						archiveMovementString="P_MOVEMENT@"+dbLink;
					}
				}
			}else {
				archiveMatchString = "P_MATCH";
				archiveMovementString = "P_MOVEMENT";
			}
			
			
			query = new String(
					"WITH PREFS AS (SELECT ? MATCH_ID, ',' DIV FROM DUAL),"
					   + " MOV_CSV AS (SELECT RTRIM(XMLAGG(XMLELEMENT(E,MATCH_STATUS || MOVEMENT_ID, ',').EXTRACT('//text()') ORDER BY MOVEMENT_ID).GetClobVal(),',') CSV_LIST"
					   + " FROM "+archiveMovementString + " M"
					   + " INNER JOIN PREFS ON (M.MATCH_ID = PREFS.MATCH_ID)"
					   + ")"
					   + " SELECT FN_ORA_HASH_CLOB(MAT.STATUS || DIV || MAT.MATCH_QUALITY || DIV || MOV_CSV.CSV_LIST) HASH"
					   + " FROM "+archiveMatchString + " MAT"
					   + " INNER JOIN PREFS ON (PREFS.MATCH_ID = MAT.MATCH_ID) CROSS JOIN MOV_CSV");
			
			session = getHibernateTemplate().getSessionFactory().openSession();
			conn = SwtUtil.connection(session);
			statement = conn.prepareStatement(query);
			statement.setLong(1, matchId);
			statement.execute();
			resultSet = statement.getResultSet();
			
					
			if (resultSet != null) {
				while (resultSet.next()) {
					matchHash = resultSet.getString(1);
				}
			}
		} catch (HibernateException hibernateException) {
			log.debug("Problem in accessing Hibernate properties");
			throw new SwtException(hibernateException.getMessage());
		} catch (SQLException sqlException) {
			log.debug("Problem in executing Query");
			throw new SwtException(sqlException.getMessage());
		} finally {
			
			// Edited by KaisBS for mantis 1745 : introducing the implementation of the JDBCCloser class
			SwtException thrownException = null;
			Object[] exceptions = JDBCCloser.close(resultSet, statement, conn, session);

			if (exceptions[0] != null)
			    thrownException = new SwtException(((SQLException) exceptions[0]).getMessage());

			if (thrownException == null && exceptions[1] !=null) 
			    thrownException =  new SwtException(((HibernateException) exceptions[1]).getMessage());

			if(thrownException!=null)
				throw thrownException;
			
		}

		
		log.debug("exiting  getMatchHash()");

		return matchHash;
	}

	@Override

	/**
	 * This method is used to confirm the given match
	 * 
	 * @param String
	 *            entityId
	 * @param String
	 *            hostId
	 * @param String
	 *            matchId
	 * @param String
	 *            userId
	 * @return void
	 * @throws SwtException
	 */
	public void confirmMatch(String hostId, String entityId, String matchId, String userId) throws SwtException {
		Session session = null;
		Connection conn = null;
		CallableStatement cstmt = null;
		ResultSet rs = null;

		try {
			
			log.debug(this.getClass().getName()
					+ "-[confirmMatch()]- Entr	iy");
			session = getHibernateTemplate().getSessionFactory().openSession();
			conn = SwtUtil.connection(session);
			cstmt = conn
					.prepareCall("{call PKG_MANUAL_MATCHING.sp_confirm_match(?,?,?,?)}");
			cstmt.setString(1, hostId);
			cstmt.setString(2, entityId);
			cstmt.setString(3, matchId);
			cstmt.setString(4, userId);
			cstmt.execute();
			conn.commit();
			log.debug(this.getClass().getName() + " - [confirmMatch] - "
					+ "Exit");
			
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [confirmMatch] method : - "
					+ exp.getMessage());
		} finally {
			SwtException thrownException = null;
			Object[] exceptions = JDBCCloser.close(rs, cstmt, conn, session);
			
			if (exceptions[0] != null)
				thrownException = SwtErrorHandler.getInstance().handleException((SQLException) exceptions[0], "confirmMatch",WorkflowMonitorDAOHibernate.class);
			
			if (thrownException == null && exceptions[1] !=null) 
				thrownException = SwtErrorHandler.getInstance().handleException((HibernateException) exceptions[1], "confirmMatch",WorkflowMonitorDAOHibernate.class);
			
			if (thrownException != null)
				throw thrownException;
			
		}
		
	}
	
	/**
	 * This method is used to suspend the given match
	 * 
	 * @param String
	 *            entityId
	 * @param String
	 *            hostId
	 * @param String
	 *            matchId
	 * @param String
	 *            userId
	 * @return void
	 * @throws SwtException
	 */
	public void suspendMatch(String hostId, String entityId, String matchId, String userId) throws SwtException {
		Session session = null;
		Connection conn = null;
		CallableStatement cstmt = null;
		ResultSet rs = null;

		try {
			
			log.debug(this.getClass().getName()
					+ "-[confirmMatch()]- Entr	iy");
			session = getHibernateTemplate().getSessionFactory().openSession();
			conn = SwtUtil.connection(session);
			cstmt = conn
					.prepareCall("{call PKG_MANUAL_MATCHING.sp_suspend_match(?,?,?,?)}");
			cstmt.setString(1, hostId);
			cstmt.setString(2, entityId);
			cstmt.setString(3, matchId);
			cstmt.setString(4, userId);
			cstmt.execute();
			conn.commit();
			log.debug(this.getClass().getName() + " - [suspendMatch] - "
					+ "Exit");
			
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [suspendMatch] method : - "
					+ exp.getMessage());
		} finally {
			SwtException thrownException = null;
			Object[] exceptions = JDBCCloser.close(rs, cstmt, conn, session);
			
			if (exceptions[0] != null)
				thrownException = SwtErrorHandler.getInstance().handleException((SQLException) exceptions[0], "suspendMatch",WorkflowMonitorDAOHibernate.class);
			
			if (thrownException == null && exceptions[1] !=null) 
				thrownException = SwtErrorHandler.getInstance().handleException((HibernateException) exceptions[1], "confirmMatch",WorkflowMonitorDAOHibernate.class);
			
			if (thrownException != null)
				throw thrownException;
			
		}
		
	}
	
	
	/**
	 * This method is used to reconcile the given match
	 * 
	 * @param String
	 *            entityId
	 * @param String
	 *            hostId
	 * @param String
	 *            matchId
	 * @param String
	 *            userId
	 * @return void
	 * @throws SwtException
	 */
	public void reconcileMatch(String hostId, String entityId, String matchId, String userId) throws SwtException {
		Session session = null;
		Connection conn = null;
		CallableStatement cstmt = null;
		ResultSet rs = null;

		try {
			
			log.debug(this.getClass().getName()
					+ "-[reconcileMatch()]- Entr	iy");
			session = getHibernateTemplate().getSessionFactory().openSession();
			conn = SwtUtil.connection(session);
			cstmt = conn
					.prepareCall("{call PKG_MANUAL_MATCHING.sp_reconcile_match(?,?,?,?)}");
			cstmt.setString(1, hostId);
			cstmt.setString(2, entityId);
			cstmt.setString(3, matchId);
			cstmt.setString(4, userId);
			cstmt.execute();
			conn.commit();
			log.debug(this.getClass().getName() + " - [reconcileMatch] - "
					+ "Exit");
			
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [reconcileMatch] method : - "
					+ exp.getMessage());
		} finally {
			SwtException thrownException = null;
			Object[] exceptions = JDBCCloser.close(rs, cstmt, conn, session);
			
			if (exceptions[0] != null)
				thrownException = SwtErrorHandler.getInstance().handleException((SQLException) exceptions[0], "reconcileMatch",WorkflowMonitorDAOHibernate.class);
			
			if (thrownException == null && exceptions[1] !=null) 
				thrownException = SwtErrorHandler.getInstance().handleException((HibernateException) exceptions[1], "reconcileMatch",WorkflowMonitorDAOHibernate.class);
			
			if (thrownException != null)
				throw thrownException;
			
		}
		
	}
	
	
	/**
	 * This method is used to delete the given match
	 * 
	 * @param String
	 *            entityId
	 * @param String
	 *            hostId
	 * @param String
	 *            matchId
	 * @param String
	 *            userId
	 * @return void
	 * @throws SwtException
	 */
	public void deleteMatch(String hostId, String entityId, String matchId, String userId) throws SwtException {
		Session session = null;
		Connection conn = null;
		CallableStatement cstmt = null;
		ResultSet rs = null;

		try {
			
			log.debug(this.getClass().getName()
					+ "-[deleteMatch()]- Entr	iy");
			session = getHibernateTemplate().getSessionFactory().openSession();
			conn = SwtUtil.connection(session);
			cstmt = conn
					.prepareCall("{call PKG_MANUAL_MATCHING.sp_delete_match(?,?,?,?)}");
			cstmt.setString(1, hostId);
			cstmt.setString(2, entityId);
			cstmt.setString(3, matchId);
			cstmt.setString(4, userId);
			cstmt.execute();
			conn.commit();
			log.debug(this.getClass().getName() + " - [deleteMatch] - "
					+ "Exit");
			
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [deleteMatch] method : - "
					+ exp.getMessage());
		} finally {
			SwtException thrownException = null;
			Object[] exceptions = JDBCCloser.close(rs, cstmt, conn, session);
			
			if (exceptions[0] != null)
				thrownException = SwtErrorHandler.getInstance().handleException((SQLException) exceptions[0], "deleteMatch",WorkflowMonitorDAOHibernate.class);
			
			if (thrownException == null && exceptions[1] !=null) 
				thrownException = SwtErrorHandler.getInstance().handleException((HibernateException) exceptions[1], "deleteMatch",WorkflowMonitorDAOHibernate.class);
			
			if (thrownException != null)
				throw thrownException;
			
		}
		
	}
	
	
	/**
	 * This method is used to delete the given match
	 * 
	 * @param String
	 *            entityId
	 * @param String
	 *            hostId
	 * @param String
	 *            matchId
	 * @param String
	 *            userId
	 * @return void
	 * @throws SwtException
	 */
	public void addMovementToMatch(String entityId,String listMovIds, String matchStatus,String matchId, String userId) throws SwtException {
		Session session = null;
		Connection conn = null;
		CallableStatement cstmt = null;
		ResultSet rs = null;

		try {
			
			log.debug(this.getClass().getName()
					+ "-[addMovementToMatch()]- Entr	iy");
			session = getHibernateTemplate().getSessionFactory().openSession();
			conn = SwtUtil.connection(session);
			cstmt = conn
					.prepareCall("{call PKG_MANUAL_MATCHING.sp_add_movements(?,?,?,?,?)}");
			cstmt.setString(1, entityId);
			cstmt.setString(2, listMovIds);
			cstmt.setString(3, matchStatus);
			cstmt.setString(4, matchId);
			cstmt.setString(5, userId);
			cstmt.execute();
			conn.commit();
			log.debug(this.getClass().getName() + " - [addMovementToMatch] - "
					+ "Exit");
			
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [addMovementToMatch] method : - "
					+ exp.getMessage());
		} finally {
			SwtException thrownException = null;
			Object[] exceptions = JDBCCloser.close(rs, cstmt, conn, session);
			
			if (exceptions[0] != null)
				thrownException = SwtErrorHandler.getInstance().handleException((SQLException) exceptions[0], "addMovementToMatch",WorkflowMonitorDAOHibernate.class);
			
			if (thrownException == null && exceptions[1] !=null) 
				thrownException = SwtErrorHandler.getInstance().handleException((HibernateException) exceptions[1], "addMovementToMatch",WorkflowMonitorDAOHibernate.class);
			
			if (thrownException != null)
				throw thrownException;
			
		}
		
	}
	
	/**
	 * This method is used to remove movement from match
	 * 
	 * 
	 * @param String
	 *            entityId
	 * @param String
	 *            hostId
	 * @param String
	 *            matchId
	 * @param String
	 *            userId
	 * @return void
	 * @throws SwtException
	 */
	public void removeMovementFromMatch(String hostId, String entityId,String listMovIds, String matchStatus,String matchId, String userId) throws SwtException {
		Session session = null;
		Connection conn = null;
		CallableStatement cstmt = null;
		ResultSet rs = null;

		try {
			
			log.debug(this.getClass().getName()
					+ "-[addMovementToMatch()]- Entr	iy");
			session = getHibernateTemplate().getSessionFactory().openSession();
			conn = SwtUtil.connection(session);
			cstmt = conn
					.prepareCall("{call PKG_MANUAL_MATCHING.sp_remove_movements(?,?,?,?,?,?)}");
			cstmt.setString(1, hostId);
			cstmt.setString(2, entityId);
			cstmt.setString(3, listMovIds);
			cstmt.setString(4, matchStatus);
			cstmt.setString(5, matchId);
			cstmt.setString(6, userId);
			cstmt.execute();
			conn.commit();
			log.debug(this.getClass().getName() + " - [addMovementToMatch] - "
					+ "Exit");
			
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [addMovementToMatch] method : - "
					+ exp.getMessage());
		} finally {
			SwtException thrownException = null;
			Object[] exceptions = JDBCCloser.close(rs, cstmt, conn, session);
			
			if (exceptions[0] != null)
				thrownException = SwtErrorHandler.getInstance().handleException((SQLException) exceptions[0], "addMovementToMatch",WorkflowMonitorDAOHibernate.class);
			
			if (thrownException == null && exceptions[1] !=null) 
				thrownException = SwtErrorHandler.getInstance().handleException((HibernateException) exceptions[1], "addMovementToMatch",WorkflowMonitorDAOHibernate.class);
			
			if (thrownException != null)
				throw thrownException;
			
		}
		
	}
	
	public Integer getMatchCount(String hostId, String entityId, String matchId) throws SwtException{
		Session session = null;
		Connection conn = null;
		CallableStatement cstmt = null;
		ResultSet rs = null;
		Integer result = null;
		try {
			// log debug message
			log.debug(this.getClass().getName()
					+ " - [getDataState] - Enter");

			// Get the hibernate session
			session = getHibernateTemplate().getSessionFactory().openSession();
			// Get the database connection object
			conn = SwtUtil.connection(session);
			// Make a callable statement for executing the procedure
			cstmt = conn.prepareCall("select count(*) from p_movement where host_id = ? and entity_id = ? and  match_id=?");
			
			cstmt.setString(1, hostId);
			cstmt.setString(2, entityId);
			cstmt.setString(3, matchId);
			
			rs= cstmt.executeQuery();
			rs.next();
			
			result = rs.getInt(1);
			
		}catch (HibernateException e) {
				log.error("HibernateException caught in " + this.getClass().getName()
						+ "-[getDataState]-" + e.getMessage());
				throw SwtErrorHandler.getInstance().handleException(e, "getDataState",
						ILMAnalysisMonitorDAOHibernate.class);
				
			} catch (SQLException e) {
				log.error("SQLException caught in " + this.getClass().getName()
						+ "-[getDataState]-" + e.getMessage());
				throw SwtErrorHandler.getInstance().handleException(e, "getDataState",
						ILMAnalysisMonitorDAOHibernate.class);
				
			} finally {
				JDBCCloser.close(rs, cstmt, conn, session);		
				// nullify objects
				session = null;
				conn = null;
				cstmt = null;
				rs = null;
			}
			return result;
		}
	
}