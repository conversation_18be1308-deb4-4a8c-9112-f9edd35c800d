/*
 * @(#)MovementDAOHibernate.java 1.0 03/01/06
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.work.dao.hibernate;

import java.sql.CallableStatement;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.sql.Types;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map.Entry;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

import org.swallow.batchScheduler.ConnectionManager;
import org.swallow.control.model.WorkQAccess;
import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.maintenance.model.Currency;
import org.swallow.maintenance.model.CurrencyExchange;
import org.swallow.maintenance.model.CurrencyMaster;
import org.swallow.maintenance.model.Entity;
import org.swallow.maintenance.model.EntityPositionLevel;
import org.swallow.maintenance.model.MiscParams;
import org.swallow.maintenance.model.MsdDisplayColumns;
import org.swallow.maintenance.model.Party;
import org.swallow.model.ScreenInfo;
import org.swallow.util.CacheManager;
import org.swallow.util.CommonDataManager;
import org.swallow.util.JDBCCloser;
import org.swallow.util.SwtConstants;
import org.swallow.util.SwtDataSource;
import org.swallow.util.SwtInterceptor;
import org.swallow.util.SwtUtil;
import org.swallow.util.UserThreadLocalHolder;
import org.swallow.work.dao.MovementDAO;
import org.swallow.work.model.CrossReference;
import org.swallow.work.model.Match;
import org.swallow.work.model.MatchDriver;
import org.swallow.work.model.MatchNote;
import org.swallow.work.model.Movement;
import org.swallow.work.model.MovementExt;
import org.swallow.work.model.MovementMessage;
import org.swallow.work.model.MovementNote;
import org.swallow.work.model.MsdAdditionalColumns;
import org.springframework.context.annotation.Lazy;
import org.springframework.orm.hibernate5.support.HibernateDaoSupport;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.stereotype.Repository;
import org.springframework.beans.factory.annotation.Autowired;
import org.hibernate.HibernateException;
import org.hibernate.SessionFactory;
import org.hibernate.Transaction;
import org.hibernate.Session;





/**
 * <AUTHOR>
 * <AUTHOR> Tripathi has modified this class file
 *
 * <Pre>
 * DAO layer for Movement
 *
 * Method to used to 
 * - Display Movement details
 * - Get Movement details
 * - save Movement
 * - Update Movement
 * - Save Movement
 *
 * </Pre>
 */
@Repository ("movementDAO")
@Transactional
public class MovementDAOHibernate extends HibernateDaoSupport implements
		MovementDAO {
	public MovementDAOHibernate(@Lazy SessionFactory sessionfactory) {
		setSessionFactory(sessionfactory);
	}

	/**
	 * DOCUMENT ME!
	 */
	private static final Log log = LogFactory
			.getLog(MovementDAOHibernate.class);

	/**
	 * DOCUMENT ME!
	 */
	private static final String HQL_CURRENCYLIST = "from Currency c where c.id.entityId =?0 and c.id.hostId=?1 and c.id.currencyCode!='*' order by c.id.currencyCode";

	/**
	 * DOCUMENT ME!
	 */
	private static final String HQL_CCYLIST = "from Currency c where  c.id.hostId=?0 and c.id.entityId =?1 and c.currencyGroupId =?2 and c.id.currencyCode!='*' order by c.priorityOrder,c.id.currencyCode";

	/**
	 * DOCUMENT ME!
	 */
	private static final String HQL_CCYLIST_CCYGRPALL = "from Currency c where  c.id.hostId=?0 and c.id.entityId =?1  and c.id.currencyCode!='*' order by c.priorityOrder, c.id.currencyCode";

	/**
	 * DOCUMENT ME!
	 */
	private static final String HQL_CURRENCY = "from CurrencyMaster c where c.currencyCode=?0";

	/**
	 * DOCUMENT ME!
	 */
	private static final String HQL_ENTITYDETAILS = "from Entity c where c.id.hostId=?0 and c.id.entityId=?1";

	/**
	 * DOCUMENT ME!
	 */
	private static final String HQL_CURRENCYDETAILS = "from Currency c where c.id.hostId=?0 and c.id.entityId=?1 and c.id.currencyCode=?2";

	/**
	 * DOCUMENT ME!
	 */
	private static final String HQL_CURRENCYNAME = "from CurrencyMaster c where c.currencyCode=?0";

	/**
	 * DOCUMENT ME!
	 */
	private static final String HQL_OUTSTANDINGSTATUSROLE = "from WorkQAccess w where w.id.hostId=?0 and w.id.entityId=?1 and w.id.currencyCode=?2 and w.id.roleId=?3";

	/**
	 * DOCUMENT ME!
	 */
	private static final String HQL_POSITIONLEVEL = " from Movement p where p.id.hostId=?0 and p.id.entityId=?1 and p.id.movementId=?2";

	/* Entries for movement screen */

	/**
	 * DOCUMENT ME!
	 */
	private static final String HOL_MOVEMENTDETAILS = "from Movement p where p.id.hostId=?0 and p.id.movementId=?1 ";

	/**
	 * DOCUMENT ME!
	 */
	private static final String HQL_ENTITYNAME = "from Entity p where p.id.hostId=?0 and p.id.entityId=?1 ";

	/**
	 * DOCUMENT ME!
	 */
	private static final String HQL_BOOKCODELIST = "from BookCode p where p.id.hostId=?0 and p.id.entityId=?1 order by p.id.bookCode";

	/**
	 * DOCUMENT ME!
	 */
	private static final String HQL_PARTYLIST = "from Party p where p.id.hostId=?0 and p.id.entityId=?1 order by p.id.partyId";

	/**
	 * DOCUMENT ME!
	 */
	private static final String HQL_MATCHDRIVERLIST = "from MatchDriver m where m.id.hostId=?0";

	/**
	 * DOCUMENT ME!
	 */
	private static final String HQL_PARTYRECORD = "from Party p where p.id.hostId=?0 and p.id.entityId=?1  and  p.id.partyId =?2";

	/**
	 * DOCUMENT ME!
	 */
	private static final String HQL_NOTELIST = "from MovementNote m where m.id.hostId=?0 and m.id.movementId=?1 ";

	private static final String HQL_CROSS_REFERENCELIST = "from CrossReference cr where cr.hostId=?0 and cr.entityId=?1 and cr.movementId=?2 ";

	private static final String HOL_MOVEMENT_EXT_DETAILS = "from MovementExt p where p.id.hostId=?0 and p.id.movementId=?1 ";


	/**
	 * @param entityId
	 * @param hostId
	 * @return
	 * @throws SwtException
	 */
	public List getCurrencyList(String entityId, String hostId)
			throws SwtException {
		log.debug("Entering getCurrencyList method");

		java.util.List currList = getHibernateTemplate().find(HQL_CURRENCYLIST,
				new Object[] { entityId, hostId });
		log.debug("Exiting getCurrencyList method");

		return currList;
	}

	/**
	 * @param hostId
	 * @param entityId
	 * @param currencyCode
	 * @return
	 * @throws SwtException
	 */
	public String getCurrency(String currencyCode) throws SwtException {
		log.debug("Entering getCurrency method");

		CurrencyMaster currency = new CurrencyMaster();
		List currencyList = getHibernateTemplate().find(HQL_CURRENCY,
				new Object[] { currencyCode });
		currency = (CurrencyMaster) currencyList.get(0);
		log.debug("Exiting getCurrency method");

		return currency.getCurrencyName();
	}

	/**
	 * @param hostId
	 * @param entityId
	 * @return
	 * @throws SwtException
	 */
	public Entity getEntityDetail(String hostId, String entityId)
			throws SwtException {
		log.debug("Entering getEntityDetail method");

		Entity entity = new Entity();
		List entityList = (List ) getHibernateTemplate().find(HQL_ENTITYDETAILS,
				new Object[] { hostId, entityId });
		entity = (Entity) entityList.get(0);
		log.debug("Exiting getEntityDetail method");

		return entity;
	}

	/**
	 * @param hostId
	 * @param entityId
	 * @param currencyId
	 * @return
	 * @throws SwtException
	 */
	public Currency getCurrencyDetail(String hostId, String entityId,
									  String currencyId) throws SwtException {
		log.debug("Entering getCurrencyDetail method");

		Currency currency = new Currency();
		List currencyList = getHibernateTemplate().find(HQL_CURRENCYDETAILS,
				new Object[] { hostId, entityId, currencyId });
		currency = (Currency) currencyList.get(0);
		log.debug("Exiting getCurrencyDetail method");

		return currency;
	}

	public int getOutMovbyposlevbydate(String hostId, String entityId,
									   String currencyCode, Date date, Integer positionLevel, boolean isAll)
			throws SwtException {
		log.debug("Entering getOutMovbyposlevbydate method::CurrencyCode===>"
				+ currencyCode);

		List outstandingMovementCollpos = null;
		List entityColl = null;

		if (isAll) {
			outstandingMovementCollpos = (List ) getHibernateTemplate().find(
					"from Movement p "
							+ "where  p.id.hostId=?0 and p.id.entityId=?1 "
							+ "and p.currencyCode=?2 and p.matchStatus='L' and p.predictStatus != 'C' "
							+ "and p.positionLevel=?3 and p.valueDate >= ?4",
					new Object[] { hostId, entityId, currencyCode,
							positionLevel, date });
		} else {
			outstandingMovementCollpos = (List ) getHibernateTemplate().find(
					"from Movement p "
							+ "where  p.id.hostId=?0 and p.id.entityId=?1 and p.currencyCode=?2 "
							+ "and p.matchStatus='L' and p.predictStatus != 'C' and p.positionLevel=?3 "
							+ "and p.valueDate = ?4",
					new Object[] { hostId, entityId, currencyCode,
							positionLevel, date });
		}

		/*
		 * Checking for Movement with Amount greater than cash/securities
		 * threshold
		 */
		Entity ent = new Entity();
		ent = getEntityDetail(hostId, entityId);

		Currency curr = new Currency();
		curr = getCurrencyDetail(hostId, entityId, currencyCode);

		List finaloutstandingMovement = new ArrayList();
		Iterator movCollIterator = outstandingMovementCollpos.iterator();
		Movement mov = new Movement();

		while (movCollIterator.hasNext()) {
			mov = (Movement) movCollIterator.next();

			double factor = 1.0;

			if (ent.getExchangeRateFormat() != null) {
				if (ent.getExchangeRateFormat().equalsIgnoreCase("1")) {
					factor = curr.getExchangeRate().doubleValue();
				} else if ((ent.getExchangeRateFormat().equalsIgnoreCase("2"))) {
					factor = factor / (curr.getExchangeRate().doubleValue());
				}
			}

			if (mov.getMovementType().equalsIgnoreCase("C")) {
				if (((mov.getAmount()).doubleValue() * factor) >= (ent
						.getCashFilterThreshold()).doubleValue()) {
					finaloutstandingMovement.add(mov);
				}
			} else if (mov.getMovementType().equalsIgnoreCase("U")) {
				if (((mov.getAmount()).doubleValue() * factor) >= (ent
						.getSecuritiesFilterThreshold()).doubleValue()) {
					finaloutstandingMovement.add(mov);
				}
			}
		}

		log.debug("Exiting getOutMovbyposlevbydate method::");

		return finaloutstandingMovement.size();
	}

	public int getOutMovbyposlevinterimbydate(String hostId, String entityId,
											  String currencyCode, Date date, Integer positionLevelFirst,
											  Integer positionLevelFinal, boolean isAll) throws SwtException {
		log
				.debug("Entering getOutMovbyposlevinterimbydate method===>currencyCode::"
						+ currencyCode);

		List outMovCollposinterim = null;
		List entityColl = null;

		if (isAll) {
			outMovCollposinterim = (List ) getHibernateTemplate().find(
					"from Movement p "
							+ "where  p.id.hostId=?0 and p.id.entityId=?1 and p.currencyCode=?2 "
							+ "and p.matchStatus='L'and p.predictStatus !='C' and p.valueDate >= ?3 and p.positionLevel != ?4 and p.positionLevel != ?5",
					new Object[] { hostId, entityId, currencyCode,
							date, positionLevelFirst,
							positionLevelFinal });
		} else {
			outMovCollposinterim = (List ) getHibernateTemplate().find(
					"from Movement p where  p.id.hostId=?0 and p.id.entityId=?1 "
							+ "and p.currencyCode=?2 and p.matchStatus='L' and p.predictStatus !='C' "
							+ "and p.valueDate = ?3 and p.positionLevel != ?4 and p.positionLevel != ?5",
					new Object[] { hostId, entityId, currencyCode,
							date, positionLevelFirst,
							positionLevelFinal });
		}

		/*
		 * Checking for Movement with Amount greater than cash/securities
		 * threshold
		 */
		Entity ent = new Entity();
		ent = getEntityDetail(hostId, entityId);

		Currency curr = new Currency();
		curr = getCurrencyDetail(hostId, entityId, currencyCode);

		List finaloutstandingInterimMovement = new ArrayList();
		Iterator movCollIterator = outMovCollposinterim.iterator();
		Movement mov = new Movement();

		while (movCollIterator.hasNext()) {
			mov = (Movement) movCollIterator.next();

			double factor = 1.0;

			if (ent.getExchangeRateFormat() != null) {
				if (ent.getExchangeRateFormat().equalsIgnoreCase("1")) {
					factor = curr.getExchangeRate().doubleValue();
				} else if ((ent.getExchangeRateFormat().equalsIgnoreCase("2"))) {
					factor = factor / (curr.getExchangeRate().doubleValue());
				}
			}

			if (mov.getMovementType().equalsIgnoreCase("C")) {
				if (((mov.getAmount()).doubleValue() * factor) >= (ent
						.getCashFilterThreshold()).doubleValue()) {
					finaloutstandingInterimMovement.add(mov);
				}
			} else if (mov.getMovementType().equalsIgnoreCase("U")) {
				if (((mov.getAmount()).doubleValue() * factor) >= (ent
						.getSecuritiesFilterThreshold()).doubleValue()) {
					finaloutstandingInterimMovement.add(mov);
				}
			}
		}

		return finaloutstandingInterimMovement.size();
	} // End of getOutMovbyposlevinterimbydate method

	/**
	 * @param hostId
	 * @param entityId
	 * @param currCode
	 * @param roleId
	 * @return
	 * @throws SwtException
	 */
	public String getOutStandingStatusbyRole(String hostId, String entityId,
											 String currCode, String roleId) throws SwtException {
		log.debug("Entering getOutStandingStatusbyRole method===> RoleID"
				+ roleId);

		List outStatusRoleList = getHibernateTemplate().find(
				HQL_OUTSTANDINGSTATUSROLE,
				new Object[] { hostId, entityId, currCode, roleId });
		WorkQAccess workQAccess = new WorkQAccess();

		if (outStatusRoleList.size() == 0) {
			return "N";
		} else {
			workQAccess = (WorkQAccess) outStatusRoleList.get(0);

			if (workQAccess.getOutstanding() != null) {
				log.debug("Exiting getOutStandingStatusbyRole method");

				return workQAccess.getOutstanding();
			} else {
				log.debug("Exiting getOutStandingStatusbyRole method");

				return "N";
			}
		}
	}

	/**
	 * This method is used to get movement details
	 *
	 * @param hostId
	 * @param entityId
	 * @param movementId
	 * @return
	 * @throws SwtException
	 */
	@SuppressWarnings("unchecked")
	public Movement getMovement(String hostId, String entityId, Long movementId)
			throws SwtException {
		// Setting movementList in List
		List movementList = null;
		// variable declaration for movement
		Movement movement = null;
		try {
			log.debug(this.getClass().getName() + " - [getMovement] Enters");
			// get the movement list form data base
			movementList = (List ) getHibernateTemplate().find(HQL_POSITIONLEVEL,
					new Object[] { hostId, entityId, movementId });
			// checks the movement list not null and not empty
			if (movementList != null && !movementList.isEmpty()) {
				movement = (Movement) movementList.get(0);
			}
			log.debug(this.getClass().getName() + " - [getMovement] Ends");
		} catch (Exception e) {
			log.error("Exception caught at " + this.getClass().getName()
					+ " - [getMovement]. Cause: " + e.getMessage());
			throw SwtErrorHandler.getInstance().handleException(e,
					"getMovement", MovementDAOHibernate.class);
		} finally {
			// Cleaning un-referrenced objects
			movementList = null;
		}
		return movement;
	}

	/**
	 * Updates all the movements in the list in one strock
	 *
	 * @param movColl
	 * @throws SwtException
	 * <AUTHOR> 29th Jan 08
	 */
	public void updateMovements(Collection movColl) throws SwtException {
		Iterator movItr = movColl.iterator();
		Movement movement = new Movement();
		// Holds the hibernate session instance
		Session session = null;
		// Holds the hibernate Transaction instance
		Transaction transaction = null;
		SwtInterceptor interceptor = null;
		try {
			interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");
			session = getHibernateTemplate()
					.getSessionFactory()
					.withOptions().interceptor(interceptor).openSession();
			transaction = session.beginTransaction();
			while (movItr.hasNext()) {
				movement = (Movement) movItr.next();
				session.update(movement);
			}
			transaction.commit();
			session.close();
		} catch (Exception exception) {
			log.error("Exception caught at " + this.getClass().getName()
					+ " - [updateMovements]. Cause: " + exception.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exception,
					"updateMovements", MovementDAOHibernate.class);
		}

	}

	private void batchUpdateMovement (Collection<Movement> movListToUpdate) throws SwtException {

		PreparedStatement pstmt = null;
		Connection conn = null;
		Session session = null;
//		DataSource dataSource = (DataSource) SwtUtil.getBean("dataSource");
		try {
//		conn = dataSource.getConnection();
//		conn.setAutoCommit(false);

			session = getHibernateTemplate().getSessionFactory().openSession();
			conn = SwtUtil.connection(session);

			int countUpdate = 0;
			if(!movListToUpdate.isEmpty()) {
				String query = "UPDATE P_MOVEMENT SET CURRENCY_CODE = ?, BOOKCODE = ?, VALUE_DATE = ?, AMOUNT = ?, SIGN = ?, MOVEMENT_TYPE = ?, ACCOUNT_ID = ?, REFERENCE1 = ?, REFERENCE2 = ?, REFERENCE3 = ?, REFERENCE4 = ?,"
						+ " COUNTERPARTY_ID = ?, COUNTERPARTY_TEXT1 = ?, COUNTERPARTY_TEXT2 = ?, COUNTERPARTY_TEXT3 = ?, COUNTERPARTY_TEXT4 = ?, COUNTERPARTY_TEXT5 = ?, BENEFICIARY_ID = ?, BENEFICIARY_TEXT1 = ?, BENEFICIARY_TEXT2 = ?,"
						+ " BENEFICIARY_TEXT3 = ?, BENEFICIARY_TEXT4 = ?, BENEFICIARY_TEXT5 = ?, CUSTODIAN_ID = ?, CUSTODIAN_TEXT1 = ?, CUSTODIAN_TEXT2 = ?, CUSTODIAN_TEXT3 = ?, CUSTODIAN_TEXT4 = ?, CUSTODIAN_TEXT5 = ?, BOOKCODE_AVAIL = ?,"
						+ " POSITION_LEVEL = ?, PREDICT_STATUS = ?, EXTRACT_STATUS = ?, MATCH_ID = ?, MATCH_STATUS = ?, UPDATE_DATE = ?, UPDATE_USER = ?, INPUT_DATE = ?, INPUT_SOURCE = ?, MESSAGE_ID = ?, MESSAGE_FORMAT = ?, INITIAL_PREDICT_STATUS = ?,"
						+ " INPUT_USER = ?, INPUT_ROLE = ?, NOTES_COUNT = ?, OPEN = ?, EXT_BAL_STATUS = ? ,"
						+ "TO_MATCH = ?, MATCHING_PARTY = ?, PRODUCT_TYPE = ?, POSTING_DATE = ?, SETTLEMENT_DATETIME = ?, EXPECTED_SETTLEMENT_DATETIME = ?, CRITICAL_PAYMENT_TYPE = ?, ORDERING_CUSTOMER = ?, ORDERING_INSTITUTION = ?, SENDERS_CORRES = ?, "
						+ "RECEIVERS_CORRES = ?, INTMDRY_INSTITUTION_ID = ?, ACC_WITH_INSTITUTION_ID = ?, BENEFICIARY_CUST = ?, ILM_FCAST_STATUS = ? WHERE HOST_ID = ? AND ENTITY_ID = ? AND MOVEMENT_ID = ?";
				pstmt = conn.prepareStatement(query);
				for (Movement movement : movListToUpdate) {
					pstmt.setString(1, movement.getCurrencyCode());
					pstmt.setString(2, movement.getBookCode());
					pstmt.setDate(3, movement.getValueDate() == null ? null : new java.sql.Date(movement.getValueDate().getTime()));

					if(movement.getAmount() != null)
						pstmt.setDouble(4, movement.getAmount());
					else
						pstmt.setNull(4, Types.NUMERIC);

					pstmt.setString(5, movement.getSign());
					pstmt.setString(6, movement.getMovementType());
					pstmt.setString(7, movement.getAccountId());
					pstmt.setString(8, movement.getReference1());
					pstmt.setString(9, movement.getReference2());
					pstmt.setString(10, movement.getReference3());
					pstmt.setString(11, movement.getReference4());
					pstmt.setString(12, movement.getCounterPartyId());
					pstmt.setString(13, movement.getCounterPartyText1());
					pstmt.setString(14, movement.getCounterPartyText2());
					pstmt.setString(15, movement.getCounterPartyText3());
					pstmt.setString(16, movement.getCounterPartyText4());
					pstmt.setString(17, movement.getCounterPartyText5());
					pstmt.setString(18, movement.getBeneficiaryId());
					pstmt.setString(19, movement.getBeneficiaryText1());
					pstmt.setString(20, movement.getBeneficiaryText2());
					pstmt.setString(21, movement.getBeneficiaryText3());
					pstmt.setString(22, movement.getBeneficiaryText4());
					pstmt.setString(23, movement.getBeneficiaryText5());
					pstmt.setString(24, movement.getCustodianId());
					pstmt.setString(25, movement.getCustodianText1());
					pstmt.setString(26, movement.getCustodianText2());
					pstmt.setString(27, movement.getCustodianText3());
					pstmt.setString(28, movement.getCustodianText4());
					pstmt.setString(29, movement.getCustodianText5());
					pstmt.setString(30, movement.getBookCodeAvail());
					pstmt.setInt(31, movement.getPositionLevel());
					pstmt.setString(32, movement.getPredictStatus());
					pstmt.setString(33, movement.getExtractStatus());
					if(movement.getMatchId() != null)
						pstmt.setLong(34, movement.getMatchId());
					else
						pstmt.setNull(34, Types.NUMERIC);
					pstmt.setString(35, movement.getMatchStatus());
					pstmt.setDate(36, movement.getUpdateDate() == null ? null : new java.sql.Date(movement.getUpdateDate().getTime()));
					pstmt.setString(37, movement.getUpdateUser());
					pstmt.setDate(38, movement.getInputDate() == null ? null : new java.sql.Date(movement.getInputDate().getTime()));
					pstmt.setString(39, movement.getInputSource());
					pstmt.setString(40, movement.getMessageId());
					pstmt.setString(41, movement.getMessageFormat());
					pstmt.setString(42, movement.getInitialPredStatus());
					pstmt.setString(43, movement.getInputUser());
					pstmt.setString(44, movement.getInputRole());

					if(movement.getNotesCount() != null)
						pstmt.setInt(45, movement.getNotesCount());
					else
						pstmt.setNull(45, Types.NUMERIC);

					pstmt.setString(46, movement.getOpenFlag());
					pstmt.setString(47, movement.getExtBalStatus());

					pstmt.setString(48, movement.getToMatch());
					pstmt.setString(49, movement.getMatchingParty());
					pstmt.setString(50, movement.getProductType());
					pstmt.setDate(51, movement.getPostingDate() != null ? new java.sql.Date(movement.getPostingDate().getTime()) : null);
					pstmt.setDate(52, movement.getSettlementDateTime() != null ? new java.sql.Date(movement.getSettlementDateTime().getTime()) : null);
					pstmt.setDate(53, movement.getExpectedSettlementDateTime() != null ? new java.sql.Date(movement.getExpectedSettlementDateTime().getTime()) : null);
					pstmt.setString(54, movement.getCriticalPaymentType());
					pstmt.setString(55, movement.getOrderingCustomerId());
					pstmt.setString(56, movement.getOrderingInstitutionId());
					pstmt.setString(57, movement.getSenderCorrespondentId());
					pstmt.setString(58, movement.getReceiverCorrespondentId());
					pstmt.setString(59, movement.getIntermediaryInstitutionId());
					pstmt.setString(60, movement.getAccountWithInstitutionId());
					pstmt.setString(61, movement.getBeneficiaryCustomerId());
					pstmt.setString(62, movement.getIlmFcastStatus());

					pstmt.setString(63, movement.getId().getHostId());
					pstmt.setString(64, movement.getId().getEntityId());
					pstmt.setLong(65, movement.getId().getMovementId());

					pstmt.addBatch();

					countUpdate++;
					if (countUpdate % 100 == 0) { // execute batch every 200 records
						pstmt.executeBatch();
					}
				}
				// execute the remaining batch
				pstmt.executeBatch();
				conn.commit();
			}
		} catch (Exception e) {
			e.printStackTrace();
			log.error("Exception caught in " + this.getClass().getName()
					+ " - [doDatabaseOperation]. Cause: " + e.getMessage());
			throw SwtErrorHandler.getInstance().handleException(e,
					"doDatabaseOperation", MovementDAOHibernate.class);
		} finally {
			JDBCCloser.close(null, pstmt, conn, session);
			// Cleaning unreferenced objects
		}

	}

	/**
	 * @desc
	 * @param hostId
	 * @param entityId
	 * @param movementIds
	 * @return List
	 * @throws SwtException
	 */
	public List getMovement(String hostId, String entityId, String movementIds)
			throws SwtException {
		List movementList = (List ) getHibernateTemplate().find(
				"from Movement m where m.id.hostId=?0"
						+ " and m.id.entityId=?1"
						+ " and m.id.movementId in ("
						+ movementIds
						+ ") order by m.positionLevel desc,m.bookCodeAvail desc",
				new Object[] { hostId, entityId });
		String dateFormat = SwtUtil.getCurrentDateFormat(UserThreadLocalHolder
				.getUserSession());
		SimpleDateFormat sdf = new SimpleDateFormat("HH:mm:ss");
		for (int i = 0; i < movementList.size(); i++) {
			Movement mvmnt = (Movement) movementList.get(i);
			mvmnt.setUpdateDateAsString(mvmnt.getUpdateDate() == null ? "" : SwtUtil.formatDate(mvmnt
					.getUpdateDate(), dateFormat)
					+ " " + sdf.format(mvmnt.getUpdateDate()));

			mvmnt.setExpectedSettlementDateTimeAsString(mvmnt.getExpectedSettlementDateTime()==null?"":SwtUtil.formatDate(mvmnt
					.getExpectedSettlementDateTime(), dateFormat)
					+ " " + sdf.format(mvmnt.getExpectedSettlementDateTime()));

			mvmnt.setSettlementDateTimeAsString(mvmnt.getSettlementDateTime() == null? "" : SwtUtil.formatDate(mvmnt
					.getSettlementDateTime(), dateFormat)
					+ " " + sdf.format(mvmnt.getSettlementDateTime()));
		}

		return movementList;
	}

	/**
	 * @param match
	 * @return
	 * @throws SwtException
	 */
	public Long createMatch(Match match) throws SwtException {
		log.debug("Entering createMatch method ");
		// Holds the hibernate session instance
		Session session = null;
		// Holds the hibernate Transaction instance
		Transaction transaction = null;
		SwtInterceptor interceptor = null;
		interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");
		session = getHibernateTemplate()
				.getSessionFactory()
				.withOptions().interceptor(interceptor).openSession();
		transaction = session.beginTransaction();
		session.save(match);
		log.debug(" Exiting createMatch method");
		transaction.commit();
		session.close();
		return match.getId().getMatchId();
	} // End of createMatch() method

	/**
	 * This method used to get the match quality from the p_match table
	 *
	 * @param matchId
	 * @param hostId
	 * @param entityId
	 * @throws SwtException
	 */

	public String getMatchQuality(Long matchId, String hostId, String entityId)
			throws SwtException {
		log.debug("Inside MovementDAOHibernate ---Entering getMatchQuality Mehtod");
		List matchQuality = null;
		Match match = null;
		String matchQlty = "";
		try {
			if (matchId != null) {
				matchQuality = (List ) getHibernateTemplate().find(
						"from Match M where M.id.matchId = " + matchId
								+ " and M.id.hostId='" + hostId
								+ "' and M.id.entityId='" + entityId + "'");
				if (matchQuality.iterator().hasNext()) {
					match = (Match) matchQuality.iterator().next();
					matchQlty = match.getMatchQuality();
				}
			}
			return matchQlty;
		} catch (Exception exception) {
			log.error("Exception caught at " + this.getClass().getName()
					+ " - [getMatchQuality]. Cause: " + exception.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exception,
					"getMatchQuality", MovementDAOHibernate.class);
		}

	}
	// Start for movement Screen
	public Movement getMovementDetails(String hostId, Long movementId)
			throws SwtException {
		log
				.debug("Inside MovementDAOHibernate ---Entering getMovementDetails Mehtod");

		List movementColl = null;
		List movementExtColl = null;
		MovementExt movExt = null;
		Session session = null;
		Connection conn = null;
		CallableStatement cstmt = null;
		ResultSet rs = null;
		String isCritcalHighlight = "N";

		List entityColl = null;
		movementColl = (List ) getHibernateTemplate().find(HOL_MOVEMENTDETAILS,
				new Object[] { hostId, movementId });

		movementExtColl = (List ) getHibernateTemplate().find(HOL_MOVEMENT_EXT_DETAILS,
				new Object[] { hostId, movementId });



		try {

			// Get the hibernate session
			session = getHibernateTemplate().getSessionFactory().openSession();
			// Get the database connection object
			conn = SwtUtil.connection(session);
			// Make a callable statement for executing the procedure
			cstmt = conn
					.prepareCall("{ ? = call pk_movement_summary.FN_IS_MOVS_HIGHLIGHTED(?,?,?)}");
			cstmt.setString(2, hostId);
			cstmt.setString(3, UserThreadLocalHolder.getUser());
			cstmt.setDouble(4, movementId);

			// Register the output parameter for entity details
			cstmt.registerOutParameter(1, oracle.jdbc.OracleTypes.CURSOR);

			// Execute the callable statement
			cstmt.execute();

			// Get the result set of entity details
			rs = (ResultSet) cstmt.getObject(1);

			while (rs.next()) {
				if(rs.getLong("MOVEMENT_ID") == movementId) {
					isCritcalHighlight = rs.getString("SCENARIO_HIGHLIGHTING");
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
		}finally {
			JDBCCloser.close(rs, cstmt, conn, session);
			// nullify objects
			session = null;
			conn = null;
			cstmt = null;
		}






		SwtDataSource dataSource = (SwtDataSource) SwtUtil
				.getBean("dataSourceDb");
		String archDatabaseName = (String) dataSource.useDataSource.get();

		dataSource.clearArchive();

		Iterator itr = movementColl.iterator();
		Movement movement = null;

		while (itr.hasNext()) {
			movement = (Movement) itr.next();

			if (movementExtColl.iterator().hasNext()) {
				movExt = (MovementExt) movementExtColl.iterator().next();
			}

			if(movExt == null) {
				movExt = new MovementExt();
			}

			movement.setMovementExt(movExt);

			movement.setScenarioHighlighted(isCritcalHighlight);

			String entityId = movement.getId().getEntityId();
			if (archDatabaseName != null) {

				movement.setEntityName(entityId);
			} else {
				entityColl = getHibernateTemplate().find(HQL_ENTITYNAME,
						new Object[] { hostId, entityId });

				if (entityColl.size() != 0) {
					Entity entity = (Entity) (entityColl.get(0));
					movement.setEntityName(entity.getEntityName());
				}
			}

		}

		if (movementColl.size() > 0) {
			return movement;
		} else {
			return null;
		}
	} // End of getMovementDetails() method

	/**
	 * DOCUMENT ME!
	 *
	 * @param hostId
	 *            DOCUMENT ME!
	 * @param entityId
	 *            DOCUMENT ME!
	 *
	 * @return DOCUMENT ME!
	 *
	 * @throws SwtException
	 *             DOCUMENT ME!
	 */
	public Collection getBookCodeList(String hostId, String entityId)
			throws SwtException {
		List bookCodeList = null;
		bookCodeList = getHibernateTemplate().find(HQL_BOOKCODELIST,
				new Object[] { hostId, entityId });

		return bookCodeList;
	} // End of getBookCodeList() method

	/**
	 * DOCUMENT ME!
	 *
	 * @param hostId
	 *            DOCUMENT ME!
	 * @param entityId
	 *            DOCUMENT ME!
	 *
	 * @return DOCUMENT ME!
	 *
	 * @throws SwtException
	 *             DOCUMENT ME!
	 */
	public Collection getPartyList(String hostId, String entityId)
			throws SwtException {
		log.debug("Entering into  getPartyList() method");

		List partyList = null;
		partyList = (List ) getHibernateTemplate().find(HQL_PARTYLIST,
				new Object[] { hostId, entityId });
		log.debug("Exiting into  getPartyList() method");

		return partyList;
	}


	/**
	 * DOCUMENT ME!
	 *
	 * @param movement
	 *            DOCUMENT ME!
	 *
	 * @throws SwtException
	 *             DOCUMENT ME!
	 */
	public void saveMovementDetails(Movement movement) throws SwtException {
		Session session = null;
		Transaction transaction = null;
		try {
			log.debug("Entering into saveMovementDetails() method");

			SwtInterceptor interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");
			session = getHibernateTemplate()
					.getSessionFactory()
					.withOptions()
					.interceptor(interceptor)
					.openSession();

			transaction = session.beginTransaction();

			movement.setUpdateUser(UserThreadLocalHolder.getUser());
			movement.setUpdateDate(SwtUtil.getSystemDatewithTime());
			session.save(movement);

			MovementExt ext = movement.getMovementExt();
			if (ext != null && !ext.checkNull()) {
				ext.getId().setEntityId(movement.getId().getEntityId());
				ext.getId().setHostId(movement.getId().getHostId());
				ext.getId().setMovementId(movement.getId().getMovementId());
				ext.setUpdateUser(UserThreadLocalHolder.getUser());
				ext.setUpdateDate(SwtUtil.getSystemDatewithTime());

				session.save(ext);
			}

			transaction.commit();
		} catch (HibernateException e) {
			if (transaction != null) transaction.rollback();
			log.error(this.getClass().getName() + " - Exception in saveMovementDetails: " + e.getMessage(), e);
			throw SwtErrorHandler.getInstance().handleException(e, "saveMovementDetails", MovementDAOHibernate.class);
		} catch (IllegalAccessException e) {
			log.error(this.getClass().getName() + " - Exception IllegalAccessException in saveMovementDetails: " + e.getMessage(), e);
			throw SwtErrorHandler.getInstance().handleException(e, "saveMovementDetails", MovementDAOHibernate.class);
        } finally {
			if (session != null) session.close();
			log.debug("Exiting from saveMovementDetails() method");
		}
	}


	/**
	 * DOCUMENT ME!
	 *
	 * @param movement
	 *            DOCUMENT ME!
	 *
	 * @throws SwtException
	 *             DOCUMENT ME!
	 */
	public void updateMovementDetails(Movement movement) throws SwtException {
		Session session = null;
		Transaction transaction = null;
		try {
			log.debug("Entering into updateMovementDetails() method");

			SwtInterceptor interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");
			session = getHibernateTemplate()
					.getSessionFactory()
					.withOptions()
					.interceptor(interceptor)
					.openSession();

			transaction = session.beginTransaction();

			movement.setUpdateUser(UserThreadLocalHolder.getUser());
			movement.setUpdateDate(SwtUtil.getSystemDatewithTime());
			session.update(movement);

			MovementExt ext = movement.getMovementExt();
			if (ext != null && !ext.checkNull()) {
				ext.getId().setEntityId(movement.getId().getEntityId());
				ext.getId().setHostId(movement.getId().getHostId());
				ext.getId().setMovementId(movement.getId().getMovementId());
				ext.setUpdateUser(UserThreadLocalHolder.getUser());
				ext.setUpdateDate(SwtUtil.getSystemDatewithTime());

				session.merge(ext); // merge is safer for uncertain state
			}

			transaction.commit();
		} catch (HibernateException e) {
			if (transaction != null) transaction.rollback();
			log.error(this.getClass().getName() + " - Exception in updateMovementDetails: " + e.getMessage(), e);
			throw SwtErrorHandler.getInstance().handleException(e, "updateMovementDetails", MovementDAOHibernate.class);
		} catch (IllegalAccessException e) {
			log.error(this.getClass().getName() + " - Exception IllegalAccessException in updateMovementDetails: " + e.getMessage(), e);
			throw SwtErrorHandler.getInstance().handleException(e, "updateMovementDetails", MovementDAOHibernate.class);
        } finally {
			if (session != null) session.close();
			log.debug("Exiting from updateMovementDetails() method");
		}
	}


	/**
	 * DOCUMENT ME!
	 *
	 * @param hostId
	 *            DOCUMENT ME!
	 *
	 * @return DOCUMENT ME!
	 *
	 * @throws SwtException
	 *             DOCUMENT ME!
	 */
	public Collection getMatchDriverDetails(String hostId) throws SwtException {
		log.debug("Entering into  getMatchDriverDetails() method");

		List matchDriverList = null;
		matchDriverList = getHibernateTemplate().find(HQL_MATCHDRIVERLIST,
				new Object[] { hostId });
		log.debug("Exiting from  getMatchDriverDetails() method");

		return (matchDriverList);
	}

	/**
	 * DOCUMENT ME!
	 *
	 * @param matchDriver
	 *            DOCUMENT ME!
	 *
	 * @throws SwtException
	 *             DOCUMENT ME!
	 */
	public void updateMatchDriverDetails(MatchDriver matchDriver)
			throws SwtException {
		log.debug("Entering into  updateMatchDriverDetails() method");
		// Holds the hibernate session instance
		Session session = null;
		SwtInterceptor interceptor = null;
		interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");
		// Holds the hibernate Transaction instance
		Transaction transaction = null;
		session = getHibernateTemplate()
				.getSessionFactory()
				.withOptions().interceptor(interceptor).openSession();
		transaction = session.beginTransaction();
		session.update(matchDriver);
		transaction.commit();
		session.close();
		log.debug("Exiting from  updateMatchDriverDetails() method");
	}

	/**
	 * To find list of accounts This function appends various filtering
	 * condition in the select query
	 */
	public Collection getAccountIdDetails(String hostId, String entityId,
										  String currCode, String movType, String inputSource)
			throws SwtException {
		log.debug("Entering into  getAccountIdDetails() method");

		List AccountIdList = null;

		StringBuffer acctQueryPart1 = new StringBuffer(
				"from AcctMaintenance acct where acct.id.hostId =?0 ");
		acctQueryPart1.append(" and acct.id.entityId=?1 and acct.accttype = ?2 ");

		StringBuffer acctQueryPart2 = new StringBuffer(
				" and acct.acctstatusflg = 'O' and acct.id.accountId != '*' order by acct.id.accountId");

		if ((inputSource != null)
				&& inputSource
				.equalsIgnoreCase(SwtConstants.MOVEMENT_SOURCE_PREADVICE)) {
			if ((currCode != null)
					&& currCode.equalsIgnoreCase(SwtConstants.ALL_VALUE)) {
				AccountIdList = (List ) getHibernateTemplate().find(
						acctQueryPart1.append(
								"and acct.allPreAdviceEntity = 'Y'").append(
								acctQueryPart2).toString(),
						new Object[] { hostId, entityId, movType });
			} else {
				AccountIdList = (List ) getHibernateTemplate().find(
						acctQueryPart1
								.append(
										"and acct.currcode = ?3 and acct.allPreAdviceEntity = 'Y'")
								.append(acctQueryPart2).toString(),
						new Object[] { hostId, entityId, movType,
								currCode });
			}
		} else {
			if ((currCode != null)
					&& currCode.equalsIgnoreCase(SwtConstants.ALL_VALUE)) {

				AccountIdList = (List ) getHibernateTemplate().find(
						acctQueryPart1.toString(),
						new Object[] { hostId, entityId, movType });
			} else {
				AccountIdList = (List ) getHibernateTemplate().find(
						acctQueryPart1.append("and acct.currcode = ?3 ").append(
								acctQueryPart2).toString(),
						new Object[] { hostId, entityId, movType, currCode });
			}
		}
		log.debug("Exiting from  getAccountIdDetails() method");

		return (AccountIdList);
	}

	/**
	 * DOCUMENT ME!
	 *
	 * @param matchDriver
	 *            DOCUMENT ME!
	 *
	 * @throws SwtException
	 *             DOCUMENT ME!
	 */
	public void saveMatchDriverDetails(MatchDriver matchDriver)
			throws SwtException {
		log.debug("Entering into  saveMatchDriverDetails() method");
		// Holds the hibernate session instance
		Session session = null;
		SwtInterceptor interceptor = null;
		interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");
		// Holds the hibernate Transaction instance
		Transaction transaction = null;
		session = getHibernateTemplate()
				.getSessionFactory()
				.withOptions().interceptor(interceptor).openSession();
		transaction = session.beginTransaction();

		session.save(matchDriver);
		transaction.commit();
		session.close();
		log.debug("Exiting from  updateMatchDriverDetails() method");
	}

	/**
	 * DOCUMENT ME!
	 *
	 * @param movementNote
	 *            DOCUMENT ME!
	 *
	 * @throws SwtException
	 *             DOCUMENT ME!
	 */
	public void saveNotesDetails(MovementNote movementNote) throws SwtException {
		log.debug("Entering saveNotesDetails");
		SwtInterceptor interceptor = null;
		interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");
		// Holds the hibernate session instance
		Session session = null;
		// Holds the hibernate Transaction instance
		Transaction transaction = null;

		session = getHibernateTemplate()
				.getSessionFactory()
				.withOptions().interceptor(interceptor).openSession();
		transaction = session.beginTransaction();
		session.save(movementNote);
		transaction.commit();
		session.close();
		log.debug("Exiting saveNotesDetails");
	}

	/**
	 * DOCUMENT ME!
	 *
	 * @param hostId
	 *            DOCUMENT ME!
	 * @param entityId
	 *            DOCUMENT ME!
	 * @param counterPartyId
	 *            DOCUMENT ME!
	 *
	 * @return DOCUMENT ME!
	 *
	 * @throws SwtException
	 *             DOCUMENT ME!
	 */
	public Collection getCounterPartyRecord(String hostId, String entityId,
											String counterPartyId) throws SwtException {
		log.debug("Entering into  getCounterPartyRecord() method");

		List partyList = null;
		partyList = (List ) getHibernateTemplate().find(HQL_PARTYRECORD,
				new Object[] { hostId, entityId, counterPartyId });
		log.debug("Exiting into  getCounterPartyRecord() method");

		return partyList;
	}
	/**
	 * DOCUMENT ME!
	 *
	 * @param hostId
	 *            DOCUMENT ME!
	 * @param entityId
	 *            DOCUMENT ME!
	 * @param counterPartyId
	 *            DOCUMENT ME!
	 *
	 * @return DOCUMENT ME!
	 *
	 * @throws SwtException
	 *             DOCUMENT ME!
	 */
	public Party getPartyRecord(String hostId, String entityId,
								String partyId) throws SwtException {
		log.debug("Entering into  getPartyRecord() method");
		Party party = null;
		List partyList = null;
		partyList = (List ) getHibernateTemplate().find(HQL_PARTYRECORD,
				new Object[] { hostId, entityId, partyId });
		log.debug("Exiting into  getPartyRecord() method");
		if(partyList.size()>0)
			party = (Party) partyList.get(0);

		return party;
	}

	/**
	 * DOCUMENT ME!
	 *
	 * @param hostId
	 *            DOCUMENT ME!
	 * @param entityId
	 *            DOCUMENT ME!
	 * @param beneficiaryId
	 *            DOCUMENT ME!
	 *
	 * @return DOCUMENT ME!
	 *
	 * @throws SwtException
	 *             DOCUMENT ME!
	 */
	public Collection getBeneficiaryRecord(String hostId, String entityId,
										   String beneficiaryId) throws SwtException {
		log.debug("Entering into  getBeneficiaryRecord() method");

		List partyList = null;
		partyList = (List ) getHibernateTemplate().find(HQL_PARTYRECORD,
				new Object[] { hostId, entityId, beneficiaryId });
		log.debug("Exiting into  getBeneficiaryRecord() method");

		return partyList;
	}

	/**
	 * DOCUMENT ME!
	 *
	 * @param hostId
	 *            DOCUMENT ME!
	 * @param entityId
	 *            DOCUMENT ME!
	 * @param custodianId
	 *            DOCUMENT ME!
	 *
	 * @return DOCUMENT ME!
	 *
	 * @throws SwtException
	 *             DOCUMENT ME!
	 */
	public Collection getCustodianRecord(String hostId, String entityId,
										 String custodianId) throws SwtException {
		log.debug("Entering into  getCustodianRecord() method");

		List partyList = null;
		partyList = (List ) getHibernateTemplate().find(HQL_PARTYRECORD,
				new Object[] { hostId, entityId, custodianId });
		log.debug("Exiting into  getCustodianRecord() method");

		return partyList;
	}

	/**
	 * This method is useful to get the Matching party records from the data
	 * base
	 *
	 * @param hostId
	 * @param entityId
	 * @param MatchingParty
	 * @throws SwtException
	 * @return collection
	 */
	public Collection getMatchingPartyRecord(String hostId, String entityId,
											 String matchingParty) throws SwtException {
		log.debug(this.getClass().getName() + " - getMatchingPartyRecord() - "
				+ " Entry ");

		List partyList = null;
		partyList = (List ) getHibernateTemplate().find(HQL_PARTYRECORD,
				new Object[] { hostId, entityId, matchingParty });

		log.debug(this.getClass().getName() + " - getMatchingPartyRecord() - "
				+ " Exit ");

		return partyList;
	}

	/**
	 * DOCUMENT ME!
	 *
	 * @param hostId
	 *            DOCUMENT ME!
	 * @param movementId
	 *            DOCUMENT ME!
	 *
	 * @return DOCUMENT ME!
	 *
	 * @throws SwtException
	 *             DOCUMENT ME!
	 */
	public Collection getNoteDetails(String hostId, Long movementId)
			throws SwtException {
		log.debug("Entering into  getNoteDetails() method");

		List noteList = null;
		noteList = getHibernateTemplate().find(HQL_NOTELIST,
				new Object[] { hostId, movementId });
		log.debug("Exiting from  getNoteDetails() method");

		return noteList;
	}

	/**
	 * @desc This method will return the List of Currencies for the given
	 *       hostId, entityId and Currency Group Id If Currency Group ID is
	 *       'ALL' then it returns all the currencies for the given hostId and
	 *       entityId
	 * @param hostId -
	 *            HostID
	 * @param entityId -
	 *            EntityID
	 * @param currencyGroupId -
	 *            Currency Group Id
	 * @return List - List
	 * @throws SwtException -
	 *             SwtException
	 */
	public List getCurrencyList(String hostId, String entityId,
								String currencyGroupId) throws SwtException {
		log.debug("Entering getCurrencyList method");

		java.util.List currList;

		if (SwtConstants.ALL_VALUE.equalsIgnoreCase(currencyGroupId)) {
			currList = getHibernateTemplate().find(HQL_CCYLIST_CCYGRPALL,
					new Object[] { hostId, entityId });
		} else {
			currList = getHibernateTemplate().find(HQL_CCYLIST,
					new Object[] { hostId, entityId, currencyGroupId });
		}
		log.debug("Exiting getCurrencyList method");

		return currList;
	}

	/**
	 * @desc - This method return the open Movement count for specified HostId,
	 *       EntityId, CurrencyCode, date and Position Level
	 * @param hostId -
	 *            HostId
	 * @param entityId -
	 *            EntityId
	 * @param currencyCode -
	 *            currency Code
	 * @param date -
	 *            Date
	 * @param positionLevel
	 *            -Position Level Id
	 * @param isAll -
	 *            boolean
	 * @return - count of open Movements satisfying given condition
	 * @throws SwtException -
	 *             SwtException
	 */
	public int getOpenMovementsbydate(String hostId, String entityId,
									  String currencyCode, Date date, Integer positionLevel, boolean isAll)
			throws SwtException {
		// Open Movements Collection
		List<Movement> openMovementsColl = null;
		// Last opened movement Collection
		List<Movement> finalOpenMovement = null;
		// Currency Exchange List
		ArrayList<CurrencyExchange> currencyExchangeList = null;
		// Iterator to iterate the movement
		Iterator<Movement> movCollIterator = null;
		// Variable for movement
		Movement movement = null;
		// Variable for entity
		Entity entity = null;
		// exchange rate of inverse
		double exchangeRateInverseFactor;
		try {
			log.debug(this.getClass().getName()
					+ "- [getOpenMovementsbydate] - Entry");
			finalOpenMovement = new ArrayList<Movement>();
			if (isAll) {
				// Gets all movements
				openMovementsColl = (List<Movement>) getHibernateTemplate()
						.find(
								"from Movement p "
										+ "where  p.id.hostId=?0 and p.id.entityId=?1 and p.currencyCode=?2 "
										+ "and (( p.matchStatus in ('L','M','S') and p.positionLevel < ?3) or (p.matchStatus = 'C' and p.predictStatus = 'I' and p.positionLevel < ?4))  and p.predictStatus != 'C'  "
										+ "and p.valueDate >= ?5",
								new Object[] {
										hostId,
										entityId,
										currencyCode,
										new Integer(
												positionLevel.intValue() + 1),
										positionLevel, date });
			} else {
				// gets movements by latest
				openMovementsColl = (List<Movement>) getHibernateTemplate()
						.find(
								"from Movement p "
										+ "where  p.id.hostId=?0 and p.id.entityId=?1 and p.currencyCode=?2 "
										+ "and (( p.matchStatus in ('L','M','S') and p.positionLevel < ?3) or (p.matchStatus = 'C' and p.predictStatus = 'I' and p.positionLevel < ?4))  and p.predictStatus != 'C'  "
										+ "and p.valueDate = ?5",
								new Object[] {
										hostId,
										entityId,
										currencyCode,
										new Integer(
												positionLevel.intValue() + 1),
										positionLevel, date });
			}

			entity = new Entity();
			// Gets the entity detail
			entity = getEntityDetail(hostId, entityId);
			// Gets the currency exchange list
			currencyExchangeList = (ArrayList<CurrencyExchange>) getCurrencyExchangeRateDetail(
					hostId, entityId, currencyCode, date);
			CurrencyExchange currencyExchange = null;

			movCollIterator = openMovementsColl.iterator();
			movement = new Movement();
			// Iterates the movement collection
			while (movCollIterator.hasNext()) {
				movement = (Movement) movCollIterator.next();
				exchangeRateInverseFactor = 1.0;
				// checks the currency exchange exists
				if (currencyExchangeList.size() > 0) {
					currencyExchange = (CurrencyExchange) currencyExchangeList
							.get(0);
					if ((entity.getExchangeRateFormat() != null)
							&& (currencyExchange.getExchangeRate() != null)) {

						if (entity.getExchangeRateFormat()
								.equalsIgnoreCase("1")) {
							// Converts to double
							exchangeRateInverseFactor = new Double(
									currencyExchange.getExchangeRate());
						} else if ((entity.getExchangeRateFormat()
								.equalsIgnoreCase("2"))) {
							// Gets the inverse
							exchangeRateInverseFactor = exchangeRateInverseFactor
									/ new Double(currencyExchange
									.getExchangeRate());
						}

					}
				}
				// Checks the movement type if "U" or "C" adds to final movement
				// array
				if (movement.getMovementType().equalsIgnoreCase("C")) {
					if (((movement.getAmount()).doubleValue() * exchangeRateInverseFactor) >= (entity
							.getCashFilterThreshold()).doubleValue()) {
						finalOpenMovement.add(movement);
					}
				} else if (movement.getMovementType().equalsIgnoreCase("U")) {
					if (((movement.getAmount()).doubleValue() * exchangeRateInverseFactor) >= (entity
							.getSecuritiesFilterThreshold()).doubleValue()) {
						finalOpenMovement.add(movement);
					}
				}
			}
			log.debug(this.getClass().getName()
					+ "- [getOpenMovementsbydate] - Exit");

		} catch (SwtException swtExp) {
			log.error("Exception Catch in" + this.getClass().getName()
					+ "- [getOpenMovementsbydate] method : "
					+ swtExp.getMessage());
			SwtUtil.logException(swtExp, null, "");
			throw swtExp;

		} catch (Exception exp) {
			log
					.error("Exception Catch in" + this.getClass().getName()
							+ "- [getOpenMovementsbydate] method : "
							+ exp.getMessage());

			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
							exp, "getOpenMovementsbydate", MovementDAOHibernate.class),
					null, "");
			// Re-throw as SwtException
			throw SwtErrorHandler.getInstance().handleException(exp,
					"getOpenMovementsbydate", MovementDAOHibernate.class);

		} finally {
			// nullify objects
			openMovementsColl = null;
			currencyExchangeList = null;
			movCollIterator = null;
			movement = null;
			entity = null;
		}
		return finalOpenMovement.size();
	}

	/**
	 * @desc This method will delete all the Match Objects contained in
	 *       deleteMatchList
	 * @param deleteMatchList -
	 *            List contains Match Objects to be deleted
	 * @param deleteMatchNotesList -
	 *            List contains MatchNote Objects to be deleted
	 * @throws SwtException -
	 *             SwtException
	 */
	public void deleteOtherMatches(List deleteMatchList,
								   List deleteMatchNotesList) throws SwtException {

		Session session = null;
		Transaction tx = null;
		SwtInterceptor interceptor = null;
		Iterator itr = null;

		try {
			log.debug("entering deleteOtherMatches");
			interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");
			session = getHibernateTemplate().getSessionFactory().withOptions().interceptor(interceptor).openSession();
			tx = session.beginTransaction();
			itr = deleteMatchNotesList.iterator();

			while (itr.hasNext()) {
				Object obj = (Object) itr.next();

				if (obj != null) {
					session.delete(obj);
				}
			}

			itr = deleteMatchList.iterator();

			while (itr.hasNext()) {
				Object obj = (Object) itr.next();

				if (obj != null) {
					session.delete(obj);
				}
			}

			tx.commit();
			log.debug("exiting deleteOtherMatches");
		} catch (Exception exception) {
			log.debug("Problem in executing hibernate query;");
			exception.printStackTrace();
			throw SwtErrorHandler.getInstance().handleException(exception, "deleteOtherMatches",
					MovementDAOHibernate.class);
		} finally {
			JDBCCloser.close(session);
		}
	}

	/**
	 * @desc this method fetches all the Movements satisfying the conditions for
	 *       Offered Match , Suspend Match, Confirm
	 * @param hostId
	 * @param entityId
	 * @param movementIds
	 * @return
	 * @throws SwtException
	 */
	public List getAllMovement(String hostId, String entityId,
							   String movementIds) throws SwtException {
		ArrayList<Object> params =null;

		log.debug("Entering getAllMovement method");
		params = new ArrayList<Object>();
		/* Finding Unique matchIds */
		StringBuffer movQuery = new StringBuffer(
				" select distinct p.matchId from Movement p");
		movQuery.append(" where p.id.hostId = ?0");
		movQuery.append(" and p.id.entityId = ?1");
		movQuery.append(" and p.id.movementId in (" + movementIds + ")");

		StringBuffer matchIdString = new StringBuffer(" ");
		List matchIdList = getHibernateTemplate().find(movQuery.toString(), new Object[] { hostId, entityId });

		if (matchIdList != null && matchIdList.size() > 0) {
			Iterator itr = matchIdList.iterator();
			while (itr.hasNext()) {
				Object obj = itr.next();
				if (obj != null) {
					matchIdString.append(obj.toString());
					matchIdString.append(",");
				}
			}
		}
		StringBuffer hqlQuery = new StringBuffer(" select distinct m  from  Movement m where m.id.hostId =?0");
		hqlQuery.append(" and m.id.entityId = ?1");
		hqlQuery.append(" and  (m.id.movementId in (" + movementIds + ")");

		if (matchIdString.toString().trim().equalsIgnoreCase("")) {
			hqlQuery.append(")");
		} else {
			hqlQuery.append(" or  (m.matchId in ("
					+ matchIdString.substring(0, matchIdString.length() - 1)
					+ ")))");
		}
		hqlQuery.append(" order by m.positionLevel desc,m.bookCodeAvail desc");
		List alreadyMatchList = getHibernateTemplate()
				.find(hqlQuery.toString(), new Object[] { hostId, entityId });

		log.debug(" Exiting getAllMovement ");

		return alreadyMatchList;
	}

	/**
	 * @desc This method fetches the Match for given hostId, entityId and
	 *       matchId
	 * @param hostId -
	 *            HostId
	 * @param entityId -
	 *            EntityId
	 * @param matchId -
	 *            MatchId
	 * @return Match Object
	 * @throws SwtException -
	 *             SwtException
	 */
	public Match getMatch(String hostId, String entityId, Long matchId)
			throws SwtException {
		log.debug("Entering getMatch method");

		String hqlQuery = " from Match m where m.id.hostId=?0 and m.id.entityId=?1 and m.id.matchId = ?2";

		List matchColl = getHibernateTemplate().find(hqlQuery,
				new Object[] { hostId, entityId, matchId });
		Match match = null;

		if (matchColl.size() > 0) {
			match = (Match) matchColl.get(0);
		}

		log.debug("Exiting getMatch method");

		return match;
	} // End of getMatch method

	/**
	 * @desc - This method will update the Match Object
	 * @param match -
	 *            Match Object
	 * @throws SwtException -
	 *             SwtException
	 */
	public void updateMatch(Match match) throws SwtException {
		log.debug(" Entering updateMatch ");
		// Holds the hibernate session instance
		Session session = null;
		SwtInterceptor interceptor = null;
		interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");
		// Holds the hibernate Transaction instance
		Transaction transaction = null;
		session = getHibernateTemplate()
				.getSessionFactory()
				.withOptions().interceptor(interceptor).openSession();
		transaction = session.beginTransaction();
		session.update(match);
		transaction.commit();
		session.close();
		log.debug(" Exiting upDateAllMovements ");
	} // End of updateMatch method

	/**
	 * @desc This method fetches the Collection of CurrencyExchange for the
	 *       hostId, entityId, currencyId and exchangeRateDate <= date
	 * @param hostId -
	 *            HostId
	 * @param entityId -
	 *            EntityId
	 * @param currencyId -
	 *            currencyId
	 * @param date -
	 *            date
	 * @return List of all the currencyExchange defined for this hostIs,
	 *         entityId, currencyId and exchangeRateDate <= date
	 * @throws SwtException -
	 *             SwtException
	 */
	private List getCurrencyExchangeRateDetail(String hostId, String entityId,
											   String currencyId, Date date) throws SwtException {
		log.debug("Entering getCurrencyExchangeRateDetail method");

		final String HQL_GETCCYEXCHANGERATE = "from CurrencyExchange c where c.id.hostId=?0 and c.id.entityId=?1 and c.id.currencyCode=?2 and c.id.exchangeRateDate <=?3 order by c.id.exchangeRateDate desc";
		CurrencyExchange currencyExchange = new CurrencyExchange();
		List currencyExchangeList = getHibernateTemplate().find(
				HQL_GETCCYEXCHANGERATE,
				new Object[] { hostId, entityId, currencyId, date });

		return currencyExchangeList;
	}

	/**
	 * This method finds the MessageId for a particualar movement Record
	 */
	public Collection getMovementMessageId(String hostId, Long movementId)
			throws SwtException {
		log.debug("Entering getMovementMessageId method");

		StringBuffer query = new StringBuffer(
				"select mm from MovementMessage mm where mm.id.hostId = ?0 and mm.id.movementId = ?1 ");
		query.append(" and mm.inputDate = (select max(dd.inputDate) from ");
		query
				.append(" MovementMessage dd where dd.id.hostId = ?2 and dd.id.movementId = ?3)");

		List messageIdColl = getHibernateTemplate().find(query.toString(),
				new Object[] { hostId, movementId, hostId, movementId });

		if (messageIdColl != null) {
			Iterator itrMessageColl = messageIdColl.iterator();

			while (itrMessageColl.hasNext()) {
				MovementMessage ms = (MovementMessage) (itrMessageColl.next());
			}
		}

		log.debug("Exiting getMovementMessageId method");

		return messageIdColl;
	}

	/**
	 *
	 * @param hostId -
	 *            HostId
	 * @param entityId -
	 *            EntityId
	 * @param roleId -
	 *            roleId
	 * @param currencyGrpId -
	 *            currency Group Id
	 * @param date -
	 *            Date
	 * @param highestPosLvl -
	 *            Highest defined Position Level for that Entity
	 * @param isAll -
	 *            String
	 * @param applyCurrencyThreshold -
	 *            Apply Currency Threshold
	 * @return - Collection of EntityPositionLevel Objects
	 * @throws SwtException -
	 *             SwtException
	 * @throws HibernateException -
	 *             HibernateException
	 */
	public ArrayList getOpenMovementsCountdate(String hostId, String entityId,
											   String roleId, String currencyGrpId, Date date, int highestPosLvl,
											   String isAll, String applyCurrencyThreshold) throws SwtException {

		Session session = null;
		CallableStatement cstmt = null;
		ArrayList<Object> arrColl = new ArrayList<Object>();
		Collection<EntityPositionLevel> entPosLvlList = new ArrayList<EntityPositionLevel>();
		ResultSet rs = null;
		long movcount = 0;
		int posLvl = 0;
		String currCode = null;
		String currName = null;
		EntityPositionLevel entPosLvl = null;
		int rowCount = 0;
		/*
		 * ResultSet object initialize the null value and used to get the data
		 * from DB.
		 */
		ResultSet rsTabFlag = null;
		// Variable to hold Tab Flag
		String tabFlag = null;
		Connection conn = null;
		try {
			log.debug(this.getClass().getName()
					+ "-[getOpenMovementsCountdate()]- Entry");
			session = getHibernateTemplate().getSessionFactory().openSession();
			conn = SwtUtil.connection(session);

			cstmt = conn
					.prepareCall("{call PK_APPLICATION.sp_open_movements(?,?,?,?,?,?,?,?,?,?)}");

			cstmt.setString(1, hostId);
			cstmt.setString(2, entityId);
			cstmt.setString(3, currencyGrpId);
			cstmt.setDate(4, SwtUtil.truncateDateTime(date));
			cstmt.setString(5, isAll);
			cstmt.setString(6, roleId);
			cstmt.setString(7, SwtConstants.FLAG_EXCLUDEDOUTSTANDING);

			cstmt.setString(8, applyCurrencyThreshold);

			cstmt.registerOutParameter(9, oracle.jdbc.OracleTypes.CURSOR);
			cstmt.registerOutParameter(10, oracle.jdbc.OracleTypes.CURSOR);
			cstmt.execute();
			rs = (ResultSet) cstmt.getObject(9);
			// To get the Tabflag values for Result Set
			rsTabFlag = (ResultSet) cstmt.getObject(10);
			// To initial the Tab Flag value
			tabFlag = "";
			// To read the Tab flag Values and set the Local String value
			if (rsTabFlag != null) {
				while (rsTabFlag.next()) {
					tabFlag = tabFlag + rsTabFlag.getString(1);
				}
			}
			// To add the arraylist for tabflag
			arrColl.add(tabFlag);

			while (rs.next()) {
				rowCount++;
				entPosLvl = new EntityPositionLevel();
				movcount = rs.getLong(1);
				posLvl = rs.getInt(2);
				currCode = rs.getString(3);
				currName = rs.getString(4);
				entPosLvl.getId().setEntityId(entityId);
				entPosLvl.getId().setPositionLevel(new Integer(posLvl));
				entPosLvl.setCurrencyName(currName);
				entPosLvl.setCurrencyCode(currCode);

				if (movcount > 0) {
					entPosLvl.setMovementTotalAsString("" + movcount);
				} else {
					entPosLvl.setMovementTotalAsString("");
				}

				entPosLvl.setMovementTotal(new Long(movcount));
				entPosLvlList.add(entPosLvl);
			}
			arrColl.add(entPosLvlList);
		} catch (HibernateException hibernateException) {
			log.error("Exception got in " + this.getClass().getName()
					+ "-[getOpenMovementsCountdate()]- "
					+ hibernateException.getMessage());
			throw SwtErrorHandler.getInstance().handleException(
					hibernateException, "getOpenMovementsCountdate",
					MovementDAOHibernate.class);
		} catch (SQLException sqlException) {
			log.error("Exception got in " + this.getClass().getName()
					+ "-[getOpenMovementsCountdate()]- "
					+ sqlException.getMessage());
			throw SwtErrorHandler.getInstance().handleException(sqlException,
					"getOpenMovementsCountdate", MovementDAOHibernate.class);
		} finally {

			// edited by KaisBS for mantis 1745 : introducing the implementation
			// of the JDBCCloser class
			SwtException thrownException = null;
			Object[] exceptions = JDBCCloser.close(rs, cstmt, conn, session);

			if (exceptions[0] != null)
				thrownException = SwtErrorHandler.getInstance()
						.handleException((SQLException) exceptions[0],
								"getOpenMovementsCountdate",
								MovementDAOHibernate.class);

			if (thrownException == null && exceptions[1] != null)
				thrownException = SwtErrorHandler.getInstance()
						.handleException((HibernateException) exceptions[1],
								"getOpenMovementsCountdate",
								MovementDAOHibernate.class);

			if (thrownException != null)
				throw thrownException;

			log.debug(this.getClass().getName()
					+ "-[getOpenMovementsCountdate()]- Exit");
		}

		return arrColl;
	} // End of getOpenMovementsCountdate() method

	/**
	 * This method returns the collection of messages attached with the movemetn
	 * with given movementId.
	 *
	 * @param movementId
	 * @return
	 * @throws SwtException
	 */
	public Collection getMovMessageColl(String movementId) throws SwtException {
		log.debug("Entering getMovMessageColl method");

		String hostId = CacheManager.getInstance().getHostId();
		StringBuffer query = new StringBuffer(
				"select mm from MovementMessage mm where mm.id.hostId = ?0 and mm.id.movementId = ?1 order by mm.inputDate desc");
		List movMessageColl = getHibernateTemplate().find(query.toString(),
				new Object[] { hostId, new Long(movementId) });

		return movMessageColl;
	}

	public Collection getMovementsForMatch(String hostId, String entityId,
										   Long matchId) throws SwtException {
		log.debug("Entering getMovementsForMatch method");

		String hqlQuery = " from Movement m where m.id.hostId=?0 and m.id.entityId=?1 and m.matchId = ?2";

		List mvmntColl = getHibernateTemplate().find(hqlQuery,
				new Object[] { hostId, entityId, matchId });

		log.debug("Exiting getMatch method");
		return mvmntColl;
	}

	/*
	 * (non-Javadoc)
	 *
	 * @see org.swallow.work.dao.MovementDAO#saveRolledMatch(org.swallow.work.model.Match)
	 */
	public void saveRolledMatch(Match newMatch) throws SwtException {
		log.debug("Entering into  saveRolledMatch() method");
		// Holds the hibernate session instance
		Session session = null;
		// Holds the hibernate Transaction instance
		SwtInterceptor interceptor = null;
		interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");
		Transaction transaction = null;
		session = getHibernateTemplate()
				.getSessionFactory()
				.withOptions().interceptor(interceptor).openSession();
		transaction = session.beginTransaction();
		session.save(newMatch);
		session.close();
		log.debug("Exiting from  saveRolledMatch() method");
	}

	/*
	 * (non-Javadoc)
	 *
	 * @see org.swallow.work.dao.MovementDAO#saveMatchNotes(org.swallow.work.model.MatchNote)
	 */
	public void saveMatchNotes(MatchNote matchNote) throws SwtException {
		log.debug("Entering saveMatchNotes");
		// Holds the hibernate session instance
		Session session = null;
		// Holds the hibernate Transaction instance
		Transaction transaction = null;
		SwtInterceptor interceptor = null;
		interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");
		session = getHibernateTemplate()
				.getSessionFactory()
				.withOptions().interceptor(interceptor).openSession();
		transaction = session.beginTransaction();
		session.save(matchNote);
		transaction.commit();
		session.close();
		log.debug("Exiting saveMatchNotes");
	}

	// Modified for Mantis 1443, add the scenario id in parameters
	/**
	 * <pre>
	 * This method gets movement details and total no of movements
	 * This method handles two type of request.
	 * 1. Get movement details and total no of movements
	 * 2. No of movements
	 *
	 * This method executes two procedures. First SP gives movement details or
	 * count flag value (0-No movements and 1 - Movements available). Second SP
	 * gives total no of movements.
	 *
	 * Second SP will be run only when the request is for movement details.
	 * </pre>
	 *
	 * @param hostId
	 * @param entityId
	 * @param currCode
	 * @param accountId
	 * @param valueDate
	 * @param accountType
	 * @param balType
	 * @param posLevelId
	 * @param pageSize
	 * @param currentPage
	 * @param isAllStr
	 * @param filterCriteria
	 * @param sortCriteria
	 * @param sourceScreen
	 * @param totalCount
	 * @param monitorMvmntList
	 * @param dataFetchIndicator
	 * @param applyCurrencyThreshold
	 * @param extraValues
	 * @return int
	 * @throws SwtException
	 */
	public HashMap<String, Object> getMonitorMovements(String hostId, String entityId,
													   String currCode, String accountId, Date valueDate,
													   String accountType, String balType, int posLevelId, int pageSize,
													   int currentPage, String isAllStr, String filterCriteria,
													   String openMovementFlag, String sortCriteria, String sourceScreen,
													   ArrayList<Movement> lstMonitorMovement,
													   String dataFetchIndicator, String applyCurrencyThreshold, String userId, String profileId, String...extraValues)
			throws SwtException {
		// Match status values for selected entity
		Collection<MiscParams> colMiscParams = null;
		Iterator<MiscParams> itrMiscParams = null;
		// Hibernate session
		Session session = null;
		// Database connection
		Connection conn = null;
		// To get movement details
		CallableStatement cstmtMovement = null;
		// To get movement count
		CallableStatement cstmtCount = null;
		// To get movement details
		ResultSet rsMovement = null;
		// To get movement count
		ResultSet rsCount = null;
		// Total flag
		String totalFlag = null;
		// To store movement details
		Movement movement = null;
		// Date format string
		String dateFormat = null;
		// Date time format
		SimpleDateFormat sdfInputDate = null;
		// Time format
		SimpleDateFormat sdfTime = null;
		// Movement updated date
		String updateDate = null;
		// varible to hold misc params to iterate the match status values
		MiscParams miscParams = null;
		// To hold no of movements
		int movementCount;
		// To hold movement Posting date
		java.sql.Date postingDate = null;
		String scenarioId = null;
		String roleId = null;
		HashMap<String, Object> hashMapResult = null;
		String[] filterSearchArray = null;
		String searchFilter = null;
		//Those filters will be added from the new throughput monitor breakdown monitor it contains ILM GROUP, Actual or Forecasted or unsettled Outflow
		String filterThruMonitor = null;
		/* Variable Declaration for CommonDataManager */
		CommonDataManager CDM = null;
		boolean isExportAction = false;
		String inputDate = null;
		try {
			log.debug(this.getClass().getName()
					+ " - [getMonitorMovements] - Entry");
			// Get scenario id and role id if we come from scenario summary or workflow monitor
			scenarioId = extraValues[0];
			roleId = extraValues[1];
			if(!SwtUtil.isEmptyOrNull(filterCriteria) && !"All".equalsIgnoreCase(filterCriteria)) {
				if(filterCriteria.indexOf("|") == -1)
					filterCriteria= SwtUtil.decode64(filterCriteria);
			}
			if(!SwtUtil.isEmptyOrNull(sortCriteria) && !"None".equalsIgnoreCase(sortCriteria)) {
				if(sortCriteria.indexOf("|") == -1)
					sortCriteria= SwtUtil.decode64(sortCriteria);
			}
			// Get threshold flag and total flag values
			// Default movement count
			movementCount = 0;
			// Get Flag value to apply currency threshold and total flag value
			if (applyCurrencyThreshold == null) {
				applyCurrencyThreshold = SwtConstants.YES;
			}
			// Condition to check applycurrency threshold is with totalflag value
			if (applyCurrencyThreshold.length() == 2) {
				totalFlag = applyCurrencyThreshold.substring(1);
				applyCurrencyThreshold = applyCurrencyThreshold.substring(0, 1);
			} else {
				totalFlag = "N";
			}

			// From previous class (Manager), accountType contains original data from screen + \n#\n + searchFilter, they are separated here.
			if (!SwtUtil.isEmptyOrNull(accountType) && (sourceScreen.equals("W") || sourceScreen.equals("X") || sourceScreen.equals("K")) ) {
				filterSearchArray = accountType.split("\n#\n");
				if(filterSearchArray.length == 2) {
					searchFilter = filterSearchArray[1];
					accountType = filterSearchArray[0];

				}else if(filterSearchArray.length == 1) {
					accountType = filterSearchArray[0];
					searchFilter = null;
				}
			}else {
				if(!SwtUtil.isEmptyOrNull(accountType) && (sourceScreen.equals("S"))){
					filterSearchArray = accountType.split("#&#");
					if(filterSearchArray.length == 2) {
						filterThruMonitor = filterSearchArray[1];
						accountType = filterSearchArray[0];

					}else if(filterSearchArray.length == 1) {
						accountType = filterSearchArray[0];
						filterThruMonitor = null;
					}
				}

				searchFilter = accountType;

			}

			/*
			 * Start:Code Modified For Mantis 2078 by sudhakar on
			 * 06-Nov-2012:INTERNAL: PK_MOVEMENT_SUMMARY package to be
			 * streamlined
			 */
			// If filterCriteria is not null,All and last filter criteria has no
			// '|',Append pipe line to filter
			// posting date
			/*if (!SwtUtil.isEmptyOrNull(filterCriteria)
					&& !(filterCriteria.charAt(filterCriteria.length() - 1) == '|')
					&& !filterCriteria.equalsIgnoreCase("All"))
				filterCriteria = filterCriteria + "|";*/


			/*
			 * End:Code Modified For Mantis 2078 by sudhakar on
			 * 06-Nov-2012:INTERNAL: PK_MOVEMENT_SUMMARY package to be
			 * streamlined
			 */
			// Open/Get hibernate session
			session = getHibernateTemplate().getSessionFactory().openSession();
			conn = SwtUtil.connection(session);
			// Create statement to invoke SP

		/*	PROCEDURE sp_movement_summary_ex(pHostId              IN  s_host.host_id%TYPE,
                    pEntityId            IN  s_entity.entity_id%TYPE,
                    pCurrencyCode        IN  s_currency.currency_code%TYPE,
                    pValueDate           IN  DATE,
                    pPosLevel            IN  NUMBER,
                    pPageSize            IN  NUMBER,
                    pCurrentPage         IN  NUMBER,
                    pIsAllTab            IN  VARCHAR2,
                    pFilterCriteria      IN  VARCHAR2,
                    pInclOpenMovements   IN  VARCHAR2,
                    pSortCriteria        IN  VARCHAR2,
                    pSource              IN  VARCHAR2,
                    pBalType             IN  VARCHAR2 DEFAULT NULL,
                    pAccountId           IN  VARCHAR2 DEFAULT NULL,
                    pAccountType         IN  VARCHAR2 DEFAULT NULL,
                    pIsShowRecords       IN  VARCHAR2 DEFAULT 'Y',
                    pCurrThreshold       IN  VARCHAR2 DEFAULT 'Y',
                    pTotalFlag           IN  VARCHAR2 DEFAULT 'N',
                    pDBLink              IN  VARCHAR2 DEFAULT NULL,
                    pRef                 OUT SYS_REFCURSOR,
                    pScenario_id         IN P_SCENARIO.SCENARIO_ID%TYPE DEFAULT NULL,
                    pRoleId              IN VARCHAR2 DEFAULT NULL,
                    pSearchFilter        IN VARCHAR2 DEFAULT NULL,
                 	pFilterThruMonitor   IN VARCHAR2 DEFAULT NULL,
                 	pProfileId           IN P_MSD_ADDITIONAL_COLUMNS.PROFILE_ID%TYPE DEFAULT NULL
                   ); */

			cstmtMovement = conn
					.prepareCall("{call PK_MOVEMENT_SUMMARY.SP_MOVEMENT_SUMMARY_EX(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)}");
			// Set SP parameters
			// set hostId
			cstmtMovement.setString(1, hostId);
			// Set entityId contains selected entity or logged in user default
			// entityId
			cstmtMovement.setString(2, entityId);
			// Set currency Code contains selected currency or entity's domestic
			// currency
			cstmtMovement.setString(3, currCode);
			// contains selected date or date of balance from where the user
			// drills down or the tab date
			cstmtMovement.setDate(4, SwtUtil.truncateDateTime(valueDate));
			// Selected position level or '0' while drill down from monitor
			// screens
			cstmtMovement.setInt(5, posLevelId);
			// Number of records to be display per page
			cstmtMovement.setInt(6, pageSize);
			// selected page number or '1' by default
			cstmtMovement.setInt(7, currentPage);
			// to pass if All tab is clicked to get all records without respect
			// to date
			cstmtMovement.setString(8, isAllStr);
			// filtered movement details concatenated with '|' with selected or
			// 'All' values
			cstmtMovement.setString(9,
					((filterCriteria == null) ? filterCriteria : filterCriteria
							.replaceAll("amp;", "&").replaceAll("plus;", "+")));
			// flag for including open movements
			cstmtMovement.setString(10, openMovementFlag);
			// contains column based sorting with column index and true /false
			// concatenated with '|'
			cstmtMovement.setString(11, sortCriteria);
			// flag to denote from where movement summary is invoked
			/*
			 * A - Account Monitor,  B - Account Breakdown / Central Bank Monitor,
			 * C - Currency Monitor, E - Excluded Outstanding / MSD / MMSD, K -
			 * Book group Monitor, R - Archive Search, S - Movement Search, W
			 * -Workflow Monitor
			 */
			cstmtMovement.setString(12, sourceScreen);
			// Flag denotes movement type if drill down from workflow monitor or
			// balance type from other screens
			/*
			 * If from work flow monitor following value can be passed, I -
			 * Included Movements Value dated based on the tab date, E - Excluded
			 * Movements Value dated based on the tab date, R - Reconciled
			 * Movements Value dated based on the tab date, B - Back valued
			 * movements, O - Open Unexpected, S - Unsettled Movements
			 */

			/*
			 * If not from work flow monitor following value can be passed P -
			 * Predicted : L - Loro, O - Open Unexpected, E - External, S - Unsettled,
			 * U - Unexpected
			 */
			cstmtMovement.setString(13, balType);
			// to pass selected account Id or when drill down from workflow pass
			// roled Id
			cstmtMovement.setString(14, accountId);
			// Parameter is used to pass different values based on the source
			// screen from where the drill down is initiated
			/*
			 * If the drill down is from Bookgroup monitor, the book code is
			 * passed. If the is from open unexpected balance in Account
			 * Monitor, the account type is passed. If the drill down is from
			 * Workflow monitor, the currency group id is passed. If the drill
			 * down is from Movement / Archive search, then the search
			 * parameters are passed separated by '|' for each value in search crirteria
			 */
			cstmtMovement.setString(15, ((accountType == null) ? accountType
					: accountType.replaceAll("amp;", "&").replaceAll("plus;",
					"+")));
			// fetch indicator to fetch movement or movement count Y/W- to get movements, N- to get movement count
			cstmtMovement.setString(16, dataFetchIndicator);
			// pass currency threshold Y/N
			cstmtMovement.setString(17, applyCurrencyThreshold);
			// Pass Y/N Y- for all position levels from Excluded outstanding N- for others
			cstmtMovement.setString(18, totalFlag);
			// to pass archive dbLink if it is from archive search
			cstmtMovement.setString(19, (sourceScreen.equals("R") ? accountId
					: null));
			// Run SP
			// Register output parameter
			cstmtMovement.registerOutParameter(20,
					oracle.jdbc.OracleTypes.CURSOR);
			// pass scenario id parameter
			cstmtMovement.setString(21, scenarioId);
			// pass role id parameter
			cstmtMovement.setString(22, roleId);
			// pass the search filter parameter
			cstmtMovement.setString(23, searchFilter);
			// Set UserId
			cstmtMovement.setString(24, userId);
			cstmtMovement.setString(25, filterThruMonitor);
			cstmtMovement.setString(26, profileId);

			// Execute SP
			cstmtMovement.execute();
			// If the request is for movement details, then get the details
			// otherwise simply get count flag value
			rsMovement = (ResultSet) cstmtMovement.getObject(20);

			hashMapResult = new HashMap<String, Object>();

			if (dataFetchIndicator.equals("Y")
					|| dataFetchIndicator.equals("W")) {

				// Date format
				sdfInputDate = new SimpleDateFormat("MM/dd/yyyy HH:mm:ss");
				// Time format
				sdfTime = new SimpleDateFormat("HH:mm:ss");
				// Date format defined in System Parameter
				dateFormat = SwtUtil.getCurrentDateFormat(UserThreadLocalHolder
						.getUserSession());
				SimpleDateFormat dateFormatDate = new SimpleDateFormat(dateFormat+" HH:mm:ss");
				// Get match status values for selected entity
				colMiscParams = CacheManager.getInstance().getMiscParams(
						"MATCHSTATUS", entityId);

				CDM = (CommonDataManager)UserThreadLocalHolder.getUserSession().getAttribute("CDM");
				// Iterate through movement details from db and set the value to
				// corresponding property in the Movement object
				StackTraceElement[] traceElements = Thread.currentThread().getStackTrace();

				for (StackTraceElement ste : traceElements) {
					if(ste.getMethodName() != null && "exportMovementScreen".equals(ste.getMethodName()))
					{
						isExportAction = true;
						break;
					}
				}
				while (rsMovement.next()) {

					if(isExportAction) {
						movement = new Movement(null);
						if(CDM != null) {
							if(!SwtUtil.isEmptyOrNull(CDM.getCancelMSDExport()) && "true".equals(CDM.getCancelMSDExport())) {
								throw new SwtException("generatedException");
							}
						}
					}else {
						movement = new Movement();
					}

					// Set movement details to corresponding properties in
					// Movement bean
					// Set scenario highlighted
					//movement.setScenarioHighlighted(rsMovement.getString("SCENARIO_HIGHLIGHTING"));
					movement.setPositionLevelName(rsMovement.getString("POSITION_LEVEL_NAME"));
					movement.setValueDate(new Date(rsMovement.getDate("VALUE_DATE")
							.getTime()));
					movement.setAmount(rsMovement.getDouble("AMOUNT"));
					movement.setSign(rsMovement.getString("SIGN"));
					movement.setCurrencyCode(rsMovement.getString("CURRENCY_CODE"));
					movement.setReference1(rsMovement.getString("REFERENCE1"));
					movement.setAccountId(rsMovement.getString("ACCOUNT_ID"));
					inputDate=SwtUtil.formatDate(sdfInputDate.parse(rsMovement.getString("INPUT_DATE")),dateFormat+" HH:mm:ss");
					movement.setInputDate(dateFormatDate.parse(inputDate));
					movement.setCounterPartyId(rsMovement.getString("COUNTERPARTY_ID"));
					movement.setPredictStatus(rsMovement.getString("PREDICT_STATUS"));
					movement.setMatchStatus(rsMovement.getString("MATCH_STATUS"));
					// Condition to check matchId is not null
					if (Long.valueOf(rsMovement.getLong("MATCH_ID")) != null
							&& rsMovement.getLong("MATCH_ID") != 0) {
						movement.setMatchId(Long
								.valueOf(rsMovement.getLong("MATCH_ID")));
					}
					movement.setInputSource(rsMovement.getString("INPUT_SOURCE"));
					movement.setMessageFormat(rsMovement.getString("MESSAGE_FORMAT"));
					movement.setNotesCount(rsMovement.getInt("NOTES_COUNT"));
					movement.setBeneficiaryId(rsMovement.getString("BENEFICIARY_ID"));
					movement.setReference2(rsMovement.getString("REFERENCE2"));
					movement.setReference3(rsMovement.getString("REFERENCE3"));
					movement.getId().setMovementId(rsMovement.getLong("MOVEMENT_ID"));
					movement.setBookCode(rsMovement.getString("BOOKCODE"));
					movement.setCustodianId(rsMovement.getString("CUSTODIAN_ID"));
					movement.setReference4(rsMovement.getString("REFERENCE4"));
					movement.setPositionLevel(rsMovement.getInt("POSITION_LEVEL"));
					movement.setOpenFlag(rsMovement.getString("OPEN"));
					movement.setUpdateUser(rsMovement.getString("UPDATE_USER"));
					//movement.setExtraText1(rsMovement.getString("EXTRA_TEXT1"));
					movement.setIlmFcastStatus(rsMovement.getString("ILM_FCAST_STATUS"));
					updateDate = rsMovement.getString("UPDATE_DATE");
					// Condition to check updateDate is not null
					if (updateDate != null) {
						String updateDateNew= SwtUtil.formatDate(sdfInputDate.parse(updateDate),dateFormat+" HH:mm:ss");
						movement.setUpDate_Date(dateFormatDate.parse(updateDateNew));
					}
					movement.setMatchingParty(rsMovement.getString("MATCHING_PARTY"));
					movement.setProductType(rsMovement.getString("PRODUCT_TYPE"));
					postingDate = rsMovement.getDate("POSTING_DATE");
					// Condition to check postingDate is not null
					if (postingDate != null) {
						movement
								.setPostingDate(new Date(postingDate.getTime()));
					}
					movement.setExtBalStatus(rsMovement.getString("EXT_BAL_STATUS"));
					movementCount = rsMovement.getInt("RECCNT");

					if(rsMovement.getObject("SUM_ALL_PAGES") != null) {
						hashMapResult.put("totalOverPages", rsMovement.getDouble("SUM_ALL_PAGES"));
					}

					if(rsMovement.getObject("SUM_CURRENT_PAGE") != null) {
						hashMapResult.put("totalInPage", rsMovement.getDouble("SUM_CURRENT_PAGE"));
					}

					// Iterate through match status list and get description for
					// matched value
					if (!SwtUtil.isEmptyOrNull(movement.getMatchStatus())) {
						itrMiscParams = colMiscParams.iterator();
						while (itrMiscParams.hasNext()) {
							// set match status description
							miscParams = itrMiscParams.next();
							if (movement.getMatchStatus().equals(
									miscParams.getId().getKey2())) {
								movement.setMatchStatusDesc(miscParams
										.getParValue()
										+ " " + movement.getUpdateUser());
								break;
							}
						}
					}
					// Set String value of value date
					movement.setValueDateAsString(SwtUtil.formatDate(movement
							.getValueDate(), dateFormat));
					// Set string value of amount
					movement.setAmountAsString(SwtUtil.formatCurrency(movement
							.getCurrencyCode(), movement.getAmount()));
					// Set string value of input date
					if (movement.getInputDate() != null) {
						movement
								.setInputDateAsString(SwtUtil.formatDate(
										movement.getInputDate(), dateFormat)
										+ " "
										+ sdfTime.format(movement
										.getInputDate()));
					}
					// Set string value of update date
					if (movement.getUpDate_Date() != null) {
						movement.setUpdateDateAsString(SwtUtil.formatDate(
								movement.getUpDate_Date(), dateFormat)
								+ " "
								+ sdfTime.format(movement.getUpDate_Date()));
					}
					// Set string value of posting date
					if (movement.getPostingDate() != null) {
						movement.setPostingDateAsString(SwtUtil.formatDate(
								movement.getPostingDate(), dateFormat));
					}
					// Set notes count in string format
					if (movement.getNotesCount() == 0) {
						movement.setHasNotes(SwtConstants.EMPTY_STRING);
					} else {
						movement.setHasNotes(String.valueOf(movement
								.getNotesCount()));
					}
					movement.setAttributeXml(rsMovement.getString("ADDITIONAL_COLS_XML"));
					movement.setUetr(rsMovement.getString("UETR"));
					movement.getId().setEntityId(rsMovement.getString("ENTITY_ID"));


					// Add movement details
					lstMonitorMovement.add(movement);
				}

			} else if (rsMovement.next()) {
				// Get movement count, if movements available
				movementCount = rsMovement.getInt(1);
			}
			hashMapResult.put("totalCount", movementCount);

			return hashMapResult;
		} catch (SQLException ex) {
			// log error message
			log.error(this.getClass().getName()
					+ " - [getMonitorMovements] - SQLException: "
					+ ex.getMessage());
			// Re-throw as SwtException
			SwtException exp = new SwtException(ex.getMessage());

			SwtUtil.logErrorInDatabase(exp);
			throw SwtErrorHandler.getInstance().handleException(ex,
					"getMonitorMovements", this.getClass());
		} catch (HibernateException ex) {
			// log error message
			log.error(this.getClass().getName()
					+ " - [getMonitorMovements] - HibernateException: "
					+ ex.getMessage());
			// Re-throw as SwtException
			throw SwtErrorHandler.getInstance().handleException(ex,
					"getMonitorMovements", this.getClass());
		} catch (ParseException ex) {
			// log error message
			log.error(this.getClass().getName()
					+ " - [getMonitorMovements] - ParseException: "
					+ ex.getMessage());
			// Re-throw as SwtException
			throw SwtErrorHandler.getInstance().handleException(ex,
					"getMonitorMovements", this.getClass());
		} catch (Exception ex) {
			if(!ex.getMessage().contains("generatedException")){
				// log error message
				log.error(this.getClass().getName()
						+ " - [getMonitorMovements] - Exception: "
						+ ex.getMessage());
				// Re-throw as SwtException
				throw SwtErrorHandler.getInstance().handleException(ex,
						"getMonitorMovements", this.getClass());
			}else {
				throw new SwtException("generatedException");
			}
		} finally {

			// edited by KaisBS for mantis 1745 : introducing the implementation
			// of the JDBCCloser class
			JDBCCloser.close(rsMovement, rsCount);
			JDBCCloser.close(cstmtMovement, cstmtCount);
			JDBCCloser.close(session);

			// nullify objects
			colMiscParams = null;
			itrMiscParams = null;
			session = null;
			conn = null;
			cstmtMovement = null;
			cstmtCount = null;
			rsMovement = null;
			rsCount = null;
			movement = null;
			sdfInputDate = null;
			sdfTime = null;
			postingDate = null;
			miscParams = null;
			dateFormat = null;
			totalFlag = null;
			// log debug message
			log.debug(this.getClass().getName()
					+ " - [getMonitorMovements] - Exit");
		}
	}

	public String getValueDateField(String hostId, String entityId,
									Long movementId) throws SwtException {
		log.debug("Entering into getValueDateField");
		List valueDateAsList = getHibernateTemplate()
				.find(
						"select p.valueDate from Movement p where p.id.hostId = ?0 and p.id.entityId = ?1 and p.id.movementId=?2",
						new Object[] { hostId, entityId, movementId });
		String valueDateAsString = null;
		if (valueDateAsList.size() != 0) {
			valueDateAsString = valueDateAsList.get(0).toString();
		}
		log.debug("Exiting into getValueDateField");

		return valueDateAsString;
	}

	/**
	 * This method is called when we click Open/Unopen button on Monitor screen.
	 */
	public void updateOpenUnopenFlag(String hostId, String entityId,
									 Long movementId, String openFlag, String updateUser)
			throws SwtException {
		Session session = null;
		Connection conn = null;
		PreparedStatement preparedStatement = null;

		try {
			if (openFlag.equals(SwtConstants.YES)) {
				openFlag = SwtConstants.NO;
			} else if (openFlag.equals(SwtConstants.NO)) {
				openFlag = SwtConstants.YES;
			}

			session = getHibernateTemplate().getSessionFactory().openSession();
			conn = SwtUtil.connection(session);
			String hibernateQuery = "update p_movement p set p.open=? , " +
					"p.update_date=global_var.sys_date"
					+ ", p.update_user=? where p.host_id=?" +
					" and p.entity_id=? and p.movement_id=?";
			preparedStatement = conn.prepareStatement(hibernateQuery);
			preparedStatement.setString(1, openFlag);
			preparedStatement.setString(2, updateUser);
			preparedStatement.setString(3, hostId);
			preparedStatement.setString(4, entityId);
			preparedStatement.setLong(5, movementId);
			preparedStatement.execute();
		} catch (Exception exception) {

			log.error(this.getClass().getName()
					+ " - [updateOpenUnopenFlag] - SQLException: "
					+ exception.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exception,
					"updateOpenUnopenFlag", MovementDAOHibernate.class);
		} finally {

			// edited by KaisBS for mantis 1745 : introducing the implementation
			// of the JDBCCloser class
			try {
				if (conn != null)
					conn.commit();
			} catch (SQLException e) {
				log
						.error("org.swallow.work.dao.hibernate.MovementDAOHibernate - "
								+ "[updateOpenUnopenFlag] - Exception - "
								+ e.getMessage());
			}

			SwtException thrownException = null;
			Object[] exceptions = JDBCCloser.close(null, preparedStatement,
					null, session);

			if (exceptions[0] != null)
				thrownException = SwtErrorHandler.getInstance()
						.handleException((SQLException) exceptions[0],
								"updateOpenUnopenFlag",
								MovementDAOHibernate.class);

			if (thrownException == null && exceptions[1] != null)
				thrownException = SwtErrorHandler.getInstance()
						.handleException((HibernateException) exceptions[1],
								"updateOpenUnopenFlag",
								MovementDAOHibernate.class);

			if (thrownException != null)
				throw thrownException;

		}
	}

	public String getPositionLevelInternalExternal(String hostId,
												   String entityId, int positionLevel) throws SwtException {
		log.debug("Entering into getPositionLevelInternalExternal");

		Session session = null;
		Connection conn = null;
		PreparedStatement statement = null;
		ResultSet resultSet = null;
		String positionLevelInternalExternal = null;

		try {
			String hibernateQuery = "select internal_external from p_position_level_name where host_id = ? and entity_id = ? and position_level = ?";
			session = getHibernateTemplate().getSessionFactory().openSession();
			conn = SwtUtil.connection(session);
			statement = conn.prepareStatement(hibernateQuery);
			statement.setString(1, hostId);
			statement.setString(2, entityId);
			statement.setInt(3, positionLevel);
			statement.execute();
			resultSet = statement.getResultSet();

			if (resultSet != null) {
				while (resultSet.next()) {
					positionLevelInternalExternal = resultSet.getString(1);
				}
			}
		} catch (HibernateException hibernateException) {
			log.error("Problem in accessing Hibernate properties");
			throw SwtErrorHandler.getInstance().handleException(
					hibernateException, "getPositionLevelInternalExternal",
					MovementDAOHibernate.class);
		} catch (SQLException sqlException) {
			log.error("Problem in executing Query");
			throw SwtErrorHandler.getInstance().handleException(sqlException,
					"getPositionLevelInternalExternal",
					MovementDAOHibernate.class);
		} finally {

			SwtException thrownException = null;
			Object[] exceptions = JDBCCloser.close(resultSet, statement, null,
					session);

			if (exceptions[0] != null)
				thrownException = SwtErrorHandler.getInstance()
						.handleException((SQLException) exceptions[0],
								"getPositionLevelInternalExternal",
								MovementDAOHibernate.class);

			if (thrownException == null && exceptions[1] != null)
				thrownException = SwtErrorHandler.getInstance()
						.handleException((HibernateException) exceptions[1],
								"getPositionLevelInternalExternal",
								MovementDAOHibernate.class);

			if (thrownException != null)
				throw thrownException;
		}

		log.debug("Exiting into getPositionLevelInternalExternal");

		return positionLevelInternalExternal;
	}

	/**
	 * @param movementIds
	 * @param entityId
	 * @param hostId
	 * @return boolean
	 * @throws SwtException
	 */
	public boolean checkMovementStatus(String movementIds, String entityId,
									   String hostId) throws SwtException {
		log.debug("Entering checkMovementStatus method");
		boolean mvtFlag = false;
		List movementList = (List ) getHibernateTemplate().find(
				"from Movement m where m.id.hostId=?0 and m.id.entityId=?1 and m.id.movementId in ("+ movementIds +") and m.matchStatus not in ('A')", new Object[]{ hostId, entityId });
		if (movementIds.split(",").length == movementList.size()) {
			mvtFlag = true;
		}
		log.debug("Exiting checkMovementStatus method");
		return mvtFlag;
	}

	/**
	 * Method to check external position level to change the external balance.
	 *
	 * @param entity
	 * @return boolean
	 */
	public boolean checkExternalPositionLevel(Entity entity)
			throws SwtException {
		// flag to ensure external balance presence
		boolean posFlag = false;
		// List to hold entity collection
		List entityList = null;
		try {
			log.debug(this.getClass().getName()
					+ " -[checkExternalPositionLevel]-Entry");

			// Obtain the entity list for given parameters to check whether
			// external balance is exist
			entityList = (List ) getHibernateTemplate().find(
					"from Entity e where e.id.hostId='"
							+ entity.getId().getHostId() + "'"
							+ "and e.id.entityId='"
							+ entity.getId().getEntityId()
							+ "' and e.externalBalance in ("
							+ entity.getExternalBalance() + ")");

			/* Set true if given position level is not external balance */
			if (entityList.size() == 0) {
				posFlag = true;
			}
		} catch (Exception exp) {
			log.error("Error in " + this.getClass().getName()
					+ " - [checkExternalPositionLevel] - " + exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp,
					"getPositionLevelInternalExternal",
					MovementDAOHibernate.class);
		} finally {
			log.debug(this.getClass().getName()
					+ " -[checkExternalPositionLevel]-Exit");
		}
		// return flag value
		return posFlag;
	}// End of checkExternalPositionLevel

	/**
	 * This Method is used to return the Cross Reference Object List for
	 * corresponding Movement
	 *
	 * @param host
	 * @param entity
	 * @param movement
	 * @throws SwtException
	 */
	@SuppressWarnings("unchecked")
	public ArrayList<CrossReference> getCrossReference(String host,
													   String entity, Long movement) throws SwtException {
		/* Variable Declaration for crossReferenceList */
		ArrayList<CrossReference> crossReferenceList = null;
		try {
			log
					.debug(this.getClass().getName()
							+ " -[getCrossReference]-Enter");
			/* get Cross Reference Object from Database. */
			crossReferenceList = (ArrayList<CrossReference>) getHibernateTemplate()
					.find(HQL_CROSS_REFERENCELIST,
							new Object[] { host, entity, movement });
			log.debug(this.getClass().getName() + " -[getCrossReference]-Exit");
		} catch (Exception e) {
			log.error("Error in " + this.getClass().getName()
					+ " - [getCrossReference] - " + e.getMessage());
			SwtErrorHandler swtErrorHandler = SwtErrorHandler.getInstance();
			SwtException swtexp = swtErrorHandler.handleException(e,
					"getCrossReference", MovementDAOHibernate.class);
			throw swtexp;
		}
		return crossReferenceList;
	}

	/**
	 * Method to get External Balance for selected entity
	 *
	 * @param entity
	 * @return ExternalBalance
	 */
	@SuppressWarnings("unchecked")
	public String getEntityDefaultPositionlevel(Entity entity)
			throws SwtException {
		// Setting entityExternal in List
		List<Integer> entityExternal = null;
		// Setting externalBalance as integer
		Integer externalBalance = null;
		try {
			log.debug(this.getClass().getName()
					+ " -[getEntityDefaultPositionlevel]-Enter");
			/* get EntityDefaultPositionlevel from Database. */
			entityExternal = (List<Integer> ) getHibernateTemplate().find(
					"select ent.externalBalance from Entity ent where ent.id.hostId=?0 and ent.id.entityId=?1",
					new Object[] { entity.getId().getHostId(),
							entity.getId().getEntityId() });

			externalBalance = (Integer) entityExternal.toArray()[0];
			log.debug(this.getClass().getName()
					+ " -[getEntityDefaultPositionlevel]-Exit");
		} catch (Exception e) {
			log.error("Error in " + this.getClass().getName()
					+ " - [getEntityDefaultPositionlevel] - " + e.getMessage());
			SwtErrorHandler swtErrorHandler = SwtErrorHandler.getInstance();
			SwtException swtexp = swtErrorHandler
					.handleException(e, "getEntityDefaultPositionlevel",
							MovementDAOHibernate.class);
			throw swtexp;
		}
		return externalBalance.toString();
	}

	/**
	 * This method used to get the archived movements from the archive schema
	 *
	 * @param entityId
	 * @param movementId
	 * @param archiveId
	 * @return Movement
	 * @throws SwtException
	 */
	public Movement getArchiveMovementDetails(String entityId, Long movementId,
											  String archiveId) throws SwtException {

		// session declared to hold the hibernate session
		Session session = null;
		// Variable connection declared to get DB connection
		Connection connection = null;
		// Callable statment declared to execute query to get movements and its
		// details from the Archive schema
		CallableStatement callableStatement = null;
		// string declared to hold the dblink value
		String dbLink = null;
		// Statement declared to excecute query to get dblink
		Statement statement = null;
		// resultset to get the dblink
		ResultSet resultset = null;
		// resultset to get the movements and details
		ResultSet resultsetMovement = null;
		// object to hold the movement details
		Movement movement = null;
		try {
			log.debug(this.getClass().getName()
					+ " -[getArchiveMovementDetails]-Entry");

			// following code is to get the db_link of archive id
			try {
				ArchiveManager archiveManager = (ArchiveManager) SwtUtil.getBean("archiveManager");
				Archive archive = archiveManager.getArchiveById(CacheManager.getInstance().getHostId(), archiveId);
				if (archive != null) {
					dbLink = archive.getDb_link();
				}
			} catch (Exception e) {
				log.error("Error getting archive information: " + e.getMessage());
				// Fallback to original query if needed
				session = getHibernateTemplate().getSessionFactory().openSession();
				connection = SwtUtil.connection(session);
				statement = connection.createStatement();
				statement.executeQuery("select DBLINK_SCHEMA_NAME from p_Archive where archive_id='" + archiveId + "'");
				resultset = statement.getResultSet();
				if (resultset != null) {
					while (resultset.next()) {
						dbLink = resultset.getString(1);
					}
				}
			}

			// following code is execution of procedure to get the movement
			// details from the archive schema
			// Make a callable statement for executing the procedure
			callableStatement = connection
					.prepareCall("{call PKG_ARCHIVE_DETAILS.SPSHOWARCHMOVEMENT(?,?,?,?,?,?)}");
			callableStatement.setString(1, CacheManager.getInstance()
					.getHostId());

			callableStatement.setString(2, entityId);
			callableStatement.setLong(3, movementId);
			callableStatement.setString(4, "N");
			callableStatement.setString(5, dbLink);

			callableStatement.registerOutParameter(6,
					oracle.jdbc.OracleTypes.CURSOR);
			callableStatement.execute();
			resultsetMovement = (ResultSet) callableStatement.getObject(6);
			if (resultsetMovement != null) {

				while (resultsetMovement.next()) {
					movement = new Movement();

					movement.getId()
							.setMovementId(resultsetMovement.getLong("MOVEMENT_ID"));

					movement.getId()
							.setEntityId(resultsetMovement.getString("ENTITY_ID"));
					movement.setPositionLevel(resultsetMovement.getInt("POSITION_LEVEL"));
					movement.setAmount(resultsetMovement.getDouble("AMOUNT"));
					movement.setSign(resultsetMovement.getString("SIGN"));
					movement.setCurrencyCode(resultsetMovement.getString("CURRENCY_CODE"));
					movement.setAccttype(resultsetMovement.getString("MOVEMENT_TYPE"));
					movement.setAccountId(resultsetMovement.getString("ACCOUNT_ID"));
					movement.setBookCode(resultsetMovement.getString("BOOKCODE"));
					movement.setCounterPartyId(resultsetMovement.getString("COUNTERPARTY_ID"));
					movement.setCounterPartyText1(resultsetMovement
							.getString("COUNTERPARTY_TEXT1"));
					movement.setCounterPartyText2(resultsetMovement
							.getString("COUNTERPARTY_TEXT2"));
					movement.setCounterPartyText3(resultsetMovement
							.getString("COUNTERPARTY_TEXT3"));
					movement.setCounterPartyText4(resultsetMovement
							.getString("COUNTERPARTY_TEXT4"));
					movement.setBeneficiaryId(resultsetMovement.getString("BENEFICIARY_ID"));
					movement.setBeneficiaryText1(resultsetMovement
							.getString("BENEFICIARY_TEXT1"));
					movement.setBeneficiaryText2(resultsetMovement
							.getString("BENEFICIARY_TEXT2"));
					movement.setBeneficiaryText3(resultsetMovement
							.getString("BENEFICIARY_TEXT3"));
					movement.setBeneficiaryText4(resultsetMovement
							.getString("BENEFICIARY_TEXT4"));
					movement.setMatchingParty(resultsetMovement.getString("MATCHING_PARTY"));
					movement.setProductType(resultsetMovement.getString("PRODUCT_TYPE"));
					movement.setPostingDate(resultsetMovement.getDate("POSTING_DATE"));
					movement.setInputSource(resultsetMovement.getString("INPUT_SOURCE"));
					movement.setMessageFormat(resultsetMovement.getString("MESSAGE_FORMAT"));
					movement.setPredictStatus(resultsetMovement.getString("PREDICT_STATUS"));
					movement.setExtBalStatus(resultsetMovement.getString("EXT_BAL_STATUS"));
					movement.setMatchStatus(resultsetMovement.getString("MATCH_STATUS"));
					movement.setReference1(resultsetMovement.getString("REFERENCE1"));
					movement.setReference2(resultsetMovement.getString("REFERENCE2"));
					movement.setReference3(resultsetMovement.getString("REFERENCE3"));
					movement.setReference4(resultsetMovement.getString("REFERENCE4"));
					if (resultsetMovement.getLong("MATCH_ID") != 0) {
						movement.setMatchId(resultsetMovement.getLong("MATCH_ID"));
					} else {
						movement.setMatchId(null);
					}
					movement.setNotesCount(resultsetMovement.getInt("NOTES_COUNT"));
					movement.setValueDate(resultsetMovement.getDate("VALUE_DATE"));

					movement.setSettlementDateTime(resultsetMovement.getTimestamp("SETTLEMENT_DATETIME"));
					movement.setExpectedSettlementDateTime(resultsetMovement.getTimestamp("EXPECTED_SETTLEMENT_DATETIME"));
					movement.setCriticalPaymentType(resultsetMovement.getString("CRITICAL_PAYMENT_TYPE"));

					movement.setCustodianId(resultsetMovement.getString("CUSTODIAN_ID"));
					movement.setCustodianText1(resultsetMovement.getString("CUSTODIAN_TEXT1"));
					movement.setCustodianText2(resultsetMovement.getString("CUSTODIAN_TEXT2"));
					movement.setCustodianText3(resultsetMovement.getString("CUSTODIAN_TEXT3"));
					movement.setCustodianText4(resultsetMovement.getString("CUSTODIAN_TEXT4"));
					movement.setCustodianText5(resultsetMovement.getString("CUSTODIAN_TEXT5"));

					movement.setCounterPartyText5(resultsetMovement.getString("COUNTERPARTY_TEXT5"));
					movement.setBeneficiaryText5(resultsetMovement.getString("BENEFICIARY_TEXT5"));
					movement.setOrderingCustomerId(resultsetMovement.getString("ORDERING_CUSTOMER"));
					movement.setOrderingInstitutionId(resultsetMovement.getString("ORDERING_INSTITUTION"));
					movement.setSenderCorrespondentId(resultsetMovement.getString("SENDERS_CORRES"));
					movement.setReceiverCorrespondentId(resultsetMovement.getString("RECEIVERS_CORRES"));
					movement.setIntermediaryInstitutionId(resultsetMovement.getString("INTMDRY_INSTITUTION_ID"));
					movement.setAccountWithInstitutionId(resultsetMovement.getString("ACC_WITH_INSTITUTION_ID"));
					movement.setBeneficiaryCustomerId(resultsetMovement.getString("BENEFICIARY_CUST"));
					movement.setExtraText1(resultsetMovement.getString("EXTRA_TEXT1"));
					movement.setIlmFcastStatus(resultsetMovement.getString("ILM_FCAST_STATUS"));
					movement.setUetr(resultsetMovement.getString("UETR"));

					MovementExt ext = new MovementExt();

					ext.getId().setEntityId(movement.getId().getEntityId());
					ext.getId().setHostId(movement.getId().getHostId());
					ext.getId().setMovementId(movement.getId().getMovementId());

					ext.setOrderingCustomer1(resultsetMovement.getString("ORDERING_CUSTOMER_TEXT1"));
					ext.setOrderingCustomer2(resultsetMovement.getString("ORDERING_CUSTOMER_TEXT2"));
					ext.setOrderingCustomer3(resultsetMovement.getString("ORDERING_CUSTOMER_TEXT3"));
					ext.setOrderingCustomer4(resultsetMovement.getString("ORDERING_CUSTOMER_TEXT4"));
					ext.setOrderingCustomer5(resultsetMovement.getString("ORDERING_CUSTOMER_TEXT5"));

					ext.setOrderingInstitution1(resultsetMovement.getString("ORDERING_INSTITUTION_TEXT1"));
					ext.setOrderingInstitution2(resultsetMovement.getString("ORDERING_INSTITUTION_TEXT2"));
					ext.setOrderingInstitution3(resultsetMovement.getString("ORDERING_INSTITUTION_TEXT3"));
					ext.setOrderingInstitution4(resultsetMovement.getString("ORDERING_INSTITUTION_TEXT4"));
					ext.setOrderingInstitution5(resultsetMovement.getString("ORDERING_INSTITUTION_TEXT5"));

					ext.setSenderCorrespondent1(resultsetMovement.getString("SENDERS_CORRES_TEXT1"));
					ext.setSenderCorrespondent2(resultsetMovement.getString("SENDERS_CORRES_TEXT2"));
					ext.setSenderCorrespondent3(resultsetMovement.getString("SENDERS_CORRES_TEXT3"));
					ext.setSenderCorrespondent4(resultsetMovement.getString("SENDERS_CORRES_TEXT4"));
					ext.setSenderCorrespondent5(resultsetMovement.getString("SENDERS_CORRES_TEXT5"));

					ext.setReceiverCorrespondent1(resultsetMovement.getString("RECEIVERS_CORRES_TEXT1"));
					ext.setReceiverCorrespondent2(resultsetMovement.getString("RECEIVERS_CORRES_TEXT2"));
					ext.setReceiverCorrespondent3(resultsetMovement.getString("RECEIVERS_CORRES_TEXT3"));
					ext.setReceiverCorrespondent4(resultsetMovement.getString("RECEIVERS_CORRES_TEXT4"));
					ext.setReceiverCorrespondent5(resultsetMovement.getString("RECEIVERS_CORRES_TEXT5"));

					ext.setIntermediaryInstitution1(resultsetMovement.getString("INTMDRY_INSTITUTION_TEXT1"));
					ext.setIntermediaryInstitution2(resultsetMovement.getString("INTMDRY_INSTITUTION_TEXT2"));
					ext.setIntermediaryInstitution3(resultsetMovement.getString("INTMDRY_INSTITUTION_TEXT3"));
					ext.setIntermediaryInstitution4(resultsetMovement.getString("INTMDRY_INSTITUTION_TEXT4"));
					ext.setIntermediaryInstitution5(resultsetMovement.getString("INTMDRY_INSTITUTION_TEXT5"));

					ext.setAccountWithInstitution1(resultsetMovement.getString("ACC_WITH_INSTITUTION_TEXT1"));
					ext.setAccountWithInstitution2(resultsetMovement.getString("ACC_WITH_INSTITUTION_TEXT2"));
					ext.setAccountWithInstitution3(resultsetMovement.getString("ACC_WITH_INSTITUTION_TEXT3"));
					ext.setAccountWithInstitution4(resultsetMovement.getString("ACC_WITH_INSTITUTION_TEXT4"));
					ext.setAccountWithInstitution5(resultsetMovement.getString("ACC_WITH_INSTITUTION_TEXT5"));

					ext.setBeneficiaryCustomer1(resultsetMovement.getString("BENEFICIARY_CUST_TEXT1"));
					ext.setBeneficiaryCustomer2(resultsetMovement.getString("BENEFICIARY_CUST_TEXT2"));
					ext.setBeneficiaryCustomer3(resultsetMovement.getString("BENEFICIARY_CUST_TEXT3"));
					ext.setBeneficiaryCustomer4(resultsetMovement.getString("BENEFICIARY_CUST_TEXT4"));
					ext.setBeneficiaryCustomer5(resultsetMovement.getString("BENEFICIARY_CUST_TEXT5"));

					ext.setSenderToReceiverInfo1(resultsetMovement.getString("SENDER_TO_RECEIVER_INFO1"));
					ext.setSenderToReceiverInfo2(resultsetMovement.getString("SENDER_TO_RECEIVER_INFO2"));
					ext.setSenderToReceiverInfo3(resultsetMovement.getString("SENDER_TO_RECEIVER_INFO3"));
					ext.setSenderToReceiverInfo4(resultsetMovement.getString("SENDER_TO_RECEIVER_INFO4"));
					ext.setSenderToReceiverInfo5(resultsetMovement.getString("SENDER_TO_RECEIVER_INFO5"));
					ext.setSenderToReceiverInfo6(resultsetMovement.getString("SENDER_TO_RECEIVER_INFO6"));
					ext.setExtraText1(resultsetMovement.getString("EXTRA_TEXT1"));
					movement.setMovementExt(ext);

				}
			}

		} catch (Exception e) {
			log.error("Error in " + this.getClass().getName()
					+ " - [getArchiveMovementDetails] - " + e.getMessage());

			throw SwtErrorHandler.getInstance().handleException(e,
					"getArchiveMovementDetails", MovementDAOHibernate.class);
		} finally {
			// Edited by KaisBS for mantis 1745 : introducing the implementation
			// of the JDBCCloser class
			SwtException thrownException = null;
			SQLException sqlException = null;
			HibernateException hException = null;
			try {
				if (connection != null)
					connection.commit();
			} catch (Exception exception) {
				log.error(this.getClass().getName()
						+ " - [getArchiveMatchNoteDetails] - SQLException: "
						+ exception.getMessage());

				thrownException = SwtErrorHandler.getInstance()
						.handleException(exception,
								"getArchiveMovementDetails",
								MovementDAOHibernate.class);
			}

			sqlException = JDBCCloser.close(resultset, resultsetMovement);
			if (thrownException == null && sqlException != null)
				thrownException = SwtErrorHandler.getInstance()
						.handleException(sqlException,
								"getArchiveMovementDetails",
								MovementDAOHibernate.class);

			sqlException = JDBCCloser.close(statement, callableStatement);
			if (thrownException == null && sqlException != null)
				thrownException = SwtErrorHandler.getInstance()
						.handleException(sqlException,
								"getArchiveMovementDetails",
								MovementDAOHibernate.class);

			hException = JDBCCloser.close(session);
			if (thrownException == null && hException != null)
				thrownException = SwtErrorHandler.getInstance()
						.handleException(hException,
								"getArchiveMovementDetails",
								MovementDAOHibernate.class);

			if (thrownException != null)
				throw thrownException;

			dbLink = null;
			log.debug(this.getClass().getName()
					+ " -[getArchiveMovementDetails]-Exit");
		}
		return movement;
	}

	/**
	 * Check if orginal and new vaules are equals
	 * @param oldString
	 * @param newString
	 * @return
	 */
	private boolean areEquals(Object oldString, Object newString) {
		if(oldString == null && newString == null)
			return true;
		else if(oldString != null && newString == null)
			return false;
		else if (oldString == null && newString != null)
			return false;
		else if (oldString != null && newString != null && oldString.equals(newString))
			return true;
		else if (oldString != null && newString != null && !oldString.equals(newString))
			return false;
		else
			return false;

	}

	/**
	 * This Method updated the orginal values of movements if they are different from the stored ones
	 *
	 *
	 */
	public void updateOriginalValues(Movement newMovement, Movement oldMovement)  throws SwtException {
		Connection conn = null;
		PreparedStatement st = null;
		String updateQuery = null;
		// update query
		int changes = 0;
		HashMap<String, HashMap<String, Object>> changesMap = null;
		HashMap<String, Object> typeValue = null;
		try {
			log.debug(this.getClass().getName()
					+ " - [updateMovementDetailsSQL()] - " + "Entering");
			// get the connection
			conn=ConnectionManager.getInstance().databaseCon();
			// update query
			changesMap = new HashMap<String, HashMap<String, Object>>();
			updateQuery = "update p_movement p set";
			if(!areEquals(oldMovement.getCounterPartyId(), newMovement.getCounterPartyId())) {
				updateQuery+= "  p.original_counterparty_id          =  ? ,";
				changes++;
				typeValue = new HashMap<String, Object>();
				typeValue.put("type", "String");
				typeValue.put("value", newMovement.getCounterPartyId());
				changesMap.put(""+changes, typeValue);
			}
			if(!areEquals(oldMovement.getBeneficiaryId(), newMovement.getBeneficiaryId())) {
				updateQuery+= " p.original_beneficiary_id          =  ? ,";
				changes++;
				typeValue = new HashMap<String, Object>();
				typeValue.put("type", "String");
				typeValue.put("value", newMovement.getBeneficiaryId());
				changesMap.put(""+changes, typeValue);
			}
			if(!areEquals(oldMovement.getExpectedSettlementDateTime(), newMovement.getExpectedSettlementDateTime())) {
				updateQuery+= " p.original_expected_settlemnt_dt   =  ? ,";
				changes++;
				typeValue = new HashMap<String, Object>();
				typeValue.put("type", "Date");
				if(newMovement.getExpectedSettlementDateTime() == null)
					typeValue.put("value", null);
				else
					typeValue.put("value", new java.sql.Date(newMovement.getExpectedSettlementDateTime().getTime()));

				changesMap.put(""+changes, typeValue);
			}
			if(!areEquals(oldMovement.getCriticalPaymentType(), newMovement.getCriticalPaymentType())){
				updateQuery+= " p.original_critical_payment_type          =  ? ,";
				changes++;
				typeValue = new HashMap<String, Object>();
				typeValue.put("type", "String");
				typeValue.put("value", newMovement.getCriticalPaymentType());
				changesMap.put(""+changes, typeValue);
			}
			if(!areEquals(oldMovement.getOrderingCustomerId(), newMovement.getOrderingCustomerId())) {
				updateQuery+= " p.original_ordering_customer          =  ? ,";
				changes++;
				typeValue = new HashMap<String, Object>();
				typeValue.put("type", "String");
				typeValue.put("value", newMovement.getOrderingCustomerId());
				changesMap.put(""+changes, typeValue);
			}
			if(!areEquals(oldMovement.getOrderingInstitutionId(), newMovement.getOrderingInstitutionId())) {
				updateQuery+= " p.original_ordering_institution          =  ? ,";
				changes++;
				typeValue = new HashMap<String, Object>();
				typeValue.put("type", "String");
				typeValue.put("value", newMovement.getOrderingInstitutionId());
				changesMap.put(""+changes, typeValue);
			}
			if(!areEquals(oldMovement.getSenderCorrespondentId(), newMovement.getSenderCorrespondentId())) {
				updateQuery+= " p.original_senders_corres          =  ? ,";
				changes++;
				typeValue = new HashMap<String, Object>();
				typeValue.put("type", "String");
				typeValue.put("value", newMovement.getSenderCorrespondentId());
				changesMap.put(""+changes, typeValue);
			}
			if(!areEquals(oldMovement.getReceiverCorrespondentId(), newMovement.getReceiverCorrespondentId())) {
				updateQuery+= " p.original_receivers_corres          =  ? ,";
				changes++;
				typeValue = new HashMap<String, Object>();
				typeValue.put("type", "String");
				typeValue.put("value", newMovement.getReceiverCorrespondentId());
				changesMap.put(""+changes, typeValue);
			}
			if(!areEquals(oldMovement.getBeneficiaryCustomerId(), newMovement.getBeneficiaryCustomerId())) {
				updateQuery+= " p.original_beneficiary_cust          =  ? ,";
				changes++;
				typeValue = new HashMap<String, Object>();
				typeValue.put("type", "String");
				typeValue.put("value", newMovement.getBeneficiaryCustomerId());
				changesMap.put(""+changes, typeValue);
			}
			if(!areEquals(oldMovement.getIntermediaryInstitutionId(), newMovement.getIntermediaryInstitutionId())) {
				updateQuery+= " p.original_intmdry_institut_id          =  ? ,";
				changes++;
				typeValue = new HashMap<String, Object>();
				typeValue.put("type", "String");
				typeValue.put("value", newMovement.getIntermediaryInstitutionId());
				changesMap.put(""+changes, typeValue);
			}
			if(!areEquals(oldMovement.getAccountWithInstitutionId(), newMovement.getAccountWithInstitutionId())) {
				updateQuery+= " p.original_acc_with_institut_id          =  ? ,";
				changes++;
				typeValue = new HashMap<String, Object>();
				typeValue.put("type", "String");
				typeValue.put("value", newMovement.getAccountWithInstitutionId());
				changesMap.put(""+changes, typeValue);
			}


			if(changes>0) {
				if(updateQuery.endsWith(",")) {
					updateQuery = updateQuery.substring(0, updateQuery.length()-1);
				}
				updateQuery+= " where p.host_id=?  and p.entity_id=? and p.movement_id=?";
				st=conn.prepareStatement(updateQuery);
				for (Entry<String, HashMap<String, Object>> entry : changesMap.entrySet()) {
					String key = entry.getKey();
					HashMap<String, Object> value = entry.getValue();
					if(value.get("type") != null && value.get("type").equals("String")) {
						st.setString(Integer.parseInt(key), ""+value.get("value"));
					}else if(value.get("type") != null && value.get("type").equals("Date")) {
						st.setDate(Integer.parseInt(key), (java.sql.Date) value.get("value"));
					}
				}

				st.setString(changes+1, oldMovement.getId().getHostId());
				st.setString(changes+2, oldMovement.getId().getEntityId());
				st.setLong(changes+3, oldMovement.getId().getMovementId());

				st.execute();
				conn.commit();
			}
		} catch (SQLException e) {
			try {
				if(conn != null && !conn.isClosed()){
					conn.rollback();
				}
			} catch (Exception e2) {
				// TODO: handle exception
			}
			log.error("SQLEXCEPTION IN MovementManagerImpl.updateMovementDetailsSQL "
					+ e.getMessage());
		} catch (Exception e) {
			log
					.error("EXCEPTION PROBLEM IN MovementManagerImpl.updateMovementDetailsSQL :  "
							+ e.getMessage());
		} finally {
			JDBCCloser.close(st);
			JDBCCloser.close(conn);
		}
	}



	/**
	 * This method is used to save Additional columns
	 */

	public void saveProfileAdditionalCols(String hostId, String userId, String profileId, List<MsdAdditionalColumns> listMsd) throws SwtException {

		Session session = null;
		Transaction tx = null;
		SwtInterceptor interceptor = null;

		try {
			log.debug(this.getClass().getName() + " - [saveProfileAdditionalCols] - Enter");
			interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");
			session = getHibernateTemplate().getSessionFactory().withOptions().interceptor(interceptor).openSession();
			tx = session.beginTransaction();
			if (listMsd != null && listMsd.size() > 0) {
				for (Iterator iterator = listMsd.iterator(); iterator.hasNext();) {
					MsdAdditionalColumns msdCSols = (MsdAdditionalColumns) iterator.next();
					session.save(msdCSols);

				}
			}
			tx.commit();
			log.debug(this.getClass().getName() + " - [saveProfileAdditionalCols] - Exit");
		} catch (Exception e) {
			try {
				if (tx != null) {
					tx.rollback();
				}
			} catch (HibernateException exception) {
				log.error("HibernateException occured in [saveProfileAdditionalCols] when rolling back transaction. Cause : "
						+ exception.getMessage());
			}
			log.error(this.getClass().getName() + " - Exception Catched in [saveProfileAdditionalCols] method : - " + e.getMessage());

			throw SwtErrorHandler.getInstance().handleException(e, "saveProfileAdditionalCols", this.getClass());
		} finally {
			JDBCCloser.close(session);
		}
	}

	/**
	 * This method is used to get profile list
	 */

	public List getSavedProfiles(String hostId, String userId) throws SwtException {

		log.debug(this.getClass().getName() + " - [getSavedProfiles] - "
				+ "Entry");
		/* Method's local variable declaration */
		Connection conn = null;
		Session session = null;
		PreparedStatement stmt = null;
		ResultSet rs = null;
		String profile = null;
		java.util.List profileList= null;
		try {
			conn = ConnectionManager.getInstance().databaseCon();

			profileList = (List ) getHibernateTemplate().find(
					"select distinct m.profileId from MsdAdditionalColumns m where m.hostId = ?0 and m.userId = ?1 order by m.profileId asc",
					new Object[] { hostId, userId });

			log.debug(this.getClass().getName() + " - [getSavedProfiles] - "
					+ "Exit");
			//return mvtType;
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [getSavedProfiles] method : - "
					+ exp.getMessage());
		} finally {
			JDBCCloser.close(rs, stmt, conn, session);

		}
		return profileList;
	}

	/**
	 * This method is used to get the additional columns for a provided profile
	 */
	public Collection<MsdAdditionalColumns> getAdditionalsColsList(String hostId, String profileId, String userId)
			throws SwtException {

		List profileList = null;

		log.debug(this.getClass().getName() + " - [getAdditionalsColsList] - " + "Entry");
		try {


			profileList = (List ) getHibernateTemplate().find(
					"from MsdAdditionalColumns m where m.hostId=?0" + " and m.profileId=?1 and m.userId=?2 order by m.sequenceKey asc",
					new Object[] { hostId, profileId, userId });
		} catch (Exception e) {
			log.error(this.getClass().getName() + " - Exception Catched in [getAdditionalsColsList] method : - "
					+ e.getMessage());

			throw SwtErrorHandler.getInstance().handleException(e, "getAdditionalsColsList",
					MovementDAOHibernate.class);
		}

		log.debug(this.getClass().getName() + " - [getAdditionalsColsList] - " + "Exit");

		return profileList;

	}


	public void deleteProfileData(String hostId, String profileId, String userId) throws SwtException {

		log.debug("entering 'deleteProfileData' method");

		Session session = null;
		Transaction tx = null;
		SwtInterceptor interceptor = null;
		try {

			List profileList = (List ) getHibernateTemplate().find("from MsdAdditionalColumns msd where  msd.hostId=?0 and  msd.profileId=?1 and  msd.userId=?2",
					new Object[]{hostId, profileId, userId});

			interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");
			session = getHibernateTemplate().getSessionFactory().withOptions().interceptor(interceptor).openSession();
			tx = session.beginTransaction();
			if(profileList != null)
			{
				Iterator itr = 	profileList.iterator();
				while(itr.hasNext())
				{
					Object obj = itr.next();
					session.delete(obj);
				}
			}

			tx.commit();
			log.debug("exiting 'deleteProfileData' method");
		} catch (Exception e) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [deleteProfileData] method : - " + e.getMessage());

			throw SwtErrorHandler.getInstance().handleException(e, "deleteProfileData",
					MovementDAOHibernate.class);
		}finally {
			JDBCCloser.close(session);
		}

	}


	public void crudAddColsMapping(List<MsdAdditionalColumns> listColAdd, List<MsdAdditionalColumns> listColUpdate,
								   List<MsdAdditionalColumns> listColDelete) throws SwtException  {

		List records = null;
		Session session = null;
		Transaction tx = null;
		SwtInterceptor interceptor = null;

		try {
			log.debug(this.getClass().getName() + " - [crudAddColsMapping] - Enter");
			interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");
			session = getHibernateTemplate().getSessionFactory().withOptions().interceptor(interceptor).openSession();
			tx = session.beginTransaction();
			if (listColAdd != null && listColAdd.size() > 0) {
				for (Iterator iterator = listColAdd.iterator(); iterator.hasNext();) {
					MsdAdditionalColumns addCol = (MsdAdditionalColumns) iterator.next();
					session.save(addCol);

				}
			}
			if (listColUpdate != null && listColUpdate.size() > 0) {
				for (Iterator iterator = listColUpdate.iterator(); iterator.hasNext();) {
					MsdAdditionalColumns colUpdate = (MsdAdditionalColumns) iterator.next();
					// Get available lists of categories for the specific spread process point
					records = (List ) getHibernateTemplate().find("from MsdAdditionalColumns cols where cols.sequenceKey =?0",
							new Object[] { colUpdate.getSequenceKey() });

					if (!records.isEmpty()) {
						session.update(colUpdate);
					}

				}
			}
			if (listColDelete != null && listColDelete.size() > 0) {
				for (Iterator iterator = listColDelete.iterator(); iterator.hasNext();) {
					MsdAdditionalColumns colDelete = (MsdAdditionalColumns) iterator.next();

					session.delete(colDelete);
				}
			}

			tx.commit();
			log.debug(this.getClass().getName() + " - [crudAddColsMapping] - Exit");
		} catch (Exception e) {
			try {
				if (tx != null) {
					tx.rollback();
				}
			} catch (HibernateException exception) {
				log.error("HibernateException occured in [crudAddColsMapping] when rolling back transaction. Cause : " + exception.getMessage());
			}
			log.error(this.getClass().getName() + " - Exception Catched in [crudAddColsMapping] method : - " + e.getMessage());

			throw SwtErrorHandler.getInstance().handleException(e, "crudAddColsMapping", this.getClass());
		} finally {
			JDBCCloser.close(session);
		}
	}




	/**
	 * This method is used to get table  columns
	 */

	public List getTableColumns(String table) throws SwtException {

		log.debug(this.getClass().getName() + " - [getTableColumns] - "
				+ "Entry");
		/* Method's local variable declaration */
		Connection conn = null;
		Session session = null;
		PreparedStatement stmt = null;
		ResultSet rs = null;
		java.util.List colsList= null;
		try {
			conn = ConnectionManager.getInstance().databaseCon();

			colsList = getHibernateTemplate().find(
					"select m.columnName  from MsdDisplayColumns m where m.tableName=?0",
					new Object[] { table });

			log.debug(this.getClass().getName() + " - [getTableColumns] - "
					+ "Exit");
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [getTableColumns] method : - "
					+ exp.getMessage());
		} finally {
			JDBCCloser.close(rs, stmt, conn, session);

		}
		return colsList;
	}



	/**
	 * This method is used to get table list
	 */

	public List getTableNames() throws SwtException {

		log.debug(this.getClass().getName() + " - [getTableNames] - " + "Entry");
		/* Method's local variable declaration */
		Connection conn = null;
		Session session = null;
		PreparedStatement stmt = null;
		ResultSet rs = null;
		java.util.List tableList = null;
		try {
			conn = ConnectionManager.getInstance().databaseCon();

			tableList = getHibernateTemplate().find("select distinct m.tableName from MsdDisplayColumns m where m.tableName is not null order by m.tableName asc");

			log.debug(this.getClass().getName() + " - [getTableNames] - " + "Exit");
		} catch (Exception exp) {
			log.error(this.getClass().getName() + " - Exception Catched in [getTableNames] method : - "
					+ exp.getMessage());
		} finally {
			JDBCCloser.close(rs, stmt, conn, session);

		}
		return tableList;
	}


	public Collection<MsdDisplayColumns> getMsdDisplayColsList() throws SwtException{

		log.debug(this.getClass().getName() + "- [getMsdDisplayColsList] - Entry ");
		/* Local variable declaration */
		Connection conn = null;
		PreparedStatement pstmt = null;
		ResultSet res = null;
		List msdDisplayColumnsColl = null;

		try {
			final String HQL_GETMSDDISPLAYCOLUMNS = "FROM MsdDisplayColumns AS column " +
					" ORDER BY CASE WHEN column.tableName = 'P_MOVEMENT' THEN 0 ELSE 1 END";
			msdDisplayColumnsColl = (ArrayList) getHibernateTemplate().find(
					HQL_GETMSDDISPLAYCOLUMNS);
			log.debug(this.getClass().getName() + "- [getMsdDisplayColsList] - Exit ");

		} catch (Exception e) {
			// TODO: handle exception
			e.printStackTrace();
		}

		return msdDisplayColumnsColl;
	}



	public HashMap<String, String> getMsdDisplayColumnsLbl(String tableName) throws SwtException{

		log.debug(this.getClass().getName() + "- [getMsdDisplayColumnsLbl] - Entry ");
		/* Local variable declaration */
		Connection conn = null;
		PreparedStatement pstmt = null;
		ResultSet res = null;
		HashMap<String, String> msdColsList = new LinkedHashMap<String, String>();

		try {

			/* Establish the connection using connection manager */
			conn = ConnectionManager.getInstance().databaseCon();

			/* pass the query in string buffer */
			String msdColsQuery = "SELECT COLUMN_NAME, COLUMN_LABEL FROM P_MSD_DISPLAY_COLUMNS WHERE TABLE_NAME=? ORDER BY COLUMN_NAME ASC";
			pstmt = conn.prepareStatement(msdColsQuery);
			pstmt.setString(1, tableName);
			/* execute the statement */
			res = pstmt.executeQuery();

			/* Getting the accountId,accountName and put it in List */

			while (res.next()) {
				msdColsList.put(res.getString(1),res.getString(2));

			}
		} catch (SQLException e) {

			log.debug(this.getClass().getName()
					+ "- [getMsdDisplayColumnsLbl] - Exception " + e.getMessage());
			log.error(this.getClass().getName()
					+ "- [getMsdDisplayColumnsLbl] - Exception " + e.getMessage());
			e.printStackTrace();
		} finally {

			JDBCCloser.close(res, pstmt, conn, null);

			try{
				if ((conn != null) && (!conn.isClosed()))
					ConnectionManager.getInstance().retrunConnection(conn);
			} catch (Exception e) {
				log.error(this.getClass().getName()
						+ "- [getMsdDisplayColumnsLbl] - Exception " + e.getMessage());
			}
		}
		log.debug(this.getClass().getName() + "- [getMsdDisplayColumnsLbl] - Exit ");
		return msdColsList;
	}


	public 	ArrayList<String> getEntityPosLevelList(String entityId) throws SwtException {
		ArrayList<String> posLevelList = new ArrayList<String>();
		Connection conn = null;
		PreparedStatement pstmt = null;
		ResultSet rs = null;
		String  posLevelQuery = null;
		try {
			/* Establish the connection using connection manager */
			conn = ConnectionManager.getInstance().databaseCon();
			if (!SwtUtil.isEmptyOrNull(entityId) && "All".equalsIgnoreCase(entityId)) {
				posLevelQuery = "select distinct(position_level) from p_position_level_name order by position_level asc";
				pstmt = conn.prepareStatement(posLevelQuery);
			}else if (!SwtUtil.isEmptyOrNull(entityId)  && !"All".equalsIgnoreCase(entityId)) {
				posLevelQuery = "select distinct(position_level_name) from p_position_level_name where entity_id=?";
				pstmt = conn.prepareStatement(posLevelQuery);
				pstmt.setString(1, entityId);
			}else {
				posLevelQuery = "select distinct(position_level_name) from p_position_level_name ";
				pstmt = conn.prepareStatement(posLevelQuery);
			}
			/* execute the statement */
			rs = pstmt.executeQuery();

			while (rs.next()) {
				posLevelList.add(rs.getString(1));
			}
		} catch (SQLException e) {

			log.debug(this.getClass().getName()
					+ "- [getEntityPosLevelList] - Exception " + e.getMessage());
			log.error(this.getClass().getName()
					+ "- [getEntityPosLevelList] - Exception " + e.getMessage());
			e.printStackTrace();
		} finally {

			JDBCCloser.close(rs, pstmt, conn, null);

			try{
				if ((conn != null) && (!conn.isClosed()))
					ConnectionManager.getInstance().retrunConnection(conn);
			} catch (Exception e) {
				log.error(this.getClass().getName()
						+ "- [getEntityPosLevelList] - Exception " + e.getMessage());
			}
		}
		log.debug(this.getClass().getName() + "- [getEntityPosLevelList] - Exit ");
		return posLevelList;
	}

	public ArrayList<String> getSourceList(Date startDate, Date endDate) throws SwtException {
		logger.debug("Entering getDistinctInputSources with dates: start=" + startDate + ", end=" + endDate);

		Session session = null;
		Connection conn = null;
		PreparedStatement statement = null;
		ResultSet resultSet = null;
		List<String> sources = new ArrayList<>();

		try {
			StringBuilder queryBuilder = new StringBuilder("SELECT DISTINCT INPUT_SOURCE FROM p_movement WHERE INPUT_SOURCE IS NOT NULL AND lower(INPUT_SOURCE) <> 'null'");
			if (startDate != null || endDate != null) {
				queryBuilder.append(" AND 1=1");
				if (startDate != null) {
					queryBuilder.append(" AND VALUE_DATE >= ?");
				}
				if (endDate != null) {
					queryBuilder.append(" AND VALUE_DATE <= ?");
				}
			}

			session = getHibernateTemplate().getSessionFactory().openSession();
			conn = SwtUtil.connection(session);
			statement = conn.prepareStatement(queryBuilder.toString());

			int paramIndex = 1;
			if (startDate != null) {
				statement.setDate(paramIndex++, new java.sql.Date(startDate.getTime()));
			}
			if (endDate != null) {
				statement.setDate(paramIndex, new java.sql.Date(endDate.getTime()));
			}

			statement.execute();
			resultSet = statement.getResultSet();

			while (resultSet != null && resultSet.next()) {
				sources.add(resultSet.getString(1));
			}
		} catch (HibernateException hibernateException) {
			logger.error("Problem in accessing Hibernate properties", hibernateException);
			throw SwtErrorHandler.getInstance().handleException(
					hibernateException, "getDistinctInputSources", MovementDAO.class);
		} catch (SQLException sqlException) {
			logger.error("Problem in executing Query", sqlException);
			throw SwtErrorHandler.getInstance().handleException(
					sqlException, "getDistinctInputSources", MovementDAO.class);
		} finally {
			SwtException thrownException = null;
			Object[] exceptions = JDBCCloser.close(resultSet, statement, null, session);

			if (exceptions[0] != null)
				thrownException = SwtErrorHandler.getInstance()
						.handleException((SQLException) exceptions[0],
								"getDistinctInputSources", MovementDAO.class);

			if (thrownException == null && exceptions[1] != null)
				thrownException = SwtErrorHandler.getInstance()
						.handleException((HibernateException) exceptions[1],
								"getDistinctInputSources", MovementDAO.class);

			if (thrownException != null)
				throw thrownException;
		}

		logger.debug("Exiting getDistinctInputSources");
		return new ArrayList<>(sources);
	}

	public ArrayList<String> getFormatList(Date startDate, Date endDate) throws SwtException {
		logger.debug("Entering getDistinctMessageFormats with dates: start=" + startDate + ", end=" + endDate);

		Session session = null;
		Connection conn = null;
		PreparedStatement statement = null;
		ResultSet resultSet = null;
		List<String> formats = new ArrayList<>();

		try {
			StringBuilder queryBuilder = new StringBuilder("SELECT DISTINCT MESSAGE_FORMAT FROM p_movement WHERE MESSAGE_FORMAT IS NOT NULL AND lower(MESSAGE_FORMAT) <> 'null'");
			if (startDate != null || endDate != null) {
				queryBuilder.append(" AND 1=1");
				if (startDate != null) {
					queryBuilder.append(" AND VALUE_DATE >= ?");
				}
				if (endDate != null) {
					queryBuilder.append(" AND VALUE_DATE <= ?");
				}
			}

			session = getHibernateTemplate().getSessionFactory().openSession();
			conn = SwtUtil.connection(session);
			statement = conn.prepareStatement(queryBuilder.toString());

			int paramIndex = 1;
			if (startDate != null) {
				statement.setDate(paramIndex++, new java.sql.Date(startDate.getTime()));
			}
			if (endDate != null) {
				statement.setDate(paramIndex, new java.sql.Date(endDate.getTime()));
			}

			statement.execute();
			resultSet = statement.getResultSet();

			while (resultSet != null && resultSet.next()) {
				formats.add(resultSet.getString(1));
			}
		} catch (HibernateException hibernateException) {
			logger.error("Problem in accessing Hibernate properties", hibernateException);
			throw SwtErrorHandler.getInstance().handleException(
					hibernateException, "getDistinctMessageFormats", MovementDAO.class);
		} catch (SQLException sqlException) {
			logger.error("Problem in executing Query", sqlException);
			throw SwtErrorHandler.getInstance().handleException(
					sqlException, "getDistinctMessageFormats", MovementDAO.class);
		} finally {
			SwtException thrownException = null;
			Object[] exceptions = JDBCCloser.close(resultSet, statement, null, session);

			if (exceptions[0] != null)
				thrownException = SwtErrorHandler.getInstance()
						.handleException((SQLException) exceptions[0],
								"getDistinctMessageFormats", MovementDAO.class);

			if (thrownException == null && exceptions[1] != null)
				thrownException = SwtErrorHandler.getInstance()
						.handleException((HibernateException) exceptions[1],
								"getDistinctMessageFormats", MovementDAO.class);

			if (thrownException != null)
				throw thrownException;
		}

		logger.debug("Exiting getDistinctMessageFormats");
		return new ArrayList<>(formats);
	}

	/**
	 * Method to delete the screen info
	 *
	 * @throws SwtException Collection<ScreenInfo>
	 */
	@SuppressWarnings("unchecked")
	public void deleteMsdScreenInfo(List<ScreenInfo> listScreenInfo) throws SwtException {
		// Fetch the list of screen options

		// declare a connection object
		Connection conn = null;
		// statement object is initialized
		PreparedStatement pst = null;
		// String variable for holding delete query
		String delete_screen_info = "";
		try {

			logger.debug(this.getClass().getName() + " - [deleteMsdScreenInfo] - Entry");
			conn = ConnectionManager.getInstance().databaseCon();

			if (listScreenInfo != null && listScreenInfo.size() > 0) {
				for (Iterator iterator = listScreenInfo.iterator(); iterator.hasNext();) {
					ScreenInfo screenInfoDelete = (ScreenInfo) iterator.next();

					delete_screen_info = "DELETE FROM S_SCREEN_INFO  where HOST_ID=? and ENTITY_ID=? and USER_ID=? and SCREEN_ID=? and PROPERTY_NAME = ?";
					// declare statement for delete
					pst = conn.prepareStatement(delete_screen_info);
					pst.setString(1, screenInfoDelete.getId().getHostId());
					pst.setString(2, screenInfoDelete.getId().getEntityId());
					pst.setString(3, screenInfoDelete.getId().getUserId());
					pst.setString(4, screenInfoDelete.getId().getScreenId());
					pst.setString(5, screenInfoDelete.getId().getPropertyName());
					// execute delete query
					pst.executeUpdate();

				}
			}
			// committed the transaction
			conn.commit();
			logger.debug(this.getClass().getName() + " - [deleteMsdScreenInfo] - Exit");
		} catch (Exception e) {
			logger.error(this.getClass().getName() + " - Exception Catched in [deleteMsdScreenInfo] method : - "
					+ e.getMessage());
			throw SwtErrorHandler.getInstance().handleException(e, "deleteMsdScreenInfo", MovementDAOHibernate.class);
		} finally {
			JDBCCloser.close(null, pst, conn, null);

		}

	}



} // End of class MovementDAOHibernate