/*
 * @(#)ForecastMonitorDAOHibernate.java 1.0 12/02/11
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */

package org.swallow.work.dao.hibernate;

import java.sql.CallableStatement;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collection;
import java.util.Date;
import java.util.List;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.dao.DataAccessResourceFailureException;

import org.swallow.control.model.EntityAccess;
import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.maintenance.model.CurrencyGroup;
import org.swallow.maintenance.model.CurrencyMaster;
import org.swallow.maintenance.model.ForecastMonitorTemplate;
import org.swallow.maintenance.model.SysParams;
import org.swallow.maintenance.service.SysParamsManager;
import org.swallow.util.CacheManager;
import org.swallow.util.JDBCCloser;
import org.swallow.util.OpTimer;
import org.swallow.util.SwtConstants;
import org.swallow.util.SwtInterceptor;
import org.swallow.util.SwtUtil;
import org.swallow.util.SystemFormats;
import org.swallow.work.dao.ForecastMonitorDAO;
import org.swallow.work.model.AssumptionData;
import org.swallow.work.model.ScenarioData;
import org.swallow.work.model.UserBuckets;
import org.swallow.work.model.UserTemplate;
import org.swallow.work.web.form.ForecastRecord;
import org.springframework.context.annotation.Lazy;
import org.springframework.orm.hibernate5.support.HibernateDaoSupport;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.stereotype.Repository;
import org.springframework.beans.factory.annotation.Autowired;
import org.hibernate.HibernateException;
import org.hibernate.SessionFactory;
import org.hibernate.Transaction;
import org.hibernate.Session;





/**
 * ForecastMonitorDAOHibernate.java
 * 
 * Class that implements the ForecastMonitorDAO and acts as DAO layer for all
 * database operations related to ForecastMonitor<br>
 * This interface has methods that are used for accessing the persistent storage
 * such as database <br>
 * which helps client to create, retrieve and persists data to the Persistent
 * Object.<br>
 * 
 * <AUTHOR>
 * @date May 12, 2011
 */
@Repository ("forecastMonitorDAO")
@Transactional
public class ForecastMonitorDAOHibernate extends HibernateDaoSupport implements
		ForecastMonitorDAO {
	public ForecastMonitorDAOHibernate(@Lazy SessionFactory sessionfactory) {
	    setSessionFactory(sessionfactory);
	}

	/**
	 * Instance Of Log
	 */
	private static final Log log = LogFactory
			.getLog(ForecastMonitorDAOHibernate.class);

	/**
	 * Method to get get the predicted balances for for the period specified<br>
	 * This uses PK_MONITORS.SP_FORECAST_MONITOR_JOB.<br>
	 * 
	 * This method helps getting all the balances with respect to the entities.<br>
	 * This method executes a stored procedure that will retrieve all the
	 * balances.<br>
	 * 
	 * @param OpTimer
	 *            opTimer
	 * @param String
	 *            currencyCode
	 * @param String
	 *            entityId
	 * @param String
	 *            userId
	 * @param SystemFormats
	 *            format
	 * @param boolean
	 *            multiplierFlag
	 * @return ForecastRecord object
	 * @throws SwtException
	 */
	public ForecastRecord getAllBalancesUsingStoredProc(OpTimer opTimer,
			String currencyCode, String entityId, String userId,
			SystemFormats format, boolean multiplierFlag) throws SwtException {

		// String variable to hold hostId
		String hostId = null;
		// Session object
		Session session = null;
		// Connection object
		Connection conn = null;
		// CallableStatement object
		CallableStatement cstmt = null;
		// String variable to hold screenId
		String screenId = null;
		// ResultSet to hold monitor details
		ResultSet rsMonitorInfo = null;
		// object to hold forecast monitor record details
		ForecastRecord forecastMonitorRecord = null;
		// ForecastRecord object
		ForecastRecord monitorRecord = null;
		// collection to hold the bucket collection details
		Collection<ForecastRecord> bucketColl = null;
		// ForecastRecord object
		ForecastRecord bucketRecord = null;
		// collection to hold the Forecast monitor details
		Collection<ForecastRecord> totalColl = null;
		// Calendar object
		Calendar calDate = null;
		SysParamsManager sysParamsManager = (SysParamsManager) SwtUtil.getBean("sysParamsManager");
		SysParams sysParams = sysParamsManager.getSysParamsDetail(SwtUtil.getCurrentHostId());
		String pattern = SwtConstants.DATE_PAT + sysParams.getDateFormat();
		if (pattern.equals("datePat1"))
			pattern = "dd/MM/yyyy";
		else
			pattern = "MM/dd/yyyy";
		
		try {
			log.debug(this.getClass().getName()
					+ " - [getAllBalancesUsingStoredProc] - Entry");
			// start the opTimer
			opTimer.start(SwtConstants.DATA_FETCH);
			// Make an instance of calendar
			calDate = Calendar.getInstance();
			// Initialize the ForecastRecord object
			forecastMonitorRecord = new ForecastRecord();
			// create instance for ForecastRecord
			bucketRecord = new ForecastRecord();
			// get the host Id
			hostId = CacheManager.getInstance().getHostId();
			// Get the hibernate session
			session = getHibernateTemplate().getSessionFactory().openSession();
			// Get the connection object
			conn = SwtUtil.connection(session);
			// Instantiate the bucket collection
			bucketColl = new ArrayList<ForecastRecord>();
			// Instantiate the total collection
			totalColl = new ArrayList<ForecastRecord>();
			// Set the screen id to that of the forecast monitor
			screenId = SwtConstants.FORECAST_MONITOR_ID;
			// Make a callable statement for executing the procedure
			cstmt = conn
					.prepareCall("{call PKG_FORECAST_MONITOR.SP_GET_FORECAST(?,?,?,?,?,?)}");
			// Set the parameters
			cstmt.setString(1, hostId);
			cstmt.setString(2, entityId);
			cstmt.setString(3, currencyCode);
			cstmt.setString(4, userId);
			cstmt.setString(5, screenId);
			// Register the output parameters
			cstmt.registerOutParameter(6, oracle.jdbc.OracleTypes.CURSOR);
			// Execute the callable statement
			cstmt.execute();
			// Get the result set object for predicted balance
			rsMonitorInfo = (ResultSet) cstmt.getObject(6);
			// stop the opTimer
			opTimer.stop(SwtConstants.DATA_FETCH);
			// cleanse rsPred to remove duplicate date records sometimes
			opTimer.start(SwtConstants.DATA_CLEANSE);
			// loop for execute all records and collect the
			// details
			while (rsMonitorInfo.next()) {
				if (bucketColl.size() != 0
						&& rsMonitorInfo.getString(1).equals("Bucket")) {
					// set the bucket collection details
					bucketRecord.setBucketColl(bucketColl);
					// set the bucket record details
					bucketRecord.setBalanceDate(monitorRecord.getBalanceDate());
					bucketRecord.setHeaderData(monitorRecord.getHeaderData());
					bucketRecord.setBucketId(monitorRecord.getBucketId());
					bucketRecord.setBucketState(monitorRecord.getBucketState());
					bucketRecord.setHolidayFlag(monitorRecord.getHolidayFlag());
					bucketRecord.setHeaderTooltip(monitorRecord
							.getHeaderTooltip());
					// set the record to collection
					totalColl.add(bucketRecord);
					// create instance for ForecastRecord
					bucketRecord = new ForecastRecord();
					// Re-initialize the ForecastRecord list
					bucketColl = new ArrayList<ForecastRecord>();
				}
				// Start: Code modified by Bala for Mantis 1413 on 03-Oct-2011
				// -if a template has a column with a NUMERIC ID, it causes
				// corrupt display
				// Creating new instance for ForecastRecord
				monitorRecord = new ForecastRecord();
				// setting the header in monitor record
				monitorRecord.setHeader(rsMonitorInfo.getString(1));
				// set the grid type
				if (rsMonitorInfo.getString(9).equals(
						SwtConstants.FORECAST_TOTAL)
						|| rsMonitorInfo.getString(9).equals(
								SwtConstants.FORECAST_ASSUMPTION)
						|| rsMonitorInfo.getString(9).equals(
								SwtConstants.FORECAST_SCENARIO)
						|| rsMonitorInfo.getString(9).equals(
								SwtConstants.FORECAST_GRAND_TOTAL)) {
					monitorRecord.setGridType(SwtConstants.FORECAST_SUB);
				} else {
					monitorRecord.setGridType(SwtConstants.FORECAST_MAIN);
				}
				// If header data first letter is number, append the '_' in
				// header data
				if (Character.isDigit(rsMonitorInfo.getString(1).charAt(0))) {
					// setting header data in monitor record
					monitorRecord.setHeaderData("_"
							+ rsMonitorInfo.getString(1).replaceAll(" ", "")
									.toLowerCase());
				} else {
					// setting header data in monitor record
					monitorRecord.setHeaderData(rsMonitorInfo.getString(1)
							.replaceAll(" ", "").toLowerCase());
				}
				// setting bucket id in monitor record
				monitorRecord.setBucketId(rsMonitorInfo.getString(2));
				// setting date / total / Grand total in monitor record
				SimpleDateFormat dateFormat = new SimpleDateFormat(pattern);
				try {
					Date d = dateFormat.parse(rsMonitorInfo.getString(3));
					dateFormat.applyPattern(format.getDateFormatValue());
					monitorRecord.setBalanceDate(dateFormat.format(d));
				}catch(ParseException pe){
					monitorRecord.setBalanceDate(rsMonitorInfo.getString(3));
				}
				// setting balance in monitor record
				monitorRecord.setBalance(rsMonitorInfo.getBigDecimal(4));
				// setting bucket state in monitor record
				monitorRecord.setBucketState(rsMonitorInfo.getString(5));
				if (rsMonitorInfo.getString(6)
						.equals(SwtConstants.WEEKEND_FLAG)
						|| rsMonitorInfo.getString(6).equals(
								SwtConstants.FORECAST_NONHIDE_HOLIDAY)
						|| rsMonitorInfo.getString(7).equals(
								SwtConstants.WEEKEND_FLAG)
						|| rsMonitorInfo.getString(7).equals(
								SwtConstants.FORECAST_NONHIDE_HOLIDAY)) {
					// setting holiday flag in monitor record
					monitorRecord.setHolidayFlag(SwtConstants.HOLIDAY_FLAG);
				} else {
					// setting holiday flag in monitor record
					monitorRecord.setHolidayFlag(SwtConstants.NO);
				}
				// setting header tooltip in monitorRecord
				monitorRecord.setHeaderTooltip(rsMonitorInfo.getString(8));
				if (multiplierFlag)
					// setting balance in monitor record
					monitorRecord.setBalanceAsString(SwtUtil.formatCurrency(
							currencyCode, monitorRecord.getBalance()));
				else
					// setting balance in monitor record
					monitorRecord.setBalanceAsString(SwtUtil.formatCurrency(
							SwtConstants.YES, monitorRecord.getBalance()));
				// Setting the sign as negative
				if (monitorRecord.getBalance() != null
						&& monitorRecord.getBalance().doubleValue() < 0) {
					monitorRecord.setPredBalanceNegative(true);
				}
				if (!rsMonitorInfo.getString(6).equals(
						SwtConstants.HOLIDAY_FLAG)) {
					// add the forecast record to bucket collection
					bucketColl.add(monitorRecord);
				}
			}
			// End: Code modified by Bala for Mantis 1413 on 03-Oct-2011
			// -if a template has a column with a NUMERIC ID, it causes
			// corrupt display
			// stop the opTimer
			opTimer.stop(SwtConstants.DATA_CLEANSE);
			// Add the collection to forecast monitor records
			forecastMonitorRecord.setForecastMonitorRecords(totalColl);
			// Add the grand records to forecastMonitorRecord
			forecastMonitorRecord.setGrandTotalRecords(bucketColl);
			log.debug(this.getClass().getName()
					+ " - [getAllBalancesUsingStoredProc] - Exit");
		} catch (DataAccessResourceFailureException dataAccessException) {
			log
					.error(this.getClass().getName()
							+ " - [getAllBalancesUsingStoredProc] - DataAccessResourceFailureException -"
							+ dataAccessException.getMessage());
			throw SwtErrorHandler.getInstance().handleException(
					dataAccessException, "getAllBalancesUsingStoredProc",
					ForecastMonitorDAOHibernate.class);
		} catch (IllegalStateException illegalStateException) {
			log
					.error(this.getClass().getName()
							+ " - [getAllBalancesUsingStoredProc] - IllegalStateException -"
							+ illegalStateException.getMessage());
			throw SwtErrorHandler.getInstance().handleException(
					illegalStateException, "getAllBalancesUsingStoredProc",
					ForecastMonitorDAOHibernate.class);
		} catch (HibernateException hibernateException) {
			log
					.error(this.getClass().getName()
							+ " - [getAllBalancesUsingStoredProc] - HibernateException -"
							+ hibernateException.getMessage());
			throw SwtErrorHandler.getInstance().handleException(
					hibernateException, "getAllBalancesUsingStoredProc",
					ForecastMonitorDAOHibernate.class);
		} catch (SQLException sqlException) {
			log.error(this.getClass().getName()
					+ " - [getAllBalancesUsingStoredProc] - SQLException -"
					+ sqlException.getMessage());
			throw SwtErrorHandler.getInstance().handleException(sqlException,
					"getAllBalancesUsingStoredProc",
					ForecastMonitorDAOHibernate.class);
		} catch (Exception e) {
			e.printStackTrace();
			log.error(this.getClass().getName()
					+ " - [getAllBalancesUsingStoredProc] - Exception -"
					+ e.getMessage());
			throw SwtErrorHandler.getInstance().handleException(e,
					"getAllBalancesUsingStoredProc",
					ForecastMonitorDAOHibernate.class);
		} finally {
			// Edited by KaisBS for mantis 1745 : introducing the implementation of the JDBCCloser class
			SwtException thrownException = null;
			Object[] exceptions = JDBCCloser.close(rsMonitorInfo, cstmt, conn, session);

			if (exceptions[0] != null)
			    thrownException = SwtErrorHandler.getInstance().handleException((SQLException) exceptions[0], "getAllBalancesUsingStoredProc",
									ForecastMonitorDAOHibernate.class);

			if (thrownException == null && exceptions[1] !=null) 
			    thrownException = SwtErrorHandler.getInstance().handleException((HibernateException) exceptions[1], "getAllBalancesUsingStoredProc",
									ForecastMonitorDAOHibernate.class);

			if (thrownException != null)
				throw thrownException;
			// Cleaning all the unreferenced objects
			hostId = null;
			cstmt = null;
			screenId = null;
			bucketRecord = null;
			totalColl = null;
			bucketColl = null;
		}
		return forecastMonitorRecord;
	}

	/**
	 * This is used to get the currency group access details for the particular
	 * hostId and roleId
	 * 
	 * @param String
	 *            hostId
	 * @param String
	 *            roleId
	 * @return Collection<CurrencyGroup>
	 * @throws SwtException
	 */
	@SuppressWarnings("unchecked")
	public Collection<CurrencyGroup> getCurrencyGroupAccessDetails(
			String hostId, String roleId) throws SwtException {
		// List to hold the currency group details
		Collection<CurrencyGroup> list = null;
		try {
			log.debug(this.getClass().getName()
					+ " - [getCurrencyGroupAccessDetails] - Entry");
			// Instantiate the ArrayList
			list = new ArrayList<CurrencyGroup>();
			// get the Currency Group Access Details
			list = (Collection<CurrencyGroup> ) getHibernateTemplate().find(
							"select distinct G from CurrencyGroupAccess C,"
									+ "EntityAccess E, CurrencyGroup G where E.id.hostId = C.id.hostId AND E.id.entityId = C.id.entityId "
									+ "and E.id.roleId = C.id.roleId and C.accessId in ('0','1') "
									+ "and E.accessId in ('0','1') and C.id.hostId=?0 and C.id.roleId=?1 and "
									+ "G.id.currencyGroupId = C.id.currencyGroupId",
							new Object[] { hostId, roleId });
			log.debug(this.getClass().getName()
					+ " - [getCurrencyGroupAccessDetails] - Exit");
		} catch (Exception e) {
			log.error("Exception occured in " + this.getClass().getName()
					+ " -  getCurrencyGroupAccessDetails() " + e.getMessage());
			throw SwtErrorHandler.getInstance().handleException(e,
					"getCurrencyGroupAccessDetails",
					ForecastMonitorDAOHibernate.class);
		}
		return list;
	}

	/**
	 * This method is used to save the user template details.<br>
	 * User template list details contains information like:<br>
	 * 
	 * @param UserTemplate
	 *            userTemplate
	 * @param boolean
	 *            isUpdate
	 * @throws SwtException
	 */
	public void saveUserTemplate(UserTemplate userTemplate, boolean isUpdate)
			throws SwtException {
		// collection to hold user template details
		Collection<UserTemplate> template = null;
		try {
			log
					.debug(this.getClass().getName()
							+ " saveUserTemplate() - Enter");
			// save the user template
			if (!isUpdate) {
				getHibernateTemplate().save(userTemplate);
				getHibernateTemplate().flush();
			} else {
				// Instantiate UserTemplate ArrayList
				template = new ArrayList<UserTemplate>();
				// get the UserTemplate Details
				template = getUserTemplateDetails(userTemplate);
				if (template.size() == 0) {
					// save the userTemplate
					getHibernateTemplate().clear();
					getHibernateTemplate().save(userTemplate);
					getHibernateTemplate().flush();
				} else {
					// update the userTemplate
					getHibernateTemplate().clear();
					getHibernateTemplate().update(userTemplate);
					getHibernateTemplate().flush();
				}
			}
			log.debug(this.getClass().getName() + " saveUserTemplate() - Exit");
		} catch (Exception e) {
			log
					.error("Exception occured in saving/updating user template list."
							+ e.getMessage());
			throw SwtErrorHandler.getInstance().handleException(e,
					"saveUserTemplate", ForecastMonitorDAOHibernate.class);
		} finally {
			// nullify objects
			template = null;
		}
	}

	/**
	 * This method is used to save the user bucket details.<br>
	 * 
	 * @param UserBuckets
	 *            userBucket
	 * @param boolean
	 *            isUpdate
	 * @throws SwtException
	 */
	public void saveUserBucket(UserBuckets userBucket, boolean isUpdate)
			throws SwtException {
		// Declare UserBuckets object
		UserBuckets userBuckets = null;
		// user bucket collection
		Collection<UserBuckets> bucket = null;
		try {
			log.debug(this.getClass().getName() + " saveUserBucket() - Enter"
					+ isUpdate + userBucket);
			// Instantiate UserBuckets
			userBuckets = new UserBuckets();
			// set the userBuckets values
			userBuckets.getId().setHostId(userBucket.getId().getHostId());
			userBuckets.getId().setUserId(userBucket.getId().getUserId());
			userBuckets.getId().setBucketId(userBucket.getId().getBucketId());
			// Instantiate ArrayList
			bucket = new ArrayList<UserBuckets>();
			// get the user bucket details
			bucket = getUserBucketDetails(userBuckets);
			if (bucket.size() == 0) {
				// save the userBucket
				getHibernateTemplate().clear();
				getHibernateTemplate().save(userBucket);
				getHibernateTemplate().flush();
			} else {
				// update the userBucket
				getHibernateTemplate().clear();
				getHibernateTemplate().update(userBucket);
				getHibernateTemplate().flush();
			}
			// }
			log.debug(this.getClass().getName() + " saveUserBucket() - Exit");
		} catch (Exception e) {
			log.error("Exception occured in saving/updating user bucket list."
					+ e.getMessage());
			throw SwtErrorHandler.getInstance().handleException(e,
					"saveUserBucket", ForecastMonitorDAOHibernate.class);
		} finally {
			// nullify objects
			userBuckets = null;
			bucket = null;
		}
	}

	/**
	 * This method is used to save the scenario details.<br>
	 * 
	 * @param scenarioData
	 * @throws SwtException
	 */
	public void saveScenarioData(ScenarioData scenarioData) throws SwtException {
		// Holds the hibernate session instance
		Session sesion = null;
		// Holds the hibernate Transaction instance
		Transaction tx = null;
		// Template Id
		String templateId = null;
		try {
			log.debug(this.getClass().getName()
					+ " saveScenarioData() - Begins");
			// get the template id details
			templateId = getTemplateId(scenarioData);
			// set the templateId to scenarioData
			scenarioData.getId().setTemplateId(templateId);
			boolean flag = fetchScenarioRecord(scenarioData);
			// get the session
			sesion = getHibernateTemplate().getSessionFactory().openSession();
			// Updates the details of the inputInterface
			tx = sesion.beginTransaction();
			// save/update the scenarioData
			if (!flag)
				sesion.update(scenarioData);
			else
				sesion.save(scenarioData);
			// Commits the transaction to the database
			tx.commit();
			log.debug(this.getClass().getName() + " saveScenarioData() - Ends");
		} catch (Exception e) {
			try {
				if (tx != null) {
					tx.rollback();
				}
			} catch (HibernateException ignoreExp) {
				log
						.error("HibernateException occured in rolling back transaction. Cause : "
								+ ignoreExp.getMessage());
			}
			log
					.error("Exception occured in saving/updating scenerio list. Cause : "
							+ e.getMessage());
			throw SwtErrorHandler.getInstance().handleException(e,
					"saveScenarioData", ForecastMonitorDAOHibernate.class);
		} finally {
			//edited by KaisBS for mantis 1745 : introducing the implementation of the JDBCCloser class
			HibernateException hThrownException = JDBCCloser.close(sesion);
		    if (hThrownException != null)
		    	throw new SwtException(hThrownException.getMessage());
			
			tx = null;
			templateId = null;
		}
	}

	/**
	 * This method is used get the template id
	 * 
	 * @param ScenarioData
	 *            scenarioData
	 * @return String
	 * @throws SwtException
	 */
	public String getTemplateId(ScenarioData scenarioData) throws SwtException {
		/* Method's local variable declaration */
		// Declare Session
		Session session = null;
		// Declare Connection
		Connection conn = null;
		// Declare PreparedStatement
		PreparedStatement cstmt = null;
		// Declare templateId variable
		String templateId = null;
		// Declare ResultSet
		ResultSet rs = null;
		try {
			// open the session
			session = getHibernateTemplate().getSessionFactory().openSession();
			// get the connection
			conn = SwtUtil.connection(session);
			/* Using Callable statement to execute the Stored Procedure */
			cstmt = conn
					.prepareCall("select PKG_FORECAST_MONITOR.FN_GET_USERTEMPLATE(?,?,?,?) from dual");
			cstmt.setString(1, scenarioData.getId().getHostId());
			cstmt.setString(2, scenarioData.getId().getEntityId());
			cstmt.setString(3, scenarioData.getId().getCurrencyCode());
			cstmt.setString(4, scenarioData.getId().getUserId());
			rs = cstmt.executeQuery();
			rs.next();
			// get the templateId
			templateId = (String) rs.getString(1);
		} catch (Exception e) {
			log.error("Exception occured in getTemplateId." + e.getMessage());
			throw SwtErrorHandler.getInstance().handleException(e,
					"getTemplateId", ForecastMonitorDAOHibernate.class);
		} finally {
			//edited by KaisBS for mantis 1745 : introducing the implementation of the JDBCCloser class
			SwtException thrownException = null;
			Object[] exceptions = JDBCCloser.close(rs, cstmt, conn, session);

			if (exceptions[0] != null)
			    thrownException = SwtErrorHandler.getInstance().handleException((SQLException) exceptions[0], "getTemplateId",
									ForecastMonitorDAOHibernate.class);

			if (thrownException == null && exceptions[1] !=null) 
			    thrownException = SwtErrorHandler.getInstance().handleException((HibernateException) exceptions[1], "getTemplateId",
									ForecastMonitorDAOHibernate.class);

			if (thrownException != null)
				throw thrownException;
		}
		return templateId;
	}

	/**
	 * This method is used to fetch the details of the scenario
	 * 
	 * @param ScenarioData
	 *            scenarioData
	 * @return boolean
	 * @throws SwtException
	 */
	@SuppressWarnings("unchecked")
	private boolean fetchScenarioRecord(ScenarioData scenarioData)
			throws SwtException {
		// boolean variable
		boolean flag = true;
		// List of ScenarioData
		List<ScenarioData> data = null;
		try {
			log.debug(this.getClass().getName()
					+ " - [fetchScenarioRecord] - Entering");
			// fetches the scenario data details
			data = (List<ScenarioData> ) getHibernateTemplate().find(
							"select p from ScenarioData p "
									+ "where p.id.userId = ?0 and p.id.hostId = ?1 and p.id.entityId = ?2 "
									+ "and p.id.currencyCode = ?3 and p.id.templateId = ?4 and p.id.valueDate = ?5 ",
							new Object[] {
									scenarioData.getId().getUserId(),
									scenarioData.getId().getHostId(),
									scenarioData.getId().getEntityId(),
									scenarioData.getId().getCurrencyCode(),
									scenarioData.getId().getTemplateId(),
									SwtUtil.truncateDateTime(scenarioData.getId()
											.getValueDate()) });
			if (data.size() > 0)
				flag = false;
			log.debug(this.getClass().getName()
					+ " - [fetchScenarioRecord] - Exiting");
		} catch (Exception e) {
			log
					.error(this.getClass().getName()
							+ " - [fetchScenarioRecord] - Exception -"
							+ e.getMessage());
			// Re-throwing SwtException
			throw SwtErrorHandler.getInstance().handleException(e,
					"fetchScenarioRecord", ForecastMonitorDAOHibernate.class);
		} finally {
			// nullify objects
			data = null;
		}
		return flag;
	}

	/**
	 * This method is used to get the entity list details for the given user<br>
	 * 
	 * @param hostId
	 * @param roleId
	 * @return Collection<EntityAccess>
	 * @throws SwtException
	 */
	@SuppressWarnings("unchecked")
	public Collection<EntityAccess> getUserEntityList(String hostId,
			String roleId) throws SwtException {
		// Collection of EntityAccess
		Collection<EntityAccess> entityList = null;
		try {
			log.debug(this.getClass().getName()
					+ " - [getUserEntityList] - Entering");
			// create instance
			entityList = new ArrayList<EntityAccess>();
			// getting list of entity from database
			entityList = (Collection<EntityAccess> ) getHibernateTemplate().find(
							"from EntityAccess e where e.id.hostId = ?0 "
									+ "and e.id.roleId=?1 and e.accessId != 2 order by e.id.entityId asc",
							new Object[] { hostId, roleId });
			log.debug(this.getClass().getName()
					+ " - [getUserEntityList] - Exiting");
		} catch (Exception e) {
			log.error(this.getClass().getName()
					+ " - Exception caught in [getUserEntityList] method "
					+ e.getMessage());
			throw SwtErrorHandler.getInstance().handleException(e,
					"getUserEntityList", ForecastMonitorDAOHibernate.class);
		}
		return entityList;
	}

	/**
	 * This method is used to get the user template list details for the given
	 * user<br>
	 * 
	 * @param hostId
	 * @param userId
	 * @return Collection<UserTemplate>
	 * @throws SwtException
	 */
	@SuppressWarnings("unchecked")
	public Collection<UserTemplate> getUserTemplateList(String hostId,
			String userId) throws SwtException {
		// Collection of UserTemplate
		Collection<UserTemplate> templateList = null;
		try {
			log.debug(this.getClass().getName()
					+ " - [getUserTemplateList] - Entering");
			// create UserTemplate collection instance
			templateList = new ArrayList<UserTemplate>();
			// getting list of template from database
			templateList = (Collection<UserTemplate> ) getHibernateTemplate().find(
					"from UserTemplate e where e.id.hostId = ?0 "
							+ "and e.id.userId=?1 order by e.id.entityId asc",
					new Object[] { hostId, userId });
			log.debug(this.getClass().getName()
					+ " - [getUserTemplateList] - Exiting");
		} catch (Exception e) {
			log.error(this.getClass().getName()
					+ " - Exception caught in [getUserTemplateList] method "
					+ e.getMessage());
			throw SwtErrorHandler.getInstance().handleException(e,
					"getUserTemplateList", ForecastMonitorDAOHibernate.class);
		}
		return templateList;
	}

	/**
	 * This method is used to get the user template list details for the given
	 * user<br>
	 * 
	 * @param UserTemplate
	 *            userTemplate
	 * @return Collection<UserTemplate>
	 * @throws SwtException
	 */
	@SuppressWarnings("unchecked")
	private Collection<UserTemplate> getUserTemplateDetails(
			UserTemplate userTemplate) throws SwtException {
		// Collection of UserTemplate
		Collection<UserTemplate> templateList = null;
		try {
			log.debug(this.getClass().getName()
					+ " - [getUserTemplateList] - Entering");
			// create instance
			templateList = new ArrayList<UserTemplate>();
			// getting list of user template list from database
			templateList = (Collection<UserTemplate> ) getHibernateTemplate().find(
							"from UserTemplate e where e.id.hostId = ?0 "
									+ "and e.id.userId=?1 and e.id.entityId=?2 and e.id.currencyCode=?3",
							new Object[] { userTemplate.getId().getHostId(),
									userTemplate.getId().getUserId(),
									userTemplate.getId().getEntityId(),
									userTemplate.getId().getCurrencyCode() });
			log.debug(this.getClass().getName()
					+ " - [getUserTemplateList] - Exiting");
		} catch (Exception e) {
			log.error(this.getClass().getName()
					+ " - Exception caught in [getUserTemplateList] method "
					+ e.getMessage());
			throw SwtErrorHandler.getInstance().handleException(e,
					"getUserTemplateList", ForecastMonitorDAOHibernate.class);
		}
		return templateList;
	}

	/**
	 * This method is used to get the forecast monitor template list details for
	 * the given user<br>
	 * 
	 * @param hostId
	 * @param userId
	 * @return Collection<ForecastMonitorTemplate>
	 * @throws SwtException
	 */
	@SuppressWarnings("unchecked")
	public Collection<ForecastMonitorTemplate> getForecastTemplate(
			String hostId, String userId) throws SwtException {
		// Collection of ForecastMonitorTemplate
		Collection<ForecastMonitorTemplate> templateList = null;
		try {
			log.debug(this.getClass().getName()
					+ " - [getForecastTemplate] - Entering");
			// create instance
			templateList = new ArrayList<ForecastMonitorTemplate>();
			// getting list of entity from database
			templateList = (Collection<ForecastMonitorTemplate> ) getHibernateTemplate().find(
							"select distinct e from ForecastMonitorTemplate e where e.id.hostId = ?0 "
									+ "and (e.userId = ?1 OR e.publicTemplate = ?2) order by e.id.templateId asc",
							new Object[] { hostId, userId, "Y" });
			log.debug(this.getClass().getName()
					+ " - [getForecastTemplate] - Exiting");
		} catch (Exception e) {
			log.error(this.getClass().getName()
					+ " - Exception caught in [getForecastTemplate] method "
					+ e.getMessage());
			throw SwtErrorHandler.getInstance().handleException(e,
					"getForecastTemplate", ForecastMonitorDAOHibernate.class);
		}
		return templateList;
	}

	/**
	 * This method is used to get the user bucket list details for the given
	 * user<br>
	 * 
	 * @param hostId
	 * @param userId
	 * @return Collection<UserBuckets>
	 * @throws SwtException
	 */
	@SuppressWarnings("unchecked")
	public Collection<UserBuckets> getUserBucketList(String hostId,
			String userId) throws SwtException {
		// Collection of UserBuckets
		Collection<UserBuckets> bucketList = null;
		try {
			log.debug(this.getClass().getName()
					+ " - [getUserBucketList] - Entering");
			// create instance of UserBuckets
			bucketList = new ArrayList<UserBuckets>();
			// getting list of bucket from database
			bucketList = (Collection<UserBuckets> ) getHibernateTemplate().find(
					"from UserBuckets e where e.id.hostId = ?0 "
							+ "and e.id.userId=?1 order by e.id.bucketId asc",
					new Object[] { hostId, userId });
			log.debug(this.getClass().getName()
					+ " - [getUserBucketList] - Exiting");
		} catch (Exception e) {
			log.error(this.getClass().getName()
					+ " - Exception caught in [getUserBucketList] method "
					+ e.getMessage());
			throw SwtErrorHandler.getInstance().handleException(e,
					"getUserBucketList", ForecastMonitorDAOHibernate.class);
		}
		return bucketList;
	}

	/**
	 * This method is used to get the user bucket list details for the given
	 * user<br>
	 * 
	 * @param hostId
	 * @param userId
	 * @return Collection<UserBuckets>
	 * @throws SwtException
	 */
	@SuppressWarnings("unchecked")
	private Collection<UserBuckets> getUserBucketDetails(UserBuckets userBuckets)
			throws SwtException {
		// Collection of UserBuckets
		Collection<UserBuckets> bucketList = null;
		try {
			log.debug(this.getClass().getName()
					+ " - [getUserBucketList] - Entering");
			// create instance
			bucketList = new ArrayList<UserBuckets>();
			// getting list of bucket from database
			bucketList = (Collection<UserBuckets> ) getHibernateTemplate().find(
					"from UserBuckets e where e.id.hostId = ?0 "
							+ "and e.id.userId=?1 and e.id.bucketId = ?2",
					new Object[] { userBuckets.getId().getHostId(),
							userBuckets.getId().getUserId(),
							userBuckets.getId().getBucketId() });
			getHibernateTemplate().flush();
			log.debug(this.getClass().getName()
					+ " - [getUserBucketList] - Exiting");
		} catch (Exception e) {
			log.error(this.getClass().getName()
					+ " - Exception caught in [getUserBucketList] method "
					+ e.getMessage());
			throw SwtErrorHandler.getInstance().handleException(e,
					"getUserBucketList", ForecastMonitorDAOHibernate.class);
		}
		return bucketList;
	}

	/**
	 * This method is used to check the entity access details for the given role<br>
	 * 
	 * @param roleId
	 * @param entityId
	 * @return boolean
	 * @throws SwtException
	 */
	@SuppressWarnings("unchecked")
	public boolean checkEntityAccess(String hostId, String roleId,
			String entityId) throws SwtException {
		// variable to check the access
		boolean checkAccess = true;
		// Collection to hold entity access
		Collection<EntityAccess> collAccess = null;
		try {
			log.debug(this.getClass().getName()
					+ " - [checkEntityAccess] - Entering");
			// getting list of entity from database
			collAccess = (Collection<EntityAccess>) getHibernateTemplate().find(
					"from EntityAccess e where e.id.hostId = ?0 "
							+ "and e.id.roleId=?1 and e.id.entityId=?2",
					new Object[] { hostId, roleId, entityId });
			if (collAccess.size() < 1) {
				checkAccess = false;
			}
			log.debug(this.getClass().getName()
					+ " - [checkEntityAccess] - Exiting");
		} catch (Exception e) {
			log.error(this.getClass().getName()
					+ " - Exception caught in [checkEntityAccess] method "
					+ e.getMessage());
			throw SwtErrorHandler.getInstance().handleException(e,
					"checkEntityAccess", ForecastMonitorDAOHibernate.class);
		} finally {
			// nullify objects
			collAccess = null;
		}
		return checkAccess;
	}

	/**
	 * This method is used to get the assumption list details<br>
	 * 
	 * @param AssumptionData
	 *            assumptionData
	 * @return Collection<AssumptionData>
	 * @throws SwtException
	 */
	@SuppressWarnings("unchecked")
	public Collection<AssumptionData> getForecastAssumption(
			AssumptionData assumptionData) throws SwtException {
		// Collection of AssumptionData
		Collection<AssumptionData> assumptionList = null;

		try {

			log.debug(this.getClass().getName()
					+ " - [getForecastAssumption] - Entering");
			// Start: Code modified by Bala for 1053 Beta3 testing issue on
			// 14-Sep-2011 - When open the Assumption screen, it should display
			// the corresponding template records
			// create instance for AssumptionData list
			assumptionList = new ArrayList<AssumptionData>();
			if (assumptionData.getEntityId().equals("All")) {
				// getting list of assumption from database
				assumptionList = (Collection<AssumptionData> ) getHibernateTemplate().find(
								"select distinct a from AssumptionData a, EntityAccess e where a.hostId = ?0 "
										+ "and a.valueDate = ?1 and a.currencyCode = ?2 and a.templateId = ?3 and e.accessId "
										+ "IN ('0','1') and e.id.hostId = a.hostId and e.id.entityId = a.entityId",
								new Object[] { assumptionData.getHostId(),
										assumptionData.getValueDate(),
										assumptionData.getCurrencyCode(),
										assumptionData.getTemplateId() });
			} else {
				// getting list of assumption from database
				assumptionList = (Collection<AssumptionData> ) getHibernateTemplate().find(
								"from AssumptionData a where a.hostId = ?0 and a.valueDate = ?1 "
										+ "and a.entityId = ?2 and a.currencyCode = ?3 and a.templateId = ?4",
								new Object[] { assumptionData.getHostId(),
										assumptionData.getValueDate(),
										assumptionData.getEntityId(),
										assumptionData.getCurrencyCode(),
										assumptionData.getTemplateId() });
			}
			// End: Code modified by Bala for 1053 Beta3 testing issue on
			// 14-Sep-2011 - When open the Assumption screen, it should display
			// the corresponding template records
			log.debug(this.getClass().getName()
					+ " - [getForecastAssumption] - Exiting");
		} catch (Exception e) {
			log.error(this.getClass().getName()
					+ " - Exception caught in [getForecastAssumption] method "
					+ e.getMessage());
			throw SwtErrorHandler.getInstance().handleException(e,
					"getForecastAssumption", ForecastMonitorDAOHibernate.class);
		}
		return assumptionList;
	}

	/**
	 * This method is used to save the forecast assumption details.<br>
	 * 
	 * @param AssumptionData
	 *            assumptionData
	 * @throws SwtException
	 */
	@SuppressWarnings("unchecked")
	public void saveForecastAssumption(AssumptionData assumptionData)
			throws SwtException {
		// Declare AssumptionData object
		AssumptionData assumption = null;
		// AssumptionData list
		List<AssumptionData> list = null;
		try {
			log.debug(this.getClass().getName()
					+ " saveForecastAssumption() - Enter");
			if (assumptionData.getAssumptionId() != null
					&& assumptionData.getAssumptionId() != 0) {
				// get the AssumptionData details
				list = (List<AssumptionData> ) getHibernateTemplate().find(
						"from AssumptionData a where a.assumptionId = ?0",
						new Object[] { assumptionData.getAssumptionId() });
				assumption = new AssumptionData();
				// update the assumption details
				if (list.size() != 0) {
					assumption = (AssumptionData) list.get(0);
					assumption.setAssumptionsAmount(assumptionData
							.getAssumptionsAmount());
					assumption.setAssumption(assumptionData.getAssumption());
					getHibernateTemplate().update(assumption);
				}
			} else {
				// save the assumption details
				getHibernateTemplate().save(assumptionData);
			}
			getHibernateTemplate().flush();
			log.debug(this.getClass().getName()
					+ " saveForecastAssumption() - Exit");
		} catch (Exception e) {
			log.error("Exception occured in saving forecast assumption"
					+ e.getMessage());
			throw SwtErrorHandler.getInstance()
					.handleException(e, "saveForecastAssumption",
							ForecastMonitorDAOHibernate.class);
		} finally {
			// nullify objects
			list = null;
			assumption = null;
		}
	}

	/**
	 * This method is used to delete the forecast assumption details.<br>
	 * 
	 * @param AssumptionData
	 *            assumptionData
	 * @throws SwtException
	 */
	public void deleteForecastAssumption(AssumptionData assumptionData)
			throws SwtException {
		Session session = null;
		Transaction tx = null;
		SwtInterceptor interceptor = null;
		try {
			log.debug(this.getClass().getName()
					+ " deleteForecastAssumption() - Enter");
			interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");
			session = getHibernateTemplate().getSessionFactory().withOptions().interceptor(interceptor).openSession();
			tx = session.beginTransaction();
			session.delete(assumptionData);
			tx.commit();
			log.debug(this.getClass().getName()
					+ " deleteForecastAssumption() - Exit");
		} catch (Exception e) {
			log.error("Exception occured in deleting forecast assumption"
					+ e.getMessage());
			throw SwtErrorHandler.getInstance().handleException(e,
					"deleteForecastAssumption",
					ForecastMonitorDAOHibernate.class);
		} finally {
			JDBCCloser.close(session);
		}
	}

	/**
	 * This method is used to delete the user template details.<br>
	 * 
	 * @param UserTemplate
	 *            userTemplate
	 * @throws SwtException
	 */
	public void deleteForecastUserTemplate(UserTemplate userTemplate)
			throws SwtException {
		// collection to hold user template
		Collection<UserTemplate> template = null;
		Session session = null;
		Transaction tx = null;
		SwtInterceptor interceptor = null;
		try {
			log.debug(this.getClass().getName()
					+ " deleteForecastUserTemplate() - Enter");
			// create instance for UserTemplate collection
			template = new ArrayList<UserTemplate>();
			interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");
			session = getHibernateTemplate().getSessionFactory().withOptions().interceptor(interceptor).openSession();
			tx = session.beginTransaction();
			// get the user template details
			template = getUserTemplateDetails(userTemplate);
			if (template.size() != 0) {
				session.delete(userTemplate);
				tx.commit();
			}
			log.debug(this.getClass().getName()
					+ " deleteForecastUserTemplate() - Exit");
		} catch (Exception e) {
			log.error("Exception occured in saving forecast user template"
					+ e.getMessage());
			throw SwtErrorHandler.getInstance().handleException(e,
					"deleteForecastUserTemplate",
					ForecastMonitorDAOHibernate.class);
		} finally {
			// nullify objects
			template = null;
			JDBCCloser.close(session);
		}
	}

	/**
	 * This method is used to delete the bucket details.<br>
	 * 
	 * @param UserBuckets
	 *            userBuckets
	 * @param boolean
	 *            isAll
	 * @throws SwtException
	 */
	@SuppressWarnings("unchecked")
	public void deleteForecastBucket(UserBuckets userBuckets, boolean isAll)
			throws SwtException {
		// collection to hold user buckets
		Collection<UserBuckets> bucket = null;
		try {
			log.debug(this.getClass().getName()
					+ " deleteForecastBucket() - Enter");
			// crate new instance for userbucket collection
			bucket = new ArrayList<UserBuckets>();
			// get the user bucket details
			bucket = getUserBucketDetails(userBuckets);
			bucket = (Collection<UserBuckets> ) getHibernateTemplate().find(
							"from UserBuckets e where e.id.bucketId != '1' and e.id.bucketId != '2'");
			getHibernateTemplate().deleteAll(bucket);
			getHibernateTemplate().flush();
			log.debug(this.getClass().getName()
					+ " deleteForecastBucket() - Exit");
		} catch (Exception e) {
			log.error("Exception occured in saving forecast user bucket"
					+ e.getMessage());
			throw SwtErrorHandler.getInstance().handleException(e,
					"deleteForecastBucket", ForecastMonitorDAOHibernate.class);
		} finally {
			// nullify objects
			bucket = null;
		}
	}

	/**
	 * This method is used to fetch the details of the currency for All entity
	 * 
	 * @param userId
	 * @return Collection
	 * @throws SwtException
	 */
	@SuppressWarnings("unchecked")
	public Collection<CurrencyMaster> getCurrencyForAllEntity(String userId)
			throws SwtException {
		// List of CurrencyMaster
		List<CurrencyMaster> data = null;
		try {
			log.debug(this.getClass().getName()
					+ " - [getCurrencyForAllEntity] - Entering");

			data = (List<CurrencyMaster> ) getHibernateTemplate().find(
							"select m from Currency c, CurrencyMaster m,"
									+ " CurrencyGroupAccess g, EntityAccess a, User u WHERE c.id.hostId = g.id.hostId"
									+ " AND c.id.entityId = g.id.entityId AND c.id.currencyCode = m.currencyCode"
									+ " AND c.currencyGroupId = g.id.currencyGroupId AND c.id.currencyCode != '*'"
									+ " AND g.id.roleId = u.roleId AND g.id.hostId = u.id.hostId AND g.accessId IN ('0','1')"
									+ " AND a.accessId IN ('0','1') AND u.id.userId = ?0 AND g.id.roleId = a.id.roleId"
									+ " AND g.id.hostId = a.id.hostId ORDER BY c.id.currencyCode",
							new Object[] { userId });
			log.debug(this.getClass().getName()
					+ " - [getCurrencyForAllEntity] - Exiting");
		} catch (Exception e) {
			log.error(this.getClass().getName()
					+ " - [getCurrencyForAllEntity] - Exception -"
					+ e.getMessage());
			// Re-throwing SwtException
			throw SwtErrorHandler.getInstance().handleException(e,
					"getCurrencyForAllEntity",
					ForecastMonitorDAOHibernate.class);
		}
		return data;
	}

	// Start: Code added by Bala for 1053 Beta3 testing issue on 14-Sep-2011 -
	// Main screen should display the Template Id tooltip as Template Name
	/**
	 * 
	 * This Method is used to get the forecast Monitor template details
	 * 
	 * @param ForecastMonitorTemplate
	 *            forecastMonitorTemplate
	 * @return ForecastMonitorTemplate object
	 */
	@SuppressWarnings("unchecked")
	public ForecastMonitorTemplate getForecastMonitorTemplateDetails(
			ForecastMonitorTemplate forecastMonitorTemplate)
			throws SwtException {
		// List to hold the forecast template details
		List<ForecastMonitorTemplate> forecastTemplate = null;
		try {
			log.debug(this.getClass().getName()
					+ " - [getForecastMonitorTemplateDetails] -Entry");
			// Query to get the forecastmonitor templates details
			forecastTemplate = (List<ForecastMonitorTemplate>) getHibernateTemplate().find(
					"from ForecastMonitorTemplate fcast where fcast.id.hostId=?0"
							+ " and fcast.id.templateId=?1",
					new Object[] { forecastMonitorTemplate.getId().getHostId(),
							forecastMonitorTemplate.getId().getTemplateId() });

		} catch (Exception exp) {
			log.error("An exception occured in " + this.getClass().getName()
					+ "- getForecastMonitorTemplateDetails -"
					+ exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp,
					"getForecastMonitorTemplateDetails", this.getClass());
		} finally {
			log.debug(this.getClass().getName()
					+ " - [getForecastMonitorTemplateDetails] -Exit");
		}
		return (ForecastMonitorTemplate) forecastTemplate.get(0);
	}
	// End: Code added by Bala for 1053 Beta3 testing issue on 14-Sep-2011 -
	// Main screen should display the Template Id tooltip as Template Name

}