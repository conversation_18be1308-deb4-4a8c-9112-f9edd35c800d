/*
 * @(#)MetagroupMonitorDAOHibernate.java 1.0 04/09/06
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.work.dao.hibernate;

import java.sql.CallableStatement;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;




import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.dao.DataAccessResourceFailureException;

import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.util.JDBCCloser;
import org.swallow.util.SwtConstants;
import org.swallow.util.SwtUtil;
import org.swallow.util.SystemFormats;
import org.swallow.work.dao.MetagroupMonitorDAO;
import org.swallow.work.model.MetagroupCodePredictedBalanceTO;
import org.swallow.work.model.MetagroupMonitorCurrencyBalanceTO;
import org.springframework.context.annotation.Lazy;
import org.springframework.orm.hibernate5.support.HibernateDaoSupport;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.stereotype.Repository;
import org.springframework.beans.factory.annotation.Autowired;
import org.hibernate.HibernateException;
import org.hibernate.SessionFactory;
import org.hibernate.Transaction;
import org.hibernate.Session;

/**
 * <pre>
 * DAO layer for MetaGroupMonitor Screen
 * Class used to
 *  - Display Meta Group Monitor Details
 *  - Display Group Monitor Details
 *  - Display Book Group Monitor Details
 *  - Get BookGroupMonitor JobStatusFlag  
 * </pre>
 */
@Repository ("metagroupMonitorDAO")
@Transactional
public class MetagroupMonitorDAOHibernate extends HibernateDaoSupport implements
		MetagroupMonitorDAO {
	public MetagroupMonitorDAOHibernate(@Lazy SessionFactory sessionfactory) {
	    setSessionFactory(sessionfactory);
	}

	/** The log instance */
	private static final Log log = LogFactory
			.getLog(MetagroupMonitorDAOHibernate.class);

	/**
	 * This method is used to retrieve the MetagroupMonitorDetailsUsingStoredProc persistent object
	 * from database through stored procedure
	 * pk_monitors.sp_metagroup_monitor
	 * 
	 * @param entityId
	 *            the entity id
	 * @param currencyCode
	 *            the currency code
	 * @param valueDate
	 *            the value date for which the metagroup codes will be fetched
	 * @param roleId
	 * @param locationId
	 * @return Collection of MetagroupCodePredictedBalanceTO objects
	 * @throws SwtException 
	 */
	@SuppressWarnings("unchecked")
	public ArrayList<Object> getMetagroupMonitorDetailsUsingStoredProc(
			String entityId, String currencyCode, Date valueDate,
			SystemFormats format, ArrayList metagroupMonitorDetailsTotalList,
			String roleId, String locationId) throws SwtException {

		/* Initializing the valriables used in this method */
		String hostId = null;
		ArrayList<Object> arrMonitorDetails = null;
		// Holds the metagroup monitor details based on the criteria
		// entityId,Currency and Value date
		Collection<MetagroupCodePredictedBalanceTO> metagroupMonitorDetailsList = null;
		// Stores whether the predicted balance is negative
		boolean predictedBalanceNegative;
		// Variables for executing the stored procedure
		Session session = null;
		// variable for connection
		Connection conn = null;
		// variable for callablestatement
		CallableStatement callabaleStatement = null;
		// Stores the totals and predicted balances based on the format for the
		// current entity
		String predictedBalanceString = null;
		// Stringvariable for formattedtotal
		String formattedTotal = null;
		// variable for total
		double total ;
		// Stores the output of the stores procedure in resultset
		ResultSet rsDetails = null;
		// variable for ResultSet
		ResultSet rsTotal = null;
		// variable for MetagroupCodePredictedBalanceTO
		MetagroupCodePredictedBalanceTO predictedBalanceDetails = null;
		// variable for metagroupcode
		String metagroupCode = null;
		// variable for metagroupname
		String metagroupName = null;
		// variable for levelName
		String levelName = null;
		// variable for predictedBalance
		Double predictedBalance = null;
		// variable for totalegativeFlag
		boolean totalNegativeFlag;
		// variable for MetagroupCodePredictedBalanceTO
		MetagroupMonitorCurrencyBalanceTO metagroupMonitorCurrencyBalanceTO = null;
		/*
		 * ResultSet object initialize the null value and used to get the data
		 * from DB.
		 */
		ResultSet rsTabFlag = null;
		// Variable to hold Tab Flag
		String tabFlag = null;

		try {
			
			log
					.debug(this.getClass().getName()
							+ "- [ getMetagroupMonitorDetailsUsingStoredProc ] - Entering ");
			// Stores the current entity name from the property file.
			hostId = SwtUtil.getCurrentHostId();
			metagroupMonitorDetailsList = new ArrayList<MetagroupCodePredictedBalanceTO>();
			arrMonitorDetails = new ArrayList<Object>();
			total=0.0;
			// Opens the session object for executing the CallableStatement
			session = getHibernateTemplate().getSessionFactory().openSession();
			// Opens a connection to database
			conn = SwtUtil.connection(session);

			log
					.debug(this.getClass().getName()
							+ "- [ getMetagroupMonitorDetailsUsingStoredProc ] -Calling pk_monitors.sp_metagroup_monitor starts at - "
							+ SwtUtil.getTimeWithMilliseconds());
			callabaleStatement = conn
					.prepareCall("{call pk_monitors.sp_metagroup_monitor(?,?,?,?,?,?,?,?,?)}");
			// Setting the input parameters for the stored procedure
			callabaleStatement.setString(1, hostId);
			callabaleStatement.setString(2, entityId);
			callabaleStatement.setString(3, currencyCode);
			callabaleStatement.setDate(4,
					SwtUtil.truncateDateTime(valueDate));
			callabaleStatement.setString(5, locationId);
			callabaleStatement.setString(6, roleId);
			// Registering the output parameters for the stored procedure
			callabaleStatement.registerOutParameter(7,
					oracle.jdbc.OracleTypes.CURSOR);
			callabaleStatement.registerOutParameter(8,
					oracle.jdbc.OracleTypes.CURSOR);
			callabaleStatement.registerOutParameter(9,
					oracle.jdbc.OracleTypes.CURSOR);
			// Executing the stored procedure with the parameters passed
			callabaleStatement.execute();

			log.debug(this.getClass().getName()
					+ "- [ getMetagroupMonitorDetailsUsingStoredProc ]-Calling pk_monitors.sp_metagroup_monitor ends at - "
					+ SwtUtil.getTimeWithMilliseconds());
			// Fetching the result set from the output parameters
			rsDetails = (ResultSet) callabaleStatement.getObject(7);
			rsTotal = (ResultSet) callabaleStatement.getObject(8);
			// Iterates the result set and stores the values in the bean
			if (rsDetails != null) {
				while (rsDetails.next()) {
					metagroupCode = rsDetails.getString(1);
					metagroupName = rsDetails.getString(2);
					levelName = rsDetails.getString(3);

					if (rsDetails.getString(4) != null
							&& !(SwtConstants.EMPTY_STRING.equals(rsDetails
									.getString(4)))) {
						predictedBalance = Double.valueOf(rsDetails
								.getString(4));
					} else {
						predictedBalance = Double.valueOf("0");
					}
					// Checks whether the predicted balance is negative,If
					// negative stores true.
					predictedBalanceNegative = (predictedBalance.doubleValue() >= 0) ? false
							: true;
					// start code modified by nageswararao for mantis 1597 on
					// 20120227
					predictedBalanceString = SwtUtil.formatCurrency(
							currencyCode, new Double(predictedBalance
									.doubleValue()));
					// start code modified by nageswararao for mantis 1597 on
					// 20120227
					// Metagroup details are set into a
					// MetagroupCodePredictedBalanceTO bean
					predictedBalanceDetails = new MetagroupCodePredictedBalanceTO(
							metagroupCode, metagroupName, "", "", "", "", "",
							levelName, predictedBalanceString,
							predictedBalanceNegative, predictedBalance);

					metagroupMonitorDetailsList.add(predictedBalanceDetails);
				}
			}
			if (rsTotal != null) {
				if(rsTotal.next())
				{
				total = rsTotal.getDouble(1);
			}
			}
			
				formattedTotal = SwtUtil.formatCurrency(currencyCode, new Double(
					total));
					
			// Checks whether the total balance is negative,If negative stores
			// true.
			totalNegativeFlag = (total >= 0) ? false : true;
			// Stores the total values in the MetagroupMonitorCurrencyBalanceTO
			// bean
			metagroupMonitorCurrencyBalanceTO = new MetagroupMonitorCurrencyBalanceTO(
					formattedTotal, new Date(), totalNegativeFlag);

			metagroupMonitorDetailsTotalList
					.add(metagroupMonitorCurrencyBalanceTO);
			// To get the Tabflag values for Result Set
			rsTabFlag = (ResultSet) callabaleStatement.getObject(9);
			// To initial the Tab Flag value
			tabFlag = "";
			// To read the Tab flag Values and set the Local String value
			if (rsTabFlag != null) {
				while (rsTabFlag.next()) {
					tabFlag = tabFlag + rsTabFlag.getString(1);
				}
			}
			arrMonitorDetails.add(metagroupMonitorDetailsList);
			// To add the arraylist for tabflag
			arrMonitorDetails.add(tabFlag);

		} catch (HibernateException hibernateException) {
			log.error(this.getClass().getName()
					+ " - [getMetagroupMonitorDetailsUsingStoredProc] - "
					+ "Exception - " + hibernateException.getMessage());
			throw new SwtException(hibernateException.getMessage());
		} catch (SQLException sqlException) {
			log.error(this.getClass().getName()
					+ " - [getMetagroupMonitorDetailsUsingStoredProc] - "
					+ "Exception - " + sqlException.getMessage());
			throw new SwtException(sqlException.getMessage());
		} finally {
			
			//edited by KaisBS for mantis 1745 : introducing the implementation of the JDBCCloser class
			SwtException thrownException = null;
			SQLException sqlException = null;
			
			sqlException = JDBCCloser.close(rsDetails, rsTotal);
			if (sqlException!=null)
				thrownException = new SwtException(sqlException.getMessage());
			
			Object[] exceptions = JDBCCloser.close(null, callabaleStatement, conn, session);
			
			if (thrownException == null && exceptions[0] != null)
				thrownException = new SwtException(((SQLException) exceptions[0]).getMessage());
			
			if (thrownException == null && exceptions[1] !=null) 
				thrownException = new SwtException(((HibernateException) exceptions[1]).getMessage());
			
			if (thrownException!=null)
				throw thrownException;
						
			log.debug(this.getClass().getName()
					+ "- [ getMetagroupMonitorDetailsUsingStoredProc ] - Exit ");
		}
		return arrMonitorDetails;
	}

	/**
	 * This method is used to retrieve the groupMonitorDetailsUsingStoredProc persistent object
	 * from database through stored procedure
	 *  pk_monitors.sp_group_monitor
	 * @param entityId
	 *            the entity id
	 * @param currencyCode
	 *            the currency code
	 * @param valueDate
	 *            the value date for which the group codes will be fetched
	 * @param roleId
	 * @param locationId
	 * @param metagroupId
	 * @param valueDate
	 * @return Collection of GroupCodePredictedBalanceTO objects
	 * @throws SwtException 
	 */
	/*
	 * (non-Javadoc)
	 * 
	 * @see org.swallow.work.dao.MetagroupMonitorDAO#getGroupMonitorDetailsUsingStoredProc(java.lang.String,
	 *      java.lang.String, java.util.Date, org.swallow.util.SystemFormats,
	 *      java.util.ArrayList, java.lang.String, java.lang.String,
	 *      java.lang.String)
	 */
	@SuppressWarnings("unchecked")
	public ArrayList<Object> getGroupMonitorDetailsUsingStoredProc(
			String entityId, String currencyCode, Date valueDate,
			SystemFormats format, ArrayList groupMonitorDetailsTotalList,
			String roleId, String locationId, String metagroupId)
			throws SwtException {

		/* Initializing the valriables used in this method */
		String hostId = null;
		ArrayList<Object> grpMonitorDetailsList = null;
		// Holds the group monitor details based on the criteria
		// entityId,Currency and Value date
		Collection<MetagroupCodePredictedBalanceTO> groupMonitorDetailsList = null;
		// Stores whether the predicted balance is negative
		boolean predictedBalanceNegative;
		// Variables for executing the stored procedure
		Session session = null;
		// decalre the variable forConnection
		Connection connection = null;
		// decalre the variable for callable Statement
		CallableStatement callableStmt = null;
		// Stores the output of the stores procedure in resultset
		ResultSet rsDetails = null;
		// ResultSet holds the null value
		ResultSet rsTotal = null;
		// groupcode holds the null value
		String groupCode = null;
		// groupName holds the null value
		String groupName = null;
		// level name holds the null value
		String levelName = null;
		// predictedBalance holds the null value
		Double predictedBalance = null;
		// predictedBalanceString holds the null value
		String predictedBalanceString = null;
		boolean totalNegativeFlag;
		// MetagroupCodePredictedBalanceTO holds the null value
		MetagroupCodePredictedBalanceTO predictedBalanceDetails = null;
		// MetagroupMonitorCurrencyBalanceTO holds the null value
		MetagroupMonitorCurrencyBalanceTO groupMonitorCurrencyBalanceTO = null;
		// intitilizze the totla value
		double total ;
		// intitilizze the formattedtotla value
		String formattedTotal = null;
		// Stores the current entity name from the property file.

		/*
		 * ResultSet object initialize the null value and used to get the data
		 * from DB.
		 */
		ResultSet rsTabFlag = null;
		// Variable to hold Tab Flag
		String tabFlag = null;
		// Variable to hold predictedBalance
		predictedBalance = null;
		try {
			log
					.debug(this.getClass().getName()
							+ "- [ getGroupMonitorDetailsUsingStoredProc ] - Entering ");
			grpMonitorDetailsList = new ArrayList<Object>();
			groupMonitorDetailsList = new ArrayList<MetagroupCodePredictedBalanceTO>();
			hostId = SwtUtil.getCurrentHostId();
			total = 0.0;
			// Opens the session object for executing the CallableStatement
			session = getHibernateTemplate().getSessionFactory().openSession();
			// Opens a connection to database
			connection = SwtUtil.connection(session);

			log
					.debug(this.getClass().getName()
							+ "- [ getGroupMonitorDetailsUsingStoredProc ] -Calling pk_monitors.sp_group_monitor starts at - "
							+ SwtUtil.getTimeWithMilliseconds());
			callableStmt = connection
					.prepareCall("{call pk_monitors.sp_group_monitor(?,?,?,?,?,?,?,?,?,?)}");
			// Setting the input parameters for the stored procedure
			callableStmt.setString(1, hostId);
			callableStmt.setString(2, entityId);
			callableStmt.setString(3, currencyCode);
			callableStmt.setDate(4, SwtUtil.truncateDateTime(valueDate));
			callableStmt.setString(5, locationId);
			callableStmt.setString(6, metagroupId);
			callableStmt.setString(7, roleId);
			// Registering the output parameters for the stored procedure
			callableStmt
					.registerOutParameter(8, oracle.jdbc.OracleTypes.CURSOR);
			callableStmt
					.registerOutParameter(9, oracle.jdbc.OracleTypes.CURSOR);
			callableStmt.registerOutParameter(10,
					oracle.jdbc.OracleTypes.CURSOR);
			// Executing the stored procedure with the parameters passed
			callableStmt.execute();
			log
					.debug(this.getClass().getName()
							+ "- [ getGroupMonitorDetailsUsingStoredProc ] - Calling pk_monitors.sp_group_monitor ends at - "
							+ SwtUtil.getTimeWithMilliseconds());
			// Fetching the result set from the output parameters
			rsDetails = (ResultSet) callableStmt.getObject(8);
			rsTotal = (ResultSet) callableStmt.getObject(9);
			// Iterates the result set and stores the values in the bean
			if (rsDetails != null) {
				while (rsDetails.next()) {

					groupCode = rsDetails.getString(1);
					groupName = rsDetails.getString(2);
					levelName = rsDetails.getString(3);
					if (rsDetails.getString(4) != null
							&& !(SwtConstants.EMPTY_STRING.equals(rsDetails
									.getString(4)))) {
						predictedBalance = Double.valueOf(rsDetails
								.getString(4));
					} else {
						predictedBalance = Double.valueOf("0");
					}

					// Checks whether the predicted balance is negative,If
					// negative stores true.
					predictedBalanceNegative = (predictedBalance.doubleValue() >= 0) ? false
							: true;
					// start: code modified by nageswararao for mantis 1597 on
					// 20120303
					predictedBalanceString = SwtUtil.formatCurrency(
							currencyCode, new Double(predictedBalance
									.doubleValue()));
					
					// end: code modified by nageswararao for mantis 1597 on
					// 20120303
					// Group details are set into a
					// MetagroupCodePredictedBalanceTO bean
					predictedBalanceDetails = new MetagroupCodePredictedBalanceTO(
							"", "", "", "", groupCode, groupName, "",
							levelName, predictedBalanceString,
							predictedBalanceNegative, predictedBalance);

					groupMonitorDetailsList.add(predictedBalanceDetails);
				}
			}

			if (rsTotal != null && rsTotal.next()) {
				total = rsTotal.getDouble(1);
					}
				formattedTotal = SwtUtil.formatCurrency(currencyCode, new Double(
					total));
				// Checks whether the total balance is negative,If negative stores
			// true.
			totalNegativeFlag = (total >= 0) ? false : true;
			// Stores the total values in the MetagroupMonitorCurrencyBalanceTO
			// bean
			groupMonitorCurrencyBalanceTO = new MetagroupMonitorCurrencyBalanceTO(
					formattedTotal, new Date(), totalNegativeFlag);

			groupMonitorDetailsTotalList.add(groupMonitorCurrencyBalanceTO);
			// To get the Tabflag values for Result Set
			rsTabFlag = (ResultSet) callableStmt.getObject(10);
			// To initial the Tab Flag value
			tabFlag = "";
			// To read the Tab flag Values and set the Local String value
			if (rsTabFlag != null) {
				while (rsTabFlag.next()) {
					tabFlag = tabFlag + rsTabFlag.getString(1);
				}
			}
			grpMonitorDetailsList.add(groupMonitorDetailsList);
			// To add the arraylist for tabflag
			grpMonitorDetailsList.add(tabFlag);

		} catch (DataAccessResourceFailureException dataAccessException) {
			log.error(this.getClass().getName()
					+ " - [getGroupMonitorDetailsUsingStoredProc] - "
					+ "Exception - " + dataAccessException.getMessage());
			throw SwtErrorHandler.getInstance().handleException(
					dataAccessException, "getAllBalancesUsingStoredProc",
					MetagroupMonitorDAOHibernate.class);
		} catch (IllegalStateException illegalStateException) {
			log.error(this.getClass().getName()
					+ " - [getGroupMonitorDetailsUsingStoredProc] - "
					+ "Exception - " + illegalStateException.getMessage());
			throw SwtErrorHandler.getInstance().handleException(
					illegalStateException, "getAllBalancesUsingStoredProc",
					MetagroupMonitorDAOHibernate.class);
		} catch (HibernateException hibernateException) {
			log.error(this.getClass().getName()
					+ " - [getGroupMonitorDetailsUsingStoredProc] - "
					+ "Exception - " + hibernateException.getMessage());
			throw SwtErrorHandler.getInstance().handleException(
					hibernateException, "getAllBalancesUsingStoredProc",
					MetagroupMonitorDAOHibernate.class);
		} catch (SQLException sqlException) {
			log.error(this.getClass().getName()
					+ " - [getGroupMonitorDetailsUsingStoredProc] - "
					+ "Exception - " + sqlException.getMessage());
			throw SwtErrorHandler.getInstance().handleException(sqlException,
					"getGroupMonitorDetailsUsingStoredProc",
					MetagroupMonitorDAOHibernate.class);
		} catch (SwtException swtException) {
			log.error(this.getClass().getName()
					+ " - [getGroupMonitorDetailsUsingStoredProc] - "
					+ "Exception - " + swtException.getMessage());
			throw swtException;
		} finally {
			
			//edited by KaisBS for mantis 1745 : introducing the implementation of the JDBCCloser class
			SwtException thrownException = null;
			SQLException sqlException = null;
			
			sqlException = JDBCCloser.close(rsDetails, rsTotal);
			if (sqlException!=null)
				thrownException = new SwtException(sqlException.getMessage());
			
			Object[] exceptions = JDBCCloser.close(null, callableStmt, connection, session);
			
			if (thrownException == null && exceptions[0] != null)
				thrownException = new SwtException(((SQLException) exceptions[0]).getMessage());
			
			if (thrownException == null && exceptions[1] !=null) 
				thrownException = new SwtException(((HibernateException) exceptions[1]).getMessage());
			
			if (thrownException!=null)
				throw thrownException;
			
			
			log.debug(this.getClass().getName()
					+ "- [ getGroupMonitorDetailsUsingStoredProc ] - Exit ");
		}

		return grpMonitorDetailsList;
	}

	/**
	 * This method is used to retrieve the BookMonitorDetailsUsingStoredProc persistent object
	 * from database through stored procedure
	 * pk_monitors.sp_bookcode_monitor
	 * 
	 * @param entityId
	 * 
	 * @param currencyCode
	 * 
	 * @param valueDate
	 * 
	 * @param roleId
	 * @param locationId
	 * @param groupCode
	 * @return Collection of EntityBookCodeTO objects
	 * @throws SwtException
	 */
	@SuppressWarnings("unchecked")
	public ArrayList<Object> getBookMonitorDetailsUsingStoredProc(
			String entityId, String currencyCode, Date valueDate,
			SystemFormats format, ArrayList bookMonitorDetailsTotalList,
			String roleId, String locationId, String groupCode)
			throws SwtException {
		/* Initializing the valriables used in this method */
		String hostId = null;
		ArrayList<Object> arrCollection = null;
		// Holds the book monitor details based on the criteria
		// entityId,Currency and Value date
		Collection<MetagroupCodePredictedBalanceTO> bookMonitorDetailsList = null;
		// Stores whether the predicted balance is negative
		boolean predictedBalanceNegative;
		// Variables for executing the stored procedure
		Session session = null;
		Connection connection = null;
		CallableStatement callableStatement = null;
		// Stores the output of the stores procedure in resultset
		ResultSet resultsetDetails = null;
		// Result set holds the null value
		ResultSet rsTotal = null;
		// book code set holds the null value
		String bookCode = null;
		// book name set holds the null value
		String bookName = null;
		// location holds the null value
		String locationName = null;
		// predictbalance holds the null value
		Double predictedBalance = null;
		// predictbalance holds the null value
		String predictedBalanceString = null;
		boolean totalNegativeFlag;
		// MetagroupCodePredictedBalanceTO holds the null value
		MetagroupCodePredictedBalanceTO predictedBalanceDetails = null;
		// metagroupmonitorcurrency BalanaceTo holds the null value
		MetagroupMonitorCurrencyBalanceTO bookMonitorCurrencyBalanceTO = null;
		// for grandtotal
		double total;
		String formattedTotal = null;

		ResultSet rsTabFlag = null;
		// Variable to hold Tab Flag
		String tabFlag = null;
		try {
			log.debug(this.getClass().getName()
					+ "- [ getBookMonitorDetailsUsingStoredProc ] - Entering ");
			// Stores the current entity name from the property file.
			hostId = SwtUtil.getCurrentHostId();
			arrCollection = new ArrayList<Object>();
			bookMonitorDetailsList = new ArrayList<MetagroupCodePredictedBalanceTO>();
			total = 0.0;
			// Opens the session object for executing the CallableStatement
			session = getHibernateTemplate().getSessionFactory().openSession();
			// Opens a connection to database
			connection = SwtUtil.connection(session);
			log
					.debug(this.getClass().getName()
							+ "-[ getBookMonitorDetailsUsingStoredProc ] - Calling pk_monitors.sp_bookcode_monitor starts at - "
							+ SwtUtil.getTimeWithMilliseconds());

			callableStatement = connection
					.prepareCall("{call pk_monitors.sp_bookcode_monitor(?,?,?,?,?,?,?,?,?,?)}");
			// Setting the input parameters for the stored procedure
			callableStatement.setString(1, hostId);
			callableStatement.setString(2, entityId);
			callableStatement.setString(3, currencyCode);
			callableStatement
					.setDate(4, SwtUtil.truncateDateTime(valueDate));
			callableStatement.setString(5, locationId);

			callableStatement.setString(6, groupCode);
			callableStatement.setString(7, roleId);
			// Registering the output parameters for the stored procedure
			callableStatement.registerOutParameter(8,
					oracle.jdbc.OracleTypes.CURSOR);
			callableStatement.registerOutParameter(9,
					oracle.jdbc.OracleTypes.CURSOR);
			callableStatement.registerOutParameter(10,
					oracle.jdbc.OracleTypes.CURSOR);
			// Executing the stored procedure with the parameters passed
			callableStatement.execute();
			log
					.debug(this.getClass().getName()
							+ "- [ getBookMonitorDetailsUsingStoredProc ] - Calling pk_monitors.sp_bookcode_monitor ends at - "
							+ SwtUtil.getTimeWithMilliseconds());
			// Fetching the result set from the output parameters
			resultsetDetails = (ResultSet) callableStatement.getObject(8);
			rsTotal = (ResultSet) callableStatement.getObject(9);
			// Iterates the result set and stores the values in the bean
			if (resultsetDetails != null) {
				while (resultsetDetails.next()) {

					bookCode = resultsetDetails.getString(1);
					bookName = resultsetDetails.getString(2);
					locationName = resultsetDetails.getString(3);

					// predictedBalance = Double.valueOf(rs.getString(4));
					if (resultsetDetails.getString(4) != null
							&& !(SwtConstants.EMPTY_STRING.equals(resultsetDetails
									.getString(4)))) {
						predictedBalance = Double.valueOf(resultsetDetails
								.getString(4));
					} else {
						predictedBalance = Double.valueOf("0");
					}

					// Checks whether the predicted balance is negative,If
					// negative stores true.
					predictedBalanceNegative = (predictedBalance.doubleValue() >= 0) ? false
							: true;
					// start: code modified by nageswararao for mantis 1597 on
					// 20120303
					predictedBalanceString = SwtUtil.formatCurrency(
							currencyCode, new Double(predictedBalance
									.doubleValue()));
					// end: code modified by nageswararao for mantis 1597 on
					// 20120303
				
					// Book details are set into a
					// MetagroupCodePredictedBalanceTO bean
					predictedBalanceDetails = new MetagroupCodePredictedBalanceTO(
							"", "", bookCode, bookName, "", "", locationName,
							"", predictedBalanceString,
							predictedBalanceNegative, predictedBalance);

					bookMonitorDetailsList.add(predictedBalanceDetails);
				}
			}
               
			if (rsTotal != null) {
				if(rsTotal.next())
				{
				total = rsTotal.getDouble(1);
			}
			}
				formattedTotal = SwtUtil.formatCurrency(currencyCode, new Double(
					total));
			
			// Checks whether the total balance is negative,If negative stores
			// true.
			totalNegativeFlag = (total >= 0) ? false : true;
			// Stores the total values in the MetagroupMonitorCurrencyBalanceTO
			// bean
			bookMonitorCurrencyBalanceTO = new MetagroupMonitorCurrencyBalanceTO(
					formattedTotal, new Date(), totalNegativeFlag);

			bookMonitorDetailsTotalList.add(bookMonitorCurrencyBalanceTO);
			// To get the Tabflag values for Result Set
			rsTabFlag = (ResultSet) callableStatement.getObject(10);
			// To initial the Tab Flag value
			tabFlag = "";
			// To read the Tab flag Values and set the Local String value
			if (rsTabFlag != null) {
				while (rsTabFlag.next()) {
					tabFlag = tabFlag + rsTabFlag.getString(1);
				}
			}
			arrCollection.add(bookMonitorDetailsList);
			// To add the arraylist for tabflag
			arrCollection.add(tabFlag);

		} catch (DataAccessResourceFailureException dataAccessException) {
			log.error(this.getClass().getName()
					+ " - [getBookMonitorDetailsUsingStoredProc] - "
					+ "Exception - " + dataAccessException.getMessage());
			throw SwtErrorHandler.getInstance().handleException(
					dataAccessException,
					"getBookMonitorDetailsUsingStoredProc",
					MetagroupMonitorDAOHibernate.class);
		} catch (IllegalStateException illegalStateException) {
			log.error(this.getClass().getName()
					+ " - [getBookMonitorDetailsUsingStoredProc] - "
					+ "Exception - " + illegalStateException.getMessage());
			throw SwtErrorHandler.getInstance().handleException(
					illegalStateException,
					"getBookMonitorDetailsUsingStoredProc",
					MetagroupMonitorDAOHibernate.class);
		} catch (HibernateException hibernateException) {
			log.error(this.getClass().getName()
					+ " - [getBookMonitorDetailsUsingStoredProc] - "
					+ "Exception - " + hibernateException.getMessage());
			throw SwtErrorHandler.getInstance().handleException(
					hibernateException, "getBookMonitorDetailsUsingStoredProc",
					MetagroupMonitorDAOHibernate.class);
		} catch (SQLException sqlException) {
			log.error(this.getClass().getName()
					+ " - [getBookMonitorDetailsUsingStoredProc] - "
					+ "Exception - " + sqlException.getMessage());
			throw SwtErrorHandler.getInstance().handleException(sqlException,
					"getBookMonitorDetailsUsingStoredProc",
					MetagroupMonitorDAOHibernate.class);
		} catch (SwtException swtException) {
			log.error(this.getClass().getName()
					+ " - [getBookMonitorDetailsUsingStoredProc] - "
					+ "swtException - " + swtException.getMessage());
			throw SwtErrorHandler.getInstance().handleException(swtException,
					"getBookMonitorDetailsUsingStoredProc",
					MetagroupMonitorDAOHibernate.class);
		} finally {
			
			//edited by KaisBS for mantis 1745 : introducing the implementation of the JDBCCloser class
			SwtException thrownException = null;
			SQLException sqlException = null;
			
			sqlException = JDBCCloser.close(resultsetDetails, rsTotal);
			if (sqlException!=null)
				thrownException = new SwtException(sqlException.getMessage());
			
			Object[] exceptions = JDBCCloser.close(null, callableStatement, connection, session);
			
			if (thrownException == null && exceptions[0] != null)
				thrownException = new SwtException(((SQLException) exceptions[0]).getMessage());
			
			if (thrownException == null && exceptions[1] !=null) 
				thrownException = new SwtException(((HibernateException) exceptions[1]).getMessage());
			
			if (thrownException!=null)
				throw thrownException;
			log.debug(this.getClass().getName()
					+ "- [ getBookMonitorDetailsUsingStoredProc ] - Exit ");
		}

		return arrCollection;
	}

}
