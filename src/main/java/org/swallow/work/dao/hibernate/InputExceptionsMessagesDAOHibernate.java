/*
 * @(#)InputExceptionsMessagesDAOHibernate.java 1.0 23/08/06
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.work.dao.hibernate;

import java.sql.CallableStatement;
import java.sql.Connection;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

import javax.persistence.TypedQuery;

import org.apache.commons.dbcp.BasicDataSource;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.pcm.work.model.MessageArchivePCM;
import org.swallow.util.JDBCCloser;
import org.swallow.util.OpTimer;
import org.swallow.util.SwtConstants;
import org.swallow.util.SwtUtil;
import org.swallow.work.dao.InputExceptionsMessagesDAO;
import org.swallow.work.model.InputExceptionsDataModel;
import org.swallow.work.model.InputExceptionsDataPageDTO;
import org.swallow.work.model.InputMessageType;


import org.springframework.context.annotation.Lazy;
import org.springframework.orm.hibernate5.support.HibernateDaoSupport;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.stereotype.Repository;
import org.springframework.beans.factory.annotation.Autowired;
import org.hibernate.HibernateException;
import org.hibernate.Query;
import org.hibernate.SessionFactory;
import org.hibernate.Transaction;
import org.hibernate.Session;


@Repository ("inputExceptionsMessagesDAO")
@Transactional
public class InputExceptionsMessagesDAOHibernate extends HibernateDaoSupport
		implements InputExceptionsMessagesDAO {
	public InputExceptionsMessagesDAOHibernate(@Lazy SessionFactory sessionfactory) {
	    setSessionFactory(sessionfactory);
	}


	/**
	 * Initializing logger object for this class
	 */
	private final Log log = LogFactory
			.getLog(InputExceptionsMessagesDAOHibernate.class);

	/**
	 * Method to get exception messages list from data base for given message
	 * status and message type
	 * 
	 * @param page
	 * @param opTimer
	 * @param dateFormat
	 * @return InputExceptionsDataPageDTO
	 * @throws SwtException
	 */
	public InputExceptionsDataPageDTO getMessagePage(
			InputExceptionsDataPageDTO page, OpTimer opTimer, String dateFormat)
			throws SwtException {

		/* Method's local variable declaration */
		String sQuery = null;
		int offset;
		List count = null;
		opTimer.start("fetch-data");
		ArrayList<Object> parameters = null;
		Session session = null;
		TypedQuery<InputExceptionsDataModel> query = null;
		List<InputExceptionsDataModel> resultList = null;

		opTimer.start("fetch-data");

		try {
		    log.debug(this.getClass().getName() + " - [getMessagePage] - " + "Entry");

		    // get the data page
		    sQuery = "FROM InputExceptionsDataModel e WHERE e.status = :messageStatus AND e.messageType = :messageType ";

		    if (page.getFromDate() != null && page.getToDate() != null) {
		        sQuery += "AND e.startTime BETWEEN "
		                + "to_date(:fromDate || ' 00:00:00', :dateFormat || ' HH24:MI:SS') "
		                + "AND to_date(:toDate || ' 23:59:59', :dateFormat || ' HH24:MI:SS') ";
		    }

		    // Condition to check order is not equal to none
		    if (page.getOrderBy() != InputExceptionsDataPageDTO.ORDER_NONE) {
		        sQuery += "ORDER BY e.";

		        // hibernate-specific order field query builder
		        switch (page.getOrderBy()) {
		            case InputExceptionsDataPageDTO.ORDER_AMOUNT1:
		                sQuery += "amount1";
		                break;
		            case InputExceptionsDataPageDTO.ORDER_SEQ_NO:
		                sQuery += "seqNo";
		                break;
		            case InputExceptionsDataPageDTO.ORDER_STATUS_NOTES:
		                sQuery += "statusNotes";
		                break;
		            case InputExceptionsDataPageDTO.ORDER_VALUE_DATE:
		                sQuery += "valueDate";
		                break;
		            case InputExceptionsDataPageDTO.ORDER_CURRENCY1:
		                sQuery += "currencyCode1";
		                break;
		            case InputExceptionsDataPageDTO.ORDER_SIGN1:
		                sQuery += "amountSign1";
		                break;
		            case InputExceptionsDataPageDTO.ORDER_REF1:
		                sQuery += "transactionReferenceNumber1";
		                break;
					case InputExceptionsDataPageDTO.ORDER_INPUT_DATE:
						sQuery += "startTime";
						break;
		        }

		        sQuery += page.isOrderDesc() ? " DESC" : " ASC";
		    }

		    offset = (page.getPageNumber() - 1) * page.getRecordsPerPage();
		    session = getHibernateTemplate().getSessionFactory().openSession();
		    query = session.createQuery(sQuery, InputExceptionsDataModel.class);
		    query.setMaxResults(page.getRecordsPerPage());
			if (offset >= 0) {
				query.setFirstResult(offset);
			} else {
				query.setFirstResult(0);
			}
		    query.setFirstResult(offset);

		    // set query parameters
		    query.setParameter("messageStatus", ""+page.getMessageStatus());
		    query.setParameter("messageType", page.getMessageType());

		    if (page.getFromDate() != null && page.getToDate() != null) {
		        query.setParameter("fromDate", page.getFromDate());
		        query.setParameter("dateFormat", dateFormat);
		        query.setParameter("toDate", page.getToDate());
		    }

		    resultList = query.getResultList();
		    page.setCurrentPageData(resultList);

			// get the total record count
			sQuery = "select count(*) from InputExceptionsDataModel e where e.status= ?0 and e.messageType= ?1";
			parameters = new ArrayList<Object>();
			parameters.add(""+page.getMessageStatus());
			parameters.add( page.getMessageType());
			
			if (page.getFromDate() != null && page.getToDate() != null) {

				sQuery += "and e.startTime between to_date( ?2 || ' 00:00:00',?3|| ' HH24:MI:SS') and to_date(?4||' 23:59:59',?5|| ' HH24:MI:SS') ";
				parameters.add(page.getFromDate());
				parameters.add(dateFormat);
				parameters.add(page.getToDate());
				parameters.add(dateFormat);
			}
			
			
			count = (List ) getHibernateTemplate().find(sQuery, parameters.toArray());

			if (count.size() > 0)
				page.setTotalSize(Integer.parseInt(count.get(0).toString()));
			opTimer.stop("fetch-data");
			log.debug(this.getClass().getName() + " - [getMessagePage] - "
					+ "Exit");
		} catch (HibernateException e) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [getMessagePage] method : - "
					+ e.getMessage());
			throw new SwtException(
					"HQL Error, could not retrieve message exceptions data page: "
							+ e.getMessage());
		} catch (Exception e) {
			e.printStackTrace();
			log.error(this.getClass().getName()
					+ " - Exception Catched in [getMessagePage] method : - "
					+ e.getMessage());
			throw new SwtException(
					"Could not retrieve message exceptions data page: "
							+ e.getMessage());
		} finally {
			JDBCCloser.close(session);
		}
		return page;
	}
	
	/**
	 * Method to get exception messages list from data base for given message
	 * status and message type
	 * 
	 * @param page
	 * @param opTimer
	 * @param dateFormat
	 * @return InputExceptionsDataPageDTO
	 * @throws SwtException
	 */
	public InputExceptionsDataPageDTO getMessagePagePCM(
			InputExceptionsDataPageDTO page, OpTimer opTimer, String dateFormat, boolean fromDashboard)
			throws SwtException {

		/* Method's local variable declaration */
		String sQuery = null;
		int offset;
		List count = null;
		opTimer.start("fetch-data");
		ArrayList<Object> parameters = null;
		Session session = null;
		
		TypedQuery<InputExceptionsDataModel> query = null;
		List<InputExceptionsDataModel> resultList = null;

		opTimer.start("fetch-data");

		try {
		    log.debug(this.getClass().getName() + " - [getMessagePagePCM] - Entry");

		    // get the data page
		    sQuery = "from MessageArchivePCM e where e.status = :messageStatus ";

		    if (!SwtConstants.ALL_VALUE.equals(page.getMessageType())) {
		        sQuery += " and e.messageType = :messageType ";
		    }

		    if (fromDashboard) {
		        sQuery += " and e.currencyCode1 = :currencyCode ";
		    }

		    if (fromDashboard) {
		        sQuery += " and e.valueDate = to_date(:valueDate, :dateFormat) ";
		    } else {
		        if (page.getFromDate() != null && page.getToDate() != null) {
		            sQuery += " and e.startTime between to_date(:fromDate || ' 00:00:00', :dateFormat || ' HH24:MI:SS') and to_date(:toDate || ' 23:59:59', :dateFormat || ' HH24:MI:SS') ";
		        }
		    }

		    /* Condition to check order is not equal to none */
		    if (page.getOrderBy() != InputExceptionsDataPageDTO.ORDER_NONE) {
		        sQuery += "order by e.";

		        // hibernate-specific order field query builder
		        switch (page.getOrderBy()) {
		            case InputExceptionsDataPageDTO.ORDER_AMOUNT1:
		                sQuery += "amount1";
		                break;
		            case InputExceptionsDataPageDTO.ORDER_SEQ_NO:
		                sQuery += "pcPaymentId";
		                break;
		            case InputExceptionsDataPageDTO.ORDER_STATUS_NOTES:
		                sQuery += "statusNotes";
		                break;
		            case InputExceptionsDataPageDTO.ORDER_VALUE_DATE:
		                sQuery += "valueDate";
		                break;
		            case InputExceptionsDataPageDTO.ORDER_CURRENCY1:
		                sQuery += "currencyCode1";
		                break;
		            case InputExceptionsDataPageDTO.ORDER_SIGN1:
		                sQuery += "amountSign1";
		                break;
		            case InputExceptionsDataPageDTO.ORDER_REF1:
		                sQuery += "transactionReferenceNumber1";
		                break;
		            case InputExceptionsDataPageDTO.ORDER_MESSAGE_TYPE:
		                sQuery += "messageType";
		                break;
		            case InputExceptionsDataPageDTO.ORDER_ACCOUNT:
		                sQuery += "accountId";
		                break;
					case InputExceptionsDataPageDTO.ORDER_INPUT_DATE:
						sQuery += "startTime";
						break;


				}

		        sQuery += page.isOrderDesc() ? " DESC" : " ASC";
		    }

		    offset = (page.getPageNumber() - 1) * page.getRecordsPerPage();
		    session = getHibernateTemplate().getSessionFactory().openSession();
		    query = session.createQuery(sQuery, InputExceptionsDataModel.class);
		    query.setMaxResults(page.getRecordsPerPage());
			if (offset >= 0) {
				query.setFirstResult(offset);
			} else {
				query.setFirstResult(0);
			}

		    query.setFirstResult(offset);

		    // set query parameters
		    query.setParameter("messageStatus", ""+page.getMessageStatus());

		    if (!SwtConstants.ALL_VALUE.equals(page.getMessageType())) {
		        query.setParameter("messageType", page.getMessageType());
		    }

		    if (fromDashboard) {
		        query.setParameter("currencyCode", page.getCurrencyCode());
		        query.setParameter("valueDate", page.getFromDate());
		        query.setParameter("dateFormat", dateFormat);
		    } else {
		        if (page.getFromDate() != null && page.getToDate() != null) {
		            query.setParameter("fromDate", page.getFromDate());
		            query.setParameter("dateFormat", dateFormat);
		            query.setParameter("toDate", page.getToDate());
		        }
		    }

		    resultList = query.getResultList();
		    page.setCurrentPageData(resultList);


			parameters = new ArrayList<Object>();
			// get the total record count
			if (fromDashboard) {
				if ("All".equals(page.getMessageType())) {
					sQuery = "select count(*) from MessageArchivePCM e where e.status= ?"+parameters.size()+" ";
					parameters.add(""+page.getMessageStatus());
				} else {
					sQuery = "select count(*) from MessageArchivePCM e where e.status= ?"+parameters.size()+" and e.messageType= ?"+(parameters.size()+1)+" ";
					parameters.add(""+page.getMessageStatus());
					parameters.add( page.getMessageType());
				}
				sQuery += "and e.valueDate = to_date(?"+parameters.size()+",?"+parameters.size()+1+") and e.currencyCode1 = ?"+(parameters.size()+2)+" ";
				parameters.add(page.getFromDate());
				parameters.add(dateFormat);
				parameters.add(page.getCurrencyCode());
			} else {
				sQuery = "select count(*) from MessageArchivePCM e where e.status= ?"+parameters.size()+" and e.messageType= ?"+parameters.size()+1+" ";
				parameters.add(""+page.getMessageStatus());
				parameters.add( page.getMessageType());
				if (page.getFromDate() != null && page.getToDate() != null) {
					
					sQuery += "and e.startTime between to_date( ?"+parameters.size()+" || ' 00:00:00',?"+(parameters.size()+1)+"|| ' HH24:MI:SS') and to_date(?"+(parameters.size()+2)+"||' 23:59:59',?"+(parameters.size()+3)+"|| ' HH24:MI:SS') ";
					parameters.add(page.getFromDate());
					parameters.add(dateFormat);
					parameters.add(page.getToDate());
					parameters.add(dateFormat);
				}
			}
			
			count = (List ) getHibernateTemplate().find(sQuery, parameters.toArray());

			if (count.size() > 0)
				page.setTotalSize(Integer.parseInt(count.get(0).toString()));
			opTimer.stop("fetch-data");
			log.debug(this.getClass().getName() + " - [getMessagePagePCM] - "
					+ "Exit");
		} catch (HibernateException e) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [getMessagePagePCM] method : - "
					+ e.getMessage());
			throw new SwtException(
					"HQL Error, could not retrieve message exceptions data page: "
							+ e.getMessage());
		} catch (Exception e) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [getMessagePagePCM] method : - "
					+ e.getMessage());
			throw new SwtException(
					"Could not retrieve message exceptions data page: "
							+ e.getMessage());
		} finally {
			JDBCCloser.close(session);
		}
		return page;
	}
	
	public Collection<String> getMessageTypeList(String currencyCode, String messageStatus, String date,
			String dateFormat) throws SwtException {
		
		String sQuery = "select distinct e.messageType from MessageArchivePCM e where e.status= ?0 and e.currencyCode1= ?1";
		ArrayList<Object> parameters = new ArrayList<Object>();
		parameters.add(messageStatus);
		parameters.add(currencyCode);

		sQuery += "and e.valueDate = to_date(?2,?3) ";
		parameters.add(date);
		parameters.add(dateFormat);

		return (Collection<String>) getHibernateTemplate().find(sQuery, parameters.toArray());
	}

	/**
	 * Method to get Archive message for selected sequence Id
	 * 
	 * @param seqId
	 * @param opTimer
	 * @return InputExceptionsDataModel
	 */
	public InputExceptionsDataModel getArchiveMessage(String seqId,
			OpTimer opTimer) {
		log.debug(this.getClass().getName() + " - [getArchiveMessages] - "
				+ "Entry");
		/* Method's local variable declaration */
		List list;
		opTimer.start("fetch-message");
		list = (List) getHibernateTemplate().find(
				"from InputExceptionsDataModel e where e.seqNo=" + seqId);
		opTimer.stop("fetch-message");

		if (list.size() > 0) {
			log.debug(this.getClass().getName() + " - [getArchiveMessages] - "
					+ "Exit");
			return (InputExceptionsDataModel) list.get(0);
		}
		log.debug(this.getClass().getName() + " - [getArchiveMessages] - "
				+ "Exit");
		return null;
	}

	/**
	 * Method to get Message Type from I_MESSAGE_TYPE
	 * 
	 * @param messageType
	 * @param opTimer
	 * @return InputMessageType
	 */
	public InputMessageType getMessageType(String messageType, OpTimer opTimer) {
		log.debug(this.getClass().getName() + " - [getMessageType] - "
				+ "Entry");
		/* Method's local variable declaration */
		List list;
		opTimer.start("fetch-messagetype");
		/* Retrieve message type from I_MESSAGE_TYPE */
		list = (List) getHibernateTemplate().find(
				"from InputMessageType e where e.messageType='" + messageType
						+ "'");
		opTimer.stop("fetch-messagetype");

		if (list.size() > 0) {
			log.debug(this.getClass().getName() + " - [getMessageType] - "
					+ "Exit");
			return (InputMessageType) list.get(0);
		}
		log
				.debug(this.getClass().getName() + " - [getMessageType] - "
						+ "Exit");
		return null;
	}

	/* Start: Modified for Mantis 1446 by Marshal on 21-Oct-2011 */
	/**
	 * This method is used to re-process the invalid messages(say, Filtered and
	 * Bad messages).<br>
	 * After the re-processing, the status of the message is changed as
	 * 'Processed'.<br>
	 * 
	 * @param messageIds
	 *            [If more than one messages they'll be stored in csv format]
	 * @param opTimer
	 * @throws SwtException
	 */
	public void reprocess(String[] messageIds, OpTimer opTimer, boolean fromPCM)
			throws SwtException {
		// Holds the Message Id(s) as a comma separated value
		String csvMessageIds = null;
		// A BasicDataSource is a DataSource that can create itself from
		// configuration information expressed in XML.
		Session session = null;
		// Declares the Connection object
		Connection connection = null;
		// Declares the CallableStatement
		CallableStatement procedure = null;
		try {
			log.debug(this.getClass().getName() + " -[reprocess] Begins");
			// Calls the method to convert the Array of Message Id(s) into CSV
			// and assigns to csvMessageIds
			csvMessageIds = setMsgIdsInCSV(messageIds);
			// Starts the timer
			opTimer.start(SwtConstants.QUERY_PROCESS);
			// Gets the BasicDataSource
			if(fromPCM) {
				session = SwtUtil.pcSessionFactory.openSession();

			} else {
				session = SwtUtil.sessionFactory.openSession();
			}
			// Establishes the Connection
			connection =  SwtUtil.connection(session);
			// Calls the Stored Procedure to reprocess
			if(!fromPCM) {
				procedure = connection.prepareCall("{call sp_set_msgs_to_reprocess(?)}");
			}
			else {
				procedure = connection.prepareCall("{call PKG_PC_INTERFACE_MONITOR.sp_set_msgs_to_reprocess(?)}");
			}
			// Sets the message Id as IN parameter
			procedure.setString(1, csvMessageIds);
			// Executes the Stored Procedure
			procedure.executeUpdate();
			// Commits the changes in the database
			connection.commit();
			// Stops the timer
			opTimer.stop(SwtConstants.QUERY_PROCESS);
			log.debug(this.getClass().getName() + " -[reprocess] Ends");
		} catch (Exception genericExp) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [reprocess] method : - "
					+ genericExp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(genericExp,
					"reprocess", InputExceptionsMessagesDAOHibernate.class);
		} finally {
			// Cleaning up of unreferenced objects goes here
			csvMessageIds = null;
			opTimer = null;
			
			//edited by KaisBS for mantis 1745 : introducing the implementation of the JDBCCloser class
			Object[] exceptions = JDBCCloser.close(null, procedure, connection, session);
			
			if (exceptions[0]!=null)
			   	throw SwtErrorHandler.getInstance().handleException((SQLException)exceptions[0], "reprocess", InputExceptionsMessagesDAOHibernate.class);
			
		}
	}
	
	/**
	 * This method is used to suppress or reject messages(say, Filtered and
	 * Bad messages).<br>
	 * After the suppressing or rejecting, the status of the message is changed as
	 * 'Suppressed' or 'Rejected'.<br>
	 * 
	 * @param messageIds
	 *            [If more than one messages they'll be stored in csv format]
	 * @param opTimer
	 * @throws SwtException
	 */
	public void suppressOrReject(String[] messageIds,String pProcessOption, OpTimer opTimer, boolean fromPCM)
			throws SwtException {
		// Holds the Message Id(s) as a comma separated value
		String csvMessageIds = null;
		// A BasicDataSource is a DataSource that can create itself from
		// configuration information expressed in XML.
		Session session = null;
		// Declares the Connection object
		Connection connection = null;
		// Declares the CallableStatement
		CallableStatement procedure = null;
		try {
			log.debug(this.getClass().getName() + " -["+pProcessOption+"] Begins");
			// Calls the method to convert the Array of Message Id(s) into CSV
			// and assigns to csvMessageIds
			csvMessageIds = setMsgIdsInCSV(messageIds);
			// Starts the timer
			opTimer.start(SwtConstants.QUERY_PROCESS);
			// Gets the BasicDataSource
			if(fromPCM) {
				session = SwtUtil.pcSessionFactory.openSession();

			} else {
				session = SwtUtil.sessionFactory.openSession();
			}
			// Establishes the Connection
			connection =  SwtUtil.connection(session);
			// Calls the Stored Procedure to suppress
			if(!fromPCM) {
				procedure = connection.prepareCall("{call sp_set_msgs_to_reprocess(?,?)}");
			}
			else {
				procedure = connection.prepareCall("{call PKG_PC_INTERFACE_MONITOR.sp_set_msgs_to_reprocess(?,?)}");
			}
			// Sets the message Id as IN parameter
			procedure.setString(1, csvMessageIds);
			procedure.setString(2, pProcessOption);
			// Executes the Stored Procedure
			procedure.executeUpdate();
			// Commits the changes in the database
			connection.commit();
			// Stops the timer
			opTimer.stop(SwtConstants.QUERY_PROCESS);
			log.debug(this.getClass().getName() + " -["+pProcessOption+"] Ends");
		} catch (Exception genericExp) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [suppressOrReject] method : - "
					+ genericExp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(genericExp,
					"suppress", InputExceptionsMessagesDAOHibernate.class);
		} finally {
			// Cleaning up of unreferenced objects goes here
			csvMessageIds = null;
			opTimer = null;
			
			//edited by imiraoui for mantis 3117 : Input Exception: Option to move Rejected to Suppressed and vice versa
			Object[] exceptions = JDBCCloser.close(null, procedure, connection, session);
			
			if (exceptions[0]!=null)
			   	throw SwtErrorHandler.getInstance().handleException((SQLException)exceptions[0], pProcessOption, InputExceptionsMessagesDAOHibernate.class);
			
		}
	}

	/**
	 * Method to get each Message Id from Message Id array.<br>
	 * 
	 * @param msgIds
	 * @return Message Ids in CSV format
	 * @throws SwtException
	 */
	private String setMsgIdsInCSV(String[] msgIds) throws SwtException {
		// Holds the Message Id(s) in CSV format
		String csvMsgIds = null;
		try {
			// Instantiates csvMsgIds
			csvMsgIds = new String();
			// Iterates the given Message Id(s) and convert them into CSV
			for (int i = 0; i < msgIds.length; i++) {
				// Adds comma to every next message Id
				csvMsgIds += i > 0 ? "," : "";
				// Adds every next message Id from the Array object
				csvMsgIds += msgIds[i];
			}
		} catch (Exception genericExp) {
			log
					.error("Exception occured in "
							+ this.getClass().getName()
							+ "-[setMsgIdsInCSV] while converting the array of message Ids into csv format. Cause: "
							+ genericExp.getMessage());
			// Re-throwing SwtException
			throw SwtErrorHandler.getInstance()
					.handleException(genericExp, "setMsgIdsInCSV",
							InputExceptionsMessagesDAOHibernate.class);
		}
		return csvMsgIds;
	}
	/* End: Modified for Mantis 1446 by Marshal on 21-Oct-2011 */
	
	public String getTotalCountFromDashboard(String messageStatus, String fromDate, String dateFormat, String currencyCode) throws SwtException {
		
		String sQuery = null;
		List count = null;
		ArrayList<Object> parameters = null;
		
		try {
			log.debug(this.getClass().getName() + " - [getTotalCountFromDashboard] - " + "Entry");
			parameters = new ArrayList<Object>();
			// get the total record count
			sQuery = "select count(*) from MessageArchivePCM e where e.status= ?0 ";
			parameters.add(messageStatus);
			sQuery += "and e.valueDate = to_date(?1,?2) and e.currencyCode1 = ?3";
			parameters.add(fromDate);
			parameters.add(dateFormat);
			parameters.add(currencyCode);
			count = (List ) getHibernateTemplate().find(sQuery, parameters.toArray());

			log.debug(this.getClass().getName() + " - [getTotalCountFromDashboard] - " + "Exit");
		} catch (Exception e) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [getTotalCountFromDashboard] method : - "
					+ e.getMessage());
			throw new SwtException(
					"Exception Catched in [getTotalCountFromDashboard] method: "
							+ e.getMessage());
		}
		return count.get(0).toString();
	}
}
