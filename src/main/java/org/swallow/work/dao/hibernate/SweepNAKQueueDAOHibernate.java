/*
 * @(#)SweepNAKQueueDAOHibernate.java 1.0 02/01/2010
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.work.dao.hibernate;

import java.sql.CallableStatement;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.List;




import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.dao.DataAccessResourceFailureException;

import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.maintenance.service.SysParamsManager;
import org.swallow.util.CacheManager;
import org.swallow.util.JDBCCloser;
import org.swallow.util.SwtUtil;
import org.swallow.work.dao.SweepNAKQueueDAO;
import org.swallow.work.model.SweepNAKQueue;
import org.springframework.context.annotation.Lazy;
import org.springframework.orm.hibernate5.support.HibernateDaoSupport;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.stereotype.Repository;
import org.springframework.beans.factory.annotation.Autowired;
import org.hibernate.HibernateException;
import org.hibernate.SessionFactory;
import org.hibernate.Transaction;
import org.hibernate.Session;

/**
 * This class is used to display the ACK Overdue Message, NAK Message.
 * 
 */
@Repository ("sweepNAKQueueDAO")
@Transactional
public class SweepNAKQueueDAOHibernate extends HibernateDaoSupport implements
		SweepNAKQueueDAO {
	public SweepNAKQueueDAOHibernate(@Lazy SessionFactory sessionfactory) {
	    setSessionFactory(sessionfactory);
	}

	/**
	 * Comment for <code>log</code>
	 */
	private final Log log = LogFactory.getLog(SweepNAKQueueDAOHibernate.class);

	@SuppressWarnings("unchecked")
	public String getNAKQueueDetails(String entityId, String currCode,
			Date valueDate) throws SwtException {
		log.debug("Entering getNAKQueueDetails method");

		String hostId = CacheManager.getInstance().getHostId();
		String count = null;
		List queueCount = getHibernateTemplate()
				.find(
						"select count(*) from SweepMsgFormat m where m.id.hostId=?0 and"
								+ " (select s.valueDate from Sweep s where s.id.hostId=?1 and s.id.sweepId=m.id.sweepId and s.currencyCode=?2 )>= ?3 "
								+ "and m.status='N'",
						new Object[] { hostId, hostId, currCode,
								valueDate });

		if ((queueCount != null) && (queueCount.size() > 0)) {
			count = queueCount.get(0).toString();
		}

		return count;
	}

	/**
	 * This method is used to fetch the NAK (Exceptions)sweep messages.
	 * 
	 * @param entityId
	 * @param currCode
	 * @param valueDate
	 * @return Collection of NAK sweep messages.
	 * @throws SwtException
	 */
	@SuppressWarnings("unchecked")
	public Collection getNAKMessages(String entityId, String currCode,
			Date valueDate) throws SwtException {
		log.debug("Entering getMessageDetails method");

		String hostId = CacheManager.getInstance().getHostId();
		Collection msgColl = getHibernateTemplate()
				.find(
						"select m.updateDate,m.msgFormatId,s.id.sweepId,s.valueDate,s.currencyCode,s.originalSweepAmt,s.submitSweepAmt,s.authorizeSweepAmt,m.messageId,m.ACKNAKmessageId from SweepMsgFormat m,Sweep s where m.id.hostId=?0 and (s.entityIdCr=?1 or s.entityIdDr=?2) and"
								+ " m.id.sweepId=s.id.sweepId and (select s.valueDate from Sweep s where s.id.hostId=?3 and s.id.sweepId=m.id.sweepId and s.currencyCode=?4 )>= ?5 "
								+ "and m.status='N'",
						new Object[] { hostId, entityId, entityId, hostId, currCode,
								valueDate });

		return msgColl;
	}

	/**
	 * This method is used to fetch the Overdue ACK sweep messages.
	 * 
	 * @param entityId
	 * @param currCode
	 * @param valueDate
	 * @return Collection of Overdue ACK sweep messages
	 * @throws SwtException
	 */
	@SuppressWarnings("unchecked")
	public Collection getOverdueACKMessages(String entityId, String currCode,
			Date valueDate) throws SwtException {
		/* Local variable declaration. */
		/* Variable Declaration for sysParamsManager. */
		SysParamsManager sysParamsManager = null;
		/* Variable Declaration for hostId. */
		String hostId = null;
		/* Variable Declaration for overdueQuery. */
		StringBuffer overdueQuery = null;
		/* Variable Declaration for msgColl. */
		Collection msgColl = null;
		try {
			log.debug(this.getClass().getName()
					+ " - [getOverdueACKMessages] - " + "Entry");
			sysParamsManager = (SysParamsManager) SwtUtil
					.getBean("sysParamsManager");
			// get Host id from Database.
			hostId = CacheManager.getInstance().getHostId();
			// create hql query.
			overdueQuery = new StringBuffer();
			overdueQuery
					.append("select m.updateDate,m.msgFormatId,s.id.sweepId,s.valueDate,s.currencyCode,s.originalSweepAmt,s.submitSweepAmt,s.authorizeSweepAmt,m.messageId,m.ACKNAKmessageId");
			overdueQuery
					.append(" from SweepMsgFormat m,Sweep s, MessageFormats f");
			overdueQuery.append(" where m.id.hostId=?0 "
					+ "and (s.entityIdCr=?1 or s.entityIdDr=?2) "
					);
			overdueQuery.append(" and m.id.hostId=s.id.hostId");
//			overdueQuery.append(" and m.id.entityId=s.id.entityId");
			overdueQuery.append(" and m.id.sweepId=s.id.sweepId");
			/*
			 * Start : Modified by venkat for Mantis 1434: "Sweep Exception
			 * Queue - overdue figure".
			 */
			overdueQuery.append(" and f.id.hostId=m.id.hostId");
//			overdueQuery.append(" and f.id.entityId=m.id.entityId");
			/*
			 * End : Modified by venkat for Mantis 1434: "Sweep Exception Queue -
			 * overdue figure".
			 */
			overdueQuery.append(" and f.id.formatId=m.msgFormatId");
			overdueQuery.append(" and m.status='S'");
			overdueQuery.append(" and s.valueDate >= ?3");
			overdueQuery.append(" and s.currencyCode=?4");
			/*
			 * Start : Modified by venkat for Mantis 1434: "Sweep Exception
			 * Queue - overdue figure".
			 */
			overdueQuery.append(" and m.ACKNAKmessageId is null ");
			overdueQuery
					.append(" and  f.overdueTime <> '00:00:00' and  f.overdueTime is not null");
			overdueQuery
					.append(" and m.updateDate + to_number(to_char(to_date(f.overdueTime,'hh24:mi:ss'), 'SSSSS')/86400) < ?5 ");

			msgColl = getHibernateTemplate().find(
					overdueQuery.toString(),
					new Object[] { hostId, entityId, entityId, valueDate, currCode,
							sysParamsManager.getDBSytemDate() });
			/*
			 * End : Modified by venkat for Mantis 1434: "Sweep Exception Queue -
			 * overdue figure".
			 */
		} catch (Exception exception) {
			log.error(this.getClass().getName()
					+ "- [getOverdueACKMessages] - Exception "
					+ exception.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exception,
					"getOverdueACKMessages", SweepNAKQueueDAOHibernate.class);
		} finally {
			/* null the objects created already. */
			sysParamsManager = null;
			hostId = null;
			overdueQuery = null;
			log.debug(this.getClass().getName()
					+ " - [getOverdueACKMessages] - " + "Exit");
		}
		return msgColl;
	}

	/**
	 * This method used to execute the DB procedure for fetching the sweep NAK
	 * details.
	 * 
	 * @param hostId
	 * @param entityId
	 * @param currencyGrpId
	 * @param roleId
	 * @param valueDate
	 * @return collection of
	 * @throws SwtException
	 *             sweepNAKQueue objects
	 */
	@SuppressWarnings("unchecked")
	public Collection getNAKQueueDetailsFromProc(String hostId,
			String entityId, String currencyGrpId, String roleId, Date valueDate)
			throws SwtException {
		log
				.debug("Entering in SweepNAKQueueDAOHibernate.getNAKQueueDetailsFromProc()");

		Session session = null;
		Connection conn = null;
		CallableStatement cstmt = null;
		ResultSet rs = null;
		String currencyCode = null;
		Collection NAKDetails = new ArrayList();
		SweepNAKQueue sweepNAKQueue = new SweepNAKQueue();

		try {
			session = getHibernateTemplate().getSessionFactory().openSession();
			conn = SwtUtil.connection(session);
			cstmt = conn
					.prepareCall("{call sweeping_process.sweep_exception_detail(?,?,?,?,?)}");

			cstmt.setString(1, hostId);
			cstmt.setString(2, entityId);
			cstmt.setString(3, roleId);
			cstmt.setString(4, currencyGrpId);
			cstmt.registerOutParameter(5, oracle.jdbc.OracleTypes.CURSOR);
			cstmt.execute();
			rs = (ResultSet) cstmt.getObject(5);

			// Populating bussiness object
			while (rs.next()) {
				sweepNAKQueue = new SweepNAKQueue();
				currencyCode = rs.getString(1);
				sweepNAKQueue.setCurrencyCode(currencyCode);
				sweepNAKQueue.setCurrencyName(rs.getString(2));

				if (rs.getLong(3) > 0) {
					sweepNAKQueue.setNAKs(String.valueOf(rs.getLong(3)));
					sweepNAKQueue.setZeroCount(false);
				} else {
					sweepNAKQueue.setNAKs("");
					sweepNAKQueue.setZeroCount(true);
				}

				if (rs.getLong(4) > 0) {
					sweepNAKQueue.setOverdueACKs(String.valueOf(rs.getLong(4)));
					sweepNAKQueue.setZeroCountOverdueACK(false);
				} else {
					sweepNAKQueue.setOverdueACKs("");
					sweepNAKQueue.setZeroCountOverdueACK(true);
				}

				NAKDetails.add(sweepNAKQueue);
			}
		} catch (DataAccessResourceFailureException e) {
			log.error("Problem in accessing DB");
			throw (SwtErrorHandler.getInstance().handleException(e,
					"getNAKQueueDetailsFromProc",
					SweepNAKQueueDAOHibernate.class));
		} catch (IllegalStateException e) {
			log.error("Problem in DB State");
			throw (SwtErrorHandler.getInstance().handleException(e,
					"getNAKQueueDetailsFromProc",
					SweepNAKQueueDAOHibernate.class));
		} catch (HibernateException e) {
			log.error("Problem in accessing Hibernate properties");
			throw (SwtErrorHandler.getInstance().handleException(e,
					"getNAKQueueDetailsFromProc",
					SweepNAKQueueDAOHibernate.class));
		} catch (SQLException e) {
			log.error("Problem in executing Query");
			throw (SwtErrorHandler.getInstance().handleException(e,
					"getNAKQueueDetailsFromProc",
					SweepNAKQueueDAOHibernate.class));
		} finally {
			
			//edited by KaisBS for mantis 1745 : introducing the implementation of the JDBCCloser class
			SwtException thrownException = null;
			Object[] exceptions = JDBCCloser.close(rs, cstmt, conn, session);

			if (exceptions[0] != null)
			    thrownException = SwtErrorHandler.getInstance().handleException((SQLException) exceptions[0], "getNAKQueueDetailsFromProc",
						SweepNAKQueueDAOHibernate.class);

			if (thrownException == null && exceptions[1] !=null) 
			    thrownException = SwtErrorHandler.getInstance().handleException((HibernateException) exceptions[1], "getNAKQueueDetailsFromProc",
						SweepNAKQueueDAOHibernate.class);

			if (thrownException!=null)
			    throw thrownException;
			
		}

		return NAKDetails;
	}
}
