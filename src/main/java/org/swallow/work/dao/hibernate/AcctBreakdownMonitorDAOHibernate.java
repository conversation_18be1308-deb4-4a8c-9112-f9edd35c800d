/**
 * @(#)AcctBreakdownMonitorDAOHibernate.java 1.0 / Mar 7, 2012
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.work.dao.hibernate;

import java.sql.CallableStatement;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Collection;

import javax.persistence.TypedQuery;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.dao.DataAccessResourceFailureException;

import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.maintenance.model.AcctMaintenance;
import org.swallow.util.JDBCCloser;
import org.swallow.util.SwtConstants;
import org.swallow.util.SwtUtil;
import org.swallow.work.dao.AcctBreakdownMonitorDAO;
import org.swallow.work.model.AcctBreakdownModel;
import org.swallow.work.model.AcctBreakdownMonitor;

import org.springframework.context.annotation.Lazy;
import org.springframework.orm.hibernate5.support.HibernateDaoSupport;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.stereotype.Repository;
import org.springframework.beans.factory.annotation.Autowired;
import org.hibernate.HibernateException;
import org.hibernate.Query;
import org.hibernate.SessionFactory;
import org.hibernate.Transaction;
import org.hibernate.Session;

/**
 * AcctBreakdownMonitorDAOHibernate.java
 * 
 * <pre>
 * This DAO class performs Account Breakdown Monitor related database operation.
 * It extends HibernateDaoSupport, so that it will get the reference of
 * HibernateSession to access database. It performs following tasks,
 * 
 * - Get Account list based on selected entity, currency and account class
 * - Get account breakdown details for grid
 * - Update account details
 * </pre>
 * 
 * <AUTHOR> R / Mar 7, 2012
 * @version SmartPredict-1054
 */
@Repository ("acctBreakdownMonitorDAO")
@Transactional
public class AcctBreakdownMonitorDAOHibernate extends HibernateDaoSupport
		implements AcctBreakdownMonitorDAO {
	public AcctBreakdownMonitorDAOHibernate(@Lazy SessionFactory sessionfactory) {
	    setSessionFactory(sessionfactory);
	}

	/**
	 * logger object for logging
	 */
	private static final Log log = LogFactory
			.getLog(AcctBreakdownMonitorDAOHibernate.class);

	/**
	 * This method gets account details belongs to selected entity, currency and
	 * account class. The account status should be either open or blocked. They
	 * should not be closed accounts.
	 * 
	 * @param acctBreakdownMonitor
	 * @return Collection<AcctMaintenance>
	 * @throws SwtException
	 */
	@SuppressWarnings("unchecked")
	public Collection<AcctMaintenance> getAcctList(
			AcctBreakdownMonitor acctBreakdownMonitor) throws SwtException {
		// Hold queries to get account details
		StringBuffer sbAcctQuery = null;
		// Query parameter values
		Object[] queryValues = null;

		try {
			// log debug message
			log.debug(this.getClass().getName() + " - [getAcctList] - Enter");
			// Query to get account details belongs to given entity, currency
			// and account class. The account should be either open or blocked.
			sbAcctQuery = new StringBuffer().append(
					"FROM AcctMaintenance acct where ").append(
					"acct.id.hostId = ?0 and acct.id.entityId = ?1 ").append(
					"AND acct.id.accountId != '*' ").append(
					"AND acct.currcode = ?2 ").append(
					"AND acct.acctstatusflg IN ('").append(
					SwtConstants.ACCOUNT_STATUS_FLAG_OPEN).append("','")
					.append(SwtConstants.ACCOUNT_STATUS_FLAG_BLOCKED).append(
							"') ");
			// If account class is selected then add the account class criteria,
			// otherwise select all account class records
			if (acctBreakdownMonitor.getAcctClass().equals("All")) {
				queryValues = new Object[] { acctBreakdownMonitor.getHostId(),
						acctBreakdownMonitor.getEntityId(),
						acctBreakdownMonitor.getCurrencyCode() };
			} else {
				sbAcctQuery.append("AND acct.acctClass = ?3 ");
				queryValues = new Object[] { acctBreakdownMonitor.getHostId(),
						acctBreakdownMonitor.getEntityId(),
						acctBreakdownMonitor.getCurrencyCode(),
						acctBreakdownMonitor.getAcctClass() };
			}
			sbAcctQuery.append("ORDER BY acct.id.accountId");
			// Get account details and return the same
			return (Collection<AcctMaintenance>) getHibernateTemplate().find(sbAcctQuery.toString(),
					queryValues);
		} catch (Exception ex) {
			// log error message
			log.error(this.getClass().getName()
					+ " - [getAcctList] - Exception: " + ex.getMessage());
			// Re-throw as SwtException
			throw SwtErrorHandler.getInstance().handleException(ex,
					"getAcctList", this.getClass());
		} finally {
			// nullify objects
			sbAcctQuery = null;
			queryValues = null;
			// log debug message
			log.debug(this.getClass().getName() + " - [getAcctList] - Exit");
		}
	}

	/**
	 * <pre>
	 * This method is used to get account breakdown monitor details based on the
	 * selected criteria. This method invokes Stored Procedure
	 * &quot;PKG_ACCOUNT_MONITOR.SP_ACCOUNT_BD_MONITOR_JOB&quot; to get account breakdown
	 * details.
	 * 
	 * Stored Procedure returns three cursors as output.
	 * - First cursor for account breakdown details
	 *    This cursor contains, account id, account name, predicted balance, unexpected
	 *    balance, start of day balance, sum flag, loro to predicted balance flag,
	 *    loro balance indicator and etc.
	 *    
	 * - Second one for holiday flag for tabs
	 * 
	 * - Third one for balance total
	 * </pre>
	 * 
	 * @param acctBreakdownMonitor
	 * @throws SwtException
	 */
	public void getAllBalancesUsingStoredProc(
			AcctBreakdownMonitor acctBreakdownMonitor) throws SwtException {
		// To hold account breakdown monitor details
		Collection<AcctBreakdownModel> colAcctBreakdown = null;
		// Hibernate session to execute stored procedure
		Session session = null;
		// Declaration for Database connection
		Connection conn = null;
		// CallableStatement instance to execute stored procedure
		CallableStatement cstmt = null;
		// ResultSet to get account breakdown monitor details
		ResultSet rsAcctBreakdown = null;
		// ResultSet to get tab holiday flag values
		ResultSet rsTabFlag = null;
		// ResultSet to get sum value of balances
		ResultSet rsBalanceTotal = null;
		// To hold account breakdown details for an account
		AcctBreakdownModel acctBreakdown = null;
		// To hold tab holiday flag
		StringBuffer sbTabFlag = null;
		// String declared to hold the account class
		String acctClass = null;
		// double declared to hold the predictedBalance
		double predictedBalance;
		// double declared to hold the predictedBalanceTotal
		double predictedBalanceTotal;
		// double declared to hold the unexpectedBalance
		double unexpectedBalance;
		// double declared to hold the unexpectedBalanceTotal
		double unexpectedBalanceTotal;
		// double declared to hold the startOfDayBalance
		double startOfDayBalance;
		// double declared to hold the startOfDayBalanceTotal
		double startOfDayBalanceTotal;
		// int declared to hold the paramIndex
		int paramIndex;
		try {
			// log debug message
			log.debug(this.getClass().getName()
					+ " - [getAllBalancesUsingStoredProc] - Enter");

			// Get the hibernate session
			session = getHibernateTemplate().getSessionFactory().openSession();
			// Get the database connection object
			conn = SwtUtil.connection(session);
			// Make a callable statement for executing the procedure
			cstmt = conn
					.prepareCall("{call PKG_ACCOUNT_MONITOR.SP_ACCOUNT_BD_MONITOR_JOB(?,?,?,?,?,?,?,?,?,?,?,?,?)}");
			// Set the parameters for stored procedure
			paramIndex = 1;
			cstmt.setString(paramIndex++, acctBreakdownMonitor.getHostId());
			cstmt.setString(paramIndex++, acctBreakdownMonitor.getEntityId());
			cstmt.setString(paramIndex++, acctBreakdownMonitor
					.getCurrencyCode());
			cstmt
					.setString(paramIndex++, acctBreakdownMonitor
							.getBalanceType());
			cstmt.setString(paramIndex++, acctBreakdownMonitor.getAcctId());
			cstmt.setDate(paramIndex++, SwtUtil.truncateDateTime(acctBreakdownMonitor
					.getValueDate()));
			cstmt.setString(paramIndex++, acctBreakdownMonitor
					.getApplyCurrencyThreshold());
			cstmt.setString(paramIndex++, acctBreakdownMonitor
					.getHideZeroBalances());
			cstmt.setString(paramIndex++, acctBreakdownMonitor.getAcctClass());
			cstmt.setString(paramIndex++, acctBreakdownMonitor.getRoleId());

			// Register the output parameter for account breakdown details,
			// balance totals and tab holiday flag
			cstmt.registerOutParameter(paramIndex++,
					oracle.jdbc.OracleTypes.CURSOR);
			cstmt.registerOutParameter(paramIndex++,
					oracle.jdbc.OracleTypes.CURSOR);
			cstmt.registerOutParameter(paramIndex++,
					oracle.jdbc.OracleTypes.CURSOR);
			// Execute the callable statement
			cstmt.execute();

			paramIndex = 11;
			// Get the result set of account breakdown details
			rsAcctBreakdown = (ResultSet) cstmt.getObject(paramIndex++);
			// Get the result set of tab holiday flag
			rsTabFlag = (ResultSet) cstmt.getObject(paramIndex++);
			// Get the result set of balance totals
			rsBalanceTotal = (ResultSet) cstmt.getObject(paramIndex++);
			// Initialize collection to hold account breakdown monitor details
			colAcctBreakdown = new ArrayList<AcctBreakdownModel>();

			if (rsAcctBreakdown != null) {
				// Iterate through result set and get account breakdown details
				while (rsAcctBreakdown.next()) {
					// Set first index of result set
					paramIndex = 1;

					// Initialize bean to hold account breakdown details for an
					// account
					acctBreakdown = new AcctBreakdownModel();
					// Set properties of a bean from result set
					
					//SET SCENARIO HIGHLIGHTED
					acctBreakdown.setScenarioHighlighted(rsAcctBreakdown.getString("SCENARIO_HIGHLIGHTING"));
					// getting Currency Code from the data base
					acctBreakdown.setCurrencyCode(rsAcctBreakdown
							.getString(paramIndex++));
					// getting Account Id from the database
					acctBreakdown.setAcctId(rsAcctBreakdown
							.getString(paramIndex++));
					// getting Account Name from the database
					acctBreakdown.setAcctName(rsAcctBreakdown
							.getString(paramIndex++));
					// getting Predicted Balance from the database
					predictedBalance = rsAcctBreakdown.getDouble(paramIndex++);
					acctBreakdown.setPredBalance(SwtUtil.formatCurrency(
							acctBreakdown.getCurrencyCode(), predictedBalance));
					acctBreakdown
							.setPredBalanceSign((predictedBalance >= 0) ? SwtConstants.STR_TRUE
									: SwtConstants.STR_FALSE);
					// getting Start of day balance from database
					startOfDayBalance = rsAcctBreakdown.getDouble(paramIndex++);
					acctBreakdown
							.setStartingBalance(SwtUtil.formatCurrency(
									acctBreakdown.getCurrencyCode(),
									startOfDayBalance));
					acctBreakdown
							.setStartingBalanceSign((startOfDayBalance >= 0) ? "true"
									: "false");
					// getting Unexpected balance from database
					unexpectedBalance = rsAcctBreakdown.getDouble(paramIndex++);
					acctBreakdown
							.setUnexpectedBalance(SwtUtil.formatCurrency(
									acctBreakdown.getCurrencyCode(),
									unexpectedBalance));
					acctBreakdown
							.setUnexpectedBalanceSign((unexpectedBalance >= 0) ? "true"
									: "false");
					/*
					 * Start:Code Modified By Chinniah on 19-Jul-2012 for
					 * 1054_STL_63 in Mantis 1933:Color Flag not Proper for
					 * SUM='C'
					 */
					// set Summable flag
					if (rsAcctBreakdown.getString(paramIndex).length() == 2) {
						acctBreakdown.setSummable(rsAcctBreakdown.getString(
								paramIndex).substring(1));
					} else {
						acctBreakdown.setSummable(SwtConstants.YES);
					}
					/*
					 * End:Code Modified By Chinniah on 19-Jul-2012 for
					 * 1054_STL_63 in Mantis 1933:Color Flag not Proper for
					 * SUM='C'
					 */
					// Sum flag
					acctBreakdown.setSum(rsAcctBreakdown
							.getString(paramIndex++).substring(0, 1));
					// Set flag value (move predicted balance to loro balance)
					acctBreakdown.setLoroToPredictedFlag(rsAcctBreakdown
							.getString(paramIndex));
					// Set flag value (loro balance indicator)
					acctBreakdown.setIconIndicatorFlag(rsAcctBreakdown
							.getString(paramIndex++));
					// getting Account class from the database
					acctClass = rsAcctBreakdown.getString(paramIndex++);

					// Code MOdified By Chinniah on 28-May-2012 for Mantis
					// 1933:Account Monitor: Screen hangs with "Function
					// returned without
					// value" if closed accounts
					acctBreakdown.setAccountStatus(rsAcctBreakdown
							.getString(paramIndex++));
					acctBreakdown.setIsLoroOrCurrAcct((acctClass
							.equalsIgnoreCase("L") || acctClass
							.equalsIgnoreCase("C")) ? "Y" : "N");

					// Set properties from AcctBreakdownMonitor bean
					acctBreakdown.setEntityId(acctBreakdownMonitor
							.getEntityId());
					acctBreakdown.setValueDate(acctBreakdownMonitor
							.getValueDateAsString());
					acctBreakdown.setBalanceType(acctBreakdownMonitor
							.getBalanceType());
					
					
					acctBreakdown.setIncludeLoroInPredictedIndicator("N".equalsIgnoreCase(rsAcctBreakdown.getString("predict_loro_flag"))?"":rsAcctBreakdown.getString("predict_loro_flag"));
//					acctBreakdown.setIncludePredictedInLoroIndicator("N".equalsIgnoreCase(rsAcctBreakdown.getString("loro_curr_flag"))?"":rsAcctBreakdown.getString("loro_curr_flag"));
//					acctBreakdown.setIncludeLoroInPredictedColor("L".equalsIgnoreCase(rsAcctBreakdown.getString("predict_loro_color"))?"#DDDDDD":"D".equalsIgnoreCase(rsAcctBreakdown.getString("predict_loro_color"))?"#AAAAAA":"");
					acctBreakdown.setIncludeLoroInPredictedColor(rsAcctBreakdown.getString("predict_loro_color"));
//					acctBreakdown.setIncludePredictedInLoroColor("L".equalsIgnoreCase(rsAcctBreakdown.getString("loro_curr_color"))?"#DDDDDD":"D".equalsIgnoreCase(rsAcctBreakdown.getString("loro_curr_color"))?"#AAAAAA":"");
					// Add account monitor detail in a collection
					colAcctBreakdown.add(acctBreakdown);
				}
			}

			// Get total of all balances
			if ( rsBalanceTotal != null && rsBalanceTotal.next()) {
				// Set first index of result set
				paramIndex = 1;
				// Get total of predicted balance
				predictedBalanceTotal = rsBalanceTotal.getDouble(paramIndex++);
				// Get total of start of day balance
				startOfDayBalanceTotal = rsBalanceTotal.getDouble(paramIndex++);
				// Get total of unexpected balance
				unexpectedBalanceTotal = rsBalanceTotal.getDouble(paramIndex++);
			} else {
				// Set default value
				predictedBalanceTotal = 0;
				startOfDayBalanceTotal = 0;
				unexpectedBalanceTotal = 0;
			}

			// Initialize string buffer to hold tab holiday flag
			sbTabFlag = new StringBuffer();
			// Iterate through tab flag result set and get tab holiday flag
			// value
			if (rsTabFlag != null) {
				while (rsTabFlag.next()) {
					sbTabFlag.append(rsTabFlag.getString(1));
				}
			}
			// Set tab holiday flag value
			acctBreakdownMonitor.setTabFlag(sbTabFlag.toString());
			// Set account breakdown details
			acctBreakdownMonitor.setAcctBreakdownList(colAcctBreakdown);
			// Set total value
			// Set predicted balance total and sign flag
			acctBreakdownMonitor.setPredBalanceTotal(SwtUtil.formatCurrency(
					acctBreakdownMonitor.getCurrencyCode(),
					predictedBalanceTotal));
			acctBreakdownMonitor
					.setPredBalanceTotalSign((predictedBalanceTotal >= 0) ? SwtConstants.STR_TRUE
							: SwtConstants.STR_FALSE);
			// Set starting balance total and sign flag
			acctBreakdownMonitor.setStartingBalanceTotal(SwtUtil
					.formatCurrency(acctBreakdownMonitor.getCurrencyCode(),
							startOfDayBalanceTotal));
			acctBreakdownMonitor
					.setStartingBalanceTotalSign((startOfDayBalanceTotal >= 0) ? SwtConstants.STR_TRUE
							: SwtConstants.STR_FALSE);
			// Set unexpected balance total and sign flag
			acctBreakdownMonitor.setUnexpectedBalanceTotal(SwtUtil
					.formatCurrency(acctBreakdownMonitor.getCurrencyCode(),
							unexpectedBalanceTotal));
			acctBreakdownMonitor
					.setUnexpectedBalanceTotalSign((unexpectedBalanceTotal >= 0) ? SwtConstants.STR_TRUE
							: SwtConstants.STR_FALSE);
		} catch (DataAccessResourceFailureException ex) {
			// log error message
			log
					.error(this.getClass().getName()
							+ " - [getAllBalancesUsingStoredProc] - DataAccessResourceFailureException: "
							+ ex.getMessage());
			// Re-throw as SwtException
			throw SwtErrorHandler.getInstance().handleException(ex,
					"getAllBalancesUsingStoredProc", this.getClass());
		} catch (IllegalStateException ex) {
			// log error message
			log
					.error(this.getClass().getName()
							+ " - [getAllBalancesUsingStoredProc] - IllegalStateException: "
							+ ex.getMessage());
			// Re-throw as SwtException
			throw SwtErrorHandler.getInstance().handleException(ex,
					"getAllBalancesUsingStoredProc", this.getClass());
		} catch (HibernateException ex) {
			// log error message
			log
					.error(this.getClass().getName()
							+ " - [getAllBalancesUsingStoredProc] - HibernateException: "
							+ ex.getMessage());
			// Re-throw as SwtException
			throw SwtErrorHandler.getInstance().handleException(ex,
					"getAllBalancesUsingStoredProc", this.getClass());
		} catch (SQLException ex) {
			// log error message
			log.error(this.getClass().getName()
					+ " - [getAllBalancesUsingStoredProc] - SQLException: "
					+ ex.getMessage());
			// Re-throw as SwtException
			throw SwtErrorHandler.getInstance().handleException(ex,
					"getAllBalancesUsingStoredProc", this.getClass());
		} catch (SwtException ex) {
			// log error message
			log.error(this.getClass().getName()
					+ " - [getAllBalancesUsingStoredProc] - SwtException: "
					+ ex.getMessage());
			// Re-throw SwtException
			throw ex;
		} catch (Exception ex) {
			// log error message
			log.error(this.getClass().getName()
					+ " - [getAllBalancesUsingStoredProc] - Exception: "
					+ ex.getMessage());
			// Re-throw as SwtException
			throw SwtErrorHandler.getInstance().handleException(ex,
					"getAllBalancesUsingStoredProc", this.getClass());
		} finally {
			//edited by KaisBS for mantis 1745 : introducing the implementation of the JDBCCloser class
			JDBCCloser.close(rsAcctBreakdown, rsBalanceTotal, rsTabFlag);
			JDBCCloser.close(null, cstmt, conn, session);

			// nullify objects
			colAcctBreakdown = null;
			session = null;
			conn = null;
			cstmt = null;
			rsAcctBreakdown = null;
			rsBalanceTotal = null;
			rsTabFlag = null;
			acctBreakdown = null;
			sbTabFlag = null;
			acctClass = null;
			// log debug message
			log.debug(this.getClass().getName()
					+ " - [getAllBalancesUsingStoredProc] - Exit");
		}
	}

	/**
	 * This method is used to update account details, like sum flag or loro to
	 * predicted flag
	 * 
	 * @param hostId
	 * @param entityId
	 * @param acctId
	 * @param sumFlag
	 * @param loroToPredicted
	 * @throws SwtException
	 */
	public void updateAccount(String hostId, String entityId, String acctId,
	        String sumFlag, String loroToPredicted) throws SwtException {
		Session session  = null;

	        try {
	        	session =  getHibernateTemplate().getSessionFactory().openSession();
	        	// Start a new transaction
	        	Transaction transaction = session.beginTransaction();
	            // Log debug message
	            log.debug(this.getClass().getName() + " - [updateAccount] - Enter");

	            // Form criteria-based HQL query
	            String hql = "FROM AcctMaintenance WHERE id.hostId = :hostId AND id.entityId = :entityId AND id.accountId = :acctId";
	            TypedQuery<AcctMaintenance> query = session.createQuery(hql, AcctMaintenance.class);
	            query.setParameter("hostId", hostId);
	            query.setParameter("entityId", entityId);
	            query.setParameter("acctId", acctId);

	            // Get account details based on the given criteria
	            AcctMaintenance acctMaintenance =  query.getSingleResult();

	            // Update account details
	            if (acctMaintenance != null) {
	                if (!SwtUtil.isEmptyOrNull(sumFlag)) {
	                    // Update sum flag
	                    acctMaintenance.setAcctMonitorSum(sumFlag);
	                }
	                /*if (!SwtUtil.isEmptyOrNull(loroToPredicted)) {
	                    // Update loro to predicted flag
	                    acctMaintenance.setLoroToPredicted(loroToPredicted);
	                }*/

	                session.update(acctMaintenance);
	                transaction.commit();
	            }
	        } catch (Exception ex) {
	            // Log error message
	            log.error(this.getClass().getName() + " - [updateAccount] - Exception: " + ex.getMessage());


	            // Re-throw as SwtException
	            throw SwtErrorHandler.getInstance().handleException(ex, "updateAccount", this.getClass());
	        } finally {
	        	JDBCCloser.close(null, null, null, session);
	            // Log debug message
	            log.debug(this.getClass().getName() + " - [updateAccount] - Exit");
	        }
	}
}