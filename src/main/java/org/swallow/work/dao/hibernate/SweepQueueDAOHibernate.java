/*
 * @(#)SweepQueueDAOHibernate.java 
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */

package org.swallow.work.dao.hibernate;

import java.sql.CallableStatement;
import java.sql.Connection;
import java.sql.SQLException;
import java.sql.Types;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.Iterator;
import java.util.List;
import java.util.StringTokenizer;
import java.util.regex.Matcher;
import java.util.regex.Pattern;


import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.dao.DataAccessResourceFailureException;

import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.maintenance.model.AccountSpecificSweepFormat;
import org.swallow.maintenance.model.AcctMaintenance;
import org.swallow.maintenance.model.EntityUserAccess;
import org.swallow.maintenance.service.SysParamsManager;
import org.swallow.util.CacheManager;
import org.swallow.util.JDBCCloser;
import org.swallow.util.PropertiesFileLoader;
import org.swallow.util.SwtConstants;
import org.swallow.util.SwtPager;
import org.swallow.util.SwtUtil;
import org.swallow.util.SystemFormats;
import org.swallow.work.dao.SweepQueueDAO;
import org.swallow.work.model.Sweep;
import org.swallow.work.model.SweepQueueDetailVO;
import org.springframework.context.annotation.Lazy;
import org.springframework.orm.hibernate5.support.HibernateDaoSupport;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.stereotype.Repository;
import org.springframework.beans.factory.annotation.Autowired;
import org.hibernate.HibernateException;
import org.hibernate.Query;
import org.hibernate.SessionFactory;
import org.hibernate.Transaction;
import org.hibernate.Session;

/**
 * <AUTHOR>
 * 
 * TODO To change the template for this generated type comment go to Window -
 * Preferences - Java - Code Style - Code Templates
 */
@Repository ("sweepQueueDAO")
public class SweepQueueDAOHibernate extends HibernateDaoSupport implements
		SweepQueueDAO {
	public SweepQueueDAOHibernate(@Lazy SessionFactory sessionfactory) {
	    setSessionFactory(sessionfactory);
	}

	private static final Log log = LogFactory
			.getLog(SweepQueueDAOHibernate.class);

	/**
	 * @desc-This function is used to create query for the Submit/Authorize
	 *            screen for the both the panels
	 * @param sweep-
	 *            Identifies the object for which query has to be executed.
	 * @param roleId-Role
	 *            of the LogIn User
	 * @param currencyGrpAccess-It
	 *            gives the currency group access as Full/View
	 * @param sweepQueueDetailVO-It
	 *            gives the object for populating the SweepDetail List and
	 *            OtherDetail List
	 * @return -query
	 */
	public void getSweepQueueDetail(Sweep sweep, String roleId,
			int currencyGrpAccess, SweepQueueDetailVO sweepQueueDetailVO)
			throws SwtException {

		if ((sweep.getEntityId() != null)
				&& (sweep.getCurrencyCode() != null)) {
			Collection accessList = sweepQueueDetailVO.getEntityAccessList();

			String query;
			List list = new ArrayList();
			if (!(currencyGrpAccess == SwtConstants.CURRENCYGRP_READ_ACCESS)) { // //log.debug("inside==>!(currencyGrpAccess==SwtConstants.CURRENCYGRP_READ_ACCESS)

				query = "Select distinct s from Sweep s, SweepLimits sl ,Currency cg"
						+ " where  s.currencyCode=sl.id.currencyCode and cg.id.currencyCode=s.currencyCode and s.id.hostId=?0 and sl.id.roleId=?1  "
						+ "and cg.currencyGroupId='"
						+ sweep.getCurrencyCode()
						+ "' and cg.id.entityId='"
						+ sweep.getEntityId()
						+ "'"
						+ buildParamQuery(sweep, accessList, roleId)
						+ buildQueueQuery(sweep.getQueueName(), sweep
								.getRequestUser(), sweep.getRequestUser())
						+ " and s.valueDate >= ?5" + " order by s.id.sweepId";
				
				list = (List ) getHibernateTemplate().find(
						query,
						new Object[] { sweep.getId().getHostId(), roleId,
								sweep.getId().getHostId(),
								sweep.getEntityId(),
								sweep.getCurrencyCode(),
								getEntityDatewithoutTime(sweep.getEntityId()) });

				if (list != null) {
					Iterator itrSearchList = list.iterator();
					// Instance for sweep
					while (itrSearchList.hasNext()) {
						Sweep sweepTemp = (Sweep) itrSearchList.next();

						// Process Credit account formats (deep copy matching record)
						if (sweepTemp.getAccountCr().getAccountSweepFormats() != null
							&& !sweepTemp.getAccountCr().getAccountSweepFormats().isEmpty()) {
							for (AccountSpecificSweepFormat record : sweepTemp.getAccountCr().getAccountSweepFormats()) {
								if (record.getId().getSpecifiedAccountId().equals(
										sweepTemp.getAccountDr().getId().getAccountId())) {

									// Deep clone Credit account and update formats
									AcctMaintenance clonedCrAccount = sweepTemp.getAccountCr() != null
											? sweepTemp.getAccountCr().deepClone() : null;
									clonedCrAccount.setAcctNewCrInternal(record.getNewInternalCrFormat());
									clonedCrAccount.setAcctNewCrExternal(record.getNewExternalCrFormat());
									sweepTemp.setAccountCr(clonedCrAccount);
									break; // Assuming only one match needed
								}
							}
						}

						// Process Debit account formats (deep copy matching record)
						if (sweepTemp.getAccountDr().getAccountSweepFormats() != null
							&& !sweepTemp.getAccountDr().getAccountSweepFormats().isEmpty()) {
							for (AccountSpecificSweepFormat record : sweepTemp.getAccountDr().getAccountSweepFormats()) {
								if (record.getId().getSpecifiedAccountId().equals(
										sweepTemp.getAccountCr().getId().getAccountId())) {

									// Deep clone Debit account and update formats
									AcctMaintenance clonedDrAccount = sweepTemp.getAccountDr() != null
											? sweepTemp.getAccountDr().deepClone() : null;
									clonedDrAccount.setAcctNewDrInternal(record.getNewInternalDrFormat());
									clonedDrAccount.setAcctNewDrExternal(record.getNewExternalDrFormat());
									sweepTemp.setAccountDr(clonedDrAccount);
									break; // Assuming only one match needed
								}
							}
						}
					}
				}
				sweepQueueDetailVO.setSweepDetailList(list);

			} else {
				sweepQueueDetailVO.setSweepDetailList(new ArrayList());
			}

			String queryOther = "Select distinct s from Sweep s, SweepLimitsView sl ,Currency cg"
					+ " where s.currencyCode=sl.id.currencyCode and cg.id.currencyCode=s.currencyCode "
					+ " and s.id.hostId=?0 and sl.id.roleId=?1 and cg.currencyGroupId='"
					+ sweep.getCurrencyCode()
					+ "'and cg.id.entityId='"
					+ sweep.getEntityId()
					+ "'"
					+ buildOtherParamQuery(sweep, accessList, roleId)
					+ buildOtherQueueQuery(sweep.getQueueName(), sweep
							.getRequestUser(), sweep.getRequestUser(), roleId);


			// 🔍 Get next available parameter index
			int nextParamIndex = getNextParameterIndex(queryOther);

		// 🧩 Append the valueDate condition
			queryOther += " and s.valueDate >= ?" + nextParamIndex + " order by s.id.sweepId";
			// Build parameter list dynamically
						List<Object> paramList = new ArrayList<>();
						paramList.add(sweep.getId().getHostId());     // ?0
						paramList.add(sweep.getRequestRole());        // ?1

			// Only add the extra params if they are used in the query
						if (nextParamIndex > 2) {
							paramList.add(sweep.getCurrencyCode());     // ?2
							paramList.add(sweep.getId().getHostId());   // ?3
							paramList.add(sweep.getEntityId());         // ?4
							paramList.add(sweep.getCurrencyCode());     // ?5
						}

			// Finally add valueDate at the correct index
						paramList.add(getEntityDatewithoutTime(sweep.getEntityId())); // ?nextParamIndex

			// Now call Hibernate with the correct number of parameters
						ArrayList listOther = (ArrayList) getHibernateTemplate().find(
								queryOther,
								paramList.toArray()
						);



			if (listOther != null) {
				Iterator itrSearchList = listOther.iterator();
				// Instance for sweep
				while (itrSearchList.hasNext()) {
					Sweep sweepTemp = (Sweep) itrSearchList.next();

					// Process Credit account formats (deep copy matching record)
					if (sweepTemp.getAccountCr().getAccountSweepFormats() != null
						&& !sweepTemp.getAccountCr().getAccountSweepFormats().isEmpty()) {
						for (AccountSpecificSweepFormat record : sweepTemp.getAccountCr().getAccountSweepFormats()) {
							if (record.getId().getSpecifiedAccountId().equals(
									sweepTemp.getAccountDr().getId().getAccountId())) {

								// Deep clone Credit account and update formats
								AcctMaintenance clonedCrAccount = sweepTemp.getAccountCr() != null
										? sweepTemp.getAccountCr().deepClone() : null;
								clonedCrAccount.setAcctNewCrInternal(record.getNewInternalCrFormat());
								clonedCrAccount.setAcctNewCrExternal(record.getNewExternalCrFormat());
								sweepTemp.setAccountCr(clonedCrAccount);
								break; // Only one match needed
							}
						}
					}

					// Process Debit account formats (deep copy matching record)
					if (sweepTemp.getAccountDr().getAccountSweepFormats() != null
						&& !sweepTemp.getAccountDr().getAccountSweepFormats().isEmpty()) {
						for (AccountSpecificSweepFormat record : sweepTemp.getAccountDr().getAccountSweepFormats()) {
							if (record.getId().getSpecifiedAccountId().equals(
									sweepTemp.getAccountCr().getId().getAccountId())) {

								// Deep clone Debit account and update formats
								AcctMaintenance clonedDrAccount = sweepTemp.getAccountDr() != null
										? sweepTemp.getAccountDr().deepClone() : null;
								clonedDrAccount.setAcctNewDrInternal(record.getNewInternalDrFormat());
								clonedDrAccount.setAcctNewDrExternal(record.getNewExternalDrFormat());
								sweepTemp.setAccountDr(clonedDrAccount);
								break; // Only one match needed
							}
						}
					}
				}
			}



			sweepQueueDetailVO.setOtherDetailList(listOther);

		} else {
			sweepQueueDetailVO.setSweepDetailList(new ArrayList());
			sweepQueueDetailVO.setOtherDetailList(new ArrayList());
		}

	}

	private int getNextParameterIndex(String query) {
		Pattern pattern = Pattern.compile("\\?(\\d+)");
		Matcher matcher = pattern.matcher(query);
		int maxIndex = -1;
		while (matcher.find()) {
			int index = Integer.parseInt(matcher.group(1));
			if (index > maxIndex) {
				maxIndex = index;
			}
		}
		return maxIndex + 1; // Next available index
	}
	/**
	 * Get system date with entity time frame offset
	 * @param entityId
	 * @return
	 */
	public static Date getEntityDatewithoutTime(String entityId) {
		SysParamsManager sysParamsManager = null;
		Date sysDateWithEntityTimeFrame = null;
		try {
			
			// get manager bean
			sysParamsManager = (SysParamsManager) SwtUtil.getBean("sysParamsManager");
			sysDateWithEntityTimeFrame = sysParamsManager
					.getDBSytemDateWithEntityOffset(entityId);
			SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
			String strNowDate = sdf.format(sysDateWithEntityTimeFrame);
			return sdf.parse(strNowDate);
		} catch (Exception e) {
			return new Date();
		}
	}

	private String buildParamQuery(Sweep sweep, Collection accessList) {

		String query = "";
		String entStr = getFullAccessList(accessList);

		if (!(sweep.getEntityId().equalsIgnoreCase("All"))) {
			query = query + " and( (s.entityIdCr = '"
					+ sweep.getEntityId() + "'"
					+ " or s.entityIdDr = '" + sweep.getEntityId()
					+ "') and " + " s.entityIdCr in (" + entStr
					+ "'') and s.entityIdDr in (" + entStr + "'') )";
		}
		if (!(sweep.getAccountType().equalsIgnoreCase("All"))) {
			query = query
					+ " and (EXISTS (select acc.accttype from AcctMaintenance acc "
					+ " where acc.accttype = '" + sweep.getAccountType()
					+ "' and acc.id.hostId = s.id.hostId "
					+ " and (( acc.id.entityId = s.entityIdCr "
					+ " and acc.id.accountId = s.accountIdCr)"
					+ " or (acc.id.entityId = s.entityIdDr "
					+ " and acc.id.accountId = s.accountIdDr))"
					+ " and acc.currcode = s.currencyCode))";
		}

		return query;
	}

	private String buildCancelOtherParamQuery(Sweep sweep) {
		String query = "";

		if (!(sweep.getAccountType().equals("All"))) {

			query = query
					+ " and (EXISTS (select acc.accttype from AcctMaintenance acc "
					+ " where acc.accttype = '" + sweep.getAccountType()
					+ "' and acc.id.hostId = s.id.hostId "
					+ " and (( acc.id.entityId = s.entityIdCr "
					+ " and acc.id.accountId = s.accountIdCr)"
					+ " or (acc.id.entityId = s.entityIdDr "
					+ " and acc.id.accountId = s.accountIdDr))"
					+ " and acc.currcode = s.currencyCode))";
		}

		return query;
	}

	// overloaded buildParamQuery with roleId as the 3rd parameter
	private String buildParamQuery(Sweep sweep, Collection accessList,
			String roleId) {

		String query = "";
		String entStr = getFullAccessList(accessList);
		String hostId = "";

		try {
			hostId = CacheManager.getInstance().getHostId();
		} catch (SwtException e) {
		}

		if (!(sweep.getEntityId().equalsIgnoreCase("All"))) {
			query = query + " and( (s.entityIdCr = '"
					+ sweep.getEntityId() + "'"
					+ " or s.entityIdDr = '" + sweep.getEntityId()
					+ "') and " + " ( (s.entityIdCr in (" + entStr
					+ "'') and s.entityIdDr in (" + entStr + "'') )";
		} else {
			query = " and (s.entityIdCr in (" + entStr
					+ "'') and s.entityIdDr in (" + entStr + "'')) ";
		}

		if (!(sweep.getCurrencyCode().equalsIgnoreCase("All"))) {
			query = query + " and cg.currencyGroupId = '"
					+ sweep.getCurrencyCode() + "' ";

			// added as per the rabo bank customization
			query = query
					+ "and cg.id.currencyCode in (Select s2.id.currencyCode from SweepLimits s2 "
					+ "where s2.id.currencyCode=s.currencyCode "
					+ "and s2.sweepLimit>=s.originalSweepAmt "
					+ "and s2.id.roleId = '"
					+ roleId
					+ "')AND(0 =(Select cga.accessId from CurrencyGroupAccess cga  where cga.id.hostId=?2 and cga.id.entityId=?3 and cga.id.roleId='"
					+ roleId
					+ "' and cga.id.currencyGroupId=?4 )) AND (0 =(Select cga.accessId from EntityAccess cga  where cga.id.hostId='"
					+ hostId + "' and cga.id.entityId='"
					+ sweep.getEntityId() + "' and cga.id.roleId='"
					+ roleId + "')))) ";
		}

		if (!(sweep.getAccountType().equalsIgnoreCase("All"))) {
			query = query + " and (s.accountCr.accttype = '"
					+ sweep.getAccountType() + "' or s.accountDr.accttype = '"
					+ sweep.getAccountType() + "')";
		}

		return query;
	}

	// overloaded buildParamQuery for adding roleId ends
	private String buildQueueQuery(String queueName, String inputUser,
			String submitUser) {

		String query = "";

		if (SwtConstants.SWEEP_STATUS_NEW.equalsIgnoreCase(queueName)) {
			query = " and s.sweepStatus='" + SwtConstants.SWEEP_STATUS_NEW
					+ "' and s.sweepType='" + SwtConstants.SWEEP_TYPE_AUTO
					+ "' and s.inputUser != '" + inputUser + "'";
		} else if (SwtConstants.SWEEP_STATUS_SUBMIT.equalsIgnoreCase(queueName)) {
			query = " and (( (s.sweepStatus='"
					+ SwtConstants.SWEEP_STATUS_SUBMIT + "'"
					+ " or s.sweepStatus='" + SwtConstants.SWEEP_STATUS_STP
					+ "' )" + " and (s.sweepType='"
					+ SwtConstants.SWEEP_TYPE_AUTO + "'" + "  or s.sweepType='"
					+ SwtConstants.SWEEP_TYPE_AMENDED + "' ) and"
					+ "  s.updateUser<> '" + inputUser
					+ "' and s.submitSweepAmt <= sl.sweepLimit"
					+ "  and s.movementIdCr is NULL) or" + " (s.sweepStatus='"
					+ SwtConstants.SWEEP_STATUS_NEW + "' and "
					+ " s.sweepType='" + SwtConstants.SWEEP_TYPE_MANUAL
					+ "' and s.movementIdCr is NULL" + " and s.updateUser<> '"
					+ inputUser
					+ "' and s.originalSweepAmt <= sl.sweepLimit) ) ";
		}

		return query;
	}

	private String buildOtherParamQuery(Sweep sweep, Collection accessList) {

		String query = "";
		String entStr = getFullAccessList(accessList);

		if (!(sweep.getCurrencyCode().equalsIgnoreCase("All"))) {
			query = query + " and cg.currencyGroupId = '"
					+ sweep.getCurrencyCode() + "' ";
		}

		if (!(sweep.getAccountType().equalsIgnoreCase("All"))) {
			query = query + " and (s.accountCr.accttype = '"
					+ sweep.getAccountType() + "' or s.accountDr.accttype = '"
					+ sweep.getAccountType() + "')";
		}

		if (!(sweep.getEntityId().equalsIgnoreCase("All"))) {
			query = query + " and (s.entityIdCr = '"
					+ sweep.getEntityId() + "' or "
					+ " s.entityIdDr = '" + sweep.getEntityId()
					+ "') and " + " (s.entityIdCr not in (" + entStr
					+ "'') or s.entityIdDr not in (" + entStr + "'')  "; // keep
			// one
			// bracket
			// open
		} else {
			String entAllStr = getAllAccessList(accessList);
			query = " and ( ( s.entityIdCr not in (" + entStr
					+ "'') or s.entityIdDr not in (" + entStr + "'') )"
					+ " and   ( s.entityIdCr in (" + entAllStr
					+ "'') or s.entityIdDr in (" + entAllStr + "'') )"; // keep
			// one
			// bracket
			// open
		}

		return query;
	}

	// OVERLOADED FUNCTION FOR buildOtherParamQuery with roleId as the 3rd
	// parameter--starts
	private String buildOtherParamQuery(Sweep sweep, Collection accessList,
			String roleId) {

		String query = "";
		String entStr = getFullAccessList(accessList);

		if (!(sweep.getAccountType().equalsIgnoreCase("All"))) {
			query = query + " and (s.accountCr.accttype = '"
					+ sweep.getAccountType() + "' or s.accountDr.accttype = '"
					+ sweep.getAccountType() + "')";
		}

		if (!(sweep.getEntityId().equalsIgnoreCase("All"))) {
			query = query + " and (s.entityIdCr = '"
					+ sweep.getEntityId() + "' or "
					+ " s.entityIdDr = '" + sweep.getEntityId()
					+ "') and " + " ((s.entityIdCr not in (" + entStr
					+ "'') or s.entityIdDr not in (" + entStr + "''))  "; // keep
			// one
			// bracket
			// open
		} else {
			String entAllStr = getAllAccessList(accessList);
			query = " and ( ( s.entityIdCr not in (" + entStr
					+ "'') or s.entityIdDr not in (" + entStr + "'') )"
					+ " and   ( s.entityIdCr in (" + entAllStr
					+ "'') or s.entityIdDr in (" + entAllStr + "'') )"; // keep
			// one
			// bracket
			// open
		}

		return query;
	}


	/**
	 * @desc-This function is used to create query for the View Panel for
	 *            Submit/Authorize screen
	 * @param queueName-
	 *            Identifies queue as Sumbit or Authorize
	 * @param inputUser-The
	 *            user who has inputd the data for the queue
	 * @param submitUser-Submit
	 *            User
	 * @param roleId-Role
	 *            of the LogIn User
	 * @return -query
	 */

	// overloaded function buildOtherQueueQuery with roleId as the 4th
	// parameter==starts
	private String buildOtherQueueQuery(String queueName, String inputUser,
			String submitUser, String roleId) {

		String query = "";

		if (SwtConstants.SWEEP_STATUS_NEW.equalsIgnoreCase(queueName)) {
			query = " or s.inputUser = '"
					+ inputUser
					+ "' "
					+ " or(cg.currencyGroupId= ?2  "
					+ "and cg.id.currencyCode in (Select s2.id.currencyCode from SweepLimitsView s2 "
					+ "where s2.id.currencyCode=s.currencyCode "
					+ "and s2.sweepLimit<s.originalSweepAmt "
					+ "and s2.id.roleId = '"
					+ roleId
					+ "'))"
					+ "or (1 =(Select cga.accessId from CurrencyGroupAccess cga  where cga.id.hostId=?3 and cga.id.entityId=?4 and cga.id.roleId='"
					+ roleId + "' and cga.id.currencyGroupId=?5)))"
					+ " and s.sweepStatus='" + SwtConstants.SWEEP_STATUS_NEW
					+ "' and s.sweepType='" + SwtConstants.SWEEP_TYPE_AUTO
					+ "'";
		} else if (SwtConstants.SWEEP_STATUS_SUBMIT.equalsIgnoreCase(queueName)) {
			query = " or (( (s.sweepStatus='"
					+ SwtConstants.SWEEP_STATUS_SUBMIT
					+ "'"
					+ " or s.sweepStatus='"
					+ SwtConstants.SWEEP_STATUS_STP
					+ "' )"
					+ " and (s.sweepType='"
					+ SwtConstants.SWEEP_TYPE_AUTO
					+ "'"
					+ " or s.sweepType='"
					+ SwtConstants.SWEEP_TYPE_AMENDED
					+ "' ) and s.movementIdCr is NULL) and"
					+
					// START:Code Added for Mantis 864(Authorize sweep
					// Enhancement) by Thirumurugan on 20-Jan-2009
					" ((s.submitUser= '"
					+ submitUser
					+ "'  and s.updateUser= '"
					+ inputUser
					+ "') "
					+ " or s.updateUser= '"
					+ inputUser
					+
					// END:Code Added for Mantis 864(Authorize sweep
					// Enhancement) by Thirumurugan on 20-Jan-2009
					"' or s.submitSweepAmt > sl.sweepLimit)) or"
					+ " ((s.sweepStatus='"
					+ SwtConstants.SWEEP_STATUS_NEW
					+ "' and "
					+ " s.sweepType='"
					+ SwtConstants.SWEEP_TYPE_MANUAL
					+ "' and s.movementIdCr is NULL) and "
					+
					// START:Code Added for Mantis 864(Authorize sweep
					// Enhancement) by Thirumurugan on 24-Dec-2008
					" ((s.inputUser= '"
					+ inputUser
					+ "'  and s.updateUser= '"
					+ inputUser
					+ "') "
					+ " or s.updateUser= '"
					+ inputUser
					+ "'  "
					+ " or(cg.currencyGroupId= ?2  "
					+ "and cg.id.currencyCode in (Select s2.id.currencyCode from SweepLimitsView s2 "
					+ "where s2.id.currencyCode=s.currencyCode "
					+ "and s2.sweepLimit<s.originalSweepAmt "
					+ "and s2.id.roleId = '"
					+ roleId
					+ "'))or (1 =(Select cga.accessId from CurrencyGroupAccess cga  where cga.id.hostId=?3 and cga.id.entityId=?4 and cga.id.roleId='"
					+ roleId + "' and cga.id.currencyGroupId=?5)))))";
		}else {
			query = " ) ";
		}

		return query;
	}

	/**
	 * This funcition returns the query for showing the data for the Cancel
	 * Queue.Here the join with Currency is added for bringing in the the
	 * Currency Group Requirement,
	 * 
	 * @param sweep
	 * @param sweepQueueDetailVO
	 * @param currentPage
	 * @param maxPage
	 * @param filterSortStatus
	 * @param systemFormats
	 * @param roleId
	 * 
	 * @return maxcount
	 * 
	 */
	public int getCancelQueueDetail(Sweep sweep,
			SweepQueueDetailVO sweepQueueDetailVO, int currentPage,
			int maxPage, String filterSortStatus, SystemFormats systemFormats,
			String roleId) throws SwtException {
		String hostId = "";
		Query cancelSweep = null;
		Query countSweep = null;
		SessionFactory sf;
		Session session = null;
		int maxCount = 0;
		String countQuery = null;
		String sweepQuery = null;
		String query = null;
		List list = null;
		Collection accessList = null;
		try {
			hostId = CacheManager.getInstance().getHostId();
			if ((sweep.getEntityId() != null)
					&& (sweep.getCurrencyCode() != null)) {
				accessList = sweepQueueDetailVO.getEntityAccessList();
				countQuery = "Select count(*) from Sweep s, SweepLimits sl,Currency cg";
				sweepQuery = "Select distinct  s from Sweep s, SweepLimits sl,Currency cg";

				// Code modified for Mantis 1394 by chinna on 6-JUN-2011:Full
				// auto-STP sweeps not shown in Sweep Cancel screen
				// This query is for to retrive sweep account based on the
				// Sattus of Sweeping for cancel panel in the Sweep cancel Queue
				// Screen
				query = " where cg.id.hostId = s.id.hostId "
						+ " and( cg.id.entityId = s.entityIdCr or cg.id.entityId = s.entityIdDr) "
						+ " and cg.id.currencyCode=s.currencyCode "
						+ " and cg.id.hostId = ?0 "
						+ " and s.currencyCode=sl.id.currencyCode "
						+ " and cg.currencyGroupId in ("
						+ sweep.getCurrencyCode()
						+ ") "
						+ " and sl.id.roleId=?1 "
						+ " and ((s.sweepStatus=?2"
						+ " and s.originalSweepAmt <= sl.sweepLimit "
						+ " and s.inputUser <> ?3) "

						+ "or (s.sweepStatus=?4"
						+ " and s.originalSweepAmt <= sl.sweepLimit "
						+ " and s.inputUser <> ?5) "

						+ " or (s.sweepStatus=?6"
						+ " and s.submitSweepAmt <= sl.sweepLimit "
						+ " and s.submitUser <> ?7) "

						+ " or (s.sweepStatus=?8"
						+ " and s.authorizeSweepAmt <= sl.sweepLimit "
						+ " and s.authorizedUser <> ?9)) "
						+ " and (0 =(Select cga.accessId from EntityAccess cga "
						+ "where cga.id.hostId="
						+ "?10"
						+ " and cga.id.entityId="
						+ "?11"
						+ " and cga.id.roleId="
						+ "?12" + ")) " + buildParamQuery(sweep, accessList);

				countQuery += query;
				query = formQuery(new StringBuffer(query), filterSortStatus,
						systemFormats, false).toString();
				countQuery = formQuery(new StringBuffer(countQuery),
						filterSortStatus, systemFormats, true).toString();
				sweepQuery += query;

				sf = (SessionFactory) SwtUtil.getBean("sessionFactory");
				/* opening the session factory */

				session = sf.openSession();

				countSweep = session.createQuery(countQuery);
				countSweep.setString(0, sweep.getId().getHostId());
				countSweep.setString(1, sweep.getRequestRole());
				countSweep.setString(2, SwtConstants.SWEEP_STATUS_NEW);
				countSweep.setString(3, sweep.getRequestUser());
				countSweep.setString(4, SwtConstants.SWEEP_STATUS_STP);
				countSweep.setString(5, sweep.getRequestUser());
				countSweep.setString(6, SwtConstants.SWEEP_STATUS_SUBMIT);
				countSweep.setString(7, sweep.getRequestUser());
				countSweep.setString(8, SwtConstants.SWEEP_STATUS_AUTHORIZE);
				countSweep.setString(9, sweep.getRequestUser());
				countSweep.setString(10, hostId);
				countSweep.setString(11, sweep.getEntityId());
				countSweep.setString(12, roleId);

				// Code modified for Mantis 1394 by chinna on 6-JUN-2011: Full
				// auto-STP sweeps not shown in Sweep Cancel screen
				// setting the values for query
				

				list = countSweep.list();
				maxCount = (list != null && list.size() > 0 && list.get(0) != null) ? Integer
						.parseInt(list.get(0).toString())
						: 0;
				cancelSweep = session.createQuery(sweepQuery);
				
				cancelSweep.setString(0, sweep.getId().getHostId());
				cancelSweep.setString(1, sweep.getRequestRole());
				cancelSweep.setString(2, SwtConstants.SWEEP_STATUS_NEW);
				cancelSweep.setString(3, sweep.getRequestUser());
				cancelSweep.setString(4, SwtConstants.SWEEP_STATUS_STP);
				cancelSweep.setString(5, sweep.getRequestUser());
				cancelSweep.setString(6, SwtConstants.SWEEP_STATUS_SUBMIT);
				cancelSweep.setString(7, sweep.getRequestUser());
				cancelSweep.setString(8, SwtConstants.SWEEP_STATUS_AUTHORIZE);
				cancelSweep.setString(9, sweep.getRequestUser());
				cancelSweep.setString(10, hostId);
				cancelSweep.setString(11, sweep.getEntityId());
				cancelSweep.setString(12, roleId);

				// Code modified for Mantis 1394 by chinna on 6-JUN-2011: Full
				// auto-STP sweeps not shown in Sweep Cancel screen
				// setting the values for query


				SwtPager
						.next(
								cancelSweep,
								currentPage - 1,
								maxPage,
								SwtUtil.getPageSizeFromProperty(SwtConstants.SWEEP_CANCEL_QUEUE_PAGE_SIZE),
								list);


				if (list != null) {
					Iterator itrSearchList = list.iterator();
					// Instance for sweep
					while (itrSearchList.hasNext()) {
						Sweep sweepTemp = (Sweep) itrSearchList.next();

						// Process Credit account formats (deep copy matching record)
						if (sweepTemp.getAccountCr().getAccountSweepFormats() != null
							&& !sweepTemp.getAccountCr().getAccountSweepFormats().isEmpty()) {
							for (AccountSpecificSweepFormat record : sweepTemp.getAccountCr().getAccountSweepFormats()) {
								if (record.getId().getSpecifiedAccountId().equals(
										sweepTemp.getAccountDr().getId().getAccountId())) {

									// Deep clone Credit account and update formats
									AcctMaintenance clonedCrAccount = sweepTemp.getAccountCr() != null
											? sweepTemp.getAccountCr().deepClone() : null;
									clonedCrAccount.setAcctNewCrInternal(record.getNewInternalCrFormat());
									clonedCrAccount.setAcctNewCrExternal(record.getNewExternalCrFormat());
									sweepTemp.setAccountCr(clonedCrAccount);
									break; // Stop on first match
								}
							}
						}

						// Process Debit account formats (deep copy matching record)
						if (sweepTemp.getAccountDr().getAccountSweepFormats() != null
							&& !sweepTemp.getAccountDr().getAccountSweepFormats().isEmpty()) {
							for (AccountSpecificSweepFormat record : sweepTemp.getAccountDr().getAccountSweepFormats()) {
								if (record.getId().getSpecifiedAccountId().equals(
										sweepTemp.getAccountCr().getId().getAccountId())) {

									// Deep clone Debit account and update formats
									AcctMaintenance clonedDrAccount = sweepTemp.getAccountDr() != null
											? sweepTemp.getAccountDr().deepClone() : null;
									clonedDrAccount.setAcctNewDrInternal(record.getNewInternalDrFormat());
									clonedDrAccount.setAcctNewDrExternal(record.getNewExternalDrFormat());
									sweepTemp.setAccountDr(clonedDrAccount);
									break; // Stop on first match
								}
							}
						}
					}
				}


				sweepQueueDetailVO.setSweepDetailList(list);
				/*
				 * Start:code modified for Mantis 1394 by chinna on 6-JUN-2011:
				 * Full auto-STP sweeps not shown in Sweep Cancel screen
				 */
				// This query is for to retrieve sweep account based on the
				// Status of Sweeping for View panel in the Sweep cancel Queue
				// Screen
				query = "Select distinct  s from Sweep s, SweepLimitsView sl,Currency cg "
						+ " where cg.id.hostId = s.id.hostId "
//						+ " and cg.id.entityId = s.id.entityId "
						 +" and( cg.id.entityId = s.entityIdCr or cg.id.entityId = s.entityIdDr) "
						+ " and cg.id.currencyCode = s.currencyCode "
						+ " and cg.id.hostId = ?0 " + " and sl.id.roleId = ?1 "
						+ " and s.currencyCode = sl.id.currencyCode "
						+ " and s.sweepStatus != ?2"
						+ " and cg.currencyGroupId in ("
						+ sweep.getCancelViewCurrGrpId()
						+ ") and (s.entityIdCr = ?3"
						+ " or s.entityIdCr = ?4"
						+ " ) and ( "
						+ buildCancelEntityParamQuery(sweep, accessList)
						+ " or ("

						+ " (s.sweepStatus = ?5"
						+ " and ( s.originalSweepAmt > sl.sweepLimit "
						+ " or s.inputUser = ?6 ) )"

						+ " or (s.sweepStatus = ?7"
						+ " and ( s.originalSweepAmt > sl.sweepLimit "
						+ " or s.inputUser = ?8 ) )"

						+ " or (s.sweepStatus = ?9"
						+ " and ( s.submitSweepAmt > sl.sweepLimit "
						+ " or s.submitUser = ?10 ) )"

						+ " or (s.sweepStatus = ?11"
						+ " and ( s.authorizeSweepAmt > sl.sweepLimit "
						+ " or s.authorizedUser = ?12 ) )"
						+ " or ( ( 1 = (select cga.accessId "
						+ " from CurrencyGroupAccess cga "
						+ " where cga.id.hostId=?13"
						+ " and cga.id.entityId=?14"
						+ " and cga.id.roleId=?15"
						+ " and cga.id.currencyGroupId = cg.currencyGroupId)) "
						+ " or ( 1 = (Select cga.accessId "
						+ " from EntityAccess cga  "
						+ " where cga.id.hostId=?16"
						+ " and cga.id.entityId=?17"
						+ " and cga.id.roleId=?18"
						+ "))))) "
						+ buildCancelOtherParamQuery(sweep)
						+ " order by s.id.sweepId";

				list = new ArrayList();
				// Excecuting query
				list = (List ) getHibernateTemplate().find(
						query,
						new Object[] { sweep.getId().getHostId(),
								sweep.getRequestRole(),
								SwtConstants.SWEEP_STATUS_CANCEL,
								sweep.getEntityId(),
								sweep.getEntityId(),
								SwtConstants.SWEEP_STATUS_NEW,
								sweep.getRequestUser(),
								SwtConstants.SWEEP_STATUS_STP,
								sweep.getRequestUser(),
								SwtConstants.SWEEP_STATUS_SUBMIT,
								sweep.getRequestUser(),
								SwtConstants.SWEEP_STATUS_AUTHORIZE,
								sweep.getRequestUser(), hostId,
								sweep.getEntityId(), roleId, hostId,
								sweep.getEntityId(), roleId });
				/*
				 * End:code modified for Mantis 1394 by chinna on 6-JUN-2011:
				 * Full auto-STP sweeps not shown in Sweep Cancel screen
				 */
				if (list != null) {
					Iterator itrSearchList = list.iterator();
					// Instance for sweep
					while (itrSearchList.hasNext()) {
						Sweep sweepTemp = (Sweep) itrSearchList.next();

						// Process Credit account formats (deep copy matching record)
						if (sweepTemp.getAccountCr().getAccountSweepFormats() != null
							&& !sweepTemp.getAccountCr().getAccountSweepFormats().isEmpty()) {
							for (AccountSpecificSweepFormat record : sweepTemp.getAccountCr().getAccountSweepFormats()) {
								if (record.getId().getSpecifiedAccountId().equals(
										sweepTemp.getAccountDr().getId().getAccountId())) {

									// Deep clone Credit account and update formats
									AcctMaintenance clonedCrAccount = sweepTemp.getAccountCr() != null
											? sweepTemp.getAccountCr().deepClone() : null;
									clonedCrAccount.setAcctNewCrInternal(record.getNewInternalCrFormat());
									clonedCrAccount.setAcctNewCrExternal(record.getNewExternalCrFormat());
									sweepTemp.setAccountCr(clonedCrAccount);
									break; // Stop on first match
								}
							}
						}

						// Process Debit account formats (deep copy matching record)
						if (sweepTemp.getAccountDr().getAccountSweepFormats() != null
							&& !sweepTemp.getAccountDr().getAccountSweepFormats().isEmpty()) {
							for (AccountSpecificSweepFormat record : sweepTemp.getAccountDr().getAccountSweepFormats()) {
								if (record.getId().getSpecifiedAccountId().equals(
										sweepTemp.getAccountCr().getId().getAccountId())) {

									// Deep clone Debit account and update formats
									AcctMaintenance clonedDrAccount = sweepTemp.getAccountDr() != null
											? sweepTemp.getAccountDr().deepClone() : null;
									clonedDrAccount.setAcctNewDrInternal(record.getNewInternalDrFormat());
									clonedDrAccount.setAcctNewDrExternal(record.getNewExternalDrFormat());
									sweepTemp.setAccountDr(clonedDrAccount);
									break; // Stop on first match
								}
							}
						}
					}
				}

				sweepQueueDetailVO.setOtherDetailList(list);

			} else {
				sweepQueueDetailVO.setSweepDetailList(new ArrayList());
				sweepQueueDetailVO.setOtherDetailList(new ArrayList());
			}

			return maxCount;
		} catch (HibernateException he) {
			log
					.error(this.getClass().getName()
							+ " - Exception Catched in [getCancelQueueDetail] method : - "
							+ he.getMessage());

			throw SwtErrorHandler.getInstance().handleException(he,
					"getCancelQueueDetail", SweepQueueDAOHibernate.class);
		} finally {
			
			//edited by KaisBS for mantis 1745 : introducing the implementation of the JDBCCloser class
			HibernateException hThrownException = JDBCCloser.close(session);

			if (hThrownException != null)
				throw new SwtException(hThrownException.getMessage());
		}

	}

	private String buildCancelEntityParamQuery(Sweep sweep,
			Collection accessList) {
		String query = "";
		String entStr = getFullAccessList(accessList);

		if (!(sweep.getEntityId().equalsIgnoreCase("All"))) {
			query = " ((s.entityIdCr = '" + sweep.getEntityId()
					+ "' or " + " s.entityIdDr = '"
					+ sweep.getEntityId() + "') and "
					+ " (s.entityIdCr not in (" + entStr
					+ "'') or s.entityIdDr not in (" + entStr + "'')))  ";
		}
		return query;
	}

	public Collection submit(Sweep sweep, String selectedList)
			throws SwtException {
		Collection list = null;
		String query2 = "Select s from Sweep s"
				+ " where s.id.hostId=?0 and s.id.sweepId in(" + selectedList
				+ ")";
		list = (List ) getHibernateTemplate().find(query2,
				new Object[] { sweep.getId().getHostId() });
		return list;
	}

	public Double getPredictBalance(String entityId, String hostId,
			String currencyId, String accountId, Date valDate)
			throws SwtException {
		log
				.debug("Entering in SweepQueueDaoHibernate.getPredictedBalance method ");

		Double predictedBalance = new Double(0);
		Session session = null;
		Connection conn = null;
		CallableStatement cstmt = null;
		java.sql.Date valueDate = SwtUtil.truncateDateTime(valDate);

		try {
			Date systemDate = SwtUtil.getSystemDatewithoutTime();
			java.sql.Date todayDate = SwtUtil.truncateDateTime(systemDate);
			session = getHibernateTemplate().getSessionFactory().openSession();
			conn = SwtUtil.connection(session);
			// Log Added for calculating the Time taken for the stored procedure
			// to execute on 25-03-2008
			log
					.debug(" $$$$$$$$$  PK_MONITORS.SP_GET_SWEEP_PREDICT_BALANCE Called at "
							+ SwtUtil.getTimeWithMilliseconds());
			cstmt = conn
					.prepareCall("{call PK_MONITORS.SP_GET_SWEEP_PREDICT_BALANCE(?,?,?,?,?,?,?)}");
			cstmt.setString(1, hostId);
			cstmt.setString(2, entityId);
			cstmt.setString(3, currencyId);
			cstmt.setString(4, accountId);
			cstmt.setDate(5, valueDate);
			cstmt.setDate(6, todayDate);
			cstmt.registerOutParameter(7, Types.DOUBLE);
			cstmt.execute();
			// Log Added for calculating the Time taken for the stored procedure
			// to execute on 25-03-2008
			log
					.debug(" $$$$$$$$$  PK_MONITORS.SP_GET_SWEEP_PREDICT_BALANCE Ended at "
							+ SwtUtil.getTimeWithMilliseconds());
			predictedBalance = new Double(cstmt.getDouble(7));
		} catch (DataAccessResourceFailureException e) {
			log.debug("Problem in accessing DB");
			e.printStackTrace();
			throw new SwtException();
		} catch (IllegalStateException e) {
			log.debug("Problem in DB State");
			e.printStackTrace();
			throw new SwtException();
		} catch (HibernateException e) {
			log.debug("Problem in accessing Hibernate properties");
			e.printStackTrace();
			throw new SwtException();
		} catch (SQLException e) {
			log.debug("Problem in executing Query");
			e.printStackTrace();
			throw new SwtException();
		} finally {
			
			//edited by KaisBS for mantis 1745 : introducing the implementation of the JDBCCloser class
			SwtException thrownException = null;
			Object[] exceptions = JDBCCloser.close(null, cstmt, conn, session);

			if (exceptions[0] != null)
			    thrownException = new SwtException();

			if (thrownException == null && exceptions[1] !=null) 
			    thrownException = new SwtException();

			if (thrownException!=null)
			    throw thrownException;
	
		}

		return predictedBalance;
	}

	/**
	 * This function returns status for Manual Sweeping sweeping process while
	 * getting the data from database.
	 * 
	 * 
	 * @param sweep
	 * @param systemFormats
	 * @return status
	 */
	public int processSweep(Sweep sweep, SystemFormats systemFormats)
			throws SwtException {

		// variable declared for session
		Session session = null;
		// variable declared for Connection
		Connection conn = null;
		// variable declared for CallableStatment
		CallableStatement cstmtSweep = null;

		try {
			log.debug(this.getClass().getName() + " - [processSweep] - Entry");
//			session = SessionFactoryUtils.getSession(getHibernateTemplate()
//					.getSessionFactory(), true);
			session = getHibernateTemplate().getSessionFactory().openSession();
			/*
			 * start: Code modified by sunil for Mantis 1438 - removing the
			 * Directory path are never used
			 */

			// Prepared statement for the cursor
			cstmtSweep = SwtUtil.connection(session).prepareCall(
					"{call MANUAL_SWEEPING(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)}");
			// Setting hostid value
			cstmtSweep.setString(1, sweep.getId().getHostId());
			// Setting EntityId Value
//			cstmtSweep.setString(2, sweep.getEntityId());
			// Setting AccountID debit Value
			cstmtSweep.setString(2, sweep.getAccountIdDr());
			// Setting AccountId Credit Value
			cstmtSweep.setString(3, sweep.getAccountIdCr());
			// Setting Amount value
			cstmtSweep.setDouble(4, getSweepAmount(sweep));
			// Setting request user
			cstmtSweep.setString(5, sweep.getRequestUser());
			// Setting SweepId value
			cstmtSweep.setLong(6, sweep.getId().getSweepId().longValue());
			// setting QueueName
			cstmtSweep.setString(7, sweep.getQueueName());
			// setting RequestRole
			cstmtSweep.setString(8, sweep.getRequestRole());
			// Setting AlignAccountId value
			cstmtSweep.setString(9, sweep.getAlignAccountId());
			// setting EntityIdCredit value
			cstmtSweep.setString(10, sweep.getEntityIdCr());
			// setting Entityid value
			cstmtSweep.setString(11, sweep.getEntityIdDr());
			// setting the Settlement method credit value
			cstmtSweep.setString(12,  sweep.getSettleMethodCR());
			// setting the Settlement method debit value
			cstmtSweep.setString(13, sweep.getSettleMethodDR());
			// setting the Book Code credit value
			cstmtSweep.setString(14, sweep.getBookCodeCR());
			// setting the Book Code debit value
			cstmtSweep.setString(15, sweep.getBookCodeDR());
			// setting the additional References value
			cstmtSweep.setString(16, sweep.getAdditionalReference());
			// Setting sweep from balance credit
			cstmtSweep.setString(17, sweep.getSweepFromBalanceTypeCr());
			//Setting sweep from balance debit
			cstmtSweep.setString(18, sweep.getSweepFromBalanceTypeDr());
			//Setting sweep target balance type id
			cstmtSweep.setString(19, sweep.getTargetBalanceTypId());
			// Retriving using above given values database
			// Getting output from the cursor
			cstmtSweep.registerOutParameter(20, Types.INTEGER);
			// Excecute cursor
			cstmtSweep.execute();
			return cstmtSweep.getInt(20);
			/*
			 * End : Code modified by sunil for Mantis 1438 - removing the
			 * Directory path are never used
			 */

		} catch (SQLException sqle) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [processSweep] method : - "
					+ sqle.getMessage());
			throw new SwtException(sqle.getMessage());
		} catch (Exception e) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [processSweep] method : - "
					+ e.getMessage());
			throw new SwtException(e.getMessage());
		} finally {
			
			//edited by KaisBS for mantis 1745 : introducing the implementation of the JDBCCloser class
			SwtException thrownException = null;
			Object[] exceptions = JDBCCloser.close(null, cstmtSweep, conn, session);

			if (exceptions[0] != null)
			    thrownException = new SwtException(((SQLException) exceptions[0]).getMessage());

			if (thrownException == null && exceptions[1] !=null) 
			    thrownException = new SwtException(((HibernateException) exceptions[1]).getMessage());

			if (thrownException!=null)
			    throw thrownException;
			
			log.debug(this.getClass().getName() + " - [processSweep] - Exit");
		}

	}

	private double getSweepAmount(Sweep sweep) throws SwtException {
		double amount = 0.0;

		if (SwtConstants.SWEEP_TYPE_AUTO.equalsIgnoreCase(sweep.getSweepType())
				&& (SwtConstants.SWEEP_STATUS_NEW.equalsIgnoreCase(sweep
						.getSweepStatus()) || SwtConstants.SWEEP_STATUS_STP
						.equalsIgnoreCase(sweep.getSweepStatus()))) {

			amount = (sweep.getAuthorizeSweepAmt() == null) ? (sweep
					.getOriginalSweepAmt().doubleValue()) : (sweep
					.getAuthorizeSweepAmt().doubleValue());

		} else if (SwtConstants.SWEEP_TYPE_AUTO.equalsIgnoreCase(sweep
				.getSweepType())
				&& SwtConstants.SWEEP_STATUS_SUBMIT.equalsIgnoreCase(sweep
						.getSweepStatus())) {

			if (SwtConstants.SWEEP_STATUS_SUBMIT.equalsIgnoreCase(sweep
					.getQueueName()))
				amount = (sweep.getAuthorizeSweepAmt() == null) ? (sweep
						.getSubmitSweepAmt().doubleValue()) : (sweep
						.getAuthorizeSweepAmt().doubleValue());

			else
				amount = sweep.getSubmitSweepAmt().doubleValue();
		} else if (SwtConstants.SWEEP_TYPE_MANUAL.equalsIgnoreCase(sweep
				.getSweepType())
				&& SwtConstants.SWEEP_STATUS_NEW.equalsIgnoreCase(sweep
						.getSweepStatus())) {

			amount = (sweep.getAuthorizeSweepAmt() == null) ? (sweep
					.getOriginalSweepAmt().doubleValue()) : (sweep
					.getAuthorizeSweepAmt().doubleValue());

		} else if (SwtConstants.SWEEP_STATUS_CANCEL.equalsIgnoreCase(sweep
				.getQueueName())) {
			if (SwtConstants.SWEEP_STATUS_NEW.equalsIgnoreCase(sweep
					.getSweepStatus())) {
				amount = sweep.getOriginalSweepAmt().doubleValue();
			} else if (SwtConstants.SWEEP_STATUS_SUBMIT.equalsIgnoreCase(sweep
					.getSweepStatus())) {
				amount = sweep.getSubmitSweepAmt().doubleValue();
			} else {
				amount = sweep.getAuthorizeSweepAmt().doubleValue();
			}
		}

		return amount;
	}

	private String getFullAccessList(Collection accessList) {
		Iterator itr = accessList.iterator();
		EntityUserAccess entityAccess = null;
		String entStr = "";

		while (itr.hasNext()) {
			entityAccess = (EntityUserAccess) itr.next();

			if (SwtConstants.ENTITY_FULL_ACCESS == entityAccess.getAccess()) {
				entStr = entStr + "'" + entityAccess.getEntityId() + "',";
			}
		}

		return entStr;
	}

	private String getAllAccessList(Collection accessList) {
		Iterator itr = accessList.iterator();
		EntityUserAccess entityAccess = null;
		String entStr = "";

		while (itr.hasNext()) {
			entityAccess = (EntityUserAccess) itr.next();
			entStr = entStr + "'" + entityAccess.getEntityId() + "',";
		}

		// log.debug("returning from getAllAccessList :" + entStr);
		return entStr;
	}

	/* Start : Code added for Mantis 1179 by ASB on 09-Jun-2010 */
	/**
	 * Method returns the query in a format for filter and sort
	 * 
	 * @param query
	 * @param filterSortStatus
	 * @param formats
	 * @return StringBuffer
	 */
	private StringBuffer formQuery(StringBuffer query, String filterSortStatus,
			SystemFormats systemFormats, boolean countQuery) {
		log.debug(this.getClass().getName() + " - [formQuery] - " + "Entry");
		/* Method's local and local variable declaration */

		String[] filterSort;
		String filterStatus;
		String sortStatus;
		String[] sortValues;
		String format;
		int i;
		String sortColumn = "";
		String sortDesc = "";
		String[] filterValues;
		int[] nonString;
		int j;
		int dateIndex;
		boolean logDate;
		String nextVal;
		StringTokenizer st;
		StringTokenizer st1;

		/*
		 * The query from filterSortStatus is separated and stored in filter
		 * sort
		 */
		filterSort = filterSortStatus.split("#");
		/* Filter status is collected from the filter sort array */
		filterStatus = filterSort[0].toString();
		/* sort status is collected from the filter sort array */
		sortStatus = filterSort[1].toString();

		/* Collect tokens of filters */
		st1 = new StringTokenizer(filterStatus, "|");
		filterValues = new String[21];
		i = 0;
		while (st1.hasMoreTokens()) {
			/* Values from tokens are assigned to the sortValues array */
			filterValues[i] = st1.nextToken();
			if (i == 0
					&& !(filterValues[i])
							.equalsIgnoreCase(SwtConstants.ALL_VALUE)) {
				if (filterValues[i]
						.equalsIgnoreCase(SwtConstants.FILTER_STATUS_EMPTY)) {
					query.append(" AND s.valueDate is NULL ");
				} else if (filterValues[i]
						.equalsIgnoreCase(SwtConstants.FILTER_STATUS_NOT_EMPTY)) {
					query.append(" AND s.valueDate is NOT NULL ");
				} else {
					query.append(" AND to_char(s.valueDate,'");
					query.append(systemFormats.getDateFormatValue());
					query.append("') = '");
					query.append(filterValues[i]);
					query.append("' ");
				}
			}
			if (i == 1
					&& !(filterValues[i])
							.equalsIgnoreCase(SwtConstants.ALL_VALUE)) {
				if (filterValues[i]
						.equalsIgnoreCase(SwtConstants.FILTER_STATUS_EMPTY)) {
					query.append(" AND s.currencyCode is NULL ");
				} else if (filterValues[i]
						.equalsIgnoreCase(SwtConstants.FILTER_STATUS_NOT_EMPTY)) {
					query.append(" AND s.currencyCode is NOT NULL ");
				} else {
					query.append(" AND s.currencyCode = '");
					query.append(filterValues[i]);
					query.append("' ");
				}
			}
			if (i == 4
					&& !(filterValues[i])
							.equalsIgnoreCase(SwtConstants.ALL_VALUE)) {
				if (filterValues[i]
						.equalsIgnoreCase(SwtConstants.FILTER_STATUS_EMPTY)) {
					query.append(" AND s.entityIdCr is NULL ");
				} else if (filterValues[i]
						.equalsIgnoreCase(SwtConstants.FILTER_STATUS_NOT_EMPTY)) {
					query.append(" AND s.entityIdCr is NOT NULL ");
				} else {
					query.append(" AND s.entityIdCr = '");
					query.append(filterValues[i]);
					query.append("' ");
				}
			}
			
			if (i == 5
					&& !(filterValues[i])
							.equalsIgnoreCase(SwtConstants.ALL_VALUE)) {
				if (filterValues[i]
						.equalsIgnoreCase(SwtConstants.FILTER_STATUS_EMPTY)) {
					query.append(" AND s.accountIdCr is NULL ");
				} else if (filterValues[i]
						.equalsIgnoreCase(SwtConstants.FILTER_STATUS_NOT_EMPTY)) {
					query.append(" AND s.accountIdCr is NOT NULL ");
				} else {
					query.append(" AND s.accountIdCr = '");
					query.append(filterValues[i]);
					query.append("' ");
				}
			}
			if (i == 7
					&& !(filterValues[i])
							.equalsIgnoreCase(SwtConstants.ALL_VALUE)) {
				if (filterValues[i]
						.equalsIgnoreCase(SwtConstants.FILTER_STATUS_EMPTY)) {
					query.append(" AND s.entityIdDr is NULL ");
				} else if (filterValues[i]
						.equalsIgnoreCase(SwtConstants.FILTER_STATUS_NOT_EMPTY)) {
					query.append(" AND s.entityIdDr is NOT NULL ");
				} else {
					query.append(" AND s.entityIdDr = '");
					query.append(filterValues[i]);
					query.append("' ");
				}
			}
			if (i == 8
					&& !(filterValues[i])
							.equalsIgnoreCase(SwtConstants.ALL_VALUE)) {
				if (filterValues[i]
						.equalsIgnoreCase(SwtConstants.FILTER_STATUS_EMPTY)) {
					query.append(" AND s.accountIdDr is NULL ");
				} else if (filterValues[i]
						.equalsIgnoreCase(SwtConstants.FILTER_STATUS_NOT_EMPTY)) {
					query.append(" AND s.accountIdDr is NOT NULL ");
				} else {
					query.append(" AND s.accountIdDr = '");
					query.append(filterValues[i]);
					query.append("' ");
				}
			}
			if (i == 10
					&& !(filterValues[i])
							.equalsIgnoreCase(SwtConstants.ALL_VALUE)) {
				if (filterValues[i]
						.equalsIgnoreCase(SwtConstants.FILTER_STATUS_EMPTY)) {
					query.append(" AND s.accountCr.acctNewCrInternal is NULL ");
				} else if (filterValues[i]
						.equalsIgnoreCase(SwtConstants.FILTER_STATUS_NOT_EMPTY)) {
					query
							.append(" AND s.accountCr.acctNewCrInternal is NOT NULL ");
				} else {
					query.append(" AND s.accountCr.acctNewCrInternal = '");
					query.append(filterValues[i]);
					query.append("' ");
				}
			}
			if (i == 12
					&& !(filterValues[i])
							.equalsIgnoreCase(SwtConstants.ALL_VALUE)) {
				if (filterValues[i]
						.equalsIgnoreCase(SwtConstants.FILTER_STATUS_EMPTY)) {
					query.append(" AND s.accountCr.acctNewCrExternal is NULL ");
				} else if (filterValues[i]
						.equalsIgnoreCase(SwtConstants.FILTER_STATUS_NOT_EMPTY)) {
					query
							.append(" AND s.accountCr.acctNewCrExternal is NOT NULL ");
				} else {
					query.append(" AND s.accountCr.acctNewCrExternal = '");
					query.append(filterValues[i]);
					query.append("' ");
				}
			}
			if (i == 13
					&& !(filterValues[i])
							.equalsIgnoreCase(SwtConstants.ALL_VALUE)) {
				if (filterValues[i]
						.equalsIgnoreCase(SwtConstants.FILTER_STATUS_EMPTY)) {
					query.append(" AND s.accountDr.acctNewDrInternal is NULL ");
				} else if (filterValues[i]
						.equalsIgnoreCase(SwtConstants.FILTER_STATUS_NOT_EMPTY)) {
					query
							.append(" AND s.accountDr.acctNewDrInternal is NOT NULL ");
				} else {
					query.append(" AND s.accountDr.acctNewDrInternal = '");
					query.append(filterValues[i]);
					query.append("'");
				}
			}
			if (i == 14
					&& !(filterValues[i])
							.equalsIgnoreCase(SwtConstants.ALL_VALUE)) {
				if (filterValues[i]
						.equalsIgnoreCase(SwtConstants.FILTER_STATUS_EMPTY)) {
					query.append(" AND s.accountDr.acctNewDrExternal is NULL ");
				} else if (filterValues[i]
						.equalsIgnoreCase(SwtConstants.FILTER_STATUS_NOT_EMPTY)) {
					query
							.append(" AND s.accountDr.acctNewDrExternal is NOT NULL ");
				} else {
					query.append(" AND s.accountDr.acctNewDrExternal = '");
					query.append(filterValues[i]);
					query.append("' ");
				}
			}
			if (i == 15
					&& !(filterValues[i])
							.equalsIgnoreCase(SwtConstants.ALL_VALUE)) {
				if (filterValues[i]
						.equalsIgnoreCase(SwtConstants.FILTER_STATUS_EMPTY)) {
					query.append(" AND s.sweepType is NULL ");
				} else if (filterValues[i]
						.equalsIgnoreCase(SwtConstants.FILTER_STATUS_NOT_EMPTY)) {
					query.append(" AND s.sweepType is NOT NULL ");
				} else {
					query.append(" AND s.sweepType = '");
					if (filterValues[i].equalsIgnoreCase("man")) {
						query.append(SwtConstants.SWEEP_TYPE_MANUAL);
					} else if (filterValues[i].equalsIgnoreCase("auto")) {
						query.append(SwtConstants.SWEEP_TYPE_AUTO);
					} else {
						query.append(filterValues[i]);
					}
					query.append("' ");
				}
			}
			i++;
		}

		/* Collect tokens of filters */
		st1 = new StringTokenizer(sortStatus, "|");
		/* Initializing sort values array */
		sortValues = new String[3];
		/* Loop to collect string tokens */
		i = 0;
		while (st1.hasMoreTokens()) {
			/* Values from tokens are assigned to the sortValues array */
			sortValues[i] = st1.nextToken();
			i++;
		}

		sortColumn = sortValues[0];
		sortDesc = sortValues[1];
		/* Condition to check sortStatus is equal to none */
		if (!countQuery) {
			if (!sortStatus.equals("none")) {

				query.append(" order by ");

				/* Condition to check the sort column to append in the query */
				if (sortColumn.equals("0")) {
					query.append(" s.valueDate ");
				}
				if (sortColumn.equals("1")) {
					query.append(" s.currencyCode ");
				}
				if (sortColumn.equals("4")) {
					query.append(" s.accountIdCr ");
				}
				if (sortColumn.equals("5")) {
					query.append(" s.entityIdCr ");
				}
				if (sortColumn.equals("7")) {
					query.append(" s.entityIdDr ");
				}
				if (sortColumn.equals("8")) {
					query.append(" s.accountIdDr ");
				}
				if (sortColumn.equals("11")) {
					query.append(" s.accountCr.acctNewCrInternal ");
				}
				if (sortColumn.equals("12")) {
					query.append(" s.accountCr.acctNewCrExternal ");
				}
				if (sortColumn.equals("13")) {
					query.append(" s.accountDr.acctNewDrInternal ");
				}
				if (sortColumn.equals("14")) {
					query.append(" s.accountDr.acctNewDrExternal ");
				}
				if (sortColumn.equals("15")) {
					query.append(" s.sweepType ");
				}
				/* Condition to check sort description is true */
				if (sortDesc.equalsIgnoreCase("true")) {
					query.append(" desc");

					query.append(" , s.valueDate, s.originalSweepAmt desc");
				} else {
					query.append(" , s.valueDate,s.originalSweepAmt ");
				}
			} else {

				query
						.append(" order by s.valueDate desc,s.originalSweepAmt desc");
			}
		}
		return query;
	}

}