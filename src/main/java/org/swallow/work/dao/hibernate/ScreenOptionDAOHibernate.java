/*
 * @(#)ScreenOptionDAOHibernate.java 1.0 06/08/10
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.work.dao.hibernate;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Iterator;

import org.springframework.dao.DataAccessException;
import org.springframework.stereotype.Repository;
import org.swallow.batchScheduler.ConnectionManager;
import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.maintenance.dao.hibernate.ILMGeneralMaintenanceDAOHibernate;
import org.swallow.util.JDBCCloser;
import org.swallow.util.PropertiesFileLoader;
import org.swallow.util.SwtConstants;
import org.swallow.work.dao.ScreenOptionDAO;
import org.swallow.work.model.ScreenOption;
import org.hibernate.Transaction;
import org.hibernate.Session;
import org.hibernate.SessionFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.orm.hibernate5.support.HibernateDaoSupport;

/**
 * ScreenOptionDAOHibernate.java
 * 
 * Class that implements the ScreenOptionDAO and acts as DAO layer for all
 * database operations related to screen option
 * 
 * <AUTHOR> Balaji A
 * @date Aug 06, 2010
 */
@Repository
public class ScreenOptionDAOHibernate extends HibernateDaoSupport implements
		ScreenOptionDAO {

	public ScreenOptionDAOHibernate(@Lazy SessionFactory sessionfactory){
	    setSessionFactory(sessionfactory);
	}
	
	
	/**
	 * Method to retrieve the screen options
	 * 
	 * @throws SwtException
	 *             Collection<ScreenOption>
	 */
	@SuppressWarnings("unchecked")
	public Collection<ScreenOption> getScreenOption(ScreenOption screenOption)
			throws SwtException {
		// Fetch the list of screen options
		Collection collScreenOption = null;
		// Holds the query to fetch the screen option details
		StringBuffer screenOptionQry = null;
		ArrayList<Object> parameters = null;
		try {
			screenOptionQry = new StringBuffer(
					"from ScreenOption so where so.id.hostId=?0 and so.id.userId=?1 and so.id.screenId=?2 ");
			parameters = new ArrayList<Object>();
			parameters.add(screenOption.getId().getHostId());
			parameters.add(screenOption.getId().getUserId());
			parameters.add(screenOption.getId().getScreenId());
			
			if (screenOption.getId().getPropertyName() != null) {
				screenOptionQry.append("and so.id.propertyName=?3");
				parameters.add(screenOption.getId().getPropertyName());
			}
			collScreenOption = getHibernateTemplate().find(
					screenOptionQry.toString(), parameters.toArray());
			// Calls the method to set the screen option's default property
			// values
			return setDefaultPropertyValues(collScreenOption);
		} catch (DataAccessException e) {
			logger.error("Exception caught in" + this.getClass().getName()
					+ "[getScreenOption] " + e.getMessage());
			// Re-throwing SwtException
			throw SwtErrorHandler.getInstance().handleException(e,
					"getScreenOption", ScreenOptionDAOHibernate.class);
		} finally {
			// Nullifying unreferenced objects
			screenOptionQry = null;
		}
	}

	/**
	 * Method to save the screen option
	 * 
	 * @param screenOption
	 * @param isUpdate
	 * @throws SwtException
	 */
	public void saveScreenOption(ScreenOption screenOption, boolean isUpdate)
			throws SwtException {
		
		Session session = null;
    	Transaction tx = null;
 
    	try{
    		logger.debug(this.getClass().getName()
					+ " - [saveScreenOption] - Entering ");
    		session = getHibernateTemplate().getSessionFactory().openSession();
    		tx = session.beginTransaction();
    		//tx.setTimeout(5);
    		// Update the screen option if isUpdate is true
			if (isUpdate) {
				session.update(screenOption);
			} else {
				// Saves the screen option otherwise
				session.save(screenOption);
			}
    		//session.saveOrUpdate(screenOption);
    		session.flush();
    		tx.commit();
			logger.debug(this.getClass().getName()
					+ " - [saveScreenOption] - Exiting ");
    	}catch (Exception e) {
			try {
				if(tx != null)
					tx.rollback();
			} catch (Exception e2) {
				// Re-throwing SwtException
				throw SwtErrorHandler.getInstance().handleException(e2,
						"saveScreenOption", ScreenOptionDAOHibernate.class);
			}

			logger.error("Exception caught in" + this.getClass().getName()
					+ "[saveScreenOption] " + e.getMessage());
			// Re-throwing SwtException
			throw SwtErrorHandler.getInstance().handleException(e,
					"saveScreenOption", ScreenOptionDAOHibernate.class);
		}finally{
			try {
				if(session!=null){
	    			session.close();
	    		}
			} catch (Exception e2) {
				// Re-throwing SwtException
				throw SwtErrorHandler.getInstance().handleException(e2,
						"saveScreenOption", ScreenOptionDAOHibernate.class);
			}  		
    	}		
	}

	/**
	 * Method to set default property values which are not in database
	 * 
	 * @param coll
	 * @return screenOptions
	 */
	@SuppressWarnings("unchecked")
	private Collection<ScreenOption> setDefaultPropertyValues(Collection coll)
			throws SwtException {
		// Collection to hold the screen option after setting the default values
		Collection<ScreenOption> screenOptions = null;
		// Declares the Iterator of ScreenOption
		Iterator iterator = null;
		// Declares ScreenOption object
		ScreenOption screenOption = null;
		try {
			// Creates new instance of ArrayList of ScreenOption
			screenOptions = new ArrayList<ScreenOption>();
			// Iterator instance for retrieved screen options
			iterator = coll.iterator();
			// Loop to run through all the screen options fetched
			while (iterator.hasNext()) {
				// Fetching each screen option from collection using
				// iterator.next
				screenOption = (ScreenOption) iterator.next();
				// Setting up property value based on the property name if it is
				// null
				if (screenOption.getId().getPropertyName().equals(
						SwtConstants.PROPNAME_CCY_PERSONAL_LIST)
						&& screenOption.getPropertyValue() == null) {
					screenOption
							.setPropertyValue(SwtConstants.DEFAULT_CCY_PERSONAL_LIST);
				} else if (screenOption.getId().getPropertyName().equals(
						SwtConstants.PROPNAME_HIDE_LORO)
						&& screenOption.getPropertyValue() == null) {
					screenOption
							.setPropertyValue(SwtConstants.DEFAULT_HIDE_LORO);
				} else if (screenOption.getId().getPropertyName().equals(
						SwtConstants.PROPNAME_HIDE_WEEKENDS)
						&& screenOption.getPropertyValue() == null) {
					screenOption
							.setPropertyValue(SwtConstants.DEFAULT_HIDE_WEEKENDS);
				} else if (screenOption.getId().getPropertyName().equals(
						SwtConstants.PROPNAME_REFRESH_RATE)
						&& screenOption.getPropertyValue() == null) {
					screenOption.setPropertyValue(PropertiesFileLoader
							.getInstance().getPropertiesValue(
									SwtConstants.DEFAULT_REFRESH_RATE));
				} else if (screenOption.getId().getPropertyName().equals(
						SwtConstants.PROPNAME_NUMBER_OF_DAYS)
						&& screenOption.getPropertyValue() == null) {
					screenOption.setPropertyValue(PropertiesFileLoader
							.getInstance().getPropertiesValue(
									SwtConstants.CURR_MONITOR_DEFAULT_DAYS));
				} else if (screenOption.getId().getPropertyName().equals(
						SwtConstants.PROPNAME_USE_CURRENCY_MULTIPLIER)
						&& screenOption.getPropertyValue() == null) {
					screenOption
							.setPropertyValue(SwtConstants.DEFAULT_USE_CURRENCY_MULTIPLIER);
				} else if (screenOption.getId().getPropertyName().equals(
						SwtConstants.PROPNAME_FONTSIZE)
						&& screenOption.getFontSize() == null) {
					screenOption.setFontSize(SwtConstants.DEFAULT_FONT_SIZE);
				}
				// Adding the screen option to the list
				screenOptions.add(screenOption);
			}
		} catch (Exception exception) {
			logger.error("Exception caught in" + this.getClass().getName()
					+ "[setDefaultPropertyValues] " + exception.getMessage());
			// Re-throwing SwtException
			throw SwtErrorHandler.getInstance().handleException(exception,
					"setDefaultPropertyValues", ScreenOptionDAOHibernate.class);
		}
		return screenOptions;
	}
	
	/**
	 * Method to delete the screen option
	 * 
	 * @throws SwtException
	 *             Collection<ScreenOption>
	 */
	@SuppressWarnings("unchecked")
	public void deleteScreenOption(ScreenOption screenOption)
			throws SwtException {
		// Fetch the list of screen options
		
		//declare a connection object
		Connection conn=null;
		//statement object is initialized
		PreparedStatement pst=null;
		//String variable for holding delete query
		String delete_notifications="";
		try {
			logger.debug(this.getClass().getName()
					+ " - [deleteILMCcyParametersDetails] - Entry");
			/* Method's local variable declaration */
			
		    conn=ConnectionManager.getInstance().databaseCon();
		    
		    //delete query	under checking HOST_ID, ENTITY_ID,  RELATION_ID,NOTIFICATION_TYPE,
		    delete_notifications="DELETE FROM S_SCREEN_OPTION  where HOST_ID=? and USER_ID=? and SCREEN_ID=? and PROPERTY_NAME = ?";
		    //declare statement for delete
		    pst=conn.prepareStatement(delete_notifications);
		    pst.setString(1, screenOption.getId().getHostId());
		    pst.setString(2, screenOption.getId().getUserId());
		    pst.setString(3, screenOption.getId().getScreenId());
		    pst.setString(4, screenOption.getId().getPropertyName());
		    //execute delete query
		    pst.executeUpdate();
		  //committed the transaction
		    conn.commit();
		    
			logger.debug(this.getClass().getName()
					+ " - [deleteILMCcyParametersDetails] - Exit");
		} catch (Exception e) {
			logger.error(this.getClass().getName()
					+ " - Exception Catched in [deleteILMCcyParametersDetails] method : - "
					+ e.getMessage());
			throw SwtErrorHandler.getInstance().handleException(e,
					"deleteILMCcyParametersDetails",
					ILMGeneralMaintenanceDAOHibernate.class);
		} finally {
			JDBCCloser.close(null, pst, conn, null);

		}
		
	}
	
	
	
	/**
	 * Method to check the screen Option
	 * @param hostId
	 * @param userId
	 * @param screenId
	 * @param propertyName
	 * @throws SwtException
	 */

	public boolean checkIfScreenOptionsExists(String hostId, String userId,String screenId, String propertyName)
			throws SwtException {
		Connection conn = null;
		ResultSet res = null;
		PreparedStatement stmt = null;
		ResultSet rs = null;
		boolean result = false;

		try {
			logger.debug("PreAdviceInputDAOHibernate  - [checkIfEntityExists] - Enter");

			conn = ConnectionManager.getInstance().databaseCon();
			String checkOptionQuery =  "select 1 from s_screen_option opt where opt.host_id=? and opt.user_id=? and opt.screen_id=? and opt.property_name=?";

			stmt = conn.prepareStatement(checkOptionQuery);
			stmt.setString(1, hostId);
			stmt.setString(2, userId);
			stmt.setString(3, screenId);
			stmt.setString(4, propertyName);
			stmt.execute();
			/* Fetching the Result */
			rs = (ResultSet) stmt.getResultSet();
			if (rs != null) {
				if(rs.next() == false) {
					result=false;
				}else {
					result=true;
				}
					
			}

			logger.debug("PreAdviceInputDAOHibernate  - [checkIfEntityExists] - Exit");
		} catch (SQLException ex) {
			try {
				logger.error("PreAdviceInputDAOHibernate  - [checkIfEntityExists]  Exception: " + ex.getMessage());
				// rollBack
				conn.rollback();
			} catch (SQLException e) {

			}

		} catch (Exception e) {
			logger.error("An exception occured in " + "PreAdviceInputDAOHibernate : checkIfEntityExists "
					+ e.getMessage());
			throw new SwtException(e.getMessage());

		} finally {
			// Close conn and nullify objects
			Object[] exceptions = JDBCCloser.close(res, stmt, conn, null);
		}

		return result;
	}
	
	
	/**
	 * This method is used to get property value from s_screen_option
	 * @param hostId
	 * @param userId
	 * @param screenId
	 * @param propertyName
	 * @throws SwtException
	 */
	public String getPropertyValue(String hostId, String userId,String screenId, String propertyName) throws SwtException{
		logger.debug(this.getClass().getName() + " - [getPropertyValue] - "
				+ "Entry");
		/* Method's local variable declaration */
		Connection conn = null;
		PreparedStatement stmt = null;
		ResultSet rs = null;
		String propertyValue = new String();
		try {
			conn = ConnectionManager.getInstance().databaseCon();
			String getPropertyValue = "select property_value from s_screen_option opt  where opt.host_id=? and opt.user_id=? and opt.screen_id=? and opt.property_name=?";
			stmt = conn.prepareStatement(getPropertyValue);
			stmt.setString(1, hostId);
			stmt.setString(2, userId);
			stmt.setString(3, screenId);
			stmt.setString(4, propertyName);
			stmt.execute();
			/* Fetching the Result */
			rs = (ResultSet) stmt.getResultSet();
			if (rs != null) {
				/* Loop to iterate result set */
				while (rs.next()) {
					propertyValue = rs.getString(1);
				}
			}			
			logger.debug(this.getClass().getName() + " - [getPropertyValue] - "
					+ "Exit");
		} catch (Exception exp) {
			logger.error(this.getClass().getName()
					+ " - Exception Catched in [getPropertyValue] method : - "
					+ exp.getMessage());
		} finally {
			JDBCCloser.close(rs, stmt, conn, null);

		}
		return propertyValue;
	}
	
	
}
