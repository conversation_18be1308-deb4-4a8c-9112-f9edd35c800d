/*
 * @(#)MovementSearchDAOHibernateAction.java 1.0 
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.work.dao.hibernate;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.Iterator;
import java.util.List;




import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.swallow.batchScheduler.ConnectionManager;
import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.maintenance.model.AcctMaintenance;
import org.swallow.maintenance.model.BookCode;
import org.swallow.maintenance.model.Group;
import org.swallow.maintenance.model.MetaGroup;
import org.swallow.util.CacheManager;
import org.swallow.util.JDBCCloser;
import org.swallow.util.LabelValueBean;
import org.swallow.util.PropertiesFileLoader;
import org.swallow.util.SwtConstants;
import org.swallow.util.SwtPager;
import org.swallow.util.SwtServerSideSortFilter;
import org.swallow.util.SwtUtil;
import org.swallow.work.dao.MovementSearchDAO;
import org.springframework.context.annotation.Lazy;
import org.springframework.orm.hibernate5.support.HibernateDaoSupport;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.stereotype.Repository;
import org.springframework.beans.factory.annotation.Autowired;
import org.hibernate.HibernateException;
import org.hibernate.SessionFactory;
import org.hibernate.Transaction;
import org.hibernate.query.Query;
import org.hibernate.type.DateType;
import org.hibernate.Session;

/**
 * <AUTHOR>
 * 
 * TODO To change the template for this generated type comment go to Window -
 * Preferences - Java - Code Style - Code Templates
 */
@Repository ("movementSearchDAO")
@Transactional
public class MovementSearchDAOHibernate extends HibernateDaoSupport implements
		MovementSearchDAO {
	public MovementSearchDAOHibernate(@Lazy SessionFactory sessionfactory) {
	    setSessionFactory(sessionfactory);
	}

	private final Log log = LogFactory.getLog(MovementSearchDAOHibernate.class);


	public Collection getCurrencyDetails(String hostId, String entityId) {
		log.debug("Inside the Currency details method");

		java.util.List currencyList = getHibernateTemplate()
				.find(
						" from Currency c where c.id.hostId = ?0 and c.id.entityId = ?1 and c.id.currencyCode != '*' order by c.id.currencyCode",
						new Object[] { hostId, entityId });
		log.debug("Outside the Currency details method");

		return currencyList;
	}

	public Collection getBeneficiaryDetails(String hostId, String entityId) {
		log.debug("Inside the beneficiary details method");

		java.util.List beneficiaryList = getHibernateTemplate()
				.find(
						"from Party p where p.id.hostId = ?0 and p.id.entityId = ?1 and p.partyType !='U' order by p.id.partyId",
						new Object[] { hostId, entityId });
		log.debug("Outside the beneficiary details method");

		return beneficiaryList;
	}

	public Collection getCounterPartyDetails(String hostId, String entityId) {
		log.debug("Inside the counterparty details method");

		java.util.List counterpartyList = getHibernateTemplate()
				.find(
						"from Party p where p.id.hostId = ?0 and p.id.entityId = ?1 and p.partyType !='U' order by p.id.partyId",
						new Object[] { hostId, entityId });
		log.debug("Outside the counterparty details method");

		return counterpartyList;
	}

	public Collection getCustodianDetails(String hostId, String entityId) {
		log.debug("Inside the custodian details method");

		java.util.List custodianList = getHibernateTemplate()
				.find(
						"from Party p where p.id.hostId = ?0 and p.id.entityId = ?1 and p.partyType ='U' order by p.id.partyId",
						new Object[] { hostId, entityId });
		log.debug("Outside the custodian details method");

		return custodianList;
	}

	public Collection getBookCodeDetails(String hostId, String entityId) {
		log.debug("Inside the bookcode details method");

		java.util.List bookList = getHibernateTemplate()
				.find(
						"from BookCode book where book.id.hostId = ?0 and book.id.entityId = ?1 order by book.id.bookCode",
						new Object[] { hostId, entityId });
		log.debug("Outside the bookcode details method");

		return bookList;
	}

	public Collection getGroupDetails(String hostId, String entityId) {
		log.debug("Inside the getgroupdetails");

		String grpHQL = " select m.id.hostId, m.id.entityId, m.id.groupId, m.groupName, s.groupLevelName from Group m , GroupLevel s where m.id.hostId = s.id.hostId "
				+ " and  m.id.entityId = s.id.entityId and  m.groupLvlCode = s.id.groupLevelId and m.id.hostId = ?0 and m.id.entityId = ?1";

		java.util.List list = getHibernateTemplate().find(grpHQL,
				new Object[] { hostId, entityId });

		Collection groupList = new ArrayList();
		log.debug("noofRecords.size : " + list.size());

		// Iterating through the collection (list of groups plus groupLvl name)
		Object[] row = null;

		if (list != null) {
			Iterator itr = list.iterator();

			while (itr.hasNext()) {
				row = (Object[]) itr.next();

				Group group = new Group();

				group.getId().setHostId((String) row[0]);

				group.getId().setEntityId((String) row[1]);

				group.getId().setGroupId((String) row[2]);

				group.setGroupName((String) row[3]);

				group.setGroupLvlName((String) row[4]);
				groupList.add(group);

				log.debug("The Job record from the database is==>"
						+ group.toString());
			}
		}

		log.debug("Exiting the getgroupdetails");

		return groupList;
	}

	public Collection getMetaGroupDetails(String hostId, String entityId) {
		log.debug("Inside the metagetgroupdetails");

		String metaGrpHQL = " select m.id.hostId, m.id.entityId, m.id.mgroupId, m.mgroupName, s.mgrpLvlName from MetaGroup m , MetaGroupLevel s where m.id.hostId = s.id.hostId "
				+ " and  m.id.entityId = s.id.entityId and  m.mgrpLvlCode = s.id.mgrpLvlCode and m.id.hostId = ?0 and m.id.entityId = ?1";

		java.util.List list = getHibernateTemplate().find(metaGrpHQL,
				new Object[] { hostId, entityId });

		Collection metaGroupList = new ArrayList();
		log.debug("noofRecords.size : " + list.size());

		// Iterating through the collection (list of metagroups plus
		// metagroupLvl name) and setting the values in individual beans
		Object[] row = null;

		if (list != null) {
			Iterator itr = list.iterator();

			while (itr.hasNext()) {
				row = (Object[]) itr.next();

				MetaGroup metaGroup = new MetaGroup();

				metaGroup.getId().setHostId((String) row[0]);

				metaGroup.getId().setEntityId((String) row[1]);

				metaGroup.getId().setMgroupId((String) row[2]);

				metaGroup.setMgroupName((String) row[3]);

				metaGroup.setMgrpLvlName((String) row[4]);
				metaGroupList.add(metaGroup);

				log.debug("The Job record from the database is==>"
						+ metaGroup.toString());
			}
		}

		log.debug(" metaGroup List return  :::" + metaGroupList.toString());
		log.debug("Outside the metagetgroupdetails");

		return metaGroupList;
	}

	public Collection getAccountDetails(String hostId, String entityId) {
		log.debug("Inside the accountdetails");

		java.util.List accountList = (List ) getHibernateTemplate().find(
						"from AcctMaintenance ac where ac.id.hostId = ?0 and ac.id.entityId = ?1 and ac.id.accountId != '*'  order by ac.id.accountId",
						new Object[] { hostId, entityId });
		log.debug("Outside the accountdetails");

		return accountList;
	}

	/**
	 * @desc : fetches all the accounts defined for hostId, entityId and
	 *       currCode
	 * @param hostid
	 * @param entityid
	 * @param currCode
	 * @return
	 */
	public Collection getAccountDetails(String hostId, String entityId,
			String currCode) {
		log.debug("Entering the accountdetails");

		java.util.List accountList = (List ) getHibernateTemplate().find(
						"from AcctMaintenance ac where ac.id.hostId = ?0 and ac.id.entityId = ?1 and ac.currcode = ?2 and ac.id.accountId != '*'  order by ac.id.accountId",
						new Object[] { hostId, entityId, currCode });
		log.debug("Exiting the accountdetails");

		return accountList;
	}

	/*
	 * Start:Code Modified For Mantis 2072 by Sudhakar on 31-10-2012:Currency
	 * change should not change Status and Account class to All
	 */
	/**
	 * This method is used to get account details based on the currency code and
	 * account class
	 * 
	 * @param hostId
	 * @param entityId
	 * @param accountClass
	 * @param currCode
	 * @return accountList
	 * @throws SwtException
	 */
	public Collection getAccountClassDetails(String hostId, String entityId,
			String accountClass, String currCode) throws SwtException {
		// To hold the hql query
		String hqlQuery = null;
		// To hold the list of account which is retrieved based on account class
		// and currency code
		List accountList = null;
		try {
			log.debug(this.getClass().getName()
					+ " - [getAccountClassDetails]  - Entering ");
			// retrieve the account list,if currency code and account class is
			// not
			// all
			if ((!accountClass.equals(SwtConstants.ALL_VALUE))
					&& (!currCode.equals(SwtConstants.ALL_VALUE))) {
				hqlQuery = "from AcctMaintenance ac where ac.id.hostId = ?0 and ac.id.entityId = ?1 and ac.currcode = ?2 and ac.id.accountId != '*' and ac.acctClass = ?3  order by ac.id.accountId";
				accountList = (List ) getHibernateTemplate().find(
								hqlQuery,
								new Object[] { hostId, entityId, currCode,
										accountClass });
				// retrieve the account list,if account class is all and
				// currency code is not all
			} else if ((accountClass.equals(SwtConstants.ALL_VALUE))
					&& (!currCode.equals(SwtConstants.ALL_VALUE))) {
				hqlQuery = "from AcctMaintenance ac where ac.id.hostId = ?0 and ac.id.entityId = ?1 and ac.currcode = ?2 and ac.id.accountId != '*' order by ac.id.accountId";
				accountList = (List ) getHibernateTemplate().find(hqlQuery,
						new Object[] { hostId, entityId, currCode });
				// retrieve the account list,if account class is not all and
				// currency code is all
			} else if ((!accountClass.equals(SwtConstants.ALL_VALUE))
					&& (currCode.equals(SwtConstants.ALL_VALUE))) {
				hqlQuery = "from AcctMaintenance ac where ac.id.hostId = ?0 and ac.id.entityId = ?1 and ac.id.accountId != '*' and ac.acctClass = ?2  order by ac.id.accountId";
				accountList = (List ) getHibernateTemplate().find(hqlQuery,
						new Object[] { hostId, entityId, accountClass });
			} else {
				hqlQuery = "from AcctMaintenance ac where ac.id.hostId = ?0 and ac.id.entityId = ?1 and ac.id.accountId != '*' order by ac.id.accountId";
				accountList = (List ) getHibernateTemplate().find(hqlQuery,
						new Object[] { hostId, entityId });
			}
			log.debug(this.getClass().getName()
					+ " - [ getAccountClassDetails ] - Exit ");
			return accountList;
		} catch (Exception ex) {
			// log error message
			log.error(this.getClass().getName()
					+ " - [getAccountClassDetails] - Exception - "
					+ ex.getMessage());
			// Re-throw an exception
			throw SwtErrorHandler.getInstance().handleException(ex,
					"getAccountClassDetails", MovementSearchDAOHibernate.class);
		} finally {
			// nullify the objects
			hqlQuery = null;
		}

	}

	/*
	 * End:Code Modified For Mantis 2072 by Sudhakar on 31-10-2012:Currency
	 * change should not change Status and Account class to All
	 */
	/**
	 * 
	 * @param listToPopulate
	 * @param hostid
	 * @param entityid
	 * @param status
	 * @param movementtype
	 * @param sign
	 * @param predictstatus
	 * @param amountover
	 * @param amountunder
	 * @param currencycode
	 
	 * @param beneficiaryId
	 * @param custodianId
	 * @param poslevel
	 * @param accountId
	 * @param group
	 * @param metaGroup
	 * @param bookCode
	 * @param sortorder
	 * @param fromdate
	 * @param todate
	 * @param reference
	 * @param msgtype
	 * @param inputdate
	 * @param counterparty
	 * @param fintrade
	 * @param timefrom
	 * @param timeto
	 * @param format
	 * @param currentPage
	 * @param incrementFactor
	 * @return
	 * @throws SwtException
	 */
	public int fetchDetails(List list, String hostId, String entityId,
			String status, String movementtype, String sign,
			String predictstatus, Double amountover, Double amountunder,
			String currencycode, String beneficiaryId,
			String custodianId, Integer poslevel, String accountId,
			String group, String metaGroup, String bookCode, String sortorder,
			String fromdate, String todate, String reference, String msgtype,
			String inputdate, String counterparty, String fintrade,
			String format, String timefrom, String timeto, int currentPage,
			int incrementFactor, String filterSortStatus, int totalCount)
			throws SwtException {
		ArrayList paramList = new ArrayList();
		ArrayList typeList = new ArrayList();
		List arraylist = null;

		Session sess = null;

		boolean isNext = false;

		try {
			if (!(sortorder.equalsIgnoreCase("A"))) {
				arraylist = (List ) getHibernateTemplate().find(
								"from AcctMaintenance acct where acct.id.entityId =?0 and acct.acctClass=?1",
								new Object[] { entityId, sortorder });
			} else {
				arraylist = (List ) getHibernateTemplate().find(
						"from AcctMaintenance acct where acct.id.entityId =?0 ",
						new Object[] { entityId });
			}

			Iterator itr = arraylist.iterator();
			ArrayList accountArray = new ArrayList();
			StringBuffer strAccount = new StringBuffer();
			int count = 0;

			while (itr.hasNext()) {
				AcctMaintenance AccountMaint = (AcctMaintenance) itr.next();
				accountArray.add(AccountMaint.getId().getAccountId());

				if (count == 0) {
					strAccount.append("'" + AccountMaint.getId().getAccountId()
							+ "'");
					count = 1;
				} else {
					strAccount.append("," + "'"
							+ AccountMaint.getId().getAccountId() + "'");
				}
			}

			ArrayList inputObjects = new ArrayList();
			StringBuffer str = new StringBuffer(
					"from Movement p where p.id.hostId ='" + hostId
							+ "' and p.id.entityId='" + entityId + "'");

			// Movementtype
			if (!movementtype.equals("B")) {
				str.append("  and p.movementType='" + movementtype + "'");
			}

			// Predict status
			if (!predictstatus.equals("A")) {
				str.append(" and p.predictStatus='" + predictstatus + "'");
			}

			// Match Status
			if (!status.equals("X")) {
				if (status.equals("D")) {
					str.append("  and p.matchStatus IN('M','C','S')");
				} else {
					str.append(" and p.matchStatus='" + status + "'");
				}

			}

			if (!sign.equals("B")) {
				str.append(" and p.sign='" + sign + "'");
			}

			double over = amountover.doubleValue();
			double under = amountunder.doubleValue();

			if (over == 0.0) {
				if (under == 0.0) {
				} else {
					str.append(" and p.amount > " + amountover
							+ " and p.amount < " + amountunder + " ");
				}
			} else {
				if (under == 0.0) {
					str.append(" and p.amount > " + amountover + " ");
				} else {
					str.append(" and p.amount > " + amountover
							+ " and p.amount < " + amountunder + " ");
				}
			}

			// Currency Code
			if (!currencycode.equals("")) {
				str.append(" and p.currencyCode ='" + currencycode + "'");
			}


			// Beneficiary Id
			if (!beneficiaryId.equals("")) {
				str.append(" and p.beneficiaryId='" + beneficiaryId + "'");
			}

			// Custodian Id
			if (!custodianId.equals("")) {
				str.append(" and p.custodianId='" + custodianId + "'");
			}

			// CounterParty
			if (!counterparty.equals("")) {
				str.append(" and p.counterPartyId='" + counterparty + "'");
			}

			// Position Level
			int position = poslevel.intValue();

			if ((position != 1) && (position != 2) && (position != 3)
					&& (position != 4) && (position != 5) && (position != 6)
					&& (position != 7) && (position != 8) && (position != 9)) {
			} else {
				str.append(" and p.positionLevel='" + position + "'");
			}

			// Account ID
			if (!sortorder.equalsIgnoreCase("A")) {
				if (!accountId.equals("")) {
					str.append(" and p.accountId='" + accountId + "'");
				} else {
					if (!sortorder.equals("")) {
						log.debug("Sort Order-->" + sortorder);

						if (!(arraylist.size() == 0)) {
							str.append(" and p.accountId IN (" + strAccount
									+ ")");

						}
					}
				}
			}

			// MetaGroup, Group and Bookcode
			if (!bookCode.equals("")) {
				str.append(" and p.bookCode='" + bookCode + "'");
			} else if (!group.equals("")) {
				// have to fetch bookcodes according to the group selected
				String groupquery = "select book.id.bookCode from BookCode book where book.groupIdLevel1= :group"
						+ " or book.groupIdLevel2= :group"
						+ " or book.groupIdLevel3= :group"
						+ " and book.id.hostId= :hostId"
						+ " and book.id.entityId = :entityId";
				java.util.List bookcodelist1 = getHibernateTemplate().findByNamedParam(groupquery, new String[]{"group","hostId","entityId"}, new String[]{group,hostId,entityId});
				int list1 = bookcodelist1.size();

				String val = "";

				if (list1 == 0) {
					str.append(" and p.bookCode IN ('')");
				}

				for (int i = 0; i < list1; i++) {
					val = (String) bookcodelist1.get(i);

					if (i == 0) {
						str.append("and p.bookCode IN ( '");
					}

					if (i == (list1 - 1)) {
						str.append(val + "'");
						str.append(")");
					} else {
						str.append(val + "','");
					}
				}
			} else if (!metaGroup.equals("")) {
				// have to fetch bookcodes according to the metagroup selected
				String metagroupquery = "select gro.id.groupId from Group gro where gro.mgroupId=?0"
						+ " and gro.id.hostId=?1"
						+ " and gro.id.entityId=?2";
				java.util.List grouplist = getHibernateTemplate().find(
						metagroupquery, new Object[]{metaGroup, hostId, entityId});

				int list2 = grouplist.size();

				String value = "";
				java.util.List bookcodelist = new ArrayList();

				for (int i = 0; i < list2; i++) {
					value = (String) grouplist.get(i);

					String bookquery = "select book.id.bookCode from BookCode book where book.groupIdLevel1=:groupIdLevel"
							+ " or book.groupIdLevel2= :groupIdLevel"
							+ " or book.groupIdLevel3= :groupIdLevel"
							+ " and book.id.hostId= :hostId"
							+ " and book.id.entityId= :entityId ";
					java.util.List temp = getHibernateTemplate()
							.findByNamedParam(bookquery, new String[]{"groupIdLevel","hostId","entityId"}, new String[]{value,hostId,entityId});

					for (int j = 0; j < temp.size(); j++) {
						bookcodelist.add(j, temp.get(j));
					}
				}

				// All the bookcodes fetched
				int bookslist = bookcodelist.size();

				if (bookslist == 0) {
					str.append(" and p.bookCode IN ('')");
				}

				for (int i = 0; i < bookslist; i++) {
					String val = (String) bookcodelist.get(i);

					if (i == 0) {
						str.append("and p.bookCode IN ( '");
					}

					if (i == (bookslist - 1)) {
						str.append(val + "'");
						str.append(")");
					} else {
						str.append(val + "','");
					}
				}
			}

			Date fromDt = null;
			Date toDt = null;

			if (!fromdate.equals("") && !todate.equals("")) {
				// Both From and To date are non empty
				if (format.equalsIgnoreCase("D")) {
					fromDt = SwtUtil.parseDate(fromdate, "dd/MM/yyyy");
					toDt = SwtUtil.parseDate(todate, "dd/MM/yyyy");
				} else {
					fromDt = SwtUtil.parseDate(fromdate, "MM/dd/yyyy");
					toDt = SwtUtil.parseDate(todate, "MM/dd/yyyy");
				}

				str.append(" and p.valueDate >= ?0 ");
				str.append(" and p.valueDate <= ?1 ");
				paramList.add(fromDt);
				typeList.add(new DateType());
				paramList.add(toDt);
				typeList.add(new DateType());
			} else if (fromdate.equals("") && todate.equals("")) {
				str.append("and p.valueDate >= ?0  ");
				paramList.add(SwtUtil.getSystemDatewithoutTime());
				typeList.add(new DateType());

			} else if (!fromdate.equals("")) {
				if (format.equalsIgnoreCase("D")) {
					fromDt = SwtUtil.parseDate(fromdate, "dd/MM/yyyy");
				} else {
					fromDt = SwtUtil.parseDate(fromdate, "MM/dd/yyyy");
				}

				str.append(" and p.valueDate >= ?0 ");
				paramList.add(fromDt);
				typeList.add(new DateType());
			} else if (!todate.equals("")) {
				if (format.equalsIgnoreCase("D")) {
					toDt = SwtUtil.parseDate(todate, "dd/MM/yyyy");
				} else {
					toDt = SwtUtil.parseDate(todate, "MM/dd/yyyy");
				}

				str.append(" and p.valueDate >= ?0 ");
				str.append(" and p.valueDate <= ?1 ");
				paramList.add(SwtUtil.getSystemDatewithoutTime());
				typeList.add(new DateType());
				paramList.add(toDt);
				typeList.add(new DateType());
			}

			if (!(timefrom.equals("")) && (!(timeto.equals("")))) {

				String checkstatus = "select distinct p.id.movementId from Movement p where  (p.id.hostId='"
						+ hostId
						+ "') and (p.id.entityId='"
						+ entityId
						+ "') and ((to_date(to_char(p.inputDate,'HH24:MI'),'HH24:MI') > to_date('"
						+ timefrom
						+ "','HH24:MI')) and (to_date(to_char(p.inputDate,'HH24:MI'),'HH24:MI') < to_date('"
						+ timeto + "','HH24:MI')))";

				str
						.append(" and ((to_date(to_char(p.inputDate,'HH24:MI'),'HH24:MI') >= to_date('"
								+ timefrom
								+ "','HH24:MI')) and (to_date(to_char(p.inputDate,'HH24:MI'),'HH24:MI') < to_date('"
								+ timeto + "','HH24:MI')))");
			}

			if (!fintrade.equals("B")) {
				String val = null;

				if (fintrade.equals("T")) {
					val = "T";
				} else if (fintrade.equals("F")) {
					val = "F";
				}

				java.util.List grouplist = new ArrayList();
				BookCode book = null;
				String flagfin = "false";
				String flagtrade = "false";
				Collection metaGroupCollection = null;
				ArrayList finalbookCollection = new ArrayList();

				String metaGroupHQL = "from MetaGroup m where  m.id.hostId=?0 and m.id.entityId=?1 and m.financeTrade=?2";
				String groupHQL = "from Group m where  m.id.hostId=?0 and m.id.entityId=?1 and m.mgroupId=?2";
				String bookHQL = "from BookCode m where  m.id.hostId=?0 and m.id.entityId=?1 and (m.groupIdLevel1=?2 or m.groupIdLevel2=?3 or m.groupIdLevel3=?4) ";
				metaGroupCollection = (Collection ) getHibernateTemplate().find(metaGroupHQL,
						new Object[] { hostId, entityId, val });

				Iterator metaGroupIterator = null;

				if (metaGroupCollection.size() > 0) {
					metaGroupIterator = metaGroupCollection.iterator();

					while (metaGroupIterator.hasNext()) {
						Collection groupCollection = null;
						MetaGroup mGroup = (MetaGroup) metaGroupIterator.next();

						groupCollection = (Collection ) getHibernateTemplate().find(
								groupHQL,
								new Object[] { hostId, entityId,
										mGroup.getId().getMgroupId() });

						Iterator groupIterator = null;

						if (groupCollection.size() > 0) {
							groupIterator = groupCollection.iterator();

							while (groupIterator.hasNext()) {
								Collection bookCollection = null;
								Group grp = (Group) groupIterator.next();
								bookCollection = getHibernateTemplate().find(
										bookHQL,
										new Object[] { hostId, entityId,
												grp.getId().getGroupId(),
												grp.getId().getGroupId(),
												grp.getId().getGroupId() });

								if (bookCollection.size() > 0) {
									finalbookCollection.addAll(bookCollection);

								}
							}
						}
					}
				}

				if (finalbookCollection.size() == 0) {
					str.append(" and p.bookCode IN('')");
				} else {
					for (int i = 0; i < finalbookCollection.size(); i++) {
						if (i == 0) {
							str.append(" and p.bookCode IN('");
						}

						if (i == (finalbookCollection.size() - 1)) {
							str.append(((BookCode) finalbookCollection.get(i))
									.getId().getBookCode()
									+ "'");
							str.append(")");
						} else {
							str.append(((BookCode) finalbookCollection.get(i))
									.getId().getBookCode()
									+ "','");
						}
					}
				}
			}

			// Msg Type
			if (!msgtype.equals("")) {
				str.append(" and p.messageFormat like '" + msgtype + "%'");
			}

			// Input Date
			if (!inputdate.equals("")) {
				str
						.append(" and to_date(to_char(p.inputDate,'DD-MM-YYYY'),'DD-MM-YYYY') = to_date('"
								+ inputdate + "','DD-MM-YYYY')");
			}

			// Reference
			if (!reference.equals("")) {
				str.append(" and (p.reference1 like '" + reference
						+ "%' or p.reference2 like '" + reference
						+ "%' or p.reference3 like '" + reference
						+ "%' or p.reference4 like '" + reference + "%')");
			}

			str = SwtServerSideSortFilter.constructQuery(entityId, str,
					filterSortStatus, format);

			String strQuery = str.toString();
			log.debug("final Query:::" + strQuery);

			StringBuffer strmovCount = new StringBuffer(
					" select count(p.id.movementId) ");
			strmovCount.append(str.toString());

			int pageSize = SwtUtil.getPageSizeFromProperty(SwtConstants.MOVEMENT_SUMMARY_SCREEN_PAGE_SIZE);
			Object[] params = paramList.toArray();
			Object[] types = typeList.toArray();
			SessionFactory sf = (SessionFactory) SwtUtil
					.getBean("sessionFactory");
			sess = sf.openSession();
			Query queryCount = sess.createQuery(strmovCount.toString());

			for (int i = 0; i < params.length; i++) {
				queryCount.setParameter(i, params[i]);
			}

			if (totalCount == 0) {
				List movlist = queryCount.list();

				totalCount = ((Integer) movlist.get(0)).intValue();
			}

			Query query = sess.createQuery(strQuery);

			for (int i = 0; i < params.length; i++) {
				query.setParameter(i, params[i]);
			}

			isNext = SwtPager.next(query, currentPage, totalCount, pageSize,
					list);

		} catch (HibernateException hibernateException) {
			log.debug("Problem in accessing Hibernate properties;");
			hibernateException.printStackTrace();
			throw new SwtException(hibernateException.getMessage());
		} finally {

			JDBCCloser.close(sess);
		}

		log.debug("Outside the fetch details");

		return totalCount;
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see org.swallow.work.dao.MovementSearchDAO#getArchiveList()
	 */
	public List getArchiveList() throws SwtException {
		log.debug("Entering getArchiveList method");

		String hostId = CacheManager.getInstance().getHostId();
		List archiveList = getHibernateTemplate().find(
				"from Archive a where a.id.hostId=?0 and a.moduleId = 'Predict'", new Object[] { hostId });
		return archiveList;
	}
	
	
	public ArrayList<LabelValueBean>  getPositionLevelList(String hostId) throws SwtException{
		log.debug(this.getClass().getName() + " - [getPositionLevelList] - "
				+ "Entry");
		/* Method's local variable declaration */
		Connection conn = null;
		Session session = null;
		PreparedStatement stmt = null;
		ResultSet rs = null;
		ArrayList<LabelValueBean> posLevels =null;
		try {
			posLevels = new ArrayList<LabelValueBean>();
			conn = ConnectionManager.getInstance().databaseCon();
			String getPosLevel = "select distinct(POSITION_LEVEL) from P_POSITION_LEVEL_NAME where HOST_ID='" + hostId + "' ORDER BY  POSITION_LEVEL ASC";
			stmt = conn.prepareStatement(getPosLevel);
			stmt.execute();
			/* Fetching the Result */
			rs = (ResultSet) stmt.getResultSet();
			if (rs != null) {
				/* Loop to iterate result set */
				while (rs.next()) {					
					posLevels.add(new LabelValueBean(rs.getString(1),rs.getString(1)));
				}
			}	
			log.debug(this.getClass().getName() + " - [getPositionLevelList] - "
					+ "Exit");
			//return mvtType;
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [getPositionLevelList] method : - "
					+ exp.getMessage());
		} finally {
			JDBCCloser.close(rs, stmt, conn, session);

		}
		return posLevels;
	}
}