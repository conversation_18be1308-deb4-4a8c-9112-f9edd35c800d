/*
 * Created on Jan 4, 2006
 *
 * TODO To change the template for this generated file go to
 * Window - Preferences - Java - Code Style - Code Templates
 */
package org.swallow.work.dao.hibernate;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Iterator;
import java.util.List;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

import org.hibernate.*;
import org.hibernate.criterion.Expression;
import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.model.User;
import org.swallow.util.CacheManager;
import org.swallow.util.JDBCCloser;
import org.swallow.util.SessionManager;
import org.swallow.util.SwtConstants;
import org.swallow.util.SwtInterceptor;
import org.swallow.util.SwtMailSender;
import org.swallow.util.SwtUtil;
import org.swallow.util.SystemFormats;
import org.swallow.util.UserThreadLocalHolder;
import org.swallow.work.dao.SweepAlertDAO;
import org.swallow.work.model.SweepAlert;
import org.springframework.context.annotation.Lazy;
import org.springframework.orm.hibernate5.support.HibernateDaoSupport;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.stereotype.Repository;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR> Tripathi has modified this class on 25th Feb 08
 * 
 */
@Repository ("sweepAlertDAO")
@Transactional
public class SweepAlertDAOHibernate extends HibernateDaoSupport implements
		SweepAlertDAO {
	public SweepAlertDAOHibernate(@Lazy SessionFactory sessionfactory) {
	    setSessionFactory(sessionfactory);
	}


	private final Log log = LogFactory.getLog(SweepAlertDAOHibernate.class);

	/**
	 * 
	 */
	public Collection checkSweepAlertMessages(String hostId, String roleId,
			SystemFormats formats) throws SwtException {
		Collection collAlerts = checkSweepNonMGAlertMessages(hostId, roleId);
		/**
		 * Start : Modified for including the alerts for sweep message generation as per
		 * James suggestion for Mantis 1197 by sutheendran Balaji A on
		 * 16-July-2010
		 */
		// Collection collMGAlerts = checkMGSweepAlerts(hostId, roleId);
		// sweelAlerts.addAll(collMGAlerts);
		// setMGAlertsAsInterSession(collMGAlerts, formats);
		// insertSweepAlertLogs(collAlerts);
		User usr = SwtUtil.getCurrentUser(UserThreadLocalHolder
				.getUserSession());
		if (usr.getAlertType().equals(SwtConstants.ALERT_BOTH)
				|| usr.getAlertType().equals(SwtConstants.ALERT_EMAIL)) {
			sendMailtoNonMGAlerts(collAlerts);
		}
		return collAlerts;
		/**
		 * End : Modified for including the alerts for sweep message generation as per
		 * James suggestion for Mantis 1197 by sutheendran Balaji A on
		 * 16-July-2010
		 */
	}

	private void sendMailtoNonMGAlerts(Collection collNonMGAlerts)
			throws SwtException {
		/**
		* Start : Modified for including the alerts for sweep message generation as per
		* James suggestion for Mantis 1197 by sutheendran Balaji A on
		* 16-July-2010
		*/
		User usr = SwtUtil.getCurrentUser(UserThreadLocalHolder
				.getUserSession());
		/**
		* End : Modified for including the alerts for sweep message generation as per
		* James suggestion for Mantis 1197 by sutheendran Balaji A on
		* 16-July-2010
		*/
		Iterator itr = collNonMGAlerts.iterator();
		while (itr.hasNext()) {
			SweepAlert sweepAlertObj = (SweepAlert) (itr.next());
			String msg = sweepAlertObj.getAlertMaster().getAlertmessage();
			Long sweepId = sweepAlertObj.getSweepId();
			Double sweepAmount = sweepAlertObj.getSweepAmount();
			String mailTo = usr.getEmailId();
			String mailFrom = "<EMAIL>";
			String mailSubj = msg;
			StringBuffer mailBody = new StringBuffer();
			mailBody.append("Message - " + msg);
			mailBody.append("\n");
			mailBody.append("Sweep Id - " + sweepAlertObj.getSweepId());
			mailBody.append("\n");
			mailBody.append("Sweep Amount - " + sweepAlertObj.getSweepAmount());
			mailBody.append("\n");
			mailBody.append("\n");
			SwtMailSender.getInstance().sendMail(mailFrom, mailTo, mailSubj,
					mailBody.toString());
		}
	}

	private void setMGAlertsAsInterSession(Collection mgSweelAlerts,
			SystemFormats formats) throws SwtException {
		SessionManager sesisonManager = SessionManager.getInstance();
		String sessionId = UserThreadLocalHolder.getUserSessionId();
		Iterator itr = mgSweelAlerts.iterator();
		StringBuffer messageBuf = new StringBuffer();
		while (itr.hasNext()) {
			SweepAlert sweepAlertObj = (SweepAlert) (itr.next());
			if (sweepAlertObj != null) {
				messageBuf.append(getMGAlertMessages(sweepAlertObj, formats));
			}
		}
		sesisonManager.sendMessage(sessionId, messageBuf.toString());
	}

	private StringBuffer getMGAlertMessages(SweepAlert sweepAlertObj,
			SystemFormats formats) throws SwtException {
		StringBuffer buf = new StringBuffer();
		if (sweepAlertObj != null) {
			buf.append("Message - "
					+ sweepAlertObj.getAlertMaster().getAlertmessage());
			buf.append("\n");
			buf.append("Sweep Id - " + sweepAlertObj.getSweepId());
			buf.append("\n");
			buf.append("Sweep Amount - "
					+ SwtUtil.formatCurrency(sweepAlertObj.getSweepAmount(),
							formats.getCurrencyFormat()));
			buf.append("\n");
			buf.append("\n");
		}
		return buf;
	}

	private Collection checkSweepNonMGAlertMessages(String hostId, String roleId)
			throws SwtException {
		/**
		 * Modified for including the alerts for Sweep Message Generation as per
		 * James suggestion for Mantis 1197 By Sutheendran Balaji A on
		 * 16-July-2010
		 */
		List list = getHibernateTemplate().find(
				"from SweepAlert s  where s.id.hostId=?0 and (s.alertMaster.roleId=?1 "
						+ " or s.alertMaster.roleId='All') "
						+ " and s.status = 'P' ",
				new Object[] { hostId, roleId });
		return list;
	}
	/**
	 * Start : Modified for including the alerts for sweep message generation as per
	 * James suggestion for Mantis 1197 by sutheendran Balaji A on
	 * 16-July-2010
	 */
	/**
	 * Fetches the url of the screens to be opened for each alert stage.
	 * 
	 * @param sweepAlert
	 * @return String
	 * @throws SwtException
	 */
	public String getMenuAccessId(SweepAlert sweepAlert) throws SwtException {
		SessionFactory sf = null;
		Connection conn = null;
		PreparedStatement st = null;
		Session session = null;
		String alertUrl = "#";
		StringBuffer qryStr = new StringBuffer();
		String accessId = "";
		String programName = "";
		ResultSet rs = null;
		try {
			qryStr.append("Select a.menu_item_id,a.access_id,p.program_name,");
			qryStr.append("m.width,m.height from p_menu_access a,s_program p");
			qryStr.append(" , p_menu_item m WHERE a.role_id = ");
			qryStr.append("?");
			qryStr.append(" and m.menu_item_id = ");
			qryStr.append("?");
			qryStr.append(" and a.menu_item_id = m.menu_item_id and ");
			qryStr.append("m.program_id = p.program_id ");
			
			sf = (SessionFactory) SwtUtil.getBean("sessionFactory");
			session = sf.openSession();
			conn = SwtUtil.connection(session);
			st = conn.prepareStatement(qryStr.toString());
			st.setString(1, sweepAlert.getAlertMaster().getRoleId());
			st.setString(2, SwtConstants.getAlertStageMenuitems(sweepAlert.getAlertStage()));
			rs = st.executeQuery();
			while(rs.next()){
				accessId = rs.getString(2);
				programName = rs.getString(3);
				if(programName.indexOf('?') > 0){
					alertUrl = programName + "&menuAccessId=" + accessId;
				}else{
					alertUrl = programName + "?menuAccessId=" + accessId;
				}
				sweepAlert.setWidth(rs.getString(4));
				sweepAlert.setHeight(rs.getString(5));
				sweepAlert.setAccessId(accessId);
			}
			
		} catch (SQLException he) {
			log.error(this.getClass().getName()
					+ " - SQLException Catched in [getMenuAccessId] method : - "
					+ he.getMessage());
			he.printStackTrace();
			throw SwtErrorHandler.getInstance().handleException(he,
					"getMenuAccessId", SweepAlertDAOHibernate.class);
		}catch (Exception e) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [getMenuAccessId] method : - "
					+ e.getMessage());
			e.printStackTrace();
			throw SwtErrorHandler.getInstance().handleException(e,
					"getMenuAccessId", SweepAlertDAOHibernate.class);
		} finally {
			
			//edited by KaisBS for mantis 1745 : introducing the implementation of the JDBCCloser class
			SwtException thrownException = null;
			Object[] exceptions = JDBCCloser.close(rs, st, null, session);

			if (exceptions[0] != null)
			    thrownException = SwtErrorHandler.getInstance().handleException((SQLException) exceptions[0], "getMenuAccessId",
						SweepAlertDAOHibernate.class);

			if (thrownException == null && exceptions[1] !=null) 
			    thrownException = SwtErrorHandler.getInstance().handleException((HibernateException) exceptions[1], "getMenuAccessId",
						SweepAlertDAOHibernate.class);

			if (thrownException!=null)
			    throw thrownException;
			
		}
		return alertUrl;
	}
	/**
	 * End : Modified for including the alerts for sweep message generation as per
	 * James suggestion for Mantis 1197 by sutheendran Balaji A on
	 * 16-July-2010
	 */

	/**
	 * Retreives the MovementAlert for given movementId and alertStage.
	 * 
	 * @param hostId
	 * @param entityId
	 * @param movementId
	 * @param alertStage
	 * @param status
	 * @return
	 * @throws SwtException
	 */
	public Collection getMovementAlert(String hostId, String entityId,
			Long movementId, String alertStage, String status)
			throws SwtException {
		Collection collSweepAlert = new ArrayList();
		Criteria criteria = null;
		Session session = null;
		try {
			/*Criteria queryMovementAlert = getSession().createCriteria(
					SweepAlert.class).add(
					Expression.like("id.hostId", hostId, MatchMode.EXACT)).add(
					Expression.like("id.entityId", entityId, MatchMode.EXACT))
					.add(Expression.like("sweepId", movementId)).add(
							Expression.like("alertStage", alertStage,
									MatchMode.EXACT)).add(
							Expression.like("status", status));*/


			session = getHibernateTemplate().getSessionFactory().openSession();

			criteria = session.createCriteria(SweepAlert.class).add(
					Expression.like("id.hostId", hostId)).add(
					Expression.like("id.entityId", entityId)).
					add(Expression.like("sweepId", movementId)).
					add(Expression.like("alertStage", alertStage)).
					add(Expression.like("status", "status"));
			// End : Mantis 1615 Added by Nithiyananthan on 02-02-2012
			// get the result from criteria
			collSweepAlert =  (List) criteria.list();


		} catch (Exception exception) {
			log.debug("Problem in executing hibernate query;");
			exception.printStackTrace();
			throw SwtErrorHandler.getInstance().handleException(exception,
					"getMovementAlert", SweepAlertDAOHibernate.class);
		}finally {
			JDBCCloser.close(session);
		}
		return collSweepAlert;
	}

	/**
	 * Just creates a row in P_SWEEP_ALERT table. AlertId gets generated by
	 * AlertSequemceGenerator class
	 * 
	 * @param sweepAlert
	 * @throws SwtException
	 */
	public void createMovementAlert(SweepAlert sweepAlert) throws SwtException {
		try {
			getHibernateTemplate().save(sweepAlert);
			getHibernateTemplate().flush();
		} catch (Exception exception) {
			log.debug("Problem in executing hibernate query;");
			exception.printStackTrace();
			throw SwtErrorHandler.getInstance().handleException(exception,
					"createMovementAlert", SweepAlertDAOHibernate.class);
		}
	}

	/**
	 * Method is used to update movement alert with given stage.
	 * 
	 * @param sweepAlert
	 * @throws SwtException
	 */
	public void updateMovementAlert(SweepAlert sweepAlert) throws SwtException {
		getHibernateTemplate().update(sweepAlert);
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see org.swallow.work.dao.SweepAlertDAO#getSweepCurrency(java.lang.Long)
	 */
	public String getSweepCurrency(Long sweepId) throws SwtException {
		String hostId = CacheManager.getInstance().getHostId();
		List currency = getHibernateTemplate()
				.find(
						"select s.currencyCode from Sweep s where s.id.hostId=?0 and s.id.sweepId=?1",
						new Object[] { hostId, sweepId });
		/* START: Code fixed to avoid IndexOutOfBoundException, 27-SEP-2007 */
		String sweepCurrency = SwtConstants.EMPTY_STRING;
		if (currency != null && currency.size() != 0) {
			sweepCurrency = currency.get(0).toString();
		}
		return sweepCurrency;
		/* END: Code fixed to avoid IndexOutOfBoundException, 27-SEP-2007 */
	}

	/*
	 * START : Code Added for providing service used in defect number 223 on
	 * 25/04/2007
	 */

	/**
	 * This method deletes the given MovementAlert object from the database.
	 * 
	 * @param SweepAlert
	 * @throws SwtException
	 */
	public void deleteMovementAlert(SweepAlert movementAlert)
			throws SwtException {
		Session session = null;
		Transaction tx = null;
		SwtInterceptor interceptor = null;
		try {
			interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");
			session = getHibernateTemplate().getSessionFactory().withOptions().interceptor(interceptor).openSession();
			tx = session.beginTransaction();
			session.delete(movementAlert);
			tx.commit();
		} catch (Exception exception) {
			log.debug("Problem in executing hibernate query;");
			exception.printStackTrace();
			throw SwtErrorHandler.getInstance().handleException(exception,
					"createMovementAlert", SweepAlertDAOHibernate.class);
		} finally {
			JDBCCloser.close(session);
		}
	}

	/*
	 * END: Code Added for providing service used in defect number 223 on
	 * 25/04/2007
	 */
}
