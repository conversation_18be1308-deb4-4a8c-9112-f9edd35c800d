/*
 * @(#)AccountMonitorNewDAOHibernate.java 1.0
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.work.dao.hibernate;

import java.sql.CallableStatement;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.Iterator;
import java.util.List;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.dao.DataAccessResourceFailureException;

import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.maintenance.model.AcctMaintenance;
import org.swallow.util.JDBCCloser;
import org.swallow.util.SwtConstants;
import org.swallow.util.SwtUtil;
import org.swallow.work.dao.AccountMonitorNewDAO;
import org.swallow.work.model.AccountMonitorNew;
import org.swallow.work.model.AccountMonitorTotalCacheValue;
import org.swallow.work.service.AccountMonitorNewDetailVO;
import org.springframework.context.annotation.Lazy;
import org.springframework.orm.hibernate5.support.HibernateDaoSupport;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.stereotype.Repository;
import org.springframework.beans.factory.annotation.Autowired;
import org.hibernate.HibernateException;
import org.hibernate.SessionFactory;
import org.hibernate.Transaction;
import org.hibernate.Session;




/**
 * <pre>
 * DAO layer for Account Monitor screen. This class performs 
 * following tasks 
 *  - Gets account details and linked account list
 *  - Updates sum flag of accounts
 *  - Updates loro flag for loro accounts
 *  - Account monitor details
 * </pre>
 */
@Repository ("acctmonitorNewDAO")
@Transactional
public class AccountMonitorNewDAOHibernate extends HibernateDaoSupport
		implements AccountMonitorNewDAO {
	public AccountMonitorNewDAOHibernate(@Lazy SessionFactory sessionfactory) {
	    setSessionFactory(sessionfactory);
	}

	/**
	 * Instance of Log object
	 */
	private final Log log = LogFactory
			.getLog(AccountMonitorNewDAOHibernate.class);

	/**
	 * This method gets account details as well as linked account list for the
	 * given account id and returns the same
	 * 
	 * @param String
	 *            hostId
	 * @param String
	 *            entityId
	 * @param String
	 *            currencyCode
	 * @param String
	 *            accountId
	 * @return Collection<AcctMaintenance>
	 * @throws SwtException
	 */
	@SuppressWarnings("unchecked")
	public Collection<AcctMaintenance> getAccountAndLinkedAccountsList(
			String hostId, String entityId, String currencyCode,
			String accountId) throws SwtException {
		// To build query and get account details as well as linked accounts
		StringBuffer accountListQuery = null;
		// Account details
		List<AcctMaintenance> list = null;

		try {
			// log debug message
			log.debug(this.getClass().getName()
					+ " - [ getAccountAndLinkedAccountsList ] - Entry ");
			// Build a query to get account details and linked accounts
			accountListQuery = new StringBuffer().append(
					"SELECT a from AcctMaintenance a ").append(
					"WHERE a.id.hostId = ?0 AND a.id.entityId = ?1 ").append(
					"AND a.currcode = ?2 AND a.acctstatusflg IN ").append(
					"('O','B') AND ( a.id.accountId = ?3 OR a.linkAccID = ?4 )");

			// Get account details from DB using HibernateTemplate, which is
			// pre-initialized with the SessionFactory and Executes a query for
			// persistent instances
			list = (List<AcctMaintenance> ) getHibernateTemplate().find(
					accountListQuery.toString(),
					new Object[] { hostId, entityId, currencyCode, accountId,
							accountId });
			return list;
		} catch (Exception ex) {
			// log error message
			log.error(this.getClass().getName()
					+ " - [getAccountAndLinkedAccountsList] - Exception - "
					+ ex.getMessage());
			// Re-throw an exception
			throw SwtErrorHandler.getInstance().handleException(ex,
					"getAccountAndLinkedAccountsList",
					AccountMonitorNewDAOHibernate.class);
		} finally {
			// nullify object(s)
			accountListQuery = null;
			// log debug message
			log.debug(this.getClass().getName()
					+ " - [ getAccountAndLinkedAccountsList ] - Exit ");
		}

	}

	/**
	 * This method updates monitor sum flag of accounts. Flag denotes whether to
	 * include the account for sum calculation
	 * 
	 * @param Collection
	 *            <AcctMaintenance> colAcctMaintanance
	 * @throws SwtException
	 */
	public void updateMonitorSum(Collection<AcctMaintenance> colAcctMaintanance)
			throws SwtException {
		// Iterator for update all records one by one
		Iterator<AcctMaintenance> itrAcctMaintenance = null;

		try {
			// log debug message
			log.debug(this.getClass().getName()
					+ " - [ updateMonitorSum ] - Entry ");

			// If account maintenance details available update monitor sum flag
			if (colAcctMaintanance != null) {
				// Iterate through account maintenance collection and update
				// monitor sum flag of each account
				itrAcctMaintenance = colAcctMaintanance.iterator();
				while (itrAcctMaintenance.hasNext()) {
					// Update monitor sum flag of a account
					getHibernateTemplate().update(itrAcctMaintenance.next());
				}
			}
			/*
			 * Hibernate's Flush all pending saves, updates and deletes to the
			 * database.
			 */
			getHibernateTemplate().flush();
		} catch (Exception ex) {
			// log error message
			log.error(this.getClass().getName()
					+ " - [updateMonitorSum] - Exception - " + ex.getMessage());
			// Re-throw an exception
			throw SwtErrorHandler.getInstance().handleException(ex,
					"updateMonitorSum", AccountMonitorNewDAOHibernate.class);
		} finally {
			// nullify object(s)
			itrAcctMaintenance = null;
			// log debug message
			log.debug(this.getClass().getName()
					+ " - [ updateMonitorSum ] - Exit ");
		}
	}

	/**
	 * This method updates Loro_To_Predicted flag for the selected account. If
	 * the it is Loro account, flag will be updated to 'Y' otherwise it is 'N'
	 * 
	 * @param AcctMaintenance
	 *            accountMaintenance
	 * @throws SwtException
	 */
	public void updateLoroToPredictedFlag(AcctMaintenance accountMaintenance)
			throws SwtException {
		try {
			// log debug message
			log.debug(this.getClass().getName()
					+ " - [ updateLoroToPredictedFlag ] - Entry ");
			/*
			 * If the AcctMaintenance object is not null and if it is Loro
			 * account, flag will be updated to 'Y' otherwise it is 'N'
			 */
			if (accountMaintenance != null) {
				// Update Loro flag
				getHibernateTemplate().update(accountMaintenance);
			}
			/*
			 * Hibernate's Flush all pending saves, updates and deletes to the
			 * database.
			 */
			getHibernateTemplate().flush();
		} catch (Exception ex) {
			// log error message
			log.error(this.getClass().getName()
					+ " - [updateLoroToPredictedFlag] - Exception - "
					+ ex.getMessage());
			// Re-throw an exception
			throw SwtErrorHandler.getInstance().handleException(ex,
					"updateLoroToPredictedFlag",
					AccountMonitorNewDAOHibernate.class);
		} finally {
			// log debug message
			log.debug(this.getClass().getName()
					+ " - [ updateLoroToPredictedFlag ] - Exit ");
		}

	}

	/**
	 * 
	 * <pre>
	 * This method invokes stored procedure PKG_ACCOUNT_MONITOR.SP_ACCOUNT_MONITOR_JOB
	 * and gets below mentioned details
	 *  - Account monitor grid data
	 *  - Totals grid data (Sum value of Predicted balances, Loro balances,
	 *    Unsettled balances,Unexpected balances, External balances)
	 *  - Start of day balance
	 *  - No of unexpected movements
	 *  - Holiday flag for tabs
	 * </pre>
	 * 
	 * @param String
	 *            hostId
	 * @param String
	 *            entityId
	 * @param String
	 *            currencyCode
	 * @param String
	 *            accountType
	 * @param Date
	 *            dateParam
	 * @param boolean
	 *            isCacheSearch
	 * @param String
	 *            applyCurrencyThreshold
	 * @param String
	 *            accountClass
	 * @param String
	 *            hideZeroBalances
	 * @param String
	 *            roleId
	 * @return AccountMonitorNewDetailVO
	 * @throws SwtException
	 */

	@SuppressWarnings("unchecked")
	public AccountMonitorNewDetailVO getAllBalancesUsingStoredProc(
			String hostId, String entityId, String currencyCode,
			String accountType, Date dateParam, boolean isCacheSearch,
			String applyCurrencyThreshold, String accountClassParm,
			String hideZeroBalances, String roleId) throws SwtException {
		// Hibernate Session
		Session session = null;
		// DB Connection object executes SP
		Connection conn = null;
		// CallableStatement object invokes SP and gets AccountMonitor details.
		CallableStatement cstmtAccount = null;
		// To get AccountMonitor details from SP.
		ResultSet rsData = null;
		// To get values total grid from database
		ResultSet rsTotal = null;
		// To get start of day balance and no of unexpected movements
		ResultSet rsSODBalUnexpectedMovment = null;
		// To get tab holiday flag (To differentiate working days from holiday)
		ResultSet rsTabHolidayFlag = null;
		// Tab holiday flag (N - Working Day, Y - Holiday)
		StringBuffer tabHolidayFlag = null;
		// Account Monitor Details List
		Collection<AccountMonitorNew> colAccountMonitor = null;
		// Value bean to be returned, which holds all data
		AccountMonitorNewDetailVO acctMonitorVO = null;
		// To store Account monitor details (grid data)
		AccountMonitorNew acctMonitor = null;
		// To holds Totals value
		AccountMonitorTotalCacheValue acctMonitorTotal = null;
		// Flag denotes whether the account is included in total or not
		String sumFlag = null;
		// Flag denotes whether the data will be fetched from cache or not. If
		// not data will be calculated again
		String cacheRefreshFlag = null;
		// To get Total of Predicted Balance
		double predictedBalTotal = 0;
		// To get Total of Unsettled Balance
		double unsettledBalTotal = 0;
		// To get Total of Unexpected Balance
		double unexpectedBalTotal = 0;
		// To get Total of Loro Balance
		double loroBalTotal = 0;
		// To get Total of External Balance
		double externalBalTotal = 0;
		// To get Total of unexpected movements
		double unexpectedMovementTotal = 0;
		// Accumulated SOD By Currency
		double accumulatedSODByCurrency = 0;
		
		String highlightFlagSOD  = null;

		try {
			// log debug message
			log.debug(this.getClass().getName()
					+ " - [ getAllBalancesUsingStoredProc ] - Entry ");
			// If cache search is enabled, it gets previously calculated data
			// (from cache), otherwise calculation will be made again
			cacheRefreshFlag = isCacheSearch ? new String("A")
					: new String("O");

			/* Opens a Hibernate SessionFactory to connect with database */
			session = getHibernateTemplate().getSessionFactory().openSession();
			/* Using the sessionFactory to open/get a DB connection */
			conn = SwtUtil.connection(session);
			// Invoke SP to get account monitor details
			/* Passing the Query to execute Stored procedure */

			cstmtAccount = conn
					.prepareCall("{call PKG_ACCOUNT_MONITOR.sp_Account_monitor_job(?,?,?,?,?,?,?,?,?,?,?,?,?)}");
			// Set parameters
			cstmtAccount.setString(1, hostId);
			cstmtAccount.setString(2, entityId);
			cstmtAccount.setString(3, currencyCode);
			cstmtAccount.setString(4, accountType);
			cstmtAccount.setDate(5, SwtUtil.truncateDateTime(dateParam));
			cstmtAccount.setString(6, roleId);
			cstmtAccount.setString(7, applyCurrencyThreshold);
			// Register out parameters to get result from SP
			cstmtAccount
					.registerOutParameter(8, oracle.jdbc.OracleTypes.CURSOR);
			cstmtAccount
					.registerOutParameter(9, oracle.jdbc.OracleTypes.CURSOR);
			cstmtAccount.registerOutParameter(10,
					oracle.jdbc.OracleTypes.CURSOR);
			cstmtAccount.registerOutParameter(11,
					oracle.jdbc.OracleTypes.CURSOR);
			// Set parameters
			cstmtAccount.setString(12, hideZeroBalances);
			cstmtAccount.setString(13, accountClassParm);
			/* Execute the SP to get Account details */
			cstmtAccount.execute();
			// The returned cursor is TypeCasted to a ResultSet
			// Account details
			rsData = (ResultSet) cstmtAccount.getObject(8);
			// Total (Sum of account balance, based on include and exclude flag)
			rsTotal = (ResultSet) cstmtAccount.getObject(9);
			// Start of day balance and no of unexpected movements
			rsSODBalUnexpectedMovment = (ResultSet) cstmtAccount.getObject(10);
			// Tab holiday flag
			rsTabHolidayFlag = (ResultSet) cstmtAccount.getObject(11);

			/* Assigning a new ArrayList to Collection Object */

			colAccountMonitor = new ArrayList<AccountMonitorNew>();
			/* Creating Object for AccountMonitorNewDetailVO */
			acctMonitorVO = new AccountMonitorNewDetailVO();

			// Iterate through tab holiday RS and get holiday flag
			if (rsTabHolidayFlag != null) {
				// Initialize StringBuffer to store tab holiday flag
				tabHolidayFlag = new StringBuffer();
				while (rsTabHolidayFlag.next()) {
					tabHolidayFlag.append(rsTabHolidayFlag.getString(1));

				}
				acctMonitorVO.setTabFlag(tabHolidayFlag.toString());
			}
			if (rsData != null) {
				// Iterate through data RS, get account monitor details and put
				// them in a collection
				while (rsData.next()) {
					// Initialize new account monitor bean to hold account
					// details
					acctMonitor = new AccountMonitorNew();
					// Get values from result set and set them in the
					// corresponding properties in the account monitor bean
					acctMonitor.setCurrencyCode(rsData.getString(1));
					acctMonitor.setAccountId(rsData.getString(2));
					acctMonitor.setAccountName(rsData.getString(3));
					acctMonitor.setPredictedBal(rsData.getDouble(4));
					acctMonitor.setUnsettledBal(rsData.getDouble(5));
					acctMonitor.setUnexpectedBal(rsData.getDouble(6));
					acctMonitor.setLoroBal(rsData.getDouble(7));
					acctMonitor.setExternalBal(rsData.getDouble(8));
					//SET SCENARIO HIGHLIGHTED
					acctMonitor.setScenarioHighlighted(rsData.getString("SCENARIO_HIGHLIGHTING"));
					sumFlag = rsData.getString(9);

					// Get summable flag
					if (sumFlag.contains("|")) {
						// Flag indicates, whether this account is included in
						// total or not
						acctMonitor
								.setSummable(sumFlag.substring(
										sumFlag.indexOf("|") + 1,
										sumFlag.length()).equals(
										SwtConstants.NO) ? SwtConstants.STR_FALSE
										: SwtConstants.STR_TRUE);
						// get the sum Flag
						sumFlag = sumFlag.substring(0, sumFlag.indexOf("|"));
					} else if (sumFlag.equals(SwtConstants.CUTOFF_SUM_FLAG)) {
						// Exceeds cut-off time, so do not include the account
						// in total
						acctMonitor.setSummable(SwtConstants.STR_FALSE);
					}

					acctMonitor.setSum(sumFlag);
					acctMonitor.setAccountClass(rsData.getString(10));
					acctMonitor.setLoroToPredictedFlag(rsData.getString(11));
					acctMonitor.setIconIndicatorFlag(rsData.getString(12));
					acctMonitor.setColorFlag(rsData.getString(13));
					
					
					acctMonitor.setIncludeLoroInPredictedIndicator("N".equalsIgnoreCase(rsData.getString("predict_flag"))?"":rsData.getString("predict_flag"));
					acctMonitor.setIncludePredictedInLoroIndicator("N".equalsIgnoreCase(rsData.getString("loro_curr_flag"))?"":rsData.getString("loro_curr_flag"));
					acctMonitor.setIncludeLoroInPredictedColor("L".equalsIgnoreCase(rsData.getString("predict_color"))?"#DDDDDD":"D".equalsIgnoreCase(rsData.getString("predict_color"))?"#AAAAAA":"");
					acctMonitor.setIncludePredictedInLoroColor("L".equalsIgnoreCase(rsData.getString("loro_curr_color"))?"#DDDDDD":"D".equalsIgnoreCase(rsData.getString("loro_curr_color"))?"#AAAAAA":"");
					
					
					// Code MOdified By Chinniah on 28-May-2012 for Mantis
					// 1933:Account Monitor: Screen hangs with "Function
					// returned without
					// value" if closed accounts
					acctMonitor.setAccountStatus(rsData.getString(14));
					// Calculate string values of some properties and set the
					// same to corresponding string variables in the same
					// bean(account monitor)
					acctMonitor.setPredictedBalAsString(SwtUtil
							.formatCurrencyWithoutDecimals(acctMonitor
									.getCurrencyCode(), acctMonitor
									.getPredictedBal()));
					acctMonitor.setUnsettledBalAsString(SwtUtil
							.formatCurrencyWithoutDecimals(acctMonitor
									.getCurrencyCode(), acctMonitor
									.getUnsettledBal()));
					acctMonitor.setUnexpectedBalAsString(SwtUtil
							.formatCurrencyWithoutDecimals(acctMonitor
									.getCurrencyCode(), acctMonitor
									.getUnexpectedBal()));
					acctMonitor.setLoroBalAsString(SwtUtil
							.formatCurrencyWithoutDecimals(acctMonitor
									.getCurrencyCode(), acctMonitor
									.getLoroBal()));
					acctMonitor.setExternalBalAsString(SwtUtil
							.formatCurrencyWithoutDecimals(acctMonitor
									.getCurrencyCode(), acctMonitor
									.getExternalBal()));

					// Put account monitor details bean in a collection
					colAccountMonitor.add(acctMonitor);
				}
			}
			// Initialize object to hold total data, start of day balance
			// and no of unexpected movement
			acctMonitorTotal = new AccountMonitorTotalCacheValue();

			// Total can be calculated only there should be data as well as
			// single currency. For all currencies option totals can not be
			// calculated. So check for data availability and currency
			if ((colAccountMonitor != null) && (colAccountMonitor.size() > 0)
					&& !currencyCode.equalsIgnoreCase(SwtConstants.ALL_VALUE)) {

				// Get totals from result set
				if (rsTotal != null && rsTotal.next()) {
					// Get totals
					loroBalTotal = rsTotal.getDouble(2);
					predictedBalTotal = rsTotal.getDouble(3);
					unsettledBalTotal = rsTotal.getDouble(4);
					unexpectedBalTotal = rsTotal.getDouble(5);
					externalBalTotal = rsTotal.getDouble(6);
				}

				// Get start of day balance and total no of unexpected movement
				// from result set
				if (rsSODBalUnexpectedMovment != null
						&& rsSODBalUnexpectedMovment.next()) {
					accumulatedSODByCurrency = rsSODBalUnexpectedMovment
							.getDouble(1);
					unexpectedMovementTotal = rsSODBalUnexpectedMovment
							.getDouble(2);
					
					highlightFlagSOD = rsSODBalUnexpectedMovment
					.getString(3);
				}

				// Set properties
				acctMonitorTotal.setPredictedBalTotal(predictedBalTotal);
				acctMonitorTotal.setUnsettledBalTotal(unsettledBalTotal);
				acctMonitorTotal.setUnexpectedBalTotal(unexpectedBalTotal);
				acctMonitorTotal.setLoroBalTotal(loroBalTotal);
				acctMonitorTotal.setExternalBalTotal(externalBalTotal);
				acctMonitorTotal
						.setOpenUnexpectedBalTotal(unexpectedMovementTotal);
				acctMonitorTotal.setAccumulatedSODBal(accumulatedSODByCurrency);
				acctMonitorTotal.setScenarioHighlighted(highlightFlagSOD);

				// Calculate string values of total properties and set the
				// same to corresponding string variables in the same
				// bean(account monitor total)
				acctMonitorTotal.setPredictedBalTotalAsString(SwtUtil
						.formatCurrencyWithoutDecimals(currencyCode,
								acctMonitorTotal.getPredictedBalTotal()));
				acctMonitorTotal.setUnsettledBalTotalAsString(SwtUtil
						.formatCurrencyWithoutDecimals(currencyCode,
								acctMonitorTotal.getUnsettledBalTotal()));
				acctMonitorTotal.setUnexpectedBalTotalAsString(SwtUtil
						.formatCurrencyWithoutDecimals(currencyCode,
								acctMonitorTotal.getUnexpectedBalTotal()));
				acctMonitorTotal.setLoroBalTotalAsString(SwtUtil
						.formatCurrencyWithoutDecimals(currencyCode,
								acctMonitorTotal.getLoroBalTotal()));
				acctMonitorTotal.setExternalBalTotalAsString(SwtUtil
						.formatCurrencyWithoutDecimals(currencyCode,
								acctMonitorTotal.getExternalBalTotal()));
				acctMonitorTotal.setOpenUnexpectedBalTotalAsString(SwtUtil
						.formatCurrencyWithoutDecimals(currencyCode,
								acctMonitorTotal.getOpenUnexpectedBalTotal()));
				acctMonitorTotal.setAccumulatedSODBalAsString(SwtUtil
						.formatCurrencyWithoutDecimals(currencyCode,
								acctMonitorTotal.getAccumulatedSODBal()));
				acctMonitorTotal.setScenarioHighlighted(highlightFlagSOD);
			} else {
				// Data not found or user selected all currencies, so totals can
				// not be calculated. Set default value '0'
				acctMonitorTotal.setPredictedBalTotal(predictedBalTotal);
				acctMonitorTotal.setUnsettledBalTotal(unsettledBalTotal);
				acctMonitorTotal.setUnexpectedBalTotal(unexpectedBalTotal);
				acctMonitorTotal.setLoroBalTotal(loroBalTotal);
				acctMonitorTotal.setExternalBalTotal(externalBalTotal);
				acctMonitorTotal
						.setOpenUnexpectedBalTotal(unexpectedMovementTotal);
				acctMonitorTotal.setAccumulatedSODBal(accumulatedSODByCurrency);
				// Data not found or user selected all currencies, so totals can
				// not be calculated. Set empty properties
				acctMonitorTotal.setPredictedBalTotalAsString("");
				acctMonitorTotal.setUnsettledBalTotalAsString("");
				acctMonitorTotal.setUnexpectedBalTotalAsString("");
				acctMonitorTotal.setLoroBalTotalAsString("");
				acctMonitorTotal.setExternalBalTotalAsString("");
				acctMonitorTotal.setOpenUnexpectedBalTotalAsString("");
				acctMonitorTotal.setAccumulatedSODBalAsString("");
				acctMonitorTotal.setScenarioHighlighted(highlightFlagSOD);
			}
			// Set account monitor (summary) data and totals
			acctMonitorVO.setSummaryDetails(colAccountMonitor);
			acctMonitorVO.setTotalDetails(acctMonitorTotal);
			return acctMonitorVO;
		} catch (DataAccessResourceFailureException dataAccessException) {
			// log error message
			log
					.error(this.getClass().getName()
							+ " - [getAllBalancesUsingStoredProc] - DataAccessResourceFailureException - "
							+ dataAccessException.getMessage());
			throw SwtErrorHandler.getInstance().handleException(
					dataAccessException, "getAllBalancesUsingStoredProc",
					AccountMonitorNewDAOHibernate.class);
		} catch (IllegalStateException illegalStateException) {
			// log error message
			log
					.error(this.getClass().getName()
							+ " - [getAllBalancesUsingStoredProc] - IllegalStateException - "
							+ illegalStateException.getMessage());
			throw SwtErrorHandler.getInstance().handleException(
					illegalStateException, "getAllBalancesUsingStoredProc",
					AccountMonitorNewDAOHibernate.class);
		} catch (HibernateException hibernateException) {
			// log error message
			log
					.error(this.getClass().getName()
							+ " - [getAllBalancesUsingStoredProc] - HibernateException - "
							+ hibernateException.getMessage());
			throw SwtErrorHandler.getInstance().handleException(
					hibernateException, "getAllBalancesUsingStoredProc",
					AccountMonitorNewDAOHibernate.class);
		} catch (SQLException sqlException) {
			// log error message
			log.error(this.getClass().getName()
					+ " - [getAllBalancesUsingStoredProc] - SQLException - "
					+ sqlException.getMessage());
			throw SwtErrorHandler.getInstance().handleException(sqlException,
					"getAllBalancesUsingStoredProc",
					AccountMonitorNewDAOHibernate.class);
		} catch (SwtException swtException) {
			// log error message
			log.error(this.getClass().getName()
					+ " - [getAllBalancesUsingStoredProc] - SWTException - "
					+ swtException.getMessage());
			throw SwtErrorHandler.getInstance().handleException(swtException,
					"getAllBalancesUsingStoredProc",
					AccountMonitorNewDAOHibernate.class);
		} catch (Exception ex) {
			// log error message
			log.error(this.getClass().getName()
					+ " - [getAllBalancesUsingStoredProc] - Exception - "
					+ ex.getMessage());
			throw SwtErrorHandler.getInstance().handleException(ex,
					"getAllBalancesUsingStoredProc",
					AccountMonitorNewDAOHibernate.class);
		} finally {
			// Edited by KaisBS for mantis 1745 : introducing the implementation of the JDBCCloser class
			SwtException thrownException = null;
			SQLException sqlException = null;

			sqlException = JDBCCloser.close(rsData, rsTotal, rsSODBalUnexpectedMovment, rsTabHolidayFlag);
			if (sqlException!=null)
				thrownException = new SwtException(sqlException.getMessage());

			Object[] exceptions = JDBCCloser.close(null,cstmtAccount, conn, session);

			if (thrownException == null && exceptions[0] != null)
				thrownException = new SwtException(((SQLException) exceptions[0]).getMessage());

			if (thrownException == null && exceptions[1] !=null) 
				thrownException = new SwtException(((HibernateException) exceptions[1]).getMessage());

			if (thrownException != null)
				throw thrownException;

			// nullify objects
			session = null;
			conn = null;
			cstmtAccount = null;
			rsData = null;
			rsTotal = null;
			rsSODBalUnexpectedMovment = null;
			rsTabHolidayFlag = null;
			tabHolidayFlag = null;
			colAccountMonitor = null;
			acctMonitor = null;
			acctMonitorTotal = null;
			sumFlag = null;
			cacheRefreshFlag = null;
			log.debug(this.getClass().getName()
					+ " - [ getAllBalancesUsingStoredProc ] - Exit ");
		}
	}
}
