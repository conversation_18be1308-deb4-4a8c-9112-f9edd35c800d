/*
 * @(#)InputExceptionsDataDAOHibernate.java 1.0 23/08/06
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.work.dao.hibernate;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;




import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.util.JDBCCloser;
import org.swallow.util.OpTimer;
import org.swallow.util.PropertiesFileLoader;
import org.swallow.util.SwtConstants;
import org.swallow.util.SwtUtil;
import org.swallow.work.dao.InputExceptionsDataDAO;
import org.springframework.context.annotation.Lazy;
import org.springframework.orm.hibernate5.support.HibernateDaoSupport;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.stereotype.Repository;
import org.springframework.beans.factory.annotation.Autowired;
import org.hibernate.HibernateException;
import org.hibernate.SessionFactory;
import org.hibernate.Transaction;
import org.hibernate.Session;

/*
 * This class is used to get counts of input exceptions from data base.
 */
@Repository ("inputExceptionsDataDAO")
@Transactional
public class InputExceptionsDataDAOHibernate extends HibernateDaoSupport
		implements InputExceptionsDataDAO {
	public InputExceptionsDataDAOHibernate(@Lazy SessionFactory sessionfactory) {
		setSessionFactory(sessionfactory);
	}


	/**
	 * Initializing logger object for this class
	 */
	private final Log log = LogFactory
			.getLog(InputExceptionsDataDAOHibernate.class);

	/**
	 * Method to get count of input exceptions from data base
	 *
	 * @param fromDate
	 * @param toDate
	 * @param opTimer
	 * @param dateFormat
	 * @return List
	 * @throws SwtException
	 */
	@SuppressWarnings("unchecked")
	public List getInterfaceMessageList(String fromDate, String toDate,
										OpTimer opTimer, String dateFormat, boolean fromPCM) throws SwtException {
		/* Method's local variable declaration */
		/* Variable Declaration for interfaceMessageList. */
		List interfaceMessageList = null;
		/* Variable Declaration for time. */
		String time = null;
		/*
		 * Start: Code added for Mantis 1446(1053_STL_090) by venkat on
		 * 20-Oct-11.
		 */
		/* Variable Declaration for temporaryTableName. */
		String temporaryTableName = null;
		/* Variable Declaration for rs. */
		ResultSet awaitingStatusResultset = null;
		/* Variable Declaration for conn. */
		Connection conn = null;
		/* Variable Declaration for pstat. */
		PreparedStatement pstat = null;
		/* Variable Declaration for query. */
		StringBuffer query = null;
		/* Variable Declaration for session. */
		Session session = null;
		/* End: Code added for Mantis 1446(1053_STL_090) by venkat on 20-Oct-11. */
		try {
			log.debug(this.getClass().getName()
					  + " - [getInterfaceMessageList] - " + "Entry");
			opTimer.start("fetch-data");

			/*
			 * This returning list contains count of the
			 * accepted,Rejected,Submitted,Suppressed status.
			 */


			java.sql.Date startDate=null;
			java.sql.Date endDate=null;
			SimpleDateFormat formatter = new SimpleDateFormat(dateFormat);
			java.util.Date startDt = formatter.parse(fromDate);
			java.util.Date endDt = formatter.parse(toDate);
			startDate=SwtUtil.truncateDateTime(startDt);
			endDate=SwtUtil.truncateDateTime(new Date(endDt.getTime() + 86400000));
			interfaceMessageList = getHibernateTemplate()
					.find(	"select e.messageType,e.status, count(*) from InputExceptionsDataModel e where  e.startTime >= ?0" +
							  " and"+
							  " e.startTime < ?1 "+
							  " group by e.messageType,e.status order by e.messageType", new java.sql.Date[]{startDate,endDate});

			/*
			 * Start: Code added for Mantis 1446(1053_STL_090) by venkat on
			 * 20-Oct-11.
			 */
			/* get the MESSAGEDETAILSTEMP name from property file. */
			temporaryTableName = PropertiesFileLoader
					.getInstance()
					.getPropertiesValue(
							SwtConstants.PROPERTY_INTERFACES_TABLE_TEMPORARY_NAME);
			/*
			 * Frame the query to get the count of Awaiting status from
			 * I_MESSAGEDETAILSTEMP table.
			 */
			if (fromPCM) {
				query = new StringBuffer("select MESSAGES01,");
				query.append(SwtConstants.INPUT_EXCEPTIONS_AWAITING_STATUS);
				query.append(" as status,count(*) from ");
				query.append(temporaryTableName);
				query.append(" where (messagestatus < 'new' OR messagestatus > 'new') AND STARTTIME >= ?");
				query.append("  AND STARTTIME  < ? ");
				query.append("group by MESSAGES01");

			} else {
				query = new StringBuffer("select messagetype ,");
				query.append(SwtConstants.INPUT_EXCEPTIONS_AWAITING_STATUS);
				query.append(" as status,count(*) from ");
				query.append(temporaryTableName);
				query.append(" where (messagestatus < 'new' OR messagestatus > 'new') AND STARTTIME >= ?");
				query.append("  AND STARTTIME  < ? ");
				query.append("group by messagetype");

			}

			/* get the session object. */
			session = getHibernateTemplate().getSessionFactory().openSession();
			/* get the connection object. */
			conn = SwtUtil.connection(session);
			/* get the PreparedStatement object. */
			pstat = conn.prepareStatement(query.toString());
			pstat.setDate(1, startDate);
			pstat.setDate(2,endDate);

			/* Execute the prepare statement. */
			awaitingStatusResultset = pstat.executeQuery();
			/* iterate the Result Set and set the object in list. */
			while (awaitingStatusResultset.next()) {
				Object[] awaitingStatusObj = new Object[3];
				awaitingStatusObj[0] = awaitingStatusResultset.getString(1);
				awaitingStatusObj[1] = awaitingStatusResultset.getString(2);
				awaitingStatusObj[2] = awaitingStatusResultset.getString(3);
				interfaceMessageList.add(awaitingStatusObj);
			}
			/*
			 * End: Code added for Mantis 1446(1053_STL_090) by venkat on
			 * 20-Oct-11.
			 */
			opTimer.stop("fetch-data");

		} catch (Exception exp) {
			log
					.error(this.getClass().getName()
						   + " - Exception Catched in [getInterfaceMessageList] method : - "
						   + exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp,
					"getInterfaceMessageList",
					InputExceptionsDataDAOHibernate.class);
		} finally {
			log.debug(this.getClass().getName()
					  + " - [getInterfaceMessageList] - " + "Exit");

			// Edited by KaisBS for mantis 1745 : introducing the implementation of the JDBCCloser class
			SwtException thrownException = null;
			// Modified for TUN_017, kill the session instead of the connection
			Object[] exceptions = JDBCCloser.close(awaitingStatusResultset, pstat, conn, session);

			if (exceptions[0] != null)
				thrownException = SwtErrorHandler.getInstance().handleException((SQLException) exceptions[0], "getInterfaceMessageList",
						InputExceptionsDataDAOHibernate.class);

			if (thrownException == null && exceptions[1] !=null)
				thrownException = SwtErrorHandler.getInstance().handleException((HibernateException) exceptions[1], "getInterfaceMessageList",
						InputExceptionsDataDAOHibernate.class);

			if(thrownException!=null)
				throw thrownException;

			/* null the objects created already. */

			temporaryTableName = null;
			query = null;
			session = null;
		}
		return interfaceMessageList;
	}
}