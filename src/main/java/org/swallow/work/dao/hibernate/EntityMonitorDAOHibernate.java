/*
 * @(#)EntityMonitorDAOHibernate.java 1.0 12/02/11
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */

package org.swallow.work.dao.hibernate;

import java.math.BigDecimal;
import java.sql.CallableStatement;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.List;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.dao.DataAccessResourceFailureException;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;
import org.swallow.control.model.EntityAccess;
import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.maintenance.model.CurrencyGroup;
import org.swallow.maintenance.model.PersonalCurrency;
import org.swallow.util.CacheManager;
import org.swallow.util.JDBCCloser;
import org.swallow.util.OpTimer;
import org.swallow.util.SwtConstants;
import org.swallow.util.SwtInterceptor;
import org.swallow.util.SwtUtil;
import org.swallow.util.SystemFormats;
import org.swallow.work.dao.EntityMonitorDAO;
import org.swallow.work.model.PersonalCcyEntityMap;
import org.swallow.work.model.PersonalEntityList;
import org.swallow.work.model.PersonalEntityListExt;
import org.swallow.work.web.form.EntityRecord;
import org.hibernate.HibernateException;
import org.hibernate.Session;
import org.hibernate.SessionFactory;
import org.hibernate.Transaction;
import org.springframework.context.annotation.Lazy;
import org.springframework.orm.hibernate5.support.HibernateDaoSupport;
import org.springframework.stereotype.Repository;

/**
 * EntityMonitorDAOHibernate.java
 * 
 * Class that implements the EntityMonitorDAO and acts as DAO layer for all
 * database operations related to EntityMonitor<br>
 * This interface has methods that are used for accessing the persistent storage
 * such as database <br>
 * which helps client to create, retrieve and persists data to the Persistent
 * Object.<br>
 * 
 * <AUTHOR>
 * @date Feb 12, 2011
 */
@Repository ("entityMonitorDAO")
@Transactional
public class EntityMonitorDAOHibernate extends HibernateDaoSupport implements
		EntityMonitorDAO {
	/**
	 * Instance Of Log
	 */
	private static final Log log = LogFactory
			.getLog(EntityMonitorDAOHibernate.class);
	
	public EntityMonitorDAOHibernate(@Lazy SessionFactory sessionfactory){
	    setSessionFactory(sessionfactory);
	}

	/*
	 * Start:code modified by sudhakar for Mantis 1994 on
	 * 10-Jul-2012:Performance improvement in Entity Monitor and Currency
	 * Monitor
     * and Code modified by Vivekanandan A for Mantis 1991 on 09-07-2012
	 */

	/**
	 * Method to get get the predicted balances for for the period specified<br>
	 * This uses PKG_ENTITY_MONITOR.SP_ENTITY_MONITOR_JOB.<br>
	 * 
	 * This method helps getting all the balances with respect to the entities.<br>
	 * This method executes a stored procedure that will retrieve all the
	 * balances.<br>
	 * 
	 * @param OpTimer
	 *            opTimer
	 * @param String
	 *            currGrp
	 * @param String
	 *            userId
	 * @param Date
	 *            firstDate
	 * @param SystemFormats
	 *            format
	 * @param String
	 *            currencyCode
	 * @return EntityRecord object
	 * @throws SwtException
	 */
	@SuppressWarnings("unchecked")
	public EntityRecord getAllBalancesUsingStoredProc(OpTimer opTimer,
			String currGrp, String userId, Date startDate,
			SystemFormats format, String CurrencyCode,String entityOffsetTime) throws SwtException {

		// To get the hostId from cache manager
		String hostId = null;
		// Hibernate session to execute stored procedure
		Session session = null;
		// DB Connection object executes SP
		Connection conn = null;
		// CallableStatement object invokes SP and gets Entity monitor details.
		CallableStatement cstmt = null;
		// To hold entity monitor screenId
		String screenId = null;
		// ResultSet to hold monitor details and total balance
		ResultSet rsMonitorInfo = null;
		// To hold total predict balance for reporting currency
		HashMap<Integer, BigDecimal> dateTotal = null;
		// To hold map currency with entity records
		HashMap<String, ArrayList<EntityRecord>> mapCurrency = null;
		// Collection to hold entity & currency details
		Collection<EntityRecord> currentEntityColl = null;
		// To iterate entity & currency details
		Iterator<EntityRecord> currEntityItr = null;
		// Collection to hold total grid entity monitor details
		Collection<EntityRecord> totalCollection = null;
		// Collection to hold entity details
		Collection<EntityRecord> entityNewColl = null;
		// object to hold currency code details and sum of reporting currency
		// details
		EntityRecord entityMonitorRecord = null;
		// object to hold monitorRecord from db
		ArrayList<EntityRecord> monitorRecordList = null;
		// To get the column grid records from db
		EntityRecord monitorRecord = null;
		// Map used to map the entityid & date
		HashMap<String, String> holidayFlag = null;
		// loop for execute all currencies & entities and collect date
		// details
		Iterator<String> currentEntityItr = null;
		// To hold the iterated the mapcurrency
		Iterator<String> iteratorStr = null;
		// Declares the Iterator that iterates EntityRecord
		Iterator<EntityRecord> currencyIte = null;
		// To hold the currency code and maped entity records
		EntityRecord entityRecordList = null;
		// To hold the all entity records in collection
		Collection<EntityRecord> entityColl = null;
		// To get the map currency key values
		Collection<EntityRecord> currencyCollection = null;
		// To hold the all entity records based entity id
		HashMap<String, Collection<EntityRecord>> mapEntity = null;
		// Declares the EntityRecord object
		EntityRecord entityRecordResultSet = null;
		// To hold the entityRecordResultSet values
		Collection<EntityRecord> collEntityRecord = null;
		// To hold the all grid currency records
		EntityRecord currencyRecord = null;
		// To set the entity key
		EntityRecord entityRecord = null;
		// To hold the framed column currency records,Date and total predict
		// balance,
		Collection<EntityRecord> entColl = null;
		// To hold the framed column currency records and total predict balance
		EntityRecord dateRecord = null;
		// To get the current entity key
		Collection<EntityRecord> currencyEntity = null;
		// To hold the framed column currency records and total predict balance
		Collection<EntityRecord> dateColl = null;
		// Iterator for EntityRecord
		Iterator<EntityRecord> currencyEntityIte = null;
		// To get the total predict balance count
		int dateSumCounter = 0;
		// To get the total number of predict balance days
		int totalNoOfDays = 0;
		// To get the sum of predict balance for reporting currency
		BigDecimal totalBalance = null;
		String isAggregation = null;
		HashMap<String, String> scenarioAlertingMap = new HashMap<String, String>();
		SwtInterceptor interceptor = null;

		try {
			log.debug(this.getClass().getName()
					+ " - [getAllBalancesUsingStoredProc] - Entry");
			// Initializing the holidayFlag object
			holidayFlag = new LinkedHashMap<String, String>();
			// start the opTimer
			opTimer.start(SwtConstants.DATA_FETCH);
			// Initialize the object
			currentEntityColl = new ArrayList<EntityRecord>();
			// Initialize the object
			totalCollection = new ArrayList<EntityRecord>();
			// Initialize the object
			mapCurrency = new LinkedHashMap<String, ArrayList<EntityRecord>>();
			// Initialize the EntityRecord object
			entityMonitorRecord = new EntityRecord();
			// get the host Id from cache manager
			hostId = CacheManager.getInstance().getHostId();
			interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");

			// Get the current session from session factory
			session = getHibernateTemplate().getSessionFactory().withOptions().interceptor(interceptor).openSession();
			// Get the connection object
			conn = SwtUtil.connection(session);
			// Set the screen id to that of the entity monitor
			screenId = SwtConstants.ENTITY_MONITOR_ID;
			// Make a callable statement for executing the procedure
			cstmt = conn
					.prepareCall("{call PKG_ENTITY_MONITOR.SP_ENTITY_MONITOR_JOB_AGGR(?,?,?,?,?,?,?,?,?)}");
			// Set the parameters
			cstmt.setString(1, hostId);
			cstmt.setString(2, currGrp);
			cstmt.setDate(3, SwtUtil.truncateDateTime(startDate));
			cstmt.setString(4, userId);
			cstmt.setString(5, screenId);
			cstmt.setString(6, CurrencyCode);
            cstmt.setString(7, entityOffsetTime);
			// Register the output parameters
			cstmt.registerOutParameter(8, oracle.jdbc.OracleTypes.CURSOR);
			// Register the output parameters
			cstmt.registerOutParameter(9, oracle.jdbc.OracleTypes.VARCHAR);
			// Execute the callable statement
			cstmt.execute();
			// Get the result set object for predicted balance
			rsMonitorInfo = (ResultSet) cstmt.getObject(8);
			// Get isAggregation value. 'Y' if the personal entity list is defined and 'N' if not
			isAggregation = (String) cstmt.getString(9);
			// stop the opTimer
			opTimer.stop(SwtConstants.DATA_FETCH);
			// Initialize the object
			dateTotal = new LinkedHashMap<Integer, BigDecimal>();
			// cleanse rsPred to remove duplicate date records sometimes
			opTimer.start(SwtConstants.DATA_CLEANSE);
			// loop for execute all records and collect all currencies with
			// details
			while (rsMonitorInfo.next()) {

				// Creating new instance for EntityRecord
				monitorRecord = new EntityRecord();
				// setting entity id in monitor record
				monitorRecord.setEntityId(rsMonitorInfo.getString(1));
				scenarioAlertingMap.put(rsMonitorInfo.getString(2) , SwtUtil.getScenarioAlertingValue(rsMonitorInfo.getString("SCENARIO_HIGHLIGHTING"), scenarioAlertingMap.get(rsMonitorInfo.getString(2))));
				// setting currency code in monitor record
				monitorRecord.setCurrCode(rsMonitorInfo.getString(2));
				// setting date in monitor record
				monitorRecord.setDate(rsMonitorInfo.getDate(3));
				// setting balance in monitor record
				monitorRecord.setBalance(rsMonitorInfo.getBigDecimal(4));
				// setting holiday flag in monitor record
				monitorRecord.setHolidayFlag(rsMonitorInfo.getString(5));
				// set if the ccy has alerts instances related
				// setting clickable value in monitor record
				if ("Y".equals(isAggregation)) {
					 monitorRecord.setClickable(!"Y".equals(rsMonitorInfo.getString(13)) + "");
					 if ("Y".equals(rsMonitorInfo.getString(13))) {
						 monitorRecord.setTooltip(rsMonitorInfo.getString(14));				 
					 } else {
						 monitorRecord.setTooltip("");
					 }
					 
				} else {
					monitorRecord.setClickable("true");
					monitorRecord.setTooltip("");
				}
				if (!rsMonitorInfo.getString(8).equalsIgnoreCase(
						SwtConstants.HOLIDAY_FLAG)) {
					// Assign totalNoOfDays from db
					totalNoOfDays = rsMonitorInfo.getInt(6);
					// Assign totalpredictBalance from db
					totalBalance = rsMonitorInfo.getBigDecimal(7);
					// Set the total sum of predict balance for first
					// currency(selected dates)
					if (dateSumCounter < totalNoOfDays) {
						if (totalBalance != null) {
							// set the predict total balance in monitor record
							// object
							//Set scale 2 (two decimal places), 4 is round ROUND_HALF_UP
							monitorRecord.setDateSum(totalBalance
									.setScale(2, 4));
						} else {
							// set the predict total balance in monitor record
							// object
							monitorRecord.setDateSum(totalBalance);
						}
						dateSumCounter++;
					}

				}
				if (mapCurrency.containsKey(rsMonitorInfo.getString(2))) {
					List<EntityRecord> list = (ArrayList<EntityRecord>) mapCurrency
							.get(rsMonitorInfo.getString(2));
					list.add(monitorRecord);
				} else {
					monitorRecordList = new ArrayList<EntityRecord>();
					monitorRecordList.add(monitorRecord);
					mapCurrency.put(rsMonitorInfo.getString(2),
							monitorRecordList);
				}

				// set the header flag for specific entity & currency
				holidayFlag.put(rsMonitorInfo.getString(1) + ""
						+ rsMonitorInfo.getDate(3), rsMonitorInfo.getString(8));
				// set currency multiplier value
				entityMonitorRecord.setCurrencyMultiplier(rsMonitorInfo
						.getString(9));
			}
			// Assign dateSumCounter as zero
			dateSumCounter = 0;
			// loop for execute all currencies and collect the entity & date
			// details together
			iteratorStr = mapCurrency.keySet().iterator();
			while (iteratorStr.hasNext()) {
				entityRecordList = new EntityRecord();
				entityColl = new ArrayList<EntityRecord>();
				String key = (String) iteratorStr.next();
				currencyCollection = (Collection<EntityRecord>) mapCurrency
						.get(key);
				entityRecordList.setCurrCode(key);
				currencyIte = currencyCollection.iterator();
				mapEntity = new LinkedHashMap<String, Collection<EntityRecord>>();
				while (currencyIte.hasNext()) {
					// Instantiate the EntityRecord object
					entityRecordResultSet = new EntityRecord();
					// Iterating the result set with EntityRecord
					EntityRecord entityRecords = (EntityRecord) currencyIte
							.next();
					// setting entity id in result set
					entityRecordResultSet.setEntityId(entityRecords
							.getEntityId());
					// setting currency code in result set
					entityRecordResultSet.setCurrCode(entityRecords
							.getCurrCode());
					// setting holiday in result set
					entityRecordResultSet.setHolidayFlag(entityRecords
							.getHolidayFlag());
					// setting balance in result set
					entityRecordResultSet
							.setBalance(entityRecords.getBalance());
					// setting date in result set
					entityRecordResultSet.setDate(entityRecords.getDate());
					// setting clickable in result set
					entityRecordResultSet.setClickable(entityRecords.getClickable());
					entityRecordResultSet.setTooltip(entityRecords.getTooltip());

					if (dateSumCounter < totalNoOfDays) {
						// set the predict total balance entityRecordResultSet
						// object
						entityRecordResultSet.setDateSum(entityRecords
								.getDateSum());
						dateSumCounter++;
					}

					if (mapEntity.containsKey(entityRecords.getEntityId())) {
						collEntityRecord = (Collection<EntityRecord>) mapEntity
								.get(entityRecords.getEntityId());
						collEntityRecord.add(entityRecordResultSet);

					} else {
						// Creating new instance for EntityRecord list
						entityColl = new ArrayList<EntityRecord>();
						entityColl.add(entityRecordResultSet);
						mapEntity.put(entityRecords.getEntityId(), entityColl);
					}

				}
				entityRecordList.setHashRecords(mapEntity);
				currentEntityColl.add(entityRecordList);

			}
			// Assign dateSumCounter as zero
			dateSumCounter = 0;
			// if there is any content in the list set the currency
			if (currentEntityColl.size() > 0) {
				// iterate the currency entity details
				currEntityItr = currentEntityColl.iterator();
				while (currEntityItr.hasNext()) {
					// Instantiating EntityRecord list
					entColl = new ArrayList<EntityRecord>();
					// create instance for EntityRecord
					currencyRecord = new EntityRecord();
					// Type casting the EntityRecord object
					EntityRecord currentEntity = (EntityRecord) currEntityItr
							.next();

					// setting currency code in currency record
					currencyRecord.setCurrCode(currentEntity.getCurrCode());
					// Instantiate the EntityRecord List
					entityNewColl = new ArrayList<EntityRecord>();
					// iterate the entity collection details
					currentEntityItr = currentEntity.getHashRecords().keySet()
							.iterator();
					while (currentEntityItr.hasNext()) {
						// get the entityId
						String key = (String) currentEntityItr.next();
						// create a instance for EntityRecord
						entityRecord = new EntityRecord();
						// Instantiate the EntityRecord collection
						dateColl = new ArrayList<EntityRecord>();
						// set the entityId
						entityRecord.setEntityId(key);

						// get the currency & entity details
						currencyEntity = (Collection<EntityRecord>) currentEntity
								.getHashRecords().get(key);
						// iterate the currency & entity details
						currencyEntityIte = currencyEntity.iterator();

						int i = 0;
						while (currencyEntityIte.hasNext()) {
							// Instantiating EntityRecord
							dateRecord = new EntityRecord();
							// get the date details
							EntityRecord entityRecords = (EntityRecord) currencyEntityIte
									.next();
							// set the date details to dateRecord
							dateRecord.setBalance(entityRecords.getBalance());

							/*
							 * Condition added to check currency multiplier is
							 * applied
							 */
							if (entityMonitorRecord.getCurrencyMultiplier()
									.equals(SwtConstants.YES)) {
								// Set decimal format with one decimal
								dateRecord.setBalanceAsString(SwtUtil
										.formatCurrency(SwtConstants.YES,
												entityRecords.getBalance()));
							} else {
								// set decimal format with two decimals
								dateRecord.setBalanceAsString(SwtUtil
										.formatCurrency(entityRecords
												.getCurrCode().substring(0, 3),
												entityRecords.getBalance()));
							}

							dateRecord.setCurrCode(entityRecords.getCurrCode());
							dateRecord.setHolidayFlag(entityRecords
									.getHolidayFlag());
							dateRecord.setClickable(entityRecords
									.getClickable());
							dateRecord.setTooltip(entityRecords
									.getTooltip());
							dateRecord.setBalanceDate(SwtUtil.formatDate(
									entityRecords.getDate(), format
											.getDateFormatValue()));

							// sign as negative
							if (entityRecords.getBalance() != null
									&& entityRecords.getBalance().doubleValue() < 0) {
								dateRecord.setPredBalanceNegative(true);
							}
							// checks the date format
							if (format.getDateFormatValue().equals(
									SwtConstants.FULL_DATE_FORMAT)) {
								dateRecord.setBalanceDateAsString(SwtUtil
										.formatDate(entityRecords.getDate(),
												SwtConstants.DATE_MONTH));
								if ("Y".equals(entityOffsetTime) && "false".equals(dateRecord.getClickable())) {
									if (i == 0) {
										dateRecord.setBalanceDateAsString("T");
									} else {
										dateRecord.setBalanceDateAsString("T+" + i);
									}
									i++;
								}
							} else {
								// getting the date and month
								dateRecord.setBalanceDateAsString(SwtUtil
										.formatDate(entityRecords.getDate(),
												SwtConstants.MONTH_DATE));
							}
							dateRecord.setEntityId(entityRecords.getEntityId());
							// set the total record flag to date record header
							// flag
							if (!holidayFlag.get(
									dateRecord.getEntityId() + ""
											+ entityRecords.getDate()).equals(
									SwtConstants.HOLIDAY_FLAG)) {
								// set the collection to object
								dateColl.add(dateRecord);
								if (dateSumCounter < totalNoOfDays) {
									// set the predict total balance dateTotal
									// object
									
									dateTotal.put(dateSumCounter, entityRecords
											.getDateSum());
									dateSumCounter++;
								}
								if (holidayFlag.containsKey(dateRecord
										.getEntityId()
										+ "" + entityRecords.getDate())) {
									dateRecord.setHeaderFlag(holidayFlag
											.get(dateRecord.getEntityId() + ""
													+ entityRecords.getDate()));
								}
							}

							
						}
						// add the collection to object
						if (dateColl.size() != 0) {
							dateRecord.setDateRecords(dateColl);
							entColl.add(dateRecord);
						}
					}
					// set the entity & currency collection to object
					entityRecord.setEntityRecords(entColl);
					entityRecord.setScenarioHighlighted(scenarioAlertingMap.get(currencyRecord.getCurrCode()));
					entityNewColl.add(entityRecord);
					currencyRecord.setCurrencyRecords(entityNewColl);
					currencyRecord.setScenarioHighlighted(scenarioAlertingMap.get(currencyRecord.getCurrCode()));
					totalCollection.add(currencyRecord);
				}
			}

			// stop the opTimer
			opTimer.stop(SwtConstants.DATA_CLEANSE);
			// Add the entity monitor records
			entityMonitorRecord.setCurrencyMonitorRecords(totalCollection);
			// Add the entity monitor total records
			entityMonitorRecord.setTotalCurrencyRecords(dateTotal);

			log.debug(this.getClass().getName()
					+ " - [getAllBalancesUsingStoredProc] - Exit");
		} catch (DataAccessResourceFailureException dataAccessException) {
			log
					.error(this.getClass().getName()
							+ " - [getAllBalancesUsingStoredProc] - DataAccessResourceFailureException -"
							+ dataAccessException.getMessage());

			throw SwtErrorHandler.getInstance().handleException(
					dataAccessException, "getAllBalancesUsingStoredProc",
					EntityMonitorDAOHibernate.class);
		} catch (IllegalStateException illegalStateException) {
			log
					.error(this.getClass().getName()
							+ " - [getAllBalancesUsingStoredProc] - IllegalStateException -"
							+ illegalStateException.getMessage());

			throw SwtErrorHandler.getInstance().handleException(
					illegalStateException, "getAllBalancesUsingStoredProc",
					EntityMonitorDAOHibernate.class);
		} catch (HibernateException hibernateException) {
			log
					.error(this.getClass().getName()
							+ " - [getAllBalancesUsingStoredProc] - HibernateException -"
							+ hibernateException.getMessage());

			throw SwtErrorHandler.getInstance().handleException(
					hibernateException, "getAllBalancesUsingStoredProc",
					EntityMonitorDAOHibernate.class);
		} catch (SQLException sqlException) {
			log.error(this.getClass().getName()
					+ " - [getAllBalancesUsingStoredProc] - SQLException -"
					+ sqlException.getMessage());
			throw SwtErrorHandler.getInstance().handleException(sqlException,
					"getAllBalancesUsingStoredProc",
					EntityMonitorDAOHibernate.class);
		} catch (Exception e) {
			log.error(this.getClass().getName()
					+ " - [getAllBalancesUsingStoredProc] - Exception -"
					+ e.getMessage());

			throw SwtErrorHandler.getInstance().handleException(e,
					"getAllBalancesUsingStoredProc",
					EntityMonitorDAOHibernate.class);
		} finally {
			//edited by KaisBS for mantis 1745 : introducing the implementation of the JDBCCloser class
			SwtException thrownException = null;
			Object[] exceptions = JDBCCloser.close(rsMonitorInfo,cstmt, conn, session);

			if (exceptions[0] != null)
			    thrownException = SwtErrorHandler.getInstance().handleException((SQLException) exceptions[0], "getAllBalancesUsingStoredProc",
						EntityMonitorDAOHibernate.class);

			if (thrownException == null && exceptions[1] !=null) 
			    thrownException = SwtErrorHandler.getInstance().handleException((HibernateException) exceptions[1], "getAllBalancesUsingStoredProc",
						EntityMonitorDAOHibernate.class);

			if (thrownException!=null)
			    throw thrownException;

			// Cleaning all the unreferenced objects
			hostId = null;
			cstmt = null;
			screenId = null;
			dateTotal = null;
			monitorRecordList = null;
			currentEntityColl = null;
			totalCollection = null;
			entityNewColl = null;
			mapCurrency = null;
			currentEntityItr = null;
		}
		return entityMonitorRecord;
	}

	/*
	 * End:code modified by sudhakar/vivek for Mantis 1994 on 10-Jul-2012:Performance
	 * improvement in Entity Monitor and Currency Monitor  and for mantis 1991
	 */
	/**
	 * This is used to get the currency codes for the entities based on the
	 * access set by the administrator.<br>
	 * If particular entity has no access, currency code for that entity will
	 * not be shown to the client.<br>
	 * 
	 * @param String
	 *            hostId
	 * @param String
	 *            roleId
	 * @return Collection<CurrencyGroup>
	 * @throws SwtException
	 */
	@SuppressWarnings("unchecked")
	public Collection<CurrencyGroup> getCurrencyGroupAccessDetails(
			String hostId, String roleId) throws SwtException {

		Collection<CurrencyGroup> list = null;
		try {
			log.debug(this.getClass().getName()
					+ " - [getCurrencyGroupAccessDetails] - Entry");
			// Instantiate the ArrayList
			list = new ArrayList<CurrencyGroup>();
			// get the Currency Group Access Details
			 list = (Collection<CurrencyGroup>) getHibernateTemplate()
					.find(
							"select distinct G from CurrencyGroupAccess C,"
									+ "EntityAccess E, CurrencyGroup G where E.id.hostId = C.id.hostId AND E.id.entityId = C.id.entityId "
									+ "and E.id.roleId = C.id.roleId and C.accessId in ('0','1') "
									+ "and E.accessId in ('0','1') and C.id.hostId=?0 and C.id.roleId=?1 and "
									+ "G.id.currencyGroupId = C.id.currencyGroupId ORDER BY G.id.currencyGroupId ",
							new Object[] { hostId, roleId });
			log.debug(this.getClass().getName()
					+ " - [getCurrencyGroupAccessDetails] - Exit");
		} catch (Exception e) {
			log.error("Exception occured in " + this.getClass().getName()
					+ " -  getCurrencyGroupAccessDetails() " + e.getMessage());
			throw SwtErrorHandler.getInstance().handleException(e,
					"getCurrencyGroupAccessDetails",
					EntityMonitorDAOHibernate.class);
		}
		return list;
	}

	/**
	 * This method is used to retrieve the personal entity list details.<br>
	 * Personal Entity List details contains information like:<br>
	 * <UL>
	 * <li>Entity Id</li>
	 * <li>Entity Name</li>
	 * <li>Display</li>
	 * <li>Default Days</li>
	 * <li>Order Of Display</li>
	 * </UL>
	 * 
	 * @param screenId
	 * @param hostId
	 * @param userId
	 * @return Collection of personal entity list
	 * @throws SwtException
	 */
	@SuppressWarnings("unchecked")
	public Collection<PersonalEntityList> getPersonalEntityList(String userId,
			String screenId, String hostId, String...aggrEntity) throws SwtException {
		// Session object
		Session session = null;
		// Connection object
		Connection conn = null;
		// CallableStatement object
		CallableStatement cstmt = null;
		// ResultSet object
		ResultSet rs = null;
		// Collection of PersonalEntityList
		Collection<PersonalEntityList> details = null;
		// PersonalEntityList object
		PersonalEntityList entityList = null;
		String selectedAggrEntity = null;
		try {
			log.debug(this.getClass().getName()
					+ " - [getPersonalEntityList] - " + "Entry");
			if (aggrEntity != null) {
				if (aggrEntity.length > 0) {
					selectedAggrEntity = aggrEntity[0];
				}
			}
			/*
			 * Returns the HibernateTemplate and session factory for this DAO,
			 * and opening a Hibernate Session
			 */
			session = getHibernateTemplate().getSessionFactory().openSession();
			conn = SwtUtil.connection(session);
			/* Using Callable statement to execute the Stored Procedure */
			// package name Modified for Mantis 1119 by VIvekanandan A on
			// 16-02-2012
			cstmt = conn
					.prepareCall("{call PKG_ENTITY_MONITOR.SP_ENTITY_PERSONAL_LIST(?,?,?,?,?)}");
			cstmt.setString(1, hostId);
			cstmt.setString(2, userId);
			cstmt.setString(3, screenId);
			if (selectedAggrEntity != null) {
				cstmt.setString(4, SwtConstants.PROPNAME_PERSONAL_ENTITY_AGGR + ":" + selectedAggrEntity);
			} else {
				cstmt.setString(4, SwtConstants.PROPNAME_PERSONAL_ENTITY);
			}
			cstmt.registerOutParameter(5, oracle.jdbc.OracleTypes.CURSOR);
			cstmt.execute();
			/* Fetching the Result */
			rs = (ResultSet) cstmt.getObject(5);
			details = new ArrayList<PersonalEntityList>();
			/* Condition to check result set has value */
			if (rs != null) {
				if (selectedAggrEntity != null) {
					/* Loop to iterate result set */
					while (rs.next()) {
						entityList = new PersonalEntityList();
						/* Setting entity id,entity name using bean class */
						entityList.getId().setEntityId(rs.getString(1));
						entityList.setEntityName(rs.getString(2));
						if (!SwtUtil.isEmptyOrNull(rs.getString(3)))
							entityList.setYesAggrgEntity(rs.getString(3));
						else
							entityList.setYesAggrgEntity(SwtConstants.NO);
						/* Add the entity details in collection */
						details.add(entityList);
					}
				} else {
					/* Loop to iterate result set */
					while (rs.next()) {
						entityList = new PersonalEntityList();
						/* Setting entity id,entity name using bean class */
						entityList.getId().setEntityId(rs.getString(1));
						entityList.setEntityName(rs.getString(2));
						/* Setting yes display to entityList */
						if (!SwtUtil.isEmptyOrNull(rs.getString(3)))
							entityList.setYesDisplay(rs.getString(3));
						else
							entityList.setYesDisplay(SwtConstants.NO);
						/* Setting display days to entityList */
						if (!SwtUtil.isEmptyOrNull(rs.getString(4)))
							entityList.setDisplayDays(Integer.parseInt(rs
									.getString(4)));
						else
							entityList.setDisplayDays(1);
						/* Setting priority order to entityList */
						if (!SwtUtil.isEmptyOrNull(rs.getString(5)))
							entityList.setPriorityOrder(Integer.parseInt(rs
									.getString(5)));
						else
							entityList.setPriorityOrder(500);
						
						if (!SwtUtil.isEmptyOrNull(rs.getString(6)))
							entityList.setYesAggrgEntity(rs.getString(6));
						else
							entityList.setYesAggrgEntity(SwtConstants.NO);
						/* Add the entity details in collection */
						details.add(entityList);
					}
				}
			}
			log.debug(this.getClass().getName()
					+ " - [getPersonalEntityList] - " + "Exit");
		} catch (Exception e) {
			log
					.error(this.getClass().getName()
							+ " - Exception caught in [getPersonalEntityList] method : - "
							+ e.getMessage());
			throw SwtErrorHandler.getInstance().handleException(e,
					"getPersonalEntityList", EntityMonitorDAOHibernate.class);
		} finally {
			//edited by KaisBS for mantis 1745 : introducing the implementation of the JDBCCloser class
			SwtException thrownException = null;
			Object[] exceptions = JDBCCloser.close(rs,cstmt, conn, session);

			if (exceptions[0] != null)
			    thrownException = SwtErrorHandler.getInstance().handleException((SQLException) exceptions[0], "getPersonalEntityList",
						EntityMonitorDAOHibernate.class);

			if (thrownException == null && exceptions[1] !=null) 
			    thrownException = SwtErrorHandler.getInstance().handleException((HibernateException) exceptions[1], "getPersonalEntityList",
						EntityMonitorDAOHibernate.class);

			if (thrownException!=null)
			    throw thrownException;
		}
		return details;
	}

	/**
	 * This method is used to save the personal entity details.<br>
	 * Personal Entity List details contains information like:<br>
	 * <UL>
	 * <li>Entity Id</li>
	 * <li>Entity Name</li>
	 * <li>Display</li>
	 * <li>Default Days</li>
	 * <li>Order Of Display</li>
	 * </UL>
	 * 
	 * @param entityList
	 * @throws SwtException
	 */
	public void savePersonalEntityList(PersonalEntityList entityList)
			throws SwtException {

		try {
			log.debug(this.getClass().getName()
					+ " savePersonalEntityList() - Enter");
			getHibernateTemplate().saveOrUpdate(entityList);
			getHibernateTemplate().flush();
			log.debug(this.getClass().getName()
					+ " savePersonalEntityList() - Exit");
		} catch (Exception e) {
			log
					.error("Exception occured in saving/updating personal entity list."
							+ e.getMessage());
			throw SwtErrorHandler.getInstance().handleException(e,
					"savePersonalEntityList", EntityMonitorDAOHibernate.class);
		}
	}

	/**
	 * This method is used to retrieve the personal currency list details.<br>
	 * Personal Currency List details contains the following information:<br>
	 * <UL>
	 * <li>Currency Code</li>
	 * <li>Currency Name</li>
	 * <li>Order Of Display</li>
	 * <li>Group of Entities</li>
	 * </UL>
	 * 
	 * @param screenId
	 * @param hostId
	 * @param userId
	 * @return Collection of personal currency list
	 * @throws SwtException
	 */
	@SuppressWarnings("unchecked")
	public PersonalCurrency getPersonalCurrency(String hostId, String userId,
			String screenId) throws SwtException {
		// Declares the Hibernate session object
		Session session = null;
		// Declares the SQL Connection object
		Connection conn = null;
		// Declares the CallableStatement object
		CallableStatement cstmt = null;
		// Declares the result set
		ResultSet rs = null;
		// map currency with entity records
		HashMap<String, ArrayList<PersonalCurrency>> mapCurrency = null;
		// map currency with entity records
		HashMap<String, String> mapEntity = null;
		// object to hold entity details
		ArrayList<PersonalCurrency> currencyRecordList = null;
		// EntityRecord object
		PersonalCurrency currency = null;
		try {
			log.debug(this.getClass().getName() + " - [getPersonalCurrency] - "
					+ "Entry");
			// Initialize the object
			mapCurrency = new LinkedHashMap<String, ArrayList<PersonalCurrency>>();
			mapEntity = new LinkedHashMap<String, String>();
			/*
			 * Returns the HibernateTemplate and session factory for this DAO,
			 * and opening a Hibernate Session
			 */
			session = getHibernateTemplate().getSessionFactory().openSession();
			conn = SwtUtil.connection(session);
			/* Using Callable statement to execute the Stored Procedure */
			// package name Modified for Mantis 1119 by VIvekanandan A on
			// 16-02-2012
			cstmt = conn
					.prepareCall("{call PKG_ENTITY_MONITOR.SP_ENTITY_PERSONAL_LIST(?,?,?,?,?)}");
			cstmt.setString(1, hostId);
			cstmt.setString(2, userId);
			cstmt.setString(3, screenId);
			cstmt.setString(4, SwtConstants.PROPNAME_PERSONAL_CURRENCY);
			cstmt.registerOutParameter(5, oracle.jdbc.OracleTypes.CURSOR);
			cstmt.execute();
			/* Fetching the Result */
			rs = (ResultSet) cstmt.getObject(5);
			/* Condition to check result set has value */
			currency = new PersonalCurrency();
			if (rs != null) {
				/* Loop to iterate result set */
				while (rs.next()) {
					PersonalCurrency currencyList = new PersonalCurrency();
					/* Setting account id,account name using bean class */
					currencyList.getId().setCurrencyCode(rs.getString(1));
					currencyList.setCurrencyName(rs.getString(2));
					currencyList.setEntityId(rs.getString(5));
					if (!SwtUtil.isEmptyOrNull(rs.getString(3)))
						currencyList.setPriorityOrder(Integer.parseInt(rs
								.getString(3)));
					else
						currencyList.setPriorityOrder(50);
					if (!SwtUtil.isEmptyOrNull(rs.getString(4)))
						currencyList.setEntityFlag(rs.getString(4));
					else
						currencyList.setEntityFlag(SwtConstants.NO);
					if (!SwtUtil.isEmptyOrNull(rs.getString(6)))
						currencyList.setAccessFlag(rs.getString(6));
					else
						currencyList.setAccessFlag(SwtConstants.NO);

					if (mapEntity.containsKey(currencyList.getEntityId())) {
						if (!currencyList.getEntityFlag().equals("Y")) {
							mapEntity.put(currencyList.getEntityId(),
									currencyList.getEntityFlag());
						}
					} else {
						if (currencyList.getEntityFlag().equals("Y")) {
							mapEntity.put(currencyList.getEntityId(),
									currencyList.getEntityFlag());
						} else {
							mapEntity.put(currencyList.getEntityId(),
									SwtConstants.NO);
						}
					}
					if (mapCurrency.containsKey(rs.getString(1))) {
						List<PersonalCurrency> list = (ArrayList<PersonalCurrency>) mapCurrency
								.get(rs.getString(1));
						list.add(currencyList);
					} else {
						currencyRecordList = new ArrayList<PersonalCurrency>();
						currencyRecordList.add(currencyList);
						mapCurrency.put(rs.getString(1), currencyRecordList);
					}
				}
				currency.setHashRecords(mapCurrency);
				currency.setHashEntityFlag(mapEntity);
			}
			log.debug(this.getClass().getName() + " - [getPersonalCurrency] - "
					+ "Exit");
		} catch (Exception e) {
			log
					.error(this.getClass().getName()
							+ " - Exception caught in [getPersonalCurrency] method : - "
							+ e.getMessage());
			throw SwtErrorHandler.getInstance().handleException(e,
					"getPersonalCurrency", EntityMonitorDAOHibernate.class);
		} finally {
			//edited by KaisBS for mantis 1745 : introducing the implementation of the JDBCCloser class
			SwtException thrownException = null;
			Object[] exceptions = JDBCCloser.close(rs,cstmt, conn, session);

			if (exceptions[0] != null)
			    thrownException = SwtErrorHandler.getInstance().handleException((SQLException) exceptions[0], "getPersonalCurrency",
						EntityMonitorDAOHibernate.class);

			if (thrownException == null && exceptions[1] !=null) 
			    thrownException = SwtErrorHandler.getInstance().handleException((HibernateException) exceptions[1], "getPersonalCurrency",
						EntityMonitorDAOHibernate.class);

			if (thrownException!=null)
			    throw thrownException;
		}
		return currency;
	}

	/**
	 * This method is used to save the personal currencyEntityMap details.<br>
	 * Here, the information of both Personal Currency and Personal Entity are
	 * get saved.<br>
	 * Persisting these two information together will be happened in separate
	 * table.<br>
	 * 
	 * @param currencyList
	 * @throws SwtException
	 */
	public void savePersonalCurrencyEntityMap(PersonalCcyEntityMap currencyList)
			throws SwtException {
		// Holds the hibernate session instance
		Session sesion = null;
		// Holds the hibernate Transaction instance
		Transaction tx = null;
		try {
			log.debug(this.getClass().getName()
					+ " savePersonalCurrencyEntityMap() - Begins");
			boolean flag = fetchCurrencyRecord(currencyList);
			sesion = getHibernateTemplate().getSessionFactory().openSession();
			// Updates the details of the inputInterface
			tx = sesion.beginTransaction();
			if (!flag)
				sesion.update(currencyList);
			else
				sesion.save(currencyList);
			// Commits the transaction to the database
			tx.commit();
			log.debug(this.getClass().getName()
					+ " savePersonalCurrencyEntityMap() - Ends");
		} catch (Exception e) {
			try {
				if (tx != null) {
					tx.rollback();
				}
			} catch (HibernateException ignore) {
				log
						.error("HibernateException occured in rolling back transaction. Cause : "
								+ ignore.getMessage());
			}
			log
					.error("Exception occured in saving/updating personal currency list. Cause : "
							+ e.getMessage());
			throw SwtErrorHandler.getInstance().handleException(e,
					"savePersonalCurrencyEntityMap",
					EntityMonitorDAOHibernate.class);
		} finally {
			//edited by KaisBS for mantis 1745 : introducing the implementation of the JDBCCloser class
			HibernateException hThrownException = JDBCCloser.close(sesion);
			   
			if (hThrownException != null)
				throw new SwtException(hThrownException.getMessage());
			
		}
	}

	/**
	 * This method is used to save the personal currency details.<br>
	 * Personal Currency List details contains the following information:<br>
	 * <UL>
	 * <li>Currency Code</li>
	 * <li>Currency Name</li>
	 * <li>Order Of Display</li>
	 * <li>Group of Entities</li>
	 * </UL>
	 * 
	 * @param currencyList
	 * @throws SwtException
	 */
	public void savePersonalCcy(PersonalCurrency currencyList)
			throws SwtException {
		// Holds the hibernate session instance
		Session sesion = null;
		// Holds the hibernate Transaction instance
		Transaction tx = null;
		try {
			log
					.debug(this.getClass().getName()
							+ " savePersonalCcy() - Begins");
			boolean flag = fetchCcyRecord(currencyList);
			sesion = getHibernateTemplate().getSessionFactory().openSession();
			// Updates the details of the inputInterface
			tx = sesion.beginTransaction();
			if (!flag)
				sesion.update(currencyList);
			else
				sesion.save(currencyList);
			// Commits the transaction to the database
			tx.commit();
			log.debug(this.getClass().getName() + " savePersonalCcy() - Ends");
		} catch (Exception e) {
			try {
				if (tx != null) {
					tx.rollback();
				}
			} catch (HibernateException ignoreExp) {
				log
						.error("HibernateException occured in rolling back transaction. Cause : "
								+ ignoreExp.getMessage());
			}
			log
					.error("Exception occured in saving/updating personal currency list. Cause : "
							+ e.getMessage());
			throw SwtErrorHandler.getInstance().handleException(e,
					"savePersonalCcy", EntityMonitorDAOHibernate.class);
		} finally {
			//edited by KaisBS for mantis 1745 : introducing the implementation of the JDBCCloser class
			HibernateException hThrownException = JDBCCloser.close(sesion);
			   
			if (hThrownException != null)
				throw new SwtException(hThrownException.getMessage());
		}
	}

	/**
	 * This method is used to fetch the details of the passed interface
	 * 
	 * @param opTimer
	 * @param interfaceId
	 * @return InputInterface
	 */
	@SuppressWarnings("unchecked")
	private boolean fetchCurrencyRecord(PersonalCcyEntityMap personalCcyEntity)
			throws SwtException {
		boolean flag = true;
		// List of PersonalEntity
		List<PersonalEntityList> data = null;
		try {
			log.debug(this.getClass().getName()
					+ " - [fetchCurrencyRecord] - Entering");
			// fetches the details of an interfaces from P_PERSONAL_ENTITY_LIST
			// table
			data = (List<PersonalEntityList>) getHibernateTemplate().find(
					"select p " + "from PersonalCcyEntityMap p "
							+ "where p.id.entityId =?0 "
							+ "and p.id.userId = ?1 and p.id.hostId = ?2 "
							+ "and p.id.currencyCode = ?3 ",
					new Object[] { personalCcyEntity.getId().getEntityId(),
							personalCcyEntity.getId().getUserId(),
							personalCcyEntity.getId().getHostId(),
							personalCcyEntity.getId().getCurrencyCode() });
			if (data.size() > 0)
				flag = false;
			log.debug(this.getClass().getName()
					+ " - [fetchCurrencyRecord] - Exiting");
		} catch (Exception e) {
			log
					.error(this.getClass().getName()
							+ " - [fetchCurrencyRecord] - Exception -"
							+ e.getMessage());
			// Re-throwing SwtException
			throw SwtErrorHandler.getInstance().handleException(e,
					"fetchCurrencyRecord", EntityMonitorDAOHibernate.class);

		}
		return flag;
	}

	/**
	 * This method is used to fetch the details of the passed interface
	 * 
	 * @param opTimer
	 * @param interfaceId
	 * @return InputInterface
	 */
	@SuppressWarnings("unchecked")
	private boolean fetchCcyRecord(PersonalCurrency personalCcyy)
			throws SwtException {

		boolean flag = true;
		// List of PersonalEntity
		List<PersonalEntityList> data = null;
		try {
			log.debug(this.getClass().getName()
					+ " - [fetchCcyRecord] - Entering");
			// fetches the details of an interfaces from P_PERSONAL_ENTITY_LIST
			// table
			data = (List<PersonalEntityList>) getHibernateTemplate().find(
					"select p " + "from PersonalCurrency p "
							+ "where p.id.screenId =?0 "
							+ "and p.id.userId = ?1 and p.id.hostId = ?2 "
							+ "and p.id.currencyCode = ?3 ",
					new Object[] { personalCcyy.getId().getScreenId(),
							personalCcyy.getId().getUserId(),
							personalCcyy.getId().getHostId(),
							personalCcyy.getId().getCurrencyCode() });
			if (data.size() > 0)
				flag = false;
			log.debug(this.getClass().getName()
					+ " - [fetchCcyRecord] - Exiting");
		} catch (Exception e) {
			log.error(this.getClass().getName()
					+ " - [fetchCcyRecord] - Exception -" + e.getMessage());
			// Re-throwing SwtException
			throw SwtErrorHandler.getInstance().handleException(e,
					"fetchCcyRecord", EntityMonitorDAOHibernate.class);
		}
		return flag;
	}

	/**
	 * This method is used to retrieve the personal entity list details based on
	 * the access privilege given to the entity.<br>
	 * 
	 * @return PersonalEntityList
	 * @throws SwtException
	 */
	@SuppressWarnings("unchecked")
	public PersonalEntityList fetchRecord(PersonalEntityList personalEntityList)
			throws SwtException {

		// PersonalEntityList object
		PersonalEntityList per = null;
		// List of Personal Entity
		List<PersonalEntityList> data = null;
		try {
			log
					.debug(this.getClass().getName()
							+ " - [fetchRecord] - Entering");
			// creating instance for PersonalEntityList
			per = new PersonalEntityList();
			// fetches the details of an interfaces from P_PERSONAL_ENTITY_LIST
			// table
			data = (List<PersonalEntityList>) getHibernateTemplate()
					.find(
							"select distinct p "
									+ "from Entity e, EntityAccess ea, PersonalEntityList p "
									+ "where e.id.entityId = p.id.entityId and ea.id.roleId = ?0 "
									+ "and p.id.userId = ?1 and p.id.screenId = ?2 and p.id.hostId = ?3 "
									+ "and ea.accessId != 2 and e.id.entityId = ?4 order by e.id.entityId asc",
							new Object[] {
									personalEntityList.getId().getRoleId(),
									personalEntityList.getId().getUserId(),
									personalEntityList.getId().getScreenId(),
									personalEntityList.getId().getHostId(),
									personalEntityList.getId().getEntityId() });
			log.debug(this.getClass().getName() + " - [fetchRecord] - Exiting");
			if (data.size() < 1) {
				// set the values to PersonalEntityList
				per.getId().setEntityId(
						personalEntityList.getId().getEntityId());
				per.getId().setHostId(personalEntityList.getId().getHostId());
				per.getId().setScreenId(
						personalEntityList.getId().getScreenId());
				per.getId().setUserId(personalEntityList.getId().getUserId());
				per.setYesAggrgEntity(personalEntityList.getYesAggrgEntity());
				per.setPriorityOrder(500);
				per.setYesDisplay(SwtConstants.NO);
				per.setDisplayDays(1);
				per.setSaveorupdate("save");
			} else {
				// set the values to PersonalEntityList
				per = data.get(0);
				per.setSaveorupdate("update");
			}
			log
					.debug(this.getClass().getName()
							+ " - [fetchRecord] - Entering");
		} catch (Exception e) {
			log.error(this.getClass().getName()
					+ " - [fetchRecord] - Exception -" + e.getMessage());
			throw SwtErrorHandler.getInstance().handleException(e,
					"fetchRecord", EntityMonitorDAOHibernate.class);
		}
		return per;
	}

	/**
	 * This method is used to save the personal entity list<br>
	 * 
	 * @param dto
	 *            PersonalEntityList
	 * @return
	 * @throws SwtException
	 */
	public void saveRecord(PersonalEntityList dto) throws SwtException {
		// Holds the hibernate session instance
		Session session = null;
		// Holds the hibernate Transaction instance
		Transaction tx = null;
		try {
			log.debug(this.getClass().getName() + " - [saveRecord] - Entering");
			session = getHibernateTemplate().getSessionFactory().openSession();
			// Updates the details of the inputInterface
			tx = session.beginTransaction();
			if (dto.getSaveorupdate().equals("save"))
				session.save(dto);
			else
				session.update(dto);
			// Commits the transaction to the database
			tx.commit();
			log.debug(this.getClass().getName() + " - [saveRecord] - Exiting");
		} catch (Exception e) {
			try {
				if (tx != null) {
					tx.rollback();
				}
			} catch (HibernateException exception) {
				log
						.error("HibernateException occured in rolling back transaction. Cause : "
								+ exception.getMessage());
			}
			log.error(this.getClass().getName()
					+ " - Exception caught in [saveRecord] method "
					+ e.getMessage());
			throw SwtErrorHandler.getInstance().handleException(e,
					"saveRecord", EntityMonitorDAOHibernate.class);

		} finally {
			//edited by KaisBS for mantis 1745 : introducing the implementation of the JDBCCloser class
			HibernateException hThrownException = JDBCCloser.close(session);

			if (hThrownException != null)
				throw new SwtException(hThrownException.getMessage());
		}
	}

	/**
	 * This method is used to get the entity list details for the given user<br>
	 * 
	 * @param hostId
	 * @param roleId
	 * @return Collection<EntityAccess>
	 * @throws SwtException
	 */
	@SuppressWarnings("unchecked")
	public Collection<EntityAccess> getUserEntityList(String hostId,
			String roleId) throws SwtException {
		// Collection of EntityAccess
		Collection<EntityAccess> entityList = null;
		try {
			log.debug(this.getClass().getName()
					+ " - [getUserEntityList] - Entering");
			// create instance
			entityList = new ArrayList<EntityAccess>();
			// getting list of entity from database
			entityList = (Collection<EntityAccess>) getHibernateTemplate()
					.find(
							"from EntityAccess e where e.id.hostId = ?0 "
									+ "and e.id.roleId=?1 and e.accessId != 2 order by e.id.entityId asc",
							new Object[] { hostId, roleId });
			log.debug(this.getClass().getName()
					+ " - [getUserEntityList] - Exiting");
		} catch (Exception e) {
			log.error(this.getClass().getName()
					+ " - Exception caught in [getUserEntityList] method "
					+ e.getMessage());
			throw SwtErrorHandler.getInstance().handleException(e,
					"getUserEntityList", EntityMonitorDAOHibernate.class);
		}
		return entityList;
	}
	
	@SuppressWarnings("unchecked")
	public PersonalEntityList fetchSumRecord(PersonalEntityList personalEntityList)
			throws SwtException {

		// PersonalEntityList object
		PersonalEntityList per = null;
		// List of Personal Entity
		List<PersonalEntityList> data = null;
		try {
			log
					.debug(this.getClass().getName()
							+ " - [fetchSumRecord] - Entering");
			// creating instance for PersonalEntityList
			per = new PersonalEntityList();
			// fetches the details of an interfaces from P_PERSONAL_ENTITY_LIST
			// table
			data = (List<PersonalEntityList>) getHibernateTemplate()
					.find(
							"select distinct p "
									+ "from PersonalEntityList p "
									+ "where p.id.userId = ?0 and p.id.screenId = ?1 and p.id.hostId = ?2" +
									" and p.id.entityId = ?3 ",
							new Object[] {
									personalEntityList.getId().getUserId(),
									personalEntityList.getId().getScreenId(),
									personalEntityList.getId().getHostId(),
									personalEntityList.getId().getEntityId()});
			log.debug(this.getClass().getName() + " - [fetchSumRecord] - Exiting");
			if (data.size() < 1) {
				// set the values to PersonalEntityList
				per.getId().setEntityId(
						personalEntityList.getId().getEntityId());
				per.getId().setHostId(personalEntityList.getId().getHostId());
				per.getId().setScreenId(
						personalEntityList.getId().getScreenId());
				per.getId().setUserId(personalEntityList.getId().getUserId());
				per.setDisplayDays(1);
				per.setPriorityOrder(500);
				per.setYesDisplay(SwtConstants.NO);
				per.setYesAggrgEntity(SwtConstants.YES);
				per.setSaveorupdate("save");
			} else {
				// set the values to PersonalEntityList
				per = data.get(0);
				per.setSaveorupdate("update");
			}
			log
					.debug(this.getClass().getName()
							+ " - [fetchSumRecord] - Entering");
		} catch (Exception e) {
			log.error(this.getClass().getName()
					+ " - [fetchSumRecord] - Exception -" + e.getMessage());
			throw SwtErrorHandler.getInstance().handleException(e,
					"fetchSumRecord", EntityMonitorDAOHibernate.class);
		}
		return per;
	}
	
	public void saveSumRecord(PersonalEntityList dto, String sumEntitiesList) throws SwtException {
		// Holds the hibernate session instance

		Session session = null;
		// Holds the hibernate Transaction instance
		Transaction tx = null;
		String[] sumEntitiesArray = null; 
		PersonalEntityListExt personalEntityListExt = null;
		String deleteSumEntitesHQL = null;
		int rowsDeteled;
		
		try {
			log.debug(this.getClass().getName() + " - [saveSumRecord] - Entering");
			session = getHibernateTemplate().getSessionFactory().openSession();
			// Updates the details of the inputInterface
			tx = session.beginTransaction();
			sumEntitiesArray = sumEntitiesList.split(",");
			personalEntityListExt = new PersonalEntityListExt();
			personalEntityListExt.getId().setHostId(dto.getId().getHostId());
			personalEntityListExt.getId().setScreenId(dto.getId().getScreenId());
			personalEntityListExt.getId().setUserId(dto.getId().getUserId());
			personalEntityListExt.getId().setAggrgEntityId(dto.getId().getEntityId());
			if (dto.getSaveorupdate().equals("save")) {
				session.save(dto);
				for (int i = 0; i < sumEntitiesArray.length; i++) {
					personalEntityListExt.getId().setEntityId(sumEntitiesArray[i]);
					session.save(personalEntityListExt);
					session.flush();
			        session.clear();
				}
				
			} else {
				// Delete all the personal entity list ext first before saving new entries
				deleteSumEntitesHQL = "from PersonalEntityListExt  p where p.id.hostId=?0 and p.id.aggrgEntityId=?1 and p.id.screenId=?2 and p.id.userId = ?3 ";
				/*rowsDeteled = session.delete(
						deleteSumEntitesHQL,
						new Object[] { dto.getId().getHostId(),
								dto.getId().getEntityId(),
								dto.getId().getScreenId(),
								dto.getId().getUserId() },
						new Type[] { Hibernate.STRING, Hibernate.STRING,
								Hibernate.STRING, Hibernate.STRING });*/
				session.update(dto);
				for (int i = 0; i < sumEntitiesArray.length; i++) {
					personalEntityListExt.getId().setEntityId(sumEntitiesArray[i]);
					session.save(personalEntityListExt);
					session.flush();
			        session.clear();
				}
			}
			// Commits the transaction to the database
			tx.commit();
			log.debug(this.getClass().getName() + " - [saveSumRecord] - Exiting");
		} catch (Exception e) {
			try {
				if (tx != null) {
					tx.rollback();
				}
			} catch (HibernateException exception) {
				log
						.error("HibernateException occured in rolling back transaction. Cause : "
								+ exception.getMessage());
			}
			log.error(this.getClass().getName()
					+ " - Exception caught in [saveSumRecord] method "
					+ e.getMessage());
			throw SwtErrorHandler.getInstance().handleException(e,
					"saveSumRecord", EntityMonitorDAOHibernate.class);

		} finally {
			HibernateException hThrownException = JDBCCloser.close(session);

			if (hThrownException != null)
				throw new SwtException(hThrownException.getMessage());
		}
	}

	public boolean deleteSumRecord(String hostId, String userId,
			String screenId, String entityId) throws SwtException {
		Session session = null;
		// Holds the hibernate Transaction instance
		Transaction tx = null;
		String deleteSumEntitesHQL = null;
		String deletePersonalEntityHQL = null;
		int rowsSumDeteled;
		int rowsDeteled;
		
		try {
			log.debug(this.getClass().getName() + " - [saveSumRecord] - Entering");
			session = getHibernateTemplate().getSessionFactory().openSession();
			// Updates the details of the inputInterface
			tx = session.beginTransaction();
			
			// Delete all the personal entity list ext first before saving new entries
			deletePersonalEntityHQL = "from PersonalEntityList  p where p.id.hostId=?0 and p.id.entityId=?1 and p.id.screenId=?2 and p.id.userId = ?3 ";
			/*rowsDeteled = session.delete(
					deletePersonalEntityHQL,
					new Object[] { hostId, entityId, screenId, userId},
					new Type[] { Hibernate.STRING, Hibernate.STRING,
							Hibernate.STRING, Hibernate.STRING });
			// Delete all the personal entity list ext first before saving new entries
			deleteSumEntitesHQL = "from PersonalEntityListExt  p where p.id.hostId=? and p.id.aggrgEntityId=? and p.id.screenId=? and p.id.userId = ? ";
			rowsSumDeteled = session.delete(
					deleteSumEntitesHQL,
					new Object[] { hostId, entityId, screenId, userId},
					new Type[] { Hibernate.STRING, Hibernate.STRING,
							Hibernate.STRING, Hibernate.STRING });*/
			/*if (rowsDeteled > 0 && rowsSumDeteled > 0) {
				// Commits the transaction to the database
				tx.commit();
				return true;
			} else {
				tx.rollback();*/
				return false;
			//}
		} catch (Exception e) {
			try {
				if (tx != null) {
					tx.rollback();
				}
			} catch (HibernateException exception) {
				log
						.error("HibernateException occured in rolling back transaction. Cause : "
								+ exception.getMessage());
			}
			log.error(this.getClass().getName()
					+ " - Exception caught in [saveSumRecord] method "
					+ e.getMessage());
			throw SwtErrorHandler.getInstance().handleException(e,
					"saveSumRecord", EntityMonitorDAOHibernate.class);

		} finally {
			log.debug(this.getClass().getName() + " - [saveSumRecord] - Exiting");
			HibernateException hThrownException = JDBCCloser.close(session);

			if (hThrownException != null)
				throw new SwtException(hThrownException.getMessage());
		}
	}
}