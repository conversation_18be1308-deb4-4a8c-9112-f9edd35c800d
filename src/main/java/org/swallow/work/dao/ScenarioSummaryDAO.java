
/*
 * @(#)InputExceptionsDataDAO.java 1.0 23/08/06
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.work.dao;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import org.swallow.exception.SwtException;
import org.swallow.work.model.AlertInstance;
import org.swallow.work.model.AlertTreeVO;
import org.swallow.work.model.EntityScenarioCount;
import org.swallow.work.model.ScenarioAlertCount;
import org.swallow.work.model.ScenarioInstanceLog;
import org.swallow.work.model.ScenarioInstanceMessage;
import org.swallow.work.model.ScenariosSummary;

public interface ScenarioSummaryDAO {
	
	/**
	 * Return the number of existing record for each Scenario from P_SCENARIO_COUNTS
	 * @param baseQuery
	 * @return integer
	 * @throws SwtException 
	 * 
	 */
	public ScenariosSummary getScenariosSummaryInfoDetails(String roleId,String entityId,String threshold,String hideZero,String alertable, String selectedCurrencyGroup,String callOption, String selectedTab) throws SwtException;
	
	/**
	 * Return the number of existing counts grouped by Entity and Currency
	 * @param scenarioId
	 * @param roleId
	 * @param thresholdFlag
	 * @param groupingSummary
	 * @return
	 * @throws SwtException
	 */
		public ArrayList<EntityScenarioCount> getScenarioCountByEntituCurrency(String scenarioId,String roleId,String thresholdFlag,String groupingSummary, String entityId,String selectedSort,String selectedCurrencyGroup,String isAlertable, String selectedFilter) throws SwtException ;
		
	/**
	 * 	Return last run date and last run duration of the selected scenario 
	 * @param scenarioId
	 * @return
	 * @throws SwtException
	 */
	public String getSelectedScenarioLastRun(String scenarioId)  throws SwtException ;

	/**
	 * 
	 * @param hostId
	 * @param facilityId
	 * @param roleId
	 * @return String
	 * @throws SwtException 
	 */
	public String getFacilityAccess(String hostId, String facilityId,
			String roleId) throws SwtException;

	/**
	 * 
	 * @param roleId
	 * @param entityId
	 * @param currencyCode
	 * @param facilityId
	 * @param hostId
	 * @param valueDate
	 * @return
	 * @throws SwtException 
	 */
	public AlertTreeVO getAlertsScenarioCount(AlertInstance instance) throws SwtException;

	/**
	 * get instance list based on some criteria
	 * @param instanceCriteria
	 * @return
	 * @throws SwtException
	 */
	public List<AlertInstance> getInstanceList(AlertInstance instanceCriteria) throws SwtException;
	
	public void updateScenInstanceStatus(String id, String status) throws SwtException;
	
	public List<ScenarioInstanceLog>  getInstanceLogs(String instanceId) throws SwtException ;

	/**
	 * This method is used to get scenario instance access from P_NOTIFY_SCENARIO table
	 */
	public String getScenarioInstAccess(String scenarioId, String hostId, String roleId, String entityId) throws SwtException;
	
	
	/**
	 * This method is used to get scenario facility from p_scenario table
	 */
	public String getScenarioFacility(String scenarioId) throws SwtException;
	
	public String getInstAttXml(String instanceId) throws SwtException;
	
	public AlertInstance getInstanceNewData(String instanceId) throws SwtException;
	
	public ArrayList<ScenarioInstanceMessage> getInstanceMessages(String instanceId) throws SwtException;
	
	public String getHostName(String hostId) throws SwtException;
	
	/**
	 * This method is used to get scenario required access from P_NOTIFY_SCENARIO table
	 */
	public String getScenarioReqAccess(String scenarioId, String hostId, String roleId, String entityId) throws SwtException;
	

}