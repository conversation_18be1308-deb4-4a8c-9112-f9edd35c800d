/*
 * Created on Jul 31, 2007
 *
 * TODO To change the template for this generated file go to
 * Window - Preferences - Java - Code Style - Code Templates
 */
package org.swallow.work.dao;


import org.swallow.dao.DAO;

import org.swallow.exception.SwtException;

import org.swallow.util.SystemFormats;
import org.swallow.work.model.BookMonitorCurrencyDateTO;

import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 *
 * TODO To change the template for this generated type comment go to
 * Window - Preferences - Java - Code Style - Code Templates
 */
public interface SweepPriorCutOffDAO extends DAO {
	public Collection getSweepPriorToCutOffListUsingStoredProc( String hostId, String entityId, String subAccount, int leadTime, int extendDisplayTimeBy, SystemFormats format) throws SwtException;
	public Collection getMainAccountDetails( String hostId, String entityId, String mainAccountId) throws SwtException;

}
