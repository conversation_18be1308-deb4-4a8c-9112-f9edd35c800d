/*
 * @(#)MetagroupMonitorDAO.java 1.0 04/09/06
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.work.dao;

import java.util.ArrayList;
import java.util.Date;

import org.swallow.dao.DAO;
import org.swallow.exception.SwtException;
import org.swallow.util.SystemFormats;

/**
 * <pre>
 * DAO layer for MetaGroupMonitor Screen
 * Class used to
 *  - Display Meta Group Monitor Details
 *  - Display Group Monitor Details
 *  - Display Book Group Monitor Details
 *  - Get BookGroupMonitor JobStatusFlag 
 * </pre>
 */
public interface MetagroupMonitorDAO extends DAO {
	/**
	 * @param String
	 *            entityId
	 * @param String
	 *            currencyCode
	 * @param Date
	 *            valueDate-the date for which the calculations are to be done
	 * @param SystemFormats
	 *            format - the system format for date, currency etc.
	 * @param ArrayList
	 *            metaMonitorDetailsTotalList
	 * @param String
	 *            roleId
	 * @param String
	 *            locationId
	 * @return Collection of predicted balances and total
	 * @throws SwtException
	 *             if any
	 */
	public ArrayList<Object> getMetagroupMonitorDetailsUsingStoredProc(
			String entityId, String currencyCode, Date valueDate,
			SystemFormats format, ArrayList metaMonitorDetailsTotalList,
			String roleId, String locationId) throws SwtException;

	/**
	 * @param String
	 *            entityId
	 * @param String
	 *            currencyCode
	 * @param Date
	 *            valueDate-the date for which the calculations are to be done
	 * @param SystemFormats
	 *            format - the system format for date, currency etc.
	 * @param ArrayList
	 *            metaMonitorDetailsTotalList
	 * @param String
	 *            roleId
	 * @param String
	 *            locationId
	 * @param String
	 *            metagroupId
	 * @return Collection of predicted balances and total
	 * @throws SwtException
	 *             if any
	 */

	public ArrayList<Object> getGroupMonitorDetailsUsingStoredProc(
			String entityId, String currencyCode, Date valueDate,
			SystemFormats format, ArrayList groupMonitorDetailsTotalList,
			String roleId, String locationId, String metagroupId)
			throws SwtException;

	/**
	 * @param String
	 *            entityId
	 * @param String
	 *            currencyCode
	 * @param Date
	 *            valueDate-the date for which the calculations are to be done
	 * @param SystemFormats
	 *            format - the system format for date, currency etc.
	 * @param ArrayList
	 *            metaMonitorDetailsTotalList
	 * @param String
	 *            roleId
	 * @param String
	 *            locationId
	 * @param String
	 *            groupCode
	 * @return Collection of predicted balances and total
	 * @throws SwtException
	 *             if any
	 */

	public ArrayList<Object> getBookMonitorDetailsUsingStoredProc(
			String entityId, String currencyCode, Date valueDate,
			SystemFormats format, ArrayList bookMonitorDetailsTotalList,
			String roleId, String locationId, String groupCode)
			throws SwtException;

}
