/*
 * @(#)CentralBankMonitorDAO.java 1.0 12/07/10
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.work.dao;

import java.util.List;

import org.swallow.exception.SwtException;
import org.swallow.work.model.CorporateAccount;

/**
 * CorporateAccountDAO.java
 * 
 * Interface definitions for CorporateAccount DAO.
 * 
 * <AUTHOR>
 * @date July 12, 2010
 */
public interface CorporateAccountDAO {
	/**
	 * Method to get the corporate account details based on the value date
	 * passed
	 * 
	 * @param corporateAccount
	 * @return List<CorporateAccount>
	 * @throws SwtException
	 */
	List<CorporateAccount> getCorporateAccount(
			CorporateAccount corporateAccount) throws SwtException;

	/**
	 * Method to create or update the corporate account details for a value date
	 * 
	 * @param corporateAccount
	 * @param saveFlag
	 * @throws SwtException
	 */
	void saveCorporateAccount(CorporateAccount corporateAccount,String saveFlag)
			throws SwtException;

	/**
	 * Method to delete the corporate account details for a value date
	 * 
	 * @param corporateAccount
	 * @throws SwtException
	 */
	void deleteCorporateAccount(CorporateAccount corporateAccount)
			throws SwtException;
}
