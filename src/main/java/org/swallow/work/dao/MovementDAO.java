/*
 * @(#)MovementDAO.java 03/01/06
 * 
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 * 
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */

package org.swallow.work.dao;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.HashMap;
import java.util.List;

import org.swallow.dao.DAO;
import org.swallow.exception.SwtException;
import org.swallow.maintenance.model.Currency;
import org.swallow.maintenance.model.Entity;
import org.swallow.maintenance.model.MsdDisplayColumns;
import org.swallow.maintenance.model.Party;
import org.swallow.model.ScreenInfo;
import org.swallow.work.model.CrossReference;
import org.swallow.work.model.Match;
import org.swallow.work.model.MatchDriver;
import org.swallow.work.model.MatchNote;
import org.swallow.work.model.Movement;
import org.swallow.work.model.MovementNote;
import org.swallow.work.model.MsdAdditionalColumns;

/**
 * <AUTHOR>
 * 
 * <Pre>
 * DAO layer for Movement
 * 
 * Method to used to 
 * - Display Movement details
 * - Get Movement details
 * - save Movement
 * - Update Movement
 * - Save Movement
 * 
 * </Pre>
 */
public interface MovementDAO extends DAO {
	/**
	 * @param entityId
	 * @param hostId
	 * @return
	 * @throws SwtException
	 */
	public List<Currency> getCurrencyList(String entityId, String hostId)
			throws SwtException;

	/**
	 * @param hostId
	 * @param entityId
	 * @param currencyCode
	 * @return
	 * @throws SwtException
	 */
	public String getCurrency(String currencyCode) throws SwtException;

	/**
	 * @param hostId
	 * @param entityId
	 * @param currencyCode
	 * @param date
	 * @param positionLevel
	 * @return
	 * @throws SwtException
	 */
	public int getOutMovbyposlevbydate(String hostId, String entityId,
			String currencyCode, Date date, Integer positionLevel, boolean isAll)
			throws SwtException;

	/**
	 * @param hostId
	 * @param entityId
	 * @param currencyCode
	 * @param date
	 * @return
	 * @throws SwtException
	 */
	public int getOutMovbyposlevinterimbydate(String hostId, String entityId,
			String currencyCode, Date date, Integer positionLevelFirst,
			Integer positionLevelFinal, boolean isAll) throws SwtException;

	/**
	 * @param hostId
	 * @param entityId
	 * @param currCode
	 * @param roleId
	 * @return
	 * @throws SwtException
	 */
	public String getOutStandingStatusbyRole(String hostId, String entityId,
			String currCode, String roleId) throws SwtException;

	/**
	 * @param hostId
	 * @param entityId
	 * @param movementId
	 * @return
	 * @throws SwtException
	 */
	public Movement getMovement(String hostId, String entityId, Long movementId)
			throws SwtException;

	/**
	 * 
	 * @param hostId
	 * @param entityId
	 * @param movementIds
	 * @return
	 * @throws SwtException
	 */
	public List getMovement(String hostId, String entityId, String movementIds)
			throws SwtException;

	/**
	 * @param hostId
	 * @param entityId
	 * @param movementId
	 * @throws SwtException
	 */

	/**
	 * Updates all the movements in the list in one strock
	 * 
	 * @param movColl
	 * @throws SwtException
	 * <AUTHOR> 29th Jan 08
	 */
	public void updateMovements(Collection movColl) throws SwtException;

	/**
	 * @param match
	 * @return
	 * @throws SwtException
	 */
	public Long createMatch(Match match) throws SwtException;

	/**
	 * 
	 * @param hostId
	 * @param movementId
	 * @return
	 * @throws SwtException
	 */
	public Movement getMovementDetails(String hostId, Long movementId)
			throws SwtException;

	/**
	 * 
	 * @param hostId
	 * @param entityId
	 * @return
	 * @throws SwtException
	 */
	public Collection getBookCodeList(String hostId, String entityId)
			throws SwtException;

	/**
	 * 
	 * @param hostId
	 * @param entityId
	 * @return
	 * @throws SwtException
	 */
	public Collection getPartyList(String hostId, String entityId)
			throws SwtException;


	/**
	 * 
	 * @param movement
	 * @throws SwtException
	 */
	public void saveMovementDetails(Movement movement) throws SwtException;

	/**
	 * *
	 * 
	 * <AUTHOR>
	 * 
	 * TODO To change the template for this generated type comment go to Window -
	 * Preferences - Java - Code Style - Code Templates
	 */
	public void updateMovementDetails(Movement movement) throws SwtException;

	/**
	 * 
	 * @param hostId
	 * @return
	 * @throws SwtException
	 */
	public Collection getMatchDriverDetails(String hostId) throws SwtException;

	/**
	 * 
	 * @param matchDriver
	 * @throws SwtException
	 */
	public void updateMatchDriverDetails(MatchDriver matchDriver)
			throws SwtException;

	/**
	 * 
	 * @param hostId
	 * @param entityId
	 * @param currCode
	 * @param movType
	 * @param inputSource
	 * @return
	 * @throws SwtException
	 */
	// The function is overhauled so that the filtering parameters are checked
	// at query level
	public Collection getAccountIdDetails(String hostId, String entityId,
			String currCode, String movType, String inputSource)
			throws SwtException;

	/**
	 * 
	 * @param matchDriver
	 * @return
	 * @throws SwtException
	 */
	public void saveMatchDriverDetails(MatchDriver matchDriver)
			throws SwtException;

	/**
	 * 
	 * @param movementNote
	 * @throws SwtException
	 */
	public void saveNotesDetails(MovementNote movementNote) throws SwtException;

	/**
	 * 
	 * @param hostId
	 * @param entityId
	 * @param counterPartyId
	 * @return
	 * @throws SwtException
	 */
	public Collection getCounterPartyRecord(String hostId, String entityId,
			String counterPartyId) throws SwtException;
	/**
	 * 
	 * @param hostId
	 * @param entityId
	 * @param counterPartyId
	 * @return
	 * @throws SwtException
	 */
	public Party getPartyRecord(String hostId, String entityId,
			String partyId) throws SwtException ;

	/**
	 * 
	 * @param hostId
	 * @param entityId
	 * @param counterPartyId
	 * @return
	 * @throws SwtException
	 */
	public Collection getBeneficiaryRecord(String hostId, String entityId,
			String counterPartyId) throws SwtException;

	/**
	 * 
	 * @param hostId
	 * @param entityId
	 * @param counterPartyId
	 * @return
	 * @throws SwtException
	 */
	public Collection getCustodianRecord(String hostId, String entityId,
			String counterPartyId) throws SwtException;

	/**
	 * 
	 * @param hostId
	 * @param entityId
	 * @param MatchingParty
	 * @return
	 * @throws SwtException
	 */
	public Collection getMatchingPartyRecord(String hostId, String entityId,
			String matchingParty) throws SwtException;

	/**
	 * @param hostId
	 * @param entityId
	 * @return
	 * @throws SwtException
	 */
	public Entity getEntityDetail(String hostId, String entityId)
			throws SwtException;

	/**
	 * @param hostId
	 * @param entityId
	 * @param currencyId
	 * @return
	 * @throws SwtException
	 */
	public Currency getCurrencyDetail(String hostId, String entityId,
			String currencyId) throws SwtException;

	/**
	 * 
	 * @param hostId
	 * @param movementId
	 * @return
	 * @throws SwtException
	 */
	public Collection getNoteDetails(String hostId, Long movementId)
			throws SwtException;

	/**
	 * @desc This method will return the List of Currencies for the given
	 *       hostId, entityId and Currency Group Id
	 * @param hostId -
	 *            HostID
	 * @param entityId -
	 *            EntityID
	 * @param currencyGroupId -
	 *            Currency Group Id
	 * @return List - List
	 * @throws SwtException -
	 *             SwtException
	 */
	List getCurrencyList(String hostId, String entityId, String currencyGroupId)
			throws SwtException;

	/**
	 * 
	 * @param hostId -
	 *            HostId
	 * @param entityId -
	 *            EntityId
	 * @param currencyCode -
	 *            currency Code
	 * @param date -
	 *            Date
	 * @param positionLevel
	 *            -Position Level Id
	 * @param isAll -
	 *            boolean
	 * @return - count of open Movements satisfying given condition
	 * @throws SwtException -
	 *             SwtException
	 */
	int getOpenMovementsbydate(String hostId, String entityId,
			String currencyCode, Date date, Integer positionLevel, boolean isAll)
			throws SwtException;

	/**
	 * @desc This method will delete all the Match Objects contained in
	 *       deleteMatchList
	 * @param deleteMatchList -
	 *            List contains Match Objects to be deleted
	 * @param deleteMatchNotesList -
	 *            List contains MatchNote Objects to be deleted
	 * @throws SwtException -
	 *             SwtException
	 */
	void deleteOtherMatches(List deleteMatchList, List deleteMatchNotesList)
			throws SwtException;

	/**
	 * @desc this method fetches all the Movements satisfying the conditons for
	 *       Offered Match , Suspend Match, Confirm
	 * @param hostId
	 * @param entityId
	 * @param movementIds
	 * @return
	 * @throws SwtException
	 */
	List getAllMovement(String hostId, String entityId, String movementIds)
			throws SwtException;

	/**
	 * @desc This method fetches the Match for given hostId, entityId and
	 *       matchId
	 * @param hostId -
	 *            HostId
	 * @param entityId -
	 *            EntityId
	 * @param matchId -
	 *            MatchId
	 * @return Match Object
	 * @throws SwtException -
	 *             SwtException
	 */
	Match getMatch(String hostId, String entityId, Long matchId)
			throws SwtException;

	/**
	 * @desc - This method will update the Match Object
	 * @param match -
	 *            Match Object
	 * @throws SwtException -
	 *             SwtException
	 */
	void updateMatch(Match match) throws SwtException;

	/**
	 * This function gets messageId for a particular movement form the table
	 * P_MOVEMENT_MESSAGE
	 */
	public Collection getMovementMessageId(String hostId, Long movementId)
			throws SwtException;

	/**
	 * 
	 * @param hostId -
	 *            HostId
	 * @param entityId -
	 *            EntityId
	 * @param roleId -
	 *            roleId
	 * @param currencyGrpId -
	 *            currency Group Id
	 * @param date -
	 *            Date
	 * @param highestPosLvl -
	 *            Highest defined Position Level for that Entity
	 * @param isAll -
	 *            String
	 * @param applyCurrencyThreshold -
	 *            Apply Currency Threshold
	 * @return - Collection of EntityPositionLevel Objects
	 * @throws SwtException -
	 *             SwtException
	 */
	public ArrayList<Object> getOpenMovementsCountdate(String hostId,
			String entityId, String roleId, String currencyGrpId, Date date,
			int highestPosLvl, String isAll, String applyCurrencyThreshold)
			throws SwtException;

	/**
	 * This method returns the collection of messages attached with the movemetn
	 * with given movementId.
	 * 
	 * @param movementId
	 * @return
	 * @throws SwtException
	 */
	public Collection getMovMessageColl(String movementId) throws SwtException;

	/* END: Code change at offshore for Integration */

	/**
	 * @param hostId
	 * @param entityId
	 * @param matchId
	 * @return
	 */
	public Collection getMovementsForMatch(String hostId, String entityId,
			Long matchId) throws SwtException;

	/**
	 * @param newMatch
	 */
	public void saveRolledMatch(Match newMatch) throws SwtException;

	/**
	 * @param matchNote
	 */
	public void saveMatchNotes(MatchNote matchNote) throws SwtException;

	/*
	 * Start: Modified by RK on 26-Mar-2012
	 */

	// Modified for Mantis 1443, add the scenario id
	/**
	 * <pre>
	 * This method gets movement details and total no of movements
	 * This method handles two type of request.
	 * 1. Get movement details and total no of movements
	 * 2. No of movements
	 * </pre>
	 * 
	 * @param hostId
	 * @param entityId
	 * @param currCode
	 * @param accountId
	 * @param valueDate
	 * @param accountType
	 * @param balType
	 * @param posLevelId
	 * @param pageSize
	 * @param currentPage
	 * @param isAllStr
	 * @param filterCriteria
	 * @param sortCriteria
	 * @param sourceScreen
	 * @param totalCount
	 * @param lstMonitorMovement
	 * @param dataFetchIndicator
	 * @param applyCurrencyThreshold
	 * @param extraValues
	 * @return int
	 * @throws SwtException
	 */

	public HashMap<String, Object> getMonitorMovements(String hostId, String entityId,
			String currCode, String accountId, Date valueDate,
			String accountType, String balType, int posLevelId, int pageSize,
			int currentPage, String isAllStr, String filterCriteria,
			String openMovementFlag, String sortCriteria, String sourceScreen,
			ArrayList<Movement> lstMonitorMovement,
			String dataFetchIndicator, String applyCurrencyThreshold, String userId, String profileId, String...extraValues)
			throws SwtException;

	public String getValueDateField(String hostId, String entityId,
			Long movementId) throws SwtException;

	public void updateOpenUnopenFlag(String hostId, String entityId,
			Long movementId, String openFlag, String updateUser)
			throws SwtException;

	public String getPositionLevelInternalExternal(String hostId,
			String entityId, int positionLevel) throws SwtException;

	/**
	 * @param movementIds
	 * @param entityId
	 * @param hostId
	 * @return boolean
	 */
	public boolean checkMovementStatus(String movementIds, String entityId,
			String hostId) throws SwtException;

	/**
	 * Method to check external position level to change the external balance.
	 * 
	 * @param entity
	 * @return boolean
	 */
	public boolean checkExternalPositionLevel(Entity entity)
			throws SwtException;

	/**
	 * This Method is used to return the Cross Reference Object List for
	 * corresponding Movement
	 * 
	 * @param host
	 * @param entity
	 * @param movement
	 * @throws SwtException
	 */
	public ArrayList<CrossReference> getCrossReference(String host,
			String entity, Long movement) throws SwtException;

	/**
	 * Method to get External Balance for selected entity
	 * 
	 * @param entity
	 * @return ExternalBalance
	 */
	public String getEntityDefaultPositionlevel(Entity entity)
			throws SwtException;
	

	/*
	 * Start:Code Modified for Mantis 1270 by Chinniah on 5-Aug-2012: Data
	 * Archive setup: Remove redundant fields from Archive setup screen
	 */
	/**
	 * This method used to get the archived movements from the archive schema
	 * 
	 * @param entityId
	 * @param movementId
	 * @param archiveId
	 * @return Movement
	 * @throws SwtException
	 */
	public Movement getArchiveMovementDetails(String entityId, Long movementId,
			String archiveId) throws SwtException;
	/*
	 * End:Code Modified for Mantis 1270 by Chinniah on 5-Aug-2012: Data Archive
	 * setup: Remove redundant fields from Archive setup screen
	 */
         /**
	 * This method used to get the match quality from the p_match table
	 * 
	 * @param matchId
	 * @param hostId
	 * @param entityId
	 * @throws SwtException
	 */
	public String getMatchQuality(Long matchId,String hostId,String entityId) throws SwtException;
	
	/**
	 * This method used to set the original counter party_id if the counter party_id is changed
	 * 
	 * @param hostId
	 * @param entityId
	 * @param movementId
	 * @param originalCounterPartyId
	 * @throws SwtException
	 */
	public void updateOriginalValues(Movement newMovement, Movement oldMovement)  throws SwtException;
	

	
	public void saveProfileAdditionalCols(String hostId, String userId, String profileId, List<MsdAdditionalColumns> listMsd) throws SwtException;
	
	public List getSavedProfiles(String hostId, String userId) throws SwtException ;
	
	public Collection<MsdAdditionalColumns> getAdditionalsColsList(String hostId, String profileId, String userId) throws SwtException;
	
	public void deleteProfileData(String hostId, String profileId, String userId) throws SwtException;

	public void crudAddColsMapping(List<MsdAdditionalColumns> listColAdd, List<MsdAdditionalColumns> listColUpdate,
			List<MsdAdditionalColumns> listColDelete) throws SwtException ;
	
	public List getTableColumns(String table) throws SwtException;
	
	public List getTableNames() throws SwtException;
	
	public Collection<MsdDisplayColumns> getMsdDisplayColsList() throws SwtException;
	
	public HashMap<String, String> getMsdDisplayColumnsLbl(String tableName) throws SwtException;
	
	public 	ArrayList<String> getEntityPosLevelList(String entityId) throws SwtException;

	public 	ArrayList<String> getSourceList(Date startDate, Date endDate) throws SwtException;

	public 	ArrayList<String> getFormatList(Date startDate, Date endDate) throws SwtException;
	/**
	 * Method to delete the screen info
	 * 
	 * @throws SwtException
	 *             Collection<ScreenInfo>
	 */
	@SuppressWarnings("unchecked")
	public void deleteMsdScreenInfo(List<ScreenInfo> listScreenInfo) throws SwtException;
}
