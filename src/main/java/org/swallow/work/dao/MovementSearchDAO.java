/*
 * @(#)MovementSearchDAO.java 1.0 
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.work.dao;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

import org.swallow.dao.DAO;
import org.swallow.exception.SwtException;
import org.swallow.util.LabelValueBean;


/**
 * <AUTHOR>
 *
 * TODO To change the template for this generated type comment go to Window -
 * Preferences - Java - Code Style - Code Templates
 */
public interface MovementSearchDAO extends DAO {
    //Populating the combo boxes acc. to the entityid and hostid(9 combo boxes)
    public Collection getCurrencyDetails(String hostid, String entityid);

    public Collection getBeneficiaryDetails(String hostid, String entityid);

    public Collection getCustodianDetails(String hostid, String entityid);

    public Collection getBookCodeDetails(String hostid, String entityid);

    public Collection getGroupDetails(String hostid, String entityid);

    public Collection getMetaGroupDetails(String hostid, String entityid);

    public Collection getAccountDetails(String hostid, String entityid);

    
    /**
     *
     * @param hostid
     * @param entityid
     * @param currCode
     * @return
     */
    public Collection getAccountDetails(String hostid, String entityid,
        String currCode);
    /*
	 * Start:code modified by Sandeep kumar for Mantis 1894:Movement:
	 * Account drop down displays other currency accounts
	 */
   
    /**
     * This method is used to get account class details
     * @param hostId
     * @param entityId
     * @param accountClass
     * @param currCode
     * @return
     */
    public Collection getAccountClassDetails(String hostId, String entityId,
        String accountClass,String currCode)throws SwtException;
    /*
	 * End:code modified by Sandeep kumar for Mantis 1894:Movement:
	 * Account drop down displays other currency accounts
	 */
    public Collection getCounterPartyDetails(String hostid, String entityid);

    /**
     *
     * @param listToPopulate
     * @param hostid
     * @param entityid
     * @param status
     * @param movementtype
     * @param sign
     * @param predictstatus
     * @param amountover
     * @param amountunder
     * @param currencycode
     * @param beneficiaryId
     * @param custodianId
     * @param poslevel
     * @param accountId
     * @param group
     * @param metaGroup
     * @param bookCode
     * @param sortorder
     * @param fromdate
     * @param todate
     * @param reference
     * @param msgtype
     * @param inputdate
     * @param counterparty
     * @param fintrade
     * @param timefrom
     * @param timeto
     * @param format
     * @param currentPage
     * @param incrementFactor
     * @param filterSortStatus
     * @return @throws
     *         SwtException
     */
    public int fetchDetails(List listToPopulate, String hostid,
        String entityid, String status, String movementtype, String sign,
        String predictstatus, Double amountover, Double amountunder,
        String currencycode, String beneficiaryId,
        String custodianId, Integer poslevel, String accountId, String group,
        String metaGroup, String bookCode, String sortorder, String fromdate,
        String todate, String reference, String msgtype, String inputdate,
        String counterparty, String fintrade, String format, String timefrom,
        String timeto, int currentPage, int incrementFactor,
        String filterSortStatus, int totalCount) throws SwtException;

    /**
     * @return
     */
    public List getArchiveList() throws SwtException;
    
    public ArrayList<LabelValueBean>  getPositionLevelList(String hostId) throws SwtException;
      }
