/*
 * @(#)SweepDetailDAO.java 23/01/2006
 * 
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 * 
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */

package org.swallow.work.dao;
import java.util.Collection;
import java.util.List;
import org.swallow.dao.DAO;
import org.swallow.exception.SwtException;
import org.swallow.maintenance.model.AcctMaintenance;
import org.swallow.util.SystemFormats;
import org.swallow.work.model.Sweep;
import org.swallow.work.model.SweepAmount;
import org.swallow.work.model.SweepDetail;
import org.swallow.work.model.SweepNote;

/**
 * This class used as interface between Dao hibernate and Impl.java
 */
public interface SweepDetailDAO extends DAO {
	/**
	 * @param entityId
	 * @param hostId
	 * @param accIds
	 * @return
	 */
	public List<AcctMaintenance> getAccountDetails(String entityId, String hostId,
			String accIds) throws SwtException;

	public Double getPredictBalance(String entityId, String hostId,
			String currencyId, String accountId, String date, String format)
			throws SwtException;

	/*
	 * Start Code Added for Mantis 1370 by Chinna to get the external balance on
	 * 7-MAR-2011
	 */
	/**
	 * @param entityId
	 * @param hostId
	 * @param currencyId
	 * @param accountId
	 * @param date
	 * @param format
	 * 
	 */
	//used to get the External Balance
	public Double getExternalBalance(String entityId, String hostId,
			String currencyId, String accountId, String date, String format)
			throws SwtException;

	/*
	 * End Code Added for Mantis 1370 by Chinna to get the external balance on
	 * 7-MAR-2011
	 */

	/**
	 * @param entityId
	 * @param hostId
	 * @param formatId
	 * @return
	 */
	public String getAuthFlag(String entityId, String hostId, String formatId)
			throws SwtException;

	/**
	 * @param currCode
	 * @param roleId
	 * @return
	 */
	public Double getSweepLimit(String currCode, String roleId)
			throws SwtException;

	/**
	 * @param entityId
	 * @param hostId
	 * @param sweepId
	 * @return
	 */
	public List getSweepDetails(String entityId, String hostId, Long sweepId)
			throws SwtException;

	/**
	 * @param entityId
	 * @param hostId
	 * @param sweepId
	 * @param archiveId
	 * @return
	 */
	public List getSweepDetailsArchive(String entityId, String hostId, Long sweepId, String archiveId)
			throws SwtException;

	/**
	 * @param sweepAct1
	 * @param sweepAct2
	 * @param sweepAmt
	 * @param hostId
	 * @param userId
	 * @param format
	 */
	public List generateSweep(SweepDetail sweepAct1, SweepDetail sweepAct2,
			SweepAmount sweepAmt, String hostId, String userId, String format)
			throws SwtException;

	/**
	 * @param sweep
	 * @param systemFormats
	 * @param sweepAmount
	 * @param authFlagDr
	 * @param authFlagCr
	 * @return
	 */
	public int processSweep(Sweep sweep, SystemFormats systemFormats,
			Double sweepAmount, String authFlagCr, String authFlagDr)
			throws SwtException;

	/**
	 * @param entity1
	 * @param hostId
	 * @return
	 */
	public String getOffsetTime(String entity, String hostId)
			throws SwtException;

	public void saveNotesDetails(SweepNote sweepNote) throws SwtException;

	public Collection getsweepMessageDaoColl(String sweepId)
			throws SwtException;


	/**
	 * This method is used to update the sweep details.
	 * 
	 * @param sweep
	 * @throws SwtException
	 */
	public void updateSweepDetails(Sweep sweep) throws SwtException;
	
	/**
	 * get used sweep formats for selected sweep id 
	 * @param sweepId
	 * @return
	 * @throws SwtException
	 */
	public String getSweepFormatFromSweep(String sweepId) throws SwtException;



}
