/*
 *@SweepDAO.java
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */

package org.swallow.work.dao;

import java.util.Collection;
import java.util.Date;
import java.util.List;

import org.swallow.dao.DAO;
import org.swallow.exception.SwtException;
import org.swallow.util.SystemFormats;
import org.swallow.work.service.SweepDetailVO;

/**
 * SweepDAO.java
 * 
 * This interface has methods that are used for accessing the persistent storage
 * such as database which helps client to create, retrieve and persists data to
 * the Persistent Object.
 * 
 */

public interface SweepDAO extends DAO {
	/**
	 * @param entityId
	 * @param hostId
	 * @return
	 * @throws SwtException
	 */
	public List getCountryCode(String entityId, String hostId)
			throws SwtException;

	/**
	 * @param entityId
	 * @param hostId
	 * @return
	 * @throws SwtException
	 */
	public List getCurrencyList(String entityId, String hostId)
			throws SwtException;

	/**
	 * @param entityId
	 * @param hostId
	 * @param currencyId
	 * @param acctType
	 * @return
	 * @throws SwtException
	 */
	public Collection getAccountIdDetails(String entityId, String hostId,
			String currencyId, String acctType) throws SwtException;

	/**
	 * @param acctId
	 * @param entityId
	 * @param hostId
	 * @return
	 * @throws SwtException
	 */
	public Double getTargetBalance(String acctId, String entityId, String hostId)
			throws SwtException;

	/**
	 * @param acctId
	 * @param entityId
	 * @param hostId
	 * @return
	 * @throws SwtException
	 */
	public String getLevel(String acctId, String entityId, String hostId)
			throws SwtException;

	/**
	 * @param acctId
	 * @param entityId
	 * @param hostId
	 * @return
	 * @throws SwtException
	 */
	public String getCutOff(String acctId, String entityId, String hostId)
			throws SwtException;

	/**
	 * @param entityId
	 * @param hostId
	 * @param currencyId
	 * @param acctType
	 * @return
	 * @throws SwtException
	 */
	public Collection getSubAccountIdDetails(String entityId, String hostId,
			String currencyId, String acctType) throws SwtException;

	/**
	 * @param entityId
	 * @param hostId
	 * @param currencyId
	 * @param acctType
	 * @param accountId
	 * @param todaydate
	 * @return
	 */
	public Collection getSweepDetails(String entityId, String hostId,
			String currencyId, String acctType, String accountId, Date todaydate)
			throws SwtException;

	/**
	 * @param entityId
	 * @param hostId
	 * @param currencyId
	 * @param acctType
	 * @param todaydatePlusWeek
	 * @param accountId
	 * @return
	 */
	public Collection getSweepDetailsOneWeek(String entityId, String hostId,
			String currencyId, String acctType, Date todaydatePlusWeek,
			String accountId) throws SwtException;

	/**
	 * @param currentEntity
	 * @param accountId1
	 * @param accountId2
	 * @param entityId1
	 * @param entityId2
	 * @param valueDate
	 * @param currentSystemFormats
	 * @return
	 */
	public String getSweepStatus(String currentEntity, String accountId1,
			String accountId2, String entityId1, String entityId2,
			String valueDate, SystemFormats currentSystemFormats)
			throws SwtException;

	/**
	 * @param hostId
	 * @param entityId
	 * @param currencyCode
	 * @param acctType
	 * @return
	 */
	public List getCurrentList(String hostId, String entityId,
			String currencyCode, String acctType) throws SwtException;

	/**
	 * @param hostId
	 * @param entityId
	 * @param acctId
	 * @param startDate
	 * @param acctType
	 * @return
	 */
	public Double getStartBalance(String hostId, String entityId,
			String acctId, Date startDate, String acctType) throws SwtException;

	/**
	 * @param hostId
	 * @param entityId
	 * @param currencyCode
	 * @param todayDate
	 * @param weekDate
	 * @param predictStatus
	 * @return
	 */
	public List getMovementRecords(String hostId, List acctList,
			Date todayDate, Date weekDate, String predictStatus)
			throws SwtException;

	public List getAccountList(String hostId, String entityId,
			String currencyCode, String acctType) throws SwtException;

	/**
	 * This method is used to get the sweep details
	 * 
	 * @param entityId
	 * @param currencyCode
	 * @param acctType
	 * @param date
	 * @param format
	 * @param sweepTabFlag
	 * @return
	 * @throws SwtException
	 * 
	 */
	/**
	 * Start:Code Modified For Mantis 1901 by Sudhakar on 03-May-2012: Implement
	 * T1 and T2 Tabs in addition to the 7 tabs
	 * 
	 */
	public SweepDetailVO getSweepDetailsUsingStoredProc(String entityId, String againstEntityId,
			String currencyCode, String acctType, Date date,
			SystemFormats format, String sweepTabFlag) throws SwtException;
	/**
	 * End:Code Modified For Mantis 1901 by Sudhakar on 03-May-2012: Implement
	 * T1 and T2 Tabs in addition to the 7 tabs
	 * 
	 */
    /**
     * this method get list of settelment method from P_SWEEP_SETTLEMENT_METHOD table
     * @param entityId
     * @return
     * @throws SwtException
     */
	public Collection getSweepSettlementMethodList(String entityId) throws SwtException;
}
