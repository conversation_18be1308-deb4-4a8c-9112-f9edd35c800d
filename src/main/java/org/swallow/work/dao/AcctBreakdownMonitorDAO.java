/**
 * @(#)AcctBreakdownMonitorDAO.java 1.0 / Mar 7, 2012
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.work.dao;

import java.util.Collection;

import org.swallow.dao.DAO;
import org.swallow.exception.SwtException;
import org.swallow.maintenance.model.AcctMaintenance;
import org.swallow.work.model.AcctBreakdownMonitor;

/**
 * AcctBreakdownMonitorDAO.java
 * 
 * <pre>
 * This interface has methods to perform Account Breakdown Monitor
 * related database operation like,
 * 
 * - Get Account list based on selected entity, currency and account class
 * - Get account breakdown details for grid
 * - Update account details
 * </pre>
 * 
 * <AUTHOR> R / Mar 7, 2012
 * @version SmartPredict-1054
 */
public interface AcctBreakdownMonitorDAO extends DAO {

	/**
	 * This method gets account details belongs to selected entity, currency and
	 * account class. The account status should be either open or blocked. They
	 * should not be closed accounts.
	 * 
	 * @param acctBreakdownMonitor
	 * @return Collection<AcctMaintenance>
	 * @throws SwtException
	 */
	public Collection<AcctMaintenance> getAcctList(
			AcctBreakdownMonitor acctBreakdownMonitor) throws SwtException;

	/**
	 * This method is used to get account breakdown monitor details based on the
	 * selected criteria.
	 * 
	 * @param acctBreakdownMonitor
	 * @throws SwtException
	 */
	public void getAllBalancesUsingStoredProc(
			AcctBreakdownMonitor acctBreakdownMonitor) throws SwtException;

	/**
	 * This method is used to update account details, like sum flag or loro to
	 * predicted flag
	 * 
	 * @param hostId
	 * @param entityId
	 * @param acctId
	 * @param sumFlag
	 * @param loroToPredicted
	 * @throws SwtException
	 */
	public void updateAccount(String hostId, String entityId, String acctId,
			String sumFlag, String loroToPredicted) throws SwtException;
}