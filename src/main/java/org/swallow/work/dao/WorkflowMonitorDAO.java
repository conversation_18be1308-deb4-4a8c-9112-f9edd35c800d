/*
 * @(#)WorkflowMonitorDao.java
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.work.dao;

import java.util.Date;
import java.util.HashMap;

import org.swallow.exception.SwtException;
import org.swallow.work.model.OracleTimeDTO;

/**
 * This is a DAO interface which contains prototypes of all the function of DAO.
 * 
 * <AUTHOR> Systems
 * @version 1.0 04 December 2006
 */

public interface WorkflowMonitorDAO {

	/**
	 * This method used to execute the DB procedure for calculating the workflow
	 * monitor details.
	 * 
	 * @param hostId
	 * @param entityId
	 * @param currencyGrpId
	 * @param roleId
	 * @param sysDate
	 * @param sysDatePlusOne
	 * @param applyCurrencyThreshold
	 * @param userId
	 * @return
	 * @throws SwtException
	 */
	public HashMap<String, Object> getWorkflowMonitorDetailsFromProc(
			String hostId, String entityId, String currencyGrpId,
			String roleId, Date sysDate, String applyCurrencyThreshold,
			String userId) throws SwtException;

	/**
	 * This method used to extract the All Entity Option from the S_ROLE.
	 * 
	 * @param roleId
	 * @return
	 * @throws SwtException
	 */
	public boolean getAllEntityOption(String roleId) throws SwtException;

	public OracleTimeDTO getOracleSystemTime() throws SwtException;

	public String getEntityOffset(String entityId) throws SwtException;

}