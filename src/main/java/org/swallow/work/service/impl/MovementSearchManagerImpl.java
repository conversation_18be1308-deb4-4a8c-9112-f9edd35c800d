/*
 * @(#)MovementSearchManagerImpl.java 1.0 
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.work.service.impl;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.maintenance.model.AcctMaintenance;
import org.swallow.maintenance.model.BookCode;
import org.swallow.maintenance.model.Currency;
import org.swallow.maintenance.model.Group;
import org.swallow.maintenance.model.MetaGroup;
import org.swallow.maintenance.model.Party;
import org.swallow.util.LabelValueBean;
import org.swallow.work.dao.MovementSearchDAO;
import org.swallow.work.service.MovementSearchManager;


/**
 * <AUTHOR>
 *
 * TODO To change the template for this generated type comment go to Window -
 * Preferences - Java - Code Style - Code Templates
 */
@Component("movementsearchManager")
public class MovementSearchManagerImpl implements MovementSearchManager {
    private final Log log = LogFactory.getLog(MovementSearchManagerImpl.class);
    @Autowired
    private MovementSearchDAO movementsearchDAO;

    public void setMovementsearchDAO(MovementSearchDAO movementsearchDAO) {
        this.movementsearchDAO = movementsearchDAO;
    }


    public ArrayList getCurrencyDetails(String hostId, String entityId)
        throws SwtException {
        log.debug("entering currencydetails method");

        ArrayList currList = new ArrayList();
        try {
			Iterator itr = (movementsearchDAO.getCurrencyDetails(hostId, entityId)).iterator();
			Currency curr = null;

			currList.add(new LabelValueBean("", ""));

			while (itr.hasNext()) {
				curr = (Currency) (itr.next());
				
				currList.add(new LabelValueBean(curr.getCurrencyMaster()
													.getCurrencyName(),
						curr.getCurrencyMaster().getCurrencyCode()));
			}
		} catch (Exception exp) {
            log.debug(
                "Exception Catch in MovementSearchManagerImpl.'getCurrencyDetails' method : " +
                exp.getMessage());
            throw SwtErrorHandler.getInstance().handleException(exp,
                "getCurrencyDetails", MovementSearchManagerImpl.class);
        }

        log.debug("exiting currencydetails method");

        return currList;
    }

    public ArrayList getBeneficiaryDetails(String hostId, String entityId)
        throws SwtException {
        log.debug("entering beneficiarydetails method");

        ArrayList benList = new ArrayList();
        try {
			Iterator itr = (movementsearchDAO.getBeneficiaryDetails(hostId, entityId)).iterator();
			Party party = null;

			benList.add(new LabelValueBean("", ""));

			while (itr.hasNext()) {
				party = (Party) (itr.next());
				benList.add(new LabelValueBean(party.getPartyName(),
						party.getId().getPartyId()));
			}
		} catch (Exception exp) {
            log.debug(
                "Exception Catch in MovementSearchManagerImpl.'getBeneficiaryDetails' method : " +
                exp.getMessage());
            throw SwtErrorHandler.getInstance().handleException(exp,
                "getBeneficiaryDetails", MovementSearchManagerImpl.class);
        }

        log.debug("exiting beneficiarydetails method");

        return benList;
    }

    public ArrayList getCounterPartyDetails(String hostId, String entityId)
        throws SwtException {
        log.debug("entering counterparty method");

        ArrayList counterList = new ArrayList();
        try {
			Iterator itr = (movementsearchDAO.getCounterPartyDetails(hostId,
					entityId)).iterator();
			Party party = null;

			counterList.add(new LabelValueBean("", ""));

			while (itr.hasNext()) {
				party = (Party) (itr.next());
				counterList.add(new LabelValueBean(party.getPartyName(),
						party.getId().getPartyId()));
			}
		} catch (Exception exp) {
            log.debug(
                "Exception Catch in MovementSearchManagerImpl.'getCounterPartyDetails' method : " +
                exp.getMessage());
            throw SwtErrorHandler.getInstance().handleException(exp,
                "getCounterPartyDetails", MovementSearchManagerImpl.class);
        }

        log.debug("exiting counterpartydetails method");

        return counterList;
    }

    public ArrayList getCustodianDetails(String hostId, String entityId)
        throws SwtException {
        log.debug("entering custodiandetails method");

        ArrayList cusList = new ArrayList();
        try {
			Iterator itr = (movementsearchDAO.getCustodianDetails(hostId, entityId)).iterator();
			Party party = null;

			cusList.add(new LabelValueBean("", ""));

			while (itr.hasNext()) {
				party = (Party) (itr.next());
				cusList.add(new LabelValueBean(party.getPartyName(),
						party.getId().getPartyId()));
			}
		} catch (Exception exp) {
            log.debug(
                "Exception Catch in MovementSearchManagerImpl.'getCustodianDetails' method : " +
                exp.getMessage());
            throw SwtErrorHandler.getInstance().handleException(exp,
                "getCustodianDetails", MovementSearchManagerImpl.class);
        }

        log.debug("exiting beneficiarydetails method");

        return cusList;
    }

    public ArrayList getBookCodeDetails(String hostId, String entityId)
        throws SwtException {
        log.debug("entering bookcodedetails method");

        ArrayList bookList = new ArrayList();
        try {
			Iterator itr = (movementsearchDAO.getBookCodeDetails(hostId, entityId)).iterator();
			BookCode bookcode = null;

			bookList.add(new LabelValueBean("", ""));

			while (itr.hasNext()) {
				bookcode = (BookCode) (itr.next());
				bookList.add(new LabelValueBean(bookcode.getBookName(),
						bookcode.getId().getBookCode()));
			}
		} catch (Exception exp) {
            log.debug(
                "Exception Catch in MovementSearchManagerImpl.'getBookCodeDetails' method : " +
                exp.getMessage());
            throw SwtErrorHandler.getInstance().handleException(exp,
                "getBookCodeDetails", MovementSearchManagerImpl.class);
        }

        log.debug("exiting bookcodedetails method");

        return bookList;
    }

    public ArrayList getGroupDetails(String hostId, String entityId)
        throws SwtException {
        ArrayList groupList = new ArrayList();
        try {
			Iterator itr = (movementsearchDAO.getGroupDetails(hostId, entityId)).iterator();
			Group group = null;
			groupList.add(new LabelValueBean("", ""));

			while (itr.hasNext()) {
				group = (Group) (itr.next());

				int length = (group.getGroupName().trim()).length();
				StringBuffer label = new StringBuffer();
				label.append(group.getGroupName().trim());

				if (length < 30) {
					for (; length <= 30; length++) {
						label.append("&nbsp;");
					}
				}

				label.append("&nbsp;");
				label.append(group.getGroupLvlName());

				groupList.add(new LabelValueBean(label.toString(),
						group.getId().getGroupId()));
			}
		} catch (Exception exp) {
            log.debug(
                "Exception Catch in MovementSearchManagerImpl.'getGroupDetails' method : " +
                exp.getMessage());
            throw SwtErrorHandler.getInstance().handleException(exp,
                "getGroupDetails", MovementSearchManagerImpl.class);
        }

        return groupList;
    }

    public ArrayList getMetaGroupDetails(String hostId, String entityId)
        throws SwtException {
        ArrayList metagroupList = new ArrayList();
        try {
			Iterator itr = (movementsearchDAO.getMetaGroupDetails(hostId, entityId)).iterator();
			MetaGroup metagroup = null;

			metagroupList.add(new LabelValueBean("", ""));

			while (itr.hasNext()) {
				metagroup = (MetaGroup) (itr.next());

				int length = (metagroup.getMgroupName().trim()).length();
				StringBuffer label = new StringBuffer();
				label.append(metagroup.getMgroupName().trim());

				if (length < 30) {
					for (; length <= 30; length++) {
						label.append("&nbsp;");
					}
				}

				label.append("&nbsp;");
				label.append(metagroup.getMgrpLvlName());

				metagroupList.add(new LabelValueBean(label.toString(),
						metagroup.getId().getMgroupId()));
			}
		} catch (Exception exp) {
            log.debug(
                "Exception Catch in MovementSearchManagerImpl.'getMetaGroupDetails' method : " +
                exp.getMessage());
            throw SwtErrorHandler.getInstance().handleException(exp,
                "getMetaGroupDetails", MovementSearchManagerImpl.class);
        }

        log.debug("exiting groupdetails method");

        return metagroupList;
    }

    public ArrayList getAccountDetails(String hostId, String entityId)
        throws SwtException {
        ArrayList accountList = new ArrayList();
        try {
			Iterator itr = (movementsearchDAO.getAccountDetails(hostId, entityId)).iterator();
			AcctMaintenance acmaint = null;

			accountList.add(new LabelValueBean("", ""));

			while (itr.hasNext()) {
				acmaint = (AcctMaintenance) (itr.next());
				accountList.add(new LabelValueBean(acmaint.getAcctname(),
						acmaint.getId().getAccountId()));
			}
		} catch (Exception exp) {
            log.debug(
                "Exception Catch in MovementSearchManagerImpl.'getAccountDetails' method : " +
                exp.getMessage());
            throw SwtErrorHandler.getInstance().handleException(exp,
                "getAccountDetails", MovementSearchManagerImpl.class);
        }

        log.debug("exiting accountdetails method");

        return accountList;
    }


    /**
     * @desc - This method fetches all the accounts defined for this hostId, entityId and CurrencyCode
     * @param hostId
     * @param entityId
     * @param currCode
     * @return
     * @throws SwtException
     */
    public ArrayList getAccountDetails(String hostId, String entityId,
        String currCode) throws SwtException {
        ArrayList accountList = new ArrayList();
        try {
			Iterator itr = (movementsearchDAO.getAccountDetails(hostId, entityId,
					currCode)).iterator();
			AcctMaintenance acmaint = null;

			accountList.add(new LabelValueBean("", ""));

			while (itr.hasNext()) {
				acmaint = (AcctMaintenance) (itr.next());
				accountList.add(new LabelValueBean(acmaint.getAcctname(),
						acmaint.getId().getAccountId()));
			}
		} catch (Exception exp) {
            log.debug(
                "Exception Catch in MovementSearchManagerImpl.'getAccountDetails' method : " +
                exp.getMessage());
            throw SwtErrorHandler.getInstance().handleException(exp,
                "getAccountDetails", MovementSearchManagerImpl.class);
        }

        log.debug("exiting accountdetails method");

        return accountList;
    }

    /*
	 * Start:code modified by Sandeep kumar for Mantis 1894:Movement:
	 * Account drop down displays other currency accounts
	 */
    /**
     * This method is used to get  account class details
     * @param hostId
     * @param entityId
     * @param accountClass
     * @param currCode
     * @return
     * @throws SwtException
     */
    public ArrayList getAccountClassDetails(String hostId, String entityId,
        String accountClass, String currCode ) throws SwtException {
    	  /*
    	 * End:code modified by Sandeep kumar for Mantis 1894:Movement:
    	 * Account drop down displays other currency accounts
    	 */
    	//variable to hold account list
        ArrayList accountList = null;
        //iterate through list of updated accounts
        Iterator accClassItr =null;
        //variable to hold account maintenance
        AcctMaintenance accountMaintenance = null;
        try {
        	log.debug(this.getClass().getName() + " - [getAccountClassDetails]  - Entering ");
        	//instance for account list
        	accountList = new ArrayList();
        	//get account class details 
			accClassItr = (movementsearchDAO.getAccountClassDetails(hostId,
					entityId, accountClass,currCode)).iterator();
			accountList.add(new LabelValueBean("", ""));
			while (accClassItr.hasNext()) {
				accountMaintenance = (AcctMaintenance) (accClassItr.next());
				accountList.add(new LabelValueBean(accountMaintenance.getAcctname(),
						accountMaintenance.getId().getAccountId()));
			}
		} catch (Exception exp) {
            log.debug(
                "Exception Catch in MovementSearchManagerImpl.'getAccountClassDetails' method : " +
                exp.getMessage());
            throw SwtErrorHandler.getInstance().handleException(exp,
                "getAccountClassDetails", MovementSearchManagerImpl.class);
        }finally
        {
        	accClassItr =null;
        	accountMaintenance = null;
        }
		log.debug(this.getClass().getName() + " - [getAccountClassDetails]  - Exiting ");
        return accountList;
    }

    /* (non-Javadoc)
     * @see org.swallow.work.service.MovementSearchManager#getArchiveLis()
     */
    public List getArchiveList() throws SwtException {
        log.debug("Entering getArchiveList method");
        List archiveList = new ArrayList();
        try {
            archiveList = movementsearchDAO.getArchiveList();
		} catch (Exception exp) {
            log.debug(
                "Exception Catch in MovementSearchManagerImpl.'getArchiveList' method : " +
                exp.getMessage());
            throw SwtErrorHandler.getInstance().handleException(exp,
                "getArchiveList", MovementSearchManagerImpl.class);
        }

        return archiveList;
    }
    
    
    public ArrayList<LabelValueBean>  getPositionLevelList(String hostId) throws SwtException{
        log.debug("Entering getPositionLevelList method");
        ArrayList posLevelList = new ArrayList();
        try {
        	posLevelList = movementsearchDAO.getPositionLevelList(hostId);
		} catch (Exception exp) {
            log.debug(
                "Exception Catch in MovementSearchManagerImpl.'getPositionLevelList' method : " +
                exp.getMessage());
            throw SwtErrorHandler.getInstance().handleException(exp,
                "getPositionLevelList", MovementSearchManagerImpl.class);
        }

        return posLevelList;
    }
    

}
