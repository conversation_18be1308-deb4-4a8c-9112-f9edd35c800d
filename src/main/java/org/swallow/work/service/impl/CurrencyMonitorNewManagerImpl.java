/*
 * @(#)CurrencyMonitorNewManagerImpl.java 1.0 23/08/06
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.work.service.impl;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.util.OpTimer;
import org.swallow.util.PropertiesFileLoader;
import org.swallow.util.SwtConstants;
import org.swallow.util.SwtUtil;
import org.swallow.util.SystemFormats;
import org.swallow.util.UserThreadLocalHolder;
import org.swallow.work.dao.CurrencyMonitorNewDAO;
import org.swallow.work.model.CurrencyRecordVO;
import org.swallow.work.model.Movement;
import org.swallow.work.service.CurrencyMonitorNewManager;
import org.swallow.work.service.MovementManager;
import org.swallow.work.web.form.BalanceDateTO;
import org.swallow.work.web.form.CurrencyRecord;

/**
 * <pre>
 * Manager layer for Currency Monitor screen. This class performs following
 * tasks
 *  - Get no of movements
 *  - Get currency based predicted/loro balances
 * </pre>
 * 
 * <AUTHOR>
 * <AUTHOR> R / 19-Dec-2011
 */
@Repository ("currmonitorNewManager")
public class CurrencyMonitorNewManagerImpl implements CurrencyMonitorNewManager {

	/**
	 * Instance Of Log
	 */
	private static final Log log = LogFactory
			.getLog(CurrencyMonitorNewManagerImpl.class);
	/**
	 * Instance of implementation of CurrencyMonitorNewDAO, to perform currency
	 * monitor related database operation
	 */
	@Autowired
	private CurrencyMonitorNewDAO currmonitorNewDAO = null;

	/**
	 * Setter method of currmonitorNewDAO
	 * 
	 * @param currmonitorNewDAO
	 *            the currmonitorNewDAO to set
	 */
	public void setCurrmonitorNewDAO(CurrencyMonitorNewDAO currmonitorNewDAO) {
		this.currmonitorNewDAO = currmonitorNewDAO;
	}

	/**
	 * <pre>
	 * This method gets currency based predicted/Loro balances for the period
	 * specified (range of maximum of 14 value dates)
	 * 
	 * This method first checks whether the data building job is running or not.
	 * If the job is running, it will returns flag as true, so that the message
	 * &quot;DataBuild in progress&quot; will be displayed to the user
	 * 
	 * If the job is not running, that means the data build is completed. So get
	 * data from database and returns the same
	 * </pre>
	 * 
	 * @param String
	 *            entityId
	 * @param String
	 *            currGrp
	 * @param String
	 *            userId
	 * @param String
	 *            roleId
	 * @param String
	 *            startDate
	 * @param String
	 *            endDate
	 * @param boolean
	 *            hideWeekends
	 * @param SystemFormats
	 *            systemFormat
	 * @param OpTimer
	 *            opTimer
	 * @return CurrencyRecordVO
	 * @throws SwtException
	 */
	public CurrencyRecordVO getCurrMonitorDetails(String entityId,
			String currGrp, String userId, String roleId, String startDate,
			String endDate, boolean hideWeekends, SystemFormats systemFormat,
			OpTimer opTimer) throws SwtException {
		// This object holds all details of currency monitor screen
		CurrencyRecordVO currencyVO = null;
		// Start date
		Date firstDate = null;
		// End date
		Date lastDate = null;
		// Flag denotes whether the job(build data from main table to monitor
		// table) is running or not
		boolean jobFlag = false;

		try {
			// log debug message
			log.debug(this.getClass().getName()
					+ " - [getCurrMonitorDetails] - Entry");
			// Start the timer - Check monitor job
			opTimer.start("jobflag");
			// Get flag value, whether the job is running or not
			jobFlag = SwtUtil.getMonitorJobFlag(entityId,
					SwtConstants.JOB_PROCESS_MONITOR);
			// Stop the timer - Check monitor job
			opTimer.stop("jobflag");
			// Parse date - get the from date and to date
			firstDate = SwtUtil.parseDate(startDate, systemFormat
					.getDateFormatValue());
			lastDate = SwtUtil.parseDate(endDate, systemFormat
					.getDateFormatValue());
			// If jobFlag is true, which means job is not running, so get the
			// balances
			if (jobFlag) {
				currencyVO = currmonitorNewDAO.getAllBalancesUsingStoredProc(
						entityId, currGrp, userId, roleId, firstDate, lastDate,
						hideWeekends, systemFormat, opTimer);
			} else {
				// job is running, so set the flag
				currencyVO = new CurrencyRecordVO();
				currencyVO.setHeaderDetails(new ArrayList<BalanceDateTO>());
				currencyVO.setBalanceDetails(new ArrayList<CurrencyRecord>());
				currencyVO.setBalanceTotal(new CurrencyRecord());
				currencyVO.setJobRunning(true);
			}
			return currencyVO;
		} catch (SwtException ex) {
			// log error message
			log.error(this.getClass().getName()
					+ " - [getCurrMonitorDetails] - SwtException -"
					+ ex.getMessage());
			// Re-throw SwtException
			throw ex;
		} catch (Exception ex) {
			// log error message
			log.error(this.getClass().getName()
					+ " - [getCurrMonitorDetails] - Exception -"
					+ ex.getMessage());
			// Re-throw as SwtException
			throw SwtErrorHandler.getInstance().handleException(ex,
					"getCurrMonitorDetails",
					CurrencyMonitorNewManagerImpl.class);
		} finally {
			// nullify objects
			firstDate = null;
			lastDate = null;
			// log debug message
			log.debug(this.getClass().getName()
					+ " - [getCurrMonitorDetails] - Exit");
		}
	}

	/**
	 * This method gets no of movements for movement breakdown and returns the
	 * same
	 * 
	 * @param String
	 *            hostId
	 * @param String
	 *            entityId
	 * @param String
	 *            currencyCode
	 * @param Date
	 *            valueDate
	 * @return int - no of movements
	 * @throws SwtException
	 */
	public int getNoOfMovements(String hostId, String entityId,
			String currencyCode, Date valueDate) throws SwtException {
		// To get the number of movements
		MovementManager movementManager = null;
		// No of records per page
		int pageSize;
		// Totals returned from the SQL call
		HashMap<String, Object> totalMap = null;
		// Total no of movements
		int totalCount = 0;
		try {
			// log debug message
			log.debug(this.getClass().getName()
					+ " - [getNoOfMovements] - Entry");
			// Get implementation of MovementManager interface from Spring
			// context
			movementManager = (MovementManager) SwtUtil
					.getBean("movementManager");
			// Get no of records to be displayed on the page
			pageSize = SwtUtil.getPageSizeFromProperty(SwtConstants.MOVEMENT_SUMMARY_SCREEN_PAGE_SIZE);
			// Get total no of movements and returns the same
			// Start:Code Added By ASBalaji for mantis 2032 on
						// 15-08-2012
			// Modified to send openMovementFlag as a separate parameter instead
			// of concatenating with isAllstr.
			totalMap = movementManager.getMonitorMovements(hostId, entityId,
					currencyCode, null, valueDate, "", "", Integer.valueOf(0),
					"", "", pageSize, 1, "N", "", new ArrayList<Movement>(),
					"all,2|false|", SwtConstants.CURRENCY_MONITOR, "N", "N", UserThreadLocalHolder.getUser(),"<None>");
			// End:Code Added By ASBalaji for mantis 2032 on
						// 15-08-2012
			if(totalMap != null && totalMap.get("totalCount") != null )
				totalCount = Integer.parseInt(String.valueOf(totalMap.get("totalCount")));
			
			return totalCount;
		} catch (SwtException ex) {
			// log error message
			log.error(this.getClass().getName()
					+ " - [getNoOfMovements] - SwtException -"
					+ ex.getMessage());
			// Re-throw SwtException
			throw ex;
		} catch (Exception ex) {
			// log error message
			log.error(this.getClass().getName()
					+ " - [getNoOfMovements] - Exception -" + ex.getMessage());
			// Re-throw as SwtException
			throw SwtErrorHandler.getInstance().handleException(ex,
					"getNoOfMovements", CurrencyMonitorNewManagerImpl.class);
		} finally {
			// nullify object(s)
			movementManager = null;
			// log debug message
			log.debug(this.getClass().getName()
					+ " - [getNoOfMovements] - Exit");
		}
	}
}