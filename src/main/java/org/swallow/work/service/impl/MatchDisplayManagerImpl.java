/*
 * @(#)MatchDisplayManagerImpl.java 1.0 04/01/2006
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.work.service.impl;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.Hashtable;
import java.util.Iterator;
import java.util.List;
import java.util.StringTokenizer;

import javax.servlet.http.HttpSession;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.maintenance.model.EntityPositionLevel;
import org.swallow.maintenance.model.MiscParams;
import org.swallow.util.CacheManager;
import org.swallow.util.SwtConstants;
import org.swallow.util.SwtDataSource;
import org.swallow.util.SwtUtil;
import org.swallow.util.SystemFormats;
import org.swallow.util.UserThreadLocalHolder;
import org.swallow.work.dao.MatchDAO;
import org.swallow.work.dao.MatchDisplayDAO;
import org.swallow.work.dao.MovementDAO;
import org.swallow.work.dao.MovementLockDAO;
import org.swallow.work.dao.NotesDAO;
import org.swallow.work.model.Match;
import org.swallow.work.model.Movement;
import org.swallow.work.model.MovementDetail;
import org.swallow.work.model.MovementLock;
import org.swallow.work.model.PrevMatch;
import org.swallow.work.service.MatchDisplayManager;
import org.swallow.work.service.MovementDetailVO;
import org.swallow.work.service.MovementManager;

/**
 * This class will handle matching operations such as confirm,reconcile,suspend
 * a match Also handles the logic to add or remove Movements(s) from a match.<br>
 * 
 * Modified by Marshal<br>
 * Modified on 13-June-2012<br>
 */
@Component("matchDisplayManager")
public class MatchDisplayManagerImpl implements MatchDisplayManager {
	/*
	 * Initializing Log variable for logging comments
	 */
	private final Log log = LogFactory.getLog(MatchDisplayManagerImpl.class);
	/* Declares the instance for MatchDisplayDAO */
	@Autowired
	private MatchDisplayDAO matchDisplayDAO = null;
	private final String EMPTY_STRING = SwtConstants.EMPTY_STRING;

	/**
	 * This is mandatory method to initiate DAOHibernate instance and to bind it
	 * with this MatchDisplayDAO
	 * 
	 * @param MatchDisplayDAO
	 */
	public void setMatchDisplayDAO(MatchDisplayDAO matchDisplayDAO) {
		this.matchDisplayDAO = matchDisplayDAO;
	}

	/**
	 * This method is used to get the movement details for the given matchId
	 * 
	 * @param String
	 *            entityId
	 * @param String
	 *            hostId
	 * @param String
	 *            matchId
	 * @param SystemFormats
	 *            formats
	 * @param String
	 *            quality
	 * @return MovementDetailVO object
	 */
	public MovementDetailVO getMovementDetailList(String entityId,
			String hostId, String matchId, SystemFormats formats, String quality)
			throws SwtException {
		boolean notesPresent = false;
		int posLevelInternal = 0;
		int posLevelExternal = 0;
		String statusCode = null;
		String matchSatus = null;
		String entityName = null;
		String archDatabaseName = null;
		ArrayList<Movement> collmovementDetails = null;
		ArrayList<MovementDetail> posDetails = null;
		HashMap<Integer, String> mapPositionLevels = null;
		Collection<Movement> collMvmntDetails = null;
		Iterator<Movement> itrMvntDetails = null;
		SimpleDateFormat inputDateFormat = null;
		Movement movement = null;
		MovementDetail mvmtDetails = null;
		MovementDetailVO movementDetailVO = null;
		SwtDataSource dataSource = null;

		try {
			log.debug(this.getClass().getName()
					+ "- [getMovementDetailList] - Entering ");
			movement = new Movement();
			dataSource = (SwtDataSource) SwtUtil.getBean("dataSourceDb");
			archDatabaseName = (String) dataSource.useDataSource.get();
			if (archDatabaseName != null) {
				dataSource.clearArchive();
				dataSource.useArchive(archDatabaseName);
			} else {
				// Gets the entityName for the entityId
				entityName = matchDisplayDAO.getEntityName(entityId, hostId);
			}
			// Gets the list of position levels and put it in a HashMap with
			// position level as key
			mapPositionLevels = getPositionLevelMap(entityId, hostId);
			/* Create an instance for ArrayList to get the movement details */
			collmovementDetails = new ArrayList<Movement>();
			// Create an instance for MovementDetail
			mvmtDetails = new MovementDetail();
			/*
			 * Create an instance for ArrayList to get the position level
			 * details
			 */
			posDetails = new ArrayList<MovementDetail>();
			// Create an instance for MovementDetailVO
			movementDetailVO = new MovementDetailVO();
			/* Gets the movement details for the given matchId */
			collMvmntDetails = matchDisplayDAO.getMovementDetailList(entityId, hostId,
					matchId);
			/* Iterates the collection */
			itrMvntDetails = collMvmntDetails.iterator();

			/* Create an instance for Movement */
			/* Gets the flag whether notes is present or not */
			notesPresent = matchDisplayDAO.checkNotes(entityId, hostId, matchId);

			while (itrMvntDetails.hasNext()) {
				/* Gets the movement details */
				movement = (Movement) itrMvntDetails.next();
				/* Formats the value date and sets in movement object */
				movement.setValueDateAsString(SwtUtil.formatDate(movement
						.getValueDate(), formats.getDateFormatValue()));
				/* Formats the update date and sets in movement object */
				movement.setUpdateDateAsString(SwtUtil.formatDate(movement
						.getUpdateDate(), formats.getDateFormatValue()));
				/* Formats the amount and sets in movement object */
				movement.setAmountAsString(SwtUtil.formatCurrency(movement
						.getCurrencyCode(), movement.getAmount()));
				/* Create an instance for SimpleDateFormat */
				inputDateFormat = new SimpleDateFormat("HH:mm:ss");
				/* Condition checked to set the input date in movement object */
				if (movement.getInputDate() != null)
					movement.setInputDateAsString(SwtUtil.formatDate(movement
							.getInputDate(), formats.getDateFormatValue())
							+ " "
							+ inputDateFormat.format(movement.getInputDate()));
				if (movement.getPostingDate() != null)
					movement.setPostingDateAsString(SwtUtil.formatDate(movement
							.getPostingDate(), formats.getDateFormatValue()));
				if (archDatabaseName != null)
					movement.setPositionLevelName(movement.getPositionLevel()
							.toString());
				else {
					// Validates mapPositionLevels to increment the
					// internal/external position level
					if (mapPositionLevels.containsKey(movement
							.getPositionLevel())) {
						// Sets the position level name from the map
						movement.setPositionLevelName(mapPositionLevels.get(
								movement.getPositionLevel()).split(",")[0]
								.toString());
						// Validates the mapPositionLevels to check whether the
						// position level is internal
						if (mapPositionLevels.get(movement.getPositionLevel())
								.split(",")[1].toString().equals(
								SwtConstants.POSITION_LEVEL_INTERNAL)) {
							posLevelInternal++;
						} else {
							posLevelExternal++;
						}
					}
				}
				/* Sets the movementId in movement object */
				movement.getId().setMovementIdAsString(
						movement.getId().getMovementId().toString());
				collmovementDetails.add(movement);
			}
			/* Gets the status code */
			statusCode = movement.getMatchStatus();

			/* Gets the match status using status code */
			matchSatus = getMatchStatus(statusCode, entityId);

			/* Sets the flag as Y if notesPresent */
			if (notesPresent) {
				mvmtDetails.setNotes("Y");
			}
			/* Sets the movement details in movementDetails object */
			mvmtDetails.setMatchStatus(matchSatus);
			mvmtDetails.setPosLevelInternal(posLevelInternal);
			mvmtDetails.setPosLevelExternal(posLevelExternal);

			if (archDatabaseName == null)
				mvmtDetails.setEntityName(entityName);

			mvmtDetails.setMatchQuality(quality);
			/* Adding movementDetails in ArrayList */
			posDetails.add(mvmtDetails);
			/* Setting the collection object into movementDetailVO object */
			movementDetailVO.setPosLevelDetail(posDetails);
			movementDetailVO.setMovementDetail(collmovementDetails);
			if (dataSource != null) {
				dataSource.clearArchive();
			}
			log.debug(this.getClass().getName()
					+ "- [getMovementDetailList] - Exiting ");
		} catch (Exception exception) {
			log.error("Exception caught in " + this.getClass().getName()
					+ "- [getMovementDetailList]. Cause: "
					+ exception.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exception,
					"getMovementDetailList", MatchDisplayManagerImpl.class);
		} finally {
			// Cleaning unreferenced objects
			posLevelInternal = 0;
			posLevelExternal = 0;
			statusCode = null;
			matchSatus = null;
			entityName = null;
			collmovementDetails = null;
			posDetails = null;
			collMvmntDetails = null;
			mapPositionLevels = null;
			itrMvntDetails = null;
			inputDateFormat = null;
			movement = null;
			mvmtDetails = null;
			dataSource = null;
		}
		return movementDetailVO;
	}

	/**
	 * This method is used to get the match status
	 * 
	 * @param String
	 *            statusCode
	 * @param String
	 *            entityId
	 * @return String
	 * @throws SwtException
	 */
	private String getMatchStatus(String statusCode, String entityId)
			throws SwtException {
		/* Local variable declarations */
		String partyValue = null;
		ArrayList currentMatchStatus = null;
		Iterator itrCurrencyMatch = null;
		MiscParams miscParams = null;
		try {
			log.debug(this.getClass().getName()
					+ "- [getMatchStatus] - Entering ");
			/* Condition checked to get the match status from MiscParams */
			if (statusCode != null && statusCode.trim().length() > 0) {
				currentMatchStatus = (ArrayList) CacheManager.getInstance()
						.getMiscParams("MATCHSTATUS", statusCode, entityId);
				itrCurrencyMatch = currentMatchStatus.iterator();
				miscParams = new MiscParams();
				miscParams = (MiscParams) itrCurrencyMatch.next();
				partyValue = miscParams.getParValue();

			}
			log.debug(this.getClass().getName()
					+ "- [getMatchStatus] - Exiting ");
		} catch (Exception e) {
			log.error("Exception caught in " + this.getClass().getName()
					+ "- [getMatchStatus]. Cause: " + e.getMessage());
			throw SwtErrorHandler.getInstance().handleException(e,
					"getMatchStatus", MatchDisplayManagerImpl.class);
		} finally {
			// Cleaning unreferenced objects
			currentMatchStatus = null;
			itrCurrencyMatch = null;
			miscParams = null;
		}
		/* Return the match status */
		return partyValue;
	}

	/**
	 * This method is used to delete the given match record
	 * 
	 * @param String
	 *            entityId
	 * @param String
	 *            hostId
	 * @param String
	 *            matchId
	 * @return void
	 * @throws SwtException
	 */
	public void deleteMatchRecord(String entityId, String hostId, String matchId)
			throws SwtException {
		log.debug(this.getClass().getName()
				+ "- [deleteMatchRecord] - Entering ");
		/* Local variable declaration */
		MatchDAO matchDAO = null;
		Collection updateObjects = null;
		Collection deleteObjects = null;
		Match match = null;
		boolean orgMatchStatusConfirmed = false;
		Collection matchNotes = null;
		String status = null;
		Collection collMovementDetails = null;
		Iterator mvtitr = null;
		Movement mvt = null;
		MovementLock movementLockObj = null;
		String bookCodeAvl = null;
		String inputSrc = null;
		String positionLevelInternalExternal = null;
		List removeList = null;
		Long prevMatchedWith = null;
		Long movementId = null;
		PrevMatch prevMatch = null;
		String[] temp_movementId = null;
		int count = 0;
		int size = 0;

		try {
			
			
			int matchCount = matchDisplayDAO.getMatchCount(hostId, entityId, matchId);
			if(matchCount>1000) {
				String userId = null;
				try {
				HttpSession session = UserThreadLocalHolder
						.getUserSession();
				userId = SwtUtil
						.getCurrentUserId(session);
				}catch(Exception e) {
					
				}
				matchDisplayDAO.deleteMatch(hostId, entityId, matchId, userId);
			}else {
				
				
			/* Gets the matchDAO instance from SwtUtil */
			matchDAO = (MatchDAO) SwtUtil.getBean("matchDAO");
			/* Object creation */
			updateObjects = new ArrayList();
			deleteObjects = new ArrayList();
			removeList = (List) matchDisplayDAO.getPrevMatch(hostId, entityId, matchId,
					true);
			size = removeList.size();
			if (size > 0) {
				size = size * 2;
				temp_movementId = new String[size];
				Iterator iterator = removeList.iterator();
				while (iterator.hasNext()) {
					StringTokenizer st = new StringTokenizer(iterator.next()
							.toString(), ",");
					while (st.hasMoreTokens()) {

						temp_movementId[count] = st.nextToken();
						if ((count % 2) == 0) {
							movementId = Long.valueOf(temp_movementId[count]);
						} else {
							prevMatchedWith = Long
									.valueOf(temp_movementId[count]);

							prevMatch = new PrevMatch();

							prevMatch.getId().setEntityId(entityId);
							prevMatch.getId().setMovementId(movementId);
							prevMatch.getId().setPrevMatchedWith(
									prevMatchedWith);
							prevMatch.setUpdateDate(SwtUtil
									.getSystemDatewithoutTime());
							HttpSession session = UserThreadLocalHolder
									.getUserSession();
							prevMatch.setUpdateUser(SwtUtil
									.getCurrentUserId(session));

							matchDisplayDAO.insertPrevMatch(prevMatch);
							prevMatch = null;
						}

						count++;
					}

				}
			}

			/* Gets the match object for the given matchId */
			match = matchDAO.getMatchObject(hostId, entityId, matchId);
			/* Checking for confirm status */
			orgMatchStatusConfirmed = match.getStatus().equalsIgnoreCase(
					SwtConstants.CONFRM_STATUS) || match.getStatus().equalsIgnoreCase(
							SwtConstants.RECONCILE_STATUS)  ? true : false;
			/* Gets the match notes details */
			matchNotes = matchDisplayDAO.getMatchNotes(entityId, hostId, matchId);
			/* Gets the status */
			status = match.getStatus();
			/* Gets the movement details list and iterates */
			collMovementDetails = matchDisplayDAO.getMovementDetailList(entityId, hostId,
					matchId);
			mvtitr = collMovementDetails.iterator();
			/* Object creation */
			mvt = new Movement();
			movementLockObj = new MovementLock();

			while (mvtitr.hasNext()) {
				mvt = (Movement) mvtitr.next();
				/*
				 * Condition checked to set the book code,predict status,input
				 * source ,extract status and beneficiaryId
				 */
				if (status.equals(SwtConstants.CONFRM_STATUS) || status.equals(SwtConstants.RECONCILE_STATUS)) {
					bookCodeAvl = mvt.getBookCodeAvail();
					if ((bookCodeAvl == null)
							|| (bookCodeAvl.compareToIgnoreCase("N") == 0)) {
						mvt.setBookCode(null);
					}
					mvt.setPredictStatus(mvt.getInitialPredStatus());
					inputSrc = mvt.getInputSource();
					if (inputSrc != null
							&& inputSrc.equals(SwtConstants.INPUT_HST)) {
						mvt.setExtractStatus(SwtConstants.EXTRACT_INC);
					} else
						mvt.setExtractStatus(SwtConstants.EXTRACT_EXC);


				}

				else if (status.equals(SwtConstants.OFFERD_STATUS)) {

					mvt.setPredictStatus(mvt.getInitialPredStatus());

				}

				/* Gets the position level from movement manager */
				positionLevelInternalExternal = ((MovementManager) (SwtUtil
						.getBean("movementManager")))
						.getPositionLevelInternalExternal(hostId, entityId, mvt
								.getPositionLevel().intValue());
				/* Condition checked to set the ExtbalStatus */
				if (positionLevelInternalExternal
						.equals(SwtConstants.POSITION_LEVEL_EXTERNAL)) {
					mvt.setExtBalStatus(SwtConstants.EXT_BAL_INC); /*
																	 * Only for
																	 * External
																	 * Position
																	 * level
																	 */
				}
				/* Sets the matchId,matchStatus and predict status */
				mvt.setMatchId(null);
				mvt.setMatchStatus(SwtConstants.OUTSTANDING_STATUS);
				if (orgMatchStatusConfirmed) {
					mvt.setPredictStatus(mvt.getInitialPredStatus());
				}
				/* Object creation */
				movementLockObj = new MovementLock();
				/* Sets the movementId and hostId in movementLockObj object */
				movementLockObj.getId().setMovementId(
						mvt.getId().getMovementId());
				movementLockObj.getId().setHostId(hostId);

				movementLockObj.getId().setEntityId(mvt.getId().getEntityId());

				/* Adds the movement object */
				updateObjects.add(mvt);
				/* Delete the movement lock object */
				deleteObjects.add(movementLockObj);
			}
			/* Adding the match notes and match object */
			deleteObjects.addAll(matchNotes);
			deleteObjects.add(match);
			/* Updates the movement details and delete the match details */
			matchDisplayDAO.doDatabaseOperation(null, updateObjects, deleteObjects);
			}
			log.debug(this.getClass().getName()
					+ "- [deleteMatchRecord] - Exiting ");
		} catch (Exception e) {
			log.error(this.getClass().getName()
					+ "- [deleteMatchRecord] - Exception " + e.getMessage());
			throw SwtErrorHandler.getInstance().handleException(e,
					"deleteMatchRecord", MatchDisplayManagerImpl.class);
		}

	}

	/**
	 * This method is used to suspend the given match
	 * 
	 * @param String
	 *            entityId
	 * @param String
	 *            hostId
	 * @param String
	 *            matchId
	 * @param String
	 *            userId
	 * @return void
	 * @throws SwtException
	 */
	public void suspendMatchRecord(String entityId, String hostId,
			String matchId, String userId) throws SwtException {
		log.debug(this.getClass().getName()
				+ "- [suspendMatchRecord] - Entering ");
		/* Local variable declaration */
		MatchDAO matchDAO = null;
		Collection updateObjects = null;
		Collection deleteObjects = null;
		ArrayList positionLevelList = null;
		Match match = null;
		Movement mvt = null;
		Collection collMovementDetails = null;
		Iterator mvtitr = null;
		String status = null;
		int highPosLev = 0;
		MovementLock movementLockObj = null;
		String bookCodeAvl = null;
		String inputSrc = null;
		Iterator itPositionLevelList = null;
		String matchStatus = null;
		try {
			
			int matchCount = matchDisplayDAO.getMatchCount(hostId, entityId, matchId);
			if(matchCount>1000) {
				matchDisplayDAO.suspendMatch(hostId, entityId, matchId, userId);
			}else {
			/* Gets the matchDAO object from SwtUtil */
			matchDAO = (MatchDAO) SwtUtil.getBean("matchDAO");
			/* Object creation */
			updateObjects = new ArrayList();
			deleteObjects = new ArrayList();
			positionLevelList = new ArrayList();
			/* Gets the match object for the given matchId */
			match = matchDAO.getMatchObject(hostId, entityId, matchId);
			matchStatus = match.getStatus();
			/* Sets the status,confirm date,status date and user */
			match.setStatus(SwtConstants.SUSPEND_STATUS);
			match.setConfirmedDate(null);
			match.setStatusDate(SwtUtil.getSystemDatewithTime());
			match.setStatusUser(userId);
			/* Object creation */
			mvt = new Movement();
			/* Gets the movement details list and iterates */
			collMovementDetails = matchDisplayDAO.getMovementDetailList(entityId, hostId,
					matchId);
			mvtitr = collMovementDetails.iterator();
			/* Gets the status */
			status = match.getStatus();
			/* Gets the highest position level */
			highPosLev = match.getHighestPosLev();
			while (mvtitr.hasNext()) {
				movementLockObj = new MovementLock();
				mvt = (Movement) mvtitr.next();
				/*
				 * Condition checked to set the book code,predict status,input
				 * source and extract status
				 */
				if (matchStatus.equals(SwtConstants.CONFRM_STATUS) || matchStatus.equals(SwtConstants.RECONCILE_STATUS)) {
					bookCodeAvl = mvt.getBookCodeAvail();
					if ((bookCodeAvl == null)
							|| (bookCodeAvl.compareToIgnoreCase("N") == 0)) {
						mvt.setBookCode(null);
					}
					mvt.setPredictStatus(mvt.getInitialPredStatus());
					inputSrc = mvt.getInputSource();
					if (inputSrc != null
							&& inputSrc.equals(SwtConstants.INPUT_HST)) {
						mvt.setExtractStatus(SwtConstants.EXTRACT_INC);
					} else
						mvt.setExtractStatus(SwtConstants.EXTRACT_EXC);
				}
				/* Sets the match status */
				mvt.setMatchStatus(SwtConstants.SUSPEND_STATUS);
				/* Adds the movement object */
				updateObjects.add(mvt);
				/* Sets the movementId and hostId in movementLockObj object */
				movementLockObj.getId().setMovementId(
						mvt.getId().getMovementId());
				movementLockObj.getId().setHostId(hostId);

				movementLockObj.getId().setEntityId(mvt.getId().getEntityId());

				/* Adding the movement lock object and position level */
				deleteObjects.add(movementLockObj);
				positionLevelList.add(mvt.getPositionLevel());
			}
			/* Iterates and sets the match quality */
			itPositionLevelList = positionLevelList.iterator();
			while (itPositionLevelList.hasNext()) {
				switch (((Integer) itPositionLevelList.next()).intValue()) {

				case 1:
					match.setIntQuality1(SwtConstants.MANUAL_MATCH_QUALITY);
					break;

				case 2:
					match.setIntQuality2(SwtConstants.MANUAL_MATCH_QUALITY);
					break;
				case 3:
					match.setIntQuality3(SwtConstants.MANUAL_MATCH_QUALITY);
					break;
				case 4:
					match.setIntQuality4(SwtConstants.MANUAL_MATCH_QUALITY);
					break;
				case 5:
					match.setIntQuality5(SwtConstants.MANUAL_MATCH_QUALITY);
					break;
				case 6:
					match.setIntQuality6(SwtConstants.MANUAL_MATCH_QUALITY);
					break;
				case 7:
					match.setIntQuality7(SwtConstants.MANUAL_MATCH_QUALITY);
					break;
				case 8:
					match.setIntQuality8(SwtConstants.MANUAL_MATCH_QUALITY);
					break;
				case 9:
					match.setIntQuality9(SwtConstants.MANUAL_MATCH_QUALITY);
					break;
				}
			}
			/* Adding the match object */
			updateObjects.add(match);
			/* Updates the match details and delete the movement lock details */
			matchDisplayDAO.doDatabaseOperation(null, updateObjects, deleteObjects);
			}
			log.debug(this.getClass().getName()
					+ "- [suspendMatchRecord] - Exiting ");
		} catch (Exception e) {
			log.error(this.getClass().getName()
					+ "- [suspendMatchRecord] - Exception " + e.getMessage());

			throw SwtErrorHandler.getInstance().handleException(e,
					"suspendMatchRecord", MatchDisplayManagerImpl.class);
		}
	}

	/**
	 * This method is used to confirm the given match
	 * 
	 * @param String
	 *            entityId
	 * @param String
	 *            hostId
	 * @param String
	 *            matchId
	 * @param String
	 *            userId
	 * @return void
	 * @throws SwtException
	 */
	public void confirmMatchRecord(String entityId, String hostId,
			String matchId, String userId) throws SwtException {
		log.debug(this.getClass().getName()
				+ "- [confirmMatchRecord] - Entering ");
		/* Local variable declaration */
		// To create instance of matchdao
		MatchDAO matchDAO = null;
		// To create instance of updateobjects
		Collection updateObjects = null;
		// To create instance of locked movement list
		Collection<MovementLock> lockedMovement = null;
		// To create instance of loroaccount list
		ArrayList loroAccountList = null;
		// To create instance of selected movements that makes match
		Collection<Movement> selectedMovements = null;
		// To create instance of match
		Match match = null;
		// To create instance of movemebt
		Movement mvt, movTemp = null;
		// To create instance of movement iterator
		Iterator<Movement> mvtitr, movementIterator = null;
		// To create instance highest poslevel
		int highestPosLev = 0;
		// To create instance of initial predict status
		String initialPredictStatus = "";
		// To create instance of movement lock object
		MovementLock movementLockObj = null;
		// To create instance of position level
		int posLevel = 0;
		// To create instance of input hst
		String inputHst = "";
		// To create instance variable to check whether bookcode available for a
		// movement
		String isBookCode = null;
		// To create instance of input source of movement
		String inputSrc = null;
		// To create instance of position levels
		List<Integer> poslevelsList = null;
		try {
			/* Gets the matchDAO object from SwtUtil */
			int matchCount = matchDisplayDAO.getMatchCount(hostId, entityId, matchId);
			if(matchCount>10000) {
				matchDisplayDAO.confirmMatch(hostId,entityId,matchId,userId);
			}else {
			matchDAO = (MatchDAO) SwtUtil.getBean("matchDAO");
			// Initialize the variables that hold lockedmovments,loroaccount
			// list,poslevles,updateobjects
			updateObjects = new ArrayList();
			lockedMovement = new ArrayList();
			loroAccountList = new ArrayList();
			poslevelsList = new ArrayList<Integer>();
			/* Gets the movement details list and iterates */
			selectedMovements = (Collection) matchDisplayDAO.getMovementDetailList(
					entityId, hostId, matchId);
			/* Gets the match object for the given matchId */
			match = matchDAO.getMatchObject(hostId, entityId, matchId);
			/* Object creation */
			mvt = new Movement();
			/* Iterates the collection object */
			movementIterator = selectedMovements.iterator();
			/* Gets the highest position level and match quality */
			highestPosLev = match.getHighestPosLev();
			/*
			 * Sets the status, confirmed date,OrigConfirmedDate,StatusDate and
			 * StatusUser in match object
			 */
			match.setStatus(SwtConstants.CONFRM_STATUS);
			match.setConfirmedDate(SwtUtil.getSystemDatewithTime());
			match.setOrigConfirmedDate(SwtUtil.getSystemDatewithTime());
			match.setStatusDate(SwtUtil.getSystemDatewithTime());
			match.setStatusUser(userId);
			/* Object creation */
			movementLockObj = new MovementLock();
			/* Assigns the book code status as Y */
			isBookCode = SwtConstants.YES;

			while (movementIterator.hasNext()) {
				mvt = (Movement) movementIterator.next();
				/* Gets the InitialPredStatus and input source */
				initialPredictStatus = initialPredictStatus
						+ mvt.getInitialPredStatus();

				inputSrc = mvt.getInputSource();
				/* Condition checked to set the inputHst */
				if (inputSrc != null && inputSrc.equals(SwtConstants.INPUT_HST)) {
					inputHst = "Y";
				}
				/* Gets the position level value */
				posLevel = mvt.getPositionLevel().intValue();
				/* Condition checked to set the book code status */
				if (posLevel == highestPosLev) {
					if (SwtConstants.NO
							.equalsIgnoreCase(mvt.getBookCodeAvail())) {
						isBookCode = SwtConstants.NO;
					}
				}
				/* Condition checked to set the match quality */
				switch (posLevel) {

				case 1:
					match.setIntQuality1(SwtConstants.MANUAL_MATCH_QUALITY);
					break;

				case 2:
					match.setIntQuality2(SwtConstants.MATCH_INTERNAL_QUALITY);
					break;
				case 3:
					match.setIntQuality3(SwtConstants.MATCH_INTERNAL_QUALITY);
					break;
				case 4:
					match.setIntQuality4(SwtConstants.MATCH_INTERNAL_QUALITY);
					break;
				case 5:
					match.setIntQuality5(SwtConstants.MATCH_INTERNAL_QUALITY);
					break;
				case 6:
					match.setIntQuality6(SwtConstants.MATCH_INTERNAL_QUALITY);
					break;
				case 7:
					match.setIntQuality7(SwtConstants.MATCH_INTERNAL_QUALITY);
					break;
				case 8:
					match.setIntQuality8(SwtConstants.MATCH_INTERNAL_QUALITY);
					break;
				case 9:
					match.setIntQuality9(SwtConstants.MATCH_INTERNAL_QUALITY);
					break;
				}
			}

			mvtitr = selectedMovements.iterator();
			while (mvtitr.hasNext()) {
				mvt = (Movement) mvtitr.next();
				posLevel = mvt.getPositionLevel().intValue();
				/* Condition checked to set the Extract Status */
				if (inputHst.equals("Y")) {
					if (posLevel == highestPosLev) {
						mvt.setExtractStatus(SwtConstants.EXTRACT_INC);
					} else {
						mvt.setExtractStatus(SwtConstants.EXTRACT_EXC);
					}
				}
				/* Condition checked to set the book code in movement object */
				// Get selected movements
				movementIterator = selectedMovements.iterator();
				// check bookcode is available for the movement
				if (isBookCode.equalsIgnoreCase(SwtConstants.NO)) {
					if (highestPosLev == mvt.getPositionLevel().intValue()
							&& SwtConstants.NO.equalsIgnoreCase(mvt
									.getBookCodeAvail())) {
						while (movementIterator.hasNext()) {
							movTemp = (Movement) movementIterator.next();
							poslevelsList.add(movTemp.getPositionLevel());
						}

						/*
						 * following condition will pick bookcode from second
						 * highest position level and set to movement of highest
						 * position level in a match
						 */
						if (poslevelsList != null) {
							Collections.sort(poslevelsList);
							for (int i = 0; i < poslevelsList.size(); i++) {
								movementIterator = selectedMovements.iterator();
								while (movementIterator.hasNext()) {
									movTemp = (Movement) movementIterator
											.next();
									if (poslevelsList.get(i).equals(
											movTemp.getPositionLevel())
											&& (movTemp.getBookCode() != null)) {
										mvt.setBookCode(movTemp.getBookCode());
										break;
									}
								}
							}
						}
					}
				}

				if (loroAccountList.size() == 0) {
					/*
					 * Set the Predict status of selected Movement in the
					 * highest position level to "I" only when there are no LORO
					 * Account associated with the movements involved
					 */
					if (posLevel == highestPosLev) {
						if (initialPredictStatus.indexOf('I') != -1)
							mvt.setPredictStatus(SwtConstants.PREDICT_INC);
					} else {
						/*
						 * Set the Predict status of other Movements in the
						 * lower position level to "E" only when there are no
						 * LORO Account associated with the movements involved
						 */
						mvt.setPredictStatus(SwtConstants.PREDICT_EXC);
					}
				}

				/* Sets the open flag and match status */
				mvt.setOpenFlag(SwtConstants.NO);
				mvt.setMatchStatus(SwtConstants.CONFRM_STATUS);
				/* Adds the movement object */
				updateObjects.add(mvt);
				/* Object creation */
				movementLockObj = new MovementLock();
				/* Sets the movementId and hostId in movementLockObj object */
				movementLockObj.getId().setMovementId(
						mvt.getId().getMovementId());
				movementLockObj.getId().setHostId(hostId);
				movementLockObj.getId().setEntityId(mvt.getId().getEntityId());
				/* Adding the movement lock object */
				lockedMovement.add(movementLockObj);
			}
			/* Adding the match object for update */
			updateObjects.add(match);
			/* Updates the match details and delete the movement lock details */
			matchDisplayDAO.doDatabaseOperation(null, updateObjects, lockedMovement);
			}
			log.debug(this.getClass().getName()
					+ "- [confirmMatchRecord] - Exiting ");
		} catch (Exception e) {
			log.error(this.getClass().getName()
					+ "- [confirmMatchRecord] - Exception " + e.getMessage());
			throw SwtErrorHandler.getInstance().handleException(e,
					"confirmMatchRecord", MatchDisplayManagerImpl.class);
		} finally {
			// Variables used in this method set to null.
			matchDAO = null;
			updateObjects = null;
			lockedMovement = null;
			loroAccountList = null;
			selectedMovements = null;
			match = null;
			mvt = null;
			movTemp = null;
			mvtitr = null;
			highestPosLev = 0;
			initialPredictStatus = "";
			movementLockObj = null;
			posLevel = 0;
			inputHst = "";
			isBookCode = null;
			inputSrc = null;
			movementIterator = null;
			poslevelsList = null;
		}

	}

	/**
	 * This method is used to remove the movements from a match
	 * 
	 * @param String
	 *            entityId
	 * @param String
	 *            hostId
	 * @param String
	 *            movementList
	 * @param String
	 *            status
	 * @param Long
	 *            currentMatchId
	 * @return void
	 * @throws SwtException
	 */
	public void removeMovements(String entityId, String hostId,
			String movementList, String status, Long currentMatchId)
			throws SwtException {
		log
				.debug(this.getClass().getName()
						+ "- [removeMovements] - Entering ");
		// To create instance of movementDao
		MovementDAO movementDAO = null;
		// To create instance of movement lockdao
		MovementLockDAO movementLockDAO = null;
		// To create instance of matchdao
		MatchDAO matchDAO = null;
		// To create instance of movement manager
		MovementManager movementManager = null;
		// To create instance of update movements
		Collection<Movement> updateMovements = null;
		// To create instance of unlock movements
		Collection<MovementLock> unlockMovements = null;
		// To create instance of original movement list
		Collection<Movement> orgMovementList, bookcodeList = null;
		// To create instance of locked movements
		HashMap<String, String> lockedMovements = null;
		// To create instance of match source details
		HashMap matchSRCDetails = null;
		// To create instance of input highest position level
		String inputHst = null;
		// To create instance of highest position level in a match
		String highestPositionLevel = null;
		// To create instance of original predict status
		String orgPredictStatusList = null;
		// To create instance of position level matrix
		HashMap<Integer, Integer> positionLevelMatrix = null;
		// To create instance of internal quality
		String internalQuality = null;
		// To create instance of match id
		String matchId = null;
		// To create instance of string tokenizer
		StringTokenizer strToken = null;
		// To create instance of movement id
		String movementId = null;
		// To create instance of selected match object
		Match selectedMatchObject = null;
		// To create instance of movement iterator
		Iterator<Movement> itOrgMovementList = null;
		// To create instance of movement
		Movement movement, movTemp = null;
		// To create instance of book code availability for a movement
		String bookCodeAvl = null;
		// To create instance of input source for a movement
		String inputSrc = null;
		// To create instance of
		Integer count = 0;
		// To create instance of position level type whether internal or
		// external
		String positionLevelInternalExternal = null;
		// To create instance of position level
		int positionLevel, chkPositionLevel = 0;
		// To create instance of movement lock object
		MovementLock movementLockObj = null;
		// To create instance of remove list to hold movements that are removed
		Collection<Movement> removeList = null;
		// To create instance of previous match id
		Long prevMatchedWith = null;
		// To create instance of previous match details
		PrevMatch prevMatch = null;
		// To create instance of movementIterator
		Iterator<Movement> movementIterator = null;
		// To create instance of list of available position levels in a match
		List<Integer> poslevelsList = null;
		// variable to hold session
		HttpSession session = null;
		// To create Iterator for all the movements in the match
		Iterator<Movement> itrMvmntMatch = null;
		try {
			
			int matchCount = matchDisplayDAO.getMatchCount(hostId, entityId, ""+currentMatchId);
			//commented as bad perfermance using procedure
			if(false) {
				
				String userId = null;
				try {
				session = UserThreadLocalHolder
						.getUserSession();
				userId = SwtUtil
						.getCurrentUserId(session);
				}catch(Exception e) {
					
				}
				movementList = movementList.replaceAll("'", "").trim();
				matchDisplayDAO.removeMovementFromMatch(hostId, entityId, movementList, status, ""+currentMatchId, userId);
			}else {
				
		
			// Gets the object movementDAO movementManager from SwtUtil
			movementDAO = (MovementDAO) SwtUtil.getBean("movementDAO");
			// Gets the object movementLockDAO movementManager from SwtUtil
			movementLockDAO = (MovementLockDAO) SwtUtil
					.getBean("movementLockDAO");
			// Gets the object matchDAO movementManager from SwtUtil
			matchDAO = (MatchDAO) SwtUtil.getBean("matchDAO");
			// Gets the object movementManager from SwtUtil
			movementManager = (MovementManager) (SwtUtil
					.getBean("movementManager"));
			// Instance creation for update movements
			updateMovements = new ArrayList<Movement>();
			// Instance creation for remove list
			removeList = new ArrayList<Movement>();
			// instance creation for unlock movements
			unlockMovements = new ArrayList<MovementLock>();
			// instance for original movement list
			orgMovementList = new ArrayList<Movement>();
			// instance for locked movements
			lockedMovements = new HashMap<String, String>();
			// instance for match source details
			matchSRCDetails = new HashMap();
			/* Gets the position level from Hash map */
			positionLevelMatrix = getpositionLevelMatrix();
			/* Gets the current matchId */
			matchId = currentMatchId.toString();
			/* Gets the collection of movement */
			strToken = new StringTokenizer(movementList, ",");
			/* Gets the movement details list */
			orgMovementList = matchDisplayDAO.getMovementDetailList(entityId, hostId,
					matchId);
			/* Condition checked to put the movementId in hash map */
			while (strToken.hasMoreTokens()) {
				// Get the selected movement id to be removed from the match
				movementId = strToken.nextToken();
				// trimming any leading or trailing whitespace from the current
				// movement id and replace ' with space
				movementId = movementId.replaceAll("'", "").trim();
				// Iterator for all the movements in the match
				itrMvmntMatch = orgMovementList.iterator();
				while (itrMvmntMatch.hasNext()) {
					movement = itrMvmntMatch.next();
					/*
					 * validate the movement id with the movement id available
					 * in collection if same add the movement instance to remove
					 * list
					 */
					if (movement.getId().getMovementId().toString().equals(
							movementId)) {
						// variable to hold position level getting from movement
						chkPositionLevel = movement.getPositionLevel();
						// Adding the movement to remove list
						removeList.add(movement);
					}
					/*
					 * validates the movement id to be removed and the
					 * corresponding position level for the given match
					 */

					if (!(movement.getId().getMovementId().toString()
							.equals(movementId))
							&& movement.getPositionLevel() != chkPositionLevel) {
						// get the movement id from the movement
						prevMatchedWith = movement.getId().getMovementId();
						// instance for previous match
						prevMatch = new PrevMatch();
						// Set the all other movements apart from the selected
						// in the match
						// set the entity id in previous match
						prevMatch.getId().setEntityId(entityId);
						// set the movement id in previous match
						prevMatch.getId().setMovementId(
								Long.valueOf(movementId));
						// set the movement id from movement into previous match
						// object
						prevMatch.getId().setPrevMatchedWith(prevMatchedWith);
						// set system date with out time in previous match
						// object
						prevMatch.setUpdateDate(SwtUtil
								.getSystemDatewithoutTime());
						// Gets the UserThreadLocalHolder session
						session = UserThreadLocalHolder.getUserSession();
						// set the current user id in previous match object
						prevMatch.setUpdateUser(SwtUtil
								.getCurrentUserId(session));
						// used to insert into Previous match
						matchDisplayDAO.insertPrevMatch(prevMatch);
					}
				}
				// put the locked movements in hash map
				lockedMovements.put(movementId, movementId);
			}

			/* Gets the match object for the given matchId */
			selectedMatchObject = matchDAO.getMatchObject(hostId, entityId,
					matchId);
			/* Extracting HPL and INPUT_HST within updated match */
			matchSRCDetails = getMatchSRCDetsils(lockedMovements,
					orgMovementList);
			// get the input highest position level from matchSRCDetail
			inputHst = matchSRCDetails.get("inputHst").toString();
			// get the highest position level from matchSRCDetail
			highestPositionLevel = matchSRCDetails.get("highestPositionLevel")
					.toString();
			// get the orginal predict status list from matchSRCDetails
			orgPredictStatusList = matchSRCDetails.get("orgPredictStatusList")
					.toString();
			/*
			 * Sets the
			 * maxAmount,highestValueDate,lowestPositionLevel,highestPositionLevel
			 * and predictStatusFlag in match object
			 */
			selectedMatchObject.setMaxAmount((Double) matchSRCDetails
					.get("maxAmount"));
			selectedMatchObject.setMaxValueDate((Date) matchSRCDetails
					.get("highestValueDate"));
			selectedMatchObject.setLowestPosLev(((Integer) (matchSRCDetails
					.get("lowestPositionLevel"))).intValue());
			selectedMatchObject.setHighestPosLev(((Integer) (matchSRCDetails
					.get("highestPositionLevel"))).intValue());
			selectedMatchObject.setPredictStatusFlag(matchSRCDetails.get(
					"predictStatusFlag").toString());
			/* Iterates the collection object */
			movement = null;
			bookcodeList = orgMovementList;
			itOrgMovementList = orgMovementList.iterator();
			while (itOrgMovementList.hasNext()) {
				movement = (Movement) itOrgMovementList.next();
				/*
				 * Condition checked to set the book code,predict status and
				 * Extract status
				 */
				if (lockedMovements.containsKey(movement.getId()
						.getMovementId().toString())) {

					/* Populating locked movement that needs to be removed */
					// get the book code available in movement
					bookCodeAvl = movement.getBookCodeAvail();
					// checks the book code is equal to null or book code is
					// equals to zero
					if ((bookCodeAvl == null)
							|| (bookCodeAvl.compareToIgnoreCase("N") == 0))
						// set the book code null in movement
						movement.setBookCode(null);
					// set predict status in movement
					movement.setPredictStatus(movement.getInitialPredStatus());
					// get the input source from movement
					inputSrc = movement.getInputSource();
					// checks the input source is not equals to null and input
					// source is equals to HST
					if (inputSrc != null
							&& inputSrc.equals(SwtConstants.INPUT_HST))
						// set the extract status in movement
						movement.setExtractStatus(SwtConstants.EXTRACT_INC);
					else
						movement.setExtractStatus(SwtConstants.EXTRACT_EXC);
					// set match id as null
					movement.setMatchId(null);
					// set match status as outstanding
					movement.setMatchStatus(SwtConstants.OUTSTANDING_STATUS);
					/* Adds the movement object */
					updateMovements.add(movement);
					// get the position level from position level matrix
					count = (Integer) positionLevelMatrix.get(movement
							.getPositionLevel());
					/* Gets the position level and puts into Hash map */
					positionLevelMatrix.put(movement.getPositionLevel(),
							new Integer(count.intValue() - 1));

					/*
					 * Info: Selected movement of this match has been locked
					 * from the front end so we will need to unlock all of them
					 * after removing the selected movements.
					 */
					// instance for movement lock
					movementLockObj = new MovementLock();
					/* Sets the movementId in movementLockObj object */
					movementLockObj.getId().setMovementId(
							movement.getId().getMovementId());
					/* Sets the hostId in movementLockObj object */
					movementLockObj.getId().setHostId(hostId);
					/* Sets the entityId in movementLockObj object */
					movementLockObj.getId().setEntityId(entityId);

					/* Adds the movementLockObj object in ArrayList */
					unlockMovements.add(movementLockObj);

				} else {

					/*
					 * Condition checked to set the ExtractStatus,PredictStatus
					 * and ExtBalStatus & book code
					 */
					if (selectedMatchObject.getStatus().equals(
							SwtConstants.CONFRM_STATUS)) {
						// iterate the book code list
						movementIterator = bookcodeList.iterator();
						// set movtemp as null
						movTemp = null;
						// instance for position level list
						poslevelsList = new ArrayList();
						if (highestPositionLevel.equals(movement
								.getPositionLevel().toString())
								&& SwtConstants.NO.equalsIgnoreCase(movement
										.getBookCodeAvail())) {
							// set the book code as null
							movement.setBookCode(null);
							while (movementIterator.hasNext()) {
								movTemp = (Movement) movementIterator.next();
								if (!removeList.contains(movTemp)) {
									// add position level in position level list
									poslevelsList.add(movTemp
											.getPositionLevel());
								}

							}

							/*
							 * following condition will pick book code from
							 * second highest position level and set to movement
							 * of highest position level in a match
							 */
							if (poslevelsList != null) {
								Collections.sort(poslevelsList);
								for (int i = 0; i < poslevelsList.size(); i++) {
									movementIterator = bookcodeList.iterator();

									while (movementIterator.hasNext()) {

										movTemp = (Movement) movementIterator
												.next();

										if (poslevelsList.get(i).equals(
												movTemp.getPositionLevel())
												&& (movTemp.getBookCode() != null)
												&& (!removeList
														.contains(movTemp))) {

											movement.setBookCode(movTemp
													.getBookCode());
										}

									}
								}
							}
						}

						/*
						 * Populating rest of the movements that needs to be
						 * updated
						 */
						if (inputHst.equals("Y")) {
							if (movement.getPositionLevel().toString()
									.equalsIgnoreCase(highestPositionLevel))
								movement
										.setExtractStatus(SwtConstants.EXTRACT_INC);
							else
								movement
										.setExtractStatus(SwtConstants.EXTRACT_EXC);
						}
						/*
						 * set predict status/external balance status to
						 * movements
						 */
						if (movement.getPositionLevel().toString()
								.equalsIgnoreCase(highestPositionLevel)) {
							if (orgPredictStatusList.indexOf('I') != -1)
								movement
										.setPredictStatus(SwtConstants.PREDICT_INC);
							else
								movement
										.setPredictStatus(SwtConstants.PREDICT_EXC);
							positionLevelInternalExternal = movementManager
									.getPositionLevelInternalExternal(hostId,
											entityId, movement
													.getPositionLevel()
													.intValue());
							if (positionLevelInternalExternal
									.equals(SwtConstants.POSITION_LEVEL_EXTERNAL))
								movement
										.setExtBalStatus(SwtConstants.EXT_BAL_INC);
						} else {
							movement.setExtBalStatus(SwtConstants.EXT_BAL_EXC);
						}
						/* Adds the movement object */
						updateMovements.add(movement);
					}
					// set predict status for a offered match
					else if (status.equals(SwtConstants.OFFERD_STATUS)) {

						movement.setPredictStatus(movement
								.getInitialPredStatus());

					}

					count = (Integer) positionLevelMatrix.get(movement
							.getPositionLevel());
					/* Gets the position level and puts into Hash map */
					positionLevelMatrix.put(movement.getPositionLevel(),
							new Integer(count.intValue() + 1));

				}
				/*
				 * Condition checked to populate match quality of match based on
				 * highest position level.
				 */
				if (selectedMatchObject.getStatus().equals(
						SwtConstants.CONFRM_STATUS)) {
					selectedMatchObject = resetMatchQuality(
							selectedMatchObject, highestPositionLevel);
					selectedMatchObject.setConfirmedDate(SwtUtil
							.getSystemDatewithTime());
					internalQuality = SwtConstants.MATCH_INTERNAL_QUALITY;
				} else {
					internalQuality = SwtConstants.MANUAL_MATCH_QUALITY;
				}
				/* Condition checked to set the match quality */
				for (int i = 2; i <= 9; i++) {
					positionLevel = ((Integer) positionLevelMatrix
							.get(new Integer(i))).intValue();
					switch (i) {

					case 1:
						if (positionLevel > 0) {
							selectedMatchObject.setIntQuality1(internalQuality);
						} else {
							selectedMatchObject.setIntQuality1(EMPTY_STRING);
						}
						break;

					case 2:
						if (positionLevel > 0) {
							selectedMatchObject.setIntQuality2(internalQuality);
						} else {
							selectedMatchObject.setIntQuality2(EMPTY_STRING);
						}
						break;
					case 3:
						if (positionLevel > 0) {
							selectedMatchObject.setIntQuality3(internalQuality);
						} else {
							selectedMatchObject.setIntQuality3(EMPTY_STRING);
						}
						break;
					case 4:
						if (positionLevel > 0) {
							selectedMatchObject.setIntQuality4(internalQuality);
						} else {
							selectedMatchObject.setIntQuality4(EMPTY_STRING);
						}
						break;
					case 5:
						if (positionLevel > 0) {
							selectedMatchObject.setIntQuality5(internalQuality);
						} else {
							selectedMatchObject.setIntQuality5(EMPTY_STRING);
						}
						break;
					case 6:
						if (positionLevel > 0) {
							selectedMatchObject.setIntQuality6(internalQuality);
						} else {
							selectedMatchObject.setIntQuality6(EMPTY_STRING);
						}
						break;
					case 7:
						if (positionLevel > 0) {
							selectedMatchObject.setIntQuality7(internalQuality);
						} else {
							selectedMatchObject.setIntQuality7(EMPTY_STRING);
						}
						break;
					case 8:
						if (positionLevel > 0) {
							selectedMatchObject.setIntQuality8(internalQuality);
						} else {
							selectedMatchObject.setIntQuality8(EMPTY_STRING);
						}
						break;
					case 9:
						if (positionLevel > 0) {
							selectedMatchObject.setIntQuality9(internalQuality);
						} else {
							selectedMatchObject.setIntQuality9(EMPTY_STRING);
						}
						break;
					}
				}
				/*
				 * Info: Every movement of this match has been locked from the
				 * front end so we will need to unlock all of them after
				 * removing the selected movements.
				 */

			}
			/* Unlock the movement details */
			movementLockDAO.unLockMovement(unlockMovements);
			/* Update the movement details in the collection object */
			movementDAO.updateMovements(updateMovements);
			/* Updates the selected match object */
			matchDAO.updateMatch(selectedMatchObject);
			}
			log.debug(this.getClass().getName()
					+ "- [removeMovements] - Exiting ");
		} catch (Exception exception) {
			log.error(this.getClass().getName()
					+ "- [removeMovements] - Exception "
					+ exception.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exception,
					"removeMovements", MatchDisplayManagerImpl.class);
		} finally {
			// variables used in this method set to null
			movementDAO = null;
			movementLockDAO = null;
			matchDAO = null;
			movementManager = null;
			updateMovements = null;
			unlockMovements = null;
			orgMovementList = null;
			inputHst = null;
			highestPositionLevel = null;
			orgPredictStatusList = null;
			positionLevelMatrix = null;
			internalQuality = null;
			matchId = null;
			strToken = null;
			movementId = null;
			selectedMatchObject = null;
			itOrgMovementList = null;
			movTemp = null;
			bookCodeAvl = null;
			inputSrc = null;
			count = 0;
			positionLevelInternalExternal = null;
			positionLevel = 0;
			movementLockObj = null;
			movement = null;
			prevMatchedWith = null;
			prevMatch = null;
			movementIterator = null;
			poslevelsList = null;
		}
	}

	/**
	 * This method is used to add the movements
	 * 
	 * @param String
	 *            entityId
	 * @param String
	 *            movementIds
	 * @param String
	 *            status
	 * @param Long
	 *            matchId
	 * @return void
	 * @throws SwtException
	 */
	@SuppressWarnings("unchecked")
	public void addMovements(String entityId, String movementIds,
			String status, Long matchId) throws SwtException {
		log.debug(this.getClass().getName() + "- [addMovements] - Entering ");
		/* Local variable declarations */
		// To create instance of matchdao
		MatchDAO matchDAO = null;
		// To create instance of hostid
		String hostId = null;
		// To create instance of movementdetails
		Collection<Movement> collMovementDetails = null;
		// To create instance of variable which will hold whether bookcode
		// available for a movement
		String isBookCode = null;
		// To create instance of match id
		Long tempMatchId;
		// To create instance of movement
		Movement mvmnt, movTemp = null;
		// To create instance of currentmatch
		Match currentMatch = null;
		// To create instance of movement iterator
		Iterator<Movement> tempmvtitr = null;
		// To create instance of internal match quality
		String internalMatchQuality = null;
		// To create instance of postionlevel
		Integer positionLevel = 0;
		// To create instance of match source
		HashMap matchSRCDetails = null;
		// To create instance of input hst
		String inputHst = null;
		// To create instance of original predict status
		String orgPredictStatusList = null;
		// To create instance of highest position level
		int highestPosLev = 0;
		// To create instance of movement iterator
		Iterator<Movement> mvtitr = null;
		// To create instance of movement lock object
		MovementLock movementLockObj = null;
		// To create instance of movement internal/external status of movement
		String positionLevelInternalExternal = null;
		// To create instance of movementlockdao
		MovementLockDAO movementLockDAO = null;
		// To create instance of movement iterator
		Iterator<Movement> movementIterator = null;
		// To create instance of list of position levels
		List<Integer> poslevelsList = null;
		// To create instance of update movements
		Collection<Movement> updateMovements = null;
		MovementDAO movementDAO = null;
		try {
			
			int matchCount = matchDisplayDAO.getMatchCount(hostId, entityId, ""+matchId);
			//commented as bad perfermance using procedure
			if(false) {
				
				String userId = null;
				try {
				HttpSession session = UserThreadLocalHolder
						.getUserSession();
				userId = SwtUtil
						.getCurrentUserId(session);
				}catch(Exception e) {
					
				}
				matchDisplayDAO.addMovementToMatch(entityId, movementIds, status, ""+matchId, userId);
			}else {
			
			updateMovements = new ArrayList<Movement>();
			movementLockDAO = (MovementLockDAO) SwtUtil
					.getBean("movementLockDAO");
			/* Gets the matchDAO object from SwtUtil */
			matchDAO = (MatchDAO) SwtUtil.getBean("matchDAO");
			/* Gets the hostId */
			hostId = CacheManager.getInstance().getHostId();
			/* Gets the movement details list */
			collMovementDetails = matchDisplayDAO.getMovementDetails(entityId, hostId,
					movementIds);
			/* Condition checked to give the alert message */
			if (collMovementDetails.isEmpty()) {
				throw new SwtException("movement.doesnotexist", "N");
			}
			isBookCode = SwtConstants.YES;
			tempMatchId = Long.valueOf(matchId.toString());
			mvmnt = new Movement();
			/* Gets the match object */
			currentMatch = matchDAO.getMatchObject(hostId, entityId, matchId
					.toString());
			tempmvtitr = collMovementDetails.iterator();
			/*
			 * Gets the HighestPosLev,MatchQuality , CurrencyCode and
			 * MatchQuality
			 */
			highestPosLev = currentMatch.getHighestPosLev();
			internalMatchQuality = currentMatch.getStatus().equalsIgnoreCase(
					SwtConstants.CONFRM_STATUS) ? SwtConstants.MATCH_INTERNAL_QUALITY
					: SwtConstants.MANUAL_MATCH_QUALITY;
			while (tempmvtitr.hasNext()) {
				mvmnt = (Movement) tempmvtitr.next();
				positionLevel = mvmnt.getPositionLevel();
				if (highestPosLev < positionLevel.intValue()) {
					highestPosLev = positionLevel.intValue();
				}
				// Initialise the movement lock object
				movementLockObj = new MovementLock();
				movementLockObj.getId().setMovementId(
						mvmnt.getId().getMovementId());
				movementLockObj.getId().setHostId(hostId);

				movementLockObj.getId().setEntityId(entityId);

				/* Sets the matchId and matchStatus */
				mvmnt.setMatchId(tempMatchId);
				mvmnt.setMatchStatus(status);
				/*
				 * Condition checked to set the
				 * HighestPosLevel,MaxAmount,MaxValueDate,LowestPosLev and
				 * PredictStatusFlag in match object
				 */
				currentMatch
						.setHighestPosLev(currentMatch.getHighestPosLev() < mvmnt
								.getPositionLevel().intValue() ? mvmnt
								.getPositionLevel().intValue() : currentMatch
								.getHighestPosLev());

				currentMatch.setMaxAmount(currentMatch.getMaxAmount()
						.compareTo(mvmnt.getAmount()) < 0 ? mvmnt.getAmount()
						: currentMatch.getMaxAmount());

				currentMatch.setMaxValueDate(currentMatch.getMaxValueDate()
						.getTime() < mvmnt.getValueDate().getTime() ? mvmnt
						.getValueDate() : currentMatch.getMaxValueDate());

				currentMatch
						.setLowestPosLev(currentMatch.getLowestPosLev() < mvmnt
								.getPositionLevel().intValue() ? mvmnt
								.getPositionLevel().intValue() : currentMatch
								.getLowestPosLev());
				currentMatch.setPredictStatusFlag(mvmnt.getPredictStatus()!=null && mvmnt.getPredictStatus()
						.equalsIgnoreCase("I") ? SwtConstants.YES
						: SwtConstants.NO);
				/* Updates the movement details */
				matchDisplayDAO.updateMovements(mvmnt);
				/* Condition checked to set the match quality */
				switch (mvmnt.getPositionLevel().intValue()) {

				case 1:
					currentMatch
							.setIntQuality1(SwtConstants.MANUAL_MATCH_QUALITY);
					break;

				case 2:
					currentMatch.setIntQuality2(internalMatchQuality);
					break;
				case 3:
					currentMatch.setIntQuality3(internalMatchQuality);
					break;
				case 4:
					currentMatch.setIntQuality4(internalMatchQuality);
					break;
				case 5:
					currentMatch.setIntQuality5(internalMatchQuality);
					break;
				case 6:
					currentMatch.setIntQuality6(internalMatchQuality);
					break;
				case 7:
					currentMatch.setIntQuality7(internalMatchQuality);
					break;
				case 8:
					currentMatch.setIntQuality8(internalMatchQuality);
					break;
				case 9:
					currentMatch.setIntQuality9(internalMatchQuality);
					break;
				}
			}
			/* Updates the current match details */
			matchDisplayDAO.updateMatch(currentMatch);
			/* Condition checked for confirm match */
			if (status.equals(SwtConstants.CONFRM_STATUS)) {
				/* Object creation */
				matchSRCDetails = new HashMap();
				/*
				 * Modified for mantis 2156 by Meftah: Match Screen: Predict status is not correctly 
				 * propagated when using add button  
				 * Gets the movement details list
				 */
				collMovementDetails = matchDisplayDAO.getMovementDetailsByMatch(entityId, hostId, matchId);

				highestPosLev = currentMatch.getHighestPosLev();

				currentMatch.setConfirmedDate(SwtUtil.getSystemDatewithTime());
				/* Extracting HPL and INPUT_HST within updated match */
				matchSRCDetails = getMatchSRCDetsils(new HashMap(),
						collMovementDetails);
				inputHst = matchSRCDetails.get("inputHst").toString();
				orgPredictStatusList = matchSRCDetails.get(
						"orgPredictStatusList").toString();
				/*
				 * Sets the
				 * maxAmount,highestValueDate,lowestPositionLevel,highestPositionLevel
				 * and predictStatusFlag in match object
				 */
				currentMatch.setMaxAmount((Double) matchSRCDetails
						.get("maxAmount"));
				currentMatch.setMaxValueDate((Date) matchSRCDetails
						.get("highestValueDate"));
				currentMatch.setLowestPosLev(((Integer) (matchSRCDetails
						.get("lowestPositionLevel"))).intValue());
				currentMatch.setHighestPosLev(((Integer) (matchSRCDetails
						.get("highestPositionLevel"))).intValue());
				currentMatch.setPredictStatusFlag(matchSRCDetails.get(
						"predictStatusFlag").toString());
				/* Object creation */
				mvmnt = new Movement();
				tempmvtitr = collMovementDetails.iterator();
				mvtitr = collMovementDetails.iterator();
				while (tempmvtitr.hasNext()) {
					mvmnt = (Movement) tempmvtitr.next();
					positionLevel = mvmnt.getPositionLevel().intValue();
					/* Condition checked to set the book code */
					if (positionLevel == highestPosLev) {
						if (SwtConstants.NO.equalsIgnoreCase(mvmnt
								.getBookCodeAvail())) {
							isBookCode = SwtConstants.NO;
						}
					}
					/* Condition checked to set the match quality */
					switch (positionLevel) {

					case 1:
						currentMatch
								.setIntQuality1(SwtConstants.MANUAL_MATCH_QUALITY);
						break;

					case 2:
						currentMatch.setIntQuality2(internalMatchQuality);
						break;
					case 3:
						currentMatch.setIntQuality3(internalMatchQuality);
						break;
					case 4:
						currentMatch.setIntQuality4(internalMatchQuality);
						break;
					case 5:
						currentMatch.setIntQuality5(internalMatchQuality);
						break;
					case 6:
						currentMatch.setIntQuality6(internalMatchQuality);
						break;
					case 7:
						currentMatch.setIntQuality7(internalMatchQuality);
						break;
					case 8:
						currentMatch.setIntQuality8(internalMatchQuality);
						break;
					case 9:
						currentMatch.setIntQuality9(internalMatchQuality);
						break;
					}

				}
				while (mvtitr.hasNext()) {

					mvmnt = (Movement) mvtitr.next();

					positionLevel = mvmnt.getPositionLevel().intValue();
					/*
					 * Condition checked to set the
					 * ExtractStatus,PredictStatus,Book Code, ExtBalStatus and
					 * Match Status
					 */
					if (inputHst.equals("Y")) {
						if (positionLevel == highestPosLev) {
							mvmnt.setExtractStatus(SwtConstants.EXTRACT_INC);
						} else {
							mvmnt.setExtractStatus(SwtConstants.EXTRACT_EXC);
						}
					}

					// Check bookcode available for a movement
					movementIterator = collMovementDetails.iterator();
					movTemp = null;
					poslevelsList = new ArrayList();
					if (isBookCode.equalsIgnoreCase(SwtConstants.NO)) {
						if (highestPosLev == mvmnt.getPositionLevel()
								.intValue()
								&& SwtConstants.NO.equalsIgnoreCase(mvmnt
										.getBookCodeAvail())) {
							// store position levels of movement into object
							while (movementIterator.hasNext()) {
								movTemp = (Movement) movementIterator.next();
								poslevelsList.add(movTemp.getPositionLevel());
							}
							/*
							 * following condition will pick bookcode from
							 * second highest position level and set to movement
							 * of highest position level in a match
							 */
							if (poslevelsList != null) {
								Collections.sort(poslevelsList);
								for (int i = 0; i < poslevelsList.size(); i++) {
									movementIterator = collMovementDetails
											.iterator();
									while (movementIterator.hasNext()) {
										movTemp = (Movement) movementIterator
												.next();
										if (poslevelsList.get(i).equals(
												movTemp.getPositionLevel())
												&& (movTemp.getBookCode() != null)) {
											mvmnt.setBookCode(movTemp
													.getBookCode());
										}
									}
								}
							}
						}
					}

					if (positionLevel == highestPosLev) {
						/* External Balance Calculation for ING */
						if (orgPredictStatusList.indexOf('I') != -1)
							mvmnt.setPredictStatus(SwtConstants.PREDICT_INC);
						/* External Balance Calculation for ING */
					} else {
						mvmnt.setPredictStatus(SwtConstants.PREDICT_EXC);
					}

					/* External Balance Calculation for ING */
					positionLevelInternalExternal = ((MovementManager) (SwtUtil
							.getBean("movementManager")))
							.getPositionLevelInternalExternal(hostId, entityId,
									mvmnt.getPositionLevel().intValue());

					if (positionLevelInternalExternal
							.equals(SwtConstants.POSITION_LEVEL_EXTERNAL)) {
						if (positionLevel == highestPosLev) {
							mvmnt.setExtBalStatus(SwtConstants.EXT_BAL_INC);
						} else {
							mvmnt.setExtBalStatus(SwtConstants.EXT_BAL_EXC);
						}
					}
					/* External Balance Calculation for ING */
					mvmnt.setMatchStatus(SwtConstants.CONFRM_STATUS);
					/* Updates the movement details */
//					dao.updateMovements(mvmnt);
					updateMovements.add(mvmnt);
				}
			}
			
			/* Updates the current match details */
			movementDAO = (MovementDAO) SwtUtil.getBean("movementDAO");
			movementDAO.updateMovements(updateMovements);
			matchDisplayDAO.updateMatch(currentMatch);
			
			}
			log
					.debug(this.getClass().getName()
							+ "- [addMovements] - Exiting ");
		} catch (SwtException swtEx) {
			log.error(this.getClass().getName()
					+ "- [addMovements] - Exception " + swtEx.getMessage());

			throw swtEx;
		} catch (Exception e) {
			log.error(this.getClass().getName()
					+ "- [addMovements] - Exception " + e.getMessage());

			throw SwtErrorHandler.getInstance().handleException(e,
					"addMovements", MatchDisplayManagerImpl.class);
		} finally {
			// Reset variables to null
			log.debug("unlocking movements");
			movementLockDAO.unLockMovement(movementLockObj);
			matchDAO = null;
			hostId = null;
			collMovementDetails = null;
			isBookCode = null;
			tempMatchId = null;
			mvmnt = null;
			currentMatch = null;
			tempmvtitr = null;
			internalMatchQuality = null;
			positionLevel = 0;
			matchSRCDetails = null;
			inputHst = null;
			orgPredictStatusList = null;
			highestPosLev = 0;
			mvtitr = null;
			movTemp = null;
			movementLockObj = null;
			positionLevelInternalExternal = null;
			movementLockDAO = null;
			movementIterator = null;
			poslevelsList = null;

		}
	}

	/**
	 * This method is used to match details
	 * 
	 * @param String
	 *            entityId
	 * @param String
	 *            entityId
	 * @param String
	 *            matchId
	 * @return Match Object
	 * @throws SwtException
	 */
	public Match getMatchDetails(String hostId, String entityId, String matchId)
			throws SwtException {
		log
				.debug(this.getClass().getName()
						+ "- [getMatchDetails] - Entering ");
		/* Local variable declaration */
		MatchDAO matchDAO = null;
		Match match = null;

		try {
			/* This is used to give the alert message if the match is null */
			matchDAO = (MatchDAO) SwtUtil.getBean("matchDAO");
			matchId = matchId.replaceAll("'", "").trim();
			match = matchDAO.getMatchObject(hostId, entityId, matchId);
			log.debug(this.getClass().getName()
					+ "- [getMatchDetails] - Exiting ");
			return match;
		} catch (SwtException swtEx) {

			log.error(this.getClass().getName()
					+ "- [getMatchDetails] - Exception " + swtEx.getMessage());

			throw swtEx;
		} catch (Exception e) {
			log.error(this.getClass().getName()
					+ "- [getMatchDetails] - Exception " + e.getMessage());

			throw SwtErrorHandler.getInstance().handleException(e,
					"getMatchDetails", MatchDisplayManagerImpl.class);
		}
	}

	/**
	 * Gets the Latest Match Id list from p_match table
	 * 
	 * @param String
	 *            hostId
	 * @param String
	 *            entityId
	 * @param String
	 *            curencyId
	 * @param String
	 *            status
	 * @param String
	 *            quality
	 * @param Date
	 *            date
	 * @param String
	 *            day
	 * @param String
	 *            applyCurrencyThreshold
	 * @param String
	 *            noIncludedMovementMatches
	 * @return List
	 */
	public List<Long> getLatestMatchIdAsList(String hostId, String entityId,
			String currCode, String status, String quality, Date matchDate,
			String day, String applyCurrencyThreshold,
			String noIncludedMovementMatches) throws SwtException {
		log.debug(this.getClass().getName()
				+ "- [getLatestMatchIdAsList] - Entering ");
		List matchList = matchDisplayDAO.getLatestMatchIdAsList(hostId, entityId, currCode,
				status, quality, matchDate, day, applyCurrencyThreshold,
				noIncludedMovementMatches);
		log.debug(this.getClass().getName()
				+ "- [getLatestMatchIdAsList] - Exiting ");

		return matchList;

	}

	/**
	 * get match ids of a scenario as list
	 * 
	 * @param String
	 *            hostId
	 * @param String
	 *            entityId
	 * @param String
	 *            matchId
	 * @param String
	 *            currencyCode
	 * @param String
	 *            scenarioId
	 * @param String
	 *            applyCurrencyThreshold
	 * @return List
	 *
	 */
	public List<Long> getMatchIdsPerScenarioAsList (String hostId, String  entityId,
			String  currencyCode,String  scenarioId,String  applyCurrencyThreshold,String currencyGroup)throws SwtException {
				
		log.debug(this.getClass().getName()
				+ "- [getLatestMatchIdAsList] - Entering ");
		List matchList = matchDisplayDAO.getMatchIdsPerScenarioAsList ( hostId,   entityId,
				  currencyCode,  scenarioId,  applyCurrencyThreshold, currencyGroup);
		log.debug(this.getClass().getName()
				+ "- [getLatestMatchIdAsList] - Exiting ");

		return matchList;
		
		
	}

	/**
	 * Gets the Match Status
	 * 
	 * @param String
	 *            hostId
	 * @param String
	 *            entityId
	 * @param String
	 *            matchId
	 * @param String
	 *            movementList
	 * @param String
	 *            rowCount
	 * @return boolean
	 */
	public String getMatchStatus(String hostId, String entityId, String matchId, String archiveId)
			throws SwtException {
		String matchHash = null;
		try {
			if(!SwtUtil.isEmptyOrNull(matchId)){
				matchId = matchId.replace("\'", "");
				matchHash = matchDisplayDAO.getMatchHash(entityId, hostId, Long.parseLong(matchId), archiveId);
				
			}
			log.debug(this.getClass().getName()
					+ "- [getMatchStatus] - Exiting ");
		} catch (SwtException ex) {
			log
					.error("Exception Catch in MatchDisplayManagerImpl.'getMatchStatus' method : "
							+ ex.getMessage());
			// Re-throw an exception
			throw SwtErrorHandler.getInstance().handleException(ex,
					"getMatchStatus", MatchDisplayManagerImpl.class);
		} catch (Exception exp) {
			log
					.error("Exception Catch in MatchDisplayManagerImpl.'getMatchStatus' method : "
							+ exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp,
					"getMatchStatus", MatchDisplayManagerImpl.class);
		} 
		return matchHash;
	}

	/**
	 * This method set the status of an existing match to "R" i.e. 'Reconciled'
	 * 
	 * @param entityId
	 * @param hostId
	 * @param matchId
	 * @param userId
	 * @throws SwtException
	 */
	public void reconcileMatchRecord(String entityId, String hostId,
			String matchId, String userId) throws SwtException {
		log.debug(this.getClass().getName()
				+ "- [reconcileMatchRecord] - Entering ");
		/* Local variable declaration */
		Collection insertObjects = null;
		Collection updateObjects = null;
		Collection deleteObjects = null;
		List collMovementDetails = null;
		Collection matchDetails = null;
		Movement mvt = null;
		Match match = null;
		boolean orgMatchStatusConfirmed = false;
		Iterator itr = null;
		Iterator mvtitr = null;
		MovementLockDAO movementLockDAO = null;
		int highestPosLev = 0;
		MovementLock movementLockObj = null;
		int posLevel = 0;
		String inputHst = "";
		String isBookCode = null;
		String inputSrc = null;
		Iterator it3 = null;
		Movement movTemp = null;
		String initialPredictStatus = "";
		try {
			int matchCount = matchDisplayDAO.getMatchCount(hostId, entityId, matchId);
			if(matchCount>1000) {
				matchDisplayDAO.reconcileMatch(hostId, entityId, matchId, userId);
			}else {
			/* Object creation */
			insertObjects = new ArrayList();
			updateObjects = new ArrayList();
			deleteObjects = new ArrayList();
			/* Gets the movement details list */
			collMovementDetails = (List) matchDisplayDAO.getMovementDetailList(entityId,
					hostId, matchId);
			/* Gets the match details */
			matchDetails = matchDisplayDAO.getMatchDetails(entityId, hostId, matchId);
			/* Object creation */
			mvt = new Movement();
			match = new Match();
			/* Iterates the collectio object */
			itr = collMovementDetails.iterator();
			mvtitr = collMovementDetails.iterator();
			match = (Match) matchDetails.iterator().next();
			movementLockDAO = (MovementLockDAO) SwtUtil
					.getBean("movementLockDAO");
			/* Gets the highest position level */
			highestPosLev = match.getHighestPosLev();
			orgMatchStatusConfirmed = match.getStatus().equalsIgnoreCase(
					SwtConstants.CONFRM_STATUS) ? true : false;
			/* Sets the status,original confirm date,status date and user */
			match.setStatus(SwtConstants.RECONCILE_STATUS);
			match.setOrigConfirmedDate(SwtUtil.getSystemDatewithTime());
			match.setStatusDate(SwtUtil.getSystemDatewithTime());
			match.setStatusUser(userId);
			movementLockObj = new MovementLock();
			isBookCode = SwtConstants.YES;

			while (itr.hasNext()) {
				mvt = (Movement) itr.next();
				/* Gets the position level and input source */
				initialPredictStatus = initialPredictStatus
						+ mvt.getInitialPredStatus();
				posLevel = mvt.getPositionLevel().intValue();
				inputSrc = mvt.getInputSource();
				/* Condition checked to assign the inputHst */
				if (inputSrc != null && inputSrc.equals(SwtConstants.INPUT_HST)) {
					inputHst = "Y";
				}
				/* Condition checked to assign the book code */
				if (posLevel == highestPosLev) {
					if (SwtConstants.NO
							.equalsIgnoreCase(mvt.getBookCodeAvail())) {
						isBookCode = SwtConstants.NO;
					}
				}
			}
			/* Updates the match details */
			updateObjects.add(match);
			it3 = collMovementDetails.iterator();
			while (mvtitr.hasNext()) {
				mvt = (Movement) mvtitr.next();
				posLevel = mvt.getPositionLevel().intValue();
				/*
				 * Condition checked to set the ExtractStatus,PredictStatus,Book
				 * Code and Match Status
				 */
				if (inputHst.equals("Y")) {
					if (posLevel == highestPosLev) {
						mvt.setExtractStatus(SwtConstants.EXTRACT_INC);
					} else {
						mvt.setExtractStatus(SwtConstants.EXTRACT_EXC);
					}
				}

				if (isBookCode.equalsIgnoreCase(SwtConstants.NO)) {
					if (highestPosLev == mvt.getPositionLevel().intValue()
							&& SwtConstants.NO.equalsIgnoreCase(mvt
									.getBookCodeAvail())) {
						while (it3.hasNext()) {
							movTemp = (Movement) it3.next();
							if (movTemp.getBookCode() != null) {
								mvt.setBookCode(movTemp.getBookCode());
								break;
							}
						}
					}
				}
//				if (orgMatchStatusConfirmed) {
//					mvt.setPredictStatus(mvt.getInitialPredStatus());
//				}
				
				
				if (posLevel == highestPosLev) {
					if (initialPredictStatus.indexOf('I') != -1)
						mvt.setPredictStatus(SwtConstants.PREDICT_INC);
				} else {
					/*
					 * Set the Predict status of other Movements in the
					 * lower position level to "E" only when there are no
					 * LORO Account associated with the movements involved
					 */
					mvt.setPredictStatus(SwtConstants.PREDICT_EXC);
				}
				/* Sets the match status */
				mvt.setMatchStatus(SwtConstants.RECONCILE_STATUS);
				movementLockObj = new MovementLock();
				/* Sets the movementId and hostId in movementLockObj object */
				movementLockObj.getId().setMovementId(
						mvt.getId().getMovementId());
				movementLockObj.getId().setHostId(hostId);

				movementLockObj.getId().setEntityId(mvt.getId().getEntityId());

				/* Adding the movement object */
				updateObjects.add(mvt);
				/* Adding the movementLockObj object */
				deleteObjects.add(movementLockObj);

			}
			/*
			 * Updates the match and movement details and delete the movement
			 * lock details
			 */
			matchDisplayDAO
					.doDatabaseOperation(insertObjects, updateObjects,
							deleteObjects);
			}
			log.debug(this.getClass().getName()
					+ "- [reconcileMatchRecord] - Exiting ");

		} catch (Exception e) {
			log.error(this.getClass().getName()
					+ "- [reconcileMatchRecord] - Exception " + e.getMessage());

			SwtErrorHandler swtErrorHandler = SwtErrorHandler.getInstance();
			SwtException swtexp = swtErrorHandler.handleException(e,
					"reconcileMatchRecord", MatchDisplayManagerImpl.class);
			throw swtexp;
		}

	}

	/**
	 * This is used to get the added movement details
	 * 
	 * @param hostId
	 * @param entityId
	 * @param moevementLongId
	 * @return Movement object
	 */
	public Movement getAddedMovementDetails(String hostId, String entityId,
			Long moevementLongId) throws SwtException {
		Movement movement = null;
		MovementDAO movementDAO = null;
		try {
			log.debug(this.getClass().getName()
					+ "- [getAddedMovementDetails] - Entering ");
			// Get the movmentDAO bean from swtutil
			movementDAO = (MovementDAO) SwtUtil.getBean("movementDAO");
			/* Gets the movement details */
			movement = movementDAO.getMovement(hostId, entityId,
					moevementLongId);

			log.debug(this.getClass().getName()
					+ "- [getAddedMovementDetails] - Exiting ");
		} catch (Exception e) {
			log.error(this.getClass().getName()
					+ "- [getAddedMovementDetails] - Exception "
					+ e.getMessage());

			SwtErrorHandler swtErrorHandler = SwtErrorHandler.getInstance();
			SwtException swtexp = swtErrorHandler.handleException(e,
					"getAddedMovementDetails", MatchDisplayManagerImpl.class);
		} finally {
			// To clean un-refrenced variables
			movementDAO = null;
		}
		return movement;
	}

	/**
	 * This is private method used to populate match quality of match based on
	 * highest position level.
	 * 
	 * @param selectedMatchObject
	 * @param highestPositionLevel
	 * @return Match object
	 */
	private Match resetMatchQuality(Match selectedMatchObject,
			String highestPositionLevel) {
		log.debug(this.getClass().getName()
				+ "- [resetMatchQuality] - Entering ");
		/* Sets the match quality as "" */

		selectedMatchObject.setIntQuality1("");
		selectedMatchObject.setIntQuality2("");
		selectedMatchObject.setIntQuality3("");
		selectedMatchObject.setIntQuality4("");
		selectedMatchObject.setIntQuality5("");
		selectedMatchObject.setIntQuality6("");
		selectedMatchObject.setIntQuality7("");
		selectedMatchObject.setIntQuality8("");
		selectedMatchObject.setIntQuality9("");
		/* Condition checked to set the match quality */
		switch (Integer.parseInt(highestPositionLevel)) {

		case 1:
			selectedMatchObject
					.setIntQuality1(SwtConstants.MATCH_INTERNAL_QUALITY);
			break;

		case 2:
			selectedMatchObject
					.setIntQuality2(SwtConstants.MATCH_INTERNAL_QUALITY);
			break;
		case 3:
			selectedMatchObject
					.setIntQuality3(SwtConstants.MATCH_INTERNAL_QUALITY);
			break;
		case 4:
			selectedMatchObject
					.setIntQuality4(SwtConstants.MATCH_INTERNAL_QUALITY);
			break;
		case 5:
			selectedMatchObject
					.setIntQuality5(SwtConstants.MATCH_INTERNAL_QUALITY);
			break;
		case 6:
			selectedMatchObject
					.setIntQuality6(SwtConstants.MATCH_INTERNAL_QUALITY);
			break;
		case 7:
			selectedMatchObject
					.setIntQuality7(SwtConstants.MATCH_INTERNAL_QUALITY);
			break;
		case 8:
			selectedMatchObject
					.setIntQuality8(SwtConstants.MATCH_INTERNAL_QUALITY);
			break;
		case 9:
			selectedMatchObject
					.setIntQuality9(SwtConstants.MATCH_INTERNAL_QUALITY);
			break;
		}
		log.debug(this.getClass().getName()
				+ "- [resetMatchQuality] - Exiting ");
		return selectedMatchObject;
	}

	/**
	 * This method returns the string array containing INPUT_HST,
	 * highestPositionLevel and orgPredictStatusList from the remaining
	 * movements of match.
	 * 
	 * @param lockedMap
	 * @param movementList
	 * @return String[INPUT_HST,highestPositionLevel,orgPredictStatusList]
	 */
	private HashMap getMatchSRCDetsils(HashMap lockedMap,
			Collection movementList) {
		log.debug(this.getClass().getName()
				+ "- [getMatchSRCDetsils] - Entering ");
		/* Local variable declaration */
		String inputHst = "";
		Integer highestPositionLevel = null;
		Integer lowestPositionLevel = null;
		Date highestValueDate = null;
		boolean firstIterationFlag = true;
		String predictStatusFlag = SwtConstants.NO;
		StringBuffer orgPredictStatusList = new StringBuffer("");
		HashMap matchSRCDetails = new HashMap();
		Double maxAmount = new Double(0);
		Iterator itmoveList = movementList.iterator();
		while (itmoveList.hasNext()) {
			Movement movement = (Movement) itmoveList.next();
			/*
			 * Condition checked to set the
			 * highestValueDate,maxAmount,inputHst,highestPositionLevel,
			 * lowestPositionLevel and predictStatusFlag.
			 */
			if (!lockedMap.containsValue(movement.getId().getMovementId()
					.toString())) {
				if (firstIterationFlag) {
					highestValueDate = movement.getValueDate();
					lowestPositionLevel = movement.getPositionLevel();
					highestPositionLevel = lowestPositionLevel;
					firstIterationFlag = false;
				} else {
					if (highestValueDate.getTime() < movement.getValueDate()
							.getTime()) {
						highestValueDate = movement.getValueDate();
					}
				}
				if (maxAmount.compareTo(movement.getAmount()) < 0) {
					maxAmount = movement.getAmount();
				}
				String inputSrc = movement.getInputSource();
				if (inputSrc != null && inputSrc.equals(SwtConstants.INPUT_HST)) {
					inputHst = "Y";
				}
				if (highestPositionLevel.intValue() < movement
						.getPositionLevel().intValue()) {
					highestPositionLevel = movement.getPositionLevel();
				}
				if (lowestPositionLevel.intValue() > movement
						.getPositionLevel().intValue()) {
					lowestPositionLevel = movement.getPositionLevel();
				}
				if (movement.getPredictStatus() != null
						&& movement.getPredictStatus().equalsIgnoreCase("I")) {
					predictStatusFlag = SwtConstants.YES;
				}
				/*
				 * Updates the predictStatus based on initialPredictStatus while
				 * manual matching
				 */
				orgPredictStatusList.append(movement.getInitialPredStatus());
			}
		}
		/*
		 * Puts the
		 * highestValueDate,maxAmount,inputHst,highestPositionLevel,lowestPositionLevel
		 * and predictStatusFlag value into hash map.
		 */
		matchSRCDetails.put("inputHst", inputHst);
		matchSRCDetails.put("highestPositionLevel", highestPositionLevel);
		matchSRCDetails.put("orgPredictStatusList", orgPredictStatusList
				.toString());
		matchSRCDetails.put("highestValueDate", highestValueDate);
		matchSRCDetails.put("lowestPositionLevel", lowestPositionLevel);
		matchSRCDetails.put("maxAmount", maxAmount);
		matchSRCDetails.put("predictStatusFlag", predictStatusFlag);
		log.debug(this.getClass().getName()
				+ "- [getMatchSRCDetsils] - Exiting ");
		return matchSRCDetails;
	}

	/**
	 * This method puts the position level and its value into Hash map
	 * 
	 */
	private HashMap getpositionLevelMatrix() {
		log.debug(this.getClass().getName()
				+ "- [getpositionLevelMatrix] - Entering ");
		HashMap positionLevelMatrix = new HashMap();
		positionLevelMatrix.put(new Integer(1), new Integer(0));
		positionLevelMatrix.put(new Integer(2), new Integer(0));
		positionLevelMatrix.put(new Integer(3), new Integer(0));
		positionLevelMatrix.put(new Integer(4), new Integer(0));
		positionLevelMatrix.put(new Integer(5), new Integer(0));
		positionLevelMatrix.put(new Integer(6), new Integer(0));
		positionLevelMatrix.put(new Integer(7), new Integer(0));
		positionLevelMatrix.put(new Integer(8), new Integer(0));
		positionLevelMatrix.put(new Integer(9), new Integer(0));
		log.debug(this.getClass().getName()
				+ "- [getpositionLevelMatrix] - Exiting ");
		return positionLevelMatrix;
	}

	/**
	 * This helper method is used to get all the position levels and returns a
	 * map with Position Level as the key and Position Level Name and Indicator
	 * jointly as value.<br>
	 * 
	 * @param entityId
	 * @param hostId
	 * @return Map with Position Level as the key and Position Level Name and
	 *         Indicator jointly as value
	 * @throws SwtException
	 */
	private HashMap<Integer, String> getPositionLevelMap(String entityId,
			String hostId) throws SwtException {
		// Holds the key/value pair of position level and position level name
		// and indicator
		HashMap<Integer, String> mapPositionLevels = null;
		// Holds the list of position levels fetched from p_position_level_name
		// table
		List<EntityPositionLevel> listPositionLevels = null;
		try {
			log.debug(this.getClass().getName()
					+ " - [getPositionLevelMap] Begins");
			// Makes the call to DAO layer to get the list of position levels
			listPositionLevels = matchDisplayDAO.getAllPositionLevels(hostId, entityId);
			// Instantiates the Map
			mapPositionLevels = new HashMap<Integer, String>();
			// Validates the list to put the values into the map
			if (listPositionLevels != null && listPositionLevels.size() > 0) {
				for (EntityPositionLevel level : listPositionLevels) {
					// Puts the position level as key and position level name
					// along with indicator as value in the map
					mapPositionLevels.put(level.getId().getPositionLevel(),
							level.getPositionLevelName() + ","
									+ level.getIndicator());
				}
			}
			log.debug(this.getClass().getName()
					+ " - [getPositionLevelMap] Ends");
		} catch (Exception exp) {
			log
					.error(this.getClass().getName()
							+ "- [getPositionLevelMap] - Exception "
							+ exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp,
					"getPositionLevelMap", MatchDisplayManagerImpl.class);
		} finally {
			// Cleaning unreferenced objects
			listPositionLevels = null;
		}
		return mapPositionLevels;
	}

	/**
	 * This method is used to get the account access status for the given match
	 * Id.<br>
	 * 
	 * @param roleId
	 * @param entityId
	 * @param matchId
	 * @throws SwtException
	 * @return Account Access Status
	 */
	public String getAccountAccessStatus(String roleId, String entityId,
			String matchId) throws SwtException {
		// Holds the account access status
		String accessStatus = null;
		try {
			log.debug(this.getClass().getName()
					+ " - [getAccountAccessStatus()] Begins");
			accessStatus = matchDisplayDAO
					.getAccountAccessStatus(roleId, entityId, matchId);
			log.debug(this.getClass().getName()
					+ " - [getAccountAccessStatus()] Ends");
		} catch (Exception e) {
			log.error(this.getClass().getName()
					+ "- [getAccountAccessStatus] - Exception "
					+ e.getMessage());
			throw SwtErrorHandler.getInstance().handleException(e,
					"getAccountAccessStatus", MatchDisplayManagerImpl.class);
		}
		return accessStatus;
	}

	/*
	 * Start:Code Modified for Mantis 1270 by Chinniah on 29-Feb-2012:Data
	 * Archive setup: Remove redundant fields from Archive setup screen
	 */
	/**
	 * To get the archived match details from archive schema
	 * 
	 * @param hostId
	 * @param entityId
	 * @param matchId
	 * @param archiveId
	 * @return Match
	 * @throws SwtException
	 */
	public Match getArchiveMatchDetails(String hostId, String entityId,
			String matchId, String archiveId) throws SwtException {
		// variable to hold the Match object
		Match match = null;
		// variable to hold the MatchDAO object
		MatchDAO matchDAO = null;
		try {
			log.debug(this.getClass().getName()
					+ " - [getArchiveMatchDetails()] Begins");
			// getting Match details from the DAO layer
			matchId = matchId.replaceAll("'", "").trim();
			matchDAO = (MatchDAO) SwtUtil.getBean("matchDAO");
			match = matchDAO.getArchiveMatchObject(hostId, entityId, matchId,
					archiveId);
		} catch (Exception e) {
			log.error(this.getClass().getName()
					+ "- [getArchiveMatchDetails] - Exception "
					+ e.getMessage());
			throw SwtErrorHandler.getInstance().handleException(e,
					"getArchiveMatchDetails", MatchDisplayManagerImpl.class);
		} finally {
			matchDAO = null;
			log.debug(this.getClass().getName()
					+ " - [getArchiveMatchDetails()] Exit");
		}
		return match;
	}

	/**
	 * To get the archived Movement details for selected match id from archive
	 * schema
	 * 
	 * @param entityId
	 * @param hostId
	 * @param matchId
	 * @param formats
	 * @param quality
	 * @param archiveId
	 * @return MovementDetailVO
	 * @throws SwtException
	 */
	public MovementDetailVO getArchiveMovementDetailList(String entityId,
			String hostId, String matchId, SystemFormats formats,
			String quality, String archiveId) throws SwtException {
		// Collection to hold the Movement details
		Collection<Movement> collMvmntDetails = null;
		// collection to hold the flag to dtermine the availablity of notes
		boolean notesPresent = false;
		// integer declared to manipulate the external and internal position
		// level
		int posLevelInternal = 0;
		int posLevelExternal = 0;
		// to hold the Match status code
		String statusCode = null;
		// to hold the Match status
		String matchSatus = null;
		// to hold the entity name
		String entityName = null;
		// to hold the archive database anme
		String archDatabaseName = null;
		// collection to hold the Movement details
		ArrayList<Movement> collmovementDetails = null;
		// collection to hold the Movement details
		ArrayList<MovementDetail> posDetails = null;
		// Hash Map Position level and Position level name
		HashMap<Integer, String> mapPositionLevels = null;
		// iterator to iterate collmovementDetails
		Iterator<Movement> itrMvntDetails = null;
		// variable to hold the date format
		SimpleDateFormat inputDateFormat = null;
		// Declared to hold the Movement object
		Movement movement = null;
		// Declared to hold the MovementDetail object
		MovementDetail mvmtDetails = null;
		// Declared to hold the MovementDetailVO object
		MovementDetailVO movementDetailVO = null;
		// Declared to hold the SwtDataSource object
		SwtDataSource dataSource = null;
		// To get the object NotesDAO
		NotesDAO notesDAO = null;
		// collection used to determine whether the notes available or not
		Collection collNotes = null;
		try {
			log.debug(this.getClass().getName()
					+ " - [getArchiveMovementDetailList()] Begins");
			mapPositionLevels = getPositionLevelMap(entityId, hostId);
			/* Create an instance for ArrayList to get the movement details */
			collmovementDetails = new ArrayList<Movement>();
			// Create an instance for MovementDetail
			mvmtDetails = new MovementDetail();
			/*
			 * Create an instance for ArrayList to get the position level
			 * details
			 */
			posDetails = new ArrayList<MovementDetail>();
			// Create an instance for MovementDetailVO
			movementDetailVO = new MovementDetailVO();
			collMvmntDetails = matchDisplayDAO.getArchiveMovementDetailList(entityId,
					hostId, matchId, archiveId);
			itrMvntDetails = collMvmntDetails.iterator();
			/* Create an instance for Movement */
			/* Gets the flag whether notes is present or not */
			notesDAO = (NotesDAO) SwtUtil.getBean("notesDAO");
			collNotes = notesDAO.getArchiveMatchNoteDetails(hostId, new Long(
					matchId), archiveId, entityId);
			while (itrMvntDetails.hasNext()) {
				/* Gets the movement details */
				movement = (Movement) itrMvntDetails.next();
				/* Formats the value date and sets in movement object */
				movement.setValueDateAsString(SwtUtil.formatDate(movement
						.getValueDate(), formats.getDateFormatValue()));
				/* Formats the update date and sets in movement object */
				movement.setUpdateDateAsString(SwtUtil.formatDate(movement
						.getUpdateDate(), formats.getDateFormatValue()));
				/* Formats the amount and sets in movement object */
				movement.setAmountAsString(SwtUtil.formatCurrency(movement
						.getCurrencyCode(), movement.getAmount()));
				/* Create an instance for SimpleDateFormat */
				inputDateFormat = new SimpleDateFormat("HH:mm:ss");
				/* Condition checked to set the input date in movement object */
				if (movement.getInputDate() != null)
					movement.setInputDateAsString(SwtUtil.formatDate(movement
							.getInputDate(), formats.getDateFormatValue())
							+ " "
							+ inputDateFormat.format(movement.getInputDate()));
				if (movement.getPostingDate() != null)
					movement.setPostingDateAsString(SwtUtil.formatDate(movement
							.getPostingDate(), formats.getDateFormatValue()));

				// Validates mapPositionLevels to increment the
				// internal/external position level
				if (mapPositionLevels.containsKey(movement.getPositionLevel())) {
					// Sets the position level name from the map
					movement.setPositionLevelName(mapPositionLevels.get(
							movement.getPositionLevel()).split(",")[0]
							.toString());
					// Validates the mapPositionLevels to check whether the
					// position level is internal
					if (mapPositionLevels.get(movement.getPositionLevel())
							.split(",")[1].toString().equals(
							SwtConstants.POSITION_LEVEL_INTERNAL)) {
						posLevelInternal++;
					} else {
						posLevelExternal++;
					}
				}

				/* Sets the movementId in movement object */
				movement.getId().setMovementIdAsString(
						movement.getId().getMovementId().toString());
				collmovementDetails.add(movement);
			}
			/* Gets the status code */
			statusCode = movement.getMatchStatus();

			/* Gets the match status using status code */
			matchSatus = getMatchStatus(statusCode, entityId);

			/* Sets the flag as Y if notesPresent */
			if (collNotes.size() > 0) {
				mvmtDetails.setNotes("Y");
			}
			/* Sets the movement details in movementDetails object */
			mvmtDetails.setMatchStatus(matchSatus);
			mvmtDetails.setPosLevelInternal(posLevelInternal);
			mvmtDetails.setPosLevelExternal(posLevelExternal);

			if (archDatabaseName == null)
				mvmtDetails.setEntityName(entityName);

			mvmtDetails.setMatchQuality(quality);
			/* Adding movementDetails in ArrayList */
			posDetails.add(mvmtDetails);
			/* Setting the collection object into movementDetailVO object */
			movementDetailVO.setPosLevelDetail(posDetails);
			movementDetailVO.setMovementDetail(collmovementDetails);
			if (dataSource != null) {
				dataSource.clearArchive();
			}

		} catch (Exception e) {
			log.error(this.getClass().getName()
					+ "- [getArchiveMovementDetailList] - Exception "
					+ e.getMessage());
			throw SwtErrorHandler.getInstance().handleException(e,
					"getArchiveMovementDetailList",
					MatchDisplayManagerImpl.class);
		} finally {
			collMvmntDetails = null;
			statusCode = null;
			matchSatus = null;
			entityName = null;
			archDatabaseName = null;
			mapPositionLevels = null;
			itrMvntDetails = null;
			inputDateFormat = null;
			movement = null;
			mvmtDetails = null;
			log.debug(this.getClass().getName()
					+ "- [getMovementDetailList] - Exiting ");
		}
		return movementDetailVO;
	}
	/*
	 * End:Code Modified for Mantis 1270 by Chinniah on 29-Feb-2012:Data Archive
	 * setup: Remove redundant fields from Archive setup screen
	 */
}
