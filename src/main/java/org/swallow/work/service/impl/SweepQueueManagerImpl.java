/*
 * @(#)SweepQueueManagerImpl.java 1.0 Jan 10, 2006
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.work.service.impl;

import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Collection;
import java.util.Date;
import java.util.GregorianCalendar;
import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.swallow.control.dao.SweepLimitsDAO;
import org.swallow.control.model.SweepLimits;
import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.maintenance.model.AcctMaintenance;
import org.swallow.maintenance.service.AcctMaintenanceManager;
import org.swallow.util.LabelValueBean;
import org.swallow.util.SwtConstants;
import org.swallow.util.SwtUtil;
import org.swallow.util.SystemFormats;
import org.swallow.util.struts.ActionMessage;
import org.swallow.util.struts.ActionMessages;
import org.swallow.work.dao.SweepDetailDAO;
import org.swallow.work.dao.SweepQueueDAO;
import org.swallow.work.model.Sweep;
import org.swallow.work.model.SweepQueueDetailVO;
import org.swallow.work.service.SweepQueueManager;

/**
 * This class used get sweep queue detail and makes calculation on sweep
 * parameters whether sweep can be allowed or not
 * 
 */
@Component("sweepQueueManager")
public class SweepQueueManagerImpl implements SweepQueueManager {
	@Autowired
	private SweepQueueDAO sweepQueueDAO;
	private final Log log = LogFactory.getLog(SweepQueueManagerImpl.class);
	private Map<String, Double> actPredictBalMap = new HashMap<String, Double>();
	private Map<String, Double> actExternalBalMap = new HashMap<String, Double>();

	/**
	 * @return Returns the sweepQueueDAO.
	 */
	public SweepQueueDAO getSweepQueueDAO() {
		return sweepQueueDAO;
	}

	/**
	 * @param sweepQueueDAO
	 *            The sweepQueueDAO to set.
	 */
	public void setSweepQueueDAO(SweepQueueDAO sweepQueueDAO) {
		this.sweepQueueDAO = sweepQueueDAO;
	}

	/**
	 * Method for displaying sweep details on sweep queue screens
	 * 
	 * @param sweep
	 *            of Sweep
	 * @param roleId
	 *            of String
	 * @param currencyGrpAccess
	 *            of int
	 * @param sweepQueueDetailVO
	 *            of SweepQueueDetailVO
	 * @param systemFormats
	 *            of SystemFormats
	 * @return int
	 */
	public int getSweepQueueDetail(Sweep sweep, String roleId,
			int currencyGrpAccess, SweepQueueDetailVO sweepQueueDetailVO,
			int currentPage, int initialPageCount, String filterSortStatus,
			SystemFormats systemFormats) throws SwtException {

		log
				.debug(this.getClass().getName()
						+ "- [getSweepQueueDetail] - return getSweepQueueDetail method ");

		Double aut = null;
		Double ori = null;
		Double sub = null;
		int maxPage = 0;

		try {
			// Handling of Sweepstatus Cancel-C
			if (SwtConstants.SWEEP_STATUS_CANCEL.equalsIgnoreCase(sweep
					.getQueueName())) {
				maxPage = sweepQueueDAO.getCancelQueueDetail(sweep,
						sweepQueueDetailVO, currentPage, initialPageCount,
						filterSortStatus, systemFormats, roleId);
				Collection<Sweep> list = sweepQueueDetailVO
						.getSweepDetailList();

				Sweep sweepItem = null;
				Iterator<Sweep> itr = list.iterator();

				while (itr.hasNext()) {
					sweepItem = (Sweep) itr.next();

					sweepItem.setValueDateAsString(SwtUtil
							.formatDate(sweepItem.getValueDate(), systemFormats
									.getDateFormatValue()));
					sweepItem.setDisplayStatus(getDisplayStatus(sweepItem
							.getSweepStatus()));
					sweepItem.setQueueName(sweep.getQueueName());
					// Handling of Sweepstatus New -N
					if (SwtConstants.SWEEP_STATUS_NEW
							.equalsIgnoreCase(sweepItem.getSweepStatus())) {
						sweepItem.setDisplayUser(sweepItem.getInputUser());
						sweepItem.setDisplaySweepAmount(SwtUtil.formatCurrency(
								sweepItem.getCurrencyCode(), sweepItem
										.getOriginalSweepAmt()));
						// added to display new calculated amount on sweep queue
						// screen
						sweepItem.setNewCalulatedAmount("");
						sweepItem.setDisplayDateTimeUser(getDateTime(sweepItem
								.getInputDateTime(), systemFormats
								.getDateFormatValue())
								+ " " + sweepItem.getInputUser());
						// Handling of Sweepstatus Submit -U
					} else if (SwtConstants.SWEEP_STATUS_STP
							.equalsIgnoreCase(sweepItem.getSweepStatus())) {
						sweepItem.setDisplayUser(sweepItem.getInputUser());
						sweepItem.setDisplaySweepAmount(SwtUtil.formatCurrency(
								sweepItem.getCurrencyCode(), sweepItem
										.getOriginalSweepAmt()));
						// added to display new calculated amount on sweep queue
						// screen
						sweepItem.setNewCalulatedAmount("");
						sweepItem.setDisplayDateTimeUser(getDateTime(sweepItem
								.getInputDateTime(), systemFormats
								.getDateFormatValue())
								+ " " + sweepItem.getInputUser());
						// Handling of Sweepstatus Submit -S
					}

					else if (SwtConstants.SWEEP_STATUS_SUBMIT
							.equalsIgnoreCase(sweepItem.getSweepStatus())) {
						sweepItem.setDisplayUser(sweepItem.getSubmitUser());
						sweepItem.setDisplaySweepAmount(SwtUtil.formatCurrency(
								sweepItem.getCurrencyCode(), sweepItem
										.getSubmitSweepAmt()));
						// added to display new calculated amount on sweep queue
						// screen
						sweepItem.setNewCalulatedAmount("");
						sweepItem.setDisplayDateTimeUser(getDateTime(sweepItem
								.getInputDateTime(), systemFormats
								.getDateFormatValue())
								+ " " + sweepItem.getInputUser());
						// Handling of Sweepstatus authorize -A
					} else if (SwtConstants.SWEEP_STATUS_AUTHORIZE
							.equalsIgnoreCase(sweepItem.getSweepStatus())) {
						sweepItem.setDisplayUser(sweepItem.getAuthorizedUser());
						sweepItem.setDisplaySweepAmount(SwtUtil.formatCurrency(
								sweepItem.getCurrencyCode(), sweepItem
										.getAuthorizeSweepAmt()));
						// added to display new calculated amount on sweep queue
						// screen
						sweepItem.setNewCalulatedAmount("");
						sweepItem.setDisplayDateTimeUser(getDateTime(sweepItem
								.getInputDateTime(), systemFormats
								.getDateFormatValue())
								+ " " + sweepItem.getInputUser());
					}

					if (!getCutoffCondition(sweepItem.getAccountCr(), sweepItem
							.getAccountDr())) {
						sweepItem.setCutOffExceeded("Y");
					}
				}

				list = sweepQueueDetailVO.getOtherDetailList();
				itr = list.iterator();

				while (itr.hasNext()) {
					sweepItem = (Sweep) itr.next();
					// added to display new calculated amount on sweep queue
					// screen
					sweepItem.setValueDateAsString(SwtUtil
							.formatDate(sweepItem.getValueDate(), systemFormats
									.getDateFormatValue()));
					sweepItem.setDisplayStatus(getDisplayStatus(sweepItem
							.getSweepStatus()));
					sweepItem.setQueueName(sweep.getQueueName());
					// Handling of Sweepstatus New -N
					if (SwtConstants.SWEEP_STATUS_NEW
							.equalsIgnoreCase(sweepItem.getSweepStatus())) {
						sweepItem.setDisplayUser(sweepItem.getInputUser());
						sweepItem.setDisplaySweepAmount(SwtUtil.formatCurrency(
								sweepItem.getCurrencyCode(), sweepItem
										.getOriginalSweepAmt()));
						// added to display new calculated amount on sweep queue
						// screen
						sweepItem.setNewCalulatedAmount("");
						sweepItem.setDisplayDateTimeUser(getDateTime(sweepItem
								.getInputDateTime(), systemFormats
								.getDateFormatValue())
								+ " " + sweepItem.getInputUser());

					}
					/*
					 * Start:code modified for Mantis 1394 by chinna on
					 * 6-JUN-2011: Full auto-STP sweeps not shown in Sweep
					 * Cancel screen
					 */
					// Handling of Sweepstatus Submit -S
					else if (SwtConstants.SWEEP_STATUS_STP
							.equalsIgnoreCase(sweepItem.getSweepStatus())) {
						sweepItem.setDisplayUser(sweepItem.getInputUser());
						sweepItem.setDisplaySweepAmount(SwtUtil.formatCurrency(
								sweepItem.getCurrencyCode(), sweepItem
										.getOriginalSweepAmt()));
						// added to display new calculated amount on sweep queue
						// screen
						sweepItem.setNewCalulatedAmount("");
						sweepItem.setDisplayDateTimeUser(getDateTime(sweepItem
								.getInputDateTime(), systemFormats
								.getDateFormatValue())
								+ " " + sweepItem.getInputUser());

					}
					/*
					 * End:code modified for Mantis 1394 by chinna on
					 * 6-JUN-2011: Full auto-STP sweeps not shown in Sweep
					 * Cancel screen
					 */

					else if (SwtConstants.SWEEP_STATUS_SUBMIT
							.equalsIgnoreCase(sweepItem.getSweepStatus())) {
						sweepItem.setDisplayUser(sweepItem.getSubmitUser());
						sweepItem.setDisplaySweepAmount(SwtUtil.formatCurrency(
								sweepItem.getCurrencyCode(), sweepItem
										.getSubmitSweepAmt()));

						// added to display new calculated amount on sweep queue
						// screen
						sweepItem.setNewCalulatedAmount("");
						sweepItem.setDisplayDateTimeUser(getDateTime(sweepItem
								.getInputDateTime(), systemFormats
								.getDateFormatValue())
								+ " " + sweepItem.getInputUser());
						// Handling of Sweepstatus Submit -A
					} else if (SwtConstants.SWEEP_STATUS_AUTHORIZE
							.equalsIgnoreCase(sweepItem.getSweepStatus())) {
						sweepItem.setDisplayUser(sweepItem.getAuthorizedUser());
						sweepItem.setDisplaySweepAmount(SwtUtil.formatCurrency(
								sweepItem.getCurrencyCode(), sweepItem
										.getAuthorizeSweepAmt()));

						// added to display new calculated amount on sweep queue
						// screen
						sweepItem.setNewCalulatedAmount("");
						sweepItem.setDisplayDateTimeUser(getDateTime(sweepItem
								.getInputDateTime(), systemFormats
								.getDateFormatValue())
								+ " " + sweepItem.getInputUser());
					}

					if (!getCutoffCondition(sweepItem.getAccountCr(), sweepItem
							.getAccountDr())) {
						sweepItem.setCutOffExceeded("Y");
					}
				}
			}// End of Handling of Sweepstatus Cancel -C
			// Start handling sweepstatus New,Cancel,Authorise-N,C,A
			else {

				sweepQueueDAO.getSweepQueueDetail(sweep, roleId,
						currencyGrpAccess, sweepQueueDetailVO);
				Collection<Sweep> list = sweepQueueDetailVO
						.getSweepDetailList();
				Sweep sweepItem = null;
				Iterator<Sweep> itr = list.iterator();
				while (itr.hasNext()) {
					sweepItem = (Sweep) itr.next();
					// added to display new calculated amount on sweep queue
					// screen

					sweepItem.setValueDateAsString(SwtUtil
							.formatDate(sweepItem.getValueDate(), systemFormats
									.getDateFormatValue()));
					sweepItem.setDisplayStatus(getDisplayStatus(sweepItem
							.getSweepStatus()));
					sweepItem.setQueueName(sweep.getQueueName());
					// Handling sweep status -N
					if (SwtConstants.SWEEP_STATUS_NEW
							.equalsIgnoreCase(sweepItem.getSweepStatus())) {
						sweepItem.setDisplayUser(sweepItem.getInputUser());
						sweepItem.setDisplaySweepAmount(SwtUtil.formatCurrency(
								sweepItem.getCurrencyCode(), sweepItem
										.getOriginalSweepAmt()));

						if (sweepItem.getAuthorizeSweepAmt() != null) {
							sweepItem.setNewCalulatedAmount(SwtUtil
									.formatCurrency(
											sweepItem.getCurrencyCode(),
											sweepItem.getAuthorizeSweepAmt()));
						} else {
							sweepItem.setNewCalulatedAmount("");
						}

					} else if (SwtConstants.SWEEP_STATUS_SUBMIT
							.equalsIgnoreCase(sweepItem.getSweepStatus())) {

						sweepItem.setDisplayUser(sweepItem.getSubmitUser());
						sweepItem.setDisplaySweepAmount(SwtUtil.formatCurrency(
								sweepItem.getCurrencyCode(), sweepItem
										.getOriginalSweepAmt()));

						aut = sweepItem.getAuthorizeSweepAmt();
						ori = sweepItem.getOriginalSweepAmt();
						sub = sweepItem.getSubmitSweepAmt();

						if ((sub != null && aut != null)
								&& (!(sub.equals(aut) && aut.equals(sub) && aut
										.equals(ori)))) {
							sweepItem.setNewCalulatedAmount(SwtUtil
									.formatCurrency(
											sweepItem.getCurrencyCode(),
											sweepItem.getAuthorizeSweepAmt()));
						} else if (sub != null && (!sub.equals(ori))) {
							sweepItem.setNewCalulatedAmount(SwtUtil
									.formatCurrency(
											sweepItem.getCurrencyCode(),
											sweepItem.getSubmitSweepAmt()));
						} else {
							sweepItem.setNewCalulatedAmount("");
						}

					} else if (SwtConstants.AUTHORISE_STATUS
							.equalsIgnoreCase(sweepItem.getSweepStatus())) {
						sweepItem.setDisplayUser(sweepItem.getSubmitUser());
						sweepItem.setDisplaySweepAmount(SwtUtil.formatCurrency(
								sweepItem.getCurrencyCode(), sweepItem
										.getOriginalSweepAmt()));

						// added to display new calculated amount on sweep queue
						// screen
						if (sweepItem.getAuthorizeSweepAmt() != null) {
							sweepItem.setNewCalulatedAmount(SwtUtil
									.formatCurrency(
											sweepItem.getCurrencyCode(),
											sweepItem.getAuthorizeSweepAmt()));
						} else {
							sweepItem.setNewCalulatedAmount("");
						}
					}

					if (!getCutoffCondition(sweepItem.getAccountCr(), sweepItem
							.getAccountDr())) {
						sweepItem.setCutOffExceeded("Y");
					}
				}

				list = sweepQueueDetailVO.getOtherDetailList();
				itr = list.iterator();
				while (itr.hasNext()) {
					sweepItem = (Sweep) itr.next();

					sweepItem.setValueDateAsString(SwtUtil
							.formatDate(sweepItem.getValueDate(), systemFormats
									.getDateFormatValue()));
					sweepItem.setDisplayStatus(getDisplayStatus(sweepItem
							.getSweepStatus()));
					sweepItem.setQueueName(sweep.getQueueName());
					// Handling sweep status U
					if (SwtConstants.SWEEP_STATUS_NEW
							.equalsIgnoreCase(sweepItem.getSweepStatus())) {
						sweepItem.setDisplayUser(sweepItem.getInputUser());
						sweepItem.setDisplaySweepAmount(SwtUtil.formatCurrency(
								sweepItem.getCurrencyCode(), sweepItem
										.getOriginalSweepAmt()));

						if (sweepItem.getAuthorizeSweepAmt() != null) {
							sweepItem.setNewCalulatedAmount(SwtUtil
									.formatCurrency(
											sweepItem.getCurrencyCode(),
											sweepItem.getAuthorizeSweepAmt()));
						} else {
							sweepItem.setNewCalulatedAmount("");
						}

					} else if (SwtConstants.SWEEP_STATUS_SUBMIT
							.equalsIgnoreCase(sweepItem.getSweepStatus())) {
						sweepItem.setDisplayUser(sweepItem.getSubmitUser());
						sweepItem.setDisplaySweepAmount(SwtUtil.formatCurrency(
								sweepItem.getCurrencyCode(), sweepItem
										.getOriginalSweepAmt()));

						// added to display new calculated amount on sweep queue
						// screen

						aut = sweepItem.getAuthorizeSweepAmt();
						ori = sweepItem.getOriginalSweepAmt();
						sub = sweepItem.getSubmitSweepAmt();
						if ((sub != null && aut != null)
								&& (!(sub.equals(aut) && aut.equals(sub) && aut
										.equals(ori)))) {
							sweepItem.setNewCalulatedAmount(SwtUtil
									.formatCurrency(
											sweepItem.getCurrencyCode(),
											sweepItem.getAuthorizeSweepAmt()));
						} else if (sub != null && (!sub.equals(ori))) {
							sweepItem.setNewCalulatedAmount(SwtUtil
									.formatCurrency(
											sweepItem.getCurrencyCode(),
											sweepItem.getSubmitSweepAmt()));
						} else {
							sweepItem.setNewCalulatedAmount("");
						}

						// Handling sweep status A
					} else if (SwtConstants.AUTHORISE_STATUS
							.equalsIgnoreCase(sweepItem.getSweepStatus())) {
						sweepItem.setDisplayUser(sweepItem.getSubmitUser());
						sweepItem.setDisplaySweepAmount(SwtUtil.formatCurrency(
								sweepItem.getCurrencyCode(), sweepItem
										.getOriginalSweepAmt()));

						// added to display new calculated amount on sweep queue
						// screen

						if (sweepItem.getAuthorizeSweepAmt() != null) {
							sweepItem.setNewCalulatedAmount(SwtUtil
									.formatCurrency(
											sweepItem.getCurrencyCode(),
											sweepItem.getAuthorizeSweepAmt()));
						} else {
							sweepItem.setNewCalulatedAmount("");
						}

					}

					if (!getCutoffCondition(sweepItem.getAccountCr(), sweepItem
							.getAccountDr())) {
						sweepItem.setCutOffExceeded("Y");
					}
				}

				actPredictBalMap = new HashMap();
				actExternalBalMap = new HashMap();

				log.debug(this.getClass().getName()
						+ "- [getSweepQueueDetail] -Exit ");
			}

			return maxPage;

		} catch (Exception exp) {
			log
					.error("Exception Catch in SweepQueueManagerImpl.'getSweepQueueDetail' method : "
							+ exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp,
					"getSweepQueueDetail", SweepQueueManagerImpl.class);
		}
	}

	/**
	 * Method to submit or cancel the sweep or sweeps. Before submitting or
	 * cancelling the sweep(s), it checks whether sweeps are properly made. If
	 * not throws proper exception based on the condition which fails.
	 * 
	 * @param sweep
	 * @param selectedList
	 * @param systemFormats
	 * @param bypassChangedSweep
	 * @param bypassCutOff
	 * @param bypassAccountBreach
	 * @return ActionMessages
	 */
	public ActionMessages submit(Sweep sweep, String selectedList,
			SystemFormats systemFormats, String bypassChangedSweep,
			String bypassCutOff, String bypassAccountBreach)
			throws SwtException {
		// Stores the output of sweeps that are submitted
		int output = 0;
		// Stores the calculated amount for the sweep to be submitted
		double calculatedAmt = 0.0;
		// Stores the current sweep amount
		double sweepAmount = 0.0;
		// Flag to check the maximum sweep amount for the sweep
		boolean maxSweepFlag = true;
		// Stores the intra day sweep time
		String intradaySweepTime = "";
		// Stores the EOD sweep time
		String eodSweepTime = "";
		// calendar instance for Intraday sweep time for the account
		Calendar intradaySweepDateTime = null;
		// calendar instance for EOD sweep time for the account
		Calendar eodSweepDateTime = null;
		// Stores the intraday hour part of the time specified in the account
		int intradayHour = 0;
		// Stores the intraday minute part of the time specified in the account
		int intradayMin = 0;
		// Stores the EOD hour part of the time specified in the account
		int eodHour = 0;
		// Stores the Eod minute part of the time specified in the account
		int eodMin = 0;
		// Flag to check if the auto sweep is a intra day sweep
		boolean intradaySweep = false;
		// Map to get the sweep input time with offset
		HashMap<String, Object> sweepInputTimeMap = null;
		// SweepDateTime with offset applied
		Date sweepInputDateTime = null;
		// AccountManager instance
		AcctMaintenanceManager acctManager = null;
		// Store the sub account details
		AcctMaintenance acctMaintenance = null;
		// Stores the message to be appened to error messages
		String msg1 = "";
		// Stores the message to be appened to error messages
		String msg2 = "";
		// Stores the message to be appened to error messages
		String msg3 = "";
		// Stores sweep id's already submitted by other user
		String alreadySubmittedSweeps = "";
		// Stores sweep id's whose cutoff exceeded
		String cutoffExceedingSweeps = "";
		// Stores sweep id's whose account limits breached
		String accountBreachingSweeps = "";
		// Stores sweep id's which exceeds beyond user limit
		String userlimitExceedingSweeps = "";
		// Stores sweep id's which are successfully submitted
		String submittedSweeps = "";
		// Stores sweep id's which are not successfully submitted
		String nonSubmittedSweeps = "";
		// Stores sweep id's whose calculated amount has changed
		String calculatedAmountChangesSweeps = "";
		// Holds the sweeps while iterating
		Sweep sweepItem = null;
		// Holds the list of selected sweeps
		Collection<Sweep> list = null;
		// For iterating through each sweep
		Iterator<Sweep> itr = null;
		// Stores the sweep amount to check if user has exceed his limit
		Map<String, Double> sweepLimitMap = null;
		// Stores the messages that are to be send to the user
		ActionMessage actionMessage = null;
		// Stores the error messages to be displayed
		ActionMessages errorList = new ActionMessages();
		try {
			log.debug(this.getClass().getName() + ".submit Entering");
			// Checks from which queue the sweeps are submitted from
			if (SwtConstants.SWEEP_STATUS_NEW.equalsIgnoreCase(sweep
					.getQueueName())) {
				msg1 = "submitted or cancelled";
				msg2 = "submitting";
				msg3 = "submitted";
			} else {
				msg1 = "authorised or cancelled";
				msg2 = "authorising";
				msg3 = "authorised";
			}
			// Fetch the list of sweeps selected by the user
			list = sweepQueueDAO.submit(sweep, selectedList.substring(0,
					selectedList.length() - 1));
			// Fetches the sweep limits for the current user
			sweepLimitMap = getSweepLimitsMap(sweep.getRequestRole());
			// Stores the iterator for the selected sweeps
			itr = list.iterator();
			// Loop to iterate through selected sweeps
			while (itr.hasNext()) {
				// Selects each sweeps from the list
				sweepItem = (Sweep) itr.next();
				// Sets the current user's role
				sweepItem.setRequestRole(sweep.getRequestRole());
				// Sets the current user id
				sweepItem.setRequestUser(sweep.getRequestUser());
				// Sets the current queue name
				sweepItem.setQueueName(sweep.getQueueName());
				// Sets the displayed sweep amount
				sweepItem.setDisplaySweepAmount(sweep.getDisplaySweepAmount());
				// Check if the sweeps are
				if (isSubmitAllowed(sweep.getQueueName(), sweepItem)) {

					if (getCutoffCondition(sweepItem.getAccountCr(), sweepItem
							.getAccountDr())
							|| ((bypassCutOff != null) && bypassCutOff
									.equalsIgnoreCase("Y"))) {
						calculatedAmt = getCalculatedSweepAmount(sweepItem,
								systemFormats);

						sweepAmount = getQueueAmount(sweepItem);

						// Get the account manager instance
						acctManager = (AcctMaintenanceManager) SwtUtil
								.getBean("acctMaintenanceManager");
						// Check if the selected sweep is a Auto Sweep
						if (sweepItem.getSweepType().equals(
								SwtConstants.SWEEP_TYPE_AUTO)) {
							// Get the account details of sweep CR account id
							acctMaintenance = acctManager.copyAccountIdDetails(
									sweepItem.getId().getHostId(), sweepItem
											.getEntityIdCr(), sweepItem
											.getAccountIdCr(), null, null);
							// If the selected account is not a sub account
							if (!(acctMaintenance.getAcctlevel()
									.equals(SwtConstants.ACCT_SUB))) {
								// Get the account details of sweep DR account
								// id which is a sub account, as sweeps
								// generated by auto sweep will include one main
								// and one sub account
								acctMaintenance = acctManager
										.copyAccountIdDetails(sweepItem.getId()
												.getHostId(), sweepItem.getEntityIdDr(), sweepItem
												.getAccountIdDr(), null, null);
							}
							// Convert the sweep input time based on the offset
							// time of the entity
							sweepInputTimeMap = SwtUtil.getOffsetDateTime(
									sweepItem.getInputDateTime(),
									acctMaintenance.getId().getEntityId());
							sweepInputDateTime = (Date) sweepInputTimeMap
									.get("entityDate");
							/*
							 * Start: Code modified by Sudhakar for Mantis 1487
							 * on 24-07-2011
							 */

							// Set the intraday Sweep time (method name used in
							// account maintenance is bit confusing)
							intradaySweepTime = acctMaintenance
									.getIntraDaySweeptime();
							// Set the eod Sweep time (method name used in
							// account maintenance is bit confusing)
							eodSweepTime = acctMaintenance.getEodSweeptime();
							/*
							 * End: Code modified by Sudhakar for Mantis 1487 on
							 * 24-07-2011
							 */
							// Check if the intraday and eod Sweep time are not
							// null
							if (!(SwtUtil.isEmptyOrNull(intradaySweepTime))
									&& !(SwtUtil.isEmptyOrNull(eodSweepTime))) {
								// Initialise the calendar instance for intraday
								intradaySweepDateTime = Calendar.getInstance();
								// Initialise the calendar instance for eod
								eodSweepDateTime = Calendar.getInstance();
								// Get the hour part of intraday sweep time
								intradayHour = Integer
										.parseInt(intradaySweepTime.split(":")[0]);
								// Get the minute part of intraday sweep time
								intradayMin = Integer
										.parseInt(intradaySweepTime.split(":")[1]);
								// Get the hour part of eod sweep time
								eodHour = Integer.parseInt(eodSweepTime
										.split(":")[0]);
								// Get the minute part of eod sweep time
								eodMin = Integer.parseInt(eodSweepTime
										.split(":")[1]);
								// Set intradaydatetime to sweep date
								intradaySweepDateTime.setTime(sweepItem
										.getInputDateTime());
								// Set eoddatetime to sweep date
								eodSweepDateTime.setTime(sweepItem
										.getInputDateTime());
								// set the intra day sweep hour and min from sub
								// account
								intradaySweepDateTime.set(Calendar.AM_PM, 0);
								intradaySweepDateTime.set(Calendar.HOUR,
										intradayHour);
								intradaySweepDateTime.set(Calendar.MINUTE,
										intradayMin);
								// set the eod sweep hour and min from sub
								// account
								eodSweepDateTime.set(Calendar.AM_PM, 0);
								eodSweepDateTime.set(Calendar.HOUR, eodHour);
								eodSweepDateTime.set(Calendar.MINUTE, eodMin);
								// Check if the intraday hour is greater than
								// Eod
								if (intradayHour > eodHour) {
									// then EOD is next day
									eodSweepDateTime
											.set(
													Calendar.DATE,
													eodSweepDateTime
															.get(Calendar.DATE) + 1);
								} else if (intradayHour == eodHour) {
									// Check if the intraday hour is equal to
									// EOD, Check if the intraday min is greater
									// than Eod
									if (intradayMin > eodMin) {
										// then EOD is next day, else EOD is
										// same day
										eodSweepDateTime
												.set(
														Calendar.DATE,
														eodSweepDateTime
																.get(Calendar.DATE) + 1);
									}
								}
								// Check if the sweep input date time is between
								// intraday and eod sweep time, then this sweep
								// is a intraday sweep
								if (sweepInputDateTime
										.after(intradaySweepDateTime.getTime())
										&& sweepInputDateTime
												.before(eodSweepDateTime
														.getTime())) {
									intradaySweep = true;
								} else {
									intradaySweep = false;
								}
							} else {
								intradaySweep = false;
							}
						} else {
							intradaySweep = false;
						}
						if ((sweepAmount == calculatedAmt || intradaySweep)
								|| ((bypassChangedSweep != null) && bypassChangedSweep
										.equalsIgnoreCase("Y"))) {

							if (((sweepAmount >= (sweepItem.getAccountCr()
									.getMinseepamt() != null ? sweepItem
									.getAccountCr().getMinseepamt()
									.doubleValue() : Double.valueOf(0)
									.doubleValue())) && (sweepAmount >= (sweepItem
									.getAccountDr().getMinseepamt() != null ? sweepItem
									.getAccountDr().getMinseepamt()
									.doubleValue()
									: Double.valueOf(0).doubleValue())))
									|| ((bypassAccountBreach != null) && bypassAccountBreach
											.equalsIgnoreCase("Y"))) {
								/*
								 * Variable added for checking whether the
								 * Maximum Sweep amount contains any value or
								 * not
								 */
								maxSweepFlag = true;
								/*
								 * Checking Maximum Sweep Amount whether it is
								 * NULL or Zero
								 */
								/*
								 * If it is NULL or Zero maxSweepFlag will be
								 * made false
								 */
								if (sweepItem.getAccountCr().getMaxsweepamte() == null) {
									maxSweepFlag = false;
								} else if (sweepItem.getAccountCr()
										.getMaxsweepamte().doubleValue() == Double
										.valueOf(0).doubleValue()) {
									maxSweepFlag = false;
								} else if (maxSweepFlag
										&& sweepItem.getAccountDr()
												.getMaxsweepamte() == null) {
									maxSweepFlag = false;
								} else if (maxSweepFlag
										&& sweepItem.getAccountDr()
												.getMaxsweepamte()
												.doubleValue() == Double
												.valueOf(0).doubleValue()) {
									maxSweepFlag = false;
								}
								/*
								 * Added to check the Maximum Sweep Amount if it
								 * null or zero
								 */
								if (maxSweepFlag) {
									if ((sweepAmount <= sweepItem
											.getAccountCr().getMaxsweepamte()
											.doubleValue())
											&& (sweepAmount <= sweepItem
													.getAccountDr()
													.getMaxsweepamte()
													.doubleValue())) {
										if (sweepAmount > ((Double) sweepLimitMap
												.get(sweepItem
														.getCurrencyCode()))
												.doubleValue()) {
											userlimitExceedingSweeps += (sweepItem
													.getId().getSweepId() + ",");
										} else {
											output = sweepQueueDAO
													.processSweep(sweepItem,
															systemFormats);

											if (output == 0) {
												submittedSweeps += (sweepItem
														.getId().getSweepId() + ",");
											} else if (output == 1) {
												nonSubmittedSweeps += (sweepItem
														.getId().getSweepId() + ",");
											} else if (output == 3) {
												throw new SwtException(
														"sweep.invalidDirectoryPath",
														"N");
											} else if (output == 4) {
												throw new SwtException(
														"sweep.accountTypeNotFound",
														"N");
											} else if (output == 5) {
												throw new SwtException(
														"sweep.messgeFormatNotDefined",
														"N");
											} else if (output == 9) {
												throw new SwtException(
														"sweep.errorInsertingAlert",
														"N");
											} else if (output == 10) {
												throw new SwtException(
														"sweep.messageGenerationError",
														"N");
											} else {
												submittedSweeps += (sweepItem
														.getId().getSweepId() + ",");

											}
										}
									}
								} else {
									if (sweepAmount > ((Double) sweepLimitMap
											.get(sweepItem.getCurrencyCode()))
											.doubleValue()) {
										userlimitExceedingSweeps += (sweepItem
												.getId().getSweepId() + ",");
									} else {
										output = sweepQueueDAO.processSweep(
												sweepItem, systemFormats);

										if (output == 0) {
											submittedSweeps += (sweepItem
													.getId().getSweepId() + ",");
										} else if (output == 1) {
											nonSubmittedSweeps += (sweepItem
													.getId().getSweepId() + ",");
										} else if (output == 3) {
											throw new SwtException(
													"sweep.invalidDirectoryPath",
													"N");
										} else if (output == 4) {
											throw new SwtException(
													"sweep.accountTypeNotFound",
													"N");
										} else if (output == 5) {
											throw new SwtException(
													"sweep.messgeFormatNotDefined",
													"N");
										} else if (output == 9) {
											throw new SwtException(
													"sweep.errorInsertingAlert",
													"N");
										} else if (output == 10) {
											throw new SwtException(
													"sweep.messageGenerationError",
													"N");
										} else {
											submittedSweeps += (sweepItem
													.getId().getSweepId() + ",");

										}
									}
								}
							} else {
								accountBreachingSweeps += (sweepItem.getId()
										.getSweepId() + ",");
							}
						} else {
							calculatedAmountChangesSweeps += (sweepItem.getId()
									.getSweepId() + ",");
						}
					} else {
						cutoffExceedingSweeps += (sweepItem.getId()
								.getSweepId() + ",");
					}
				} else {
					alreadySubmittedSweeps += (sweepItem.getId().getSweepId() + ",");
				}
			}

			if (alreadySubmittedSweeps.length() > 0) {
				actionMessage = new ActionMessage(
						SwtConstants.SWEEP_AlRDY_SUBMIT, alreadySubmittedSweeps
								.substring(0,
										alreadySubmittedSweeps.length() - 1),
						msg1);
				errorList.add(SwtConstants.SWEEP_AlRDY_SUBMIT, actionMessage);
			}
			if (cutoffExceedingSweeps.length() > 0) {
				actionMessage = new ActionMessage(
						SwtConstants.SWEEP_CUTOFF_EXCEEDED,
						cutoffExceedingSweeps.substring(0,
								cutoffExceedingSweeps.length() - 1));
				errorList
						.add(SwtConstants.SWEEP_CUTOFF_EXCEEDED, actionMessage);
			}
			if (calculatedAmountChangesSweeps.length() > 0) {
				actionMessage = new ActionMessage(
						SwtConstants.SWEEP_AMOUNT_CHANGED,
						calculatedAmountChangesSweeps.substring(0,
								calculatedAmountChangesSweeps.length() - 1));
				errorList.add(SwtConstants.SWEEP_AMOUNT_CHANGED, actionMessage);
			}
			if (accountBreachingSweeps.length() > 0) {
				actionMessage = new ActionMessage(
						SwtConstants.SWEEP_ACCOUNT_BREACHED,
						accountBreachingSweeps.substring(0,
								accountBreachingSweeps.length() - 1));
				errorList.add(SwtConstants.SWEEP_ACCOUNT_BREACHED,
						actionMessage);
			}
			if (userlimitExceedingSweeps.length() > 0) {
				actionMessage = new ActionMessage(
						SwtConstants.SWEEP_USERLIMIT_EXCEEDED,
						userlimitExceedingSweeps.substring(0,
								userlimitExceedingSweeps.length() - 1));
				errorList.add(SwtConstants.SWEEP_USERLIMIT_EXCEEDED,
						actionMessage);
			}
			if (submittedSweeps.length() > 0) {
				actionMessage = new ActionMessage(SwtConstants.SWEEP_SAVED,
						submittedSweeps.substring(0,
								submittedSweeps.length() - 1), msg3);
				errorList.add(SwtConstants.SWEEP_SAVED, actionMessage);
			}
			if (nonSubmittedSweeps.length() > 0) {
				actionMessage = new ActionMessage(SwtConstants.SWEEP_NOT_SAVED,
						nonSubmittedSweeps.substring(0, nonSubmittedSweeps
								.length() - 1), msg2);
				errorList.add(SwtConstants.SWEEP_NOT_SAVED, actionMessage);
			}
			log.debug(this.getClass().getName() + ".submit Exiting");
		} catch (Exception exp) {
			 exp.printStackTrace();
			log
					.error("Exception Catch in SweepQueueManagerImpl.'submit' method : "
							+ exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp, "submit",
					SweepQueueManagerImpl.class);
		} finally {
			// Nullify the created instances
			intradaySweepTime = null;
			eodSweepTime = null;
			intradaySweepDateTime = null;
			eodSweepDateTime = null;
			sweepInputTimeMap = null;
			sweepInputDateTime = null;
			acctManager = null;
			acctMaintenance = null;
			msg1 = null;
			msg2 = null;
			msg3 = null;
			alreadySubmittedSweeps = null;
			cutoffExceedingSweeps = null;
			accountBreachingSweeps = null;
			userlimitExceedingSweeps = null;
			submittedSweeps = null;
			nonSubmittedSweeps = null;
			calculatedAmountChangesSweeps = null;
			sweepItem = null;
			list = null;
			itr = null;
			sweepLimitMap = null;
		}
		return errorList;
	}

	public ActionMessages cancel(Sweep sweep, String selectedList,
			SystemFormats systemFormats) throws SwtException {
		ActionMessages errorList = new ActionMessages();
		int out = 1;

		try {
			Collection list = sweepQueueDAO.submit(sweep, selectedList
					.substring(0, selectedList.length() - 1));
			Iterator itr = list.iterator();
			Sweep sweepItem = null;
			double calculatedAmt = 0.0;
			HashMap sweepLimitMap = new HashMap(); // get list from sweeplimits
			// dao
			LabelValueBean labelValueBean = null;
			String msgStr1 = ""; // already submitted by other user
			String msgStr5 = ""; // the sweep successfully cancelled
			String msgStr6 = ""; // error occured in cancel

			while (itr.hasNext()) {
				sweepItem = (Sweep) itr.next();

				if (!SwtConstants.SWEEP_STATUS_CANCEL
						.equalsIgnoreCase(sweepItem.getSweepStatus())) {
					sweepItem.setRequestRole(sweep.getRequestRole());
					sweepItem.setRequestUser(sweep.getRequestUser());
					sweepItem.setQueueName(sweep.getQueueName());
					sweepItem.setDisplaySweepAmount(sweep
							.getDisplaySweepAmount());
					out = sweepQueueDAO.processSweep(sweepItem, systemFormats);

					if (out == 0) {
						msgStr5 += (sweepItem.getId().getSweepId() + ",");
					} else if (out == 1) {
						msgStr6 += (sweepItem.getId().getSweepId() + ",");
					} else if (out == 3) {
						throw new SwtException("sweep.invalidDirectoryPath",
								"N");
					} else if (out == 4) {
						throw new SwtException("sweep.accountTypeNotFound", "N");
					} else if (out == 5) {
						throw new SwtException("sweep.messgeFormatNotDefined",
								"N");
					} else if (out == 9) {
						throw new SwtException("sweep.errorInsertingAlert", "N");
					} else if (out == 10) {
						throw new SwtException("sweep.messageGenerationError",
								"N");
					} else {
						msgStr5 += (sweepItem.getId().getSweepId() + ",");

					}
				} else {
					msgStr1 += (sweepItem.getId().getSweepId() + ",");
				}
			}

			ActionMessage actionMessage = null;

			if (msgStr1.length() > 0) {
				actionMessage = new ActionMessage(
						SwtConstants.SWEEP_AlRDY_SUBMIT, msgStr1.substring(0,
								msgStr1.length() - 1), "cancelled");
				errorList.add(SwtConstants.SWEEP_AlRDY_SUBMIT, actionMessage);
			}

			if (msgStr5.length() > 0) {
				actionMessage = new ActionMessage(SwtConstants.SWEEP_SAVED_ID,
						msgStr5.substring(0, msgStr5.length() - 1), "cancelled");
				errorList.add(SwtConstants.SWEEP_SAVED_ID, actionMessage);
			}

			if (msgStr6.length() > 0) {
				actionMessage = new ActionMessage(SwtConstants.SWEEP_NOT_SAVED,
						msgStr6.substring(0, msgStr6.length() - 1),
						"cancelling");
				errorList.add(SwtConstants.SWEEP_NOT_SAVED, actionMessage);
			}
		} catch (Exception exp) {
			log
					.debug("Exception Catch in SweepQueueManagerImpl.'cancel' method : "
							+ exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp, "cancel",
					SweepQueueManagerImpl.class);
		}

		return errorList;
	}

	private double getCalculatedSweepAmount(Sweep sweep,
			SystemFormats systemFormats) throws SwtException {
		double amt = 0.0;
		double targeBal = 0.0;
		double predictBal = 0.0;
		Double predictBalDbl = null;
		String targetBalanceSign = null;
		double externalBal = 0.0;// To hold external balance
		Double externalBalDbl = null;// To hold external balance in double
		// datatype
		String format = "";// To hold dateformats
		SweepDetailDAO sweepdetailDao = null; // To hold swepdetail dao
		String dateAsString = null;
		String alignedAccountEntityId = null;
		try {
			log.debug(this.getClass().getName()
					+ "- [getCalculatedSweepAmount] -Entry ");
			if (systemFormats.getDateFormatValue().equalsIgnoreCase(
					"MM/dd/yyyy")) {
				format = "M";
			} else {
				format = "D";
			}
			
			sweepdetailDao = (SweepDetailDAO) SwtUtil.getBean("sweepDetailDAO");
			dateAsString = SwtUtil.formatDate(sweep.getValueDate(),
					systemFormats.getDateFormatValue());
			
			if(!SwtUtil.isEmptyOrNull(sweep.getAccountIdOrigin())) {
				if( sweep.getAccountIdOrigin().equals(sweep.getAccountCr().getId().getAccountId())) {
					sweep.getAccountCr().setMinseepamt(sweep.getMinAmount());
					sweep.getAccountCr().setTgtbalsign(sweep.getTargetBalanceType());
					sweep.getAccountCr().setTargetbalance(sweep.getTargetBalance());
					if (sweep.getAccountIdCr().equalsIgnoreCase(
							sweep.getAlignAccountId())) {
						sweep.getAccountCr().setSweepFrmbal(sweep.getSweepFromBalanceTypeCr());
					}else {
						sweep.getAccountCr().setSweepFrmbal(sweep.getSweepFromBalanceTypeDr());
					}
					
				}else {
					sweep.getAccountDr().setMinseepamt(sweep.getMinAmount());
					sweep.getAccountDr().setTgtbalsign(sweep.getTargetBalanceType());
					sweep.getAccountDr().setTargetbalance(sweep.getTargetBalance());
				}
			}

			if (sweep.getAccountIdCr().equalsIgnoreCase(
					sweep.getAlignAccountId())) {
				alignedAccountEntityId = sweep.getEntityIdCr();
				targeBal = sweep.getAccountCr().getTargetbalance()
						.doubleValue();
				targetBalanceSign = sweep.getAccountCr().getTgtbalsign();
			} else {
				alignedAccountEntityId = sweep.getEntityIdDr();
				targeBal = sweep.getAccountDr().getTargetbalance()
						.doubleValue();
				targetBalanceSign = sweep.getAccountDr().getTgtbalsign();
			} 
			if (!actPredictBalMap.containsKey(sweep.getAlignAccountId())) {
				predictBalDbl = sweepQueueDAO.getPredictBalance(
						alignedAccountEntityId, sweep.getId().getHostId(),
						sweep.getCurrencyCode(), sweep.getAlignAccountId(),
						sweep.getValueDate());
				actPredictBalMap.put(sweep.getAlignAccountId(), predictBalDbl);
			}
			predictBal = ((Double) actPredictBalMap.get(sweep
					.getAlignAccountId())).doubleValue();
			// start external balance handling
			if (!actExternalBalMap.containsKey(sweep.getAlignAccountId())) {
				externalBalDbl = sweepdetailDao.getExternalBalance(
						alignedAccountEntityId, sweep.getId().getHostId(),
						sweep.getCurrencyCode(), sweep.getAlignAccountId(),
						dateAsString, format);
				actExternalBalMap
						.put(sweep.getAlignAccountId(), externalBalDbl);
			}
			externalBal = ((Double) actExternalBalMap.get(sweep
					.getAlignAccountId())).doubleValue();

			if (targetBalanceSign.equalsIgnoreCase(SwtConstants.DEBIT)) {
				targeBal = 0 - targeBal;
			}
			
			if (targetBalanceSign.equalsIgnoreCase(SwtConstants.RULE)) {
				amt = sweep.getOriginalSweepAmt();
			}else if ((sweep.getAccountIdCr().equalsIgnoreCase(
					sweep.getAlignAccountId()) && (sweep.getAccountCr()
					.getSweepFrmbal().equalsIgnoreCase("E")))
					|| (sweep.getAccountIdDr(	).equalsIgnoreCase(
							sweep.getAlignAccountId()) && (sweep.getAccountDr()
							.getSweepFrmbal().equalsIgnoreCase("E")))) {
				amt = Math.abs(externalBal - targeBal);
			} else {
				amt = Math.abs(predictBal - targeBal);
			}

			log.debug(this.getClass().getName()
					+ "- [getCalculatedSweepAmount] -Exit ");
		} catch (SwtException exp) {
			log
					.error("Exception Catch in SweepQueueManagerImpl.'getCalculatedSweepAmount' method : "
							+ exp.getMessage());

			throw SwtErrorHandler.getInstance().handleException(exp,
					"getCalculatedSweepAmount", SweepQueueManagerImpl.class);

		} catch (Exception exp) {
			log
					.error("Exception Catch in SweepQueueManagerImpl.'getCalculatedSweepAmount' method : "
							+ exp.getMessage());
			throw new SwtException("errors.sweep.account.changes", "Y", " ",
					" ", " ");
		}

		return amt;
	}

	/**
	 * This method returns the sweep limits for the currencies
	 * 
	 * @param role
	 * @return
	 * @throws SwtException
	 */
	private Map<String, Double> getSweepLimitsMap(String role)
			throws SwtException {
		// Sweeplimitsdao
		SweepLimitsDAO swpLimDao = null;
		// List to store the sweep limits for the logged in user's role
		Collection<SweepLimits> coll = null;
		// Iterator for Sweeplimits collection
		Iterator<SweepLimits> itr = null;
		// Sweeplimits instance
		SweepLimits sweepLimits = null;
		// Map to store the sweep limit for each currency
		HashMap<String, Double> sweepLimitMap = null;
		try {
			// Instantiation of sweep limit dao
			swpLimDao = (SweepLimitsDAO) SwtUtil.getBean("sweepLimitsDAO");
			// Fetch the sweep limit list for the given role
			coll = swpLimDao.getSweepLimitsDetails(role);
			// Instantiate the iterator instance
			itr = coll.iterator();
			// Initialise the map to return the sweep limit list for each
			// currency
			sweepLimitMap = new HashMap<String, Double>();
			// Loop through the list fetched for the role
			while (itr.hasNext()) {
				// Store the sweep limits instance from the list
				sweepLimits = (SweepLimits) itr.next();
				// Add each sweep limit to the map
				sweepLimitMap.put(sweepLimits.getId().getCurrencyCode(),
						sweepLimits.getSweepLimit());
			}
		} catch (Exception e) {
			log
					.error("Exception Catch in SweepQueueManagerImpl.'getSweepLimitsMap' method : "
							+ e.getMessage());
			throw SwtErrorHandler.getInstance().handleException(e,
					"getSweepLimitsMap", SweepQueueManagerImpl.class);
		} finally {
			swpLimDao = null;
			coll = null;
			itr = null;
			sweepLimits = null;
		}
		return sweepLimitMap;
	}

	private boolean getCutoffCondition(AcctMaintenance accCr,
			AcctMaintenance accDr) throws SwtException {
		SimpleDateFormat sdf = new SimpleDateFormat("dd/MM/yyyy HH:mm");

		try {
			log.debug(this.getClass().getName()
					+ "- [getCutoffCondition] -Entry ");
			// sweep cutoff
			String cutoff1 = accCr.getCutoff();
			String cutoff2 = accDr.getCutoff();

			cutoff1 = cutoff1.replace('+', '0');
			cutoff2 = cutoff2.replace('+', '0');

			int index = cutoff1.indexOf(":");
			String hour = cutoff1.substring(0, index);
			String minute = cutoff1.substring(index + 1, cutoff1.length());
			Calendar date1 = new GregorianCalendar();
			date1.setTime(SwtUtil.getSystemDatewithoutTime());
			date1.set(Calendar.HOUR_OF_DAY, Integer.parseInt(hour));
			date1.set(Calendar.MINUTE, Integer.parseInt(minute));

			index = cutoff2.indexOf(":");
			hour = cutoff2.substring(0, index);
			minute = cutoff2.substring(index + 1, cutoff2.length());

			java.util.Calendar date2 = new GregorianCalendar();
			date2.setTime(SwtUtil.getSystemDatewithoutTime());
			date2.set(Calendar.HOUR_OF_DAY, Integer.parseInt(hour));
			date2.set(Calendar.MINUTE, Integer.parseInt(minute));

			// sysdate + server time offset
//			cutoff1 = accCr.getEntity().getEntServerTimeOffSet();
//			cutoff2 = accDr.getEntity().getEntServerTimeOffSet();
			
			
			cutoff1 = SwtUtil.getEntityOffsetTime(accCr.getEntity().getId().getHostId(), accCr.getEntity().getId().getEntityId());
			cutoff2 = SwtUtil.getEntityOffsetTime(accDr.getEntity().getId().getHostId(), accDr.getEntity().getId().getEntityId());

			cutoff1 = cutoff1.replace('+', '0');
			cutoff2 = cutoff2.replace('+', '0');
			index = cutoff1.indexOf(":");
			hour = cutoff1.substring(0, index);
			minute = cutoff1.substring(index + 1, cutoff1.length());

			Calendar calnow1 = new GregorianCalendar();

			calnow1.setTime(SwtUtil.getSystemDatewithTime());
			calnow1.add(Calendar.HOUR_OF_DAY, Integer.parseInt(hour));
			calnow1.add(Calendar.MINUTE, Integer.parseInt(minute));

			index = cutoff2.indexOf(":");
			hour = cutoff2.substring(0, index);
			minute = cutoff2.substring(index + 1, cutoff2.length());

			Calendar calnow2 = new java.util.GregorianCalendar();
			calnow2.setTime(SwtUtil.getSystemDatewithTime());
			calnow2.add(Calendar.HOUR_OF_DAY, Integer.parseInt(hour));
			calnow2.add(Calendar.MINUTE, Integer.parseInt(minute));

			/*
			 * If either of two accounts involved in sweeping uses subaccount
			 * timing,then consider timing of both account's timing,otherwise
			 * consider only subaccount timing to proceed sweep
			 * 
			 */
			
			log.debug(this.getClass().getName()
					+ "- [getCutoffCondition] -Exit ");
			
			if (!"Y".equals(accCr.getSubAcctim())
					&& !"Y".equals(accDr.getSubAcctim())) {

				if (calnow1.before(date1) && calnow2.before(date2)) {

					return true;
				} else {

					return false;
				}
			} else if (!"Y".equals(accCr.getSubAcctim())) {

				if (calnow2.before(date2)) {
					return true;
				} else {

					return false;
				}

			} else if (!"Y".equals(accDr.getSubAcctim())) {

				if (calnow1.before(date1)) {

					return true;
				} else {

					return false;
				}
			}else {
				if (calnow1.before(date1) && calnow2.before(date2)) {
					return true;
				} else {

					return false;
				}
			}

			

		} catch (Exception exp) {
			log
					.error("Exception Catch in SweepQueueManagerImpl.'getCutoffCondition' method : "
							+ exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp,
					"getCutoffCondition", SweepQueueManagerImpl.class);
		}

	}

	private String getDateTime(Date inputDateTime, String format) {
		SimpleDateFormat sdf = new SimpleDateFormat(format + " HH:mm");

		return sdf.format(inputDateTime);
	}

	/*
	 * Start:code modified for Mantis 1394 by chinna on 6-JUN-2011: Full
	 * auto-STP sweeps not shown in Sweep Cancel screen
	 */
	/**
	 * This Method is used to Check the display status and the it returs Display
	 * status
	 * 
	 * @param status
	 *            Identifies the Sweep Status.
	 * @throws SwtException
	 * 
	 * @return disStatus
	 */
	private String getDisplayStatus(String status) throws SwtException {
		// Variable declaration
		String disStatus = null;
		try {
			// Empty or null Check
			if (!SwtUtil.isEmptyOrNull(status)) {
				// Finding Status
				if (SwtConstants.SWEEP_STATUS_NEW.equalsIgnoreCase(status)) {
					disStatus = "New";
				} else if (SwtConstants.SWEEP_STATUS_SUBMIT
						.equalsIgnoreCase(status)) {
					disStatus = "Submitted";

				} else if (SwtConstants.SWEEP_STATUS_STP
						.equalsIgnoreCase(status)) {
					disStatus = "STP";
				} else if (SwtConstants.SWEEP_STATUS_AUTHORIZE
						.equalsIgnoreCase(status)) {
					disStatus = "Authorised";
				} else if (SwtConstants.SWEEP_STATUS_CANCEL
						.equalsIgnoreCase(status)) {
					disStatus = "Cancelled";
				}
			}

		} catch (Exception exp) {
			log
					.error("Exception Catch in SweepQueueManagerImpl.'getDisplayStatus' method : "
							+ exp.getMessage());

			throw SwtErrorHandler.getInstance().handleException(exp,
					"getDisplayStatus", SweepQueueManagerImpl.class);
		}
		// returning Status
		return disStatus;
	}

	/*
	 * End:code modified for Mantis 1394 by chinna on 6-JUN-2011: Full auto-STP
	 * sweeps not shown in Sweep Cancel screen
	 */

	private boolean isSubmitAllowed(String queueName, Sweep sweep) {
		String user = "";
		boolean toReturn = false;

		if (SwtConstants.SWEEP_STATUS_NEW.equalsIgnoreCase(queueName)) {
			toReturn = ((sweep.getSubmitUser() == null) && (sweep
					.getCancelUser() == null));
		} else if (SwtConstants.SWEEP_STATUS_SUBMIT.equalsIgnoreCase(queueName)) {
			toReturn = ((sweep.getAuthorizedUser() == null) && (sweep
					.getCancelUser() == null));
		}

		return toReturn;
	}

	private double getQueueAmount(Sweep sweep) {
		double amount = 0.0;

		if (SwtConstants.SWEEP_TYPE_AUTO.equalsIgnoreCase(sweep.getSweepType())
				&& (SwtConstants.SWEEP_STATUS_NEW.equalsIgnoreCase(sweep
						.getSweepStatus()) || SwtConstants.SWEEP_STATUS_STP
						.equalsIgnoreCase(sweep.getSweepStatus()))) {
			amount = sweep.getOriginalSweepAmt().doubleValue();
		} else if (SwtConstants.SWEEP_TYPE_AUTO.equalsIgnoreCase(sweep
				.getSweepType())
				&& SwtConstants.SWEEP_STATUS_SUBMIT.equalsIgnoreCase(sweep
						.getSweepStatus())) {
			amount = sweep.getSubmitSweepAmt().doubleValue();
		} else if (SwtConstants.SWEEP_TYPE_MANUAL.equalsIgnoreCase(sweep
				.getSweepType())
				&& SwtConstants.SWEEP_STATUS_NEW.equalsIgnoreCase(sweep
						.getSweepStatus())) {
			amount = sweep.getOriginalSweepAmt().doubleValue();
		}

		return amount;
	}

}
