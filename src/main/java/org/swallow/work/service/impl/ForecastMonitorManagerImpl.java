/*
 * @(#)ForecastMonitorManagerImpl.java 1.0 12/06/11
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.work.service.impl;

import java.util.ArrayList;
import java.util.Collection;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.swallow.control.model.EntityAccess;
import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.maintenance.model.CurrencyGroup;
import org.swallow.maintenance.model.CurrencyMaster;
import org.swallow.maintenance.model.ForecastMonitorTemplate;
import org.swallow.util.OpTimer;
import org.swallow.util.SwtConstants;
import org.swallow.util.SwtUtil;
import org.swallow.util.SystemFormats;
import org.swallow.work.dao.ForecastMonitorDAO;
import org.swallow.work.model.AssumptionData;
import org.swallow.work.model.ScenarioData;
import org.swallow.work.model.UserBuckets;
import org.swallow.work.model.UserTemplate;
import org.swallow.work.service.ForecastMonitorManager;
import org.swallow.work.web.form.ForecastRecord;

/**
 * ForecastMonitorManagerImpl.java
 * 
 * ForecastMonitorManagerImpl class implements the ForecastMonitorManager
 * interface
 * 
 * <AUTHOR>
 * @date Feb 12, 2011
 */
@Component("forecastMonitorManager")
public class ForecastMonitorManagerImpl implements ForecastMonitorManager {

	// Create a log instance for logging
	private static final Log log = LogFactory
			.getLog(ForecastMonitorManagerImpl.class);

	/*
	 * ForecastMonitorDAO Object
	 */
	@Autowired
	private ForecastMonitorDAO forecastMonitorDAO;

	/**
	 * No-Arg constructor
	 */
	public ForecastMonitorManagerImpl() {
		forecastMonitorDAO = null;
	}

	/**
	 * @param forecastMonitorDAO
	 *            the forecastMonitorDAO to set
	 */
	public void setForecastMonitorDAO(ForecastMonitorDAO forecastMonitorDao) {
		this.forecastMonitorDAO = forecastMonitorDao;
	}

	/**
	 * Method to get forecast monitor details and predicted balances for the
	 * period specified.
	 * 
	 * @param String
	 *            currGrp
	 * @param String
	 *            userId
	 * @param String
	 *            startDate
	 * @param SystemFormats
	 *            format
	 * @param OpTimer
	 *            opTimer
	 * @param String
	 *            currencyCode
	 * @throws SwtException
	 * @return ForecastRecord
	 */
	public ForecastRecord getForecastMonitorDetails(String currencyCode,
			String entityId, String userId, SystemFormats format,
			OpTimer opTimer, boolean multiplierFlag) throws SwtException {
		// boolean variable
		boolean jobFlag = false;
		// ForecastRecord object
		ForecastRecord forecastRecord = null;
		try {
			log.debug(this.getClass().getName()
					+ " - [getForecastMonitorDetails] - Entry");
			// create instance for EntityRecord
			forecastRecord = new ForecastRecord();
			// Start the optimer
			opTimer.start(SwtConstants.JOB_FLAG);
			// code modified by Bala on 3-10-2011 for Mantis 1600:DataBuild in
			// progress not coming properly
			// Get the jobFlag
			jobFlag = SwtUtil.getMonitorJobFlag(entityId,
					SwtConstants.PROCESS_GROUP_MONITOR);
			// Stop the optimer
			opTimer.stop(SwtConstants.JOB_FLAG);

			// If jobFlag is true, get the balances
			if (jobFlag) {
				forecastRecord = forecastMonitorDAO
						.getAllBalancesUsingStoredProc(opTimer, currencyCode,
								entityId, userId, format, multiplierFlag);
			} else { // else set the jobflag status as true
				forecastRecord
						.setJobFlagStatus(SwtConstants.JOB_FLAG_STATUS_TRUE);
				forecastRecord
						.setForecastMonitorRecords(new ArrayList<ForecastRecord>());
				forecastRecord
						.setGrandTotalRecords(new ArrayList<ForecastRecord>());
			}
			log.debug(this.getClass().getName()
					+ " - [getForecastMonitorDetails] - Exit");
			return forecastRecord;
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					+ " - [getForecastMonitorDetails] - " + exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp,
					"getForecastMonitorDetails",
					ForecastMonitorManagerImpl.class);
		}
	}

	/**
	 * This is used to get the currency group access details
	 * 
	 * @param String
	 *            hostId
	 * @param String
	 *            roleId
	 * @return Collection
	 * @throws SwtException
	 */
	public Collection<CurrencyGroup> getCurrencyGroupAccessDetails(
			String hostId, String roleId) throws SwtException {
		// collection to hold currency group details
		Collection<CurrencyGroup> currencyGroupList = null;
		try {
			log.debug(this.getClass().getName()
					+ " - [getCurrencyGroupAccessDetails] - Entry");
			// Getting collection of currency group
			currencyGroupList = forecastMonitorDAO
					.getCurrencyGroupAccessDetails(hostId, roleId);
			log.debug(this.getClass().getName()
					+ " - [getCurrencyGroupAccessDetails] - Exit");
			return (currencyGroupList);
		} catch (Exception e) {
			log.error(this.getClass().getName()
					+ " - [getCurrencyGroupAccessDetails] - " + e.getMessage());
			throw SwtErrorHandler.getInstance().handleException(e,
					"getCurrencyGroupAccessDetails",
					ForecastMonitorManagerImpl.class);
		}
	}

	/**
	 * This method saves the user template.<br>
	 * 
	 * @param UserTemplate
	 *            userTemplate
	 * @param boolean
	 *            isUpdate
	 * @exception SwtException
	 */
	public void saveUserTemplate(UserTemplate userTemplate, boolean isUpdate)
			throws SwtException {
		try {
			log.debug(this.getClass().getName()
					+ " - [saveUserTemplate] - Entry");
			// calling the method to save personal entity list
			forecastMonitorDAO.saveUserTemplate(userTemplate, isUpdate);
			log.debug(this.getClass().getName()
					+ " - [saveUserTemplate] - Exit");
		} catch (Exception e) {
			log.error(this.getClass().getName() + " - [saveUserTemplate] - "
					+ e.getMessage());
			throw SwtErrorHandler.getInstance().handleException(e,
					"saveUserTemplate", ForecastMonitorManagerImpl.class);
		}
	}

	/**
	 * This method saves the user bucket.<br>
	 * 
	 * @param UserBuckets
	 *            userBucket
	 * @param boolean
	 *            isUpdate
	 * @exception SwtException
	 */
	public void saveUserBucket(UserBuckets userBucket, boolean isUpdate)
			throws SwtException {
		try {
			log
					.debug(this.getClass().getName()
							+ " - [saveUserBucket] - Entry");
			// calling the method to save personal entity list
			forecastMonitorDAO.saveUserBucket(userBucket, isUpdate);
			log.debug(this.getClass().getName() + " - [saveUserBucket] - Exit");
		} catch (Exception e) {
			log.error(this.getClass().getName() + " - [saveUserBucket] - "
					+ e.getMessage());
			throw SwtErrorHandler.getInstance().handleException(e,
					"saveUserBucket", ForecastMonitorManagerImpl.class);
		}
	}

	/**
	 * This method is used to persist the scenario List in the database.<br>
	 * 
	 * @param scenarioData
	 * @throws SwtException
	 */
	public void saveScenarioData(ScenarioData scenarioData) throws SwtException {

		try {
			log
					.debug(this.getClass().getName()
							+ " saveScenarioData() - Enter");
			// calling the method to save personal currency
			forecastMonitorDAO.saveScenarioData(scenarioData);
			log.debug(this.getClass().getName() + " saveScenarioData() - Exit");
		} catch (Exception e) {
			log.error(this.getClass().getName() + " - [saveScenarioData] - "
					+ e.getMessage());
			throw SwtErrorHandler.getInstance().handleException(e,
					"saveScenarioData", ForecastMonitorManagerImpl.class);
		}
	}

	/**
	 * This method is used to get the user entity list details database.<br>
	 * 
	 * @param hostId
	 * @param roleId
	 *            return Collection
	 * @throws SwtException
	 */
	public Collection<EntityAccess> getUserEntityList(String hostId,
			String roleId) throws SwtException {
		// Declare EntityAccess collection
		Collection<EntityAccess> coll = null;
		try {
			log.debug(this.getClass().getName()
					+ " getUserEntityList() - Enter");
			// Instantiate ArrayList
			coll = new ArrayList<EntityAccess>();
			// calling the method for getting the entity list based on the user
			coll = forecastMonitorDAO.getUserEntityList(hostId, roleId);
			log
					.debug(this.getClass().getName()
							+ " getUserEntityList() - Exit");
		} catch (Exception e) {
			log.error(this.getClass().getName() + " - [getUserEntityList] - "
					+ e.getMessage());
			throw SwtErrorHandler.getInstance().handleException(e,
					"getUserEntityList", ForecastMonitorManagerImpl.class);
		}
		return coll;
	}

	/**
	 * This method is used to get the user template list details database.<br>
	 * 
	 * @param hostId
	 * @param userId
	 * @return Collection
	 * @throws SwtException
	 */
	public Collection<UserTemplate> getUserTemplateList(String hostId,
			String userId) throws SwtException {
		// Declare userTemplate collection
		Collection<UserTemplate> coll = null;
		try {
			log.debug(this.getClass().getName()
					+ " getUserTemplateList() - Enter");
			// Instantiate ArrayList
			coll = new ArrayList<UserTemplate>();
			// calling the method for getting the entity list based on the user
			coll = forecastMonitorDAO.getUserTemplateList(hostId, userId);
			log.debug(this.getClass().getName()
					+ " getUserTemplateList() - Exit");
		} catch (Exception e) {
			log.error(this.getClass().getName() + " - [getUserTemplateList] - "
					+ e.getMessage());
			throw SwtErrorHandler.getInstance().handleException(e,
					"getUserTemplateList", ForecastMonitorManagerImpl.class);
		}
		return coll;
	}

	/**
	 * This method is used to get the user bucket list details database.<br>
	 * 
	 * @param hostId
	 * @param userId
	 * @return Collection
	 * @throws SwtException
	 */
	public Collection<UserBuckets> getUserBucketList(String hostId,
			String userId) throws SwtException {
		// Declare userBuckets collection
		Collection<UserBuckets> coll = null;
		try {
			log.debug(this.getClass().getName()
					+ " getUserBucketList() - Enter");
			// Instantiate ArrayList
			coll = new ArrayList<UserBuckets>();
			// calling the method for getting the entity list based on the user
			coll = forecastMonitorDAO.getUserBucketList(hostId, userId);
			log
					.debug(this.getClass().getName()
							+ " getUserBucketList() - Exit");
		} catch (Exception e) {
			log.error(this.getClass().getName() + " - [getUserBucketList] - "
					+ e.getMessage());
			throw SwtErrorHandler.getInstance().handleException(e,
					"getUserBucketList", ForecastMonitorManagerImpl.class);
		}
		return coll;
	}

	/**
	 * This method is used to check the entity access
	 * 
	 * @param hostId
	 * @param roleId
	 * @param entityId
	 * @return boolean
	 * @throws SwtException
	 */
	public boolean checkEntityAccess(String hostId, String roleId,
			String entityId) throws SwtException {
		boolean checkAccess = true;
		try {
			log.debug(this.getClass().getName()
					+ " getUserBucketList() - Enter");
			// calling the method for getting the entity list based on the user
			checkAccess = forecastMonitorDAO.checkEntityAccess(hostId, roleId,
					entityId);
			log
					.debug(this.getClass().getName()
							+ " getUserBucketList() - Exit");
		} catch (Exception e) {
			log.error(this.getClass().getName() + " - [getUserBucketList] - "
					+ e.getMessage());
			throw SwtErrorHandler.getInstance().handleException(e,
					"getUserBucketList", ForecastMonitorManagerImpl.class);
		}
		return checkAccess;
	}

	/**
	 * This method is used to get the user template list details database.<br>
	 * 
	 * @param hostId
	 * @param userId
	 * @return Collection
	 * @throws SwtException
	 */
	public Collection<ForecastMonitorTemplate> getForecastTemplate(
			String hostId, String userId) throws SwtException {
		// Declare ForecastMonitorTemplate collection
		Collection<ForecastMonitorTemplate> coll = null;
		try {
			log.debug(this.getClass().getName()
					+ " getForecastTemplate() - Enter");
			coll = new ArrayList<ForecastMonitorTemplate>();
			// calling the method for getting the entity list based on the user
			coll = forecastMonitorDAO.getForecastTemplate(hostId, userId);
			log.debug(this.getClass().getName()
					+ " getForecastTemplate() - Exit");
		} catch (Exception e) {
			log.error(this.getClass().getName() + " - [getForecastTemplate] - "
					+ e.getMessage());
			throw SwtErrorHandler.getInstance().handleException(e,
					"getForecastTemplate", ForecastMonitorManagerImpl.class);
		}
		return coll;
	}

	/**
	 * This method is used to get the assumption list details database.<br>
	 * 
	 * @param AssumptionData
	 *            object
	 * @return Collection
	 * @throws SwtException
	 */
	public Collection<AssumptionData> getForecastAssumption(
			AssumptionData assumptionData) throws SwtException {
		// Declare AssumptionData collection
		Collection<AssumptionData> coll = null;
		try {
			log.debug(this.getClass().getName()
					+ " getForecastAssumption() - Enter");
			coll = new ArrayList<AssumptionData>();
			// calling the method for getting the entity list based on the user
			coll = forecastMonitorDAO.getForecastAssumption(assumptionData);
			log.debug(this.getClass().getName()
					+ " getForecastAssumption() - Exit");
		} catch (Exception e) {
			log.error(this.getClass().getName()
					+ " - [getForecastAssumption] - " + e.getMessage());
			throw SwtErrorHandler.getInstance().handleException(e,
					"getForecastAssumption", ForecastMonitorManagerImpl.class);
		}
		return coll;
	}

	/**
	 * This method is used to save the assumption list details database.<br>
	 * 
	 * @param AssumptionData
	 *            object
	 * @throws SwtException
	 */
	public void saveForecastAssumption(AssumptionData assumptionData)
			throws SwtException {
		try {
			log.debug(this.getClass().getName()
					+ " saveForecastAssumption() - Enter");
			// calling the method for getting the entity list based on the user
			forecastMonitorDAO.saveForecastAssumption(assumptionData);
			log.debug(this.getClass().getName()
					+ " saveForecastAssumption() - Exit");
		} catch (Exception e) {
			log.error(this.getClass().getName()
					+ " - [saveForecastAssumption] - " + e.getMessage());
			throw SwtErrorHandler.getInstance().handleException(e,
					"saveForecastAssumption", ForecastMonitorManagerImpl.class);
		}
	}

	/**
	 * This method is used to delete the assumption list details database.<br>
	 * 
	 * @param AssumptionData
	 *            object
	 * @throws SwtException
	 */
	public void deleteForecastAssumption(AssumptionData assumptionData)
			throws SwtException {
		try {
			log.debug(this.getClass().getName()
					+ " deleteForecastAssumption() - Enter");
			// calling the method for getting the entity list based on the user
			forecastMonitorDAO.deleteForecastAssumption(assumptionData);
			log.debug(this.getClass().getName()
					+ " deleteForecastAssumption() - Exit");
		} catch (Exception e) {
			log.error(this.getClass().getName()
					+ " - [deleteForecastAssumption] - " + e.getMessage());
			throw SwtErrorHandler.getInstance().handleException(e,
					"deleteForecastAssumption",
					ForecastMonitorManagerImpl.class);
		}
	}

	/**
	 * This method is used to delete the forecast monitor list details database.<br>
	 * 
	 * @param UserTemplate
	 *            object
	 * @throws SwtException
	 */
	public void deleteForecastUserTemplate(UserTemplate userTemplate)
			throws SwtException {
		try {
			log.debug(this.getClass().getName()
					+ " deleteForecastUserTemplate() - Enter");
			// calling the method for getting the entity list based on the user
			forecastMonitorDAO.deleteForecastUserTemplate(userTemplate);
			log.debug(this.getClass().getName()
					+ " deleteForecastUserTemplate() - Exit");
		} catch (Exception e) {
			log.error(this.getClass().getName()
					+ " - [deleteForecastUserTemplate] - " + e.getMessage());
			throw SwtErrorHandler.getInstance().handleException(e,
					"deleteForecastUserTemplate",
					ForecastMonitorManagerImpl.class);
		}
	}

	/**
	 * This method is used to delete the forecast monitor bucket details
	 * database.<br>
	 * 
	 * @param UserBuckets
	 *            object
	 * @param boolean
	 *            isAll
	 * @throws SwtException
	 */
	public void deleteForecastBucket(UserBuckets userBuckets, boolean isAll)
			throws SwtException {
		try {
			log.debug(this.getClass().getName()
					+ " deleteForecastBucket() - Enter");
			// calling the method for getting the entity list based on the user
			forecastMonitorDAO.deleteForecastBucket(userBuckets, isAll);
			log.debug(this.getClass().getName()
					+ " deleteForecastBucket() - Exit");
		} catch (Exception e) {
			log.error(this.getClass().getName()
					+ " - [deleteForecastBucket] - " + e.getMessage());
			throw SwtErrorHandler.getInstance().handleException(e,
					"deleteForecastBucket", ForecastMonitorManagerImpl.class);
		}
	}

	/**
	 * This is used to get the currency for all entity
	 * 
	 * @param String
	 *            userId
	 * @return Collection
	 * @throws SwtException
	 */
	public Collection<CurrencyMaster> getCurrencyForAllEntity(String userId)
			throws SwtException {
		// collection to hold currency group details
		Collection<CurrencyMaster> currencyList = null;
		try {
			log.debug(this.getClass().getName()
					+ " - [getCurrencyForAllEntity] - Entry");
			// Getting collection of currency group
			currencyList = forecastMonitorDAO.getCurrencyForAllEntity(userId);
			log.debug(this.getClass().getName()
					+ " - [getCurrencyForAllEntity] - Exit");
			return currencyList;
		} catch (Exception e) {
			log.error(this.getClass().getName()
					+ " - [getCurrencyForAllEntity] - " + e.getMessage());
			throw SwtErrorHandler.getInstance()
					.handleException(e, "getCurrencyForAllEntity",
							ForecastMonitorManagerImpl.class);
		}
	}

	/**
	 * This method is used to get the template id
	 * 
	 * @param Scenario
	 *            object
	 * @return String
	 * @throws SwtException
	 */
	public String getTemplateId(ScenarioData scenarioData) throws SwtException {
		// variable to hold templateId
		String templateId = null;
		try {
			log.debug(this.getClass().getName() + " getTemplateId() - Enter");
			// calling the method for getting the template id
			templateId = forecastMonitorDAO.getTemplateId(scenarioData);
			log.debug(this.getClass().getName() + " getTemplateId() - Exit");
		} catch (Exception e) {
			log.error(this.getClass().getName() + " - [getTemplateId] - "
					+ e.getMessage());
			throw SwtErrorHandler.getInstance().handleException(e,
					"getTemplateId", ForecastMonitorManagerImpl.class);
		}
		return templateId;
	}

	/**
	 * This method is used to get the ForecastMonitorTemplate Details
	 * 
	 * @param ForecastMonitorTemplate
	 *            forecastMonitorTemplate
	 * @return ForecastMonitorTemplate object
	 * @throws SwtException
	 */
	public ForecastMonitorTemplate getForecastMonitorTemplateDetails(
			ForecastMonitorTemplate forecastMonitorTemplate)
			throws SwtException {
		// ForecastMonitorTemplate object to hold the details
		ForecastMonitorTemplate forecastTemplateDetails = null;
		try {
			log.debug(this.getClass().getName()
					+ " getForecastMonitorTemplateDetails() - Enter");
			// calling the method for getting the forecastMonitor
			// Template Details
			forecastTemplateDetails = forecastMonitorDAO
					.getForecastMonitorTemplateDetails(forecastMonitorTemplate);
			log.debug(this.getClass().getName()
					+ " getForecastMonitorTemplateDetails() - Exit");
		} catch (Exception e) {
			log.error(this.getClass().getName()
					+ " - [getForecastMonitorTemplateDetails] - "
					+ e.getMessage());
			throw SwtErrorHandler.getInstance().handleException(e,
					"getForecastMonitorTemplateDetails",
					ForecastMonitorManagerImpl.class);
		}
		return forecastTemplateDetails;
	}

}