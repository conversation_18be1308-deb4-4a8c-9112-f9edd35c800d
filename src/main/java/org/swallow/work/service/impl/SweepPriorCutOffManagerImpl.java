/*
 * Created on Jul 31, 2007
 *
 * TODO To change the template for this generated file go to
 * Window - Preferences - Java - Code Style - Code Templates
 */
package org.swallow.work.service.impl;

import java.util.Collection;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.swallow.control.service.impl.AuditLogManagerImpl;
import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.util.SystemFormats;
import org.swallow.work.dao.SweepPriorCutOffDAO;
import org.swallow.work.service.SweepPriorCutOffManager;

/**
 * <AUTHOR>
 *
 * TODO To change the template for this generated type comment go to
 * Window - Preferences - Java - Code Style - Code Templates
 */
@Component("sweepPriorCutOffManager")
public class SweepPriorCutOffManagerImpl implements SweepPriorCutOffManager {
	private final Log log = LogFactory.getLog(SweepPriorCutOffManagerImpl.class);
	private SweepPriorCutOffDAO sweepPriorCutOffDAO;
	
	@Autowired
	public void setSweepPriorCutOffDAO(SweepPriorCutOffDAO sweepPriorCutOffDAO) {
		this.sweepPriorCutOffDAO = sweepPriorCutOffDAO;
	}

	public Collection getSweepPriorToCutOffListUsingStoredProc( String hostId, String entityId, String subAccount, int leadTime, int extendDisplayTimeBy, SystemFormats format)
	throws SwtException {
		try{
			log.debug("Entering getSweepPriorToCutOffListUsingStoredProc method");
			return sweepPriorCutOffDAO.getSweepPriorToCutOffListUsingStoredProc(hostId, entityId, subAccount, leadTime, extendDisplayTimeBy, format);

		}catch(Exception e){
            throw SwtErrorHandler.getInstance().handleException(e,"getSweepPriorToCutOffListUsingStoredProc",SweepPriorCutOffManagerImpl.class);

    	}
	}

	public Collection getMainAccountDetails( String hostId, String entityId, String mainAccountId)
	throws SwtException {
		try{
			log.debug("Entering getMainAccountDetails method");
			return sweepPriorCutOffDAO.getMainAccountDetails(hostId, entityId, mainAccountId);

		}catch(Exception e){
            throw SwtErrorHandler.getInstance().handleException(e,"getSweepPriorToCutOffListUsingStoredProc",SweepPriorCutOffManagerImpl.class);

    	}

	}
}
