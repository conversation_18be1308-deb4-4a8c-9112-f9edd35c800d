/* Swallow Technology
 * All Rights Reserved.
 * This software and documentation is the confidential and proprietary
 * information of Perot Systems ("Confidential Information").
 * You shall not disclose such Confidential Information and shall use
 * it only in accordance with the terms of the license agreement you
 * entered into with Perot Systems.
 * Unauthorized reproduction or distribution of this Confidential
 * Information, or any portion of it, may result in severe civil
 * and criminal penalties.
 *
 * Developed by Perot Systems .
 */

package org.swallow.work.service.impl;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.util.SwtConstants;
import org.swallow.work.dao.MatchDAO;
import org.swallow.work.model.MatchQueue;
import org.swallow.work.model.Movement;
import org.swallow.work.service.MatchDetailVO;
import org.swallow.work.service.MatchManager;

/**
 * <AUTHOR> Tripathi
 * @version 11th Jan 2007
 * <pre>
 * Manager layer for Match Display screen, Used to
 * - Display Offered / Suspend / Confirmed queues screen
 * - Display Match Source details
 * - Update broken match
 * </pre>
 */
@Component("matchManager")
public class MatchManagerImpl implements MatchManager {
	/**
	 * Comment for <code>log</code>
	 */
	private final Log log = LogFactory.getLog(MatchManagerImpl.class);
	/**
	 * Comment for <code>dao</code>
	 */
	@Autowired
	private MatchDAO matchDAO;

	/*
	 * (non-Javadoc)
	 * 
	 * @see org.swallow.work.service.MatchManager#setMatchDAO(org.swallow.work.dao.MatchDAO)
	 */
	public void setMatchDAO(MatchDAO matchDAO) {
		this.matchDAO = matchDAO;
	}

	// Start: Code modified by Bala on 19102010 for Mantis 1209
	/**
	 * This method is used to fetch screen data for all the Match Screen's
	 * 
	 * @param entityId
	 * @param hostId
	 * @param currencyGrpId
	 * @param status
	 * @param roleId
	 * @param selectedTabIndex
	 * @param applyCurrencyThreshold
	 * @param noIncludedMovementMatches
	 * @param date
	 * @return
	 * @throws SwtException
	 */
	public MatchDetailVO getMatchScreenData(String entityId, String hostId,
			String currencyGrpId, String status, String roleId,
			String selectedTabIndex, String applyCurrencyThreshold,
			String noIncludedMovementMatches, Date date) throws SwtException {
		/*
		 * Start : Mantis Issue 1306 : Code added for Enhancement to Non-Working
		 * Days (Holidays and Weekends) by Arumugam on 08-Mar-2011
		 */
		// Object to hold arrobj
		ArrayList<Object> arrObj = new ArrayList<Object>();
		// MatchDetailVO Instance
		MatchDetailVO matchDetailsVO = new MatchDetailVO();
		try {
			log
					.debug(this.getClass().getName()
							+ "-[getMatchScreenData]-Entry");
			// To collesct data through data layer and store the array object
			arrObj = matchDAO.getMatchDetailsUsingStoredProc(hostId, entityId,
					currencyGrpId, roleId, selectedTabIndex,
					applyCurrencyThreshold, noIncludedMovementMatches, status,
					date);
			// To arrobject values to fetch and store the bean Instance
			matchDetailsVO.setMatchListDetailsForSelectedTab(arrObj.get(1));
			matchDetailsVO.setTabFlag((String)arrObj.get(0));
			/*
			 * End : Mantis Issue 1306 : Code added for Enhancement to
			 * Non-Working Days (Holidays and Weekends) by Arumugam on
			 * 08-Mar-2011
			 */
		} catch (Exception exp) {
			log.error("Exception caught in " + this.getClass().getName()
					+ "-[getMatchScreenData]-" + exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp,
					"getMatchScreenData", this.getClass());
		} finally {
			log.debug(this.getClass().getName() + "-[getMatchScreenData]-Exit");
		}
		return matchDetailsVO;
		// End: Code modified by Bala on 19102010 for Mantis 1209
	}

	/**
	 * This method repopulates and updates highest_position level, lowest
	 * position level, max value date and max amount of movements associated
	 * with given match id.
	 * 
	 * @param hostId
	 * @param entityId
	 * @param matchIds[]
	 * @throws SwtException
	 */
	public void updateBrokenMatch(String hostId, String entityId,
			HashSet matchIds) throws SwtException {
		if (matchIds != null && matchIds.size() > 0) {
			Iterator it = matchIds.iterator();
			while (it.hasNext()) {
				matchDAO.updateBrokenMatch(hostId, entityId, ((Long) (it.next()))
						.longValue());
			}
		}
	}

	/**
	 * This method returns the string array containing INPUT_HST,
	 * highestPositionLevel and orgPredictStatusList from the remaining
	 * movements of match.
	 * 
	 * @param lockedMap
	 * @param movementObjctList
	 * @return String[INPUT_HST,highestPositionLevel,orgPredictStatusList]
	 */
	public HashMap getMatchSRCDetsils(Collection movementObjctList) {
		HashMap matchSRCDetails = new HashMap();
		String inputHst = SwtConstants.EMPTY_STRING;
		Double maxAmount = new Double(0);
		Integer highestPositionLevel = null;
		Integer lowestPositionLevel = null;
		Date highestValueDate = null;
		String predictStatusFlag = SwtConstants.NO;
		StringBuffer orgPredictStatusList = new StringBuffer(
				SwtConstants.EMPTY_STRING);// seperated by ','
		boolean firstIterationFlag = true;
		Iterator itmoveList = movementObjctList.iterator();
		while (itmoveList.hasNext()) {
			Movement movement = (Movement) itmoveList.next();

			if (firstIterationFlag) {
				highestValueDate = movement.getValueDate();
				lowestPositionLevel = movement.getPositionLevel();
				highestPositionLevel = lowestPositionLevel;
				firstIterationFlag = false;
			} else {
				if (highestValueDate.getTime() < movement.getValueDate()
						.getTime()) {
					highestValueDate = movement.getValueDate();
				}
			}
			if (maxAmount.compareTo(movement.getAmount()) < 0) {
				maxAmount = movement.getAmount();
			}
			String inputSrc = movement.getInputSource();
			if (inputSrc != null && inputSrc.equals(SwtConstants.INPUT_HST)) {
				inputHst = "Y";
			}
			if (highestPositionLevel.intValue() < movement.getPositionLevel()
					.intValue()) {
				highestPositionLevel = movement.getPositionLevel();
			}
			if (lowestPositionLevel.intValue() > movement.getPositionLevel()
					.intValue()) {
				lowestPositionLevel = movement.getPositionLevel();
			}
			if (movement.getPredictStatus() != null
					&& movement.getPredictStatus().equalsIgnoreCase("I")) {
				predictStatusFlag = SwtConstants.YES;
			}
			orgPredictStatusList.append(movement.getPredictStatus());

		}
		matchSRCDetails.put("inputHst", inputHst);
		matchSRCDetails.put("highestPositionLevel", highestPositionLevel);
		matchSRCDetails.put("orgPredictStatusList", orgPredictStatusList
				.toString());
		matchSRCDetails.put("highestValueDate", highestValueDate);
		matchSRCDetails.put("lowestPositionLevel", lowestPositionLevel);
		matchSRCDetails.put("maxAmount", maxAmount);
		matchSRCDetails.put("predictStatusFlag", predictStatusFlag);
		return matchSRCDetails;
	}

}