/*
 * @(#)InputExceptionsMessagesManagerImpl.java 1.0 23/08/06
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.work.service.impl;

import java.sql.Clob;
import java.util.Collection;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.util.OpTimer;
import org.swallow.util.PropertiesFileLoader;
import org.swallow.util.SwtConstants;
import org.swallow.util.SwtUtil;
import org.swallow.work.dao.InputExceptionsMessagesDAO;
import org.swallow.work.model.InputExceptionsDataModel;
import org.swallow.work.model.InputExceptionsDataPageDTO;
import org.swallow.work.service.InputExceptionsMessagesManager;

@Component("inputExceptionsMessagesManager")
public class InputExceptionsMessagesManagerImpl implements
		InputExceptionsMessagesManager {

	/**
	 * Initializing logger object for this class
	 */
	private final Log log = LogFactory
			.getLog(InputExceptionsMessagesManagerImpl.class);

	@Autowired
	private InputExceptionsMessagesDAO inputExceptionsMessagesDAO = null;

	/**
	 * Method to set InputExceptionsMessagesDAO
	 * 
	 * @param inputExceptionsMessagesDAO
	 */
	public void setInputExceptionsMessagesDAO(
			InputExceptionsMessagesDAO inputExceptionsMessagesDAO) {
		this.inputExceptionsMessagesDAO = inputExceptionsMessagesDAO;
	}

	/**
	 * Method to get exception messages for message status and message type
	 * 
	 * @param page
	 * @param opTimer
	 * @param dateFormat
	 * @return InputExceptionsDataPageDTO
	 * @throws SwtException
	 */
	public InputExceptionsDataPageDTO getMessages(
			InputExceptionsDataPageDTO page, OpTimer opTimer, String dateFormat, boolean fromPCM, boolean fromDashboard)
			throws SwtException {

		log.debug(this.getClass().getName() + " - [getMessages] -"
				+ "Entry - Return DAO.messagePage");
		
		if(!fromPCM) {
			return inputExceptionsMessagesDAO.getMessagePage(page, opTimer, dateFormat);
		}
		else {
			InputExceptionsMessagesDAO inputExceptionsMessagesDAOPCM = (InputExceptionsMessagesDAO)SwtUtil.getBean("inputExceptionsMessagesDAOPCM");
			return inputExceptionsMessagesDAOPCM.getMessagePagePCM(page, opTimer, dateFormat, fromDashboard);
		}
	}

	/**
	 * Method to get message of selected sequence id
	 * 
	 * @param seqId
	 * @param opTimer
	 * @return String
	 * @throws SwtException
	 */
	public String getMessageBody(String seqId, OpTimer opTimer, boolean fromPCM)
			throws SwtException {

		log.debug(this.getClass().getName() + " - [getMessageBody] - "
				+ "Entry");
		/* Method's local variable declaration */
		InputExceptionsDataModel iedm;
		String message = null;
		Clob clob;

		/* Get archive message for selected sequence id from DAO */
		if(!fromPCM) {
			iedm = (InputExceptionsDataModel) inputExceptionsMessagesDAO
					.getArchiveMessage(seqId, opTimer);
			
		}
		else {
			InputExceptionsMessagesDAO inputExceptionsMessagesDAOPCM = (InputExceptionsMessagesDAO)SwtUtil.getBean("inputExceptionsMessagesDAOPCM");
			iedm = (InputExceptionsDataModel) inputExceptionsMessagesDAOPCM
					.getArchiveMessage(seqId, opTimer);
		}
		
		if (iedm == null)
			throw new SwtException("Archive record " + seqId
					+ " does not exist");

		/* Condition to check Data model bean is not null */
		if (iedm != null) {
			/* Get message for selected sequence Id in clob */
			clob = iedm.getMessageBody();
			try {
				/* Get message details from clob as string */
				if (clob != null)
					message = clob.getSubString(1, (int) clob.length());
				else
					message = "";
			} catch (Exception e) {

				log
						.debug(this.getClass().getName()
								+ " - Exception Catched in [getMessageBody] method : - "
								+ e.getMessage());

				log
						.error(this.getClass().getName()
								+ " - Exception Catched in [getMessageBody] method : - "
								+ e.getMessage());

				throw new SwtException("Could not retrieve message body "
						+ seqId + " from CLOB: " + message);
			}
		}
		log
				.debug(this.getClass().getName() + " - [getMessageBody] - "
						+ "Exit");
		return message;
	}

	/* Start: Modified for Mantis 1446 by Marshal on 21-Oct-2011 */
	/**
	 * Method to reprocess exception message for selected sequence id
	 * 
	 * @param seqId
	 * @param opTimer
	 * @return boolean
	 * @throws SwtException
	 */
	public boolean reprocessMessage(String[] seqIds, OpTimer opTimer, boolean fromPCM)
			throws SwtException {
		/* Method's local variable declaration */
		// Flag represents the status of reprocessing
		boolean result = false;
		// Holds the table name to get the messages
		String temporaryTableName = null;
		// Holds the table name to get the failed messages
		String failedTableName = null;
		// Holds the primary key for the failed message
		String failedTablePk = null;
		// Declares the InputExceptionsDataModel
		InputExceptionsDataModel archiveModel = null;
		try {
			log.debug(this.getClass().getName() + " - [reprocessMessage] - "
					+ "Entry");
			/* Get I_MESSAGEDETAILSTEMP from Swt common properties */
			temporaryTableName = PropertiesFileLoader
					.getInstance()
					.getPropertiesValue(
							SwtConstants.PROPERTY_INTERFACES_TABLE_TEMPORARY_NAME);
			/* Get I_MESSAGEDETAILS_FAILED table name from swt common properties */
			failedTableName = PropertiesFileLoader.getInstance()
					.getPropertiesValue(
							SwtConstants.PROPERTY_INTERFACES_TABLE_FAILED_NAME);
			/* Read row id from swt common properties */
			failedTablePk = PropertiesFileLoader.getInstance()
					.getPropertiesValue(
							SwtConstants.PROPERTY_INTERFACES_TABLE_FAILED_PK);
			// Gets the archive message if the message id is available
			if (seqIds.length > 0 && temporaryTableName != null
					&& failedTableName != null && failedTablePk != null) {
				
				/* Get archive message for selected sequence Id */
				if(!fromPCM) {
					archiveModel = inputExceptionsMessagesDAO.getArchiveMessage(
							seqIds[0], opTimer);
				}
				else {
					InputExceptionsMessagesDAO inputExceptionsMessagesDAOPCM = (InputExceptionsMessagesDAO)SwtUtil.getBean("inputExceptionsMessagesDAOPCM");
					archiveModel = inputExceptionsMessagesDAOPCM.getArchiveMessage(
							seqIds[0], opTimer);
				}
				
				/* Condition to check model bean is null */
				if (archiveModel == null) {
					throw new SwtException("Archive record " + seqIds[0]
							+ " does not exist");
				}
				// Starts the timer for reprocessing
				opTimer.start("query-reprocess");
				// Calls the DAO layer to reprocess the given messages
				if(!fromPCM) {
					inputExceptionsMessagesDAO.reprocess(seqIds, opTimer, fromPCM);
				}
				else {
					InputExceptionsMessagesDAO inputExceptionsMessagesDAOPCM = (InputExceptionsMessagesDAO)SwtUtil.getBean("inputExceptionsMessagesDAOPCM");
					inputExceptionsMessagesDAOPCM.reprocess(seqIds, opTimer, fromPCM);
				}
				
				// Stops the timer
				opTimer.stop("query-reprocess");
				// Sets the success flag
				result = true;
			} else {
				// Sets the failure flag
				result = false;
			}
			log.debug(this.getClass().getName() + " - [reprocessMessage] - "
					+ "Exit");
		} catch (Exception e) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [reprocessMessage] method : - "
					+ e.getMessage());
			throw new SwtException("Could not reprocess message"
					+ (seqIds.length > 1 ? "s" : "") + ": " + e.getMessage());
		}
		return result;
	}
	/* End: Modified for Mantis 1446 by Marshal on 21-Oct-2011 */
	
	/**
	 * Method to suppress or reject exception message for selected sequence id
	 * 
	 * @param seqId
	 * @param opTimer
	 * @return boolean
	 * @throws SwtException
	 */
	public boolean suppressOrRejectMessage(String[] seqIds,String pProcessOption, OpTimer opTimer, boolean fromPCM)
			throws SwtException {
		/* Method's local variable declaration */
		// Flag represents the status of suppressing or rejecting
		boolean result = false;
		// Holds the table name to get the messages
		String temporaryTableName = null;
		// Holds the table name to get the failed messages
		String failedTableName = null;
		// Holds the primary key for the failed message
		String failedTablePk = null;
		// Declares the InputExceptionsDataModel
		InputExceptionsDataModel archiveModel = null;
		try {
			log.debug(this.getClass().getName() + " - [suppressOrRejectMessage] - "
					+ "Entry");
			/* Get I_MESSAGEDETAILSTEMP from Swt common properties */
			temporaryTableName = PropertiesFileLoader
					.getInstance()
					.getPropertiesValue(
							SwtConstants.PROPERTY_INTERFACES_TABLE_TEMPORARY_NAME);
			/* Get I_MESSAGEDETAILS_FAILED table name from swt common properties */
			failedTableName = PropertiesFileLoader.getInstance()
					.getPropertiesValue(
							SwtConstants.PROPERTY_INTERFACES_TABLE_FAILED_NAME);
			/* Read row id from swt common properties */
			failedTablePk = PropertiesFileLoader.getInstance()
					.getPropertiesValue(
							SwtConstants.PROPERTY_INTERFACES_TABLE_FAILED_PK);
			// Gets the archive message if the message id is available
			if (seqIds.length > 0 && temporaryTableName != null
					&& failedTableName != null && failedTablePk != null) {
				/* Get archive message for selected sequence Id */
				if(!fromPCM) {
					archiveModel = inputExceptionsMessagesDAO.getArchiveMessage(
							seqIds[0], opTimer);
				}
				else {
					InputExceptionsMessagesDAO inputExceptionsMessagesDAOPCM = (InputExceptionsMessagesDAO)SwtUtil.getBean("inputExceptionsMessagesDAOPCM");
					archiveModel = inputExceptionsMessagesDAOPCM.getArchiveMessage(
							seqIds[0], opTimer);
				}
				/* Condition to check model bean is null */
				if (archiveModel == null) {
					throw new SwtException("Archive record " + seqIds[0]
							+ " does not exist");
				}
				// Starts the timer for suppressing or rejecting
				opTimer.start("query-"+pProcessOption);
				// Calls the DAO layer to suppress or reject the given messages
				if(!fromPCM) {
					inputExceptionsMessagesDAO.suppressOrReject(seqIds,pProcessOption, opTimer, fromPCM);
				}
				else {
					InputExceptionsMessagesDAO inputExceptionsMessagesDAOPCM = (InputExceptionsMessagesDAO)SwtUtil.getBean("inputExceptionsMessagesDAOPCM");
					inputExceptionsMessagesDAOPCM.suppressOrReject(seqIds,pProcessOption, opTimer, fromPCM);
				}
				// Stops the timer
				opTimer.stop("query-"+pProcessOption);
				// Sets the success flag
				result = true;
			} else {
				// Sets the failure flag
				result = false;
			}
			log.debug(this.getClass().getName() + " - [suppressOrRejectMessage] - "
					+ "Exit");
		} catch (Exception e) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [suppressOrRejectMessage] method : - "
					+ e.getMessage());
			throw new SwtException("Could not "+pProcessOption+" message"
					+ (seqIds.length > 1 ? "s" : "") + ": " + e.getMessage());
		}
		return result;
	}

	public Collection<String> getMessageTypeList(String currencyCode, String status, String date, String dateFormat) throws SwtException {
		try {
			log.debug(this.getClass().getName() + " - [getMessageTypeList] - Entry/ Exit");
			InputExceptionsMessagesDAO inputExceptionsMessagesDAOPCM = (InputExceptionsMessagesDAO)SwtUtil.getBean("inputExceptionsMessagesDAOPCM");
			return inputExceptionsMessagesDAOPCM.getMessageTypeList(currencyCode, status, date, dateFormat);
		} catch (Exception e) {
			log.error(this.getClass().getName() + " - [getMessageTypeList] - " + e.getMessage());
			throw SwtErrorHandler.getInstance().handleException(e, "getMessageTypeList",
					this.getClass());
		}
	}
	
	public String getTotalCountFromDashboard(String messageStatus, String fromDate, String dateFormat, String currencyCode) throws SwtException {
		try {
			log.debug(this.getClass().getName() + " - [getTotalCountFromDashboard] - Entry/ Exit");
			InputExceptionsMessagesDAO inputExceptionsMessagesDAOPCM = (InputExceptionsMessagesDAO)SwtUtil.getBean("inputExceptionsMessagesDAOPCM");
			return inputExceptionsMessagesDAOPCM.getTotalCountFromDashboard(messageStatus, fromDate, dateFormat, currencyCode);
		} catch (Exception e) {
			log.error(this.getClass().getName() + " - [getTotalCountFromDashboard] - " + e.getMessage());
			throw SwtErrorHandler.getInstance().handleException(e, "getTotalCountFromDashboard",
					this.getClass());
		}
	}

}
