/*
 *
 * Swallow Technology
 * All Rights Reserved.
 * This software and documentation is the confidential and proprietary
 * information of Perot Systems ("Confidential Information").
 * You shall not disclose such Confidential Information and shall use
 * it only in accordance with the terms of the license agreement you
 * entered into with Perot Systems.
 * Unauthorized reproduction or distribution of this Confidential
 * Information, or any portion of it, may result in severe civil
 * and criminal penalties.
 *
 * Developed by Perot Systems .
 * Created on Aug 17, 2006
 *
 */
package org.swallow.work.service.impl;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;

import org.swallow.maintenance.model.CurrencyTO;
import org.swallow.maintenance.model.EntityCurrencyGroupTO;

import org.swallow.util.SwtUtil;
import org.swallow.util.SystemFormats;

import org.swallow.work.dao.SweepNAKQueueDAO;
import org.swallow.work.model.SweepNAKMessage;
import org.swallow.work.model.SweepNAKQueue;
import org.swallow.work.service.SweepNAKQueueManager;

import java.text.SimpleDateFormat;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.Iterator;
import java.util.List;


/**
 *
 * <AUTHOR> Tripathi has rewritten this service class on 01/08/2007.
 *
 */
@Component("sweepNAKQueueManager")
public class SweepNAKQueueManagerImpl implements SweepNAKQueueManager {
    /**
    * To create a Log factory reference variable.
    */
    private final Log log = LogFactory.getLog(SweepNAKQueueManagerImpl.class);

    /**
    * Default DAO object associated with this managerimpl class
    */
    @Autowired
    private SweepNAKQueueDAO sweepNAKQueueDAO = null;

    /**
     * Final type string used to initialize various string object
     */
    private final String EMPTY_STRING = "";

    /**
     * Initializing DAO object to access the DAO services.
     * @param SweepNAKQueueDAO The preAdviceInputDAO to set.
     */
    public void setSweepNAKQueueDAO(SweepNAKQueueDAO sweepNAKQueueDAO) {
        this.sweepNAKQueueDAO = sweepNAKQueueDAO;
    }

    /**
     *
     * @param format SystemFormat Object
     * @param entityId String
     * @param currencyGroup String
     * @param roleId String
     */
    public List getNAKQueueDetails(SystemFormats format, String entityId,
        String currGrp, String roleId) throws SwtException {
        log.debug("Entering SweepNAKQueueManagerImpl.getNAKQueueDetails method");

        Collection currencies = new ArrayList();
        SweepNAKQueue sweepNAKQueue = new SweepNAKQueue();
        Collection NAKDetails = new ArrayList();
        String sweepQueueCount = EMPTY_STRING;
        CurrencyTO currTO = new CurrencyTO(EMPTY_STRING, EMPTY_STRING,
                EMPTY_STRING);

        try {
            Date valueDate = SwtUtil.getSystemDatewithoutTime();

            if ((currGrp != null) && !currGrp.equalsIgnoreCase("All")) {
                currencies = (ArrayList) SwtUtil.getSwtMaintenanceCache()
                                                .getCurrencies(new EntityCurrencyGroupTO(
                            entityId, currGrp));
            } else {
                currencies = (ArrayList) SwtUtil.getSwtMaintenanceCache()
                                                .getCurrencyViewORFullAcess(roleId,
                        entityId);
            }

            List currList = (List) currencies;
            Iterator itr = currList.iterator();

            while (itr.hasNext()) {
                currTO = (CurrencyTO) itr.next();
                sweepNAKQueue = new SweepNAKQueue();

                String currCode = currTO.getCurrencyId();
                sweepNAKQueue.setCurrencyCode(currCode);
                sweepNAKQueue.setCurrencyName(currTO.getCurrencyName());

                if (!(currCode.equals("*"))) {
                    sweepQueueCount = sweepNAKQueueDAO.getNAKQueueDetails(entityId,
                            currCode, valueDate);

                    if ((sweepQueueCount != null) &&
                            !(sweepQueueCount.equalsIgnoreCase("0"))) {
                        sweepNAKQueue.setNAKs(sweepQueueCount);
                    } else {
                        sweepNAKQueue.setNAKs("");
                        sweepNAKQueue.setZeroCount(true);
                    }

                    NAKDetails.add(sweepNAKQueue);
                }
            }
        } catch (Exception exp) {
            log.debug(
                "Exception Catch in SweepNAKQueueManagerImpl.'getNAKQueueDetails' method : " +
                exp.getMessage());
            exp.printStackTrace();
            throw SwtErrorHandler.getInstance().handleException(exp,
                "getNAKQueueDetails", SweepNAKQueueManagerImpl.class);
        }

        return (List) NAKDetails;
    }

    /**
     * This method is used to fetch messages from P_SWEEP_MEESAGES table. Selection of messages depends on the linkSource parameter
     * wheather fetch to NAK messages or overdue ACK messages.
     * @param entityId
     * @param currCode
     * @param format
     * @param linkSource
     * @return collection of sweep messages
     * @throws SwtException
     */
    public List getMessageDetails(String entityId, String currCode,
        SystemFormats format, String linkSource) throws SwtException {
        log.debug("Entering SweepNAKQueueManagerImpl.getMessageDetails method");

        List allNAKMessages = new ArrayList();
        Collection messageColl = new ArrayList();
        SimpleDateFormat sdf = new SimpleDateFormat("HH:mm:ss");
        Object[] rowSweepNAKMessage = new Object[9];
        SweepNAKMessage NAKMessage = null;

        try {
            Date valueDate = SwtUtil.getSystemDatewithoutTime();

            if (linkSource.equalsIgnoreCase("NAK")) {
                messageColl = sweepNAKQueueDAO.getNAKMessages(entityId,
                        currCode, valueDate);
            } else {
                messageColl = sweepNAKQueueDAO.getOverdueACKMessages(entityId,
                        currCode, valueDate);
            }

            Iterator itr = messageColl.iterator();

            while (itr.hasNext()) {
                NAKMessage = new SweepNAKMessage();
                rowSweepNAKMessage = (Object[]) itr.next();
                NAKMessage.setUpdateDateAsString(SwtUtil.formatDate(
                        (Date) rowSweepNAKMessage[0],
                        format.getDateFormatValue()));
                NAKMessage.setUpdateDateAsString((rowSweepNAKMessage[0] != null)
                    ? SwtUtil.formatDate((Date) rowSweepNAKMessage[0],
                        format.getDateFormatValue()) : EMPTY_STRING);
                NAKMessage.setUpdateTime((rowSweepNAKMessage[0] != null)
                    ? sdf.format((Date) rowSweepNAKMessage[0]) : EMPTY_STRING);
                NAKMessage.setMsgFormatId((rowSweepNAKMessage[1] != null)
                    ? rowSweepNAKMessage[1].toString() : null);
                NAKMessage.setSweepId((rowSweepNAKMessage[2] != null)
                    ? rowSweepNAKMessage[2].toString() : null);
                NAKMessage.setValueDateAsString(SwtUtil.formatDate(
                        (Date) rowSweepNAKMessage[3],
                        format.getDateFormatValue()));
                NAKMessage.setCurrcode(rowSweepNAKMessage[4].toString());

                /*
                 * TODO: Check this logic
                 */
                if (rowSweepNAKMessage[7] != null) {
                    NAKMessage.setSweepAmountAsString(SwtUtil.formatCurrency(
                            currCode, (Double) rowSweepNAKMessage[7]));
                } else if (rowSweepNAKMessage[6] != null) {
                    NAKMessage.setSweepAmountAsString(SwtUtil.formatCurrency(
                            currCode, (Double) rowSweepNAKMessage[6]));
                } else {
                    NAKMessage.setSweepAmountAsString(SwtUtil.formatCurrency(
                            currCode, (Double) rowSweepNAKMessage[5]));
                }

                NAKMessage.setMessageId((rowSweepNAKMessage[8] != null)
                    ? rowSweepNAKMessage[8].toString() : EMPTY_STRING);
                NAKMessage.setACKNAKmessageId((rowSweepNAKMessage[9] != null)
                    ? rowSweepNAKMessage[9].toString() : EMPTY_STRING);

                allNAKMessages.add(NAKMessage);
            }

            return allNAKMessages;
        } catch (Exception exp) {
            log.debug(
                "Exception Catch in SweepNAKQueueManagerImpl.'getMessageDetails' method : " +
                exp.getMessage());
            exp.printStackTrace();
            throw SwtErrorHandler.getInstance().handleException(exp,
                "getMessageDetails", SweepNAKQueueManagerImpl.class);
        }
    }

    /**
     * This method is used to call DAO service layer for fetching up the sweep NAK details.
     * @param hostId
     * @param entityId
     * @param currencyGrpId
     * @param roleId
     * @param valueDate
     * @return Collection of sweepNAKQueue objects
     * @throws SwtException
     */
    public Collection getNAKQueueDetails(String hostId, String entityId,
        String currencyGrpId, String roleId, Date valueDate)
        throws SwtException {
        return sweepNAKQueueDAO.getNAKQueueDetailsFromProc(hostId, entityId,
            currencyGrpId, roleId, valueDate);
    }
}
