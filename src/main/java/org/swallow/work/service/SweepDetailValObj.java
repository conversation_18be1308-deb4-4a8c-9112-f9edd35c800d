/*
 * Created on Jan 24, 2006
 *
 * TODO To change the template for this generated file go to
 * Window - Preferences - Java - Code Style - Code Templates
 */
package org.swallow.work.service;

import java.util.Collection;


/**
 * <AUTHOR>
 *
 * TODO To change the template for this generated type comment go to
 * Window - Preferences - Java - Code Style - Code Templates
 */
public class SweepDetailValObj {
	private Collection accountDetail;
	private Collection sweepAmountDetail;
	private int alignIndex;
	
	
	/**
	 * @return Returns the alignIndex.
	 */
	public int getAlignIndex() {
		return alignIndex;
	}
	/**
	 * @param alignIndex The alignIndex to set.
	 */
	public void setAlignIndex(int alignIndex) {
		this.alignIndex = alignIndex;
	}
	
	
	public SweepDetailValObj(){
	}
	public SweepDetailValObj(Collection accountDetail, Collection sweepAmountDetail){
		this.accountDetail = accountDetail;
		this.sweepAmountDetail = sweepAmountDetail;
	}
	
	
	
	/**
	 * @return Returns the accountDetail.
	 */
	public Collection getAccountDetail() {
		return accountDetail;
	}
	/**
	 * @param accountDetail The accountDetail to set.
	 */
	public void setAccountDetail(Collection accountDetail) {
		this.accountDetail = accountDetail;
	}
	/**
	 * @return Returns the sweepAmountDetail.
	 */
	public Collection getSweepAmountDetail() {
		return sweepAmountDetail;
	}
	/**
	 * @param sweepAmountDetail The sweepAmountDetail to set.
	 */
	public void setSweepAmountDetail(Collection sweepAmountDetail) {
		this.sweepAmountDetail = sweepAmountDetail;
	}
}
