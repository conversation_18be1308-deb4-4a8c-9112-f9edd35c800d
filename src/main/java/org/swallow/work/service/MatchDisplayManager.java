/*
 * @(#)MatchDisplayManager.java 1.0 04/01/2006
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.work.service;

import java.util.Date;
import java.util.List;

import org.swallow.exception.SwtException;
import org.swallow.util.SystemFormats;
import org.swallow.work.dao.MatchDisplayDAO;
import org.swallow.work.model.Match;
import org.swallow.work.model.Movement;

/**
 * This class is Manager class for performing all the Match operations such as
 * suspend,match,confiem remove match and also display match details in both
 * current schema and archive schema
 * 
 * 
 * 
 */
public interface MatchDisplayManager {

	/**
	 * This method is used to get the movement details for the given matchId
	 * 
	 * @param entityId
	 * @param hostId
	 * @param matchId
	 * @param formats
	 * @param quality
	 * @return MovementDetailVO
	 * @throws SwtException
	 */
	public MovementDetailVO getMovementDetailList(String entityId,
			String hostId, String matchId, SystemFormats formats, String quality)
			throws SwtException;

	/**
	 * This is mandatory method to initiate DAO instance and to bind it with
	 * this MatchDisplayDAO
	 * 
	 * @param MatchDisplayDAO
	 */
	public void setMatchDisplayDAO(MatchDisplayDAO matchDisplayDAO);

	/**
	 * This method is used to delete the given match record
	 * 
	 * @param String
	 *            entityId
	 * @param String
	 *            hostId
	 * @param String
	 *            matchId
	 * @return void
	 * @throws SwtException
	 */
	public void deleteMatchRecord(String entityId, String hostId, String matchId)
			throws SwtException;

	/**
	 * This method is used to suspend the given match
	 * 
	 * @param String
	 *            entityId
	 * @param String
	 *            hostId
	 * @param String
	 *            matchId
	 * @param String
	 *            userId
	 * @return void
	 * @throws SwtException
	 */
	public void suspendMatchRecord(String entityId, String hostId,
			String matchId, String userId) throws SwtException;

	/**
	 * This method is used to confirm the given match
	 * 
	 * @param String
	 *            entityId
	 * @param String
	 *            hostId
	 * @param String
	 *            matchId
	 * @param String
	 *            userId
	 * @return void
	 * @throws SwtException
	 */
	public void confirmMatchRecord(String entityId, String hostId,
			String matchId, String userId) throws SwtException;

	/**
	 * This method is used to remove the movements
	 * 
	 * @param String
	 *            entityId
	 * @param String
	 *            hostId
	 * @param String
	 *            movementList
	 * @param String
	 *            status
	 * @param Long
	 *            currentMatchId
	 * @return void
	 * @throws SwtException
	 */
	public void removeMovements(String entityId, String hostId,
			String movementList, String status, Long currentMatchId)
			throws SwtException;

	/**
	 * This method is used to add the movements
	 * 
	 * @param String
	 *            entityId
	 * @param String
	 *            movementIds
	 * @param String
	 *            status
	 * @param Long
	 *            matchId
	 * @return void
	 * @throws SwtException
	 */
	public void addMovements(String entityId, String movementIds,
			String status, Long matchId) throws SwtException;

	/**
	 * This method is used to match details
	 * 
	 * @param String
	 *            entityId
	 * @param String
	 *            entityId
	 * @param String
	 *            matchId
	 * @return Match Object
	 * @throws SwtException
	 */
	public Match getMatchDetails(String hostId, String entityId, String matchId)
			throws SwtException;

	/**
	 * This method is used to Get the Match Status from manager class
	 * 
	 * @param hostId
	 * @param entityId
	 * @param matchId
	 * @param movementList
	 * @param totalRows
	 * @return
	 */
	public String getMatchStatus(String hostId, String entityId, String matchId, String archiveId) throws SwtException;

	/**
	 * This method set the status of an existing match to "R" i.e. 'Reconciled'
	 * 
	 * @param entityId
	 * @param hostId
	 * @param matchId
	 * @param userId
	 * @throws SwtException
	 */
	public void reconcileMatchRecord(String entityId, String hostId,
			String matchId, String userId) throws SwtException;

	/**
	 * This is used to get the added movement details
	 * 
	 * @param host_ID
	 * @param entity_ID
	 * @param moevementLongId
	 * @return Movement object
	 */
	public Movement getAddedMovementDetails(String host_ID, String entity_ID,
			Long moevementLongId) throws SwtException;

	/**
	 * Gets the Latest Match Id list from p_match table
	 * 
	 * @param String
	 *            hostId
	 * @param String
	 *            entityId
	 * @param String
	 *            curencyId
	 * @param String
	 *            status
	 * @param String
	 *            quality
	 * @param Date
	 *            date
	 * @param String
	 *            day
	 * @param String
	 *            applyCurrencyThreshold
	 * @param String
	 *            noIncludedMovementMatches
	 * @return List
	 */
	public List<Long> getLatestMatchIdAsList(String hostId, String entityId,
			String currCode, String status, String quality, Date matchDate,
			String day, String applyCurrencyThreshold,
			String noIncludedMovementMatches) throws SwtException;

	
	/**
	 *  Gets the Match Id list from Scenario Query 
	 * @param hostId
	 * @param entityId
	 * @param currencyCode
	 * @param scenarioId
	 * @param applyCurrencyThreshold
	 * @return
	 * @throws SwtException
	 */
	public List<Long> getMatchIdsPerScenarioAsList (String hostId, String  entityId,
			String  currencyCode,String  scenarioId,String  applyCurrencyThreshold,String currencyGroup)throws SwtException;
	

	/**
	 * This method is used to get the account access status for the given match
	 * Id.<br>
	 * 
	 * @param roleId
	 * @param entityId
	 * @param matchId
	 * @throws SwtException
	 * @return Account Access Status
	 */
	public String getAccountAccessStatus(String roleId, String entityId,
			String matchId) throws SwtException;
	/*
	 * Start:Code Modified for Mantis 1270 by Chinniah on 29-Feb-2012:Data
	 * Archive setup: Remove redundant fields from Archive setup screen
	 */
	/**
	 * To get the archived match details from archive schema
	 * 
	 * @param hostId
	 * @param entityId
	 * @param matchId
	 * @param archiveId
	 * @return Match
	 * @throws SwtException
	 */
	public Match getArchiveMatchDetails(String hostId, String entityId,
			String matchId, String archiveId) throws SwtException;

	/**
	 * To get the archived Movement details for selected match id from archive
	 * schema
	 * 
	 * @param entityId
	 * @param hostId
	 * @param matchId
	 * @param formats
	 * @param quality
	 * @param archiveId
	 * @return MovementDetailVO
	 * @throws SwtException
	 */
	public MovementDetailVO getArchiveMovementDetailList(String entityId,
			String hostId, String matchId, SystemFormats formats,
			String quality, String archiveId) throws SwtException;
	/*
	 * End:Code Modified for Mantis 1270 by Chinniah on 29-Feb-2012:Data
	 * Archive setup: Remove redundant fields from Archive setup screen
	 */
}