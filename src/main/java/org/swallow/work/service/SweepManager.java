/*
 * @(#) SweepManager.java 
 * 
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 * 
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.work.service;


import java.util.Collection;

import org.swallow.exception.SwtException;

import org.swallow.util.SystemFormats;
import org.swallow.util.SystemInfo;

import org.swallow.work.dao.SweepDAO;
import org.swallow.work.model.Movement;


public interface SweepManager {
    /**
    * @param entityId
    * @param hostId
    * @param currencyId
    * @param acctType
    * @param sysformat
    * @return
    * @throws SwtException
    * @throws HibernateException
    */
    public SweepDetailVO getSweepDetailList(String entityId, String hostId,
        String currencyId, String acctType, SystemInfo systemInfo,
        SystemFormats sysformat) throws SwtException;

    /**
    * @param sweepDAO
    */
    public void setSweepDAO(SweepDAO sweepDAO);

    /**
     * @param currentEntity
     * @param accountId1
     * @param accountId2
     * @param valueDates
     * @param currentSystemFormats
     * @param entityId1
     * @param entityId2
     * @return
     */
    public String getSweepStatus(String currentEntity, String accountId1,
        String accountId2, String valueDates,
        SystemFormats currentSystemFormats, String entityId1, String entityId2)
        throws SwtException;

    /**
     * @param movement
     * @param selectedTabIndex
     * @param format
     * @return
     */
    public SweepDetailVO getManualSweepDetails(Movement movement,
        String selectedTabIndex, SystemFormats format)
        throws SwtException;
    
    /**
     * this method get list of settelment method from P_SWEEP_SETTLEMENT_METHOD table
     * @param entityId
     * @return
     * @throws SwtException
     */
    public Collection getSweepSettlementMethodList(String entityId) throws SwtException;
}
