/*
 * @(#)ScreenOptionManager.java 1.0 06/08/10
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.work.service;

import java.util.ArrayList;
import java.util.Collection;

import org.swallow.exception.SwtException;
import org.swallow.work.model.FilterMSDConfig;
import org.swallow.work.model.ScreenOption;

/**
 * ScreenOptionManager.java
 * 
 * ScreenOptionManager interface contains only method definition for accessing
 * the dao layer
 * 
 * <AUTHOR> A
 * @date Aug 06, 2010
 */
public interface ScreenOptionManager {

	/**
	 * Method to retrieve the screen options
	 * 
	 * @param screenOption
	 * @throws SwtException
	 *             Collection<ScreenOption>
	 */
	public Collection<ScreenOption> getScreenOption(ScreenOption screenOption)
			throws SwtException;

	/**
	 * Method to save the refresh rate
	 * 
	 * @param screenOption
	 * @throws SwtException
	 */
	public void saveRefreshRate(ScreenOption screenOption) throws SwtException;

	/**
	 * Method to save the currency monitor options
	 * 
	 * @param screenOption
	 * @throws SwtException
	 */
	public void saveCurrencyMonitorOptions(ScreenOption screenOption)
			throws SwtException;

	/**
	 * Method to save the central bank monitor options
	 * 
	 * @param screenOption
	 * @throws SwtException
	 */
	public void saveCentralBankMonitorOptions(ScreenOption screenOption)
			throws SwtException;

	/**
	 * Method to retrieve the refresh rate for a screen
	 * 
	 * @param screenOption
	 * @return ScreenOption
	 * @throws SwtException
	 */
	public ScreenOption getRefreshRate(ScreenOption screenOption)
			throws SwtException;

	/**
	 * Get the currency monitor option details from database
	 * 
	 * @param screenOption
	 * @return ScreenOption
	 * @throws SwtException
	 */
	public ScreenOption getCurrencyMonitorOptions(ScreenOption screenOption)
			throws SwtException;

	/**
	 * Get the central bank monitor option details from database
	 * 
	 * @param screenOption
	 * @return ScreenOption
	 * @throws SwtException
	 */
	public ScreenOption getCentralBankMonitorOptions(ScreenOption screenOption)
			throws SwtException;

	/**
	 * Get the entity monitor option details from database
	 * 
	 * @param screenOption
	 * @return ScreenOption
	 * @throws SwtException
	 */
	public ScreenOption getEntityMonitorOptions(ScreenOption screenOption)
			throws SwtException;

	/**
	 * Method to save the entity monitor options
	 * 
	 * @param screenOption
	 * @throws SwtException
	 */
	public void saveEntityMonitorOptions(ScreenOption screenOption)
			throws SwtException;

	/**
	 * This method is used to get the Font Size for particular screen.<br>
	 * 
	 * @param screenOption
	 * @return
	 * @throws SwtException
	 * <AUTHOR>
	 */
	public ScreenOption getFontSize(ScreenOption screenOption)
			throws SwtException;

	/**
	 * This method is used to save the Font size of the screen per user.<br>
	 * 
	 * @param tempScreenOption
	 * @throws SwtException
	 * <AUTHOR>
	 */
	public void saveFontSize(ScreenOption screenOption) throws SwtException;

	// Start: Code added by Bala for Mantis 1413 on 06-June-2011
	/**
	 * Get the forecast monitor option details from database
	 * 
	 * @param screenOption
	 * @return ScreenOption
	 * @throws SwtException
	 */
	public ScreenOption getForecastMonitorOptions(ScreenOption screenOption)
			throws SwtException;

	/**
	 * Method to save the forecast monitor options
	 * 
	 * @param screenOption
	 * @throws SwtException
	 */
	public void saveForecastMonitorOptions(ScreenOption screenOption)
			throws SwtException;
	// End: Code added by Bala for Mantis 1413 on 06-June-2011
	
	/*
	 * Start: Added for Mantis 1446:"GUI changes in Predict for Smart Input v6"
	 * by Marshal on 23-June-2011
	 */
	/**
	 * Method to retrieve the refresh rate for Interface Monitor screen.<br>
	 * The default Refresh Rate for Interface Monitor is 120 seconds.<br>
	 * 
	 * @param screenOption
	 * @return ScreenOption
	 * @throws SwtException
	 */
	public ScreenOption getInterfaceMonitorRefreshRate(ScreenOption screenOption)
			throws SwtException;
	/*
	 * End: Added for Mantis 1446:"GUI changes in Predict for Smart Input v6" by
	 * Marshal on 23-June-2011
	 */
	
	/**
	 *  Method to save the Liquidity monitor options
	 * @param screenOption
	 * @throws SwtException
	 */
    public void saveLiquidityMonitorOptions(ScreenOption screenOption, boolean isGeneral, String entityId, String currencyId, String profileId )
			throws SwtException;
    /**
     * Get the Liquidity monitor option details from database
     * @param screenOption
     * @return
     * @throws SwtException
     */
    public ScreenOption getLiquidityMonitorOptions(ScreenOption screenOption, boolean isGeneral, String entityId, String currencyId, String profileId)
			throws SwtException;
    
    /**
     * Get the Liquidity monitor profile list from database
     * @param screenOption
     * @return
     * @throws SwtException
     */
    public ArrayList<String> getLiquidityMonitorProfileList(ScreenOption screenOption, String entityId, String currencyId)
    		throws SwtException;
    
    /**
     * Get the Liquidity monitor profile list from database
     * @param screenOption
     * @return
     * @throws SwtException
     */
    public void deleteLiquidityMonitorProfile(ScreenOption screenOption)	throws SwtException;
    
    /**
     * 
     * @param screenOption
     * @param entityId
     * @param currencyId
     * @param inputScreen
     * @return
     * @throws SwtException
     */
	public ArrayList<String> getMSDFilterList(ScreenOption screenOption, String entityId, String currencyId, String inputScreen)
    		throws SwtException;
    
    /**
     * Method to save the MSD filter config
     * 
     * @param screenOption
     * @param entityId
     * @param currencyId
     * @param inputScreen
     * @param filterId
     * @throws SwtException
     */
    public void saveMSDFilterConfig(ScreenOption screenOption, String entityId, String currencyId, String inputScreen, String filterId)
			throws SwtException;
    
    /**
     * Delete the given filter config from database
     * @param screenOption
     * @return
     * @throws SwtException
     */
    public void deleteMSDFilterConfig(ScreenOption screenOption) throws SwtException;
    
    /**
     * Get the MSD filter config for a given saver filter conf
     * @param screenOption
     * @param entityId
     * @param currencyId
     * @param inputScreen
     * @param filterId
     * @return
     * @throws SwtException
     */
    public FilterMSDConfig getMSDFilterConfigValue(ScreenOption screenOption, String entityId, String currencyId, String inputScreen, String filterId)
    		throws SwtException;
     public void  saveILMOptions(ScreenOption screenOption, Boolean isSave) throws SwtException ;
     
     public void  savePreAdviceOptions(ScreenOption screenOption, Boolean isUpdate) throws SwtException ;
     
 	/**
 	 * Method to check the screen Option
 	 * @param hostId
 	 * @param userId
 	 * @param screenId
 	 * @param propertyName
 	 * @throws SwtException
 	 */
 	public boolean checkIfScreenOptionsExists(String hostId, String userId,String screenId, String propertyName)
 			throws SwtException ;
 	
	/**
	 * This method is used to get property value from s_screen_option
	 * @param hostId
	 * @param userId
	 * @param screenId
	 * @param propertyName
	 * @throws SwtException
	 */
	public String getPropertyValue(String hostId, String userId,String screenId, String propertyName) throws SwtException;
	
	/**
	 * Delete the pre Advice input option from database
	 * 
	 * @param screenOption
	 * @return
	 * @throws SwtException
	 */
	public void deletePreAdviceInputOption(ScreenOption screenOption) throws SwtException;
	
    public void  saveWorkflowOptions(ScreenOption screenOption, Boolean isUpdate) throws SwtException ;

}
