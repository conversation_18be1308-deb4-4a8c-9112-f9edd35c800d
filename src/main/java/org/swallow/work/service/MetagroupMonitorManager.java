/*
 * @(#)MetagroupMonitorManager.java 1.0 04/09/06
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.work.service;

import java.util.Collection;

import org.swallow.exception.SwtException;
import org.swallow.util.SystemFormats;

/**
 * <pre>
 * Manager layer for MetaGroupMonitor Screen
 * Class used to
 *  - Display Meta Group Monitor Details
 *  - Display Group Monitor Details
 *  - Display Book Group Monitor Details
 *  - Get BookGroupMonitor JobStatusFlag  
 * </pre>
 */
public interface MetagroupMonitorManager {
	/**
	 * This method returns the predicted balance categorised by meta group code
	 * and the total for a particular currency, entity ID and date
	 * 
	 * @param String
	 *            entity Id
	 * @param String
	 *            currencyCode
	 * @param Date
	 *            date-the date for which the calculations are to be done
	 * @param SystemFormats
	 *            format - the system format for date, currency etc.
	 * @param String
	 *            refreshFlag - flag for checking whether the refresh button has
	 *            been clicked
	 * @param String
	 *            roleId
	 * @param String
	 *            locationId
	 * @return Collection of predicted balances and total
	 * @throws SwtException
	 *             if any
	 */
	public Collection getMetagroupMonitorDetailsNew(String entityId,
			String currencyCode, String date, SystemFormats format,
			String refreshFlag, String roleId, String locationId)
			throws SwtException;

	/**
	 * This method returns the predicted balance categorised by group code and
	 * the total for a particular currency, entity ID and date
	 * 
	 * @param String
	 *            entity Id
	 * @param String
	 *            currencyCode
	 * @param Date
	 *            date-the date for which the calculations are to be done
	 * @param SystemFormats
	 *            format - the system format for date, currency etc.
	 * @param String
	 *            refreshFlag - flag for checking whether the refresh button has
	 *            been clicked
	 * @param String
	 *            roleId
	 * @param String
	 *            metagroupId
	 * @return Collection of predicted balances and total
	 * @throws SwtException
	 *             if any
	 */
	public Collection getGroupMonitorDetailsNew(String entityId,
			String currencyCode, String date, SystemFormats format,
			String refreshFlag, String roleId, String locationId,
			String metagroupId) throws SwtException;

	/**
	 * This method returns the predicted balance categorised by book code and
	 * the total for a particular currency, entity ID and date
	 * 
	 * @param String
	 *            entity Id
	 * @param String
	 *            currencyCode
	 * @param Date
	 *            date-the date for which the calculations are to be done
	 * @param SystemFormats
	 *            format - the system format for date, currency etc.
	 * @param String
	 *            refreshFlag - flag for checking whether the refresh button has
	 *            been clicked
	 * @param String
	 *            roleId
	 * @param String
	 *            locationId
	 * @param String
	 *            groupCode
	 * @return Collection of predicted balances and total
	 * @throws SwtException
	 *             if any
	 */
	public Collection getBookMonitorDetailsNew(String entityId,
			String currencyCode, String date, SystemFormats format,
			String refreshFlag, String roleId, String locationId,
			String groupCode) throws SwtException;

	/* Code: Modified by Arumugam for Mantis 1445 on 13-07-2011 */
	/**
	 * This method is used to return Job Status Flag.
	 * 
	 * @param entityId
	 * @return boolean
	 * @throws SwtException
	 */
	public boolean getBookGroupMonitorJobFlag(String entityId)
			throws SwtException;

}
