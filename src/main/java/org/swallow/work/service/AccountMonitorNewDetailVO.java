/*
 * @(#)AccountMonitorForm 1.0 09/09/06
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.work.service;

import java.util.ArrayList;
import java.util.Collection;

import org.swallow.work.model.AccountMonitorNew;
import org.swallow.work.model.AccountMonitorTotalCacheValue;

/**
 * This is the Value object class, object of which is used to contain both
 * summary details that are displayed in the table, and total details.
 * 
 * <AUTHOR> Systems
 * @version 1.0 09 September 2006
 */
public class AccountMonitorNewDetailVO {

	// Collection for holding summary details;
	Collection<AccountMonitorNew> summaryDetails = null;
	// AccountMonitorTotalCacheValue object to hold total details
	AccountMonitorTotalCacheValue totalDetails = new AccountMonitorTotalCacheValue();
	// Flag denontes, whether the job is running or not
	private String jobFlag = null;
	// Weekends
	private ArrayList<String> weekends = new ArrayList<String>();
	// Tab holiday flag (N - Working Day, Y - Holiday)
	private String tabFlag = null;

	/**
	 * @return Returns the TabFlag.
	 */
	public String getTabFlag() {
		return tabFlag;
	}

	/**
	 * @param TabFlag
	 *            The TabFlag to set.
	 */
	public void setTabFlag(String tabFlag) {
		this.tabFlag = tabFlag;
	}

	/**
	 * @return Returns the summaryDetails.
	 */
	public Collection<AccountMonitorNew> getSummaryDetails() {
		return summaryDetails;
	}

	/**
	 * @return Returns the getWeekends.
	 */
	public ArrayList<String> getWeekends() {
		return weekends;
	}

	/**
	 * @param setWeekends
	 *            The setWeekends to set.
	 */
	public void setWeekends(ArrayList<String> weekends) {
		this.weekends = weekends;
	}

	/**
	 * @param summaryDetails
	 *            The summaryDetails to set.
	 */
	public void setSummaryDetails(Collection<AccountMonitorNew> summaryDetails) {
		this.summaryDetails = summaryDetails;
	}

	/**
	 * @return Returns the totalDetails.
	 */
	public AccountMonitorTotalCacheValue getTotalDetails() {
		return totalDetails;
	}

	/**
	 * @param totalDetails
	 *            The totalDetails to set.
	 */
	public void setTotalDetails(AccountMonitorTotalCacheValue totalDetails) {
		this.totalDetails = totalDetails;
	}

	/**
	 * @return Returns the jobFlag.
	 */
	public String getJobFlag() {
		return jobFlag;
	}

	/**
	 * @param jobFlag
	 *            The jobFlag to set.
	 */
	public void setJobFlag(String jobFlag) {
		this.jobFlag = jobFlag;
	}
}
