/*/*
 *@SweepDetailvo.java
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */

package org.swallow.work.service;

import java.util.Collection;

/**
 * This pojo class for the manual sweeping screen.This class is mapped to
 * PK_MONITORS.SP_MANUAL_SWEEP_MONITOR to get the manual sweeping details from
 * the database.
 */
public class SweepDetailVO {
	// Variable declared to hold sweep details for the selected date
	private Collection sweepDetailsToday = null;
	//START:Added by Mefteh Bouazizi for Mantis 2115: Tab holiday flag (N - Working Day, Y - Holiday)
	private String tabFlag = null;
	/**
	 * @return Returns the TabFlag.
	 */
	public String getTabFlag() {
		return tabFlag;
	}

	/**
	 * @param TabFlag
	 *            The TabFlag to set.
	 */
	public void setTabFlag(String tabFlag) {
		this.tabFlag = tabFlag;
	}
	//END:Added by <PERSON><PERSON>eh Bouazizi for Mantis 2115: Tab holiday flag (N - Working Day, Y - Holiday)
	// default constructor of SweepDetailVO
	public SweepDetailVO() {
	}

	/**
	 * Parameterized constructor to provide an alias component
	 */
	public SweepDetailVO(Collection sweepDetailsToday) {

		this.sweepDetailsToday = sweepDetailsToday;
	}

	/**
	 * Getter method for SweepDetailsToday
	 */
	public Collection getSweepDetailsToday() {
		// return the sweep details of the selected date
		return sweepDetailsToday;
	}

	/**
	 * Setter method for SweepDetailsToday
	 */
	public void setSweepDetailsToday(Collection sweepDetailsToday) {
		this.sweepDetailsToday = sweepDetailsToday;
	}

}
