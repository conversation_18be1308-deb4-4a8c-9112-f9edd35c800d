/*
 * @(#) SweepQueueManager.java 10/01/06
 * 
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 * 
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */

package org.swallow.work.service;

//import org.apache.struts.action.ActionMessages;
import org.swallow.exception.SwtException;
import org.swallow.util.SystemFormats;
import org.swallow.util.struts.ActionMessages;
import org.swallow.work.model.Sweep;
import org.swallow.work.model.SweepQueueDetailVO;


public interface SweepQueueManager {
	/* Start : Code modified for Mantis 1179 by ASB on 09-Jun-2010 */
	/**
	 * Method to fetch the sweep details for Authorise, Submit and Cancel queues
	 * 
	 * @param sweep
	 * @param roleId
	 * @param currencyGrpAccess
	 * @param sweepQueueDetailVO
	 * @param systemFormats
	 * @throws SwtException
	 */
	int getSweepQueueDetail(Sweep sweep, String roleId, int currencyGrpAccess,
			SweepQueueDetailVO sweepQueueDetailVO, int currentPage,
			int initialPageCount, String filterSortStatus,
			SystemFormats systemFormats) throws SwtException;

	/* End : Code modified for Mantis 1179 by ASB on 09-Jun-2010 */

	// submit() changed to pass the parameter bypassChangedSweep which
	// identifies if changed sweeps to be submitted without review
	public ActionMessages submit(Sweep sweep, String selectedList,
			SystemFormats systemFormats, String bypassChangedSweep,
			String bypassCutOff, String bypassAccountBreach)
			throws SwtException;

	public ActionMessages cancel(Sweep sweep, String selectedList,
			SystemFormats systemFormats) throws SwtException;
}
