/*
 * @(#)SweepPriorCutOffAction.java 1.0 16/05/2011
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.work.web;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;





import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.maintenance.model.AcctMaintenance;
import org.swallow.maintenance.model.Entity;
import org.swallow.maintenance.service.EntityManager;
import org.swallow.util.CacheManager;
import org.swallow.util.SwtConstants;
import org.swallow.util.SwtUtil;
import org.swallow.util.SystemFormats;
import org.swallow.util.UserThreadLocalHolder;
import org.swallow.work.model.ScreenOption;
import org.swallow.work.model.SweepPriorCutOff;
import org.swallow.work.service.ScreenOptionManager;
import org.swallow.work.service.SweepManager;
import org.swallow.work.service.SweepPriorCutOffManager;
import org.swallow.util.LabelValueBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.apache.struts2.convention.annotation.Result;
import org.apache.struts2.convention.annotation.AllowedMethods;
import org.apache.struts2.convention.annotation.Action;
import org.apache.struts2.ServletActionContext;
import org.swallow.util.struts.ActionErrors;
import org.swallow.util.struts.CustomActionSupport;

/**
 * SweepPriorCutOffAction.java
 * 
 * This class is used to maintain the Sweep Prior Cutoff details.
 */
@Action(value = "/sweeppriorcutoff", results = {
	@Result(name = "success", location = "/jsp/work/sweeppriorcutoff.jsp"),
	@Result(name = "fail", location = "/error.jsp"),
	@Result(name = "sweepdetail", location = "/jsp/work/sweepdetail.jsp"),
})

@AllowedMethods ({"displaySweepDetails" ,"checkAccess" ,"checkSweep" })
public class SweepPriorCutOffAction extends CustomActionSupport {
public String execute() throws Exception {
    HttpServletRequest request = ServletActionContext.getRequest();

    // List of methods 
    String method = String.valueOf(request.getParameter("method"));

    switch (method) {
        case "unspecified":
            return unspecified();
        case "displaySweepDetails":
            return displaySweepDetails();
        case "checkAccess":
            return checkAccess();
        case "checkSweep":
            return checkSweep();
        default:
            break;
    }

    return unspecified();
}


private SweepPriorCutOff sweeppriorcutoff;
public SweepPriorCutOff getSweeppriorcutoff() {
	if (sweeppriorcutoff == null) {
		sweeppriorcutoff = new SweepPriorCutOff();
	}
	return sweeppriorcutoff;
}
public void setSweeppriorcutoff(SweepPriorCutOff sweeppriorcutoff) {
	this.sweeppriorcutoff = sweeppriorcutoff;
	HttpServletRequest request = ServletActionContext.getRequest();
	request.setAttribute("sweeppriorcutoff", sweeppriorcutoff);
}
private Date entityDate;
public Date getEntityDate() {
	if (entityDate == null) {
		entityDate = new Date();
	}
	return entityDate;
}
public void setEntityDate(Date entityDate) {
	this.entityDate = entityDate;
	HttpServletRequest request = ServletActionContext.getRequest();
	request.setAttribute("entityDate", entityDate);
}
private String offSet;
public String getOffSet() {
	if (offSet == null) {
		offSet = new String();
	}
	return offSet;
}
public void setOffSet(String offSet) {
	this.offSet = offSet;
	HttpServletRequest request = ServletActionContext.getRequest();
	request.setAttribute("offSet", offSet);
}

	@Autowired
	SweepPriorCutOffManager sweepPriorCutOffManager = null;
	private final Log log = LogFactory.getLog(SweepPriorCutOffAction.class);

	public void setSweepPriorCutOffManager(
			SweepPriorCutOffManager sweepPriorCutOffManager) {
		this.sweepPriorCutOffManager = sweepPriorCutOffManager;
	}

	/**
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return a Result of type Action forward
	 */
	public String unspecified()
			throws Exception {
		log.debug("exiting 'unspecified' method");
		return displaySweepDetails();
	}

	/**
	 * This method is used to Sweep Prior CutOff details.
	 * 
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return
	 * @throws Exception
	 */
	public String displaySweepDetails() throws Exception {
		/* Local variable declaration and initialization */
		// Variable to hold defaultRunTime
		String hostId = null;
		// Variable to hold host Id
		String entityId = null;
		// Screen option instance
		ScreenOption screenOption = null;
		// Screen option instance
		ScreenOption scrOption = null;
		// Variable to hold date to display
		String datetodisplay = null;
		/* Start:added by Arumugam for Mantis 1445 on 31-05-2011 */
		// Variable to hold entity date
		Date entityDate = null;
		// Variable to hold offset
		String offset = null;
		// Variable to hold simple date format
		SimpleDateFormat sdf = null;
		// hash map to hold the entity date and offset
		HashMap<String, Object> entMap = null;
		/* End:added by Arumugam for Mantis 1445 on 31-05-2011 */
		// Dyna Validator form
		// DynaValidatorForm dyForm = null;
		// object to hold the sweep prior cutoff bean
		SweepPriorCutOff swpPriorCutOff = null;
		// object to hold the sweep prior cutoff bean
		SweepPriorCutOff sweepPriorCutOff = null;
		// object to hold the system format date
		SystemFormats format = null;
		// variable to hold sub account
		String subAccount = null;
		// variable to hold the extend display time
		int extendDisplayTimeBy;
		// variable to hold the cut off lead time
		int cutOffLeadTime;
		// Object to hold the entity manager
		EntityManager mgr = null;
		// Collection to hold the currency details
		Collection coll = null;
		// Iterator to hold the currency details
		Iterator itr = null;
		// Collection to hold the sweep prior cutoff details
		Collection sweepPriorCutOffList = null;
		// Variable to hold the user id
		String userId = null;
		// Variable to hold the screen id
		String screenId = null;
		// Object to hold the screen option manager
		ScreenOptionManager screenOptionManager = null;
		// Variable to hold the filter Status
		String filterStatus = null;
		// Variable to hold the sort Status
		String sortStatus = null;
		// Variable to hold the sort Descending
		String sortDescending = null;
		HttpServletRequest request = ServletActionContext.getRequest();
		HttpServletResponse response = ServletActionContext.getResponse();
		try {
			log.debug(this.getClass().getName()
					+ " - [displaySweepDetails()] - " + "Entry");
			// To collect the entity id's
			putEntityListInReq(request);
			// object to read the form value and set the values
			// To get form object for dyna validator
			swpPriorCutOff = (SweepPriorCutOff) getSweeppriorcutoff();
			// To create the object for sweep prior cutoff
			sweepPriorCutOff = new SweepPriorCutOff();
			// To create the object for system format date
			format = SwtUtil.getCurrentSystemFormats(request.getSession());
			// To get the host Id for cache manager
			hostId = CacheManager.getInstance().getHostId();
			// To get the entity Id for bean object
			entityId = swpPriorCutOff.getId().getEntityId();
			// To check the entity id value if null or not
			if (SwtUtil.isEmptyOrNull(entityId)) {
				sweepPriorCutOff.getId().setEntityId(
						SwtUtil.getUserCurrentEntity(request.getSession()));
				// To set the entity Id from bean object
				entityId = sweepPriorCutOff.getId().getEntityId();
			} else {
				sweepPriorCutOff.getId().setEntityId(entityId);
			}
			/* Start:added by Arumugam for Mantis 1445 on 31-05-2011 */
			// To set the simple date format
			sdf = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss");
			// To get the entity date and offset from swtutil.getEntityDate
			// method
			entMap = SwtUtil.getOffsetDateTime(SwtUtil.getSystemDatewithTime(),
					entityId);
			// Retrieve the entity date from entmap object
			entityDate = (Date) entMap.get("entityDate");
			// Retrieve the offset value from entmap object
			offset = (String) entMap.get("offSet");
			// Retrieve the date from entity date
			datetodisplay = sdf.format(entityDate.getTime());
			request.setAttribute("hrs", datetodisplay.substring(11, 13));
			request.setAttribute("min", datetodisplay.substring(14, 16));
			request.setAttribute("sec", datetodisplay.substring(17, 19));
			request.setAttribute("offSet", offset);
			/* End:added by Arumugam for Mantis 1445 on 31-05-2011 */
			// To set the sub account value
			subAccount = "Sub Nostro";
			// To set the sub account value for bean object
			sweepPriorCutOff.setAcctType(subAccount);
			// To check the extend display time
			if (swpPriorCutOff.getExtendDisplayTime() != null) {
				// To set the extend display time
				sweepPriorCutOff.setExtendDisplayTime(swpPriorCutOff
						.getExtendDisplayTime());
			}
			// To init the extend display time value
			extendDisplayTimeBy = 0;
			// To check the extend display time value
			if (swpPriorCutOff.getExtendDisplayTimeBy() != 0) {
				// To set the extend display time value
				extendDisplayTimeBy = swpPriorCutOff.getExtendDisplayTimeBy();
				sweepPriorCutOff.setExtendDisplayTimeBy(extendDisplayTimeBy);
			}
			// To init the cutoff lead time value
			cutOffLeadTime = 0;
			// To get the entity manager object
			mgr = (EntityManager) (SwtUtil.getBean("entityManager"));
			// To get the currency details for entity manager
			coll = mgr.getCurrencyDetail(hostId, entityId);
			// To fetch the currency details
			itr = coll.iterator();
			while (itr.hasNext()) {
				// Object to hold the entity bean
				Entity ent = (Entity) (itr.next());
				// To get the cutoff lead time
				cutOffLeadTime = ent.getSweepCutoffLeadTime().intValue();
				// To set the cutoff lead time
				sweepPriorCutOff.setLeadTime(cutOffLeadTime);

			}
			// To get the sweep prior cutoff lead time
			sweepPriorCutOffList = sweepPriorCutOffManager
					.getSweepPriorToCutOffListUsingStoredProc(hostId, entityId,
							"N", cutOffLeadTime, extendDisplayTimeBy, format);
			// To get the user id value for swtutil class
			userId = SwtUtil.getCurrentUserId(request.getSession());
			screenId = SwtConstants.SWEEP_PRIOR_CUT_OFF_ID;
			// Initialising the screen option instance
			scrOption = new ScreenOption();
			// Setting the hostId
			scrOption.getId().setHostId(hostId);
			// Setting the userId
			scrOption.getId().setUserId(userId);
			// Setting the screenId
			scrOption.getId().setScreenId(screenId);
			// Creating a ScreenOptionManager instance
			screenOptionManager = (ScreenOptionManager) (SwtUtil
					.getBean("screenOptionManager"));
			screenOption = screenOptionManager.getRefreshRate(scrOption);
			/*sets the autoRefreshRate in request object*/
			request.setAttribute("autoRefreshRate", new Integer(screenOption
					.getPropertyValue()));
			/*sets the scrolltableLeft in request object*/
			request.setAttribute("scrollLeft", request
					.getParameter("scrollLeft"));
			/*sets the scrollTop in request object*/
			request
					.setAttribute("scrollTop", request
							.getParameter("scrollTop"));
			/*sets the scrolltableLeft in request object*/
			request.setAttribute("scrollTableLeft", request
					.getParameter("scrollTableLeft"));
			/*sets the scrolltableTop in request object*/
			request.setAttribute("scrollTableTop", request
					.getParameter("scrollTableTop"));
			/* Sets the sweepPriorCutOffList in request object */
			request.setAttribute("sweepPriorCutOffList", sweepPriorCutOffList);
			request.setAttribute("lastRefTime", SwtUtil.getLastRefTimeOnGMTOffset(request,
					entityId));
			/* Getting the selectedFilterStatus from request */
			filterStatus = request.getParameter("selectedFilterStatus");
			/* Sets the Filter Status in request object */
			request.setAttribute("filterStatus", filterStatus);
			/* Getting the selectedFilterStatus from request */
			sortStatus = request.getParameter("selectedSortStatus");
			/* Sets the Filter Status in request object */
			request.setAttribute("sortStatus", sortStatus);
			/* Getting the selectedFilterStatus from request */
			sortDescending = request.getParameter("selectedSortDescending");
			/* Sets the Filter Status in request object */
			request.setAttribute("sortDescending", sortDescending);
			setSweeppriorcutoff(sweepPriorCutOff);

			SwtUtil.getMenuEntityCurrGrpAccess(request, entityId, null);
			log.debug(this.getClass().getName()
					+ " - [displaySweepDetails()] - " + "Exit");
			return ("success");
		} catch (SwtException swtexp) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [displaySweepDetails] method. ");
			SwtUtil.logException(swtexp, request, "");

			return ("fail");
		} catch (Exception e) {
			log
					.error(this.getClass().getName()
							+ " - Exception Catched in [displaySweepDetails] method : - "
							+ e.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					e, "displaySweepDetails", SweepPriorCutOffAction.class),
					request, "");

			return ("fail");
		} finally {
			/* To set null value for local variable */
			hostId = null;
			entityId = null;
			screenOption = null;
			scrOption = null;
			swpPriorCutOff = null;
			format = null;
			subAccount = null;
			mgr = null;
			coll = null;
			itr = null;
			userId = null;
			screenId = null;
			screenOptionManager = null;
		}

	}

	private void putEntityListInReq(HttpServletRequest request)
			throws SwtException {
		log.debug("Inside LocationMaintenanceAction.putEntityListInReq method");

		HttpSession session = request.getSession();
		Collection entities = new ArrayList();
		entities = SwtUtil.getUserEntityAccessList(session);
		entities = SwtUtil.convertEntityAcessCollectionLVL(entities, session);
		request.setAttribute("entities", entities);

		log.debug("Exit LocationMaintenanceAction.putEntityListInReq method");
	}

	public String checkAccess()
			throws SwtException {
HttpServletRequest request = ServletActionContext.getRequest();
HttpServletResponse response = ServletActionContext.getResponse();

		ActionErrors errors = new ActionErrors();

		try {
			log.debug("entering 'checkSweep' method");
			String hostId = CacheManager.getInstance().getHostId();

			String strselectedList = request.getParameter("selectedList");

			String replacedstrselectedList = strselectedList
					.replaceAll("'", "");
			String valuedate1 = replacedstrselectedList.substring(0,
					(replacedstrselectedList.indexOf(",") - 1));

			String tempaccountiD1 = replacedstrselectedList.substring(
					(replacedstrselectedList.indexOf(",") + 1),
					(replacedstrselectedList.indexOf("|")));

			String accountId1 = tempaccountiD1.substring(0, (tempaccountiD1
					.indexOf(",") - 1));

			String tempcurrEntiy = tempaccountiD1.substring((tempaccountiD1
					.indexOf(",") + 1), (tempaccountiD1.length()));

			String entityId1 = tempcurrEntiy.substring(0, (tempcurrEntiy
					.indexOf(",") - 1));

			String tempCurrCode1 = tempcurrEntiy.substring((tempcurrEntiy
					.indexOf(",") + 1), tempcurrEntiy.length());

			String currCode1 = tempCurrCode1.substring(0, (tempCurrCode1
					.indexOf(",") - 1));

			String mainAccountId = tempCurrCode1.substring((tempCurrCode1
					.lastIndexOf(",") + 1), tempCurrCode1.length());

			Collection mainAccountIdColl = sweepPriorCutOffManager
					.getMainAccountDetails(hostId, entityId1, mainAccountId);

			String mainCurrCode = "";
			Iterator itr = mainAccountIdColl.iterator();
			while (itr.hasNext()) {
				AcctMaintenance acct = (AcctMaintenance) itr.next();
				mainCurrCode = acct.getCurrcode();
			}

			int acc1 = SwtUtil.getMenuEntityCurrGrpAccess(request, entityId1,
					null);
			int acc2 = SwtUtil.getMenuEntityCurrGrpAccess(request, entityId1,
					null);

			if ((acc1 == SwtConstants.ENTITY_FULL_ACCESS)
					&& (acc2 == SwtConstants.ENTITY_FULL_ACCESS)) {
				response.getWriter().print(true);
			} else {
				response.getWriter().print(false);
			}

			return null;
		} catch (SwtException swtexp) {
			log
					.error("SwtException Catch in SweepAction.'checkAccess' method : "
							+ swtexp.getMessage());
			SwtUtil.logException(swtexp, request, "");

			return ("fail");
		} catch (Exception exp) {
			log.error("Exception Catch in SweepAction.'checkAccess' method : "
					+ exp.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "checkAccess", SweepAction.class), request, "");

			return ("fail");
		}
	}

	public String checkSweep()
			throws SwtException {
HttpServletRequest request = ServletActionContext.getRequest();
HttpServletResponse response = ServletActionContext.getResponse();

		ActionErrors errors = new ActionErrors();

		try {
			log.debug("entering 'checkSweep' method");
			String hostId = CacheManager.getInstance().getHostId();

			String strselectedList = request.getParameter("selectedList");

			String replacedstrselectedList = strselectedList
					.replaceAll("'", "");
			String valuedate1 = replacedstrselectedList.substring(0,
					(replacedstrselectedList.indexOf(",") - 1));

			String tempaccountiD1 = replacedstrselectedList.substring(
					(replacedstrselectedList.indexOf(",") + 1),
					(replacedstrselectedList.indexOf("|")));

			String accountId1 = tempaccountiD1.substring(0, (tempaccountiD1
					.indexOf(",") - 1));

			String tempcurrEntiy = tempaccountiD1.substring((tempaccountiD1
					.indexOf(",") + 1), (tempaccountiD1.length()));

			String entityId1 = tempcurrEntiy.substring(0, (tempcurrEntiy
					.indexOf(",") - 1));

			String tempCurrCode1 = tempcurrEntiy.substring((tempcurrEntiy
					.indexOf(",") + 1), tempcurrEntiy.length());

			String currCode1 = tempCurrCode1.substring(0, (tempCurrCode1
					.indexOf(",") - 1));

			String mainAccountId = tempCurrCode1.substring((tempCurrCode1
					.lastIndexOf(",") + 1), tempCurrCode1.length());

			Collection mainAccountIdColl = sweepPriorCutOffManager
					.getMainAccountDetails(hostId, entityId1, mainAccountId);

			String mainCurrCode = "";
			Iterator itr = mainAccountIdColl.iterator();
			while (itr.hasNext()) {
				AcctMaintenance acct = (AcctMaintenance) itr.next();
				mainCurrCode = acct.getCurrcode();
			}

			String currentEntity = SwtUtil
					.getUserCurrentEntity(UserThreadLocalHolder
							.getUserSession());

			SweepManager mgr = (SweepManager) (SwtUtil.getBean("sweepManager"));

			String accountList = mgr.getSweepStatus(currentEntity, accountId1,
					mainAccountId, valuedate1, SwtUtil
							.getCurrentSystemFormats(request.getSession()),
					entityId1, entityId1);

			if (!"".equals(accountList)) {
				response.getWriter().print(accountList);
			} else {
				response.getWriter().print("false");
			}

			return null;
		} catch (SwtException swtexp) {
			log
					.error("SwtException Catch in SweepAction.'checkSweep' method : "
							+ swtexp.getMessage());

			saveErrors(request, SwtUtil.logException(swtexp, request, ""));

			return ("success");
		} catch (Exception exp) {
			log.error("Exception Catch in SweepAction.'checkSweep' method : "
					+ exp.getMessage());

			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "checkSweep", SweepAction.class), request, "");

			return ("fail");
		}
	}

}
