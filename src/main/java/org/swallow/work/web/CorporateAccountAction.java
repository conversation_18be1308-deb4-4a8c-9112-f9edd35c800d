/*
 * @(#)CorporateAccountAction.java 1.0 13/07/10
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.work.web;

import java.math.BigDecimal;
import java.util.List;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;


import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.util.SwtUtil;
import org.swallow.util.SystemFormats;
import org.swallow.work.model.CorporateAccount;
import org.swallow.work.service.CorporateAccountManager;
import org.springframework.beans.factory.annotation.Autowired;
import org.apache.struts2.convention.annotation.Result;
import org.apache.struts2.convention.annotation.AllowedMethods;
import org.apache.struts2.convention.annotation.Action;
import org.apache.struts2.ServletActionContext;
import org.swallow.util.struts.CustomActionSupport;

/**
 * CorporateAccountAction.java
 * 
 * CorporateAccountAction class is used for CorporateAccount screen
 * 
 * <AUTHOR> G 
 * @date July 13, 2010
 */
@Action(value = "/corporateAccount", results = {
	@Result(name = "success", location = "/jsp/work/corporateaccountflexdata.jsp"),
	@Result(name = "flexobject", location = "/jsp/work/corporateaccountflex.jsp"),
	@Result(name = "fail", location = "/error.jsp"),
	@Result(name = "dataerror", location = "/jsp/work/corporateaccountflexdataerror.jsp"),
})

@AllowedMethods ({"flexCorporateAccount" ,"display" ,"saveCorporateAccountDetails" ,"deleteCorporateAccountDetails" })
public class CorporateAccountAction extends CustomActionSupport {
public String execute() throws Exception {
    HttpServletRequest request = ServletActionContext.getRequest();

    // List of methods 
    String method = String.valueOf(request.getParameter("method"));

    switch (method) {
        case "flexCorporateAccount":
            return flexCorporateAccount();
        case "display":
            return display();
        case "saveCorporateAccountDetails":
            return saveCorporateAccountDetails();
        case "deleteCorporateAccountDetails":
            return deleteCorporateAccountDetails();
        default:
            break;
    }

    return null;
}



	
	/**
	 * Final log instance for logging this class
	 */
	private final Log log = LogFactory.getLog(CorporateAccountAction.class);
	
	/**
	 * An instance of CorporateAccountManager to be used across the
	 * application
	 */
	@Autowired
private CorporateAccountManager corporateAccountManager = null;
	
	
	/**
	 * @param corporateAccountManager the corporateAccountManager to set
	 */
	public void setCorporateAccountManager(
			CorporateAccountManager corporateAccountManager) {
		this.corporateAccountManager = corporateAccountManager;
	}
	
	/**
	 * Loads the flex object into the client
	 * 
	 * @param ActionMapping
	 *            mapping
	 * @param ActionForm
	 *            form
	 * @param HttpServletRequest
	 *            request
	 * @param HttpServletResponse
	 *            response
	 * @throws SwtException
	 */
	public String flexCorporateAccount()
			throws SwtException {
HttpServletRequest request = ServletActionContext.getRequest();
HttpServletResponse response = ServletActionContext.getResponse();

		log.debug(this.getClass().getName() + " - [flex] - Entering ");
		
		// Get the selected column date
		String selectedDate = request.getParameter("selectedDate");
		String selectedEntityId = request.getParameter("selectedEntityId");
		String selectedEntityName = request.getParameter("selectedEntityName");
		// set the selected date,selectedEntityId and selectedEntityName
		request.setAttribute("selectedDate",selectedDate);
		request.setAttribute("selectedEntityId",selectedEntityId);
		request.setAttribute("selectedEntityName",selectedEntityName);
		
		log.debug(this.getClass().getName() + " - [flex] - Exit ");
		return ("flexobject");
	}
	
	/**
	 * Method is used to display the Corporate Account Screen details
	 * 
	 * @param mapping
	 *            ActionMapping
	 * @param form
	 *            ActionForm
	 * @param request
	 *            HttpServletRequest
	 * @param response
	 *            HttpServletResponse
	 * @return a Result of type Action forward
	 */
	public String display()throws SwtException {
HttpServletRequest request = ServletActionContext.getRequest();
HttpServletResponse response = ServletActionContext.getResponse();

		    // value date
			String valueDate = null;
			// EntityId, EntityName
			String selectedEntityId = null;
			String selectedEntityName = null;
			// Corporate Account
			CorporateAccount corpAcct = null;
			// List of Corporate Account details
			List<CorporateAccount> lstCorpAcct = null;
		    SystemFormats format = null;
		try{
			// Get the value date,selectedEntityId and selectedEntityName
			valueDate = request.getParameter("selectedDate");
			selectedEntityId = request.getParameter("selectedEntityId");
			selectedEntityName = request.getParameter("selectedEntityName");
			// Instantiate the Corporate Account
			corpAcct = new CorporateAccount();
			// Get the currentFormat 
			format = SwtUtil.getCurrentSystemFormats(request.getSession());
			corpAcct.setEntityId(selectedEntityId);
			// Set the value date
			corpAcct.setValueDate(SwtUtil.parseDate(valueDate, format.getDateFormatValue()));
			// Calls the manager class to pass the corporate account details
			lstCorpAcct = corporateAccountManager.getCorporateAccount(corpAcct);
			// Sets the corporate account details,selectedEntityId,selectedEntityName and value date in request
			request.setAttribute("corporateAccountDetails", lstCorpAcct);
			request.setAttribute("recordCount", lstCorpAcct.size());
			request.setAttribute("selectedEntityId", selectedEntityId);
			request.setAttribute("selectedEntityName", selectedEntityName);
			request.setAttribute("valueDate", valueDate);
			// set the reply status
			request.setAttribute("reply_status_ok", "true");
			// set the reply message
			request.setAttribute("reply_message", "Data fetch ok");
			return ("success");
		}catch (SwtException e) {
				String message = e.getStackTrace()[0].getClassName() + "."
				+ e.getStackTrace()[0].getMethodName() + ":"
				+ e.getStackTrace()[0].getLineNumber() + " "
				+ e.getMessage();
				log.error(this.getClass().getName()
					+ " - [display] - Exception -"
					+ message);
			saveErrors(request, SwtUtil.logException(e, request, ""));
			request.setAttribute("reply_status_ok", "false");
			request.setAttribute("reply_message", e.getMessage());
			request.setAttribute("reply_location", e.getStackTrace()[0]
					.getClassName()
					+ "."
					+ e.getStackTrace()[0].getMethodName()
					+ ":"
					+ e.getStackTrace()[0].getLineNumber());
			return ("dataerror");
		} catch (Exception e) {
			String message = e.getStackTrace()[0].getClassName() + "."
					+ e.getStackTrace()[0].getMethodName() + ":"
					+ e.getStackTrace()[0].getLineNumber() + " "
					+ e.getMessage();
			log.error(this.getClass().getName()
					+ " - [display] - Exception -"
					+ message);
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					e, "display",
					CentralBankMonitorAction.class), request, "");
			request.setAttribute("reply_status_ok", "false");
			request.setAttribute("reply_message", e.getMessage());
		
			request.setAttribute("reply_location", e.getStackTrace()[0]
					.getClassName()
					+ "."
					+ e.getStackTrace()[0].getMethodName()
					+ ":"
					+ e.getStackTrace()[0].getLineNumber());
			return ("dataerror");
		}
	}
	
	/**
	 * Method is used to save the Corporate Account Details
	 * 
	 * @param mapping
	 *            ActionMapping
	 * @param form
	 *            ActionForm
	 * @param request
	 *            HttpServletRequest
	 * @param response
	 *            HttpServletResponse
	 * @return a Result of type Action forward
	 */
	public String saveCorporateAccountDetails()throws SwtException {
HttpServletRequest request = ServletActionContext.getRequest();
HttpServletResponse response = ServletActionContext.getResponse();

		// Value date
		String valueDate = null;
		// Corporate Name
		String corpName = null;
		// Amount
		String amount = null;
		// EntityId and EntityName
		String selectedEntityId = null;
		String selectedEntityName = null;
		// Corporate Account
		CorporateAccount corpAcct = null;
		// System Formats
		SystemFormats format = null;
		// To save or update flag
		String saveFlag = null;
		// To save corporateseqno added for mantis 1256 by sami on 28-Sep-2010
		String corporateSeqNo=null;
		try{
			// Get the corporateseqno added for mantis 1256 by sami on 28-Sep-2010
			corporateSeqNo=request.getParameter("corporateSeqNo");			
			// Get the value date
			valueDate = request.getParameter("selectedDate");
			// Get the corporate name
			corpName  = request.getParameter("corpName");
			// Get the amount
			amount = request.getParameter("amount");
			// Gets the flag value
			saveFlag = request.getParameter("editFlag");
			// Get the selectedEntityId and selectedEntityName
			selectedEntityId = request.getParameter("selectedEntityId");
			selectedEntityName = request.getParameter("selectedEntityName");
			// get the system format
			format = SwtUtil.getCurrentSystemFormats(request.getSession());
			// Instantiate the corporate Account
			corpAcct = new CorporateAccount();
			// set the corporateseqno  added for mantis 1256 by sami on 28-Sep-2010
			if (saveFlag.equalsIgnoreCase("false"))
				corpAcct.setCorporateSeqNo(Long.parseLong(corporateSeqNo));
			corpAcct.setEntityId(selectedEntityId);
			// Set the value date
			corpAcct.setValueDate(SwtUtil.parseDate(valueDate, SwtUtil.getCurrentDateFormat(request.getSession())));
			// Set the corporate name
			/* Corporate name field removed from primary key  for Mantis 1256 by sami on 28-Sep-2010  */
			corpAcct.setCorporateName(corpName);
			// Set the amount
			corpAcct.setAmount(SwtUtil.isEmptyOrNull(amount) ? new BigDecimal(0.0)
							: SwtUtil.parseCurrencyBig(amount, format
									.getCurrencyFormat()));
			// Set the update date
			corpAcct.setUpdateDate(SwtUtil.getSystemDatewithoutTime());
			// Set the update user
			corpAcct.setUpdateUser(SwtUtil.getCurrentUserId(request.getSession()));
			// Save the corporate account details
			corporateAccountManager.saveCorporateAccount(corpAcct,saveFlag);
			// Sets the selectedEntityId and selectedEntityName
			request.setAttribute("selectedEntityId", selectedEntityId);
			request.setAttribute("selectedEntityName", selectedEntityName);
		}catch (SwtException swtexp) {
			log.error(this.getClass().getName()
					+ " - [saveCorporateAccountDetails] - SwtException -"
					+ swtexp.getMessage());
			saveErrors(request, SwtUtil.logException(swtexp, request, ""));
			SwtUtil.logException(swtexp, request, "");
			request.setAttribute("reply_status_ok", "false");
			request.setAttribute("reply_message", swtexp.getMessage());
			request.setAttribute("reply_location", swtexp.getStackTrace()[0]
					.getClassName()
					+ "."
					+ swtexp.getStackTrace()[0].getMethodName()
					+ ":"
					+ swtexp.getStackTrace()[0].getLineNumber());
			return ("dataerror");
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					+ " - [saveCorporateAccountDetails] - GenericException -"
					+ exp.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "saveCorporateAccountDetails",
					CorporateAccountAction.class), request, "");
			request.setAttribute("reply_status_ok", "false");
			request.setAttribute("reply_message", exp.getMessage());
			request.setAttribute("reply_location", exp.getStackTrace()[0]
					.getClassName()
					+ "."
					+ exp.getStackTrace()[0].getMethodName()
					+ ":"
					+ exp.getStackTrace()[0].getLineNumber());
			return ("dataerror");
		}
		return display();
		
	}
	
	/**
	 * Method is used to delete the Corporate Account Details
	 * 
	 * @param mapping
	 *            ActionMapping
	 * @param form
	 *            ActionForm
	 * @param request
	 *            HttpServletRequest
	 * @param response
	 *            HttpServletResponse
	 * @return a Result of type Action forward
	 */
	public String deleteCorporateAccountDetails()throws SwtException {
HttpServletRequest request = ServletActionContext.getRequest();
HttpServletResponse response = ServletActionContext.getResponse();

		// Value date
		String valueDate = null;
		// Corporate Name
		String corpName = null;
		// Amount
		String amount = null;
		// Corporate Account
		CorporateAccount corpAcct = null;
		// System Formats
		SystemFormats format = null;
		// EntityId and EntityName
		String selectedEntityId = null;
		String selectedEntityName = null;
		// corporate sequence number added for mantis 1256 by sami on 28-Sep-2010
		String corporateSeqNo=null;
		try{
			//get  corporate sequence number added for mantis 1256 by sami on 28-Sep-2010
			corporateSeqNo=request.getParameter("corporateSeqNo");		
			// Get the value date
			valueDate = request.getParameter("selectedDate");
			// Get the corporate name
			corpName  = request.getParameter("corpName");
			// Get the amount
			amount = request.getParameter("amount");
			// Gets the selectedEntityId and selectedEntityName
			selectedEntityId = request.getParameter("selectedEntityId");
			selectedEntityName = request.getParameter("selectedEntityName");
			// get the system format
			format = SwtUtil.getCurrentSystemFormats(request.getSession());
			// Instantiate the corporate Account
			corpAcct = new CorporateAccount();
			//set2  corporate sequence number added for mantis 1256 by sami on 28-Sep-2010
			corpAcct.setCorporateSeqNo(Long.parseLong(corporateSeqNo));
			corpAcct.setEntityId(selectedEntityId);
			// Set the value date
			corpAcct.setValueDate(SwtUtil.parseDate(valueDate, SwtUtil.getCurrentDateFormat(request.getSession())));
			/* Corporate name filed removed from primary key  for Mantis 1256 by sami on 28-Sep-2010  */
			// Set the corporate name
			corpAcct.setCorporateName(corpName);
			// Set the amount
			corpAcct.setAmount(SwtUtil.isEmptyOrNull(amount) ? new BigDecimal(0.0)
							: SwtUtil.parseCurrencyBig(amount, format
									.getCurrencyFormat()));
			corpAcct.setUpdateUser(SwtUtil.getCurrentUserId(request.getSession()));
			// Delete the corporate account details
			corporateAccountManager.deleteCorporateAccount(corpAcct);
			//Sets the selectedEntityId and selectedEntityName
			request.setAttribute("selectedEntityId", selectedEntityId);
			request.setAttribute("selectedEntityName", selectedEntityName);
		}catch (SwtException swtexp) {
			log.error(this.getClass().getName()
					+ " - [deleteCorporateAccountDetails] - SwtException -"
					+ swtexp.getMessage());
			saveErrors(request, SwtUtil.logException(swtexp, request, ""));
			SwtUtil.logException(swtexp, request, "");
			request.setAttribute("reply_status_ok", "false");
			request.setAttribute("reply_message", swtexp.getMessage());
			request.setAttribute("reply_location", swtexp.getStackTrace()[0]
					.getClassName()
					+ "."
					+ swtexp.getStackTrace()[0].getMethodName()
					+ ":"
					+ swtexp.getStackTrace()[0].getLineNumber());
			return ("dataerror");
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					+ " - [deleteCorporateAccountDetails] - GenericException -"
					+ exp.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "deleteCorporateAccountDetails",
					CorporateAccountAction.class), request, "");
			request.setAttribute("reply_status_ok", "false");
			request.setAttribute("reply_message", exp.getMessage());
			request.setAttribute("reply_location", exp.getStackTrace()[0]
					.getClassName()
					+ "."
					+ exp.getStackTrace()[0].getMethodName()
					+ ":"
					+ exp.getStackTrace()[0].getLineNumber());
			return ("dataerror");
		}
		return display();
		
	}

}