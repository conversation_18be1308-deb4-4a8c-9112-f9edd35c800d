/*
 * @(#)SweepQueueAction.java 1.0 12-12-2005
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */

package org.swallow.work.web;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Iterator;
import java.util.LinkedHashSet;
import java.util.Set;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;






import org.swallow.control.model.EntityCurrencyGroupAccess;
import org.swallow.control.model.RoleTO;
import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.maintenance.model.CurrencyGroup;
import org.swallow.maintenance.model.EntityUserAccess;
import org.swallow.maintenance.service.CurrencyDetailVO;
import org.swallow.maintenance.service.CurrencyManager;
import org.swallow.model.User;
import org.swallow.util.CacheManager;
import org.swallow.util.CommonDataManager;
import org.swallow.util.SwtConstants;
import org.swallow.util.SwtUtil;
import org.swallow.util.SystemFormats;
import org.swallow.work.model.Sweep;
import org.swallow.work.model.SweepQueueDetailVO;
import org.swallow.work.service.SweepQueueManager;
import org.swallow.util.LabelValueBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.apache.struts2.convention.annotation.Result;
import org.apache.struts2.convention.annotation.AllowedMethods;
import org.apache.struts2.convention.annotation.Action;
import org.apache.struts2.ServletActionContext;
import org.swallow.util.struts.ActionMessages;
import org.swallow.util.struts.CustomActionSupport;

/**
 * <AUTHOR>
 * 
 * This class is to run the perform the Authorise,Submit and cancel on sweeps
 * generated
 */
@Action(value = "/sweepqueue", results = {
	@Result(name = "success", location = "/jsp/work/sweepqueue.jsp"),
	@Result(name = "cancel", location = "/jsp/work/sweepqueuecancel.jsp"),
	@Result(name = "fail", location = "/error.jsp"),
})

@AllowedMethods ({"display" ,"displayList" ,"submit" ,"setCurrencyAccess" })
public class SweepQueueAction extends CustomActionSupport {
public String execute() throws Exception {
    HttpServletRequest request = ServletActionContext.getRequest();

    // List of methods 
    String method = String.valueOf(request.getParameter("method"));

    switch (method) {
        case "unspecified":
            return unspecified();
        case "display":
            return display();
        case "displayList":
            return displayList();
        case "submit":
            return submit();
        case "setCurrencyAccess":
            setCurrencyAccess();
            return null;
        default:
            break;
    }

    return unspecified();
}


private Sweep sweepQueue;
public Sweep getSweepQueue() {
	if (sweepQueue == null) {
		sweepQueue = new Sweep();
	}
	return sweepQueue;
}
public void setSweepQueue(Sweep sweepQueue) {
	this.sweepQueue = sweepQueue;
	HttpServletRequest request = ServletActionContext.getRequest();
	request.setAttribute("sweepQueue", sweepQueue);
}


	@Autowired
private SweepQueueManager sweepQueueManager = null;

	/**
	 * Comment for <code>log</code>
	 */
	private final Log log = LogFactory.getLog(SweepQueueAction.class);
	private final String EMPTYSTRING = "";

	/**
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return a Result of type Action forward
	 */
	public String unspecified()
			throws SwtException {
HttpServletRequest request = ServletActionContext.getRequest();
HttpServletResponse response = ServletActionContext.getResponse();


		return display();
	} // end of unspecified method

	private String getFirstCurrencyGrp(HttpServletRequest request,
			String entityId) throws SwtException {
		ArrayList currrencyGroupList = new ArrayList();
		HttpSession session = request.getSession();
		User user = SwtUtil.getCurrentUser(session);
		String roleId = user.getRoleId();
		String firstCurrId = "All";

		currrencyGroupList = (ArrayList) SwtUtil.getSwtMaintenanceCache()
				.getCurrencyGroupFullORViewAcessLVL(roleId, entityId);

		if (currrencyGroupList != null) {
			if (currrencyGroupList.size() != 0) {
				LabelValueBean lvl = (LabelValueBean) currrencyGroupList.get(0);
				firstCurrId = lvl.getValue();
			} else {
				firstCurrId = "All";
			}

		}

		return firstCurrId;
	}

	/**
	 * This method is used to display sweep details
	 * 
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return
	 * @throws SwtException
	 */
	public String display()
			throws SwtException {
HttpServletRequest request = ServletActionContext.getRequest();
HttpServletResponse response = ServletActionContext.getResponse();

		/* Local variable declaration */
		String forward = null;
		String queueName = null;
		String currGrpId = null;
		String roleId = null;
		/* Variable Declaration for calledFrom */
		String calledFrom = null;
		try {
			log
					.debug(this.getClass().getName() + " - [display()] - "
							+ "Entry");
			forward = "success";
			// ArrayLists added for Currency Group 'All' case
			ArrayList sweepDetailListAll = new ArrayList();
			ArrayList otherDetailListAll = new ArrayList();
			// DynaValidatorForm HttpSession session = request.getSession();
			queueName = (String) request.getParameter("queueName");

			if (SwtConstants.SWEEP_STATUS_CANCEL.equalsIgnoreCase(queueName)) {
				forward = "cancel";
			}
			SystemFormats systemFormats = new SystemFormats();
			systemFormats.setCurrencyFormat(SwtUtil
					.getCurrentCurrencyFormat(request.getSession()));
			systemFormats.setDateFormatValue(SwtUtil
					.getCurrentDateFormat(request.getSession()));

			Sweep sweepForm = (Sweep) getSweepQueue();
			String hostId = sweepForm.getId().getHostId();
			sweepForm.setEntityId(
					SwtUtil.getUserCurrentEntity(request.getSession()));

			// Get the user's default currency group

			// Value set to default Currency Grp
			currGrpId = "";

			currGrpId = ((CommonDataManager) request.getSession()
					.getAttribute(SwtConstants.CDM_BEAN)).getUser()
					.getCurrentCcyGrpId();

			roleId = getRoleId(request);
			String entityId = SwtUtil
					.getUserCurrentEntity(request.getSession());
			if (request.getParameter("entityId") != null) {
				entityId = request.getParameter("entityId");
				
				/*
				 * if screen is excuted from genric than append selectedCurrencyGroup from currencyGroup of the selected currencyId
				 */
				calledFrom = request.getParameter("calledFrom");
				if(calledFrom!=null&&calledFrom.equals("generic")){
					
					currGrpId =  SwtUtil.getCurrencyGroup(entityId,request
							.getParameter("currencyId" ) );
				}else {
				currGrpId = request.getParameter("currCode");
				}				
				sweepForm.setEntityId(entityId);
			}

			int currGrpAccess = SwtUtil.getSwtMaintenanceCache()
					.getCurrencyGroupAccess(roleId, entityId, currGrpId);

			if (currGrpAccess == SwtConstants.CURRENCYGRP_NO_ACCESS) {
				currGrpId = "All";
			}

			// Currency Group set to 0 by default
			int currencyGrpAccess = 0;

			// Identifies the screen displayed - Submit or Authorise.
			sweepForm.setQueueName(queueName);
			sweepForm.setCurrencyCode(currGrpId);
			setSweepQueue(sweepForm);

			Sweep sweep = new Sweep();
			sweep.getId().setHostId(
					SwtUtil.getCurrentHostId(request.getSession()));
			sweep
					.setRequestUser(SwtUtil.getCurrentUserId(request
							.getSession()));
			sweep.setRequestRole(SwtUtil.getCurrentUser(request.getSession())
					.getRoleId());

			sweep.setEntityId(entityId);

			sweep.setCurrencyCode(currGrpId);
			sweep.setAccountType("All");
			sweep.setQueueName(queueName);

			SweepQueueDetailVO sweepQueueDetailVO = new SweepQueueDetailVO();
			sweepQueueDetailVO.setEntityAccessList(SwtUtil
					.getUserEntityAccessList(request.getSession()));

			// Code added for Currency Group 'All' case
			if (currGrpId.equalsIgnoreCase(SwtConstants.ALL_VALUE)) {
				Collection groupList = SwtUtil.getSwtMaintenanceCache()
						.getCurrencyGroupViewORFullAcess(roleId, entityId);
				Iterator itGroupList = groupList.iterator();
				ArrayList temp = new ArrayList();

				while (itGroupList.hasNext()) {
					EntityCurrencyGroupAccess entityCurrencyGroupAccess = (EntityCurrencyGroupAccess) itGroupList
							.next();

					currGrpId = entityCurrencyGroupAccess.getCurrencyGroupId();

					sweep.setCurrencyCode(currGrpId);
					sweepQueueDetailVO.setEntityAccessList(SwtUtil
							.getUserEntityAccessList(request.getSession()));
					sweepQueueDetailVO.setSweepDetailList(new ArrayList());
					sweepQueueDetailVO.setOtherDetailList(new ArrayList());
					currencyGrpAccess = SwtUtil
							.getSwtMaintenanceCache()
							.getCurrencyGroupAccess(roleId, entityId, currGrpId);

					sweepQueueManager.getSweepQueueDetail(sweep, roleId,
							currencyGrpAccess, sweepQueueDetailVO, 0, 0,
							SwtConstants.EMPTY_STRING, systemFormats);
					temp = (ArrayList) sweepQueueDetailVO.getSweepDetailList();

					Iterator tempItr = temp.iterator();

					while (tempItr.hasNext())
						sweepDetailListAll.add(tempItr.next());

					temp = (ArrayList) sweepQueueDetailVO.getOtherDetailList();
					tempItr = temp.iterator();

					while (tempItr.hasNext())
						otherDetailListAll.add(tempItr.next());

					sweep.setCurrencyCode("All");
				}
			} else {
				sweepQueueManager.getSweepQueueDetail(sweep, roleId,
						currencyGrpAccess, sweepQueueDetailVO, 0, 0,
						SwtConstants.EMPTY_STRING, systemFormats);
				sweepDetailListAll = (ArrayList) sweepQueueDetailVO
						.getSweepDetailList();
				otherDetailListAll = (ArrayList) sweepQueueDetailVO
						.getOtherDetailList();
			}

			putEntityListInReq(request);
			putCurrencyGroupListInReq(request, sweep.getId().getHostId(), sweep
					.getEntityId());

			putAccountListInReq(request, entityId);

			request.setAttribute("AccountDesp", SwtConstants.ALL_ACCOUNTS_TYPE);

			// Code added for Currency Group 'All' case - attributes set in
			// request
			request.setAttribute("sweepDetailList", sweepDetailListAll);
			request.setAttribute("othersDetailList", otherDetailListAll);

			request.setAttribute("lastRefTime", SwtUtil.getLastRefTime(request,
					entityId));
			SwtUtil.getMenuEntityCurrGrpAccessWithoutCurrency(request,
					entityId, currGrpId);
			log.debug(this.getClass().getName() + " - [display()] - " + "Exit");
			return forward;
		} catch (SwtException swtexp) {
			log
					.error("SwtException Catch in SweepQueueAction.'display' method : "
							+ swtexp.getMessage());
			SwtUtil.logException(swtexp, request, "");

			return ("fail");
		} catch (Exception exp) {
			log.error("Exception Catch in SweepQueueAction.'display' method : "
					+ exp.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "display", SweepQueueAction.class), request, "");

			return ("fail");
		} finally {
			/* null the objects created already. */
			forward = null;
			queueName = null;
			currGrpId = null;
			roleId = null;
		}
	}

	/**
	 * 
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return
	 * @throws SwtException
	 */
	public String displayList()
			throws SwtException {
HttpServletRequest request = ServletActionContext.getRequest();
HttpServletResponse response = ServletActionContext.getResponse();

		ActionMessages errors = new ActionMessages();
		Sweep sweep = null;
		// DynaValidatorForm dyForm = null;

		try {
			log.debug(" Entering displayList method");
			String forward = "success";
			ArrayList sweepDetailListAll = new ArrayList();
			ArrayList otherDetailListAll = new ArrayList();
			HttpSession session = null;
			int currencyGrpAccess = 0;
			SystemFormats systemFormats = new SystemFormats();
			String fromEntity = request.getParameter("fromEntity");
			systemFormats.setCurrencyFormat(SwtUtil
					.getCurrentCurrencyFormat(request.getSession()));
			systemFormats.setDateFormatValue(SwtUtil
					.getCurrentDateFormat(request.getSession()));
			sweep = (Sweep) getSweepQueue();

			sweep.getId().setHostId(
					SwtUtil.getCurrentHostId(request.getSession()));
			sweep
					.setRequestUser(SwtUtil.getCurrentUserId(request
							.getSession()));

			sweep.setRequestRole(SwtUtil.getCurrentUser(request.getSession())
					.getRoleId());

			if (sweep.getAccountType().equalsIgnoreCase("C")) {
				request.setAttribute("AccountDesp",
						SwtConstants.CASH_ACCOUNTS_TYPE);
			}

			if (sweep.getAccountType().equalsIgnoreCase("U")) {
				request.setAttribute("AccountDesp",
						SwtConstants.CUSTODIAN_ACCOUNTS_TYPE);
			}

			if (sweep.getAccountType().equalsIgnoreCase("All")) {
				request.setAttribute("AccountDesp",
						SwtConstants.ALL_ACCOUNTS_TYPE);
			}

			putEntityListInReq(request);

			// now putCurrencyGroupListInReq function will be used
			// as per the rabo bank implementation
			putCurrencyGroupListInReq(request, sweep.getId().getHostId(), sweep
					.getEntityId());

			putAccountListInReq(request, sweep.getEntityId());

			SweepQueueDetailVO sweepQueueDetailVO = new SweepQueueDetailVO();
			sweepQueueDetailVO.setEntityAccessList(SwtUtil
					.getUserEntityAccessList(request.getSession()));

			String currGrpId = "";
			String roleId = getRoleId(request);
			String entityId = sweep.getEntityId();

			session = request.getSession();
			if ((!entityId.equalsIgnoreCase(SwtUtil
					.getUserCurrentEntity(request.getSession())))
					&& sweep.getCurrencyCode().equalsIgnoreCase(
							((CommonDataManager) session
									.getAttribute(SwtConstants.CDM_BEAN))
									.getUser().getCurrentCcyGrpId())) {
				currGrpId = "All";

			} else {
				currGrpId = sweep.getCurrencyCode();
			}

			// code added to handle Entity as All starts
			if (entityId.equalsIgnoreCase(SwtConstants.ALL_VALUE)) {
				RoleTO roleObject = new RoleTO(roleId);
				Collection entityList = SwtUtil.getSwtMaintenanceCache()
						.getEntityAccessCollection(roleObject);
				Iterator itr = entityList.iterator();

				while (itr.hasNext()) {

					currGrpId = sweep.getCurrencyCode();

					EntityUserAccess entityUserAccess = (EntityUserAccess) itr
							.next();
					entityId = entityUserAccess.getEntityId();

					sweep.setEntityId(entityId);

					if (currGrpId.equalsIgnoreCase(SwtConstants.ALL_VALUE)) {
						currencyGrpAccess = 0;
					} else {
						currencyGrpAccess = SwtUtil.getSwtMaintenanceCache()
								.getCurrencyGroupAccess(roleId, entityId,
										currGrpId);
					}

					if (currGrpId.equalsIgnoreCase(SwtConstants.ALL_VALUE)) {

						Collection groupList = SwtUtil.getSwtMaintenanceCache()
								.getCurrencyGroupViewORFullAcess(roleId,
										entityId);

						Iterator itGroupList = groupList.iterator();
						ArrayList temp = new ArrayList();

						while (itGroupList.hasNext()) {
							EntityCurrencyGroupAccess entityCurrencyGroupAccess = (EntityCurrencyGroupAccess) itGroupList
									.next();
							currGrpId = entityCurrencyGroupAccess
									.getCurrencyGroupId();

							sweep.setCurrencyCode(currGrpId);

							sweepQueueDetailVO.setEntityAccessList(SwtUtil
									.getUserEntityAccessList(request
											.getSession()));
							sweepQueueDetailVO
									.setSweepDetailList(new ArrayList());
							sweepQueueDetailVO
									.setOtherDetailList(new ArrayList());

							currencyGrpAccess = SwtUtil
									.getSwtMaintenanceCache()
									.getCurrencyGroupAccess(roleId, entityId,
											currGrpId);

							sweepQueueManager.getSweepQueueDetail(sweep,
									roleId, currencyGrpAccess,
									sweepQueueDetailVO, 0, 0,
									SwtConstants.EMPTY_STRING, systemFormats);
							temp = (ArrayList) sweepQueueDetailVO
									.getSweepDetailList();

							Iterator tempItr = temp.iterator();

							while (tempItr.hasNext())
								sweepDetailListAll.add(tempItr.next());

							temp = (ArrayList) sweepQueueDetailVO
									.getOtherDetailList();
							tempItr = temp.iterator();

							while (tempItr.hasNext())
								otherDetailListAll.add(tempItr.next());
						}

						sweep.setCurrencyCode("All");
					} else {

						ArrayList temp = new ArrayList();
						sweepQueueManager.getSweepQueueDetail(sweep, roleId,
								currencyGrpAccess, sweepQueueDetailVO, 0, 0,
								SwtConstants.EMPTY_STRING, systemFormats);

						temp = (ArrayList) sweepQueueDetailVO
								.getSweepDetailList();

						Iterator tempItr = temp.iterator();

						while (tempItr.hasNext())
							sweepDetailListAll.add(tempItr.next());

						temp = (ArrayList) sweepQueueDetailVO
								.getOtherDetailList();
						tempItr = temp.iterator();

						while (tempItr.hasNext())
							otherDetailListAll.add(tempItr.next());
					}
				}

				sweep.setEntityId("All");

			} else {
				if (currGrpId.equalsIgnoreCase(SwtConstants.ALL_VALUE)) {
					currencyGrpAccess = 0;
				} else {
					currencyGrpAccess = SwtUtil
							.getSwtMaintenanceCache()
							.getCurrencyGroupAccess(roleId, entityId, currGrpId);
				}

				if (currGrpId.equalsIgnoreCase(SwtConstants.ALL_VALUE)) {

					Collection groupList = SwtUtil.getSwtMaintenanceCache()
							.getCurrencyGroupViewORFullAcess(roleId, entityId);
					Iterator itGroupList = groupList.iterator();
					ArrayList temp = new ArrayList();

					// HashSet temp= new HashSet();
					while (itGroupList.hasNext()) {
						EntityCurrencyGroupAccess entityCurrencyGroupAccess = (EntityCurrencyGroupAccess) itGroupList
								.next();
						currGrpId = entityCurrencyGroupAccess
								.getCurrencyGroupId();

						sweep.setCurrencyCode(currGrpId);
						sweepQueueDetailVO.setEntityAccessList(SwtUtil
								.getUserEntityAccessList(request.getSession()));
						sweepQueueDetailVO.setSweepDetailList(new ArrayList());

						sweepQueueDetailVO.setOtherDetailList(new ArrayList());

						currencyGrpAccess = SwtUtil.getSwtMaintenanceCache()
								.getCurrencyGroupAccess(roleId, entityId,
										currGrpId);
						sweepQueueManager.getSweepQueueDetail(sweep, roleId,
								currencyGrpAccess, sweepQueueDetailVO, 0, 0,
								SwtConstants.EMPTY_STRING, systemFormats);
						temp = (ArrayList) sweepQueueDetailVO
								.getSweepDetailList();

						Iterator tempItr = temp.iterator();

						while (tempItr.hasNext())
							sweepDetailListAll.add(tempItr.next());

						temp = (ArrayList) sweepQueueDetailVO
								.getOtherDetailList();

						tempItr = temp.iterator();

						while (tempItr.hasNext())
							otherDetailListAll.add(tempItr.next());

						sweep.setCurrencyCode("All");
					}
				} else {
					sweepQueueManager.getSweepQueueDetail(sweep, roleId,
							currencyGrpAccess, sweepQueueDetailVO, 0, 0,
							SwtConstants.EMPTY_STRING, systemFormats);

					sweepDetailListAll = (ArrayList) sweepQueueDetailVO
							.getSweepDetailList();
					otherDetailListAll = (ArrayList) sweepQueueDetailVO
							.getOtherDetailList();
				}
			}

			setSweepQueue(sweep);

			request.setAttribute("sweepDetailList", sweepDetailListAll);
			request.setAttribute("othersDetailList", otherDetailListAll);

			request.setAttribute("lastRefTime", SwtUtil.getLastRefTime(request,
					entityId));

			if (SwtConstants.SWEEP_STATUS_CANCEL.equalsIgnoreCase(sweep
					.getQueueName())) {
				forward = "cancel";
			}

			SwtUtil.getMenuEntityCurrGrpAccessWithoutCurrency(request,
					entityId, currGrpId);

			return forward;
		} catch (SwtException swtexp) {
			log
					.error("SwtException Catch in SweepQueueAction.'displayList' method : "
							+ swtexp.getMessage());
			saveErrors(request, SwtUtil.logException(swtexp, request, ""));
//			if (dyForm != null) {
				setSweepQueue(sweep);
//			}

			return ("success");
		} catch (Exception exp) {
			log
					.error("Exception Catch in SweepQueueAction.'displayList' method : "
							+ exp.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "displayList", SweepQueueAction.class), request, "");

			return ("fail");
		}
	}

	/**
	 * 
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return
	 * @throws SwtException
	 */
	public String submit()
			throws SwtException {
HttpServletRequest request = ServletActionContext.getRequest();
HttpServletResponse response = ServletActionContext.getResponse();

		ActionMessages errors = new ActionMessages();
		Sweep sweep = null;
		// DynaValidatorForm dyForm = null;
		SystemFormats systemFormats = new SystemFormats();
		ArrayList sweepDetailListAll = new ArrayList();
		ArrayList otherDetailListAll = new ArrayList();
		int currencyGrpAccess = -1;
		String sweeps = "";

		try {
			log.debug(" Entering submit method");

			String forward = "success";
			String selectedlist = (String) request.getParameter("selectedList");

			// Added to identify if the sweep is to be submitted
			// even if new sweep amount is changed.
			String bypassChangedSweep = (String) request
					.getParameter("bypassChangedSweep");

			String bypassCutOff = (String) request.getParameter("bypassCutOff");

			String bypassAccountBreach = (String) request
					.getParameter("bypassAccountBreach");

			sweep = (Sweep) getSweepQueue();

			systemFormats.setCurrencyFormat(SwtUtil
					.getCurrentCurrencyFormat(request.getSession()));
			systemFormats.setDateFormatValue(SwtUtil
					.getCurrentDateFormat(request.getSession()));

			sweep.getId().setHostId(
					SwtUtil.getCurrentHostId(request.getSession()));
			sweep
					.setRequestUser(SwtUtil.getCurrentUserId(request
							.getSession()));

			sweep.setRequestRole(SwtUtil.getCurrentUser(request.getSession())
					.getRoleId());

			// submit/authorize/cancel sweep
			ActionMessages list = null;

			if (SwtConstants.SWEEP_STATUS_CANCEL.equalsIgnoreCase(sweep
					.getQueueName())) {
				list = sweepQueueManager.cancel(sweep, selectedlist,
						systemFormats);
				forward = "cancel";
			} else {

				list = sweepQueueManager.submit(sweep, selectedlist,
						systemFormats, bypassChangedSweep, bypassCutOff,
						bypassAccountBreach);
			}

			// Checking if error is of type - SwtConstants.SWEEP_AMOUNT_CHANGED
			Iterator errorsType1 = list.get(SwtConstants.SWEEP_CUTOFF_EXCEEDED);

			Iterator errorsType2 = list.get(SwtConstants.SWEEP_AMOUNT_CHANGED);

			Iterator errorsType3 = list
					.get(SwtConstants.SWEEP_ACCOUNT_BREACHED);

			if (errorsType1.hasNext()) {

				// Setting this status will enable to call a pop-up with Yes and
				// No
				// as buttons along with error message.
				String sweepMsg = errorsType1.next().toString();
				int start = sweepMsg.indexOf("[");
				int end = sweepMsg.indexOf("]");
				sweeps = sweepMsg.substring(start + 1, end);

				// Jsp will open a message box with yes/no options
				// if IS_ERROR_SWEEP_AMOUNT = "Y"
				request.setAttribute("IS_ERROR_CUT_OFF", "Y");

				// sets queueName in request
				request.setAttribute("queueName", sweep.getQueueName());
			}

			if (errorsType2.hasNext()) {

				// Setting this status will enable to call a pop-up with Yes and
				// No
				// as buttons along with error message.
				String sweepMsg = errorsType2.next().toString();
				int start = sweepMsg.indexOf("[");
				int end = sweepMsg.indexOf("]");
				if (sweeps.equals(""))
					sweeps = sweepMsg.substring(start + 1, end);
				else
					sweeps = sweeps + "," + sweepMsg.substring(start + 1, end);
				// Jsp will open a message box with yes/no options
				// if IS_ERROR_SWEEP_AMOUNT = "Y"
				request.setAttribute("IS_ERROR_SWEEP_AMOUNT", "Y");

				request.setAttribute("bypassCutOff", bypassCutOff);

				// sets the sweep ids whose calculated amount <> queue amount

				// sets queueName in request
				request.setAttribute("queueName", sweep.getQueueName());
			}

			if (errorsType3.hasNext()) {

				// Setting this status will enable to call a pop-up with Yes and
				// No
				// as buttons along with error message.
				String sweepMsg = errorsType3.next().toString();
				int start = sweepMsg.indexOf("[");
				int end = sweepMsg.indexOf("]");
				if (sweeps.equals(""))
					sweeps = sweepMsg.substring(start + 1, end);
				else
					sweeps = sweeps + "," + sweepMsg.substring(start + 1, end);
				// Jsp will open a message box with yes/no options
				// if IS_ERROR_SWEEP_AMOUNT = "Y"
				request.setAttribute("IS_ERROR_ACCOUNT_BREACH", "Y");

				// sets the sweep ids whose calculated amount <> queue amount

				request.setAttribute("bypassChangedSweep", bypassChangedSweep);
				request.setAttribute("bypassCutOff", bypassCutOff);

				// sets queueName in request
				request.setAttribute("queueName", sweep.getQueueName());
			}

			saveErrors(request, list);
			// sets the sweep ids whose calculated amount <> queue amount
			if (!sweeps.equals(""))
				request.setAttribute("ERROR_SWEEPS", sweeps);
			// after processing refresh the screen
			SweepQueueDetailVO sweepQueueDetailVO = new SweepQueueDetailVO();
			sweepQueueDetailVO.setEntityAccessList(SwtUtil
					.getUserEntityAccessList(request.getSession()));
			putEntityListInReq(request);

			String currGrpId = sweep.getCurrencyCode();
			String roleId = getRoleId(request);
			String entityId = sweep.getEntityId();

			if (!entityId.equalsIgnoreCase(SwtConstants.ALL_VALUE)
					&& !currGrpId.equalsIgnoreCase(SwtConstants.ALL_VALUE)) {
				currencyGrpAccess = SwtUtil.getSwtMaintenanceCache()
						.getCurrencyGroupAccess(roleId, entityId, currGrpId);
			}

			// code handling for Entity:All starts
			if (entityId.equalsIgnoreCase(SwtConstants.ALL_VALUE)) {
				RoleTO roleObject = new RoleTO(roleId);
				Collection entityList = SwtUtil.getSwtMaintenanceCache()
						.getEntityAccessCollection(roleObject);
				Iterator itr = entityList.iterator();

				while (itr.hasNext()) {
					currGrpId = sweep.getCurrencyCode();

					EntityUserAccess entityUserAccess = (EntityUserAccess) itr
							.next();
					entityId = entityUserAccess.getEntityId();

					sweep.setEntityId(entityId);

					if (currGrpId.equalsIgnoreCase(SwtConstants.ALL_VALUE)) {
						currencyGrpAccess = 0;
					} else {
						currencyGrpAccess = SwtUtil.getSwtMaintenanceCache()
								.getCurrencyGroupAccess(roleId, entityId,
										currGrpId);
					}

					if (currGrpId.equalsIgnoreCase(SwtConstants.ALL_VALUE)) {

						Collection groupList = SwtUtil.getSwtMaintenanceCache()
								.getCurrencyGroupViewORFullAcess(roleId,
										entityId);

						Iterator itGroupList = groupList.iterator();
						ArrayList temp = new ArrayList();

						while (itGroupList.hasNext()) {
							EntityCurrencyGroupAccess entityCurrencyGroupAccess = (EntityCurrencyGroupAccess) itGroupList
									.next();
							currGrpId = entityCurrencyGroupAccess
									.getCurrencyGroupId();

							sweep.setCurrencyCode(currGrpId);

							sweepQueueDetailVO.setEntityAccessList(SwtUtil
									.getUserEntityAccessList(request
											.getSession()));
							sweepQueueDetailVO
									.setSweepDetailList(new ArrayList());
							sweepQueueDetailVO
									.setOtherDetailList(new ArrayList());

							currencyGrpAccess = SwtUtil
									.getSwtMaintenanceCache()
									.getCurrencyGroupAccess(roleId, entityId,
											currGrpId);
							sweepQueueManager.getSweepQueueDetail(sweep,
									roleId, currencyGrpAccess,
									sweepQueueDetailVO, 0, 0,
									SwtConstants.EMPTY_STRING, systemFormats);
							temp = (ArrayList) sweepQueueDetailVO
									.getSweepDetailList();

							Iterator tempItr = temp.iterator();

							while (tempItr.hasNext()) {
								sweepDetailListAll.add(tempItr.next());
							}

							temp = (ArrayList) sweepQueueDetailVO
									.getOtherDetailList();
							tempItr = temp.iterator();

							while (tempItr.hasNext()) {
								otherDetailListAll.add(tempItr.next());
							}
						}

						sweep.setCurrencyCode("All");
					} else {
						ArrayList temp = new ArrayList();

						sweepQueueManager.getSweepQueueDetail(sweep, roleId,
								currencyGrpAccess, sweepQueueDetailVO, 0, 0,
								SwtConstants.EMPTY_STRING, systemFormats);
						temp = (ArrayList) sweepQueueDetailVO
								.getSweepDetailList();

						Iterator tempItr = temp.iterator();

						while (tempItr.hasNext())
							sweepDetailListAll.add(tempItr.next());

						temp = (ArrayList) sweepQueueDetailVO
								.getOtherDetailList();
						tempItr = temp.iterator();

						while (tempItr.hasNext())
							otherDetailListAll.add(tempItr.next());
					}
				}

				sweep.setEntityId("All");

			} else {
				// code handling for Entity: All ends
				// code for handling of All currency group added starts
				if (currGrpId.equalsIgnoreCase(SwtConstants.ALL_VALUE)) {
					Collection groupList = SwtUtil.getSwtMaintenanceCache()
							.getCurrencyGroupViewORFullAcess(roleId, entityId);
					Iterator itGroupList = groupList.iterator();
					ArrayList temp = new ArrayList();

					while (itGroupList.hasNext()) {
						EntityCurrencyGroupAccess entityCurrencyGroupAccess = (EntityCurrencyGroupAccess) itGroupList
								.next();
						currGrpId = entityCurrencyGroupAccess
								.getCurrencyGroupId();

						sweep.setCurrencyCode(currGrpId);

						sweepQueueDetailVO.setEntityAccessList(SwtUtil
								.getUserEntityAccessList(request.getSession()));
						sweepQueueDetailVO.setSweepDetailList(new ArrayList());
						sweepQueueDetailVO.setOtherDetailList(new ArrayList());

						currencyGrpAccess = SwtUtil.getSwtMaintenanceCache()
								.getCurrencyGroupAccess(roleId, entityId,
										currGrpId);

						sweepQueueManager.getSweepQueueDetail(sweep, roleId,
								currencyGrpAccess, sweepQueueDetailVO, 0, 0,
								SwtConstants.EMPTY_STRING, systemFormats);
						temp = (ArrayList) sweepQueueDetailVO
								.getSweepDetailList();

						Iterator tempItr = temp.iterator();

						while (tempItr.hasNext())
							sweepDetailListAll.add(tempItr.next());

						temp = (ArrayList) sweepQueueDetailVO
								.getOtherDetailList();
						tempItr = temp.iterator();

						while (tempItr.hasNext())
							otherDetailListAll.add(tempItr.next());

						sweep.setCurrencyCode("All");
					}
				} else {
					sweepQueueManager.getSweepQueueDetail(sweep, roleId,
							currencyGrpAccess, sweepQueueDetailVO, 0, 0,
							SwtConstants.EMPTY_STRING, systemFormats);
					sweepDetailListAll = (ArrayList) sweepQueueDetailVO
							.getSweepDetailList();
					otherDetailListAll = (ArrayList) sweepQueueDetailVO
							.getOtherDetailList();
				}
			}

			// code for handling of All currency group ends
			setSweepQueue(sweep);
			request.setAttribute("sweepDetailList", sweepDetailListAll);
			request.setAttribute("othersDetailList", otherDetailListAll);

			// now putCurrencyGroupListInReq function
			// will be used as per the rabo bank implementation
			putCurrencyGroupListInReq(request, sweep.getId().getHostId(), sweep
					.getEntityId());

			putAccountListInReq(request, entityId);

			request.setAttribute("lastRefTime", SwtUtil.getLastRefTime(request,
					entityId));
			return forward;
		} catch (SwtException sexp) {
			log
					.error("SwtException Catch in SweepQueueAction.'submit' method : "
							+ sexp.getMessage());

			saveErrors(request, SwtUtil.logException(sexp, request, ""));

			String currGrpId = sweep.getCurrencyCode();
			String roleId = getRoleId(request);
			String entityId = SwtUtil
					.getUserCurrentEntity(request.getSession());
			currencyGrpAccess = SwtUtil.getSwtMaintenanceCache()
					.getCurrencyGroupAccess(roleId, entityId, currGrpId);

			// open the screen in same status again
			SweepQueueDetailVO sweepQueueDetailVO = new SweepQueueDetailVO();
			sweepQueueDetailVO.setEntityAccessList(SwtUtil
					.getUserEntityAccessList(request.getSession()));

			// sweepQueueManager.getSweepQueueDetail(sweep, roleId,
			// currencyGrpAccess, sweepQueueDetailVO, systemFormats);
			// code for handling of All currency group added starts
			if (currGrpId.equalsIgnoreCase(SwtConstants.ALL_VALUE)) {
				// log.debug("processing for ALL starts");
				Collection groupList = SwtUtil.getSwtMaintenanceCache()
						.getCurrencyGroupViewORFullAcess(roleId, entityId);
				Iterator itGroupList = groupList.iterator();
				ArrayList temp = new ArrayList();

				while (itGroupList.hasNext()) {
					EntityCurrencyGroupAccess entityCurrencyGroupAccess = (EntityCurrencyGroupAccess) itGroupList
							.next();
					currGrpId = entityCurrencyGroupAccess.getCurrencyGroupId();

					sweep.setCurrencyCode(currGrpId);

					sweepQueueDetailVO.setEntityAccessList(SwtUtil
							.getUserEntityAccessList(request.getSession()));
					sweepQueueDetailVO.setSweepDetailList(new ArrayList());
					sweepQueueDetailVO.setOtherDetailList(new ArrayList());

					currencyGrpAccess = SwtUtil
							.getSwtMaintenanceCache()
							.getCurrencyGroupAccess(roleId, entityId, currGrpId);

					sweepQueueManager.getSweepQueueDetail(sweep, roleId,
							currencyGrpAccess, sweepQueueDetailVO, 0, 0,
							SwtConstants.EMPTY_STRING, systemFormats);

					temp = (ArrayList) sweepQueueDetailVO.getSweepDetailList();

					Iterator tempItr = temp.iterator();

					while (tempItr.hasNext())
						sweepDetailListAll.add(tempItr.next());

					temp = (ArrayList) sweepQueueDetailVO.getOtherDetailList();
					tempItr = temp.iterator();

					while (tempItr.hasNext())
						otherDetailListAll.add(tempItr.next());

					sweep.setCurrencyCode("All");
				}
					setSweepQueue(sweep);
				request.setAttribute("sweepDetailList", sweepDetailListAll);
				request.setAttribute("othersDetailList", otherDetailListAll);
			} else {
				sweepQueueManager.getSweepQueueDetail(sweep, roleId,
						currencyGrpAccess, sweepQueueDetailVO, 0, 0,
						SwtConstants.EMPTY_STRING, systemFormats);
				setSweepQueue(sweep);
				request.setAttribute("sweepDetailList", sweepQueueDetailVO
						.getSweepDetailList());
				request.setAttribute("othersDetailList", sweepQueueDetailVO
						.getOtherDetailList());
			}

			putEntityListInReq(request);

			putCurrencyGroupListInReq(request, sweep.getId().getHostId(), sweep
					.getEntityId());
			putAccountListInReq(request, entityId);
			request.setAttribute("lastRefTime", SwtUtil.getLastRefTime(request,
					entityId));
			String forward = "success";

			if (SwtConstants.SWEEP_STATUS_CANCEL.equalsIgnoreCase(sweep
					.getQueueName())) {
				forward = "cancel";
			}

			SwtUtil.getMenuEntityCurrGrpAccessWithoutCurrency(request,
					entityId, currGrpId);

			return forward;
		} catch (Exception exp) {
			log.error("Exception Catch in SweepQueueAction.'submit' method : "
					+ exp.getMessage());

			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "submit", SweepQueueAction.class), request, "");

			return ("fail");
		}
	}

	private void putEntityListInReq(HttpServletRequest request)
			throws SwtException {

		Collection coll = SwtUtil.getUserEntityAccessList(request.getSession());
		coll = SwtUtil.convertEntityAcessCollectionLVL(coll, request
				.getSession());

		ArrayList list = (ArrayList) coll;
		list.add(0, new LabelValueBean("All", "All"));
		request.setAttribute("entityList", coll);

	}

	private void putCurrencyListInReq(HttpServletRequest request, Sweep sweep)
			throws SwtException {
		CurrencyManager currencyManager = (CurrencyManager) SwtUtil
				.getBean("currencyManager");

		CurrencyDetailVO currencyDetailVO = null;

		if (sweep.getEntityId() != null) {
			if (sweep.getEntityId().equals(SwtConstants.ALL_VALUE)) {
				Collection coll = SwtUtil.getUserEntityAccessList(request
						.getSession());
				coll = SwtUtil.convertEntityAcessCollectionLVL(coll, request
						.getSession());
				currencyDetailVO = currencyManager.getCurrencyDetailList(coll,
						sweep.getId().getHostId(), "All", false);

				Set set = new LinkedHashSet();
				set.addAll(currencyDetailVO.getCurrencyList());
				currencyDetailVO.getCurrencyList().clear();
				currencyDetailVO.getCurrencyList().addAll(set);
			} else {
				currencyDetailVO = currencyManager.getCurrencyDetailList(sweep
						.getEntityId(), sweep.getId().getHostId(),
						"All");

				ArrayList coll = (ArrayList) currencyDetailVO.getCurrencyList();

				if (coll.size() > 0) {
					coll.remove(0);
				}
			}

			ArrayList list = (ArrayList) currencyDetailVO.getCurrencyList();
			list.add(0, new LabelValueBean(SwtConstants.ALL_LABEL,
					SwtConstants.ALL_VALUE));

			request.setAttribute("currencyList", list);
		} else {
			request.setAttribute("currencyList", new ArrayList());
		}
	}

	private void putAccountListInReq(HttpServletRequest request, String entityId)
			throws SwtException {
		CacheManager cacheManagerInst = CacheManager.getInstance();
		ArrayList coll = (ArrayList) cacheManagerInst.getMiscParamsLVL(
				"ACCOUNTTYPE", entityId);
		coll.remove(0);
		coll.add(0, new LabelValueBean("All", "All"));
		request.setAttribute("accountList", coll);
	}

	/**
	 * @return Returns the sweepQueueManager.
	 */
	public SweepQueueManager getSweepQueueManager() {
		return sweepQueueManager;
	}

	/**
	 * @param sweepQueueManager
	 *            The sweepQueueManager to set.
	 */
	public void setSweepQueueManager(SweepQueueManager sweepQueueManager) {
		this.sweepQueueManager = sweepQueueManager;
	}

	/**
	 * This method is used to put associated CurrencyGroup list into the request
	 * that is further displayed on the UI.
	 * 
	 * @param request
	 * @param hostId
	 * @param entityId
	 */
	private String putCurrencyGroupListInReq(HttpServletRequest request,
			String hostId, String entityId) throws SwtException {
		log.debug("Inside SweepQueueAction.putCurrencyGroupListInReq method");

		Collection currencyGroupList = new ArrayList();

		CurrencyGroup currencyGroup = new CurrencyGroup();

		String defaultCurrencyGroup = new String();
		String roleId = getRoleId(request);

		// code added for handling of the Entity:All STARTS
		ArrayList al = new ArrayList();

		if (entityId.equalsIgnoreCase(SwtConstants.ALL_VALUE)) {

			RoleTO roleObject = new RoleTO(roleId);
			Collection entityList = SwtUtil.getSwtMaintenanceCache()
					.getEntityAccessCollection(roleObject);
			Iterator itr = entityList.iterator();

			while (itr.hasNext()) {
				EntityUserAccess entityUserAccess = (EntityUserAccess) itr
						.next();
				String entityIdAll = entityUserAccess.getEntityId();

				Collection groupListAll = SwtUtil.getSwtMaintenanceCache()
						.getCurrencyGroupViewORFullAcess(roleId, entityIdAll);
				Iterator groupListAllitr = groupListAll.iterator();

				while (groupListAllitr.hasNext())
					al.add(groupListAllitr.next());
			}
		} else {
			// code added for handling of the Entity:All ENDS
			al = (ArrayList) SwtUtil.getSwtMaintenanceCache()
					.getCurrencyGroupViewORFullAcess(roleId, entityId);

		}

		Iterator itGroupList = al.iterator();
		currencyGroupList.add(new LabelValueBean(SwtConstants.ALL_LABEL,
				SwtConstants.ALL_VALUE));

		while (itGroupList.hasNext()) {
			EntityCurrencyGroupAccess entityCurrencyGroupAccess = (EntityCurrencyGroupAccess) itGroupList
					.next();

			currencyGroupList.add(new LabelValueBean(entityCurrencyGroupAccess
					.getCurrencyGroupName(), entityCurrencyGroupAccess
					.getCurrencyGroupId()));
		}

		request.setAttribute("currencyGroupList", currencyGroupList);

		if ((currencyGroupList != null) && (currencyGroupList.size() > 1)) {
			ArrayList tempArrayList = (ArrayList) currencyGroupList;
			LabelValueBean labelValueBean = (LabelValueBean) tempArrayList
					.get(1);

			defaultCurrencyGroup = labelValueBean.getValue();
		}

		return defaultCurrencyGroup;
	}

	public String getRoleId(HttpServletRequest request) {
		CommonDataManager CDM = (CommonDataManager) request.getSession()
				.getAttribute("CDM");
		User currUser = (User) CDM.getUser();
		String roleId = currUser.getRoleId();

		return roleId;
	}

	/**
	 * Start:Code Modified for Mantis 1483 by Chinniah on
	 * 7-Aug-2011:Authorization button not Enabled. When the last currency set
	 * view access through role for the particular Entity
	 */
	/**
	 * This method is used to enable the Authorise button based on selection of
	 * the Sweeps
	 * 
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @throws SwtException
	 */
	public void setCurrencyAccess()
			throws SwtException {
HttpServletRequest request = ServletActionContext.getRequest();
HttpServletResponse response = ServletActionContext.getResponse();

		// Declering string hostId for storing the current hostid
		String hostId = null;
		// Declearing String Entity Id for storing the current entityId
		String entityId = null;
		// Declearing String for storing selected currency in data grid
		String currencyId = null;
		// Declaring Boolean access for determining the access type for the
		// currency
		boolean access = false;
		try {
			log.debug(this.getClass().getName() + " - [setCurrencyAccess()] - "
					+ "Entering");
			// getting current hostId
			hostId = SwtUtil.getCurrentHostId(request.getSession());
			// Getting the current entityId from request
			entityId = request.getParameter("entityId");
			// getting the selected currency in the data grid from request
			currencyId = request.getParameter("selectedCurrency");
			// Detremining the currency access
			access = SwtUtil.getFullAccesOnCurrencyAndEntity(request, hostId,
					entityId, currencyId);
			// returning response
			response.getWriter().print(access);
		} catch (Exception exp) {
			log
					.error("Exception Catch in SweepQueueAction.'setCurrencyAccess' method : "
							+ exp.getMessage());

			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "setCurrencyAccess", SweepQueueAction.class), request,
					"");
		} finally {
			// nullify the objects
			hostId = null;
			entityId = null;
			currencyId = null;
			log.debug(this.getClass().getName() + " - [setCurrencyAccess()] - "
					+ "Exit");
		}

	}
	/**
	 * End:Code Modified for Mantis 1483 by Chinniah on 7-Aug-2011:Authorization
	 * button not Enabled. When the last currency set view access through role
	 * for the particular Entity
	 */
}
