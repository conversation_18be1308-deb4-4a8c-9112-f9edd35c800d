/*
 * @(#)SweepCancelQueueAction.java 1.0 11-06-2010
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.work.web;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Iterator;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;






import org.swallow.control.model.EntityCurrencyGroupAccess;
import org.swallow.control.model.RoleTO;
import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.maintenance.model.EntityUserAccess;
import org.swallow.model.User;
import org.swallow.util.CacheManager;
import org.swallow.util.CommonDataManager;
import org.swallow.util.PageDetails;
import org.swallow.util.PropertiesFileLoader;
import org.swallow.util.SwtConstants;
import org.swallow.util.SwtUtil;
import org.swallow.util.SystemFormats;
import org.swallow.work.model.Sweep;
import org.swallow.work.model.SweepQueueDetailVO;
import org.swallow.work.service.SweepQueueManager;
import org.swallow.util.LabelValueBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.apache.struts2.convention.annotation.Result;
import org.apache.struts2.convention.annotation.AllowedMethods;
import org.apache.struts2.convention.annotation.Action;
import org.apache.struts2.ServletActionContext;
import org.swallow.util.struts.ActionMessages;
import org.swallow.util.struts.CustomActionSupport;

/**
 * <AUTHOR> Balaji .A
 * 
 * Class to handle the sweep cancel queue related operations
 */
@Action(value = "/sweepcancelqueue", results = {
	@Result(name = "cancel", location = "/jsp/work/sweepqueuecancel.jsp"),
	@Result(name = "fail", location = "/error.jsp"),
})

@AllowedMethods ({"display" ,"displayList" ,"submit" ,"next" })
public class SweepCancelQueueAction extends CustomActionSupport {
public String execute() throws Exception {
    HttpServletRequest request = ServletActionContext.getRequest();

    // List of methods 
    String method = String.valueOf(request.getParameter("method"));

    switch (method) {
        case "unspecified":
            return unspecified();
        case "display":
            return display();
        case "displayList":
            return displayList();
        case "submit":
            return submit();
        case "next":
            return next();
        default:
            break;
    }

    return unspecified();
}


private Sweep sweepQueue;
public Sweep getSweepQueue() {
	if (sweepQueue == null) {
		sweepQueue = new Sweep();
	}
	return sweepQueue;
}
public void setSweepQueue(Sweep sweepQueue) {
	this.sweepQueue = sweepQueue;
	HttpServletRequest request = ServletActionContext.getRequest();
	request.setAttribute("sweepQueue", sweepQueue);
}


	// Misc Param to added the entityId field in table and check the entityId
	@Autowired
private SweepQueueManager sweepQueueManager = null;

	/**
	 * Comment for <code>log</code>
	 */
	private final Log log = LogFactory.getLog(SweepCancelQueueAction.class);

	/**
	 * Method invoked when no method name has been specified for invoking a
	 * request
	 * 
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return a Result of type Action forward
	 */
	public String unspecified()
			throws SwtException {
HttpServletRequest request = ServletActionContext.getRequest();
HttpServletResponse response = ServletActionContext.getResponse();

		return display();
	}

	/**
	 * display method is used to display the sweeps that are ready for
	 * cancelling. This method fetches sweeps for the default currency group of
	 * the logged in user.
	 * 
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return
	 * @throws SwtException
	 */
	@SuppressWarnings("unchecked")
	public String display()
			throws SwtException {
HttpServletRequest request = ServletActionContext.getRequest();
HttpServletResponse response = ServletActionContext.getResponse();

		/* Variable Declaration for forward */
		String forward = "cancel";
		/* Variable Declaration for currentFilter */
		String currentFilter = null;
		/* Variable Declaration for currentSort */
		String currentSort = null;
		/* Variable Declaration for filterSortStatus */
		String filterSortStatus = null;
		/* Variable Declaration for currentPage */
		int currentPage = 0;
		/* Variable Declaration for initialPageCount */
		int initialPageCount = 0;
		/* Variable Declaration for isNext */
		boolean isNext = false;
		/* Variable Declaration for maxPage */
		int maxPage = 0;
		/* Variable Declaration for pSummary */
		PageDetails pSummary = null;
		ArrayList<PageDetails> pageSummaryList = null;
		/* Variable Declaration for initialCurrencyCode */
		String initialCurrencyCode = null;
		/* Variable Declaration for totalCount */
		int totalCount = 0;
		/* Variable Declaration for sweepDetailListAll */
		ArrayList sweepDetailListAll = null;
		/* Variable Declaration for otherDetailListAll */
		ArrayList otherDetailListAll = null;
		/* Variable Declaration for dyForm */
		// DynaValidatorForm dyForm = null;
		/* Variable Declaration for session */
		HttpSession session = null;
		/* Variable Declaration for queueName */
		String queueName = null;
		/* Variable Declaration for currGrpId */
		String currGrpId = null;
		/* Variable Declaration for roleId */
		String roleId = null;
		/* Variable Declaration for entityId */
		String entityId = null;
		// Currency Group set to 0 by default
		// Variable Declaration for currencyGrpAccess
		int currencyGrpAccess = 0;
		// Variable Declaration for currGrpCounter
		int currGrpCounter = 0;
		// Variable Declaration for currGrpAccess
		int currGrpAccess = 0;
		// Variable Declaration for cancelViewCurrGrpId
		String cancelViewCurrGrpId = null;
		// Variable Declaration for systemFormats
		SystemFormats systemFormats = null;
		// Variable Declaration for sweepForm
		Sweep sweepForm = null;
		// Variable Declaration for sweep
		Sweep sweep = null;
		// Variable Declaration for sweepQueueDetailVO
		SweepQueueDetailVO sweepQueueDetailVO = null;
		// Variable Declaration for groupList
		Collection groupList = null;
		// Variable Declaration for itGroupList
		Iterator itGroupList = null;
		// Variable Declaration for temp
		ArrayList temp = null;
		// Variable Declaration for entityCurrencyGroupAccess
		EntityCurrencyGroupAccess entityCurrencyGroupAccess = null;
		// Variable Declaration for tempItr
		Iterator tempItr = null;
		try {
			log
					.debug(this.getClass().getName() + " - [display()] - "
							+ "Entry");
			// ArrayLists added for Currency Group 'All' case
			sweepDetailListAll = new ArrayList();
			otherDetailListAll = new ArrayList();
			session = request.getSession();
			queueName = (String) request.getParameter("queueName");
			// This condition is cheks thant the opened Screen either Cancel
			// queue or other Screen
			if (SwtConstants.SWEEP_STATUS_CANCEL.equalsIgnoreCase(queueName)) {

				forward = "cancel";
				// Read the selected filter from the request and store in String
				// variable
				currentFilter = request.getParameter("selectedFilter");
				// Read the selectedSort from the request and store in String
				// variable
				currentSort = request.getParameter("selectedSort");
				/* Condition to check currentFilter is null */
				if (currentFilter == null
						|| currentFilter.equals(SwtConstants.EMPTY_STRING)) {
					// If current filter is null the default value 'All' is set
					// for currentFilter
					currentFilter = "all";
				}

				/* Condition to check currentSort is null */
				if (currentSort == null
						|| currentSort.equals(SwtConstants.EMPTY_STRING)) {
					/* If currentSort is null then set current sort as none. */
					currentSort = "none";
				}

				// Use # as separator for the combined current filter and
				// current sort
				filterSortStatus = currentFilter + "#" + currentSort;
				currentPage = 1;
				initialPageCount = 0;
				isNext = false;
			}
			systemFormats = new SystemFormats();
			// Setting Currency Formats
			systemFormats.setCurrencyFormat(SwtUtil
					.getCurrentCurrencyFormat(request.getSession()));
			// Setiing Date Formats
			systemFormats.setDateFormatValue(SwtUtil
					.getCurrentDateFormat(request.getSession()));

			sweepForm = (Sweep) getSweepQueue();
			sweepForm.setEntityId(
					SwtUtil.getUserCurrentEntity(request.getSession()));

			// Get the user's default currency group
			// Value set to default Currency Grp

			currGrpId = ((CommonDataManager) session
					.getAttribute(SwtConstants.CDM_BEAN)).getUser()
					.getCurrentCcyGrpId();

			roleId = getRoleId(request);
			entityId = SwtUtil.getUserCurrentEntity(request.getSession());
			if (request.getParameter("entityId") != null) {
				entityId = request.getParameter("entityId");
				currGrpId = request.getParameter("currCode");
				sweepForm.setEntityId(entityId);
			}
			// Getting Currency Group Access
			currGrpAccess = SwtUtil.getSwtMaintenanceCache()
					.getCurrencyGroupAccess(roleId, entityId, currGrpId);

			if (currGrpAccess == SwtConstants.CURRENCYGRP_NO_ACCESS) {
				currGrpId = "All";
			}

			// Identifies the screen displayed - Submit or Authorise.
			sweepForm.setQueueName(queueName);
			sweepForm.setCurrencyCode(currGrpId);
			setSweepQueue(sweepForm);

			sweep = new Sweep();
			sweep.getId().setHostId(
					SwtUtil.getCurrentHostId(request.getSession()));
			sweep
					.setRequestUser(SwtUtil.getCurrentUserId(request
							.getSession()));
			sweep.setRequestRole(SwtUtil.getCurrentUser(request.getSession())
					.getRoleId());
			sweep.setEntityId(entityId);
			sweep.setCurrencyCode(currGrpId);
			sweep.setAccountType("All");
			sweep.setQueueName(queueName);
			initialCurrencyCode = currGrpId;
			sweepQueueDetailVO = new SweepQueueDetailVO();
			sweepQueueDetailVO.setEntityAccessList(SwtUtil
					.getUserEntityAccessList(request.getSession()));
			SwtUtil.getMenuEntityCurrGrpAccessWithoutCurrency(request,
					entityId, currGrpId);
			// Code added for Currency Group 'All' case
			if (currGrpId.equalsIgnoreCase(SwtConstants.ALL_VALUE)) {
				groupList = SwtUtil.getSwtMaintenanceCache()
						.getCurrencyGroupViewORFullAcess(roleId, entityId);
				itGroupList = groupList.iterator();
				temp = new ArrayList();

				currGrpId = "";
				/*
				 * Start:Code Modified by Balaji for Mantis 1483 on
				 * 8-JUN-2011:For Sweep Cancel Queue Screen displays error when
				 * the last currency group in Currency group Access screen is
				 * set to View Access
				 */
				// Appending commas on currency group Id's
				while (itGroupList.hasNext()) {
					entityCurrencyGroupAccess = (EntityCurrencyGroupAccess) itGroupList
							.next();
					if (entityCurrencyGroupAccess.getAccess() == 0) {
						if (currGrpCounter > 0) {
							currGrpId += ",'"
									+ entityCurrencyGroupAccess
											.getCurrencyGroupId() + "'";
						} else {
							currGrpId += "'"
									+ entityCurrencyGroupAccess
											.getCurrencyGroupId() + "'";
						}
						currGrpCounter++;
					}
				}

				/*
				 * End:Code Modified by Balaji on 8-JUN-2011 for Mantis 1483:For
				 * Sweep Cancel Queue Screen displays error when the last
				 * currency group in Currency group Access screen is set to View
				 * Access
				 */
				if (currGrpId.equals(SwtConstants.EMPTY_STRING)) {
					currGrpId = "''";
				}
				sweep.setCurrencyCode(currGrpId);

				itGroupList = groupList.iterator();
				// Intializing cancelViewCurrGrpId as empty
				cancelViewCurrGrpId = "";
				// This following condition ids used to get currency group which
				// are having access.
				while (itGroupList.hasNext()) {

					entityCurrencyGroupAccess = (EntityCurrencyGroupAccess) itGroupList
							.next();
					if (itGroupList.hasNext())
						cancelViewCurrGrpId += "'"
								+ entityCurrencyGroupAccess
										.getCurrencyGroupId() + "',";
					else
						cancelViewCurrGrpId += "'"
								+ entityCurrencyGroupAccess
										.getCurrencyGroupId() + "'";
				}
				if (cancelViewCurrGrpId.equals(SwtConstants.EMPTY_STRING)) {
					cancelViewCurrGrpId = "''";
				}

				sweep.setCancelViewCurrGrpId(cancelViewCurrGrpId);

				sweepQueueDetailVO.setEntityAccessList(SwtUtil
						.getUserEntityAccessList(request.getSession()));
				sweepQueueDetailVO.setSweepDetailList(new ArrayList());
				sweepQueueDetailVO.setOtherDetailList(new ArrayList());

				currencyGrpAccess = SwtUtil.getSwtMaintenanceCache()
						.getCurrencyGroupAccess(roleId, entityId, currGrpId);
				// getiing no of counts of records having and setting it to
				// total count variable
				totalCount = sweepQueueManager.getSweepQueueDetail(sweep,
						roleId, currencyGrpAccess, sweepQueueDetailVO,
						currentPage, initialPageCount, filterSortStatus,
						systemFormats);
				request.setAttribute("totalCount", totalCount);
				maxPage = setMaxPageAttribute(
						totalCount,
						SwtUtil.getPageSizeFromProperty(SwtConstants.SWEEP_CANCEL_QUEUE_PAGE_SIZE));
				sweep.setCurrencyCode(initialCurrencyCode);
				temp = (ArrayList) sweepQueueDetailVO.getSweepDetailList();

				tempItr = temp.iterator();

				while (tempItr.hasNext())
					sweepDetailListAll.add(tempItr.next());

				temp = (ArrayList) sweepQueueDetailVO.getOtherDetailList();
				tempItr = temp.iterator();

				while (tempItr.hasNext())
					otherDetailListAll.add(tempItr.next());

				sweep.setCurrencyCode("All");
			}
			// This else condition is for Submit queue and authhorized queue
			else {
				sweep.setCurrencyCode("'" + initialCurrencyCode + "'");
				sweep.setCancelViewCurrGrpId("'" + initialCurrencyCode + "'");
				totalCount = sweepQueueManager.getSweepQueueDetail(sweep,
						roleId, currencyGrpAccess, sweepQueueDetailVO,
						currentPage, initialPageCount, filterSortStatus,
						systemFormats);
				request.setAttribute("totalCount", totalCount);
				maxPage = setMaxPageAttribute(
						totalCount,
						SwtUtil.getPageSizeFromProperty(SwtConstants.SWEEP_CANCEL_QUEUE_PAGE_SIZE));
				sweepDetailListAll = (ArrayList) sweepQueueDetailVO
						.getSweepDetailList();

				otherDetailListAll = (ArrayList) sweepQueueDetailVO
						.getOtherDetailList();
			}

			sweep.setCurrencyCode(initialCurrencyCode);
			putEntityListInReq(request);
			putCurrencyGroupListInReq(request, sweep.getId().getHostId(), sweep
					.getEntityId());
			putAccountListInReq(request, entityId);
			request.setAttribute("AccountDesp", SwtConstants.ALL_ACCOUNTS_TYPE);

			pageSummaryList = new ArrayList<PageDetails>();
			setPageSummaryList(currentPage, maxPage, totalCount,
					pageSummaryList);

			/* Condition checked to enable or disable the next link */
			if (maxPage > 1) {
				// If true the isNext flag is set to true which is set to
				// nextEnabled request attribute which is used in UI to
				// enable the next link.
				isNext = true;
			}

			/* Condition checked for current sort equal to none */
			if ("none".equals(currentSort)) {
				// If the condition return true the default value for sort
				// is assigned for first column ascending
				currentSort = "0|true"; // default sorting column
			}

			// Hide pagination if no of records are "0" or records can be
			// displayed in one page.
			if (maxPage <= 1) {
				request.setAttribute("hidePagination", true);
			}

			request.setAttribute("currentPage", Integer.toString(currentPage));
			request.setAttribute("prevEnabled", "false");
			request.setAttribute("nextEnabled", "" + isNext);
			request.setAttribute("maxPage", Integer.toString(maxPage));
			request.setAttribute("selectedFilter", currentFilter);
			request.setAttribute("selectedSort", currentSort);
			request.setAttribute("pageSummaryList", pageSummaryList);
			request.setAttribute("lastRefTime", SwtUtil.getLastRefTimeOnGMTOffset(request,
					entityId));
			// Code added for Currency Group 'All' case - attributes set in
			// request
			request.setAttribute("sweepDetailList", sweepDetailListAll);
			request.setAttribute("othersDetailList", otherDetailListAll);
			log.debug(this.getClass().getName() + " - [display()] - " + "Exit");
			return forward;
		} catch (SwtException swtexp) {
			log
					.error("SwtException Catch in SweepCancelQueueAction.'display' method : "
							+ swtexp.getMessage());
			SwtUtil.logException(swtexp, request, "");

			return ("fail");
		} catch (Exception exp) {
			log
					.error("Exception Catch in SweepCancelQueueAction.'display' method : "
							+ exp.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "display", SweepCancelQueueAction.class), request, "");

			return ("fail");
		} finally {
			currentFilter = null;
			currentSort = null;
			filterSortStatus = null;
		}
	}

	/**
	 * displayList method fetches the list of sweeps based on the user chosen
	 * criteria.
	 * 
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return
	 * @throws SwtException
	 */
	@SuppressWarnings("unchecked")
	public String displayList()
			throws SwtException {
HttpServletRequest request = ServletActionContext.getRequest();
HttpServletResponse response = ServletActionContext.getResponse();

		/* Variable Declaration for sweep */
		Sweep sweep = null;
		/* Variable Declaration for dyForm */
		// DynaValidatorForm dyForm = null;
		/* Variable Declaration for forward */
		String forward = "cancel";
		/* Variable Declaration for currentFilter */
		String currentFilter = null;
		/* Variable Declaration for currentSort */
		String currentSort = null;
		/* Variable Declaration for filterSortStatus */
		String filterSortStatus = null;
		/* Variable Declaration for currentPage */
		int currentPage = 0;
		/* Variable Declaration for initialPageCount */
		int initialPageCount = 0;
		/* Variable Declaration for isNext */
		boolean isNext = false;
		/* Variable Declaration for maxPage */
		int maxPage = 0;
		/* Variable Declaration for pSummary */

		ArrayList<PageDetails> pageSummaryList = null;
		/* Variable Declaration for initialCurrencyCode */
		String initialCurrencyCode = null;
		// Variable Declaration for sweepDetailListAll
		ArrayList sweepDetailListAll = null;
		// Variable Declaration for otherDetailListAll
		ArrayList otherDetailListAll = null;

		// Variable Declaration for session
		HttpSession session = null;
		// Variable Declaration for currencyGrpAccess
		int currencyGrpAccess = 0;
		// Variable Declaration for systemFormats
		SystemFormats systemFormats = new SystemFormats();
		// Variable Declaration for totalCount
		int totalCount = 0;
		// Variable Declaration for currGrpId
		String currGrpId = null;
		// Variable Declaration for roleId
		String roleId = null;
		// Variable Declaration for entityId
		String entityId = null;
		// Variable Declaration for currGrpCounter
		int currGrpCounter = 0;
		// Variable Declaration for cancelViewCurrGrpId
		String cancelViewCurrGrpId = null;
		// Variable Declaration for sweepQueueDetailVO
		SweepQueueDetailVO sweepQueueDetailVO = null;
		// Variable Declaration for roleObject
		RoleTO roleObject = null;
		// Variable Declaration for entityList
		Collection entityList = null;
		// Variable Declaration for itr
		Iterator itr = null;
		// Variable Declaration for itGroupList
		Iterator itGroupList = null;
		// Variable Declaration for temp
		ArrayList temp = null;
		// Variable Declaration for tempItr
		Iterator tempItr = null;
		// Variable Declaration for entityCurrencyGroupAccess
		EntityCurrencyGroupAccess entityCurrencyGroupAccess = null;
		try {
			log.debug(this.getClass().getName() + " - [displayList()] - "
					+ "Entry");
			// Read the selected filter from the request and store in String
			// variable
			currentFilter = request.getParameter("selectedFilter");
			// Read the selectedSort from the request and store in String
			// variable
			currentSort = request.getParameter("selectedSort");
			/* Condition to check currentFilter is null */
			if (currentFilter == null
					|| currentFilter.equals(SwtConstants.EMPTY_STRING)) {
				// If current filter is null the default value 'All' is set
				// for currentFilter
				currentFilter = "all";
			}

			/* Condition to check currentSort is null */
			if (currentSort == null
					|| currentSort.equals(SwtConstants.EMPTY_STRING)) {
				/* If currentSort is null then set current sort as none. */
				currentSort = "none";
			}

			// Use # as separator for the combined current filter and
			// current sort
			filterSortStatus = currentFilter + "#" + currentSort;
			currentPage = 1;
			initialPageCount = 0;
			isNext = false;

			systemFormats.setCurrencyFormat(SwtUtil
					.getCurrentCurrencyFormat(request.getSession()));
			systemFormats.setDateFormatValue(SwtUtil
					.getCurrentDateFormat(request.getSession()));
			sweep = (Sweep) getSweepQueue();

			sweep.getId().setHostId(
					SwtUtil.getCurrentHostId(request.getSession()));
			sweep
					.setRequestUser(SwtUtil.getCurrentUserId(request
							.getSession()));

			sweep.setRequestRole(SwtUtil.getCurrentUser(request.getSession())
					.getRoleId());

			if (sweep.getAccountType().equalsIgnoreCase("C")) {
				request.setAttribute("AccountDesp",
						SwtConstants.CASH_ACCOUNTS_TYPE);
			}

			if (sweep.getAccountType().equalsIgnoreCase("U")) {
				request.setAttribute("AccountDesp",
						SwtConstants.CUSTODIAN_ACCOUNTS_TYPE);
			}

			if (sweep.getAccountType().equalsIgnoreCase("All")) {
				request.setAttribute("AccountDesp",
						SwtConstants.ALL_ACCOUNTS_TYPE);
			}

			putEntityListInReq(request);

			putCurrencyGroupListInReq(request, sweep.getId().getHostId(), sweep
					.getEntityId());
			putAccountListInReq(request, sweep.getEntityId());

			sweepQueueDetailVO = new SweepQueueDetailVO();
			sweepDetailListAll = new ArrayList();
			otherDetailListAll = new ArrayList();

			sweepQueueDetailVO.setEntityAccessList(SwtUtil
					.getUserEntityAccessList(request.getSession()));

			roleId = getRoleId(request);
			entityId = sweep.getEntityId();

			session = request.getSession();
			if ((!entityId.equalsIgnoreCase(SwtUtil
					.getUserCurrentEntity(request.getSession())))
					&& sweep.getCurrencyCode().equalsIgnoreCase(
							((CommonDataManager) session
									.getAttribute(SwtConstants.CDM_BEAN))
									.getUser().getCurrentCcyGrpId())) {
				currGrpId = "All";

			} else {
				currGrpId = sweep.getCurrencyCode();
			}
			SwtUtil.getMenuEntityCurrGrpAccessWithoutCurrency(request,
					entityId, currGrpId);
			initialCurrencyCode = currGrpId;
			// code added to handle Entity as All starts
			if (entityId.equalsIgnoreCase(SwtConstants.ALL_VALUE)) {
				maxPage = 0;
				roleObject = new RoleTO(roleId);
				entityList = SwtUtil.getSwtMaintenanceCache()
						.getEntityAccessCollection(roleObject);
				itr = entityList.iterator();
				// Getting all the details of the entity
				while (itr.hasNext()) {

					currGrpId = sweep.getCurrencyCode();

					EntityUserAccess entityUserAccess = (EntityUserAccess) itr
							.next();
					entityId = entityUserAccess.getEntityId();

					sweep.setEntityId(entityId);

					if (currGrpId.equalsIgnoreCase(SwtConstants.ALL_VALUE)) {
						currencyGrpAccess = 0;
					} else {
						currencyGrpAccess = SwtUtil.getSwtMaintenanceCache()
								.getCurrencyGroupAccess(roleId, entityId,
										currGrpId);
					}

					if (currGrpId.equalsIgnoreCase(SwtConstants.ALL_VALUE)) {
						Collection groupList = SwtUtil.getSwtMaintenanceCache()
								.getCurrencyGroupViewORFullAcess(roleId,
										entityId);
						itGroupList = groupList.iterator();
						temp = new ArrayList();
						currGrpId = "";
						/*
						 * Start:Code Modified by Balaji for Mantis 1483 on
						 * 8-JUN-2011:For Sweep Cancel Queue Screen displays
						 * error when the last currency group in Currency group
						 * Access screen is set to View Access
						 */
						// This while loop is for gennerating currecy group
						// access for cancel panel
						while (itGroupList.hasNext()) {
							entityCurrencyGroupAccess = (EntityCurrencyGroupAccess) itGroupList
									.next();
							// If condition is for appending qouation and commas
							// for currency group access
							if (entityCurrencyGroupAccess.getAccess() == 0) {
								if (currGrpCounter > 0) {
									currGrpId += ",'"
											+ entityCurrencyGroupAccess
													.getCurrencyGroupId() + "'";
								} else {
									currGrpId += "'"
											+ entityCurrencyGroupAccess
													.getCurrencyGroupId() + "'";
								}
							}
							currGrpCounter++;
						}
						/*
						 * End:Code Modified by Balaji for Mantis 1483 on
						 * 8-JUN-2011:For Sweep Cancel Queue Screen displays
						 * error when the last currency group in Currency group
						 * Access screen is set to View Access
						 */
						if (currGrpId.equals(SwtConstants.EMPTY_STRING)) {
							currGrpId = "''";
						}
						sweep.setCurrencyCode(currGrpId);
						// Intialzing cancelViewCurrGrpId as empty
						cancelViewCurrGrpId = "";
						itGroupList = groupList.iterator();
						// This while loop is for gennerating currecy group
						// access for view panel
						while (itGroupList.hasNext()) {
							entityCurrencyGroupAccess = (EntityCurrencyGroupAccess) itGroupList
									.next();
							// If condition is for appending qouation and commas
							// for currency group access
							if (itGroupList.hasNext())
								cancelViewCurrGrpId += "'"
										+ entityCurrencyGroupAccess
												.getCurrencyGroupId() + "',";
							else
								cancelViewCurrGrpId += "'"
										+ entityCurrencyGroupAccess
												.getCurrencyGroupId() + "'";
						}
						if (cancelViewCurrGrpId
								.equals(SwtConstants.EMPTY_STRING)) {
							cancelViewCurrGrpId = "''";
						}
						// Setting value in bean
						sweep.setCancelViewCurrGrpId(cancelViewCurrGrpId);
						sweepQueueDetailVO.setEntityAccessList(SwtUtil
								.getUserEntityAccessList(request.getSession()));
						sweepQueueDetailVO.setSweepDetailList(new ArrayList());
						sweepQueueDetailVO.setOtherDetailList(new ArrayList());

						currencyGrpAccess = SwtUtil.getSwtMaintenanceCache()
								.getCurrencyGroupAccess(roleId, entityId,
										currGrpId);
						// Setting count records will available in the grid
						maxPage += sweepQueueManager.getSweepQueueDetail(sweep,
								roleId, currencyGrpAccess, sweepQueueDetailVO,
								currentPage, initialPageCount,
								filterSortStatus, systemFormats);

						temp = (ArrayList) sweepQueueDetailVO
								.getSweepDetailList();

						tempItr = temp.iterator();

						while (tempItr.hasNext())
							sweepDetailListAll.add(tempItr.next());

						temp = (ArrayList) sweepQueueDetailVO
								.getOtherDetailList();
						tempItr = temp.iterator();

						while (tempItr.hasNext())
							otherDetailListAll.add(tempItr.next());
						sweep.setCurrencyCode("All");
					} else {
						temp = new ArrayList();
						sweep.setCurrencyCode("'" + initialCurrencyCode + "'");
						sweep.setCancelViewCurrGrpId("'" + initialCurrencyCode
								+ "'");
						maxPage += sweepQueueManager.getSweepQueueDetail(sweep,
								roleId, currencyGrpAccess, sweepQueueDetailVO,
								currentPage, initialPageCount,
								filterSortStatus, systemFormats);
						temp = (ArrayList) sweepQueueDetailVO
								.getSweepDetailList();

						tempItr = temp.iterator();

						while (tempItr.hasNext())
							sweepDetailListAll.add(tempItr.next());

						temp = (ArrayList) sweepQueueDetailVO
								.getOtherDetailList();
						tempItr = temp.iterator();

						while (tempItr.hasNext())
							otherDetailListAll.add(tempItr.next());
					}
				}
				request.setAttribute("totalCount", maxPage);
				maxPage = setMaxPageAttribute(
						maxPage,
						SwtUtil.getPageSizeFromProperty(SwtConstants.SWEEP_CANCEL_QUEUE_PAGE_SIZE));
				sweep.setEntityId("All");
			} else {
				if (currGrpId.equalsIgnoreCase(SwtConstants.ALL_VALUE)) {
					currencyGrpAccess = 0;
				} else {
					currencyGrpAccess = SwtUtil
							.getSwtMaintenanceCache()
							.getCurrencyGroupAccess(roleId, entityId, currGrpId);
				}
				if (currGrpId.equalsIgnoreCase(SwtConstants.ALL_VALUE)) {
					Collection groupList = SwtUtil.getSwtMaintenanceCache()
							.getCurrencyGroupViewORFullAcess(roleId, entityId);
					itGroupList = groupList.iterator();
					temp = new ArrayList();

					currGrpId = "";
					/*
					 * Start:Code Modified by Balaji for Mantis 1483 on
					 * 8-JUN-2011:For Sweep Cancel Queue Screen displays error
					 * when the last currency group in Currency group Access
					 * screen is set to View Access
					 */
					currGrpCounter = 0;
					// This while loop is for gennerating currecy group
					// access for cancel panel

					while (itGroupList.hasNext()) {
						entityCurrencyGroupAccess = (EntityCurrencyGroupAccess) itGroupList
								.next();
						// If condition is for appending qouation and commas
						// for currency group access
						if (entityCurrencyGroupAccess.getAccess() == 0) {
							if (currGrpCounter > 0) {
								currGrpId += ",'"
										+ entityCurrencyGroupAccess
												.getCurrencyGroupId() + "'";
							} else {
								currGrpId += "'"
										+ entityCurrencyGroupAccess
												.getCurrencyGroupId() + "'";
							}
						}
						currGrpCounter++;
					}
					/*
					 * End:Code Modified by Balaji for Mantis 1483 on
					 * 8-JUN-2011:For Sweep Cancel Queue Screen displays error
					 * when the last currency group in Currency group Access
					 * screen is set to View Access
					 */
					if (currGrpId.equals(SwtConstants.EMPTY_STRING)) {
						currGrpId = "''";
					}
					sweep.setCurrencyCode(currGrpId);
					// Intialzing cancelViewCurrGrpId as empty
					cancelViewCurrGrpId = "";
					itGroupList = groupList.iterator();
					// This while loop is for gennerating currecy group
					// access for view panel

					while (itGroupList.hasNext()) {
						entityCurrencyGroupAccess = (EntityCurrencyGroupAccess) itGroupList
								.next();
						// If condition is for appending qouation and commas
						// for currency group access
						if (itGroupList.hasNext())
							cancelViewCurrGrpId += "'"
									+ entityCurrencyGroupAccess
											.getCurrencyGroupId() + "',";
						else
							cancelViewCurrGrpId += "'"
									+ entityCurrencyGroupAccess
											.getCurrencyGroupId() + "'";
					}
					if (cancelViewCurrGrpId.equals(SwtConstants.EMPTY_STRING)) {
						cancelViewCurrGrpId = "''";
					}
					sweep.setCancelViewCurrGrpId(cancelViewCurrGrpId);
					sweepQueueDetailVO.setEntityAccessList(SwtUtil
							.getUserEntityAccessList(request.getSession()));
					sweepQueueDetailVO.setSweepDetailList(new ArrayList());

					sweepQueueDetailVO.setOtherDetailList(new ArrayList());
					currencyGrpAccess = SwtUtil
							.getSwtMaintenanceCache()
							.getCurrencyGroupAccess(roleId, entityId, currGrpId);
					// getting count of records from manager class
					totalCount = sweepQueueManager.getSweepQueueDetail(sweep,
							roleId, currencyGrpAccess, sweepQueueDetailVO,
							currentPage, initialPageCount, filterSortStatus,
							systemFormats);
					request.setAttribute("totalCount", totalCount);
					maxPage = setMaxPageAttribute(
							totalCount,
							SwtUtil.getPageSizeFromProperty(SwtConstants.SWEEP_CANCEL_QUEUE_PAGE_SIZE));
					temp = (ArrayList) sweepQueueDetailVO.getSweepDetailList();
					tempItr = temp.iterator();
					while (tempItr.hasNext())
						sweepDetailListAll.add(tempItr.next());
					temp = (ArrayList) sweepQueueDetailVO.getOtherDetailList();
					tempItr = temp.iterator();
					while (tempItr.hasNext())
						otherDetailListAll.add(tempItr.next());
					sweep.setCurrencyCode("All");
				} else {
					sweep.setCurrencyCode("'" + initialCurrencyCode + "'");
					sweep.setCancelViewCurrGrpId("'" + initialCurrencyCode
							+ "'");
					totalCount = sweepQueueManager.getSweepQueueDetail(sweep,
							roleId, currencyGrpAccess, sweepQueueDetailVO,
							currentPage, initialPageCount, filterSortStatus,
							systemFormats);
					request.setAttribute("totalCount", totalCount);
					maxPage = setMaxPageAttribute(
							totalCount,
							SwtUtil.getPageSizeFromProperty(SwtConstants.SWEEP_CANCEL_QUEUE_PAGE_SIZE));
					sweepDetailListAll = (ArrayList) sweepQueueDetailVO
							.getSweepDetailList();
					otherDetailListAll = (ArrayList) sweepQueueDetailVO
							.getOtherDetailList();
				}
			}
			sweep.setCurrencyCode(initialCurrencyCode);
			setSweepQueue(sweep);
			pageSummaryList = new ArrayList<PageDetails>();
			setPageSummaryList(currentPage, maxPage, totalCount,
					pageSummaryList);

			/* Condition checked to enable or disable the next link */
			if (maxPage > 1) {
				// If true the isNext flag is set to true which is set to
				// nextEnabled request attribute which is used in UI to
				// enable the next link.
				isNext = true;
			}

			/* Condition checked for current sort equal to none */
			if ("none".equals(currentSort)) {
				// If the condition return true the default value for sort
				// is assigned for first column ascending
				currentSort = "0|true"; // default sorting column
			}
			// Hide pagination if no of records are "0" or records can be
			// displayed in one page.
			if (maxPage <= 1) {
				request.setAttribute("hidePagination", true);
			}

			request.setAttribute("currentPage", Integer.toString(currentPage));
			request.setAttribute("prevEnabled", "false");
			request.setAttribute("nextEnabled", "" + isNext);
			request.setAttribute("maxPage", Integer.toString(maxPage));
			request.setAttribute("selectedFilter", currentFilter);
			request.setAttribute("selectedSort", currentSort);
			request.setAttribute("pageSummaryList", pageSummaryList);
			request.setAttribute("sweepDetailList", sweepDetailListAll);
			request.setAttribute("othersDetailList", otherDetailListAll);
			request.setAttribute("lastRefTime", SwtUtil.getLastRefTimeOnGMTOffset(request,
					entityId));

			if (SwtConstants.SWEEP_STATUS_CANCEL.equalsIgnoreCase(sweep
					.getQueueName())) {
				forward = "cancel";
			}
			log.debug(this.getClass().getName() + " - [displayList()] - "
					+ "Exit");
			return forward;
		} catch (SwtException swtexp) {
			log
					.error("SwtException Catch in SweepCancelQueueAction.'displayList' method : "
							+ swtexp.getMessage());
			saveErrors(request, SwtUtil.logException(swtexp, request, ""));
			setSweepQueue(sweep);
			return ("cancel");
		} catch (Exception exp) {
			log
					.error("Exception Catch in SweepCancelQueueAction.'displayList' method : "
							+ exp.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "displayList", SweepCancelQueueAction.class), request,
					"");

			return ("fail");
		} finally {
			/* null the objects created already. */
			sweep = null;
			currentFilter = null;
			currentSort = null;
			filterSortStatus = null;
			initialCurrencyCode = null;
		}
	}

	/**
	 * Submit method is used to cancel a single sweep or the list of sweeps
	 * selected by the user.
	 * 
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return
	 * @throws SwtException
	 */
	@SuppressWarnings("unchecked")
	public String submit()
			throws SwtException {
HttpServletRequest request = ServletActionContext.getRequest();
HttpServletResponse response = ServletActionContext.getResponse();

		Sweep sweep = null;
		// DynaValidatorForm dyForm = null;
		SystemFormats systemFormats = null;
		ArrayList sweepDetailListAll = null;
		ArrayList otherDetailListAll = null;
		int currencyGrpAccess = -1;
		String sweeps = "";
		String currentFilter = "";
		String currentSort = "";
		String filterSortStatus = "";
		int currentPage = 0;
		int initialPageCount = 0;
		boolean isNext = false;
		int maxPage = 0;
		PageDetails pSummary;
		ArrayList<PageDetails> pageSummaryList = null;
		String initialCurrencyCode = null;
		// Variable declaration for totalcount
		int totalCount = 0;
		// Variable declaration for forward
		String forward = null;
		// Variable declaration for selectedlist
		String selectedlist = null;
		// Variable declaration for bypassChangedSweep
		String bypassChangedSweep = null;
		// Variable declaration for bypassCutOff
		String bypassCutOff = null;
		// Variable declaration for bypassAccountBreach
		String bypassAccountBreach = null;
		// Variable declaration for list
		// submit/authorize/cancel sweep
		ActionMessages list = null;
		// Variable declaration for sweepMsg
		String sweepMsg = null;
		// Variable declaration for start
		int start = 0;
		// Variable declaration for end
		int end = 0;
		// Variable declaration for currGrpId
		String currGrpId = null;
		// Variable declaration for roleId
		String roleId = null;
		// Variable declaration for entityId
		String entityId = null;
		// Variable declaration for entityList
		Collection entityList = null;
		// Variable declaration for cancelViewCurrGrpId
		String cancelViewCurrGrpId = null;
		// Variable declaration for temp
		ArrayList temp = null;
		// Variable declaration for groupList
		Collection groupList = null;
		// Variable declaration for currGrpCounter
		int currGrpCounter = 0;
		// Variable declaration for sweepQueueDetailVO
		SweepQueueDetailVO sweepQueueDetailVO = null;
		// Variable declaration for errorsType1
		Iterator errorsType1 = null;
		// Variable declaration for errorsType2
		Iterator errorsType2 = null;
		// Variable declaration for errorsType3
		Iterator errorsType3 = null;
		// Variable declaration for roleObject
		RoleTO roleObject = null;
		// Variable declaration for itGroupList
		Iterator itGroupList = null;
		// Variable declaration for entityCurrencyGroupAccess
		EntityCurrencyGroupAccess entityCurrencyGroupAccess = null;
		// Variable declaration for tempItr
		Iterator tempItr = null;
		try {
			log.debug(" Entering submit method");
			forward = "cancel";
			selectedlist = (String) request.getParameter("selectedList");
			// Read the selected filter from the request and store in String
			// variable
			currentFilter = request.getParameter("selectedFilter");
			// Read the selectedSort from the request and store in String
			// variable
			currentSort = request.getParameter("selectedSort");

			systemFormats = new SystemFormats();
			sweepDetailListAll = new ArrayList();
			otherDetailListAll = new ArrayList();

			/* Condition to check currentFilter is null */
			if (currentFilter == null
					|| currentFilter.equals(SwtConstants.EMPTY_STRING)) {
				// If current filter is null the default value 'All' is set
				// for currentFilter
				currentFilter = "all";
			}
			/* Condition to check currentSort is null */
			if (currentSort == null
					|| currentSort.equals(SwtConstants.EMPTY_STRING)) {
				/* If currentSort is null then set current sort as none. */
				currentSort = "none";
			}
			// Use # as separator for the combined current filter and
			// current sort
			filterSortStatus = currentFilter + "#" + currentSort;
			currentPage = 1;
			initialPageCount = 0;
			isNext = false;
			// Added to identify if the sweep is to be submitted
			// even if new sweep amount is changed.
			bypassChangedSweep = (String) request
					.getParameter("bypassChangedSweep");
			bypassCutOff = (String) request.getParameter("bypassCutOff");
			bypassAccountBreach = (String) request
					.getParameter("bypassAccountBreach");
			sweep = (Sweep) getSweepQueue();
			systemFormats.setCurrencyFormat(SwtUtil
					.getCurrentCurrencyFormat(request.getSession()));
			systemFormats.setDateFormatValue(SwtUtil
					.getCurrentDateFormat(request.getSession()));

			sweep.getId().setHostId(
					SwtUtil.getCurrentHostId(request.getSession()));
			sweep
					.setRequestUser(SwtUtil.getCurrentUserId(request
							.getSession()));
			sweep.setRequestRole(SwtUtil.getCurrentUser(request.getSession())
					.getRoleId());

			// If condition for canceling or submiting the queue
			if (SwtConstants.SWEEP_STATUS_CANCEL.equalsIgnoreCase(sweep
					.getQueueName())) {
				list = sweepQueueManager.cancel(sweep, selectedlist,
						systemFormats);
				forward = "cancel";
			} else {
				list = sweepQueueManager.submit(sweep, selectedlist,
						systemFormats, bypassChangedSweep, bypassCutOff,
						bypassAccountBreach);
			}
			// Checking if error is of type - SwtConstants.SWEEP_AMOUNT_CHANGED
			errorsType1 = list.get(SwtConstants.SWEEP_CUTOFF_EXCEEDED);

			errorsType2 = list.get(SwtConstants.SWEEP_AMOUNT_CHANGED);

			errorsType3 = list.get(SwtConstants.SWEEP_ACCOUNT_BREACHED);

			if (errorsType1.hasNext()) {
				// Setting this status will enable to call a pop-up with Yes and
				// No
				// as buttons along with error message.
				sweepMsg = errorsType1.next().toString();
				start = sweepMsg.indexOf("[");
				end = sweepMsg.indexOf("]");
				sweeps = sweepMsg.substring(start + 1, end);

				// Jsp will open a message box with yes/no options
				// if IS_ERROR_SWEEP_AMOUNT = "Y"
				request.setAttribute("IS_ERROR_CUT_OFF", "Y");
				// sets queueName in request
				request.setAttribute("queueName", sweep.getQueueName());
			}

			if (errorsType2.hasNext()) {
				// Setting this status will enable to call a pop-up with Yes and
				// No as buttons along with error message.
				sweepMsg = errorsType2.next().toString();
				start = sweepMsg.indexOf("[");
				end = sweepMsg.indexOf("]");
				if (sweeps.equals(""))
					sweeps = sweepMsg.substring(start + 1, end);
				else
					sweeps = sweeps + "," + sweepMsg.substring(start + 1, end);
				// Jsp will open a message box with yes/no options
				// if IS_ERROR_SWEEP_AMOUNT = "Y"
				request.setAttribute("IS_ERROR_SWEEP_AMOUNT", "Y");

				request.setAttribute("bypassCutOff", bypassCutOff);
				// sets queueName in request
				request.setAttribute("queueName", sweep.getQueueName());
			}

			if (errorsType3.hasNext()) {
				// Setting this status will enable to call a pop-up with Yes and
				// No as buttons along with error message.
				sweepMsg = errorsType3.next().toString();
				start = sweepMsg.indexOf("[");
				end = sweepMsg.indexOf("]");
				if (sweeps.equals(""))
					sweeps = sweepMsg.substring(start + 1, end);
				else
					sweeps = sweeps + "," + sweepMsg.substring(start + 1, end);
				// Jsp will open a message box with yes/no options
				// if IS_ERROR_SWEEP_AMOUNT = "Y"
				request.setAttribute("IS_ERROR_ACCOUNT_BREACH", "Y");
				// sets the sweep ids whose calculated amount <> queue amount
				// request.setAttribute("ERROR_SWEEPS", sweeps);
				request.setAttribute("bypassChangedSweep", bypassChangedSweep);
				request.setAttribute("bypassCutOff", bypassCutOff);
				// sets queueName in request
				request.setAttribute("queueName", sweep.getQueueName());
			}

			saveErrors(request, list);
			if (!sweeps.equals(""))
				request.setAttribute("ERROR_SWEEPS", sweeps);
			// after processing refresh the screen
			sweepQueueDetailVO = new SweepQueueDetailVO();
			sweepQueueDetailVO.setEntityAccessList(SwtUtil
					.getUserEntityAccessList(request.getSession()));
			putEntityListInReq(request);

			currGrpId = sweep.getCurrencyCode();
			initialCurrencyCode = currGrpId;
			roleId = getRoleId(request);
			entityId = sweep.getEntityId();

			if (!entityId.equalsIgnoreCase(SwtConstants.ALL_VALUE)
					&& !currGrpId.equalsIgnoreCase(SwtConstants.ALL_VALUE)) {
				currencyGrpAccess = SwtUtil.getSwtMaintenanceCache()
						.getCurrencyGroupAccess(roleId, entityId, currGrpId);
			}
			// code handling for Entity:All starts
			if (entityId.equalsIgnoreCase(SwtConstants.ALL_VALUE)) {
				totalCount = 0;
				roleObject = new RoleTO(roleId);
				entityList = SwtUtil.getSwtMaintenanceCache()
						.getEntityAccessCollection(roleObject);
				Iterator itr = entityList.iterator();

				while (itr.hasNext()) {
					currGrpId = sweep.getCurrencyCode();

					EntityUserAccess entityUserAccess = (EntityUserAccess) itr
							.next();
					entityId = entityUserAccess.getEntityId();

					sweep.setEntityId(entityId);

					if (currGrpId.equalsIgnoreCase(SwtConstants.ALL_VALUE)) {
						currencyGrpAccess = 0;
					} else {
						currencyGrpAccess = SwtUtil.getSwtMaintenanceCache()
								.getCurrencyGroupAccess(roleId, entityId,
										currGrpId);
					}

					if (currGrpId.equalsIgnoreCase(SwtConstants.ALL_VALUE)) {
						groupList = SwtUtil.getSwtMaintenanceCache()
								.getCurrencyGroupViewORFullAcess(roleId,
										entityId);
						itGroupList = groupList.iterator();
						temp = new ArrayList();
						currGrpId = "";
						/*
						 * Start:Code Modified by Balaji for Mantis 1483 on
						 * 8-JUN-2011:For Sweep Cancel Queue Screen displays
						 * error when the last currency group in Currency group
						 * Access screen is set to View Access
						 */
						// This while loop is for gennerating currecy group
						// access for cancel panel
						while (itGroupList.hasNext()) {
							entityCurrencyGroupAccess = (EntityCurrencyGroupAccess) itGroupList
									.next();
							// If condition is for appending qouation and commas
							// for currency group access
							if (entityCurrencyGroupAccess.getAccess() == 0) {
								if (currGrpCounter > 0) {
									currGrpId += ",'"
											+ entityCurrencyGroupAccess
													.getCurrencyGroupId() + "'";
								} else {
									currGrpId += "'"
											+ entityCurrencyGroupAccess
													.getCurrencyGroupId() + "'";
								}
							}
							currGrpCounter++;
						}
						/*
						 * End:Code Modified by Balaji for Mantis 1483 on for
						 * Mantis 1483 8-JUN-2011:For Sweep Cancel Queue Screen
						 * displays error when the last currency group in
						 * Currency group Access screen is set to View Access
						 */
						if (currGrpId.equals(SwtConstants.EMPTY_STRING)) {
							currGrpId = "''";
						}
						sweep.setCurrencyCode(currGrpId);
						// Intializing cancelViewCurrGrpId as empty
						cancelViewCurrGrpId = "";
						itGroupList = groupList.iterator();
						// This while loop is for gennerating currecy group
						// access for view panel

						while (itGroupList.hasNext()) {
							entityCurrencyGroupAccess = (EntityCurrencyGroupAccess) itGroupList
									.next();
							// If condition is for appending qouation and commas
							// for currency group access
							if (itGroupList.hasNext())
								cancelViewCurrGrpId += "'"
										+ entityCurrencyGroupAccess
												.getCurrencyGroupId() + "',";
							else
								cancelViewCurrGrpId += "'"
										+ entityCurrencyGroupAccess
												.getCurrencyGroupId() + "'";
						}
						if (cancelViewCurrGrpId
								.equals(SwtConstants.EMPTY_STRING)) {
							cancelViewCurrGrpId = "''";
						}
						sweep.setCancelViewCurrGrpId(cancelViewCurrGrpId);
						sweepQueueDetailVO.setEntityAccessList(SwtUtil
								.getUserEntityAccessList(request.getSession()));
						sweepQueueDetailVO.setSweepDetailList(new ArrayList());
						sweepQueueDetailVO.setOtherDetailList(new ArrayList());

						currencyGrpAccess = SwtUtil.getSwtMaintenanceCache()
								.getCurrencyGroupAccess(roleId, entityId,
										currGrpId);

						totalCount += sweepQueueManager.getSweepQueueDetail(
								sweep, roleId, currencyGrpAccess,
								sweepQueueDetailVO, currentPage,
								initialPageCount, filterSortStatus,
								systemFormats);
						temp = (ArrayList) sweepQueueDetailVO
								.getSweepDetailList();

						tempItr = temp.iterator();

						while (tempItr.hasNext()) {
							sweepDetailListAll.add(tempItr.next());
						}

						temp = (ArrayList) sweepQueueDetailVO
								.getOtherDetailList();
						tempItr = temp.iterator();

						while (tempItr.hasNext()) {
							otherDetailListAll.add(tempItr.next());
						}
						sweep.setCurrencyCode("All");
					} else {
						temp = new ArrayList();
						sweep.setCurrencyCode("'" + initialCurrencyCode + "'");
						sweep.setCancelViewCurrGrpId("'" + initialCurrencyCode
								+ "'");
						totalCount += sweepQueueManager.getSweepQueueDetail(
								sweep, roleId, currencyGrpAccess,
								sweepQueueDetailVO, currentPage,
								initialPageCount, filterSortStatus,
								systemFormats);

						temp = (ArrayList) sweepQueueDetailVO
								.getSweepDetailList();

						tempItr = temp.iterator();

						while (tempItr.hasNext())
							sweepDetailListAll.add(tempItr.next());

						temp = (ArrayList) sweepQueueDetailVO
								.getOtherDetailList();
						tempItr = temp.iterator();

						while (tempItr.hasNext())
							otherDetailListAll.add(tempItr.next());
					}
				}
				request.setAttribute("totalCount", totalCount);
				maxPage = setMaxPageAttribute(
						totalCount,
						SwtUtil.getPageSizeFromProperty(SwtConstants.SWEEP_CANCEL_QUEUE_PAGE_SIZE));
				sweep.setEntityId("All");

			} else {
				// code handling for Entity: All ends
				// code for handling of All currency group added starts
				if (currGrpId.equalsIgnoreCase(SwtConstants.ALL_VALUE)) {
					groupList = SwtUtil.getSwtMaintenanceCache()
							.getCurrencyGroupViewORFullAcess(roleId, entityId);
					itGroupList = groupList.iterator();
					temp = new ArrayList();

					currGrpId = "";
					/*
					 * Start:Code Modified by Balaji for Mantis 1483 on
					 * 8-JUN-2011:For Sweep Cancel Queue Screen displays error
					 * when the last currency group in Currency group Access
					 * screen is set to View Access
					 */
					currGrpCounter = 0;
					// This while loop is for gennerating currecy group
					// access for cancel panel

					while (itGroupList.hasNext()) {
						entityCurrencyGroupAccess = (EntityCurrencyGroupAccess) itGroupList
								.next();
						if (entityCurrencyGroupAccess.getAccess() == 0) {
							// If condition is for appending qouation and commas
							// for currency group access
							if (currGrpCounter > 0) {
								currGrpId += ",'"
										+ entityCurrencyGroupAccess
												.getCurrencyGroupId() + "'";
							} else {
								currGrpId += "'"
										+ entityCurrencyGroupAccess
												.getCurrencyGroupId() + "'";
							}
						}
						currGrpCounter++;
					}
					/*
					 * End:Code Modified by Balaji for Mantis 1483 on
					 * 8-JUN-2011:For Sweep Cancel Queue Screen displays error
					 * when the last currency group in Currency group Access
					 * screen is set to View Access
					 */
					if (currGrpId.equals(SwtConstants.EMPTY_STRING)) {
						currGrpId = "''";
					}
					sweep.setCurrencyCode(currGrpId);
					// Intializing cancelViewCurrGrpId as empty
					cancelViewCurrGrpId = "";
					itGroupList = groupList.iterator();
					// This while loop is for gennerating currecy group
					// access for view panel

					while (itGroupList.hasNext()) {
						entityCurrencyGroupAccess = (EntityCurrencyGroupAccess) itGroupList
								.next();
						// If condition is for appending qouation and commas
						// for currency group access
						if (itGroupList.hasNext())
							cancelViewCurrGrpId += "'"
									+ entityCurrencyGroupAccess
											.getCurrencyGroupId() + "',";
						else
							cancelViewCurrGrpId += "'"
									+ entityCurrencyGroupAccess
											.getCurrencyGroupId() + "'";
					}
					if (cancelViewCurrGrpId.equals(SwtConstants.EMPTY_STRING)) {
						cancelViewCurrGrpId = "''";
					}
					sweep.setCancelViewCurrGrpId(cancelViewCurrGrpId);
					sweepQueueDetailVO.setEntityAccessList(SwtUtil
							.getUserEntityAccessList(request.getSession()));
					sweepQueueDetailVO.setSweepDetailList(new ArrayList());
					sweepQueueDetailVO.setOtherDetailList(new ArrayList());

					currencyGrpAccess = SwtUtil
							.getSwtMaintenanceCache()
							.getCurrencyGroupAccess(roleId, entityId, currGrpId);

					totalCount = sweepQueueManager.getSweepQueueDetail(sweep,
							roleId, currencyGrpAccess, sweepQueueDetailVO,
							currentPage, initialPageCount, filterSortStatus,
							systemFormats);
					request.setAttribute("totalCount", totalCount);
					maxPage = setMaxPageAttribute(
							totalCount,
							SwtUtil.getPageSizeFromProperty(SwtConstants.SWEEP_CANCEL_QUEUE_PAGE_SIZE));
					temp = (ArrayList) sweepQueueDetailVO.getSweepDetailList();

					tempItr = temp.iterator();

					while (tempItr.hasNext())
						sweepDetailListAll.add(tempItr.next());

					temp = (ArrayList) sweepQueueDetailVO.getOtherDetailList();
					tempItr = temp.iterator();

					while (tempItr.hasNext())
						otherDetailListAll.add(tempItr.next());

					sweep.setCurrencyCode("All");
				} else {
					sweep.setCurrencyCode("'" + initialCurrencyCode + "'");
					sweep.setCancelViewCurrGrpId("'" + initialCurrencyCode
							+ "'");
					totalCount = sweepQueueManager.getSweepQueueDetail(sweep,
							roleId, currencyGrpAccess, sweepQueueDetailVO,
							currentPage, initialPageCount, filterSortStatus,
							systemFormats);
					request.setAttribute("totalCount", totalCount);
					maxPage = setMaxPageAttribute(
							totalCount,
							SwtUtil.getPageSizeFromProperty(SwtConstants.SWEEP_CANCEL_QUEUE_PAGE_SIZE));
					sweepDetailListAll = (ArrayList) sweepQueueDetailVO
							.getSweepDetailList();
					otherDetailListAll = (ArrayList) sweepQueueDetailVO
							.getOtherDetailList();
				}
			}
			sweep.setCurrencyCode(initialCurrencyCode);
			// code for handling of All currency group ends
			setSweepQueue(sweep);

			pageSummaryList = new ArrayList<PageDetails>();
			setPageSummaryList(currentPage, maxPage, totalCount,
					pageSummaryList);

			/* Condition checked to enable or disable the next link */
			if (maxPage > 1) {
				// If true the isNext flag is set to true which is set to
				// nextEnabled request attribute which is used in UI to
				// enable the next link.
				isNext = true;
			}

			/* Condition checked for current sort equal to none */
			if ("none".equals(currentSort)) {
				// If the condition return true the default value for sort
				// is assigned for first column ascending
				currentSort = "0|true"; // default sorting column
			}
			// Hide pagination if no of records are "0" or records can be
			// displayed in one page.
			if (maxPage <= 1) {
				request.setAttribute("hidePagination", true);
			}
			request.setAttribute("currentPage", Integer.toString(currentPage));
			request.setAttribute("prevEnabled", "false");
			request.setAttribute("nextEnabled", "" + isNext);
			request.setAttribute("maxPage", Integer.toString(maxPage));
			request.setAttribute("selectedFilter", currentFilter);
			request.setAttribute("selectedSort", currentSort);
			request.setAttribute("pageSummaryList", pageSummaryList);

			request.setAttribute("sweepDetailList", sweepDetailListAll);
			request.setAttribute("othersDetailList", otherDetailListAll);
			putCurrencyGroupListInReq(request, sweep.getId().getHostId(), sweep
					.getEntityId());

			putAccountListInReq(request, entityId);

			request.setAttribute("lastRefTime", SwtUtil.getLastRefTimeOnGMTOffset(request,
					SwtUtil.getUserCurrentEntity(request.getSession())));
			return forward;
		} catch (SwtException sexp) {
			log
					.error("SwtException Catch in SweepCancelQueueAction.'submit' method : "
							+ sexp.getMessage());

			saveErrors(request, SwtUtil.logException(sexp, request, ""));

			currGrpId = sweep.getCurrencyCode();
			initialCurrencyCode = currGrpId;
			roleId = getRoleId(request);
			entityId = SwtUtil.getUserCurrentEntity(request.getSession());
			currencyGrpAccess = SwtUtil.getSwtMaintenanceCache()
					.getCurrencyGroupAccess(roleId, entityId, currGrpId);
			SwtUtil.getMenuEntityCurrGrpAccessWithoutCurrency(request,
					entityId, currGrpId);
			// open the screen in same status again
			sweepQueueDetailVO = new SweepQueueDetailVO();
			sweepQueueDetailVO.setEntityAccessList(SwtUtil
					.getUserEntityAccessList(request.getSession()));

			// code for handling of All currency group added starts
			if (currGrpId.equalsIgnoreCase(SwtConstants.ALL_VALUE)) {
				// log.debug("processing for ALL starts");
				groupList = SwtUtil.getSwtMaintenanceCache()
						.getCurrencyGroupViewORFullAcess(roleId, entityId);
				itGroupList = groupList.iterator();
				temp = new ArrayList();

				currGrpId = "";
				/*
				 * Start:Code Modified by Balaji for Mantis 1483 on
				 * 8-JUN-2011:For Sweep Cancel Queue Screen displays error when
				 * the last currency group in Currency group Access screen is
				 * set to View Access
				 */
				currGrpCounter = 0;
				// This while loop is for gennerating currecy group
				// access for cancel panel

				while (itGroupList.hasNext()) {
					entityCurrencyGroupAccess = (EntityCurrencyGroupAccess) itGroupList
							.next();
					// If condition is for appending qouation and commas
					// for currency group access
					if (entityCurrencyGroupAccess.getAccess() == 0) {
						if (currGrpCounter > 0) {
							currGrpId += ",'"
									+ entityCurrencyGroupAccess
											.getCurrencyGroupId() + "'";
						} else {
							currGrpId += "'"
									+ entityCurrencyGroupAccess
											.getCurrencyGroupId() + "'";
						}
					}
					currGrpCounter++;
				}
				/*
				 * End:Code Modified by Balaji on 8-JUN-2011:For Sweep Cancel
				 * Queue Screen displays error when the last currency group in
				 * Currency group Access screen is set to View Access
				 */
				if (currGrpId.equals(SwtConstants.EMPTY_STRING)) {
					currGrpId = "''";
				}
				sweep.setCurrencyCode(currGrpId);
				// Intializing cancelViewCurrGrpId as empty
				cancelViewCurrGrpId = "";
				itGroupList = groupList.iterator();
				// This while loop is for gennerating currecy group
				// access for view panel

				while (itGroupList.hasNext()) {
					entityCurrencyGroupAccess = (EntityCurrencyGroupAccess) itGroupList
							.next();
					// If condition is for appending qouation and commas
					// for currency group access
					if (itGroupList.hasNext())
						cancelViewCurrGrpId += "'"
								+ entityCurrencyGroupAccess
										.getCurrencyGroupId() + "',";
					else
						cancelViewCurrGrpId += "'"
								+ entityCurrencyGroupAccess
										.getCurrencyGroupId() + "'";
				}
				if (cancelViewCurrGrpId.equals(SwtConstants.EMPTY_STRING)) {
					cancelViewCurrGrpId = "''";
				}
				// Setting currency group to bean
				sweep.setCancelViewCurrGrpId(cancelViewCurrGrpId);
				sweepQueueDetailVO.setEntityAccessList(SwtUtil
						.getUserEntityAccessList(request.getSession()));
				sweepQueueDetailVO.setSweepDetailList(new ArrayList());
				sweepQueueDetailVO.setOtherDetailList(new ArrayList());
				currencyGrpAccess = SwtUtil.getSwtMaintenanceCache()
						.getCurrencyGroupAccess(roleId, entityId, currGrpId);
				totalCount = sweepQueueManager.getSweepQueueDetail(sweep,
						roleId, currencyGrpAccess, sweepQueueDetailVO,
						currentPage, initialPageCount, filterSortStatus,
						systemFormats);
				request.setAttribute("totalCount", totalCount);
				maxPage = setMaxPageAttribute(
						totalCount,
						SwtUtil.getPageSizeFromProperty(SwtConstants.SWEEP_CANCEL_QUEUE_PAGE_SIZE));
				temp = (ArrayList) sweepQueueDetailVO.getSweepDetailList();

				tempItr = temp.iterator();

				while (tempItr.hasNext())
					sweepDetailListAll.add(tempItr.next());

				temp = (ArrayList) sweepQueueDetailVO.getOtherDetailList();
				tempItr = temp.iterator();

				while (tempItr.hasNext())
					otherDetailListAll.add(tempItr.next());

				sweep.setCurrencyCode("All");

			} else {
				sweep.setCurrencyCode("'" + initialCurrencyCode + "'");
				sweep.setCancelViewCurrGrpId("'" + initialCurrencyCode + "'");
				totalCount = sweepQueueManager.getSweepQueueDetail(sweep,
						roleId, currencyGrpAccess, sweepQueueDetailVO,
						currentPage, initialPageCount, filterSortStatus,
						systemFormats);
				request.setAttribute("totalCount", totalCount);
				maxPage = setMaxPageAttribute(
						totalCount,
						SwtUtil.getPageSizeFromProperty(SwtConstants.SWEEP_CANCEL_QUEUE_PAGE_SIZE));
			}
			sweep.setCurrencyCode(initialCurrencyCode);
			pageSummaryList = new ArrayList<PageDetails>();

			setPageSummaryList(currentPage, maxPage, totalCount,
					pageSummaryList);

			/* Condition checked to enable or disable the next link */
			if (maxPage > 1) {
				// If true the isNext flag is set to true which is set to
				// nextEnabled request attribute which is used in UI to
				// enable the next link.
				isNext = true;
			}

			/* Condition checked for current sort equal to none */
			if ("none".equals(currentSort)) {
				// If the condition return true the default value for sort
				// is assigned for first column ascending
				currentSort = "0|true"; // default sorting column
			}

			// Hide pagination if no of records are "0" or records can be
			// displayed in one page.
			if (maxPage <= 1) {
				request.setAttribute("hidePagination", true);
			}

			request.setAttribute("currentPage", Integer.toString(currentPage));
			request.setAttribute("prevEnabled", "false");
			request.setAttribute("nextEnabled", "" + isNext);
			request.setAttribute("maxPage", Integer.toString(maxPage));
			request.setAttribute("selectedFilter", currentFilter);
			request.setAttribute("selectedSort", currentSort);
			request.setAttribute("pageSummaryList", pageSummaryList);
			setSweepQueue(sweep);
			request.setAttribute("sweepDetailList", sweepQueueDetailVO
					.getSweepDetailList());
			request.setAttribute("othersDetailList", sweepQueueDetailVO
					.getOtherDetailList());

			putEntityListInReq(request);

			putCurrencyGroupListInReq(request, sweep.getId().getHostId(), sweep
					.getEntityId());

			putAccountListInReq(request, entityId);

			forward = "cancel";

			if (SwtConstants.SWEEP_STATUS_CANCEL.equalsIgnoreCase(sweep
					.getQueueName())) {
				forward = "cancel";
			}
			return forward;
		} catch (Exception exp) {
			log
					.error("Exception Catch in SweepCancelQueueAction.'submit' method : "
							+ exp.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "submit", SweepCancelQueueAction.class), request, "");

			return ("fail");
		}
	}

	/**
	 * Method to put the entity list in the request
	 * 
	 * @param request
	 * @throws SwtException
	 */
	@SuppressWarnings("unchecked")
	private void putEntityListInReq(HttpServletRequest request)
			throws SwtException {
		Collection coll = SwtUtil.getUserEntityAccessList(request.getSession());
		coll = SwtUtil.convertEntityAcessCollectionLVL(coll, request
				.getSession());

		request.setAttribute("entityList", coll);
	}

	/**
	 * Method to put the account type in the request
	 * 
	 * @param request
	 * @param entityId
	 * @throws SwtException
	 */
	@SuppressWarnings("unchecked")
	private void putAccountListInReq(HttpServletRequest request, String entityId)
			throws SwtException {
		CacheManager cacheManagerInst = CacheManager.getInstance();
		ArrayList coll = (ArrayList) cacheManagerInst.getMiscParamsLVL(
				"ACCOUNTTYPE", entityId);
		coll.remove(0);
		coll.add(0, new LabelValueBean("All", "All"));
		request.setAttribute("accountList", coll);
	}

	/**
	 * @return Returns the sweepQueueManager.
	 */
	public SweepQueueManager getSweepQueueManager() {
		return sweepQueueManager;
	}

	/**
	 * @param sweepQueueManager
	 *            The sweepQueueManager to set.
	 */
	public void setSweepQueueManager(SweepQueueManager sweepQueueManager) {
		this.sweepQueueManager = sweepQueueManager;
	}

	/**
	 * This method is used to put associated CurrencyGroup list into the request
	 * that is further displayed on the UI.
	 * 
	 * @param request
	 * @param hostId
	 * @param entityId
	 */
	@SuppressWarnings("unchecked")
	private String putCurrencyGroupListInReq(HttpServletRequest request,
			String hostId, String entityId) throws SwtException {
		log
				.debug("Inside SweepCancelQueueAction.putCurrencyGroupListInReq method");

		Collection currencyGroupList = new ArrayList();

		String defaultCurrencyGroup = new String();
		String roleId = getRoleId(request);
		// code added for handling of the Entity:All STARTS
		ArrayList al = new ArrayList();

		if (entityId.equalsIgnoreCase(SwtConstants.ALL_VALUE)) {
			RoleTO roleObject = new RoleTO(roleId);
			Collection entityList = SwtUtil.getSwtMaintenanceCache()
					.getEntityAccessCollection(roleObject);
			Iterator itr = entityList.iterator();

			while (itr.hasNext()) {
				EntityUserAccess entityUserAccess = (EntityUserAccess) itr
						.next();
				String entityIdAll = entityUserAccess.getEntityId();

				Collection groupListAll = SwtUtil.getSwtMaintenanceCache()
						.getCurrencyGroupViewORFullAcess(roleId, entityIdAll);
				Iterator groupListAllitr = groupListAll.iterator();

				while (groupListAllitr.hasNext())
					al.add(groupListAllitr.next());
			}
		} else {
			// code added for handling of the Entity:All ENDS
			al = (ArrayList) SwtUtil.getSwtMaintenanceCache()
					.getCurrencyGroupViewORFullAcess(roleId, entityId);

		}

		Iterator itGroupList = al.iterator();
		currencyGroupList.add(new LabelValueBean(SwtConstants.ALL_LABEL,
				SwtConstants.ALL_VALUE));

		while (itGroupList.hasNext()) {
			EntityCurrencyGroupAccess entityCurrencyGroupAccess = (EntityCurrencyGroupAccess) itGroupList
					.next();

			currencyGroupList.add(new LabelValueBean(entityCurrencyGroupAccess
					.getCurrencyGroupName(), entityCurrencyGroupAccess
					.getCurrencyGroupId()));
		}

		request.setAttribute("currencyGroupList", currencyGroupList);

		if ((currencyGroupList != null) && (currencyGroupList.size() > 1)) {
			ArrayList tempArrayList = (ArrayList) currencyGroupList;
			LabelValueBean labelValueBean = (LabelValueBean) tempArrayList
					.get(1);

			defaultCurrencyGroup = labelValueBean.getValue();
		}

		return defaultCurrencyGroup;
	}

	/**
	 * Fetched the role id of the current logged in user
	 * 
	 * @param request
	 * @return String
	 */
	public String getRoleId(HttpServletRequest request) {
		CommonDataManager CDM = (CommonDataManager) request.getSession()
				.getAttribute("CDM");
		User currUser = (User) CDM.getUser();
		String roleId = currUser.getRoleId();

		return roleId;
	}

	/**
	 * This method is called when PREVIOUS LINK, NEXT LINK or any Page no is
	 * clicked. It fetches the sweeps that can be cancelled for that page.
	 * 
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return ActionForward
	 * @throws SwtException
	 */
	@SuppressWarnings("unchecked")
	public String next()
			throws SwtException {
HttpServletRequest request = ServletActionContext.getRequest();
HttpServletResponse response = ServletActionContext.getResponse();

		String currentFilter = "";
		String currentSort = "";
		String filterSortStatus = "";
		int currentPage = 0;
		int initialPageCount = 0;
		boolean isNext = false;
		int maxPage = 0;

		ArrayList<PageDetails> pageSummaryList = null;
		int clickedPage;
		String nextLinkStatus = null;
		String prevLinkStatus = null;
		Sweep sweep;
		String initialCurrencyCode = "";
		// Variable declaration for sweepDetailListAll
		ArrayList sweepDetailListAll = null;
		// Variable declaration for otherDetailListAll
		ArrayList otherDetailListAll = null;
		// Variable declaration for dyForm
		// DynaValidatorForm dyForm = null;
		// Variable declaration for session
		HttpSession session = null;
		// Variable declaration for currencyGrpAccess
		int currencyGrpAccess = 0;
		// Variable declaration for currGrpCounter
		int currGrpCounter = 0;
		// Variable declaration for cancelViewCurrGrpId
		String cancelViewCurrGrpId = null;
		// Variable declaration for entityList
		Collection entityList = null;
		// Variable declaration for entityUserAccess
		EntityUserAccess entityUserAccess = null;
		// Variable declaration for groupList
		Collection groupList = null;
		// Variable declaration for entityCurrencyGroupAccess
		EntityCurrencyGroupAccess entityCurrencyGroupAccess = null;
		// Variable declaration for acctType
		String acctType = null;
		// Variable declaration for currGrpId
		String currGrpId = null;
		// Variable declaration for roleId
		String roleId = null;
		// Variable declaration for entityId
		String entityId = null;
		// Variable declaration for roleObject
		RoleTO roleObject = null;
		// Variable declaration for itr
		Iterator itr = null;
		// Variable declaration for itGroupList
		Iterator itGroupList = null;
		// Variable declaration for systemFormats
		SystemFormats systemFormats = null;
		// Variable declaration for sweepQueueDetailVO
		SweepQueueDetailVO sweepQueueDetailVO = null;
		// Variable declaration for temp
		ArrayList temp = null;
		// Variable declaration for tempItr
		Iterator tempItr = null;
		try {
			log.debug(this.getClass().getName() + " - [next] - " + "Entry");
			// ArrayLists added for Currency Group 'All' case
			sweepDetailListAll = new ArrayList();
			otherDetailListAll = new ArrayList();
			session = request.getSession();
			int totalCount = 0;
			/* Get all the filter values specified for all columns */
			currentFilter = (request.getParameter("selectedFilter") != null) ? request
					.getParameter("selectedFilter").trim().toString()
					: "all";

			/* Gets the sort column specified */
			currentSort = (request.getParameter("selectedSort") != null) ? request
					.getParameter("selectedSort").trim().toString()
					: "1|false";

			filterSortStatus = currentFilter + "#" + currentSort;

			/*
			 * Read the current page value and store it as integer in a integer
			 * variable
			 */
			currentPage = Integer.parseInt(((request
					.getParameter("currentPage") != null) && !request
					.getParameter("currentPage").equals("")) ? request
					.getParameter("currentPage").trim().toString() : "0");

			/*
			 * Read the clicked page value and store it as integer in a integer
			 * variable
			 */
			clickedPage = Integer
					.parseInt(((request.getParameter("goToPageNo") != null) && !request
							.getParameter("goToPageNo").equals("")) ? request
							.getParameter("goToPageNo").trim().toString() : "0");

			/*
			 * Read the maxpage value and store it as integer in a integer
			 * variable
			 */
			maxPage = Integer
					.parseInt(((request.getParameter("maxPages") != null) && !request
							.getParameter("maxPages").equals("")) ? request
							.getParameter("maxPages").trim().toString() : "0");

			/* Previous Link Clicked */
			if (clickedPage == -2) {
				currentPage--;
				clickedPage = currentPage;
			} else {
				/* Next Link Clicked */
				if (clickedPage == -1) {
					currentPage++;
					clickedPage = currentPage;
				} else {
					currentPage = clickedPage;
				}
			}
			/*
			 * Condition to check the clicked page.If Clicked page greater than
			 * 1 then previous link enable. If Clicked page less than 1 then
			 * previous link disable
			 */
			if (clickedPage > 1) {
				prevLinkStatus = "true";
			} else {
				prevLinkStatus = "false";
			}
			/*
			 * Condition to check the clicked page.If Clicked page less than
			 * maximum page then next link enable. If Clicked page greater than
			 * maximum page then next link disable
			 */

			if (clickedPage < maxPage) {
				nextLinkStatus = "true";
			} else {
				nextLinkStatus = "false";
			}

			systemFormats = new SystemFormats();

			// Read the selected filter from the request and store in String
			// variable
			currentFilter = request.getParameter("selectedFilter");
			// Read the selectedSort from the request and store in String
			// variable
			currentSort = request.getParameter("selectedSort");
			/* Condition to check currentFilter is null */
			if (currentFilter == null
					|| currentFilter.equals(SwtConstants.EMPTY_STRING)) {
				// If current filter is null the default value 'All' is set
				// for currentFilter
				currentFilter = "all";
			}

			/* Condition to check currentSort is null */
			if (currentSort == null
					|| currentSort.equals(SwtConstants.EMPTY_STRING)) {
				/* If currentSort is null then set current sort as none. */
				currentSort = "none";
			}

			// Use # as separator for the combined current filter and
			// current sort
			filterSortStatus = currentFilter + "#" + currentSort;

			systemFormats.setCurrencyFormat(SwtUtil
					.getCurrentCurrencyFormat(request.getSession()));
			systemFormats.setDateFormatValue(SwtUtil
					.getCurrentDateFormat(request.getSession()));
			sweep = (Sweep) getSweepQueue();

			sweep.getId().setHostId(
					SwtUtil.getCurrentHostId(request.getSession()));
			sweep
					.setRequestUser(SwtUtil.getCurrentUserId(request
							.getSession()));

			sweep.setRequestRole(SwtUtil.getCurrentUser(request.getSession())
					.getRoleId());
			sweep.setQueueName(SwtConstants.SWEEP_STATUS_CANCEL);
			acctType = request.getParameter("accountType");
			sweep.setEntityId(request.getParameter("entityId"));
			sweep.setCurrencyCode(request.getParameter("currGrpId"));
			sweep.setAccountType(acctType);
			if (sweep.getAccountType().equalsIgnoreCase("C")) {
				request.setAttribute("AccountDesp",
						SwtConstants.CASH_ACCOUNTS_TYPE);
			}

			if (sweep.getAccountType().equalsIgnoreCase("U")) {
				request.setAttribute("AccountDesp",
						SwtConstants.CUSTODIAN_ACCOUNTS_TYPE);
			}

			if (sweep.getAccountType().equalsIgnoreCase("All")) {
				request.setAttribute("AccountDesp",
						SwtConstants.ALL_ACCOUNTS_TYPE);
			}

			putEntityListInReq(request);

			putCurrencyGroupListInReq(request, sweep.getId().getHostId(), sweep
					.getEntityId());

			putAccountListInReq(request, sweep.getEntityId());

			sweepQueueDetailVO = new SweepQueueDetailVO();
			sweepQueueDetailVO.setEntityAccessList(SwtUtil
					.getUserEntityAccessList(request.getSession()));

			currGrpId = "";
			roleId = getRoleId(request);
			entityId = sweep.getEntityId();

			session = request.getSession();
			if ((!entityId.equalsIgnoreCase(SwtUtil
					.getUserCurrentEntity(request.getSession())))
					&& sweep.getCurrencyCode().equalsIgnoreCase(
							((CommonDataManager) session
									.getAttribute(SwtConstants.CDM_BEAN))
									.getUser().getCurrentCcyGrpId())) {
				currGrpId = "All";

			} else {
				currGrpId = sweep.getCurrencyCode();
			}
			SwtUtil.getMenuEntityCurrGrpAccessWithoutCurrency(request,
					entityId, currGrpId);
			initialCurrencyCode = currGrpId;
			// code added to handle Entity as All starts
			if (entityId.equalsIgnoreCase(SwtConstants.ALL_VALUE)) {
				maxPage = 0;
				roleObject = new RoleTO(roleId);
				entityList = SwtUtil.getSwtMaintenanceCache()
						.getEntityAccessCollection(roleObject);
				itr = entityList.iterator();

				while (itr.hasNext()) {

					currGrpId = sweep.getCurrencyCode();
					entityUserAccess = (EntityUserAccess) itr.next();
					entityId = entityUserAccess.getEntityId();
					sweep.setEntityId(entityId);
					if (currGrpId.equalsIgnoreCase(SwtConstants.ALL_VALUE)) {
						currencyGrpAccess = 0;
					} else {
						currencyGrpAccess = SwtUtil.getSwtMaintenanceCache()
								.getCurrencyGroupAccess(roleId, entityId,
										currGrpId);
					}
					if (currGrpId.equalsIgnoreCase(SwtConstants.ALL_VALUE)) {
						groupList = SwtUtil.getSwtMaintenanceCache()
								.getCurrencyGroupViewORFullAcess(roleId,
										entityId);
						itGroupList = groupList.iterator();
						temp = new ArrayList();
						currGrpId = "";
						/*
						 * Start:Code Modified by Balaji for Mantis 1483 on
						 * 8-JUN-2011:For Sweep Cancel Queue Screen displays
						 * error when the last currency group in Currency group
						 * Access screen is set to View Access
						 */

						// This while loop is for gennerating currecy group
						// access for cancel panel
						while (itGroupList.hasNext()) {
							entityCurrencyGroupAccess = (EntityCurrencyGroupAccess) itGroupList
									.next();
							// If condition is for appending qouation and commas
							// for currency group access

							if (entityCurrencyGroupAccess.getAccess() == 0) {
								if (currGrpCounter > 0) {
									currGrpId += ",'"
											+ entityCurrencyGroupAccess
													.getCurrencyGroupId() + "'";
								} else {
									currGrpId += "'"
											+ entityCurrencyGroupAccess
													.getCurrencyGroupId() + "'";
								}
							}
							currGrpCounter++;
						}
						/*
						 * End:Code Modified by Balaji for Mantis 1483 on
						 * 8-JUN-2011:For Sweep Cancel Queue Screen displays
						 * error when the last currency group in Currency group
						 * Access screen is set to View Access
						 */
						if (currGrpId.equals(SwtConstants.EMPTY_STRING)) {
							currGrpId = "''";
						}
						sweep.setCurrencyCode(currGrpId);
						// Intializing cancelViewCurrGrpId as empty
						cancelViewCurrGrpId = "";
						itGroupList = groupList.iterator();
						// This while loop is for gennerating currecy group
						// access for view panel
						while (itGroupList.hasNext()) {
							// If condition is for appending qouation and commas
							// for currency group access
							entityCurrencyGroupAccess = (EntityCurrencyGroupAccess) itGroupList
									.next();

							if (itGroupList.hasNext())
								cancelViewCurrGrpId += "'"
										+ entityCurrencyGroupAccess
												.getCurrencyGroupId() + "',";
							else
								cancelViewCurrGrpId += "'"
										+ entityCurrencyGroupAccess
												.getCurrencyGroupId() + "'";
						}
						if (cancelViewCurrGrpId
								.equals(SwtConstants.EMPTY_STRING)) {
							cancelViewCurrGrpId = "''";
						}
						sweep.setCancelViewCurrGrpId(cancelViewCurrGrpId);
						sweepQueueDetailVO.setEntityAccessList(SwtUtil
								.getUserEntityAccessList(request.getSession()));
						sweepQueueDetailVO.setSweepDetailList(new ArrayList());
						sweepQueueDetailVO.setOtherDetailList(new ArrayList());
						currencyGrpAccess = SwtUtil.getSwtMaintenanceCache()
								.getCurrencyGroupAccess(roleId, entityId,
										currGrpId);
						// Getting no of counts of recors from the mannager
						// class
						totalCount += sweepQueueManager.getSweepQueueDetail(
								sweep, roleId, currencyGrpAccess,
								sweepQueueDetailVO, currentPage,
								initialPageCount, filterSortStatus,
								systemFormats);

						temp = (ArrayList) sweepQueueDetailVO
								.getSweepDetailList();

						tempItr = temp.iterator();

						while (tempItr.hasNext())
							sweepDetailListAll.add(tempItr.next());

						temp = (ArrayList) sweepQueueDetailVO
								.getOtherDetailList();
						tempItr = temp.iterator();

						while (tempItr.hasNext())
							otherDetailListAll.add(tempItr.next());
						sweep.setCurrencyCode("All");
					} else {
						temp = new ArrayList();
						sweep.setCurrencyCode("'" + initialCurrencyCode + "'");
						sweep.setCancelViewCurrGrpId("'" + initialCurrencyCode
								+ "'");
						totalCount += sweepQueueManager.getSweepQueueDetail(
								sweep, roleId, currencyGrpAccess,
								sweepQueueDetailVO, currentPage,
								initialPageCount, filterSortStatus,
								systemFormats);

						temp = (ArrayList) sweepQueueDetailVO
								.getSweepDetailList();

						tempItr = temp.iterator();

						while (tempItr.hasNext())
							sweepDetailListAll.add(tempItr.next());

						temp = (ArrayList) sweepQueueDetailVO
								.getOtherDetailList();
						tempItr = temp.iterator();

						while (tempItr.hasNext())
							otherDetailListAll.add(tempItr.next());
					}
				}
				request.setAttribute("totalCount", totalCount);
				maxPage = setMaxPageAttribute(
						totalCount,
						SwtUtil.getPageSizeFromProperty(SwtConstants.SWEEP_CANCEL_QUEUE_PAGE_SIZE));
				sweep.setEntityId("All");
			} else {
				if (currGrpId.equalsIgnoreCase(SwtConstants.ALL_VALUE)) {
					currencyGrpAccess = 0;
				} else {
					currencyGrpAccess = SwtUtil
							.getSwtMaintenanceCache()
							.getCurrencyGroupAccess(roleId, entityId, currGrpId);
				}
				if (currGrpId.equalsIgnoreCase(SwtConstants.ALL_VALUE)) {
					groupList = SwtUtil.getSwtMaintenanceCache()
							.getCurrencyGroupViewORFullAcess(roleId, entityId);
					itGroupList = groupList.iterator();
					temp = new ArrayList();
					currGrpId = "";
					/*
					 * Start:Code Modified by Balaji for Mantis 1483 on
					 * 8-JUN-2011:For Sweep Cancel Queue Screen displays error
					 * when the last currency group in Currency group Access
					 * screen is set to View Access
					 */
					currGrpCounter = 0;
					// This while loop is for gennerating currecy group
					// access for cancel panel
					while (itGroupList.hasNext()) {
						entityCurrencyGroupAccess = (EntityCurrencyGroupAccess) itGroupList
								.next();
						// If condition is for appending qouation and commas
						// for currency group access

						if (entityCurrencyGroupAccess.getAccess() == 0) {
							if (currGrpCounter > 0) {
								currGrpId += ",'"
										+ entityCurrencyGroupAccess
												.getCurrencyGroupId() + "'";
							} else {
								currGrpId += "'"
										+ entityCurrencyGroupAccess
												.getCurrencyGroupId() + "'";
							}
							currGrpCounter++;
						}
					}
					/*
					 * End:Code Modified by Balaji for Mantis 1483 on
					 * 8-JUN-2011:For Sweep Cancel Queue Screen displays error
					 * when the last currency group in Currency group Access
					 * screen is set to View Access
					 */
					if (currGrpId.equals(SwtConstants.EMPTY_STRING)) {
						currGrpId = "''";
					}
					sweep.setCurrencyCode(currGrpId);
					// Intializing cancelViewCurrGrpId as empty
					cancelViewCurrGrpId = "";
					itGroupList = groupList.iterator();
					// This while loop is for gennerating currecy group
					// access for view panel
					while (itGroupList.hasNext()) {
						entityCurrencyGroupAccess = (EntityCurrencyGroupAccess) itGroupList
								.next();
						// If condition is for appending qouation and commas
						// for currency group access

						if (itGroupList.hasNext())
							cancelViewCurrGrpId += "'"
									+ entityCurrencyGroupAccess
											.getCurrencyGroupId() + "',";
						else
							cancelViewCurrGrpId += "'"
									+ entityCurrencyGroupAccess
											.getCurrencyGroupId() + "'";
					}
					if (cancelViewCurrGrpId.equals(SwtConstants.EMPTY_STRING)) {
						cancelViewCurrGrpId = "''";
					}
					sweep.setCancelViewCurrGrpId(cancelViewCurrGrpId);
					sweepQueueDetailVO.setEntityAccessList(SwtUtil
							.getUserEntityAccessList(request.getSession()));
					sweepQueueDetailVO.setSweepDetailList(new ArrayList());
					sweepQueueDetailVO.setOtherDetailList(new ArrayList());
					currencyGrpAccess = SwtUtil
							.getSwtMaintenanceCache()
							.getCurrencyGroupAccess(roleId, entityId, currGrpId);
					totalCount = sweepQueueManager.getSweepQueueDetail(sweep,
							roleId, currencyGrpAccess, sweepQueueDetailVO,
							currentPage, initialPageCount, filterSortStatus,
							systemFormats);
					request.setAttribute("totalCount", totalCount);
					maxPage = setMaxPageAttribute(
							totalCount,
							SwtUtil.getPageSizeFromProperty(SwtConstants.SWEEP_CANCEL_QUEUE_PAGE_SIZE));
					temp = (ArrayList) sweepQueueDetailVO.getSweepDetailList();
					tempItr = temp.iterator();

					while (tempItr.hasNext())
						sweepDetailListAll.add(tempItr.next());

					temp = (ArrayList) sweepQueueDetailVO.getOtherDetailList();
					tempItr = temp.iterator();

					while (tempItr.hasNext())
						otherDetailListAll.add(tempItr.next());

					sweep.setCurrencyCode("All");
				} else {
					sweep.setCurrencyCode("'" + initialCurrencyCode + "'");
					sweep.setCancelViewCurrGrpId("'" + initialCurrencyCode
							+ "'");
					totalCount = sweepQueueManager.getSweepQueueDetail(sweep,
							roleId, currencyGrpAccess, sweepQueueDetailVO,
							currentPage, initialPageCount, filterSortStatus,
							systemFormats);
					request.setAttribute("totalCount", totalCount);
					maxPage = setMaxPageAttribute(
							totalCount,
							SwtUtil.getPageSizeFromProperty(SwtConstants.SWEEP_CANCEL_QUEUE_PAGE_SIZE));
					sweepDetailListAll = (ArrayList) sweepQueueDetailVO
							.getSweepDetailList();
					otherDetailListAll = (ArrayList) sweepQueueDetailVO
							.getOtherDetailList();
				}
			}
			sweep.setCurrencyCode(initialCurrencyCode);
			setSweepQueue(sweep);
			pageSummaryList = new ArrayList<PageDetails>();

			setPageSummaryList(currentPage, maxPage, totalCount,
					pageSummaryList);

			/* Condition checked for current sort equal to none */
			if ("none".equals(currentSort)) {
				// If the condition return true the default value for sort
				// is assigned for first column ascending
				currentSort = "0|true"; // default sorting column
			}
			// Hide pagination if no of records are "0" or records can be
			// displayed in one page.
			if (maxPage <= 1) {
				request.setAttribute("hidePagination", true);
			}
			request.setAttribute("currentPage", Integer.toString(currentPage));
			request.setAttribute("prevEnabled", prevLinkStatus);
			request.setAttribute("nextEnabled", nextLinkStatus);
			request.setAttribute("maxPage", Integer.toString(maxPage));
			request.setAttribute("selectedFilter", currentFilter);
			request.setAttribute("selectedSort", currentSort);
			request.setAttribute("pageSummaryList", pageSummaryList);

			request.setAttribute("lastRefTime", SwtUtil.getLastRefTimeOnGMTOffset(request,
					entityId));
			request.setAttribute("sweepDetailList", sweepDetailListAll);
			request.setAttribute("othersDetailList", otherDetailListAll);

			log.debug(this.getClass().getName() + " - [next] - " + "Exit");
			return ("cancel");
		} catch (SwtException swtexp) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [next] method : - "
					+ swtexp.getMessage());
			SwtUtil.logException(swtexp, request, "");

			return ("cancel");
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [next] method : - "
					+ exp.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "next", SweepCancelQueueAction.class), request, "");

			return ("fail");
		}
	}

	/**
	 * This function is used calculate the maximum pages based on the total
	 * number of records and the records to be displayed per page
	 * 
	 * @param totalCount
	 * @param pageSize
	 * @return int
	 */
	private int setMaxPageAttribute(int totalCount, int pageSize) {
		log.debug(this.getClass().getName()
				+ "- [setMaxPageAttribute] - Entering");
		/* Class instance declaration */
		int maxPage = 0;
		int remainder = 0;
		if (totalCount > 0 && pageSize > 0) {
			maxPage = (totalCount) / (pageSize);
			remainder = totalCount % pageSize;
			if (remainder > 0) {
				maxPage++;
			}
		}
		log.debug(this.getClass().getName()
				+ "- [setMaxPageAttribute] - Exiting");
		return maxPage;
	}

	/**
	 * Method added for setting the page numbers.
	 * 
	 * @param EntityId
	 * @param CurrentPage
	 * @param MaxPage
	 * @param MovTotalCount
	 * @param PageSummaryList
	 * @return none
	 */
	private void setPageSummaryList(int currentPage, int maxPage,
			int totalCount, ArrayList<PageDetails> pageSummaryList) {
		log.debug(this.getClass().getName() + " - [setPageSummaryList] - "
				+ "Entry");
		PageDetails pSummary = new PageDetails();
		pSummary.setCurrentPageNo(currentPage);
		pSummary.setMaxPages(maxPage);
		pSummary.setTotalCount(totalCount);
		pageSummaryList.add(pSummary);
		log.debug(this.getClass().getName() + " - [setPageSummaryList] - "
				+ "Exit");
	}

}
