/*
 * @(#)InputAuthoriseAction.java 1.0 
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */

package org.swallow.work.web;

import java.text.Format;
import java.text.SimpleDateFormat;
import java.util.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;






import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.maintenance.model.Currency;
import org.swallow.maintenance.model.EntityUserAccess;
import org.swallow.maintenance.service.CurrencyGroupManager;
import org.swallow.util.CacheManager;
import org.swallow.util.CommonDataManager;
import org.swallow.util.PropertiesFileLoader;
import org.swallow.util.SwtConstants;
import org.swallow.util.SwtUtil;
import org.swallow.util.SystemFormats;
import org.swallow.work.model.Movement;
import org.swallow.work.service.MovementLockManager;
import org.swallow.work.service.PreAdviceInputManager;
import org.swallow.util.LabelValueBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.apache.struts2.convention.annotation.Result;
import org.apache.struts2.convention.annotation.AllowedMethods;
import org.apache.struts2.convention.annotation.Action;
import org.apache.struts2.ServletActionContext;
import org.swallow.util.struts.CustomActionSupport;

/**
 * <pre>
 * Action layer for Input Authorize screen.
 * Used to
 * - Display Input Authorize counts
 * - Display Input Authorize movements
 * - Display Input Referred Screen
 * - Authorize movements
 * - Refer Movements
 * - Submit Movements  
 * </pre>
 */
@Action(value = "/inputauthorise", results = {
	@Result(name = "fail", location = "/error.jsp"),
	@Result(name = "success", location = "/jsp/work/inputauthorisedisplay.jsp"),
})

@AllowedMethods ({"display" ,"authoriseMovement" ,"refereMovement" ,"submitMovement" ,"deleteMovements","checkLockedMvts" })
public class InputAuthoriseAction extends CustomActionSupport {
public String execute() throws Exception {
    HttpServletRequest request = ServletActionContext.getRequest();

    // List of methods 
    String method = String.valueOf(request.getParameter("method"));

    switch (method) {
        case "unspecified":
            return unspecified();
        case "display":
            return display();
        case "authoriseMovement":
            return authoriseMovement();
        case "refereMovement":
            return refereMovement();
        case "submitMovement":
            return submitMovement();
        case "deleteMovements":
            return deleteMovements();
		case "checkLockedMvts":
			return checkLockedMvts();

        default:
            break;
    }

    return unspecified();
}


private Movement movement;
public Movement getMovement() {
	if (movement == null) {
		movement = new Movement();
	}
	return movement;
}
public void setMovement(Movement movement) {
	this.movement = movement;
	HttpServletRequest request = ServletActionContext.getRequest();
	request.setAttribute("movement", movement);
}

	/**
	 * Reference Variable of Movement Manager
	 */
    @Autowired
	private PreAdviceInputManager preAdviceInputManager;

	/**
	 * Reference variable of log factory
	 */
	private Log log = LogFactory.getLog(InputAuthoriseAction.class);

	/**
	 * Initializing empty string
	 */
	private final String EMPTYSTRING = "";

	/**
	 * Initializing default search string
	 */
	private final String DEFAULTSEARCHSTRING = "All";

	/**
	 * Initializing default search string
	 */
	private final String OUTSTANDING_MATCHSTATUS = "L";

	/**
	 * Initializing default search string
	 */
	private final String REFERED_MATCHSTATUS = "R";

	/**
	 * Initializing default search string
	 */
	private final String AUTHORISE_MATCHSTATUS = "A";

	/**
	 * Variable used to define screen buttons and functionality for Authorise UI
	 */
	private final String SCREEN_STATUS_AUTH = "authorise";

	/**
	 * Variable used to define screen buttons and functionality for Refered UI
	 */
	private final String SCREEN_STATUS_REFER = "refere";

	/**
	 * @param PreAdviceInputManager -
	 *            preAdviceInputManager to set.
	 */
	public void setPreAdviceInputManager(
			PreAdviceInputManager preAdviceInputManager) {
		this.preAdviceInputManager = preAdviceInputManager;
	}

	/**
	 * From manual input jsp or movement display jsp control first directly goes
	 * to this method if no methodName is specified
	 * 
	 * @param ActionMapping
	 *            mapping
	 * @param ActionForm
	 *            form
	 * @param HttpServletRequest
	 *            request
	 * @param HttpServletResponse
	 *            response
	 * @return - ActionForward object
	 * @throws SwtException -
	 *             SwtException object
	 */
	public String unspecified()
			throws SwtException {
HttpServletRequest request = ServletActionContext.getRequest();
HttpServletResponse response = ServletActionContext.getResponse();


		log.debug(this.getClass().getName() + " - unspecified () - "
				+ " Entry ");

		/* Setting in Session Attribute & Request Attribute */
		request.getSession().setAttribute("buttonDiv", "movementDisplay");
		request.setAttribute("buttonDiv", "movementDisplay");

		log
				.debug(this.getClass().getName() + " - unspecified () - "
						+ " Exit ");

		return display();
	}

	/**
	 * This Method is used to display the Input Authorize movements.
	 * 
	 * @param ActionMapping
	 *            mapping
	 * @param ActionForm
	 *            form
	 * @param HttpServletRequest
	 *            request
	 * @param HttpServletResponse
	 *            response
	 * @return - ActionForward object
	 * @throws SwtException -
	 *             SwtException object
	 */

	public String display()
			throws SwtException {
HttpServletRequest request = ServletActionContext.getRequest();
HttpServletResponse response = ServletActionContext.getResponse();


		/* Variable Declaration for hostId */
		String hostId = EMPTYSTRING;
		/* Variable Declaration for entityId */
		String entityId = EMPTYSTRING;
		/* Variable Declaration for currencyCode */
		String currencyCode = EMPTYSTRING;
		/* Variable Declaration for selectedCurrencyGroup */
		String selectedCurrencyGroup = EMPTYSTRING;
		/* Variable Declaration for inputSource */
		String inputSource = EMPTYSTRING;
		/* Variable Declaration for matchStatus */
		String matchStatus = EMPTYSTRING;
		/* Variable Declaration for inputscreen */
		String inputscreen = EMPTYSTRING;
		/* Variable Declaration for userId */
		String userId = EMPTYSTRING;
		/* Variable Declaration for dyForm */
		// DynaValidatorForm dyForm = null;
		/* Variable Declaration for movement */
		Movement movement = null;
		/* Variable Declaration for todayDate */
		String todayDate = null;
		/* Variable Declaration for todayDatePlusOne */
		String todayDatePlusOne = null;
		/* Variable Declaration for todayDatePlusTwo */
		String todayDatePlusTwo = null;
		/* Variable Declaration for searchDate */
		String searchDate = EMPTYSTRING;
		/* Variable Declaration for defaultCurrencyCode */
		String defaultCurrencyCode = null;
		/* Variable Declaration for dateFlag */
		String dateFlag = null;
		/* Variable Declaration for accessInd */
		int accessInd = 0;
		/* Variable Declaration for options */
		String options = null;
		/* Variable Declaration for sourceDate */
		Date sourceDate = null;
		/* Variable Declaration for sysformat */
		SystemFormats sysformat = null;
		/* Variable Declaration for calledFrom */
		String calledFrom = null;

		try {
			log.debug(this.getClass().getName() + " -[display]- " + "Entry");

			sysformat = SwtUtil.getCurrentSystemFormats(request.getSession());
			movement = (Movement) getMovement();

			/* Getting the current User Id from the Session */
			userId = SwtUtil.getCurrentUserId(request.getSession());

			/*
			 * Getting the host Id from the cache if available else from the
			 * swtCommon.properties
			 */
			// Modified for mantis 1443
			if (!SwtUtil.isEmptyOrNull(request.getParameter("hostId"))) 
				hostId = request.getParameter("hostId");
			else
				hostId = putHostIdListInReq(request);

			/* Setting hostid in movement object */
			movement.getId().setHostId(hostId);

			entityId = ((request.getParameter("entityId") != null) && (request
					.getParameter("entityId").trim().length() > 0)) ? request
					.getParameter("entityId") : ((movement.getId()
					.getEntityId() != null) ? movement.getId().getEntityId()
					: SwtUtil.getUserCurrentEntity(request.getSession()));

			/*
			 * Setting entityId in movement object if available in request else
			 * getting from Session
			 */
			movement.getId().setEntityId(entityId);

			/*
			 * if screen is excuted from genric than append selectedCurrencyGroup from currencyGroup of the selected currencyId
			 */
			calledFrom = request.getParameter("calledFrom");
			if(calledFrom!=null&&calledFrom.equals("generic")){
				
			selectedCurrencyGroup =  SwtUtil.getCurrencyGroup(entityId,request
						.getParameter("currencyId" ) );
			}else {
			selectedCurrencyGroup = request
					.getParameter("selectedCurrencyGroup");
			}
			if ((selectedCurrencyGroup != null)
					&& (selectedCurrencyGroup.trim().length() > 0)
					&& !selectedCurrencyGroup.trim().equals(
							SwtConstants.ALL_VALUE)) {
				defaultCurrencyCode = putCurrencyFromCurrencyGroup(request,
						hostId, entityId, selectedCurrencyGroup);
				currencyCode = ((request.getParameter("currencyId") != null) && (request
						.getParameter("currencyId").trim().length() > 0)) ? request
						.getParameter("currencyId")
						: ((movement.getCurrencyCode() != null) ? movement
								.getCurrencyCode() : defaultCurrencyCode);
			} else {
				currencyCode = ((request.getParameter("currencyId") != null) && (request
						.getParameter("currencyId").trim().length() > 0)) ? request
						.getParameter("currencyId")
						: ((movement.getCurrencyCode() != null) ? movement
								.getCurrencyCode() : SwtUtil
								.getDomesticCurrencyForUser(request, SwtUtil
										.getCurrentHostId(), entityId));
				putCurrencyListInReq(request, hostId, entityId);

			}
			/* Setting entityId in movement object if available in request */
			movement.setCurrencyCode(currencyCode);

			inputSource = ((request.getParameter("source") != null) && (request
					.getParameter("source").trim().length() > 0)) ? request
					.getParameter("source")
					: ((movement.getInputSource() != null) ? movement
							.getInputSource() : DEFAULTSEARCHSTRING);

			inputSource = (((inputSource != null) && (inputSource.trim()
					.length() > 0)) ? inputSource : DEFAULTSEARCHSTRING);
			if (((request.getParameter("inputScreen") != null) && request
					.getParameter("inputScreen").equalsIgnoreCase("M"))
					|| ((request.getParameter("fromMenu") != null) && request
							.getParameter("fromMenu").equalsIgnoreCase("yes"))) {

				movement.setInputSource(inputSource);
				request.setAttribute("source", inputSource);

			} else {

				if (inputSource.equals("All")) {
					inputSource = request.getParameter("inputSourceValue");
					movement.setInputSource(inputSource);
					request.setAttribute("source", inputSource);
				}
				movement.setInputSource(inputSource);
				request.setAttribute("source", inputSource);
			}

			if (((request.getParameter("inputScreen") != null) && request
					.getParameter("inputScreen").equalsIgnoreCase("M"))
					|| ((request.getParameter("fromMenu") != null) && request
							.getParameter("fromMenu").equalsIgnoreCase("yes"))) {

				matchStatus = REFERED_MATCHSTATUS;
				request.setAttribute("fromMenu", "yes");

			} else {
				matchStatus = ((request.getParameter("selectedMatchStatus") != null) && (request
						.getParameter("selectedMatchStatus").trim().length() > 0)) ? request
						.getParameter("selectedMatchStatus")
						: ((movement.getMatchStatus() != null) ? movement
								.getMatchStatus() : DEFAULTSEARCHSTRING);

			}

			/* Setting matchStatus as 'R' in movement object */
			movement.setMatchStatus(matchStatus);

			inputscreen = (matchStatus.equals("A") ? SCREEN_STATUS_AUTH
					: SCREEN_STATUS_REFER);

			dateFlag = (((request.getParameter("dateFlag") != null) && (request
					.getParameter("dateFlag").trim().length() > 0)) ? request
					.getParameter("dateFlag") : EMPTYSTRING); // empty means
			// for all date.

			/* Setting dateFlag in Request object */
			request.setAttribute("dateFlag", dateFlag);
			if (!dateFlag.equals(EMPTYSTRING)) {
				/* Setting the today's,today + 1,today + 2 date */
				todayDate = SwtUtil.getSystemDateString();
				/*
				 * Start: Mantis Issue 1306 : Code Removed for column in
				 * weekend1 and weekend2 by Arumugam on 08-Mar-2011
				 */
				todayDatePlusOne = SwtUtil
						.getSystemDatePluswithoutTimeAsString(
								PropertiesFileLoader
										.getInstance()
										.getFacilityPropertiesValue(
												SwtConstants.FACILITY_INPUT_AUTHORISE_QUEUE),
								SwtUtil.getUserCurrentEntity(request
										.getSession()), currencyCode, 1, null);
				todayDatePlusTwo = SwtUtil
						.getSystemDatePluswithoutTimeAsString(
								PropertiesFileLoader
										.getInstance()
										.getFacilityPropertiesValue(
												SwtConstants.FACILITY_INPUT_AUTHORISE_QUEUE),
								SwtUtil.getUserCurrentEntity(request
										.getSession()), currencyCode, 2, null);
				/*
				 * End: Mantis Issue 1306 : Code Removed for column in weekend1
				 * and weekend2 by Arumugam on 08-Mar-2011
				 */

				/* Depends upon the dateFlag the date will be assigned */
				if (dateFlag.equals(todayDate)) {
					searchDate = todayDate;
					sourceDate = SwtUtil.parseDate(searchDate, SwtUtil
							.getCurrentDateFormat(request.getSession()));
					Format formatter = new SimpleDateFormat("dd/MM/yyyy");
					searchDate = formatter.format(sourceDate);
				} else if (dateFlag.equals(todayDatePlusOne)) {
					searchDate = todayDatePlusOne;
					sourceDate = SwtUtil.parseDate(searchDate, SwtUtil
							.getCurrentDateFormat(request.getSession()));
					Format formatter = new SimpleDateFormat("dd/MM/yyyy");
					searchDate = formatter.format(sourceDate);
				} else if (dateFlag.equals(todayDatePlusTwo)) {
					searchDate = todayDatePlusTwo;
					sourceDate = SwtUtil.parseDate(searchDate, SwtUtil
							.getCurrentDateFormat(request.getSession()));
					Format formatter = new SimpleDateFormat("dd/MM/yyyy");
					searchDate = formatter.format(sourceDate);
				} else {
					searchDate = EMPTYSTRING;
				}
			}
			/*
			 * Calls preAdviceInputManager getAccountMonitorDetails to fetch
			 * record set from the database
			 */
			preAdviceInputManager.getMovementWithMatchStatus(request, movement
					.getId().getHostId(), movement.getId().getEntityId(),
					movement.getCurrencyCode(), movement.getInputSource(),
					movement.getMatchStatus(), sysformat, searchDate, userId);

			/*
			 * Setting MovmtButton in Request object based on the Manual or
			 * PreAdvice
			 */
			request
					.setAttribute(
							"MovmtButton",
							(movement.getInputSource().equalsIgnoreCase(
									SwtConstants.MOVEMENT_SOURCE_DEFAULT) || movement
									.getInputSource()
									.equalsIgnoreCase(
											SwtConstants.MOVEMENT_SOURCE_PREADVICE)) ? "true"
									: EMPTYSTRING);

			/*
			 * Class Local Method used to set the button status,Entity,Source in
			 * request
			 */
			putSubmitButtonStatus(request, movement.getId().getEntityId(),
					movement.getCurrencyCode());
			putAuthButtonStatus(request, entityId, currencyCode);
			putRefButtonStatus(request, entityId, currencyCode, inputSource);
			putEntityListInReq(request);
			putSourcesInReq(request);

			/* Getting the Entity & currency access for Entity access */
			accessInd = SwtUtil.getMenuEntityCurrGrpAccess(request, entityId,
					currencyCode);

			if (accessInd == 0) {
				setButtonStatus(request, SwtConstants.STR_TRUE,
						SwtConstants.STR_FALSE, SwtConstants.STR_FALSE);

				request.setAttribute("EntityAccess",
						SwtConstants.ENTITY_FULL_ACCESS + "");
			} else {
				request.setAttribute("EntityAccess",
						SwtConstants.ENTITY_READ_ACCESS + "");
			}

			/* To set the option value in Request object */
			options = request.getParameter("options");
			options = ((options != null) && (options.trim().length() > 0)) ? options
					: "Y";
			request.setAttribute("options", options);

			/* Class Local Method used to set the description in Request object */
			putDescriptionsInRequest(request, movement);

			/* Setting the movement in dynaForm object */
			setMovement(movement);

			/*
			 * Setting input screen as ( Authorize or Referer
			 * ),showMovementId,methodName in Request object
			 */
			request.setAttribute("inputscreen", inputscreen);
			request.setAttribute("showMovementId", "yes");
			request.setAttribute("methodName", "success");
			/*
			 * Code added by venkat on 28-mar-2011 for Mantis 1308:"Show 'Last
			 * refresh' time in screens that have refresh function"
			 */
			request.setAttribute("lastRefTime", SwtUtil.getLastRefTimeOnGMTOffset(request,
					entityId));
			log.debug(this.getClass().getName() + " - [display()] - " + "Exit");
			return ("success");
		} catch (SwtException swtexp) {

			log.error(this.getClass().getName()
					+ " - Exception Catched in [display()] method : - "
					+ swtexp.getMessage());
			SwtUtil.logException(swtexp, request, "");
			return ("fail");

		} catch (Exception e) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [display()] method : - "
					+ e.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					e, "display", PreAdviceInputAction.class), request, "");
			return ("fail");
		} finally {
			/* null the objects created already. */
			movement = null;
			todayDate = null;
			todayDatePlusOne = null;
			todayDatePlusTwo = null;
			defaultCurrencyCode = null;
			dateFlag = null;
			options = null;
			sourceDate = null;
			sysformat = null;
		}
	}

	/**
	 * This method is used to get the list of associated currency from given
	 * currency group.
	 * 
	 * @param HttpServletRequest
	 *            request
	 * @param String
	 *            hostId
	 * @param String
	 *            entityId
	 * @param String
	 *            selectedCurrencyGroup
	 * @throws SwtException -
	 *             SwtException object
	 */
	private String putCurrencyFromCurrencyGroup(HttpServletRequest request,
			String hostId, String entityId, String selectedCurrencyGroup)
			throws SwtException {

		log.debug(this.getClass().getName()
				+ " - [ putCurrencyFromCurrencyGroup () ]- " + "Entry ");

		/* Method's local variable declaration */
		String firstCurrencyCodeInList = EMPTYSTRING;
		CurrencyGroupManager currencyGroupManager;
		Collection coll;
		Collection currencyDropDown = new ArrayList();
		int indexCurrencyCodeInList = 0;

		/*
		 * Calls currencyGroupManager getCurrencyGroupCurrenciesList to fetch
		 * currency for the currency group from the database
		 */
		currencyGroupManager = ((CurrencyGroupManager) (SwtUtil
				.getBean("currencyGroupManager")));
		coll = currencyGroupManager.getCurrencyGroupCurrenciesList(hostId,
				entityId, selectedCurrencyGroup);

		if (coll != null) {
			currencyDropDown.add(new LabelValueBean(SwtConstants.ALL_LABEL,
					SwtConstants.ALL_VALUE));

			Iterator it_col = coll.iterator();

			while (it_col.hasNext()) {
				Currency curr = (Currency) (it_col.next());
				currencyDropDown.add(new LabelValueBean(curr
						.getCurrencyMaster().getCurrencyName(), curr.getId()
						.getCurrencyCode()));

				if (indexCurrencyCodeInList == 0) {
					firstCurrencyCodeInList = curr.getId().getCurrencyCode();
					indexCurrencyCodeInList++;
				}
			}
		}

		/* Setting currencies,selectedCurrencyGroup in Request Object */
		request.setAttribute("currencies", currencyDropDown);
		request.setAttribute("selectedCurrencyGroup", selectedCurrencyGroup);

		log.debug(this.getClass().getName()
				+ " - [ putCurrencyFromCurrencyGroup () ]- " + " Exit");

		return firstCurrencyCodeInList;
	}

	/**
	 * This method is used to set the button status
	 * 
	 * @param HttpServletRequest
	 *            request
	 * @param String
	 *            addStatus
	 * @param String
	 *            changeStatus
	 * @param String
	 *            deleteStatus
	 * @throws SwtException
	 */
	private void setButtonStatus(HttpServletRequest req, String addStatus,
			String changeStatus, String deleteStatus) throws SwtException {

		log.debug(this.getClass().getName() + " - [ setButtonStatus () ]- "
				+ " Entry");

		/* Setting addStatus,changeStatus,deleteStatus in Request Object */
		req.setAttribute(SwtConstants.ADD_BUT_STS, addStatus);
		req.setAttribute(SwtConstants.CHG_BUT_STS, changeStatus);
		req.setAttribute(SwtConstants.DEL_BUT_STS, deleteStatus);
		log.debug(this.getClass().getName() + " - [ setButtonStatus () ]- "
				+ " Exit");
	}

	/**
	 * This action is used to authorize selected Movements
	 * 
	 * @param ActionMapping
	 *            mapping
	 * @param ActionForm
	 *            form
	 * @param HttpServletRequest
	 *            request
	 * @param HttpServletResponse
	 *            response
	 * @return - ActionForward object
	 * @throws SwtException -
	 *             SwtException object
	 */
	public String authoriseMovement() throws SwtException {
HttpServletRequest request = ServletActionContext.getRequest();
HttpServletResponse response = ServletActionContext.getResponse();


		String hostId = EMPTYSTRING;
		String entityId = EMPTYSTRING;
		String movementIds = EMPTYSTRING;
		String currencyCode = EMPTYSTRING;
		String inputSource = EMPTYSTRING;
		String userId = EMPTYSTRING;
		Movement movement = null;
		Movement updatedMovement = null;
		ArrayList<String> movIdList = null;
		String todayDate = null;
		String todayDatePlusOne = null;
		String todayDatePlusTwo = null;
		String searchDate = EMPTYSTRING;
		String dateFlag = null;
		int entityAcess;
		Date sourceDate = null;
		SystemFormats sysformat = null;

		try {
			log.debug("[ InputAuthoriseAction ]"
					+ " - [ authoriseMovement() ] - " + " Entry ");
			movIdList = new ArrayList<String>();

			sysformat = SwtUtil.getCurrentSystemFormats(request.getSession());

			/* Setting the today's,today + 1,today + 2 date */
			todayDate = SwtUtil.getSystemDateString();
			/*
			 * Start: Mantis Issue 1306 : Code Removed for column in weekend1
			 * and weekend2 by Arumugam on 08-Mar-2011
			 */
			todayDatePlusOne = SwtUtil
					.getSystemDatePluswithoutTimeAsString(
							PropertiesFileLoader
									.getInstance()
									.getFacilityPropertiesValue(
											SwtConstants.FACILITY_INPUT_AUTHORISE_QUEUE),
							SwtUtil.getUserCurrentEntity(request.getSession()),
							currencyCode, 1, null);
			todayDatePlusTwo = SwtUtil
					.getSystemDatePluswithoutTimeAsString(
							PropertiesFileLoader
									.getInstance()
									.getFacilityPropertiesValue(
											SwtConstants.FACILITY_INPUT_AUTHORISE_QUEUE),
							SwtUtil.getUserCurrentEntity(request.getSession()),
							currencyCode, 2, null);
			/*
			 * End: Mantis Issue 1306 : Code Removed for column in weekend1 and
			 * weekend2 by Arumugam on 08-Mar-2011
			 */

			/*
			 * Getting the host Id from the cache if available else from the
			 * swtCommon.properties
			 */
			hostId = putHostIdListInReq(request);

			/* Getting the current User Id from the Session */
			userId = SwtUtil.getCurrentUserId(request.getSession());

			/*
			 * Getting the entityid,movementIds,currencyCode,inputSource from
			 * the request parameter
			 */
			entityId = request.getParameter("entityId").trim();
			movementIds = request.getParameter("selectedMovementId").trim();
			currencyCode = request.getParameter("currencyCode").trim();
			inputSource = request.getParameter("inputSourceValue").trim();

			request.setAttribute("source", inputSource);
			movIdList = getMovementIdList(movementIds);
			// DynaValidatorForm movIdList = getMovementIdList(movementIds);

			Iterator<String> it_movId = movIdList.iterator();

			/*
			 * Iterating the movement Id and passing to preAdviceInputManager's
			 * getMovement,manageMovementAlert, updateMovementDetails
			 */
			while (it_movId.hasNext()) {
				String tempMoveId = it_movId.next().toString();

				updatedMovement = preAdviceInputManager.getMovement(hostId,
						entityId, new Long(tempMoveId.trim()));

				preAdviceInputManager.manageMovementAlert(updatedMovement,
						updatedMovement.getMatchStatus(),
						SwtConstants.OUTSTANDING_STATUS);

				updatedMovement.setMatchStatus(SwtConstants.OUTSTANDING_STATUS);

				updatedMovement.setUpdateDate(new Date());

				updatedMovement.setUpdateUser(userId);

				preAdviceInputManager.updateMovementDetails(updatedMovement,
						null);
			}

			if ((inputSource == null) || (inputSource.trim().length() == 0)) {
				inputSource = DEFAULTSEARCHSTRING;
			}

			/*
			 * Setting the HostId,entityId,inputSource,currencyCode in movement
			 * object
			 */
			movement = (Movement) getMovement();
			movement.getId().setHostId(hostId);
			movement.getId().setEntityId(entityId);
			movement.setInputSource(inputSource);
			movement.setCurrencyCode(currencyCode);

			/*
			 * Setting the dateFlag in request object if present else
			 * EMPTYSTRING
			 */
			dateFlag = (((request.getParameter("dateFlag") != null) && (request
					.getParameter("dateFlag").trim().length() > 0)) ? request
					.getParameter("dateFlag") : EMPTYSTRING); // EMPTYSTRING
			// means for all
			// date.

			request.setAttribute("dateFlag", dateFlag);
			Format formatter = new SimpleDateFormat("dd/MM/yyyy");
			if (dateFlag.equals(todayDate)) {
				sourceDate = SwtUtil.parseDate(todayDate, SwtUtil
						.getCurrentDateFormat(request.getSession()));
				searchDate = formatter.format(sourceDate);
			} else if (dateFlag.equals(todayDatePlusOne)) {
				sourceDate = SwtUtil.parseDate(todayDatePlusOne, SwtUtil
						.getCurrentDateFormat(request.getSession()));
				searchDate = formatter.format(sourceDate);
			} else if (dateFlag.equals(todayDatePlusTwo)) {
				sourceDate = SwtUtil.parseDate(todayDatePlusTwo, SwtUtil
						.getCurrentDateFormat(request.getSession()));
				searchDate = formatter.format(sourceDate);
			} else {
				searchDate = EMPTYSTRING;
			}

			/*
			 * Calls preAdviceInputManager's getMovementWithMatchStatus to fetch
			 * record set from the database
			 */
			preAdviceInputManager.getMovementWithMatchStatus(request, movement
					.getId().getHostId(), movement.getId().getEntityId(),
					currencyCode, inputSource, AUTHORISE_MATCHSTATUS,
					sysformat, searchDate, userId);

			/*
			 * Setting MovmtButton in Request object based on the Manual or
			 * PreAdvice
			 */
			request
					.setAttribute(
							"MovmtButton",
							(movement.getInputSource().equalsIgnoreCase(
									SwtConstants.MOVEMENT_SOURCE_DEFAULT) || movement
									.getInputSource()
									.equalsIgnoreCase(
											SwtConstants.MOVEMENT_SOURCE_PREADVICE)) ? "true"
									: EMPTYSTRING);

			/*
			 * Class Local Method used to set the button
			 * status,Entity,Source,currency in request
			 */
			putSubmitButtonStatus(request, movement.getId().getEntityId(),
					movement.getCurrencyCode());
			putAuthButtonStatus(request, entityId, currencyCode);
			putRefButtonStatus(request, entityId, currencyCode, inputSource);
			putEntityListInReq(request);
			putSourcesInReq(request);
			putCurrencyListInReq(request, movement.getId().getHostId(),
					movement.getId().getEntityId());

			Collection<EntityUserAccess> coll = SwtUtil
					.getUserEntityAccessList(request.getSession());
			entityAcess = SwtUtil.getUserEntityAccess(coll, entityId);

			if (entityAcess == SwtConstants.ENTITY_FULL_ACCESS) {
				setButtonStatus(request, SwtConstants.STR_TRUE,
						SwtConstants.STR_FALSE, SwtConstants.STR_FALSE);

				request.setAttribute("EntityAccess",
						SwtConstants.ENTITY_FULL_ACCESS + "");
			} else {
				request.setAttribute("EntityAccess",
						SwtConstants.ENTITY_READ_ACCESS + "");
			}

			movement = (Movement) getMovement();

			/*
			 * Setting options in the request object got from the request
			 * parameter
			 */
			String options = request.getParameter("options");
			options = ((options != null) && (options.trim().length() > 0)) ? options
					: "Y";
			request.setAttribute("options", options);

			/* Class Local Method used to set the description in Request object */
			putDescriptionsInRequest(request, movement);

			setMovement(movement);

			/*
			 * Setting input screen as ( Authorize or Referer
			 * ),parentFormRefresh,showMovementId,methodName in Request object
			 */
			request.setAttribute("inputscreen", SCREEN_STATUS_AUTH);
			request.setAttribute("parentFormRefresh", "yes");
			request.setAttribute("showMovementId", "yes");
			request.setAttribute("methodName", "success");
			/*
			 * Code added by venkat on 14-Apr-2011 for Mantis 1308:"Show 'Last
			 * refresh' time in screens that have refresh function"
			 */
			request.setAttribute("lastRefTime", SwtUtil.getLastRefTimeOnGMTOffset(request,
					entityId));
			return ("success");
		} catch (SwtException swtexp) {
			log.error(this.getClass().getName()
					+ "- [ authoriseMovement() ] - " + swtexp.getMessage());
			SwtUtil.logException(swtexp, request, "");
			return ("fail");

		} catch (Exception e) {
			log.error(this.getClass().getName()
					+ "- [ authoriseMovement() ] - " + e.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					e, "authoriseMovement", PreAdviceInputAction.class),
					request, "");
			return ("fail");
		} finally {
			log.debug(this.getClass().getName()
					+ "- [ authoriseMovement() ] - Exit ");
		}
	}

	/**
	 * This action is used to refer selected Movements
	 * 
	 * @param ActionMapping
	 *            mapping
	 * @param ActionForm
	 *            form
	 * @param HttpServletRequest
	 *            request
	 * @param HttpServletResponse
	 *            response
	 * @return - ActionForward object
	 * @throws SwtException -
	 *             SwtException object
	 */
	public String refereMovement()
			throws SwtException {
HttpServletRequest request = ServletActionContext.getRequest();
HttpServletResponse response = ServletActionContext.getResponse();


		String hostId = EMPTYSTRING;
		String entityId = EMPTYSTRING;
		String movementIds = EMPTYSTRING;
		String currencyCode = EMPTYSTRING;
		String inputSource = EMPTYSTRING;
		Movement movement = null;
		Movement updatedMovement = null;
		ArrayList<String> movIdList = new ArrayList<String>();
		String userId = EMPTYSTRING;
		String todayDate = null;
		String todayDatePlusOne = null;
		String todayDatePlusTwo = null;
		String searchDate = EMPTYSTRING;
		Date sourceDate = null;
		SystemFormats sysformat = null;

		try {
			log.debug("[inputAuthoriseAction]" + "- refereMovement() - "
					+ " Entry ");
			sysformat = SwtUtil.getCurrentSystemFormats(request.getSession());

			/* Setting the today's,today + 1,today + 2 date */
			todayDate = SwtUtil.getSystemDateString();
			/*
			 * Start: Mantis Issue 1306 : Code Removed for column in weekend1
			 * and weekend2 by Arumugam on 08-Mar-2011
			 */
			todayDatePlusOne = SwtUtil
					.getSystemDatePluswithoutTimeAsString(
							PropertiesFileLoader
									.getInstance()
									.getFacilityPropertiesValue(
											SwtConstants.FACILITY_INPUT_AUTHORISE_QUEUE),
							SwtUtil.getUserCurrentEntity(request.getSession()),
							currencyCode, 1, null);
			todayDatePlusTwo = SwtUtil
					.getSystemDatePluswithoutTimeAsString(
							PropertiesFileLoader
									.getInstance()
									.getFacilityPropertiesValue(
											SwtConstants.FACILITY_INPUT_AUTHORISE_QUEUE),
							SwtUtil.getUserCurrentEntity(request.getSession()),
							currencyCode, 2, null);
			/*
			 * End: Mantis Issue 1306 : Code Removed for column in weekend1 and
			 * weekend2 by Arumugam on 08-Mar-2011
			 */
			/*
			 * Getting the host Id from the cache if available else from the
			 * swtCommon.properties
			 */
			hostId = putHostIdListInReq(request);

			/*
			 * Getting the entityid,movementIds,currencyCode,inputSource from
			 * the request parameter
			 */
			entityId = request.getParameter("entityId").trim();
			movementIds = request.getParameter("selectedMovementId").trim();
			currencyCode = request.getParameter("currencyCode").trim();
			inputSource = request.getParameter("inputSourceValue").trim();

			request.setAttribute("source", inputSource);

			// DynaValidatorForm /* Getting the current User Id from the Session */
			userId = SwtUtil.getCurrentUserId(request.getSession());

			/*
			 * Passing more than 1 movement ID's and tokenizing the movements
			 * and putting in Array List
			 */
			movIdList = getMovementIdList(movementIds);

			Iterator<String> it_movId = movIdList.iterator();

			/*
			 * Iterating the movement Id and passing to preAdviceInputManager's
			 * getMovement,manageMovementAlert, updateMovementDetails
			 */

			while (it_movId.hasNext()) {
				String tempMoveId = it_movId.next().toString();
				updatedMovement = preAdviceInputManager.getMovement(hostId,
						entityId, new Long(tempMoveId.trim()));

				preAdviceInputManager.manageMovementAlert(updatedMovement,
						updatedMovement.getMatchStatus(),
						SwtConstants.REFERRED_STATUS);

				updatedMovement.setMatchStatus(SwtConstants.REFERRED_STATUS);

				updatedMovement.setUpdateDate(SwtUtil.getSystemDatewithTime());

				updatedMovement.setUpdateUser(userId);

				preAdviceInputManager.updateMovementDetails(updatedMovement,
						null);
			}

			if ((inputSource == null) || (inputSource.trim().length() == 0)) {
				inputSource = DEFAULTSEARCHSTRING;
			}

			/*
			 * Setting the HostId,entityId,inputSource,currencyCode in movement
			 * object
			 */
			movement = (Movement) getMovement();
			movement.getId().setHostId(hostId);
			movement.getId().setEntityId(entityId);
			movement.setInputSource(inputSource);
			movement.setCurrencyCode(currencyCode);

			/*
			 * Setting the dateFlag in request object if present else
			 * EMPTYSTRING
			 */
			String dateFlag = (((request.getParameter("dateFlag") != null) && (request
					.getParameter("dateFlag").trim().length() > 0)) ? request
					.getParameter("dateFlag") : EMPTYSTRING); // EMPTYSTRING
			// means for all
			// date.
			request.setAttribute("dateFlag", dateFlag);

			/* Depends upon the dateFlag the date will be assigned */

			Format formatter = new SimpleDateFormat("dd/MM/yyyy");
			if (dateFlag.equals(todayDate)) {
				sourceDate = SwtUtil.parseDate(todayDate, SwtUtil
						.getCurrentDateFormat(request.getSession()));
				searchDate = formatter.format(sourceDate);
			} else if (dateFlag.equals(todayDatePlusOne)) {
				sourceDate = SwtUtil.parseDate(todayDatePlusOne, SwtUtil
						.getCurrentDateFormat(request.getSession()));
				searchDate = formatter.format(sourceDate);
			} else if (dateFlag.equals(todayDatePlusTwo)) {
				sourceDate = SwtUtil.parseDate(todayDatePlusTwo, SwtUtil
						.getCurrentDateFormat(request.getSession()));
				searchDate = formatter.format(sourceDate);
			} else {
				searchDate = EMPTYSTRING;
			}

			/*
			 * Calls preAdviceInputManager's getMovementWithMatchStatus to fetch
			 * record set from the database
			 */
			preAdviceInputManager.getMovementWithMatchStatus(request, movement
					.getId().getHostId(), movement.getId().getEntityId(),
					currencyCode, inputSource, AUTHORISE_MATCHSTATUS // As
					// this
					// function
					// is
					// always
					// called
					// for
					// "A"
					// status
					, sysformat, searchDate, userId);

			/*
			 * Setting MovmtButton in Request object based on the Manual or
			 * PreAdvice
			 */
			request
					.setAttribute(
							"MovmtButton",
							(movement.getInputSource().equalsIgnoreCase(
									SwtConstants.MOVEMENT_SOURCE_DEFAULT) || movement
									.getInputSource()
									.equalsIgnoreCase(
											SwtConstants.MOVEMENT_SOURCE_PREADVICE)) ? "true"
									: EMPTYSTRING);

			Collection<EntityUserAccess> coll = SwtUtil
					.getUserEntityAccessList(request.getSession());

			int entityAcess = SwtUtil.getUserEntityAccess(coll, entityId);

			if (entityAcess == SwtConstants.ENTITY_FULL_ACCESS) {
				setButtonStatus(request, SwtConstants.STR_TRUE,
						SwtConstants.STR_FALSE, SwtConstants.STR_FALSE);

				request.setAttribute("EntityAccess",
						SwtConstants.ENTITY_FULL_ACCESS + "");
			} else {
				request.setAttribute("EntityAccess",
						SwtConstants.ENTITY_READ_ACCESS + "");
			}

			/*
			 * Class Local Method used to set the button
			 * status,Entity,Source,currency in request
			 */
			putSubmitButtonStatus(request, movement.getId().getEntityId(),
					movement.getCurrencyCode());
			putAuthButtonStatus(request, entityId, currencyCode);
			putRefButtonStatus(request, entityId, currencyCode, inputSource);
			putEntityListInReq(request);
			putSourcesInReq(request);
			putCurrencyListInReq(request, movement.getId().getHostId(),
					movement.getId().getEntityId());

			movement = (Movement) getMovement();

			/*
			 * Setting options in the request object got from the request
			 * parameter
			 */
			String options = request.getParameter("options");
			options = ((options != null) && (options.trim().length() > 0)) ? options
					: "Y";
			request.setAttribute("options", options);

			/* Class Local Method used to set the description in Request object */
			putDescriptionsInRequest(request, movement);

			setMovement(movement);

			request.setAttribute("inputscreen", SCREEN_STATUS_AUTH);
			request.setAttribute("parentFormRefresh", "yes");
			request.setAttribute("showMovementId", "yes");
			request.setAttribute("methodName", "success");
			/*
			 * Code added by venkat on 14-apr-2011 for Mantis 1308:"Show 'Last
			 * refresh' time in screens that have refresh function"
			 */
			request.setAttribute("lastRefTime", SwtUtil.getLastRefTimeOnGMTOffset(request,
					entityId));
			log.debug(this.getClass().getName()
					+ "- [ refereMovement() ] - Exit ");
			return ("success");
		} catch (SwtException swtexp) {
			log.error(this.getClass().getName() + " - [ refereMovement() ] - "
					+ swtexp.getMessage());
			SwtUtil.logException(swtexp, request, "");
			return ("fail");

		} catch (Exception e) {
			log.error(this.getClass().getName() + " - [ refereMovement() ] - "
					+ e.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					e, "refereMovement", PreAdviceInputAction.class), request,
					"");
			return ("fail");
		} finally {
			hostId = null;
			entityId = null;
			movementIds = null;
			currencyCode = null;
			inputSource = null;
			movement = null;
			updatedMovement = null;
			userId = null;
			todayDate = null;
			todayDatePlusOne = null;
			todayDatePlusTwo = null;
			searchDate = null;
			sourceDate = null;
			sysformat = null;

		}
	}

	/**
	 * This action is used to Submit selected Movements
	 * 
	 * @param ActionMapping
	 *            mapping
	 * @param ActionForm
	 *            form
	 * @param HttpServletRequest
	 *            request
	 * @param HttpServletResponse
	 *            response
	 * @return - ActionForward object
	 * @throws SwtException -
	 *             SwtException object
	 */
	public String submitMovement()
			throws SwtException {
HttpServletRequest request = ServletActionContext.getRequest();
HttpServletResponse response = ServletActionContext.getResponse();


		String hostId = EMPTYSTRING;
		String entityId = EMPTYSTRING;
		String movementIds = EMPTYSTRING;
		String currencyCode = EMPTYSTRING;
		String inputSource = EMPTYSTRING;
		String fromMenu = EMPTYSTRING;
		String userId = EMPTYSTRING;
		String roleId = EMPTYSTRING;
		String authorizeStatus = EMPTYSTRING;
		Movement movement = null;
		Movement updatedMovement = null;
		ArrayList<String> movIdList = new ArrayList<String>();
		String todayDate = null;
		String todayDatePlusOne = null;
		String todayDatePlusTwo = null;
		String searchDate = EMPTYSTRING;
		SystemFormats sysformat = null;

		try {
			log.debug(this.getClass().getName() + " - [ submitMovement ] - "
					+ " Entry ");
			sysformat = SwtUtil.getCurrentSystemFormats(request.getSession());

			/* Setting the today's,today + 1,today + 2 date */
			todayDate = SwtUtil.getSystemDateString();
			/*
			 * Start: Mantis Issue 1306 : Code Removed for column in weekend1
			 * and weekend2 by Arumugam on 08-Mar-2011
			 */
			todayDatePlusOne = SwtUtil
					.getSystemDatePluswithoutTimeAsString(
							PropertiesFileLoader
									.getInstance()
									.getFacilityPropertiesValue(
											SwtConstants.FACILITY_INPUT_AUTHORISE_QUEUE),
							SwtUtil.getUserCurrentEntity(request.getSession()),
							currencyCode, 1, null);
			todayDatePlusTwo = SwtUtil
					.getSystemDatePluswithoutTimeAsString(
							PropertiesFileLoader
									.getInstance()
									.getFacilityPropertiesValue(
											SwtConstants.FACILITY_INPUT_AUTHORISE_QUEUE),
							SwtUtil.getUserCurrentEntity(request.getSession()),
							currencyCode, 2, null);
			/*
			 * End: Mantis Issue 1306 : Code Removed for column in weekend1 and
			 * weekend2 by Arumugam on 08-Mar-2011
			 */
			/*
			 * Getting the host Id from the cache if available else from the
			 * swtCommon.properties
			 */
			hostId = putHostIdListInReq(request);

			/* Getting the current User Id from the Session */
			userId = SwtUtil.getCurrentUserId(request.getSession());

			/* Calls preAdviceInputManager getUserDetails to get the role Id */
			roleId = preAdviceInputManager.getUserDetails(hostId, userId);

			/*
			 * Calls preAdviceInputManager getRoleDetails to get the Authorise
			 * status
			 */
			authorizeStatus = preAdviceInputManager.getRoleDetails(roleId);

			/*
			 * Getting the
			 * entityid,movementIds,currencyCode,inputSource,fromMenu from the
			 * request parameter
			 */
			entityId = request.getParameter("entityId").trim();
			movementIds = request.getParameter("selectedMovementId").trim();
			currencyCode = request.getParameter("currencyCode").trim();
			inputSource = request.getParameter("inputSourceValue").trim();
			fromMenu = request.getParameter("fromMenu").trim();

			/* Setting the fromMenu in request object */
			request.setAttribute("fromMenu", fromMenu);

			// DynaValidatorForm /*
			 /* Passing more than 1 movement ID's and tokenizing the movements
			 * and putting in Array List
			 */
			movIdList = getMovementIdList(movementIds);

			Iterator<String> it_movId = movIdList.iterator();

			/*
			 * Iterating the movement Id and passing to preAdviceInputManager's
			 * getMovement,manageMovementAlert, updateMovementDetails
			 */

			while (it_movId.hasNext()) {
				String tempMoveId = it_movId.next().toString();
				updatedMovement = preAdviceInputManager.getMovement(hostId,
						entityId, new Long(tempMoveId.trim()));

				preAdviceInputManager
						.manageMovementAlert(
								updatedMovement,
								updatedMovement.getMatchStatus(),
								(((authorizeStatus != null) && authorizeStatus
										.equalsIgnoreCase("Y")) ? SwtConstants.AUTHORISE_STATUS
										: SwtConstants.OUTSTANDING_STATUS));

				updatedMovement
						.setMatchStatus((((authorizeStatus != null) && authorizeStatus
								.equalsIgnoreCase("Y")) ? SwtConstants.AUTHORISE_STATUS
								: SwtConstants.OUTSTANDING_STATUS));

				updatedMovement.setUpdateDate(SwtUtil.getSystemDatewithTime());

				updatedMovement.setUpdateUser(userId);

				preAdviceInputManager.updateMovementDetails(updatedMovement,
						null);
			}

			inputSource = (((inputSource != null) && (inputSource.trim()
					.length() > 0)) ? inputSource : DEFAULTSEARCHSTRING);

			if ((fromMenu != null) && fromMenu.trim().equalsIgnoreCase("yes")) {
				inputSource = DEFAULTSEARCHSTRING;
			}

			/* Setting the inputSource in request object */
			request.setAttribute("source", inputSource);

			/*
			 * Setting the HostId,entityId,inputSource,currencyCode in movement
			 * object
			 */
			movement = (Movement) getMovement();
			movement.getId().setHostId(hostId);
			movement.getId().setEntityId(entityId);
			movement.setInputSource(inputSource);
			movement.setCurrencyCode(currencyCode);

			/*
			 * Setting the dateFlag in request object if present else
			 * EMPTYSTRING
			 */
			String dateFlag = (((request.getParameter("dateFlag") != null) && (request
					.getParameter("dateFlag").trim().length() > 0)) ? request
					.getParameter("dateFlag") : EMPTYSTRING); // EMPTYSTRING
			// means for all
			// date.
			request.setAttribute("dateFlag", dateFlag);

			/* Depends upon the dateFlag the date will be assigned */
			Date sourceDate;
			Format formatter = new SimpleDateFormat("dd/MM/yyyy");
			if (dateFlag.equals(todayDate)) {
				sourceDate = SwtUtil.parseDate(todayDate, SwtUtil
						.getCurrentDateFormat(request.getSession()));
				searchDate = formatter.format(sourceDate);
			} else if (dateFlag.equals(todayDatePlusOne)) {
				sourceDate = SwtUtil.parseDate(todayDatePlusOne, SwtUtil
						.getCurrentDateFormat(request.getSession()));
				searchDate = formatter.format(sourceDate);
			} else if (dateFlag.equals(todayDatePlusTwo)) {
				sourceDate = SwtUtil.parseDate(todayDatePlusTwo, SwtUtil
						.getCurrentDateFormat(request.getSession()));
				searchDate = formatter.format(sourceDate);
			} else {
				searchDate = EMPTYSTRING;
			}

			/*
			 * Calls preAdviceInputManager's getMovementWithMatchStatus to fetch
			 * record set from the database
			 */
			preAdviceInputManager.getMovementWithMatchStatus(request, hostId,
					entityId, currencyCode, inputSource, REFERED_MATCHSTATUS // As
					// this
					// function
					// is
					// always
					// called
					// for
					// "R"
					// status
					, sysformat, searchDate, userId);

			/*
			 * Setting MovmtButton in Request object based on the Manual or
			 * PreAdvice
			 */
			request
					.setAttribute(
							"MovmtButton",
							(movement.getInputSource().equalsIgnoreCase(
									SwtConstants.MOVEMENT_SOURCE_DEFAULT) || movement
									.getInputSource()
									.equalsIgnoreCase(
											SwtConstants.MOVEMENT_SOURCE_PREADVICE)) ? "true"
									: EMPTYSTRING);

			/*
			 * Class Local Method used to set the Submit button status in
			 * request
			 */
			putSubmitButtonStatus(request, movement.getId().getEntityId(),
					movement.getCurrencyCode());

			Collection<EntityUserAccess> coll = SwtUtil
					.getUserEntityAccessList(request.getSession());

			int entityAcess = SwtUtil.getUserEntityAccess(coll, entityId);

			if (entityAcess == SwtConstants.ENTITY_FULL_ACCESS) {
				setButtonStatus(request, SwtConstants.STR_TRUE,
						SwtConstants.STR_FALSE, SwtConstants.STR_FALSE);

				request.setAttribute("EntityAccess",
						SwtConstants.ENTITY_FULL_ACCESS + "");
			} else {
				request.setAttribute("EntityAccess",
						SwtConstants.ENTITY_READ_ACCESS + "");
			}

			/*
			 * Class Local Method used to set the Entity,Source,currency list in
			 * Request Object
			 */
			putEntityListInReq(request);
			putSourcesInReq(request);
			putCurrencyListInReq(request, movement.getId().getHostId(),
					movement.getId().getEntityId());

			movement = (Movement) getMovement();

			/*
			 * Setting options in the request object got from the request
			 * parameter
			 */
			String options = request.getParameter("options");
			options = ((options != null) && (options.trim().length() > 0)) ? options
					: "Y";
			request.setAttribute("options", options);

			/* Class Local Method used to set the description in Request object */
			putDescriptionsInRequest(request, movement);

			setMovement(movement);

			/* Setting input screen in the request object as "refere" */
			request.setAttribute("inputscreen", SCREEN_STATUS_REFER);
			String refreshAuthSelection = request
					.getParameter("refreshAuthSelection");
			if (((request.getParameter("inputScreen") != null) && request
					.getParameter("inputScreen").trim().equalsIgnoreCase(
							SCREEN_STATUS_REFER))
					|| (refreshAuthSelection != null && "Y"
							.equalsIgnoreCase(refreshAuthSelection))) {
				request.setAttribute("parentFormRefresh", "yes");
			} else {
				request.setAttribute("parentFormRefresh", "no");
			}

			/* Setting showMovementId,methodName in the request object */
			request.setAttribute("showMovementId", "yes");
			request.setAttribute("methodName", "success");
			/*
			 * Code added by venkat on 14-Apr-2011 for Mantis 1308:"Show 'Last
			 * refresh' time in screens that have refresh function"
			 */
			request.setAttribute("lastRefTime", SwtUtil.getLastRefTimeOnGMTOffset(request,
					entityId));
			return ("success");
		} catch (SwtException swtexp) {
			log.error(this.getClass().getName() + " - [ submitMovement ] - "
					+ swtexp.getMessage());
			SwtUtil.logException(swtexp, request, ""); /*
														 * Model_name name of
														 * POJO in name in
														 * java.lang.String
														 */
			return ("fail");

		} catch (Exception e) {
			e.printStackTrace();
			log.error(this.getClass().getName() + " - [ submitMovement ] - "
					+ e.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					e, "submitMovement", PreAdviceInputAction.class), request,
					"");
			return ("fail");
		} finally {
			log.debug(this.getClass().getName()
					+ " - [ submitMovement ] - Exit ");
		}
	}

	/**
	 * This method is used to Tokenize the selected movement Id's comes from the
	 * screen
	 * 
	 * @param String
	 *            movIds
	 * @return - ArrayList movIdList
	 */
	private ArrayList<String> getMovementIdList(String movIds) {

		log.debug(this.getClass().getName() + " - [ getMovementIdList ] - "
				+ " Entry ");

		ArrayList<String> movIdList = new ArrayList<String>();

		StringTokenizer st = new StringTokenizer(movIds, ",");

		/*
		 * Iterating the movement Id's with comma separated values that comes
		 * from the screen
		 */
		while (st.hasMoreTokens()) {
			String movId = st.nextToken();
			movIdList.add(movId);
		}

		log.debug(this.getClass().getName() + " - [ getMovementIdList ] - "
				+ " Exit ");

		return movIdList;

	}

	/**
	 * This method is used to put host Id into request object.
	 * 
	 * @param HttpServletRequest
	 *            request
	 * @return string hostId
	 * @throws SwtException
	 */
	private String putHostIdListInReq(HttpServletRequest request)
			throws SwtException {
		return CacheManager.getInstance().getHostId();
	}

	/**
	 * This method is used to put collection of entity list into request.
	 * 
	 * @param HttpServletRequest
	 *            request
	 * @throws SwtException
	 */
	private void putEntityListInReq(HttpServletRequest request)
			throws SwtException {

		log.debug(this.getClass().getName() + " - [ putEntityListInReq ] - "
				+ " Entry ");

		HttpSession session = request.getSession();

		Collection entities = SwtUtil.getUserEntityAccessList(session);

		entities = SwtUtil.convertEntityAcessCollectionLVL(entities, session);

		/* Setting the Entities in Request object */
		request.setAttribute("entities", entities);

		log.debug(this.getClass().getName() + " - [ putEntityListInReq ] - "
				+ " Exit ");

	}

	/**
	 * This method is used to put currency list into request.
	 * 
	 * @param HttpServletRequest
	 *            request
	 * @param String
	 *            hostId
	 * @param String
	 *            entityId
	 * @throws SwtException
	 */

	private void putCurrencyListInReq(HttpServletRequest request,
			String hostId, String entityId) throws SwtException {

		/* Method's local variable declaration */
		Collection currencyDropDown = new ArrayList();
		String roleId;

		log.debug(this.getClass().getName() + " - [ putCurrencyListInReq ] - "
				+ " Entry ");

		/* Getting the User's Role Id from the session object */
		roleId = ((CommonDataManager) request.getSession().getAttribute(
				SwtConstants.CDM_BEAN)).getUser().getRoleId();

		/* Getting the currencies with access */
		ArrayList currencyList = (ArrayList) SwtUtil.getSwtMaintenanceCache()
				.getCurrencyViewORFullAcessLVL(roleId, entityId);

		if (currencyList != null) {
			currencyList.remove(new LabelValueBean("Default", "*"));
		}

		currencyDropDown.add(new LabelValueBean(DEFAULTSEARCHSTRING,
				DEFAULTSEARCHSTRING));

		if (currencyList != null) {
			Iterator itr = currencyList.iterator();

			while (itr.hasNext()) {
				LabelValueBean lb = (LabelValueBean) (itr.next());
				String label = lb.getLabel();
				String value = lb.getValue();
				currencyDropDown.add(new LabelValueBean(label, value));
			}
		}
		/* Setting the currencies in request object */
		request.setAttribute("currencies", currencyDropDown);

		log.debug(this.getClass().getName() + " - [ putCurrencyListInReq ] - "
				+ " Exit ");

	}

	/**
	 * This method is used to sources into Request.
	 * 
	 * @param HttpServletRequest
	 *            request
	 * @throws SwtException
	 * 
	 */
	private void putSourcesInReq(HttpServletRequest request)
			throws SwtException {

		Collection sourceList = new ArrayList();

		log.debug(this.getClass().getName() + " - [ putSourcesInReq ] - "
				+ " Entry ");

		sourceList.add(new LabelValueBean(DEFAULTSEARCHSTRING,
				DEFAULTSEARCHSTRING));

		sourceList.add(new LabelValueBean(SwtConstants.MOVEMENT_SOURCE_DEFAULT,
				SwtConstants.MOVEMENT_SOURCE_DEFAULT));

		sourceList.add(new LabelValueBean(
				SwtConstants.MOVEMENT_SOURCE_PREADVICE,
				SwtConstants.MOVEMENT_SOURCE_PREADVICE));

		/* Setting the Movement Source in request object */
		request.setAttribute("sources", sourceList);

		log.debug(this.getClass().getName() + " - [ putSourcesInReq ] - "
				+ " Exit ");
	}

	/**
	 * This function is used to set Authorize Button status in Request
	 * 
	 * @param HttpServletRequest
	 *            request
	 * @param String
	 *            entityId
	 * @param String
	 *            currencyCode
	 */
	private void putAuthButtonStatus(HttpServletRequest request,
			String entityId, String currencyCode) throws SwtException {

		log.debug(this.getClass().getName() + " - [ putAuthButtonStatus ] - "
				+ " Entry ");

		int entityAccess = getEntityAccessType(request, entityId);
		int ccyGrpAccess = getCcyGrpAccessType(request, entityId, currencyCode);

		/*
		 * Setting the AuthButton value based on the Entity & Currency Full
		 * access in request object
		 */
		if ((entityAccess == SwtConstants.ENTITY_FULL_ACCESS)
				&& (ccyGrpAccess == SwtConstants.CURRENCYGRP_FULL_ACCESS)) {
			request.setAttribute("AuthButton", "true");
		}

		log.debug(this.getClass().getName() + " - [ putAuthButtonStatus ] - "
				+ " Exit ");
	}

	/**
	 * This function is used to set Referer Button status in Request
	 * 
	 * @param HttpServletRequest
	 *            request
	 * @param String
	 *            entityId
	 * @param String
	 *            currencyCode
	 * @param String
	 *            source
	 */
	private void putRefButtonStatus(HttpServletRequest request,
			String entityId, String currencyCode, String source)
			throws SwtException {

		log.debug(this.getClass().getName() + " - [ putRefButtonStatus ] - "
				+ " Entry ");

		int entityAccess = getEntityAccessType(request, entityId);
		int ccyGrpAccess = getCcyGrpAccessType(request, entityId, currencyCode);

		/*
		 * Setting the RefButton value based on the Entity,Currency Full
		 * access,movement source in request object
		 */
		if ((entityAccess == SwtConstants.ENTITY_FULL_ACCESS)
				&& (ccyGrpAccess == SwtConstants.CURRENCYGRP_FULL_ACCESS)
				&& (source
						.equalsIgnoreCase(SwtConstants.MOVEMENT_SOURCE_DEFAULT) || source
						.equalsIgnoreCase(SwtConstants.MOVEMENT_SOURCE_PREADVICE))) {

			request.setAttribute("RefButton", "true");
		}

		log.debug(this.getClass().getName() + " - [ putRefButtonStatus ] - "
				+ " Exit ");
	}

	/**
	 * This function is used to set Submit Button status in Request
	 * 
	 * @param HttpServletRequest
	 *            request
	 * @param String
	 *            entityId
	 * @param String
	 *            currencyCode
	 * @throws SwtException
	 */
	private void putSubmitButtonStatus(HttpServletRequest request,
			String entityId, String currencyCode) throws SwtException {

		int entityAccess;
		int ccyGrpAccess;

		log.debug(this.getClass().getName() + " - [ putSubmitButtonStatus ] - "
				+ " Entry ");

		entityAccess = getEntityAccessType(request, entityId);
		ccyGrpAccess = getCcyGrpAccessType(request, entityId, currencyCode);

		/*
		 * Setting the RefButton value based on the Entity,Currency Full access
		 * in request object
		 */

		if ((entityAccess == SwtConstants.ENTITY_FULL_ACCESS)
				&& (ccyGrpAccess == SwtConstants.CURRENCYGRP_FULL_ACCESS)) {
			request.setAttribute("SubmitButton", "true");
		}

		log.debug(this.getClass().getName() + " - [ putSubmitButtonStatus ] - "
				+ " Exit ");
	}

	/**
	 * This function is used to get Currency Group access
	 * 
	 * @param HttpServletRequest
	 *            request
	 * @param String
	 *            entityId
	 * @param String
	 *            currencyId
	 * @param int
	 *            ccyGrpAccess
	 * @throws SwtException
	 */
	private int getCcyGrpAccessType(HttpServletRequest request,
			String entityId, String currencyId) throws SwtException {

		/* Method's local variable declaration */
		int ccyGrpAccess;
		String hostId;
		String userId;
		String roleId;

		log.debug(this.getClass().getName() + " - [ getCcyGrpAccessType ] - "
				+ " Entry ");

		/* Getting the currency group Access for the hostId,userId,roleId */
		hostId = putHostIdListInReq(request);
		userId = SwtUtil.getCurrentUserId(request.getSession());
		roleId = preAdviceInputManager.getUserDetails(hostId, userId);
		ccyGrpAccess = SwtUtil.getSwtMaintenanceCache().getCurrencyAccess(
				roleId, entityId, currencyId);

		log.debug(this.getClass().getName() + " - [ getCcyGrpAccessType ] - "
				+ " Exit ");

		return ccyGrpAccess;
	}

	/**
	 * This function is used to get Currency Group access
	 * 
	 * @param HttpServletRequest
	 *            request
	 * @param String
	 *            entityId
	 * @param int
	 *            entityAccess
	 */
	private int getEntityAccessType(HttpServletRequest request, String entityId) {

		/* Method's local variable declaration */
		Collection collEntity;
		int entityAccess;

		log.debug(this.getClass().getName() + " - [ getEntityAccessType ] - "
				+ " Entry ");

		collEntity = SwtUtil.getUserEntityAccessList(request.getSession());

		/* Gets the user Entity access type */
		entityAccess = SwtUtil.getUserEntityAccess(collEntity, entityId);

		log.debug(this.getClass().getName() + " - [ getEntityAccessType ] - "
				+ " Exit ");

		return entityAccess;
	}

	/**
	 * This function puts the description corresponding to accountId,
	 *  bookcode, entityId in the request
	 * 
	 * @param HttpServletRequest
	 *            request
	 * @param Movement
	 *            movement
	 */
	private void putDescriptionsInRequest(HttpServletRequest request,
			Movement movement) {

		/* Method's local variable declaration */
		Collection collEntity;
		Collection collcurrencies;
		String entityId = null;
		String currencyCode = null;

		log.debug(this.getClass().getName()
				+ " - [ putDescriptionsInRequest ] - " + " Entry ");

		collEntity = (Collection) request.getAttribute("entities");
		collcurrencies = (Collection) request.getAttribute("currencies");

		if (movement != null) {
			entityId = movement.getId().getEntityId();
			currencyCode = movement.getCurrencyCode();
		}

		/* Iterates to get the Entity description */
		if (collEntity != null) {
			Iterator itr = collEntity.iterator();

			while (itr.hasNext()) {
				LabelValueBean lvb = (LabelValueBean) (itr.next());

				if (lvb.getValue().equals(entityId)) {
					request.setAttribute("entityDesc", lvb.getLabel());
					break;
				}
			}
		}

		/* Iterates to get the Currency description */
		if (collcurrencies != null) {
			Iterator itr_collcurrencies = collcurrencies.iterator();

			while (itr_collcurrencies.hasNext()) {
				LabelValueBean lvb = (LabelValueBean) (itr_collcurrencies
						.next());

				if (currencyCode.equals("All")) {
					request.setAttribute("currencyDesc", "All");

					break;
				}

				if (lvb.getValue().equals(currencyCode)) {
					request.setAttribute("currencyDesc", lvb.getLabel());

					break;
				}
			}
		}
		log.debug(this.getClass().getName()
				+ " - [ putDescriptionsInRequest ] - " + " Exit ");
	}

	/**
	 * This method is called from Input Authorize Queue screen on the press of
	 * 'delete' button
	 * 
	 * @param ActionMapping
	 *            mapping
	 * @param ActionForm
	 *            form
	 * @param HttpServletRequest
	 *            request
	 * @param HttpServletResponse
	 *            response
	 * @return - ActionForward object
	 * @throws SwtException -
	 *             SwtException object
	 */

	public String deleteMovements() throws SwtException {
HttpServletRequest request = ServletActionContext.getRequest();
HttpServletResponse response = ServletActionContext.getResponse();


		String hostId = EMPTYSTRING;
		String entityId = EMPTYSTRING;
		String movementIds = EMPTYSTRING;
		String currencyCode = EMPTYSTRING;
		String inputSource = EMPTYSTRING;
		Movement movement = null;
		String userId = null;
		String todayDate = null;
		String todayDatePlusOne = null;
		String todayDatePlusTwo = null;
		String searchDate = EMPTYSTRING;
		Date sourceDate = null;
		SystemFormats sysformat = null;
		try {
			log.debug(this.getClass().getName() + " - [ deleteMovements ] - "
					+ " Entry ");
			/* Getting the current User Id from the Session */
			userId = SwtUtil.getCurrentUserId(request.getSession());

			sysformat = SwtUtil.getCurrentSystemFormats(request.getSession());

			/* Setting the today's,today + 1,today + 2 date */
			todayDate = SwtUtil.getSystemDateString();
			/*
			 * Start: Mantis Issue 1306 : Code Removed for column in weekend1
			 * and weekend2 by Arumugam on 08-Mar-2011
			 */
			todayDatePlusOne = SwtUtil
					.getSystemDatePluswithoutTimeAsString(
							PropertiesFileLoader
									.getInstance()
									.getFacilityPropertiesValue(
											SwtConstants.FACILITY_INPUT_AUTHORISE_QUEUE),
							SwtUtil.getUserCurrentEntity(request.getSession()),
							currencyCode, 1, null);
			todayDatePlusTwo = SwtUtil
					.getSystemDatePluswithoutTimeAsString(
							PropertiesFileLoader
									.getInstance()
									.getFacilityPropertiesValue(
											SwtConstants.FACILITY_INPUT_AUTHORISE_QUEUE),
							SwtUtil.getUserCurrentEntity(request.getSession()),
							currencyCode, 2, null);
			/*
			 * End: Mantis Issue 1306 : Code Removed for column in weekend1 and
			 * weekend2 by Arumugam on 08-Mar-2011
			 */

			/*
			 * Getting the host Id from the cache if available else from the
			 * swtCommon.properties
			 */
			hostId = putHostIdListInReq(request);

			/*
			 * Getting the
			 * entityid,movementIds,currencyCode,inputSource,fromMenu from the
			 * request parameter
			 */
			entityId = request.getParameter("entityId").trim();
			movementIds = request.getParameter("selectedMovementId").trim();
			currencyCode = request.getParameter("currencyCode").trim();
			inputSource = request.getParameter("inputSourceValue").trim();

			request.setAttribute("source", inputSource);
			// DynaValidatorForm /*
			 /* Calls preAdviceInputManager deleteMovement to fetch record set
			 * from the database by passing the movement Id's
			 */
			movementIds = movementIds.replace(",", "|");
			movementIds = movementIds + "|";
			preAdviceInputManager.deleteMovement(hostId, entityId, movementIds);

			if ((inputSource == null) || (inputSource.trim().length() == 0)) {
				inputSource = DEFAULTSEARCHSTRING;
			}

			/*
			 * Setting the HostId,entityId,inputSource,currencyCode in movement
			 * object
			 */
			movement = (Movement) getMovement();
			movement.getId().setHostId(hostId);
			movement.getId().setEntityId(entityId);
			movement.setInputSource(inputSource);
			movement.setCurrencyCode(currencyCode);

			/*
			 * Setting the dateFlag in request object if present else
			 * EMPTYSTRING
			 */
			String dateFlag = (((request.getParameter("dateFlag") != null) && (request
					.getParameter("dateFlag").trim().length() > 0)) ? request
					.getParameter("dateFlag") : EMPTYSTRING); // EMPTYSTRING
			// means for all
			// date.

			request.setAttribute("dateFlag", dateFlag);

			/* Depends upon the dateFlag the date will be assigned */
			Format formatter = new SimpleDateFormat("dd/MM/yyyy");
			if (dateFlag.equals(todayDate)) {
				sourceDate = SwtUtil.parseDate(todayDate, SwtUtil
						.getCurrentDateFormat(request.getSession()));
				searchDate = formatter.format(sourceDate);
			} else if (dateFlag.equals(todayDatePlusOne)) {
				sourceDate = SwtUtil.parseDate(todayDatePlusOne, SwtUtil
						.getCurrentDateFormat(request.getSession()));
				searchDate = formatter.format(sourceDate);
			} else if (dateFlag.equals(todayDatePlusTwo)) {
				sourceDate = SwtUtil.parseDate(todayDatePlusTwo, SwtUtil
						.getCurrentDateFormat(request.getSession()));
				searchDate = formatter.format(sourceDate);
			} else {
				searchDate = EMPTYSTRING;
			}

			/*
			 * Calls preAdviceInputManager getAccountMonitorDetails to fetch
			 * record set from the database
			 */
			preAdviceInputManager.getMovementWithMatchStatus(request, movement
					.getId().getHostId(), movement.getId().getEntityId(),
					currencyCode, inputSource, AUTHORISE_MATCHSTATUS,
					sysformat, searchDate, userId);

			/*
			 * Setting MovmtButton in Request object based on the Manual or
			 * PreAdvice
			 */
			request
					.setAttribute(
							"MovmtButton",
							(movement.getInputSource().equalsIgnoreCase(
									SwtConstants.MOVEMENT_SOURCE_DEFAULT) || movement
									.getInputSource()
									.equalsIgnoreCase(
											SwtConstants.MOVEMENT_SOURCE_PREADVICE)) ? "true"
									: EMPTYSTRING);

			/*
			 * Class Local Method used to set the button status,Entity,Source in
			 * request
			 */
			putSubmitButtonStatus(request, movement.getId().getEntityId(),
					movement.getCurrencyCode());
			putAuthButtonStatus(request, entityId, currencyCode);
			putRefButtonStatus(request, entityId, currencyCode, inputSource);
			putEntityListInReq(request);
			putSourcesInReq(request);
			putCurrencyListInReq(request, movement.getId().getHostId(),
					movement.getId().getEntityId());

			Collection<EntityUserAccess> coll = SwtUtil
					.getUserEntityAccessList(request.getSession());

			int entityAcess = SwtUtil.getUserEntityAccess(coll, entityId);

			if (entityAcess == SwtConstants.ENTITY_FULL_ACCESS) {
				setButtonStatus(request, SwtConstants.STR_TRUE,
						SwtConstants.STR_FALSE, SwtConstants.STR_FALSE);

				request.setAttribute("EntityAccess",
						SwtConstants.ENTITY_FULL_ACCESS + "");
			} else {
				request.setAttribute("EntityAccess",
						SwtConstants.ENTITY_READ_ACCESS + "");
			}

			movement = (Movement) getMovement();

			/*
			 * Setting options in the request object got from the request
			 * parameter
			 */
			String options = request.getParameter("options");
			options = ((options != null) && (options.trim().length() > 0)) ? options
					: "Y";
			request.setAttribute("options", options);

			/* Class Local Method used to set the description in Request object */
			putDescriptionsInRequest(request, movement);

			setMovement(movement);

			/*
			 * Setting input screen as
			 * Authorize,parentFormRefresh,showMovementId,methodName in Request
			 * object
			 */
			request.setAttribute("inputscreen", SCREEN_STATUS_AUTH);
			request.setAttribute("parentFormRefresh", "yes");
			request.setAttribute("showMovementId", "yes");
			request.setAttribute("methodName", "success");
			/*
			 * Code added by venkat on 14-Apr-2011 for Mantis 1308:"Show 'Last
			 * refresh' time in screens that have refresh function"
			 */
			request.setAttribute("lastRefTime", SwtUtil.getLastRefTimeOnGMTOffset(request,
					entityId));
			return ("success");
		} catch (SwtException swtexp) {

			log.error(this.getClass().getName() + "- [ deleteMovements() ] - "
					+ swtexp.getMessage());
			SwtUtil.logException(swtexp, request, "");
			return ("fail");

		} catch (Exception e) {

			log.error(this.getClass().getName() + "- [ deleteMovements() ] - "
					+ e.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					e, "deleteMovements", PreAdviceInputAction.class), request,
					"");

			return ("fail");
		} finally {
			log.debug(this.getClass().getName()
					+ " - [ deleteMovements ] -  Exit ");
		}

	}




	public String checkLockedMvts()
			throws SwtException {
		log.debug("entering 'checkLockedMvts' method");
		String hostId= null;
		String entityId = null;
		String logged_UserId= null;
		String mvtsList= null;
		boolean isLocked = false;
		HashMap<Long, String> lockedMvts  = new HashMap<Long, String>();
		String alerMsg= "";
		HttpServletRequest request = ServletActionContext.getRequest();
		HttpServletResponse response = ServletActionContext.getResponse();
		try {
			hostId= putHostIdListInReq(request);

			logged_UserId= SwtUtil.getCurrentUserId(request.getSession());

			mvtsList= request.getParameter("mvtsList");

			entityId = ((request.getParameter("entityId") != null) && (request
					.getParameter("entityId").trim().length() > 0)) ? request
					.getParameter("entityId") : SwtUtil.getUserCurrentEntity(request.getSession());
			if (!SwtUtil.isEmptyOrNull(mvtsList)) {
				mvtsList= mvtsList.substring(0, mvtsList.length() - 1);
				MovementLockManager movementLockManager = (MovementLockManager) SwtUtil
						.getBean("movementLockManager");
				lockedMvts = movementLockManager.checkMvtsLock(hostId, entityId, logged_UserId, mvtsList);
				if (lockedMvts.size()>0) {
					for (Map.Entry<Long, String> entry : lockedMvts.entrySet()) {
						Long key = entry.getKey();
						String value = entry.getValue();
						alerMsg= alerMsg+"Movement ID '"+key+ "' is in use by User '"+value+"' ,";
					}
					alerMsg= alerMsg.substring(0, alerMsg.lastIndexOf(" "));
				}else {
					alerMsg="";
				}
			}
			response.getWriter().print(alerMsg);

			log.debug("exiting 'checkLockedMvts' method");

			return null;
		} catch (SwtException swtexp) {
			log.debug(
					"SwtException Catch in InputAuthoriseAction.'checkLockedMvts' method : " +
							swtexp.getMessage());
			SwtUtil.logException(swtexp, request, "");

			return null;
		} catch (Exception exp) {
			log.debug(
					"Exception Catch in InputAuthoriseAction.'checkLockedMvts' method : " +
							exp.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(exp,
					"checkLockedMvts", InputAuthoriseAction.class), request, "");

			return null;
		}
	}


}
