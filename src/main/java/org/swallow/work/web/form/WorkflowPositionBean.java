package org.swallow.work.web.form;

public class WorkflowPositionBean {

	private String Position = "";
	private String included = "";
	private String excluded = "";
	public String getPosition() {
		return Position;
	}
	public void setPosition(String position) {
		Position = position;
	}
	public String getIncluded() {
		return included;
	}
	public void setIncluded(String included) {
		this.included = included;
	}
	public String getExcluded() {
		return excluded;
	}
	public void setExcluded(String excluded) {
		this.excluded = excluded;
	}
}
