/*
 * @(#)CentralBankRecords.java 1.0 19/03/10
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.work.web.form;

import java.util.ArrayList;
import java.util.List;

/**
 * CentralBankRecords.java
 * 
 * This java bean is used to handle the various balance for central bank monitor records.
 * 
 * <AUTHOR>
 * @date Mar 19,2010
 */
public class CentralBankRecords {
	// Balance Name
	private String balance = null;
	// Balance Name
	private Boolean negativeBal = false;
	// Clickable Flag
	private boolean clickableFlag = false;
	// Editable Flag
	private boolean editableFlag = false;
	// Currency Id
	private String currencyId = null;
	// multiplerType
	private String multiplierType;
	// List of CentralBankMonitorBean;
	private List<CentralBankMonitorBean> centralMonitor = new ArrayList<CentralBankMonitorBean>();

	/**
	 * @return the balance
	 */
	public String getBalance() {
		return balance;
	}

	/**
	 * @param balance
	 *            the balance to set
	 */
	public void setBalance(String balance) {
		this.balance = balance;
	}

	/**
	 * @return the currencyId
	 */
	public String getCurrencyId() {
		return currencyId;
	}

	/**
	 * @param currencyId
	 *            the currencyId to set
	 */
	public void setCurrencyId(String currencyId) {
		this.currencyId = currencyId;
	}

	/**
	 * @return the centralMonitor
	 */
	public List<CentralBankMonitorBean> getCentralMonitor() {
		return this.centralMonitor;
	}

	/**
	 * @param centralMonitor
	 *            the centralMonitor to set
	 */
	public void addCentralMonitor(CentralBankMonitorBean centralMonitor) {
		this.centralMonitor.add(centralMonitor);
	}

	/**
	 * @return the negativeBal
	 */
	public Boolean getNegativeBal() {
		return negativeBal;
	}

	/**
	 * @param negativeBal
	 *            the negativeBal to set
	 */
	public void setNegativeBal(Boolean negativeBal) {
		this.negativeBal = negativeBal;
	}

	/**
	 * @return the clickableFlag
	 */
	public boolean isClickableFlag() {
		return clickableFlag;
	}

	/**
	 * @param clickableFlag
	 *            the clickableFlag to set
	 */
	public void setClickableFlag(boolean clickableFlag) {
		this.clickableFlag = clickableFlag;
	}

	/**
	 * @return the editableFlag
	 */
	public boolean isEditableFlag() {
		return editableFlag;
	}

	/**
	 * @param editableFlag
	 *            the editableFlag to set
	 */
	public void setEditableFlag(boolean editableFlag) {
		this.editableFlag = editableFlag;
	}

	/**
	 * @return the multiplierType
	 */
	public String getMultiplierType() {
		return multiplierType;
	}

	/**
	 * @param multiplierType
	 *            the multiplierType to set
	 */
	public void setMultiplierType(String multiplierType) {
		this.multiplierType = multiplierType;
	}
}
