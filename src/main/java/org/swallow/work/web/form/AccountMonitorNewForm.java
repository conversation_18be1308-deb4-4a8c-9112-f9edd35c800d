/*
 * @(#) AccountMonitorNewForm  .java  1.0 04/09/06
 * 
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 * 
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */

package org.swallow.work.web.form;

import java.util.Collection;
import java.util.Date;
import java.util.Iterator;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.swallow.work.model.AccountMonitorNew;
import org.swallow.work.model.AccountMonitorTotalCacheValue;

/**
 * This class will keep all the variables that are displayed on accountmonitor
 * display screen and corresponding getters and setters.
 * 
*/
public class AccountMonitorNewForm {

	/**
	 * Instance of Log object
	 */
	private final Log log = LogFactory.getLog(AccountMonitorNewForm.class);

	// Entity id
	private String entityId = null;
	// Currency code
	private String currencyCode = null;
	// Account type
	private String accountType = null;
	// Tab date
	private Date date = null;
	private String dateAsString = null;
	// Negative sign for total - Predicted balance
	private boolean signFlagForPredBalTotal = false;
	// Negative sign for total - Unsettled balance
	private boolean signFlagForUnsettledBalTotal = false;
	// Negative sign for total - Unexpected balance
	private boolean signFlagForUnexpectedBalTotal = false;
	// Negative sign for total - Loro balance
	private boolean signFlagForLoroBalTotal = false;
	// Negative sign for total - External balance
	private boolean signFlagForExternalBalTotal = false;
	// Negative sign for total - No of unexpected movement
	private boolean signFlagForOpenUnexpectedBalTotal = false;
	// Negative sign for total - Start of day balance
	private boolean signFlagForAccumulatedSODBal = false;
	// No of unexpected movements
	private String openUnexpectedBalTotalAsString = null;
	// Start of day balance
	private String accumulatedSODBalAsString = null;
	// Flag, denotes whether to apply threshold or not
	private String applyCurrencyThreshold = null;
	// Account class
	private String accountClass = null;
	// Flag, denotes whether to hide zero balance records or not
	private String hideZeroBalances = null;
	// contain data present in Total fields
	Collection<AccountMonitorTotalCacheValue> totalDetails = null;
	// The data displayed in the table
	Collection<AccountMonitorNew> accountMonitorDetails = null;
	// scenario Highlighted account
	private String scenarioHighlighted = null;
	/**
	 * @return Returns the totalDetails.
	 */
	public Collection<AccountMonitorTotalCacheValue> getTotalDetails() {
		return totalDetails;
	}

	/**
	 * @param totalDetails
	 *            The totalDetails to set.
	 */
	public void setTotalDetails(
			Collection<AccountMonitorTotalCacheValue> totalDetails) {
		log.debug("Inside the function setTotalDetails()");
		if (totalDetails != null) {
			Iterator<AccountMonitorTotalCacheValue> itrTotal = totalDetails
					.iterator();
			if (itrTotal.hasNext()) {
				AccountMonitorTotalCacheValue totalValObj = itrTotal.next();
				setOpenUnexpectedBalTotalAsString(totalValObj
						.getOpenUnexpectedBalTotalAsString());
				setAccumulatedSODBalAsString(totalValObj
						.getAccumulatedSODBalAsString());
				setScenarioHighlighted(totalValObj
						.getScenarioHighlighted());
				if (totalValObj.getPredictedBalTotal().doubleValue() < 0) {
					signFlagForPredBalTotal = true;
				}
				if (totalValObj.getUnsettledBalTotal().doubleValue() < 0) {
					signFlagForUnsettledBalTotal = true;
				}
				if (totalValObj.getUnexpectedBalTotal().doubleValue() < 0) {
					signFlagForUnexpectedBalTotal = true;
				}
				if (totalValObj.getLoroBalTotal().doubleValue() < 0) {
					signFlagForLoroBalTotal = true;
				}
				if (totalValObj.getExternalBalTotal().doubleValue() < 0) {
					signFlagForExternalBalTotal = true;
				}
				if (totalValObj.getOpenUnexpectedBalTotal().doubleValue() < 0) {
					signFlagForOpenUnexpectedBalTotal = true;
				}
				if (totalValObj.getAccumulatedSODBal().doubleValue() < 0) {
					signFlagForAccumulatedSODBal = true;
				}
			}
		}
		this.totalDetails = totalDetails;
	}

	/**
	 * @return Returns the accountMonitorDetails.
	 */
	public Collection<AccountMonitorNew> getAccountMonitorDetails() {
		return accountMonitorDetails;
	}

	/**
	 * @param accountMonitorDetails
	 *            The accountMonitorDetails to set.
	 */
	public void setAccountMonitorDetails(
			Collection<AccountMonitorNew> accountMonitorDetails) {
		this.accountMonitorDetails = accountMonitorDetails;
	}

	/**
	 * @return Returns the accountType.
	 */
	public String getAccountType() {
		return accountType;
	}

	/**
	 * @param accountType
	 *            The accountType to set.
	 */
	public void setAccountType(String accountType) {
		this.accountType = accountType;
	}

	/**
	 * @return Returns the currencyCode.
	 */
	public String getCurrencyCode() {
		return currencyCode;
	}

	/**
	 * @param currencyCode
	 *            The currencyCode to set.
	 */
	public void setCurrencyCode(String currencyCode) {
		this.currencyCode = currencyCode;
	}

	/**
	 * @return Returns the date.
	 */
	public Date getDate() {
		return date;
	}

	/**
	 * @param date
	 *            The date to set.
	 */
	public void setDate(Date date) {
		this.date = date;
	}

	/**
	 * @return Returns the dateAsString.
	 */
	public String getDateAsString() {
		return dateAsString;
	}

	/**
	 * @param dateAsString
	 *            The dateAsString to set.
	 */
	public void setDateAsString(String dateAsString) {
		this.dateAsString = dateAsString;
	}

	/**
	 * @return Returns the entityId.
	 */
	public String getEntityId() {
		return entityId;
	}

	/**
	 * @param entityId
	 *            The entityId to set.
	 */
	public void setEntityId(String entityId) {
		this.entityId = entityId;
	}

	/**
	 * @return Returns the signFlagForPredBalTotal.
	 */
	public boolean isSignFlagForPredBalTotal() {

		return signFlagForPredBalTotal;
	}

	/**
	 * @param signFlagForpredBalTotal
	 *            The signFlagForPredBalTotal to set.
	 */
	public void setSignFlagForPredBalTotal(boolean signFlagForPredBalTotal) {
		this.signFlagForPredBalTotal = signFlagForPredBalTotal;
	}

	/**
	 * @return Returns the signFlagForExternalBalTotal.
	 */
	public boolean isSignFlagForExternalBalTotal() {
		return signFlagForExternalBalTotal;
	}

	/**
	 * @param signFlagForExternalBalTotal
	 *            The signFlagForExternalBalTotal to set.
	 */
	public void setSignFlagForExternalBalTotal(
			boolean signFlagForExternalBalTotal) {
		this.signFlagForExternalBalTotal = signFlagForExternalBalTotal;
	}

	/**
	 * @return Returns the signFlagForLoroBalTotal.
	 */
	public boolean isSignFlagForLoroBalTotal() {
		return signFlagForLoroBalTotal;
	}

	/**
	 * @param signFlagForLoroBalTotal
	 *            The signFlagForLoroBalTotal to set.
	 */
	public void setSignFlagForLoroBalTotal(boolean signFlagForLoroBalTotal) {
		this.signFlagForLoroBalTotal = signFlagForLoroBalTotal;
	}

	/**
	 * @return Returns the signFlagForUnexpectedBalTotal.
	 */
	public boolean isSignFlagForUnexpectedBalTotal() {
		return signFlagForUnexpectedBalTotal;
	}

	/**
	 * @param signFlagForUnexpectedBalTotal
	 *            The signFlagForUnexpectedBalTotal to set.
	 */
	public void setSignFlagForUnexpectedBalTotal(
			boolean signFlagForUnexpectedBalTotal) {
		this.signFlagForUnexpectedBalTotal = signFlagForUnexpectedBalTotal;
	}

	/**
	 * @return Returns the signFlagForUnsettledBalTotal.
	 */
	public boolean isSignFlagForUnsettledBalTotal() {
		return signFlagForUnsettledBalTotal;
	}

	/**
	 * @param signFlagForUnsettledBalTotal
	 *            The signFlagForUnsettledBalTotal to set.
	 */
	public void setSignFlagForUnsettledBalTotal(
			boolean signFlagForUnsettledBalTotal) {
		this.signFlagForUnsettledBalTotal = signFlagForUnsettledBalTotal;
	}

	/**
	 * @return Returns the accumulatedSODBalAsString.
	 */
	public String getAccumulatedSODBalAsString() {
		return accumulatedSODBalAsString;
	}

	/**
	 * @param accumulatedSODBalAsString
	 *            The accumulatedSODBalAsString to set.
	 */
	public void setAccumulatedSODBalAsString(String accumulatedSODBalAsString) {
		this.accumulatedSODBalAsString = accumulatedSODBalAsString;
	}

	/**
	 * @return Returns the openUnexpectedBalTotalAsString.
	 */
	public String getOpenUnexpectedBalTotalAsString() {
		return openUnexpectedBalTotalAsString;
	}

	/**
	 * @param openUnexpectedBalTotalAsString
	 *            The openUnexpectedBalTotalAsString to set.
	 */
	public void setOpenUnexpectedBalTotalAsString(
			String openUnexpectedBalTotalAsString) {
		this.openUnexpectedBalTotalAsString = openUnexpectedBalTotalAsString;
	}

	/**
	 * @return Returns the signFlagForAccumulatedSODBal.
	 */
	public boolean isSignFlagForAccumulatedSODBal() {
		return signFlagForAccumulatedSODBal;
	}

	/**
	 * @param signFlagForAccumulatedSODBal
	 *            The signFlagForAccumulatedSODBal to set.
	 */
	public void setSignFlagForAccumulatedSODBal(
			boolean signFlagForAccumulatedSODBal) {
		this.signFlagForAccumulatedSODBal = signFlagForAccumulatedSODBal;
	}

	/**
	 * @return Returns the signFlagForOpenUnexpectedBalTotal.
	 */
	public boolean isSignFlagForOpenUnexpectedBalTotal() {
		return signFlagForOpenUnexpectedBalTotal;
	}

	/**
	 * @param signFlagForOpenUnexpectedBalTotal
	 *            The signFlagForOpenUnexpectedBalTotal to set.
	 */
	public void setSignFlagForOpenUnexpectedBalTotal(
			boolean signFlagForOpenUnexpectedBalTotal) {
		this.signFlagForOpenUnexpectedBalTotal = signFlagForOpenUnexpectedBalTotal;
	}

	/**
	 * @return Returns the applyCurrencyThreshold.
	 */
	public String getApplyCurrencyThreshold() {
		return applyCurrencyThreshold;
	}

	/**
	 * @param applyCurrencyThreshold
	 *            The applyCurrencyThreshold to set.
	 */
	public void setApplyCurrencyThreshold(String applyCurrencyThreshold) {
		this.applyCurrencyThreshold = applyCurrencyThreshold;
	}

	/**
	 * @return Returns the accountClass.
	 */
	public String getAccountClass() {
		return accountClass;
	}

	/**
	 * @param accountClass
	 *            The accountClass to set.
	 */
	public void setAccountClass(String accountClass) {
		this.accountClass = accountClass;
	}

	/**
	 * @return Returns the hideZeroBalances.
	 */
	public String getHideZeroBalances() {
		return hideZeroBalances;
	}

	/**
	 * @param hideZeroBalances
	 *            The hideZeroBalances to set.
	 */
	public void setHideZeroBalances(String hideZeroBalances) {
		this.hideZeroBalances = hideZeroBalances;
	}

	public String getScenarioHighlighted() {
		return scenarioHighlighted;
	}

	public void setScenarioHighlighted(String scenarioHighlighted) {
		this.scenarioHighlighted = scenarioHighlighted;
	}
}
