/*
 * @(#)EntityMonitorForm.java 1.0 08/02/11
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.work.web.form;

import java.util.Collection;
import java.util.Date;

/**
 * EntityMonitorForm.java
 * 
 * This form bean to get the Entity monitor details from screen
 * 
 * <AUTHOR>
 * @date Feb 12, 2011
 */
public class EntityMonitorForm {
	// Entity Id
	private String entityId = null;
	// host Id
	private String hostId = null;
	// To Date
	private Date userId = null;
	// From Date
	private Date screenId = null;
	// Update Date
	private String UpdateDate = null;
	// yDisplay
	private String yDisplay = null;
	// display Days
	private String displayDays = null;
	// priority Order
	private String priorityOrder = null;
	// currency Code
	private String currencyCode = null;
	// From Date
	private Date currentDate = null;
	// FromDate AsString
	private String currentDateAsString = null;
	// Currency Group
	private String currGrp = null;
	// Break Down
	private String breakDown = null;
	// Totals
	private Collection<BalanceDateTO> totals = null;
	// Entity Records
	private Collection<EntityRecord> entityRecords = null;
	// Code added by Vivekanandan A for Mantis 1991 on 09-07-2012
	// entityOffsetTime flag
	private String entityOffsetTime = null;

	/**
	 * @return the entityRecords
	 */
	public Collection<EntityRecord> getEntityRecords() {
		return entityRecords;
	}

	/**
	 * @param entityRecords
	 *            the entityRecords to set
	 */
	public void setEntityRecords(Collection<EntityRecord> entityRecords) {
		this.entityRecords = entityRecords;
	}

	/**
	 * @return the breakDown
	 */
	public String getBreakDown() {
		return breakDown;
	}

	/**
	 * @param breakDown
	 *            the breakDown to set
	 */
	public void setBreakDown(String breakDown) {
		this.breakDown = breakDown;
	}

	/**
	 * @return the totals
	 */
	public Collection<BalanceDateTO> getTotals() {
		return totals;
	}

	/**
	 * @param totals
	 *            the totals to set
	 */
	public void setTotals(Collection<BalanceDateTO> totals) {
		this.totals = totals;
	}

	/**
	 * @return the currGrp
	 */
	public String getCurrGrp() {
		return currGrp;
	}

	/**
	 * @param currGrp
	 *            the currGrp to set
	 */
	public void setCurrGrp(String currGrp) {
		this.currGrp = currGrp;
	}

	/**
	 * @return the currentDate
	 */
	public Date getCurrentDate() {
		return currentDate;
	}

	/**
	 * @param currentDate
	 *            the currentDate to set
	 */
	public void setCurrentDate(Date currentDate) {
		this.currentDate = currentDate;
	}

	/**
	 * @return the currentDateAsString
	 */
	public String getCurrentDateAsString() {
		return currentDateAsString;
	}

	/**
	 * @param currentDateAsString
	 *            the currentDateAsString to set
	 */
	public void setCurrentDateAsString(String currentDateAsString) {
		this.currentDateAsString = currentDateAsString;
	}

	/**
	 * @return Returns the entityId.
	 */
	public String getEntityId() {
		return entityId;
	}

	/**
	 * @param entityId
	 *            The entityId to set.
	 */
	public void setEntityId(String entityId) {
		this.entityId = entityId;
	}

	/**
	 * @return the hostId
	 */
	public String getHostId() {
		return hostId;
	}

	/**
	 * @param hostId
	 *            the hostId to set
	 */
	public void setHostId(String hostId) {
		this.hostId = hostId;
	}

	/**
	 * @return the userId
	 */
	public Date getUserId() {
		return userId;
	}

	/**
	 * @param userId
	 *            the userId to set
	 */
	public void setUserId(Date userId) {
		this.userId = userId;
	}

	/**
	 * @return the screenId
	 */
	public Date getScreenId() {
		return screenId;
	}

	/**
	 * @param screenId
	 *            the screenId to set
	 */
	public void setScreenId(Date screenId) {
		this.screenId = screenId;
	}

	/**
	 * @return the updateDate
	 */
	public String getUpdateDate() {
		return UpdateDate;
	}

	/**
	 * @param updateDate
	 *            the updateDate to set
	 */
	public void setUpdateDate(String updateDate) {
		UpdateDate = updateDate;
	}

	/**
	 * @return the yDisplay
	 */
	public String getYDisplay() {
		return yDisplay;
	}

	/**
	 * @param display
	 *            the yDisplay to set
	 */
	public void setYDisplay(String display) {
		yDisplay = display;
	}

	/**
	 * @return the displayDays
	 */
	public String getDisplayDays() {
		return displayDays;
	}

	/**
	 * @param displayDays
	 *            the displayDays to set
	 */
	public void setDisplayDays(String displayDays) {
		this.displayDays = displayDays;
	}

	/**
	 * @return the priorityOrder
	 */
	public String getPriorityOrder() {
		return priorityOrder;
	}

	/**
	 * @param priorityOrder
	 *            the priorityOrder to set
	 */
	public void setPriorityOrder(String priorityOrder) {
		this.priorityOrder = priorityOrder;
	}

	/**
	 * @return the currencyCode
	 */
	public String getCurrencyCode() {
		return currencyCode;
	}

	/**
	 * @param currencyCode
	 *            the currencyCode to set
	 */
	public void setCurrencyCode(String currencyCode) {
		this.currencyCode = currencyCode;
	}

	// START Code added by Vivekanandan A for Mantis 1991 on 09-07-2012
	/**
	 * Getter method for entityOffsetTime
	 * 
	 * @return entityOffsetTime as String
	 */

	public String getEntityOffsetTime() {
		return entityOffsetTime;
	}

	/**
	 * Setter method for entityOffsetTime
	 * 
	 * @param entityOffsetTime
	 */
	public void setEntityOffsetTime(String entityOffsetTime) {
		this.entityOffsetTime = entityOffsetTime;
	}
	// END Code added by Vivekanandan A for Mantis 1991 on 09-07-2012
}