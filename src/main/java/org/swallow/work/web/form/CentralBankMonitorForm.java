/*
 * @(#)CentralBankMonitorForm.java 1.0 19/03/10
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.work.web.form;

import java.util.Date;
import java.util.List;

/**
 * CentralBankMonitorForm.java
 * 
 * This form bean to get the central bank monitor details from screen
 * 
 * <AUTHOR> B
 * @date Mar 19, 2010
 */
public class CentralBankMonitorForm {
	// Entity Id
	private String entityId;
	// Currency Limit
	private String currencyLimit;
	// To Date
	private Date toDate;
	// From Date
	private Date fromDate;
	// FromDate AsString
	private String fromDateAsString;
	// List of CentralBankRecords
	private List<CentralBankRecords> monitorRecords = null;
	// ToDate AsString
	private String toDateAsString;
	// Current TimeStamp
	private String currTimeStamp;

	/**
	 * @return Returns the fromDateAsString.
	 */
	public String getFromDateAsString() {
		return fromDateAsString;
	}

	/**
	 * @param fromDateAsString
	 *            The fromDateAsString to set.
	 */
	public void setFromDateAsString(String fromDateAsString) {
		this.fromDateAsString = fromDateAsString;
	}

	/**
	 * @return Returns the toDateAsString.
	 */
	public String getToDateAsString() {
		return toDateAsString;
	}

	/**
	 * @param toDateAsString
	 *            The toDateAsString to set.
	 */
	public void setToDateAsString(String toDateAsString) {
		this.toDateAsString = toDateAsString;
	}

	/**
	 * @return Returns the entityId.
	 */
	public String getEntityId() {
		return entityId;
	}

	/**
	 * @param entityId
	 *            The entityId to set.
	 */
	public void setEntityId(String entityId) {
		this.entityId = entityId;
	}

	/**
	 * @return Returns the fromDate.
	 */
	public Date getFromDate() {
		return fromDate;
	}

	/**
	 * @param fromDate
	 *            The fromDate to set.
	 */
	public void setFromDate(Date fromDate) {
		this.fromDate = fromDate;
	}

	/**
	 * @return Returns the toDate.
	 */
	public Date getToDate() {
		return toDate;
	}

	/**
	 * @param toDate
	 *            The toDate to set.
	 */
	public void setToDate(Date toDate) {
		this.toDate = toDate;
	}

	/**
	 * @return the monitorRecords
	 */
	public List<CentralBankRecords> getMonitorRecords() {
		return monitorRecords;
	}

	/**
	 * @param monitorRecords
	 *            the monitorRecords to set
	 */
	public void setMonitorRecords(List<CentralBankRecords> monitorRecords) {
		this.monitorRecords = monitorRecords;
	}

	/**
	 * @return the currencyLimit
	 */
	public String getCurrencyLimit() {
		return currencyLimit;
	}

	/**
	 * @param currencyLimit
	 *            the currencyLimit to set
	 */
	public void setCurrencyLimit(String currencyLimit) {
		this.currencyLimit = currencyLimit;
	}

	/**
	 * @return the currTimeStamp
	 */
	public String getCurrTimeStamp() {
		return currTimeStamp;
	}

	/**
	 * @param currTimeStamp
	 *            the currTimeStamp to set
	 */
	public void setCurrTimeStamp(String currTimeStamp) {
		this.currTimeStamp = currTimeStamp;
	}
}
