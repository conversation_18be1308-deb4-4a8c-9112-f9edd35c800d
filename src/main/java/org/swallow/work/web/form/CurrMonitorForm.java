/*
 * @(#)CurrMonitorForm.java 1.0 29/08/06
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.work.web.form;

import java.util.Collection;

/**
 * This is a form bean, has getters and setters for currency monitor form fields
 * 
 * <AUTHOR>
 */
public class CurrMonitorForm {

	// Entity id allocated for user / selected entity id on the screen
	private String entityId = null;
	// Currency group allocated for user / selected currency group id on the
	// screen
	private String currGrp = null;
	// From Date in string
	private String fromDateAsString = null;
	// To date in string
	private String toDateAsString = null;
	// To hold grid header details
	private Collection<BalanceDateTO> headerDetails = null;
	// To hold currency based predicted/loro balances
	private Collection<CurrencyRecord> balanceDetails = null;
	// To hold sum of predicted/loro balances
	private CurrencyRecord balanceTotal = null;

	/**
	 * Getter method of entityId
	 * 
	 * @return the entityId
	 */
	public String getEntityId() {
		return entityId;
	}

	/**
	 * Setter method of entityId
	 * 
	 * @param entityId
	 *            the entityId to set
	 */
	public void setEntityId(String entityId) {
		this.entityId = entityId;
	}

	/**
	 * Getter method of currGrp
	 * 
	 * @return the currGrp
	 */
	public String getCurrGrp() {
		return currGrp;
	}

	/**
	 * Setter method of currGrp
	 * 
	 * @param currGrp
	 *            the currGrp to set
	 */
	public void setCurrGrp(String currGrp) {
		this.currGrp = currGrp;
	}

	/**
	 * Getter method of fromDateAsString
	 * 
	 * @return the fromDateAsString
	 */
	public String getFromDateAsString() {
		return fromDateAsString;
	}

	/**
	 * Setter method of fromDateAsString
	 * 
	 * @param fromDateAsString
	 *            the fromDateAsString to set
	 */
	public void setFromDateAsString(String fromDateAsString) {
		this.fromDateAsString = fromDateAsString;
	}

	/**
	 * Getter method of toDateAsString
	 * 
	 * @return the toDateAsString
	 */
	public String getToDateAsString() {
		return toDateAsString;
	}

	/**
	 * Setter method of toDateAsString
	 * 
	 * @param toDateAsString
	 *            the toDateAsString to set
	 */
	public void setToDateAsString(String toDateAsString) {
		this.toDateAsString = toDateAsString;
	}

	/**
	 * Getter method of headerDetails
	 * 
	 * @return the headerDetails
	 */
	public Collection<BalanceDateTO> getHeaderDetails() {
		return headerDetails;
	}

	/**
	 * Setter method of headerDetails
	 * 
	 * @param headerDetails
	 *            the headerDetails to set
	 */
	public void setHeaderDetails(Collection<BalanceDateTO> headerDetails) {
		this.headerDetails = headerDetails;
	}

	/**
	 * Getter method of balanceDetails
	 * 
	 * @return the balanceDetails
	 */
	public Collection<CurrencyRecord> getBalanceDetails() {
		return balanceDetails;
	}

	/**
	 * Setter method of balanceDetails
	 * 
	 * @param balanceDetails
	 *            the balanceDetails to set
	 */
	public void setBalanceDetails(Collection<CurrencyRecord> balanceDetails) {
		this.balanceDetails = balanceDetails;
	}

	/**
	 * Getter method of balanceTotal
	 * 
	 * @return the balanceTotal
	 */
	public CurrencyRecord getBalanceTotal() {
		return balanceTotal;
	}

	/**
	 * Setter method of balanceTotal
	 * 
	 * @param balanceTotal
	 *            the balanceTotal to set
	 */
	public void setBalanceTotal(CurrencyRecord balanceTotal) {
		this.balanceTotal = balanceTotal;
	}
}