 /*
 * @(#)MetagroupMonitorForm.java 1.0 04/09/06
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.work.web.form;

import java.util.ArrayList;
import java.util.Collection;

public class MetagroupMonitorForm {
	
	
    /**
     * The entity ID
     */
    private String entityId = "";

    /**
     * The currency ID
     */
    private String currencyId = "";
    
    /**
     * The Monitor Type
     */
    private String monitorType = "";

    /**
     * The location ID
     */
    private String locationId = "";

    /**
     * The locations collection for the location drop down
     */
    private Collection locations = new ArrayList();

    /**
     * The selected date
     */
    private String date = "";

    /**
     * The entities collection for the entities drop down
     */
    private Collection entities = new ArrayList();

    /**
     * The currencies collection for the currencies drop down
     */
    private Collection currencies = new ArrayList();
    
    /**
     * The monitor type collection for Monitor type drop down
     */
    private Collection monitors = new ArrayList();

    /**
     * The collection of predicted balances for today
     */
    private Collection predictedBalances = new ArrayList();

    /**
     * The total
     */
    private String total = "";

    private String grandTotalAsString= " ";
	/**
	 * @return Returns the grandTotalAsString.
	 */
	public String getGrandTotalAsString() {
		return grandTotalAsString;
	}
	/**
	 * @param grandTotalAsString The grandTotalAsString to set.
	 */
	public void setGrandTotalAsString(String grandTotalAsString) {
		this.grandTotalAsString = grandTotalAsString;
	}
    private boolean grandTotalNegative=false;

    /**
     * Flag for checking whether the total is negative
     */
    private boolean totalNegativeFlag = false;

    /**
     * The index of the selected tab
     */
    private String selectedTabIndex;

    /**
     * The name of the selected tab
     */
    private String selectedTabName;

    /**
     * @return Returns the totalNegativeFlag.
     */
    public boolean isTotalNegativeFlag() {
        return totalNegativeFlag;
    }

    /**
     * @param totalNegativeFlag The totalNegativeFlag to set.
     */
    public void setTotalNegativeFlag(boolean totalNegativeFlag) {
        this.totalNegativeFlag = totalNegativeFlag;
    }

    /**
     * @param total The total to set.
     */
    public void setTotal(String total) {
        this.total = total;
    }

    /**
     * @return Returns the total.
     */
    public String getTotal() {
        return total;
    }

    /**
     * @return Returns the currencyCode.
     */
    public String getCurrencyId() {
        return currencyId;
    }

    /**
     * @param currencyId The currencyCode to set.
     */
    public void setCurrencyId(String currencyId) {
        this.currencyId = currencyId;
    }

    /**
     * @return Returns the date.
     */
    public String getDate() {
        return date;
    }

    /**
     * @param date The date to set.
     */
    public void setDate(String date) {
        this.date = date;
    }

    /**
     * @return Returns the entityId.
     */
    public String getEntityId() {
        return entityId;
    }

    /**
     * @param entityId The entityId to set.
     */
    public void setEntityId(String entityId) {
        this.entityId = entityId;
    }

    /**
     * @return Returns the todayPredictedBalances.
     */
    public Collection getPredictedBalances() {
        return predictedBalances;
    }

    /**
     * @param predictedBalances The todayPredictedBalances to set.
     */
    public void setPredictedBalances(Collection predictedBalances) {
        this.predictedBalances = predictedBalances;
    }

    /**
     * @return Returns the currencies.
     */
    public Collection getCurrencies() {
        return currencies;
    }

    /**
     * @param currencies The currencies to set.
     */
    public void setCurrencies(Collection currencies) {
        this.currencies = currencies;
    }

    /**
     * @return Returns the entities.
     */
    public Collection getEntities() {
        return entities;
    }

    /**
     * @param entities The entities to set.
     */
    public void setEntities(Collection entities) {
        this.entities = entities;
    }

    /**
     * @return Returns the selectedTabIndex.
     */
    public String getSelectedTabIndex() {
        return selectedTabIndex;
    }

    /**
     * @param selectedTabIndex The selectedTabIndex to set.
     */
    public void setSelectedTabIndex(String selectedTabIndex) {
        this.selectedTabIndex = selectedTabIndex;
    }

    /**
     * @return Returns the selectedTabName.
     */
    public String getSelectedTabName() {
        return selectedTabName;
    }

    /**
     * @param selectedTabName The selectedTabName to set.
     */
    public void setSelectedTabName(String selectedTabName) {
        this.selectedTabName = selectedTabName;
    }

	/**
	 * @return Returns the grandTotalNegative.
	 */
    public boolean isGrandTotalNegative() {
		return grandTotalNegative;
	}
	/**
	 * @param grandTotalNegative The grandTotalNegative to set.
	 */
	public void setGrandTotalNegative(boolean grandTotalNegative) {
		this.grandTotalNegative = grandTotalNegative;
	}

	/**
	 * @return Returns the locationId.
	 */
	public String getLocationId() {
		return locationId;
	}
	/**
	 * @param locationId The locationId to set.
	 */
	public void setLocationId(String locationId) {
		this.locationId = locationId;
	}

	/**
	 * @return Returns the locations.
	 */
	public Collection getLocations() {
		return locations;
	}
	/**
	 * @param locations The locations to set.
	 */
	public void setLocations(Collection locations) {
		this.locations = locations;
	}
	public String getMonitorType() {
		return monitorType;
	}
	public void setMonitorType(String monitorType) {
		this.monitorType = monitorType;
	}
	public Collection getMonitors() {
		return monitors;
	}
	public void setMonitors(Collection monitors) {
		this.monitors = monitors;
	}
	
	 /**
     * The metagroup ID
     */
    private String metagroupId = "";
    
    /*
	 * @return Returns the metagroupId.
	 */
	public String getMetagroupId() {
		return metagroupId;
	}
	/**
	 * @param metagroupId The metagroupId to set.
	 */
	public void setMetagroupId(String metagroupId) {
		this.metagroupId = metagroupId;
	}
	/**
     * The metagroups collection for the metagroup drop down
     */
    private Collection metagroups = new ArrayList();
	
	/**
	 * @return Returns the metagroups.
	 */
	public Collection getMetagroups() {
		return metagroups;
	}
	/**
	 * @param metagroups The metagroups to set.
	 */
	public void setMetagroups(Collection metagroups) {
		this.metagroups = metagroups;
	}
	
	private String itemId;
	public String getItemId() {
		return itemId;
	}
	public void setItemId(String itemId) {
		this.itemId = itemId;
	}
	
	  /**
     * The group Code
     */
    private String groupCode = "";

    /**
     * The groups collection for the group drop down
     */
    private Collection groups = new ArrayList();
    
	public String getGroupCode() {
		return groupCode;
	}
	public void setGroupCode(String groupCode) {
		this.groupCode = groupCode;
	}
	public Collection getGroups() {
		return groups;
	}
	public void setGroups(Collection groups) {
		this.groups = groups;
	}

    
    
	
}