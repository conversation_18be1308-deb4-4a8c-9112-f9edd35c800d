/*
 * @(#)CorporateAccountForm.java 1.0 13/07/10
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.work.web.form;

import java.util.ArrayList;
import java.util.List;

import org.swallow.work.model.CorporateAccount;

/**
 * CorporateAccountForm.java
 * 
 * This form bean to get the corporate account details from screen
 * 
 * <AUTHOR> G
 * @date July 13, 2010
 */
public class CorporateAccountForm {
	private String valueDate;
	
	List<CorporateAccount> listCorpAcct = new ArrayList<CorporateAccount>();

	/**
	 * @return the valueDate
	 */
	public String getValueDate() {
		return valueDate;
	}

	/**
	 * @param valueDate the valueDate to set
	 */
	public void setValueDate(String valueDate) {
		this.valueDate = valueDate;
	}

	/**
	 * @return the listCorpAcct
	 */
	public List<CorporateAccount> getListCorpAcct() {
		return listCorpAcct;
	}

	/**
	 * @param listCorpAcct the listCorpAcct to set
	 */
	public void setListCorpAcct(List<CorporateAccount> listCorpAcct) {
		this.listCorpAcct = listCorpAcct;
	}

}
