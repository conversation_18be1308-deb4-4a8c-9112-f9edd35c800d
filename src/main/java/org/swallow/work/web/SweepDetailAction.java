/*
 * @(#)SweepDetailAction.java 1.0 23/01/06
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */

package org.swallow.work.web;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Iterator;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;


import org.swallow.control.service.ArchiveManager;
import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.maintenance.model.AcctMaintenance;
import org.swallow.maintenance.model.MiscParams;
import org.swallow.model.User;
import org.swallow.util.CacheManager;
import org.swallow.util.CommonDataManager;
import org.swallow.util.SwtConstants;
import org.swallow.util.SwtUtil;
import org.swallow.util.SystemFormats;
import org.swallow.work.model.Movement;
import org.swallow.work.model.Sweep;
import org.swallow.work.model.SweepAmount;
import org.swallow.work.model.SweepDetail;
import org.swallow.work.model.SweepMsgFormat;
import org.swallow.work.service.SweepDetailManager;
import org.swallow.work.service.SweepDetailValObj;
import org.swallow.work.service.SweepPriorCutOffManager;
import org.swallow.util.LabelValueBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.apache.struts2.convention.annotation.Result;
import org.apache.struts2.convention.annotation.AllowedMethods;
import org.apache.struts2.convention.annotation.Action;
import org.apache.struts2.ServletActionContext;
import org.swallow.util.struts.ActionErrors;
import org.swallow.util.struts.ActionMessages;
import org.swallow.util.struts.CustomActionSupport;

/**
 * <pre>
 *  This class is used to Display sweep details
 * </pre>
 */
@Action(value = "/sweepdetail", results = {
	@Result(name = "success", location = "/jsp/work/sweepdetail.jsp"),
	@Result(name = "sweepMessageDisplay", location = "/jsp/work/sweepMessageDiplay.jsp"),
	@Result(name = "fail", location = "/error.jsp"),
})

@AllowedMethods ({"manual" ,"sweep" ,"submit" ,"displayQueue" ,"sweepMessageDisplay" ,"updateSweepDetails" })
public class SweepDetailAction extends CustomActionSupport {
public String execute() throws Exception {
    HttpServletRequest request = ServletActionContext.getRequest();

    // List of methods 
    String method = String.valueOf(request.getParameter("method"));

    switch (method) {
        case "unspecified":
            return unspecified();
        case "manual":
            return manual();
        case "sweep":
            return sweep();
        case "submit":
            return submit();
        case "displayQueue":
            return displayQueue();
        case "sweepMessageDisplay":
            return sweepMessageDisplay();
        case "updateSweepDetails":
            return updateSweepDetails();
        default:
            break;
    }

    return unspecified();
}


private Movement movement;
public Movement getMovement() {
	if (movement == null) {
		movement = new Movement();
	}
	return movement;
}
public void setMovement(Movement movement) {
	this.movement = movement;
	HttpServletRequest request = ServletActionContext.getRequest();
	request.setAttribute("movement", movement);
}
private SweepAmount sweepAmount;
public SweepAmount getSweepAmount() {
	if (sweepAmount == null) {
		sweepAmount = new SweepAmount();
	}
	return sweepAmount;
}
public void setSweepAmount(SweepAmount sweepAmount) {
	this.sweepAmount = sweepAmount;
	HttpServletRequest request = ServletActionContext.getRequest();
	request.setAttribute("sweepAmount", sweepAmount);
}
private SweepDetail sweepAccount1;
public SweepDetail getSweepAccount1() {
	if (sweepAccount1 == null) {
		sweepAccount1 = new SweepDetail();
	}
	return sweepAccount1;
}
public void setSweepAccount1(SweepDetail sweepAccount1) {
	this.sweepAccount1 = sweepAccount1;
	HttpServletRequest request = ServletActionContext.getRequest();
	request.setAttribute("sweepAccount1", sweepAccount1);
}
private SweepDetail sweepAccount2;
public SweepDetail getSweepAccount2() {
	if (sweepAccount2 == null) {
		sweepAccount2 = new SweepDetail();
	}
	return sweepAccount2;
}
public void setSweepAccount2(SweepDetail sweepAccount2) {
	this.sweepAccount2 = sweepAccount2;
	HttpServletRequest request = ServletActionContext.getRequest();
	request.setAttribute("sweepAccount2", sweepAccount2);
}


	@Autowired
private SweepDetailManager sweepDetailManager = null;

	/**
	 * Comment for <code>log</code>
	 */
	private final Log log = LogFactory.getLog(SweepDetailAction.class);

	/**
	 * @param matchManager
	 */
	public void setSweepDetailManager(SweepDetailManager sweepDetailManager) {
		this.sweepDetailManager = sweepDetailManager;
	}

	/**
	 * @param request
	 * @return
	 */
	public String getRoleId(HttpServletRequest request) {
		CommonDataManager CDM = (CommonDataManager) request.getSession()
				.getAttribute("CDM");
		User currUser = (User) CDM.getUser();
		String roleId = currUser.getRoleId();
		return roleId;
	}

	/**
	 * @param request
	 * @return
	 */
	public String getUserId(HttpServletRequest request) {
		CommonDataManager CDM = (CommonDataManager) request.getSession()
				.getAttribute("CDM");
		User currUser = (User) CDM.getUser();
		String userId = currUser.getId().getUserId();

		return userId;
	}

	/**
	 * @param request
	 * @throws SwtException
	 */
	private void putEntityListInReq(HttpServletRequest request)
			throws SwtException {
		log.debug("entering 'putEntityListInReq' method");

		HttpSession session = request.getSession();
		Collection coll = SwtUtil.getUserEntityAccessList(session);
		coll = SwtUtil.convertEntityAcessCollectionLVL(coll, session);
		request.setAttribute("entities", coll);

		log.debug("exiting 'putEntityListInReq' method");
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see org.apache.struts.actions.CustomActionSupport#unspecified(org.apache.struts.action.ActionMapping,
	 *      org.apache.struts.action.ActionForm,
	 *      javax.servlet.http.HttpServletRequest,
	 *      javax.servlet.http.HttpServletResponse)
	 */
	public String unspecified()
			throws SwtException {
HttpServletRequest request = ServletActionContext.getRequest();
HttpServletResponse response = ServletActionContext.getResponse();

		try {
			log.debug("entering 'unspecified' method");
			log.debug("exiting 'unspecified' method");

			return displayQueue();
		} catch (SwtException swtexp) {
			log
					.error("SwtException Catch in SweepDetailAction.'unspecified' method : "
							+ swtexp.getMessage());
			SwtUtil.logException(swtexp, request, "");

			return ("fail");
		} catch (Exception exp) {
			log
					.error("Exception Catch in SweepDetailAction.'unspecified' method : "
							+ exp.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "unspecified", SweepDetailAction.class), request, "");

			return ("fail");
		}
	}

	/*
	 * @param request @throws SwtException
	 * 
	 */
	public String manual()
			throws SwtException {
HttpServletRequest request = ServletActionContext.getRequest();
HttpServletResponse response = ServletActionContext.getResponse();

		ActionErrors errors = new ActionErrors();

		try {
			log.debug("entering 'manual' method");
			// DynaValidatorForm Movement movement = (Movement) getMovement();
			String calledFrom = request.getParameter("calledFrom");
			int entityAcess1 = 0;
			int entityAcess2 = 0;
			String currentEntity = "";
			String entities = "";
			String accounts = "";
			String valueDates = "";
			String valuedate1 = "";
			String tempaccountiD1 = "";
			String accountId1 = "";
			String entityId1 = "";
			String tempdata2 = "";
			String valudate2 = "";
			String accountId2 = "";
			String entityId2 = "";
			String hostId = "";
			String strselectedList = "";
			String replacedstrselectedList = "";
			String tempcurrEntiy = "";
			String tempCurrCode1 = "";
			String currCode1 = "";
			String mainAccountId = "";
			String mainCurrCode = "";
			String sweepFrom = "";

			if (calledFrom.equalsIgnoreCase("manualsweeping")) {
				strselectedList = request.getParameter("selectedList");

				replacedstrselectedList = strselectedList.replaceAll("'", "");
				valuedate1 = replacedstrselectedList.substring(0,
						(replacedstrselectedList.indexOf(",") - 1));

				tempaccountiD1 = replacedstrselectedList.substring(
						(replacedstrselectedList.indexOf(",") + 1),
						(replacedstrselectedList.indexOf("|")));

				accountId1 = tempaccountiD1.substring(0, (tempaccountiD1
						.lastIndexOf(",") - 1));

				entityId1 = tempaccountiD1.substring((tempaccountiD1
						.lastIndexOf(",") + 1), tempaccountiD1.length());

				tempdata2 = replacedstrselectedList.substring(
						(replacedstrselectedList.indexOf("|") + 1),
						(replacedstrselectedList.lastIndexOf("|")));

				valudate2 = tempdata2
						.substring(0, (tempdata2.indexOf(",") - 1));

				accountId2 = tempdata2.substring((tempdata2.indexOf(",") + 1),
						(tempdata2.lastIndexOf(",") - 1));

				entityId2 = tempdata2.substring(
						(tempdata2.lastIndexOf(",") + 1), tempdata2.length());

				entities = "'" + entityId1 + "','" + entityId2 + "'";
				accounts = "'" + accountId1 + "','" + accountId2 + "'";
				valueDates = valudate2;
				currentEntity = SwtUtil.getUserCurrentEntity(request
						.getSession());

				Collection coll = SwtUtil.getUserEntityAccessList(request
						.getSession());
				entityAcess1 = SwtUtil.getUserEntityAccess(coll, entityId1);
				entityAcess2 = SwtUtil.getUserEntityAccess(coll, entityId2);
				request.setAttribute("parentMethod", "manualsweeping");

			}

			else if (calledFrom.equalsIgnoreCase("sweeppriorcutoff")) {
				log.debug("Inside sweeppriorcutoff");
				hostId = CacheManager.getInstance().getHostId();

				strselectedList = request.getParameter("selectedList");

				replacedstrselectedList = strselectedList.replaceAll("'", "");
				valuedate1 = replacedstrselectedList.substring(0,
						(replacedstrselectedList.indexOf(",") - 1));

				tempaccountiD1 = replacedstrselectedList.substring(
						(replacedstrselectedList.indexOf(",") + 1),
						(replacedstrselectedList.indexOf("|")));

				accountId1 = tempaccountiD1.substring(0, (tempaccountiD1
						.indexOf(",") - 1));

				tempcurrEntiy = tempaccountiD1.substring((tempaccountiD1
						.indexOf(",") + 1), (tempaccountiD1.length()));

				entityId1 = tempcurrEntiy.substring(0, (tempcurrEntiy
						.indexOf(",") - 1));

				tempCurrCode1 = tempcurrEntiy.substring((tempcurrEntiy
						.indexOf(",") + 1), tempcurrEntiy.length());

				currCode1 = tempCurrCode1.substring(0, (tempCurrCode1
						.indexOf(",") - 1));

				mainAccountId = tempCurrCode1.substring((tempCurrCode1
						.lastIndexOf(",") + 1), tempCurrCode1.length());

				SweepPriorCutOffManager mgr = (SweepPriorCutOffManager) (SwtUtil
						.getBean("sweepPriorCutOffManager"));
				Collection mainAccountIdColl = mgr.getMainAccountDetails(
						hostId, entityId1, mainAccountId);

				Iterator itr = mainAccountIdColl.iterator();
				while (itr.hasNext()) {
					AcctMaintenance acct = (AcctMaintenance) itr.next();
					mainCurrCode = acct.getCurrcode();

					sweepFrom = acct.getSweepFrmbal();

				}

				entities = "'" + entityId1 + "','" + entityId1 + "'";
				accounts = "'" + accountId1 + "','" + mainAccountId + "'";
				valueDates = valuedate1;
				currentEntity = SwtUtil.getUserCurrentEntity(request
						.getSession());

				Collection coll = SwtUtil.getUserEntityAccessList(request
						.getSession());
				entityAcess1 = SwtUtil.getUserEntityAccess(coll, entityId1);
				entityAcess2 = SwtUtil.getUserEntityAccess(coll, entityId1);
				request.setAttribute("parentMethod", "sweeppriorcutoff");
			}

			if ((entityAcess1 == SwtConstants.ENTITY_FULL_ACCESS)
					&& (entityAcess2 == SwtConstants.ENTITY_FULL_ACCESS)) {
				
				String selectedSweepScheduleSetting  = request.getParameter("selectedSweepScheduleSetting");
				if(SwtUtil.isEmptyOrNull(selectedSweepScheduleSetting)) {
					selectedSweepScheduleSetting = "Account"; 
				}
				
				SweepDetailValObj sweepDetailVO = sweepDetailManager
						.getAccountDetails(currentEntity, accounts, valueDates,
								SwtUtil.getCurrentSystemFormats(request
										.getSession()), entities, selectedSweepScheduleSetting);
				SweepDetail acct1 = new SweepDetail();
				SweepDetail acct2 = new SweepDetail();

				Iterator itr = sweepDetailVO.getAccountDetail().iterator();

				acct1 = (SweepDetail) itr.next();
				acct2 = (SweepDetail) itr.next();

				SweepAmount sweepamt = new SweepAmount();
				sweepamt = (SweepAmount) sweepDetailVO.getSweepAmountDetail()
						.iterator().next();

				int alignIndex = sweepDetailVO.getAlignIndex();

				if (alignIndex == 1) {
					setSweepAccount1(acct1);
					setSweepAccount2(acct2);
					request.setAttribute("bookcodeFirst", acct1.getBookcode());
					request.setAttribute("bookCodeSecond", acct2.getBookcode());
				} else {
					setSweepAccount1(acct2);
					setSweepAccount2(acct1);
					request.setAttribute("bookcodeFirst", acct2.getBookcode());
					request.setAttribute("bookCodeSecond", acct1.getBookcode());
				}
				
				SweepAmount sweepAmtForm = (SweepAmount) getSweepAmount();
				if(sweepAmtForm != null) {
					sweepamt.setAdditionalReference(sweepAmtForm.getAdditionalReference());
				}
				
				sweepamt.setSelectedList(request.getParameter("selectedList"));
				if(!sweepamt.isScheduleSweepExist()) {
					selectedSweepScheduleSetting = "Default"; 
				}
				sweepamt.setSelectedScheduleSweepOption(selectedSweepScheduleSetting);
//				dynForm.set("sweepAmount", sweepamt);
				setSweepAmount(sweepamt);
				request.setAttribute("sweep", "org");
				
			
				
				
				CacheManager cacheManagerInst = CacheManager.getInstance();
				// get the target sign collection according to the entity id
				Collection targetSignCol = cacheManagerInst.getMiscParamsLVL(
						"SWEEP_SETTLE_METHOD", acct1.getEntityId());
				// get the list of the target sign.
				ArrayList targetSignList = (ArrayList) targetSignCol;
//				request.setAttribute("defaultSettleMethodList1", targetSignList);
				
				
				if (sweepDetailVO.getAlignIndex() == 1) {
					request.setAttribute("defaultSettleMethodList1", targetSignList);
				}else {
					request.setAttribute("defaultSettleMethodList2", targetSignList);
				}
				
				
				
				// get the target sign collection according to the entity id
				targetSignCol = cacheManagerInst.getMiscParamsLVL(
						"SWEEP_SETTLE_METHOD", acct2.getEntityId());
				// get the list of the target sign.
				targetSignList = (ArrayList) targetSignCol;
//				request.setAttribute("defaultSettleMethodList2", targetSignList);
				if (sweepDetailVO.getAlignIndex() == 1) {
					request.setAttribute("defaultSettleMethodList2", targetSignList);
				}else {
					request.setAttribute("defaultSettleMethodList1", targetSignList);
				}
				
			} else {
				log.debug("collection is null");
				throw new SwtException("manual.doesnotmatch", "N");
			}

			request.getSession().setAttribute(
					SwtConstants.NOTES_SWEEP_SESSION_OBJECT, null);

			log.debug("exiting 'manual' method");

			return ("success");
		} catch (SwtException swtexp) {
			swtexp.printStackTrace();
			log
					.error("SwtException Catch in SweepDetailAction.'manual' method : "
							+ swtexp.getMessage());

			saveErrors(request, SwtUtil.logException(swtexp, request, ""));

			return ("success");
		} catch (Exception exp) {
			exp.printStackTrace();
			log.error("Exception Catch in SweepDetailAction.'manual' method : "
					+ exp.getMessage());

			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "manual", SweepDetailAction.class), request, "");

			return ("fail");
		}
	}

	/**
	 * This method is used to sweep accounts
	 * 
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return
	 * @throws SwtException
	 */
	public String sweep()
			throws SwtException {
HttpServletRequest request = ServletActionContext.getRequest();
HttpServletResponse response = ServletActionContext.getResponse();

		// variable to hold errors
		ActionErrors errors = null;
		// variable to hold sweepAct1
		SweepDetail sweepAct1 = null;
		// variable to hold sweepAct2
		SweepDetail sweepAct2 = null;
		// variable to hold sweepAmt
		SweepAmount sweepAmt = null;
		// variable to hold sessionNotesDetails
		Collection sessionNotesDetails = null;
		// variable to hold roleId
		String roleId = null;
		// variable to hold userId
		String userId = null;
		// variable to hold cutOffExceed
		boolean cutOffExceed;
		// variable to hold sweepGenerated
		int sweepGenerated;
		String additionalReferences = null;
		try {
			log.debug(this.getClass().getName() + " - [sweep] - Entering");
			// instance for ActionErrors
			errors = new ActionErrors();
			// instance for sweepdetail
			sweepAct1 = new SweepDetail();
			// instance for sweepdetail
			sweepAct2 = new SweepDetail();
			// instance for sweep amount
			sweepAmt = new SweepAmount();
			// get sweep account1 form
			sweepAct1 = (SweepDetail) getSweepAccount1();
			// get the sweep account2 form
			sweepAct2 = (SweepDetail) getSweepAccount2();
			// get the sweep amount from dyform
			sweepAmt = (SweepAmount) getSweepAmount();
			// get notes details form session
			sessionNotesDetails = (Collection) request.getSession()
					.getAttribute(SwtConstants.NOTES_SWEEP_SESSION_OBJECT);
			
//			additionalReferences = request.getParameter("additionalReferences");
			// get role id
			roleId = getRoleId(request);
			// get user id
			userId = getUserId(request);
			// checks the checkcutoff
			if (request.getParameter("checkCutOff").equalsIgnoreCase("yes")) {
				cutOffExceed = sweepDetailManager.cutOffExeeded(sweepAct1,
						sweepAct2);
				if (!cutOffExceed) {
					setSweepAccount1(sweepAct1);
					setSweepAccount2(sweepAct2);
					setSweepAmount(sweepAmt);

					if (request.getParameter("calledFrom").equalsIgnoreCase(
							"sweeppriorcutoff")) {
						request.setAttribute("sweep", "org");
						request
								.setAttribute("parentMethod",
										"sweeppriorcutoff");
					} else {
						request.setAttribute("sweep", "org");
						request.setAttribute("parentMethod", "manualsweeping");
					}

					request.setAttribute("cuttOff", "yes");
					
					
					request.setAttribute("bookcodeFirst", sweepAct1.getBookcode());
					request.setAttribute("bookCodeSecond", sweepAct2.getBookcode());
					
					
					CacheManager cacheManagerInst = CacheManager.getInstance();
					// get the target sign collection according to the entity id
					Collection targetSignCol = cacheManagerInst.getMiscParamsLVL(
							"SWEEP_SETTLE_METHOD", sweepAct1.getEntityId());
					// get the list of the target sign.
					ArrayList targetSignList = (ArrayList) targetSignCol;
					request.setAttribute("defaultSettleMethodList1", targetSignList);
					
					
					
					// get the target sign collection according to the entity id
					targetSignCol = cacheManagerInst.getMiscParamsLVL(
							"SWEEP_SETTLE_METHOD", sweepAct2.getEntityId());
					// get the list of the target sign.
					targetSignList = (ArrayList) targetSignCol;
					request.setAttribute("defaultSettleMethodList2", targetSignList);
					
					return ("success");
				} else {
					// get the generated sweep
					sweepGenerated = sweepDetailManager.sweep(sweepAct1,
							sweepAct2, sweepAmt, SwtUtil
									.getCurrentSystemFormats(request
											.getSession()), roleId, userId,
							sessionNotesDetails);
					// setting the sweepidgenerated in request
					request.setAttribute("sweepIdGenerated", Integer
							.toString(sweepGenerated));
					// setting the parentFormRefresh in request
					request.setAttribute("parentFormRefresh", "no");
					
					
					// checks sweep prior cutoff
					if (request.getParameter("calledFrom").equalsIgnoreCase(
							"sweeppriorcutoff")) {
						// set sweep in request
						request.setAttribute("sweep", "org");
						// set parent method in request
						request
								.setAttribute("parentMethod",
										"sweeppriorcutoff");
					} else {
						request.setAttribute("sweep", "org");
						request.setAttribute("parentMethod", "manualsweeping");
					}

				}
			} else {
				// get the generated sweep id
				sweepGenerated = sweepDetailManager.sweep(sweepAct1, sweepAct2,
						sweepAmt, SwtUtil.getCurrentSystemFormats(request
								.getSession()), roleId, userId,
						sessionNotesDetails);
				// set the generated sweep id in request
				request.setAttribute("sweepIdGenerated", Integer
						.toString(sweepGenerated));
				// set parentformrefresh in request
				request.setAttribute("parentFormRefresh", "no");

				if (request.getParameter("calledFrom").equalsIgnoreCase(
						"sweeppriorcutoff")) {
					request.setAttribute("sweep", "org");
					request.setAttribute("parentMethod", "sweeppriorcutoff");
				} else {
					request.setAttribute("sweep", "org");
					request.setAttribute("parentMethod", "manualsweeping");
				}

			}
			
			request.setAttribute("bookcodeFirst", sweepAct1.getBookcode());
			request.setAttribute("bookCodeSecond", sweepAct2.getBookcode());
			
			
			CacheManager cacheManagerInst = CacheManager.getInstance();
			// get the target sign collection according to the entity id
			Collection targetSignCol = cacheManagerInst.getMiscParamsLVL(
					"SWEEP_SETTLE_METHOD", sweepAct1.getEntityId());
			// get the list of the target sign.
			ArrayList targetSignList = (ArrayList) targetSignCol;
			request.setAttribute("defaultSettleMethodList1", targetSignList);
			
			
			
			// get the target sign collection according to the entity id
			targetSignCol = cacheManagerInst.getMiscParamsLVL(
					"SWEEP_SETTLE_METHOD", sweepAct2.getEntityId());
			// get the list of the target sign.
			targetSignList = (ArrayList) targetSignCol;
			request.setAttribute("defaultSettleMethodList2", targetSignList);
			
			// set sweep session object in request
			request.getSession().setAttribute(
					SwtConstants.NOTES_SWEEP_SESSION_OBJECT, null);
			return ("success");
			/*
			 * Start:Code Modified for Mantis 1877 by sandeep kumar on
			 * 24-APR-12-For Manual sweeping: Unnecessary error written to
			 * server log when a sweep limit is not defined
			 */
		} catch (SwtException swtexp) {
			log
					.info("SwtException Catch in SweepDetailAction.'sweep' method : "
							+ swtexp.getMessage());
			/*
			 * End:Code Modified for Mantis 1877 by sandeep kumar on
			 * 24-APR-12-For Manual sweeping: Unnecessary error written to
			 * server log when a sweep limit is not defined
			 */
			// Declaration to hold the form
			// instance for sweepdetail
			sweepAct1 = new SweepDetail();
			// instance for sweepdetail
			sweepAct2 = new SweepDetail();
			// instance for sweepamount
			sweepAmt = new SweepAmount();
			// get sweep account1 from dyform
			sweepAct1 = (SweepDetail) getSweepAccount1();
			// get sweep account2 from dyform
			sweepAct2 = (SweepDetail) getSweepAccount2();
			// get sweep amount from dyform
			sweepAmt = (SweepAmount) getSweepAmount();
			// set sweep account1 into dyform
			setSweepAccount1(sweepAct1);
			// set sweep account2 into dyform
			setSweepAccount2(sweepAct2);
			// set sweep amount into dyform
			setSweepAmount(sweepAmt);
			// set sweep in request
			request.setAttribute("sweep", "org");
			
			request.setAttribute("bookcodeFirst", sweepAct1.getBookcode());
			request.setAttribute("bookCodeSecond", sweepAct2.getBookcode());
			
			
			CacheManager cacheManagerInst = CacheManager.getInstance();
			// get the target sign collection according to the entity id
			Collection targetSignCol = cacheManagerInst.getMiscParamsLVL(
					"SWEEP_SETTLE_METHOD", sweepAct1.getEntityId());
			// get the list of the target sign.
			ArrayList targetSignList = (ArrayList) targetSignCol;
			request.setAttribute("defaultSettleMethodList1", targetSignList);
			
			
			
			// get the target sign collection according to the entity id
			targetSignCol = cacheManagerInst.getMiscParamsLVL(
					"SWEEP_SETTLE_METHOD", sweepAct2.getEntityId());
			// get the list of the target sign.
			targetSignList = (ArrayList) targetSignCol;
			request.setAttribute("defaultSettleMethodList2", targetSignList);
			
			// set parentmethod in request
			request.setAttribute("parentMethod", "manualsweeping");
			// save errors
			saveErrors(request, SwtUtil.logException(swtexp, request, ""));
			SwtUtil.logException(swtexp, request, "");
			return ("success");
		} catch (Exception exp) {
			log.error("Exception Catch in SweepDetailAction.'sweep' method : "
					+ exp.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "sweep", SweepDetailAction.class), request, "");
			return ("fail");
		} finally {
			// nullyfing objects
			sweepAct1 = null;
			sweepAct2 = null;
			sweepAmt = null;
			sessionNotesDetails = null;
			roleId = null;
			userId = null;
		}
	}

	public String submit()
			throws SwtException {
HttpServletRequest request = ServletActionContext.getRequest();
HttpServletResponse response = ServletActionContext.getResponse();

		ActionErrors errors = new ActionErrors();

		try {
			log.debug("entering submit method");

//			DynaActionForm dyForm = (DynaActionForm) form;
			SweepDetail sweepAct1 = new SweepDetail();
			SweepDetail sweepAct2 = new SweepDetail();
			SweepAmount sweepAmt = new SweepAmount();
			sweepAct1 = (SweepDetail) getSweepAccount1();
			sweepAct2 = (SweepDetail) getSweepAccount2();
			sweepAmt = (SweepAmount) getSweepAmount();

			String roleId = getRoleId(request);
			ActionMessages list = null;
			CommonDataManager CDM = (CommonDataManager) request.getSession()
					.getAttribute("CDM");

			User userinsession = CDM.getUser();
			String userId = userinsession.getId().getUserId();
			String currentSweepId = request.getParameter("currentSweepId");

			Long sweepId = Long.valueOf(currentSweepId);

			String process = request.getParameter("process");

			String checkCutOff = request.getParameter("checkCutOff");

			if (checkCutOff.equalsIgnoreCase("yes")) {
				boolean cutOffExceed = sweepDetailManager.cutOffExeeded(
						sweepAct1, sweepAct2);

				if (!cutOffExceed) {
					setSweepAccount1(sweepAct1);
					setSweepAccount2(sweepAct2);
					setSweepAmount(sweepAmt);

					if (process.equals("submit")) {
						request.setAttribute("sweep", "submit");
					} else {
						request.setAttribute("sweep", "auth");
					}
					
					request.setAttribute("bookcodeFirst", sweepAct1.getBookcode());
					request.setAttribute("bookCodeSecond", sweepAct2.getBookcode());
					
					
					CacheManager cacheManagerInst = CacheManager.getInstance();
					// get the target sign collection according to the entity id
					Collection targetSignCol = cacheManagerInst.getMiscParamsLVL(
							"SWEEP_SETTLE_METHOD", sweepAct1.getEntityId());
					// get the list of the target sign.
					ArrayList targetSignList = (ArrayList) targetSignCol;
					request.setAttribute("defaultSettleMethodList1", targetSignList);
					
					// get the target sign collection according to the entity id
					targetSignCol = cacheManagerInst.getMiscParamsLVL(
							"SWEEP_SETTLE_METHOD", sweepAct2.getEntityId());
					// get the list of the target sign.
					targetSignList = (ArrayList) targetSignCol;
					request.setAttribute("defaultSettleMethodList2", targetSignList);
					

					request.setAttribute("sweepId", sweepId);
					request.setAttribute("cuttOff", "yes");

					if (process.equals("submit")) {
						request.setAttribute("sweep", "submit");
					} else {
						request.setAttribute("sweep", "auth");
					}

					return ("success");
				} else {
					sweepDetailManager.submit(sweepAct1, sweepAct2, sweepAmt,
							SwtUtil.getCurrentSystemFormats(request
									.getSession()), roleId, sweepId, userId,
							process);

					if (process.equals("submit")) {
						request.setAttribute("sweep", "submit");
					} else {
						request.setAttribute("sweep", "auth");
					}
					
					request.setAttribute("bookcodeFirst", sweepAct1.getBookcode());
					request.setAttribute("bookCodeSecond", sweepAct2.getBookcode());
					
					
					CacheManager cacheManagerInst = CacheManager.getInstance();
					// get the target sign collection according to the entity id
					Collection targetSignCol = cacheManagerInst.getMiscParamsLVL(
							"SWEEP_SETTLE_METHOD", sweepAct1.getEntityId());
					// get the list of the target sign.
					ArrayList targetSignList = (ArrayList) targetSignCol;
					request.setAttribute("defaultSettleMethodList1", targetSignList);
					
					// get the target sign collection according to the entity id
					targetSignCol = cacheManagerInst.getMiscParamsLVL(
							"SWEEP_SETTLE_METHOD", sweepAct2.getEntityId());
					// get the list of the target sign.
					targetSignList = (ArrayList) targetSignCol;
					request.setAttribute("defaultSettleMethodList2", targetSignList);
					

					request.setAttribute("sweepIdGenerated", sweepId);
					request.setAttribute("parentFormRefresh", "no");
				}
			} else {
				sweepDetailManager.submit(sweepAct1, sweepAct2, sweepAmt,
						SwtUtil.getCurrentSystemFormats(request.getSession()),
						roleId, sweepId, userId, process);

				if (process.equals("submit")) {
					request.setAttribute("sweep", "submit");
				} else {
					request.setAttribute("sweep", "auth");
				}
				
				request.setAttribute("bookcodeFirst", sweepAct1.getBookcode());
				request.setAttribute("bookCodeSecond", sweepAct2.getBookcode());
				
				
				CacheManager cacheManagerInst = CacheManager.getInstance();
				// get the target sign collection according to the entity id
				Collection targetSignCol = cacheManagerInst.getMiscParamsLVL(
						"SWEEP_SETTLE_METHOD", sweepAct1.getEntityId());
				// get the list of the target sign.
				ArrayList targetSignList = (ArrayList) targetSignCol;
				request.setAttribute("defaultSettleMethodList1", targetSignList);
				
				// get the target sign collection according to the entity id
				targetSignCol = cacheManagerInst.getMiscParamsLVL(
						"SWEEP_SETTLE_METHOD", sweepAct2.getEntityId());
				// get the list of the target sign.
				targetSignList = (ArrayList) targetSignCol;
				request.setAttribute("defaultSettleMethodList2", targetSignList);

				request.setAttribute("sweepIdGenerated", sweepId);
				request.setAttribute("parentFormRefresh", "no");
			}

			return ("success");
		} catch (SwtException swtexp) {
			log
					.error("SwtException Catch in SweepDetailAction.'submit' method : "
							+ swtexp.getMessage());

			SweepDetail sweepAct1 = new SweepDetail();
			SweepDetail sweepAct2 = new SweepDetail();
			SweepAmount sweepAmt = new SweepAmount();
			sweepAct1 = (SweepDetail) getSweepAccount1();
			sweepAct2 = (SweepDetail) getSweepAccount2();
			sweepAmt = (SweepAmount) getSweepAmount();
			setSweepAccount1(sweepAct1);
			setSweepAccount2(sweepAct2);
			setSweepAmount(sweepAmt);

			String process = request.getParameter("process");
			String currentSweepId = request.getParameter("currentSweepId");

			Long sweepId = Long.valueOf(currentSweepId);

			if (process.equals("submit")) {
				request.setAttribute("sweep", "submit");
			} else {
				request.setAttribute("sweep", "auth");
			}

			request.setAttribute("sweepId", sweepId);
			
			request.setAttribute("bookcodeFirst", sweepAct1.getBookcode());
			request.setAttribute("bookCodeSecond", sweepAct2.getBookcode());
			
			
			CacheManager cacheManagerInst = CacheManager.getInstance();
			// get the target sign collection according to the entity id
			Collection targetSignCol = cacheManagerInst.getMiscParamsLVL(
					"SWEEP_SETTLE_METHOD", sweepAct1.getEntityId());
			// get the list of the target sign.
			ArrayList targetSignList = (ArrayList) targetSignCol;
			request.setAttribute("defaultSettleMethodList1", targetSignList);
			
			// get the target sign collection according to the entity id
			targetSignCol = cacheManagerInst.getMiscParamsLVL(
					"SWEEP_SETTLE_METHOD", sweepAct2.getEntityId());
			// get the list of the target sign.
			targetSignList = (ArrayList) targetSignCol;
			request.setAttribute("defaultSettleMethodList2", targetSignList);
			
			saveErrors(request, SwtUtil.logException(swtexp, request, ""));

			return ("success");
		} catch (Exception exp) {
			log.error("Exception Catch in SweepDetailAction.'submit' method : "
					+ exp.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "submit", SweepDetailAction.class), request, "");

			return ("fail");
		}
	}

	/**
	 * This method is used to display the sweep details based upon the sweep id
	 * 
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return
	 * @throws SwtException
	 */
	public String displayQueue()
			throws SwtException {
HttpServletRequest request = ServletActionContext.getRequest();
HttpServletResponse response = ServletActionContext.getResponse();

		// decleartion of dyForm
		// variable to hold sweepDetailVO
		SweepDetailValObj sweepDetailVO = null;
		// variable to hold acct1 details
		SweepDetail sweepAcct1 = null;
		// variable to hold acct2 details
		SweepDetail sweepAcct2 = null;
		// iterate sweepDetailsItr
		Iterator<SweepDetail> sweepDetailsItr = null;
		// variable to hold sweepAmount
		SweepAmount sweepAmount = null;
		String archiveId = null;
		String  dbLink = null;
		try {
			log.debug("entering displayQueue method");
			// get the form assign into the DynaActionForm form
			// Getting the sweep details
			
			archiveId = request.getParameter("archiveId");
			ArchiveManager archiveManager = (ArchiveManager) SwtUtil.getBean("archiveManager");
			if(!SwtUtil.isEmptyOrNull(archiveId)) {
				dbLink = archiveManager.getDBlink(archiveId);
			}
			if(SwtUtil.isEmptyOrNull(archiveId)) {
				sweepDetailVO = sweepDetailManager.getSweepDetails(request
						.getParameter("entid"), Long.valueOf(request
								.getParameter("swpid")), SwtUtil
						.getCurrentSystemFormats(request.getSession()), request
						.getParameter("qname").trim());
			}else {
				sweepDetailVO = sweepDetailManager.getSweepDetailsArchive(request
						.getParameter("entid"), Long.valueOf(request
								.getParameter("swpid")), SwtUtil
						.getCurrentSystemFormats(request.getSession()), request
						.getParameter("qname").trim(), dbLink);
			}

			// Get the account details
			sweepDetailsItr = sweepDetailVO.getAccountDetail().iterator();
			// Iterating the accounts
			sweepAcct1 = sweepDetailsItr.next();
			sweepAcct2 = sweepDetailsItr.next();
			// Instance for sweep amount
			sweepAmount = new SweepAmount();
			// Getting the sweep amount details
			sweepAmount = (SweepAmount) sweepDetailVO.getSweepAmountDetail()
					.iterator().next();

			if (sweepDetailVO.getAlignIndex() == 1) {
				setSweepAccount1(sweepAcct1);
				setSweepAccount2(sweepAcct2);
				request.setAttribute("bookcodeFirst", sweepAcct1.getBookcode());
				request.setAttribute("bookCodeSecond", sweepAcct2.getBookcode());
			} else {
				setSweepAccount1(sweepAcct2);
				setSweepAccount2(sweepAcct1);
				request.setAttribute("bookcodeFirst", sweepAcct2.getBookcode());
				request.setAttribute("bookCodeSecond", sweepAcct1.getBookcode());
			}
			// set the sweep amount in dyform
			setSweepAmount(sweepAmount);

			if (request.getParameter("qname").trim().equals(
					SwtConstants.SWEEP_STATUS_NEW)) {
				request.setAttribute("sweep", "submit");
			} else if (request.getParameter("qname").trim().equals(
					SwtConstants.SWEEP_STATUS_SUBMIT)) {
				request.setAttribute("sweep", "auth");
			} else if (request.getParameter("qname").trim().equals(
					SwtConstants.SWEEP_STATUS_CANCEL)) {
				request.setAttribute("sweep", "display");
			}
			// set the attribute for currGrpAccess
			request.setAttribute("currGrpAccess", request
					.getParameter("currencyAccess"));
			// set the attribute for sweep id
			request.setAttribute("sweepId", Long.valueOf(request
					.getParameter("swpid")));
			// set the attribute for archiveId
			request.setAttribute("archiveId" , request.getParameter("archiveId"));
			
			
			
			CacheManager cacheManagerInst = CacheManager.getInstance();
			// get the target sign collection according to the entity id
			Collection targetSignCol = cacheManagerInst.getMiscParamsLVL(
					"SWEEP_SETTLE_METHOD", sweepAcct1.getEntityId());
			// get the list of the target sign.
			ArrayList targetSignList = (ArrayList) targetSignCol;
		
			if(request.getParameter("qname").trim().equals(
					SwtConstants.SWEEP_STATUS_CANCEL) && !SwtUtil.isEmptyOrNull(sweepAcct1.getDefaultSettleMethod())) {
				boolean found = false;
				for (int i = 0; i < targetSignList.size(); i++) {
					LabelValueBean bean = (LabelValueBean) targetSignList.get(i);
					if(bean.getValue().equals(sweepAcct1.getDefaultSettleMethod())) {
						found = true;
						break;
					}
				}
				if(!found) {
					targetSignList.add(new LabelValueBean(sweepAcct1.getDefaultSettleMethod(), sweepAcct1.getDefaultSettleMethod()));
				}
			}
			if (sweepDetailVO.getAlignIndex() == 1) {
				request.setAttribute("defaultSettleMethodList1", targetSignList);
			}else {
				request.setAttribute("defaultSettleMethodList2", targetSignList);
			}
			
			
			
			// get the target sign collection according to the entity id
			targetSignCol = cacheManagerInst.getMiscParamsLVL(
					"SWEEP_SETTLE_METHOD", sweepAcct2.getEntityId());
			// get the list of the target sign.
			targetSignList = (ArrayList) targetSignCol;
			
			if(request.getParameter("qname").trim().equals(
					SwtConstants.SWEEP_STATUS_CANCEL) && !SwtUtil.isEmptyOrNull(sweepAcct2.getDefaultSettleMethod())) {
				
				boolean found = false;
				for (int i = 0; i < targetSignList.size(); i++) {
					LabelValueBean bean = (LabelValueBean) targetSignList.get(i);
					if(bean.getValue().equals(sweepAcct2.getDefaultSettleMethod())) {
						found = true;
						break;
					}
				}
				if(!found) {
					targetSignList.add(new LabelValueBean(sweepAcct2.getDefaultSettleMethod(), sweepAcct2.getDefaultSettleMethod()));
				}
			}
			if (sweepDetailVO.getAlignIndex() == 1) {
				request.setAttribute("defaultSettleMethodList2", targetSignList);
			}else {
				request.setAttribute("defaultSettleMethodList1", targetSignList);
			}
			
			
			
			log.debug("exiting 'displayQueue' method");

			return ("success");
		} catch (SwtException swtexp) {
			swtexp.printStackTrace();
			log
					.error("SwtException Catch in SweepDetailAction.'displayQueue' method : "
							+ swtexp.getMessage());
			saveErrors(request, SwtUtil.logException(swtexp, request, ""));

			
			CacheManager cacheManagerInst = CacheManager.getInstance();
			// get the target sign collection according to the entity id
			Collection targetSignCol = cacheManagerInst.getMiscParamsLVL(
					"SWEEP_SETTLE_METHOD", "");
			// get the list of the target sign.
			ArrayList targetSignList = (ArrayList) targetSignCol;
			request.setAttribute("defaultSettleMethodList1", targetSignList);
			
			
			
			// get the target sign collection according to the entity id
			targetSignCol = cacheManagerInst.getMiscParamsLVL(
					"SWEEP_SETTLE_METHOD", "");
			// get the list of the target sign.
			targetSignList = (ArrayList) targetSignCol;
			request.setAttribute("defaultSettleMethodList2", targetSignList);
			
			return ("success");
		} catch (Exception exp) {
			exp.printStackTrace();
			log
					.error("Exception Catch in SweepDetailAction.'displayQueue' method : "
							+ exp.getMessage());

			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "displayQueue", SweepDetailAction.class), request, "");
			
			CacheManager cacheManagerInst = CacheManager.getInstance();
			// get the target sign collection according to the entity id
			Collection targetSignCol = cacheManagerInst.getMiscParamsLVL(
					"SWEEP_SETTLE_METHOD", sweepAcct1.getEntityId());
			// get the list of the target sign.
			ArrayList targetSignList = (ArrayList) targetSignCol;
			request.setAttribute("defaultSettleMethodList1", targetSignList);
			
			
			
			// get the target sign collection according to the entity id
			targetSignCol = cacheManagerInst.getMiscParamsLVL(
					"SWEEP_SETTLE_METHOD", sweepAcct2.getEntityId());
			// get the list of the target sign.
			targetSignList = (ArrayList) targetSignCol;
			request.setAttribute("defaultSettleMethodList2", targetSignList);

			return ("fail");
		} finally {
			// nullify objects
			sweepDetailVO = null;
			sweepAcct1 = null;
			sweepAcct2 = null;
			sweepDetailsItr = null;
			sweepAmount = null;
		}

	}

	/**
	 * Following function is added for the new functionality to display the msgs
	 * 
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return
	 * @throws SwtException
	 */
	public String sweepMessageDisplay() throws SwtException {
HttpServletRequest request = ServletActionContext.getRequest();
HttpServletResponse response = ServletActionContext.getResponse();

		try {
			log.debug("inside sweepMessageDisplay begins");

			String entityId = SwtUtil
					.getUserCurrentEntity(request.getSession());
			Collection msgListColl = CacheManager.getInstance().getMiscParams(
					"MESSAGESTATUS", entityId);

			SystemFormats sysformat = SwtUtil.getCurrentSystemFormats(request
					.getSession());
			SimpleDateFormat sdf = new SimpleDateFormat("HH:mm:ss");
			String sweepId = request.getParameter("sweepId");

			Collection sweepMessageColl = sweepDetailManager
					.getsweepMessageMgrColl(sweepId);

			Iterator itr = sweepMessageColl.iterator();

			while (itr.hasNext()) {
				SweepMsgFormat sweepMsgFormat = (SweepMsgFormat) itr.next();

				// changes done to add time also --starts
				sweepMsgFormat.setUpdateDateAsString(SwtUtil.formatDate(
						sweepMsgFormat.getUpdateDate(), sysformat
								.getDateFormatValue()));

				if (sweepMsgFormat.getUpdateDate() != null) {
					sweepMsgFormat.setUpdateTime(sdf.format(sweepMsgFormat
							.getUpdateDate()));
				} else {
					sweepMsgFormat.setUpdateTime("");
				}

				Iterator itrMiscParams = msgListColl.iterator();

				while (itrMiscParams.hasNext()) {
					MiscParams mp = (MiscParams) (itrMiscParams.next());

					if (sweepMsgFormat.getStatus().trim().equalsIgnoreCase(
							mp.getId().getKey2().trim())) {

						log
								.debug("setting the status as==>"
										+ mp.getParValue());
						sweepMsgFormat.setStatus(mp.getParValue());

					}

				}
			}

			log.debug("inside sweepMessageDisplay ends");
			request.setAttribute("sweepMessageList", sweepMessageColl);

			return ("sweepMessageDisplay");
		} catch (SwtException swtexp) {
			log
					.error("SwtException Catch in SweepDetailAction.'sweepMessageDisplay' method : "
							+ swtexp.getMessage());
			SwtUtil.logException(swtexp, request, "");

			return ("fail");
		} catch (Exception exp) {
			log
					.error("Exception Catch in SweepDetailAction.'sweepMessageDisplay' method : "
							+ exp.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "sweepMessageDisplay", SweepDetailAction.class),
					request, "");

			return ("fail");
		}
	}

	/**
	 * 
	 * @param request
	 * @param entityId
	 * @param currencyCode
	 */
	private void putSaveButtonStatus(HttpServletRequest request,
			String entityId, String currencyCode) throws SwtException {
		int entityAccess = getEntityAccessType(request, entityId);
		log.debug("entityAccess is " + entityAccess);

		int ccyGrpAccess = getCcyGrpAccessType(request, entityId, currencyCode);
		log.debug("ccyGrpAccess is " + ccyGrpAccess);

		if ((entityAccess == SwtConstants.ENTITY_FULL_ACCESS)
				&& (ccyGrpAccess == SwtConstants.CURRENCYGRP_FULL_ACCESS)) {
			request.setAttribute("SaveButton", "true");
		}
	}

	/**
	 * This method is used to get entity access status
	 * 
	 * @param request
	 * @param entityId
	 * @return
	 */
	private int getEntityAccessType(HttpServletRequest request, String entityId) {
		Collection collEntity = SwtUtil.getUserEntityAccessList(request
				.getSession());
		int entityAccess = SwtUtil.getUserEntityAccess(collEntity, entityId);

		return entityAccess;
	}

	/**
	 * 
	 * @param request
	 * @param entityId
	 * @param currencyId
	 * @return
	 * @throws SwtException
	 */
	private int getCcyGrpAccessType(HttpServletRequest request,
			String entityId, String currencyId) throws SwtException {
		int ccyGrpAccess;
		String hostId = putHostIdListInReq(request);
		String userId = SwtUtil.getCurrentUserId(request.getSession());
		String roleId = getRoleId(request);
		log.debug("host Id is ==>" + hostId + "userId is==>" + userId
				+ "roleId is ==>" + roleId);
		ccyGrpAccess = SwtUtil.getSwtMaintenanceCache().getCurrencyAccess(
				roleId, entityId, currencyId);

		return ccyGrpAccess;
	}

	/**
	 * This method is used to put hosid into request object.
	 * 
	 * @param request
	 * @return string hostId
	 * @throws SwtException
	 */
	private String putHostIdListInReq(HttpServletRequest request)
			throws SwtException {
		log.debug("Inside PreAdviceInputAction.putHostIdListInReq method");

		return CacheManager.getInstance().getHostId();
	}

	/**
	 * This method is used to update the sweep details.
	 * 
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return success or failure
	 * @throws SwtException
	 */
	public String updateSweepDetails() throws SwtException {
HttpServletRequest request = ServletActionContext.getRequest();
HttpServletResponse response = ServletActionContext.getResponse();

		log.debug(this.getClass().getName()
				+ "- [updateSweepDetails] - Entering ");


		SweepAmount sweepAmt = null;
		SweepDetail sweepAct1 = null;
		SweepDetail sweepAct2 = null;
		Sweep sweep = null;
		Double newAmount = null;
		Double authAmount = null;
		String currentUser = null;
		String currentSweepId = null;
		String entityId = null;

		sweepAmt = (SweepAmount) getSweepAmount();
		sweepAct1 = (SweepDetail) getSweepAccount1();
		sweepAct2 = (SweepDetail) getSweepAccount2();
		newAmount = sweepAmt.getAuthSweepAmt();

		currentSweepId = request.getParameter("currentSweepId");

		entityId = sweepAct1.getEntityId();

		try {

			sweep = sweepDetailManager.getSweepDetails(entityId, Long
					.parseLong(currentSweepId));

			authAmount = sweep.getAuthorizeSweepAmt();

			if (!(newAmount.equals(authAmount))) {

				currentUser = SwtUtil.getCurrentUserId(request.getSession());

				sweepDetailManager.updateSweepDetails(entityId, Long
						.parseLong(currentSweepId), sweepAmt , sweepAct1 , sweepAct2, currentUser);
			}
			
			
			request.setAttribute("bookcodeFirst", sweepAct1.getBookcode());
			request.setAttribute("bookCodeSecond", sweepAct2.getBookcode());
			
			
			CacheManager cacheManagerInst = CacheManager.getInstance();
			// get the target sign collection according to the entity id
			Collection targetSignCol = cacheManagerInst.getMiscParamsLVL(
					"SWEEP_SETTLE_METHOD", sweepAct1.getEntityId());
			// get the list of the target sign.
			ArrayList targetSignList = (ArrayList) targetSignCol;
			request.setAttribute("defaultSettleMethodList1", targetSignList);
			
			
			
//			// get the target sign collection according to the entity id
			targetSignCol = cacheManagerInst.getMiscParamsLVL(
					"SWEEP_SETTLE_METHOD", sweepAct2.getEntityId());
			// get the list of the target sign.
			targetSignList = (ArrayList) targetSignCol;
			request.setAttribute("defaultSettleMethodList2", targetSignList);
			
		} catch (SwtException e) {

			log.error(this.getClass().getName()
					+ "- [updateSweepDetails] - Exception " + e.getMessage());

			saveErrors(request, SwtUtil.logException(e, request, ""));
			return ("fail");
		}

		request.setAttribute("parentFormRefresh", "no");
		log.debug(this.getClass().getName()
				+ "- [updateSweepDetails] - Exiting ");
		return ("success");
	}
}
