/*
 * @(#)MetagroupMonitorAction.java 1.0 04/09/06
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.work.web;

import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collection;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.Properties;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;






import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.maintenance.model.MiscParams;
import org.swallow.util.CacheManager;
import org.swallow.util.CommonDataManager;
import org.swallow.util.LabelValueBean;
import org.swallow.util.PropertiesFileLoader;
import org.swallow.util.SwtConstants;
import org.swallow.util.SwtUtil;
import org.swallow.util.SystemFormats;
import org.swallow.util.UserThreadLocalHolder;
import org.swallow.work.model.AccountMonitorNew;
import org.swallow.work.model.MetagroupCodePredictedBalanceTO;
import org.swallow.work.model.MetagroupMonitorCurrencyBalanceTO;
import org.swallow.work.model.ScreenOption;
import org.swallow.work.service.MetagroupMonitorManager;
import org.swallow.work.service.MovementManager;
import org.swallow.work.service.ScreenOptionManager;
import org.swallow.work.web.form.MetagroupMonitorForm;
import org.springframework.beans.factory.annotation.Autowired;
import org.apache.struts2.convention.annotation.Result;
import org.apache.struts2.convention.annotation.Action;
import org.apache.struts2.convention.annotation.AllowedMethods;
import org.apache.struts2.ServletActionContext;
import org.swallow.util.struts.CustomActionSupport;

/**
 * <pre>
 * Action layer for Group, Meta Group, Book Group, Monitor Screen
 * Used to
 * - Display Group Monitor Screen
 * - Display Meta Group Monitor Screen
 * - Display Book Group Monitor Screen
 * - Get Monitor Tab list
 * </pre>
 */
@AllowedMethods ({"flex" ,"displayMetagroupMonitorDetails" ,"saveColumnOrder" ,"saveColumnWidth" ,"displayGroupMonitorDetails" ,"displayBookMonitorDetails" ,"countMovements" ,"callfromcurrency" })

@Action(value = "/metagroupmonitor", results = {
	@Result(name = "success", location = "/jsp/work/groupmonitorflexdata.jsp"),
	@Result(name = "flex", location = "/jsp/work/groupmonitorflex.jsp"),
	@Result(name = "statechange", location = "/jsp/flexstatechange.jsp"),
	@Result(name = "dataerror", location = "/jsp/work/groupmonitorflexdataerror.jsp"),
	@Result(name = "fail", location = "/error.jsp"),
})

public class MetagroupMonitorAction extends CustomActionSupport {
public String execute() throws Exception {
    HttpServletRequest request = ServletActionContext.getRequest();

    // List of methods 
    String method = String.valueOf(request.getParameter("method"));

    switch (method) {
        case "unspecified":
            return unspecified();
        case "flex":
            return flex();
        case "displayMetagroupMonitorDetails":
            return displayMetagroupMonitorDetails();
        case "saveColumnOrder":
            return saveColumnOrder();
        case "saveColumnWidth":
            return saveColumnWidth();
        case "displayGroupMonitorDetails":
            return displayGroupMonitorDetails();
        case "displayBookMonitorDetails":
            return displayBookMonitorDetails();
        case "countMovements":
            return countMovements();
        case "callfromcurrency":
            return callfromcurrency();
        default:
            break;
    }

    return null;
}

private MetagroupMonitorForm metagroupMonitor ;
public void setMetagroupMonitor(MetagroupMonitorForm metagroupMonitor) {
	this.metagroupMonitor = metagroupMonitor;
}
public MetagroupMonitorForm getMetagroupMonitor() {
	return this.metagroupMonitor;
}


	/**
	 * Manager class instance
	 */
	@Autowired
private MetagroupMonitorManager metagroupMonitorManager = null;

	/**
	 * @param metagroupMonitorManager
	 *            manager class instance to set
	 */
	public void setMetagroupMonitorManager(
			MetagroupMonitorManager metagroupMonitorManager) {
		this.metagroupMonitorManager = metagroupMonitorManager;
	}

	String itemId = "7";
	String menuItemId = "" + SwtConstants.METAGROUP_MONITORS;
	/**
	 * An instance of Log
	 */
	private final Log log = LogFactory.getLog(MetagroupMonitorAction.class);

	/**
	 * Default method which will be called
	 * 
	 * @param mapping
	 *            Mapping specified in the struts-config.xml
	 * @param form
	 *            Form class which contains the value from the JSP
	 * @param request
	 *            the request parameter
	 * @param response
	 *            the response parameter
	 * @return ActionForward which will specify the JSP to which the control
	 *         will be transferred using the mapping defined in the
	 *         struts-config.xml
	 * 
	 * @throws SwtException
	 *             Throw exception, if any
	 */
	public String unspecified()
			throws SwtException {
		log.debug(this.getClass().getName() + "- [unspecified] - "
				+ "Entering & Calling [displayMetagroupMonitorDetails] method");

		return displayMetagroupMonitorDetails();
	}

	/**
	 * Routes the request to load the Metagroup monitor flex screen
	 * 
	 * @param mapping
	 *            Mapping specified in the struts-config.xml
	 * @param form
	 *            Form class which contains the value from the JSP
	 * @param request
	 *            the request parameter
	 * @param response
	 *            the response parameter
	 * @return ActionForward which will specify the JSP to which the control
	 *         will be transferred using the mapping defined in the
	 *         struts-config.xml
	 * 
	 * @throws SwtException
	 *             Throw exception, if any
	 */
	public String flex()
			throws SwtException {
	HttpServletRequest request = ServletActionContext.getRequest();
HttpServletResponse response = ServletActionContext.getResponse();
	// variable to hold monitor type
		String monitortype = null;
		// variable declaration for meta group monitor form
		MetagroupMonitorForm metagroupMonitorForm = null;
		// variable to hold meta group menu item id
		String metagroupMenuItemId = null;
		// variable to hold group menu item id
		String groupMenuItemId = null;
		// variable to hold book menu item id
		String bookMenuItemId = null;
		// variable to hold meta group Screen Id
		String metagroupScreenId = null;
		// variable to hold group Screen Id
		String groupScreenId = null;
		// variable to hold book Screen Id
		String bookScreenId = null;
		// variable to hold host Id
		String hostId = null;
		// variable to hold user Id
		String userId = null;
		try {
			log.debug(this.getClass().getName() + "- [flex] - Entering ");
			// get the monitor type from request
			monitortype = request.getParameter("monitortype");
			// Stores the menuitemid and screen id for Metagroup,Group and book
			// monitors
			metagroupMenuItemId = SwtConstants.METAGROUP_MONITORS;
			groupMenuItemId = SwtConstants.GROUP_MONITORS;
			bookMenuItemId = SwtConstants.BOOK_MONITORS;
			metagroupScreenId = SwtConstants.METAGROUP_MONITOR_ID;
			groupScreenId = SwtConstants.GROUP_MONITOR_ID;
			bookScreenId = SwtConstants.BOOK_MONITOR_ID;
			// Fetches the host id from session
			hostId = SwtUtil.getCurrentHostId();
			// Fetches the User id from session
			userId = SwtUtil.getCurrentUserId(request.getSession());
			// get meta group monitor form
			metagroupMonitorForm = getForm(request);
			// set the meta group menu item id in request
			request.setAttribute("metagroupMenuItemId", metagroupMenuItemId);
			// set the group menu item id in request
			request.setAttribute("groupMenuItemId", groupMenuItemId);
			// set the book menu item id in request
			request.setAttribute("bookMenuItemId", bookMenuItemId);
			// set the meta group screen id in request
			request.setAttribute("metagroupScreenId", metagroupScreenId);
			// set the group screen id in request
			request.setAttribute("groupScreenId", groupScreenId);
			// set the book screen id in request
			request.setAttribute("bookScreenId", bookScreenId);
			// set the item id in request
			request.setAttribute("itemId", itemId);
			// set the monitor type in request
			request.setAttribute("monitorType", monitortype);
			// Sets the item id in Form object
			metagroupMonitorForm.setItemId(itemId);
			// set user id in request
			request.setAttribute("userId", userId);
			// set host id in request
			request.setAttribute("hostId", hostId);
			// Start: Code added by Vivekanandan A for mantis 1991 on 18-07-2012
			// Set user default entity id as existing entityId
			request.setAttribute("existingEntityId", SwtUtil
					.getUserCurrentEntity(request.getSession()));
			// End: Code added by Vivekanandan A for mantis 1991 on 18-07-2012
			// Calling the helper method for getting the font size from the
			// request.
			setFontSize(request, itemId);
			log.debug(this.getClass().getName() + "- [flex] - Exiting ");
		} catch (Exception exception) {
			exception.printStackTrace();
			log.error("Exception caught in" + this.getClass().getName()
					+ "[flex] " + exception.getMessage());
			// Re-throwing SwtException
			throw SwtErrorHandler.getInstance().handleException(exception,
					"flex", MetagroupMonitorAction.class);
		}
		return ("flex");
	}
	/**
	 * This class will send the input parameters to the manager class and the
	 * result to the JSP
	 * 
	 * @param mapping
	 *            Mapping specified in the struts-config.xml
	 * @param form
	 *            Form class which contains the value from the JSP
	 * @param request
	 *            the request parameter
	 * @param response
	 *            the response parameter
	 * @return ActionForward which will specify the JSP to which the control
	 *         will be transferred using the mapping defined in the
	 *         struts-config.xml
	 */
	public String displayMetagroupMonitorDetails() throws SwtException {
	HttpServletRequest request = ServletActionContext.getRequest();
HttpServletResponse response = ServletActionContext.getResponse();
	// Object to get the system format
		SystemFormats format = null;
		// Variable to hold the entityId
		String entityId = null;
		// Variable to hold the roleId
		String roleId = null;
		// Variable to hold the metagroup menu itemId
		String metagroupMenuItemId = null;
		// Variable to hold the screenId
		String screenId = null;
		// MetagroupMonitor form object
		MetagroupMonitorForm metagroupMonitorForm = null;
		// Form Object
		// collection used to get the monitor details
		Collection monitorTypeColl = null;
		// Variable to hold the screen name
		String screenFlag = "metaGroupMonitor";
		// Variable to hold the hostId
		String hostId = "";
		// Variable to hold Tab Flag
		String tabFlag = null;
		try {
			log.debug(this.getClass().getName()
					+ "- [displayMetagroupMonitorDetails] - Entering ");
			// get the system format for details like date format etc
			format = SwtUtil.getCurrentSystemFormats(request.getSession());
			// To get the form value
			metagroupMonitorForm = getForm(request);
			// To get the entityId
			entityId = metagroupMonitorForm.getEntityId();
			// Check if entity id is null. If yes, then set it to the default
			// entity id.
			if ((entityId == null) || (entityId.trim().length() <= 0)) {
				entityId = SwtUtil.getUserCurrentEntity(request.getSession());
			}
			// To get the hostId
			hostId = SwtUtil.getCurrentHostId();
			// To get the roleId
			roleId = ((CommonDataManager) request.getSession().getAttribute(
					SwtConstants.CDM_BEAN)).getUser().getRoleId();
			// To get the Monitor details and retrun the tab flag value
			tabFlag = getMonitorTabList(request, metagroupMonitorForm, roleId,
					entityId, screenFlag);
			// To get the holiday list details and pass the tab flag value
			getHolidayList(request, metagroupMonitorForm, format, entityId,
					hostId, tabFlag);
			this.metagroupMonitor = metagroupMonitorForm;
			request.setAttribute("metagroupMonitor", metagroupMonitorForm);
			// set the monitor type to request
			request.setAttribute("monitorType", "Metagroup");
			// Stores the metagroup menu item id from Constants
			metagroupMenuItemId = SwtConstants.METAGROUP_MONITORS;
			// Sets the Column order from the session in request
			bindColumnOrderInRequest(request, entityId, metagroupMenuItemId);
			// Sets the Column width from the session in request
			bindColumnWidthInRequest(request, entityId, metagroupMenuItemId);
			// get the monitor type collection
			monitorTypeColl = getMonitorType(SwtConstants.MONITORS_TYPES,
					entityId);

			// set the collection to metagroupMonitorForm
			metagroupMonitorForm.setMonitors(monitorTypeColl);
			// set the monitors to request
			request
					.setAttribute("monitors", metagroupMonitorForm
							.getMonitors());
			// set the itemId to form
			metagroupMonitorForm.setItemId(itemId);
			// set the itemId to request
			request.setAttribute("itemId", itemId);
			// get the screenId
			screenId = SwtConstants.METAGROUP_MONITOR_ID;
			request.setAttribute("metagroupMonitor", metagroupMonitorForm);
			this.metagroupMonitor = metagroupMonitorForm;
			// set the Refresh Rate
			setRefreshRate(request, screenId);
			// Calling this method for getting the font size from the request.
			setFontSize(request, screenId);
			return ("success");
		} catch (SwtException swtexp) {
			String message2 = swtexp.getStackTrace()[0].getClassName() + "."
					+ swtexp.getStackTrace()[0].getMethodName() + ":"
					+ swtexp.getStackTrace()[0].getLineNumber() + " "
					+ swtexp.getMessage();
			log.error(this.getClass().getName()
					+ " - [displayMetagroupMonitorDetails] - SwtException "
					+ message2);

			SwtUtil.logException(swtexp, request, "");
			request.setAttribute("reply_status_ok", "false");
			request.setAttribute("reply_message", swtexp.getMessage());
			request.setAttribute("reply_location", swtexp.getStackTrace()[0]
					.getClassName()
					+ "."
					+ swtexp.getStackTrace()[0].getMethodName()
					+ ":"
					+ swtexp.getStackTrace()[0].getLineNumber());
			return ("dataerror");
		} catch (Exception exp) {
			String message1 = exp.getStackTrace()[0].getClassName() + "."
					+ exp.getStackTrace()[0].getMethodName() + ":"
					+ exp.getStackTrace()[0].getLineNumber() + " "
					+ exp.getMessage();
			log.error(this.getClass().getName()
					+ " - [displayMetagroupMonitorDetails] - Exception "
					+ message1);
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "displayMetagroupMonitorDetails",
					MetagroupMonitorAction.class), request, "");
			request.setAttribute("reply_status_ok", "false");
			request.setAttribute("reply_message", exp.getMessage());
			request.setAttribute("reply_location", exp.getStackTrace()[0]
					.getClassName()
					+ "."
					+ exp.getStackTrace()[0].getMethodName()
					+ ":"
					+ exp.getStackTrace()[0].getLineNumber());
			return ("dataerror");
		} finally {
			// Bind the information like the selected tab etc to request
			bindTabInfoInRequest(request);
			log.debug(this.getClass().getName()
					+ "- [displayMetagroupMonitorDetails] - Exiting ");
		}
	}


	/**
	 * This method will get the currency list from the database
	 * 
	 * @param request
	 *            the request parameter
	 * @param roleId
	 *            the role ID
	 * @param entityId
	 *            the entity ID
	 * @throws SwtException
	 *             throw exception if any
	 * @return Collection collection of currencies
	 */
	private Collection getCurrencyList(HttpServletRequest request,	String roleId, String entityId) throws SwtException {
	log
				.debug(this.getClass().getName()
						+ "- [getCurrencyList] - Entering ");
		ArrayList currencyList = null;
		// Fetches the currency list as a collection of Label value bean for the
		// given entity
		currencyList = (ArrayList) SwtUtil.getSwtMaintenanceCache()
				.getCurrencyViewORFullAcessLVL(roleId, entityId);
		// Adding the default values in the currency list
		currencyList.remove(new LabelValueBean("Default", "*"));
		currencyList.remove(new LabelValueBean("Leck", "ALL"));
		log.debug(this.getClass().getName() + "- [getCurrencyList] - Exiting ");

		return currencyList;
	}

	/**
	 * This method will get the entity list from the database
	 * 
	 * @param request
	 *            the request parameter
	 * @throws SwtException
	 *             throw exception if any
	 * @return Collection of entities
	 */
	private Collection getEntityList(HttpServletRequest request)
			throws SwtException {
		log.debug(this.getClass().getName() + "- [getEntityList] - Entering ");
		HttpSession session = null;
		Collection entityList = null;
		// Gets a HttpSession instance
		session = request.getSession();
		// Stores the entity details for which the logged in user has access
		// rights
		entityList = SwtUtil.getUserEntityAccessList(session);
		// Stores entity list as label value bean
		entityList = SwtUtil.convertEntityAcessCollectionLVL(entityList,
				session);

		log.debug(this.getClass().getName() + "- [getEntityList] - Exiting ");

		return entityList;
	}

	/**
	 * This method returns the collection of locations associated with the
	 * Entity id passed.
	 * 
	 * @param request
	 * @param entityId
	 * @return Collection object
	 * @throws SwtException
	 */
	private Collection getLocationList(HttpServletRequest request,
			String entityId) throws SwtException {
		log
				.debug(this.getClass().getName()
						+ "- [getLocationList] - Entering ");
		Collection locationColl = null;
		HttpSession session = null;
		Collection coll = null;

		locationColl = new ArrayList();
		// Gets a HttpSession instance
		session = UserThreadLocalHolder.getUserSession();
		// Stores the location details for the entity id passed
		coll = SwtUtil.getUserLocationAccessListLVB(session, entityId);
		// Adding ('All', 'All Locations') to the drop down
		locationColl.add(new LabelValueBean(SwtConstants.ALL_LABEL,
				SwtConstants.ALL_VALUE));

		if ((coll != null) && (coll.size() > 0)) {
			Iterator itr = coll.iterator();
			// Iterates the collection and set the location details in Label
			// value bean
			while (itr.hasNext()) {
				LabelValueBean lvb = (LabelValueBean) (itr.next());
				locationColl.add(lvb);
			}
		}

		request.setAttribute("locationColl", locationColl);

		log.debug(this.getClass().getName() + "- [getLocationList] - Exiting ");

		return locationColl;
	}

	/**
	 * This method binds the tab information to the request
	 * 
	 * @param metagroupMonitorForm
	 *            the form class instance
	 * 
	 * @param request
	 *            the request parameter
	 * 
	 */
	private void bindTabInfoInRequest(HttpServletRequest request) {
		log.debug(this.getClass().getName()
				+ "- [bindTabInfoInRequest] - Entering ");
		String selectedTabIndex = null;
		String selectedTabName = null;
		selectedTabIndex = request.getParameter("selectedTabIndex");
		selectedTabName = request.getParameter("selectedTabName");
		/*
		 * Sets the selected tab index and tab info in request If the selected
		 * tab index is Null default tab index is set in request
		 */
		if ((SwtUtil.isEmptyOrNull(selectedTabIndex))) {
			request.setAttribute("selectedTabIndex", "1");
			request.setAttribute("selectedTabName",
					"metagroupMonitorTodayParent");
		} else {
			request.setAttribute("selectedTabIndex", selectedTabIndex);
			request.setAttribute("selectedTabName", selectedTabName);
		}
		log.debug(this.getClass().getName()
				+ "- [bindTabInfoInRequest] - Exiting ");
	}

	/**
	 * This method gets the refresh rate for the metagroup monitor screen from
	 * the database
	 * 
	 * @param request
	 *            the request parameter
	 * @throws SwtException
	 *             if any\
	 */
	private void setScreenRefreshRate(HttpServletRequest request)
			throws SwtException {
		log.debug(this.getClass().getName()
				+ "- [setScreenRefreshRate] - Entering ");
		// Fetches the user id from session
		String userId = SwtUtil.getCurrentUserId(request.getSession());
		// Gets the ScreenOptionManager instance
		ScreenOptionManager screenOptionManager = (ScreenOptionManager) (SwtUtil
				.getBean("screenOptionManager"));
		// Initialising the ScreenOption instance
		ScreenOption screenOption = new ScreenOption();
		// Setting the host id
		screenOption.getId().setHostId(SwtUtil.getCurrentHostId());
		// Setting the user id
		screenOption.getId().setUserId(userId);
		// Setting the screen id
		screenOption.getId().setScreenId(SwtConstants.GROUP_MONITOR_ID);
		// Fetching the refresh rate
		screenOption = screenOptionManager.getRefreshRate(screenOption);
		// setting the rate in request
		request.setAttribute("autoRefreshRate", new Integer(screenOption
				.getPropertyValue()));
		log.debug(this.getClass().getName()
				+ "- [setScreenRefreshRate] - Exiting ");
	}

	/**
	 * This method gets the parameter values from dynaform or request and sets
	 * it into the form class
	 * 
	 * @param form
	 *            form class instance
	 * @param request
	 *            the request
	 * @return MetagroupMonitorForm instance
	 */
	private MetagroupMonitorForm getForm(HttpServletRequest request) {
		log.debug(this.getClass().getName() + "- [getForm] - Entering ");
		MetagroupMonitorForm metagroupMonitorForm = null;
		if(this.metagroupMonitor == null)
			this.metagroupMonitor = new MetagroupMonitorForm();
		metagroupMonitorForm = this.metagroupMonitor;
		if (request.getParameter("entityId") != null) {
			metagroupMonitorForm = new MetagroupMonitorForm();
			metagroupMonitorForm.setEntityId(request.getParameter("entityId"));
			metagroupMonitorForm.setCurrencyId(request
					.getParameter("currencyCode"));
			metagroupMonitorForm.setDate(request.getParameter("valueDate"));
			metagroupMonitorForm.setSelectedTabIndex("8");
			metagroupMonitorForm
					.setSelectedTabName("metagroupMonitorSelectedDateParent");
		}
		log.debug(this.getClass().getName() + "- [getForm] - Exiting ");
		return metagroupMonitorForm;
	}

	/**
	 * Added for storing column order in request
	 * 
	 * @param request
	 *            the request
	 * @param entityId
	 *            the entityId
	 * @param menuItemId
	 *            the menuItemId
	 * 
	 */
	private void bindColumnOrderInRequest(HttpServletRequest request,
			String entityId, String menuItemId) {
		log.debug(this.getClass().getName()
				+ "- [bindColumnOrderInRequest] - Entering ");
		String order = null;

		order = SwtUtil.getPropertyValue(request, entityId, menuItemId,
				"display", "column_order");
		if (order.equalsIgnoreCase("")) { // default condition
			order = "group,name,level,total";

		}

		ArrayList<String> orders = new ArrayList<String>();
		String[] props = order.split(",");
		for (int i = 0; i < props.length; i++) {
			orders.add(props[i]);
		}

		request.setAttribute("column_order", orders);
		log.debug(this.getClass().getName()
				+ "- [bindColumnOrderInRequest] - Exiting ");
	}

	/**
	 * For storing column order
	 * 
	 * @param mapping
	 *            Mapping specified in the struts-config.xml
	 * @param form
	 *            Form class which contains the value from the JSP
	 * @param request
	 *            the request parameter
	 * @param response
	 *            the response parameter
	 * @return ActionForward which will specify the JSP to which the control
	 *         will be transferred using the mapping defined in the
	 *         struts-config.xml
	 * 
	 * @throws SwtException
	 *             Throw exception, if any
	 */
	public String saveColumnOrder() {
		log
				.debug(this.getClass().getName()
						+ "- [saveColumnOrder] - Entering ");
		String order = null;
		String entityid = null;
		String menuItemId = null;
		HttpServletRequest request = ServletActionContext.getRequest();
		try {

			menuItemId = request.getParameter("menuItemId");
			if ((order = request.getParameter("order")) != null
					&& (entityid = request.getParameter("entityid")) != null) {
				SwtUtil.setPropertyValue(request, entityid, menuItemId,
						"display", "column_order", order);
			}
			request.setAttribute("reply_status_ok", "true");
			request.setAttribute("reply_message", "Column order saved ok");
		} catch (Exception e) {
			request.setAttribute("reply_status_ok", "false");
			request.setAttribute("reply_message", e.getMessage());
			request.setAttribute("reply_location", e.getStackTrace()[0]
					.getClassName()
					+ "."
					+ e.getStackTrace()[0].getMethodName()
					+ ":"
					+ e.getStackTrace()[0].getLineNumber());
		}
		log.debug(this.getClass().getName() + "- [saveColumnOrder] - Exiting ");
		return ("statechange");
	}

	/**
	 * Added for storing column width in request
	 * 
	 * @param request
	 *            the request
	 * @param entityId
	 *            the entityId
	 * @param menuItemId
	 *            the menuItemId
	 * 
	 */
	private void bindColumnWidthInRequest(HttpServletRequest request,
			String entityId, String menuItemId) {
		log.debug(this.getClass().getName()
				+ "- [bindColumnWidthInRequest] - Entering ");
		String width = null;
		width = SwtUtil.getPropertyValue(request, entityId, menuItemId,
				"display", "column_width");
		if (width.equalsIgnoreCase("")) { // default condition
			width = "group=166,name=190,level=200,total=225";
		}

		HashMap<String, String> widths = new HashMap<String, String>();
		String[] props = width.split(",");
		for (int i = 0; i < props.length; i++) {
			if (props[i].indexOf("=") != -1) {
				String[] propval = props[i].split("=");
				widths.put(propval[0], propval[1]);
			}
		}

		request.setAttribute("column_width", widths);
		log.debug(this.getClass().getName()
				+ "- [bindColumnWidthInRequest] - Exiting ");
	}

	/**
	 * Used to save the Column width.
	 * 
	 * @param mapping
	 *            Mapping specified in the struts-config.xml
	 * @param form
	 *            Form class which contains the value from the JSP
	 * @param request
	 *            the request parameter
	 * @param response
	 *            the response parameter
	 * @return ActionForward which will specify the JSP to which the control
	 *         will be transferred using the mapping defined in the
	 *         struts-config.xml
	 */
	public String saveColumnWidth() {
		log
				.debug(this.getClass().getName()
						+ "- [saveColumnWidth] - Entering ");
		String width = null;
		String entityid = null;
		String menuItemId = null;
		HttpServletRequest request = ServletActionContext.getRequest();
		try {
			menuItemId = request.getParameter("menuItemId");
			if ((width = request.getParameter("width")) != null
					&& (entityid = request.getParameter("entityid")) != null) {
				SwtUtil.setPropertyValue(request, entityid, menuItemId,
						"display", "column_width", width);
			}
			request.setAttribute("reply_status_ok", "true");
			request.setAttribute("reply_message", "Column width saved ok");

		} catch (Exception e) {
			request.setAttribute("reply_status_ok", "false");
			request.setAttribute("reply_message", e.getMessage());
			request.setAttribute("reply_location", e.getStackTrace()[0]
					.getClassName()
					+ "."
					+ e.getStackTrace()[0].getMethodName()
					+ ":"
					+ e.getStackTrace()[0].getLineNumber());
		}
		log.debug(this.getClass().getName() + "- [saveColumnWidth] - Exiting ");
		return ("statechange");
	}

	/**
	 * This method will send the input paramters to the manager class and the
	 * result to the JSP
	 * 
	 * @param mapping
	 *            Mapping specified in the struts-config.xml
	 * @param form
	 *            Form class which contains the value from the JSP
	 * @param request
	 *            the request parameter
	 * @param response
	 *            the response parameter
	 * @return ActionForward which will specify the JSP to which the control
	 *         will be transferred using the mapping defined in the
	 *         struts-config.xml
	 */
	public String displayGroupMonitorDetails() throws SwtException {

		// Object to get the system format
		SystemFormats format = null;
		// Variable to hold the entityId
		String entityId = null;
		// Variable to hold the entityId
		String roleId = null;
		// Variable to hold the message
		String message = null;
		// Collection to get the monitor details
		Collection monitorTypeColl = null;
		// Variable to hold the message
		String groupMenuItemId = null;
		// Variable to hold the itemId
		String itemId = null;
		// Variable to hold the screenId
		String screenId = null;
		// MetagroupMonitorForm object
		MetagroupMonitorForm groupMonitorForm = null;
		// Variable to hold the hostId
		String hostId = "";
		// Variable to hold the screen flag
		String screenFlag = "groupMonitor";
		// Variable to hold Tab Flag
		String tabFlag = null;
		HttpServletRequest request = ServletActionContext.getRequest();
		try {
			log.debug(this.getClass().getName()
					+ "- [displayGroupMonitorDetails] - Entering ");
			// Get the form values
			groupMonitorForm = getForm( request);
			// get the system format for details like date format etc
			format = SwtUtil.getCurrentSystemFormats(request.getSession());
			// Get the entityId
			entityId = groupMonitorForm.getEntityId();
			// Check if entity id is null. If yes, then set it to the default
			// entity id.
			if ((entityId == null) || (entityId.trim().length() <= 0)) {
				entityId = SwtUtil.getUserCurrentEntity(request.getSession());
			}
			// Get the hostId
			hostId = SwtUtil.getCurrentHostId();
			// Get the roleId
			roleId = ((CommonDataManager) request.getSession().getAttribute(
					SwtConstants.CDM_BEAN)).getUser().getRoleId();
			// Get the monitor tab details and return the tab flag value
			tabFlag = getMonitorTabList(request, groupMonitorForm, roleId,
					entityId, screenFlag);
			// Get the monitor holiday list and pass the tab flag value
			getHolidayList(request, groupMonitorForm, format, entityId, hostId,
					tabFlag);
			// Set the metagroupMonitor to form
			this.metagroupMonitor= groupMonitorForm;
			
			// Sets the monitor type in request
			request.setAttribute("monitorType", "Group");
			// Stores the group monitor menu item id
			groupMenuItemId = SwtConstants.GROUP_MONITORS;
			// Sets the Column order from the session in request
			bindColumnOrderInRequest(request, entityId, groupMenuItemId);
			// Fetches the column Width for this screen for the logged in user
			bindColumnWidthInRequest(request, entityId, groupMenuItemId);
			// Get the monitor collection details
			monitorTypeColl = getMonitorType(SwtConstants.MONITORS_TYPES,
					entityId);

			// Sets the group monitor details in form object
			groupMonitorForm.setMonitors(monitorTypeColl);
			// Set the monitors to request
			request.setAttribute("monitors", groupMonitorForm.getMonitors());
			// Gets the item Id
			itemId = SwtConstants.GROUP_MONITORS;
			// Set the item id for group monitor in Form Object
			groupMonitorForm.setItemId(itemId);
			// Set the itemId to request
			request.setAttribute("itemId", itemId);
			// Get the group monitor id
			screenId = SwtConstants.GROUP_MONITOR_ID;
			request.setAttribute("metagroupMonitor", groupMonitorForm);
			this.metagroupMonitor = groupMonitorForm;
			// Set the refresh rate
			setRefreshRate(request, screenId);
			// Calling this method for getting the font size from the request.
			setFontSize(request, screenId);
		} catch (SwtException swtexp) {
			message = swtexp.getStackTrace()[0].getClassName() + "."
					+ swtexp.getStackTrace()[0].getMethodName() + ":"
					+ swtexp.getStackTrace()[0].getLineNumber() + " "
					+ swtexp.getMessage();
			log
					.error(this.getClass().getName()
							+ "-[displayGroupMonitorDetails] - SwtException "
							+ message);
			SwtUtil.logException(swtexp, request, "");
			request.setAttribute("reply_status_ok", "false");
			request.setAttribute("reply_message", swtexp.getMessage());
			request.setAttribute("reply_location", swtexp.getStackTrace()[0]
					.getClassName()
					+ "."
					+ swtexp.getStackTrace()[0].getMethodName()
					+ ":"
					+ swtexp.getStackTrace()[0].getLineNumber());
			log.debug(this.getClass().getName()
					+ "- [displayGroupMonitorDetails] - Exiting ");
			return ("dataerror");
		} catch (Exception exp) {
			message = exp.getStackTrace()[0].getClassName() + "."
					+ exp.getStackTrace()[0].getMethodName() + ":"
					+ exp.getStackTrace()[0].getLineNumber() + " "
					+ exp.getMessage();
			log.error(this.getClass().getName()
					+ " - [displayGroupMonitorDetails] - Exception " + message);
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "displayGroupMonitorDetails",
					MetagroupMonitorAction.class), request, "");
			request.setAttribute("reply_status_ok", "false");
			request.setAttribute("reply_message", exp.getMessage());
			request.setAttribute("reply_location", exp.getStackTrace()[0]
					.getClassName()
					+ "."
					+ exp.getStackTrace()[0].getMethodName()
					+ ":"
					+ exp.getStackTrace()[0].getLineNumber());
			return ("dataerror");
		} finally {
			// Bind the information like the selected tab etc to request
			bindTabInfoInRequest(request);
		}
		return ("success");
	}

	/**
	 * This method will send the input parameters to the manager class and the
	 * result to the JSP
	 * 
	 * @param mapping
	 *            Mapping specified in the struts-config.xml
	 * @param form
	 *            Form class which contains the value from the JSP
	 * @param request
	 *            the request parameter
	 * @param response
	 *            the response parameter
	 * @return ActionForward which will specify the JSP to which the control
	 *         will be transferred using the mapping defined in the
	 *         struts-config.xml
	 */
	public String displayBookMonitorDetails() throws SwtException {

HttpServletRequest request = ServletActionContext.getRequest();
HttpServletResponse response = ServletActionContext.getResponse();
		// Object to get the system format
		SystemFormats format = null;
		// Variable to hold the entityId
		String entityId = null;
		// Variable to hold the locationId
		String locationId = null;
		// Variable to hold the roleId
		String roleId = null;
		// Variable to hold the MenuItemId
		String bookMenuItemId = null;
		// Variable to hold the screenId
		String screenId = null;
		// Variable to hold the message
		String message = null;
		// Variable to hold the itemId
		String itemId = null;
		// MetagroupMonitorForm object
		MetagroupMonitorForm bookMonitorForm = null;
		// collection to get the monitor details
		Collection monitorTypeColl = null;
		// Variable to hold the hostId
		String hostId = "";
		// Variable to hold the screen flag
		String screenFlag = "bookMonitor";
		// Variable to hold Tab Flag
		String tabFlag = null;
		try {
			log.debug(this.getClass().getName()
					+ "- [displayBookMonitorDetails] - Entering ");
			// get the form details
			bookMonitorForm = getForm(request);
			// set the Total NegativeFlag to form
			bookMonitorForm.setTotalNegativeFlag(false);
			// set the total to form
			bookMonitorForm.setTotal("");
			// get the system format for details like date format etc
			format = SwtUtil.getCurrentSystemFormats(request.getSession());
			// Get the entityId
			entityId = bookMonitorForm.getEntityId();
			// Get the locationId
			locationId = bookMonitorForm.getLocationId();
			// Check if entity id is null. If yes, then set it to the default
			// entity id.
			if ((entityId == null) || (entityId.trim().length() <= 0)) {
				entityId = SwtUtil.getUserCurrentEntity(request.getSession());
			}
			// Get the hostId
			hostId = SwtUtil.getCurrentHostId();
			// Get the roleId
			roleId = ((CommonDataManager) request.getSession().getAttribute(
					SwtConstants.CDM_BEAN)).getUser().getRoleId();
			// Set the default entity id or the entity id present in the form,
			// if not null.
			bookMonitorForm.setEntityId(entityId);
			// Put the list of entities in the form
			bookMonitorForm.setEntities(getEntityList(request));
			// set the entities in request
			request.setAttribute("entities", bookMonitorForm.getEntities());
			// Put the currencies list in the form
			bookMonitorForm.setCurrencies(getCurrencyList(request, roleId,
					entityId));
			// set the currencies in request
			request.setAttribute("currencies", bookMonitorForm.getCurrencies());
			// set the locationId in form
			bookMonitorForm.setLocationId(locationId);
			// Sets the group list for the group drop down
			bookMonitorForm.setGroups(getGroupList(request, entityId));
			// set the groups in request
			request.setAttribute("groups", bookMonitorForm.getGroups());
			// get the monitor tb details and return the tab flag value
			tabFlag = getMonitorTabList(request, bookMonitorForm, roleId,
					entityId, screenFlag);
			// get the holiday details list and pass the tab flag value
			getHolidayList(request, bookMonitorForm, format, entityId, hostId,
					tabFlag);
//			// set the metagroupMonitor to form
//			this.metagroupMonitor = bookMonitorForm;
			// set the monitorType to request
			request.setAttribute("monitorType", "Book");
			// get the Menu Item Id
			bookMenuItemId = SwtConstants.BOOK_MONITORS;
			// Fetches the column order for this screen for the logged in user
			bindColumnOrderInRequest(request, entityId, bookMenuItemId);
			// Fetches the column Width for this screen for the logged in user
			bindColumnWidthInRequest(request, entityId, bookMenuItemId);
			// get the monitor collection details
			monitorTypeColl = getMonitorType(SwtConstants.MONITORS_TYPES,
					entityId);

			// set the collection to form
			bookMonitorForm.setMonitors(monitorTypeColl);
			// set the monitors to request
			request.setAttribute("monitors", bookMonitorForm.getMonitors());
			// Gets the itemId
			itemId = SwtConstants.BOOK_MONITORS;
			// set the itemId to form
			bookMonitorForm.setItemId(itemId);
			// set the itemid to request
			request.setAttribute("itemId", itemId);
			// get the screenId
			screenId = SwtConstants.BOOK_MONITOR_ID;
			// set the refresh rate
			setRefreshRate(request, screenId);
			// Calling this method for getting the font size from the request.
			setFontSize(request, screenId);
			request.setAttribute("metagroupMonitor", bookMonitorForm);
			this.metagroupMonitor = bookMonitorForm;
			log.debug(this.getClass().getName()
					+ "- [displayBookMonitorDetails] - Exiting ");
		} catch (SwtException swtexp) {
			message = swtexp.getStackTrace()[0].getClassName() + "."
					+ swtexp.getStackTrace()[0].getMethodName() + ":"
					+ swtexp.getStackTrace()[0].getLineNumber() + " "
					+ swtexp.getMessage();
			log.error(this.getClass().getName()
					+ "-[displayBookMonitorDetails] - SwtException " + message);
			SwtUtil.logException(swtexp, request, "");
			request.setAttribute("reply_status_ok", "false");
			request.setAttribute("reply_message", swtexp.getMessage());
			request.setAttribute("reply_location", swtexp.getStackTrace()[0]
					.getClassName()
					+ "."
					+ swtexp.getStackTrace()[0].getMethodName()
					+ ":"
					+ swtexp.getStackTrace()[0].getLineNumber());
			return ("dataerror");
		} catch (Exception exp) {
			message = exp.getStackTrace()[0].getClassName() + "."
					+ exp.getStackTrace()[0].getMethodName() + ":"
					+ exp.getStackTrace()[0].getLineNumber() + " "
					+ exp.getMessage();
			log.error(this.getClass().getName()
					+ " - [displayBookMonitorDetails] - Exception " + message);
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "displayBookMonitorDetails",
					MetagroupMonitorAction.class), request, "");
			request.setAttribute("reply_status_ok", "false");
			request.setAttribute("reply_message", exp.getMessage());
			request.setAttribute("reply_location", exp.getStackTrace()[0]
					.getClassName()
					+ "."
					+ exp.getStackTrace()[0].getMethodName()
					+ ":"
					+ exp.getStackTrace()[0].getLineNumber());
			return ("dataerror");
		} finally {
			// Bind the information like the selected tab etc to request
			bindTabInfoInRequest(request);
		}
		return ("success");
	}

	/**
	 * This method sets the group id and name in a label Value bean to be
	 * displayed in a Drop down
	 * 
	 * @param request
	 * @param entityId
	 * @return Collection object
	 * @throws SwtException
	 */
	private Collection getGroupList(HttpServletRequest request, String entityId)
			throws SwtException {


		log.debug(this.getClass().getName() + "- [getGroupList] - Entering ");
		Collection groupColl;
		Collection coll;

		groupColl = new ArrayList();

		// Adding ('All', 'All Groups') to the drop down
		groupColl.add(new LabelValueBean(SwtConstants.ALL_VALUE,
				SwtConstants.ALL_VALUE));

		groupColl.add(new LabelValueBean(SwtConstants.OTHERS_VALUE,
				SwtConstants.OTHERS_VALUE));

		coll = SwtUtil.getGroupDetails(entityId);

		if ((coll != null) && (coll.size() > 0)) {
			Iterator itr = coll.iterator();

			while (itr.hasNext()) {
				LabelValueBean lvb = (LabelValueBean) (itr.next());
				groupColl.add(lvb);
			}
		}

		request.setAttribute("groupColl", groupColl);

		log.debug(this.getClass().getName() + "- [getGroupList] - Exiting ");

		return groupColl;
	}

	/**
	 * This method fetches the refresh rate of the screen id and for the user
	 * passed
	 * 
	 * @param HttpServletRequest
	 *            request
	 * @param String
	 *            screenId
	 * @return - Collection object
	 * @throws SwtException
	 */
	private void setRefreshRate(HttpServletRequest request, String screenId)
			throws SwtException {

		log.debug(this.getClass().getName() + "- [setRefreshRate] - Entering ");
		// Getting the user id from session
		String userId = SwtUtil.getCurrentUserId(request.getSession());
		// Creating a ScreenOptionManager instance
		ScreenOptionManager screenOptionManager = (ScreenOptionManager) (SwtUtil
				.getBean("screenOptionManager"));
		// Initialising the ScreenOption instance
		ScreenOption screenOption = new ScreenOption();
		// Setting the host id
		screenOption.getId().setHostId(SwtUtil.getCurrentHostId());
		// Setting the user id
		screenOption.getId().setUserId(userId);
		// Setting the screen id
		screenOption.getId().setScreenId(screenId);
		// Fetching the refresh rate
		screenOption = screenOptionManager.getRefreshRate(screenOption);
		// Setting the rate in request
		request.setAttribute("autoRefreshRate", new Integer(screenOption
				.getPropertyValue()));
		log.debug(this.getClass().getName() + "- [setRefreshRate] - Exiting ");
	}

	/**
	 * This method sets the metagroup id and name in a label Value bean to be
	 * displayed in a Drop down
	 * 
	 * @param HttpServletRequest
	 *            request
	 * @param String
	 *            entityId
	 * @return - Collection object
	 * @throws SwtException
	 */
	private Collection getMetagroupList(HttpServletRequest request,
			String entityId) throws SwtException {

		log
				.debug(this.getClass().getName()
						+ " - [ getMetagroupList ] - Entry");

		Collection metagroupColl;
		Collection coll;

		metagroupColl = new ArrayList();
		// Adding ('All', 'All Metagroups') to the drop down
		metagroupColl.add(new LabelValueBean(SwtConstants.ALL_VALUE,
				SwtConstants.ALL_VALUE));

		metagroupColl.add(new LabelValueBean(SwtConstants.OTHERS_VALUE,
				SwtConstants.OTHERS_VALUE));

		coll = SwtUtil.getMetagroupDetails(entityId);

		if ((coll != null) && (coll.size() > 0)) {
			Iterator itr = coll.iterator();

			while (itr.hasNext()) {
				LabelValueBean lvb = (LabelValueBean) (itr.next());
				metagroupColl.add(lvb);
			}
		}

		request.setAttribute("metagroupColl", metagroupColl);
		log.debug(this.getClass().getName() + " - [ getMetagroupList ] - Exit");
		return metagroupColl;
	}

	/**
	 * This class will count the movements of attached bookCode and send the
	 * result to the JSP
	 * 
	 * @param ActionMapping
	 *            mapping
	 * @param ActionForm
	 *            form
	 * @param HttpServletRequest
	 *            request
	 * @param HttpServletResponse
	 *            response
	 * @return - ActionForward object
	 * @throws SwtException
	 */
	public String countMovements()
			throws SwtException {

		log.debug(this.getClass().getName() + " - [ countMovements ] - Entry");

		String noOfMovements = "0";
		String hostId;
		String entityId;
		String currencyCode;
		String valueDateAsString;
		String bookCode;
		InputStream io;
		Properties pr;
		Date valueDate;
		int pageSize;
		ArrayList movementCollection;
		String currentFilter = "all";
		String currentSort = "2|false|";
		String filterSortStatus = currentFilter + "," + currentSort;
		MovementManager movementManager;
		// Totals returned from the SQL call
		HashMap<String, Object> totalMap = null; 
		HttpServletRequest request = ServletActionContext.getRequest();
		HttpServletResponse response = ServletActionContext.getResponse();
		hostId = SwtUtil.getCurrentHostId();
		entityId = request.getParameter("entityId");
		currencyCode = request.getParameter("currencyCode");
		valueDateAsString = request.getParameter("valueDate");
		bookCode = request.getParameter("bookCode");

		// Fetches the Movement summary page size from swtCommon.properties
		pageSize = SwtUtil.getPageSizeFromProperty(SwtConstants.MOVEMENT_SUMMARY_SCREEN_PAGE_SIZE);
		movementCollection = new ArrayList();

		if ((entityId != null) && (currencyCode != null)
				&& (valueDateAsString != null) && (bookCode != null)) {
			valueDate = SwtUtil.parseDate(valueDateAsString, SwtUtil
					.getCurrentSystemFormats(request.getSession())
					.getDateFormatValue());

			movementManager = (MovementManager) SwtUtil
					.getBean("movementManager");
			// Fetches the number of movements for the selected Book code from
			// database
			// Start:Code Modified By ASBalaji for mantis 2032 on
						// 15-08-2012
			// Modified to send openMovementFlag as a separate parameter instead
			// of concatenating with isAllstr.
			totalMap = movementManager.getMonitorMovements(hostId,
					entityId, currencyCode, null, valueDate, bookCode, "",
					Integer.valueOf(0), "", "", pageSize, 1, "N", "", 
					movementCollection, filterSortStatus,
					SwtConstants.BOOK_MONITOR, "N", "N" , SwtUtil.getCurrentUserId(request.getSession()), "<None>");
			// End:Code Modified By ASBalaji for mantis 2032 on
							// 15-08-2012
			if(totalMap != null && totalMap.get("totalCount") != null )
				noOfMovements = ""+Integer.parseInt(String.valueOf(totalMap.get("totalCount")));
			
		}

		log.debug(this.getClass().getName() + " - [ countMovements ]-"
				+ " noOfMovements -> " + noOfMovements);

		try {
			response.getWriter().print(noOfMovements);
		} catch (IOException ioException) {
			log.error(this.getClass().getName() + " IOException --> "
					+ ioException.getMessage());

			throw new SwtException(ioException.getMessage());
		}

		log.debug(this.getClass().getName() + " - [ countMovements ] - Exit");

		return null;
	}

	/**
	 * This method sets the Monitor type in a label Value bean to be displayed
	 * in a Drop down
	 * 
	 * @param monitorType
	 * @param entityId
	 * @return Collection
	 * @throws SwtException
	 */
	private Collection getMonitorType(String monitorType, String entityId)
			throws SwtException {
	HttpServletRequest request = ServletActionContext.getRequest();
HttpServletResponse response = ServletActionContext.getResponse();
	log.debug(this.getClass().getName() + " - [getMonitorType] - Entry");
		Collection monitorTypesCollection = new ArrayList();
		MiscParams miscParamsObj = null;
		Iterator monitorTypeIterator = null;
		// Fetches the Monitor types from Cache
		Collection monitorTypeCollection = CacheManager.getInstance()
				.getMiscParams("MONITORTYPE", entityId);
		// Iterator instance created for the collection retrived from cache
		monitorTypeIterator = monitorTypeCollection.iterator();
		/*
		 * Iterates the Monitor types collection for setting the Monitor type in
		 * Label value bean
		 */
		while (monitorTypeIterator.hasNext()) {
			miscParamsObj = (MiscParams) monitorTypeIterator.next();
			LabelValueBean monitorTypeLVB = new LabelValueBean(miscParamsObj
					.getId().getKey2(), miscParamsObj.getId().getKey2());
			monitorTypesCollection.add(monitorTypeLVB);
		}
		log.debug(this.getClass().getName() + " - [getMonitorType] - Exit");
		return monitorTypesCollection;
	}

	/**
	 * This method is used to get and process the request made from currency
	 * monitor breakdown.
	 * 
	 * @param ActionMapping
	 *            mapping
	 * @param ActionForm
	 *            form
	 * @param HttpServletRequest
	 *            request
	 * @param HttpServletResponse
	 *            response
	 * @return
	 * @throws SwtException
	 */
	public String callfromcurrency() throws SwtException {
		log.debug(this.getClass().getName()
				+ "- [callfromcurrency] - Entering ");
		String entityId = null;
		String currencyId = null;
		String todayDate = null;
		HttpServletRequest request = ServletActionContext.getRequest();
		HttpServletResponse response = ServletActionContext.getResponse();
		try {
			MetagroupMonitorForm metagroupMonitorForm = null;
			metagroupMonitorForm = getForm( request);
			/*
			 * Receive the request parameters from request made from currency
			 * monitor
			 */
			entityId = request.getParameter("entityId");
			currencyId = request.getParameter("currencyCode");
			todayDate = (String) request.getParameter("valueDate");
			String str = (String) request.getParameter("callstatus");
			request.setAttribute("callFromParent", "y");
			request.setAttribute("entityId1", entityId);
			request.setAttribute("currencyId1", currencyId);
			request.setAttribute("datefr", todayDate);
			if (str == null) {
				metagroupMonitorForm.setEntityId(entityId);
				metagroupMonitorForm.setCurrencyId(currencyId);
				metagroupMonitorForm.setDate(todayDate);
			}
			log.debug(this.getClass().getName()
					+ "- [callfromcurrency] - Exiting ");
			return flex();
		} catch (SwtException swtexp) {
			String message2 = swtexp.getStackTrace()[0].getClassName() + "."
					+ swtexp.getStackTrace()[0].getMethodName() + ":"
					+ swtexp.getStackTrace()[0].getLineNumber() + " "
					+ swtexp.getMessage();
			log.error(this.getClass().getName()
					+ " - [callfromcurrency] - SwtException " + message2);
			SwtUtil.logException(swtexp, request, "");
			return ("fail");
		} catch (Exception exp) {
			String message1 = exp.getStackTrace()[0].getClassName() + "."
					+ exp.getStackTrace()[0].getMethodName() + ":"
					+ exp.getStackTrace()[0].getLineNumber() + " "
					+ exp.getMessage();
			log.error(this.getClass().getName()
					+ " - [callfromcurrency] - Exception " + message1);
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "displayMetagroupMonitorDetails",
					MetagroupMonitorAction.class), request, "");
			return ("fail");
		}
	}

	/**
	 * This method is used to get the holiday list
	 * 
	 * @param HttpServletRequest
	 *            request
	 * @param SystemFormats
	 *            format
	 * @param String
	 *            entityId
	 * @param String
	 *            hostId
	 * @param String
	 *            tabFlag
	 * @return
	 * @throws SwtException
	 */
	public void getHolidayList(HttpServletRequest request,
			MetagroupMonitorForm metagroupMonitorForm, SystemFormats format,
			String entityId, String hostId, String tabFlag) throws SwtException {
	// Object to get the sysdate
		Date sysDate = null;
		// Variable to hold the currencyCode
		String currencyCode = null;
		// Variable to hold the current DateFormat
		String currentDateFormat = null;
		// List to hold the account monitor details
		ArrayList<AccountMonitorNew> acctMonitorList = null;
		// Calendar instance to hold system date
		Calendar systemCal = null;
		// To hold looping index
		int i;
		// To hold the system date
		Date systemDBDate = null;
		try {
			// Initialize the currencyCode
			currencyCode = SwtConstants.EMPTY_STRING;
			// Start : Method modified by Balaji for Mantis 1991
			// get the system db date form swt util
			systemDBDate = SwtUtil.getSysParamDateWithEntityOffset(entityId);
			// End : Method modified by Balaji for Mantis 1991
			// Get the system date
			sysDate = SwtUtil.getDBSysDatewithTime(systemDBDate);
			// To get the currency Id
			currencyCode = metagroupMonitorForm.getCurrencyId();
			// Get the current date format value
			currentDateFormat = format.getDateFormatValue();
			// Create instance for AccountMonitorNew
			acctMonitorList = new ArrayList<AccountMonitorNew>();
			// get the calender instance
			systemCal = Calendar.getInstance();
			// set the currency code based on condition
			if (currencyCode == null || currencyCode.trim().length() == 0
					|| currencyCode.equals("All")) {
				// set the domestic currency code
				currencyCode = SwtUtil.getDomesticCurrencyForUser(request,
						hostId, entityId);
				if (currencyCode.trim().length() == 0) {
					currencyCode = null;
				}
			}
			// set the system date time
			systemCal.setTime(sysDate);
			for (i = 0; i < 7; i++) {
				// Create instance for AccountMonitorNew
				AccountMonitorNew acctMonitor = new AccountMonitorNew();
				// set the BusinessDay flag
				if (tabFlag.charAt(i) == 'N') {
					acctMonitor.setBusinessDay("true");
				} else {
					acctMonitor.setBusinessDay("false");
				}
				// set the tab date label
				if (currentDateFormat.equals("dd/MM/yyyy")) {
					acctMonitor.setTabDateLabel(SwtUtil.formatDate(systemCal
							.getTime(), "dd/MM"));
					// set the tab date
					acctMonitor.setTabDateAsString(SwtUtil.formatDate(systemCal
							.getTime(), "dd/MM/yyyy"));

				} else {
					acctMonitor.setTabDateLabel(SwtUtil.formatDate(systemCal
							.getTime(), "MM/dd"));
					// set the tab date
					acctMonitor.setTabDateAsString(SwtUtil.formatDate(systemCal
							.getTime(), "MM/dd/yyyy"));
				}
				systemCal.add(Calendar.DATE, 1);
				// add the acctMonitor to list
				acctMonitorList.add(acctMonitor);
			}
			// set the tabDetails to request
			request.setAttribute("tabDetails", acctMonitorList);
		} catch (SwtException exp) {
			log.error("Exception Catch in getHolidayList method : "
					+ exp.getMessage());
			SwtUtil.logException(exp, request, "");
			throw exp;
		} catch (Exception exp) {
			log.error("Exception Catch in getHolidayList method : "
					+ exp.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "getHolidayList", MetagroupMonitorAction.class),
					request, "");
		} finally {
			// nullify objects
			sysDate = null;
			currencyCode = null;
			currentDateFormat = null;
			acctMonitorList = null;
			systemCal = null;
			systemDBDate = null;
		}
	}

	/**
	 * This method is used to get the tabs detail list
	 * 
	 * @param HttpServletRequest
	 *            request
	 * @param MetagroupMonitorForm
	 *            metagroupMonitorForm
	 * @param String
	 *            roleId
	 * @param String
	 *            entityId
	 * @param String
	 *            screenFlag
	 * @param entityId
	 * @param screenFlag
	 * 
	 * @return String tabFlag
	 * @throws SwtException
	 */
	@SuppressWarnings("unchecked")
	public String getMonitorTabList(HttpServletRequest request,
			MetagroupMonitorForm metagroupMonitorForm, String roleId,
			String entityId, String screenFlag) throws SwtException {
		// Object to get the system format
		SystemFormats format = null;
		// Stringvariable to hold currencyId
		String currencyId = null;
		// Object to get the date
		Date dateOnLoad = null;
		// Object to get the date format
		Date testDateFormatted = null;
		// stringVariable to hold the system date
		String systemDate = null;
		// stringvariable to hold the locationId
		String locationId = null;
		// stringVariable to hold the date
		String formDate = null;
		// stringVariable to hold the submit date
		String submitDate = null;
		// stringVariable to hold the selected tab index
		String selectedTabIndex = null;
		// stringVariable to hold the today date
		String todayDate = null;
		// stringVariable to hold the refresh flag
		String refreshFlag = null;
		// List used to get the predict balance detail
		ArrayList<Object> predictedBalanceDetailList = null;
		// variable to hold the grand total
		double grandTotal;
		// booleanVariable to hold the grand Total Negative flag
		boolean grandTotalNegative;
		// Object to get the test Date
		Date testDate = null;
		// stringVariable to hold the today value
		String today = null;
		// stringVariable to hold the groupCode
		String groupCode = null;
		// stringVariable to hold the meta group Id
		String metagroupId = null;
		// stringVariable to hold the date as String
		String todayDateAsString = null;
		// stringVariable to hold the tabFlag as String
		String tabFlag = null;
		// Variable to hold the jobFlag as String
		boolean jobFlag;
		// Variable to hold Iterator object
		Iterator<MetagroupCodePredictedBalanceTO> itrPredictedBalance = null;
		// Variable to hold predicted Balance Total
		Collection<MetagroupCodePredictedBalanceTO> predictedBalanceTotal = null;
		// Variable to hold metagroupMonitorCurrencyBalanceTO object
		MetagroupMonitorCurrencyBalanceTO metagroupMonitorCurrencyBalanceTO = null;
		// Variable to hold balance Total
		Collection<MetagroupMonitorCurrencyBalanceTO> balanceTotal = null;
		// stringVariable to hold call status
		String callStatus = null;
		// Variable to hold Iterator object
		Iterator<MetagroupMonitorCurrencyBalanceTO> itrbalanceTotal = null;
		// stringVariable to hold date Plus One As String
		String datePlusOneAsString = null;
		// StringVariable to hold today Date Plus Two As String
		String todayDatePlusTwoAsString = null;
		// StringVariable to hold today Date Plus Three As String
		String todayDatePlusThreeAsString = null;
		// StringVariable to hold today Date Plus Four As String
		String todayDatePlusFourAsString = null;
		// Variable to hold today Date Plus Five As String
		String todayDatePlusFiveAsString = null;
		// StringVariable to hold today Date Plus Six As String
		String todayDatePlusSixAsString = null;
		// Variable to hold system date
		Calendar systemDateCal = null;
		// To hold the system date
		Date systemDBDate = null;
		/* Start code modified by Vivekanandan for Mantis 1991 on 24-07-2012 */
		// boolean to handle is entity change flag
		boolean isEntityChanged = false;
		/* End code modified by Vivekanandan for Mantis 1991 on 24-07-2012 */
		try {
			log.debug(this.getClass().getName()
					+ " - [getMonitorTabList] -Entry");
			grandTotalNegative = false;
			grandTotal = 0.0;
			jobFlag = false;
			// Set the filterStatus to request
			request.setAttribute("filterStatus", request
					.getParameter("selectedFilterStatus"));
			// Set the sortStatus to request
			request.setAttribute("sortStatus", request
					.getParameter("selectedSortStatus"));
			// Set the sort descending to request
			request.setAttribute("sortDescending", request
					.getParameter("selectedSortDescending"));
			// To get the currency Id
			currencyId = metagroupMonitorForm.getCurrencyId();
			// set the scrollleft to request
			request.setAttribute("scrollLeft", request
					.getParameter("scrollLeft"));
			// set the scrolltop to request
			request
					.setAttribute("scrollTop", request
							.getParameter("scrollTop"));
			// set the scrollTabLeft to request
			request.setAttribute("scrollTableLeft", request
					.getParameter("scrollTableLeft"));
			// set the scrollTableTop to request
			request.setAttribute("scrollTableTop", request
					.getParameter("scrollTableTop"));
			// To get the system Date
			systemDate = request.getParameter("systemDate");
			systemDate = ((systemDate != null) && (systemDate.trim().length() > 0)) ? systemDate : SwtUtil.getSystemDateString();
			
			// set the attribute for lastRefTime in request
			request.setAttribute("lastRefTime", SwtUtil.getLastRefTimeOnGMTOffset(request,
					entityId));
			// To get the refresh flag
			refreshFlag = request.getParameter("refreshFlag");
			if (SwtUtil.isEmptyOrNull(refreshFlag)) {
				refreshFlag = "N";
			}

			// get the system date.
			// Start : Method modified by Balaji for Mantis 1991
			systemDBDate = SwtUtil.getSysParamDateWithEntityOffset(entityId);
			// End : Method modified by Balaji for Mantis 1991
			// To get the todayDate
			todayDate = SwtUtil.getSysDateWithFmt(systemDBDate);
			// To get the today Date as String
			todayDateAsString = SwtUtil.getSysDateWithFmt(systemDBDate);
			// To get the current system format
			format = SwtUtil.getCurrentSystemFormats(request.getSession());
			// get the date representing the calendar time value with given
			// 1 dateIncrement and systemDBDate
			datePlusOneAsString = SwtUtil.getDBSysDatewithoutTimeAsString(1,
					systemDBDate);
			// get the date representing the calendar time value with given
			// 2 dateIncrement and systemDBDate
			todayDatePlusTwoAsString = SwtUtil.getDBSysDatewithoutTimeAsString(
					2, systemDBDate);
			// get the date representing the calendar time value with given
			// 3 dateIncrement and systemDBDate
			todayDatePlusThreeAsString = SwtUtil
					.getDBSysDatewithoutTimeAsString(3, systemDBDate);
			// get the date representing the calendar time value with given
			// 4 dateIncrement and systemDBDate
			todayDatePlusFourAsString = SwtUtil
					.getDBSysDatewithoutTimeAsString(4, systemDBDate);
			// get the date representing the calendar time value with given
			// 5 dateIncrement and systemDBDate
			todayDatePlusFiveAsString = SwtUtil
					.getDBSysDatewithoutTimeAsString(5, systemDBDate);
			// get the date representing the calendar time value with given
			// 6 dateIncrement and systemDBDate
			todayDatePlusSixAsString = SwtUtil.getDBSysDatewithoutTimeAsString(
					6, systemDBDate);
			// parse the date, get the date as current date formatted
			testDateFormatted = SwtUtil.parseDate(todayDateAsString, format
					.getDateFormatValue());
			// To get the system date
			request.setAttribute("sysDateFrmSession", todayDateAsString);
			// parse the date, get the date as current date formatted
			dateOnLoad = SwtUtil.parseDate(systemDate, format
					.getDateFormatValue());
			// Set the default entity id or the entity id present in the form
			metagroupMonitorForm.setEntityId(entityId);
			// Put the list of entities in the form
			metagroupMonitorForm.setEntities(getEntityList(request));
			// set the entities to request
			request
					.setAttribute("entities", metagroupMonitorForm
							.getEntities());
			// Put the currencies list in the form
			metagroupMonitorForm.setCurrencies(getCurrencyList(request, roleId,
					entityId));
			// set the currencies list in the form
			request.setAttribute("currencies", metagroupMonitorForm
					.getCurrencies());
			// Check if the currency is null. If yes, then set it to the first
			// currency available in the list.
			if (SwtUtil.isEmptyOrNull(currencyId)) {
				currencyId = SwtUtil.getDomesticCurrencyForUser(request,
						SwtUtil.getCurrentHostId(), entityId);
			}
			// Set the currencyId to form
			metagroupMonitorForm.setCurrencyId(currencyId);
			if (metagroupMonitorForm.getCurrencies().size() == 0)
				metagroupMonitorForm.setCurrencyId("");
			// Fetches the location list for the entity and set it in form
			// object
			locationId = metagroupMonitorForm.getLocationId();
			// get the form date
			formDate = metagroupMonitorForm.getDate();
			// set the locations to request
			request.setAttribute("locations", metagroupMonitorForm
					.getLocations());
			// get the group code
			groupCode = metagroupMonitorForm.getGroupCode();
			// Set the locations to form
			metagroupMonitorForm
					.setLocations(getLocationList(request, entityId));
			selectedTabIndex = metagroupMonitorForm.getSelectedTabIndex();
			// get the callStatus from request
			callStatus = request.getParameter("callstatus");

			// Start: Code added by Vivekanandan A for mantis 1991 on 18-07-2012
			// Condition to check entity id is not equal existing entity Id to
			// select first tab
			if (!SwtUtil
					.isEmptyOrNull(request.getParameter("existingEntityId"))
					&& !(request.getParameter("existingEntityId")
							.equals(entityId))) {
				// assigning the form date to today date
				formDate = todayDate;
				// set the selected tab index as one in request
				request.setAttribute("selectedTabIndex", "1");
				// set the selected tab name in request
				request.setAttribute("selectedTabName",
						"metagroupMonitorTodayParent");
				// flag to handle isEntityChanged
				isEntityChanged = true;
			}

			request.setAttribute("existingEntityId", entityId);
			// End: Code added by Vivekanandan A for mantis 1991 on 18-07-2012
			/*
			 * Condition to check the call status received and set the date
			 * based on that the call status variable is used to know the call
			 * made from currency monitor and menuclick
			 */
			if (callStatus != null) {
				// Stores the selected tab index in the form object
				submitDate = formDate;
			} else {
				// Stores the date in the form object
				if (selectedTabIndex != null) {
					if (formDate.equalsIgnoreCase(datePlusOneAsString)) {
						submitDate = datePlusOneAsString;
						metagroupMonitorForm.setDate(datePlusOneAsString);
					} else if (formDate
							.equalsIgnoreCase(todayDatePlusTwoAsString)) {
						submitDate = todayDatePlusTwoAsString;
						metagroupMonitorForm.setDate(todayDatePlusTwoAsString);
					} else if (formDate
							.equalsIgnoreCase(todayDatePlusThreeAsString)) {
						submitDate = todayDatePlusThreeAsString;
						metagroupMonitorForm
								.setDate(todayDatePlusThreeAsString);
					} else if (formDate
							.equalsIgnoreCase(todayDatePlusFourAsString)) {
						submitDate = todayDatePlusFourAsString;
						metagroupMonitorForm.setDate(todayDatePlusFourAsString);
					} else if (formDate
							.equalsIgnoreCase(todayDatePlusFiveAsString)) {
						submitDate = todayDatePlusFiveAsString;
						metagroupMonitorForm.setDate(todayDatePlusFiveAsString);
					} else if (formDate
							.equalsIgnoreCase(todayDatePlusSixAsString)) {
						submitDate = todayDatePlusSixAsString;
						metagroupMonitorForm.setDate(todayDatePlusSixAsString);
					} else if (selectedTabIndex.equalsIgnoreCase("8")
							&& ((formDate != null) && (formDate.length() > 0))) {
						if (todayDateAsString.equals(systemDate)) {
							submitDate = formDate;
							metagroupMonitorForm.setDate(formDate);
						} else {
							submitDate = todayDateAsString;
							metagroupMonitorForm.setDate(todayDateAsString);
						}
					} else {
						todayDateAsString = SwtUtil.formatDate(
								testDateFormatted, SwtUtil
										.getCurrentSystemFormats(
												request.getSession())
										.getDateFormatValue());
						submitDate = todayDateAsString;
						metagroupMonitorForm.setDate(todayDateAsString);
					}
					systemDateCal = Calendar.getInstance();
					systemDateCal.setTime(dateOnLoad);
					systemDateCal.add(Calendar.DATE, 7);
					if (systemDate.equals(todayDateAsString)
							|| (selectedTabIndex.equalsIgnoreCase("8") && (dateOnLoad
									.before(testDateFormatted) && systemDateCal
									.getTime().after(testDateFormatted)))) {
						/*
						 * Start code modified by Vivekanandan for Mantis 1991
						 * on 24-07-2012
						 */
						if (isEntityChanged)
							// if entity changed set date compare as yes
							request.setAttribute("dateComparing",
									SwtConstants.YES);
						else
							// Set date compare as no
							request.setAttribute("dateComparing",
									SwtConstants.NO);
						/*
						 * End code modified by Vivekanandan for Mantis 1991 on
						 * 24-07-2012
						 */
					} else {
						request.setAttribute("dateComparing", SwtConstants.YES);
					}
				} else {
					submitDate = todayDate;
					metagroupMonitorForm.setDate(todayDate);
				}
				// Stores the selected tab index in form object
			}
			request.setAttribute("submitDate", submitDate);
			if (screenFlag.equals("bookMonitor")) {
				// set the locationId based on condition check
				if (SwtUtil.isEmptyOrNull(locationId)) {
					locationId = request.getParameter("locationId");
					if (SwtUtil.isEmptyOrNull(locationId)) {
						locationId = SwtConstants.ALL_VALUE;
					}
				}
				// set the attribute for locations & locationId in request
				request.setAttribute("locations", metagroupMonitorForm
						.getLocations());
				request.setAttribute("locationId", locationId);
				metagroupMonitorForm.setLocationId(locationId);
				// Sets the group list for the group drop down
				metagroupMonitorForm.setGroups(getGroupList(request, entityId));
				// set the groupCode based on the condition check
				if (SwtUtil.isEmptyOrNull(groupCode)) {
					groupCode = request.getParameter("groupCode");
					if (SwtUtil.isEmptyOrNull(groupCode)) {
						groupCode = SwtConstants.ALL_VALUE;
					}
				}
				// set the attribute for groupCode,groups in request
				request.setAttribute("groupCode", groupCode);
				// set the attribute for groups to requst
				request
						.setAttribute("groups", metagroupMonitorForm
								.getGroups());
				// set the GroupCode
				metagroupMonitorForm.setGroupCode(groupCode);
				// get the book monitor details
				predictedBalanceDetailList = (ArrayList) metagroupMonitorManager
						.getBookMonitorDetailsNew(entityId, currencyId,
								submitDate, format, refreshFlag, roleId,
								locationId, groupCode);
			} else if (screenFlag.equals("metaGroupMonitor")) {
				// set the locationId
				if (SwtUtil.isEmptyOrNull(locationId)) {
					locationId = SwtConstants.ALL_VALUE;
				}
				// set the attribute for locations,locationId in request
				request.setAttribute("locations", metagroupMonitorForm
						.getLocations());
				request.setAttribute("locationId", locationId);
				metagroupMonitorForm.setLocationId(locationId);
				metagroupMonitorForm.setLocationId(locationId);
				// get the metagroup monitor details
				predictedBalanceDetailList = (ArrayList) metagroupMonitorManager
						.getMetagroupMonitorDetailsNew(entityId, currencyId,
								submitDate, format, refreshFlag, roleId,
								locationId);
			} else {
				if (SwtUtil.isEmptyOrNull(locationId)) {
					// get the location Id
					locationId = request.getParameter("locationId");
					if (SwtUtil.isEmptyOrNull(locationId)) {
						locationId = SwtConstants.ALL_VALUE;
					}
				}
				// set the attribute for locations,locationId in request
				request.setAttribute("locations", metagroupMonitorForm
						.getLocations());
				request.setAttribute("locationId", locationId);
				metagroupMonitorForm.setLocationId(locationId);
				// get the meta group Id
				metagroupId = metagroupMonitorForm.getMetagroupId();
				metagroupMonitorForm.setMetagroups(getMetagroupList(request,
						entityId));
				// set the metagroupId
				if (SwtUtil.isEmptyOrNull(metagroupId)) {
					metagroupId = request.getParameter("metagroupId");
					if (SwtUtil.isEmptyOrNull(metagroupId)) {
						metagroupId = SwtConstants.ALL_VALUE;
					}
				}
				// set the attribute for metagroupId,metagroups in request
				request.setAttribute("metagroupId", metagroupId);
				metagroupMonitorForm.setMetagroupId(metagroupId);
				// set the attribute for metaGroups
				request.setAttribute("metagroups", metagroupMonitorForm
						.getMetagroups());
				// get the GroupMonitor Details
				predictedBalanceDetailList = (ArrayList) metagroupMonitorManager
						.getGroupMonitorDetailsNew(entityId, currencyId,
								submitDate, format, refreshFlag, roleId,
								locationId, metagroupId);
			}

			if (!predictedBalanceDetailList.isEmpty()) {
				metagroupMonitorForm
						.setPredictedBalances((Collection) predictedBalanceDetailList
								.get(0));
				// To get the tab flag value
				tabFlag = (String) predictedBalanceDetailList.get(2);
			}
			balanceTotal = (Collection) predictedBalanceDetailList.get(1);
			itrbalanceTotal = balanceTotal.iterator();
			// Sets the total and negative flag for total in form object
			while (itrbalanceTotal.hasNext()) {
				metagroupMonitorCurrencyBalanceTO = itrbalanceTotal.next();
				metagroupMonitorForm.setTotal(metagroupMonitorCurrencyBalanceTO
						.getTotal());
				metagroupMonitorForm
						.setTotalNegativeFlag(metagroupMonitorCurrencyBalanceTO
								.isTotalNegativeFlag());
			}
			// get the predicted Balance Total
			predictedBalanceTotal = (Collection) predictedBalanceDetailList
					.get(0);
			itrPredictedBalance = predictedBalanceTotal.iterator();
			// Iterates the predicted balance collection to fetch the total sum
			while (itrPredictedBalance.hasNext()) {
				MetagroupCodePredictedBalanceTO metagroupCodePredictedBalanceTO = itrPredictedBalance
						.next();
				grandTotal = grandTotal
						+ (metagroupCodePredictedBalanceTO
								.getPredictedBalanceBeforeFormatting())
								.doubleValue();
			}
			// Converts the grand total in the specified format and stores it in
			// Form object

			// set the grandTotal to form
			metagroupMonitorForm.setGrandTotalAsString(SwtUtil.formatCurrency(
					currencyId, new Double(grandTotal)));
			grandTotalNegative = (grandTotal >= 0) ? false : true;
			// set the grandTotalNegative to form
			metagroupMonitorForm.setGrandTotalNegative(grandTotalNegative);
			// Set the refresh rate of the screen
			setScreenRefreshRate(request);
			// Calling this method for getting the font size from the request.
			setScreenFontSize(request);
			// Get the test date
			testDate = SwtUtil
					.getTestDateFromParams(SwtUtil.getCurrentHostId());
			// Get the today date
			today = testDate.getDate() + "/" + (testDate.getMonth() + 1) + "/"
					+ (testDate.getYear() + 1900);
			// Set the attribute to request
			jobFlag = metagroupMonitorManager
					.getBookGroupMonitorJobFlag(entityId);
			if (!jobFlag) {
				request.setAttribute("bkMonitorJobStatusFlag", "Y");
			}
			// set the today to request
			request.setAttribute("today", today);
			// set the reply_status_ok to request
			request.setAttribute("reply_status_ok", "true");
			// set the reply_message to request
			request.setAttribute("reply_message", "Data fetch ok");
			// set the optimesto request
			request.setAttribute("opTimes", "");
			log.debug(this.getClass().getName()
					+ " - [getMonitorTabList] -Exit ");
		} catch (SwtException exp) {
			log.error("Exception Catch in getMonitorTabList method : "
					+ exp.getMessage());
			SwtUtil.logException(exp, request, "");
			throw SwtErrorHandler.getInstance().handleException(exp,
					"setFontSize()", MetagroupMonitorAction.class);
		} catch (Exception exp) {
			log.error("Exception Catch in getMonitorTabList method : "
					+ exp.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "getMonitorTabList", MetagroupMonitorAction.class),
					request, "");
			throw new SwtException(exp.getMessage());
		} finally {
			// Cleaning the unreferenced objects
			format = null;
			currencyId = null;
			dateOnLoad = null;
			testDateFormatted = null;
			systemDate = null;
			locationId = null;
			formDate = null;
			submitDate = null;
			selectedTabIndex = null;
			todayDate = null;
			refreshFlag = null;
			predictedBalanceDetailList = null;
			grandTotal = 0.0;
			grandTotalNegative = false;
			testDate = null;
			today = null;
			groupCode = null;
			metagroupId = null;
			todayDateAsString = null;
			itrPredictedBalance = null;
			predictedBalanceTotal = null;
			metagroupMonitorCurrencyBalanceTO = null;
			balanceTotal = null;
			callStatus = null;
			itrbalanceTotal = null;
			datePlusOneAsString = null;
			todayDatePlusTwoAsString = null;
			todayDatePlusThreeAsString = null;
			todayDatePlusFourAsString = null;
			todayDatePlusFiveAsString = null;
			todayDatePlusSixAsString = null;
			systemDateCal = null;
			systemDBDate = null;
		}
		return tabFlag;
	}

	/**
	 * This function sets the font size to a user defined value.<br>
	 * 
	 * @param request
	 * @param screenId
	 * @throws SwtException
	 * <AUTHOR>
	 */
	private void setFontSize(HttpServletRequest request, String screenId)
			throws SwtException {
		// Declares erroeMessage
		String errorMessage = null;
		// Declares screenOption
		ScreenOption screenOption = null;
		// Declares optionManager
		ScreenOptionManager optionManager = null;
		try {
			log.debug(this.getClass().getName() + " - [setFontSize] - Begins");
			// Creates new instance of ScreenOption
			screenOption = new ScreenOption();
			// Casts with the ScreenOptionManager
			optionManager = (ScreenOptionManager) SwtUtil
					.getBean("screenOptionManager");
			// Sets the host Id from SwtUtil
			screenOption.getId().setHostId(SwtUtil.getCurrentHostId());
			// Sets the user Id from SwtUtil
			screenOption.getId().setUserId(
					SwtUtil.getCurrentUserId(request.getSession()));
			// Sets the screen Id
			screenOption.getId().setScreenId(screenId);
			// Gets the font size from screenOption
			screenOption = optionManager.getFontSize(screenOption);
			// Sets the font size in the request
			request.setAttribute("fontSize", screenOption.getPropertyValue());
			log.debug(this.getClass().getName() + " - [setFontSize] - Ends");
		} catch (Exception exception) {
			errorMessage = exception.getStackTrace()[0].getClassName() + "."
					+ exception.getStackTrace()[0].getMethodName() + ":"
					+ exception.getStackTrace()[0].getLineNumber() + " "
					+ exception.getMessage();
			log.error(this.getClass().getName()
					+ " - setFontSize(). Exception : " + errorMessage);
			// Re-throws tException
			throw SwtErrorHandler.getInstance().handleException(exception,
					"setFontSize()", MetagroupMonitorAction.class);
		}
	}

	/**
	 * This method is used to set the font size and put in the request.
	 * 
	 * @param request
	 * @throws SwtException
	 * <AUTHOR>
	 */
	private void setScreenFontSize(HttpServletRequest request)
			throws SwtException {
		// Declares the user Id
		String userId = null;
		// Declares the error message
		String errorMessage = null;
		// Declares the screenOption object
		ScreenOption screenOption = null;
		// Declares the ScreenOptionManager object
		ScreenOptionManager screenOptionManager = null;
		try {
			log.debug(this.getClass().getName()
					+ " - [setScreenFontSize] - Begins");
			// Reads the user Id from session
			userId = SwtUtil.getCurrentUserId(request.getSession());
			// Creates a new instance of ScreenOption
			screenOption = new ScreenOption();
			// Gets the ScreenOptionManager bean
			screenOptionManager = (ScreenOptionManager) (SwtUtil
					.getBean("screenOptionManager"));
			// Sets the host Id
			screenOption.getId().setHostId(SwtUtil.getCurrentHostId());
			// Sets the user Id
			screenOption.getId().setUserId(userId);
			// Sets the screen Id
			screenOption.getId().setScreenId(SwtConstants.GROUP_MONITOR_ID);
			// Calls this method to get the refresh rate and set it in request
			screenOption = screenOptionManager.getRefreshRate(screenOption);
			request.setAttribute("fontSize", screenOption.getPropertyValue());
			log.debug(this.getClass().getName()
					+ " - [setScreenFontSize] - Ends");
		} catch (Exception exception) {
			errorMessage = exception.getStackTrace()[0].getClassName() + "."
					+ exception.getStackTrace()[0].getMethodName() + ":"
					+ exception.getStackTrace()[0].getLineNumber() + " "
					+ exception.getMessage();
			log.error(this.getClass().getName()
					+ " - setScreenFontSize(). Exception : " + errorMessage);
			// Re-throws tException
			throw SwtErrorHandler.getInstance().handleException(exception,
					"setScreenFontSize()", MetagroupMonitorAction.class);
		}
	}

}
