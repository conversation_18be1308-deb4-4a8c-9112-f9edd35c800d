/*
 * @(#)ScenarioSummaryAction.java 1.0 24/12/12
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.work.web;

import java.net.URLDecoder;
import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import javax.xml.parsers.ParserConfigurationException;
import javax.xml.transform.TransformerException;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.apache.struts2.convention.annotation.Action;
import org.apache.struts2.convention.annotation.AllowedMethods;
import org.apache.struts2.convention.annotation.Result;
import org.json.JSONArray;
import org.swallow.control.model.Scenario;
import org.swallow.control.service.ScenMaintenanceManager;
import org.swallow.dao.LogonDAO;
import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.maintenance.model.EntityUserAccess;
import org.swallow.maintenance.service.AcctMaintenanceManager;
import org.swallow.model.MenuItem;
import org.swallow.pcm.maintenance.model.core.ColumnInfo;
import org.swallow.pcm.maintenance.model.core.OptionInfo;
import org.swallow.pcm.maintenance.model.core.SelectInfo;
import org.swallow.pcm.maintenance.web.ResponseHandler;
import org.swallow.pcm.util.PCMConstant;
import org.swallow.pcm.util.SwtResponseConstructor;
import org.swallow.pcm.util.SwtXMLWriter;
import org.swallow.util.CacheManager;
import org.swallow.util.CommonDataManager;
import org.swallow.util.LabelValueBean;
import org.swallow.util.OpTimer;
import org.swallow.util.PropertiesFileLoader;
import org.swallow.util.ScenarioAlertsUtils;
import org.swallow.util.SwtConstants;
import org.swallow.util.SwtUtil;
import org.swallow.util.SystemFormats;
import org.swallow.work.model.AlertInstance;
import org.swallow.work.model.AlertTreeVO;
import org.swallow.work.model.EntityScenarioCount;
import org.swallow.work.model.ScenarioAlertCount;
import org.swallow.work.model.ScenarioInstanceLog;
import org.swallow.work.model.ScenarioInstanceMessage;
import org.swallow.work.model.ScenarioSummaryVO;
import org.swallow.work.model.ScenarioTreeElement;
import org.swallow.work.model.ScenariosSummary;
import org.swallow.work.model.ScreenOption;
import org.swallow.work.service.InputExceptionsMessagesManager;
import org.swallow.work.service.ScenarioSummaryManager;
import org.swallow.work.service.ScreenOptionManager;
import org.swallow.work.web.form.MonitorTabBean;

import com.opensymphony.xwork2.ActionSupport;

import org.apache.struts2.ServletActionContext;
import org.apache.struts2.convention.annotation.Action;
import org.apache.struts2.convention.annotation.AllowedMethods;
import org.apache.struts2.convention.annotation.Namespace;
import org.apache.struts2.convention.annotation.Result;
/**
 * This is the action class which contains all the methods, many of which are
 * invoked form front end.
 * 
 */
@Action(value = "/logon", results = {
	    @Result(name = "fail", location = "/login.jsp"),
	    @Result(name = "success", location = "/main.jsp"),
	    @Result(name = "error", location = "/fault.jsp"),
	    @Result(name = "preloginscreen", location = "/prelogin.jsp"),
	    @Result(name = "data", location = "/jsp/data.jsp")
	})
@AllowedMethods ({"reLogin", "login", "loginFail", "preLoginScreen", "preLoginScreenData"}) 
public class ScenarioSummaryAction extends CustomActionSupport {

	/**
	 * Initializing logger object for this class
	 */
	private final Log log = LogFactory.getLog(ScenarioSummaryAction.class);
	/**
	 * Initializing menuItemIds.
	 */
	private final String menuItemId = "" + SwtConstants.SCREEN_SCENARIOSUMMARY;
	private final String menuItemIdWorkFlow = "" + SwtConstants.SCREEN_WORKFLOWMONITOR;

	/**
	 * Initializing InputExceptionsDataManager object
	 */
	private ScenarioSummaryManager scenarioSummaryManager = null;
	private ScenMaintenanceManager scenMaintenanceManager = null;

	/**
	 * Initializing OpTimer object
	 */
	private OpTimer opTimer = new OpTimer();


	/**
	 * @param scenMaintenanceManager the scenMaintenanceManager to set
	 */
	public void setScenMaintenanceManager(
			ScenMaintenanceManager scenMaintenanceManager) {
		this.scenMaintenanceManager = scenMaintenanceManager;
	}

	/**
	 * @param scenarioSummaryManager the scenarioSummaryManager to set
	 */
	public void setScenarioSummaryManager(ScenarioSummaryManager scenarioSummaryManager) {
		this.scenarioSummaryManager = scenarioSummaryManager;
	}

	
	/**
	 * Method to load  scenario summary tree depending of flag sent(Apply Threshold, Hide Zeros and Show alertable only) on which we can find title, description, number of records and time to cut-off 
	 * and if any scenario is selected from the tree, then we display a grid containing some detailed informations of alerts
	 * such as Entity name , currency and counts.
	 * 
	 * 
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return ActionForward
	 * @throws SwtException
	 */
	public String summaryData()
			throws SwtException {
		HttpServletRequest request = ServletActionContext.getRequest();
    	HttpServletResponse response = ServletActionContext.getResponse();
		/* Variable Declaration for category name */		
		String categoryName = null ;
		/* Variable Declaration for scenario  */		
		Scenario scenario = null;
		/* Variable Declaration for scenario List By Cat  */		
		ScenarioSummaryVO scenarioListByCat;
		/* Variable Declaration for scenario Tree  */		
		ArrayList<ScenarioSummaryVO> scenarioTree;
		/* Variable Declaration for category Counts HashMap */		
		HashMap<String, String> categoryCounts=null;
		/* Variable Declaration for tree */		
		ArrayList<ScenarioTreeElement> tree = null;
		/* Variable Declaration for scenario List per category */
		ArrayList<Scenario> scenarioList =null;
		/* Variable Declaration for tree element */
		ScenarioTreeElement element =null;
		/* Variable Declaration for grid data */
		ArrayList<EntityScenarioCount> grid;
		/* Variable Declaration for scenario info containing counts and cut-off time */
		StringBuffer scenarioInfo = null;
		/* Variable Declaration for category information containing counts per category */
		String categoryInfo =null;
		/* Variable Declaration for selected scenario id */
		String selectedScenarioId=null;
		/* Variable Declaration for currency threshold Flag */
		String currencythresholdFlag = null;
		/* Variable Declaration for alertable Flag */
		String alertableFlag = null;
		/* Variable Declaration for hide Zero Flag */
		String hideZeroFlag = null;
		/* Variable Declaration for role Id */
		String roleId = null;
		/* Variable Declaration for Scenario Summary Object*/
		ScenariosSummary scenarioSummary =null;
		/* Variable Declaration for Scenario Summary Object*/
		ScenariosSummary scenarioSummaryTab2 =null;
		/* Variable Declaration for total Count */
		int totalCount=0; 
		/* Variable Declaration for selected scenario last run */
		String lastrun= null;
		/* Variable Declaration for summary Grouping */
		String summaryGrouping = null;
		/* Variable Declaration for selected Entity Id */
		String selectedEntityId= null;
		/* Variable Declaration for facility Id */
		String facilityId = null;
		/* Variable Declaration for facility name */
		String facilityName = null;
		/* Variable Declaration for category Description */
		String categoryDescription = null;
		/* Variable Declaration for from WorkFlowMonitor */
		String fromWorkFlow = null; 
		/* Variable Declaration for useGeneric */
		String useGeneric = "";
		/* Variable Declaration for scenarioTitle */
		String scenarioTitle = "";
		/* Variable Declaration for selectedSort */
		String selectedSort = null;
		String selectedFilter = null;
		/* Variable Declaration for selected Currency Group */
		String selectedCurrencyGroup = null;
		/* Variable Declaration for facility id access*/
		String facilityAccess = null;
		/* Variable Declaration for facility id access*/
		String hostId = null;
		/* Variable Declaration for caller Method*/
		String callerMethod =null;
		/* Variable Declaration for caller Method*/
		String selectedTab =null;
		/* Variable Declaration of supportAllCurrency */ 
		String supportAllCurrency = SwtConstants.STR_FALSE;
		/* Variable Declaration of supportAllEntity */ 
		String supportAllEntity = SwtConstants.STR_FALSE;
		/*Variable Declaration of existing scenario in the list */
		boolean scenarioExist = false;
		/*Variable Declaration of scenario alertable */
		String isAlertable = null;
		/*Variable Declaration of the first Load */
		String firstLoad = null;
		int scenarioTotalForTab1 = 0;
		int scenarioTotalForTab2 = 0;
		
		try {
			log.debug(this.getClass().getName() + " - [summaryData] - Entry");
			opTimer.start("all");
			// get the selected Scenario Id form request
			selectedScenarioId=request.getParameter("selectedScenario");
			// get the selected Entity Id form request
			selectedEntityId = request.getParameter("entityId");
			// get the caller Method form request
			callerMethod = request.getParameter("callerMethod");
			// get the caller Method form request
			firstLoad = request.getParameter("firstLoad");
			// get fromWorkFlow  form request if caller application is workFlowMonitor, this variable should  be set to true
			fromWorkFlow = request.getParameter("fromWorkFlow")!=null?request.getParameter("fromWorkFlow"):SwtConstants.STR_FALSE;
			// get currency threshold Flag form request
			currencythresholdFlag=request.getParameter("currThreshold")!=null?request.getParameter("currThreshold"):SwtConstants.STR_FALSE;
			// get alertable Flag form request
			alertableFlag=request.getParameter("alertScenarios")!=null?request.getParameter("alertScenarios"):fromWorkFlow.equals(SwtConstants.STR_TRUE)?SwtConstants.STR_FALSE:SwtConstants.STR_TRUE;
			// get hideZero Flag form request
			hideZeroFlag=request.getParameter("zeroBalances")!=null?request.getParameter("zeroBalances"):SwtConstants.STR_TRUE;
			// get selected Sort form request
			selectedSort = request.getParameter("selectedSort")!=null?request.getParameter("selectedSort"):"-1|DESC";
			selectedFilter = request.getParameter("selectedFilter");
			// get selected Currency Group form request
			selectedCurrencyGroup =request.getParameter("selectedCurrencyGroup")!=null?request.getParameter("selectedCurrencyGroup"):"All";
			//get if the selected scenario is alertable
			isAlertable =request.getParameter("isScenarioAlertable")!=null?request.getParameter("isScenarioAlertable"):SwtConstants.NO;
			// get the selected Tab form request
			selectedTab = request.getParameter("selectedTab");
		
			// get roleId  form session
			roleId = ((CommonDataManager) request.getSession()
					.getAttribute(SwtConstants.CDM_BEAN)).getUser()
					.getRoleId();			
			//if currency threshold is not sent as parameter then set it to SwtConstants.NO 
			currencythresholdFlag = (currencythresholdFlag.equals(SwtConstants.STR_TRUE))?SwtConstants.YES:SwtConstants.NO;
			//if currency threshold is not sent as parameter then set it to SwtConstants.NO 
			alertableFlag = (alertableFlag.equals(SwtConstants.STR_TRUE))?SwtConstants.YES:SwtConstants.NO;
			//if currency threshold is not sent as parameter then set it to SwtConstants.NO 
			hideZeroFlag = (hideZeroFlag.equals(SwtConstants.STR_TRUE))?SwtConstants.YES:SwtConstants.NO;
			// init variables
			scenarioTree = new ArrayList<ScenarioSummaryVO>();
			scenarioList = new ArrayList<Scenario>();
			//Change callerMethod to display all scenarios if show Alertable only is checked
			callerMethod = alertableFlag.equals(SwtConstants.NO)?"___":callerMethod;
			//get the list of scenarios which will be displayed on the tree depending of roleId selectedEntityId, selected currencyGroup and flags  
			scenarioSummary = scenarioSummaryManager.getScenariosSummaryInfoDetails(roleId,selectedEntityId,currencythresholdFlag,hideZeroFlag,alertableFlag,selectedCurrencyGroup,callerMethod, selectedTab);			
			tree= scenarioSummary.getTree();
			//extract the list of category and their respective counts from scenarioSummary
			categoryCounts= scenarioSummary.getCategoryInfo();
			//extract total count from scenarioSummary			
			totalCount = scenarioSummary.getTotalCount();
			
			if(!SwtUtil.isEmptyOrNull(selectedTab)) {
				if("1".equals(selectedTab)) {
					scenarioTotalForTab1 = totalCount;
					scenarioSummaryTab2 = scenarioSummaryManager.getScenariosSummaryInfoDetails(roleId,selectedEntityId,currencythresholdFlag,hideZeroFlag,alertableFlag,selectedCurrencyGroup,callerMethod, "2");			
					scenarioTotalForTab2 = scenarioSummaryTab2.getTotalCount();
				}else {
					scenarioTotalForTab2 = totalCount;
					scenarioSummaryTab2 = scenarioSummaryManager.getScenariosSummaryInfoDetails(roleId,selectedEntityId,currencythresholdFlag,hideZeroFlag,alertableFlag,selectedCurrencyGroup,callerMethod, "1");			
					scenarioTotalForTab1 = scenarioSummaryTab2.getTotalCount();

				}
			}
			
			//processed in filling the tree if the variable is not null
			if (tree != null) {
				// browse all element of the tree
				for (int i = 0; i < tree.size(); i++) {

					element = (ScenarioTreeElement) tree.get(i);
					if (element != null) {
						// For the first iteration set cateogryName as the value
						// of category name of the first element
						if (categoryName == null)
							categoryName = element.getCategoryName();
						// Create a category node if the value of categoryName
						// is different form the previous one
						else if ((!categoryName.equals(element
								.getCategoryName()))) {

							// A category node contain a name, some
							// informations(counts), description and a list of
							// scenarios
							scenarioListByCat = new ScenarioSummaryVO();
							scenarioListByCat.setCategory(categoryName);
							categoryInfo = categoryCounts.get(categoryName);
							scenarioListByCat.setCount(categoryInfo);
							scenarioListByCat
									.setCategoryDescription(categoryDescription);
							scenarioListByCat.setScenarioList(scenarioList);
							// add scenario list to category node
							scenarioTree.add(scenarioListByCat);
							// create a new scenario list of the next category
							scenarioList = new ArrayList<Scenario>();
							categoryName = element.getCategoryName();
						}
						// For each element of the tree create a new scenario
						scenario = new Scenario();
						// Set scenario Id
						scenario.getId().setScenarioId(element.getScenarioId());
						if (element.getScenarioId().equals(selectedScenarioId))
							scenarioExist = true;
						if(SwtUtil.isEmptyOrNull(selectedScenarioId) && firstLoad.equals("true") && !fromWorkFlow.equals(SwtConstants.STR_TRUE) ){
							selectedScenarioId=element.getScenarioId();
							scenarioExist=true;
							isAlertable =element.getIsAlertable();
						}
						// set scenario Title
						scenario.setTitle(element.getScenarioName());
						// set scenario description
						scenario.setDescription(element
								.getScenarioDescription());
						// set alertable flag
						scenario.setAlertable(element.getIsAlertable());
						// fill scenario informations (counts and cut-off time)
						scenarioInfo = new StringBuffer();
						scenarioInfo.append(" ");
						scenarioInfo.append(element.getCutOffTime());
						scenarioInfo.append(element.getCount() != 0 ? (" ("
								+ element.getCount() + ")") : " (0)");
						// set scenario informations
						scenario.setScenarioInfo(scenarioInfo.toString());
						scenario.setTreeOrder(i);
						// add the screnario to the list
						scenarioList.add(scenario);
						categoryDescription = element.getCategoryDescription();
					}

				}
				//For the last element of the tree we need to create a new category node and fill necessary informations
				scenarioListByCat = new ScenarioSummaryVO();
				scenarioListByCat.setCategory(categoryName);
				categoryInfo = categoryCounts.get(categoryName);
				scenarioListByCat.setCount(categoryInfo);
				scenarioListByCat.setCategoryDescription(categoryDescription);
				scenarioListByCat.setScenarioList(scenarioList);
				scenarioTree.add(scenarioListByCat);
				
			}
			// Init grid data variable
			grid = new ArrayList<EntityScenarioCount>();
			// get scenario details from DAO
			if (scenarioExist)
				scenario = scenMaintenanceManager
						.getEditableDataDetailList(selectedScenarioId);
			if (scenario != null) {
				// if the scenario summary grouping is null than set it to
				// "None"
				summaryGrouping = scenario.getSummaryGrouping() != null ? scenario
						.getSummaryGrouping() : SwtConstants.NO;
				// if the scenario facility is null than set it to "None"
				if (scenario.getFacility() != null)
					 {
						facilityId = scenario.getFacility().getFacilityid() != null ? scenario
								.getFacility().getFacilityid()
								: SwtConstants.FACILITY_NONE;
						facilityName = scenario.getFacility().getFacilityname() != null ? scenario
								.getFacility().getFacilityname()
								: SwtConstants.FACILITY_NONE;
							
					 }
				else
					{
						facilityId = SwtConstants.FACILITY_NONE;
						facilityName =SwtConstants.FACILITY_NONE;
					}
				// if the use generic display flag is not null than fill
				// useGeneric variable
				if (scenario.getUseGenericDisplay() != null)
					useGeneric = scenario.getUseGenericDisplay();
				// if the use scenario title is not null than fill useGeneric
				// variable
				if (scenario.getTitle() != null)
					scenarioTitle = scenario.getTitle();
				// Set supportAllCurrency and supportAllEntity variable
				// depending on value form database
				if (scenario.getFacility() != null) {
					supportAllCurrency = scenario.getFacility()
							.getSupportAllCurrency().equals(SwtConstants.YES) ? SwtConstants.STR_TRUE
							: SwtConstants.STR_FALSE;
					supportAllEntity = scenario.getFacility()
							.getSupportAllEntity().equals(SwtConstants.YES) ? SwtConstants.STR_TRUE
							: SwtConstants.STR_FALSE;
				}
			} else {
				facilityId = SwtConstants.FACILITY_NONE;
				facilityName =SwtConstants.FACILITY_NONE;
				summaryGrouping = SwtConstants.NO;
			}
			// Test if selected scenario id is not empty or null than get grid
			// data from DAO
			if (!SwtUtil.isEmptyOrNull(selectedScenarioId) && scenarioExist) {
				grid = scenarioSummaryManager.getScenarioCountByEntituCurrency(
						selectedScenarioId, roleId, currencythresholdFlag,
						summaryGrouping, selectedEntityId, selectedSort,
						selectedCurrencyGroup, isAlertable,selectedFilter);
				lastrun = scenarioSummaryManager
						.getSelectedScenarioLastRun(selectedScenarioId);
			}

			// get hostId form session
			hostId = CacheManager.getInstance().getHostId();
			if (useGeneric.equals(SwtConstants.YES)) {
				facilityAccess = "0";
				supportAllCurrency = SwtConstants.STR_TRUE;
				supportAllEntity = SwtConstants.STR_TRUE;
			} else {
				if (SwtUtil.isEmptyOrNull(facilityId)
						|| facilityId
								.equalsIgnoreCase(SwtConstants.FACILITY_NONE)) {
					facilityAccess = "2";
				} else
					// Get the access to the facility screen defined by its
					// facility id
					facilityAccess = scenarioSummaryManager.getFacilityAccess(
							hostId, facilityId, roleId);
			}
			// Put scenario tree, selected scenarioLastRun, selectedScenarioId ,
			// facilityId , scenarioTitle , useGeneric , fromWorkFlow , total
			// and grid data in request
			request.setAttribute("scenarioTree", scenarioTree);
			request.setAttribute("selectedScenarioLastRun",
					lastrun != null ? lastrun : "");
			request.setAttribute("selectedScenario",
					selectedScenarioId != null ? selectedScenarioId : "");
			request.setAttribute("facilityId", facilityId);
			request.setAttribute("facilityName", facilityName);
			request.setAttribute("facilityAccess", facilityAccess);
			request.setAttribute("supportAllCurrency", supportAllCurrency);
			request.setAttribute("supportAllEntity", supportAllEntity);
			request.setAttribute("useGeneric", useGeneric);
			request.setAttribute("scenarioTitle", scenarioTitle);
			request.setAttribute("fromWorkFlow", fromWorkFlow);
			request.setAttribute("total", totalCount);
			request.setAttribute("scenarioTotalForTab1", scenarioTotalForTab1);
			request.setAttribute("scenarioTotalForTab2", scenarioTotalForTab2);
			request.setAttribute("grid", grid);
			request.setAttribute("rowSize", grid.size());
			request.setAttribute("reply_status_ok", SwtConstants.STR_TRUE);
			request.setAttribute("reply_message", "Data fetch OK");
			/* Set column width with request attribute */
			bindColumnWidthInRequest(request, fromWorkFlow);
			// Stop opTimer
			opTimer.stop("all");
			// set the attribute for opTimes,lastRefTime in request
			request.setAttribute("opTimes", opTimer.getOpTimes());
			log.debug(this.getClass().getName() + " - [summaryData] - Exit");
			return "data";
		} catch (SwtException swtExp) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [summaryData] method : - "
					+ swtExp.getMessage());
			request.setAttribute("reply_status_ok", SwtConstants.STR_FALSE);
			request.setAttribute("reply_message", swtExp.getMessage());
			request.setAttribute(
					"reply_location",
					swtExp.getStackTrace()[0].getClassName() + "."
							+ swtExp.getStackTrace()[0].getMethodName() + ":"
							+ swtExp.getStackTrace()[0].getLineNumber());
			return "dataerror";
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [summaryData] method : - "
					+ exp.getMessage());
			SwtUtil.logException(
					SwtErrorHandler.getInstance().handleException(exp,
							"summaryData", ScenarioSummaryAction.class),
					request, "");
			return "fail";

		} finally {
			/* null the objects created already. */
			categoryName = null;
			scenario = null;
			firstLoad = null;
			scenarioListByCat = null;
			scenarioTree = null;
			categoryCounts = null;
			tree = null;
			scenarioList = null;
			element = null;
			grid = null;
			scenarioInfo = null;
			categoryInfo = null;
			selectedScenarioId = null;
			currencythresholdFlag = null;
			alertableFlag = null;
			hideZeroFlag = null;
			roleId = null;
			scenarioSummary = null;
			totalCount = 0;
			lastrun = null;
			summaryGrouping = null;
			selectedEntityId = null;
			facilityId = null;
			facilityName = null;
			categoryDescription = null;
			fromWorkFlow = null;
			useGeneric = null;
			scenarioTitle = null;
			selectedSort = null;
			selectedCurrencyGroup = null;
		}
	}
	
	/**
	 * This Method is used to get scenario summary screen info such as flags, entityList and lastRefresh time 
	 * 
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return ActionForward
	 * @throws SwtException
	 */
	public String summaryScreenInfo()
					throws SwtException {
		HttpServletRequest request = ServletActionContext.getRequest();
    	HttpServletResponse response = ServletActionContext.getResponse();
		/* Variable Declaration for session */
		String selectedEntityId = null;
		String currencythresholdFlag = null;
		String hideZeroFlag = null;
		String showAlertableFlag =null;
		String entityId= null;
		String popupScenariosFlag = null;
		String flashScenariosFlag = null;
		String emailScenariosFlag = null;
		String callerMethod = null;
		String[] options = null;
		/* Variable Declaration for selectedCategory */
		String selectedCategory = null;
		// To hold the tab category list
		ArrayList<MonitorTabBean> tabCategoryNamesList = null;
		String dateFormat = null;
		/* Variable Declaration for screenOption */
		ScreenOption screenOption = null;
		/* Variable Declaration for screenOptionManager */
		ScreenOptionManager screenOptionManager = null;
		String screenId= null;
		String hostId= null;
		String userId= null;
		String currencyFormat = null;
		try {
			
			log.debug(this.getClass().getName() + " - [summaryScreenInfo] - Entry");
			opTimer.start("all");
			/* Used to get the instance from Configuration file */
			screenOptionManager = (ScreenOptionManager) (SwtUtil.getBean("screenOptionManager"));
			/* get screenId. */
			screenId = SwtConstants.ALERT_INSTANCE_SUMMARY_ID;
			hostId = CacheManager.getInstance().getHostId();
			userId = SwtUtil.getCurrentUser(request.getSession()).getId().getUserId();
			dateFormat= SwtUtil.getCurrentDateFormat(request.getSession());
			currencyFormat= SwtUtil.getCurrentCurrencyFormat(request.getSession());
			callerMethod = request.getParameter("callerMethod");
			if(SwtUtil.isEmptyOrNull(callerMethod)) {
				callerMethod = "___";
			}
			options = callerMethod.split("(?!^)");
			
			for (int i=0 ; i<options.length;i++){
				// || options[i].equals("_")
				options[i] = options[i].equals(SwtConstants.YES)? SwtConstants.STR_TRUE:SwtConstants.STR_FALSE;
			}
			currencythresholdFlag=request.getParameter("currThreshold")!=null?request.getParameter("currThreshold"):SwtConstants.NO;
			popupScenariosFlag=request.getParameter("popupScenarios")!=null?request.getParameter("popupScenarios"):options[0];
			flashScenariosFlag=request.getParameter("flashScenarios")!=null?request.getParameter("flashScenarios"):options[1];
			emailScenariosFlag=request.getParameter("emailScenarios")!=null?request.getParameter("emailScenarios"):options[2];
			
			hideZeroFlag = request.getParameter("zeroBalances");
			showAlertableFlag = request.getParameter("alertScenarios");
			selectedEntityId = request.getParameter("entityId")!=null?request.getParameter("entityId"):SwtConstants.ALL_LABEL;								
			entityId = SwtUtil.getUserCurrentEntity(request.getSession());
			selectedCategory = request.getParameter("selectedCategory");

			
			//put entityList in request
			putEntityListInReq(request, SwtConstants.STR_TRUE);
			
			// Put scenario currencythreshold, selected alertablescenarios, selectedScenaentityIdrioId , lastRefTime , hidezerocounts and lastRefTime in request
			request.setAttribute("reply_status_ok", SwtConstants.STR_TRUE);
			request.setAttribute("reply_message", "Data fetch OK");
			// Initialising the ScreenOption instance
			screenOption = new ScreenOption();
			// Setting the host id
			screenOption.getId().setHostId(SwtUtil.getCurrentHostId());
			// Setting the user id
			screenOption.getId().setUserId(userId);
			// Setting the screen id for book monitor
			screenOption.getId().setScreenId(screenId);
			// Fetching the refresh rate
			screenOption = screenOptionManager.getRefreshRate(screenOption);
			// Setting the rate in request
			request.setAttribute("autoRefreshRate", new Integer(screenOption
					.getPropertyValue()));
			request.setAttribute("hidezerocounts", hideZeroFlag!=null?hideZeroFlag:SwtConstants.STR_TRUE);
			request.setAttribute("currencythreshold", currencythresholdFlag);
			request.setAttribute("popupScenarios", popupScenariosFlag);
			request.setAttribute("flashScenarios", flashScenariosFlag);
			request.setAttribute("emailScenarios", emailScenariosFlag);
			request.setAttribute("alertablescenarios", showAlertableFlag!=null?showAlertableFlag:SwtConstants.STR_TRUE);
			request.setAttribute("entityId", selectedEntityId);
			request.setAttribute("lastRefTime", SwtUtil.getLastRefTime(request,
			SwtUtil.getUserCurrentEntity(request.getSession())));
			request.setAttribute("displayedDate", (SwtUtil.formatDate(SwtUtil
					.getSystemDatewithoutTime(), SwtUtil
					.getCurrentSystemFormats(request.getSession())
					.getDateFormatValue())));
			request.setAttribute("dateFormat", dateFormat);
			request.setAttribute("currencyFormat", currencyFormat);
			
			String tabName1 = PropertiesFileLoader.getInstance()
					.getPropertiesValue(SwtConstants.ALERT_SUMMARY_TAB1_NAME);
			String tabName2 = PropertiesFileLoader.getInstance()
					.getPropertiesValue(SwtConstants.ALERT_SUMMARY_TAB2_NAME);
			
			// Instantiate the array list
			tabCategoryNamesList = new ArrayList<MonitorTabBean>();
			
			int k = 1;
			for (int h = 1; h <= 2; h++) {
				// Instantiate MonitorTabBean
				MonitorTabBean acctMonitor = new MonitorTabBean();
				// set the tabDate as String
				if(h==1) {
					acctMonitor.setTabNameAsString(tabName1);
					acctMonitor.setCount(0);
					acctMonitor.setTabName(tabName1);
				}else {
					acctMonitor.setTabNameAsString(tabName2);
					acctMonitor.setTabName(tabName2);
					acctMonitor.setCount(0);
					
				}
				acctMonitor.setTabId(h);
				
				if (!SwtUtil.isEmptyOrNull(selectedCategory)
						&& acctMonitor.getTabName().equals(
								selectedCategory)) {
					request.setAttribute("selectedIndexCategory", h - 1);
					request.setAttribute("selectedTabIndexCategory", k - 1);
				} else {
						request.setAttribute("selectedIndexCategory", "0");
						request.setAttribute("selectedTabIndexCategory", "0");
				}
				
				tabCategoryNamesList.add(acctMonitor);
				//increment Business Day counter
				k++;
			}
			
			
			request.setAttribute("tabCategoryDetails", tabCategoryNamesList);
			request.setAttribute("tabSize", tabCategoryNamesList.size());
			/* Set column width with request attribute */
			bindColumnWidthInRequest(request,SwtConstants.STR_FALSE);
			/* Set Column Order in request*/
			bindColumnOrderInRequest(request,entityId);
			
			opTimer.stop("all");
			// set the attribute for opTimes,lastRefTime in request
			request.setAttribute("opTimes", opTimer.getOpTimes());
			log.debug(this.getClass().getName() + " - [summaryScreenInfo] - Exit");
			return "screeninfo";
		} catch (SwtException swtExp) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [summaryScreenInfo] method : - "
					+ swtExp.getMessage());
			request.setAttribute("reply_status_ok", SwtConstants.STR_FALSE);
			request.setAttribute("reply_message", swtExp.getMessage());
			request.setAttribute("reply_location", swtExp.getStackTrace()[0]
					.getClassName()
					+ "."
					+ swtExp.getStackTrace()[0].getMethodName()
					+ ":"
					+ swtExp.getStackTrace()[0].getLineNumber());
			return "dataerror";
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [summaryData] method : - "
					+ exp.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "summaryScreenInfo", ScenarioSummaryAction.class),
					request, "");
			return "fail";
			
		} finally {
			/* null the objects created already. */			
			selectedEntityId = null;
			currencythresholdFlag = null;
			hideZeroFlag = null;
			showAlertableFlag =null;
			entityId= null;

		}
	}
	
	

	/**
	 * This method gets currency maintenance screen display it.
	 * 
	 * @param ActionMapping       mapping
	 * @param ActionForm          form
	 * @param HttpServletRequest  request
	 * @param HttpServletResponse response
	 * @return ActionForward
	 * @throws SwtException
	 */
	public String getScenarioSummaryDetails() throws SwtException {
		// To build error message and log the same
		String errorMessage = null;
		// String variable to hold languageId
		String languageId = null;
		// Retrieves current system formats
		SystemFormats systemFormats = null;
		// variable to hold list of module license details
//		List<AlertInstance> instances = null;
		int payReqId = 0;
		String params = null;
		String facilityGuiId = null;
		String scenarioId=null;
		String selectedStatus = null;
		String fromWorkflow = null;
		AlertInstance instanceCriteria = null;
		HttpServletRequest request = ServletActionContext.getRequest();
    	HttpServletResponse response = ServletActionContext.getResponse();
		try {
			// log debug message
			log.debug(this.getClass().getName() + " - [ display ] - " + "Entry");
			instanceCriteria = new AlertInstance();
			fromWorkflow = request.getParameter("fromWorkFlow");
			selectedStatus = request.getParameter("status");
			
//			params = request.getParameter("sqlParams");
//			facilityGuiId = request.getParameter("facilityGuiId");
//			instanceCriteria.setScenarioId(scenarioId);
//			instanceCriteria.setStatus(selectedStatus);
			
			scenarioId= request.getParameter("scenarioId");
			//to be checked with atef
			Scenario scenario = new Scenario();
			// get scenario details from DAO
				scenario = scenMaintenanceManager
						.getEditableDataDetailList(scenarioId);
			// Initializing array list to hold rules details
//			instances = new ArrayList<AlertInstance>();
			// Get languageId for defaultUser
			languageId = SwtUtil.getCurrentUser(request.getSession()).getLanguage();
			// Retrieves current system formats
			systemFormats = SwtUtil.getCurrentSystemFormats(request.getSession());
//			currencyDetailsVO = currencyMaintenanceManager.getCurrencyDetailList(false);
//			instances = (List<AlertInstance>) scenarioSummaryManager.getInstanceList(instanceCriteria);
			// get the caller Method form request
			String firstLoad = request.getParameter("firstLoad");
			// build XML response
			if("false".equalsIgnoreCase(firstLoad)) {
				return sendDisplayResponsegetScenarioSummary(request, languageId, systemFormats,scenario);
			}else {
				return sendDisplayResponsegetScenarioSummary1(request, languageId, systemFormats);
			}

		} catch (SwtException swtexp) {
			swtexp.printStackTrace();
			log.error(
					this.getClass().getName() + " - Exception Catched in [display] method : - " + swtexp.getMessage());
		} catch (Exception ex) {
			ex.printStackTrace();
			// Build error message
			errorMessage = ex.getStackTrace()[0].getClassName() + "." + ex.getStackTrace()[0].getMethodName() + ":"
					+ ex.getStackTrace()[0].getLineNumber() + " " + ex.getMessage();
			// log error message
			log.error(this.getClass().getName() + " - [display] - SwtException -" + errorMessage);
		} finally {
			// log debug message
			log.debug(this.getClass().getName() + " - [display] - Exit");
			// nullify objects
			errorMessage = null;
			languageId = null;
			systemFormats = null;
		}
		return null;
	}
	
	
	
	
	public String sendDisplayResponsegetScenarioSummary1(HttpServletRequest request, String languageId, SystemFormats systemFormats) throws SwtException {

	SwtResponseConstructor responseConstructor = null;
	SwtXMLWriter xmlWriter = null;
	String userId = null;
	Date mDate = null;
	String componentId = null;
	String width = null;
	String columnOrder = null;
	String hiddenColumns = null;
	int menuAccessId = 2;
	CommonDataManager cdm = null;
	String scenarioTitle=null;
	String rcdScenInstance=null;
	String useGeneric="";
	String facilityName= null;
	String facilityId= null;
	String savedAlertInst=null;
	ArrayList<String> alertInstCollist = new ArrayList<String>();
	/* Variable Declaration for grid data */
	ArrayList<EntityScenarioCount> grid;
	/* Variable Declaration for selected scenario last run */
	String lastrun= null;
	/*Variable Declaration of existing scenario in the list */
	boolean scenarioExist = false;
	String roleId=null;
	String currencythresholdFlag = null;
	String summaryGrouping= null;
	String selectedEntityId = null;
	String selectedSort= null;
	String selectedCurrencyGroup = null;
	String selectedFilter = null;
	String isAlertable = null;
	String hideZeroFlag = null;
	String firstLoad = null;
	String callerMethod = null;
	String selectedTab = null;
	String fromWorkFlow = null;
	String facilityAccess = null;
	String hostId = null;
	/* Variable Declaration of supportAllCurrency */ 
	String supportAllCurrency = SwtConstants.STR_FALSE;
	/* Variable Declaration of supportAllEntity */ 
	String supportAllEntity = SwtConstants.STR_FALSE;
	int scenarioTotalForTab1 = 0;
	int scenarioTotalForTab2 = 0;
	/* Variable Declaration for total Count */
	int totalCount=0;
	String currencyCode= null;
	String selectedDate= null;
	String selectedAccountId= null;
	String selectedMvtId= null;
	String selectedMatchId = null;
	String ilmAccountGroup = null;
	String facilityGuiId = null;
	Double sweepId = null;
	String alertableFlag = null;
	String menuItemId = null;
	try {
		
		// log debug message
		log.debug(this.getClass().getName() + " - [ sendDisplayResponse ] - " + "Entry");
		// Get currentUserId from session
		userId = SwtUtil.getCurrentUser(request.getSession()).getId().getUserId();
		
		// get the selected Entity Id form request
		selectedEntityId = request.getParameter("entityId");
		// get the caller Method form request
		callerMethod = request.getParameter("callerMethod");
		// get the caller Method form request
		firstLoad = request.getParameter("firstLoad");
		// get fromWorkFlow form request if caller application is workFlowMonitor, this
		// variable should be set to true
		fromWorkFlow = request.getParameter("fromWorkFlow") != null ? request.getParameter("fromWorkFlow")
				: SwtConstants.STR_FALSE;
		// get currency threshold Flag form request
		currencythresholdFlag = request.getParameter("currThreshold") != null
				? request.getParameter("currThreshold")
				: SwtConstants.STR_FALSE;
		// get alertable Flag form request
		alertableFlag = request.getParameter("alertScenarios") != null ? request.getParameter("alertScenarios")
				: fromWorkFlow.equals(SwtConstants.STR_TRUE) ? SwtConstants.STR_FALSE : SwtConstants.STR_TRUE;
		// get hideZero Flag form request
		hideZeroFlag = request.getParameter("zeroBalances") != null ? request.getParameter("zeroBalances")
				: SwtConstants.STR_TRUE;
		// get selected Sort form request
		selectedSort = request.getParameter("selectedSort") != null ? request.getParameter("selectedSort")
				: "-1|DESC";
		selectedFilter = request.getParameter("selectedFilter");
		// get selected Currency Group form request
		selectedCurrencyGroup = request.getParameter("selectedCurrencyGroup") != null
				? request.getParameter("selectedCurrencyGroup")
				: "All";
		// get if the selected scenario is alertable
		isAlertable = request.getParameter("isScenarioAlertable") != null
				? request.getParameter("isScenarioAlertable")
				: SwtConstants.NO;
		// get the selected Tab form request
		selectedTab = request.getParameter("selectedTab");

		// get roleId form session
		roleId = ((CommonDataManager) request.getSession().getAttribute(SwtConstants.CDM_BEAN)).getUser()
				.getRoleId();
		// if currency threshold is not sent as parameter then set it to SwtConstants.NO
		currencythresholdFlag = (currencythresholdFlag.equals(SwtConstants.STR_TRUE)) || (currencythresholdFlag.equals(SwtConstants.YES)) ? SwtConstants.YES
				: SwtConstants.NO;	
		
		alertableFlag = (alertableFlag.equals(SwtConstants.STR_TRUE))?SwtConstants.YES:SwtConstants.NO;
		callerMethod = alertableFlag.equals(SwtConstants.NO)?"___":callerMethod;
		
//		fromWorkFlow = request.getParameter("fromWorkFlow");
		boolean fromScreenFacility = "true".equalsIgnoreCase(request.getParameter("fromScreenFacility"));
		
//		menuItemId= fromWorkFlow.equals(SwtConstants.STR_TRUE)?menuItemIdWorkFlow:menuItemId;
		menuItemId= menuItemIdWorkFlow;
		/* Read the user preferences for column width from property value */
		width = SwtUtil.getPropertyValue(request, selectedEntityId,
				menuItemId, "display", "column_width");
		
		facilityId = request.getParameter("facilityId");
		
		if(SwtUtil.isEmptyOrNull(facilityId)) {
			facilityId = SwtConstants.FACILITY_NONE;
			facilityName =SwtConstants.FACILITY_NONE;
			summaryGrouping = SwtConstants.NO;	
		}
		// Init grid data variable
		grid = new ArrayList<EntityScenarioCount>();
		//check facility access
		// get hostId form session
		hostId = CacheManager.getInstance().getHostId();
		if (useGeneric.equals(SwtConstants.YES)) {
			facilityAccess = "0";
			supportAllCurrency = SwtConstants.STR_TRUE;
			supportAllEntity = SwtConstants.STR_TRUE;
		} else {
			if (SwtUtil.isEmptyOrNull(facilityId)
					|| facilityId
							.equalsIgnoreCase(SwtConstants.FACILITY_NONE)) {
				facilityAccess = "2";
			} else
				// Get the access to the facility screen defined by its
				// facility id
				facilityAccess = scenarioSummaryManager.getFacilityAccess(
						hostId, facilityId, roleId);
		}
		
		
		// Instantiate SwtResponseConstructor
		responseConstructor = new SwtResponseConstructor();
		// Get instance of SwtXMLWriter
		xmlWriter = responseConstructor.getXMLWriter();
		// Get component ID
		componentId = "scenarioinstancedetails";

		cdm = (CommonDataManager) request.getSession().getAttribute(SwtConstants.CDM_BEAN);

		LogonDAO logonDAO = (LogonDAO) SwtUtil.getBean("logonDAO");
		MenuItem menuItem = logonDAO.getMenuItem(SwtConstants.WORKFLOW_MONITOR_ID + "", cdm.getUser());

		if (menuItem != null)
			menuAccessId = SwtUtil.getHierarchicalAccessId(menuItem, request);
		alertInstCollist.add("NONE");
		// Adding screen id and current user id as attributes
		xmlWriter.addAttribute(PCMConstant.SCREEN_ID, componentId);
		xmlWriter.addAttribute(SwtConstants.MENU_ACCESS, menuAccessId);
		xmlWriter.addAttribute(PCMConstant.CURRUSER, userId);
		// Adding screen id and current user id as attributes
		xmlWriter.addAttribute(SwtConstants.DATE_FORMAT, SwtUtil.getCurrentDateFormat(request.getSession()));

		xmlWriter.startElement(componentId);
		xmlWriter.clearAttribute();
		responseConstructor.formRequestReply(Boolean.parseBoolean(SwtConstants.STR_TRUE),
				SwtConstants.DATA_FETCH_OK);
		
		/**  other id types combo****/
		ArrayList<SelectInfo> lstSelect = new ArrayList<SelectInfo>();
		// options drop down list
		ArrayList<OptionInfo> lstOptions = new ArrayList<OptionInfo>();
		LabelValueBean row = null;
		Collection types = scenMaintenanceManager.getOtherIdTypes();
		lstOptions.add(new OptionInfo("", "", false));
		Iterator j = types.iterator();
		while (j.hasNext()) {
			row = (LabelValueBean) j.next();
			lstOptions.add(new OptionInfo(row.getValue(), row.getLabel(), false));
		}
		lstSelect.add(new SelectInfo("otherIdTypeCombo", lstOptions));
		responseConstructor.formSelect(lstSelect);
		
		responseConstructor.formGridStart();

		//check if scenario is instance based or not
		if ("Y".equalsIgnoreCase(rcdScenInstance)|| SwtUtil.isEmptyOrNull(rcdScenInstance)) {
		// form column details
		responseConstructor.formColumn(getInstancesDisplayColumns(width, columnOrder, hiddenColumns,request,alertInstCollist));

		} else {
					
		responseConstructor.formColumn(getScenarioColumns(width, columnOrder, hiddenColumns, request,fromWorkFlow));

	   }
		
		responseConstructor.formGridEnd();
		
		
		selectedEntityId = !SwtUtil.isEmptyOrNull(request.getParameter("entityId"))?request.getParameter("entityId"):SwtConstants.ALL_LABEL;
		currencyCode = !SwtUtil.isEmptyOrNull(request.getParameter("currencyCode"))?request.getParameter("currencyCode"):SwtConstants.ALL_LABEL;
		selectedDate = request.getParameter("selectedDate");
		selectedAccountId = request.getParameter("selectedAccountId");
		selectedMvtId = request.getParameter("selectedMvtId");
		selectedMatchId = request.getParameter("selectedMatchId");
		facilityGuiId = request.getParameter("facilityGuiId");
		currencythresholdFlag = !SwtUtil.isEmptyOrNull(request.getParameter("currencythresholdFlag"))?request.getParameter("currencythresholdFlag"):currencythresholdFlag;
		
		
		Date valueDate = null;
		if(!SwtUtil.isEmptyOrNull(selectedDate)) {
			valueDate = SwtUtil.parseDate(selectedDate,  SwtUtil
					.getCurrentSystemFormats(request.getSession())
					.getDateFormatValue());
		}
		
		// get roleId  form session
		userId = SwtUtil.getCurrentUserId(request.getSession());
		AlertInstance alertIntance  = new AlertInstance();
		alertIntance.setSelectedUserId(userId);
		alertIntance.setFaciltityId(facilityGuiId);
		alertIntance.setAccountId(selectedAccountId);
		alertIntance.setHostId(hostId);
		alertIntance.setEntityId(selectedEntityId);
		alertIntance.setCurrencyCode(currencyCode);
		alertIntance.setValueDate(valueDate);
		alertIntance.setCurrencyThresholdFlag(currencythresholdFlag);
		alertIntance.setMovementId(!SwtUtil.isEmptyOrNull(selectedMvtId)?Double.parseDouble(selectedMvtId):null);
		alertIntance.setMatchId(!SwtUtil.isEmptyOrNull(selectedMatchId)?Double.parseDouble(selectedMatchId):null);
		alertIntance.setIlmAccountGroup(ilmAccountGroup);
		alertIntance.setSweepId(sweepId);
		alertIntance.setZeroBalances("true".equalsIgnoreCase(hideZeroFlag));
		alertIntance.setSelectedCurrencyGroup(selectedCurrencyGroup);
		alertIntance.setIsAlertable(alertableFlag);
		alertIntance.setSelectedTab(selectedTab);
		alertIntance.setCallerMethod(callerMethod);
		
		if(fromScreenFacility) {
			alertIntance.setStatus("");
		}
		
		
		AlertTreeVO  dataResult = scenarioSummaryManager.getAlertsScenarioCount(alertIntance );
		totalCount = dataResult.getTotalCount();
		if(!SwtUtil.isEmptyOrNull(selectedTab) && !fromScreenFacility) {
			if("1".equals(selectedTab)) {
				
//				/* Variable Declaration for Scenario Summary Object*/
//				ScenariosSummary scenarioSummary =null;
				/* Variable Declaration for Scenario Summary Object*/
				ScenariosSummary scenarioSummaryTab2 =null;
				scenarioTotalForTab1 = totalCount;
				scenarioSummaryTab2 = scenarioSummaryManager.getScenariosSummaryInfoDetails(roleId,selectedEntityId,currencythresholdFlag,hideZeroFlag,alertableFlag,selectedCurrencyGroup,callerMethod, "2");			
				scenarioTotalForTab2 = scenarioSummaryTab2.getTotalCount();
			}else {
				ScenariosSummary scenarioSummaryTab2 =null;
				scenarioTotalForTab2 = totalCount;
				scenarioSummaryTab2 = scenarioSummaryManager.getScenariosSummaryInfoDetails(roleId,selectedEntityId,currencythresholdFlag,hideZeroFlag,alertableFlag,selectedCurrencyGroup,callerMethod, "1");			
				scenarioTotalForTab1 = scenarioSummaryTab2.getTotalCount();

			}
		}else {
			if("1".equals(selectedTab)) {
				scenarioTotalForTab1 = totalCount;
				scenarioTotalForTab2 = 0;
			}else {
				scenarioTotalForTab1 = 0;
				scenarioTotalForTab2 = totalCount;
			}
		}
		
		
		// forms singleton node
		xmlWriter.startElement(SwtConstants.SINGLETONS);

		// format test date to system format
		mDate = SwtUtil.getTestDateFromParams(SwtUtil.getCurrentHostId());
		// String variable for test date
		String testDate = "";
		// formats the date
		if (mDate != null)
			testDate = SwtUtil.formatDate(mDate, systemFormats.getDateFormatValue());
		responseConstructor.createElement(PCMConstant.CURRENCYPATTERN,
				SwtUtil.getCurrentSystemFormats(request.getSession()).getCurrencyFormat());
		responseConstructor.createElement(PCMConstant.TEST_DATE, testDate);
		responseConstructor.createElement("useGeneric", useGeneric);
		responseConstructor.createElement("scenarioTitle", scenarioTitle); 
		responseConstructor.createElement("rcdScenInstance", rcdScenInstance);
		responseConstructor.createElement("facilityName", facilityName);
		responseConstructor.createElement("facilityId", facilityId);
		responseConstructor.createElement("facilityAccess", facilityAccess);
		responseConstructor.createElement("supportAllCurrency", supportAllCurrency);
		responseConstructor.createElement("supportAllEntity", supportAllEntity);
		responseConstructor.createElement("selectedscenariolastran", lastrun);
		responseConstructor.createElement("total", totalCount);
		responseConstructor.createElement("scenarioTotalForTab1", scenarioTotalForTab1);
		responseConstructor.createElement("scenarioTotalForTab2", scenarioTotalForTab2);
		xmlWriter.endElement(SwtConstants.SINGLETONS);
		
		
		
		
		String xmlTree = createDummyTree(dataResult);	
		xmlWriter.appendText(xmlTree.toString());
		
		
		xmlWriter.endElement(componentId);

		request.setAttribute("data", xmlWriter.getData());
		return "jsondata";
	} catch (SwtException exp) {
		exp.printStackTrace();
		xmlWriter.clearData();
		log.error(this.getClass().getName() + " - SwtException Catched in [sendDisplayResponse] method : - "
				+ exp.getMessage());
		return "fail";
	} catch (Exception ex) {
		ex.printStackTrace();
		xmlWriter.clearData();

		log.error(this.getClass().getName() + " - Exception Catched in [sendDisplayResponse] method : - "
				+ ex.getMessage());
		return "fail";
	} finally {
		// nullify objects
		xmlWriter.destroyWriter();
		responseConstructor = null;
		xmlWriter = null;
		userId = null;
		mDate = null;
		componentId = null;
		width = null;
		columnOrder = null;
		hiddenColumns = null;
		// log debug message
		log.debug(this.getClass().getName() + " - [ sendDisplayResponse ] - Exit");
	}
}
	
	/**
	 * This method forms the xml for displaying the currency list.
	 * 
	 * @param width           - passing default widths
	 * @param columnOrder     - passing default order
	 * @param currencyDetails - passing currency details
	 * @param languageId      - passing languageId
	 * @param systemFormats   - passing system formats date
	 * @return
	 */
	public String sendDisplayResponsegetScenarioSummary(HttpServletRequest request,
			 String languageId, SystemFormats systemFormats, Scenario scenario) throws SwtException {

		long time = System.currentTimeMillis();
		SwtResponseConstructor responseConstructor = null;
		SwtXMLWriter xmlWriter = null;
		String userId = null;
		Date mDate = null;
		String componentId = null;
		String width = null;
		String columnOrder = null;
		String hiddenColumns = null;
		int menuAccessId = 2;
		CommonDataManager cdm = null;
		String scenarioTitle=null;
		String rcdScenInstance=null;
		String useGeneric=null;
		String facilityName= null;
		String facilityId= null;
		String savedAlertInst=null;
		ArrayList<String> alertInstCollist = new ArrayList<String>();
		/* Variable Declaration for grid data */
		ArrayList<EntityScenarioCount> grid;
		/* Variable Declaration for selected scenario last run */
		String lastrun= null;
		/*Variable Declaration of existing scenario in the list */
		boolean scenarioExist = false;
		String roleId=null;
		String currencythresholdFlag = null;
		String summaryGrouping= null;
		String selectedEntityId = null;
		String selectedSort= null;
		String sortDirection= null;
		String selectedCurrencyGroup = null;
		String selectedFilter = null;
		String isAlertable = null;
		String hideZeroFlag = null;
		String firstLoad = null;
		String callerMethod = null;
		String selectedTab = null;
		String fromWorkFlow = null;
		String facilityAccess = null;
		String hostId = null;
		/* Variable Declaration of supportAllCurrency */ 
		String supportAllCurrency = SwtConstants.STR_FALSE;
		/* Variable Declaration of supportAllEntity */ 
		String supportAllEntity = SwtConstants.STR_FALSE;
		int scenarioTotalForTab1 = 0;
		int scenarioTotalForTab2 = 0;
		/* Variable Declaration for total Count */
		int totalCount=0;
		String currencyCode= null;
		String selectedDate= null;
		String selectedAccountId= null;
		String selectedMvtId= null;
		String selectedMatchId = null;
		String ilmAccountGroup = null;
		String facilityGuiId = null;
		Double sweepId = null;
		String selectedStatus = null;
		ArrayList<AlertInstance> messages = null;
		String alertableFlag = null;
		boolean fromScreenFacility = false;
		boolean refreshGridOnly = false;
		String menuItemId=null;
		String statusChanged = null;

		try {
			
			// log debug message
			log.debug(this.getClass().getName() + " - [ sendDisplayResponse ] - " + "Entry");
			// Get currentUserId from session
			userId = SwtUtil.getCurrentUser(request.getSession()).getId().getUserId();
			// get the selected Entity Id form request
			selectedEntityId = request.getParameter("entityId");
			// get the caller Method form request
			callerMethod = request.getParameter("callerMethod");
			
			statusChanged= request.getParameter("statusChanged");
			
			// get the caller Method form request
			refreshGridOnly = request.getParameter("refreshGridOnly") != null && "true".equalsIgnoreCase(request.getParameter("refreshGridOnly"));
			

			// get the caller Method form request
			firstLoad = request.getParameter("firstLoad");
			fromScreenFacility = "true".equalsIgnoreCase(request.getParameter("fromScreenFacility"));
			// get fromWorkFlow form request if caller application is workFlowMonitor, this
			// variable should be set to true
			/*fromWorkFlow = request.getParameter("fromWorkFlow") != null ? request.getParameter("fromWorkFlow")
					: SwtConstants.STR_FALSE;*/
			// get currency threshold Flag form request
			currencythresholdFlag = request.getParameter("currThreshold") != null
					? request.getParameter("currThreshold")
					: SwtConstants.STR_FALSE;
			
			// get hideZero Flag form request
			hideZeroFlag = request.getParameter("zeroBalances") != null ? request.getParameter("zeroBalances")
					: SwtConstants.STR_TRUE;
			// get selected Sort form request
			selectedSort = request.getParameter("selectedSort") != null ? request.getParameter("selectedSort")
					: "-1|DESC";
			selectedFilter = request.getParameter("selectedFilter");
			// get selected Currency Group form request
			selectedCurrencyGroup = request.getParameter("selectedCurrencyGroup") != null
					? request.getParameter("selectedCurrencyGroup")
					: "All";
			// get if the selected scenario is alertable
			isAlertable = request.getParameter("isScenarioAlertable") != null
					? request.getParameter("isScenarioAlertable")
					: SwtConstants.NO;
			// get the selected Tab form request
			selectedTab = request.getParameter("selectedTab");

			// get roleId form session
			roleId = ((CommonDataManager) request.getSession().getAttribute(SwtConstants.CDM_BEAN)).getUser()
					.getRoleId();
			// if currency threshold is not sent as parameter then set it to SwtConstants.NO
			currencythresholdFlag = (currencythresholdFlag.equals(SwtConstants.STR_TRUE)) || (currencythresholdFlag.equals(SwtConstants.YES)) ? SwtConstants.YES
					: SwtConstants.NO;		
			
			fromWorkFlow = request.getParameter("fromWorkFlow");
			// get alertable Flag form request
			alertableFlag = request.getParameter("alertScenarios") != null ? request.getParameter("alertScenarios")
						: fromWorkFlow.equals(SwtConstants.STR_TRUE) ? SwtConstants.STR_FALSE : SwtConstants.STR_TRUE;
			
			alertableFlag = (alertableFlag.equals(SwtConstants.STR_TRUE))?SwtConstants.YES:SwtConstants.NO;
			callerMethod = alertableFlag.equals(SwtConstants.NO)?"___":callerMethod;
			
		
			menuItemId= menuItemIdWorkFlow;
//			menuItemId= fromWorkFlow.equals(SwtConstants.STR_TRUE)?menuItemIdWorkFlow:menuItemId;
			/* Read the user preferences for column width from property value */
			width = SwtUtil.getPropertyValue(request, selectedEntityId,
					menuItemId, "display", "column_width");
			
		    if(scenario != null) {
		    	savedAlertInst= scenario.getAlertInstanceColumn();			
			    if(!SwtUtil.isEmptyOrNull(savedAlertInst) &&  savedAlertInst.startsWith("[") ) {
					JSONArray AlertInstJSONArray = new JSONArray(savedAlertInst);
					for (int i = 0; i < AlertInstJSONArray.length(); i++) {
						alertInstCollist.add((AlertInstJSONArray.getJSONObject(i).getString("content")).replace("\"", ""));            		        		
					}
				}
		    	
			// if the scenario summary grouping is null than set it to
			// "None"
			summaryGrouping = scenario.getSummaryGrouping() != null ? scenario.getSummaryGrouping()
						: SwtConstants.NO;
			
			// record scenario instance variable
			if (scenario.getRecordScenarioInstance() != null)
				rcdScenInstance = scenario.getRecordScenarioInstance();
			else 
				rcdScenInstance = "N";
			
			// if the scenario facility is null than set it to "None"
			if (scenario.getFacility() != null) {
				facilityId = scenario.getFacility().getFacilityid() != null ? scenario.getFacility().getFacilityid()
						: SwtConstants.FACILITY_NONE;
				facilityName = scenario.getFacility().getFacilityname() != null
						? scenario.getFacility().getFacilityname()
						: SwtConstants.FACILITY_NONE;

			} else {
				facilityId = SwtConstants.FACILITY_NONE;
				facilityName = SwtConstants.FACILITY_NONE;
			}
			
			// useGeneric variable
			if (scenario.getUseGenericDisplay() != null) {
				useGeneric = scenario.getUseGenericDisplay();
			}else {
				useGeneric="";//SwtConstants.NO;
			}
			// scenario title variable
			if (scenario.getTitle() != null)
				scenarioTitle = scenario.getTitle();
			
			// Set supportAllCurrency and supportAllEntity variable
			// depending on value form database
			if (scenario.getFacility() != null) {
				supportAllCurrency = scenario.getFacility()
						.getSupportAllCurrency().equals(SwtConstants.YES) ? SwtConstants.STR_TRUE
						: SwtConstants.STR_FALSE;
				supportAllEntity = scenario.getFacility()
						.getSupportAllEntity().equals(SwtConstants.YES) ? SwtConstants.STR_TRUE
						: SwtConstants.STR_FALSE;
			}
			
			
		    }else {
				facilityId = SwtConstants.FACILITY_NONE;
				facilityName =SwtConstants.FACILITY_NONE;
				summaryGrouping = SwtConstants.NO;

		    }
			
			// Init grid data variable
			grid = new ArrayList<EntityScenarioCount>();
			//check facility access
			// get hostId form session
			hostId = CacheManager.getInstance().getHostId();
			if (SwtConstants.YES.equals(useGeneric)) {
				facilityAccess = "0";
				supportAllCurrency = SwtConstants.STR_TRUE;
				supportAllEntity = SwtConstants.STR_TRUE;
			} else {
				if (SwtUtil.isEmptyOrNull(facilityId)
						|| facilityId
								.equalsIgnoreCase(SwtConstants.FACILITY_NONE)) {
					facilityAccess = "2";
				} else
					// Get the access to the facility screen defined by its
					// facility id
					facilityAccess = scenarioSummaryManager.getFacilityAccess(
							hostId, facilityId, roleId);
			}
			
			
			facilityGuiId = request.getParameter("facilityGuiId");
			selectedEntityId = !SwtUtil.isEmptyOrNull(request.getParameter("entityId"))?request.getParameter("entityId"):SwtConstants.ALL_LABEL;
			currencyCode = !SwtUtil.isEmptyOrNull(request.getParameter("currencyCode"))?request.getParameter("currencyCode"):SwtConstants.ALL_LABEL;
			if(!fromWorkFlow.equals(SwtConstants.STR_TRUE)) {
				selectedDate = request.getParameter("selectedDate");
			}else {
				selectedDate = null;
			}
			selectedAccountId = request.getParameter("selectedAccountId");
			selectedMvtId = request.getParameter("selectedMvtId");
			selectedMatchId = request.getParameter("selectedMatchId");
			currencythresholdFlag = !SwtUtil.isEmptyOrNull(request.getParameter("currencythresholdFlag"))?request.getParameter("currencythresholdFlag"):currencythresholdFlag;
			Date valueDate = null;
			if(!SwtUtil.isEmptyOrNull(selectedDate)) {
				valueDate = SwtUtil.parseDate(selectedDate,  SwtUtil
						.getCurrentSystemFormats(request.getSession())
						.getDateFormatValue());
			}			
			String selectedTreeItemQuery = request.getParameter("selectedTreeItemQuery");
			
			selectedStatus = request.getParameter("status");
			String resovedDate = null;
			if(SwtUtil.isEmptyOrNull(selectedStatus) || "R".equalsIgnoreCase(selectedStatus) || "all".equalsIgnoreCase(selectedStatus)) {
				resovedDate = request.getParameter("resolvedOnDate");
			}
			
			// get roleId  form session
			userId = SwtUtil.getCurrentUserId(request.getSession());
			AlertInstance alertIntance  = new AlertInstance();
			alertIntance.setSelectedUserId(userId);
			alertIntance.setFaciltityId(facilityGuiId);
			alertIntance.setAccountId(selectedAccountId);
			alertIntance.setHostId(hostId);
			alertIntance.setEntityId(selectedEntityId);
			alertIntance.setCurrencyCode(currencyCode);
			alertIntance.setValueDate(valueDate);
			alertIntance.setCurrencyThresholdFlag(currencythresholdFlag);
			alertIntance.setMovementId(!SwtUtil.isEmptyOrNull(selectedMvtId)?Double.parseDouble(selectedMvtId):null);
			alertIntance.setMatchId(!SwtUtil.isEmptyOrNull(selectedMatchId)?Double.parseDouble(selectedMatchId):null);
			alertIntance.setIlmAccountGroup(ilmAccountGroup);
			alertIntance.setSweepId(sweepId);
			if(fromScreenFacility && SwtUtil.isEmptyOrNull(selectedStatus)) {
				alertIntance.setStatus("");
			}else
				alertIntance.setStatus(selectedStatus);
			alertIntance.setZeroBalances("true".equalsIgnoreCase(hideZeroFlag));
			alertIntance.setSelectedCurrencyGroup(selectedCurrencyGroup);
			alertIntance.setIsAlertable(alertableFlag);
			alertIntance.setSelectedTab(selectedTab);
			alertIntance.setCallerMethod(callerMethod);
			
			alertIntance.setSelectedAditionalQueryFilter(selectedTreeItemQuery);

			alertIntance.setSortedColumn(selectedSort);
			alertIntance.setSelectedFilter(selectedFilter);
			alertIntance.setSortDirection(sortDirection);
			if(!SwtUtil.isEmptyOrNull(resovedDate)) {
				alertIntance.setResolvedDatetime(SwtUtil.parseDate(resovedDate, SwtUtil.getCurrentDateFormat(request.getSession())));
			}else {
				alertIntance.setResolvedDatetime(null);
			}
			AlertTreeVO  dataResult = null;
			
			if(!refreshGridOnly) {
				try {
				  dataResult = scenarioSummaryManager.getAlertsScenarioCount(alertIntance );
				}catch (Exception e) {
						e.printStackTrace();
						dataResult = new AlertTreeVO();
				}
			}
			if(refreshGridOnly  || (dataResult.getScenarioAlertList() != null && dataResult.getScenarioAlertList().size()>0 && !SwtUtil.isEmptyOrNull(selectedTreeItemQuery) && "Y".equalsIgnoreCase(rcdScenInstance))) {
				if(!SwtConstants.STR_TRUE.equalsIgnoreCase(statusChanged)) {
					try {
						if("Y".equalsIgnoreCase(rcdScenInstance)) {
							String selectedSortDirection  = SwtUtil.isEmptyOrNull(selectedSort)?"DESC":selectedSort.split("\\|")[1];
							String selectedSortColumn  = SwtUtil.isEmptyOrNull(selectedSort)?"id":selectedSort.split("\\|")[0];
							if((SwtUtil.isEmptyOrNull(selectedSortColumn)) || (!SwtUtil.isEmptyOrNull(selectedSortColumn) && ("1".equalsIgnoreCase(selectedSortColumn) || "0".equalsIgnoreCase(selectedSortColumn)|| "-1".equalsIgnoreCase(selectedSortColumn)))) {
								selectedSortColumn = "_id";
							}
							
							if(("_"+SwtConstants.INSTANCE_ID_TAGNAME).equalsIgnoreCase(selectedSortColumn)) {alertIntance.setSortedColumn("ID");}
//							SwtConstants.INSTANCE_ID_TAGNAME                         ).equalsIgnoreCase(selectedSortColumn)) {alertIntance.setSortedColumn("SCENARIO_ID");}
							else if(SwtConstants.INSTANCE_SCENARIO_ID_TAGNAME          .equalsIgnoreCase(selectedSortColumn)) {alertIntance.setSortedColumn("SCENARIO_ID");}
							else if(SwtConstants.INSTANCE_UNIQUE_IDENTIFIER_TAGNAME    .equalsIgnoreCase(selectedSortColumn)) {alertIntance.setSortedColumn("UNIQUE_IDENTIFIER");}
							else if(SwtConstants.INSTANCE_STATUS_TAGNAME               .equalsIgnoreCase(selectedSortColumn)) {alertIntance.setSortedColumn("STATUS");}
							else if(SwtConstants.INSTANCE_RAISED_DATETIME_TAGNAME      .equalsIgnoreCase(selectedSortColumn)) {alertIntance.setSortedColumn("RAISED_DATETIME");}
//							else if(SwtConstants.INSTANCE_RAISED_USER_TAGNAME          .equalsIgnoreCase(selectedSortColumn)) {alertIntance.setSortedColumn("LAST_RAISED_DATETIME");}
							else if(SwtConstants.INSTANCE_LAST_RAISED_DATETIME_TAGNAME .equalsIgnoreCase(selectedSortColumn)) {alertIntance.setSortedColumn("LAST_RAISED_DATETIME");}
//							else if(SwtConstants.INSTANCE_LAST_RAISED_USER_TAGNAME     .equalsIgnoreCase(selectedSortColumn)) {alertIntance.setSortedColumn("SCENARIO_ID");}
							else if(SwtConstants.INSTANCE_RESOLVED_DATETIME_TAGNAME    .equalsIgnoreCase(selectedSortColumn)) {alertIntance.setSortedColumn("RESOLVED_DATETIME");}
							else if(SwtConstants.INSTANCE_RESOLVED_BY_USER_TAGNAME     .equalsIgnoreCase(selectedSortColumn)) {alertIntance.setSortedColumn("RESOLVED_BY_USER");}
							else if(SwtConstants.INSTANCE_EVENTS_LAUNCH_STATUS_TAGNAME .equalsIgnoreCase(selectedSortColumn)) {alertIntance.setSortedColumn("EVENTS_LAUNCH_STATUS");}
							else if(SwtConstants.INSTANCE_HOST_ID_TAGNAME              .equalsIgnoreCase(selectedSortColumn)) {alertIntance.setSortedColumn("HOST_ID");}
							else if(SwtConstants.INSTANCE_ENTITY_ID_TAGNAME            .equalsIgnoreCase(selectedSortColumn)) {alertIntance.setSortedColumn("ENTITY_ID");}
							else if(SwtConstants.INSTANCE_CURRENCY_CODE_TAGNAME        .equalsIgnoreCase(selectedSortColumn)) {alertIntance.setSortedColumn("CURRENCY_CODE");}
							else if(SwtConstants.INSTANCE_ACCOUNT_ID_TAGNAME           .equalsIgnoreCase(selectedSortColumn)) {alertIntance.setSortedColumn("ACCOUNT_ID");}
							else if(SwtConstants.INSTANCE_AMOUNT_TAGNAME               .equalsIgnoreCase(selectedSortColumn)) {alertIntance.setSortedColumn("AMOUNT");}   
							else if(SwtConstants.INSTANCE_SIGN_TAGNAME                 .equalsIgnoreCase(selectedSortColumn)) {alertIntance.setSortedColumn("SIGN");}         
							else if(SwtConstants.INSTANCE_OVER_THRESHOLD_TAGNAME       .equalsIgnoreCase(selectedSortColumn)) {alertIntance.setSortedColumn("OVER_THRESHOLD");}
							else if(SwtConstants.INSTANCE_MOVEMENT_ID_TAGNAME          .equalsIgnoreCase(selectedSortColumn)) {alertIntance.setSortedColumn("MOVEMENT_ID");}
							else if(SwtConstants.INSTANCE_MATCH_ID_TAGNAME             .equalsIgnoreCase(selectedSortColumn)) {alertIntance.setSortedColumn("MATCH_ID");}  
							else if(SwtConstants.INSTANCE_SWEEP_ID_TAGNAME             .equalsIgnoreCase(selectedSortColumn)) {alertIntance.setSortedColumn("SWEEP_ID");}     
							else if(SwtConstants.INSTANCE_PAYMENT_ID_TAGNAME           .equalsIgnoreCase(selectedSortColumn)) {alertIntance.setSortedColumn("PAYMENT_ID");}
							else if(SwtConstants.INSTANCE_ATTRIBUTES_XML_TAGNAME       .equalsIgnoreCase(selectedSortColumn)) {alertIntance.setSortedColumn("ID");} 
							else if(SwtConstants.INSTANCE_VALUE_DATE_TAGNAME           .equalsIgnoreCase(selectedSortColumn)) {alertIntance.setSortedColumn("VALUE_DATE");}
							else if(SwtConstants.INSTANCE_OTHER_ID_TAGNAME             .equalsIgnoreCase(selectedSortColumn)) {alertIntance.setSortedColumn("OTHER_ID");}
							else if(SwtConstants.INSTANCE_OTHER_ID_TYPE_TAGNAME        .equalsIgnoreCase(selectedSortColumn)) {alertIntance.setSortedColumn("OTHER_ID_TYPE");}
							else {alertIntance.setSortedColumn("ID");}
							alertIntance.setSortDirection(selectedSortDirection);
						}else {
							alertIntance.setSortedColumn("ID");
							alertIntance.setSortDirection("DESC");
						}
						messages = (ArrayList<AlertInstance>) scenarioSummaryManager.getInstanceList(alertIntance);
					} catch (Exception e) {
						e.printStackTrace();
						messages = new ArrayList<AlertInstance>();
					}
					
				}else {
					messages = new ArrayList<AlertInstance>();
				}
			}else {
				messages = new ArrayList<AlertInstance>();
			}
			
//			totalCount = dataResult.getTotalCount();
			if(!SwtUtil.isEmptyOrNull(selectedTab) && !fromScreenFacility) {
				if("1".equals(selectedTab)) {
					
//					/* Variable Declaration for Scenario Summary Object*/
//					ScenariosSummary scenarioSummary =null;
					/* Variable Declaration for Scenario Summary Object*/
					ScenariosSummary scenarioSummaryTab2 =null;
					ScenariosSummary scenarioSummaryTab1 =null;
					scenarioTotalForTab1 = totalCount;
					
					scenarioSummaryTab1 = scenarioSummaryManager.getScenariosSummaryInfoDetails(roleId,selectedEntityId,currencythresholdFlag,hideZeroFlag,alertableFlag,selectedCurrencyGroup,callerMethod, "1");
					scenarioSummaryTab2 = scenarioSummaryManager.getScenariosSummaryInfoDetails(roleId,selectedEntityId,currencythresholdFlag,hideZeroFlag,alertableFlag,selectedCurrencyGroup,callerMethod, "2");			
					scenarioTotalForTab2 = scenarioSummaryTab2.getTotalCount();
					totalCount = scenarioTotalForTab1 = scenarioSummaryTab1.getTotalCount();
				}else {
					ScenariosSummary scenarioSummaryTab2 =null;
					ScenariosSummary scenarioSummaryTab1 =null;
					scenarioSummaryTab2 = scenarioSummaryManager.getScenariosSummaryInfoDetails(roleId,selectedEntityId,currencythresholdFlag,hideZeroFlag,alertableFlag,selectedCurrencyGroup,callerMethod, "1");			
					scenarioSummaryTab1 = scenarioSummaryManager.getScenariosSummaryInfoDetails(roleId,selectedEntityId,currencythresholdFlag,hideZeroFlag,alertableFlag,selectedCurrencyGroup,callerMethod, "2");
					scenarioTotalForTab1 = scenarioSummaryTab2.getTotalCount();
					scenarioTotalForTab2 = totalCount = scenarioSummaryTab1.getTotalCount();

				}
			}else {
				if("1".equals(selectedTab)) {
					scenarioTotalForTab1 = totalCount;
					scenarioTotalForTab2 = 0;
				}else {
					scenarioTotalForTab1 = 0;
					scenarioTotalForTab2 = totalCount;
				}
			}
			// Instantiate SwtResponseConstructor
			responseConstructor = new SwtResponseConstructor();
			// Get instance of SwtXMLWriter
			xmlWriter = responseConstructor.getXMLWriter();
			// Get component ID
			componentId = "scenarioinstancedetails";

			cdm = (CommonDataManager) request.getSession().getAttribute(SwtConstants.CDM_BEAN);

			LogonDAO logonDAO = (LogonDAO) SwtUtil.getBean("logonDAO");
			MenuItem menuItem = logonDAO.getMenuItem(SwtConstants.WORKFLOW_MONITOR_ID + "", cdm.getUser());

			if (menuItem != null)
				menuAccessId = SwtUtil.getHierarchicalAccessId(menuItem, request);

			// Adding screen id and current user id as attributes
			xmlWriter.addAttribute(PCMConstant.SCREEN_ID, componentId);
			xmlWriter.addAttribute(SwtConstants.MENU_ACCESS, menuAccessId);
			xmlWriter.addAttribute(PCMConstant.CURRUSER, userId);
			// Adding screen id and current user id as attributes
			xmlWriter.addAttribute(SwtConstants.DATE_FORMAT, SwtUtil.getCurrentDateFormat(request.getSession()));

			xmlWriter.startElement(componentId);
			xmlWriter.clearAttribute();
			responseConstructor.formRequestReply(Boolean.parseBoolean(SwtConstants.STR_TRUE),
					SwtConstants.DATA_FETCH_OK);
			
			/**  other id types combo****/
			ArrayList<SelectInfo> lstSelect = new ArrayList<SelectInfo>();
			// options drop down list
			ArrayList<OptionInfo> lstOptions = new ArrayList<OptionInfo>();
			LabelValueBean row = null;
			Collection types = scenMaintenanceManager.getOtherIdTypes();
			lstOptions.add(new OptionInfo("", "", false));
			Iterator j = types.iterator();
			while (j.hasNext()) {
				row = (LabelValueBean) j.next();
				lstOptions.add(new OptionInfo(row.getValue(), row.getLabel(), false));
			}
			lstSelect.add(new SelectInfo("otherIdTypeCombo", lstOptions));
			responseConstructor.formSelect(lstSelect);

			responseConstructor.formGridStart();
			// form paging details
			responseConstructor.formPaging(null);
			//check if scenario is instance based or not
			if ("Y".equalsIgnoreCase(rcdScenInstance)) {
				
			if(scenario != null) {
				lastrun = scenarioSummaryManager.getSelectedScenarioLastRun(scenario.getId().getScenarioId());
			}
			// form column details
			responseConstructor.formColumn(getInstancesDisplayColumns(width, columnOrder, hiddenColumns,request,alertInstCollist));

			
			
			
			// form rows (records)
			responseConstructor.formRowsStart(messages !=null?messages.size():0);
			String amountFormat = SwtUtil.getCurrentCurrencyFormat(request.getSession());
			// Iterating currency details
			for (Iterator<AlertInstance> it = messages.iterator(); it.hasNext();) {
				// Obtain currency tag from iterator
				AlertInstance instance = it.next();

//				instance.getInputDate() != null?SwtUtil.formatDate(instance.getInputDate(), SwtUtil.getCurrentDateFormat(request.getSession()) + " HH:mm:ss"):"");
				
				responseConstructor.formRowStart();
				
				responseConstructor.createRowElement(SwtConstants.INSTANCE_ID_TAGNAME                      , ""+new DecimalFormat("#").format(instance.getId()));
				responseConstructor.createRowElement(SwtConstants.INSTANCE_SCENARIO_ID_TAGNAME             , instance.getScenarioId());
				responseConstructor.createRowElement(SwtConstants.INSTANCE_UNIQUE_IDENTIFIER_TAGNAME       , instance.getUniqueIdentifier());
				responseConstructor.createRowElement(SwtConstants.INSTANCE_STATUS_TAGNAME                  , !SwtUtil.isEmptyOrNull(instance.getStatus())?getStatusDesc(instance.getStatus()):"");
				responseConstructor.createRowElement(SwtConstants.INSTANCE_RAISED_DATETIME_TAGNAME         , instance.getRaisedDatetime() != null?SwtUtil.formatDate(instance.getRaisedDatetime(), SwtUtil.getCurrentDateFormat(request.getSession()) + " HH:mm:ss"):"");
				responseConstructor.createRowElement(SwtConstants.INSTANCE_LAST_RAISED_DATETIME_TAGNAME    , instance.getLastRaisedDatetime() != null?SwtUtil.formatDate(instance.getLastRaisedDatetime(), SwtUtil.getCurrentDateFormat(request.getSession()) + " HH:mm:ss"):"");
				responseConstructor.createRowElement(SwtConstants.INSTANCE_RESOLVED_DATETIME_TAGNAME       , instance.getResolvedDatetime() != null?SwtUtil.formatDate(instance.getResolvedDatetime(), SwtUtil.getCurrentDateFormat(request.getSession()) + " HH:mm:ss"):"");
				responseConstructor.createRowElement(SwtConstants.INSTANCE_RESOLVED_BY_USER_TAGNAME        , instance.getResolvedByUser());
				responseConstructor.createRowElement(SwtConstants.INSTANCE_RAISED_USER_TAGNAME             , instance.getRaisedUser());
				responseConstructor.createRowElement(SwtConstants.INSTANCE_LAST_RAISED_USER_TAGNAME        , instance.getLastRaisedUser());
				responseConstructor.createRowElement(SwtConstants.INSTANCE_EVENTS_LAUNCH_STATUS_TAGNAME    , getEventStatusDesc(instance.getEventsLaunchStatus()));
				responseConstructor.createRowElement(SwtConstants.INSTANCE_HOST_ID_TAGNAME                 , instance.getHostId());
				responseConstructor.createRowElement(SwtConstants.INSTANCE_ENTITY_ID_TAGNAME               , instance.getEntityId());
				responseConstructor.createRowElement(SwtConstants.INSTANCE_CURRENCY_CODE_TAGNAME           , instance.getCurrencyCode());
				responseConstructor.createRowElement(SwtConstants.INSTANCE_ACCOUNT_ID_TAGNAME              , instance.getAccountId());
				responseConstructor.createRowElement(SwtConstants.INSTANCE_AMOUNT_TAGNAME                 ,  instance.getCurrencyCode() != null?SwtUtil.formatCurrency(instance.getCurrencyCode(), instance.getAmount() ,amountFormat):"");
				responseConstructor.createRowElement(SwtConstants.INSTANCE_SIGN_TAGNAME                    , instance.getSign());
				responseConstructor.createRowElement(SwtConstants.INSTANCE_OVER_THRESHOLD_TAGNAME          , instance.getOverThreshold());
				responseConstructor.createRowElement(SwtConstants.INSTANCE_MOVEMENT_ID_TAGNAME             , instance.getMovementId() != null ?""+ new DecimalFormat("#").format(instance.getMovementId()):"");
				responseConstructor.createRowElement(SwtConstants.INSTANCE_MATCH_ID_TAGNAME                , instance.getMatchId() != null ?""+ new DecimalFormat("#").format(instance.getMatchId()):"");
				responseConstructor.createRowElement(SwtConstants.INSTANCE_SWEEP_ID_TAGNAME                , instance.getSweepId() != null ?""+ new DecimalFormat("#").format(instance.getSweepId()):"");
				responseConstructor.createRowElement(SwtConstants.INSTANCE_PAYMENT_ID_TAGNAME              , instance.getPaymentId() != null ?""+ new DecimalFormat("#").format(instance.getPaymentId()):"");
				responseConstructor.createRowElement(SwtConstants.INSTANCE_ATTRIBUTES_XML_TAGNAME          , "xml", "", false, true);

				responseConstructor.createRowElement(SwtConstants.INSTANCE_VALUE_DATE_TAGNAME              , instance.getValueDate() != null?SwtUtil.formatDate(instance.getValueDate(), SwtUtil.getCurrentDateFormat(request.getSession())):"");
				responseConstructor.createRowElement(SwtConstants.INSTANCE_OTHER_ID_TAGNAME                , instance.getOtherId());
				responseConstructor.createRowElement(SwtConstants.INSTANCE_OTHER_ID_TYPE_TAGNAME           , instance.getOtherIdType());
				responseConstructor.formRowEnd();
			}

			responseConstructor.formRowsEnd();
			
		} else {

			// Test if selected scenario id is not empty or null than get grid
			// data from DAO
			if (scenario != null && !SwtUtil.isEmptyOrNull(scenario.getId().getScenarioId())) {// && scenarioExist) {
				if ("N".equalsIgnoreCase(rcdScenInstance)) {
					grid = scenarioSummaryManager.getScenarioCountByEntituCurrency(scenario.getId().getScenarioId(), roleId,
							currencythresholdFlag, summaryGrouping, selectedEntityId, selectedSort, selectedCurrencyGroup,
							isAlertable, selectedFilter);
					
//					System.err.println(scenario.getId().getScenarioId()+"  "+ roleId + "  "+
//							currencyth7esholdFlag + "  "+summaryGrouping +  "  "+selectedEntityId + "  "+selectedSort + "  "+selectedCurrencyGroup+
//							"  "+isAlertable+ "  "+selectedFilter);
				}
				lastrun = scenarioSummaryManager.getSelectedScenarioLastRun(scenario.getId().getScenarioId());
			}
			responseConstructor.formColumn(getScenarioColumns(width, columnOrder, hiddenColumns, request,fromWorkFlow));
			responseConstructor.formRowsStart(grid.size());
			for (Iterator<EntityScenarioCount> it = grid.iterator(); it.hasNext();) {
				  HashMap<String, String> expandAttributes = new HashMap<String, String>();
				  HashMap<String, String> entityAttributes = new HashMap<String, String>();

				// Obtain currency tag from iterator
				EntityScenarioCount entityScenCount = it.next();
				
				expandAttributes.put("clickable", "false");
				expandAttributes.put("opened",entityScenCount.isOpen() ? "true" : "false");
				entityAttributes.put("clickable", "false");
				entityAttributes.put("isopen",entityScenCount.isOpen() ? "true" : "false");
				entityAttributes.put("haschildren",entityScenCount.isHasChildren() ? "true" : "false");

				responseConstructor.formRowStart(entityScenCount.isVisible() ? true : false);
				responseConstructor.createRowElement("expand", entityScenCount.isHasChildren() ? "Y": "N",expandAttributes);
				responseConstructor.createRowElement("entity", entityScenCount.getEntity(),entityAttributes);
				responseConstructor.createRowElement("ccy", entityScenCount.getCcy());
				responseConstructor.createRowElement("count" , String.valueOf(entityScenCount.getCount()) , "count", false, true);
				responseConstructor.formRowEnd();
			}
			responseConstructor.formRowsEnd();


		}
			
			responseConstructor.formGridEnd();
			// forms singleton node
			xmlWriter.startElement(SwtConstants.SINGLETONS);

			// format test date to system format
			mDate = SwtUtil.getTestDateFromParams(SwtUtil.getCurrentHostId());
			// String variable for test date
			String testDate = "";
			// formats the date
			if (mDate != null)
				testDate = SwtUtil.formatDate(mDate, systemFormats.getDateFormatValue());
			responseConstructor.createElement(PCMConstant.CURRENCYPATTERN,
					SwtUtil.getCurrentSystemFormats(request.getSession()).getCurrencyFormat());
			responseConstructor.createElement(PCMConstant.TEST_DATE, testDate);
			responseConstructor.createElement("useGeneric", useGeneric);
			responseConstructor.createElement("scenarioTitle", scenarioTitle); 
			responseConstructor.createElement("rcdScenInstance", rcdScenInstance);
			responseConstructor.createElement("facilityName", facilityName);
			responseConstructor.createElement("facilityId", facilityId);
			responseConstructor.createElement("facilityAccess", facilityAccess);
			responseConstructor.createElement("supportAllCurrency", supportAllCurrency);
			responseConstructor.createElement("supportAllEntity", supportAllEntity);
			responseConstructor.createElement("selectedscenariolastran", lastrun);
			responseConstructor.createElement("selectedscenario",scenario!=null? scenario.getId().getScenarioId():"");
			responseConstructor.createElement("total", totalCount);
			responseConstructor.createElement("scenarioTotalForTab1", scenarioTotalForTab1);
			responseConstructor.createElement("scenarioTotalForTab2", scenarioTotalForTab2);
			xmlWriter.endElement(SwtConstants.SINGLETONS);
			
			
			if(!refreshGridOnly) {
				String xmlTree = createDummyTree(dataResult);
				xmlWriter.appendText(xmlTree.toString());
			}
			
			xmlWriter.endElement(componentId);

			request.setAttribute("data", xmlWriter.getData());
			return "jsondata";
		} catch (SwtException exp) {
			exp.printStackTrace();
			xmlWriter.clearData();			
			log.error(this.getClass().getName() + " - SwtException Catched in [sendDisplayResponse] method : - "
					+ exp.getMessage());
			return "fail";
		} catch (Exception ex) {
			ex.printStackTrace();
//			xmlWriter.clearData();

			log.error(this.getClass().getName() + " - Exception Catched in [sendDisplayResponse] method : - "
					+ ex.getMessage());
			return "fail";
		} finally {
			// nullify objects
			xmlWriter.destroyWriter();
			responseConstructor = null;
			xmlWriter = null;
			userId = null;
			mDate = null;
			componentId = null;
			width = null;
			columnOrder = null;
			hiddenColumns = null;
			// log debug message
			log.debug(this.getClass().getName() + " - [ sendDisplayResponse ] - Exit");
		}
	}
	
	private String getStatusDesc(String status) {
		String statusCode = null;
		
		switch (status) {
		case "A":
			statusCode = "Active";
			break;
		case "R":
			statusCode = "Resolved";
			break;
		case "P":
			statusCode = "Pending";
			break;
		case "O":
			statusCode = "Overdue";
			break;
		default:
			break;
		}

		return statusCode;

	}
	
	private String getEventStatusDesc(String status) {
		String statusCode = null;
		
		switch (status) {
		case "N":
			statusCode = "No events to launch";
			break;
		case "W":
			statusCode = "Waiting to Launch";
			break;
		case "L":
			statusCode = "Launched";
			break;
		case "F":
			statusCode = "Failed";
			break;
		default:
			break;
		}

		return statusCode;

	}
	
	
	/**
	 * This method gets currency maintenance screen display it.
	 * 
	 * @param ActionMapping       mapping
	 * @param ActionForm          form
	 * @param HttpServletRequest  request
	 * @param HttpServletResponse response
	 * @return ActionForward
	 * @throws SwtException
	 */
	public String getInstanceList() throws SwtException {
		// To build error message and log the same
		String errorMessage = null;
		// String variable to hold languageId
		String languageId = null;
		// Retrieves current system formats
		SystemFormats systemFormats = null;
		// variable to hold list of module license details
		List<AlertInstance> instances = null;
		int payReqId = 0;
		String params = null;
		String facilityId = null;
		AlertInstance instanceCriteria = null;
		String scenarioId = null;
		HttpServletRequest request = ServletActionContext.getRequest();
    	HttpServletResponse response = ServletActionContext.getResponse();
		try {

			params = request.getParameter("sqlParams");
			facilityId = request.getParameter("facilityId");
			scenarioId= request.getParameter("scenarioId");
			instanceCriteria.setScenarioId(scenarioId);
			
//			System.err.println("facilityId=="+facilityId);
//			System.err.println("params=="+params );
			// log debug message
			log.debug(this.getClass().getName() + " - [ display ] - " + "Entry");
			// Initializing array list to hold rules details
			instances = new ArrayList<AlertInstance>();
			// Get languageId for defaultUser
			languageId = SwtUtil.getCurrentUser(request.getSession()).getLanguage();
			// Retrieves current system formats
			systemFormats = SwtUtil.getCurrentSystemFormats(request.getSession());
//			currencyDetailsVO = currencyMaintenanceManager.getCurrencyDetailList(false);
			instances = (List<AlertInstance>) scenarioSummaryManager.getInstanceList(instanceCriteria);
			//to be checked with atef
			Scenario scenario = new Scenario();
			// get scenario details from DAO
				scenario = scenMaintenanceManager
						.getEditableDataDetailList(scenarioId);

			// build XML response
			return sendDisplayResponseInstances(request, instances, languageId, systemFormats, scenario);

		} catch (SwtException swtexp) {
			swtexp.printStackTrace();
			log.error(
					this.getClass().getName() + " - Exception Catched in [display] method : - " + swtexp.getMessage());
		} catch (Exception ex) {
			ex.printStackTrace();
			// Build error message
			errorMessage = ex.getStackTrace()[0].getClassName() + "." + ex.getStackTrace()[0].getMethodName() + ":"
					+ ex.getStackTrace()[0].getLineNumber() + " " + ex.getMessage();
			// log error message
			log.error(this.getClass().getName() + " - [display] - SwtException -" + errorMessage);
		} finally {
			// log debug message
			log.debug(this.getClass().getName() + " - [display] - Exit");
			// nullify objects
			errorMessage = null;
			languageId = null;
			systemFormats = null;
		}
		return null;
	}
	
	
	
	/**
	 * This method forms the xml for displaying the currency list.
	 * 
	 * @param width           - passing default widths
	 * @param columnOrder     - passing default order
	 * @param currencyDetails - passing currency details
	 * @param languageId      - passing languageId
	 * @param systemFormats   - passing system formats date
	 * @return
	 */
	public String sendDisplayResponseInstances(HttpServletRequest request,
			List<AlertInstance> messages, String languageId, SystemFormats systemFormats, Scenario scenario) throws SwtException {

		SwtResponseConstructor responseConstructor = null;
		SwtXMLWriter xmlWriter = null;
		String userId = null;
		Date mDate = null;
		String componentId = null;
		String width = null;
		String columnOrder = null;
		String hiddenColumns = null;
		int menuAccessId = 2;
		CommonDataManager cdm = null;
		String savedAlertInst=null;
		ArrayList<String> alertInstCollist = new ArrayList<String>();
		try {
			// log debug message
			log.debug(this.getClass().getName() + " - [ sendDisplayResponse ] - " + "Entry");
			// Get currentUserId from session
			userId = SwtUtil.getCurrentUser(request.getSession()).getId().getUserId();
			savedAlertInst= scenario.getAlertInstanceColumn();
			
		    if(savedAlertInst.startsWith("[") ) {
			JSONArray AlertInstJSONArray = new JSONArray(savedAlertInst);
			for (int i = 0; i < AlertInstJSONArray.length(); i++) {
				alertInstCollist.add((AlertInstJSONArray.getJSONObject(i).getString("content")).replace("\"", ""));            		        		
			}
			}
		    
			// Instantiate SwtResponseConstructor
			responseConstructor = new SwtResponseConstructor();
			// Get instance of SwtXMLWriter
			xmlWriter = responseConstructor.getXMLWriter();
			// Get component ID
			componentId = "messageSummary";

			cdm = (CommonDataManager) request.getSession().getAttribute(SwtConstants.CDM_BEAN);

			LogonDAO logonDAO = (LogonDAO) SwtUtil.getBean("logonDAO");
			MenuItem menuItem = logonDAO.getMenuItem(SwtConstants.MENU_ITEM_PAYMENT_DISPLAY + "", cdm.getUser());

			if (menuItem != null)
				menuAccessId = SwtUtil.getHierarchicalAccessId(menuItem, request);

			// Adding screen id and current user id as attributes
			xmlWriter.addAttribute(PCMConstant.SCREEN_ID, componentId);
			xmlWriter.addAttribute(SwtConstants.MENU_ACCESS, menuAccessId);
			xmlWriter.addAttribute(PCMConstant.CURRUSER, userId);
			// Adding screen id and current user id as attributes
			xmlWriter.addAttribute(SwtConstants.DATE_FORMAT, SwtUtil.getCurrentDateFormat(request.getSession()));

			xmlWriter.startElement(PCMConstant.MESSAGE_OUT);
			xmlWriter.clearAttribute();
			responseConstructor.formRequestReply(Boolean.parseBoolean(SwtConstants.STR_TRUE),
					SwtConstants.DATA_FETCH_OK);

			responseConstructor.formGridStart();
			// form paging details
			responseConstructor.formPaging(null);
			// form column details
			responseConstructor.formColumn(getInstancesDisplayColumns(width, columnOrder, hiddenColumns,request,alertInstCollist));

			// form rows (records)
			responseConstructor.formRowsStart(messages.size());
			String amountFormat = SwtUtil.getCurrentCurrencyFormat(request.getSession());
			// Iterating currency details
			for (Iterator<AlertInstance> it = messages.iterator(); it.hasNext();) {
				// Obtain currency tag from iterator
				AlertInstance instance = it.next();

//				instance.getInputDate() != null?SwtUtil.formatDate(instance.getInputDate(), SwtUtil.getCurrentDateFormat(request.getSession()) + " HH:mm:ss"):"");
				
				responseConstructor.formRowStart();
				
				responseConstructor.createRowElement(SwtConstants.INSTANCE_ID_TAGNAME                      , ""+new DecimalFormat("#").format(instance.getId()));
				responseConstructor.createRowElement(SwtConstants.INSTANCE_SCENARIO_ID_TAGNAME             , instance.getScenarioId());
				responseConstructor.createRowElement(SwtConstants.INSTANCE_UNIQUE_IDENTIFIER_TAGNAME       , instance.getUniqueIdentifier());
				responseConstructor.createRowElement(SwtConstants.INSTANCE_STATUS_TAGNAME                  , instance.getStatus());
				responseConstructor.createRowElement(SwtConstants.INSTANCE_RAISED_DATETIME_TAGNAME         , instance.getRaisedDatetime() != null?SwtUtil.formatDate(instance.getRaisedDatetime(), SwtUtil.getCurrentDateFormat(request.getSession()) + " HH:mm:ss"):"");
				responseConstructor.createRowElement(SwtConstants.INSTANCE_LAST_RAISED_DATETIME_TAGNAME    , instance.getLastRaisedDatetime() != null?SwtUtil.formatDate(instance.getLastRaisedDatetime(), SwtUtil.getCurrentDateFormat(request.getSession()) + " HH:mm:ss"):"");
				responseConstructor.createRowElement(SwtConstants.INSTANCE_RESOLVED_DATETIME_TAGNAME       , instance.getResolvedDatetime() != null?SwtUtil.formatDate(instance.getResolvedDatetime(), SwtUtil.getCurrentDateFormat(request.getSession()) + " HH:mm:ss"):"");
				responseConstructor.createRowElement(SwtConstants.INSTANCE_RESOLVED_BY_USER_TAGNAME        , instance.getResolvedByUser());
				responseConstructor.createRowElement(SwtConstants.INSTANCE_EVENTS_LAUNCH_STATUS_TAGNAME    , instance.getEventsLaunchStatus());
				responseConstructor.createRowElement(SwtConstants.INSTANCE_HOST_ID_TAGNAME                 , instance.getHostId());
				responseConstructor.createRowElement(SwtConstants.INSTANCE_ENTITY_ID_TAGNAME               , instance.getEntityId());
				responseConstructor.createRowElement(SwtConstants.INSTANCE_CURRENCY_CODE_TAGNAME           , instance.getCurrencyCode());
				responseConstructor.createRowElement(SwtConstants.INSTANCE_ACCOUNT_ID_TAGNAME              , instance.getAccountId());
				responseConstructor.createRowElement(SwtConstants.INSTANCE_AMOUNT_TAGNAME                 ,  instance.getCurrencyCode() != null?SwtUtil.formatCurrency(instance.getCurrencyCode(), instance.getAmount() ,amountFormat):"");
				responseConstructor.createRowElement(SwtConstants.INSTANCE_SIGN_TAGNAME                    , instance.getSign());
				responseConstructor.createRowElement(SwtConstants.INSTANCE_OVER_THRESHOLD_TAGNAME          , instance.getOverThreshold());
				responseConstructor.createRowElement(SwtConstants.INSTANCE_MOVEMENT_ID_TAGNAME             , instance.getMovementId() != null ?""+ new DecimalFormat("#").format(instance.getMovementId()):"");
				responseConstructor.createRowElement(SwtConstants.INSTANCE_MATCH_ID_TAGNAME                , instance.getMatchId() != null ?""+ new DecimalFormat("#").format(instance.getMatchId()):"");
				responseConstructor.createRowElement(SwtConstants.INSTANCE_SWEEP_ID_TAGNAME                , instance.getSweepId() != null ?""+ new DecimalFormat("#").format(instance.getSweepId()):"");
				responseConstructor.createRowElement(SwtConstants.INSTANCE_PAYMENT_ID_TAGNAME              , instance.getPaymentId() != null ?""+ new DecimalFormat("#").format(instance.getPaymentId()):"");
				responseConstructor.createRowElement(SwtConstants.INSTANCE_ATTRIBUTES_XML_TAGNAME          , instance.getAttributesXml());
				responseConstructor.createRowElement(SwtConstants.INSTANCE_VALUE_DATE_TAGNAME              , instance.getValueDate() != null?SwtUtil.formatDate(instance.getValueDate(), SwtUtil.getCurrentDateFormat(request.getSession())):"");
				responseConstructor.createRowElement(SwtConstants.INSTANCE_OTHER_ID_TAGNAME                , instance.getOtherId());
				
				
				
				
				responseConstructor.formRowEnd();
			}

			responseConstructor.formRowsEnd();
			responseConstructor.formGridEnd();
			// forms singleton node
			xmlWriter.startElement(SwtConstants.SINGLETONS);

			// format test date to system format
			mDate = SwtUtil.getTestDateFromParams(SwtUtil.getCurrentHostId());
			// String variable for test date
			String testDate = "";
			// formats the date
			if (mDate != null)
				testDate = SwtUtil.formatDate(mDate, systemFormats.getDateFormatValue());
			responseConstructor.createElement(PCMConstant.CURRENCYPATTERN,
					SwtUtil.getCurrentSystemFormats(request.getSession()).getCurrencyFormat());
			responseConstructor.createElement(PCMConstant.TEST_DATE, testDate);
			xmlWriter.endElement(SwtConstants.SINGLETONS);
			xmlWriter.endElement(PCMConstant.MESSAGE_OUT);

			request.setAttribute("data", xmlWriter.getData());
			return "jsondata";
		} catch (SwtException exp) {
			xmlWriter.clearData();
			log.error(this.getClass().getName() + " - SwtException Catched in [sendDisplayResponse] method : - "
					+ exp.getMessage());
			return "fail";
		} catch (Exception ex) {
			xmlWriter.clearData();

			log.error(this.getClass().getName() + " - Exception Catched in [sendDisplayResponse] method : - "
					+ ex.getMessage());
			return "fail";
		} finally {
			// nullify objects
			xmlWriter.destroyWriter();
			responseConstructor = null;
			xmlWriter = null;
			userId = null;
			mDate = null;
			componentId = null;
			width = null;
			columnOrder = null;
			hiddenColumns = null;
			// log debug message
			log.debug(this.getClass().getName() + " - [ sendDisplayResponse ] - Exit");
		}
	}
	
	
	/**
	 * This method creates sample column details
	 * 
	 * @param width       - passing columns widths
	 * @param columnOrder - passing default column order
	 * @return List 0f columns
	 * @throws SwtException
	 */
	private List<ColumnInfo> getInstancesDisplayColumns(String width, String columnOrder, String hiddenColumns, HttpServletRequest request,ArrayList<String> alertInstColList)
			throws SwtException {
		// Array list to hold column order
		ArrayList<String> orders = null;
		// String array variable to hold column order property
		String[] columnOrderProp = null;
		// Iterator variable to hold column order
		Iterator<String> columnOrderItr = null;
		// Hash map to hold column width
		LinkedHashMap<String, String> widths = null;
		// String array variable to hold width property
		String[] columnWidthProperty = null;
		// List for column info
		List<ColumnInfo> lstColumns = null;
		/* Hash map to hold column hidden_Columns */
		HashMap<String, Boolean> hiddenColumnsMap = new LinkedHashMap<String, Boolean>();
		/* Array list to hold hidden Column array */
		ArrayList<String> lstHiddenColunms = null;
		/* String array variable to hold hidden columns property */
		String[] hiddenColumnsProp = null;
		try {
			// log debug message
			log.debug(this.getClass().getName() + " - [ getInstancesDisplayColumns ] - Entry");
			// Condition to check width is null

			if (SwtUtil.isEmptyOrNull(width)) {
				// default width for columns
				width = SwtConstants.INSTANCE_ID_TAGNAME                        + "=50,"
						+ SwtConstants.INSTANCE_SCENARIO_ID_TAGNAME             + "=150,"
						+ SwtConstants.INSTANCE_UNIQUE_IDENTIFIER_TAGNAME       + "=150,"
						+ SwtConstants.INSTANCE_STATUS_TAGNAME                  + "=150,"
						+ SwtConstants.INSTANCE_RAISED_DATETIME_TAGNAME         + "=150,"
						+ SwtConstants.INSTANCE_RAISED_USER_TAGNAME             + "=150,"
						+ SwtConstants.INSTANCE_LAST_RAISED_DATETIME_TAGNAME    + "=150,"
						+ SwtConstants.INSTANCE_LAST_RAISED_USER_TAGNAME        + "=150,"
						+ SwtConstants.INSTANCE_RESOLVED_DATETIME_TAGNAME       + "=150,"
						+ SwtConstants.INSTANCE_RESOLVED_BY_USER_TAGNAME        + "=150,"
						+ SwtConstants.INSTANCE_EVENTS_LAUNCH_STATUS_TAGNAME    + "=150,"
						+ SwtConstants.INSTANCE_HOST_ID_TAGNAME                 + "=150,"
						+ SwtConstants.INSTANCE_ENTITY_ID_TAGNAME               + "=150,"
						+ SwtConstants.INSTANCE_CURRENCY_CODE_TAGNAME           + "=150,"
						+ SwtConstants.INSTANCE_ACCOUNT_ID_TAGNAME              + "=150,"
						+ SwtConstants.INSTANCE_AMOUNT_TAGNAME                  + "=150,"
						+ SwtConstants.INSTANCE_SIGN_TAGNAME                    + "=150,"
						+ SwtConstants.INSTANCE_OVER_THRESHOLD_TAGNAME          + "=150,"
						+ SwtConstants.INSTANCE_MOVEMENT_ID_TAGNAME             + "=150,"
						+ SwtConstants.INSTANCE_MATCH_ID_TAGNAME                + "=150,"
						+ SwtConstants.INSTANCE_SWEEP_ID_TAGNAME                + "=150,"
						+ SwtConstants.INSTANCE_PAYMENT_ID_TAGNAME              + "=150,"
						+ SwtConstants.INSTANCE_ATTRIBUTES_XML_TAGNAME          + "=150,"
						+ SwtConstants.INSTANCE_VALUE_DATE_TAGNAME              + "=150,"
						+ SwtConstants.INSTANCE_OTHER_ID_TAGNAME                + "=150,"
				        + SwtConstants.INSTANCE_OTHER_ID_TYPE_TAGNAME           + "=150";
			}

			// Obtain width for each column
			columnWidthProperty = width.split(",");

			// Loop to insert each column in hash map
			widths = new LinkedHashMap<String, String>();
			for (int i = 0; i < columnWidthProperty.length; i++) {
				// Condition to check index of = is -1
				if (columnWidthProperty[i].indexOf("=") != -1) {
					String[] propval = columnWidthProperty[i].split("=");
					widths.put(propval[0], propval[1].contains(".")?propval[1].split("\\.")[0]:propval[1]);
				}
			}

			// Condition to check column order is null or empty
			if (SwtUtil.isEmptyOrNull(columnOrder)) {
				// Default values for column order
				columnOrder = SwtConstants.INSTANCE_ID_TAGNAME                      + ","
						+ SwtConstants.INSTANCE_SCENARIO_ID_TAGNAME             + ","
						+ SwtConstants.INSTANCE_UNIQUE_IDENTIFIER_TAGNAME       + ","
						+ SwtConstants.INSTANCE_STATUS_TAGNAME                  + ","
						+ SwtConstants.INSTANCE_RAISED_DATETIME_TAGNAME         + ","
						+ SwtConstants.INSTANCE_RAISED_USER_TAGNAME             + ","
						+ SwtConstants.INSTANCE_LAST_RAISED_DATETIME_TAGNAME    + ","
						+ SwtConstants.INSTANCE_LAST_RAISED_USER_TAGNAME        + ","
						+ SwtConstants.INSTANCE_RESOLVED_DATETIME_TAGNAME       + ","
						+ SwtConstants.INSTANCE_RESOLVED_BY_USER_TAGNAME        + ","
						+ SwtConstants.INSTANCE_EVENTS_LAUNCH_STATUS_TAGNAME    + ","
						+ SwtConstants.INSTANCE_HOST_ID_TAGNAME                 + ","
						+ SwtConstants.INSTANCE_ENTITY_ID_TAGNAME               + ","
						+ SwtConstants.INSTANCE_CURRENCY_CODE_TAGNAME           + ","
						+ SwtConstants.INSTANCE_ACCOUNT_ID_TAGNAME              + ","
						+ SwtConstants.INSTANCE_AMOUNT_TAGNAME                  + ","
						+ SwtConstants.INSTANCE_SIGN_TAGNAME                    + ","
						+ SwtConstants.INSTANCE_OVER_THRESHOLD_TAGNAME          + ","
						+ SwtConstants.INSTANCE_MOVEMENT_ID_TAGNAME             + ","
						+ SwtConstants.INSTANCE_MATCH_ID_TAGNAME                + ","
						+ SwtConstants.INSTANCE_SWEEP_ID_TAGNAME                + ","
						+ SwtConstants.INSTANCE_PAYMENT_ID_TAGNAME              + ","
						+ SwtConstants.INSTANCE_ATTRIBUTES_XML_TAGNAME          + ","
						+ SwtConstants.INSTANCE_VALUE_DATE_TAGNAME              + ","
						+ SwtConstants.INSTANCE_OTHER_ID_TAGNAME                + ","
				        + SwtConstants.INSTANCE_OTHER_ID_TYPE_TAGNAME           ;
			}
			orders = new ArrayList<String>();
			// Split the columns using , and save in string array
			columnOrderProp = columnOrder.split(",");

			// Loop to enter column order in array list
			for (int i = 0; i < columnOrderProp.length; i++) {
				// Adding the Column values to ArrayList
				orders.add(columnOrderProp[i]);
				hiddenColumnsMap.put(columnOrderProp[i], true);
			}

			/* Condition to check column hidden is null or empty */
			if (!SwtUtil.isEmptyOrNull(hiddenColumns)) {
				lstHiddenColunms = new ArrayList<String>();
				/* Split the hidden columns , and save in string array */
				hiddenColumnsProp = hiddenColumns.split(",");

				for (int i = 0; i < hiddenColumnsProp.length; i++) {
					/* Adding the Column values to ArrayList */
					lstHiddenColunms.add(hiddenColumnsProp[i]);
				}

				Iterator<String> listKeys = hiddenColumnsMap.keySet().iterator();
				for (int i = 0; i < hiddenColumnsMap.size(); i++) {
					String columnKey = listKeys.next();
					// boolean found = false;
					for (int j = 0; j < lstHiddenColunms.size(); j++) {
						if (columnKey.equals(lstHiddenColunms.get(j))) {
							hiddenColumnsMap.put(columnKey, false);
							break;
						}
					}
				}
			}

			columnOrderItr = orders.iterator();
			lstColumns = new ArrayList<ColumnInfo>();

			while (columnOrderItr.hasNext()) {
				String order = (String) columnOrderItr.next();
				ColumnInfo tmpColumnInfo = null;
				// CURRENCY CODE type column
				if (order.equals(SwtConstants.INSTANCE_ID_TAGNAME)) {
					int idWidth = 50;
					if(widths.get(SwtConstants.INSTANCE_ID_TAGNAME)!=null) {
						idWidth = Integer.parseInt(widths.get(SwtConstants.INSTANCE_ID_TAGNAME));
					}else if(widths.get("_id") != null) {
						idWidth = Integer.parseInt(widths.get("_id"));
					}
					tmpColumnInfo =(new ColumnInfo(SwtUtil.getMessage(SwtConstants.INSTANCE_ID_HEADER, request),
							SwtConstants.INSTANCE_ID_TAGNAME, PCMConstant.COLUMN_TYPE_DATE, 0,
							idWidth, false, true,
							hiddenColumnsMap.get(SwtConstants.INSTANCE_ID_TAGNAME)));
				tmpColumnInfo.setHeaderTooltip(SwtUtil.getMessage(SwtConstants.INSTANCE_ID_TOOLTIP, request));
				lstColumns.add(tmpColumnInfo);
				}
				// Ordinal column
				if (order.equals(SwtConstants.INSTANCE_SCENARIO_ID_TAGNAME)) {
					tmpColumnInfo =(new ColumnInfo(SwtUtil.getMessage(SwtConstants.INSTANCE_SCENARIO_ID_HEADER, request),
							SwtConstants.INSTANCE_SCENARIO_ID_TAGNAME, PCMConstant.COLUMN_TYPE_STRING, 1,
							Integer.parseInt(widths.get(SwtConstants.INSTANCE_SCENARIO_ID_TAGNAME)!=null?widths.get(SwtConstants.INSTANCE_SCENARIO_ID_TAGNAME):"150"), true, true,
							hiddenColumnsMap.get(SwtConstants.INSTANCE_SCENARIO_ID_TAGNAME)));
				tmpColumnInfo.setHeaderTooltip(SwtUtil.getMessage(SwtConstants.INSTANCE_SCENARIO_ID_TOOLTIP, request));
				lstColumns.add(tmpColumnInfo);
				}
				// Ordinal column
				if (order.equals(SwtConstants.INSTANCE_UNIQUE_IDENTIFIER_TAGNAME)) {
					tmpColumnInfo =(new ColumnInfo(SwtUtil.getMessage(SwtConstants.INSTANCE_UNIQUE_IDENTIFIER_HEADER, request),
							SwtConstants.INSTANCE_UNIQUE_IDENTIFIER_TAGNAME, PCMConstant.COLUMN_TYPE_STRING, 2,
							Integer.parseInt(widths.get(SwtConstants.INSTANCE_UNIQUE_IDENTIFIER_TAGNAME)!=null?widths.get(SwtConstants.INSTANCE_UNIQUE_IDENTIFIER_TAGNAME):"150"), true, true,
							hiddenColumnsMap.get(SwtConstants.INSTANCE_UNIQUE_IDENTIFIER_TAGNAME)));
				
				tmpColumnInfo.setHeaderTooltip(SwtUtil.getMessage(SwtConstants.INSTANCE_UNIQUE_IDENTIFIER_TOOLTIP, request));
				lstColumns.add(tmpColumnInfo);
				}
				// Ordinal column
				if (order.equals(SwtConstants.INSTANCE_STATUS_TAGNAME)) {
					tmpColumnInfo =(new ColumnInfo(SwtUtil.getMessage(SwtConstants.INSTANCE_STATUS_HEADER, request),
							SwtConstants.INSTANCE_STATUS_TAGNAME, PCMConstant.COLUMN_TYPE_STRING, 3,
							Integer.parseInt(widths.get(SwtConstants.INSTANCE_STATUS_TAGNAME)!=null?widths.get(SwtConstants.INSTANCE_STATUS_TAGNAME):"150"), true, true,
							hiddenColumnsMap.get(SwtConstants.INSTANCE_STATUS_TAGNAME)));
				
				tmpColumnInfo.setHeaderTooltip(SwtUtil.getMessage(SwtConstants.INSTANCE_STATUS_TOOLTIP, request));
				lstColumns.add(tmpColumnInfo);
				}
				// Ordinal column
				if (order.equals(SwtConstants.INSTANCE_RAISED_DATETIME_TAGNAME)) {
					tmpColumnInfo =(new ColumnInfo(SwtUtil.getMessage(SwtConstants.INSTANCE_RAISED_DATETIME_HEADER, request),
							SwtConstants.INSTANCE_RAISED_DATETIME_TAGNAME, PCMConstant.COLUMN_TYPE_STRING, 4,
							Integer.parseInt(widths.get(SwtConstants.INSTANCE_RAISED_DATETIME_TAGNAME)!=null?widths.get(SwtConstants.INSTANCE_RAISED_DATETIME_TAGNAME):"150"), true, true,
							hiddenColumnsMap.get(SwtConstants.INSTANCE_RAISED_DATETIME_TAGNAME)));
				tmpColumnInfo.setHeaderTooltip(SwtUtil.getMessage(SwtConstants.INSTANCE_RAISED_DATETIME_TOOLTIP, request));
				lstColumns.add(tmpColumnInfo);
				
				}
				// Ordinal column
				if (order.equals(SwtConstants.INSTANCE_RAISED_USER_TAGNAME)) {
					tmpColumnInfo =(new ColumnInfo(SwtUtil.getMessage(SwtConstants.INSTANCE_RAISED_USER_HEADER, request),
							SwtConstants.INSTANCE_RAISED_USER_TAGNAME, PCMConstant.COLUMN_TYPE_STRING, 4,
							Integer.parseInt(widths.get(SwtConstants.INSTANCE_RAISED_USER_TAGNAME)!=null?widths.get(SwtConstants.INSTANCE_RAISED_USER_TAGNAME):"150"), true, true,
							hiddenColumnsMap.get(SwtConstants.INSTANCE_RAISED_USER_TAGNAME)));
				tmpColumnInfo.setHeaderTooltip(SwtUtil.getMessage(SwtConstants.INSTANCE_RAISED_USER_TOOLTIP, request));
				lstColumns.add(tmpColumnInfo);
				
				}
				// Ordinal column
				if (order.equals(SwtConstants.INSTANCE_LAST_RAISED_DATETIME_TAGNAME)) {
					tmpColumnInfo =(new ColumnInfo(SwtUtil.getMessage(SwtConstants.INSTANCE_LAST_RAISED_DATETIME_HEADER, request),
							SwtConstants.INSTANCE_LAST_RAISED_DATETIME_TAGNAME, PCMConstant.COLUMN_TYPE_STRING, 5,
							Integer.parseInt(widths.get(SwtConstants.INSTANCE_LAST_RAISED_DATETIME_TAGNAME)!=null?widths.get(SwtConstants.INSTANCE_LAST_RAISED_DATETIME_TAGNAME):"150"), true, true,
							hiddenColumnsMap.get(SwtConstants.INSTANCE_LAST_RAISED_DATETIME_TAGNAME)));
				tmpColumnInfo.setHeaderTooltip(SwtUtil.getMessage(SwtConstants.INSTANCE_LAST_RAISED_DATETIME_TOOLTIP, request));
				lstColumns.add(tmpColumnInfo);
				}
				// Ordinal column
				if (order.equals(SwtConstants.INSTANCE_LAST_RAISED_USER_TAGNAME)) {
					tmpColumnInfo =(new ColumnInfo(SwtUtil.getMessage(SwtConstants.INSTANCE_LAST_RAISED_USER_HEADER, request),
							SwtConstants.INSTANCE_LAST_RAISED_USER_TAGNAME, PCMConstant.COLUMN_TYPE_STRING, 5,
							Integer.parseInt(widths.get(SwtConstants.INSTANCE_LAST_RAISED_USER_TAGNAME)!=null?widths.get(SwtConstants.INSTANCE_LAST_RAISED_USER_TAGNAME):"150"), true, true,
							hiddenColumnsMap.get(SwtConstants.INSTANCE_LAST_RAISED_USER_TAGNAME)));
				tmpColumnInfo.setHeaderTooltip(SwtUtil.getMessage(SwtConstants.INSTANCE_LAST_RAISED_USER_TOOLTIP, request));
				lstColumns.add(tmpColumnInfo);
				}
				// Ordinal column
				if (order.equals(SwtConstants.INSTANCE_RESOLVED_DATETIME_TAGNAME)) {
					tmpColumnInfo =(new ColumnInfo(SwtUtil.getMessage(SwtConstants.INSTANCE_RESOLVED_DATETIME_HEADER, request),
							SwtConstants.INSTANCE_RESOLVED_DATETIME_TAGNAME, PCMConstant.COLUMN_TYPE_STRING, 6,
							Integer.parseInt(widths.get(SwtConstants.INSTANCE_RESOLVED_DATETIME_TAGNAME)!=null?widths.get(SwtConstants.INSTANCE_RESOLVED_DATETIME_TAGNAME):"150"), true, true,
							hiddenColumnsMap.get(SwtConstants.INSTANCE_RESOLVED_DATETIME_TAGNAME)));				
				tmpColumnInfo.setHeaderTooltip(SwtUtil.getMessage(SwtConstants.INSTANCE_RESOLVED_DATETIME_TOOLTIP, request));
				lstColumns.add(tmpColumnInfo);
				}
				// Ordinal column
				if (order.equals(SwtConstants.INSTANCE_RESOLVED_BY_USER_TAGNAME)) {
					tmpColumnInfo =(new ColumnInfo(SwtUtil.getMessage(SwtConstants.INSTANCE_RESOLVED_BY_USER_HEADER, request),
							SwtConstants.INSTANCE_RESOLVED_BY_USER_TAGNAME, PCMConstant.COLUMN_TYPE_STRING, 7,
							Integer.parseInt(widths.get(SwtConstants.INSTANCE_RESOLVED_BY_USER_TAGNAME)!=null?widths.get(SwtConstants.INSTANCE_RESOLVED_BY_USER_TAGNAME):"150"), true, true,
							hiddenColumnsMap.get(SwtConstants.INSTANCE_RESOLVED_BY_USER_TAGNAME)));
				tmpColumnInfo.setHeaderTooltip(SwtUtil.getMessage(SwtConstants.INSTANCE_RESOLVED_BY_USER_TOOLTIP, request));
				lstColumns.add(tmpColumnInfo);
				}
				// Ordinal column
				if (order.equals(SwtConstants.INSTANCE_EVENTS_LAUNCH_STATUS_TAGNAME)) {
					tmpColumnInfo =(new ColumnInfo(SwtUtil.getMessage(SwtConstants.INSTANCE_EVENTS_LAUNCH_STATUS_HEADER, request),
							SwtConstants.INSTANCE_EVENTS_LAUNCH_STATUS_TAGNAME, PCMConstant.COLUMN_TYPE_STRING, 8,
							Integer.parseInt(widths.get(SwtConstants.INSTANCE_EVENTS_LAUNCH_STATUS_TAGNAME)!=null?widths.get(SwtConstants.INSTANCE_EVENTS_LAUNCH_STATUS_TAGNAME):"150"), true, true,
							hiddenColumnsMap.get(SwtConstants.INSTANCE_EVENTS_LAUNCH_STATUS_TAGNAME)));
				tmpColumnInfo.setHeaderTooltip(SwtUtil.getMessage(SwtConstants.INSTANCE_EVENTS_LAUNCH_STATUS_TOOLTIP, request));
				lstColumns.add(tmpColumnInfo);
				}
				// Ordinal column
				if (order.equals(SwtConstants.INSTANCE_HOST_ID_TAGNAME)  && alertInstColList.contains(getColumn(SwtConstants.INSTANCE_HOST_ID_TAGNAME))) {
					tmpColumnInfo =(new ColumnInfo(SwtUtil.getMessage(SwtConstants.INSTANCE_HOST_ID_HEADER, request),
							SwtConstants.INSTANCE_HOST_ID_TAGNAME, PCMConstant.COLUMN_TYPE_STRING, 9,
							Integer.parseInt(widths.get(SwtConstants.INSTANCE_HOST_ID_TAGNAME)!=null?widths.get(SwtConstants.INSTANCE_HOST_ID_TAGNAME):"150"), true, true,
							hiddenColumnsMap.get(SwtConstants.INSTANCE_HOST_ID_TAGNAME)));
				tmpColumnInfo.setHeaderTooltip(SwtUtil.getMessage(SwtConstants.INSTANCE_HOST_ID_TAGNAME, request));
				lstColumns.add(tmpColumnInfo);
				}
				// Ordinal column
				if (order.equals(SwtConstants.INSTANCE_ENTITY_ID_TAGNAME)  && alertInstColList.contains(getColumn(SwtConstants.INSTANCE_ENTITY_ID_TAGNAME))) {
					tmpColumnInfo =(new ColumnInfo(SwtUtil.getMessage(SwtConstants.INSTANCE_ENTITY_ID_HEADER, request),
							SwtConstants.INSTANCE_ENTITY_ID_TAGNAME, PCMConstant.COLUMN_TYPE_STRING, 10,
							Integer.parseInt(widths.get(SwtConstants.INSTANCE_ENTITY_ID_TAGNAME)!=null?widths.get(SwtConstants.INSTANCE_ENTITY_ID_TAGNAME):"150"), true, true,
							hiddenColumnsMap.get(SwtConstants.INSTANCE_ENTITY_ID_TAGNAME)));
				tmpColumnInfo.setHeaderTooltip(SwtUtil.getMessage(SwtConstants.INSTANCE_ENTITY_ID_TOOLTIP, request));
				lstColumns.add(tmpColumnInfo);
				}
				// Ordinal column
				if (order.equals(SwtConstants.INSTANCE_CURRENCY_CODE_TAGNAME)  && alertInstColList.contains(getColumn(SwtConstants.INSTANCE_CURRENCY_CODE_TAGNAME))) {
					tmpColumnInfo =(new ColumnInfo(SwtUtil.getMessage(SwtConstants.INSTANCE_CURRENCY_CODE_HEADER, request),
							SwtConstants.INSTANCE_CURRENCY_CODE_TAGNAME, PCMConstant.COLUMN_TYPE_STRING, 11,
							Integer.parseInt(widths.get(SwtConstants.INSTANCE_CURRENCY_CODE_TAGNAME)!=null?widths.get(SwtConstants.INSTANCE_CURRENCY_CODE_TAGNAME):"150"), true, true,
							hiddenColumnsMap.get(SwtConstants.INSTANCE_CURRENCY_CODE_TAGNAME)));
				tmpColumnInfo.setHeaderTooltip(SwtUtil.getMessage(SwtConstants.INSTANCE_CURRENCY_CODE_TOOLTIP, request));
				lstColumns.add(tmpColumnInfo);
				}
				// Ordinal column
				if (order.equals(SwtConstants.INSTANCE_ACCOUNT_ID_TAGNAME) && alertInstColList.contains(getColumn(SwtConstants.INSTANCE_ACCOUNT_ID_TAGNAME))) {
					tmpColumnInfo =(new ColumnInfo(SwtUtil.getMessage(SwtConstants.INSTANCE_ACCOUNT_ID_HEADER, request),
							SwtConstants.INSTANCE_ACCOUNT_ID_TAGNAME, PCMConstant.COLUMN_TYPE_STRING, 12,
							Integer.parseInt(widths.get(SwtConstants.INSTANCE_ACCOUNT_ID_TAGNAME)!=null?widths.get(SwtConstants.INSTANCE_ACCOUNT_ID_TAGNAME):"150"), true, true,
							hiddenColumnsMap.get(SwtConstants.INSTANCE_ACCOUNT_ID_TAGNAME)));
				tmpColumnInfo.setHeaderTooltip(SwtUtil.getMessage(SwtConstants.INSTANCE_ACCOUNT_ID_TOOLTIP, request));
				lstColumns.add(tmpColumnInfo);
				}
				// Ordinal column
				if (order.equals(SwtConstants.INSTANCE_AMOUNT_TAGNAME)  && alertInstColList.contains(getColumn(SwtConstants.INSTANCE_AMOUNT_TAGNAME))) {
					tmpColumnInfo =(new ColumnInfo(SwtUtil.getMessage(SwtConstants.INSTANCE_AMOUNT_HEADER, request),
							SwtConstants.INSTANCE_AMOUNT_TAGNAME, PCMConstant.COLUMN_TYPE_STRING, 13,
							Integer.parseInt(widths.get(SwtConstants.INSTANCE_AMOUNT_TAGNAME)!=null?widths.get(SwtConstants.INSTANCE_AMOUNT_TAGNAME):"150"), true, true,
							hiddenColumnsMap.get(SwtConstants.INSTANCE_AMOUNT_TAGNAME)));
				tmpColumnInfo.setHeaderTooltip(SwtUtil.getMessage(SwtConstants.INSTANCE_AMOUNT_TOOLTIP, request));
				lstColumns.add(tmpColumnInfo);
				}
				// Ordinal column
				if (order.equals(SwtConstants.INSTANCE_SIGN_TAGNAME)  && alertInstColList.contains(getColumn(SwtConstants.INSTANCE_SIGN_TAGNAME))) {
					tmpColumnInfo =(new ColumnInfo(SwtUtil.getMessage(SwtConstants.INSTANCE_SIGN_HEADER, request),
							SwtConstants.INSTANCE_SIGN_TAGNAME, PCMConstant.COLUMN_TYPE_STRING, 14,
							Integer.parseInt(widths.get(SwtConstants.INSTANCE_SIGN_TAGNAME)!=null?widths.get(SwtConstants.INSTANCE_SIGN_TAGNAME):"150"), true, true,
							hiddenColumnsMap.get(SwtConstants.INSTANCE_SIGN_TAGNAME)));
				tmpColumnInfo.setHeaderTooltip(SwtUtil.getMessage(SwtConstants.INSTANCE_SIGN_TOOLTIP, request));
				lstColumns.add(tmpColumnInfo);
				}
				// Ordinal column
				if (order.equals(SwtConstants.INSTANCE_OVER_THRESHOLD_TAGNAME)) {
					tmpColumnInfo =(new ColumnInfo(SwtUtil.getMessage(SwtConstants.INSTANCE_OVER_THRESHOLD_HEADER, request),
							SwtConstants.INSTANCE_OVER_THRESHOLD_TAGNAME, PCMConstant.COLUMN_TYPE_STRING, 15,
							Integer.parseInt(widths.get(SwtConstants.INSTANCE_OVER_THRESHOLD_TAGNAME)!=null?widths.get(SwtConstants.INSTANCE_OVER_THRESHOLD_TAGNAME):"150"), true, true,
							hiddenColumnsMap.get(SwtConstants.INSTANCE_OVER_THRESHOLD_TAGNAME)));
				tmpColumnInfo.setHeaderTooltip(SwtUtil.getMessage(SwtConstants.INSTANCE_OVER_THRESHOLD_TOOLTIP, request));
				lstColumns.add(tmpColumnInfo);
				}
				// Ordinal column
				if (order.equals(SwtConstants.INSTANCE_MOVEMENT_ID_TAGNAME)  && alertInstColList.contains(getColumn(SwtConstants.INSTANCE_MOVEMENT_ID_TAGNAME))) {
					tmpColumnInfo =(new ColumnInfo(SwtUtil.getMessage(SwtConstants.INSTANCE_MOVEMENT_ID_HEADER, request),
							SwtConstants.INSTANCE_MOVEMENT_ID_TAGNAME, PCMConstant.COLUMN_TYPE_STRING, 16,
							Integer.parseInt(widths.get(SwtConstants.INSTANCE_MOVEMENT_ID_TAGNAME)!=null?widths.get(SwtConstants.INSTANCE_MOVEMENT_ID_TAGNAME):"150"), true, true,
							hiddenColumnsMap.get(SwtConstants.INSTANCE_MOVEMENT_ID_TAGNAME)));
				tmpColumnInfo.setHeaderTooltip(SwtUtil.getMessage(SwtConstants.INSTANCE_MOVEMENT_ID_TOOLTIP, request));
				lstColumns.add(tmpColumnInfo);
				}
				// Ordinal column
				if (order.equals(SwtConstants.INSTANCE_MATCH_ID_TAGNAME) && alertInstColList.contains(getColumn(SwtConstants.INSTANCE_MATCH_ID_TAGNAME))) {
					tmpColumnInfo =(new ColumnInfo(SwtUtil.getMessage(SwtConstants.INSTANCE_MATCH_ID_HEADER, request),
							SwtConstants.INSTANCE_MATCH_ID_TAGNAME, PCMConstant.COLUMN_TYPE_STRING, 17,
							Integer.parseInt(widths.get(SwtConstants.INSTANCE_MATCH_ID_TAGNAME)!=null?widths.get(SwtConstants.INSTANCE_MATCH_ID_TAGNAME):"150"), true, true,
							hiddenColumnsMap.get(SwtConstants.INSTANCE_MATCH_ID_TAGNAME)));
				tmpColumnInfo.setHeaderTooltip(SwtUtil.getMessage(SwtConstants.INSTANCE_MATCH_ID_TOOLTIP, request));
				lstColumns.add(tmpColumnInfo);
				}
				// Ordinal column
				if (order.equals(SwtConstants.INSTANCE_SWEEP_ID_TAGNAME) && alertInstColList.contains(getColumn(SwtConstants.INSTANCE_SWEEP_ID_TAGNAME))) {
					tmpColumnInfo =(new ColumnInfo(SwtUtil.getMessage(SwtConstants.INSTANCE_SWEEP_ID_HEADER, request),
							SwtConstants.INSTANCE_SWEEP_ID_TAGNAME, PCMConstant.COLUMN_TYPE_STRING, 18,
							Integer.parseInt(widths.get(SwtConstants.INSTANCE_SWEEP_ID_TAGNAME)!=null?widths.get(SwtConstants.INSTANCE_SWEEP_ID_TAGNAME):"150"), true, true,
							hiddenColumnsMap.get(SwtConstants.INSTANCE_SWEEP_ID_TAGNAME)));
				tmpColumnInfo.setHeaderTooltip(SwtUtil.getMessage(SwtConstants.INSTANCE_SWEEP_ID_TOOLTIP, request));
				lstColumns.add(tmpColumnInfo);
			    }
				// Ordinal column
				if (order.equals(SwtConstants.INSTANCE_PAYMENT_ID_TAGNAME)  && alertInstColList.contains(getColumn(SwtConstants.INSTANCE_PAYMENT_ID_TAGNAME))) {
					tmpColumnInfo =(new ColumnInfo(SwtUtil.getMessage(SwtConstants.INSTANCE_PAYMENT_ID_HEADER, request),
							SwtConstants.INSTANCE_PAYMENT_ID_TAGNAME, PCMConstant.COLUMN_TYPE_STRING, 19,
							Integer.parseInt(widths.get(SwtConstants.INSTANCE_PAYMENT_ID_TAGNAME)!=null?widths.get(SwtConstants.INSTANCE_PAYMENT_ID_TAGNAME):"150"), true, true,
							hiddenColumnsMap.get(SwtConstants.INSTANCE_PAYMENT_ID_TAGNAME)));
				tmpColumnInfo.setHeaderTooltip(SwtUtil.getMessage(SwtConstants.INSTANCE_PAYMENT_ID_TOOLTIP, request));
				lstColumns.add(tmpColumnInfo);
				}
				// Ordinal column
				if (order.equals(SwtConstants.INSTANCE_ATTRIBUTES_XML_TAGNAME)) {   //&& alertInstColList.contains(getColumn(SwtConstants.INSTANCE_ATTRIBUTES_XML_TAGNAME))){
					tmpColumnInfo =(new ColumnInfo(SwtUtil.getMessage(SwtConstants.INSTANCE_ATTRIBUTES_XML_HEADER, request),
							SwtConstants.INSTANCE_ATTRIBUTES_XML_TAGNAME, PCMConstant.COLUMN_TYPE_STRING, 20,
							Integer.parseInt(widths.get(SwtConstants.INSTANCE_ATTRIBUTES_XML_TAGNAME)!=null?widths.get(SwtConstants.INSTANCE_ATTRIBUTES_XML_TAGNAME):"150"), true, true,
							hiddenColumnsMap.get(SwtConstants.INSTANCE_ATTRIBUTES_XML_TAGNAME)));
				tmpColumnInfo.setHeaderTooltip(SwtUtil.getMessage(SwtConstants.INSTANCE_ATTRIBUTES_XML_TOOLTIP, request));
				lstColumns.add(tmpColumnInfo);
				}
				// Ordinal column
				if (order.equals(SwtConstants.INSTANCE_VALUE_DATE_TAGNAME)   && alertInstColList.contains(getColumn(SwtConstants.INSTANCE_VALUE_DATE_TAGNAME))){
					tmpColumnInfo =(new ColumnInfo(SwtUtil.getMessage(SwtConstants.INSTANCE_VALUE_DATE_HEADER, request),
							SwtConstants.INSTANCE_VALUE_DATE_TAGNAME, PCMConstant.COLUMN_TYPE_STRING, 21,
							Integer.parseInt(widths.get(SwtConstants.INSTANCE_VALUE_DATE_TAGNAME)!=null?widths.get(SwtConstants.INSTANCE_VALUE_DATE_TAGNAME):"150"), true, true,
							hiddenColumnsMap.get(SwtConstants.INSTANCE_VALUE_DATE_TAGNAME)));
				tmpColumnInfo.setHeaderTooltip(SwtUtil.getMessage(SwtConstants.INSTANCE_VALUE_DATE_TOOLTIP, request));
				lstColumns.add(tmpColumnInfo);
				}
				// Ordinal column
				if (order.equals(SwtConstants.INSTANCE_OTHER_ID_TAGNAME) && isOtherIdExists(alertInstColList)) {
					tmpColumnInfo =(new ColumnInfo(SwtUtil.getMessage(SwtConstants.INSTANCE_OTHER_ID_HEADER, request),
							SwtConstants.INSTANCE_OTHER_ID_TAGNAME, PCMConstant.COLUMN_TYPE_STRING, 22,
							Integer.parseInt(widths.get(SwtConstants.INSTANCE_OTHER_ID_TAGNAME)!=null?widths.get(SwtConstants.INSTANCE_OTHER_ID_TAGNAME):"150"), true, true,
							hiddenColumnsMap.get(SwtConstants.INSTANCE_OTHER_ID_TAGNAME)));
				tmpColumnInfo.setHeaderTooltip(SwtUtil.getMessage(SwtConstants.INSTANCE_OTHER_ID_TOOLTIP, request));
				lstColumns.add(tmpColumnInfo);
				}
				// Ordinal column
				if (order.equals(SwtConstants.INSTANCE_OTHER_ID_TYPE_TAGNAME) && isOtherIdExists(alertInstColList)) {
					tmpColumnInfo =(new ColumnInfo(SwtUtil.getMessage(SwtConstants.INSTANCE_OTHER_ID_TYPE_HEADER, request),
							SwtConstants.INSTANCE_OTHER_ID_TYPE_TAGNAME, PCMConstant.COLUMN_TYPE_STRING, 23,
							Integer.parseInt(widths.get(SwtConstants.INSTANCE_OTHER_ID_TYPE_TAGNAME)!=null?widths.get(SwtConstants.INSTANCE_OTHER_ID_TYPE_TAGNAME):"150"), true, true, false));
				tmpColumnInfo.setHeaderTooltip(SwtUtil.getMessage(SwtConstants.INSTANCE_OTHER_ID_TOOLTIP, request));
				lstColumns.add(tmpColumnInfo);
				}

			}

		} catch (Exception ex) {
			// log error message
			log.error(this.getClass().getName()
					+ " - Exception Catched in [getInstancesDisplayColumns] method : - " + ex.getMessage());

			throw SwtErrorHandler.getInstance().handleException(ex, "getInstancesDisplayColumns",
					this.getClass());

		} finally {
			// Nullify Objects
			orders = null;
			columnOrderProp = null;
			columnOrderItr = null;
			widths = null;
			columnWidthProperty = null;
			// log debug message
			log.debug(this.getClass().getName() + " - [ getInstancesDisplayColumns ] - Exit");
		}
		// return XML columns
		return lstColumns;
	}
	
	
	private String getColumn(String column) {
		String value = null;

		switch (column) {
		case "hostId":
			value = "HOST_ID";
			break;
		case "entityId":
			value = "ENTITY_ID";
			break;
		case "currencyCode":
			value = "CURRENCY_CODE";
			break;
		case "accountId":
			value = "ACCOUNT_ID";
			break;
		case "amount":
			value = "AMOUNT";
			break;
		case "sign":
			value = "SIGN";
			break;
		case "matchId":
			value = "MATCH_ID";
			break;
		case "sweepId":
			value = "SWEEP_ID";
			break;
		case "paymentId":
			value = "PAYMENT_ID";
			break;
		case "valueDate":
			value = "VALUE_DATE";
			break;
		case "movementId":
			value = "MOVEMENT_ID";
			break;
		default:
			break;
		}

		return value;

	}
	
	
	private boolean isOtherIdExists(ArrayList<String> alertInstColList) {

		String[] columnList = { "HOST_ID", "ENTITY_ID", "CURRENCY_CODE", "ACCOUNT_ID", "AMOUNT", "SIGN", "MATCH_ID",
				"SWEEP_ID", "PAYMENT_ID", "VALUE_DATE", "MOVEMENT_ID" };
		boolean flag = false;
		boolean contains = false;
		for (int i = 0; i < alertInstColList.size(); i++) {
			contains = !Arrays.stream(columnList).anyMatch(alertInstColList.get(i)::equals);
			if (contains == true)
				flag = true;

		}
		return flag;
	}
	
	
	/**
	 * This method creates sample column details
	 * 
	 * @param width       - passing columns widths
	 * @param columnOrder - passing default column order
	 * @return List 0f columns
	 * @throws SwtException
	 */
	private List<ColumnInfo> getScenarioColumns(String width, String columnOrder, String hiddenColumns, HttpServletRequest request, String fromWorkFlow)
			throws SwtException {
		// Array list to hold column order
		ArrayList<String> orders = null;
		// String array variable to hold column order property
		String[] columnOrderProp = null;
		// Iterator variable to hold column order
		Iterator<String> columnOrderItr = null;
		// Hash map to hold column width
		LinkedHashMap<String, String> widths = null;
		// String array variable to hold width property
		String[] columnWidthProperty = null;
		// List for column info
		List<ColumnInfo> lstColumns = null;
		/* Hash map to hold column hidden_Columns */
		HashMap<String, Boolean> hiddenColumnsMap = new LinkedHashMap<String, Boolean>();
		/* Array list to hold hidden Column array */
		ArrayList<String> lstHiddenColunms = null;
		/* String array variable to hold hidden columns property */
		String[] hiddenColumnsProp = null;
		try {
			// log debug message
			log.debug(this.getClass().getName() + " - [ getScenarioColumns ] - Entry");
			// Condition to check width is null
			/* Set default width for columns */
			if (SwtUtil.isEmptyOrNull(width)) 
					width = "expand=10,entity=150,ccy=150,count=150";

			// Obtain width for each column
			columnWidthProperty = width.split(",");

			// Loop to insert each column in hash map
			widths = new LinkedHashMap<String, String>();
			for (int i = 0; i < columnWidthProperty.length; i++) {
				// Condition to check index of = is -1
				if (columnWidthProperty[i].indexOf("=") != -1) {
					String[] propval = columnWidthProperty[i].split("=");
					widths.put(propval[0], propval[1].contains(".")?propval[1].split("\\.")[0]:propval[1]);
				}
			}
			// Condition to check column order is null or empty
			if (SwtUtil.isEmptyOrNull(columnOrder)) {
				// Default values for column order
				columnOrder = columnOrder = "expand,entity,ccy,count";
			}	
			orders = new ArrayList<String>();
			// Split the columns using , and save in string array
			columnOrderProp = columnOrder.split(",");

			// Loop to enter column order in array list
			for (int i = 0; i < columnOrderProp.length; i++) {
				// Adding the Column values to ArrayList
				orders.add(columnOrderProp[i]);
				hiddenColumnsMap.put(columnOrderProp[i], true);
			}

			/* Condition to check column hidden is null or empty */
			if (!SwtUtil.isEmptyOrNull(hiddenColumns)) {
				lstHiddenColunms = new ArrayList<String>();
				/* Split the hidden columns , and save in string array */
				hiddenColumnsProp = hiddenColumns.split(",");

				for (int i = 0; i < hiddenColumnsProp.length; i++) {
					/* Adding the Column values to ArrayList */
					lstHiddenColunms.add(hiddenColumnsProp[i]);
				}

				Iterator<String> listKeys = hiddenColumnsMap.keySet().iterator();
				for (int i = 0; i < hiddenColumnsMap.size(); i++) {
					String columnKey = listKeys.next();
					// boolean found = false;
					for (int j = 0; j < lstHiddenColunms.size(); j++) {
						if (columnKey.equals(lstHiddenColunms.get(j))) {
							hiddenColumnsMap.put(columnKey, false);
							break;
						}
					}
				}
			}

			columnOrderItr = orders.iterator();
			lstColumns = new ArrayList<ColumnInfo>();

			while (columnOrderItr.hasNext()) {
				String order = (String) columnOrderItr.next();
				ColumnInfo tmpColumnInfo = null;
				// CURRENCY CODE type column
				if (order.equals("expand")) {
					tmpColumnInfo =(new ColumnInfo("",
							"expand", PCMConstant.COLUMN_TYPE_STRING, 0,
							Integer.parseInt(widths.get("expand")!=null?widths.get("expand"):"10"), false, true,
							hiddenColumnsMap.get("expand")));
				tmpColumnInfo.setHeaderTooltip(SwtUtil.getMessage(SwtConstants.INSTANCE_EXPAND_TOOLTIP, request));
				lstColumns.add(tmpColumnInfo);
				}
				// Ordinal column
				if (order.equals("entity")) {
					tmpColumnInfo =(new ColumnInfo(SwtUtil.getMessage(SwtConstants.INSTANCE_ENTITY_HEADER, request),
							"entity", PCMConstant.COLUMN_TYPE_STRING, 0,
							widths.get("entity") !=null ?Integer.parseInt(widths.get("entity")):150, false, true,
							hiddenColumnsMap.get("entity")));
				tmpColumnInfo.setHeaderTooltip(SwtUtil.getMessage(SwtConstants.INSTANCE_ENTITY_TOOLTIP, request));
				lstColumns.add(tmpColumnInfo);
				}
				// Ordinal column
				if (order.equals("ccy")) {
					tmpColumnInfo =(new ColumnInfo(SwtUtil.getMessage(SwtConstants.INSTANCE_CCY_HEADER, request),
							"ccy", PCMConstant.COLUMN_TYPE_STRING, 0,
							widths.get("ccy") != null ?Integer.parseInt(widths.get("ccy")):150, false, true,
							hiddenColumnsMap.get("ccy")));
				tmpColumnInfo.setHeaderTooltip(SwtUtil.getMessage(SwtConstants.INSTANCE_CCY_TOOLTIP, request));
				lstColumns.add(tmpColumnInfo);
				}
				// Ordinal column
				if (order.equals("count")) {
					tmpColumnInfo =(new ColumnInfo(SwtUtil.getMessage(SwtConstants.INSTANCE_COUNT_HEADER, request),
							"count", PCMConstant.COLUMN_TYPE_NUMBER, 0,
							widths.get("count") != null?Integer.parseInt(widths.get("count")):150, false, true,
							hiddenColumnsMap.get("count")));
				tmpColumnInfo.setHeaderTooltip(SwtUtil.getMessage(SwtConstants.INSTANCE_COUNT_TOOLTIP, request));
				lstColumns.add(tmpColumnInfo);
				}

			}
			

		} catch (Exception ex) {
			// log error message
			log.error(this.getClass().getName()
					+ " - Exception Catched in [getScenarioColumns] method : - " + ex.getMessage());

			throw SwtErrorHandler.getInstance().handleException(ex, "getScenarioColumns",
					this.getClass());

		} finally {
			// Nullify Objects
			orders = null;
			columnOrderProp = null;
			columnOrderItr = null;
			widths = null;
			columnWidthProperty = null;
			// log debug message
			log.debug(this.getClass().getName() + " - [ getScenarioColumns ] - Exit");
		}
		// return XML columns
		return lstColumns;
	}

	
	private String createDummyTree(AlertTreeVO data) {
		String xmlTree = new String();
		ArrayList<ScenarioAlertCount> result  = data.getScenarioAlertList();
//		if(result == null || result.size() == 0) {
//			
//			result = new ArrayList<ScenarioAlertCount>();
//			result.add(new ScenarioAlertCount("SCEN1", "INPUT", "RABONL2U", "EUR", "ACCOUNT1", 50, "",new HashMap<String, String>() {{   put("CATEGORY", "INPUT");  put("CRITICAL_GUI_HIGHLIGHT", "Y");	put("RECORD_SCENARIO_INSTANCES", "Y"); put("SCENARIO", "SCEN1"); put("ENTITY", "RABONL2U"); put("CCY", "EUR"); put("ACCOUNT", "ACCOUNT1");put("COUNT", "50"); put("field1", "value1");   put("field2", "2");  }}));
//			result.add(new ScenarioAlertCount("SCEN1", "INPUT", "RABONL2U", "EUR", "ACCOUNT1", 50, "",new HashMap<String, String>() {{   put("CATEGORY", "INPUT");  put("CRITICAL_GUI_HIGHLIGHT", "Y");	put("RECORD_SCENARIO_INSTANCES", "Y"); put("SCENARIO", "SCEN1"); put("ENTITY", "RABONL2U"); put("CCY", "USD"); put("ACCOUNT", "ACCOUNT1");put("COUNT", "20"); put("field1", "value3");   put("field2", "12");  }}));
//			result.add(new ScenarioAlertCount("SCEN1", "INPUT", "RABONL2U", "EUR", "ACCOUNT1", 50, "",new HashMap<String, String>() {{   put("CATEGORY", "INPUT");  put("CRITICAL_GUI_HIGHLIGHT", "Y");	put("RECORD_SCENARIO_INSTANCES", "Y"); put("SCENARIO", "SCEN1"); put("ENTITY", "RABONL2U"); put("CCY", "USD"); put("ACCOUNT", "ACCOUNT2");put("COUNT", "10"); put("field1", "value1");   put("field2", "12");  }}));
//			result.add(new ScenarioAlertCount("SCEN1", "INPUT", "RABONL2U", "EUR", "ACCOUNT1", 50, "",new HashMap<String, String>() {{   put("CATEGORY", "INPUT");  put("CRITICAL_GUI_HIGHLIGHT", "Y");	put("RECORD_SCENARIO_INSTANCES", "Y"); put("SCENARIO", "SCEN1"); put("ENTITY", "RABONL2U"); put("CCY", "EUR"); put("ACCOUNT", "ACCOUNT3");put("COUNT", "40"); put("field1", "value3");   put("field2", "32");  }}));
//			result.add(new ScenarioAlertCount("SCEN1", "INPUT", "RABONL2U", "EUR", "ACCOUNT1", 50, "",new HashMap<String, String>() {{   put("CATEGORY", "MATCH"); put("CRITICAL_GUI_HIGHLIGHT", "N");	put("RECORD_SCENARIO_INSTANCES", "Y");  put("SCENARIO", "SCEN2"); put("ENTITY", "RABONL2U"); put("CCY", "EUR"); put("ACCOUNT", "ACCOUNT4");put("COUNT", "60"); put("field1", "value4");   put("field2", "2");  }}));
//			result.add(new ScenarioAlertCount("SCEN1", "INPUT", "RABONL2U", "EUR", "ACCOUNT1", 50, "",new HashMap<String, String>() {{   put("CATEGORY", "MATCH");  put("CRITICAL_GUI_HIGHLIGHT", "N");	put("RECORD_SCENARIO_INSTANCES", "Y"); put("SCENARIO", "SCEN2"); put("ENTITY", "RABONSS"); put("CCY", "EUR"); put("ACCOUNT", "ACCOUNT9");put("COUNT", "80"); put("field1", "value9");   put("field2", "2");  }}));
//			
//			
//			result.add(new ScenarioAlertCount("SCEN1", "INPUT", "RABONL2U", "EUR", "ACCOUNT1", 50, "",new HashMap<String, String>() {{   put("CATEGORY", "INPUT"); put("CRITICAL_GUI_HIGHLIGHT", "Y");	put("RECORD_SCENARIO_INSTANCES", "Y");  put("SCENARIO", "SCEN3"); put("ENTITY", "RABONL2U"); put("CCY", "EUR"); put("ACCOUNT", "ACCOUNT1");put("COUNT", "50"); put("field1", "value1");   put("field2", "2");  }}));
//			result.add(new ScenarioAlertCount("SCEN1", "INPUT", "RABONL2U", "EUR", "ACCOUNT1", 50, "",new HashMap<String, String>() {{   put("CATEGORY", "INPUT");put("CRITICAL_GUI_HIGHLIGHT", "Y");	put("RECORD_SCENARIO_INSTANCES", "Y");   put("SCENARIO", "SCEN3"); put("ENTITY", "RABONL2U"); put("CCY", "USD"); put("ACCOUNT", "ACCOUNT1");put("COUNT", "20"); put("field1", "value3");   put("field2", "12");  }}));
//			result.add(new ScenarioAlertCount("SCEN1", "INPUT", "RABONL2U", "EUR", "ACCOUNT1", 50, "",new HashMap<String, String>() {{   put("CATEGORY", "INPUT"); put("CRITICAL_GUI_HIGHLIGHT", "Y");	put("RECORD_SCENARIO_INSTANCES", "Y");  put("SCENARIO", "SCEN3"); put("ENTITY", "RABONL2U"); put("CCY", "USD"); put("ACCOUNT", "ACCOUNT2");put("COUNT", "10"); put("field1", "value1");   put("field2", "12");  }}));
//			result.add(new ScenarioAlertCount("SCEN1", "INPUT", "RABONL2U", "EUR", "ACCOUNT1", 50, "",new HashMap<String, String>() {{   put("CATEGORY", "INPUT"); put("CRITICAL_GUI_HIGHLIGHT", "N");	put("RECORD_SCENARIO_INSTANCES", "Y");  put("SCENARIO", "SCEN4"); put("ENTITY", "RABONL2U"); put("CCY", "EUR"); put("ACCOUNT", "ACCOUNT3");put("COUNT", "40"); put("field1", "value3");   put("field2", "32");  }}));
//			result.add(new ScenarioAlertCount("SCEN1", "INPUT", "RABONL2U", "EUR", "ACCOUNT1", 50, "",new HashMap<String, String>() {{   put("CATEGORY", "MATCH"); put("CRITICAL_GUI_HIGHLIGHT", "N");	put("RECORD_SCENARIO_INSTANCES", "Y");  put("SCENARIO", "SCEN5"); put("ENTITY", "RABONL2U"); put("CCY", "EUR"); put("ACCOUNT", "ACCOUNT4");put("COUNT", "60"); put("field1", "value4");   put("field2", "2");  }}));
//			result.add(new ScenarioAlertCount("SCEN1", "INPUT", "RABONL2U", "EUR", "ACCOUNT1", 50, "",new HashMap<String, String>() {{   put("CATEGORY", "MATCH");  put("CRITICAL_GUI_HIGHLIGHT", "Y");	put("RECORD_SCENARIO_INSTANCES", "Y"); put("SCENARIO", "SCEN5"); put("ENTITY", "RABONSS"); put("CCY", "EUR"); put("ACCOUNT", "ACCOUNT9");put("COUNT", "80"); put("field1", "value9");   put("field2", "2");  }}));
//			result.add(new ScenarioAlertCount("SCEN1", "INPUT", "RABONL2U", "EUR", "ACCOUNT1", 50, "",new HashMap<String, String>() {{   put("CATEGORY", "INPUT");  put("CRITICAL_GUI_HIGHLIGHT", "Y");	put("RECORD_SCENARIO_INSTANCES", "Y"); put("SCENARIO", "ENHANCE_SYS_INP_AUTH"); put("ENTITY", "RABONSS"); put("CCY", "EUR"); put("ACCOUNT", "ACCOUNT9");put("COUNT", "80"); put("field1", "value9");   put("field2", "2");  }}));
//		}
		
//		  LinkedList<String> levels = new LinkedList<String>();
//		  LinkedList<String> levels2 = new LinkedList<String>();
//		  levels.add("category_id");
//		  levels.add("scenario_id");
//		  levels.add("entity_id");
//		  levels.add("currency_code");
//		  levels.add("account_id");
		  
		  
		  
//		  levels2.add("CATEGORY");
//		  levels2.add("SCENARIO");
//		  levels2.add("ENTITY");
//		  levels2.add("ACCOUNT");
		
		HashMap<String, LinkedList<String>> scenarioGrouppedByTreeLevels = new HashMap<String, LinkedList<String>>();
		
		for (Map.Entry<String, String> entry : data.getScenarioTreeGroupingLevels().entrySet()) {
			 LinkedList<String> levels = new LinkedList<String>();
			 levels.add("category_id");
			  levels.add("scenario_id");
		    String key = entry.getKey();
		    String value = entry.getValue();
		    if(!SwtUtil.isEmptyOrNull(value)) {
		    	String[] array = value.split(",");
		    	for (int i = 0; i < array.length; i++) {
		    		 levels.add(array[i].toLowerCase());
				}
		    }
		    
		    scenarioGrouppedByTreeLevels.put(key, levels);
		    
		    // ...
		}
		
		
//		scenarioGrouppedByTreeLevels.put("UPDATE_MOV", levels);
//		scenarioGrouppedByTreeLevels.put("INS_MOV", levels);
//		scenarioGrouppedByTreeLevels.put("SCEN3", levels2);
//		scenarioGrouppedByTreeLevels.put("SCEN4", levels);
//		scenarioGrouppedByTreeLevels.put("SCEN5", levels);
//		scenarioGrouppedByTreeLevels.put("ENHANCE_SYS_INP_AUTH", levels);
		
		
		HashMap<String, ArrayList<HashMap<String, String>>> mappedScearioDetails = new HashMap<String, ArrayList<HashMap<String,String>>>();
		
		ArrayList<HashMap<String, String>> itemsList = null;
		for (ScenarioAlertCount scenarioAlertCount : result) {
			if(mappedScearioDetails.get(scenarioAlertCount.getAllFields().get("scenario_id")) == null) {
				itemsList = new ArrayList<HashMap<String,String>>();
				itemsList.add(scenarioAlertCount.getAllFields());
			}else {
				itemsList = mappedScearioDetails.get(scenarioAlertCount.getAllFields().get("scenario_id"));
				itemsList.add(scenarioAlertCount.getAllFields());
			}
			mappedScearioDetails.put(scenarioAlertCount.getAllFields().get("scenario_id"), itemsList);
		}
		
		
		//Map<String, String> pathValueMap = new HashMap<String, String>();
		Map<String, HashMap<String, String>> pathValueMap = new HashMap<String, HashMap<String, String>>();
		String delimiter = "/";
		
		
		LinkedList<String> groupedBy = null;
		for (Entry<String, ArrayList<HashMap<String, String>>> entry : mappedScearioDetails.entrySet()) {
		    String key = entry.getKey();
		    groupedBy  = scenarioGrouppedByTreeLevels.get(key);
		    for (HashMap<String, String> recordElement : entry.getValue()) {
		    	pathValueMap.put(ScenarioAlertsUtils.createPath(recordElement,groupedBy), recordElement);
			}
		    // ...
		}
		
		try {
			xmlTree =  ScenarioAlertsUtils.transformAlertMapToXML(pathValueMap, delimiter);
		} catch (ParserConfigurationException e) {
		} catch (TransformerException e) {
		}
		
		
		return xmlTree;
		
		
	}
	
	/**
	 * This Method is used to get scenario summary screen info such as flags, entityList and lastRefresh time 
	 * 
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return ActionForward
	 * @throws SwtException
	 */
	public String getAlertingTooltipDetails()
					throws SwtException {
		
		/* Variable Declaration for session */
		String selectedEntityId = null;
		String currencythresholdFlag = null;
//		String hideZeroFlag = null;
//		String showAlertableFlag =null;
		String currencyCode= null;
		String selectedDate= null;
		String selectedAccountId= null;
		String selectedMvtId= null;
		String selectedMatchId = null;
		String facilityId = null;
		String facilityName = null;
		String userId = null;
		String hostId= null;
		/* Variable Declaration for selectedCategory */
		Date valueDate = null;
		// To hold the tab category list
		int totalCount = 0;
		ResponseHandler responseHandler = null;
		SwtResponseConstructor responseConstructor = null;
		SwtXMLWriter xmlWriter = null;
		ArrayList<ScenarioAlertCount> result = null;
		StringBuffer params = new StringBuffer();
		StringBuffer paramsSQL = new StringBuffer();
		String ilmAccountGroup = null;
		Double sweepId = null;
		HttpServletRequest request = ServletActionContext.getRequest();
    	HttpServletResponse response = ServletActionContext.getResponse();
		try {
			
			log.debug(this.getClass().getName() + " - [getAlertingTooltipDetails] - Entry");
			opTimer.start("all");
			
			
//			hideZeroFlag = request.getParameter("zeroBalances");
//			showAlertableFlag = request.getParameter("alertScenarios");
//			selectedCategory = request.getParameter("selectedCategory");			
			selectedEntityId = !SwtUtil.isEmptyOrNull(request.getParameter("entityId"))?request.getParameter("entityId"):SwtConstants.ALL_LABEL;
			currencyCode = !SwtUtil.isEmptyOrNull(request.getParameter("currencyCode"))?request.getParameter("currencyCode"):SwtConstants.ALL_LABEL;
			facilityId = request.getParameter("facilityId");
			selectedDate = request.getParameter("selectedDate");
			selectedAccountId = request.getParameter("selectedAccountId");
			selectedMvtId = request.getParameter("selectedMvtId");
			selectedMatchId = request.getParameter("selectedMatchId");
			currencythresholdFlag = request.getParameter("currencythresholdFlag");
			ilmAccountGroup = request.getParameter("ilmAccountGroup");
			if(!SwtUtil.isEmptyOrNull(facilityId))
				facilityName = scenMaintenanceManager.getGuiFacilityDetails(facilityId).getDescription();
			else
				facilityName = facilityId;
			
			
			if(!SwtUtil.isEmptyOrNull(selectedEntityId)) {
				params.append("ENTITY_ID("+selectedEntityId+")");
				paramsSQL.append("ENTITY_ID='"+selectedEntityId+"'");
			}
			if(!SwtUtil.isEmptyOrNull(currencyCode)) {
				params.append(params.length()>0?",CURRENCY_CODE("+currencyCode+")":"CURRENCY_CODE("+currencyCode+")");
				paramsSQL.append(params.length()>0?" AND CURRENCY_CODE= '"+currencyCode+"'":"CURRENCY_CODE= '"+currencyCode+"'");
			}
			if(!SwtUtil.isEmptyOrNull(selectedDate)) {
				params.append("$#$");
				params.append(params.length()>0?",VALUE_DATE("+selectedDate+")":"VALUE_DATE("+selectedDate+")");
				paramsSQL.append(params.length()>0?" AND VALUE_DATE='"+selectedDate+"'":"VALUE_DATE='"+selectedDate+"'");
			}

			if(!SwtUtil.isEmptyOrNull(selectedAccountId)) {
				if(SwtUtil.isEmptyOrNull(selectedDate)) 
				params.append("$#$");
				params.append(params.length()>0?",ACCOUNT_ID("+selectedAccountId+")":"ACCOUNT_ID("+selectedAccountId+")");
				paramsSQL.append(params.length()>0?" AND ACCOUNT_ID = '"+selectedAccountId+"'":"ACCOUNT_ID='"+selectedAccountId+"'");
			}
			
			if(!SwtUtil.isEmptyOrNull(selectedMvtId)) {
				params.append("$#$");
				params.append(params.length()>0?",MOVEMENT_ID("+selectedMvtId+")":"MOVEMENT_ID("+selectedMvtId+")");
				paramsSQL.append(params.length()>0?" AND MOVEMENT_ID = '"+selectedMvtId+"'":"MOVEMENT_ID='"+selectedMvtId+"'");
			}
			
			if(!SwtUtil.isEmptyOrNull(selectedMatchId)) {
				params.append("$#$");
				params.append(params.length()>0?",MATCH_ID("+selectedMatchId+")":"MATCH_ID("+selectedMatchId+")");
				paramsSQL.append(params.length()>0?" AND MATCH_ID = '"+selectedMatchId+"'":"MATCH_ID='"+selectedMatchId+"'");
			}
			
			if(!SwtUtil.isEmptyOrNull(selectedDate)) {
				valueDate = SwtUtil.parseDate(selectedDate,  SwtUtil
						.getCurrentSystemFormats(request.getSession())
						.getDateFormatValue());
			}
			
			hostId = SwtUtil.getCurrentHostId();
			// get roleId  form session
			userId = SwtUtil.getCurrentUserId(request.getSession());
			AlertInstance alertIntance  = new AlertInstance();
			alertIntance.setSelectedUserId(userId);
			alertIntance.setFaciltityId(facilityId);
			alertIntance.setAccountId(selectedAccountId);
			alertIntance.setHostId(hostId);
			alertIntance.setEntityId(selectedEntityId);
			alertIntance.setCurrencyCode(currencyCode);
			alertIntance.setValueDate(valueDate);
			alertIntance.setCurrencyThresholdFlag(currencythresholdFlag);
			alertIntance.setMovementId(!SwtUtil.isEmptyOrNull(selectedMvtId)?Double.parseDouble(selectedMvtId):null);
			alertIntance.setMatchId(!SwtUtil.isEmptyOrNull(selectedMatchId)?Double.parseDouble(selectedMatchId):null);
			alertIntance.setIlmAccountGroup(ilmAccountGroup);
			alertIntance.setSweepId(sweepId);
			
			AlertTreeVO  dataResult = scenarioSummaryManager.getAlertsScenarioCount(alertIntance );
//			result = dataResult.getScenarioAlertList();
			
			
//			result.add(new ScenarioAlertCount("SCEN1", "INPUT", "RABONL2U", "EUR", "ACCOUNT1", 50, "", new HashMap<String, String>() {{   put("field1", "value1");   put("field2", "2"); }}));
//			result.add(new ScenarioAlertCount("SCEN1", "INPUT", "RABONL2U", "USD", "ACCOUNT1", 20, "", new HashMap<String, String>() {{   put("field1", "value3");   put("field2", "12"); }}));
//			result.add(new ScenarioAlertCount("SCEN1", "INPUT", "RABONL2U", "USD", "ACCOUNT2", 10, "", new HashMap<String, String>() {{   put("field1", "value1");   put("field2", "12"); }}));
//			result.add(new ScenarioAlertCount("SCEN3", "INPUT", "RABONL2U", "EUR", "ACCOUNT3", 40, "", new HashMap<String, String>() {{   put("field1", "value3");   put("field2", "32"); }}));
//			result.add(new ScenarioAlertCount("SCEN4", "MATCH", "RABONL2U", "EUR", "ACCOUNT4", 60, "", new HashMap<String, String>() {{   put("field1", "value4");   put("field2", "2"); }}));
//			result.add(new ScenarioAlertCount("SCEN4", "MATCH", "RABONSS", "EUR", "ACCOUNT9", 80, "", new HashMap<String, String>() {{   put("field1", "value9");   put("field2", "2"); }}));
			
			
			
			
//			result = new ArrayList<ScenarioAlertCount>();
//			
//			result.add(new ScenarioAlertCount("SCEN1", "INPUT", "RABONL2U", "EUR", "ACCOUNT1", 50, "",new HashMap<String, String>() {{   put("CATEGORY", "INPUT");  put("CRITICAL_GUI_HIGHLIGHT", "Y");	put("RECORD_SCENARIO_INSTANCES", "Y"); put("SCENARIO", "SCEN1"); put("ENTITY", "RABONL2U"); put("CCY", "EUR"); put("ACCOUNT", "ACCOUNT1");put("COUNT", "50"); put("field1", "value1");   put("field2", "2");  }}));
//			result.add(new ScenarioAlertCount("SCEN1", "INPUT", "RABONL2U", "EUR", "ACCOUNT1", 50, "",new HashMap<String, String>() {{   put("CATEGORY", "INPUT");  put("CRITICAL_GUI_HIGHLIGHT", "Y");	put("RECORD_SCENARIO_INSTANCES", "Y"); put("SCENARIO", "SCEN1"); put("ENTITY", "RABONL2U"); put("CCY", "USD"); put("ACCOUNT", "ACCOUNT1");put("COUNT", "20"); put("field1", "value3");   put("field2", "12");  }}));
//			result.add(new ScenarioAlertCount("SCEN1", "INPUT", "RABONL2U", "EUR", "ACCOUNT1", 50, "",new HashMap<String, String>() {{   put("CATEGORY", "INPUT");  put("CRITICAL_GUI_HIGHLIGHT", "Y");	put("RECORD_SCENARIO_INSTANCES", "Y"); put("SCENARIO", "SCEN1"); put("ENTITY", "RABONL2U"); put("CCY", "USD"); put("ACCOUNT", "ACCOUNT2");put("COUNT", "10"); put("field1", "value1");   put("field2", "12");  }}));
//			result.add(new ScenarioAlertCount("SCEN1", "INPUT", "RABONL2U", "EUR", "ACCOUNT1", 50, "",new HashMap<String, String>() {{   put("CATEGORY", "INPUT");  put("CRITICAL_GUI_HIGHLIGHT", "Y");	put("RECORD_SCENARIO_INSTANCES", "Y"); put("SCENARIO", "SCEN1"); put("ENTITY", "RABONL2U"); put("CCY", "EUR"); put("ACCOUNT", "ACCOUNT3");put("COUNT", "40"); put("field1", "value3");   put("field2", "32");  }}));
//			result.add(new ScenarioAlertCount("SCEN1", "INPUT", "RABONL2U", "EUR", "ACCOUNT1", 50, "",new HashMap<String, String>() {{   put("CATEGORY", "MATCH"); put("CRITICAL_GUI_HIGHLIGHT", "N");	put("RECORD_SCENARIO_INSTANCES", "Y");  put("SCENARIO", "SCEN2"); put("ENTITY", "RABONL2U"); put("CCY", "EUR"); put("ACCOUNT", "ACCOUNT4");put("COUNT", "60"); put("field1", "value4");   put("field2", "2");  }}));
//			result.add(new ScenarioAlertCount("SCEN1", "INPUT", "RABONL2U", "EUR", "ACCOUNT1", 50, "",new HashMap<String, String>() {{   put("CATEGORY", "MATCH");  put("CRITICAL_GUI_HIGHLIGHT", "N");	put("RECORD_SCENARIO_INSTANCES", "Y"); put("SCENARIO", "SCEN2"); put("ENTITY", "RABONSS"); put("CCY", "EUR"); put("ACCOUNT", "ACCOUNT9");put("COUNT", "80"); put("field1", "value9");   put("field2", "2");  }}));
//			
//			
//			
//			
//			result.add(new ScenarioAlertCount("SCEN1", "INPUT", "RABONL2U", "EUR", "ACCOUNT1", 50, "",new HashMap<String, String>() {{   put("CATEGORY", "INPUT"); put("CRITICAL_GUI_HIGHLIGHT", "Y");	put("RECORD_SCENARIO_INSTANCES", "Y");  put("SCENARIO", "SCEN3"); put("ENTITY", "RABONL2U"); put("CCY", "EUR"); put("ACCOUNT", "ACCOUNT1");put("COUNT", "50"); put("field1", "value1");   put("field2", "2");  }}));
//			result.add(new ScenarioAlertCount("SCEN1", "INPUT", "RABONL2U", "EUR", "ACCOUNT1", 50, "",new HashMap<String, String>() {{   put("CATEGORY", "INPUT");put("CRITICAL_GUI_HIGHLIGHT", "Y");	put("RECORD_SCENARIO_INSTANCES", "Y");   put("SCENARIO", "SCEN3"); put("ENTITY", "RABONL2U"); put("CCY", "USD"); put("ACCOUNT", "ACCOUNT1");put("COUNT", "20"); put("field1", "value3");   put("field2", "12");  }}));
//			result.add(new ScenarioAlertCount("SCEN1", "INPUT", "RABONL2U", "EUR", "ACCOUNT1", 50, "",new HashMap<String, String>() {{   put("CATEGORY", "INPUT"); put("CRITICAL_GUI_HIGHLIGHT", "Y");	put("RECORD_SCENARIO_INSTANCES", "Y");  put("SCENARIO", "SCEN3"); put("ENTITY", "RABONL2U"); put("CCY", "USD"); put("ACCOUNT", "ACCOUNT2");put("COUNT", "10"); put("field1", "value1");   put("field2", "12");  }}));
//			result.add(new ScenarioAlertCount("SCEN1", "INPUT", "RABONL2U", "EUR", "ACCOUNT1", 50, "",new HashMap<String, String>() {{   put("CATEGORY", "INPUT"); put("CRITICAL_GUI_HIGHLIGHT", "N");	put("RECORD_SCENARIO_INSTANCES", "Y");  put("SCENARIO", "SCEN4"); put("ENTITY", "RABONL2U"); put("CCY", "EUR"); put("ACCOUNT", "ACCOUNT3");put("COUNT", "40"); put("field1", "value3");   put("field2", "32");  }}));
//			result.add(new ScenarioAlertCount("SCEN1", "INPUT", "RABONL2U", "EUR", "ACCOUNT1", 50, "",new HashMap<String, String>() {{   put("CATEGORY", "MATCH"); put("CRITICAL_GUI_HIGHLIGHT", "N");	put("RECORD_SCENARIO_INSTANCES", "Y");  put("SCENARIO", "SCEN5"); put("ENTITY", "RABONL2U"); put("CCY", "EUR"); put("ACCOUNT", "ACCOUNT4");put("COUNT", "60"); put("field1", "value4");   put("field2", "2");  }}));
//			result.add(new ScenarioAlertCount("SCEN1", "INPUT", "RABONL2U", "EUR", "ACCOUNT1", 50, "",new HashMap<String, String>() {{   put("CATEGORY", "MATCH");  put("CRITICAL_GUI_HIGHLIGHT", "Y");	put("RECORD_SCENARIO_INSTANCES", "Y"); put("SCENARIO", "SCEN5"); put("ENTITY", "RABONSS"); put("CCY", "EUR"); put("ACCOUNT", "ACCOUNT9");put("COUNT", "80"); put("field1", "value9");   put("field2", "2");  }}));
//
//			
//			  LinkedList<String> levels = new LinkedList<String>();
//			  LinkedList<String> levels2 = new LinkedList<String>();
//			  levels.add("CATEGORY");
//			  levels.add("SCENARIO");
//			  levels.add("ENTITY");
//			  levels.add("CCY");
//			  levels.add("ACCOUNT");
//			  
//			  
//			  
//			  levels2.add("CATEGORY");
//			  levels2.add("SCENARIO");
//			  levels2.add("ENTITY");
//			  levels2.add("ACCOUNT");
//			
//			HashMap<String, LinkedList<String>> scenarioGrouppedByTreeLevels = new HashMap<String, LinkedList<String>>();
//			scenarioGrouppedByTreeLevels.put("SCEN1", levels);
//			scenarioGrouppedByTreeLevels.put("SCEN2", levels2);
//			scenarioGrouppedByTreeLevels.put("SCEN3", levels2);
//			scenarioGrouppedByTreeLevels.put("SCEN4", levels);
//			scenarioGrouppedByTreeLevels.put("SCEN5", levels);
//			  
//			
//			
//			HashMap<String, ArrayList<HashMap<String, String>>> mappedScearioDetails = new HashMap<String, ArrayList<HashMap<String,String>>>();
//			
//			ArrayList<HashMap<String, String>> itemsList = null;
//			for (ScenarioAlertCount scenarioAlertCount : result) {
//				if(mappedScearioDetails.get(scenarioAlertCount.getAllFields().get("SCENARIO")) == null) {
//					itemsList = new ArrayList<HashMap<String,String>>();
//					itemsList.add(scenarioAlertCount.getAllFields());
//				}else {
//					itemsList = mappedScearioDetails.get(scenarioAlertCount.getAllFields().get("SCENARIO"));
//					itemsList.add(scenarioAlertCount.getAllFields());
//				}
//				mappedScearioDetails.put(scenarioAlertCount.getAllFields().get("SCENARIO"), itemsList);
//			}
//			
//			
//			//Map<String, String> pathValueMap = new HashMap<String, String>();
//			Map<String, HashMap<String, String>> pathValueMap = new HashMap<String, HashMap<String, String>>();
//			String delimiter = "/";
//			
//			
//			LinkedList<String> groupedBy = null;
//			for (Entry<String, ArrayList<HashMap<String, String>>> entry : mappedScearioDetails.entrySet()) {
//			    String key = entry.getKey();
//			    groupedBy  = scenarioGrouppedByTreeLevels.get(key);
//			    for (HashMap<String, String> recordElement : entry.getValue()) {
//			    	pathValueMap.put(ScenarioAlertsUtils.createPath(recordElement,groupedBy), recordElement);
//				}
//			    // ...
//			}
//			String xmlTree = ScenarioAlertsUtils.transformAlertMapToXML(pathValueMap, delimiter);
			
			
			
			String xmlTree = createDummyTree(dataResult);
//			System.err.println(xmlTree);
			
//			pathValueMap.put(createPath((new HashMap<String, String>() {{   put("CATEGORY", "INPUT");   put("SCENARIO", "SCEN1"); put("ENTITY", "RABONL2U"); put("CCY", "EUR"); put("ACCOUNT", "ACCOUNT1");put("COUNT", "50"); put("field1", "value1");   put("field2", "2");  }}),levels, ""), "50");

			
			
			
//			Map<String, String> doubleBraceMap  = new HashMap<String, String>() {{   put("field1", "value1");   put("field2", "value2"); }};
			
			
			
			
			
//			HashMap<String, ArrayList<ScenarioAlertCount>> alerts = new HashMap<String, ArrayList<ScenarioAlertCount>>();
//			HashMap<String, Integer> categoryCount = new HashMap<String, Integer>();
//			
//			for (int i = 0; i < result.size(); i++) {
//				if(alerts.get(result.get(i).getScenarioCategory()) != null) {
//					categoryCount.put(result.get(i).getScenarioCategory(), 	categoryCount.get(result.get(i).getScenarioCategory()) + result.get(i).getCount() );
//					ArrayList<ScenarioAlertCount> list = alerts.get(result.get(i).getScenarioCategory());
//					list.add(result.get(i));
//					alerts.put(result.get(i).getScenarioCategory(), list);
//				}else {
//					categoryCount.put(result.get(i).getScenarioCategory(),result.get(i).getCount());
//					
//					ArrayList<ScenarioAlertCount> list = new ArrayList<ScenarioAlertCount>();
//					list.add(result.get(i));
//					alerts.put(result.get(i).getScenarioCategory(), list);
//				
//					
//				}
//				
//			}
			
//		    buffer.append("<node	id=\""+key+"\"		isBranch=\"true\"		label=\""+labelValue+"\"	desc=\"\">");
//
//			
//			buffer.append("<node	id=\"\"		category=\"" + elem.getScenarioCategory()
//			+ "\" desc=\"\"		alert=\"" + (elem.isCriticalGuiHighlight() ? "Y" : "N")
//			+ "\"	treeOrder=\"\"	isBranch=\"false\"  folder= \"false\"  label=\""+elem.getScenarioId()+"("+elem.getCount()+")"+"\"></node> ");
//
//			buffer.append("</node>");
//			
			
//			StringBuffer buffer = new StringBuffer();
			
//			
//			buffer.append("<tree><root>");
//			String labelValue = null;
//			for (Map.Entry<String, ArrayList<ScenarioAlertCount>> entry : alerts.entrySet()) {
//			    String key = entry.getKey();
//			    ArrayList<ScenarioAlertCount> value = entry.getValue();
//			    labelValue = key+"("+categoryCount.get(key)+")";
//			    buffer.append("<node	id=\""+key+"\"		isBranch=\"true\"		label=\""+labelValue+"\"	desc=\"\">");
//			    
//			    for (int i = 0; i < value.size(); i++) {
//			    	ScenarioAlertCount elem = value.get(i);
//					buffer.append("<node	id=\"\"		category=\"" + elem.getScenarioCategory()
//							+ "\" desc=\"\"		alert=\"" + (elem.isCriticalGuiHighlight() ? "Y" : "N")
//							+ "\"	treeOrder=\"\"	isBranch=\"false\"  folder= \"false\"  label=\""+elem.getScenarioId()+"("+elem.getCount()+")"+"\"></node> ");
//			    	
//				}
//			    totalCount+= categoryCount.get(key);
//			    buffer.append("</node>");
//			    
//			}
//			
			
			
//			buffer.append("</root></tree>");
			//put entityList in request
			putEntityListInReq(request, SwtConstants.STR_TRUE);
			
			// Put scenario currencythreshold, selected alertablescenarios, selectedScenaentityIdrioId , lastRefTime , hidezerocounts and lastRefTime in request
			request.setAttribute("reply_status_ok", SwtConstants.STR_TRUE);
			request.setAttribute("reply_message", "Data fetch OK");
			request.setAttribute("currencythreshold", currencythresholdFlag);
//			request.setAttribute("hidezerocounts", hideZeroFlag!=null?hideZeroFlag:SwtConstants.STR_TRUE);
//			request.setAttribute("popupScenarios", popupScenariosFlag);
//			request.setAttribute("flashScenarios", flashScenariosFlag);
//			request.setAttribute("emailScenarios", emailScenariosFlag);
//			request.setAttribute("alertablescenarios", showAlertableFlag!=null?showAlertableFlag:SwtConstants.STR_TRUE);
			request.setAttribute("entityId", selectedEntityId);
			request.setAttribute("lastRefTime", SwtUtil.getLastRefTime(request,
			SwtUtil.getUserCurrentEntity(request.getSession())));
			
			
			opTimer.stop("all");
			// set the attribute for opTimes,lastRefTime in request
			request.setAttribute("opTimes", opTimer.getOpTimes());
			log.debug(this.getClass().getName() + " - [getAlertingTooltipDetails] - Exit");
			responseHandler = new ResponseHandler();
			responseConstructor = new SwtResponseConstructor();
			xmlWriter = responseConstructor.getXMLWriter();
			xmlWriter.startElement(SwtConstants.SCENARIO_DETAILS);
			responseConstructor.formRequestReply(Boolean.parseBoolean(SwtConstants.STR_TRUE), SwtConstants.DATA_FETCH_OK);
			xmlWriter.startElement(SwtConstants.SINGLETONS);
			responseConstructor.createElement("total",dataResult.getTotalCount() );
			responseConstructor.createElement("params",params.toString() );
			responseConstructor.createElement("paramsSQL",paramsSQL.toString() );
			responseConstructor.createElement("hostId",hostId);
			responseConstructor.createElement("facilityName",facilityName );
			responseConstructor.createElement("facilityId",facilityId );			
			
			xmlWriter.endElement(SwtConstants.SINGLETONS);
			xmlWriter.appendText(xmlTree.toString());
			xmlWriter.endElement(SwtConstants.SCENARIO_DETAILS);
			request.setAttribute("data", xmlWriter.getData());
			
		
			// set the access to response
			//response.getWriter().print(result + SwtConstants.SEPARATOR_RECORD + (endTime-startTime));			
			log.debug(this.getClass().getName() + " - [getAlertingTooltipDetails] - " + "Exit");
			return "jsondata";
		} catch (Exception exp) {
			exp.printStackTrace();
			log.error(this.getClass().getName()
					+ " - Exception Catched in [getAlertingTooltipDetails] method : - "
					+ exp.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "getAlertingTooltipDetails", ScenarioSummaryAction.class), request, "");
			return "fail";
		}
		}
	

	


	
	
	/**
	 * This method is used to bind the column order in request object
	 * 
	 * @param HttpServletRequest
	 *            request
	 * @param String
	 *            entityId
	 * @throws SwtException
	 */
	private void bindColumnOrderInRequest(HttpServletRequest request,
			String entityId) throws SwtException {
		// To get column order from DB (Comma separated value)
		String columnOrder = null;
		// To hold grid column order
		ArrayList<String> alColumnOrder = null;
		// Property value (split by Comma)
		String[] props = null;

		try {
			// log debug message
			log.debug(this.getClass().getName()
					+ " - [ bindColumnOrderInRequest ] - Entry ");
			// Get column order from DB (User preference)
			columnOrder = SwtUtil.getPropertyValue(request, entityId,
					menuItemId, "display", "column_order");
			// If user preference not found in DB, set default order
			if (SwtUtil.isEmptyOrNull(columnOrder)) {
				columnOrder = "maingroup,entity,ccy,count";
			}
			/*
			 * Comma special character is used to split and put in String array
			 * variable
			 */
			props = columnOrder.split(",");
			// Initialize list to hold grid column order
			alColumnOrder = new ArrayList<String>(props.length);
			for (String prop : props) {
				/* Adding the Column values to ArrayList */
				alColumnOrder.add(prop);
			}
			/*
			 * Setting the Column orders value in request object to show in
			 * screen
			 */
			request.setAttribute("column_order", alColumnOrder);
		} catch (Exception ex) {
			// log error message
			log.error(this.getClass().getName()
					+ " - [bindColumnOrderInRequest] - Exception - "
					+ ex.getMessage());
			// Re-throw an exception
			throw SwtErrorHandler.getInstance().handleException(ex,
					"bindColumnOrderInRequest", ScenarioSummaryAction.class);
		} finally {
			// nullify objects
			columnOrder = null;
			alColumnOrder = null;
			props = null;
			// log debug message
			log.debug(this.getClass().getName()
					+ " - [ bindColumnOrderInRequest ] - Exit ");
		}

	}

	/**
	 * This method is used to save the dataGrid column's order in the database
	 * based on the user
	 * 
	 * @param ActionMapping
	 *            mapping
	 * @param ActionForm
	 *            form
	 * @param HttpServletRequest
	 *            request
	 * @param HttpServletResponse
	 *            response
	 * @return - ActionForward object
	 * @throws SwtException
	 */
	public String saveColumnOrder() {
		// To hold column order (comma separated value)
		String columnOrder = null;
		// Entity id
		String entityId = null;
		HttpServletRequest request = ServletActionContext.getRequest();
    	HttpServletResponse response = ServletActionContext.getResponse();
		try {
			// log debug message
			log.debug(this.getClass().getName()
					+ " - [ saveColumnOrder ] - Entry ");
			// If column order and entity id found in request, save the same in
			// DB (user preference comma separated value)
			entityId =  SwtUtil.getUserCurrentEntity(request.getSession());
			if ((columnOrder = request.getParameter("order")) != null
					&& (entityId != null)) {
				SwtUtil.setPropertyValue(request, entityId, menuItemId,
						"display", "column_order", columnOrder);
			}
			/* Setting the reply_status_ok,reply_message in request object */
			request.setAttribute("reply_status_ok", SwtConstants.STR_TRUE);
			request.setAttribute("reply_message", "Column order saved ok");
		} catch (Exception e) {
			/*
			 * Setting the reply_status_ok,reply_message,reply_location in
			 * request object
			 */
			log.error(this.getClass().getName()
					+ "- [ saveColumnOrder() ] - " + e);
			request.setAttribute("reply_status_ok", SwtConstants.STR_FALSE);
			request.setAttribute("reply_message", e.getMessage());
			request.setAttribute("reply_location", e.getStackTrace()[0]
					.getClassName()
					+ "."
					+ e.getStackTrace()[0].getMethodName()
					+ ":"
					+ e.getStackTrace()[0].getLineNumber());
		} finally {
			// nullify object(s)
			columnOrder = null;
			entityId = null;
			// log debug message
			log.debug(this.getClass().getName()
					+ " - [ saveColumnOrder ] - Exit ");
		}
		/* Return Type of this Struts Action and returns to flexstatechange.jsp */
		return "statechange";
	}

	
	/**
	 * Method to set column width in request attribute
	 * 
	 * @param request
	 */
	private void bindColumnWidthInRequest(HttpServletRequest request, String fromWorkFlow) {
		/* Method's local variable declaration */
		String width = null;
		HashMap<String, String> widths = null;
		String[] props = null;
		String menuId = null;
		try {

			log.debug(this.getClass().getName()
					+ " - [bindColumnWidthInRequest] - " + "Entry");
			/* Read the user preferences for column width from property value */
			menuId = fromWorkFlow.equals(SwtConstants.STR_TRUE)?menuItemIdWorkFlow:menuItemId;
			width = SwtUtil.getPropertyValue(request, menuId, "display",
					"column_width");
			/* Condition to set default column width */
			/* Set default width for columns */
			if (SwtUtil.isEmptyOrNull(width)) {
				/*Check if fromWorkFlow flag is set to true*/ 
				if (fromWorkFlow.equals(SwtConstants.STR_TRUE))
					width = "maingroup=5,entity=132,ccy=132,count=100,divider=40";
				else
					width = "maingroup=5,entity=132,ccy=132,count=100,divider=60";
			}
			
			widths = new HashMap<String, String>();
			/* Get column width for each column */
			props = width.split(",");
			/* Loop to separate column and width value in hash map */
			for (int i = 0; i < props.length; i++) {
				if (props[i].indexOf("=") != -1) {
					String[] propval = props[i].split("=");
					widths.put(propval[0], propval[1]);
				}
			}
			log.debug(this.getClass().getName()
					+ " - [bindColumnWidthInRequest] - " + "Exit");
			request.setAttribute("column_width", widths);
		} catch (Exception e) {
			log.error(this.getClass().getName()
					+ " - [bindColumnWidthInRequest] - " + e.getMessage());
		}finally{
			//nullify the objects
			width = null;
			props = null;
		}
	}
	/**
	 * Method to save column width in user preferences list
	 * 
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return ActionForward
	 * @param SwtException
	 */
	public String saveColumnWidth() throws SwtException {
		log.debug(this.getClass().getName() + " - [saveColumnWidth] - "
				+ "Entry");
		/* Method's local variable declaration */
		String width = null;
		HttpServletRequest request = ServletActionContext.getRequest();
    	HttpServletResponse response = ServletActionContext.getResponse();
		try {
			/* Read width values from request */
			width = URLDecoder.decode(request.getParameter("width"), "UTF-8");
			/* Condition to check width is not null */
			if (width != null) {
				/* Set width value for the screen in user preference list */
				SwtUtil.setPropertyValue(request, menuItemId, "display",
						"column_width", width);

				request.setAttribute("reply_status_ok", SwtConstants.STR_TRUE);
				request.setAttribute("reply_message", "Column width saved ok");
			} else {
				request.setAttribute("reply_status_ok", SwtConstants.STR_FALSE);
				request.setAttribute("reply_message",
						"Width parameter not sent");
			}
			log.debug(this.getClass().getName() + " - [saveColumnWidth] - "
					+ "Exit");
		} catch (Exception e) {		

			log.error(this.getClass().getName()
					+ " - Exception Catched in [saveColumnWidth] method : - "
					+ e.getMessage());

			request.setAttribute("reply_status_ok", SwtConstants.STR_FALSE);
			request.setAttribute("reply_message", e.getMessage());
			request.setAttribute("reply_location", e.getStackTrace()[0]
					.getClassName()
					+ "."
					+ e.getStackTrace()[0].getMethodName()
					+ ":"
					+ e.getStackTrace()[0].getLineNumber());

			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					e, "saveColumnWidth", ScenarioSummaryAction.class),
					request, "");
		}

		return "statechange";
	}
	/**
	 * This method is used across this action class to add the entity into
	 * request
	 * 
	 * @param request
	 *            HttpServletRequest request
	 * @return Collection<LabelValueBean>
	 * @throws SwtException
	 */
	private Collection<LabelValueBean> putEntityListInReq(
			HttpServletRequest request, String flag) throws SwtException {

		// HttpSession variable to hold the session
		HttpSession session = null;
		// Collection to hold the entity access
		Collection<EntityUserAccess> userEntityAccess = null;
		// Collection to hold the entity access
		Collection<LabelValueBean> entityAccess = null;
		// Collection to hold the entity access
		Collection<LabelValueBean> collWithAll = null;
		try {
			log.debug(this.getClass().getName()
					+ "- [putEntityListInReq] - Entering ");
			// get the session
			session = request.getSession();
			// Get the entity access list, and access list as label value bean
			userEntityAccess = SwtUtil.getUserEntityAccessList(session);
			// get the entity Access
			entityAccess = SwtUtil.convertEntityAcessCollectionLVL(
					userEntityAccess, session);
			// Instantiate the ArrayList
			collWithAll = new ArrayList<LabelValueBean>();
			if (flag.equals(SwtConstants.STR_TRUE)) {
				if (entityAccess.size() > 1) {
					collWithAll.add(new LabelValueBean(SwtConstants.ALL_LABEL,
							SwtConstants.ALL_VALUE));
				}
			} else if (flag.equals(SwtConstants.STR_FALSE)) {
				collWithAll.add(new LabelValueBean("Default", "*DEFAULT*"));
			}
			// add entityAccess to collection
			collWithAll.addAll(entityAccess);
			request.setAttribute("entities", collWithAll);
			log.debug(this.getClass().getName()
					+ " - [putEntityListInReq] - Exit ");
		} catch (Exception e) {
			log.error(this.getClass().getName()
					+ " - putEntityListInReq(). Exception caught : "
					+ e.getMessage());
			// Re-throwing SwtException
			throw SwtErrorHandler.getInstance().handleException(e,
					"bindColumnWidthInRequest", ScenarioSummaryAction.class);
		}
		return collWithAll;
	}
	/**
	 * Method called when screen is loaded and returns flex page
	 * 
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return ActionForward
	 */
	@Override
	public String execute()
			throws SwtException {
		HttpServletRequest request = ServletActionContext.getRequest();
    	HttpServletResponse response = ServletActionContext.getResponse();
		log.debug(this.getClass().getName() + " - [unspecified] - " + " Entry");		
		request.setAttribute("userId", SwtUtil.getCurrentUserId(request
				.getSession()));
		request.setAttribute("callerMethod", request.getParameter("callerMethod"));
		request.setAttribute("popupScreen", request.getParameter("popupScreen"));
		return "flex";
	}
	
	public void updateScenInstanceStatus(HttpServletRequest request,
			HttpServletResponse response) throws SwtException {

		String id = null;
		String newStatus = null;
        String oldStatus = null;
		try {
			log.debug(this.getClass().getName() + "- [updateScenInstanceStatus] - starting ");
			id=request.getParameter("id");
			newStatus=request.getParameter("newStatus");
			oldStatus=request.getParameter("oldStatus");
			scenarioSummaryManager.updateScenInstanceStatus(id, newStatus);

			log.debug(this.getClass().getName() + "- [updateScenInstanceStatus] - exiting ");
			
		} catch (Exception e) {
			log.error(this.getClass().getName() + " - [updateScenInstanceStatus] - Exception - " + e.getMessage());
			// Re-throw an exception
			throw SwtErrorHandler.getInstance().handleException(e, "updateScenInstanceStatus", ScenarioSummaryAction.class);

		} finally {
			id = null;
			newStatus = null;
		}
	}
	
	public String openInstanceDetails() throws SwtException {
		HttpServletRequest request = ServletActionContext.getRequest();
    	HttpServletResponse response = ServletActionContext.getResponse();
		log.debug(this.getClass().getName() + "- [openInstanceDetails] - starting ");

		request.setAttribute("screenName", request.getParameter("screenName"));
		//Mantis 6163
		request.setAttribute("params", SwtUtil.decode64(request.getParameter("allParams")));
		request.setAttribute("fromMenu", "false");
		log.debug(this.getClass().getName() + "- [openInstanceDetails] - exiting ");

		return "openInstDetails";

	}
	
	
	public String getAlertInstDetails() throws SwtException {
		ResponseHandler responseHandler = null;
		SwtResponseConstructor responseConstructor = null;
		SwtXMLWriter xmlWriter = null;
        String instanceId = null;
        String fromMenu= null;
        String entityName= null;
        String accountName = null;
        String ccyName = null;
        String hostId= null;
        String hostName= null;
        List<ScenarioInstanceLog> logs = null;     			
		String width = null;
		String columnOrder = null;
		String hiddenColumns = null;	
		ArrayList<ScenarioInstanceMessage> messages = null;
		HttpServletRequest request = ServletActionContext.getRequest();
    	HttpServletResponse response = ServletActionContext.getResponse();
		try {
			log.debug(this.getClass().getName() + "- [getAlertInstDetails] - starting ");
			responseHandler = new ResponseHandler();
			responseConstructor = new SwtResponseConstructor();
			xmlWriter = responseConstructor.getXMLWriter();
			instanceId= request.getParameter("instanceId");
			fromMenu= request.getParameter("fromMenu");
			String entityId= request.getParameter("entityId");
			String accountId= request.getParameter("accountId");
			String ccyCode= request.getParameter("ccyCode");
			logs = new ArrayList<ScenarioInstanceLog>();
            if ("false".equalsIgnoreCase(fromMenu)) {
			//get scenario instance logs
			logs=scenarioSummaryManager.getInstanceLogs(instanceId);
			// get entity Name, account Name, Ccy Name

			String roleId = ((CommonDataManager) request.getSession().getAttribute(SwtConstants.CDM_BEAN)).getUser().getRoleId();
			hostId = SwtUtil.getCurrentHostId();
			
			if(!SwtUtil.isEmptyOrNull(hostId)) {
			hostName= scenarioSummaryManager.getHostName(hostId);
			System.out.println("hostnameee nadiaaa"+hostName);
			hostName= !SwtUtil.isEmptyOrNull(hostName)?hostName:SwtUtil.getMessage(SwtConstants.HOST_NOT_FOUND, request);
			}
			
			if(!SwtUtil.isEmptyOrNull(entityId)  && !SwtUtil.isEmptyOrNull(ccyCode)) {
			ArrayList<LabelValueBean> currencyList = (ArrayList) SwtUtil.getSwtMaintenanceCache()
					.getCurrencyViewORFullAcessLVL(roleId, entityId);
			LabelValueBean elementCcy = currencyList.stream().filter(customer -> ccyCode.equals(customer.getValue()))
					.findAny().orElse(null);
			ccyName= elementCcy!=null?elementCcy.getLabel():SwtUtil.getMessage(SwtConstants.CURRENCY_NOT_FOUND, request);
			}
			
			if(!SwtUtil.isEmptyOrNull(entityId)) {
			Collection<EntityUserAccess> entityAccess = SwtUtil.getUserEntityAccessList(request.getSession());
			EntityUserAccess elementEntity = entityAccess.stream()
					.filter(customer -> entityId.equals(customer.getEntityId())).findAny().orElse(null);
			entityName= elementEntity!=null?elementEntity.getEntityName():SwtUtil.getMessage(SwtConstants.ENTITY_NOT_FOUND, request);
			}
			
			if(!SwtUtil.isEmptyOrNull(entityId)  && !SwtUtil.isEmptyOrNull(ccyCode) && !SwtUtil.isEmptyOrNull(accountId)) {
			ArrayList<LabelValueBean> accountList = (ArrayList) ((AcctMaintenanceManager) (SwtUtil
					.getBean("acctMaintenanceManager"))).getAccountIDDropDownForCopy(hostId, entityId, ccyCode);

			LabelValueBean elementAcount = accountList.stream().filter(customer -> accountId.equals(customer.getValue()))
					.findAny().orElse(null);
			accountName= elementAcount!=null?elementAcount.getLabel():SwtUtil.getMessage(SwtConstants.ACCOUNT_NOT_FOUND, request);
			}
			xmlWriter.startElement(SwtConstants.SCENARIO_DETAILS);
			responseConstructor.formRequestReply(Boolean.parseBoolean(SwtConstants.STR_TRUE),
					SwtConstants.DATA_FETCH_OK);
			
			//scenario instance log grid
			responseConstructor.formGridStart("instanceLogGrid");
			// form column details
			responseConstructor.formColumn(getInstanceLogColumns(width, columnOrder, hiddenColumns,request));

			// form rows (records)
			responseConstructor.formRowsStart(logs.size());
			// Iterating currency details
			for (Iterator<ScenarioInstanceLog> it = logs.iterator(); it.hasNext();) {
				// Obtain currency tag from iterator
				ScenarioInstanceLog log = it.next();
				
				responseConstructor.formRowStart();
				
				responseConstructor.createRowElement(SwtConstants.INSTANCE_TEXT_LOG_TAGNAME   , !SwtUtil.isEmptyOrNull(log.getLogText())?SwtUtil.encode64(log.getLogText()):"");
				responseConstructor.createRowElement(SwtConstants.INSTANCE_USER_LOG_TAGNAME   , log.getLogUser());
				responseConstructor.createRowElement(SwtConstants.INSTANCE_DATETIME_LOG_TAGNAME , log.getLogDateTime() != null?SwtUtil.formatDate(log.getLogDateTime(), SwtUtil.getCurrentDateFormat(request.getSession()) + " HH:mm:ss"):"");				
				
				responseConstructor.formRowEnd();
			}

			responseConstructor.formRowsEnd();
			responseConstructor.formGridEnd();
			
			
			
			//instance message part
			
			messages= scenarioSummaryManager.getInstanceMessages(instanceId);
			responseConstructor.formGridStart("msgGrid");
			// form paging details
			responseConstructor.formPaging(null);
			// form column details
			responseConstructor.formColumn(getMessageSummaryColumns(width, columnOrder, hiddenColumns, request));

			// form rows (records)
			responseConstructor.formRowsStart(messages.size());

			// Iterating message details
			for (Iterator<ScenarioInstanceMessage> it = messages.iterator(); it.hasNext();) {
				// Obtain currency tag from iterator
				ScenarioInstanceMessage message = (ScenarioInstanceMessage) it.next();

				responseConstructor.formRowStart();
				responseConstructor.createRowElement(SwtConstants.INSTANCE_MSG_FORMATID_TAGNAME,
						message.getFormatId());
								
				responseConstructor.createRowElement(SwtConstants.INSTANCE_MSG_MESSAGEID_TAGNAME,
						message.getMessageId());
				
				responseConstructor.createRowElement(SwtConstants.INSTANCE_MSG_INPUTDATE_TAGNAME,
						SwtUtil.formatDate(message.getInputDate(),
								SwtUtil.getCurrentDateFormat(request.getSession())));
				
				responseConstructor.createRowElement(SwtConstants.INSTANCE_MSG_UPDATEDATE_TAGNAME,
						SwtUtil.formatDate(message.getUpdateDate(),
								SwtUtil.getCurrentDateFormat(request.getSession()) + " HH:mm:ss"));
				
				responseConstructor.createRowElement(SwtConstants.INSTANCE_MSG_UPDATEUSER_TAGNAME,
						message.getUpdateUser());
				
				responseConstructor.formRowEnd();
			}

			responseConstructor.formRowsEnd();
			responseConstructor.formGridEnd();
			
            }else {
    			xmlWriter.startElement(SwtConstants.SCENARIO_DETAILS);
    			responseConstructor.formRequestReply(Boolean.parseBoolean(SwtConstants.STR_TRUE),
    					SwtConstants.DATA_FETCH_OK);
            	//scenario instance log grid
    			responseConstructor.formGridStart("instanceLogGrid");
    			// form column details
    			responseConstructor.formColumn(getInstanceLogColumns(width, columnOrder, hiddenColumns,request));
    			responseConstructor.formGridEnd();
    			
            	//instance message grid
    			responseConstructor.formGridStart("msgGrid");
    			// form column details
    			responseConstructor.formColumn(getMessageSummaryColumns(width, columnOrder, hiddenColumns, request));
    			responseConstructor.formGridEnd();

            }
			// forms singleton node
			xmlWriter.startElement(SwtConstants.SINGLETONS);
			responseConstructor.createElement("hostName", hostName);
			responseConstructor.createElement("entityName", entityName);
			responseConstructor.createElement("accountName", accountName);
			responseConstructor.createElement("ccyName", ccyName);
			xmlWriter.endElement(SwtConstants.SINGLETONS);
			xmlWriter.endElement(SwtConstants.SCENARIO_DETAILS);
			request.setAttribute("data", xmlWriter.getData());
	
			log.debug(this.getClass().getName() + "- [getAlertInstDetails] - exiting ");

			return "jsondata";

			
		} catch (Exception e) {
			log.error(this.getClass().getName() + " - [getAlertInstDetails] - Exception - " + e.getMessage());
			// Re-throw an exception
			throw SwtErrorHandler.getInstance().handleException(e, "getAlertInstDetails", ScenarioSummaryAction.class);

		} finally {
			instanceId = null;
		}
	}
	
	
	/**
	 * This method creates sample column details
	 * 
	 * @param width       - passing columns widths
	 * @param columnOrder - passing default column order
	 * @return List 0f columns
	 * @throws SwtException
	 */
	private List<ColumnInfo> getInstanceLogColumns(String width, String columnOrder, String hiddenColumns, HttpServletRequest request)
			throws SwtException {
		// Array list to hold column order
		ArrayList<String> orders = null;
		// String array variable to hold column order property
		String[] columnOrderProp = null;
		// Iterator variable to hold column order
		Iterator<String> columnOrderItr = null;
		// Hash map to hold column width
		LinkedHashMap<String, String> widths = null;
		// String array variable to hold width property
		String[] columnWidthProperty = null;
		// List for column info
		List<ColumnInfo> lstColumns = null;
		/* Hash map to hold column hidden_Columns */
		HashMap<String, Boolean> hiddenColumnsMap = new LinkedHashMap<String, Boolean>();
		/* Array list to hold hidden Column array */
		ArrayList<String> lstHiddenColunms = null;
		/* String array variable to hold hidden columns property */
		String[] hiddenColumnsProp = null;
		try {
			// log debug message
			log.debug(this.getClass().getName() + " - [ getInstanceLogColumns ] - Entry");
			// Condition to check width is null

			if (SwtUtil.isEmptyOrNull(width)) {
				// default width for columns
				width = SwtConstants.INSTANCE_DATETIME_LOG_TAGNAME  + "=190,"
						+ SwtConstants.INSTANCE_USER_LOG_TAGNAME    + "=150,"
						+ SwtConstants.INSTANCE_TEXT_LOG_TAGNAME    + "=480";
			}

			// Obtain width for each column
			columnWidthProperty = width.split(",");

			// Loop to insert each column in hash map
			widths = new LinkedHashMap<String, String>();
			for (int i = 0; i < columnWidthProperty.length; i++) {
				// Condition to check index of = is -1
				if (columnWidthProperty[i].indexOf("=") != -1) {
					String[] propval = columnWidthProperty[i].split("=");
					widths.put(propval[0], propval[1]);
				}
			}

			// Condition to check column order is null or empty
			if (SwtUtil.isEmptyOrNull(columnOrder)) {
				// Default values for column order
				columnOrder = SwtConstants.INSTANCE_DATETIME_LOG_TAGNAME  + ","
						+ SwtConstants.INSTANCE_USER_LOG_TAGNAME          + ","
						+ SwtConstants.INSTANCE_TEXT_LOG_TAGNAME          ;
			}
			orders = new ArrayList<String>();
			// Split the columns using , and save in string array
			columnOrderProp = columnOrder.split(",");

			// Loop to enter column order in array list
			for (int i = 0; i < columnOrderProp.length; i++) {
				// Adding the Column values to ArrayList
				orders.add(columnOrderProp[i]);
				hiddenColumnsMap.put(columnOrderProp[i], true);
			}

			/* Condition to check column hidden is null or empty */
			if (!SwtUtil.isEmptyOrNull(hiddenColumns)) {
				lstHiddenColunms = new ArrayList<String>();
				/* Split the hidden columns , and save in string array */
				hiddenColumnsProp = hiddenColumns.split(",");

				for (int i = 0; i < hiddenColumnsProp.length; i++) {
					/* Adding the Column values to ArrayList */
					lstHiddenColunms.add(hiddenColumnsProp[i]);
				}

				Iterator<String> listKeys = hiddenColumnsMap.keySet().iterator();
				for (int i = 0; i < hiddenColumnsMap.size(); i++) {
					String columnKey = listKeys.next();
					// boolean found = false;
					for (int j = 0; j < lstHiddenColunms.size(); j++) {
						if (columnKey.equals(lstHiddenColunms.get(j))) {
							hiddenColumnsMap.put(columnKey, false);
							break;
						}
					}
				}
			}

			columnOrderItr = orders.iterator();
			lstColumns = new ArrayList<ColumnInfo>();

			while (columnOrderItr.hasNext()) {
				String order = (String) columnOrderItr.next();
				ColumnInfo tmpColumnInfo = null;
				
				
				if (order.equals(SwtConstants.INSTANCE_DATETIME_LOG_TAGNAME)) {
					tmpColumnInfo =(new ColumnInfo(SwtUtil.getMessage(SwtConstants.INSTANCE_DATETIME_LOG_HEADER, request),
							SwtConstants.INSTANCE_DATETIME_LOG_TAGNAME, PCMConstant.COLUMN_TYPE_DATE, 0,
							Integer.parseInt(widths.get(SwtConstants.INSTANCE_DATETIME_LOG_TAGNAME)), false, true,
							hiddenColumnsMap.get(SwtConstants.INSTANCE_DATETIME_LOG_TAGNAME)));
				tmpColumnInfo.setHeaderTooltip(SwtUtil.getMessage(SwtConstants.INSTANCE_DATETIME_LOG_TOOLTIP, request));
				lstColumns.add(tmpColumnInfo);
				}
				
				
				if (order.equals(SwtConstants.INSTANCE_USER_LOG_TAGNAME)) {
					tmpColumnInfo =(new ColumnInfo(SwtUtil.getMessage(SwtConstants.INSTANCE_USER_LOG_HEADER, request),
							SwtConstants.INSTANCE_USER_LOG_TAGNAME, PCMConstant.COLUMN_TYPE_STRING, 1,
							Integer.parseInt(widths.get(SwtConstants.INSTANCE_USER_LOG_TAGNAME)), true, true,
							hiddenColumnsMap.get(SwtConstants.INSTANCE_USER_LOG_TAGNAME)));
				tmpColumnInfo.setHeaderTooltip(SwtUtil.getMessage(SwtConstants.INSTANCE_USER_LOG_TOOLTIP, request));
				lstColumns.add(tmpColumnInfo);
				}

				
				if (order.equals(SwtConstants.INSTANCE_TEXT_LOG_TAGNAME)) {
					tmpColumnInfo =(new ColumnInfo(SwtUtil.getMessage(SwtConstants.INSTANCE_TEXT_LOG_HEADER, request),
							SwtConstants.INSTANCE_TEXT_LOG_TAGNAME, PCMConstant.COLUMN_TYPE_STRING, 2,
							Integer.parseInt(widths.get(SwtConstants.INSTANCE_TEXT_LOG_TAGNAME)), true, true,
							hiddenColumnsMap.get(SwtConstants.INSTANCE_TEXT_LOG_TAGNAME)));
				
				tmpColumnInfo.setHeaderTooltip(SwtUtil.getMessage(SwtConstants.INSTANCE_TEXT_LOG_TOOLTIP, request));
				lstColumns.add(tmpColumnInfo);
				}

			}

		} catch (Exception ex) {
			// log error message
			log.error(this.getClass().getName()
					+ " - Exception Catched in [getInstanceLogColumns] method : - " + ex.getMessage());

			throw SwtErrorHandler.getInstance().handleException(ex, "getInstanceLogColumns",
					this.getClass());

		} finally {
			// Nullify Objects
			orders = null;
			columnOrderProp = null;
			columnOrderItr = null;
			widths = null;
			columnWidthProperty = null;
			// log debug message
			log.debug(this.getClass().getName() + " - [ getInstanceLogColumns ] - Exit");
		}
		// return XML columns
		return lstColumns;
	}
	
	public String checkUserInstAccess() throws SwtException {

		String instanceId = null;
		String scenarioId = null;
		String entityId= null;
		String ccyCode= null;
		String hostId = null;
        String hasAccess = null;
        String roleId = null;
        String fromWorkflow = null;
        String fullInstanceAccess = null;
        String workflowAccess = null;
        String entityCcyAccess = null;
        boolean hasAccesstoEntity=false;
        boolean workflowFlag = false;
        int menuAccessId = 2;
        CommonDataManager cdm = null;
        SwtResponseConstructor responseConstructor = null;
		SwtXMLWriter xmlWriter = null;
		HttpServletRequest request = ServletActionContext.getRequest();
    	HttpServletResponse response = ServletActionContext.getResponse();
		try {
			log.debug(this.getClass().getName() + "- [checkUserInstAccess] - starting ");
			instanceId=request.getParameter("instanceId");
			scenarioId=request.getParameter("scenarioId");
			entityId=request.getParameter("entityId");
			ccyCode=request.getParameter("ccyCode");
			hostId=request.getParameter("hostId");
			fromWorkflow=request.getParameter("fromWorkflow");
			// get roleId  form session
			roleId = ((CommonDataManager) request.getSession()
					.getAttribute(SwtConstants.CDM_BEAN)).getUser()
					.getRoleId();
			//check if user has full access to scenario instance
			fullInstanceAccess=scenarioSummaryManager.getScenarioInstAccess(scenarioId, hostId, roleId, entityId);
			// check if user has access to workflow monitor
			if ("true".equalsIgnoreCase(fromWorkflow)) {
				LogonDAO logonDAO = (LogonDAO) SwtUtil.getBean("logonDAO");
				cdm = (CommonDataManager) request.getSession().getAttribute(SwtConstants.CDM_BEAN);
				MenuItem menuItem = logonDAO.getMenuItem(SwtConstants.MENU_ITEM_WORKFLOW_MONITOR + "", cdm.getUser());
				if (menuItem != null) {
					menuAccessId = SwtUtil.getHierarchicalAccessId(menuItem, request);
					if (menuAccessId == 0) {
						workflowFlag = true;
					} else {
						workflowFlag = false;
					}
				}
			} else {
				workflowFlag = true;
			}
			//check if user has entity and currency access
			if(!SwtUtil.isEmptyOrNull(entityId) && !SwtUtil.isEmptyOrNull(ccyCode)) {
				hasAccesstoEntity = SwtUtil.getFullAccesOnCurrencyAndEntity(request, hostId, entityId, ccyCode);	
			}else {
				hasAccesstoEntity=true;
			}
			
			//chech access
			if("Y".equalsIgnoreCase(fullInstanceAccess) && workflowFlag && hasAccesstoEntity) {
				hasAccess="true";
			}else {
				hasAccess="false";
			}
			// Instantiate SwtResponseConstructor
			responseConstructor = new SwtResponseConstructor();
			// Get instance of SwtXMLWriter
			xmlWriter = responseConstructor.getXMLWriter();
			xmlWriter.startElement(SwtConstants.SCENARIO_DETAILS);
			responseConstructor.formRequestReply(Boolean.parseBoolean(SwtConstants.STR_TRUE), SwtConstants.DATA_FETCH_OK);
			xmlWriter.startElement(SwtConstants.SINGLETONS);
			responseConstructor.createElement("hasAccess",hasAccess );						
			xmlWriter.endElement(SwtConstants.SINGLETONS);
			xmlWriter.endElement(SwtConstants.SCENARIO_DETAILS);
			request.setAttribute("data", xmlWriter.getData());		

			log.debug(this.getClass().getName() + "- [checkUserInstAccess] - exiting ");
			return "jsondata";

		} catch (Exception e) {
			log.error(this.getClass().getName() + " - [checkUserInstAccess] - Exception - " + e.getMessage());
			// Re-throw an exception
			throw SwtErrorHandler.getInstance().handleException(e, "checkUserInstAccess", ScenarioSummaryAction.class);

		} finally {
			instanceId = null;
			scenarioId = null;
			entityId = null;
			ccyCode = null;
			fullInstanceAccess = null;
			workflowAccess = null;
		}
	}


	public String getScenarioFacility() throws SwtException {
		SwtResponseConstructor responseConstructor = null;
		SwtXMLWriter xmlWriter = null;
		String componentId = null;
		String scenarioFacility = null;
		String scenarioId = null;
		HttpServletRequest request = ServletActionContext.getRequest();
    	HttpServletResponse response = ServletActionContext.getResponse();
		// debug message
		log.debug(this.getClass().getName()
					+ " - [ getMvtType ] - Entry");
		scenarioId = request.getParameter("scenarioId");
		scenarioFacility= scenarioSummaryManager.getScenarioFacility(scenarioId);
		responseConstructor = new SwtResponseConstructor();
	
		xmlWriter = responseConstructor.getXMLWriter();
		// Get component ID
		componentId = "ScenarioSummary";	
		// Adding screen id and current user id as attributes
		xmlWriter.addAttribute(SwtConstants.SCREEN_ID, componentId);
		xmlWriter.addAttribute("scenarioFacility", scenarioFacility);
		xmlWriter.startElement("ScenarioSummary");
		xmlWriter.clearAttribute();
		responseConstructor.formRequestReply(Boolean.parseBoolean(SwtConstants.STR_TRUE), SwtConstants.DATA_FETCH_OK);
		xmlWriter.endElement("ScenarioSummary");
			
		request.setAttribute("data", xmlWriter.getData());
		return "jsondata";
	}
	

	public String getInstanceXml() throws SwtException {
		SwtResponseConstructor responseConstructor = null;
		SwtXMLWriter xmlWriter = null;
		String componentId = null;
		String instanceXml = null;
		String instanceId = null;
		// debug message
		log.debug(this.getClass().getName()
					+ " - [ getInstanceXm!l ] - Entry");
		HttpServletRequest request = ServletActionContext.getRequest();
    	HttpServletResponse response = ServletActionContext.getResponse();
		instanceId = request.getParameter("instanceId");
		instanceXml= scenarioSummaryManager.getInstAttXml(instanceId);
		responseConstructor = new SwtResponseConstructor();
	
		xmlWriter = responseConstructor.getXMLWriter();
		// Get component ID
		componentId = "ScenarioSummary";	
		// Adding screen id and current user id as attributes
		xmlWriter.addAttribute(SwtConstants.SCREEN_ID, componentId);
		xmlWriter.startElement("ScenarioSummary");
		xmlWriter.clearAttribute();
		// forms singleton node
		xmlWriter.startElement(SwtConstants.SINGLETONS);
		responseConstructor.createElement("instanceXml",instanceXml.replace(">", "#"));
		xmlWriter.endElement(SwtConstants.SINGLETONS);
		responseConstructor.formRequestReply(Boolean.parseBoolean(SwtConstants.STR_TRUE), SwtConstants.DATA_FETCH_OK);
		xmlWriter.endElement("ScenarioSummary");
			
		request.setAttribute("data", xmlWriter.getData());
		return "jsondata";
	}

	public String getInstanceNewData() throws SwtException {
		SwtResponseConstructor responseConstructor = null;
		SwtXMLWriter xmlWriter = null;
		String componentId = null;
		AlertInstance instance = new AlertInstance();
		String instanceId = null;
		String oldStatus = null;
		String newStatus = null;
		String width = null;
		String columnOrder = null;
		String hiddenColumns = null;	
		List<ScenarioInstanceLog> logs = null; 
		// debug message
		log.debug(this.getClass().getName()
					+ " - [ getInstanceNewData] - Entry");
		HttpServletRequest request = ServletActionContext.getRequest();
    	HttpServletResponse response = ServletActionContext.getResponse();
		instanceId = request.getParameter("id");
		newStatus=request.getParameter("newStatus");
		oldStatus=request.getParameter("oldStatus");
		logs = new ArrayList<ScenarioInstanceLog>();

		//update scenario instance status
		scenarioSummaryManager.updateScenInstanceStatus(instanceId, newStatus);
		
		String amountFormat = SwtUtil.getCurrentCurrencyFormat(request.getSession());
		
		//get scenario instance details
		instance= scenarioSummaryManager.getInstanceNewData(instanceId);
		
		//get scenario instance logs
		logs=scenarioSummaryManager.getInstanceLogs(instanceId);
		
		responseConstructor = new SwtResponseConstructor();	
		xmlWriter = responseConstructor.getXMLWriter();
		// Get component ID
		componentId = "ScenarioSummary";	
		// Adding screen id and current user id as attributes
		xmlWriter.addAttribute(SwtConstants.SCREEN_ID, componentId);
		xmlWriter.startElement("ScenarioSummary");
		xmlWriter.clearAttribute();
		// forms singleton node
		xmlWriter.startElement(SwtConstants.SINGLETONS);
		if (instance !=null) {
		responseConstructor.createElement("id",""+new DecimalFormat("#").format(instance.getId()));
		responseConstructor.createElement("scenarioId",instance.getScenarioId());
		responseConstructor.createElement("status",!SwtUtil.isEmptyOrNull(instance.getStatus())?getStatusDesc(instance.getStatus()):"");
		responseConstructor.createElement("eventStatus",!SwtUtil.isEmptyOrNull(instance.getEventsLaunchStatus())?getEventStatusDesc(instance.getEventsLaunchStatus()):"");
		responseConstructor.createElement("raisedDateTime",instance.getRaisedDatetime() != null?SwtUtil.formatDate(instance.getRaisedDatetime(), SwtUtil.getCurrentDateFormat(request.getSession()) + " HH:mm:ss"):"");
		responseConstructor.createElement("lastRaisedDateTime",instance.getLastRaisedDatetime() != null?SwtUtil.formatDate(instance.getLastRaisedDatetime(), SwtUtil.getCurrentDateFormat(request.getSession()) + " HH:mm:ss"):"");
		responseConstructor.createElement("resolvedDateTime",instance.getResolvedDatetime() != null?SwtUtil.formatDate(instance.getResolvedDatetime(), SwtUtil.getCurrentDateFormat(request.getSession()) + " HH:mm:ss"):"");
		responseConstructor.createElement("resolvedUser",instance.getResolvedByUser());
		responseConstructor.createElement("uniqueIdentifer",instance.getUniqueIdentifier());
		responseConstructor.createElement("hostId",instance.getHostId());
		responseConstructor.createElement("entityId",instance.getEntityId());
		responseConstructor.createElement("ccyCode",instance.getCurrencyCode());
		responseConstructor.createElement("accountId",instance.getAccountId());
		responseConstructor.createElement("valueDate",instance.getValueDate() != null?SwtUtil.formatDate(instance.getValueDate(), SwtUtil.getCurrentDateFormat(request.getSession())):"");				
		responseConstructor.createElement("amount", instance.getCurrencyCode() != null?SwtUtil.formatCurrency(instance.getCurrencyCode(), instance.getAmount() ,amountFormat):"");
		responseConstructor.createElement("sign",instance.getSign());
		responseConstructor.createElement("mvtId",instance.getMovementId() != null ?""+ new DecimalFormat("#").format(instance.getMovementId()):"");
		responseConstructor.createElement("matchId", instance.getMatchId() != null ?""+ new DecimalFormat("#").format(instance.getMatchId()):"");
		responseConstructor.createElement("sweepId",instance.getSweepId() != null ?""+ new DecimalFormat("#").format(instance.getSweepId()):"");
		responseConstructor.createElement("payId",instance.getPaymentId() != null ?""+ new DecimalFormat("#").format(instance.getPaymentId()):"");
		responseConstructor.createElement("otherId",instance.getOtherId());
		responseConstructor.createElement("otherIdType",instance.getOtherIdType());
		
		}
		xmlWriter.endElement(SwtConstants.SINGLETONS);
		//get the scenario instance log grid new records
		responseConstructor.formGridStart("instanceLogGrid");
		// form column details
		responseConstructor.formColumn(getInstanceLogColumns(width, columnOrder, hiddenColumns,request));

		// form rows (records)
		responseConstructor.formRowsStart(logs.size());
		// Iterating currency details
		for (Iterator<ScenarioInstanceLog> it = logs.iterator(); it.hasNext();) {
			// Obtain currency tag from iterator
			ScenarioInstanceLog log = it.next();
			
			responseConstructor.formRowStart();
			
			responseConstructor.createRowElement(SwtConstants.INSTANCE_TEXT_LOG_TAGNAME   , !SwtUtil.isEmptyOrNull(log.getLogText())?SwtUtil.encode64(log.getLogText()):"");
			responseConstructor.createRowElement(SwtConstants.INSTANCE_USER_LOG_TAGNAME   , log.getLogUser());
			responseConstructor.createRowElement(SwtConstants.INSTANCE_DATETIME_LOG_TAGNAME , log.getLogDateTime() != null?SwtUtil.formatDate(log.getLogDateTime(), SwtUtil.getCurrentDateFormat(request.getSession()) + " HH:mm:ss"):"");				
			
			responseConstructor.formRowEnd();
		}

		responseConstructor.formRowsEnd();
		responseConstructor.formGridEnd();
		responseConstructor.formRequestReply(Boolean.parseBoolean(SwtConstants.STR_TRUE), SwtConstants.DATA_FETCH_OK);
		xmlWriter.endElement("ScenarioSummary");
			
		request.setAttribute("data", xmlWriter.getData());
		return "jsondata";
	}
	
	
	public String alertInstanceDisplay() throws SwtException {
		HttpServletRequest request = ServletActionContext.getRequest();
    	HttpServletResponse response = ServletActionContext.getResponse();
		try {
			request.setAttribute("fromMenu", "true");
				return "openInstDetails";
			
		} catch (Exception e) {
			log.error("Exception Catch in ScenarioSummaryAction.'alertInstanceDisplay' method : " + e.getMessage());
			SwtUtil.logErrorInDatabase(
					SwtErrorHandler.getInstance().handleException(e, "alertInstanceDisplay", ScenarioSummaryAction.class));
			return "fail";
		}
	}
	
	
	public String getInstanceDetails() throws SwtException {
		SwtResponseConstructor responseConstructor = null;
		SwtXMLWriter xmlWriter = null;		
		String width = null;
		String columnOrder = null;
		String hiddenColumns = null;	
		String componentId = null;
		AlertInstance instance = new AlertInstance();
		String instanceId = null;
        String entityName= null;
        String accountName = null;
        String ccyName = null;
        String hostId= null;
        String hostName = null;
		List<ScenarioInstanceLog> logs = null; 
		ArrayList<ScenarioInstanceMessage> messages = null;
		HttpServletRequest request = ServletActionContext.getRequest();
    	HttpServletResponse response = ServletActionContext.getResponse();
		try {
		// debug message
		log.debug(this.getClass().getName()
					+ " - [ getInstanceDetails] - Entry");
		instanceId = request.getParameter("instanceId");
		logs = new ArrayList<ScenarioInstanceLog>();
		String amountFormat = SwtUtil.getCurrentCurrencyFormat(request.getSession());
		//get scenario instance details
		instance= scenarioSummaryManager.getInstanceNewData(instanceId);
		//get scenario instance logs
		logs=scenarioSummaryManager.getInstanceLogs(instanceId);

		String roleId = ((CommonDataManager) request.getSession().getAttribute(SwtConstants.CDM_BEAN)).getUser()
				.getRoleId();
		hostId = SwtUtil.getCurrentHostId();
		responseConstructor = new SwtResponseConstructor();	
		xmlWriter = responseConstructor.getXMLWriter();
		// Get component ID
		componentId = "ScenarioSummary";	
		// Adding screen id and current user id as attributes
		xmlWriter.addAttribute(SwtConstants.SCREEN_ID, componentId);
		xmlWriter.startElement("scenarioDetails");
		xmlWriter.clearAttribute();
		// forms singleton node
		xmlWriter.startElement(SwtConstants.SINGLETONS);
		if (instance.getId() !=null) {
		String entityId=instance.getEntityId();
		String accountId=instance.getAccountId();
		String ccyCode=instance.getCurrencyCode();
		responseConstructor.createElement("id",""+new DecimalFormat("#").format(instance.getId()));
		responseConstructor.createElement("scenarioId",instance.getScenarioId());
		responseConstructor.createElement("status",!SwtUtil.isEmptyOrNull(instance.getStatus())?getStatusDesc(instance.getStatus()):"");
		responseConstructor.createElement("eventStatus",!SwtUtil.isEmptyOrNull(instance.getEventsLaunchStatus())?getEventStatusDesc(instance.getEventsLaunchStatus()):"");
		responseConstructor.createElement("raisedDateTime",instance.getRaisedDatetime() != null?SwtUtil.formatDate(instance.getRaisedDatetime(), SwtUtil.getCurrentDateFormat(request.getSession()) + " HH:mm:ss"):"");
		responseConstructor.createElement("lastRaisedDateTime",instance.getLastRaisedDatetime() != null?SwtUtil.formatDate(instance.getLastRaisedDatetime(), SwtUtil.getCurrentDateFormat(request.getSession()) + " HH:mm:ss"):"");
		responseConstructor.createElement("resolvedDateTime",instance.getResolvedDatetime() != null?SwtUtil.formatDate(instance.getResolvedDatetime(), SwtUtil.getCurrentDateFormat(request.getSession()) + " HH:mm:ss"):"");
		responseConstructor.createElement("resolvedUser",instance.getResolvedByUser());
		responseConstructor.createElement("uniqueIdentifer",instance.getUniqueIdentifier());
		responseConstructor.createElement("hostId",instance.getHostId());
		responseConstructor.createElement("entityId",instance.getEntityId());
		responseConstructor.createElement("ccyCode",instance.getCurrencyCode());
		responseConstructor.createElement("accountId",instance.getAccountId());
		responseConstructor.createElement("valueDate",instance.getValueDate() != null?SwtUtil.formatDate(instance.getValueDate(), SwtUtil.getCurrentDateFormat(request.getSession())):"");
		try {
		responseConstructor.createElement("amount", instance.getCurrencyCode() != null?SwtUtil.formatCurrency(instance.getCurrencyCode(), instance.getAmount() ,amountFormat):"");
		}catch(Exception e) {
			responseConstructor.createElement("amount",  ""+instance.getAmount());
		}
		responseConstructor.createElement("sign",instance.getSign());
		responseConstructor.createElement("mvtId",instance.getMovementId() != null ?""+ new DecimalFormat("#").format(instance.getMovementId()):"");
		responseConstructor.createElement("matchId", instance.getMatchId() != null ?""+ new DecimalFormat("#").format(instance.getMatchId()):"");
		responseConstructor.createElement("sweepId",instance.getSweepId() != null ?""+ new DecimalFormat("#").format(instance.getSweepId()):"");
		responseConstructor.createElement("payId",instance.getPaymentId() != null ?""+ new DecimalFormat("#").format(instance.getPaymentId()):"");
		responseConstructor.createElement("otherId",instance.getOtherId());
		responseConstructor.createElement("otherIdType",instance.getOtherIdType());
		responseConstructor.createElement("xmlAttributes",instance.getAttributesXml().replace(">", "#"));
		
		if(!SwtUtil.isEmptyOrNull(hostId)) {
		hostName= scenarioSummaryManager.getHostName(hostId);
		hostName= !SwtUtil.isEmptyOrNull(hostName)?hostName:SwtUtil.getMessage(SwtConstants.HOST_NOT_FOUND, request);
		}
		
		
		if(!SwtUtil.isEmptyOrNull(entityId)  && !SwtUtil.isEmptyOrNull(ccyCode)) {
		ArrayList<LabelValueBean> currencyList = (ArrayList) SwtUtil.getSwtMaintenanceCache()
				.getCurrencyViewORFullAcessLVL(roleId, entityId);
		LabelValueBean elementCcy = currencyList.stream().filter(customer -> ccyCode.equals(customer.getValue()))
				.findAny().orElse(null);
		ccyName = elementCcy!=null?elementCcy.getLabel():SwtUtil.getMessage(SwtConstants.CURRENCY_NOT_FOUND, request);
		}
        
		if(!SwtUtil.isEmptyOrNull(entityId)) {
		Collection<EntityUserAccess> entityAccess = SwtUtil.getUserEntityAccessList(request.getSession());
		EntityUserAccess elementEntity = entityAccess.stream()
				.filter(customer -> entityId.equals(customer.getEntityId())).findAny().orElse(null);
		entityName = elementEntity!=null?elementEntity.getEntityName():SwtUtil.getMessage(SwtConstants.ENTITY_NOT_FOUND, request);
		}
		
		if(!SwtUtil.isEmptyOrNull(entityId)&& !SwtUtil.isEmptyOrNull(ccyCode) && !SwtUtil.isEmptyOrNull(accountId)) {
		ArrayList<LabelValueBean> accountList = (ArrayList) ((AcctMaintenanceManager) (SwtUtil
				.getBean("acctMaintenanceManager"))).getAccountIDDropDownForCopy(hostId, entityId, ccyCode);
		LabelValueBean elementAcount = accountList.stream().filter(customer -> accountId.equals(customer.getValue()))
				.findAny().orElse(null);
		accountName = elementAcount!=null?elementAcount.getLabel():SwtUtil.getMessage(SwtConstants.ACCOUNT_NOT_FOUND, request);
		}
		responseConstructor.createElement("entityName", entityName);
		responseConstructor.createElement("accountName", accountName);
		responseConstructor.createElement("ccyName", ccyName);
		responseConstructor.createElement("hostName", hostName);
		
		
		}else {
		responseConstructor.createElement("errorMsg",(SwtUtil.getMessage(SwtConstants.INVALID_INSTANCE_ID, request)));

		}
		xmlWriter.endElement(SwtConstants.SINGLETONS);
		//scenario instance log grid
		responseConstructor.formGridStart("instanceLogGrid");
		// form column details
		responseConstructor.formColumn(getInstanceLogColumns(width, columnOrder, hiddenColumns,request));

		// form rows (records)
		responseConstructor.formRowsStart(logs.size());
		// Iterating currency details
		for (Iterator<ScenarioInstanceLog> it = logs.iterator(); it.hasNext();) {
			// Obtain currency tag from iterator
			ScenarioInstanceLog log = it.next();
			
			responseConstructor.formRowStart();
			
			responseConstructor.createRowElement(SwtConstants.INSTANCE_TEXT_LOG_TAGNAME   , !SwtUtil.isEmptyOrNull(log.getLogText())?SwtUtil.encode64(log.getLogText()):"");
			responseConstructor.createRowElement(SwtConstants.INSTANCE_USER_LOG_TAGNAME   , log.getLogUser());
			responseConstructor.createRowElement(SwtConstants.INSTANCE_DATETIME_LOG_TAGNAME , log.getLogDateTime() != null?SwtUtil.formatDate(log.getLogDateTime(), SwtUtil.getCurrentDateFormat(request.getSession()) + " HH:mm:ss"):"");				
			
			responseConstructor.formRowEnd();
		}

		responseConstructor.formRowsEnd();
		responseConstructor.formGridEnd();
		

		//instance message part
		
		messages= scenarioSummaryManager.getInstanceMessages(instanceId);
		responseConstructor.formGridStart("msgGrid");
		// form paging details
		responseConstructor.formPaging(null);
		// form column details
		responseConstructor.formColumn(getMessageSummaryColumns(width, columnOrder, hiddenColumns, request));

		// form rows (records)
		responseConstructor.formRowsStart(messages.size());

		// Iterating message details
		for (Iterator<ScenarioInstanceMessage> it = messages.iterator(); it.hasNext();) {
			// Obtain currency tag from iterator
			ScenarioInstanceMessage message = (ScenarioInstanceMessage) it.next();
			responseConstructor.formRowStart();
			responseConstructor.createRowElement(SwtConstants.INSTANCE_MSG_FORMATID_TAGNAME,
					message.getFormatId());
							
			responseConstructor.createRowElement(SwtConstants.INSTANCE_MSG_MESSAGEID_TAGNAME,
					message.getMessageId());
			
			responseConstructor.createRowElement(SwtConstants.INSTANCE_MSG_INPUTDATE_TAGNAME,
					SwtUtil.formatDate(message.getInputDate(),
							SwtUtil.getCurrentDateFormat(request.getSession())));
			
			responseConstructor.createRowElement(SwtConstants.INSTANCE_MSG_UPDATEDATE_TAGNAME,
					SwtUtil.formatDate(message.getUpdateDate(),
							SwtUtil.getCurrentDateFormat(request.getSession()) + " HH:mm:ss"));
			
			responseConstructor.createRowElement(SwtConstants.INSTANCE_MSG_UPDATEUSER_TAGNAME,
					message.getUpdateUser());
			
			responseConstructor.formRowEnd();
		}

		responseConstructor.formRowsEnd();
		responseConstructor.formGridEnd();
		
		responseConstructor.formRequestReply(Boolean.parseBoolean(SwtConstants.STR_TRUE), SwtConstants.DATA_FETCH_OK);
		xmlWriter.endElement("scenarioDetails");
			
		request.setAttribute("data", xmlWriter.getData());
		return "jsondata";
		
	} catch (Exception e) {
		log.error(this.getClass().getName() + " - [getInstanceDetails] - Exception - " + e.getMessage());
		// Re-throw an exception
		throw SwtErrorHandler.getInstance().handleException(e, "getInstanceDetails", ScenarioSummaryAction.class);

	} finally {
		instanceId = null;
        entityName= null;
        accountName = null;
        ccyName = null;
        hostId= null;
		logs = null;
	}
	}

	
	
	
	public String getInstMsg() throws SwtException {
		HttpServletRequest request = ServletActionContext.getRequest();
    	HttpServletResponse response = ServletActionContext.getResponse();
		request.setAttribute("instanceId", request.getParameter("instanceId"));

		return "alertInstMsg";
	}
	


	public String getAlertInstMsg() {

		SwtResponseConstructor responseConstructor = null;
		SwtXMLWriter xmlWriter = null;
		String userId = null;
		Date mDate = null;
		String componentId = null;
		String width = null;
		String columnOrder = null;
		String hiddenColumns = null;
		int menuAccessId = 2;
		CommonDataManager cdm = null;
		ArrayList<ScenarioInstanceMessage> messages = null;
		String instanceId= null;
		HttpServletRequest request = ServletActionContext.getRequest();
    	HttpServletResponse response = ServletActionContext.getResponse();
		try {
			// log debug message
			log.debug(this.getClass().getName() + " - [ getAlertInstMsg ] - " + "Entry");
			instanceId = request.getParameter("instanceId");
			// Get currentUserId from session
			userId = SwtUtil.getCurrentUser(request.getSession()).getId().getUserId();
			messages= scenarioSummaryManager.getInstanceMessages(instanceId);
			// Instantiate SwtResponseConstructor
			responseConstructor = new SwtResponseConstructor();
			// Get instance of SwtXMLWriter
			xmlWriter = responseConstructor.getXMLWriter();
			// Get component ID
			componentId = "scenarioSummary";

			// Adding screen id and current user id as attributes
			xmlWriter.addAttribute(SwtConstants.SCREEN_ID, componentId);
			xmlWriter.addAttribute(SwtConstants.MENU_ACCESS, menuAccessId);
			xmlWriter.addAttribute(SwtConstants.CURRUSER, userId);
			// Adding screen id and current user id as attributes
			xmlWriter.addAttribute(SwtConstants.DATE_FORMAT, SwtUtil.getCurrentDateFormat(request.getSession()));

			xmlWriter.startElement("scenarioSummary");
			xmlWriter.clearAttribute();
			responseConstructor.formRequestReply(Boolean.parseBoolean(SwtConstants.STR_TRUE),
					SwtConstants.DATA_FETCH_OK);

			responseConstructor.formGridStart("msgGrid");
			// form paging details
			responseConstructor.formPaging(null);
			// form column details
			responseConstructor.formColumn(getMessageSummaryColumns(width, columnOrder, hiddenColumns, request));

			// form rows (records)
			responseConstructor.formRowsStart(messages.size());

			// Iterating message details
			for (Iterator<ScenarioInstanceMessage> it = messages.iterator(); it.hasNext();) {
				// Obtain currency tag from iterator
				ScenarioInstanceMessage message = (ScenarioInstanceMessage) it.next();

				responseConstructor.formRowStart();
				responseConstructor.createRowElement(SwtConstants.INSTANCE_MSG_FORMATID_TAGNAME,
						message.getFormatId());
								
				responseConstructor.createRowElement(SwtConstants.INSTANCE_MSG_MESSAGEID_TAGNAME,
						message.getMessageId());
				
				responseConstructor.createRowElement(SwtConstants.INSTANCE_MSG_INPUTDATE_TAGNAME,
						SwtUtil.formatDate(message.getInputDate(),
								SwtUtil.getCurrentDateFormat(request.getSession())));
				
				responseConstructor.createRowElement(SwtConstants.INSTANCE_MSG_UPDATEDATE_TAGNAME,
						SwtUtil.formatDate(message.getUpdateDate(),
								SwtUtil.getCurrentDateFormat(request.getSession()) + " HH:mm:ss"));
				
				responseConstructor.createRowElement(SwtConstants.INSTANCE_MSG_UPDATEUSER_TAGNAME,
						message.getUpdateUser());
				
				responseConstructor.formRowEnd();
			}

			responseConstructor.formRowsEnd();
			responseConstructor.formGridEnd();
			// forms singleton node
			xmlWriter.startElement(SwtConstants.SINGLETONS);

			// format test date to system format
			//mDate = SwtUtil.getTestDateFromParams(SwtUtil.getCurrentHostId());
			// String variable for test date
			String testDate = "";
			// formats the date
			/*if (mDate != null)
				testDate = SwtUtil.formatDate(mDate, systemFormats.getDateFormatValue());
			responseConstructor.createElement(PCMConstant.CURRENCYPATTERN,
					SwtUtil.getCurrentSystemFormats(request.getSession()).getCurrencyFormat());
			responseConstructor.createElement(PCMConstant.TEST_DATE, testDate);*/
			xmlWriter.endElement(SwtConstants.SINGLETONS);
			xmlWriter.endElement("scenarioSummary");

			request.setAttribute("data", xmlWriter.getData());
			return "jsondata";
		} catch (SwtException exp) {
			xmlWriter.clearData();
			log.error(this.getClass().getName() + " - SwtException Catched in [getAlertInstMsg] method : - "
					+ exp.getMessage());
			return "fail";
		} catch (Exception ex) {
			xmlWriter.clearData();

			log.error(this.getClass().getName() + " - Exception Catched in [getAlertInstMsg] method : - "
					+ ex.getMessage());
			return "fail";
		} finally {
			// nullify objects
			xmlWriter.destroyWriter();
			responseConstructor = null;
			xmlWriter = null;
			userId = null;
			mDate = null;
			componentId = null;
			width = null;
			columnOrder = null;
			hiddenColumns = null;
			// log debug message
			log.debug(this.getClass().getName() + " - [ getAlertInstMsg ] - Exit");
		}
	}
	
	/**
	 * This method creates sample column details
	 * 
	 * @param width       - passing columns widths
	 * @param columnOrder - passing default column order
	 * @return List 0f columns
	 * @throws SwtException
	 */
	private List<ColumnInfo> getMessageSummaryColumns(String width, String columnOrder, String hiddenColumns, HttpServletRequest request)
			throws SwtException {
		// Array list to hold column order
		ArrayList<String> orders = null;
		// String array variable to hold column order property
		String[] columnOrderProp = null;
		// Iterator variable to hold column order
		Iterator<String> columnOrderItr = null;
		// Hash map to hold column width
		LinkedHashMap<String, String> widths = null;
		// String array variable to hold width property
		String[] columnWidthProperty = null;
		// List for column info
		List<ColumnInfo> lstColumns = null;
		/* Hash map to hold column hidden_Columns */
		HashMap<String, Boolean> hiddenColumnsMap = new LinkedHashMap<String, Boolean>();
		/* Array list to hold hidden Column array */
		ArrayList<String> lstHiddenColunms = null;
		/* String array variable to hold hidden columns property */
		String[] hiddenColumnsProp = null;
		try {
			// log debug message
			log.debug(this.getClass().getName() + " - [ getMessageSummaryColumns ] - Entry");
			// Condition to check width is null

			if (SwtUtil.isEmptyOrNull(width)) {
				// default width for columns
				width =  SwtConstants.INSTANCE_MSG_FORMATID_TAGNAME + "=120,"
						+ SwtConstants.INSTANCE_MSG_MESSAGEID_TAGNAME + "=140,"
						+ SwtConstants.INSTANCE_MSG_INPUTDATE_TAGNAME + "=160,"
						+ SwtConstants.INSTANCE_MSG_UPDATEDATE_TAGNAME + "=180,"
						+ SwtConstants.INSTANCE_MSG_UPDATEUSER_TAGNAME + "=150";
			}

			// Obtain width for each column
			columnWidthProperty = width.split(",");

			// Loop to insert each column in hash map
			widths = new LinkedHashMap<String, String>();
			for (int i = 0; i < columnWidthProperty.length; i++) {
				// Condition to check index of = is -1
				if (columnWidthProperty[i].indexOf("=") != -1) {
					String[] propval = columnWidthProperty[i].split("=");
					widths.put(propval[0], propval[1]);
				}
			}

			// Condition to check column order is null or empty
			if (SwtUtil.isEmptyOrNull(columnOrder)) {
				// Default values for column order
				columnOrder = SwtConstants.INSTANCE_MSG_FORMATID_TAGNAME + ","
						+ SwtConstants.INSTANCE_MSG_MESSAGEID_TAGNAME + ","
						+ SwtConstants.INSTANCE_MSG_INPUTDATE_TAGNAME + ","
						+ SwtConstants.INSTANCE_MSG_UPDATEDATE_TAGNAME + ","
						+ SwtConstants.INSTANCE_MSG_UPDATEUSER_TAGNAME;
			}
			orders = new ArrayList<String>();
			// Split the columns using , and save in string array
			columnOrderProp = columnOrder.split(",");

			// Loop to enter column order in array list
			for (int i = 0; i < columnOrderProp.length; i++) {
				// Adding the Column values to ArrayList
				orders.add(columnOrderProp[i]);
				hiddenColumnsMap.put(columnOrderProp[i], true);
			}

			/* Condition to check column hidden is null or empty */
			if (!SwtUtil.isEmptyOrNull(hiddenColumns)) {
				lstHiddenColunms = new ArrayList<String>();
				/* Split the hidden columns , and save in string array */
				hiddenColumnsProp = hiddenColumns.split(",");

				for (int i = 0; i < hiddenColumnsProp.length; i++) {
					/* Adding the Column values to ArrayList */
					lstHiddenColunms.add(hiddenColumnsProp[i]);
				}

				Iterator<String> listKeys = hiddenColumnsMap.keySet().iterator();
				for (int i = 0; i < hiddenColumnsMap.size(); i++) {
					String columnKey = listKeys.next();
					// boolean found = false;
					for (int j = 0; j < lstHiddenColunms.size(); j++) {
						if (columnKey.equals(lstHiddenColunms.get(j))) {
							hiddenColumnsMap.put(columnKey, false);
							break;
						}
					}
				}
			}

			columnOrderItr = orders.iterator();
			lstColumns = new ArrayList<ColumnInfo>();

			while (columnOrderItr.hasNext()) {
				String order = (String) columnOrderItr.next();
				ColumnInfo tmpColumnInfo = null;
				// FORMAT_ID column
				if (order.equals(SwtConstants.INSTANCE_MSG_FORMATID_TAGNAME)) {
					tmpColumnInfo=(new ColumnInfo(SwtUtil.getMessage(SwtConstants.INSTANCE_MSG_FORMATID_HEADER, request),
							SwtConstants.INSTANCE_MSG_FORMATID_TAGNAME, SwtConstants.COLUMN_TYPE_STRING, 0,
							Integer.parseInt(widths.get(SwtConstants.INSTANCE_MSG_FORMATID_TAGNAME)), false, true,
							hiddenColumnsMap.get(SwtConstants.INSTANCE_MSG_FORMATID_TAGNAME)));
				tmpColumnInfo.setHeaderTooltip(SwtUtil.getMessage(SwtConstants.INSTANCE_MSG_FORMATID_TOOLTIP, request));
				lstColumns.add(tmpColumnInfo);
				}


				// MESSAGE_ID column
				if (order.equals(SwtConstants.INSTANCE_MSG_MESSAGEID_TAGNAME)) {
					tmpColumnInfo= (new ColumnInfo(SwtUtil.getMessage(SwtConstants.INSTANCE_MSG_MESSAGEID_HEADER, request),
							SwtConstants.INSTANCE_MSG_MESSAGEID_TAGNAME, SwtConstants.COLUMN_TYPE_NUMBER, 1,
							Integer.parseInt(widths.get(SwtConstants.INSTANCE_MSG_MESSAGEID_TAGNAME)), true, true,
							hiddenColumnsMap.get(SwtConstants.INSTANCE_MSG_MESSAGEID_TAGNAME)));
				tmpColumnInfo.setHeaderTooltip(SwtUtil.getMessage(SwtConstants.INSTANCE_MSG_MESSAGEID_TOOLTIP, request));
				lstColumns.add(tmpColumnInfo);
				}
				
				// INPUT_DATE column
				if (order.equals(SwtConstants.INSTANCE_MSG_INPUTDATE_TAGNAME)) {
					tmpColumnInfo= (new ColumnInfo(SwtUtil.getMessage(SwtConstants.INSTANCE_MSG_INPUTDATE_HEADER, request),
							SwtConstants.INSTANCE_MSG_INPUTDATE_TAGNAME, SwtConstants.COLUMN_TYPE_DATE, 2,
							Integer.parseInt(widths.get(SwtConstants.INSTANCE_MSG_INPUTDATE_TAGNAME)), true, true,
							hiddenColumnsMap.get(SwtConstants.INSTANCE_MSG_INPUTDATE_TAGNAME)));
				tmpColumnInfo.setHeaderTooltip(SwtUtil.getMessage(SwtConstants.INSTANCE_MSG_INPUTDATE_TOOLTIP, request));
				lstColumns.add(tmpColumnInfo);
				}
				
				// UPDATE_DATE column
				if (order.equals(SwtConstants.INSTANCE_MSG_UPDATEDATE_TAGNAME)) {
					tmpColumnInfo= (new ColumnInfo(SwtUtil.getMessage(SwtConstants.INSTANCE_MSG_UPDATEDATE_HEADER, request),
							SwtConstants.INSTANCE_MSG_UPDATEDATE_TAGNAME, SwtConstants.COLUMN_TYPE_DATE, 3,
							Integer.parseInt(widths.get(SwtConstants.INSTANCE_MSG_UPDATEDATE_TAGNAME)), true, true,
							hiddenColumnsMap.get(SwtConstants.INSTANCE_MSG_UPDATEDATE_TAGNAME)));
				tmpColumnInfo.setHeaderTooltip(SwtUtil.getMessage(SwtConstants.INSTANCE_MSG_UPDATEDATE_TOOLTIP, request));
				lstColumns.add(tmpColumnInfo);
				}
				
				// UPDATE_USER column
				if (order.equals(SwtConstants.INSTANCE_MSG_UPDATEUSER_TAGNAME)) {
					tmpColumnInfo= (new ColumnInfo(SwtUtil.getMessage(SwtConstants.INSTANCE_MSG_UPDATEUSER_HEADER, request),
							SwtConstants.INSTANCE_MSG_UPDATEUSER_TAGNAME, SwtConstants.COLUMN_TYPE_STRING, 4,
							Integer.parseInt(widths.get(SwtConstants.INSTANCE_MSG_UPDATEUSER_TAGNAME)), true, true,
							hiddenColumnsMap.get(SwtConstants.INSTANCE_MSG_UPDATEUSER_TAGNAME)));
				tmpColumnInfo.setHeaderTooltip(SwtUtil.getMessage(SwtConstants.INSTANCE_MSG_UPDATEUSER_TOOLTIP, request));
				lstColumns.add(tmpColumnInfo);
				}
			}

		} catch (Exception ex) {
			// log error message
			log.error(this.getClass().getName()
					+ " - Exception Catched in [getMessageSummaryColumns] method : - " + ex.getMessage());

			throw SwtErrorHandler.getInstance().handleException(ex, "getMessageSummaryColumns",
					this.getClass());

		} finally {
			// Nullify Objects
			orders = null;
			columnOrderProp = null;
			columnOrderItr = null;
			widths = null;
			columnWidthProperty = null;
			// log debug message
			log.debug(this.getClass().getName() + " - [ getMessageSummaryColumns ] - Exit");
		}
		// return XML columns
		return lstColumns;
	}
	
	

	public String getInstMsgBody() {

		SwtResponseConstructor responseConstructor = null;
		SwtXMLWriter xmlWriter = null;
		String userId = null;
		Date mDate = null;
		String componentId = null;
		String width = null;
		String columnOrder = null;
		String hiddenColumns = null;
		int menuAccessId = 2;
		CommonDataManager cdm = null;
		String messageId= null;
		String messageBody= null;
		HttpServletRequest request = ServletActionContext.getRequest();
    	HttpServletResponse response = ServletActionContext.getResponse();
		try {
			// log debug message
			log.debug(this.getClass().getName() + " - [ getInstMsgBody ] - " + "Entry");
			
			messageId = request.getParameter("messageId");
			
			// Get currentUserId from session
			userId = SwtUtil.getCurrentUser(request.getSession()).getId().getUserId();
			InputExceptionsMessagesManager inputExceptionsMessagesManager = (InputExceptionsMessagesManager) SwtUtil
					.getBean("inputExceptionsMessagesManager");
			messageBody= inputExceptionsMessagesManager.getMessageBody(messageId, new OpTimer(), false);
			if(!SwtUtil.isEmptyOrNull(messageBody)) {				
				messageBody = messageBody.replaceAll("\n", "\\$\\#\\$");
				messageBody = messageBody.replaceAll(" ", "&@&");
				messageBody=SwtUtil.encode64(messageBody);
			}
			
			// Instantiate SwtResponseConstructor
			responseConstructor = new SwtResponseConstructor();
			// Get instance of SwtXMLWriter
			xmlWriter = responseConstructor.getXMLWriter();
			// Get component ID
			componentId = "scenarioSummary";


			// Adding screen id and current user id as attributes
			xmlWriter.addAttribute(SwtConstants.SCREEN_ID, componentId);
			xmlWriter.addAttribute(SwtConstants.MENU_ACCESS, menuAccessId);
			xmlWriter.addAttribute(SwtConstants.CURRUSER, userId);
			// Adding screen id and current user id as attributes
			xmlWriter.addAttribute(SwtConstants.DATE_FORMAT, SwtUtil.getCurrentDateFormat(request.getSession()));

			xmlWriter.startElement("scenarioSummary");
			xmlWriter.clearAttribute();
			responseConstructor.formRequestReply(Boolean.parseBoolean(SwtConstants.STR_TRUE),
					SwtConstants.DATA_FETCH_OK);

			// forms singleton node
			xmlWriter.startElement(SwtConstants.SINGLETONS);

			responseConstructor.createElement("messageBody",messageBody);
			
			xmlWriter.endElement(SwtConstants.SINGLETONS);
			xmlWriter.endElement("scenarioSummary");

			request.setAttribute("data", xmlWriter.getData());
			return "jsondata";
		} catch (SwtException exp) {
			xmlWriter.clearData();
			log.error(this.getClass().getName() + " - SwtException Catched in [getInstMsgBody] method : - "
					+ exp.getMessage());
			return "fail";
		} catch (Exception ex) {
			xmlWriter.clearData();

			log.error(this.getClass().getName() + " - Exception Catched in [getInstMsgBody] method : - "
					+ ex.getMessage());
			return "fail";
		} finally {
			// nullify objects
			xmlWriter.destroyWriter();
			responseConstructor = null;
			xmlWriter = null;
			userId = null;
			mDate = null;
			componentId = null;
			width = null;
			columnOrder = null;
			hiddenColumns = null;
			// log debug message
			log.debug(this.getClass().getName() + " - [ getInstMsgBody ] - Exit");
		}
	}

}

