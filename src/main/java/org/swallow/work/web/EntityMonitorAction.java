/*
 * @(#)EntityMonitorAction.java 1.0 12/02/11
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.work.web;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collection;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.apache.struts2.ServletActionContext;
import org.apache.struts2.convention.annotation.Action;
import org.apache.struts2.convention.annotation.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.maintenance.model.Currency;
import org.swallow.maintenance.model.CurrencyGroup;
import org.swallow.maintenance.model.CurrencyMaster;
import org.swallow.maintenance.model.Entity;
import org.swallow.maintenance.model.EntityUserAccess;
import org.swallow.maintenance.model.PersonalCurrency;
import org.swallow.maintenance.service.CurrencyDetailVO;
import org.swallow.maintenance.service.CurrencyManager;
import org.swallow.maintenance.service.EntityManager;
import org.swallow.model.MenuAccess;
import org.swallow.model.User;
import org.swallow.util.CacheManager;
import org.swallow.util.CommonDataManager;
import org.swallow.util.LabelValueBean;
import org.swallow.util.OpTimer;
import org.swallow.util.SwtConstants;
import org.swallow.util.SwtUtil;
import org.swallow.util.SystemFormats;
import org.swallow.util.struts.CustomActionSupport;
import org.swallow.work.model.PersonalCcyEntityMap;
import org.swallow.work.model.PersonalEntityList;
import org.swallow.work.model.ScreenOption;
import org.swallow.work.service.EntityMonitorManager;
import org.swallow.work.service.ScreenOptionManager;
import org.swallow.work.web.form.BalanceDateTO;
import org.swallow.work.web.form.EntityMonitorForm;
import org.swallow.work.web.form.EntityRecord;

/**
 * EntityMonitorAction.java
 * 
 * EntityMonitorAction class is used for EntityMonitorAction screen that will
 * display currencies positions across Entities.<br>
 * 
 * <AUTHOR>
 * @date Feb 10, 2011
 */

@Action(value = "/entityMonitor", results = {
	    @Result(name = "fail", location = "/error.jsp"),
	    @Result(name = "success", location = "/jsp/work/entitymonitorflexdata.jsp"),
	    @Result(name = "flexobject", location = "/jsp/work/entitymonitorflex.jsp" ),
	    @Result(name = "personalentity", location = "/jsp/work/personalentityflexdata.jsp" ),
	    @Result(name = "addsumpersonalentity", location = "/jsp/work/addsumpersonalentityflexdata.jsp"),    
	    @Result(name = "entityflexobject", location = "/jsp/work/personalentityflex.jsp"),    
	    @Result(name = "dataerror", location = "/jsp/work/entitymonitorflexdataerror.jsp"),    
	    @Result(name = "options", location = "/jsp/work/entitymonitoroptionsflexdata.jsp"),  	    
	    @Result(name = "flexobjectOption", location = "/jsp/work/entitymonitoroptionsflex.jsp"),    
	    @Result(name = "personalcurrency", location = "/jsp/work/personalcurrencyflexdata.jsp"),    
	    @Result(name = "currencyflexobject", location = "/jsp/work/personalcurrencyflex.jsp"),    
	    @Result(name = "statechange", location ="/jsp/flexstatechange.jsp" ) 
	})


public class EntityMonitorAction extends CustomActionSupport {

	/**
	 * Final log instance for logging this class
	 */
	private final Log logger = LogFactory.getLog(EntityMonitorAction.class);
	
	private EntityMonitorForm entityMonitorForm;	
	public EntityMonitorForm getEntityMonitorForm() {
		return entityMonitorForm;
	}
	public void setEntityMonitorForm(EntityMonitorForm entityMonitorForm) {
		this.entityMonitorForm = entityMonitorForm;
	}
	
	private EntityMonitorForm entityMonitor;	
	public EntityMonitorForm getEntityMonitor() {
		return entityMonitor;
	}
	public void setEntityMonitor(EntityMonitorForm entityMonitorForm) {
		this.entityMonitor = entityMonitorForm;
	}
	
	
	
	private PersonalEntityList personalEntityList;
	public PersonalEntityList getPersonalEntityList() {
		return personalEntityList;
	}
	public void setPersonalEntityList(PersonalEntityList personalEntityList) {
		this.personalEntityList = personalEntityList;
	}

	private PersonalCurrency personalCurrencyList;
	public PersonalCurrency getPersonalCurrency() {
		return personalCurrencyList;
	}
	public void setPersonalCurrency(PersonalCurrency personalCurrencyList) {
		this.personalCurrencyList = personalCurrencyList;
	}
	

	private String menuAccessId;
	public String getMenuAccessId() {
		return menuAccessId;
	}
	public void setMenuAccessId(String menuAccessId) {
		this.menuAccessId = menuAccessId;
	}
	
	private String ismenuItem;
	public String getIsmenuItem() {
		return ismenuItem;
	}
	public void setIsmenuItem(String ismenuItem) {
		this.ismenuItem = ismenuItem;
	}
	
	private String menuItemId;
	public String getMenuItemId() {
		return menuItemId;
	}
	public void setMenuItemId(String menuItemId) {
		this.menuItemId = menuItemId;
	}
	
	/**
	 * An instance of EntityMonitorManager to be used across the application
	 */
	@Autowired
	private EntityMonitorManager entityMonitorManager = null;

	// Object for flexFieldMapping
	private FlexFieldMapping flexFieldMapping = null;

	/**
	 * @param entityMonitorManager
	 *            the entityMonitorManager to set
	 */
	public void setEntityMonitorManager(EntityMonitorManager entityMonitorManager) {
		this.entityMonitorManager = entityMonitorManager;
	}

	/**
	 * No-argument constructor where FlexFieldMapping has got instantiated.<br>
	 */
	public EntityMonitorAction() {
		this.flexFieldMapping = new FlexFieldMapping();
	}

	/**
	 * This Class used to map the variables with the flex data
	 */
	public class FlexFieldMapping extends org.swallow.util.FlexFieldMapping {
		public FlexFieldMapping() {
			// key-value pair for Entity Id
			map(SwtConstants.ENTITY_ID, SwtConstants.ENTITY_ID);
			// key-value pair for Entity Name
			map(SwtConstants.ENTITY_NAME, SwtConstants.ENTITY_NAME);
			// key-value pair for Display
			map(SwtConstants.DISPLAY, SwtConstants.DISPLAY);
			// key-value pair for Display Days
			map(SwtConstants.DISPLAY_DAYS, SwtConstants.DISPLAY_DAYS);
			// key-value pair for Order of Display
			map(SwtConstants.PRIORITY, SwtConstants.PRIORITY);
		}
	}

	/**
	 * Loads the flex object into the client
	 * 
	 * @param ActionMapping
	 *            mapping
	 * @param ActionForm
	 *            form
	 * @param HttpServletRequest
	 *            request
	 * @param HttpServletResponse
	 *            response
	 * @throws SwtException
	 */
	public String flex()
			throws SwtException {
		// variable to hold error message
		String errorMessage = null;
		HttpServletRequest request = ServletActionContext.getRequest();
    	HttpServletResponse response = ServletActionContext.getResponse();
		try {
			logger.debug(this.getClass().getName() + " - [flex] - Entering ");
			// Get the host id from CacheManager
			String hostId = CacheManager.getInstance().getHostId();
			// Get the user id from SwtUtil
			String userId = SwtUtil.getCurrentUserId(request.getSession());
			// Get the screen id from SwtConstants
			String itemId = SwtConstants.ENTITY_MONITOR_ID;
			// set the hostId
			request.setAttribute("hostId", hostId);
			// set the userId
			request.setAttribute("userId", userId);
			// set the itemId
			request.setAttribute("itemId", itemId);
			// Code modified by Vivekanandan a on 20120710 for Mantis 1991 Start
			// set the dbdate
			request.setAttribute("dbDate", SwtUtil
					.getSysParamDateWithEntityOffset(SwtUtil.getCurrentUser(
							request.getSession()).getCurrentEntity()));
			// Code modified by Vivekanandan a on 20120710 for Mantis 1991 END
			// Calling this method for getting the font size from the request.
			setFontSize(request);
			logger.debug(this.getClass().getName() + " - [flex] - Exit ");
		} catch (Exception exception) {
			errorMessage = exception.getStackTrace()[0].getClassName() + "."
					+ exception.getStackTrace()[0].getMethodName() + ":"
					+ exception.getStackTrace()[0].getLineNumber() + " "
					+ exception.getMessage();
			logger.error(this.getClass().getName() + " - flex(). Exception : "
					+ errorMessage);
		}
		return ("flexobject");
	}

	/**
	 * Loads the personal entity flex object into the client
	 * 
	 * @param ActionMapping
	 *            mapping
	 * @param ActionForm
	 *            form
	 * @param HttpServletRequest
	 *            request
	 * @param HttpServletResponse
	 *            response
	 * @throws SwtException
	 */
	public String personalEntityFlex() throws SwtException {
		// variable to hold error message
		String errorMessage = null;
		// Holds the Host Id
		String hostId = null;
		// Holds the User Id
		String userId = null;
		// Holds the Item Id
		String itemId = null;
		HttpServletRequest request = ServletActionContext.getRequest();
    	HttpServletResponse response = ServletActionContext.getResponse();
		try {
			logger.debug(this.getClass().getName()
					+ " - [personalEntityFlex] - Entering ");
			// Get the host id from CacheManager
			hostId = CacheManager.getInstance().getHostId();
			// Get the user id from SwtUtil
			userId = SwtUtil.getCurrentUserId(request.getSession());
			// Get the screen id from SwtConstants
			itemId = SwtConstants.ENTITY_MONITOR_ID;
			// set the hostId
			request.setAttribute("hostId", hostId);
			// set the userId
			request.setAttribute("userId", userId);
			// set the itemId
			request.setAttribute("itemId", itemId);
			logger.debug(this.getClass().getName()
					+ " - [personalEntityFlex] - Exit ");
		} catch (Exception exception) {
			errorMessage = exception.getStackTrace()[0].getClassName() + "."
					+ exception.getStackTrace()[0].getMethodName() + ":"
					+ exception.getStackTrace()[0].getLineNumber() + " "
					+ exception.getMessage();
			logger.error(this.getClass().getName()
					+ " - personalEntityFlex(). Exception : " + errorMessage);
		}
		return ("entityflexobject");
	}

	/**
	 * Loads the personal currency flex object into the client
	 * 
	 * @param ActionMapping
	 *            mapping
	 * @param ActionForm
	 *            form
	 * @param HttpServletRequest
	 *            request
	 * @param HttpServletResponse
	 *            response
	 * @throws SwtException
	 */
	public String personalCurrencyFlex() throws SwtException {

		// variable to hold error message
		String errorMessage = null;
		// Holds the Host Id
		String hostId = null;
		// Holds the User Id
		String userId = null;
		// Holds the Item Id
		String itemId = null;
		HttpServletRequest request = ServletActionContext.getRequest();
    	HttpServletResponse response = ServletActionContext.getResponse();
		try {
			logger.debug(this.getClass().getName()
					+ " - [personalCurrencyFlex] - Entering ");
			// Get the host id from CacheManager
			hostId = CacheManager.getInstance().getHostId();
			// Get the user id from SwtUtil
			userId = SwtUtil.getCurrentUserId(request.getSession());
			// Get the screen id from SwtConstants
			itemId = SwtConstants.ENTITY_MONITOR_ID;
			// set the hostId
			request.setAttribute("hostId", hostId);
			// set the userId
			request.setAttribute("userId", userId);
			// set the itemId
			request.setAttribute("itemId", itemId);
			logger.debug(this.getClass().getName()
					+ " - [personalCurrencyFlex] - Exit ");
		} catch (Exception exception) {
			errorMessage = exception.getStackTrace()[0].getClassName() + "."
					+ exception.getStackTrace()[0].getMethodName() + ":"
					+ exception.getStackTrace()[0].getLineNumber() + " "
					+ exception.getMessage();
			logger.error(this.getClass().getName()
					+ " - personalCurrencyFlex(). Exception : " + errorMessage);
		}
		return ("currencyflexobject");
	}

	/**
	 * This method is called when the response is returned to flex screen and
	 * then submitted.
	 * 
	 * @param ActionMapping
	 *            mapping
	 * @param ActionForm
	 *            form
	 * @param HttpServletRequest
	 *            request
	 * @param HttpServletResponse
	 *            response
	 * @return ActionForward
	 * 
	 * @throws SwtException
	 */
	public String execute() throws Exception {
	    HttpServletRequest request = ServletActionContext.getRequest();

	    logger.debug("Enter into logonActon.'unspecified' method");

	    // List of methods "reLogin", "login", "loginFail", "preLoginScreen", "preLoginScreenData"
	    String method = String.valueOf(request.getParameter("method"));

	    switch (method) {
	        case "flex":
	            return flex();
	        case "unspecified":
	        	return displayEntityMonitorDetails();
	        case "personalEntityFlex":
	            return personalEntityFlex();
	        case "personalCurrencyFlex":
	            return personalCurrencyFlex();
	        case "displayEntityMonitorDetails":
	            return displayEntityMonitorDetails();
	        case "displayEntityMonitorOptions":
	            return displayEntityMonitorOptions();
	        case "saveEntityMonitorOptions":
	            return saveEntityMonitorOptions();
	        case "displayPersonalEntityList":
	            return displayPersonalEntityList();
	        case "displayAddSumPersonalEntityList":
	            return displayAddSumPersonalEntityList();
	        case "savePersonalEntityList":
	            return savePersonalEntityList();
	        case "displayPersonalCurrencyList":
	            return displayPersonalCurrencyList();
	        case "flexOption":
	            return flexOption();
	        case "savePersonalCurrencyList":
	            return savePersonalCurrencyList();
	        case "saveColumnWidth":
	            return saveColumnWidth();
	        case "savePersonalSumEntityList":
	            return savePersonalSumEntityList();
	        case "deletePersonalSumEntityList":
	            return deletePersonalSumEntityList();
	        default:
	            break;
	    }

	    return null;
	}


	/**
	 * This method is used across this action class to add the currency group
	 * list in request
	 * 
	 * @param HttpServletRequest
	 *            request
	 * @param String
	 *            hostId
	 * @param String
	 *            roleId
	 * @return String
	 * 
	 * @throws SwtException
	 */
	private void putCcyGroupDetailsInReuest(HttpServletRequest request,
			String hostId, String roleId) throws SwtException {
		// variable to hold error message
		String errorMessage = null;
		// Collection to hold the currency group list
		Collection<CurrencyGroup> currencyGroupList = null;
		// ArrayList to hold the currency group list
		List<LabelValueBean> currencyGroupDetails = null;
		// map to hold the currency group id & currency group name
		HashMap<String, String> map = null;
		// iterator used to iterate the currency group details
		Iterator<CurrencyGroup> ite = null;
		try {
			logger.debug(this.getClass().getName()
					+ " - [putCcyGroupDetailsInRequest] - Enter");
			// Instantiate the collection
			currencyGroupList = new ArrayList<CurrencyGroup>();
			// Instantiate the ArrayList
			currencyGroupDetails = new ArrayList<LabelValueBean>();
			// Instantiate the HashMap
			map = new HashMap<String, String>();
			// Get the currency group list from entityMonotorManager
			currencyGroupList = entityMonitorManager
					.getCurrencyGroupAccessDetails(hostId, roleId);
			// Iterate through the collection
			if (currencyGroupList != null) {
				if (currencyGroupList.size() != 0) {
					ite = currencyGroupList.iterator();
					while (ite.hasNext()) {
						// Type casting CurrencyGroup with Iterator
						CurrencyGroup currGroup = (CurrencyGroup) ite.next();
						// Instantiating LabelValueBean with the required
						// parameters
						if (!map.containsKey(currGroup.getId()
								.getCurrencyGroupId())) {
							LabelValueBean labelValueBean = new LabelValueBean(
									currGroup.getId().getCurrencyGroupId(),
									currGroup.getCurrencyGroupName());
							currencyGroupDetails.add(labelValueBean);
						}
						map.put(currGroup.getId().getCurrencyGroupId(),
								currGroup.getCurrencyGroupName());
					}
				}
				// add the label value bean to currency group details
				currencyGroupDetails.add(0, new LabelValueBean(
						SwtConstants.CURRENCY_GROUP_ALL,
						SwtConstants.CURRENCY_GROUP_ALL));
			}
			// Set the currency group list in request
			request.setAttribute("currencyGroupList", currencyGroupDetails);
			logger.debug(this.getClass().getName()
					+ " - [putCcyGroupDetailsInRequest] - Exit");
		} catch (Exception exception) {
			errorMessage = exception.getStackTrace()[0].getClassName() + "."
					+ exception.getStackTrace()[0].getMethodName() + ":"
					+ exception.getStackTrace()[0].getLineNumber() + " "
					+ exception.getMessage();
			logger.error(this.getClass().getName()
					+ " - putCcyGroupDetailsInRequest(). Exception : "
					+ errorMessage);
			// Re-throwing SwtException
			throw SwtErrorHandler.getInstance().handleException(exception,
					"putCcyGroupDetailsInReuest", EntityMonitorAction.class);
		}
	}

	// START Code modified by Vivekanandan A for Mantis 1991 on 09-07-2012
	/**
	 * This method calls the Manager class to get the Entity monitor details and
	 * forwards the request to display it.<br>
	 * 
	 * @param ActionMapping
	 *            mapping
	 * @param ActionForm
	 *            form
	 * @param HttpServletRequest
	 *            request
	 * @param HttpServletResponse
	 *            response
	 * @return ActionForward
	 * @throws SwtException
	 */
	public String displayEntityMonitorDetails() throws SwtException {
		/* Variable Declaration for opTimer */
		OpTimer opTimer = null;
		/* Variable Declaration for SystemFormats */
		SystemFormats systemFormat = null;
		/* Variable Declaration for hostId */
		String hostId = null;
		/* Variable Declaration for userId */
		String userId = null;
		/* Variable Declaration for screenId */
		String screenId = null;
		/* Variable Declaration for entityMonitorOption */
		ScreenOption entityMonitorOption = null;
		/* Variable Declaration for scrOption */
		ScreenOption scrOption = null;
		/* Variable Declaration for startDate */
		String startDate = null;
		/* Variable Declaration for currGrp */
		String currGrp = null;
		/* Variable Declaration for user */
		User user = null;
		/* Variable Declaration for entityMonitor */
		EntityMonitorForm entityMonitor = null;
		/* Variable Declaration for session */
		HttpSession session = null;
		/* Variable Declaration for collMonitorDetails */
		EntityRecord entityMonitorDetails = null;
		/* Variable Declaration for totals */
		HashMap<Integer, BigDecimal> totals = null;
		/* Variable Declaration for currentDate */
		Date currentDate = null;
		/* Variable Declaration for date */
		Date balanceDate = null;
		/* Variable Declaration for dateColl */
		Collection<BalanceDateTO> dateColl = null;
		/* Variable Declaration for currDate */
		Calendar currDate = null;
		/* Variable Declaration for balDate */
		BalanceDateTO balDate = null;
		/* Variable Declaration for totalColl */
		Collection<BalanceDateTO> totalColl = null;
		/* Variable Declaration for entityManager */
		EntityManager entityManager = null;
		/* Variable Declaration for entity */
		Entity entity = null;
		/* Variable Declaration for entityFetched */
		Entity entityFetched = null;
		/* Variable Declaration for autoRefreshRate */
		int autoRefreshRate = 0;
		/* Variable Declaration for roleId */
		String roleId = null;
		/* Variable Declaration for sysDate */
		String sysDate = null;
		/* Variable Declaration for currencyManager */
		CurrencyManager currencyManager = null;
		/* Variable Declaration for ccy */
		Currency ccy = null;
		/* Variable Declaration for tempCurrencyCode */
		String tempCurrencyCode = null;
		/* Variable Declaration for tempEntity */
		String tempEntity = null;
		/* Variable Declaration for CurrencyDetailVO */
		CurrencyDetailVO currencyDetailVO = null;
		// iterator used to iterate currencies details
		// and get the currency multiplier
		Iterator<Currency> multiplierItr = null;
		// Calendar object
		Calendar calDate = null;
		// To hold the system date
		Date systemDBDate = null;
		HttpServletRequest request = ServletActionContext.getRequest();
    	HttpServletResponse response = ServletActionContext.getResponse();
		try {
			logger.debug("Enter " + this.getClass().getName()
					+ " - [displayEntityMonitorDetails]");
			// create instance for currency
			ccy = new Currency();
			// create instance for OpTimer
			opTimer = new OpTimer();
			opTimer.start(SwtConstants.OPTIMER_START_ALL);

			//Nadia
			// get the entityMonitor for object
			if(this.entityMonitorForm ==null)
				if(this.entityMonitor != null)
					entityMonitor = this.entityMonitor;
				else
					entityMonitor = new EntityMonitorForm();
			else 
				entityMonitor = this.entityMonitorForm;
			
			// get the Current System Format
			systemFormat = SwtUtil
					.getCurrentSystemFormats(request.getSession());
			// Get the date from the entity monitor bean
			startDate = entityMonitor.getCurrentDateAsString();
			// Get the currency group from entity monitor bean
			currGrp = entityMonitor.getCurrGrp();
			// Get the host id from CacheManager
			hostId = CacheManager.getInstance().getHostId();
			// Get the user from SwtUtil
			userId = SwtUtil.getCurrentUserId(request.getSession());
			// Get the screen id from SwtConstants
			screenId = SwtConstants.ENTITY_MONITOR_ID;
			// Initializing the screen option instance
			scrOption = new ScreenOption();
			// Setting the hostId
			scrOption.getId().setHostId(hostId);
			// Setting the userId
			scrOption.getId().setUserId(userId);
			// Setting the screenId
			scrOption.getId().setScreenId(screenId);
			// Creating a ScreenOptionManager instance
			ScreenOptionManager screenOptionManager = (ScreenOptionManager) (SwtUtil
					.getBean("screenOptionManager"));
			// set the scrOption to entityMonitorOption
			entityMonitorOption = screenOptionManager
					.getEntityMonitorOptions(scrOption);
			// Make an instance of calendar
			calDate = Calendar.getInstance();
			// get the system date.

			systemDBDate = SwtUtil.getSysParamDateWithEntityOffset(SwtUtil
					.getCurrentUser(request.getSession()).getCurrentEntity());

			// The following condition is executed when the start date is
			// null. The start date is null only when the screen is loaded for
			// the first time. All other times, the start date is populated in
			// the form bean as the start date is null, get the system date as
			// start date
			if (SwtUtil.isEmptyOrNull(startDate)) {
				startDate = SwtUtil.getSysDateWithFmt(systemDBDate);

			} else {
				if ((!SwtUtil
						.isEmptyOrNull(entityMonitor.getEntityOffsetTime()) && entityMonitor
						.getEntityOffsetTime().equals("true"))) {
					startDate = SwtUtil.getSysDateWithFmt(systemDBDate);
				}

			}
			// Set the start date as calendar date
			calDate.setTime(SwtUtil.parseDate(startDate, systemFormat
					.getDateFormatValue()));
			// format the date
			startDate = SwtUtil.formatDate(calDate.getTime(), systemFormat
					.getDateFormatValue());
			// Setting the values of start date and end date as Date and String
			// values respectively to form
			entityMonitor.setCurrentDateAsString(startDate);
			// set the date to form object
			entityMonitor.setCurrentDate(SwtUtil.parseDate(startDate,
					systemFormat.getDateFormatValue()));
			// If the break down radio button is null, set to default Account
			// Breakdown get the session
			session = request.getSession();
			// Get the current user from SwtUtil
			user = SwtUtil.getCurrentUser(session);
			// Get the role Id associated to the user
			roleId = user.getRoleId();
			// If currency group is null, populate it from session
			// get the currency group details
			// If currency group is null, populate it from session
			if (currGrp == null) {
				currGrp = ((CommonDataManager) request.getSession()
						.getAttribute(SwtConstants.CDM_BEAN)).getUser()
						.getCurrentCcyGrpId();
			}

			// set the currency group details
			putCcyGroupDetailsInReuest(request, hostId, roleId);
			// Add the currency group in form
			entityMonitor.setCurrGrp(currGrp);
			// Get the current use from SwtUtil
			user = SwtUtil.getCurrentUser(session);
			// Call the entityMonitorManagerImpl and get the entity monitor
			// details
			entityMonitorDetails = entityMonitorManager
					.getEntityMonitorDetails(
							currGrp,
							userId,
							startDate,
							systemFormat,
							opTimer,
							SwtUtil.isEmptyOrNull(request
									.getParameter("currencyCode")) ? null
									: request.getParameter("currencyCode")
											.substring(0, 3),
							(!SwtUtil.isEmptyOrNull(entityMonitor
									.getEntityOffsetTime()) && entityMonitor
									.getEntityOffsetTime().equals("false")) ? "N"
									: "Y");
			// Check the job flag status, set to request
			if (!SwtUtil.isEmptyOrNull(entityMonitorDetails.getJobFlagStatus())
					&& entityMonitorDetails.getJobFlagStatus().equals(
							"jobFlagStatusTrue")) {
				request.setAttribute("jobFlagStatus", SwtConstants.YES);
				entityMonitorDetails.setJobFlagStatus("");
			}
			// set the entity records
			entityMonitor.setEntityRecords(entityMonitorDetails
					.getCurrencyMonitorRecords());
			// Get the totals
			totals = (HashMap<Integer, BigDecimal>) entityMonitorDetails
					.getTotalCurrencyRecords();

			// Form the currentDate
			currentDate = SwtUtil.parseDate(startDate, systemFormat
					.getDateFormatValue());
			// Calculate the currentDate
			currDate = Calendar.getInstance();
			currDate.setTime(currentDate);
			balanceDate = currDate.getTime();
			// initialize the list
			dateColl = new ArrayList<BalanceDateTO>();
			currDate.setTime(currentDate);
			balanceDate = currDate.getTime();
			// Create an instance of entity manager
			entityManager = (EntityManager) (SwtUtil.getBean("entityManager"));
			// Create a instance of entity
			entity = new Entity();
			// Set the host id in the entity
			entity.getId().setHostId(CacheManager.getInstance().getHostId());
			// Set the host id in the entity
			entity.getId().setEntityId(
					SwtUtil.getUserCurrentEntity(request.getSession()));
			// Set the entity id in the entity
			entityFetched = entityManager.getEntityDetail(entity);
			// if reporting currency is empty then take the default entity
			// reporting currency
			if (SwtUtil.isEmptyOrNull(entityMonitorOption.getReportCurrency())) {
				tempCurrencyCode = SwtUtil.getReportingCurrencyForEntity(
						hostId, SwtUtil.getUserCurrentEntity(session));
			}
			// if reporting currency is not empty then take the
			// entityMonitorOption
			else if (!entityMonitorOption.getReportCurrency().equals(
					SwtConstants.NO)) {
				tempCurrencyCode = entityMonitorOption.getReportCurrency();
			}
			// set the default entity reporting currency
			else {
				tempCurrencyCode = SwtUtil.getReportingCurrencyForEntity(
						hostId, SwtUtil.getUserCurrentEntity(session));
			}
			// if currency multiplier is empty then take the default entity
			if (SwtUtil.isEmptyOrNull(entityMonitorOption
					.getUseCurrencyMultiplier())) {
				tempEntity = null;
			}
			// if currency multiplier is not empty then take entity from
			// entityMonitorOption
			else if (!entityMonitorOption.getUseCurrencyMultiplier().equals(
					SwtConstants.NO)) {
				tempEntity = entityMonitorOption.getUseCurrencyMultiplier();
			} else {
				tempEntity = null;
			}
			if (tempEntity != null) {
				// get the currencyManager object
				currencyManager = (CurrencyManager) SwtUtil
						.getBean("currencyManager");
				// get the currency details
				currencyDetailVO = currencyManager.getCurrencyDetailList(
						tempEntity, hostId, tempCurrencyCode);
				multiplierItr = currencyDetailVO.getCurrencyListDetails()
						.iterator();
				if (multiplierItr.hasNext()) {
					// get the currency details
					ccy = (Currency) multiplierItr.next();
				}
			}
			if (!SwtUtil.isEmptyOrNull(entityMonitorOption.getReportCurrency())
					&& entityMonitorOption.getReportCurrency().equals(
							SwtConstants.NO)) {
				// Get the entity detail and set the domestic currency of the
				// entity
				// in request
				request.setAttribute("reportingCurrency", entityFetched
						.getReprotingCurrency()
						+ ((SwtUtil.isEmptyOrNull(ccy.getMultiplier()) || ccy
								.getMultiplier().equals(SwtConstants.NO)) ? ""
								: "(" + ccy.getMultiplier() + ")"));

				entityMonitorOption.setReportCurrency("("
						+ entityFetched.getReprotingCurrency() + ")");
			} else if (SwtUtil.isEmptyOrNull(entityMonitorOption
					.getReportCurrency())) {
				// setting reporting currency in request
				request.setAttribute("reportingCurrency", entityFetched
						.getReprotingCurrency()
						+ ((SwtUtil.isEmptyOrNull(ccy.getMultiplier()) || ccy
								.getMultiplier().equals(SwtConstants.NO)) ? ""
								: "(" + ccy.getMultiplier() + ")"));
				entityMonitorOption.setReportCurrency("("
						+ entityFetched.getReprotingCurrency() + ")");
			} else {
				// setting reporting currency in request
				request.setAttribute("reportingCurrency", entityMonitorOption
						.getReportCurrency()
						+ ((SwtUtil.isEmptyOrNull(ccy.getMultiplier()) || ccy
								.getMultiplier().equals(SwtConstants.NO)) ? ""
								: "(" + ccy.getMultiplier() + ")"));
			}
			// set the total in request
			if (!SwtUtil.isEmptyOrNull(request.getParameter("currencyCode"))) {
				request.setAttribute("viewTotal", SwtConstants.NO);
			} else {
				request.setAttribute("viewTotal", SwtConstants.YES);
			}
			// getting system date from request
			sysDate = request.getParameter("systemDate");
			// if system date not null, get the db date
			sysDate = ((sysDate != null) && (sysDate.trim().length() > 0)) ? sysDate
					: SwtUtil.getSystemDateString();
			// setting the system date to request
			request.setAttribute("sysDate", sysDate);
			// Creating a object for BalanceDateTO
			balDate = new BalanceDateTO();
			// Creating a new array list to hold totals
			totalColl = new ArrayList<BalanceDateTO>();
			// Loop for total columns
			for (int i = 0; i < totals.size(); i++) {

				balDate = new BalanceDateTO();
				// Set the balance in the BalanceDataTO
				balDate.setBalanceOrgDateAsString(SwtUtil.formatDate(
						balanceDate, systemFormat.getDateFormatValue()));

				// check total value is not null
				if ((BigDecimal) totals.get(i) != null) {

					// Condition to check currency multiplier is set
					if (entityMonitorDetails.getCurrencyMultiplier().equals(
							SwtConstants.YES)) {
						// Set decimal format with one decimal
						balDate.setPredBalanceAsString(SwtUtil.formatCurrency(
								SwtConstants.YES, (BigDecimal) totals.get(i)));
					} else {
						// set decimal format with two decimals
						// Set the predicted balance as string in BalanceDateTO
						if (entityMonitorOption.getReportCurrency().contains(
								"(")) {

							balDate.setPredBalanceAsString(SwtUtil
									.formatCurrency(entityMonitorOption
											.getReportCurrency()
											.substring(1, 4),
											(BigDecimal) totals.get(i)));

						} else {
							// setting predict balance
							balDate.setPredBalanceAsString(SwtUtil
									.formatCurrency(entityMonitorOption
											.getReportCurrency()
											.substring(0, 3).trim(),
											(BigDecimal) totals.get(i)));

						}
					}

					// Set the predicted balance in BalanceDateTO
					balDate.setPredBalance(new BigDecimal(totals.get(i)
							.toString()));
					// If the balance is negative set the symbol to negative
					if (balDate.getPredBalance() != null
							&& balDate.getPredBalance().doubleValue() < 0) {
						balDate.setPredBalanceNegative(true);
					}
				}
				// Add the totals and dateColl to collection
				totalColl.add(balDate);
				dateColl.add(balDate);
			}
			// Set the totals in the bean and respective attributes
			entityMonitor.setTotals(totalColl);
			// set the entityMonitor
			//Nadia
			//dyForm.set("entityMonitor", entityMonitor);
			request.setAttribute("balanceDate", dateColl);
			// Get the auto refresh rate
			autoRefreshRate = Integer.parseInt(entityMonitorOption
					.getPropertyValue());
			// set the attribute values
			request.setAttribute("autoRefreshRate", autoRefreshRate);
			// Calling the helper method for getting the font size from the
			// request.
			setFontSize(request);
			// setting reply status as true in request
			request.setAttribute("reply_status_ok", SwtConstants.STR_TRUE);
			// setting the reply message in request
			request.setAttribute("reply_message", SwtConstants.DATA_FETCH_OK);
			/* Set Last Reference Time in request. */
			request.setAttribute("lastRefTime", SwtUtil.getLastRefTimeOnGMTOffset(request,
					SwtUtil.getUserCurrentEntity(request.getSession())));
			// set the column width
			this.entityMonitorForm = entityMonitor;
			this.entityMonitor = entityMonitor;
			
			this.bindColumnWidthInRequest(request);
			opTimer.stop(SwtConstants.OPTIMER_STOP_ALL);
			request.setAttribute("opTimes", opTimer.getOpTimes());
			logger.debug("Exit " + this.getClass().getName()
					+ " - [displayEntityMonitorDetails]");
		} catch (SwtException e) {
			String message = e.getStackTrace()[0].getClassName() + "."
					+ e.getStackTrace()[0].getMethodName() + ":"
					+ e.getStackTrace()[0].getLineNumber() + " "
					+ e.getMessage();
			logger.error(this.getClass().getName()
					+ " - [displayEntityMonitorDetails] - Exception -"
					+ message);
			request.setAttribute("balanceDate", new ArrayList<BalanceDateTO>());
			request.setAttribute("reply_status_ok", SwtConstants.STR_FALSE);
			request.setAttribute("reply_message", e.getMessage());
			request.setAttribute("reply_location", e.getStackTrace()[0]
					.getClassName()
					+ "."
					+ e.getStackTrace()[0].getMethodName()
					+ ":"
					+ e.getStackTrace()[0].getLineNumber());
			SwtUtil.logException(e, request, "");
			return ("dataerror");
		} catch (Exception e) {
			String message = e.getStackTrace()[0].getClassName() + "."
					+ e.getStackTrace()[0].getMethodName() + ":"
					+ e.getStackTrace()[0].getLineNumber() + " "
					+ e.getMessage();
			logger.error(this.getClass().getName()
					+ " - [displayEntityMonitorDetails] - Exception -"
					+ message);
			request.setAttribute("balanceDate", new ArrayList<BalanceDateTO>());
			SwtUtil.logException(SwtErrorHandler.getInstance()
					.handleException(e, "displayEntityMonitorDetails",
							EntityMonitorAction.class), request, "");
			request.setAttribute("reply_status_ok", SwtConstants.STR_FALSE);
			request.setAttribute("reply_message", e.getMessage());
			request.setAttribute("reply_location", e.getStackTrace()[0]
					.getClassName()
					+ "."
					+ e.getStackTrace()[0].getMethodName()
					+ ":"
					+ e.getStackTrace()[0].getLineNumber());
			return ("dataerror");
		} finally {
			// null the objects created already
			hostId = null;
			userId = null;
			screenId = null;
			entityMonitorOption = null;
			startDate = null;
			currGrp = null;
			user = null;
			balanceDate = null;
			totals = null;
			entityManager = null;
			entity = null;
			entityMonitorDetails = null;
			dateColl = null;
			totalColl = null;
			multiplierItr = null;
			currencyDetailVO = null;
			systemDBDate = null;
		}
		return ("success");
	}

	// END Code modified by Vivekanandan A for Mantis 1991 on 09-07-2012

	/**
	 * This method is used to display existing options for the entity monitor(if
	 * any) Click on the "Options" button from the entity monitor screen
	 * 
	 * @param ActionMapping
	 *            mapping
	 * @param ActionForm
	 *            form
	 * @param HttpServletRequest
	 *            request
	 * @param HttpServletResponse
	 *            response
	 * 
	 * @return ActionForward
	 * 
	 * @throws SwtException
	 */
	public String displayEntityMonitorOptions() throws SwtException {
		// Variable Declaration for autoRefreshRate
		int autoRefreshRate = 0;
		// String variable to hold host id
		String hostId = null;
		// String variable to hold user id
		String userId = null;
		// ScreenOption object
		ScreenOption screenOption = null;
		// session object
		HttpSession session = null;
		// setting the currency multiplier
		Collection<EntityUserAccess> coll = null;
		// Entity object
		Entity entity = null;
		// EntityManager object
		EntityManager entityManager = null;
		// ScreenOption object
		ScreenOption scOption = null;
		// Collection to hold entity user access
		Collection<LabelValueBean> entityAccess = null;
		HttpServletRequest request = ServletActionContext.getRequest();
    	HttpServletResponse response = ServletActionContext.getResponse();
		try {
			logger.debug(this.getClass().getName()
					+ " - [displayEntityMonitorOptions] - Entry");
			// Instantiating Entity object
			entity = new Entity();
			// Get host id from the CacheManager
			hostId = CacheManager.getInstance().getHostId();
			// Get user id from session
			userId = SwtUtil.getCurrentUserId(request.getSession());
//Nadia
			screenOption = this.entityMonitorOptions;
			// Initializing the screen option instance
			screenOption = new ScreenOption();
			// Setting the hostId
			screenOption.getId().setHostId(hostId);
			// Setting the userId
			screenOption.getId().setUserId(userId);
			// Setting the screenId
			screenOption.getId().setScreenId(SwtConstants.ENTITY_MONITOR_ID);
			// setting the reporting currency from the CacheManager
			CacheManager cacheManagerInst = CacheManager.getInstance();
			screenOption
					.setReportCurrencyList(cacheManagerInst.getCurrencies());
			session = request.getSession();
			// setting currency list
			putCurrencyListInReq(request);

			// Getting the session from the request
			session = request.getSession();

			/* Collects the list of entity for the user */
			coll = SwtUtil.getUserEntityAccessList(session);

			/* Collects the label value bean for entity name and entity id */
			entityAccess = SwtUtil.convertEntityAcessCollectionLVL(coll,
					session);
			// Creating a ScreenOptionManager instance
			ScreenOptionManager screenOptionManager = (ScreenOptionManager) (SwtUtil
					.getBean("screenOptionManager"));
			// Creating an instance of EntityManager
			entityManager = (EntityManager) (SwtUtil.getBean("entityManager"));
			// Setting entity Id in Entity bean
			entity.getId().setEntityId(SwtUtil.getUserCurrentEntity(session));
			// Setting host Id in Entity bean
			entity.getId().setHostId(hostId);
			// create instance for ScreenOption
			scOption = new ScreenOption();
			// Get the entity monitor option values
			scOption = screenOptionManager
					.getEntityMonitorOptions(screenOption);
			// set the reporting Currency collection to request
			request.setAttribute("reportingCurr", entityAccess);
			if (!SwtUtil.isEmptyOrNull(scOption.getReportCurrency())
					&& scOption.getReportCurrency().equals(SwtConstants.NO)) {
				entity = entityManager.getEntityDetail(entity);
				// Setting the default reporting currency from entity bean to
				// screenOption bean.
				scOption.setReportCurrency(entity.getReprotingCurrency());
			}
			if (SwtUtil.isEmptyOrNull(scOption.getReportCurrency())) {
				scOption.setReportCurrency(SwtConstants.NO);
			}
			// Setting the entity monitor options to form
			//Nadia
			//dyForm.set("entityMonitorOptions", scOption);
			request.setAttribute("userId", userId);
			request.setAttribute("hostId", hostId);
			request.setAttribute("itemId", screenOption.getId().getScreenId());
			// Get the auto refresh rate
			autoRefreshRate = Integer.parseInt(scOption.getPropertyValue());
			// set the attribute values
			request.setAttribute("autoRefreshRate", autoRefreshRate);
			// Calling the helper method for getting the font size from the
			// request.
			setFontSize(request);
			this.entityMonitorOptions = scOption;
			request.setAttribute("entityMonitorOptions", scOption);
			request.setAttribute("reply_status_ok", SwtConstants.STR_TRUE);
			request.setAttribute("reply_message", SwtConstants.DATA_FETCH_OK);
			logger.debug(this.getClass().getName()
					+ " - [displayEntityMonitorOptions] - Exit");
		} catch (SwtException e) {
			logger.error(this.getClass().getName()
					+ " - [displayEntityMonitorOptions] - SwtException -"
					+ e.getMessage());
			SwtUtil.logException(e, request, "");
			request.setAttribute("reply_status_ok", SwtConstants.STR_FALSE);
			request.setAttribute("reply_message", e.getMessage());
			request.setAttribute("reply_location", e.getStackTrace()[0]
					.getClassName()
					+ "."
					+ e.getStackTrace()[0].getMethodName()
					+ ":"
					+ e.getStackTrace()[0].getLineNumber());
			return ("dataerror");
		} catch (Exception e) {
			logger.error(this.getClass().getName()
					+ " - [displayEntityMonitorOptions] - GenericException -"
					+ e.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance()
					.handleException(e, "displayEntityMonitorOptions",
							EntityMonitorAction.class), request, "");
			request.setAttribute("reply_status_ok", SwtConstants.STR_FALSE);
			request.setAttribute("reply_message", e.getMessage());
			request.setAttribute("reply_location", e.getStackTrace()[0]
					.getClassName()
					+ "."
					+ e.getStackTrace()[0].getMethodName()
					+ ":"
					+ e.getStackTrace()[0].getLineNumber());
			return ("dataerror");
		} finally {
			// Cleaning the Unreferenced objects
			hostId = null;
			userId = null;
			entityAccess = null;
			// closing the session
			if (session != null) {
				session = null;
			}
			// Nullifies the Non-referenced Collection object
			if (coll != null) {
				coll = null;
			}
		}
		return ("options");
	}

	/**
	 * This method is used to save the options or preferences in entity monitor
	 * screen. From entity monitor screen, click on "Options" set/reset the
	 * preference and "Save". This method is called on the save action.
	 * 
	 * @param ActionMapping
	 *            mapping
	 * @param ActionForm
	 *            form
	 * @param HttpServletRequest
	 *            request
	 * @param HttpServletResponse
	 *            response
	 * @return ActionForward
	 * @throws SwtException
	 */
	private ScreenOption entityMonitorOptions;
	
	public ScreenOption getEntityMonitorOptions() {
		return entityMonitorOptions;
	}
	public void setEntityMonitorOptions(ScreenOption entityMonitorOptions) {
		this.entityMonitorOptions = entityMonitorOptions;
	}
	/**
	 * This method is used to save the options or preferences in entity monitor
	 * screen. From entity monitor screen, click on "Options" set/reset the
	 * preference and "Save". This method is called on the save action.
	 * 
	 * @param ActionMapping
	 *            mapping
	 * @param ActionForm
	 *            form
	 * @param HttpServletRequest
	 *            request
	 * @param HttpServletResponse
	 *            response
	 * @return ActionForward
	 * @throws SwtException
	 */
	public String saveEntityMonitorOptions() throws SwtException {
		// ScreenOption object
				ScreenOption screenOption = null;
				// String object to hold hostId
				String hostId = null;
				// String object to hold userId
				String userId = null;
				// DynaValidatorForm object
				// StringBuffer object
				StringBuffer sb = null;
				// ScreenOptionManager object
				ScreenOptionManager screenOptionManager = null;
				HttpServletRequest request = ServletActionContext.getRequest();
		    	HttpServletResponse response = ServletActionContext.getResponse();
				try {
					logger.debug(this.getClass().getName()
							+ " - [saveEntityMonitorOptions] - Entry");
					// create instance for StringBuffer
					sb = new StringBuffer();
					// Object for DynaValidatorForm
					// Getting the AutoRefresh bean object from DynaValidatorForm
					screenOption = (ScreenOption) this.entityMonitorOptions;
					// Get the host id from CacheManager
					hostId = CacheManager.getInstance().getHostId();
					// Get user id from SwtUtil
					userId = SwtUtil.getCurrentUserId(request.getSession());
					// Set the host id in the ScreenOption form
					screenOption.getId().setHostId(hostId);
					// Set the user id in ScreenOption form
					screenOption.getId().setUserId(userId);
					// Set the screen id in ScreenOption form
					screenOption.getId().setScreenId(SwtConstants.ENTITY_MONITOR_ID);

					// Creating a ScreenOptionManager instance
					screenOptionManager = (ScreenOptionManager) (SwtUtil
							.getBean("screenOptionManager"));
					// Save the entity monitor option changes, by calling the
					// EntityMonitorNewManagerImpl
					screenOptionManager.saveEntityMonitorOptions(screenOption);
					// Set the request attribute to refresh the entity monitor screen
					// set the refresh flag
					request.setAttribute("parentFormRefresh", SwtConstants.YES);
					// Appending the success message in the StringBuffer
					sb.append("<?xml version=\"1.0\" encoding=\"UTF-8\"?> ");
					sb.append("<EntityMonitor>");
					sb.append("<request_reply><status_ok>true"
							+ "</status_ok><message>true"
							+ "</message></request_reply>");
					sb.append("</EntityMonitor>");
					// Setting the success message in response
					response.setContentType("text/xml");
					response.getOutputStream().println(sb.toString());
					logger.debug(this.getClass().getName()
							+ " - [saveEntityMonitorOptions] - Exit");
					return null;
				} catch (SwtException swtexp) {
					logger.error(this.getClass().getName()
							+ " - [saveEntityMonitorOptions] - SwtException -"
							+ swtexp.getMessage());
					request.setAttribute("reply_status_ok", SwtConstants.STR_FALSE);
					request.setAttribute("reply_message", swtexp.getMessage());
					request.setAttribute("reply_location", swtexp.getStackTrace()[0]
							.getClassName()
							+ "."
							+ swtexp.getStackTrace()[0].getMethodName()
							+ ":"
							+ swtexp.getStackTrace()[0].getLineNumber());
					SwtUtil.logException(swtexp, request, "");
					return ("success");
				} catch (Exception exp) {
					logger.error(this.getClass().getName()
							+ " - [saveEntityMonitorOptions] - GenericException -"
							+ exp.getMessage());
					SwtUtil.logException(
							SwtErrorHandler.getInstance().handleException(exp,
									"saveEntityMonitorOptions",
									EntityMonitorAction.class), request, "");
					request.setAttribute("reply_status_ok", SwtConstants.STR_FALSE);
					request.setAttribute("reply_message", exp.getMessage());
					request.setAttribute("reply_location", exp.getStackTrace()[0]
							.getClassName()
							+ "."
							+ exp.getStackTrace()[0].getMethodName()
							+ ":"
							+ exp.getStackTrace()[0].getLineNumber());
					return ("fail");
				} finally {
					try {
						// Cleaning the Unreferenced objects
						screenOption = null;
						hostId = null;
						userId = null;
						// Nullifying the Non-referenced StringBuffer object
						if (sb != null) {
							sb = null;
						}
					} catch (Exception ignore) {
						logger.error("Error in cleaning the objects. Cause : "
								+ ignore.getMessage());
					}
				}
			}


	/**
	 * This method is used to show the details for the users to maintain a
	 * personal entity list.<br>
	 * This also helps the users to define Entities display priority (sorting).<br>
	 * 
	 * @param actionMapping
	 * @param actionForm
	 * @param request
	 * @param response
	 * @return ActionForward
	 * @throws SwtException
	 * <AUTHOR> Joel Sudhan .I<br>
	 */
	public String displayPersonalEntityList() throws SwtException {
		// Variable holds the Role Id
		String roleId = null;
		// Host Id
		String hostId = null;
		// Current user Id
		String userId = null;
		// Screen Id for entity monitor
		String screenId = null;
		// HttpSession
		HttpSession session = null;
		// User object
		User user = null;
		// PersonalEntityList object
		PersonalEntityList entityList = null;
		// Collection of PersonalEntityList
		Collection<PersonalEntityList> personalEntityInfo = null;
		HttpServletRequest request = ServletActionContext.getRequest();
    	HttpServletResponse response = ServletActionContext.getResponse();
		try {
			logger.debug(this.getClass().getName()
					+ " [displayPersonalEntityList()] Begins");
			// getting session from request
			session = request.getSession();
			// Instantiating the ArrayList personalEntityInfo
			personalEntityInfo = new ArrayList<PersonalEntityList>();
			// Type casting ActionForm with DynaValidatorForm
			//form = (DynaValidatorForm) actionForm;
			// Type casting PersonalEntityList with the DynaValidatorForm
			if(this.personalEntityList == null) {
				this.personalEntityList = new PersonalEntityList();
			}
			entityList = this.personalEntityList;
			// Creating EntityMonitorManager instance
			entityMonitorManager = (EntityMonitorManager) SwtUtil
					.getBean("entityMonitorManager");
			// Getting the current user from the session
			user = SwtUtil.getCurrentUser(session);
			// Gets the role Id from session
			roleId = user.getRoleId();
			// Gets the host id from CacheManager
			hostId = CacheManager.getInstance().getHostId();
			// Gets the user from SwtUtil
			userId = SwtUtil.getCurrentUserId(request.getSession());
			// Gets the screen id from SwtConstants
			screenId = SwtConstants.ENTITY_MONITOR_ID;
			// set the entity list values
			entityList.getId().setRoleId(roleId);
			entityList.getId().setHostId(hostId);
			entityList.getId().setUserId(userId);
			entityList.getId().setScreenId(screenId);
			// calling method to get the personal entity list
			personalEntityInfo = entityMonitorManager.getPersonalEntityList(
					userId, screenId, hostId);
			// set the personal entity details
			entityList.setPersonalEntityRecords(personalEntityInfo);
			// set the personalentitylist to request
			request.setAttribute("personalEntityList", personalEntityInfo);
//			this.personalEntityList = entityList;
//			request.setAttribute("personalEntityList", this.personalEntityList);
			request.setAttribute("reply_status_ok", SwtConstants.STR_TRUE);
			request.setAttribute("reply_message", SwtConstants.DATA_FETCH_OK);
			logger.debug(this.getClass().getName()
					+ " [displayPersonalEntityList()] Ends");
		} catch (SwtException e) {
			e.printStackTrace();
			logger.error(this.getClass().getName()
					+ " - [displayPersonalEntityList] - SwtException -"
					+ e.getMessage());
			SwtUtil.logException(e, request, "");
			request.setAttribute("reply_status_ok", SwtConstants.STR_FALSE);
			request.setAttribute("reply_message", e.getMessage());
			request.setAttribute("reply_location", e.getStackTrace()[0]
					.getClassName()
					+ "."
					+ e.getStackTrace()[0].getMethodName()
					+ ":"
					+ e.getStackTrace()[0].getLineNumber());
			return ("error");
		} catch (Exception e) {
			e.printStackTrace();
			logger.error(this.getClass().getName()
					+ " - [displayPersonalEntityList] - GenericException -"
					+ e.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					e, "displayPersonalEntityList", EntityMonitorAction.class),
					request, "");
			request.setAttribute("reply_status_ok", SwtConstants.STR_FALSE);
			request.setAttribute("reply_message", e.getMessage());
			request.setAttribute("reply_location", e.getStackTrace()[0]
					.getClassName()
					+ "."
					+ e.getStackTrace()[0].getMethodName()
					+ ":"
					+ e.getStackTrace()[0].getLineNumber());
			return("error");
		} finally {
			try {
				// Cleaning Unreferenced objects
				hostId = null;
				userId = null;
				roleId = null;
				screenId = null;
				// Closing the session
				if (session != null) {
					session = null;
				}
			} catch (Exception ignore) {
				logger.error("Error in nullifying objects. Cause : "
						+ ignore.getMessage());
			}
		}
		return ("personalentity");
	}
	
	/**
	 * This method is used to show the details for the users to maintain a
	 * personal entity list.<br>
	 * This also helps the users to define Entities display priority (sorting).<br>
	 * 
	 * @param actionMapping
	 * @param actionForm
	 * @param request
	 * @param response
	 * @return ActionForward
	 * @throws SwtException
	 * <AUTHOR> Joel Sudhan .I<br>
	 */
	public String displayAddSumPersonalEntityList() throws SwtException {

		// Variable holds the Role Id
		String roleId = null;
		// Host Id
		String hostId = null;
		// Current user Id
		String userId = null;
		// Screen Id for entity monitor
		String screenId = null;
		// HttpSession
		HttpSession session = null;
		// User object
		User user = null;
		// Collection of PersonalEntityList
		Collection<PersonalEntityList> personalEntityInfo = null;
		String entityId = null;
		HttpServletRequest request = ServletActionContext.getRequest();
    	HttpServletResponse response = ServletActionContext.getResponse();

		try {
			logger.debug(this.getClass().getName() + " [displayAddSumPersonalEntityList()] Begins");
			entityId = request.getParameter("entityId");
			// getting session from request
			session = request.getSession();
			// Instantiating the ArrayList personalEntityInfo
			personalEntityInfo = new ArrayList<PersonalEntityList>();
			// Type casting ActionForm with DynaValidatorForm
			//Nadia
			//form = (DynaValidatorForm) actionForm;
			// Creating EntityMonitorManager instance
			entityMonitorManager = (EntityMonitorManager) SwtUtil
					.getBean("entityMonitorManager");
			// Getting the current user from the session
			user = SwtUtil.getCurrentUser(session);
			// Gets the role Id from session
			roleId = user.getRoleId();
			// Gets the host id from CacheManager
			hostId = CacheManager.getInstance().getHostId();
			// Gets the user from SwtUtil
			userId = SwtUtil.getCurrentUserId(request.getSession());
			// Gets the screen id from SwtConstants
			screenId = SwtConstants.ENTITY_MONITOR_ID;
			// calling method to get the personal entity list
			personalEntityInfo = entityMonitorManager.getPersonalEntityList(
					userId, screenId, hostId, entityId);
			// set the personalentitylist to request
			request.setAttribute("personalEntityList", personalEntityInfo);
			request.setAttribute("reply_status_ok", SwtConstants.STR_TRUE);
			request.setAttribute("reply_message", SwtConstants.DATA_FETCH_OK);
			logger.debug(this.getClass().getName() + " [displayAddSumPersonalEntityList()] Ends");
		} catch (SwtException e) {
			logger.error(this.getClass().getName()
					+ " - [displayAddSumPersonalEntityList] - SwtException -"
					+ e.getMessage());
			SwtUtil.logException(e, request, "");
			request.setAttribute("reply_status_ok", SwtConstants.STR_FALSE);
			request.setAttribute("reply_message", e.getMessage());
			request.setAttribute("reply_location", e.getStackTrace()[0]
					.getClassName()
					+ "."
					+ e.getStackTrace()[0].getMethodName()
					+ ":"
					+ e.getStackTrace()[0].getLineNumber());
			return  ("error");
		} catch (Exception e) {
			logger.error(this.getClass().getName()
					+ " - [displayAddSumPersonalEntityList] - GenericException -"
					+ e.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					e, "displayAddSumPersonalEntityList", EntityMonitorAction.class),
					request, "");
			request.setAttribute("reply_status_ok", SwtConstants.STR_FALSE);
			request.setAttribute("reply_message", e.getMessage());
			request.setAttribute("reply_location", e.getStackTrace()[0]
					.getClassName()
					+ "."
					+ e.getStackTrace()[0].getMethodName()
					+ ":"
					+ e.getStackTrace()[0].getLineNumber());
			return ("error");
		} finally {
			try {
				// Cleaning Unreferenced objects
				hostId = null;
				userId = null;
				roleId = null;
				screenId = null;
				// Closing the session
				if (session != null) {
					session = null;
				}
			} catch (Exception ignore) {
				logger.error("Error in nullifying objects. Cause : "
						+ ignore.getMessage());
			}
		}
		return ("addsumpersonalentity");
	}

	/**
	 * This method is called to save the changes made to personal entity list.
	 * 
	 * @param ActionMapping
	 *            mapping
	 * @param ActionForm
	 *            form
	 * @param HttpServletRequest
	 *            request
	 * @param HttpServletResponse
	 *            response *
	 * @return state change
	 */
	public String savePersonalEntityList() throws SwtException {

		String update = null;
		// Array of Primary Keys
		String[] pksToUpdate = null;
		// Array of primary key values
		String[] pkValues = null;
		// Array of aggregate values for each entity
		String[] aggregateValues = null;
		// PersonalEntityList object
		PersonalEntityList personalEntityList = null;
		// List of good keys
		ArrayList<String[]> goodKeys = new ArrayList<String[]>();
		// List of bad keys
		ArrayList<String[]> badKeys = new ArrayList<String[]>();
		// Variable holds the Role Id
		String roleId = null;
		// Host Id
		String hostId = null;
		// Current user Id
		String userId = null;
		// Screen Id for entity monitor
		String screenId = null;
		// User object
		User user = null;
		// HttpSession
		HttpSession session = null;
		// StringBuffer object
		StringBuffer sb = null;
		String aggregateString = null;
		boolean isSumEntity = false;
		HttpServletRequest request = ServletActionContext.getRequest();
    	HttpServletResponse response = ServletActionContext.getResponse();
		try {
			logger.debug(this.getClass().getName()
					+ " - [savePersonalEntityList] - Entering");
			// create instance for StringBuffers
			sb = new StringBuffer();
			// getting session from request
			session = request.getSession();
			// get the current user from session
			user = SwtUtil.getCurrentUser(session);
			// Gets the role Id from session
			roleId = user.getRoleId();
			// Gets the host id from CacheManager
			hostId = CacheManager.getInstance().getHostId();
			// Gets the user from SwtUtil
			userId = SwtUtil.getCurrentUserId(request.getSession());
			// Gets the screen id from SwtConstants
			screenId = SwtConstants.ENTITY_MONITOR_ID;
			aggregateString = request.getParameter("aggregate");
			// Split the values passed from the flex screen and the set
			// the values to the corresponding bean classes
			if (!SwtUtil.isEmptyOrNull(update = request.getParameter("update"))) {
				pksToUpdate = update.split(",");
				aggregateValues = aggregateString.split(",");
				for (int i = 0; i < pksToUpdate.length; i++) {
					isSumEntity = "Y".equals(aggregateValues[i]);
					pkValues = pksToUpdate[i].split("-");
					// fetch personal entity list record
					personalEntityList = fetchKeyedRecord(roleId, userId,
							screenId, hostId, pkValues, isSumEntity);
					if (personalEntityList == null) { // record does not exist
						// for this particular implementation this is a failure
						// state, but we could add a record here
						badKeys.add(pkValues);
					} else { // record found ok
						try {
							// look for params and set them on dto
							setPersonalEntityListDTOParams(request,
									personalEntityList, pkValues);
							// set the update date
							personalEntityList.setUpdateDate(SwtUtil
									.getSystemDatewithTime());
							// calling the method to save the personal entity
							// list in the database
							entityMonitorManager.saveRecord(personalEntityList);
							// add success keys
							goodKeys.add(pkValues);
						} catch (SwtException e) {
							logger
									.error(this.getClass().getName()
											+ " - Exception Catched in [savePersonalEntityList] method - "
											+ e.getMessage());
							badKeys.add(pkValues);
						}
					}
				}
				// Sets the error message to be displayed else request was good
				if (badKeys.size() > 0) {
					request.setAttribute("reply_status_ok",
							SwtConstants.STR_FALSE);
					request.setAttribute("reply_message", "SUCCESS: "
							+ formatKeyList(goodKeys) + " | FAIL: "
							+ formatKeyList(badKeys));
				} else {
					request.setAttribute("reply_status_ok",
							SwtConstants.STR_TRUE);
					request.setAttribute("reply_message", "SUCCESS: "
							+ formatKeyList(goodKeys));
				}
			} else {
				request.setAttribute("reply_status_ok", SwtConstants.STR_FALSE);
				request.setAttribute("reply_message",
						SwtConstants.UPDATE_PARAM_NOT_SENT);
			}
			// Appending the success message in the StringBuffer
			sb.append("<?xml version=\"1.0\" encoding=\"UTF-8\"?> ");
			sb.append("<PersonalEntity>");
			sb.append("<request_reply><status_ok>true"
					+ "</status_ok><message>true"
					+ "</message></request_reply>");
			sb.append("</PersonalEntity>");
			response.setContentType("text/xml");
			// Setting the success message in the response
			response.getOutputStream().println(sb.toString());
			logger.debug(this.getClass().getName()
					+ " - [savePersonalEntityList] - Exiting");
		} catch (SwtException swtexp) {
			logger.error(this.getClass().getName()
					+ " - [saveEntityMonitorOptions] - SwtException -"
					+ swtexp.getMessage());
			request.setAttribute("reply_status_ok", SwtConstants.STR_FALSE);
			request.setAttribute("reply_message", swtexp.getMessage());
			request.setAttribute("reply_location", swtexp.getStackTrace()[0]
					.getClassName()
					+ "."
					+ swtexp.getStackTrace()[0].getMethodName()
					+ ":"
					+ swtexp.getStackTrace()[0].getLineNumber());
			SwtUtil.logException(swtexp, request, "");
			return ("success");
		} catch (Exception e) {
			logger
					.error(this.getClass().getName()
							+ " - Exception Catched in [savePersonalEntityList] method - "
							+ e.getMessage());
			request.setAttribute("reply_status_ok", SwtConstants.STR_FALSE);
			request.setAttribute("reply_message", e.getMessage());
			request.setAttribute("reply_location", e.getStackTrace()[0]
					.getClassName()
					+ "."
					+ e.getStackTrace()[0].getMethodName()
					+ ":"
					+ e.getStackTrace()[0].getLineNumber());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					e, "savePersonalEntityList", EntityMonitorAction.class),
					request, "");
			return ("dataerror");
		} finally {
			try {
				// Cleaning up unused object goes here
				update = null;
				roleId = null;
				hostId = null;
				userId = null;
				screenId = null;
				if (pksToUpdate != null) {
					pksToUpdate = null;
				}
				if (pkValues != null) {
					pkValues = null;
				}
			} catch (Exception ignore) {
				logger
						.error("Error occured in cleaning unused objects. Cause : "
								+ ignore.getMessage());
			}
		}
		return null;
	}

	/**
	 * This method fetches the personal entity list details based on the key
	 * passed.
	 * 
	 * @param releId
	 * @param userId
	 * @param screenId
	 * @param hostId
	 * @param keys
	 * @param isSumEntity
	 * @return PersonalEntityList throws SwtException
	 */
	private PersonalEntityList fetchKeyedRecord(String roleId, String userId,
			String screenId, String hostId, String[] keys, boolean isSumEntity) throws SwtException {

		// PersonalEntityList object
		PersonalEntityList personalEntityList = null;
		try {
			logger.debug(this.getClass().getName()
					+ " - [fetchKeyedRecord] - Entering");
			// implementation-specific key mapping goes here
			personalEntityList = new PersonalEntityList();
			// setting Entity Id
			personalEntityList.getId().setEntityId(SwtUtil.decode64(keys[0]));
			// setting Host Id
			personalEntityList.getId().setHostId(hostId);
			// setting Role Id
			personalEntityList.getId().setRoleId(roleId);
			// setting Screen Id
			personalEntityList.getId().setScreenId(screenId);
			// setting User Id
			personalEntityList.getId().setUserId(userId);
			logger.debug(this.getClass().getName()
					+ " - [fetchKeyedRecord] - Exiting");
			if (isSumEntity) {
				personalEntityList.setYesAggrgEntity("Y");
				return entityMonitorManager.fetchSumRecord(personalEntityList);
			} else {
				personalEntityList.setYesAggrgEntity("N");
				return entityMonitorManager.fetchRecord(personalEntityList);
			}
		} catch (Exception e) {
			String message = e.getStackTrace()[0].getClassName() + "."
					+ e.getStackTrace()[0].getMethodName() + ":"
					+ e.getStackTrace()[0].getLineNumber() + " "
					+ e.getMessage();
			logger.error(this.getClass().getName()
					+ " - [fetchKeyedRecord] - Exception -" + message);
			// Re-throwing SwtException
			throw SwtErrorHandler.getInstance().handleException(e,
					"fetchKeyedRecord", EntityMonitorAction.class);
		}
	}

	/**
	 * Sets the personal entity list values to the corresponding bean objects
	 * 
	 * @param HttpServletRequest
	 *            request
	 * @param PersonalEntityList
	 *            dto
	 * @param String[]
	 *            keys
	 * @throws SwtException
	 */
	private void setPersonalEntityListDTOParams(HttpServletRequest request,
			PersonalEntityList dto, String[] keys) throws SwtException {
		// variable to hold keys
		String sKeys = "";
		try {
			logger.debug(this.getClass().getName()
					+ " - [setPersonalEntityListDTOParams] - Entering");
			for (int i = 0; i < keys.length; i++) {
				sKeys += "," + SwtUtil.decode64(keys[i]);
			}
			sKeys = sKeys.substring(1);
			// only update the DTO with data that are sent
			// Setting entityId from getField method that fetches the values
			// from the map for the passed key
			String entityId = getField(request, sKeys, SwtConstants.ENTITY_ID,
					true);
			if (entityId != null)
				dto.getId().setEntityId(entityId);

			// Setting entityName from getField method that fetches the values
			// from the map for the passed key
			String entityName = getField(request, sKeys,
					SwtConstants.ENTITY_NAME, true);
			if (entityName != null)
				dto.setEntityName(entityName);

			// Setting displayDays from getField method that fetches the values
			// from the map for the passed key
			String displayDays = getField(request, sKeys,
					SwtConstants.DISPLAY_DAYS, true);
			if (displayDays != null)
				dto.setDisplayDays(Integer.parseInt(displayDays));

			// Setting yesDisplay from getField method that fetches the values
			// from the map for the passed key
			String yesDisplay = getField(request, sKeys, SwtConstants.DISPLAY,
					true);
			if (yesDisplay != null)
				dto
						.setYesDisplay(yesDisplay.equalsIgnoreCase("Y") ? SwtConstants.YES
								: SwtConstants.NO);

			// Setting order of display from getField method that fetches the
			// values from the map for the passed key
			String orderOfDisplay = getField(request, sKeys,
					SwtConstants.PRIORITY, true);
			if (orderOfDisplay != null)
				dto.setPriorityOrder(Integer.parseInt(orderOfDisplay));
			logger.debug(this.getClass().getName()
					+ " - [setPersonalEntityListDTOParams] - Exiting");
		} catch (Exception e) {
			String message = e.getStackTrace()[0].getClassName() + "."
					+ e.getStackTrace()[0].getMethodName() + ":"
					+ e.getStackTrace()[0].getLineNumber() + " "
					+ e.getMessage();
			logger.error(this.getClass().getName()
					+ " - [setPersonalEntityListDTOParams] - Exception -"
					+ message);
			// Re-throwing SwtException
			throw SwtErrorHandler.getInstance()
					.handleException(e, "setPersonalEntityListDTOParams",
							EntityMonitorAction.class);
		}
	}

	/**
	 * This method fetches the values from the map for the passed key
	 * 
	 * @param request
	 * @param sKeys
	 * @param name
	 * @param allowNull
	 * @return data String
	 * @throws SwtException
	 */
	private String getField(HttpServletRequest request, String sKeys,
			String name, boolean allowNull) throws SwtException {
		// variable to hold keys & values
		String data = null;
		try {
			logger
					.debug(this.getClass().getName()
							+ " - [getField] - Entering");
			// Getting keys from request
			data = request.getParameter(sKeys + "_"
					+ flexFieldMapping.getTag(name));
			if (!allowNull && data == null)
				throw new SwtException("data field: " + sKeys + "_"
						+ flexFieldMapping.getTag(name) + " not recevied");
			logger.debug(this.getClass().getName() + " - [getField] - Exiting");
		} catch (Exception e) {
			String message = e.getStackTrace()[0].getClassName() + "."
					+ e.getStackTrace()[0].getMethodName() + ":"
					+ e.getStackTrace()[0].getLineNumber() + " "
					+ e.getMessage();
			logger.error(this.getClass().getName()
					+ " - [getField] - Exception -" + message);
			// Re-throwing SwtException
			throw SwtErrorHandler.getInstance().handleException(e, "getField",
					EntityMonitorAction.class);
		}
		return data;
	}

	/**
	 * This method formats the values and creates a string with all the values.<br>
	 * The single row values are appended with '-' and the row are
	 * differentiated by appending a ','
	 * 
	 * @param keyList
	 * @return rtn String
	 */
	private String formatKeyList(ArrayList<String[]> keyList)
			throws SwtException {

		// variable to hold keys
		String rtn = "";
		// used to iterate key values
		Iterator<String[]> itr = null;
		try {
			logger.debug(this.getClass().getName()
					+ " - [formatKeyList] - Entering");
			itr = keyList.iterator();
			// Iterates the array list to append the values in a string
			while (itr.hasNext()) {
				String[] keys = itr.next();
				String row = "";
				// For each row values
				for (int i = 0; i < keys.length; i++) {
					row += "-" + keys[i];
				}
				// For differentiating the rows
				if (row.length() > 0)
					rtn += ", " + row.substring(1);
			}
			// setting the substring value for rtn
			if (rtn.length() > 0)
				rtn = rtn.substring(2);
			logger.debug(this.getClass().getName()
					+ " - [formatKeyList] - Exiting");
		} catch (Exception e) {
			String message = e.getStackTrace()[0].getClassName() + "."
					+ e.getStackTrace()[0].getMethodName() + ":"
					+ e.getStackTrace()[0].getLineNumber() + " "
					+ e.getMessage();
			logger.error(this.getClass().getName()
					+ " - [formatKeyList] - Exception -" + message);
			// Re-throwing SwtException
			throw SwtErrorHandler.getInstance().handleException(e,
					"formatKeyList", EntityMonitorAction.class);
		}
		return rtn;
	}

	/**
	 * This method is used to show the details for the users to maintain a
	 * personal currency list.<br>
	 * This also helps the users to define currencies display priority
	 * (sorting).<br>
	 * 
	 * @param actionMapping
	 * @param actionForm
	 * @param request
	 * @param response
	 * @return ActionForward
	 * @throws SwtException
	 * 
	 */
	public String displayPersonalCurrencyList()
			throws SwtException {
		// Host Id
		String hostId = null;
		// Current user Id
		String userId = null;
		// Screen Id for entity monitor
		String screenId = null;
		// DynaValidatorForm object
		// PersonalEntityList object
		PersonalCurrency currencyList = null;
		// Collection of PersonalEntityList
		PersonalCurrency personalCurrencyInfo = null;
		HttpSession session = null;
		// Role id
		String roleId = null;
		// user object
		User user = null;
		HttpServletRequest request = ServletActionContext.getRequest();
    	HttpServletResponse response = ServletActionContext.getResponse();
		try {
			logger.debug(this.getClass().getName()
					+ " [displayPersonalCurrencyList()] Begins");
			personalCurrencyInfo = new PersonalCurrency();
			//Nadia
			//form = (DynaValidatorForm) actionForm;
			if(this.personalCurrencyList == null)
				this.personalCurrencyList = new PersonalCurrency();
			currencyList =  this.personalCurrencyList;
			entityMonitorManager = (EntityMonitorManager) SwtUtil
					.getBean("entityMonitorManager");
			// Gets the host id from CacheManager
			hostId = CacheManager.getInstance().getHostId();
			// Gets the user from SwtUtil
			userId = SwtUtil.getCurrentUserId(request.getSession());
			// Gets the screen id from SwtConstants
			screenId = SwtConstants.ENTITY_MONITOR_ID;
			currencyList.getId().setHostId(hostId);
			currencyList.getId().setUserId(userId);
			// get the session
			session = request.getSession();
			// Get the current user from SwtUtil
			user = SwtUtil.getCurrentUser(session);
			// Get the role Id associated to the user
			roleId = user.getRoleId();
			currencyList.getId().setScreenId(screenId);
			// calling method to get the personal entity list
			personalCurrencyInfo = entityMonitorManager.getPersonalCurrency(
					hostId, userId, screenId, roleId);

			// Setting entity records in currencyList
			currencyList.setEntityRecords(personalCurrencyInfo
					.getEntityRecords());

			// Setting personal currency records in currency list
			currencyList.setPersonalCurrencyRecords(personalCurrencyInfo
					.getPersonalCurrencyRecords());
			// setting personal currency list in request
			request.setAttribute("personalCurrencyList", currencyList);
			// setting currency list in request
			request.setAttribute("currencyList", currencyList);
			request.setAttribute("reply_status_ok", SwtConstants.STR_TRUE);
			request.setAttribute("reply_message", SwtConstants.DATA_FETCH_OK);
			logger.debug(this.getClass().getName()
					+ " [displayPersonalCurrencyList()] Ends");
		} catch (SwtException e) {
			e.printStackTrace();
			logger.error(this.getClass().getName()
					+ " - [displayPersonalCurrencyList] - SwtException -"
					+ e.getMessage());
			SwtUtil.logException(e, request, "");
			request.setAttribute("reply_status_ok", SwtConstants.STR_FALSE);
			request.setAttribute("reply_message", e.getMessage());
			request.setAttribute("reply_location", e.getStackTrace()[0]
					.getClassName()
					+ "."
					+ e.getStackTrace()[0].getMethodName()
					+ ":"
					+ e.getStackTrace()[0].getLineNumber());
			return ("error");
		} catch (Exception e) {
			logger.error(this.getClass().getName()
					+ " - [displayPersonalCurrencyList] - GenericException -"
					+ e.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance()
					.handleException(e, "displayPersonalCurrencyList",
							EntityMonitorAction.class), request, "");
			request.setAttribute("reply_status_ok", SwtConstants.STR_FALSE);
			request.setAttribute("reply_message", e.getMessage());
			request.setAttribute("reply_location", e.getStackTrace()[0]
					.getClassName()
					+ "."
					+ e.getStackTrace()[0].getMethodName()
					+ ":"
					+ e.getStackTrace()[0].getLineNumber());
			return ("error");
		} finally {
			try {
				// Cleaning Unreferenced objects
				hostId = null;
				userId = null;
				screenId = null;
				roleId = null;
				if (session != null) {
					session = null;
				}
			} catch (Exception e) {
				logger.error("Error in nullifying objects. Cause : "
						+ e.getMessage());
			}
		}
		return ("personalcurrency");
	}

	/**
	 * This helper method gets the list of currencies from the CacheManager and
	 * puts in the request.<br>
	 * 
	 * @param request
	 * @throws SwtException
	 */
	private void putCurrencyListInReq(HttpServletRequest request)
			throws SwtException {
		try {
			// Getting the instance for CacheManager
			CacheManager cacheManagerInst = CacheManager.getInstance();
			// Getting all the currencies from the Currency Master
			Collection<CurrencyMaster> coll = cacheManagerInst.getCurrencies();
			// Setting all currencies in request
			request.setAttribute("currencyList", coll);
		} catch (Exception e) {
			logger.error(this.getClass().getName()
					+ " - putCurrencyListInReq(). Exception caught : "
					+ e.getMessage());
			// Re-throwing SwtException
			throw SwtErrorHandler.getInstance().handleException(e,
					"putCurrencyListInReq", EntityMonitorAction.class);
		}
	}

	/**
	 * This method loads the flexOption object into the client
	 * 
	 * @param ActionMapping
	 *            mapping
	 * @param ActionForm
	 *            form
	 * @param HttpServletRequest
	 *            request
	 * @param HttpServletResponse
	 *            response
	 * @throws SwtException
	 */
	public String flexOption()
			throws SwtException {
		// variable to hold hostId
		String hostId = null;
		// variable to hold userId
		String userId = null;
		// variable to hold itemId
		String itemId = null;
		HttpServletRequest request = ServletActionContext.getRequest();
    	HttpServletResponse response = ServletActionContext.getResponse();
		try {
			logger.debug(this.getClass().getName()
					+ " - [flexOption] - Begins ");
			// Get the host id from CacheManager
			hostId = CacheManager.getInstance().getHostId();
			// Get the user id from SwtUtil
			userId = SwtUtil.getCurrentUserId(request.getSession());
			// Get the screen id from SwtConstants
			itemId = SwtConstants.ENTITY_MONITOR_ID;
			// set the hostId
			request.setAttribute("hostId", hostId);
			// set the userId
			request.setAttribute("userId", userId);
			// set the itemId
			request.setAttribute("itemId", itemId);
			// Calling the helper method for getting the font size from the
			// request.
			setFontSize(request);
			logger.debug(this.getClass().getName() + " - [flexOption] - Ends ");
		} catch (Exception exception) {
			String errorMessage = exception.getStackTrace()[0].getClassName()
					+ "." + exception.getStackTrace()[0].getMethodName() + ":"
					+ exception.getStackTrace()[0].getLineNumber() + " "
					+ exception.getMessage();
			logger.error(this.getClass().getName()
					+ " - flexOption(). Exception : " + errorMessage);

		}
		return ("flexobjectOption");
	}

	/**
	 * This method is called to save the changes made to the interfaces.
	 * 
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return
	 */
	public String savePersonalCurrencyList() throws SwtException {

		// update
		String update = null;
		// editupdate
		String editupdate = null;
		// Update list
		String[] pksToUpdate;
		// Update list
		String[] editpksToUpdate;
		// saveorupdate values
		String[] pkValues;
		// saveorupdate values
		String[] editpkValues;
		// PersonalCcyEntityMap object
		PersonalCcyEntityMap personalMap = new PersonalCcyEntityMap();
		// PersonalCcy object
		PersonalCurrency personalCurrency = new PersonalCurrency();
		// good keys
		ArrayList<String[]> goodKeys = new ArrayList<String[]>();
		// bad keys
		ArrayList<String[]> badKeys = new ArrayList<String[]>();
		// Host Id
		String hostId = null;
		// Current user Id
		String userId = null;
		// StringBuffer object
		StringBuffer sb = null;
		HttpServletRequest request = ServletActionContext.getRequest();
    	HttpServletResponse response = ServletActionContext.getResponse();
		try {
			logger.debug(this.getClass().getName()
					+ " - [savePersonalCurrencyList] - Entering");
			// create instance for StringBuffer
			sb = new StringBuffer();
			// Gets the host id from CacheManager
			hostId = CacheManager.getInstance().getHostId();
			// Gets the user from SwtUtil
			userId = SwtUtil.getCurrentUserId(request.getSession());
			// Split the values passed from the flex screen and the set
			// the values to the corresponding bean classes

			if (!SwtUtil.isEmptyOrNull(update = request.getParameter("update"))) {
				pksToUpdate = update.split(",");
				for (int i = 0; i < pksToUpdate.length; i++) {
					pkValues = pksToUpdate[i].split("_#");
					try {
						if (request.getParameter(pksToUpdate[i]) != null
								&& request.getParameter(pksToUpdate[i]).equals(
										"true")) {
							personalMap.setYesDisplayCcy(SwtConstants.YES);
						} else {
							personalMap.setYesDisplayCcy(SwtConstants.NO);
						}
						personalMap.getId().setCurrencyCode(pkValues[0]);
						personalMap.getId().setEntityId(pkValues[1]);
						personalMap.getId().setHostId(hostId);
						personalMap.getId().setUserId(userId);
						personalMap.setUpdateDate(SwtUtil
								.getSystemDatewithTime());
						// calling the method to save personal currency list
						entityMonitorManager
								.savePersonalCurrencyEntityMap(personalMap);
						goodKeys.add(pkValues);
					} catch (Exception e) {
						logger
								.error(this.getClass().getName()
										+ " - Exception Catched in [savePersonalCurrencyList] method - "
										+ e.getMessage());
						badKeys.add(pkValues);
					}
				}
				// Sets the error message to be displayed else request was good
				if (badKeys.size() > 0) {
					request.setAttribute("reply_status_ok",
							SwtConstants.STR_FALSE);
					request.setAttribute("reply_message", "SUCCESS: "
							+ formatKeyList(goodKeys) + " | FAIL: "
							+ formatKeyList(badKeys));
				} else {
					request.setAttribute("reply_status_ok",
							SwtConstants.STR_TRUE);
					request.setAttribute("reply_message", "SUCCESS: "
							+ formatKeyList(goodKeys));
				}
			} else {
				request.setAttribute("reply_status_ok", SwtConstants.STR_FALSE);
				request.setAttribute("reply_message",
						SwtConstants.UPDATE_PARAM_NOT_SENT);
			}
			// checks whether editupdate is null or not
			if (!SwtUtil.isEmptyOrNull(editupdate = request
					.getParameter("editupdate"))) {
				editpksToUpdate = editupdate.split(",");
				for (int i = 0; i < editpksToUpdate.length; i++) {
					editpkValues = editpksToUpdate[i].split("_");
					try {
						personalCurrency
								.setPriorityOrder(Integer.parseInt(request
										.getParameter(editpkValues[0])));
						personalCurrency.getId().setCurrencyCode(
								editpksToUpdate[i]);
						personalCurrency.getId().setHostId(hostId);
						personalCurrency.getId().setUserId(userId);
						personalCurrency.getId().setScreenId(
								SwtConstants.ENTITY_MONITOR_ID);
						personalCurrency.setUpdateDate(SwtUtil
								.getSystemDatewithTime());
						// calling the method to save personal currency list
						entityMonitorManager.savePersonalCcy(personalCurrency);
					} catch (Exception e) {
						logger
								.error(this.getClass().getName()
										+ " - Exception Catched in [savePersonalCurrencyList] method - "
										+ e.getMessage());
						request.setAttribute("reply_status_ok",
								SwtConstants.STR_FALSE);
						request.setAttribute("reply_message", e.getMessage());
						request.setAttribute("reply_location", e
								.getStackTrace()[0].getClassName()
								+ "."
								+ e.getStackTrace()[0].getMethodName()
								+ ":" + e.getStackTrace()[0].getLineNumber());
					}
				}
				// Sets the error message to be displayed else request was good
				if (badKeys.size() > 0) {
					request.setAttribute("reply_status_ok",
							SwtConstants.STR_FALSE);
					request.setAttribute("reply_message", "SUCCESS: "
							+ formatKeyList(goodKeys) + " | FAIL: "
							+ formatKeyList(badKeys));
				} else {
					request.setAttribute("reply_status_ok",
							SwtConstants.STR_TRUE);
					request.setAttribute("reply_message", "SUCCESS: "
							+ formatKeyList(goodKeys));
				}
			} else {
				request.setAttribute("reply_status_ok", SwtConstants.STR_FALSE);
				request.setAttribute("reply_message",
						SwtConstants.UPDATE_PARAM_NOT_SENT);
			}
			// Appending the success message in the StringBuffer
			sb.append("<?xml version=\"1.0\" encoding=\"UTF-8\"?> ");
			sb.append("<PersonalCurrency>");
			sb.append("<request_reply><status_ok>true"
					+ "</status_ok><message>true"
					+ "</message></request_reply>");
			sb.append("</PersonalCurrency>");
			// Setting the success message in the response
			response.setContentType("text/xml");
			response.getOutputStream().println(sb.toString());
			logger.debug(this.getClass().getName()
					+ " - [savePersonalCurrencyList] - Exiting");
		} catch (SwtException swtexp) {
			logger.error(this.getClass().getName()
					+ " - [saveEntityMonitorOptions] - SwtException -"
					+ swtexp.getMessage());
			request.setAttribute("reply_status_ok", SwtConstants.STR_FALSE);
			request.setAttribute("reply_message", swtexp.getMessage());
			request.setAttribute("reply_location", swtexp.getStackTrace()[0]
					.getClassName()
					+ "."
					+ swtexp.getStackTrace()[0].getMethodName()
					+ ":"
					+ swtexp.getStackTrace()[0].getLineNumber());
			SwtUtil.logException(swtexp, request, "");
			return ("success");
		} catch (Exception e) {
			logger
					.error(this.getClass().getName()
							+ " - Exception Catched in [savePersonalCurrencyList] method - "
							+ e.getMessage());
			request.setAttribute("reply_status_ok", SwtConstants.STR_FALSE);
			request.setAttribute("reply_message", e.getMessage());
			request.setAttribute("reply_location", e.getStackTrace()[0]
					.getClassName()
					+ "."
					+ e.getStackTrace()[0].getMethodName()
					+ ":"
					+ e.getStackTrace()[0].getLineNumber());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					e, "savePersonalCurrencyList", EntityMonitorAction.class),
					request, "");
			return ("dataerror");
		} finally {
			try {
				// Cleaning up unused objects
				update = null;
				editupdate = null;
				hostId = null;
				userId = null;
				if (sb != null) {
					sb = null;
				}
			} catch (Exception ignore) {
				logger
						.error("Error occured in cleaning up unused objects. Cause : "
								+ ignore.getMessage());
			}
		}
		return null;
	}

	/**
	 * Method to save the column width as per the user preferences
	 * 
	 * @param ActionMapping
	 *            mapping
	 * @param ActionForm
	 *            form
	 * @param HttpServletRequest
	 *            request
	 * @param HttpServletResponse
	 *            response
	 * @return ActionForward
	 */
	public String saveColumnWidth() {
		logger.debug(this.getClass().getName()
				+ " - [saveColumnWidth] - Entering ");
		// String variable to hold the column width
		String width = null;
		HttpServletRequest request = ServletActionContext.getRequest();
    	HttpServletResponse response = ServletActionContext.getResponse();
		try {
			if (!SwtUtil.isEmptyOrNull(width = request.getParameter("width"))) {
				SwtUtil.setPropertyValue(request, SwtUtil
						.getUserCurrentEntity(request.getSession()),
						SwtConstants.ENTITY_MONITOR_ID, "display",
						"column_width", width);
			} else {
				throw new Exception(
						"You must send 'width' and 'entityid' parameters");
			}
			request.setAttribute("reply_status_ok", "true");
			request.setAttribute("reply_message", "Column width saved ok");
			logger.debug(this.getClass().getName()
					+ " - [saveColumnWidth] - Exit ");
		} catch (Exception e) {
			logger.error(this.getClass().getName()
					+ " - [saveColumnWidth] - Error - " + e.getMessage());
			request.setAttribute("reply_status_ok", "false");
			request.setAttribute("reply_message", e.getMessage());
			request.setAttribute("reply_location", e.getStackTrace()[0]
					.getClassName()
					+ "."
					+ e.getStackTrace()[0].getMethodName()
					+ ":"
					+ e.getStackTrace()[0].getLineNumber());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					e, "saveColumnWidth", EntityMonitorAction.class), request,
					"");
		}
		return ("statechange");
	}

	/**
	 * Used for binding the column width into request.
	 * 
	 * @param HttpServletRequest
	 *            request
	 * @throws SwtException
	 */
	private void bindColumnWidthInRequest(HttpServletRequest request)
			throws SwtException {
		logger.debug(this.getClass().getName()
				+ " - [bindColumnWidthInRequest] - Entering ");
		try {
			String width = SwtUtil.getPropertyValue(request, SwtUtil
					.getUserCurrentEntity(request.getSession()),
					SwtConstants.ENTITY_MONITOR_ID, "display", "column_width");
			if (width.equalsIgnoreCase("")) { // default condition
				width = "125";
			}
			request.setAttribute("column_width", width);
			logger.debug(this.getClass().getName()
					+ " - [bindColumnWidthInRequest] - Exit ");
		} catch (Exception e) {
			logger.error(this.getClass().getName()
					+ " - bindColumnWidthInRequest(). Exception caught : "
					+ e.getMessage());
			// Re-throwing SwtException
			throw SwtErrorHandler.getInstance().handleException(e,
					"bindColumnWidthInRequest", EntityMonitorAction.class);
		}
	}

	/**
	 * This function sets the font size to a user defined value.<br>
	 * 
	 * @param request
	 * @throws SwtException
	 * <AUTHOR>
	 */
	private void setFontSize(HttpServletRequest request) throws SwtException {
		// Declares erroeMessage
		String errorMessage = null;
		// Declares screenOption
		ScreenOption screenOption = null;
		// Declares optionManager
		ScreenOptionManager optionManager = null;
		try {
			logger.debug(this.getClass().getName() + " - [setFontSize] - Begins");
			// Creates new instance of ScreenOption
			screenOption = new ScreenOption();
			// Casts with the ScreenOptionManager
			optionManager = (ScreenOptionManager) SwtUtil
					.getBean("screenOptionManager");
			// Sets the host Id from SwtUtil
			screenOption.getId().setHostId(SwtUtil.getCurrentHostId());
			// Sets the user Id from SwtUtil
			screenOption.getId().setUserId(
					SwtUtil.getCurrentUserId(request.getSession()));
			// Sets the screen Id
			screenOption.getId().setScreenId(SwtConstants.ENTITY_MONITOR_ID);
			// Gets the font size from screenOption
			screenOption = optionManager.getFontSize(screenOption);
			// Sets the font size in the request
			request.setAttribute("fontSize", screenOption.getPropertyValue());
			logger.debug(this.getClass().getName() + " - [setFontSize] - Ends");
		} catch (Exception exception) {
			errorMessage = exception.getStackTrace()[0].getClassName() + "."
					+ exception.getStackTrace()[0].getMethodName() + ":"
					+ exception.getStackTrace()[0].getLineNumber() + " "
					+ exception.getMessage();
			logger.error(this.getClass().getName()
					+ " - setFontSize(). Exception : " + errorMessage);
			// Re-throws tException
			throw SwtErrorHandler.getInstance().handleException(exception,
					"setFontSize()", EntityMonitorAction.class);
		}
	}
	
	/**
	 * This method is called to save the changes made to personal entity list.
	 * 
	 * @param ActionMapping
	 *            mapping
	 * @param ActionForm
	 *            form
	 * @param HttpServletRequest
	 *            request
	 * @param HttpServletResponse
	 *            response *
	 * @return state change
	 */
	public String savePersonalSumEntityList() throws SwtException {


		// PersonalEntityList object
		PersonalEntityList personalEntityList = null;
		// List of good keys
		ArrayList<String[]> goodKeys = new ArrayList<String[]>();
		// List of bad keys
		ArrayList<String[]> badKeys = new ArrayList<String[]>();
		// Variable holds the Role Id
		String roleId = null;
		// Host Id
		String hostId = null;
		// Current user Id
		String userId = null;
		// Screen Id for entity monitor
		String screenId = null;
		// User object
		User user = null;
		// HttpSession
		HttpSession session = null;
		// StringBuffer object
		StringBuffer sb = null;
		String action = null;
		String entityName = null;
		String entityId = null;
		String sumEntitiesList = null;
		HttpServletRequest request = ServletActionContext.getRequest();
    	HttpServletResponse response = ServletActionContext.getResponse();
		try {
			logger.debug(this.getClass().getName()
					+ " - [deletePersonalSumEntityList] - Entering");
			// create instance for StringBuffers
			sb = new StringBuffer();
			// getting session from request
			session = request.getSession();
			// get the current user from session
			user = SwtUtil.getCurrentUser(session);
			// Gets the role Id from session
			roleId = user.getRoleId();
			// Gets the host id from CacheManager
			hostId = CacheManager.getInstance().getHostId();
			// Gets the user from SwtUtil
			userId = SwtUtil.getCurrentUserId(request.getSession());
			// Gets the screen id from SwtConstants
			screenId = SwtConstants.ENTITY_MONITOR_ID;
			// Split the values passed from the flex screen and the set
			// the values to the corresponding bean classes
			action = request.getParameter("action");
			entityName = request.getParameter("entityName");
			entityId = request.getParameter("entityId");
			sumEntitiesList = request.getParameter("sumEntitiesList");
			if (!SwtUtil.isEmptyOrNull(action)) {
				// fetch personal entity list record
				personalEntityList = fetchKeyedRecord(roleId, userId,
						screenId, hostId, new String[]{entityId}, true);
				if (personalEntityList == null) { // record does not exist
					// for this particular implementation this is a failure
					// state, but we could add a record here
					badKeys.add(new String[]{entityId});
				} else {
					try {
						personalEntityList.setEntityName(entityName);
						// set the update date
						personalEntityList.setUpdateDate(SwtUtil
								.getSystemDatewithTime());
						// calling the method to save the personal entity
						// list in the database
						entityMonitorManager.saveSumRecord(personalEntityList, sumEntitiesList);
						// add success keys
						goodKeys.add(new String[]{entityId});
					} catch (SwtException e) {
						logger
						.error(this.getClass().getName()
								+ " - Exception Catched in [savePersonalSumEntityList] method - "
								+ e.getMessage());
						badKeys.add(new String[]{entityId});
					}
				}
			} else {
				request.setAttribute("reply_status_ok", SwtConstants.STR_FALSE);
				request.setAttribute("reply_message",
						SwtConstants.UPDATE_PARAM_NOT_SENT);
			}
			
			// Sets the error message to be displayed else request was good
			if (badKeys.size() > 0) {
				request.setAttribute("reply_status_ok",
						SwtConstants.STR_FALSE);
				request.setAttribute("reply_message", "SUCCESS: "
						+ formatKeyList(goodKeys) + " | FAIL: "
						+ formatKeyList(badKeys));
			} else {
				request.setAttribute("reply_status_ok",
						SwtConstants.STR_TRUE);
				request.setAttribute("reply_message", "SUCCESS: "
						+ formatKeyList(goodKeys));
			}
			// Appending the success message in the StringBuffer
			sb.append("<?xml version=\"1.0\" encoding=\"UTF-8\"?> ");
			sb.append("<PersonalEntity>");
			sb.append("<request_reply><status_ok>true"
					+ "</status_ok><message>true"
					+ "</message></request_reply>");
			sb.append("</PersonalEntity>");
			response.setContentType("text/xml");
			// Setting the success message in the response
			response.getOutputStream().println(sb.toString());
			logger.debug(this.getClass().getName()
					+ " - [deletePersonalSumEntityList] - Exiting");
		} catch (SwtException swtexp) {
			logger.error(this.getClass().getName()
					+ " - [deletePersonalSumEntityList] - SwtException -"
					+ swtexp.getMessage());
			request.setAttribute("reply_status_ok", SwtConstants.STR_FALSE);
			request.setAttribute("reply_message", swtexp.getMessage());
			request.setAttribute("reply_location", swtexp.getStackTrace()[0]
					.getClassName()
					+ "."
					+ swtexp.getStackTrace()[0].getMethodName()
					+ ":"
					+ swtexp.getStackTrace()[0].getLineNumber());
			SwtUtil.logException(swtexp, request, "");
			return ("success");
		} catch (Exception e) {
			logger
			.error(this.getClass().getName()
					+ " - Exception Catched in [deletePersonalSumEntityList] method - "
					+ e.getMessage());
			request.setAttribute("reply_status_ok", SwtConstants.STR_FALSE);
			request.setAttribute("reply_message", e.getMessage());
			request.setAttribute("reply_location", e.getStackTrace()[0]
					.getClassName()
					+ "."
					+ e.getStackTrace()[0].getMethodName()
					+ ":"
					+ e.getStackTrace()[0].getLineNumber());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					e, "deletePersonalSumEntityList", EntityMonitorAction.class),
					request, "");
			return ("dataerror");
		} finally {
			try {
				// Cleaning up unused object goes here
				roleId = null;
				hostId = null;
				userId = null;
				screenId = null;
			} catch (Exception ignore) {
				logger
				.error("Error occured in cleaning unused objects. Cause : "
						+ ignore.getMessage());
			}
		}
		return null;
	}
	
	/**
	 * This method is called to delete the personal sum entity list.
	 * 
	 * @param ActionMapping
	 *            mapping
	 * @param ActionForm
	 *            form
	 * @param HttpServletRequest
	 *            request
	 * @param HttpServletResponse
	 *            response *
	 * @return state change
	 */
	public String deletePersonalSumEntityList() throws SwtException {
		
		// Host Id
		String hostId = null;
		// Current user Id
		String userId = null;
		// Screen Id for entity monitor
		String screenId = null;
		// User object
		User user = null;
		// HttpSession
		HttpSession session = null;
		// StringBuffer object
		StringBuffer sb = null;
		boolean deleteSuccessful = false;
		String entityId = null;
		HttpServletRequest request = ServletActionContext.getRequest();
    	HttpServletResponse response = ServletActionContext.getResponse();
		try {
			logger.debug(this.getClass().getName()
					+ " - [deletePersonalSumEntityList] - Entering");
			// create instance for StringBuffers
			sb = new StringBuffer();
			// getting session from request
			session = request.getSession();
			// get the current user from session
			user = SwtUtil.getCurrentUser(session);
			// Gets the host id from CacheManager
			hostId = CacheManager.getInstance().getHostId();
			// Gets the user from SwtUtil
			userId = SwtUtil.getCurrentUserId(request.getSession());
			// Gets the screen id from SwtConstants
			screenId = SwtConstants.ENTITY_MONITOR_ID;
			// Split the values passed from the flex screen and the set
			// the values to the corresponding bean classes
			entityId = request.getParameter("entityId");
			
			deleteSuccessful = entityMonitorManager.deleteSumRecord(hostId, userId, screenId, entityId);
										
			// Sets the error message to be displayed else request was good
			if (!deleteSuccessful) {
				request.setAttribute("reply_status_ok",
						SwtConstants.STR_FALSE);
			} else {
				request.setAttribute("reply_status_ok",
						SwtConstants.STR_TRUE);
			}
			// Appending the success message in the StringBuffer
			sb.append("<?xml version=\"1.0\" encoding=\"UTF-8\"?> ");
			sb.append("<PersonalEntity>");
			sb.append("<request_reply><status_ok>true"
					+ "</status_ok><message>true"
					+ "</message></request_reply>");
			sb.append("</PersonalEntity>");
			response.setContentType("text/xml");
			// Setting the success message in the response
			response.getOutputStream().println(sb.toString());
			logger.debug(this.getClass().getName()
					+ " - [savePersonalSumEntityList] - Exiting");
		} catch (SwtException swtexp) {
			logger.error(this.getClass().getName()
					+ " - [savePersonalSumEntityList] - SwtException -"
					+ swtexp.getMessage());
			request.setAttribute("reply_status_ok", SwtConstants.STR_FALSE);
			request.setAttribute("reply_message", swtexp.getMessage());
			request.setAttribute("reply_location", swtexp.getStackTrace()[0]
					.getClassName()
					+ "."
					+ swtexp.getStackTrace()[0].getMethodName()
					+ ":"
					+ swtexp.getStackTrace()[0].getLineNumber());
			SwtUtil.logException(swtexp, request, "");
			return ("success");
		} catch (Exception e) {
			logger
					.error(this.getClass().getName()
							+ " - Exception Catched in [savePersonalSumEntityList] method - "
							+ e.getMessage());
			request.setAttribute("reply_status_ok", SwtConstants.STR_FALSE);
			request.setAttribute("reply_message", e.getMessage());
			request.setAttribute("reply_location", e.getStackTrace()[0]
					.getClassName()
					+ "."
					+ e.getStackTrace()[0].getMethodName()
					+ ":"
					+ e.getStackTrace()[0].getLineNumber());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					e, "savePersonalSumEntityList", EntityMonitorAction.class),
					request, "");
			return ("dataerror");
		} finally {
			try {
				// Cleaning up unused object goes here
				hostId = null;
				userId = null;
				screenId = null;
			} catch (Exception ignore) {
				logger
						.error("Error occured in cleaning unused objects. Cause : "
								+ ignore.getMessage());
			}
		}
		return null;
	}
}