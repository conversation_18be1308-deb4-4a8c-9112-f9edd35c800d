/*
 * Created on Sep 19, 2006
 *
 * TODO To change the template for this generated file go to
 * Window - Preferences - Java - Code Style - Code Templates
 */
package org.swallow.work.model;

import java.io.Serializable;

import org.swallow.model.BaseObject;

/**
 * <AUTHOR>
 *
 * TODO To change the template for this generated type comment go to
 * Window - Preferences - Java - Code Style - Code Templates
 */
public class Sequence implements Serializable {
    /**
     * Id 
     */
    private Id id = new Id();
    
    private Long sequenceValue;
    
    

   
	/**
	 * @return Returns the sequenceValue.
	 */
	public Long getSequenceValue() {
		return sequenceValue;
	}
	/**
	 * @param sequenceValue The sequenceValue to set.
	 */
	public void setSequenceValue(Long sequenceValue) {
		this.sequenceValue = sequenceValue;
	}
    /**
    *	Constructer
    */
    public Sequence() {
        
    }

    /**
     * @return Returns the id.
     */
    public Id getId() {
        return id;
    }

    /**
     * @param id The id to set.
     */
    public void setId(Id id) {
        this.id = id;
    }

    /**
     * DOCUMENT ME!
     *
     * <AUTHOR>
     * @version $Revision$
     */
    public static class Id extends BaseObject {
        /**
         * Host Id
         */
        private String hostId;

        /**
         * User Id
         */
        private String sequenceName;

       /**
         * Creates a new Id object.
         */
        public Id() {
        }

        /**
         * Creates a new Id object.
         *
         * @param hostId DOCUMENT ME!
         * @param userId DOCUMENT ME!
         * @param itemId DOCUMENT ME!
         */
        public Id(String hostId, String sequenceName) {
            this.hostId = hostId;
            this.sequenceName = sequenceName;
        }
        
        

		/**
		 * @return Returns the hostId.
		 */
		public String getHostId() {
			return hostId;
		}
		/**
		 * @param hostId The hostId to set.
		 */
		public void setHostId(String hostId) {
			this.hostId = hostId;
		}
		/**
		 * @return Returns the sequenceName.
		 */
		public String getSequenceName() {
			return sequenceName;
		}
		/**
		 * @param sequenceName The sequenceName to set.
		 */
		public void setSequenceName(String sequenceName) {
			this.sequenceName = sequenceName;
		}
     }
}
