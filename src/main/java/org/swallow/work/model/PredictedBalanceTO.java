/*
 * @(#)PredictedBalanceTO.java  07/07/06
 * 
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 * 
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */

package org.swallow.work.model;
import java.io.Serializable;
import java.util.Date;
public class PredictedBalanceTO implements Serializable{
    /**
     * the predictedBalance
     */
    private Double predictedBalance;

    /**
     * the timeStamp
     */
    private Date timeStamp;

    /**
     * The constructor for the class
     * @param predictedBalance the predicted balance value
     * @param timeStamp the time at which this value is being stored
     */
    public PredictedBalanceTO(Double predictedBalance, Date timeStamp) {
        this.predictedBalance = predictedBalance;
        this.timeStamp = timeStamp;
    }

    /**
     * @return Returns the predictedBalance.
     */
    public Double getPredictedBalance() {
        return predictedBalance;
    }

    /**
     * @return Returns the timeStamp.
     */
    public Date getTimeStamp() {
        return timeStamp;
    }
}
