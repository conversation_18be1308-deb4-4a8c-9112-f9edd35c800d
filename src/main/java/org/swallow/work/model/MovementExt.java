/*
 * Created on Mars 14, 2018
 *
 */
package org.swallow.work.model;

import java.lang.reflect.Field;
import java.util.Date;

import org.swallow.model.BaseObject;
import org.swallow.util.SwtUtil;

/**
 * <AUTHOR>
 * 
 * TODO To change the template for this generated type comment go to Window -
 * Preferences - Java - Code Style - Code Templates
 */
public class MovementExt extends BaseObject {

	private static final long serialVersionUID = 1L;
	
	
	private Id id;
	/**
	 * Ordering Customer ID  1 as String
	 */
	private String orderingCustomer1;
	/**
	 * Ordering Customer ID  2 as String
	 */
	private String orderingCustomer2;
	/**
	 * Ordering Customer ID  3 as String
	 */
	private String orderingCustomer3;
	/**
	 * Ordering Customer ID  4 as String
	 */
	private String orderingCustomer4;
	/**
	 * Ordering Customer ID  5 as String
	 */
	private String orderingCustomer5;
	

	/**
	 * Ordering institution ID 1 as String
	 */
	private String orderingInstitution1;
	/**
	 * Ordering institution ID 2 as String
	 */
	private String orderingInstitution2;
	/**
	 * Ordering institution ID 3 as String
	 */
	private String orderingInstitution3;
	/**
	 * Ordering institution ID 4 as String
	 */
	private String orderingInstitution4;
	/**
	 * Ordering institution ID 5 as String
	 */
	private String orderingInstitution5;
	
	

	/**
	 * Sender's Correspondent ID 1 as String
	 */
	private String senderCorrespondent1;
	/**
	 * Sender's Correspondent ID 2 as String
	 */
	private String senderCorrespondent2;
	/**
	 * Sender's Correspondent ID 3 as String
	 */
	private String senderCorrespondent3;
	/**
	 * Sender's Correspondent ID 4 as String
	 */
	private String senderCorrespondent4;
	/**
	 * Sender's Correspondent ID 5 as String
	 */
	private String senderCorrespondent5;

	

	/**
	 * Receiver's Correspondent ID 1 as String
	 */
	private String receiverCorrespondent1;
	/**
	 * Receiver's Correspondent ID  2 as String
	 */
	private String receiverCorrespondent2;
	/**
	 * Receiver's Correspondent ID 3 as String
	 */
	private String receiverCorrespondent3;
	/**
	 * Receiver's Correspondent ID 4 as String
	 */
	private String receiverCorrespondent4;
	/**
	 * Receiver's Correspondent ID 5 as String
	 */
	private String receiverCorrespondent5;

	/**
	 * Intermediary Institution ID  1 as String
	 */
	private String intermediaryInstitution1;
	/**
	 * Intermediary Institution ID 2 as String
	 */
	private String intermediaryInstitution2;
	/**
	 * Intermediary Institution ID 3 as String
	 */
	private String intermediaryInstitution3;
	/**
	 * Intermediary Institution ID 4 as String
	 */
	private String intermediaryInstitution4;
	/**
	 * Intermediary Institution ID 5 as String
	 */
	private String intermediaryInstitution5;

	/**
	 * Account with Institution ID 1 as String
	 */
	private String accountWithInstitution1;
	/**
	 * Account with Institution ID 2 as String
	 */
	private String accountWithInstitution2;
	/**
	 * Account with Institution ID 3 as String
	 */
	private String accountWithInstitution3;
	/**
	 * Account with Institution ID 4 as String
	 */
	private String accountWithInstitution4;
	/**
	 * Account with Institution ID 5 as String
	 */
	private String accountWithInstitution5;

	/**
	 * Beneficiary Customer 1 as String
	 */
	private String beneficiaryCustomer1;
	/**
	 * Beneficiary Customer 2 as String
	 */
	private String beneficiaryCustomer2;
	/**
	 * Beneficiary Customer 3 as String
	 */
	private String beneficiaryCustomer3;
	/**
	 * Beneficiary Customer 4 as String
	 */
	private String beneficiaryCustomer4;
	/**
	 * Beneficiary Customer 5 as String
	 */
	private String beneficiaryCustomer5;
	/**
	 * sender To Receiver Info 1 as String
	 */
	private String senderToReceiverInfo1;
	/**
	 * sender To Receiver Info 2 as String
	 */
	private String senderToReceiverInfo2;
	/**
	 * sender To Receiver Info 3 as String
	 */
	private String senderToReceiverInfo3;
	/**
	 * sender To Receiver Info 4 as String
	 */
	private String senderToReceiverInfo4;
	/**
	 * sender To Receiver Info 5 as String
	 */
	private String senderToReceiverInfo5;
	/**
	 * sender To Receiver Info 6 as String
	 */
	private String senderToReceiverInfo6;
	
	/**
	 * updateDate as Date
	 */
	private Date updateDate;

	
	/**
	 * updateUser as String
	 */
	private String updateUser;

	/**
	 * extrText1 as String
	 */
	private String extraText1;
	
	/**
	 * extrText2 as String
	 */
	private String extraText2;
	/**
	 * extrText3 as String
	 */
	private String extraText3;
	/**
	 * extrText4 as String
	 */
	private String extraText4;
	/**
	 * extrText5 as String
	 */
	private String extraText5;
	/**
	 * extrText6 as String
	 */
	private String extraText6;
	/**
	 * extrText7 as String
	 */
	private String extraText7;
	/**
	 * extrText8 as String
	 */
	private String extraText8;
	/**
	 * extrText9 as String
	 */
	private String extraText9;
	/**
	 * extrText10 as String
	 */
	private String extraText10;
	
	private String lastMmaUpdateNote;
	private Integer lastMmaUpdateEventId;
	
	public MovementExt() {
		super();
		id = new Id();
	}
	
	
	





	public static class Id extends BaseObject {
		private String hostId;
		private String entityId;
		private Long movementId;
		private String movementIdAsString;

		/**
		 * @param hostId
		 * @param entityId
		 * @param movementId
		 * @param movementIdAsString
		 */
		public Id(String hostId, String entityId, Long movementId,
				String movementIdAsString) {
			super();
			this.hostId = hostId;
			this.entityId = entityId;
			this.movementId = movementId;
			this.movementIdAsString = movementIdAsString;
		}

		/**
		 * 
		 */
		public Id() {
			super();

			// TODO Auto-generated constructor stub
		}

		/**
		 * @return Returns the movementIdAsString.
		 */
		public String getMovementIdAsString() {
			return movementIdAsString;
		}

		/**
		 * @param movementIdAsString
		 *            The movementIdAsString to set.
		 */
		public void setMovementIdAsString(String movementIdAsString) {
			this.movementIdAsString = movementIdAsString;
		}

		public String getEntityId() {
			return entityId;
		}

		/**
		 * @param entityId
		 *            The entityId to set.
		 */
		public void setEntityId(String entityId) {
			this.entityId = entityId;
		}

		/**
		 * @return Returns the hostId.
		 */
		public String getHostId() {
			return hostId;
		}

		/**
		 * @param hostId
		 *            The hostId to set.
		 */
		public void setHostId(String hostId) {
			this.hostId = hostId;
		}

		/**
		 * @return Returns the movementId.
		 */
		public Long getMovementId() {
			return movementId;
		}

		/**
		 * @param movementId
		 *            The movementId to set.
		 */
		public void setMovementId(Long movementId) {
			this.movementId = movementId;
		}
	}




	public String getOrderingCustomer1() {
		return orderingCustomer1;
	}



	public void setOrderingCustomer1(String orderingCustomer1) {
		this.orderingCustomer1 = orderingCustomer1;
	}



	public String getOrderingCustomer2() {
		return orderingCustomer2;
	}



	public void setOrderingCustomer2(String orderingCustomer2) {
		this.orderingCustomer2 = orderingCustomer2;
	}



	public String getOrderingCustomer3() {
		return orderingCustomer3;
	}



	public void setOrderingCustomer3(String orderingCustomer3) {
		this.orderingCustomer3 = orderingCustomer3;
	}



	public String getOrderingCustomer4() {
		return orderingCustomer4;
	}



	public void setOrderingCustomer4(String orderingCustomer4) {
		this.orderingCustomer4 = orderingCustomer4;
	}



	public String getOrderingCustomer5() {
		return orderingCustomer5;
	}



	public void setOrderingCustomer5(String orderingCustomer5) {
		this.orderingCustomer5 = orderingCustomer5;
	}




	public String getOrderingInstitution1() {
		return orderingInstitution1;
	}



	public void setOrderingInstitution1(String orderingInstitution1) {
		this.orderingInstitution1 = orderingInstitution1;
	}



	public String getOrderingInstitution2() {
		return orderingInstitution2;
	}



	public void setOrderingInstitution2(String orderingInstitution2) {
		this.orderingInstitution2 = orderingInstitution2;
	}



	public String getOrderingInstitution3() {
		return orderingInstitution3;
	}



	public void setOrderingInstitution3(String orderingInstitution3) {
		this.orderingInstitution3 = orderingInstitution3;
	}



	public String getOrderingInstitution4() {
		return orderingInstitution4;
	}



	public void setOrderingInstitution4(String orderingInstitution4) {
		this.orderingInstitution4 = orderingInstitution4;
	}



	public String getOrderingInstitution5() {
		return orderingInstitution5;
	}



	public void setOrderingInstitution5(String orderingInstitution5) {
		this.orderingInstitution5 = orderingInstitution5;
	}



	public String getSenderCorrespondent1() {
		return senderCorrespondent1;
	}



	public void setSenderCorrespondent1(String senderCorrespondent1) {
		this.senderCorrespondent1 = senderCorrespondent1;
	}



	public String getSenderCorrespondent2() {
		return senderCorrespondent2;
	}



	public void setSenderCorrespondent2(String senderCorrespondent2) {
		this.senderCorrespondent2 = senderCorrespondent2;
	}



	public String getSenderCorrespondent3() {
		return senderCorrespondent3;
	}



	public void setSenderCorrespondent3(String senderCorrespondent3) {
		this.senderCorrespondent3 = senderCorrespondent3;
	}



	public String getSenderCorrespondent4() {
		return senderCorrespondent4;
	}



	public void setSenderCorrespondent4(String senderCorrespondent4) {
		this.senderCorrespondent4 = senderCorrespondent4;
	}



	public String getSenderCorrespondent5() {
		return senderCorrespondent5;
	}



	public void setSenderCorrespondent5(String senderCorrespondent5) {
		this.senderCorrespondent5 = senderCorrespondent5;
	}




	public String getReceiverCorrespondent1() {
		return receiverCorrespondent1;
	}



	public void setReceiverCorrespondent1(String receiverCorrespondent1) {
		this.receiverCorrespondent1 = receiverCorrespondent1;
	}



	public String getReceiverCorrespondent2() {
		return receiverCorrespondent2;
	}



	public void setReceiverCorrespondent2(String receiverCorrespondent2) {
		this.receiverCorrespondent2 = receiverCorrespondent2;
	}



	public String getReceiverCorrespondent3() {
		return receiverCorrespondent3;
	}



	public void setReceiverCorrespondent3(String receiverCorrespondent3) {
		this.receiverCorrespondent3 = receiverCorrespondent3;
	}



	public String getReceiverCorrespondent4() {
		return receiverCorrespondent4;
	}



	public void setReceiverCorrespondent4(String receiverCorrespondent4) {
		this.receiverCorrespondent4 = receiverCorrespondent4;
	}



	public String getReceiverCorrespondent5() {
		return receiverCorrespondent5;
	}



	public void setReceiverCorrespondent5(String receiverCorrespondent5) {
		this.receiverCorrespondent5 = receiverCorrespondent5;
	}



	public String getIntermediaryInstitution1() {
		return intermediaryInstitution1;
	}



	public void setIntermediaryInstitution1(String intermediaryInstitution1) {
		this.intermediaryInstitution1 = intermediaryInstitution1;
	}



	public String getIntermediaryInstitution2() {
		return intermediaryInstitution2;
	}



	public void setIntermediaryInstitution2(String intermediaryInstitution2) {
		this.intermediaryInstitution2 = intermediaryInstitution2;
	}



	public String getIntermediaryInstitution3() {
		return intermediaryInstitution3;
	}



	public void setIntermediaryInstitution3(String intermediaryInstitution3) {
		this.intermediaryInstitution3 = intermediaryInstitution3;
	}



	public String getIntermediaryInstitution4() {
		return intermediaryInstitution4;
	}



	public void setIntermediaryInstitution4(String intermediaryInstitution4) {
		this.intermediaryInstitution4 = intermediaryInstitution4;
	}



	public String getIntermediaryInstitution5() {
		return intermediaryInstitution5;
	}



	public void setIntermediaryInstitution5(String intermediaryInstitution5) {
		this.intermediaryInstitution5 = intermediaryInstitution5;
	}



	public String getAccountWithInstitution1() {
		return accountWithInstitution1;
	}



	public void setAccountWithInstitution1(String accountWithInstitution1) {
		this.accountWithInstitution1 = accountWithInstitution1;
	}



	public String getAccountWithInstitution2() {
		return accountWithInstitution2;
	}



	public void setAccountWithInstitution2(String accountWithInstitution2) {
		this.accountWithInstitution2 = accountWithInstitution2;
	}



	public String getAccountWithInstitution3() {
		return accountWithInstitution3;
	}



	public void setAccountWithInstitution3(String accountWithInstitution3) {
		this.accountWithInstitution3 = accountWithInstitution3;
	}



	public String getAccountWithInstitution4() {
		return accountWithInstitution4;
	}



	public void setAccountWithInstitution4(String accountWithInstitution4) {
		this.accountWithInstitution4 = accountWithInstitution4;
	}



	public String getAccountWithInstitution5() {
		return accountWithInstitution5;
	}



	public void setAccountWithInstitution5(String accountWithInstitution5) {
		this.accountWithInstitution5 = accountWithInstitution5;
	}



	public String getBeneficiaryCustomer1() {
		return beneficiaryCustomer1;
	}



	public void setBeneficiaryCustomer1(String beneficiaryCustomer1) {
		this.beneficiaryCustomer1 = beneficiaryCustomer1;
	}



	public String getBeneficiaryCustomer2() {
		return beneficiaryCustomer2;
	}



	public void setBeneficiaryCustomer2(String beneficiaryCustomer2) {
		this.beneficiaryCustomer2 = beneficiaryCustomer2;
	}



	public String getBeneficiaryCustomer3() {
		return beneficiaryCustomer3;
	}



	public void setBeneficiaryCustomer3(String beneficiaryCustomer3) {
		this.beneficiaryCustomer3 = beneficiaryCustomer3;
	}



	public String getBeneficiaryCustomer4() {
		return beneficiaryCustomer4;
	}



	public void setBeneficiaryCustomer4(String beneficiaryCustomer4) {
		this.beneficiaryCustomer4 = beneficiaryCustomer4;
	}



	public String getBeneficiaryCustomer5() {
		return beneficiaryCustomer5;
	}



	public void setBeneficiaryCustomer5(String beneficiaryCustomer5) {
		this.beneficiaryCustomer5 = beneficiaryCustomer5;
	}



	public String getSenderToReceiverInfo1() {
		return senderToReceiverInfo1;
	}



	public void setSenderToReceiverInfo1(String senderToReceiverInfo1) {
		this.senderToReceiverInfo1 = senderToReceiverInfo1;
	}



	public String getSenderToReceiverInfo2() {
		return senderToReceiverInfo2;
	}



	public void setSenderToReceiverInfo2(String senderToReceiverInfo2) {
		this.senderToReceiverInfo2 = senderToReceiverInfo2;
	}



	public String getSenderToReceiverInfo3() {
		return senderToReceiverInfo3;
	}



	public void setSenderToReceiverInfo3(String senderToReceiverInfo3) {
		this.senderToReceiverInfo3 = senderToReceiverInfo3;
	}



	public String getSenderToReceiverInfo4() {
		return senderToReceiverInfo4;
	}



	public void setSenderToReceiverInfo4(String senderToReceiverInfo4) {
		this.senderToReceiverInfo4 = senderToReceiverInfo4;
	}



	public String getSenderToReceiverInfo5() {
		return senderToReceiverInfo5;
	}



	public void setSenderToReceiverInfo5(String senderToReceiverInfo5) {
		this.senderToReceiverInfo5 = senderToReceiverInfo5;
	}



	public String getSenderToReceiverInfo6() {
		return senderToReceiverInfo6;
	}



	public void setSenderToReceiverInfo6(String senderToReceiverInfo6) {
		this.senderToReceiverInfo6 = senderToReceiverInfo6;
	}


	public Id getId() {
		return id;
	}



	public void setId(Id id) {
		this.id = id;
	}

	/**
	 * @return Returns the updateDate.
	 */
	public Date getUpdateDate() {
		return updateDate;
	}

	/**
	 * @param updateDate
	 *            The updateDate to set.
	 */
	public void setUpdateDate(Date updateDate) {
		this.updateDate = updateDate;
	}

	/**
	 * @return Returns the updateUser.
	 */
	public String getUpdateUser() {
		return updateUser;
	}

	/**
	 * @param updateUser
	 *            The updateUser to set.
	 */
	public void setUpdateUser(String updateUser) {
		this.updateUser = updateUser;
	}

	
	public String getExtraText1() {
		return extraText1;
	}



	public void setExtraText1(String extraText1) {
		this.extraText1 = extraText1;
	}



	public String getExtraText2() {
		return extraText2;
	}



	public void setExtraText2(String extraText2) {
		this.extraText2 = extraText2;
	}



	public String getExtraText3() {
		return extraText3;
	}



	public void setExtraText3(String extraText3) {
		this.extraText3 = extraText3;
	}



	public String getExtraText4() {
		return extraText4;
	}



	public void setExtraText4(String extraText4) {
		this.extraText4 = extraText4;
	}



	public String getExtraText5() {
		return extraText5;
	}



	public void setExtraText5(String extraText5) {
		this.extraText5 = extraText5;
	}



	public String getExtraText6() {
		return extraText6;
	}



	public void setExtraText6(String extraText6) {
		this.extraText6 = extraText6;
	}



	public String getExtraText7() {
		return extraText7;
	}



	public void setExtraText7(String extraText7) {
		this.extraText7 = extraText7;
	}



	public String getExtraText8() {
		return extraText8;
	}



	public void setExtraText8(String extraText8) {
		this.extraText8 = extraText8;
	}



	public String getExtraText9() {
		return extraText9;
	}



	public void setExtraText9(String extraText9) {
		this.extraText9 = extraText9;
	}



	public String getExtraText10() {
		return extraText10;
	}



	public void setExtraText10(String extraText10) {
		this.extraText10 = extraText10;
	}

    public String getLastMmaUpdateNote() {
        return lastMmaUpdateNote;
    }

	public void setLastMmaUpdateNote(String lastMmaUpdateNote) {
		this.lastMmaUpdateNote = lastMmaUpdateNote;
	}

	public Integer getLastMmaUpdateEventId() {
        return lastMmaUpdateEventId;
    }

	public void setLastMmaUpdateEventId(Integer lastMmaUpdateEventId) {
		this.lastMmaUpdateEventId = lastMmaUpdateEventId;
	}

	public boolean checkNull() throws IllegalAccessException {
	    for (Field f : getClass().getDeclaredFields()) {
	    	if(!f.getName().equals("isUpdateUserNeedUpdated") && !f.getName().equals("isUpdateDateNeedUpdated") &&
	    			!f.getName().equals("serialVersionUID") && !f.getName().equals("id")) {
		    	if(f.getType().equals(String.class)) {
		    		 if(f.get(this) != null && !SwtUtil.isEmptyOrNull(f.get(this).toString())) {
		    			 return false;
		    		 }
		    	}else {
			        if (f.get(this) != null) {
			            return false;
			        }
		    	}
	    	}
	    }
	    return true;            
	}
	
}
