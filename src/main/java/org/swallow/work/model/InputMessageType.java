package org.swallow.work.model;

import org.swallow.model.BaseObject;

public class InputMessageType extends BaseObject {
	private String messageType = null;
	private String description = null;
	private String groupId = null;
	private int errorNumber;
	private String tempTable = null;
	private String procedure = null;
	private String updateColumn = null;
	private String tempTableId = null;
	private String affType = null;
	
	public String getMessageType() {
		return messageType;
	}
	public void setMessageType(String messageType) {
		this.messageType = messageType;
	}
	public String getDescription() {
		return description;
	}
	public void setDescription(String description) {
		this.description = description;
	}
	public String getGroupId() {
		return groupId;
	}
	public void setGroupId(String groupid) {
		this.groupId = groupid;
	}
	public int getErrorNumber() {
		return errorNumber;
	}
	public void setErrorNumber(int errorNumber) {
		this.errorNumber = errorNumber;
	}
	public String getTempTable() {
		return tempTable;
	}
	public void setTempTable(String tempTable) {
		this.tempTable = tempTable;
	}
	public String getProcedure() {
		return procedure;
	}
	public void setProcedure(String procedure) {
		this.procedure = procedure;
	}
	public String getUpdateColumn() {
		return updateColumn;
	}
	public void setUpdateColumn(String updateColumn) {
		this.updateColumn = updateColumn;
	}
	public String getTempTableId() {
		return tempTableId;
	}
	public void setTempTableId(String tempTableId) {
		this.tempTableId = tempTableId;
	}
	public String getAffType() {
		return affType;
	}
	public void setAffType(String affType) {
		this.affType = affType;
	}
}
