/*
 * Created on Sep 14, 2006
 *
 * TODO To change the template for this generated file go to
 * Window - Preferences - Java - Code Style - Code Templates
 */
package org.swallow.work.model;

import java.io.Serializable;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Types;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

import org.hibernate.HibernateException;
import org.hibernate.JDBCException;
import org.hibernate.MappingException;
import org.hibernate.dialect.Dialect;
import org.hibernate.engine.spi.SessionImplementor;
import org.hibernate.engine.spi.SharedSessionContractImplementor;
import org.hibernate.exception.spi.SQLExceptionConverter;
import org.hibernate.id.Configurable;
import org.hibernate.id.IdentifierGenerator;
import org.hibernate.type.LongType;
import org.hibernate.type.StringType;
import org.hibernate.type.Type;
import org.hibernate.usertype.CompositeUserType;

/**
 * <AUTHOR>
 *
 * TODO To change the template for this generated type comment go to
 * Window - Preferences - Java - Code Style - Code Templates
 */
public class SweepAlertIdType implements CompositeUserType, Serializable{
	
	/*
	 * Log object for debuging
	 */
	private final Log log = LogFactory.getLog(SweepAlertIdType.class);	
	
	/*
	 * Property Names involved in defining Id for SweepAlert object
	 */
	private static final String[] PROPERTY_NAMES = {"hostId", "entityId" ,"alertId"}; 

	/*
	 * Hibernate Mapping for above columns.
	 */
	private static final Type[] PROPERTY_TYPES = {StringType.INSTANCE, StringType.INSTANCE , LongType.INSTANCE}; 


	/* (non-Javadoc)
	 * @see net.sf.hibernate.CompositeUserType#getPropertyNames()
	 */
	public String[] getPropertyNames() {
		
		return PROPERTY_NAMES;		
	}

	/* (non-Javadoc)
	 * @see net.sf.hibernate.CompositeUserType#getPropertyTypes()
	 */
	public Type[] getPropertyTypes() {

		return PROPERTY_TYPES;
	}

	/* (non-Javadoc)
	 * @see net.sf.hibernate.CompositeUserType#getPropertyValue(java.lang.Object, int)
	 */
	public Object getPropertyValue(Object component, int property) 
	throws HibernateException {
		log.debug("Inside SweepAlertIdType.getPropertyValue ");
		   	  
	   	  SweepAlert.Id sweepAlertIdObject = (SweepAlert.Id)component;
	   	  
	      switch (property) { 
	      case 0: 
	      	log.debug("Exit SweepAlertIdType.getPropertyValue hostId " + sweepAlertIdObject.getHostId());
	         return sweepAlertIdObject.getHostId(); 
	      case 1: 
	      	log.debug("Exit SweepAlertIdType.getPropertyValue getEntityId " + sweepAlertIdObject.getEntityId());
	         return sweepAlertIdObject.getEntityId();
	      case 2: 
	      	log.debug("Exit SweepAlertIdType.getPropertyValue getAlertId " + sweepAlertIdObject.getAlertId());
	         return sweepAlertIdObject.getAlertId();
	         
	      default: 
	         throw new HibernateException(property + " is invalid number"); 
	      } 
	      
	      
	   }

	/* (non-Javadoc)
	 * @see net.sf.hibernate.CompositeUserType#setPropertyValue(java.lang.Object, int, java.lang.Object)
	 */
	public void setPropertyValue(Object component, int property, Object value) 
	throws HibernateException { 
	   	 log.debug("Inside SweepAlertIdType.setPropertyValue ");
	   	 log.debug("The component is: " + component.getClass());
		 SweepAlert.Id sweepAlertIdObject = (SweepAlert.Id) component;
	   	  log.debug("Component is set to the id object");
	      switch (property) 
		  { 
		      case 0:
		      	sweepAlertIdObject.setHostId((String)value);
		      	break;
		      case 1: 
		      	sweepAlertIdObject.setEntityId((String)value);
		      	break;
		      case 2: 
		      	sweepAlertIdObject.setAlertId((Long)value);
		      	break;
		      default: 
		         throw new HibernateException(property + " is invalid index"); 
	      }
	      
	   }

	/* (non-Javadoc)
	 * @see net.sf.hibernate.CompositeUserType#returnedClass()
	 */
	public Class returnedClass() {
		//TODO : Check It is ok or not
		return SweepAlert.Id.class;
	}

	/* (non-Javadoc)
	 * @see net.sf.hibernate.CompositeUserType#equals(java.lang.Object, java.lang.Object)
	 */
	public boolean equals(Object x, Object y) 
	throws HibernateException { 
	      if (x == y) return true; 
	      if (x == null || y == null) return false; 
	      return x.equals(y); 
	   }

	/* (non-Javadoc)
	 * @see net.sf.hibernate.CompositeUserType#nullSafeGet(java.sql.ResultSet, java.lang.String[], net.sf.hibernate.engine.SessionImplementor, java.lang.Object)
	 */
	public Object nullSafeGet(ResultSet rs, String[] names, SessionImplementor session, Object owner) 
	throws HibernateException, SQLException { 
		 log.debug("Inside SweepAlertIdType.nullSafeGet ");
		if (rs.getObject(names[0])!=null && rs.getObject(names[1])!=null){
			String hostId = rs.getString(names[0]);
			String entityId = rs.getString(names[1]);
	   		long alertId = rs.getLong(names[2]);
	   		
	   		SweepAlert.Id sweepAlertIdObj = new SweepAlert.Id();
	   		sweepAlertIdObj.setHostId(hostId);
	   		sweepAlertIdObj.setEntityId(entityId);	
	   		sweepAlertIdObj.setAlertId(new Long(alertId));		   		
	   		return sweepAlertIdObj;
	   	} 
	   	else 
	   	{ 
	   		return null; 
	   	} 
   }

	/* (non-Javadoc)
	 * @see net.sf.hibernate.CompositeUserType#nullSafeSet(java.sql.PreparedStatement, java.lang.Object, int, net.sf.hibernate.engine.SessionImplementor)
	 */
	public void nullSafeSet(PreparedStatement st, Object value, int index, SessionImplementor session) 
	throws HibernateException, SQLException {
		 log.debug("Inside SweepAlertIdType.nullSafeSet ");
	      if (value==null) { 
	         st.setNull(index, Types.VARCHAR); 
	         st.setNull(index+1, Types.VARCHAR);
	         st.setNull(index+1, Types.NUMERIC);	         
	      } 
	      else 
	      { 	      	
	      	SweepAlert.Id sweepAlertIdObj = (SweepAlert.Id)value;
	      	
	         // Setting the host id
	      	 if (sweepAlertIdObj.getHostId() == null) { 
	            st.setNull(index, Types.VARCHAR); 
	         } 
	         else { 
	            st.setString(index, sweepAlertIdObj.getHostId()); 
	         } 
	      	 
	         // Setting the entity id
	         if (sweepAlertIdObj.getEntityId() == null) { 
	            st.setNull(index+1, Types.VARCHAR); 
	         } 
	         else { 
	         	st.setString(index+1, sweepAlertIdObj.getEntityId()); 
	         }
	         
	         // Setting the match id
	         if (sweepAlertIdObj.getAlertId() == null) { 
	            st.setNull(index+2, Types.VARCHAR); // TODO 
	         } 
	         else { 
	         	st.setLong(index+2, sweepAlertIdObj.getAlertId().longValue()); 
	         }	         	         
	      } 
	   }

	/* (non-Javadoc)
	 * @see net.sf.hibernate.CompositeUserType#deepCopy(java.lang.Object)
	 */
	public Object deepCopy(Object value) 
	throws HibernateException {
		 log.debug("Inside SweepAlertIdType.deepCopy ");
		return value;
	}

	/* (non-Javadoc)
	 * @see net.sf.hibernate.CompositeUserType#isMutable()
	 */
	public boolean isMutable() {
		
		return false;
	}

	/* (non-Javadoc)
	 * @see net.sf.hibernate.CompositeUserType#disassemble(java.lang.Object, net.sf.hibernate.engine.SessionImplementor)
	 */
	public Serializable disassemble(Object value, SessionImplementor session) 
	throws HibernateException {
		log.debug("Inside SweepAlertIdType.disassemble ");
		 return (Serializable)value;
	}

	/* (non-Javadoc)
	 * @see net.sf.hibernate.CompositeUserType#assemble(java.io.Serializable, net.sf.hibernate.engine.SessionImplementor, java.lang.Object)
	 */
	public Object assemble(Serializable cached, SessionImplementor session, Object owner)
	throws HibernateException {
		 log.debug("Inside SweepAlertIdType.assemble ");
		return cached;
	}

	
	@Override
	public int hashCode(Object x) throws HibernateException {
		return super.hashCode();
	}

	@Override
	public Object nullSafeGet(ResultSet rs, String[] names, SharedSessionContractImplementor session, Object owner)
			throws HibernateException, SQLException {
		return nullSafeGet(rs, names, (SessionImplementor) session, owner);
	}

	@Override
	public void nullSafeSet(PreparedStatement st, Object value, int index, SharedSessionContractImplementor session)
			throws HibernateException, SQLException {
		nullSafeSet(st, value, index, (SessionImplementor) session);
	}

	@Override
	public Serializable disassemble(Object value, SharedSessionContractImplementor session) throws HibernateException {
		return disassemble(value, (SessionImplementor) session);
	}

	@Override
	public Object assemble(Serializable cached, SharedSessionContractImplementor session, Object owner)
			throws HibernateException {
		return assemble(session, (SessionImplementor) session, owner);
	}

	@Override
	public Object replace(Object original, Object target, SharedSessionContractImplementor session, Object owner)
			throws HibernateException {
		throw new HibernateException("Method [replace] not implemented in ForecastTemplateIdType");
	}
}
