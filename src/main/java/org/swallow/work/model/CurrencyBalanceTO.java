/*
 * @(#)CurrencyBalanceTO.java 1.0 23/08/06
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
/*
 * Created on Aug 23, 2006
 *
 * TODO To change the template for this generated file go to
 * Window - Preferences - Java - Code Style - Code Templates
 */
package org.swallow.work.model;

import java.io.Serializable;
import java.util.Date;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;


/**
 * <AUTHOR>
 *
 * TODO To change the template for this generated type comment go to
 * Window - Preferences - Java - Code Style - Code Templates
 */
public class CurrencyBalanceTO implements Serializable{
    /** stores the instance of log object */
    private final Log log = LogFactory.getLog(CurrencyBalanceTO.class);

    /**
     * startBalance
     */
    private double startBalance;

    /**
     * todayBalance
     */
    private double todayBalance;

    /**
     * unexpectedBal
     */
    private double unexpectedBal;

    /**
     * predictedBalance
     */
    private double predictedBalance;
    
    private Date timeStamp;

    /**
     * Creates a new CurrencyBalanceTO object.
     *
     * @param startBalance 
     * @param todayBalance 
     * @param unexpectedBal 
     * @param predictedBalance 
     */
    public CurrencyBalanceTO(double startBalance, double todayBalance,
        double unexpectedBal, double predictedBalance, Date timeStamp) {
        this.startBalance = startBalance;
        this.todayBalance = todayBalance;
        this.unexpectedBal = unexpectedBal;
        this.predictedBalance = predictedBalance;
        this.timeStamp=timeStamp;
    }

    /**
     *This function overrides the equals function of Object class.
     */
    public boolean equals(Object obj) {
        log.debug("entering equals method");

        boolean retValue = false;

        if ((obj != null) && obj instanceof CurrencyBalanceTO) {
            log.debug("obj - " + obj);
            retValue = new Double(startBalance).equals(new Double(
                        ((CurrencyBalanceTO) obj).getStartBalance())) &&
                new Double(todayBalance).equals(new Double(
                        ((CurrencyBalanceTO) obj).getTodayBalance())) &&
                new Double(unexpectedBal).equals(new Double(
                        ((CurrencyBalanceTO) obj).getUnexpectedBal())) &&
                new Double(predictedBalance).equals(new Double(
                        ((CurrencyBalanceTO) obj).getPredictedBalance()));
        }

        log.debug("retValue - " + retValue);
        log.debug("exiting equals method");

        return retValue;
    }

    /**
     *This function overrides the hashCode function of Object class.
     */
    public int hashCode() {
        return new Double(startBalance).hashCode() +
        new Double(todayBalance).hashCode() +
        new Double(unexpectedBal).hashCode() +
        new Double(predictedBalance).hashCode();
    }

    /**
     * @return Returns the predictedBalance.
     */
    public double getPredictedBalance() {
        return predictedBalance;
    }

    /**
     * @param predictedBalance The predictedBalance to set.
     */
    public void setPredictedBalance(double predictedBalance) {
        this.predictedBalance = predictedBalance;
    }

    /**
     * @return Returns the startBalance.
     */
    public double getStartBalance() {
        return startBalance;
    }

    /**
     * @param startBalance The startBalance to set.
     */
    public void setStartBalance(double startBalance) {
        this.startBalance = startBalance;
    }

    /**
     * @return Returns the todayBalance.
     */
    public double getTodayBalance() {
        return todayBalance;
    }

    /**
     * @param todayBalance The todayBalance to set.
     */
    public void setTodayBalance(double todayBalance) {
        this.todayBalance = todayBalance;
    }

    /**
     * @return Returns the unexpectedBal.
     */
    public double getUnexpectedBal() {
        return unexpectedBal;
    }

    /**
     * @param unexpectedBal The unexpectedBal to set.
     */
    public void setUnexpectedBal(double unexpectedBal) {
        this.unexpectedBal = unexpectedBal;
    }
	/**
	 * @return Returns the timeStamp.
	 */
	public Date getTimeStamp() {
		return timeStamp;
	}
	/**
	 * @param timeStamp The timeStamp to set.
	 */
	public void setTimeStamp(Date timeStamp) {
		this.timeStamp = timeStamp;
	}
}
