/*
 * @(#)Sweep.java 1.0 
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.work.model;

import java.util.Date;

import org.swallow.maintenance.model.AcctMaintenance;
import org.swallow.maintenance.model.Currency;
import org.swallow.maintenance.model.Entity;
import org.swallow.model.BaseObject;
import org.swallow.util.SwtUtil;

/**
 * <AUTHOR>
 * 
 * 
 * This class contains getters and setters for  SweepSearch screen
 */
public class Sweep extends BaseObject {
	/**
	 * currencycode as String
	 */
	private String currencyCode=null;
	/**
	 * currencyGroup as String
	 */
	private String currencyGroup=null;
	/**
	 * accountIdCr as String
	 */
	private String accountIdCr=null;
	/**
	 * accountIdDr as String
	 */

	private String accountIdDr=null;
	/**
	 * entityIdCr as String
	 */
	private String entityIdCr=null;
	/**
	 * entityIdDr as String
	 */
	private String entityIdDr=null;
	/**
	 * sweepGroupId as String
	 */

	private String sweepGroupId=null;
	/**
	 * movementIdCr as String
	 */

	private String movementIdCr=null;
	/**
	 * movementIddr as String
	 */

	private String movementIdDr=null;
	/**
	 * orginalSweepAmt as Double
	 */

	private Double originalSweepAmt=null;
	/**
	 * SubmitSweepAmt as Double
	 */

	private Double submitSweepAmt=null;
	/**
	 * movementIdCr as Double
	 */

	private Double authorizeSweepAmt=null;
	/**
	 * SweepType as String
	 */

	private String sweepType=null;
	/**
	 * SweepStatus as String
	 */

	private String sweepStatus=null;
	/**
	 * inputDateTime as Date
	 */

	private Date inputDateTime=null;
	/**
	 * subitDateTime as Date
	 */

	private Date submitDateTime=null;
	/**
	 * cancelDateTime as Date
	 */

	private Date cancelDateTime=null;
	/**
	 * authDateTime as Date
	 */
	private Date authDateTime=null;
	/**
	 * inputUser as String
	 */
	private String inputUser=null;
	/**
	 * SubmitUser as String
	 */
	private String submitUser=null;
	/**
	 * authorizedUser as String
	 */
	private String authorizedUser=null;
	/**
	 * cancelUser as String
	 */
	private String cancelUser=null;
	/**
	 * valueDate as Date
	 */
	private Date valueDate=null;
	/**
	 * valueDateAsString as String
	 */
	private String valueDateAsString=null;
	/**
	 * alignAccountId as String
	 */
	private String alignAccountId=null;
	/**
	 * requestUser as String
	 */
	// used by action to set request parameters
	private String requestUser=null;
	// used by action to set request parameters
	private String requestRole=null;
	/**
	 * queueNameas String
	 */
	private String queueName=null;
	/**
	 * accountType as String
	 */
	private String accountType=null;
	/**
	 * entityAccess as String
	 */
	private String entityAccess=null;
	// used by jsp to set different color of
	private String cutOffExceeded=null;
	/**
	 * displayUser as String
	 */
	private String displayUser=null; 
	/**
	 * displaySweepAmount as String
	 */
	private String displaySweepAmount=null;
	/**
	 *displayDateTimeUser as String
	 */
	private String displayDateTimeUser=null;
	/**
	 * displayStatus as String
	 */
	private String displayStatus=null;
	/**
	 * sortOrder as String
	 */
	private String sortOrder=null;
	/**
	 * sortOrder as String
	 */
	private String accType=null;
	/**
	 * book as String
	 */
	private String book=null;
	/**
	 * msgFormat as String
	 */
	private String msgFormat=null;
	/**
	 * posLevel as Integer
	 */
	private Integer posLevel=null;
	/**
	 * amountover as Double
	 */
	private Double amountover=null;
	/**
	 * amountunder as Double
	 */
	private Double amountunder=null;
	/* start:code modified by nageswararao for mantis 1597 on *********/
	// String variable for amountOverAsString
	private String amountOverAsString=null;
	// String variable for amountunderAsString
	private String amountUnderAsString=null;
	/* End:code modified by nageswararao for mantis 1597 on *********/
	/**
	 * postcutoff as String
	 */
	private String postcutoff=null;
	/**
	 * accountId as String
	 */
	private String accountId=null;
	/**
	 * ValueFrom Date as Date
	 */
	private Date valueFromDate = SwtUtil.getSystemDatewithoutTime();
	/**
	 * valueFromDateAsString as String
	 */
	private String valueFromDateAsString=null;
	/**
	 * ValueToDate as Date
	 */
	private Date valueToDate = SwtUtil.getSystemDatewithoutTime();
	/**
	 * valueToDateAsString as String
	 */
	private String valueToDateAsString=null;
	/**
	 * amt as String
	 */
	private String amt=null;
	/**
	 * user as String
	 */
	private String user=null;
	/**
	 * id Date as Id
	 */
	private Id id = new Id();
	/**
	 * entity Entity
	 */
	private Entity entityCr = new Entity();
	/**
	 * entity Entity
	 */
	private Entity entityDr = new Entity();
	/**
	 * accountCr as AcctMaintenance
	 */
	private AcctMaintenance accountCr = new AcctMaintenance();
	/**
	 * accountDr as AcctMaintenance
	 */
	private AcctMaintenance accountDr = new AcctMaintenance();
	/**
	 * currency as Currency
	 */
	private Currency currency = new Currency();


	/**
	 * generatedpostcutoffflg as String
	 */
	private String generatedpostcutoffflg=null;
	/**
	 * submittedpostcutoffflg as String
	 */
	private String submittedpostcutoffflg=null;
	/**
	 * authorisedpostcutoffflg as String
	 */
	private String authorisedpostcutoffflg=null;
	/**
	 * inputtime as String
	 */
	private String inputtime=null;
	/**
	 * submittedtime as String
	 */
	private String submittedtime=null;
	/**
	 * authorisetime as String
	 */
	private String authorisetime=null;
	/**
	 * outMessageDr as String
	 */
	private String outMessageDr=null;
	/**
	 * outMessageCr as String
	 */
	private String outMessageCr=null;
	

	
	/**
	 * originalSweepAmtasstring as String
	 */
	private String originalSweepAmtasstring=null;
	/**
	 * submitSweepAmtasstring as String
	 */
	private String submitSweepAmtasstring=null;
	/**
	 * authorizeSweepAmtasstring as String
	 */
	private String authorizeSweepAmtasstring=null;
	/**
	 * sweepamount as String
	 */
	private String sweepamount=null;
	/**
	 * newCalulatedAmount as String
	 */
	
	private String newCalulatedAmount=null;

	private String additionalReference;
	private String settleMethodCR = null;
	private String settleMethodDR = null;
	
	private String bookCodeCR = null;
	

	private String bookCodeDR = null;
	private Double targetBalance = null;
	private Double minAmount = null;
	private String targetBalanceasString = null;
	private String targetBalanceType = null;
	private String minAmountasString = null;
	
	private String accountIdOrigin = null;
	
	private String entityIdOrigin = null;
	
	private String sweepFromBalanceTypeCr = null;
	private String sweepFromBalanceTypeDr = null;
	private String entityId;
	
	private String targetBalanceTypId = null;
	/**
	 * cancelViewCurrGrpId  as String
	 */
	String cancelViewCurrGrpId = "";
	
	String sweepUetr1= null;
	String sweepUetr2= null;
	
	private String archiveId =null;

	/**
	 * @return Returns the entityId.
	 */
	public String getEntityId() {
		return entityId;
	}

	/**
	 * @param entityId
	 *            The entityId to set.
	 */
	public void setEntityId(String entityId) {
		this.entityId = entityId;
	}
	
	
	public static class Id extends BaseObject {
		private String hostId;
//		private String entityId;
		private Long sweepId;

		public Id() {
		}

		public Id(String hostId, String entityId, Long sweepId) {
			this.hostId = hostId;
//			this.entityId = entityId;
			this.sweepId = sweepId;

		}

		/**
		 * @return Returns the sweepId.
		 */
		public Long getSweepId() {
			return sweepId;
		}

		/**
		 * @param balanceType
		 *            The balanceType to set.
		 */
		public void setSweepId(Long sweepId) {
			this.sweepId = sweepId;
		}

//		/**
//		 * @return Returns the entityId.
//		 */
//		public String getEntityId() {
//			return entityId;
//		}
//
//		/**
//		 * @param entityId
//		 *            The entityId to set.
//		 */
//		public void setEntityId(String entityId) {
//			this.entityId = entityId;
//		}

		/**
		 * @return Returns the hostId.
		 */
		public String getHostId() {
			return hostId;
		}

		/**
		 * @param hostId
		 *            The hostId to set.
		 */
		public void setHostId(String hostId) {
			this.hostId = hostId;
		}

	}

	/**
	 * @return Returns the displayStatus.
	 */
	public String getDisplayStatus() {
		return displayStatus;
	}

	/**
	 * @param displayStatus
	 *            The displayStatus to set.
	 */
	public void setDisplayStatus(String displayStatus) {
		this.displayStatus = displayStatus;
	}


	/**
	 * @return Returns the authDateTime.
	 */
	public Date getAuthDateTime() {
		return authDateTime;
	}

	/**
	 * @param authDateTime
	 *            The authDateTime to set.
	 */
	public void setAuthDateTime(Date authDateTime) {
		this.authDateTime = authDateTime;
	}

	/**
	 * @return Returns the inputDateTime.
	 */
	public Date getInputDateTime() {
		return inputDateTime;
	}

	/**
	 * @param inputDateTime
	 *            The inputDateTime to set.
	 */
	public void setInputDateTime(Date inputDateTime) {
		this.inputDateTime = inputDateTime;
	}

	/**
	 * @return Returns the valueDate.
	 */
	public Date getValueDate() {
		return valueDate;
	}

	/**
	 * @param valueDate
	 *            The valueDate to set.
	 */
	public void setValueDate(Date valueDate) {
		this.valueDate = valueDate;
	}

	private Date updateDate;
	private String updateUser;

	/**
	 * @return Returns the id.
	 */
	public Id getId() {
		return id;
	}

	/**
	 * @param id
	 *            The id to set.
	 */
	public void setId(Id id) {
		this.id = id;
	}

	/**
	 * @return Returns the accountCr.
	 */
	public String getAccountIdCr() {
		return accountIdCr;
	}

	/**
	 * @param accountCr
	 *            The accountCr to set.
	 */
	public void setAccountIdCr(String accountCr) {
		this.accountIdCr = accountCr;
	}

	/**
	 * @return Returns the accountDr.
	 */
	public String getAccountIdDr() {
		return accountIdDr;
	}

	/**
	 * @return Returns the accountId.
	 */
	public String getAccountId() {
		return accountId;
	}

	/**
	 * @param accountId
	 *            The accountId to set.
	 */
	public void setAccountId(String accountId) {
		this.accountId = accountId;
	}

	/**
	 * @return Returns the accType.
	 */
	public String getAccType() {
		return accType;
	}

	/**
	 * @param accType
	 *            The accType to set.
	 */
	public void setAccType(String accType) {
		this.accType = accType;
	}

	/**
	 * @return Returns the amountover.
	 */
	public Double getAmountover() {
		return amountover;
	}

	/**
	 * @param amountover
	 *            The amountover to set.
	 */
	public void setAmountover(Double amountover) {
		this.amountover = amountover;
	}

	/**
	 * @return Returns the amountunder.
	 */
	public Double getAmountunder() {
		return amountunder;
	}

	/**
	 * @param amountunder
	 *            The amountunder to set.
	 */
	public void setAmountunder(Double amountunder) {
		this.amountunder = amountunder;
	}

	/**
	 * @return Returns the book.
	 */
	public String getBook() {
		return book;
	}

	/**
	 * @param book
	 *            The book to set.
	 */
	public void setBook(String book) {
		this.book = book;
	}

	/**
	 * @return Returns the msgFormat.
	 */
	public String getMsgFormat() {
		return msgFormat;
	}

	/**
	 * @param msgFormat
	 *            The msgFormat to set.
	 */
	public void setMsgFormat(String msgFormat) {
		this.msgFormat = msgFormat;
	}

	/**
	 * @return Returns the posLevel.
	 */
	public Integer getPosLevel() {
		return posLevel;
	}

	/**
	 * @param posLevel
	 *            The posLevel to set.
	 */
	public void setPosLevel(Integer posLevel) {
		this.posLevel = posLevel;
	}

	/**
	 * @return Returns the postcutoff.
	 */
	public String getPostcutoff() {
		return postcutoff;
	}

	/**
	 * @param postcutoff
	 *            The postcutoff to set.
	 */
	public void setPostcutoff(String postcutoff) {
		this.postcutoff = postcutoff;
	}

	/**
	 * @return Returns the sortOrder.
	 */
	public String getSortOrder() {
		return sortOrder;
	}

	/**
	 * @param sortOrder
	 *            The sortOrder to set.
	 */
	public void setSortOrder(String sortOrder) {
		this.sortOrder = sortOrder;
	}

	/**
	 * @return Returns the user.
	 */
	public String getUser() {
		return user;
	}

	/**
	 * @param user
	 *            The user to set.
	 */
	public void setUser(String user) {
		this.user = user;
	}

	/**
	 * @return Returns the valueFromDate.
	 */
	public Date getValueFromDate() {
		return valueFromDate;
	}

	/**
	 * @param valueFromDate
	 *            The valueFromDate to set.
	 */
	public void setValueFromDate(Date valueFromDate) {
		this.valueFromDate = valueFromDate;
	}

	/**
	 * @return Returns the valueFromDateAsString.
	 */
	public String getValueFromDateAsString() {
		return valueFromDateAsString;
	}

	/**
	 * @param valueFromDateAsString
	 *            The valueFromDateAsString to set.
	 */
	public void setValueFromDateAsString(String valueFromDateAsString) {
		this.valueFromDateAsString = valueFromDateAsString;
	}

	/**
	 * @return Returns the valueToDate.
	 */
	public Date getValueToDate() {
		return valueToDate;
	}

	/**
	 * @param valueToDate
	 *            The valueToDate to set.
	 */
	public void setValueToDate(Date valueToDate) {
		this.valueToDate = valueToDate;
	}

	/**
	 * @return Returns the valueToDateAsString.
	 */
	public String getValueToDateAsString() {
		return valueToDateAsString;
	}

	/**
	 * @param valueToDateAsString
	 *            The valueToDateAsString to set.
	 */
	public void setValueToDateAsString(String valueToDateAsString) {
		this.valueToDateAsString = valueToDateAsString;
	}

	/**
	 * @param accountDr
	 *            The accountDr to set.
	 */
	public void setAccountIdDr(String accountDr) {
		this.accountIdDr = accountDr;
	}

	/**
	 * @return Returns the currencyCode.
	 */
	public String getCurrencyCode() {
		return currencyCode;
	}

	/**
	 * @param currencyCode
	 *            The currencyCode to set.
	 */
	public void setCurrencyCode(String currencyCode) {
		this.currencyCode = currencyCode;
	}

	/**
	 * @return Returns the currencyGroup.
	 */
	public String getCurrencyGroup() {
		return currencyGroup;
	}

	/**
	 * @param currencyGroup
	 *            The currencyGroup to set.
	 */
	public void setCurrencyGroup(String currencyGroup) {
		this.currencyGroup = currencyGroup;
	}

	/**
	 * @return Returns the authorizeSweepAmt.
	 */
	public Double getAuthorizeSweepAmt() {
		return authorizeSweepAmt;
	}

	/**
	 * @param authorizeSweepAmt
	 *            The authorizeSweepAmt to set.
	 */
	public void setAuthorizeSweepAmt(Double authorizeSweepAmt) {
		this.authorizeSweepAmt = authorizeSweepAmt;
	}

	/**
	 * @return Returns the authorizeUser.
	 */
	public String getAuthorizedUser() {
		return authorizedUser;
	}

	/**
	 * @param authorizeUser
	 *            The authorizeUser to set.
	 */
	public void setAuthorizedUser(String authorizeUser) {
		this.authorizedUser = authorizeUser;
	}

	/**
	 * @return Returns the cancelUser.
	 */
	public String getCancelUser() {
		return cancelUser;
	}

	/**
	 * @param cancelUser
	 *            The cancelUser to set.
	 */
	public void setCancelUser(String cancelUser) {
		this.cancelUser = cancelUser;
	}

	/**
	 * @return Returns the inputUser.
	 */
	public String getInputUser() {
		return inputUser;
	}

	/**
	 * @param inputUser
	 *            The inputUser to set.
	 */
	public void setInputUser(String inputUser) {
		this.inputUser = inputUser;
	}

	/**
	 * @return Returns the movementIdCr.
	 */
	public String getMovementIdCr() {
		return movementIdCr;
	}

	/**
	 * @param movementIdCr
	 *            The movementIdCr to set.
	 */
	public void setMovementIdCr(String movementIdCr) {
		this.movementIdCr = movementIdCr;
	}

	/**
	 * @return Returns the movementIdDr.
	 */
	public String getMovementIdDr() {
		return movementIdDr;
	}

	/**
	 * @param movementIdDr
	 *            The movementIdDr to set.
	 */
	public void setMovementIdDr(String movementIdDr) {
		this.movementIdDr = movementIdDr;
	}

	/**
	 * @return Returns the originalSweepAmt.
	 */
	public Double getOriginalSweepAmt() {
		return originalSweepAmt;
	}

	/**
	 * @param originalSweepAmt
	 *            The originalSweepAmt to set.
	 */
	public void setOriginalSweepAmt(Double originalSweepAmt) {
		this.originalSweepAmt = originalSweepAmt;
	}

	/**
	 * @return Returns the submitSweepAmt.
	 */
	public Double getSubmitSweepAmt() {
		return submitSweepAmt;
	}

	/**
	 * @param submitSweepAmt
	 *            The submitSweepAmt to set.
	 */
	public void setSubmitSweepAmt(Double submitSweepAmt) {
		this.submitSweepAmt = submitSweepAmt;
	}

	/**
	 * @return Returns the submitUser.
	 */
	public String getSubmitUser() {
		return submitUser;
	}

	/**
	 * @param submitUser
	 *            The submitUser to set.
	 */
	public void setSubmitUser(String submitUser) {
		this.submitUser = submitUser;
	}

	/**
	 * @return Returns the sweepGroupId.
	 */
	public String getSweepGroupId() {
		return sweepGroupId;
	}

	/**
	 * @param sweepGroupId
	 *            The sweepGroupId to set.
	 */
	public void setSweepGroupId(String sweepGroupId) {
		this.sweepGroupId = sweepGroupId;
	}

	/**
	 * @return Returns the sweepType.
	 */
	public String getSweepType() {
		return sweepType;
	}

	/**
	 * @param sweepType
	 *            The sweepType to set.
	 */
	public void setSweepType(String sweepType) {
		this.sweepType = sweepType;
	}

	/**
	 * @return Returns the swepstatus.
	 */
	public String getSweepStatus() {
		return sweepStatus;
	}

	/**
	 * @param swepstatus
	 *            The swepstatus to set.
	 */
	public void setSweepStatus(String swepstatus) {
		this.sweepStatus = swepstatus;
	}

	/**
	 * @return Returns the updateDate.
	 */
	public Date getUpdateDate() {
		return updateDate;
	}

	/**
	 * @param updateDate
	 *            The updateDate to set.
	 */
	public void setUpdateDate(Date updateDate) {
		this.updateDate = updateDate;
	}

	/**
	 * @return Returns the updateUser.
	 */
	public String getUpdateUser() {
		return updateUser;
	}

	/**
	 * @param updateUser
	 *            The updateUser to set.
	 */
	public void setUpdateUser(String updateUser) {
		this.updateUser = updateUser;
	}

	/**
	 * @return Returns the queueName.
	 */
	public String getQueueName() {
		return queueName;
	}

	/**
	 * @param queueName
	 *            The queueName to set.
	 */
	public void setQueueName(String queueName) {
		this.queueName = queueName;
	}

	/**
	 * @return Returns the accountType.
	 */
	public String getAccountType() {
		return accountType;
	}

	/**
	 * @param accountType
	 *            The accountType to set.
	 */
	public void setAccountType(String accountType) {
		this.accountType = accountType;
	}

	/**
	 * @return Returns the requestUser.
	 */
	public String getRequestUser() {
		return requestUser;
	}

	/**
	 * @param requestUser
	 *            The requestUser to set.
	 */
	public void setRequestUser(String requestUser) {
		this.requestUser = requestUser;
	}

	/**
	 * @return Returns the requestRole.
	 */
	public String getRequestRole() {
		return requestRole;
	}

	/**
	 * @param requestRole
	 *            The requestRole to set.
	 */
	public void setRequestRole(String requestRole) {
		this.requestRole = requestRole;
	}

	/**
	 * @return Returns the accountCr.
	 */
	public AcctMaintenance getAccountCr() {
		return accountCr;
	}

	/**
	 * @param accountCr
	 *            The accountCr to set.
	 */
	public void setAccountCr(AcctMaintenance accountCr) {
		this.accountCr = accountCr;
	}

	/**
	 * @return Returns the accountDr.
	 */
	public AcctMaintenance getAccountDr() {
		return accountDr;
	}

	/**
	 * @param accountDr
	 *            The accountDr to set.
	 */
	public void setAccountDr(AcctMaintenance accountDr) {
		this.accountDr = accountDr;
	}

	/**
	 * @return Returns the entityAccess.
	 */
	public String getEntityAccess() {
		return entityAccess;
	}

	/**
	 * @param entityAccess
	 *            The entityAccess to set.
	 */
	public void setEntityAccess(String entityAccess) {
		this.entityAccess = entityAccess;
	}

	/**
	 * @return Returns the alignAccountId.
	 */
	public String getAlignAccountId() {
		return alignAccountId;
	}

	/**
	 * @return Returns the displaySweepAmount.
	 */
	public String getDisplaySweepAmount() {
		return displaySweepAmount;
	}

	/**
	 * @param displaySweepAmount
	 *            The displaySweepAmount to set.
	 */
	public void setDisplaySweepAmount(String displaySweepAmount) {
		this.displaySweepAmount = displaySweepAmount;
	}

	/**
	 * @return Returns the displayUser.
	 */
	public String getDisplayUser() {
		return displayUser;
	}

	/**
	 * @param displayUser
	 *            The displayUser to set.
	 */
	public void setDisplayUser(String displayUser) {
		this.displayUser = displayUser;
	}

	/**
	 * @param alignAccountId
	 *            The alignAccountId to set.
	 */
	public void setAlignAccountId(String alignAccountId) {
		this.alignAccountId = alignAccountId;
	}

	/**
	 * @return Returns the currency.
	 */
	public Currency getCurrency() {
		return currency;
	}

	/**
	 * @param currency
	 *            The currency to set.
	 */
	public void setCurrency(Currency currency) {
		this.currency = currency;
	}

	/**
	 * @return Returns the cutOffExceeded.
	 */
	public String getCutOffExceeded() {
		return cutOffExceeded;
	}

	/**
	 * @param cutOffExceeded
	 *            The cutOffExceeded to set.
	 */
	public void setCutOffExceeded(String cutOffExceeded) {
		this.cutOffExceeded = cutOffExceeded;
	}

	/**
	 * @return Returns the displayDateTimeUser.
	 */
	public String getDisplayDateTimeUser() {
		return displayDateTimeUser;
	}

	/**
	 * @param displayDateTimeUser
	 *            The displayDateTimeUser to set.
	 */
	public void setDisplayDateTimeUser(String displayDateTimeUser) {
		this.displayDateTimeUser = displayDateTimeUser;
	}

	/**
	 * @return Returns the valueDateAsString.
	 */
	public String getValueDateAsString() {
		return valueDateAsString;
	}

	/**
	 * @param valueDateAsString
	 *            The valueDateAsString to set.
	 */
	public void setValueDateAsString(String valueDateAsString) {
		this.valueDateAsString = valueDateAsString;
	}

	/**
	 * @return Returns the cancelDateTime.
	 */
	public Date getCancelDateTime() {
		return cancelDateTime;
	}

	/**
	 * @param cancelDateTime
	 *            The cancelDateTime to set.
	 */
	public void setCancelDateTime(Date cancelDateTime) {
		this.cancelDateTime = cancelDateTime;
	}

	/**
	 * @return Returns the submitDateTime.
	 */
	public Date getSubmitDateTime() {
		return submitDateTime;
	}

	/**
	 * @param submitDateTime
	 *            The submitDateTime to set.
	 */
	public void setSubmitDateTime(Date submitDateTime) {
		this.submitDateTime = submitDateTime;
	}

	/**
	 * @return Returns the entityIdCr.
	 */
	public String getEntityIdCr() {
		return entityIdCr;
	}

	/**
	 * @param entityIdCr
	 *            The entityIdCr to set.
	 */
	public void setEntityIdCr(String entityIdCr) {
		this.entityIdCr = entityIdCr;
	}

	/**
	 * @return Returns the entityIdDr.
	 */
	public String getEntityIdDr() {
		return entityIdDr;
	}

	/**
	 * @param entityIdDr
	 *            The entityIdDr to set.
	 */
	public void setEntityIdDr(String entityIdDr) {
		this.entityIdDr = entityIdDr;
	}

	/**
	 * @return Returns the authorisedpostcutoffflg.
	 */
	public String getAuthorisedpostcutoffflg() {
		return authorisedpostcutoffflg;
	}

	/**
	 * @param authorisedpostcutoffflg
	 *            The authorisedpostcutoffflg to set.
	 */
	public void setAuthorisedpostcutoffflg(String authorisedpostcutoffflg) {
		this.authorisedpostcutoffflg = authorisedpostcutoffflg;
	}

	/**
	 * @return Returns the generatedpostcutoffflg.
	 */
	public String getGeneratedpostcutoffflg() {
		return generatedpostcutoffflg;
	}

	/**
	 * @param generatedpostcutoffflg
	 *            The generatedpostcutoffflg to set.
	 */
	public void setGeneratedpostcutoffflg(String generatedpostcutoffflg) {
		this.generatedpostcutoffflg = generatedpostcutoffflg;
	}

	/**
	 * @return Returns the submittedpostcutoffflg.
	 */
	public String getSubmittedpostcutoffflg() {
		return submittedpostcutoffflg;
	}

	/**
	 * @param submittedpostcutoffflg
	 *            The submittedpostcutoffflg to set.
	 */
	public void setSubmittedpostcutoffflg(String submittedpostcutoffflg) {
		this.submittedpostcutoffflg = submittedpostcutoffflg;
	}

	/**
	 * @return Returns the authorisetime.
	 */
	public String getAuthorisetime() {
		return authorisetime;

	}

	/**
	 * @param authorisetime
	 *            The authorisetime to set.
	 */
	public void setAuthorisetime(String authorisetime) {
		this.authorisetime = authorisetime;
	}

	/**
	 * @return Returns the submittedtime.
	 */
	public String getSubmittedtime() {

		return submittedtime;
	}

	/**
	 * @param submittedtime
	 *            The submittedtime to set.
	 */
	public void setSubmittedtime(String submittedtime) {
		this.submittedtime = submittedtime;
	}

	/**
	 * @return Returns the authorizeSweepAmtasstring.
	 */
	public String getAuthorizeSweepAmtasstring() {
		return authorizeSweepAmtasstring;
	}

	/**
	 * @param authorizeSweepAmtasstring
	 *            The authorizeSweepAmtasstring to set.
	 */
	public void setAuthorizeSweepAmtasstring(String authorizeSweepAmtasstring) {
		this.authorizeSweepAmtasstring = authorizeSweepAmtasstring;
	}

	/**
	 * @return Returns the originalSweepAmtasstring.
	 */
	public String getOriginalSweepAmtasstring() {
		return originalSweepAmtasstring;
	}

	/**
	 * @param originalSweepAmtasstring
	 *            The originalSweepAmtasstring to set.
	 */
	public void setOriginalSweepAmtasstring(String originalSweepAmtasstring) {
		this.originalSweepAmtasstring = originalSweepAmtasstring;
	}

	/**
	 * @return Returns the submitSweepAmtasstring.
	 */
	public String getSubmitSweepAmtasstring() {
		return submitSweepAmtasstring;
	}

	/**
	 * @param submitSweepAmtasstring
	 *            The submitSweepAmtasstring to set.
	 */
	public void setSubmitSweepAmtasstring(String submitSweepAmtasstring) {
		this.submitSweepAmtasstring = submitSweepAmtasstring;
	}

	/**
	 * @return Returns the sweepamount.
	 */
	public String getSweepamount() {
		return sweepamount;
	}

	/**
	 * @param sweepamount
	 *            The sweepamount to set.
	 */
	public void setSweepamount(String sweepamount) {
		this.sweepamount = sweepamount;
	}

	/**
	 * @return Returns the amt.
	 */
	public String getAmt() {
		return amt;
	}

	/**
	 * @param amt
	 *            The amt to set.
	 */
	public void setAmt(String amt) {
		this.amt = amt;
	}

	/**
	 * @return Returns the inputtime.
	 */
	public String getInputtime() {
		return inputtime;
	}

	/**
	 * @param inputtime
	 *            The inputtime to set.
	 */
	public void setInputtime(String inputtime) {
		this.inputtime = inputtime;
	}

	/**
	 * @return Returns the outMessageCr.
	 */
	public String getOutMessageCr() {
		return outMessageCr;
	}

	/**
	 * @param outMessageCr
	 *            The outMessageCr to set.
	 */
	public void setOutMessageCr(String outMessageCr) {
		this.outMessageCr = outMessageCr;
	}

	/**
	 * @return Returns the outMessageDr.
	 */
	public String getOutMessageDr() {
		return outMessageDr;
	}

	/**
	 * @param outMessageDr
	 *            The outMessageDr to set.
	 */
	public void setOutMessageDr(String outMessageDr) {
		this.outMessageDr = outMessageDr;
	}

	/**
	 * @return Returns the newCalulatedAmount.
	 */
	public String getNewCalulatedAmount() {
		return newCalulatedAmount;
	}

	/**
	 * @param newCalulatedAmount
	 *            The newCalulatedAmount to set.
	 */
	public void setNewCalulatedAmount(String newCalulatedAmount) {
		this.newCalulatedAmount = newCalulatedAmount;
	}

	/**
	 * @return Returns the cancelViewCurrGrpId.
	 */
	public String getCancelViewCurrGrpId() {
		return cancelViewCurrGrpId;
	}

	/**
	 * @param cancelViewCurrGrpId
	 *            The cancelViewCurrGrpId to set.
	 */
	public void setCancelViewCurrGrpId(String cancelViewCurrGrpId) {
		this.cancelViewCurrGrpId = cancelViewCurrGrpId;
	}

	/**
	 * @return the amountoverAsString
	 */
	public String getAmountoverAsString() {
		return amountOverAsString;
	}

	/**
	 * @param amountoverAsString
	 *            the amountoverAsString to set
	 */
	public void setAmountoverAsString(String amountoverAsString) {
		this.amountOverAsString = amountoverAsString;
	}

	/**
	 * @return the amountunderAsString
	 */
	public String getAmountunderAsString() {
		return amountUnderAsString;
	}

	/**
	 * @param amountunderAsString
	 *            the amountunderAsString to set
	 */
	public void setAmountunderAsString(String amountunderAsString) {
		this.amountUnderAsString = amountunderAsString;
	}

	public String getAdditionalReference() {
		return additionalReference;
	}

	public void setAdditionalReference(String additionalReference) {
		this.additionalReference = additionalReference;
	}

	public String getSettleMethodCR() {
		return settleMethodCR;
	}

	public void setSettleMethodCR(String settleMethodCR) {
		this.settleMethodCR = settleMethodCR;
	}

	public String getSettleMethodDR() {
		return settleMethodDR;
	}

	public void setSettleMethodDR(String settleMethodDR) {
		this.settleMethodDR = settleMethodDR;
	}

	public String getBookCodeCR() {
		return bookCodeCR;
	}

	public void setBookCodeCR(String bookCodeCR) {
		this.bookCodeCR = bookCodeCR;
	}

	public String getBookCodeDR() {
		return bookCodeDR;
	}

	public void setBookCodeDR(String bookCodeDR) {
		this.bookCodeDR = bookCodeDR;
	}
	
	public Double getTargetBalance() {
		return targetBalance;
	}

	public void setTargetBalance(Double targetBalance) {
		this.targetBalance = targetBalance;
	}

	public Double getMinAmount() {
		return minAmount;
	}

	public void setMinAmount(Double minAmount) {
		this.minAmount = minAmount;
	}

	public String getTargetBalanceasString() {
		return targetBalanceasString;
	}

	public void setTargetBalanceasString(String targetBalanceasString) {
		this.targetBalanceasString = targetBalanceasString;
	}

	public String getTargetBalanceType() {
		return targetBalanceType;
	}

	public void setTargetBalanceType(String targetBalanceType) {
		this.targetBalanceType = targetBalanceType;
	}

	public String getMinAmountasString() {
		return minAmountasString;
	}

	public void setMinAmountasString(String minAmountasString) {
		this.minAmountasString = minAmountasString;
	}

	public String getAccountIdOrigin() {
		return accountIdOrigin;
	}

	public void setAccountIdOrigin(String accountIdOrigin) {
		this.accountIdOrigin = accountIdOrigin;
	}

	public String getEntityIdOrigin() {
		return entityIdOrigin;
	}

	public void setEntityIdOrigin(String entityIdOrigin) {
		this.entityIdOrigin = entityIdOrigin;
	}


	public String getSweepFromBalanceTypeCr() {
		return sweepFromBalanceTypeCr;
	}

	public void setSweepFromBalanceTypeCr(String sweepFromBalanceTypeCr) {
		this.sweepFromBalanceTypeCr = sweepFromBalanceTypeCr;
	}

	public String getSweepFromBalanceTypeDr() {
		return sweepFromBalanceTypeDr;
	}

	public void setSweepFromBalanceTypeDr(String sweepFromBalanceTypeDr) {
		this.sweepFromBalanceTypeDr = sweepFromBalanceTypeDr;
	}

	public Entity getEntityCr() {
		return entityCr;
	}

	public void setEntityCr(Entity entityCr) {
		this.entityCr = entityCr;
	}

	public Entity getEntityDr() {
		return entityDr;
	}

	public void setEntityDr(Entity entityDr) {
		this.entityDr = entityDr;
	}

	public String getTargetBalanceTypId() {
		return targetBalanceTypId;
	}

	public void setTargetBalanceTypId(String targetBalanceTypId) {
		this.targetBalanceTypId = targetBalanceTypId;
	}

	public String getSweepUetr1() {
		return sweepUetr1;
	}

	public void setSweepUetr1(String sweepUetr1) {
		this.sweepUetr1 = sweepUetr1;
	}

	public String getSweepUetr2() {
		return sweepUetr2;
	}

	public void setSweepUetr2(String sweepUetr2) {
		this.sweepUetr2 = sweepUetr2;
	}

	public String getArchiveId() {
		return archiveId;
	}

	public void setArchiveId(String archiveId) {
		this.archiveId = archiveId;
	}
	
	
}
