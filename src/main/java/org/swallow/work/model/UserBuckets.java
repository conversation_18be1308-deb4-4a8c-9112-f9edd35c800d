/*
 * @(#)UserBuckets.java 1.0 23/05/11
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.work.model;

import org.swallow.model.BaseObject;

/**
 * UserBuckets.java
 * 
 * This java bean has getters and setters for User Buckets details
 * 
 * <AUTHOR>
 * @date May 23, 2011
 * 
 */
public class UserBuckets extends BaseObject {

	/** Default version id */
	private static final long serialVersionUID = 1L;
	// days To
	private String daysTo = null;
	// bucket State
	private String bucketState = null;
	// Denote id
	private Id id = new Id();

	public static class Id extends BaseObject {
		/** Default version id */
		private static final long serialVersionUID = 1L;
		// host Id
		private String hostId = null;
		// user Id
		private String userId = null;
		// bucket Id
		private Integer bucketId = null;

		/**
		 * Default constructor
		 */
		public Id() {
		}

		/**
		 * Getter method for hostId
		 * 
		 * @return hostId as String
		 */

		public String getHostId() {
			return hostId;
		}

		/**
		 * Setter method for hostId
		 * 
		 * @param hostId
		 */

		public void setHostId(String hostId) {
			this.hostId = hostId;
		}

		/**
		 * Getter method for userId
		 * 
		 * @return userId as String
		 */

		public String getUserId() {
			return userId;
		}

		/**
		 * Setter method for userId
		 * 
		 * @param userId
		 */

		public void setUserId(String userId) {
			this.userId = userId;
		}

		/**
		 * Getter method for bucketId
		 * 
		 * @return bucketId as Integer
		 */

		public Integer getBucketId() {
			return bucketId;
		}

		/**
		 * Setter method for bucketId
		 * 
		 * @param bucketId
		 */

		public void setBucketId(Integer bucketId) {
			this.bucketId = bucketId;
		}

	}

	/**
	 * Getter method for daysTo
	 * 
	 * @return daysTo as String
	 */

	public String getDaysTo() {
		return daysTo;
	}

	/**
	 * Setter method for daysTo
	 * 
	 * @param daysTo
	 */

	public void setDaysTo(String daysTo) {
		this.daysTo = daysTo;
	}

	/**
	 * Getter method for bucketState
	 * 
	 * @return bucketState as String
	 */

	public String getBucketState() {
		return bucketState;
	}

	/**
	 * Setter method for bucketState
	 * 
	 * @param bucketState
	 */

	public void setBucketState(String bucketState) {
		this.bucketState = bucketState;
	}

	/**
	 * Getter method for id
	 * 
	 * @return id as Id
	 */

	public Id getId() {
		return id;
	}

	/**
	 * Setter method for id
	 * 
	 * @param id
	 */

	public void setId(Id id) {
		this.id = id;
	}

}