/*
 * @(#)BookCodePredictedBalanceTO java  05/09/06
 * 
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 * 
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */

package org.swallow.work.model;

import java.io.Serializable;

public class BookCodePredictedBalanceTO implements Serializable{
    /** The predicted balance for a book code */
    private String predictedBalance = "";

    /** The flag for determining whether the predictedbalance is negative */
    private boolean predictedBalanceNegative = false;

    /** The Book Code */
    private String bookCode = "";

    /** The Book Name */
    private String bookName = "";

    /*--Start Reference: Mail by <PERSON> on May 4th;Total To be Displayed, 5th argument added--*/
    private Double predictedBalanceBeforeFormatting = null;
    

    /*--Start: Code changed for SRS Book Locations ON 20-JULY-2007 BY <PERSON>--*/	
    /** Holds the locationName */
    private String locationName;
    /*--End: Code changed for SRS Book Locations ON 20-JULY-2007 BY <PERSON>--*/	

    /** Constructor for the class
     * @param bookCode the book code
     * @param bookName the book name
     * @param predictedBalance the predicted balance
     * @param predictedBalanceNegative the flag to check whether the predicted balance is negative
     */
    public BookCodePredictedBalanceTO(String bookCode, String bookName, String locationName,
        String predictedBalance, boolean predictedBalanceNegative,Double predictedBalanceBeforeFormatting) {
        this.bookCode = bookCode;
        this.bookName = bookName;
        /*--Start: Code changed for SRS Book Locations ON 20-JULY-2007 BY Sanjay Sharma--*/	
        this.locationName = locationName;
        /*--End: Code changed for SRS Book Locations ON 20-JULY-2007 BY Sanjay Sharma--*/	
        this.predictedBalance = predictedBalance;
        this.predictedBalanceNegative = predictedBalanceNegative;
        this.predictedBalanceBeforeFormatting=predictedBalanceBeforeFormatting;
    }
    /*--End Reference: Mail by Antony Harris on May 4th;Total To be Displayed, 5th argument added--*/

    /**
     * @return Returns the bookCode.
     */
    public String getBookCode() {
        return bookCode;
    }

    /**
     * @return Returns the bookName.
     */
    public String getBookName() {
        return bookName;
    }

    /**
     * @return Returns the predictedBalance.
     */
    public String getPredictedBalance() {
        return predictedBalance;
    }

    /**
     * @return Returns the predictedBalanceNegative.
     */
    public boolean isPredictedBalanceNegative() {
        return predictedBalanceNegative;
    }

    /*--Start Reference: Mail by Antony Harris on May 4th;Total To be Displayed, 5th argument added--*/
    /**
	 * @return Returns the predictedBalanceBeforeFormatting.
	 */
    public Double getPredictedBalanceBeforeFormatting() {
		return predictedBalanceBeforeFormatting;
	}
	/**
	 * @param predictedBalanceBeforeFormatting The predictedBalanceBeforeFormatting to set.
	 */
	public void setPredictedBalanceBeforeFormatting(
			Double predictedBalanceBeforeFormatting) {
		this.predictedBalanceBeforeFormatting = predictedBalanceBeforeFormatting;
	}
	/*--End Reference: Mail by Antony Harris on May 4th;Total To be Displayed, 5th argument added--*/

    /*--Start: Code changed for SRS Book Locations ON 20-JULY-2007 BY Sanjay Sharma--*/	
	public String getLocationName() {
		return locationName;
	}
	public void setLocationName(String locationName) {
		this.locationName = locationName;
	}
    /*--End: Code changed for SRS Book Locations ON 20-JULY-2007 BY Sanjay Sharma--*/	
}
