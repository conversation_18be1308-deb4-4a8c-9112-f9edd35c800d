/*
 * @(#)AccountMonitorNew.java 1.0
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.work.model;

import org.swallow.model.BaseObject;

/**
 * This is a POJO which for displaying objects which are present in the summary
 * display on accountmonitor screen.
 * 
 * <AUTHOR> Systems
 * @version 1.0 06 September 2006
 */
public class AccountMonitorNew extends BaseObject {

	/** Default version id */
	private static final long serialVersionUID = 1L;

	// String to hold the Currency Code
	private String currencyCode = null;
	// String to hold the Account Id
	private String accountId = null;
	// String to hold the Account Name
	private String accountName = null;
	// String to hold the Predicted balance
	private Double predictedBal = null;
	// String to hold the Unsettled balance
	private Double unsettledBal = null;
	// String to hold the unexpected balance
	private Double unexpectedBal = null;
	// String to hold the loro balance
	private Double loroBal = null;
	// String to hold the external balance
	private Double externalBal = null;
	// String to hold the Predicted balance
	private String predictedBalAsString = null;
	// String to hold the Currency Code
	private String unsettledBalAsString = null;
	// String to hold the unexpected balance
	private String unexpectedBalAsString = null;
	// String to hold the loro balance
	private String loroBalAsString = null;
	// String to hold the external balance
	private String externalBalAsString = null;
	// String to hold the Sum flag
	private String sum = null;
	// Sign Flag For Sign flag for Predict balance
	private boolean signFlagForPredictedBal = false;
	// Sign Flag For Sign flag for un settled balance
	private boolean signFlagForUnsettledBal = false;
	// Sign Flag For Unexpected Balance
	private boolean signFlagForUnexpectedBal = false;
	// Sign Flag For Loro Balance
	private boolean signFlagForLoroBal = false;
	// Sign Flag For External Balance
	private boolean signFlagForExternalBal = false;
	// String to hold the summable
	private String summable = null;
	// String to hold the Color Flag
	private String colorFlag = null;
	// String to hold the Account Class
	private String accountClass = null;
	// Loro To Predicted Flag
	private String loroToPredictedFlag = null;
	// Icon IndicatorFlag, used to display an 'L' indicator against predicted
	// bal figure
	private String iconIndicatorFlag = null;
	// String to hold the businessDay
	private String businessDay = null;
	// String to hold the To date
	private String tabDateAsString = null;
	// String to hold the tab date label
	private String tabDateLabel = null;
	/*
	 * Start:Code Modified By Chinniah on 28-May-2012 for Mantis 1933:Account
	 * Monitor: Screen hangs with "Function returned without value" if closed
	 * accounts
	 */
	// String hold Account Status
	private String accountStatus = null;
	// scenario Highlighted account
	private String scenarioHighlighted = null;
	
	private String includeLoroInPredictedIndicator = null;
	private String includePredictedInLoroIndicator = null;
	
	private String includeLoroInPredictedColor = null;
	private String includePredictedInLoroColor = null;
	

	/**
	 * Getter method for accountStatus
	 * 
	 * @return accountStatus as String
	 */

	public String getAccountStatus() {
		return accountStatus;
	}

	/**
	 * Setter method for accountStatus
	 * 
	 * @param accountStatus
	 */

	public void setAccountStatus(String accountStatus) {
		this.accountStatus = accountStatus;
	}

	/*
	 * End:Code Modified By Chinniah on 28-May-2012 for Mantis 1933:Account
	 * Monitor: Screen hangs with "Function returned without value" if closed
	 * accounts
	 */
	/**
	 * @return Returns the currencyCode.
	 */
	public String getCurrencyCode() {
		return currencyCode;
	}

	/**
	 * @param currencyCode
	 *            The currencyCode to set.
	 */
	public void setCurrencyCode(String currencyCode) {
		this.currencyCode = currencyCode;
	}

	/**
	 * @return Returns the iconIndicatorFlag.
	 */
	public String getIconIndicatorFlag() {
		return iconIndicatorFlag;
	}

	/**
	 * @param iconIndicatorFlag
	 *            The iconIndicatorFlag to set.
	 */
	public void setIconIndicatorFlag(String iconIndicatorFlag) {
		this.iconIndicatorFlag = iconIndicatorFlag;
	}

	/**
	 * @return Returns the accountClass.
	 */
	public String getAccountClass() {
		return accountClass;
	}

	/**
	 * @param accountClass
	 *            The accountClass to set.
	 */
	public void setAccountClass(String accountClass) {
		this.accountClass = accountClass;
	}

	/**
	 * @return Returns the loroToPredictedFlag.
	 */
	public String getLoroToPredictedFlag() {
		return loroToPredictedFlag;
	}

	/**
	 * @param loroToPredictedFlag
	 *            The loroToPredictedFlag to set.
	 */
	public void setLoroToPredictedFlag(String loroToPredictedFlag) {
		this.loroToPredictedFlag = loroToPredictedFlag;
	}

	/**
	 * @return Returns the accountId.
	 */
	public String getAccountId() {
		return accountId;
	}

	/**
	 * @param accountId
	 *            The accountId to set.
	 */
	public void setAccountId(String accountId) {
		this.accountId = accountId;
	}

	/**
	 * @return Returns the accountName.
	 */
	public String getAccountName() {
		return accountName;
	}

	/**
	 * @param accountName
	 *            The accountName to set.
	 */
	public void setAccountName(String accountName) {
		this.accountName = accountName;
	}

	/**
	 * @return Returns the externalBal.
	 */
	public Double getExternalBal() {
		return externalBal;
	}

	/**
	 * @param externalBal
	 *            The externalBal to set.
	 */
	public void setExternalBal(Double externalBal) {
		this.externalBal = externalBal;
	}

	/**
	 * @return Returns the loroBal.
	 */
	public Double getLoroBal() {
		return loroBal;
	}

	/**
	 * @param loroBal
	 *            The loroBal to set.
	 */
	public void setLoroBal(Double loroBal) {
		this.loroBal = loroBal;
	}

	/**
	 * @return Returns the predictedBal.
	 */
	public Double getPredictedBal() {
		return predictedBal;
	}

	/**
	 * @param predictedBal
	 *            The predictedBal to set.
	 */
	public void setPredictedBal(Double predictedBal) {
		this.predictedBal = predictedBal;
	}

	/**
	 * @return Returns the sum.
	 */
	public String getSum() {
		return sum;
	}

	/**
	 * @param sum
	 *            The sum to set.
	 */
	public void setSum(String sum) {
		this.sum = sum;
	}

	/**
	 * @return Returns the unexpectedBal.
	 */
	public Double getUnexpectedBal() {
		return unexpectedBal;
	}

	/**
	 * @param unexpectedBal
	 *            The unexpectedBal to set.
	 */
	public void setUnexpectedBal(Double unexpectedBal) {
		this.unexpectedBal = unexpectedBal;
	}

	/**
	 * @return Returns the unsettledBal.
	 */
	public Double getUnsettledBal() {
		return unsettledBal;
	}

	/**
	 * @param unsettledBal
	 *            The unsettledBal to set.
	 */
	public void setUnsettledBal(Double unsettledBal) {
		this.unsettledBal = unsettledBal;
	}

	/**
	 * @return Returns the signFlagForExternalBal.
	 */
	public boolean isSignFlagForExternalBal() {
		Double externalBal = getExternalBal();
		if (externalBal != null && externalBal.doubleValue() < 0) {
			signFlagForExternalBal = true;
		}
		return signFlagForExternalBal;
	}

	/**
	 * @param signFlagForExternalBal
	 *            The signFlagForExternalBal to set.
	 */
	public void setSignFlagForExternalBal(boolean signFlagForExternalBal) {
		this.signFlagForExternalBal = signFlagForExternalBal;
	}

	/**
	 * @return Returns the signFlagForLoroBal.
	 */
	public boolean isSignFlagForLoroBal() {
		Double loroBal = getLoroBal();
		if (loroBal != null && loroBal.doubleValue() < 0) {
			signFlagForLoroBal = true;
		}
		return signFlagForLoroBal;
	}

	/**
	 * @param signFlagForLoroBal
	 *            The signFlagForLoroBal to set.
	 */
	public void setSignFlagForLoroBal(boolean signFlagForLoroBal) {
		this.signFlagForLoroBal = signFlagForLoroBal;
	}

	/**
	 * @return Returns the signFlagForPredictedBal.
	 */
	public boolean isSignFlagForPredictedBal() {
		Double predBal = getPredictedBal();
		if (predBal != null && predBal.doubleValue() < 0) {
			signFlagForPredictedBal = true;
		}
		return signFlagForPredictedBal;
	}

	/**
	 * @param signFlagForPredictedBal
	 *            The signFlagForPredictedBal to set.
	 */
	public void setSignFlagForPredictedBal(boolean signFlagForPredictedBal) {
		this.signFlagForPredictedBal = signFlagForPredictedBal;
	}

	/**
	 * @return Returns the signFlagForUnexpectedBal.
	 */
	public boolean isSignFlagForUnexpectedBal() {
		Double unexpectedBal = getUnexpectedBal();
		if (unexpectedBal != null && unexpectedBal.doubleValue() < 0) {
			signFlagForUnexpectedBal = true;
		}
		return signFlagForUnexpectedBal;
	}

	/**
	 * @param signFlagForUnexpectedBal
	 *            The signFlagForUnexpectedBal to set.
	 */
	public void setSignFlagForUnexpectedBal(boolean signFlagForUnexpectedBal) {
		this.signFlagForUnexpectedBal = signFlagForUnexpectedBal;
	}

	/**
	 * @return Returns the signFlagForUnsettledBal.
	 */
	public boolean isSignFlagForUnsettledBal() {
		Double unsettledBal = getUnsettledBal();
		if (unsettledBal != null && unsettledBal.doubleValue() < 0) {
			signFlagForUnsettledBal = true;
		}
		return signFlagForUnsettledBal;
	}

	/**
	 * @param signFlagForUnsettledBal
	 *            The signFlagForUnsettledBal to set.
	 */
	public void setSignFlagForUnsettledBal(boolean signFlagForUnsettledBal) {
		this.signFlagForUnsettledBal = signFlagForUnsettledBal;
	}

	/**
	 * @return Returns the externalBalAsString.
	 */
	public String getExternalBalAsString() {
		return externalBalAsString;
	}

	/**
	 * @param externalBalAsString
	 *            The externalBalAsString to set.
	 */
	public void setExternalBalAsString(String externalBalAsString) {
		this.externalBalAsString = externalBalAsString;
	}

	/**
	 * @return Returns the loroBalAsString.
	 */
	public String getLoroBalAsString() {
		return loroBalAsString;
	}

	/**
	 * @param loroBalAsString
	 *            The loroBalAsString to set.
	 */
	public void setLoroBalAsString(String loroBalAsString) {
		this.loroBalAsString = loroBalAsString;
	}

	/**
	 * @return Returns the predictedBalAsString.
	 */
	public String getPredictedBalAsString() {
		return predictedBalAsString;
	}

	/**
	 * @param predictedBalAsString
	 *            The predictedBalAsString to set.
	 */
	public void setPredictedBalAsString(String predictedBalAsString) {
		this.predictedBalAsString = predictedBalAsString;
	}

	/**
	 * @return Returns the unexpectedBalAsString.
	 */
	public String getUnexpectedBalAsString() {
		return unexpectedBalAsString;
	}

	/**
	 * @param unexpectedBalAsString
	 *            The unexpectedBalAsString to set.
	 */
	public void setUnexpectedBalAsString(String unexpectedBalAsString) {
		this.unexpectedBalAsString = unexpectedBalAsString;
	}

	/**
	 * @return Returns the unsettledBalAsString.
	 */
	public String getUnsettledBalAsString() {
		return unsettledBalAsString;
	}

	/**
	 * @param unsettledBalAsString
	 *            The unsettledBalAsString to set.
	 */
	public void setUnsettledBalAsString(String unsettledBalAsString) {
		this.unsettledBalAsString = unsettledBalAsString;
	}

	/**
	 * @return the tabDateAsString
	 */
	public String getTabDateAsString() {
		return tabDateAsString;
	}

	/**
	 * @param tabDateAsString
	 *            the tabDateAsString to set
	 */
	public void setTabDateAsString(String tabDateAsString) {
		this.tabDateAsString = tabDateAsString;
	}

	/**
	 * @return the businessDay
	 */
	public String getBusinessDay() {
		return businessDay;
	}

	/**
	 * @param businessDay
	 *            the businessDay to set
	 */
	public void setBusinessDay(String businessDay) {
		this.businessDay = businessDay;
	}

	/**
	 * @return the tabDateLabel
	 */
	public String getTabDateLabel() {
		return tabDateLabel;
	}

	/**
	 * @param tabDateLabel
	 *            the tabDateLabel to set
	 */
	public void setTabDateLabel(String tabDateLabel) {
		this.tabDateLabel = tabDateLabel;
	}

	/**
	 * @return the colorFlag
	 */
	public String getColorFlag() {
		return colorFlag;
	}

	/**
	 * @param colorFlag
	 *            the colorFlag to set
	 */
	public void setColorFlag(String colorFlag) {
		this.colorFlag = colorFlag;
	}

	/**
	 * @return the summable
	 */
	public String getSummable() {
		return summable;
	}

	/**
	 * @param summable
	 *            the summable to set
	 */
	public void setSummable(String summable) {
		this.summable = summable;
	}
	
	
	public String getScenarioHighlighted() {
		return scenarioHighlighted;
	}

	public void setScenarioHighlighted(String scenarioHighlighted) {
		this.scenarioHighlighted = scenarioHighlighted;
	}

	public String getIncludeLoroInPredictedIndicator() {
		return includeLoroInPredictedIndicator;
	}

	public void setIncludeLoroInPredictedIndicator(String includeLoroInPredictedIndicator) {
		this.includeLoroInPredictedIndicator = includeLoroInPredictedIndicator;
	}

	public String getIncludePredictedInLoroIndicator() {
		return includePredictedInLoroIndicator;
	}

	public void setIncludePredictedInLoroIndicator(String includePredictedInLoroIndicator) {
		this.includePredictedInLoroIndicator = includePredictedInLoroIndicator;
	}

	public String getIncludeLoroInPredictedColor() {
		return includeLoroInPredictedColor;
	}

	public void setIncludeLoroInPredictedColor(String includeLoroInPredictedColor) {
		this.includeLoroInPredictedColor = includeLoroInPredictedColor;
	}

	public String getIncludePredictedInLoroColor() {
		return includePredictedInLoroColor;
	}

	public void setIncludePredictedInLoroColor(String includePredictedInLoroColor) {
		this.includePredictedInLoroColor = includePredictedInLoroColor;
	}
}
