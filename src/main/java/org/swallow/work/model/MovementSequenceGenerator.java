/*
 * Created on Jan 14, 2006
 *
 * TODO To change the template for this generated file go to
 * Window - Preferences - Java - Code Style - Code Templates
 */
package org.swallow.work.model;

import java.io.Serializable;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.Properties;

import org.hibernate.HibernateException;
import org.hibernate.JDBCException;
import org.hibernate.MappingException;
import org.hibernate.dialect.Dialect;
import org.hibernate.engine.spi.SessionImplementor;
import org.hibernate.engine.spi.SharedSessionContractImplementor;
import org.hibernate.exception.spi.SQLExceptionConverter;
import org.hibernate.id.Configurable;
import org.hibernate.id.IdentifierGenerator;
import org.hibernate.type.LongType;
import org.hibernate.type.StringType;
import org.hibernate.type.Type;
import org.hibernate.usertype.CompositeUserType;
import org.swallow.exception.SwtException;
import org.swallow.util.Batcher;
import org.swallow.util.JDBCExceptionReporter;
import org.swallow.util.SequenceFactory;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;


/**
 * <AUTHOR>
 *
 * TODO To change the template for this generated type comment go to
 * Window - Preferences - Java - Code Style - Code Templates
 */
public class MovementSequenceGenerator implements IdentifierGenerator, Configurable, Serializable{
	
	private final Log log = LogFactory.getLog(MatchSequenceGenerator.class); // Log object 	
	
	/* (non-Javadoc)
     * @see net.sf.hibernate.id.Configurable#configure(net.sf.hibernate.type.Type, java.util.Properties, net.sf.hibernate.dialect.Dialect)
     */
    public void configure(Type type, Properties params, Dialect dialect)
    throws MappingException	{
    	log.debug("No Configuration required for generating Movement Sequence number");
    } 
    /**
     * Create a new sequence number for movement object.
     * @param SessionImplementor session
     * @param Object obj
     */
    public Serializable generate(SessionImplementor session, Object obj)
    throws SQLException, HibernateException	{
    	Movement  movementObj = null;
    	Movement.Id  movementIdObj = null;
    	ResultSet rsSelect = null;
    	PreparedStatement stSelect = null;
    	long sequenceValue = -1 ;
    	try{
    		if(obj == null || !obj.getClass().equals(Movement.class)){
    			throw new HibernateException("Model object is null or object type is not org.swallow.work.model.Movement  ");
    		}else{
    			movementObj = (Movement)obj;
    			movementIdObj = movementObj.getId();
//    			String _sequenceQuery = "select PK_UTILITY.FN_GET_SEQUENCE_NUMBER('movement') from dual";
//    			stSelect = Batcher.of(session).prepareStatement(_sequenceQuery);
//    			rsSelect = stSelect.executeQuery();
//    			if(rsSelect.next())
//    				sequenceValue = rsSelect.getLong(1);
//    			if(sequenceValue == -1)
//    				throw new HibernateException("Could not fecth the sequence number");
    			sequenceValue =  SequenceFactory.getSequenceFromDbAsLong("p_movement_sequence");
    			movementIdObj.setMovementId(new Long(sequenceValue));
    		}
    	}catch (SwtException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}finally{
    		if(rsSelect != null && stSelect != null)
    			Batcher.of(session).closeQueryStatement(stSelect,rsSelect);
    	}
    	return movementIdObj;
    }
    
	@Override
	public Serializable generate(SharedSessionContractImplementor session, Object object) throws HibernateException {
		try {
			return generate((SessionImplementor)session, object);
		} catch (Exception e) {
			throw new HibernateException(e);
		}
	}
}
		
