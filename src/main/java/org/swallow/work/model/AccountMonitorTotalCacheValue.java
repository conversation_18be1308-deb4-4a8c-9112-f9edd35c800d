/*
 * @(#)AccountMonitorTotalCacheValue.java 1.0
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.work.model;

import java.io.Serializable;

/**
 * This is a POJO for storing value objects in the cache corresponding to
 * different key objects for totals.
 * 
 * <AUTHOR> Systems
 * @version 1.0 08 September 2006
 */
public class AccountMonitorTotalCacheValue implements Serializable{

	// Total - Today balance
	private Double todayBalTotal = null;
	// Total - Predicted balance
	private Double predictedBalTotal = null;
	// Total - Unexpected balance
	private Double unexpectedBalTotal = null;
	// Total - Unsettled balance
	private Double unsettledBalTotal = null;
	// Total - Loro balance
	private Double loroBalTotal = null;
	// Total - External balance
	private Double externalBalTotal = null;
	// Total no of unexpected movement
	private Double openUnexpectedBalTotal = null;
	// Start of day balance
	private Double accumulatedSODBal = null;
	// Total in String - Predicted balance
	private String predictedBalTotalAsString = null;
	// Total in String - Unsettled balance
	private String unsettledBalTotalAsString = null;
	// Total in String - Unexpected balance
	private String unexpectedBalTotalAsString = null;
	// Total in String - Loro balance
	private String loroBalTotalAsString = null;
	// Total in String - External Balance
	private String externalBalTotalAsString = null;
	// String value of no of unexpected movements
	private String openUnexpectedBalTotalAsString = null;
	// String value of start of day balance
	private String accumulatedSODBalAsString = null;
	// scenario Highlighted account
	private String scenarioHighlighted = null;

	/**
	 * @return Returns the accumulatedSODBal.
	 */
	public Double getAccumulatedSODBal() {
		return accumulatedSODBal;
	}

	/**
	 * @param accumulatedSODBal
	 *            The accumulatedSODBal to set.
	 */
	public void setAccumulatedSODBal(Double accumulatedSODBal) {
		this.accumulatedSODBal = accumulatedSODBal;
	}

	/**
	 * @return Returns the accumulatedSODBalAsString.
	 */
	public String getAccumulatedSODBalAsString() {
		return accumulatedSODBalAsString;
	}

	/**
	 * @param accumulatedSODBalAsString
	 *            The accumulatedSODBalAsString to set.
	 */
	public void setAccumulatedSODBalAsString(String accumulatedSODBalAsString) {
		this.accumulatedSODBalAsString = accumulatedSODBalAsString;
	}

	/**
	 * @return Returns the externalBalTotal.
	 */
	public Double getExternalBalTotal() {
		return externalBalTotal;
	}

	/**
	 * @param externalBalTotal
	 *            The externalBalTotal to set.
	 */
	public void setExternalBalTotal(Double externalBalTotal) {
		this.externalBalTotal = externalBalTotal;
	}

	/**
	 * @return Returns the loroBalTotal.
	 */
	public Double getLoroBalTotal() {
		return loroBalTotal;
	}

	/**
	 * @param loroBalTotal
	 *            The loroBalTotal to set.
	 */
	public void setLoroBalTotal(Double loroBalTotal) {
		this.loroBalTotal = loroBalTotal;
	}

	/**
	 * @return Returns the predictedBalTotal.
	 */
	public Double getPredictedBalTotal() {
		return predictedBalTotal;
	}

	/**
	 * @param predictedBalTotal
	 *            The predictedBalTotal to set.
	 */
	public void setPredictedBalTotal(Double predictedBalTotal) {
		this.predictedBalTotal = predictedBalTotal;
	}

	/**
	 * @return Returns the todayBalTotal.
	 */
	public Double getTodayBalTotal() {
		return todayBalTotal;
	}

	/**
	 * @param todayBalTotal
	 *            The todayBalTotal to set.
	 */
	public void setTodayBalTotal(Double todayBalTotal) {
		this.todayBalTotal = todayBalTotal;
	}

	/**
	 * @return Returns the unexpectedBalTotal.
	 */
	public Double getUnexpectedBalTotal() {
		return unexpectedBalTotal;
	}

	/**
	 * @param unexpectedBalTotal
	 *            The unexpectedBalTotal to set.
	 */
	public void setUnexpectedBalTotal(Double unexpectedBalTotal) {
		this.unexpectedBalTotal = unexpectedBalTotal;
	}

	/**
	 * @return Returns the unsettledBalTotal.
	 */
	public Double getUnsettledBalTotal() {
		return unsettledBalTotal;
	}

	/**
	 * @param unsettledBalTotal
	 *            The unsettledBalTotal to set.
	 */
	public void setUnsettledBalTotal(Double unsettledBalTotal) {
		this.unsettledBalTotal = unsettledBalTotal;
	}

	/**
	 * @return Returns the externalBalTotalAsString.
	 */
	public String getExternalBalTotalAsString() {
		return externalBalTotalAsString;
	}

	/**
	 * @param externalBalTotalAsString
	 *            The externalBalTotalAsString to set.
	 */
	public void setExternalBalTotalAsString(String externalBalTotalAsString) {
		this.externalBalTotalAsString = externalBalTotalAsString;
	}

	/**
	 * @return Returns the loroBalTotalAsString.
	 */
	public String getLoroBalTotalAsString() {
		return loroBalTotalAsString;
	}

	/**
	 * @param loroBalTotalAsString
	 *            The loroBalTotalAsString to set.
	 */
	public void setLoroBalTotalAsString(String loroBalTotalAsString) {
		this.loroBalTotalAsString = loroBalTotalAsString;
	}

	/**
	 * @return Returns the predictedBalTotalAsString.
	 */
	public String getPredictedBalTotalAsString() {
		return predictedBalTotalAsString;
	}

	/**
	 * @param predictedBalTotalAsString
	 *            The predictedBalTotalAsString to set.
	 */
	public void setPredictedBalTotalAsString(String predictedBalTotalAsString) {
		this.predictedBalTotalAsString = predictedBalTotalAsString;
	}

	/**
	 * @return Returns the unexpectedBalTotalAsString.
	 */
	public String getUnexpectedBalTotalAsString() {
		return unexpectedBalTotalAsString;
	}

	/**
	 * @param unexpectedBalTotalAsString
	 *            The unexpectedBalTotalAsString to set.
	 */
	public void setUnexpectedBalTotalAsString(String unexpectedBalTotalAsString) {
		this.unexpectedBalTotalAsString = unexpectedBalTotalAsString;
	}

	/**
	 * @return Returns the unsettledBalTotalAsString.
	 */
	public String getUnsettledBalTotalAsString() {
		return unsettledBalTotalAsString;
	}

	/**
	 * @param unsettledBalTotalAsString
	 *            The unsettledBalTotalAsString to set.
	 */
	public void setUnsettledBalTotalAsString(String unsettledBalTotalAsString) {
		this.unsettledBalTotalAsString = unsettledBalTotalAsString;
	}

	/**
	 * @return Returns the openUnexpectedBalTotal.
	 */
	public Double getOpenUnexpectedBalTotal() {
		return openUnexpectedBalTotal;
	}

	/**
	 * @param openUnexpectedBalTotal
	 *            The openUnexpectedBalTotal to set.
	 */
	public void setOpenUnexpectedBalTotal(Double openUnexpectedBalTotal) {
		this.openUnexpectedBalTotal = openUnexpectedBalTotal;
	}

	/**
	 * @return Returns the openUnexpectedBalTotalAsString.
	 */
	public String getOpenUnexpectedBalTotalAsString() {
		return openUnexpectedBalTotalAsString;
	}

	/**
	 * @param openUnexpectedBalTotalAsString
	 *            The openUnexpectedBalTotalAsString to set.
	 */
	public void setOpenUnexpectedBalTotalAsString(
			String openUnexpectedBalTotalAsString) {
		this.openUnexpectedBalTotalAsString = openUnexpectedBalTotalAsString;
	}

	public String getScenarioHighlighted() {
		return scenarioHighlighted;
	}

	public void setScenarioHighlighted(String scenarioHighlighted) {
		this.scenarioHighlighted = scenarioHighlighted;
	}
}