/*
 * @(#)SweepAmount.java  25/01/06
 * 
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 * 
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */

package org.swallow.work.model;
import java.util.Date;
import org.swallow.model.BaseObject;
public class SweepAmount extends BaseObject {
	
	private String entityId;
	private Double origSweepAmt;
	private String origSweepAmtAsString;
	private Double subSweepAmt;
	private String subSweepAmtAsString;
	private Double authSweepAmt;
	private String authSweepAmtAsString;
	private Date valueDate;
	private String valueDateAsString;
	private String additionalReference;
	
	private boolean scheduleSweepExist = false;
	private String scheduleSweepDetailsAsString = "";
	private boolean scheduleSweepApplyed = false;
	private String selectedScheduleSweepOption = "default";
	private String selectedList = "";
	
	
	
	/**
	 * @return Returns the authSweepAmt.
	 */
	public Double getAuthSweepAmt() {
		return authSweepAmt;
	}
	/**
	 * @param authSweepAmt The authSweepAmt to set.
	 */
	public void setAuthSweepAmt(Double authSweepAmt) {
		this.authSweepAmt = authSweepAmt;
	}
	/**
	 * @return Returns the origSweepAmt.
	 */
	public Double getOrigSweepAmt() {
		return origSweepAmt;
	}
	/**
	 * @param origSweepAmt The origSweepAmt to set.
	 */
	public void setOrigSweepAmt(Double origSweepAmt) {
		this.origSweepAmt = origSweepAmt;
	}
	/**
	 * @return Returns the origSweepAmtAsString.
	 */
	public String getOrigSweepAmtAsString() {
		return origSweepAmtAsString;
	}
	/**
	 * @param origSweepAmtAsString The origSweepAmtAsString to set.
	 */
	public void setOrigSweepAmtAsString(String origSweepAmtAsString) {
		this.origSweepAmtAsString = origSweepAmtAsString;
	}
	/**
	 * @return Returns the subSweepAmt.
	 */
	public Double getSubSweepAmt() {
		return subSweepAmt;
	}
	/**
	 * @param subSweepAmt The subSweepAmt to set.
	 */
	public void setSubSweepAmt(Double subSweepAmt) {
		this.subSweepAmt = subSweepAmt;
	}
	/**
	 * @return Returns the valueDate.
	 */
	public Date getValueDate() {
		return valueDate;
	}
	/**
	 * @param valueDate The valueDate to set.
	 */
	public void setValueDate(Date valueDate) {
		this.valueDate = valueDate;
	}
	/**
	 * @return Returns the valueDateAsString.
	 */
	public String getValueDateAsString() {
		return valueDateAsString;
	}
	/**
	 * @param valueDateAsString The valueDateAsString to set.
	 */
	public void setValueDateAsString(String valueDateAsString) {
		this.valueDateAsString = valueDateAsString;
	}
	/**
	 * @return Returns the authSweepAmtAsString.
	 */
	public String getAuthSweepAmtAsString() {
		return authSweepAmtAsString;
	}
	/**
	 * @param authSweepAmtAsString The authSweepAmtAsString to set.
	 */
	public void setAuthSweepAmtAsString(String authSweepAmtAsString) {
		this.authSweepAmtAsString = authSweepAmtAsString;
	}
	/**
	 * @return Returns the subSweepAmtAsString.
	 */
	public String getSubSweepAmtAsString() {
		return subSweepAmtAsString;
	}
	/**
	 * @param subSweepAmtAsString The subSweepAmtAsString to set.
	 */
	public void setSubSweepAmtAsString(String subSweepAmtAsString) {
		this.subSweepAmtAsString = subSweepAmtAsString;
	}
	/**
	 * @return Returns the entityId.
	 */
	public String getEntityId() {
		return entityId;
	}
	/**
	 * @param entityId The entityId to set.
	 */
	public void setEntityId(String entityId) {
		this.entityId = entityId;
	}
	public String getAdditionalReference() {
		return additionalReference;
	}
	public void setAdditionalReference(String additionalReference) {
		this.additionalReference = additionalReference;
	}
	
	public boolean isScheduleSweepExist() {
		return scheduleSweepExist;
	}
	public void setScheduleSweepExist(boolean scheduleSweepExist) {
		this.scheduleSweepExist = scheduleSweepExist;
	}
	public String getScheduleSweepDetailsAsString() {
		return scheduleSweepDetailsAsString;
	}
	public void setScheduleSweepDetailsAsString(String scheduleSweepDetailsAsString) {
		this.scheduleSweepDetailsAsString = scheduleSweepDetailsAsString;
	}
	public boolean isScheduleSweepApplyed() {
		return scheduleSweepApplyed;
	}
	public void setScheduleSweepApplyed(boolean scheduleSweepApplyed) {
		this.scheduleSweepApplyed = scheduleSweepApplyed;
	}
	public String getSelectedScheduleSweepOption() {
		return selectedScheduleSweepOption;
	}
	public void setSelectedScheduleSweepOption(String selectedScheduleSweepOption) {
		this.selectedScheduleSweepOption = selectedScheduleSweepOption;
	}
	public String getSelectedList() {
		return selectedList;
	}
	public void setSelectedList(String selectedList) {
		this.selectedList = selectedList;
	}
}
