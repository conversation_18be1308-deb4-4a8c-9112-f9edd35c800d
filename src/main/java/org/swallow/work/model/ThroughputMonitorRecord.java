package org.swallow.work.model;

import org.swallow.model.AuditComponent;
import org.swallow.model.BaseObject;

public class ThroughputMonitorRecord extends BaseObject implements AuditComponent {
	private static final long serialVersionUID = 1L;
	private Id id = new Id();

	private String alerting;
	private Double forecastedInflow;
	private Double forecastedOutflows;
	private Double actualsInflow;
	private Double actualsOutflows;
	private Double unsetteledOutflows;
	private String threshold1Time;
	private String threshold2Time;
	private Double threshold1Percent;
	private Double threshold2Percent;
	private Integer threshold1State;
	private Integer threshold2State;
	private String threshold1Color;
	private String threshold2Color;
	private Double threshold1ActualPercent;
	private Double threshold2ActualPercent;
	
	
	private String chartsTimeData = "";
	private String chartsPercentData = "";

	private String threshold1AsString;
	private String threshold2AsString;

	private Double current;
	private String currentAsString;
	private String currentTime;

	public Id getId() {
		return id;
	}

	public void setId(Id id) {
		this.id = id;
	}

	
	public String getAlerting() {
		return alerting;
	}

	public void setAlerting(String alerting) {
		this.alerting = alerting;
	}

	public Double getForecastedInflow() {
		return forecastedInflow;
	}

	public void setForecastedInflow(Double forecastedInflow) {
		this.forecastedInflow = forecastedInflow;
	}

	public Double getForecastedOutflows() {
		return forecastedOutflows;
	}

	public void setForecastedOutflows(Double forecastedOutflows) {
		this.forecastedOutflows = forecastedOutflows;
	}

	public Double getActualsInflow() {
		return actualsInflow;
	}

	public void setActualsInflow(Double actualsInflow) {
		this.actualsInflow = actualsInflow;
	}

	public Double getActualsOutflows() {
		return actualsOutflows;
	}

	public void setActualsOutflows(Double actualsOutflows) {
		this.actualsOutflows = actualsOutflows;
	}

	public Double getUnsetteledOutflows() {
		return unsetteledOutflows;
	}

	public void setUnsetteledOutflows(Double unsetteledOutflows) {
		this.unsetteledOutflows = unsetteledOutflows;
	}

	public String getThreshold1Time() {
		return threshold1Time;
	}

	public void setThreshold1Time(String threshold1Time) {
		this.threshold1Time = threshold1Time;
	}

	public String getThreshold2Time() {
		return threshold2Time;
	}

	public void setThreshold2Time(String threshold2Time) {
		this.threshold2Time = threshold2Time;
	}

	public Double getThreshold1Percent() {
		return threshold1Percent;
	}

	public void setThreshold1Percent(Double threshold1Percent) {
		this.threshold1Percent = threshold1Percent;
	}

	public Double getThreshold2Percent() {
		return threshold2Percent;
	}

	public void setThreshold2Percent(Double threshold2Percent) {
		this.threshold2Percent = threshold2Percent;
	}

	public Double getThreshold1ActualPercent() {
		return threshold1ActualPercent;
	}

	public void setThreshold1ActualPercent(Double threshold1ActualPercent) {
		this.threshold1ActualPercent = threshold1ActualPercent;
	}

	public Double getThreshold2ActualPercent() {
		return threshold2ActualPercent;
	}

	public void setThreshold2ActualPercent(Double threshold2ActualPercent) {
		this.threshold2ActualPercent = threshold2ActualPercent;
	}

	public String getThreshold1AsString() {
		return threshold1AsString;
	}

	public void setThreshold1AsString(String threshold1AsString) {
		this.threshold1AsString = threshold1AsString;
	}

	public String getThreshold2AsString() {
		return threshold2AsString;
	}

	public void setThreshold2AsString(String threshold2AsString) {
		this.threshold2AsString = threshold2AsString;
	}

	public String getCurrentAsString() {
		return currentAsString;
	}

	public void setCurrentAsString(String currentAsString) {
		this.currentAsString = currentAsString;
	}

	public Double getCurrent() {
		return current;
	}

	public void setCurrent(Double current) {
		this.current = current;
	}


	public Integer getThreshold1State() {
		return threshold1State;
	}

	public void setThreshold1State(Integer threshold1State) {
		this.threshold1State = threshold1State;
	}

	public Integer getThreshold2State() {
		return threshold2State;
	}

	public void setThreshold2State(Integer threshold2State) {
		this.threshold2State = threshold2State;
	}

	public String getThreshold1Color() {
		return threshold1Color;
	}

	public void setThreshold1Color(String threshold1Color) {
		this.threshold1Color = threshold1Color;
	}

	public String getThreshold2Color() {
		return threshold2Color;
	}

	public void setThreshold2Color(String threshold2Color) {
		this.threshold2Color = threshold2Color;
	}

	public String getCurrentTime() {
		return currentTime;
	}

	public void setCurrentTime(String currentTime) {
		this.currentTime = currentTime;
	}

	public String getChartsTimeData() {
		return chartsTimeData;
	}

	public void setChartsTimeData(String chartsTimeData) {
		this.chartsTimeData = chartsTimeData;
	}

	public String getChartsPercentData() {
		return chartsPercentData;
	}

	public void setChartsPercentData(String chartsPercentData) {
		this.chartsPercentData = chartsPercentData;
	}

	public static class Id extends BaseObject {
		private static final long serialVersionUID = 1L;
		private String entityId;
		private String currencyCode;

		public String getEntityId() {
			return entityId;
		}

		public void setEntityId(String entityId) {
			this.entityId = entityId;
		}

		public String getCurrencyCode() {
			return currencyCode;
		}

		public void setCurrencyCode(String currencyCode) {
			this.currencyCode = currencyCode;
		}

		public String getIlmGroup() {
			return ilmGroup;
		}

		public void setIlmGroup(String ilmGroup) {
			this.ilmGroup = ilmGroup;
		}

		private String ilmGroup;

		public Id() {
		}

	}
}
