<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE hibernate-mapping PUBLIC
        "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
        "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">

<hibernate-mapping>
    <class name="org.swallow.work.model.MsdAdditionalColumns" table="P_MSD_ADDITIONAL_COLUMNS" >
    
	    <id name="sequenceKey" column="SEQUENCE_KEY">
		   <generator class="assigned">
		   </generator>
	 	 </id>
	 	<property name="hostId" column="HOST_ID" not-null="false"/>
	 	<property name="userId" column="USER_ID" not-null="false"/>
		<property name="profileId" column="PROFILE_ID" not-null="false"/>
		<property name="label" column="MSD_LABEL" not-null="false"/>
		<property name="refCol" column="REF_COLUMN" not-null="false"/>
		
		<many-to-one name="msdDisplayColumns" update="false" insert="false" lazy="false"
			cascade="none" class="org.swallow.maintenance.model.MsdDisplayColumns"
			not-null="true" outer-join="true"
			foreign-key="FK_P_MSD_ADD_REF_COL">
			<column name="REF_COLUMN" />
		</many-to-one>	
		
	</class>
</hibernate-mapping>
