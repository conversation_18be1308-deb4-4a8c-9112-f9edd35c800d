/*
 * @(#)SweepLimit.java  03/01/06
 * 
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 * 
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */

package org.swallow.work.model;

import java.util.Date;


import org.swallow.model.BaseObject;



public class SweepLimit extends BaseObject {

	private Id id = new Id();
	private Double sweepLimit;
	private Date updateDate ;
	private String updateUser;
    	
	
		public static class Id extends BaseObject{
		
		private String currencyCode ;
		private String roleId;
		
		
		public Id() {
		}
		
		public Id(String currencyCode ,String roleId ) {
			this.currencyCode = currencyCode;
			this.roleId = roleId;
			
		}
	
		/**
		 * @return Returns the currencyCode.
		 */
		public String getCurrencyCode() {
			return currencyCode;
		}
		/**
		 * @param currencyCode The currencyCode to set.
		 */
		public void setCurrencyCode(String currencyCode) {
			this.currencyCode = currencyCode;
		}
		/**
		 * @return Returns the roleId.
		 */
		public String getRoleId() {
			return roleId;
		}
		/**
		 * @param roleId The roleId to set.
		 */
		public void setRoleId(String roleId) {
			this.roleId = roleId;
		}
	}
	
	/**
	 * @return Returns the id.
	 */
	public Id getId() {
		return id;
	}
	/**
	 * @param id The id to set.
	 */
	public void setId(Id id) {
		this.id = id;
	}
	/**
	 * @return Returns the updateUser.
	 */
	public String getUpdateUser() {
		return updateUser;
	}
	/**
	 * @param updateUser The updateUser to set.
	 */
	public void setUpdateUser(String updateUser) {
		this.updateUser = updateUser;
		}
	/**
	 * @return Returns the updateDate.
	 */
	public Date getUpdateDate() {
		return updateDate;
	}
	/**
	 * @return Returns the sweepLimit.
	 */
	public Double getSweepLimit() {
		return sweepLimit;
	}
	/**
	 * @param sweepLimit The sweepLimit to set.
	 */
	public void setSweepLimit(Double sweepLimit) {
		this.sweepLimit = sweepLimit;
	}
	/**
	 * @param updateDate The updateDate to set.
	 */
	public void setUpdateDate(Date updateDate) {
		this.updateDate = updateDate;
	}
}
