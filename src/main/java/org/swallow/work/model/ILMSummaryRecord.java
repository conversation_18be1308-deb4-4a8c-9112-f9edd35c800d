package org.swallow.work.model;

import java.io.Serializable;
import java.util.HashMap;

public class ILMSummaryRecord implements Serializable{

	private String hostId = null;
	private String currencyCode = null;
	private String entityId = null;
	private String ilmGroupId = null;
	private String ilmGlobalAccountGroupId = null;
	private String ilmAccountGroupName = null;
	private String ilmAccountGroupDesc = null;

	private String accountId = null;
	private String accountName = null;
	private String cutOff = null;
	private boolean cutOffExceeded = false;
	private String predictedBalance = "0";
	private String predictBalCol = "";
	private String externalBal = "0";
	private String externalBalCol = "";
	private String turnover = "0";
	private String available = "0";
	private String unsettledBalance = "0";
	private String unexpectedBalance = "0";
	private String preadvices = "0";
	private String dpctThru = "";
	private String dPctThruColor = "";
	private String pctConfCr = "";
	
	private String actualSod = "0";
	private String sodBalCol = "";
	private String minbal = "0";
	private String minBalTime = "";
	private String minBalColor = "";
	private String sum = "";
	private String currencyMultiplierLabel  = "";
	
	private String rowColor = "";
	private Integer order = 99;
	private String scenarioHighlighted = null;
	
	
	private HashMap<String, ILMSummaryRecord> subILMSummaryList = new HashMap<String, ILMSummaryRecord>();

	public String getHostId() {
		return hostId;
	}

	public void setHostId(String hostId) {
		this.hostId = hostId;
	}

	public String getCurrencyCode() {
		return currencyCode;
	}

	public void setCurrencyCode(String currencyCode) {
		this.currencyCode = currencyCode;
	}

	public String getEntityId() {
		return entityId;
	}

	public void setEntityId(String entityId) {
		this.entityId = entityId;
	}

	public String getIlmGroupId() {
		return ilmGroupId;
	}

	public void setIlmGroupId(String ilmGroupId) {
		this.ilmGroupId = ilmGroupId;
	}

	public String getAccountId() {
		return accountId;
	}

	public void setAccountId(String accountId) {
		this.accountId = accountId;
	}

	
	public String getAccountName() {
		return accountName;
	}

	public void setAccountName(String accountName) {
		this.accountName = accountName;
	}

	public String getCutOff() {
		return cutOff;
	}

	public void setCutOff(String cutOff) {
		this.cutOff = cutOff;
	}

	public String getPredictedBalance() {
		return predictedBalance;
	}

	public void setPredictedBalance(String predictedBalance) {
		this.predictedBalance = predictedBalance;
	}

	public String getPredictBalCol() {
		return predictBalCol;
	}

	public void setPredictBalCol(String predictBalCol) {
		this.predictBalCol = predictBalCol;
	}

	public String getExternalBal() {
		return externalBal;
	}

	public void setExternalBal(String externalBal) {
		this.externalBal = externalBal;
	}

	public String getExternalBalCol() {
		return externalBalCol;
	}

	public void setExternalBalCol(String externalBalCol) {
		this.externalBalCol = externalBalCol;
	}

	public String getTurnover() {
		return turnover;
	}

	public void setTurnover(String turnover) {
		this.turnover = turnover;
	}

	public String getAvailable() {
		return available;
	}

	public void setAvailable(String available) {
		this.available = available;
	}

	public String getUnsettledBalance() {
		return unsettledBalance;
	}

	public void setUnsettledBalance(String unsettledBalance) {
		this.unsettledBalance = unsettledBalance;
	}

	public String getUnexpectedBalance() {
		return unexpectedBalance;
	}

	public void setUnexpectedBalance(String unexpectedBalance) {
		this.unexpectedBalance = unexpectedBalance;
	}

	public String getPreadvices() {
		return preadvices;
	}

	public void setPreadvices(String preadvices) {
		this.preadvices = preadvices;
	}

	public String getDpctThru() {
		return dpctThru;
	}

	public void setDpctThru(String dpctThru) {
		this.dpctThru = dpctThru;
	}

	public String getdPctThruColor() {
		return dPctThruColor;
	}

	public void setdPctThruColor(String dPctThruColor) {
		this.dPctThruColor = dPctThruColor;
	}

	public String getActualSod() {
		return actualSod;
	}

	public void setActualSod(String actualSod) {
		this.actualSod = actualSod;
	}

	public String getSodBalCol() {
		return sodBalCol;
	}

	public void setSodBalCol(String sodBalCol) {
		this.sodBalCol = sodBalCol;
	}

	public String getMinbal() {
		return minbal;
	}

	public void setMinbal(String minbal) {
		this.minbal = minbal;
	}

	public String getMinBalTime() {
		return minBalTime;
	}

	public void setMinBalTime(String minBalTime) {
		this.minBalTime = minBalTime;
	}

	public Integer getOrder() {
		return order;
	}

	public void setOrder(Integer order) {
		this.order = order;
	}

	public HashMap<String, ILMSummaryRecord> getSubILMSummaryList() {
		return subILMSummaryList;
	}

	public void setSubILMSummaryList(HashMap<String, ILMSummaryRecord> subILMSummaryList) {
		this.subILMSummaryList = subILMSummaryList;
	}

	public boolean isCutOffExceeded() {
		return cutOffExceeded;
	}

	public void setCutOffExceeded(boolean cutOffExceeded) {
		this.cutOffExceeded = cutOffExceeded;
	}

	public String getIlmGlobalAccountGroupId() {
		return ilmGlobalAccountGroupId;
	}

	public void setIlmGlobalAccountGroupId(String ilmGlobalAccountGroupId) {
		this.ilmGlobalAccountGroupId = ilmGlobalAccountGroupId;
	}

	public String getSum() {
		return sum;
	}

	public void setSum(String sum) {
		this.sum = sum;
	}

	public String getMinBalColor() {
		return minBalColor;
	}

	public void setMinBalColor(String minBalColor) {
		this.minBalColor = minBalColor;
	}

	public String getRowColor() {
		return rowColor;
	}

	public void setRowColor(String rowColor) {
		this.rowColor = rowColor;
	}

	public String getCurrencyMultiplierLabel() {
		return currencyMultiplierLabel;
	}

	public void setCurrencyMultiplierLabel(String currencyMultiplierLabel) {
		this.currencyMultiplierLabel = currencyMultiplierLabel;
	}

	public String getIlmAccountGroupName() {
		return ilmAccountGroupName;
	}

	public void setIlmAccountGroupName(String ilmAccountGroupName) {
		this.ilmAccountGroupName = ilmAccountGroupName;
	}

	public String getIlmAccountGroupDesc() {
		return ilmAccountGroupDesc;
	}

	public void setIlmAccountGroupDesc(String ilmAccountGroupDesc) {
		this.ilmAccountGroupDesc = ilmAccountGroupDesc;
	}
	
	public String getScenarioHighlighted() {
		return scenarioHighlighted;
	}

	public void setScenarioHighlighted(String scenarioHighlighted) {
		this.scenarioHighlighted = scenarioHighlighted;
	}

	public String getPctConfCr() {
		return pctConfCr;
	}

	public void setPctConfCr(String pctConfCr) {
		this.pctConfCr = pctConfCr;
	}
	
	
}	
