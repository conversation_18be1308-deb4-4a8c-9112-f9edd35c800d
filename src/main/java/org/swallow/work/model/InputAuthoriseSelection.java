/*
 * Created on Aug 22, 2006
 *
 * TODO To change the template for this generated file go to
 * Window - Preferences - Java - Code Style - Code Templates
 */
package org.swallow.work.model;

import java.util.HashMap;
import java.util.Map;

import org.swallow.model.BaseObject;

/**
 * <AUTHOR>
 *
 * TODO To change the template for this generated type comment go to
 * Window - Preferences - Java - Code Style - Code Templates
 */
public class InputAuthoriseSelection extends BaseObject{
	
	/*
	 * This is used to hold no of authorised movements
	 */
	String authorise;
	/*
	 * This is used to hold entityId for movements
	 */
	String entityId;
	
	/*
	 * This is used to hold currencyId for movements
	 */
	String currencyId;
	/*
	 * This is used to hold currency name for movements
	 */
	String currencyName;
	/*
	 * This is used to hold name of source for movements
	 */
	String source;
	/*
	 * This is used to hold no of references
	 */
	String reffered;
	
	/*
	 * This is used to hold the date flag. '0; means today,'1' means today+1,'2' means today+2 and '3' means ALL
	 */
	String dateFlag;
	
	/*
	 * This is used to hold no of references
	 */
	
Map<String,String> objDetails = new HashMap<String, String>();
	
	private String urlParams = new String();
	public String getUrlParams() {
		getObjDetails();
		urlParams =  objDetails.entrySet().stream()
		          .map(p -> p.getKey() + "=" + p.getValue())
		          .reduce((p1, p2) -> p1 + "&" + p2)
		          .map(s -> "?" + s)
		          .orElse("");
		return urlParams;
	}

	/**
	 * @return Returns the objDetails.
	 */
	public Map getObjDetails() {
		objDetails = new HashMap();
		objDetails.put("entityId",this.entityId);
		objDetails.put("currencyId",this.currencyId);
		objDetails.put("source",this.source);
		objDetails.put("options","N");
		objDetails.put("dateFlag",this.dateFlag);
		return objDetails;
	}
	/**
	 * @param objDetails The objDetails to set.
	 */
	public void setObjDetails(Map objDetails) {
		
		this.objDetails = objDetails;
	}
	/**
	 * @return Returns the entityId.
	 */
	public String getEntityId() {
		return entityId;
	}
	/**
	 * @param entityId The entityId to set.
	 */
	public void setEntityId(String entityId) {
		this.entityId = entityId;
	}
	
	/**
	 * @return Returns the authorise.
	 */
	public String getAuthorise() {
		return authorise;
	}
	/**
	 * @param authorise The authorise to set.
	 */
	public void setAuthorise(String authorise) {
		this.authorise = authorise;
	}
	/**
	 * @return Returns the currencyId.
	 */
	public String getCurrencyId() {
		return currencyId;
	}
	/**
	 * @param currencyId The currencyId to set.
	 */
	public void setCurrencyId(String currencyId) {
		this.currencyId = currencyId;
	}
	/**
	 * @return Returns the currencyName.
	 */
	public String getCurrencyName() {
		return currencyName;
	}
	/**
	 * @param currencyName The currencyName to set.
	 */
	public void setCurrencyName(String currencyName) {
		this.currencyName = currencyName;
	}
	/**
	 * @return Returns the reffered.
	 */
	public String getReffered() {
		return reffered;
	}
	/**
	 * @param reffered The reffered to set.
	 */
	public void setReffered(String reffered) {
		this.reffered = reffered;
	}
	/**
	 * @return Returns the source.
	 */
	public String getSource() {
		return source;
	}
	/**
	 * @param source The source to set.
	 */
	public void setSource(String source) {
		this.source = source;
	}
	
	/**
	 * @return Returns the dateFlag.
	 */
	public String getDateFlag() {
		return dateFlag;
	}
	/**
	 * @param dateFlag The dateFlag to set.
	 */
	public void setDateFlag(String dateFlag) {
		this.dateFlag = dateFlag;
	}
}
