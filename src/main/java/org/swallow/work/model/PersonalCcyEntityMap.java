/*
 * @(#)PersonalCcyEntityMap.java 1.0 19/02/11
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.work.model;

import java.util.Date;

import org.swallow.model.BaseObject;

/**
 * PersonalCcyEntityMap.java
 * 
 * This java bean has getters and setters for PersonalCcyEntityMap details
 * 
 * <AUTHOR> <PERSON>
 * @date Feb 19, 2011
 * 
 */
public class PersonalCcyEntityMap extends BaseObject implements
		org.swallow.model.AuditComponent {

	/** Default version id */
	private static final long serialVersionUID = 1L;
	// update Date
	private Date updateDate = null;
	// yesdisplay Currency
	private String yesDisplayCcy = null;
	// Denote id
	private Id id = new Id();

	public static class Id extends BaseObject {
		/** Default version id */
		private static final long serialVersionUID = 1L;
		// host Id
		private String hostId = null;
		// user Id
		private String userId = null;
		// currency Code
		private String currencyCode = null;
		// entity Id
		private String entityId = null;

		/**
		 * Default constructor
		 */
		public Id() {
		}

		/**
		 * @return the hostId
		 */
		public String getHostId() {
			return hostId;
		}

		/**
		 * @param hostId
		 *            the hostId to set
		 */
		public void setHostId(String hostId) {
			this.hostId = hostId;
		}

		/**
		 * @return the userId
		 */
		public String getUserId() {
			return userId;
		}

		/**
		 * @param userId
		 *            the userId to set
		 */
		public void setUserId(String userId) {
			this.userId = userId;
		}

		/**
		 * @return the currencyCode
		 */
		public String getCurrencyCode() {
			return currencyCode;
		}

		/**
		 * @param currencyCode
		 *            the currencyCode to set
		 */
		public void setCurrencyCode(String currencyCode) {
			this.currencyCode = currencyCode;
		}

		/**
		 * @return the entityId
		 */
		public String getEntityId() {
			return entityId;
		}

		/**
		 * @param entityId
		 *            the entityId to set
		 */
		public void setEntityId(String entityId) {
			this.entityId = entityId;
		}
	}

	/**
	 * @return the id
	 */
	public Id getId() {
		return id;
	}

	/**
	 * @param id
	 *            the id to set
	 */
	public void setId(Id id) {
		this.id = id;
	}

	/**
	 * @return the updateDate
	 */
	public Date getUpdateDate() {
		return updateDate;
	}

	/**
	 * @return the yesDisplayCcy
	 */
	public String getYesDisplayCcy() {
		return yesDisplayCcy;
	}

	/**
	 * @param yesDisplayCcy
	 *            the yesDisplayCcy to set
	 */
	public void setYesDisplayCcy(String yesDisplayCcy) {
		this.yesDisplayCcy = yesDisplayCcy;
	}

	/**
	 * @param updateDate
	 *            the updateDate to set
	 */
	public void setUpdateDate(Date updateDate) {
		this.updateDate = updateDate;
	}

}
