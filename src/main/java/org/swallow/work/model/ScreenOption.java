/*
 * @(#)ScreenOption.java 1.0 06/08/10
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.work.model;

import java.util.Collection;

import org.swallow.maintenance.model.CurrencyMaster;
import org.swallow.model.BaseObject;
import org.swallow.util.PropertiesFileLoader;
import org.swallow.util.SwtConstants;
import org.swallow.util.SwtUtil;

/**
 * ScreenOption.java
 * 
 * This java bean has getters and setters for screen options
 * 
 * <AUTHOR> A
 * @date Aug 06, 2010
 * 
 */
public class ScreenOption extends BaseObject {
	/** Id */
	private Id id = new Id();
	/** property Value */
	private String propertyValue = null;
	/** Default version id */
	private static final long serialVersionUID = 1L;
	// Holds the number of days
	private String numberOfDays = null;
	// Holds the default currency multiplier
	private String useCurrencyMultiplier = SwtConstants.DEFAULT_USE_CURRENCY_MULTIPLIER;
	// Holds the default currency personal list
	private String ccyPersonalList = SwtConstants.DEFAULT_CCY_PERSONAL_LIST;
	// Holds the default hide weekends value
	private String hideWeekends = SwtConstants.DEFAULT_HIDE_WEEKENDS;
	// Holds the default hide loro
	private String hideLoro = SwtConstants.DEFAULT_HIDE_LORO;
	// Holds the default report currency value
	private String reportCurrency = SwtConstants.NO;
	// Holds the default personal currency value
	private String personalCurrencyList = SwtConstants.NO;
	// Holds the default personal entity list value
	private String personalEntityList = SwtConstants.NO;
	// Holds the default reporting currency list
	private Collection<CurrencyMaster> reportCurrencyList = null;

	// Holds the default font size for the DataGrid which is taken from the
	// properties file.
	private String fontSize = PropertiesFileLoader.getInstance()
			.getPropertiesValue(SwtConstants.DEFAULT_FONT_SIZE);
	// Start: Code added by Bala for Mantis 1413 - Forecast Monitor on
	// 06-Jun-2011
	// Holds the hide zero sum
	private String hideZeroSum = null;
	// Holds the hide zero value
	private String hideZeroValue = null;
	// Holds the cumulative bucket total
	private String cumulativeBucketTotal = null;
	// Holds the Entity
	private String entity = null;
	// Holds the Currency
	private String currency = null;
	// Holds the hide total
	private String hideTotal = null;
	// Holds the hide Assumption
	private String hideAssumption = null;
	// Holds the hide Scenario
	private String hideScenario = null;
	// Holds the  liquidity monitor screen config
    private String liquidityMonitorConfig = null; 
	// End: Code added by Bala for Mantis 1413 - Forecast Monitor on
	// 06-Jun-2011

	/**
	 * Constructor
	 */
	public ScreenOption() {
	}

	/**
	 * @return Returns the propertyValue.
	 */
	public String getPropertyValue() {
		return propertyValue;
	}

	/**
	 * @param propertyValue
	 *            The propertyValue to set.
	 */
	public void setPropertyValue(String propertyValue) {
		this.propertyValue = propertyValue;
	}

	/**
	 * @return Returns the id.
	 */
	public Id getId() {
		return id;
	}

	/**
	 * @param id
	 *            The id to set.
	 */
	public void setId(Id id) {
		this.id = id;
	}

	/**
	 * Class to hold the primary key values for screen options
	 * 
	 * <AUTHOR> Balaji .A
	 * @date Aug 06, 2010
	 */
	public static class Id extends BaseObject {
		/** Host Id */
		private String hostId = null;
		/** User Id */
		private String userId = null;
		/** screen Id */
		private String screenId = null;
		/** Screen Id */
		private String propertyName = null;
		/** Default version id */
		private static final long serialVersionUID = 1L;

		/**
		 * Creates a new Id object.
		 */
		public Id() {
		}

		/**
		 * Creates a new Id object.
		 * 
		 * @param hostId
		 * @param userId
		 * @param screenId
		 */
		public Id(String hostId, String userId, String screenId,
				String propertyName) {
			this.hostId = hostId;
			this.userId = userId;
			this.screenId = screenId;
			this.propertyName = propertyName;
		}

		/**
		 * @return Returns the screenId.
		 */
		public String getScreenId() {
			return screenId;
		}

		/**
		 * @param screenId
		 *            The screenId to set.
		 */
		public void setScreenId(String screenId) {
			this.screenId = screenId;
		}

		/**
		 * @return Returns the propertyName.
		 */
		public String getPropertyName() {
			return propertyName;
		}

		/**
		 * @param propertyName
		 *            The propertyName to set.
		 */
		public void setPropertyName(String propertyName) {
			this.propertyName = propertyName;
		}

		/**
		 * @return Returns the userId.
		 */
		public String getUserId() {
			return userId;
		}

		/**
		 * @param userId
		 *            The userId to set.
		 */
		public void setUserId(String userId) {
			this.userId = userId;
		}

		/**
		 * @return Returns the hostId.
		 */
		public String getHostId() {
			return hostId;
		}

		/**
		 * @param hostId
		 *            The hostId to set.
		 */
		public void setHostId(String hostId) {
			this.hostId = hostId;
		}
	}

	/**
	 * @return Returns the numberOfDays.
	 */
	public String getNumberOfDays() {
		return numberOfDays;
	}

	/**
	 * @param numberOfDays
	 *            The numberOfDays to set.
	 */
	public void setNumberOfDays(String numberOfDays) {
		this.numberOfDays = numberOfDays;
	}

	/**
	 * @return Returns the useCurrencyMultiplier.
	 */
	public String getUseCurrencyMultiplier() {
		return useCurrencyMultiplier;
	}

	/**
	 * @param useCurrencyMultiplier
	 *            The useCurrencyMultiplier to set.
	 */
	public void setUseCurrencyMultiplier(String useCurrencyMultiplier) {
		if(SwtUtil.isEmptyOrNull(useCurrencyMultiplier))
			useCurrencyMultiplier = SwtConstants.DEFAULT_USE_CURRENCY_MULTIPLIER;
		this.useCurrencyMultiplier = useCurrencyMultiplier;
	}

	/**
	 * @return Returns the ccyPersonalList.
	 */
	public String getCcyPersonalList() {
		return ccyPersonalList;
	}

	/**
	 * @param ccyPersonalList
	 *            The ccyPersonalList to set.
	 */
	public void setCcyPersonalList(String ccyPersonalList) {
		if(!SwtConstants.YES.equals(ccyPersonalList))
			ccyPersonalList = SwtConstants.DEFAULT_CCY_PERSONAL_LIST;
		this.ccyPersonalList = ccyPersonalList;
	}

	/**
	 * @return Returns the hideWeekends.
	 */
	public String getHideWeekends() {
		return hideWeekends;
	}

	/**
	 * @param hideWeekends
	 *            The hideWeekends to set.
	 */
	public void setHideWeekends(String hideWeekends) {
		if(!SwtConstants.YES.equals(hideWeekends))
			hideWeekends = SwtConstants.DEFAULT_HIDE_WEEKENDS;
		this.hideWeekends = hideWeekends;
	}

	/**
	 * @return Returns the hideLoro.
	 */
	public String getHideLoro() {
		return hideLoro;
	}

	/**
	 * @param hideLoro
	 *            The hideLoro to set.
	 */
	public void setHideLoro(String hideLoro) {
		if(!SwtConstants.YES.equals(hideLoro))
			hideLoro = SwtConstants.DEFAULT_HIDE_LORO;
		this.hideLoro = hideLoro;
	}

	/**
	 * @return Returns the reportCurrency.
	 */
	public String getReportCurrency() {
		return reportCurrency;
	}

	/**
	 * @param reportCurrency
	 *            The reportCurrency to set.
	 */
	public void setReportCurrency(String reportCurrency) {
		this.reportCurrency = reportCurrency;
	}

	/**
	 * @return Returns the personalCurrencyList.
	 */
	public String getPersonalCurrencyList() {
		return personalCurrencyList;
	}

	/**
	 * @param personalCurrencyList
	 *            The personalCurrencyList to set.
	 */
	public void setPersonalCurrencyList(String personalCurrencyList) {
		this.personalCurrencyList = personalCurrencyList;
	}

	/**
	 * @return Returns the personalEntityList.
	 */
	public String getPersonalEntityList() {
		return personalEntityList;
	}

	/**
	 * @param personalEntityList
	 *            The personalEntityList to set.
	 */
	public void setPersonalEntityList(String personalEntityList) {
		this.personalEntityList = personalEntityList;
	}

	/**
	 * @return the reportCurrencyList
	 */
	public Collection<CurrencyMaster> getReportCurrencyList() {
		return reportCurrencyList;
	}

	/**
	 * @param reportCurrencyList
	 *            the reportCurrencyList to set
	 */
	public void setReportCurrencyList(
			Collection<CurrencyMaster> reportCurrencyList) {
		this.reportCurrencyList = reportCurrencyList;
	}

	/**
	 * @return fontSize
	 */
	public String getFontSize() {
		return fontSize;
	}

	/**
	 * @param sets
	 *            fontSize the fontSize
	 */
	public void setFontSize(String font) {
		this.fontSize = font;
	}

	// Start: Code added by Bala for Mantis 1413 - Forecast Monitor on
	// 06-Jun-2011
	/**
	 * Getter method for hideTotal
	 * 
	 * @return hideTotal as String
	 */

	public String getHideTotal() {
		return hideTotal;
	}

	/**
	 * Setter method for hideTotal
	 * 
	 * @param hideTotal
	 */

	public void setHideTotal(String hideTotal) {
		this.hideTotal = hideTotal;
	}

	/**
	 * Getter method for hideAssumption
	 * 
	 * @return hideAssumption as String
	 */

	public String getHideAssumption() {
		return hideAssumption;
	}

	/**
	 * Setter method for hideAssumption
	 * 
	 * @param hideAssumption
	 */

	public void setHideAssumption(String hideAssumption) {
		this.hideAssumption = hideAssumption;
	}

	/**
	 * Getter method for hideScenario
	 * 
	 * @return hideScenario as String
	 */

	public String getHideScenario() {
		return hideScenario;
	}

	/**
	 * Setter method for hideScenario
	 * 
	 * @param hideScenario
	 */

	public void setHideScenario(String hideScenario) {
		this.hideScenario = hideScenario;
	}

	/**
	 * Getter method for hideZeroSum
	 * 
	 * @return hideZeroSum as String
	 */

	public String getHideZeroSum() {
		return hideZeroSum;
	}

	/**
	 * Setter method for hideZeroSum
	 * 
	 * @param hideZeroSum
	 */

	public void setHideZeroSum(String hideZeroSum) {
		this.hideZeroSum = hideZeroSum;
	}

	/**
	 * Getter method for hideZeroValue
	 * 
	 * @return hideZeroValue as String
	 */

	public String getHideZeroValue() {
		return hideZeroValue;
	}

	/**
	 * Setter method for hideZeroValue
	 * 
	 * @param hideZeroValue
	 */

	public void setHideZeroValue(String hideZeroValue) {
		this.hideZeroValue = hideZeroValue;
	}

	/**
	 * Getter method for cumulativeBucketTotal
	 * 
	 * @return cumulativeBucketTotal as String
	 */

	public String getCumulativeBucketTotal() {
		return cumulativeBucketTotal;
	}

	/**
	 * Setter method for cumulativeBucketTotal
	 * 
	 * @param cumulativeBucketTotal
	 */

	public void setCumulativeBucketTotal(String cumulativeBucketTotal) {
		this.cumulativeBucketTotal = cumulativeBucketTotal;
	}

	/**
	 * Getter method for entity
	 * 
	 * @return entity as String
	 */

	public String getEntity() {
		return entity;
	}

	/**
	 * Setter method for entity
	 * 
	 * @param entity
	 */

	public void setEntity(String entity) {
		this.entity = entity;
	}

	/**
	 * Getter method for currency
	 * 
	 * @return currency as String
	 */

	public String getCurrency() {
		return currency;
	}

	/**
	 * Setter method for currency
	 * 
	 * @param currency
	 */

	public void setCurrency(String currency) {
		this.currency = currency;
	}
	// End: Code added by Bala for Mantis 1413 - Forecast Monitor on
	// 06-Jun-2011

	//Start : Code added by Med Amine For Mantis 2352
	public String getLiquidityMonitorOption() {
		return liquidityMonitorConfig;
	}

	public void setLiquidityMonitorOption(String liquidityMonitorOption) {
		this.liquidityMonitorConfig = liquidityMonitorOption;
	}
	//End : Code added by Med Amine For Mantis 2352
	
}
