/*
 * Created on Jan 14, 2006
 *
 * TODO To change the template for this generated file go to
 * Window - Preferences - Java - Code Style - Code Templates
 */
package org.swallow.work.model;


import java.io.Serializable;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Types;

import org.hibernate.HibernateException;
import org.hibernate.JDBCException;
import org.hibernate.MappingException;
import org.hibernate.dialect.Dialect;
import org.hibernate.engine.spi.SessionImplementor;
import org.hibernate.engine.spi.SharedSessionContractImplementor;
import org.hibernate.exception.spi.SQLExceptionConverter;
import org.hibernate.id.Configurable;
import org.hibernate.id.IdentifierGenerator;
import org.hibernate.type.LongType;
import org.hibernate.type.StringType;
import org.hibernate.type.Type;
import org.hibernate.usertype.CompositeUserType;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

/**
 * <AUTHOR>
 *
 * TODO To change the template for this generated type comment go to
 * Window - Preferences - Java - Code Style - Code Templates
 */

public class MovementIdType implements CompositeUserType, Serializable{
	
	private final Log log = LogFactory.getLog(MovementIdType.class);	// log object
	
	private static final String[] PROPERTY_NAMES = {"hostId", "entityId","movementId"}; // property name array 
	
	private static final Type[] PROPERTY_TYPES = {StringType.INSTANCE, StringType.INSTANCE,LongType.INSTANCE}; // property type array 

	  public String[] getPropertyNames() 
	  { 
	      return PROPERTY_NAMES; 
	  } 

	  public Type[] getPropertyTypes() 
	  { 
	      return PROPERTY_TYPES; 
	  }
	  
	   public Object getPropertyValue(Object component, int property) throws HibernateException 
	   {
	   		   	  
	   	  Movement.Id movementIdObject = (Movement.Id)component;
	   	  
	      switch (property) { 
	      case 0: 
	         return movementIdObject.getHostId(); 
	      case 1: 
	         return movementIdObject.getEntityId();
	      case 2: 
	         return movementIdObject.getMovementId();
	         
	      default: 
	         throw new HibernateException(property+" is invalid number"); 
	      } 
	   }
	   
	   public void setPropertyValue(Object component, int property, Object value) throws HibernateException 
	   { 
	   	  
	   	  Movement.Id movementIdObject = (Movement.Id)component;
	   	  
	      switch (property) 
		  { 
		      case 0:  
		      	movementIdObject.setHostId((String)value);
		      	break;
		      case 1: 
		      	movementIdObject.setEntityId((String)value);
		      	break;
		      case 2: 
		      	movementIdObject.setMovementId((Long)value);      	
		      	break;
		      default: 
		         throw new HibernateException(property+" is invalid index"); 
	      }
	      
	   } 	
	   
	   public Class returnedClass() 
	   { 
	      return  Match.Id.class; 
	   } 	   

	   public boolean equals(Object x, Object y) throws HibernateException 
	   { 
	      if (x == y) return true; 
	      if (x == null || y == null) return false; 
	      return x.equals(y); 
	   } 	   

	   public Object nullSafeGet(ResultSet rs, String[] names, SessionImplementor session, Object owner) 
			throws HibernateException, SQLException 
       { 
		   	if (rs.getObject(names[0])!=null && rs.getObject(names[1])!=null) 
		   	{ 
		   		String hostId = rs.getString(names[0]); 
		   		String entityId = rs.getString(names[1]);
		   		long movementhId = rs.getLong(names[2]);
		   		
		   		Movement.Id movementIdObj = new Movement.Id();
		   		movementIdObj.setHostId(hostId);
		   		movementIdObj.setEntityId(entityId);	
		   		movementIdObj.setMovementId(new Long(movementhId));		   		
		   		return movementIdObj;
		   	} 
		   	else 
		   	{ 
		   		return null; 
		   	} 
       } 
	   
	   public void nullSafeSet(PreparedStatement st, Object value, int index, SessionImplementor session) throws HibernateException, SQLException { 
	      if (value==null) { 
	         st.setNull(index, Types.VARCHAR); 
	         st.setNull(index+1, Types.VARCHAR);
	         st.setNull(index+1, Types.NUMERIC);	         
	      } 
	      else 
	      { 	      	
	      	 Movement.Id movementIdObj = (Movement.Id)value;
	      	
	         // Setting the host id
	      	 if (movementIdObj.getHostId() == null) { 
	            st.setNull(index, Types.VARCHAR); 
	         } 
	         else { 
	            st.setString(index, movementIdObj.getHostId()); 
	         } 
	      	 
	         // Setting the entity id
	         if (movementIdObj.getEntityId() == null) { 
	            st.setNull(index+1, Types.VARCHAR); 
	         } 
	         else { 
	         	st.setString(index+1, movementIdObj.getEntityId()); 
	         }
	         
	         // Setting the match id
	         if (movementIdObj.getMovementId() == null) { 
	            st.setNull(index+2, Types.VARCHAR); // TODO 
	         } 
	         else { 
	         	st.setLong(index+2, movementIdObj.getMovementId().longValue()); 
	         }	         	         
	      } 
	   } 

	   public Object deepCopy(Object value) throws HibernateException { 
	      return value; 
	   } 	   

	   public boolean isMutable() { 
	      return false; 
	   } 

	   public Serializable disassemble(Object value, SessionImplementor session) throws HibernateException { 
	      return (Serializable)value; 
	   } 

	   public Object assemble(Serializable cached, SessionImplementor session, Object owner) throws HibernateException { 
	      return cached; 
	   } 	   	  
	   
	   
		@Override
		public int hashCode(Object x) throws HibernateException {
			return super.hashCode();
		}

		@Override
		public Object nullSafeGet(ResultSet rs, String[] names, SharedSessionContractImplementor session, Object owner)
				throws HibernateException, SQLException {
			return nullSafeGet(rs, names, (SessionImplementor) session, owner);
		}

		@Override
		public void nullSafeSet(PreparedStatement st, Object value, int index, SharedSessionContractImplementor session)
				throws HibernateException, SQLException {
			nullSafeSet(st, value, index, (SessionImplementor) session);
		}

		@Override
		public Serializable disassemble(Object value, SharedSessionContractImplementor session) throws HibernateException {
			return disassemble(value, (SessionImplementor) session);
		}

		@Override
		public Object assemble(Serializable cached, SharedSessionContractImplementor session, Object owner)
				throws HibernateException {
			return assemble(session, (SessionImplementor) session, owner);
		}

		@Override
		public Object replace(Object original, Object target, SharedSessionContractImplementor session, Object owner)
				throws HibernateException {
			throw new HibernateException("Method [replace] not implemented in ForecastTemplateIdType");
		}
}
