/*
 * Created on Sep 15, 2006
 *
 * TODO To change the template for this generated file go to
 * Window - Preferences - Java - Code Style - Code Templates
 */
package org.swallow.work.model;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 *
 * TODO To change the template for this generated type comment go to
 * Window - Preferences - Java - Code Style - Code Templates
 */
public class AcctBreakdownModelCashValue implements Serializable{
	/* Start: Code change at onsight for Smart-Predict_SRS_PhaseII_Loro_Curr_0.3.doc */
	private String iconIndicatorFlag;
	/* End: Code change at onsight for Smart-Predict_SRS_PhaseII_Loro_Curr_0.3.doc */
	private Double balance;
	/* START :Code change aton sight for implementing "Shortcut to Smart-Predict_SRS_PhaseII_Open_Unexpected_0.1.doc"  */
	private Double openUnexpectedBalance;
	/* END :Code change aton sight for implementing "Shortcut to Smart-Predict_SRS_PhaseII_Open_Unexpected_0.1.doc"  */
	private Double startingBalance;
	private Date timeStamp;
	
	
	/**
	 * @return Returns the balance.
	 */
	public Double getBalance() {
		return balance;
	}
	/**
	 * @param balance The balance to set.
	 */
	public void setBalance(Double balance) {
		this.balance = balance;
	}
	/**
	 * @return Returns the startingBalance.
	 */
	public Double getStartingBalance() {
		return startingBalance;
	}
	/**
	 * @param startingBalance The startingBalance to set.
	 */
	public void setStartingBalance(Double startingBalance) {
		this.startingBalance = startingBalance;
	}
	
	/**
	 * @return Returns the timeStamp.
	 */
	public Date getTimeStamp() {
		return timeStamp;
	}
	/**
	 * @param timeStamp The timeStamp to set.
	 */
	public void setTimeStamp(Date timeStamp) {
		this.timeStamp = timeStamp;
	}
	/* START :Code change aton sight for implementing "Shortcut to Smart-Predict_SRS_PhaseII_Open_Unexpected_0.1.doc"  */
	/*
	 * @return Returns the OpenUnexpectedBalance.
	 */
	public Double getOpenUnexpectedBalance() {
		return openUnexpectedBalance;
	}
	/**
	 * 
	 * @param openUnexpectedBalance
	 */
	public void setOpenUnexpectedBalance(Double openUnexpectedBalance) {
		this.openUnexpectedBalance = openUnexpectedBalance;
	}
	/* END:Code change aton sight for implementing "Shortcut to Smart-Predict_SRS_PhaseII_Open_Unexpected_0.1.doc"  */
	
	/* Start: Code change at onsight for Smart-Predict_SRS_PhaseII_Loro_Curr_0.3.doc */
	/**
	 * Returns Yes if the account for this cash object cantains the Loro/Curr account in calculation.
	 */
	public String getIconIndicatorFlag() {
		return iconIndicatorFlag;
	}
	/**
	 * 
	 * @param iconIndicatorFlag
	 */
	public void setIconIndicatorFlag(String iconIndicatorFlag) {
		this.iconIndicatorFlag = iconIndicatorFlag;
	}
	
	
	
	/* End: Code change at onsight for Smart-Predict_SRS_PhaseII_Loro_Curr_0.3.doc */
}
