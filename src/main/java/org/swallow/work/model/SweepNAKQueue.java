package org.swallow.work.model;

import java.io.Serializable;

public class SweepNAKQueue implements Serializable {
	private String entityId;
	private String currGrp;
	private String currencyCode;
	private String currencyName;
	private String NAKs;
	private boolean zeroCount;
	/* START : Smart-Predict_SRS_Sweep_Overdue_ACKs_0.2.doc change 31/07/2007 */
	private String overdueACKs;
	private boolean zeroCountOverdueACK;
	/* END : Smart-Predict_SRS_Sweep_Overdue_ACKs_0.2.doc change 31/07/2007 */
	
	public boolean isZeroCount() {
		return zeroCount;
	}
	public void setZeroCount(boolean zeroCount) {
		this.zeroCount = zeroCount;
	}
	public String getCurrencyCode() {
		return currencyCode;
	}
	public void setCurrencyCode(String currencyCode) {
		this.currencyCode = currencyCode;
	}
	public String getCurrencyName() {
		return currencyName;
	}
	public void setCurrencyName(String currencyName) {
		this.currencyName = currencyName;
	}
	public String getNAKs() {
		return NAKs;
	}
	public void setNAKs(String ks) {
		NAKs = ks;
	}
	public String getCurrGrp() {
		return currGrp;
	}
	public void setCurrGrp(String currGrp) {
		this.currGrp = currGrp;
	}
	public String getEntityId() {
		return entityId;
	}
	public void setEntityId(String entityId) {
		this.entityId = entityId;
	}
	/* START : Smart-Predict_SRS_Sweep_Overdue_ACKs_0.2.doc change 31/07/2007 */
	public String getOverdueACKs() {
		return overdueACKs;
	}
	public void setOverdueACKs(String overdueACKs) {
		this.overdueACKs = overdueACKs;
	}
	public boolean isZeroCountOverdueACK() {
		return zeroCountOverdueACK;
	}
	public void setZeroCountOverdueACK(boolean zeroCountOverdueACK) {
		this.zeroCountOverdueACK = zeroCountOverdueACK;
	}
	/* END : Smart-Predict_SRS_Sweep_Overdue_ACKs_0.2.doc change 31/07/2007 */
	

}
