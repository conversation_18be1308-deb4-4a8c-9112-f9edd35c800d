/*
 * Created on Jan 3, 2006
 *
 * TODO To change the template for this generated file go to
 * Window - Preferences - Java - Code Style - Code Templates
 */
package org.swallow.work.model;

import java.util.Date;

import org.swallow.model.BaseObject;
import org.swallow.work.model.Match.Id;

/**
 * <AUTHOR>
 * 
 * TODO To change the template for this generated type comment go to Window -
 * Preferences - Java - Code Style - Code Templates
 */
public class MovementLock extends BaseObject {
	
	 
	 private String currCode;
	
	/**
	 * @return Returns the id.
	 */
	public Id getId() {
		return id;
	}
	/**
	 * @param id The id to set.
	 */
	public void setId(Id id) {
		this.id = id;
	}
	/**
	 * @return Returns the updateUser.
	 */
	public String getUpdateUser() {
		return updateUser;
	}
	/**
	 * @param updateUser The updateUser to set.
	 */
	public void setUpdateUser(String updateUser) {
		this.updateUser = updateUser;
	}
	/**
	 * @return Returns the updateDate.
	 */
	public Date getUpdateDate() {
		return updateDate;
	}
	/**
	 * @param updateDate The updateDate to set.
	 */
	public void setUpdateDate(Date updateDate) {
		this.updateDate = updateDate;
	}
	/**
	 * @return Returns the userId.
	 */
	private Id id = new Id();
	
	private Date updateDate;
	private String updateUser;
	public static class Id extends BaseObject{
		
		private String hostId ;
		private Long movementId;
		/* Start code: Code added for Mantis 1221 by Karthik on 08-Sep-10 */ 
		private String entityId;
		/* End code: Code added for Mantis 1221 by Karthik on 08-Sep-10 */
		
		public Id() {
		}
		
		public Id(String hostId ,Long movementId ) {
			this.hostId = hostId;
			this.movementId = movementId;
			
			
		}
		/**
		 * @return Returns the hostId.
		 */
		public String getHostId() {
			return hostId;
		}
		/**
		 * @param hostId The hostId to set.
		 */
		public void setHostId(String hostId) {
			this.hostId = hostId;
		}
		/**
		 * @return Returns the movementId.
		 */
		public Long getMovementId() {
			return movementId;
		}
		/**
		 * @param movementId The movementId to set.
		 */
		public void setMovementId(Long movementId) {
			this.movementId = movementId;
		}
		
		/* Start code: Code added for Mantis 1221 by Karthik on 08-Sep-10 */
		/**
		 * @return Returns the entityId.
		 */
		public String getEntityId() {
			return entityId;
		}
		/**
		 * @param entityId The entityId to set.
		 */
		public void setEntityId(String entityId) {
			this.entityId = entityId;
		}
		/* End code: Code added for Mantis 1221 by Karthik on 08-Sep-10 */
	}
	/**
	 * @return Returns the currCode.
	 */
	public String getCurrCode() {
		return currCode;
	}
	/**
	 * @param currCode The currCode to set.
	 */
	public void setCurrCode(String currCode) {
		this.currCode = currCode;
	}
	
}