package org.swallow.work.model;

import org.swallow.model.BaseObject;

/**
 * ScenarioCount
 * 
 * <AUTHOR>
 * TODO: add java dopcs
 */
public class ScenarioCounts extends BaseObject{

	/** serial version id */
	private static final long serialVersionUID = 1L;
	private Id id = new Id();

// TODO: Add java docs
	public static class Id extends BaseObject {
		/** Default version id */
		private static final long serialVersionUID = 1L;
		private String scenarioId;
		private String hostId;
		private String entityId;
		private String currencyCode;

		
		public String getScenarioId() {
			return scenarioId;
		}
		public void setScenarioId(String scenarioId) {
			this.scenarioId = scenarioId;
		}
		public String getHostId() {
			return hostId;
		}
		public void setHostId(String hostId) {
			this.hostId = hostId;
		}
		public String getEntityId() {
			return entityId;
		}
		public void setEntityId(String entityId) {
			this.entityId = entityId;
		}
		public String getCurrencyCode() {
			return currencyCode;
		}
		public void setCurrencyCode(String currencyCode) {
			this.currencyCode = currencyCode;
		}
		public static long getSerialversionuid() {
			return serialVersionUID;
		}
		
	}
	

	private Long scenarioCount;
	private Long scenarioCountOverThreshold;
	

	public Id getId() {
		return id;
	}

	public void setId(Id id) {
		this.id = id;
	}

	public Long getScenarioCount() {
		return scenarioCount;
	}

	public void setScenarioCount(Long scenarioCount) {
		this.scenarioCount = scenarioCount;
	}

	public Long getScenarioCountOverThreshold() {
		return scenarioCountOverThreshold;
	}

	public void setScenarioCountOverThreshold(Long scenarioCountOverThreshold) {
		this.scenarioCountOverThreshold = scenarioCountOverThreshold;
	}

}