/*
 * @(#)CentralBankMonitor.java 1.0 19/03/10
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.work.model;

import java.math.BigDecimal;
import java.util.Date;
import java.util.Hashtable;

import org.swallow.model.BaseObject;

/**
 * CentralBankMonitor.java
 * 
 * This java bean has getters and setters for CentralBankMonitor details
 * 
 * <AUTHOR>
 * @date Mar 19, 2010
 * 
 */
public class CentralBankMonitor extends BaseObject implements
		org.swallow.model.AuditComponent {

	/** Default version id */
	private static final long serialVersionUID = 1L;
	/* Start:Code modified by <PERSON><PERSON><PERSON> to use the amount from Double to BigDecimal */
	// Amount
	private BigDecimal amount;
	/* End:Code modified by <PERSON><PERSON><PERSON> to use the amount from Double to BigDecimal */
	// Start:Code added by Kalidass for Mantis 1181 on 23-July-2010
	// Amount
	private BigDecimal amtBeforeEdit;
	// Update Date
	private Date updateDate;
	//Update User
	private String updateUser = null;
	private Boolean isCheckNeeded = false;
	// End:Code added by Kalidass for Mantis 1181 on 23-July-2010
	// Denote id
	private Id id = new Id();

	public static Hashtable logTable = new Hashtable();

	static {
		// Start:Code modified by Kalidass for Mantis 1181 on 23-July-2010
		logTable.put("entityId", "Entity Id");
		logTable.put("userId", "User Id");
		logTable.put("valueDate", "Value Date");
		logTable.put("amount", "Amount");
		logTable.put("updateDate", "Update Date");
		logTable.put("updateUser", "Update User");
		//End:Code modified by Kalidass for Mantis 1181 on 23-July-2010
	}

	public static class Id extends BaseObject {
		/** Default version id */
		private static final long serialVersionUID = 1L;
		// Start:Code modified/added by Kalidass for Mantis 1181 on 23-July-2010
		private String entityId;
		private String userId;
		// End:Code modified/added by Kalidass for Mantis 1181 on 23-July-2010
		// Value Date
		private Date valueDate;

		/**
		 * Default constructor
		 */
		public Id() {
		}

		// Start:Code modified/added by Kalidass for Mantis 1181 on 23-July-2010
		
		/**
		 * @return the entityId
		 */
		public String getEntityId() {
			return entityId;
		}



		/**
		 * @param entityId the entityId to set
		 */
		public void setEntityId(String entityId) {
			this.entityId = entityId;
		}



		/**
		 * @return the userId
		 */
		public String getUserId() {
			return userId;
		}



		/**
		 * @param userId the userId to set
		 */
		public void setUserId(String userId) {
			this.userId = userId;
		}

		// End:Code modified/added by Kalidass for Mantis 1181 on 23-July-2010

		/**
		 * @return the valueDate
		 */
		public Date getValueDate() {
			return valueDate;
		}

		/**
		 * @param valueDate
		 *            the valueDate to set
		 */
		public void setValueDate(Date valueDate) {
			this.valueDate = valueDate;
		}

	}
	/* Start:Code commented by Kalidass to use the amount from Double to BigDecimal */
//	/**
//	 * @return the amount
//	 */
//	public Double getAmount() {
//		return amount;
//	}
//
//	/**
//	 * @param amount
//	 *            the amount to set
//	 */
//	public void setAmount(Double amount) {
//		this.amount = amount;
//	}
	/* End:Code commented by Kalidass to use the amount from Double to BigDecimal */
	/**
	 * @return the id
	 */
	public Id getId() {
		return id;
	}

	/**
	 * @param id
	 *            the id to set
	 */
	public void setId(Id id) {
		this.id = id;
	}
	/* Start:Code added by Kalidass to use the amount from Double to BigDecimal */
	/**
	 * @return the amount
	 */
	public BigDecimal getAmount() {
		return amount;
	}

	/**
	 * @param amount the amount to set
	 */
	public void setAmount(BigDecimal amount) {
		this.amount = amount;
	}
	/* end:Code added by Kalidass to use the amount from Double to BigDecimal */
	
	// Start:Code modified/added by Kalidass for Mantis 1181 on 23-July-2010
	/**
	 * @return the updateDate
	 */
	public Date getUpdateDate() {
		return updateDate;
	}

	/**
	 * @param updateDate the updateDate to set
	 */
	public void setUpdateDate(Date updateDate) {
		this.updateDate = updateDate;
	}

	/**
	 * @return the updateUser
	 */
	public String getUpdateUser() {
		return updateUser;
	}

	/**
	 * @param updateUser the updateUser to set
	 */
	public void setUpdateUser(String updateUser) {
		this.updateUser = updateUser;
	}

	/**
	 * @return the amtBeforeEdit
	 */
	public BigDecimal getAmtBeforeEdit() {
		return amtBeforeEdit;
	}

	/**
	 * @param amtBeforeEdit the amtBeforeEdit to set
	 */
	public void setAmtBeforeEdit(BigDecimal amtBeforeEdit) {
		this.amtBeforeEdit = amtBeforeEdit;
	}

	/**
	 * @return the isCheckNeeded
	 */
	public Boolean getIsCheckNeeded() {
		return isCheckNeeded;
	}

	/**
	 * @param isCheckNeeded the isCheckNeeded to set
	 */
	public void setIsCheckNeeded(Boolean isCheckNeeded) {
		this.isCheckNeeded = isCheckNeeded;
	}
	// End:Code modified/added by Kalidass for Mantis 1181 on 23-July-2010
}
