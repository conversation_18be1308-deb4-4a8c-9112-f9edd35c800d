<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE hibernate-mapping PUBLIC
        "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
        "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">

<hibernate-mapping>
    <class name="org.swallow.work.model.SweepMsgFormat" table="P_SWEEP_MESSAGE">
    
		<composite-id name="id" class="org.swallow.work.model.SweepMsgFormat$Id" unsaved-value="any">
			<key-property name="hostId" access="field" column="HOST_ID" />
<!-- 	        <key-property name="entityId" access="field" column="ENTITY_ID"/> -->
	        <key-property name="sweepId" access="field" column="SWEEP_ID" />
	        <key-property name="sweepMsgLetter" access="field" column="SWEEP_MESSAGE_LETTER" />
	     </composite-id>
	     
	    <property name="messageId" column="MESSAGE_ID" not-null="false"/>
      	<property name="msgFormatId" column="MESSAGE_FORMAT_ID" not-null="true"/>	
        <property name="status" column="SWEEP_MESSAGE_STATUS" not-null="true"/>	
        <property name="updateDate" column="UPDATE_DATE" not-null="false"/>	
	    <property name="updateuser" column="UPDATE_USER" not-null="false"/>
   	    <property name="ACKNAKmessageId" column="ACK_NAK_MESSAGE_ID" not-null="false"/>
	     	
	    
	</class>
</hibernate-mapping>