/*
 * Created on Jun 7, 2006
 *
 * TODO To change the template for this generated file go to
 * Window - Preferences - Java - Code Style - Code Templates
 */
package org.swallow.work.model;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 *
 * TODO To change the template for this generated type comment go to
 * Window - Preferences - Java - Code Style - Code Templates
 */
public class PageSummary implements Serializable{
	private int pageNo;
	private String currencyCode;
	private String entityId;
	private String dateAsString;
	private String selectedList;
	private int currentPageNo;
	private int maxPages;
	private String posLvlIdAsString;
	private String bookCode;
/* Start Added for integration with WorkFlow Monitor, unsettledYesterday */	
	private String currGrp;
	private String predictStatus;
	private String matchStatus;
	private String roleId;
/* End Added for integration with WorkFlow Monitor, unsettledYesterday*/
	private int totalCount;
	
	private Map pageSummary;
	

	
	public Map getPageSummary() {
		pageSummary = new HashMap();
		pageSummary.put("pageNoValue", Integer.toString(pageNo));
		pageSummary.put("currentPage", Integer.toString(currentPageNo));
		pageSummary.put("entityId", entityId);
		pageSummary.put("currencyCode", currencyCode);
		pageSummary.put("date", dateAsString);
		pageSummary.put("valueDate", dateAsString);
		//pageSummary.put("selectedList",selectedList);
		pageSummary.put("maxPages", Integer.toString(maxPages));
		pageSummary.put("posLvlId", posLvlIdAsString);
		pageSummary.put("bookCode", bookCode);
/* Start Added for integration with WorkFlow Monitor, unsettledYesterday, BackValued etc */		
		pageSummary.put("currGrp", currGrp);
		pageSummary.put("predictStatus", predictStatus);
		pageSummary.put("matchStatus", matchStatus);
		pageSummary.put("roleId", roleId);
/* End Added for integration with WorkFlow Monitor, unsettledYesterday, BackValued etc*/
		pageSummary.put("totalCount", Integer.toString(totalCount));
		return pageSummary;
	}
	public void setPageSummary(Map pageSummary) {
		this.pageSummary = pageSummary;
	}
	public int getPageNo() {
		return pageNo;
	}
	public void setPageNo(int pageNo) {
		this.pageNo = pageNo;
	}
	public String getCurrencyCode() {
		return currencyCode;
	}
	public void setCurrencyCode(String currencyCode) {
		this.currencyCode = currencyCode;
	}
	public String getDateAsString() {
		return dateAsString;
	}
	public void setDateAsString(String dateAsString) {
		this.dateAsString = dateAsString;
	}
	public String getEntityId() {
		return entityId;
	}
	public void setEntityId(String entityId) {
		this.entityId = entityId;
	}
	public String getSelectedList() {
		return selectedList;
	}
	public void setSelectedList(String selectedList) {
		this.selectedList = selectedList;
	}
	public int getCurrentPageNo() {
		return currentPageNo;
	}
	public void setCurrentPageNo(int currentPageNo) {
		this.currentPageNo = currentPageNo;
	}
	public int getMaxPages() {
		return maxPages;
	}
	public void setMaxPages(int maxPages) {
		this.maxPages = maxPages;
	}
	
	public String getPosLvlIdAsString() {
		return posLvlIdAsString;
	}
	public void setPosLvlIdAsString(String posLvlIdAsString) {
		this.posLvlIdAsString = posLvlIdAsString;
	}
	public String getBookCode() {
		return bookCode;
	}
	public void setBookCode(String bookCode) {
		this.bookCode = bookCode;
	}

/* Start Added for integration with WorkFlow Monitor, unsettledYesterday, getMvmntsfromWorkFlowMonitor */	
	/**
	 * @return Returns the currGrp.
	 */
	public String getCurrGrp() {
		return currGrp;
	}
	/**
	 * @param currGrp The currGrp to set.
	 */
	public void setCurrGrp(String currGrp) {
		this.currGrp = currGrp;
	}

	/**
	 * @return Returns the matchStatus.
	 */
	public String getMatchStatus() {
		return matchStatus;
	}
	/**
	 * @param matchStatus The matchStatus to set.
	 */
	public void setMatchStatus(String matchStatus) {
		this.matchStatus = matchStatus;
	}
	/**
	 * @return Returns the predictStatus.
	 */
	public String getPredictStatus() {
		return predictStatus;
	}
	/**
	 * @param predictStatus The predictStatus to set.
	 */
	public void setPredictStatus(String predictStatus) {
		this.predictStatus = predictStatus;
	}
	/**
	 * @return Returns the roleId.
	 */
	public String getRoleId() {
		return roleId;
	}
	/**
	 * @param roleId The roleId to set.
	 */
	public void setRoleId(String roleId) {
		this.roleId = roleId;
	}
/* End Added for integration with WorkFlow Monitor, unsettledYesterday, getMvmntsfromWorkFlowMonitor */	

/**
 * @return Returns the totalCount.
 */
public int getTotalCount() {
	return totalCount;
}
/**
 * @param totalCount The totalCount to set.
 */
public void setTotalCount(int totalCount) {
	this.totalCount = totalCount;
}
}
