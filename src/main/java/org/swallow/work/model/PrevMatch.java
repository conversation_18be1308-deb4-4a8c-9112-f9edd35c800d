/*
 * @(#)PrevMatch.java 1.0 29/12/08
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */

package org.swallow.work.model;

import java.util.Date;

import org.swallow.model.BaseObject;

/**
 * @version 1.0
 * To maintain prevmatch information 
 */

public class PrevMatch extends BaseObject {

	/*Start:variable declaration*/
	private Id id = new Id();	
	private String updateUser;
	private Date updateDate;
	/*End:variable declaration*/
	/**
	 * Constructor
	 */
	public PrevMatch()
	{
	}		
	
	public static class Id extends BaseObject{
		/*Start:variable declaration*/		
		private String entityId;
		private Long movementId;
		private Long prevMatchedWith;		
		/*End:variable declaration*/
		
		/**
		 * Constructor
		 */
		public Id() {
		}
		
		/**
		 * inner class defines primary key fields 
		 */
		public Id(String entityId ,Long movementId ,Long prevMatchWith) {
			
			this.entityId = entityId;
			this.movementId = movementId;
			this.prevMatchedWith=prevMatchWith;
		}		
		
		/**
		 * @return Returns the entityId.
		 */

		public String getEntityId() {
			return entityId;
		}
		/**
		 * @param entityId The entityId to set.
		 */

		public void setEntityId(String entityId) {
			this.entityId = entityId;
		}
		/**
		 * @return Returns the movementId.
		 */

		public Long getMovementId() {
			return movementId;
		}
		/**
		 * @param movementId The movementId to set.
		 */

		public void setMovementId(Long movementId) {
			this.movementId = movementId;
		}

		/**
		 * @return Returns the prevMatchedWith
		 */

		public Long getPrevMatchedWith() {
			return prevMatchedWith;
		}
		/**
		 * @param prevMatchedWith The prevMatchedWith to set.
		 */

		public void setPrevMatchedWith(Long prevMatchedWith) {
			this.prevMatchedWith = prevMatchedWith;
		}
		
	}

	/**
	 * @return Returns the id.
	 */
	public Id getId() {
		return id;
	}
	
	/**
	 * @param id The id to set.
	 */

	public void setId(Id id) {
		this.id = id;
	}
	
	/**
	 * @return Returns the updateUser.
	 */

	public String getUpdateUser() {
		return updateUser;
	}
	
	/**
	 * @param updateUser The updateUser to set.
	 */
	public void setUpdateUser(String updateUser) {
		this.updateUser = updateUser;
	}
	/**
	 * @return Returns the updateDate.
	 */

	public Date getUpdateDate() {
		return updateDate;
	}
	
	
	/**
	 * @param updateDate The updateDate to set.
	 */

	public void setUpdateDate(Date updateDate) {
		this.updateDate = updateDate;
	}
}
