package org.swallow.work.model;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.HashMap;

public class AlertTreeVO  implements Serializable {

	
	
	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	
	
	
	private ArrayList<ScenarioAlertCount> scenarioAlertList = new ArrayList<ScenarioAlertCount>();
	private HashMap<String, String> scenarioTreeGroupingLevels = new HashMap<String, String>();
	private int totalCount = 0;
	
	
	
	public ArrayList<ScenarioAlertCount> getScenarioAlertList() {
		return scenarioAlertList;
	}
	public void setScenarioAlertList(ArrayList<ScenarioAlertCount> scenarioAlertList) {
		this.scenarioAlertList = scenarioAlertList;
	}
	public HashMap<String, String> getScenarioTreeGroupingLevels() {
		return scenarioTreeGroupingLevels;
	}
	public void setScenarioTreeGroupingLevels(HashMap<String, String> scenarioTreeGroupingLevels) {
		this.scenarioTreeGroupingLevels = scenarioTreeGroupingLevels;
	}
	public int getTotalCount() {
		return totalCount;
	}
	public void setTotalCount(int totalCount) {
		this.totalCount = totalCount;
	}
	
	
	
}
