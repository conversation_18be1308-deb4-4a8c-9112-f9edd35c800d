<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE hibernate-mapping PUBLIC
        "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
        "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">

<hibernate-mapping>
    <class name="org.swallow.work.model.CrossReference" table="P_REFERENCE_XREF">

  <id name="refSeqNo" column="XREF_SEQ_NO" type="long">
   <generator class="sequence">
    <param name="sequence_name">SEQ_P_REFERENCE_XREF</param>
   </generator>
  </id>
  <property name="hostId" column="HOST_ID"/>
  <property name="entityId" column="ENTITY_ID"/>
  <property name="movementId" column="MOVEMENT_ID"/>
  <property name="sourceOfReference" column="SOURCE_OF_REFERENCE"/>
  <property name="alternateId" column="ALTERNATE_ID"/>
  <property name="businessSource" column="BUSINESS_SOURCE"/>
  <property name="crossReference" column="CROSS_REFERENCE"/>
  <property name="currencyCode" column="CURRENCY_CODE"/>
  <property name="updateDate" column="UPDATE_DATE"/>  
  <property name="updateUser" column="UPDATE_USER"/>

    </class>
</hibernate-mapping>
