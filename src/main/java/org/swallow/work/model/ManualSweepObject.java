/*
 * Created on Mar 18, 2006
 *
 * TODO To change the template for this generated file go to
 * Window - Preferences - Java - Code Style - Code Templates
 */
package org.swallow.work.model;

import java.util.Date;

import javax.servlet.http.HttpSession;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.swallow.model.BaseObject;
import org.swallow.util.CommonDataManager;
import org.swallow.util.SwtConstants;
import org.swallow.util.SwtUtil;
import org.swallow.util.UserThreadLocalHolder;


/**
 *This class contains getters and setters for Manual Sweeping
 */
public class ManualSweepObject extends BaseObject implements Comparable{

	//instance for  id
	private Id id = new Id();
	//instance for predicted Balance
	private Double predictedBalance = new Double(0);
	//instance for external Balance 
	private Double externalBalance = new Double(0);
	//instance for target Balance
	private Double targetBalance = new Double(0);
	//instance for final Balance
	private Double finalBalance = new Double(0);
	//instance for start Balance
	private Double startBalance = new Double(0);
	//instance for sweep Amount
	private Double sweepAmount = new Double(0);
	
	//variable used to hold currency code
	private String currencyCode = "";
	//variable used to hold account type
	private String acctType = "";
	//variable used to hold target balance sign
	private String targetBalSign = "";
	//variable used to hold display level
	private String displayLevel = "";
	//variable used to hold cut off 
	private String cuttOff = "";
	//variable used to hold flag for sweep amount
	private String signFlagForSweepAmount = "";
	//variable used to hold main account id
	private String mainAcctId = "";
	//Start:code modified by sandeepkumar for mantis 2039 - Visually indicate accounts past cut-off or subject to non-working day
	//variable used to hold value date achievable value
	private String isValueDateAchievable = null;
	//End:code modified by sandeepkumar for mantis 2039 - Visually indicate accounts past cut-off or subject to non-working day
	//variable used to hold account name
	private String accName = "";
	//variable used to hold sweep from balance
	private String sweepFrmbal = "";
	//variable used to hold	 movement entity id
	private String movementEntityId = "";
	/**
	 * @return the sweepFrmbal
	 */
	public String getSweepFrmbal() {
		return sweepFrmbal;
	}
	/**
	 * @param sweepFrmbal the sweepFrmbal to set
	 */
	public void setSweepFrmbal(String sweepFrmbal) {
		this.sweepFrmbal = sweepFrmbal;
	}
	
	/**
	 * @return the externalBalanceAsString
	 */
	public String getExternalBalanceAsString() {
		return externalBalanceAsString;
	}
	/**
	 * @param externalBalanceAsString the externalBalanceAsString to set
	 */
	public void setExternalBalanceAsString(String externalBalanceAsString) {
		this.externalBalanceAsString = externalBalanceAsString;
	}
	/**
	 * @return the externalBalanceNegative
	 */
	public boolean isExternalBalanceNegative() {
		return externalBalanceNegative;
	}
	/**
	 * @param externalBalanceNegative the externalBalanceNegative to set
	 */
	public void setExternalBalanceNegative(boolean externalBalanceNegative) {
		this.externalBalanceNegative = externalBalanceNegative;
	}
	/**
	 * @return the externalBalance
	 */
	public Double getExternalBalance() {
		return externalBalance;
	}
	/**
	 * @param externalBalance the externalBalance to set
	 */
	public void setExternalBalance(Double externalBalance) {
		this.externalBalance = externalBalance;
	}

	/**
	 * @return Returns the movementEntityId.
	 */
	public String getMovementEntityId() {
		return movementEntityId;
	}
	/**
	 * @param movementEntityId The movementEntityId to set.
	 */
	public void setMovementEntityId(String movementEntityId) {
		this.movementEntityId = movementEntityId;
	}
	
	
	
	//added for Rabo Bank starts
	/**
	 * @return Returns the accName.
	 */
	public String getAccName() {
		return accName;
	}
	/**
	 * @param accName The accName to set.
	 */
	public void setAccName(String accName) {
		this.accName = accName;
	}

	/**
	 * @return Returns the signFlagForSweepAmount.
	 */
	public String getSignFlagForSweepAmount() {
		return signFlagForSweepAmount;
	}
	/**
	 * @param signFlagForSweepAmount The signFlagForSweepAmount to set.
	 */
	public void setSignFlagForSweepAmount(String signFlagForSweepAmount) {
		this.signFlagForSweepAmount = signFlagForSweepAmount;
	}
	/**
	 * @return Returns the finalBalanceNegative.
	 */
	public boolean getFinalBalanceNegative() {
		return finalBalanceNegative;
	}
	/**
	 * @param finalBalanceNegative The finalBalanceNegative to set.
	 */
	public void setFinalBalanceNegative(boolean finalBalanceNegative) {
		this.finalBalanceNegative = finalBalanceNegative;
	}
	/**
	 * @return Returns the predictedBalanceNegative.
	 */
	public boolean getPredictedBalanceNegative() {
		return predictedBalanceNegative;
	}
	/**
	 * @param predictedBalanceNegative The predictedBalanceNegative to set.
	 */
	public void setPredictedBalanceNegative(boolean predictedBalanceNegative) {
		this.predictedBalanceNegative = predictedBalanceNegative;
	}
	/**
	 * @return Returns the startBalanceNegative.
	 */
	public boolean getStartBalanceNegative() {
		return startBalanceNegative;
	}
	/**
	 * @param startBalanceNegative The startBalanceNegative to set.
	 */
	public void setStartBalanceNegative(boolean startBalanceNegative) {
		this.startBalanceNegative = startBalanceNegative;
	}
	private boolean predictedBalanceNegative = false;
	
	private boolean externalBalanceNegative = false;
	
	private boolean finalBalanceNegative = false;
	private boolean startBalanceNegative = false;
	private boolean targetBalanceNegative = false;
	
	private String predictedBalanceAsString = "";
	
	private String externalBalanceAsString = "";
	
	
	private String finalBalanceAsString =  "";
	private String targetBalanceAsString =  "";
	private String sweepAmountAsString =  "";
	
	
	private String startBalanceAsString =  "";
	

	private boolean isEmpty = true;	
	private boolean isToday = false;	
	private boolean isTodayPlusOne = false;	
	private boolean isTodayPlusTwo = false;
	
	private static final Log log = LogFactory.getLog(ManualSweepObject.class);	

	public ManualSweepObject() {
		super();
	}
	/**
	 * @return Returns the finalBalance.
	 */
	public Double getFinalBalance() {
		return finalBalance;
	}
	/**
	 * @param finalBalance The finalBalance to set.
	 */
	public void setFinalBalance(Double finalBalance) {
		this.finalBalance = finalBalance;
	}
	
	/**
	 * @return Returns the id.
	 */
	public Id getId() {
		return id;
	}
	/**
	 * @param id The id to set.
	 */
	public void setId(Id id) {
		this.id = id;
	}
	/**
	 * @return Returns the predictedBalance.
	 */
	public Double getPredictedBalance() {
		return predictedBalance;
	}
	/**
	 * @param predictedBalance The predictedBalance to set.
	 */
	public void setPredictedBalance(Double predictedBalance) {
		this.predictedBalance = predictedBalance;
	}
	
	/**
	 * @return Returns the startBalance.
	 */
	public Double getStartBalance() {
		return startBalance;
	}
	/**
	 * @param startBalance The startBalance to set.
	 */
	public void setStartBalance(Double startBalance) {
		this.startBalance = startBalance;
	}
	

	/**
	 * @return Returns the finalBalanceAsString.
	 */
	public String getFinalBalanceAsString() {
		return finalBalanceAsString;
	}
	/**
	 * @param finalBalanceAsString The finalBalanceAsString to set.
	 */
	public void setFinalBalanceAsString(String finalBalanceAsString) {
		this.finalBalanceAsString = finalBalanceAsString;
	}
	
	
	/**
	 * @return Returns the predictedBalanceAsString.
	 */
	public String getPredictedBalanceAsString() {
		return predictedBalanceAsString;
	}
	/**
	 * @param predictedBalanceAsString The predictedBalanceAsString to set.
	 */
	public void setPredictedBalanceAsString(String predictedBalanceAsString) {
		this.predictedBalanceAsString = predictedBalanceAsString;
	}
	
	/**
	 * @return Returns the startBalanceAsString.
	 */
	public String getStartBalanceAsString() {
		return startBalanceAsString;
	}
	/**
	 * @param startBalanceAsString The startBalanceAsString to set.
	 */
	public void setStartBalanceAsString(String startBalanceAsString) {
		this.startBalanceAsString = startBalanceAsString;
	}
		
	/**
	 * @return Returns the isEmpty.
	 */
	public boolean isEmpty() {
		return isEmpty;
	}
	/**
	 * @param isEmpty The isEmpty to set.
	 */
	public void setEmpty(boolean isEmpty) {
		this.isEmpty = isEmpty;
	}
	/**
	 * @return Returns the isToday.
	 */
	public boolean isToday() {
		return isToday;
	}
	/**
	 * @param isToday The isToday to set.
	 */
	public void setToday(boolean isToday) {
		this.isToday = isToday;
	}
	/**
	 * @return Returns the isTodayPlusOne.
	 */
	public boolean isTodayPlusOne() {
		return isTodayPlusOne;
	}
	/**
	 * @param isTodayPlusOne The isTodayPlusOne to set.
	 */
	public void setTodayPlusOne(boolean isTodayPlusOne) {
		this.isTodayPlusOne = isTodayPlusOne;
	}
	/**
	 * @return Returns the isTodayPlusTwo.
	 */
	public boolean isTodayPlusTwo() {
		return isTodayPlusTwo;
	}
	/**
	 * @param isTodayPlusTwo The isTodayPlusTwo to set.
	 */
	public void setTodayPlusTwo(boolean isTodayPlusTwo) {
		this.isTodayPlusTwo = isTodayPlusTwo;
	}

	/**
	 * @param id
	 * @param predictedBalance
	 * @param finalBalance
	 * @param predictedBalanceIn
	 * @param finalBalanceIn
	 * @param predictedBalanceOut
	 * @param finalBalanceOut
	 * @param startBalance
	 */
	public ManualSweepObject(Id id, Double predictedBalance,
			Double finalBalance, Double startBalance,Double targetBalance) {
		super();
		this.id = id;
		this.predictedBalance = predictedBalance;
		this.finalBalance = finalBalance;
		this.startBalance = startBalance;
		this.targetBalance = targetBalance ;
	}
	
	/**
	 * Containing Primary Key Getter setter
	 * <AUTHOR>
	 *
	 */
	public static class Id extends BaseObject implements Comparable{
		
		private String hostId ;
		private String entityId;
		private String accId;
		private Date valueDate; 
		private String valueDateAsString;	
		
		
		
		
		
				/**
		 * @param hostId
		 * @param entityId
		 * @param accCurrId
		 * @param valueDate
		 */
		public Id(String hostId, String entityId, String accId,
				Date valueDate) {
			super();
			this.hostId = hostId;
			this.entityId = entityId;
			this.accId = accId;
			this.valueDate = valueDate;
		}
		public Id() {
		}
	
	
		/**
		 * @return Returns the accCurrId.
		 */
		public String getAccId() {
			return accId;
		}
		/**
		 * @param accCurrId The accCurrId to set.
		 */
		public void setAccId(String accId) {
			this.accId = accId;
		}
		/**
		 * @return Returns the entityId.
		 */
		public String getEntityId() {
			return entityId;
		}
		/**
		 * @param entityId The entityId to set.
		 */
		public void setEntityId(String entityId) {
			this.entityId = entityId;
		}
		/**
		 * @return Returns the hostId.
		 */
		public String getHostId() {
			return hostId;
		}
		/**
		 * @param hostId The hostId to set.
		 */
		public void setHostId(String hostId) {
			this.hostId = hostId;
		}
		/**
		 * @return Returns the valueDate.
		 */
		public Date getValueDate() {
			return valueDate;
		}
		/**
		 * @param valueDate The valueDate to set.
		 */
		public void setValueDate(Date valueDate) {
			this.valueDate = valueDate;
		}
		
		public boolean equals(Object obj)
		{
			boolean retValue = false;
			
			if(obj != null && 
			   obj instanceof ManualSweepObject.Id) {
				
				ManualSweepObject.Id idObj = (ManualSweepObject.Id)obj; 
				if(hostId.equals(idObj.getHostId()) 
				   && entityId.equals(idObj.getEntityId())
				   && accId.equals(idObj.getAccId())
				   && valueDate.equals(idObj.getValueDate()) )
					{
						retValue = true;
					}
				
			}
			return retValue;
		}
		/**
		 * @return Returns the valueDateAsString.
		 */
		public String getValueDateAsString() {
			return valueDateAsString;
		}
		/**
		 * @param valueDateAsString The valueDateAsString to set.
		 */
		public void setValueDateAsString(String valueDateAsString) {
			this.valueDateAsString = valueDateAsString;
		}
		
		public int hashCode()		
		{
			int hashCode = -1;
			
			if(hostId != null)
				hashCode += hostId.hashCode();
			
			if(entityId != null)
				hashCode += entityId.hashCode();
			 
			if(accId != null)
				hashCode += accId.hashCode();
			
			if(valueDate != null)
				hashCode += valueDate.hashCode();
			
			return hashCode;
		}
		public int compareTo(Object o)
		{
			int retValue = 0;
			if(o != null && o instanceof ManualSweepObject.Id)
			{
				ManualSweepObject.Id inputObject = (ManualSweepObject.Id)o;
				
				if(this.getValueDate().before(inputObject.getValueDate()))
					retValue = -1;
				else if(this.getValueDate().after(inputObject.getValueDate()))
					retValue = 1;
			}			
			return retValue;
		}
		
			
		}
	public  void convertDateAmountIntoString()
	{
			
			Date valueDate = getId().getValueDate();
			String dateFormat = null;
			String currencyFormat = null;
			HttpSession session = UserThreadLocalHolder.getUserSession();
			if(session != null)
			{
				CommonDataManager cdm = (CommonDataManager)session.getAttribute(SwtConstants.CDM_BEAN);
				if(cdm != null)
				{
					dateFormat = cdm.getDateFormatValue();
					currencyFormat = cdm.getCurrencyFormatValue();
					log.debug("dateFormat - " + dateFormat + " , currencyFormat - " + currencyFormat);
				}
					
			}
			if( dateFormat != null)
			{
				String valueDateAsString = SwtUtil.formatDate(valueDate,dateFormat);
				getId().setValueDateAsString(valueDateAsString);
			}
			
			
		
			
			 			
			{			
			Double predictBalance = getPredictedBalance();
			if(predictBalance.doubleValue() < 0 )
				setPredictedBalanceNegative(true);
			
			setPredictedBalanceAsString(
					SwtUtil.formatCurrency(predictBalance,currencyFormat));
			}
			
			{
			Double finalBalance = getFinalBalance();
			if(finalBalance.doubleValue() < 0 )
				setFinalBalanceNegative(true);
			
			setFinalBalanceAsString(
					SwtUtil.formatCurrency(finalBalance,currencyFormat));
			}
			
			
			{
			Double startBalance = getStartBalance();
			if(startBalance.doubleValue() < 0 )
				setStartBalanceNegative(true);
			
			setStartBalanceAsString(
					SwtUtil.formatCurrency(startBalance,currencyFormat));
			}
			
					
	}
	public int compareTo(Object o)
	{
		log.debug("Inside compareTo function");
		log.debug("o - " + o.getClass());
		
		int retValue = 0;
		if(o != null && o instanceof ManualSweepObject)
		{
			ManualSweepObject inputObject = (ManualSweepObject)o;
			
			retValue = getId().getValueDate().compareTo(inputObject.getId().getValueDate());
			if(retValue == 0)
			{
				retValue = getId().getAccId().compareTo(inputObject.getId().getAccId());
			}
			
		}			
		return retValue;
	}
	
/**
 * @return Returns the currencyCode.
 */
public String getCurrencyCode() {
	return currencyCode;
}
/**
 * @param currencyCode The currencyCode to set.
 */
public void setCurrencyCode(String currencyCode) {
	this.currencyCode = currencyCode;
}
	/**
	 * @return Returns the targetBalance.
	 */
	public Double getTargetBalance() {
		return targetBalance;
	}
	/**
	 * @param targetBalance The targetBalance to set.
	 */
	public void setTargetBalance(Double targetBalance) {
		this.targetBalance = targetBalance;
	}
	/**
	 * @return Returns the targetBalanceNegative.
	 */
	public boolean isTargetBalanceNegative() {
		return targetBalanceNegative;
	}
	/**
	 * @param targetBalanceNegative The targetBalanceNegative to set.
	 */
	public void setTargetBalanceNegative(boolean targetBalanceNegative) {
		this.targetBalanceNegative = targetBalanceNegative;
	}
	/**
	 * @return Returns the targetBalanceAsString.
	 */
	public String getTargetBalanceAsString() {
		return targetBalanceAsString;
	}
	/**
	 * @param targetBalanceAsString The targetBalanceAsString to set.
	 */
	public void setTargetBalanceAsString(String targetBalanceAsString) {
		this.targetBalanceAsString = targetBalanceAsString;
	}
	/**
	 * @return Returns the acctType.
	 */
	public String getAcctType() {
		return acctType;
	}
	/**
	 * @param acctType The acctType to set.
	 */
	public void setAcctType(String acctType) {
		this.acctType = acctType;
	}
	/**
	 * @return Returns the targetBalSign.
	 */
	public String getTargetBalSign() {
		return targetBalSign;
	}
	/**
	 * @param targetBalSign The targetBalSign to set.
	 */
	public void setTargetBalSign(String targetBalSign) {
		this.targetBalSign = targetBalSign;
	}
	
	/**
	 * @return Returns the sweepAmount.
	 */
	public Double getSweepAmount() {
		return sweepAmount;
	}
	/**
	 * @param sweepAmount The sweepAmount to set.
	 */
	public void setSweepAmount(Double sweepAmount) {
		this.sweepAmount = sweepAmount;
	}
	/**
	 * @return Returns the sweepAmountAsString.
	 */
	public String getSweepAmountAsString() {
		return sweepAmountAsString;
	}
	/**
	 * @param sweepAmountAsString The sweepAmountAsString to set.
	 */
	public void setSweepAmountAsString(String sweepAmountAsString) {
		this.sweepAmountAsString = sweepAmountAsString;
	}
	/**
	 * @return Returns the displayLevel.
	 */
	public String getDisplayLevel() {
		return displayLevel;
	}
	/**
	 * @param displayLevel The displayLevel to set.
	 */
	public void setDisplayLevel(String displayLevel) {
		this.displayLevel = displayLevel;
	}
	/**
	 * @return Returns the cuttOff.
	 */
	public String getCuttOff() {
		return cuttOff;
	}
	/**
	 * @param cuttOff The cuttOff to set.
	 */
	public void setCuttOff(String cuttOff) {
		this.cuttOff = cuttOff;
	}
	/**
	 * @return Returns the mainAcctId.
	 */
	public String getMainAcctId() {
		return mainAcctId;
	}
	/**
	 * @param mainAcctId The mainAcctId to set.
	 */
	public void setMainAcctId(String mainAcctId) {
		this.mainAcctId = mainAcctId;
	}
	//Start:code modified by sandeepkumar for mantis 2039 - Visually indicate accounts past cut-off or subject to non-working day
	/**
	 * Getter method for isValueDateAchievable
	 * @return isValueDateAchievable
	 */
	public String getIsValueDateAchievable() {
		return isValueDateAchievable;
	}
	/**
	 * Setter method for isValueDateAchievable
	 * @param isValueDateAchievable
	 */
	public void setIsValueDateAchievable(String isValueDateAchievable) {
		this.isValueDateAchievable = isValueDateAchievable;
	}
	
	//End:code modified by sandeepkumar for mantis 2039 - Visually indicate accounts past cut-off or subject to non-working day
	
}

