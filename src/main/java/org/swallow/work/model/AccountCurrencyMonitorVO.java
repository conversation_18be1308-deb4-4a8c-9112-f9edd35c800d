/*
 * Created on Mar 11, 2006
 *
 * TODO To change the template for this generated file go to
 * Window - Preferences - Java - Code Style - Code Templates
 */
package org.swallow.work.model;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Collection;

/**
 * <AUTHOR>
 *
 * TODO To change the template for this generated type comment go to
 * Window - Preferences - Java - Code Style - Code Templates
 */
public class AccountCurrencyMonitorVO implements Serializable {

	public Collection todayMonitors ;
	public Collection todayPlusOneMonitors ;
	public Collection todayPlusTwoMonitors; 
	public Collection monthMonitors ;
	
	public AccountCurrencyMonitor todayFinalMonitor ;
	public AccountCurrencyMonitor todayPlusOneFinalMonitor ;	
	public AccountCurrencyMonitor todayPlusTwoFinalMonitor ;
	public AccountCurrencyMonitor monthFinalMonitor ;
	
	/**
	 * 
	 */
	public AccountCurrencyMonitorVO() {
		super();
		this.todayMonitors = new ArrayList();
		this.todayPlusOneMonitors =  new ArrayList();
		this.todayPlusTwoMonitors =  new ArrayList();
		this.monthMonitors =  new ArrayList();
		this.todayFinalMonitor = new AccountCurrencyMonitor();
		this.todayPlusOneFinalMonitor = new AccountCurrencyMonitor();
		this.todayPlusTwoFinalMonitor = new AccountCurrencyMonitor();
		this.monthFinalMonitor = new AccountCurrencyMonitor();

	}
	
	/**
	 * @param todayMonitors
	 * @param todayPlusOneMonitors
	 * @param todayPlusTwoMonitors
	 * @param monthMonitors
	 * @param todayFinalMonitor
	 * @param todayPlusOneFinalMonitor
	 * @param todayPlusTwoFinalMonitor
	 * @param monthFinalMonitor
	 */
	public AccountCurrencyMonitorVO(Collection todayMonitors,
			Collection todayPlusOneMonitors, Collection todayPlusTwoMonitors,
			Collection monthMonitors, AccountCurrencyMonitor todayFinalMonitor,
			AccountCurrencyMonitor todayPlusOneFinalMonitor,
			AccountCurrencyMonitor todayPlusTwoFinalMonitor,
			AccountCurrencyMonitor monthFinalMonitor) {
		super();
		this.todayMonitors = todayMonitors;
		this.todayPlusOneMonitors = todayPlusOneMonitors;
		this.todayPlusTwoMonitors = todayPlusTwoMonitors;
		this.monthMonitors = monthMonitors;
		this.todayFinalMonitor = todayFinalMonitor;
		this.todayPlusOneFinalMonitor = todayPlusOneFinalMonitor;
		this.todayPlusTwoFinalMonitor = todayPlusTwoFinalMonitor;
		this.monthFinalMonitor = monthFinalMonitor;
	}
	/**
	 * @return Returns the monthFinalMonitor.
	 */
	public AccountCurrencyMonitor getMonthFinalMonitor() {
		return monthFinalMonitor;
	}
	/**
	 * @param monthFinalMonitor The monthFinalMonitor to set.
	 */
	public void setMonthFinalMonitor(AccountCurrencyMonitor monthFinalMonitor) {
		this.monthFinalMonitor = monthFinalMonitor;
	}
	/**
	 * @return Returns the monthMonitors.
	 */
	public Collection getMonthMonitors() {
		return monthMonitors;
	}
	/**
	 * @param monthMonitors The monthMonitors to set.
	 */
	public void setMonthMonitors(Collection monthMonitors) {
		this.monthMonitors = monthMonitors;
	}
	/**
	 * @return Returns the todayFinalMonitor.
	 */
	public AccountCurrencyMonitor getTodayFinalMonitor() {
		return todayFinalMonitor;
	}
	/**
	 * @param todayFinalMonitor The todayFinalMonitor to set.
	 */
	public void setTodayFinalMonitor(AccountCurrencyMonitor todayFinalMonitor) {
		this.todayFinalMonitor = todayFinalMonitor;
	}
	/**
	 * @return Returns the todayMonitors.
	 */
	public Collection getTodayMonitors() {
		return todayMonitors;
	}
	/**
	 * @param todayMonitors The todayMonitors to set.
	 */
	public void setTodayMonitors(Collection todayMonitors) {
		this.todayMonitors = todayMonitors;
	}
	/**
	 * @return Returns the todayPlusOneFinalMonitor.
	 */
	public AccountCurrencyMonitor getTodayPlusOneFinalMonitor() {
		return todayPlusOneFinalMonitor;
	}
	/**
	 * @param todayPlusOneFinalMonitor The todayPlusOneFinalMonitor to set.
	 */
	public void setTodayPlusOneFinalMonitor(
			AccountCurrencyMonitor todayPlusOneFinalMonitor) {
		this.todayPlusOneFinalMonitor = todayPlusOneFinalMonitor;
	}
	/**
	 * @return Returns the todayPlusOneMonitors.
	 */
	public Collection getTodayPlusOneMonitors() {
		return todayPlusOneMonitors;
	}
	/**
	 * @param todayPlusOneMonitors The todayPlusOneMonitors to set.
	 */
	public void setTodayPlusOneMonitors(Collection todayPlusOneMonitors) {
		this.todayPlusOneMonitors = todayPlusOneMonitors;
	}
	/**
	 * @return Returns the todayPlusTwoFinalMonitor.
	 */
	public AccountCurrencyMonitor getTodayPlusTwoFinalMonitor() {
		return todayPlusTwoFinalMonitor;
	}
	/**
	 * @param todayPlusTwoFinalMonitor The todayPlusTwoFinalMonitor to set.
	 */
	public void setTodayPlusTwoFinalMonitor(
			AccountCurrencyMonitor todayPlusTwoFinalMonitor) {
		this.todayPlusTwoFinalMonitor = todayPlusTwoFinalMonitor;
	}
	/**
	 * @return Returns the todayPlusTwoMonitors.
	 */
	public Collection getTodayPlusTwoMonitors() {
		return todayPlusTwoMonitors;
	}
	/**
	 * @param todayPlusTwoMonitors The todayPlusTwoMonitors to set.
	 */
	public void setTodayPlusTwoMonitors(Collection todayPlusTwoMonitors) {
		this.todayPlusTwoMonitors = todayPlusTwoMonitors;
	}
}
