<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE hibernate-mapping PUBLIC
        "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
        "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">

<hibernate-mapping>
    <class name="org.swallow.work.model.SweepNote" table="P_SWEEP_NOTE">

		<composite-id name="id" class="org.swallow.work.model.SweepNote$Id" unsaved-value="any">
			  <key-property name="hostId" access="field" column="HOST_ID"/>
<!-- 			 <key-property name="entityId" access="field" column="ENTITY_ID" /> -->
			 <key-property name="sweepId" access="field" column="SWEEP_ID"/>
			 <key-property name="updateDate" access="field" column="UPDATE_DATE"/>
		</composite-id>
		
		<property name="noteText" column="NOTE_TEXT" not-null="false"/>	
		<property name="updateUser" column="UPDATE_USER" not-null="false"/>

	    </class>
</hibernate-mapping>
