/*
 * @(#)BookMonitorCurrencyDateTO .java 11/09/06
 * 
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 * 
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.work.model;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

import java.io.Serializable;
import java.util.Date;
public class BookMonitorCurrencyDateTO implements Serializable {
    /** stores the instance of log object */
    private final Log log = LogFactory.getLog(BookMonitorCurrencyDateTO.class);

    /**
     * entityId
     */
    private String entityId = "";

    /**
     * currCode
     */
    private String currCode = "";

    /**
     * date
     */
    private Date date;

    /**
     * Creates a new BookMonitorCurrencyDateTO object.
     *
     * @param entityId the entity id
     * @param currCode the currency code
     * @param date the date
     */
    public BookMonitorCurrencyDateTO(String entityId, String currCode, Date date) {
        this.entityId = entityId;
        this.currCode = currCode;
        this.date = date;
    }

    /**
     * This function overrides the equals function of Object class.
     * @param obj the object to check
     * @return boolean value indicating whether the value exists in cache
     */
    public boolean equals(Object obj) {
        log.debug("entering equals method");

        boolean retValue = false;

        if ((obj != null) && obj instanceof BookMonitorCurrencyDateTO) {
            log.debug("obj - " + obj);
            retValue = entityId.equals(((BookMonitorCurrencyDateTO) obj)
                    .getEntityId())
                && currCode.equals(((BookMonitorCurrencyDateTO) obj)
                    .getCurrCode())
                && date.equals(((BookMonitorCurrencyDateTO) obj).getDate());
        }

        log.debug("retValue - " + retValue);
        log.debug("exiting equals method");

        return retValue;
    }

    /**
     * This function overrides the hashCode function of Object class.
     * @return hashcode
     */
    public int hashCode() {
        return entityId.hashCode() + currCode.hashCode() + date.hashCode();
    }

    /**
     * @return Returns the currCode.
     */
    public String getCurrCode() {
        return currCode;
    }

    /**
     * @param currCode The currCode to set.
     */
    public void setCurrCode(String currCode) {
        this.currCode = currCode;
    }

    /**
     * @return Returns the date.
     */
    public Date getDate() {
        return date;
    }

    /**
     * @param date The date to set.
     */
    public void setDate(Date date) {
        this.date = date;
    }

    /**
     * @return Returns the entityId.
     */
    public String getEntityId() {
        return entityId;
    }

    /**
     * @param entityId The entityId to set.
     */
    public void setEntityId(String entityId) {
        this.entityId = entityId;
    }
}
