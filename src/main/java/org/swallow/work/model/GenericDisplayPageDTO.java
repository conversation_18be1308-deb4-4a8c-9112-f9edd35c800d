/*
 * @(#)InputExceptionsDataPageDTO.java 1.0 23/08/06
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */

package org.swallow.work.model;

import java.io.Serializable;

public class GenericDisplayPageDTO implements Serializable{
	/* Class variable declaration */
	private int totalSize = 0;
	private int pageNumber = 0;
	private int recordsPerPage = 0;
	private String fromDate = null;
	private String toDate = null;
	private String selectedFilter = null;
	private String selectedSort = null; 
	private String facilityRefColumns = null;
	private String scenarioRefColumns = null;
	private String scenarioRefParams = null;
	private String facilityRefParams = null;
	private int messageStatus = 0;
	private String orderByTag = null;
	private boolean allPages = false;
	

	
	
	/**
	 * @return the selectedSort
	 */
	public String getSelectedSort() {
		return selectedSort;
	}
		
	/**
	 * @param selectedSort the selectedSort to set
	 */
	public void setSelectedSort(String selectedSort) {
		this.selectedSort = selectedSort;
	}
	
	/**
	 * @return the selectedFilter
	 */
	public String getSelectedFilter() {
		return selectedFilter;
	}


	/**
	 * @param selectedFilter the selectedFilter to set
	 */
	public void setSelectedFilter(String selectedFilter) {
		this.selectedFilter = selectedFilter;
	}


	/**
	 * Getter Method for orderByTag
	 * @return String
	 */
	public String getOrderByTag() {
		return orderByTag;
	}

	
	/**
	 * Getter method for totalPages
	 * @return long
	 */
	public long getTotalPages () {
		Double rtn = Math.ceil (new Integer (totalSize).doubleValue()/new Integer (recordsPerPage).doubleValue()); 
		return rtn.longValue();
	}
	
	/**
	 * Setter method for orderByTag
	 * @param orderByTag
	 * @return
	 */
	public void setOrderByTag(String orderByTag) {
		this.orderByTag = orderByTag;
	}

	/**
	 * Setter method totalSize
	 * @param size
	 * @return
	 */
	public void setTotalSize (int size) {
		this.totalSize = size;
	}
	
	/**
	 * Getter method for totalSize
	 * @return int 
	 */
	public int getTotalSize () {
		return totalSize;
	}

	/**
	 * Getter method for recordsPerPage
	 * @return int
	 */
	public int getRecordsPerPage() {
		return recordsPerPage;
	}

	/**
	 * Setter method for recordsPerPage
	 * @param recordsPerPage
	 * @return
	 */
	public void setRecordsPerPage(int recordsPerPage) {
		this.recordsPerPage = recordsPerPage;
	}



	/**
	 * Getter method for fromDate
	 * @return String
	 */
	public String getFromDate() {
		return fromDate;
	}

	/**
	 * Setter method for fromDate
	 * @param fromDate
	 * @return
	 */
	public void setFromDate(String fromDate) {
		this.fromDate = fromDate;
	}

	/**
	 * Getter Method for toDate
	 * @return String
	 */
	public String getToDate() {
		return toDate;
	}

	/**
	 * Setter Method for toDate
	 * @param toDate
	 * @return
	 */
	public void setToDate(String toDate) {
		this.toDate = toDate;
	}

	/**
	 * Getter method for messageStatus
	 * @return int
	 */
	public int getMessageStatus() {
		return messageStatus;
	}

	/**
	 * Setter method for messageStatus
	 * @param messageStatus
	 * @return
	 */
	public void setMessageStatus(int messageStatus) {
		this.messageStatus = messageStatus;
	}


	/**
	 * Getter method for pageNumber
	 * @return int
	 */
	public int getPageNumber() {
		return pageNumber;
	}

	/**
	 * Setter method for pageNumber
	 * @param pageNumber
	 * @return
	 */
	public void setPageNumber(int pageNumber) {
		this.pageNumber = pageNumber;
	}

	/**
	 * @return the allPages
	 */
	public boolean isAllPages() {
		return allPages;
	}

	/**
	 * @param allPages the allPages to set
	 */
	public void setAllPages(boolean allPages) {
		this.allPages = allPages;
	}
	
	/**
	 * @return the facilityRefColumns
	 */
	public String getFacilityRefColumns() {
		return facilityRefColumns;
	}

	/**
	 * @param facilityRefColumns the facilityRefColumns to set
	 */
	public void setFacilityRefColumns(String facilityRefColumns) {
		this.facilityRefColumns = facilityRefColumns;
	}

	/**
	 * @return the scenarioRefColumns
	 */
	public String getScenarioRefColumns() {
		return scenarioRefColumns;
	}

	/**
	 * @param scenarioRefColumns the scenarioRefColumns to set
	 */
	public void setScenarioRefColumns(String scenarioRefColumns) {
		this.scenarioRefColumns = scenarioRefColumns;
	}
	
	/**
	 * @return the scenarioRefParams
	 */
	public String getScenarioRefParams() {
		return scenarioRefParams;
	}

	/**
	 * @param scenarioRefParams the scenarioRefParams to set
	 */
	public void setScenarioRefParams(String scenarioRefParams) {
		this.scenarioRefParams = scenarioRefParams;
	}

	/**
	 * @return the facilityRefParams
	 */
	public String getFacilityRefParams() {
		return facilityRefParams;
	}

	/**
	 * @param facilityRefParams the facilityRefParams to set
	 */
	public void setFacilityRefParams(String facilityRefParams) {
		this.facilityRefParams = facilityRefParams;
	}
	
	
}
