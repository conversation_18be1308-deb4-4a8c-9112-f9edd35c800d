<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE hibernate-mapping PUBLIC
        "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
        "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">
<hibernate-mapping>
 <class name="org.swallow.work.model.MovementLock" table="P_MOVEMENT_LOCK">
  
  
  <composite-id name="id" class="org.swallow.work.model.MovementLock$Id" unsaved-value="any">
			<key-property name="hostId" access="field" column="HOST_ID" />
	        <key-property name="movementId" access="field" column="MOVEMENT_ID"/>
	        <!-- Start code: Code added for Mantis 1221 to make ENTITY_ID as primary key 
	        			by <PERSON><PERSON><PERSON>  on 08-Sep-10 -->
	        <key-property name="entityId" access="field" column="ENTITY_ID"/>
	        <!-- End code: Code added for Mantis 1221 to make ENTITY_ID as primary key 
	        			by <PERSON><PERSON><PERSON>  on 08-Sep-10 -->
  </composite-id>
  <property name="currCode" column="CURRENCY_CODE"/>
  <property name="updateDate" column="UPDATE_DATE"/>
  <property name="updateUser" column="UPDATE_USER"/>
 </class>
</hibernate-mapping>
