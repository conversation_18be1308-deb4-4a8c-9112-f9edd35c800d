/*
 * @(#)MovementDetail.java  09/01/06
 * 
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 * 
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */

package org.swallow.work.model;
import org.swallow.maintenance.model.MsdDisplayColumns;
import org.swallow.model.BaseObject;
import org.swallow.model.MenuItem;
public class MsdAdditionalColumns extends BaseObject {

	private static final long serialVersionUID = 1L;
	private String sequenceKey;
	private String hostId;
	private String userId;
	private String profileId;
	private String label;
	private Long refCol;
	private MsdDisplayColumns msdDisplayColumns = new MsdDisplayColumns();
	

	public String getSequenceKey() {
		return sequenceKey;
	}
	public void setSequenceKey(String sequenceKey) {
		this.sequenceKey = sequenceKey;
	}
	public String getHostId() {
		return hostId;
	}
	public void setHostId(String hostId) {
		this.hostId = hostId;
	}
	
	
	public String getUserId() {
		return userId;
	}
	public void setUserId(String userId) {
		this.userId = userId;
	}
	public String getProfileId() {
		return profileId;
	}
	public void setProfileId(String profileId) {
		this.profileId = profileId;
	}
	
	public String getLabel() {
		return label;
	}
	public void setLabel(String label) {
		this.label = label;
	}
 
	public Long getRefCol() {
		return refCol;
	}
	public void setRefCol(Long refCol) {
		this.refCol = refCol;
	}
	public MsdDisplayColumns getMsdDisplayColumns() {
		return msdDisplayColumns;
	}
	public void setMsdDisplayColumns(MsdDisplayColumns msdDisplayColumns) {
		this.msdDisplayColumns = msdDisplayColumns;
	}
	
	

}
