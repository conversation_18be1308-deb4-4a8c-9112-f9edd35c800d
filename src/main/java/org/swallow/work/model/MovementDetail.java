/*
 * @(#)MovementDetail.java  09/01/06
 * 
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 * 
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */

package org.swallow.work.model;
import org.swallow.model.BaseObject;
public class MovementDetail extends BaseObject {

	private int pos1;
	private int pos9;
	private int posInt;
	private String entityName;
	private String matchQuality;
	private String matchStatus;
	private int matchIndex;
	private int totalMatches;
	private String notes;
	private int posLevelInternal;
	private int posLevelExternal;
	
	
	
	/**
	 * @return Returns the posLevelExternal.
	 */
	public int getPosLevelExternal() {
		return posLevelExternal;
	}
	/**
	 * @param posLevelExternal The posLevelExternal to set.
	 */
	public void setPosLevelExternal(int posLevelExternal) {
		this.posLevelExternal = posLevelExternal;
	}
	/**
	 * @return Returns the posLevelInternal.
	 */
	public int getPosLevelInternal() {
		return posLevelInternal;
	}
	/**
	 * @param posLevelInternal The posLevelInternal to set.
	 */
	public void setPosLevelInternal(int posLevelInternal) {
		this.posLevelInternal = posLevelInternal;
	}
	/**
	 * @return Returns the matchQuality.
	 */
	public String getMatchQuality() {
		return matchQuality;
	}
	/**
	 * @param matchQuality The matchQuality to set.
	 */
	public void setMatchQuality(String matchQuality) {
		this.matchQuality = matchQuality;
	}
	/**
	 * @return Returns the pos1.
	 */
	public int getPos1() {
		return pos1;
	}
	/**
	 * @param pos1 The pos1 to set.
	 */
	public void setPos1(int pos1) {
		this.pos1 = pos1;
	}
	/**
	 * @return Returns the pos9.
	 */
	public int getPos9() {
		return pos9;
	}
	/**
	 * @param pos9 The pos9 to set.
	 */
	public void setPos9(int pos9) {
		this.pos9 = pos9;
	}
	/**
	 * @return Returns the posInt.
	 */
	public int getPosInt() {
		return posInt;
	}
	/**
	 * @param posInt The posInt to set.
	 */
	public void setPosInt(int posInt) {
		this.posInt = posInt;
	}
	/**
	 * @return Returns the entityName.
	 */
	public String getEntityName() {
		return entityName;
	}
	/**
	 * @param entityName The entityName to set.
	 */
	public void setEntityName(String entityName) {
		this.entityName = entityName;
	}
	/**
	 * @return Returns the matchStatus.
	 */
	public String getMatchStatus() {
		return matchStatus;
	}
	/**
	 * @param matchStatus The matchStatus to set.
	 */
	public void setMatchStatus(String matchStatus) {
		this.matchStatus = matchStatus;
	}
	
	/**
	 * @return Returns the matchIndex.
	 */
	public int getMatchIndex() {
		return matchIndex;
	}
	/**
	 * @param matchIndex The matchIndex to set.
	 */
	public void setMatchIndex(int matchIndex) {
		this.matchIndex = matchIndex;
	}
	/**
	 * @return Returns the totalMatches.
	 */
	public int getTotalMatches() {
		return totalMatches;
	}
	/**
	 * @param totalMatches The totalMatches to set.
	 */
	public void setTotalMatches(int totalMatches) {
		this.totalMatches = totalMatches;
	}
	/**
	 * @return Returns the notes.
	 */
	public String getNotes() {
		return notes;
	}
	/**
	 * @param notes The notes to set.
	 */
	public void setNotes(String notes) {
		this.notes = notes;
	}
}
