/*
 * @(#)FilterMSDConfig.java 31/01/2018
 * 
 * Copyright (c) 2006-2018 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 * 
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.work.model;

import java.io.Serializable;

public class FilterMSDConfig implements Serializable{
	
	private String source = null;
	private String amountFrom = null;
	private String amountTo = null;
	private String valueDateFrom = null;
	private String valueDateTo = null;
	private String group = null;
	private String metagroup = null;
	private String inputDate = null;
	private String inputTimeFrom = null;
	private String inputTimeTo = null;
	private String reference = null;
	private String includeOpen = null;
	private String filterAccntType = null;
	private String dateFormat = null; 
	private String searchDateBehaviour = null;
	private int diffrenceFromDate;
	private int diffrenceToDate;
	
	
	
	
	public FilterMSDConfig() {
		super();
	}

	public FilterMSDConfig(String source, String amountFrom, String amountTo,
			String valueDateFrom, String valueDateTo, String group,
			String metagroup, String inputDate, String inputTimeFrom,
			String inputTimeTo, String reference, String includeOpen,
			String filterAccntType, String dateFormat, String searchDateBehaviour) {
		super();
		this.source = source;
		this.amountFrom = amountFrom;
		this.amountTo = amountTo;
		this.valueDateFrom = valueDateFrom;
		this.valueDateTo = valueDateTo;
		this.group = group;
		this.metagroup = metagroup;
		this.inputDate = inputDate;
		this.inputTimeFrom = inputTimeFrom;
		this.inputTimeTo = inputTimeTo;
		this.reference = reference;
		this.includeOpen = includeOpen;
		this.filterAccntType = filterAccntType;
		this.searchDateBehaviour = searchDateBehaviour;
		this.dateFormat = dateFormat;
	}
	
	
	
	/**
	 * @return the source
	 */
	public String getSource() {
		return source;
	}

	/**
	 * @param source the source to set
	 */
	public void setSource(String source) {
		this.source = source;
	}

	/**
	 * @return the amountFrom
	 */
	public String getAmountFrom() {
		return amountFrom;
	}
	/**
	 * @param amountFrom the amountFrom to set
	 */
	public void setAmountFrom(String amountFrom) {
		this.amountFrom = amountFrom;
	}
	/**
	 * @return the amountTo
	 */
	public String getAmountTo() {
		return amountTo;
	}
	/**
	 * @param amountTo the amountTo to set
	 */
	public void setAmountTo(String amountTo) {
		this.amountTo = amountTo;
	}
	/**
	 * @return the valueDateFrom
	 */
	public String getValueDateFrom() {
		return valueDateFrom;
	}
	/**
	 * @param valueDateFrom the valueDateFrom to set
	 */
	public void setValueDateFrom(String valueDateFrom) {
		this.valueDateFrom = valueDateFrom;
	}
	/**
	 * @return the valueDateTo
	 */
	public String getValueDateTo() {
		return valueDateTo;
	}
	/**
	 * @param valueDateTo the valueDateTo to set
	 */
	public void setValueDateTo(String valueDateTo) {
		this.valueDateTo = valueDateTo;
	}
	/**
	 * @return the group
	 */
	public String getGroup() {
		return group;
	}
	/**
	 * @param group the group to set
	 */
	public void setGroup(String group) {
		this.group = group;
	}
	/**
	 * @return the metagroup
	 */
	public String getMetagroup() {
		return metagroup;
	}
	/**
	 * @param metagroup the metagroup to set
	 */
	public void setMetagroup(String metagroup) {
		this.metagroup = metagroup;
	}
	/**
	 * @return the inputDate
	 */
	public String getInputDate() {
		return inputDate;
	}
	/**
	 * @param inputDate the inputDate to set
	 */
	public void setInputDate(String inputDate) {
		this.inputDate = inputDate;
	}
	/**
	 * @return the inputTimeFrom
	 */
	public String getInputTimeFrom() {
		return inputTimeFrom;
	}
	/**
	 * @param inputTimeFrom the inputTimeFrom to set
	 */
	public void setInputTimeFrom(String inputTimeFrom) {
		this.inputTimeFrom = inputTimeFrom;
	}
	/**
	 * @return the inputTimeTo
	 */
	public String getInputTimeTo() {
		return inputTimeTo;
	}
	/**
	 * @param inputTimeTo the inputTimeTo to set
	 */
	public void setInputTimeTo(String inputTimeTo) {
		this.inputTimeTo = inputTimeTo;
	}
	/**
	 * @return the reference
	 */
	public String getReference() {
		return reference;
	}
	/**
	 * @param reference the reference to set
	 */
	public void setReference(String reference) {
		this.reference = reference;
	}
	/**
	 * @return the includeOpen
	 */
	public String getIncludeOpen() {
		return includeOpen;
	}
	/**
	 * @param includeOpen the includeOpen to set
	 */
	public void setIncludeOpen(String includeOpen) {
		this.includeOpen = includeOpen;
	}
	/**
	 * @return the filterAccntType
	 */
	public String getFilterAccntType() {
		return filterAccntType;
	}
	/**
	 * @param filterAccntType the filterAccntType to set
	 */
	public void setFilterAccntType(String filterAccntType) {
		this.filterAccntType = filterAccntType;
	}

	/**
	 * @return the dateFormat
	 */
	public String getDateFormat() {
		return dateFormat;
	}

	/**
	 * @param dateFormat the dateFormat to set
	 */
	public void setDateFormat(String dateFormat) {
		this.dateFormat = dateFormat;
	}

	/**
	 * @return the searchDateBehaviour
	 */
	public String getSearchDateBehaviour() {
		return searchDateBehaviour;
	}

	/**
	 * @param searchDateBehaviour the searchDateBehaviour to set
	 */
	public void setSearchDateBehaviour(String searchDateBehaviour) {
		this.searchDateBehaviour = searchDateBehaviour;
	}

	/**
	 * @return the diffrenceFromDate
	 */
	public int getDiffrenceFromDate() {
		return diffrenceFromDate;
	}

	/**
	 * @param diffrenceFromDate the diffrenceFromDate to set
	 */
	public void setDiffrenceFromDate(int diffrenceFromDate) {
		this.diffrenceFromDate = diffrenceFromDate;
	}

	/**
	 * @return the diffrenceToDate
	 */
	public int getDiffrenceToDate() {
		return diffrenceToDate;
	}

	/**
	 * @param diffrenceToDate the diffrenceToDate to set
	 */
	public void setDiffrenceToDate(int diffrenceToDate) {
		this.diffrenceToDate = diffrenceToDate;
	}
	
	
}
