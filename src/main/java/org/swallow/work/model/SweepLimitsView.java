/*
 * Created on Mar 21, 2018
 *
 * TODO To change the template for this generated file go to
 * Window - Preferences - Java - Code Style - Code Templates
 */
package org.swallow.work.model;
import java.math.BigDecimal;
import java.util.Date;
import java.util.Hashtable;

import org.apache.commons.lang.builder.EqualsBuilder;
import org.swallow.model.BaseObject;
/**
 * <AUTHOR>
 *
 * TODO To change the template for this generated type comment go to
 * Window - Preferences - Java - Code Style - Code Templates
 */
public class SweepLimitsView extends BaseObject implements org.swallow.model.AuditComponent{
	private Id id = new Id();
	private Double sweepLimit;
	private BigDecimal sweepLimitBig;
	private String sweepLimitAsString;
	private String sweepLimitAsStringAnother;//This variable temporarily holds the unformatted currency
	
	public static Hashtable  logTable = new Hashtable();
	static {
		logTable.put("sweepLimit","Sweep Limit");
	}
	
	
	
	
	/**
	 * @return Returns the sweepLimitAsStringAnother.
	 */
	public String getSweepLimitAsStringAnother() {
		return sweepLimitAsStringAnother;
	}
	/**
	 * @param sweepLimitAsStringAnother The sweepLimitAsStringAnother to set.
	 */
	public void setSweepLimitAsStringAnother(String sweepLimitAsStringAnother) {
		this.sweepLimitAsStringAnother = sweepLimitAsStringAnother;
	}
	/**
	 * @return Returns the sweepLimitAsString.
	 */
	public String getSweepLimitAsString() {
		return sweepLimitAsString;
	}
	/**
	 * @param sweepLimitAsString The sweepLimitAsString to set.
	 */
	public void setSweepLimitAsString(String sweepLimitAsString) {
		this.sweepLimitAsString = sweepLimitAsString;
	}
	private Date updateDate = new Date();
	private String updateUser;
	
	
	
	
//	/**
//	 * @param id
//	 * @param sweepLimit
//	 * @param updateDate
//	 * @param updateUser
//	 */
//	public SweepLimitsView(Id id, double sweepLimit, Date updateDate,
//			String updateUser) {
//		super();
//		this.id = id;
//		this.sweepLimit = sweepLimit;
//		this.updateDate = updateDate;
//		this.updateUser = updateUser;
//	}
	public static class Id extends BaseObject{
		
		private String roleId;
		private String currencyCode;
		
		public Id(String roleId ,String currencyCode ) {
			this.currencyCode = currencyCode;
			 
			this.roleId = roleId;
			 
			
		}
		/**
		 * @return Returns the currencyCode.
		 */
		public String getCurrencyCode() {
			return currencyCode;
		}
		/**
		 * @param currencyCode The currencyCode to set.
		 */
		public void setCurrencyCode(String currencyCode) {
			this.currencyCode = currencyCode;
		}
		/**
		 * @return Returns the roleId.
		 */
		public String getRoleId() {
			return roleId;
		}
		/**
		 * @param roleId The roleId to set.
		 */
		public void setRoleId(String roleId) {
			this.roleId = roleId;
		}
		public Id() {}

	}



	/**
	 * @return Returns the id.
	 */
	public Id getId() {
		return id;
	}
	/**
	 * @param id The id to set.
	 */
	public void setId(Id id) {
		this.id = id;
	}
	
	public Double getSweepLimit() {
		return sweepLimit;
	}
	public void setSweepLimit(Double sweepLimit) {
		this.sweepLimit = sweepLimit;
	}
	/**
	 * @return Returns the sweepLimitBig.
	 */
	public BigDecimal getSweepLimitBig() {
		return sweepLimitBig;
	}
	/**
	 * @param sweepLimitBig The sweepLimitBig to set.
	 */
	public void setSweepLimitBig(BigDecimal sweepLimitBig) {
		this.sweepLimit = new Double(sweepLimitBig.doubleValue());
		this.sweepLimitBig = sweepLimitBig;
	}
	/**
	 * @return Returns the updateDate.
	 */
	public Date getUpdateDate() {
		return updateDate;
	}
	/**
	 * @param updateDate The updateDate to set.
	 */
	public void setUpdateDate(Date updateDate) {
		this.updateDate = updateDate;
	}
	/**
	 * @return Returns the updateUser.
	 */
	public String getUpdateUser() {
		return updateUser;
	}
	/**
	 * @param updateUser The updateUser to set.
	 */
	public void setUpdateUser(String updateUser) {
		this.updateUser = updateUser;
	}
	
	public boolean equals(Object objCurr, Object objIni){
		return EqualsBuilder.reflectionEquals(objCurr, objIni);
	}
}
