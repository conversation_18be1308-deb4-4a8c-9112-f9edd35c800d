<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE hibernate-mapping PUBLIC
        "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
        "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">

<hibernate-mapping>
	<class name="org.swallow.work.model.UserBuckets"
		table="P_FCAST_USERBUCKETS">
		<composite-id
			class="org.swallow.work.model.UserBuckets$Id" name="id"
			unsaved-value="any">
			<key-property name="hostId" access="field"
				column="HOST_ID" />
			<key-property name="userId" access="field"
				column="USER_ID" />
			<key-property name="bucketId" access="field"
				 column="BUCKET_ID" />
		</composite-id>
		<property name="daysTo" column="DAYS_TO" not-null="false" />
		<property name="bucketState" column="BUCKET_STATE" not-null="false" />
	</class>
</hibernate-mapping>
