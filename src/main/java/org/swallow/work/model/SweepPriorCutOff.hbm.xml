<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE hibernate-mapping PUBLIC
        "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
        "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">

<hibernate-mapping>
    <class name="org.swallow.work.model.SweepPriorCutOff" table="P_ACCOUNT">
		<composite-id name="id" class="org.swallow.work.model.SweepPriorCutOff$Id" unsaved-value="any">

        <key-property name="hostId" access="field" column="HOST_ID"/>
        <key-property name="entityId" access="field" column="ENTITY_ID" />
        <key-property name="accountId" access="field" column="ACCOUNT_ID" />	
	</composite-id>
	
		
		<property name="acctname" column="ACCOUNT_NAME" not-null="true"/>
		<property name="currcode" column="CURRENCY_CODE" not-null="false"/>
		<property name="cutOff" column="CUT_OFF" not-null="true"/>
		<property name="acctType" column="ACCOUNT_TYPE" not-null="false"/>
		<property name="targetBalance" column="TARGET_BALANCE" not-null="false"/>
		<property name="minSweepAmt" column="MIN_SWEEP_AMOUNT" not-null="false"/>
		<property name="manualSweepFlag" column="MANUAL_SWEEP_FLAG" not-null="false"/>
		<property name="autoSweepSwitchFlag" column="AUTO_SWEEP_SWITCH" not-null="false"/>
		<property name="sweepDays" column="SWEEP_DAYS" not-null="false"/>
		
    </class>
</hibernate-mapping>