/*
 * @(#)UserTemplate.java 1.0 23/05/11
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.work.model;

import org.swallow.model.BaseObject;

/**
 * UserTemplate.java
 * 
 * This java bean has getters and setters for User Template details
 * 
 * <AUTHOR>
 * @date May 23, 2011
 * 
 */
public class UserTemplate extends BaseObject {

	/** Default version id */
	private static final long serialVersionUID = 1L;
	// template Id
	private String templateId = null;
	// Denote id
	private Id id = new Id();

	public static class Id extends BaseObject {
		/** Default version id */
		private static final long serialVersionUID = 1L;
		// host Id
		private String hostId = null;
		// user Id
		private String userId = null;
		// currency code
		private String currencyCode = null;
		// entity Id
		private String entityId = null;

		/**
		 * Default constructor
		 */
		public Id() {
		}

		/**
		 * Getter method for hostId
		 * 
		 * @return hostId as String
		 */

		public String getHostId() {
			return hostId;
		}

		/**
		 * Setter method for hostId
		 * 
		 * @param hostId
		 */

		public void setHostId(String hostId) {
			this.hostId = hostId;
		}

		/**
		 * Getter method for userId
		 * 
		 * @return userId as String
		 */

		public String getUserId() {
			return userId;
		}

		/**
		 * Setter method for userId
		 * 
		 * @param userId
		 */

		public void setUserId(String userId) {
			this.userId = userId;
		}

		/**
		 * Getter method for currencyCode
		 * 
		 * @return currencyCode as String
		 */

		public String getCurrencyCode() {
			return currencyCode;
		}

		/**
		 * Setter method for currencyCode
		 * 
		 * @param currencyCode
		 */

		public void setCurrencyCode(String currencyCode) {
			this.currencyCode = currencyCode;
		}

		/**
		 * Getter method for entityId
		 * 
		 * @return entityId as String
		 */

		public String getEntityId() {
			return entityId;
		}

		/**
		 * Setter method for entityId
		 * 
		 * @param entityId
		 */

		public void setEntityId(String entityId) {
			this.entityId = entityId;
		}

	}

	/**
	 * Getter method for id
	 * 
	 * @return id as Id
	 */

	public Id getId() {
		return id;
	}

	/**
	 * Setter method for id
	 * 
	 * @param id
	 */

	public void setId(Id id) {
		this.id = id;
	}

	/**
	 * Getter method for templateId
	 * 
	 * @return templateId as String
	 */

	public String getTemplateId() {
		return templateId;
	}

	/**
	 * Setter method for templateId
	 * 
	 * @param templateId
	 */

	public void setTemplateId(String templateId) {
		this.templateId = templateId;
	}

}