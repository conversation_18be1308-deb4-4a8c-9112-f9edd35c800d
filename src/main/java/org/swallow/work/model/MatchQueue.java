/*
 * @(#)MatchQueue.java  03/01/06
 * 
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 * 
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */

package org.swallow.work.model;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

import org.swallow.model.BaseObject;

public class MatchQueue extends BaseObject {

	private String currencyCode;
	private String status;
	private String entityId;
	private String currencyName;
	private String matchIdsA;
	private String matchIdsB;
	private String matchIdsC;
	private String matchIdsD;
	private String matchIdsE;
	private String matchIdsZ;
	private String qualityA;
	private String qualityB;
	private String qualityC;
	private String qualityD;
	private String qualityE;
	private String qualityZ;
	private Map<String,String>    summaryA;
	private Map<String,String>    summaryB;
	private Map<String,String>    summaryC;
	private Map<String,String>    summaryD;
	private Map<String,String>    summaryE;
	private Map<String,String>    summaryZ;
	private Date valuedate;
	private String day;
	//added for currency group code
	private String currencyGrpCode;
    /* START: Code changed as par SRS - Currency Threshold for ING, 06-JUL-2007 */
	private String applyCurrencyThreshold;
    /* END: Code changed as par SRS - Currency Threshold for ING, 06-JUL-2007 */

    /* START: Code changed as par SRS - Match Exceptions for ING, 13-NOV-2007 */
	private String noIncludedMovementMatches;
    /* END: Code changed as par SRS - Match Exceptions for ING, 13-NOV-2007 */

	/* Start: Code added by Bala on 28092010 for Mantis 1209 */
	// variable to hold valueDateAsString
	private String valueDateAsString;
	/* End: Code added by Bala on 28092010 for Mantis 1209 */
	
	/**
	 * @return Returns the currencyCode.
	 */
	public String getCurrencyCode() {
		return currencyCode;
	}
	/**
	 * @param currencyCode The currencyCode to set.
	 */
	public void setCurrencyCode(String currencyCode) {
		this.currencyCode = currencyCode;
	}
	/**
	 * @return Returns the currencyName.
	 */
	public String getCurrencyName() {
		return currencyName;
	}
	/**
	 * @param currencyName The currencyName to set.
	 */
	public void setCurrencyName(String currencyName) {
		this.currencyName = currencyName;
	}
	/**
	 * @return Returns the qualityA.
	 */
	public String getQualityA() {
		return qualityA;
	}
	/**
	 * @param qualityA The qualityA to set.
	 */
	public void setQualityA(String qualityA) {
		this.qualityA = qualityA;
	}
	/**
	 * @return Returns the qualityB.
	 */
	public String getQualityB() {
		return qualityB;
	}
	/**
	 * @param qualityB The qualityB to set.
	 */
	public void setQualityB(String qualityB) {
		this.qualityB = qualityB;
	}
	/**
	 * @return Returns the qualityC.
	 */
	public String getQualityC() {
		return qualityC;
	}
	/**
	 * @param qualityC The qualityC to set.
	 */
	public void setQualityC(String qualityC) {
		this.qualityC = qualityC;
	}
	/**
	 * @return Returns the qualityD.
	 */
	public String getQualityD() {
		return qualityD;
	}
	/**
	 * @param qualityD The qualityD to set.
	 */
	public void setQualityD(String qualityD) {
		this.qualityD = qualityD;
	}
	/**
	 * @return Returns the qualityE.
	 */
	public String getQualityE() {
		return qualityE;
	}
	/**
	 * @param qualityE The qualityE to set.
	 */
	public void setQualityE(String qualityE) {
		this.qualityE = qualityE;
	}
	/**
	 * @return Returns the qualityZ.
	 */
	public String getQualityZ() {
		return qualityZ;
	}
	/**
	 * @param qualityZ The qualityZ to set.
	 */
	public void setQualityZ(String qualityZ) {
		this.qualityZ = qualityZ;
	}
	/**
	 * @return Returns the matchIdsA.
	 */
	public String getMatchIdsA() {
		return matchIdsA;
	}
	/**
	 * @param matchIdsA The matchIdsA to set.
	 */
	public void setMatchIdsA(String matchIdsA) {
		this.matchIdsA = matchIdsA;
	}

	/**
	 * @return Returns the entityId.
	 */
	public String getEntityId() {
		return entityId;
	}
	/**
	 * @param entityId The entityId to set.
	 */
	public void setEntityId(String entityId) {
		this.entityId = entityId;
	}
	/**
	 * @return Returns the matchIdsB.
	 */
	public String getMatchIdsB() {
		return matchIdsB;
	}
	/**
	 * @param matchIdsB The matchIdsB to set.
	 */
	public void setMatchIdsB(String matchIdsB) {
		this.matchIdsB = matchIdsB;
	}

	/**
	 * @return Returns the matchIdsE.
	 */
	public String getMatchIdsE() {
		return matchIdsE;
	}
	/**
	 * @param matchIdsE The matchIdsE to set.
	 */
	public void setMatchIdsE(String matchIdsE) {
		this.matchIdsE = matchIdsE;
	}
	/**
	 * @return Returns the matchIdsZ.
	 */
	public String getMatchIdsZ() {
		return matchIdsZ;
	}
	/**
	 * @param matchIdsZ The matchIdsZ to set.
	 */
	public void setMatchIdsZ(String matchIdsZ) {
		this.matchIdsZ = matchIdsZ;
	}
	/**
	 * @return Returns the matchIdsC.
	 */
	public String getMatchIdsC() {
		return matchIdsC;
	}
	/**
	 * @param matchIdsC The matchIdsC to set.
	 */
	public void setMatchIdsC(String matchIdsC) {
		this.matchIdsC = matchIdsC;
	}
	/**
	 * @return Returns the matchIdsD.
	 */
	public String getMatchIdsD() {
		return matchIdsD;
	}
	/**
	 * @param matchIdsD The matchIdsD to set.
	 */
	public void setMatchIdsD(String matchIdsD) {
		this.matchIdsD = matchIdsD;
	}
	/**
	 * @return Returns the summaryA.
	 */
	public Map getSummaryA() {
		summaryA = new  HashMap();
		summaryA.put("currencyCode",currencyCode);
		summaryA.put("matchCount",qualityA);
		summaryA.put("entityId",entityId);
		summaryA.put("status",status);
		summaryA.put("quality","A");
		summaryA.put("date",""+valuedate);
		summaryA.put("day",day);
		return summaryA;
	}
	/**
	 * @param summaryA The summaryA to set.
	 */
	public void setSummaryA(Map summaryA) {
		this.summaryA = summaryA;
	}
	/**
	 * @return Returns the summaryB.
	 */
	public Map getSummaryB() {
		summaryB = new  HashMap();
		summaryB.put("currencyCode",currencyCode);
		summaryB.put("matchCount",qualityB);
		summaryB.put("entityId",entityId);
		summaryB.put("status",status);
		summaryB.put("quality","B");
		summaryB.put("date",""+valuedate);
		summaryB.put("day",day);
		return summaryB;
	}
	/**
	 * @param summaryB The summaryB to set.
	 */
	public void setSummaryB(Map summaryB) {
		this.summaryB = summaryB;
	}
	/**
	 * @return Returns the summaryC.
	 */
	public Map getSummaryC() {
		summaryC = new  HashMap();
		summaryC.put("currencyCode",currencyCode);
		summaryC.put("matchCount",qualityC);
		summaryC.put("entityId",entityId);
		summaryC.put("status",status);
		summaryC.put("quality","C");
		summaryC.put("date",""+valuedate);
		summaryC.put("day",day);
		return summaryC;
	}
	/**
	 * @param summaryC The summaryC to set.
	 */
	public void setSummaryC(Map summaryC) {
		this.summaryC = summaryC;
	}
	/**
	 * @return Returns the summaryD.
	 */
	public Map getSummaryD() {
		summaryD = new  HashMap();
		summaryD.put("currencyCode",currencyCode);
		summaryD.put("matchCount",qualityD);
		summaryD.put("entityId",entityId);
		summaryD.put("status",status);
		summaryD.put("quality","D");
		summaryD.put("date",""+valuedate);
		summaryD.put("day",day);
		return summaryD;
	}
	/**
	 * @param summaryD The summaryD to set.
	 */
	public void setSummaryD(Map summaryD) {
		this.summaryD = summaryD;
	}
	/**
	 * @return Returns the summaryE.
	 */
	public Map getSummaryE() {
		summaryE = new  HashMap();
		summaryE.put("currencyCode",currencyCode);
		summaryE.put("matchCount",qualityE);
		summaryE.put("entityId",entityId);
		summaryE.put("status",status);
		summaryE.put("quality","E");
		summaryE.put("date",""+valuedate);
		summaryE.put("day",day);
		return summaryE;
	}
	/**
	 * @param summaryE The summaryE to set.
	 */
	public void setSummaryE(Map summaryE) {
		this.summaryE = summaryE;
	}
	/**
	 * @return Returns the summaryZ.
	 */
	public Map getSummaryZ() {
		summaryZ = new  HashMap();
		summaryZ.put("currencyCode",currencyCode);
		summaryZ.put("matchCount",qualityZ);
		summaryZ.put("entityId",entityId);
		summaryZ.put("status",status);
		summaryZ.put("quality","Z");
		summaryZ.put("date",""+valuedate);
		summaryZ.put("day",day);
		return summaryZ;
	}
	/**
	 * @param summaryZ The summaryZ to set.
	 */
	public void setSummaryZ(Map summaryZ) {
		this.summaryZ = summaryZ;
	}

	private String summaryAurlParams = new String();
	public String getSummaryAurlParams() {
		getSummaryA();
		summaryAurlParams =  summaryA.entrySet().stream()
				.map(p -> p.getKey() + "=" + p.getValue())
				.reduce((p1, p2) -> p1 + "&" + p2)
				.map(s -> "" + s)
				.orElse("");
		return summaryAurlParams;
	}
	private String summaryBurlParams = new String();
	public String getSummaryBurlParams() {
		getSummaryB();
		summaryBurlParams =  summaryB.entrySet().stream()
				.map(p -> p.getKey() + "=" + p.getValue())
				.reduce((p1, p2) -> p1 + "&" + p2)
				.map(s -> "" + s)
				.orElse("");
		return summaryBurlParams;
	}
	private String summaryCurlParams = new String();
	public String getSummaryCurlParams() {
		getSummaryC();
		summaryCurlParams =  summaryC.entrySet().stream()
				.map(p -> p.getKey() + "=" + p.getValue())
				.reduce((p1, p2) -> p1 + "&" + p2)
				.map(s -> "" + s)
				.orElse("");
		return summaryCurlParams;
	}
	private String summaryDurlParams = new String();
	public String getSummaryDurlParams() {
		getSummaryD();
		summaryDurlParams =  summaryD.entrySet().stream()
				.map(p -> p.getKey() + "=" + p.getValue())
				.reduce((p1, p2) -> p1 + "&" + p2)
				.map(s -> "" + s)
				.orElse("");
		return summaryDurlParams;
	}

	private String summaryEurlParams = new String();
	public String getSummaryEurlParams() {
		getSummaryE();
		summaryEurlParams =  summaryE.entrySet().stream()
				.map(p -> p.getKey() + "=" + p.getValue())
				.reduce((p1, p2) -> p1 + "&" + p2)
				.map(s -> "" + s)
				.orElse("");
		return summaryEurlParams;
	}


	private String summaryZurlParams = new String();
	public String getSummaryZurlParams() {
		getSummaryZ();
		summaryZurlParams =  summaryZ.entrySet().stream()
				.map(p -> p.getKey() + "=" + p.getValue())
				.reduce((p1, p2) -> p1 + "&" + p2)
				.map(s -> "" + s)
				.orElse("");
		return summaryZurlParams;
	}


	/**
	 * @return Returns the status.
	 */
	public String getStatus() {
		return status;
	}
	/**
	 * @param status The status to set.
	 */
	public void setStatus(String status) {
		this.status = status;
	}
	/**
	 * @return Returns the day.
	 */
	public String getDay() {
		return day;
	}
	/**
	 * @param day The day to set.
	 */
	public void setDay(String day) {
		this.day = day;
	}
	/**
	 * @return Returns the valuedate.
	 */
	public Date getValuedate() {
		return valuedate;
	}
	/**
	 * @param valuedate The valuedate to set.
	 */
	public void setValuedate(Date valuedate) {
		this.valuedate = valuedate;
	}
	//added  for the currency group Code
	/**
	 * @return Returns the currencyGrpCode.
	 */
	public String getCurrencyGrpCode() {
		return currencyGrpCode;
	}
	/**
	 * @param currencyGrpCode The currencyGrpCode to set.
	 */
	public void setCurrencyGrpCode(String currencyGrpCode) {
		this.currencyGrpCode = currencyGrpCode;
	}

    /* START: Code changed as par SRS - Currency Threshold for ING, 06-JUL-2007 */
	/**
	 * @return Returns the applyCurrencyThreshold.
	 */
	public String getApplyCurrencyThreshold() {
		return applyCurrencyThreshold;
	}
	/**
	 * @param applyCurrencyThreshold The applyCurrencyThreshold to set.
	 */
	public void setApplyCurrencyThreshold(String applyCurrencyThreshold) {
		this.applyCurrencyThreshold = applyCurrencyThreshold;
	}
    /* END: Code changed as par SRS - Currency Threshold for ING, 06-JUL-2007 */

    /* START: Code changed as par SRS - Match Exceptions for ING, 13-NOV-2007 */
	/**
	 * @return Returns the noIncludedMovementMatches.
	 */
	public String getNoIncludedMovementMatches() {
		return noIncludedMovementMatches;
	}
	/**
	 * @param noIncludedMovementMatches The noIncludedMovementMatches to set.
	 */
	public void setNoIncludedMovementMatches(String noIncludedMovementMatches) {
		this.noIncludedMovementMatches = noIncludedMovementMatches;
	}
    /* END: Code changed as par SRS - Match Exceptions for ING, 13-NOV-2007 */
	
	/* Start: Code added by Bala on 28092010 for Mantis 1209 */
	/**
	 * @return the valueDateAsString
	 */
	public String getValueDateAsString() {
		return valueDateAsString;
	}
	/**
	 * @param valueDateAsString the valueDateAsString to set
	 */
	public void setValueDateAsString(String valueDateAsString) {
		this.valueDateAsString = valueDateAsString;
	}
	/* End: Code added by Bala on 28092010 for Mantis 1209 */
}
