/*
 * Created on Aug 28, 2006
 *
 * TODO To change the template for this generated file go to
 * Window - Preferences - Java - Code Style - Code Templates
 */
package org.swallow.work.model;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 *
 * TODO To change the template for this generated type comment go to
 * Window - Preferences - Java - Code Style - Code Templates
 */
public class OpenMovementSummary implements Serializable{
	//private String entityId ;
	private String currencyCode;
	private String currencyName;
	//private String date;
	private List posLvlNames;
	//private Map summaryTotal;

	/**
	 * @return
	 */
	public String getCurrencyCode() {
		return currencyCode;
	}
	/**
	 * @param currencyCode
	 */
	public void setCurrencyCode(String currencyCode) {
		this.currencyCode = currencyCode;
	}
	/**
	 * @return
	 */
	public String getCurrencyName() {
		return currencyName;
	}
	/**
	 * @param currencyName
	 */
	public void setCurrencyName(String currencyName) {
		this.currencyName = currencyName;
	}
	/**
	 * @return
	 */
//	public String getDate() {
//		return date;
//	}
	/**
	 * @param date
	 */
//	public void setDate(String date) {
//		this.date = date;
//	}
	/**
	 * @return
	 */
	public List getPosLvlNames() {
		return posLvlNames;
	}
	/**
	 * @param posLvlNames - List 
	 */
	public void setPosLvlNames(List posLvlNames) {
		this.posLvlNames = posLvlNames;
	}
	/**
	 * @return Map
	 */
//	public Map getSummaryTotal() {
//		summaryTotal = new HashMap();
//		summaryTotal.put("currencyCode", currencyCode);
//		summaryTotal.put("currencyName", currencyName);
//		summaryTotal.put("entityId", entityId);
//		summaryTotal.put("date", date);	
//		
//		return summaryTotal;
//	}
	/**
	 * @param summaryTotal
	 */
//	public void setSummaryTotal(Map summaryTotal) {
//		this.summaryTotal = summaryTotal;
//	}
	/**
	 * @return
	 */
//	public String getEntityId() {
//		return entityId;
//	}
	/**
	 * @param entityId
	 */
//	public void setEntityId(String entityId) {
//		this.entityId = entityId;
//	}
}
