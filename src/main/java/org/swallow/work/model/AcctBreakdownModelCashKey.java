/*
 * Created on Sep 15, 2006
 *
 * TODO To change the template for this generated file go to
 * Window - Preferences - Java - Code Style - Code Templates
 */
package org.swallow.work.model;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 *
 * TODO To change the template for this generated type comment go to
 * Window - Preferences - Java - Code Style - Code Templates
 */
public class AcctBreakdownModelCashKey implements Serializable{

	private String entityId;
	private String accountId;
	private String accountType;
	private String balanceType;
	private Date   date;
	private String hostId;
	
	
	/**
	 * @return Returns the hostId.
	 */
	public String getHostId() {
		return hostId;
	}
	/**
	 * @param hostId The hostId to set.
	 */
	public void setHostId(String hostId) {
		this.hostId = hostId;
	}
	/**
	 * @return Returns the accountId.
	 */
	public String getAccountId() {
		return accountId;
	}
	/**
	 * @param accountId The accountId to set.
	 */
	public void setAccountId(String accountId) {
		this.accountId = accountId;
	}
	
	
	
	/**
	 * @return Returns the accountType.
	 */
	public String getAccountType() {
		return accountType;
	}
	/**
	 * @param accountType The accountType to set.
	 */
	public void setAccountType(String accountType) {
		this.accountType = accountType;
	}
	/**
	 * @return Returns the balanceType.
	 */
	public String getBalanceType() {
		return balanceType;
	}
	/**
	 * @param balanceType The balanceType to set.
	 */
	public void setBalanceType(String balanceType) {
		this.balanceType = balanceType;
	}
	/**
	 * @return Returns the date.
	 */
	public Date getDate() {
		return date;
	}
	/**
	 * @param date The date to set.
	 */
	public void setDate(Date date) {
		this.date = date;
	}
	/**
	 * @return Returns the entityId.
	 */
	public String getEntityId() {
		return entityId;
	}
	/**
	 * @param entityId The entityId to set.
	 */
	public void setEntityId(String entityId) {
		this.entityId = entityId;
	}	
	
	/**
	 * This function overrides the equals function of Object class.
	 * 
	 * @param obj
	 *            Object to be compared
	 * 
	 * @return true/false
	 */
	public boolean equals(Object obj) {
		boolean retValue = false;

		if ((obj != null) && obj instanceof AcctBreakdownModelCashKey) {
			AcctBreakdownModelCashKey acctBreakdownModelCashKey = (AcctBreakdownModelCashKey) obj;
			retValue = acctBreakdownModelCashKey.getAccountId().equals(accountId)
					&& acctBreakdownModelCashKey.getAccountType().equals(accountType)
					&& acctBreakdownModelCashKey.getBalanceType().equals(balanceType)
					&& acctBreakdownModelCashKey.getDate().equals(date)
					&& acctBreakdownModelCashKey.getEntityId().equals(entityId);
		}

		return retValue;
	}

	/**
	 * This function overrides the hashCode function of Object class.
	 * 
	 * @return Retunrs the Hash Code of object.
	 */
	public int hashCode() {
		return accountId.hashCode() + accountType.hashCode() + balanceType.hashCode()
				+ date.hashCode()+ entityId.hashCode();
	}


}
