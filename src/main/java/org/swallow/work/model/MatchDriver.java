/*
 * Created on Jan 30, 2006
 *
 * TODO To change the template for this generated file go to
 * Window - Preferences - Java - Code Style - Code Templates
 */
package org.swallow.work.model;
import java.text.SimpleDateFormat;
import java.util.Date;

import javax.servlet.http.HttpSession;

import org.swallow.model.BaseObject;
import org.swallow.util.CommonDataManager;
import org.swallow.util.SwtConstants;
import org.swallow.util.SwtUtil;
import org.swallow.util.SystemFormats;
import org.swallow.util.UserThreadLocalHolder;
import org.swallow.work.model.Movement.Id;
/**
 * <AUTHOR>
 *
 * TODO To change the template for this generated type comment go to
 * Window - Preferences - Java - Code Style - Code Templates
 */
public class MatchDriver extends BaseObject {


	private String newMoveFlag;
	private String processingFlag;
	private Date updateDate;
	private String lastRun;
	private String updateDateAsString;
	// Start: Code added by <PERSON><PERSON> for mantis 1206 on 01122010
	// variable to hold lastStarted
	private Date lastStarted;
	// End: Code added by <PERSON><PERSON> for mantis 1206 on 01122010
	/**
	 * @return Returns the updateDateAsString.
	 */

	
	
	/**
	 * @param updateDateAsString The updateDateAsString to set.
	 */
	public void setUpdateDateAsString(String updateDateAsString) {
		this.updateDateAsString = updateDateAsString;
	}
	/**
	 * @return Returns the lastRun.
	 */
	public String getLastRun() {
		return lastRun;
	}
	/**
	 * @param lastRun The lastRun to set.
	 */
	public void setLastRun(String lastRun) {
		this.lastRun = lastRun;
	}
	private String updateUser;
	private Id id = new Id();
	
	
	public static class Id extends BaseObject{
		
		private String hostId ;
		private String entityId;
		private String currencyCode;
		
		/**
		 * 
		 */
		public Id() {
			super();
			// TODO Auto-generated constructor stub
		}
		
		
		
		/**
		 * @param hostId
		 * @param entityId
		 * @param currencyCode
		 */
		public Id(String hostId, String entityId, String currencyCode) {
			super();
			this.hostId = hostId;
			this.entityId = entityId;
			this.currencyCode = currencyCode;
		}
		
		
		
		
		/**
		 * @return Returns the currencyCode.
		 */
		public String getCurrencyCode() {
			return currencyCode;
		}
		/**
		 * @param currencyCode The currencyCode to set.
		 */
		public void setCurrencyCode(String currencyCode) {
			this.currencyCode = currencyCode;
		}
		/**
		 * @return Returns the entityId.
		 */
		public String getEntityId() {
			return entityId;
		}
		/**
		 * @param entityId The entityId to set.
		 */
		public void setEntityId(String entityId) {
			this.entityId = entityId;
		}
		/**
		 * @return Returns the hostId.
		 */
		public String getHostId() {
			return hostId;
		}
		/**
		 * @param hostId The hostId to set.
		 */
		public void setHostId(String hostId) {
			this.hostId = hostId;
		}
	}
	
	
	
	/**
	 * 
	 */
	public MatchDriver() {
		super();
		// TODO Auto-generated constructor stub
	}
	
	
	
	
	/**
	 * @param newMoveFlag
	 * @param processingFlag
	 * @param updateDate
	 * @param updateUser
	 * @param id
	 */
	public MatchDriver(String newMoveFlag, String processingFlag,
			Date updateDate, String updateUser, Id id) {
		super();
		this.newMoveFlag = newMoveFlag;
		this.processingFlag = processingFlag;
		this.updateDate = updateDate;
		this.updateUser = updateUser;
		this.id = id;
	}
	
	
	
	/**
	 * @return Returns the id.
	 */
	public Id getId() {
		return id;
	}
	/**
	 * @param id The id to set.
	 */
	public void setId(Id id) {
		this.id = id;
	}
	/**
	 * @return Returns the newMoveFlag.
	 */
	public String getNewMoveFlag() {
		return newMoveFlag;
	}
	/**
	 * @param newMoveFlag The newMoveFlag to set.
	 */
	public void setNewMoveFlag(String newMoveFlag) {
		this.newMoveFlag = newMoveFlag;
	}
	/**
	 * @return Returns the processingFlag.
	 */
	public String getProcessingFlag() {
		return processingFlag;
	}
	/**
	 * @param processingFlag The processingFlag to set.
	 */
	public void setProcessingFlag(String processingFlag) {
		this.processingFlag = processingFlag;
	}
	/**
	 * @return Returns the updateDate.
	 */
	public Date getUpdateDate() {
		return updateDate;
	}
	/**
	 * @param updateDate The updateDate to set.
	 */
	public void setUpdateDate(Date updateDate) {
		this.updateDate = updateDate;
	}
	/**
	 * @return Returns the updateUser.
	 */
	public String getUpdateUser() {
		return updateUser;
	}
	/**
	 * @param updateUser The updateUser to set.
	 */
	public void setUpdateUser(String updateUser) {
		this.updateUser = updateUser;
	}
	/**
	 * @return Returns the updateDateAsString.
	 */
	public String getUpdateDateAsString() {
		return updateDateAsString;
	}
	// Start: Code added by Bala for mantis 1206 on 01122010
	/**
	 * @return the lastStarted
	 */
	public Date getLastStarted() {
		return lastStarted;
	}
	/**
	 * @param lastStarted the lastStarted to set
	 */
	public void setLastStarted(Date lastStarted) {
		this.lastStarted = lastStarted;
	}
	// End: Code added by Bala for mantis 1206 on 01122010
}
