package org.swallow.work.model;

import java.io.Serializable;
import java.util.Date;

import org.swallow.work.model.SweepMsgFormat.Id;

public class SweepNAKMessage implements Serializable{
	private String messageId;
	private String msgFormatId;
	private Date updateDate;
	private String updateDateAsString;
	private String updateTime;
	private String sweepId;
	private String currcode;
	private String valueDate;
	private String valueDateAsString;
	private Double sweepAmount;
	private String sweepAmountAsString;
	private String ACKNAKmessageId;
	
	public String getCurrcode() {
		return currcode;
	}
	public void setCurrcode(String currcode) {
		this.currcode = currcode;
	}
	public String getMsgFormatId() {
		return msgFormatId;
	}
	public void setMsgFormatId(String msgFormatId) {
		this.msgFormatId = msgFormatId;
	}
	public Double getSweepAmount() {
		return sweepAmount;
	}
	public void setSweepAmount(Double sweepAmount) {
		this.sweepAmount = sweepAmount;
	}
	public String getSweepAmountAsString() {
		return sweepAmountAsString;
	}
	public void setSweepAmountAsString(String sweepAmountAsString) {
		this.sweepAmountAsString = sweepAmountAsString;
	}
	
	public String getSweepId() {
		return sweepId;
	}
	public void setSweepId(String sweepId) {
		this.sweepId = sweepId;
	}
	public Date getUpdateDate() {
		return updateDate;
	}
	public void setUpdateDate(Date updateDate) {
		this.updateDate = updateDate;
	}
	public String getUpdateDateAsString() {
		return updateDateAsString;
	}
	public void setUpdateDateAsString(String updateDateAsString) {
		this.updateDateAsString = updateDateAsString;
	}
	public String getUpdateTime() {
		return updateTime;
	}
	public void setUpdateTime(String updateTime) {
		this.updateTime = updateTime;
	}
	public String getValueDate() {
		return valueDate;
	}
	public void setValueDate(String valueDate) {
		this.valueDate = valueDate;
	}
	public String getValueDateAsString() {
		return valueDateAsString;
	}
	public void setValueDateAsString(String valueDateAsString) {
		this.valueDateAsString = valueDateAsString;
	}
	
	
	/**
	 * @return Returns the messageId.
	 */
	public String getMessageId() {
		return messageId;
	}
	/**
	 * @param messageId The messageId to set.
	 */
	public void setMessageId(String messageId) {
		this.messageId = messageId;
	}
	/**
	 * @return Returns the aCKNAKmessageId.
	 */
	public String getACKNAKmessageId() {
		return ACKNAKmessageId;
	}
	/**
	 * @param kmessageId The aCKNAKmessageId to set.
	 */
	public void setACKNAKmessageId(String kmessageId) {
		ACKNAKmessageId = kmessageId;
	}
}
