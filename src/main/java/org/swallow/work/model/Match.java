
/*
 * @(#)Match .java  03/01/06
 * 
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 * 
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */

package org.swallow.work.model;
import java.util.Date;
import org.swallow.model.BaseObject;
public class Match extends BaseObject {

	private Id id = new Id();
	private String currencyCode;
	private int highestPosLev;
	/* START: Code modified for populating P_MATCH new coloumns on 25th Jan 08*/
	private int lowestPosLev;
	private Double maxAmount;
	private Date maxValueDate;
	private String predictStatusFlag;
	/* END: Code modified for populating P_MATCH new coloumns on 25th Jan 08*/	
	private String matchQuality;
	private String status;
	private Date statusDate ;
	private Date confirmedDate;
	/* variable declared to handle internal quality1-Mantis 711 on 08/Nov/2008*/	
	private String intQuality1;
	private String intQuality2;
	private String intQuality3;
	private String intQuality4;
	private String intQuality5;
	private String intQuality6;
	private String intQuality7; 
	private String intQuality8; 
	private String intQuality9; 
	private Date origConfirmedDate ;
	private String statusUser;
	private String updateUser;
    
	private Date updateDate;
	
	public Match(Match obj)
	{
		this.getId().setMatchId(obj.getId().getMatchId());
		this.getId().setEntityId( obj.getId().getEntityId());
		this.getId().setHostId(obj.getId().getHostId());
		this.setCurrencyCode(obj.getCurrencyCode());
		this.setHighestPosLev(obj.getHighestPosLev());
		this.setMatchQuality(obj.getMatchQuality());
		this.setStatus(obj.getStatus());
		this.setStatusDate(obj.getStatusDate());
		this.setConfirmedDate(obj.getConfirmedDate());
		this.setIntQuality1(obj.getIntQuality1());
		this.setIntQuality2(obj.getIntQuality2());
		this.setIntQuality3(obj.getIntQuality3());
		this.setIntQuality4(obj.getIntQuality4());
		this.setIntQuality5(obj.getIntQuality5());
		this.setIntQuality6(obj.getIntQuality6());
		this.setIntQuality7(obj.getIntQuality7());
		this.setIntQuality8(obj.getIntQuality8());
		this.setIntQuality9(obj.getIntQuality9());
		this.setOrigConfirmedDate(obj.getOrigConfirmedDate());
		this.setStatusUser(obj.getStatusUser());
		this.setUpdateUser(obj.getUpdateUser());
		this.setUpdateDate(obj.getUpdateDate());
		
	}
	
	public Match()
	{
	}
	
	public static class Id extends BaseObject{
		
		private String hostId ;
		private String entityId;
		private Long matchId;
		
		public Id() {
		}
		
		public Id(String hostId ,String entityId ,Long matchId ) {
			this.hostId = hostId;
			this.entityId = entityId;
			this.matchId = matchId;
			
		}
		
		
		/**
		 * @return Returns the matchId.
		 */
		public Long getMatchId() {
			return matchId;
		}
		/**
		 * @param matchId The matchId to set.
		 */
		public void setMatchId(Long matchId) {
			this.matchId = matchId;
		}
		/**
		 * @return Returns the entityId.
		 */
		public String getEntityId() {
			return entityId;
		}
		/**
		 * @param entityId The entityId to set.
		 */
		public void setEntityId(String entityId) {
			this.entityId = entityId;
		}
		/**
		 * @return Returns the hostId.
		 */
		public String getHostId() {
			return hostId;
		}
		/**
		 * @param hostId The hostId to set.
		 */
		public void setHostId(String hostId) {
			this.hostId = hostId;
		}
	}
	
	/**
	 * @return Returns the id.
	 */
	public Id getId() {
		return id;
	}
	/**
	 * @param id The id to set.
	 */
	public void setId(Id id) {
		this.id = id;
	}
	
	/**
	 * @return Returns the confirmedDate.
	 */
	public Date getConfirmedDate() {
		return confirmedDate;
	}
	/**
	 * @param confirmedDate The confirmedDate to set.
	 */
	public void setConfirmedDate(Date confirmedDate) {
		this.confirmedDate = confirmedDate;
	}
	/**
	 * @return Returns the currencyCode.
	 */
	public String getCurrencyCode() {
		return currencyCode;
	}
	/**
	 * @param currencyCode The currencyCode to set.
	 */
	public void setCurrencyCode(String currencyCode) {
		this.currencyCode = currencyCode;
	}
	/**
	 * @return Returns the highestPosLev.
	 */
	public int getHighestPosLev() {
		return highestPosLev;
	}
	/**
	 * @param highestPosLev The highestPosLev to set.
	 */
	public void setHighestPosLev(int highestPosLev) {
		this.highestPosLev = highestPosLev;
	}
	/* getter,setter method added to handle internal quality 1-Mantis 711 on 08/Nov/2008*/
	/**
	 * @return Returns the intQuality1.
	 */	
	public String getIntQuality1() {
		return intQuality1;
	}	
	/**
	 * @param intQuality1 The intQuality1 to set.
	 */
	public void setIntQuality1(String intQuality1) {
		this.intQuality1 = intQuality1;
	}
	/**
	 * @return Returns the intQuality2.
	 */
	public String getIntQuality2() {
		return intQuality2;
	}
	/**
	 * @param intQuality2 The intQuality2 to set.
	 */
	public void setIntQuality2(String intQuality2) {
		this.intQuality2 = intQuality2;
	}
	/**
	 * @return Returns the intQuality3.
	 */
	public String getIntQuality3() {
		return intQuality3;
	}
	/**
	 * @param intQuality3 The intQuality3 to set.
	 */
	public void setIntQuality3(String intQuality3) {
		this.intQuality3 = intQuality3;
	}
	/**
	 * @return Returns the intQuality4.
	 */
	public String getIntQuality4() {
		return intQuality4;
	}
	/**
	 * @param intQuality4 The intQuality4 to set.
	 */
	public void setIntQuality4(String intQuality4) {
		this.intQuality4 = intQuality4;
	}
	/**
	 * @return Returns the intQuality5.
	 */
	public String getIntQuality5() {
		return intQuality5;
	}
	/**
	 * @param intQuality5 The intQuality5 to set.
	 */
	public void setIntQuality5(String intQuality5) {
		this.intQuality5 = intQuality5;
	}
	/**
	 * @return Returns the intQuality6.
	 */
	public String getIntQuality6() {
		return intQuality6;
	}
	/**
	 * @param intQuality6 The intQuality6 to set.
	 */
	public void setIntQuality6(String intQuality6) {
		this.intQuality6 = intQuality6;
	}
	/**
	 * @return Returns the intQuality7.
	 */
	public String getIntQuality7() {
		return intQuality7;
	}
	/**
	 * @param intQuality7 The intQuality7 to set.
	 */
	public void setIntQuality7(String intQuality7) {
		this.intQuality7 = intQuality7;
	}
	/**
	 * @return Returns the intQuality8.
	 */
	public String getIntQuality8() {
		return intQuality8;
	}
	/**
	 * @param intQuality8 The intQuality8 to set.
	 */
	public void setIntQuality8(String intQuality8) {
		this.intQuality8 = intQuality8;
	}
	/**
	 * @return Returns the intQuality9.
	 */
	public String getIntQuality9() {
		return intQuality9;
	}
	/**
	 * @param intQuality9 The intQuality9 to set.
	 */
	public void setIntQuality9(String intQuality9) {
		this.intQuality9 = intQuality9;
	}
	/**
	 * @return Returns the matchQuality.
	 */
	public String getMatchQuality() {
		return matchQuality;
	}
	/**
	 * @param matchQuality The matchQuality to set.
	 */
	public void setMatchQuality(String matchQuality) {
		this.matchQuality = matchQuality;
	}
	/**
	 * @return Returns the origConfirmedDate.
	 */
	public Date getOrigConfirmedDate() {
		return origConfirmedDate;
	}
	/**
	 * @param origConfirmedDate The origConfirmedDate to set.
	 */
	public void setOrigConfirmedDate(Date origConfirmedDate) {
		this.origConfirmedDate = origConfirmedDate;
	}
	/**
	 * @return Returns the status.
	 */
	public String getStatus() {
		return status;
	}
	/**
	 * @param status The status to set.
	 */
	public void setStatus(String status) {
		this.status = status;
	}
	/**
	 * @return Returns the statusDate.
	 */
	public Date getStatusDate() {
		return statusDate;
	}
	/**
	 * @param statusDate The statusDate to set.
	 */
	public void setStatusDate(Date statusDate) {
		this.statusDate = statusDate;
	}
	/**
	 * @return Returns the statusUser.
	 */
	public String getStatusUser() {
		return statusUser;
	}
	/**
	 * @param statusUser The statusUser to set.
	 */
	public void setStatusUser(String statusUser) {
		this.statusUser = statusUser;
	}

	/**
	 * @return Returns the updateUser.
	 */
	public String getUpdateUser() {
		return updateUser;
	}
	/**
	 * @param updateUser The updateUser to set.
	 */
	public void setUpdateUser(String updateUser) {
		this.updateUser = updateUser;
	}
/**
 * @return Returns the updateDate.
 */
public Date getUpdateDate() {
	return updateDate;
}
/**
 * @param updateDate The updateDate to set.
 */
public void setUpdateDate(Date updateDate) {
	this.updateDate = updateDate;
}

	/* START: Code modified for populating P_MATCH new coloumns on 25th Jan 08*/
	/**
	 * @return Returns the lowestPosLev.
	 */
	public int getLowestPosLev() {
		return lowestPosLev;
	}
	/**
	 * @param lowestPosLev The lowestPosLev to set.
	 */
	public void setLowestPosLev(int lowestPosLev) {
		this.lowestPosLev = lowestPosLev;
	}
	/**
	 * @return Returns the maxAmount.
	 */
	public Double getMaxAmount() {
		return maxAmount;
	}
	/**
	 * @param maxAmount The maxAmount to set.
	 */
	public void setMaxAmount(Double maxAmount) {
		this.maxAmount = maxAmount;
	}
	/**
	 * @return Returns the maxValueDate.
	 */
	public Date getMaxValueDate() {
		return maxValueDate;
	}
	/**
	 * @param maxValueDate The maxValueDate to set.
	 */
	public void setMaxValueDate(Date maxValueDate) {
		this.maxValueDate = maxValueDate;
	}	
	/**
	 * @return Returns the predictStatusFlag.
	 */
	public String getPredictStatusFlag() {
		return predictStatusFlag;
	}
	/**
	 * @param predictStatusFlag The predictStatusFlag to set.
	 */
	public void setPredictStatusFlag(String predictStatusFlag) {
		this.predictStatusFlag = predictStatusFlag;
	}
	/* END: Code modified for populating P_MATCH new coloumns on 25th Jan 08*/

	
}
