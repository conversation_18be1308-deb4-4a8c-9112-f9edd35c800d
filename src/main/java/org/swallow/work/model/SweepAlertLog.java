/*
 * Created on Jan 3, 2006
 *
 * TODO To change the template for this generated file go to
 * Window - Preferences - Java - Code Style - Code Templates
 */
package org.swallow.work.model;

import java.util.Date;

import org.swallow.model.BaseObject;

/**
 * <AUTHOR>
 * 
 * TODO To change the template for this generated type comment go to Window -
 * Preferences - Java - Code Style - Code Templates
 */


public class SweepAlertLog extends BaseObject {
	
	private Id id = new Id();
	public static class Id extends BaseObject{
		
		/**
		 * @return Returns the alertId.
		 */
		public Long getAlertId() {
			return alertId;
		}
		/**
		 * @param alertId The alertId to set.
		 */
		public void setAlertId(Long alertId) {
			this.alertId = alertId;
		}
		
		/**
		 * @return Returns the hostId.
		 */
		public String getHostId() {
			return hostId;
		}
		/**
		 * @param hostId The hostId to set.
		 */
		public void setHostId(String hostId) {
			this.hostId = hostId;
		}
		/**
		 * @return Returns the alertDate.
		 */
		public Date getAlertDate() {
			return alertDate;
		}
		/**
		 * @param alertDate The alertDate to set.
		 */
		public void setAlertDate(Date alertDate) {
			this.alertDate = alertDate;
		}
		/**
		 * @return Returns the userId.
		 */
		public String getUserId() {
			return userId;
		}
		/**
		 * @param userId The userId to set.
		 */
		public void setUserId(String userId) {
			this.userId = userId;
		}
		private String hostId ;
		private Long alertId;
		private String userId;
		private Date alertDate;
	}
	
	
	/**
	 * @return Returns the id.
	 */
	public Id getId() {
		return id;
	}
	/**
	 * @param id The id to set.
	 */
	public void setId(Id id) {
		this.id = id;
	}
}
