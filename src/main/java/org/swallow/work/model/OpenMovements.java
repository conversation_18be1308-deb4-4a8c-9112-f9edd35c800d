/*
 * Created on Apr 3, 2007
 *
 * TODO To change the template for this generated file go to
 * Window - Preferences - Java - Code Style - Code Templates
 */
package org.swallow.work.model;

import org.swallow.model.BaseObject;


/**
 * <AUTHOR>
 *
 * TODO To change the template for this generated type comment go to
 * Window - Preferences - Java - Code Style - Code Templates
 */
public class OpenMovements extends BaseObject {
private Integer movementCount;
private Double cashFilter;
private Double securitiesFilter;
private String currName;

private Id id = new Id();

public static class Id extends BaseObject {
   private String hostId;
   private String entityId;
   private String currCode;
   private Integer posLvl;


    
	/**
	 * 
	 */
	public Id() {
		super();
	}
	/**
	 * @param hostId
	 * @param entityId
	 * @param currCode
	 * @param posLvl
	 */
	public Id(String hostId, String entityId, String currCode, Integer posLvl) {
		super();
		this.hostId = hostId;
		this.entityId = entityId;
		this.currCode = currCode;
		this.posLvl = posLvl;
	}
/**
 * @return Returns the currCode.
 */
public String getCurrCode() {
	return currCode;
}
/**
 * @param currCode The currCode to set.
 */
public void setCurrCode(String currCode) {
	this.currCode = currCode;
}
/**
 * @return Returns the entityId.
 */
public String getEntityId() {
	return entityId;
}
/**
 * @param entityId The entityId to set.
 */
public void setEntityId(String entityId) {
	this.entityId = entityId;
}
/**
 * @return Returns the hostId.
 */
public String getHostId() {
	return hostId;
}
/**
 * @param hostId The hostId to set.
 */
public void setHostId(String hostId) {
	this.hostId = hostId;
}
/**
 * @return Returns the posLvl.
 */
public Integer getPosLvl() {
	return posLvl;
}
/**
 * @param posLvl The posLvl to set.
 */
public void setPosLvl(Integer posLvl) {
	this.posLvl = posLvl;
}
}	
	
/**
 * @return Returns the cashFilter.
 */
public Double getCashFilter() {
	return cashFilter;
}
/**
 * @param cashFilter The cashFilter to set.
 */
public void setCashFilter(Double cashFilter) {
	this.cashFilter = cashFilter;
}
/**
 * @return Returns the currName.
 */
public String getCurrName() {
	return currName;
}
/**
 * @param currName The currName to set.
 */
public void setCurrName(String currName) {
	this.currName = currName;
}
/**
 * @return Returns the id.
 */
public Id getId() {
	return id;
}
/**
 * @param id The id to set.
 */
public void setId(Id id) {
	this.id = id;
}
/**
 * @return Returns the movementCount.
 */
public Integer getMovementCount() {
	return movementCount;
}
/**
 * @param movementCount The movementCount to set.
 */
public void setMovementCount(Integer movementCount) {
	this.movementCount = movementCount;
}
/**
 * @return Returns the securitiesFilter.
 */
public Double getSecuritiesFilter() {
	return securitiesFilter;
}
/**
 * @param securitiesFilter The securitiesFilter to set.
 */
public void setSecuritiesFilter(Double securitiesFilter) {
	this.securitiesFilter = securitiesFilter;
}
}
