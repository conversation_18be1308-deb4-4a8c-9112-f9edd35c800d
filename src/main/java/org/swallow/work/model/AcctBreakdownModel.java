/**
 * @(#)AcctBreakdownModel.java 1.0 / Sep 14, 2006
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.work.model;

import java.io.Serializable;

/**
 * AcctBreakdownModel.java
 * 
 * This java bean has getters and setters for Account Breakdown Monitor Details.
 * This is used to populate data into the table on Account Breakdown Monitor
 * screen.
 * 
 * @modified by <PERSON><PERSON><PERSON><PERSON><PERSON> R / Mar 12, 2012 / SmartPredict-1054
 */
public class AcctBreakdownModel implements Serializable {

	// String to hold the Entity Id
	private String entityId = null;
	// String to hold the Currency Code
	private String currencyCode = null;
	// String to hold the Account Id
	private String acctId = null;
	// String to hold the Account Name
	private String acctName = null;
	// String to hold the Predict Balance
	private String predBalance = null;
	// Predicted balance sign flag (positive/negative)
	private String predBalanceSign = null;
	// String to hold the Un Expected balance
	private String unexpectedBalance = null;
	// Unexpected balance sign flag (positive/negative)
	private String unexpectedBalanceSign = null;
	// String to hold the Starting Balance
	private String startingBalance = null;
	// Start of the day balance sign flag (positive/negative)
	private String startingBalanceSign = null;
	// Sum flag denotes whether to include this account in sum calculation
	private String sum = null;
	// String to hold the Selected Date
	private String valueDate = null;
	// String to hold the Balance Type
	private String balanceType = null;
	// Denoted whether this is loro or account
	private String isLoroOrCurrAcct = null;
	// Flag denotes whether the amount can move from loro to predict balance
	private String loroToPredictedFlag = null;
	// Icon indicator for loro balance
	private String iconIndicatorFlag = null;

	// Code Modified By Chinniah on 19-Jul-2012 for
	// 1054_STL_63 in Mantis 1933:Color Flag not Proper for
	// SUM='C'
	// Variable to hold the summable status based on the cut-off time
	private String summable = null;
	/*
	 * Start:Code MOdified By Chinniah on 28-May-2012 for Mantis 1933:Account
	 * Monitor: Screen hangs with "Function returned without value" if closed
	 * accounts
	 */
	// String to hold account status
	private String accountStatus = null;
	private String scenarioHighlighted = null;
	
	
	

	private String includeLoroInPredictedIndicator = null;
	private String includePredictedInLoroIndicator = null;
	
	private String includeLoroInPredictedColor = null;
	private String includePredictedInLoroColor = null;
	/**
	 * Getter method for accountStatus
	 * 
	 * @return accountStatus as String
	 */

	public String getAccountStatus() {
		return accountStatus;
	}

	/**
	 * Setter method for accountStatus
	 * 
	 * @param accountStatus
	 */

	public void setAccountStatus(String accountStatus) {
		this.accountStatus = accountStatus;
	}

	/*
	 * End:Code MOdified By Chinniah on 28-May-2012 for Mantis 1933:Account
	 * Monitor: Screen hangs with "Function returned without value" if closed
	 * accounts
	 */
	/**
	 * Getter method of entityId
	 * 
	 * @return the entityId
	 */
	public String getEntityId() {
		return entityId;
	}

	/**
	 * Setter method of entityId
	 * 
	 * @param entityId
	 *            the entityId to set
	 */
	public void setEntityId(String entityId) {
		this.entityId = entityId;
	}

	/**
	 * Getter method of currencyCode
	 * 
	 * @return the currencyCode
	 */
	public String getCurrencyCode() {
		return currencyCode;
	}

	/**
	 * Setter method of currencyCode
	 * 
	 * @param currencyCode
	 *            the currencyCode to set
	 */
	public void setCurrencyCode(String currencyCode) {
		this.currencyCode = currencyCode;
	}

	/**
	 * Getter method of acctId
	 * 
	 * @return the acctId
	 */
	public String getAcctId() {
		return acctId;
	}

	/**
	 * Setter method of acctId
	 * 
	 * @param acctId
	 *            the acctId to set
	 */
	public void setAcctId(String acctId) {
		this.acctId = acctId;
	}

	/**
	 * Getter method of acctName
	 * 
	 * @return the acctName
	 */
	public String getAcctName() {
		return acctName;
	}

	/**
	 * Setter method of acctName
	 * 
	 * @param acctName
	 *            the acctName to set
	 */
	public void setAcctName(String acctName) {
		this.acctName = acctName;
	}

	/**
	 * Getter method of predBalance
	 * 
	 * @return the predBalance
	 */
	public String getPredBalance() {
		return predBalance;
	}

	/**
	 * Setter method of predBalance
	 * 
	 * @param predBalance
	 *            the predBalance to set
	 */
	public void setPredBalance(String predBalance) {
		this.predBalance = predBalance;
	}

	/**
	 * Getter method of predBalanceSign
	 * 
	 * @return the predBalanceSign
	 */
	public String getPredBalanceSign() {
		return predBalanceSign;
	}

	/**
	 * Setter method of predBalanceSign
	 * 
	 * @param predBalanceSign
	 *            the predBalanceSign to set
	 */
	public void setPredBalanceSign(String predBalanceSign) {
		this.predBalanceSign = predBalanceSign;
	}

	/**
	 * Getter method of unexpectedBalance
	 * 
	 * @return the unexpectedBalance
	 */
	public String getUnexpectedBalance() {
		return unexpectedBalance;
	}

	/**
	 * Setter method of unexpectedBalance
	 * 
	 * @param unexpectedBalance
	 *            the unexpectedBalance to set
	 */
	public void setUnexpectedBalance(String unexpectedBalance) {
		this.unexpectedBalance = unexpectedBalance;
	}

	/**
	 * Getter method of unexpectedBalanceSign
	 * 
	 * @return the unexpectedBalanceSign
	 */
	public String getUnexpectedBalanceSign() {
		return unexpectedBalanceSign;
	}

	/**
	 * Setter method of unexpectedBalanceSign
	 * 
	 * @param unexpectedBalanceSign
	 *            the unexpectedBalanceSign to set
	 */
	public void setUnexpectedBalanceSign(String unexpectedBalanceSign) {
		this.unexpectedBalanceSign = unexpectedBalanceSign;
	}

	/**
	 * Getter method of startingBalance
	 * 
	 * @return the startingBalance
	 */
	public String getStartingBalance() {
		return startingBalance;
	}

	/**
	 * Setter method of startingBalance
	 * 
	 * @param startingBalance
	 *            the startingBalance to set
	 */
	public void setStartingBalance(String startingBalance) {
		this.startingBalance = startingBalance;
	}

	/**
	 * Getter method of startingBalanceSign
	 * 
	 * @return the startingBalanceSign
	 */
	public String getStartingBalanceSign() {
		return startingBalanceSign;
	}

	/**
	 * Setter method of startingBalanceSign
	 * 
	 * @param startingBalanceSign
	 *            the startingBalanceSign to set
	 */
	public void setStartingBalanceSign(String startingBalanceSign) {
		this.startingBalanceSign = startingBalanceSign;
	}

	/**
	 * Getter method of sum
	 * 
	 * @return the sum
	 */
	public String getSum() {
		return sum;
	}

	/**
	 * Setter method of sum
	 * 
	 * @param sum
	 *            the sum to set
	 */
	public void setSum(String sum) {
		this.sum = sum;
	}

	/**
	 * Getter method of valueDate
	 * 
	 * @return the valueDate
	 */
	public String getValueDate() {
		return valueDate;
	}

	/**
	 * Setter method of valueDate
	 * 
	 * @param valueDate
	 *            the valueDate to set
	 */
	public void setValueDate(String valueDate) {
		this.valueDate = valueDate;
	}

	/**
	 * Getter method of balanceType
	 * 
	 * @return the balanceType
	 */
	public String getBalanceType() {
		return balanceType;
	}

	/**
	 * Setter method of balanceType
	 * 
	 * @param balanceType
	 *            the balanceType to set
	 */
	public void setBalanceType(String balanceType) {
		this.balanceType = balanceType;
	}

	/**
	 * Getter method of isLoroOrCurrAcct
	 * 
	 * @return the isLoroOrCurrAcct
	 */
	public String getIsLoroOrCurrAcct() {
		return isLoroOrCurrAcct;
	}

	/**
	 * Setter method of isLoroOrCurrAcct
	 * 
	 * @param isLoroOrCurrAcct
	 *            the isLoroOrCurrAcct to set
	 */
	public void setIsLoroOrCurrAcct(String isLoroOrCurrAcct) {
		this.isLoroOrCurrAcct = isLoroOrCurrAcct;
	}

	/**
	 * Getter method of loroToPredictedFlag
	 * 
	 * @return the loroToPredictedFlag
	 */
	public String getLoroToPredictedFlag() {
		return loroToPredictedFlag;
	}

	/**
	 * Setter method of loroToPredictedFlag
	 * 
	 * @param loroToPredictedFlag
	 *            the loroToPredictedFlag to set
	 */
	public void setLoroToPredictedFlag(String loroToPredictedFlag) {
		this.loroToPredictedFlag = loroToPredictedFlag;
	}

	/**
	 * Getter method of iconIndicatorFlag
	 * 
	 * @return the iconIndicatorFlag
	 */
	public String getIconIndicatorFlag() {
		return iconIndicatorFlag;
	}

	/**
	 * Setter method of iconIndicatorFlag
	 * 
	 * @param iconIndicatorFlag
	 *            the iconIndicatorFlag to set
	 */
	public void setIconIndicatorFlag(String iconIndicatorFlag) {
		this.iconIndicatorFlag = iconIndicatorFlag;
	}

	/*
	 * Start:Code Modified By Chinniah on 19-Jul-2012 for 1054_STL_63 in Mantis
	 * 1933:Color Flag not Proper for SUM='C'
	 */
	/**
	 * Getter method for summable
	 * 
	 * @return summable as String
	 */

	public String getSummable() {
		return summable;
	}

	/**
	 * Setter method for summable
	 * 
	 * @param summable
	 */

	public void setSummable(String summable) {
		this.summable = summable;
	}
	
	public String getScenarioHighlighted() {
		return scenarioHighlighted;
	}

	public void setScenarioHighlighted(String scenarioHighlighted) {
		this.scenarioHighlighted = scenarioHighlighted;
	}
	/*
	 * End:Code Modified By Chinniah on 19-Jul-2012 for 1054_STL_63 in Mantis
	 * 1933:Color Flag not Proper for SUM='C'
	 */
	
	public String getIncludeLoroInPredictedIndicator() {
		return includeLoroInPredictedIndicator;
	}

	public void setIncludeLoroInPredictedIndicator(String includeLoroInPredictedIndicator) {
		this.includeLoroInPredictedIndicator = includeLoroInPredictedIndicator;
	}

	public String getIncludePredictedInLoroIndicator() {
		return includePredictedInLoroIndicator;
	}

	public void setIncludePredictedInLoroIndicator(String includePredictedInLoroIndicator) {
		this.includePredictedInLoroIndicator = includePredictedInLoroIndicator;
	}

	public String getIncludeLoroInPredictedColor() {
		return includeLoroInPredictedColor;
	}

	public void setIncludeLoroInPredictedColor(String includeLoroInPredictedColor) {
		this.includeLoroInPredictedColor = includeLoroInPredictedColor;
	}

	public String getIncludePredictedInLoroColor() {
		return includePredictedInLoroColor;
	}

	public void setIncludePredictedInLoroColor(String includePredictedInLoroColor) {
		this.includePredictedInLoroColor = includePredictedInLoroColor;
	}
}
