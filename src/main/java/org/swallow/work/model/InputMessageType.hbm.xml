<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE hibernate-mapping PUBLIC
        "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
        "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">

<hibernate-mapping>
    <class name="org.swallow.work.model.InputMessageType" table="I_MESSAGE_TYPE">
	    <id name="messageType" column="MSGTYPE" unsaved-value="null">
			<generator class="assigned"/>
		</id>
		
		<property name="description" column="DESCRIPTION" not-null="false"/>	
		<property name="groupId" column="GROUPID" not-null="false"/>
		<property name="errorNumber" column="NB_ERR" not-null="false"/>	
		<property name="tempTable" column="TEMP_TABLE" not-null="false"/>	
		<property name="procedure" column="RUN_PROC" not-null="false"/>	
		<property name="updateColumn" column="COLUMN2UPDATE" not-null="false"/>
		<property name="tempTableId" column="ARCID" not-null="true"/>	
		<property name="affType" column="AFFTYPE" not-null="false"/>
    </class>
</hibernate-mapping>