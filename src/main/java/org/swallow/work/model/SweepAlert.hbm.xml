<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE hibernate-mapping PUBLIC
        "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
        "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">
<hibernate-mapping>
 <class name="org.swallow.work.model.SweepAlert" table="P_SWEEP_ALERTS">
<id name="id" access="field" type="org.swallow.work.model.SweepAlertIdType">
		   <column name="HOST_ID" not-null="true" sql-type="hostId"/>
		   <column name="ENTITY_ID" not-null="true" sql-type="entityId"/>
		   <column name="ALERT_ID" not-null="true" sql-type="alertId"/>
		   <generator class="org.swallow.work.model.AlertSequenceGenerator"/>		   
		 </id> 
  <property name="alertStage" column="ALERT_STAGE"/>
  <property name="sweepId" column="SWEEP_ID"/>
 	<property name="currencyCode" column="CURRENCY_CODE"/>
  <property name="sweepAmount" column="SWEEP_AMOUNT"/>
  <property name="alertDate" column="ALERT_DATE"/>
  <property name="status" column="STATUS"/>
  <many-to-one name="alertMaster" lazy="false"
   class="org.swallow.control.model.SystemAlertMesages" not-null="true"
   outer-join="true" update="false" insert="false" foreign-key="FK_P_SWEEP_ALERT_P_ALERT">
   <column name="HOST_ID"/>
   <column name="ALERT_STAGE"/>
  </many-to-one>
 </class>
</hibernate-mapping>
