<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE hibernate-mapping PUBLIC
        "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
        "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">

<hibernate-mapping>
    <class name="org.swallow.work.model.SweepLimitsView" table="VW_P_SWEEP_LIMIT">
		<composite-id name="id" class="org.swallow.work.model.SweepLimitsView$Id" unsaved-value="any">
		
		<key-property name="roleId" access="field" column="ROLE_ID" />
		<key-property name="currencyCode" access="field" column="CURRENCY_CODE" />
		
		</composite-id>
		<property name="sweepLimitBig" column="SWEEP_LIMIT" not-null="false"/>
		<property name="sweepLimit" column="SWEEP_LIMIT" not-null="false" update="false" insert="false"/>
		<property name="updateDate" column="UPDATE_DATE" not-null="false"/>	
		<property name="updateUser" column="UPDATE_USER" not-null="false"/>	
		

    </class>
</hibernate-mapping>
