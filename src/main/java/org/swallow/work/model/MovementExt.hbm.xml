<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE hibernate-mapping PUBLIC
        "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
        "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">

<hibernate-mapping>
    <class name="org.swallow.work.model.MovementExt" table="P_MOVEMENT_EXT">
		  <composite-id name="id" class="org.swallow.work.model.MovementExt$Id" unsaved-value="any">
				<key-property name="hostId" access="field" column="HOST_ID" />
		        <key-property name="movementId" access="field" column="MOVEMENT_ID"/>
		        <key-property name="entityId" access="field" column="ENTITY_ID"/>
		  </composite-id>
		<property name="orderingCustomer1" column="ORDERING_CUSTOMER_TEXT1" not-null="false"/>
		<property name="orderingCustomer2" column="ORDERING_CUSTOMER_TEXT2" not-null="false"/>		
		<property name="orderingCustomer3" column="ORDERING_CUSTOMER_TEXT3" not-null="false"/>		
		<property name="orderingCustomer4" column="ORDERING_CUSTOMER_TEXT4" not-null="false"/>		
		<property name="orderingCustomer5" column="ORDERING_CUSTOMER_TEXT5" not-null="false"/>		
		<property name="orderingInstitution1" column="ORDERING_INSTITUTION_TEXT1" not-null="false"/>	
		<property name="orderingInstitution2" column="ORDERING_INSTITUTION_TEXT2" not-null="false"/>	
		<property name="orderingInstitution3" column="ORDERING_INSTITUTION_TEXT3" not-null="false"/>	
		<property name="orderingInstitution4" column="ORDERING_INSTITUTION_TEXT4" not-null="false"/>	
		<property name="orderingInstitution5" column="ORDERING_INSTITUTION_TEXT5" not-null="false"/>	
		<property name="senderCorrespondent1" column="SENDERS_CORRES_TEXT1" not-null="false"/>		
		<property name="senderCorrespondent2" column="SENDERS_CORRES_TEXT2" not-null="false"/>		
		<property name="senderCorrespondent3" column="SENDERS_CORRES_TEXT3" not-null="false"/>		
		<property name="senderCorrespondent4" column="SENDERS_CORRES_TEXT4" not-null="false"/>		
		<property name="senderCorrespondent5" column="SENDERS_CORRES_TEXT5" not-null="false"/>		
		<property name="receiverCorrespondent1" column="RECEIVERS_CORRES_TEXT1" not-null="false"/>		
		<property name="receiverCorrespondent2" column="RECEIVERS_CORRES_TEXT2" not-null="false"/>		
		<property name="receiverCorrespondent3" column="RECEIVERS_CORRES_TEXT3" not-null="false"/>		
		<property name="receiverCorrespondent4" column="RECEIVERS_CORRES_TEXT4" not-null="false"/>		
		<property name="receiverCorrespondent5" column="RECEIVERS_CORRES_TEXT5" not-null="false"/>		
		<property name="intermediaryInstitution1" column="INTMDRY_INSTITUTION_TEXT1" not-null="false"/>	
		<property name="intermediaryInstitution2" column="INTMDRY_INSTITUTION_TEXT2" not-null="false"/>	
		<property name="intermediaryInstitution3" column="INTMDRY_INSTITUTION_TEXT3" not-null="false"/>	
		<property name="intermediaryInstitution4" column="INTMDRY_INSTITUTION_TEXT4" not-null="false"/>	
		<property name="intermediaryInstitution5" column="INTMDRY_INSTITUTION_TEXT5" not-null="false"/>	
		<property name="accountWithInstitution1" column="ACC_WITH_INSTITUTION_TEXT1" not-null="false"/>	
		<property name="accountWithInstitution2" column="ACC_WITH_INSTITUTION_TEXT2" not-null="false"/>	
		<property name="accountWithInstitution3" column="ACC_WITH_INSTITUTION_TEXT3" not-null="false"/>	
		<property name="accountWithInstitution4" column="ACC_WITH_INSTITUTION_TEXT4" not-null="false"/>	
		<property name="accountWithInstitution5" column="ACC_WITH_INSTITUTION_TEXT5" not-null="false"/>	
		<property name="beneficiaryCustomer1" column="BENEFICIARY_CUST_TEXT1" not-null="false"/>		
		<property name="beneficiaryCustomer2" column="BENEFICIARY_CUST_TEXT2" not-null="false"/>		
		<property name="beneficiaryCustomer3" column="BENEFICIARY_CUST_TEXT3" not-null="false"/>		
		<property name="beneficiaryCustomer4" column="BENEFICIARY_CUST_TEXT4" not-null="false"/>		
		<property name="beneficiaryCustomer5" column="BENEFICIARY_CUST_TEXT5" not-null="false"/>		
		<property name="senderToReceiverInfo1" column="SENDER_TO_RECEIVER_INFO1" not-null="false"/>	
		<property name="senderToReceiverInfo2" column="SENDER_TO_RECEIVER_INFO2" not-null="false"/>	
		<property name="senderToReceiverInfo3" column="SENDER_TO_RECEIVER_INFO3" not-null="false"/>	
		<property name="senderToReceiverInfo4" column="SENDER_TO_RECEIVER_INFO4" not-null="false"/>	
		<property name="senderToReceiverInfo5" column="SENDER_TO_RECEIVER_INFO5" not-null="false"/>	
		<property name="senderToReceiverInfo6" column="SENDER_TO_RECEIVER_INFO6" not-null="false"/>	
		<property name="updateDate" column="UPDATE_DATE" not-null="false"/>	
		<property name="updateUser" column="UPDATE_USER" not-null="false"/>
		<property name="extraText1" column="EXTRA_TEXT1" not-null="false"/>
		<property name="extraText2" column="EXTRA_TEXT2" not-null="false"/>
		<property name="extraText3" column="EXTRA_TEXT3" not-null="false"/>
		<property name="extraText4" column="EXTRA_TEXT4" not-null="false"/>
		<property name="extraText5" column="EXTRA_TEXT5" not-null="false"/>
		<property name="extraText6" column="EXTRA_TEXT6" not-null="false"/>
		<property name="extraText7" column="EXTRA_TEXT7" not-null="false"/>
		<property name="extraText8" column="EXTRA_TEXT8" not-null="false"/>
		<property name="extraText9" column="EXTRA_TEXT9" not-null="false"/>
		<property name="extraText10" column="EXTRA_TEXT10" not-null="false"/>
		<property name="lastMmaUpdateNote" column="LAST_MMA_UPDATE_NOTE" not-null="false"/>
		<property name="lastMmaUpdateEventId" column="LAST_MMA_UPDATE_EVENT_ID" not-null="false"/>
		
    </class>
</hibernate-mapping>