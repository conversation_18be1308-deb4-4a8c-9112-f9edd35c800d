package org.swallow.work.model;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.LinkedHashMap;

public class CashManagementVO implements Serializable {

	private String host_id;
	private String entityId;
	private String accountId;
	private int period;
	private Date startDate;
	private Date endDate;
	private BigDecimal targetAvgBal;
	private String targetAvgBalAsString;
	private Integer fillInDays;
	private BigDecimal fillInBal;
	private String fillInBalAsString;
	private BigDecimal minTargetBalance;
	private String minTargetBalanceAsString;

	private ArrayList<CashManagementDateRecord> refGrid = new ArrayList<CashManagementDateRecord>();

	public String getHost_id() {
		return host_id;
	}

	public void setHost_id(String host_id) {
		this.host_id = host_id;
	}

	public String getEntityId() {
		return entityId;
	}

	public void setEntityId(String entityId) {
		this.entityId = entityId;
	}

	public String getAccountId() {
		return accountId;
	}

	public void setAccountId(String accountId) {
		this.accountId = accountId;
	}

	public int getPeriod() {
		return period;
	}

	public void setPeriod(int period) {
		this.period = period;
	}

	public Date getStartDate() {
		return startDate;
	}

	public void setStartDate(Date startDate) {
		this.startDate = startDate;
	}

	public Date getEndDate() {
		return endDate;
	}

	public void setEndDate(Date endDate) {
		this.endDate = endDate;
	}

	public BigDecimal getTargetAvgBal() {
		return targetAvgBal;
	}

	public void setTargetAvgBal(BigDecimal targetAvgBal) {
		this.targetAvgBal = targetAvgBal;
	}

	public String getTargetAvgBalAsString() {
		return targetAvgBalAsString;
	}

	public void setTargetAvgBalAsString(String targetAvgBalAsString) {
		this.targetAvgBalAsString = targetAvgBalAsString;
	}

	public Integer getFillInDays() {
		return fillInDays;
	}

	public void setFillInDays(Integer fillInDays) {
		this.fillInDays = fillInDays;
	}

	public BigDecimal getFillInBal() {
		return fillInBal;
	}

	public void setFillInBal(BigDecimal fillInBal) {
		this.fillInBal = fillInBal;
	}

	public String getFillInBalAsString() {
		return fillInBalAsString;
	}

	public void setFillInBalAsString(String fillInBalAsString) {
		this.fillInBalAsString = fillInBalAsString;
	}

	public ArrayList<CashManagementDateRecord> getRefGrid() {
		return refGrid;
	}

	public void setRefGrid(ArrayList<CashManagementDateRecord> refGrid) {
		this.refGrid = refGrid;
	}

	public BigDecimal getMinTargetBalance() {
		return minTargetBalance;
	}

	public void setMinTargetBalance(BigDecimal minTargetBalance) {
		this.minTargetBalance = minTargetBalance;
	}

	public String getMinTargetBalanceAsString() {
		return minTargetBalanceAsString;
	}

	public void setMinTargetBalanceAsString(String minTargetBalanceAsString) {
		this.minTargetBalanceAsString = minTargetBalanceAsString;
	}


	
}
