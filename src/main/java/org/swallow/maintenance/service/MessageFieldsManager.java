/*
 * @(#) MessageFieldsManager.java 20/12/05
 * 
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 * 
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */

package org.swallow.maintenance.service;
import org.swallow.exception.SwtException;
import org.swallow.maintenance.dao.MessageFieldsDAO;
import org.swallow.maintenance.model.MessageFields;
import org.swallow.maintenance.model.ScenarioMessageFields;
import org.swallow.util.SystemInfo;
import java.util.Collection;

public interface MessageFieldsManager {
    /**
     * @param hostId
     * @param entityId
     * @param formatId
     * @return
     * @throws SwtException
     */
    public Collection getMsgFieldDetailList(String hostId, String formatId,
        String entityId) throws SwtException;


    /**
     * @param msgfld
     * @throws SwtException
     */
    public void saveMsgFieldDetails(MessageFields msgfld, SystemInfo systemInfo)
        throws SwtException;

    /**
     * @param msgFieldsDAO
     * @throws SwtException
     */
    public void setMessageFieldsDAO(MessageFieldsDAO msgFieldsDAO)
        throws SwtException;

    /**
     * @param msgfld
     * @throws SwtException
     */
    public void updateMsgFieldDetails(MessageFields msgfld,
        SystemInfo systemInfo) throws SwtException;

    /**
     * @param msgfld
     * @throws SwtException
     */
    public void deleteMessageFieldDetail(MessageFields msgfld,
        SystemInfo systemInfo) throws SwtException;

    /**
     *
     * @throws SwtException
     */
    public Integer getMaxSerialNo(String hostId, String formatId,
        String entityId) throws SwtException;

    /**
     *
     * @return
     * @throws SwtException
     */
    public Collection getKeyWordsList() throws SwtException;
    
    /**
     * Scenario Format methods 
     */
    
    
    
    /**
     * @param hostId
     * @param scenarioId
     * @param formatId
     * @return
     * @throws SwtException
     */
    public Collection getScenarioMsgFieldDetailList(String formatId) throws SwtException;


    /**
     * @param msgfld
     * @throws SwtException
     */
    public void saveScenarioMsgFieldDetails(ScenarioMessageFields msgfld, SystemInfo systemInfo)
        throws SwtException;


    /**
     * @param msgfld
     * @throws SwtException
     */
    public void updateScenarioMsgFieldDetails(ScenarioMessageFields msgfld,
        SystemInfo systemInfo) throws SwtException;

    /**
     * @param msgfld
     * @throws SwtException
     */
    public void deleteScenarioMessageFieldDetail(ScenarioMessageFields msgfld,
        SystemInfo systemInfo) throws SwtException;

    /**
     *
     * @throws SwtException
     */
    public Integer getScenarioMaxSerialNo(String formatId) throws SwtException;

    /**
     *
     * @return
     * @throws SwtException
     */
    public Collection getScenarioKeyWordsList() throws SwtException;
}
