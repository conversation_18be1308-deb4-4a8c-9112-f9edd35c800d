/*
 * @(#)BookCodeAction.java 1.0 21/12/05
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */

package org.swallow.maintenance.service;

import java.util.Collection;

import org.swallow.exception.SwtException;
import org.swallow.maintenance.model.BookCode;


public interface BookCodeManager {
	
    /**
     * This is used to get the Book code details from database and is used in Group and Metagroup action
     * @param hostId
     * @param entityId
     * @param groupId
     * @return Collection
     * @throws SwtException
     */
    public Collection getBookCodeList(String hostId, String entityId,
        String groupId)
        throws SwtException;

    /**
     * This is used to get the book code details from P_BOOKCODE
     * @param hostId
     * @param entityId
     * @return Collection
     * @throws SwtException
     */
	
    public Collection getAllBookCodeList(String hostId, String entityId)
        throws SwtException;

    /**
     * This is used to get the group details from P_GROUP table.
     * @param hostId
     * @param entityId
     * @param groupLevel
     * @return Collection
     * @throws SwtException
     */
    public Collection getGroupLevelDetails(String hostId, String entityId,
        Integer groupLevel)throws SwtException;

    /**
     * This is used to save the bookcode details in P_BOOKCODE table
     * @param bookcode
     * @return 
     * @throws SwtException
     */
    public void saveBookCodeDetail(BookCode bookcode) throws SwtException;

    /**
     * This is used to save the updated book details in P_BOOKCODE table.
     * @param bookcode
     * @return
     * @throws SwtException
     */
    public void updateBookCodeDetail(BookCode bookcode) throws SwtException;

    /**
     * This is used to delete the book details  from P_BOOKCODE table.
     * @param bookcode
     * @return 
     * @throws SwtException
     */
    public void deleteBookCodeDetail(BookCode bookcode) throws SwtException;
    
    /**
     * This is used to edit the required fields in P_BOOKCODE table.
     * @param hostId
     * @param entityId
     * @param groupId
     * @return BookCode
     * @throws SwtException
     */

    public BookCode getEditableData(String hostId, String entityId,
        String groupId) throws SwtException;
    
    /**
     * This is used to get the book code locations from P_LOCATION table
     * @param hostId
     * @param entityId
     * @return Collection
     * @throws SwtException
     */

    public Collection getBookLocations(String hostId, String entityId)
        throws SwtException;

    /**
   	 * This is used to get the book code locations from P_LOCATION table.
   	 * 
   	 * @param locationId
   	 * @return Collection
   	 * @throws SwtException
   	 */
	public Collection getBooksLocationId(String locationId) throws SwtException;
	
    /**
   	 * This is used to check if book code exists for the relevant entity
   	 * 
   	 * @param hostId
   	 * @param entityId
   	 * @throws SwtException
   	 */
	public boolean checkBookCodeAndEntity(String hostId, String entityId, String bookCode) throws SwtException;

	/**
	 * This is used to check if book code exists in p_bookcode table
	 * 
	 * @param hostId 
	 * @param bookCode
	 * @throws SwtException
	 */
	public boolean checkIfBookCodeExists(String hostId, String bookCode) throws SwtException;
	
}
