/*
 * @(#)BalMaintenanceManager.java 1.0 23/08/06
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.maintenance.service;

import java.util.Collection;
import java.util.Date;

import org.swallow.exception.SwtException;
import org.swallow.maintenance.dao.BalMaintenanceDAO;
import org.swallow.maintenance.model.BalMaintenance;
import org.swallow.maintenance.model.BalType;
import org.swallow.util.SystemFormats;

/**
 * <pre>
 * This is the Manager class which contains all the methods, many of which are
 * invoked form front end. 
 * - Display Start of Day screen
 * - Display Balances for different balance types
 * - Display records in pages
 * - View Selected Balances
 * - Change selected Balances
 * - View reports for selected balances 
 * </pre>
 * 
 * @Modified Vivekanandan A / 12-Jan-2012
 */
public interface BalMaintenanceManager {

	/**
	 * Returns collection of balance list from P_BALANCE table.
	 * 
	 * @param entityId
	 * @param hostId
	 * @return Collection
	 * @throws SwtException
	 */
	public Collection getBalanceList(String entityId, String hostId)
			throws SwtException;

	/**
	 * To update the changed balance details in database.
	 * 
	 * @param balmaintenance
	 * @param startBalance
	 * @param hostId
	 * @param entityId
	 * @param balanceType
	 * @param replacebalanceDate
	 * @param sysformat
	 * @throws SwtException
	 */
	public void updateBalanceDetail(BalMaintenance balmaintenance,
			String sodBalance, String hostId, String entityId,
			String balanceType, Date replacebalanceDate, SystemFormats sysformat)
			throws SwtException;

	/**
	 * @param balMaintenanceDAO
	 * @return
	 */
	public void setBalMaintenanceDAO(BalMaintenanceDAO balMaintenanceDAO);

	/* Start : Modified by Vivekanandan A for Mantis 1682 on 13-01-2011 */
	/**
	 * This method receives the editable fields of the balance maintenance
	 * screen If the flag from the baltype is for currency , cash account ,
	 * custodian account it gets the editable fields by currency code on the
	 * screen and if the flag from baltype is custodian it gets editable data.
	 * 
	 * @param selectedCurrencyCode
	 * @param hostId
	 * @param entityId
	 * @param balanceTypeId
	 * @param balType
	 * @param replaceDate
	 * @param sysFormat
	 * @return BalMaintenance
	 * @throws SwtException
	 * 
	 */
	public BalMaintenance getEditableData(String selectedCurrencyCode,
			String hostId, String entityId, String balanceTypeId,
			String balType, Date replaceDate, SystemFormats sysFormat)
			throws SwtException;

	/* End : Modified by Vivekanandan A for Mantis 1682 on 13-01-2011 */

	/**
	 * Get the collection of account detail from the accountDetailStoreProc of
	 * BalmaintenanceDAO
	 * 
	 * @param hostId
	 * @param entityId
	 * @param balanceType
	 * @param currency
	 * @param selectedDate
	 * @param startRowNumber
	 * @param endRowNumber
	 * @param filterValue
	 * @param roleId
	 * @return Collection
	 * @throws SwtException
	 */
	public Collection<BalType> accountDetail(String hostId, String entityId,
			String balanceType, String currency, Date selectedDate,
			int startRowNumber, int endRowNumber, String filterValue,
			String roleId) throws SwtException;

}
