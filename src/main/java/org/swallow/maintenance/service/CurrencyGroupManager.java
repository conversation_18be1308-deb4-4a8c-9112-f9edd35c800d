/*
 * @(#)CurrencyGroupManager.java 1.0 23/08/06
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.maintenance.service;

import java.util.Collection;

import org.swallow.exception.SwtException;
import org.swallow.maintenance.dao.CurrencyGroupDAO;
import org.swallow.maintenance.model.CurrencyGroup;

public interface CurrencyGroupManager {
	/**
	 * 
	 * @param currencyGroupDAO
	 */
	public void setCurrencyGroupDAO(CurrencyGroupDAO currencyGroupDAO);

	/**
	 * Retrieves the currency group list from DAO
	 * 
	 * @param hostId
	 * @param entityId
	 * @return Collection
	 * @throws SwtException
	 */
	public Collection getCurrencyGroupList(String hostId, String entityId)
			throws SwtException;

	/**
	 * Pass the CurrencyGroupBean to the DAO to save new currency group details
	 * in database
	 * 
	 * @param currencyGroup
	 * @return
	 * @throws SwtException
	 */
	public void saveCurrencyGroupDetails(CurrencyGroup currencyGroup)
			throws SwtException;

	/**
	 * Pass the CurrencyGroupBean to the DAO to update the existing currency
	 * group details in database
	 * 
	 * @param currencyGroup
	 * @return
	 * @throws SwtException
	 */
	public void updateCurrencyGroupDetails(CurrencyGroup currencyGroup)
			throws SwtException;

	/**
	 * Pass the CurrencyGroupBean to the DAO to delete the given currency group
	 * details from database
	 * 
	 * @param currencyGroup
	 * @throws SwtException
	 */
	public void deleteCurrencyGroupRecord(CurrencyGroup currencyGroup)
			throws SwtException;

	/**
	 * Get the List of currencies for the entity passed from DAO
	 * 
	 * @param hostId
	 * @param entityId
	 * @param currGrpId
	 * @return Collection
	 * @throws SwtException
	 */
	public Collection getCurrencyGroupCurrenciesList(String hostId,
			String entityId, String currGrpId) throws SwtException;
}
