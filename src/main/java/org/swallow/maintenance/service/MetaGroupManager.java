/*
 * @(#)MetaGroupManager.java 1.0 23/08/06
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */

package org.swallow.maintenance.service;

import java.util.Collection;

import org.swallow.exception.SwtException;
import org.swallow.maintenance.dao.MetaGroupDAO;
import org.swallow.maintenance.model.MetaGroup;


public interface MetaGroupManager {
    /**
     * @param metaGroupDAO
     */
    public void setMetaGroupDAO(MetaGroupDAO metaGroupDAO);

    /**
     * This is used to get the metag roup details from P_METAGROUP & P_GROUP table
     * @param hostId
     * @param entityId
     * @return Collection
     * @throws SwtException
     */
    public Collection getMetaGroupDetails(String hostId, String entityId,Integer mGroupLevel)
        throws SwtException;

    /**
     * This is used to update the meta group details in P_METAGROUP table
     * @param hostId
     * @param entityId
     * @param mgroupId
     * @return Metagroup
     * @throws SwtException
     */
    public MetaGroup change(String hostId, String entityId, String mgroupId)
        
        throws SwtException;

    /**
     * This is used to get the metagroup level from P_METAGROUP_LEVEL table
     * @param hostId
     * @param entityId
     * @return Collection
     * @throws SwtException
     */
    public Collection getMgrpLvlCodeList(String hostId, String entityId)
                throws SwtException;

   /**
     * This is used to save the updated meta group details in P_METAGROUP table
     * @param metaGroup
     * @return none
     * @throws SwtException
     */
    public void updateMetaGroupDetails(MetaGroup metaGroup)
                throws SwtException;

    /**
     * This is used to save the newly added metagroup details in P_METAGROUP table
     * @param metaGroup
     * @throws SwtException
     */
    public void saveMetaGroup(MetaGroup metaGroup) throws SwtException;

    /**
     * This is used to delete the metagroup details from P_METAGROUP table
     * @param metaGroup
     * @throws SwtException
     */
    public void deleteMetaGroup(MetaGroup metaGroup) throws SwtException;

    /**
     * This is used to get the group details from P_METAGROUP, P_GROUP tables
     * @param hostId
     * @param entityId
     * @param mgroupId
     * @return
     */
    public Collection groupDetails(String hostId, String entityId,String mgroupId)
        throws SwtException;
}
