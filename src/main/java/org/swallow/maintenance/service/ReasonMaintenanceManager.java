/*
 * @(#)ReasonMaintenanceManager.java 1.0 25/08/2008
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.maintenance.service;

import java.util.Collection;

import org.swallow.exception.SwtException;
import org.swallow.maintenance.model.ReasonMaintenance;
/**
 * 
 * <AUTHOR> G
 *
 */
public interface ReasonMaintenanceManager {
	
	/**
	 *  Gets the  hostId and entityId from Action class for getting the Reason Code and description from DB.
	 * @param String hostId
	 * @param String entityId
	 * @return Collection object
	 * 
	 */
	public Collection getReasonMaintenanceDetails(String hostId, String entityId) throws SwtException;
	/**
	 *  Gets the data from Reason Maintenance bean object and passing into dao layer to save the data.
	 * @param ReasonMaintenance reasonMaintenance
	 * 
	 */
    public void saveReasonMaintenanceDetails(ReasonMaintenance reasonMaintenance)  throws SwtException;
    /**
	 *  Gets the data from Reason Maintenance bean object and passing into dao layer to update the data.
	 * @param ReasonMaintenance reasonMaintenance
	 * 
	 */       
    public void updateReasonMaintenanceDetails(ReasonMaintenance reasonMaintenance)  throws SwtException;
    /**
	 *  Gets the data from Reason Maintenance bean object and passing into dao layer to delete the record.
	 * @param ReasonMaintenance reasonMaintenance
	 * 
	 */
    public void deleteReasonMaintenanceRecord(ReasonMaintenance reasonMaintenance) throws SwtException;
    
   
}
