/*
 * @(#)DefaultAcctMaintenanceManager.java 20/12/05
 * 
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 * 
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */

package org.swallow.maintenance.service;
import java.util.ArrayList;
import java.util.Collection;
import org.swallow.exception.SwtException;
import org.swallow.maintenance.dao.DefaultAcctMaintenanceDAO;
import org.swallow.maintenance.model.DefaultAcct;
import org.swallow.util.SwtUtil;
import org.swallow.util.SystemFormats;
import org.swallow.util.SystemInfo;

public interface DefaultAcctMaintenanceManager {
	/**
	 * Asks the DAO for a list of DefaultAcct's matching the given entitiyId and hostId
	 * @param entityId
	 * @param hostId
	 * @return Collection
	 * @throws SwtException
	 */
	public Collection getDefaultAccountList (String entityId, String hostId) throws SwtException;
	
	/**
	 * Asks the DAO to delete the given DefaultAcct
	 * @param acct
	 * @throws SwtException
	 */
	public void deleteRecord (DefaultAcct acct) throws SwtException;
	
	/**
	 * Asks the DAO for the record matching the PK data stored in DefaultAcct parameter
	 * @param acct
	 * @return DefaultAcct
	 * @throws SwtException
	 */
	public DefaultAcct getRecord (DefaultAcct acct) throws SwtException;
	
	/**
	 * Returns a collection of open account label-value beans for use in select elements and the like
	 * @param hostId
	 * @param entityId
	 * @param currencyCode
	 * @return Collection
	 * @throws SwtException
	 */
	public Collection getAccountList (String hostId, String entityId, String currencyCode) throws SwtException;
	
	/**
	 * Asks the DAO to save a new copy of the given DefaultAcct object
	 * @param acct
	 * @throws SwtException
	 */
	public void saveRecord (DefaultAcct acct) throws SwtException;
	
	/**
	 * Checks that the given form bean contains a valid accountId and if it does, asks the DAO to update it
	 * @param acct
	 * @throws SwtException
	 */
	public void updateRecord (DefaultAcct acct) throws SwtException;
}
