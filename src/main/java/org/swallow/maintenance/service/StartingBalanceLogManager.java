/*
 * @(#)StartingBalanceLogManager.java 1.0 04/08/06
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.maintenance.service;

import java.util.ArrayList;

import org.swallow.exception.SwtException;
import org.swallow.util.SystemFormats;

public interface StartingBalanceLogManager {

	// Start: Method modified by Vivekanandan A for mantis 1767 20-09-2012
	/**
	 * This method populates records from table and returns maximum number
	 * pages.
	 * 
	 * @param entityId
	 * @param date
	 * @param balanceTypeId
	 * @param currentPAge
	 * @param initialPageCount
	 * @param balLogList
	 * @param filterSortStatus
	 * @param format
	 * @return int
	 */
	public int getBalanceLogDetails(String entityId, String date,
			String balanceTypeId, int currentPage, int initialPageCount,
			ArrayList balLogList, String filterSortStatus, SystemFormats format)
			throws SwtException;

	// End: Method modified by Vive<PERSON>andan A for mantis 1767 20-09-2012
}
