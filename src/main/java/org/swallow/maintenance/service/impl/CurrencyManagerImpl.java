/*
 * @(#)CurrencyManagerimpl.java 1.0 23/08/06
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.maintenance.service.impl;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.Iterator;
import java.util.List;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.maintenance.dao.CurrencyDAO;
import org.swallow.maintenance.model.Currency;
import org.swallow.maintenance.model.CurrencyExchange;
import org.swallow.maintenance.model.MiscParams;
import org.swallow.maintenance.service.CurrencyDetailVO;
import org.swallow.maintenance.service.CurrencyManager;
import org.swallow.util.CacheManager;
import org.swallow.util.LabelValueBean;
import org.swallow.util.SwtConstants;
import org.swallow.util.SwtUtil;
import org.swallow.util.SystemFormats;
/**
 *This is manager class for Currency screen
 */
@Component("currencyManager")
public class CurrencyManagerImpl implements CurrencyManager {
	/**
	 * Initializing logger object for this class
	 */
	private final Log log = LogFactory.getLog(CurrencyManagerImpl.class);
	/**
	 * Used to hold CurrencyDAO reference object
	 */
	@Autowired
	private CurrencyDAO currencyDAO;

	/**
	 * Set the CurrencyDAO
	 * 
	 * @param currencydao
	 * @return
	 */
	public void setCurrencyDAO(CurrencyDAO currencydao) {
		this.currencyDAO = currencydao;
	}

	/**
	 * Collects the currency detail list such as currency name , exchange rate
	 * for the current day and multiplier of the currency
	 * 
	 * @param entityId
	 * @param hostId
	 * @param currencyId
	 * @return CurrencyDeatilVO
	 * @throws SwtException
	 */
	public CurrencyDetailVO getCurrencyDetailList(String entityId,
			String hostId, String currencyId, SystemFormats... sysformat) throws SwtException {
		CurrencyDetailVO currDetailVO = new CurrencyDetailVO();
		log.debug(this.getClass().getName() + " - [getCurrencyDetailList] - "
				+ "Entry");
		
		/* Method's local variable and class instance declaration */
		ArrayList currencyList;
		ArrayList currencyDetailsList;
		Collection collCurre;
		Currency currency;
		ArrayList collExchangeRate;
		CurrencyExchange currencyExchange = null;
		currencyList = new ArrayList();
		currencyDetailsList = new ArrayList();
		/*
		 * Start:Code modified by Venkatesan.A for mantis 348 on 07-09-2010 return
		 * type changed from java.lang.Double to java.math.BigDecimal for
		 * the issue to accomodate the exchangeRate 18-digited huge
		 * value
		 */		
		BigDecimal ccyExchangeRates = null;
		/*
		 * End:Code modified by Venkatesan.A for mantis 348 on 07-09-2010 return
		 * type changed from java.lang.Double to java.math.BigDecimal for
		 * the issue to accomodate the exchangeRate 18-digited huge
		 * value
		 */			
		try {

			/*
			 * Pass the entity id and host id to get the collection of currency
			 * detail list
			 */
			collCurre = currencyDAO.getCurrencyDetailList(entityId, hostId);
			/* Iterate the collection of currency */
			Iterator itr = collCurre.iterator();
			currencyList.add(new LabelValueBean("", ""));
			/* Initializing currency bean */
			currency = new Currency();
			/* Loop to set iterate the currency detail list collection */
			while (itr.hasNext()) {
				currency = (Currency) (itr.next());

				/*
				 * Retrieve the currency and from the currency bean and adding
				 * to the currency collection list as label value bean
				 */
				
				currencyList.add(new LabelValueBean(currency.getCurrencyMaster()
						.getCurrencyName(), currency.getId().getCurrencyCode()));

				/* Condition to check currency id is null */
				if ((currencyId == null) || currencyId.equals("All")
						|| (currency.getId().getCurrencyCode()).equals(currencyId)) {
					/* Collects the exchange rate of the currency from the dao */
					collExchangeRate = (ArrayList) currencyDAO.getTodayExchangeRate(
							entityId, currency.getId().getCurrencyCode());
	
					/*
					 * Condition to check the collection of currency exchange
					 * rate is greater than zero
					 */
					if (collExchangeRate.size() > 0) {
						currencyExchange = new CurrencyExchange();
						/* Set the value to the currency exchange bean */
						currencyExchange = (CurrencyExchange) collExchangeRate
								.get(0);
						/* Set the value to the setExcahngeRate of currency bean */

						/*
						 * Start:Code modified by Venkatesan.A for mantis 348 on 07-09-2010 return
						 * type changed from java.lang.Double to java.math.BigDecimal for
						 * the issue to accomodate the exchangeRate 18-digited huge
						 * value
						 */
						/*Condition to check exchange rate has value*/
						if (currencyExchange.getExchangeRate() != null && !"".equals(currencyExchange.getExchangeRate().toString())) {
							ccyExchangeRates = new BigDecimal(SwtUtil
									.getFormattedCurrencyBig(currencyExchange.getExchangeRate().toString(), "7"));
						} else
						{
							/* If the condition fails set null value */
							ccyExchangeRates = null; 
						}
						/*
						 * End:Code modified by Venkatesan.A for mantis 348 on 07-09-2010 return
						 * type changed from java.lang.Double to java.math.BigDecimal for
						 * the issue to accomodate the exchangeRate 18-digited huge
						 * value
						 */						
						currency.setExchangeRate(ccyExchangeRates);
					} else {
						/* If the condition fails set null value */
						currency.setExchangeRate(null);
					}
					/* Calls the set MultilierDesciption method */
					setMultiplierDescription(currency);
					
					String timeZoneOffset = currencyDAO.getCurrencyTznameAndTzOffset(currency.getId().getCurrencyCode());
					if(!SwtUtil.isEmptyOrNull(timeZoneOffset)) {
						String[] offsetArray = timeZoneOffset.split("\\*\\*");
						if(offsetArray != null && offsetArray.length==2) {
							currency.setGmtOffset(offsetArray[1]);
						}
						
					}
					
					/* Add the currency bean to the currencyDetailListCollection */
					currencyDetailsList.add(currency);
				}
			}
			/* Set the currency list to the currencyDetailVo bean */
			currDetailVO.setCurrencyList(currencyList);
			/* Set the currency list details to the currencyDetailVo bean */
			currDetailVO.setCurrencyListDetails(currencyDetailsList);
			log.debug(this.getClass().getName()
					+ " - [getCurrencyDetailList] - " + "Exit");
		} catch (Exception exp) {
			log.debug(this.getClass().getName()
							+ " - Exception Catched in [getCurrencyDetailList] method : - "
							+ exp.getMessage());
			log	.error(this.getClass().getName()
							+ " - Exception Catched in [getCurrencyDetailList] method : - "
							+ exp.getMessage());
			exp.printStackTrace();
			throw SwtErrorHandler.getInstance().handleException(exp,
					"getCurrencyDetailList", CurrencyManagerImpl.class);
		}
		return currDetailVO;
	}

	/**
	 * set the Multiplier description collected from miscparams
	 * 
	 * @param ccy
	 * @return
	 * @throws SwtException
	 */
	private void setMultiplierDescription(Currency ccy) throws SwtException {
		try {
			log.debug(this.getClass().getName()
					+ " - [setMultiplierDescription] - " + "Entry");

			/* Method's local variable and class instance declaration */
			Collection coll;
			MiscParams mp;
			/* Collects the currency multiplier from cache manager */
			/*Start: code Added/Modified by Arumugam on 03-Dec-2010 for Mantis:0001296- Misc Param to added the entityId field in table and check the entityId */
			coll = (Collection) CacheManager.getInstance().getMiscParams(
					"CURRENCYMULTIPLIER",ccy.getId().getEntityId());
			
			/* Condition to check the collection is not null */
			if ((coll != null) && (coll.size() > 0)) {
				/* Iterate the collection */
				Iterator itr = coll.iterator();
				/* Loop to get each multiplier value from the collection */
				while (itr.hasNext()) {
					mp = (MiscParams) (itr.next());
					/*
					 * Condition to check the multiplier of currency bean is not
					 * null and equal to the par value of Miscparams
					 */
					if ((ccy.getMultiplier() != null)
							&& ccy.getMultiplier().equals(mp.getId().getKey2())) {
						/*
						 * If the condition is true the key2value of miscparams
						 * is set to the currency bean
						 */
						/*Start code modified by Betcy for mantis 1220 on 22-09-10 Change primary key of P_MISC_PARAMS*/
						ccy.setMultiplierDesc(mp.getParValue().trim());
						/*End code modified by Betcy for mantis 1220 on 22-09-10 Change primary key of P_MISC_PARAMS*/
						/*
						 * Condition to check the multiplier description is
						 * equal to curr monitor default multiplier of swt
						 * constatnts
						 */
						if (ccy.getMultiplierDesc().equals(
								SwtConstants.CURR_MONITOR_DEFAULT_MULTIPLIER)) {
							ccy.setMultiplierDesc("");
						}
					}
				}
			}
			/*End: code Added/Modified by Arumugam on 03-Dec-2010 for Mantis:0001296- Misc Param to added the entityId field in table and check the entityId */
			log.debug(this.getClass().getName()
					+ " - [setMultiplierDescription] - " + "Exit");
		} catch (Exception exp) {
			log.debug(this.getClass().getName()
							+ " - Exception Catched in [setMultiplierDescription] method : - "
							+ exp.getMessage());
			log.error(this.getClass().getName()
							+ " - Exception Catched in [setMultiplierDescription] method : - "
							+ exp.getMessage());
			exp.printStackTrace();
			throw SwtErrorHandler.getInstance().handleException(exp,
					"setMultiplierDescription", CurrencyManagerImpl.class);
		}

	}

	/**
	 * Get the currency details for all currencies called in other actions
	 * 
	 * @param entityId
	 * @param hostId
	 * @param currencyId
	 * @return CurrencyDetailVO
	 * @throws SwtException
	 */
	public CurrencyDetailVO getCurrencyDetailListWithAll(String entityId,
			String hostId, String currencyId) throws SwtException {
		log.debug(this.getClass().getName()
				+ " - [getCurrencyDetailListWithAll] - " + "Entry");
		/* Method'slocal variable  class instance declaration */
		CurrencyDetailVO currDetailVO;
		Collection collCurre;
		ArrayList currencyList;
		ArrayList currencyDetailsList;
		Currency currency;
		currDetailVO = new CurrencyDetailVO();
		currencyList = new ArrayList();
		currencyDetailsList = new ArrayList();

		try {

			/* Calling the dao method to get the currency detail list */
			collCurre = currencyDAO.getCurrencyDetailList(entityId, hostId);

			/* Iterate the collection */
			Iterator itr = collCurre.iterator();

			/* Add the label value bean for all in currency list */
			currencyList.add(new LabelValueBean("All", "All"));
			/* Initialize the currency bean */
			currency = new Currency();

			/* Loop to set iterate the currency detail list collection */
			while (itr.hasNext()) {
				currency = (Currency) (itr.next());

				/* Get the currency name from the currency bean */
				currencyList.add(new LabelValueBean(currency.getCurrencyMaster()
						.getCurrencyName(), currency.getId().getCurrencyCode()));

				/*
				 * Condition to check the currency id is null or all or equal to
				 * the currency code
				 */
				if ((currencyId == null) || currencyId.equals("All")
						|| (currency.getId().getCurrencyCode()).equals(currencyId)) {
					/*
					 * Add the currency bean to the currency detail list
					 * collection
					 */
					currencyDetailsList.add(currency);
				}
			}

			/* Set the currency list to the currencyDetailVo bean */
			currDetailVO.setCurrencyList(currencyList);
			/* Set the currency list details to the currencyDetailVo bean */
			currDetailVO.setCurrencyListDetails(currencyDetailsList);
			log.debug(this.getClass().getName()
					+ " - [getCurrencyDetailListWithAll] - " + "Exit");
		} catch (Exception exp) {
			log.debug(this.getClass().getName()
							+ " - Exception Catched in [getCurrencyDetailListWithAll] method : - "
							+ exp.getMessage());

			log.error(this.getClass().getName()
							+ " - Exception Catched in [getCurrencyDetailListWithAll] method : - "
							+ exp.getMessage());
			exp.printStackTrace();
			throw SwtErrorHandler.getInstance().handleException(exp,
					"getCurrencyDetailListWithAll", CurrencyManagerImpl.class);
		}
		return currDetailVO;
	}

	/**
	 * To save the added value to the Database
	 * 
	 * @param currency
	 * @return
	 * @throws SwtException
	 */

	public void saveCurrencyDetail(Currency currency,SystemFormats... sysFormat ) throws SwtException {
		// Variable to hold the startDate
		Date startDate = null;
		// Variable to hold the  endDate
		Date endDate = null;
		try {

			log.debug(this.getClass().getName() + " - [saveCurrencyDetail] - "
					+ "Entry");

			/* Calls the dao method to save the currency bean values */
			currencyDAO.saveCurrencyDetail(currency);
			log.debug(this.getClass().getName() + " - [saveCurrencyDetail] - "
					+ "Exit");
		} catch (Exception exp) {
			/*
			 * Start : Modified for Mantis 1366-Remove entry to log if duplicate
			 * records added by betcy on 18-02-2011
			 */
			log.error(this.getClass().getName()
					+ " - Exception Catched in [saveCurrencyDetail] method : - "
					+ exp.getMessage());
			/*
			 * End : Modified for Mantis 1366-Remove entry to log if duplicate
			 * records added by betcy on 18-02-2011
			 */
			throw SwtErrorHandler.getInstance().handleException(exp,
					"saveCurrencyDetail", CurrencyManagerImpl.class);
		}
	}

	/**
	 * Update the changed value to the DataBase
	 * 
	 * @param currency
	 * @return
	 * @throws SwtException
	 */
	public void updateCurrencyDetail(Currency currency,SystemFormats... sysFormat) throws SwtException {
		
		// Variable to hold the startDate
		Date startDate = null;
		// Variable to hold the  endDate
		Date endDate = null;
		try {
			log.debug(this.getClass().getName() + " - [updateCurrencyDetail] - "
					+ "Entry");
			/* Calling the DAO method to update the currency */
			currencyDAO.updateCurrencyDetail(currency);
			log.debug(this.getClass().getName()
					+ " - [updateCurrencyDetail] - " + "Exit");
		} catch (Exception exp) {
			log.debug(this.getClass().getName()
							+ " - Exception Catched in [updateCurrencyDetail] method : - "
							+ exp.getMessage());

			log	.error(this.getClass().getName()
							+ " - Exception Catched in [updateCurrencyDetail] method : - "
							+ exp.getMessage());
			exp.printStackTrace();
			throw SwtErrorHandler.getInstance().handleException(exp,
					"updateCurrencyDetail", CurrencyManagerImpl.class);
		}

	}

	/**
	 * Delete the selected value from the DataBase
	 * 
	 * @param currency
	 * @return
	 * @throws SwtException
	 */
	public void deleteCurrencyDetail(Currency currency) throws SwtException {
		log.debug(this.getClass().getName() + " - [deleteCurrencyDetail] - "
				+ "Entry");
		try {

			/* Calling the DAO method to delete the currency */
			currencyDAO.deleteCurrencyDetail(currency);
			log.debug(this.getClass().getName()
					+ " - [deleteCurrencyDetail] - " + "Exit");
		} catch (Exception exp) {
			log.debug(this.getClass().getName()
					+ " - Exception Catched in [deleteCurrencyDetail] method : - "
					+ exp.getMessage());

			log.error(this.getClass().getName()
					+ " - Exception Catched in [deleteCurrencyDetail] method : - "
					+ exp.getMessage());
			exp.printStackTrace();
			throw SwtErrorHandler.getInstance().handleException(exp,
					"deleteCurrencyDetail", CurrencyManagerImpl.class);
		}

	}

	/**
	 * Returns to the getCurrencyDeatilList with true for the boolean is all
	 * required field
	 * 
	 * @param entityLVL
	 * @param hostId
	 * @param currencyId
	 * @return CurrencyDetailVO
	 * @throws SwtException
	 * 
	 */
	public CurrencyDetailVO getCurrencyDetailList(Collection entityLVL,
			String hostId, String currencyId) throws SwtException {
		try {
			log.debug(this.getClass().getName()
					+ " - [deleteCurrencyDetail] - " + "returns "
					+ "getCurrencyDetailList with true as param");
			return getCurrencyDetailList(entityLVL, hostId, currencyId, true);
		} catch (Exception exp) {
			log.debug(this.getClass().getName()
					+ " - Exception Catched in [getCurrencyDetailList] method : - "
					+ exp.getMessage());

			log.error(this.getClass().getName()
							+ " - Exception Catched in [getCurrencyDetailList] method : - "
							+ exp.getMessage());
			exp.printStackTrace();
			throw SwtErrorHandler.getInstance().handleException(exp,
					"getCurrencyDetailList", CurrencyManagerImpl.class);
		}
	}

	/**
	 * Get the currency detail list with label value bean for all currencies
	 * values
	 * 
	 * @param entityLVL
	 * @param hostId
	 * @param currencyId
	 * @param isALLRequired
	 * @return CurrencyDetailVO
	 * @throws SwtException
	 * 
	 */
	public CurrencyDetailVO getCurrencyDetailList(Collection entityLVL,
			String hostId, String currencyId, boolean isALLRequired)
			throws SwtException {
		try {
			log.debug(this.getClass().getName()
					+ " - [getCurrencyDetailList] - " + "Entry");
			/* Method's local variable and class instance declaration */
			CurrencyDetailVO currDetailVO;
			ArrayList currencyList;
			ArrayList currencyDetailsList;
			Collection coll;
			Currency cur = null;
			currDetailVO = new CurrencyDetailVO();
			currencyList = new ArrayList();
			currencyDetailsList = new ArrayList();
			/* Get the currency detail list from the dao */
			coll = currencyDAO.getCurrencyDetailList(entityLVL, hostId);
			/* Iterate the collection */
			Iterator itr = coll.iterator();
			/* Condition to check isALLRequired is true */
			if (isALLRequired) {
				currencyList.add(new LabelValueBean(SwtConstants.ALL_LABEL,
						SwtConstants.ALL_VALUE));
			}

			/* Loop to set iterate the currency detail list collection */
			while (itr.hasNext()) {
				cur = (Currency) (itr.next());
				/*
				 * Add the currency id and the currency name from get currency
				 * bean to set in label value bean
				 */
				currencyList.add(new LabelValueBean(cur.getCurrencyMaster()
						.getCurrencyName(), cur.getId().getCurrencyCode()));

				/*
				 * Condition to check currency id is null or all or equal to the
				 * currency code
				 */
				if ((currencyId == null)
						|| currencyId.equals(SwtConstants.ALL_VALUE)
						|| (cur.getId().getCurrencyCode()).equals(currencyId)) {
					/*
					 * Add the currency bean to the collection of
					 * currencyDetailsList
					 */
					currencyDetailsList.add(cur);
				}
			}

			/* Set the currency list to the currencyDetailVo bean */
			currDetailVO.setCurrencyList(currencyList);
			/* Set the currency list details to the currencyDetailVo bean */
			currDetailVO.setCurrencyListDetails(currencyDetailsList);
			log.debug(this.getClass().getName()+ " - [getCurrencyDetailList] - " + "Exit");
			return currDetailVO;
		} catch (Exception exp) {
			log.debug(this.getClass().getName()
					+ " - Exception Catched in [getCurrencyDetailList] method : - "
					+ exp.getMessage());
			log.error(this.getClass().getName()
					+ " - Exception Catched in [getCurrencyDetailList] method : - "
					+ exp.getMessage());
			exp.printStackTrace();
			throw SwtErrorHandler.getInstance().handleException(exp,
					"getCurrencyDetailList", CurrencyManagerImpl.class);
		}
	}
	
	
	/**
	 * @param hostId
	 * @param currencyCode
	 * @return String
	 * @throws SwtException 
	 */
	public boolean checkIfCurrencyExists(String hostId,String currencyCode,String entity) 
			throws SwtException{
		log.debug("Entering 'checkIfCurrencyExists' method");
		try {
			
			return currencyDAO.checkIfCurrencyExists(hostId,currencyCode,entity);
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [checkIfCurrencyExists] method : - "
					+ exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp,
					"checkIfCurrencyExists", CurrencyManagerImpl.class);
		}	
		
	}
	
	
	/**
	 * This method is used to get the currency tolerance
	 */

	public String getCcyTolerance(String hostId, String ccy ,String entity)
			throws SwtException {
		log.debug("Entering 'getCcyTolerance' method");
		try {
			
			return currencyDAO.getCcyTolerance(hostId,ccy,entity);
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [getCcyTolerance] method : - "
					+ exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp,
					"getCcyTolerance", CurrencyManagerImpl.class);
		}	
		
	}
	
	/**
	 * This method is used to get the currency Tzname And tz_offset
	 */
	public String getCurrencyTznameAndTzOffset(String currencyCode) throws SwtException {
		String rtn = "";
		if (!currencyCode.equalsIgnoreCase("All")) {
			rtn = currencyDAO.getCurrencyTznameAndTzOffset(currencyCode);
		}
		return rtn;
	}

}
