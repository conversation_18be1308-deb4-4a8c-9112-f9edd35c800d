/*
 * @(#)LocationMaintenanceManagerImpl.java 1.0 23/08/06
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.maintenance.service.impl;

import java.util.Collection;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.maintenance.dao.LocationMaintenanceDAO;
import org.swallow.maintenance.model.Location;
import org.swallow.maintenance.service.LocationMaintenanceManager;

/**
 * 
 * This is manager class for Location screen.
 * 
 */
@Component ("locationMaintenanceManager")
public class LocationMaintenanceManagerImpl implements
		LocationMaintenanceManager {
	/**
	 * Initializing logger object for this class
	 */
	private final Log log = LogFactory
			.getLog(LocationMaintenanceManagerImpl.class);
	/**
	 * Initializing LocationMaintenanceDAO object for this class
	 */
	@Autowired
	private LocationMaintenanceDAO locationDAO;

	/**
	 * Method to set Location Maintenance DAO
	 * 
	 * @param locationDAO
	 * @return
	 */
	public void setLocationMaintenanceDAO(LocationMaintenanceDAO locationDAO) {
		this.locationDAO = locationDAO;
	}

	
	public Collection getLocationIdDetails(String locationId)
			throws SwtException {
		try {
			log.debug(this.getClass().getName() + " - [getLocationIdDetails] - "
					+ "Entry");

			/* Retrieves the collection of Location details from dao */
			Collection noofRecords = locationDAO.getLocationIdDetails(locationId);
			log.debug(this.getClass().getName() + " - [getLocationIdDetails] - "
					+ "Exit");
			return noofRecords;
		} catch (Exception exp) {
			log.debug(this.getClass().getName()
							+ " - Exception Catched in [getLocationIdDetails] method : - "
							+ exp.getMessage());

			log.error(this.getClass().getName()
							+ " - Exception Catched in [getLocationIdDetails] method : - "
							+ exp.getMessage());

			exp.printStackTrace();
			throw SwtErrorHandler.getInstance().handleException(exp,
					"getLocationIdDetails", LocationMaintenanceManagerImpl.class);
		}
		
	}
	/**
	 * Returns the collection of location details for an entity to display into
	 * the screen
	 * 
	 * @param hostId
	 * @param entityId
	 * @return Collection
	 * @throws SwtException
	 */
	public Collection getLocationDetails(String hostId, String entityId)
			throws SwtException {
		try {
			log.debug(this.getClass().getName() + " - [getLocationDetails] - "
					+ "Entry");
			/* Retrieves the collection of Location details from dao */
			Collection noofRecords = locationDAO.getLocationDetails(hostId,
					entityId);
			log.debug(this.getClass().getName() + " - [getLocationDetails] - "
					+ "Exit");
			return noofRecords;
		} catch (Exception exp) {
			log.debug(this.getClass().getName()
							+ " - Exception Catched in [getLocationDetails] method : - "
							+ exp.getMessage());

			log.error(this.getClass().getName()
							+ " - Exception Catched in [getLocationDetails] method : - "
							+ exp.getMessage());

			exp.printStackTrace();
			throw SwtErrorHandler.getInstance().handleException(exp,
					"getLocationDetails", LocationMaintenanceManagerImpl.class);
		}
	}

	/**
	 * Save the location details in to the data base through DAO
	 * 
	 * @param location
	 * @return
	 * @throws SwtException
	 */
	public void saveLocationDetail(Location location) throws SwtException {

		log.debug(this.getClass().getName() + " - [saveLocationDetail] - "
				+ "Entry");

		try {

			/* Pass the location bean to save method of DAO */
			locationDAO.saveLocationDetail(location);

			log.debug(this.getClass().getName() + " - [saveLocationDetail] - "
					+ "Exit");
		} catch (Exception exp) {
			log.error(this.getClass().getName()
							+ " - Exception Catched in [saveLocationDetail] method : - "
							+ exp.getMessage());
			throw SwtErrorHandler.getInstance()
					.handleException(exp, "saveLocationDetails",
							LocationMaintenanceManagerImpl.class);
		}

	}

	/**
	 * Update the location details in to the data base through DAO
	 * 
	 * @param location
	 * @return
	 * @throws SwtException
	 */
	public void updateLocationDetail(Location location) throws SwtException {

		log.debug(this.getClass().getName() + " - [updateLocationDetail] - "
				+ "Entry");

		try {
			/* Pass the location bean to the update method of location DAO */
			locationDAO.updateLocationDetail(location);

			log.debug(this.getClass().getName()
					+ " - [updateLocationDetail] - " + "Exit");

		} catch (Exception exp) {
			log.debug(this.getClass().getName()
							+ " - Exception Catched in [updateLocationDetail] method : - "
							+ exp.getMessage());

			log.error(this.getClass().getName()
							+ " - Exception Catched in [updateLocationDetail] method : - "
							+ exp.getMessage());

			exp.printStackTrace();
			throw SwtErrorHandler.getInstance().handleException(exp,
					"updateLocationDetail",
					LocationMaintenanceManagerImpl.class);
		}
	}

	/**
	 * Delete the location details from the data base through DAO
	 * 
	 * @param location
	 * @return
	 * @throws SwtException
	 */
	public void deleteLocationDetail(Location location) throws SwtException {
		log.debug(this.getClass().getName() + " - [deleteLocationDetail] - "
				+ "Entry");

		try {

			/* Passing the location bean to the dlete method of DAO */
			locationDAO.deleteLocationDetail(location);

			log.debug(this.getClass().getName()
					+ " - [deleteLocationDetail] - " + "Exit");
		} catch (Exception exp) {
			log.debug(this.getClass().getName()
							+ " - Exception Catched in [deleteLocationDetail] method : - "
							+ exp.getMessage());

			log.error(this.getClass().getName()
							+ " - Exception Catched in [deleteLocationDetail] method : - "
							+ exp.getMessage());

			exp.printStackTrace();
			throw SwtErrorHandler.getInstance().handleException(exp,
					"deleteLocationDetail",
					LocationMaintenanceManagerImpl.class);
		}
	}

	/**
	 * Get the location id alone that access rights from the database
	 * 
	 * @param roleId
	 * @param hostId
	 * @return Collection
	 * @throws SwtException
	 */
	public Collection getLocationIdFromDB(String hostId, String roleId)
			throws SwtException {
		try {
			log.debug(this.getClass().getName() + " - [getLocationIdFromDB] - "
					+ "Entry");
			/* Retrieve the location details from database */
			Collection noofRecords = locationDAO.getLocationIdFromDB(hostId,
					roleId);
			log.debug(this.getClass().getName() + " - [getLocationIdFromDB] - "
					+ "Exit");
			return noofRecords;
		} catch (Exception exp) {
			log.debug(this.getClass().getName()
							+ " - Exception Catched in [getLocationIdFromDB] method : - "
							+ exp.getMessage());

			log.error(this.getClass().getName()
							+ " - Exception Catched in [getLocationIdFromDB] method : - "
							+ exp.getMessage());

			exp.printStackTrace();
			throw SwtErrorHandler.getInstance()
					.handleException(exp, "getLocationIdFromDB",
							LocationMaintenanceManagerImpl.class);
		}
	}

	/**
	 * Method to collect location details for the given hostId
	 * 
	 * @param hostId
	 * @return Collection
	 * @throws SwtException
	 */
	public Collection getAllLocationDetails(String hostId) throws SwtException {
		try {
			log.debug(this.getClass().getName()
					+ " - [getAllLocationDetails] - " + "Entry");

			Collection noofRecords = locationDAO.getAllLocationDetails(hostId);

			log.debug(this.getClass().getName()
					+ " - [getAllLocationDetails] - " + "Exit");

			return noofRecords;
		} catch (Exception exp) {
			log.debug(this.getClass().getName()
							+ " - Exception Catched in [getAllLocationDetails] method : - "
							+ exp.getMessage());

			log.error(this.getClass().getName()
							+ " - Exception Catched in [getAllLocationDetails] method : - "
							+ exp.getMessage());

			exp.printStackTrace();
			throw SwtErrorHandler.getInstance().handleException(exp,
					"getAllLocationDetails",
					LocationMaintenanceManagerImpl.class);
		}
	}

}
