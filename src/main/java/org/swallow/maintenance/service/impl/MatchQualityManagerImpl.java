/*
 * @(#)MatchQualityMangerImpl.java 1.0 23/08/06
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech. 
 */
package org.swallow.maintenance.service.impl;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Iterator;
import java.util.List;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.maintenance.dao.MatchQualityDAO;
import org.swallow.maintenance.dao.QualityActionDAO;
import org.swallow.maintenance.model.MatchQuality;
import org.swallow.maintenance.model.QualityAction;
import org.swallow.maintenance.service.MatchQualityManager;
import org.swallow.util.LabelValueBean;
import org.swallow.util.PropertiesFileLoader;
import org.swallow.util.SwtConstants;

/**
 * MatchQualityManagerImpl.java<br>
 * 
 * This class implements the MatchQualityManager interface methods that are used
 * to Collect the Match list from DAO, also to get the position level(s) for the
 * given entity which belongs to the given host and with the given currency
 * code.<br>
 * 
 * @Modified: Marshal on 31-March-2011
 */
@Component("matchQualityManager")
public class MatchQualityManagerImpl implements MatchQualityManager {

	/**
	 * Used to hold MatchQualityDAO reference object
	 */
	@Autowired
	private MatchQualityDAO matchQualityDAO;
	/**
	 * Used to hold QualityActionDAO reference object
	 */
	@Autowired
	private QualityActionDAO qualityActionDAO;
	/**
	 * Initializing logger object for this class
	 */
	private final Log log = LogFactory.getLog(MatchQualityManagerImpl.class);

	/**
	 * Set MachQuallityDAO
	 * 
	 * @param MatchQualityDAO
	 * @return None
	 */
	public void setMatchQualityDAO(MatchQualityDAO dao) {
		this.matchQualityDAO = dao;
	}

	/**
	 * @param qualityActionDAO
	 *            The qualityActionDAO to set.
	 */
	public void setQualityActionDAO(QualityActionDAO qualityActionDAO) {
		this.qualityActionDAO = qualityActionDAO;
	}

	/**
	 * Collect the Match list from DAO and return to Action
	 * 
	 * @param entityId
	 * @param hostId
	 * @return Collection
	 * @throws SwtException
	 */
	public Collection getMatchList(String entityId, String hostId)
			throws SwtException {

		log.debug(this.getClass().getName() + " - [getMatchList] - " + "Entry");

		/* Method's class instance declaration */
		Collection collMatch;
		MatchQuality matchQualiy = null;
		MatchQuality matchQualiyLast = null;
		Collection outColl;

		outColl = new ArrayList();

		/* Collect the match list from DAO */
		collMatch = matchQualityDAO.getMatchList(entityId, hostId);
		/* Condition to check collection is not null */
		if (collMatch != null) {
			/* Iterate the collection */
			Iterator itr = collMatch.iterator();

			/* Loop to get each value from the iterator */
			while (itr.hasNext()) {
				/* Get the values from iterator in matchQuality bean */
				matchQualiy = (MatchQuality) itr.next();

				outColl.add(matchQualiy);

			}
		}

		log.debug(this.getClass().getName() + " - [getMatchList] - " + "Exit");
		return outColl;
	}

	/**
	 * Get Parameter Description from DAO and return the collection
	 * 
	 * @return Collection
	 * @throws SwtException
	 */
	public Collection getParamsDescAll() throws SwtException {
		log.debug(this.getClass().getName() + " - [getParamsDescAll] - "
				+ "Entry");
		Collection collParam = matchQualityDAO.getParamDescAll();
		log.debug(this.getClass().getName() + " - [getParamsDescAll] - "
				+ "Exit");
		return collParam;
	}

	/**
	 * Pass the matchQuality bean to the matchQualityDAO
	 * 
	 * @param collNatchList
	 * @param matchQualityObj
	 * @return None
	 * @throws SwtException
	 */
	public void addMatchList(Collection collNatchList,
			MatchQuality matchQualityObj) throws SwtException {
		try {
			log.debug(this.getClass().getName() + " - [addMatchList] - "
					+ "Entry");

			/* Method's class instance declaration */

			QualityAction qualityActionObj;

			/* Condition to check collectionNatchList is null */
			if (collNatchList == null) {
				/* Return empty */
				return;
			}

			/* Iterate the collectionNatchList */
			Iterator itr = collNatchList.iterator();

			/* Loop till the iterate has the value */
			while (itr.hasNext()) {
				/* Get the value in QualityAction bean from iterator */
				qualityActionObj = (QualityAction) (itr.next());

				/* condition to check QualityAction bean is not null */
				if (qualityActionObj != null) {
					/*
					 * pass the QualityAction bean to the addMatchQuality of
					 * Match quality DAo
					 */
					matchQualityDAO.addMatchQuality(qualityActionObj);
				}
			}

			/*
			 * Pass the matchQualityObj bean to the save method of Quality
			 * action DAO
			 */
			qualityActionDAO.save(matchQualityObj);

			log.debug(this.getClass().getName() + " - [addMatchList] - "
					+ "Exit");
		} catch (Exception exp) {
			/*
			 * Start : Modified for Mantis 1366-Remove entry to log if duplicate
			 * records added by betcy on 18-02-2011
			 */
			log.error(this.getClass().getName()
					+ " - Exception Catched in [addMatchList] method : - "
					+ exp.getMessage());
			/*
			 * End : Modified for Mantis 1366-Remove entry to log if duplicate
			 * records added by betcy on 18-02-2011
			 */
			throw SwtErrorHandler.getInstance().handleException(exp,
					"addMatchList", MatchQualityManagerImpl.class);
		}
	}

	/**
	 * Update the Match Quality list through DAO
	 * 
	 * @param collNatchList
	 * @param matchQualityObj
	 * @return None
	 * @throws SwtException
	 */
	public void updateMatchList(Collection collNatchList,
			MatchQuality matchQualityObj) throws SwtException {
		try {
			log.debug(this.getClass().getName() + " - [updateMatchList] - "
					+ "Entry");

			/* Method's class instance declaration */

			QualityAction qualityActionObj;

			/* Condition to check collectionNatchList is null */
			if (collNatchList == null) {
				return;
			}

			/* Iterate the collectionNatchList */
			Iterator itr = collNatchList.iterator();

			/* Loop till the iterate has the value */
			while (itr.hasNext()) {
				/* Get the value in QualityAction bean from iterator */
				qualityActionObj = (QualityAction) (itr.next());

				/* condition to check qualityAction bean is not null */
				if (qualityActionObj != null) {
					/*
					 * pass the MatchQuality bean to the addMatchQuality of
					 * Match quality DAO
					 */
					matchQualityDAO.updateMatchQuality(qualityActionObj);
				}
			}

			/*
			 * Pass the matchQuality bean to the save method of Quality action
			 * DAO
			 */
			qualityActionDAO.update(matchQualityObj);

			log.debug(this.getClass().getName() + " - [updateMatchList] - "
					+ "Exit");
		} catch (Exception exp) {
			log.debug(this.getClass().getName()
					+ " - Exception Catched in [updateMatchList] method : - "
					+ exp.getMessage());

			log.error(this.getClass().getName()
					+ " - Exception Catched in [updateMatchList] method : - "
					+ exp.getMessage());
			exp.printStackTrace();
			throw SwtErrorHandler.getInstance().handleException(exp,
					"updateMatchList", MatchQualityManagerImpl.class);
		}
	}

	/**
	 * Collects the Quality action from QualityActionDAO and return the
	 * collection
	 * 
	 * @param hostId
	 * @param entityId
	 * @param currencyCode
	 * @param posLevel
	 * @return Collection
	 * @throws SwtException
	 */

	public Collection getQualityAction(String hostId, String entityId,
			String currencyCode, Integer posLevel) throws SwtException {

		log.debug(this.getClass().getName() + " - [getQualityAction] - "
				+ "Entry");

		Collection qualityActionObj = qualityActionDAO.getQualityAction(hostId,
				entityId, currencyCode, posLevel);

		log.debug(this.getClass().getName() + " - [getQualityAction] - "
				+ "Exit");

		return qualityActionObj;
	}

	/**
	 * Collect the match QualityList from MatchQualityDAO and returns the
	 * MatchQuality bean
	 * 
	 * @param hostId
	 * @param entityId
	 * @param currencyCode
	 * @param posLevel
	 * @return MatchQuality
	 * @throws SwtException
	 */
	public MatchQuality getMatchQualityList(String hostId, String entityId,
			String currencyCode, Integer posLevel) throws SwtException {

		log.debug(this.getClass().getName() + " - [getMatchQualityList] - "
				+ "Entry");

		/* get the MathqualityList from MatchQualityDAO */
		MatchQuality qualityActionObj = matchQualityDAO.getMatchQualityList(
				hostId, entityId, currencyCode, posLevel);

		log.debug(this.getClass().getName() + " - [getMatchQualityList] - "
				+ "Exit");
		return qualityActionObj;
	}

	/**
	 * Delete the MatchQuality through DAO
	 * 
	 * @param matchQualityObj
	 * @param qualityActionObj
	 * @return None
	 * @throws SwtException
	 */
	public void deleteMatchQuality(MatchQuality matchQualityObj,
			QualityAction qualityActionObj) throws SwtException {
		/*
		 * START:code modified by Mahesh on 13-Oct-2009 for Mantis 1034: changed
		 * for loop max value from 9 to 15
		 */
		int maxParamCounter = 0;
		/*
		 * END:code modified by Mahesh on 13-Oct-2009 for Mantis 1034: changed
		 * for loop max value from 9 to 15
		 */
		try {

			log.debug(this.getClass().getName() + " - [deleteMatchQuality] - "
					+ "Entry");
			/*
			 * START:code added/modified by Mahesh on 13-Oct-2009 for Mantis
			 * 1034: changed for loop max value from 9 to 15
			 */
			maxParamCounter = Integer.parseInt(PropertiesFileLoader
					.getInstance().getPropertiesValue(
							SwtConstants.TOTAL_PARAMETER_COUNT));
			/* Pass the MatchQuality bean to the delete method of DAO */
			matchQualityDAO.deleteMatchQuality(matchQualityObj);

			for (int i = 1; i <= maxParamCounter; i++) {
				if (i == maxParamCounter)
					qualityActionObj.getId().setParameterId(15);
				else
					qualityActionObj.getId().setParameterId(new Integer(i));
				/*
				 * END:code modified by Mahesh on 13-Oct-2009 for Mantis 1034:
				 * changed for loop max value from 9 to 15
				 */
				qualityActionDAO.delete(qualityActionObj);
			}

			log.debug(this.getClass().getName() + " - [deleteMatchQuality] - "
					+ "Exit");
		} catch (Exception exp) {
			log
					.debug(this.getClass().getName()
							+ " - Exception Catched in [deleteMatchQuality] method : - "
							+ exp.getMessage());

			log
					.error(this.getClass().getName()
							+ " - Exception Catched in [deleteMatchQuality] method : - "
							+ exp.getMessage());
			exp.printStackTrace();
			throw SwtErrorHandler.getInstance().handleException(exp,
					"deleteMatchQuality", MatchQualityManagerImpl.class);
		}
	}

	/*
	 * Start: Code modified for Mantis 1238: Summary[Copy Match Qualities
	 * functionality generates error] - by Marshal on 31-Mar-2011
	 */

	/**
	 * This method is used to get the position level(s) for the given entity
	 * which belongs to the given host and with the given currency code.<br>
	 * 
	 * @param hostId
	 * @param entityId
	 * @param currencyCode
	 * @return Collection
	 * @throws SwtException
	 */
	public Collection<LabelValueBean> getfilterPositionlevel(String hostId,
			String entityId, String currencyCode) throws SwtException {
		// Declares the position index
		int positionIndex = 0;
		// Declares the position level
		String positionLevel = null;
		// Declares the collection of position levels
		List<Integer> collPositions = null;
		// Declares the collection of filter position levels
		ArrayList<LabelValueBean> filterPosCol = null;
		try {
			log.debug(this.getClass().getName()
					+ " - [getfilterPositionlevel] - " + "Entry");
			// Creates a new instance of filterPosCol object
			filterPosCol = new ArrayList<LabelValueBean>();

			// Calls the helper method and gets the List of filterPositionLevel
			// from MatchQualityDAO
			collPositions = castGenericList(Integer.class, matchQualityDAO
					.getfilterPositionlevel(hostId, entityId, currencyCode));

			// Adds the LabelValueBean with the filterPosCol list
			filterPosCol.add(new LabelValueBean("", ""));

			// loop if the collection has at least one value
			while (positionIndex < collPositions.size()) {
				// Collects the string value of the collection
				positionLevel = collPositions.get(positionIndex).toString();
				// Adds the label value bean of position
				filterPosCol.add(new LabelValueBean(positionLevel,
						positionLevel));
				// Increments position index value
				positionIndex++;
			}
			log.debug(this.getClass().getName()
					+ " - [getfilterPositionlevel] - " + "Exit");
		} catch (Exception exception) {
			log
					.error(this.getClass().getName()
							+ " - Exception Catched in [getfilterPositionlevel] method : - "
							+ exception.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exception,
					"getfilterPositionlevel", MatchQualityManagerImpl.class);
		} finally {
			// Cleaning the object that are not referenced anymore
			positionLevel = null;
			try {
				if (collPositions != null) {
					collPositions = null;
				}
			} catch (Exception ignore) {
				log
						.error(this.getClass().getName()
								+ " - Exception Catched in [getfilterPositionlevel] method while cleaning collPositoins : - "
								+ ignore.getMessage());
			}
		}
		return filterPosCol;
	}

	/*
	 * End: Code modified for Mantis 1238: Summary[Copy Match Qualities
	 * functionality generates error] - by Marshal on 31-Mar-2011
	 */

	/*
	 * Start: Code added for Mantis 1238: Summary[Copy Match Qualities
	 * functionality generates error] - by Marshal on 31-Mar-2011
	 */
	/**
	 * This is a generic method that checks the elements indeed have the correct
	 * element type by means of a cast.<br>
	 * 
	 * @param <GenericType>
	 * @param clazz
	 * @param collection
	 * @return genericList: List<GenericType>
	 */
	private <GenericType> List<GenericType> castGenericList(
			Class<? extends GenericType> clazz, Collection<?> collection) {
		// Declares the genericList(List) object
		List<GenericType> genericList = null;
		try {
			log.debug(this.getClass().getName() + " - [castList] - " + "Entry");
			// Creates a new instance of genericList object
			genericList = new ArrayList<GenericType>(collection.size());
			// Iterates and casts the object with the genericList object
			for (Object object : collection)
				genericList.add(clazz.cast(object));
			log.debug(this.getClass().getName() + " - [castList] - " + "Exit");
		} catch (Exception exception) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [castList] method : - "
					+ exception.getMessage());
		}
		return genericList;
	}
	/*
	 * End: Code added for Mantis 1238: Summary[Copy Match Qualities
	 * functionality generates error] - by Marshal on 31-Mar-2011
	 */
}