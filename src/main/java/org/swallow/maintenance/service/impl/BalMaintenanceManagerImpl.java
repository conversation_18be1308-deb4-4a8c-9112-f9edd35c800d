/*
 * @(#)BalMaintenanceManagerImpl.java 1.0 23/08/06
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.maintenance.service.impl;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.Iterator;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.maintenance.dao.BalMaintenanceDAO;
import org.swallow.maintenance.model.BalMaintenance;
import org.swallow.maintenance.model.BalType;
import org.swallow.maintenance.service.BalMaintenanceManager;
import org.swallow.util.LabelValueBean;
import org.swallow.util.SwtUtil;
import org.swallow.util.SystemFormats;

/**
 * <pre>
 * This is the Manager class which contains all the methods, many of which are
 * invoked form front end. 
 * - Display Start of Day screen
 * - Display Balances for different balance types
 * - Display records in pages
 * - View Selected Balances
 * - Change selected Balances
 * - View reports for selected balances 
 * </pre>
 * 
 * @Modified Vivekanandan A / 12-Jan-2012
 */
@Component ("balMaintenanceManager")
public class BalMaintenanceManagerImpl implements BalMaintenanceManager {
	/**
	 * Initializing logger object for this class
	 */
	private final Log log = LogFactory.getLog(BalMaintenanceManagerImpl.class);
	/**
	 * Instance declared for BalMaintenanceDAO
	 */
	@Autowired
	private BalMaintenanceDAO balMaintDAO;

	/**
	 * Method to setBalMaintenanceDAO
	 * 
	 * @param balMaintenanceDAO
	 * @return
	 */
	public void setBalMaintenanceDAO(BalMaintenanceDAO balMaintenanceDAO) {
		this.balMaintDAO = balMaintenanceDAO;
	}

	/**
	 * Returns the collection of balance list from P_BALANCE table
	 * 
	 * @param hostId
	 * @param entityId
	 * @return Collection
	 * @throws SwtException
	 */
	public Collection getBalanceList(String hostId, String entityId)
			throws SwtException {
		log.debug(this.getClass().getName() + " - [getBalanceList] - "
				+ "Entry");

		/* Method's local variable and class instance declaration */
		ArrayList balMaintList;
		Collection collbalMaint = null;
		BalMaintenance balMaintenance;
		Iterator itr;
		balMaintList = new ArrayList();

		try {
			/* Retrieve balance list from DB based on the parameters passed */
			collbalMaint = balMaintDAO.getBalanceList(hostId, entityId);
			/* Iterate the collection get from DAO */
			itr = collbalMaint.iterator();

			/* Add the label value bean to the arrayList */
			balMaintList.add(new LabelValueBean("Please Select", ""));

			balMaintenance = new BalMaintenance();

			/* Loop until the last value of iterator */
			while (itr.hasNext()) {

				/* Store the values in balMaintenance bean */
				balMaintenance = (BalMaintenance) (itr.next());

				/* Add the label value bean of balance type to the array list */
				balMaintList.add(new LabelValueBean(balMaintenance
						.getBalanceType(), balMaintenance.getBalanceType()));
				log.debug(this.getClass().getName() + " - [getBalanceList] - "
						+ "Exit");
			}
		} catch (Exception exp) {

			log.error(this.getClass().getName()
					+ " - Exception Catched in [getBalanceList] method : - "
					+ exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp,
					"getBalanceList", BalMaintenanceManagerImpl.class);
		}

		return balMaintList;
	}

	/**
	 * To update the changed balance detail in the data base table
	 * 
	 * @param balmaintenance
	 * @param startBalance
	 * @param hostId
	 * @param entityId
	 * @param balanceType
	 * @param replacebalanceDate
	 * @param sysformat
	 * @throws SwtException
	 */
	public void updateBalanceDetail(BalMaintenance balmaintenance,
			String sodBalance, String hostId, String entityId,
			String balanceType, Date replacebalanceDate, SystemFormats sysformat)
			throws SwtException {
		log.debug(this.getClass().getName() + " - [updateBalanceList] - "
				+ "Entry");

		try {
			balMaintDAO.updateBalanceDetail(balmaintenance, sodBalance, hostId,
					entityId, balanceType, replacebalanceDate);
			log.debug(this.getClass().getName() + " - [updateBalanceList] - "
					+ "Exit");
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [updateBalanceList] method : - "
					+ exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp,
					"updateBalanceDetail", BalMaintenanceManagerImpl.class);
		}
	}

	/* Start : Modified by Vivekanandan A for Mantis 1682 on 13-01-2011 */

	/**
	 * This method receives the editable fields of the balance maintenance
	 * screen If the flag from the baltype is for currency , cash account ,
	 * custodian account it gets the editable fields by currency code on the
	 * screen and if the flag from baltype is custodian it gets editable data by
	 * balance type id.
	 * 
	 * @param selectedCurrencyCode
	 * @param hostId
	 * @param entityId
	 * @param balanceTypeId
	 * @param balType
	 * @param replaceDate
	 * @param sysFormat
	 * @return BalMaintenance
	 */
	public BalMaintenance getEditableData(String selectedCurrencyCode,
			String hostId, String entityId, String balanceTypeId,
			String balType, Date replaceDate, SystemFormats sysFormat)
			throws SwtException {
		/* Method's class instance declaration */
		// Balmaintenance bean to hold balance values
		BalMaintenance balMaint = null;
		try {
			log.debug(this.getClass().getName()
					+ " - [getEditableData] - Entry");

			
			/* Fetches editable data from database. */
			balMaint = balMaintDAO.getEditableData(hostId, entityId,
					balanceTypeId, balType, replaceDate);
			/* Set Replace update date in BalMaintenance bean */
			balMaint.setReplaceDate(SwtUtil.formatDate(
					balMaint.getUpdateDate(), sysFormat.getDateFormatValue()));

			/* Set the balance date in the given date format value */
			balMaint.setBalancedateAsString(SwtUtil.formatDate(balMaint.getId()
					.getBalanceDate(), sysFormat.getDateFormatValue()));
			log
					.debug(this.getClass().getName()
							+ " - [getEditableData] - Exit");
			return balMaint;
		} catch (SwtException swtExp) {

			log.error(this.getClass().getName()
					+ " - Exception Catched in [getEditableData] method : - "
					+ swtExp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(swtExp,
					"getEditableData", BalMaintenanceManagerImpl.class);
		} catch (Exception exp) {

			log.error(this.getClass().getName()
					+ " - Exception Catched in [getEditableData] method : - "
					+ exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp,
					"getEditableData", BalMaintenanceManagerImpl.class);
		}

	}

	/* End : Modified by Vivekanandan A for Mantis 1682 on 13-01-2011 */

	/**
	 * Get the collection of account detail from the accountDetailStoreProc of
	 * BalmaintenanceDAO
	 * 
	 * @param hostId
	 * @param entityId
	 * @param balanceType
	 * @param currency
	 * @param selectedDate
	 * @param startRowNumber
	 * @param endRowNumber
	 * @param filterValue
	 * @param roleId
	 * @return Collection
	 * @throws SwtException
	 * 
	 */
	public Collection<BalType> accountDetail(String hostId, String entityId,
			String balanceType, String currency, Date selectedDate,
			int startRowNumber, int endRowNumber, String filterValue,
			String roleId) throws SwtException {
		// collection to hold the balance maintenance details
		Collection<BalType> coll = null;
		try {
			log.debug(this.getClass().getName() + " - [accountDetail] - "
					+ "Entry");
			/* Fetches balance type details from database. */
			coll = balMaintDAO.accountDetailUsingStoredProc(hostId, entityId,
					balanceType, currency, selectedDate, startRowNumber,
					endRowNumber, filterValue, roleId);
			log.debug(this.getClass().getName() + "-[accountDetail]- Exit");
			return coll;
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [accountDetail] method : - "
					+ exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp,
					"accountDetail", BalMaintenanceManagerImpl.class);
		}
	}

}
