/*
 * @(#)PartyManager.java 1.0 23/08/06
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.maintenance.service.impl;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Iterator;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.maintenance.dao.PartyDAO;
import org.swallow.maintenance.model.MiscParams;
import org.swallow.maintenance.model.Party;
import org.swallow.maintenance.model.PartyAlias;
import org.swallow.maintenance.service.PartyManager;
import org.swallow.util.CacheManager;
import org.swallow.util.LabelValueBean;
import org.swallow.util.SwtConstants;

/**
 * 
 * This is manager class for Party screen used to get and Add/Change/Delete the
 * party and alias detials.
 * 
 */
@Component("partyManager")
public class PartyManagerImpl implements PartyManager {
	/**
	 * Final instance for log
	 */
	private final Log log = LogFactory.getLog(PartyManagerImpl.class);
	@Autowired
	private PartyDAO partyDAO;

	/**
	 * This is used to set the partyDAO
	 * 
	 * @param partyDAO
	 * @return
	 */

	public void setPartyDAO(PartyDAO partyDAO) {
		this.partyDAO = partyDAO;
	}

	/**
	 * Get the total count of value from database
	 * 
	 * @param Party
	 *            party
	 * @return int
	 * @throws SwtException
	 * 
	 */
	public int getTotalCount(Party party) throws SwtException {
		try {
			log.debug(this.getClass().getName()
					+ "- [getTotalCount] - returns the partyDAOHibernate "
					+ "of getTotalCount method ");
			return partyDAO.getTotalCount(party);
		} catch (Exception exp) {

			log.error(this.getClass().getName()
					+ " - Exception Catched in [getTotalCount] method : - "
					+ exp.getMessage());

			throw SwtErrorHandler.getInstance().handleException(exp,
					"saveGroupDetail", PartyManagerImpl.class);
		}

	}

	/**
	 * Get the filtered details from P_PARTY table
	 * 
	 * @param Party
	 * @return Collection
	 * @return int
	 * @throws SwtException
	 */
	public int getFilterCount(Party party) throws SwtException {
		try {
			log.debug(this.getClass().getName()
					+ "- [getFilterCount] -returns the partyDAOHibernate "
					+ "of getFilterCount method ");
			return partyDAO.getFilterCount(party);
		} catch (Exception exp) {

			log.error(this.getClass().getName()
					+ " - Exception Catched in [getFilterCount] method : - "
					+ exp.getMessage());

			throw SwtErrorHandler.getInstance().handleException(exp,
					"saveGroupDetail", PartyManagerImpl.class);
		}

	}

	/**
	 * Save the newly added data's in P_PARTY table
	 * 
	 * @param custodian
	 * @return
	 * @throws SwtException
	 */
	public void saveCusdoianDetail(Party custodian) throws SwtException {
		try {

			log.debug(this.getClass().getName()
					+ "- [saveCusdoianDetail] - Entering ");
			if (!SwtConstants.YES
					.equalsIgnoreCase(custodian.getCustodianFlag())) {
				custodian.setCustodianFlag(SwtConstants.NO);
			}
			partyDAO.saveCustodianDetail(custodian);
			log.debug(this.getClass().getName()
					+ "- [saveCusdoianDetail] - Exiting ");
		} catch (Exception exp) {

			log
					.error(this.getClass().getName()
							+ " - Exception Catched in [saveCusdoianDetail] method : - "
							+ exp.getMessage());

			throw SwtErrorHandler.getInstance().handleException(exp,
					"saveCusdoianDetail", PartyManagerImpl.class);
		}
	}

	/**
	 * This function is used to save updated records in P_PARTY table.
	 * 
	 * @param custodian
	 * @return throws SwtException
	 */
	public void updateCustodianDetail(Party custodian) throws SwtException {
		try {
			log.debug(this.getClass().getName()
					+ "- [updateCustodianDetail] - Entering ");
			/* Method's local variable declaration */
			if (!SwtConstants.YES
					.equalsIgnoreCase(custodian.getCustodianFlag())) {
				custodian.setCustodianFlag(SwtConstants.NO);
			}
			/* Used to update the records in P_PARTY table */
			partyDAO.updateCustodianDetail(custodian);
			log.debug(this.getClass().getName()
					+ "- [updateCustodianDetail] - Exiting ");
		} catch (Exception exp) {

			log
					.error(this.getClass().getName()
							+ " - Exception Catched in [updateCustodianDetail] method : - "
							+ exp.getMessage());

			throw SwtErrorHandler.getInstance().handleException(exp,
					"updateCustodianDetail", PartyManagerImpl.class);
		}
	}

	/**
	 * This is used to delete the records from P_PARTY table
	 * 
	 * @param custodian
	 * @return
	 * @throws SwtException
	 */
	public void deleteCustodianDetail(Party custodian) throws SwtException {
		try {
			log.debug(this.getClass().getName()
					+ "- [deleteCustodianDetail] - Entering ");
			/* This is used to delete the records from P_PARTY table. */
			partyDAO.deleteCustodianDetail(custodian);
			log.debug(this.getClass().getName()
					+ "- [deleteCustodianDetail] - Exiting");
		} catch (Exception exp) {

			log
					.error(this.getClass().getName()
							+ " - Exception Catched in [deleteCustodianDetail] method : - "
							+ exp.getMessage());

			throw SwtErrorHandler.getInstance().handleException(exp,
					"deleteCustodianDetail", PartyManagerImpl.class);
		}
	}

	/**
	 * This is used to fetches the custodian details from P_PARTY table.
	 * 
	 * @param party
	 * @return Collection
	 * @throws SwtException
	 */
	public Collection getCustodianList(Party party) throws SwtException {
		try {
			log.debug(this.getClass().getName()
					+ "- [getCustodianList] - Entering ");
			/* Method's local variable declaration */
			String hostId = "";
			String entityId = "";
			String partyId = "";
			/* Getting hostid & Entity id from Bean cllass of Party */
			hostId = party.getId().getHostId();
			entityId = party.getId().getEntityId();
			partyId = new String("");

			/*
			 * This is used to get the custodian list list from DB in Collection
			 * variables
			 */
			Collection coll = partyDAO.getCustodianList(party);
			setPartyDesc(coll, entityId);
			return coll;
		} catch (Exception exp) {

			log.error(this.getClass().getName()
					+ " - Exception Catched in [getCustodianList] method : - "
					+ exp.getMessage());

			throw SwtErrorHandler.getInstance().handleException(exp,
					"getCustodianList", PartyManagerImpl.class);
		}
	}

	/**
	 * This is used to get the custodian flag from P_PARTY table
	 * 
	 * @param entityId
	 * @param hostId
	 * @param partyId
	 * @return Party
	 * @throws SwtException
	 */
	public Party getCustodianFlag(String entityId, String hostId, String partyId)
			throws SwtException {
		try {
			log.debug(this.getClass().getName()
					+ "- [getCustodianFlag] - Entering ");
			/* Class instance declaration */
			Party custodian = null;
			/* Retrieve the custodian flag details from database */
			Collection custodianFlag = partyDAO.getCustodianFlag(entityId,
					hostId, partyId);
			Iterator itr = custodianFlag.iterator();

			while (itr.hasNext()) {
				custodian = (Party) (itr.next());
			}
			log.debug(this.getClass().getName()
					+ "- [getCustodianFlag] - Exiting ");
			return (custodian);
		} catch (Exception exp) {

			log.error(this.getClass().getName()
					+ " - Exception Catched in [saveGroupDetail] method : - "
					+ exp.getMessage());

			throw SwtErrorHandler.getInstance().handleException(exp,
					"saveGroupDetail", PartyManagerImpl.class);
		}

	}

	/**
	 * This is used to fetches the Party details
	 * 
	 * @param Party
	 * @return Collection throws SwtException
	 */
	public Collection getPartyList(Party party) throws SwtException {
		log.debug(this.getClass().getName()
				+ "- [getCustodianFlag] - Entering ");
		/* Method's local variable declaration */
		Collection coll = null;
		/*
		 * Used to get the party details in collection variable from the
		 * database
		 */
		coll = partyDAO.getPartyList(party);

		setPartyDesc(coll, party.getId().getEntityId());

		log
				.debug(this.getClass().getName()
						+ "- [getCustodianFlag] - Exiting ");
		return coll;
	}

	/**
	 * This is used set the party description
	 * 
	 * @param input
	 * @return throws SwtException
	 */

	private void setPartyDesc(Collection input, String entityId)
			throws SwtException {
		try {
			log.debug(this.getClass().getName()
					+ "- [setPartyDesc] - Entering ");
			/* Method's local variable declaration */
			Collection coll = null;
			String partyDesc = "";
			coll = (Collection) CacheManager.getInstance()
					.getMiscParamsDetails("PARTYTYPE", entityId);

			/* Condition to check input not equal to null */
			if (input != null) {
				Iterator itrInput = input.iterator();
				while (itrInput.hasNext()) {
					Party par = (Party) (itrInput.next());
					Iterator itr = coll.iterator();
					/* Condition to check collection variable not equal to null */
					if (coll != null) {
						while (itr.hasNext()) {
							MiscParams mp = (MiscParams) (itr.next());
							/* Getting type of party from bean class */
							partyDesc = par.getPartyType();

							if (partyDesc != null) {
								if (partyDesc.equalsIgnoreCase(mp.getId()
										.getKey2())) {

									par.setPartyDescription(mp.getParValue());

									break;
								}
							}
						}
					}
				}
			}

			log
					.debug(this.getClass().getName()
							+ "- [setPartyDesc] - Exiting ");
		} catch (Exception exp) {

			log.error(this.getClass().getName()
					+ " - Exception Catched in [setPartyDesc] method : - "
					+ exp.getMessage());

			throw SwtErrorHandler.getInstance().handleException(exp,
					"saveGroupDetail", PartyManagerImpl.class);
		}
	}

	/**
	 * This is used to get the Party Alias details from P_PARTY_ALLIAS table
	 * 
	 * @param hostId
	 * @param entityId
	 * @param partyId
	 * @return collection
	 * @throws SwtException
	 */
	public Collection getPartyAliasList(String hostId, String entityId,
			String partyId) throws SwtException {
		try {
			log.debug(this.getClass().getName()
					+ "- [getPartyAliasList] - Entering ");
			/* Method's local variable declaration */
			Collection aliasDetails = null;
			Collection partyAliasList = null;
			/* Class Instance declaration */
			PartyAlias parAlias;
			aliasDetails = new ArrayList();
			/*
			 * Retrieve the Alias details in collection variable from DB based
			 * on parameter value passed
			 */
			partyAliasList = partyDAO.getPartyAliasList(hostId, entityId,
					partyId);
			/* Condition to check collection variable not equal to null */
			if (partyAliasList != null) {
				Iterator itr = partyAliasList.iterator();
				Object row = null;
				while (itr.hasNext()) {
					row = (Object) itr.next();
					parAlias = new PartyAlias();

					parAlias.getId().setPartyAlias((String) (row));
					aliasDetails.add(parAlias);
				}
			}
			log.debug(this.getClass().getName()
					+ "- [getPartyAliasList] - Exiting ");
			return aliasDetails;
		} catch (Exception exp) {

			log.error(this.getClass().getName()
					+ " - Exception Catched in [getPartyAliasList] method : - "
					+ exp.getMessage());

			throw SwtErrorHandler.getInstance().handleException(exp,
					"saveGroupDetail", PartyManagerImpl.class);
		}
	}

	/**
	 * This is used to save the alias details in P_PARTY_ALLIAS table
	 * 
	 * @param PartyAlias
	 *            return throws SwtException
	 */
	public void saveAliasDetails(PartyAlias partyAlias) throws SwtException {
		try {
			log.debug(this.getClass().getName()
					+ "- [saveAliasDetails] - Entering ");
			/*
			 * Used to save the newly added Party alias details in
			 * P_PARTY_ALLIAS table
			 */
			partyDAO.savePartyAliasDetails(partyAlias);
			log.debug(this.getClass().getName()
					+ "- [saveAliasDetails] - Exiting ");
		} catch (Exception exp) {

			log.error(this.getClass().getName()
					+ " - Exception Catched in [saveAliasDetails] method : - "
					+ exp.getMessage());

			throw SwtErrorHandler.getInstance().handleException(exp,
					"saveAliasDetails", PartyManagerImpl.class);
		}
	}

	/**
	 * This is used to remove alias details from P_PARTY_ALLIAS table
	 * 
	 * @param PartyAlias
	 * @return @ throws SwtException
	 */

	public void deleteAliasDetails(PartyAlias partyAlias) throws SwtException {
		try {
			log.debug(this.getClass().getName()
					+ "- [deleteAliasDetails] - Entering ");
			/* Used to delete the records from P_PARTY_ALLIAS table */
			partyDAO.deletePartyAliasDetails(partyAlias);
			log.debug(this.getClass().getName()
					+ "- [deleteAliasDetails] - Exiting ");
			log.debug("Exiting deletePartyAliasDetails() method");
		} catch (Exception exp) {

			log
					.error(this.getClass().getName()
							+ " - Exception Catched in [deleteAliasDetails] method : - "
							+ exp.getMessage());

			throw SwtErrorHandler.getInstance().handleException(exp,
					"deleteAliasDetails", PartyManagerImpl.class);
		}
	}

	/*
	 * Start:Code Modified For Mantis 1680 by chinniah on 16-Feb-2012:DNB
	 * Screen/Reporting - Mid Term Solution
	 */
	/**
	 * This is used to get the collection of parties
	 * 
	 * @param hostId
	 * @param entityId
	 * @return Collection @ throws SwtException
	 */

	public ArrayList<LabelValueBean> getParties(String hostId, String entityId)
			throws SwtException {
		// decleration of collection to hold the [party details
		Collection<Party> parties = null;
		// Variable decleration to hold the Party list
		ArrayList<LabelValueBean> partyList = null;
		// object to hold the party details
		Party party = null;
		// itreator to itreate party object
		Iterator<Party> itrParty = null;

		try {
			log.debug(this.getClass().getName() + "- [getParties] - Entering ");
			// get the details from th DAO
			parties = partyDAO.getParties(hostId, entityId);
			itrParty = parties.iterator();
			partyList = new ArrayList<LabelValueBean>();
			partyList.add(new LabelValueBean("", ""));
			// Iterating Party
			while (itrParty.hasNext()) {
				party = (Party) (itrParty.next());
				// storing the party list with thier names
				partyList.add(new LabelValueBean(party.getPartyName(), party
						.getId().getPartyId()));
			}

		} catch (Exception e) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [getParties] method : - "
					+ e.getMessage());

			throw SwtErrorHandler.getInstance().handleException(e,
					"getParties", PartyManagerImpl.class);
		} finally {
			parties = null;
			itrParty = null;
			log.debug(this.getClass().getName() + "- [getParties] - Exit ");
		}
		return partyList;
	}
	/*
	 * Start:Code Modified For Mantis 1680 by chinniah on 16-Feb-2012:DNB
	 * Screen/Reporting - Mid Term Solution
	 */
	/**
	 * This is used to get the collection of parties
	 * 
	 * @param hostId
	 * @param entityId
	 * @return Collection @ throws SwtException
	 */
	
	public String getPartiesAsXML(String hostId, String entityId) throws SwtException {
		// decleration of collection to hold the [party details
		Collection<Party> parties = null;
		// Variable decleration to hold the Party list
		ArrayList<LabelValueBean> partyList = null;
		// object to hold the party details
		Party party = null;
		// itreator to itreate party object
		Iterator<Party> itrParty = null;
		
		try {
			log.debug(this.getClass().getName() + "- [getParties] - Entering ");
			// get the details from th DAO
			return partyDAO.getPartiesAsXML(hostId, entityId);
			
		} catch (Exception e) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [getParties] method : - "
					+ e.getMessage());
			
			throw SwtErrorHandler.getInstance().handleException(e,
					"getParties", PartyManagerImpl.class);
		} finally {
			parties = null;
			itrParty = null;
			log.debug(this.getClass().getName() + "- [getParties] - Exit ");
		}
	}

	/**
	 * This method is used to Checks whether the changed Parent Party is forming
	 * cyclic loop.If it is forming cyclic loop it will return '1' other wise it
	 * will return '0' for valid Parent Id
	 * 
	 * 
	 * @param hostId
	 * @param entityId
	 * @param partyId
	 * @param changeParentId
	 * @return int @ throws SwtException
	 */
	public int checkParentId(String hostId, String entityId, String partyId,
			String changeParentId) throws SwtException {

		try {
			log.debug(this.getClass().getName()
					+ "- [checkParentId] - Entering ");
			// returns the status of the parent party
			return partyDAO.checkParentId(hostId, entityId, partyId,
					changeParentId);
		} catch (Exception e) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [checkParentId] method : - "
					+ e.getMessage());

			throw SwtErrorHandler.getInstance().handleException(e,
					"checkParentId", PartyManagerImpl.class);
		} finally {
			log.debug(this.getClass().getName() + "- [checkParentId] - Exit ");

		}

	}

	/**
	 * This method is used to check whether the given party is referred by any
	 * child party in the same entity or not, while deleting the party. If it is
	 * referred system will not allow deleting the party.
	 * 
	 * @param hostId
	 * @param entityId
	 * @param partyId
	 * @return boolean
	 * @throws SwtException
	 */
	public boolean checkPartyUse(String hostId, String entityId, String partyId)
			throws SwtException {
		try {
			log.debug(this.getClass().getName()
					+ "- [checkPartyUse] - Entering ");
			// get the status party status from the DAO
			return partyDAO.checkPartyUse(hostId, entityId, partyId);
		} catch (Exception e) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [checkPartyUse] method : - "
					+ e.getMessage());

			throw SwtErrorHandler.getInstance().handleException(e,
					"checkPartyUse", PartyManagerImpl.class);
		} finally {
			log.debug(this.getClass().getName() + "- [checkPartyUse] - Exit ");
		}

	}
	/*
	 * End:Code Modified For Mantis 1680 by chinniah on 16-Feb-2012:DNB
	 * Screen/Reporting - Mid Term Solution
	 */

}
