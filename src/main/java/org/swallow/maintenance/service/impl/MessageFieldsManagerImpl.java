/*
 * @(#) MessageFieldsManagerImpl.java 20/12/05
 * 
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 * 
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */

package org.swallow.maintenance.service.impl;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.swallow.util.LabelValueBean;
import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.maintenance.dao.MessageFieldsDAO;
import org.swallow.maintenance.model.KeyWords;
import org.swallow.maintenance.model.MessageFields;
import org.swallow.maintenance.model.ScenarioMessageFields;
import org.swallow.maintenance.service.MessageFieldsManager;
import org.swallow.util.SystemInfo;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Iterator;

@Component("messageFieldsManager")
public class MessageFieldsManagerImpl implements MessageFieldsManager {
    private final Log log = LogFactory.getLog(MessageFieldsManagerImpl.class);
    @Autowired
    private MessageFieldsDAO dao;

    /**
     * @param DAO
     * @throws SwtException
     */
    public void setMessageFieldsDAO(MessageFieldsDAO dao) {
        this.dao = dao;
    }

    /**
     * @param hostId
     * @param entityId
     * @param formatId
     * @return
     * @throws SwtException
     */
    public Collection getMsgFieldDetailList(String hostId, String formatId,
        String entityId) throws SwtException {
        log.debug("entering 'get Message Field List' method");

        ArrayList msgFieldsDetailsList = new ArrayList();

        MessageFields msgFlds = new MessageFields();

        try {
            Collection collmsgFlds = dao.getMsgFieldDetailList(hostId,
                    formatId, entityId);
            Iterator itr = collmsgFlds.iterator();

            log.debug("in 'get list' method");

            while (itr.hasNext()) {
                msgFlds = (MessageFields) (itr.next());
                msgFieldsDetailsList.add(msgFlds);
                //log.debug("Message Field Details--> " + msgFieldsDetailsList);
            }
        } catch (Exception exp) {
            log.debug(
                "Exception Catch in MessageFieldsManagerImpl.'getMsgFieldDetailList' method : " +
                exp.getMessage());
            exp.printStackTrace();
            throw SwtErrorHandler.getInstance().handleException(exp,
                "getMsgFieldDetailList", MessageFieldsManagerImpl.class);
        }

        return msgFieldsDetailsList;
    }

    /**
     * @param msgfld
     * @throws SwtException
     */
    public void saveMsgFieldDetails(MessageFields msgfld, SystemInfo systemInfo)
        throws SwtException {
        try {
            log.debug("entering 'saveMsgFieldDetails' method");
            dao.saveMsgFieldDetails(msgfld);
            log.debug("exiting 'saveMsgFieldDetails' method");
        } catch (Exception exp) {
            log.debug(
                "Exception Catch in MessageFieldsManagerImpl.'saveMsgFieldDetails' method : " +
                exp.getMessage());
            exp.printStackTrace();
            throw SwtErrorHandler.getInstance().handleException(exp,
                "saveMsgFieldDetails", MessageFieldsManagerImpl.class);
        }
    }

    /**
     * @param msgfld
     * @throws SwtException
     */
    public void updateMsgFieldDetails(MessageFields msgfld,
        SystemInfo systemInfo) throws SwtException {
        try {
            log.debug("entering 'updateMsgFieldDetails' method");
            dao.updateMsgFieldDetails(msgfld);
            log.debug("exiting 'updateMsgFieldDetails' method");
        } catch (Exception exp) {
            log.debug(
                "Exception Catch in MessageFieldsManagerImpl.'updateMsgFieldDetails' method : " +
                exp.getMessage());
            exp.printStackTrace();
            throw SwtErrorHandler.getInstance().handleException(exp,
                "updateMsgFieldDetails", MessageFieldsManagerImpl.class);
        }
    }

    /**
     * @param msgfld
     * @throws SwtException
     */
    public void deleteMessageFieldDetail(MessageFields msgfld,
        SystemInfo systemInfo) throws SwtException {
        try {
            log.debug("entering 'deleteMessageFieldDetail' method");
            dao.deleteMessageFieldDetail(msgfld);
            log.debug("exiting 'deleteMessageFieldDetail' method");
        } catch (Exception exp) {
            log.debug(
                "Exception Catch in MessageFieldsManagerImpl.'deleteMessageFieldDetail' method : " +
                exp.getMessage());
            exp.printStackTrace();
            throw SwtErrorHandler.getInstance().handleException(exp,
                "deleteMessageFieldDetail", MessageFieldsManagerImpl.class);
        }
    }

    public Integer getMaxSerialNo(String hostId, String formatId,
        String entityId) throws SwtException {
        log.debug("entering 'getMaxSerialNo' method");

        Integer maxSerialNo = null;
        Collection coll = dao.getMaxSerialNo(hostId, formatId, entityId);
//        log.debug("The dao.getMaxSerialNo(hostId,formatId,entityId) gives ==> " +
//            coll);

        //log.debug("Inside ifffff");
        Iterator itr = coll.iterator();
       // log.debug("itr.getClass() " + itr.getClass());

        String maxSerialNoString = new String("");
        int i = 0;

        while (itr.hasNext()) {
            //log.debug("Inside while");

            Object obj = itr.next();
            //log.debug(("casted into object"));

            if (obj == null) {
                maxSerialNo = new Integer(0);
            } else {
              //  log.debug("Inside else");
                maxSerialNoString = obj.toString();
                maxSerialNo = new Integer(maxSerialNoString);
            }

//            log.debug("The maximum serial no obtained is ===>" +
//                maxSerialNoString);
        }

        log.debug("exiting 'getMaxSerialNo' method");

        return maxSerialNo;
    }

    public Collection getKeyWordsList() throws SwtException {
        log.debug("entering 'getMsgFormatIdgetKeyWordsList' method");

        Collection keyWordsDropDown = new ArrayList();
        Collection collKeyWords = dao.getKeyWordsList();

        if (collKeyWords != null) {
            Iterator itr = collKeyWords.iterator();

            while (itr.hasNext()) {
                KeyWords kw = (KeyWords) (itr.next());
                keyWordsDropDown.add(new LabelValueBean(
                        kw.getId().getKeyWord(), kw.getId().getKeyWord()));
            }
        }

        log.debug("in 'getMsgFormatIdgetKeyWordsList' method");

        return keyWordsDropDown;
    }
    
    
    
    /**
     * Scenario Message methods
     */
    
    
    
    /**
     * @param hostId
     * @param entityId
     * @param formatId
     * @return
     * @throws SwtException
     */
    public Collection getScenarioMsgFieldDetailList( String formatId) throws SwtException {
        log.debug("entering 'get Message Field List' method");

        ArrayList msgFieldsDetailsList = new ArrayList();

        ScenarioMessageFields msgFlds = new ScenarioMessageFields();

        try {
            Collection collmsgFlds = dao.getScenarioMsgFieldDetailList(
                    formatId);
            Iterator itr = collmsgFlds.iterator();

            log.debug("in 'get list' method");

            while (itr.hasNext()) {
                msgFlds = (ScenarioMessageFields) (itr.next());
                msgFieldsDetailsList.add(msgFlds);
                //log.debug("Message Field Details--> " + msgFieldsDetailsList);
            }
        } catch (Exception exp) {
            log.debug(
                "Exception Catch in MessageFieldsManagerImpl.'getMsgFieldDetailList' method : " +
                exp.getMessage());
            exp.printStackTrace();
            throw SwtErrorHandler.getInstance().handleException(exp,
                "getMsgFieldDetailList", MessageFieldsManagerImpl.class);
        }

        return msgFieldsDetailsList;
    }

    /**
     * @param msgfld
     * @throws SwtException
     */
    public void saveScenarioMsgFieldDetails(ScenarioMessageFields msgfld, SystemInfo systemInfo)
        throws SwtException {
        try {
            log.debug("entering 'saveMsgFieldDetails' method");
            dao.saveScenarioMsgFieldDetails(msgfld);
            log.debug("exiting 'saveMsgFieldDetails' method");
        } catch (Exception exp) {
            log.debug(
                "Exception Catch in MessageFieldsManagerImpl.'saveMsgFieldDetails' method : " +
                exp.getMessage());
            exp.printStackTrace();
            throw SwtErrorHandler.getInstance().handleException(exp,
                "saveMsgFieldDetails", MessageFieldsManagerImpl.class);
        }
    }

    /**
     * @param msgfld
     * @throws SwtException
     */
    public void updateScenarioMsgFieldDetails(ScenarioMessageFields msgfld,
        SystemInfo systemInfo) throws SwtException {
        try {
            log.debug("entering 'updateMsgFieldDetails' method");
            dao.updateScenarioMsgFieldDetails(msgfld);
            log.debug("exiting 'updateMsgFieldDetails' method");
        } catch (Exception exp) {
            log.debug(
                "Exception Catch in MessageFieldsManagerImpl.'updateMsgFieldDetails' method : " +
                exp.getMessage());
            exp.printStackTrace();
            throw SwtErrorHandler.getInstance().handleException(exp,
                "updateMsgFieldDetails", MessageFieldsManagerImpl.class);
        }
    }

    /**
     * @param msgfld
     * @throws SwtException
     */
    public void deleteScenarioMessageFieldDetail(ScenarioMessageFields msgfld,
        SystemInfo systemInfo) throws SwtException {
        try {
            log.debug("entering 'deleteMessageFieldDetail' method");
            dao.deleteScenarioMessageFieldDetail(msgfld);
            log.debug("exiting 'deleteMessageFieldDetail' method");
        } catch (Exception exp) {
            log.debug(
                "Exception Catch in MessageFieldsManagerImpl.'deleteMessageFieldDetail' method : " +
                exp.getMessage());
            exp.printStackTrace();
            throw SwtErrorHandler.getInstance().handleException(exp,
                "deleteMessageFieldDetail", MessageFieldsManagerImpl.class);
        }
    }

    public Integer getScenarioMaxSerialNo( String formatId) throws SwtException {
        log.debug("entering 'getMaxSerialNo' method");

        Integer maxSerialNo = null;
        Collection coll = dao.getScenarioMaxSerialNo( formatId);
//        log.debug("The dao.getMaxSerialNo(hostId,formatId,entityId) gives ==> " +
//            coll);

        //log.debug("Inside ifffff");
        Iterator itr = coll.iterator();
       // log.debug("itr.getClass() " + itr.getClass());

        String maxSerialNoString = new String("");
        int i = 0;

        while (itr.hasNext()) {
            //log.debug("Inside while");

            Object obj = itr.next();
            //log.debug(("casted into object"));

            if (obj == null) {
                maxSerialNo = new Integer(0);
            } else {
              //  log.debug("Inside else");
                maxSerialNoString = obj.toString();
                maxSerialNo = new Integer(maxSerialNoString);
            }

//            log.debug("The maximum serial no obtained is ===>" +
//                maxSerialNoString);
        }

        log.debug("exiting 'getMaxSerialNo' method");

        return maxSerialNo;
    }

    public Collection getScenarioKeyWordsList() throws SwtException {
        log.debug("entering 'getMsgFormatIdgetKeyWordsList' method");

        Collection keyWordsDropDown = new ArrayList();
        Collection collKeyWords = dao.getScenarioKeyWordsList();

        if (collKeyWords != null) {
            Iterator itr = collKeyWords.iterator();

            while (itr.hasNext()) {
                KeyWords kw = (KeyWords) (itr.next());
                keyWordsDropDown.add(new LabelValueBean(
                        kw.getId().getKeyWord(), kw.getId().getKeyWord()));
            }
        }

        log.debug("in 'getMsgFormatIdgetKeyWordsList' method");

        return keyWordsDropDown;
    }
}

