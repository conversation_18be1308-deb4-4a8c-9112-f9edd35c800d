/*
 * @(#)BookCodeManagerImpl.java 1.0 21/12/05
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */

package org.swallow.maintenance.service.impl;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Iterator;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.maintenance.dao.BookCodeDAO;
import org.swallow.maintenance.model.BookCode;
import org.swallow.maintenance.model.Group;
import org.swallow.maintenance.service.BookCodeManager;
import org.swallow.util.LabelValueBean;

/**
 * This is manager class for Book screen
 *
 */
@Component ("bookCodeManager")
public class BookCodeManagerImpl implements BookCodeManager {
    private final Log log = LogFactory.getLog(BookCodeManagerImpl.class);
    @Autowired
    private BookCodeDAO dao;

    /**
     * This is used to set book codeDAO
     * @return .
     */
    public void setBookCodeDAO(BookCodeDAO dao) {
        this.dao = dao;
    }

    /**
     * This is used to get the Book code details from database and is used in Group and Metagroup action
     * @param hostId
     * @param entityId
     * @param groupId
     * @param systemInfo
     * @param sysformat
     * @return Collection
     * @throws SwtException
     */
    public Collection getBookCodeList(String hostId, String entityId,
        String groupId) {
    	log.debug(this.getClass().getName() + "- [getBookCodeList] - Entering");
        Collection coll = dao.getBookCodeDetail(hostId, entityId, groupId);
        log.debug(this.getClass().getName() + "- [getBookCodeList] - Exiting");
        return coll;
    }

    /**
     * This is used to get the book code details from P_BOOKCODE table
     * @param hostId
     * @param entityId
     * @param systemInfo
     * @param sysformat
     * @return Collection
     * @throws SwtException
     */
    public Collection getAllBookCodeList(String hostId, String entityId) {
        log.debug(this.getClass().getName() + "- [getAllBookCodeList] - Entering");
        Collection coll = dao.getAllBookCodeDetail(hostId, entityId);
        log.debug(this.getClass().getName() + "- [getAllBookCodeList] - Exiting");
        return coll;
    }

    /**
     * This is used to get the group details from P_GROUP table.
     * @param hostId
     * @param entityId
     * @param groupLevel
     * @param systemInfo
     * @param sysformat
     * @return Collection
     * @throws SwtException
     */
    public Collection getGroupLevelDetails(String hostId, String entityId,
        Integer groupLevel) {
    	log.debug(this.getClass().getName() + "- [getGroupLevelsDetail] - Entering");
    	/*Method's local variable declaration*/
        ArrayList bookCodeLevelList=null;
        bookCodeLevelList = new ArrayList();
        bookCodeLevelList.add(new LabelValueBean("", ""));
        Iterator itr = (dao.getGroupLevelDetails(hostId, entityId, groupLevel)).iterator();
        Group group = null;
        while (itr.hasNext()) {
            group = (Group) (itr.next());
            bookCodeLevelList.add(new LabelValueBean(group.getGroupName(),
                    group.getId().getGroupId()));
        }
        log.debug(this.getClass().getName() + "- [getGroupLevelsDetail] - Exiting");
        return bookCodeLevelList;
    }

    /**
     * This is used to save the bookcode details in P_BOOKCODE table
     * @param bookcode
     * @param systemInfo
     * @param sysformat
     * @return  
     * @throws SwtException
     */
    public void saveBookCodeDetail(BookCode bookcode) throws SwtException {
        try {
        	log.debug(this.getClass().getName() + "- [saveBookCodeDetail] - Entering");
        	/*Used to save the book code details in P_BOOKCODE table*/
            dao.saveBookCodeDetail(bookcode);
            log.debug(this.getClass().getName() + "- [saveBookCodeDetail] - Exiting");
        } catch (Exception exp) {
        	/*
			 * Start : Modified for Mantis 1366-Remove entry to log if duplicate
			 * records added by betcy on 18-02-2011
			 */
			log.error(this.getClass().getName()
					+ " - Exception Catched in [saveBookCodeDetail] method  - "
					+ exp.getMessage());
            throw SwtErrorHandler.getInstance().handleException(exp,
                "saveBookCodeDetail", BookCodeManagerImpl.class);
            /*
			 * End : Modified for Mantis 1366-Remove entry to log if duplicate
			 * records added by betcy on 18-02-2011
			 */
        }
    } 

    /**
     * This is used to save the updated book details in P_BOOKCODE table.
     * @param bookcode
     * @param systemInfo
     * @param sysformat
     * @return 
     * @throws SwtException
     */
    public void updateBookCodeDetail(BookCode bookcode) throws SwtException {
        try {
        	log.debug(this.getClass().getName() + "- [updateBookCodeDetail] - Entering");
        	/*used to save the updated book details in P_BOOKCODE table.*/
            dao.updateBookCodeDetail(bookcode);
            /*Comparing old and new values*/
            log.debug(this.getClass().getName() + "- [updateBookCodeDetail] - Exiting");
        } catch (Exception exp) {
        	log.debug(this.getClass().getName()
					+ " - Exception Catched in [updateBookCodeDetail] method : - "
					+ exp.getMessage());
			log.error(this.getClass().getName()
					+ " - Exception Catched in [updateBookCodeDetail] method : - "
					+ exp.getMessage());
            exp.printStackTrace();
            throw SwtErrorHandler.getInstance().handleException(exp,
                "updateBookCodeDetail", BookCodeManagerImpl.class);
        }
    } 

    /**
     * This is used to delete the book details  from P_BOOKCODE table.
     * @param bookcode
     * @param systemInfo
     * @param sysformat
     * @throws SwtException
     */
    public void deleteBookCodeDetail(BookCode bookcode) throws SwtException {
        try {
        	log.debug(this.getClass().getName() + "- [deleteBookCodeDetail] - Entering");
        	/*Delete the book details  from P_BOOKCODE table.*/
            dao.deleteBookCodeDetail(bookcode);
            log.debug(this.getClass().getName() + "- [deleteBookCodeDetail] - Exiting");
        } catch (Exception exp) {
        	log.debug(this.getClass().getName()
					+ " - Exception Catched in [deleteBookCodeDetail] method : - "
					+ exp.getMessage());
			log.error(this.getClass().getName()
					+ " - Exception Catched in [deleteBookCodeDetail] method : - "
					+ exp.getMessage());
            exp.printStackTrace();
            throw SwtErrorHandler.getInstance().handleException(exp,
                "deleteBookCodeDetail", BookCodeManagerImpl.class);
        }
    }
    
    /**
     * This is used to edit the required fields 
     * @param hostId
     * @param entityId
     * @param groupId 
     * @param systemInfo
     * @param sysformat
     * @return BookCode
     * throws SwtException
     * */
    
    public BookCode getEditableData(String hostId, String entityId,
        String groupId)throws SwtException {
        log.debug(this.getClass().getName() + 
        		"- [getEditableData] - returns getEditableData method in DAOHibernate");	
        return dao.getEditableData(hostId, entityId, groupId);
    }
    
    /**
     * This is used to get the book code locations from P_LOCATION table
     * @param hostId
     * @param entityId
     * @return Collection
     * @throws SwtException 
     * */
    
    public Collection getBookLocations(String hostId, String entityId)
        throws SwtException {
    	log.debug(this.getClass().getName() + "- [getBookLocations] - Entering");
        Collection coll = dao.getBookLocations(hostId, entityId);
        log.debug(this.getClass().getName() + "- [getBookLocations] - Exiting");
        return coll;
    }

    /**
	 * This is used to get the book code locations from P_LOCATION table.
	 * 
	 * @param locationId
	 * @return Collection
	 * @throws SwtException
	 */
	public Collection getBooksLocationId(String locationId)  throws SwtException {
		log.debug(this.getClass().getName() + "- [getBooksLocationId] - Entering");
        Collection coll = dao.getBooksLocationId(locationId);
        log.debug(this.getClass().getName() + "- [getBooksLocationId] - Exiting");
        return coll;
	}


	/**
	 * @param hostId
	 * @param entityId
	 * @param currency
	 * @return String
	 * @throws SwtException 
	 */
	public boolean checkBookCodeAndEntity(String hostId, String entityId, String bookCode) 
			throws SwtException{
		log.debug("Entering 'checkBookCodeAndEntity' method");
		try {
			
			return dao.checkBookCodeAndEntity(hostId, entityId,bookCode);
		} catch (Exception exp) {
			log
					.error("Exception Catch in AcctMaintenanceManagerImpl.'checkBookCodeAndEntity' method : "
							+ exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp,
					"checkBookCodeAndEntity", BookCodeManagerImpl.class);
		}	
		
	}

	/**
	 * This is used to check if book code exists in p_bookcode table
	 * 
	 * @param hostId
	 * @param bookCode
	 * @throws SwtException
	 */
	public boolean checkIfBookCodeExists(String hostId, String bookCode) throws SwtException{
    	log.debug(this.getClass().getName() + "- [checkIfBookCodeExists] - Entering");
		return dao.checkIfBookCodeExists(hostId, bookCode);
	}

} 