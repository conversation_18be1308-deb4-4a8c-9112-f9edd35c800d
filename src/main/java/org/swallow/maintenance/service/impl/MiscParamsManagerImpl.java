/*
 * Created on Nov 4, 2005
 *
 * TODO To change the template for this generated file go to
 * Window - Preferences - Java - Code Style - Code Templates
 */
package org.swallow.maintenance.service.impl;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.swallow.maintenance.dao.*;
import org.swallow.maintenance.model.*;
import org.swallow.maintenance.service.*;

import org.swallow.exception.*;

import java.util.*;


@Component("miscParamsManager")
public class MiscParamsManagerImpl implements MiscParamsManager {

	private final Log log = LogFactory.getLog(MiscParamsManagerImpl.class);
	private ArrayList miscParamsList=null;
	
	@Autowired
	private MiscParamsDAO dao;

    public void setMiscParamsDAO(MiscParamsDAO dao) {
        this.dao = dao;
    }
    
	public Collection getAllValues(String hostId)  throws SwtException
	{
		log.debug("entering 'getAllValues' method");
		
		Collection coll = dao.getAllValues(hostId);
		log.debug("exiting 'getAllValues' method");
		return coll;
	}

}
