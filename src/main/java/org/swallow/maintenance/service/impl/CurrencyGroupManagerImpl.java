/*
 * @(#)CurrencyGroupManagerImpl.java 1.0 23/08/06
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.maintenance.service.impl;

import java.util.Collection;
import java.util.Date;
import java.util.Iterator;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.maintenance.dao.CurrencyGroupDAO;
import org.swallow.maintenance.model.Currency;
import org.swallow.maintenance.model.CurrencyGroup;
import org.swallow.maintenance.service.CurrencyGroupManager;

/**
 * This is manager class for Currency group screen
 * 
 */
@Component("currencyGroupManager")
public class CurrencyGroupManagerImpl implements CurrencyGroupManager {
	/**
	 * Instance of Log.java class
	 */
	private final Log log = LogFactory.getLog(CurrencyGroupManagerImpl.class);

	/**
	 * CurrencyGroupDAO object
	 */
	@Autowired
	private CurrencyGroupDAO currencyGroupDAO;

	/**
	 * Used to set the currency group DAO
	 * 
	 * @param currencyGroupDAO
	 * @return
	 */
	public void setCurrencyGroupDAO(CurrencyGroupDAO currencyGroupDAO) {
		this.currencyGroupDAO = currencyGroupDAO;
	}

	/**
	 * Retrieves the currency group list and the number of currencies in each
	 * currency group from DAO
	 * 
	 * @param hostId
	 * @param entityId
	 * @return Collection
	 * @throws SwtException
	 */
	public Collection getCurrencyGroupList(String hostId, String entityId)
			throws SwtException {
		log.debug(this.getClass().getName() + " - [getCurrencyGroupList] - "
				+ "Entry");

		/* Method's class instance and local variable declaration */
		String currGrpId;
		Integer noOfcurrenciesObject;
		Collection coll;
		CurrencyGroup currGrp;

		/* Retrieves collection of currencyGroupList */
		coll = currencyGroupDAO.getCurrencyGroupList(hostId, entityId);

		/* Condition to check the collection is not null */
		if (coll != null) {
			/* Iterate the collection */
			Iterator itr = coll.iterator();
			/* Loop to get the each value from iterator */
			while (itr.hasNext()) {
				/* Store each value of iterator into the currencyGroup bean */
				currGrp = (CurrencyGroup) (itr.next());
				/* Retrieve the currency groupId from the currency group bean */
				currGrpId = currGrp.getId().getCurrencyGroupId();

				/*
				 * Retrieves the number of currencies in the currency group from
				 * DAO
				 */
				noOfcurrenciesObject = currencyGroupDAO.getNoOfCurrencies(
						hostId, entityId, currGrpId);
				/* Save the number of currencies in currencyGroup bean as String */
				currGrp.setNoOfCurrencies(noOfcurrenciesObject.toString());
			}
		}

		log.debug(this.getClass().getName() + " - [getCurrencyGroupList] - "
				+ "Exit");
		return coll;
	}

	/**
	 * Pass the CurrencyGroupBean to the DAO to save new currency group in
	 * database
	 * 
	 * @param currencyGroup
	 * @return None
	 * @throws SwtException
	 */
	public void saveCurrencyGroupDetails(CurrencyGroup currencyGroup)
			throws SwtException {
		try {
			log.debug(this.getClass().getName()
					+ " - [saveCurrencyGroupDetails] - " + "Entry");
			/* Pass the currency group bean to the save method of DAO */
			currencyGroupDAO.saveCurrencyGroupDetails(currencyGroup);
			log.debug(this.getClass().getName()
					+ " - [saveCurrencyGroupDetails] - " + "Exit");
		} catch (Exception exp) {
			log.error(this.getClass().getName()
							+ " - Exception Catched in [saveCurrencyGroupDetails] method : - "
							+ exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp,
					"saveCurrencyGroupDetails", CurrencyGroupManagerImpl.class);
		}
	}

	/**
	 * Pass the CurrencyGroupBean to the DAO to update the existing currency
	 * group in database
	 * 
	 * @param currencyGroup
	 * @return
	 * @throws SwtException
	 */
	public void updateCurrencyGroupDetails(CurrencyGroup currencyGroup)
			throws SwtException {
		try {
			log.debug(this.getClass().getName()
					+ " - [updateCurrencyGroupDetails] - " + "Entry");
			/* Pass the currency group bean to the update method of DAO */
			currencyGroupDAO.updateCurrencyGroupDetails(currencyGroup);
			log.debug(this.getClass().getName()
					+ " - [updateCurrencyGroupDetails] - " + "Exit");
		} catch (Exception exp) {
			log.debug(this.getClass().getName()
							+ " - Exception Catched in [updateCurrencyGroupDetails] method : - "
							+ exp.getMessage());

			log.error(this.getClass().getName()
							+ " - Exception Catched in [updateCurrencyGroupDetails] method : - "
							+ exp.getMessage());
			exp.printStackTrace();
			throw SwtErrorHandler.getInstance().handleException(exp,
					"updateCurrencyGroupDetails",
					CurrencyGroupManagerImpl.class);
		}
	}

	/**
	 * Pass the CurrencyGroupBean to the DAO to delete the currency group from
	 * database
	 * 
	 * @param currencyGroup
	 * @return
	 * @throws SwtException
	 */

	public void deleteCurrencyGroupRecord(CurrencyGroup currencyGroup)
			throws SwtException {
		try {
			log.debug(this.getClass().getName()
					+ " - [deleteCurrencyGroupRecord] - " + "Entry");
			/**
			 * Here the not null properties of the bean are set to blank values
			 * if this is not done it causes a dataAccessException
			 */
			currencyGroup.setCurrencyGroupName(" ");
			currencyGroup.setUpdateDate(new Date());
			currencyGroup.setUpdateUser(" ");
			/* currency group bean is pass to the delete method of DAO */
			currencyGroupDAO.deleteCurrencyGroupRecord(currencyGroup);
			log.debug(this.getClass().getName()
					+ " - [deleteCurrencyGroupRecord] - " + "Exit");
		} catch (Exception exp) {
			log.debug(this.getClass().getName()
							+ " - Exception Catched in [deleteCurrencyGroupRecord] method : - "
							+ exp.getMessage());

			log.error(this.getClass().getName()
							+ " - Exception Catched in [deleteCurrencyGroupRecord] method : - "
							+ exp.getMessage());

			exp.printStackTrace();
			throw SwtErrorHandler.getInstance()
					.handleException(exp, "deleteCurrencyGroupRecord",
							CurrencyGroupManagerImpl.class);
		}
	}

	/**
	 * Get the List of currencies for the entity passed from DAO
	 * 
	 * @param hostId
	 * @param entityId
	 * @param currGrpId
	 * @return Collection
	 * @throws SwtException
	 */
	public Collection getCurrencyGroupCurrenciesList(String hostId,
			String entityId, String currGrpId) throws SwtException {
		log.debug(this.getClass().getName()
				+ " - [getCurrencyGroupCurrenciesList] - " + "Entry");

		/* Method's local variable and class instance declaration */
		Collection coll;
		Currency curr;

		/*
		 * Retrieve the collection of currencies for each currency group from
		 * DAO
		 */
		coll = currencyGroupDAO.getCurrencyGroupCurrenciesList(hostId,
				entityId, currGrpId);
		/* Condition to check the collection is not null */

		if (coll != null) {
			/* Iterate the collection */
			Iterator itr = coll.iterator();
			/* Loop to get each value from the iterator */
			while (itr.hasNext()) {
				/* Store each value from the iterator in currency bean */
				curr = (Currency) (itr.next());

			}
		}
		log.debug(this.getClass().getName()
				+ " - [getCurrencyGroupCurrenciesList] - " + "Exit");

		return coll;
	}
}
