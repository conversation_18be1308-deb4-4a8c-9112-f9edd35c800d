package org.swallow.maintenance.service.impl;

import java.util.ArrayList;
import java.util.Collection;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.swallow.control.service.impl.AuditLogManagerImpl;
import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.maintenance.dao.InterfaceRulesDAO;
import org.swallow.maintenance.model.InterfaceRule;
import org.swallow.maintenance.service.InterfaceRulesManager;

/**
 * This is manager class for Interface Rules screen.
 * 
 */
@Component("interfaceRulesManager")
public class InterfaceRulesManagerImpl implements InterfaceRulesManager {
	@Autowired
	private InterfaceRulesDAO interfaceRulesDAO;

	/**
	 * @param interfaceRulesDAO
	 *            the interfaceRulesDAO to set
	 */
	public void setInterfaceRulesDAO(InterfaceRulesDAO interfaceRulesDAO) {
		this.interfaceRulesDAO = interfaceRulesDAO;
	}

	private final Log log = LogFactory.getLog(InterfaceRulesManagerImpl.class);

	/**
	 * Asks DAO for a collection of Interface rules objects.
	 * 
	 * @return Collection
	 * @throws SwtException
	 */
	public Collection<InterfaceRule> getList() throws SwtException {
		return interfaceRulesDAO.getList();
	}

	/**
	 * Asks DAO for a collection of Interface rules objects matching the
	 * supplied message type parameter
	 * 
	 * @param messageType
	 * @return Collection
	 * @throws SwtException
	 */
	/*
	 * Code modified by Venkat on 24-Feb-2011 for mantis 1365:"Allow Special
	 * characters in Interface Rules Screen."
	 */
	public Collection<InterfaceRule> getInterfaceRulesList(String messageType,
			String ruleId, String partialRuleId) throws SwtException {
		return interfaceRulesDAO.getInterfaceRulesList(messageType, ruleId,
				partialRuleId);
	}

	/**
	 * Asks DAO to delete the unique interface rule matching the data in the
	 * given InterfaceRule object
	 * 
	 * @param interfaceRule
	 * @throws SwtException
	 */
	public void deleteInterfaceRule(InterfaceRule interfaceRule)
			throws SwtException {
		interfaceRulesDAO.deleteInterfaceRule(interfaceRule);
	}

	/**
	 * Asks DAO for a unique record matching the messageType, ruleId and ruleKey
	 * contained in the given InterfaceRule object
	 * 
	 * @param interfaceRule
	 * @return InterfaceRule
	 * @throws SwtException
	 */
	public InterfaceRule getInterfaceRule(InterfaceRule interfaceRule)
			throws SwtException {
		return interfaceRulesDAO.getInterfaceRule(interfaceRule);
	}

	/**
	 * Asks DAO to update the persistent storage with the existing InterfaceRule
	 * object given. The PK data contained therein is used to locate the record
	 * to update.
	 * 
	 * @param interfaceRule
	 * @throws SwtException
	 */
	public void updateInterfaceRule(InterfaceRule interfaceRule)
			throws SwtException {
		interfaceRulesDAO.updateInterfaceRule(interfaceRule);
	}

	/**
	 * Asks DAO to save the interface rule details in I_INTERFACE_RULES table.
	 * 
	 * @param interfaceRule
	 * @return none
	 * @throws SwtException
	 */
	public void saveInterfaceRule(InterfaceRule interfaceRule)
			throws SwtException {
		try {
			interfaceRulesDAO.saveInterfaceRule(interfaceRule);
		} catch (Exception exp) {
			/*
			 * Start : Modified for Mantis 1366-Remove entry to log if duplicate
			 * records added by betcy on 18-02-2011
			 */
			log.error(this.getClass().getName()
					+ " - Exception Catched in [saveInterfaceRule] method : - "
					+ exp.getMessage());
			/*
			 * End : Modified for Mantis 1366-Remove entry to log if duplicate
			 * records added by betcy on 18-02-2011
			 */
			throw SwtErrorHandler.getInstance().handleException(exp,
					"saveInterfaceRule", InterfaceRulesDAO.class);
		}
	}

	/**
	 * This is used to populate the interface rules message types drop down
	 * values.
	 * 
	 * @return list
	 * @throws SwtException
	 */
	/*
	 * public Collection<String> getMessageTypesDropdownValues() throws
	 * SwtException { log.debug(this.getClass().getName() + " -
	 * getMessageTypesList () - " + " Entry "); return
	 * interfaceRulesDAO.getMessageTypesList(); }
	 */
	/*
	 * Start:Code modified by Venkat on 24-jan-2011 for mantis 1365:"Allow
	 * Special characters in Interface Rules Screen."
	 */
	public int getInterfaceRulesListUsingStoredProc(String messageType,
			String ruleId, int currentPage, int maxPage,
			ArrayList<InterfaceRule> interfaceRulesList,
			String filterSortStatus, String partialRuleId) throws SwtException {
		try {
			return interfaceRulesDAO.getInterfaceRulesListUsingStoredProc(
					messageType, ruleId, currentPage, maxPage,
					interfaceRulesList, filterSortStatus, partialRuleId);
		} catch (Exception e) {
			throw SwtErrorHandler.getInstance().handleException(e,
					"getInterfaceRulesListUsingStoredProc",
					AuditLogManagerImpl.class);

		}
	}
	/*
	 * End:Code modified by Venkat on 24-jan-2011 for mantis 1365:"Allow Special
	 * characters in Interface Rules Screen."
	 */
}
