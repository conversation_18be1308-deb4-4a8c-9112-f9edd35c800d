/*
 * @(#)HolidayMangerImpl.java 1.0 23/08/06
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.maintenance.service.impl;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Iterator;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.maintenance.dao.CountryDAO;
import org.swallow.maintenance.dao.HolidayDAO;
import org.swallow.maintenance.model.Country;
import org.swallow.maintenance.model.Holiday;
import org.swallow.maintenance.service.HolidayManager;
import org.swallow.util.LabelValueBean;
import org.swallow.util.SwtUtil;

@Component ("holidayManager")
public class HolidayManagerImpl implements HolidayManager {

	/**
	 * Initializing logger object for this class
	 */
	private final Log log = LogFactory.getLog(HolidayManagerImpl.class);
	
	@Autowired
	private HolidayDAO holidayDAO;
	private CountryDAO countryDAO;

	/**
	 * This is used to set country DAO
	 * 
	 * @param countryDAO
	 * @return
	 */
	public void setCountryDAO(CountryDAO countryDAO) {
		this.countryDAO = countryDAO;
	}

	/**
	 * This is used to set the Holiday DAO
	 * 
	 * @param
	 * @return
	 */
	public void setHolidayDAO(HolidayDAO holidayDAO) {
		this.holidayDAO = holidayDAO;
	}

	/**
	 * This is used to delete the holiday details from S_HOLIDAY table
	 * 
	 * @param holiday
	 * @return
	 * @throws SwtException
	 */
	public void deleteHoliday(Holiday holiday) throws SwtException {
		try {
			log.debug(this.getClass().getName() + " - [deleteHoliday] - "
					+ "Entry");
			/* Holidays details are removed from database */
			holidayDAO.deleteHoliday(holiday);
			log.debug(this.getClass().getName() + " - [deleteHoliday] - "
					+ "Exit");
		} catch (Exception exp) {
			log.debug(this.getClass().getName()
					+ " - Exception Catched in [deleteHoliday] method : - "
					+ exp.getMessage());
			log.error(this.getClass().getName()
					+ " - Exception Catched in [deleteHoliday] method : - "
					+ exp.getMessage());
			exp.printStackTrace();
			throw SwtErrorHandler.getInstance().handleException(exp,
					"deleteHoliday", HolidayManagerImpl.class);
		}
	}

	/**
	 * This is used save the newly added holiday details in S_HOLIDAY table
	 * 
	 * @param holiday
	 * @return
	 * @throws SwtException
	 */
	public void saveHoliday(Holiday holiday) throws SwtException {
		try {
			log.debug(this.getClass().getName() + " - [saveHoliday] - "
					+ "Entry");
			/* Save the holiday details in database */
			holidayDAO.saveHoliday(holiday);
			log.debug(this.getClass().getName() + " - [saveHoliday] - "
					+ "Exit");
		} catch (Exception exp) {
			/*
			 * Start : Modified for Mantis 1366-Remove entry to log if duplicate
			 * records added by betcy on 18-02-2011
			 */
			log.error(this.getClass().getName()
					+ " - Exception Catched in [saveHoliday] method : - "
					+ exp.getMessage());
			/*
			 * End : Modified for Mantis 1366-Remove entry to log if duplicate
			 * records added by betcy on 18-02-2011
			 */
			throw SwtErrorHandler.getInstance().handleException(exp,
					"saveHoliday", HolidayManagerImpl.class);
		}
	}

	/**
	 * This is used to fetches the holiday's list from S_HOLIDAY table
	 * 
	 * @param entityId
	 * @param hostId
	 * @return Collection
	 * @throws SwtException
	 */
	public Collection getHolidayList(String entity, String hostId)
			throws SwtException {
		try {
			log.debug(this.getClass().getName() + " - [getHolidayList] - "
					+ "returns getHolidayList of DAO class");
			return holidayDAO.getHolidayList(entity, hostId);
		} catch (Exception exp) {
			log.debug(this.getClass().getName()
					+ " - Exception Catched in [getHolidayList] method : - "
					+ exp.getMessage());

			log.error(this.getClass().getName()
					+ " - Exception Catched in [getHolidayList] method : - "
					+ exp.getMessage());
			exp.printStackTrace();
			throw SwtErrorHandler.getInstance().handleException(exp,
					"getHolidayList", HolidayManagerImpl.class);
		}
	}

	/**
	 * This is used to fetches country name and country code from S_COUNTRY
	 * table
	 * 
	 * @param
	 * @return Collection
	 * @throws SwtException
	 */
	public Collection getCountry() throws SwtException {
		try {
			log.debug(this.getClass().getName() + " - [getCountry] - "
					+ "Entry");
			/* Method's local variable declaration */
			Iterator itr = null;
			/* Class instance declaration */
			Country country = null;
			ArrayList countryList = null;
			countryDAO = (CountryDAO) (SwtUtil.getBean("countryDAO"));
			countryList = new ArrayList();
			/* Retrieve the country's name from database */
			itr = (countryDAO.getCountries()).iterator();
			while (itr.hasNext()) {
				country = (Country) (itr.next());
				/* Add the country name in label value bean */
				countryList.add(new LabelValueBean(country.getCountryName(),
						country.getCountryCode()));
			}
			return countryList;
		} catch (Exception exp) {
			log.debug(this.getClass().getName()
					+ " - Exception Catched in [getCountry] method : - "
					+ exp.getMessage());

			log.error(this.getClass().getName()
					+ " - Exception Catched in [getCountry] method : - "
					+ exp.getMessage());
			exp.printStackTrace();
			throw SwtErrorHandler.getInstance().handleException(exp,
					"getCountry", HolidayManagerImpl.class);
		}
	}
}