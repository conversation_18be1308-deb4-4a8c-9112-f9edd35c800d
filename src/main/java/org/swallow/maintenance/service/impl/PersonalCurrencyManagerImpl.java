/*
* @(#)PersonalCurrencyManagerImpl.java 1.0 09/07/07
*
* Copyright (c) 2006-2012 SwallowTech, Inc.
* 14 Lion Yard ,Tremadoc Road,  London  UK
* All Rights Reserved.
*
* This software is the confidential and proprietary information of
* SwallowTech Inc. ("Confidential Information"). You shall not
* disclose such Confidential Information and shall use it only in
* accordance with the terms of the license agreement you entered into
* with SwallowTech.
*/
package org.swallow.maintenance.service.impl;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;

import org.swallow.maintenance.dao.PersonalCurrencyDAO;
import org.swallow.maintenance.model.PersonalCurrency;
import org.swallow.maintenance.service.PersonalCurrencyManager;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Iterator;

/**
 * 
 * This is manager class for Personal currency screen and sub screen for currency monitor
 * 
 */
@Component("personalCurrencyManager")
public class PersonalCurrencyManagerImpl implements PersonalCurrencyManager {
    private final Log log = LogFactory.getLog(PersonalCurrencyManagerImpl.class);
    @Autowired
    private PersonalCurrencyDAO personalCurrencyDAO;

    public void setPersonalCurrencyDAO(PersonalCurrencyDAO dao) {
        this.personalCurrencyDAO = dao;
    }

    public Collection getPersonalCurrencyDetailList(String hostId, String userId)
        throws SwtException {
        log.debug("Entering getPersonalCurrencyDetailList()");

        Collection coll = personalCurrencyDAO.getPersonalCurrencyDetailList(hostId,
                userId);

        Collection retColl = new ArrayList();
        Iterator itr = coll.iterator();

        Object[] row = null;

        while (itr.hasNext()) {
            row = (Object[]) itr.next();

            PersonalCurrency perCurr = new PersonalCurrency();
            perCurr.getId().setCurrencyCode((String) row[0]);
            perCurr.setCurrencyName((String) row[2]);
            perCurr.setPriorityOrder((Integer) row[1]);
            retColl.add(perCurr);
        }

        log.debug("Exiting getPersonalCurrencyDetailList()");

        return retColl;
    }

    public void saveOrUpdatePersonalCurrencyDetails(PersonalCurrency perCurr,
			String saveOrUpdateFlag) throws SwtException {
		try {
			log.debug("Entering saveOrUpdatePersonalCurrencyDetails()");
			personalCurrencyDAO.saveOrUpdatePersonalCurrencyDetails(perCurr,
					saveOrUpdateFlag);
			log.debug("Exiting saveOrUpdatePersonalCurrencyDetails()");
		} catch (Exception exp) {
			/*
			 * Start : Modified for Mantis 1366-Remove entry to log if duplicate
			 * records added by betcy on 18-02-2011
			 */
			log.error(this.getClass().getName()
					+ " - Exception Catched in [saveOrUpdatePersonalCurrencyDetails] method : - "
					+ exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp,
					"saveOrUpdatePersonalCurrencyDetails",
					PersonalCurrencyManagerImpl.class);
			/*
			 * End : Modified for Mantis 1366-Remove entry to log if duplicate
			 * records added by betcy on 18-02-2011
			 */
		}
	}


    public void removePersonalCurrencyDetails(PersonalCurrency perCurr)
        throws SwtException {
        try {
            log.debug("Entering removePersonalCurrencyDetails()");
            personalCurrencyDAO.removePersonalCurrencyDetails(perCurr);
            log.debug("Exiting removePersonalCurrencyDetails()");
        } catch (Exception exp) {
            log.debug(
                "Exception Catch in PersonalCurrencyManagerImpl.'removePersonalCurrencyDetails' method : " +
                exp.getMessage());
            exp.printStackTrace();
            throw SwtErrorHandler.getInstance().handleException(exp,
                "removePersonalCurrencyDetails",
                PersonalCurrencyManagerImpl.class);
        }
    }
}
