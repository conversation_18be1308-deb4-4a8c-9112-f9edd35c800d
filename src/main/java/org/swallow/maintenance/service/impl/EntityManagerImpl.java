/*
 * @(#)EntityManagerImpl.java 1.0 on Nov 4, 2005
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.maintenance.service.impl;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.swallow.control.dao.hibernate.MaintenanceLogDAOHibernate;
import org.swallow.control.model.MaintenanceLog;
import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.maintenance.dao.EntityDAO;
import org.swallow.maintenance.dao.MetaGroupDAO;
import org.swallow.maintenance.model.EditableData;
import org.swallow.maintenance.model.Entity;
import org.swallow.maintenance.model.EntityMaster;
import org.swallow.maintenance.model.GroupLevel;
import org.swallow.maintenance.model.MetaGroupLevel;
import org.swallow.maintenance.service.EntityManager;
import org.swallow.util.LabelValueBean;
import org.swallow.util.SwtConstants;
import org.swallow.util.SwtUtil;
import org.swallow.util.SystemInfo;

/**
 * This Class that implements the EntityManager.
 * 
 */
@Component ("entityManager")
public class EntityManagerImpl implements EntityManager {

	private final Log log = LogFactory.getLog(EntityManagerImpl.class);
	@Autowired
	private EntityDAO dao;
	@Autowired
	private MetaGroupDAO Metagrpdao;

	public void setEntityDAO(EntityDAO dao) {
		this.dao = dao;
	}

	public void setMetaGroupDAO(MetaGroupDAO Metagrpdao) {
		this.Metagrpdao = Metagrpdao;
	}

	public Entity getEntityDetail(Entity entity) throws SwtException {
		log.debug("entering EntityManagerImpl.'getEntity' method");

		return dao.getEntityDetail(entity);
	}

	public Collection getEntityList(String hostId) throws SwtException {
		log.debug("entering EntityManagerImpl.'getEntityList' method");

		ArrayList entitiesList = new ArrayList();

		try {
			Iterator itr = (dao.getEntityList(hostId)).iterator();
			EntityMaster entityMst = null;
			entitiesList.add(new LabelValueBean("", ""));

			while (itr.hasNext()) {
				entityMst = (EntityMaster) (itr.next());
				entitiesList.add(new LabelValueBean(entityMst.getEntityName(),
						entityMst.getId().getEntityId()));
			}

			log.debug("exiting EntityManagerImpl.'getEntityList' method");
		} catch (Exception exp) {
			log
					.error("Exception Catch in EntityManagerImpl.'getEntityList' method : "
							+ exp.getMessage());

			throw SwtErrorHandler.getInstance().handleException(exp,
					"getEntityList", EntityManagerImpl.class);
		}

		return entitiesList;
	}

	/**
	 * 
	 */
	public Collection getCurrencyDetail(String hostId, String entityId)
			throws SwtException {
		log.debug("entering EntityManagerImpl.'getCurrencyList' method");

		Collection collCurr = dao.getCurrencyDetail(hostId, entityId);

		return collCurr;
	}

	public Collection getGroupLevel(String hostId, String entityId)
			throws SwtException {
		log.debug("entering EntityManagerImpl.'getGroupLevel' method");

		Collection collGroup = dao.getGroupLevel(hostId, entityId);

		return collGroup;
	}

	public Collection getMetaGroupLevel(String hostId, String entityId)
			throws SwtException {
		log.debug("entering EntityManagerImpl.'getMetaGroupLevel' method");

		Collection collMetaGroup = dao.getMetaGroupLevel(hostId, entityId);
		log.debug("exiting getMetaGroupLevel method");

		return collMetaGroup;
	}

	public void saveEntity(Entity entity, SystemInfo systemInfo)
			throws SwtException {
		try {
			log.debug("entering EntityManagerImpl.'saveEntity' method");

			MaintenanceLogDAOHibernate maintenanceLogDAOHibernate = (MaintenanceLogDAOHibernate) (SwtUtil
					.getBean("maintenanceLogDAO"));
			MaintenanceLog mainLog = new MaintenanceLog();
			mainLog.setHostId(entity.getId().getHostId());
			mainLog.setuserId(entity.getUpdateUser());
			mainLog.setIpAddress(systemInfo.getIpAddress());
			mainLog.setAction(SwtConstants.ACTION_ADD);
			mainLog.setTableName("ENTITY");
			mainLog.setReference(entity.getId().getHostId() + "-"
					+ entity.getId().getEntityId());
			mainLog.setLogDate(new Date());
			maintenanceLogDAOHibernate.logMaintenanceAudit(mainLog);
			log.debug("exiting 'saveEntity' method");
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [saveEntity] method "
					+ exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp,
					"saveEntity", EntityManagerImpl.class);
		}
	}

	/**
	 * 
	 * @param entity -
	 *            Entity Object
	 * @param groupLevel -
	 *            Collection of GroupLevel Objects
	 * @param metaGroupLevel -
	 *            Collection of MetaGroupLevel Objects
	 * @param collPosLevelName -
	 *            Collection of EntityPositionLevel Objects
	 * @throws SwtException -
	 *             SwtException
	 */
	public void deleteEntityDetails(Entity entity, Collection groupLevel,
			Collection metaGroupLevel, Collection collPosLevelName,
			Collection collEditableFields) throws SwtException {
		try {
			log
					.debug("entering EntityManagerImpl.'deleteEntityDetails' method");
			dao.deleteEntityDetails(entity, groupLevel, metaGroupLevel,
					collPosLevelName, collEditableFields);
		} catch (Exception exp) {
			log
					.error("Exception Catch in EntityManagerImpl.'deleteEntityDetails' method : "
							+ exp.getMessage());

			throw SwtErrorHandler.getInstance().handleException(exp,
					"deleteEntityDetails", EntityManagerImpl.class);
		}
	}

	/**
	 * This method used to update the details for table
	 * 
	 * @param entity -
	 *            Entity Object
	 * @param groupLevel -
	 *            Collection of GroupLevel Object
	 * @param metaGroupLevel -
	 *            Collection of MetaGroupLevel Objects
	 * @param collPosLvlDeleted -
	 *            Collection of EntityPositionLevel Objects to be deleted
	 * @param collPosLvlAdded -
	 *            Collection of EntityPositionLevel Objects to be added
	 * @param collPosLvlChanged -
	 *            Collection of EntityPositionLevel Objects to be updated
	 * @throws SwtException -
	 *             SwtException
	 */
	public void updateEntityDetails(Entity entity, Collection groupLevel,
			Collection metaGroupLevel, Collection collPosLvlDeleted,
			Collection collPosLvlAdded, Collection collPosLvlChanged,
			Collection collEditableData) throws SwtException {

		// Variable to hold i value
		int i = 1;
		// Variable to hold j value
		int j = 1;
		// Variable to hold itrerator object
		Iterator itr = null;
		// Variable to hold group level list object
		ArrayList<GroupLevel> grpLevelList = null;
		// Variable to meta group level list itrerator object
		ArrayList<MetaGroupLevel> metaGrpLevelList = null;
		// Variable to hold counter object
		Integer counter = null;
		// Variable to hold group level object
		GroupLevel grpLevel = null;
		// Variable to hold meta group level object
		MetaGroupLevel metaGrpLevel = null;
		// Variable to hold hostid
		String hostId = null;
		// Variable to hold entityid
		String entityId = null;
		// Variable to hold editable data object
		EditableData editableData = null;
		// Variable to hold count object
		Long count = null;
		// Variable to hold no of records object
		int noofRecords = 0;
		try {
			log
					.debug("entering EntityManagerImpl.'updateEntityDetails' method");
			// to create the array list object
			grpLevelList = new ArrayList<GroupLevel>();
			// to create the iterator object
			itr = groupLevel.iterator();
			// to create the array list object
			metaGrpLevelList = new ArrayList<MetaGroupLevel>();
			while (itr.hasNext()) {
				// to create the integer object
				counter = new Integer(i);
				// to create the group level object
				grpLevel = (GroupLevel) (itr.next());
				grpLevel.getId().setGroupLevelId(counter);
				grpLevel.getId().setEntityId(entity.getId().getEntityId());
				grpLevel.getId().setHostId(entity.getId().getHostId());
				grpLevelList.add(grpLevel);
				i = i + 1;
			}
			// to create the iterator object
			itr = metaGroupLevel.iterator();

			while (itr.hasNext()) {
				// to create the meta group level object
				metaGrpLevel = new MetaGroupLevel();
				// to create the counter object
				counter = new Integer(j);
				// to create the meta group level object
				metaGrpLevel = (MetaGroupLevel) (itr.next());
				metaGrpLevel.getId().setMgrpLvlCode(counter);
				metaGrpLevel.getId().setEntityId(entity.getId().getEntityId());
				metaGrpLevel.getId().setHostId(entity.getId().getHostId());
				metaGrpLevelList.add(metaGrpLevel);
				j = j + 1;
			}

			hostId = ""; // Checking if editable records for the selected
			// entity exist in DB
			entityId = "";
			// to create the iterator object
			itr = collEditableData.iterator();
			// to create the editable data object
			editableData = new EditableData();

			while (itr.hasNext()) {
				// to create the editable data object
				editableData = (EditableData) itr.next();
				// to get the hostid value
				hostId = editableData.getId().getHostId();
				// to get the entity id value
				entityId = editableData.getId().getEntityId();

				break;
			}
			// to get the count value
			count = dao.getRecordsFromEditableData(hostId, entityId);
			// to get the no of records value
			noofRecords = count.intValue();
			// To update the entity details
			dao.updateEntity(entity, grpLevelList, metaGrpLevelList,
					collPosLvlDeleted, collPosLvlAdded, collPosLvlChanged,
					collEditableData, noofRecords);

			log.debug("exiting updateEntityDetails method");
		} catch (Exception exp) {
			log
					.error("Exception Catch in EntityManagerImpl.'updateEntityDetails' method : "
							+ exp.getMessage());

			throw SwtErrorHandler.getInstance().handleException(exp,
					"updateEntityDetails", EntityManagerImpl.class);
		} finally {
			// To set null value for local variables
			itr = null;
			grpLevelList = null;
			metaGrpLevelList = null;
			counter = null;
			grpLevel = null;
			metaGrpLevel = null;
			hostId = null;
			entityId = null;
			editableData = null;
			count = null;

		}
	}

	/**
	 * 
	 * @param entity -
	 *            Entity Object
	 * @param groupLevel -
	 *            Collection of GroupLevel Objects
	 * @param metaGroupLevel -
	 *            Collection of MetaGroupLevel Objects
	 * @param posLevelNameList -
	 *            Collection of EntityPositionLevel Objects
	 * @throws SwtException -
	 *             SwtException
	 */
	public void saveEntityDetails(Entity entity, Collection groupLevel,
			Collection metaGroupLevel, Collection posLevelNameList,
			Collection editableData) throws SwtException {
		try {
			log.debug("entering 'saveEntityDetails' method");

			int i = 1;
			int j = 1;
			Iterator itr = groupLevel.iterator();
			ArrayList grpLevelList = new ArrayList();
			ArrayList metaGrpLevelList = new ArrayList();

			while (itr.hasNext()) {
				Integer counter = new Integer(i);
				GroupLevel grpLevel = (GroupLevel) (itr.next());
				grpLevel.getId().setGroupLevelId(counter);
				grpLevel.getId().setEntityId(entity.getId().getEntityId());
				grpLevel.getId().setHostId(entity.getId().getHostId());
				grpLevelList.add(grpLevel);
				i = i + 1;
			}

			itr = metaGroupLevel.iterator();

			while (itr.hasNext()) {
				MetaGroupLevel metaGrpLevel = new MetaGroupLevel();
				Integer counter = new Integer(j);
				metaGrpLevel = (MetaGroupLevel) (itr.next());
				metaGrpLevel.getId().setMgrpLvlCode(counter);
				metaGrpLevel.getId().setEntityId(entity.getId().getEntityId());
				metaGrpLevel.getId().setHostId(entity.getId().getHostId());
				metaGrpLevelList.add(metaGrpLevel);
				j = j + 1;
			}

			dao.saveEntity(entity, grpLevelList, metaGrpLevelList,
					posLevelNameList, editableData);
			log.debug("exiting EntityManagerImpl.'saveEntityDetails' method");
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					+ "- [saveEntityDetails] - Exception " + exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp,
					"saveEntityDetails", EntityManagerImpl.class);
		}
	}

	/**
	 * @desc This method fetches the collection of EntityPositionLevel Objects
	 *       for the given hostId and entityId
	 * @param hostId -
	 *            hostId
	 * @param entityId -
	 *            entityId
	 * @return Collection
	 * @throws SwtException -
	 *             SwtException
	 */
	public Collection getPosLvlNameDetails(String hostId, String entityId)
			throws SwtException {
		return dao.getEntityPositionLevels(hostId, entityId);
	}

	public Collection getEditableDataDetails(String hostId, String entityId)
			throws SwtException {
		Collection collEditable = dao.getEditableDataDetails(hostId, entityId);

		return collEditable;
	}

	/**
	 * This method is used to get the accountId list
	 * 
	 * @param currencyCode
	 *            String
	 * @param entityId
	 *            String
	 * @param currencyCode
	 *            String
	 * @return Collection
	 */
	public Collection getAccountList(String hostId, String entityId,
			String currencyCode) throws SwtException {
		Collection returnColl = new ArrayList();
		log.debug(this.getClass().getName() + "- [getAccountList] - Entering ");
		try {
			returnColl = dao.getAccountList(hostId, entityId, currencyCode);
			log.debug(this.getClass().getName()
					+ "- [getAccountList] - Exiting ");
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [getAccountList] method : - "
					+ exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp,
					"getAccountList", EntityManagerImpl.class);
		}
		return returnColl;
	}

	/**
	 * This method is used to get the Misc params details
	 * 
	 * @param hostId
	 *            String
	 * @param entityId
	 *            String
	 * @return Collection
	 */
	@SuppressWarnings("unchecked")
	public Collection getMiscDataDetails(String hostId, String entityId)
			throws SwtException {
		try {
			log.debug(this.getClass().getName()
					+ " - [getMiscDataDetails] - Entering ");
			// To get the details of getMiscDataDetails
			return dao.getMiscDataDetails(hostId, entityId);
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					+ "- [getMiscDataDetails] - Exception " + exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp,
					"getMiscDataDetails", EntityManagerImpl.class);
		}
	}
		
	public HashMap<String, String> getTimeZoneRegionsList() throws SwtException {

		try {
			log.debug(this.getClass().getName() + " - [getTimeZoneRegionsList] - " + "Entry");
			return dao.getTimeZoneRegionsList();

		} catch (Exception e) {
			log.debug(this.getClass().getName() + " - Exception Catched in [getTimeZoneRegionsList] method : - "
					+ e.getMessage());

			log.error(this.getClass().getName() + " - Exception Catched in [getTimeZoneRegionsList] method : - "
					+ e.getMessage());
			e.printStackTrace();
			throw SwtErrorHandler.getInstance().handleException(e, "getTimeZoneRegionsList",
					SysParamsManagerImpl.class);
		}

	}

}