/*
 * @(#)GroupLevelManager.java 1.0 21/12/05
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.maintenance.service;

import org.swallow.exception.SwtException;

import java.util.Collection;



public interface GroupLevelManager {
	/**
	 * This is used to fetches meta group details from P_GROUP table
	 * @param hostId
	 * @param entityId
	 * @return Collection
	 * @throws SwtException
	 */	
   public Collection getGroupLevelList(String hostId, String entityId)
        throws SwtException;
	/**
	 * This is used to fetch group level name from P_GROUP table
	 * *@param hostId
	 * @param entityId
	 * @return Collection
	 * @throws SwtException
	 */	
    public Collection getGroupLevelNamesList(String hostId, String entityId)
        throws SwtException;
    /**
     * This is used to fetch group level details from P_GROUP table
     * *@param hostId
	 * @param entityId
	 * @param grpLvlName
	 * @return Collection
	 * @throws SwtException
	 */	
    public int getGroupLevelDetailsList(String hostId, String entityId,
        String grpLvlName) throws SwtException;
     /**
      * This is used in Logon Action
      * @param hostId
	 *  @param entityId
	 *  @param grpLvlName
	 *  @return String
	 *  @throws SwtException
	 */	
    
    public String getGroupLevelNames(String hostId, String entityId,
        Integer gLevel) throws SwtException;
}
