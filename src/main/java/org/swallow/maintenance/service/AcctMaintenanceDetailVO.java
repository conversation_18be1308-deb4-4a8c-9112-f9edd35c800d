/*
 * @(#)AcctMaintenanceDetailVO.java 21/12/05
 * 
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 * 
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.maintenance.service;

import java.util.Collection;


public class AcctMaintenanceDetailVO {
	
	private Collection acctmaintenancelist;
	private Collection acctmaintenancelistDetails;
	private Collection currencylist;

	/**
	 * @return Returns the acctmaintenancelist.
	 */
	public Collection getAcctmaintenancelist() {
		return acctmaintenancelist;
	}
	/**
	 * @param acctmaintenancelist The acctmaintenancelist to set.
	 */
	public void setAcctmaintenancelist(Collection acctmaintenancelist) {
		this.acctmaintenancelist = acctmaintenancelist;
	}
	/**
	 * @return Returns the acctmaintenancelistDetails.
	 */
	public Collection getAcctmaintenancelistDetails() {
		return acctmaintenancelistDetails;
	}
	/**
	 * @param acctmaintenancelistDetails The acctmaintenancelistDetails to set.
	 */
	public void setAcctmaintenancelistDetails(
			Collection acctmaintenancelistDetails) {
		this.acctmaintenancelistDetails = acctmaintenancelistDetails;
	}
	/**
	 * @return Returns the currencylist.
	 */
	public Collection getCurrencylist() {
		return currencylist;
	}
	/**
	 * @param currencylist The currencylist to set.
	 */
	public void setCurrencylist(Collection currencylist) {
		this.currencylist = currencylist;
	}
}
