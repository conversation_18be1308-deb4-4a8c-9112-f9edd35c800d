package org.swallow.maintenance.service;

import java.util.Collection;
import java.util.Date;

import org.swallow.exception.SwtException;
import org.swallow.maintenance.model.AccountCurrencyPeriodMaintenance;

public interface AcctCcyPeriodMaintenanceManager {

	/**
	 * This method is used to get Account Currency Maintenance Period records 
	 */
	public Collection<AccountCurrencyPeriodMaintenance> getAcctCcyPeriodMaintRecords(String hostId, String entityId, String accountId, Date forDate,  String show)
			throws SwtException;
	
	/**
	 * This method is used to get Account Currency Maintenance Period records 
	 */
	public   Collection<AccountCurrencyPeriodMaintenance> getAcctCcyPeriodMaintRecordsPerAccount(String hostId, String entityId, String accountId, Date forDate, String show)
			throws SwtException;
	
	/**
	 * This method is used to get Accounts ids list for a given entity and currency
	 */
	public   Collection getAccountIdDropDown(String hostId, String entityId, String ccyCode, String screenName)
			throws SwtException;
	
	/**
	 * This method is used to check if the Start-End Date period overlaps another existing record having the same host/entity/account
	 */
	
	public boolean checkIfOverlaps(String hostId, String entityId, String accountId, Date startDate, Date endDate)
			throws SwtException ;
	
	/**
	 * This method is used to check if record already exists for given host, entity, account, startDate and endDate
	 */
	
	
	public boolean checkIfAcctCcyPeriodExists(String hostId, String entityId, String accountId, Date startDate, Date endDate)
			throws SwtException ;
	
	
	/**
	 * This method is used to save new account currency period in P_CCY_ACC_MAINT_PERIOD
	 */
	
	public void saveAcctCcyPeriodRecord(AccountCurrencyPeriodMaintenance acctCcyPeriod,String action) throws SwtException;
	
	
	/**
	 * This method is used to delete existing account currency period from  P_CCY_ACC_MAINT_PERIOD
	 */
	
	public void deleteAcctCcyPeriodMaintRecord(String hostId, String entityId, String accountId, Date startDate, Date endDate)
    		throws SwtException ;
	
	
	/**
	 * This method is used to get the list of records from  VW_S_MAINTENANCE_LOG according to given fromDate, endDate and reference
	 */
	
	public Collection getMaintenanceLogList(Date fromDate, Date toDate, String reference) throws SwtException ;
	
	
	/**
	 * This method is used to get the list of records from  s_maintenance_log according to given hostId, userId, logDate, ipAddress, tableName, reference and action
	 */
	
	public Collection getViewLogDetails(String hostId, String userId,  Date logDate, String ipAddress, String tableName, String reference, String action) throws SwtException ;
	
	/**
	 * This method is used to get account currency code from p_account table
	 */
	public String getAccountCcy(String accountId) throws SwtException ;

}