/*
 * Created on Dec 9, 2005
 *
 * TODO To change the template for this generated file go to
 * Window - Preferences - Java - Code Style - Code Templates
 */
package org.swallow.maintenance.service;

import java.util.Collection;

import org.swallow.maintenance.model.Currency;

/**
 * <AUTHOR>
 * 
 * This class has getters and setters related to Currency Details<br>
 */
public class CurrencyDetailVO {
	private Collection currencyList;
	// Holds the collection of currency
	private Collection<Currency> currencyListDetails;

	public CurrencyDetailVO() {
	}

	public CurrencyDetailVO(Collection currencyList,
			Collection currencyListDetails) {
		this.currencyList = currencyList;
		this.currencyListDetails = currencyListDetails;
	}

	/**
	 * @return Returns the currencyList.
	 */
	public Collection getCurrencyList() {
		return currencyList;
	}

	/**
	 * @param currencyList
	 *            The currencyList to set.
	 */
	public void setCurrencyList(Collection currencyList) {
		this.currencyList = currencyList;
	}

	/**
	 * @return the currencyListDetails
	 */
	public Collection<Currency> getCurrencyListDetails() {
		return currencyListDetails;
	}

	/**
	 * @param currencyListDetails
	 *            the currencyListDetails to set
	 */
	public void setCurrencyListDetails(Collection<Currency> currencyListDetails) {
		this.currencyListDetails = currencyListDetails;
	}

}
