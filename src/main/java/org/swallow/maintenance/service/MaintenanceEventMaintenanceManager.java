package org.swallow.maintenance.service;

import java.lang.*;
import java.util.*;

import org.swallow.control.model.MaintenanceLogView;
import org.swallow.exception.SwtException;
import org.swallow.maintenance.model.MaintenanceEvent;
import org.swallow.maintenance.model.MaintenanceEventDetails;
import org.swallow.maintenance.model.MaintenanceEventForm;
import org.swallow.maintenance.model.MaintenanceLogViewAuth;

public interface MaintenanceEventMaintenanceManager {

	public Long saveMaintenanceEvent(MaintenanceEvent maintenanceEvent) throws SwtException;

	public void updateMaintenanceEvent(MaintenanceEvent maintenanceEvent) throws SwtException;

	public void deleteMaintenanceEvent(MaintenanceEvent maintenanceEvent) throws SwtException;

	public MaintenanceEvent getMaintenanceEvent(String maintEventId) throws SwtException;

	public Collection<MaintenanceEvent> getMaintenanceEventList(MaintenanceEventForm eventForm) throws SwtException;

	public void saveMaintenanceEventDetails(MaintenanceEventDetails maintenanceEventDetails) throws SwtException;

	public void updateMaintenanceEventDetails(MaintenanceEventDetails maintenanceEventDetails) throws SwtException;

	public void deleteMaintenanceEventDetails(MaintenanceEventDetails maintenanceEventDetails) throws SwtException;

	public MaintenanceEventDetails getMaintenanceEventDetails(String maintEventId, String tableName)
			throws SwtException;

	public Collection<MaintenanceEventDetails> getMaintenanceEventDetailsList() throws SwtException;

	public Collection<MaintenanceLogViewAuth> getMaintenanceLogList(String maintEventId) throws SwtException;

	/**
	 * This method is used to get the list of records from s_maintenance_log
	 * according to given hostId, userId, logDate, ipAddress, tableName, reference
	 * and action
	 */

	public Collection getViewLogDetails(String hostId, String userId, Date logDate, String ipAddress, String tableName,
			String reference, String action, Long mainEventId) throws SwtException;

	public boolean isRecordRelatedToMaintenanceEvent(String recordId, String facilityId) throws SwtException;
}