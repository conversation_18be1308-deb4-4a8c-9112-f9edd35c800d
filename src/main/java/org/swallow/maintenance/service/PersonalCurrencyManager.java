/*
* @(#)PersonalCurrencyManager.java 1.0 09/07/07
*
* Copyright (c) 2006-2012 SwallowTech, Inc.
* 14 Lion Yard ,Tremadoc Road,  London  UK
* All Rights Reserved.
*
* This software is the confidential and proprietary information of
* SwallowTech Inc. ("Confidential Information"). You shall not
* disclose such Confidential Information and shall use it only in
* accordance with the terms of the license agreement you entered into
* with SwallowTech.
*/
package org.swallow.maintenance.service;

import org.swallow.exception.SwtException;

import org.swallow.maintenance.dao.PersonalCurrencyDAO;
import org.swallow.maintenance.model.PersonalCurrency;

import java.util.Collection;


public interface PersonalCurrencyManager {
    public void setPersonalCurrencyDAO(PersonalCurrencyDAO dao);

    public Collection getPersonalCurrencyDetailList(String hostId, String userId)
        throws SwtException;

    public void saveOrUpdatePersonalCurrencyDetails(PersonalCurrency perCurr,
        String saveOrUpdateFlag) throws SwtException;

    public void removePersonalCurrencyDetails(PersonalCurrency perCurr)
        throws SwtException;
}
