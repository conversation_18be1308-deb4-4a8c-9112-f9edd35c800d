package org.swallow.maintenance.service;

import java.util.Collection;

import org.swallow.exception.SwtException;
import org.swallow.work.model.CashManagementVO;

public interface CashRsrvBalManagmntManager {

	public CashManagementVO getCashRsvBalanceGridData(CashManagementVO cashManagementVO) throws SwtException;
	
	public CashManagementVO getCashRsvBalancePeriod(CashManagementVO cashManagementVO) throws SwtException;


	/**
	 * @param hostId
	 * @param entityId
	 * @param currencyCode
	 * @return
	 * @throws SwtException
	 */

	public Collection getAccountIDDropDown(String hostId, String entityId, String currencyCode, String accountType) throws SwtException;
}
