/*
 * @(#)AccountAttributeMaintenanceAction.java 1.0 12/12/14
 *
 * Copyright (c) 2006-2015 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.maintenance.web;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;






import org.swallow.control.model.MaintenanceLog;
import org.swallow.control.model.MaintenanceLogView;
import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.maintenance.model.AccountCurrencyPeriodMaintenance;
import org.swallow.maintenance.service.AcctCcyPeriodMaintenanceManager;
import org.swallow.pcm.maintenance.model.core.ColumnInfo;
import org.swallow.pcm.maintenance.model.core.OptionInfo;
import org.swallow.pcm.maintenance.model.core.SelectInfo;
import org.swallow.pcm.maintenance.web.ResponseHandler;
import org.swallow.util.xml.SwtResponseConstructor;
import org.swallow.util.xml.SwtXMLWriter;
import org.swallow.util.CacheManager;
import org.swallow.util.CommonDataManager;
import org.swallow.util.SwtConstants;
import org.swallow.util.SwtUtil;
import org.swallow.util.SystemFormats;
import org.swallow.work.web.PreAdviceInputAction;

import org.swallow.util.LabelValueBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.apache.struts2.convention.annotation.Result;
import org.apache.struts2.convention.annotation.AllowedMethods;
import org.apache.struts2.convention.annotation.Action;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.apache.struts2.ServletActionContext;
import org.swallow.util.struts.CustomActionSupport;

@Action(value = "/accountPeriod", results = {
	@Result(name = "fail", location = "/error.jsp"),
	@Result(name = "success", location = "/jsp/maintenance/accountcurrencymaintenanceperiod.jsp"),
	@Result(name = "data", location = "/jsp/data.jsp"),
	@Result(name = "statechange", location = "/jsp/flexstatechange.jsp"),
	@Result(name = "subScreen", location = "/jsp/maintenance/accountcurrencymaintenanceperiodadd.jsp"),
	@Result(name = "logScreen", location = "/jsp/maintenance/accountcurrencymaintenanceperiodLog.jsp"),
	@Result(name = "viewLogScreen", location = "/jsp/maintenance/acctccymaintperiodviewlog.jsp"),
})

@AllowedMethods ({"display" ,"getLists" ,"getUpdatedAccountList" ,"OpenSubAcctCcyPeriodMaint" ,"displayLogScreen" ,"subDisplay" ,"checkIfOverlaps" ,"saveAcctCcyPeriodMaint" ,"deleteAcctCcyPeriod" ,"displayLog" ,"displayViewLogScreen" ,"displayViewLog" })
public class AcctCcyPeriodMaintenanceAction extends CustomActionSupport {
public String execute() throws Exception {
    HttpServletRequest request = ServletActionContext.getRequest();

    // List of methods 
    String method = String.valueOf(request.getParameter("method"));

    switch (method) {
        case "unspecified":
            return unspecified();
        case "display":
            return display();
        case "getLists":
            return getLists();
        case "getUpdatedAccountList":
            return getUpdatedAccountList();
        case "OpenSubAcctCcyPeriodMaint":
            return OpenSubAcctCcyPeriodMaint();
        case "displayLogScreen":
            return displayLogScreen();
        case "subDisplay":
            return subDisplay();
        case "checkIfOverlaps":
            return checkIfOverlaps();
        case "saveAcctCcyPeriodMaint":
            return saveAcctCcyPeriodMaint();
        case "deleteAcctCcyPeriod":
            return deleteAcctCcyPeriod();
        case "displayLog":
            return displayLog();
        case "displayViewLogScreen":
            return displayViewLogScreen();
        case "displayViewLog":
            return displayViewLog();
        default:
            break;
    }

    return unspecified();
}

/**
 * Final log instance for logging this class
 */
private final Log log = LogFactory.getLog(AcctCcyPeriodMaintenanceAction.class);

	
	@Autowired
private AcctCcyPeriodMaintenanceManager acctCcyPeriodMaintenanceManager = null;
	
	
	public void setAcctCcyPeriodMaintenanceManager(
			AcctCcyPeriodMaintenanceManager acctCcyPeriodMaintenanceManager) {
		this.acctCcyPeriodMaintenanceManager = acctCcyPeriodMaintenanceManager;
	}
	
	
	/**
	 * Default method used to called from change password screen.
	 * 
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return a Result of type Action forward
	 */
	public String unspecified() throws SwtException {
HttpServletRequest request = ServletActionContext.getRequest();
HttpServletResponse response = ServletActionContext.getResponse();

		try {
			
				return ("success");
			
		} catch (Exception e) {
			log.error("Exception Catch in AccountCurrencyPeriodMaintenanceAction.'unspecified' method : " + e.getMessage());
			SwtUtil.logErrorInDatabase(
					SwtErrorHandler.getInstance().handleException(e, "unspecified", AcctCcyPeriodMaintenanceAction.class));
			return ("fail");
		}
	}
	
	
	/**
	 * This method is used to load the account currency period maintenance screen and it will be
	 * invoked while changing entity or currency on screen to get the corresponding details.
	 * 
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return
	 * @throws SwtException
	 */
	public String display()
			throws SwtException {
HttpServletRequest request = ServletActionContext.getRequest();
HttpServletResponse response = ServletActionContext.getResponse();

		/* Local variable declaration */
		// To host for application
		String hostId = null;
		// To hold the current entity from screen
		String entityId = null;
		// To hold the current entity from screen
		String forDate = null;
		// To hold selected currency in screen
		String currencyCode = null;
		String dateFormat = null;
		// To hold the role for current user
		String roleId = null;
		String show = null;
		String fromMethod = null;
		String displayedDate = null;
		ResponseHandler responseHandler = null;
		SwtResponseConstructor responseConstructor = null;
		SwtXMLWriter xmlWriter = null;
		String width = null;
		String columnOrder = null;
		String hiddenColumns = null;

		Collection<AccountCurrencyPeriodMaintenance> listActCcy = new ArrayList<AccountCurrencyPeriodMaintenance>();
		String amountFormat = null;
		SystemFormats sysFormat=null;
		String defaultEntity = null;
		try {
			
			log.debug(this.getClass().getName() + " - [display()] - Entry");

			sysFormat = SwtUtil.getCurrentSystemFormats(request.getSession());
			dateFormat= SwtUtil.getCurrentDateFormat(request.getSession());
			amountFormat=sysFormat.getCurrencyFormat();
			entityId= request.getParameter("entityId");
			currencyCode = request.getParameter("currencyCode");
			show = request.getParameter("show");
			fromMethod = request.getParameter("fromMethod");
			forDate = request.getParameter("forDate");
			// to get the host id for application
			hostId = CacheManager.getInstance().getHostId();
			defaultEntity= SwtUtil.getUserCurrentEntity(request.getSession());
			
			if (SwtUtil.isEmptyOrNull(entityId)) {				
				entityId =  defaultEntity;				
			}
			

			// Get role id associated with the logged-in user
			roleId = ((CommonDataManager) request.getSession().getAttribute(SwtConstants.CDM_BEAN)).getUser()
					.getRoleId();
			if (SwtUtil.isEmptyOrNull(currencyCode)) {
			// get the default currency for entity id
				currencyCode = SwtUtil.getDomesticCurrencyForUser(request,
					hostId, entityId);
			}
			//selected for date value
			Date selectedDate = null;
			if (SwtUtil.isEmptyOrNull(forDate)) {
				
				selectedDate = SwtUtil.getSystemDatewithoutTime();
	          displayedDate= SwtUtil.formatDate(selectedDate, SwtUtil.getCurrentSystemFormats(
						request.getSession()).getDateFormatValue());
	          
			}else {
//				forDate = SwtUtil.getSysParamDate()
				selectedDate =SwtUtil.parseDate(forDate,SwtUtil.getCurrentSystemFormats(
						request.getSession()).getDateFormatValue());
			  displayedDate= forDate;
					  /*SwtUtil.formatDate(forDate, SwtUtil.getCurrentSystemFormats(
						request.getSession()).getDateFormatValue()); */
			}
			//get the list of records saved in  Account Currency Maintenance Period table
			listActCcy=acctCcyPeriodMaintenanceManager.getAcctCcyPeriodMaintRecords(hostId, entityId, currencyCode, selectedDate , show);

			responseHandler = new ResponseHandler();
			responseConstructor = new SwtResponseConstructor();
			xmlWriter = responseConstructor.getXMLWriter();

			xmlWriter.startElement(SwtConstants.CCY_ACC_MAINT_PERIOD);

			responseConstructor.formRequestReply(Boolean.parseBoolean(SwtConstants.STR_TRUE),
					SwtConstants.DATA_FETCH_OK);
			// forms singleton node
			xmlWriter.startElement(SwtConstants.SINGLETONS);
			responseConstructor.createElement("displayedDate",displayedDate );
			responseConstructor.createElement("selectedEntity",
					entityId);
			responseConstructor.createElement("selectedCurrency",
					currencyCode);
			responseConstructor.createElement("show",
					show);
			responseConstructor.createElement("fromMethod",
					fromMethod);
			responseConstructor.createElement("dateFormat",
					dateFormat);
			responseConstructor.createElement(SwtConstants.CURRENCYPATTERN,
					SwtUtil.getCurrentSystemFormats(request.getSession()).getCurrencyFormat());
			xmlWriter.endElement(SwtConstants.SINGLETONS); 
			/***** Entity Combo Start ***********/
			ArrayList<SelectInfo> lstSelect = new ArrayList<SelectInfo>();
			// options drop down list
			ArrayList<OptionInfo> lstOptions = new ArrayList<OptionInfo>();

			lstOptions = new ArrayList<OptionInfo>();
			LabelValueBean row = null;
			Collection entityList = putFullAcessEntityListInReq(request,roleId);
			Iterator j = entityList.iterator();
			row = null;
			lstOptions.add(new OptionInfo(SwtConstants.ALL_VALUE, SwtConstants.ALL_ENTITY_VALUE, false));
			while (j.hasNext()) {
				row = (LabelValueBean) j.next();
				lstOptions.add(new OptionInfo(row.getValue(), row.getLabel(), false));
			}
			lstSelect.add(new SelectInfo("entityList", lstOptions));			
			/***** Entity Combo End ***********/
			

			/***** Currency Combo Start ***********/
			lstOptions = new ArrayList<OptionInfo>();
			
			Collection currencyList = putCurrencyFullAccessListInReq(request, hostId, "All".equalsIgnoreCase(entityId)? defaultEntity:entityId );
			 j = currencyList.iterator();
			row = null;
			lstOptions.add(new OptionInfo(SwtConstants.ALL_VALUE, SwtConstants.ALL_CURRENCY_VALUE, false));
			while (j.hasNext()) {
				row = (LabelValueBean) j.next();
				lstOptions.add(new OptionInfo(row.getValue(), row.getLabel(), false));
			}
			lstSelect.add(new SelectInfo("currencyList", lstOptions));		
			/***** Currency Combo End ***********/	
			
			
			responseConstructor.formSelect(lstSelect);
			
			/******* acctCcyMaintPeriodGrid ******/
			responseConstructor.formGridStart("acctCcyMaintPeriodGrid");
			responseConstructor.formColumn(getGridColumns(width, columnOrder, hiddenColumns, request));
			// form rows (records)
			responseConstructor.formRowsStart(listActCcy.size());
			for (Iterator<AccountCurrencyPeriodMaintenance> it = listActCcy.iterator(); it.hasNext();) {
				// Obtain rules definition tag from iterator
				AccountCurrencyPeriodMaintenance acctCcy = (AccountCurrencyPeriodMaintenance) it.next();
				responseConstructor.formRowStart();
				responseConstructor.createRowElement(SwtConstants.CCY_ACC_MAINT_PERIOD_ENTITY_ID_TAGNAME , acctCcy.getId().getEntityId());
				responseConstructor.createRowElement(SwtConstants.CCY_ACC_MAINT_PERIOD_CCY_TAGNAME,acctCcy.getCurrnecyCode() );
				responseConstructor.createRowElement(SwtConstants.CCY_ACC_MAINT_PERIOD_ACCOUNT_ID_TAGNAME,acctCcy.getId().getAccountId());
				responseConstructor.createRowElement(SwtConstants.CCY_ACC_MAINT_PERIOD_START_DATE_TAGNAME, (acctCcy.getId().getStartDate() == null || acctCcy.getId().getStartDate().equals("")) ? "" :  SwtUtil.formatDate(acctCcy.getId().getStartDate(), dateFormat ));
				responseConstructor.createRowElement(SwtConstants.CCY_ACC_MAINT_PERIOD_END_DATE_TAGNAME, (acctCcy.getEndDate() == null || acctCcy.getEndDate().equals("")) ? "" :  SwtUtil.formatDate(acctCcy.getEndDate(), dateFormat ));
				responseConstructor.createRowElement(SwtConstants.CCY_ACC_MAINT_PERIOD_MINIMUM_RESERVE_TAGNAME, SwtUtil.formatCurrencyWithoutDecimals(acctCcy.getCurrnecyCode(), acctCcy.getMinimumReserve()));
				responseConstructor.createRowElement(SwtConstants.CCY_ACC_MAINT_PERIOD_TIER_TAGNAME, SwtUtil.formatCurrencyWithoutDecimals(acctCcy.getCurrnecyCode(), acctCcy.getTier())); 
				responseConstructor.createRowElement(SwtConstants.CCY_ACC_MAINT_PERIOD_TARGET_AVG_BALANCE_TAGNAME, SwtUtil.formatCurrencyWithoutDecimals(acctCcy.getCurrnecyCode(), acctCcy.getTargetAvgBalance()));
				responseConstructor.createRowElement(SwtConstants.CCY_ACC_MAINT_PERIOD_MIN_TARGET_BALANCE_TAGNAME, SwtUtil.formatCurrencyWithoutDecimals(acctCcy.getCurrnecyCode(), acctCcy.getMinTargetBalance()));
				responseConstructor.createRowElement(SwtConstants.CCY_ACC_MAINT_PERIOD_FILL_DAYS_TAGNAME, SwtUtil.formatCurrencyWithoutDecimals(acctCcy.getCurrnecyCode(), acctCcy.getFillDays()));
				responseConstructor.createRowElement(SwtConstants.CCY_ACC_MAINT_PERIOD_FILL_BALANCE_TAGNAME, SwtUtil.formatCurrencyWithoutDecimals(acctCcy.getCurrnecyCode(), acctCcy.getFillBalance())); 					
				responseConstructor.createRowElement(SwtConstants.CCY_ACC_MAINT_PERIOD_FILL_EOD_BALANCE_SRC_TAGNAME, acctCcy.getEofBalanceSource());
				
				if ("E".equalsIgnoreCase(acctCcy.getEofBalanceSource())) {
					responseConstructor.createRowElement(
							SwtConstants.CCY_ACC_MAINT_PERIOD_FILL_EOD_BALANCE_SRC_AsSTRING_TAGNAME,
							SwtUtil.getMessage("ccyAccMaintPeriod.external", request));
				} else if ("I".equalsIgnoreCase(acctCcy.getEofBalanceSource())) {
					responseConstructor.createRowElement(
							SwtConstants.CCY_ACC_MAINT_PERIOD_FILL_EOD_BALANCE_SRC_AsSTRING_TAGNAME,
							SwtUtil.getMessage("ccyAccMaintPeriod.internal", request));
				} else
					acctCcy.getEofBalanceSource();

				responseConstructor.createRowElement(SwtConstants.CCY_ACC_MAINT_PERIOD_EXCLUDE_FILL_DAYS_TAGNAME, acctCcy.getExcludeFillPeriod()); 					
				responseConstructor.formRowEnd();
			}

			responseConstructor.formRowsEnd();
			responseConstructor.formGridEnd();
			
			xmlWriter.endElement(SwtConstants.CCY_ACC_MAINT_PERIOD);
			request.setAttribute("data", xmlWriter.getData());

			// set the method name,last ref time and access level on request
			// attribute which are used in front end
			request.setAttribute("methodName", "success");
			log.debug(this.getClass().getName() + " - [display()] - Exit");
			return ("data");
		} catch (SwtException swtexp) {
			swtexp.printStackTrace();
			log
					.error("SwtException Catch in AcctCcyPeriodMaintenanceAction.'display' method : "
							+ swtexp.getMessage());
			SwtUtil.logException(swtexp, request, "");
			return ("fail");
		} catch (Exception exp) {
			exp.printStackTrace();
			log
					.error("Exception Catch in AcctCcyPeriodMaintenanceAction.'display' method : "
							+ exp.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "display", AcctCcyPeriodMaintenanceAction.class), request, "");
			return ("fail");
		} finally {
			/* null the objects created already. */
			responseConstructor = null;
			xmlWriter=null;
			hostId = null;
			entityId = null;
			forDate = null;
			currencyCode = null;
			roleId = null;
			fromMethod= null;
			show= null;
			displayedDate= null;
			currencyCode = null;
			dateFormat = null;
			roleId = null;
		}
	}
	
	/**
	 * This method is used to get the collection of entities which are having
	 * access for current user's role
	 * 
	 * @param request
	 * @throws SwtException
	 */
	private Collection putFullAcessEntityListInReq(HttpServletRequest request,
			String roleId) throws SwtException {
		try {
			Collection entityList = null;
			log.debug(this.getClass().getName()
					+ " - [putFullAcessEntityListInReq] - Entering");
			// get the collection of entity which having full access and
			// set the same in request to populate the entity select box
			entityList=SwtUtil.getSwtMaintenanceCache().getFullEntityAccessCollectionLVL(roleId);
			//entityList=SwtUtil.getSwtMaintenanceCache().getFullOrViewEntityAccessCollectionLVL(roleId);
			
			request.setAttribute("entities",entityList );
			log.debug(this.getClass().getName()
					+ " - [putFullAcessEntityListInReq] - Existing");
			return entityList;
		} catch (Exception e) {
			log.error(this.getClass().getName()
					+ " - [putFullAcessEntityListInReq] - Exception -"
					+ e.getMessage());
			throw new SwtException(e.getMessage());
		}
	}
	
	
	/**
	 * This method is used to get the collection of currency full access list
	 * for given host id and entity id and set it into request which is used to
	 * populate the currency list in select box
	 * 
	 * @param request
	 * @param hostId
	 * @param entityId
	 * @throws SwtException
	 */
	private Collection putCurrencyFullAccessListInReq(HttpServletRequest request,
			String hostId, String entityId) throws SwtException {
		// collection of currency drop down
		Collection<LabelValueBean> currencyDropDown = null;
		// Map for currency id , currency name
		Map<String, String> currencyMap = null;
		// hold the role id
		String roleId = null;
		try {
			log.debug(this.getClass().getName()
					+ " - [putCurrencyFullAccessListInReq] - Entering");
			// Instantiate the currency drop down
			currencyDropDown = new ArrayList<LabelValueBean>();
			// get the role id for current user
			roleId = ((CommonDataManager) request.getSession().getAttribute(
					SwtConstants.CDM_BEAN)).getUser().getRoleId();
			// get the map for currency with full access
			currencyMap = SwtUtil.getCurrencyFullAccessMap(roleId, hostId,
					entityId);
			//currencyMap = SwtUtil.getCurrencyFullOrViewAccessMap(roleId, hostId,entityId);
			
			if (currencyMap != null && currencyMap.size() > 0) {
				
				for (Map.Entry<String, String> entry : currencyMap.entrySet()) {
					String currencyId = entry.getKey() ;
					String currencyDesc = entry.getValue();
					// add labelvaluebean for currency id
					currencyDropDown.add(new LabelValueBean(currencyDesc, currencyId));
				}
			}
			// set the currency drop down collection in request
			request.setAttribute("currencies", currencyDropDown);
			log.debug(this.getClass().getName()
					+ " - [putCurrencyFullAccessListInReq] - Existing");
			return currencyDropDown;
		} catch (Exception e) {
			log.error(this.getClass().getName()
					+ " - [putCurrencyFullAccessListInReq] - Exception -"
					+ e.getMessage());
			throw new SwtException(e.getMessage());
		} finally {
			// nullify objects
			currencyDropDown = null;
			currencyMap = null;
			roleId = null;
		}
	}
	
	
	
	private List<ColumnInfo> getGridColumns(String width, String columnOrder, String hiddenColumns,
  			HttpServletRequest request) throws SwtException {
  		// Array list to hold column order
  		ArrayList<String> orders = null;
  		// String array variable to hold column order property
  		String[] columnOrderProp = null;
  		// Iterator variable to hold column order
  		Iterator<String> columnOrderItr = null;
  		// Hash map to hold column width
  		LinkedHashMap<String, String> widths = null;
  		// String array variable to hold width property
  		String[] columnWidthProperty = null;
  		// List for column info
  		List<ColumnInfo> lstColumns = null;
  		/* Hash map to hold column hidden_Columns */
  		HashMap<String, Boolean> hiddenColumnsMap = new LinkedHashMap<String, Boolean>();
  		/* Array list to hold hidden Column array */
  		ArrayList<String> lstHiddenColunms = null;
  		/* String array variable to hold hidden columns property */
  		String[] hiddenColumnsProp = null;
  		try {
  			// log debug message
  			log.debug(this.getClass().getName() + " - [ getGridColumns ] - Entry");
  			// Condition to check width is null

  			if (SwtUtil.isEmptyOrNull(width)) {
  				// default width for columns
				width =  SwtConstants.CCY_ACC_MAINT_PERIOD_ENTITY_ID_TAGNAME + "=120" + ","
						+ SwtConstants.CCY_ACC_MAINT_PERIOD_CCY_TAGNAME + "=120" + ","
						+ SwtConstants.CCY_ACC_MAINT_PERIOD_ACCOUNT_ID_TAGNAME + "=120" + ","
						+ SwtConstants.CCY_ACC_MAINT_PERIOD_START_DATE_TAGNAME + "=120" + ","
						+ SwtConstants.CCY_ACC_MAINT_PERIOD_END_DATE_TAGNAME + "=120" + ","
						+ SwtConstants.CCY_ACC_MAINT_PERIOD_MINIMUM_RESERVE_TAGNAME + "=180" + ","
						+ SwtConstants.CCY_ACC_MAINT_PERIOD_TIER_TAGNAME + "=120" + ","
						+ SwtConstants.CCY_ACC_MAINT_PERIOD_TARGET_AVG_BALANCE_TAGNAME + "=180" + ","
						+ SwtConstants.CCY_ACC_MAINT_PERIOD_MIN_TARGET_BALANCE_TAGNAME + "=220" + ","
						+ SwtConstants.CCY_ACC_MAINT_PERIOD_FILL_DAYS_TAGNAME + "=120" + ","
						+ SwtConstants.CCY_ACC_MAINT_PERIOD_FILL_BALANCE_TAGNAME + "=130" + ","
				        + SwtConstants.CCY_ACC_MAINT_PERIOD_FILL_EOD_BALANCE_SRC_TAGNAME + "=130"+","
				        + SwtConstants.CCY_ACC_MAINT_PERIOD_FILL_EOD_BALANCE_SRC_AsSTRING_TAGNAME + "=130" + ","
						+ SwtConstants.CCY_ACC_MAINT_PERIOD_EXCLUDE_FILL_DAYS_TAGNAME + "=150";

   			}

  			// Obtain width for each column
  			columnWidthProperty = width.split(",");

  			// Loop to insert each column in hash map
  			widths = new LinkedHashMap<String, String>();
  			for (int i = 0; i < columnWidthProperty.length; i++) {
  				// Condition to check index of = is -1
  				if (columnWidthProperty[i].indexOf("=") != -1) {
  					String[] propval = columnWidthProperty[i].split("=");
  					widths.put(propval[0], propval[1]);
  				}
  			}

  			// Condition to check column order is null or empty
  			if (SwtUtil.isEmptyOrNull(columnOrder)) {
  				// Default values for column order
				columnOrder = SwtConstants.CCY_ACC_MAINT_PERIOD_ENTITY_ID_TAGNAME + ","
						+ SwtConstants.CCY_ACC_MAINT_PERIOD_CCY_TAGNAME + ","
						+ SwtConstants.CCY_ACC_MAINT_PERIOD_ACCOUNT_ID_TAGNAME + ","
						+ SwtConstants.CCY_ACC_MAINT_PERIOD_START_DATE_TAGNAME + ","
						+ SwtConstants.CCY_ACC_MAINT_PERIOD_END_DATE_TAGNAME + ","
						+ SwtConstants.CCY_ACC_MAINT_PERIOD_MINIMUM_RESERVE_TAGNAME + ","
						+ SwtConstants.CCY_ACC_MAINT_PERIOD_TIER_TAGNAME + ","
						+ SwtConstants.CCY_ACC_MAINT_PERIOD_TARGET_AVG_BALANCE_TAGNAME + ","
						+ SwtConstants.CCY_ACC_MAINT_PERIOD_MIN_TARGET_BALANCE_TAGNAME+ ","
						+ SwtConstants.CCY_ACC_MAINT_PERIOD_FILL_DAYS_TAGNAME + ","
						+ SwtConstants.CCY_ACC_MAINT_PERIOD_FILL_BALANCE_TAGNAME + ","
				        + SwtConstants.CCY_ACC_MAINT_PERIOD_FILL_EOD_BALANCE_SRC_TAGNAME+ ","
				        + SwtConstants.CCY_ACC_MAINT_PERIOD_FILL_EOD_BALANCE_SRC_AsSTRING_TAGNAME + ","
				        + SwtConstants.CCY_ACC_MAINT_PERIOD_EXCLUDE_FILL_DAYS_TAGNAME;
}
  			orders = new ArrayList<String>();
  			// Split the columns using , and save in string array
  			columnOrderProp = columnOrder.split(",");

  			// Loop to enter column order in array list
  			for (int i = 0; i < columnOrderProp.length; i++) {
  				// Adding the Column values to ArrayList
  				orders.add(columnOrderProp[i]);
  				hiddenColumnsMap.put(columnOrderProp[i], true);
  			}

  			/* Condition to check column hidden is null or empty */
  			if (!SwtUtil.isEmptyOrNull(hiddenColumns)) {
  				lstHiddenColunms = new ArrayList<String>();
  				/* Split the hidden columns , and save in string array */
  				hiddenColumnsProp = hiddenColumns.split(",");

  				for (int i = 0; i < hiddenColumnsProp.length; i++) {
  					/* Adding the Column values to ArrayList */
  					lstHiddenColunms.add(hiddenColumnsProp[i]);
  				}

  				Iterator<String> listKeys = hiddenColumnsMap.keySet().iterator();
  				for (int i = 0; i < hiddenColumnsMap.size(); i++) {
  					String columnKey = listKeys.next();
  					// boolean found = false;
  					for (int j = 0; j < lstHiddenColunms.size(); j++) {
  						if (columnKey.equals(lstHiddenColunms.get(j))) {
  							hiddenColumnsMap.put(columnKey, false);
  							break;
  						}
  					}
  				}
  			}

  			columnOrderItr = orders.iterator();
  			lstColumns = new ArrayList<ColumnInfo>();

  			while (columnOrderItr.hasNext()) {
  				String order = (String) columnOrderItr.next();
 				ColumnInfo tmpColumnInfo = null;
  				// Host Id column
  				if (order.equals(SwtConstants.CCY_ACC_MAINT_PERIOD_CCY_TAGNAME)) {
  					tmpColumnInfo = (new ColumnInfo(SwtUtil.getMessage(SwtConstants.CCY_ACC_MAINT_PERIOD_CCY_HEADING, request),
  							SwtConstants.CCY_ACC_MAINT_PERIOD_CCY_TAGNAME, SwtConstants.COLUMN_TYPE_STRING, 0,
  							Integer.parseInt(widths.get(SwtConstants.CCY_ACC_MAINT_PERIOD_CCY_TAGNAME)), false,
  							true, hiddenColumnsMap.get(SwtConstants.CCY_ACC_MAINT_PERIOD_CCY_TAGNAME)));
  					
  					tmpColumnInfo.setHeaderTooltip(SwtUtil.getMessage(SwtConstants.CCY_ACC_MAINT_PERIOD_CCY_HEADING_TOOLTIP, request));
  					lstColumns.add(tmpColumnInfo);
  				}
  				// Entity Id column
  				if (order.equals(SwtConstants.CCY_ACC_MAINT_PERIOD_ENTITY_ID_TAGNAME)) {
  					tmpColumnInfo = (new ColumnInfo(SwtUtil.getMessage(SwtConstants.CCY_ACC_MAINT_PERIOD_ENTITY_ID_HEADING, request),
  							SwtConstants.CCY_ACC_MAINT_PERIOD_ENTITY_ID_TAGNAME, SwtConstants.COLUMN_TYPE_STRING, 1,
  							Integer.parseInt(widths.get(SwtConstants.CCY_ACC_MAINT_PERIOD_ENTITY_ID_TAGNAME)), false,
  							true, hiddenColumnsMap.get(SwtConstants.CCY_ACC_MAINT_PERIOD_ENTITY_ID_TAGNAME)));
  					
  					tmpColumnInfo.setHeaderTooltip(SwtUtil.getMessage(SwtConstants.CCY_ACC_MAINT_PERIOD_ENTITY_ID_HEADING_TOOLTIP, request));
  					lstColumns.add(tmpColumnInfo);
  				}
  				// Account Id column
  				if (order.equals(SwtConstants.CCY_ACC_MAINT_PERIOD_ACCOUNT_ID_TAGNAME)) {
  					tmpColumnInfo = (new ColumnInfo(SwtUtil.getMessage(SwtConstants.CCY_ACC_MAINT_PERIOD_ACCOUNT_ID_HEADING, request),
  							SwtConstants.CCY_ACC_MAINT_PERIOD_ACCOUNT_ID_TAGNAME, SwtConstants.COLUMN_TYPE_STRING, 2,
  							Integer.parseInt(widths.get(SwtConstants.CCY_ACC_MAINT_PERIOD_ACCOUNT_ID_TAGNAME)), false,
  							true, hiddenColumnsMap.get(SwtConstants.CCY_ACC_MAINT_PERIOD_ACCOUNT_ID_TAGNAME)));
  					
  					tmpColumnInfo.setHeaderTooltip(SwtUtil.getMessage(SwtConstants.CCY_ACC_MAINT_PERIOD_ACCOUNT_ID_HEADING_TOOLTIP, request));
  					lstColumns.add(tmpColumnInfo);
  				}
  				// Start Date column
  				if (order.equals(SwtConstants.CCY_ACC_MAINT_PERIOD_START_DATE_TAGNAME)) {
  					tmpColumnInfo = (new ColumnInfo(SwtUtil.getMessage(SwtConstants.CCY_ACC_MAINT_PERIOD_START_DATE_HEADING, request),
  							SwtConstants.CCY_ACC_MAINT_PERIOD_START_DATE_TAGNAME, SwtConstants.COLUMN_TYPE_DATE, 3,
  							Integer.parseInt(widths.get(SwtConstants.CCY_ACC_MAINT_PERIOD_START_DATE_TAGNAME)), false,
  							true, hiddenColumnsMap.get(SwtConstants.CCY_ACC_MAINT_PERIOD_START_DATE_TAGNAME)));
  					
  					tmpColumnInfo.setHeaderTooltip(SwtUtil.getMessage(SwtConstants.CCY_ACC_MAINT_PERIOD_START_DATE_HEADING_TOOLTIP, request));
  					lstColumns.add(tmpColumnInfo);
  				}
  				// End Date column
  				if (order.equals(SwtConstants.CCY_ACC_MAINT_PERIOD_END_DATE_TAGNAME)) {
  					tmpColumnInfo = (new ColumnInfo(SwtUtil.getMessage(SwtConstants.CCY_ACC_MAINT_PERIOD_END_DATE_HEADING, request),
  							SwtConstants.CCY_ACC_MAINT_PERIOD_END_DATE_TAGNAME, SwtConstants.COLUMN_TYPE_DATE, 4,
  							Integer.parseInt(widths.get(SwtConstants.CCY_ACC_MAINT_PERIOD_END_DATE_TAGNAME)), false,
  							true, hiddenColumnsMap.get(SwtConstants.CCY_ACC_MAINT_PERIOD_END_DATE_TAGNAME)));
  					
  					tmpColumnInfo.setHeaderTooltip(SwtUtil.getMessage(SwtConstants.CCY_ACC_MAINT_PERIOD_END_DATE_HEADING_TOOLTIP, request));
  					lstColumns.add(tmpColumnInfo);
  				}
  				// Minimum Reserve column
  				if (order.equals(SwtConstants.CCY_ACC_MAINT_PERIOD_MINIMUM_RESERVE_TAGNAME)) {
  					tmpColumnInfo = (new ColumnInfo(SwtUtil.getMessage(SwtConstants.CCY_ACC_MAINT_PERIOD_MINIMUM_RESERVE_HEADING, request),
  							SwtConstants.CCY_ACC_MAINT_PERIOD_MINIMUM_RESERVE_TAGNAME, SwtConstants.COLUMN_TYPE_NUMBER, 5,
  							Integer.parseInt(widths.get(SwtConstants.CCY_ACC_MAINT_PERIOD_MINIMUM_RESERVE_TAGNAME)), false,
  							true, false));
  					
  					tmpColumnInfo.setHeaderTooltip(SwtUtil.getMessage(SwtConstants.CCY_ACC_MAINT_PERIOD_MINIMUM_RESERVE_HEADING_TOOLTIP, request));
  					lstColumns.add(tmpColumnInfo);
  				}
  				// Tier column
  				if (order.equals(SwtConstants.CCY_ACC_MAINT_PERIOD_TIER_TAGNAME)) {
  					tmpColumnInfo = (new ColumnInfo(SwtUtil.getMessage(SwtConstants.CCY_ACC_MAINT_PERIOD_TIER_HEADING, request),
  							SwtConstants.CCY_ACC_MAINT_PERIOD_TIER_TAGNAME, SwtConstants.COLUMN_TYPE_NUMBER, 6,
  							Integer.parseInt(widths.get(SwtConstants.CCY_ACC_MAINT_PERIOD_TIER_TAGNAME)), false,
  							true, false));
  					
  					tmpColumnInfo.setHeaderTooltip(SwtUtil.getMessage(SwtConstants.CCY_ACC_MAINT_PERIOD_TIER_HEADING_TOOLTIP, request));
  					lstColumns.add(tmpColumnInfo);
  				}
  				// Target Avg Balance column
  				if (order.equals(SwtConstants.CCY_ACC_MAINT_PERIOD_TARGET_AVG_BALANCE_TAGNAME)) {
  					tmpColumnInfo = (new ColumnInfo(SwtUtil.getMessage(SwtConstants.CCY_ACC_MAINT_PERIOD_TARGET_AVG_BALANCE_HEADING, request),
  							SwtConstants.CCY_ACC_MAINT_PERIOD_TARGET_AVG_BALANCE_TAGNAME, SwtConstants.COLUMN_TYPE_NUMBER, 7,
  							Integer.parseInt(widths.get(SwtConstants.CCY_ACC_MAINT_PERIOD_TARGET_AVG_BALANCE_TAGNAME)), false,
  							true, hiddenColumnsMap.get(SwtConstants.CCY_ACC_MAINT_PERIOD_TARGET_AVG_BALANCE_TAGNAME)));
  					
  					tmpColumnInfo.setHeaderTooltip(SwtUtil.getMessage(SwtConstants.CCY_ACC_MAINT_PERIOD_TARGET_AVG_BALANCE_HEADING_TOOLTIP, request));
  					lstColumns.add(tmpColumnInfo);
  				}
  			// Target Avg Balance column
  				if (order.equals(SwtConstants.CCY_ACC_MAINT_PERIOD_MIN_TARGET_BALANCE_TAGNAME)) {
  					tmpColumnInfo = (new ColumnInfo(SwtUtil.getMessage(SwtConstants.CCY_ACC_MAINT_PERIOD_MIN_TARGET_BALANCE_HEADING, request),
  							SwtConstants.CCY_ACC_MAINT_PERIOD_MIN_TARGET_BALANCE_TAGNAME, SwtConstants.COLUMN_TYPE_NUMBER, 8,
  							Integer.parseInt(widths.get(SwtConstants.CCY_ACC_MAINT_PERIOD_MIN_TARGET_BALANCE_TAGNAME)), false,
  							true, hiddenColumnsMap.get(SwtConstants.CCY_ACC_MAINT_PERIOD_MIN_TARGET_BALANCE_TAGNAME)));
  					
  					tmpColumnInfo.setHeaderTooltip(SwtUtil.getMessage(SwtConstants.CCY_ACC_MAINT_PERIOD_MIN_TARGET_BALANCE_HEADING_TOOLTIP, request));
  					lstColumns.add(tmpColumnInfo);
  				}
  				// Fill Days column
  				if (order.equals(SwtConstants.CCY_ACC_MAINT_PERIOD_FILL_DAYS_TAGNAME)) {
  					tmpColumnInfo = (new ColumnInfo(SwtUtil.getMessage(SwtConstants.CCY_ACC_MAINT_PERIOD_FILL_DAYS_HEADING, request),
  							SwtConstants.CCY_ACC_MAINT_PERIOD_FILL_DAYS_TAGNAME, SwtConstants.COLUMN_TYPE_NUMBER, 9,
  							Integer.parseInt(widths.get(SwtConstants.CCY_ACC_MAINT_PERIOD_FILL_DAYS_TAGNAME)), false,
  							true, hiddenColumnsMap.get(SwtConstants.CCY_ACC_MAINT_PERIOD_FILL_DAYS_TAGNAME)));
  					
  					tmpColumnInfo.setHeaderTooltip(SwtUtil.getMessage(SwtConstants.CCY_ACC_MAINT_PERIOD_FILL_DAYS_HEADING_TOOLTIP, request));
  					lstColumns.add(tmpColumnInfo);
  				}
  				// Fill Balance column
  				if (order.equals(SwtConstants.CCY_ACC_MAINT_PERIOD_FILL_BALANCE_TAGNAME)) {
  					tmpColumnInfo = (new ColumnInfo(SwtUtil.getMessage(SwtConstants.CCY_ACC_MAINT_PERIOD_FILL_BALANCE_HEADING, request),
  							SwtConstants.CCY_ACC_MAINT_PERIOD_FILL_BALANCE_TAGNAME, SwtConstants.COLUMN_TYPE_NUMBER, 10,
  							Integer.parseInt(widths.get(SwtConstants.CCY_ACC_MAINT_PERIOD_FILL_BALANCE_TAGNAME)), false,
  							true, hiddenColumnsMap.get(SwtConstants.CCY_ACC_MAINT_PERIOD_FILL_BALANCE_TAGNAME)));
  					
  					tmpColumnInfo.setHeaderTooltip(SwtUtil.getMessage(SwtConstants.CCY_ACC_MAINT_PERIOD_FILL_BALANCE_HEADING_TOOLTIP, request));
  					lstColumns.add(tmpColumnInfo);
  				}

 				// Fill EOD Balance SOURCE column
  				if (order.equals(SwtConstants.CCY_ACC_MAINT_PERIOD_FILL_EOD_BALANCE_SRC_TAGNAME)) {
  					tmpColumnInfo = (new ColumnInfo(SwtUtil.getMessage(SwtConstants.CCY_ACC_MAINT_PERIOD_FILL_EOD_BALANCE_SRC_HEADING, request),
  							SwtConstants.CCY_ACC_MAINT_PERIOD_FILL_EOD_BALANCE_SRC_TAGNAME, SwtConstants.COLUMN_TYPE_NUMBER, 11,
  							Integer.parseInt(widths.get(SwtConstants.CCY_ACC_MAINT_PERIOD_FILL_EOD_BALANCE_SRC_TAGNAME)), false,
  							true, false));
  					
  					tmpColumnInfo.setHeaderTooltip(SwtUtil.getMessage(SwtConstants.CCY_ACC_MAINT_PERIOD_FILL_EOD_BALANCE_SRC_HEADING_TOOLTIP, request));
  					lstColumns.add(tmpColumnInfo);
  				}		
  				
  				if (order.equals(SwtConstants.CCY_ACC_MAINT_PERIOD_EXCLUDE_FILL_DAYS_TAGNAME)) {
  					tmpColumnInfo = (new ColumnInfo(SwtUtil.getMessage(SwtConstants.CCY_ACC_MAINT_PERIOD_EXCLUDE_FILL_DAYS_HEADING, request),
  							SwtConstants.CCY_ACC_MAINT_PERIOD_EXCLUDE_FILL_DAYS_TAGNAME, SwtConstants.COLUMN_TYPE_STRING, 12,
  							Integer.parseInt(widths.get(SwtConstants.CCY_ACC_MAINT_PERIOD_EXCLUDE_FILL_DAYS_TAGNAME)), false,
  							true, hiddenColumnsMap.get(SwtConstants.CCY_ACC_MAINT_PERIOD_EXCLUDE_FILL_DAYS_TAGNAME)));
  					
  					tmpColumnInfo.setHeaderTooltip(SwtUtil.getMessage(SwtConstants.CCY_ACC_MAINT_PERIOD_EXCLUDE_FILL_DAYS_HEADING_TOOLTIP, request));
  					lstColumns.add(tmpColumnInfo);
  				}	
  				
  				if (order.equals(SwtConstants.CCY_ACC_MAINT_PERIOD_FILL_EOD_BALANCE_SRC_AsSTRING_TAGNAME)) {
					tmpColumnInfo = (new ColumnInfo(
							SwtUtil.getMessage(SwtConstants.CCY_ACC_MAINT_PERIOD_FILL_EOD_BALANCE_SRC_HEADING, request),
							SwtConstants.CCY_ACC_MAINT_PERIOD_FILL_EOD_BALANCE_SRC_AsSTRING_TAGNAME,
							SwtConstants.COLUMN_TYPE_STRING, 13,
							Integer.parseInt(widths
									.get(SwtConstants.CCY_ACC_MAINT_PERIOD_FILL_EOD_BALANCE_SRC_AsSTRING_TAGNAME)),
							false, true, true));

					tmpColumnInfo.setHeaderTooltip(SwtUtil.getMessage(
							SwtConstants.CCY_ACC_MAINT_PERIOD_FILL_EOD_BALANCE_SRC_HEADING_TOOLTIP, request));
					lstColumns.add(tmpColumnInfo);
				}

  			}

  		} catch (Exception ex) {
  			// log error message
  			log.error(this.getClass().getName()
  					+ " - Exception Catched in [getGridColumns] method : - " + ex.getMessage());

  			throw SwtErrorHandler.getInstance().handleException(ex, "getGridColumns",
  					this.getClass());

  		} finally {
  			// Nullify Objects
  			orders = null;
  			columnOrderProp = null;
  			columnOrderItr = null;
  			widths = null;
  			columnWidthProperty = null;
  			// log debug message
  			log.debug(this.getClass().getName() + " - [ getGridColumns ] - Exit");
  		}
  		// return XML columns
  		return lstColumns;
  	}
	
	
	public String getLists() throws SwtException {
HttpServletRequest request = ServletActionContext.getRequest();
HttpServletResponse response = ServletActionContext.getResponse();

		// Entity Id
		String entityId = null;
		// Currency Code
		String defaultCurrency = null;
		// To host for application
		String hostId = null;
		ResponseHandler responseHandler = null;
		SwtResponseConstructor responseConstructor = null;
		SwtXMLWriter xmlWriter = null;
		String screenName= null;
		try {
			log.debug(this.getClass().getName()
					+ " - [getLists] - Entering");
			// to get the host id for application
			hostId = CacheManager.getInstance().getHostId();
			// get the entityId,currencyCode,movType,inputSource from request
			entityId = request.getParameter("entityId");
			screenName = request.getParameter("screenName");
			// get the default currency for entity id
			defaultCurrency = SwtUtil.getDomesticCurrencyForUser(request,
					hostId, entityId);
			responseHandler = new ResponseHandler();
			responseConstructor = new SwtResponseConstructor();
			xmlWriter = responseConstructor.getXMLWriter();

			xmlWriter.startElement(SwtConstants.REFRESH_COMBO_LIST);

			responseConstructor.formRequestReply(Boolean.parseBoolean(SwtConstants.STR_TRUE),
					SwtConstants.DATA_FETCH_OK);
			
			// forms singleton node
			xmlWriter.startElement(SwtConstants.SINGLETONS);
							
			responseConstructor.createElement("defaultCurrency",
					defaultCurrency);
			
			xmlWriter.endElement(SwtConstants.SINGLETONS);	
			
			ArrayList<SelectInfo> lstSelect = new ArrayList<SelectInfo>();
			// options drop down list
			ArrayList<OptionInfo> lstOptions = new ArrayList<OptionInfo>();
				
			/***** Currency Combo Start ***********/
			lstOptions = new ArrayList<OptionInfo>();
			LabelValueBean row = null;
			Collection currencyList = putCurrencyFullAccessListInReq(request, CacheManager.getInstance().getHostId(),entityId);
			Iterator j = currencyList.iterator();
			row = null;
			while (j.hasNext()) {
				row = (LabelValueBean) j.next();
				lstOptions.add(new OptionInfo(row.getValue(), row.getLabel(), false));
			}
			lstSelect.add(new SelectInfo("currencyList", lstOptions));		
			/***** Currency Combo End ***********/
			
			
			/***** Account list Combo Start ***********/
			lstOptions = new ArrayList<OptionInfo>();
			Collection accountList = acctCcyPeriodMaintenanceManager.getAccountIdDropDown(
					CacheManager.getInstance().getHostId(), entityId,
					defaultCurrency, screenName);
			j = accountList.iterator();
			row = null;
			while (j.hasNext()) {
				row = (LabelValueBean) j.next();
				lstOptions.add(new OptionInfo(row.getValue(), row.getLabel(), false));
			}
			lstSelect.add(new SelectInfo("accountList", lstOptions));
			/***** Account list Combo End ***********/
						
			responseConstructor.formSelect(lstSelect);
			
			xmlWriter.endElement(SwtConstants.REFRESH_COMBO_LIST);
			request.setAttribute("data", xmlWriter.getData());
			log.debug(this.getClass().getName()
					+ " - [getLists] - Existing");
			return ("data");

		} catch (SwtException swtexp) {
			log
					.error("SwtException Catch in AcctCcyPeriodMaintenanceAction.'getLists' method : "
							+ swtexp.getMessage());
			SwtUtil.logException(swtexp, request, "");
		} catch (Exception exp) {
			log
					.error("Exception Catch in AcctCcyPeriodMaintenanceAction.'getLists' method : "
							+ exp.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "getLists", AcctCcyPeriodMaintenanceAction.class),
					request, "");
		} finally {
			// nullify objects
			responseConstructor = null;
			xmlWriter=null;
			entityId = null;
			defaultCurrency = null;
		}
		return null;
	}
	
	public String getUpdatedAccountList() throws SwtException {
HttpServletRequest request = ServletActionContext.getRequest();
HttpServletResponse response = ServletActionContext.getResponse();

		// Entity Id
		String entityId = null;
		// Currency Code
		String currencyCode = null;
		// To hold the default currency for entity
		String defaultCurrency = null;
		// To host for application
		String hostId = null;
		ResponseHandler responseHandler = null;
		SwtResponseConstructor responseConstructor = null;
		SwtXMLWriter xmlWriter = null;
		String screenName= null;
		try {
			log.debug(this.getClass().getName() + " - [getUpdatedAccountList] - Entering");
			// to get the host id for application
			hostId = CacheManager.getInstance().getHostId();
			// get the entityId,currencyCode  from request
			entityId = request.getParameter("entityId");
			currencyCode = request.getParameter("currencyCode");
			screenName = request.getParameter("screenName");
			// get the default currency for entity id
			defaultCurrency = SwtUtil.getDomesticCurrencyForUser(request, hostId, entityId);
			responseHandler = new ResponseHandler();
			responseConstructor = new SwtResponseConstructor();
			xmlWriter = responseConstructor.getXMLWriter();

			xmlWriter.startElement(SwtConstants.REFRESH_COMBO_LIST);

			responseConstructor.formRequestReply(Boolean.parseBoolean(SwtConstants.STR_TRUE),
					SwtConstants.DATA_FETCH_OK);
			responseConstructor.createElement("defaultCurrency", defaultCurrency);

			ArrayList<SelectInfo> lstSelect = new ArrayList<SelectInfo>();
			// options drop down list
			ArrayList<OptionInfo> lstOptions = new ArrayList<OptionInfo>();

			/***** Account list Combo Start ***********/
			lstOptions = new ArrayList<OptionInfo>();
			LabelValueBean row = null;
			Collection accountList = acctCcyPeriodMaintenanceManager.getAccountIdDropDown(
					CacheManager.getInstance().getHostId(), entityId,
					currencyCode, screenName);
			Iterator j = accountList.iterator();
			row = null;
			while (j.hasNext()) {
				row = (LabelValueBean) j.next();
				lstOptions.add(new OptionInfo(row.getValue(), row.getLabel(), false));
			}
			lstSelect.add(new SelectInfo("accountList", lstOptions));
			/***** Account list Combo End ***********/
			
			responseConstructor.formSelect(lstSelect);
			xmlWriter.endElement(SwtConstants.REFRESH_COMBO_LIST);
			request.setAttribute("data", xmlWriter.getData());
			log.debug(this.getClass().getName() + " - [getUpdatedAccountList] - Existing");
			return ("data");

		} catch (SwtException swtexp) {
			log.error("SwtException Catch in AcctCcyPeriodMaintenanceAction.'getUpdatedAccountList' method : " + swtexp.getMessage());
			SwtUtil.logException(swtexp, request, "");
		} catch (Exception exp) {
			log.error("Exception Catch in AcctCcyPeriodMaintenanceAction.'getUpdatedAccountList' method : " + exp.getMessage());
			SwtUtil.logException(
					SwtErrorHandler.getInstance().handleException(exp, "getUpdatedAccountList", PreAdviceInputAction.class),
					request, "");
		} finally {
			// nullify objects
			responseConstructor = null;
			xmlWriter=null;
			entityId = null;
			hostId = null;
			currencyCode = null;
			defaultCurrency = null;
		}
		return null;
	}
	
	
	public String OpenSubAcctCcyPeriodMaint() throws SwtException {
HttpServletRequest request = ServletActionContext.getRequest();
HttpServletResponse response = ServletActionContext.getResponse();

		request.setAttribute("screenName", request.getParameter("screenName"));
		request.setAttribute("params", request.getParameter("allParams"));
		return ("subScreen");
	}
	
	
	public String displayLogScreen() throws SwtException {
HttpServletRequest request = ServletActionContext.getRequest();
HttpServletResponse response = ServletActionContext.getResponse();

		return ("logScreen");
	}
	
	
	public String subDisplay()
			throws SwtException {
HttpServletRequest request = ServletActionContext.getRequest();
HttpServletResponse response = ServletActionContext.getResponse();

		/* Local variable declaration */
		// To host for application
		String hostId = null;
		// To hold the current entity from screen
		String entityId = null;
		// To hold the current entity from screen
		String accountId = null;
		// To hold selected currency in screen
		String currencyCode = null;
		String dateFormat = null;
		// To hold the role for current user
		String roleId = null;
		ResponseHandler responseHandler = null;
		SwtResponseConstructor responseConstructor = null;
		SwtXMLWriter xmlWriter = null;
		String amountFormat = null;
		SystemFormats sysFormat=null;
		String screenName= null;
		try {
			log.debug(this.getClass().getName() + " - [subDisplay()] - Entry");

			sysFormat = SwtUtil.getCurrentSystemFormats(request.getSession());
			dateFormat= SwtUtil.getCurrentDateFormat(request.getSession());
			amountFormat=sysFormat.getCurrencyFormat();
			entityId= request.getParameter("entityId");		
			currencyCode = request.getParameter("currencyCode");
			accountId = request.getParameter("accountId");
			screenName = request.getParameter("screenName");
			// to get the host id for application
			hostId = CacheManager.getInstance().getHostId();
			
			
			if (SwtUtil.isEmptyOrNull(entityId)) {				
			entityId = SwtUtil.getUserCurrentEntity(request.getSession());				
			}
			
			
			if (SwtUtil.isEmptyOrNull(currencyCode)) {	
			// get the default currency for entity id
			currencyCode = SwtUtil.getDomesticCurrencyForUser(request,
					hostId, entityId);
			}
			
			if (SwtUtil.isEmptyOrNull(accountId)) {	
			// get the default currency for entity id
				accountId = "";
			}

			// Get role id associated with the logged-in user
			roleId = ((CommonDataManager) request.getSession().getAttribute(SwtConstants.CDM_BEAN)).getUser()
					.getRoleId();
						
			responseHandler = new ResponseHandler();
			responseConstructor = new SwtResponseConstructor();
			xmlWriter = responseConstructor.getXMLWriter();

			xmlWriter.startElement(SwtConstants.CCY_ACC_MAINT_PERIOD);

			responseConstructor.formRequestReply(Boolean.parseBoolean(SwtConstants.STR_TRUE),
					SwtConstants.DATA_FETCH_OK);
			// forms singleton node
			xmlWriter.startElement(SwtConstants.SINGLETONS);
			
			responseConstructor.createElement("defaultCurrency",
					currencyCode);
			responseConstructor.createElement("defaultEntity",
					entityId);
			responseConstructor.createElement("defaultAccount",
					accountId);
			responseConstructor.createElement("displayedDate", SwtUtil.formatDate(SwtUtil
					.getSystemDatewithoutTime(), SwtUtil.getCurrentSystemFormats(
					request.getSession()).getDateFormatValue()));
			responseConstructor.createElement("dateFormat",
					dateFormat);
			responseConstructor.createElement(SwtConstants.CURRENCYPATTERN,
					SwtUtil.getCurrentSystemFormats(request.getSession()).getCurrencyFormat());
			
			xmlWriter.endElement(SwtConstants.SINGLETONS); 
			
			/***** Entity Combo Start ***********/
			ArrayList<SelectInfo> lstSelect = new ArrayList<SelectInfo>();
			// options drop down list
			ArrayList<OptionInfo> lstOptions = new ArrayList<OptionInfo>();

			lstOptions = new ArrayList<OptionInfo>();
			LabelValueBean row = null;
			Collection entityList = putFullAcessEntityListInReq(request,roleId);
			Iterator j = entityList.iterator();
			row = null;
			while (j.hasNext()) {
				row = (LabelValueBean) j.next();
				lstOptions.add(new OptionInfo(row.getValue(), row.getLabel(), false));
			}
			lstSelect.add(new SelectInfo("entityList", lstOptions));			
			/***** Entity Combo End ***********/
			

			/***** Currency Combo Start ***********/
			lstOptions = new ArrayList<OptionInfo>();
			Collection currencyList = putCurrencyFullAccessListInReq(request, hostId, entityId);
			 j = currencyList.iterator();
			row = null;
			while (j.hasNext()) {
				row = (LabelValueBean) j.next();
				lstOptions.add(new OptionInfo(row.getValue(), row.getLabel(), false));
			}
			lstSelect.add(new SelectInfo("currencyList", lstOptions));		
			/***** Currency Combo End ***********/	
			
			
			/***** Account Combo Start ***********/
			lstOptions = new ArrayList<OptionInfo>();
			Collection accountList = acctCcyPeriodMaintenanceManager.getAccountIdDropDown(
					CacheManager.getInstance().getHostId(), entityId,
					currencyCode, screenName);
			j = accountList.iterator();
			row = null;
			lstOptions.add(new OptionInfo("","", false));
			while (j.hasNext()) {
				row = (LabelValueBean) j.next();
				lstOptions.add(new OptionInfo(row.getValue(), row.getLabel(), false));
			}
			lstSelect.add(new SelectInfo("accountList", lstOptions));	
			/***** Account Combo End ***********/	
			
			
			responseConstructor.formSelect(lstSelect);
					
			
			xmlWriter.endElement(SwtConstants.CCY_ACC_MAINT_PERIOD);
			request.setAttribute("data", xmlWriter.getData());

			// set the method name,last ref time and access level on request
			// attribute which are used in front end
			request.setAttribute("methodName", "success");
			log.debug(this.getClass().getName() + " - [SubDisplay()] - Exit");
			return ("data");
		} catch (SwtException swtexp) {
			log
					.error("SwtException Catch in AcctCcyPeriodMaintenanceAction.'SubDisplay' method : "
							+ swtexp.getMessage());
			SwtUtil.logException(swtexp, request, "");
			return ("fail");
		} catch (Exception exp) {
			log
					.error("Exception Catch in AcctCcyPeriodMaintenanceAction.'SubDisplay' method : "
							+ exp.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "SubDisplay", AcctCcyPeriodMaintenanceAction.class), request, "");
			return ("fail");
		} finally {
			/* null the objects created already. */
			responseConstructor = null;
			xmlWriter=null;
			hostId = null;
			entityId = null;
			accountId = null;
			currencyCode = null;
			roleId = null;
			dateFormat = null;
			amountFormat = null;
			sysFormat = null;
		}
	}
	
	
	
	
	public String checkIfOverlaps() {
HttpServletRequest request = ServletActionContext.getRequest();
HttpServletResponse response = ServletActionContext.getResponse();

	boolean overlapsFlag = false;
	String entityId=null;
	String accountId=null;
	String startDate=null;
	String endDate=null;
	String hostId=null;
	String checkFlag = null;
	String dateFormat = null;
	ResponseHandler responseHandler = null;
	SwtResponseConstructor responseConstructor = null;
	SwtXMLWriter xmlWriter = null;
	SystemFormats sysFormat=null;

	try {
		sysFormat = SwtUtil.getCurrentSystemFormats(request.getSession());
		dateFormat= SwtUtil.getCurrentDateFormat(request.getSession());
		entityId = request.getParameter("entityId");
		accountId = request.getParameter("accountId");
		startDate = request.getParameter("startDate");
		endDate = request.getParameter("endDate");
		// to get the host id for application
		hostId = CacheManager.getInstance().getHostId();

		overlapsFlag=  acctCcyPeriodMaintenanceManager.checkIfOverlaps(hostId, entityId, accountId, 
        		SwtUtil.parseDate(startDate, sysFormat.getDateFormatValue()),  SwtUtil.parseDate(endDate, sysFormat.getDateFormatValue()));
        if (overlapsFlag) {
        	checkFlag="true";	
        }else {
        	checkFlag="false";
        }
		responseHandler = new ResponseHandler();
		responseConstructor = new SwtResponseConstructor();
		xmlWriter = responseConstructor.getXMLWriter();

		xmlWriter.startElement(SwtConstants.CCY_ACC_MAINT_PERIOD);

		responseConstructor.formRequestReply(Boolean.parseBoolean(SwtConstants.STR_TRUE),
				SwtConstants.DATA_FETCH_OK);
		// forms singleton node
		xmlWriter.startElement(SwtConstants.SINGLETONS);
		
		responseConstructor.createElement("checkFlag",
				checkFlag);
		
		xmlWriter.endElement(SwtConstants.SINGLETONS); 
		
		
		xmlWriter.endElement(SwtConstants.CCY_ACC_MAINT_PERIOD);
		request.setAttribute("data", xmlWriter.getData());

		request.setAttribute("methodName", "success");
		log.debug(this.getClass().getName() + " - [checkIfOverlaps()] - Exit");
		return ("data");
	} catch (SwtException swtexp) {
		log
				.error("SwtException Catch in AcctCcyPeriodMaintenanceAction.'checkIfOverlaps' method : "
						+ swtexp.getMessage());
		SwtUtil.logException(swtexp, request, "");
		return ("fail");
	} catch (Exception exp) {
		log
				.error("Exception Catch in AcctCcyPeriodMaintenanceAction.'checkIfOverlaps' method : "
						+ exp.getMessage());
		SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
				exp, "checkIfOverlaps", AcctCcyPeriodMaintenanceAction.class), request, "");
		return ("fail");
	}finally {
		//nullify objects
		responseConstructor = null;
		xmlWriter=null;
		overlapsFlag = false;
		entityId=null;
		accountId=null;
		startDate=null;
		endDate=null;
		hostId=null;
		checkFlag = null;
	}
}	
	
	
	public String saveAcctCcyPeriodMaint()
			throws SwtException {
HttpServletRequest request = ServletActionContext.getRequest();
HttpServletResponse response = ServletActionContext.getResponse();

		// variable to get the host id
		String hostId = null;
		// variable to get the entity id
		String entityId = null;
		// variable to get the currency code
		String currencyCode = null;
		// variable to get the account ID
		String accountId = null;
		// variable to get the start date
		String startDate = null;
		// variable to get the end date
		String endDate = null;
		// variable to get the minimum reserve
		String minReserve = null;
		// variable to get the tier
		String tier = null;
		// variable to get the target AVG balance
		String targetAvgBal = null;
		// variable to get the fill days
		String fillDays = null;
		// variable to get the fill balance
		String fillBalance = null;
		// variable to get the fill balance
		String minTargetBalance = null;
		// variable to get the fill balance
		String excludeFillPeriod = null;
		// variable to get the EOD Balance Source
		String eodBalanceSource= null;
		// variable to get the saveStatus
		String action = null;
		// variable to get the movement
		AccountCurrencyPeriodMaintenance acctCcyPeriod = null;		
		// variable to get the system format
		SystemFormats sysFormat = null;

		try {
			log.debug(this.getClass().getName() + " - [saveAcctCcyPeriodMaint] - Entry");
			// get host id for application and set value in movement bean
			hostId = CacheManager.getInstance().getHostId();
			// get current system formats to format the start - end dates
			sysFormat = SwtUtil.getCurrentSystemFormats(request.getSession());
			acctCcyPeriod= new AccountCurrencyPeriodMaintenance();
			entityId= request.getParameter("entityId");
			currencyCode= request.getParameter("ccyCode");
			accountId= request.getParameter("accountId");
			startDate= request.getParameter("startDate");
			endDate= request.getParameter("endDate");
			minReserve= request.getParameter("minReserve");
			tier= request.getParameter("tier");
			targetAvgBal= request.getParameter("targetAvgBal");
			fillDays= request.getParameter("fillDays");
			fillBalance= request.getParameter("fillBalance");
			eodBalanceSource= request.getParameter("eodBalanceSource");
			minTargetBalance= request.getParameter("mintargetBalance");
			excludeFillPeriod= request.getParameter("excludeFillPeriodFromAvg");
			
			action = request.getParameter("action");
			
			//populate values
			acctCcyPeriod.getId().setHostId(hostId);		
			acctCcyPeriod.getId().setEntityId(entityId);
			acctCcyPeriod.getId().setAccountId(accountId);
			acctCcyPeriod.getId().setStartDate((SwtUtil.parseDate(startDate, sysFormat
					.getDateFormatValue())));
			acctCcyPeriod.setEndDate((SwtUtil.parseDate(endDate, sysFormat
					.getDateFormatValue())));
			
			acctCcyPeriod.setMinimumReserve(SwtUtil.parseCurrency(minReserve, sysFormat.getCurrencyFormat()));
			
			if(!SwtUtil.isEmptyOrNull(tier))
			acctCcyPeriod.setTier(SwtUtil.parseCurrency(tier, sysFormat.getCurrencyFormat()));//setTier(Integer.parseInt(tier));
			if(!SwtUtil.isEmptyOrNull(targetAvgBal))
			acctCcyPeriod.setTargetAvgBalance(SwtUtil.parseCurrency(targetAvgBal, sysFormat.getCurrencyFormat()));
			if(!SwtUtil.isEmptyOrNull(fillDays))
			acctCcyPeriod.setFillDays(SwtUtil.parseCurrency(fillDays, sysFormat.getCurrencyFormat()));
			if(!SwtUtil.isEmptyOrNull(fillBalance))
			acctCcyPeriod.setFillBalance(SwtUtil.parseCurrency(fillBalance, sysFormat.getCurrencyFormat()));
			acctCcyPeriod.setEofBalanceSource(eodBalanceSource);
			
			if(!SwtUtil.isEmptyOrNull(minTargetBalance))
				acctCcyPeriod.setMinTargetBalance(SwtUtil.parseCurrency(minTargetBalance, sysFormat.getCurrencyFormat()));
			
			acctCcyPeriod.setExcludeFillPeriod("true".equalsIgnoreCase(excludeFillPeriod)?"Y":"N");
			//save or update according to the action
			acctCcyPeriodMaintenanceManager.saveAcctCcyPeriodRecord(acctCcyPeriod, action);

			request.setAttribute("reply_status_ok", "true");
			request.setAttribute("reply_message", "SUCCESS");

		} catch (SwtException ex) {
			// log error message
			log.error(this.getClass().getName() + " - [save] - Exception -" + ex.getMessage());
			// Log error message in DB
			SwtUtil.logException(
					SwtErrorHandler.getInstance().handleException(ex, "saveCurrency", AcctCcyPeriodMaintenanceAction.class),
					request, "");
			request.setAttribute("reply_status_ok", "false");
			if (ex.getErrorCode().equals(
					"errors.DataIntegrityViolationExceptioninAdd")) {
				request.setAttribute("reply_message",
						"RECORD_EXIST");
			}else {
				request.setAttribute("reply_message",
						"ERROR_SAVE");
			}
			

		} finally {
			log.debug(this.getClass().getName() + "- [save] - Exit");
		}
		return ("statechange");
	}
	
	public String sendSaveResponse() throws SwtException {
		SwtResponseConstructor responseConstructor = null;
		SwtXMLWriter xmlWriter = null;
		String userId = null;
		String componentId = null;

		// debug message
		log.debug(this.getClass().getName()
					+ " - [ sendSaveResponse ] - Entry");
	
		HttpServletRequest request = ServletActionContext.getRequest();
		userId = SwtUtil.getCurrentUser(request.getSession()).getId().getUserId();
		responseConstructor = new SwtResponseConstructor();
	
		xmlWriter = responseConstructor.getXMLWriter();
		// Get component ID
		componentId = "AcctCcyPeriodMaint";	
		// Adding screen id and current user id as attributes
		xmlWriter.addAttribute(SwtConstants.SCREEN_ID, componentId);
		xmlWriter.addAttribute(SwtConstants.CURRUSER, userId);
		xmlWriter.startElement("acctCcyPeriodMaint");
		xmlWriter.clearAttribute();
		responseConstructor.formRequestReply(Boolean.parseBoolean(SwtConstants.STR_TRUE), SwtConstants.DATA_FETCH_OK);
		xmlWriter.endElement("acctCcyPeriodMaint");
			
		request.setAttribute("data", xmlWriter.getData());
		return ("data");
	}
	
	
	public String deleteAcctCcyPeriod() throws SwtException {
HttpServletRequest request = ServletActionContext.getRequest();
HttpServletResponse response = ServletActionContext.getResponse();

		SwtXMLWriter xmlWriter = null;
		String componentId = null;
		SwtResponseConstructor responseConstructor = null;

		String hostId = null;
		String entityId = null;
		String accountId = null;
		String startDate = null;
		String endDate = null;
		AccountCurrencyPeriodMaintenance acctCcyPeriod = null;
		Boolean found =false;
		try {
			log.debug(this.getClass().getName() + "- [deleteUserSettings] - starting ");			
			// variable to get the system format
			SystemFormats sysFormat = null;
			// get current system formats to format the start - end dates
			sysFormat = SwtUtil.getCurrentSystemFormats(request.getSession());
			
			entityId= request.getParameter("entityId");
			accountId= request.getParameter("accountId");
			startDate= request.getParameter("startDate");
			endDate= request.getParameter("endDate");
			hostId = CacheManager.getInstance().getHostId();

			found=acctCcyPeriodMaintenanceManager.checkIfAcctCcyPeriodExists(hostId, entityId, accountId, SwtUtil.parseDate(startDate,SwtUtil.getCurrentSystemFormats(
					request.getSession()).getDateFormatValue()), SwtUtil.parseDate(endDate,SwtUtil.getCurrentSystemFormats(
							request.getSession()).getDateFormatValue()));
			if(found) {
			// delete  preAdvice input options
			acctCcyPeriodMaintenanceManager.deleteAcctCcyPeriodMaintRecord(hostId, entityId, accountId, SwtUtil.parseDate(startDate,SwtUtil.getCurrentSystemFormats(
					request.getSession()).getDateFormatValue()), SwtUtil.parseDate(endDate,SwtUtil.getCurrentSystemFormats(
					request.getSession()).getDateFormatValue()));
			}
			responseConstructor = new SwtResponseConstructor();
			
			xmlWriter = responseConstructor.getXMLWriter();
			// Get component ID
			componentId = "AcctCcyPeriodMaint";	
			// Adding screen id and current user id as attributes
			xmlWriter.addAttribute(SwtConstants.SCREEN_ID, componentId);			
			xmlWriter.startElement("acctCcyPeriodMaint");
			xmlWriter.clearAttribute();
			responseConstructor.formRequestReply(Boolean.parseBoolean(SwtConstants.STR_TRUE), SwtConstants.DATA_FETCH_OK);
			xmlWriter.endElement("acctCcyPeriodMaint");
			request.setAttribute("methodName", "success");
			request.setAttribute("data", xmlWriter.getData());
			return ("data");
		} catch (Exception e) {
			log.error(this.getClass().getName() + " - [deleteUserSettings] - Exception - " + e.getMessage());
			// Re-throw an exception
			throw SwtErrorHandler.getInstance().handleException(e, "deleteUserSettings", AcctCcyPeriodMaintenanceAction.class);

		} finally {
			responseConstructor = null;
			xmlWriter = null;
			acctCcyPeriod = null;
			hostId = null;
			entityId = null;
			accountId = null;
			startDate = null;
			endDate = null;
		}
	}
	
	
	private List<ColumnInfo> getLogGridColumns(String width, String columnOrder, String hiddenColumns,
  			HttpServletRequest request) throws SwtException {
  		// Array list to hold column order
  		ArrayList<String> orders = null;
  		// String array variable to hold column order property
  		String[] columnOrderProp = null;
  		// Iterator variable to hold column order
  		Iterator<String> columnOrderItr = null;
  		// Hash map to hold column width
  		LinkedHashMap<String, String> widths = null;
  		// String array variable to hold width property
  		String[] columnWidthProperty = null;
  		// List for column info
  		List<ColumnInfo> lstColumns = null;
  		/* Hash map to hold column hidden_Columns */
  		HashMap<String, Boolean> hiddenColumnsMap = new LinkedHashMap<String, Boolean>();
  		/* Array list to hold hidden Column array */
  		ArrayList<String> lstHiddenColunms = null;
  		/* String array variable to hold hidden columns property */
  		String[] hiddenColumnsProp = null;
  		try {
  			// log debug message
  			log.debug(this.getClass().getName() + " - [ getLogGridColumns ] - Entry");
  			// Condition to check width is null

  			if (SwtUtil.isEmptyOrNull(width)) {
  				// default width for columns
				width =  SwtConstants.CCY_ACC_MAINT_PERIOD_LOG_DATE_TAGNAME + "=120" + ","
						+ SwtConstants.CCY_ACC_MAINT_PERIOD_LOG_TIME_TAGNAME + "=120" + ","
						+ SwtConstants.CCY_ACC_MAINT_PERIOD_LOG_USER_TAGNAME + "=120" + ","
						+ SwtConstants.CCY_ACC_MAINT_PERIOD_LOG_IP_ADDRESS_TAGNAME + "=150" + ","
						+ SwtConstants.CCY_ACC_MAINT_PERIOD_LOG_REFERENCE_TAGNAME + "=270" + ","
						+ SwtConstants.CCY_ACC_MAINT_PERIOD_LOG_ACTION_TAGNAME + "=120" ;
   			}

  			// Obtain width for each column
  			columnWidthProperty = width.split(",");

  			// Loop to insert each column in hash map
  			widths = new LinkedHashMap<String, String>();
  			for (int i = 0; i < columnWidthProperty.length; i++) {
  				// Condition to check index of = is -1
  				if (columnWidthProperty[i].indexOf("=") != -1) {
  					String[] propval = columnWidthProperty[i].split("=");
  					widths.put(propval[0], propval[1]);
  				}
  			}

  			// Condition to check column order is null or empty
  			if (SwtUtil.isEmptyOrNull(columnOrder)) {
  				// Default values for column order
				columnOrder = SwtConstants.CCY_ACC_MAINT_PERIOD_LOG_DATE_TAGNAME + ","
						+ SwtConstants.CCY_ACC_MAINT_PERIOD_LOG_TIME_TAGNAME + ","
						+ SwtConstants.CCY_ACC_MAINT_PERIOD_LOG_USER_TAGNAME + ","
						+ SwtConstants.CCY_ACC_MAINT_PERIOD_LOG_IP_ADDRESS_TAGNAME + ","
						+ SwtConstants.CCY_ACC_MAINT_PERIOD_LOG_REFERENCE_TAGNAME + ","
						+ SwtConstants.CCY_ACC_MAINT_PERIOD_LOG_ACTION_TAGNAME;
}
  			orders = new ArrayList<String>();
  			// Split the columns using , and save in string array
  			columnOrderProp = columnOrder.split(",");

  			// Loop to enter column order in array list
  			for (int i = 0; i < columnOrderProp.length; i++) {
  				// Adding the Column values to ArrayList
  				orders.add(columnOrderProp[i]);
  				hiddenColumnsMap.put(columnOrderProp[i], true);
  			}

  			/* Condition to check column hidden is null or empty */
  			if (!SwtUtil.isEmptyOrNull(hiddenColumns)) {
  				lstHiddenColunms = new ArrayList<String>();
  				/* Split the hidden columns , and save in string array */
  				hiddenColumnsProp = hiddenColumns.split(",");

  				for (int i = 0; i < hiddenColumnsProp.length; i++) {
  					/* Adding the Column values to ArrayList */
  					lstHiddenColunms.add(hiddenColumnsProp[i]);
  				}

  				Iterator<String> listKeys = hiddenColumnsMap.keySet().iterator();
  				for (int i = 0; i < hiddenColumnsMap.size(); i++) {
  					String columnKey = listKeys.next();
  					// boolean found = false;
  					for (int j = 0; j < lstHiddenColunms.size(); j++) {
  						if (columnKey.equals(lstHiddenColunms.get(j))) {
  							hiddenColumnsMap.put(columnKey, false);
  							break;
  						}
  					}
  				}
  			}

  			columnOrderItr = orders.iterator();
  			lstColumns = new ArrayList<ColumnInfo>();

  			while (columnOrderItr.hasNext()) {
  				String order = (String) columnOrderItr.next();
 				ColumnInfo tmpColumnInfo = null;
  				// Host Id column
  				if (order.equals(SwtConstants.CCY_ACC_MAINT_PERIOD_LOG_DATE_TAGNAME)) {
  					tmpColumnInfo = (new ColumnInfo(SwtUtil.getMessage(SwtConstants.CCY_ACC_MAINT_PERIOD_LOG_DATE_HEADING, request),
  							SwtConstants.CCY_ACC_MAINT_PERIOD_LOG_DATE_TAGNAME, SwtConstants.COLUMN_TYPE_STRING, 0,
  							Integer.parseInt(widths.get(SwtConstants.CCY_ACC_MAINT_PERIOD_LOG_DATE_TAGNAME)), false,
  							true, hiddenColumnsMap.get(SwtConstants.CCY_ACC_MAINT_PERIOD_LOG_DATE_TAGNAME)));
  					
  					tmpColumnInfo.setHeaderTooltip(SwtUtil.getMessage(SwtConstants.CCY_ACC_MAINT_PERIOD_LOG_DATE_HEADING_TOOLTIP, request));
  					lstColumns.add(tmpColumnInfo);
  				}
  				// Entity Id column
  				if (order.equals(SwtConstants.CCY_ACC_MAINT_PERIOD_LOG_TIME_TAGNAME)) {
  					tmpColumnInfo = (new ColumnInfo(SwtUtil.getMessage(SwtConstants.CCY_ACC_MAINT_PERIOD_LOG_TIME_HEADING, request),
  							SwtConstants.CCY_ACC_MAINT_PERIOD_LOG_TIME_TAGNAME, SwtConstants.COLUMN_TYPE_STRING, 1,
  							Integer.parseInt(widths.get(SwtConstants.CCY_ACC_MAINT_PERIOD_LOG_TIME_TAGNAME)), false,
  							true, hiddenColumnsMap.get(SwtConstants.CCY_ACC_MAINT_PERIOD_LOG_TIME_TAGNAME)));
  					
  					tmpColumnInfo.setHeaderTooltip(SwtUtil.getMessage(SwtConstants.CCY_ACC_MAINT_PERIODv_TIME_HEADING_TOOLTIP, request));
  					lstColumns.add(tmpColumnInfo);
  				}
  				// Account Id column
  				if (order.equals(SwtConstants.CCY_ACC_MAINT_PERIOD_LOG_USER_TAGNAME)) {
  					tmpColumnInfo = (new ColumnInfo(SwtUtil.getMessage(SwtConstants.CCY_ACC_MAINT_PERIOD_LOG_USER_HEADING, request),
  							SwtConstants.CCY_ACC_MAINT_PERIOD_LOG_USER_TAGNAME, SwtConstants.COLUMN_TYPE_STRING, 2,
  							Integer.parseInt(widths.get(SwtConstants.CCY_ACC_MAINT_PERIOD_LOG_USER_TAGNAME)), false,
  							true, hiddenColumnsMap.get(SwtConstants.CCY_ACC_MAINT_PERIOD_LOG_USER_TAGNAME)));
  					
  					tmpColumnInfo.setHeaderTooltip(SwtUtil.getMessage(SwtConstants.CCY_ACC_MAINT_PERIOD_LOG_USER_HEADING_TOOLTIP, request));
  					lstColumns.add(tmpColumnInfo);
  				}
  				// Start Date column
  				if (order.equals(SwtConstants.CCY_ACC_MAINT_PERIOD_LOG_IP_ADDRESS_TAGNAME)) {
  					tmpColumnInfo = (new ColumnInfo(SwtUtil.getMessage(SwtConstants.CCY_ACC_MAINT_PERIOD_LOG_IP_ADDRESS_HEADING, request),
  							SwtConstants.CCY_ACC_MAINT_PERIOD_LOG_IP_ADDRESS_TAGNAME, SwtConstants.COLUMN_TYPE_STRING, 3,
  							Integer.parseInt(widths.get(SwtConstants.CCY_ACC_MAINT_PERIOD_LOG_IP_ADDRESS_TAGNAME)), false,
  							true, hiddenColumnsMap.get(SwtConstants.CCY_ACC_MAINT_PERIOD_LOG_IP_ADDRESS_TAGNAME)));
  					
  					tmpColumnInfo.setHeaderTooltip(SwtUtil.getMessage(SwtConstants.CCY_ACC_MAINT_PERIOD_LOG_IP_ADDRESS_HEADING_TOOLTIP, request));
  					lstColumns.add(tmpColumnInfo);
  				}
  				// End Date column
  				if (order.equals(SwtConstants.CCY_ACC_MAINT_PERIOD_LOG_REFERENCE_TAGNAME)) {
  					tmpColumnInfo = (new ColumnInfo(SwtUtil.getMessage(SwtConstants.CCY_ACC_MAINT_PERIOD_LOG_REFERENCE_HEADING, request),
  							SwtConstants.CCY_ACC_MAINT_PERIOD_LOG_REFERENCE_TAGNAME, SwtConstants.COLUMN_TYPE_STRING, 4,
  							Integer.parseInt(widths.get(SwtConstants.CCY_ACC_MAINT_PERIOD_LOG_REFERENCE_TAGNAME)), false,
  							true, hiddenColumnsMap.get(SwtConstants.CCY_ACC_MAINT_PERIOD_LOG_REFERENCE_TAGNAME)));
  					
  					tmpColumnInfo.setHeaderTooltip(SwtUtil.getMessage(SwtConstants.CCY_ACC_MAINT_PERIODv_REFERENCE_HEADING_TOOLTIP, request));
  					lstColumns.add(tmpColumnInfo);
  				}
  				// Minimum Reserve column
  				if (order.equals(SwtConstants.CCY_ACC_MAINT_PERIOD_LOG_ACTION_TAGNAME)) {
  					tmpColumnInfo = (new ColumnInfo(SwtUtil.getMessage(SwtConstants.CCY_ACC_MAINT_PERIOD_LOG_ACTION_HEADING, request),
  							SwtConstants.CCY_ACC_MAINT_PERIOD_LOG_ACTION_TAGNAME, SwtConstants.COLUMN_TYPE_STRING, 5,
  							Integer.parseInt(widths.get(SwtConstants.CCY_ACC_MAINT_PERIOD_LOG_ACTION_TAGNAME)), false,
  							true, hiddenColumnsMap.get(SwtConstants.CCY_ACC_MAINT_PERIOD_LOG_ACTION_TAGNAME)));
  					
  					tmpColumnInfo.setHeaderTooltip(SwtUtil.getMessage(SwtConstants.CCY_ACC_MAINT_PERIODv_ACTION_HEADING_TOOLTIP, request));
  					lstColumns.add(tmpColumnInfo);
  				}

  			}

  		} catch (Exception ex) {
  			// log error message
  			log.error(this.getClass().getName()
  					+ " - Exception Catched in [getLogGridColumns] method : - " + ex.getMessage());

  			throw SwtErrorHandler.getInstance().handleException(ex, "getLogGridColumns",
  					this.getClass());

  		} finally {
  			// Nullify Objects
  			orders = null;
  			columnOrderProp = null;
  			columnOrderItr = null;
  			widths = null;
  			columnWidthProperty = null;
  			// log debug message
  			log.debug(this.getClass().getName() + " - [ getLogGridColumns ] - Exit");
  		}
  		// return XML columns
  		return lstColumns;
  	}
	
	/**
	 * This method is used to load the pre-advice input screen and it will be
	 * invoked while changing entity on screen to get the corresponding details.
	 * 
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return
	 * @throws SwtException
	 */
	public String displayLog()
			throws SwtException {
HttpServletRequest request = ServletActionContext.getRequest();
HttpServletResponse response = ServletActionContext.getResponse();

		/* Local variable declaration */
		// To host for application
		String hostId = null;
		String fromDateAsString= null;
		String toDateAsString= null;
		// To hold the fromDate from screen
		Date fromDate = null;
		// To hold the toDate from screen
		Date toDate = null;
		String dateFormat = null;
		// To hold the role for current user
		String roleId = null;
		// to check if the entity has been changed
		ResponseHandler responseHandler = null;
		SwtResponseConstructor responseConstructor = null;
		SwtXMLWriter xmlWriter = null;
		String width = null;
		String columnOrder = null;
		String hiddenColumns = null;
		SystemFormats sysFormat=null;
		String time = null;
		String reference = null;
		Collection<MaintenanceLogView> listLogRecords = new ArrayList<MaintenanceLogView>();
		try {
			log.debug(this.getClass().getName() + " - [displayLog()] - Entry");

			sysFormat = SwtUtil.getCurrentSystemFormats(request.getSession());
			dateFormat= SwtUtil.getCurrentDateFormat(request.getSession());
			// to get the host id for application
			hostId = CacheManager.getInstance().getHostId();

			// Get role id associated with the logged-in user
			roleId = ((CommonDataManager) request.getSession().getAttribute(SwtConstants.CDM_BEAN)).getUser()
					.getRoleId();
			
			fromDateAsString= request.getParameter("fromDate") ;
			toDateAsString= request.getParameter("toDate") ;
			
			if (SwtUtil.isEmptyOrNull(fromDateAsString)) {
				fromDate= SwtUtil.getSystemDatewithoutTime();
				fromDateAsString= SwtUtil.formatDate( SwtUtil.getSystemDatewithoutTime(), SwtUtil.getCurrentSystemFormats(
						request.getSession()).getDateFormatValue());
			}else {
				
				fromDate=  new SimpleDateFormat(SwtUtil.getCurrentSystemFormats(
						request.getSession()).getDateFormatValue()).parse(fromDateAsString); 
			}
			
			if (SwtUtil.isEmptyOrNull(toDateAsString)) {
				toDate= SwtUtil.getSystemDatewithoutTime();
				toDateAsString= SwtUtil.formatDate( SwtUtil.getSystemDatewithoutTime(), SwtUtil.getCurrentSystemFormats(
						request.getSession()).getDateFormatValue());
			}else {
				  				
				toDate=  new SimpleDateFormat(SwtUtil.getCurrentSystemFormats(
						request.getSession()).getDateFormatValue()).parse(toDateAsString ); 	
			}
			
			reference= request.getParameter("reference") ;

			//get the list of records saved in  Account Currency Maintenance Period table
			listLogRecords=acctCcyPeriodMaintenanceManager.getMaintenanceLogList(fromDate,toDate, reference);

			responseHandler = new ResponseHandler();
			responseConstructor = new SwtResponseConstructor();
			xmlWriter = responseConstructor.getXMLWriter();

			xmlWriter.startElement(SwtConstants.CCY_ACC_MAINT_PERIOD);

			responseConstructor.formRequestReply(Boolean.parseBoolean(SwtConstants.STR_TRUE),
					SwtConstants.DATA_FETCH_OK);
			// forms singleton node
			xmlWriter.startElement(SwtConstants.SINGLETONS);
			responseConstructor.createElement("fromDate",fromDateAsString );
			responseConstructor.createElement("toDate",toDateAsString );
			responseConstructor.createElement("dateFormat",
					dateFormat);
			responseConstructor.createElement(SwtConstants.CURRENCYPATTERN,
					SwtUtil.getCurrentSystemFormats(request.getSession()).getCurrencyFormat());
			xmlWriter.endElement(SwtConstants.SINGLETONS); 

			
			/******* acctCcyMaintPeriodLogGrid ******/
			responseConstructor.formGridStart("acctCcyMaintPeriodLogGrid");
			responseConstructor.formColumn(getLogGridColumns(width, columnOrder, hiddenColumns, request));
			// form rows (records)
			responseConstructor.formRowsStart(listLogRecords.size());
			for (Iterator<MaintenanceLogView> it = listLogRecords.iterator(); it.hasNext();) {
				// Obtain rules definition tag from iterator
				MaintenanceLogView acctCcyLog = (MaintenanceLogView) it.next();
				responseConstructor.formRowStart();
				
				time = 	(acctCcyLog.getId().getLogDate()).toString().split("\\s+")[1];			
								 
								 
				responseConstructor.createRowElement(SwtConstants.CCY_ACC_MAINT_PERIOD_LOG_DATE_TAGNAME , 
						(acctCcyLog.getId().getLogDate() == null || acctCcyLog.getId().getLogDate().equals("")) ? "" :  SwtUtil.formatDate(acctCcyLog.getId().getLogDate(), dateFormat ));
				responseConstructor.createRowElement(SwtConstants.CCY_ACC_MAINT_PERIOD_LOG_TIME_TAGNAME, 
						time.substring(0, time.length() - 2));
				responseConstructor.createRowElement(SwtConstants.CCY_ACC_MAINT_PERIOD_LOG_USER_TAGNAME,acctCcyLog.getId().getUserId());
				responseConstructor.createRowElement(SwtConstants.CCY_ACC_MAINT_PERIOD_LOG_IP_ADDRESS_TAGNAME,acctCcyLog.getId().getIpAddress());
				responseConstructor.createRowElement(SwtConstants.CCY_ACC_MAINT_PERIOD_LOG_REFERENCE_TAGNAME, (acctCcyLog.getId().getReference() ));
				responseConstructor.createRowElement(SwtConstants.CCY_ACC_MAINT_PERIOD_LOG_ACTION_TAGNAME, getActionDesc(acctCcyLog.getId().getAction()));
				responseConstructor.formRowEnd();
			}
	
			responseConstructor.formRowsEnd();
			responseConstructor.formGridEnd();
			
			xmlWriter.endElement(SwtConstants.CCY_ACC_MAINT_PERIOD);
			request.setAttribute("data", xmlWriter.getData());

			// set the method name,last ref time and access level on request
			// attribute which are used in front end
			request.setAttribute("methodName", "success");
			log.debug(this.getClass().getName() + " - [displayLog()] - Exit");
			return ("data");
		} catch (SwtException swtexp) {
			log
					.error("SwtException Catch in AcctCcyPeriodMaintenanceAction.'displayLog' method : "
							+ swtexp.getMessage());
			SwtUtil.logException(swtexp, request, "");
			return ("fail");
		} catch (Exception exp) {
			log
					.error("Exception Catch in AcctCcyPeriodMaintenanceAction.'displayLog' method : "
							+ exp.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "displayLog", AcctCcyPeriodMaintenanceAction.class), request, "");
			return ("fail");
		} finally {
			/* null the objects created already. */
			responseConstructor = null;
			xmlWriter = null;
			hostId = null;
			fromDateAsString = null;
			toDateAsString = null;
            toDate = null;
            fromDate = null;
            dateFormat = null;
            sysFormat = null;
			roleId = null;
			time = null;
			reference = null;
			listLogRecords = null;
		}
	}
	
	private String  getActionDesc(String code) {
	    String desc = null;

	    switch (code) {
	      case "I":
	    	  desc = "Added";
	        break;
	      case "U":
	    	  desc = "Changed";
	        break;
	      default:
	        break;
	    }

	    return desc;
	  }
	
	
	private String  getActionCode(String desc) {
	    String code = null;

	    switch (desc) {
	      case "Added":
	    	  code = "I";
	        break;
	      case "Changed":
	    	  code = "U";
	        break;
	      default:
	        break;
	    }

	    return code;
	  }
	
	
	
		public String displayViewLogScreen() throws SwtException {
HttpServletRequest request = ServletActionContext.getRequest();
HttpServletResponse response = ServletActionContext.getResponse();

			request.setAttribute("params", request.getParameter("params"));
			return ("viewLogScreen");
		}
	
		
		/**
		 * This method is used to load the pre-advice input screen and it will be
		 * invoked while changing entity on screen to get the corresponding details.
		 * 
		 * @param mapping
		 * @param form
		 * @param request
		 * @param response
		 * @return
		 * @throws SwtException
		 */
		public String displayViewLog()
				throws SwtException {
HttpServletRequest request = ServletActionContext.getRequest();
HttpServletResponse response = ServletActionContext.getResponse();

			/* Local variable declaration */
			// To host for application
			String hostId = null;
			String logDate = null;
			String userId = null;
			String ipAddress = null;
			String reference = null;
			String action = null;			
			// To hold the role for current user
			String roleId = null;
			// to check if the entity has been changed
			ResponseHandler responseHandler = null;
			SwtResponseConstructor responseConstructor = null;
			SwtXMLWriter xmlWriter = null;
			String width = null;
			String columnOrder = null;
			String hiddenColumns = null;
			SystemFormats sysFormat=null;
			String dateFormat=null;
			String dateF=null;
			String logTime = null;
			String ccyCode = null;
			Collection<MaintenanceLog> listViewLogRecords = new ArrayList<MaintenanceLog>();
			try {
				log.debug(this.getClass().getName() + " - [displayViewLog()] - Entry");

				dateFormat= SwtUtil.getCurrentDateFormat(request.getSession());//.toUpperCase()+" HH24:MI:SS";
				
				// to get the host id for application
				hostId = CacheManager.getInstance().getHostId();

				// Get role id associated with the logged-in user
				roleId = ((CommonDataManager) request.getSession().getAttribute(SwtConstants.CDM_BEAN)).getUser()
						.getRoleId();
				
				userId= request.getParameter("userId") ;
				ipAddress= request.getParameter("ipAddress") ;
				reference= request.getParameter("reference") ;
				action = getActionCode(request.getParameter("action")) ; 
				logDate=request.getParameter("logDate");
				logTime=request.getParameter("logTime");
				ccyCode= request.getParameter("ccyCode");
//				StringBuilder finalDate = new StringBuilder();
//				finalDate.append(formatDate(request.getParameter("logDate"),dateFormat,"yyyy-MM-dd")).append(" ").append(logTime);
//				dateF= finalDate.toString();
				
//				SwtUtil.parseDate(request.getParameter("logDate")+" "+logTime, dateFormat +" HH:mm:ss");
				
				//get the list of records saved in  Account Currency Maintenance Period table
                listViewLogRecords=acctCcyPeriodMaintenanceManager.getViewLogDetails(hostId, userId, SwtUtil.parseDate(request.getParameter("logDate")+" "+logTime, dateFormat +" HH:mm:ss")/*dateF*/, ipAddress, "AccountCurrencyPeriodMaintenance",reference, action);
				responseHandler = new ResponseHandler();
				responseConstructor = new SwtResponseConstructor();
				xmlWriter = responseConstructor.getXMLWriter();

				xmlWriter.startElement(SwtConstants.CCY_ACC_MAINT_PERIOD);

				responseConstructor.formRequestReply(Boolean.parseBoolean(SwtConstants.STR_TRUE),
						SwtConstants.DATA_FETCH_OK); 

				
				/******* acctCcyMaintPeriodLogGrid ******/
				responseConstructor.formGridStart("acctCcyMaintPeriodViewLogGrid");
				responseConstructor.formColumn(getViewLogGridColumns(width, columnOrder, hiddenColumns, request));
				// form rows (records)
				responseConstructor.formRowsStart(listViewLogRecords.size());
				for (Iterator<MaintenanceLog> it = listViewLogRecords.iterator(); it.hasNext();) {
					// Obtain rules definition tag from iterator
					MaintenanceLog acctCcyViewLog = (MaintenanceLog) it.next();
					responseConstructor.formRowStart();
					
					responseConstructor.createRowElement(SwtConstants.CCY_ACC_MAINT_PERIOD_FIELD_TAGNAME,acctCcyViewLog.getColumnName());
					responseConstructor.createRowElement(SwtConstants.CCY_ACC_MAINT_PERIOD_CHANGED_FROM_TAGNAME, SwtUtil.isEmptyOrNull(acctCcyViewLog.getOldValue()) ? "" : getColFormattedValue(acctCcyViewLog.getColumnName(),acctCcyViewLog.getOldValue(),dateFormat, ccyCode));
					responseConstructor.createRowElement(SwtConstants.CCY_ACC_MAINT_PERIOD_CHANGED_TO_USER_TAGNAME,  SwtUtil.isEmptyOrNull(acctCcyViewLog.getNewValue()) ? "" : getColFormattedValue(acctCcyViewLog.getColumnName(),acctCcyViewLog.getNewValue(),dateFormat, ccyCode));
					
					responseConstructor.formRowEnd();
				}
		
				responseConstructor.formRowsEnd();
				responseConstructor.formGridEnd();
				
				xmlWriter.endElement(SwtConstants.CCY_ACC_MAINT_PERIOD);
				request.setAttribute("data", xmlWriter.getData());

				// set the method name,last ref time and access level on request
				// attribute which are used in front end
				request.setAttribute("methodName", "success");
				log.debug(this.getClass().getName() + " - [displayViewLog()] - Exit");
				return ("data");
			} catch (SwtException swtexp) {
				log
						.error("SwtException Catch in AcctCcyPeriodMaintenanceAction.'displayViewLog' method : "
								+ swtexp.getMessage());
				SwtUtil.logException(swtexp, request, "");
				return ("fail");
			} catch (Exception exp) {
				log
						.error("Exception Catch in AcctCcyPeriodMaintenanceAction.'displadisplayViewLogyLog' method : "
								+ exp.getMessage());
				SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
						exp, "displayViewLog", AcctCcyPeriodMaintenanceAction.class), request, "");
				return ("fail");
			} finally {
				/* null the objects created already. */
				responseHandler = null;
				responseConstructor = null;
				xmlWriter = null;				
				hostId = null;
				roleId = null;
				logDate = null;
				userId = null;
				ipAddress = null;
				reference = null;
				action = null;
				dateFormat = null;
				dateF = null;
				logTime = null;
				listViewLogRecords = null;
			}
		}
		
		
		private String formatDate (String date, String initDateFormat, String endDateFormat) throws ParseException {

		    Date initDate = null;
			try {
				initDate = new SimpleDateFormat(initDateFormat).parse(date);
			} catch (java.text.ParseException e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
			}
		    SimpleDateFormat formatter = new SimpleDateFormat(endDateFormat);
		    String parsedDate = formatter.format(initDate);

		    return parsedDate;
		}
		
		
		
		private List<ColumnInfo> getViewLogGridColumns(String width, String columnOrder, String hiddenColumns,
	  			HttpServletRequest request) throws SwtException {
	  		// Array list to hold column order
	  		ArrayList<String> orders = null;
	  		// String array variable to hold column order property
	  		String[] columnOrderProp = null;
	  		// Iterator variable to hold column order
	  		Iterator<String> columnOrderItr = null;
	  		// Hash map to hold column width
	  		LinkedHashMap<String, String> widths = null;
	  		// String array variable to hold width property
	  		String[] columnWidthProperty = null;
	  		// List for column info
	  		List<ColumnInfo> lstColumns = null;
	  		/* Hash map to hold column hidden_Columns */
	  		HashMap<String, Boolean> hiddenColumnsMap = new LinkedHashMap<String, Boolean>();
	  		/* Array list to hold hidden Column array */
	  		ArrayList<String> lstHiddenColunms = null;
	  		/* String array variable to hold hidden columns property */
	  		String[] hiddenColumnsProp = null;
	  		try {
	  			// log debug message
	  			log.debug(this.getClass().getName() + " - [ getViewLogGridColumns ] - Entry");
	  			// Condition to check width is null

	  			if (SwtUtil.isEmptyOrNull(width)) {
	  				// default width for columns
					width =  SwtConstants.CCY_ACC_MAINT_PERIOD_FIELD_TAGNAME + "=200" + ","
							+ SwtConstants.CCY_ACC_MAINT_PERIOD_CHANGED_FROM_TAGNAME + "=280" + ","
							+ SwtConstants.CCY_ACC_MAINT_PERIOD_CHANGED_TO_USER_TAGNAME + "=280" ;
	   			}

	  			// Obtain width for each column
	  			columnWidthProperty = width.split(",");

	  			// Loop to insert each column in hash map
	  			widths = new LinkedHashMap<String, String>();
	  			for (int i = 0; i < columnWidthProperty.length; i++) {
	  				// Condition to check index of = is -1
	  				if (columnWidthProperty[i].indexOf("=") != -1) {
	  					String[] propval = columnWidthProperty[i].split("=");
	  					widths.put(propval[0], propval[1]);
	  				}
	  			}

	  			// Condition to check column order is null or empty
	  			if (SwtUtil.isEmptyOrNull(columnOrder)) {
	  				// Default values for column order
					columnOrder = SwtConstants.CCY_ACC_MAINT_PERIOD_FIELD_TAGNAME + ","
							+ SwtConstants.CCY_ACC_MAINT_PERIOD_CHANGED_FROM_TAGNAME + ","
							+ SwtConstants.CCY_ACC_MAINT_PERIOD_CHANGED_TO_USER_TAGNAME ;
	}
	  			orders = new ArrayList<String>();
	  			// Split the columns using , and save in string array
	  			columnOrderProp = columnOrder.split(",");

	  			// Loop to enter column order in array list
	  			for (int i = 0; i < columnOrderProp.length; i++) {
	  				// Adding the Column values to ArrayList
	  				orders.add(columnOrderProp[i]);
	  				hiddenColumnsMap.put(columnOrderProp[i], true);
	  			}

	  			/* Condition to check column hidden is null or empty */
	  			if (!SwtUtil.isEmptyOrNull(hiddenColumns)) {
	  				lstHiddenColunms = new ArrayList<String>();
	  				/* Split the hidden columns , and save in string array */
	  				hiddenColumnsProp = hiddenColumns.split(",");

	  				for (int i = 0; i < hiddenColumnsProp.length; i++) {
	  					/* Adding the Column values to ArrayList */
	  					lstHiddenColunms.add(hiddenColumnsProp[i]);
	  				}

	  				Iterator<String> listKeys = hiddenColumnsMap.keySet().iterator();
	  				for (int i = 0; i < hiddenColumnsMap.size(); i++) {
	  					String columnKey = listKeys.next();
	  					// boolean found = false;
	  					for (int j = 0; j < lstHiddenColunms.size(); j++) {
	  						if (columnKey.equals(lstHiddenColunms.get(j))) {
	  							hiddenColumnsMap.put(columnKey, false);
	  							break;
	  						}
	  					}
	  				}
	  			}

	  			columnOrderItr = orders.iterator();
	  			lstColumns = new ArrayList<ColumnInfo>();

	  			while (columnOrderItr.hasNext()) {
	  				String order = (String) columnOrderItr.next();
	 				ColumnInfo tmpColumnInfo = null;
	  				// Host Id column
	  				if (order.equals(SwtConstants.CCY_ACC_MAINT_PERIOD_FIELD_TAGNAME)) {
	  					tmpColumnInfo = (new ColumnInfo(SwtUtil.getMessage(SwtConstants.CCY_ACC_MAINT_PERIOD_FIELD__HEADING, request),
	  							SwtConstants.CCY_ACC_MAINT_PERIOD_FIELD_TAGNAME, SwtConstants.COLUMN_TYPE_STRING, 0,
	  							Integer.parseInt(widths.get(SwtConstants.CCY_ACC_MAINT_PERIOD_FIELD_TAGNAME)), false,
	  							true, hiddenColumnsMap.get(SwtConstants.CCY_ACC_MAINT_PERIOD_FIELD_TAGNAME)));
	  					
	  					tmpColumnInfo.setHeaderTooltip(SwtUtil.getMessage(SwtConstants.CCY_ACC_MAINT_PERIOD_FIELD_HEADING_TOOLTIP, request));
	  					lstColumns.add(tmpColumnInfo);
	  				}
	  				// Entity Id column
	  				if (order.equals(SwtConstants.CCY_ACC_MAINT_PERIOD_CHANGED_FROM_TAGNAME)) {
	  					tmpColumnInfo = (new ColumnInfo(SwtUtil.getMessage(SwtConstants.CCY_ACC_MAINT_PERIOD_CHANGED_FROM_HEADING, request),
	  							SwtConstants.CCY_ACC_MAINT_PERIOD_CHANGED_FROM_TAGNAME, SwtConstants.COLUMN_TYPE_STRING, 1,
	  							Integer.parseInt(widths.get(SwtConstants.CCY_ACC_MAINT_PERIOD_CHANGED_FROM_TAGNAME)), false,
	  							true, hiddenColumnsMap.get(SwtConstants.CCY_ACC_MAINT_PERIOD_CHANGED_FROM_TAGNAME)));
	  					
	  					tmpColumnInfo.setHeaderTooltip(SwtUtil.getMessage(SwtConstants.CCY_ACC_MAINT_PERIOD_CHANGED_FROM_HEADING_TOOLTIP, request));
	  					lstColumns.add(tmpColumnInfo);
	  				}
	  				// Account Id column
	  				if (order.equals(SwtConstants.CCY_ACC_MAINT_PERIOD_CHANGED_TO_USER_TAGNAME)) {
	  					tmpColumnInfo = (new ColumnInfo(SwtUtil.getMessage(SwtConstants.CCY_ACC_MAINT_PERIOD_CHANGED_TO_HEADING, request),
	  							SwtConstants.CCY_ACC_MAINT_PERIOD_CHANGED_TO_USER_TAGNAME, SwtConstants.COLUMN_TYPE_STRING, 2,
	  							Integer.parseInt(widths.get(SwtConstants.CCY_ACC_MAINT_PERIOD_CHANGED_TO_USER_TAGNAME)), false,
	  							true, hiddenColumnsMap.get(SwtConstants.CCY_ACC_MAINT_PERIOD_CHANGED_TO_USER_TAGNAME)));
	  					
	  					tmpColumnInfo.setHeaderTooltip(SwtUtil.getMessage(SwtConstants.CCY_ACC_MAINT_PERIOD_CHANGED_TO_HEADING_TOOLTIP, request));
	  					lstColumns.add(tmpColumnInfo);
	  				}


	  			}

	  		} catch (Exception ex) {
	  			// log error message
	  			log.error(this.getClass().getName()
	  					+ " - Exception Catched in [getViewLogGridColumns] method : - " + ex.getMessage());

	  			throw SwtErrorHandler.getInstance().handleException(ex, "getViewLogGridColumns",
	  					this.getClass());

	  		} finally {
	  			// Nullify Objects
	  			orders = null;
	  			columnOrderProp = null;
	  			columnOrderItr = null;
	  			widths = null;
	  			columnWidthProperty = null;
	  			// log debug message
	  			log.debug(this.getClass().getName() + " - [ getViewLogGridColumns ] - Exit");
	  		}
	  		// return XML columns
	  		return lstColumns;
	  	}

		
		//Mantis 6135
		private String getColFormattedValue(String column, String value, String dateFormat, String ccyCode) {
			String formattedVal = null;
			if ("Target Avg Balance".equalsIgnoreCase(column) || "Minimum Target Balance".equalsIgnoreCase(column)
					|| "Minimum Reserve".equalsIgnoreCase(column) || "Tier".equalsIgnoreCase(column)
					|| "Fill Days".equalsIgnoreCase(column) || "Fill Balance".equalsIgnoreCase(column)) {
				try {
					formattedVal = SwtUtil.formatCurrencyWithoutDecimals(ccyCode, Double.parseDouble(value));
				} catch (NumberFormatException e) {
					log.error("NumberFormatException caught in " + this.getClass().getName()
							+ " - [getColFormattedValue]. Cause: " + e.getMessage());
				} catch (SwtException exp) {
					log.error(this.getClass().getName()
							+ " - SwtException Catched in [getColFormattedValue] method : - " + exp.getMessage());
				}
			} else if ("Start Date".equalsIgnoreCase(column) || "End Date".equalsIgnoreCase(column)) {
				try {
					Date initDate = new SimpleDateFormat("yyyyMMdd").parse(value);
					formattedVal = SwtUtil.formatDate(initDate, dateFormat);
				} catch (ParseException e) {
					log.error("ParseException caught in " + this.getClass().getName()
							+ " - [getColFormattedValue]. Cause: " + e.getMessage());
				}
			} else {
				formattedVal = value;
			}

			return formattedVal;
		}
	
	

}
