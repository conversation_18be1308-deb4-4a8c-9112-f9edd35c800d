package org.swallow.maintenance.web;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;


import org.swallow.control.model.FacilityAccess;
import org.swallow.control.service.MessageInternalManager;
import org.swallow.control.service.RoleManager;
import org.swallow.dao.LogonDAO;
import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.maintenance.authorization.MaintenanceAuthUtils;
import org.swallow.maintenance.model.MaintenanceEvent;
import org.swallow.maintenance.model.MaintenanceEventForm;
import org.swallow.maintenance.model.MaintenanceLogAuth;
import org.swallow.maintenance.model.MaintenanceLogViewAuth;
import org.swallow.maintenance.service.MaintenanceEventMaintenanceManager;
import org.swallow.model.MenuItem;
import org.swallow.pcm.maintenance.model.core.ColumnInfo;
import org.swallow.pcm.maintenance.model.core.OptionInfo;
import org.swallow.pcm.maintenance.model.core.SelectInfo;
import org.swallow.pcm.maintenance.web.ResponseHandler;
import org.swallow.util.pcm.PCMConstant;
import org.swallow.util.xml.SwtResponseConstructor;
import org.swallow.util.xml.SwtXMLWriter;
import org.swallow.util.CacheManager;
import org.swallow.util.CommonDataManager;
import org.swallow.util.SwtConstants;
import org.swallow.util.SwtUtil;
import org.swallow.util.SystemFormats;

import org.swallow.util.LabelValueBean;
import org.apache.struts2.convention.annotation.Result;
import org.apache.struts2.convention.annotation.AllowedMethods;
import org.apache.struts2.convention.annotation.Action;
import org.apache.struts2.ServletActionContext;
import org.swallow.util.struts.CustomActionSupport;

@Action(value = "/maintenanceEvent", results = {
	@Result(name = "fail", location = "/error.jsp"),
	@Result(name = "maintenanceeventAngular", location = "/jsp/maintenance/maintenanceeventsummary.jsp"),
	@Result(name = "maintenanceeventdetailsAngular", location = "/jsp/maintenance/maintenanceeventdetails.jsp"),
	@Result(name = "data", location = "/jsp/data.jsp"),
	@Result(name = "statechange", location = "/jsp/flexstatechange.jsp"),
})

@AllowedMethods ({"displayMaintenanceEvent" ,"openSubEventMaintScreen" ,"subDisplay" ,"displayMaintenanceEventList" ,"updateMaintenanceEventStatus" ,"deleteMaintenanceEvent" ,"checkExistingDataMethod" ,"maintenanceEventDetails" ,"saveColumnWidth" ,"saveColumnOrder" ,"displayLog" ,"displayViewLogScreen" ,"displayViewLog" ,"checkIfMaintenenanceEventExist" })
public class MaintenanceEventMaintenanceAction extends CustomActionSupport {
public String execute() throws Exception {
    HttpServletRequest request = ServletActionContext.getRequest();

    // List of methods 
    String method = String.valueOf(request.getParameter("method"));

    switch (method) {
        case "displayMaintenanceEvent":
            return displayMaintenanceEvent();
        case "openSubEventMaintScreen":
            return openSubEventMaintScreen();
        case "subDisplay":
            return subDisplay();
        case "displayMaintenanceEventList":
            return displayMaintenanceEventList();
        case "updateMaintenanceEventStatus":
            return updateMaintenanceEventStatus();
        case "deleteMaintenanceEvent":
            return deleteMaintenanceEvent();
        case "checkExistingDataMethod":
            return checkExistingDataMethod();
        case "unspecified":
            return unspecified();
        case "maintenanceEventDetails":
            return maintenanceEventDetails();
        case "saveColumnWidth":
            return saveColumnWidth();
        case "saveColumnOrder":
            return saveColumnOrder();
        case "displayLog":
            return displayLog();
        case "displayViewLogScreen":
            return displayViewLogScreen();
        case "displayViewLog":
            return displayViewLog();
        case "checkIfMaintenenanceEventExist":
            return checkIfMaintenenanceEventExist();
        default:
            break;
    }

    return unspecified();
}




	private final Log log = LogFactory
			.getLog(MaintenanceEventMaintenanceAction.class);
	private MaintenanceEventMaintenanceManager maintenanceEventMaintenanceManager;

	public void setMaintenanceEventMaintenanceManager(
			MaintenanceEventMaintenanceManager maintenanceEventMaintenanceManager) {
		this.maintenanceEventMaintenanceManager = maintenanceEventMaintenanceManager;
	}

	public String displayMaintenanceEvent() throws SwtException {
HttpServletRequest request = ServletActionContext.getRequest();

		MaintenanceEvent maintenanceEvent = null;
		String screen = null;
		String maintEventId = null;
		try {
			log.debug(this.getClass().getName()
					+ " method [displayMaintenanceEvent] - Enter ");
			request.setAttribute("screenName",
					request.getParameter("screenName"));
			maintEventId = request.getParameter("maintEventId");
			screen = request.getParameter("screenName");
			if (SwtUtil.isEmptyOrNull(request.getParameter("loadFlex"))) {
				log.debug(this.getClass().getName()
						+ "-[displayMaintenanceEvent] - Exit loading SWF");
				request.setAttribute("maintEventId", maintEventId);
				return ("maintenanceeventaddflex");
			}
			if (screen.equals("addScreen")) {
				maintenanceEvent = new MaintenanceEvent();
			} else {
				maintenanceEvent = maintenanceEventMaintenanceManager
						.getMaintenanceEvent(maintEventId);
			}
			request.setAttribute("maintenanceEvent", maintenanceEvent);
			request.setAttribute("menuAccessId",
					request.getParameter("menuAccessId"));
			request.setAttribute("reply_status_ok", SwtConstants.STR_TRUE);
			request.setAttribute("reply_message", "Data fetch OK");
			log.debug(this.getClass().getName()
					+ " method [displayMaintenanceEvent] - Exit ");
		} catch (SwtException e) {
			log.error(this.getClass().getName()
					+ " - [displayMaintenanceEvent] - Exception -"
					+ e.getStackTrace()[0].getClassName() + "."
					+ e.getStackTrace()[0].getMethodName() + ":"
					+ e.getStackTrace()[0].getLineNumber() + " "
					+ e.getMessage());
			request.setAttribute("reply_status_ok", SwtConstants.STR_FALSE);
			request.setAttribute("reply_message", e.getMessage());
			request.setAttribute(
					"reply_location",
					e.getStackTrace()[0].getClassName() + "."
							+ e.getStackTrace()[0].getMethodName() + ":"
							+ e.getStackTrace()[0].getLineNumber());
			return ("dataerror");
		}
		return ("maintenanceeventaddflexdata");
	}
	
	
	public String openSubEventMaintScreen() throws SwtException {
HttpServletRequest request = ServletActionContext.getRequest();
HttpServletResponse response = ServletActionContext.getResponse();

		request.setAttribute("screenName", request.getParameter("screenName"));
		request.setAttribute("params", request.getParameter("allParams"));
		return ("subScreen");
	}
	
	

	public String subDisplay()
			throws SwtException {
HttpServletRequest request = ServletActionContext.getRequest();
HttpServletResponse response = ServletActionContext.getResponse();

		/* Local variable declaration */
		// To host for application
		String hostId = null;
		// To hold the current entity from screen
		String dateFormat = null;
		// To hold the role for current user
		String roleId = null;
		ResponseHandler responseHandler = null;
		SwtResponseConstructor responseConstructor = null;
		SwtXMLWriter xmlWriter = null;
		String amountFormat = null;
		SystemFormats sysFormat=null;
		String maintenanceEventId = null;
		
		try {
			log.debug(this.getClass().getName() + " - [subDisplay()] - Entry");

			sysFormat = SwtUtil.getCurrentSystemFormats(request.getSession());
			dateFormat= SwtUtil.getCurrentDateFormat(request.getSession());
			amountFormat=sysFormat.getCurrencyFormat();
			maintenanceEventId= request.getParameter("maintenanceEventId");		
			
			// to get the host id for application
			hostId = CacheManager.getInstance().getHostId();
			

			// Get role id associated with the logged-in user
			roleId = ((CommonDataManager) request.getSession().getAttribute(SwtConstants.CDM_BEAN)).getUser()
					.getRoleId();
						
			responseHandler = new ResponseHandler();
			responseConstructor = new SwtResponseConstructor();
			xmlWriter = responseConstructor.getXMLWriter();

			xmlWriter.startElement(SwtConstants.MAINTENANCE_EVENT_DETAILS_XML_TAG);

			responseConstructor.formRequestReply(Boolean.parseBoolean(SwtConstants.STR_TRUE),
					SwtConstants.DATA_FETCH_OK);
			// forms singleton node
			xmlWriter.startElement(SwtConstants.SINGLETONS);
			
			responseConstructor.createElement("maintenanceEventId",
					maintenanceEventId);
			responseConstructor.createElement("displayedDate", SwtUtil.formatDate(SwtUtil
					.getSystemDatewithoutTime(), SwtUtil.getCurrentSystemFormats(
					request.getSession()).getDateFormatValue()));
			responseConstructor.createElement("dateFormat",
					dateFormat);
			responseConstructor.createElement(SwtConstants.CURRENCYPATTERN,
					SwtUtil.getCurrentSystemFormats(request.getSession()).getCurrencyFormat());
			
			xmlWriter.endElement(SwtConstants.SINGLETONS); 
			
					
			
			xmlWriter.endElement(SwtConstants.MAINTENANCE_EVENT_DETAILS_XML_TAG);
			request.setAttribute("data", xmlWriter.getData());

			// set the method name,last ref time and access level on request
			// attribute which are used in front end
			request.setAttribute("methodName", "success");
			log.debug(this.getClass().getName() + " - [SubDisplay()] - Exit");
			return ("data");
		} catch (SwtException swtexp) {
			log
					.error("SwtException Catch in AcctCcyPeriodMaintenanceAction.'SubDisplay' method : "
							+ swtexp.getMessage());
			SwtUtil.logException(swtexp, request, "");
			return ("fail");
		} catch (Exception exp) {
			log
					.error("Exception Catch in AcctCcyPeriodMaintenanceAction.'SubDisplay' method : "
							+ exp.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "SubDisplay", MaintenanceEventMaintenanceAction.class), request, "");
			return ("fail");
		} finally {
			/* null the objects created already. */
			responseConstructor = null;
			xmlWriter=null;
			hostId = null;
			roleId = null;
			dateFormat = null;
			amountFormat = null;
			sysFormat = null;
		}
	}
	
	
	public static String getMenuItemFromFacilityId(String facilityId) {
		if(PCMConstant.AUTHORISATION_STOP_RULE_SCREEN_ID.equalsIgnoreCase(facilityId)) {
			return ""+SwtConstants.MENU_ITEM_STOPRULE_MAINTENANCE;
		}else if(PCMConstant.AUTHORISATION_STOP_ACCOUNT_GROUP_ID.equalsIgnoreCase(facilityId)) {
			return ""+SwtConstants.MENU_ITEM_PCM_ACCOUNT_GROUPS_MAINTENANCE;
		}else if(PCMConstant.AUTHORISATION_SPREAD_PROFILE_GROUP_ID.equalsIgnoreCase(facilityId)) {
			return ""+SwtConstants.MENU_ITEM_SPREAD_PROFILES_MAINTENANCE;
		}else 
			return null;
		
	}

	public String displayMaintenanceEventList() throws SwtException {
HttpServletRequest request = ServletActionContext.getRequest();
HttpServletResponse response = ServletActionContext.getResponse();

		log.debug(this.getClass().getName()
				+ " method [displayMaintenanceEventList] - Enter ");
		Collection<MaintenanceEvent> result = null;
		ResponseHandler responseHandler = null;
		SwtResponseConstructor responseConstructor = null;
		SwtXMLWriter xmlWriter = null;
		String width = null;
		String columnOrder = null;
		String hiddenColumns = null;
		String maintFacilityId= null;
		String recordId= null;
		String requestUser= null;
		String requestDate= null;
		String authUser= null;
		String authDate= null;
		String prevId= null;
		String nextId= null;
		String id= null;
		// To hold date format
		String dateFormat = null;
		
		String selectedUser;
		String selectedFacility;
		Date fromDate;
		Date toDate;
		String action;
		boolean isPendingChecked = true;
		boolean isAcceptedChecked = false;
		boolean isRejectedChecked = false;
		boolean isAllDates = true;
		ArrayList<FacilityAccess> accessList = null;
		String roleId = null;
		String currentUserId = null;
		HashMap<String, Integer> faciltiyAccessMap = new HashMap<String, Integer>();
		
//		private String maintFacilityId;
//		private Date requestDate;
//		private String requestUser;
//		private String authUser;
//		private String selectedUser;
//		private Date fromDate;
//		private Date toDate;
//		private Double prevId;
//		private Double nextId;
//		private String action;
//		private boolean isPendingChecked = true;
//		private boolean isAcceptedChecked = false;
//		private boolean isRejectedChecked = false;
//		private boolean isAllDates = true;
		
		MaintenanceEventForm eventForm = new MaintenanceEventForm();
		try {
			dateFormat= SwtUtil.getCurrentDateFormat(request.getSession());
			
			
			
			selectedUser = 	request.getParameter("selectedUser"); 
			if(SwtUtil.isEmptyOrNull(selectedUser)) {
				selectedUser = "All";
			}
			fromDate = SwtUtil.isEmptyOrNull(request.getParameter("fromDate")) ?null:SwtUtil.parseDate(request.getParameter("fromDate"),dateFormat) ;
			toDate = 	SwtUtil.isEmptyOrNull(request.getParameter("toDate")) ?null:SwtUtil.parseDate(request.getParameter("toDate"),dateFormat) ;
			isPendingChecked = "Y".equalsIgnoreCase(request.getParameter("isPendingChecked"));
			isAcceptedChecked = 	"Y".equalsIgnoreCase(request.getParameter("isAcceptedChecked"));
			isRejectedChecked = 	"Y".equalsIgnoreCase(request.getParameter("isRejectedChecked"));
			if(SwtUtil.isEmptyOrNull(request.getParameter("isAllDates"))) {
				isAllDates = true;
			}else {
				isAllDates = 	"Y".equalsIgnoreCase(request.getParameter("isAllDates"));
				
			}
			selectedFacility = 	request.getParameter("selectedFacility"); 
			if(SwtUtil.isEmptyOrNull(selectedFacility)) {
				selectedFacility = "All";
			}
			
			
			eventForm.setSelectedUser(selectedUser);
			eventForm.setFromDate(fromDate);
			eventForm.setToDate(toDate);
			eventForm.setPendingChecked(isPendingChecked);
			eventForm.setAcceptedChecked(isAcceptedChecked);
			eventForm.setRejectedChecked(isRejectedChecked);
			eventForm.setAllDates(isAllDates);
			eventForm.setMaintFacilityId(selectedFacility);
			
			result = maintenanceEventMaintenanceManager
					.getMaintenanceEventList(eventForm);
			request.setAttribute("menuAccessId",
					request.getParameter("menuAccessId"));
			
			
			
			RoleManager roleManager = (RoleManager) SwtUtil.getBean("roleManager");
			HashMap<String, String> facilitiesList = roleManager.getAllFacilitiesList();
			
			
			responseHandler = new ResponseHandler();
			responseConstructor = new SwtResponseConstructor();
			xmlWriter = responseConstructor.getXMLWriter();
			xmlWriter.startElement(SwtConstants.MAINTENANCE_EVENT);
			responseConstructor.formRequestReply(
					Boolean.parseBoolean(SwtConstants.STR_TRUE),
					SwtConstants.DATA_FETCH_OK);
			xmlWriter.startElement(SwtConstants.SINGLETONS);
			responseConstructor.createElement("maintFacilityId",
					maintFacilityId);
//			responseConstructor.createElement("recordId", recordId);
//			responseConstructor.createElement("requestUser", requestUser);
//			responseConstructor.createElement("requestDate", requestDate);
//			responseConstructor.createElement("authUser", authUser);
//			responseConstructor.createElement("authDate", authDate);
//			responseConstructor.createElement("prevId", prevId);
//			responseConstructor.createElement("nextId", nextId);
			if(fromDate == null)
				fromDate = SwtUtil.getSysParamDate();
			if(toDate == null)
				toDate = fromDate;
			responseConstructor.createElement("selectedUser", selectedUser);
			responseConstructor.createElement("selectedFacility", selectedFacility);
			
			responseConstructor.createElement("fromDate", SwtUtil.formatDate(fromDate, SwtUtil.getCurrentDateFormat(request.getSession())));;
			responseConstructor.createElement("toDate", SwtUtil.formatDate(toDate, SwtUtil.getCurrentDateFormat(request.getSession())));
			responseConstructor.createElement("isPendingChecked", isPendingChecked?"Y":"N");
			responseConstructor.createElement("isAcceptedChecked", isAcceptedChecked?"Y":"N");
			responseConstructor.createElement("isRejectedChecked", isRejectedChecked?"Y":"N");
			responseConstructor.createElement("isAllDates", isAllDates?"Y":"N");
			
//			responseConstructor.createElement(SwtConstants.CURRENCYPATTERN,
//					SwtUtil.getCurrentSystemFormats(request.getSession())
//							.getCurrencyFormat());
			responseConstructor.createElement("menuAccessId",
					request.getParameter("menuAccessId"));
			responseConstructor.createElement(SwtConstants.DATE_FORMAT,
					SwtUtil.getCurrentDateFormat(request.getSession()));
			xmlWriter.endElement(SwtConstants.SINGLETONS);
			ArrayList<SelectInfo> lstSelect = new ArrayList<SelectInfo>();
			ArrayList<OptionInfo> lstOptions = new ArrayList<OptionInfo>();
			lstOptions = new ArrayList<OptionInfo>();
			
			
			String hostId = CacheManager.getInstance().getHostId();
			MessageInternalManager messageInternalManager = (MessageInternalManager) (SwtUtil.getBean("messageInternalManager"));
			Collection collUser = messageInternalManager.getUserList(hostId);
			
			
			lstOptions = new ArrayList<OptionInfo>();
			LabelValueBean row = null;
			Iterator j = collUser.iterator();
			row = null;
//			lstOptions.add(new OptionInfo(SwtConstants.ALL_VALUE, SwtConstants.ALL_ENTITY_VALUE, false));
			while (j.hasNext()) {
				row = (LabelValueBean) j.next();
				lstOptions.add(new OptionInfo(row.getValue(), row.getLabel(), false));
			}
			lstSelect.add(new SelectInfo("userList", lstOptions));	
			
			lstOptions = new ArrayList<OptionInfo>();
			lstOptions.add(new OptionInfo("ALL", "ALL", false));
			for(Map.Entry<String, String> entry: facilitiesList.entrySet()) {
			    String key = entry.getKey();
			    String value = entry.getValue();
			    lstOptions.add(new OptionInfo(value, key, false));
			}
			lstSelect.add(new SelectInfo("facilityList", lstOptions));	
//			facilityList
			
			
			responseConstructor.formSelect(lstSelect);
			
			roleId = ((CommonDataManager) request.getSession()
					.getAttribute(SwtConstants.CDM_BEAN)).getUser()
					.getRoleId();	
			
			currentUserId = ((CommonDataManager) request.getSession()
					.getAttribute(SwtConstants.CDM_BEAN)).getUser().getId().getUserId();
			
			ArrayList<String> listOfFullAcessFacility =  new ArrayList<String>();
			accessList = (ArrayList<FacilityAccess>) roleManager.getFacilityAccessList(roleId);
			for (int i = 0; i < accessList.size(); i++) {
				FacilityAccess access = accessList.get(i);
				if("Y".equalsIgnoreCase(access.getAuthOthers())) {
					listOfFullAcessFacility.add(access.getId().getFacilityId());
				}
			}
			
			
			CommonDataManager cdm = (CommonDataManager) request.getSession()
					.getAttribute(SwtConstants.CDM_BEAN);
			
			responseConstructor.formGridStart("maintEventGrid");
			responseConstructor.formColumn(getGridColumns(width, columnOrder,
					hiddenColumns, request));
			responseConstructor.formRowsStart(result.size());
			for (Iterator<MaintenanceEvent> it = result.iterator(); it
					.hasNext();) {
				MaintenanceEvent element = (MaintenanceEvent) it.next();
				
//				if(!listOfFullAcessFacility.contains(element.getMaintFacilityId()) && !element.getRequestUser().equals(currentUserId) )
//					continue;
				


				if(faciltiyAccessMap.containsKey(element.getMaintFacilityId())) {
					if(faciltiyAccessMap.get(element.getMaintFacilityId())==2) 
						continue;
				}else {
					
					LogonDAO logonDAO = (LogonDAO)SwtUtil.getBean("logonDAO");
					MenuItem menuItem = logonDAO.getMenuItem(getMenuItemFromFacilityId(element.getMaintFacilityId())+"", cdm.getUser());

					if (menuItem != null) {
						int menuAccessId = SwtUtil.getHierarchicalAccessId(menuItem, request);
						faciltiyAccessMap.put(element.getMaintFacilityId(),menuAccessId);
						if(menuAccessId==2)
							continue;
					}else {
						continue;
					}
				}
				
				
				
				
				responseConstructor.formRowStart();
				responseConstructor.createRowElement(SwtConstants.MAINT_EVENT_MAINT_EVENT_ID_TAGNAME,
						element.getMaintEventId().toString());
				
				
//				responseConstructor.createRowElement(
//						SwtConstants.MAINT_EVENT_MAINT_FACILITY_ID_TAGNAME, element
//								.getMaintFacilityId());
				
				responseConstructor.createRowElement(
						SwtConstants.MAINT_EVENT_MAINT_FACILITY_ID_TAGNAME, facilitiesList.get(element
								.getMaintFacilityId()));
				
				responseConstructor.createRowElement(
						SwtConstants.MAINT_EVENT_MAINT_FACILITY_ID_VALUE_TAGNAME, element
								.getMaintFacilityId());
				
				
				responseConstructor.createRowElement(SwtConstants.MAINT_EVENT_RECORD_ID_TAGNAME,
						element.getRecordId());
				responseConstructor.createRowElement(SwtConstants.MAINT_EVENT_REQUEST_USER_TAGNAME,
						element.getRequestUser());
				responseConstructor.createRowElement(SwtConstants.MAINT_EVENT_REQUEST_DATE_TAGNAME,
						(element.getRequestDate() == null || element.getRequestDate().equals("")) ? "" :  SwtUtil.formatDate(element.getRequestDate(), dateFormat+ " HH:mm:ss" ));
				responseConstructor.createRowElement(SwtConstants.MAINT_EVENT_AUTH_USER_TAGNAME,
						element.getAuthUser());
				responseConstructor.createRowElement(SwtConstants.MAINT_EVENT_AUTH_DATE_TAGNAME,
						(element.getAuthDate() == null || element.getAuthDate().equals("")) ? "" :  SwtUtil.formatDate(element.getAuthDate(), dateFormat+ " HH:mm:ss" ));
				responseConstructor.createRowElement(SwtConstants.MAINT_EVENT_STATUS_TAGNAME, getStatusDesc(element.getStatus()));
				responseConstructor.createRowElement(SwtConstants.MAINT_EVENT_STATUS_VALUE_TAGNAME, element.getStatus());
				responseConstructor.createRowElement(SwtConstants.MAINT_EVENT_ACTION_TAGNAME, getActionDesc(element.getAction()));
				responseConstructor.createRowElement(SwtConstants.MAINT_EVENT_ACTION_VALUE_TAGNAME, element.getAction());
				responseConstructor.createRowElement(SwtConstants.MAINT_EVENT_PREV_ID_TAGNAME,
						element.getPrevId() != null?element.getPrevId().toString():"");
				responseConstructor.createRowElement(SwtConstants.MAINT_EVENT_NEXT_ID_TAGNAME,
						element.getNextId() != null?element.getNextId().toString():"");
				responseConstructor.formRowEnd();
			}
			responseConstructor.formRowsEnd();
			responseConstructor.formGridEnd();
			xmlWriter.endElement(SwtConstants.MAINTENANCE_EVENT);
			request.setAttribute("data", xmlWriter.getData());
			log.debug(this.getClass().getName()
					+ " method [displayMaintenanceEventList] - Exit ");
		} catch (SwtException e) {
			e.printStackTrace();
			log.error(this.getClass().getName()
					+ " - [displayMaintenanceEventList] - Exception -"
					+ e.getStackTrace()[0].getClassName() + "."
					+ e.getStackTrace()[0].getMethodName() + ":"
					+ e.getStackTrace()[0].getLineNumber() + " "
					+ e.getMessage());
			request.setAttribute("reply_status_ok", SwtConstants.STR_FALSE);
			request.setAttribute("reply_message", e.getMessage());
			request.setAttribute(
					"reply_location",
					e.getStackTrace()[0].getClassName() + "."
							+ e.getStackTrace()[0].getMethodName() + ":"
							+ e.getStackTrace()[0].getLineNumber());
			return ("dataerror");
		}
		return ("data");
	}

	public String updateMaintenanceEventStatus() throws SwtException {
HttpServletRequest request = ServletActionContext.getRequest();
HttpServletResponse response = ServletActionContext.getResponse();

		MaintenanceEvent maintenanceEvent = null;
		Long maintEventId = null;
		String action = null;
		String userId = null;
		
		try {
			log.debug(this.getClass().getName()
					+ " method [saveMaintenanceEvent] - Enter ");
			userId = SwtUtil.getCurrentUser(request.getSession()).getId().getUserId();
			if (!SwtUtil.isEmptyOrNull(request.getParameter("maintEventId"))
					&& !request.getParameter("maintEventId").equals("null")) {
				maintEventId = Long.parseLong(request.getParameter("maintEventId"));
			}
			if (!SwtUtil.isEmptyOrNull(request.getParameter("action"))
					&& !request.getParameter("action").equals("null")) {
				action = request.getParameter("action");
			}
			maintenanceEvent = new MaintenanceEvent();
			maintenanceEvent.setMaintEventId(maintEventId);
			maintenanceEvent.setAuthUser(userId);
			maintenanceEvent.setAuthDate(SwtUtil.getSysParamDate());
			maintenanceEvent.setStatus(action);
			
			maintenanceEventMaintenanceManager
					.updateMaintenanceEvent(maintenanceEvent);
			if("A".equalsIgnoreCase(action)) {
				MaintenanceAuthUtils.acceptMaintenanceEvent(""+maintEventId);
			}else if("R".equalsIgnoreCase(action)) {
				MaintenanceAuthUtils.rejectMaintenanceEvent(""+maintEventId);
			}
			log.debug(this.getClass().getName()
					+ " method [saveMaintenanceEvent] - Exit ");
			return sendSaveResponse();
		} catch (SwtException e) {
			log.error(this.getClass().getName()
					+ " - [saveMaintenanceEvent] - Exception -"
					+ e.getStackTrace()[0].getClassName() + "."
					+ e.getStackTrace()[0].getMethodName() + ":"
					+ e.getStackTrace()[0].getLineNumber() + " "
					+ e.getMessage());
			saveErrors(request, SwtUtil.logException(e, request, ""));
			if (e.getErrorCode().equals(
					"errors.DataIntegrityViolationExceptioninAdd")) {
			} else {
				log.error(this.getClass().getName()
						+ " - Exception Catched in [saveMaintenanceEvent] method swt : - "
						+ e.getMessage());
				saveErrors(request, SwtUtil.logException(e, request, ""));
			}
			request.setAttribute("reply_status_ok", SwtConstants.STR_FALSE);
			request.setAttribute("reply_message", e.getMessage());
			request.setAttribute(
					"reply_location",
					e.getStackTrace()[0].getClassName() + "."
							+ e.getStackTrace()[0].getMethodName() + ":"
							+ e.getStackTrace()[0].getLineNumber());
			return ("success");
		}
	}
	
	
	public String sendSaveResponse() throws SwtException {
		SwtResponseConstructor responseConstructor = null;
		SwtXMLWriter xmlWriter = null;
		String userId = null;
		String componentId = null;

		// debug message
		log.debug(this.getClass().getName()
					+ " - [ sendSaveResponse ] - Entry");
	
		HttpServletRequest request = ServletActionContext.getRequest();
		userId = SwtUtil.getCurrentUser(request.getSession()).getId().getUserId();
		responseConstructor = new SwtResponseConstructor();
	
		xmlWriter = responseConstructor.getXMLWriter();
		// Get component ID
		componentId = "MaintenanceEventDetails";	
		// Adding screen id and current user id as attributes
		xmlWriter.addAttribute(SwtConstants.SCREEN_ID, componentId);
		xmlWriter.addAttribute(SwtConstants.CURRUSER, userId);
		xmlWriter.startElement("MaintenanceEventDetails");
		xmlWriter.clearAttribute();
		responseConstructor.formRequestReply(Boolean.parseBoolean(SwtConstants.STR_TRUE), SwtConstants.DATA_FETCH_OK);
		xmlWriter.endElement("MaintenanceEventDetails");
			
		request.setAttribute("data", xmlWriter.getData());
		return ("data");
	}

	public String deleteMaintenanceEvent() throws SwtException {
HttpServletRequest request = ServletActionContext.getRequest();
HttpServletResponse response = ServletActionContext.getResponse();

		MaintenanceEvent maintenanceEvent = null;
		String maintEventId = null;
		try {
			log.debug(this.getClass().getName()
					+ " method [deleteMaintenanceEvent] - Enter ");
			maintEventId =  request.getParameter("maintEventId");
			maintenanceEvent = maintenanceEventMaintenanceManager
					.getMaintenanceEvent(maintEventId);
			maintenanceEventMaintenanceManager
					.deleteMaintenanceEvent(maintenanceEvent);
			request.setAttribute("maintenanceEvent", maintenanceEvent);
			request.setAttribute("menuAccessId",
					request.getParameter("menuAccessId"));
			request.setAttribute("reply_status_ok", SwtConstants.STR_TRUE);
			request.setAttribute("reply_message", "Data fetch OK");
			log.debug(this.getClass().getName()
					+ " method [deleteMaintenanceEvent] - Exit ");
		} catch (SwtException e) {
			log.error(this.getClass().getName()
					+ " - [deleteMaintenanceEvent] - Exception -"
					+ e.getStackTrace()[0].getClassName() + "."
					+ e.getStackTrace()[0].getMethodName() + ":"
					+ e.getStackTrace()[0].getLineNumber() + " "
					+ e.getMessage());
			request.setAttribute("reply_status_ok", SwtConstants.STR_FALSE);
			request.setAttribute("reply_message", e.getMessage());
			request.setAttribute(
					"reply_location",
					e.getStackTrace()[0].getClassName() + "."
							+ e.getStackTrace()[0].getMethodName() + ":"
							+ e.getStackTrace()[0].getLineNumber());
			return ("dataerror");
		}
		return ("maintenanceeventflexdata");
	}

	public String checkExistingDataMethod() throws SwtException {
HttpServletRequest request = ServletActionContext.getRequest();
HttpServletResponse response = ServletActionContext.getResponse();

		MaintenanceEvent maintenanceEvent = null;
		String maintEventId = null;
		String existingData = "N";
		try {
			log.debug(this.getClass().getName()
					+ "- [checkExistingDataMethod] - Enter");
			maintenanceEvent = maintenanceEventMaintenanceManager
					.getMaintenanceEvent(maintEventId);
			if (maintenanceEvent != null) {
				existingData = "Y";
			}
			response.getWriter().print(existingData);
			log.debug(this.getClass().getName()
					+ " - [checkExistingDataMethod] - " + "Exit");
			return null;
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [checkExistingDataMethod] method : - "
					+ exp.getMessage());
			SwtUtil.logException(
					SwtErrorHandler.getInstance().handleException(exp,
							"checkExistingDataMethod",
							MaintenanceEventMaintenanceAction.class), request,
					"");
			return ("fail");
		}
	}

	public String unspecified()
			throws SwtException {
HttpServletRequest request = ServletActionContext.getRequest();
HttpServletResponse response = ServletActionContext.getResponse();

		request.setAttribute("menuAccessId",
				request.getParameter("menuAccessId"));
		return ("maintenanceeventAngular");
	}
	
	
	public String maintenanceEventDetails()
			throws SwtException {
HttpServletRequest request = ServletActionContext.getRequest();
HttpServletResponse response = ServletActionContext.getResponse();

		request.setAttribute("menuAccessId",
				request.getParameter("menuAccessId"));
		return ("maintenanceeventdetailsAngular");
	}

	private void bindColumnOrderInRequest(HttpServletRequest request)
			throws SwtException {
	}

	private void bindColumnWidthInRequest(HttpServletRequest request)
			throws SwtException {
	}

	public String saveColumnWidth() throws SwtException {
HttpServletRequest request = ServletActionContext.getRequest();
HttpServletResponse response = ServletActionContext.getResponse();

		String width = null;
		try {
			if (!SwtUtil.isEmptyOrNull(width = request.getParameter("width"))) {
				SwtUtil.setPropertyValue(request,
						SwtUtil.getUserCurrentEntity(request.getSession()),
						SwtConstants.MAINT_EVENT_ID, "display", "column_width", width);
			} else {
				throw new Exception(
						"You must send 'width' and 'entityid' parameters");
			}
			request.setAttribute("reply_status_ok", "true");
			request.setAttribute("reply_message", "Column width saved ok");
			log.debug(this.getClass().getName()
					+ " - [saveColumnWidth] - Exit ");
		} catch (Exception e) {
			log.error(this.getClass().getName()
					+ " - [saveColumnWidth] - Error - " + e.getMessage());
			request.setAttribute("reply_status_ok", "false");
			request.setAttribute("reply_message", e.getMessage());
			request.setAttribute(
					"reply_location",
					e.getStackTrace()[0].getClassName() + "."
							+ e.getStackTrace()[0].getMethodName() + ":"
							+ e.getStackTrace()[0].getLineNumber());
			SwtUtil.logException(
					SwtErrorHandler.getInstance().handleException(e,
							"saveColumnWidth", MaintenanceEvent.class),
					request, "");
		}
		return ("statechange");
	}

	public String saveColumnOrder() throws SwtException {
HttpServletRequest request = ServletActionContext.getRequest();
HttpServletResponse response = ServletActionContext.getResponse();

		String columnOrder = null;
		try {
			log.debug(this.getClass().getName()
					+ " - [ saveColumnOrder ] - Entry ");
			columnOrder = request.getParameter("order");
			if (!SwtUtil.isEmptyOrNull(columnOrder)) {
				SwtUtil.setPropertyValue(request,
						SwtUtil.getUserCurrentEntity(request.getSession()),
						SwtConstants.MAINT_EVENT_ID, "display", "column_order", columnOrder);
			}
			request.setAttribute("reply_status_ok", "true");
			request.setAttribute("reply_message", "Column order saved ok");
		} catch (Exception e) {
			log.error(this.getClass().getName() + "- [ saveColumnOrder() ] - "
					+ e);
			request.setAttribute("reply_status_ok", "false");
			request.setAttribute("reply_message", e.getMessage());
			request.setAttribute(
					"reply_location",
					e.getStackTrace()[0].getClassName() + "."
							+ e.getStackTrace()[0].getMethodName() + ":"
							+ e.getStackTrace()[0].getLineNumber());
		} finally {
			columnOrder = null;
			log.debug(this.getClass().getName()
					+ " - [ saveColumnOrder ] - Exit ");
		}
		return ("statechange");
	}
	
	
	private List<ColumnInfo> getGridColumns(String width, String columnOrder, String hiddenColumns,
  			HttpServletRequest request) throws SwtException {
  		// Array list to hold column order
  		ArrayList<String> orders = null;
  		// String array variable to hold column order property
  		String[] columnOrderProp = null;
  		// Iterator variable to hold column order
  		Iterator<String> columnOrderItr = null;
  		// Hash map to hold column width
  		LinkedHashMap<String, String> widths = null;
  		// String array variable to hold width property
  		String[] columnWidthProperty = null;
  		// List for column info
  		List<ColumnInfo> lstColumns = null;
  		/* Hash map to hold column hidden_Columns */
  		HashMap<String, Boolean> hiddenColumnsMap = new LinkedHashMap<String, Boolean>();
  		/* Array list to hold hidden Column array */
  		ArrayList<String> lstHiddenColunms = null;
  		/* String array variable to hold hidden columns property */
  		String[] hiddenColumnsProp = null;
  		try {
  			// log debug message
  			log.debug(this.getClass().getName() + " - [ getGridColumns ] - Entry");
  			// Condition to check width is null

  			if (SwtUtil.isEmptyOrNull(width)) {
  				// default width for columns
  				width = 
  						
					SwtConstants.MAINT_EVENT_MAINT_EVENT_ID_TAGNAME + "=100"  +","+
					SwtConstants.MAINT_EVENT_MAINT_FACILITY_ID_TAGNAME + "=250" 
 +"," 				 + SwtConstants.MAINT_EVENT_ACTION_TAGNAME + "=100" 
 +"," 				 + SwtConstants.MAINT_EVENT_RECORD_ID_TAGNAME + "=250" 
 +"," 				 + SwtConstants.MAINT_EVENT_STATUS_TAGNAME + "=100" 
 +"," 				 + SwtConstants.MAINT_EVENT_REQUEST_USER_TAGNAME + "=150" 
 +"," 				 + SwtConstants.MAINT_EVENT_REQUEST_DATE_TAGNAME + "=180" 
 +"," 				 + SwtConstants.MAINT_EVENT_AUTH_USER_TAGNAME + "=150" 
 +"," 				 + SwtConstants.MAINT_EVENT_AUTH_DATE_TAGNAME + "=180" 
 +"," 				 + SwtConstants.MAINT_EVENT_PREV_ID_TAGNAME + "=100" 
 +"," 				 + SwtConstants.MAINT_EVENT_NEXT_ID_TAGNAME + "=100" ;
   			}

  			// Obtain width for each column
  			columnWidthProperty = width.split(",");

  			// Loop to insert each column in hash map
  			widths = new LinkedHashMap<String, String>();
  			for (int i = 0; i < columnWidthProperty.length; i++) {
  				// Condition to check index of = is -1
  				if (columnWidthProperty[i].indexOf("=") != -1) {
  					String[] propval = columnWidthProperty[i].split("=");
  					widths.put(propval[0], propval[1]);
  				}
  			}

  			// Condition to check column order is null or empty
  			if (SwtUtil.isEmptyOrNull(columnOrder)) {
  				// Default values for column order
  				columnOrder = 
					SwtConstants.MAINT_EVENT_MAINT_EVENT_ID_TAGNAME +","+
  				   SwtConstants.MAINT_EVENT_MAINT_FACILITY_ID_TAGNAME  
+"," 				+ SwtConstants.MAINT_EVENT_ACTION_TAGNAME  
+"," 				+ SwtConstants.MAINT_EVENT_RECORD_ID_TAGNAME  
+"," 				+ SwtConstants.MAINT_EVENT_STATUS_TAGNAME  
+"," 				+ SwtConstants.MAINT_EVENT_REQUEST_USER_TAGNAME  
+"," 				+ SwtConstants.MAINT_EVENT_REQUEST_DATE_TAGNAME  
+"," 				+ SwtConstants.MAINT_EVENT_AUTH_USER_TAGNAME  
+"," 				+ SwtConstants.MAINT_EVENT_AUTH_DATE_TAGNAME  
+"," 				+ SwtConstants.MAINT_EVENT_PREV_ID_TAGNAME  
+"," 				+ SwtConstants.MAINT_EVENT_NEXT_ID_TAGNAME ; 
}
  			orders = new ArrayList<String>();
  			// Split the columns using , and save in string array
  			columnOrderProp = columnOrder.split(",");

  			// Loop to enter column order in array list
  			for (int i = 0; i < columnOrderProp.length; i++) {
  				// Adding the Column values to ArrayList
  				orders.add(columnOrderProp[i]);
  				hiddenColumnsMap.put(columnOrderProp[i], true);
  			}

  			/* Condition to check column hidden is null or empty */
  			if (!SwtUtil.isEmptyOrNull(hiddenColumns)) {
  				lstHiddenColunms = new ArrayList<String>();
  				/* Split the hidden columns , and save in string array */
  				hiddenColumnsProp = hiddenColumns.split(",");

  				for (int i = 0; i < hiddenColumnsProp.length; i++) {
  					/* Adding the Column values to ArrayList */
  					lstHiddenColunms.add(hiddenColumnsProp[i]);
  				}

  				Iterator<String> listKeys = hiddenColumnsMap.keySet().iterator();
  				for (int i = 0; i < hiddenColumnsMap.size(); i++) {
  					String columnKey = listKeys.next();
  					// boolean found = false;
  					for (int j = 0; j < lstHiddenColunms.size(); j++) {
  						if (columnKey.equals(lstHiddenColunms.get(j))) {
  							hiddenColumnsMap.put(columnKey, false);
  							break;
  						}
  					}
  				}
  			}

  			columnOrderItr = orders.iterator();
  			lstColumns = new ArrayList<ColumnInfo>();

  			while (columnOrderItr.hasNext()) {
  				String order = (String) columnOrderItr.next();
 				ColumnInfo tmpColumnInfo = null;
  				// Maint Event Id column
  				if (order.equals(SwtConstants.MAINT_EVENT_MAINT_EVENT_ID_TAGNAME)) {
  					tmpColumnInfo = (new ColumnInfo("ID",
  							SwtConstants.MAINT_EVENT_MAINT_EVENT_ID_TAGNAME, SwtConstants.COLUMN_TYPE_NUMBER, 0,
  							Integer.parseInt(widths.get(SwtConstants.MAINT_EVENT_MAINT_EVENT_ID_TAGNAME)), false,
  							true, hiddenColumnsMap.get(SwtConstants.MAINT_EVENT_MAINT_EVENT_ID_TAGNAME)));
  					
  					tmpColumnInfo.setHeaderTooltip(SwtUtil.getMessage(SwtConstants.MAINT_EVENT_MAINT_EVENT_ID_HEADING_TOOLTIP, request));
  					lstColumns.add(tmpColumnInfo);
  				}
  				// Maint Facility Id column
  				if (order.equals(SwtConstants.MAINT_EVENT_MAINT_FACILITY_ID_TAGNAME)) {
  					tmpColumnInfo = (new ColumnInfo(SwtUtil.getMessage(SwtConstants.MAINT_EVENT_MAINT_FACILITY_ID_HEADING, request),
  							SwtConstants.MAINT_EVENT_MAINT_FACILITY_ID_TAGNAME, SwtConstants.COLUMN_TYPE_STRING, 1,
  							Integer.parseInt(widths.get(SwtConstants.MAINT_EVENT_MAINT_FACILITY_ID_TAGNAME)), false,
  							true, hiddenColumnsMap.get(SwtConstants.MAINT_EVENT_MAINT_FACILITY_ID_TAGNAME)));
  					
  					tmpColumnInfo.setHeaderTooltip(SwtUtil.getMessage(SwtConstants.MAINT_EVENT_MAINT_FACILITY_ID_HEADING_TOOLTIP, request));
  					lstColumns.add(tmpColumnInfo);
  				}
  				// Action column
  				if (order.equals(SwtConstants.MAINT_EVENT_ACTION_TAGNAME)) {
  					tmpColumnInfo = (new ColumnInfo(SwtUtil.getMessage(SwtConstants.MAINT_EVENT_ACTION_HEADING, request),
  							SwtConstants.MAINT_EVENT_ACTION_TAGNAME, SwtConstants.COLUMN_TYPE_STRING, 2,
  							Integer.parseInt(widths.get(SwtConstants.MAINT_EVENT_ACTION_TAGNAME)), false,
  							true, hiddenColumnsMap.get(SwtConstants.MAINT_EVENT_ACTION_TAGNAME)));
  					
  					tmpColumnInfo.setHeaderTooltip(SwtUtil.getMessage(SwtConstants.MAINT_EVENT_ACTION_HEADING_TOOLTIP, request));
  					lstColumns.add(tmpColumnInfo);
  				}
  				// Record Id column
  				if (order.equals(SwtConstants.MAINT_EVENT_RECORD_ID_TAGNAME)) {
  					tmpColumnInfo = (new ColumnInfo(SwtUtil.getMessage(SwtConstants.MAINT_EVENT_RECORD_ID_HEADING, request),
  							SwtConstants.MAINT_EVENT_RECORD_ID_TAGNAME, SwtConstants.COLUMN_TYPE_STRING, 3,
  							Integer.parseInt(widths.get(SwtConstants.MAINT_EVENT_RECORD_ID_TAGNAME)), false,
  							true, hiddenColumnsMap.get(SwtConstants.MAINT_EVENT_RECORD_ID_TAGNAME)));
  					
  					tmpColumnInfo.setHeaderTooltip(SwtUtil.getMessage(SwtConstants.MAINT_EVENT_RECORD_ID_HEADING_TOOLTIP, request));
  					lstColumns.add(tmpColumnInfo);
  				}
  				// Status column
  				if (order.equals(SwtConstants.MAINT_EVENT_STATUS_TAGNAME)) {
  					tmpColumnInfo = (new ColumnInfo(SwtUtil.getMessage(SwtConstants.MAINT_EVENT_STATUS_HEADING, request),
  							SwtConstants.MAINT_EVENT_STATUS_TAGNAME, SwtConstants.COLUMN_TYPE_STRING, 4,
  							Integer.parseInt(widths.get(SwtConstants.MAINT_EVENT_STATUS_TAGNAME)), false,
  							true, hiddenColumnsMap.get(SwtConstants.MAINT_EVENT_STATUS_TAGNAME)));
  					
  					tmpColumnInfo.setHeaderTooltip(SwtUtil.getMessage(SwtConstants.MAINT_EVENT_STATUS_HEADING_TOOLTIP, request));
  					lstColumns.add(tmpColumnInfo);
  				}
  				// Request User column
  				if (order.equals(SwtConstants.MAINT_EVENT_REQUEST_USER_TAGNAME)) {
  					tmpColumnInfo = (new ColumnInfo(SwtUtil.getMessage(SwtConstants.MAINT_EVENT_REQUEST_USER_HEADING, request),
  							SwtConstants.MAINT_EVENT_REQUEST_USER_TAGNAME, SwtConstants.COLUMN_TYPE_STRING, 5,
  							Integer.parseInt(widths.get(SwtConstants.MAINT_EVENT_REQUEST_USER_TAGNAME)), false,
  							true, hiddenColumnsMap.get(SwtConstants.MAINT_EVENT_REQUEST_USER_TAGNAME)));
  					
  					tmpColumnInfo.setHeaderTooltip(SwtUtil.getMessage(SwtConstants.MAINT_EVENT_REQUEST_USER_HEADING_TOOLTIP, request));
  					lstColumns.add(tmpColumnInfo);
  				}
  				// Request Date column
  				if (order.equals(SwtConstants.MAINT_EVENT_REQUEST_DATE_TAGNAME)) {
  					tmpColumnInfo = (new ColumnInfo(SwtUtil.getMessage(SwtConstants.MAINT_EVENT_REQUEST_DATE_HEADING, request),
  							SwtConstants.MAINT_EVENT_REQUEST_DATE_TAGNAME, SwtConstants.COLUMN_TYPE_DATE, 6,
  							Integer.parseInt(widths.get(SwtConstants.MAINT_EVENT_REQUEST_DATE_TAGNAME)), false,
  							true, hiddenColumnsMap.get(SwtConstants.MAINT_EVENT_REQUEST_DATE_TAGNAME)));
  					
  					tmpColumnInfo.setHeaderTooltip(SwtUtil.getMessage(SwtConstants.MAINT_EVENT_REQUEST_DATE_HEADING_TOOLTIP, request));
  					lstColumns.add(tmpColumnInfo);
  				}
  				// Auth User column
  				if (order.equals(SwtConstants.MAINT_EVENT_AUTH_USER_TAGNAME)) {
  					tmpColumnInfo = (new ColumnInfo(SwtUtil.getMessage(SwtConstants.MAINT_EVENT_AUTH_USER_HEADING, request),
  							SwtConstants.MAINT_EVENT_AUTH_USER_TAGNAME, SwtConstants.COLUMN_TYPE_STRING, 7,
  							Integer.parseInt(widths.get(SwtConstants.MAINT_EVENT_AUTH_USER_TAGNAME)), false,
  							true, hiddenColumnsMap.get(SwtConstants.MAINT_EVENT_AUTH_USER_TAGNAME)));
  					
  					tmpColumnInfo.setHeaderTooltip(SwtUtil.getMessage(SwtConstants.MAINT_EVENT_AUTH_USER_HEADING_TOOLTIP, request));
  					lstColumns.add(tmpColumnInfo);
  				}
  				// Auth Date column
  				if (order.equals(SwtConstants.MAINT_EVENT_AUTH_DATE_TAGNAME)) {
  					tmpColumnInfo = (new ColumnInfo(SwtUtil.getMessage(SwtConstants.MAINT_EVENT_AUTH_DATE_HEADING, request),
  							SwtConstants.MAINT_EVENT_AUTH_DATE_TAGNAME, SwtConstants.COLUMN_TYPE_DATE, 8,
  							Integer.parseInt(widths.get(SwtConstants.MAINT_EVENT_AUTH_DATE_TAGNAME)), false,
  							true, hiddenColumnsMap.get(SwtConstants.MAINT_EVENT_AUTH_DATE_TAGNAME)));
  					
  					tmpColumnInfo.setHeaderTooltip(SwtUtil.getMessage(SwtConstants.MAINT_EVENT_AUTH_DATE_HEADING_TOOLTIP, request));
  					lstColumns.add(tmpColumnInfo);
  				}
  				// Prev Id column
  				if (order.equals(SwtConstants.MAINT_EVENT_PREV_ID_TAGNAME)) {
  					tmpColumnInfo = (new ColumnInfo(SwtUtil.getMessage(SwtConstants.MAINT_EVENT_PREV_ID_HEADING, request),
  							SwtConstants.MAINT_EVENT_PREV_ID_TAGNAME, SwtConstants.COLUMN_TYPE_NUMBER, 9,
  							Integer.parseInt(widths.get(SwtConstants.MAINT_EVENT_PREV_ID_TAGNAME)), false,
  							true, hiddenColumnsMap.get(SwtConstants.MAINT_EVENT_PREV_ID_TAGNAME)));
  					
  					tmpColumnInfo.setHeaderTooltip(SwtUtil.getMessage(SwtConstants.MAINT_EVENT_PREV_ID_HEADING_TOOLTIP, request));
  					lstColumns.add(tmpColumnInfo);
  				}
  				// Next Id column
  				if (order.equals(SwtConstants.MAINT_EVENT_NEXT_ID_TAGNAME)) {
  					tmpColumnInfo = (new ColumnInfo(SwtUtil.getMessage(SwtConstants.MAINT_EVENT_NEXT_ID_HEADING, request),
  							SwtConstants.MAINT_EVENT_NEXT_ID_TAGNAME, SwtConstants.COLUMN_TYPE_NUMBER, 10,
  							Integer.parseInt(widths.get(SwtConstants.MAINT_EVENT_NEXT_ID_TAGNAME)), false,
  							true, hiddenColumnsMap.get(SwtConstants.MAINT_EVENT_NEXT_ID_TAGNAME)));
  					
  					tmpColumnInfo.setHeaderTooltip(SwtUtil.getMessage(SwtConstants.MAINT_EVENT_NEXT_ID_HEADING_TOOLTIP, request));
  					lstColumns.add(tmpColumnInfo);
  				}

 	  					

  			}

  		} catch (Exception ex) {
  			// log error message
  			log.error(this.getClass().getName()
  					+ " - Exception Catched in [getGridColumns] method : - " + ex.getMessage());

  			throw SwtErrorHandler.getInstance().handleException(ex, "getGridColumns",
  					this.getClass());

  		} finally {
  			// Nullify Objects
  			orders = null;
  			columnOrderProp = null;
  			columnOrderItr = null;
  			widths = null;
  			columnWidthProperty = null;
  			// log debug message
  			log.debug(this.getClass().getName() + " - [ getGridColumns ] - Exit");
  		}
  		// return XML columns
  		return lstColumns;
  	}
	
	
	
	
	
	
	
	
	
	
	
	
	
	
	
	
	
	
	
	
	
	
	
	/**
	 * This method is used to load the pre-advice input screen and it will be
	 * invoked while changing entity on screen to get the corresponding details.
	 * 
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return
	 * @throws SwtException
	 */
	public String displayLog()
			throws SwtException {
HttpServletRequest request = ServletActionContext.getRequest();
HttpServletResponse response = ServletActionContext.getResponse();

		/* Local variable declaration */
		// To host for application
		String hostId = null;
		// To hold the fromDate from screen
		String dateFormat = null;
		// To hold the role for current user
		String roleId = null;
		// to check if the entity has been changed
		ResponseHandler responseHandler = null;
		SwtResponseConstructor responseConstructor = null;
		SwtXMLWriter xmlWriter = null;
		String width = null;
		String columnOrder = null;
		String hiddenColumns = null;
		SystemFormats sysFormat=null;
		String time = null;
		String maintEventId = null;
		String currentUserId = null;
		Collection<MaintenanceLogViewAuth> listLogRecords = new ArrayList<MaintenanceLogViewAuth>();
		ArrayList<FacilityAccess>  accessList = null;
		CommonDataManager cdm = null;
		try {
			log.debug(this.getClass().getName() + " - [displayLog()] - Entry");

			sysFormat = SwtUtil.getCurrentSystemFormats(request.getSession());
			dateFormat= SwtUtil.getCurrentDateFormat(request.getSession());
			// to get the host id for application
			hostId = CacheManager.getInstance().getHostId();
			cdm = (CommonDataManager) request.getSession().getAttribute(SwtConstants.CDM_BEAN);
			// Get role id associated with the logged-in user
			roleId = cdm.getUser().getRoleId();
			currentUserId = cdm.getUser().getId().getUserId();
			RoleManager roleManager = (RoleManager) SwtUtil.getBean("roleManager");
			HashMap<String, String> facilitiesList = roleManager.getAllFacilitiesList();
			
			
			maintEventId= request.getParameter("maintEventId") ;

			//get the list of records saved in  Account Currency Maintenance Period table
			listLogRecords=maintenanceEventMaintenanceManager.getMaintenanceLogList(maintEventId);
			MaintenanceEvent event = maintenanceEventMaintenanceManager.getMaintenanceEvent(maintEventId);
			
			
			roleId = ((CommonDataManager) request.getSession()
					.getAttribute(SwtConstants.CDM_BEAN)).getUser()
					.getRoleId();	
			accessList = (ArrayList<FacilityAccess>) roleManager.getFacilityAccessList(roleId);
			
			boolean containsAuthOthers = accessList.stream()
			        .filter(fa -> fa.getId().getFacilityId().equals(event.getMaintFacilityId()))
			        .anyMatch(fa -> fa.getAuthOthers().equals("Y"));
			
				boolean fullAccessToScreen = false;
				
				LogonDAO logonDAO = (LogonDAO) SwtUtil.getBean("logonDAO");
	            MenuItem menuItem;
				try {
					menuItem = logonDAO.getMenuItem(MaintenanceEventMaintenanceAction.getMenuItemFromFacilityId(event.getMaintFacilityId()) + "", cdm.getUser());
		            if (menuItem != null) {
		            	fullAccessToScreen = SwtUtil.getHierarchicalAccessId(menuItem, request) == 0;
		            }
				} catch (SwtException e) {
					e.printStackTrace();
				}
			
			
			responseHandler = new ResponseHandler();
			responseConstructor = new SwtResponseConstructor();
			xmlWriter = responseConstructor.getXMLWriter();

			xmlWriter.startElement(SwtConstants.CCY_ACC_MAINT_PERIOD);

			responseConstructor.formRequestReply(Boolean.parseBoolean(SwtConstants.STR_TRUE),
					SwtConstants.DATA_FETCH_OK);
			// forms singleton node
			xmlWriter.startElement(SwtConstants.SINGLETONS);
			
			
			responseConstructor.createElement("maintEventId",""+maintEventId );
			responseConstructor.createElement("authUser",event.getAuthUser() );
			responseConstructor.createElement("authDate",(event.getAuthDate() == null || event.getAuthDate().equals("")) ? "" :  SwtUtil.formatDate(event.getAuthDate(), dateFormat ) );
			responseConstructor.createElement("requestUser",event.getRequestUser() );
			responseConstructor.createElement("requestDate",(event.getRequestDate() == null || event.getRequestDate().equals("")) ? "" :  SwtUtil.formatDate(event.getRequestDate(), dateFormat ) );
			responseConstructor.createElement("maintFacilityId",facilitiesList.get(event.getMaintFacilityId()) );
			responseConstructor.createElement("recordId",event.getRecordId() );
			responseConstructor.createElement("status",getStatusDesc(event.getStatus()) );
			responseConstructor.createElement("action",getActionDesc(event.getAction()) );
			responseConstructor.createElement("actionValue",event.getAction() );
			responseConstructor.createElement("authOthers",""+containsAuthOthers );
			responseConstructor.createElement("fullAccessToScreen",""+fullAccessToScreen );
			responseConstructor.createElement("currentUserId",""+currentUserId );
			
			
			
			responseConstructor.createElement("dateFormat",	dateFormat);
			responseConstructor.createElement(SwtConstants.CURRENCYPATTERN,
					SwtUtil.getCurrentSystemFormats(request.getSession()).getCurrencyFormat());
			xmlWriter.endElement(SwtConstants.SINGLETONS); 

			
			/******* acctCcyMaintPeriodLogGrid ******/
			responseConstructor.formGridStart("acctCcyMaintPeriodLogGrid");
			responseConstructor.formColumn(getLogGridColumns(width, columnOrder, hiddenColumns, request));
			// form rows (records)
			responseConstructor.formRowsStart(listLogRecords.size());
			for (Iterator<MaintenanceLogViewAuth> it = listLogRecords.iterator(); it.hasNext();) {
				// Obtain rules definition tag from iterator
				MaintenanceLogViewAuth acctCcyLog = (MaintenanceLogViewAuth) it.next();
				responseConstructor.formRowStart();
				
				time = 	(acctCcyLog.getId().getLogDate()).toString().split("\\s+")[1];			
								 
								 
				responseConstructor.createRowElement(SwtConstants.CCY_ACC_MAINT_PERIOD_LOG_DATE_TAGNAME , 
						(acctCcyLog.getId().getLogDate() == null || acctCcyLog.getId().getLogDate().equals("")) ? "" :  SwtUtil.formatDate(acctCcyLog.getId().getLogDate(), dateFormat ));
				responseConstructor.createRowElement(SwtConstants.CCY_ACC_MAINT_PERIOD_LOG_TIME_TAGNAME, 
						time.substring(0, time.length() - 2));
				responseConstructor.createRowElement(SwtConstants.CCY_ACC_MAINT_PERIOD_LOG_USER_TAGNAME,acctCcyLog.getId().getUserId());
				responseConstructor.createRowElement(SwtConstants.CCY_ACC_MAINT_PERIOD_LOG_IP_ADDRESS_TAGNAME,acctCcyLog.getId().getIpAddress());
				responseConstructor.createRowElement(SwtConstants.CCY_ACC_MAINT_PERIOD_LOG_FACILITY_TAGNAME, acctCcyLog.getId().getTableName());
				responseConstructor.createRowElement(SwtConstants.CCY_ACC_MAINT_PERIOD_LOG_REFERENCE_TAGNAME, (acctCcyLog.getId().getReference() ));
				responseConstructor.createRowElement(SwtConstants.CCY_ACC_MAINT_PERIOD_LOG_ACTION_TAGNAME, getActionDesc(acctCcyLog.getId().getAction()));
				responseConstructor.formRowEnd();
			}
	
			responseConstructor.formRowsEnd();
			responseConstructor.formGridEnd();
			
			xmlWriter.endElement(SwtConstants.CCY_ACC_MAINT_PERIOD);
			request.setAttribute("data", xmlWriter.getData());

			// set the method name,last ref time and access level on request
			// attribute which are used in front end
			request.setAttribute("methodName", "success");
			log.debug(this.getClass().getName() + " - [displayLog()] - Exit");
			return ("data");
		} catch (SwtException swtexp) {
			log
					.error("SwtException Catch in AcctCcyPeriodMaintenanceAction.'displayLog' method : "
							+ swtexp.getMessage());
			SwtUtil.logException(swtexp, request, "");
			return ("fail");
		} catch (Exception exp) {
			log
					.error("Exception Catch in AcctCcyPeriodMaintenanceAction.'displayLog' method : "
							+ exp.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "displayLog", MaintenanceEventMaintenanceAction.class), request, "");
			return ("fail");
		} finally {
			/* null the objects created already. */
			responseConstructor = null;
			xmlWriter = null;
			hostId = null;
            dateFormat = null;
            sysFormat = null;
			roleId = null;
			time = null;
			maintEventId = null;
			listLogRecords = null;
		}
	}
	

	private List<ColumnInfo> getLogGridColumns(String width, String columnOrder, String hiddenColumns,
  			HttpServletRequest request) throws SwtException {
  		// Array list to hold column order
  		ArrayList<String> orders = null;
  		// String array variable to hold column order property
  		String[] columnOrderProp = null;
  		// Iterator variable to hold column order
  		Iterator<String> columnOrderItr = null;
  		// Hash map to hold column width
  		LinkedHashMap<String, String> widths = null;
  		// String array variable to hold width property
  		String[] columnWidthProperty = null;
  		// List for column info
  		List<ColumnInfo> lstColumns = null;
  		/* Hash map to hold column hidden_Columns */
  		HashMap<String, Boolean> hiddenColumnsMap = new LinkedHashMap<String, Boolean>();
  		/* Array list to hold hidden Column array */
  		ArrayList<String> lstHiddenColunms = null;
  		/* String array variable to hold hidden columns property */
  		String[] hiddenColumnsProp = null;
  		try {
  			// log debug message
  			log.debug(this.getClass().getName() + " - [ getLogGridColumns ] - Entry");
  			// Condition to check width is null

  			if (SwtUtil.isEmptyOrNull(width)) {
  				// default width for columns
				width =  SwtConstants.CCY_ACC_MAINT_PERIOD_LOG_DATE_TAGNAME + "=120" + ","
						+ SwtConstants.CCY_ACC_MAINT_PERIOD_LOG_TIME_TAGNAME + "=120" + ","
//						+ SwtConstants.CCY_ACC_MAINT_PERIOD_LOG_USER_TAGNAME + "=120" + ","
						+ SwtConstants.CCY_ACC_MAINT_PERIOD_LOG_FACILITY_TAGNAME + "=300" + ","
//						+ SwtConstants.CCY_ACC_MAINT_PERIOD_LOG_IP_ADDRESS_TAGNAME + "=150" + ","
						+ SwtConstants.CCY_ACC_MAINT_PERIOD_LOG_REFERENCE_TAGNAME + "=350" + ","
						+ SwtConstants.CCY_ACC_MAINT_PERIOD_LOG_ACTION_TAGNAME + "=120" ;
   			}

  			// Obtain width for each column
  			columnWidthProperty = width.split(",");

  			// Loop to insert each column in hash map
  			widths = new LinkedHashMap<String, String>();
  			for (int i = 0; i < columnWidthProperty.length; i++) {
  				// Condition to check index of = is -1
  				if (columnWidthProperty[i].indexOf("=") != -1) {
  					String[] propval = columnWidthProperty[i].split("=");
  					widths.put(propval[0], propval[1]);
  				}
  			}

  			// Condition to check column order is null or empty
  			if (SwtUtil.isEmptyOrNull(columnOrder)) {
  				// Default values for column order
				columnOrder = SwtConstants.CCY_ACC_MAINT_PERIOD_LOG_DATE_TAGNAME + ","
						+ SwtConstants.CCY_ACC_MAINT_PERIOD_LOG_TIME_TAGNAME + ","
//						+ SwtConstants.CCY_ACC_MAINT_PERIOD_LOG_USER_TAGNAME + ","
						+ SwtConstants.CCY_ACC_MAINT_PERIOD_LOG_FACILITY_TAGNAME + ","
//						+ SwtConstants.CCY_ACC_MAINT_PERIOD_LOG_IP_ADDRESS_TAGNAME + ","
						+ SwtConstants.CCY_ACC_MAINT_PERIOD_LOG_REFERENCE_TAGNAME + ","
						+ SwtConstants.CCY_ACC_MAINT_PERIOD_LOG_ACTION_TAGNAME;
}
  			orders = new ArrayList<String>();
  			// Split the columns using , and save in string array
  			columnOrderProp = columnOrder.split(",");

  			// Loop to enter column order in array list
  			for (int i = 0; i < columnOrderProp.length; i++) {
  				// Adding the Column values to ArrayList
  				orders.add(columnOrderProp[i]);
  				hiddenColumnsMap.put(columnOrderProp[i], true);
  			}

  			/* Condition to check column hidden is null or empty */
  			if (!SwtUtil.isEmptyOrNull(hiddenColumns)) {
  				lstHiddenColunms = new ArrayList<String>();
  				/* Split the hidden columns , and save in string array */
  				hiddenColumnsProp = hiddenColumns.split(",");

  				for (int i = 0; i < hiddenColumnsProp.length; i++) {
  					/* Adding the Column values to ArrayList */
  					lstHiddenColunms.add(hiddenColumnsProp[i]);
  				}

  				Iterator<String> listKeys = hiddenColumnsMap.keySet().iterator();
  				for (int i = 0; i < hiddenColumnsMap.size(); i++) {
  					String columnKey = listKeys.next();
  					// boolean found = false;
  					for (int j = 0; j < lstHiddenColunms.size(); j++) {
  						if (columnKey.equals(lstHiddenColunms.get(j))) {
  							hiddenColumnsMap.put(columnKey, false);
  							break;
  						}
  					}
  				}
  			}

  			columnOrderItr = orders.iterator();
  			lstColumns = new ArrayList<ColumnInfo>();

  			while (columnOrderItr.hasNext()) {
  				String order = (String) columnOrderItr.next();
 				ColumnInfo tmpColumnInfo = null;
  				// Host Id column
  				if (order.equals(SwtConstants.CCY_ACC_MAINT_PERIOD_LOG_DATE_TAGNAME)) {
  					tmpColumnInfo = (new ColumnInfo(SwtUtil.getMessage(SwtConstants.CCY_ACC_MAINT_PERIOD_LOG_DATE_HEADING, request),
  							SwtConstants.CCY_ACC_MAINT_PERIOD_LOG_DATE_TAGNAME, SwtConstants.COLUMN_TYPE_STRING, 0,
  							Integer.parseInt(widths.get(SwtConstants.CCY_ACC_MAINT_PERIOD_LOG_DATE_TAGNAME)), false,
  							true, hiddenColumnsMap.get(SwtConstants.CCY_ACC_MAINT_PERIOD_LOG_DATE_TAGNAME)));
  					
  					tmpColumnInfo.setHeaderTooltip(SwtUtil.getMessage(SwtConstants.CCY_ACC_MAINT_PERIOD_LOG_DATE_HEADING_TOOLTIP, request));
  					lstColumns.add(tmpColumnInfo);
  				}
  				// Entity Id column
  				if (order.equals(SwtConstants.CCY_ACC_MAINT_PERIOD_LOG_TIME_TAGNAME)) {
  					tmpColumnInfo = (new ColumnInfo(SwtUtil.getMessage(SwtConstants.CCY_ACC_MAINT_PERIOD_LOG_TIME_HEADING, request),
  							SwtConstants.CCY_ACC_MAINT_PERIOD_LOG_TIME_TAGNAME, SwtConstants.COLUMN_TYPE_STRING, 1,
  							Integer.parseInt(widths.get(SwtConstants.CCY_ACC_MAINT_PERIOD_LOG_TIME_TAGNAME)), false,
  							true, hiddenColumnsMap.get(SwtConstants.CCY_ACC_MAINT_PERIOD_LOG_TIME_TAGNAME)));
  					
  					tmpColumnInfo.setHeaderTooltip(SwtUtil.getMessage(SwtConstants.CCY_ACC_MAINT_PERIODv_TIME_HEADING_TOOLTIP, request));
  					lstColumns.add(tmpColumnInfo);
  				}
  				// Account Id column
  				if (order.equals(SwtConstants.CCY_ACC_MAINT_PERIOD_LOG_USER_TAGNAME)) {
  					tmpColumnInfo = (new ColumnInfo(SwtUtil.getMessage(SwtConstants.CCY_ACC_MAINT_PERIOD_LOG_USER_HEADING, request),
  							SwtConstants.CCY_ACC_MAINT_PERIOD_LOG_USER_TAGNAME, SwtConstants.COLUMN_TYPE_STRING, 2,
  							Integer.parseInt(widths.get(SwtConstants.CCY_ACC_MAINT_PERIOD_LOG_USER_TAGNAME)), false,
  							true, hiddenColumnsMap.get(SwtConstants.CCY_ACC_MAINT_PERIOD_LOG_USER_TAGNAME)));
  					
  					tmpColumnInfo.setHeaderTooltip(SwtUtil.getMessage(SwtConstants.CCY_ACC_MAINT_PERIOD_LOG_USER_HEADING_TOOLTIP, request));
  					lstColumns.add(tmpColumnInfo);
  				}
  				
  				
  			// Account Id column
  				if (order.equals(SwtConstants.CCY_ACC_MAINT_PERIOD_LOG_FACILITY_TAGNAME)) {
  					tmpColumnInfo = (new ColumnInfo(SwtUtil.getMessage(SwtConstants.CCY_ACC_MAINT_PERIOD_LOG_FACILITY_HEADING, request),
  							SwtConstants.CCY_ACC_MAINT_PERIOD_LOG_FACILITY_TAGNAME, SwtConstants.COLUMN_TYPE_STRING, 3,
  							Integer.parseInt(widths.get(SwtConstants.CCY_ACC_MAINT_PERIOD_LOG_FACILITY_TAGNAME)), false,
  							true, hiddenColumnsMap.get(SwtConstants.CCY_ACC_MAINT_PERIOD_LOG_FACILITY_TAGNAME)));
  					
  					tmpColumnInfo.setHeaderTooltip(SwtUtil.getMessage(SwtConstants.CCY_ACC_MAINT_PERIODLOG_FACILITY_HEADING_TOOLTIP, request));
  					lstColumns.add(tmpColumnInfo);
  				}
  				
  				
  				
  				// Start Date column
  				if (order.equals(SwtConstants.CCY_ACC_MAINT_PERIOD_LOG_IP_ADDRESS_TAGNAME)) {
  					tmpColumnInfo = (new ColumnInfo(SwtUtil.getMessage(SwtConstants.CCY_ACC_MAINT_PERIOD_LOG_IP_ADDRESS_HEADING, request),
  							SwtConstants.CCY_ACC_MAINT_PERIOD_LOG_IP_ADDRESS_TAGNAME, SwtConstants.COLUMN_TYPE_STRING, 4,
  							Integer.parseInt(widths.get(SwtConstants.CCY_ACC_MAINT_PERIOD_LOG_IP_ADDRESS_TAGNAME)), false,
  							true, hiddenColumnsMap.get(SwtConstants.CCY_ACC_MAINT_PERIOD_LOG_IP_ADDRESS_TAGNAME)));
  					
  					tmpColumnInfo.setHeaderTooltip(SwtUtil.getMessage(SwtConstants.CCY_ACC_MAINT_PERIOD_LOG_IP_ADDRESS_HEADING_TOOLTIP, request));
  					lstColumns.add(tmpColumnInfo);
  				}
  				// End Date column
  				if (order.equals(SwtConstants.CCY_ACC_MAINT_PERIOD_LOG_REFERENCE_TAGNAME)) {
  					tmpColumnInfo = (new ColumnInfo(SwtUtil.getMessage(SwtConstants.CCY_ACC_MAINT_PERIOD_LOG_REFERENCE_HEADING, request),
  							SwtConstants.CCY_ACC_MAINT_PERIOD_LOG_REFERENCE_TAGNAME, SwtConstants.COLUMN_TYPE_STRING, 5,
  							Integer.parseInt(widths.get(SwtConstants.CCY_ACC_MAINT_PERIOD_LOG_REFERENCE_TAGNAME)), false,
  							true, hiddenColumnsMap.get(SwtConstants.CCY_ACC_MAINT_PERIOD_LOG_REFERENCE_TAGNAME)));
  					
  					tmpColumnInfo.setHeaderTooltip(SwtUtil.getMessage(SwtConstants.CCY_ACC_MAINT_PERIODv_REFERENCE_HEADING_TOOLTIP, request));
  					lstColumns.add(tmpColumnInfo);
  				}
  				// Minimum Reserve column
  				if (order.equals(SwtConstants.CCY_ACC_MAINT_PERIOD_LOG_ACTION_TAGNAME)) {
  					tmpColumnInfo = (new ColumnInfo(SwtUtil.getMessage(SwtConstants.CCY_ACC_MAINT_PERIOD_LOG_ACTION_HEADING, request),
  							SwtConstants.CCY_ACC_MAINT_PERIOD_LOG_ACTION_TAGNAME, SwtConstants.COLUMN_TYPE_STRING, 6,
  							Integer.parseInt(widths.get(SwtConstants.CCY_ACC_MAINT_PERIOD_LOG_ACTION_TAGNAME)), false,
  							true, hiddenColumnsMap.get(SwtConstants.CCY_ACC_MAINT_PERIOD_LOG_ACTION_TAGNAME)));
  					
  					tmpColumnInfo.setHeaderTooltip(SwtUtil.getMessage(SwtConstants.CCY_ACC_MAINT_PERIODv_ACTION_HEADING_TOOLTIP, request));
  					lstColumns.add(tmpColumnInfo);
  				}

  			}

  		} catch (Exception ex) {
  			// log error message
  			log.error(this.getClass().getName()
  					+ " - Exception Catched in [getLogGridColumns] method : - " + ex.getMessage());

  			throw SwtErrorHandler.getInstance().handleException(ex, "getLogGridColumns",
  					this.getClass());

  		} finally {
  			// Nullify Objects
  			orders = null;
  			columnOrderProp = null;
  			columnOrderItr = null;
  			widths = null;
  			columnWidthProperty = null;
  			// log debug message
  			log.debug(this.getClass().getName() + " - [ getLogGridColumns ] - Exit");
  		}
  		// return XML columns
  		return lstColumns;
  	}
	
	
	private String  getStatusDesc(String code) {
	    String desc = null;
	    switch (code) {
	      case "P":
	    	  desc = "Pending";
	        break;
	      case "A":
	    	  desc = "Accepted";
	        break;
	      case "R":
	    	  desc = "Rejected";
	        break;
	        
	      default:
	        break;
	    }

	    return desc;
	  }
	
	private String  getStatusCode(String desc) {
	    String code = null;

	    switch (desc) {
	      case "Pending":
	    	  code = "P";
	        break;
	      case "Accepted":
	    	  code = "A";
	        break;
	      case "Rejected":
	    	  code = "R";
	        break;
	      default:
	        break;
	    }

	    return code;
	  }
	
	
	
	private String  getActionDesc(String code) {
	    String desc = null;
	    switch (code) {
	      case "I":
	    	  desc = "Added";
	        break;
	      case "U":
	    	  desc = "Changed";
	        break;
	      case "D":
	    	  desc = "Deleted";
	        break;
	      default:
	        break;
	    }

	    return desc;
	  }
	
	
	private String  getActionCode(String desc) {
	    String code = null;

	    switch (desc) {
	      case "Added":
	    	  code = "I";
	        break;
	      case "Changed":
	    	  code = "U";
	        break;
	      default:
	        break;
	    }

	    return code;
	  }
	
	
	
		public String displayViewLogScreen() throws SwtException {
HttpServletRequest request = ServletActionContext.getRequest();
HttpServletResponse response = ServletActionContext.getResponse();

			request.setAttribute("params", request.getParameter("params"));
			return ("viewLogScreen");
		}
	
		
		/**
		 * This method is used to load the pre-advice input screen and it will be
		 * invoked while changing entity on screen to get the corresponding details.
		 * 
		 * @param mapping
		 * @param form
		 * @param request
		 * @param response
		 * @return
		 * @throws SwtException
		 */
		public String displayViewLog()
				throws SwtException {
HttpServletRequest request = ServletActionContext.getRequest();
HttpServletResponse response = ServletActionContext.getResponse();

			/* Local variable declaration */
			// To host for application
			String hostId = null;
			String logDate = null;
			String userId = null;
			String ipAddress = null;
			String reference = null;
			String tableName = null;
			String action = null;			
			// To hold the role for current user
			String roleId = null;
			// to check if the entity has been changed
			ResponseHandler responseHandler = null;
			SwtResponseConstructor responseConstructor = null;
			SwtXMLWriter xmlWriter = null;
			String width = null;
			String columnOrder = null;
			String hiddenColumns = null;
			SystemFormats sysFormat=null;
			String dateFormat=null;
			String dateF=null;
			String logTime = null;
			String ccyCode = null;
			Long maintEventId = null;
			Collection<MaintenanceLogAuth> listViewLogRecords = new ArrayList<MaintenanceLogAuth>();
			try {
				log.debug(this.getClass().getName() + " - [displayViewLog()] - Entry");

				dateFormat= SwtUtil.getCurrentDateFormat(request.getSession());//.toUpperCase()+" HH24:MI:SS";
				
				// to get the host id for application
				hostId = CacheManager.getInstance().getHostId();

				// Get role id associated with the logged-in user
				roleId = ((CommonDataManager) request.getSession().getAttribute(SwtConstants.CDM_BEAN)).getUser()
						.getRoleId();
				
				userId= request.getParameter("userId") ;
				ipAddress= request.getParameter("ipAddress") ;
				reference= request.getParameter("reference") ;
				tableName= request.getParameter("tableName") ;
				action = getActionCode(request.getParameter("action")) ; 
				logDate=request.getParameter("date");
				logTime=request.getParameter("time");
				if(!SwtUtil.isEmptyOrNull(request.getParameter("maintEventId"))) {
					maintEventId =Long.parseLong(request.getParameter("maintEventId"));
					listViewLogRecords=maintenanceEventMaintenanceManager.getViewLogDetails(hostId, userId, SwtUtil.parseDate(logDate+" "+logTime, dateFormat +" HH:mm:ss")/*dateF*/, ipAddress, tableName,reference, action, maintEventId);
				}else {
					listViewLogRecords = new ArrayList<MaintenanceLogAuth>();
				}
//				StringBuilder finalDate = new StringBuilder();
//				finalDate.append(formatDate(request.getParameter("logDate"),dateFormat,"yyyy-MM-dd")).append(" ").append(logTime);
//				dateF= finalDate.toString();
				
//				SwtUtil.parseDate(request.getParameter("logDate")+" "+logTime, dateFormat +" HH:mm:ss");
				
				//get the list of records saved in  Account Currency Maintenance Period table
                
				responseHandler = new ResponseHandler();
				responseConstructor = new SwtResponseConstructor();
				xmlWriter = responseConstructor.getXMLWriter();

				xmlWriter.startElement(SwtConstants.CCY_ACC_MAINT_PERIOD);

				responseConstructor.formRequestReply(Boolean.parseBoolean(SwtConstants.STR_TRUE),
						SwtConstants.DATA_FETCH_OK); 

				
				/******* acctCcyMaintPeriodLogGrid ******/
				responseConstructor.formGridStart("acctCcyMaintPeriodViewLogGrid");
				responseConstructor.formColumn(getViewLogGridColumns(width, columnOrder, hiddenColumns, request));
				// form rows (records)
				responseConstructor.formRowsStart(listViewLogRecords.size());
				for (Iterator<MaintenanceLogAuth> it = listViewLogRecords.iterator(); it.hasNext();) {
					// Obtain rules definition tag from iterator
					MaintenanceLogAuth acctCcyViewLog = (MaintenanceLogAuth) it.next();
					responseConstructor.formRowStart();
					
					responseConstructor.createRowElement(SwtConstants.CCY_ACC_MAINT_PERIOD_FIELD_TAGNAME,acctCcyViewLog.getColumnName());
					responseConstructor.createRowElement(SwtConstants.CCY_ACC_MAINT_PERIOD_CHANGED_FROM_TAGNAME, SwtUtil.isEmptyOrNull(acctCcyViewLog.getOldValue()) ? "" : getColFormattedValue(acctCcyViewLog.getColumnName(),acctCcyViewLog.getOldValue(),dateFormat, ccyCode));
					responseConstructor.createRowElement(SwtConstants.CCY_ACC_MAINT_PERIOD_CHANGED_TO_USER_TAGNAME,  SwtUtil.isEmptyOrNull(acctCcyViewLog.getNewValue()) ? "" : getColFormattedValue(acctCcyViewLog.getColumnName(),acctCcyViewLog.getNewValue(),dateFormat, ccyCode));
					
					responseConstructor.formRowEnd();
				}
		
				responseConstructor.formRowsEnd();
				responseConstructor.formGridEnd();
				
				xmlWriter.endElement(SwtConstants.CCY_ACC_MAINT_PERIOD);
				request.setAttribute("data", xmlWriter.getData());

				// set the method name,last ref time and access level on request
				// attribute which are used in front end
				request.setAttribute("methodName", "success");
				log.debug(this.getClass().getName() + " - [displayViewLog()] - Exit");
				return ("data");
			} catch (SwtException swtexp) {
				log
						.error("SwtException Catch in AcctCcyPeriodMaintenanceAction.'displayViewLog' method : "
								+ swtexp.getMessage());
				SwtUtil.logException(swtexp, request, "");
				return ("fail");
			} catch (Exception exp) {
				log
						.error("Exception Catch in AcctCcyPeriodMaintenanceAction.'displadisplayViewLogyLog' method : "
								+ exp.getMessage());
				SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
						exp, "displayViewLog", MaintenanceEventMaintenanceAction.class), request, "");
				return ("fail");
			} finally {
				/* null the objects created already. */
				responseHandler = null;
				responseConstructor = null;
				xmlWriter = null;				
				hostId = null;
				roleId = null;
				logDate = null;
				userId = null;
				ipAddress = null;
				reference = null;
				action = null;
				dateFormat = null;
				dateF = null;
				logTime = null;
				listViewLogRecords = null;
			}
		}
		
		
		private String formatDate (String date, String initDateFormat, String endDateFormat) throws ParseException {

		    Date initDate = null;
			try {
				initDate = new SimpleDateFormat(initDateFormat).parse(date);
			} catch (java.text.ParseException e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
			}
		    SimpleDateFormat formatter = new SimpleDateFormat(endDateFormat);
		    String parsedDate = formatter.format(initDate);

		    return parsedDate;
		}
		
		
		
		private List<ColumnInfo> getViewLogGridColumns(String width, String columnOrder, String hiddenColumns,
	  			HttpServletRequest request) throws SwtException {
	  		// Array list to hold column order
	  		ArrayList<String> orders = null;
	  		// String array variable to hold column order property
	  		String[] columnOrderProp = null;
	  		// Iterator variable to hold column order
	  		Iterator<String> columnOrderItr = null;
	  		// Hash map to hold column width
	  		LinkedHashMap<String, String> widths = null;
	  		// String array variable to hold width property
	  		String[] columnWidthProperty = null;
	  		// List for column info
	  		List<ColumnInfo> lstColumns = null;
	  		/* Hash map to hold column hidden_Columns */
	  		HashMap<String, Boolean> hiddenColumnsMap = new LinkedHashMap<String, Boolean>();
	  		/* Array list to hold hidden Column array */
	  		ArrayList<String> lstHiddenColunms = null;
	  		/* String array variable to hold hidden columns property */
	  		String[] hiddenColumnsProp = null;
	  		try {
	  			// log debug message
	  			log.debug(this.getClass().getName() + " - [ getViewLogGridColumns ] - Entry");
	  			// Condition to check width is null

	  			if (SwtUtil.isEmptyOrNull(width)) {
	  				// default width for columns
					width =  SwtConstants.CCY_ACC_MAINT_PERIOD_FIELD_TAGNAME + "=200" + ","
							+ SwtConstants.CCY_ACC_MAINT_PERIOD_CHANGED_FROM_TAGNAME + "=280" + ","
							+ SwtConstants.CCY_ACC_MAINT_PERIOD_CHANGED_TO_USER_TAGNAME + "=280" ;
	   			}

	  			// Obtain width for each column
	  			columnWidthProperty = width.split(",");

	  			// Loop to insert each column in hash map
	  			widths = new LinkedHashMap<String, String>();
	  			for (int i = 0; i < columnWidthProperty.length; i++) {
	  				// Condition to check index of = is -1
	  				if (columnWidthProperty[i].indexOf("=") != -1) {
	  					String[] propval = columnWidthProperty[i].split("=");
	  					widths.put(propval[0], propval[1]);
	  				}
	  			}

	  			// Condition to check column order is null or empty
	  			if (SwtUtil.isEmptyOrNull(columnOrder)) {
	  				// Default values for column order
					columnOrder = SwtConstants.CCY_ACC_MAINT_PERIOD_FIELD_TAGNAME + ","
							+ SwtConstants.CCY_ACC_MAINT_PERIOD_CHANGED_FROM_TAGNAME + ","
							+ SwtConstants.CCY_ACC_MAINT_PERIOD_CHANGED_TO_USER_TAGNAME ;
	}
	  			orders = new ArrayList<String>();
	  			// Split the columns using , and save in string array
	  			columnOrderProp = columnOrder.split(",");

	  			// Loop to enter column order in array list
	  			for (int i = 0; i < columnOrderProp.length; i++) {
	  				// Adding the Column values to ArrayList
	  				orders.add(columnOrderProp[i]);
	  				hiddenColumnsMap.put(columnOrderProp[i], true);
	  			}

	  			/* Condition to check column hidden is null or empty */
	  			if (!SwtUtil.isEmptyOrNull(hiddenColumns)) {
	  				lstHiddenColunms = new ArrayList<String>();
	  				/* Split the hidden columns , and save in string array */
	  				hiddenColumnsProp = hiddenColumns.split(",");

	  				for (int i = 0; i < hiddenColumnsProp.length; i++) {
	  					/* Adding the Column values to ArrayList */
	  					lstHiddenColunms.add(hiddenColumnsProp[i]);
	  				}

	  				Iterator<String> listKeys = hiddenColumnsMap.keySet().iterator();
	  				for (int i = 0; i < hiddenColumnsMap.size(); i++) {
	  					String columnKey = listKeys.next();
	  					// boolean found = false;
	  					for (int j = 0; j < lstHiddenColunms.size(); j++) {
	  						if (columnKey.equals(lstHiddenColunms.get(j))) {
	  							hiddenColumnsMap.put(columnKey, false);
	  							break;
	  						}
	  					}
	  				}
	  			}

	  			columnOrderItr = orders.iterator();
	  			lstColumns = new ArrayList<ColumnInfo>();

	  			while (columnOrderItr.hasNext()) {
	  				String order = (String) columnOrderItr.next();
	 				ColumnInfo tmpColumnInfo = null;
	  				// Host Id column
	  				if (order.equals(SwtConstants.CCY_ACC_MAINT_PERIOD_FIELD_TAGNAME)) {
	  					tmpColumnInfo = (new ColumnInfo(SwtUtil.getMessage(SwtConstants.CCY_ACC_MAINT_PERIOD_FIELD__HEADING, request),
	  							SwtConstants.CCY_ACC_MAINT_PERIOD_FIELD_TAGNAME, SwtConstants.COLUMN_TYPE_STRING, 0,
	  							Integer.parseInt(widths.get(SwtConstants.CCY_ACC_MAINT_PERIOD_FIELD_TAGNAME)), false,
	  							true, hiddenColumnsMap.get(SwtConstants.CCY_ACC_MAINT_PERIOD_FIELD_TAGNAME)));
	  					
	  					tmpColumnInfo.setHeaderTooltip(SwtUtil.getMessage(SwtConstants.CCY_ACC_MAINT_PERIOD_FIELD_HEADING_TOOLTIP, request));
	  					lstColumns.add(tmpColumnInfo);
	  				}
	  				// Entity Id column
	  				if (order.equals(SwtConstants.CCY_ACC_MAINT_PERIOD_CHANGED_FROM_TAGNAME)) {
	  					tmpColumnInfo = (new ColumnInfo(SwtUtil.getMessage(SwtConstants.CCY_ACC_MAINT_PERIOD_CHANGED_FROM_HEADING, request),
	  							SwtConstants.CCY_ACC_MAINT_PERIOD_CHANGED_FROM_TAGNAME, SwtConstants.COLUMN_TYPE_STRING, 1,
	  							Integer.parseInt(widths.get(SwtConstants.CCY_ACC_MAINT_PERIOD_CHANGED_FROM_TAGNAME)), false,
	  							true, hiddenColumnsMap.get(SwtConstants.CCY_ACC_MAINT_PERIOD_CHANGED_FROM_TAGNAME)));
	  					
	  					tmpColumnInfo.setHeaderTooltip(SwtUtil.getMessage(SwtConstants.CCY_ACC_MAINT_PERIOD_CHANGED_FROM_HEADING_TOOLTIP, request));
	  					lstColumns.add(tmpColumnInfo);
	  				}
	  				// Account Id column
	  				if (order.equals(SwtConstants.CCY_ACC_MAINT_PERIOD_CHANGED_TO_USER_TAGNAME)) {
	  					tmpColumnInfo = (new ColumnInfo(SwtUtil.getMessage(SwtConstants.CCY_ACC_MAINT_PERIOD_CHANGED_TO_HEADING, request),
	  							SwtConstants.CCY_ACC_MAINT_PERIOD_CHANGED_TO_USER_TAGNAME, SwtConstants.COLUMN_TYPE_STRING, 2,
	  							Integer.parseInt(widths.get(SwtConstants.CCY_ACC_MAINT_PERIOD_CHANGED_TO_USER_TAGNAME)), false,
	  							true, hiddenColumnsMap.get(SwtConstants.CCY_ACC_MAINT_PERIOD_CHANGED_TO_USER_TAGNAME)));
	  					
	  					tmpColumnInfo.setHeaderTooltip(SwtUtil.getMessage(SwtConstants.CCY_ACC_MAINT_PERIOD_CHANGED_TO_HEADING_TOOLTIP, request));
	  					lstColumns.add(tmpColumnInfo);
	  				}


	  			}

	  		} catch (Exception ex) {
	  			// log error message
	  			log.error(this.getClass().getName()
	  					+ " - Exception Catched in [getViewLogGridColumns] method : - " + ex.getMessage());

	  			throw SwtErrorHandler.getInstance().handleException(ex, "getViewLogGridColumns",
	  					this.getClass());

	  		} finally {
	  			// Nullify Objects
	  			orders = null;
	  			columnOrderProp = null;
	  			columnOrderItr = null;
	  			widths = null;
	  			columnWidthProperty = null;
	  			// log debug message
	  			log.debug(this.getClass().getName() + " - [ getViewLogGridColumns ] - Exit");
	  		}
	  		// return XML columns
	  		return lstColumns;
	  	}

		
		//Mantis 6135
		private String getColFormattedValue(String column, String value, String dateFormat, String ccyCode) {
			String formattedVal = null;
			if ("Target Avg Balance".equalsIgnoreCase(column) || "Minimum Target Balance".equalsIgnoreCase(column)
					|| "Minimum Reserve".equalsIgnoreCase(column) || "Tier".equalsIgnoreCase(column)
					|| "Fill Days".equalsIgnoreCase(column) || "Fill Balance".equalsIgnoreCase(column)) {
				try {
					formattedVal = SwtUtil.formatCurrencyWithoutDecimals(ccyCode, Double.parseDouble(value));
				} catch (NumberFormatException e) {
					log.error("NumberFormatException caught in " + this.getClass().getName()
							+ " - [getColFormattedValue]. Cause: " + e.getMessage());
				} catch (SwtException exp) {
					log.error(this.getClass().getName()
							+ " - SwtException Catched in [getColFormattedValue] method : - " + exp.getMessage());
				}
			} else if ("Start Date".equalsIgnoreCase(column) || "End Date".equalsIgnoreCase(column)) {
				try {
					Date initDate = new SimpleDateFormat("yyyyMMdd").parse(value);
					formattedVal = SwtUtil.formatDate(initDate, dateFormat);
				} catch (ParseException e) {
					log.error("ParseException caught in " + this.getClass().getName()
							+ " - [getColFormattedValue]. Cause: " + e.getMessage());
				}
			} else {
				formattedVal = value;
			}

			return formattedVal;
		}
	
	
	
	
	
		/**
		 * Method to check if the selected record has any maintenance event related in the databaes
		 * 
		 * @param mapping
		 * @param form
		 * @param request
		 * @param response
		 * @return ActionForward
		 * @throws SwtException
		 * 
		 */
		public String checkIfMaintenenanceEventExist() throws SwtException {
HttpServletRequest request = ServletActionContext.getRequest();
HttpServletResponse response = ServletActionContext.getResponse();


			String recordId = null;
			String facilityId = null;
//			Boolean maintenenanceEventExist = false;
			try {

				log.debug(this.getClass().getName() + " - [add] - " + "Entry");

				// Get component ID

				recordId = (String) request.getParameter("recordId");
				facilityId = (String) request.getParameter("facilityId");
				
				
				request.setAttribute("reply_status_ok", "false");
				if(MaintenanceAuthUtils.isRecordRelatedToMaintenanceEvent(recordId,facilityId)) {
					request.setAttribute("reply_message",
							"RECOD_EXIST");
				}
				
				return ("statechange");
				
			} catch (SwtException swtexp) {

				log.error(this.getClass().getName() + " - Exception Catched in [add] method : - " + swtexp.getMessage());
				SwtUtil.logException(swtexp, request, "");

				request.setAttribute("reply_status_ok", "false");
				if (swtexp.getErrorCode().equals(
						"errors.DataIntegrityViolationExceptioninDelete")) {
					request.setAttribute("reply_message",
							"OTHER_RECORD_DEPEND");
				}else {
					request.setAttribute("reply_message",
							"ERROR_DELETE");
				}
				

				return ("statechange");
			} catch (Exception exp) {

				log.error(this.getClass().getName() + " - Exception Catched in [add] method : - " + exp.getMessage());

				SwtUtil.logException(
						SwtErrorHandler.getInstance().handleException(exp, "add", MaintenanceEventMaintenanceAction.class), request,
						"");

				request.setAttribute("reply_status_ok", "false");
				request.setAttribute("reply_message",
						"ERROR_SAVE");
				

				return ("statechange");
			}
		}
	
	
	
	
	
	
	
	
}
