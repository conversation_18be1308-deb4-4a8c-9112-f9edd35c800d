/*
 * @(#)MatchQualityAction.java 1.0 23/08/06
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not 
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.maintenance.web;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Enumeration;
import java.util.Iterator;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;



import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.maintenance.model.CurrencyExchange;
import org.swallow.maintenance.model.MatchParams;
import org.swallow.maintenance.model.MatchParamsQuality;
import org.swallow.maintenance.model.MatchQuality;
import org.swallow.maintenance.model.QualityAction;
import org.swallow.maintenance.service.MatchQualityManager;
import org.swallow.util.CacheManager;
import org.swallow.util.CommonDataManager;
import org.swallow.util.SwtConstants;
import org.swallow.util.SwtUtil;
import org.swallow.util.LabelValueBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.apache.struts2.convention.annotation.Result;
import org.apache.struts2.convention.annotation.AllowedMethods;
import org.apache.struts2.convention.annotation.Action;
import org.apache.struts2.ServletActionContext;
import org.swallow.util.struts.ActionMessage;
import org.swallow.util.struts.ActionMessages;
import org.swallow.util.struts.CustomActionSupport;

/**
 * MatchQualityAction.java.<br>
 * <br>
 * 
 * This main purpose of this class is to match the currency code with the
 * position level for certain entity.<br>
 * 
 * @Modified: Marshal on 31-March-2011
 */
@Action(value = "/matchQuality", results = {
	@Result(name = "fail", location = "/error.jsp"),
	@Result(name = "add", location = "/jsp/maintenance/matchqualityadd.jsp"),
	@Result(name = "success", location = "/jsp/maintenance/matchquamaintenance.jsp"),
	@Result(name = "copyFrom", location = "/jsp/maintenance/copyfrommatchquality.jsp"),
})

@AllowedMethods ({"displayList" ,"change" ,"delete" ,"view" ,"add" ,"save" ,"update" ,"copyFrom" ,"copy" ,"filterPositionlevel" })
public class MatchQualityAction extends CustomActionSupport {
public String execute() throws Exception {
    HttpServletRequest request = ServletActionContext.getRequest();

    // List of methods 
    String method = String.valueOf(request.getParameter("method"));

    switch (method) {
        case "unspecified":
            return unspecified();
        case "displayList":
            return displayList();
        case "change":
            return change();
        case "delete":
            return delete();
        case "view":
            return view();
        case "add":
            return add();
        case "save":
            return save();
        case "update":
            return update();
        case "copyFrom":
            return copyFrom();
        case "copy":
            return copy();
        case "filterPositionlevel":
            return filterPositionlevel();
        default:
            break;
    }

    return unspecified();
}


private MatchQuality matchQuality;
public MatchQuality getMatchQuality() {
	if (matchQuality == null) {
		matchQuality = new MatchQuality();
	}
	return matchQuality;
}
public void setMatchQuality(MatchQuality matchQuality) {
	this.matchQuality = matchQuality;
	HttpServletRequest request = ServletActionContext.getRequest();
	request.setAttribute("matchQuality", matchQuality);
}


 private String matchQualityA;
 
public String getMatchQualityA() {
	return matchQualityA;
}
public void setMatchQualityA(String matchQualityA) {
	this.matchQualityA = matchQualityA;
}

private String matchQualityB;

public String getMatchQualityB() {
	return matchQualityB;
}
public void setMatchQualityB(String matchQualityB) {
	this.matchQualityB = matchQualityB;
}


private String matchQualityC;

public String getMatchQualityC() {
	return matchQualityC;
}
public void setMatchQualityC(String matchQualityC) {
	this.matchQualityC = matchQualityC;
}


private String matchQualityD; 

public String getMatchQualityD() {
	return matchQualityD;
}
public void setMatchQualityD(String matchQualityD) {
	this.matchQualityD = matchQualityD;
}


private String matchQualityE; 


public String getMatchQualityE() {
	return matchQualityE;
}
public void setMatchQualityE(String matchQualityE) {
	this.matchQualityE = matchQualityE;
}


	/**
	 * Initializing logger object for this class
	 */
	private final Log log = LogFactory.getLog(MatchQualityAction.class);

	/**
	 * Used to hold MatchQualityManager reference object
	 */
	@Autowired
	private MatchQualityManager matchMgr = null;

	/**
	 * The MatchQuakityManager to set.
	 * 
	 * @param matchQualityManager
	 */
	public void setMatchQualityManager(MatchQualityManager matchQualityManager) {
		this.matchMgr = matchQualityManager;
	}
	
	
	

	/**
	 * Default method of this action class
	 * 
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return ActionForward
	 * @throws SwtException
	 */
	public String unspecified()
			throws SwtException {
HttpServletRequest request = ServletActionContext.getRequest();
HttpServletResponse response = ServletActionContext.getResponse();

		log.debug(this.getClass().getName()
				+ "-[unspecified]- Returns displaylist method");
		return displayList();
	}

	/**
	 * Method called when entity changed,Load Match Quality Maintenance screen
	 * displays the currency code and position level
	 * 
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return
	 * @throws SwtException
	 */
	public String displayList()
			throws SwtException {
HttpServletRequest request = ServletActionContext.getRequest();
HttpServletResponse response = ServletActionContext.getResponse();


		try {
			log.debug(this.getClass().getName() + " - [displayList] - "
					+ "Entry");

			/* Method's local variable and class instance declaration */
			String entityId;
			String hostId;
			int accessInd;

			MatchQuality matchQuality;
			Collection collMatch;

			/*
			 * Pass the request parameter to set the accessible entity in to the
			 * request attribute
			 */
			putEntityListInReq(request);

			matchQuality = (MatchQuality) getMatchQuality();
			/* get the entity id from matchQuality bean */
			entityId = matchQuality.getId().getEntityId();

			/* Condition to check entity id is null */
			if ((entityId == null) || (entityId.trim().length() <= 0)) {
				/* Get the default entity of the user from SwtUtil */
				entityId = SwtUtil.getUserCurrentEntity(request.getSession());
				/* Set the entityId to the Match quality bean */
				matchQuality.getId().setEntityId(entityId);
			}

			/* Reads the hostId from SwtUtil */
			hostId = SwtUtil.getCurrentHostId();

			/* Retrieve the access id from SwtUtil */
			accessInd = SwtUtil.getMenuEntityCurrGrpAccess(request, entityId,
					null);
			/* Condition to check the accesId is '0' */
			if (accessInd == 0) {
				/* set full access in request attribute */
				request.setAttribute("EntityAccess",
						SwtConstants.ENTITY_FULL_ACCESS + "");
			} else {
				/* Set View access in request attribute */
				request.setAttribute("EntityAccess",
						SwtConstants.ENTITY_READ_ACCESS + "");
			}

			/* collect the MatchList from Manager */
			collMatch = matchMgr.getMatchList(entityId, hostId);

			request.setAttribute("MatchColl", collMatch);
			log.debug(this.getClass().getName() + " - [displayList] - "
					+ "Exit");
			return ("success");
		} catch (SwtException swtexp) {

			log.error(this.getClass().getName()
					+ " - Exception Catched in [displayList] method - "
					+ swtexp.getMessage());

			SwtUtil.logException(swtexp, request, "");

			return ("fail");
		} catch (Exception exp) {

			log.error(this.getClass().getName()
					+ " - Exception Catched in [displayList] method - "
					+ exp.getMessage());

			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "displayList", MatchQualityAction.class), request, "");

			return ("fail");
		}
	}

	/**
	 * This method adds List of currency in Add MatchQuality screen
	 * 
	 * @param request
	 * @param hostId
	 * @param entityId
	 * @return None
	 * @throws SwtException
	 */
	private void addCurrecnyList(HttpServletRequest request, String hostId,
			String entityId) throws SwtException {
		log.debug(this.getClass().getName() + " -[addCurrecnyList] - "
				+ "Entry");

		String roleId = "";
		ArrayList currencyList = null;
		Collection currrencyListWithAll = null;

		roleId = ((CommonDataManager) request.getSession().getAttribute(
				SwtConstants.CDM_BEAN)).getUser().getRoleId();

		currencyList = (ArrayList) SwtUtil.getSwtMaintenanceCache()
				.getCurrencyViewORFullAcessLVL(roleId, entityId);

		if (currencyList != null) {
			currencyList.remove(new LabelValueBean("Default", "*"));
			currrencyListWithAll = new ArrayList();
			currrencyListWithAll.add(new LabelValueBean(
					SwtConstants.CURRENCY_DEFAULT_LABEL, "*"));
			currrencyListWithAll.addAll(currencyList);
		}
		request.setAttribute("currencies", currrencyListWithAll);
		log.debug(this.getClass().getName() + " - [addCurrecnyList] - "
				+ "Exit");

	}

	/**
	 * Method called when change button is clicked in match Quality maintenance
	 * screen. which loads the change Match Quality Screen
	 * 
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return ActionForward
	 * @throws SwtException
	 */
	public String change()
			throws SwtException {
HttpServletRequest request = ServletActionContext.getRequest();
HttpServletResponse response = ServletActionContext.getResponse();

		log.debug(this.getClass().getName() + " - [change] - " + "Entry");

		try {

			/* Method's local variable and class instance declaration */
			String hostId;
			String label;
			String value;
			String entityId;
			String entityName;
			String currencyCode;
			String positionlevel;
			String oldVal;
			String key;

			Integer intPositionlevel;
			MatchQuality matchQuality;
			Collection qualityActionObj;
			Collection collMatchParams;
			Collection qualityActionColl;
			Collection collPos;
			Collection currencyDropDown;
			LabelValueBean lvb;
			MatchQuality.Id matchQualityId;
			MatchQuality collMatchQualities;

			/* Reads the hostId from SwtUtil */
			hostId = SwtUtil.getCurrentHostId();
			
			if(this.matchQuality != null)
				matchQuality = this.matchQuality;
			else
				matchQuality = new MatchQuality();
			
			/* read entity Id from request parameter */
			entityId = request.getParameter("entityId");
			/* Read entity name from request parameter */
			entityName = request.getParameter("entityName");
			/* Read currencyCode from request parameter */
			currencyCode = request.getParameter("selectedCurrencyCode");

			/* Condition to check currency code is not null */
			if (currencyCode != null) {
				/* Trim the currency code */
				currencyCode = currencyCode.trim();
			}

			/* read the position level from request parameter */
			positionlevel = request.getParameter("selectedPostionLevel");

			/*
			 * Condition to check position level is not equal to empty string
			 * and not null
			 */
			if ((positionlevel != null) && !positionlevel.equals("")) {
				/* get the integer value of position level */
				intPositionlevel = Integer.valueOf(positionlevel);

				matchQualityId = new MatchQuality.Id();

				/* Set the host id to the Match quality Id bean */
				matchQualityId.setHostId(hostId);
				/* Set the entity id to the Match quality ID bean */
				matchQualityId.setEntityId(entityId);
				/* Set the currency code to the Match quality Id bean */
				matchQualityId.setCurrencyCode(currencyCode);
				/*
				 * Set the Position level integer value to the Match quality Id
				 * bean
				 */
				matchQualityId.setPosLevel(intPositionlevel);

				/* Set all is fields to match quality bean */
				matchQuality.setId(matchQualityId);

				/*
				 * Pass request , entityId , hostId and position level to set
				 * position level description in request attribute
				 */
				putPosLevelDescInRequest(request, entityId, hostId,
						intPositionlevel);

				setMatchQuality(matchQuality);

				/*
				 * retrieve and store the matchQualityList from
				 * MatchQualityManager to the maatchQuality bean
				 */
				collMatchQualities = matchMgr.getMatchQualityList(hostId,
						entityId, currencyCode, intPositionlevel);

				/* Collects the QualityAction from MatchQuality Manager */
				qualityActionObj = matchMgr.getQualityAction(hostId, entityId,
						currencyCode, intPositionlevel);

				/* Collect parameters description from Match quality manager */
				collMatchParams = matchMgr.getParamsDescAll();

				/* condition to check MatchQuality bean is not null */
				if (collMatchQualities != null) {
					/*
					 * Pass the collection of matchParams, qualityAction,
					 * hostId, entityId , currencyCodeand position level
					 * parameters to convert collection method to make them as a
					 * single collection collMatchParamQuality
					 */
					Collection collMatchParamQuality = convertCollection(
							collMatchParams, qualityActionObj, hostId,
							entityId, currencyCode, intPositionlevel);
					/*
					 * Set the collection collMatchParamQuality to the request
					 * attribute
					 */
					request.setAttribute("collMatchParams",
							collMatchParamQuality);

				} else {
					/*
					 * Instantiate the array list and set the empty list to the
					 * request attribute
					 */
					ArrayList collMatchParamQuality = new ArrayList();
					request.setAttribute("collMatchParams",
							collMatchParamQuality);
				}

				/* Retrieve the collection of Quality from MatchQualityManager */

				CacheManager cacheManagerInst = CacheManager.getInstance();
				qualityActionColl = cacheManagerInst.getMiscParamsLVL(
						SwtConstants.QUALITY_ACTION, entityId);

				/* set the qualityAction collection in the request attribute */
				request.setAttribute("qualityActionColl", qualityActionColl);

				/* Set the Match QualityA of match quality bean to the dyna form */
				setMatchQualityA(collMatchQualities.getMatchQuaA());

				/* Set the Match QualityB of match quality bean to the dyna form */
				setMatchQualityB(collMatchQualities.getMatchQuaB());

				/* Set the Match QualityC of match quality bean to the dyna form */
				setMatchQualityC(collMatchQualities.getMatchQuaC());

				/* Set the Match QualityD of match quality bean to the dyna form */
				setMatchQualityD(collMatchQualities.getMatchQuaD());

				/* Set the Match QualityE of match quality bean to the dyna form */
				setMatchQualityE(collMatchQualities.getMatchQuaE());

				/* collect the String value of match quality A,B,C,D and E */
				oldVal = new StringBuffer("Quality-A=").append(
						matchQuality.getMatchQuaA()).append("^Quality-B=")
						.append(matchQuality.getMatchQuaB()).append(
								"^Quality-C=").append(
								matchQuality.getMatchQuaC()).append(
								"^Quality-D=").append(
								matchQuality.getMatchQuaD()).append(
								"^Quality-E=").append(
								matchQuality.getUpdateDate()).toString();
				request.setAttribute("entityName", entityName);
				request.setAttribute("oldValue", oldVal);
			}

			request.setAttribute("methodName", "change");

			key = SwtConstants.POSITION_LEVEL;

			/* Collects the Position level of from the match manager */

			CacheManager cacheManagerInst = CacheManager.getInstance();
			collPos = cacheManagerInst.getMiscParamsLVL(key, entityId);

			request.setAttribute("position", collPos);
			/* pass request hostId and entityId to add currency List */
			addCurrecnyList(request, hostId, entityId);

			// Code to show currencyName in case of change
			currencyDropDown = (Collection) request.getAttribute("currencies");

			/* Condition to check currency drop down is not null */
			if (currencyDropDown != null) {
				/* Iterate and store the currencyDropDown */
				Iterator itr = currencyDropDown.iterator();

				/* Loop to get the values from iterator */
				while (itr.hasNext()) {
					/* get the values in label value bean */
					lvb = (LabelValueBean) (itr.next());
					/* Collects the currency name */
					label = lvb.getLabel();
					/* get the currency id */
					value = lvb.getValue();

					/* Condition to check value is equal to currency code */
					if (value.equals(currencyCode)) {
						/* set the currency name to the request attribute */
						request.setAttribute("currencyName", label);
						break;
					}
				}
			}
			log.debug(this.getClass().getName() + " - [change] - " + "Exit");
			return ("add");
		} catch (SwtException swtexp) {

			log.error(this.getClass().getName()
					+ " - Exception Catched in [change] method - "
					+ swtexp.getMessage());

			SwtUtil.logException(swtexp, request, "");

			return ("fail");
		} catch (Exception exp) {

			log.error(this.getClass().getName()
					+ " - Exception Catched in [change] method - "
					+ exp.getMessage());

			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "change", MatchQualityAction.class), request, "");

			return ("fail");
		}
	}

	/**
	 * Action method to delete the selected MatchQuality from data base
	 * 
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return ActionForward
	 * @throws SwtException
	 */
	public String delete()
			throws SwtException {
HttpServletRequest request = ServletActionContext.getRequest();
HttpServletResponse response = ServletActionContext.getResponse();


		log.debug(this.getClass().getName() + "-[delete]- Entering");

		/* Method's class instance and local variable declaration */
		String hostId;
		String entityId;
		String currencyCode;
		String positionlevel;
		Integer intPositionlevel;

		MatchQuality matchQuality;
		MatchQuality.Id matchQualityId;
		QualityAction qualityActionObj;
		Collection collMatch;

		/* Reads the hostId from SwtUtil */
		hostId = SwtUtil.getCurrentHostId();

		matchQuality = (MatchQuality) getMatchQuality();

		/* get the entity id from matchQuality bean */
		entityId = matchQuality.getId().getEntityId();
		/* Read the currencyCode from request parameter */
		currencyCode = request.getParameter("selectedCurrencyCode");
		/* Read the positionLevel from request parameter */
		positionlevel = request.getParameter("selectedPostionLevel");
		/* get the integer value of positionLevel */
		intPositionlevel = Integer.valueOf(positionlevel);

		try {
			/* Instantiate matchQualityId bean */
			matchQualityId = new MatchQuality.Id();

			/* set the host id to the matchQualityId bean */
			matchQualityId.setHostId(hostId);
			/* set the entityId to the matchQualityId bean */
			matchQualityId.setEntityId(entityId);
			/* set the currencyCode to the matchQualityId bean */
			matchQualityId.setCurrencyCode(currencyCode);
			/* set the positionLevel to the matchQualityId bean */
			matchQualityId.setPosLevel(intPositionlevel);

			/* set id of match quality bean */
			matchQuality.setId(matchQualityId);

			/* Instantiate QualityAction bean */
			qualityActionObj = new QualityAction();
			/* set the hostId to the QualityAction Bean */
			qualityActionObj.getId().setHostId(hostId);
			/* set the entityId to the QualityAction Bean */
			qualityActionObj.getId().setEntityId(entityId);
			/* set the currencyCode to the QualityAction Bean */
			qualityActionObj.getId().setCurrencyCode(currencyCode);
			/* set the positionLevel to the QualityAction Bean */
			qualityActionObj.getId().setPositionLevel(intPositionlevel);

			/*
			 * Pass the matchQuality bean and Quality action bean with system
			 * info and current system formats to delete method of MatchQuality
			 * manager
			 */
			matchMgr.deleteMatchQuality(matchQuality, qualityActionObj);

			/* Set the matchQuality bean to the dyna-form */
			setMatchQuality(matchQuality);

			/*
			 * Pass the request object to set the accessible entity in to the
			 * request attribute
			 */
			putEntityListInReq(request);

			log.debug(this.getClass().getName() + " - [delete] - " + "Exit");

			return displayList();
		} catch (SwtException swtexp) {

			log.error(this.getClass().getName()
					+ " - Exception Catched in [delete] method - "
					+ swtexp.getMessage());
			request.setAttribute("methodName", "delete");
			request.setAttribute("parentFormRefresh", "");
			putEntityListInReq(request);

			collMatch = matchMgr.getMatchList(entityId, hostId);
			Iterator itr = collMatch.iterator();
			request.setAttribute("MatchColl", collMatch);
			saveErrors(request, SwtUtil.logException(swtexp, request, ""));
			SwtUtil.getMenuEntityCurrGrpAccess(request, entityId, null);

			return ("success");
		} catch (Exception exp) {

			log.error(this.getClass().getName()
					+ " - Exception Catched in [delete] method - "
					+ exp.getMessage());

			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "delete", MatchQualityAction.class), request, "");

			return ("fail");
		}
	}

	/**
	 * Method called when view button is clicked and loads the view screen for
	 * selected match criteria
	 * 
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return ActionForward
	 * @throws SwtException
	 */
	public String view()
			throws SwtException {
HttpServletRequest request = ServletActionContext.getRequest();
HttpServletResponse response = ServletActionContext.getResponse();


		log.debug(this.getClass().getName() + " - [View] - " + "Entry");

		/* Method's local variable and class instance declaration */
		String hostId;
		String label;
		String value;
		String entityId;
		String entityName;
		String currencyCode;
		String positionlevel;
		String key;

		Integer intPositionlevel;
		MatchQuality matchQuality;
		Collection qualityActionObj;
		Collection collMatchParams;
		Collection qualityActionColl;
		Collection collPos;
		Collection currencyDropDown;
		LabelValueBean lvb;
		MatchQuality.Id matchQualityId;
		MatchQuality collMatchQualities;

		try {
			/* Reads the hostId from SwtUtil */
			hostId = SwtUtil.getCurrentHostId();

			matchQuality = (MatchQuality) getMatchQuality();

			/* read entity Id from request parameter */
			entityId = request.getParameter("entityId");
			/* Read entity name from request parameter */
			entityName = request.getParameter("entityName");
			/* Read currencyCode from request parameter */
			currencyCode = request.getParameter("selectedCurrencyCode");

			/* read the positionLevel from request parameter */
			positionlevel = request.getParameter("selectedPostionLevel");

			/*
			 * Condition to check position level is not equal to empty string
			 * and not null
			 */
			if ((positionlevel != null) && !positionlevel.equals("")) {
				/* get the integer value of position level */
				intPositionlevel = Integer.valueOf(positionlevel);

				matchQualityId = new MatchQuality.Id();

				/* Set the host id to the Match quality Id bean */
				matchQualityId.setHostId(hostId);
				/* Set the entity id to the Match quality ID bean */
				matchQualityId.setEntityId(entityId);
				/* Set the currency code to the Match quality Id bean */
				matchQualityId.setCurrencyCode(currencyCode);
				/*
				 * Set the Position level integer value to the Match quality Id
				 * bean
				 */
				matchQualityId.setPosLevel(intPositionlevel);

				/* Set all is fields to match quality bean */
				matchQuality.setId(matchQualityId);

				/*
				 * Pass request , entityId , hostId and position level to set
				 * position level description in request attribute
				 */
				putPosLevelDescInRequest(request, entityId, hostId,
						intPositionlevel);

				setMatchQuality(matchQuality);

				/*
				 * retrieve and store the matchQualityList from
				 * MatchQualityManager to the maatchQuality bean
				 */
				collMatchQualities = matchMgr.getMatchQualityList(hostId,
						entityId, currencyCode, intPositionlevel);

				/* Collects the QualityAction from MatchQuality Manager */
				qualityActionObj = matchMgr.getQualityAction(hostId, entityId,
						currencyCode, intPositionlevel);

				/* Collect parameters description from Match quality manager */
				collMatchParams = matchMgr.getParamsDescAll();

				/*
				 * condition to check collection of MatchQuality bean is not is
				 * null
				 */
				if (collMatchQualities != null) {
					/*
					 * Pass the collection of matchParams, qualityAction,
					 * hostId, entityId , currencyCodeand position level
					 * parameters to convert collection method to make them as a
					 * single collection collMatchParamQuality
					 */
					Collection collMatchParamQuality = convertCollection(
							collMatchParams, qualityActionObj, hostId,
							entityId, currencyCode, intPositionlevel);
					/*
					 * Set the collection collMatchParamQuality to the request
					 * attribute
					 */
					request.setAttribute("collMatchParams",
							collMatchParamQuality);
				} else {
					/*
					 * Instantiate the array list and set the empty list to the
					 * request attribute
					 */
					ArrayList collMatchParamQuality = new ArrayList();
					request.setAttribute("collMatchParams",
							collMatchParamQuality);
				}

				/* Retrieve the collection of Quality from MatchQualityManager */

				CacheManager cacheManagerInst = CacheManager.getInstance();
				qualityActionColl = cacheManagerInst.getMiscParamsLVL(
						SwtConstants.QUALITY_ACTION, entityId);

				/* set the qualityaction collection in the request attribute */
				request.setAttribute("qualityActionColl", qualityActionColl);

				/* Set the Match QualityA of match quality bean to the dyna form */
				setMatchQualityA(collMatchQualities.getMatchQuaA());

				/* Set the Match QualityB of match quality bean to the dyna form */
				setMatchQualityB(collMatchQualities.getMatchQuaB());

				/* Set the Match QualityC of match quality bean to the dyna form */
				setMatchQualityC(collMatchQualities.getMatchQuaC());

				/* Set the Match QualityD of match quality bean to the dyna form */
				setMatchQualityD(collMatchQualities.getMatchQuaD());

				/* Set the Match QualityE of match quality bean to the dyna form */
				setMatchQualityE(collMatchQualities.getMatchQuaE());

				request.setAttribute("methodName", "view");
			}

			key = SwtConstants.POSITION_LEVEL;

			/* Collects the Position level of from the match manager */

			CacheManager cacheManagerInst = CacheManager.getInstance();
			collPos = cacheManagerInst.getMiscParamsLVL(key, entityId);

			request.setAttribute("position", collPos);

			/* pass request, hostId and entityId to add currency List */
			addCurrecnyList(request, hostId, entityId);

			// Code to show currencyName in case of change
			currencyDropDown = (Collection) request.getAttribute("currencies");

			/* Condition to check currency drop down is not null */
			if (currencyDropDown != null) {
				/* Iterate and store the currencyDropDown */
				Iterator itr = currencyDropDown.iterator();

				/* Loop to get the values from iterator */
				while (itr.hasNext()) {
					/* get the values in label value bean */
					lvb = (LabelValueBean) (itr.next());
					/* Collects the currency name */
					label = lvb.getLabel();
					/* get the currency id */
					value = lvb.getValue();

					/* Condition to check value is equal to currency code */
					if (value.equals(currencyCode)) {
						/* set the currency name to the request attribute */
						request.setAttribute("currencyName", label);
						break;
					}
				}
			}

			request.setAttribute("entityName", entityName);
			log.debug(this.getClass().getName() + " - [view] - " + "Exit");

			return ("add");
		} catch (SwtException swtexp) {

			log.error(this.getClass().getName()
					+ " - Exception Catched in [view] method - "
					+ swtexp.getMessage());
			SwtUtil.logException(swtexp, request, "");

			return ("fail");
		} catch (Exception exp) {

			log.error(this.getClass().getName()
					+ " - Exception Catched in [view] method - "
					+ exp.getMessage());

			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "view", MatchQualityAction.class), request, "");

			return ("fail");
		}
	}

	/**
	 * Method returns the QualityAction if the Id fields in the collection are
	 * equal to the Id fields of Quality Action
	 * 
	 * @param coll
	 * @param hostId
	 * @param entityid
	 * @param currencyCode
	 * @param posLevel
	 * @param paramCode
	 * @return QualityAction
	 */
	private QualityAction getMatchQualityObj(Collection coll, String hostId,
			String entityid, String currencyCode, Integer posLevel,
			Integer paramCode) {
		log.debug(this.getClass().getName() + " - [getMatchQualityObj] - "
				+ "Entry");

		/* Method's class instance declaration */
		QualityAction matchQualityObj = null;
		/* Iterate the collection */
		Iterator itr = coll.iterator();

		/* Loop to get each value from iterator */
		while (itr.hasNext()) {
			/* get each value in iterator in QualityAction bean */
			matchQualityObj = (QualityAction) itr.next();

			/*
			 * Condition to check the id fields of collection equal to id fileds
			 * in the parameters
			 */
			if (matchQualityObj.getId().getHostId().equals(hostId)
					&& matchQualityObj.getId().getEntityId().equals(entityid)
					&& matchQualityObj.getId().getCurrencyCode().equals(
							currencyCode)
					&& matchQualityObj.getId().getPositionLevel().equals(
							posLevel)
					&& matchQualityObj.getId().getParameterId().equals(
							paramCode)) {
				return matchQualityObj;
			}
		}
		log.debug(this.getClass().getName() + " - [getMatchQualityObj] - "
				+ "Exit");
		return null;
	}

	/**
	 * This function set the position level name to the request attribute
	 * 
	 * @param request
	 * @return None
	 */
	private void putPosLevelDescInRequest(HttpServletRequest request,
			String entityId, String hostId, Integer posLevel) {
		log.debug(this.getClass().getName()
				+ " - [putPosLevelDescInRequest] - " + "Entry");

		/* Method's class instance and variable declaration */
		Collection collPosLevel;
		String positionName = "";

		/* Collect the position level list from request parameter */
		collPosLevel = (Collection) request.getAttribute("positionLevelList");

		/* Read the position level name passed */
		positionName = SwtUtil.getSwtMaintenanceCache()
				.getEntityPositionLevelName(entityId, posLevel);

		request.setAttribute("posLevelName", positionName);

		log.debug(this.getClass().getName()
				+ " - [putPosLevelDescInRequest] - " + "Exit");
	}

	/**
	 * Method calling in change and view actions to bind the parameter in to the
	 * single collection
	 * 
	 * @param matchParams
	 * @param matchQuality
	 * @param hostId
	 * @param entityId
	 * @param currencyCode
	 * @param posLevel
	 * @return Collection
	 */
	/*
	 * Start:Code Modified for Manits 1997 by chinniah on 16-Jul-2012: Remove
	 * functionality that disable 'Amount Total' checkbox
	 */
	private Collection convertCollection(Collection matchParams,
			Collection matchQuality, String hostId, String entityId,
			String currencyCode, Integer posLevel) throws SwtException {
		log.debug(this.getClass().getName() + " - [convertCollection] - "
				+ "Entry");

		// Object declared to hold the param code,param desc and match quality
		// html
		MatchParamsQuality matchParamsQualityObj = null;
		// Object declared to hold the iterated values of Match Params object
		MatchParams matchParamsObj = null;
		// object to hold the match quality status
		QualityAction matchQulaityObj = null;
		// collection to hold the MatchParamsQuality object
		Collection<MatchParamsQuality> outColl = null;
		// variable used to iterate the match Params
		Iterator<MatchParams> itrMatchParams = null;
		try {
			// Inntialization for Collectionn outColl
			outColl = new ArrayList();

			/* Condition to check matchParams collection is not null */
			if (matchParams != null) {

				/* Iterate the Match parameters */
				itrMatchParams = matchParams.iterator();

				/* Loop to get each value from the iterator */
				while (itrMatchParams.hasNext()) {
					/* Get the value in MatchParams bean */
					matchParamsObj = (MatchParams) itrMatchParams.next();
					// get the Match match Quality object
					matchQulaityObj = getMatchQualityObj(matchQuality, hostId,
							entityId, currencyCode, posLevel, matchParamsObj
									.getId().getParamCode());

					/* Condition to check matchQulaityObj is not null */
					if (matchQulaityObj != null) {
						matchParamsQualityObj = new MatchParamsQuality();
						// Set the paramcCode
						matchParamsQualityObj.setParamCode(matchParamsObj
								.getId().getParamCode());

						// Set the paramcDesc
						matchParamsQualityObj.setParamDesc(matchParamsObj
								.getParamDesc());

						/*
						 * Condition to check whether the MatchQuality A is not
						 * null
						 */
						if (matchQulaityObj.getMatchQualityA() != null) {
							/*
							 * Condition to check whether the MatchchQuality A
							 * is 'Y'
							 */
							if (matchQulaityObj.getMatchQualityA().equals(
									SwtConstants.YES)) {
								/*
								 * Set the check box of MatchQuality A as
								 * checked
								 */
								matchParamsQualityObj
										.setMatchQualityAHTML("checked");
							}
						} else {
							/* Set the check box of MatchQuality A as unchecked */
							matchParamsQualityObj
									.setMatchQualityAHTML("");
						}

						/*
						 * Condition to check whether the MatchQuality B is not
						 * null
						 */
						if (matchQulaityObj.getMatchQualityB() != null) {

							/*
							 * Condition to check whether the MatchchQuality B
							 * is 'Y'
							 */
							if (matchQulaityObj.getMatchQualityB().equals(
									SwtConstants.YES)) {
								/*
								 * Set the check box of MatchQuality B as
								 * checked
								 */
								matchParamsQualityObj
										.setMatchQualityBHTML("checked");
							}
						} else {
							/* Set the check box of MatchQuality B as unchecked */
							matchParamsQualityObj
									.setMatchQualityBHTML("");
						}

						/*
						 * Condition to check whether the MatchQuality C is not
						 * null
						 */
						if (matchQulaityObj.getMatchQualityC() != null) {
							/*
							 * Condition to check whether the MatchchQuality C
							 * is 'Y'
							 */
							if (matchQulaityObj.getMatchQualityC().equals(
									SwtConstants.YES)) {
								/*
								 * Set the check box of MatchQuality C as
								 * checked
								 */
								matchParamsQualityObj
										.setMatchQualityCHTML("checked");
							}
						} else {
							/* Set the check box of MatchQuality C as unchecked */
							matchParamsQualityObj
									.setMatchQualityCHTML("");
						}

						/*
						 * Condition to check whether the MatchQuality D is not
						 * null
						 */
						if (matchQulaityObj.getMatchQualityD() != null) {
							/*
							 * Condition to check whether the MatchchQuality D
							 * is 'Y'
							 */
							if (matchQulaityObj.getMatchQualityD().equals(
									SwtConstants.YES)) {
								/*
								 * Set the check box of MatchQuality D as
								 * checked
								 */
								matchParamsQualityObj
										.setMatchQualityDHTML("checked");
							}
						} else {
							/* Set the check box of MatchQuality D as unchecked */
							matchParamsQualityObj
									.setMatchQualityDHTML("");
						}

						/*
						 * Condition to check whether the MatchQuality E is not
						 * null
						 */
						if (matchQulaityObj.getMatchQualityE() != null) {
							/*
							 * Condition to check whether the MatchchQuality E
							 * is 'Y'
							 */
							if (matchQulaityObj.getMatchQualityE().equals(
									SwtConstants.YES)) {
								/*
								 * Set the check box of MatchQuality E as
								 * checked
								 */
								matchParamsQualityObj
										.setMatchQualityEHTML("checked");
							}
						} else {
							/* Set the check box of MatchQuality E as unchecked */
							matchParamsQualityObj
									.setMatchQualityEHTML("");
						}

						outColl.add(matchParamsQualityObj);
					}
				}
			}
		} catch (Exception e) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [convertCollection] method - "
					+ e.getMessage());
			throw new SwtException();
		} finally {
			// Nullify
			matchParamsQualityObj = null;
			matchParamsObj = null;
			matchQulaityObj = null;
			itrMatchParams = null;
			log.debug(this.getClass().getName() + " - [convertCollection] - "
					+ "Exit");
		}

		return outColl;
	}

	/**
	 * This method is called in Add action method to convert the collection
	 * 
	 * @param matchParams
	 * @return Collection
	 */
	private Collection convertCollection(Collection matchParams)
			throws SwtException {

		// Object declared to hold the param code,param desc and match quality
		// html
		MatchParamsQuality matchParamsQualityObj = null;
		// Object declared to hold the iterated values of Match Params object
		MatchParams matchParamsObj = null;
		// collection to hold the MatchParamsQuality object
		Collection<MatchParamsQuality> outColl = null;
		// variable used to iterate the match Params
		Iterator<MatchParams> itrMatchParams = null;
		try {
			log.debug(this.getClass().getName() + " - [convertCollection] - "
					+ "Entry");

			// outColl Intialization
			outColl = new ArrayList();
			/* Condition to check matchParams collection is not null */
			if (matchParams != null) {
				/* Iterate the Match parameters */
				itrMatchParams = matchParams.iterator();

				/* Loop to get each value from the iterator */
				while (itrMatchParams.hasNext()) {
					/* Get the value in MatchParams bean */
					matchParamsObj = (MatchParams) itrMatchParams.next();
					matchParamsQualityObj = new MatchParamsQuality();

					// Set the paramcCode
					matchParamsQualityObj.setParamCode(matchParamsObj.getId()
							.getParamCode());

					// Set the paramcDesc
					matchParamsQualityObj.setParamDesc(matchParamsObj
							.getParamDesc());

					/* Set the check box for MatchQuality A */
					matchParamsQualityObj
							.setMatchQualityAHTML("<input type='checkbox' />");

					/* Set the check box for MatchQuality B */
					matchParamsQualityObj
							.setMatchQualityBHTML("<input type='checkbox' />");

					/* Set the check box for MatchQuality C */
					matchParamsQualityObj
							.setMatchQualityCHTML("<input type='checkbox' />");

					/* Set the check box for MatchQuality D */
					matchParamsQualityObj
							.setMatchQualityDHTML("<input type='checkbox' />");

					/* Set the check box for MatchQuality E */
					matchParamsQualityObj
							.setMatchQualityEHTML("<input type='checkbox' />");

					outColl.add(matchParamsQualityObj);
				}
			}
		} catch (Exception e) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [convertCollection] method - "
					+ e.getMessage());
			throw new SwtException();
		} finally {
			// Nullify
			matchParamsQualityObj = null;
			matchParamsObj = null;
			itrMatchParams = null;
			log.debug(this.getClass().getName() + " - [convertCollection] - "
					+ "Exit");
		}

		return outColl;
	}

	/*
	 * End:Code Modified for Manits 1997 by chinniah on 16-Jul-2012: Remove
	 * functionality that disable 'Amount Total' checkbox
	 */
	/**
	 * Method called when add button clicked. Loads the add page values
	 * 
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return ActionForward
	 * @throws SwtException
	 */
	public String add()
			throws SwtException {
HttpServletRequest request = ServletActionContext.getRequest();
HttpServletResponse response = ServletActionContext.getResponse();

		try {
			log.debug(this.getClass().getName() + " - [add] - " + "Entry");

			/* Method's class instance and local variable declaration */
			MatchQuality matchQuality;
			Collection collMatchParams;
			Collection qualityActionColl;
			Collection collPositionLvlNames;

			String entityId;
			String entityName;
			String currencyId;
			String hostId;

			/* Reads the hostId from SwtUtil */
			hostId = SwtUtil.getCurrentHostId();

			/*
			 * Pass the request parameter to set the entity access list in to
			 * the request attribute
			 */
			putEntityListInReq(request);


			if(this.matchQuality != null)
				matchQuality = this.matchQuality;
			else
				matchQuality = new MatchQuality();

			/* Read the entityId from request parameter */
			entityId = request.getParameter("entityId");

			/* Read the entityName from request parameter */
			entityName = request.getParameter("entityName");

			/* Read the currencyId from MatchQuality */
			currencyId = matchQuality.getId().getCurrencyCode();

			/* Set the entity to the MatchQuality bean */
			matchQuality.getId().setEntityId(entityId);

			/* Condition to check entityId is not null */
			if ((entityId != null) && (entityId.length() > 0)) {
				/*
				 * pass request hostId and entityId to set the currency list
				 * into the request attribute
				 */
				addCurrecnyList(request, hostId, entityId);
			} else {
				/* Set the empty list to the request attribute */
				request.setAttribute("currencies", new ArrayList());
			}

			/* Collect params description from Match quality manager */
			collMatchParams = matchMgr.getParamsDescAll();
			/*
			 * Pass the collection of matchParams parameter to convert
			 * collection method to make them as a single collection
			 * MatchParamQuality
			 */
			collMatchParams = convertCollection(collMatchParams);

			/* set the collection of MatchParams into the request attribute */
			request.setAttribute("collMatchParams", collMatchParams);

			/* Retrieve the collection of Quality from MatchQualityManager */

			CacheManager cacheManagerInst = CacheManager.getInstance();
			qualityActionColl = cacheManagerInst.getMiscParamsLVL(
					SwtConstants.QUALITY_ACTION, entityId);

			/* Collect the position level names */
			collPositionLvlNames = SwtUtil.getSwtMaintenanceCache()
					.getEntityPositionLevelObjectLVL(entityId);

			request.setAttribute("position", collPositionLvlNames);

			request.setAttribute("qualityActionColl", qualityActionColl);
			request.setAttribute("methodName", "add");
			request.setAttribute("entityName", entityName);
			/*
			 * pass request hostId and entityId to set the currency list into
			 * the request attribute
			 */
			addCurrecnyList(request, hostId, entityId);
			request.setAttribute("currencyName",
					SwtConstants.CURRENCY_DEFAULT_LABEL);
this.matchQuality= matchQuality;
			log.debug(this.getClass().getName() + " - [add] - " + "Exit");
			return ("add");
		} catch (SwtException swtexp) {

			log.error(this.getClass().getName()
					+ " - Exception Catched in [add] method - "
					+ swtexp.getMessage());
			SwtUtil.logException(swtexp, request, "");

			return ("fail");
		} catch (Exception exp) {

			log.error(this.getClass().getName()
					+ " - Exception Catched in [add] method - "
					+ exp.getMessage());

			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "add", MatchQualityAction.class), request, "");

			return ("fail");
		}
	}

	/**
	 * Method called in save action and update action Returns a string value'Y'
	 * if the parameter is 'on'
	 * 
	 * @param inValue
	 * @return String
	 */
	private String checkMatchQuality(String inValue) {
		String outVal = "";

		if ((inValue != null) && inValue.equalsIgnoreCase("on")) {
			outVal = "Y";
		}

		return outVal;
	}

	/**
	 * Method called when save button in add match quality is clicked. save the
	 * Match Quality values in database
	 * 
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return ActionForward
	 * @throws SwtException
	 */
	public String save()
			throws SwtException {
HttpServletRequest request = ServletActionContext.getRequest();
HttpServletResponse response = ServletActionContext.getResponse();

		/* Method's local variable and class instance declaration */
		// DynaValidatorForm dyForm = null;
		MatchQuality matchQuality = null;
		ArrayList matchQualityColl = null;
		Enumeration coll = null;
		MatchQuality matchQualityObj = null;
		QualityAction qualityActionObj = null;
		ActionMessages errors = null;

		Integer intParamCode = null;
		String entityId = null;
		String currencyCode = null;
		Integer posLevel = null;
		String hostId = null;
		int paramCodeIndex = 0;

		String quality1 = null;
		String quality2 = null;
		String quality3 = null;
		String quality4 = null;
		String quality5 = null;
		String paramName = null;
		String paramValue = null;
		String paramCode = null;
		String matchQualityA = null;
		String matchQualityB = null;
		String matchQualityC = null;
		String matchQualityD = null;
		String matchQualityE = null;
		try {
			log.debug(this.getClass().getName() + " - [save] - " + "Entry");
			matchQuality = (MatchQuality) getMatchQuality();
			/* Read the entityId from matchQuality bean */
			entityId = matchQuality.getId().getEntityId();
			/* Read the currencyCode from matchQuality bean */
			currencyCode = matchQuality.getId().getCurrencyCode();
			/* Read position level from MatchQuality bean */
			posLevel = matchQuality.getId().getPosLevel();

			/* Reads the hostId from SwtUtil */
			hostId = SwtUtil.getCurrentHostId();
			quality1 = "N";
			quality2 = "N";
			quality3 = "N";
			quality4 = "N";
			quality5 = "N";
			matchQualityColl = new ArrayList();
			errors = new ActionMessages();
			/* Get the parameterNames in the enumerator */
			coll = request.getParameterNames();

			/* Loop the enumerator to get each value */
			while (coll.hasMoreElements()) {
				/* Read the paramNaem from enumerator element */
				paramName = (String) (coll.nextElement());
				/* Read the parameter value from request parameter */
				paramValue = request.getParameter(paramName);
			}

			/* Infinite loop */
			while (true) {
				/* Read the parameter code from request parameter */
				paramCode = request.getParameter("paramCode_" + paramCodeIndex);

				/* Condition to check parameter code is null */
				if (paramCode == null) {
					/* Break the loop */
					break;
				}

				/* Get the integer value of the paramCode */
				intParamCode = Integer.valueOf(paramCode);

				/* Collects the value of parameter code for quality standard A */
				quality1 = checkMatchQuality(request
						.getParameter("matchquality_" + paramCode + "_" + 1));
				/* Collects the value of parameter code for quality standard B */
				quality2 = checkMatchQuality(request
						.getParameter("matchquality_" + paramCode + "_" + 2));
				/* Collects the value of parameter code for quality standard C */
				quality3 = checkMatchQuality(request
						.getParameter("matchquality_" + paramCode + "_" + 3));
				/* Collects the value of parameter code for quality standard D */
				quality4 = checkMatchQuality(request
						.getParameter("matchquality_" + paramCode + "_" + 4));
				/* Collects the value of parameter code for quality standard E */
				quality5 = checkMatchQuality(request
						.getParameter("matchquality_" + paramCode + "_" + 5));

				qualityActionObj = new QualityAction();

				/* Set the entityId to the qualityAction Id bean */
				qualityActionObj.getId().setEntityId(entityId);

				/* Set the hostId to the qualityActionId bean */
				qualityActionObj.getId().setHostId(hostId);

				/* Set the currencyCode to the qualityAction Id bean */
				qualityActionObj.getId().setCurrencyCode(currencyCode);

				/* Set the ParameterId to the qualityAction Id bean */
				qualityActionObj.getId().setParameterId(intParamCode);

				/* Set the positionLevel to the qualityAction Id bean */
				qualityActionObj.getId().setPositionLevel(posLevel);

				/* Set quality1 MatchQualityA of qualityAction bean */
				qualityActionObj.setMatchQualityA(quality1);

				/* Set quality2 MatchQualityB of qualityAction bean */
				qualityActionObj.setMatchQualityB(quality2);

				/* Set quality3 MatchQualityC of qualityAction bean */
				qualityActionObj.setMatchQualityC(quality3);

				/* Set quality4 MatchQualityD of qualityAction bean */
				qualityActionObj.setMatchQualityD(quality4);

				/* Set quality5 MatchQualityE of qualityAction bean */
				qualityActionObj.setMatchQualityE(quality5);

				matchQualityColl.add(qualityActionObj);
				++paramCodeIndex;
			}

			// Getting the values for quality action
			/* Collect the match Quality A through dyna form */
			matchQualityA = (String) (getMatchQualityA());
			/* Collect the match Quality B through dyna form */
			matchQualityB = (String) (getMatchQualityB());
			/* Collect the match Quality C through dyna form */
			matchQualityC = (String) (getMatchQualityC());
			/* Collect the match Quality D through dyna form */
			matchQualityD = (String) (getMatchQualityD());
			/* Collect the match Quality E through dyna form */
			matchQualityE = (String) (getMatchQualityE());

			// Creating the Match quality object
			matchQualityObj = new MatchQuality();

			/* Set the hostId to the Match quality Id bean */
			matchQualityObj.getId().setHostId(hostId);

			/* Set the entityId to the Match quality Id bean */
			matchQualityObj.getId().setEntityId(entityId);

			/* Set the currencyCode to the Match quality Id bean */
			matchQualityObj.getId().setCurrencyCode(currencyCode);

			/* Set the PositionLevel to the Match quality Id bean */
			matchQualityObj.getId().setPosLevel(posLevel);

			/* Set MatchQuaA in matchQuality bean with matchQualityA String */
			matchQualityObj.setMatchQuaA(matchQualityA);

			/* Set MatchQuaB in matchQuality bean with matchQualityB String */
			matchQualityObj.setMatchQuaB(matchQualityB);

			/* Set MatchQuaC in matchQuality bean with matchQualityC String */
			matchQualityObj.setMatchQuaC(matchQualityC);

			/* Set MatchQuaD in matchQuality bean with matchQualityD String */
			matchQualityObj.setMatchQuaD(matchQualityD);

			/* Set MatchQuaE in matchQuality bean with matchQualityE String */
			matchQualityObj.setMatchQuaE(matchQualityE);

			/*
			 * pass the matchQuality collection and matchQuality bean to the add
			 * match list method of matchQuality Manager
			 */
			matchMgr.addMatchList(matchQualityColl, matchQualityObj);

			request.setAttribute("parentFormRefresh", "yes");

			request.setAttribute("entities", new ArrayList());
			request.setAttribute("currencies", new ArrayList());
			request.setAttribute("position", new ArrayList());
			request.setAttribute("qualityActionColl", new ArrayList());
			request.setAttribute("collMatchParams", new ArrayList());

			log.debug(this.getClass().getName() + " - [save] - " + "Exit");
			return ("add");
		} catch (SwtException swtexp) {

			log.error(this.getClass().getName()
					+ " - Exception Catched in [save] method - "
					+ swtexp.getMessage());
			request.setAttribute("methodName", "add");
			request.setAttribute("parentFormRefresh", "");
			putEntityListInReq(request);

			if ((entityId != null) && (entityId.length() > 0)) {
				addCurrecnyList(request, hostId, entityId);
			} else {
				request.setAttribute("currencies", new ArrayList());
			}

			Collection collMatchParams = matchMgr.getParamsDescAll();
			collMatchParams = convertCollection(collMatchParams);
			request.setAttribute("collMatchParams", collMatchParams);

			CacheManager cacheManagerInst = CacheManager.getInstance();
			Collection qualityActionColl = cacheManagerInst.getMiscParamsLVL(
					SwtConstants.QUALITY_ACTION, entityId);

			request.setAttribute("qualityActionColl", qualityActionColl);

			coll = request.getParameterNames();

			while (coll.hasMoreElements()) {
				paramName = (String) (coll.nextElement());
				paramValue = request.getParameter(paramName);

			}

			while (true) {

				paramCode = request.getParameter("paramCode_" + paramCodeIndex);

				if (paramCode == null) {
					break;
				}

				intParamCode = null;

				intParamCode = Integer.valueOf(paramCode);

				quality1 = checkMatchQuality(request
						.getParameter("matchquality_" + paramCode + "_" + 1));
				quality2 = checkMatchQuality(request
						.getParameter("matchquality_" + paramCode + "_" + 2));
				quality3 = checkMatchQuality(request
						.getParameter("matchquality_" + paramCode + "_" + 3));
				quality4 = checkMatchQuality(request
						.getParameter("matchquality_" + paramCode + "_" + 4));
				quality5 = checkMatchQuality(request
						.getParameter("matchquality_" + paramCode + "_" + 5));

				qualityActionObj = new QualityAction();

				/* Set the entityId to the Match quality Id bean */
				qualityActionObj.getId().setEntityId(entityId);
				/* Set the hostId to the Match quality Id bean */
				qualityActionObj.getId().setHostId(hostId);
				/* Set the currencyCode to the Match quality Id bean */
				qualityActionObj.getId().setCurrencyCode(currencyCode);
				/* Set the ParameterId to the Match quality Id bean */
				qualityActionObj.getId().setParameterId(intParamCode);
				/* Set the positionLevel to the Match quality Id bean */
				qualityActionObj.getId().setPositionLevel(posLevel);

				qualityActionObj.setMatchQualityA(quality1);
				qualityActionObj.setMatchQualityB(quality2);
				qualityActionObj.setMatchQualityC(quality3);
				qualityActionObj.setMatchQualityD(quality4);
				qualityActionObj.setMatchQualityE(quality5);

				matchQualityColl.add(qualityActionObj);
				++paramCodeIndex;
			}
			// This function call retains the checkbox status
			converttoCollection(matchQualityColl, collMatchParams);

			Collection collPositionLvlNames = SwtUtil.getSwtMaintenanceCache()
					.getEntityPositionLevelObjectLVL(entityId);
			request.setAttribute("position", collPositionLvlNames);
			request.setAttribute("methodName", "add");
			request.setAttribute("entityName", request
					.getParameter("entityName"));

			if (swtexp.getErrorCode().equals(
					"errors.DataIntegrityViolationExceptioninAdd")) {
				if (errors != null) {
					errors.add("", new ActionMessage(swtexp.getErrorCode()));
				}
				saveErrors(request, errors);

			} else {
				saveErrors(request, SwtUtil.logException(swtexp, request, ""));
			}

			return ("add");
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [save] method - "
					+ exp.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "save", MatchQualityAction.class), request, "");

			return ("fail");
		} finally {
			paramValue = null;
			paramName = null;
			paramCode = null;
			errors = null;
		}
	}

	/**
	 * Method calls when click on save button in change , update the changed
	 * match quality to the database
	 * 
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return ActionForward
	 * @throws SwtException
	 */
	public String update()
			throws SwtException {
HttpServletRequest request = ServletActionContext.getRequest();
HttpServletResponse response = ServletActionContext.getResponse();

		log.debug(this.getClass().getName() + " - [save] - " + "Exit");

		/* Method's local variable and class instance declaration */
		MatchQuality matchQuality;
		ArrayList matchQualityColl;
		Enumeration coll;
		MatchQuality matchQualityObj;
		QualityAction qualityActionObj;

		String userId;
		Integer intParamCode = null;
		String entityId;
		String currencyCode;
		Integer posLevel;
		String hostId;
		int paramCodeIndex;
		String quality1;
		String quality2;
		String quality3;
		String quality4;
		String quality5;
		String paramName;
		String paramValue;
		String paramCode;
		String matchQualityA;
		String matchQualityB;
		String matchQualityC;
		String matchQualityD;
		String matchQualityE;
		String oldValue;
		String newValue;

		matchQuality = (MatchQuality) getMatchQuality();

		/* Read the entityId from matchQuality bean */
		entityId = matchQuality.getId().getEntityId();
		/* Read the currencyCode from matchQuality bean */
		currencyCode = matchQuality.getId().getCurrencyCode();
		/* Read position level from MatchQuality bean */
		posLevel = matchQuality.getId().getPosLevel();

		/* Reads the hostId from SwtUtil */
		hostId = SwtUtil.getCurrentHostId();

		paramCodeIndex = 0;

		quality1 = "N";
		quality2 = "N";
		quality3 = "N";
		quality4 = "N";
		quality5 = "N";
		matchQualityColl = new ArrayList();

		try {
			/* Get the parameterNames in the enumerator */
			coll = request.getParameterNames();

			/* Loop the enumerator to get each value */
			while (coll.hasMoreElements()) {
				/* Read the paramNaem from enumerator element */
				paramName = (String) (coll.nextElement());
				/* Read the parameter value from request parameter */
				paramValue = request.getParameter(paramName);
			}
			/* Infinite loop */
			while (true) {
				/* Read the parameter code from request parameter */
				paramCode = request.getParameter("paramCode_" + paramCodeIndex);

				/* Condition to check parameter code is null */
				if (paramCode == null) {
					/* Break the loop */
					break;
				}

				/* Get the integer value of the paramCode */
				intParamCode = Integer.valueOf(paramCode);

				/* Collects the value of parameter code for quality standard A */
				quality1 = checkMatchQuality(request
						.getParameter("matchquality_" + paramCode + "_" + 1));
				/* Collects the value of parameter code for quality standard B */
				quality2 = checkMatchQuality(request
						.getParameter("matchquality_" + paramCode + "_" + 2));
				/* Collects the value of parameter code for quality standard C */
				quality3 = checkMatchQuality(request
						.getParameter("matchquality_" + paramCode + "_" + 3));
				/* Collects the value of parameter code for quality standard D */
				quality4 = checkMatchQuality(request
						.getParameter("matchquality_" + paramCode + "_" + 4));
				/* Collects the value of parameter code for quality standard E */
				quality5 = checkMatchQuality(request
						.getParameter("matchquality_" + paramCode + "_" + 5));

				qualityActionObj = new QualityAction();

				/* Set the entityId to the qualityAction Id bean */
				qualityActionObj.getId().setEntityId(entityId);

				/* Set the hostId to the qualityActionId bean */
				qualityActionObj.getId().setHostId(hostId);

				/* Set the currencyCode to the qualityAction Id bean */
				qualityActionObj.getId().setCurrencyCode(currencyCode);

				/* Set the ParameterId to the qualityAction Id bean */
				qualityActionObj.getId().setParameterId(intParamCode);

				/* Set the positionLevel to the qualityAction Id bean */
				qualityActionObj.getId().setPositionLevel(posLevel);

				/* Set quality1 MatchQualityA of qualityAction bean */
				qualityActionObj.setMatchQualityA(quality1);

				/* Set quality2 MatchQualityB of qualityAction bean */
				qualityActionObj.setMatchQualityB(quality2);

				/* Set quality3 MatchQualityC of qualityAction bean */
				qualityActionObj.setMatchQualityC(quality3);

				/* Set quality4 MatchQualityD of qualityAction bean */
				qualityActionObj.setMatchQualityD(quality4);

				/* Set quality5 MatchQualityE of qualityAction bean */
				qualityActionObj.setMatchQualityE(quality5);

				matchQualityColl.add(qualityActionObj);
				++paramCodeIndex;
			}

			// Getting the values for quality action
			/* Collect the match Quality A through dyna form */
			matchQualityA = (String) (getMatchQualityA());
			/* Collect the match Quality B through dyna form */
			matchQualityB = (String) (getMatchQualityB());
			/* Collect the match Quality C through dyna form */
			matchQualityC = (String) (getMatchQualityC());
			/* Collect the match Quality D through dyna form */
			matchQualityD = (String) (getMatchQualityD());
			/* Collect the match Quality E through dyna form */
			matchQualityE = (String) (getMatchQualityE());

			matchQualityObj = new MatchQuality();

			/* Set the hostId to the Match quality Id bean */
			matchQualityObj.getId().setHostId(hostId);

			/* Set the entityId to the Match quality Id bean */
			matchQualityObj.getId().setEntityId(entityId);

			/* Set the currencyCode to the Match quality Id bean */
			matchQualityObj.getId().setCurrencyCode(currencyCode);

			/* Set the PositionLevel to the Match quality Id bean */
			matchQualityObj.getId().setPosLevel(posLevel);

			/* Set MatchQuaA in matchQuality bean with matchQualityA String */
			matchQualityObj.setMatchQuaA(matchQualityA);

			/* Set MatchQuaB in matchQuality bean with matchQualityB String */
			matchQualityObj.setMatchQuaB(matchQualityB);

			/* Set MatchQuaC in matchQuality bean with matchQualityC String */
			matchQualityObj.setMatchQuaC(matchQualityC);

			/* Set MatchQuaD in matchQuality bean with matchQualityD String */
			matchQualityObj.setMatchQuaD(matchQualityD);

			/* Set MatchQuaE in matchQuality bean with matchQualityE String */
			matchQualityObj.setMatchQuaE(matchQualityE);

			/* Read the oldValue from request parameter */
			oldValue = request.getParameter("oldValue");

			newValue = new StringBuffer("Quality-A=").append(
					matchQuality.getMatchQuaA()).append("^Quality-B=").append(
					matchQuality.getMatchQuaB()).append("^Quality-C=").append(
					matchQuality.getMatchQuaC()).append("^Quality-D=").append(
					matchQuality.getMatchQuaD()).append("^Quality-E=").append(
					matchQuality.getUpdateDate()).toString();

			/* Collect the user Id from SwtUtil */
			userId = SwtUtil.getCurrentUserId(request.getSession());
			/* Set the Update User of MatchQuality */
			matchQualityObj.setUpdateUser(userId);
			/* set the hostId to the MatchQuality bean */
			matchQualityObj.getId().setHostId(
					SwtUtil.getCurrentHostId(request.getSession()));

			/*
			 * Pass the match Quality collection, matchQuality bean and to the
			 * update method of matchQuality manager
			 */
			matchMgr.updateMatchList(matchQualityColl, matchQualityObj);

			request.setAttribute("parentFormRefresh", "yes");

			request.setAttribute("entities", new ArrayList());
			request.setAttribute("currencies", new ArrayList());
			request.setAttribute("position", new ArrayList());
			request.setAttribute("qualityActionColl", new ArrayList());
			request.setAttribute("collMatchParams", new ArrayList());
			/* set the currency list to the request attribute */
			addCurrecnyList(request, hostId, entityId);
			log.debug(this.getClass().getName() + " - [update] - " + "Exit");

			return ("add");
		} catch (SwtException swtexp) {

			log.error(this.getClass().getName()
					+ " - Exception Catched in [update] method - "
					+ swtexp.getMessage());
			request.setAttribute("methodName", "add");
			request.setAttribute("parentFormRefresh", "");

			Collection collMatchParams = matchMgr.getParamsDescAll();
			collMatchParams = convertCollection(collMatchParams);
			request.setAttribute("collMatchParams", collMatchParams);

			CacheManager cacheManagerInst = CacheManager.getInstance();
			Collection qualityActionColl = cacheManagerInst.getMiscParamsLVL(
					SwtConstants.QUALITY_ACTION, entityId);

			request.setAttribute("qualityActionColl", qualityActionColl);

			coll = request.getParameterNames();

			while (coll.hasMoreElements()) {
				paramName = (String) (coll.nextElement());
				paramValue = request.getParameter(paramName);

			}

			while (true) {

				paramCode = request.getParameter("paramCode_" + paramCodeIndex);

				if (paramCode == null) {
					break;
				}

				intParamCode = null;

				intParamCode = Integer.valueOf(paramCode);

				quality1 = checkMatchQuality(request
						.getParameter("matchquality_" + paramCode + "_" + 1));
				quality2 = checkMatchQuality(request
						.getParameter("matchquality_" + paramCode + "_" + 2));
				quality3 = checkMatchQuality(request
						.getParameter("matchquality_" + paramCode + "_" + 3));
				quality4 = checkMatchQuality(request
						.getParameter("matchquality_" + paramCode + "_" + 4));
				quality5 = checkMatchQuality(request
						.getParameter("matchquality_" + paramCode + "_" + 5));

				qualityActionObj = new QualityAction();

				qualityActionObj.getId().setEntityId(entityId);
				qualityActionObj.getId().setHostId(hostId);
				qualityActionObj.getId().setCurrencyCode(currencyCode);
				qualityActionObj.getId().setParameterId(intParamCode);
				qualityActionObj.getId().setPositionLevel(posLevel);

				qualityActionObj.setMatchQualityA(quality1);
				qualityActionObj.setMatchQualityB(quality2);
				qualityActionObj.setMatchQualityC(quality3);
				qualityActionObj.setMatchQualityD(quality4);
				qualityActionObj.setMatchQualityE(quality5);

				matchQualityColl.add(qualityActionObj);
				++paramCodeIndex;
			}
			/* This functionn call retains the check box status */
			converttoCollection(matchQualityColl, collMatchParams);
			addCurrecnyList(request, hostId, entityId);
			saveErrors(request, SwtUtil.logException(swtexp, request, ""));

			return ("add");
		} catch (Exception exp) {

			log.error(this.getClass().getName()
					+ " - Exception Catched in [save] method - "
					+ exp.getMessage());

			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "update", MatchQualityAction.class), request, "");

			return ("fail");
		}
	}

	/**
	 * Set the the entity access list to the request Attribute
	 * 
	 * @param request
	 * @return None
	 * @throws SwtException
	 */
	private void putEntityListInReq(HttpServletRequest request)
			throws SwtException {
		log.debug(this.getClass().getName() + " - [putEntityListInReq] - "
				+ "Entry");

		HttpSession session = request.getSession();
		Collection coll = SwtUtil.getUserEntityAccessList(session);
		coll = SwtUtil.convertEntityAcessCollectionLVL(coll, session);
		request.setAttribute("entities", coll);

		log.debug(this.getClass().getName() + " - [putEntityListInReq] - "
				+ "Exit");
	}

	/*
	 * Start:Code Modified for Manits 1997 by chinniah on 16-Jul-2012: Remove
	 * functionality that disable 'Amount Total' checkbox
	 */
	/**
	 * Method to convert the collection , called in save action method
	 * 
	 * @param matchQualityColl
	 * @param matchParamsQualityColl
	 * @return
	 */
	private Collection converttoCollection(
			Collection<QualityAction> matchQualityColl,
			Collection<MatchParams> matchParamsQualityColl) throws SwtException {
		// collection to hold the MatchParamsQuality object
		Collection<MatchParamsQuality> outColl = null;
		// object to hold the match quality status
		QualityAction matchQualityObj = null;
		// Object declared to hold the param code,param desc and match quality
		// html
		MatchParamsQuality matchParamsQualityObj = null;
		// variable used to iterate the matchQualityColl
		Iterator matchQualityitr = null;
		// variable used to iterate the matchParamsQualityColl
		Iterator matchParamsQualityitr = null;
		// Variables declared to run a infinite loop
		int loopCounter1 = 0;
		int loopCounter2 = 0;
		try {
			log.debug(this.getClass().getName() + " - [converttoCollection] - "
					+ "Entry");
			// initialization
			outColl = new ArrayList();

			loopCounter1 = 1;
			loopCounter2 = 0;

			/* Iterate the Match Quality Collection */
			matchQualityitr = matchQualityColl.iterator();

			/* Iterate the MatchParams Quality collection */
			matchParamsQualityitr = matchParamsQualityColl.iterator();

			/* Loop to get the each value from the matchQualityitr */
			while (matchQualityitr.hasNext()) {
				/* get each value as matchQuality bean */
				matchQualityObj = (QualityAction) matchQualityitr.next();

				/* Infinite loop */
				while (loopCounter2 < loopCounter1) {

					matchParamsQualityObj = (MatchParamsQuality) matchParamsQualityitr
							.next();
					/*
					 * Condition to check whether the MatchchQuality A is 'Y'
					 */
					if (matchQualityObj.getMatchQualityA().equals(
							SwtConstants.YES)) {
						/* Set the check box in MatchQuality A as checked */
						matchParamsQualityObj
								.setMatchQualityAHTML("<input type=checkbox checked >");
					} else {
						matchParamsQualityObj.setMatchQualityAHTML(
						/* Set the check box in MatchQuality A as unchecked */
						"<input type=checkbox >");
					}

					/*
					 * Condition to check whether the MatchchQuality B is 'Y'
					 */
					if (matchQualityObj.getMatchQualityB().equals(
							SwtConstants.YES)) {
						/* Set the check box in MatchQuality B as checked */
						matchParamsQualityObj
								.setMatchQualityBHTML("<input type=checkbox checked >");
					} else {
						/* Set the check box in MatchQuality B as unchecked */
						matchParamsQualityObj
								.setMatchQualityBHTML("<input type=checkbox >");
					}

					/*
					 * Condition to check whether the MatchchQuality C is 'Y'
					 */
					if (matchQualityObj.getMatchQualityC().equals(
							SwtConstants.YES)) {
						/* Set the check box in MatchQuality C as checked */
						matchParamsQualityObj
								.setMatchQualityCHTML("<input type=checkbox checked >");
					} else {
						/* Set the check box in MatchQuality C as unchecked */
						matchParamsQualityObj
								.setMatchQualityCHTML("<input type=checkbox >");
					}

					/*
					 * Condition to check whether the MatchchQuality D is 'Y'
					 */
					if (matchQualityObj.getMatchQualityD().equals(
							SwtConstants.YES)) {
						/* Set the check box in MatchQuality D as checked */
						matchParamsQualityObj
								.setMatchQualityDHTML("<input type=checkbox checked >");
					} else {
						/* Set the check box in MatchQuality B as unchecked */
						matchParamsQualityObj
								.setMatchQualityDHTML("<input type=checkbox >");
					}

					/*
					 * Condition to check whether the MatchchQuality E is 'Y'
					 */
					if (matchQualityObj.getMatchQualityE().equals(
							SwtConstants.YES)) {
						/* Set the check box in MatchQuality E as checked */
						matchParamsQualityObj
								.setMatchQualityEHTML("<input type=checkbox checked >");
					} else {
						/* Set the check box in MatchQuality E as unchecked */
						matchParamsQualityObj
								.setMatchQualityEHTML("<input type=checkbox >");
					}

					outColl.add(matchParamsQualityObj);
					loopCounter2++;
				}

				loopCounter1++;
			}

		} catch (Exception exp) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [converttoCollection] method - "
					+ exp.getMessage());
			throw new SwtException();
		} finally {
			// nullify
			matchQualityObj = null;
			matchParamsQualityObj = null;
			matchQualityitr = null;
			matchParamsQualityitr = null;
			log.debug(this.getClass().getName() + " - [converttoCollection] - "
					+ "Exit");
		}

		return outColl;
	}

	/*
	 * End:Code Modified for Manits 1997 by chinniah on 16-Jul-2012: Remove
	 * functionality that disable 'Amount Total' checkbox
	 */
	/**
	 * This method is used to retrieve the currency code(s) based on the
	 * currency access for the corresponding entity).<br>
	 * Also, this method retrieves the position level(s) with respect to the
	 * currency code which already been added.<br>
	 * 
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return ActionForward
	 * @throws SwtException
	 */
	public String copyFrom()
			throws SwtException {
HttpServletRequest request = ServletActionContext.getRequest();
HttpServletResponse response = ServletActionContext.getResponse();

		// Declares the Currency code
		String currCode = null;
		// Declares the Position level
		String posLevel = null;
		// Declares the Host Id
		String hostId = null;
		// Declares the Entity Id
		String entityId = null;
		// Declares the Currency code
		String currencyCode = null;
		// Declares the Role Id
		String roleId = null;
		// Declares the Collection of Position levels
		Collection<LabelValueBean> collPosition = null;
		// Declares the list of Currencies
		ArrayList<LabelValueBean> currencyList = null;
		// Declares the Currency list iterator
		Iterator<LabelValueBean> currListItr = null;
		// Declares the DynaValidatorForm object
		// DynaValidatorForm dynaForm = null;
		// Declares the LabelValueBean object
		LabelValueBean valueBean = null;
		// Declares the MatchQuality object
		MatchQuality matchQuality = null;
		try {
			log.debug(this.getClass().getName() + " - [copyFrom] - " + "Entry");

			// Creates a new instance of currencyList(ArrayList) object
			currencyList = new ArrayList<LabelValueBean>();
			// Casts the ActionForm object with DynaValidationForm
			// Casts explicitly the DynaValidatorForm with MatchQuality object
			matchQuality = (MatchQuality) getMatchQuality();
			// Reads the hostId from SwtUtil
			hostId = SwtUtil.getCurrentHostId();
			// Reads the entity Id from request
			entityId = request.getParameter("entityCode");
			// Reads the entity Id from the request
			request.getSession().setAttribute("entityId", entityId);
			// Sets the entity id to the Match quality ID bean
			matchQuality.getId().setEntityId(entityId);
			// Sets the host id to the Match quality Id bean
			matchQuality.getId().setHostId(hostId);
			// Sets the currency code
			currencyCode = "*";
			// Collects the filter position level from matchQualityManager
			collPosition = matchMgr.getfilterPositionlevel(hostId, entityId,
					currencyCode);
			// Gets the role Id
			roleId = ((CommonDataManager) request.getSession().getAttribute(
					SwtConstants.CDM_BEAN)).getUser().getRoleId();

			// Retrieves the list of currencies based on the entity-currency
			// access (View/Full access)
			currencyList = (ArrayList) SwtUtil.getSwtMaintenanceCache()
					.getCurrencyViewORFullAcessLVL(roleId, entityId);

			// Checks the currencyList for the 'Default' label(*) and if not
			// adding it in the 0th index of the list.
			if (!currencyList.contains(new LabelValueBean(
					SwtConstants.CURRENCY_DEFAULT_LABEL, "*"))) {
				currencyList.add(0, new LabelValueBean(
						SwtConstants.CURRENCY_DEFAULT_LABEL, "*"));
			}

			// Checks whether the currencyList has values
			if (currencyList != null) {
				// Iterates the list of currencies
				currListItr = currencyList.iterator();
				// Condition to check the iterator has values
				if (currListItr.hasNext()) {
					// Get the value from iterator to the label value bean
					valueBean = (LabelValueBean) currListItr.next();
					// Condition to check label is empty
					if (valueBean.getLabel().equals("")) {
						// Removes from iterator
						currListItr.remove();
						// Adds the default currency to the collection
						currencyList.add(0, new LabelValueBean(
								SwtConstants.CURRENCY_DEFAULT_LABEL, "*"));
					}
				}
			}
			
			// Sets the currencyList in the request
			request.setAttribute("currency", currencyList);
			// Calling the method to add the currency list
			addCurrecnyList(request, hostId, entityId);

			// Reads currency code from request parameter
			currCode = request.getParameter("currencyId");
			// Reads Position level from request parameter
			posLevel = request.getParameter("posLevel");

			// Sets the currency code in the request
			request.setAttribute("currencyId", currCode);
			// Sets the position level in the request
			request.setAttribute("posLevel", posLevel);
			// Sets the collection of positions in the request
			request.setAttribute("position", collPosition);

			/*
			 * Pass request parameter to set accessible entity to the request
			 * parameter
			 */
			putEntityListInReq(request);

			// Sets the matchQuality object in the DynaValidatorForm
			setMatchQuality(matchQuality);
			// Sets the entity name in the request
			request.setAttribute("entityName", request
					.getParameter("entityName"));

			log.debug(this.getClass().getName() + " - [copyFrom] - " + "Exit");
			return ("copyFrom");
		} catch (SwtException swtException) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [copyFrom] method - "
					+ swtException.getMessage());
			SwtUtil.logException(swtException, request, "");
			return ("fail");
		} catch (Exception exception) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [copyFrom] method - "
					+ exception.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exception, "copyFrom", MatchQualityAction.class), request,
					"");
			return ("fail");
		} finally {
			// Cleaning the object that are not referenced anymore
			currCode = null;
			posLevel = null;
			hostId = null;
			entityId = null;
			currencyCode = null;
			roleId = null;
		}
	}

	/**
	 * Method called when copy button clicked on copy from screen. copy the
	 * selected match quality in the main screen
	 * 
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return ActionForward
	 * @throws SwtException
	 */
	public String copy()
			throws SwtException {
HttpServletRequest request = ServletActionContext.getRequest();
HttpServletResponse response = ServletActionContext.getResponse();

		try {
			log.debug(this.getClass().getName() + " - [copy] - " + "Entry");

			/* Method's local variable and class instance declaration */
			MatchQuality matchQuality;
			MatchQuality.Id matchQualityId;
			Collection qualityActionColl;
			MatchQuality collMatchQualities;
			Collection qualityActionObj;
			Collection collMatchParams;
			Collection collPos;
			Collection collPositionLvlNames;

			String hostId;
			String entityId;
			String currencyCode;
			String positionlevel;
			Integer intPositionlevel;
			Integer pvousPosLevel;
			String oldVal;
			String key;

			request.setAttribute("screenFieldsStatus", "false");

			matchQuality = (MatchQuality) getMatchQuality();

			/* Reads the hostId from SwtUtil */
			hostId = SwtUtil.getCurrentHostId();

			/* Set the host id to the Match quality bean */
			matchQuality.getId().setHostId(hostId);

			/* read entity Id from request parameter */
			entityId = (String) request.getSession().getAttribute("entityId");

			/* Set the entity id to the Match quality bean */
			matchQuality.getId().setEntityId(entityId);

			/* Read currencyCode from request parameter */
			currencyCode = request.getParameter("selectedCurrencyCode");

			/* read the position level from request parameter */
			positionlevel = request.getParameter("selectedPostionLevel");

			/*
			 * Condition to check position level is not equal to empty string
			 * and not null
			 */
			if ((positionlevel != null) && !positionlevel.equals("")) {
				/* get the integer value of position level */
				intPositionlevel = Integer.valueOf(positionlevel);

				matchQualityId = new MatchQuality.Id();

				/* Set the host id to the Match quality Id bean */
				matchQualityId.setHostId(hostId);
				/* Set the entity id to the Match quality ID bean */
				matchQualityId.setEntityId(entityId);

				/* Condition to check currencyId is not null */
				if (request.getParameter("currencyId") != null) {
					/* Set the currency code to the Match quality Id bean */
					matchQualityId.setCurrencyCode(request
							.getParameter("currencyId"));
				}

				/* Condition to check position level is not null */
				if (request.getParameter("posLevel") != null) {
					/* Condition to check position level is not empty */
					if (!request.getParameter("posLevel").equals("")) {
						/*
						 * Get the previous position level through request
						 * parameter
						 */
						pvousPosLevel = Integer.valueOf(request
								.getParameter("posLevel"));
						/*
						 * Set the prevPosition level integer value to the Match
						 * quality Id bean
						 */
						matchQualityId.setPosLevel(pvousPosLevel);
					} else {
						/*
						 * Set the intPositionlevel integer value to the Match
						 * quality Id bean
						 */
						matchQualityId.setPosLevel(intPositionlevel);
					}
				}

				/* Set all is fields to match quality bean */
				matchQuality.setId(matchQualityId);

				/* Retrieve the collection of Quality from MatchQualityManager */

				CacheManager cacheManagerInst = CacheManager.getInstance();
				qualityActionColl = cacheManagerInst.getMiscParamsLVL(
						SwtConstants.QUALITY_ACTION, entityId);

				/* set the quality action collection in the request attribute */
				request.setAttribute("qualityActionColl", qualityActionColl);

				/*
				 * retrieve and store the matchQualityList from
				 * MatchQualityManager to the maatchQuality bean
				 */
				collMatchQualities = matchMgr.getMatchQualityList(hostId,
						entityId, currencyCode, intPositionlevel);

				/* Collects the QualityAction from MatchQuality Manager */
				qualityActionObj = matchMgr.getQualityAction(hostId, entityId,
						currencyCode, intPositionlevel);

				/* Collect params description from Match quality manager */
				collMatchParams = matchMgr.getParamsDescAll();

				/*
				 * condition to check collection of MatchQuality bean is not is
				 * null
				 */
				if ((collMatchQualities != null) && (collMatchParams != null)) {
					/*
					 * Pass the collection of matchParams, qualityAction,
					 * hostId, entityId , currencyCodeand position level
					 * parameters to convert collection method to make them as a
					 * single collection collMatchParamQuality
					 */
					Collection collMatchParamQuality = convertCollection(
							collMatchParams, qualityActionObj, hostId,
							entityId, currencyCode, intPositionlevel);

					/*
					 * Set the collection collMatchParamQuality to the request
					 * attribute
					 */
					request.setAttribute("collMatchParams",
							collMatchParamQuality);
				} else {
					/*
					 * Instantiate the array list and set the empty list to the
					 * request attribute
					 */
					ArrayList collMatchParamQuality = new ArrayList();
					request.setAttribute("collMatchParams",
							collMatchParamQuality);
				}

				/* Set the Match QualityA of match quality bean to the dyna form */
				 setMatchQualityA(collMatchQualities.getMatchQuaA());

				/* Set the Match QualityB of match quality bean to the dyna form */
				setMatchQualityB(collMatchQualities.getMatchQuaB());

				/* Set the Match QualityC of match quality bean to the dyna form */
				setMatchQualityC(collMatchQualities.getMatchQuaC());

				/* Set the Match QualityD of match quality bean to the dyna form */
				setMatchQualityD(collMatchQualities.getMatchQuaD());

				/* Set the Match QualityE of match quality bean to the dyna form */
				setMatchQualityE(collMatchQualities.getMatchQuaE());

				/* collect the String value of match quality A,B,C,D and E */
				oldVal = new StringBuffer("Quality-A=").append(
						matchQuality.getMatchQuaA()).append("^Quality-B=")
						.append(matchQuality.getMatchQuaB()).append(
								"^Quality-C=").append(
								matchQuality.getMatchQuaC()).append(
								"^Quality-D=").append(
								matchQuality.getMatchQuaD()).append(
								"^Quality-E=").append(
								matchQuality.getUpdateDate()).toString();

				request.setAttribute("oldValue", oldVal);
			}

			/* Set the currency list into the request attribute */
			addCurrecnyList(request, hostId, entityId);
			setMatchQuality(matchQuality);

			/*
			 * Pass request parameter to set entity access list in request
			 * attribute
			 */
			putEntityListInReq(request);

			key = SwtConstants.POSITION_LEVEL;

			/* Collects the Position level of from the match manager */

			CacheManager cacheManagerInst = CacheManager.getInstance();
			collPos = cacheManagerInst.getMiscParamsLVL(key, entityId);

			request.setAttribute("position", collPos);

			/* Get position level names from swtUtil */
			collPositionLvlNames = SwtUtil.getSwtMaintenanceCache()
					.getEntityPositionLevelObjectLVL(entityId);
			request.setAttribute("position", collPositionLvlNames);

			request.setAttribute("methodName", "add");
			request.setAttribute("entityName", request
					.getParameter("entityName"));

			log.debug(this.getClass().getName() + " - [copy] - " + "Exit");
			return ("add");
		} catch (SwtException swtexp) {

			log.error(this.getClass().getName()
					+ " - Exception Catched in [copy] method - "
					+ swtexp.getMessage());

			SwtUtil.logException(swtexp, request, "");

			return ("fail");
		} catch (Exception exp) {

			log.error(this.getClass().getName()
					+ " - Exception Catched in [copy] method - "
					+ exp.getMessage());

			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "copy", MatchQualityAction.class), request, "");

			return ("fail");
		}
	}

	/**
	 * This action is called in copy from screen when currency code is changed
	 * 
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return ActionForward
	 * @throws SwtException
	 */
	public String filterPositionlevel() throws SwtException {
HttpServletRequest request = ServletActionContext.getRequest();
HttpServletResponse response = ServletActionContext.getResponse();

		// Declares the role Id
		String roleId = null;
		// Declares the host Id
		String hostId = null;
		// Declares the entity Id
		String entityId = null;
		// Declares the currency code
		String currencyCode = null;
		// Declares the LabelValueBean object
		LabelValueBean valBean = null;
		// Declares the Collection object that holds the position levels
		Collection<LabelValueBean> collPos = null;
		// Declares the collection of currency
		ArrayList<LabelValueBean> collCurrency = null;
		// Declares the Iterator object for collPos
		Iterator<LabelValueBean> iterator = null;
		// Declares the DynaValidatorForm object
		// DynaValidatorForm dyForm = null;
		// Declares the MatchQuality object
		MatchQuality matchQuality = null;
		try {

			log.debug(this.getClass().getName() + " - [filterPositionlevel] - "
					+ "Entry");

			// Casting the ActionForm object with DynaValidatorForm object
			// Casting with MatchQuality object
			matchQuality = (MatchQuality) getMatchQuality();

			// Reads the hostId from SwtUtil
			hostId = SwtUtil.getCurrentHostId();
			// Reads the entity from session
			entityId = (String) request.getSession().getAttribute("entityId");
			// Sets the session attribute for entity Id
			request.getSession().setAttribute("entityId", entityId);
			// Gets the role Id
			roleId = ((CommonDataManager) request.getSession().getAttribute(
					SwtConstants.CDM_BEAN)).getUser().getRoleId();
			// Raeds the currencycode from matchQuality bean
			currencyCode = matchQuality.getId().getCurrencyCode();
			// Set the currency code to the match Quality bean
			matchQuality.getId().setCurrencyCode(currencyCode);
			// Sets the entityId to the match Quality bean
			matchQuality.getId().setEntityId(entityId);
			// Sets the hostId to the match Quality bean
			matchQuality.getId().setHostId(hostId);

			// Gets the list of currency(s) were the given entity has access
			collCurrency = (ArrayList) SwtUtil.getSwtMaintenanceCache()
					.getCurrencyViewORFullAcessLVL(roleId, entityId);

			// Checks the currencyList for the 'Default' label(*) and if not
			// adding it in the 0th index of the list.
			if (!collCurrency.contains(new LabelValueBean(
					SwtConstants.CURRENCY_DEFAULT_LABEL, "*"))) {
				collCurrency.add(0, new LabelValueBean(
						SwtConstants.CURRENCY_DEFAULT_LABEL, "*"));
			}

			// Iterates the currency collection
			iterator = collCurrency.iterator();

			// Condition to check iterator has next value
			if (iterator.hasNext()) {
				// Gets the label value from iterator
				valBean = (LabelValueBean) iterator.next();

				// Condition to check value bean is empty
				if (valBean.getLabel().equals("")) {
					// Removes from iterator
					iterator.remove();
					// Sets the default currency to the collection
					collCurrency.add(0, new LabelValueBean(
							SwtConstants.CURRENCY_DEFAULT_LABEL, "*"));
				}
			}
			// Sets the collCurrency in the request
			request.setAttribute("currency", collCurrency);

			// Gets the filter position level from manager
			collPos = matchMgr.getfilterPositionlevel(hostId, entityId,
					currencyCode);

			// condition to check collection id greater or equal to 2
			if (collPos.size() >= 2) {
				request.setAttribute("position", collPos);
			} else {
				request.setAttribute("position", new ArrayList());
				request.setAttribute("methodName", "Hide");
			}

			// Sets the currency Id in the request
			request.setAttribute("currencyId", request
					.getParameter("currencyId"));
			// Sets the position level(s) in the request
			request.setAttribute("posLevel", request.getParameter("posLevel"));

			log.debug(this.getClass().getName() + " - [filterPositionlevel] - "
					+ "Exit");
			return ("copyFrom");
		} catch (SwtException swtexp) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [filterPositionlevel] method - "
					+ swtexp.getMessage());
			SwtUtil.logException(swtexp, request, "");

			return ("fail");
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [filterPositionlevel] method - "
					+ exp.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "filterPositionlevel", MatchQualityAction.class),
					request, "");

			return ("fail");
		}
	}
}
