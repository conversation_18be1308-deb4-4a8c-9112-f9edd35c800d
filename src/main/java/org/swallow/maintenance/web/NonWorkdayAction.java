/*
 * @(#)NonWorkdayAction.java 1.0 28/02/2010
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.maintenance.web;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.Iterator;
import java.util.List;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.apache.struts2.ServletActionContext;
import org.apache.struts2.convention.annotation.Action;
import org.apache.struts2.convention.annotation.AllowedMethods;
import org.apache.struts2.convention.annotation.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.maintenance.model.EntityUserAccess;
import org.swallow.maintenance.model.Holiday;
import org.swallow.maintenance.model.NonWorkday;
import org.swallow.maintenance.service.NonWorkdayManager;
import org.swallow.util.LabelValueBean;
import org.swallow.util.PropertiesFileLoader;
import org.swallow.util.SwtUtil;
import org.swallow.util.struts.ActionMessage;
import org.swallow.util.struts.ActionMessages;
import org.swallow.util.struts.CustomActionSupport;

/**
 * <AUTHOR> A
 * 
 * <pre>
 * Action Class Non Work day Maintenance screen
 * - Display Faclity settings
 * - Save / Update Facility settings for country
 * - Delete existing facility setting for country
 * </pre>
 * 
 */


@Action(value = "/nonworkday", results = {
		@Result(name = "fail", location = "/error.jsp"),
		@Result(name = "success", location = "/jsp/maintenance/nonworkday.jsp"),
		@Result(name = "change", location = "/jsp/maintenance/nonworkdaychange.jsp"),
		})

@AllowedMethods ({"showDetails" ,"delete" ,"change" ,"add" ,"save"})

public class NonWorkdayAction extends CustomActionSupport {

	/**
	 * Initializing logger object for this class
	 */
	private final Log log = LogFactory.getLog(NonWorkdayAction.class);

	/**
	 * Initializing NonWorkdayManager object for this class
	 */
	@Autowired
	private NonWorkdayManager nonWorkdayManager = null; 

	/**
	 * This is used to set the nonWorkdayManager
	 * 
	 * @param nonWorkdayManager
	 * @return
	 */
	public void setNonWorkdayManager(NonWorkdayManager nonWorkdayManager) {
		this.nonWorkdayManager = nonWorkdayManager;
	}

	NonWorkday nonWorkday;
	
	public NonWorkday getNonWorkday() {
		return nonWorkday;
	}

	public void setNonWorkday(NonWorkday nonWorkday) {
		this.nonWorkday = nonWorkday;
	}


	String menuAccessId;
	public String getMenuAccessId() {
		return menuAccessId;
	}
	public void setMenuAccessId(String menuAccessId) {
		this.menuAccessId = menuAccessId;
	}
	
	String ismenuItem;
	public String getIsmenuItem() {
		return ismenuItem;
	}
	public void setIsmenuItem(String ismenuItem) {
		this.ismenuItem = ismenuItem;
	}
	
	String menuItemId;
	public String getMenuItemId() {
		return menuItemId;
	}
	public void setMenuItemId(String menuItemId) {
		this.menuItemId= menuItemId;
	} 
	
	public String execute() throws Exception {
	    HttpServletRequest request = ServletActionContext.getRequest();

	    // List of methods 
	    String method = String.valueOf(request.getParameter("method"));

	    switch (method) {
	        case "showDetails":
	            return showDetails();
	        case "delete":
	            return delete();
	        case "change":
	            return change();
	        case "add":
	            return add();
	        case "save":
	            return save();
	        default:
	            break;
	    }

	    return showDetails();
	}
	/**
	 * This method is default method in struts.This method is called only once
	 * at the time of loading.
	 * 
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return ActionForward
	 * @throws SwtException
	 */
	public String unspecified()
			throws SwtException {
		log.debug(this.getClass().getName()
				+ "- [unspecified] - return showDetails method ");
		return "showDetails";
	}

	/**
	 * This method loaded the holiday's details in a screen based on a default
	 * entity or user selected entity.
	 * 
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return ActionForward
	 * @throws SwtException
	 */
	public String showDetails()
			throws SwtException {

		/* Method's local variable declaration */
		// Variable to hold hostId
		String hostId = null;
		// Variable to hold entityId
		String entityId = null;
		// Collection instance to hold facility list
		Collection<NonWorkday> facilityColl = null;
		// NonWorkday Instance
		NonWorkday nonWorkday = null;
		HttpServletRequest request = ServletActionContext.getRequest();

		try {
			log.debug(this.getClass().getName() + " - [showDetails] - Entry");
			
			if(this.nonWorkday != null)
				nonWorkday = this.nonWorkday;
			else
				nonWorkday = new NonWorkday();

			/* Retrieve and store hostId from SwtUtil */
			hostId = SwtUtil.getCurrentHostId();
			/* Read the entity id from bean class */
			entityId = nonWorkday.getId().getEntityId();
			/* Condition to check entity is null or empty */
			if ((entityId == null) || (entityId.trim().length() <= 0)) {
				/* Retrieve the user's default entity from session */
				entityId = SwtUtil.getUserCurrentEntity(request.getSession());
				/* Set the entity id using bean class */
				nonWorkday.getId().setEntityId(entityId);
			}
			/*
			 * Retrieve the facilities from database based on the parameters
			 * passed
			 */
			facilityColl = nonWorkdayManager.getFacilityList(entityId, hostId);

			// set faility list in request
			request.setAttribute("facilityList", facilityColl);
			// set non work day bean
			this.nonWorkday=  nonWorkday;
			/* set entity list in request */
			putEntityListInReq(request);
			/* Retrieve User's Menu,Entity and Currency Group */
			SwtUtil.getMenuEntityCurrGrpAccess(request, entityId, null);
			request.setAttribute("methodName", "showDetails");
			return "success";
		} catch (SwtException swtexp) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [showDetails] method : - "
					+ swtexp.getMessage());
			SwtUtil.logException(swtexp, request, "");
			return "fail";
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [showDetails] method : - "
					+ exp.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "showDetails", NonWorkdayAction.class), request, "");
			return "fail";
		} finally {
			hostId = null;
			entityId = null;
			log.debug(this.getClass().getName() + " - [showDetails] - Exit");

		}
	}

	/**
	 * Method to delete Non working day values
	 * 
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return ActionForward
	 * @throws SwtException
	 */
	public String delete()
			throws SwtException {
		/* Method's local variable declaration */
		// Variable to hold hostId
		String hostId = null;
		// Variable to hold entityId
		String entityId = null;
		// Variable to hold facility
		String facility = null;
		// NonWorkday Instance
		NonWorkday nonWorkday = null;
		HttpServletRequest request = ServletActionContext.getRequest();

		try {
			log.debug(this.getClass().getName() + "- [delete] - Entry");
			// Read host Id from properties
			hostId = SwtUtil.getCurrentHostId();
			// Instantiate NonWorkday bean
			nonWorkday = new NonWorkday();
			// set host Id in bean
			nonWorkday.getId().setHostId(hostId);
			// read entityId from request
			entityId = request.getParameter("entityId");
			// read facility from request
			facility = request.getParameter("facility");
			// set entityId in bean
			nonWorkday.getId().setEntityId(entityId.trim());
			// set facility in bean
			nonWorkday.getId().setFacility(facility.trim());
			// delete selected value
			nonWorkdayManager.save(nonWorkday, "delete");
			// return to showDetails to refresh parent screen
			return showDetails();
		} catch (SwtException swtexp) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [delete] method : - "
					+ swtexp.getMessage());
			SwtUtil.logException(swtexp, request, "");
			return  "fail";
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [delete] method : - "
					+ exp.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "delete", NonWorkdayAction.class), request, "");
			return showDetails();
		} finally {
			log.debug(this.getClass().getName() + "- [delete] - Exit");
			// nullify hostId
			hostId = null;
			// nullifying entityId
			entityId = null;
			// nullifying facility
			facility = null;
			// nullifying NonWorkday bean
			nonWorkday = null;

		}
	}

	/**
	 * Method to load Change screen
	 * 
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return ActionForward
	 * @throws SwtException
	 */
	public String change()
			throws SwtException {

		/* Method's local variable declaration */
		// Variable to hold entityId
		String entityId = null;
		// Variable to hold entityName
		String entityName = null;
		// Variable to hold applEntity
		String applEntity = null;
		// Variable to hold applAccount
		String applAccount = null;
		// Variable to hold applCurrency
		String applCurrency = null;
		// Variable to hold facility
		String facility = null;
		// NonWorkDay Instance
		NonWorkday nonWorkday = null;
		HttpServletRequest request = ServletActionContext.getRequest();


		try {
			log.debug(this.getClass().getName() + "- [change] - Entry");
			// Read entityId from request
			entityId = request.getParameter("entityId");
			// Read entityName from request
			entityName = request.getParameter("EntityName");
			// Read applEntity from request
			applEntity = request.getParameter("applEntity");
			// Read applAccount from request
			applAccount = request.getParameter("applAccount");
			// Read applCurrency from request
			applCurrency = request.getParameter("applCurrency");
			// Read facility from request
			facility = request.getParameter("facility");
			
			if(this.nonWorkday != null)
				nonWorkday = this.nonWorkday;
			else
				nonWorkday = new NonWorkday();
			// Set entityId in bean
			nonWorkday.getId().setEntityId(entityId.trim());
			// Set facility in bean
			nonWorkday.getId().setFacility(facility.trim());
			// Set applAccount in bean
			nonWorkday.setApplyAccountCountry(applAccount.trim());
			// Set applCurrency in bean
			nonWorkday.setApplyCurrencyCountry(applCurrency.trim());
			// Set applEntity in bean
			nonWorkday.setApplyEntityCountry(applEntity.trim());
			// Set non work day bean in action form
			this.nonWorkday= nonWorkday;
			// Set enetityName in request
			request.setAttribute("entityName", entityName);
			// Set true for screen field status in request
			request.setAttribute("screenFieldsStatus", "true");
			// Set update for save status in request
			request.setAttribute("saveStatus", "update");

			return "change";
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [change] method : - "
					+ exp.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "change", this.getClass()), request, "");
			return "fail";
		} finally {
			// Set entityId to null
			entityId = null;
			// Set entityName to null
			entityName = null;
			// SetapplEntity to null
			applEntity = null;
			// Set applAccount to null
			applAccount = null;
			// Set applCurrency to null
			applCurrency = null;
			// Set facility to null
			facility = null;
			log.debug(this.getClass().getName() + " - [change] - Exit");
		}
	}

	/**
	 * Method to load Non Work day Add screen
	 * 
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return ActionForward
	 * @throws SwtException
	 */
	public String add()
			throws SwtException {
		log.debug(this.getClass().getName() + "- [change] - Enter");
		/* Method's local variable declaration */
		// Variable to hold entityId
		String entityId = null;
		// Variable to hold entityId
		String entityName = null;
		// NonWorkday Instance
		NonWorkday nonWorkday = null;
		// Collection Instance to hold facility
		Collection<LabelValueBean> facilitycoll = null;
		// Collection Instance to hold facility
		List<String> facilitycollStr = null;
		// Iterator instance
		Iterator<Object> itr = null;
		// Iterator instance
		Iterator<String> itrLbl = null;
		HttpServletRequest request = ServletActionContext.getRequest();

		try {
			// Read entityId from request
			entityId = request.getParameter("entityId");
			// Read entityName from request
			entityName = request.getParameter("EntityName");
			
			if(this.nonWorkday != null)
				nonWorkday = this.nonWorkday;
			else
				nonWorkday = new NonWorkday();
			
			// Set entityId in bean
			nonWorkday.getId().setEntityId(entityId.trim());
			// Set default value as NO for account in bean
			nonWorkday.setApplyAccountCountry("2");
			// Set default value as No for currency in bean
			nonWorkday.setApplyCurrencyCountry("2");
			// Set default value as YES for entity in bean
			nonWorkday.setApplyEntityCountry("1");

			// Instantiate collection for facility
			facilitycoll = new ArrayList<LabelValueBean>();
			// Add empty values by default
			facilitycoll.add(new LabelValueBean("", ""));
			facilitycollStr = new ArrayList<String>();
			// Read facilities from property files
			itr = PropertiesFileLoader.getInstance().getFacilityMessageValues()
					.iterator();

			// Iterate facilities and store in collection to display in drop
			// down
			while (itr.hasNext()) {
				String proValue = (String) itr.next();
				facilitycollStr.add(proValue);
			}
			Collections.sort(facilitycollStr);

			itrLbl = facilitycollStr.iterator();
			while (itrLbl.hasNext()) {
				String proValue = (String) itrLbl.next();
				facilitycoll.add(new LabelValueBean(proValue, proValue));
			}
			// set facility collection in request
			request.setAttribute("facilitycoll", facilitycoll);
			// set NonWorkday form in Dyna form with default values
			this.nonWorkday= nonWorkday;
			// set entityName in request
			request.setAttribute("entityCode", entityName);
			// set screen field status as empty
			request.setAttribute("screenFieldsStatus", "");
			// set save status as save
			request.setAttribute("saveStatus", "save");

			return "change";
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [add] method : - "
					+ exp.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "add", NonWorkdayAction.class), request, "");
			return "fail";
		} finally {
			log.debug(this.getClass().getName() + " - [add] - " + "Exit");
		}
	}

	/**
	 * Method to save / update / delete Non workday details
	 * 
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return ActionForward
	 * @throws SwtException
	 */
	public String save()
			throws SwtException {
		// Variable to hold saveStatus
		String saveStatus = "";
		// Instance for NonWorkday
		NonWorkday nonWorkday = null;
		// Variable to hold hostId
		String hostId = null;
		// Variable to display the errors in error log file
		ActionMessages errors = null;
		HttpServletRequest request = ServletActionContext.getRequest();

		try {
			log.debug(this.getClass().getName() + " - [save] - Entry");
			errors = new ActionMessages();
			/* Read host Id from properties */
			hostId = SwtUtil.getCurrentHostId();
			
			if(this.nonWorkday != null)
				nonWorkday = this.nonWorkday;
			else
				nonWorkday = new NonWorkday();
			
			// Read save Status from request
			saveStatus = request.getParameter("saveStatus");
			// set host Id in non work day bean
			nonWorkday.getId().setHostId(hostId);

			// Set value for account in bean
			nonWorkday.setApplyAccountCountry(changeStatus(nonWorkday
					.getApplyAccountCountry()));
			// Set value for entity in bean
			nonWorkday.setApplyEntityCountry(changeStatus(nonWorkday
					.getApplyEntityCountry()));
			// Set value for currency in bean
			nonWorkday.setApplyCurrencyCountry(changeStatus(nonWorkday
					.getApplyCurrencyCountry()));
			// Condition to check save status is not null and not empty
			if (!saveStatus.trim().equals("")) {
				// call manager layer to save / update / delete NonWorkday
				// values
				nonWorkdayManager.save(nonWorkday, saveStatus);
			}

			// set true for screen field status
			request.setAttribute("screenFieldsStatus", "true");
			// Refresh parent form after save / update / delete action
			request.setAttribute("parentFormRefresh", "yes");
			return "change";
		} catch (SwtException swtexp) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [save] method : - "
					+ swtexp.getMessage());

			// read entity name from request
			String entityName = request.getParameter("entityCode");
			if(this.nonWorkday != null)
				nonWorkday = this.nonWorkday;
			else
				nonWorkday = new NonWorkday();
			// Condition to check Account values is empty
			if (nonWorkday.getApplyAccountCountry().equals("")) {
				// set No by default for account
				nonWorkday.setApplyAccountCountry("2");
				// set No by default for currency
				nonWorkday.setApplyCurrencyCountry("2");
				// set YES by default for entity
				nonWorkday.setApplyEntityCountry("1");
			} else {
				// Set the previous user selected values for account, currency
				// and entity
				nonWorkday.setApplyAccountCountry(changeStatusNum(nonWorkday
						.getApplyAccountCountry()));
				nonWorkday.setApplyEntityCountry(changeStatusNum(nonWorkday
						.getApplyEntityCountry()));
				nonWorkday.setApplyCurrencyCountry(changeStatusNum(nonWorkday
						.getApplyCurrencyCountry()));
			}
			// Read facilities from proeprties to set in drop down box
			Collection<LabelValueBean> facilitycoll = new ArrayList<LabelValueBean>();
			List<String> facilitycollStr = new ArrayList<String>();

			facilitycoll.add(new LabelValueBean("", ""));
			Iterator<Object> itr = PropertiesFileLoader.getInstance()
					.getFacilityMessageValues().iterator();
			// Iterate facilities and store in collection to display in drop
			// down
			while (itr.hasNext()) {
				String proValue = (String) itr.next();
				facilitycollStr.add(proValue);
			}
			Collections.sort(facilitycollStr);

			Iterator<String> itrLbl = facilitycollStr.iterator();
			while (itrLbl.hasNext()) {
				String proValue = (String) itrLbl.next();
				facilitycoll.add(new LabelValueBean(proValue, proValue));
			}

			// set fality collection in request
			request.setAttribute("facilitycoll", facilitycoll);
			// set savestatus as save
			request.setAttribute("saveStatus", "save");
			// set NonWorkday bean
			this.nonWorkday= nonWorkday;
			// set entity name in request
			request.setAttribute("entityCode", entityName);
			// set screen field status as empty
			request.setAttribute("screenFieldsStatus", "");
			/*
			 * Start : Modified for Mantis 1366-Remove entry to log if duplicate
			 * records added by betcy on 18-02-2011
			 */
			if (swtexp.getErrorCode().equals(
					"errors.DataIntegrityViolationExceptioninAdd")) {
				if (errors != null) {
					errors.add("", new ActionMessage(swtexp.getErrorCode()));
				}
				saveErrors(request, errors);

			} else {
				saveErrors(request, SwtUtil.logException(swtexp, request, ""));
			}
			/*
			 * End : Modified for Mantis 1366-Remove entry to log if duplicate
			 * records added by betcy on 18-02-2011
			 */
			return "change";

		} catch (Exception exp) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [save] method : - "
					+ exp.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "save", NonWorkdayAction.class), request, "");
			return "fail";
		} finally {
			log.debug(this.getClass().getName() + " - [save] - Exit");
		}
	}

	/**
	 * Local method to convert 1 as Y(ES) and 2 as N(O)
	 * 
	 * @param option
	 * @return String - Y/N
	 */
	public String changeStatus(String option) {
		log.debug(this.getClass().getName() + " - [changeStatus] - Entry");
		// Condition to check selected option is 2
		if (option.equals("2")) {
			// set option as N
			option = "N";
			// Condition to check selected option is 1
		} else if (option.equals("1")) {
			// Set option as Y
			option = "Y";
		}
		log.debug(this.getClass().getName() + " - [changeStatus] - Exit");
		return option;
	}

	/**
	 * Local method to convert Y(ES) as 1 and N(O) as 2
	 * 
	 * @param option
	 * @return String - 1/2
	 */
	public String changeStatusNum(String option) {
		log.debug(this.getClass().getName() + " - [changeStatusNum] - Entry");
		// Condition to check selected option is N
		if (option.equals("N")) {
			// set option as 2
			option = "2";
			// Condition to check selected option is Y
		} else if (option.equals("Y")) {
			// set option as 1
			option = "1";
		}
		log.debug(this.getClass().getName() + " - [changeStatusNum] - Exit");
		return option;
	}

	/**
	 * Method to set entity details in request based on user's access rights.
	 * 
	 * @param request
	 * @return
	 * @throws SwtException
	 */
	private void putEntityListInReq(HttpServletRequest request)
			throws SwtException {
		log
				.debug(this.getClass().getName()
						+ " - [putEntityListInReq] - Entry");
		/* Method's local variable declaration */
		HttpSession session = null;
		/* Collection hold the entity list */
		Collection<EntityUserAccess> coll = null;
		/* Collection hold the entity label list */
		Collection<LabelValueBean> lvlColl = null;
		try {
			session = request.getSession();
			/* Retrieve the user's entity Access List from session */
			coll = SwtUtil.getUserEntityAccessList(session);
			/*
			 * Retrieve the user's entity and default entity List from entity
			 * collection
			 */
			lvlColl = SwtUtil.convertEntityAcessCollectionLVLWithDefault(coll);
			/* Set collection for combobox in screen */
			request.setAttribute("entities", lvlColl);
		} catch (Exception exp) {
			log
					.error(this.getClass().getName()
							+ " - Exception Catched in [putEntityListInReq] method : - "
							+ exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp,
					"putEntityListInReq", this.getClass());
		} finally {
			/* To set null value for method local variable's */
			coll = null;
			lvlColl = null;
			session = null;
		}
		log.debug(this.getClass().getName() + " - [putEntityListInReq] - Exit");
	}

}
