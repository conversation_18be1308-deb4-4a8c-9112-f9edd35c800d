/**
 * @(#)ForecastMonitorTemplateAction.java 1.0 24/05/11
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.maintenance.web;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Iterator;
import java.util.List;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

import org.swallow.control.model.UserMaintenance;
import org.swallow.control.service.UserMaintenanceManager;
import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.maintenance.model.BookCode;
import org.swallow.maintenance.model.EntityUserAccess;
import org.swallow.maintenance.model.ForecastMonitorTemplate;
import org.swallow.maintenance.model.ForecastMonitorTemplateCol;
import org.swallow.maintenance.model.ForecastMonitorTemplateColSrc;
import org.swallow.maintenance.model.Group;
import org.swallow.maintenance.model.InterfaceRule;
import org.swallow.maintenance.model.MetaGroup;
import org.swallow.maintenance.service.ForecastMonitorTemplateManager;
import org.swallow.model.User;
import org.swallow.util.CacheManager;
import org.swallow.util.CommonDataManager;
import org.swallow.util.SwtConstants;
import org.swallow.util.SwtUtil;
import org.swallow.util.LabelValueBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.apache.struts2.convention.annotation.Result;
import org.apache.struts2.convention.annotation.AllowedMethods;
import org.apache.struts2.convention.annotation.Action;
import org.apache.struts2.ServletActionContext;
import org.swallow.util.struts.CustomActionSupport;
import org.swallow.work.web.form.EntityMonitorForm;
import org.swallow.work.web.form.ForecastMonitorForm;

/**
 * ForecastMonitorTemplateAction.java
 * 
 * ForecastMonitorTemplateAction class is used for ForecastMonitorTemplate
 * screen that will display ForecastMonitor Templates<br>
 * 
 * <AUTHOR> A
 * @date May 24, 2011
 */
@Action(value = "/forecastMonitorTemplate", results = {
	@Result(name = "success", location = "/jsp/maintenance/forecastmonitortemplateflexdata.jsp"),
	@Result(name = "flexobject", location = "/jsp/maintenance/forecastmonitortemplateflex.jsp"),
	@Result(name = "templateaddflex", location = "/jsp/maintenance/forecastmonitortemplateaddflex.jsp"),
	@Result(name = "templateadd", location = "/jsp/maintenance/forecastmonitortemplateaddflexdata.jsp"),
	@Result(name = "dataerror", location = "/jsp/maintenance/forecastmonitortemplateflexdataerror.jsp"),
	@Result(name = "detailaddflex", location = "/jsp/maintenance/forecastdetailflexdata.jsp"),
	@Result(name = "detailadd", location = "/jsp/maintenance/forecastdetail.jsp"),
	@Result(name = "loadpopup", location = "/jsp/maintenance/forecastpopupflex.jsp"),
	@Result(name = "loadpopupdata", location = "/jsp/maintenance/forecastpopupflexdata.jsp"),
	@Result(name = "loadsubtotalflexdata", location = "/jsp/maintenance/forecastsubtotalflexdata.jsp"),
	@Result(name = "loadsubtotalpopup", location = "/jsp/maintenance/forecastsubtotalpopupflex.jsp"),
	@Result(name = "loadsubtotalpopupflexdata", location = "/jsp/maintenance/forecastsubtotalpopupflexdata.jsp"),
	@Result(name = "copyfrom", location = "/jsp/maintenance/copyfromforecasttemplate.jsp"),
	@Result(name = "copyfromflexdata", location = "/jsp/maintenance/copyfromforecasttemplateflexdata.jsp"),
	@Result(name = "fail", location = "/error.jsp"),
})

@AllowedMethods ({"displayMonitorTemplate" ,"displayAddMonitorTemplate" ,"refreshMonitorTemplate" ,"displayChangeMonitorTemplate" ,"addForecastDetails" ,"refreshDetail" ,"changeForecastDetails" ,"addPopUp" ,"addPopUpForSubtotal" ,"loadSubtotalAdd" ,"refreshSubDetail" ,"loadSubtotalChange" ,"deleteMonitorTemplate" ,"lockTemplate" ,"checkTemplateExists" ,"unLockTemplate" ,"loadCopyFrom" ,"saveTemplates" ,"saveTemplatesSrcInSession" ,"updateTemplates" ,"displayCopyMonitorTemplate" ,"getTemplateDetails" ,"clearSessionInstance" ,"addColumnSources" ,"addSubTotalColumnSources" ,"deleteColumnSrc" ,"deleteForecastColumn" })
public class ForecastMonitorTemplateAction extends CustomActionSupport {
public String execute() throws Exception {
    HttpServletRequest request = ServletActionContext.getRequest();

    // List of methods 
    String method = String.valueOf(request.getParameter("method"));

    switch (method) {
        case "unspecified":
            return unspecified();
        case "displayMonitorTemplate":
            return displayMonitorTemplate();
        case "displayAddMonitorTemplate":
            return displayAddMonitorTemplate();
        case "refreshMonitorTemplate":
            return refreshMonitorTemplate();
        case "displayChangeMonitorTemplate":
            return displayChangeMonitorTemplate();
        case "addForecastDetails":
            return addForecastDetails();
        case "refreshDetail":
            return refreshDetail();
        case "changeForecastDetails":
            return changeForecastDetails();
        case "addPopUp":
            return addPopUp();
        case "addPopUpForSubtotal":
            return addPopUpForSubtotal();
        case "loadSubtotalAdd":
            return loadSubtotalAdd();
        case "refreshSubDetail":
            return refreshSubDetail();
        case "loadSubtotalChange":
            return loadSubtotalChange();
        case "deleteMonitorTemplate":
            return deleteMonitorTemplate();
        case "lockTemplate":
            return lockTemplate();
        case "checkTemplateExists":
            checkTemplateExists();
        case "unLockTemplate":
            return unLockTemplate();
        case "loadCopyFrom":
            return loadCopyFrom();
        case "saveTemplates":
            return saveTemplates();
        case "saveTemplatesSrcInSession":
            return saveTemplatesSrcInSession();
        case "updateTemplates":
            return updateTemplates();
        case "displayCopyMonitorTemplate":
            return displayCopyMonitorTemplate();
        case "getTemplateDetails":
            return getTemplateDetails();
        case "clearSessionInstance":
            return clearSessionInstance();
        case "addColumnSources":
            return addColumnSources();
        case "addSubTotalColumnSources":
            return addSubTotalColumnSources();
        case "deleteColumnSrc":
            return deleteColumnSrc();
        case "deleteForecastColumn":
            return deleteForecastColumn();
        default:
            break;
    }

    return unspecified();
}




	/**
	 * Final log instance for logging this class
	 */
	private final Log log = LogFactory.getLog(this.getClass());

	/**
	 * An instance of forecastMonitorTemplateManager to be used across this
	 * Action
	 */
	@Autowired
private ForecastMonitorTemplateManager forecastMonitorTemplateManager = null;

	/**
	 * Method to inject forecastMonitorTemplateManager bean
	 * 
	 * @param forecastMonitorTemplateManager
	 *            the forecastMonitorTemplateManager to set
	 */
	public void setForecastMonitorTemplateManager(
			ForecastMonitorTemplateManager forecastMonitorTemplateManager) {
		this.forecastMonitorTemplateManager = forecastMonitorTemplateManager;
	}

	
	private ForecastMonitorTemplate forecastMonitorTemplate;
	public ForecastMonitorTemplate getForecastMonitorTemplate() {
		if (forecastMonitorTemplate == null) {
			forecastMonitorTemplate = new ForecastMonitorTemplate();
		}
		return forecastMonitorTemplate;
	}
	public void setForecastMonitorTemplate(ForecastMonitorTemplate forecastMonitorTemplate) {
		this.forecastMonitorTemplate = forecastMonitorTemplate;
		HttpServletRequest request = ServletActionContext.getRequest();
		request.setAttribute("forecastMonitorTemplate", forecastMonitorTemplate);
	}
	
	
	private ForecastMonitorTemplateColSrc forecastMonitorTemplateColSrc;
	public ForecastMonitorTemplateColSrc getForecastMonitorTemplateColSrc() {
		if (forecastMonitorTemplateColSrc == null) {
			forecastMonitorTemplateColSrc = new ForecastMonitorTemplateColSrc();
		}
		return forecastMonitorTemplateColSrc;
	}
	public void setForecastMonitorTemplateColSrc(ForecastMonitorTemplateColSrc forecastMonitorTemplateColSrc) {
		this.forecastMonitorTemplateColSrc = forecastMonitorTemplateColSrc;
		HttpServletRequest request = ServletActionContext.getRequest();
		request.setAttribute("forecastMonitorTemplateColSrc", forecastMonitorTemplateColSrc);
	}
	

	private ForecastMonitorTemplateCol forecastMonitorTemplateCol;
	public ForecastMonitorTemplateCol getForecastMonitorTemplateCol() {
		if (forecastMonitorTemplateCol == null) {
			forecastMonitorTemplateCol = new ForecastMonitorTemplateCol();
		}
		return forecastMonitorTemplateCol;
	}
	public void setForecastMonitorTemplateCol(ForecastMonitorTemplateCol forecastMonitorTemplateCol) {
		this.forecastMonitorTemplateCol = forecastMonitorTemplateCol;
		HttpServletRequest request = ServletActionContext.getRequest();
		request.setAttribute("forecastMonitorTemplateCol", forecastMonitorTemplateCol);
	}
	
	/**
	 * Default Action Method used to load the SWF object of Forecast Monitor
	 * Template Screen
	 * 
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return ActionForward
	 * 
	 * @throws SwtException
	 */
	public String unspecified()
			throws SwtException {
HttpServletRequest request = ServletActionContext.getRequest();
HttpServletResponse response = ServletActionContext.getResponse();

		log.debug(this.getClass().getName() + " - [unspecified] - Enter/Exit ");
		// set the Attribute for menuAccessId
		request.setAttribute("menuAccessId", request
				.getParameter("menuAccessId"));
		return ("flexobject");
	}

	/**
	 * 
	 * Method to load the Forecast Monitor Template screen, with private
	 * templates defined for currently logged in user and all public templates
	 * 
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return ActionForward
	 * @throws SwtException
	 */
	public String displayMonitorTemplate() throws SwtException {
HttpServletRequest request = ServletActionContext.getRequest();
HttpServletResponse response = ServletActionContext.getResponse();

		/* Variable Declaration for dyForm */
		// DynaValidatorForm dyForm = null;
		/* Variable Declaration for collMonitorDetails */
		List<ForecastMonitorTemplate> collMonitorTemplateDetails = null;
		// Insatnce for forecast Monitor template
		ForecastMonitorTemplate forecastMonitorTemplate = null;
		// Iterator instance vor collMonitorTemplateDetails
		Iterator<ForecastMonitorTemplate> itrMonitorTemplateDetails = null;
		// Array list to display template details
		ArrayList<ForecastMonitorTemplate> listToDisplayTemplates = null;
		// Sting variable to hold current user id
		String currentUserId = null;
		// String variable to hold hostId
		String hostId = null;
		// Get Forecast template instance
		ForecastMonitorTemplate forecastTemplate = null;

		try {
			log.debug(this.getClass().getName()
					+ " - [displayMonitorTemplate]-Entry");
			// set the form object into // DynaValidatorForm
			// get current UserId from session
			currentUserId = SwtUtil.getCurrentUserId(request.getSession());

			hostId = SwtUtil.getCurrentHostId();
			// get the forecastMonitorTemplate for object
			forecastMonitorTemplate = (ForecastMonitorTemplate) (getForecastMonitorTemplate());
			// Call the forecastMonitorTemplateManager and get the forecast
			// monitor template details
			collMonitorTemplateDetails = forecastMonitorTemplateManager
					.getForecastMonitorTemplate(hostId, currentUserId);

			listToDisplayTemplates = new ArrayList<ForecastMonitorTemplate>();

			// Set list to itereate
			itrMonitorTemplateDetails = collMonitorTemplateDetails.iterator();

			// loop to get forecast template values
			while (itrMonitorTemplateDetails.hasNext()) {
				// Get Forecast template Values
				forecastTemplate = itrMonitorTemplateDetails.next();
				// Condition to check public value is 'Y'
				if (forecastTemplate.getPublicTemplate().equals(
						SwtConstants.YES)) {
					// Set values As Yes
					forecastTemplate.setPublicTemplate(SwtConstants.YES_VALUE);

					// Condition to check public value is 'N'
				} else {
					// Ste values as No
					forecastTemplate.setPublicTemplate(SwtConstants.No_VALUE);
				}

				listToDisplayTemplates.add(forecastTemplate);

			}
			// set the forecastMonitorTemplate
			setForecastMonitorTemplate(forecastMonitorTemplate);
			// setting currentUserId in request
			request.setAttribute("currentUserId", currentUserId);
			request.setAttribute("recordCount", collMonitorTemplateDetails.size());
			// setting reply status as true in request
			request.setAttribute("reply_status_ok", SwtConstants.STR_TRUE);
			// setting the reply message in request
			request.setAttribute("reply_message", SwtConstants.DATA_FETCH_OK);
			// Setting template collection in request
			request.setAttribute("collMonitorTemplateDetails",
					listToDisplayTemplates);
			// set the Attribute for menuAccessId
			request.setAttribute("menuAccessId", request
					.getParameter("menuAccessId"));
			// setting reply status as true in request
			request.setAttribute("reply_status_ok", SwtConstants.STR_TRUE);
			// setting the reply message in request
			request.setAttribute("reply_message", SwtConstants.DATA_FETCH_OK);

		} catch (SwtException e) {
			log.error(this.getClass().getName()
					+ " - [displayMonitorTemplate] - Exception -"
					+ e.getStackTrace()[0].getClassName() + "."
					+ e.getStackTrace()[0].getMethodName() + ":"
					+ e.getStackTrace()[0].getLineNumber() + " "
					+ e.getMessage());
			request.setAttribute("reply_status_ok", SwtConstants.STR_FALSE);
			request.setAttribute("reply_message", e.getMessage());
			request.setAttribute("reply_location", e.getStackTrace()[0]
					.getClassName()
					+ "."
					+ e.getStackTrace()[0].getMethodName()
					+ ":"
					+ e.getStackTrace()[0].getLineNumber());
			SwtUtil.logException(e, request, "");
			return ("dataerror");
		} catch (Exception e) {
			log.error(this.getClass().getName()
					+ " - [displayMonitorTemplate] - Exception -"
					+ e.getStackTrace()[0].getClassName() + "."
					+ e.getStackTrace()[0].getMethodName() + ":"
					+ e.getStackTrace()[0].getLineNumber() + " "
					+ e.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					e, "displayMonitorTemplate", this.getClass()), request, "");
			request.setAttribute("reply_status_ok", SwtConstants.STR_FALSE);
			request.setAttribute("reply_message", e.getMessage());
			request.setAttribute("reply_location", e.getStackTrace()[0]
					.getClassName()
					+ "."
					+ e.getStackTrace()[0].getMethodName()
					+ ":"
					+ e.getStackTrace()[0].getLineNumber());
			return ("dataerror");
		} finally {
			collMonitorTemplateDetails = null;
			itrMonitorTemplateDetails = null;
			hostId = null;
			log.debug(this.getClass().getName()
					+ " - [displayMonitorTemplate] - Exit");
		}
		return ("success");
	}

	/**
	 * 
	 * Method to load the Add Forecast Monitor Templates, with Fixed columns
	 * 
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return ActionForward
	 * @throws SwtException
	 */

	public String displayAddMonitorTemplate() throws SwtException {
HttpServletRequest request = ServletActionContext.getRequest();
HttpServletResponse response = ServletActionContext.getResponse();

		// Method's local variable declaration
		/* Variable Declaration for collMonitorDetails */
		List<ForecastMonitorTemplateCol> listMonitorTemplateDetailsCol = null;
		// ForecastMonitorTemplate bean
		ForecastMonitorTemplate forecastMonitorTemplate = null;
		// Array list to hold User Collection
		ArrayList<User> templateUsersList = null;
		// Session Instance
		HttpSession session = null;
		// User Instance
		User user = null;
		// Common Data Manager Instance
		CommonDataManager CDM = null;
		// String to hold isPublic
		String isPublic = null;
		try {
			// debug message
			log.debug(this.getClass().getName()
					+ "-[displayAddMonitorTemplate]-Entry");
			// Read is public from request
			isPublic = request.getParameter("isPublic");
			request.setAttribute("callFrom", request.getParameter("callFrom"));
			// Set template Id in request
			request.setAttribute("selectedTemplateId", request
					.getParameter("templateId"));
			// Set template Name in request
			request.setAttribute("selectedTemplateName", request
					.getParameter("templateName"));
			// Set user Id in request
			request.setAttribute("selectedUserId", request
					.getParameter("userId"));
			// Set is public in request
			request.setAttribute("isPublic", isPublic);

			if (SwtUtil.isEmptyOrNull(request.getParameter("loadFlex"))) {
				// debug message
				log.debug(this.getClass().getName()
						+ "-[displayAddMonitorTemplate]-Exit loading SWF");
				return ("templateaddflex");
			}
			request.getSession().setAttribute("forecastMonitorTemplate", null);
			// Obtain session from request
			session = request.getSession();
			// Get Common Data manager bean from session
			CDM = (CommonDataManager) session
					.getAttribute(SwtConstants.CDM_BEAN);
			// Get user bean from Common Data manager
			user = CDM.getUser();
			// Set Current and default User Id in list
			templateUsersList = new ArrayList<User>();
			// Add current user in collection
			templateUsersList.add(user);
			// Instantiate User bean fro default users
			user = new User();
			// set default user id in user bean
			user.getId().setUserId(SwtConstants.DEFAULT);
			// set user name in bean
			user.setUserName(SwtConstants.DEFAULT);
			// Add default user in collection
			templateUsersList.add(user);

			forecastMonitorTemplate = (ForecastMonitorTemplate) session
					.getAttribute("forecastMonitorTemplate");

			if (forecastMonitorTemplate == null) {
				// Get Template columns
				listMonitorTemplateDetailsCol = forecastMonitorTemplateManager
						.getForecastMonitorTemplateCol();
				// Add row colors
				listMonitorTemplateDetailsCol = addDataGridColors(listMonitorTemplateDetailsCol);
				// Initialise the forecastMonitorTemplate bean
				forecastMonitorTemplate = new ForecastMonitorTemplate();
				// set the list of columns for the template instance
				forecastMonitorTemplate
						.setForecastTemplateColList(listMonitorTemplateDetailsCol);
				// set the list of user for the template instance
				forecastMonitorTemplate.setTemplateUsersList(templateUsersList);
				// set the template id as empty string
				forecastMonitorTemplate.getId().setTemplateId(
						request.getParameter("templateId"));
				// set the template name as empty string
				forecastMonitorTemplate.setTemplateName(request
						.getParameter("templateName"));
			}
			// Set both list in bean
			request.setAttribute("forecastMonitorTemplate",
					forecastMonitorTemplate);
			request.setAttribute("recordCount", listMonitorTemplateDetailsCol.size());
			request.getSession().setAttribute("forecastMonitorTemplate",
					forecastMonitorTemplate);
			// setting reply status as true in request
			request.setAttribute("reply_status_ok", SwtConstants.STR_TRUE);
			// setting the reply message in request
			request.setAttribute("reply_message", SwtConstants.DATA_FETCH_OK);

		} catch (SwtException e) {
			log.error(this.getClass().getName()
					+ " - [displayAddMonitorTemplate] - Exception -"
					+ e.getStackTrace()[0].getClassName() + "."
					+ e.getStackTrace()[0].getMethodName() + ":"
					+ e.getStackTrace()[0].getLineNumber() + " "
					+ e.getMessage());
			request.setAttribute("reply_status_ok", SwtConstants.STR_FALSE);
			request.setAttribute("reply_message", e.getMessage());
			request.setAttribute("reply_location", e.getStackTrace()[0]
					.getClassName()
					+ "."
					+ e.getStackTrace()[0].getMethodName()
					+ ":"
					+ e.getStackTrace()[0].getLineNumber());
			SwtUtil.logException(e, request, "");
			return ("dataerror");
		} catch (Exception e) {
			log.error(this.getClass().getName()
					+ " - [displayAddMonitorTemplate] - Exception -"
					+ e.getStackTrace()[0].getClassName() + "."
					+ e.getStackTrace()[0].getMethodName() + ":"
					+ e.getStackTrace()[0].getLineNumber() + " "
					+ e.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					e, "displayAddMonitorTemplate", this.getClass()), request,
					"");
			request.setAttribute("reply_status_ok", SwtConstants.STR_FALSE);
			request.setAttribute("reply_message", e.getMessage());
			request.setAttribute("reply_location", e.getStackTrace()[0]
					.getClassName()
					+ "."
					+ e.getStackTrace()[0].getMethodName()
					+ ":"
					+ e.getStackTrace()[0].getLineNumber());
			return ("dataerror");
		} finally {
			listMonitorTemplateDetailsCol = null;
			templateUsersList = null;
			session = null;
			user = null;
			CDM = null;
			log.debug(this.getClass().getName()
					+ " - [displayAddMonitorTemplate] - Exit");
		}
		return ("templateadd");
	}

	/**
	 * 
	 * Method to refresh the Add/Change Forecast Monitor Templates grid after
	 * adding column and sources
	 * 
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return ActionForward
	 * @throws SwtException
	 */

	public String refreshMonitorTemplate() throws SwtException {
HttpServletRequest request = ServletActionContext.getRequest();
HttpServletResponse response = ServletActionContext.getResponse();

		try {
			log.debug(this.getClass().getName()
					+ " - [refreshMonitorTemplate]-Entry");
			// Set both list in bean
			request.setAttribute("forecastMonitorTemplate", request
					.getSession().getAttribute("forecastMonitorTemplate"));
			// setting reply status as true in request
			request.setAttribute("reply_status_ok", SwtConstants.STR_TRUE);
			// setting the reply message in request
			request.setAttribute("reply_message", SwtConstants.DATA_FETCH_OK);

		} catch (Exception e) {
			log.error(this.getClass().getName()
					+ " - [refreshMonitorTemplate] - Exception -"
					+ e.getStackTrace()[0].getClassName() + "."
					+ e.getStackTrace()[0].getMethodName() + ":"
					+ e.getStackTrace()[0].getLineNumber() + " "
					+ e.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					e, "refreshMonitorTemplate", this.getClass()), request, "");
			request.setAttribute("reply_status_ok", SwtConstants.STR_FALSE);
			request.setAttribute("reply_message", e.getMessage());
			request.setAttribute("reply_location", e.getStackTrace()[0]
					.getClassName()
					+ "."
					+ e.getStackTrace()[0].getMethodName()
					+ ":"
					+ e.getStackTrace()[0].getLineNumber());
			return ("dataerror");
		} finally {
			log.debug(this.getClass().getName()
					+ " - [refreshMonitorTemplate]-Exit");
		}
		return ("templateadd");
	}

	/**
	 * 
	 * Method to load the Change Forecast Monitor Templates screen, for the
	 * selected template Id
	 * 
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return ActionForward
	 * @throws SwtException
	 */

	public String displayChangeMonitorTemplate() throws SwtException {
HttpServletRequest request = ServletActionContext.getRequest();
HttpServletResponse response = ServletActionContext.getResponse();

		// Method's local variable declaration
		// variable to hold hostId
		String hostId = null;
		/* Variable Declaration for dyForm */
		// DynaValidatorForm dyForm = null;
		/* Variable Declaration for collMonitorDetails */
		List<ForecastMonitorTemplateCol> listMonitorTemplateDetailsCol = null;
		/* Variable Declaration for collMonitorDetails */
		ArrayList<ForecastMonitorTemplateCol> listMonitorTemplateDetailsColToSave = null;
		// Insatnce for forecast Monitor template
		ForecastMonitorTemplateCol forecastMonitorTemplateCol = null;
		// Array list to hold User Collection
		ArrayList<User> templateUsersList = null;
		// User Instance
		User user = null;
		// UserMaintenance Instance
		UserMaintenance userMaintenance = null;
		// String to hold selectedTemplateId
		String selectedTemplateId = null;
		// String to hold selectedTemplateName
		String selectedTemplateName = null;
		// String to hold isPublic
		String isPublic = null;
		// String to hold selectedUserId
		String selectedUserId = null;
		// User Dao Hibenate Instance
		UserMaintenanceManager userMaintenanceManagerImpl = null;
		// ForecastMonitorTemplate bean
		ForecastMonitorTemplate forecastMonitorTemplate = null;
		// ForecastMonitorTemplateColSrc bean
		ForecastMonitorTemplateColSrc forecastMonitorTemplateColSrc = null;
		ArrayList<ForecastMonitorTemplateColSrc> forecastMonitorTemplateColSrcList = null;
		try {
			// debug message
			log.debug(this.getClass().getName()
					+ "-[displayChangeMonitorTemplate]-Entry");

			// Get hostId from DB
			hostId = SwtUtil.getCurrentHostId();
			// Read templateId from request
			selectedTemplateId = request.getParameter("templateId");
			// Read templateName from request
			selectedTemplateName = request.getParameter("templateName");
			// Read selected user from request
			selectedUserId = request.getParameter("userId");
			// Read is public from request
			isPublic = request.getParameter("isPublic");
			// Set template Id in request
			request.setAttribute("selectedTemplateId", selectedTemplateId);
			// Set template Name in request
			request.setAttribute("selectedTemplateName", selectedTemplateName);
			// Set user Id in request
			request.setAttribute("selectedUserId", selectedUserId);
			// Set is public in request
			request.setAttribute("isPublic", isPublic);
			request.setAttribute("callFrom", request.getParameter("callFrom"));

			if (SwtUtil.isEmptyOrNull(request.getParameter("screen")))
				// Set is screen Name as change Screen
				request.setAttribute("screenName", "changeScreen");

			if (SwtUtil.isEmptyOrNull(request.getParameter("loadFlex"))) {
				// debug message
				log.debug(this.getClass().getName()
						+ "-[displayChangeMonitorTemplate]-Exit to load SWF");
				return ("templateaddflex");
			}

			// set session value as null
			request.getSession().setAttribute("forecastMonitorTemplate", null);

			forecastMonitorTemplate = (ForecastMonitorTemplate) request
					.getSession().getAttribute("forecastMonitorTemplate");

			if (forecastMonitorTemplate == null) {
				forecastMonitorTemplate = new ForecastMonitorTemplate();
				// set template id in bean
				forecastMonitorTemplate.getId().setTemplateId(
						selectedTemplateId);
				// set template name
				forecastMonitorTemplate.setTemplateName(selectedTemplateName);
				// set selected user id
				forecastMonitorTemplate.setUserId(selectedUserId);
				// Condition to check is public is yes
				if (!SwtUtil.isEmptyOrNull(isPublic)
						&& isPublic.equalsIgnoreCase(SwtConstants.YES_VALUE)) {
					forecastMonitorTemplate.setPublicTemplate(SwtConstants.YES);
				} else {
					forecastMonitorTemplate.setPublicTemplate(SwtConstants.NO);
				}
				// set the form object into // DynaValidatorForm
				// get the forecastMonitorTemplate for object
				forecastMonitorTemplateCol = (ForecastMonitorTemplateCol)getForecastMonitorTemplateCol();

				// Instantiate list
				templateUsersList = new ArrayList<User>();
				// Set host Id in bean
				forecastMonitorTemplateCol.getId().setHostId(hostId);
				// Set user Id in bean
				forecastMonitorTemplateCol.setUserId(selectedUserId);
				// Set template Id in bean
				forecastMonitorTemplateCol.getId().setTemplateId(
						selectedTemplateId);
				// Instantiate listMonitorTemplateDetailsColToSave
				listMonitorTemplateDetailsColToSave = new ArrayList<ForecastMonitorTemplateCol>();

				if (!selectedUserId.equals(SwtConstants.DEFAULT)) {
					// Obtain user manager bean
					userMaintenanceManagerImpl = (UserMaintenanceManager) (SwtUtil
							.getBean("usermaintenanceManager"));
					// get selected user detail
					userMaintenance = userMaintenanceManagerImpl
							.fetchUserDetail(hostId, selectedUserId);

					user = new User();
					user.getId().setUserId(userMaintenance.getId().getUserId());
					user.setUserName(userMaintenance.getUsername());
				} else {
					// Instantiate User bean for default users
					user = new User();
					// set default user id in user bean
					user.getId().setUserId(SwtConstants.DEFAULT);
					// set user name in bean
					user.setUserName(SwtConstants.DEFAULT);
				}
				// Add default user in collection
				templateUsersList.add(user);

				// Get Template columns for selected template ID
				listMonitorTemplateDetailsCol = forecastMonitorTemplateManager
						.getForecastMonitorTemplateCol(forecastMonitorTemplateCol);

				// Add row colors
				listMonitorTemplateDetailsCol = addDataGridColors(listMonitorTemplateDetailsCol);

				// Add corresponding column and sources in session
				for (ForecastMonitorTemplateCol forecastTemplateCol : listMonitorTemplateDetailsCol) {
					forecastMonitorTemplateColSrcList = new ArrayList<ForecastMonitorTemplateColSrc>();
					if ((!forecastTemplateCol.getColumnType().equals(
							SwtConstants.FIXED_LABEL))
							|| forecastTemplateCol.getColumnDisplayName()
									.equalsIgnoreCase(SwtConstants.TOTAL)) {

						// Adding Total and Subtotal source values to session
						if (!forecastTemplateCol.getColumnType().equals(
								SwtConstants.NORMAL)
								&& !forecastTemplateCol.getColumnType().equals(
										SwtConstants.NORMAL_LABEL)) {

							// Set id values for forecastMonitorTemplateColSrc
							// bean
							forecastMonitorTemplateColSrc = new ForecastMonitorTemplateColSrc();
							forecastMonitorTemplateColSrc.getId().setHostId(
									hostId);
							forecastMonitorTemplateColSrc
									.setUserId(selectedUserId);
							forecastMonitorTemplateColSrc.getId()
									.setTemplateId(selectedTemplateId);
							forecastMonitorTemplateColSrc.getId().setColumnId(
									forecastTemplateCol.getId().getColumnId());

							// Get forecastMonitorTemplateColSrc collection for
							// given id fields
							for (ForecastMonitorTemplateColSrc forecastMonitorTemplateSrc : forecastMonitorTemplateManager
									.getForecastMonitorTemplateColSrcForColumnId(
											forecastMonitorTemplateColSrc,
											forecastTemplateCol.getColumnType())) {

								// Loop to get forecastMonitorTemplateCol
								// collection bean set ordinal position and
								// column description names in it
								for (ForecastMonitorTemplateCol forecastCol : listMonitorTemplateDetailsCol) {
									if (forecastCol.getColumnDisplayName()
											.equals(
													forecastMonitorTemplateSrc
															.getEntityId())) {
										forecastMonitorTemplateSrc
												.setOrdinalPos(forecastCol
														.getOrdinalPos());
										forecastMonitorTemplateSrc
												.setName(forecastCol
														.getColumnDescription());
										break;
									}
								}
								// Condition to check column id is total to set
								// total multiplier for columns
								if (forecastMonitorTemplateSrc.getId()
										.getColumnId().equalsIgnoreCase(
												SwtConstants.TOTAL)
										&& !forecastTemplateCol
												.getColumnDisplayName()
												.equalsIgnoreCase(
														SwtConstants.TOTAL)) {
									forecastTemplateCol
											.setTotalMultiplier(forecastMonitorTemplateSrc
													.getMultiplier().toString());

								} else {
									forecastMonitorTemplateColSrcList
											.add(forecastMonitorTemplateSrc);
								}
							}
							// set forecastMonitorTemplateColSrc collection in
							// forecastMonitorTemplateCol bean
							forecastTemplateCol
									.setListForecastMonitorTemplateColSrc(forecastMonitorTemplateColSrcList);

						} else {
							// Adding normal source values to session

							// Set id values for forecastMonitorTemplateColSrc
							// bean
							forecastMonitorTemplateColSrc = new ForecastMonitorTemplateColSrc();
							forecastMonitorTemplateColSrc
									.setEntityId(forecastTemplateCol
											.getColumnDisplayName());
							forecastMonitorTemplateColSrc
									.setName(forecastTemplateCol
											.getColumnDescription());
							forecastMonitorTemplateColSrc.getId().setHostId(
									hostId);
							forecastMonitorTemplateColSrc
									.setUserId(selectedUserId);
							forecastMonitorTemplateColSrc.getId()
									.setTemplateId(selectedTemplateId);
							forecastMonitorTemplateColSrc.getId().setColumnId(
									forecastTemplateCol.getId().getColumnId());
							// Get forecastMonitorTemplateColSrc collection for
							// given id fields
							for (ForecastMonitorTemplateColSrc forecastMonitorTemplateSrc : forecastMonitorTemplateManager
									.getChangeForecastMonitorTemplateColSrc(forecastMonitorTemplateColSrc)) {
								// Condition to check column id is total to set
								// total multiplier for columns
								if (forecastMonitorTemplateSrc.getId()
										.getColumnId().equalsIgnoreCase(
												SwtConstants.TOTAL)) {
									forecastTemplateCol
											.setTotalMultiplier(forecastMonitorTemplateSrc
													.getMultiplier().toString());
								} else {
									forecastMonitorTemplateColSrcList
											.add(forecastMonitorTemplateSrc);
								}
							}
							// set forecastMonitorTemplateColSrc collection in
							// forecastMonitorTemplateCol bean
							forecastTemplateCol
									.setListForecastMonitorTemplateColSrc(forecastMonitorTemplateColSrcList);

						}
					}
					// Set template Id in bean
					// set modified values as save
					forecastTemplateCol.setModifiedValue("update");
					// set columns in session
					listMonitorTemplateDetailsColToSave
							.add(forecastTemplateCol);

				}

				// add columns in template
				forecastMonitorTemplate
						.setForecastTemplateColList(listMonitorTemplateDetailsColToSave);
				// add user list in template
				forecastMonitorTemplate.setTemplateUsersList(templateUsersList);
			}
			// Set both list in bean
			request.setAttribute("forecastMonitorTemplate",
					forecastMonitorTemplate);
			request.setAttribute("recordCount", listMonitorTemplateDetailsCol.size());
			request.getSession().setAttribute("forecastMonitorTemplate",
					forecastMonitorTemplate);

			// setting reply status as true in request
			request.setAttribute("reply_status_ok", SwtConstants.STR_TRUE);
			// setting the reply message in request
			request.setAttribute("reply_message", SwtConstants.DATA_FETCH_OK);
		} catch (SwtException e) {
			log.error(this.getClass().getName()
					+ " - [displayChangeMonitorTemplate] - Exception -"
					+ e.getStackTrace()[0].getClassName() + "."
					+ e.getStackTrace()[0].getMethodName() + ":"
					+ e.getStackTrace()[0].getLineNumber() + " "
					+ e.getMessage());
			request.setAttribute("reply_status_ok", SwtConstants.STR_FALSE);
			request.setAttribute("reply_message", e.getMessage());
			request.setAttribute("reply_location", e.getStackTrace()[0]
					.getClassName()
					+ "."
					+ e.getStackTrace()[0].getMethodName()
					+ ":"
					+ e.getStackTrace()[0].getLineNumber());
			SwtUtil.logException(e, request, "");
			return ("dataerror");
		} catch (Exception e) {
			log.error(this.getClass().getName()
					+ " - [displayChangeMonitorTemplate] - Exception -"
					+ e.getStackTrace()[0].getClassName() + "."
					+ e.getStackTrace()[0].getMethodName() + ":"
					+ e.getStackTrace()[0].getLineNumber() + " "
					+ e.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					e, "displayChangeMonitorTemplate", this.getClass()),
					request, "");
			request.setAttribute("reply_status_ok", SwtConstants.STR_FALSE);
			request.setAttribute("reply_message", e.getMessage());
			request.setAttribute("reply_location", e.getStackTrace()[0]
					.getClassName()
					+ "."
					+ e.getStackTrace()[0].getMethodName()
					+ ":"
					+ e.getStackTrace()[0].getLineNumber());
			return ("dataerror");
		} finally {
			hostId = null;
			forecastMonitorTemplateCol = null;
			user = null;
			userMaintenance = null;
			userMaintenanceManagerImpl = null;
			forecastMonitorTemplateColSrc = null;
			log.debug(this.getClass().getName()
					+ " - [displayChangeMonitorTemplate]- Exit");
		}
		return ("templateadd");
	}

	/**
	 * 
	 * Method to load  Add Column to Forecast Monitor Template screen
	 * 
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return ActionForward
	 * @throws SwtException
	 */

	public String addForecastDetails() throws SwtException {
HttpServletRequest request = ServletActionContext.getRequest();
HttpServletResponse response = ServletActionContext.getResponse();

		// Method's local variable declaration
		/* Variable Declaration for dyForm */
		// DynaValidatorForm dyForm = null;
		// Instance for forecast Monitor template
		ForecastMonitorTemplateColSrc forecastMonitorTemplateColSrc = null;
		// Array list to hold User Collection
		ArrayList<LabelValueBean> columnTypeList = null;
		// Label Value Bean instance
		LabelValueBean columnTypeLVB = null;
		try {

			// debug message
			log
					.debug(this.getClass().getName()
							+ "-[addForecastDetails]-Entry");
			// Set template Id in request
			request.setAttribute("templateId", request
					.getParameter("templateId"));
			// Set template Name in request
			request.setAttribute("templateName", request
					.getParameter("templateName"));
			// Set user id in request
			request.setAttribute("userId", request.getParameter("userId"));
			// Set addClick in request
			request.setAttribute("addClick", request.getParameter("addClick"));

			request.setAttribute("selectedColumnType",
					SwtConstants.NORMAL_LABEL);
			if (SwtUtil.isEmptyOrNull(request.getParameter("loadFlex"))) {
				// debug message
				log.debug(this.getClass().getName()
						+ "-[addForecastDetails]-Exit loading SWF");
				return ("detailadd");
			}

			// set the form object into // DynaValidatorForm
			// get the forecastMonitorTemplate for object
			forecastMonitorTemplateColSrc = (ForecastMonitorTemplateColSrc)getForecastMonitorTemplateColSrc();
			request.getSession().setAttribute(
					"forecastMonitorTemplateColSrcList", null);

			columnTypeList = new ArrayList<LabelValueBean>();
			columnTypeLVB = new LabelValueBean(SwtConstants.NORMAL_LABEL,
					SwtConstants.NORMAL);
			columnTypeList.add(columnTypeLVB);
			columnTypeLVB = new LabelValueBean(SwtConstants.SUB_TOTAL_LABEL,
					SwtConstants.SUB_TOTAL);
			columnTypeList.add(columnTypeLVB);
			// Set displayName
			request.setAttribute("shortName", "");
			// Set description
			request.setAttribute("description", "");
			// Set type as SwtConstants.NORMAL_LABEL
			request.setAttribute("type", SwtConstants.NORMAL_LABEL);
			// Set column id
			request.setAttribute("columnId", "");
			// Set ordinal position
			request.setAttribute("ordinalPos", "");
			// Set total multiplier
			request.setAttribute("totalMultiplier", "");
			// Set column type list
			request.setAttribute("columnTypeList", columnTypeList);

			// Set both list in bean
			request.setAttribute("listMonitorTemplateDetailsColSrc",
					new ArrayList<ForecastMonitorTemplateCol>());
			request.setAttribute("recordCount", new ArrayList<ForecastMonitorTemplateCol>().size());
			forecastMonitorTemplateColSrc.getId().setSourceId(
					SwtConstants.NORMAL_LABEL);

			// set form object
			setForecastMonitorTemplateColSrc(forecastMonitorTemplateColSrc);
			// setting reply status as true in request
			request.setAttribute("reply_status_ok", SwtConstants.STR_TRUE);
			// setting the reply message in request
			request.setAttribute("reply_message", SwtConstants.DATA_FETCH_OK);
		} catch (Exception e) {
			log.error(this.getClass().getName()
					+ " - [addForecastDetails] - Exception -"
					+ e.getStackTrace()[0].getClassName() + "."
					+ e.getStackTrace()[0].getMethodName() + ":"
					+ e.getStackTrace()[0].getLineNumber() + " "
					+ e.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					e, "addForecastDetails", this.getClass()), request, "");
			request.setAttribute("reply_status_ok", SwtConstants.STR_FALSE);
			request.setAttribute("reply_message", e.getMessage());
			request.setAttribute("reply_location", e.getStackTrace()[0]
					.getClassName()
					+ "."
					+ e.getStackTrace()[0].getMethodName()
					+ ":"
					+ e.getStackTrace()[0].getLineNumber());
			return ("dataerror");
		} finally {
			columnTypeLVB = null;
			log.debug(this.getClass().getName()
					+ " - [addForecastDetails]- Exit");
		}
		return ("detailaddflex");
	}

	/**
	 * 
	 * Method to add Normal column sources in add a column to / Change column in
	 * Forecast Template grid
	 * 
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return ActionForward
	 * @throws SwtException
	 */
	public String refreshDetail()
			throws SwtException {
HttpServletRequest request = ServletActionContext.getRequest();
HttpServletResponse response = ServletActionContext.getResponse();

		// Array list to hold User Collection
		ArrayList<LabelValueBean> columnTypeList = null;
		// Label Value Bean instance
		LabelValueBean columnTypeLVB = null;
		try {
			log.debug(this.getClass().getName() + " - [refreshDetail] - Entry");
			request
					.setAttribute("shortName", request
							.getParameter("shortName"));
			// Set description
			request.setAttribute("description", request
					.getParameter("description"));
			// Set type as SwtConstants.NORMAL_LABEL
			request.setAttribute("type", request.getParameter("type"));
			// Set column id
			request.setAttribute("columnId", request.getParameter("columnId"));
			// Set ordinal position
			request.setAttribute("ordinalPos", request
					.getParameter("ordinalPos"));
			// Set total multiplier
			request.setAttribute("totalMultiplier", request
					.getParameter("totalMultiplier"));
			columnTypeList = new ArrayList<LabelValueBean>();
			columnTypeLVB = new LabelValueBean(SwtConstants.NORMAL_LABEL,
					SwtConstants.NORMAL);
			columnTypeList.add(columnTypeLVB);
			columnTypeLVB = new LabelValueBean(SwtConstants.SUB_TOTAL_LABEL,
					SwtConstants.SUB_TOTAL);
			columnTypeList.add(columnTypeLVB);
			// Set column type list
			request.setAttribute("columnTypeList", columnTypeList);

			request.setAttribute("listMonitorTemplateDetailsColSrc", request
					.getSession().getAttribute(
							"forecastMonitorTemplateColSrcList"));
			ArrayList arrayList = new ArrayList<>();
			arrayList = (ArrayList) request.getSession().getAttribute("forecastMonitorTemplateColSrcList");
			request.setAttribute("recordCount", arrayList.size());

			// setting reply status as true in request
			request.setAttribute("reply_status_ok", SwtConstants.STR_TRUE);
			// setting the reply message in request
			request.setAttribute("reply_message", SwtConstants.DATA_FETCH_OK);
		} catch (Exception e) {
			log.error(this.getClass().getName()
					+ " - [refreshDetail] - Exception -"
					+ e.getStackTrace()[0].getClassName() + "."
					+ e.getStackTrace()[0].getMethodName() + ":"
					+ e.getStackTrace()[0].getLineNumber() + " "
					+ e.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					e, "refreshDetail", this.getClass()), request, "");
			request.setAttribute("reply_status_ok", SwtConstants.STR_FALSE);
			request.setAttribute("reply_message", e.getMessage());
			request.setAttribute("reply_location", e.getStackTrace()[0]
					.getClassName()
					+ "."
					+ e.getStackTrace()[0].getMethodName()
					+ ":"
					+ e.getStackTrace()[0].getLineNumber());
			return ("dataerror");
		} finally {

			columnTypeLVB = null;
			log.debug(this.getClass().getName()
					+ " - [refreshDetail] - Exit");
		}
		return ("detailaddflex");
	}

	/**
	 * 
	 * Method to load the Change Column in Forecast Monitor Templates Screen for selected 
	 * normal column sources
	 * 
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return ActionForward
	 * @throws SwtException
	 */

	@SuppressWarnings("unchecked")
	public String changeForecastDetails() throws SwtException {
HttpServletRequest request = ServletActionContext.getRequest();
HttpServletResponse response = ServletActionContext.getResponse();

		// Method's local variable declaration
		// variable to hold hostId
		String hostId = null;
		/* Variable Declaration for dyForm */
		// DynaValidatorForm dyForm = null;
		/* Variable Declaration for collMonitorDetails */
		List<ForecastMonitorTemplateCol> listMonitorTemplateDetailsCol = null;
		/* Variable Declaration for collMonitorDetails */
		Iterator<ForecastMonitorTemplateCol> itrMonitorTemplateDetailsCol = null;
		/* Variable Declaration for collMonitorDetails */
		ArrayList<ForecastMonitorTemplateColSrc> sessionlistMonitorTemplateDetailsColSrc = null;
		// Insatnce for forecast Monitor template
		ForecastMonitorTemplateColSrc forecastMonitorTemplateColSrc = null;
		// String to hold selectedTemplateId
		String selectedTemplateId = null;
		// String to hold selectedUserId
		String selectedUserId = null;
		// String to hold selectedColumnName
		String selectedDisplayName = null;
		// String to hold selectedDescription
		String selectedType = null;
		// Array list to hold User Collection
		ArrayList<LabelValueBean> columnTypeList = null;
		// Label Value Bean instance
		LabelValueBean columnTypeLVB = null;
		// Array list to get ForecastMonitorTemplateColSrc values
		List<ForecastMonitorTemplateColSrc> monitorTemplateColSrcList = null;
		// Iterator to iterate ForecastMonitorTemplateColSrc values
		Iterator<ForecastMonitorTemplateColSrc> monitorTemplateColSrcItr = null;
		// Long to hold TotalMultiplier
		String totalMultiplier = null;
		// Forecast template instance
		ForecastMonitorTemplate forecastMonitorTemplate = null;
		// ForecastMonitorTemplateCol instance
		ForecastMonitorTemplateCol forecastMonitorCol = null;
		// ForecastMonitorTemplateColSrc instance
		ForecastMonitorTemplateColSrc forecastMonitorTemplateSrc = null;
		try {
			// debug message
			log.debug(this.getClass().getName()
					+ "-[changeForecastDetails]-Entry");

			// Get hostId from DB
			hostId = SwtUtil.getCurrentHostId();
			// Read templateId from request
			selectedTemplateId = request.getParameter("templateId");
			// Read selected user from request
			selectedUserId = request.getParameter("userId");
			// read display name from request
			selectedDisplayName = request.getParameter("shortName");
			// read type from request
			selectedType = request.getParameter("type");
			// read copiedUserId from request
			request.setAttribute("copiedUserId", request
					.getParameter("copiedUserId"));
			// read copiedTemplateId from request
			request.setAttribute("copiedTemplateId", request
					.getParameter("copiedTemplateId"));

			// read copyFromFlag from request
			request.setAttribute("copyFromFlag", request
					.getParameter("copyFromFlag"));

			// Set template Id in request
			request.setAttribute("templateId", selectedTemplateId);
			// Set template Name in request
			request.setAttribute("templateName", request.getParameter("templateName"));
			// Set user Id in request
			request.setAttribute("userId", selectedUserId);
			// Set public in request
			request.setAttribute("isPublic", request.getParameter("isPublic"));
			// Set displayName
			request.setAttribute("shortName", selectedDisplayName);
			// Set description
			request.setAttribute("description", request.getParameter("description"));
			// Set screen Name as change Screen
			request.setAttribute("type", selectedType);
			// Set screen Name as change Screen
			request.setAttribute("columnId", request.getParameter("columnId"));
			// Set ordinal position
			request.setAttribute("ordinalPos", request
					.getParameter("ordinalPos"));
			// Set ordinal position
			request.setAttribute("screenName", "change");
			// Set modify
			request.setAttribute("modify", request.getParameter("modify"));

			if (SwtUtil.isEmptyOrNull(request.getParameter("loadFlex"))) {
				// debug message
				log.debug(this.getClass().getName()
						+ "-[changeForecastDetails]-Exit to load SWF");
				return ("detailadd");
			}
			// nulifying session col source list
			request.getSession().setAttribute(
					"forecastMonitorTemplateColSrcList", null);

			// set the form object into // DynaValidatorForm
			// get the forecastMonitorTemplate for object
			forecastMonitorTemplateColSrc = (ForecastMonitorTemplateColSrc)getForecastMonitorTemplateColSrc();

			columnTypeList = new ArrayList<LabelValueBean>();
			// Condition to check selected type is normal
			if (selectedType.equals(SwtConstants.NORMAL_LABEL))
				columnTypeLVB = new LabelValueBean(SwtConstants.NORMAL_LABEL,
						SwtConstants.NORMAL);

			columnTypeList.add(columnTypeLVB);

			// Set host Id in bean
			forecastMonitorTemplateColSrc.getId().setHostId(hostId);
			// Set user Id in bean
			forecastMonitorTemplateColSrc.setUserId(selectedUserId);
			// Set template Id in bean
			forecastMonitorTemplateColSrc.getId().setTemplateId(
					selectedTemplateId);

			sessionlistMonitorTemplateDetailsColSrc = new ArrayList<ForecastMonitorTemplateColSrc>();

			forecastMonitorTemplate = (ForecastMonitorTemplate) request
					.getSession().getAttribute("forecastMonitorTemplate");
			// Condition to check forecastMonitorTemplate is not null
			if (forecastMonitorTemplate != null) {

				// get col list from template
				listMonitorTemplateDetailsCol = forecastMonitorTemplate
						.getForecastTemplateColList();
				// Condition to check listMonitorTemplateDetailsCol is not null
				// and not empty
				if (listMonitorTemplateDetailsCol != null
						&& listMonitorTemplateDetailsCol.size() > 0) {
					// iterate the given list
					itrMonitorTemplateDetailsCol = listMonitorTemplateDetailsCol
							.iterator();
					// loop to iterate columns
					while (itrMonitorTemplateDetailsCol.hasNext()) {
						// to get columns
						forecastMonitorCol = itrMonitorTemplateDetailsCol
								.next();

						// chcek whether the given display name is exist in
						// session
						if (selectedDisplayName.equals(forecastMonitorCol
								.getColumnDisplayName())) {

							if (forecastMonitorCol.getTotalMultiplier() != null)
								totalMultiplier = forecastMonitorCol
										.getTotalMultiplier();

							if (forecastMonitorCol
									.getListForecastMonitorTemplateColSrc() != null
									&& forecastMonitorCol
											.getListForecastMonitorTemplateColSrc()
											.size() > 0) {
								// Obtain source list from session
								monitorTemplateColSrcList = forecastMonitorCol
										.getListForecastMonitorTemplateColSrc();

								monitorTemplateColSrcItr = monitorTemplateColSrcList
										.iterator();

								while (monitorTemplateColSrcItr.hasNext()) {
									forecastMonitorTemplateSrc = monitorTemplateColSrcItr
											.next();
									if (!forecastMonitorTemplateSrc
											.getId()
											.getSourceType()
											.equals(
													SwtConstants.TOTAL_COLUMN_SOURCETYPE))
										sessionlistMonitorTemplateDetailsColSrc
												.add(forecastMonitorTemplateSrc);

								}

							}
							request.setAttribute(
									"listMonitorTemplateDetailsColSrc",
									sessionlistMonitorTemplateDetailsColSrc);
							// set session col source list
							request
									.getSession()
									.setAttribute(
											"forecastMonitorTemplateColSrcList",
											forecastMonitorCol
													.getListForecastMonitorTemplateColSrc());
							break;
						}
					}
				}
			}

			if (SwtUtil.isEmptyOrNull(totalMultiplier))
				request.setAttribute("totalMultiplier", "");
			else
				request.setAttribute("totalMultiplier", totalMultiplier);

			setForecastMonitorTemplateColSrc(forecastMonitorTemplateColSrc);
			// set columnTypeList
			request.setAttribute("columnTypeList", columnTypeList);

			// setting reply status as true in request
			request.setAttribute("reply_status_ok", SwtConstants.STR_TRUE);
			// setting the reply message in request
			request.setAttribute("reply_message", SwtConstants.DATA_FETCH_OK);
		} catch (Exception e) {
			log.error(this.getClass().getName()
					+ " - [changeForecastDetails] - Exception -"
					+ e.getStackTrace()[0].getClassName() + "."
					+ e.getStackTrace()[0].getMethodName() + ":"
					+ e.getStackTrace()[0].getLineNumber() + " "
					+ e.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					e, "changeForecastDetails", this.getClass()), request, "");
			request.setAttribute("reply_status_ok", SwtConstants.STR_FALSE);
			request.setAttribute("reply_message", e.getMessage());
			request.setAttribute("reply_location", e.getStackTrace()[0]
					.getClassName()
					+ "."
					+ e.getStackTrace()[0].getMethodName()
					+ ":"
					+ e.getStackTrace()[0].getLineNumber());
			return ("dataerror");
		} finally {
			hostId = null;
			listMonitorTemplateDetailsCol = null;
			itrMonitorTemplateDetailsCol = null;
			columnTypeLVB = null;
			monitorTemplateColSrcList = null;
			monitorTemplateColSrcItr = null;
			log.debug(this.getClass().getName()
					+ " - [changeForecastDetails]-Exit");
		}
		return ("detailaddflex");
	}

	/**
	 * 
	 * Method to load the Add pop screen for normal
	 * 
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return ActionForward
	 * @throws SwtException
	 */
	public String addPopUp()
			throws SwtException {
HttpServletRequest request = ServletActionContext.getRequest();
HttpServletResponse response = ServletActionContext.getResponse();

		// Method's local variable declaration
		// variable to hold hostId
		String hostId = null;
		// variable to hold
		Collection<EntityUserAccess> entityCollection = null;
		// variable to hold
		Collection<LabelValueBean> entityCollectionLVB = null;
		// String variable to hold default entity id
		String entityId = null;
		// Label Value Bean instance
		LabelValueBean columnTypeLVB = null;
		// Collection to hold type list
		List<LabelValueBean> typeList = null;
		// String varible to hold fieldId
		String fieldId = null;
		// String variable to hold fieldName
		String fieldName = null;
		// String variable to hold selected typeId
		String typeId = null;
		// List to hold Book details
		List<BookCode> bookDetailsList = null;
		// List to hold Group details
		List<Group> groupDetailsList = null;
		// List to hold Meta group details
		List<MetaGroup> metaGroupList = null;
		try {

			// debug message
			log.debug(this.getClass().getName() + "-[addPopUp]-Entry");

			// set screen name in request
			request.setAttribute("screenName", request.getParameter("screenName"));
			// set templateId in request
			request.setAttribute("templateId", request
					.getParameter("templateId"));
			// set screen name in request
			request.setAttribute("templateName", request
					.getParameter("templateName"));
			// set screen name in request
			request.setAttribute("userId", request.getParameter("userId"));
			// set screen name in request
			request.setAttribute("columnId", request.getParameter("columnId"));
			// set screen name in request
			request.setAttribute("description", request
					.getParameter("description"));
			// set screen name in request
			request
					.setAttribute("shortName", request
							.getParameter("shortName"));
			// set row count in requesr
			request.setAttribute("detailRowCount", request.getParameter("detailRowCount"));

			// Condition to check loadflex variable is null
			if (SwtUtil.isEmptyOrNull(request.getParameter("loadFlex"))) {
				// debug message
				log.debug(this.getClass().getName()
						+ "-[addPopUp]-Exit to load SWF");
				return ("loadpopup");
			}

			// Get full access Entity Collection
			entityCollection = SwtUtil.getUserEntityAccessList(request
					.getSession());
			// read entity ID from request
			entityId = request.getParameter("entityId");

			if (SwtUtil.isEmptyOrNull(entityId))
				// Get user Default Entity
				entityId = SwtUtil.getUserCurrentEntity(request.getSession());

			/*
			 * Passing the Entity access collection Object and getting the
			 * Entity Id & Name
			 */
			entityCollectionLVB = SwtUtil.convertEntityAcessCollectionLVL(
					entityCollection, request.getSession());

			// List to hold types
			typeList = new ArrayList<LabelValueBean>();
			columnTypeLVB = new LabelValueBean(SwtConstants.BOOK_LABEL,
					SwtConstants.BOOK_LABEL);
			typeList.add(columnTypeLVB);
			columnTypeLVB = new LabelValueBean(SwtConstants.ENTITY_LABEL,
					SwtConstants.ENTITY_LABEL);
			typeList.add(columnTypeLVB);
			columnTypeLVB = new LabelValueBean(SwtConstants.GROUP_LABEL,
					SwtConstants.GROUP_LABEL);
			typeList.add(columnTypeLVB);
			columnTypeLVB = new LabelValueBean(SwtConstants.META_GROUP_LABEL,
					SwtConstants.META_GROUP_LABEL);
			typeList.add(columnTypeLVB);

			// Get host Id from Data base
			hostId = SwtUtil.getCurrentHostId();

			// read fieldId from request
			fieldId = request.getParameter("fieldId");

			// read fieldName from request
			fieldName = request.getParameter("fieldName");

			// Read selected type id from request
			typeId = request.getParameter("typeId");

			// Set values in request
			request.setAttribute("typeList", typeList);
			request.setAttribute("entityCollection", entityCollectionLVB);
			request.setAttribute("entityId", entityId);
			request.setAttribute("entities", entityCollection);
			request.setAttribute("typeId", typeId);

			/* Condition to check typeId */
			if (!SwtUtil.isEmptyOrNull(typeId)) {

				// Condition to check typeId is BOOK
				if (typeId.equals(SwtConstants.BOOK_LABEL)) {
					// Get book list
					bookDetailsList = forecastMonitorTemplateManager
							.getBookCollection(hostId, entityId, fieldId,
									fieldName);
					// set book list in request
					request.setAttribute("bookDetailsList", bookDetailsList);
					// set meta group list in request
					request.setAttribute("metaGroupList", null);
					// set group list in request
					request.setAttribute("groupDetailsList", null);
					// Condition to check typeId is Group
				} else if (typeId.equals(SwtConstants.GROUP_LABEL)) {
					// Get group list
					groupDetailsList = forecastMonitorTemplateManager
							.getGroupDetails(hostId, entityId, fieldId,
									fieldName);

					// set book list in request
					request.setAttribute("bookDetailsList", null);
					// set meta group list in request
					request.setAttribute("metaGroupList", null);
					// set group list in request
					request.setAttribute("groupDetailsList", groupDetailsList);

					// Condition to check typeId is Meta Group
				} else if (typeId.equals(SwtConstants.META_GROUP_LABEL)) {
					// Get meta group list
					metaGroupList = forecastMonitorTemplateManager
							.getMetaGroupDetails(hostId, entityId, fieldId,
									fieldName);
					// set book list in request
					request.setAttribute("bookDetailsList", null);
					// set meta group list in request
					request.setAttribute("metaGroupList", null);
					// set meta group list in request
					request.setAttribute("metaGroupList", metaGroupList);
				}
			}

			// setting reply status as true in request
			request.setAttribute("reply_status_ok", SwtConstants.STR_TRUE);
			// setting the reply message in request
			request.setAttribute("reply_message", SwtConstants.DATA_FETCH_OK);
		} catch (SwtException e) {
			log.error(this.getClass().getName() + " - [addPopUp] - Exception -"
					+ e.getStackTrace()[0].getClassName() + "."
					+ e.getStackTrace()[0].getMethodName() + ":"
					+ e.getStackTrace()[0].getLineNumber() + " "
					+ e.getMessage());
			request.setAttribute("reply_status_ok", SwtConstants.STR_FALSE);
			request.setAttribute("reply_message", e.getMessage());
			request.setAttribute("reply_location", e.getStackTrace()[0]
					.getClassName()
					+ "."
					+ e.getStackTrace()[0].getMethodName()
					+ ":"
					+ e.getStackTrace()[0].getLineNumber());
			SwtUtil.logException(e, request, "");
			return ("dataerror");
		} catch (Exception e) {
			log.error(this.getClass().getName() + " - [addPopUp] - Exception -"
					+ e.getStackTrace()[0].getClassName() + "."
					+ e.getStackTrace()[0].getMethodName() + ":"
					+ e.getStackTrace()[0].getLineNumber() + " "
					+ e.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					e, "addPopUp", this.getClass()), request, "");
			request.setAttribute("reply_status_ok", SwtConstants.STR_FALSE);
			request.setAttribute("reply_message", e.getMessage());
			request.setAttribute("reply_location", e.getStackTrace()[0]
					.getClassName()
					+ "."
					+ e.getStackTrace()[0].getMethodName()
					+ ":"
					+ e.getStackTrace()[0].getLineNumber());
			return ("dataerror");
		} finally {
			hostId = null;
			columnTypeLVB = null;
			fieldId = null;
			fieldName = null;
			log.debug(this.getClass().getName() + " - [addPopUp] - Exit");
		}
		return ("loadpopupdata");
	}

	/**
	 * 
	 * Method to load the Add pop screen for subtotal
	 * 
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return ActionForward
	 * @throws SwtException
	 */

	@SuppressWarnings("unchecked")
	public String addPopUpForSubtotal() throws SwtException {
HttpServletRequest request = ServletActionContext.getRequest();
HttpServletResponse response = ServletActionContext.getResponse();

		// Method's local variable declaration
		// String variable to hold fieldId
		String fieldId = null;
		// String variable to hold fieldName
		String fieldName = null;
		// String to hold selected Column display Name
		String selectedDisplayName = null;
		// String to hold selected column number
		String selectedColumnNo = null;
		// forecastMonitorTemplateCol bean
		ForecastMonitorTemplateCol forecastMonitorTemplateCol = null;
		/* Variable Declaration for collMonitorDetails */
		List<ForecastMonitorTemplateCol> listMonitorTemplateDetailsCol = null;
		/* Variable Declaration for dyForm */
		// DynaValidatorForm dyForm = null;
		// variable to hold Pressed button
		String pressedButton = null;
		// To get sessionlistMonitorTemplateDetailsCol
		Iterator<ForecastMonitorTemplateCol> itrSessionMonitorTempDetailCol = null;
		// To get sessionlistMonitorTemplateDetailsColSrc
		ArrayList<ForecastMonitorTemplateCol> sessionListMonitorTempDetailToDisplay = null;
		// Ordinal position
		int ordinalPos;
		// Forecast template instance
		ForecastMonitorTemplate forecastMonitorTemplate = null;
		try {

			// debug message
			log.debug(this.getClass().getName()
					+ "-[addPopUpForSubtotal]-Entry");

			// read pressed button from request
			pressedButton = request.getParameter("pressedbutton");
			// set screen name in request
			request.setAttribute("screenName", request.getParameter("screenName"));
			// read display name from request
			selectedDisplayName = request.getParameter("shortName");
			// read type from request
			selectedColumnNo = request.getParameter("columnId");
			// Set template Id in request
			request.setAttribute("templateId", request.getParameter("templateId"));
			// Set template Name in request
			request.setAttribute("templateName", request.getParameter("templateName"));
			// Set user Id in request
			request.setAttribute("userId", request.getParameter("userId"));
			// Set public in request
			request.setAttribute("isPublic", request.getParameter("isPublic"));
			// Set displayName
			request.setAttribute("shortName", selectedDisplayName);
			// Set description
			request.setAttribute("description", request.getParameter("description"));
			// Set selected column type
			request.setAttribute("type", request.getParameter("type"));
			// Set selected column id
			request.setAttribute("columnId", selectedColumnNo);
			// Set ordinal position
			request.setAttribute("ordinalPos",  request.getParameter("ordinalPos"));
			// Set ordinal pressedbutton
			request.setAttribute("pressedbutton", pressedButton);
			// set row count in requesr
			request.setAttribute("detailRowCount", request.getParameter("detailRowCount"));

			// Condition to check loadflex variable is null
			if (SwtUtil.isEmptyOrNull(request.getParameter("loadFlex"))) {
				// debug message
				log.debug(this.getClass().getName()
						+ "-[addPopUpForSubtotal]-Exit to load SWF");
				return ("loadsubtotalpopup");
			}

			// set the form object into // DynaValidatorForm
			// get the forecastMonitorTemplate for object
			forecastMonitorTemplateCol = (ForecastMonitorTemplateCol) getForecastMonitorTemplateCol();

			// get forecast template from session
			forecastMonitorTemplate = (ForecastMonitorTemplate) request
					.getSession().getAttribute("forecastMonitorTemplate");

			// read fieldId from request
			fieldId = request.getParameter("fieldId");

			// read fieldName from request
			fieldName = request.getParameter("fieldName");

			sessionListMonitorTempDetailToDisplay = new ArrayList<ForecastMonitorTemplateCol>();

			// Condition to check session valules is empty
			if (forecastMonitorTemplate != null) {

				// get column list form session
				listMonitorTemplateDetailsCol = forecastMonitorTemplate
						.getForecastTemplateColList();
				// Iterate column list
				itrSessionMonitorTempDetailCol = listMonitorTemplateDetailsCol
						.iterator();
				// loop to get forecast columns
				while (itrSessionMonitorTempDetailCol.hasNext()) {
					// get forecast column
					forecastMonitorTemplateCol = itrSessionMonitorTempDetailCol
							.next();
					// Condition to check the selected name is not being
					// displayed in list
					if (!forecastMonitorTemplateCol.getColumnDisplayName()
							.equals(selectedDisplayName)
							&& !forecastMonitorTemplateCol.getColumnType()
									.equals(SwtConstants.FIXED_LABEL)) {
						// Condition to check add pbutton is pressed to load all
						// user defined data
						if (SwtUtil.isEmptyOrNull(pressedButton)
								|| (!SwtUtil.isEmptyOrNull(pressedButton) && pressedButton
										.equalsIgnoreCase("add"))) {
							// get all data
							if (SwtUtil.isEmptyOrNull(fieldId)
									&& SwtUtil.isEmptyOrNull(fieldName)) {
								sessionListMonitorTempDetailToDisplay
										.add(forecastMonitorTemplateCol);
							} else {
								// get selected data given in field id or name
								if ((!SwtUtil.isEmptyOrNull(fieldId) && (forecastMonitorTemplateCol
										.getColumnDisplayName()
										.contains(fieldId)))
										|| (!SwtUtil.isEmptyOrNull(fieldName) && (forecastMonitorTemplateCol
												.getColumnDescription()
												.contains(fieldName)))) {
									sessionListMonitorTempDetailToDisplay
											.add(forecastMonitorTemplateCol);
								}

							}
						} else {
							// get suggest data
							if (!SwtUtil.isEmptyOrNull(selectedColumnNo)) {
								ordinalPos = Integer.parseInt(selectedColumnNo);
								if (ordinalPos > forecastMonitorTemplateCol
										.getOrdinalPos()) {
									if (SwtUtil.isEmptyOrNull(fieldId)
											&& SwtUtil.isEmptyOrNull(fieldName)) {
										sessionListMonitorTempDetailToDisplay
												.add(forecastMonitorTemplateCol);
									} else {
										if ((!SwtUtil.isEmptyOrNull(fieldId) && (forecastMonitorTemplateCol
												.getColumnDisplayName()
												.contains(fieldId)))
												|| (!SwtUtil
														.isEmptyOrNull(fieldName) && (forecastMonitorTemplateCol
														.getColumnDescription()
														.contains(fieldName)))) {
											sessionListMonitorTempDetailToDisplay
													.add(forecastMonitorTemplateCol);
										}
									}
								}
							}
						}
					}
				}
			}

			// Set values in request
			request.setAttribute("typeList", "");
			request.setAttribute("entityCollection", "");
			request.setAttribute("entityId", "");
			request.setAttribute("fieldId", fieldId);
			request.setAttribute("fieldName", fieldName);
			request.setAttribute("entities", "");
			request.setAttribute("typeId", "");
			request.setAttribute("listMonitorTemplateDetailsCol",
					sessionListMonitorTempDetailToDisplay);

			// setting reply status as true in request
			request.setAttribute("reply_status_ok", SwtConstants.STR_TRUE);
			// setting the reply message in request
			request.setAttribute("reply_message", SwtConstants.DATA_FETCH_OK);

		} catch (Exception e) {
			log.error(this.getClass().getName()
					+ " - [addPopUpForSubtotal] - Exception -"
					+ e.getStackTrace()[0].getClassName() + "."
					+ e.getStackTrace()[0].getMethodName() + ":"
					+ e.getStackTrace()[0].getLineNumber() + " "
					+ e.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					e, "addPopUp", this.getClass()), request, "");
			request.setAttribute("reply_status_ok", SwtConstants.STR_FALSE);
			request.setAttribute("reply_message", e.getMessage());
			request.setAttribute("reply_location", e.getStackTrace()[0]
					.getClassName()
					+ "."
					+ e.getStackTrace()[0].getMethodName()
					+ ":"
					+ e.getStackTrace()[0].getLineNumber());
			return ("dataerror");
		} finally {
			fieldId = null;
			fieldName = null;
			itrSessionMonitorTempDetailCol = null;
			forecastMonitorTemplateCol = null;

			log.debug(this.getClass().getName()
					+ " - [addPopUpForSubtotal] - Exit");
		}
		return ("loadsubtotalpopupflexdata");
	}

	/**
	 * 
	 * Method to load the Add Subtotal data grid in Add a column to forecast
	 * monitor Template screen
	 * 
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return ActionForward
	 * @throws SwtException
	 */

	public String loadSubtotalAdd() throws SwtException {
HttpServletRequest request = ServletActionContext.getRequest();
HttpServletResponse response = ServletActionContext.getResponse();

		// Method's local variable declaration
		/* Variable Declaration for dyForm */
		// DynaValidatorForm dyForm = null;
		// Array list to hold User Collection
		ArrayList<LabelValueBean> columnTypeList = null;
		// Label Value Bean instance
		LabelValueBean columnTypeLVB = null;
		/* Variable Declaration for collMonitorDetails */
		List<ForecastMonitorTemplateCol> listMonitorTemplateDetailsColSrc = null;
		// Insatnce for forecast Monitor template
		ForecastMonitorTemplateColSrc forecastMonitorTemplateColSrc = null;
		// String to hold selectedTemplateId
		String selectedTemplateId = null;
		// String to hold selectedTemplateName
		String selectedTemplateName = null;
		// String to hold isPublic
		String isPublic = null;
		// String to hold selectedUserId
		String selectedUserId = null;
		// String to hold selectedColumnName
		String selectedDisplayName = null;
		// String to hold selectedDescription
		String selectedDescription = null;
		// String to hold selectedDescription
		String selectedOrdinalPosition = null;
		// String to hold selectedDescription
		String selectedColumnNo = null;
		// String to hold selectedDescription
		String selectedType = null;
		// String to hold hostId
		String hostId = null;
		try {
			// debug message
			log.debug(this.getClass().getName() + "-[loadSubtotalAdd]-Entry");
			// Get hostId from DB
			hostId = SwtUtil.getCurrentHostId();
			// Read templateId from request
			selectedTemplateId = request.getParameter("templateId");
			// Read templateName from request
			selectedTemplateName = request.getParameter("templateName");
			// Read selected user from request
			selectedUserId = request.getParameter("userId");
			// Read is public from request
			isPublic = request.getParameter("isPublic");
			// read display name from request
			selectedDisplayName = request.getParameter("shortName");
			// read display name from request
			selectedDescription = request.getParameter("description");
			// read type from request
			selectedType = request.getParameter("type");
			// read type from request
			selectedColumnNo = request.getParameter("columnId");
			// read selectedOrdinalPosition from request
			selectedOrdinalPosition = request.getParameter("ordinalPos");
			// set selectedType in request
			request.setAttribute("type", selectedType);
			// Set template Id in request
			request.setAttribute("templateId", SwtUtil
					.isEmptyOrNull(selectedTemplateId) ? ""
					: selectedTemplateId);
			// Set template Name in request
			request.setAttribute("templateName", SwtUtil
					.isEmptyOrNull(selectedTemplateName) ? ""
					: selectedTemplateName);
			// Set user Id in request
			request.setAttribute("userId", SwtUtil
					.isEmptyOrNull(selectedUserId) ? "" : selectedUserId);
			// Set public in request
			request.setAttribute("isPublic",
					SwtUtil.isEmptyOrNull(isPublic) ? "" : isPublic);
			// Set displayName
			request.setAttribute("shortName", SwtUtil
					.isEmptyOrNull(selectedDisplayName) ? ""
					: selectedDisplayName);
			// Set description
			request.setAttribute("description", SwtUtil
					.isEmptyOrNull(selectedDescription) ? ""
					: selectedDescription);
			// Set screen Name as change Screen
			request.setAttribute("type",
					SwtUtil.isEmptyOrNull(selectedType) ? "" : selectedType);
			// Set screen Name as change Screen
			request.setAttribute("columnId", "");
			// Set total multiplier as change Screen
			request.setAttribute("totalMultiplier", "");
			// Set ordinal position
			request.setAttribute("ordinalPos", SwtUtil
					.isEmptyOrNull(selectedOrdinalPosition) ? ""
					: selectedOrdinalPosition);
			// set the form object into // DynaValidatorForm
			// get the forecastMonitorTemplate for object
			forecastMonitorTemplateColSrc = (ForecastMonitorTemplateColSrc) getForecastMonitorTemplateColSrc();
			// nullifying session col source list
			request.getSession().setAttribute(
					"forecastMonitorTemplateColSrcList", null);
			columnTypeList = new ArrayList<LabelValueBean>();
			columnTypeLVB = new LabelValueBean(SwtConstants.NORMAL_LABEL,
					SwtConstants.NORMAL);
			columnTypeList.add(columnTypeLVB);
			columnTypeLVB = new LabelValueBean(SwtConstants.SUB_TOTAL_LABEL,
					SwtConstants.SUB_TOTAL);
			columnTypeList.add(columnTypeLVB);

			request.setAttribute("selectedColumnType",
					SwtConstants.SUB_TOTAL_LABEL);
			// Set host Id in bean
			forecastMonitorTemplateColSrc.getId().setHostId(hostId);
			// Set user Id in bean
			forecastMonitorTemplateColSrc.setUserId(selectedUserId);
			// Set template Id in bean
			forecastMonitorTemplateColSrc.getId().setTemplateId(
					selectedTemplateId);
			// Set template Id in bean
			forecastMonitorTemplateColSrc.getId().setColumnId(selectedColumnNo);
			// Set source Id in bean
			forecastMonitorTemplateColSrc.getId().setSourceId(selectedType);
			// Get Template columns for selected template ID
			listMonitorTemplateDetailsColSrc = forecastMonitorTemplateManager
					.getChangeForecastMonitorTemplateColSrcForSubTotal(forecastMonitorTemplateColSrc);
			request.setAttribute("listMonitorTemplateDetailsColSrc",
					listMonitorTemplateDetailsColSrc);
			request.setAttribute("columnTypeList", columnTypeList);
			// set form object
			setForecastMonitorTemplateColSrc(forecastMonitorTemplateColSrc);
			// setting reply status as true in request
			request.setAttribute("reply_status_ok", SwtConstants.STR_TRUE);
			// setting the reply message in request
			request.setAttribute("reply_message", SwtConstants.DATA_FETCH_OK);
		} catch (SwtException e) {
			log.error(this.getClass().getName()
					+ " - [loadSubtotalAdd] - Exception -"
					+ e.getStackTrace()[0].getClassName() + "."
					+ e.getStackTrace()[0].getMethodName() + ":"
					+ e.getStackTrace()[0].getLineNumber() + " "
					+ e.getMessage());
			request.setAttribute("reply_status_ok", SwtConstants.STR_FALSE);
			request.setAttribute("reply_message", e.getMessage());
			request.setAttribute("reply_location", e.getStackTrace()[0]
					.getClassName()
					+ "."
					+ e.getStackTrace()[0].getMethodName()
					+ ":"
					+ e.getStackTrace()[0].getLineNumber());
			SwtUtil.logException(e, request, "");
			return ("dataerror");
		} catch (Exception e) {
			log.error(this.getClass().getName()
					+ " - [loadSubtotalAdd] - Exception -"
					+ e.getStackTrace()[0].getClassName() + "."
					+ e.getStackTrace()[0].getMethodName() + ":"
					+ e.getStackTrace()[0].getLineNumber() + " "
					+ e.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					e, "loadSubtotalAdd", this.getClass()), request, "");
			request.setAttribute("reply_status_ok", SwtConstants.STR_FALSE);
			request.setAttribute("reply_message", e.getMessage());
			request.setAttribute("reply_location", e.getStackTrace()[0]
					.getClassName()
					+ "."
					+ e.getStackTrace()[0].getMethodName()
					+ ":"
					+ e.getStackTrace()[0].getLineNumber());
			return ("dataerror");
		} finally {
			columnTypeLVB = null;
			log.debug(this.getClass().getName()
					+ " - [loadSubtotalAdd] - Exit");
		}
		return ("loadsubtotalflexdata");
	}

	/**
	 * 
	 * Method to refresh the grid with added column sources for sub total /
	 * total
	 * 
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return ActionForward
	 * @throws SwtException
	 */
	@SuppressWarnings("unchecked")
	public String refreshSubDetail() throws SwtException {
HttpServletRequest request = ServletActionContext.getRequest();
HttpServletResponse response = ServletActionContext.getResponse();

		// Array list to hold User Collection
		ArrayList<LabelValueBean> columnTypeList = null;
		// Label Value Bean instance
		LabelValueBean columnTypeLVB = null;
		// list forecast source list
		List<ForecastMonitorTemplateColSrc> monitorTemplateColSrcList = null;
		// selectedDisplayName
		String selectedDisplayName = null;
		// list to hold columns
		List<ForecastMonitorTemplateCol> listMonitorTemplateDetailsColTODisplay = null;
		// ForecastMonitorTemplate instance
		ForecastMonitorTemplate sessionTemplat = null;
		try {
			log.debug(this.getClass().getName()
					+ " - [refreshSubDetail] - Entry");
			request
					.setAttribute("shortName", request
							.getParameter("shortName"));
			// Set description
			request.setAttribute("description", request
					.getParameter("description"));
			// Set type as SwtConstants.NORMAL_LABEL
			request.setAttribute("type", request.getParameter("type"));
			// Set column id
			request.setAttribute("columnId", request.getParameter("columnId"));
			// Set ordinal position
			request.setAttribute("ordinalPos", request
					.getParameter("ordinalPos"));
			// Set total multiplier
			request.setAttribute("totalMultiplier", request
					.getParameter("totalMultiplier"));
			columnTypeList = new ArrayList<LabelValueBean>();
			columnTypeLVB = new LabelValueBean(SwtConstants.NORMAL_LABEL,
					SwtConstants.NORMAL);
			columnTypeList.add(columnTypeLVB);
			columnTypeLVB = new LabelValueBean(SwtConstants.SUB_TOTAL_LABEL,
					SwtConstants.SUB_TOTAL);
			columnTypeList.add(columnTypeLVB);
			// Set column type list
			request.setAttribute("columnTypeList", columnTypeList);
			// selectedDisplayName
			selectedDisplayName = request.getParameter("shortName");
		
			monitorTemplateColSrcList = (List<ForecastMonitorTemplateColSrc>) request
					.getSession().getAttribute(
							"forecastMonitorTemplateColSrcList");
			// instantiate listMonitorTemplateDetailsColTODisplay
			listMonitorTemplateDetailsColTODisplay = new ArrayList<ForecastMonitorTemplateCol>();
			// Obtain template from session
			sessionTemplat = (ForecastMonitorTemplate) request.getSession()
					.getAttribute("forecastMonitorTemplate");
			// loop to get column values
			for (ForecastMonitorTemplateCol forecastMonitorTemplateCol : sessionTemplat
					.getForecastTemplateColList()) {
				// get sources from session
				for (ForecastMonitorTemplateColSrc forecastMonitorTemplateColSrc : monitorTemplateColSrcList) {
					// Condition to check column display name is equal to entity
					if (forecastMonitorTemplateCol.getColumnDisplayName().equals(forecastMonitorTemplateColSrc.getEntityId()) ) {
						// Condition to check column id is total to not add that
						// in
						// collection
						if (!(!SwtUtil
								.isEmptyOrNull(forecastMonitorTemplateColSrc
										.getId().getColumnId())
								&& forecastMonitorTemplateColSrc.getId()
										.getColumnId().equalsIgnoreCase(
												SwtConstants.TOTAL)
								&& selectedDisplayName
										.equals(forecastMonitorTemplateColSrc
												.getEntityId()))) {
							forecastMonitorTemplateCol
									.setMultiplier(forecastMonitorTemplateColSrc
											.getMultiplier());
							listMonitorTemplateDetailsColTODisplay
									.add(forecastMonitorTemplateCol);
						} 
					}

				}
			}
			request.setAttribute("listMonitorTemplateDetailsColSrc", listMonitorTemplateDetailsColTODisplay);
			request.setAttribute("recordCount", listMonitorTemplateDetailsColTODisplay.size());
			// setting reply status as true in request
			request.setAttribute("reply_status_ok", SwtConstants.STR_TRUE);
			// setting the reply message in request
			request.setAttribute("reply_message", SwtConstants.DATA_FETCH_OK);
		} catch (Exception e) {
			log.error(this.getClass().getName()
					+ " - [refreshSubDetail] - Exception -"
					+ e.getStackTrace()[0].getClassName() + "."
					+ e.getStackTrace()[0].getMethodName() + ":"
					+ e.getStackTrace()[0].getLineNumber() + " "
					+ e.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					e, "refreshSubDetail", this.getClass()), request, "");
			request.setAttribute("reply_status_ok", SwtConstants.STR_FALSE);
			request.setAttribute("reply_message", e.getMessage());
			request.setAttribute("reply_location", e.getStackTrace()[0]
					.getClassName()
					+ "."
					+ e.getStackTrace()[0].getMethodName()
					+ ":"
					+ e.getStackTrace()[0].getLineNumber());
			return ("dataerror");
		} finally {
			columnTypeLVB = null;
			log.debug(this.getClass().getName()
					+ " - [refreshSubDetail] - Exit");
		}
		return ("loadsubtotalflexdata");
	}

	/**
	 * 
	 * Method to load the change Subtotal data grid in forecast monitor details
	 * screen
	 * 
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return ActionForward
	 * @throws SwtException
	 */

	@SuppressWarnings("unchecked")
	public String loadSubtotalChange() throws SwtException {
HttpServletRequest request = ServletActionContext.getRequest();
HttpServletResponse response = ServletActionContext.getResponse();

		// Method's local variable declaration
		/* Variable Declaration for dyForm */
		// DynaValidatorForm dyForm = null;
		/* Variable Declaration for collMonitorDetails */
		List<ForecastMonitorTemplateCol> listMonitorTemplateDetailsCol = null;
		/* Variable Declaration for collMonitorDetails */
		ArrayList<ForecastMonitorTemplateCol> listMonitorTemplateDetailsColTODisplay = null;
		/* Variable Declaration for collMonitorDetails */
		Iterator<ForecastMonitorTemplateCol> itrMonitorTemplateDetailsCol = null;
		// Insatnce for forecast Monitor template
		ForecastMonitorTemplateColSrc forecastMonitorTemplateColSrc = null;
		// Array list to hold User Collection
		ArrayList<LabelValueBean> columnTypeList = null;
		// Label Value Bean instance
		LabelValueBean columnTypeLVB = null;
		// String to hold selectedTemplateId
		String selectedTemplateId = null;
		// String to hold selectedUserId
		String selectedUserId = null;
		// String to hold selectedColumnName
		String selectedDisplayName = null;
		// String to hold selectedDescription
		String selectedColumnNo = null;
		// String to hold selectedDescription
		String selectedType = null;
		// String to hold hostId
		String hostId = null;
		// Long to hold TotalMultiplier
		String totalMultiplier = null;
		// Insatnce for forecast Monitor template
		ForecastMonitorTemplate forecastMonitorTemplate = null;
		// Array list to get ForecastMonitorTemplateColSrc values
		List<ForecastMonitorTemplateColSrc> monitorTemplateColSrcList = null;
		// Insatnce for forecast Monitor template
		ForecastMonitorTemplateCol forecastMonitorTemplateCol = null;
		try {
			// debug message
			log.debug(this.getClass().getName()
							+ "-[loadSubtotalChange]-Entry");
			// Get hostId from DB
			hostId = SwtUtil.getCurrentHostId();

			// Read templateId from request
			selectedTemplateId = request.getParameter("templateId");

			// Read selected user from request
			selectedUserId = request.getParameter("userId");

			// read display name from request
			selectedDisplayName = request.getParameter("shortName");
			// read type from request
			selectedType = request.getParameter("type");
			// read type from request
			selectedColumnNo = request.getParameter("columnId");
			// read copiedUserId from request
			request.setAttribute("copiedUserId",  request.getParameter("copiedUserId"));
			// read copiedTemplateId from request
			request.setAttribute("copiedTemplateId", request.getParameter("copiedTemplateId"));

			// read copyFromFlag from request
			request.setAttribute("copyFromFlag", request.getParameter("copyFromFlag"));

			// Set template Id in request
			request.setAttribute("templateId", selectedTemplateId);
			// Set template Name in request
			request.setAttribute("templateName", request.getParameter("templateName"));
			// Set user Id in request
			request.setAttribute("userId", selectedUserId);
			// Set public in request
			request.setAttribute("isPublic", request.getParameter("isPublic"));
			// Set displayName
			request.setAttribute("shortName", selectedDisplayName);
			// Set description
			request.setAttribute("description", request.getParameter("description"));
			// Set selected type
			request.setAttribute("type", selectedType);
			// Set column number
			request.setAttribute("columnId", selectedColumnNo);
			// Set ordinal position
			request.setAttribute("ordinalPos", request.getParameter("ordinalPos"));
			// Set screen name
			request.setAttribute("screenName", "change");
			// Set modify
			request.setAttribute("modify", request.getParameter("modify"));

			if (SwtUtil.isEmptyOrNull(request.getParameter("loadFlex"))) {
				// debug message
				log.debug(this.getClass().getName()
						+ "-[loadSubtotalChange]-Exit to load SWF");
				return ("detailadd");
			}
			
			// set the form object into // DynaValidatorForm
			// get the forecastMonitorTemplate for object
			forecastMonitorTemplateColSrc = (ForecastMonitorTemplateColSrc)getForecastMonitorTemplateColSrc();

			// nulifying session col source list
			request.getSession().setAttribute(
					"forecastMonitorTemplateColSrcList", null);

			columnTypeList = new ArrayList<LabelValueBean>();
			// Condition to check selected type is normal
			if (selectedType.equals(SwtConstants.NORMAL_LABEL))
				columnTypeLVB = new LabelValueBean(SwtConstants.NORMAL_LABEL,
						SwtConstants.NORMAL);
			// Condition to check selected type is subtotal
			else if (selectedType.equals(SwtConstants.SUB_TOTAL_LABEL))
				columnTypeLVB = new LabelValueBean(SwtConstants.SUB_TOTAL_LABEL,
						SwtConstants.SUB_TOTAL);

			// Condition to check selected type is fixed
			else if (selectedType.equals(SwtConstants.ENTITY_LABEL))
				columnTypeLVB = new LabelValueBean(SwtConstants.ENTITY_LABEL,
						SwtConstants.ENTITY);
			else
				columnTypeLVB = new LabelValueBean(SwtConstants.TOTAL, SwtConstants.TOTAL);

			columnTypeList.add(columnTypeLVB);

			// Set host Id in bean
			forecastMonitorTemplateColSrc.getId().setHostId(hostId);
			// Set user Id in bean
			forecastMonitorTemplateColSrc.setUserId(selectedUserId);
			// Set template Id in bean
			forecastMonitorTemplateColSrc.getId().setTemplateId(
					selectedTemplateId);

			// Set template Id in bean
			forecastMonitorTemplateColSrc.getId().setColumnId(selectedColumnNo);

			// Condition to check selected type is sub total
			if (selectedType.equals(SwtConstants.SUB_TOTAL_LABEL)
					|| selectedType.equals(SwtConstants.TOTAL)) {
				// set selected type as C for column source
				selectedType = SwtConstants.TOTAL_COLUMN_SOURCETYPE;
				// Condition to check selected type is normal label
			} else{
				// set selected type as N
				selectedType = SwtConstants.NORMAL;
			}
			// Set template Id in bean
			forecastMonitorTemplateColSrc.getId().setSourceType(selectedType);

			// get from session forecastMonitorTemplate
			forecastMonitorTemplate = (ForecastMonitorTemplate) request
					.getSession().getAttribute("forecastMonitorTemplate");

			// instantiate
			// listMonitorTemplateDetailsColTODisplay
			listMonitorTemplateDetailsColTODisplay = new ArrayList<ForecastMonitorTemplateCol>();

			// Condition to check session valules is empty
			if (forecastMonitorTemplate != null
					&& forecastMonitorTemplate.getForecastTemplateColList() != null
					&& forecastMonitorTemplate.getForecastTemplateColList()
							.size() > 0) {

				listMonitorTemplateDetailsCol = forecastMonitorTemplate
						.getForecastTemplateColList();

				// iterate the column values
				itrMonitorTemplateDetailsCol = listMonitorTemplateDetailsCol
						.iterator();
				// loop to iterate values
				while (itrMonitorTemplateDetailsCol.hasNext()) {
					forecastMonitorTemplateCol = itrMonitorTemplateDetailsCol
							.next();
					if (forecastMonitorTemplateCol.getColumnDisplayName()
							.equals(selectedDisplayName)) {
						// gett total multiplier
						totalMultiplier = forecastMonitorTemplateCol
								.getTotalMultiplier();

						if (forecastMonitorTemplateCol
								.getListForecastMonitorTemplateColSrc() != null
								&& forecastMonitorTemplateCol
										.getListForecastMonitorTemplateColSrc()
										.size() > 0) {

							monitorTemplateColSrcList = forecastMonitorTemplateCol
									.getListForecastMonitorTemplateColSrc();

							// loop to get column values
							for (ForecastMonitorTemplateCol forecastTemplateCol : forecastMonitorTemplate
									.getForecastTemplateColList()) {
								// get sources from session
								for (ForecastMonitorTemplateColSrc forecastTemplateColSrc : monitorTemplateColSrcList) {
									// Condition to check column display name is
									// equal to entity
									if (forecastTemplateCol
											.getColumnDescription().equals(
													forecastTemplateColSrc
															.getName())) {
										// Condition to check column id is total
										// to not add that in
										// collection
										if (!(selectedDisplayName
												.equals(forecastTemplateColSrc
														.getEntityId())
												&& !SwtUtil
														.isEmptyOrNull(forecastTemplateColSrc
																.getId()
																.getColumnId()) && forecastTemplateColSrc
												.getId().getColumnId()
												.equalsIgnoreCase(
														SwtConstants.TOTAL))) {
											forecastTemplateCol
													.setMultiplier(forecastTemplateColSrc
															.getMultiplier());
											listMonitorTemplateDetailsColTODisplay
													.add(forecastTemplateCol);
										}
									}
								}
							}

						}
						// set session col source list
						request.getSession().setAttribute(
								"forecastMonitorTemplateColSrcList",
								monitorTemplateColSrcList);
						break;
					}
				}
			}

			if (SwtUtil.isEmptyOrNull(totalMultiplier))
				request.setAttribute("totalMultiplier", "");
			else
				request.setAttribute("totalMultiplier", totalMultiplier);
			request.setAttribute("columnTypeList", columnTypeList);

			request.setAttribute("listMonitorTemplateDetailsColSrc",
					listMonitorTemplateDetailsColTODisplay);

			// set form object
			setForecastMonitorTemplateColSrc(forecastMonitorTemplateColSrc);

			// setting reply status as true in request
			request.setAttribute("reply_status_ok", SwtConstants.STR_TRUE);
			// setting the reply message in request
			request.setAttribute("reply_message", SwtConstants.DATA_FETCH_OK);
		} catch (Exception e) {
			log.error(this.getClass().getName()
					+ " - [loadSubtotalChange] - Exception -"
					+ e.getStackTrace()[0].getClassName() + "."
					+ e.getStackTrace()[0].getMethodName() + ":"
					+ e.getStackTrace()[0].getLineNumber() + " "
					+ e.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					e, "loadSubtotalChange", this.getClass()), request, "");
			request.setAttribute("reply_status_ok", SwtConstants.STR_FALSE);
			request.setAttribute("reply_message", e.getMessage());
			request.setAttribute("reply_location", e.getStackTrace()[0]
					.getClassName()
					+ "."
					+ e.getStackTrace()[0].getMethodName()
					+ ":"
					+ e.getStackTrace()[0].getLineNumber());
			return ("dataerror");
		} finally {
			log.debug(this.getClass().getName()
					+ " - [loadSubtotalChange] - Exit");
		}
		return ("loadsubtotalflexdata");
	}

	/**
	 * 
	 * Method called when click on delete button Forecast monitor Template
	 * screen to delete selected Forecast Monitor Template
	 * 
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return ActionForward
	 * @throws SwtException
	 */

	public String deleteMonitorTemplate() throws SwtException {
HttpServletRequest request = ServletActionContext.getRequest();
HttpServletResponse response = ServletActionContext.getResponse();

		// Method's local Variable declaration
		// Variable to hold HostId
		String hostId = null;
		// Variable to hold userId
		String userId = null;
		// Variable to hold templateId
		String templateId = null;
		// ForecastMonitorTemplate Instance
		ForecastMonitorTemplate forecastMonitorTemplate = null;
		/* Variable Declaration for dyForm */
		// DynaValidatorForm dyForm = null;

		try {
			// debug message
			log.debug(this.getClass().getName()
					+ "-[deleteMonitorTemplate]-Entry");
			// set the form object into // DynaValidatorForm
			// get the forecastMonitorTemplate for object
			forecastMonitorTemplate = (ForecastMonitorTemplate)getForecastMonitorTemplate();
			// Obtain host Id from DB
			hostId = SwtUtil.getCurrentHostId();
			// Read templateId from request
			templateId = request.getParameter("templateId");
			// Read userId from request
			userId = request.getParameter("userId");
			// set hostId in forecast template
			forecastMonitorTemplate.getId().setHostId(hostId);
			// Set templaetId in bean
			forecastMonitorTemplate.getId().setTemplateId(templateId);
			// Set userId in bean
			forecastMonitorTemplate.setUserId(userId);
			forecastMonitorTemplateManager
					.deleteForecastMonitorTemplate(forecastMonitorTemplate);
			// setting reply status as true in request
			request.setAttribute("reply_status_ok", SwtConstants.STR_TRUE);
			// setting the reply message in request
			request.setAttribute("reply_message", SwtConstants.DATA_FETCH_OK);
		} catch (SwtException e) {
			log.error(this.getClass().getName()
					+ " - [deleteMonitorTemplate] - Exception -"
					+ e.getStackTrace()[0].getClassName() + "."
					+ e.getStackTrace()[0].getMethodName() + ":"
					+ e.getStackTrace()[0].getLineNumber() + " "
					+ e.getMessage());
			request.setAttribute("reply_status_ok", SwtConstants.STR_FALSE);
			request.setAttribute("reply_message", e.getMessage());
			request.setAttribute("reply_location", e.getStackTrace()[0]
					.getClassName()
					+ "."
					+ e.getStackTrace()[0].getMethodName()
					+ ":"
					+ e.getStackTrace()[0].getLineNumber());
			SwtUtil.logException(e, request, "");
			return ("dataerror");
		} catch (Exception e) {
			log.error(this.getClass().getName()
					+ " - [deleteMonitorTemplate] - Exception -"
					+ e.getStackTrace()[0].getClassName() + "."
					+ e.getStackTrace()[0].getMethodName() + ":"
					+ e.getStackTrace()[0].getLineNumber() + " "
					+ e.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					e, "deleteMonitorTemplate", this.getClass()), request, "");
			request.setAttribute("reply_status_ok", SwtConstants.STR_FALSE);
			request.setAttribute("reply_message", e.getMessage());
			request.setAttribute("reply_location", e.getStackTrace()[0]
					.getClassName()
					+ "."
					+ e.getStackTrace()[0].getMethodName()
					+ ":"
					+ e.getStackTrace()[0].getLineNumber());
			return ("dataerror");
		} finally {
			log.debug(this.getClass().getName()
					+ "-[deleteMonitorTemplate]-Exit");
		}
		return displayMonitorTemplate();
	}

	/**
	 * Method to Add colors for data grid
	 * 
	 * @param listForecastMonitorTemplateCol
	 * @return List<ForecastMonitorTemplateCol>
	 * @return List
	 */
	private List<ForecastMonitorTemplateCol> addDataGridColors(
			List<ForecastMonitorTemplateCol> listForecastMonitorTemplateCol)
			throws SwtException {
		// Method's local variable declaration
		// iterator instance
		Iterator<ForecastMonitorTemplateCol> itrForecastColl = null;
		// list to hold columns
		ArrayList<ForecastMonitorTemplateCol> arraylistForeCastCol = null;
		// ForecastMonitorTemplateCol instance
		ForecastMonitorTemplateCol foreCol = null;
		try {
			log.debug(this.getClass().getName()
					+ " - [addDataGridColors] -Entry");
			if (listForecastMonitorTemplateCol != null) {
				arraylistForeCastCol = new ArrayList<ForecastMonitorTemplateCol>();
				itrForecastColl = listForecastMonitorTemplateCol.iterator();
				while (itrForecastColl.hasNext()) {
					foreCol = itrForecastColl.next();

					// Condition to check column type is fixed
					if (foreCol.getColumnType().equals(SwtConstants.FIXED)
							|| foreCol.getColumnType().equals(
									SwtConstants.FIXED_LABEL)) {

						// Condition to display name total
						if (foreCol.getColumnDisplayName().equals(
								SwtConstants.TOTAL)) {
							foreCol.setRowColor(SwtConstants.ROW_COLOR_RED);
						} else {
							foreCol.setRowColor(SwtConstants.ROW_COLOR_GREY);
						}
						foreCol.setColumnType(SwtConstants.FIXED_LABEL);
						// Condition to column type is normal
					} else if (foreCol.getColumnType().equals(
							SwtConstants.NORMAL)
							|| foreCol.getColumnType().equals(
									SwtConstants.NORMAL_LABEL)) {
						foreCol.setRowColor(SwtConstants.ROW_COLOR_WHITE);
						foreCol.setColumnType(SwtConstants.NORMAL_LABEL);

						// Condition to check column type is subtotal
					} else {
						foreCol.setRowColor(SwtConstants.ROW_COLOR_BLUE);
						foreCol.setColumnType(SwtConstants.SUB_TOTAL_LABEL);
					}

					arraylistForeCastCol.add(foreCol);
				}
			}
		} catch (Exception exp) {
			log.error("An exception occured in " + this.getClass().getName()
					+ "- addDataGridColors -" + exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp,
					"addDataGridColors", this.getClass());
		} finally {
			log.debug(this.getClass().getName()
					+ " - [addDataGridColors] -Exit");
		}
		return arraylistForeCastCol;
	}

	/**
	 * 
	 * Method to check selected template is locked and to lock if it is not
	 * selected by other user
	 * 
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return ActionForward
	 * @throws SwtException
	 */
	public String lockTemplate()
			throws SwtException {
HttpServletRequest request = ServletActionContext.getRequest();
HttpServletResponse response = ServletActionContext.getResponse();

		// Method's local Variable declaration
		ForecastMonitorTemplate forecastMonitorTemplate = null;
		// String variable to hold current userId
		String currentUserId = null;
		// boolean variable to check whether the template is locked
		String lockedBy = null;
		// String to hold isPublic
		String isPublic = null;
		try {
			log.debug(this.getClass().getName() + " - [lockTemplate] -Entry");


			// Read is public from request
			isPublic = request.getParameter("isPublic");
			// Instantiate forecastMonitorTemplate
			forecastMonitorTemplate = new ForecastMonitorTemplate();
			// Get current user id
			currentUserId = SwtUtil.getCurrentUserId(request.getSession());
			// set host Id
			forecastMonitorTemplate.getId().setHostId(
					SwtUtil.getCurrentHostId());
			// selected template id
			forecastMonitorTemplate.getId().setTemplateId(request.getParameter("templateId"));
			// selected template Name
			forecastMonitorTemplate.setTemplateName(request.getParameter("templateName"));

			// set selected user id
			forecastMonitorTemplate.setUserId(request.getParameter("userId"));
			// Condition to check ispublic is not null and values is yes
			if (!SwtUtil.isEmptyOrNull(isPublic)
					&& isPublic.equals(SwtConstants.YES_VALUE))
				// set selected public
				forecastMonitorTemplate.setPublicTemplate(SwtConstants.YES);
			else
				// set selected public
				forecastMonitorTemplate.setPublicTemplate(SwtConstants.NO);

			// check whether the template is locked
			lockedBy = forecastMonitorTemplateManager
					.checkTemplateLocked(forecastMonitorTemplate);

			// Condition to checked the template is not locked
			if (SwtUtil.isEmptyOrNull(lockedBy)) {
				// set current user id in locked user column
				forecastMonitorTemplate.setLockedBy(currentUserId);
				// lock the template for current user
				forecastMonitorTemplateManager
						.lockTemplate(forecastMonitorTemplate);
				response.getWriter().print("");
			} else {
				// Start : Commented by Vivekanandan A for 1053 Beta2 testing issue.
				// To Avoid opening same template from FMT and Forecast Monitor
				// by the same user
				// if (!currentUserId.equals(lockedBy)) {
				// END : Commented by Vivekanandan A for 1053 Beta2 testing issue.
				// To Avoid opening same template from FMT and Forecast Monitor
				// by the same user
				// Set locked user id in response
				response.getWriter().print(lockedBy);
				// Start : Commented by Vivekanandan A for 1053 Beta2 testing issue.
				// To Avoid opening same template from FMT and Forecast Monitor
				// by the same user
				// } else {
				// // Set locked user id in response
				// response.getWriter().print("");
				//				}
				// End : Commented by Vivekanandan A for 1053 Beta2 testing issue.
				// To Avoid opening same template from FMT and Forecast Monitor
				// by the same user
			}

			// setting reply status as true in request
			request.setAttribute("reply_status_ok", SwtConstants.STR_TRUE);
			// setting the reply message in request
			request.setAttribute("reply_message", SwtConstants.DATA_FETCH_OK);
		} catch (Exception e) {
			log.error(this.getClass().getName()
					+ " - [lockTemplate] - Exception -"
					+ e.getStackTrace()[0].getClassName() + "."
					+ e.getStackTrace()[0].getMethodName() + ":"
					+ e.getStackTrace()[0].getLineNumber() + " "
					+ e.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					e, "lockTemplate", this.getClass()), request, "");
		} finally {
			log.debug(this.getClass().getName() + " - [lockTemplate] -Exit");
		}
		return null;
	}

	/**
	 * 
	 * Method to check given template is Exist in DB
	 * 
	 * 
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return
	 * @throws SwtException
	 */
	public void checkTemplateExists()
			throws SwtException {
HttpServletRequest request = ServletActionContext.getRequest();
HttpServletResponse response = ServletActionContext.getResponse();

		// Method's local Variable declaration
		ForecastMonitorTemplate forecastMonitorTemplate = null;
		// boolean variable to check whether the template is locked
		boolean isExist = false;
		try {
			log.debug(this.getClass().getName()
					+ " - [checkTemplateExists] -Entry");

			// Instantiate forecastMonitorTemplate
			forecastMonitorTemplate = new ForecastMonitorTemplate();
			// set host Id
			forecastMonitorTemplate.getId().setHostId(
					SwtUtil.getCurrentHostId());
			// selected template id
			forecastMonitorTemplate.getId().setTemplateId(request.getParameter("templateId"));

			// set selected user id
			forecastMonitorTemplate.setUserId(request.getParameter("userId"));
			// Condition to check ispublic is not null and values is yes

			// check whether the template is exist
			isExist = forecastMonitorTemplateManager
					.checkTemplateExist(forecastMonitorTemplate);

			// Set locked user id in response
			response.getWriter().print(isExist);

			// setting reply status as true in request
			request.setAttribute("reply_status_ok", SwtConstants.STR_TRUE);
			// setting the reply message in request
			request.setAttribute("reply_message", SwtConstants.DATA_FETCH_OK);
		} catch (Exception exp) {
			log.error("An exception occured in " + this.getClass().getName()
					+ "- checkTemplateExists -" + exp.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "checkTemplateExists", this.getClass()), request, "");
		} finally {
			log.debug(this.getClass().getName()
					+ " - [checkTemplateExists] -Exit");
		}
	}

	/**
	 * 
	 * Method to unlock the selected templateId
	 * 
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return ActionForward
	 * @throws SwtException
	 */

	public String unLockTemplate()
			throws SwtException {
HttpServletRequest request = ServletActionContext.getRequest();
HttpServletResponse response = ServletActionContext.getResponse();

		// Method's local Variable declaration
		ForecastMonitorTemplate forecastMonitorTemplate = null;
		// Session instance
		HttpSession session = null;
		// String variable to hold current userId
		String currentUserId = null;
		// boolean variable to check whether the template is locked
		String lockedBy = null;
		// String to hold selectedTemplateId
		String selectedTemplateId = null;
		// String to hold selectedTemplateName
		String selectedTemplateName = null;
		// String to hold isPublic
		String isPublic = null;
		// String to hold selectedUserId
		String selectedUserId = null;
		try {
			log.debug(this.getClass().getName() + " - [unLockTemplate] -Entry");

			// Read templateId from request
			selectedTemplateId = request.getParameter("templateId");

			// Read templateName from request
			selectedTemplateName = request.getParameter("templateName");

			// Read selected user from request
			selectedUserId = request.getParameter("userId");

			// Read is public from request
			isPublic = request.getParameter("isPublic");

			// Instantiate forecastMonitorTemplate
			forecastMonitorTemplate = new ForecastMonitorTemplate();
			// Obtain session from request
			session = request.getSession();
			// Get current user id
			currentUserId = SwtUtil.getCurrentUserId(session);
			// set host Id
			forecastMonitorTemplate.getId().setHostId(
					SwtUtil.getCurrentHostId());
			// selected template id
			forecastMonitorTemplate.getId().setTemplateId(selectedTemplateId);
			// selected template Name
			forecastMonitorTemplate.setTemplateName(selectedTemplateName);

			// set selected user id
			forecastMonitorTemplate.setUserId(selectedUserId);
			// Condition to check ispublic is not null and values is yes
			if (!SwtUtil.isEmptyOrNull(isPublic)
					&& isPublic.equals(SwtConstants.YES_VALUE))
				// set selected public
				forecastMonitorTemplate.setPublicTemplate(SwtConstants.YES);
			else
				// set selected public
				forecastMonitorTemplate.setPublicTemplate(SwtConstants.NO);

			// check whether the template is locked
			lockedBy = forecastMonitorTemplateManager
					.checkTemplateLocked(forecastMonitorTemplate);

			// Condition to checked locked by user id and current user id is
			// same to unlock the template
			if (!SwtUtil.isEmptyOrNull(lockedBy)
					&& lockedBy.equals(currentUserId)) {
				// set null in locked user column to unlock the template
				forecastMonitorTemplate.setLockedBy(null);
				// lock the template for current user
				forecastMonitorTemplateManager
						.lockTemplate(forecastMonitorTemplate);
				response.getWriter().print("unlocked");
			} else {
				// Set locked user id in response
				response.getWriter().print("");
			}

			// setting reply status as true in request
			request.setAttribute("reply_status_ok", SwtConstants.STR_TRUE);
			// setting the reply message in request
			request.setAttribute("reply_message", SwtConstants.DATA_FETCH_OK);
		} catch (Exception exp) {
			log.error("An exception occured in " + this.getClass().getName()
					+ "- unLockTemplate -" + exp.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "unLockTemplate", this.getClass()), request, "");
		} finally {
			log.debug(this.getClass().getName() + " - [unLockTemplate] -Exit");
		}
		// return null
		return null;
	}

	/**
	 * 
	 * Method to load the Copy From Forecast screen
	 * 
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return ActionForward
	 * @throws SwtException
	 */

	public String loadCopyFrom()
			throws SwtException {
HttpServletRequest request = ServletActionContext.getRequest();
HttpServletResponse response = ServletActionContext.getResponse();

		// Method's local variable declaration
		/* Variable Declaration for dyForm */
		// DynaValidatorForm dyForm = null;
		/* Variable Declaration for collMonitorDetails */
		List<ForecastMonitorTemplate> listMonitorTemplateDetails = null;
		/* Variable Declaration for iterator */
		Iterator<ForecastMonitorTemplate> itrMonitorTemplateDetails = null;
		/* Variable Declaration for collMonitorDetails */
		ArrayList<ForecastMonitorTemplate> loadListMonitorTemplateDetails = null;
		// Insatnce for forecast Monitor template
		ForecastMonitorTemplate forecastMonitorTemplate = null;
		// String to hold selectedTemplateId
		String currentUserId = null;
		// String to hold hostId
		String hostId = null;
		// String to hold templateId
		String templateId = null;
		// ForecastMonitorTemplate instance =
		ForecastMonitorTemplate checkTemplate = null;
		try {
			// debug message
			log.debug(this.getClass().getName() + "-[loadCopyFrom]-Entry");
			// read template from request
			templateId = request.getParameter("templateId");

			// set template id in request
			request.setAttribute("templateId", templateId);
			// set user id in request
			request.setAttribute("userId", request.getParameter("userId"));

			// Condition to check load flex is null
			if (SwtUtil.isEmptyOrNull(request.getParameter("loadFlex"))) {
				// debug message
				log.debug(this.getClass().getName()
						+ "-[loadCopyFrom]- Exit to load SWF");
				return ("copyfrom");
			}
			// Get current hostId from DB
			hostId = SwtUtil.getCurrentHostId();
			// Get current user id
			currentUserId = SwtUtil.getCurrentUserId(request.getSession());

			// set the form object into // DynaValidatorForm
			// get the forecastMonitorTemplate for object
			forecastMonitorTemplate = (ForecastMonitorTemplate) getForecastMonitorTemplate();

			// Get templates for copy from screen
			listMonitorTemplateDetails = forecastMonitorTemplateManager
					.getTemplatesCopyFrom(hostId, currentUserId);
			// Instantiate list
			loadListMonitorTemplateDetails = new ArrayList<ForecastMonitorTemplate>();
			// Iterate list
			itrMonitorTemplateDetails = listMonitorTemplateDetails.iterator();
			// loop to get templates
			while (itrMonitorTemplateDetails.hasNext()) {
				checkTemplate = itrMonitorTemplateDetails.next();
				// Condition to check same template id is in list and remove the
				// same if present
				
				if (SwtUtil.isEmptyOrNull(templateId) ||(
						 !checkTemplate.getId().getTemplateId().equals(
								templateId))){
					loadListMonitorTemplateDetails.add(checkTemplate);
				}

			}

			request.setAttribute("listMonitorTemplateDetails",
					loadListMonitorTemplateDetails);

			// set form object
			setForecastMonitorTemplate(forecastMonitorTemplate);
			// setting reply status as true in request
			request.setAttribute("reply_status_ok", SwtConstants.STR_TRUE);
			// setting the reply message in request
			request.setAttribute("reply_message", SwtConstants.DATA_FETCH_OK);

		} catch (SwtException e) {
			log.error(this.getClass().getName()
					+ " - [loadCopyFrom] - Exception -"
					+ e.getStackTrace()[0].getClassName() + "."
					+ e.getStackTrace()[0].getMethodName() + ":"
					+ e.getStackTrace()[0].getLineNumber() + " "
					+ e.getMessage());
			request.setAttribute("reply_status_ok", SwtConstants.STR_FALSE);
			request.setAttribute("reply_message", e.getMessage());
			request.setAttribute("reply_location", e.getStackTrace()[0]
					.getClassName()
					+ "."
					+ e.getStackTrace()[0].getMethodName()
					+ ":"
					+ e.getStackTrace()[0].getLineNumber());
			SwtUtil.logException(e, request, "");
			return ("dataerror");
		} catch (Exception e) {
			log.error(this.getClass().getName()
					+ " - [loadCopyFrom] - Exception -"
					+ e.getStackTrace()[0].getClassName() + "."
					+ e.getStackTrace()[0].getMethodName() + ":"
					+ e.getStackTrace()[0].getLineNumber() + " "
					+ e.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					e, "loadCopyFrom", this.getClass()), request, "");
			request.setAttribute("reply_status_ok", SwtConstants.STR_FALSE);
			request.setAttribute("reply_message", e.getMessage());
			request.setAttribute("reply_location", e.getStackTrace()[0]
					.getClassName()
					+ "."
					+ e.getStackTrace()[0].getMethodName()
					+ ":"
					+ e.getStackTrace()[0].getLineNumber());
			return ("dataerror");
		} finally {
			log
					.debug(this.getClass().getName()
							+ " - [loadCopyFrom] - Exit");
		}
		return ("copyfromflexdata");
	}

	/**
	 * 
	 * Method to save forecast templates
	 * 
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return ActionForward
	 * @throws SwtException
	 */

	public String saveTemplates()
			throws SwtException {
HttpServletRequest request = ServletActionContext.getRequest();
HttpServletResponse response = ServletActionContext.getResponse();


		// Method's local variable declaration
		/* Variable Declaration for dyForm */
		// DynaValidatorForm dyForm = null;
		/* Variable Declaration for collMonitorDetails */
		List<ForecastMonitorTemplate> listMonitorTemplateDetails = null;
		// Insatnce for forecast Monitor template
		ForecastMonitorTemplate forecastMonitorTemplate = null;
		// String to hold hostId
		String hostId = null;
		// String to hold templateId
		String templateId = null;
		// String to hold templateName
		String templateName = null;
		// String to hold userId
		String userId = null;
		// String to hold isPublic
		String isPublic = null;
		/* Array list to hold menu access collection */
		ArrayList<ForecastMonitorTemplateCol> forecastColList = null;
		// variable to hold ForecastMonitorTemplateCol
		ForecastMonitorTemplateCol fcastTemplateColName = null;
		// variable to hold ForecastMonitorTemplateCol
		ForecastMonitorTemplateCol forecastCol = null;
		// Array list to get ForecastMonitorTemplateColSrc values
		List<ForecastMonitorTemplateCol> monitorTemplateCol = null;
		// Array list to get ForecastMonitorTemplateCol values
		Iterator<ForecastMonitorTemplateCol> monitorTemplateColItr = null;
		// Iterator to get ForecastMonitorTemplateCol values
		Iterator<ForecastMonitorTemplateCol> monitorTemplateColNameItr = null;
		// list to get ForecastMonitorTemplateColSrc values
		List<ForecastMonitorTemplateColSrc> monitorTemplateColSrcList = null;
		// Iterator to get ForecastMonitorTemplateColSrc values
		Iterator<ForecastMonitorTemplateColSrc> monitorTemplateColSrcItr = null;
		// List to hold ForecastMonitorTemplateColSrc values
		List<ForecastMonitorTemplateColSrc> columnSrcchecklist = null;
		// boolean falg to save ordinal position
		boolean fixedFlag = false;
		List<ForecastMonitorTemplateCol> lstForecastMonitorTemplateCol = null;
		// Iterator to get ForecastMonitorTemplateCol values
		Iterator<ForecastMonitorTemplateCol> itrForecastMonitorTemplateCol = null;
		// saveDisplayName
		String[] saveDisplayName = null;
		// saveOrdinalPos
		String[] saveOrdinalPos = null;
		// ForecastMonitorTemplateCol instance
		ForecastMonitorTemplateCol forecastTemplateColFromSession = null;
		// ForecastMonitorTemplateCol instance
		ForecastMonitorTemplateCol forecastTemplateCol = null;
		// ForecastMonitorTemplateColSrc instance
		ForecastMonitorTemplateColSrc forecastTemplateColSrc = null;
		try {

			// debug message
			log.debug(this.getClass().getName() + "-[saveTemplates]-Entry");

			// set the form object into // DynaValidatorForm
			// Get current hostId from DB
			hostId = SwtUtil.getCurrentHostId();

			// read templateid from request
			templateId = request.getParameter("templateId");

			// read template namefrom request
			templateName = request.getParameter("templateName");

			// read user name from request
			userId = request.getParameter("userId");

			// read isPublic from request
			isPublic = request.getParameter("isPublic");

			// saveDisplayName from form
			saveDisplayName = request.getParameter("saveDisplayName").split(",");
			// saveOrdinalPos from form
			saveOrdinalPos =  request.getParameter("saveOrdinalPos").split(",");

			// get the forecastMonitorTemplate for object
			forecastMonitorTemplate = (ForecastMonitorTemplate) request
					.getSession().getAttribute("forecastMonitorTemplate");

			// Condition to chcek forecastMonitorTemplate is null and initialize
			if (forecastMonitorTemplate == null)
				forecastMonitorTemplate = new ForecastMonitorTemplate();

			// set hostId in template
			forecastMonitorTemplate.getId().setHostId(hostId);

			// set templateId in template
			forecastMonitorTemplate.getId().setTemplateId(templateId);

			// set userId in template
			forecastMonitorTemplate.setUserId(userId);

			// set template name in template
			forecastMonitorTemplate.setTemplateName(templateName);

			// set ispublic name in template
			forecastMonitorTemplate.setPublicTemplate(isPublic);

			// save forecast template
			forecastMonitorTemplateManager
					.saveTemplate(forecastMonitorTemplate);

			if (forecastMonitorTemplate.getForecastTemplateColList() != null
					&& forecastMonitorTemplate.getForecastTemplateColList()
							.size() > 0) {
				forecastColList = new ArrayList<ForecastMonitorTemplateCol>();
				lstForecastMonitorTemplateCol = forecastMonitorTemplate
						.getForecastTemplateColList();
				itrForecastMonitorTemplateCol = lstForecastMonitorTemplateCol
						.iterator();

				while (itrForecastMonitorTemplateCol.hasNext()) {
					forecastTemplateCol = itrForecastMonitorTemplateCol.next();
					// loop to get ordinal posiion
					for (int ordCounter = 0; ordCounter < saveOrdinalPos.length; ordCounter++) {
						if (saveDisplayName[ordCounter]
								.equals(forecastTemplateCol
										.getColumnDisplayName())) {
							// set ordinal position as collection size
							forecastTemplateCol.setOrdinalPos(Integer
									.parseInt(saveOrdinalPos[ordCounter]));
							break;
						}
					}
					forecastTemplateCol.getId().setTemplateId(templateId);
					forecastTemplateCol.getId().setHostId(hostId);
					forecastTemplateCol.setUserId(userId);
					// Condition to check type is fixed
					if (forecastTemplateCol.getColumnType().equals(
							SwtConstants.FIXED_LABEL)) {
						// set column type as fixed
						forecastTemplateCol.setColumnType(SwtConstants.FIXED);
						// Condition to check display name is bucket
						if (forecastTemplateCol.getColumnDisplayName().equals(
								"Bucket"))
							// set ordinal position as 1
							forecastTemplateCol.setOrdinalPos(1);
						// Condition to check display name is date
						else if (forecastTemplateCol.getColumnDisplayName()
								.equals("Date"))
							// set ordinal position as 2
							forecastTemplateCol.setOrdinalPos(2);
						// Condition to check display name is total
						else if (forecastTemplateCol.getColumnDisplayName()
								.equals(SwtConstants.TOTAL)) {
							// set ordinal position as 960
							forecastTemplateCol.setOrdinalPos(960);
							// Condition to check display name is Assumption
						} else if (forecastTemplateCol.getColumnDisplayName()
								.equals("Assumption"))
							// set ordinal position as 970
							forecastTemplateCol.setOrdinalPos(970);
						// Condition to check display name is Scenario
						else if (forecastTemplateCol.getColumnDisplayName()
								.equals("Scenario"))
							// set ordinal position as 980
							forecastTemplateCol.setOrdinalPos(980);
						// Condition to check display name is Grand Total
						else if (forecastTemplateCol.getColumnDisplayName()
								.equals("Grand Total"))
							// set ordinal position as 990
							forecastTemplateCol.setOrdinalPos(990);

						fixedFlag = true;
						forecastTemplateCol.setUserEditable("N");

					} else if (forecastTemplateCol.getColumnType().equals(
							SwtConstants.SUB_TOTAL_LABEL)) {

						forecastTemplateCol
								.setColumnType(SwtConstants.SUB_TOTAL);
						fixedFlag = false;

					} else{

						forecastTemplateCol.setColumnType(SwtConstants.NORMAL);
						fixedFlag = false;
					}

					// Set user editable value as Y for all user defined columns
					// except fixed
					if (!fixedFlag) {
						forecastTemplateCol.setUserEditable("Y");
					}

					/* Add forecast col values to Array list */
					if (forecastTemplateCol.getColumnType().equals(
							SwtConstants.NORMAL)) {
						forecastColList.add(0, forecastTemplateCol);
					} else {
						forecastColList.add(forecastTemplateCol);
					}
				}
				// save forecast template collection
				forecastMonitorTemplateManager.saveTemplateCol(forecastColList);
				// iterate the same list
				monitorTemplateColItr = forecastColList.iterator();
				// loop to get column src values of the column defined
				while (monitorTemplateColItr.hasNext()) {

					// read column values
					forecastTemplateColFromSession = monitorTemplateColItr
							.next();
					// get source from each column bean
					monitorTemplateColSrcList = forecastTemplateColFromSession
							.getListForecastMonitorTemplateColSrc();

					// get column id for each column
					forecastCol = forecastMonitorTemplateManager
							.getForecastMonitorTemplateColForColumnId(forecastTemplateColFromSession);
					// Condition to check total multiplier is not efined
					if (!SwtUtil.isEmptyOrNull(forecastTemplateColFromSession
							.getTotalMultiplier())) {
						if (!forecastCol.getId().getColumnId()
								.equalsIgnoreCase(SwtConstants.TOTAL)) {
							forecastTemplateColSrc = new ForecastMonitorTemplateColSrc();
							/* Set values to forecast col bean */
							forecastTemplateColSrc
									.setEntityId(forecastTemplateColFromSession
											.getColumnDisplayName());
							forecastTemplateColSrc
									.setName(forecastTemplateColFromSession
											.getColumnDescription());
							forecastTemplateColSrc.getId().setTemplateId(
									templateId);
							forecastTemplateColSrc.getId().setSourceId(
									forecastCol.getId().getColumnId());
							forecastTemplateColSrc.getId().setHostId(hostId);
							forecastTemplateColSrc.setUserId(userId);
							forecastTemplateColSrc.getId().setSourceType(
									SwtConstants.TOTAL_COLUMN_SOURCETYPE);
							forecastTemplateColSrc.getId().setColumnId(
									SwtConstants.TOTAL.toUpperCase());
							forecastTemplateColSrc
									.setMultiplier(new BigDecimal(
											forecastTemplateColFromSession
													.getTotalMultiplier()));
							forecastTemplateColSrc.setTotalChanged("false");

							forecastMonitorTemplateManager
									.saveTemplateColSrc(forecastTemplateColSrc);
						}

					}

					// get monitor template col collection
					monitorTemplateCol = forecastMonitorTemplateManager
							.getForecastMonitorTemplateCol(forecastCol);
					// Condition to check monitorTemplateColSrcList is null and
					// collection size is not null
					if (monitorTemplateColSrcList != null
							&& monitorTemplateColSrcList.size() > 0) {
						// iterate the source collection
						monitorTemplateColSrcItr = monitorTemplateColSrcList
								.iterator();
						// loop to iterate colsrc list
						while (monitorTemplateColSrcItr.hasNext()) {
							// get col src bean
							forecastTemplateColSrc = monitorTemplateColSrcItr
									.next();
							// set id field values
							forecastTemplateColSrc
									.setForecastMonitorTemplateCol(null);
							// set template id
							forecastTemplateColSrc.getId().setTemplateId(
									forecastTemplateColFromSession.getId()
											.getTemplateId());
							// set user id
							forecastTemplateColSrc
									.setUserId(forecastTemplateColFromSession
											.getUserId());

							// Condition to check column id is total
							if ((!SwtUtil.isEmptyOrNull(forecastTemplateColSrc
									.getId().getColumnId()) && forecastTemplateColSrc
									.getId().getColumnId().equalsIgnoreCase(
											SwtConstants.TOTAL))) {
								forecastTemplateColSrc.getId().setSourceId(
										forecastCol.getId().getColumnId());
								// Condition to check column type is Total or
								// sub total
							} else if (forecastTemplateColSrc
									.getId()
									.getSourceType()
									.equals(
											SwtConstants.TOTAL_COLUMN_SOURCETYPE)) {

								// if column type is not normal get
								// corresponding normal and subtotal column's
								// column id and set it as source for this
								if (!forecastTemplateColFromSession
										.getColumnType().equals(
												SwtConstants.NORMAL)) {
									forecastTemplateColSrc.getId().setColumnId(
											forecastCol.getId().getColumnId());

									monitorTemplateColNameItr = monitorTemplateCol
											.iterator();
									while (monitorTemplateColNameItr.hasNext()) {
										fcastTemplateColName = monitorTemplateColNameItr
												.next();
										if (fcastTemplateColName
												.getColumnDisplayName().equals(
														forecastTemplateColSrc
																.getEntityId())) {

											forecastTemplateColSrc
													.getId()
													.setSourceId(
															fcastTemplateColName
																	.getId()
																	.getColumnId());
											break;
										}
									}

								}
								// Set column id as normal
							} else {
								forecastTemplateColSrc.getId().setColumnId(
										forecastCol.getId().getColumnId());

							}

							// Condition to check selected type is book
							if (forecastTemplateColSrc.getId().getSourceType()
									.equals(SwtConstants.BOOK_LABEL))
								forecastTemplateColSrc.getId().setSourceType(
										SwtConstants.BOOK);
							// Condition to check selected type is group
							else if (forecastTemplateColSrc.getId()
									.getSourceType().equals(
											SwtConstants.GROUP_LABEL))
								forecastTemplateColSrc.getId().setSourceType(
										SwtConstants.GROUP);

							// Condition to check selected type is meta group
							else if (forecastTemplateColSrc.getId()
									.getSourceType().equals(
											SwtConstants.META_GROUP_LABEL))
								forecastTemplateColSrc.getId().setSourceType(
										SwtConstants.META_GROUP);
							// Condition to check selected type is entity
							else if (forecastTemplateColSrc.getId()
									.getSourceType().equals(
											SwtConstants.ENTITY_LABEL))
								forecastTemplateColSrc.getId().setSourceType(
										SwtConstants.ENTITY);

							if (!forecastTemplateColSrc.getId().getSourceId()
									.equalsIgnoreCase(SwtConstants.TOTAL)) {

								// check whether the given source is exist
								columnSrcchecklist = forecastMonitorTemplateManager
										.checkForecastExist(forecastTemplateColSrc);
								// Condition to check whether the given source
								// is exist
								if (columnSrcchecklist.size() == 0) {
									// save template col src in table
									if ((!forecastTemplateColSrc.getId()
											.getColumnId().equalsIgnoreCase(
													SwtConstants.TOTAL)))
										forecastMonitorTemplateManager
												.saveTemplateColSrc(forecastTemplateColSrc);
								}
							}

						}
					}
				}

			}
			// set template as null
			request.getSession().setAttribute("forecastMonitorTemplate", null);

			request.setAttribute("listMonitorTemplateDetails",
					listMonitorTemplateDetails);

			// Set template Id in request
			request.setAttribute("selectedTemplateId", templateId);
			// Set template Name in request
			request.setAttribute("selectedTemplateName", templateName);
			// Set both list in bean
			request.setAttribute("listMonitorTemplateDetailsCol",
					new ArrayList<ForecastMonitorTemplateCol>());
			request.setAttribute("templateUsersList", new ArrayList<User>());
			// set form object
			setForecastMonitorTemplate(forecastMonitorTemplate);
			request.setAttribute("forecastMonitorTemplate",
					forecastMonitorTemplate);

			// setting reply status as true in request
			request.setAttribute("reply_status_ok", SwtConstants.STR_TRUE);
			// setting the reply message in request
			request.setAttribute("reply_message", SwtConstants.DATA_FETCH_OK);

		} catch (SwtException swtexp) {
			request.setAttribute("reply_status_ok", SwtConstants.STR_FALSE);
			if (swtexp.getErrorCode().equals(
					"errors.DataIntegrityViolationExceptioninAdd")) {
				// set template as null
				request.getSession().setAttribute("forecastMonitorTemplate",
						forecastMonitorTemplate);
				request.setAttribute("reply_message", "Record already exists");
			} else {
				request.setAttribute("reply_message", swtexp.getMessage());
				request.setAttribute("reply_location",
						swtexp.getStackTrace()[0].getClassName() + "."
								+ swtexp.getStackTrace()[0].getMethodName()
								+ ":"
								+ swtexp.getStackTrace()[0].getLineNumber());
			}
			log.error(this.getClass().getName()
					+ " - [saveTemplates] - Exception -"
					+ swtexp.getStackTrace()[0].getClassName() + "."
					+ swtexp.getStackTrace()[0].getMethodName() + ":"
					+ swtexp.getStackTrace()[0].getLineNumber() + " "
					+ swtexp.getMessage());

			SwtUtil.logException(swtexp, request, "");
			return ("dataerror");
		} catch (Exception e) {
			log.error(this.getClass().getName()
					+ " - [saveTemplates] - Exception -"
					+ e.getStackTrace()[0].getClassName() + "."
					+ e.getStackTrace()[0].getMethodName() + ":"
					+ e.getStackTrace()[0].getLineNumber() + " "
					+ e.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					e, "saveTemplates", this.getClass()), request, "");
			request.setAttribute("reply_status_ok", SwtConstants.STR_FALSE);
			request.setAttribute("reply_message", e.getMessage());
			request.setAttribute("reply_location", e.getStackTrace()[0]
					.getClassName()
					+ "."
					+ e.getStackTrace()[0].getMethodName()
					+ ":"
					+ e.getStackTrace()[0].getLineNumber());
			return ("dataerror");
		} finally {
			log.debug(this.getClass().getName()
					+ " - [saveTemplates] - Exit");
		}
		return ("templateadd");
	}

	/**
	 * 
	 * Method to save/ update forecast template column and its source in session
	 * 
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return ActionForward
	 * @throws SwtException
	 */

	public String saveTemplatesSrcInSession() throws SwtException {
HttpServletRequest request = ServletActionContext.getRequest();
HttpServletResponse response = ServletActionContext.getResponse();


		// Method's local variable declaration
		/* Variable Declaration for dyForm */
		// DynaValidatorForm dyForm = null;
		// String to hold hostId
		String hostId = null;
		// String to hold type
		String type = null;
		// String to hold columnId
		String columnId = null;
		// String to hold shortName
		String shortName = null;
		/* Array list to hold forecast col src collection */
		ArrayList<ForecastMonitorTemplateColSrc> forecastColSrcList = null;
		// String to hold templateId
		String templateId = null;
		// String to hold userId
		String userId = null;
		// Stores the multiplier value
		String totalMultiplier = null;
		// Stores the column description
		String description = null;
		// Holds the ForecastMonitorTemplate from session
		ForecastMonitorTemplate forecastMonitorTemplate = null;
		// Holds the list of columns for this templates
		List<ForecastMonitorTemplateCol> forecastMonitorTemplateColList = null;
		// Holds the list of columns sources for this templates
		List<ForecastMonitorTemplateColSrc> forecastMonitorTemplateColSrcList = null;
		// Iterator for Forecast template column list
		ForecastMonitorTemplateCol modifiedForecastTemplateCol = null;
		// Holds the maximum ordinal position
		int maxColOrdinalPos = 2;
		// Counter for looping through sources
		int sourceCounter;
		// ForecastMonitorTemplateColSrc instance
		ForecastMonitorTemplateColSrc forecastMonitorTemplateColSrc = null;
		// String variable to hold columnSourceType
		String[] columnSourceType = new String[] {};
		// String variable to hold entity
		String[] entity = new String[] {};
		// String variable to hold sourceId
		String[] sourceId = new String[] {};
		// String variable to hold sourceName
		String[] sourceName = new String[] {};
		// String variable to hold sourceMultiplier
		String[] sourceMultiplier = new String[] {};
		// String variable to hold modifiedValue for columns
		String modifiedValue = null;
		// isNameChanged
		String isNameChanged = null;
		// modifiedShortName
		String modifiedShortName = null;
		// flag to check total multiplier is exist in total sources
		boolean isTotalExist = false;
		// sourceOrdinalPos []
		String[] sourceOrdinalPos = new String[] {};
		// Start: changes made by balaji for ordinal position
		// Flag to check if the given column is new
		boolean isNewColumnFlag = false;
		// Old ordinal position value for existing column
		int oldOrdinalPosition;
		// End: changes made by balaji for ordinal position
		try {

			// debug message
			log.debug(this.getClass().getName()
					+ "-[saveTemplatesSrcInSession]-Entry");

			// set the form object into // DynaValidatorForm
			// Get current hostId from DB
			hostId = SwtUtil.getCurrentHostId();

			// read templateid from request
			templateId = request.getParameter("templateId");

			// read user name from request
			userId = request.getParameter("userId");

			// read type from request
			type = request.getParameter("type");

			// read columnId from request
			columnId = request.getParameter("columnId");
			// read shortNameOnLoad from request
			shortName = request.getParameter("shortNameOnLoad");
			// read shortName from request
			modifiedShortName = request.getParameter("shortName");
			// read description from request
			description = request.getParameter("description");
			// read totalMultiplier from request
			totalMultiplier = request.getParameter("multiplier");

			// read columnSourceType from request
			if(!SwtUtil.isEmptyOrNull(request.getParameter("columnSourceType")) )
			columnSourceType = request.getParameter("columnSourceType").split(",");
			// read sourceOrdinalPos from request
			if(!SwtUtil.isEmptyOrNull(request.getParameter("sourceOrdinalPos")) )
			sourceOrdinalPos = request.getParameter("sourceOrdinalPos").split(",");

			// read modifiedValue from request
			modifiedValue = request.getParameter("modifiedValue");

			// read entity from request
			if(!SwtUtil.isEmptyOrNull(request.getParameter("entity")) )
			entity = request.getParameter("entity").split(",");

			// read sourceId from request
			String sourceIdString = null;
			if(!SwtUtil.isEmptyOrNull(request.getParameter("sourceId")) ) {
				if(request.getParameter("sourceId").endsWith(",")) {
					sourceIdString = request.getParameter("sourceId")+null;
				} else {
				sourceIdString =request.getParameter("sourceId");
			   }
				sourceId = sourceIdString.split(",");
			   }
			
			

			// read sourceName from request
			if(!SwtUtil.isEmptyOrNull(request.getParameter("sourceName")) )
			sourceName = request.getParameter("sourceName").split(",");

			// read sourceMultiplier from request
			if(!SwtUtil.isEmptyOrNull(request.getParameter("sourceMultiplier")) )
			sourceMultiplier = request.getParameter("sourceMultiplier").split(",");
			// read is name changed from request
			isNameChanged = request.getParameter("isNameChanged");

			// get the forecastMonitorTemplate from session
			forecastMonitorTemplate = (ForecastMonitorTemplate) request
					.getSession().getAttribute("forecastMonitorTemplate");
			forecastColSrcList = new ArrayList<ForecastMonitorTemplateColSrc>();
			// get the list of column from the current template
			forecastMonitorTemplateColList = forecastMonitorTemplate
					.getForecastTemplateColList();

			// Template Id in bean
			forecastMonitorTemplate.getId().setTemplateId(templateId);

			// set user id in bean
			forecastMonitorTemplate.setUserId(userId);

			// Checking duplicate column name while modifying an existing column
			if (!SwtUtil.isEmptyOrNull(isNameChanged)
					&& isNameChanged.equalsIgnoreCase("true")) {
				if (checkDuplicateColumn(forecastMonitorTemplate,
						modifiedShortName)) {
					// setting reply status as true in request
					request.setAttribute("reply_status_ok",
							SwtConstants.STR_FALSE);
					// setting the reply message in request
					request.setAttribute("reply_message",
							"Column name already exist for template "
									+ templateId);
					// If duplicate column found return error
					return ("dataerror");

				}
			}
			// Checking duplicate column name while creating a new column
			if (!SwtUtil.isEmptyOrNull(modifiedValue)
					&& modifiedValue.equalsIgnoreCase("save")) {
				if (checkDuplicateColumn(forecastMonitorTemplate,
						modifiedShortName)) {
					// setting reply status as true in request
					request.setAttribute("reply_status_ok",
							SwtConstants.STR_FALSE);
					// setting the reply message in request
					request.setAttribute("reply_message",
							"Column name already exist for template "
									+ templateId);
					// If duplicate column found return error
					return ("dataerror");
				}
			}

			// Loop through the ForecastMonitorTemplateCol list for updating the
			// new column name for the existing column
			for (ForecastMonitorTemplateCol tempForecastMonitorTemplateCol : forecastMonitorTemplateColList) {
				if (tempForecastMonitorTemplateCol.getColumnDisplayName()
						.equalsIgnoreCase(shortName)) {
					// Condition to check name changed to change the currenct
					// source name
					if (!SwtUtil.isEmptyOrNull(isNameChanged)
							&& isNameChanged.equalsIgnoreCase("true")) {
						tempForecastMonitorTemplateCol
								.setColumnDisplayName(modifiedShortName);
					}
					modifiedForecastTemplateCol = tempForecastMonitorTemplateCol;
					forecastMonitorTemplateColList
							.remove(tempForecastMonitorTemplateCol);
					break;
				}
			}
			// Condition to check name changed if so modify the corresponding
			// sources values used for refference
			if (!SwtUtil.isEmptyOrNull(isNameChanged)
					&& isNameChanged.equalsIgnoreCase("true")) {
				for (ForecastMonitorTemplateCol tempForecastMonitorTemplateCol : forecastMonitorTemplateColList) {
					if (tempForecastMonitorTemplateCol
							.getListForecastMonitorTemplateColSrc() != null
							&& tempForecastMonitorTemplateCol
									.getListForecastMonitorTemplateColSrc()
									.size() > 0) {
						for (ForecastMonitorTemplateColSrc forecastColSrc : tempForecastMonitorTemplateCol
								.getListForecastMonitorTemplateColSrc()) {
							if (forecastColSrc.getEntityId().equals(shortName)) {
								forecastColSrc.setEntityId(modifiedShortName);
							}

						}
					}
				}
			}

			if (modifiedForecastTemplateCol == null) {
				isNewColumnFlag = true;
				modifiedForecastTemplateCol = new ForecastMonitorTemplateCol();
				// set host id in forecast columns
				modifiedForecastTemplateCol.getId().setHostId(hostId);
				// set template id in forecast columns
				modifiedForecastTemplateCol.getId().setTemplateId(templateId);
				// set user id in forecast columns
				modifiedForecastTemplateCol.setUserId(userId);

				// set type
				modifiedForecastTemplateCol.setColumnType(type);
				// set ordinal position
				modifiedForecastTemplateCol.setOrdinalPos(Integer
						.parseInt(columnId));
				// set description
				modifiedForecastTemplateCol.setModifiedValue("save");
				// Condition to check type is subtotal
				if (type.equals(SwtConstants.SUB_TOTAL_LABEL))
					// set color
					modifiedForecastTemplateCol
							.setRowColor(SwtConstants.ROW_COLOR_BLUE);
				else
					// set color
					modifiedForecastTemplateCol
							.setRowColor(SwtConstants.ROW_COLOR_WHITE);
				// set short name
				modifiedForecastTemplateCol
						.setColumnDisplayName(modifiedShortName);
			}

			// set description
			modifiedForecastTemplateCol.setColumnDescription(description);
			// Start: changes made by balaji for ordinal position
			oldOrdinalPosition = modifiedForecastTemplateCol.getOrdinalPos();
			modifiedForecastTemplateCol.setOrdinalPos(Integer
					.parseInt(columnId));
			// End: changes made by balaji for ordinal position
			if (!SwtUtil.isEmptyOrNull(totalMultiplier)) {
				// set totalMultiplier
				modifiedForecastTemplateCol.setTotalMultiplier(totalMultiplier);
				// set the modified total multiplier sources in total column
				for (ForecastMonitorTemplateCol tempForecastMonitorTemplateCol : forecastMonitorTemplateColList) {
					// Condition to check column name is total
					if (tempForecastMonitorTemplateCol.getColumnDisplayName()
							.equalsIgnoreCase(SwtConstants.TOTAL)) {
						// Condition to check columns ourecs already exist for
						// total
						if (tempForecastMonitorTemplateCol
								.getListForecastMonitorTemplateColSrc() != null
								&& tempForecastMonitorTemplateCol
										.getListForecastMonitorTemplateColSrc()
										.size() > 0) {
							// set the modified total multiplier sources in
							// total column
							for (ForecastMonitorTemplateColSrc tempForecastMonitorTemplateColSrc : tempForecastMonitorTemplateCol
									.getListForecastMonitorTemplateColSrc()) {
								// Condition to check whether the column sources
								// exits for selected column name
								if (tempForecastMonitorTemplateColSrc
										.getEntityId().equals(shortName)) {
									isTotalExist = true;
									tempForecastMonitorTemplateColSrc
											.setMultiplier(new BigDecimal(
													totalMultiplier));
									break;
								}
							}

						}

						// Add new column sources for total if the column not
						// exits in session
						if (!isTotalExist) {
							forecastMonitorTemplateColSrc = new ForecastMonitorTemplateColSrc();
							forecastMonitorTemplateColSrc.getId().setHostId(
									hostId);
							forecastMonitorTemplateColSrc.getId()
									.setTemplateId(templateId);
							forecastMonitorTemplateColSrc.getId().setSourceId(
									tempForecastMonitorTemplateCol.getId()
											.getColumnId());
							forecastMonitorTemplateColSrc
									.getId()
									.setSourceType(
											SwtConstants.TOTAL_COLUMN_SOURCETYPE);
							forecastMonitorTemplateColSrc.setOrdinalPos(Integer
									.parseInt(columnId));
							forecastMonitorTemplateColSrc.setName(description);
							forecastMonitorTemplateColSrc
									.setEntityId(shortName);
							forecastMonitorTemplateColSrc
									.setMultiplier(new BigDecimal(
											totalMultiplier));
							tempForecastMonitorTemplateCol
									.getListForecastMonitorTemplateColSrc()
									.add(forecastMonitorTemplateColSrc);
						}
						break;
					}
				}
			} else {
				// set totalMultiplier
				modifiedForecastTemplateCol.setTotalMultiplier("");
				// set the modified total multiplier sources in total column
				for (ForecastMonitorTemplateCol tempForecastMonitorTemplateCol : forecastMonitorTemplateColList) {
					// Condition to check column name is total
					if (tempForecastMonitorTemplateCol.getColumnDisplayName()
							.equalsIgnoreCase(SwtConstants.TOTAL)) {
						// Condition to check columns ourecs already exist for
						// total
						if (tempForecastMonitorTemplateCol
								.getListForecastMonitorTemplateColSrc() != null
								&& tempForecastMonitorTemplateCol
										.getListForecastMonitorTemplateColSrc()
										.size() > 0) {
							forecastMonitorTemplateColSrcList = tempForecastMonitorTemplateCol
									.getListForecastMonitorTemplateColSrc();
							// set the modified total multiplier sources in
							// total column
							for (ForecastMonitorTemplateColSrc tempForecastMonitorTemplateColSrc : forecastMonitorTemplateColSrcList) {
								// Condition to check whether the column sources
								// exits for selected column name
								if (tempForecastMonitorTemplateColSrc
										.getEntityId().equals(shortName)) {
									forecastMonitorTemplateColSrcList
											.remove(tempForecastMonitorTemplateColSrc);
									break;
								}
							}

						}
						break;
					}
				}
			}
			// Loop through the ForecastMonitorTemplateCol list
			for (ForecastMonitorTemplateCol tempForecastMonitorTemplateCol : forecastMonitorTemplateColList) {
				if (!tempForecastMonitorTemplateCol.getColumnType().equals(
						SwtConstants.FIXED_LABEL)) {
					maxColOrdinalPos = tempForecastMonitorTemplateCol
							.getOrdinalPos();
				}
				if (tempForecastMonitorTemplateCol.getOrdinalPos() >= modifiedForecastTemplateCol
						.getOrdinalPos()) {
					if (isNewColumnFlag
							&& (!tempForecastMonitorTemplateCol.getColumnType()
									.equals(SwtConstants.FIXED_LABEL))) {
						tempForecastMonitorTemplateCol
								.setOrdinalPos(tempForecastMonitorTemplateCol
										.getOrdinalPos() + 1);
					}
				}
			}
			// Set ordinal position for source bean and set the column according
			// to the given ordinal position
			if (maxColOrdinalPos < modifiedForecastTemplateCol.getOrdinalPos()
					&& isNewColumnFlag) {
				modifiedForecastTemplateCol.setOrdinalPos(maxColOrdinalPos + 1);
				maxColOrdinalPos = maxColOrdinalPos + 1;
			}
			// update the column in same ordinal position if the ordinal pos is
			// not changed and chage the location of column if ordinal pos is
			// changed while update
			if (!isNewColumnFlag) {
				if (oldOrdinalPosition > modifiedForecastTemplateCol
						.getOrdinalPos()) {
					for (ForecastMonitorTemplateCol tempForecastMonitorTemplateCol : forecastMonitorTemplateColList) {
						if (tempForecastMonitorTemplateCol.getOrdinalPos() >= modifiedForecastTemplateCol
								.getOrdinalPos()
								&& tempForecastMonitorTemplateCol
										.getOrdinalPos() > 2
								&& tempForecastMonitorTemplateCol
										.getOrdinalPos() < 960) {

							tempForecastMonitorTemplateCol
									.setOrdinalPos(tempForecastMonitorTemplateCol
											.getOrdinalPos() + 1);
						}
					}
				} else if (oldOrdinalPosition < modifiedForecastTemplateCol
						.getOrdinalPos()) {
					for (ForecastMonitorTemplateCol tempForecastMonitorTemplateCol : forecastMonitorTemplateColList) {

						
						// Condition to check column type is not fixed
						if (!tempForecastMonitorTemplateCol.getColumnType()
								.equals(SwtConstants.FIXED_LABEL)) {
							// Get max ordinal position
							maxColOrdinalPos = tempForecastMonitorTemplateCol
									.getOrdinalPos();
						}
						// Condition to check old ordinal position less than or
						// equal to modified and not fixed
						if (tempForecastMonitorTemplateCol.getOrdinalPos() <= modifiedForecastTemplateCol
								.getOrdinalPos()
								&& tempForecastMonitorTemplateCol
										.getOrdinalPos() > 2
								&& tempForecastMonitorTemplateCol
										.getOrdinalPos() < 960) {
							// set other columns old ordinal pos -1
							tempForecastMonitorTemplateCol
									.setOrdinalPos(tempForecastMonitorTemplateCol
											.getOrdinalPos() - 1);

						}
					}
					// Condition to check modified is greater than max ordinal
					// position
					if (modifiedForecastTemplateCol.getOrdinalPos() > maxColOrdinalPos){
						// Start : Code Modified by Vivekanandan A 08-09-2011
						// for 1053 Beta Issues
						if (oldOrdinalPosition > maxColOrdinalPos)
							maxColOrdinalPos = maxColOrdinalPos + 1;
						// End : Code Modified by Vivekanandan A 08-09-2011 for
						// 1053 Beta Issues

						// set max ordinal position
						modifiedForecastTemplateCol
								.setOrdinalPos(maxColOrdinalPos);
					}
				}
			}
			forecastColSrcList = new ArrayList<ForecastMonitorTemplateColSrc>();
			// Add new column sources for selected column name
			for (sourceCounter = 0; sourceCounter < columnSourceType.length; sourceCounter++) {
				// set selected source values collection in Column source bean
				// bean save
				forecastMonitorTemplateColSrc = new ForecastMonitorTemplateColSrc();
				forecastMonitorTemplateColSrc.getId().setHostId(hostId);
				forecastMonitorTemplateColSrc.getId().setTemplateId(templateId);
				if (sourceId.length > 0) {
				forecastMonitorTemplateColSrc.getId().setSourceId(sourceId[sourceCounter]);	
				} else {
					forecastMonitorTemplateColSrc.getId().setSourceId("");	
				}
					
				
				forecastMonitorTemplateColSrc.getId().setSourceType(
						columnSourceType[sourceCounter]);
				forecastMonitorTemplateColSrc
						.setName(sourceName[sourceCounter]);
				forecastMonitorTemplateColSrc
						.setEntityId(entity[sourceCounter]);

				// set ordinal postion values for sub total or total sources
				if ((columnSourceType.length == sourceOrdinalPos.length)
						&& !SwtUtil
								.isEmptyOrNull(sourceOrdinalPos[sourceCounter])) {
					forecastMonitorTemplateColSrc.setOrdinalPos(Integer
							.parseInt(sourceOrdinalPos[sourceCounter]));
				}

				// set multiplier for selected column sources
				if (SwtUtil.isEmptyOrNull(sourceMultiplier[sourceCounter])) {
					forecastMonitorTemplateColSrc.setMultiplier(new BigDecimal(
							1));
				} else {
					forecastMonitorTemplateColSrc.setMultiplier(new BigDecimal(
							sourceMultiplier[sourceCounter]));
				}
				forecastColSrcList.add(forecastMonitorTemplateColSrc);
			}

			// Condition to check column tolumn name is total
			if (shortName.equalsIgnoreCase(SwtConstants.TOTAL)) {
				// loop to set the col source and to set total multiplier for
				// corresponding columns
				for (ForecastMonitorTemplateCol tempForecastMonitorTemplateCol : forecastMonitorTemplateColList) {
					if (!(tempForecastMonitorTemplateCol.getColumnType()
							.equals(SwtConstants.FIXED) && tempForecastMonitorTemplateCol
							.getColumnType().equals(SwtConstants.FIXED_LABEL))) {
						// Add new column sources for selected column name
						for (sourceCounter = 0; sourceCounter < columnSourceType.length; sourceCounter++) {
							if (tempForecastMonitorTemplateCol
									.getColumnDisplayName().equals(
											entity[sourceCounter])) {
								if (!SwtUtil
										.isEmptyOrNull(sourceMultiplier[sourceCounter])) {
									tempForecastMonitorTemplateCol
											.setTotalMultiplier(sourceMultiplier[sourceCounter]);
								}
								break;
							} else {
								tempForecastMonitorTemplateCol
										.setTotalMultiplier("");
							}
						}
					}
				}
			}
			// set src list in col bean
			modifiedForecastTemplateCol
					.setListForecastMonitorTemplateColSrc(forecastColSrcList);
			// Condition to check ordinal pos is less than 960
			if (modifiedForecastTemplateCol.getOrdinalPos() < 960) {
				// set the col value in given ordinal position spot
				forecastMonitorTemplateColList.add(modifiedForecastTemplateCol
						.getOrdinalPos() - 1, modifiedForecastTemplateCol);
			} else {
				// set the col value in max ordinal postition spot
				forecastMonitorTemplateColList.add(maxColOrdinalPos,
						modifiedForecastTemplateCol);
			}
			// Set both list in bean
			request.setAttribute("forecastMonitorTemplate",
					forecastMonitorTemplate);

			// setting reply status as true in request
			request.setAttribute("reply_status_ok", SwtConstants.STR_TRUE);
			// setting the reply message in request
			request.setAttribute("reply_message", SwtConstants.DATA_FETCH_OK);
			request.getSession().setAttribute("forecastMonitorTemplateColList",
					null);

		} catch (Exception e) {
			log.error(this.getClass().getName()
					+ " - [saveTemplatesSrcInSession] - Exception -"
					+ e.getStackTrace()[0].getClassName() + "."
					+ e.getStackTrace()[0].getMethodName() + ":"
					+ e.getStackTrace()[0].getLineNumber() + " "
					+ e.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					e, "saveTemplatesSrcInSession", this.getClass()), request,
					"");
			request.setAttribute("reply_status_ok", SwtConstants.STR_FALSE);
			request.setAttribute("reply_message", e.getMessage());
			request.setAttribute("reply_location", e.getStackTrace()[0]
					.getClassName()
					+ "."
					+ e.getStackTrace()[0].getMethodName()
					+ ":"
					+ e.getStackTrace()[0].getLineNumber());
			return ("dataerror");
		} finally {
			log.debug(this.getClass().getName()
					+ " - [saveTemplatesSrcInSession] - Exit");
		}
		return ("templateadd");
	}

	/**
	 * 
	 * Method to update forecast templates which in turn save/update/delete
	 * columns and column sources
	 * 
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return ActionForward
	 * @throws SwtException
	 */

	public String updateTemplates() throws SwtException {
HttpServletRequest request = ServletActionContext.getRequest();
HttpServletResponse response = ServletActionContext.getResponse();


		// Method's local variable declaration
		/* Variable Declaration for dyForm */
		// DynaValidatorForm dyForm = null;
		/* Variable Declaration for collMonitorDetails */
		List<ForecastMonitorTemplate> listMonitorTemplateDetails = null;
		// Insatnce for forecast Monitor template
		ForecastMonitorTemplate forecastMonitorTemplate = null;
		// Insatnce for forecast Monitor template Columns
		ForecastMonitorTemplateCol forecastMonitorTemplateCol = null;
		// String to hold hostId
		String hostId = null;
		// String to hold templateId
		String templateId = null;
		// String to hold templateName
		String templateName = null;
		// String to hold userId
		String userId = null;
		// String to hold isPublic
		String isPublic = null;
		/* Array list to hold forecast columns collection */
		ArrayList<ForecastMonitorTemplateCol> modifiedForecastColList = null;
		// variable to hold ForecastMonitorTemplateCol
		ForecastMonitorTemplateCol fcastTemplateCol = null;
		// Array list to get ForecastMonitorTemplateColSrc to save
		List<ForecastMonitorTemplateCol> monitorTemplateCol = null;
		// Array list to get ForecastMonitorTemplateColSrc values
		Iterator<ForecastMonitorTemplateCol> monitorTemplateColItr = null;
		// Array list to get ForecastMonitorTemplateColSrc values
		Iterator<ForecastMonitorTemplateCol> facstmonitorTemplateColItr = null;
		// list to get ForecastMonitorTemplateColSrc values
		List<ForecastMonitorTemplateColSrc> monitorTemplateColSrcList = null;
		// Iterator to get ForecastMonitorTemplateColSrc values
		Iterator<ForecastMonitorTemplateColSrc> monitorTemplateColSrcItr = null;
		// boolean falg to save ordinal position
		boolean fixedFlag = false;
		// String variable to hold copyFromFlag
		String copyFromFlag = null;
		Iterator<ForecastMonitorTemplateCol> monitorTemplateColNameItr = null;
		// variable to hold ForecastMonitorTemplateCol
		ForecastMonitorTemplateCol fcastTemplateColName = null;
		// list to get ForecastMonitorTemplateCol values
		List<ForecastMonitorTemplateCol> lstForecastMonitorTemplateCol = null;
		// Iterator to get ForecastMonitorTemplateCol values
		Iterator<ForecastMonitorTemplateCol> itrForecastMonitorTemplateCol = null;
		// list to get ForecastMonitorTemplateColSrc values
		List<ForecastMonitorTemplateColSrc> srclist = null;
		// saveDisplayName
		String[] saveDisplayName = null;
		// saveOrdinalPos
		String[] saveOrdinalPos = null;
		// ForecastMonitorTemplateColSrc instance
		ForecastMonitorTemplateColSrc forecastMonitorTemplateColSrc = null;
		// ForecastMonitorTemplateColSrc Instance
		ForecastMonitorTemplateColSrc forecastTemplateColSrc = null;
		// ForecastMonitorTemplateCol instance
		ForecastMonitorTemplateCol forecastTemplateCol = null;
		try {

			// debug message
			log.debug(this.getClass().getName() + "-[updateTemplates]-Entry");
			// set the form object into // DynaValidatorForm
			// Get current hostId from DB
			hostId = SwtUtil.getCurrentHostId();

			// read templateid from request
			templateId = request.getParameter("templateId");

			// read template namefrom request
			templateName = request.getParameter("templateName");
			// saveDisplayName from form
			// saveDisplayName from form
			saveDisplayName = request.getParameter("saveDisplayName").split(",");
			// saveOrdinalPos from form
			saveOrdinalPos =  request.getParameter("saveOrdinalPos").split(",");

			// read user name from request
			userId = request.getParameter("userId");

			// read isPublic from request
			isPublic = request.getParameter("isPublic");

			// get the forecastMonitorTemplate for object
			forecastMonitorTemplate = (ForecastMonitorTemplate) getForecastMonitorTemplate();

			// set hostId in template
			forecastMonitorTemplate.getId().setHostId(hostId);

			// set templateId in template
			forecastMonitorTemplate.getId().setTemplateId(templateId);

			// set userId in template
			forecastMonitorTemplate.setUserId(userId);

			// set template name in template
			forecastMonitorTemplate.setTemplateName(templateName);

			// set ispublic name in template
			forecastMonitorTemplate.setPublicTemplate(isPublic);

			forecastMonitorTemplateCol = new ForecastMonitorTemplateCol();

			// save forecast template
			forecastMonitorTemplateManager
					.updateTemplate(forecastMonitorTemplate);

			// read copy from flag from request
			copyFromFlag = request.getParameter("copyFromFlag");

			// Condition to check copyFromFlag is true
			if (!SwtUtil.isEmptyOrNull(copyFromFlag)
					&& copyFromFlag.equals("true")) {
				// Delete existing column source and columns defined for the
				// template
				forecastMonitorTemplateColSrc = new ForecastMonitorTemplateColSrc();
				forecastMonitorTemplateColSrc.getId().setHostId(hostId);
				forecastMonitorTemplateColSrc.getId().setTemplateId(
						forecastMonitorTemplate.getId().getTemplateId());
				forecastMonitorTemplateColSrc.setUserId(forecastMonitorTemplate
						.getUserId());
				forecastMonitorTemplateManager
						.deleteAllForecastMonitorTemplateColSrc(forecastMonitorTemplateManager
								.getForecastMonitorTemplateColSrc(forecastMonitorTemplateColSrc));

				forecastMonitorTemplateCol.getId().setTemplateId(
						forecastMonitorTemplate.getId().getTemplateId());
				forecastMonitorTemplateCol.getId().setHostId(hostId);
				forecastMonitorTemplateCol.setUserId(forecastMonitorTemplate
						.getUserId());
				forecastMonitorTemplateManager
						.deleteAllForecastMonitorTemplateCol(forecastMonitorTemplateManager
								.getForecastMonitorTemplateCol(forecastMonitorTemplateCol));

			}

			// Obtain forecast template bean from session
			forecastMonitorTemplate = (ForecastMonitorTemplate) request
					.getSession().getAttribute("forecastMonitorTemplate");

			// Condition to check deleted list is not null
			if (forecastMonitorTemplate
					.getDeleteForecastMonitorTemplateColList() != null
					&& forecastMonitorTemplate
							.getDeleteForecastMonitorTemplateColList().size() > 0) {
				// delete forecast column source list
				for (ForecastMonitorTemplateCol forecastMonTemplateCol : forecastMonitorTemplate
						.getDeleteForecastMonitorTemplateColList()) {
					forecastMonitorTemplateManager
							.deleteAllForecastMonitorTemplateColSrc(forecastMonTemplateCol
									.getListForecastMonitorTemplateColSrc());

					// remove the total multiplier list if total multiplier
					// is null
					forecastTemplateColSrc = new ForecastMonitorTemplateColSrc();
					/* Set values to forecast col bean */
					forecastTemplateColSrc.setEntityId(forecastMonTemplateCol
							.getColumnDisplayName());
					forecastTemplateColSrc.setName(forecastMonTemplateCol
							.getColumnDescription());
					forecastTemplateColSrc.getId().setSourceId(
							forecastMonTemplateCol.getId().getColumnId());
					forecastTemplateColSrc.getId().setTemplateId(templateId);
					forecastTemplateColSrc.getId().setHostId(hostId);
					forecastTemplateColSrc.setUserId(userId);
					forecastTemplateColSrc.getId().setSourceType(
							SwtConstants.TOTAL_COLUMN_SOURCETYPE);
					forecastTemplateColSrc.getId().setColumnId(
							SwtConstants.TOTAL.toUpperCase());

					forecastMonitorTemplateManager
							.deleteTemplateColSrc(forecastTemplateColSrc);

					if (!SwtUtil.isEmptyOrNull(forecastMonTemplateCol.getId()
							.getColumnId()))
						// delete forecast column
						forecastMonitorTemplateManager
								.deleteTemplateCol(forecastMonTemplateCol);
				}
			}
			// Condition to check forecast list is empty
			if (forecastMonitorTemplate.getForecastTemplateColList() != null
					&& forecastMonitorTemplate.getForecastTemplateColList()
							.size() > 0) {
				// get col list from session
				lstForecastMonitorTemplateCol = forecastMonitorTemplate
						.getForecastTemplateColList();
				// Iterate list
				itrForecastMonitorTemplateCol = lstForecastMonitorTemplateCol
						.iterator();
				modifiedForecastColList = new ArrayList<ForecastMonitorTemplateCol>();
				// lop to get col values
				while (itrForecastMonitorTemplateCol.hasNext()) {
					// get col object
					forecastTemplateCol = itrForecastMonitorTemplateCol.next();
					for (int ordCounter = 0; ordCounter < saveOrdinalPos.length; ordCounter++) {
						// set ordinal position as chaged in template main
						// screen
						if (saveDisplayName[ordCounter]
								.equals(forecastTemplateCol
										.getColumnDisplayName())) {
							// set ordinal position as collection size
							forecastTemplateCol.setOrdinalPos(Integer
									.parseInt(saveOrdinalPos[ordCounter]));
							break;
						}
					}
					// set template col id fields
					forecastTemplateCol.getId().setTemplateId(templateId);
					forecastTemplateCol.getId().setHostId(hostId);
					forecastTemplateCol.setUserId(userId);
					// Condition to check col is fixed to set fixed ordinal
					// position values
					if (forecastTemplateCol.getColumnType().equals(
							SwtConstants.FIXED_LABEL)) {
						forecastTemplateCol.setColumnType(SwtConstants.FIXED);

						// Condition to check display name is bucket
						if (forecastTemplateCol.getColumnDisplayName().equals(
								"Bucket"))
							// set ordinal position as 1
							forecastTemplateCol.setOrdinalPos(1);
						// Condition to check display name is date
						else if (forecastTemplateCol.getColumnDisplayName()
								.equals("Date"))
							// set ordinal position as 2
							forecastTemplateCol.setOrdinalPos(2);
						// Condition to check display name is total
						else if (forecastTemplateCol.getColumnDisplayName()
								.equals(SwtConstants.TOTAL)) {
							// set ordinal position as 960
							forecastTemplateCol.setOrdinalPos(960);
							// Condition to check display name is
							// Assumption
						} else if (forecastTemplateCol.getColumnDisplayName()
								.equals("Assumption"))
							// set ordinal position as 970
							forecastTemplateCol.setOrdinalPos(970);
						// Condition to check display name is Scenario
						else if (forecastTemplateCol.getColumnDisplayName()
								.equals("Scenario"))
							// set ordinal position as 980
							forecastTemplateCol.setOrdinalPos(980);
						// Condition to check display name is Grand
						// Total
						else if (forecastTemplateCol.getColumnDisplayName()
								.equals("Grand Total"))
							// set ordinal position as 990
							forecastTemplateCol.setOrdinalPos(990);

						fixedFlag = true;
						forecastTemplateCol.setUserEditable("N");

					} else if (forecastTemplateCol.getColumnType().equals(
							SwtConstants.SUB_TOTAL_LABEL)) {
						forecastTemplateCol
								.setColumnType(SwtConstants.SUB_TOTAL);
						fixedFlag = false;
					} else{
						forecastTemplateCol.setColumnType(SwtConstants.NORMAL);
						fixedFlag = false;
					}

					// Condition to check row is not fixed
					if (!fixedFlag) {
						forecastTemplateCol.setUserEditable("Y");
					}

					/*
					 * Condition to check modified value is save
					 */
					if (!SwtUtil.isEmptyOrNull(forecastTemplateCol
							.getModifiedValue())
							&& forecastTemplateCol.getModifiedValue().equals(
									"save")) {

						// save new forecast columns
						forecastMonitorTemplateManager
								.saveTemplateCol(forecastTemplateCol);
					} else if (SwtUtil.isEmptyOrNull(forecastTemplateCol
							.getModifiedValue())
							|| forecastTemplateCol.getModifiedValue().equals(
									"update")) {

						// update new forecast columns
						forecastMonitorTemplateManager
								.updateTemplateCol(forecastTemplateCol);

					}
					modifiedForecastColList.add(forecastTemplateCol);
				}
				facstmonitorTemplateColItr = modifiedForecastColList.iterator();
				while (facstmonitorTemplateColItr.hasNext()) {

					forecastTemplateCol = facstmonitorTemplateColItr.next();

					// Condition to check delete list is not empty
					if (!forecastTemplateCol.getId().getColumnId()
							.equalsIgnoreCase(SwtConstants.TOTAL)
							&& forecastTemplateCol
									.getDeleteForecastMonitorTemplateColSrcList() != null
							&& forecastTemplateCol
									.getDeleteForecastMonitorTemplateColSrcList()
									.size() > 0) {
						// remove the column src values given in delete list
						for (ForecastMonitorTemplateColSrc forecastMonTemplateColSrc : forecastTemplateCol
								.getDeleteForecastMonitorTemplateColSrcList()) {

							forecastMonTemplateColSrc.getId()
									.setTemplateId(
											forecastTemplateCol.getId()
													.getTemplateId());
							forecastMonTemplateColSrc.getId().setColumnId(
									forecastTemplateCol.getId().getColumnId());
							forecastMonitorTemplateManager
									.deleteTemplateColSrc(forecastMonTemplateColSrc);
						}

					}

					// Coiplier is not null to add total source values
					if (!SwtUtil.isEmptyOrNull(forecastTemplateCol
							.getTotalMultiplier())) {
						if (!forecastTemplateCol.getId().getColumnId()
								.equalsIgnoreCase(SwtConstants.TOTAL)) {
							forecastTemplateColSrc = new ForecastMonitorTemplateColSrc();
							/* Set values to forecast col bean */
							forecastTemplateColSrc
									.setEntityId(forecastTemplateCol
											.getColumnDisplayName());
							forecastTemplateColSrc.setName(forecastTemplateCol
									.getColumnDescription());
							forecastTemplateColSrc.getId().setTemplateId(
									templateId);
							forecastTemplateColSrc.getId().setSourceId(
									forecastTemplateCol.getId().getColumnId());
							forecastTemplateColSrc.getId().setHostId(hostId);
							forecastTemplateColSrc.setUserId(userId);
							forecastTemplateColSrc.getId().setSourceType(
									SwtConstants.TOTAL_COLUMN_SOURCETYPE);
							forecastTemplateColSrc.getId().setColumnId(
									SwtConstants.TOTAL.toUpperCase());
							forecastTemplateColSrc
									.setMultiplier(new BigDecimal(
											forecastTemplateCol
													.getTotalMultiplier()));
							forecastTemplateColSrc.setTotalChanged("false");

							// check whether the source is exist
							srclist = forecastMonitorTemplateManager
									.checkForecastExist(forecastTemplateColSrc);

							// save the source if it is not exist
							if (srclist.size() == 0) {
								forecastMonitorTemplateManager
										.saveTemplateColSrc(forecastTemplateColSrc);
							} else {
								// update the source if it is exist
								forecastMonitorTemplateManager
										.updateTemplateColSrc(forecastTemplateColSrc);
							}
						}
					} else {
						// remove the total multiplier list if total multiplier
						// is null
						forecastTemplateColSrc = new ForecastMonitorTemplateColSrc();
						/* Set values to forecast col bean */
						forecastTemplateColSrc.setEntityId(forecastTemplateCol
								.getColumnDisplayName());
						forecastTemplateColSrc.setName(forecastTemplateCol
								.getColumnDescription());
						forecastTemplateColSrc.getId().setSourceId(
								forecastTemplateCol.getId().getColumnId());
						forecastTemplateColSrc.getId()
								.setTemplateId(templateId);
						forecastTemplateColSrc.getId().setHostId(hostId);
						forecastTemplateColSrc.setUserId(userId);
						forecastTemplateColSrc.getId().setSourceType(
								SwtConstants.TOTAL_COLUMN_SOURCETYPE);
						forecastTemplateColSrc.getId().setColumnId(
								SwtConstants.TOTAL.toUpperCase());

						forecastMonitorTemplateManager
								.deleteTemplateColSrc(forecastTemplateColSrc);
					}

					// get template column collection to get column id
					monitorTemplateCol = forecastMonitorTemplateManager
							.getForecastMonitorTemplateCol(forecastTemplateCol);

					monitorTemplateColItr = monitorTemplateCol.iterator();
					// Loop to get iterate monitorTemplateCol
					while (monitorTemplateColItr.hasNext()) {
						fcastTemplateCol = monitorTemplateColItr.next();
						// Condition to check column bean in session and data
						// bas is equal
						if (forecastTemplateCol.getColumnDisplayName().equals(
								fcastTemplateCol.getColumnDisplayName())) {
							// get column source list from col bean
							monitorTemplateColSrcList = forecastTemplateCol
									.getListForecastMonitorTemplateColSrc();
							// check monitorTemplateColSrcList is not null
							if (monitorTemplateColSrcList != null
									&& monitorTemplateColSrcList.size() > 0) {
								// Iterate the collection
								monitorTemplateColSrcItr = monitorTemplateColSrcList
										.iterator();
								// get column source values
								while (monitorTemplateColSrcItr.hasNext()) {
									forecastMonitorTemplateColSrc = monitorTemplateColSrcItr
											.next();
									// set id values of source bean
									forecastMonitorTemplateColSrc.getId()
											.setHostId(hostId);
									// check column id is total
									if ((!SwtUtil
											.isEmptyOrNull(forecastMonitorTemplateColSrc
													.getId().getColumnId()) && forecastMonitorTemplateColSrc
											.getId().getColumnId().equals(
													SwtConstants.TOTAL
															.toUpperCase()))
											|| forecastMonitorTemplateColSrc
													.getTotalMultiplier() != null) {
										// set column id as source id
										forecastMonitorTemplateColSrc.getId()
												.setSourceId(
														fcastTemplateCol
																.getId()
																.getColumnId());
										// check column ty pe is not normal
									} else if ((!fcastTemplateCol
											.getColumnType().equals(
													SwtConstants.NORMAL))
											&& (forecastMonitorTemplateColSrc
													.getId().getSourceType()
													.equals(SwtConstants.TOTAL_COLUMN_SOURCETYPE))) {
										// set column id
										forecastMonitorTemplateColSrc.getId()
												.setColumnId(
														fcastTemplateCol
																.getId()
																.getColumnId());
										// iterate the temp col collection
										monitorTemplateColNameItr = monitorTemplateCol
												.iterator();
										// loop to get col bean from collection
										while (monitorTemplateColNameItr
												.hasNext()) {
											fcastTemplateColName = monitorTemplateColNameItr
													.next();
											// Condition to check column display
											// name is equal to the normal name
											// given in entity id
											if (fcastTemplateColName
													.getColumnDisplayName()
													.equals(
															forecastMonitorTemplateColSrc
																	.getEntityId())) {
												// set source id as column id
												forecastMonitorTemplateColSrc
														.getId()
														.setSourceId(
																fcastTemplateColName
																		.getId()
																		.getColumnId());
												// beark the inner loop
												break;

											}

										}

									} else {
										// set column id for normla values
										forecastMonitorTemplateColSrc.getId()
												.setColumnId(
														fcastTemplateCol
																.getId()
																.getColumnId());
									}

									// set template id
									forecastMonitorTemplateColSrc.getId()
											.setTemplateId(
													fcastTemplateCol.getId()
															.getTemplateId());
									// set user id
									forecastMonitorTemplateColSrc
											.setUserId(fcastTemplateCol
													.getUserId());

									// Condition to check selected type is book
									if (forecastMonitorTemplateColSrc.getId()
											.getSourceType().equals(
													SwtConstants.BOOK_LABEL))
										forecastMonitorTemplateColSrc.getId()
												.setSourceType(
														SwtConstants.BOOK);
									// Condition to check selected type is group
									else if (forecastMonitorTemplateColSrc
											.getId().getSourceType().equals(
													SwtConstants.GROUP_LABEL))
										forecastMonitorTemplateColSrc.getId()
												.setSourceType(
														SwtConstants.GROUP);

									// Condition to check selected type is meta
									// group
									else if (forecastMonitorTemplateColSrc
											.getId()
											.getSourceType()
											.equals(
													SwtConstants.META_GROUP_LABEL))
										forecastMonitorTemplateColSrc
												.getId()
												.setSourceType(
														SwtConstants.META_GROUP);
									// Condition to check selected type is
									// entity
									else if (forecastMonitorTemplateColSrc
											.getId().getSourceType().equals(
													SwtConstants.ENTITY_LABEL))
										forecastMonitorTemplateColSrc.getId()
												.setSourceType(
														SwtConstants.ENTITY);
									// Condition to check modified value is save
									// to save source columns
									if (forecastMonitorTemplateColSrc
											.getModifiedValue() == null
											|| forecastMonitorTemplateColSrc
													.getModifiedValue()
													.equalsIgnoreCase("save")) {
										// check whther sources are exist
										srclist = forecastMonitorTemplateManager
												.checkForecastExist(forecastMonitorTemplateColSrc);
										// Condition to check sources are exits
										if (srclist.size() == 0
												&& (!forecastMonitorTemplateColSrc
														.getId()
														.getColumnId()
														.equalsIgnoreCase(
																SwtConstants.TOTAL))) {
											// save the source bean values
											forecastMonitorTemplateManager
													.saveTemplateColSrc(forecastMonitorTemplateColSrc);
										} else {
											// update the source bean values
											forecastMonitorTemplateManager
													.updateTemplateColSrc(forecastMonitorTemplateColSrc);
										}

									} else if (!SwtUtil
											.isEmptyOrNull(forecastMonitorTemplateColSrc
													.getModifiedValue())
											&& forecastMonitorTemplateColSrc
													.getModifiedValue()
													.equalsIgnoreCase("delete")) {

										forecastMonitorTemplateManager
												.deleteTemplateColSrc(forecastMonitorTemplateColSrc);
									} else {
										// check whther sources are exist
										srclist = forecastMonitorTemplateManager
												.checkForecastExist(forecastMonitorTemplateColSrc);
										// Condition to check sources are exits
										if (srclist.size() == 0
												&& (!forecastMonitorTemplateColSrc
														.getId()
														.getColumnId()
														.equalsIgnoreCase(
																SwtConstants.TOTAL))) {
											// save the source bean values
											forecastMonitorTemplateManager
													.saveTemplateColSrc(forecastMonitorTemplateColSrc);
										} else {
											// update the source bean values
											forecastMonitorTemplateManager
													.updateTemplateColSrc(forecastMonitorTemplateColSrc);
										}
									}
								}
							}
							break;
						}
					}
				}
			}
			request.getSession().setAttribute("forecastMonitorTemplate", null);

			request.setAttribute("forecastMonitorTemplate",
					forecastMonitorTemplate);
			request.setAttribute("listMonitorTemplateDetails",
					listMonitorTemplateDetails);
			// Set template Id in request
			request.setAttribute("selectedTemplateId", templateId);
			// Set template Name in request
			request.setAttribute("selectedTemplateName", templateName);

			// Set both list in bean
			request.setAttribute("listMonitorTemplateDetailsCol",
					new ArrayList<ForecastMonitorTemplateCol>());
			request.setAttribute("templateUsersList", new ArrayList<User>());

			// set form object
			setForecastMonitorTemplate(forecastMonitorTemplate);

			// setting reply status as true in request
			request.setAttribute("reply_status_ok", SwtConstants.STR_TRUE);
			// setting the reply message in request
			request.setAttribute("reply_message", SwtConstants.DATA_FETCH_OK);
		} catch (SwtException e) {
			log.error(this.getClass().getName()
					+ " - [updateTemplates] - Exception -"
					+ e.getStackTrace()[0].getClassName() + "."
					+ e.getStackTrace()[0].getMethodName() + ":"
					+ e.getStackTrace()[0].getLineNumber() + " "
					+ e.getMessage());
			request.setAttribute("reply_status_ok", SwtConstants.STR_FALSE);
			request.setAttribute("reply_message", e.getMessage());
			request.setAttribute("reply_location", e.getStackTrace()[0]
					.getClassName()
					+ "."
					+ e.getStackTrace()[0].getMethodName()
					+ ":"
					+ e.getStackTrace()[0].getLineNumber());
			SwtUtil.logException(e, request, "");
			return ("dataerror");
		} catch (Exception e) {
			log.error(this.getClass().getName()
					+ " - [updateTemplates] - Exception -"
					+ e.getStackTrace()[0].getClassName() + "."
					+ e.getStackTrace()[0].getMethodName() + ":"
					+ e.getStackTrace()[0].getLineNumber() + " "
					+ e.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					e, "updateTemplates", this.getClass()), request, "");
			request.setAttribute("reply_status_ok", SwtConstants.STR_FALSE);
			request.setAttribute("reply_message", e.getMessage());
			request.setAttribute("reply_location", e.getStackTrace()[0]
					.getClassName()
					+ "."
					+ e.getStackTrace()[0].getMethodName()
					+ ":"
					+ e.getStackTrace()[0].getLineNumber());
			return ("dataerror");
		} finally {
			log.debug(this.getClass().getName()
					+ " - [updateTemplates] - Exit");
		}
		return ("templateadd");
	}

	/**
	 * 
	 * Method to load the Add Forecast Monitor Templates load by copy from
	 * screen
	 * 
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return
	 * @throws SwtException
	 */

	public String displayCopyMonitorTemplate() throws SwtException {
HttpServletRequest request = ServletActionContext.getRequest();
HttpServletResponse response = ServletActionContext.getResponse();

		// Method's local variable declaration
		// variable to hold hostId
		String hostId = null;
		/* Variable Declaration for dyForm */
		// DynaValidatorForm dyForm = null;
		/* Variable Declaration for collMonitorDetails */
		List<ForecastMonitorTemplateCol> listMonitorTemplateDetailsCol = null;
		/* Variable Declaration for collMonitorDetails */
		List<ForecastMonitorTemplateCol> listMonitorTemplateDetailColForSession = null;
		/* Variable Declaration for collMonitorDetails */
		Iterator<ForecastMonitorTemplateCol> itrMonitorTemplateDetailsCol = null;
		// Insatnce for forecast Monitor template
		ForecastMonitorTemplateCol forecastMonitorTemplateCol = null;
		// Insatnce for forecast Monitor template
		ForecastMonitorTemplate forecastMonitorTemplate = null;
		// Array list to hold User Collection
		ArrayList<User> templateUsersList = null;
		// User Instance
		User user = null;
		// UserMaintenance Instance
		UserMaintenance userMaintenance = null;
		// String to hold selectedTemplateId
		String selectedTemplateId = null;
		// String to hold selectedUserId
		String selectedUserId = null;
		// User Dao Hibenate Instance
		UserMaintenanceManager userMaintenanceManagerImpl = null;
		// forecastMonitorTemplateColSrc instance
		ForecastMonitorTemplateColSrc forecastMonitorTemplateColSrc = null;
		// List to hol coped template src list
		List<ForecastMonitorTemplateColSrc> forecastMonitorTemplateColSrcList = null;
		try {
			// debug message
			log.debug(this.getClass().getName()
					+ "-[displayCopyMonitorTemplate]-Entry");

			// Get hostId from DB
			hostId = SwtUtil.getCurrentHostId();

			// initialize forecastMonitorTemplate bean
			forecastMonitorTemplate = new ForecastMonitorTemplate();

			// Read templateId from request
			selectedTemplateId = request.getParameter("templateId");

			// Read selected user from request
			selectedUserId = request.getParameter("userId");
			// Set template Id in request
			request.setAttribute("selectedTemplateId", "");
			// Set template Name in request
			request.setAttribute("selectedTemplateName", "");
			// Set user Id in request
			request.setAttribute("selectedUserId", selectedUserId);
			// Set is public in request
			request.setAttribute("isPublic", request.getParameter("isPublic"));
			if (SwtUtil.isEmptyOrNull(request.getParameter("screen")))
				// Set is screen Name as change Screen
				request.setAttribute("screenName", "changeScreen");

			if (SwtUtil.isEmptyOrNull(request.getParameter("loadFlex"))) {
				// debug message
				log.debug(this.getClass().getName()
						+ "-[displayCopyMonitorTemplate]-Exit to load SWF");
				return ("templateaddflex");
			}

			// set the form object into // DynaValidatorForm
			// get the forecastMonitorTemplate for object
			forecastMonitorTemplateCol = (ForecastMonitorTemplateCol)getForecastMonitorTemplateCol();

			// Instantiate list
			templateUsersList = new ArrayList<User>();
			// Set host Id in bean
			forecastMonitorTemplateCol.getId().setHostId(hostId);
			// Set user Id in bean
			forecastMonitorTemplateCol.setUserId(selectedUserId);
			// Set template Id in bean
			forecastMonitorTemplateCol.getId()
					.setTemplateId(selectedTemplateId);

			listMonitorTemplateDetailColForSession = new ArrayList<ForecastMonitorTemplateCol>();
			// Get Template columns for selected template ID
			listMonitorTemplateDetailsCol = forecastMonitorTemplateManager
					.getForecastMonitorTemplateCol(forecastMonitorTemplateCol);

			// iterate the collection
			itrMonitorTemplateDetailsCol = listMonitorTemplateDetailsCol
					.iterator();
			// loop to get col and source for copied template id
			while (itrMonitorTemplateDetailsCol.hasNext()) {
				forecastMonitorTemplateCol = itrMonitorTemplateDetailsCol
						.next();
				// Instantiate list
				forecastMonitorTemplateColSrcList = new ArrayList<ForecastMonitorTemplateColSrc>();
				// Condition to check columnare not fixed and should allow total
				// column
				if ((!forecastMonitorTemplateCol.getColumnType().equals(
						SwtConstants.FIXED))
						|| forecastMonitorTemplateCol.getColumnDisplayName()
								.equalsIgnoreCase(SwtConstants.TOTAL)) {

					// Condition to check column type is normal
					if (!forecastMonitorTemplateCol.getColumnType().equals(
							SwtConstants.NORMAL)) {
						// set id values of source in bean to get sub total or
						// total values
						forecastMonitorTemplateColSrc = new ForecastMonitorTemplateColSrc();
						forecastMonitorTemplateColSrc.getId().setHostId(hostId);
						forecastMonitorTemplateColSrc.setUserId(selectedUserId);
						forecastMonitorTemplateColSrc.getId().setTemplateId(
								selectedTemplateId);
						forecastMonitorTemplateColSrc.getId().setColumnId(
								forecastMonitorTemplateCol.getId()
										.getColumnId());
						// get subtotal or total column
						for (ForecastMonitorTemplateColSrc forecastMonitorTemplateSrc : forecastMonitorTemplateManager
								.getForecastMonitorTemplateColSrcForColumnId(
										forecastMonitorTemplateColSrc,
										forecastMonitorTemplateCol
												.getColumnType())) {
							// loop to get colbean to set column name and
							// ordinal positio to set column source bean
							for (ForecastMonitorTemplateCol forecastCol : listMonitorTemplateDetailsCol) {
								// Condition to chcek column name in col bean is
								// equal to col name given in entity value in
								// source bean
								if (forecastCol.getColumnDisplayName().equals(
										forecastMonitorTemplateSrc
												.getEntityId())) {
									// set ordinal position
									forecastMonitorTemplateSrc
											.setOrdinalPos(forecastCol
													.getOrdinalPos());
									// set column description
									forecastMonitorTemplateSrc
											.setName(forecastCol
													.getColumnDescription());
									break;
								}
							}
							// Condition to get column id is not total
							if (forecastMonitorTemplateSrc.getId()
									.getColumnId().equalsIgnoreCase(
											SwtConstants.TOTAL)
									&& !forecastMonitorTemplateCol
											.getColumnDisplayName()
											.equalsIgnoreCase(
													SwtConstants.TOTAL)) {
								// set total multiplier for the total values
								forecastMonitorTemplateCol
										.setTotalMultiplier(forecastMonitorTemplateSrc
												.getMultiplier().toString());

							} else {
								// set other values in list
								forecastMonitorTemplateColSrcList
										.add(forecastMonitorTemplateSrc);
							}
						}
						// set column source list in col bean
						forecastMonitorTemplateCol
								.setListForecastMonitorTemplateColSrc(forecastMonitorTemplateColSrcList);

					} else {
						// set id values of source in bean to get sub total or
						// total values
						forecastMonitorTemplateColSrc = new ForecastMonitorTemplateColSrc();
						forecastMonitorTemplateColSrc
								.setEntityId(forecastMonitorTemplateCol
										.getColumnDisplayName());
						forecastMonitorTemplateColSrc
								.setName(forecastMonitorTemplateCol
										.getColumnDescription());
						forecastMonitorTemplateColSrc.getId().setHostId(hostId);
						forecastMonitorTemplateColSrc.setUserId(selectedUserId);
						forecastMonitorTemplateColSrc.getId().setTemplateId(
								selectedTemplateId);
						forecastMonitorTemplateColSrc.getId().setColumnId(
								forecastMonitorTemplateCol.getId()
										.getColumnId());
						// get normal columns
						for (ForecastMonitorTemplateColSrc forecastMonitorTemplateSrc : forecastMonitorTemplateManager
								.getChangeForecastMonitorTemplateColSrc(forecastMonitorTemplateColSrc)) {
							if (forecastMonitorTemplateSrc.getId()
									.getColumnId().equalsIgnoreCase(
											SwtConstants.TOTAL)) {
								// set other values in list
								forecastMonitorTemplateCol
										.setTotalMultiplier(forecastMonitorTemplateSrc
												.getMultiplier().toString());
							} else {
								// set total multiplier for the total values
								forecastMonitorTemplateColSrcList
										.add(forecastMonitorTemplateSrc);
							}
						}
						// set column source list in col bean
						forecastMonitorTemplateCol
								.setListForecastMonitorTemplateColSrc(forecastMonitorTemplateColSrcList);

					}
				}
				// Set template Id as null in bean
				forecastMonitorTemplateCol.getId().setTemplateId(null);
				// set modified values as save
				forecastMonitorTemplateCol.setModifiedValue("save");
				// set columns in session
				listMonitorTemplateDetailColForSession
						.add(forecastMonitorTemplateCol);

			}

			// get manager bean
			userMaintenanceManagerImpl = (UserMaintenanceManager) (SwtUtil
					.getBean("usermaintenanceManager"));

			// get user id values for current user id bean
			userMaintenance = userMaintenanceManagerImpl.fetchUserDetail(
					hostId, SwtUtil.getCurrentUserId(request.getSession()));

			// set user values in user list to display
			user = new User();
			user.getId().setUserId(userMaintenance.getId().getUserId());
			user.setUserName(userMaintenance.getUsername());
			templateUsersList.add(user);
			// Instantiate User bean for default users
			user = new User();
			// set default user id in user bean
			user.getId().setUserId(SwtConstants.DEFAULT);
			// set user name in bean
			user.setUserName(SwtConstants.DEFAULT);
			// Add default user in collection
			templateUsersList.add(user);

			// Add row colors
			listMonitorTemplateDetailColForSession = addDataGridColors(listMonitorTemplateDetailColForSession);
			// set column sourecs in template
			forecastMonitorTemplate
					.setForecastTemplateColList(listMonitorTemplateDetailColForSession);
			// set user list in template
			forecastMonitorTemplate.setTemplateUsersList(templateUsersList);
			// set forecastMonitorTemplate in session
			request.getSession().setAttribute("forecastMonitorTemplate",
					forecastMonitorTemplate);
			// set forecastMonitorTemplate in request
			request.setAttribute("forecastMonitorTemplate",
					forecastMonitorTemplate);
			// Set both list in bean
			request.setAttribute("listMonitorTemplateDetailsCol",
					listMonitorTemplateDetailsCol);
			request.setAttribute("templateUsersList", templateUsersList);

			setForecastMonitorTemplateCol(forecastMonitorTemplateCol);
			// setting reply status as true in request
			request.setAttribute("reply_status_ok", SwtConstants.STR_TRUE);
			// setting the reply message in request
			request.setAttribute("reply_message", SwtConstants.DATA_FETCH_OK);
		} catch (SwtException e) {
			log.error(this.getClass().getName()
					+ " - [displayCopyMonitorTemplate] - Exception -"
					+ e.getStackTrace()[0].getClassName() + "."
					+ e.getStackTrace()[0].getMethodName() + ":"
					+ e.getStackTrace()[0].getLineNumber() + " "
					+ e.getMessage());
			request.setAttribute("reply_status_ok", SwtConstants.STR_FALSE);
			request.setAttribute("reply_message", e.getMessage());
			request.setAttribute("reply_location", e.getStackTrace()[0]
					.getClassName()
					+ "."
					+ e.getStackTrace()[0].getMethodName()
					+ ":"
					+ e.getStackTrace()[0].getLineNumber());
			SwtUtil.logException(e, request, "");
			return ("dataerror");
		} catch (Exception e) {
			log.error(this.getClass().getName()
					+ " - [displayCopyMonitorTemplate] - Exception -"
					+ e.getStackTrace()[0].getClassName() + "."
					+ e.getStackTrace()[0].getMethodName() + ":"
					+ e.getStackTrace()[0].getLineNumber() + " "
					+ e.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					e, "displayCopyMonitorTemplate", this.getClass()), request,
					"");
			request.setAttribute("reply_status_ok", SwtConstants.STR_FALSE);
			request.setAttribute("reply_message", e.getMessage());
			request.setAttribute("reply_location", e.getStackTrace()[0]
					.getClassName()
					+ "."
					+ e.getStackTrace()[0].getMethodName()
					+ ":"
					+ e.getStackTrace()[0].getLineNumber());
			return ("dataerror");
		} finally {
			log.debug(this.getClass().getName()
					+ " - [displayCopyMonitorTemplate] - Exit");
		}
		return ("templateadd");
	}

	/**
	 * Method to load Change Forecast Template details from Forecast monitor
	 * Screen
	 * 
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return ActionForward
	 * @throws SwtException
	 */

	public String getTemplateDetails() throws SwtException {
HttpServletRequest request = ServletActionContext.getRequest();
HttpServletResponse response = ServletActionContext.getResponse();


		/* Local variable declaration and initialization */
		// variable to get the user id
		String userId = null;
		// variable to get the host id
		String hostId = null;
		// variable to get the template Id
		String templateId = null;
		// Declare CacheManager
		CacheManager cacheManagerInst = null;
		// ForecastMonitorTemplate object
		ForecastMonitorTemplate forecastMonitorTemplate = null;
		// ForecastMonitorTemplate object
		ForecastMonitorTemplate forecastMonitorTemplateDetail = null;
		// list to hold the template details
		List<ForecastMonitorTemplate> listMonitorTemplateDetailsCol = null;
		try {
			log.debug(this.getClass().getName() + " - [getTemplateDetails] - "
					+ "Entry");
			// create instance for ForecastMonitorTemplate
			forecastMonitorTemplate = new ForecastMonitorTemplate();
			// Get user id from session
			userId = SwtUtil.getCurrentUserId(request.getSession());
			// Get the template id
			templateId = request.getParameter("templateId");
			// create instance
			cacheManagerInst = CacheManager.getInstance();
			// get the hostid
			hostId = cacheManagerInst.getHostId();
			/* Variable Declaration for collMonitorDetails */
			// set the values
			forecastMonitorTemplate.getId().setHostId(hostId);
			forecastMonitorTemplate.getId().setTemplateId(templateId);
			forecastMonitorTemplate.setUserId(userId);
			// Get Template details
			listMonitorTemplateDetailsCol = forecastMonitorTemplateManager
					.getForecastMonitorTemplate(hostId, userId);
			response.getWriter().print("");
			// Condition to chcek  template list is not null
			if (listMonitorTemplateDetailsCol.size() != 0) {
				Iterator<ForecastMonitorTemplate> itr = listMonitorTemplateDetailsCol
						.iterator();
				// loop o get template detail
				while (itr.hasNext()) {
					forecastMonitorTemplateDetail = (ForecastMonitorTemplate) itr
							.next();
					// Condition to get selected template id is in collection
					if (forecastMonitorTemplateDetail.getId().getTemplateId()
							.trim().equals(
									forecastMonitorTemplate.getId()
											.getTemplateId().trim())) {
						//  set user id, template name and public status
						response.getWriter().print(
								forecastMonitorTemplateDetail.getUserId()
										+ "_DELIMETERFORUSER_"
										+ forecastMonitorTemplateDetail
												.getTemplateName()
										+ "_DELIMETER_"
										+ forecastMonitorTemplateDetail
												.getPublicTemplate());
						break;
					}
				}
			}

		} catch (SwtException swtexp) {
			log
					.error(this.getClass().getName()
							+ " - Exception Catched in [getTemplateDetails] method : - "
							+ swtexp.getMessage());
			SwtUtil.logException(swtexp, request, "");
			return ("fail");
		} catch (Exception exp) {
			log
					.error(this.getClass().getName()
							+ " - Exception Catched in [getTemplateDetails] method : - "
							+ exp.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "getTemplateDetails", this.getClass()), request, "");
		} finally {
			listMonitorTemplateDetailsCol = null;
			forecastMonitorTemplateDetail = null;
			forecastMonitorTemplate = null;
			cacheManagerInst = null;
			hostId = null;
			templateId = null;
			userId = null;
			log.debug(this.getClass().getName() + " - [getTemplateDetails] - "
					+ "Exit");
		}
		return null;
	}

	/**
	 * Method to clear template values given in session
	 * 
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return ActionForward
	 * @throws SwtException
	 */

	public String clearSessionInstance() throws SwtException {
HttpServletRequest request = ServletActionContext.getRequest();
HttpServletResponse response = ServletActionContext.getResponse();

		try {
			log.debug(this.getClass().getName()
					+ " - [clearSessionInstance] - "
					+ "to clear template session");
			request.getSession().setAttribute("forecastMonitorTemplate", null);
		} catch (Exception exp) {
			log
					.error(this.getClass().getName()
							+ " - Exception Catched in [clearSessionInstance] method : - "
							+ exp.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "clearSessionInstance", this.getClass()), request, "");
		} finally {
			log.debug(this.getClass().getName()
					+ " - [clearSessionInstance] - " + "Exit");
		}
		return null;
	}

	/**
	 * Method called when click on Add button in Find / Add pop up(Normal)
	 * to add Column sources for normal column type
	 * 
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return ActionForward
	 * @throws SwtException
	 */
	@SuppressWarnings("unchecked")
	public String addColumnSources() throws SwtException {
HttpServletRequest request = ServletActionContext.getRequest();
HttpServletResponse response = ServletActionContext.getResponse();


		/* Local variable declaration and initialization */
		// variable to get the user id
		String userId = null;
		// variable to get the host id
		String hostId = null;
		// variable to get the template Id
		String templateId = null;
		// variable to get ssourceType
		String sourceType = null;
		// variable to get entity
		String entity = null;
		// variable to hold selected ids
		String[] selectedIds = null;
		// variable to hold selected names
		String[] selectedNames = null;
		/* Variable Declaration for dyForm */
		// DynaValidatorForm dyForm = null;
		// ForecastTemplateColSrcList to add new column src
		List<ForecastMonitorTemplateColSrc> forecastMonitorTemplateColSrcList = null;
		// ForecastTemplateColSrcList from session
		List<ForecastMonitorTemplateColSrc> forecastMonitorTemplateColSrcListInSession = null;
		// ForecastMonitorTemplateColSrc bean
		ForecastMonitorTemplateColSrc forecastMonitorTemplateColSrc = null;
		// flag to check column is exist in session
		boolean colExist = false;
		try {
			log.debug(this.getClass().getName() + " - [addColumnSources] - "
					+ "Entry");
			// set the form object into // DynaValidatorForm
			// Read sourceType from request
			sourceType = request.getParameter("sourceType");

			// Read entity from request
			entity = request.getParameter("entity");

			// Get selected id list from action form
			selectedIds = request.getParameter("selectedIds").split(",");

			// Get selected names list from action form
			selectedNames = request.getParameter("selectedNames").split(",");

			// Get user id from session
			userId = request.getParameter("userId");
			// Get the template id
			templateId = request.getParameter("templateId");
			// get the hostid
			hostId = SwtUtil.getCurrentHostId();
			forecastMonitorTemplateColSrcList = new ArrayList<ForecastMonitorTemplateColSrc>();
			if (request.getSession().getAttribute(
					"forecastMonitorTemplateColSrcList") != null) {
				forecastMonitorTemplateColSrcListInSession = (List<ForecastMonitorTemplateColSrc>) request
						.getSession().getAttribute(
								"forecastMonitorTemplateColSrcList");
				for (ForecastMonitorTemplateColSrc forecastTemplateColSrc : forecastMonitorTemplateColSrcListInSession) {
					forecastMonitorTemplateColSrcList
							.add(forecastTemplateColSrc);
				}
			}
			// Loop to get selected id
			for (int counter = 0; counter < selectedIds.length; counter++) {
				colExist = false;
				if (forecastMonitorTemplateColSrcListInSession != null
						&& forecastMonitorTemplateColSrcListInSession.size() > 0) {
					// Loop to check whether the selected column source is exist
					// in screen if so ignor that record
					// Condition modified by Vivekanandan A for issue reported in beta testing on 29-09-2011
					// to chcek source type is not null and is equal to source type in existed
					for (ForecastMonitorTemplateColSrc forecastTemplateColSrc : forecastMonitorTemplateColSrcListInSession) {
						if (((!SwtUtil.isEmptyOrNull(forecastTemplateColSrc
								.getId().getSourceType()) && (forecastTemplateColSrc
								.getId().getSourceType().equals(sourceType))))
								&& ((!SwtUtil
										.isEmptyOrNull(forecastTemplateColSrc
												.getId().getSourceId())) && (forecastTemplateColSrc
										.getId().getSourceId()
										.equals(selectedIds[counter])))) {
							colExist = true;
							break;
						}
					}
				}
				// Condition to check the sources were not exist in screen
				if (!colExist) {
					// Add new selected source to the screen
					forecastMonitorTemplateColSrc = new ForecastMonitorTemplateColSrc();
					// set hostId
					forecastMonitorTemplateColSrc.getId().setHostId(hostId);
					// set templateId
					forecastMonitorTemplateColSrc.getId().setTemplateId(
							templateId);
					// set userId
					forecastMonitorTemplateColSrc.setUserId(userId);
					// set sourceType
					forecastMonitorTemplateColSrc.getId().setSourceType(
							sourceType);
					// set sourceId
					forecastMonitorTemplateColSrc.getId().setSourceId(
							selectedIds[counter]);
					// set entity
					forecastMonitorTemplateColSrc.setEntityId(entity);
					// set multiplier
					forecastMonitorTemplateColSrc.setMultiplier(new BigDecimal(
							1));

					// set templateId
					forecastMonitorTemplateColSrc
							.setName(selectedNames[counter]);
					// set modified values as save
					forecastMonitorTemplateColSrc.setModifiedValue("save");
					// set forecastMonitorTemplateColSrc in list
					forecastMonitorTemplateColSrcList
							.add(forecastMonitorTemplateColSrc);

				}

			}
			// set screen name in request
			request.setAttribute("screenName", "");

			// set templateId in request
			request.setAttribute("templateId", request
					.getParameter("templateId"));
			// set screen name in request
			request.setAttribute("templateName", request
					.getParameter("templateName"));
			// set screen name in request
			request.setAttribute("userId", request.getParameter("userId"));
			// set screen name in request
			request.setAttribute("columnId", request.getParameter("columnId"));
			// set screen name in request
			request.setAttribute("description", request
					.getParameter("description"));
			// set screen name in request
			request
					.setAttribute("shortName", request
							.getParameter("shortName"));

			// set row count in requesr
			request.setAttribute("detailRowCount", request
					.getParameter("detailRowCount"));

			// Set values in request
			request.setAttribute("typeList", new ArrayList<LabelValueBean>());
			request.setAttribute("entityCollection",
					new ArrayList<LabelValueBean>());
			request.setAttribute("entityId", "");
			request.setAttribute("entities", new ArrayList<EntityUserAccess>());
			request.setAttribute("typeId", "");
			// set book list in request
			request.setAttribute("bookDetailsList", new ArrayList<BookCode>());
			// set meta group list in request
			request.setAttribute("metaGroupList", null);
			// set group list in request
			request.setAttribute("groupDetailsList", null);
			// setting reply status as true in request
			request.setAttribute("reply_status_ok", SwtConstants.STR_TRUE);
			// setting the reply message in request
			request.setAttribute("reply_message", SwtConstants.DATA_FETCH_OK);
			request.getSession().setAttribute(
					"forecastMonitorTemplateColSrcList",
					forecastMonitorTemplateColSrcList);
			request.setAttribute("callRefreshDetail", "true");
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [addColumnSources] method : - "
					+ exp.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "addColumnSources", this.getClass()), request, "");
		} finally {
			hostId = null;
			templateId = null;
			userId = null;
			log.debug(this.getClass().getName() + " - [addColumnSources] - "
					+ "Exit");
		}
		return ("loadpopupdata");
	}

	/**
	 * Method to add Column sources for sub ttal or total, in Add / Change a
	 * column in Forecast Template screen
	 * 
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return ActionForward
	 * @throws SwtException
	 */
	@SuppressWarnings("unchecked")
	public String addSubTotalColumnSources() throws SwtException {
HttpServletRequest request = ServletActionContext.getRequest();
HttpServletResponse response = ServletActionContext.getResponse();


		/* Local variable declaration and initialization */
		// variable to get the user id
		String userId = null;
		// variable to get the host id
		String hostId = null;
		// variable to get the template Id
		String templateId = null;
		// variable to get the shortName
		String shortName = null;
		// variable to get the description
		String description = null;
		// variable to hold selected ids
		String[] selectedIds = null;
		// variable to hold selected names
		String[] selectedNames = null;
		// variable to hold selectedColumnNo
		String[] selectedColumnNo = null;
		// variable to hold selectedOrdinalPos
		String[] selectedOrdinalPos = null;
		/* Variable Declaration for dyForm */
		// DynaValidatorForm dyForm = null;
		// Forecast Template bean
		// ForecastMonitorTemplateCol bean
		ForecastMonitorTemplateCol forecastMonitorTemplateCol = null;
		// ForecastMonitorTemplateColSrc bean
		ForecastMonitorTemplateColSrc forecastMonitorTemplateColSrc = null;
		// List to hold forecast monitor template col
		List<ForecastMonitorTemplateColSrc> forecastMonitorTemplateColSrcList = null;
		// ForecastTemplateColSrcList from session
		List<ForecastMonitorTemplateColSrc> forecastMonitorTemplateColSrcListInSession = null;
		// flag to check column is exist in session
		boolean colExist = false;
		try {
			log.debug(this.getClass().getName()
					+ " - [addSubTotalColumnSources] - " + "Entry");
			// set the form object into // DynaValidatorForm
			// Read shortName from request
			shortName = request.getParameter("shortName");

			// Read description from request
			description = request.getParameter("description");
			// Get selected id list from action form
			selectedIds = request.getParameter("selectedIds").split(","); 

			// Get selected names list from action form
			selectedNames = request.getParameter("selectedNames").split(",");

			// Get selected id list from action form
			String selectedColumns = null;
			if(request.getParameter("selectedColumnNo").endsWith(",")) {
				selectedColumns = request.getParameter("selectedColumnNo")+null;
			} else {
				selectedColumns =request.getParameter("selectedColumnNo");
			}
			selectedColumnNo =selectedColumns.split(",") ;
			// Get selected names list from action form
			selectedOrdinalPos = request.getParameter("selectedOrdinalPos").split(",");

			// Get user id from session
			userId = request.getParameter("userId");
			// Get the template id
			templateId = request.getParameter("templateId");
			// get the hostid
			hostId = SwtUtil.getCurrentHostId();

			forecastMonitorTemplateColSrcList = new ArrayList<ForecastMonitorTemplateColSrc>();
			// Condition to chcek column source list in sessionis not null
			if (request.getSession().getAttribute(
					"forecastMonitorTemplateColSrcList") != null) {
				// Obtain forecast column sources list from session
				forecastMonitorTemplateColSrcListInSession = (List<ForecastMonitorTemplateColSrc>) request
						.getSession().getAttribute(
								"forecastMonitorTemplateColSrcList");
				// Loop to get column source list
				for (ForecastMonitorTemplateColSrc forecastTemplateColSrc : forecastMonitorTemplateColSrcListInSession) {
					// add column sources to collection
					forecastMonitorTemplateColSrcList
							.add(forecastTemplateColSrc);
				}
			}
			forecastMonitorTemplateCol = new ForecastMonitorTemplateCol();
			// set hostid
			forecastMonitorTemplateCol.getId().setHostId(hostId);
			// set short name
			forecastMonitorTemplateCol.setColumnDisplayName(shortName);
			// set description
			forecastMonitorTemplateCol.setColumnDescription(description);
			// set column type
			forecastMonitorTemplateCol.setColumnType(SwtConstants.NORMAL_LABEL);
			// set modfied value
			forecastMonitorTemplateCol.setModifiedValue("save");

			// Loop to get selected id
			for (int counter = 0; counter < selectedIds.length; counter++) {
				colExist = false;
				if (forecastMonitorTemplateColSrcListInSession != null
						&& forecastMonitorTemplateColSrcListInSession.size() > 0) {
					// Loop to check whether the selected column source is exist
					// in screen if so ignor that record
					for (ForecastMonitorTemplateColSrc forecastTemplateColSrc : forecastMonitorTemplateColSrcListInSession) {
						if ((!(SwtUtil.isEmptyOrNull(forecastTemplateColSrc
								.getEntityId())) && (forecastTemplateColSrc
								.getEntityId().equals(selectedIds[counter])))) {
							colExist = true;
							break;
						}
					}
				}

				// Condition to check the sources were not exist in screen
				if (!colExist) {
					forecastMonitorTemplateColSrc = new ForecastMonitorTemplateColSrc();
					// set hostId
					forecastMonitorTemplateColSrc.getId().setHostId(hostId);
					// set templateId
					forecastMonitorTemplateColSrc.getId().setTemplateId(
							templateId);
					// set userId
					forecastMonitorTemplateColSrc.setUserId(userId);
					// set sourceType
					forecastMonitorTemplateColSrc.getId().setSourceType(
							SwtConstants.TOTAL_COLUMN_SOURCETYPE);
					// set sourceId
					forecastMonitorTemplateColSrc.getId().setSourceId(
							selectedIds[counter]);
					// set entity
					forecastMonitorTemplateColSrc
							.setEntityId(selectedIds[counter]);
					// set column no
					forecastMonitorTemplateColSrc.getId().setColumnId(
							selectedColumnNo[counter]);

					// set multiplier
					forecastMonitorTemplateColSrc.setMultiplier(new BigDecimal(
							1));
					// set selected ordinal position
					forecastMonitorTemplateColSrc.setOrdinalPos(Integer
							.parseInt(selectedOrdinalPos[counter]));
					// set templateId
					forecastMonitorTemplateColSrc
							.setName(selectedNames[counter]);
					// set forecastMonitorTemplateColSrc in list
					forecastMonitorTemplateColSrcList
							.add(forecastMonitorTemplateColSrc);
					// set modified values as save
					forecastMonitorTemplateColSrc.setModifiedValue("save");
					// add column source list to columns
					forecastMonitorTemplateCol
							.setListForecastMonitorTemplateColSrc(forecastMonitorTemplateColSrcList);
				}

			}
			// set screen name in request
			request.setAttribute("screenName", "");

			// set templateId in request
			request.setAttribute("templateId", request
					.getParameter("templateId"));
			// set screen name in request
			request.setAttribute("templateName", request
					.getParameter("templateName"));
			// set screen name in request
			request.setAttribute("userId", request.getParameter("userId"));
			// set screen name in request
			request.setAttribute("columnId", request.getParameter("columnId"));
			// set screen name in request
			request.setAttribute("description", request
					.getParameter("description"));
			// set screen name in request
			request
					.setAttribute("shortName", request
							.getParameter("shortName"));

			// set row count in requesr
			request.setAttribute("detailRowCount", request
					.getParameter("detailRowCount"));

			// Set values in request
			request.setAttribute("typeList", new ArrayList<LabelValueBean>());
			request.setAttribute("entityCollection",
					new ArrayList<LabelValueBean>());
			request.setAttribute("entityId", "");
			request.setAttribute("entities", new ArrayList<EntityUserAccess>());
			request.setAttribute("typeId", "");
			// set book list in request
			request.setAttribute("listMonitorTemplateDetailsCol",
					new ArrayList<BookCode>());
			// setting reply status as true in request
			request.setAttribute("reply_status_ok", SwtConstants.STR_TRUE);
			// setting the reply message in request
			request.setAttribute("reply_message", SwtConstants.DATA_FETCH_OK);

			request.getSession().setAttribute(
					"forecastMonitorTemplateColSrcList",
					forecastMonitorTemplateColSrcList);
			request.setAttribute("callRefreshDetail", "true");

		} catch (Exception exp) {
			log
					.error(this.getClass().getName()
							+ " - Exception Catched in [addSubTotalColumnSources] method : - "
							+ exp.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "addSubTotalColumnSources", this.getClass()), request,
					"");
		} finally {
			hostId = null;
			templateId = null;
			userId = null;
			log.debug(this.getClass().getName()
					+ " - [addSubTotalColumnSources] - " + "Exit");
		}
		return ("loadsubtotalpopupflexdata");
	}

	/**
	 * Method to delete Column sources from session, when click on delet button
	 * in Add a column to / Change a column in FOrecast monitor Template screen
	 * 
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return ActionForward
	 * @throws SwtException
	 */
	@SuppressWarnings("unchecked")
	public String deleteColumnSrc() throws SwtException {
HttpServletRequest request = ServletActionContext.getRequest();
HttpServletResponse response = ServletActionContext.getResponse();

		// local variable declaration
		// variable to hold hostId
		String hostId = null;
		// ForecastMonitorTemplate instance
		ForecastMonitorTemplate forecastMonitorTemplate = null;
		// String column display name
		String columnName = null;
		// String selectedNormalType
		String selectedNormalType = null;
		// selected id
		String selectedId = null;
		// selected displayname
		String selectedDisplayname = null;
		// Stores the selected column type
		String columnType = null;
		// Stores the selected column description
		// List to hold forecast columns
		List<ForecastMonitorTemplateCol> listForecastColumns = null;
		// List to hold forecast columns
		List<ForecastMonitorTemplateColSrc> listForecastColumnsSrc = null;
		// Array list to hold User Collection
		ArrayList<LabelValueBean> columnTypeList = null;
		// Label Value Bean instance
		LabelValueBean columnTypeLVB = null;
		// flag to check recod is deleted
		boolean removeFlag = false;

		try {
			log.debug(this.getClass().getName()
					+ " - [deleteColumnSrc] - Entry");

			// get hostId from DB
			hostId = SwtUtil.getCurrentHostId();
			// get forecast template from session
			forecastMonitorTemplate = (ForecastMonitorTemplate) request
					.getSession().getAttribute("forecastMonitorTemplate");
			// read columnn name from request
			columnName = request.getParameter("shortName");

			// read selected normal type from request
			selectedNormalType = request.getParameter("type");
			// read the selected column type from request
			columnType = request.getParameter("columnType");
			// read selected normal id from request
			selectedId = request.getParameter("id");
			// read selected Display name for sub total
			selectedDisplayname = request.getParameter("displayname");
			columnTypeList = new ArrayList<LabelValueBean>();
			// Condition to check selected type is normal
			columnTypeLVB = new LabelValueBean(SwtConstants.NORMAL_LABEL,
					SwtConstants.NORMAL);
			columnTypeList.add(columnTypeLVB);
			columnTypeLVB = new LabelValueBean(SwtConstants.SUB_TOTAL_LABEL,
					SwtConstants.SUB_TOTAL);
			columnTypeList.add(columnTypeLVB);
			columnTypeLVB = new LabelValueBean(SwtConstants.TOTAL, SwtConstants.TOTAL);
			columnTypeList.add(columnTypeLVB);

			// Condition to check template is not null
			if (forecastMonitorTemplate != null) {
				// Condition to check list is not null or empty
				if (forecastMonitorTemplate.getForecastTemplateColList() != null
						&& forecastMonitorTemplate.getForecastTemplateColList()
								.size() > 0) {
					// loop to col values
					for (ForecastMonitorTemplateCol forecastMonitorTemplateCol : forecastMonitorTemplate
							.getForecastTemplateColList()) {

						// Condition to check the selected name is in collection
						if (forecastMonitorTemplateCol.getColumnDisplayName()
								.equals(columnName)) {

							// sethost id
							forecastMonitorTemplateCol.getId()
									.setHostId(hostId);
							// read source list from session
							listForecastColumnsSrc = (List<ForecastMonitorTemplateColSrc>) request
									.getSession()
									.getAttribute(
											"forecastMonitorTemplateColSrcList");
							// Condition to check template col src
							if (forecastMonitorTemplateCol
									.getListForecastMonitorTemplateColSrc() != null
									&& forecastMonitorTemplateCol
											.getListForecastMonitorTemplateColSrc()
											.size() > 0
									&& listForecastColumnsSrc != null) {

								// loop to iterate column sources
								for (ForecastMonitorTemplateColSrc forecastMonitorTemplateColSrc : listForecastColumnsSrc) {

									if ((!SwtUtil
											.isEmptyOrNull(forecastMonitorTemplateColSrc
													.getId().getSourceId()) && (forecastMonitorTemplateColSrc.getId()
											.getSourceId().equals(selectedId))  || forecastMonitorTemplateColSrc
											.getEntityId().equals(
													selectedDisplayname))
											&& forecastMonitorTemplateColSrc
													.getId().getSourceType()
													.equals(selectedNormalType)) {

										// set corresponding total multiplier as
										// empty if to total sources were
										// deleted
										if (columnName
												.equalsIgnoreCase(SwtConstants.TOTAL)) {
											// loop to col values
											for (ForecastMonitorTemplateCol forecastTemplateCol : forecastMonitorTemplate
													.getForecastTemplateColList()) {

												if (forecastTemplateCol
														.getColumnDisplayName()
														.equals(
																selectedDisplayname)) {
													forecastTemplateCol
															.setTotalMultiplier("");
												}

											}
										}
										// remove selected sources from delete
										forecastMonitorTemplateCol
												.getListForecastMonitorTemplateColSrc()
												.remove(
														forecastMonitorTemplateColSrc);
										// set host id to sources
										forecastMonitorTemplateColSrc.getId()
												.setHostId(hostId);
										// set template id
										if (!SwtUtil
												.isEmptyOrNull(forecastMonitorTemplateCol
														.getId()
														.getTemplateId())) {
											forecastMonitorTemplateColSrc
													.getId()
													.setTemplateId(
															forecastMonitorTemplateCol
																	.getId()
																	.getTemplateId());
										}
										// Condition to check selected type is
										// book
										if (forecastMonitorTemplateColSrc
												.getId()
												.getSourceType()
												.equals(SwtConstants.BOOK_LABEL))
											forecastMonitorTemplateColSrc
													.getId().setSourceType(
															SwtConstants.BOOK);
										// Condition to check selected type is
										// group
										else if (forecastMonitorTemplateColSrc
												.getId()
												.getSourceType()
												.equals(
														SwtConstants.GROUP_LABEL))
											forecastMonitorTemplateColSrc
													.getId().setSourceType(
															SwtConstants.GROUP);

										// Condition to check selected type is
										// meta group
										else if (forecastMonitorTemplateColSrc
												.getId()
												.getSourceType()
												.equals(
														SwtConstants.META_GROUP_LABEL))
											forecastMonitorTemplateColSrc
													.getId()
													.setSourceType(
															SwtConstants.META_GROUP);
										// Condition to check selected type is
										// entity
										else if (forecastMonitorTemplateColSrc
												.getId()
												.getSourceType()
												.equals(
														SwtConstants.ENTITY_LABEL))
											forecastMonitorTemplateColSrc
													.getId()
													.setSourceType(
															SwtConstants.ENTITY);
										// add removed src in deleted list
										forecastMonitorTemplateCol
												.getDeleteForecastMonitorTemplateColSrcList()
												.add(
														forecastMonitorTemplateColSrc);
										// remove from list
										listForecastColumnsSrc
												.remove(forecastMonitorTemplateColSrc);

										request
												.getSession()
												.setAttribute(
														"forecastMonitorTemplateColSrcList",
														listForecastColumnsSrc);

										// Condition to check selected column
										// type is normal
										if (columnType
												.equalsIgnoreCase(SwtConstants.NORMAL_LABEL)) {

											// Set the column source list in
											// request to repaint the grid
											request
													.setAttribute(
															"listMonitorTemplateDetailsColSrc",
															listForecastColumnsSrc);
										} else {
											// Source values for Subtotal or
											// total to be set here
											listForecastColumns = new ArrayList<ForecastMonitorTemplateCol>();

											// after delete the sources set the
											// other sources by comparing it to
											// the Col bean
											for (ForecastMonitorTemplateCol forecastTemplateColSubTotal : forecastMonitorTemplate
													.getForecastTemplateColList()) {
												// loop to get source values
												for (ForecastMonitorTemplateColSrc forecastTemplateColSrcSubTotal : listForecastColumnsSrc) {
													// Condition to check column
													// display name is equl to
													// the entity id of source
													// bean
													if (forecastTemplateColSrcSubTotal
															.getEntityId()
															.equals(
																	forecastTemplateColSubTotal
																			.getColumnDisplayName())) {
														// set multilpier values
														forecastTemplateColSubTotal
																.setMultiplier(forecastTemplateColSrcSubTotal
																		.getMultiplier());

														if (forecastTemplateColSrcSubTotal
																.getEntityId()
																.equalsIgnoreCase(
																		columnName)) {
														} else {
															listForecastColumns
																	.add(forecastTemplateColSubTotal);
														}
													}

												}

											}
											// Set the column source list in
											// request to repaint the grid
											request
													.setAttribute(
															"listMonitorTemplateDetailsColSrc",
															listForecastColumns);

										}
										removeFlag = true;
										break;
									}
								}
							}
						}
					}
				}
			}

			// Condition to check if records are not in main session
			if (!removeFlag) {
				listForecastColumnsSrc = (List<ForecastMonitorTemplateColSrc>) request
						.getSession().getAttribute(
								"forecastMonitorTemplateColSrcList");
				if (listForecastColumnsSrc != null) {

					for (ForecastMonitorTemplateColSrc forecastMonitorTemplateColSrc : listForecastColumnsSrc) {
						// Condition to check selected id is in collection
						if ((forecastMonitorTemplateColSrc.getId().getSourceId().equals(selectedId) ||(!SwtUtil.isEmptyOrNull(forecastMonitorTemplateColSrc.getId().getColumnId()) &&forecastMonitorTemplateColSrc.getId().getColumnId().equals(selectedId)) || (SwtUtil
								.isEmptyOrNull(forecastMonitorTemplateColSrc
										.getId().getSourceId()) || forecastMonitorTemplateColSrc
								.getEntityId().equals(selectedDisplayname)))
								&& forecastMonitorTemplateColSrc.getId()
										.getSourceType().equals(
												selectedNormalType)) {
							// remove selected source values
							listForecastColumnsSrc
									.remove(forecastMonitorTemplateColSrc);
							// Condition to check selected column type is normal
							if (columnType
									.equalsIgnoreCase(SwtConstants.NORMAL_LABEL)) {

								// Set the column source list in request to
								// repaint the grid
								request.setAttribute(
										"listMonitorTemplateDetailsColSrc",
										listForecastColumnsSrc);
							} else {
								// set column values if the records deleted for
								// sub total
								listForecastColumns = new ArrayList<ForecastMonitorTemplateCol>();

								for (ForecastMonitorTemplateCol forecastTemplateColSubTotal : forecastMonitorTemplate
										.getForecastTemplateColList()) {
									for (ForecastMonitorTemplateColSrc forecastTemplateColSrcSubTotal : listForecastColumnsSrc) {
										// Condition to check entity id is equal
										// to column name
										if (forecastTemplateColSrcSubTotal
												.getEntityId()
												.equals(
														forecastTemplateColSubTotal
																.getColumnDisplayName())) {
											forecastTemplateColSubTotal
													.setMultiplier(forecastTemplateColSrcSubTotal
															.getMultiplier());
											// Condition to chcek entity is
											// equal to column name
											if (forecastTemplateColSrcSubTotal
													.getEntityId()
													.equalsIgnoreCase(
															columnName)) {
											} else {
												listForecastColumns
														.add(forecastTemplateColSubTotal);
											}
										}

									}

								}
								// Set the column source list in
								// request to repaint the grid
								request.setAttribute(
										"listMonitorTemplateDetailsColSrc",
										listForecastColumns);

							}

							break;

						}
					}
				}
			}
			// Set displayName
			request.setAttribute("shortName", columnName);
			// Set description
			request.setAttribute("description", "");
			// Set type as SwtConstants.NORMAL_LABEL
			request.setAttribute("type", SwtConstants.NORMAL_LABEL);
			// Set column id
			request.setAttribute("columnId", "");
			// Set ordinal position
			request.setAttribute("ordinalPos", "");
			// Set total multiplier
			request.setAttribute("totalMultiplier", "");
			// Set column type list
			request.setAttribute("columnTypeList", columnTypeList);

			// setting reply status as true in request
			request.setAttribute("reply_status_ok", SwtConstants.STR_TRUE);
			// setting the reply message in request
			request.setAttribute("reply_message", SwtConstants.DATA_FETCH_OK);
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					+ " - [deleteColumnSrc] - Exception -"
					+ exp.getStackTrace()[0].getClassName() + "."
					+ exp.getStackTrace()[0].getMethodName() + ":"
					+ exp.getStackTrace()[0].getLineNumber() + " "
					+ exp.getMessage());
			request.setAttribute("reply_status_ok", SwtConstants.STR_FALSE);
			request.setAttribute("reply_message", exp.getMessage());
			request.setAttribute("reply_location", exp.getStackTrace()[0]
					.getClassName()
					+ "."
					+ exp.getStackTrace()[0].getMethodName()
					+ ":"
					+ exp.getStackTrace()[0].getLineNumber());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "deleteColumnSrc", this.getClass()), request, "");
		} finally {
			hostId = null;
			columnName = null;
			selectedNormalType = null;
			selectedId = null;
			selectedDisplayname = null;
			columnTypeLVB = null;
			log.debug(this.getClass().getName() + " - [deleteColumnSrc] - "
					+ "Exit");
		}
		if (columnType.equalsIgnoreCase(SwtConstants.NORMAL_LABEL))
			return ("detailaddflex");
		else
			return ("loadsubtotalflexdata");
	}

	/**
	 * Method to delete Column from session and to add the selected column in
	 * deleted list
	 * 
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return ActionForward
	 * @throws SwtException
	 */
	public String deleteForecastColumn() throws SwtException {
HttpServletRequest request = ServletActionContext.getRequest();
HttpServletResponse response = ServletActionContext.getResponse();


		// local variable declaration
		// String vraible to hold hostId
		String hostId = null;
		// ForecastMonitorTemplateColSrc instamce
		ForecastMonitorTemplateColSrc forecastMonitorTemplateColSrc = null;
		// ForecastMonitorTemplate instance
		ForecastMonitorTemplate forecastMonitorTemplate = null;
		// ForecastMonitorTemplateCol instance
		ForecastMonitorTemplateCol forecastMonitorTemplateCol = null;
		// String column display name
		String columnName = null;
		// List to hold forecast columns
		List<ForecastMonitorTemplateCol> listForecastColumns = null;
		// Iterator to iterate forecast columns
		Iterator<ForecastMonitorTemplateCol> itrForecastColumns = null;
		// list of column sources
		List<ForecastMonitorTemplateColSrc> columnSrcColl = null;
		// Array list to hold User Collection
		ArrayList<User> templateUsersList = null;
		// User Instance
		User user = null;
		// UserMaintenance Instance
		UserMaintenance userMaintenance = null;
		// String to hold selectedUserId
		String actualUserId = null;
		// User Dao Hibenate Instance
		UserMaintenanceManager userMaintenanceManagerImpl = null;
		try {
			log.debug(this.getClass().getName()
					+ " - [deleteForecastColumn] - Entry");

			// get hostId from DB
			hostId = SwtUtil.getCurrentHostId();
			// instantiating list
			templateUsersList = new ArrayList<User>();
			// get forecast template from session
			forecastMonitorTemplate = (ForecastMonitorTemplate) request
					.getSession().getAttribute("forecastMonitorTemplate");
			// read columnn name from request
			columnName = request.getParameter("shortName");

			// Condition to check template is not null
			if (forecastMonitorTemplate != null) {
				// get list of columns in session
				listForecastColumns = forecastMonitorTemplate
						.getForecastTemplateColList();
				// Condition to check list is not null or empty
				if (listForecastColumns != null
						&& listForecastColumns.size() > 0) {

					// iterate col list
					itrForecastColumns = listForecastColumns.iterator();
					// loop to col values
					while (itrForecastColumns.hasNext()) {
						// get columns from list
						forecastMonitorTemplateCol = itrForecastColumns.next();

						// Condition to check the selected name is in collection
						if (forecastMonitorTemplateCol.getColumnDisplayName()
								.equals(columnName)) {
							// set source vallues in forecast column sources
							forecastMonitorTemplateColSrc = new ForecastMonitorTemplateColSrc();
							// set host Id
							forecastMonitorTemplateColSrc.getId().setHostId(
									forecastMonitorTemplateCol.getId()
											.getHostId());
							// set template id
							forecastMonitorTemplateColSrc.getId()
									.setTemplateId(
											forecastMonitorTemplateCol.getId()
													.getTemplateId());
							// set user id
							forecastMonitorTemplateColSrc
									.setUserId(forecastMonitorTemplateCol
											.getUserId());
							// set column id
							forecastMonitorTemplateColSrc.getId().setColumnId(
									forecastMonitorTemplateCol.getId()
											.getColumnId());

							// get forecast column sources for selected column
							columnSrcColl = forecastMonitorTemplateManager
									.getForecastMonitorTemplateColSrcForColumnId(
											forecastMonitorTemplateColSrc,
											forecastMonitorTemplateCol
													.getColumnType());

							forecastMonitorTemplateCol
									.setListForecastMonitorTemplateColSrc(columnSrcColl);

							// loop to delete column src
							for (ForecastMonitorTemplateCol forecastTemplateCol : listForecastColumns) {
								if (!forecastTemplateCol.getColumnType()
										.equals(SwtConstants.NORMAL)
										&& !forecastTemplateCol
												.getColumnType()
												.equals(
														SwtConstants.NORMAL_LABEL)) {
									for (ForecastMonitorTemplateColSrc forecastTemplateColSrc : forecastTemplateCol
											.getListForecastMonitorTemplateColSrc()) {
										if (forecastTemplateColSrc
												.getEntityId().equals(
														columnName)) {
											forecastTemplateCol
													.getListForecastMonitorTemplateColSrc()
													.remove(
															forecastTemplateColSrc);
											forecastTemplateCol
													.getDeleteForecastMonitorTemplateColSrcList()
													.add(forecastTemplateColSrc);
											break;
										}
									}

								}
							}

							// add in delete col list
							forecastMonitorTemplate
									.getDeleteForecastMonitorTemplateColList()
									.add(forecastMonitorTemplateCol);

							// remove selected column from session
							listForecastColumns
									.remove(forecastMonitorTemplateCol);
							for (ForecastMonitorTemplateCol updatePosForecastMonitorTemplateCol : listForecastColumns) {
								if (updatePosForecastMonitorTemplateCol
										.getOrdinalPos() < 960
										&& updatePosForecastMonitorTemplateCol
												.getOrdinalPos() > forecastMonitorTemplateCol
												.getOrdinalPos()) {
									updatePosForecastMonitorTemplateCol
											.setOrdinalPos(updatePosForecastMonitorTemplateCol
													.getOrdinalPos() - 1);
								}
							}
							break;
						}

					}

				}
			}
			// Instantiate User bean for default users
			user = new User();
			// set default user id in user bean
			user.getId().setUserId(SwtConstants.DEFAULT);
			// set user name in bean
			user.setUserName(SwtConstants.DEFAULT);
			// Add default user in collection
			templateUsersList.add(user);
			// set color to columns
			listForecastColumns = addDataGridColors(listForecastColumns);
			// set columnsin templates
			forecastMonitorTemplate
					.setForecastTemplateColList(listForecastColumns);
			request.setAttribute("forecastMonitorTemplate",
					forecastMonitorTemplate);
			// Set both list in bean
			request.setAttribute("listMonitorTemplateDetailsCol",
					listForecastColumns);
			request.setAttribute("templateUsersList", templateUsersList);

			// setting reply status as true in request
			request.setAttribute("reply_status_ok", SwtConstants.STR_TRUE);
			// setting the reply message in request
			request.setAttribute("reply_message", SwtConstants.DATA_FETCH_OK);
		} catch (SwtException e) {
			log.error(this.getClass().getName()
					+ " - [deleteForecastColumn] - Exception -"
					+ e.getStackTrace()[0].getClassName() + "."
					+ e.getStackTrace()[0].getMethodName() + ":"
					+ e.getStackTrace()[0].getLineNumber() + " "
					+ e.getMessage());
			request.setAttribute("reply_status_ok", SwtConstants.STR_FALSE);
			request.setAttribute("reply_message", e.getMessage());
			request.setAttribute("reply_location", e.getStackTrace()[0]
					.getClassName()
					+ "."
					+ e.getStackTrace()[0].getMethodName()
					+ ":"
					+ e.getStackTrace()[0].getLineNumber());
			SwtUtil.logException(e, request, "");
			return ("dataerror");

		} catch (Exception exp) {
			log.error(this.getClass().getName()
					+ " - [deleteForecastColumn] - Exception -"
					+ exp.getStackTrace()[0].getClassName() + "."
					+ exp.getStackTrace()[0].getMethodName() + ":"
					+ exp.getStackTrace()[0].getLineNumber() + " "
					+ exp.getMessage());
			request.setAttribute("reply_status_ok", SwtConstants.STR_FALSE);
			request.setAttribute("reply_message", exp.getMessage());
			request.setAttribute("reply_location", exp.getStackTrace()[0]
					.getClassName()
					+ "."
					+ exp.getStackTrace()[0].getMethodName()
					+ ":"
					+ exp.getStackTrace()[0].getLineNumber());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "deleteForecastColumn", this.getClass()), request, "");
		} finally {
			columnName = null;
			hostId = null;
			user = null;
			userMaintenance = null;
			actualUserId = null;
			userMaintenanceManagerImpl = null;
			log.debug(this.getClass().getName()
					+ " - [deleteForecastColumn] - " + "Exit");
		}
		return ("templateadd");
	}

	/**
	 * Method to check if the column already exists, If exists then return true
	 * else return false
	 * 
	 * @param forecastMonitorTemplate
	 * @param columnName
	 * @return booelan
	 */
	private boolean checkDuplicateColumn(
			ForecastMonitorTemplate forecastMonitorTemplate, String columnName) {
		// Flag to indicate the given column is duplicate
		boolean isDuplicate = false;
		try {
			log.debug(this.getClass().getName()
					+ " - [checkDuplicateColumn] - " + "Entry");
			// loop yo iterate column beans
			for (ForecastMonitorTemplateCol forecastMonitorTemplateCol : forecastMonitorTemplate
					.getForecastTemplateColList()) {
				// Condition to chcek selected name exist in collection
				if (forecastMonitorTemplateCol.getColumnDisplayName().equals(
						columnName)) {
					// check duplicate flag to true if exist
					isDuplicate = true;
					break;
				}
			}
		} catch (Exception e) {
			log.error(this.getClass().getName()
					+ " - [checkDuplicateColumn] - Exception -"
					+ "while checking duplicate column");
			return isDuplicate;
		} finally {
			log.debug(this.getClass().getName()
					+ " - [checkDuplicateColumn] - " + "Exit");
		}
		return isDuplicate;
	}
}
