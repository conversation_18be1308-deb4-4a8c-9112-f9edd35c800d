package org.swallow.maintenance.web;

import java.util.*;
import java.util.stream.Collectors;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;





import org.swallow.control.web.AccountAccessAction;
import org.swallow.dao.LogonDAO;
import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.maintenance.model.AccountSpecificSweepFormat;
import org.swallow.maintenance.model.EntityUserAccess;
import org.swallow.maintenance.service.AcctMaintenanceManager;
import org.swallow.maintenance.service.AcctSpecificSweepFormatManager;
import org.swallow.maintenance.service.CorrespondentAcctMaintenanceManager;
import org.swallow.model.MenuItem;
import org.swallow.pcm.maintenance.model.core.OptionInfo;
import org.swallow.pcm.maintenance.model.core.SelectInfo;
import org.swallow.pcm.maintenance.web.ResponseHandler;
import org.swallow.util.xml.SwtResponseConstructor;
import org.swallow.util.xml.SwtXMLWriter;
import org.swallow.util.CommonDataManager;
import org.swallow.util.SwtConstants;
import org.swallow.util.SwtUtil;
import org.swallow.util.SystemInfo;
import org.swallow.util.UserThreadLocalHolder;
import org.swallow.work.web.PreAdviceInputAction;
import org.swallow.util.LabelValueBean;
import org.apache.struts2.convention.annotation.Result;
import org.apache.struts2.convention.annotation.AllowedMethods;
import org.apache.struts2.convention.annotation.Action;
import org.apache.struts2.ServletActionContext;
import org.swallow.util.struts.CustomActionSupport;

@Action(value = "/accountSpecificSweepFormat", results = {
	@Result(name = "accountspecificsweepformatflex", location = "/jsp/maintenance/accountspecificsweepformatflex.jsp"),
	@Result(name = "accountspecificsweepformatflexdata", location = "/jsp/maintenance/accountspecificsweepformatflexdata.jsp"),
	@Result(name = "accountspecificsweepformataddflex", location = "/jsp/maintenance/accountspecificsweepformataddflex.jsp"),
	@Result(name = "accountspecificsweepformataddflexdata", location = "/jsp/maintenance/accountspecificsweepformataddflexdata.jsp"),
	@Result(name = "statechange", location = "/jsp/flexstatechange.jsp"),
	@Result(name = "fail", location = "/error.jsp"),
	@Result(name = "data", location = "/jsp/data.jsp"),
})

@AllowedMethods ({"saveAccountSpecificSweepFormat" ,"checkExistingDataMethod" ,"getSpecificAccounts" ,"saveColumnWidth" ,"saveColumnOrder" ,"getSpecificAccountSweepSize" ,"getUpdatedLists" })
public class AcctSpecificSweepFormatAction extends CustomActionSupport {
public String execute() throws Exception {
    HttpServletRequest request = ServletActionContext.getRequest();

    // List of methods 
    String method = String.valueOf(request.getParameter("method"));
    switch (method) {
        case "saveAccountSpecificSweepFormat":
            return saveAccountSpecificSweepFormat();
        case "checkExistingDataMethod":
            return checkExistingDataMethod();
        case "getSpecificAccounts":
            return getSpecificAccounts();
        case "saveColumnWidth":
            return saveColumnWidth();
        case "saveColumnOrder":
            return saveColumnOrder();
        case "getSpecificAccountSweepSize":
            return getSpecificAccountSweepSize();
        case "getUpdatedLists":
            return getUpdatedLists();
        case "displayAccountSpecificSweepFormatList":
            return displayAccountSpecificSweepFormatList();
        case "displayAccountSpecificSweepFormat":
        	return displayAccountSpecificSweepFormat();
		case "deleteAccountSpecificSweepFormat":
			return deleteAccountSpecificSweepFormat();
		case "clearSpecificAccountSweepFormatFromSession":
			return clearSpecificAccountSweepFormatFromSession();

        default:
            break;
    }

    return getSpecificAccounts();
}




	private final Log log = LogFactory
			.getLog(AcctSpecificSweepFormatAction.class);
	private AcctSpecificSweepFormatManager acctSpecificSweepFormatManager;

	public void setAcctSpecificSweepFormatManager(
			AcctSpecificSweepFormatManager acctSpecificSweepFormatManager) {
		this.acctSpecificSweepFormatManager = acctSpecificSweepFormatManager;
	}
	/**
	 * Initializing menuItemId.
	 */
	private final String menuItemId = ""+SwtConstants.SCREEN_ACCOUNT_SPECIFIC_MAINTENANCE ;

	/**
	 * Display the selected specific format details
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return
	 * @throws SwtException
	 */
	public String displayAccountSpecificSweepFormat() throws SwtException {
		
		AccountSpecificSweepFormat accountSpecificSweepFormat = null;
		AccountSpecificSweepFormat tempSpecificAccount = null;
		String screen = null;
		String hostId = null;
		String entityId = null;
		String accountId = null;
		String currencyId = null;
		String parentMethodName = null;
		String id = null;
		String specifiedAccountId = null;
		String specifiedEntityId = null;
		ArrayList<LabelValueBean> currencyList = null;
		AccountSpecificSweepFormat specificAccountFromRequest = null;
		
		Collection<AccountSpecificSweepFormat>  specificFormatList = null;
		HashMap<String, Collection<AccountSpecificSweepFormat>> allStoredSpecificInrequest = null;
		HttpServletRequest request = ServletActionContext.getRequest();
		HttpServletResponse response = ServletActionContext.getResponse();
		try {
			log.debug(this.getClass().getName()
					+ " method [displayAccountSpecificSweepFormat] - Enter ");
			request.setAttribute("screenName",
					request.getParameter("screenName"));
			hostId = SwtUtil.getCurrentHostId();
			entityId = request.getParameter("entityId");
			accountId = request.getParameter("accountId");
			currencyId = request.getParameter("currencyId");
			id = request.getParameter("id");
			specifiedAccountId = request.getParameter("specifiedAccountId");
			specifiedEntityId = request.getParameter("specifiedEntityId");
			screen = request.getParameter("screenName");
			parentMethodName = request.getParameter("parentMethodName");
			tempSpecificAccount = new AccountSpecificSweepFormat();
			
			if (SwtUtil.isEmptyOrNull(request.getParameter("loadFlex"))) {
				log.debug(this.getClass().getName()
						+ "-[displayAccountSpecificSweepFormat] - Exit loading SWF");
				request.setAttribute("hostId", hostId);
				request.setAttribute("entityId", entityId);
				request.setAttribute("accountId", accountId);
				request.setAttribute("currencyId", currencyId);
				request.setAttribute("specifiedAccountId", specifiedAccountId);
				request.setAttribute("specifiedEntityId", specifiedEntityId);

				request.setAttribute("screenName", screen);
				request.setAttribute("parentMethodName", parentMethodName);
				request.setAttribute("id", id);
				return ("accountspecificsweepformataddflex");
			}
			
			specificAccountFromRequest = new AccountSpecificSweepFormat();
			specificAccountFromRequest.getId().setAccountId(accountId);
			specificAccountFromRequest.getId().setEntityId(entityId);
			specificAccountFromRequest.getId().setHostId(hostId);
			specificAccountFromRequest.getId().setSpecifiedAccountId(specifiedAccountId);
			specificAccountFromRequest.getId().setSpecifiedEntityId(specifiedEntityId);
			
			if(screen.equals("addScreen")) {
				accountSpecificSweepFormat = new AccountSpecificSweepFormat();
			}else {
				if("add".equals(parentMethodName)) {
					allStoredSpecificInrequest = (HashMap<String, Collection<AccountSpecificSweepFormat>>) request.getSession()
							.getAttribute("acctSpecificFormatcollInSession"+"*.*");
				}else {
					allStoredSpecificInrequest = (HashMap<String, Collection<AccountSpecificSweepFormat>>) request.getSession()
							.getAttribute("acctSpecificFormatcollInSession"+accountId);
				}
				if(allStoredSpecificInrequest == null)
					allStoredSpecificInrequest = new HashMap<String, Collection<AccountSpecificSweepFormat>>();
				
				
				if(allStoredSpecificInrequest.containsKey(id))  {
					specificFormatList =  allStoredSpecificInrequest.get(id);
					

					for (Iterator<AccountSpecificSweepFormat> iterator = specificFormatList.iterator(); iterator.hasNext();) { 
						accountSpecificSweepFormat = iterator.next(); 
						if(specificAccountFromRequest.compareTo(accountSpecificSweepFormat)) {
							break;
						}
							
					}
				
				}
				
			}
			if(accountSpecificSweepFormat == null)
				accountSpecificSweepFormat = new AccountSpecificSweepFormat();
			
			request.setAttribute("accountSpecificSweepFormat",
					accountSpecificSweepFormat);
			request.setAttribute("entityId", entityId);
			request.setAttribute("accountId", accountId);
			request.setAttribute("currencyId", currencyId);
			request.setAttribute("id", id);
			request.setAttribute("parentMethodName", parentMethodName);
			request.setAttribute("specificAccountId", accountSpecificSweepFormat.getId().getSpecifiedAccountId());
			request.setAttribute("specificEntityId", accountSpecificSweepFormat.getId().getSpecifiedEntityId());
			putEntityListInReq(request, false);
			putAccountListInRequest(request, entityId, currencyId, false);	
			putMessageFormatListInRequest(request, entityId);
			if("addScreen".equalsIgnoreCase(screen)) {
			putSpecificAccountListInRequest(request, entityId, currencyId, false);
			}else {
			putSpecificAccountListInRequest(request, specifiedEntityId, currencyId, false);
			}
			currencyList = (ArrayList) getCurrencyList(request, entityId, false);
			request.setAttribute("currencies", currencyList);			
			request.setAttribute("menuAccessId",
					request.getParameter("menuAccessId"));
			request.setAttribute("reply_status_ok", SwtConstants.STR_TRUE);
			request.setAttribute("reply_message", "Data fetch OK");
			log.debug(this.getClass().getName()
					+ " method [displayAccountSpecificSweepFormat] - Exit ");
		} catch (SwtException e) {
			log.error(this.getClass().getName()
					+ " - [displayAccountSpecificSweepFormat] - Exception -"
					+ e.getStackTrace()[0].getClassName() + "."
					+ e.getStackTrace()[0].getMethodName() + ":"
					+ e.getStackTrace()[0].getLineNumber() + " "
					+ e.getMessage());
			request.setAttribute("reply_status_ok", SwtConstants.STR_FALSE);
			request.setAttribute("reply_message", e.getMessage());
			request.setAttribute(
					"reply_location",
					e.getStackTrace()[0].getClassName() + "."
							+ e.getStackTrace()[0].getMethodName() + ":"
							+ e.getStackTrace()[0].getLineNumber());
			return ("dataerror");
		}
		return ("accountspecificsweepformataddflexdata");
	}
	
	
	/**
	 * Display the list of specific format accounts  
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return
	 * @throws SwtException
	 */
	public String displayAccountSpecificSweepFormatList() throws SwtException {
		log.debug(this.getClass().getName()
				+ " method [displayAccountSpecificSweepFormatList] - Enter ");
		Collection<AccountSpecificSweepFormat> result = null;
		ArrayList<LabelValueBean> currencyList = null;
		String hostId = null;
		String entityId = null;
		String accountId = null;
		String currencyId = null;
		String parentMethodName = null;
		String id = null;
		AccountSpecificSweepFormat accountSpecific = null;
		
		MenuItem menuItem = null;
		String menuAccessId =  "";
		LabelValueBean labelValueBean = null;
		LogonDAO logonDAO = null;
		Collection<AccountSpecificSweepFormat>  specificFormatList = null;
		HashMap<String, Collection<AccountSpecificSweepFormat>> allStoredSpecificInrequest = null;
		HttpServletRequest request = ServletActionContext.getRequest();
		HttpServletResponse response = ServletActionContext.getResponse();
		try {
			CorrespondentAcctMaintenanceManager correspondentAcctMaintenanceManager = (CorrespondentAcctMaintenanceManager) SwtUtil
					.getBean("correspondentAcctMaintenanceManager");

			hostId = SwtUtil.getCurrentHostId();
			entityId = request.getParameter("entityId");
			accountId = request.getParameter("accountId");
			currencyId = request.getParameter("currencyId");
			parentMethodName = request.getParameter("parentMethodName");
			
			id = request.getParameter("id");
			
			if("add".equals(parentMethodName)) {
				allStoredSpecificInrequest = (HashMap<String, Collection<AccountSpecificSweepFormat>>) request.getSession()
						.getAttribute("acctSpecificFormatcollInSession"+"*.*");
			}else {
				allStoredSpecificInrequest = (HashMap<String, Collection<AccountSpecificSweepFormat>>) request.getSession()
						.getAttribute("acctSpecificFormatcollInSession"+accountId);
			}
			
			if(allStoredSpecificInrequest == null)
				allStoredSpecificInrequest = new HashMap<String, Collection<AccountSpecificSweepFormat>>();
			
			
			if(allStoredSpecificInrequest.containsKey(id))  {
				result = new LinkedList<AccountSpecificSweepFormat>();
				specificFormatList =  allStoredSpecificInrequest.get(id);
				for (Iterator<AccountSpecificSweepFormat> iterator = specificFormatList.iterator(); iterator.hasNext();) { 
					accountSpecific = iterator.next(); 
					
					
					if(accountSpecific.getChanged()== null || (accountSpecific.getChanged() != null && !accountSpecific.getChanged().equals("D")))
						result.add(accountSpecific);
				}
			}else  {
					specificFormatList = new  ArrayList<AccountSpecificSweepFormat>();
			
					specificFormatList = new ArrayList<AccountSpecificSweepFormat>();
					
					result = acctSpecificSweepFormatManager
							.getAccountSpecificSweepFormatList(hostId, entityId, accountId);

					for (Iterator<AccountSpecificSweepFormat> iterator = result.iterator(); iterator.hasNext();) { 
						accountSpecific = iterator.next(); 
						String accountName= acctSpecificSweepFormatManager.getSpecificAccountName(accountSpecific.getId().getSpecifiedAccountId());
								accountSpecific.setAccountName(accountName);					
					}
					
						
					allStoredSpecificInrequest.put(id, result);
					if("add".equals(parentMethodName)) {
						request.getSession().setAttribute("acctSpecificFormatcollInSession"+"*.*", allStoredSpecificInrequest);
					}else {
						request.getSession().setAttribute("acctSpecificFormatcollInSession"+accountId, allStoredSpecificInrequest);
					}
	
				}
				
				CommonDataManager commonDataManager = null;
				
				hostId = SwtUtil.getCurrentHostId();
				logonDAO = (LogonDAO)SwtUtil.getBean("logonDAO");
				commonDataManager = (CommonDataManager) request.getSession()
						.getAttribute(SwtConstants.CDM_BEAN);
				// The menu item to the relevant match
				menuItem = logonDAO.getMenuItem(SwtConstants.MENU_ITEM_ACCOUNT_MAINTENANCE+"", commonDataManager.getUser());
				// Change the menu access id of the screen if the menu access to the relevant match is not full access
				if (menuItem != null) {
					menuAccessId = ""+SwtUtil.getHierarchicalAccessId(menuItem, request);
				}else 
					menuAccessId = "2";
				
				
				
				
				
				bindColumnOrderInRequest(request);
				bindColumnWidthInRequest(request);
				request.setAttribute("reply_status_ok", SwtConstants.STR_TRUE);
				request.setAttribute("reply_message", "Data fetch OK");
				putEntityListInReq(request, false);
				request.setAttribute("accountSpecificSweepFormatList", result);
				currencyList = (ArrayList) getCurrencyList(request, entityId, false);
				
				request.setAttribute("currencies", currencyList);
				putAccountListInRequest(request, entityId, currencyId, true);
				request.setAttribute("entityId", entityId);
				request.setAttribute("currencyId", currencyId);
				request.setAttribute("accountId", accountId);
				request.setAttribute("menuAccessId", menuAccessId);
				request.setAttribute("id", id);
				
				request.setAttribute("recordCount", result.size());
				
				log.debug(this.getClass().getName()
						+ " method [displayAccountSpecificSweepFormatList] - Exit ");
		} catch (SwtException e) {
			log.error(this.getClass().getName()
					+ " - [displayAccountSpecificSweepFormatList] - Exception -"
					+ e.getStackTrace()[0].getClassName() + "."
					+ e.getStackTrace()[0].getMethodName() + ":"
					+ e.getStackTrace()[0].getLineNumber() + " "
					+ e.getMessage());
			request.setAttribute("reply_status_ok", SwtConstants.STR_FALSE);
			request.setAttribute("reply_message", e.getMessage());
			request.setAttribute(
					"reply_location",
					e.getStackTrace()[0].getClassName() + "."
							+ e.getStackTrace()[0].getMethodName() + ":"
							+ e.getStackTrace()[0].getLineNumber());
			return ("dataerror");
		}
		return ("accountspecificsweepformatflexdata");
	}

	/**
	 * this method is used to save details of a account specific format details 
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return
	 * @throws SwtException
	 */
	public String saveAccountSpecificSweepFormat() throws SwtException {
HttpServletRequest request = ServletActionContext.getRequest();
HttpServletResponse response = ServletActionContext.getResponse();

		AccountSpecificSweepFormat accountSpecificSweepFormat = null;
		AccountSpecificSweepFormat accountSpecificSweepFormatTmp = null;
		AccountSpecificSweepFormat specificAccountFromRequest = null;
		String screen = null;
		String hostId = null;
		String entityId = null;
		String accountId = null;
		String specifiedAccountId = null;
		String specifiedAccountName = null;
		String specifiedEntityId = null;
		String newInternalCrFormat = null;
		String newInternalDrFormat = null;
		String newExternalCrFormat = null;
		String newExternalDrFormat = null;
		String newExternalCrFormatInt = null;
		String newExternalDrFormatINt = null;
		String changedFlag = "I";
		String parentMethodName = null;
		String id = null;
		Collection<AccountSpecificSweepFormat>  specificFormatList = null;
		HashMap<String, Collection<AccountSpecificSweepFormat>> allStoredSpecificInrequest = null;
		try {
			log.debug(this.getClass().getName()
					+ " method [saveAccountSpecificSweepFormat] - Enter ");
			hostId = SwtUtil.getCurrentHostId();
			entityId = request.getParameter("entityId");
			accountId = request.getParameter("accountId");
			specifiedEntityId = request.getParameter("specifiedEntityId");
			specifiedAccountId = request.getParameter("specifiedAccountId");
			specifiedAccountName = request.getParameter("specifiedAccountName");
			newInternalCrFormat = request.getParameter("newInternalCrFormat");
			newInternalDrFormat = request.getParameter("newInternalDrFormat");
			newExternalCrFormat = request.getParameter("newExternalCrFormat");
			newExternalDrFormat = request.getParameter("newExternalDrFormat");
			newExternalCrFormatInt = request.getParameter("newExternalCrFormatInt");
			newExternalDrFormatINt = request.getParameter("newExternalDrFormatINt");
			parentMethodName = request.getParameter("parentMethodName");
			id = request.getParameter("id");
			accountSpecificSweepFormat = new AccountSpecificSweepFormat();
			accountSpecificSweepFormat.getId().setHostId(hostId);
			accountSpecificSweepFormat.getId().setEntityId(entityId);
			accountSpecificSweepFormat.getId().setAccountId(accountId);
			accountSpecificSweepFormat.getId().setSpecifiedAccountId(specifiedAccountId);
			accountSpecificSweepFormat.getId().setSpecifiedEntityId(specifiedEntityId);
			accountSpecificSweepFormat.setNewInternalCrFormat(newInternalCrFormat);
			accountSpecificSweepFormat.setNewInternalDrFormat(newInternalDrFormat);
			accountSpecificSweepFormat.setNewExternalCrFormat(newExternalCrFormat);
			accountSpecificSweepFormat.setNewExternalDrFormat(newExternalDrFormat);
			accountSpecificSweepFormat.setNewExternalCrFormatInt(newExternalCrFormatInt);
			accountSpecificSweepFormat.setNewExternalDrFormatINt(newExternalDrFormatINt);
			accountSpecificSweepFormat.setAccountName(specifiedAccountName);
			screen = request.getParameter("screenName");
			
			
			if("add".equals(parentMethodName)) {
				allStoredSpecificInrequest = (HashMap<String, Collection<AccountSpecificSweepFormat>>) request.getSession()
						.getAttribute("acctSpecificFormatcollInSession"+"*.*");
			}else {
				allStoredSpecificInrequest = (HashMap<String, Collection<AccountSpecificSweepFormat>>) request.getSession()
						.getAttribute("acctSpecificFormatcollInSession"+accountId);
			}
			
			specificAccountFromRequest = new AccountSpecificSweepFormat();
			specificAccountFromRequest.getId().setAccountId(accountId);
			specificAccountFromRequest.getId().setEntityId(entityId);
			specificAccountFromRequest.getId().setHostId(hostId);
			specificAccountFromRequest.getId().setSpecifiedEntityId(specifiedEntityId);
			specificAccountFromRequest.getId().setSpecifiedAccountId(specifiedAccountId);

			if(allStoredSpecificInrequest == null)
				allStoredSpecificInrequest = new HashMap<String, Collection<AccountSpecificSweepFormat>>();
			
			
			if(allStoredSpecificInrequest.containsKey(id))  {
				specificFormatList =  allStoredSpecificInrequest.get(id);
				
				
				if ("addScreen".equalsIgnoreCase(screen)) {
					
					for (Iterator<AccountSpecificSweepFormat> iterator = specificFormatList.iterator(); iterator.hasNext();) { 
						accountSpecificSweepFormatTmp = iterator.next(); 
						if(specificAccountFromRequest.compareTo(accountSpecificSweepFormatTmp) && (accountSpecificSweepFormatTmp.getChanged() == null || (accountSpecificSweepFormatTmp.getChanged() != null && !accountSpecificSweepFormatTmp.getChanged().equals("D")))) {
							
							if(!("I").equals(accountSpecificSweepFormatTmp.getChanged())) {
								changedFlag = "C";
							}
							iterator.remove();
						}
					}
					
					accountSpecificSweepFormat.setChanged(changedFlag);
					specificFormatList.add(accountSpecificSweepFormat);
					
				}
				else {
					for (Iterator<AccountSpecificSweepFormat> iterator = specificFormatList.iterator(); iterator.hasNext();) { 
						accountSpecificSweepFormatTmp = iterator.next(); 
						
						if(specificAccountFromRequest.compareTo(accountSpecificSweepFormatTmp)) {
								
							if(accountSpecificSweepFormatTmp.getChanged() == null || (accountSpecificSweepFormatTmp.getChanged() !=null && !accountSpecificSweepFormatTmp.getChanged().equals("I")  ))
								accountSpecificSweepFormat.setChanged("C");
							else 
								accountSpecificSweepFormat.setChanged("I");
							
								iterator.remove();
								specificFormatList.add(accountSpecificSweepFormat);
								break;
							
						}
					}
				}
			}
			
			allStoredSpecificInrequest.put(id, specificFormatList);
			if("add".equals(parentMethodName)) {
				request.getSession().setAttribute("acctSpecificFormatcollInSession"+"*.*", allStoredSpecificInrequest);
			}else {
				request.getSession().setAttribute("acctSpecificFormatcollInSession"+accountId, allStoredSpecificInrequest);
			}
			
			request.setAttribute("menuAccessId",
					request.getParameter("menuAccessId"));
			request.setAttribute("reply_status_ok", SwtConstants.STR_TRUE);
			request.setAttribute("reply_message", "Data fetch OK");
			log.debug(this.getClass().getName()
					+ " method [saveAccountSpecificSweepFormat] - Exit ");
		} catch (Exception e) {
			e.printStackTrace();
			log.error(this.getClass().getName()
					+ " - [saveAccountSpecificSweepFormat] - Exception -"
					+ e.getStackTrace()[0].getClassName() + "."
					+ e.getStackTrace()[0].getMethodName() + ":"
					+ e.getStackTrace()[0].getLineNumber() + " "
					+ e.getMessage());
			request.setAttribute("reply_status_ok", SwtConstants.STR_FALSE);
			request.setAttribute("reply_message", e.getMessage());
			request.setAttribute(
					"reply_location",
					e.getStackTrace()[0].getClassName() + "."
							+ e.getStackTrace()[0].getMethodName() + ":"
							+ e.getStackTrace()[0].getLineNumber());
			return ("fail");
		}
		return ("statechange");
	}

	/**
	 * This method is used to delete account specific format
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return
	 * @throws SwtException
	 */
	public String deleteAccountSpecificSweepFormat() throws SwtException {
		AccountSpecificSweepFormat accountSpecificSweepFormat = null;
		String hostId = null;
		String entityId = null;
		String accountId = null;
		String specifiedAccountId = null;
		String specifiedEntityId = null;
		String id = null;
		String parentMethodName = null;
		AccountSpecificSweepFormat specificAccountFromRequest = null;
		Collection<AccountSpecificSweepFormat>  specificFormatList = null;
		HashMap<String, Collection<AccountSpecificSweepFormat>> allStoredSpecificInrequest = null;
		HttpServletRequest request = ServletActionContext.getRequest();
		HttpServletResponse response = ServletActionContext.getResponse();
		try {
			log.debug(this.getClass().getName()
					+ " method [deleteAccountSpecificSweepFormat] - Enter ");
			hostId = SwtUtil.getCurrentHostId();
			entityId = request.getParameter("entityId");
			accountId = request.getParameter("accountId");
			parentMethodName = request.getParameter("parentMethodName");

			id = request.getParameter("id");
			specifiedAccountId = request.getParameter("specifiedAccountId");
			specifiedEntityId = request.getParameter("specifiedEntityId");

			specificAccountFromRequest = new AccountSpecificSweepFormat();
			specificAccountFromRequest.getId().setAccountId(accountId);
			specificAccountFromRequest.getId().setEntityId(entityId);
			specificAccountFromRequest.getId().setHostId(hostId);
			specificAccountFromRequest.getId().setSpecifiedAccountId(specifiedAccountId);
			specificAccountFromRequest.getId().setSpecifiedEntityId(specifiedEntityId);

			if ("add".equals(parentMethodName)) {
				allStoredSpecificInrequest = (HashMap<String, Collection<AccountSpecificSweepFormat>>) request.getSession()
						.getAttribute("acctSpecificFormatcollInSession" + "*.*");
			} else {
				allStoredSpecificInrequest = (HashMap<String, Collection<AccountSpecificSweepFormat>>) request.getSession()
						.getAttribute("acctSpecificFormatcollInSession" + accountId);
			}

			if (allStoredSpecificInrequest == null) {
				allStoredSpecificInrequest = new HashMap<>();
			}

			if (allStoredSpecificInrequest.containsKey(id)) {
				specificFormatList = allStoredSpecificInrequest.get(id);

				for (Iterator<AccountSpecificSweepFormat> iterator = specificFormatList.iterator(); iterator.hasNext();) {
					accountSpecificSweepFormat = iterator.next();

					if (specificAccountFromRequest.compareTo(accountSpecificSweepFormat)) { // ✅ Now includes specifiedEntityId

						if (accountSpecificSweepFormat.getChanged() != null && accountSpecificSweepFormat.getChanged().equals("I")) {
							iterator.remove();
						} else {
							accountSpecificSweepFormat.setChanged("D");
						}

						break;
					}
				}
			}

			allStoredSpecificInrequest.put(id, specificFormatList);

			if ("add".equals(parentMethodName)) {
				request.getSession().setAttribute("acctSpecificFormatcollInSession" + "*.*", allStoredSpecificInrequest);
			} else {
				request.getSession().setAttribute("acctSpecificFormatcollInSession" + accountId, allStoredSpecificInrequest);
			}

			displayAccountSpecificSweepFormatList();

			log.debug(this.getClass().getName()
					+ " method [deleteAccountSpecificSweepFormat] - Exit ");
		} catch (SwtException e) {
			log.error(this.getClass().getName()
					+ " - [deleteAccountSpecificSweepFormat] - Exception -"
					+ e.getStackTrace()[0].getClassName() + "."
					+ e.getStackTrace()[0].getMethodName() + ":"
					+ e.getStackTrace()[0].getLineNumber() + " "
					+ e.getMessage());
			request.setAttribute("reply_status_ok", SwtConstants.STR_FALSE);
			request.setAttribute("reply_message", e.getMessage());
			request.setAttribute(
					"reply_location",
					e.getStackTrace()[0].getClassName() + "."
							+ e.getStackTrace()[0].getMethodName() + ":"
							+ e.getStackTrace()[0].getLineNumber());
			return ("dataerror");
		}
		return ("accountspecificsweepformatflexdata");
	}

	/**
	 * This method check if  whatever the sent account format  it exist before saving it
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return
	 * @throws SwtException
	 */
	public String checkExistingDataMethod() throws SwtException {
HttpServletRequest request = ServletActionContext.getRequest();
HttpServletResponse response = ServletActionContext.getResponse();

		
		AccountSpecificSweepFormat accountSpecificSweepFormat = null;
		AccountSpecificSweepFormat specificAccountFromRequest = null;
		String hostId = null;
		String entityId = null;
		String accountId = null;
		String specifiedAccountId = null;
		String parentMethodName = null;
		String existingData = "N";
		Collection<AccountSpecificSweepFormat>  specificFormatList = null;
		HashMap<String, Collection<AccountSpecificSweepFormat>> allStoredSpecificInrequest = null;
		String id = null;
		
		try {
			log.debug(this.getClass().getName()
					+ "- [checkExistingDataMethod] - Enter");
			hostId = SwtUtil.getCurrentHostId();
			entityId = request.getParameter("entityId");
			accountId = request.getParameter("accountId");
			parentMethodName = request.getParameter("parentMethodName");
			specifiedAccountId = request.getParameter("specifiedAccountId");
			id = request.getParameter("id");
			
			if("add".equals(parentMethodName)) {
				allStoredSpecificInrequest = (HashMap<String, Collection<AccountSpecificSweepFormat>>) request.getSession()
						.getAttribute("acctSpecificFormatcollInSession"+"*.*");
				
			}else {
				allStoredSpecificInrequest = (HashMap<String, Collection<AccountSpecificSweepFormat>>) request.getSession()
						.getAttribute("acctSpecificFormatcollInSession"+accountId);
			}
			
			
			specificAccountFromRequest = new AccountSpecificSweepFormat();
			specificAccountFromRequest.getId().setAccountId(accountId);
			specificAccountFromRequest.getId().setEntityId(entityId);
			specificAccountFromRequest.getId().setHostId(hostId);
			specificAccountFromRequest.getId().setSpecifiedAccountId(specifiedAccountId);
			
			if(allStoredSpecificInrequest == null)
				allStoredSpecificInrequest = new HashMap<String, Collection<AccountSpecificSweepFormat>>();
			
			if (allStoredSpecificInrequest.containsKey(id)) {
				specificFormatList = allStoredSpecificInrequest.get(id);

				for (Iterator<AccountSpecificSweepFormat> iterator = specificFormatList.iterator(); iterator
						.hasNext();) {
					accountSpecificSweepFormat = iterator.next();
					if (accountSpecificSweepFormat.compareTo(specificAccountFromRequest)) {

						if (!"D".equals(accountSpecificSweepFormat.getChanged())) {
							existingData = "Y";
						}

					}

				}
			}
			
			
			response.getWriter().print(existingData);
			log.debug(this.getClass().getName()
					+ " - [checkExistingDataMethod] - " + "Exit");
			return null;
		} catch (Exception exp) {
			exp.printStackTrace();
			log.error(this.getClass().getName()
					+ " - Exception Catched in [checkExistingDataMethod] method : - "
					+ exp.getMessage());
			SwtUtil.logException(
					SwtErrorHandler.getInstance().handleException(exp,
							"checkExistingDataMethod",
							AcctSpecificSweepFormatAction.class),
					request, "");
			return ("fail");
		}
	}
	
	/**
	 * this method open specific format screen 
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return
	 * @throws SwtException
	 */
	public String getSpecificAccounts()
			throws SwtException {
HttpServletRequest request = ServletActionContext.getRequest();
HttpServletResponse response = ServletActionContext.getResponse();

		
		String entityId = null;
		String currencyId = null;
		String accountId = null;
		String methodName = null;
		String parentMethodName = null;
		String id = null;

		//get passed params
		entityId = request.getParameter("entityId");
		accountId = request.getParameter("accountId");
		currencyId = request.getParameter("currencyId");
		methodName = request.getParameter("methodName");
		parentMethodName = request.getParameter("parentMethodName");
		id = request.getParameter("id");
		
		
		if(SwtUtil.isEmptyOrNull(entityId))
			entityId = SwtUtil.getUserCurrentEntity(UserThreadLocalHolder
						.getUserSession());
		if(SwtUtil.isEmptyOrNull(currencyId))
			currencyId = SwtUtil.getDomesticCurrencyForUser(request,
						SwtUtil.getCurrentHostId(), entityId);
		//send in request the passed params
		request.setAttribute("entityId", entityId);
		request.setAttribute("currencyId", currencyId);
		request.setAttribute("parentMethodName", parentMethodName);
		request.setAttribute("menuAccessId", request
				.getParameter("menuAccessId"));
		request.setAttribute("accountId", accountId);
		request.setAttribute("methodName", methodName);
		request.setAttribute("method", "getSpecificAccounts");
		request.setAttribute("id", id); 
		return ("accountspecificsweepformatflex");
	}
	
	/**
	 * This method add column order to request
	 * @param request
	 * @throws SwtException
	 */
	private void bindColumnOrderInRequest(HttpServletRequest request)
			throws SwtException {
		String columnOrder = null;
		ArrayList<String> alColumnOrder = null;
		String[] props = null;
		try {
			log.debug(this.getClass().getName()
					+ " - [ bindColumnOrderInRequest ] - Entry ");
			columnOrder = SwtUtil.getPropertyValue(request, menuItemId, "display",
					"column_order");
			if (SwtUtil.isEmptyOrNull(columnOrder)) {
				columnOrder = "specifiedEntityId,specifiedAccountId,accountName,newInternalCrFormat,newInternalDrFormat,newExternalCrFormat,newExternalDrFormat,newExternalCrFormatInt,newExternalDrFormatINt";
			}
			props = columnOrder.split(",");
			alColumnOrder = new ArrayList<String>(props.length);
			for (String prop : props) {
				alColumnOrder.add(prop);
			}
			request.setAttribute("column_order", alColumnOrder);
		} catch (Exception ex) {
			log.error(this.getClass().getName()
					+ " - [bindColumnOrderInRequest] - Exception - "
					+ ex.getMessage());
			throw SwtErrorHandler.getInstance().handleException(ex,
					"bindColumnOrderInRequest",
					AcctSpecificSweepFormatAction.class);
		} finally {
			columnOrder = null;
			alColumnOrder = null;
			props = null;
			log.debug(this.getClass().getName()
					+ " - [ bindColumnOrderInRequest ] - Exit ");
		}
	}

	/**
	 * This method add column width in request
	 * @param request
	 * @throws SwtException
	 */
	private void bindColumnWidthInRequest(HttpServletRequest request)
			throws SwtException {
		String width = null;
		HashMap<String, String> widths = null;
		String[] props = null;
		try {
			log.debug(this.getClass().getName()
					+ " - [bindColumnWidthInRequest] - Enter");
			width = SwtUtil.getPropertyValue(request, menuItemId, "display",
					"column_width");
			if (SwtUtil.isEmptyOrNull(width)) {
				width = "specifiedEntityId=110,specifiedAccountId=110,accountName=195,newInternalCrFormat=120,newInternalDrFormat=120,newExternalCrFormat=120,newExternalDrFormat=120,newExternalCrFormatInt=120,newExternalDrFormatINt=120";
			}
			widths = new HashMap<String, String>();
			props = width.split(",");
			for (int i = 0; i < props.length; i++) {
				if (props[i].indexOf("=") != -1) {
					String[] propval = props[i].split("=");
					widths.put(propval[0], propval[1]);
				}
			}
			request.setAttribute("column_width", widths);
			log.debug(this.getClass().getName()
					+ " - [bindColumnWidthInRequest] - Exit");
		} catch (Exception e) {
			log.error(this.getClass().getName()
					+ " - [bindColumnWidthInRequest] - " + e.getMessage());
		} finally {
			widths = null;
			props = null;
		}
	}
	
	/**
	 * This method save column in session
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return
	 * @throws SwtException
	 */
	public String saveColumnWidth() throws SwtException {
HttpServletRequest request = ServletActionContext.getRequest();
HttpServletResponse response = ServletActionContext.getResponse();

		String width = null;
		try {
			if (!SwtUtil.isEmptyOrNull(width = request.getParameter("width"))) {
				SwtUtil.setPropertyValue(request,
						SwtUtil.getUserCurrentEntity(request.getSession()),
						menuItemId, "display", "column_width", width);
			} else {
				throw new Exception(
						"You must send 'width' and 'entityid' parameters");
			}
			request.setAttribute("reply_status_ok", "true");
			request.setAttribute("reply_message", "Column width saved ok");
			log.debug(this.getClass().getName()
					+ " - [saveColumnWidth] - Exit ");
		} catch (Exception e) {
			log.error(this.getClass().getName()
					+ " - [saveColumnWidth] - Error - " + e.getMessage());
			request.setAttribute("reply_status_ok", "false");
			request.setAttribute("reply_message", e.getMessage());
			request.setAttribute(
					"reply_location",
					e.getStackTrace()[0].getClassName() + "."
							+ e.getStackTrace()[0].getMethodName() + ":"
							+ e.getStackTrace()[0].getLineNumber());
			SwtUtil.logException(
					SwtErrorHandler.getInstance().handleException(e,
							"saveColumnWidth",
							AcctSpecificSweepFormatAction.class), request,
					"");
		}
		return ("statechange");
	}
	
	
	/**
	 * Save column order in session
	 * @param mapping 
	 * @param form
	 * @param request
	 * @param response
	 * @return
	 * @throws SwtException
	 */
	public String saveColumnOrder() throws SwtException {
HttpServletRequest request = ServletActionContext.getRequest();
HttpServletResponse response = ServletActionContext.getResponse();

		String columnOrder = null;
		try {
			log.debug(this.getClass().getName()
					+ " - [ saveColumnOrder ] - Entry ");
			columnOrder = request.getParameter("order");
			if (!SwtUtil.isEmptyOrNull(columnOrder)) {
				SwtUtil.setPropertyValue(request,
						SwtUtil.getUserCurrentEntity(request.getSession()),
						menuItemId, "display", "column_order", columnOrder);
			}
			request.setAttribute("reply_status_ok", "true");
			request.setAttribute("reply_message", "Column order saved ok");
		} catch (Exception e) {
			log.error(this.getClass().getName() + "- [ saveColumnOrder() ] - "
					+ e);
			request.setAttribute("reply_status_ok", "false");
			request.setAttribute("reply_message", e.getMessage());
			request.setAttribute(
					"reply_location",
					e.getStackTrace()[0].getClassName() + "."
							+ e.getStackTrace()[0].getMethodName() + ":"
							+ e.getStackTrace()[0].getLineNumber());
		} finally {
			columnOrder = null;
			log.debug(this.getClass().getName()
					+ " - [ saveColumnOrder ] - Exit ");
		}
		return ("statechange");
	}
	
	/**
	 * This method gets entity list from database and put them in request scope
	 * 
	 * @param request
	 *            HttpServletRequest request
	 * @throws SwtException
	 */
	private void putEntityListInReq(HttpServletRequest request, boolean viewEnable) throws SwtException {
		
		// entity collection
		Collection<EntityUserAccess> entityColl = null;
		Collection<EntityUserAccess> entityFullAccess = new ArrayList<EntityUserAccess>();
		Collection<LabelValueBean> colEntityLVB = null;
		EntityUserAccess entity = null;
		Iterator itr = null;
		try {
			// Get the user access entity list .
			entityColl = SwtUtil.getUserEntityAccessList(request.getSession());
			if (!viewEnable) {
				itr = entityColl.iterator();
				while (itr.hasNext()) {
	
					entity = (EntityUserAccess) itr.next();
					if (entity.getAccess() == 0) {
						entityFullAccess.add(entity);
	
					}
				}
			} else {
				entityFullAccess = entityColl;  
			}

			// Convert the entity list to label value bean.
			colEntityLVB = SwtUtil.convertEntityAcessCollectionLVLFullName(
					entityFullAccess, request.getSession());
			// Set the entity collection into request.
			request.setAttribute("entities", colEntityLVB);
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					+ " - [putEntityFullAccessListInReq] - Exception -"
					+ exp.getMessage());
		} finally {
			// nullify objects
			entityColl = null;
			colEntityLVB = null;
		}
	}
	/**
	 * Add accounts list in request
	 * @param request
	 * @param entityId
	 * @param currencyId
	 * @param putAllLabel
	 * @throws SwtException
	 */
	public void putAccountListInRequest(HttpServletRequest request,
			String entityId, String currencyId, boolean putAllLabel) throws SwtException {
		log.debug(this.getClass().getName()
				+ " method [putAccountListInRequest] - Enter ");
		CorrespondentAcctMaintenanceManager correspondentAcctMaintenanceManager = (CorrespondentAcctMaintenanceManager) SwtUtil
				.getBean("correspondentAcctMaintenanceManager");
		Collection<LabelValueBean> accounts = correspondentAcctMaintenanceManager
				.getAccountIdDropDown(SwtUtil.getCurrentHostId(),
						entityId, currencyId);
		Collection<LabelValueBean> accountDropDown = new ArrayList<LabelValueBean>();
		if (putAllLabel) {
			accountDropDown.add(new LabelValueBean(SwtConstants.ALL_LABEL,
					SwtConstants.ALL_VALUE));
		}
		Iterator<LabelValueBean> itr = accounts.iterator();
		while (itr.hasNext()) {
			LabelValueBean lvb = (LabelValueBean) (itr.next());
			accountDropDown.add(lvb);
		}
		request.setAttribute("accounts", accountDropDown);
		log.debug(this.getClass().getName()
				+ " method [putAccountListInRequest] - Exit ");
	}
	
	/**
	 * Add accounts list in request
	 * @param request
	 * @param entityId
	 * @param currencyId
	 * @param putAllLabel
	 * @throws SwtException
	 */
	public void putSpecificAccountListInRequest(HttpServletRequest request,
			String entityId, String currencyId, boolean putAllLabel) throws SwtException {
		log.debug(this.getClass().getName()
				+ " method [putSpecificAccountListInRequest] - Enter ");
		CorrespondentAcctMaintenanceManager correspondentAcctMaintenanceManager = (CorrespondentAcctMaintenanceManager) SwtUtil
				.getBean("correspondentAcctMaintenanceManager");
		Collection<LabelValueBean> accounts = correspondentAcctMaintenanceManager
				.getAccountIdDropDown(SwtUtil.getCurrentHostId(),
						entityId, currencyId);
		Collection<LabelValueBean> accountDropDown = new ArrayList<LabelValueBean>();
		if (putAllLabel) {
			accountDropDown.add(new LabelValueBean(SwtConstants.ALL_LABEL,
					SwtConstants.ALL_VALUE));
		}
		Iterator<LabelValueBean> itr = accounts.iterator();
		while (itr.hasNext()) {
			LabelValueBean lvb = (LabelValueBean) (itr.next());
			accountDropDown.add(lvb);
		}
		request.setAttribute("specAccounts", accountDropDown);
		log.debug(this.getClass().getName()
				+ " method [putSpecificAccountListInRequest] - Exit ");
	}
	
	
	/**
	 * Put message format list in request
	 * @param request
	 * @param entityId
	 * @throws SwtException
	 */
	public void putMessageFormatListInRequest(HttpServletRequest request, String entityId) throws SwtException {
		log.debug(this.getClass().getName()
				+ " method [putMessageFormatList] - Enter ");
		AcctMaintenanceManager acctMaintenanceManager  = (AcctMaintenanceManager) SwtUtil
				.getBean("acctMaintenanceManager");
		Collection<LabelValueBean> formats = acctMaintenanceManager
				.getFormatListColl(SwtUtil.getCurrentHostId(), entityId, new SystemInfo(), SwtUtil.getCurrentSystemFormats(request.getSession()));
		Collection<LabelValueBean> formatsDropDown = new ArrayList<LabelValueBean>();
		Iterator<LabelValueBean> itr = formats.iterator();
		while (itr.hasNext()) {
			LabelValueBean lvb = (LabelValueBean) (itr.next());
			formatsDropDown.add(lvb);
		}
		request.setAttribute("formats", formatsDropDown);
		log.debug(this.getClass().getName()
				+ " method [putMessageFormatList] - Exit ");
		
	}


	
	 /** 
	 * This function returns collection of currencies
	 * 
	 * @param request
	 * @param entityId
	 * @return  collection of currencies
	 * @throws SwtException
	 */
	private Collection<LabelValueBean> getCurrencyList(HttpServletRequest request, String entityId, boolean fullAccess) 
			throws SwtException {
		
		// Current user's role id
		String roleId = null;
		// Currency list allocated for current user (mapped with role)
		ArrayList<LabelValueBean> currencyList = null;
		// Currency List
		ArrayList ccyList = null;
		try {
			log.debug(this.getClass().getName() + " - [getCurrencyList] - Enter");
			// Getting the User's Role Id from the session object
			roleId = ((CommonDataManager) request.getSession().getAttribute(
					SwtConstants.CDM_BEAN)).getUser().getRoleId();
			// Returns the currency Access List based on the Role
			if (fullAccess) {
				currencyList = (ArrayList<LabelValueBean>) SwtUtil
						.getSwtMaintenanceCache().getCurrencyViewORFullAcessLVL(roleId, entityId);
			} else {
				currencyList = (ArrayList) getCurrencyFullAccessList(request,
						SwtUtil.getCurrentHostId(), entityId);
			}
			
							

			if (currencyList != null) {
				/*
				 * Removes the LabelValueBean object for the Key as 'Default'
				 * from the collection
				 */
				currencyList.remove(new LabelValueBean("Default", "*"));
			}
			log.debug(this.getClass().getName() + " - [getCurrencyList] - Exit");
			return currencyList;
		} catch (Exception ex) {
			log.error(this.getClass().getName()
					+ " - [getCurrencyList] - Exception - " + ex.getMessage());
			// Re-throw an exception
			throw SwtErrorHandler.getInstance().handleException(ex,
					"getCurrencyList", AcctSpecificSweepFormatAction.class);
		} finally {
			// nullify object(s)
			roleId = null;
		}

	}
	/**
	 * 
	 * This function returns the collection of LabelValueBean objects for a role
	 * id and entity id which have the full access.
	 * 
	 * @param HttpServletRequest
	 *            request
	 * @param String
	 *            hostId
	 * @param String
	 *            entityId
	 * @return - currrencyList
	 * @throws SwtException
	 *             - SwtException object
	 */
	private Collection<LabelValueBean> getCurrencyFullAccessList(
			HttpServletRequest request, String hostId, String entityId)
			throws SwtException {

		// String Variable to hold the roleId
		String roleId = null;
		// Variable to hold the currencyMap object
		Map<String, String> currencyMap = null;
		// Variable to hold the currrencyList object
		Collection<LabelValueBean> currrencyList = null;
		// Variable to hold the itrCurrencyKey object
		Iterator<String> itrCurrencyKey = null;
		// String Variable to hold the currencyId
		String currencyId = null;

		try {
			log.debug(this.getClass().getName()
					+ " - [ getCurrencyFullAccessList ] - Entry ");
			/* Getting the User's Role Id from the session object */
			roleId = ((CommonDataManager) request.getSession().getAttribute(
					SwtConstants.CDM_BEAN)).getUser().getRoleId();
			currrencyList = new ArrayList<LabelValueBean>();
			/* Returns the currency Access List based on the Role */
			currencyMap = SwtUtil.getCurrencyFullAccessMap(roleId, hostId,
					entityId);

			if (currencyMap != null && currencyMap.size() > 0) {
				// iterate the currency map key values
				itrCurrencyKey = currencyMap.keySet().iterator();
				while (itrCurrencyKey.hasNext()) {
					// get the currency id from map
					currencyId = itrCurrencyKey.next();

					// add labelvaluebean for currency id
					currrencyList.add(new LabelValueBean((String) currencyMap
							.get(currencyId), currencyId));
				}
			}
			log.debug(this.getClass().getName()
					+ " - [ getCurrencyFullAccessList ] - Exit ");
		} catch (SwtException swtexp) {
			log.error("SwtException Catch in "+AcctSpecificSweepFormatAction.class+".'getCurrencyFullAccessList' method : "
					+ swtexp.getMessage());
			SwtUtil.logException(swtexp, request, "");
			throw swtexp;
		} catch (Exception exp) {
			log.error("Exception Catch in "+AcctSpecificSweepFormatAction.class+".'getCurrencyFullAccessList' method : "
					+ exp.getMessage());
			throw new SwtException(exp.getMessage());

		} finally {
			// nullify objects
			roleId = null;
			currencyMap = null;
			itrCurrencyKey = null;
			currencyId = null;
		}
		return currrencyList;
	}
	public String clearSpecificAccountSweepFormatFromSession() throws SwtException {
		HttpServletRequest request = ServletActionContext.getRequest();
		HttpServletResponse response = ServletActionContext.getResponse();
		String accountId = null;
		String id = null;

		try {
			log.debug(this.getClass().getName()
					+ " method [clearSpecificAccountSweepFormatFromSession] - Enter ");
			accountId = request.getParameter("accountId");
			id = request.getParameter("id");
			if (accountId != null) {
				request.getSession().removeAttribute("acctSpecificFormatcollInSession" + accountId);
			} else {
				request.getSession().removeAttribute("acctSpecificFormatcollInSession" + "*.*");
			}

			log.debug(this.getClass().getName()
					+ " method [clearSpecificAccountSweepFormatFromSession] - Exit ");
		} catch (Exception e) {
			log.error(this.getClass().getName()
					+ " - [clearSpecificAccountSweepFormatFromSession] - Exception -"
					+ e.getStackTrace()[0].getClassName() + "."
					+ e.getStackTrace()[0].getMethodName() + ":"
					+ e.getStackTrace()[0].getLineNumber() + " "
					+ e.getMessage());
			return ("fail");
		}
		return null;
	}
	
	/**
	 * This is used to get specific account sweep size
	 * 
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return ActionForward
	 * @throws SwtException
	 */

	public String getSpecificAccountSweepSize() throws SwtException {
HttpServletRequest request = ServletActionContext.getRequest();
HttpServletResponse response = ServletActionContext.getResponse();

		
		/* Local variable declaration and initialization */
		String hostId=null;
		String entityId=null;
		String id=null;
		String accountId=null;
		String size ="0";
		String parentMethodName = null;
		Collection<AccountSpecificSweepFormat>  specificFormatList = null;
		HashMap<String, Collection<AccountSpecificSweepFormat>> allStoredSpecificInrequest = null;
		try {
			log.debug(this.getClass().getName() + " - [currencyAccessConfirm] - " + "Entry");
			/*Getting host id from cache manager file*/
			hostId = SwtUtil.getCurrentHostId();
			/*Getting account id,entity id,status from request*/
			entityId = request.getParameter("entityId");
			id = request.getParameter("id");
			accountId = request.getParameter("accountId");
			parentMethodName = request.getParameter("parentMethodName");
			if("add".equals(parentMethodName)) {
				allStoredSpecificInrequest = (HashMap<String, Collection<AccountSpecificSweepFormat>>) request.getSession()
						.getAttribute("acctSpecificFormatcollInSession"+"*.*");				
			}else {
				allStoredSpecificInrequest = (HashMap<String, Collection<AccountSpecificSweepFormat>>) request.getSession()
						.getAttribute("acctSpecificFormatcollInSession"+accountId);
			}

			if(allStoredSpecificInrequest == null) {
				allStoredSpecificInrequest = new HashMap<String, Collection<AccountSpecificSweepFormat>>();
			}
			if(allStoredSpecificInrequest.containsKey(id))  {
				specificFormatList =  allStoredSpecificInrequest.get(id);

			}else  {
				specificFormatList = acctSpecificSweepFormatManager
							.getAccountSpecificSweepFormatList(hostId, entityId, accountId);
			}

			if(specificFormatList != null) {
				List<AccountSpecificSweepFormat> filteredList = specificFormatList.stream()
						.filter(accountSpecific ->accountSpecific.getChanged() == null || ( accountSpecific.getChanged() != null && !accountSpecific.getChanged().equals("D")))
						.collect(Collectors.toList());

					size = ""+filteredList.size();
			}
			
			response.getWriter().print(size);
			log.debug(this.getClass().getName() + " - [getSpecificAccountSweepSize] - " + "Exit");
		} catch (SwtException swtexp) {			
			log.error(this.getClass().getName()
					+ " - Exception Catched in [getSpecificAccountSweepSize] method : - "
					+ swtexp.getMessage());

			SwtUtil.logException(swtexp,request,"");
	        return ("fail");
		} catch (Exception exp) {

			exp.printStackTrace();
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "getSpecificAccountSweepSize", AccountAccessAction.class), request, "");
		} 

		return null;
	}
	
	
	
	public String getUpdatedLists() throws SwtException {
HttpServletRequest request = ServletActionContext.getRequest();
HttpServletResponse response = ServletActionContext.getResponse();

		// Entity Id
		String specifiedEntityId = null;
		// Currency Id
		String currencyId = null;
		String accountId = null;
		ResponseHandler responseHandler = null;
		SwtResponseConstructor responseConstructor = null;
		SwtXMLWriter xmlWriter = null;
		try {
			log.debug(this.getClass().getName() + " - [getUpdatedFormatsList] - Entering");
			// to get the host id for application
			//hostId = CacheManager.getInstance().getHostId();
			// get the specifiedEntityId from request
			specifiedEntityId = request.getParameter("specifiedEntityId");
			currencyId = request.getParameter("currencyId");
			accountId = request.getParameter("accountId");
			
			responseHandler = new ResponseHandler();
			responseConstructor = new SwtResponseConstructor();
			xmlWriter = responseConstructor.getXMLWriter();

			xmlWriter.startElement(SwtConstants.REFRESH_COMBO_LIST);

			responseConstructor.formRequestReply(Boolean.parseBoolean(SwtConstants.STR_TRUE),
					SwtConstants.DATA_FETCH_OK);
			putSpecificAccountListInRequest(request, specifiedEntityId, currencyId, false);
			ArrayList<SelectInfo> lstSelect = new ArrayList<SelectInfo>();
			// options drop down list
			ArrayList<OptionInfo> lstOptions = new ArrayList<OptionInfo>();
			
			/***** Specific Accounts Combo Start ***********/
			lstOptions = new ArrayList<OptionInfo>();
			LabelValueBean row = null;
			Collection collAcctsList = (Collection) request.getAttribute("specAccounts");
			lstOptions.add(new OptionInfo("", "", false));
			Iterator j = collAcctsList.iterator();
			row = null;
			while (j.hasNext()) {
				row = (LabelValueBean) j.next();
				if (!SwtUtil.isEmptyOrNull(accountId)) {
						lstOptions.add(new OptionInfo(row.getValue(), row.getLabel(), false));
				} else {
					lstOptions.add(new OptionInfo(row.getValue(), row.getLabel(), false));
				}
			}
			lstSelect.add(new SelectInfo("specAccounts", lstOptions));
			/***** Specific Accounts Combo End ***********/
			
			responseConstructor.formSelect(lstSelect);
			xmlWriter.endElement(SwtConstants.REFRESH_COMBO_LIST);
			request.setAttribute("data", xmlWriter.getData());
			log.debug(this.getClass().getName() + " - [getUpdatedFormatsList] - Existing");
			return ("data");

		} catch (SwtException swtexp) {
			log.error("SwtException Catch in PreAdviceInputAction.'getUpdatedFormatsList' method : " + swtexp.getMessage());
			SwtUtil.logException(swtexp, request, "");
		} catch (Exception exp) {
			log.error("Exception Catch in PreAdviceInputAction.'getUpdatedFormatsList' method : " + exp.getMessage());
			SwtUtil.logException(
					SwtErrorHandler.getInstance().handleException(exp, "getUpdatedFormatsList", PreAdviceInputAction.class),
					request, "");
		} finally {
			// nullify objects
			specifiedEntityId = null;
		}
		return null;
	}
	
}
