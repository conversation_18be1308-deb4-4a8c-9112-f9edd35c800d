/*
 * @(#)AccountAttributeMaintenanceAction.java 1.0 12/12/14
 *
 * Copyright (c) 2006-2015 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.maintenance.web;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;



import org.springframework.dao.DataIntegrityViolationException;
import org.swallow.control.web.ShortcutAction;
import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.maintenance.model.AccountAttribute;
import org.swallow.maintenance.model.AccountAttributeFuncGroup;
import org.swallow.maintenance.model.AccountAttributeHDR;
import org.swallow.maintenance.model.EntityUserAccess;
import org.swallow.maintenance.model.FunctionalGroup;
import org.swallow.maintenance.service.AccountAttributeMaintenanceManager;
import org.swallow.util.CommonDataManager;
import org.swallow.util.OpTimer;
import org.swallow.util.SwtConstants;
import org.swallow.util.SwtUtil;
import org.swallow.util.UserThreadLocalHolder;
import org.swallow.util.LabelValueBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.apache.struts2.convention.annotation.Result;
import org.apache.struts2.convention.annotation.AllowedMethods;
import org.apache.struts2.convention.annotation.Action;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.apache.struts2.ServletActionContext;
import org.swallow.util.struts.ActionErrors;
import org.swallow.util.struts.ActionMessage;
import org.swallow.util.struts.CustomActionSupport;

@Action(value = "/accountAttribute", results = {
	@Result(name = "accounttattrdefinition", location = "/jsp/maintenance/accountattributedefinitionflex.jsp"),
	@Result(name = "accounttattrdefinitiongrid", location = "/jsp/maintenance/accountattributedefinitionflexdata.jsp"),
	@Result(name = "accountattributeaddflex", location = "/jsp/maintenance/accountattributehdraddflex.jsp"),
	@Result(name = "accountattributehdrdata", location = "/jsp/maintenance/accountattributehdraddflexdata.jsp"),
	@Result(name = "attributeUsageSummaryFlex", location = "/jsp/maintenance/attributeusagesummaryflex.jsp"),
	@Result(name = "attributeUsageSummaryFlexData", location = "/jsp/maintenance/attributeusagesummaryflexdata.jsp"),
	@Result(name = "save", location = "/jsp/flexstatechange.jsp"),
	@Result(name = "attributeUsageSummaryFlexAdd", location = "/jsp/maintenance/attributeusagesummaryaddflex.jsp"),
	@Result(name = "attributeUsageSummaryFlexAddData", location = "/jsp/maintenance/attributeusagesummaryaddflexdata.jsp"),
	@Result(name = "dataerror", location = "/jsp/maintenance/attributeusagesummaryflexdataerror.jsp"),
	@Result(name = "accountAttrMaintenance", location = "/jsp/maintenance/accountattributeflex.jsp"),
	@Result(name = "accountAttrMaintenanceData", location = "/jsp/maintenance/accountattributeflexdata.jsp"),
	@Result(name = "accountAttrMaintenanceGridData", location = "/jsp/maintenance/accountattributegridflexdata.jsp"),
	@Result(name = "accountAttrAddMaintenance", location = "/jsp/maintenance/accountattributeaddflex.jsp"),
	@Result(name = "accountAttrAddMaintenanceData", location = "/jsp/maintenance/accountattributeaddflexdata.jsp"),
	@Result(name = "saveAccountValue", location = "/jsp/flexstatechange.jsp"),
	@Result(name = "attributeslastvaluesflex", location = "/jsp/maintenance/accountattributelastvaluesflex.jsp"),
	@Result(name = "attributeslastvaluesData", location = "/jsp/maintenance/accountattributelastvaluesflexdata.jsp"),
	@Result(name = "statechange", location = "/jsp/flexstatechange.jsp"),
	@Result(name = "fail", location = "/error.jsp"),
	@Result(name = "displayAttributes", location = "/jsp/maintenance/attributesnew.jsp"),
})

@AllowedMethods ({"accountDefinition" ,"displayAcctAttributeDefinition" ,"displayAddAccountAttributeHDR" ,"displayChangeOrViewAccountAttributeHDR" ,"saveAccountAttributeHDR" ,"saveAccountAttributeValue" ,"deleteAccountAttributeHDR" ,"attributeUsageSummary" ,"displayAttributeUsageSummary" ,"deleteAttributeUsageAttribute" ,"addChangeAttributeUsageSummary" ,"displayAttributeUsageSummaryAdd" ,"saveAttributeUsageSummary" ,"accountAttributesMaintenance" ,"displayAccountAttributes" ,"listAccountAttributes" ,"accountAttributesAddMaintenance" ,"accountAttributesValuesAdd" ,"deleteAccountAttribute" ,"displayAttributesFlex" ,"checkExistingAttributeData" ,"saveColumnWidth" ,"saveColumnOrder" ,"checkExistingAccountAttributeDefintionList" ,"checkEffectiveDate" ,"displayAttributesAngular" })
public class AccountAttributeMaintenanceAction extends CustomActionSupport {
private final Log log = LogFactory.getLog(ShortcutAction.class);

public String execute() throws Exception {
    HttpServletRequest request = ServletActionContext.getRequest();
    // List of methods 
    String method = String.valueOf(request.getParameter("method"));

    switch (method) {
        case "accountDefinition":
            return accountDefinition();
        case "displayAcctAttributeDefinition":
            return displayAcctAttributeDefinition();
        case "displayAddAccountAttributeHDR":
            return displayAddAccountAttributeHDR();
        case "displayChangeOrViewAccountAttributeHDR":
            return displayChangeOrViewAccountAttributeHDR();
        case "saveAccountAttributeHDR":
            return saveAccountAttributeHDR();
        case "saveAccountAttributeValue":
            return saveAccountAttributeValue();
        case "deleteAccountAttributeHDR":
            return deleteAccountAttributeHDR();
        case "attributeUsageSummary":
            return attributeUsageSummary();
        case "displayAttributeUsageSummary":
            return displayAttributeUsageSummary();
        case "deleteAttributeUsageAttribute":
            return deleteAttributeUsageAttribute();
        case "addChangeAttributeUsageSummary":
            return addChangeAttributeUsageSummary();
        case "displayAttributeUsageSummaryAdd":
            return displayAttributeUsageSummaryAdd();
        case "saveAttributeUsageSummary":
            return saveAttributeUsageSummary();
        case "accountAttributesMaintenance":
            return accountAttributesMaintenance();
        case "displayAccountAttributes":
            return displayAccountAttributes();
        case "listAccountAttributes":
            return listAccountAttributes();
        case "accountAttributesAddMaintenance":
            return accountAttributesAddMaintenance();
        case "accountAttributesValuesAdd":
            return accountAttributesValuesAdd();
        case "deleteAccountAttribute":
            return deleteAccountAttribute();
        case "displayAttributesFlex":
            return displayAttributesFlex();
        case "checkExistingAttributeData":
            return checkExistingAttributeData();
        case "saveColumnWidth":
            return saveColumnWidth();
        case "saveColumnOrder":
            return saveColumnOrder();
        case "checkExistingAccountAttributeDefintionList":
            return checkExistingAccountAttributeDefintionList();
        case "checkEffectiveDate":
            return checkEffectiveDate();
        case "displayAttributesAngular":
            return displayAttributesAngular();
        default:
            break;
    }

    return null;
}



	
	@Autowired
private AccountAttributeMaintenanceManager accountAttributeMaintenanceManager = null;
	private final String menuItemId = "" + SwtConstants.SCREEN_ACCOUNT_ATTRIBUTES;
	
	private OpTimer opTimer = new OpTimer();
	

	public void setAccountAttributeMaintenanceManager(
			AccountAttributeMaintenanceManager accountAttributeMaintenanceManager) {
		this.accountAttributeMaintenanceManager = accountAttributeMaintenanceManager;
	}
	
	public String accountDefinition()
			throws SwtException {
HttpServletRequest request = ServletActionContext.getRequest();
HttpServletResponse response = ServletActionContext.getResponse();

		log.debug(this.getClass().getName() + " - [accountDefinition] - Enter/Exit ");
		// set the Attribute for menuAccessId
		request.setAttribute("menuAccessId", request.getParameter("menuAccessId"));
		request.setAttribute("method", "accountDefinition");
		return ("accounttattrdefinition");
	}
	
	/**
	 *  Show summary screen (Account Attribute definitions) to display a list of account attributes 
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return
	 * @throws SwtException
	 */
	public String displayAcctAttributeDefinition()
					throws SwtException {
HttpServletRequest request = ServletActionContext.getRequest();
HttpServletResponse response = ServletActionContext.getResponse();

		try {
			
			log.debug(this.getClass().getName() + " method [displayAcctAttributeDefinition] Enter ");
			
			Collection<AccountAttributeHDR>accountAttributeDetailList = accountAttributeMaintenanceManager.getAcctAttributHDRDetailList();
			Iterator itr = accountAttributeDetailList.iterator();
			AccountAttributeHDR account = null;
			while (itr.hasNext()) {
				account = (AccountAttributeHDR ) itr.next();
				account.setUpdateDateAsString((SwtUtil.formatDate(account.getUpdateDate(), SwtUtil.getCurrentSystemFormats(
								request.getSession()).getDateFormatValue() + " HH:mm:ss")));
			}
			request.setAttribute("accountAttributeDetailList",
					accountAttributeDetailList);
			if(accountAttributeDetailList != null)
			request.setAttribute("recordCount",
					accountAttributeDetailList.size());
			bindColumnOrderInRequest(request, "" + SwtConstants.SCREEN_ACCOUNT_ATTRIBUTES);
			bindColumnWidthInRequest(request, "" + SwtConstants.SCREEN_ACCOUNT_ATTRIBUTES);
			// set the Attribute for menuAccessId
			request.setAttribute("menuAccessId", request
					.getParameter("menuAccessId"));
			request.setAttribute("reply_status_ok", SwtConstants.STR_TRUE);
			request.setAttribute("reply_message", "Data fetch OK");
			log.debug(this.getClass().getName() + " method [displayAcctAttributeDefinition]  Exit ");
		} catch (Exception e) {
			log.error(this.getClass().getName()
					+ " - [displayAcctAttributeDefinition] - Exception -"
					+ e.getStackTrace()[0].getClassName() + "."
					+ e.getStackTrace()[0].getMethodName() + ":"
					+ e.getStackTrace()[0].getLineNumber() + " "
					+ e.getMessage());
			request.setAttribute("reply_status_ok", SwtConstants.STR_FALSE);
			request.setAttribute("reply_message", e.getMessage());
			request.setAttribute("reply_location", e.getStackTrace()[0]
					.getClassName()
					+ "."
					+ e.getStackTrace()[0].getMethodName()
					+ ":"
					+ e.getStackTrace()[0].getLineNumber());
			
			return ("dataerror");
		}
		
		return ("accounttattrdefinitiongrid");
	}
	/**
	 * This method return the list of attribute types
	 * @return ArrayList
	 * @throws SwtException
	 */
	 ArrayList getListTypes(HttpServletRequest request) throws SwtException{
		ArrayList<String> types = null;
		try {
			log.debug(this.getClass().getName() + " method [getListTypes]  Enter ");
			types = new ArrayList<String>(); 
			types.add(SwtUtil.getMessage(SwtConstants.NUMERIC_TYPE, request));
			types.add(SwtUtil.getMessage(SwtConstants.DATE_TYPE, request));
			types.add(SwtUtil.getMessage(SwtConstants.TEXT_TYPE, request));
			log.debug(this.getClass().getName() + " method [getListTypes]  Exit ");
		} catch (Exception e) {
			log.error(this.getClass().getName()
					+ " - [getListTypes] - Exception -"
					+ e.getStackTrace()[0].getClassName() + "."
					+ e.getStackTrace()[0].getMethodName() + ":"
					+ e.getStackTrace()[0].getLineNumber() + " "
					+ e.getMessage());
		}
		
		return types;
	}
	
	
	
	/**
	 *  Display a Flex screen (Define Attribute) to add a new attribute header
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return
	 * @throws SwtException
	 */
	public String displayAddAccountAttributeHDR() throws SwtException {
HttpServletRequest request = ServletActionContext.getRequest();
HttpServletResponse response = ServletActionContext.getResponse();

		try {
			log.debug(this.getClass().getName() + " method [displayAddAccountAttributeHDR]  Enter ");

			request.setAttribute("screenName", "addScreen");
			request.setAttribute("method", "displayAddAccountAttributeHDR");
			if (SwtUtil.isEmptyOrNull(request.getParameter("loadFlex"))) {
				log.debug(this.getClass().getName()
						+ "-[displayAddAccountAttributeHDR]-Exit loading SWF");
				
				return ("accountattributeaddflex");
			}
			
			request.setAttribute("typesList", getListTypes(request));
			// set "Numeric" as default type
			request.setAttribute("selectedType", SwtUtil.getMessage(SwtConstants.NUMERIC_TYPE, request));
			request.setAttribute("menuAccessId", request
					.getParameter("menuAccessId"));
			
			request.setAttribute("reply_status_ok", SwtConstants.STR_TRUE);
			request.setAttribute("reply_message", "Data fetch OK");
			
			
			log.debug(this.getClass().getName() + " method [displayAddAccountAttributeHDR]  Exit ");
			
		} catch (Exception e) {
			log.error(this.getClass().getName()
					+ " - [displayAddAccountAttributeHDR] - Exception -"
					+ e.getStackTrace()[0].getClassName() + "."
					+ e.getStackTrace()[0].getMethodName() + ":"
					+ e.getStackTrace()[0].getLineNumber() + " "
					+ e.getMessage());
			request.setAttribute("reply_status_ok", SwtConstants.STR_FALSE);
			request.setAttribute("reply_message", e.getMessage());
			request.setAttribute("reply_location", e.getStackTrace()[0]
					.getClassName()
					+ "."
					+ e.getStackTrace()[0].getMethodName()
					+ ":"
					+ e.getStackTrace()[0].getLineNumber());
			
			return ("dataerror");
		}
		
		return ("accountattributehdrdata");
	}
	/**
	 *  Display a Flex screen (Define Attribute) to change or view an attribute header details
	 *  
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return ActionForward
	 * @throws SwtException
	 */
	public String displayChangeOrViewAccountAttributeHDR() throws SwtException {
HttpServletRequest request = ServletActionContext.getRequest();
HttpServletResponse response = ServletActionContext.getResponse();

		
		String attributeId = null;
		AccountAttributeHDR acctAttrHDR = null;
		String selectedType = null;
		
		try {
			log.debug(this.getClass().getName() + " method [displayChangeOrViewAccountAttributeHDR] - Enter ");
			request.setAttribute("screenName", request.getParameter("screenName"));
			attributeId = SwtUtil.decode64(request.getParameter("attributeId"));
			if (SwtUtil.isEmptyOrNull(request.getParameter("loadFlex"))) {
				log.debug(this.getClass().getName() + "-[displayChangeOrViewAccountAttributeHDR] - Exit loading SWF");
				request.setAttribute("selectedAttributeId", attributeId);
				return ("accountattributeaddflex");
			}
			//Fetch the required account attribute HDR
			acctAttrHDR = accountAttributeMaintenanceManager.getAccountAttributeHDR(attributeId);
			request.setAttribute("accountAttributeHDR", acctAttrHDR);
			//set min and max values separately
			request.setAttribute("minValue", acctAttrHDR.getValidateNumMin());
			request.setAttribute("maxValue", acctAttrHDR.getValidateNumMax());
			request.setAttribute("typesList", getListTypes(request));
			
			request.setAttribute("currencyFormat", SwtUtil.getCurrentCurrencyFormat(request.getSession()));
			
			String type = acctAttrHDR.getValueType();
		    if("N".equalsIgnoreCase(type)){
		    	selectedType = SwtUtil.getMessage(SwtConstants.NUMERIC_TYPE, request);//SwtConstants.NUMERIC_TYPE;
		    }else if("T".equalsIgnoreCase(type)){
		    	selectedType = SwtUtil.getMessage(SwtConstants.TEXT_TYPE, request);//SwtConstants.TEXT_TYPE;
		    }else
		    	selectedType =SwtUtil.getMessage(SwtConstants.DATE_TYPE, request);// SwtConstants.DATE_TYPE;
		    
			request.setAttribute("selectedType", selectedType);
			request.setAttribute("menuAccessId", request
					.getParameter("menuAccessId"));
			
			request.setAttribute("reply_status_ok", SwtConstants.STR_TRUE);
			request.setAttribute("reply_message", "Data fetch OK");
			
			log.debug(this.getClass().getName() + " method [displayChangeOrViewAccountAttributeHDR] - Exit ");
			
		} catch (Exception e) {
			log.error(this.getClass().getName()
					+ " - [displayChangeOrViewAccountAttributeHDR] - Exception -"
					+ e.getStackTrace()[0].getClassName() + "."
					+ e.getStackTrace()[0].getMethodName() + ":"
					+ e.getStackTrace()[0].getLineNumber() + " "
					+ e.getMessage());
			request.setAttribute("reply_status_ok", SwtConstants.STR_FALSE);
			request.setAttribute("reply_message", e.getMessage());
			request.setAttribute("reply_location", e.getStackTrace()[0]
					.getClassName()
					+ "."
					+ e.getStackTrace()[0].getMethodName()
					+ ":"
					+ e.getStackTrace()[0].getLineNumber());
			
			return ("dataerror");
		}
		
		return ("accountattributehdrdata");
	}
	
	/**
	 * Used to add a new attribute header 
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return ActionForward
	 * @throws SwtException
	 */
	public String saveAccountAttributeHDR() throws SwtException {
HttpServletRequest request = ServletActionContext.getRequest();
HttpServletResponse response = ServletActionContext.getResponse();

		
		 String attributeId = null;
		 String attributeName = null;
		 String tooltipText = null;
		 String valueType = null;
		 String effectiveDateRequired = null;
		 String effectiveDateAllowTime = null;
		 String validateNumMin = null;
		 String validateNumMax = null ;
		 String validateDateAllowTime = null;
		 String validateTextMinLen = null;
		 String validateTextMaxLen =null ;
		 String validateTextRegex = null;
		 String validateTextRegexMsg = null;
		 String updateUser = null;
		 AccountAttributeHDR accountAttrHdr = null;
		 ActionErrors errors = null;
		 
		try {
			log.debug(this.getClass().getName() + " method [saveAccountAttributeHDR] - Enter ");
			errors = new ActionErrors();
			updateUser = SwtUtil.getCurrentUserId(request.getSession());
			//Get values from request
			attributeId = request.getParameter("accountAttributeId");
			attributeName = request.getParameter("accountAttributeName");
			tooltipText = request.getParameter("tooltipText");
			effectiveDateRequired = request.getParameter("effectiveDateRequired");
			effectiveDateAllowTime = request.getParameter("effectiveDateAllowTime");
			valueType = request.getParameter("valuetype");
			validateNumMax = request.getParameter("validateNumMax");
			validateNumMin = request.getParameter("validateNumMin");
			validateDateAllowTime = request.getParameter("validateDateAllowTime");
			validateTextMinLen = request.getParameter("validateTextMinLen");
			validateTextMaxLen = request.getParameter("validateTextMaxLen");
			validateTextRegex = request.getParameter("validateTextRegex");
			validateTextRegexMsg = request.getParameter("validateTextRegexMsg");
			
			//Set the Account Attribute HDR
			accountAttrHdr = new AccountAttributeHDR();
			accountAttrHdr.getId().setAttributeId(attributeId);
			accountAttrHdr.setAttributeName(attributeName);
			accountAttrHdr.setTooltipText(tooltipText);
			accountAttrHdr.setEffectiveDateRequired(effectiveDateRequired);
			accountAttrHdr.setEffectiveDateAllowTime(effectiveDateAllowTime);
			
			if(SwtUtil.getMessage(SwtConstants.NUMERIC_TYPE, request).equalsIgnoreCase(valueType)){
				accountAttrHdr.setValueType("N");
				if(!SwtUtil.isEmptyOrNull(validateNumMax)){
					accountAttrHdr.setValidateNumMax(new BigDecimal(validateNumMax));
				}
				if(!SwtUtil.isEmptyOrNull(validateNumMin)){
					accountAttrHdr.setValidateNumMin(new BigDecimal(validateNumMin));
				}
			}else if (SwtUtil.getMessage(SwtConstants.DATE_TYPE, request).equalsIgnoreCase(valueType)){
				accountAttrHdr.setValueType("D");
				accountAttrHdr.setValidateDateAllowTime(validateDateAllowTime);
			}else{
				accountAttrHdr.setValueType("T");
				accountAttrHdr.setValidateTextRegex(validateTextRegex);
				accountAttrHdr.setValidateTextRegexMsg(validateTextRegexMsg);
				if(!SwtUtil.isEmptyOrNull(validateTextMinLen)){
					accountAttrHdr.setValidateTextMinLen(new BigDecimal(validateTextMinLen));
				}
				if(!SwtUtil.isEmptyOrNull(validateTextMaxLen)){
					accountAttrHdr.setValidateTextMaxLen(new BigDecimal(validateTextMaxLen));
				}
			}
			
			accountAttrHdr.setUpdateUser(updateUser);
			accountAttrHdr.setUpdateDate(SwtUtil.getSysParamDate());
			accountAttrHdr.setSystemFlag("N");
			
			String screen = request.getParameter("screenName");
			if("addScreen".equalsIgnoreCase(screen)){
				accountAttributeMaintenanceManager.saveAccountAttributeHDR(accountAttrHdr);
			}else{
				accountAttributeMaintenanceManager.updateAccountAttributeHDR(accountAttrHdr);
			}
			
			request.setAttribute("menuAccessId", request
					.getParameter("menuAccessId"));
			request.setAttribute("reply_status_ok", SwtConstants.STR_TRUE);
			request.setAttribute("reply_message", "Data fetch OK");
			
			log.debug(this.getClass().getName() + " method [saveAccountAttributeHDR] - Exit ");
	
		}catch (SwtException swtExp) {
				log.error(this.getClass().getName()
						+ " - Exception Catched in [saveAccountAttributeHDR] method : - "
						+ swtExp.getMessage());
				saveErrors(request, SwtUtil.logException(swtExp, request, ""));
				if (swtExp.getErrorCode().equals(
						"errors.DataIntegrityViolationExceptioninAdd")) {
					if (errors != null) {
						errors.add("", new ActionMessage(swtExp.getErrorCode()));
					}
					saveErrors(request, errors);
				} else {
					log.error(this.getClass().getName()
							+ " - Exception Catched in [saveAccountAttributeHDR] method swt : - "
							+ swtExp.getMessage());
					saveErrors(request, SwtUtil.logException(swtExp, request, ""));
				}

				request.setAttribute("reply_status_ok", SwtConstants.STR_FALSE);
				request.setAttribute("reply_message", swtExp.getMessage());
				request.setAttribute("reply_location",swtExp.getStackTrace()[0].getClassName() + "."
								+ swtExp.getStackTrace()[0].getMethodName() + ":"
								+ swtExp.getStackTrace()[0].getLineNumber());
				return ("dataerror");
	
		} catch (Exception e) {
			log.error(this.getClass().getName()
					+ " - [saveAccountAttributeHDR] - Exception -"
					+ e.getStackTrace()[0].getClassName() + "."
					+ e.getStackTrace()[0].getMethodName() + ":"
					+ e.getStackTrace()[0].getLineNumber() + " "
					+ e.getMessage());
			request.setAttribute("reply_status_ok", SwtConstants.STR_FALSE);
			request.setAttribute("reply_message", e.getMessage());
			request.setAttribute("reply_location", e.getStackTrace()[0]
					.getClassName()
					+ "."
					+ e.getStackTrace()[0].getMethodName()
					+ ":"
					+ e.getStackTrace()[0].getLineNumber());
			
			return ("dataerror");
		}
		
		return ("save");
	}
	/**
	 * Used to save or update an account attribute value according to the passed methodname parameter 
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return ActionForward
	 * @throws SwtException
	 */
	public String saveAccountAttributeValue() throws SwtException {
HttpServletRequest request = ServletActionContext.getRequest();
HttpServletResponse response = ServletActionContext.getResponse();

		
		// Variables to holds passed params
		String entityId = null;
		String accountId = null;
		String attributeId = null;
		String effectiveDate = null;
		String valueData = null;
		String selectedType = null;
		String updateUser = null;
		String method = null;
		String seqKey = null;
		//Variable to hold error message if any exception occurs
		ActionErrors errors = null;
		AccountAttribute acctAttribute = null;
		try {
			log.debug(this.getClass().getName() + " method [saveAccountAttributeValue] - Enter ");
			errors = new ActionErrors();
			updateUser = SwtUtil.getCurrentUserId(request.getSession());
			// Get values from request
			entityId = request.getParameter("entityId");
			accountId = request.getParameter("accountId");
			attributeId = request.getParameter("attributeId");
			effectiveDate = request.getParameter("effectiveDate");
			valueData = request.getParameter("valueData");
			selectedType = request.getParameter("selectedType");
			seqKey = request.getParameter("seqKey");
			method = request.getParameter("methodName");
			// Create an account attribute object according to the passed params
			acctAttribute = new AccountAttribute();
			acctAttribute.setEntityId(entityId);
			acctAttribute.setHostId(SwtUtil.getCurrentHostId());
			acctAttribute.setAccountId(accountId);
			acctAttribute.setAttributeId(attributeId);
			// Format passed effective date and cast it as "Date"
			if(!SwtUtil.isEmptyOrNull(effectiveDate))
				acctAttribute.setEffectiveDate(SwtUtil.parseDate(effectiveDate, SwtUtil.getCurrentDateFormat(request.getSession())+" HH:mm:ss"));
			// Depending of the attribute type(Text, Numeric or date) set the value on the right variable (StrValue, NumValue or DateValue)
			if(SwtUtil.getMessage(SwtConstants.NUMERIC_TYPE, request).equalsIgnoreCase(selectedType)){
				acctAttribute.setNumValue(new BigDecimal(valueData));
			}else if (SwtUtil.getMessage(SwtConstants.DATE_TYPE, request).equalsIgnoreCase(selectedType)){
				acctAttribute.setDateValue(SwtUtil.parseDate(valueData, SwtUtil.getCurrentDateFormat(request.getSession())+" HH:mm:ss"));
			}else{
				acctAttribute.setStrValue(valueData);
			}
			//Set the user id and the update date 
			acctAttribute.setUpdateUser(updateUser);
			acctAttribute.setUpdateDate(SwtUtil.getSysParamDate());
			
			/* If method name is "add" then create a new account attribute with generating a new sequence number (done by hibernate), otherwise 
			   use the passed sequence number */
			if("add".equalsIgnoreCase(method)){
				accountAttributeMaintenanceManager.saveAcctAttribute(acctAttribute);
			}else{
				acctAttribute.setSequenceKey(Long.parseLong(seqKey));
				accountAttributeMaintenanceManager.updateAcctAttribute(acctAttribute);
			}
			
			request.setAttribute("menuAccessId", request
					.getParameter("menuAccessId"));
			request.setAttribute("reply_status_ok", SwtConstants.STR_TRUE);
			request.setAttribute("reply_message", "Data fetch OK");
			
			log.debug(this.getClass().getName() + " method [saveAccountAttributeValue] - Exit ");
			
		}catch (SwtException swtExp) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [saveAccountAttributeValue] method : - "
					+ swtExp.getMessage());
			saveErrors(request, SwtUtil.logException(swtExp, request, ""));
			if (swtExp.getErrorCode().equals(
					"errors.DataIntegrityViolationExceptioninAdd")) {
				//send exception code to client
				if (errors != null) {
					errors.add("", new ActionMessage(swtExp.getErrorCode()));
				}
				saveErrors(request, errors);
			} else {
				log.error(this.getClass().getName()
						+ " - Exception Catched in [saveAccountAttributeValue] method swt : - "
						+ swtExp.getMessage());
				saveErrors(request, SwtUtil.logException(swtExp, request, ""));
			}
		
			request.setAttribute("reply_status_ok", SwtConstants.STR_FALSE);
			request.setAttribute("reply_message", swtExp.getMessage());
			request.setAttribute(
					"reply_location",
					swtExp.getStackTrace()[0].getClassName() + "."
							+ swtExp.getStackTrace()[0].getMethodName() + ":"
							+ swtExp.getStackTrace()[0].getLineNumber());
			return ("dataerror");
			
		} catch (Exception e) {
			if (e instanceof DataIntegrityViolationException){
				log.error(this.getClass().getName()
						+ " - Exception Catched in [saveAccountAttributeValue] method : - "
						+ e.getMessage());
				//send exception code to client
				if (errors != null) {
					errors.add("", new ActionMessage("errors.DataIntegrityViolationExceptioninChange"));
				}
				saveErrors(request, errors);
				
				request.setAttribute("reply_status_ok", SwtConstants.STR_FALSE);
				request.setAttribute("reply_message", "errors.DataIntegrityViolationExceptioninChange");
				request.setAttribute(
						"reply_location",
						e.getStackTrace()[0].getClassName() + "."
								+ e.getStackTrace()[0].getMethodName() + ":"
								+ e.getStackTrace()[0].getLineNumber());
				return ("dataerror");
			} else {
				log.error(this.getClass().getName()
					+ " - [saveAccountAttributeValue] - Exception -"
					+ e.getStackTrace()[0].getClassName() + "."
					+ e.getStackTrace()[0].getMethodName() + ":"
					+ e.getStackTrace()[0].getLineNumber() + " "
					+ e.getMessage());
				request.setAttribute("reply_status_ok", SwtConstants.STR_FALSE);
				request.setAttribute("reply_message", e.getMessage());
				request.setAttribute("reply_location", e.getStackTrace()[0]
					.getClassName()
					+ "."
					+ e.getStackTrace()[0].getMethodName()
					+ ":"
					+ e.getStackTrace()[0].getLineNumber());
			
				return ("fail");
			}
		}
		
		return ("saveAccountValue");
	}
	
	/**
	 *  Used To delete an account attribute HDR
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return
	 * @throws SwtException
	 */
	public String deleteAccountAttributeHDR() throws SwtException {
HttpServletRequest request = ServletActionContext.getRequest();
HttpServletResponse response = ServletActionContext.getResponse();

		String attributeId = null;
		String totalDelete = null;
		boolean isCascade = false;
		AccountAttributeHDR accountAttrHDR = null;
		try {
			log.debug(this.getClass().getName() + " method [displayAddAccountAttributeHDR] - Enter ");
	
			attributeId = request.getParameter("attributeId");
			totalDelete = request.getParameter("totalDelete");
			//Fetch the required account attribute HDR
			accountAttrHDR = accountAttributeMaintenanceManager.getAccountAttributeHDR(attributeId);
			isCascade = totalDelete.equalsIgnoreCase("true");
			//delete the account attribute HDR
			accountAttributeMaintenanceManager.deleteAccountAttributeHDR(accountAttrHDR,isCascade);
			request.setAttribute("menuAccessId", request
					.getParameter("menuAccessId"));
			request.setAttribute("reply_status_ok", SwtConstants.STR_TRUE);
			request.setAttribute("reply_message", "Data fetch OK");
			log.debug(this.getClass().getName() + " method [displayAddAccountAttributeHDR] - Exit ");
		} catch (Exception e) {
			log.error(this.getClass().getName()
					+ " - [deleteAccountAttributeHDR] - Exception -"
					+ e.getStackTrace()[0].getClassName() + "."
					+ e.getStackTrace()[0].getMethodName() + ":"
					+ e.getStackTrace()[0].getLineNumber() + " "
					+ e.getMessage());
			request.setAttribute("reply_status_ok", SwtConstants.STR_FALSE);
			request.setAttribute("reply_message", e.getMessage());
			request.setAttribute("reply_location", e.getStackTrace()[0]
					.getClassName()
					+ "."
					+ e.getStackTrace()[0].getMethodName()
					+ ":"
					+ e.getStackTrace()[0].getLineNumber());
			
			return ("fail");
		}
		
		return displayAcctAttributeDefinition();
	}
	
	/**
	 * Called to load the SWF file for the attribute usage summary screen
	 * 
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return
	 * @throws SwtException
	 */
	public String attributeUsageSummary()
			throws SwtException {
HttpServletRequest request = ServletActionContext.getRequest();
HttpServletResponse response = ServletActionContext.getResponse();

		log.debug(this.getClass().getName() + " - [attributeUsageSummary] - Enter/Exit ");
		request.setAttribute("method", "attributeUsageSummary");
		request.setAttribute("menuAccessId", request
				.getParameter("menuAccessId"));
		return ("attributeUsageSummaryFlex");
	}
	
	/**
	 * Display the attribute usage summary screen with its data
	 * 
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return ActionForward
	 * @throws SwtException
	 */
	public String displayAttributeUsageSummary()
			throws SwtException {
HttpServletRequest request = ServletActionContext.getRequest();
HttpServletResponse response = ServletActionContext.getResponse();

		
		ArrayList<AccountAttributeFuncGroup> acctAttrFunGroupList = null;
		String functionalGrp = null;
		String restriction = null;
		ArrayList<FunctionalGroup> functionalGrpList = null;
		
		try {
			log.debug(this.getClass().getName() + "- [displayAttributeUsageSummary] - Enter ");
			functionalGrp = request.getParameter("functionalGrp");
			// Init the arraylists
			acctAttrFunGroupList = new ArrayList<AccountAttributeFuncGroup>();
			functionalGrpList = new ArrayList<FunctionalGroup>();
			
			// Get functional group list with details
			functionalGrpList = accountAttributeMaintenanceManager.getFunctionalGrpList();
			
			if (SwtUtil.isEmptyOrNull(functionalGrp)) {
				// Set functional group element to be selected in the screen
				if (functionalGrpList.size() != 0){
					// If the returned list is not empty then set the first element
					functionalGrp = functionalGrpList.get(0).getId().getFunctionalGroup();
					restriction = functionalGrpList.get(0).getRestrictType();
					// Get the account attribute function group list with details
					acctAttrFunGroupList = accountAttributeMaintenanceManager.getAccountAttributeFuncGroupList(functionalGrp, restriction);
					request.setAttribute("selectedFunctionalGrp", functionalGrp);
				} else {
					restriction = "";
				}
			} else {
				// Set the restriction value if it exists
				for(Iterator<FunctionalGroup> fgIt = functionalGrpList.iterator() ; fgIt.hasNext() ;) {
					// Get the Functional Group Object 
					FunctionalGroup functGrpObject = fgIt.next();
					if (functGrpObject.getId().getFunctionalGroup().equals(functionalGrp)) {
						// if restriction is not null then set the exact value
						if (!SwtUtil.isEmptyOrNull(functGrpObject.getRestrictType())) {
							restriction = functGrpObject.getRestrictType();
						} else {
							restriction = "";
						}
					}
				}
				// Get the account attribute function group list with details
				acctAttrFunGroupList = accountAttributeMaintenanceManager.getAccountAttributeFuncGroupList(functionalGrp, restriction);
				request.setAttribute("selectedFunctionalGrp", functionalGrp);
			}
			
			request.setAttribute("functionalGrpList", functionalGrpList);
			request.setAttribute("acctAttrFunGroupList", acctAttrFunGroupList);
			request.setAttribute("restriction", restriction);
			request.setAttribute("rowSize", acctAttrFunGroupList != null ? acctAttrFunGroupList.size() : "0");
			request.setAttribute("reply_status_ok", SwtConstants.STR_TRUE);
			request.setAttribute("reply_message", "Data fetch OK");
			bindColumnWidthInRequest(request, "" + SwtConstants.SCREEN_ACCOUNT_ATTRIBUTES_USAGE);
			bindColumnOrderInRequest(request,"" + SwtConstants.SCREEN_ACCOUNT_ATTRIBUTES_USAGE);
			log.debug(this.getClass().getName() + "- [displayAttributeUsageSummary] - Exit ");
		} catch (SwtException swtexp) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [displayAttributeUsageSummary] method : - "
					+ swtexp.getMessage());
			request.setAttribute("reply_status_ok", SwtConstants.STR_FALSE);
			request.setAttribute("reply_message", swtexp.getMessage());
			request.setAttribute("reply_location", swtexp.getStackTrace()[0]
					.getClassName() + "." + swtexp.getStackTrace()[0].getMethodName() + ":" + swtexp.getStackTrace()[0].getLineNumber());
			SwtUtil.logException(
					SwtErrorHandler.getInstance().handleException(swtexp,
							"displayAttributeUsageSummary",
							AccountAttributeMaintenanceAction.class), request, "");
			return ("dataerror");
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [displayAttributeUsageSummary] method : - "
					+ exp.getMessage());
			request.setAttribute("reply_status_ok", SwtConstants.STR_FALSE);
			request.setAttribute("reply_message", exp.getMessage());
			request.setAttribute("reply_location", exp.getStackTrace()[0]
					.getClassName() + "." + exp.getStackTrace()[0].getMethodName() + ":" + exp.getStackTrace()[0].getLineNumber());
			SwtUtil.logException(
					SwtErrorHandler.getInstance().handleException(exp,
							"displayAttributeUsageSummary",
							AccountAttributeMaintenanceAction.class), request, "");
			return ("dataerror");
		} finally {
			acctAttrFunGroupList = null;
			functionalGrp = null;
			restriction = null;
			functionalGrpList = null;
		}
		return ("attributeUsageSummaryFlexData");
	}
	
	/**
	 * Delete a selected attribute summary usage definition
	 * 
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return
	 * @throws SwtException
	 */
	public String deleteAttributeUsageAttribute()
					throws SwtException {
HttpServletRequest request = ServletActionContext.getRequest();
HttpServletResponse response = ServletActionContext.getResponse();

		
		ArrayList<AccountAttributeFuncGroup> acctAttrFunGroupList = null;
		String functionalGrp = null;
		String attributeId = null;
		String restriction = null;
		ArrayList<FunctionalGroup> functionalGrpList = null;
		AccountAttributeFuncGroup accntAttributeFuncGrp = null;
		
		try {
			log.debug(this.getClass().getName() + "- [deleteAttributeUsageAttribute] - starting ");

			// Retrieve attribute ID, functional group and restriction from the request
			attributeId = request.getParameter("attributeId");
			functionalGrp = request.getParameter("functionalGrp");
			restriction = request.getParameter("restriction");
			
			// Get the unique account attribute function group object to be deleted
			accntAttributeFuncGrp = accountAttributeMaintenanceManager.getAccntAttributeFunctGroup(attributeId, functionalGrp);
			
			// Delete the account attribute function group object in question
			accountAttributeMaintenanceManager.deleteAccntAttributeFunctionalGrp(accntAttributeFuncGrp);
			
			// Get the functional group list with details
			// We have to rebuild the data to display correct data in the screen after deleting the element
			functionalGrpList = accountAttributeMaintenanceManager.getFunctionalGrpList();
			
			// Get the account attribute functional group list with details
			acctAttrFunGroupList = accountAttributeMaintenanceManager.getAccountAttributeFuncGroupList(functionalGrp, restriction);

			request.setAttribute("functionalGrpList", functionalGrpList);
			request.setAttribute("acctAttrFunGroupList", acctAttrFunGroupList);
			request.setAttribute("restriction", restriction);
			request.setAttribute("rowSize", acctAttrFunGroupList != null ? acctAttrFunGroupList.size() : "0");
			request.setAttribute("reply_status_ok", SwtConstants.STR_TRUE);
			request.setAttribute("reply_message", "Data fetch OK");
			bindColumnWidthInRequest(request, "" + SwtConstants.SCREEN_ACCOUNT_ATTRIBUTES_USAGE);
			bindColumnOrderInRequest(request,"" + SwtConstants.SCREEN_ACCOUNT_ATTRIBUTES_USAGE);
            
			log.debug(this.getClass().getName() + "- [deleteAttributeUsageAttribute] - Exit ");
		} catch (SwtException swtexp) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [deleteAttributeUsageAttribute] method : - "
					+ swtexp.getMessage());
			request.setAttribute("reply_status_ok", SwtConstants.STR_FALSE);
			request.setAttribute("reply_message", swtexp.getMessage());
			request.setAttribute("reply_location", swtexp.getStackTrace()[0]
					.getClassName() + "." + swtexp.getStackTrace()[0].getMethodName() + ":" + swtexp.getStackTrace()[0].getLineNumber());
			SwtUtil.logException(
					SwtErrorHandler.getInstance().handleException(swtexp,
							"deleteAttributeUsageAttribute",
							AccountAttributeMaintenanceAction.class), request, "");
			return ("dataerror");
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [deleteAttributeUsageAttribute] method : - "
					+ exp.getMessage());
			request.setAttribute("reply_status_ok", SwtConstants.STR_FALSE);
			request.setAttribute("reply_message", exp.getMessage());
			request.setAttribute("reply_location", exp.getStackTrace()[0]
					.getClassName() + "." + exp.getStackTrace()[0].getMethodName() + ":" + exp.getStackTrace()[0].getLineNumber());
			SwtUtil.logException(
					SwtErrorHandler.getInstance().handleException(exp,
							"deleteAttributeUsageAttribute",
							AccountAttributeMaintenanceAction.class), request, "");
			return ("dataerror");
		} finally {
			acctAttrFunGroupList = null;
			functionalGrp = null;
			attributeId = null;
			restriction = null;
			functionalGrpList = null;
			accntAttributeFuncGrp = null;
		}
		return ("attributeUsageSummaryFlexData");
	}
	
	/**
	 * Load the SWF file for the add/change attribute usage summary screen 
	 * 
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return ActionForward
	 * @throws SwtException
	 */
	public String addChangeAttributeUsageSummary()
					throws SwtException {
HttpServletRequest request = ServletActionContext.getRequest();
HttpServletResponse response = ServletActionContext.getResponse();

		
		log.debug(this.getClass().getName() + " - [addChangeAttributeUsageSummary] - Enter/Exit ");
		request.setAttribute("calledFrom", request.getParameter("calledFrom"));
		request.setAttribute("functionalGrp", request.getParameter("functionalGrp"));
		request.setAttribute("attributeId", request.getParameter("attributeId"));
		
		return ("attributeUsageSummaryFlexAdd");
			
			
	}
	
	/**
	 * Display The Add/change attribute usage summary screen with its data
	 * 
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return
	 * @throws SwtException
	 */
	public String displayAttributeUsageSummaryAdd()
					throws SwtException {
HttpServletRequest request = ServletActionContext.getRequest();
HttpServletResponse response = ServletActionContext.getResponse();

		
		String functionalGrp = null;
		String attributeId = null;
		String restriction = null;
		Collection<AccountAttributeHDR> acctAttributHDRList = null;
		ArrayList<FunctionalGroup> functionalGrpList = null;
		AccountAttributeFuncGroup accntAttributeFuncGrp = null;
		String displayOrder = null;
		String grandTotal = null;
		
		try {
			log.debug(this.getClass().getName() + "- [displayAttributeUsageSummaryAdd] - Start ");
			
			attributeId = request.getParameter("attributeId");
			functionalGrp = request.getParameter("functionalGrp");
			
			// Get the functional group list
			functionalGrpList = accountAttributeMaintenanceManager.getFunctionalGrpList();

			// Set the restriction value if it exists
			for(Iterator<FunctionalGroup> fgIt = functionalGrpList.iterator() ; fgIt.hasNext() ;) {
				// Get the Functional Group Object 
				FunctionalGroup functGrpObject = fgIt.next();
				if (functGrpObject.getId().getFunctionalGroup().equals(functionalGrp)) {
					// If restriction is not null then set the exact value
					if (!SwtUtil.isEmptyOrNull(functGrpObject.getRestrictType())) {
						restriction = functGrpObject.getRestrictType();
					} else {
						restriction = "";
					}
				}
			}
			
			// Get the account attribute header list
			acctAttributHDRList = accountAttributeMaintenanceManager.getAcctAttributHDRDetailList(restriction);
			
			// Set the display order value if it exists. In fact, it depends on attribute Id value
			if (!SwtUtil.isEmptyOrNull(attributeId)) {
				accntAttributeFuncGrp = accountAttributeMaintenanceManager.getAccntAttributeFunctGroup(attributeId, functionalGrp);
				if (accntAttributeFuncGrp.getDisplayOrder() != null) {
					displayOrder = accntAttributeFuncGrp.getDisplayOrder().toString();
				} else {
					displayOrder = "";
				}
				grandTotal = accntAttributeFuncGrp.getContributeTotal();
			} else {
				if (acctAttributHDRList.size() != 0) {
					attributeId = acctAttributHDRList.iterator().next().getId().getAttributeId();
				} else {
					attributeId = "";
				}
			}
						
			// Set all the attributes required in the JSP file
			request.setAttribute("functionalGrpList", functionalGrpList);
			request.setAttribute("acctAttributHDRList", acctAttributHDRList);
			request.setAttribute("functionalGrp", functionalGrp);
			request.setAttribute("attributeId", attributeId);
			request.setAttribute("displayOrder", displayOrder);
			request.setAttribute("grandTotal", grandTotal);
			request.setAttribute("restriction", restriction);
			request.setAttribute("reply_status_ok", SwtConstants.STR_TRUE);
			request.setAttribute("reply_message", "Data fetch OK");
			
			log.debug(this.getClass().getName() + "- [displayAttributeUsageSummaryAdd] - Exit ");
		} catch (SwtException swtexp) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [displayAttributeUsageSummaryAdd] method : - "
					+ swtexp.getMessage());
			request.setAttribute("reply_status_ok", SwtConstants.STR_FALSE);
			request.setAttribute("reply_message", swtexp.getMessage());
			request.setAttribute("reply_location", swtexp.getStackTrace()[0]
					.getClassName() + "." + swtexp.getStackTrace()[0].getMethodName() + ":" + swtexp.getStackTrace()[0].getLineNumber());
			SwtUtil.logException(
					SwtErrorHandler.getInstance().handleException(swtexp,
							"displayAttributeUsageSummaryAdd",
							AccountAttributeMaintenanceAction.class), request, "");
			return ("dataerror");
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [displayAttributeUsageSummaryAdd] method : - "
					+ exp.getMessage());
			request.setAttribute("reply_status_ok", SwtConstants.STR_FALSE);
			request.setAttribute("reply_message", exp.getMessage());
			request.setAttribute("reply_location", exp.getStackTrace()[0]
					.getClassName() + "." + exp.getStackTrace()[0].getMethodName() + ":" + exp.getStackTrace()[0].getLineNumber());
			SwtUtil.logException(
					SwtErrorHandler.getInstance().handleException(exp,
							"displayAttributeUsageSummaryAdd",
							AccountAttributeMaintenanceAction.class), request, "");
			return ("dataerror");
		} finally {
			functionalGrp = null;
			attributeId = null;
			restriction = null;
			acctAttributHDRList = null;
			functionalGrpList = null;
			accntAttributeFuncGrp = null;
			displayOrder = null;
			grandTotal = null;
		}
		return ("attributeUsageSummaryFlexAddData");
	}
	
	/**
	 * Save Attribute usage summary details
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return ActionForward
	 * @throws SwtException
	 */
	public String saveAttributeUsageSummary()
					throws SwtException {
HttpServletRequest request = ServletActionContext.getRequest();
HttpServletResponse response = ServletActionContext.getResponse();

		
		String functionalGrp = null;
		String attributeId = null;
		String displayOrder = null;
		AccountAttributeFuncGroup acctAttrFuncGrp;  
		String calledFrom = null;
		String grandTotal = null;
		
		try {
			log.debug(this.getClass().getName() + "- [saveAttributeUsageSummary] - Start ");
			
			attributeId = request.getParameter("attributeId");
			functionalGrp = request.getParameter("functionalGrp");
			displayOrder = request.getParameter("displayOrder");
			calledFrom = request.getParameter("calledFrom");
			grandTotal = request.getParameter("grandTotal");
			if(SwtUtil.isEmptyOrNull(grandTotal)){
				grandTotal = "PLUS";
			}
			acctAttrFuncGrp = new AccountAttributeFuncGroup();
			
			acctAttrFuncGrp.getId().setAttributeId(attributeId);
			acctAttrFuncGrp.getId().setFunctionalGroup(functionalGrp);
			acctAttrFuncGrp.setContributeTotal(grandTotal);
			
			
			if (!SwtUtil.isEmptyOrNull(displayOrder))
				acctAttrFuncGrp.setDisplayOrder(Integer.parseInt(displayOrder));
			
			if (("change").equals(calledFrom)) {
				accountAttributeMaintenanceManager.updateAttributeUsageSummary(acctAttrFuncGrp);
			} else {
				accountAttributeMaintenanceManager.saveAttributeUsageSummary(acctAttrFuncGrp);
			}
			
			// Set all the attributes required in the JSP file
			request.setAttribute("reply_status_ok", SwtConstants.STR_TRUE);
			request.setAttribute("reply_message", "Data fetch OK");
			
			log.debug(this.getClass().getName() + "- [saveAttributeUsageSummary] - Exit");
		} catch (SwtException swtexp) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [saveAttributeUsageSummary] method : - "
					+ swtexp.getMessage());
			request.setAttribute("reply_status_ok", SwtConstants.STR_FALSE);
			request.setAttribute("reply_message", swtexp.getMessage());
			request.setAttribute("reply_location", swtexp.getStackTrace()[0]
					.getClassName() + "." + swtexp.getStackTrace()[0].getMethodName() + ":" + swtexp.getStackTrace()[0].getLineNumber());
			SwtUtil.logException(
					SwtErrorHandler.getInstance().handleException(swtexp,
							"saveAttributeUsageSummary",
							AccountAttributeMaintenanceAction.class), request, "");
			return ("dataerror");
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [saveAttributeUsageSummary] method : - "
					+ exp.getMessage());
			request.setAttribute("reply_status_ok", SwtConstants.STR_FALSE);
			request.setAttribute("reply_message", exp.getMessage());
			request.setAttribute("reply_location", exp.getStackTrace()[0]
					.getClassName() + "." + exp.getStackTrace()[0].getMethodName() + ":" + exp.getStackTrace()[0].getLineNumber());
			SwtUtil.logException(
					SwtErrorHandler.getInstance().handleException(exp,
							"saveAttributeUsageSummary",
							AccountAttributeMaintenanceAction.class), request, "");
			return ("dataerror");
		} finally {
			functionalGrp = null;
			attributeId = null;
			displayOrder = null;
			grandTotal = null;
		}
		return ("save");
	}
	
	
	/**
	 * This method is called to open the account group details screen
	 * 
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return actionForward
	 */
	public String accountAttributesMaintenance() {
HttpServletRequest request = ServletActionContext.getRequest();
HttpServletResponse response = ServletActionContext.getResponse();

		
		//Variables to hold passed params
		String entityId = null;
		String currencyCode = null;
		String attributeId = null;
		String effectiveDate = null;
		String dateFrom = null;
		String accountId = null;
		Date sysDate = null;
		String sysDateAsString = null;
		String parentScreen = null;
		
		try {
			log.debug(this.getClass().getName() + " - [accountAttributesMaintenance] - " + "Enter");
			
			//get passed params
			entityId = request.getParameter("entityId");
			accountId = request.getParameter("accountId");
			attributeId = request.getParameter("attributeId");
			effectiveDate = request.getParameter("effectiveDate");
			parentScreen = request.getParameter("parentScreen");
			currencyCode = request.getParameter("currencyCode");
			if(SwtUtil.isEmptyOrNull(entityId))
				entityId = SwtUtil.getUserCurrentEntity(UserThreadLocalHolder
							.getUserSession());
			// get the system date from swtutil
			sysDate = SwtUtil.getSystemDatewithoutTime();
			// get the formatted system date form swtutil
			sysDateAsString = SwtUtil.formatDate(sysDate, SwtUtil
					.getCurrentSystemFormats(request.getSession())
					.getDateFormatValue());
			// Getting the currency code
			if(!SwtUtil.isEmptyOrNull(effectiveDate))
				dateFrom = effectiveDate.split(" ")[0];
			if(SwtUtil.isEmptyOrNull(currencyCode))
				currencyCode = SwtUtil.getDomesticCurrencyForUser(request,
							SwtUtil.getCurrentHostId(), entityId);
			//send in request the passed params
			request.setAttribute("method", "accountAttributesMaintenance");
			request.setAttribute("sysDateAsString", sysDateAsString);
 			request.setAttribute("entityId", entityId);
			request.setAttribute("currencyCode", currencyCode);
			request.setAttribute("attributeId", attributeId);
			request.setAttribute("dateFrom", dateFrom);
			request.setAttribute("dateTo", dateFrom);
			request.setAttribute("effectiveDate", effectiveDate);
			request.setAttribute("menuAccessId", request
					.getParameter("menuAccessId"));
			request.setAttribute("accountId", accountId);
			request.setAttribute("parentScreen", parentScreen);
			return ("accountAttrMaintenance");
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [accountAttributesMaintenance] method : - "
					+ exp.getMessage());

			request.setAttribute("reply_status_ok", SwtConstants.STR_FALSE);
			request.setAttribute("reply_message", exp.getMessage());
			request.setAttribute(
					"reply_location",
					exp.getStackTrace()[0].getClassName() + "." 
							+ exp.getStackTrace()[0].getMethodName() + ":"
							+ exp.getStackTrace()[0].getLineNumber());

			SwtUtil.logException(
					SwtErrorHandler.getInstance().handleException(exp,
							"accountAttributesMaintenance",
							AccountAttributeMaintenanceAction.class), request, "");
			return ("fail");
		}
	}
	
	
	/**
	 * This method is used to open account attributes maintenance screen
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return
	 * @throws SwtException
	 */
	public String displayAccountAttributes()
					throws SwtException {
HttpServletRequest request = ServletActionContext.getRequest();
HttpServletResponse response = ServletActionContext.getResponse();

		
		//Variables to hold passed params
		String hostId = null;
		String entityId = null;
		String currencyCode = null;
		String accountId = null;
		String attributeId = null;
		String resetDate = null;
		//Variable to store currency list depending of user role
		ArrayList<LabelValueBean> currencyList = null;
		//Variable to hold account attribute list result
		Collection<AccountAttribute> accountAttributes = null;
		AccountAttribute account = null;
		//Hold attribute header
		AccountAttributeHDR accountHDR = null;
		Collection acctAttributesHDR = null;
		int accessInd;
		
		try {
			log.debug(this.getClass().getName() + " - [displayAccountAttributes] - Enter");
			opTimer.start("all");
			// Retrieve passed params
			hostId = SwtUtil.getCurrentHostId();
			entityId = request.getParameter("entityId");
			currencyCode = request.getParameter("currencyCode");
			accountId = request.getParameter("accountId");
			attributeId = request.getParameter("attributeId");
			resetDate = request.getParameter("resetDate");
		
			// Getting the hostId
			if (SwtUtil.isEmptyOrNull(hostId)) {
				hostId = SwtUtil.getCurrentHostId();
			}
			// Getting the user session
			if (SwtUtil.isEmptyOrNull(entityId) ) {
				entityId = SwtUtil.getUserCurrentEntity(UserThreadLocalHolder
						.getUserSession());
			}
				// Put currency list in request
 			currencyList = (ArrayList) getCurrencyList(request, entityId, true);
                        //check if the currency got from the retrieved params found in currencyList when changing entityID
 			if((currencyList.contains(new LabelValueBean("", currencyCode)))==false || SwtUtil.isEmptyOrNull(currencyCode ))
			{
				currencyCode = SwtUtil.getDomesticCurrencyForUser(request,
						SwtUtil.getCurrentHostId(), entityId);
			}
			// Get the accounts for the account id dropdown.
			Collection<LabelValueBean> accountsList = accountAttributeMaintenanceManager
					.getAccountList(hostId, entityId, currencyCode);
			// Set account id to all if no one is selected in the dropdown or when selecting a new an entity or a currency from list
			if (SwtUtil.isEmptyOrNull(accountId) ) {
				accountId = "All";
			}
			// Put account list in request
			putAccountListInRequest(request, accountsList, true);
			// Put Entity list in request depending of the use role
			putEntityListInReq(request, true);
			acctAttributesHDR = putAttributeListInReq(request, false);
			// Set the access index for the screen in order to set the behavior of the add/change/delete button related to entity and currency IDs.
 			// If 0 then without any restriction, the user could only user the screen in view mode 
 			accessInd = SwtUtil.getMenuEntityCurrGrpAccess(request, entityId, currencyCode);
			// Put result in request and forward it to the related JSP file
 			request.setAttribute("accessInd", accessInd);
			request.setAttribute("currencies", currencyList);
			
			if(!SwtUtil.isEmptyOrNull(resetDate) && resetDate.equals("true")) {
				request.setAttribute("fromDate", SwtUtil.formatDate(SwtUtil
						.getSysParamDateWithEntityOffset(entityId), SwtUtil
						.getCurrentSystemFormats(request.getSession())
						.getDateFormatValue()));
			}else {
				request.setAttribute("fromDate", "");
			}

			accountAttributes = new ArrayList<AccountAttribute>();
			request.setAttribute("accountAttibutes", accountAttributes);
			request.setAttribute("selectedAccount", accountId);
			request.setAttribute("selectedAttribute", attributeId);
			request.setAttribute("showGridcontent", "false");
			request.setAttribute("attributeEffectiveDateRequired", ((accountHDR!=null && !SwtUtil.isEmptyOrNull(accountHDR.getEffectiveDateRequired()))?accountHDR.getEffectiveDateRequired():""));
			request.setAttribute("entityId", entityId);
			request.setAttribute("currencyCode", currencyCode);
			request.setAttribute("recordCount", accountAttributes.size());
			request.setAttribute("reply_status_ok", SwtConstants.STR_TRUE);
			request.setAttribute("reply_message", "Data fetch OK");
			// Set column width with request attribute
			bindColumnOrderInRequest(request, "" + SwtConstants.SCREEN_ACCOUNT_ATTRIBUTES_VALUE);
			bindColumnWidthInRequest(request, "" + SwtConstants.SCREEN_ACCOUNT_ATTRIBUTES_VALUE);
			opTimer.stop("all");
			// Set the attribute for opTimes,lastRefTime in request
			request.setAttribute("opTimes", opTimer.getOpTimes());
			log.debug(this.getClass().getName() + " - [displayAccountAttributes] - Exit");
			return ("accountAttrMaintenanceData");
		} catch (SwtException swtExp) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [displayAccountAttributes] method : - "
					+ swtExp.getMessage());
			request.setAttribute("reply_status_ok", SwtConstants.STR_FALSE);
			request.setAttribute("reply_message", swtExp.getMessage());
			request.setAttribute(
					"reply_location",
					swtExp.getStackTrace()[0].getClassName() + "."
							+ swtExp.getStackTrace()[0].getMethodName() + ":"
							+ swtExp.getStackTrace()[0].getLineNumber());
			return ("detailsdataerror");
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [displayAccountAttributes] method : - "
					+ exp.getMessage());
			SwtUtil.logException(
					SwtErrorHandler.getInstance().handleException(exp,
							"displayAccountAttributes",
							ILMGeneralMaintenanceAction.class), request, "");
			return ("fail");

		} finally {
			// Nullify objects
			hostId = null;
			entityId = null;
			currencyCode = null;
		}
	}
	/**
	 * This method is used to show the list of account attributes 
	 * depending of the passed parameters (entity, currency, account, attribute, from date and
	 * to date
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return
	 * @throws SwtException
	 */
	public String listAccountAttributes()
					throws SwtException {
HttpServletRequest request = ServletActionContext.getRequest();
HttpServletResponse response = ServletActionContext.getResponse();

		//Variables to hold passed params
		String hostId = null;
		String entityId = null;
		String currencyCode = null;
		String accountId = null;
		String attributeId = null;
		String dateFrom = null;
		String dateTo = null;
		String fromElement = null;
		Date fromDate = null;
		Date toDate = null;
		String parentScreen = null;
		String selectedEffectiveDate = null;
		//Variable to store currency list depending of user role
		ArrayList<LabelValueBean> currencyList = null;
		//Variable to hold account attribute list result
		List<AccountAttribute> accountAttributes = null;
		AccountAttribute account = null;
		AccountAttributeHDR accountHDR = null;
		int accessInd;
		int selectedRow  = -1;;
		String accountHDRType = null;
		try {
			log.debug(this.getClass().getName() + " - [listAccountAttributes] - Enter");
			opTimer.start("all");
			// Retrieve passed params
			hostId = SwtUtil.getCurrentHostId();
			entityId = request.getParameter("entityId");
			currencyCode = request.getParameter("currencyCode");
			accountId = request.getParameter("accountId");
			attributeId = request.getParameter("attributeId");
			dateFrom = request.getParameter("dateFrom");
			dateTo = request.getParameter("dateTo");
			fromElement = request.getParameter("from");
			parentScreen = request.getParameter("parentScreen");
			selectedEffectiveDate = request.getParameter("selectedEffectiveDate");
			
			// Getting the hostId
			if (SwtUtil.isEmptyOrNull(hostId)) {
				hostId = SwtUtil.getCurrentHostId();
			}
			// Getting the user session
			if (SwtUtil.isEmptyOrNull(entityId) || entityId.equals("null") ) {
				entityId = SwtUtil.getUserCurrentEntity(UserThreadLocalHolder
						.getUserSession());
			}
			// Getting the currency code
			if (currencyCode == null || (!SwtUtil.isEmptyOrNull(fromElement) && fromElement.equals("entity"))) {
				currencyCode = SwtUtil.getDomesticCurrencyForUser(request,
						SwtUtil.getCurrentHostId(), entityId);
			}
		
			// Set account id to all if no one is selected in the dropdown or when selecting a new an entity or a currency from list
			if (SwtUtil.isEmptyOrNull(accountId) || accountId.equals("null")) {
				accountId = "All";
			}
			if(!SwtUtil.isEmptyOrNull(attributeId)) {
				// If a from date or to date was set then cast is as date
				if (!SwtUtil.isEmptyOrNull(dateFrom) && !dateFrom.equals("null")) {
					fromDate = SwtUtil.parseDate(dateFrom, SwtUtil.getCurrentDateFormat(request.getSession()));
				}
				if (!SwtUtil.isEmptyOrNull(dateTo) && !dateTo.equals("null")) {
					toDate = SwtUtil.parseDate(dateTo, SwtUtil.getCurrentDateFormat(request.getSession()));
				}
				// Get account attributes from the passed params
				accountAttributes = (List<AccountAttribute>) accountAttributeMaintenanceManager.getAccountAttributeList(hostId, entityId, currencyCode, accountId, attributeId, fromDate, toDate);
				Iterator itr1 = accountAttributes.iterator();
				int rowIndex = -1;
				// Format dates effective date, date value using system format and put it as string. 
				while (itr1.hasNext()) {
					rowIndex++;
					account = (AccountAttribute) itr1.next();
					account.setEffectiveDateAsString((SwtUtil.formatDate(account.getEffectiveDate(),
							SwtUtil.getCurrentSystemFormats(request.getSession()).getDateFormatValue() + " HH:mm:ss")));
	
					if(!SwtUtil.isEmptyOrNull(account.getEffectiveDateAsString())) {
						account.setEffectiveDateAsString(account.getEffectiveDateAsString().replaceAll(" 00:00:00", ""));
					}else {
						account.setEffectiveDateAsString("null");
					}
					if(!SwtUtil.isEmptyOrNull(parentScreen) && (selectedRow == -1) ){
						//  locate the row that corresponds to the most recent effective
						if(account.getEffectiveDateAsString().equals(selectedEffectiveDate)){
							selectedRow = rowIndex;
						}
					}
					
					
					account.setUpdateDateAsString((SwtUtil.formatDate(account.getUpdateDate(), SwtUtil.getCurrentSystemFormats(
									request.getSession()).getDateFormatValue() + " HH:mm:ss")));
					if(!SwtUtil.isEmptyOrNull(account.getUpdateDateAsString())) {
						account.setUpdateDateAsString(account.getUpdateDateAsString().replaceAll(" 00:00:00", ""));
					}
					
					if(account.getDateValue() != null) {
						accountHDRType="date";
						account.setValueAsString((SwtUtil.formatDate(account.getDateValue(), SwtUtil.getCurrentSystemFormats(
						
								request.getSession()).getDateFormatValue() + " HH:mm:ss")));
						if(!SwtUtil.isEmptyOrNull(account.getValueAsString())) {
							account.setValueAsString(account.getValueAsString().replaceAll(" 00:00:00", ""));
						}else {
							account.setValueAsString("null");
						}
					}
					
					else if(account.getNumValue() != null) {
						accountHDRType="num";
						String patternCcy = SwtUtil
								.getCurrentCurrencyFormat(request.getSession());
						String value = ""+account.getNumValue();
						if (patternCcy.indexOf("2") != -1) {
							value = value.replaceAll("\\.", ",");
						} 
						account.setValueAsString(value);
				
					} else {
						accountHDRType="str";
						account.setValueAsString(account.getStrValue());
					}
				}
				
			} else {
				accountAttributes = new ArrayList<AccountAttribute>();
			}
			
			if(!SwtUtil.isEmptyOrNull(parentScreen) && (selectedRow == -1) ){
					selectedRow = 0;
			}
			// Set the access index for the screen in order to set the behavior of the add/change/delete button related to entity and currency IDs.
 			// If 0 then without any restriction, the user could only user the screen in view mode 
 			accessInd = SwtUtil.getMenuEntityCurrGrpAccess(request, entityId, currencyCode);
			// Put result in request and forward it to the related JSP file
 			request.setAttribute("accessInd", accessInd);
			// do(Desc) sort 
			Collections.sort(accountAttributes, new AccountAttributeComparator());
			Collections.reverse(accountAttributes);
			request.setAttribute("accountAttibutes", accountAttributes);
			request.setAttribute("selectedRow", selectedRow);
			request.setAttribute("accountHDRType", accountHDRType);
			request.setAttribute("showGridcontent", "true");
	
			//request.setAttribute("selectedAttribute", attributeId);
			request.setAttribute("recordCount", accountAttributes.size());
			request.setAttribute("reply_status_ok", SwtConstants.STR_TRUE);
			request.setAttribute("reply_message", "Data fetch OK");
			// Set column width with request attribute
			bindColumnOrderInRequest(request, "" + SwtConstants.SCREEN_ACCOUNT_ATTRIBUTES_VALUE);
			bindColumnWidthInRequest(request, "" + SwtConstants.SCREEN_ACCOUNT_ATTRIBUTES_VALUE);
			opTimer.stop("all");
			// Set the attribute for opTimes,lastRefTime in request
			request.setAttribute("opTimes", opTimer.getOpTimes());
			log.debug(this.getClass().getName() + " - [listAccountAttributes] - Exit");
			return ("accountAttrMaintenanceGridData");
		} catch (SwtException swtExp) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [listAccountAttributes] method : - "
					+ swtExp.getMessage());
			request.setAttribute("reply_status_ok", SwtConstants.STR_FALSE);
			request.setAttribute("reply_message", swtExp.getMessage());
			request.setAttribute(
					"reply_location",
					swtExp.getStackTrace()[0].getClassName() + "."
							+ swtExp.getStackTrace()[0].getMethodName() + ":"
							+ swtExp.getStackTrace()[0].getLineNumber());
			return ("detailsdataerror");
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [listAccountAttributes] method : - "
					+ exp.getMessage());
			SwtUtil.logException(
					SwtErrorHandler.getInstance().handleException(exp,
							"listAccountAttributes",
							ILMGeneralMaintenanceAction.class), request, "");
			return ("fail");

		} finally {
			// Nullify objects
			hostId = null;
			entityId = null;
			currencyCode = null;
		}
	}
	
	
	/**
	 * This method is called to open the account group details screen
	 * 
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return actionForward
	 */
	public String accountAttributesAddMaintenance() {
HttpServletRequest request = ServletActionContext.getRequest();
HttpServletResponse response = ServletActionContext.getResponse();


		String entityId = null;
		String currencyCode = null;
		String accountId = null;
		String attributeId = null;
		String methodName = null;
		String effectivedateTime = null;
		String seqKey = null;
		String value = null;
		Date sysDate = null;
		String sysDateAsString = null;
		AccountAttribute account; 
		try {
			log.debug(this.getClass().getName()
					+ " - [accountAttributesAddMaintenance] - Enter");
			entityId = request.getParameter("entityId");
			currencyCode = request.getParameter("currencyCode");
			attributeId = request.getParameter("attributeId");
			methodName = request.getParameter("methodName");
			seqKey = request.getParameter("seqKey");

			// get the system date from swtutil
			sysDate = SwtUtil.getSystemDatewithoutTime();
			// get the formatted system date form swtutil
			sysDateAsString = SwtUtil.formatDate(sysDate, SwtUtil
					.getCurrentSystemFormats(request.getSession())
					.getDateFormatValue());
			if (!methodName.equals("add")) {
				account = accountAttributeMaintenanceManager.getAcctAttribute(seqKey);
				if(account.getNumValue()!=null){
					String patternCcy = SwtUtil
							.getCurrentCurrencyFormat(request.getSession());
					value = ""+account.getNumValue();
					if (methodName.equals("view") && patternCcy.indexOf("2") != -1) {
						value = value.replaceAll("\\.", ",");
					} 
				}else if(account.getStrValue()!=null){
					value = account.getStrValue();
				}else if (account.getDateValue()!=null){
					value = SwtUtil.formatDate(account.getDateValue(), SwtUtil.getCurrentSystemFormats(
							request.getSession()).getDateFormatValue() + " HH:mm:ss");
				}else
					value = "";
				
				effectivedateTime = (SwtUtil.formatDate(account.getEffectiveDate(),
						SwtUtil.getCurrentSystemFormats(request.getSession()).getDateFormatValue() + " HH:mm:ss"));
				

				if(!SwtUtil.isEmptyOrNull(effectivedateTime)) {
					effectivedateTime = effectivedateTime.replaceAll(" 00:00:00", "");
				}else {
					effectivedateTime = "null";
				}
				accountId = account.getAccountId();
			} else {
				accountId = request.getParameter("accountId");
				if (SwtUtil.isEmptyOrNull(request
						.getParameter("effectivedateTime"))) {
					effectivedateTime = SwtUtil.formatDate(
							SwtUtil.getSysParamDate(),
							SwtUtil.getCurrentSystemFormats(
									request.getSession()).getDateFormatValue()
									+ " HH:mm:ss");
				} else {
					effectivedateTime = request
							.getParameter("effectivedateTime");
				}
			}
			
			request.setAttribute("sysDateAsString", sysDateAsString);
			request.setAttribute("entityId", entityId);
			request.setAttribute("currencyCode", currencyCode);
			request.setAttribute("accountId", accountId);
			request.setAttribute("attributeId", attributeId);
			request.setAttribute("effectivedateTime",
					effectivedateTime.equals("null") ? "" : effectivedateTime);
			request.setAttribute("methodName", methodName);
			request.setAttribute("seqKey", seqKey);
			request.setAttribute("value", value);
			log.debug(this.getClass().getName()
					+ " method [accountAttributesAddMaintenance] - Exit ");
			return ("accountAttrAddMaintenance");
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [accountAttributesAddMaintenance] method : - "
					+ exp.getMessage());

			request.setAttribute("reply_status_ok", SwtConstants.STR_FALSE);
			request.setAttribute("reply_message", exp.getMessage());
			request.setAttribute(
					"reply_location",
					exp.getStackTrace()[0].getClassName() + "."
							+ exp.getStackTrace()[0].getMethodName() + ":"
							+ exp.getStackTrace()[0].getLineNumber());
			SwtUtil.logException(
					SwtErrorHandler.getInstance().handleException(exp,
							"accountAttributesAddMaintenance",
							AccountAttributeMaintenanceAction.class), request,
					"");
			return ("fail");
		}
	}
	
	/**
	 * This method is used to open account attributes details screen
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return ActionForward
	 * @throws SwtException
	 */
	public String accountAttributesValuesAdd()
					throws SwtException {
HttpServletRequest request = ServletActionContext.getRequest();
HttpServletResponse response = ServletActionContext.getResponse();

		
		String hostId = null;
		String entityId = null;
		String currencyCode = null;
		String accountId = null;
		String attributeId = null;
		String fromElement = null;
		AccountAttributeHDR accountHDR = null;
		ArrayList<LabelValueBean> currencyList = null;
		Collection acctAttributesHDR = null;
		
		try {
			log.debug(this.getClass().getName() + " - [accountAttributesValuesAdd] - Enter");
			opTimer.start("all");
			// Retrieve Host Id
			hostId = SwtUtil.getCurrentHostId();
			entityId = request.getParameter("entityId");
			currencyCode = request.getParameter("currencyCode");
			accountId = request.getParameter("accountId");
			attributeId = request.getParameter("attributeId");
			fromElement = request.getParameter("from");
			// Getting the hostId
			if (SwtUtil.isEmptyOrNull(hostId)) {
				hostId = SwtUtil.getCurrentHostId();
			}
			// Getting the user session
			if (SwtUtil.isEmptyOrNull(entityId) || entityId.equals("null") ) {
				entityId = SwtUtil.getUserCurrentEntity(UserThreadLocalHolder
						.getUserSession());
			}
			// Getting the currency code
			if (currencyCode == null || (!SwtUtil.isEmptyOrNull(fromElement) && fromElement.equals("entity"))) {
				currencyCode = SwtUtil.getDomesticCurrencyForUser(request,
						SwtUtil.getCurrentHostId(), entityId);
			}
			// Get the accounts for the account id dropdown.
			Collection<LabelValueBean> accountsList = accountAttributeMaintenanceManager
					.getAccountList(hostId, entityId, currencyCode);
			if (SwtUtil.isEmptyOrNull(accountId) 
					|| accountId.equals("null") 
					|| accountId.equals("All") 
					|| (!SwtUtil.isEmptyOrNull(fromElement) && (fromElement.equals("entity") 
					|| fromElement.equals("ccy")))) {
				// If no attribute is selected then select the first in list 
				if(accountsList != null && accountsList.size()>0)
					accountId =((LabelValueBean) accountsList.iterator().next()).getValue();
			}
			// Put lists in request (entities, currencies, accounts and attributes)
			putAccountListInRequest(request, accountsList, false);
			putEntityListInReq(request, false);
			acctAttributesHDR = putAttributeListInReq(request, false);
			
			currencyList = (ArrayList) getCurrencyList(request, entityId, false);
			
			if (SwtUtil.isEmptyOrNull(attributeId) 
					|| attributeId.equals("null") 
					|| (!SwtUtil.isEmptyOrNull(fromElement) && !fromElement.equals("attribute"))) {
				attributeId =((LabelValueBean) acctAttributesHDR.iterator().next()).getValue();
			}
			// Get attributes details from db
			accountHDR = accountAttributeMaintenanceManager.getAccountAttributeHDR(attributeId);
			// Set attribute type
			if(accountHDR.getValueType().equals("N")) {
				request.setAttribute("selectedType", SwtUtil.getMessage(SwtConstants.NUMERIC_TYPE, request));
			}
			else if(accountHDR.getValueType().equals("D")) {
				request.setAttribute("selectedType", SwtUtil.getMessage(SwtConstants.DATE_TYPE, request));
			} else {
				request.setAttribute("selectedType", SwtUtil.getMessage(SwtConstants.TEXT_TYPE, request));
			}
			// Send result in request
			request.setAttribute("currencies", currencyList);
			request.setAttribute("accountHDR", accountHDR);
			request.setAttribute("minValue", accountHDR.getValidateNumMin());
			request.setAttribute("maxValue", accountHDR.getValidateNumMax());
			request.setAttribute("selectedAccount", accountId);
			request.setAttribute("selectedAttribute", attributeId);
			request.setAttribute("entityId", entityId);
			request.setAttribute("typesList", getListTypes(request));
			request.setAttribute("currencyCode", currencyCode);
			request.setAttribute("reply_status_ok", SwtConstants.STR_TRUE);
			request.setAttribute("reply_message", "Data fetch OK");
		
		
			opTimer.stop("all");
			// Set the attribute for opTimes,lastRefTime in request
			request.setAttribute("opTimes", opTimer.getOpTimes());
			log.debug(this.getClass().getName() + " - [accountAttributesValuesAdd] - Exit");
			return ("accountAttrAddMaintenanceData");
		} catch (SwtException swtExp) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [accountAttributesValuesAdd] method : - "
					+ swtExp.getMessage());
			request.setAttribute("reply_status_ok", SwtConstants.STR_FALSE);
			request.setAttribute("reply_message", swtExp.getMessage());
			request.setAttribute(
					"reply_location",
					swtExp.getStackTrace()[0].getClassName() + "."
							+ swtExp.getStackTrace()[0].getMethodName() + ":"
							+ swtExp.getStackTrace()[0].getLineNumber());
			return ("detailsdataerror");
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [accountAttributesValuesAdd] method : - "
					+ exp.getMessage());
			SwtUtil.logException(
					SwtErrorHandler.getInstance().handleException(exp,
							"accountAttributesValuesAdd",
							ILMGeneralMaintenanceAction.class), request, "");
			return ("fail");
			
		} finally {
			// Nullify objects
			hostId = null;
			entityId = null;
			currencyCode = null;
		}
	}
	
	
	/**
	 * This method is called to delete an element from the grid depending the sent 
	 * attributes value  
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return ActionForward
	 * @throws SwtException
	 */
	public String deleteAccountAttribute()
					throws SwtException {
HttpServletRequest request = ServletActionContext.getRequest();
HttpServletResponse response = ServletActionContext.getResponse();

		String sequenceId = null;
		try {
			log.debug(this.getClass().getName() + " - [deleteAccountAttribute] - Enter");
			opTimer.start("all");
			// get sequence number from request
			sequenceId = request.getParameter("sequenceId");
			//get object related to the passed sequence number
			AccountAttribute accountAttribute = accountAttributeMaintenanceManager.getAcctAttribute(sequenceId);
			//Delete account attribute from db
			accountAttributeMaintenanceManager.deleteAcctAttribute(accountAttribute);
			//if no errors occurs the reply "ok"
			request.setAttribute("reply_status_ok", SwtConstants.STR_TRUE);
			request.setAttribute("reply_message", "Data fetch OK");
			// Set column width with request attribute
			opTimer.stop("all");
			// set the attribute for opTimes,lastRefTime in request
			request.setAttribute("opTimes", opTimer.getOpTimes());
			log.debug(this.getClass().getName() + " - [deleteAccountAttribute] - Exit");
			//Redirect request to refresh screen after delete
			return listAccountAttributes();
		} catch (SwtException swtExp) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [deleteAccountAttribute] method : - "
					+ swtExp.getMessage());
			request.setAttribute("reply_status_ok", SwtConstants.STR_FALSE);
			request.setAttribute("reply_message", swtExp.getMessage());
			request.setAttribute(
					"reply_location",
					swtExp.getStackTrace()[0].getClassName() + "."
							+ swtExp.getStackTrace()[0].getMethodName() + ":"
							+ swtExp.getStackTrace()[0].getLineNumber());
			return ("detailsdataerror");
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [deleteAccountAttribute] method : - "
					+ exp.getMessage());
			SwtUtil.logException(
					SwtErrorHandler.getInstance().handleException(exp,
							"deleteAccountAttribute",
							ILMGeneralMaintenanceAction.class), request, "");
			return ("fail");
			
		} finally {
			sequenceId = null;
		}
	}
	
	
	/**
	 *  Display "Account Attribute Last Values" screen containing a grid which will list all possible attributes
	 *  related to an account 
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return ActionForward
	 * @throws SwtException
	 */
	public String displayAttributesFlex()
					throws SwtException {
HttpServletRequest request = ServletActionContext.getRequest();
HttpServletResponse response = ServletActionContext.getResponse();

		
		String hostId = null;
		String entityId = null;
		String accountId = null;
		String currencyCode = null;
		ArrayList<AccountAttribute> attributesList = null;
		AccountAttribute accountAttr = null;
		
		try {
			log.debug(this.getClass().getName() + " method [displayAttributesFlex] - Enter ");
			hostId = SwtUtil.getCurrentHostId();
			entityId = request.getParameter("entityId");
			accountId = request.getParameter("accountId");
			currencyCode = request.getParameter("currencyCode");
			request.setAttribute("entityId", entityId);
			request.setAttribute("accountId", accountId);
			request.setAttribute("currencyCode", currencyCode);
			if (SwtUtil.isEmptyOrNull(request.getParameter("loadFlex"))) {
				
				log.debug(this.getClass().getName()
						+ "-[displayAttributesFlex]-Exit loading SWF");
				request.setAttribute("entityName", request.getParameter("entityName"));
				request.setAttribute("accountName", request.getParameter("accountName"));
				
				return ("attributeslastvaluesflex");
			}
			attributesList = accountAttributeMaintenanceManager.getLastValuesAccountAttrList(entityId, accountId, hostId);
			Iterator itr = attributesList.iterator();
			while (itr.hasNext()) {
				accountAttr = (AccountAttribute ) itr.next();
				if(accountAttr.getUpdateDate()!=null) {
					accountAttr.setUpdateDateAsString((SwtUtil.formatDate(accountAttr.getUpdateDate(), SwtUtil.getCurrentSystemFormats(
									request.getSession()).getDateFormatValue() + " HH:mm:ss")));
					if(!SwtUtil.isEmptyOrNull(accountAttr.getUpdateDateAsString())) {
						accountAttr.setUpdateDateAsString(accountAttr.getUpdateDateAsString().replaceAll(" 00:00:00", ""));
					}
				}
				if(accountAttr.getEffectiveDate()!=null) {
					accountAttr.setEffectiveDateAsString((SwtUtil.formatDate(accountAttr.getEffectiveDate(), SwtUtil.getCurrentSystemFormats(
									request.getSession()).getDateFormatValue() + " HH:mm:ss")));
					if(!SwtUtil.isEmptyOrNull(accountAttr.getEffectiveDateAsString())) {
						accountAttr.setEffectiveDateAsString(accountAttr.getEffectiveDateAsString().replaceAll(" 00:00:00", ""));
					}
				}
				if(accountAttr.getNumValue()!=null){
					String patternCcy = SwtUtil
							.getCurrentCurrencyFormat(request.getSession());
					String value = ""+accountAttr.getNumValue();
					if (patternCcy.indexOf("2") != -1) {
						value = value.replaceAll("\\.", ",");
					}
					accountAttr.setValueAsString(value);
					accountAttr.setTypeAsString("numeric");
					
				}else if(accountAttr.getStrValue()!=null){
					accountAttr.setValueAsString(accountAttr.getStrValue());
					accountAttr.setTypeAsString("text");
				}else if (accountAttr.getDateValue()!=null){
					accountAttr.setValueAsString((SwtUtil.formatDate(accountAttr.getDateValue(), SwtUtil.getCurrentSystemFormats(
							request.getSession()).getDateFormatValue() + " HH:mm:ss")));
					accountAttr.setTypeAsString("date");
				}else
					accountAttr.setValueAsString("");
			}
			request.setAttribute("lastValuesAttributesList", attributesList);
			request.setAttribute("recordCount", attributesList.size());
			request.setAttribute("reply_status_ok", SwtConstants.STR_TRUE);
			request.setAttribute("reply_message", "Data fetch OK");
			log.debug(this.getClass().getName() + " method [displayAttributesFlex] - Exit");
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [displayAttributesFlex] method : - "
					+ exp.getMessage());
			request.setAttribute("reply_status_ok", SwtConstants.STR_FALSE);
			request.setAttribute("reply_message", exp.getMessage());
			request.setAttribute("reply_location", exp.getStackTrace()[0]
					.getClassName() + "." + exp.getStackTrace()[0].getMethodName() + ":" + exp.getStackTrace()[0].getLineNumber());
			SwtUtil.logException(
					SwtErrorHandler.getInstance().handleException(exp,
							"displayAttributesFlex",
							AccountAttributeMaintenanceAction.class), request, "");
			return ("fail");
		}
		
		return ("attributeslastvaluesData");
	}
	
	/**
	 * Method to set column width in request attribute
	 * 
	 * @param request
	 * @param screenId
	 */
	private void bindColumnWidthInRequest(HttpServletRequest request, String ScreenId) {

		String width = null;
		HashMap<String, String> widths = null;
		String[] props = null;

		try {
			log.debug(this.getClass().getName() + " - [bindColumnWidthInRequest] - Enter");
			// Read the user preferences for column width from property value
			width = SwtUtil.getPropertyValue(request, ScreenId, "display",
					"column_width");
			// Condition to set default column width
			// Set default width for columns
			if (SwtUtil.isEmptyOrNull(width)) {
				if (("" + SwtConstants.SCREEN_ACCOUNT_ATTRIBUTES).equalsIgnoreCase(ScreenId)){
					width = "attributeid=250,attributename=250,type=75,updateDateAsString=150,updateuser=100,systemFlag=90"; 
				}
				else if (("" + SwtConstants.SCREEN_ACCOUNT_ATTRIBUTES_USAGE).equalsIgnoreCase(ScreenId)){
					width = "attribute_id=250,name=201,type=75,display=90";
				}
				else{
					width = "accountId=135,effectivedateTime=180,value=210,updateDateAsString=150,updateuser=100";
				}
				
			}
			widths = new HashMap<String, String>();
			// Get column width for each column */
			props = width.split(",");
			// Loop to separate column and width value in hash map
			for (int i = 0; i < props.length; i++) {
				if (props[i].indexOf("=") != -1) {
					String[] propval = props[i].split("=");
					widths.put(propval[0], propval[1]);
				}
			}
			request.setAttribute("column_width", widths);
			log.debug(this.getClass().getName() + " - [bindColumnWidthInRequest] - Exit");
		} catch (Exception e) {
			log.error(this.getClass().getName() + " - [bindColumnWidthInRequest] - " + e.getMessage());
		} finally {
			// nullify the objects
			widths = null;
			props = null;
		}
	}

	/**
	 * This method gets entity list from database and put them in request scope
	 * 
	 * @param request
	 *            HttpServletRequest request
	 * @throws SwtException
	 */
	private void putEntityListInReq(HttpServletRequest request, boolean viewEnable) throws SwtException {
		
		// entity collection
		Collection<EntityUserAccess> entityColl = null;
		Collection<EntityUserAccess> entityFullAccess = new ArrayList<EntityUserAccess>();
		Collection<LabelValueBean> colEntityLVB = null;
		EntityUserAccess entity = null;
		Iterator itr = null;
		try {
			// Get the user access entity list .
			entityColl = SwtUtil.getUserEntityAccessList(request.getSession());
			if (!viewEnable) {
				itr = entityColl.iterator();
				while (itr.hasNext()) {
	
					entity = (EntityUserAccess) itr.next();
					if (entity.getAccess() == 0) {
						entityFullAccess.add(entity);
	
					}
				}
			} else {
				entityFullAccess = entityColl;  
			}

			// Convert the entity list to label value bean.
			colEntityLVB = SwtUtil.convertEntityAcessCollectionLVLFullName(
					entityFullAccess, request.getSession());
			// Set the entity collection into request.
			request.setAttribute("entities", colEntityLVB);
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					+ " - [putEntityFullAccessListInReq] - Exception -"
					+ exp.getMessage());
		} finally {
			// nullify objects
			entityColl = null;
			colEntityLVB = null;
		}
	}
	
	
	/**
	 * 
	 * This function returns collection of currencies
	 * 
	 * @param request
	 * @param entityId
	 * @return  collection of currencies
	 * @throws SwtException
	 */
	private Collection<LabelValueBean> getCurrencyList(HttpServletRequest request, String entityId, boolean fullAccess) 
			throws SwtException {
		
		// Current user's role id
		String roleId = null;
		// Currency list allocated for current user (mapped with role)
		ArrayList<LabelValueBean> currencyList = null;
		// Currency List
		ArrayList ccyList = null;
		try {
			log.debug(this.getClass().getName() + " - [getCurrencyList] - Enter");
			// Getting the User's Role Id from the session object
			roleId = ((CommonDataManager) request.getSession().getAttribute(
					SwtConstants.CDM_BEAN)).getUser().getRoleId();
			// Returns the currency Access List based on the Role
			if (fullAccess) {
				currencyList = (ArrayList<LabelValueBean>) SwtUtil
						.getSwtMaintenanceCache().getCurrencyViewORFullAcessLVL(roleId, entityId);
			} else {
				currencyList = (ArrayList) getCurrencyFullAccessList(request,
						SwtUtil.getCurrentHostId(), entityId);
			}
			
							

			if (currencyList != null) {
				/*
				 * Removes the LabelValueBean object for the Key as 'Default'
				 * from the collection
				 */
				currencyList.remove(new LabelValueBean("Default", "*"));
			}
			log.debug(this.getClass().getName() + " - [getCurrencyList] - Exit");
			return currencyList;
		} catch (Exception ex) {
			log.error(this.getClass().getName()
					+ " - [getCurrencyList] - Exception - " + ex.getMessage());
			// Re-throw an exception
			throw SwtErrorHandler.getInstance().handleException(ex,
					"getCurrencyList", AccountAttributeMaintenanceAction.class);
		} finally {
			// nullify object(s)
			roleId = null;
		}

	}
	
	/**
	 * 
	 * This function returns the collection of LabelValueBean objects for a role
	 * id and entity id which have the full access.
	 * 
	 * @param HttpServletRequest
	 *            request
	 * @param String
	 *            hostId
	 * @param String
	 *            entityId
	 * @return - currrencyList
	 * @throws SwtException
	 *             - SwtException object
	 */
	private Collection<LabelValueBean> getCurrencyFullAccessList(
			HttpServletRequest request, String hostId, String entityId)
			throws SwtException {

		// String Variable to hold the roleId
		String roleId = null;
		// Variable to hold the currencyMap object
		Map<String, String> currencyMap = null;
		// Variable to hold the currrencyList object
		Collection<LabelValueBean> currrencyList = null;
		// Variable to hold the itrCurrencyKey object
		Iterator<String> itrCurrencyKey = null;
		// String Variable to hold the currencyId
		String currencyId = null;

		try {
			log.debug(this.getClass().getName()
					+ " - [ getCurrencyFullAccessList ] - Entry ");
			/* Getting the User's Role Id from the session object */
			roleId = ((CommonDataManager) request.getSession().getAttribute(
					SwtConstants.CDM_BEAN)).getUser().getRoleId();
			currrencyList = new ArrayList<LabelValueBean>();
			/* Returns the currency Access List based on the Role */
			currencyMap = SwtUtil.getCurrencyFullAccessMap(roleId, hostId,
					entityId);

			if (currencyMap != null && currencyMap.size() > 0) {
				// iterate the currency map key values
				itrCurrencyKey = currencyMap.keySet().iterator();
				while (itrCurrencyKey.hasNext()) {
					// get the currency id from map
					currencyId = itrCurrencyKey.next();

					// add labelvaluebean for currency id
					currrencyList.add(new LabelValueBean((String) currencyMap
							.get(currencyId), currencyId));
				}
			}
			log.debug(this.getClass().getName()
					+ " - [ getCurrencyFullAccessList ] - Exit ");
		} catch (SwtException swtexp) {
			log.error("SwtException Catch in ILMGeneralMaintenanceAction.'getCurrencyFullAccessList' method : "
					+ swtexp.getMessage());
			SwtUtil.logException(swtexp, request, "");
			throw swtexp;
		} catch (Exception exp) {
			log.error("Exception Catch in ILMGeneralMaintenanceAction.'getCurrencyFullAccessList' method : "
					+ exp.getMessage());
			throw new SwtException(exp.getMessage());

		} finally {
			// nullify objects
			roleId = null;
			currencyMap = null;
			itrCurrencyKey = null;
			currencyId = null;
		}
		return currrencyList;
	}
	
	/**
	 * Method called to populate request with account group list
	 * 
	 * @param request
	 * @param putAllLabel
	 * @return Collection
	 * @throws SwtException
	 */
	private Collection putAttributeListInReq(HttpServletRequest request,
			boolean putAllLabel) throws SwtException {
		
		ArrayList acctAttributesHDR = null;
		AccountAttributeHDR accountAttribute;
		Collection pattributeList = null;
		Iterator itr1;
		
		log.debug(this.getClass().getName() + " - [putAttributeListInReq] - " + "Enter");
		acctAttributesHDR = new ArrayList();
		pattributeList = accountAttributeMaintenanceManager.getAcctAttributHDRDetailList();
		
		// Adding a new LabelValueBean object with the Key as 'ALL' and value as
		if (putAllLabel) {
			acctAttributesHDR.add(new LabelValueBean(SwtConstants.ALL_LABEL, SwtConstants.ALL_VALUE));
		}
		
		itr1 = pattributeList.iterator();
		while (itr1.hasNext()) {
			accountAttribute = (AccountAttributeHDR) itr1.next();
			acctAttributesHDR.add(new LabelValueBean((String) accountAttribute.getAttributeName(), 
														(String) accountAttribute.getId().getAttributeId()));
		}
		
		request.setAttribute("accountAttributesDetails", acctAttributesHDR);
		log.debug(this.getClass().getName() + " - [putAttributeListInReq] - " + "Exit");
		return acctAttributesHDR;
	}
	
	/**
	 * Adds an empty label-value bean to the given collection and then puts it
	 * in request scope
	 * 
	 * @param request
	 * @param accounts
	 * @param putAllLabel
	 */
	private void putAccountListInRequest(HttpServletRequest request,
			Collection<LabelValueBean> accounts, boolean putAllLabel) {
		
		log.debug(this.getClass().getName() + " method [putAccountListInRequest] - Enter ");
		Collection<LabelValueBean> accountDropDown = new ArrayList<LabelValueBean>();
		if (putAllLabel) {
			accountDropDown.add(new LabelValueBean(SwtConstants.ALL_LABEL, SwtConstants.ALL_VALUE));
		}
		Iterator<LabelValueBean> itr = accounts.iterator();
		while (itr.hasNext()) {
			LabelValueBean lvb = (LabelValueBean) (itr.next());
			accountDropDown.add(lvb);
		}
		request.setAttribute("accounts", accountDropDown);
		log.debug(this.getClass().getName() + " method [putAccountListInRequest] - Exit ");
	}
	
	
	/**
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return
	 * @throws SwtException
	 */
	public String checkExistingAttributeData() throws SwtException {
HttpServletRequest request = ServletActionContext.getRequest();
HttpServletResponse response = ServletActionContext.getResponse();

		
		ArrayList<AccountAttribute> accountAttributesList = null;
		String attributeId = null;
		String existingData = "N";
			
		try {
			log.debug(this.getClass().getName()
					+ "- [checkExistingAttributeData] - Enter");
			attributeId = request.getParameter("attributeId");
			accountAttributesList = accountAttributeMaintenanceManager.getAccountAttributeList(attributeId);
			if(accountAttributesList != null){
				existingData = accountAttributesList.isEmpty() ? "N":"Y";
			}
			// Sending the result
			response.getWriter().print(existingData);
			log.debug(this.getClass().getName() + " - [checkExistingAttributeData] - "
					+ "Exit");
			return null;
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [checkExistingAttributeData] method : - "
					+ exp.getMessage());
			SwtUtil.logException(
					SwtErrorHandler.getInstance().handleException(exp,
							"updateAppropriateDstPeriod",
							AccountAttributeMaintenanceAction.class), request,
					"");
			return ("fail");
		}
	}
	/**
	 * This method save the width of the column in account attribute's screens
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return
	 */
	public String saveColumnWidth() {
HttpServletRequest request = ServletActionContext.getRequest();
HttpServletResponse response = ServletActionContext.getResponse();


		// String variable to hold the column width
		String width = null;
		String screenName = null;
		String screenId = null;
		
		try {
			screenName=request.getParameter("screenName");
			if (!SwtUtil.isEmptyOrNull(width = request.getParameter("width"))) {
				if(!SwtUtil.isEmptyOrNull(screenName)){ 
					if ("accountAttributeDefinition".equalsIgnoreCase(screenName)){
						screenId = ""+SwtConstants.SCREEN_ACCOUNT_ATTRIBUTES;
					}
					else if ("accountAttributeUsage".equalsIgnoreCase(screenName)){
						screenId = ""+SwtConstants.SCREEN_ACCOUNT_ATTRIBUTES_USAGE;
					}
					else if ("accountAttributeValue".equalsIgnoreCase(screenName)){
						screenId = ""+SwtConstants.SCREEN_ACCOUNT_ATTRIBUTES_VALUE;
					}
				}
				SwtUtil.setPropertyValue(request, SwtUtil
						.getUserCurrentEntity(request.getSession()),
						screenId, "display",
						"column_width", width);
			} else {
				throw new Exception(
						"You must send 'width' and 'entityid' parameters");
			}
			request.setAttribute("reply_status_ok", "true");
			request.setAttribute("reply_message", "Column width saved ok");
			log.debug(this.getClass().getName()
					+ " - [saveColumnWidth] - Exit ");
		} catch (Exception e) {
			log.error(this.getClass().getName()
					+ " - [saveColumnWidth] - Error - " + e.getMessage());
			request.setAttribute("reply_status_ok", "false");
			request.setAttribute("reply_message", e.getMessage());
			request.setAttribute("reply_location", e.getStackTrace()[0]
					.getClassName()
					+ "."
					+ e.getStackTrace()[0].getMethodName()
					+ ":"
					+ e.getStackTrace()[0].getLineNumber());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					e, "saveColumnWidth", AccountAttributeMaintenanceAction.class), request, "");
		}
		return ("save");
	}
	/**
	 * This method is for saving the Order of the columns in the account attribute 's screens
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return
	 */
	public String saveColumnOrder() {
HttpServletRequest request = ServletActionContext.getRequest();
HttpServletResponse response = ServletActionContext.getResponse();

		
		String columnOrder = null;
		String screenName=null;
		String screenId=null;

		try {
			log.debug(this.getClass().getName() + " - [ saveColumnOrder ] - Entry ");
			screenName = request.getParameter("screenName");
				if (!SwtUtil.isEmptyOrNull(screenName)) { 
					if ("accountAttributeDefinition".equalsIgnoreCase(screenName)) {
						screenId = "" + SwtConstants.SCREEN_ACCOUNT_ATTRIBUTES;
					}
					else if ("accountAttributeUsage".equalsIgnoreCase(screenName)) {
						screenId = "" + SwtConstants.SCREEN_ACCOUNT_ATTRIBUTES_USAGE;
					}
					else if ("accountAttributeValue".equalsIgnoreCase(screenName)) {
						screenId = "" + SwtConstants.SCREEN_ACCOUNT_ATTRIBUTES_VALUE;
					}
				}
					columnOrder = request.getParameter("order");
			if (!SwtUtil.isEmptyOrNull(columnOrder)) {
				SwtUtil.setPropertyValue(request, SwtUtil
						.getUserCurrentEntity(request.getSession()),
						screenId, "display", "column_order", columnOrder);
			}
			request.setAttribute("reply_status_ok", "true");
			request.setAttribute("reply_message", "Column order saved ok");
		} catch (Exception e) {
		
			log.error(this.getClass().getName() + "- [ saveColumnOrder() ] - " + e);
			request.setAttribute("reply_status_ok", "false");
			request.setAttribute("reply_message", e.getMessage());
			request.setAttribute("reply_location", e.getStackTrace()[0]
					.getClassName()
					+ "."
					+ e.getStackTrace()[0].getMethodName()
					+ ":"
					+ e.getStackTrace()[0].getLineNumber());
		} finally {
			
			columnOrder = null;
	
			log.debug(this.getClass().getName() + " - [ saveColumnOrder ] - Exit ");
		}
		// Return Type of this Struts Action and returns to flexstatechange.jsp
		return ("statechange");
	}
	/**
	 * This method is to set the columns's order
	 * @param request
	 * @param screenId
	 * @throws SwtException
	 */
	private void bindColumnOrderInRequest(HttpServletRequest request,
			String screenId) throws SwtException {
		
		// To get column order from DB (Comma separated value)
		String columnOrder = null;
		// To hold grid column order
		ArrayList<String> alColumnOrder = null;
		// Property value (split by Comma)
		String[] props = null;

		try {
			// log debug message
			log.debug(this.getClass().getName() + " - [ bindColumnOrderInRequest ] - Entry ");
			// Get column order from DB (User preference)
			columnOrder = SwtUtil.getPropertyValue(request, screenId, "display", "column_order");
			// If user preference not found in DB, set default order
			if (SwtUtil.isEmptyOrNull(columnOrder)) {
				if (("" + SwtConstants.SCREEN_ACCOUNT_ATTRIBUTES).equals(screenId))
					columnOrder = "attributeid,attributename,type,updateDateAsString,updateuser,systemFlag"; 
				else if (("" + SwtConstants.SCREEN_ACCOUNT_ATTRIBUTES_USAGE).equals(screenId))
					columnOrder = "attribute_id,name,type,display";
				else if (("" + SwtConstants.SCREEN_ACCOUNT_ATTRIBUTES_VALUE).equals(screenId))
					columnOrder="accountId,effectivedateTime,value,updateDateAsString,updateuser";
			}
			/*
			 * Comma special character is used to split and put in String array
			 * variable
			 */
			props = columnOrder.split(",");
			// Initialize list to hold grid column order
			alColumnOrder = new ArrayList<String>(props.length);
			for (String prop : props) {
				/* Adding the Column values to ArrayList */
				alColumnOrder.add(prop);
			}
			/*
			 * Setting the Column orders value in request object to show in
			 * screen
			 */
			request.setAttribute("column_order", alColumnOrder);
		} catch (Exception ex) {
			// log error message
			log.error(this.getClass().getName()
					+ " - [bindColumnOrderInRequest] - Exception - "
					+ ex.getMessage());
			// Re-throw an exception
			throw SwtErrorHandler.getInstance().handleException(ex,
					"bindColumnOrderInRequest", AccountAttributeMaintenanceAction.class);
		} finally {
			// nullify objects
			columnOrder = null;
			alColumnOrder = null;
			props = null;
			// log debug message
			log.debug(this.getClass().getName() + " - [ bindColumnOrderInRequest ] - Exit ");
		}

	}
	/**
	 * This method is to check if there is an account attribute definition
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return
	 * @throws SwtException
	 */
	public String checkExistingAccountAttributeDefintionList() throws SwtException {
HttpServletRequest request = ServletActionContext.getRequest();
HttpServletResponse response = ServletActionContext.getResponse();

			
		Collection<AccountAttributeHDR> accountAttributesList = null;
		String existingData = "N";
				
		try {
			log.debug(this.getClass().getName() + "- [checkExistingAccountAttributeDefintionList] - Enter");
			accountAttributesList = accountAttributeMaintenanceManager.getAcctAttributHDRDetailList();
			if (accountAttributesList != null) {
				existingData = accountAttributesList.isEmpty() ? "N":"Y";
			}
			// Sending the result
			response.getWriter().print(existingData);
			log.debug(this.getClass().getName() + " - [checkExistingAccountAttributeDefintionList] - Exit");
			return null;
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [checkExistingAccountAttributeDefintionList] method : - "
					+ exp.getMessage());
			SwtUtil.logException(
					SwtErrorHandler.getInstance().handleException(exp,
							"checkExistingAccountAttributeDefintionList",
							AccountAttributeMaintenanceAction.class), request,
					"");
			return ("fail");
		}
	}
	
	public String checkEffectiveDate() throws SwtException {
HttpServletRequest request = ServletActionContext.getRequest();
HttpServletResponse response = ServletActionContext.getResponse();

		
		AccountAttributeHDR accountAttributeHDR = null;
		String accountAttributeId = request.getParameter("accountAttributeId");
		String isRequired = "N";
		try {
			log.debug(this.getClass().getName() + "- [checkEffectiveDate] - Enter");
			accountAttributeHDR = accountAttributeMaintenanceManager.getAccountAttributeHDR(accountAttributeId);
			if (accountAttributeHDR != null) {
				isRequired = accountAttributeHDR.getEffectiveDateRequired();
			}
			// Sending the result
			response.getWriter().print(isRequired);
			log.debug(this.getClass().getName() + " - [checkEffectiveDate] - Exit");
			return null;
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [checkEffectiveDate] method : - "
					+ exp.getMessage());
			SwtUtil.logException(
					SwtErrorHandler.getInstance().handleException(exp,
							"checkEffectiveDate",
							AccountAttributeMaintenanceAction.class), request,
					"");
			return ("fail");
		}
	}
	
	
	 /*  Implement the Comparator interface to make custom sort .
    AccountAttributeComparator class used to sort a List of AccountAttribute object */
	private class AccountAttributeComparator implements Comparator<AccountAttribute> {

		/**
		 * Used to compare two AccountAttribute object based on effective date value
		 * 
		 * @return -1, 0, or 1 as this AccountAttribute is less than, equal to, or
		 *         greater 
		 */
		public int compare(AccountAttribute o1, AccountAttribute o2) {
			int result = 0;
			Date dateO1 = null;
			Date dateO2 = null;
			try {
				dateO1 = o1.getEffectiveDate();
				dateO2 = o2.getEffectiveDate();
				if(dateO1 != null && dateO2 != null) {
					result = (dateO1.compareTo(dateO2));
				}
			} catch (Exception e) {
				// log error message
				log.error(this.getClass().getName()
						+ " - [compare] - Exception: " + e.getMessage());
				// Re-throw as SwtException
				try {
					throw new SwtException(e.getMessage());
				} catch (SwtException e1) {
				}
			}

			return result < 0 ? -1 : result == 0 ? 0 : 1;
		}

	}
	
	public String displayAttributesAngular() throws SwtException {
HttpServletRequest request = ServletActionContext.getRequest();
HttpServletResponse response = ServletActionContext.getResponse();

		//request.setAttribute("screenName", request.getParameter("screenName"));

		return ("attributeslastvaluesflex");
	}
}
