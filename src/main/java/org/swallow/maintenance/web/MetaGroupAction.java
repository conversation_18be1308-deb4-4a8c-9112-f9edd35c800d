/*
 * @(#)MetaGroupAction.java 1.0 23/08/06
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.maintenance.web;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Iterator;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.maintenance.model.Group;
import org.swallow.maintenance.model.MetaGroup;
import org.swallow.maintenance.service.BookCodeManager;
import org.swallow.maintenance.service.MetaGroupManager;
import org.swallow.util.CacheManager;
import org.swallow.util.LabelValueBean;
import org.swallow.util.SwtConstants;
import org.swallow.util.SwtUtil;
import org.swallow.util.SystemInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.apache.struts2.convention.annotation.Result;
import org.apache.struts2.convention.annotation.Action;
import org.apache.struts2.convention.annotation.AllowedMethods;
import org.apache.struts2.ServletActionContext;
import org.swallow.util.struts.ActionErrors;
import org.swallow.util.struts.ActionMessage;
import org.swallow.util.struts.ActionMessages;
import org.swallow.util.struts.CustomActionSupport;

@AllowedMethods ({"displayListByEntity" ,"add" ,"change" ,"group" ,"update" ,"save" ,"delete" ,"bookcodes" })

@Action(value = "/metaGroup", results = {
	@Result(name = "fail", location = "/error.jsp"),
	@Result(name = "success", location = "/jsp/maintenance/metagroupmaintenance.jsp"),
	@Result(name = "change", location = "/jsp/maintenance/metagroupmaintenanceadd.jsp"),
	@Result(name = "add", location = "/jsp/maintenance/metagroupmaintenanceadd.jsp"),
	@Result(name = "group", location = "/jsp/maintenance/metagroupmaintenancegroup.jsp"),
	@Result(name = "bookcodes", location = "/jsp/maintenance/groupmaintenancebookcodes.jsp"),
})
/**
 * 
 * This is action class for Metagroup screen.
 * 
 */
public class MetaGroupAction extends CustomActionSupport {
public String execute() throws Exception {
    HttpServletRequest request = ServletActionContext.getRequest();

    // List of methods 
    String method = String.valueOf(request.getParameter("method"));
    switch (method) {
        case "displayListByEntity":
            return displayListByEntity();
        case "add":
            return add();
        case "change":
            return change();
        case "group":
            return group();
        case "update":
            return update();
        case "save":
            return save();
        case "delete":
            return delete();
        case "bookcodes":
            return bookcodes();
        default:
            break;
    }

    return displayListByEntity();
}


private MetaGroup metaGroup;
public MetaGroup getMetaGroup() {
	return metaGroup;
}
public void setMetaGroup(MetaGroup metaGroup) {
	this.metaGroup = metaGroup;
}

private String selectedMetaGroupId;
public String getSelectedMetaGroupId() {
	return selectedMetaGroupId;
}
public void setSelectedMetaGroupId(String selectedMetaGroupId) {
	this.selectedMetaGroupId = selectedMetaGroupId;
}

    @Autowired
	private MetaGroupManager metaGroupManager;

	/**
	 * Final log instance for logging this class
	 */
	private final Log log = LogFactory.getLog(MetaGroupAction.class);

	/**
	 * @param metaGroupManager
	 */
	public void setMetaGroupManager(MetaGroupManager metaGroupManager) {
		this.metaGroupManager = metaGroupManager;
	}

	/**
	 * This method is default method in struts This method is called if no
	 * method are specified in the request
	 * 
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return a Result of type Action forward
	 * @throws SwtException
	 */
	public String unspecified()
			throws SwtException {
		log.debug(this.getClass().getName()
				+ "- [unspecified] - returns displayListByEntity method ");
		return displayListByEntity();
	}

	/**
	 * This method is used to load the metagroup details in screen.
	 * 
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return Action forward
	 * @throws SwtException
	 */
	public String displayListByEntity() throws SwtException {
	HttpServletRequest request = ServletActionContext.getRequest();
HttpServletResponse response = ServletActionContext.getResponse();
	try {
			log.debug(this.getClass().getName()
					+ "- [displayListByEntity] - Entering");
			/* Method's Local variable declaration */
			String entityId;
			String hostId;
			Collection metaGroupCol;
			int accessInd;
			/* Class Instance Declaration */
			Iterator itrlevel;
			SystemInfo systemInfo;
			Integer mgroupLevelCode;
			LabelValueBean labelValueBean;
			MetaGroup metaGroup;
			Collection metaGrpLvlList;
			Collection coll;
			if(this.metaGroup != null)
				metaGroup = this.metaGroup;
			else
				metaGroup = new MetaGroup();
			/* Read the entity id from request */
			entityId = metaGroup.getId().getEntityId();
			/* Condition to check entity is equal to null */
			if ((entityId == null) || (entityId.trim().length() <= 0)) {
				/* Retrieve the user's selected entity */
				entityId = SwtUtil.getUserCurrentEntity(request.getSession());
				/* Setting the selected entity */
				metaGroup.getId().setEntityId(entityId);
			}
			hostId = SwtUtil.getCurrentHostId();
			/* To put the meta group level code in request */
			putMgrpLvlCodeListInReq(request, hostId, entityId);
			systemInfo = new SystemInfo();
			/* Reading the level code from request */
			mgroupLevelCode = metaGroup.getMgrpLvlCode();
			/* Condition to check level code is null */
			if (mgroupLevelCode == null) {
				labelValueBean = new LabelValueBean();
				/* Get the meta group level list in a collection variable */
				metaGrpLvlList = (Collection) request
						.getAttribute("metaGrpLvlList");
				itrlevel = metaGrpLvlList.iterator();
				/* Condition to check meta group level list is not equal to null */
				if ((metaGrpLvlList != null) && metaGrpLvlList.size() > 0) {
					if (itrlevel.hasNext()) {
						labelValueBean = (LabelValueBean) (itrlevel.next());
						/* Setting the meta group level code using bean class */
						metaGroup.setMgrpLvlCode(new Integer(labelValueBean
								.getValue()));
						mgroupLevelCode = metaGroup.getMgrpLvlCode();
					}
				}
				

			}	 
			coll = SwtUtil.getUserEntityAccessList(request.getSession());
			/*
			 * Retrieve and Store the user's menu,entity and currency group from
			 * swtutil file
			 */
			accessInd = SwtUtil.getMenuEntityCurrGrpAccess(request, entityId,
					null);
			/*
			 * Condition to check the access rights. If value=0 ->Full access If
			 * Value=1 ->View access
			 * 
			 */
			/* This is used to set the button status by enable all buttons */
			if (accessInd == 0) {
				setButtonStatus(request, SwtConstants.STR_TRUE,
						SwtConstants.STR_FALSE, SwtConstants.STR_FALSE);
				request.setAttribute("EntityAccess",
						SwtConstants.ENTITY_FULL_ACCESS + "");
				/* This is used to set the button status by disable all buttons */
			} else {
				setButtonStatus(request, SwtConstants.STR_FALSE,
						SwtConstants.STR_FALSE, SwtConstants.STR_FALSE);
				request.setAttribute("EntityAccess",
						SwtConstants.ENTITY_READ_ACCESS + "");
			}
			/* Setting host id using bean class */
			metaGroup.getId().setHostId(hostId);
			systemInfo = new SystemInfo();
			/*
			 * Retrieve the meta group details from DB based on the parameters
			 * passed.
			 */
			metaGroupCol = metaGroupManager.getMetaGroupDetails(hostId,
					entityId, mgroupLevelCode);

			request.setAttribute("metaGroupListDetails", metaGroupCol);
			/* Reading meta group level values from request */
			metaGrpLvlList = (Collection) request
					.getAttribute("metaGrpLvlList");
			/* Condition to check meta group list is null */
			if (metaGrpLvlList == null || metaGrpLvlList.size() == 0) {
				request.setAttribute("disableAdd", "Y");
			}
			/* Used to put the entity list in request */
			putEntityListInReq(request);
			this.metaGroup= metaGroup;
			log.debug(this.getClass().getName()
					+ "- [displayListByEntity] - Exiting");
			return ("success");
		} catch (SwtException swtexp) {
			log.debug(this.getClass().getName()
							+ " - Exception Catched in [displayListByEntity] method : - "
							+ swtexp.getMessage());

			log.error(this.getClass().getName()
							+ " - Exception Catched in [displayListByEntity] method : - "
							+ swtexp.getMessage());

			swtexp.printStackTrace();
			SwtUtil.logException(swtexp, request, "");
			return ("fail");
		} catch (Exception exp) {
			log.debug(this.getClass().getName()
							+ " - Exception Catched in [displayListByEntity] method : - "
							+ exp.getMessage());

			log.error(this.getClass().getName()
							+ " - Exception Catched in [displayListByEntity] method : - "
							+ exp.getMessage());
			exp.printStackTrace();
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "displayListByEntity", MetaGroupAction.class),
					request, "");
			return ("fail");
		}
	}

	/**
	 * This is used to add the new meta group details.
	 * 
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return ActionForward
	 * @throws SwtException
	 */
	public String add()
			throws SwtException {
	HttpServletRequest request = ServletActionContext.getRequest();
HttpServletResponse response = ServletActionContext.getResponse();
	try {
			log.debug(this.getClass().getName() + "- [add] - Entering");
			/* Method's local variable declaration */
			String entityName;
			String entityId;
			String mgrpLvlName;
			String mgrpLvlCode;
			String hostId;
			/* Class instance declaration */
			MetaGroup metaGroup;
			request.setAttribute("screenFieldsStatus", "true");
			if(this.metaGroup!=null)
				metaGroup = this.metaGroup;
			else
				metaGroup = new MetaGroup();
			entityName = request.getParameter("entityName");
			entityId = request.getParameter("entityCode");
			mgrpLvlName = request.getParameter("metaGroupLvlName");
			mgrpLvlCode = request.getParameter("metaGroupLvlCode");
			/* Setting the entity id */
			metaGroup.getId().setEntityId(entityId);
			/* Setting Meta group level code */
			metaGroup.setMgrpLvlCode(new Integer(mgrpLvlCode));
			/* Condition to check entity id is null */
			if ((entityId == null) || (entityId.trim().length() <= 0)) {
				/* Getting user's current entity from session */
				entityId = SwtUtil.getUserCurrentEntity(request.getSession());
				/* Setting the entity Id */
				metaGroup.getId().setEntityId(entityId);
			}
			/* Setting the meta group Cateogry as Finance */
			metaGroup.setDisplayedFinanceTrade("F");
			this.metaGroup= metaGroup;
			hostId = CacheManager.getInstance().getHostId();
			log.debug(this.getClass().getName() + "- [add] - Entity Id-->"
					+ entityId);
			request.setAttribute("methodName", "add");
			request.setAttribute("entityName", entityName);
			request.setAttribute("mgrpLvlName", mgrpLvlName);
			log.debug(this.getClass().getName() + "- [add] - Exiting");
			return ("add");
		} catch (SwtException swtexp) {
			log.debug(this.getClass().getName()
					+ " - Exception Catched in [add] method : - "
					+ swtexp.getMessage());

			log.error(this.getClass().getName()
					+ " - Exception Catched in [add] method : - "
					+ swtexp.getMessage());
			swtexp.printStackTrace();
			SwtUtil.logException(swtexp, request, "");
			return ("fail");
		} catch (Exception exp) {
			log.debug(this.getClass().getName()
					+ " - Exception Catched in [add] method : - "
					+ exp.getMessage());

			log.error(this.getClass().getName()
					+ " - Exception Catched in [add] method : - "
					+ exp.getMessage());
			exp.printStackTrace();
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "add", MetaGroupAction.class), request, "");
			return ("fail");
		}
	}

	/**
	 * This is used to update the meta group details
	 * 
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return
	 * @throws SwtException
	 */
	public String change()
			throws SwtException {
	HttpServletRequest request = ServletActionContext.getRequest();
HttpServletResponse response = ServletActionContext.getResponse();
	try {
			log.debug(this.getClass().getName() + "- [change] - Entering");
			/* Method's local variable declaration */
			String entityId;
			String mgroupId;
			String entityName;
			String hostId;
			String mgrpLvlName;
			String mgrpLvlCode;
			String oldVal;
			/* Class instance declaration */
			SystemInfo systemInfo;
			MetaGroup metaGroup;
			if(this.metaGroup!=null)
				metaGroup = this.metaGroup;
			else
				metaGroup = new MetaGroup();
			request.setAttribute("screenFieldsStatus", "true");
			entityId = request.getParameter("entityCode");
			mgroupId = request.getParameter("selectedMetaGroupId");
			entityName = request.getParameter("entityName");
			hostId = CacheManager.getInstance().getHostId();
			mgrpLvlName = request.getParameter("metaGroupLvlName");
			mgrpLvlCode = request.getParameter("metaGroupLvlCode");
			systemInfo = new SystemInfo();
			/* This is used to put the entity list in request */
			putEntityListInReq(request);
			/*
			 * Change the meta group details in DB based on the parameters
			 * passed.
			 */
			metaGroup = metaGroupManager.change(hostId, entityId, mgroupId);

			/* Appending Meta group details in a buffer */
			oldVal = new StringBuffer("MetaGroup Name=").append(
					metaGroup.getMgroupName()).append("^MetaGroup Level ID=")
					.append(metaGroup.getMgrpLvlCode()).append(
							"^Finance/Trade=").append(
							metaGroup.getFinanceTrade()).toString();
			this.metaGroup= metaGroup;
			request.setAttribute("methodName", "change");
			log.debug(this.getClass().getName() + "- [change] - Exiting");
			request.setAttribute("oldValue", oldVal);
			request.setAttribute("entityName", entityName);
			request.setAttribute("mgrpLvlName", mgrpLvlName);
			return ("change");
		} catch (SwtException swtexp) {
			log.debug(this.getClass().getName()
					+ " - Exception Catched in [change] method : - "
					+ swtexp.getMessage());

			log.error(this.getClass().getName()
					+ " - Exception Catched in [change] method : - "
					+ swtexp.getMessage());
			swtexp.printStackTrace();
			SwtUtil.logException(swtexp, request, "");

			return ("fail");
		} catch (Exception exp) {
			log.debug(this.getClass().getName()
					+ " - Exception Catched in [change] method : - "
					+ exp.getMessage());

			log.error(this.getClass().getName()
					+ " - Exception Catched in [change] method : - "
					+ exp.getMessage());

			exp.printStackTrace();
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "change", MetaGroupAction.class), request, "");
			return ("fail");
		}
	} // End of change method

	/**
	 * This is used to add the associated groups for entity.
	 * 
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return ActionForward
	 * @throws SwtException
	 */
	public String group()
			throws SwtException {
	HttpServletRequest request = ServletActionContext.getRequest();
HttpServletResponse response = ServletActionContext.getResponse();
	try {
			log.debug(this.getClass().getName() + "- [group] - Entering");
			/* Class instance declaration */
			MetaGroup metaGroup;
			Collection groupColl;
			SystemInfo systemInfo;
			/* Method's local variable declaration */
			String entityId;
			String entityName;
			String mgroupId;
			String hostId;
			if(this.metaGroup!=null)
				metaGroup = this.metaGroup;
			else
				metaGroup = new MetaGroup();
			systemInfo = new SystemInfo();
			request.setAttribute("screenFieldsStatus", "true");
			entityId = request.getParameter("entityCode");
			mgroupId = request.getParameter("selectedMetaGroupId");
			entityName = request.getParameter("entityName");
			/* Setting the entity id */
			metaGroup.getId().setEntityId(entityId);
			/* Retrieve and store hostId from CacheManager */
			hostId = CacheManager.getInstance().getHostId();
			/* Setting the host id */
			metaGroup.getId().setHostId(hostId);
			/* Retrieve group details from DB based on the parameters passed. */
			groupColl = metaGroupManager.groupDetails(hostId, entityId,
					mgroupId);
			request.getSession().setAttribute("entityId", entityId);
			request.setAttribute("groupListDetails", groupColl);
			return ("group");
		} catch (SwtException swtexp) {
			log.debug(this.getClass().getName()
					+ " - Exception Catched in [group] method : - "
					+ swtexp.getMessage());

			log.error(this.getClass().getName()
					+ " - Exception Catched in [group] method : - "
					+ swtexp.getMessage());
			swtexp.printStackTrace();
			SwtUtil.logException(swtexp, request, "");
			return ("fail");
		} catch (Exception exp) {
			log.debug(this.getClass().getName()
					+ " - Exception Catched in [group] method : - "
					+ exp.getMessage());

			log.error(this.getClass().getName()
					+ " - Exception Catched in [group] method : - "
					+ exp.getMessage());
			exp.printStackTrace();
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "group", MetaGroupAction.class), request, "");
			return ("fail");
		}
	}

	/**
	 * This is used to save the updated meta group details
	 * 
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return ActionForward
	 * @throws SwtException
	 */
	public String update()
			throws SwtException {
	HttpServletRequest request = ServletActionContext.getRequest();
HttpServletResponse response = ServletActionContext.getResponse();
	log.debug(this.getClass().getName() + "- [update] - Entering");
		/* Method's local variable declaration */
		String hostId;
		String entityId;
		String oldValue;
		String newValue;
		String userId;
		/* Class instance declaration */
		ActionErrors errors;
		SystemInfo systemInfo;
		errors = new ActionErrors();
		MetaGroup metaGroup;
		request.setAttribute("screenFieldsStatus", "true");
		if(this.metaGroup!=null)
			metaGroup = this.metaGroup;
		else
			metaGroup = new MetaGroup();
		hostId = CacheManager.getInstance().getHostId();
		/* Setting the host id */
		metaGroup.getId().setHostId(hostId);
		/* Getting entity id from bean class */
		entityId = metaGroup.getId().getEntityId();
		systemInfo = new SystemInfo();
		try {
			/* Get the old value from request */
			oldValue = request.getParameter("oldValue");
			log.debug(this.getClass().getName() + "- [update] -Old value-->"
					+ oldValue);
			newValue = new StringBuffer("MetaGroup Name=").append(
					metaGroup.getMgroupName()).append("^MetaGroup Level ID=")
					.append(metaGroup.getMgrpLvlCode()).append(
							"^Finance/Trade=").append(
							metaGroup.getFinanceTrade()).toString();
			/* Reading the user's id from session */
			userId = SwtUtil.getCurrentUserId(request.getSession());
			/* Reading the update user id */
			metaGroup.setUpdateUser(userId);
			/* Reading and setting the user id */
			metaGroup.getId().setHostId(
					SwtUtil.getCurrentHostId(request.getSession()));
			/* Getting & Setting the ip address */
			systemInfo.setIpAddress(request.getRemoteAddr());
			/* Setting the old value */
			systemInfo.setOldLogString(oldValue);
			/* Setting the new value */
			systemInfo.setNewLogString(newValue);
			/* Used to update the metagroup details in DB. */
			metaGroupManager.updateMetaGroupDetails(metaGroup);
			/* Used to put the entity list in request */
			putEntityListInReq(request);
			putMgrpLvlCodeListInReq(request, hostId, entityId);
			request.setAttribute("parentFormRefresh", "yes");
			log.debug(this.getClass().getName() + "- [update] - Exiting");
			this.metaGroup= metaGroup;
			return ("change");
		} catch (SwtException swtexp) {
			log.debug(this.getClass().getName()
					+ " - Exception Catched in [update] method : - "
					+ swtexp.getMessage());

			log.error(this.getClass().getName()
					+ " - Exception Catched in [update] method : - "
					+ swtexp.getMessage());
			request.setAttribute("methodName", "update");
			/* Used to put the entity list in request */
			putEntityListInReq(request);
			/* Used to put the metagroup level code in request */
			putMgrpLvlCodeListInReq(request, hostId, entityId);
			request.setAttribute("screenFieldsStatus", "");
			metaGroup.getId().setHostId(hostId);
			swtexp.printStackTrace();
			saveErrors(request, SwtUtil.logException(swtexp, request, ""));
			return ("change");
		} catch (Exception exp) {
			log.debug(this.getClass().getName()
					+ " - Exception Catched in [update] method : - "
					+ exp.getMessage());
			log.error(this.getClass().getName()
					+ " - Exception Catched in [update] method : - "
					+ exp.getMessage());
			exp.printStackTrace();
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "update", MetaGroupAction.class), request, "");
			return ("fail");
		}
	}

	/**
	 * This is used to save the newly added meta group details.
	 * 
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return ActionForward
	 * @throws SwtException
	 */
	public String save()
			throws SwtException {
	HttpServletRequest request = ServletActionContext.getRequest();
    HttpServletResponse response = ServletActionContext.getResponse();
	log.debug(this.getClass().getName() + "- [save] - Entering");
		/* Class instance declaration */
		ActionMessages errors=null;
		MetaGroup metaGroup=null;;
		/* Method's local variable declaration */
		String hostId=null;
		String entityId=null;;
		
		try {
			errors = new ActionMessages();
			if(this.metaGroup!=null)
				metaGroup = this.metaGroup;
			else
				metaGroup = new MetaGroup();
			/* Retrieve and store hostId from CacheManager */
			hostId = CacheManager.getInstance().getHostId();
			/* Setting the host id */
			metaGroup.getId().setHostId(hostId);
			entityId = metaGroup.getId().getEntityId();
			/* Used to save the meta group details in database */
			metaGroupManager.saveMetaGroup(metaGroup);
			/* Used to put the entity list in request */
			putEntityListInReq(request);
			putMgrpLvlCodeListInReq(request, hostId, entityId);
			this.metaGroup= metaGroup;
			request.setAttribute("parentFormRefresh", "yes");
			log.debug(this.getClass().getName() + "- [save] - Exiting");
			request.setAttribute("methodName", "displayListByEntity");
			/* Calling the method to load the details */
			return displayListByEntity();
		} catch (SwtException swtexp) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [add] method : - "
					+ swtexp.getMessage());
			request.setAttribute("methodName", "add");
			putEntityListInReq(request);
			putMgrpLvlCodeListInReq(request, hostId, entityId);
			request.setAttribute("screenFieldsStatus", "");
			putEntityListInReq(request);
			putMgrpLvlCodeListInReq(request, hostId, entityId);
			request.setAttribute("screenFieldsStatus", "true");
			request.setAttribute("methodName", "add");
			request.setAttribute("entityName", request
					.getParameter("entityName"));
			saveErrors(request, SwtUtil.logException(swtexp, request, ""));
			/*
			 * Start : Modified for Mantis 1366-Remove entry to log if duplicate
			 * records added by betcy on 18-02-2011
			 */
			if (swtexp.getErrorCode().equals(
					"errors.DataIntegrityViolationExceptioninAdd")) {
				if (errors != null) {
					errors.add("", new ActionMessage(swtexp.getErrorCode()));
				}
				saveErrors(request, errors);

			} else {
				saveErrors(request, SwtUtil.logException(swtexp, request, ""));
			}
			/*
			 * End : Modified for Mantis 1366-Remove entry to log if duplicate
			 * records added by betcy on 18-02-2011
			 */

			return ("add");
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [add] method : - "
					+ exp.getMessage());

			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "save", MetaGroupAction.class), request, "");

			return ("fail");
		}
	}

	/**
	 * This is used to delete the metagroup details from DB.
	 * 
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return
	 * @throws SwtException
	 */
	public String delete()
			throws SwtException {
	HttpServletRequest request = ServletActionContext.getRequest();
HttpServletResponse response = ServletActionContext.getResponse();
	/* Method's local variable declaration */
		String hostId;
		/* Class instance declaration */
		ActionErrors errors;
		errors = new ActionErrors();
		MetaGroup metaGroup;
		if(this.metaGroup!=null)
			metaGroup = this.metaGroup;
		else
			metaGroup = new MetaGroup();
		String entityId = metaGroup.getId().getEntityId();
		String mgroupId = request.getParameter("selectedMetaGroupId");
		hostId = CacheManager.getInstance().getHostId();
		metaGroup.getId().setHostId(hostId);
		Integer mgroupLevelCode = metaGroup.getMgrpLvlCode();
		SystemInfo systemInfo = new SystemInfo();
		try {
			log.debug("Entering delete method");
			request.setAttribute("screenFieldsStatus", "true");
			metaGroup.getId().setMgroupId(mgroupId);
			log.debug("entityId ->>" + entityId + " mgroupId--->" + mgroupId);
			/* Delete the records from DB based on the parameters passed */
			metaGroupManager.deleteMetaGroup(metaGroup);
			putEntityListInReq(request);
			this.metaGroup= metaGroup;
			/* Used to put the meta group level code in request */
			putMgrpLvlCodeListInReq(request, hostId, entityId);
			/*
			 * Fetches the meta group information from DB based on the
			 * parameters passed
			 */
			Collection metaGroupCol = metaGroupManager.getMetaGroupDetails(
					hostId, entityId, mgroupLevelCode);

			request.setAttribute("metaGroupListDetails", metaGroupCol);
			request.setAttribute("methodName", "displayListByEntity");
			return displayListByEntity();
		} catch (SwtException swtexp) {
			log.debug(this.getClass().getName()
					+ " - Exception Catched in [delete] method : - "
					+ swtexp.getMessage());

			log.error(this.getClass().getName()
					+ " - Exception Catched in [delete] method : - "
					+ swtexp.getMessage());

			request.setAttribute("methodName", "delete");
			putEntityListInReq(request);
			metaGroup.getId().setHostId(hostId);
			putMgrpLvlCodeListInReq(request, hostId, entityId);

			Collection metaGroupCol = metaGroupManager.getMetaGroupDetails(
					hostId, entityId, mgroupLevelCode);
			request.setAttribute("metaGroupListDetails", metaGroupCol);
			request.setAttribute("screenFieldsStatus", "");
			saveErrors(request, SwtUtil.logException(swtexp, request, ""));

			return displayListByEntity();
		} catch (Exception exp) {
			log.debug(this.getClass().getName()
					+ " - Exception Catched in [delete] method : - "
					+ exp.getMessage());

			log.error(this.getClass().getName()
					+ " - Exception Catched in [delete] method : - "
					+ exp.getMessage());

			exp.printStackTrace();
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "delete", MetaGroupAction.class), request, "");
			return ("fail");
		}
	}

	/**
	 * This is used to view the Book details for associated metagroup
	 * 
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return ActionForward
	 * @throws SwtException
	 */
	public String bookcodes()
			throws SwtException {
	HttpServletRequest request = ServletActionContext.getRequest();
HttpServletResponse response = ServletActionContext.getResponse();
	try {
			log.debug(this.getClass().getName() + "- [bookcodes] - Entering");
			/* Method's local variable declaration */
			String hostId;
			String entityId;
			String groupCode;
			MetaGroup metaGroup= null;
			if(this.metaGroup!=null)
				metaGroup = this.metaGroup;
			else
				metaGroup = new MetaGroup();
			Group group = new Group();
			SystemInfo systemInfo = new SystemInfo();
			/* Retrieve and store hostId from CacheManager */
			hostId = CacheManager.getInstance().getHostId();
			entityId = (String) request.getSession().getAttribute("entityId");
			groupCode = request.getParameter("selectedgroupId");
			/* Used to put the book code list in request */
			putBookCodeListInReq(request, entityId, groupCode);
			log.debug(this.getClass().getName() + "- [bookcodes] - Entering");
			return ("bookcodes");
		} catch (SwtException swtexp) {
			log.debug(this.getClass().getName()
					+ " - Exception Catched in [bookcodes] method : - "
					+ swtexp.getMessage());

			log.error(this.getClass().getName()
					+ " - Exception Catched in [bookcodes] method : - "
					+ swtexp.getMessage());
			SwtUtil.logException(swtexp, request, "");

			return ("fail");
		} catch (Exception exp) {
			log.debug(this.getClass().getName()
					+ " - Exception Catched in [bookcodes] method : - "
					+ exp.getMessage());

			log.error(this.getClass().getName()
					+ " - Exception Catched in [bookcodes] method : - "
					+ exp.getMessage());
			exp.printStackTrace();
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "bookcodes", MetaGroupAction.class), request, "");

			return ("fail");
		}
	}

	/**
	 * This is used to put the entity list in request
	 * 
	 * @param request
	 * @return
	 * @throws SwtException
	 */
	private void putEntityListInReq(HttpServletRequest request)
			throws SwtException {

	try {
			log.debug(this.getClass().getName()
					+ "- [putEntityListInReq] - Entering");
			HttpSession session = request.getSession();
			Collection coll = SwtUtil.getUserEntityAccessList(session);
			coll = SwtUtil.convertEntityAcessCollectionLVL(coll, session);
			request.setAttribute("entities", coll);
			log.debug(this.getClass().getName()
					+ "- [putEntityListInReq] - Exiting");
		} catch (Exception exp) {
			log.debug(this.getClass().getName()
							+ " - Exception Catched in [putEntityListInReq] method : - "
							+ exp.getMessage());

			log.error(this.getClass().getName()
							+ " - Exception Catched in [putEntityListInReq] method : - "
							+ exp.getMessage());
			exp.printStackTrace();
			throw SwtErrorHandler.getInstance().handleException(exp,
					"putEntityListInReq", MetaGroupAction.class);
		}
	}

	/**
	 * This method is used to put the Meta group list in request
	 * 
	 * @param request
	 * @param hostId
	 * @param entityId
	 * @return
	 * @throws SwtExceptio
	 */
	public void putMgrpLvlCodeListInReq(HttpServletRequest request,
			String hostId, String entityId) throws SwtException {
		try {
			log.debug(this.getClass().getName()
					+ "- [putMgrpLvlCodeListInReq] - Entering");
			/* Class instance declaration */
			SystemInfo systemInfo;
			Collection metaGrpLvlList = null;
			/* Method local variable declaration */
			systemInfo = new SystemInfo();
			metaGrpLvlList = new ArrayList();
			/*
			 * This method is used to get the metagroup level code from DB based
			 * on the parameter values passed.
			 */
			metaGrpLvlList = metaGroupManager.getMgrpLvlCodeList(hostId,
					entityId);

			request.setAttribute("metaGrpLvlList", metaGrpLvlList);
			log.debug(this.getClass().getName()
					+ "- [putMgrpLvlCodeListInReq] - Exiting");
		} catch (Exception exp) {
			log.debug(this.getClass().getName()
							+ " - Exception Catched in [putMgrpLvlCodeListInReq] method : - "
							+ exp.getMessage());
			log.error(this.getClass().getName()
							+ " - Exception Catched in [putMgrpLvlCodeListInReq] method : - "
							+ exp.getMessage());
			exp.printStackTrace();
			throw SwtErrorHandler.getInstance().handleException(exp,
					"putMgrpLvlCodeListInReq", MetaGroupAction.class);
		}
	}

	/**
	 * This method is used to put Book code list in request
	 * 
	 * @param request
	 * @param entityId
	 * @param groupId
	 * @return
	 * @throws SwtException
	 */
	private void putBookCodeListInReq(HttpServletRequest request,
			String entityId, String groupId) throws SwtException {
		try {
			log.debug(this.getClass().getName()
					+ "- [putBookCodeListInReq] - Entering");
			/* Method's local variable declaration */
			String hostId;
			Collection collBookCode = null;
			/* Class instance declaration */
			SystemInfo systemInfo = new SystemInfo();
			BookCodeManager bookCodeManager = (BookCodeManager) (SwtUtil
					.getBean("bookCodeManager"));
			/* Retrieve and store hostId from CacheManager */
			hostId = CacheManager.getInstance().getHostId();
			collBookCode = bookCodeManager.getBookCodeList(hostId, entityId,
					groupId);
			log.debug(this.getClass().getName()
					+ "- [putBookCodeListInReq] - Exiting");
			request.setAttribute("bookCode", collBookCode);
		} catch (Exception exp) {
			log.debug(this.getClass().getName()
							+ " - Exception Catched in [putBookCodeListInReq] method : - "
							+ exp.getMessage());

			log.error(this.getClass().getName()
							+ " - Exception Catched in [putBookCodeListInReq] method : - "
							+ exp.getMessage());
			exp.printStackTrace();
			throw SwtErrorHandler.getInstance().handleException(exp,
					"putBookCodeListInReq", MetaGroupAction.class);
		}
	}

	/**
	 * This function is used to set the status of button
	 * 
	 * @param req
	 * @param addStatus
	 * @param changeStatus
	 * @param deleteStatus
	 * @return
	 */
	private void setButtonStatus(HttpServletRequest req, String addStatus,
			String changeStatus, String deleteStatus) {
		log.debug(this.getClass().getName() + "- [setButtonStatus] - Entering");
		req.setAttribute(SwtConstants.ADD_BUT_STS, addStatus);
		req.setAttribute(SwtConstants.CHG_BUT_STS, changeStatus);
		req.setAttribute(SwtConstants.DEL_BUT_STS, deleteStatus);
		log.debug(this.getClass().getName() + "- [setButtonStatus] - Exiting");
	}
}
