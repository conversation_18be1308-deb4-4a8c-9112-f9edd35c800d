package org.swallow.maintenance.web;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Iterator;
import java.util.Map;
import java.util.StringTokenizer;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.apache.struts2.ServletActionContext;
import org.apache.struts2.convention.annotation.Action;
import org.apache.struts2.convention.annotation.AllowedMethods;
import org.apache.struts2.convention.annotation.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.maintenance.model.CorrespAccountSummary;
import org.swallow.maintenance.model.CorrespondentAcct;
import org.swallow.maintenance.service.CorrespondentAcctMaintenanceManager;
import org.swallow.util.CacheManager;
import org.swallow.util.CommonDataManager;
import org.swallow.util.LabelValueBean;
import org.swallow.util.SwtConstants;
import org.swallow.util.SwtUtil;
import org.swallow.util.struts.ActionMessage;
import org.swallow.util.struts.ActionMessages;
import org.swallow.util.struts.CustomActionSupport;

/**
 * <AUTHOR> This is action class for Correspondent Account maintenance
 * 
 */
@Action(value = "/correspondentacctmaintenance", results = {
	@Result(name = "display", location = "/jsp/maintenance/correspondentacctmaintenance.jsp"),
	@Result(name = "add", location = "/jsp/maintenance/correspondentacctmaintenanceadd.jsp"),
	@Result(name = "change", location = "/jsp/maintenance/correspondentacctmaintenanceadd.jsp"),
	@Result(name = "fail", location = "/error.jsp"),
})

@AllowedMethods ({"display" ,"displayFilteredResults" ,"delete" ,"add" ,"refreshAccountList" ,"save" ,"modify" ,"change" })
public class CorrespondentAcctMaintenanceAction extends CustomActionSupport {
public String execute() throws Exception {
    HttpServletRequest request = ServletActionContext.getRequest();

    // List of methods 
    String method = String.valueOf(request.getParameter("method"));

    switch (method) {
        case "unspecified":
            return unspecified();
        case "display":
            return display();
        case "displayFilteredResults":
            return displayFilteredResults();
        case "delete":
            return delete();
        case "add":
            return add();
        case "refreshAccountList":
            return refreshAccountList();
        case "save":
            return save();
        case "modify":
            return modify();
        case "change":
            return change();
        default:
            break;
    }

    return unspecified();
}



	private final Log log = LogFactory
			.getLog(CorrespondentAcctMaintenanceAction.class);
	@Autowired
private CorrespondentAcctMaintenanceManager correspondentAcctMaintenanceManager = null;

	/**
	 * @param correspondentAcctMaintenanceManager
	 *            the correspondentAcctMaintenanceManager to set
	 */
	public void setCorrespondentAcctMaintenanceManager(
			CorrespondentAcctMaintenanceManager correspondentAcctMaintenanceManager) {
		this.correspondentAcctMaintenanceManager = correspondentAcctMaintenanceManager;
	}
	
	private CorrespondentAcct correspondentAcct;
	public CorrespondentAcct getCorrespondentAcct() {
		if (correspondentAcct == null) {
			correspondentAcct = new CorrespondentAcct();
		}
		return correspondentAcct;
	}
	public void setCorrespondentAcct(CorrespondentAcct correspondentAcct) {
		this.correspondentAcct = correspondentAcct;
		HttpServletRequest request = ServletActionContext.getRequest();
		request.setAttribute("correspondentAcct", correspondentAcct);
	}

	/**
	 * This method will be called when no 'method' paramater specified, ie when
	 * menu item is clicked.
	 * 
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return ActionForward
	 * @throws Exception
	 */
	public String unspecified()
			throws Exception {
		return display();
	}

	/**
	/**
	 * Action method called to show the tabular list of existing records
	 * 
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return ActionForward
	 * @throws Exception
	 */
	public String display()
			throws Exception {
		log.debug("Entering CorrespondentAcctMaintenanceAction.display method");
		int pageSize;
		int currentPage = 1;
		int startRowNumber;
		int endRowNumber;
		int maxPage;
		int totalCount;
		String clckPageStr = null;
		int clickedPage = 0;
		String nextLinkStatus = null;
		String prevLinkStatus = null;
		String selectedSort = null;
		String userId = null;
		ArrayList<CorrespAccountSummary> pageSummaryList = null;
		HttpServletRequest request = ServletActionContext.getRequest();
		try {
			CorrespondentAcct correspondentAcct = this.getCorrespondentAcct();
			 userId = SwtUtil.getCurrentUserId(request.getSession());
			/* String variable to hold default currency code */
			final String DEFAULTCURRENCYCODE = "All";
			String entityId = correspondentAcct.getId().getEntityId();
			String currencyCode = correspondentAcct.getId().getCurrencyCode();
			/*
			 * Read page size from swtcommon.properties
			 */
			pageSize=SwtUtil.getPageSizeFromProperty(SwtConstants.DEFAULT_SYSTEM_SCREEN_PAGE_SIZE);
			String currentPageAsString = request.getParameter("currentPage");
			if (currentPageAsString != null && currentPageAsString.length() > 0) {
				currentPage = Integer.parseInt(currentPageAsString);
			} else {
				currentPage = 1;
			}
			clckPageStr = request.getParameter("goToPageNo");
			/* Condition to check currPageStr not null and clckPageStr is null */
			if ((!SwtUtil.isEmptyOrNull(currentPageAsString) ) && (clckPageStr == null)) {
				/*
				 * If the condition satisfies both, the value of clickedPage and
				 * currentPage is loaded as current page value.
				 */
				currentPage = Integer.parseInt(request
						.getParameter("currentPage"));
				clickedPage = Integer.parseInt(request
						.getParameter("currentPage"));
			} else {
				/*
				 * If the condition not satisfies, the value of currentPage and
				 * clickedPage is loaded as current page and pageNoValue
				 * respectively
				 */
				if(SwtUtil.isEmptyOrNull(currentPageAsString)) {
					currentPage = 1;
				} else {
					currentPage = Integer.parseInt(request.getParameter("currentPage"));
					clickedPage = Integer.parseInt(request
							.getParameter("goToPageNo"));
					/* Condition to check whether clicked value is previous or next */
					if (clickedPage == -2) { // Previous Link Clicked
						currentPage--;
						clickedPage = currentPage;
					} else {
						if (clickedPage == -1) { // Next Link Clicked
							currentPage++;
							clickedPage = currentPage;
						} else {
							currentPage = clickedPage;
						}
						
						
					}
					if (clickedPage > 1) {
						
						prevLinkStatus = "true";
					} else {
						prevLinkStatus = "false";
					}
				}
				
				
				
			}

			startRowNumber = 0;
			endRowNumber = pageSize;
			maxPage = 0;
			totalCount = 0;
			endRowNumber = currentPage * pageSize;
			/* Get the start row number of each page */
			if(endRowNumber == pageSize) 
				startRowNumber = endRowNumber - pageSize;
			else
			   startRowNumber = endRowNumber - pageSize +1;
			// get the host id
			CacheManager cacheManagerInst = CacheManager.getInstance();
			String hostId = cacheManagerInst.getHostId();
			correspondentAcct.getId().setHostId(hostId);

			// Check if entity id is null. If yes, then set it to the default
			// entity id.
			if (entityId == null) {
				entityId = ((request.getParameter("entityId") != null) && (request
						.getParameter("entityId").trim().length() > 0)) ? request
						.getParameter("entityId")
						: SwtUtil.getUserCurrentEntity(request.getSession());
			}

			// Set the default entity id or the entity id present in the form,
			// if not null.
			correspondentAcct.getId().setEntityId(entityId);

			// Getting the currency code from request. If it is not null then it
			// will assign that currencyCode.otherwise sets the default
			// currencyCode as ALL.
			if (currencyCode == null) {
				currencyCode = ((request.getParameter("currencyCode") != null) && (request
						.getParameter("currencyCode").trim().length() > 0)) ? request
						.getParameter("currencyCode")
						: DEFAULTCURRENCYCODE;
			}
			correspondentAcct.getId().setCurrencyCode(currencyCode);

			// This method is used to put collection of entity list into
			// request.
			putEntityListInReq(request);

			// Put the currencies list in the form
			Collection<LabelValueBean> currencyList = putCurrencyListInRequest(
					request, entityId, false);
			request.setAttribute("currencyName", getLabelFromValue(
					currencyList, currencyCode));

			// setup button access permissions
			if (SwtUtil.getMenuEntityCurrGrpAccess(request, entityId, null) == 0) {
				setButtonStatus(request, SwtConstants.STR_TRUE,
						SwtConstants.STR_FALSE, SwtConstants.STR_FALSE,
						SwtConstants.STR_FALSE, SwtConstants.STR_TRUE);

				request.setAttribute("EntityAccess",
						SwtConstants.ENTITY_FULL_ACCESS);
			} else {
				setButtonStatus(request, SwtConstants.STR_FALSE,
						SwtConstants.STR_FALSE, SwtConstants.STR_FALSE,
						SwtConstants.STR_FALSE, SwtConstants.STR_TRUE);

				request.setAttribute("EntityAccess",
						SwtConstants.ENTITY_READ_ACCESS);
			}

			// Get the message types list to populate the message types dropdown
			Collection<String> messageTypes = correspondentAcctMaintenanceManager
					.getMessageTypesDropdownValues();
			putMessageTypesListInRequest(request, messageTypes);
			CorrespondentAcct acct = this.getCorrespondentAcct();
			String messageType = acct.getId().getMessageType();
			String otherMessageType = acct.getOtherMessageType();
			if (otherMessageType != null && !"".equals(otherMessageType.trim())) {
				messageType = otherMessageType;
			}
			// put the list view data in the request
			Collection  acctList = null;
			selectedSort = request.getParameter("selectedSort");
			acctList = correspondentAcctMaintenanceManager.getList(entityId,
					hostId,startRowNumber, endRowNumber, messageType, currencyCode, userId, selectedSort );
			Iterator<CorrespondentAcct> itr = acctList.iterator();
			CorrespondentAcct corrAcct = new CorrespondentAcct();
			while (itr.hasNext()) {
				corrAcct = itr.next();
				/* Get the total row count from the collection */
				totalCount = corrAcct.getRowCount();
				break;
			}
			
			//System.err.println("acctList"+acctList);
			request.setAttribute("listViewData",acctList);
			maxPage = setMaxPageAttribute(totalCount, pageSize);
			if (maxPage <= 1) {
				request.setAttribute("hidePagination", true);
			}
			
			/* Condition to check the licked page is greater than 1 */
			
			
			/* Condition to check the clicked page is less than maxpage */
		
			if (clickedPage < maxPage) {
				nextLinkStatus = "true";
			} else {
				nextLinkStatus = "false";
			}
			

			boolean isNext = false;
			if (maxPage > 1) {
				isNext = true;
			}
			pageSummaryList = new ArrayList<CorrespAccountSummary>();
			setPageSummaryList(entityId, currentPage, maxPage, totalCount,
					pageSummaryList);
			request.setAttribute("pageSummaryList", pageSummaryList);
			request.setAttribute("currentPage", "" + currentPage);
			request.setAttribute("pageSize", "" + pageSize);
			request.setAttribute("maxPage", maxPage + "");
			request.setAttribute("prevEnabled", "false");
			request.setAttribute("nextEnabled", "" + isNext);
			request.setAttribute("totalCount", totalCount);
			request.setAttribute("prevEnabled", prevLinkStatus);
			request.setAttribute("nextEnabled", nextLinkStatus);
			request.setAttribute("selectedSort", selectedSort);
			setFilterSortInReq(request);

			// put the updated model bean back in the form bean
			setCorrespondentAcct(correspondentAcct);
		} catch (Exception e) {
			log
					.debug("Exception Catch in CorrespondentAcctMaintenanceAction.'display' method : "
							+ e.getMessage());
			e.printStackTrace();
			SwtUtil.logErrorInDatabase(SwtErrorHandler.getInstance()
					.handleException(e, "display",
							CorrespondentAcctMaintenanceAction.class));
			return ("fail");
		}
		log.debug("Exiting CorrespondentAcctMaintenanceAction.display method");
		return ("display");
	}
	

	

	/**
	 * Action method called to show the tabular list of existing records
	 * 
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return ActionForward
	 * @throws Exception
	 */
	public String displayFilteredResults() throws Exception {

		log
				.debug("Entering CorrespondentAcctMaintenanceAction.displayFilteredResults method");
		HttpServletRequest request = ServletActionContext.getRequest();

		try {
			CorrespondentAcct acct =  this.getCorrespondentAcct();

			String entityId = acct.getId().getEntityId();
			String hostId = putHostIdListInReq(request);
			String currencyCode = acct.getId().getCurrencyCode();
			String messageType = acct.getId().getMessageType();
			String otherMessageType = acct.getOtherMessageType();
			if (otherMessageType != null && !"".equals(otherMessageType.trim())) {
				messageType = otherMessageType;
			}
			// setup button access permissions
			if (SwtUtil.getMenuEntityCurrGrpAccess(request, entityId, null) == 0) {
				setButtonStatus(request, SwtConstants.STR_TRUE,
						SwtConstants.STR_FALSE, SwtConstants.STR_FALSE,
						SwtConstants.STR_FALSE, SwtConstants.STR_TRUE);

				request.setAttribute("EntityAccess",
						SwtConstants.ENTITY_FULL_ACCESS);
			} else {
				setButtonStatus(request, SwtConstants.STR_FALSE,
						SwtConstants.STR_FALSE, SwtConstants.STR_FALSE,
						SwtConstants.STR_FALSE, SwtConstants.STR_TRUE);

				request.setAttribute("EntityAccess",
						SwtConstants.ENTITY_READ_ACCESS);
			}

			request.setAttribute("listViewData",
					correspondentAcctMaintenanceManager
							.getCorrespondentAcctList(hostId, entityId,
									messageType, currencyCode));
			// This method is used to put collection of entity list into
			// request.
			putEntityListInReq(request);

			// Get the message types list to populate the message types dropdown
			Collection<String> messageTypes = correspondentAcctMaintenanceManager
					.getMessageTypesDropdownValues();
			putMessageTypesListInRequest(request, messageTypes);

			// Put the currencies list in the form
			Collection<LabelValueBean> currencies = putCurrencyListInRequest(
					request, entityId , false);
			request.setAttribute("currencyName", getLabelFromValue(currencies,
					currencyCode));
			setFilterSortInReq(request);

			// put the updated model bean back in the form bean
			setCorrespondentAcct(acct);
		} catch (Exception e) {
			log
					.debug("Exception Catch in CorrespondentAcctMaintenanceAction.'displayFilteredResults' method : "
							+ e.getMessage());
			e.printStackTrace();
			SwtUtil.logErrorInDatabase(SwtErrorHandler.getInstance()
					.handleException(e, "displayFilteredResults",
							CorrespondentAcctMaintenanceAction.class));
			return ("fail");
		}
		log
				.debug("Exiting CorrespondentAcctMaintenanceAction.displayFilteredResults method");
		return ("display");
	}

	/**
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return
	 * @throws SwtException
	 */
	public String delete()
			throws Exception {
		HttpServletRequest request = ServletActionContext.getRequest();

		try {
			log
					.debug("Entering CorrespondentAcctMaintenanceAction.delete method");
			CorrespondentAcct correspondentAcct =  this.getCorrespondentAcct();

			String entityId = (String) request.getParameter("selectedEntityId");
			String hostId = putHostIdListInReq(request);
			String currencyCode = (String) request
					.getParameter("selectedCurrencyCode");
			String messageType = (String) request
					.getParameter("selectedMessageType");

			String selectedCurrencyCode = (String) request
					.getParameter("selectedRowCurrencyCode");
			String selectedMessageType = (String) request
					.getParameter("selectedRowMessageType");
			String selectedCorresAccId = (String) request
					.getParameter("selectedCorresAccId");

			correspondentAcct.getId().setEntityId(entityId);
			correspondentAcct.getId().setHostId(hostId);
			correspondentAcct.getId().setCurrencyCode(currencyCode);
			correspondentAcct.getId().setMessageType(messageType);

			// Set the selected Correspondent Account's details
			CorrespondentAcct object = new CorrespondentAcct();
			object.getId().setHostId(hostId);
			object.getId().setEntityId(entityId);
			object.getId().setMessageType(selectedMessageType);
			object.getId().setCurrencyCode(selectedCurrencyCode);
			object.getId().setCorresAccId(selectedCorresAccId);
			// Delete the selected correspondent Account
			correspondentAcctMaintenanceManager.deleteCorrespondentAcct(object);
			setFilterSortInReq(request);

			// put the updated model bean back in the form bean
			setCorrespondentAcct(correspondentAcct);
		} catch (Exception e) {
			log
					.debug("Exception Catch in CorrespondentAcctMaintenanceAction.'delete' method : "
							+ e.getMessage());
			e.printStackTrace();
			SwtUtil.logErrorInDatabase(SwtErrorHandler.getInstance()
					.handleException(e, "delete",
							CorrespondentAcctMaintenanceAction.class));
			return ("fail");
		}
		log.debug("Exiting CorrespondentAcctMaintenanceAction.delete method");
		return display();
	}

	/**
	 * Action method called to show the new record adder form
	 * 
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return ActionForward
	 * @throws Exception
	 */
	public String add()
			throws Exception {
		HttpServletRequest request = ServletActionContext.getRequest();
		log.debug("Entering CorrespondentAcctMaintenanceAction.add method");
		try {
			CorrespondentAcct correspondentAcct = this.getCorrespondentAcct();
			String entityId = (String) request.getParameter("entityId");
			String hostId = putHostIdListInReq(request);
			putEntityListInReq(request);
			String currencyCode = ((request.getParameter("currencyCode") != null) && (request
					.getParameter("currencyCode").trim().length() > 0)) ? request
					.getParameter("currencyCode")
					: SwtConstants.ALL_VALUE;

			request.setAttribute("entityId", entityId);
			String entityName = request.getParameter("entityName");
			request.getSession().setAttribute("entityName", entityName);
			correspondentAcct.getId().setEntityId(entityId);
			correspondentAcct.getId().setHostId(hostId);
			correspondentAcct.getId().setCurrencyCode(currencyCode);

			// Get the accounts for the account id dropdown.
			Collection<LabelValueBean> accountsList = correspondentAcctMaintenanceManager
					.getAccountIdDropDown(CacheManager.getInstance()
							.getHostId(), entityId, currencyCode);
			putAccountListInRequest(request, accountsList);

			// Get the message types list to populate the message types dropdown
			Collection<String> messageTypes = correspondentAcctMaintenanceManager
					.getMessageTypesDropdownValues();
			putMessageTypesListInRequest(request, messageTypes);
			// Put the currencies list in the form
			Collection<LabelValueBean> currencies = putCurrencyListInRequest(
					request, entityId, true);
			request.setAttribute("currencyName", getLabelFromValue(currencies,
					currencyCode));

			request.setAttribute("methodName", "save");
		} catch (SwtException swtexp) {
			log
					.debug("SwtException Catch in CorrespondentAcctMaintenanceAction.'add' method : "
							+ swtexp.getMessage());
			swtexp.printStackTrace();
			SwtUtil.logErrorInDatabase(swtexp);
			return ("fail");
		} catch (Exception e) {
			log
					.debug("Exception Catch in CorrespondentAcctMaintenanceAction.'add' method : "
							+ e.getMessage());
			e.printStackTrace();
			SwtUtil.logErrorInDatabase(SwtErrorHandler.getInstance()
					.handleException(e, "add",
							CorrespondentAcctMaintenanceAction.class));
			return ("fail");
		}
		log.debug("Exiting CorrespondentAcctMaintenanceAction.add method");
		return ("add");
	}

	/**
	 * Action method called to show the new record adder form
	 * 
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return ActionForward
	 * @throws Exception
	 */
	public String refreshAccountList() throws Exception {
		log
				.debug("Entering CorrespondentAcctMaintenanceAction.refreshAccountList method");
		HttpServletRequest request = ServletActionContext.getRequest();

		try {
			CorrespondentAcct correspondentAcct = this.getCorrespondentAcct();
			String oldMethod = (String) request.getParameter("oldMethod");
			String entityId = (String) request.getParameter("selectedEntityId");
			String messageType = correspondentAcct.getId().getMessageType();
			String currencyCode = correspondentAcct.getId().getCurrencyCode();
			String corresAccId = correspondentAcct.getId().getCorresAccId();

			String actualCurrencyCode = (String) request
					.getParameter("actualCurrencyCode");
			String actualCorresAccId = (String) request
					.getParameter("actualCorresAccId");
			String actualMessageType = (String) request
					.getParameter("actualMessageType");
			String actualAccountId = (String) request
					.getParameter("actualAccountId");
			request.setAttribute("actualCurrencyCode", actualCurrencyCode);
			request.setAttribute("actualCorresAccId", actualCorresAccId);
			request.setAttribute("actualMessageType", actualMessageType);
			request.setAttribute("actualAccountId", actualAccountId);
			String hostId = putHostIdListInReq(request);
			putEntityListInReq(request);

			request.setAttribute("entityId", entityId);
			correspondentAcct.getId().setEntityId(entityId);
			correspondentAcct.getId().setHostId(hostId);
			correspondentAcct.getId().setCurrencyCode(currencyCode);
			correspondentAcct.getId().setMessageType(messageType);
			correspondentAcct.getId().setCorresAccId(corresAccId);

			// Get the accounts for the account id dropdown.
			Collection<LabelValueBean> accountsList = correspondentAcctMaintenanceManager
					.getAccountIdDropDown(CacheManager.getInstance()
							.getHostId(), entityId, currencyCode);
			putAccountListInRequest(request, accountsList);
			// Put the currencies list in the form
			Collection<LabelValueBean> currencies = putCurrencyListInRequest(
					request, entityId, true);
			request.setAttribute("currencyName", getLabelFromValue(currencies,
					currencyCode));

			// Get the message types list to populate the message types dropdown
			Collection<String> messageTypes = correspondentAcctMaintenanceManager
					.getMessageTypesDropdownValues();
			putMessageTypesListInRequest(request, messageTypes);

			request.setAttribute("copyActualValues", "N");
			request.setAttribute("methodName", oldMethod);
		} catch (SwtException swtexp) {
			log
					.debug("SwtException Catch in CorrespondentAcctMaintenanceAction.'add' method : "
							+ swtexp.getMessage());
			swtexp.printStackTrace();
			SwtUtil.logErrorInDatabase(swtexp);
			return ("fail");
		} catch (Exception e) {
			log
					.debug("Exception Catch in CorrespondentAcctMaintenanceAction.'add' method : "
							+ e.getMessage());
			e.printStackTrace();
			SwtUtil.logErrorInDatabase(SwtErrorHandler.getInstance()
					.handleException(e, "add",
							CorrespondentAcctMaintenanceAction.class));
			return ("fail");
		}
		log
				.debug("Exiting CorrespondentAcctMaintenanceAction.refreshAccountList method");
		return ("add");
	}

	/**
	 * Action method called to save a new record
	 * 
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return ActionForward
	 * @throws Exception
	 */
	public String save()
			throws Exception {
		// DynaValidatorForm dyForm = null;
		CorrespondentAcct correspondentAcct = null;
		ActionMessages errors = null;
		HttpServletRequest request = ServletActionContext.getRequest();

		try {
			log.debug(this.getClass().getName()
					+ "- [saveAliasRecord] - Entering ");
			errors = new ActionMessages();
			correspondentAcct = this.getCorrespondentAcct();
			String entityId = (String) request.getParameter("selectedEntityId");
			String currencyCode = correspondentAcct.getId().getCurrencyCode();
			// Start Code modified by Chidambaranathan for issue found on
			// 20-01-2011 - V1051 beta testing-truncate extra space//
			String messageType = correspondentAcct.getId().getMessageType()
					.trim();
			String otherMessageType = correspondentAcct.getOtherMessageType()
					.trim();
			// End Code modified by Chidambaranathan for issue found on
			// 20-01-2011 - V1051 beta testing-truncate extra space//
			// String method = (String) request.getParameter("method");
			// if (messageType.equalsIgnoreCase("OTHER")) {
			// messageType = correspondentAcct.getOtherMessageType();
			// }
			if (otherMessageType != null && !"".equals(otherMessageType.trim())) {
				messageType = otherMessageType;
			}
			String hostId = putHostIdListInReq(request);
			CorrespondentAcct account = new CorrespondentAcct();
			account.getId().setHostId(hostId);
			account.getId().setEntityId(entityId);
			account.getId().setMessageType(messageType);
			account.getId().setCurrencyCode(currencyCode);
			// Start Code modified by Chidambaranathan for issue found on
			// 20-01-2011 - V1051 beta testing-truncate extra space//
			account.getId().setCorresAccId(
					correspondentAcct.getId().getCorresAccId().trim());
			// End Code modified by Chidambaranathan for issue found on
			// 20-01-2011 - V1051 beta testing-truncate extra space//
			account.setAccountId(correspondentAcct.getAccountId());
			correspondentAcctMaintenanceManager.saveCorrespondentAcct(account);
			putEntityListInReq(request);
			// Put the currencies list in the form
			Collection<LabelValueBean> currencies = putCurrencyListInRequest(
					request, entityId, true);
			request.setAttribute("currencyName", getLabelFromValue(currencies,
					currencyCode));

			request.setAttribute("parentFormRefresh", "yes");
			// Get the accounts for the account id dropdown.
			Collection<LabelValueBean> accountsList = correspondentAcctMaintenanceManager
					.getAccountIdDropDown(CacheManager.getInstance()
							.getHostId(), entityId, currencyCode);
			putAccountListInRequest(request, accountsList);

			// Get the message types list to populate the message types dropdown
			Collection<String> messageTypes = correspondentAcctMaintenanceManager
					.getMessageTypesDropdownValues();
			putMessageTypesListInRequest(request, messageTypes);
		}
		/*
		 * Start Code added by Chidambaranathan for issues found on V1051 beta
		 * testing-for same data exists on 10-1-2011
		 */
		catch (SwtException swtexp) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [save] method : - "
					+ swtexp.getMessage());
			putEntityListInReq(request);
			request.setAttribute("methodName", "save");
			String entityId = (String) request.getParameter("selectedEntityId");
			request.setAttribute("entityId", entityId);
			String currencyCode = correspondentAcct.getId().getCurrencyCode();

			// Put the currencies list in the form
			Collection<LabelValueBean> currencies = putCurrencyListInRequest(
					request, entityId, true);
			request.setAttribute("currencyName", getLabelFromValue(currencies,
					currencyCode));
			// Get the accounts for the account id dropdown.
			Collection<LabelValueBean> accountsList = correspondentAcctMaintenanceManager
					.getAccountIdDropDown(CacheManager.getInstance()
							.getHostId(), entityId, currencyCode);
			putAccountListInRequest(request, accountsList);

			// Get the message types list to populate the message types dropdown
			Collection<String> messageTypes = correspondentAcctMaintenanceManager
					.getMessageTypesDropdownValues();
			putMessageTypesListInRequest(request, messageTypes);

			/*
			 * Start : Modified for Mantis 1366-Remove entry to log if duplicate
			 * records added by betcy on 18-02-2011
			 */
			if (swtexp.getErrorCode().equals(
					"errors.DataIntegrityViolationExceptioninAdd")) {
				if (errors != null) {
					errors.add("", new ActionMessage(swtexp.getErrorCode()));
				}
				saveErrors(request, errors);

			} else {
				saveErrors(request, SwtUtil.logException(swtexp, request, ""));
			}
			/*
			 * End : Modified for Mantis 1366-Remove entry to log if duplicate
			 * records added by betcy on 18-02-2011
			 */
			return ("add");
		} catch (Exception e) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [save] method : - "
					+ e.getMessage());
			SwtUtil.logErrorInDatabase(SwtErrorHandler.getInstance()
					.handleException(e, "add",
							CorrespondentAcctMaintenanceAction.class));
			return ("fail");
		}
		log.debug("Exiting CorrespondentAcctMaintenanceAction.save method");
		return ("add");
	}

	/*
	 * End Code added by Chidambaranathan for issues found on V1051 beta
	 * testing-for same data exists on 10-1-2011
	 */

	/**
	 * Action method called to save a new record
	 * 
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return ActionForward
	 * @throws Exception
	 */
	public String modify()
			throws Exception {
		log.debug("Entering CorrespondentAcctMaintenanceAction.modify method");
		HttpServletRequest request = ServletActionContext.getRequest();

		CorrespondentAcct correspondentAcct = null;
		try {
			correspondentAcct = this.getCorrespondentAcct();
			String entityId = (String) request.getParameter("selectedEntityId");
			String hostId = putHostIdListInReq(request);
			// String currencyCode =
			// correspondentAcct.getId().getCurrencyCode();
			/*
			 * String messageType = correspondentAcct.getId().getMessageType();
			 * String otherMessageType =
			 * correspondentAcct.getOtherMessageType();
			 * 
			 * if (otherMessageType != null && !"".equals(otherMessageType.trim())) {
			 * messageType = otherMessageType; }
			 */

			// Get the old Correspondent Account record with Old values
			String oldMessageType = (String) request
					.getParameter("actualMessageType");
			String oldCurrencyCode = (String) request
					.getParameter("actualCurrencyCode");
			String oldCorresAccId = (String) request
					.getParameter("actualCorresAccId");
			CorrespondentAcct oldObject = new CorrespondentAcct();
			oldObject.getId().setEntityId(entityId);
			oldObject.getId().setHostId(hostId);
			oldObject.getId().setCurrencyCode(oldCurrencyCode);
			oldObject.getId().setMessageType(oldMessageType);
			oldObject.getId().setCorresAccId(oldCorresAccId);

			CorrespondentAcct dbAcct = correspondentAcctMaintenanceManager
					.getCorrespondentAcct(oldObject);
			// If object exists in database, update the record.
			if (dbAcct != null) {
				CorrespondentAcct newAccount = new CorrespondentAcct();
				newAccount.getId().setHostId(hostId);
				newAccount.getId().setEntityId(entityId);
				newAccount.getId().setMessageType(oldMessageType);
				newAccount.getId().setCurrencyCode(oldCurrencyCode);
				newAccount.getId().setCorresAccId(oldCorresAccId);
				newAccount.setAccountId(correspondentAcct.getAccountId());
				// Update the selected correspondent Account
				correspondentAcctMaintenanceManager.updateCorrespondentAcct(newAccount);
			}
			putEntityListInReq(request);
			// Put the currencies list in the form
			Collection<LabelValueBean> currencies = putCurrencyListInRequest(
					request, entityId, true);
			request.setAttribute("currencyName", getLabelFromValue(currencies,
					oldCurrencyCode));

			request.setAttribute("copyActualValues", "N");
			request.setAttribute("parentFormRefresh", "yes");

			// Get the accounts for the account id dropdown.
			Collection<LabelValueBean> accountsList = correspondentAcctMaintenanceManager
					.getAccountIdDropDown(CacheManager.getInstance()
							.getHostId(), entityId, oldCurrencyCode);
			putAccountListInRequest(request, accountsList);

			// Get the message types list to populate the message types dropdown
			Collection<String> messageTypes = correspondentAcctMaintenanceManager
					.getMessageTypesDropdownValues();
			putMessageTypesListInRequest(request, messageTypes);
		} catch (Exception e) {
			log
					.debug("Exception Catch in CorrespondentAcctMaintenanceAction.'add' method : "
							+ e.getMessage());
			e.printStackTrace();
			SwtUtil.logErrorInDatabase(SwtErrorHandler.getInstance()
					.handleException(e, "add",
							CorrespondentAcctMaintenanceAction.class));
			return ("fail");
		}
		log.debug("Exiting CorrespondentAcctMaintenanceAction.modify method");
		return ("add");
	}

	/**
	 * Action method called to show the new record adder form
	 * 
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return ActionForward
	 * @throws Exception
	 */
	public String change()
			throws Exception {
		log.debug("Entering CorrespondentAcctMaintenanceAction.change method");
		HttpServletRequest request = ServletActionContext.getRequest();

		try {
			CorrespondentAcct correspondentAcct = this.getCorrespondentAcct();

			String entityId = (String) request.getParameter("entityId");
			String entityName = request.getParameter("entityName");
			request.getSession().setAttribute("entityName", entityName);
			String currencyCode = (String) request.getParameter("currencyCode");
			String messageType = (String) request.getParameter("messageType");
			String corresAccId = (String) request.getParameter("corresAccId");
			String hostId = putHostIdListInReq(request);
			putEntityListInReq(request);
			CorrespondentAcct account = new CorrespondentAcct();
			request.setAttribute("entityId", entityId);
			account.getId().setEntityId(entityId);
			account.getId().setHostId(hostId);
			account.getId().setCurrencyCode(currencyCode);
			account.getId().setMessageType(messageType);
			account.getId().setCorresAccId(corresAccId);

			correspondentAcct = correspondentAcctMaintenanceManager
					.getCorrespondentAcct(account);

			// Get the message types list to populate the message types dropdown
			Collection<String> messageTypes = correspondentAcctMaintenanceManager
					.getMessageTypesDropdownValues();
			putMessageTypesListInRequest(request, messageTypes);
			boolean label = getMessageTypeValue(messageTypes, correspondentAcct
					.getId().getMessageType());
			if (!label) {
				correspondentAcct.setOtherMessageType(correspondentAcct.getId()
						.getMessageType());
				correspondentAcct.setSearchMessageType("M");
				correspondentAcct.getId().setMessageType(
						SwtConstants.EMPTY_STRING);
			}

			// Get the accounts for the account id dropdown.
			Collection<LabelValueBean> accountsList = correspondentAcctMaintenanceManager
					.getAccountIdDropDown(CacheManager.getInstance()
							.getHostId(), entityId, currencyCode);
			putAccountListInRequest(request, accountsList);
			// Put the currencies list in the form
			Collection<LabelValueBean> currencies = putCurrencyListInRequest(
					request, entityId, true);
			request.setAttribute("currencyName", getLabelFromValue(currencies,
					currencyCode));

			// This method is used to put collection of entity list into
			// request.
			putEntityListInReq(request);

			request.setAttribute("copyActualValues", "Y");
			request.setAttribute("methodName", "modify");
			// put the updated model bean back in the form bean
			setCorrespondentAcct(correspondentAcct);
		} catch (SwtException swtexp) {
			log
					.debug("SwtException Catch in CorrespondentAcctMaintenanceAction.'add' method : "
							+ swtexp.getMessage());
			swtexp.printStackTrace();
			SwtUtil.logErrorInDatabase(swtexp);
			return ("fail");
		} catch (Exception e) {
			log
					.debug("Exception Catch in CorrespondentAcctMaintenanceAction.'add' method : "
							+ e.getMessage());
			e.printStackTrace();
			SwtUtil.logErrorInDatabase(SwtErrorHandler.getInstance()
					.handleException(e, "add",
							CorrespondentAcctMaintenanceAction.class));
			return ("fail");
		}
		log.debug("Exiting CorrespondentAcctMaintenanceAction.change method");
		return ("add");
	}

	/**
	 * This method is used to put collection of entity list into request.
	 * 
	 * @param request
	 * @throws SwtException
	 */
	private void putEntityListInReq(HttpServletRequest request)
			throws SwtException {
		log.debug(this.getClass().getName()
				+ "- [putEntityListInReq] - Entering ");
		HttpSession session = null;
		Collection entities = new ArrayList();
		session = request.getSession();
		entities = SwtUtil.getUserEntityAccessList(session);
		// get the EntityName and EntityId from Collection Object and put it
		// into labelValueBean
		entities = SwtUtil.convertEntityAcessCollectionLVL(entities, session);
		// Set the Entity Id List in request Object
		request.setAttribute("entities", entities);
		log.debug(this.getClass().getName()
				+ "- [putEntityListInReq] - Exiting ");
	}

	/*
	 * This method will get the currency list from the database
	 * 
	 * @param request the request parameter
	 * 
	 * @param roleId the role ID
	 * 
	 * @param entityId the entity ID
	 * 
	 * @throws SwtException throw exception if any
	 * 
	 * @return Collection collection of currencies
	 */
	private Collection<LabelValueBean> putCurrencyListInRequest(
			HttpServletRequest request, String entityId, boolean fullAccess) throws SwtException {
		log.debug("entering 'putCurrencyListInRequest' method");
		String roleId = ((CommonDataManager) request.getSession().getAttribute(
				SwtConstants.CDM_BEAN)).getUser().getRoleId();
		Collection<LabelValueBean> currencyList = null;
		
		if(fullAccess) {
			// Map for currency id , currency name
			Map<String, String> currencyMap = null;
			// To iterate currency key
			Iterator<String> itrCurrencyKey = null;
			// Instantiate the currency drop down
			currencyList = new ArrayList<LabelValueBean>();
			// get the role id for current user
			roleId = ((CommonDataManager) request.getSession().getAttribute(
					SwtConstants.CDM_BEAN)).getUser().getRoleId();
			// get the map for currency with full access
			currencyMap = SwtUtil.getCurrencyFullAccessMap(roleId, SwtUtil.getCurrentHostId(),
					entityId);
			if (currencyMap != null && currencyMap.size() > 0) {
				// iterate the currency map key values
				itrCurrencyKey = currencyMap.keySet().iterator();
				while (itrCurrencyKey.hasNext()) {
					// get the currency id from map
					String currencyId = itrCurrencyKey.next();
					// add labelvaluebean for currency id
					currencyList.add(new LabelValueBean(currencyId,
							currencyId));
				}
			}
		}else {
			currencyList = SwtUtil
					.getSwtMaintenanceCache().getCurrencyViewORFullAcessLVL(roleId,
							entityId);
	
			if (currencyList != null) {
				currencyList.remove(new LabelValueBean("Default", "*"));
			}
		}
		Collection<LabelValueBean> currencyListWithAll = new ArrayList<LabelValueBean>();
		// Showing 'All' in Currency drop down
		currencyListWithAll.add(new LabelValueBean(SwtConstants.ALL_LABEL,
				SwtConstants.ALL_VALUE));

		// If the currencies in the dropdown list is not equal to null, it will
		// add all currencies with ALL .
		if (currencyList != null) {
			currencyListWithAll.addAll(currencyList);
		}
		request.setAttribute("currencies", currencyListWithAll);
		log.debug("exiting 'putCurrencyListInRequest' method");
		return currencyListWithAll;
	}

	
		
	/**
	 * Given a collection of label-value beans, returns the 'label' from the
	 * first occurrence with a 'value' matching the given 'value' parameter
	 * 
	 * @param coll
	 * @param value
	 * @return String
	 */
	private String getLabelFromValue(Collection<LabelValueBean> coll,
			String value) {
		log.debug("entering 'getLabelFromValue' method");
		if (coll != null) {
			Iterator<LabelValueBean> itr = coll.iterator();

			while (itr.hasNext()) {
				LabelValueBean lvb = (LabelValueBean) (itr.next());
				if (lvb.getValue().equals(value))
					return lvb.getLabel();
			}
		}
		log.debug("exiting 'getLabelFromValue' method");
		return null;
	}

	/**
	 * Given a collection of label-value beans, returns the 'label' from the
	 * first occurrence with a 'value' matching the given 'value' parameter
	 * 
	 * @param coll
	 * @param value
	 * @return String
	 */
	private boolean getMessageTypeValue(Collection<String> messages,
			String value) {
		log.debug("entering 'getMessageTypeValue' method");
		Iterator<String> iterator = messages.iterator();
		while (iterator.hasNext()) {
			String message = (String) iterator.next();
			if (message.equalsIgnoreCase(value)) {
				return true;
			}
		}
		log.debug("exiting 'getMessageTypeValue' method");
		return false;
	}

	/**
	 * Sets flags in request scope for the buttons that this user may access
	 * 
	 * @param req
	 * @param addStatus
	 * @param changeStatus
	 * @param deleteStatus
	 * @param saveStatus
	 * @param cancelStatus
	 */
	private void setButtonStatus(HttpServletRequest req, String addStatus,
			String changeStatus, String deleteStatus, String saveStatus,
			String cancelStatus) {
		log.debug("entering 'setButtonStatus' method");
		req.setAttribute(SwtConstants.ADD_BUT_STS, addStatus);
		req.setAttribute(SwtConstants.CHG_BUT_STS, changeStatus);
		req.setAttribute(SwtConstants.DEL_BUT_STS, deleteStatus);
		req.setAttribute(SwtConstants.SAV_BUT_STS, saveStatus);
		req.setAttribute(SwtConstants.CAN_BUT_STS, cancelStatus);
		log.debug("exiting 'setButtonStatus' method");
	}

	/**
	 * Utility method to put table filter state in request
	 * 
	 * @param request
	 */
	private void setFilterSortInReq(HttpServletRequest request) {
		log.debug("entering 'setFilterSortInReq' method");
		String selectedFilterStatus = request
				.getParameter("selectedFilterStatus");

		if (selectedFilterStatus == null) {
			selectedFilterStatus = SwtConstants.EMPTY_STRING;
		}

		String selectedSortStatus = request.getParameter("selectedSortStatus");

		if (selectedSortStatus == null) {
			selectedSortStatus = SwtConstants.EMPTY_STRING;
		}

		String selectedSortDescending = request
				.getParameter("selectedSortDescending");

		if (selectedSortDescending == null) {
			selectedSortDescending = SwtConstants.EMPTY_STRING;
		}

		StringTokenizer strtokens = new StringTokenizer(selectedFilterStatus,
				",");

		boolean filterStatusFlag = false;

		while (strtokens.hasMoreTokens()) {
			String nextToken = strtokens.nextToken();

			if (!nextToken.equals("special_all")
					&& !nextToken.equals("undefined")) {
				filterStatusFlag = true;
				break;
			}
		}

		if (!filterStatusFlag) {
			selectedFilterStatus = SwtConstants.EMPTY_STRING;
		}

		request.setAttribute("filterStatus", selectedFilterStatus);
		request.setAttribute("sortStatus", selectedSortStatus);
		request.setAttribute("sortDescending", selectedSortDescending);
		log.debug("exiting 'setFilterSortInReq' method");
	} // End of setFilterSortInReq

	/**
	 * @param request
	 * @return hostId
	 * @throws SwtException
	 */
	private String putHostIdListInReq(HttpServletRequest request)
			throws SwtException {
		CacheManager cacheManagerInst = CacheManager.getInstance();
		String hostId = cacheManagerInst.getHostId();
		return hostId;
	}

	/**
	 * Adds an empty label-value bean to the given collection and then puts it
	 * in request scope
	 * 
	 * @param request
	 * @param accounts
	 */
	private void putAccountListInRequest(HttpServletRequest request,
			Collection<LabelValueBean> accounts) {
		log
				.debug("Entering CorrespondentAcctMaintenanceAction.putAccountListInRequest method");
		Collection<LabelValueBean> accountDropDown = new ArrayList<LabelValueBean>();
		accountDropDown.add(new LabelValueBean("", ""));
		Iterator<LabelValueBean> itr = accounts.iterator();
		while (itr.hasNext()) {
			LabelValueBean lvb = (LabelValueBean) (itr.next());
			accountDropDown.add(lvb);
		}
		request.setAttribute("accounts", accountDropDown);
		log
				.debug("Exit CorrespondentAcctMaintenanceAction.putAccountListInRequest method");
	}

	/**
	 * Adds an empty label-value bean to the given collection and then puts it
	 * in request scope
	 * 
	 * @param request
	 * @param accounts
	 */
	private void putMessageTypesListInRequest(HttpServletRequest request,
			Collection<String> messages) {
		log
				.debug("Entering CorrespondentAcctMaintenanceAction.putMessageTypesListInRequest method");
		Collection<LabelValueBean> messageTypesDropDown = new ArrayList<LabelValueBean>();
		LabelValueBean lvb = null;
		String message = SwtConstants.EMPTY_STRING;
		messageTypesDropDown.add(new LabelValueBean(SwtConstants.EMPTY_STRING,
				SwtConstants.EMPTY_STRING));
		Iterator<String> itr = messages.iterator();
		while (itr.hasNext()) {
			message = (String) (itr.next());
			lvb = new LabelValueBean(SwtConstants.EMPTY_STRING, message);
			messageTypesDropDown.add(lvb);
		}
		// messageTypesDropDown.add(new
		// LabelValueBean(SwtConstants.EMPTY_STRING,
		// "OTHER"));
		request.setAttribute("messagetypes", messageTypesDropDown);
		log
				.debug("Exit CorrespondentAcctMaintenanceAction.putMessageTypesListInRequest method");
	}
	/**
	 * To set the maximum number of pages for the screen according to the
	 * records.
	 * 
	 * @param totalCount
	 * @param pageSize
	 * @return int
	 */
	private int setMaxPageAttribute(int totalCount, int pageSize) {
		log.debug(this.getClass().getName() + " - [setMaxPageAttribute] - "
				+ " Entry");

		/* Method's local variable declaration */
		int maxPage;
		int remainder;
		/*
		 * The max page is calculate by the total number of records divided by
		 * the number of records per page
		 */
		maxPage = (totalCount) / (pageSize);
		/*
		 * Remainder is the modulo of the total number of records and the number
		 * of records per page
		 */
		remainder = totalCount % pageSize;

		if (remainder > 0) {
			maxPage++;
		}
		log.debug(this.getClass().getName() + " - [setMaxPageAttribute] - "
				+ "Exit");
		return maxPage;
	}
	
	/**
	 * Method added for setting the page numbers.
	 * 
	 * @param EntityId
	 * @param CurrentPage
	 * @param MaxPage
	 * @param MovTotalCount
	 * @param PageSummaryList
	 * 
	 */
	private void setPageSummaryList(String entityId, int currentPage,
			int maxPage, int movTotalCount,
			ArrayList<CorrespAccountSummary> pageSummaryList) throws SwtException {
		try {
			log.debug(this.getClass().getName()
					+ "- [setPageSummaryList] - Entering ");
			/* Class Instance declaration */
			CorrespAccountSummary corrAcct = new CorrespAccountSummary();
			corrAcct.setEntityId(entityId);
			corrAcct.setCurrentPageNo(currentPage);
			corrAcct.setMaxPages(maxPage);
			corrAcct.setTotalCount(movTotalCount);
			pageSummaryList.add(corrAcct);
			log.debug(this.getClass().getName()
					+ "- [setPageSummaryList] - Exiting ");
		} catch (Exception exp) {

			log
					.error(this.getClass().getName()
							+ " - Exception Catched in [setPageSummaryList] method : - "
							+ exp.getMessage());

		}
	}

}
