/*
 * @(#)CountryOverrideManager.java 1.0 28/02/2010
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.maintenance.web;

import java.util.Collection;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.maintenance.model.CountryOverride;
import org.swallow.maintenance.model.EntityUserAccess;
import org.swallow.maintenance.service.CountryOverrideManager;
import org.swallow.util.LabelValueBean;
import org.swallow.util.SwtUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.apache.struts2.convention.annotation.Result;
import org.apache.struts2.convention.annotation.Action;
import org.apache.struts2.convention.annotation.AllowedMethods;
import org.apache.struts2.ServletActionContext;
import org.swallow.util.struts.CustomActionSupport;


@Action(value = "/countryoverride", results = {
	@Result(name = "success", location = "/jsp/maintenance/countryoverride.jsp"),
	@Result(name = "change", location = "/jsp/maintenance/countryoverridechange.jsp"),
	@Result(name = "fail", location = "/error.jsp"),
})
@AllowedMethods ({"showDetails" ,"change" ,"save" })

/**
 * <AUTHOR> A
 * 
 * Action Layer for handle COUNTRY Maintenance screen, Methods to display, save
 * delete and update weekend settings for all countries
 */
public class CountryOverrideAction extends CustomActionSupport {
	
	
public String execute() throws Exception {
    HttpServletRequest request = ServletActionContext.getRequest();

    // List of methods 
    String method = String.valueOf(request.getParameter("method"));

    switch (method) {
        case "showDetails":
            return showDetails();
        case "change":
            return change();
        case "save":
            return save();
        default:
            break;
    }

    return showDetails();
}


	/**
	 * Initializing logger object for this class
	 */
	private final Log log = LogFactory.getLog(CountryOverrideAction.class);
	/**
	 * Initializing countryOverrideManager object for this class
	 */
	@Autowired
private CountryOverrideManager countryOverrideManager = null;

	/**
	 * This is used to set the countryOverrideManager
	 * 
	 * @param countryOverrideManager
	 * @return
	 */
	public void setCountryOverrideManager(
			CountryOverrideManager countryOverrideManager) {
		this.countryOverrideManager = countryOverrideManager;
	}
	
	CountryOverride countryOverride;
	public CountryOverride getCountryOverride() {
		return countryOverride;
	}
	public void setCountryOverride(CountryOverride countryOverride) {
		this.countryOverride = countryOverride;
	}
	

	String menuAccessId;
	public String getMenuAccessId() {
		return menuAccessId;
	}
	public void setMenuAccessId(String menuAccessId) {
		this.menuAccessId = menuAccessId;
	}
	
	String ismenuItem;
	public String getIsmenuItem() {
		return ismenuItem;
	}
	public void setIsmenuItem(String ismenuItem) {
		this.ismenuItem = ismenuItem;
	}
	
	String menuItemId;
	public String getMenuItemId() {
		return menuItemId;
	}
	public void setMenuItemId(String menuItemId) {
		this.menuItemId= menuItemId;
	}

	/**
	 * This method is default method in struts.This method is called only once
	 * at the time of loading.
	 * 
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return ActionForward
	 * @throws SwtException
	 */
	public String unspecified()
			throws SwtException {
		log.debug(this.getClass().getName()
				+ "- [unspecified] - return showDetails method ");
		return showDetails();
	}

	/**
	 * This method loaded the country override details in a screen based on a
	 * default entity or user selected entity.
	 * 
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return ActionForward
	 * @throws SwtException
	 */
	public String showDetails()
			throws SwtException {

		/* Method's local variable declaration */
		// Variable to hold hostId
		String hostId = null;
		// Variable to hold entityId
		String entityId = null;
		// Collection instance to hold country list
		Collection<CountryOverride> countryColl = null;
		// Country Override instance
		CountryOverride countryOverride = null;
		HttpServletRequest request = ServletActionContext.getRequest();

		try {
			log.debug(this.getClass().getName() + " - [showDetails] - Entry");
			
			if(this.countryOverride != null)
				countryOverride = this.countryOverride;
			else
				countryOverride = new CountryOverride();

			/* Retrieve and store hostId from SwtUtil */
			hostId = SwtUtil.getCurrentHostId();
			/* Read the entity id from bean class */
			entityId = countryOverride.getId().getEntityId();
			/* Condition to check entity is null or empty */
			if ((entityId == null) || (entityId.trim().length() <= 0)) {
				/* Retrieve the user's selected entity from session */
				entityId = SwtUtil.getUserCurrentEntity(request.getSession());
				/* Set the entity id using bean class */
				countryOverride.getId().setEntityId(entityId);
			}
			/*
			 * Retrieve the country list from database based on the parameters
			 * passed
			 */
			countryColl = countryOverrideManager.getCountryList(entityId,
					hostId);

			request.setAttribute("countryList", countryColl);

			
			/* put the entity list in request */
			putEntityListInReq(request);
			/* Retrieve User's Menu,Entity and Currency Group */
			SwtUtil.getMenuEntityCurrGrpAccess(request, entityId, null);

			request.setAttribute("methodName", "showDetails");
            this.countryOverride= countryOverride;
			return ("success");
		} catch (SwtException swtexp) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [showDetails] method : - "
					+ swtexp.getMessage());
			SwtUtil.logException(swtexp, request, "");
			return ("fail");
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [showDetails] method : - "
					+ exp.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "showDetails", CountryOverrideAction.class), request,
					"");
			return ("fail");
		} finally {
			log.debug(this.getClass().getName() + " - [showDetails] - Exit");
			hostId = null;
			entityId = null;
		}
	}

	/**
	 * This method loaded the holiday's details in a screen based on a default
	 * entity or user selected entity.
	 * 
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return ActionForward
	 * @throws SwtException
	 */
	public String change()
			throws SwtException {
	HttpServletRequest request = ServletActionContext.getRequest();
	log.debug(this.getClass().getName() + " - [change] - Entry");
		/* Method's local variable declaration */
		// Variable to hold entityName
		String entityName = "";

		// Instance for country override
		CountryOverride countryOverride = null;
		// Collection to hold non working day list
		Collection<LabelValueBean> dayListColl = null;
		try {
			
			if(this.countryOverride != null)
				countryOverride = this.countryOverride;
			else
				countryOverride = new CountryOverride();
			
			// Obtain entity Name from request
			entityName = request.getParameter("EntityName");
			// Set country Name is bean
			countryOverride.setCountryName(request.getParameter("countryName")
					.trim());
			// Set Country Code in bean
			countryOverride.getId().setCountryCode(
					request.getParameter("countryCode").trim());
			// Set entityID in bean
			countryOverride.getId().setEntityId(
					request.getParameter("entityId").trim());
			// Set weekend1
			countryOverride
					.setWeekend1(request.getParameter("weekend1").trim());
			// Set weekend 2
			countryOverride
					.setWeekend2(request.getParameter("weekend2").trim());
			// Get non working day list
			dayListColl = countryOverrideManager.getDayList();

			/* set non working day list in request */
			request.setAttribute("dayList", dayListColl);
			/* set method name in request */
			request.setAttribute("methodName", "change");
			/* set entity name in request */
			request.setAttribute("entityName", entityName);
            this.countryOverride= countryOverride;
			return ("change");
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [change] method : - "
					+ exp.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "change", CountryOverrideAction.class), request, "");
			return ("fail");
		} finally {
			log.debug(this.getClass().getName() + " - [change] - Exit");
		}
	}

	/**
	 * Method to save/delete/update country override
	 * 
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return ActionForward
	 * @throws SwtException
	 */
	public String save()
			throws SwtException {
	HttpServletRequest request = ServletActionContext.getRequest();
HttpServletResponse response = ServletActionContext.getResponse();
	/* Method's local variable declaration */
		// Variable to hold save Status
		String saveStatus = "";

		// Instance for country Override
		CountryOverride countryOverride = null;
		// Collection Instance for dayList collection
		Collection<LabelValueBean> dayListColl = null;
		// Variable to hold hostId
		String hostId = null;
		try {
			log.debug(this.getClass().getName() + "-[save]-Entry");
			// read hostId from properties
			hostId = SwtUtil.getCurrentHostId();
			
			if(this.countryOverride != null)
				countryOverride = this.countryOverride;
			else
				countryOverride = new CountryOverride();
			
			// Obtain save status from request
			saveStatus = request.getParameter("saveStatus");
			// set hostId in bean
			countryOverride.getId().setHostId(hostId);
			/* Condition to check savestatus is not empty */
			if (!saveStatus.trim().equals("")) {
				// save country overide values
				countryOverrideManager.save(countryOverride, saveStatus);
			}

			// Get non working days list
			dayListColl = countryOverrideManager.getDayList();
			// set non working days list in request
			request.setAttribute("dayList", dayListColl);
			// Refresh parent screen after saving the value
			request.setAttribute("parentFormRefresh", "yes");

			return ("change");
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [save] method : - "
					+ exp.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "save", CountryOverrideAction.class), request, "");
			return ("fail");
		} finally {
			log.debug(this.getClass().getName() + " - [save] - Exit");
		}
	}

	/**
	 * Sub method to put entity details in request based on user's access
	 * rights.
	 * 
	 * @param request
	 * @return
	 * @throws SwtException
	 */
	private void putEntityListInReq(HttpServletRequest request)
			throws SwtException {
	log
				.debug(this.getClass().getName()
						+ " - [putEntityListInReq] - Entry");
		/* Method's local variable declaration */
		HttpSession session = null;
		Collection<EntityUserAccess> coll = null;
		Collection<LabelValueBean> lvlColl = null;
		try {
			session = request.getSession();
			/* Retrieve the user's entity Access List from session */
			coll = SwtUtil.getUserEntityAccessList(session);
			lvlColl = SwtUtil.convertEntityAcessCollectionLVL(coll, session);
			request.setAttribute("entities", lvlColl);
		} catch (Exception exp) {
			log
					.error(this.getClass().getName()
							+ " - Exception Catched in [putEntityListInReq] method : - "
							+ exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp,
					"putEntityListInReq", this.getClass());
		} finally {
			log.debug(this.getClass().getName()
					+ " - [putEntityListInReq] - Exit");
		}

	}

}
