/*
 * @(#)ReasonMaintenanceAction.java 1.0 25/08/08
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.maintenance.web;

import java.net.URLDecoder;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.maintenance.model.EntityUserAccess;
import org.swallow.maintenance.model.ReasonMaintenance;
import org.swallow.maintenance.service.ReasonMaintenanceManager;
import org.swallow.util.CacheManager;
import org.swallow.util.SwtConstants;
import org.swallow.util.SwtUtil;

import org.apache.struts2.ServletActionContext;
import org.apache.struts2.convention.annotation.Action;
import org.apache.struts2.convention.annotation.AllowedMethods;
import org.apache.struts2.convention.annotation.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.swallow.util.struts.ActionMessage;
import org.swallow.util.struts.ActionMessages;
import org.swallow.util.struts.CustomActionSupport;

/*
 * This is action class for Reason screen
 * 
 */

@Action(value = "/reasonMaintenance", results = {
	    @Result(name = "fail", location = "/error.jsp"),
	    @Result(name = "success", location = "/jsp/maintenance/reasonmaintenance.jsp"),
	    @Result(name = "add", location = "/jsp/maintenance/reasonmaintenancechild.jsp"),
	    @Result(name = "change", location = "/jsp/maintenance/reasonmaintenancechild.jsp"),
	})


@AllowedMethods ({"displayList", "add", "save", "change", "update","delete","displayListByEntity"}) 



	
public class ReasonMaintenanceAction extends CustomActionSupport {
	/*
	 * Log instance
	 */
	private final Log log = LogFactory.getLog(ReasonMaintenanceAction.class);
	/**
	 * Manager class instance
	 */
	@Autowired
	private ReasonMaintenanceManager reasonMaintenanceManager = null;

	/**
	 * @param reasonMaintenanceManager
	 *            manager class instance to set
	 */
	public void setReasonMaintenanceManager(
			ReasonMaintenanceManager reasonMaintenanceManager) {
		this.reasonMaintenanceManager = reasonMaintenanceManager;
	}
	
	ReasonMaintenance reasonMaintenance;

	public ReasonMaintenance getReasonMaintenance() {
		return reasonMaintenance;
	}

	public void setReasonMaintenance(ReasonMaintenance reasonMaintenance) {
		this.reasonMaintenance = reasonMaintenance;
	}

	/**
	 * Default method which will be called
	 * 
	 * @param mapping
	 * 
	 * @param form
	 * 
	 * @param request
	 * 
	 * @param response
	 * 
	 * @return ActionForward which will specify the JSP to which the control
	 *         will be transferred using the mapping defined in the
	 *         struts-config.xml
	 * 
	 * @throws SwtException
	 *             Throw exception, if any
	 */
//	public String unspecified()
//			throws SwtException {
//		log.debug(this.getClass().getName() + "- [unspecified] - Entering ");
//		return displayList(mapping, form, request, response);
//	}
	
	@Override
	public String execute()
			throws Exception {
		HttpServletRequest request = ServletActionContext.getRequest();	
		
		log.debug("Enter into logonActon.'unspecified' method");
		// List of methods "reLogin", "login", "loginFail", "preLoginScreen", "preLoginScreenData"
		String method = String.valueOf(request.getParameter("method"));
		switch (method) {
			case "displayList":
				return displayList();
			case "add":
				return add();
			case "save":
				return save();
			case "change":
				return change();
			case "update":
				return update();
			case "delete":
				return delete();
			case "displayListByEntity":
				return displayListByEntity();
			default:
				break;
		}
		return displayList();

	}

	
	/**
	 * This method is used to display the Reason Maintenance details
	 * 
	 * @param mapping
	 * 
	 * @param form
	 * 
	 * @param request
	 * 
	 * @param response
	 * 
	 * @return ActionForward which will specify the JSP to which the control
	 *         will be transferred using the mapping defined in the
	 *         struts-config.xml
	 * 
	 * @throws SwtException
	 *             Throw exception, if any
	 */
	public String displayList()
			throws SwtException {
		log.debug(this.getClass().getName() + "- [displayList] - Entering ");
		/* Local variable declaration and initialization */
		String hostId = null;
		String entityId = null;
		/*
		 * Struts Framework built-in Class used to set and get the Form(JSP)
		 * values
		 */
		/* Gets the instance from struts-config.xml file */
		ReasonMaintenance reasonMaintenance = this.reasonMaintenance;

		HttpServletRequest request = ServletActionContext.getRequest();
		try {
			if(reasonMaintenance == null)
				reasonMaintenance = new ReasonMaintenance();
			/* Getting the hostId from swtcommon.properties file */
			hostId = CacheManager.getInstance().getHostId();
			/* Getting the User current entityId */
			entityId = SwtUtil.getUserCurrentEntity(request.getSession());
			/* Sets the entityId in reasonMaintenance bean object */
			reasonMaintenance.getId().setEntityId(entityId);
			/* Puts the EntityAccess rights into request object */
			putEntityAccessInReq(request, entityId);
			/* Gets the Reason Maintenance details */
			Collection data = reasonMaintenanceManager
					.getReasonMaintenanceDetails(hostId, entityId);
			/*
			 * Sets the Reason Maintenance details and entity into request
			 * object
			 */
			
			this.reasonMaintenance = reasonMaintenance;
			this.reasonMaintenanceDetails = data;
			this.entityName = entityId;
			request.setAttribute("entityName", entityId);
			request.setAttribute("reasonMaintenanceDetails", data);
			/* Puts the list of entities into request object */
			putEntityListInReq(request);
			log.debug(this.getClass().getName() + "- [displayList] - Exiting ");
			return "success";
		} catch (Exception exp) {
			exp.printStackTrace();
			log.error(this.getClass().getName()
					+ "- [displayList] - Exception " + exp.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "displayList", ReasonMaintenanceAction.class),
					request, "");

			return "fail";
		}
	}

//	private Collection entities;
//	public Collection getEntities() {
//	    return entities;
//	}
//	public void setEntities(Collection entities) {
//	    this.entities = entities;
//	}
	
	private Collection reasonMaintenanceDetails;
	private String entityName;
	private String EntityAccess;
	private String entityId;
	private String methodName;
	private String parentFormRefresh;

	public Collection getReasonMaintenanceDetails() {
		return reasonMaintenanceDetails;
	}

	public void setReasonMaintenanceDetails(Collection reasonMaintenanceDetails) {
		this.reasonMaintenanceDetails = reasonMaintenanceDetails;
	}

	public String getEntityName() {
		return entityName;
	}

	public void setEntityName(String entityName) {
		this.entityName = entityName;
	}
	
	/**
	 * This method is used to display the Add Reason Maintenance Screen
	 * 
	 * @param mapping
	 * 
	 * @param form
	 * 
	 * @param request
	 * 
	 * @param response
	 * 
	 * @return ActionForward which will specify the JSP to which the control
	 *         will be transferred using the mapping defined in the
	 *         struts-config.xml
	 * 
	 * @throws SwtException
	 *             Throw exception, if any
	 */
	public String add()
			throws SwtException {
		log.debug(this.getClass().getName() + "- [add] - Entering ");
		/* Local variable declaration and initialization */
		String hostId = null;
		String entityId = null;
		String entityName = null;
		/*
		 * Struts Framework built-in Class used to set and get the Form(JSP)
		 * values
		 */
		/* Create the instance for ReasonMaintenance */
		ReasonMaintenance reasonMaintenance = new ReasonMaintenance();
		HttpServletRequest request = ServletActionContext.getRequest();
		try {
			/* Getting the hostId from swtcommon.properties file */
			hostId = CacheManager.getInstance().getHostId();
			/* Getting the entityId and entityName from request */
			entityId = request.getParameter("entityId");
			entityName = request.getParameter("entityText");
			/* Sets the hostId in reasonMaintenance bean object */
			reasonMaintenance.getId().setHostId(hostId);
			/* Sets the entityId in reasonMaintenance bean object */
			reasonMaintenance.getId().setEntityId(entityId);
			/* Sets the bean object in dyna form */
//			dyForm.set("reasonMaintenance", reasonMaintenance);
			this.reasonMaintenance = reasonMaintenance; 
			/* Sets the entityId ,entityName and methodName in request object */
			request.setAttribute("entityId", entityId);
			request.setAttribute("entityName", entityName);
			request.setAttribute("methodName", "save");
			this.entityId = entityId;
			this.entityName = entityName;
			this.methodName = "save";
		} catch (Exception exp) {
			log.error(this.getClass().getName() + "- [add] - Exception "
					+ exp.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "add", ReasonMaintenanceAction.class), request, "");
			return "fail";
		}
		log.debug(this.getClass().getName() + "- [add] - Exiting ");
		return "add";
	}

	/**
	 * This method is used to save the new Reason details from the form
	 * 
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return ActionForward
	 * @throws SwtException
	 */
	public String save()
			throws SwtException {
		/* Local variable declaration and initialization */
		String hostId = null;
		String entityId = null;
		String entityName = null;
		/*
		 * Struts Framework built-in Class used to set and get the Form(JSP)
		 * values
		 */
//		DynaValidatorForm dyForm = null;
		/* Gets the instance from struts-config.xml file */
		ReasonMaintenance reasonMaintenance = null;
		ActionMessages errors = null;
		HttpServletRequest request = ServletActionContext.getRequest();
		try {
			log.debug(this.getClass().getName() + "- [save] - Entering ");
//			dyForm = (DynaValidatorForm) form;
			reasonMaintenance = this.reasonMaintenance;
			/* Getting the entityName from request */
			entityName = request.getParameter("entityText");
			/* Getting the hostId from swtcommon.properties file */
			hostId = CacheManager.getInstance().getHostId();
			/* Getting the User current entityId */
			entityId = SwtUtil.getUserCurrentEntity(request.getSession());
			/* Sets the hostId in reasonMaintenance bean object */
			reasonMaintenance.getId().setHostId(hostId.trim());
			/* Sets the reason code in reasonMaintenance bean object */
			reasonMaintenance.getId().setReasonCode(
					reasonMaintenance.getId().getReasonCode().trim());
			/* Sets the bean object in dyna form */
			this.reasonMaintenance= reasonMaintenance;
			/* Sets the entityName and methodName in request object */
			request.setAttribute("entityName", entityName);
			request.setAttribute("methodName", "save");
			this.entityName = entityName;
			this.methodName = "save";
			/* Used to save the new Reason details */
			reasonMaintenanceManager
					.saveReasonMaintenanceDetails(reasonMaintenance);
			/* Sets the parent page for refresh in request object */
			this.parentFormRefresh = "yes";
			this.setParentFormRefresh("yes");
			log.debug(this.getClass().getName() + "- [save] - Exiting ");
			return "add";
		} catch (SwtException swtexp) {
			log.error(this.getClass().getName() + "- [save] - SwtException "
					+ swtexp.getMessage());
			this.methodName = "save";

			if (swtexp.getErrorCode().equals(
					"errors.DataIntegrityViolationExceptioninAdd")) {
				errors = new ActionMessages();
				errors.add("", new ActionMessage(swtexp.getErrorCode()));
				saveErrors(request, errors);

			} else {
				saveErrors(request, SwtUtil.logException(swtexp, request, ""));
			}

			return "add";
		} catch (Exception exp) {
			log.error(this.getClass().getName() + "- [save] - Exception "
					+ exp.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "save", ReasonMaintenanceAction.class), request, "");
			return "fail";
		} finally {
			errors = null;
		}
	}

	/**
	 * This method is used to display the Change Reason Maintenance Screen
	 * 
	 * @param mapping
	 * 
	 * @param form
	 * 
	 * @param request
	 * 
	 * @param response
	 * 
	 * @return ActionForward which will specify the JSP to which the control
	 *         will be transferred using the mapping defined in the
	 *         struts-config.xml
	 * 
	 * @throws SwtException
	 *             Throw exception, if any
	 */
	public String change()
			throws SwtException {

		/* Local variable declaration and initialization */
		String hostId = null;
		String entityId = null;
		String entityName = null;
		String selectedReasonCode = null;
		String selectedDescription = null;
		ReasonMaintenance reasonMaintenance = null;
		HttpServletRequest request = ServletActionContext.getRequest();
		try {
			log.debug(this.getClass().getName() + "- [change] - Entering ");
			/*
			 * Struts Framework built-in Class used to set and get the Form(JSP)
			 * values
			 */

			/* Create the instance for ReasonMaintenance */
			 reasonMaintenance = new ReasonMaintenance();

			/* Getting the hostId from swtcommon.properties file */
			hostId = CacheManager.getInstance().getHostId();
			/*
			 * Getting the entityId, entityName, selectedReasonCode and
			 * selectedDescription from request
			 */
			entityId = request.getParameter("entityId");
			entityName = request.getParameter("entityText");
			selectedReasonCode = request.getParameter("selectedReasonCode");
			// code added by nageswararao for mantis 1580 issues found on STL
			// testing
			// Get the Description and set to the Selected Description Object
			selectedDescription = URLDecoder.decode(request
					.getParameter("selectedDescription"), "UTF-8");
			/*
			 * Sets the hostId,entityId,selectedReasonCode and
			 * selectedDescription in reasonMaintenance bean object
			 */
			reasonMaintenance.getId().setHostId(hostId);
			reasonMaintenance.getId().setEntityId(entityId);
			reasonMaintenance.getId().setReasonCode(selectedReasonCode);
			reasonMaintenance.setDescription(selectedDescription);
			/* Sets the bean object in dyna form */
			this.reasonMaintenance = reasonMaintenance;
			/* Sets the entityId ,entityName and methodName in request object */
//			request.setAttribute("entityId", entityId);
//			request.setAttribute("entityName", entityName);
//			request.setAttribute("methodName", "update");
			this.entityName = entityName;
			this.setEntityId(entityId);
			this.setMethodName("update");
			log.debug(this.getClass().getName() + "- [change] - Exiting ");
		} catch (Exception exp) {
			log.error(this.getClass().getName() + "- [change] - Exception "
					+ exp.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "change", ReasonMaintenanceAction.class), request, "");
			return "fail";
		} finally {
			hostId = null;
			entityId = null;
			entityName = null;
			selectedReasonCode = null;
			selectedDescription = null;
			reasonMaintenance = null;
		}
		return "add";
	}

	/**
	 * This method is used to update the Reason Maintenance details
	 * 
	 * @param mapping
	 * 
	 * @param form
	 * 
	 * @param request
	 * 
	 * @param response
	 * 
	 * @return ActionForward which will specify the JSP to which the control
	 *         will be transferred using the mapping defined in the
	 *         struts-config.xml
	 * 
	 * @throws SwtException
	 *             Throw exception, if any
	 */
	public String update()
			throws SwtException {
		log.debug(this.getClass().getName() + "- [update] - Entering ");
		/* Local variable declaration and initialization */
		String hostId = null;
		String entityName = null;
		String selectedReasonCode = null;
		/*
		 * Struts Framework built-in Class used to set and get the Form(JSP)
		 * values
		 */
		/* Gets the instance from struts-config.xml file */
		ReasonMaintenance reasonMaintenance = this.reasonMaintenance;
		HttpServletRequest request = ServletActionContext.getRequest();
		try {
			/* Getting the hostId from swtcommon.properties file */
			hostId = CacheManager.getInstance().getHostId();
			/* Getting the entityName and selectedReasonCode from request */
			entityName = request.getParameter("entityText");
			selectedReasonCode = request.getParameter("selectedReasonCode");
			/*
			 * Sets the hostId , selectedReasonCode and reason description in
			 * reasonMaintenance bean object
			 */
			reasonMaintenance.getId().setHostId(hostId);
			reasonMaintenance.getId().setReasonCode(selectedReasonCode.trim());
			reasonMaintenance
					.setDescription(reasonMaintenance.getDescription());
			/* Sets the bean object in dyna form */
			this.reasonMaintenance= reasonMaintenance;;
			/* Sets the entityName and methodName in request object */
			this.entityName = entityName;
			this.methodName = "update";
			/* Used to update the Reason details */
			reasonMaintenanceManager
					.updateReasonMaintenanceDetails(reasonMaintenance);
			/* Sets the parent page for refresh in request object */
			this.parentFormRefresh = "yes";
			log.debug(this.getClass().getName() + "- [update] - Exiting ");
			return "add";
		} catch (SwtException swtexp) {
			log.error(this.getClass().getName() + "- [update] - SwtException "
					+ swtexp.getMessage());
			this.methodName = "update";
			saveErrors(request, SwtUtil.logException(swtexp, request, ""));
			SwtUtil.logException(swtexp, request, "");
			return "add";
		} catch (Exception exp) {
			log.error(this.getClass().getName() + "- [update] - Exception "
					+ exp.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "update", ReasonMaintenanceAction.class), request, "");
			return "fail";
		}
	}

	/**
	 * This method is used to delete the Reason details
	 * 
	 * @param mapping
	 * 
	 * @param form
	 * 
	 * @param request
	 * 
	 * @param response
	 * 
	 * @return ActionForward which will specify the JSP to which the control
	 *         will be transferred using the mapping defined in the
	 *         struts-config.xml
	 * 
	 * @throws SwtException
	 *             Throw exception, if any
	 */
	public String delete()
			throws SwtException {
		log.debug(this.getClass().getName() + "- [delete] - Entering ");
		/* Local variable declaration and initialization */
		String hostId = null;
		String entityId = null;
		String reasonCode = null;
		String description = null;
		/*
		 * Struts Framework built-in Class used to set and get the Form(JSP)
		 * values
		 */
		/* Gets the instance from struts-config.xml file */
		ReasonMaintenance reasonMaintenance = this.reasonMaintenance;
		HttpServletRequest request = ServletActionContext.getRequest();

		try {
			/* Gets the entityId from bean object */
			entityId = reasonMaintenance.getId().getEntityId();
			/* Getting the hostId from swtcommon.properties file */
			hostId = CacheManager.getInstance().getHostId();
			/* Gets the selectedReasonCode and selectedDescription from request */
			reasonCode = request.getParameter("selectedReasonCode");
			description = request.getParameter("selectedDescription");
			/*
			 * Sets the entityId, hostId , reasonCode and reason description in
			 * reasonMaintenance bean object
			 */
			reasonMaintenance.getId().setEntityId(entityId);
			reasonMaintenance.getId().setHostId(hostId);
			reasonMaintenance.getId().setReasonCode(reasonCode.trim());
			reasonMaintenance.setDescription(description.trim());
			/* Sets the bean object in dyna form */
			this.reasonMaintenance= reasonMaintenance;
			/* Used to delete the Reason details */
			reasonMaintenanceManager
					.deleteReasonMaintenanceRecord(reasonMaintenance);

		} catch (SwtException swtexp) {
			log.error(this.getClass().getName() + "- [delete] - SwtException "
					+ swtexp.getMessage());
			saveErrors(request, SwtUtil.logException(swtexp, request, ""));
			return displayListByEntity();
		} catch (Exception exp) {
			log.error(this.getClass().getName() + "- [delete] - Exception "
					+ exp.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "delete", ReasonMaintenanceAction.class), request, "");
			return "fail";
		}
		log.debug(this.getClass().getName() + "- [delete] - Exiting ");
		return displayListByEntity();
	}

	/**
	 * This method is used to put the list of entities in request object
	 * 
	 * @param request
	 * 
	 * @throws SwtException
	 *             Throw exception, if any
	 */
	private void putEntityListInReq(HttpServletRequest request)
			throws SwtException {
		log.debug(this.getClass().getName()
				+ "- [putEntityListInReq] - Entering ");
		/* Gets the current session */
		HttpSession session = request.getSession();
		/* Create an instance for Collection */
		Collection entities = new ArrayList();
		/* Gets the list of entities */
		entities = SwtUtil.getUserEntityAccessList(session);
		/* Convert to Label Value bean */
		entities = SwtUtil.convertEntityAcessCollectionLVL(entities, session);
		/* Sets the entities in request object */
//		this.entities = entities;
		request.setAttribute("entities", entities);
		
		
		log.debug(this.getClass().getName()
				+ "- [putEntityListInReq] - Exiting ");
	}

	/**
	 * This method is used to put the Entity Access rights in request object
	 * 
	 * @param request -
	 *            HttpServletRequest
	 * 
	 * @param entityId -
	 *            String
	 * 
	 * @return int
	 * 
	 * @throws SwtException
	 *             Throw exception, if any
	 */
	private int putEntityAccessInReq(HttpServletRequest request, String entityId)
			throws SwtException {
		log.debug(this.getClass().getName()
				+ "- [putEntityAccessInReq] - Entering ");
		/* Used to get the list of entity access */
		Collection coll = SwtUtil.getUserEntityAccessList(request.getSession());
		/* Used to get the Menu Entity Access */
		int accessInd = SwtUtil.getMenuEntityCurrGrpAccess(request, entityId,
				null);
		/* sets the EntityAccess in request object */
		if (accessInd == 0) {
			request.setAttribute("EntityAccess",
					SwtConstants.ENTITY_FULL_ACCESS + "");
			this.setEntityAccess(SwtConstants.ENTITY_FULL_ACCESS + "");
		} else {
			request.setAttribute("EntityAccess",
					SwtConstants.ENTITY_READ_ACCESS + "");
			this.setEntityAccess(SwtConstants.ENTITY_READ_ACCESS + "");
		}
		log.debug(this.getClass().getName()
				+ "- [putEntityAccessInReq] - Exiting ");
		return accessInd;
	}

	/**
	 * This method is used to display the Reason details by entity
	 * 
	 * @param mapping -
	 *            ActionMapping
	 * 
	 * @param form -
	 *            ActionForm
	 * 
	 * @param request -
	 *            HttpServletRequest
	 * 
	 * @param response -
	 *            HttpServletResponse
	 * 
	 * @return ActionForward which will specify the JSP to which the control
	 *         will be transferred using the mapping defined in the
	 *         struts-config.xml
	 * 
	 * @throws SwtException
	 *             Throw exception, if any
	 */
	public String displayListByEntity() throws SwtException {
		log.debug(this.getClass().getName()
				+ "- [displayListByEntity] - Entering ");
		/* Local variable declaration and initialization */
		String hostId = null;
		String entityId = null;
		/*
		 * Struts Framework built-in Class used to set and get the Form(JSP)
		 * values
		 */
		/* Gets the instance from struts-config.xml file */
		ReasonMaintenance reasonMaintenance = this.reasonMaintenance;
		HttpServletRequest request = ServletActionContext.getRequest();
		try {
			/* Getting the hostId from swtcommon.properties file */
			hostId = CacheManager.getInstance().getHostId();
			/* Getting the entityId from bean object */
			entityId = reasonMaintenance.getId().getEntityId();
			/* Sets the bean object in dyna form */
			this.reasonMaintenance = reasonMaintenance;
			/* Puts the EntityAccess rights into request object */
			putEntityAccessInReq(request, entityId);
			/* Puts the list of entites into request object */
			putEntityListInReq(request);
			/* Gets the Reason Maintenance details */
			Collection data = reasonMaintenanceManager
					.getReasonMaintenanceDetails(hostId, entityId);
			/*
			 * Sets the Reason Maintenance details and entity into request
			 * object
			 */
			this.reasonMaintenanceDetails = data;
			this.entityName = entityId;
//			request.setAttribute("reasonMaintenanceDetails", data);
//			request.setAttribute("entityName", entityId);
			log.debug(this.getClass().getName()
					+ "- [displayListByEntity] - Exiting ");
			return "success";
		} catch (SwtException swtexp) {
			log.error(this.getClass().getName()
					+ "- [displayListByEntity] - SwtException "
					+ swtexp.getMessage());
			SwtUtil.logException(swtexp, request, "");

			return "fail";
		} catch (Exception exp) {
			log
					.error(this.getClass().getName()
							+ "- [displayListByEntity] - Exception "
							+ exp.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "displayListByEntity", ReasonMaintenanceAction.class),
					request, "");

			return "fail";
		}
	}

	public String getEntityAccess() {
		return EntityAccess;
	}

	public void setEntityAccess(String entityAccess) {
		EntityAccess = entityAccess;
	}

	public String getMethodName() {
		return methodName;
	}

	public void setMethodName(String methodName) {
		this.methodName = methodName;
	}

	public String getEntityId() {
		return entityId;
	}

	public void setEntityId(String entityId) {
		this.entityId = entityId;
	}

	public String getParentFormRefresh() {
		return parentFormRefresh;
	}

	public void setParentFormRefresh(String parentFormRefresh) {
		this.parentFormRefresh = parentFormRefresh;
	}

}
