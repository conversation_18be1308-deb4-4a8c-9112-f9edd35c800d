/*
 * @(#)StartingBalanceLogAction.java 1.0 23/08/06
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.maintenance.web;

import java.text.Format;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Iterator;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;





import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.maintenance.model.BalMaintenance;
import org.swallow.maintenance.model.StartingBalanceLog;
import org.swallow.maintenance.service.StartingBalanceLogManager;
import org.swallow.util.PageDetails;
import org.swallow.util.PropertiesFileLoader;
import org.swallow.util.SwtConstants;
import org.swallow.util.SwtUtil;
import org.swallow.util.SystemFormats;
import org.swallow.util.LabelValueBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.apache.struts2.convention.annotation.Result;
import org.apache.struts2.convention.annotation.AllowedMethods;
import org.apache.struts2.convention.annotation.Action;
import org.apache.struts2.ServletActionContext;
import org.swallow.util.struts.CustomActionSupport;

/**
 * <pre>
 * This is the Action class which contains all the methods, many of which are
 * invoked form front end. 
 * - Display Log for selected balances
 * - Display records in pages
 * </pre>
 * 
 * 
 */
@Action(value = "/balanceLog", results = {
	@Result(name = "fail", location = "/error.jsp"),
	@Result(name = "success", location = "/jsp/maintenance/startingbalanceauditlog.jsp"),
})

@AllowedMethods ({"getBlanceLogDetails" ,"next" })
public class StartingBalanceLogAction extends CustomActionSupport {
public String execute() throws Exception {
    HttpServletRequest request = ServletActionContext.getRequest();

    // List of methods 
    String method = String.valueOf(request.getParameter("method"));

    switch (method) {
        case "unspecified":
            return unspecified();
        case "getBlanceLogDetails":
            return getBlanceLogDetails();
        case "next":
            return next();
        default:
            break;
    }

    return unspecified();
}



	/**
	 * Instance of StartingBalanceLogManager class
	 */
    @Autowired
	private StartingBalanceLogManager startBalLogMgr = null;

	/**
	 * Initializing logger object for this class
	 */
	private final Log log = LogFactory.getLog(StartingBalanceLogAction.class);

	/**
	 * Setter Method for Manager bean
	 * 
	 * @param startBalLogMgr
	 * @return
	 */
	public void setStartingBalanceLogManager(
			StartingBalanceLogManager startBalLogMgr) {
		this.startBalLogMgr = startBalLogMgr;
	}
	
	private StartingBalanceLog startingBalanceLog;

	

	public StartingBalanceLog getStartingBalanceLog() {
		return startingBalanceLog;
	}

	public void setStartingBalanceLog(StartingBalanceLog startingBalanceLog) {
		this.startingBalanceLog = startingBalanceLog;
	}

	/**
	 * This method is the default method of the action which further calls the
	 * getBlanceLogDetails method
	 * 
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return ActionForward
	 * @throws SwtException
	 */
	public String unspecified()
			throws SwtException {
HttpServletRequest request = ServletActionContext.getRequest();
HttpServletResponse response = ServletActionContext.getResponse();


		log.debug(this.getClass().getName()
				+ " - [unspecified] -  returns getBlanceLogDetails");
		return getBlanceLogDetails();
	}

	// Start: Method modified by Vivekanandan A for mantis 1767 20-09-2012
	/**
	 * Method to load Starting Balance log screen will display balance changes
	 * log for selected balance in Start of day screen.
	 * 
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return ActionForward
	 * @throws SwtException
	 */
	private String getBlanceLogDetails() throws SwtException {
HttpServletRequest request = ServletActionContext.getRequest();
HttpServletResponse response = ServletActionContext.getResponse();

		/* Method's local variable declaration */
		// String to hold entityID
		String entityId = null;
		// String to hold selected date
		String selectedDate = null;
		// String to hold balance type id
		String balanceTypeId = null;
		// String to hold currenct filter
		String currentFilter = null;
		// String to hold current sort
		String currentSort = null;
		// String to hold filter and sort status
		String filterSortStatus = null;
		// String to hold balance type name
		String balanceTypeName = null;
		// boolean variable to check next page
		boolean isNext = false;
		// integer to hold maximum number of pages
		int maxPage = 0;
		// integer to hold current page number
		int currentPage = 1;
		// integer to hold initial page count
		int initialPageCount = 0;
		// SystemFormat instance to hold formats
		SystemFormats format = null;
		// StartingBalanceLog instance to hold balance log record
		StartingBalanceLog startBalLog = null;
		// Format instance to format date
		Format formatter = null;
		// String to hold currency code
		String selectedCurrencyCode = null;
		// List to hold balance log details
		ArrayList<StartingBalanceLog> balLogList = null;
		// DynaValidatorForm instance to hold form bean
		// DynaValidatorForm dyForm = null;
		// variable for pageSize
		int pageSize = 0;
		// variable for totalCount
		int totalCount = 0;
		try {
			log.debug(this.getClass().getName()
					+ " - [getBlanceLogDetails] - Entry");
			// assign the startingBalanceLog
			// Read the entityCode from request and store it in string variable
			entityId = request.getParameter("entityCode");
			// Get full date format
			formatter = new SimpleDateFormat(SwtUtil.getCurrentDateFormat(request.getSession()));
			// Read the selectedDate from request and store it in string
			// variable
			selectedDate = formatter.format(SwtUtil.parseDate(request
					.getParameter("selectedDate"), SwtUtil
					.getCurrentDateFormat(request.getSession())));
			// get selected currency
			selectedCurrencyCode = request.getParameter("selectedCurrency");

			// Read the selectedBalTypeId from request and store it in string
			// variable
			balanceTypeId = request.getParameter("selectedBalTypeId");
			// Read the selectedBalTypeName from request and store it in string
			// variable
			balanceTypeName = request.getParameter("selectedBalTypeName");

			// Get the Current system formats from SwtUtil and store it in
			// SystemFormats object
			format = SwtUtil.getCurrentSystemFormats(request.getSession());

			// Read SelectedFilter assign from request assign to the
			// currentFilter
			currentFilter = (SwtUtil.isEmptyOrNull(request
					.getParameter("selectedFilter"))) ? "all" : request
					.getParameter("selectedFilter").trim();

			// Read SelectedSort assign from request assign to the currentSort
			currentSort = (SwtUtil.isEmptyOrNull(request
					.getParameter("selectedSort"))) ? "none" : request
					.getParameter("selectedSort").trim();

			// Use # as separator for the combined current filter and current
			// sort
			filterSortStatus = currentFilter + "#" + currentSort;
			// assign the balLogList
			balLogList = new ArrayList<StartingBalanceLog>();

			// Retrieve the total count from balance log details from DB based
			// on the parameters passed
			totalCount = startBalLogMgr.getBalanceLogDetails(entityId,
					selectedDate, balanceTypeId, currentPage - 1,
					initialPageCount, balLogList, filterSortStatus, format);

			// get the properties for pageSize
			pageSize=SwtUtil.getPageSizeFromProperty(SwtConstants.SYSTEM_LOG_SCREEN_PAGE_SIZE);
			// Max page is set according to the page size and totalcount
			maxPage = totalCount / pageSize;

			// condition to check remainder of totalCount /pageSize is greater
			// than 0
			if ((totalCount % pageSize) > 0) {
				maxPage++;
			}
			// Condition checked to enable or disable the next link
			if (maxPage > 1) {
				// enable next label
				isNext = true;
			}

			// Condition check for current sort equal to none
			if ("none".equals(currentSort)) {
				// default sorting column
				currentSort = "0|true";
			}
			// create the StartingBalanceLog object
			startBalLog = new StartingBalanceLog();
			// format balance logs with given date format and currency format
			// and currency decimal format
			loadBalanceLogs(balLogList, selectedCurrencyCode, format,
					startBalLog, selectedDate, balanceTypeId, balanceTypeName,
					currentPage, maxPage, currentPage, request, totalCount);
			// set the currentPage in request
			request.setAttribute("currentPage", Integer.toString(currentPage));
			// set the boolean value in request
			request.setAttribute("prevEnabled", "false");
			// set the isNext in request
			request.setAttribute("nextEnabled", isNext ? "true" : "false");
			// set the currentFilter in request
			request.setAttribute("selectedFilter", currentFilter);
			// set the currentSort in request
			request.setAttribute("selectedSort", currentSort);
			// set the entityId in request
			request.setAttribute("entityId", entityId);
			// set the balType in request
			request.setAttribute("balType", request
					.getParameter("selectedBalType"));
			// set the balanceTypeId in request
			request.setAttribute("balanceTypeId", balanceTypeId);
			// set the balanceTypeName in request
			request.setAttribute("selectedBalTypeName", balanceTypeName);
			// set the balanceTypeId in request
			request.setAttribute("balanceTypeName", balanceTypeId);
			// set the selectedDate in request
			request.setAttribute("date", selectedDate);
			// set the balLogList in request
			request.setAttribute("balLogDetails", balLogList);
			// set the selectedCurrencyCode in request
			request.setAttribute("selectedCurrency", selectedCurrencyCode);
			// set the startingBalanceLog form
			setStartingBalanceLog(startBalLog);
			log.debug(this.getClass().getName()
					+ " - [getBlanceLogDetails] - Exit");
		} catch (SwtException swtexp) {
			log
					.error(this.getClass().getName()
							+ " - Exception Catched in [getBlanceLogDetails] method : - "
							+ swtexp.getMessage());
			saveErrors(request, SwtUtil.logException(swtexp, request, ""));

			return ("success");
		} catch (Exception exp) {
			log
					.error(this.getClass().getName()
							+ " - Exception Catched in [getBlanceLogDetails] method : - "
							+ exp.getMessage());
			SwtUtil.logException(
					SwtErrorHandler.getInstance().handleException(exp,
							"getBlanceLogDetails",
							StartingBalanceLogAction.class), request, "");

			return ("fail");
		} finally {
			entityId = null;
			selectedDate = null;
			balanceTypeId = null;
			currentFilter = null;
			currentSort = null;
			filterSortStatus = null;
			balanceTypeName = null;
			startBalLog = null;
			formatter = null;
			selectedCurrencyCode = null;
			balLogList = null;
		}

		return ("success");
	}

	// End: Method modified by Vivekanandan A for mantis 1767 20-09-2012

	// Start: Method modified by Vivekanandan A for mantis 1767 20-09-2012
	/**
	 * This method is called when pagination changed . It fetches the system Log
	 * details for that page.
	 * 
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return ActionForward
	 * @throws SwtException
	 */
	public String next()
			throws SwtException {
HttpServletRequest request = ServletActionContext.getRequest();
HttpServletResponse response = ServletActionContext.getResponse();

		/* Method's local variable and class instance declaration */
		// String to hold entityID
		String entityId = null;
		// String to hold date
		String date = null;
		// String to hold balance type id
		String balanceTypeId = null;
		// String to hold balance type
		String balType = null;
		// String to hold current filter
		String currentFilter = null;
		// String to hold current sort
		String currentSort = null;
		// String to hold filterSortStatus
		String filterSortStatus = null;
		// String to hold selected currency code
		String selectedCurrencyCode = null;
		// String to hold balance type name
		String balanceTypeName = null;
		// Integer to hold current page number
		int currentPage;
		// Integer to hold the page size
		int pageSize;
		// integer to hold clicked page number6
		int clickedPage;
		// integer to hold maximum page size
		int maxPage;
		// Integer to hold the total count value
		int totalCount;
		// SystemFormat instance to hold formats
		SystemFormats sysFormat = null;
		// List to hold balance log details
		ArrayList<StartingBalanceLog> balLogList = null;
		// StartingBalanceLog instance to hold balance log record
		StartingBalanceLog startBalLog = null;
		// DynaValidatorForm instance to hold form bean
		// DynaValidatorForm dyForm = null;
		try {

			log.debug(this.getClass().getName() + " - [next] - Entry");
			// assign the form
			// get selected currency code from request
			selectedCurrencyCode = request.getParameter("selectedCurrency");
			// Read entity from request
			entityId = request.getParameter("entityId");
			// Read Date from request
			date = request.getParameter("date");
			// Read balance type from request
			balanceTypeId = request.getParameter("balanceTypeId");
			// Read Balance type from request
			balType = request.getParameter("balType");
			// Read Balance type Name from request
			balanceTypeName = request.getParameter("selectedBalTypeName");
			// Get System format from session
			sysFormat = SwtUtil.getCurrentSystemFormats(request.getSession());

			// Get selectedFilter from request assign to currentFilter
			currentFilter = (!SwtUtil.isEmptyOrNull(request
					.getParameter("selectedFilter"))) ? request.getParameter(
					"selectedFilter").trim() : "all";

			// Get selectedSort from request assign to currentFilter
			currentSort = (!SwtUtil.isEmptyOrNull(request
					.getParameter("selectedSort"))) ? request.getParameter(
					"selectedSort").trim() : "1|false";

			// concatenation currentFilter and currentSort assign to the assign
			// filterSortStatus
			filterSortStatus = currentFilter + "#" + currentSort;

			// Read current page from request to the variable currentpage

			currentPage = Integer.parseInt(!SwtUtil.isEmptyOrNull(request
					.getParameter("currentPage")) ? request.getParameter(
					"currentPage").trim() : "1");
			// Read pageNo assign from request to the clickedPage

			clickedPage = Integer.parseInt(!SwtUtil.isEmptyOrNull(request
					.getParameter("goToPageNo")) ? request.getParameter(
					"goToPageNo").trim() : "1");

			// Read maxpages from request assign the maxpage
			maxPage = Integer.parseInt(!SwtUtil.isEmptyOrNull(request
					.getParameter("maxPages")) ? request.getParameter(
					"maxPages").trim() : "0");

			// Condition to check clicked page is -2
			if (clickedPage == -2) {
				// Get previous page records

				currentPage--;
				clickedPage = currentPage;

			} else if (clickedPage == -1) {
				// Get Next page records
				currentPage++;
				// assign to the nextpage
				clickedPage = currentPage;
			} else {
				// leave it to the current page
				currentPage = clickedPage;

			}
			// get the StartingBalanceLog
			balLogList = new ArrayList<StartingBalanceLog>();

			// Get max Page value and log records
			totalCount = startBalLogMgr.getBalanceLogDetails(entityId, date,
					balanceTypeId, currentPage - 1, maxPage, balLogList,
					filterSortStatus, sysFormat);
			// assign to the pageSize
			pageSize=SwtUtil.getPageSizeFromProperty(SwtConstants.SYSTEM_LOG_SCREEN_PAGE_SIZE);			
			// maxpage is set the totalCount and pageSize
			maxPage = totalCount / pageSize;

			// condition to check remainder of totalCount /pageSize is greater
			// than 0
			if ((totalCount % pageSize) > 0) {
				// increment max page
				maxPage++;
			}
			// create the StartingBalanceLog
			startBalLog = new StartingBalanceLog();
			// Format the balances log records with the date format, currency
			// format and currency decimal format define for the currency
			loadBalanceLogs(balLogList, selectedCurrencyCode, sysFormat,
					startBalLog, date, balanceTypeId, balanceTypeName,
					currentPage, maxPage, clickedPage, request, totalCount);
			// set the balLogList in request
			request.setAttribute("balLogDetails", balLogList);
			// set the currentFilter in request
			request.setAttribute("selectedFilter", currentFilter);
			// set the currentSort in request
			request.setAttribute("selectedSort", currentSort);
			// set the balanceTypeName in request
			request.setAttribute("selectedBalTypeName", balanceTypeName);
			// set the entityId in request
			request.setAttribute("entityId", entityId);
			// set the balType in request
			request.setAttribute("balType", balType);
			// set the balanceTypeId in request
			request.setAttribute("balanceTypeId", balanceTypeId);
			// set the date in request
			request.setAttribute("date", date);
			// set the selectedCurrencyCode in request
			request.setAttribute("selectedCurrency", selectedCurrencyCode);
			// set the startBalLog in request
			setStartingBalanceLog(startBalLog);

			log.debug(this.getClass().getName() + " - [next] - Exit");
			return ("success");
		} catch (SwtException swtexp) {

			log.error(this.getClass().getName()
					+ " - Exception Catched in [next] method : - "
					+ swtexp.getMessage());
			SwtUtil.logException(swtexp, request, "");

			return ("success");
		} catch (Exception exp) {

			log.error(this.getClass().getName()
					+ " - Exception Catched in [next] method : - "
					+ exp.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "next", StartingBalanceLogAction.class), request, "");

			return ("fail");
		} finally {
			entityId = null;
			date = null;
			balanceTypeId = null;
			balType = null;
			currentFilter = null;
			currentSort = null;
			filterSortStatus = null;
			selectedCurrencyCode = null;
			balanceTypeName = null;
			sysFormat = null;
			balLogList = null;
			startBalLog = null;

		}
	}

	// End: Method modified by Vivekanandan A for mantis 1767 20-09-2012
	/**
	 * This is used to set Start of day balance type
	 * 
	 * @param type
	 * @return String
	 */
	private String sodTypeAsString(String type) {
		log.debug(this.getClass().getName() + " - [sodTypeAsString] - Entry");
		/* Condition to check balance type is Manual */
		if (type.equals("M"))
			return SwtConstants.BALTYPE_MANUAL;
		/* Condition to check balance type is zero */
		else if (type.equals("null"))
			return SwtConstants.BALTYPE_NONE;
		else if (type.equals("Z"))
			return SwtConstants.BALTYPE_ZERO;
		/* Condition to check balance type is Internal */
		else if (type.equals("I"))
			return SwtConstants.BALTYPE_INTERNAL;
		/* Condition to check balance type is External */
		else if (type.equals("E"))
			return SwtConstants.BALTYPE_EXTERNAL;
		/* Condition to check balance type is Predicted */
		else if (type.equals("P"))
			return SwtConstants.BALTYPE_PREDICTED;
		log.debug(this.getClass().getName() + " - [sodTypeAsString] - Exit");
		return SwtConstants.BALTYPE_NONE;
	}

	// Start: Method modified by Vivekanandan A for mantis 1767
	// 20-09-2012
	/**
	 * Method to load balance log records, will get balance log and will format
	 * the date for the current date format and will format balances based on
	 * the defined decimal format of their currencies
	 * 
	 * @param balLogList
	 * @param selectedCurrencyCode
	 * @param format
	 * @param balanceTypeName
	 * @param startBalLog
	 * @param selectedDate
	 * @param balanceTypeId
	 * @param date
	 * @param currentPage
	 * @param maxPage
	 * @param clickedPage
	 * @param request
	 * @throws SwtException
	 */
	private void loadBalanceLogs(ArrayList<StartingBalanceLog> balLogList,
			String selectedCurrencyCode, SystemFormats format,
			StartingBalanceLog startBalLog, String date, String balanceTypeId,
			String balanceTypeName, int currentPage, int maxPage,
			int clickedPage, HttpServletRequest request, int totalCount)
			throws SwtException {

		// Method local variable declaration
		// Iterator instance to iterate balance logs
		Iterator<StartingBalanceLog> logItr = null;
		// String variable to hold next link status
		String nextLinkStatus = null;
		// String variable to hold previous link status
		String prevLinkStatus = null;
		// Page Details instance
		PageDetails pSummary = null;
		// set the pageSummaryList
		ArrayList<PageDetails> pageSummaryList = null;
		try {
			log.debug(this.getClass().getName()
					+ " - [loadBalanceLogs] - Entry");
			// create the pageDetails object
			pSummary = new PageDetails();

			// PageSummaryList contains the Page Link (only max 10 Pages are
			// allowed at one time) and details shown on the screen
			pageSummaryList = new ArrayList<PageDetails>();
			// set the currentpageNo in pSummary
			pSummary.setCurrentPageNo(currentPage);
			// set the MaxPages in pSummary
			pSummary.setMaxPages(maxPage);
			// set theTotalCount in in pSummary
			pSummary.setTotalCount(totalCount);
			// add the psummary to the pageSummaryList
			pageSummaryList.add(pSummary);
			// The value of nextLinkStatus change according to the condition if
			// clicked page less than maxpage
			nextLinkStatus = (clickedPage < maxPage) ? SwtConstants.STR_TRUE
					: SwtConstants.STR_FALSE;
			// condition satisfy hide pagination is true
			if (maxPage <= 1) {
				request.setAttribute("hidePagination", "true");
			}
			// The value of clicked value change according to the condition if
			// clicked page lass greater than 1
			prevLinkStatus = (clickedPage > 1) ? SwtConstants.STR_TRUE
					: SwtConstants.STR_FALSE;

			// Set the balance date as selected date
			startBalLog.setBalanceDate(SwtUtil.parseDate(date, format
					.getDateFormatValue()));
			// set the balanceTypeId
			startBalLog.setBalanceTypeId(balanceTypeId);
			// set the date
			startBalLog.setBalanceDateAsString(date);
			/* Setting balance type name using bean class */
			startBalLog.setBalanceTypeName(balanceTypeName);

			// Condition to check Log list is not null
			if ((balLogList != null) && (balLogList.size() > 0)) {
				logItr = balLogList.iterator();

				while (logItr.hasNext()) {
					startBalLog = logItr.next();
					// Set the update date for balance log in date format
					startBalLog.setUpdateDateAsString(SwtUtil.formatDate(
							startBalLog.getUpdateDate(), format
									.getDateFormatValue()));
					// Set the update date for balance log in time format
					startBalLog.setUpdateTimeAsString(SwtUtil.formatDate(
							startBalLog.getUpdateDate(), "HH:mm:ss"));

					// Condition to check ExternalSODType is null
					if (startBalLog.getNewWorkingExternalSODType() == null)
						/* Setting ExternalSODType as Zero using constant */
						startBalLog
								.setNewWorkingExternalSODTypeAsString(SwtConstants.BALTYPE_NONE);
					else
						// Set ExternalSODTypeAsString using bean class

						startBalLog
								.setNewWorkingExternalSODTypeAsString(sodTypeAsString(startBalLog
										.getNewWorkingExternalSODType()));
					// Condition to check ForecastSODType is null
					if (startBalLog.getNewWorkingForcastSODType() == null)
						// Set ForecastSODTypeAsString as Zero
						startBalLog
								.setNewWorkingForcastSODTypeAsString(SwtConstants.BALTYPE_NONE);
					else
						// Set ForecastSODTypeAsString using bean class
						startBalLog
								.setNewWorkingForcastSODTypeAsString(sodTypeAsString(startBalLog
										.getNewWorkingForcastSODType()));
					// Set WorkingForcastSODChangeAsString with its defined
					// currency format
					startBalLog.setWorkingForcastSODChangeAsString(SwtUtil
							.formatCurrency(selectedCurrencyCode, startBalLog
									.getWorkingForcastSODChange()));

					// Set NewWorkingForcastSODAsString with its defined
					// currency format
					startBalLog.setNewWorkingForcastSODAsString(SwtUtil
							.formatCurrency(selectedCurrencyCode, startBalLog
									.getNewWorkingForcastSOD()));

					
					
					
					
					
					
					
					
					
					
					// Set WorkingForcastSODChangeAsString with its defined
					// currency format
					startBalLog.setSuppliedExternalSODChangeAsString(SwtUtil
							.formatCurrency(selectedCurrencyCode, startBalLog
									.getSuppliedExternalSODChange()));

					// Set NewWorkingForcastSODAsString with its defined
					// currency format
					startBalLog.setNewSuppliedExternalSODAsString(SwtUtil
							.formatCurrency(selectedCurrencyCode, startBalLog
									.getNewSuppliedExternalSOD()));
					
					
					

					
					
					// Set WorkingForcastSODChangeAsString with its defined
					// currency format
					startBalLog.setSuppliedInternalSODChangeAsString(SwtUtil
							.formatCurrency(selectedCurrencyCode, startBalLog
									.getSuppliedInternalSODChange()));

					// Set NewWorkingForcastSODAsString with its defined
					// currency format
					startBalLog.setNewSuppliedInternalSODAsString(SwtUtil
							.formatCurrency(selectedCurrencyCode, startBalLog
									.getNewSuppliedInternalSOD()));
					
					
					
					
					
					
					
					
					
					
					
					
					
					// Set BvForecastAdjustChangeAsString with its defined
					// currency format
					startBalLog.setBvForecastAdjustChangeAsString(SwtUtil
							.formatCurrency(selectedCurrencyCode, startBalLog
									.getBvForecastAdjustChange()));

					// Set NewBVForecastAdjustAsString with its defined
					// currency format
					startBalLog.setNewBVForecastAdjustAsString(SwtUtil
							.formatCurrency(selectedCurrencyCode, startBalLog
									.getNewBVForecastAdjust()));

					// Set WorkingExternalSODChangeAsString with its defined
					// currency format
					startBalLog.setWorkingExternalSODChangeAsString(SwtUtil
							.formatCurrency(selectedCurrencyCode, startBalLog
									.getWorkingExternalSODChange()));
					// Set NewWorkingExternalSODAsString with its defined
					// currency format
					startBalLog.setNewWorkingExternalSODAsString(SwtUtil
							.formatCurrency(selectedCurrencyCode, startBalLog
									.getNewWorkingExternalSOD()));
					// Set BvExternalAdjustChangeAsString with its defined
					// currency format
					startBalLog.setBvExternalAdjustChangeAsString(SwtUtil
							.formatCurrency(selectedCurrencyCode, startBalLog
									.getBvExternalAdjustChange()));
					// Set NewBVExternalAdjustAsString with its defined
					// currency format
					startBalLog.setNewBVExternalAdjustAsString(SwtUtil
							.formatCurrency(selectedCurrencyCode, startBalLog
									.getNewBVExternalAdjust()));

					// Condition to check WorkingForcastSODChange has value and
					// value < 0.0

					if ((startBalLog.getWorkingForcastSODChange() != null)
							&& (startBalLog.getWorkingForcastSODChange()
									.doubleValue() < 0.0)) {
						// Set negative value for WorkingForcastSODChange
						startBalLog.setWorkingForcastSODChangeNegative(true);
					}

					// Condition to check NewWorkingForcastSOD has value and
					// value < 0.0
					if ((startBalLog.getNewWorkingForcastSOD() != null)
							&& (startBalLog.getNewWorkingForcastSOD()
									.doubleValue() < 0.0)) {
						// Setting negative value for NewWorkingForcastSOD
						startBalLog.setNewWorkingForcastSODNegative(true);
					}
					// Condition to check BvForecastAdjustChange has value and
					// value < 0.0
					if ((startBalLog.getBvForecastAdjustChange() != null)
							&& (startBalLog.getBvForecastAdjustChange()
									.doubleValue() < 0.0)) {
						// Setting negative value for BvForecastAdjustChange
						startBalLog.setBvForecastAdjustChangeNegative(true);
					}
					// Condition to check NewBVForecastAdjust has value and
					// value < 0.0

					if ((startBalLog.getNewBVForecastAdjust() != null)
							&& (startBalLog.getNewBVForecastAdjust()
									.doubleValue() < 0.0)) {
						// Setting negative value for NewBVForecastAdjust
						startBalLog.setNewBVForecastAdjustNegative(true);
					}

					// Condition to check WorkingExternalSODChange has value and
					// value < 0.0
					if ((startBalLog.getWorkingExternalSODChange() != null)
							&& (startBalLog.getWorkingExternalSODChange()
									.doubleValue() < 0.0)) {
						// Setting negative value for WorkingExternalSODChange
						startBalLog.setWorkingExternalSODChangeNegative(true);
					}

					// Condition to check NewWorkingExternalSOD has value and
					// value < 0.0
					if ((startBalLog.getNewWorkingExternalSOD() != null)
							&& (startBalLog.getNewWorkingExternalSOD()
									.doubleValue() < 0.0)) {
						// Setting negative value for NewWorkingExternalSOD
						startBalLog.setNewWorkingExternalSODNegative(true);
					}
					
					
					
					
					
					
					
					
					
					
					
					
					
					// Condition to check WorkingExternalSODChange has value and
					// value < 0.0
					if ((startBalLog.getSuppliedExternalSODChange() != null)
							&& (startBalLog.getSuppliedExternalSODChange()
									.doubleValue() < 0.0)) {
						// Setting negative value for WorkingExternalSODChange
						startBalLog.setSuppliedExternalSODChangeNegative(true);
					}

					// Condition to check NewWorkingExternalSOD has value and
					// value < 0.0
					if ((startBalLog.getNewSuppliedExternalSOD() != null)
							&& (startBalLog.getNewSuppliedExternalSOD()
									.doubleValue() < 0.0)) {
						// Setting negative value for NewWorkingExternalSOD
						startBalLog.setNewSuppliedExternalSODNegative(true);
					}
					
					
					// Condition to check WorkingExternalSODChange has value and
					// value < 0.0
					if ((startBalLog.getSuppliedInternalSODChange() != null)
							&& (startBalLog.getSuppliedInternalSODChange()
									.doubleValue() < 0.0)) {
						// Setting negative value for WorkingExternalSODChange
						startBalLog.setSuppliedInternalSODChangeNegative(true);
					}
					
					// Condition to check NewWorkingExternalSOD has value and
					// value < 0.0
					if ((startBalLog.getNewSuppliedInternalSOD() != null)
							&& (startBalLog.getNewSuppliedInternalSOD()
									.doubleValue() < 0.0)) {
						// Setting negative value for NewWorkingExternalSOD
						startBalLog.setNewSuppliedInternalSODNegative(true);
					}
					
					
					
					
					
					
					
					
					
					
					
					
					
					

					// Condition to check BvExternalAdjustChange has value and
					// value < 0.0
					if ((startBalLog.getBvExternalAdjustChange() != null)
							&& (startBalLog.getBvExternalAdjustChange()
									.doubleValue() < 0.0)) {
						// Setting negative value for BvExternalAdjustChange
						startBalLog.setBvExternalAdjustChangeNegative(true);
					}

					// Condition to check NewBVExternalAdjust has value and
					// value < 0.0

					if ((startBalLog.getNewBVExternalAdjust() != null)
							&& (startBalLog.getNewBVExternalAdjust()
									.doubleValue() < 0.0)) {
						// Setting negative value for NewBVExternalAdjust
						startBalLog.setNewBVExternalAdjustNegative(true);
					}

				}

			} else { // If there are no log details
				balLogList = new ArrayList<StartingBalanceLog>();
			}
			// set the balLogList in request
			request.setAttribute("balLogDetails", balLogList);
			// set the currentPage in request
			request.setAttribute("currentPage", Integer.toString(currentPage));
			// set the prevLinkStatus in request
			request.setAttribute("prevEnabled", prevLinkStatus);
			// set the nextLinkStatus in request
			request.setAttribute("nextEnabled", nextLinkStatus);
			// set the maxPage in request
			request.setAttribute("maxPage", Integer.toString(maxPage));
			// set the pageSummaryList in request
			request.setAttribute("pageSummaryList", pageSummaryList);
			// set the balanceTypeName in request
			request.setAttribute("selectedBalTypeName", balanceTypeName);
			// set the balanceTypeId in request
			request.setAttribute("balanceTypeId", balanceTypeId);
			// set the date in request
			request.setAttribute("date", date);
			// set the totalCount in request
			request.setAttribute("totalCount", totalCount);
			// set the selectedCurrencyCode in request
			request.setAttribute("selectedCurrency", selectedCurrencyCode);
			// alignment and pagination
			log
					.debug(this.getClass().getName()
							+ " - [loadBalanceLogs] - Exit");
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [loadBalanceLogs] method : - "
					+ exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp,
					"loadBalanceLogs", this.getClass());
		} finally {
			logItr = null;
			nextLinkStatus = null;
			prevLinkStatus = null;
			pSummary = null;
			pageSummaryList = null;
		}

	}

	// End: Method modified by Vivekanandan A for mantis 1767 20-09-2012
}
