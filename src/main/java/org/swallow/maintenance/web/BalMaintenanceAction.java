/*
 * @(#) BalMaintenanceAction.java 1.0 23/08/06
 * 
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 * 
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.maintenance.web;

import java.io.IOException;
import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.Iterator;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;






import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.export.model.ColumnDTO;
import org.swallow.export.model.FilterDTO;
import org.swallow.export.service.impl.Obj2CsvImpl;
import org.swallow.export.service.impl.Obj2PdfImpl;
import org.swallow.export.service.impl.Obj2XlsImpl;
import org.swallow.export.service.impl.Obj2XmlBalMaintenance;
import org.swallow.export.service.impl.Xml2CsvImpl;
import org.swallow.export.service.impl.Xml2PdfImpl;
import org.swallow.export.service.impl.Xml2XlsImpl;
import org.swallow.maintenance.model.BalMaintenance;
import org.swallow.maintenance.model.BalType;
import org.swallow.maintenance.model.MiscParams;
import org.swallow.maintenance.model.ReasonMaintenance;
import org.swallow.maintenance.model.SODPageSummary;
import org.swallow.maintenance.service.BalMaintenanceManager;
import org.swallow.maintenance.service.ReasonMaintenanceManager;
import org.swallow.model.ExportObject;
import org.swallow.util.CacheManager;
import org.swallow.util.CommonDataManager;
import org.swallow.util.PropertiesFileLoader;
import org.swallow.util.SwtConstants;
import org.swallow.util.SwtUtil;
import org.swallow.util.SystemFormats;
import org.swallow.util.SystemInfo;
import org.swallow.util.LabelValueBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.apache.struts2.convention.annotation.Result;
import org.apache.struts2.convention.annotation.AllowedMethods;
import org.apache.struts2.convention.annotation.Action;
import org.apache.struts2.ServletActionContext;
import org.swallow.util.struts.CustomActionSupport;

/**
 * <pre>
 * This is the action class which contains all the methods, many of which are
 * invoked form front end. 
 * - Display Start of Day screen
 * - Display Balances for different balance types
 * - Display records in pages
 * - View Selected Balances
 * - Change selected Balances
 * - View Log for selected Balances
 * - To enter reason for selected balance
 * - View reports for selected balances 
 * </pre>
 * 
 * @Modified Vivekanandan A / 12-Jan-2012
 */
@Action(value = "/balMaintenance", results = {
	@Result(name = "fail", location = "/error.jsp"),
	@Result(name = "success", location = "/jsp/maintenance/balancemaintenance.jsp"),
	@Result(name = "addNotes", location = "/jsp/maintenance/reasonedit.jsp"),
	@Result(name = "change", location = "/jsp/maintenance/balancemaintenancechange.jsp"),
	@Result(name = "save", location = "/jsp/maintenance/balancemaintenancechange.jsp"),
	@Result(name = "alert", location = "/jsp/work/enhancedalert.jsp"),
})

@AllowedMethods ({"list" ,"next" ,"displayListBalanceType" ,"getReasonCodeList" ,"change" ,"view" ,"addNotes" ,"saveNotes" ,"save" ,"exportBalanceMaintenance" ,"openAlerting" })
public class BalMaintenanceAction extends CustomActionSupport {
public String execute() throws Exception {
    HttpServletRequest request = ServletActionContext.getRequest();

    // List of methods 
    String method = String.valueOf(request.getParameter("method"));

    switch (method) {
        case "list":
            return list();
        case "unspecified":
            return unspecified();
        case "next":
            return next();
        case "displayListBalanceType":
            return displayListBalanceType();
        case "getReasonCodeList":
            return getReasonCodeList();
        case "change":
            return change();
        case "view":
            return view();
        case "addNotes":
            return addNotes();
        case "saveNotes":
            return saveNotes();
        case "save":
            return save();
        case "exportBalanceMaintenance":
            return exportBalanceMaintenance();
        case "openAlerting":
            return openAlerting();
        default:
            break;
    }

    return unspecified();
}


private BalMaintenance balmaintenance;
public BalMaintenance getBalmaintenance() {
	if (balmaintenance == null) {
		balmaintenance = new BalMaintenance();
	}
	return balmaintenance;
}
public void setBalmaintenance(BalMaintenance balmaintenance) {
	this.balmaintenance = balmaintenance;
	HttpServletRequest request = ServletActionContext.getRequest();
	request.setAttribute("balmaintenance", balmaintenance);
}
private String selectedBalTypeId;
public String getSelectedBalTypeId() {
	if (selectedBalTypeId == null) {
		selectedBalTypeId = new String();
	}
	return selectedBalTypeId;
}
public void setSelectedBalTypeId(String selectedBalTypeId) {
	this.selectedBalTypeId = selectedBalTypeId;
	HttpServletRequest request = ServletActionContext.getRequest();
	request.setAttribute("selectedBalTypeId", selectedBalTypeId);
}
private BalType balType;
public BalType getBalType() {
	if (balType == null) {
		balType = new BalType();
	}
	return balType;
}
public void setBalType(BalType balType) {
	this.balType = balType;
	HttpServletRequest request = ServletActionContext.getRequest();
	request.setAttribute("balType", balType);
}

    @Autowired
	private BalMaintenanceManager balmaintMgr = null;

	/**
	 * Initializing logger object for this class
	 */
	private final Log log = LogFactory.getLog(BalMaintenanceAction.class);

	/**
	 * Method to set BalMaintenanceManager
	 * 
	 * @param balMaintenanceManager
	 * @return
	 */
	public void setBalMaintenanceManager(
			BalMaintenanceManager balMaintenanceManager) {
		this.balmaintMgr = balMaintenanceManager;
	}

	/**
	 * This method set the currencyList of CurrencyVO in the request attribute
	 * 
	 * @param request
	 * @param entityId
	 * @return
	 * @throws SwtException
	 */
	private void putCurrencyDetailListWithAllInReq(HttpServletRequest request,
			String entityId) throws SwtException {
		log.debug(this.getClass().getName()
				+ " - [putCurrencyDetailListWithAllInReq] - Entry");

		/* Method's local variable and class declaration */
		String hostId;
		String roleId = "";
		ArrayList currencyList = null;
		Collection currrencyListWithAll = null;

		/* Reads the hostId from SwtUtil */
		hostId = SwtUtil.getCurrentHostId();

		roleId = ((CommonDataManager) request.getSession().getAttribute(
				SwtConstants.CDM_BEAN)).getUser().getRoleId();

		currencyList = (ArrayList) SwtUtil.getSwtMaintenanceCache()
				.getCurrencyViewORFullAcessLVL(roleId, entityId);

		if (currencyList != null) {
			currencyList.remove(new LabelValueBean("Default", "*"));
			currrencyListWithAll = new ArrayList();
			currrencyListWithAll.add(new LabelValueBean(SwtConstants.ALL_LABEL,
					SwtConstants.ALL_VALUE));
			currrencyListWithAll.addAll(currencyList);
		}

		request.setAttribute("currencyList", currrencyListWithAll);

		log.debug(this.getClass().getName()
				+ " - [putCurrencyDetailListWithAllInReq] - " + "Exit");
	}

	/**
	 * 
	 * To put the BalanceType list and its label value bean to the request to
	 * the request
	 * 
	 * @param request
	 * @param entityId
	 * @return
	 * @throws SwtException
	 */
	private void putBalanceListInReq(HttpServletRequest request, String entityId)
			throws SwtException {

		log.debug(this.getClass().getName() + " - [putBalanceListInReq] - "
				+ "Entry");

		/* Method's class instance and local variable declaration */
		CacheManager cacheManagerInst = null;
		ArrayList coll = null;
		Iterator itr = null;
		ArrayList balanceList = null;
		LabelValueBean valueBean = null;
		cacheManagerInst = CacheManager.getInstance();
		coll = (ArrayList) cacheManagerInst.getMiscParamsLVL(
				SwtConstants.BALANCE_TYPE, entityId);

		balanceList = new ArrayList();
		/* Condition to check collection not equal to null */
		if (coll != null) {
			itr = coll.iterator();
			while (itr.hasNext()) {
				valueBean = (LabelValueBean) (itr.next());
				if ((valueBean.getLabel() != null)
						&& !valueBean.getLabel().equals("")) {
					balanceList.add(valueBean);
				}
			}
		}

		request.setAttribute("balmaintenancelist", balanceList);
		log.debug(this.getClass().getName() + " - [putBalanceListInReq] -"
				+ "Exit");
	}

	/**
	 * This method receives the access rights of the user according to their
	 * role to set the button status of the screen
	 * 
	 * @param request
	 * @param entityId
	 * @return
	 * @throws SwtException
	 */
	private void putEntityAccessInRequest(HttpServletRequest request,
			String entityId) throws SwtException {
		log.debug(this.getClass().getName()
				+ " - [putEntityAccessInRequest] -  " + "Entry");

		/* Method's local variable declaration */
		int accessInd;
		/*
		 * To get the access list of menu , entity and currency group for
		 * different role.
		 */
		accessInd = SwtUtil.getMenuEntityCurrGrpAccess(request, entityId, null);

		/*
		 * Condition to check the access of the screen and to set the button
		 * status according to the access value
		 */
		if (accessInd == 0) {
			/* Set the button status for full access */
			setButtonStatus(request, SwtConstants.STR_TRUE,
					SwtConstants.STR_TRUE);
			request.setAttribute("EntityAccess",
					SwtConstants.ENTITY_FULL_ACCESS + "");
		} else {
			/* Set the button status for view access */
			setButtonStatus(request, SwtConstants.STR_FALSE,
					SwtConstants.STR_FALSE);
			request.setAttribute("EntityAccess",
					SwtConstants.ENTITY_READ_ACCESS + "");
		}
		log.debug(this.getClass().getName()
				+ " - [putEntityAccessInRequest] - " + "Exit");
	}

	/**
	 * This method is to put the SystemDate in request attribute.
	 * 
	 * @param request
	 * @return
	 * @throws SwtException
	 */
	private void putSystemDateInRequest(HttpServletRequest request)
			throws SwtException {
		log.debug(this.getClass().getName() + " - [putSystemDateInRequest] - "
				+ "Entry");

		/* Method's local variable declaration */
		String systemDate;
		/* Collects the system date from swtUtil */
		systemDate = SwtUtil.getSystemDateString();
		request.setAttribute("systemDate", systemDate);
		log.debug(this.getClass().getName() + " - [putSystemDateInRequest] - "
				+ "Exit");
	}

	/**
	 * Loads the screen for default entity, currency,replaceBalanceDate and
	 * balanceType and returns displayListBalanceType further.
	 * 
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return ActionForward
	 * @throws SwtException
	 */

	public String list()
			throws SwtException {
HttpServletRequest request = ServletActionContext.getRequest();
HttpServletResponse response = ServletActionContext.getResponse();

		try {
			log.debug(this.getClass().getName() + " - [list] - " + "Entry");

			request.setAttribute("screenFieldsStatus", "true");
			// DynaValidatorForm /* Methods local variable and class instance declaration */
			String entityId;
			BalMaintenance balmaintenance;

			/* Initiating balmaintenance model class objects. */
			balmaintenance = (BalMaintenance) getBalmaintenance();
			/* To get the entity from balmaintenance */
			entityId = balmaintenance.getId().getEntityId();
			/*
			 * Condition to check whether the entity id is null or its length is
			 * zero
			 */
			if ((entityId == null) || (entityId.trim().length() <= 0)) {
				/*
				 * If any one of the condition is true, the current entity of
				 * the user get from the swtUtil class
				 */
				entityId = SwtUtil.getUserCurrentEntity(request.getSession());
				/* The current entity id set here as entity id. */
				balmaintenance.getId().setEntityId(entityId);
			}

			/* To set the default currency as All in the screen */
			balmaintenance.setCurrency("All");
			/*
			 * Retrieve the system date using swtUtil and set as replace balance
			 * date
			 */
			balmaintenance.setReplacebalanceDate(SwtUtil.formatDate(SwtUtil
					.getSysParamDateWithEntityOffset(entityId), SwtUtil
					.getCurrentSystemFormats(request.getSession())
					.getDateFormatValue()));

			/* Set the default balance type as 'Cash account' */
			balmaintenance.setBalanceType("C");

			/*
			 * Return displayListBalanceType to show the records for other
			 * balance types.
			 */
			log.debug(this.getClass().getName() + " [list] - " + "Exit ");

			return displayListBalanceType();

		} catch (SwtException swtexp) {

			log.error(this.getClass().getName()
					+ " - Exception Catched in [list] method : - "
					+ swtexp.getMessage());

			SwtUtil.logException(swtexp, request, "");

			return ("fail");
		} catch (Exception exp) {

			log.error(this.getClass().getName()
					+ " - Exception Catched in [list] method : - "
					+ exp.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "list", BalMaintenanceAction.class), request, "");

			return ("fail");
		}
	}

	/**
	 * Default method of the action returns list method.
	 * 
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return ActionForward
	 * @throws SwtException
	 */

	public String unspecified()
			throws SwtException {
HttpServletRequest request = ServletActionContext.getRequest();
HttpServletResponse response = ServletActionContext.getResponse();

		/* Returns list method to load the screen */
		log.debug(this.getClass().getName() + "- [unsecified] - "
				+ "Returns list method");
		return list();
	}

	/**
	 * Method for pagination
	 * 
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return ActionForward
	 * @throws SwtException
	 */
	public String next()
			throws SwtException {
HttpServletRequest request = ServletActionContext.getRequest();
HttpServletResponse response = ServletActionContext.getResponse();


		/* Method's local variable and class instance declaration */
		String entityId = null;
		String currency = null;
		String balanceDate = null;
		String balanceType = null;
		String hostId = null;
		String nextLinkStatus = null;
		String prevLinkStatus = null;
		int pageSize;
		String currPageStr = null;
		String clckPageStr = null;
		int clickedPage;
		int currentPage;
		int maxPage = 0;
		int endRowNumber;
		int startRowNumber;
		int totalCount = 0;
		String currentFilter = null;
		String currentSort = null;
		String filterSortStatus = null;
		String user = null;
		Collection<BalType> balmaintenanceDetails = null;
		String roleId = null;
		Date selectedDate = null;
		BalType balType = null;
		ArrayList<SODPageSummary> pageSummaryList = null;
		// DynaValidatorForm dyForm = null;
		BalMaintenance balmaintenance = null;
		try {
			log.debug(this.getClass().getName() + " - [next] - " + "Entry");
			// create instance for BalMaintenance
			balmaintenance = new BalMaintenance();
			// create instance for BalType
			balType = new BalType();
			/*
			 * Collects the entity from the request parameter entityId and store
			 * in the string object
			 */
			entityId = (String) request.getParameter("entityId");
			/*
			 * Set the value of entityId in the enyityId of balamaintenence
			 * model class
			 */
			balmaintenance.getId().setEntityId(entityId);
			/* Reading the currency code value from the request */
			currency = request.getParameter("currencyCode");
			/* Reading the balance date from the request */
			balanceDate = request.getParameter("balanceDate");
			/* Reading the balance type from the request */
			balanceType = request.getParameter("balanceType");
			/*
			 * Set the value of balanceDate as balanceDateAsString in the
			 * balMaintenance model class
			 */
			balmaintenance.setBalancedateAsString(balanceDate);
			/* Set the value of balance date as replaceBalanceDate */
			balmaintenance.setReplacebalanceDate(balanceDate);
			/*
			 * Set the value of currency object as currency in Balmaintenance
			 * Model class
			 */
			balmaintenance.setCurrency(currency);
			/* Read the host id form SwtUtil */
			hostId = SwtUtil.getCurrentHostId();
			/*
			 * This method receives the access rights of the user according to
			 * their role to set the button status of the screen
			 */
			putEntityAccessInRequest(request, entityId);
			/*
			 * Set the value of replacebalanceDate as balanceDate, the replace
			 * balance date returns the string it is converted to the date
			 * format by SwtUtil.parseDateGeneral method
			 */
			balmaintenance.getId().setBalanceDate(
					SwtUtil.parseDateGeneral(balmaintenance
							.getReplacebalanceDate()));
			/* Returns the balanceDate */
			selectedDate = balmaintenance.getId().getBalanceDate();
			/*
			 * Returns the page size of the screen as given in
			 * the common.properties
			 */
			pageSize=SwtUtil.getPageSizeFromProperty(SwtConstants.BALANCE_MAINTENANCE_PAGE_SIZE);
			/* Reading current page value from request */
			currPageStr = request.getParameter("currentPage");
			clckPageStr = request.getParameter("goToPageNo");
			/* Condition to check currPageStr not null and clckPageStr is null */
			if ((currPageStr != null) && (clckPageStr == null)) {
				/*
				 * If the condition satisfies both, the value of clickedPage and
				 * currentPage is loaded as current page value.
				 */
				currentPage = Integer.parseInt(request
						.getParameter("currentPage"));
				clickedPage = Integer.parseInt(request
						.getParameter("currentPage"));
			} else {
				/*
				 * If the condition not satisfies, the value of currentPage and
				 * clickedPage is loaded as current page and pageNoValue
				 * respectively
				 */
				currentPage = Integer.parseInt(request
						.getParameter("currentPage"));
				/*
				 * clickedPage = Integer.parseInt(request
				 * .getParameter("pageNoValue"));
				 */
				clickedPage = Integer.parseInt(request
						.getParameter("goToPageNo"));
				/* Condition to check whether clicked value is previous or next */
				if (clickedPage == -2) { // Previous Link Clicked
					currentPage--;
					clickedPage = currentPage;
				} else {
					if (clickedPage == -1) { // Next Link Clicked
						currentPage++;
						clickedPage = currentPage;
					} else {
						currentPage = clickedPage;
					}
				}
			}
			/* Get end row number for each page */
			endRowNumber = currentPage * pageSize;
			/* Get the start row number of each page */
			startRowNumber = endRowNumber - pageSize;
			/* Reading selected Filter value from request */
			currentFilter = request.getParameter("selectedFilter");
			/* Reading the value of selectetdSort from the request */
			currentSort = request.getParameter("selectedSort");
			/*
			 * Condition checks if the current filter is null or its length less
			 * than or equal to zero
			 */
			if (currentFilter == null || currentFilter.length() <= 0) {
				/*
				 * If the condition satisfies currentFilter is set 'All' as the
				 * default value for all column
				 */
				currentFilter = "All|All|All|All|All|All|All|All|All|All|All|All";
			}
			/*
			 * Condition to check current filter is null or lessthan or equal to
			 * zero
			 */
			if (currentSort == null || currentSort.length() <= 0) {
				/*
				 * Set current as first column in ascending
				 */
				currentSort = "1|false";
			}
			filterSortStatus = currentFilter + "#" + currentSort;
			// get the role Id
			roleId = ((CommonDataManager) request.getSession().getAttribute(
					SwtConstants.CDM_BEAN)).getUser().getRoleId();
			/*
			 * Passing the values to the accountDetail method of
			 * BalMaintenaceManager
			 */
			balmaintenanceDetails = balmaintMgr.accountDetail(hostId, entityId,
					balanceType, currency, selectedDate, startRowNumber,
					endRowNumber, filterSortStatus, roleId);
			/* To iterate the collection return from the Manager class */
			Iterator<BalType> itr = balmaintenanceDetails.iterator();
			while (itr.hasNext()) {
				balType = (BalType) itr.next();
				/* Get the total row count from the collection */
				totalCount = balType.getRowCount();
				break;
			}
			/* Returns the integer value of maximum number of pages. */
			maxPage = setMaxPageAttribute(totalCount, pageSize);
			/* Condition to check the licked page is greater than 1 */
			if (clickedPage > 1) {
				/*
				 * If the condition is true it enables the previous link in the
				 * screen
				 */
				prevLinkStatus = "true";
			} else {
				/*
				 * If the condition is false it disables the previous link in
				 * the screen
				 */
				prevLinkStatus = "false";
			}
			/* Condition to check the clicked page is less than maxpage */
			if (clickedPage < maxPage) {
				/*
				 * If the condition is true it enables the Next link in the
				 * screen
				 */
				nextLinkStatus = "true";
			} else {
				/*
				 * If the condition is false it disables the Next link in the
				 * screen
				 */
				nextLinkStatus = "false";
			}
			/* Instantiating pageSummaryList to array list */
			pageSummaryList = new ArrayList<SODPageSummary>();
			/* To set page summary list for screen */
			setPageSummaryList(entityId, currentPage, maxPage, totalCount,
					pageSummaryList);

			// Hide pagination if no of records are "0" or records can be
			// displayed in one page.
			if (maxPage <= 1) {
				request.setAttribute("hidePagination", true);
			}
			request.setAttribute("currentPage", "" + currentPage);
			request.setAttribute("pageSize", "" + pageSize);
			request.setAttribute("maxPage", maxPage + "");
			request.setAttribute("prevEnabled", prevLinkStatus);
			request.setAttribute("nextEnabled", nextLinkStatus);
			request.setAttribute("pageSummaryList", pageSummaryList);
			request.setAttribute("balanceType", balanceType);
			request.setAttribute("totalCount", totalCount);
			request.setAttribute("filterSortStatus", filterSortStatus);
			request.setAttribute("selectedFilter", currentFilter);
			request.setAttribute("selectedSort", currentSort);

			/*
			 * This method set the currencyList of CurrencyVO in the request
			 * attribute
			 */
			putCurrencyDetailListWithAllInReq(request, entityId);

			/*
			 * To put the BalanceType list and its label value bean to the
			 * request to the request
			 */

			putBalanceListInReq(request, entityId);

			/* To iterate the collection return from the Manager class */
			Iterator<BalType> itr1 = balmaintenanceDetails.iterator();
			while (itr1.hasNext()) {
				balType = (BalType) itr1.next();
				/* Returns the User name */
				user = balType.getUser();

				/* Condition to check the user is null */
				if (user.equalsIgnoreCase(" ")) {

					/*
					 * If the condition returns true it set the values of
					 * startBalanceAsString,inputDateAsString inputTimeAsString
					 * and balanceSource as null
					 */
					balType.setStartBalanceAsString(" ");
					balType.setInputDateAsString(" ");
					balType.setInputTimeAsString(" ");
					balType.setBalanceSource(" ");
				} else {
					/*
					 * Set the input date as string by formating the date in a
					 * format by swtutil format date method which has inputDate
					 * as date parameter and the date format value is return
					 * from the current system formats of SwtUtil
					 */
					balType.setInputDateAsString(SwtUtil.formatDate(balType
							.getInputDate(), SwtUtil.getCurrentSystemFormats(
							request.getSession()).getDateFormatValue()));
					/* Set the value of input Time as InputTimeAsString */
					balType.setInputTimeAsString(balType.getInputTime());

					/*
					 * Condition to check external SOD has value and less than
					 * 0.0
					 */
					if (balType.getExternalSOD() != null
							&& balType.getExternalSOD().doubleValue() < 0.0)
						/* Setting negative value using bean class */
						balType.setExternalSODNegative(true);
					/*
					 * Condition to check forecast SOD has value and less than
					 * 0.0
					 */
					if (balType.getForecastSOD() != null
							&& balType.getForecastSOD().doubleValue() < 0.0)
						/* Setting negative value using bean class */
						balType.setForecastSODNegative(true);

					/*
					 * Getting forecast SOD value then convert into currency
					 * format & setting using bean class
					 */
					balType.setForecastSODAsString(SwtUtil.formatCurrency(
							balType.getBalCurrencyCode(), balType
									.getForecastSOD()));
				}
			}
			if (balType.getExternalSOD() == null) {
				/*
				 * Getting exernal SOD value then convert into currency format &
				 * setting using bean class
				 */
				balType.setExternalSODAsString(SwtUtil.formatCurrency(balType
						.getBalCurrencyCode(), balType.getExternalSOD()));
			}
			/*
			 * Put the entity access list and label value bean of the entity to
			 * the request
			 */
			putEntityListInReq(request);
			request
					.setAttribute("balmaintenanceDetails",
							balmaintenanceDetails);
			setBalmaintenance(balmaintenance);
			/* This method is to put the SystemDate in request attribute. */
			putSystemDateInRequest(request);
			log.debug(this.getClass().getName() + " - [next] - " + "Exit");
			return ("success");
		} catch (SwtException swtexp) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [next] method : - "
					+ swtexp.getMessage());
			SwtUtil.logException(swtexp, request, "");
			return ("fail");
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [next] method : - "
					+ exp.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "next", BalMaintenanceAction.class), request, "");

			return ("fail");
		} finally {
			balType = null;
			entityId = null;
			roleId = null;
			hostId = null;
			currency = null;
			balanceDate = null;
			balanceType = null;
			user = null;
			selectedDate = null;
		}
	}

	/**
	 * Method calls when balanceType dropdown is changed, to load the data for
	 * different balanceType
	 * 
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return ActionForward
	 * @throws SwtException
	 */
	public String displayListBalanceType() throws SwtException {
HttpServletRequest request = ServletActionContext.getRequest();
HttpServletResponse response = ServletActionContext.getResponse();


		/* Method's local variable and class instance declaration */
		String pageSizeAsString = null;
		int pageSize;
		int currentPage = 1;
		int startRowNumber;
		int endRowNumber;
		int maxPage;
		int totalCount;
		String currentFilter = null;
		String currentSort = null;
		String filterSortStatus = null;
		String entityId = null;
		String currency = null;
		String balanceType = null;
		String hostId = null;
		Collection<BalType> balmaintenanceDetails = null;
		ArrayList<SODPageSummary> pageSummaryList = null;
		String roleId = null;
		// DynaValidatorForm dyForm = null;
		BalMaintenance balmaintenance = null;
		BalType balType = null;
		Date selectedDate = null;
		ArrayList<BalType> arrList = null;
		try {
			log.debug(this.getClass().getName()
					+ " - [displayListBalanceType] - " + "Entry");
			/* Method's local variable declaration */

			balmaintenance = (BalMaintenance) getBalmaintenance();
			balType = new BalType();
			/* Read entity Id from Balmaintenance bean */
			entityId = balmaintenance.getId().getEntityId();
			/*
			 * Read selected currency id from balmaintenance bean
			 */
			currency = balmaintenance.getCurrency();

			/*
			 * Read selected balance type from bal maintenance bean
			 */
			balanceType = balmaintenance.getBalanceType();
			/* Read the host id form SwtUtil */
			hostId = SwtUtil.getCurrentHostId();
			/*
			 * This method receives the access rights of the user according to
			 * their role to set the button status of the screen
			 */
			putEntityAccessInRequest(request, entityId);
			/*
			 * Set the value of replacebalanceDate as balanceDate, the replace
			 * balance date returns the string it is converted to the date
			 * format by SwtUtil.parseDateGeneral method
			 */
			balmaintenance.getId().setBalanceDate(
					SwtUtil.parseDateGeneral(balmaintenance
							.getReplacebalanceDate()));
			/* Read balanceDate in Date format */
			selectedDate = balmaintenance.getId().getBalanceDate();

			/*
			 * Read page size from swtcommon.properties
			 */
			pageSize=SwtUtil.getPageSizeFromProperty(SwtConstants.BALANCE_MAINTENANCE_PAGE_SIZE);
			String currentPageAsString = request.getParameter("currentPage");
			if (currentPageAsString != null && currentPageAsString.length() > 0) {
				currentPage = Integer.parseInt(currentPageAsString);
			} else {
				currentPage = 1;
			}

			startRowNumber = 0;
			endRowNumber = pageSize;
			maxPage = 0;
			totalCount = 0;
			endRowNumber = currentPage * pageSize;
			/* Get the start row number of each page */
			startRowNumber = endRowNumber - pageSize;

			/* Read selected Filter value from request */
			currentFilter = request.getParameter("selectedFilter");
			/* Read value of selectetdSort from the request */
			currentSort = request.getParameter("selectedSort");

			/*
			 * Condition checks if the current filter is null or its length less
			 * than or equal to zero
			 */
			if (currentFilter == null || currentFilter.length() <= 0) {
				/*
				 * Set current filter as All for all columns
				 */
				currentFilter = "All|All|All|All|All|All|All|All|All|All|All|All";
			}
			/*
			 * Condition to check current filter is null or lessthan or equal to
			 * zero
			 */
			if (currentSort == null || currentSort.length() <= 0) {
				/*
				 * Set currenct sort for first column
				 */
				currentSort = "1|false";
			}
			filterSortStatus = currentFilter + "#" + currentSort;
			/*
			 * Passing the values to the accountDetail method of
			 * BalMaintenaceManager
			 */
			// get the roleId
			roleId = ((CommonDataManager) request.getSession().getAttribute(
					SwtConstants.CDM_BEAN)).getUser().getRoleId();
			balmaintenanceDetails = balmaintMgr.accountDetail(hostId, entityId,
					balanceType, currency, selectedDate, startRowNumber,
					endRowNumber, filterSortStatus, roleId);
			/* To iterate the collection return from the Manager class */
			Iterator<BalType> itr = balmaintenanceDetails.iterator();
			while (itr.hasNext()) {
				balType = (BalType) itr.next();
				/* Get the total row count from the collection */
				totalCount = balType.getRowCount();
				break;
			}
			/* Returns the integer value of maximum number of pages. */
			maxPage = setMaxPageAttribute(totalCount, pageSize);
			/* Instatiating pageSummaryList to array list */
			pageSummaryList = new ArrayList<SODPageSummary>();
			/* Set page summary list to display in screen */
			setPageSummaryList(entityId, currentPage, maxPage, totalCount,
					pageSummaryList);

			// Hide pagination if no of records are "0" or records can be
			// displayed in one page.
			if (maxPage <= 1) {
				request.setAttribute("hidePagination", true);
			}

			boolean isNext = false;
			if (maxPage > 1) {
				isNext = true;
			}
			request.setAttribute("currentPage", "" + currentPage);
			request.setAttribute("pageSize", "" + pageSize);
			request.setAttribute("maxPage", maxPage + "");
			request.setAttribute("prevEnabled", "false");
			request.setAttribute("nextEnabled", "" + isNext);
			request
					.setAttribute("balanceType", balmaintenance
							.getBalanceType());
			request.setAttribute("pageSummaryList", pageSummaryList);
			request.setAttribute("totalCount", totalCount);
			request.setAttribute("filterSortStatus", filterSortStatus);
			request.setAttribute("selectedFilter", currentFilter);
			request.setAttribute("selectedSort", currentSort);

			/*
			 * This method set currencyList of CurrencyVO in request attribute
			 */
			putCurrencyDetailListWithAllInReq(request, entityId);
			/*
			 * Set the entity list accesed to user in request attribute
			 */
			putEntityListInReq(request);
			/*
			 * Set collection of Balance type in request attribute
			 */

			putBalanceListInReq(request, entityId);

			/* To iterate the collection return from the Manager class */
			Iterator<BalType> itr1 = balmaintenanceDetails.iterator();
			arrList = new ArrayList<BalType>();
			while (itr1.hasNext()) {
				balType = (BalType) itr1.next();
				/* Returns the Username */
				String user = balType.getUser();

				/* Condition to check the user is null */
				if (user.equalsIgnoreCase(" ")) {
					/*
					 * Set the values of startBalanceAsString,inputDateAsString
					 * inputTimeAsString and balanceSource as null
					 */
					balType.setStartBalanceAsString(" ");
					balType.setInputDateAsString(" ");
					balType.setInputTimeAsString(" ");
					balType.setBalanceSource(" ");
				} else {

					/* Condition to check forecast value is null */
					if (balType.getForecastSOD() == null) {

						/*
						 * If the condition is true it set null value for
						 * StartBalanceAsString
						 */
						balType.setStartBalanceAsString(" ");
					} else {
						/*
						 * Set Start balance as string by getting the formatted
						 * currency of swtUtil formatCurrency which has
						 * BalCurrencyCode and StartBalance as parameters
						 */
						balType.setStartBalanceAsString(SwtUtil.formatCurrency(
								balType.getBalCurrencyCode(), balType
										.getStartBalance()));
					}
					/*
					 * Set the input date as string by formating the date in a
					 * format by swtutil fromatdate method which has inputDate
					 * as date parameter and the date format value is return
					 * from the current system formats of SwtUtil
					 */
					balType.setInputDateAsString(SwtUtil.formatDate(balType
							.getInputDate(), SwtUtil.getCurrentSystemFormats(
							request.getSession()).getDateFormatValue()));
					/* Set the value of input Time as InputTimeAsString */
					balType.setInputTimeAsString(balType.getInputTime());
					if (balType.getExternalSOD() == null) {
						balType.setExternalSODAsString(" ");
					} else {
						if (balType.getSuppliedExternalBalance() != null) {
							balType.setExternalSODAsString(SwtUtil
									.formatCurrency(balType
											.getBalCurrencyCode(), balType
											.getSuppliedExternalBalance()));
						} else {

							balType.setExternalSODAsString(SwtUtil
									.formatCurrency(balType
											.getBalCurrencyCode(), balType
											.getExternalSOD()));
						}
					}

					/*
					 * Getting forecast SOD value then convert into currency
					 * format and setting value using bean class
					 */
					balType.setForecastSODAsString(SwtUtil.formatCurrency(
							balType.getBalCurrencyCode(), balType
									.getForecastSOD()));
					/*
					 * Getting forecast exernal value then convert into currency
					 * format and setting value using bean class
					 */
					balType.setExternalSODAsString(SwtUtil.formatCurrency(
							balType.getBalCurrencyCode(), balType
									.getExternalSOD()));
					/*
					 * Getting external SOD value then convert into currency
					 * format and setting value using bean class
					 */

					/*
					 * Condition to check external SOD has value and less than
					 * 0.0
					 */

					if (balType.getExternalSOD() != null
							&& balType.getExternalSOD().doubleValue() < 0.0)
						/*
						 * Setting negative value for external SOD using bean
						 * class
						 */
						balType.setExternalSODNegative(true);

					/*
					 * Condition to check forecast SOD has value and less than
					 * 0.0
					 */

					if (balType.getForecastSOD() != null
							&& balType.getForecastSOD().doubleValue() < 0.0)
						/*
						 * Setting negative value for forecast SOD using bean
						 * class
						 */
						balType.setForecastSODNegative(true);
				}
				arrList.add(balType);
			}
			request
					.setAttribute("balmaintenanceDetails",
							balmaintenanceDetails);
			setBalmaintenance(balmaintenance);
			/* This method is to put the SystemDate in request attribute. */
			putSystemDateInRequest(request);
			log.debug(this.getClass().getName()
					+ " - [displayListBalanceType] - " + "Exit");
			return ("success");
		} catch (SwtException swtexp) {
			log
					.error(this.getClass().getName()
							+ " - Exception Catched in [displayListBalanceType] method : - "
							+ swtexp.getMessage());
			SwtUtil.logException(swtexp, request, "");
			return ("fail");
		} catch (Exception exp) {
			log
					.error(this.getClass().getName()
							+ " - Exception Catched in [displayListBalanceType] method : - "
							+ exp.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "displayListBalanceType", BalMaintenanceAction.class),
					request, "");
			return ("fail");
		} finally {
			entityId = null;
			currency = null;
			hostId = null;
			roleId = null;
			balType = null;
		}
	}

	/**
	 * Collects the Accessible entity from swtUtil and set in request attribute
	 * 
	 * @param request
	 * @return None
	 * @throws SwtException
	 */
	private void putEntityListInReq(HttpServletRequest request)
			throws SwtException {
		log.debug(this.getClass().getName() + " - [putEntityListInReq] - "
				+ "Entry");
		/* Method's local variable and class instance declaration */
		HttpSession session;
		Collection coll;
		session = request.getSession();
		/* Set the list of accessed by the user to request attribute */
		coll = SwtUtil.getUserEntityAccessList(session);
		/* Collect the user entity access label value bean from SwtUtil */
		coll = SwtUtil.convertEntityAcessCollectionLVL(coll, session);
		request.setAttribute("entities", coll);
		log.debug(this.getClass().getName() + " - [putEntityListInReq] - "
				+ " Exit");

	}

	/**
	 * Method to set Reason code list in request attribute
	 * 
	 * @param request
	 * @throws SwtException
	 */

	private void putReasonCodeListInReq(HttpServletRequest request)
			throws SwtException {
		log.debug(this.getClass().getName() + " - [putReasonCodeListInReq] - "
				+ "Entry");
		/* Method's local variable declaration */
		String entityId;
		String hostId;
		ReasonMaintenanceManager reasonMaintenanceManager;
		Collection coll;
		ReasonMaintenance reasonMaintenance;
		Collection returnColl;
		LabelValueBean labelValueBeanObj = null;
		Iterator itr;

		/* Read entity from request */
		entityId = request.getParameter("entityCode");
		/* Read host Id from swt common properties */
		hostId = SwtUtil.getCurrentHostId();
		/* Get bean object of ReasonMaintenanceManager */
		reasonMaintenanceManager = (ReasonMaintenanceManager) SwtUtil
				.getBean("reasonMaintenanceManager");
		/* Get collection of reason maintenance details list from data base */
		coll = reasonMaintenanceManager.getReasonMaintenanceDetails(hostId,
				entityId);
		returnColl = new ArrayList();
		/* Condition to check collection is not null */
		if (coll != null) {
			itr = coll.iterator();
			labelValueBeanObj = new LabelValueBean(SwtConstants.EMPTY_STRING,
					SwtConstants.EMPTY_STRING);
			returnColl.add(labelValueBeanObj);
			/* Get reason code and reason description and set label value bean */
			while (itr.hasNext()) {
				reasonMaintenance = (ReasonMaintenance) itr.next();
				labelValueBeanObj = new LabelValueBean(reasonMaintenance
						.getDescription(), reasonMaintenance.getId()
						.getReasonCode());
				returnColl.add(labelValueBeanObj);
			}
		}
		request.setAttribute("reasondetails", returnColl);
		log.debug(this.getClass().getName() + "- [putReasonCodeListInReq] - "
				+ "Exit");
	}

	
	/**
	 * This method is used to get the book code list
	 * 
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return ActionForward
	 * @throws SwtException
	 */
	public String getReasonCodeList() throws SwtException {
HttpServletRequest request = ServletActionContext.getRequest();
HttpServletResponse response = ServletActionContext.getResponse();

		// Entity Id
		String entityId = null;
		// Response Text
		String responseText = "";
		// Collection BookList
		Collection collBookList = null;
		// Iterator
		Iterator iterator = null;
		String hostId;
		ReasonMaintenanceManager reasonMaintenanceManager;
		Collection coll;
		ReasonMaintenance reasonMaintenance;
		Collection returnColl;
		LabelValueBean labelValueBeanObj = null;
		Iterator itr;
		try {
			
			
			
		/* Read entity from request */
		entityId = request.getParameter("entityCode");
		/* Read host Id from swt common properties */
		hostId = SwtUtil.getCurrentHostId();
		/* Get bean object of ReasonMaintenanceManager */
		reasonMaintenanceManager = (ReasonMaintenanceManager) SwtUtil
				.getBean("reasonMaintenanceManager");
		/* Get collection of reason maintenance details list from data base */
		coll = reasonMaintenanceManager.getReasonMaintenanceDetails(hostId,
				entityId);
		returnColl = new ArrayList();
		/* Condition to check collection is not null */
		if (coll != null) {
			itr = coll.iterator();
			labelValueBeanObj = new LabelValueBean(SwtConstants.EMPTY_STRING,
					SwtConstants.EMPTY_STRING);
			returnColl.add(labelValueBeanObj);
			/* Get reason code and reason description and set label value bean */
			while (itr.hasNext()) {
				reasonMaintenance = (ReasonMaintenance) itr.next();
				labelValueBeanObj = new LabelValueBean(reasonMaintenance
						.getDescription(), reasonMaintenance.getId()
						.getReasonCode());
				returnColl.add(labelValueBeanObj);
			}
		}
		
		
	
			log.debug("Entering 'getBookCodeList' method ");
			// get the entityId from request
			entityId = request.getParameter("entityId");
			// get the collection of book list
			// Iterate the book code collection
			iterator = returnColl.iterator();
			while (iterator.hasNext()) {
				// get the label value bean
				LabelValueBean lBean = (LabelValueBean) iterator.next();
				// add the label and value
				responseText = responseText + lBean.getLabel() + "~~~"
						+ lBean.getValue() + "\n";
			}
			// write the responseText into response
			response.getWriter().print(responseText);
			log.debug("Exiting 'getBookCodeList' method");
		} catch (IOException ioException) {
			log
					.error("IOException Catch in AcctMaintenanceAction.'getBookCodeList' method : "
							+ ioException.getMessage());
			throw new SwtException(ioException.getMessage());
		} catch (SwtException swtexp) {
			log
					.error("SwtException Catch in AcctMaintenanceAction.'getBookCodeList' method : "
							+ swtexp.getMessage());
			SwtUtil.logException(swtexp, request, "");
		} catch (Exception exp) {
			log
					.error("Exception Catch in AcctMaintenanceAction.'getBookCodeList' method : "
							+ exp.getMessage());

		} finally {
			// nullify objects
			entityId = null;
			responseText = null;
			collBookList = null;
			iterator = null;
		}
		return null;
	}

	/**
	 * Method to set button status
	 * 
	 * @param req
	 * @param changeStatus
	 * @param deleteStatus
	 * @return
	 */
	private void setButtonStatus(HttpServletRequest req, String changeStatus,
			String deleteStatus) {
		log.debug(this.getClass().getName() + " - [setButtonStatus] - "
				+ "Entry");
		req.setAttribute(SwtConstants.CHG_BUT_STS, changeStatus);
		req.setAttribute("Reason", deleteStatus);
		log.debug(this.getClass().getName() + " - [setButtonStatus] - "
				+ "Exit");
	}

	/* Start : Method modified for mantis 1682 by Vivekanandan A */

	/**
	 * Method calls when click on change button , loads the change balance
	 * screen which display selected account Id's Supplied SOD Balances and Back
	 * Value Adjustments and enable user to update Working SOD balances
	 * 
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return ActionForward
	 * @throws SwtException
	 */
	public String change()
			throws SwtException {
HttpServletRequest request = ServletActionContext.getRequest();
HttpServletResponse response = ServletActionContext.getResponse();


		/* Method's local variable declaration */
		// String variable to hold selected Account Id
		String selectedAccId = null;
		// String variable to hold balance type
		String balType = null;
		// String variable to hold selected currency code
		String selectedCurrencyCode = null;
		// String variable to hold entity id
		String entityId = null;
		// String variable to hold hostId
		String hostId = null;
		// Balance maintenance bean to hold balance values
		BalMaintenance balMaintenance = null;
		// DynaValidatorForm to get form bean values
		// DynaValidatorForm dyForm = null;
		// String variable to hold userName
		String userName = null;
		String dateFormat=null;
		try {
			dateFormat= SwtUtil.getCurrentDateFormat(request.getSession());
			log.debug(this.getClass().getName() + " - [change] - Entry");

			// get balMaintenance bean values
			balMaintenance = (BalMaintenance) getBalmaintenance();
			// Get selected account id
			selectedAccId = (String) getSelectedBalTypeId();
			// get selected balance type
			balType = request.getParameter("selectedBalType");
			// get selected currency code
			selectedCurrencyCode = request.getParameter("selectedCurrencyCode");

			// Read entity id from the request
			entityId = request.getParameter("entityCode");
			// Set entity id using bean class
			balMaintenance.getId().setEntityId(entityId);
			// Set selected date in the value of balanceDate as date instance
			balMaintenance.getId().setBalanceDate(
					SwtUtil.parseDateGeneral(request
							.getParameter("selectedDate")));

			// Set balance date as string in bean object
			balMaintenance.setBalancedateAsString(SwtUtil.formatDate(
					balMaintenance.getId().getBalanceDate(), SwtUtil
							.getCurrentSystemFormats(request.getSession())
							.getDateFormatValue()));
			// Reads the hostId from SwtUtil
			hostId = SwtUtil.getCurrentHostId();

			/* Reading the user name from the request */
			userName = request.getParameter("userName");
			// Set the value balType to the balance type of balmaintenance
			balMaintenance.setBalanceType(balType);
			// Condition to check userName is null
			if (!SwtUtil.isEmptyOrNull(userName)) {

				// Get Balances for selected account from Manager layer
				balMaintenance = balmaintMgr.getEditableData(
						selectedCurrencyCode, hostId, entityId, selectedAccId,
						balMaintenance.getBalanceType(), balMaintenance.getId()
								.getBalanceDate(), SwtUtil
								.getCurrentSystemFormats(request.getSession()));
			}

			// Condition to check balType is null
			if (SwtUtil.isEmptyOrNull(balType)) {
				balType = "";

			} else {
				// set balType description from P_MISC PARAMS
				balType = miscparamDesc(balType, entityId);
			}

			// Set balType in request
			request.setAttribute("balType", balType);

			// Set SuppliedExternalBalance as String , formatted by its
			// corresponding currency's decimal format
			balMaintenance.setSuppliedExternalBalanceAsString(SwtUtil
					.formatCurrency(selectedCurrencyCode, balMaintenance
							.getSuppliedExternalBalance()));
			// Set BvForecastAdjust as String , formatted by its
			// corresponding currency's decimal format
			balMaintenance
					.setBvForecastAdjustAsString(SwtUtil.formatCurrency(
							selectedCurrencyCode, balMaintenance
									.getBvForecastAdjust()));
			// Set BvExternalAdjust as String , formatted by its
			// corresponding currency's decimal format
			balMaintenance
					.setBvExternalAdjustAsString(SwtUtil.formatCurrency(
							selectedCurrencyCode, balMaintenance
									.getBvExternalAdjust()));
			// Set CalculatedIntPredBalance as String , formatted by its
			// corresponding currency's decimal format
			balMaintenance.setCalculatedIntPredBalanceAsString(SwtUtil
					.formatCurrency(selectedCurrencyCode, balMaintenance
							.getCalculatedIntPredBalance()));
			// Set CalculatedExtPredBalance as String , formatted by its
			// corresponding currency's decimal format
			balMaintenance.setCalculatedExtPredBalanceAsString(SwtUtil
					.formatCurrency(selectedCurrencyCode, balMaintenance
							.getCalculatedExtPredBalance()));
			// Set SuppliedInternalBalance as String , formatted by its
			// corresponding currency's decimal format
			balMaintenance.setSuppliedInternalBalanceAsString(SwtUtil
					.formatCurrency(selectedCurrencyCode, balMaintenance
							.getSuppliedInternalBalance()));

			// Set WorkingExternalSOD as String , formatted by its
			// corresponding currency's decimal format
			balMaintenance.setWorkingExternalSODAsString(SwtUtil
					.formatCurrency(selectedCurrencyCode, balMaintenance
							.getWorkingExternalSOD()));

			// Set WorkingForecastSOD as String , formatted by its
			// corresponding currency's decimal format
			balMaintenance.setWorkingForecastSODAsString(SwtUtil
					.formatCurrency(selectedCurrencyCode, balMaintenance
							.getWorkingForecastSOD()));

			// Condition to check working external SOD type is null
			if (SwtUtil.isEmptyOrNull(balMaintenance
					.getWorkingExternalSODType()))

				// Setting working external SOD type as empty
				balMaintenance
						.setWorkingExternalSODTypeAsString(SwtConstants.EMPTY_STRING);
			else
				// Getting and Setting ExternalSODTypeAsString using bean class
				balMaintenance
						.setWorkingExternalSODTypeAsString(sodTypeAsString(balMaintenance
								.getWorkingExternalSODType()));

			// Condition to check ForecastSODType is null
			if (SwtUtil.isEmptyOrNull(balMaintenance
					.getWorkingForecastSODType()))
				// Setting ForecastSODTypeAsString as empty using constant
				balMaintenance
						.setWorkingForecastSODTypeAsString(SwtConstants.EMPTY_STRING);
			else
				// Getting and Setting ForecastSODTypeAsString using bean class
				balMaintenance
						.setWorkingForecastSODTypeAsString(sodTypeAsString(balMaintenance
								.getWorkingForecastSODType()));

			// Condition to check Forecast SOD and external SOD is null
			if (balMaintenance.getWorkingForecastSOD() == null
					&& balMaintenance.getWorkingExternalSOD() == null) {
				// Set Y as sodBalance in request attribute
				request.setAttribute("sodBalance", "Y");
			} else {
				// Set working external SOD as Starting balance
				request.setAttribute("sodBalance", balMaintenance
						.getWorkingExternalSOD());
			}

			// Set the entity id to the balMaintenance bean
			balMaintenance.getId().setEntityId(entityId);
			// Set the account id to the balMaintenance bean
			balMaintenance.getId().setBalanceTypeId(selectedAccId);

			//Mantis 6275
			if (balMaintenance.getInternalBalanceEodDate()!=null) {
			balMaintenance.setInternalBalanceEodDateAsString(SwtUtil.formatDate(balMaintenance
					.getInternalBalanceEodDate(), dateFormat));
			}else {
			balMaintenance.setInternalBalanceEodDateAsString("");
			}
			
			if (balMaintenance.getExternalBalanceEodDate()!=null) {
			balMaintenance.setExternalBalanceEodDateAsString(SwtUtil.formatDate(balMaintenance
					.getExternalBalanceEodDate(), dateFormat));
			}else {
			balMaintenance.setExternalBalanceEodDateAsString("");
			}
			
			
			// Read the entity name from the request
			request.setAttribute("entityName", request
					.getParameter("EntityName"));

			setBalmaintenance(balMaintenance);
			request.setAttribute("selectedCurrency", request
					.getParameter("selectedCurrency"));
			request.setAttribute("methodName", "change");
			request.setAttribute("name", request.getParameter("selectedName"));
			request.setAttribute("screenFieldsStatus", "true");
			request.setAttribute("selectedCurrencyCode", selectedCurrencyCode);

			// Put the entity access list and label value bean of the entity to
			// the request
			putEntityListInReq(request);

			// Set the currencyList of CurrencyVO in the request attribute
			putCurrencyDetailListWithAllInReq(request, entityId);
			// put balance type list in to request
			putBalanceListInReq(request, entityId);

			// Set values in request
			request.setAttribute("entityCode", request
					.getParameter("entityCode"));
			request.setAttribute("reasonCode", balMaintenance.getReasonCode());

			request.setAttribute("suppliedExternalBalanceSod", request
					.getParameter("suppliedExternalBalanceSod"));

			request.setAttribute("reasonDesc", request
					.getParameter("reasonDesc"));
			request.setAttribute("userNotes", balMaintenance.getUserNotes());
			request.setAttribute("currentPage", request
					.getParameter("currentPage"));
			request.setAttribute("currencyCode", request
					.getParameter("selectedCurrency"));
			request.setAttribute("balanceType", request
					.getParameter("selectedBalType"));
			request.setAttribute("filterSortStatus", request
					.getParameter("currentFilter")
					+ "#" + request.getParameter("currentSort"));
			request.setAttribute("selectedFilter", request
					.getParameter("currentFilter"));
			request.setAttribute("selectedSort", request
					.getParameter("currentSort"));

			return ("change");
		} catch (SwtException swtexp) {

			log.error(this.getClass().getName()
					+ " - Exception Catched in [change] method : - "
					+ swtexp.getMessage());
			SwtUtil.logException(swtexp, request, "");

			return ("fail");
		} catch (Exception exp) {

			log.error(this.getClass().getName()
					+ " - Exception Catched in [change] method : - "
					+ exp.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "change", BalMaintenanceAction.class), request, "");

			return ("fail");
		} finally {
			userName = null;
			hostId = null;
			log.debug(this.getClass().getName() + " - [change] - Exit");
		}
	}

	/**
	 * Method called when view button is clicked, Loads the view screen .
	 * Returns the change method and set the request attribute value as 'view'
	 * for selectedMode
	 * 
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return ActionForward
	 * @throws SwtException
	 */

	public String view()
			throws SwtException {
HttpServletRequest request = ServletActionContext.getRequest();
HttpServletResponse response = ServletActionContext.getResponse();

		try {
			log.debug(this.getClass().getName() + " - [view] - " + "Entry");

			request.setAttribute("selectedMode", "view");

			log.debug(this.getClass().getName() + " - [view] - " + "Exit");
			return change();
		} catch (SwtException swtexp) {

			log.error(this.getClass().getName()
					+ " - Exception Catched in [view] method : - "
					+ swtexp.getMessage());
			SwtUtil.logException(swtexp, request, "");

			return ("fail");
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [view] method : - "
					+ exp.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "view", BalMaintenanceAction.class), request, "");

			return ("fail");
		}
	}

	/**
	 * Method to display Edit reason Screen
	 * 
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return ActionForward
	 * @throws SwtException
	 * @throws ParseException
	 */
	public String addNotes()
			throws SwtException, ParseException {

		/* Method's local variable declaration */
		// DynaValidatorForm dyForm = null;
		BalMaintenance balmaintenance = null;
		String hostId = null;
		String entityId = null;
		String selectedBalTypeId = null;
		String balanceType = null;
		String sodBalanceAsString = null;
		String selectedDate = null;
		String reasonCode = null;
		String reasonName = null;
		String userNotes = null;
		Date date = null;
		HttpServletRequest request = ServletActionContext.getRequest();
		HttpServletResponse response = ServletActionContext.getResponse();
		try {
			log.debug(this.getClass().getName() + " - [addNotes] - " + "Entry");
			/* put reasoncode List req */
			putReasonCodeListInReq(request);
			balmaintenance = (BalMaintenance) getBalmaintenance();
			/* Read hostId fom swt common properties */
			hostId = SwtUtil.getCurrentHostId();
			/* Read entityId from request */
			entityId = request.getParameter("entityCode");
			/* Read selected BaltypeId from request */
			selectedBalTypeId = request.getParameter("selectedBalTypeId");
			/* Read selected balType from request */
			balanceType = request.getParameter("selectedBalType");
			/* Read selected Start Balance */
			sodBalanceAsString = (String) request
					.getParameter("selectedSodBalance");
			/* Read selected date from request */
			selectedDate = request.getParameter("selectedDate");
			/* Read selected reasonCode from request */
			reasonCode = request.getParameter("reasonCode");
			/* Read selected currencyCode from request */
			String currencyCode = request.getParameter("currencyCode");	 
			/* parsing date from string to Date */
	        date = SwtUtil.parseDate(selectedDate,SwtUtil.getCurrentDateFormat(request.getSession()));
	        /* Read CurrentSystemFormats */
	        SystemFormats systemFormats = SwtUtil.getCurrentSystemFormats(request.getSession());
			/* Get the editable data from data base */
			balmaintenance = balmaintMgr.getEditableData(currencyCode, hostId, entityId, selectedBalTypeId, balanceType,
					date, systemFormats);
			
			if(!SwtUtil.isEmptyOrNull(reasonCode)){
			
				ReasonMaintenanceManager reasonMaintenanceManager = (ReasonMaintenanceManager) SwtUtil
						.getBean("reasonMaintenanceManager");
				/* Get collection of reason maintenance details list from data base */
				Collection coll = reasonMaintenanceManager.getReasonMaintenanceDetails(hostId,
						entityId);
				
				if (coll != null) {
					Iterator itr = coll.iterator();
					ReasonMaintenance reasonMaintenance = null;
					/* Get reason code and reason description and set label value bean */
					while (itr.hasNext()) {
						reasonMaintenance = (ReasonMaintenance) itr.next();
						if(reasonMaintenance.getId().getReasonCode().equals(reasonCode)) {
							reasonName = reasonMaintenance.getDescription();
						}
					}
				}
			}
			
			
			/* Read selected userNotes from balmaintenance */
			userNotes = balmaintenance.getUserNotes();
			/* End: Variable initialization from request */
			
			/* Start: To set values in balmaintenance bean */
			balmaintenance.getId().setBalanceDateAsString(selectedDate);
			balmaintenance.getId().setHostId(hostId);
			balmaintenance.getId().setEntityId(entityId);
			balmaintenance.getId().setBalanceDate(date);
			/* End : To set values in balmaintenance bean */
			/* Start: set to request */
			request.setAttribute("currentPage", (String) request
					.getParameter("currentPage"));
			request.setAttribute("entityId", entityId);
			request.setAttribute("reasonCode", reasonCode);
			request.setAttribute("reasonName", reasonName);
			request.setAttribute("userNotes", userNotes);
			request.setAttribute("selectedBalTypeId", selectedBalTypeId);
			request.setAttribute("balanceType", balanceType);
			request.setAttribute("selectedName", (String) request
					.getParameter("selectedName"));
			request.setAttribute("currencyCode", (String) request
					.getParameter("selectedCurrency"));
			request.setAttribute("filterSortStatus", (String) request
					.getParameter("currentFilter")
					+ "#" + (String) request.getParameter("currentSort"));
			request.setAttribute("selectedFilter", (String) request
					.getParameter("currentFilter"));
			request.setAttribute("selectedSort", (String) request
					.getParameter("currentSort"));
			request.setAttribute("selectedDate", (String) request
					.getParameter("selectedDate"));
			request.setAttribute("maxPage", (String) request
					.getParameter("maxPage"));
			request.setAttribute("userName", (String) request
					.getParameter("userName"));
			request.setAttribute("access", (String) request
					.getParameter("access"));
			request.setAttribute("sodBalanceAsString", sodBalanceAsString);
			/* End: set to request */
			balmaintenance.setReasonCode(reasonCode);
			setBalmaintenance(balmaintenance);
			log.debug(this.getClass().getName() + " - [addNotes] - " + "Exit");
			return ("addNotes");
		} catch (SwtException swtexp) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [addNotes] method : - "
					+ swtexp.getMessage());
			SwtUtil.logException(swtexp, request, "");
			return ("fail");
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [addNotes] method : - "
					+ exp.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "addNotes", BalMaintenanceAction.class), request, "");
			return ("fail");
		}
	}

	/**
	 * Method to save reason notes in data base
	 * 
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return ActionForward
	 * @throws SwtException
	 */

	public String saveNotes()
			throws SwtException {
HttpServletRequest request = ServletActionContext.getRequest();
HttpServletResponse response = ServletActionContext.getResponse();

		log.debug(this.getClass().getName() + " - [saveNotes] - " + "Entry ");
		/* Start: Method's local variable declaration */
		BalMaintenance balmaintenance;
		BalType balanceType;
		String hostId;
		String entityId;
		String balanceTypeId;
		String reasonCode;
		String reasonName = null;
		String reasonNotes;
		String balType;
		String selectedBalTypeId;
		String selectedName;
		String replacebalanceDate;
		String userId;
		String sodBalance;
		String flag;
		String userName;
		Date replaceDate;
		String currency;
		/* End: Method's local variable declaration */

		putReasonCodeListInReq(request);
		balmaintenance = (BalMaintenance) getBalmaintenance();

		/* Start: Values from request */
		balanceType = (BalType) getBalType();
		/* Read hostId from swt common properties */
		hostId = SwtUtil.getCurrentHostId();
		/* Read entityId from request */
		entityId = request.getParameter("entityCode");
		/* Read balanceTypeId from request */
		balanceTypeId = request.getParameter("selectedBalTypeId");
		/* Read reasonCode from request */
		reasonCode = request.getParameter("reasonCode");
		/* Read reasonNotes from request */
		reasonNotes = ((String) request.getParameter("userNotes"));
		/* Read selectedBalType from request */
		balType = request.getParameter("selectedBalType");

		if (balType != null) {
			if (balType.trim().length() > 1) {
				balType = miscparamParVal(balType, entityId);
			}
		} else {
			balType = "";
		}

		/* Read selected BalTypeId from request */
		selectedBalTypeId = request.getParameter("selectedBalTypeId");
		/* Read selected selectedName from request */
		selectedName = request.getParameter("selectedName");
		/* Read selected date from request */
		replacebalanceDate = request.getParameter("selectedDate");

		/* Get User Id from Swtutil */
		userId = SwtUtil.getCurrentUserId(request.getSession());

		/* Start: To set values in balmaintenance bean */
		balmaintenance.setBalanceType(balType); // to set balance type
		balmaintenance.getId().setHostId(hostId); // to set host id
		balmaintenance.getId().setBalanceDate(
				SwtUtil.parseDateGeneral(replacebalanceDate)); // to set
		// balance date
		balmaintenance.setUpdateUser(userId); // to set user i
		balmaintenance.getId().setEntityId(entityId); // to set entity id
		balmaintenance.getId().setBalanceTypeId(selectedBalTypeId); // to set
		// selected
		// balance
		// type id
		balmaintenance.setReasonCode((String) request
				.getParameter("reasonCode")); // to set reason code from
		// request
		balmaintenance.setReasonDesc((String) request
				.getParameter("reasonNotes")); // to set reason description
		// from request
		balmaintenance.getId().setBalanceDate(
				SwtUtil.parseDateGeneral(replacebalanceDate)); // to set
		// balance date

		/* Read the selected SOD balances from the request */
		sodBalance = (String) request.getParameter("sodBalanceAsString");
		request.setAttribute("sodBalance", sodBalance);

		/*
		 * Calculation of flag value to Set start balance for cash account,
		 * currency and custodian account
		 */
		flag = "";
		userName = request.getParameter("userName");
		replaceDate = SwtUtil.parseDateGeneral(replacebalanceDate);
		if (balType.equalsIgnoreCase("A") || balType.equalsIgnoreCase("D")
				|| balType.equalsIgnoreCase("C")) {
			if (!userName.equalsIgnoreCase("1")) {
				flag = "1";
			}
		}
		if (balType.equalsIgnoreCase("P") || balType.equalsIgnoreCase("U")) {
			if (!userName.equalsIgnoreCase("1")) {
				flag = "2";
			}
		}

		/* Get the editable data from data base */
		balmaintenance = balmaintMgr.getEditableData(request
				.getParameter("currencyCode"), hostId, entityId, balanceTypeId,
				balmaintenance.getBalanceType(), replaceDate, SwtUtil
						.getCurrentSystemFormats(request.getSession()));

		/* to set reason code to balmaintenance bean */
		balmaintenance.setReasonCode(request.getParameter("reasonCode"));
		/* to set user notes to balmaintenance bean */
		balmaintenance.setUserNotes(request.getParameter("userNotes"));
		/* to set update date balmaintenance bean */
		if (balmaintenance.getUpdateDate() == null) {
			balmaintenance.setUpdateDate(SwtUtil.getSystemDatewithTime());
			balmaintenance.setUpdateUser(SwtUtil.getCurrentUserId(request
					.getSession()));
		}
		try {
			/* Set Accesible entity in request attribute */
			putEntityListInReq(request);
			/* Set BalaceType in request attribute */

			putBalanceListInReq(request, entityId);

			/* Set currency detail in request */
			putCurrencyDetailListWithAllInReq(request, entityId);
			setBalmaintenance(balmaintenance);
			setBalType(balanceType);
			currency = request.getParameter("selectedCurrency");

			/* Save Reason code for selected Identifier in data base */

			balmaintMgr.updateBalanceDetail(balmaintenance, sodBalance, hostId,
					entityId, balType, balmaintenance.getId().getBalanceDate(),
					SwtUtil.getCurrentSystemFormats(request.getSession()));

			
			
			if(!SwtUtil.isEmptyOrNull(reasonCode)){
				
				ReasonMaintenanceManager reasonMaintenanceManager = (ReasonMaintenanceManager) SwtUtil
						.getBean("reasonMaintenanceManager");
				/* Get collection of reason maintenance details list from data base */
				Collection coll = reasonMaintenanceManager.getReasonMaintenanceDetails(hostId,
						entityId);
				
				if (coll != null) {
					Iterator itr = coll.iterator();
					ReasonMaintenance reasonMaintenance = null;
					/* Get reason code and reason description and set label value bean */
					while (itr.hasNext()) {
						reasonMaintenance = (ReasonMaintenance) itr.next();
						if(reasonMaintenance.getId().getReasonCode().equals(reasonCode)) {
							reasonName = reasonMaintenance.getDescription();
						}
					}
				}
			}
			
			
			
			/* Start:Set values to request */
			request.setAttribute("balanceType", balType);
			request.setAttribute("selectedName", selectedName);
			request.setAttribute("selectedBalTypeId", selectedBalTypeId);
			request.setAttribute("reasonCode", reasonCode);
			request.setAttribute("reasonName", reasonName);
			
			request.setAttribute("reasonNotes", reasonNotes);
			request.setAttribute("parentFormRefresh", "yes");
			request.setAttribute("selectedCurrency", currency); // Refresh
			// Method

			request.setAttribute("sodBalance", sodBalance); // Refresh Method
			request.setAttribute("screenFieldsStatus", "true");
			request.setAttribute("selectedDate", replacebalanceDate);
			request.setAttribute("entityId", entityId);
			request.setAttribute("maxPage", request.getParameter("maxPage"));
			request.setAttribute("currentPage", (String) request
					.getParameter("currentPage"));
			request.setAttribute("currencyCode", (String) request
					.getParameter("selectedCurrency"));
			request.setAttribute("filterSortStatus", (String) request
					.getParameter("currentFilter")
					+ "#" + (String) request.getParameter("currentSort"));
			request.setAttribute("selectedFilter", (String) request
					.getParameter("currentFilter"));
			request.setAttribute("selectedSort", (String) request
					.getParameter("currentSort"));
			/* End:Set values to request */
			log.debug(this.getClass().getName() + "- [saveNotes] - Exiting ");
			return ("addNotes");
		} catch (SwtException swtexp) {
			log.error(this.getClass().getName()
					+ " - SwtException Catched in [saveNotes] method : - "
					+ swtexp.getMessage());
			setButtonStatus(request, SwtConstants.STR_FALSE,
					SwtConstants.STR_FALSE);
			String errorCode = swtexp.getErrorCode();
			if (errorCode.equals("errors.DataIntegrityViolationExceptioninAdd")) {
				;
			}
			swtexp.setErrorCode("errors.DataIntegrityViolationException");
			Collection balmaintlist = balmaintMgr.getBalanceList(entityId,
					hostId);

			request.setAttribute("balmaintenancelist", balmaintlist);
			request.setAttribute("parentFormRefresh", "yes");
			saveErrors(request, SwtUtil.logException(swtexp, request, ""));
			return ("success");
		} catch (Exception exp) {

			log.error(this.getClass().getName()
					+ " - Exception Catched in [saveNotes] method : - "
					+ exp.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "saveNotes", BalMaintenanceAction.class), request, "");

			return ("fail");
		}
	}

	/**
	 * Save the balance given in the change screen
	 * 
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return ActionForward
	 * @throws SwtException
	 */
	public String save()
			throws SwtException {
HttpServletRequest request = ServletActionContext.getRequest();
HttpServletResponse response = ServletActionContext.getResponse();

		log.debug(this.getClass().getName() + " - [save] - " + "Entry");

		// DynaValidatorForm /* Method's local variable and class instance declaration */
		String hostId;
		String entityId;
		String balanceType;
		String selectedBalTypeId;
		String selectedName;
		String replacebalanceDate;
		String userId;
		BalMaintenance balmaintenance;
		BalType balType;
		SystemFormats sysformat;
		String tempSOD1;
		String tempSOD2;
		String sodBalance;
		String currency;
		BigDecimal workingForecastSOD = null;
		String workingForecastSODType = null;
		BigDecimal workingExternalSOD = null;
		String workingExternalSODType = null;
		Double bvForecastAdjust = null;
		Long bvForecastAdjustLastId = null;
		Double bvExternalAdjust = null;
		Long bvExternalAdjustLastId = null;
		Double calculatedIntPredBalance = null;
		Double calculatedExtPredBalance = null;
		Double suppliedInternalBalance = null;
		Double suppliedExternalBalance = null;
		
		Date internalBalanceEodDate = null;
		Date externalBalanceEodDate = null;
		
		Double intialInternalBalance = null;
		Double initialExternalBalance = null;
		
		String internalBalanceSource = null;
		String externalBalanceSource = null;
		String selectedInterBalEodDate= null;
		String selectedExtBalEodDate= null;
		String externalSOD = null;
		tempSOD1 = null;
		tempSOD2 = null;
		String ccyPattern = null;
		Double oldWorkingForecast = null;
		Double oldWorkingExternal = null;
		/* Get the form bean of BalType */
		balType = (BalType) getBalType();
		/* Get the form bean of balMaintenance */
		balmaintenance = (BalMaintenance) getBalmaintenance();
		/* Read the current currency format from the sesssion */
		ccyPattern = SwtUtil.getCurrentCurrencyFormat(request.getSession());
		selectedInterBalEodDate= request.getParameter("selectedInterBalEodDate"); 
		selectedExtBalEodDate= request.getParameter("selectedExtBalEodDate");
		/* Read the workingForecastSOD from request */
		if (request.getParameter("selectedWorkingForecastSOD") != null
				&& request.getParameter("selectedWorkingForecastSOD").trim()
						.length() > 0) {

			tempSOD1 = request.getParameter("selectedWorkingForecastSOD");
			/*
			 * Read forecast value by convert into currency and current currency
			 * format
			 */
			if (!SwtUtil.isEmptyOrNull(tempSOD1)) {
				workingForecastSOD = new BigDecimal(SwtUtil
						.getFormattedCurrencyBig(tempSOD1, ccyPattern));
			} else
				workingForecastSOD = null;
		}

		/* Read the old workingForecastSOD from request */
		if (!request.getParameter("oldWorkingForecast").trim().equals(""))
			oldWorkingForecast = Double.parseDouble(SwtUtil
					.getFormattedCurrencyBig(request
							.getParameter("oldWorkingForecast"), ccyPattern));

		/* Read the workingForecastSOD type from request */
		if (!request.getParameter("selectedWorkingForecastSODType").trim()
				.equals(""))
			workingForecastSODType = request
					.getParameter("selectedWorkingForecastSODType");
		/* Read the workingForecast External SOD from request */
		if (request.getParameter("selectedWorkingExternalSOD") != null
				&& request.getParameter("selectedWorkingExternalSOD").trim()
						.length() > 0) {

			tempSOD2 = request.getParameter("selectedWorkingExternalSOD")
					.trim();

			if (!SwtUtil.isEmptyOrNull(tempSOD2)) {
				/*
				 * Read external value by convert into currency and current
				 * currency format
				 */
				workingExternalSOD = new BigDecimal(SwtUtil
						.getFormattedCurrencyBig(tempSOD2, ccyPattern));
			}

		}

		/* Read the old workingForecastSOD from request */
		if (!request.getParameter("oldWorkingExternal").trim().equals(""))
			oldWorkingExternal = Double.parseDouble(SwtUtil
					.getFormattedCurrencyBig(request
							.getParameter("oldWorkingExternal"), ccyPattern));
		/* Read the workingForecast External SOD type from request */
		if (!request.getParameter("selectedWorkingExternalSODType").trim()
				.equals(""))
			workingExternalSODType = request
					.getParameter("selectedWorkingExternalSODType");
		/* Read the bv forecast Adjust SOD from request */
		if (!request.getParameter("selectedBvForecastAdjust").trim().equals(""))
			bvForecastAdjust = Double.parseDouble(SwtUtil
					.getFormattedCurrencyBig(request
							.getParameter("selectedBvForecastAdjust"),
							ccyPattern));
		/* Read the bvForecastAdjust last id from request */
		if (!request.getParameter("selectedBvForecastAdjustLastId").trim()
				.equals(""))
			bvForecastAdjustLastId = Long.parseLong(request.getParameter(
					"selectedBvForecastAdjustLastId").replace(",", "").trim());
		/* Read bv external Adjust SOD from request */
		if (!request.getParameter("selectedBvExternalAdjust").trim().equals(""))
			bvExternalAdjust = Double.parseDouble(SwtUtil
					.getFormattedCurrencyBig(request
							.getParameter("selectedBvExternalAdjust"),
							ccyPattern));
		/* Read bv external Adjust last id from request */
		if (!request.getParameter("selectedBvExternalAdjustLastId").trim()
				.equals(""))
			bvExternalAdjustLastId = Long.parseLong(request.getParameter(
					"selectedBvExternalAdjustLastId").replace(",", "").trim());
		/* Read calaculated predicted balance from request */
		if (!request.getParameter("selectedCalculatedIntPredBalance").trim()
				.equals(""))
			calculatedIntPredBalance = Double.parseDouble(SwtUtil
					.getFormattedCurrencyBig(request
							.getParameter("selectedCalculatedIntPredBalance"),
							ccyPattern));
		/* Read calaculated external balance from request */
		if (!request.getParameter("selectedCalculatedExtPredBalance").trim()
				.equals(""))
			calculatedExtPredBalance = Double.parseDouble(SwtUtil
					.getFormattedCurrencyBig(request
							.getParameter("selectedCalculatedExtPredBalance"),
							ccyPattern));
		/* Read calaculated supplied internal balance from request */
		if (!request.getParameter("selectedSuppliedInternalBalance").trim()
				.equals(""))
			suppliedInternalBalance = Double.parseDouble(SwtUtil
					.getFormattedCurrencyBig(request
							.getParameter("selectedSuppliedInternalBalance"),
							ccyPattern));
		
		/* Read calaculated supplied internal balance from request */
		if (!request.getParameter("initialSuppliedExternalBalance").trim()
				.equals(""))
			initialExternalBalance = Double.parseDouble(SwtUtil
					.getFormattedCurrencyBig(request
							.getParameter("initialSuppliedExternalBalance"),
							ccyPattern));
		
		
		/* Read calaculated supplied internal balance from request */
		if (!request.getParameter("initialSuppliedInternalBalance").trim()
				.equals(""))
			intialInternalBalance = Double.parseDouble(SwtUtil
					.getFormattedCurrencyBig(request
							.getParameter("initialSuppliedInternalBalance"),
							ccyPattern));
		
		/* Read calaculated supplied external balance from request */
		if (!request.getParameter("selectedSuppliedExternalBalance").trim()
				.equals(""))
			suppliedExternalBalance = Double.parseDouble(SwtUtil
					.getFormattedCurrencyBig(request
							.getParameter("selectedSuppliedExternalBalance"),
							ccyPattern));

		/* Read internal balance EOD date from request */
		if(!SwtUtil.isEmptyOrNull(selectedInterBalEodDate)) {
		if (!selectedInterBalEodDate.trim()
				.equals(""))
		internalBalanceEodDate = SwtUtil.parseDateGeneral(selectedInterBalEodDate);	
		balmaintenance.setInternalBalanceEodDate(internalBalanceEodDate);
		}
		
		/* Read external balance EOD date from request */
		if(!SwtUtil.isEmptyOrNull(selectedExtBalEodDate)) {
		if (!selectedExtBalEodDate.trim()
				.equals(""))
		externalBalanceEodDate = SwtUtil.parseDateGeneral(selectedExtBalEodDate);
		balmaintenance.setExternalBalanceEodDate(externalBalanceEodDate);

		}

		/* Read internal balance source from request */
		if (!request.getParameter("selectedInternalBalanceSource").trim()
				.equals(""))
			internalBalanceSource = request
					.getParameter("selectedInternalBalanceSource");
		/* Read external balance source from request */
		if (!request.getParameter("selectedExternalBalanceSource").trim()
				.equals(""))
			externalBalanceSource = request
					.getParameter("selectedExternalBalanceSource");
		
		
		if(suppliedInternalBalance != intialInternalBalance) {
			internalBalanceSource = "Manual";
		}
		
		if(suppliedExternalBalance != initialExternalBalance) {
			externalBalanceSource = "Manual";
		}

		/* Setting Working ForecastSOD using bean class */
		balmaintenance.setWorkingForecastSOD(workingForecastSOD);
		/* Setting WorkingForecast SOD type using bean class */
		balmaintenance.setWorkingForecastSODType(workingForecastSODType);
		/* Setting Working External SOD using bean class */
		balmaintenance.setWorkingExternalSOD(workingExternalSOD);
		/* Setting Working External SOD type using bean class */
		balmaintenance.setWorkingExternalSODType(workingExternalSODType);
		/* Setting Bv Forecast Adjust using bean class */
		balmaintenance.setBvForecastAdjust(bvForecastAdjust);
		/* Setting Bv Forecast Adjust last id using bean class */
		balmaintenance.setBvForecastAdjustLastId(bvForecastAdjustLastId);
		/* Setting Bv External Adjust using bean class */
		balmaintenance.setBvExternalAdjust(bvExternalAdjust);
		/* Setting Bv External Adjust last id using bean class */
		balmaintenance.setBvExternalAdjustLastId(bvExternalAdjustLastId);
		/* Setting calculated internal balance using bean class */
		balmaintenance.setCalculatedIntPredBalance(calculatedIntPredBalance);
		/* Setting calculated external balance using bean class */
		balmaintenance.setCalculatedExtPredBalance(calculatedExtPredBalance);
		/* Setting supplied internal balance using bean class */
		balmaintenance.setSuppliedInternalBalance(suppliedInternalBalance);
		/* Setting supplied external balance using bean class */
		balmaintenance.setSuppliedExternalBalance(suppliedExternalBalance);
		/* Setting supplied internal balance source using bean class */
		balmaintenance.setInternalBalanceSource(internalBalanceSource);
		/* Setting supplied external balance source using bean class */
		balmaintenance.setExternalBalanceSource(externalBalanceSource);
		/* Setting working forecast SOD type as "Manual" */


		/* Condition to check working forecast value with old value */
		if (workingForecastSOD != null
				&& oldWorkingForecast != null
				&& (Double.parseDouble(workingForecastSOD.toString()) != oldWorkingForecast))
			/* Setting working forecast SOD type as "Manual" */
			balmaintenance.setWorkingForecastSODType("M");
		else if (workingForecastSOD != null && oldWorkingForecast == null)
			/* Setting working forecast SOD type as "Manual" */
			balmaintenance.setWorkingForecastSODType("M");
		/* Condition to check working forecast SOD is null */
		else if (workingForecastSOD == null)
			/* Set the working forecast SOD as empty using bean class */
			balmaintenance.setWorkingForecastSODType(SwtConstants.EMPTY_STRING);

		/* Condition to check working external value with old value */
		if (workingExternalSOD != null
				&& oldWorkingExternal != null
				&& (Double.parseDouble(workingExternalSOD.toString()) != oldWorkingExternal))
			/* Setting working external SOD type as "Manual" */
			balmaintenance.setWorkingExternalSODType("M");
		else if (workingExternalSOD != null && oldWorkingExternal == null)
			/* Setting working external SOD type as "Manual" */
			balmaintenance.setWorkingExternalSODType("M");
		/* Condition to check working external SOD is null */
		else if (workingExternalSOD == null)
			/* Set the working external SOD as empty using bean class */
			balmaintenance.setWorkingExternalSODType(SwtConstants.EMPTY_STRING);
		/* Reads the hostId from SwtUtil */
		hostId = SwtUtil.getCurrentHostId();

		/*
		 * Returns the entity from the form bean and store it in the string
		 * variable
		 */
		entityId = balmaintenance.getId().getEntityId(); // 2

		/* Read the selected balance type from the request */
		balanceType = request.getParameter("selectedBalType");

		request.setAttribute("balanceType", balanceType); // Refresh Method -
		// balanceType
		if (balanceType != null) {
			if (balanceType.trim().length() > 1) {
				balanceType = miscparamParVal(balanceType, request
						.getParameter("selectedEntity"));
			}
		} else {
			balanceType = "";
		}
		/* Set the balance type in the balmaintenance form bean */
		balmaintenance.setBalanceType(balanceType); // 3
		/* Read the value of selectedBaltypeid from request */
		selectedBalTypeId = request.getParameter("selectedBalTypeId");
		request.setAttribute("selectedBalTypeId", selectedBalTypeId);

		/* Read the value of selected name from the request */
		selectedName = request.getParameter("selectedName");
		request.setAttribute("selectedName", selectedName);

		/* Get the current system formats from the swt util */
		sysformat = SwtUtil.getCurrentSystemFormats(request.getSession());

		/* Set the host id for balmaintenance bean */
		balmaintenance.getId().setHostId(hostId);

		/* Read the value of selected Date from request */
		replacebalanceDate = request.getParameter("selectedDate");
		request.setAttribute("selectedDate", replacebalanceDate);

		/*
		 * Set the replaceBalance date as balance date in date format by swtUtil
		 * parsedate general
		 */
		balmaintenance.getId().setBalanceDate(
				SwtUtil.parseDateGeneral(replacebalanceDate));
		/*
		 * Get the current user id from swt util and stores it in a String
		 * variable
		 */
		userId = SwtUtil.getCurrentUserId(request.getSession());
		balmaintenance.setUpdateUser(userId);

		try {

			request.setAttribute("screenFieldsStatus", "true");

			/*
			 * Read the selected balance from request and store it in the string
			 * variable
			 */
			sodBalance = request.getParameter("selectedSodBalance");
			request.setAttribute("sodBalance", sodBalance);

			balmaintenance.setReasonCode(request.getParameter("reasonCode"));
			balmaintenance.setReasonDesc(request.getParameter("reasonDesc"));
			balmaintenance.setUserNotes(request.getParameter("userNotes"));
			balmaintenance.setSuppliedExternalBalanceAsString((String) request
					.getParameter("suppliedExternalBalance"));
			/* Read the selected entity id from the request */
			entityId = request.getParameter("selectedEntity");
			/* Setting entity id using bean class */
			balmaintenance.getId().setEntityId(entityId);
			/* Setting balance type id using bean class */
			balmaintenance.getId().setBalanceTypeId(selectedBalTypeId);

			/*
			 * This is used to update the data's in DB based on the parameters
			 * passed
			 */

			balmaintMgr.updateBalanceDetail(balmaintenance, sodBalance, hostId,
					entityId, balanceType, balmaintenance.getId()
							.getBalanceDate(), SwtUtil
							.getCurrentSystemFormats(request.getSession()));

			/*
			 * Put the entity access list and labelvalue bean of the entity to
			 * the request
			 */
			putEntityListInReq(request);

			/*
			 * To put the BalanceType list and its label value bean to the
			 * request to the request
			 */
			putBalanceListInReq(request, entityId);

			/*
			 * This method set the currencyList of CurrencyVO in the request
			 * attribute
			 */
			putCurrencyDetailListWithAllInReq(request, entityId);

			setBalmaintenance(balmaintenance);
			setBalType(balType);

			/* Read the selected currency from the request */
			currency = request.getParameter("selectedCurrency");

			request.setAttribute("selectedCurrency", currency);

			request.setAttribute("parentFormRefresh", "yes");
			request.setAttribute("entityId", entityId);
			request.setAttribute("currentPage", (String) request
					.getParameter("currentPage"));
			request.setAttribute("currencyCode", (String) request
					.getParameter("selectedCurrency"));
			request.setAttribute("balanceType", (String) request
					.getParameter("balanceType"));
			request.setAttribute("filterSortStatus", (String) request
					.getParameter("currentFilter")
					+ "#" + (String) request.getParameter("currentSort"));
			request.setAttribute("selectedFilter", (String) request
					.getParameter("currentFilter"));
			request.setAttribute("selectedSort", (String) request
					.getParameter("currentSort"));

			log.debug(this.getClass().getName() + " - [save] - " + "Exit");
			return ("change");
		} catch (SwtException swtexp) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [save] method : - "
					+ swtexp.getMessage());
			setButtonStatus(request, SwtConstants.STR_FALSE,
					SwtConstants.STR_FALSE);
			request.setAttribute("methodName", "change");
			request.setAttribute("screenFieldsStatus", "true");
			String errorCode = swtexp.getErrorCode();

			if (errorCode.equals("errors.DataIntegrityViolationExceptioninAdd")) {
				;
			}

			swtexp.setErrorCode("errors.DataIntegrityViolationException");

			Collection balmaintlist = balmaintMgr.getBalanceList(entityId,
					hostId);

			request.setAttribute("balmaintenancelist", balmaintlist);
			request.setAttribute("parentFormRefresh", "yes");
			saveErrors(request, SwtUtil.logException(swtexp, request, ""));

			return ("change");
		} catch (Exception exp) {

			log.error(this.getClass().getName()
					+ " - Exception Catched in [save] method : - "
					+ exp.getMessage());

			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "save", BalMaintenanceAction.class), request, "");

			return ("fail");
		}
	}

	/**
	 * To set the Page summary list for the Screen
	 * 
	 * @param entityId
	 * @param currentPage
	 * @param maxpage
	 * @param movTotalCount
	 * @param pageSummaryList
	 * @return
	 */
	private void setPageSummaryList(String entityId, int currentPage,
			int maxPage, int movTotalCount,
			ArrayList<SODPageSummary> pageSummaryList) {
		log.debug(this.getClass().getName() + " - [setPageSummaryList] - "
				+ "Entry");
		SODPageSummary sodPageSummary = new SODPageSummary();
		sodPageSummary.setEntityId(entityId);
		sodPageSummary.setCurrentPageNo(currentPage);
		sodPageSummary.setMaxPages(maxPage);
		sodPageSummary.setTotalCount(movTotalCount);
		pageSummaryList.add(sodPageSummary);

		log.debug(this.getClass().getName() + " - [setPageSummaryList] - "
				+ "Exit");
	}

	/**
	 * To set the maximum number of pages for the screen according to the
	 * records.
	 * 
	 * @param totalCount
	 * @param pageSize
	 * @return int
	 */
	private int setMaxPageAttribute(int totalCount, int pageSize) {
		log.debug(this.getClass().getName() + " - [setMaxPageAttribute] - "
				+ " Entry");

		/* Method's local variable declaration */
		int maxPage;
		int remainder;
		/*
		 * The max page is calculate by the total number of records divided by
		 * the number of records per page
		 */
		maxPage = (totalCount) / (pageSize);
		/*
		 * Remainder is the modulo of the total number of records and the number
		 * of records per page
		 */
		remainder = totalCount % pageSize;

		if (remainder > 0) {
			maxPage++;
		}
		log.debug(this.getClass().getName() + " - [setMaxPageAttribute] - "
				+ "Exit");
		return maxPage;
	}

	/**
	 * This is used to set external and internal balance type
	 * 
	 * @param type
	 * @return String
	 */
	public String sodTypeAsString(String type) {
		log.debug(this.getClass().getName() + " - [sodTypeAsString] - "
				+ "Entry");
		/* Condition to check balance type is Manual */
		if (type.equals("M"))
			return SwtConstants.BALTYPE_MANUAL;
		/* Condition to check balance type is zero */
		if (type.equals("Z"))
			return SwtConstants.BALTYPE_ZERO;
		/* Condition to check balance type is Internal */
		else if (type.equals("I"))
			return SwtConstants.BALTYPE_INTERNAL;
		/* Condition to check balance type is External */
		else if (type.equals("E"))
			return SwtConstants.BALTYPE_EXTERNAL;
		/* Condition to check balance type is Predicted */
		else if (type.equals("P"))
			return SwtConstants.BALTYPE_PREDICTED;
		log.debug(this.getClass().getName() + " - [sodTypeAsString] - "
				+ "Exit");
		return SwtConstants.EMPTY_STRING;
	}

	/**
	 * This is used to export the data in SOD screen
	 * 
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return ActionForward
	 * @throws SwtException
	 * 
	 */
	public String exportBalanceMaintenance() throws SwtException {
HttpServletRequest request = ServletActionContext.getRequest();
HttpServletResponse response = ServletActionContext.getResponse();


		/* Methods local variable and class instance declaration */
		// Variable to hold export type
		String exportType = null;
		// Variable to hold fileName
		String fileName = null;
		// Variable to hold titleSuffix
		String titleSuffix = null;
		// Variable to hold dyForm
		// DynaValidatorForm dyForm = null;
		// Variable to hold entityId
		String entityId = null;
		// Variable to hold balanceType
		String balanceType = null;
		// Variable to hold replacebalanceDate
		String replacebalanceDate = null;
		// Variable to hold currency
		String currency = null;
		// Variable to hold pageSizeAsString
		String pageSizeAsString = null;
		// Variable to hold pageSize
		int pageSize = 0;
		// Variable to hold currentPage
		int currentPage = 1;
		// Variable to hold startRowNumber
		int startRowNumber = 0;
		// Variable to hold endRowNumber
		int endRowNumber = 0;
		// Variable to hold maxPage
		int maxPage = 0;
		// Variable to hold totalCount
		int totalCount = 0;
		// Variable to hold currentFilter
		String currentFilter = null;
		// Variable to hold currentSort
		String currentSort = null;
		// Variable to hold filterSortStatus
		String filterSortStatus = null;
		// Variable to hold hostId
		String hostId = null;
		// Variable to hold balmaintenanceDetails
		Collection<BalType> balmaintenanceDetails = null;
		// Variable to hold pageSummaryList
		ArrayList<SODPageSummary> pageSummaryList = null;
		// Variable to hold balType
		BalType balType = null;
		// Variable to hold selectedDate
		Date selectedDate = null;
		// Variable to hold balMaintenance object
		BalMaintenance balMaintenance = null;
		// Variable to hold cacheManagerInst
		CacheManager cacheManagerInst = null;
		// List to hold the balance type label's
		ArrayList<LabelValueBean> balanceTypeList = null;
		// Iterator instance to iterate through balance type label list
		Iterator<LabelValueBean> balanceTypeLabelIterator = null;
		// Variable to hold balTypeLabelValueBean
		LabelValueBean balTypeLabelValueBean = null;
		// Variable to hold balanceTypeLAbel
		String balanceTypeLabel = null;
		// Variable to hold currentPageAsString
		String currentPageAsString = null;
		// Variable to hold roleId
		String roleId = null;
		// Variable to iterate through balance type list
		Iterator<BalType> balTypeIterator = null;
		// Variable to hold balTypeArray
		ArrayList<BalType> balTypeArray = null;
		// Variable to hold user
		String user = null;
		// Variable to hold filterData
		ArrayList<FilterDTO> filterData = null;
		// Variable to hold filterDTO
		FilterDTO filterDTO = null;
		// Variable to hold convertBalMaintenanceToXML
		Obj2XmlBalMaintenance convertBalMaintenanceToXML = null;
		// Variable to hold csvResponse
		String csvResponse = null;
		// Variable to hold columnDataList
		ArrayList<ColumnDTO> columnDataList = null;
		// Variable declaration for pdfGen
		Obj2PdfImpl pdfGen = null;
		// Variable declaration for csvGen
		Obj2CsvImpl csvGen = null;
		// Variable declaration for excelGen
		Obj2XlsImpl excelGen = null;
		// Variable declaration for columnDTO
		ColumnDTO columnDTO = null;
		// Variable declaration for isNext
		boolean isNext = false;
		// Variable declaration for data
		ArrayList<ArrayList<ExportObject>> data = null;


		try {
			log.debug(this.getClass().getName()
					+ " - [exportBalanceMaintenance] - " + "Entry");
			balType = new BalType();
			// Getting exportType from request
			exportType = request.getParameter("exportType");
			// Setting screenFieldsStatus value
			request.setAttribute("screenFieldsStatus", "true");
			// Setting dyForm value
			/* Initiating balmaintenance model class objects. */
			balMaintenance = (BalMaintenance) getBalmaintenance();
			// To get the entity from balmaintenance
			entityId = balMaintenance.getId().getEntityId();
			// Getting balanceType
			balanceType = balMaintenance.getBalanceType();
			// Getting replacebalanceDate
			replacebalanceDate = balMaintenance.getReplacebalanceDate();
			// Getting currency
			currency = balMaintenance.getCurrency();
			/*
			 * Condition to check whether the entity id is null or its length is
			 * zero
			 */
			if ((entityId == null) || (entityId.trim().length() <= 0)) {
				/*
				 * If any one of the condition is true, the current entity of
				 * the user get from the swtUtil class
				 */
				entityId = SwtUtil.getUserCurrentEntity(request.getSession());
				/* The current entity id set here as entity id. */
				balMaintenance.getId().setEntityId(entityId);
			}
			/* Set the default balance type as 'Cash account' */
			balMaintenance.setBalanceType("C");
			/*
			 * Condition to check whether the balanceType is null or not null
			 */
			if (SwtUtil.isEmptyOrNull(balanceType)) {
				balMaintenance.setBalanceType("C");
				balanceType = "C";
			} else {
				balMaintenance.setBalanceType(balanceType);
			}
			/* To set the default currency as All in the screen */
			balMaintenance.setCurrency("All");
			if (SwtUtil.isEmptyOrNull(currency)) {
				balMaintenance.setCurrency("All");
				currency = "All";
			} else {
				balMaintenance.setCurrency(currency);
			}
			/*
			 * Retrieve the system date using swtUtil and set as replace balance
			 * date
			 */
			balMaintenance.setReplacebalanceDate(SwtUtil.getSystemDateString());
			if (SwtUtil.isEmptyOrNull(replacebalanceDate)) {
				balMaintenance.setReplacebalanceDate(SwtUtil
						.getSystemDateString());
				replacebalanceDate = SwtUtil.getSystemDateString();
			} else {
				balMaintenance.setReplacebalanceDate(replacebalanceDate);
			}
			/* Read entity Id from Balmaintenance bean */
			entityId = balMaintenance.getId().getEntityId();
			/*
			 * /* Read the host id form SwtUtil
			 */
			hostId = SwtUtil.getCurrentHostId();
			/*
			 * This method receives the access rights of the user according to
			 * their role to set the button status of the screen
			 */
			putEntityAccessInRequest(request, entityId);
			/*
			 * Set the value of replacebalanceDate as balanceDate, the replace
			 * balance date returns the string it is converted to the date
			 * format by SwtUtil.parseDateGeneral method
			 */
			balMaintenance.getId().setBalanceDate(
					SwtUtil.parseDateGeneral(balMaintenance
							.getReplacebalanceDate()));
			/* Read balanceDate in Date format */
			selectedDate = balMaintenance.getId().getBalanceDate();

			/*
			 * Read page size from swtcommon.properties
			 */
			pageSize=SwtUtil.getPageSizeFromProperty(SwtConstants.BALANCE_MAINTENANCE_PAGE_SIZE);
			// Getting max pages from request
			maxPage = Integer.parseInt(request.getParameter("maxPages"));
			// Getting current page from request
			currentPageAsString = request.getParameter("currentPage");
			if (currentPageAsString != null && currentPageAsString.length() > 0) {
				currentPage = Integer.parseInt(currentPageAsString);
			} else {
				currentPage = 1;
			}
			endRowNumber = pageSize;
			if(currentPage==0)
			{ 
				
				endRowNumber = maxPage *pageSize ;
				startRowNumber = 0;
			}else {
				
			endRowNumber = currentPage * pageSize;
			/* Get the start row number of each page */
			startRowNumber = endRowNumber - pageSize;
			}
			/* Read selected Filter value from request */
			currentFilter = request.getParameter("selectedFilter");
			/* Read value of selectetdSort from the request */
			currentSort = request.getParameter("selectedSort");
			/*
			 * Condition checks if the current filter is null or its length less
			 * than or equal to zero
			 */
			if (currentFilter == null || currentFilter.length() <= 0) {
				/*
				 * Set current filter as All for all columns
				 */
				currentFilter = "All|All|All|All|All|All|All|All|All|All|All|All";
			}
			/*
			 * Condition to check current filter is null or lessthan or equal to
			 * zero
			 */
			if (currentSort == null || currentSort.length() <= 0) {
				/*
				 * Set currenct sort for first column
				 */
				currentSort = "1|false";
			}
			filterSortStatus = currentFilter + "#" + currentSort;
			/*
			 * Passing the values to the accountDetail method of
			 * BalMaintenaceManager
			 */
			// get the role id
			roleId = ((CommonDataManager) request.getSession().getAttribute(
					SwtConstants.CDM_BEAN)).getUser().getRoleId();
			balmaintenanceDetails = balmaintMgr.accountDetail(hostId, entityId,
					balanceType, currency, selectedDate, startRowNumber,
					endRowNumber, filterSortStatus, roleId);
			/* To iterate the collection return from the Manager class */
			balTypeIterator = balmaintenanceDetails.iterator();
			while (balTypeIterator.hasNext()) {
				balType = (BalType) balTypeIterator.next();
				/* Get the total row count from the collection */
				totalCount = balType.getRowCount();
				break;
			}
			/* Returns the integer value of maximum number of pages. */
			maxPage = setMaxPageAttribute(totalCount, pageSize);
			/* Instatiating pageSummaryList to array list */
			pageSummaryList = new ArrayList<SODPageSummary>();
			/* Set page summary list to display in screen */
			setPageSummaryList(entityId, currentPage, maxPage, totalCount,
					pageSummaryList);
			/*
			 * Hide pagination if no of records are "0" or records can be
			 * displayed in one page.
			 */
			if (maxPage <= 1) {
				request.setAttribute("hidePagination", true);
			}

			if (maxPage > 1) {
				isNext = true;
			}
			// Set the values for pagination variables
			request.setAttribute("currentPage", "" + currentPage);
			// Set the values for pageSize
			request.setAttribute("pageSize", "" + pageSize);
			// Set the values for maxPage
			request.setAttribute("maxPage", maxPage + "");
			// Set the values for prevEnabled
			request.setAttribute("pr evEnabled", "false");
			// Set the values for nextEnabled
			request.setAttribute("nextEnabled", "" + isNext);
			// Set the values for balanceType
			request
					.setAttribute("balanceType", balMaintenance
							.getBalanceType());
			// Set the values for pageSummaryList
			request.setAttribute("pageSummaryList", pageSummaryList);
			// Set the values for totalCount
			request.setAttribute("totalCount", totalCount);
			// Set the values for filterSortStatus
			request.setAttribute("filterSortStatus", filterSortStatus);
			// Set the values for selectedFilter
			request.setAttribute("selectedFilter", currentFilter);
			// Set the values for selectedSort
			request.setAttribute("selectedSort", currentSort);

			/* This method set currencyList of CurrencyVO in request attribute */
			putCurrencyDetailListWithAllInReq(request, entityId);
			/* Set the entity list accesed to user in request attribute */
			putEntityListInReq(request);
			/* Set collection of Balance type in request attribute */
			putBalanceListInReq(request, entityId);
			/* To iterate the collection return from the Manager class */
			balTypeIterator = balmaintenanceDetails.iterator();
			balTypeArray = new ArrayList<BalType>();
			while (balTypeIterator.hasNext()) {
				balType = (BalType) balTypeIterator.next();
				/* Returns the Username */
				user = balType.getUser();
				/* Condition to check the user is null */
				if (SwtUtil.isEmptyOrNull(user)) {
					/*
					 * Set the values of startBalanceAsString,inputDateAsString
					 * inputTimeAsString and balanceSource as null
					 */
					balType.setStartBalanceAsString(" ");
					balType.setInputDateAsString(" ");
					balType.setInputTimeAsString(" ");
					balType.setBalanceSource(" ");
				} else {
					/* Condition to check Forecast SOD */
					if (balType.getForecastSOD() == null) {
						/* Setting start balance using bean class */
						balType.setStartBalanceAsString(" ");
					} else {
						/*
						 * Reterive currency format and set start balance using
						 * bean class
						 */
						balType.setStartBalanceAsString(SwtUtil.formatCurrency(
								balType.getBalCurrencyCode(), balType
										.getStartBalance()));
					}
					/*
					 * Reterive the system format and set input date using bean
					 * class
					 */
					balType.setInputDateAsString(SwtUtil.formatDate(balType
							.getInputDate(), SwtUtil.getCurrentSystemFormats(
							request.getSession()).getDateFormatValue()));
					/* Set the value of input Time as InputTimeAsString */
					balType.setInputTimeAsString(balType.getInputTime());
					/* Condition to check external SOD */
					if (balType.getExternalSOD() == null) {
						/* Setting external SOD using bean class */
						balType.setExternalSODAsString(" ");
					} else {
						/*
						 * Condition to check supplied external balance has
						 * value
						 */
						if (balType.getSuppliedExternalBalance() != null) {
							/*
							 * Reterive the currency format and set external SOD
							 * using bean class
							 */
							balType.setExternalSODAsString(SwtUtil
									.formatCurrency(balType
											.getBalCurrencyCode(), balType
											.getSuppliedExternalBalance()));
						} else {
							/*
							 * Reterive the currency format and set external SOD
							 * using bean class
							 */
							balType.setExternalSODAsString(SwtUtil
									.formatCurrency(balType
											.getBalCurrencyCode(), balType
											.getExternalSOD()));
						}
					}
					/*
					 * Reterive the currency format and set Forecast SOD using
					 * bean class
					 */
					balType.setForecastSODAsString(SwtUtil.formatCurrency(
							balType.getBalCurrencyCode(), balType
									.getForecastSOD()));
					/*
					 * Reterive the currency format and set external SOD using
					 * bean class
					 */
					balType.setExternalSODAsString(SwtUtil.formatCurrency(
							balType.getBalCurrencyCode(), balType
									.getExternalSOD()));
					/*
					 * To check since the return type for the forecastSOD has
					 * been changed from java.lang.Double to
					 * java.math.BigDecimal for the issue to display the
					 * externalWorkingSOD correctly in the screen
					 */
					if (balType.getExternalSOD() != null
							&& balType.getExternalSOD().doubleValue() < 0.0)
						/*
						 * Setting negative value for external SOD using bean
						 * class
						 */
						balType.setExternalSODNegative(true);
					if (balType.getForecastSOD() != null
							&& balType.getForecastSOD().doubleValue() < 0.0)
						/*
						 * Setting negative value for forecast SOD using bean
						 * class
						 */
						balType.setForecastSODNegative(true);
				}
				balTypeArray.add(balType);
			}

			cacheManagerInst = CacheManager.getInstance();
			balanceTypeList = (ArrayList<LabelValueBean>) cacheManagerInst
					.getMiscParamsLVL(SwtConstants.BALANCE_TYPE, entityId);
			/* Condition to check collection not equal to null */
			if (balanceTypeList != null) {
				balanceTypeLabelIterator = balanceTypeList.iterator();
				while (balanceTypeLabelIterator.hasNext()) {
					balTypeLabelValueBean = (balanceTypeLabelIterator.next());
					if ((balTypeLabelValueBean.getLabel() != null)
							&& !balTypeLabelValueBean.getLabel().equals("")) {
						if (balanceType.equalsIgnoreCase(balTypeLabelValueBean
								.getValue())) {
							balanceTypeLabel = balTypeLabelValueBean.getLabel();
						}
					}
				}
			}
			request
					.setAttribute("balmaintenanceDetails",
							balmaintenanceDetails);
			setBalmaintenance(balMaintenance);
			/* This method is to put the SystemDate in request attribute. */
			putSystemDateInRequest(request);
			/* To set the column headings */
			columnDataList = new ArrayList<ColumnDTO>();

			columnDTO = new ColumnDTO();
			columnDTO.setHeading("Account Id");
			columnDTO.setType("num");
			columnDTO.setDataElement("accountid");
			columnDataList.add(columnDTO);

			columnDTO = new ColumnDTO();
			columnDTO.setHeading("Name");
			columnDTO.setType("str");
			columnDTO.setDataElement("name");

			columnDataList.add(columnDTO);
			columnDTO = new ColumnDTO();
			columnDTO.setHeading("Effective Forecast SOD");
			columnDTO.setType("num");
			columnDTO.setDataElement("effectiveforecastSOD");
			columnDataList.add(columnDTO);

			columnDTO = new ColumnDTO();
			columnDTO.setHeading("Forecast SOD Type");
			columnDTO.setType("str");
			columnDTO.setDataElement("forecastSODtype");
			columnDataList.add(columnDTO);

			columnDTO = new ColumnDTO();
			columnDTO.setHeading("Effective External SOD");
			columnDTO.setType("num");
			columnDTO.setDataElement("effectiveexternalSOD");
			columnDataList.add(columnDTO);

			columnDTO = new ColumnDTO();
			columnDTO.setHeading("Effective SOD Type");
			columnDTO.setType("str");
			columnDTO.setDataElement("effectiveSODtype");
			columnDataList.add(columnDTO);

			columnDTO = new ColumnDTO();
			columnDTO.setHeading("Reason Code");
			columnDTO.setType("str");
			columnDTO.setDataElement("reasoncode");
			columnDataList.add(columnDTO);

			columnDTO = new ColumnDTO();
			columnDTO.setHeading("Reason Description");
			columnDTO.setType("str");
			columnDTO.setDataElement("reasondescription");
			columnDataList.add(columnDTO);

			columnDTO = new ColumnDTO();
			columnDTO.setHeading("User Notes");
			columnDTO.setType("str");
			columnDTO.setDataElement("usernotes");
			columnDataList.add(columnDTO);

			columnDTO = new ColumnDTO();
			columnDTO.setHeading("User ID");
			columnDTO.setType("str");
			columnDTO.setDataElement("userid");
			columnDataList.add(columnDTO);

			columnDTO = new ColumnDTO();
			columnDTO.setHeading("Update Date");
			columnDTO.setType("num");
			columnDTO.setDataElement("updatedate");
			columnDataList.add(columnDTO);

			/* Set and display the data in defined format */
			filterData = new ArrayList<FilterDTO>();
			filterDTO = new FilterDTO();

			filterDTO.setName("Entity");
			filterDTO.setValue(entityId);
			filterData.add(filterDTO);

			filterDTO = new FilterDTO();
			filterDTO.setName("Balance Type");
			filterDTO.setValue(balanceTypeLabel);
			filterData.add(filterDTO);

			filterDTO = new FilterDTO();
			filterDTO.setName("Currency");
			filterDTO.setValue(currency);
			filterData.add(filterDTO);

			filterDTO = new FilterDTO();
			filterDTO.setName("Date");
			filterDTO.setValue(replacebalanceDate);
			filterData.add(filterDTO);

			/* Gets the Screen name from request */
			fileName = request.getParameter("screen").replaceAll(" ", "");
			titleSuffix = PropertiesFileLoader.getInstance()
					.getPropertiesValue("windows.title.suffix");
			/*
			 * Start Code modified by Chidambaranathan for include timestamp in
			 * the export function for Mantis_1513 on 04-Aug-2011
			 */
			/* check whether titleSuffix is null and set empty string */
			if (titleSuffix == null) {
				titleSuffix = "";
			}
			convertBalMaintenanceToXML = new Obj2XmlBalMaintenance();
			 data  = 	convertBalMaintenanceToXML.getExportData(columnDataList, filterData, (ArrayList) balmaintenanceDetails)	;								
			/* To export the data in PDF,Excel and CSV format */
			if (exportType.equalsIgnoreCase("excel")) {
				
				response.setContentType("application/vnd.ms-excel");
				response.setHeader("Content-disposition",
						"attachment; filename=" + fileName + titleSuffix + "_"
								+ SwtUtil.FormatCurrentDate() + ".xls");

				excelGen = new Obj2XlsImpl();
				excelGen.convertObject(request, response, columnDataList, filterData, data, null, null, fileName);
			} else if (exportType.equalsIgnoreCase("pdf")) {
				pdfGen = new Obj2PdfImpl();
				response.setContentType("application/pdf");
				response.setHeader("Content-disposition",
						"attachment; filename=" + fileName + titleSuffix + "_"
								+ SwtUtil.FormatCurrentDate() + ".pdf");
				pdfGen.convertObject(request, response, columnDataList, filterData, data, null, null, fileName);
			} else if (exportType.equalsIgnoreCase("csv")) {
				csvGen = new Obj2CsvImpl();
				response.setContentType("application/vnd.ms-excel");
				response.setHeader("Content-disposition",
						"attachment; filename=" + fileName + titleSuffix + "_"
								+ SwtUtil.FormatCurrentDate() + ".csv");
				csvResponse = csvGen.convertObject(request, columnDataList, filterData, data, null, null, fileName);
				/*
				 * End Code modified by Chidambaranathan for include timestamp
				 * in the export function for Mantis_1513 on 04-Aug-2011
				 */
				try {
					response.getOutputStream().print(csvResponse);
					log.debug(this.getClass().getName()
							+ "- [exportBalanceMaintenance] - Exiting ");
				} catch (IOException e) {
					log.error(this.getClass().getName()
							+ "- [exportBalanceMaintenance] - IOException "
							+ e.getMessage());
					throw new SwtException(e.getMessage());
				}
			}

		} catch (SwtException swtexp) {
			log
					.error(this.getClass().getName()
							+ " - Exception Catched in [exportBalanceMaintenance] method : - "
							+ swtexp.getMessage());
			SwtUtil.logException(swtexp, request, "");

		} catch (Exception exp) {

			log
					.error(this.getClass().getName()
							+ " - Exception Catched in [exportBalanceMaintenance] method : - "
							+ exp.getMessage());

			SwtUtil.logException(SwtErrorHandler.getInstance()
					.handleException(exp, "exportBalanceMaintenance",
							BalMaintenanceAction.class), request, "");
		} finally {
			// Nullifying the already created objects
			exportType = null;
			fileName = null;
			titleSuffix = null;
			entityId = null;
			balanceType = null;
			replacebalanceDate = null;
			currency = null;
			pageSizeAsString = null;
			currentFilter = null;
			currentSort = null;
			filterSortStatus = null;
			hostId = null;
			balmaintenanceDetails = null;
			pageSummaryList = null;
			balType = null;
			selectedDate = null;
			balMaintenance = null;
			cacheManagerInst = null;
			balanceTypeList = null;
			balanceTypeLabelIterator = null;
			balTypeLabelValueBean = null;
			balanceTypeLabel = null;
			currentPageAsString = null;
			roleId = null;
			pdfGen = null;
			csvGen = null;
			excelGen = null;
			balTypeIterator = null;
			balTypeArray = null;
			user = null;
			filterData = null;
			filterDTO = null;
			convertBalMaintenanceToXML = null;
			csvResponse = null;
			columnDataList = null;
			columnDTO = null;

		}
    return null;
	}

	/**
	 * Method used to pass the parvalue and get the description in alert message
	 * event
	 * 
	 * @param parValue
	 * @return String
	 * @throws Exception
	 */
	public String miscparamDesc(String parValue, String entityId)
			throws SwtException {
		log.debug(this.getClass().getName() + " - [misparamDesc] - " + "Entry");

		CacheManager cacheManagerInst = CacheManager.getInstance();
		/* Collect the Misc Params of Alert messages */
		Collection coll = cacheManagerInst.getMiscParams("BALANCETYPE",
				parValue, entityId);
		if (coll != null) {
			Iterator itr = coll.iterator();
			MiscParams miscParamsObj = (MiscParams) itr.next();
			log.debug(this.getClass().getName() + " - [misparamDesc] - "
					+ "Exit");
			return miscParamsObj.getParValue();
		} else {
			log.debug(this.getClass().getName() + " - [misparamDesc] - "
					+ "Exit");
			return "";
		}
	}

	/**
	 * Method used to pass the parvalue and get the description in alert message
	 * event
	 * 
	 * @param keyValue
	 * @return String
	 * @throws Exception
	 */
	public String miscparamParVal(String keyValue, String entityId)
			throws SwtException {
		log.debug(this.getClass().getName() + " - [miscparamParVal] - "
				+ "Entry");

		CacheManager cacheManagerInst = CacheManager.getInstance();
		String parVal = "";
		/* Collect the Misc Params of Alert messages */
		Collection coll = cacheManagerInst.getMiscParamsDetails("BALANCETYPE",
				entityId);
		if (coll != null) {
			Iterator itr = coll.iterator();

			while (itr.hasNext()) {
				MiscParams miscParamsObj = (MiscParams) itr.next();
				if (miscParamsObj.getParValue().equalsIgnoreCase(keyValue)) {
					parVal = miscParamsObj.getId().getKey2();
					break;
				}
			}
		}

		log.debug(this.getClass().getName() + " - [miscparamParVal] - "
				+ "Exit");

		return parVal;
	}
	
	public String openAlerting() throws SwtException {
HttpServletRequest request = ServletActionContext.getRequest();
HttpServletResponse response = ServletActionContext.getResponse();

		request.setAttribute("tooltipEntityId", request.getParameter("tooltipEntityId"));
		request.setAttribute("tooltipCurrencyCode", request.getParameter("tooltipCurrencyCode"));
		request.setAttribute("tooltipFacilityId", request.getParameter("tooltipFacilityId"));
		request.setAttribute("tooltipSelectedDate", request.getParameter("tooltipSelectedDate"));
		request.setAttribute("tooltipSelectedAccount", request.getParameter("tooltipSelectedAccount"));
		request.setAttribute("tooltipOtherParams", request.getParameter("tooltipOtherParams"));
		return ("alert");
	}

}
