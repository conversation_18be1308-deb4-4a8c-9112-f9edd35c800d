/* (c) MessageFormatsAction.java 1.0 23/08/06
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */

package org.swallow.maintenance.web;

import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.Iterator;
import java.util.Map.Entry;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;









import org.springframework.context.ApplicationContext;
import org.springframework.context.support.FileSystemXmlApplicationContext;
import org.swallow.control.model.Scenario;
import org.swallow.control.service.ScenMaintenanceManager;
import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.maintenance.model.MessageFields;
import org.swallow.maintenance.model.MessageFormats;
import org.swallow.maintenance.model.ScenarioMessageFields;
import org.swallow.maintenance.model.ScenarioMessageFormats;
import org.swallow.maintenance.service.MessageFieldsManager;
import org.swallow.maintenance.service.MessageFormatsManager;
import org.swallow.util.CacheManager;
import org.swallow.util.CommonDataManager;
import org.swallow.util.SwtConstants;
import org.swallow.util.SwtUtil;
import org.swallow.util.struts.ActionMessages;
import org.swallow.util.struts.ActionMessage;
import org.swallow.util.struts.ActionErrors;
import org.swallow.util.LabelValueBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.apache.struts2.convention.annotation.Result;
import org.apache.struts2.convention.annotation.AllowedMethods;
import org.apache.struts2.convention.annotation.Action;
import org.apache.struts2.ServletActionContext;
import org.swallow.util.struts.CustomActionSupport;

/**
 * <AUTHOR>
 * 
 * TODO To change the template for this generated type comment go to Window -
 * Preferences - Java - Code Style - Code Templates
 */
/**
 * 
 * This is Class is used for perform funtions like Add,save,update view and
 * delete on Sweep Message formats
 * 
 */
@Action(value = "/messageformats", results = {
	@Result(name = "success", location = "/jsp/maintenance/messageformatmaintenance.jsp"),
	@Result(name = "add", location = "/jsp/maintenance/messageformatadd.jsp"),
	@Result(name = "copyFrom", location = "/jsp/maintenance/copyfromformatid.jsp"),
	@Result(name = "fail", location = "/error.jsp"),
	@Result(name = "copyFromScenario", location = "/jsp/maintenance/scenariocopyfromformatid.jsp"),
	@Result(name = "addScenarioFormat", location = "/jsp/maintenance/scenariomessageformatadd.jsp"),
	@Result(name = "scenarioList", location = "/jsp/maintenance/scenariomessageformatmaintenance.jsp"),
})

@AllowedMethods ({"display" ,"add" ,"save" ,"update" ,"change" ,"view" ,"delete" ,"copyFrom" ,"copy" ,"displayScenarioFormats" ,"changeScenarioFormat" ,"viewScenarioFormat" ,"addScenarioFormat" ,"saveScenario" ,"updateScenario" ,"copyFromScenario" ,"copyScenario" ,"deleteScenario" })
public class MessageFormatsAction extends CustomActionSupport {

private MessageFormats messageFormats;
public MessageFormats getMessageFormats() {
	if (messageFormats == null) {
		messageFormats = new MessageFormats();
	}
	return messageFormats;
}
public void setMessageFormats(MessageFormats messageFormats) {
	this.messageFormats = messageFormats;
	HttpServletRequest request = ServletActionContext.getRequest();
	request.setAttribute("messageFormats", messageFormats);
}
private ScenarioMessageFormats scenarioMessageFormats;
public ScenarioMessageFormats getScenarioMessageFormats() {
	if (scenarioMessageFormats == null) {
		scenarioMessageFormats = new ScenarioMessageFormats();
	}
	return scenarioMessageFormats;
}
public void setScenarioMessageFormats(ScenarioMessageFormats scenarioMessageFormats) {
	this.scenarioMessageFormats = scenarioMessageFormats;
	HttpServletRequest request = ServletActionContext.getRequest();
	request.setAttribute("scenarioMessageFormats", scenarioMessageFormats);
}

	@Autowired
private MessageFormatsManager messageFormatsManager = null;
	private final Log log = LogFactory.getLog(MessageFormatsAction.class);
	private ApplicationContext ctx = null;

	/**
	 * @param messageFormatsManager
	 */
	public void setMessageFormatsManager(
			MessageFormatsManager messageFormatsManager) {
		this.messageFormatsManager = messageFormatsManager;
	}

	/**
	 * @param name
	 * @return
	 */
	public Object getBean(String name) {
		if (ctx == null) {
			String[] confLoc = {
					"../webapps/swallowtech/WEB-INF/applicationContext.xml",
					"../webapps/swallowtech/WEB-INF/applicationContext-hibernate.xml" };
			ctx = new FileSystemXmlApplicationContext(confLoc);
		}

		return ctx.getBean(name);
	}

	
	public String execute() throws Exception {
	    HttpServletRequest request = ServletActionContext.getRequest();

	    // List of methods 
	    String method = String.valueOf(request.getParameter("method"));

	    switch (method) {
	        case "unspecified":
	            return unspecified();
	        case "display":
	            return display();
	        case "add":
	            return add();
	        case "save":
	            return save();
	        case "update":
	            return update();
	        case "change":
	            return change();
	        case "view":
	            return view();
	        case "delete":
	            return delete();
	        case "copyFrom":
	            return copyFrom();
	        case "copy":
	            return copy();
	        case "displayScenarioFormats":
	            return displayScenarioFormats();
	        case "changeScenarioFormat":
	            return changeScenarioFormat();
	        case "viewScenarioFormat":
	            return viewScenarioFormat();
	        case "addScenarioFormat":
	            return addScenarioFormat();
	        case "saveScenario":
	            return saveScenario();
	        case "updateScenario":
	            return updateScenario();
	        case "copyFromScenario":
	            return copyFromScenario();
	        case "copyScenario":
	            return copyScenario();
	        case "deleteScenario":
	            return deleteScenario();
	        default:
	            break;
	    }

	    return unspecified();
	}

	
	/**
	 * Default method implementation as per struts setting.
	 * 
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return actionForword
	 * @throws SwtException
	 */
	public String unspecified()
			throws SwtException {
HttpServletRequest request = ServletActionContext.getRequest();
HttpServletResponse response = ServletActionContext.getResponse();

		
		boolean isScenarioFormat = "Y".equalsIgnoreCase(request.getParameter("isScenarioFormat"));
		if(isScenarioFormat)
			return displayScenarioFormats();
		else 
			return display();
	}

	/**
	 * This is called from submition of Sweep Message Formate screen.
	 * 
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return a Result of type Action forward
	 * <AUTHOR> Tripathi has re-written this method on 6th Aug 07
	 */
	public String display()
			throws SwtException {
HttpServletRequest request = ServletActionContext.getRequest();
HttpServletResponse response = ServletActionContext.getResponse();

		log.debug("entering MessageFormatsAction.'display' method");

		try {
// To remove: 			DynaValidatorForm dyForm = (DynaValidatorForm) form;
			MessageFormats msgfmt = (MessageFormats) getMessageFormats();
			String hostId = CacheManager.getInstance().getHostId();
			String entityId = msgfmt.getId().getEntityId();

			if (!((entityId != null) && (entityId.trim().length() >= 0))) {
				entityId = SwtUtil.getUserCurrentEntity(request.getSession());
				msgfmt.getId().setEntityId(entityId);
			}

			// Fetching the data from P_Message_Format table and putting it to
			// request object
			Collection collMsgFmts = messageFormatsManager
					.getMsgFormatDetailList(hostId, "All", entityId);

			request.setAttribute("msgFormatDetails", collMsgFmts);

			int accessInd = SwtUtil.getMenuEntityCurrGrpAccess(request,
					entityId, null);

			if (accessInd == 0) {
				setButtonStatus(request, SwtConstants.STR_TRUE,
						SwtConstants.STR_FALSE, SwtConstants.STR_FALSE,
						SwtConstants.STR_FALSE);
				request.setAttribute("EntityAccess",
						SwtConstants.ENTITY_FULL_ACCESS + "");
			} else {
				setButtonStatus(request, SwtConstants.STR_FALSE,
						SwtConstants.STR_FALSE, SwtConstants.STR_FALSE,
						SwtConstants.STR_FALSE);
				request.setAttribute("EntityAccess",
						SwtConstants.ENTITY_READ_ACCESS + "");
			}

			String temp = "3,333,333,333";
			temp = temp.replaceAll(",", "");

			putEntityListInReq(request);
			log.debug("Exiting MessageFormatsAction.'display' method");
		} catch (SwtException swtexp) {
			log
					.error("SwtException Catch in MessageFormatsAction.'display' method : "
							+ swtexp.getMessage());

			SwtUtil.logException(swtexp, request, "");

			return ("fail");
		} catch (Exception exp) {
			log
					.error("Exception Catch in MessageFormatsAction.'display' method : "
							+ exp.getMessage());

			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "display", MessageFormatsAction.class), request, "");

			return ("fail");
		}

		return ("success");
	}

	/**
	 * This method is called from click on 'Add' button of Sweep Message Formate
	 * screen.
	 * 
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return ActionForward
	 * @throws SwtException
	 * <AUTHOR> Tripathi has re-written this method on 6th Aug 07
	 */
	public String add()
			throws SwtException {
HttpServletRequest request = ServletActionContext.getRequest();
HttpServletResponse response = ServletActionContext.getResponse();


		// Variable Declaration for dyForm
// To remove: 		DynaValidatorForm dyForm = null;
		// Variable Declaration for interfaceId
		Collection interfaceId = null;
		// Variable Declaration for msgfmt
		MessageFormats msgfmt = null;
		// Variable Declaration for hostId
		String hostId = null;
		// Variable Declaration for entityId
		String entityId = null;
		// Variable Declaration for entityName
		String entityName = null;
		// Variable Declaration for pathPropFile
		String pathPropFile = null;

		try {
			log.debug("entering MessageFormatsAction.'add' method");
// To remove: 			dyForm = (DynaValidatorForm) form;
			msgfmt = (MessageFormats) getMessageFormats();
			// getting hostId
			hostId = CacheManager.getInstance().getHostId();
			// Getting Entity Id
			entityId = request.getParameter("entityCode");
			// Getting Entity Name
			entityName = request.getParameter("entityName");

			// Getting path from SwtCommon.prop file
			// Getting message Path
			pathPropFile = messageFormatsManager.getMessageFormatPath();
			// Getting interface Id
			interfaceId = messageFormatsManager.getInterfaceId();
			request.setAttribute("interfaceId", interfaceId);
			// Set hostId in bean
			msgfmt.getId().setHostId(hostId);
			// Set entity Id in bean
			msgfmt.getId().setEntityId(entityId);
			// Set Message path in bean
			msgfmt.setPath(pathPropFile);
			setMessageFormats(msgfmt);

			putEntityListInReq(request);
			request.setAttribute("entityName", entityName);
			
			request.setAttribute("methodName", "save");
			request.getSession().setAttribute("formatTypeInSession",
					SwtConstants.FORMAT_TYPE_FIXED);
			request.getSession().setAttribute("usageInSession",
					SwtConstants.FORMAT_TYPE_SWEEP);
			request.getSession().setAttribute("messageFieldDetailsInSession",
					null);
			request.getSession().setAttribute(
					"messageFieldDetailsInSessionSize", "0");
		} catch (SwtException swtexp) {
			log
					.error("SwtException Catch in MessageFormatsAction.'add' method : "
							+ swtexp.getMessage());

			SwtUtil.logException(swtexp, request, "");

			return ("fail");
		} catch (Exception exp) {
			log.error("Exception Catch in MessageFormatsAction.'add' method : "
					+ exp.getMessage());

			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "add", MessageFormatsAction.class), request, "");

			return ("fail");
		} finally {
			log.debug("Exiting MessageFormatsAction.'add' method");
			interfaceId = null;
			hostId = null;
			entityId = null;
			entityName = null;
			pathPropFile = null;

		}

		return ("add");
	}

	/**
	 * This method is called while saving the message format details into
	 * database.
	 * 
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return ActionForward
	 * @throws SwtException
	 */
	public String save()
			throws SwtException {
HttpServletRequest request = ServletActionContext.getRequest();
HttpServletResponse response = ServletActionContext.getResponse();

		// Variable Declaration for errors
		ActionMessages errors = null;
		// Variable Declaration for dyForm
// To remove: 		DynaValidatorForm dyForm = null;
		// Variable Declaration for msgfmt
		MessageFormats msgFormat = null;
		// Variable Declaration for currentFormatType
		String currentFormatType = null;
		// Variable Declaration for seqNoAsString
		String seqNoAsString = null;
		// Variable Declaration for formatId
		String formatId = null;
		// Variable Declaration for entityId
		String entityId = null;
		// Variable Declaration for hostId
		String hostId = null;
		// Variable Declaration for collMsgFlds
		Collection collMsgFlds = null;
		// Variable Declaration for messageFieldDetails
		Collection messageFieldDetails = null;
		// Variable Declaration for interfaceId
		Collection interfaceId = null;
		// Variable Declaration for itr
		Iterator itrMessageFields = null;
		// Variable Declaration for serialNoPrimitive
		int serialNoPrimitive = 0;
		// Variable Declaration for msgFldExisting
		MessageFields msgFldExisting = null;
		// Variable Declaration for msgFld
		MessageFields msgFld = null;
		// Variable Declaration for serialNoObject
		Integer serialNoObject = null;
		// Variable Declaration for seqNoObject
		Integer seqNoObject = null;
		// Variable Declaration for lineNoAsString
		String lineNoAsString = null;
		try {
			log.debug(this.getClass().getName() + " - [save] - " + "Entering");
			// instance for action messages
			errors = new ActionMessages();
			// Getting the form values
// To remove: 			dyForm = (DynaValidatorForm) form;
			// instance for message format
			msgFormat = new MessageFormats();
			// get message format values from form
			msgFormat = (MessageFormats) getMessageFormats();
			/* Getting the format id and entity id using bean class */
			formatId = msgFormat.getId().getFormatId().trim();
			entityId = msgFormat.getId().getEntityId().trim();
			/* setting the host from Swtutil fle */
			hostId = SwtUtil.getCurrentHostId();
			/* Setting format id,entity id and host id using bean class */
			msgFormat.getId().setEntityId(entityId);
			msgFormat.getId().setFormatId(formatId);
			msgFormat.getId().setHostId(hostId);
			messageFieldDetails = (Collection) request.getSession()
					.getAttribute("messageFieldDetailsInSession");
			collMsgFlds = messageFieldDetails;
			/* Condition to check the collection value */
			if (collMsgFlds != null) {
				itrMessageFields = collMsgFlds.iterator();
				/*
				 * Loop to iterate the records based on the message type save
				 * the records in DB
				 */
				while (itrMessageFields.hasNext()) {
					msgFldExisting = (MessageFields) (itrMessageFields.next());
					if ((msgFldExisting.getLineNoAsString() != null)
							&& !(msgFldExisting.getLineNoAsString().equals(""))) {
						if (msgFldExisting.getEndPos() == null
								|| msgFldExisting.getEndPos().equals("0")
								|| msgFldExisting.getEndPos().equals(""))
							currentFormatType = SwtConstants.FORMAT_TYPE_TAGGED_VARIABLE;
						else
							currentFormatType = SwtConstants.FORMAT_TYPE_TAGGED;
						break;
						/* Condition execute if the message type is FIXED */
					} else {
						if ((msgFldExisting.getStartPos() != null)
								&& !(msgFldExisting.getStartPos().equals(""))) {
							currentFormatType = SwtConstants.FORMAT_TYPE_FIXED;

							break;
						} else {
							/* Condition execute if the message type is DELMITED */
							currentFormatType = SwtConstants.FORMAT_TYPE_DELIMITED;

							break;
						}
					}
				}
			}
			if (!msgFormat.getFormatType().equals(currentFormatType)) {
				request.getSession().setAttribute(
						"messageFieldDetailsInSession", null);
			}

			serialNoPrimitive = 1;

			if (messageFieldDetails != null) {
				msgFld = new MessageFields();
				itrMessageFields = messageFieldDetails.iterator();

				while (itrMessageFields.hasNext()) {
					msgFld = (MessageFields) (itrMessageFields.next());
					msgFld.getId().setHostId(hostId);
					msgFld.getId().setEntityId(entityId);
					msgFld.getId().setFormatId(formatId);

					serialNoObject = new Integer(serialNoPrimitive);
					msgFld.getId().setSerialNo(serialNoObject);
					/* Getting and setting the sequence number using bean class */
					seqNoAsString = msgFld.getSeqNoAsString();

					if ((seqNoAsString != null) && !seqNoAsString.equals("")) {
						seqNoAsString = seqNoAsString.trim();

						seqNoObject = new Integer(msgFld.getSeqNoAsString());
						msgFld.setSeqNo(seqNoObject);
					}
					/* Getting and setting the line number using bean class */
					if ((msgFld.getLineNoAsString() != null)
							&& !(msgFld.getLineNoAsString().equals(""))) {
						lineNoAsString = msgFld.getLineNoAsString().trim();
						msgFld.setLineNo(new Integer(lineNoAsString));
					}

					serialNoPrimitive++;
				}
			}
			//code modified by sandeepkumar for mantis 2092
			//put format type in request
			request.getSession().setAttribute("formatTypeInSession",
					msgFormat.getFormatType());
			
			request.getSession().setAttribute("usageInSession",
					msgFormat.getUsage());
			/* Save the message format details by calling manager class */
			messageFormatsManager.saveMsgFormatDetails(msgFormat,
					messageFieldDetails);
			log.debug(this.getClass().getName() + " - [save] - " + "Exiiting");
			request.setAttribute("parentFormRefresh", "yes");
		} catch (SwtException swtexp) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [save] method - "
					+ swtexp.getMessage());

			if (swtexp.getErrorCode().equals(
					"errors.DataIntegrityViolationExceptioninAdd")) {
				if (errors != null) {
					errors.add("", new ActionMessage(swtexp.getErrorCode()));
				}
				saveErrors(request, errors);

			} else {
				saveErrors(request, SwtUtil.logException(swtexp, request, ""));
			}

			return ("add");
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [save] method - "
					+ exp.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "save", MessageFormatsAction.class), request, "");

			return ("fail");
		} finally {

			currentFormatType = null;
			formatId = null;
			entityId = null;
			hostId = null;
			messageFieldDetails = null;
			interfaceId = null;
			msgFldExisting = null;
			serialNoObject = null;
			lineNoAsString = null;
			request.setAttribute("methodName", "add");
			request.setAttribute("screenFieldsStatus", "");
			request.setAttribute("entityName", request
					.getParameter("entityName"));
			interfaceId = messageFormatsManager.getInterfaceId();
			request.setAttribute("interfaceId", interfaceId);

		}
		return ("add");
	}

	/**
	 * This method is called while updating the message format details into
	 * database.
	 * 
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return ActionForward
	 * @throws SwtException
	 * <AUTHOR> Tripathi has re-written this method on 6th Aug 07
	 */
	public String update()
			throws SwtException {
HttpServletRequest request = ServletActionContext.getRequest();
HttpServletResponse response = ServletActionContext.getResponse();


		// variable Decleration for dyForm
// To remove: 		DynaValidatorForm dyForm = null;
		// variable Decleration for errors
		ActionErrors errors = null;
		// variable Decleration for msgFmtDataBase
		MessageFormats msgFmtDataBase = null;
		// variable Decleration for currentFormatType
		String currentFormatType = null;
		// variable Decleration for interfaceId
		Collection interfaceId = null;
		// variable Decleration for msgfmt
		MessageFormats msgfmt = null;
		// variable Decleration for hostId
		String hostId = null;
		// variable Decleration for formatId
		String formatId = null;
		// variable Decleration for entityId
		String entityId = null;

		// variable Decleration for collMsgFlds
		Collection collMsgFlds = null;
		// variable Decleration for itr
		Iterator itrMessageFields = null;
		// variable Decleration for msgFldExisting
		MessageFields msgFldExisting = null;
		try {
			log.debug("Entering MessageFormatsAction.'update' method");
// To remove: 			dyForm = (DynaValidatorForm) form;
			errors = new ActionErrors();
			msgfmt = (MessageFormats) getMessageFormats();
			// Get host Id
			hostId = CacheManager.getInstance().getHostId();
			// Get format Id
			formatId = msgfmt.getId().getFormatId().trim();
			// Get Entity Id
			entityId = msgfmt.getId().getEntityId().trim();

			// set host Id in bean
			msgfmt.getId().setHostId(hostId);

			collMsgFlds = (Collection) request.getSession().getAttribute(
					"messageFieldDetailsInSession");

			if ((collMsgFlds != null) && (collMsgFlds.size() > 0)) {
				itrMessageFields = collMsgFlds.iterator();

				while (itrMessageFields.hasNext()) {
					msgFldExisting = (MessageFields) (itrMessageFields.next());
					if ((msgFldExisting.getLineNoAsString() != null)
							&& !(msgFldExisting.getLineNoAsString().equals(""))) {
						if (msgFldExisting.getEndPos() == null
								|| msgFldExisting.getEndPos().equals("0")
								|| msgFldExisting.getEndPos().equals(""))
							currentFormatType = SwtConstants.FORMAT_TYPE_TAGGED_VARIABLE;
						else
							currentFormatType = SwtConstants.FORMAT_TYPE_TAGGED;
						break;

					} else {
						if ((msgFldExisting.getStartPos() != null)
								&& !(msgFldExisting.getStartPos().equals(""))) {
							currentFormatType = SwtConstants.FORMAT_TYPE_FIXED;

							break;
						} else {
							currentFormatType = SwtConstants.FORMAT_TYPE_DELIMITED;

							break;
						}
					}
				}
			}

			if (!msgfmt.getFormatType().equals(currentFormatType)) {
				request.getSession().setAttribute(
						"messageFieldDetailsInSession", null);
			}

			msgFmtDataBase = messageFormatsManager.getMsgFormatDetail(entityId,
					hostId, formatId);

			if (msgFmtDataBase.getFormatType().equals(msgfmt.getFormatType())) {
				updateWhenFormatTypeNotChanged(request, msgfmt);
			} else {
				updateWhenFormatTypeChanged(request, msgfmt);
			}

			request.setAttribute("parentFormRefresh", "yes");
		} catch (SwtException swtexp) {
			log
					.error("SwtException Catch in MessageFormatsAction.'update' method : "
							+ swtexp.getMessage());

			saveErrors(request, SwtUtil.logException(swtexp, request, ""));

			return ("add");
		} catch (Exception exp) {
			log
					.error("Exception Catch in MessageFormatsAction.'update' method : "
							+ exp.getMessage());

			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "update", MessageFormatsAction.class), request, "");

			return ("fail");
		} finally {
			log.debug("Exiting MessageFormatsAction.'update' method");
			errors = null;
			msgFmtDataBase = null;
			currentFormatType = null;
			interfaceId = null;
			hostId = null;
			formatId = null;
			entityId = null;
			collMsgFlds = null;
			msgFldExisting = null;

			request.setAttribute("methodName", "update");
			request.setAttribute("screenFieldsStatus", "true");

			interfaceId = messageFormatsManager.getInterfaceId();
			request.setAttribute("interfaceId", interfaceId);

			putEntityListInReq(request);
		}

		log.debug("Exiting MessageFormatsAction.'update' method");

		return ("add");
	}

	/**
	 * This method is called when user selects any message format and clicks on
	 * the change button of message format maintenance screen.
	 * 
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return ActionForward
	 * @throws SwtException
	 */
	public String change()
			throws SwtException {
HttpServletRequest request = ServletActionContext.getRequest();
HttpServletResponse response = ServletActionContext.getResponse();

		/* Start:code modified by sandeepkumar for mantis 2092 */
		// Variable declaration for dyForm
// To remove: 		DynaValidatorForm dyForm = null;
		// Variable declaration for message formats
		MessageFormats msgfmt = null;
		// Variable declaration for entityId
		String entityId = null;
		// Variable declaration for entityName
		String entityName = null;
		// Variable declaration for formatId
		String formatId = null;
		// Variable declaration for copyFromAddOrChange
		String copyFromAddOrChange = null;
		// Variable declaration for previousFormatId
		String previousFormatId = null;
		// Variable declaration for newFormatId
		String newFormatId = null;
		// Variable declaration for hostId
		String hostId = null;
		// Variable declaration for interfaceId
		Collection interfaceId = null;
		// Variable declaration for pathPropFile
		String pathPropFile = null;
		// variable declaration for selected entity code
		String selectedEntityCode = null;

		try {
			log.debug("Entering MessageFormatsAction.'Change' method");
			// Getting the form values
// To remove: 			dyForm = (DynaValidatorForm) form;
			// instance for message formats
			msgfmt = new MessageFormats();
			// get entity id from request
			entityId = request.getParameter("entityCode");
			// get selected entity code from request
			selectedEntityCode = request.getParameter("selectedEntityCode");
			// get format id from request
			formatId = request.getParameter("formatId").trim();
			// get copyFromAddOrChange value from request
			copyFromAddOrChange = request.getParameter("copyFromAddOrChange");
			// get previousFormatId from request
			previousFormatId = request.getParameter("previousFormatId");
			// get newFormatId from request
			newFormatId = request.getParameter("newFormatId");
			// Get entityName from request
			entityName = request.getParameter("entityName");
			// Get hostId from cache manager
			hostId = CacheManager.getInstance().getHostId();
			// set hostId in bean
			msgfmt.getId().setHostId(hostId);
			// if selected entity code is null get the entity code from request
			if (SwtUtil.isEmptyOrNull(selectedEntityCode)) {
				selectedEntityCode = request.getParameter("entityCode");
			}
			// get message format details from manager
			msgfmt = messageFormatsManager.getMsgFormatDetail(
					selectedEntityCode, hostId, formatId);
			//checks the mehtod name and set the method name in request
			if ((copyFromAddOrChange != null)
					&& copyFromAddOrChange.equalsIgnoreCase("save")) {
				request.setAttribute("methodName", "save");
				request.setAttribute("screenFieldsStatus", "false");
				msgfmt.getId().setFormatId(newFormatId);
			} else if ((copyFromAddOrChange != null)
					&& copyFromAddOrChange.equalsIgnoreCase("add")) {
				request.setAttribute("methodName", "save");
				request.setAttribute("screenFieldsStatus", "false");
				msgfmt.getId().setFormatId(newFormatId);
			} else if ((copyFromAddOrChange != null)
					&& copyFromAddOrChange.equalsIgnoreCase("update")) {
				request.setAttribute("methodName", "update");
				request.setAttribute("screenFieldsStatus", "true");
				msgfmt.getId().setFormatId(newFormatId);
			} else {
				request.setAttribute("methodName", "update");
				request.setAttribute("screenFieldsStatus", "true");
			}
			// get path form manager
			pathPropFile = messageFormatsManager.getMessageFormatPath();
			// set the path to message format bean
			msgfmt.setPath(pathPropFile);
			// get interface id from manager
			interfaceId = messageFormatsManager.getInterfaceId();
			// set interface id in request
			request.setAttribute("interfaceId", interfaceId);
			// set entity name in request
			request.setAttribute("entityName", entityName);
			// set format type in request
			request.getSession().setAttribute("formatTypeInSession",
					msgfmt.getFormatType());
			request.getSession().setAttribute("usageInSession",
					msgfmt.getUsage());
			// put messaged field status details in session
			putMessageFieldDetailsInSession(request, formatId,
					selectedEntityCode);
			// Set message format details in form
			msgfmt.getId().setEntityId(entityId);
			//set the message format in dynaform
			setMessageFormats(msgfmt);
			/* End:code modified by sandeepkumar for mantis 2092 */
		} catch (SwtException swtexp) {
			log
					.error("SwtException Catch in MessageFormatsAction.'change' method : "
							+ swtexp.getMessage());

			SwtUtil.logException(swtexp, request, "");

			return ("fail");
		} catch (Exception exp) {
			log
					.error("Exception Catch in MessageFormatsAction.'change' method : "
							+ exp.getMessage());

			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "change", MessageFormatsAction.class), request, "");

			return ("fail");
		} finally {
			log.debug("Exiting MessageFormatsAction.'Change' method");
			entityId = null;
			entityName = null;
			formatId = null;
			copyFromAddOrChange = null;
			previousFormatId = null;
			newFormatId = null;
			hostId = null;
			interfaceId = null;
			pathPropFile = null;

		}

		log.debug("Exiting MessageFormatsAction.'Change' method");

		return ("add");
	}

	/**
	 * This method is called when user selects any message format and clicks on
	 * Veiw button on Message Format screen.
	 * 
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return ActionForward
	 * @throws SwtException
	 * <AUTHOR> Tripathi has re-written this method on 6th Aug 07
	 */
	public String view()
			throws SwtException {
HttpServletRequest request = ServletActionContext.getRequest();
HttpServletResponse response = ServletActionContext.getResponse();


		// Variable Decleration for errors
		ActionErrors errors = null;
		// Variable Decleration for dyForm
// To remove: 		DynaValidatorForm dyForm = null;
		// Variable Decleration for msgfmt
		MessageFormats msgfmt = null;
		// Variable Decleration for entityName
		String entityName = null;
		// Variable Decleration for entityId
		String entityId = null;
		// Variable Decleration for formatId
		String formatId = null;
		// Variable Decleration for hostId
		String hostId = null;
		// Variable Decleration for interfaceId
		Collection interfaceId = null;
		// Variable Decleration for pathPropFile
		String pathPropFile = null;

		try {
			log.debug("Entering MessageFormatsAction.'view' method");
			errors = new ActionErrors();
// To remove: 			dyForm = (DynaValidatorForm) form;
			msgfmt = new MessageFormats();
			// get entityName
			entityName = request.getParameter("entityName");
			// get entityId
			entityId = request.getParameter("entityCode");
			// get formatId
			formatId = request.getParameter("formatId").trim();
			// get hostId
			hostId = CacheManager.getInstance().getHostId();

			msgfmt = messageFormatsManager.getMsgFormatDetail(entityId, hostId,
					formatId);

			// get the message path
			pathPropFile = messageFormatsManager.getMessageFormatPath();
			// set the message path in bean
			msgfmt.setPath(pathPropFile);
			// get interface id
			interfaceId = messageFormatsManager.getInterfaceId();
			request.setAttribute("interfaceId", interfaceId);

			// set host id in bean
			msgfmt.getId().setHostId(hostId);
			request.setAttribute("entityName", entityName);
			request.setAttribute("screenFieldsStatus", "true");
			request.setAttribute("isViewFormat", "yes");
			request.getSession().setAttribute("formatTypeInSession",
					msgfmt.getFormatType());
			request.getSession().setAttribute("usageInSession",
					msgfmt.getUsage());
			setMessageFormats(msgfmt);
			putMessageFieldDetailsInSession(request, formatId, entityId);
		} catch (SwtException swtexp) {
			log
					.error("SwtException Catch in MessageFormatsAction.'view' method : "
							+ swtexp.getMessage());

			SwtUtil.logException(swtexp, request, "");

			return ("fail");
		} catch (Exception exp) {
			log
					.error("Exception Catch in MessageFormatsAction.'view' method : "
							+ exp.getMessage());

			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "view", MessageFormatsAction.class), request, "");

			return ("fail");
		} finally {
			log.debug("Exiting MessageFormatsAction.'Change' method");
			entityName = null;
			entityId = null;
			formatId = null;
			hostId = null;
			interfaceId = null;
			pathPropFile = null;

		}

		return ("add");
	}

	/**
	 * This method is called on the click of delete button of sweep message
	 * format screen.This method is used to delete the data from
	 * P_MESSAGE_FORMATS table. It also deletes the associated message feilds
	 * with the given format id.
	 * 
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return ActionForward
	 * @throws SwtException
	 * <AUTHOR> Tripathi has re-written this method on 6th Aug 07
	 */
	public String delete()
			throws SwtException {
HttpServletRequest request = ServletActionContext.getRequest();
HttpServletResponse response = ServletActionContext.getResponse();

		// variable declaration for dynavalidator form
// To remove: 		DynaValidatorForm dyForm = null;
		// variable declaration for message format
		MessageFormats msgfmt = null;
		// varible to hold format id
		String formatId = null;
		// varible to hold host id
		String hostId = null;
		try {
			log.debug("Entering MessageFormatsAction.'Delete' method");
			// get dyna validator form
// To remove: 			dyForm = (DynaValidatorForm) form;
			// instance for message formats
			msgfmt = new MessageFormats();
			// get format id from request
			formatId = request.getParameter("selectedFormatId");
			// get host id from request
			hostId = CacheManager.getInstance().getHostId();
			// set host id in message formats
			msgfmt.getId().setHostId(hostId);
			formatId = formatId.trim();
			// set entity id in message formats
			msgfmt.getId()
					.setEntityId(request.getParameter("selectedEntityId"));
			// set format id in message formats
			msgfmt.getId().setFormatId(formatId);
			// set message formats in dyform
			setMessageFormats(msgfmt);
			messageFormatsManager.deleteMsgFormatDetail(msgfmt);
			log.debug("Exiting MessageFormatsAction.'Delete' method");
			return display();
		} catch (SwtException swtexp) {
			log
					.error("SwtException Catch in MessageFormatsAction.'delete' method : "
							+ swtexp.getMessage());

			saveErrors(request, SwtUtil.logException(swtexp, request, ""));

			return display();
		} catch (Exception exp) {
			log
					.error("Exception Catch in MessageFormatsAction.'delete' method : "
							+ exp.getMessage());

			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "delete", MessageFormatsAction.class), request, "");

			return ("fail");
		} finally {
			// nullifying objects
			msgfmt = null;
			formatId = null;
			hostId = null;
		}
	}

	/**
	 * This method is called on click of copyForm button of Message Format Add
	 * screen. This method is used when user wants to populate the Message
	 * Format data from some other existing Message Format.
	 * 
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return ActionForward
	 * @throws SwtException
	 */
	/*
	 * Start:code modified by sandeepkumar for mantis 2095 :Allow user to copy
	 * Message format across entity
	 */
	public String copyFrom()
			throws SwtException {
HttpServletRequest request = ServletActionContext.getRequest();
HttpServletResponse response = ServletActionContext.getResponse();

		// variable to declare Form Validator
// To remove: 		DynaValidatorForm dyForm = null;
		// variable to hold message format
		MessageFormats msgFormat = null;
		// variable to hold entity id
		String entityId = null;
		// variable to hold format id
		String formatId = null;
		// variable to hold host id
		String hostId = null;
		// collection for message format id
		Collection collmsgFormatId = null;
		// variable to hold method value
		String methodValue = null;
		// variable to hold selected entity code
		String selectedEntityCode = null;
		// variable to hold new format id
		String newFormatId = null;
		try {
			log.debug("Entring MessageFormatsAction.'copyFrom' method");
			// get dyna validator form
// To remove: 			dyForm = (DynaValidatorForm) form;
			// instance for message formats
			msgFormat = new MessageFormats();
			// get the entity id from request
			entityId = request.getParameter("entityCode");
			// get the selected entity code from request
			selectedEntityCode = request.getParameter("selectedEntityCode");
			// get previous format id from request
			formatId = request.getParameter("formatId");
			// if format id is empty get the new format id from request
			if (SwtUtil.isEmptyOrNull(formatId)) {
				formatId = request.getParameter("newFormatId");
			}
			// get format id from request
			newFormatId = request.getParameter("newFormatId");
			// get host id from chache manager instance
			hostId = CacheManager.getInstance().getHostId();
			// get method value from request
			methodValue = request.getParameter("copyFromAddOrChange");
			// if selected entity code is null get the entity code from request
			if (SwtUtil.isEmptyOrNull(selectedEntityCode)) {
				selectedEntityCode = request.getParameter("entityCode");
			}
			// check the method value is equal to add or save if not update send
			// formatid as empty
			if (methodValue.equals("add") || methodValue.equals("save")) {
				formatId = SwtConstants.EMPTY_STRING;
			}
			/*
			 * Getting all MessageFormats in label value bean collection by
			 * passing entity id host id and new format id
			 */
			collmsgFormatId = messageFormatsManager.getMsgFormatId(
					selectedEntityCode, hostId, formatId);
			// set selected entity id in bean
			msgFormat.getId().setEntityId(selectedEntityCode);
			// put entity list in request
			putEntityListInReq(request);
			// set collection of message format id in request
			request.setAttribute("collmsgFormatId", collmsgFormatId);
			// set new format id in request
			request.setAttribute("newFormatId", newFormatId);
			// set previous format id in request
			request.setAttribute("previousFormatId", request
					.getParameter("formatId"));
			// set copy form add or change value in request
			request.setAttribute("copyFromAddOrChange", methodValue);
			// set entity id in request
			request.setAttribute("entityId", entityId);
			// set selected entity code in request
			request.setAttribute("selectedEntityCode", selectedEntityCode);
			// set selected entity name in request
			request.setAttribute("entityName", request
					.getParameter("entityName"));
			// set message formats in form
			setMessageFormats(msgFormat);
			log.debug("Exiting MessageFormatsAction.'copyFrom' method");
			return ("copyFrom");
			/*
			 * End:code modified by sandeepkumar for mantis 2095 :Allow user to copy
			 * Message format across entity
			 */
		} catch (SwtException swtexp) {
			log
					.error("SwtException Catch in MessageFormatsAction.'copyFrom' method : "
							+ swtexp.getMessage());

			SwtUtil.logException(swtexp, request, "");

			return ("fail");
		} catch (Exception exp) {
			log
					.error("Exception Catch in MessageFormatsAction.'copyFrom' method : "
							+ exp.getMessage());

			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "copyFrom", MessageFormatsAction.class), request, "");

			return ("fail");
		} finally {
			// nullyfiying objects
			msgFormat = null;
			entityId = null;
			formatId = null;
			hostId = null;
			methodValue = null;
			collmsgFormatId = null;
		}

	}

	/**
	 * This function is called when user selects a formatId from the dropdwonn
	 * and clicks on the copy button while adding new format using copyFormat.
	 * 
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return ActionForward
	 * @throws SwtException
	 */
	public String copy()
			throws SwtException {
HttpServletRequest request = ServletActionContext.getRequest();
HttpServletResponse response = ServletActionContext.getResponse();

		log.debug("Entering MessageFormatsAction.copy method");
		// variable declaration for dyform
// To remove: 		DynaValidatorForm dyForm = null;
		// variable declaration for message formats
		MessageFormats msgfmt = null;
		// variable to hold new format id
		String newFormatId = null;
		// variable to hold format id
		String formatId = null;
		// variable to hold method name
		String copyFromAddOrChange = null;
		// variable to hold previous format id
		String previousFormatId = null;
		// variable to hold entity id
		String entityId = null;
		// variable to hold host id
		String hostId = null;
		// variable declaration for collection
		Collection msgFormatIdColl = null;
		// variable to hold selected entity code
		String selectedEntityCode = null;
		try {
			// get the form details from dyna validator form
// To remove: 			dyForm = (DynaValidatorForm) form;
			// get message format details from form
			msgfmt = (MessageFormats) getMessageFormats();
			// get new format id from request
			newFormatId = request.getParameter("newFormatId");
			// get format id from request
			formatId = request.getParameter("copiedFormatId");
			// get method name value from request
			copyFromAddOrChange = request.getParameter("copyFromAddOrChange");
			// get previous format id from request
			previousFormatId = request.getParameter("previousFormatId");
			// get entity id from request
			entityId = request.getParameter("entityId");
			// get selected entity code from request
			selectedEntityCode = request.getParameter("selectedEntityCode");
			// get host id from cache manager
			hostId = CacheManager.getInstance().getHostId();
			// get message format details from manager
			msgfmt = messageFormatsManager.getMsgFormatDetail(
					selectedEntityCode, hostId, formatId);
			// put entity list in request
			putEntityListInReq(request);
			// set message format details in dyform
			setMessageFormats(msgfmt);
			// instance for message format id collection
			msgFormatIdColl = new ArrayList();
			// add empty values to lablel value bean
			msgFormatIdColl.add(new LabelValueBean("", ""));
			// set format id in request
			request.setAttribute("selectedFormatId", msgfmt.getId()
					.getFormatId());
			// set entity id in request
			request.setAttribute("selectedEntityId", entityId);
			// set new message format id in request
			request.setAttribute("newFormatId", newFormatId);
			// set method value in request
			request.setAttribute("copyFromAddOrChange", copyFromAddOrChange);
			// code modified by sandeep kumar for mantis 2092
			// set entity name in request
			request.setAttribute("entityName", request
					.getParameter("entityName"));
			// set message format collection in request
			request.setAttribute("collmsgFormatId", msgFormatIdColl);
			// set method name in request
			request.setAttribute("methodName", "save");
			// set previous format id in request
			request.setAttribute("previousFormatId", previousFormatId);
			// set selected entity code in request
			request.setAttribute("selectedEntityCode", selectedEntityCode);
			// set parent form refresh in request
			request.setAttribute("parentFormRefresh", "yes");
			log.debug("exiting MessageFormatsAction.'copy' method");
			return ("copyFrom");
		} catch (SwtException swtexp) {
			log
					.error("SwtException Catch in MessageFormatsAction.'copy' method : "
							+ swtexp.getMessage());

			SwtUtil.logException(swtexp, request, "");

			return ("fail");
		} catch (Exception exp) {
			log
					.error("Exception Catch in MessageFormatsAction.'copy' method : "
							+ exp.getMessage());

			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "copy", MessageFormatsAction.class), request, "");

			return ("fail");
		} finally {
			// nullifying objects
			newFormatId = null;
			formatId = null;
			copyFromAddOrChange = null;
			previousFormatId = null;
			entityId = null;
			hostId = null;
		}

	}

	/**
	 * This method is used to set the button status in request and used for
	 * enabling and disabling buttons on Message Format maintenance screen.
	 * 
	 * @param req
	 * @param addStatus
	 * @param changeStatus
	 * @param deleteStatus
	 * @param cancelStatus
	 * <AUTHOR> Tripathi has re-written this method on 6th Aug 07
	 */
	private void setButtonStatus(HttpServletRequest req, String addStatus,
			String changeStatus, String deleteStatus, String cancelStatus) {
		req.setAttribute(SwtConstants.ADD_BUT_STS, addStatus);
		req.setAttribute(SwtConstants.CHG_BUT_STS, changeStatus);
		req.setAttribute(SwtConstants.DEL_BUT_STS, deleteStatus);
		req.setAttribute(SwtConstants.CAN_BUT_STS, cancelStatus);
	}

	/**
	 * This method is used for storing message feilds into user's session. Use
	 * of this method is to associate the message feilds with message format.
	 * 
	 * @param req
	 * @param formatId
	 * @param entityId
	 * @throws SwtException
	 * <AUTHOR> Tripathi has re-written this method on 6th Aug 07
	 */
	private void putMessageFieldDetailsInSession(HttpServletRequest req,
			String formatId, String entityId) throws SwtException {
		log
				.debug("Entering MessageFormatsAction.putMessageFieldDetailsInSession method");

		MessageFieldsManager messageFieldsManager = (MessageFieldsManager) (SwtUtil
				.getBean("messageFieldsManager"));
		String hostId = CacheManager.getInstance().getHostId();
		Collection messageFieldsDetails = new ArrayList();
		messageFieldsDetails = (Collection) messageFieldsManager
				.getMsgFieldDetailList(hostId, formatId, entityId);

		if (messageFieldsDetails != null) {
			Iterator itr = messageFieldsDetails.iterator();

			while (itr.hasNext()) {
				MessageFields msgFld = (MessageFields) (itr.next());

				if ((msgFld.getFieldType() != null)
						&& msgFld.getFieldType().equals(
								SwtConstants.FIELD_TYPE_KEYWORD)) {
					msgFld.setValueKeyWord(msgFld.getValue());
				}

				if (msgFld.getSeqNo() != null) {
					msgFld.setSeqNoAsString(msgFld.getSeqNo().toString());
				}

				if (msgFld.getLineNo() != null) {
					msgFld.setLineNoAsString(msgFld.getLineNo().toString());
				}
			}
		}

		req.getSession().setAttribute("messageFieldDetailsInSession",
				messageFieldsDetails);

		Collection collMessageFieldsInitial = new ArrayList(
				messageFieldsDetails);

		String collMsgFldSize = new Integer(messageFieldsDetails.size())
				.toString();
		req.getSession().setAttribute("messageFieldDetailsInSessionSize",
				collMsgFldSize);
		req.getSession().setAttribute("messageFieldDetailsInSessionPrevious",
				collMessageFieldsInitial);
	}
	
	/**
	 * This method is used for storing message feilds into user's session. Use
	 * of this method is to associate the message feilds with message format.
	 * 
	 * @param req
	 * @param formatId
	 * @param entityId
	 * @throws SwtException
	 * <AUTHOR> Tripathi has re-written this method on 6th Aug 07
	 */
	private void putScenarioMessageFieldDetailsInSession(HttpServletRequest req,
			String formatId) throws SwtException {
		log
				.debug("Entering MessageFormatsAction.putMessageFieldDetailsInSession method");

		MessageFieldsManager messageFieldsManager = (MessageFieldsManager) (SwtUtil
				.getBean("messageFieldsManager"));
		String hostId = CacheManager.getInstance().getHostId();
		Collection messageFieldsDetails = new ArrayList();
		messageFieldsDetails = (Collection) messageFieldsManager
				.getScenarioMsgFieldDetailList( formatId);

		if (messageFieldsDetails != null) {
			Iterator itr = messageFieldsDetails.iterator();

			while (itr.hasNext()) {
				ScenarioMessageFields msgFld = (ScenarioMessageFields) (itr.next());

				if ((msgFld.getFieldType() != null)
						&& msgFld.getFieldType().equals(
								SwtConstants.FIELD_TYPE_KEYWORD)) {
					msgFld.setValueKeyWord(msgFld.getValue());
				}

				if (msgFld.getSeqNo() != null) {
					msgFld.setSeqNoAsString(msgFld.getSeqNo().toString());
				}

				if (msgFld.getLineNo() != null) {
					msgFld.setLineNoAsString(msgFld.getLineNo().toString());
				}
			}
		}

		req.getSession().setAttribute("scenarioMessageFieldDetailsInSession",
				messageFieldsDetails);

		Collection collMessageFieldsInitial = new ArrayList(
				messageFieldsDetails);

		String collMsgFldSize = new Integer(messageFieldsDetails.size())
				.toString();
		req.getSession().setAttribute("scenarioMessageFieldDetailsInSessionSize",
				collMsgFldSize);
		req.getSession().setAttribute("scenarioMessageFieldDetailsInSessionPrevious",
				collMessageFieldsInitial);
	}

	/**
	 * TODO: Provide the details.Called while updating the message format.
	 * 
	 * @param request
	 * @param msgFmt
	 * @throws SwtException
	 * <AUTHOR> Tripathi has re-written this method on 6th Aug 07
	 */
	private void updateWhenFormatTypeChanged(HttpServletRequest request,
			MessageFormats msgFmt) throws SwtException {
		log
				.debug("Entering the MessageFormatsAction.updateWhenFormatTypeChanged method");

		// First deleting all the details
		String hostId = CacheManager.getInstance().getHostId();
		String entityId = msgFmt.getId().getEntityId();
		String formatId = msgFmt.getId().getFormatId();
		MessageFieldsManager msgFldMgr = (MessageFieldsManager) (SwtUtil
				.getBean("messageFieldsManager"));

		Collection exitingMsgFldDetails = msgFldMgr.getMsgFieldDetailList(
				hostId, formatId, entityId);

		if (exitingMsgFldDetails != null) {
			Iterator itrExisting = exitingMsgFldDetails.iterator();

			while (itrExisting.hasNext()) {
				MessageFields msgFld = (MessageFields) (itrExisting.next());
				msgFldMgr.deleteMessageFieldDetail(msgFld, null);
			}
		}

		Collection messageFieldDetails = (Collection) request.getSession()
				.getAttribute("messageFieldDetailsInSession");
		int serialNoPrimitive = 1;

		if (messageFieldDetails != null) {
			MessageFields msgFld = new MessageFields();
			Iterator itr = messageFieldDetails.iterator();

			while (itr.hasNext()) {
				msgFld = (MessageFields) (itr.next());
				msgFld.getId().setHostId(hostId);
				msgFld.getId().setEntityId(entityId);
				msgFld.getId().setFormatId(formatId);

				Integer serialNoObject = new Integer(serialNoPrimitive);
				msgFld.getId().setSerialNo(serialNoObject);

				String seqNoAsString = msgFld.getSeqNoAsString();

				if ((seqNoAsString != null) && !seqNoAsString.equals("")) {
					Integer seqNoObject = new Integer(msgFld.getSeqNoAsString());
					msgFld.setSeqNo(seqNoObject);
				}

				serialNoPrimitive++;
			}
		}

		messageFormatsManager.updateMsgFormatDetails(msgFmt,
				messageFieldDetails, null, null);
		log
				.debug("Exiting the MessageFormatsAction.updateWhenFormatTypeChanged method");
	}

	/**
	 * TODO: Provide the details.Called while updating the message format.
	 * 
	 * @param request
	 * @param msgFmt
	 * @throws SwtException
	 * <AUTHOR> Tripathi has re-written this method on 6th Aug 07
	 */
	private void updateWhenFormatTypeNotChanged(HttpServletRequest request,
			MessageFormats msgFmt) throws SwtException {
		log
				.debug("Entering the MessageFormatsAction.updateWhenFormatTypeNotChanged method");

		Collection messageFieldDetails = (Collection) request.getSession()
				.getAttribute("messageFieldDetailsInSession");
		Collection recordsToBeAdded = new ArrayList();
		Collection recordsToBeUpdated = new ArrayList();

		String hostId = CacheManager.getInstance().getHostId();
		String formatId = msgFmt.getId().getFormatId().trim();
		String entityId = msgFmt.getId().getEntityId().trim();

		if (messageFieldDetails != null) {
			Iterator itrCurrent = messageFieldDetails.iterator();

			while (itrCurrent.hasNext()) {
				MessageFields msgFld = (MessageFields) (itrCurrent.next());
				msgFld.getId().setHostId(hostId);
				msgFld.getId().setFormatId(formatId);
				msgFld.getId().setEntityId(entityId);

				if (msgFld.getId().getSerialNo() == null) {
					recordsToBeAdded.add(msgFld);
				} else {
					recordsToBeUpdated.add(msgFld);
				}
			}
		}

		Collection messageFieldDetailsPrevious = (Collection) request
				.getSession().getAttribute(
						"messageFieldDetailsInSessionPrevious");

		if (recordsToBeUpdated != null) {
			Iterator itrCurrent = recordsToBeUpdated.iterator();

			while (itrCurrent.hasNext()) {
				MessageFields msgFldCurrent = (MessageFields) (itrCurrent
						.next());

				if (messageFieldDetailsPrevious != null) {
					MessageFields temp = new MessageFields();
					Iterator itrPrevious = messageFieldDetailsPrevious
							.iterator();

					while (itrPrevious.hasNext()) {
						MessageFields msgFldPrevious = (MessageFields) (itrPrevious
								.next());
						temp = msgFldPrevious;

						if (msgFldPrevious.getId().getSerialNo().intValue() == msgFldCurrent
								.getId().getSerialNo().intValue()) {
							break;
						}
					}

					messageFieldDetailsPrevious.remove(temp);
				}
			}
		}

		MessageFieldsManager msgFldMgr = (MessageFieldsManager) (SwtUtil
				.getBean("messageFieldsManager"));
		Integer maxSerialNo = msgFldMgr.getMaxSerialNo(hostId, formatId,
				entityId);
		int maxSerialNoExisting = maxSerialNo.intValue();

		// Adding the record of message feilds to the database
		if (recordsToBeAdded != null) {
			Iterator itrAdd = recordsToBeAdded.iterator();

			while (itrAdd.hasNext()) {
				MessageFields msgFldAdd = (MessageFields) (itrAdd.next());
				msgFldAdd.getId().setSerialNo(
						new Integer(maxSerialNoExisting + 1));
				maxSerialNoExisting++;
			}
		}

		msgFmt.getId().setHostId(hostId);
		messageFormatsManager.updateMsgFormatDetails(msgFmt, recordsToBeAdded,
				recordsToBeUpdated, messageFieldDetailsPrevious);
		log
				.debug("Exiting the MessageFormatsAction.updateWhenFormatTypeNotChanged method");
	}

	/**
	 * This method is used for putting up entity list into request
	 * 
	 * @param request
	 * @throws SwtException
	 */
	/* Start:code modified by sandeepkumar for mantis 2095 */
	private void putEntityListInReq(HttpServletRequest request)
			throws SwtException {

		// To hold the current role
		String roleId = null;
		try {
			log.debug(this.getClass().getName()
					+ " - [putEntityListInReq] - Entering");
			// get the role id for current user
			roleId = ((CommonDataManager) request.getSession().getAttribute(
					SwtConstants.CDM_BEAN)).getUser().getRoleId();
			// get the collection of entities which having full or view access for given
			// role id and set the collection of entities in request is used to
			// display in
			// the select box.
			request.setAttribute("entities", SwtUtil.getSwtMaintenanceCache()
					.getFullOrViewEntityAccessCollectionLVL(roleId));
			log.debug(this.getClass().getName()
					+ " - [putEntityListInReq] - Exiting");
		} catch (Exception e) {
			log.error(this.getClass().getName()
					+ " - [putEntityListInReq] - Exception -" + e.getMessage());
			throw new SwtException(e.getMessage());
		} finally {
			// nullify objects
			roleId = null;
		}
	}
	/* End:code modified by sandeepkumar for mantis 2095 */
	
	/**
	 * This method is used for putting up entity list into request
	 * 
	 * @param request
	 * @throws SwtException
	 */
	/* Start:code modified by sandeepkumar for mantis 2095 */
	private Collection putScenarioListInReq(HttpServletRequest request)
			throws SwtException {
		
		Scenario scen = null; 
		Collection retColl = new ArrayList();

		// To hold the current role
		try {
			log.debug(this.getClass().getName()
					+ " - [putEntityListInReq] - Entering");
			ScenMaintenanceManager scenMaintenanceManager = (ScenMaintenanceManager) SwtUtil.getBean("scenMaintenanceManager");
			ArrayList scenarioMaintenanceDetail = scenMaintenanceManager.getScenariosDetailList(null);
			for (int i = 0; i < scenarioMaintenanceDetail.size(); i++) {
				scen = (Scenario) scenarioMaintenanceDetail.get(i);
				LabelValueBean labelValueBean = new LabelValueBean(
						scen.getTitle(),
						scen.getId().getScenarioId());
				retColl.add(labelValueBean);
				
			}
			
			
			// the select box.
			request.setAttribute("entities", retColl);
			log.debug(this.getClass().getName()
					+ " - [putEntityListInReq] - Exiting");
		} catch (Exception e) {
			log.error(this.getClass().getName()
					+ " - [putEntityListInReq] - Exception -" + e.getMessage());
			throw new SwtException(e.getMessage());
		} finally {
			// nullify objects
			scen = null;
		}
		return retColl;
	}
	
	/**
	 * This is called from submition of Sweep Message Formate screen.
	 * 
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return a Result of type Action forward
	 * <AUTHOR> Tripathi has re-written this method on 6th Aug 07
	 */
	public String displayScenarioFormats()
			throws SwtException {
HttpServletRequest request = ServletActionContext.getRequest();
HttpServletResponse response = ServletActionContext.getResponse();

		log.debug("entering MessageFormatsAction.'displayScenarioFormats' method");

		try {
// To remove: 			DynaValidatorForm dyForm = (DynaValidatorForm) form;
			ScenarioMessageFormats msgfmt = (ScenarioMessageFormats) getScenarioMessageFormats();
			String hostId = CacheManager.getInstance().getHostId();
			
//			Collection scenarioList = putScenarioListInReq(request);
			
			String method = (String) request.getSession().getAttribute("methodName");
//			String scenarioId = msgfmt.getId().getScenarioId();
            String from = (String) request.getParameter("from");
            
//			if(SwtUtil.isEmptyOrNull(from) || !from.equals("eventsSub")) {
//				if (!((scenarioId != null) && (scenarioId.trim().length() >= 0))) {
//					if(scenarioList != null && scenarioList.size() > 0) {
//						LabelValueBean bean = (LabelValueBean) (scenarioList.iterator().next());
//						scenarioId = bean.getValue();
//						
//					}
//						
//					msgfmt.getId().setScenarioId(scenarioId);
//				}
//			}else {
//				scenarioId =(String) request.getParameter("scenarioId");
//				if(scenarioId != null) {
//					request.setAttribute("scenarioId", scenarioId);
//				}
//				
//			}
			request.setAttribute("from", from);
			
			

			// Fetching the data from P_Message_Format table and putting it to
			// request object
			Collection collMsgFmts = messageFormatsManager
					.getScenarioMsgFormatDetailList( "All");
			
			
//			if(!SwtUtil.isEmptyOrNull(from) && from.equals("eventsSub")) {
//				HashMap<ScenarioMessageFormats,Collection<MessageFields>> scenarioMessageFormatsInSession = (HashMap<ScenarioMessageFormats, Collection<MessageFields>>) request.getSession().getAttribute("scenarioMessageFormatsInSession");
//				
//				if(scenarioMessageFormatsInSession== null || scenarioMessageFormatsInSession.isEmpty()) {
//					scenarioMessageFormatsInSession = new HashMap<ScenarioMessageFormats, Collection<MessageFields>>();
//					if(collMsgFmts.size() > 0) {
//						Iterator itr = collMsgFmts.iterator();
//						while (itr.hasNext()) {
//							ScenarioMessageFormats smsgFmts = (ScenarioMessageFormats) (itr.next());
//							
//							MessageFieldsManager messageFieldsManager = (MessageFieldsManager) (SwtUtil
//									.getBean("messageFieldsManager"));
//							Collection messageFieldsDetails = new ArrayList();
//							messageFieldsDetails = (Collection) messageFieldsManager
//									.getScenarioMsgFieldDetailList( smsgFmts.getId().getFormatId());
//							if (messageFieldsDetails != null) {
//								Iterator itrr = messageFieldsDetails.iterator();
//
//								while (itrr.hasNext()) {
//									ScenarioMessageFields msgFld = (ScenarioMessageFields) (itrr.next());
//
//									if ((msgFld.getFieldType() != null)
//											&& msgFld.getFieldType().equals(
//													SwtConstants.FIELD_TYPE_KEYWORD)) {
//										msgFld.setValueKeyWord(msgFld.getValue());
//									}
//
//									if (msgFld.getSeqNo() != null) {
//										msgFld.setSeqNoAsString(msgFld.getSeqNo().toString());
//									}
//
//									if (msgFld.getLineNo() != null) {
//										msgFld.setLineNoAsString(msgFld.getLineNo().toString());
//									}
//								}
//							}
//							scenarioMessageFormatsInSession.put(smsgFmts, messageFieldsDetails);
//						}						
//					}
//					 
//				}//else if(collMsgFmts.size() == 0 || collMsgFmts.size() < scenarioMessageFormatsInSession.keySet().size()) {
//				
//				//if("update".equalsIgnoreCase(method)) {
//					collMsgFmts = new ArrayList();
//					for (ScenarioMessageFormats key : scenarioMessageFormatsInSession.keySet()) {
//						collMsgFmts.add(key);
//					}
//				//}
//				
//				request.getSession().setAttribute("scenarioMessageFormatsInSession",scenarioMessageFormatsInSession);
//			}
			
			request.setAttribute("msgFormatDetails", collMsgFmts);
			

//			int accessInd = SwtUtil.getMenuEntityCurrGrpAccess(request,
//					entityId, null);

//			if (accessInd == 0) {
				setButtonStatus(request, SwtConstants.STR_TRUE,
						SwtConstants.STR_FALSE, SwtConstants.STR_FALSE,
						SwtConstants.STR_FALSE);
				request.setAttribute("EntityAccess",
						SwtConstants.ENTITY_FULL_ACCESS + "");
//			} else {
//				setButtonStatus(request, SwtConstants.STR_FALSE,
//						SwtConstants.STR_FALSE, SwtConstants.STR_FALSE,
//						SwtConstants.STR_FALSE);
//				request.setAttribute("EntityAccess",
//						SwtConstants.ENTITY_READ_ACCESS + "");
//				}


//				putEntityListInReq(request);
				
				log.debug("Exiting MessageFormatsAction.'displayScenarioFormats' method");
			} catch (SwtException swtexp) {
				log
						.error("SwtException Catch in MessageFormatsAction.'displayScenarioFormats' method : "
								+ swtexp.getMessage());

				SwtUtil.logException(swtexp, request, "");

				return ("fail");
			} catch (Exception exp) {
			log
					.error("Exception Catch in MessageFormatsAction.'displayScenarioFormats' method : "
							+ exp.getMessage());

			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "display", MessageFormatsAction.class), request, "");

			return ("fail");
		}

		return ("scenarioList");
	}

	
	
	/**
	 * This method is called when user selects any message format and clicks on
	 * the change button of message format maintenance screen.
	 * 
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return ActionForward
	 * @throws SwtException
	 */
	public String changeScenarioFormat()
			throws SwtException {
HttpServletRequest request = ServletActionContext.getRequest();
HttpServletResponse response = ServletActionContext.getResponse();

		/* Start:code modified by sandeepkumar for mantis 2092 */
		// Variable declaration for dyForm
// To remove: 		DynaValidatorForm dyForm = null;
		// Variable declaration for message formats
		ScenarioMessageFormats msgfmt = null;
		// Variable declaration for entityId
		String scenarioId = null;
		// Variable declaration for entityName
		String scenarioDesc = null;
		// Variable declaration for formatId
		String formatId = null;
		// Variable declaration for copyFromAddOrChange
		String copyFromAddOrChange = null;
		// Variable declaration for previousFormatId
		String previousFormatId = null;
		// Variable declaration for newFormatId
		String newFormatId = null;
		// Variable declaration for hostId
		String hostId = null;
		// Variable declaration for interfaceId
		Collection interfaceId = null;
		// Variable declaration for pathPropFile
		String pathPropFile = null;
		// variable declaration for selected entity code
		String selectedScenarioId = null;

		try {
			log.debug("Entering MessageFormatsAction.'changeScenarioFormat' method");
			// Getting the form values
// To remove: 			dyForm = (DynaValidatorForm) form;
			// instance for message formats
			msgfmt = new ScenarioMessageFormats();
			// get entity id from request
			scenarioId = request.getParameter("scenarioId");
			// get selected entity code from request
			selectedScenarioId = request.getParameter("selectedScenarioId");
			// get format id from request
			formatId = request.getParameter("formatId").trim();
			// get copyFromAddOrChange value from request
			copyFromAddOrChange = request.getParameter("copyFromAddOrChange");
			// get previousFormatId from request
			previousFormatId = request.getParameter("previousFormatId");
			// get newFormatId from request
			newFormatId = request.getParameter("newFormatId");
			// Get entityName from request
			scenarioDesc = request.getParameter("scenarioDesc");
			// Get hostId from cache manager
			hostId = CacheManager.getInstance().getHostId();
			// set hostId in bean
//			msgfmt.getId().setHostId(hostId);
			// if selected entity code is null get the entity code from request
			if (SwtUtil.isEmptyOrNull(selectedScenarioId)) {
				selectedScenarioId = request.getParameter("scenarioId");
			}
			// get message format details from manager
			msgfmt = messageFormatsManager.getScenarioMsgFormatDetail(formatId);
			//checks the mehtod name and set the method name in request
			if ((copyFromAddOrChange != null)
					&& copyFromAddOrChange.equalsIgnoreCase("save")) {
				request.setAttribute("methodName", "save");
				request.setAttribute("screenFieldsStatus", "false");
				msgfmt.getId().setFormatId(newFormatId);
			} else if ((copyFromAddOrChange != null)
					&& copyFromAddOrChange.equalsIgnoreCase("add")) {
				request.setAttribute("methodName", "save");
				request.setAttribute("screenFieldsStatus", "false");
				msgfmt.getId().setFormatId(newFormatId);
			} else if ((copyFromAddOrChange != null)
					&& copyFromAddOrChange.equalsIgnoreCase("update")) {
				request.setAttribute("methodName", "update");
				request.setAttribute("screenFieldsStatus", "true");
				msgfmt.getId().setFormatId(newFormatId);
			} else {
				request.setAttribute("methodName", "update");
				request.setAttribute("screenFieldsStatus", "true");
			}
			// get path form manager
			pathPropFile = messageFormatsManager.getMessageFormatPath();
			// set the path to message format bean
			msgfmt.setPath(pathPropFile);
			// get interface id from manager
			interfaceId = messageFormatsManager.getInterfaceId();
			// set interface id in request
			request.setAttribute("interfaceId", interfaceId);
			// set entity name in request
			request.setAttribute("scenarioDesc", scenarioDesc);
			// set format type in request
			request.getSession().setAttribute("formatTypeInSession",
					msgfmt.getFormatType());
			request.getSession().setAttribute("usageInSession",
					SwtConstants.FORMAT_TYPE_OTHER);
			// put messaged field status details in session
			putScenarioMessageFieldDetailsInSession(request, formatId);
			// Set message format details in form
//			msgfmt.getId().setScenarioId(scenarioId);
			//set the message format in dynaform
			setScenarioMessageFormats(msgfmt);
			/* End:code modified by sandeepkumar for mantis 2092 */
		} catch (SwtException swtexp) {
			log
					.error("SwtException Catch in MessageFormatsAction.'changeScenarioFormat' method : "
							+ swtexp.getMessage());

			SwtUtil.logException(swtexp, request, "");

			return ("fail");
		} catch (Exception exp) {
			log
					.error("Exception Catch in MessageFormatsAction.'changeScenarioFormat' method : "
							+ exp.getMessage());

			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "change", MessageFormatsAction.class), request, "");

			return ("fail");
		} finally {
			log.debug("Exiting MessageFormatsAction.'changeScenarioFormat' method");
			scenarioId = null;
			scenarioDesc = null;
			formatId = null;
			copyFromAddOrChange = null;
			previousFormatId = null;
			newFormatId = null;
			hostId = null;
			interfaceId = null;
			pathPropFile = null;

		}

		log.debug("Exiting MessageFormatsAction.'changeScenarioFormat' method");

		return ("addScenarioFormat");
	}

	/**
	 * This method is called when user selects any message format and clicks on
	 * Veiw button on Message Format screen.
	 * 
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return ActionForward
	 * @throws SwtException
	 * <AUTHOR> Tripathi has re-written this method on 6th Aug 07
	 */
	public String viewScenarioFormat()
			throws SwtException {
HttpServletRequest request = ServletActionContext.getRequest();
HttpServletResponse response = ServletActionContext.getResponse();


		// Variable Decleration for errors
		ActionErrors errors = null;
		// Variable Decleration for dyForm
// To remove: 		DynaValidatorForm dyForm = null;
		// Variable Decleration for msgfmt
		ScenarioMessageFormats msgfmt = null;
		// Variable Decleration for entityName
		String scenarioDesc = null;
		// Variable Decleration for entityId
		String scenarioId = null;
		// Variable Decleration for formatId
		String formatId = null;
		// Variable Decleration for hostId
		String hostId = null;
		// Variable Decleration for interfaceId
		Collection interfaceId = null;
		// Variable Decleration for pathPropFile
		String pathPropFile = null;

		try {
			log.debug("Entering MessageFormatsAction.'viewScenarioFormat' method");
			errors = new ActionErrors();
// To remove: 			dyForm = (DynaValidatorForm) form;
			msgfmt = new ScenarioMessageFormats();
			// get entityName
			scenarioDesc = request.getParameter("scenarioDesc");
			// get entityId
			scenarioId = request.getParameter("scenarioId");
			// get formatId
			formatId = request.getParameter("formatId").trim();
			// get hostId
			hostId = CacheManager.getInstance().getHostId();

			msgfmt = messageFormatsManager.getScenarioMsgFormatDetail(formatId);

			// get the message path
			pathPropFile = messageFormatsManager.getMessageFormatPath();
			// set the message path in bean
			msgfmt.setPath(pathPropFile);
			// get interface id
			interfaceId = messageFormatsManager.getInterfaceId();
			request.setAttribute("interfaceId", interfaceId);

			// set host id in bean
//			msgfmt.getId().setHostId(hostId);
			request.setAttribute("scenarioDesc", scenarioDesc);
			request.setAttribute("screenFieldsStatus", "true");
			request.setAttribute("isViewFormat", "yes");
			request.getSession().setAttribute("formatTypeInSession",
					msgfmt.getFormatType());
			request.getSession().setAttribute("usageInSession",
					SwtConstants.FORMAT_TYPE_OTHER);
			setScenarioMessageFormats(msgfmt);
			putScenarioMessageFieldDetailsInSession(request, formatId);
		} catch (SwtException swtexp) {
			log
					.error("SwtException Catch in MessageFormatsAction.'viewScenarioFormat' method : "
							+ swtexp.getMessage());

			SwtUtil.logException(swtexp, request, "");

			return ("fail");
		} catch (Exception exp) {
			log
					.error("Exception Catch in MessageFormatsAction.'viewScenarioFormat' method : "
							+ exp.getMessage());

			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "view", MessageFormatsAction.class), request, "");

			return ("fail");
		} finally {
			log.debug("Exiting MessageFormatsAction.'viewScenarioFormat' method");
			scenarioDesc = null;
			scenarioId = null;
			formatId = null;
			hostId = null;
			interfaceId = null;
			pathPropFile = null;

		}

		return ("addScenarioFormat");
	}
	
	/**
	 * This method is called from click on 'Add' button of Sweep Message Formate
	 * screen.
	 * 
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return ActionForward
	 * @throws SwtException
	 * <AUTHOR> Tripathi has re-written this method on 6th Aug 07
	 */
	public String addScenarioFormat()
			throws SwtException {
HttpServletRequest request = ServletActionContext.getRequest();
HttpServletResponse response = ServletActionContext.getResponse();


		// Variable Declaration for dyForm
// To remove: 		DynaValidatorForm dyForm = null;
		// Variable Declaration for interfaceId
		Collection interfaceId = null;
		// Variable Declaration for msgfmt
		ScenarioMessageFormats msgfmt = null;
		// Variable Declaration for hostId
		String hostId = null;
		// Variable Declaration for entityId
		String scenarioId = null;
		// Variable Declaration for entityName
		String scenarioDesc = null;
		// Variable Declaration for pathPropFile
		String pathPropFile = null;

		try {
			log.debug("entering MessageFormatsAction.'addScenarioFormat' method");
// To remove: 			dyForm = (DynaValidatorForm) form;
			msgfmt = (ScenarioMessageFormats) getScenarioMessageFormats();
			msgfmt.setUsage(SwtConstants.FORMAT_TYPE_OTHER);
			// getting hostId
			hostId = CacheManager.getInstance().getHostId();
			// Getting Entity Id
//			scenarioId = request.getParameter("scenarioId");
			// Getting Entity Name
//			scenarioDesc = request.getParameter("scenarioDesc");

			// Getting path from SwtCommon.prop file
			// Getting message Path
			pathPropFile = messageFormatsManager.getMessageFormatPath();
			// Getting interface Id
			interfaceId = messageFormatsManager.getInterfaceId();
			request.setAttribute("interfaceId", interfaceId);
			// Set hostId in bean
//			msgfmt.getId().setHostId(hostId);
			// Set entity Id in bean
//			msgfmt.getId().setScenarioId(scenarioId);
			// Set Message path in bean
			msgfmt.setPath(pathPropFile);
			setScenarioMessageFormats(msgfmt);
			String from = (String) request.getParameter("from");
			request.setAttribute("from", from);
			putEntityListInReq(request);
//			request.setAttribute("scenarioDesc", scenarioDesc);
			request.setAttribute("methodName", "save");
			request.getSession().setAttribute("formatTypeInSession",
					SwtConstants.FORMAT_TYPE_FIXED);
			request.getSession().setAttribute("usageInSession",
					SwtConstants.FORMAT_TYPE_OTHER);
			request.getSession().setAttribute("scenarioMessageFieldDetailsInSession",
					null);
			request.getSession().setAttribute(
					"scenarioMessageFieldDetailsInSessionSize", "0");
			
			
		} catch (SwtException swtexp) {
			log
					.error("SwtException Catch in MessageFormatsAction.'addScenarioFormat' method : "
							+ swtexp.getMessage());

			SwtUtil.logException(swtexp, request, "");

			return ("fail");
		} catch (Exception exp) {
			log.error("Exception Catch in MessageFormatsAction.'add' method : "
					+ exp.getMessage());

			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "add", MessageFormatsAction.class), request, "");

			return ("fail");
		} finally {
			log.debug("Exiting MessageFormatsAction.'addScenarioFormat' method");
			interfaceId = null;
			hostId = null;
			scenarioId = null;
			scenarioDesc = null;
			pathPropFile = null;

		}

		return ("addScenarioFormat");
	}
	
	/**
	 * This method is called while saving the message format details into
	 * database.
	 * 
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return ActionForward
	 * @throws SwtException
	 */
	public String saveScenario()
			throws SwtException {
HttpServletRequest request = ServletActionContext.getRequest();
HttpServletResponse response = ServletActionContext.getResponse();

		// Variable Declaration for errors
		ActionMessages errors = null;
		// Variable Declaration for dyForm
// To remove: 		DynaValidatorForm dyForm = null;
		// Variable Declaration for msgfmt
		ScenarioMessageFormats msgFormat = null;
		// Variable Declaration for currentFormatType
		String currentFormatType = null;
		// Variable Declaration for seqNoAsString
		String seqNoAsString = null;
		// Variable Declaration for formatId
		String formatId = null;
		// Variable Declaration for entityId
		String scenarioId = null;
		// Variable Declaration for hostId
		String hostId = null;
		// Variable Declaration for collMsgFlds
		Collection collMsgFlds = null;
		// Variable Declaration for messageFieldDetails
		Collection messageFieldDetails = null;
		// Variable Declaration for interfaceId
		Collection interfaceId = null;
		// Variable Declaration for itr
		Iterator itrMessageFields = null;
		// Variable Declaration for serialNoPrimitive
		int serialNoPrimitive = 0;
		// Variable Declaration for msgFldExisting
		ScenarioMessageFields msgFldExisting = null;
		// Variable Declaration for msgFld
		ScenarioMessageFields msgFld = null;
		// Variable Declaration for serialNoObject
		Integer serialNoObject = null;
		// Variable Declaration for seqNoObject
		Integer seqNoObject = null;
		// Variable Declaration for lineNoAsString
		String lineNoAsString = null;
		try {
			log.debug(this.getClass().getName() + " - [save] - " + "Entering");
			// instance for action messages
			errors = new ActionMessages();
			// Getting the form values
// To remove: 			dyForm = (DynaValidatorForm) form;
			// instance for message format
			msgFormat = new ScenarioMessageFormats();
			// get message format values from form
			msgFormat = (ScenarioMessageFormats) getScenarioMessageFormats();
			/* Getting the format id and entity id using bean class */
			formatId = msgFormat.getId().getFormatId().trim();
//			scenarioId = msgFormat.getId().getScenarioId().trim();
			/* setting the host from Swtutil fle */
			hostId = SwtUtil.getCurrentHostId();
			/* Setting format id,entity id and host id using bean class */
//			msgFormat.getId().setScenarioId(scenarioId);
			msgFormat.getId().setFormatId(formatId);
//			msgFormat.getId().setHostId(hostId);
			messageFieldDetails = (Collection) request.getSession()
					.getAttribute("scenarioMessageFieldDetailsInSession");
			collMsgFlds = messageFieldDetails;
			/* Condition to check the collection value */
			if (collMsgFlds != null) {
				itrMessageFields = collMsgFlds.iterator();
				/*
				 * Loop to iterate the records based on the message type save
				 * the records in DB
				 */
				while (itrMessageFields.hasNext()) {
					msgFldExisting = (ScenarioMessageFields) (itrMessageFields.next());
					if ((msgFldExisting.getLineNoAsString() != null)
							&& !(msgFldExisting.getLineNoAsString().equals(""))) {
						if (msgFldExisting.getEndPos() == null
								|| msgFldExisting.getEndPos().equals("0")
								|| msgFldExisting.getEndPos().equals(""))
							currentFormatType = SwtConstants.FORMAT_TYPE_TAGGED_VARIABLE;
						else
							currentFormatType = SwtConstants.FORMAT_TYPE_TAGGED;
						break;
						/* Condition execute if the message type is FIXED */
					} else {
						if ((msgFldExisting.getStartPos() != null)
								&& !(msgFldExisting.getStartPos().equals(""))) {
							currentFormatType = SwtConstants.FORMAT_TYPE_FIXED;

							break;
						} else {
							/* Condition execute if the message type is DELMITED */
							currentFormatType = SwtConstants.FORMAT_TYPE_DELIMITED;

							break;
						}
					}
				}
			}
			if (!msgFormat.getFormatType().equals(currentFormatType)) {
				request.getSession().setAttribute(
						"scenarioMessageFieldDetailsInSession", null);
			}

			serialNoPrimitive = 1;

			if (messageFieldDetails != null) {
				msgFld = new ScenarioMessageFields();
				itrMessageFields = messageFieldDetails.iterator();

				while (itrMessageFields.hasNext()) {
					msgFld = (ScenarioMessageFields) (itrMessageFields.next());
//					msgFld.getId().setHostId(hostId);
//					msgFld.getId().setScenarioId(scenarioId);
					msgFld.getId().setFormatId(formatId);

					serialNoObject = new Integer(serialNoPrimitive);
					msgFld.getId().setSerialNo(serialNoObject);
					/* Getting and setting the sequence number using bean class */
					seqNoAsString = msgFld.getSeqNoAsString();

					if ((seqNoAsString != null) && !seqNoAsString.equals("")) {
						seqNoAsString = seqNoAsString.trim();

						seqNoObject = new Integer(msgFld.getSeqNoAsString());
						msgFld.setSeqNo(seqNoObject);
					}
					/* Getting and setting the line number using bean class */
					if ((msgFld.getLineNoAsString() != null)
							&& !(msgFld.getLineNoAsString().equals(""))) {
						lineNoAsString = msgFld.getLineNoAsString().trim();
						msgFld.setLineNo(new Integer(lineNoAsString));
					}

					serialNoPrimitive++;
				}
			}
			//code modified by sandeepkumar for mantis 2092
			//put format type in request
			request.getSession().setAttribute("formatTypeInSession",
					msgFormat.getFormatType());
			
			request.getSession().setAttribute("usageInSession",
					SwtConstants.FORMAT_TYPE_OTHER);
			//we need to add hashmap to save message format in session: <msgformatid/ object(msgformat,messagefiledsdetails)>
			/* Save the message format details by calling manager class */
			String from = (String) request.getParameter("from");
			msgFormat.setUsage("O");
//			if(!"eventsSub".equalsIgnoreCase(from)) {
				messageFormatsManager.saveScenarioMsgFormatDetails(msgFormat, messageFieldDetails);
//			}else {
//				HashMap<ScenarioMessageFormats, Collection> scenarioMessageFormatsInSession = (HashMap<ScenarioMessageFormats, Collection>) request.getSession().getAttribute("scenarioMessageFormatsInSession");
//				scenarioMessageFormatsInSession.put(msgFormat, messageFieldDetails);
//				request.getSession().setAttribute("scenarioMessageFormatsInSession",scenarioMessageFormatsInSession);
//			}
			
			
			log.debug(this.getClass().getName() + " - [save] - " + "Exiiting");
			request.setAttribute("parentFormRefresh", "yes");
		} catch (Exception exp) {
			exp.printStackTrace();
			log.error(this.getClass().getName()
					+ " - Exception Catched in [save] method - "
					+ exp.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "save", MessageFormatsAction.class), request, "");

			return ("fail");
		} finally {

			currentFormatType = null;
			formatId = null;
			scenarioId = null;
			hostId = null;
			messageFieldDetails = null;
			interfaceId = null;
			msgFldExisting = null;
			serialNoObject = null;
			lineNoAsString = null;
			request.setAttribute("methodName", "add");
			request.setAttribute("screenFieldsStatus", "");
			request.setAttribute("entityName", request
					.getParameter("entityName"));
			interfaceId = messageFormatsManager.getInterfaceId();
			request.setAttribute("interfaceId", interfaceId);

		}
		return ("addScenarioFormat");
	}

	/**
	 * This method is called while updating the message format details into
	 * database.
	 * 
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return ActionForward
	 * @throws SwtException
	 * <AUTHOR> Tripathi has re-written this method on 6th Aug 07
	 */
	public String updateScenario()
			throws SwtException {
HttpServletRequest request = ServletActionContext.getRequest();
HttpServletResponse response = ServletActionContext.getResponse();


		// variable Decleration for dyForm
// To remove: 		DynaValidatorForm dyForm = null;
		// variable Decleration for errors
		ActionErrors errors = null;
		// variable Decleration for msgFmtDataBase
		ScenarioMessageFormats msgFmtDataBase = null;
		// variable Decleration for currentFormatType
		String currentFormatType = null;
		// variable Decleration for interfaceId
		Collection interfaceId = null;
		// variable Decleration for msgfmt
		ScenarioMessageFormats msgfmt = null;
		// variable Decleration for hostId
		String hostId = null;
		// variable Decleration for formatId
		String formatId = null;
		// variable Decleration for entityId
		String scenarioId = null;

		// variable Decleration for collMsgFlds
		Collection collMsgFlds = null;
		// variable Decleration for itr
		Iterator itrMessageFields = null;
		// variable Decleration for msgFldExisting
		ScenarioMessageFields msgFldExisting = null;
		//Collection msgFieldDetails = null;
		// Variable Declaration for serialNoPrimitive
		//int serialNoPrimitive = 0;
		// Variable Declaration for msgFld
		ScenarioMessageFields msgFld = null;
		// Variable Declaration for serialNoObject
		Integer serialNoObject = null;
		// Variable Declaration for seqNoObject
		Integer seqNoObject = null;
		// Variable Declaration for lineNoAsString
		String lineNoAsString = null;
		// Variable Declaration for seqNoAsString
		String seqNoAsString = null;
		HashMap<ScenarioMessageFormats, Collection> scenarioMessageFormatsInSessionNew = null;
		try {			
			log.debug("Entering MessageFormatsAction.'update' method");
// To remove: 			dyForm = (DynaValidatorForm) form;
			errors = new ActionErrors();
			msgfmt = (ScenarioMessageFormats) getScenarioMessageFormats();
			// Get host Id
			hostId = CacheManager.getInstance().getHostId();
			// Get format Id
			formatId = msgfmt.getId().getFormatId().trim();
			// Get Entity Id
//			scenarioId = msgfmt.getId().getScenarioId().trim();

			// set host Id in bean
//			msgfmt.getId().setHostId(hostId);
			/*msgFieldDetails = (Collection) request.getSession()
					.getAttribute("scenarioMessageFieldDetailsInSession");*/
			collMsgFlds = (Collection) request.getSession().getAttribute(
					"scenarioMessageFieldDetailsInSession");

			if ((collMsgFlds != null) && (collMsgFlds.size() > 0)) {
				itrMessageFields = collMsgFlds.iterator();

				while (itrMessageFields.hasNext()) {
					msgFldExisting = (ScenarioMessageFields) (itrMessageFields.next());
					if ((msgFldExisting.getLineNoAsString() != null)
							&& !(msgFldExisting.getLineNoAsString().equals(""))) {
						if (msgFldExisting.getEndPos() == null
								|| msgFldExisting.getEndPos().equals("0")
								|| msgFldExisting.getEndPos().equals(""))
							currentFormatType = SwtConstants.FORMAT_TYPE_TAGGED_VARIABLE;
						else
							currentFormatType = SwtConstants.FORMAT_TYPE_TAGGED;
						break;

					} else {
						if ((msgFldExisting.getStartPos() != null)
								&& !(msgFldExisting.getStartPos().equals(""))) {
							currentFormatType = SwtConstants.FORMAT_TYPE_FIXED;

							break;
						} else {
							currentFormatType = SwtConstants.FORMAT_TYPE_DELIMITED;

							break;
						}
					}
				}
			}

			if (!msgfmt.getFormatType().equals(currentFormatType)) {
				request.getSession().setAttribute(
						"scenarioMessageFieldDetailsInSession", null);
			}

			msgFmtDataBase = messageFormatsManager.getScenarioMsgFormatDetail(formatId);

			/*serialNoPrimitive = 1;

			if (msgFieldDetails != null) {
				msgFld = new ScenarioMessageFields();
				itrMessageFields = msgFieldDetails.iterator();

				while (itrMessageFields.hasNext()) {
					msgFld = (ScenarioMessageFields) (itrMessageFields.next());
//					msgFld.getId().setHostId(hostId);
//					msgFld.getId().setScenarioId(scenarioId);
					msgFld.getId().setFormatId(formatId);

					serialNoObject = new Integer(serialNoPrimitive);
					//msgFld.getId().setSerialNo(serialNoObject);
					/* Getting and setting the sequence number using bean class */
					/*seqNoAsString = msgFld.getSeqNoAsString();

					if ((seqNoAsString != null) && !seqNoAsString.equals("")) {
						seqNoAsString = seqNoAsString.trim();

						seqNoObject = new Integer(msgFld.getSeqNoAsString());
						msgFld.setSeqNo(seqNoObject);
					}
					/* Getting and setting the line number using bean class */
					/* ((msgFld.getLineNoAsString() != null)
							&& !(msgFld.getLineNoAsString().equals(""))) {
						lineNoAsString = msgFld.getLineNoAsString().trim();
						msgFld.setLineNo(new Integer(lineNoAsString));
					}

					serialNoPrimitive++;
				}
			}
			String from = "eventsSub";//(String) request.getAttribute("from");*/
			
//			if(!from.equals("eventsSub")) {
				if (msgFmtDataBase.getFormatType().equals(msgfmt.getFormatType())) {
				updateScenarioWhenFormatTypeNotChanged(request, msgfmt);
			} else {
				updateScenarioWhenFormatTypeChanged(request, msgfmt);
			}
//			}else {
//				HashMap<ScenarioMessageFormats, Collection> scenarioMessageFormatsInSession = (HashMap<ScenarioMessageFormats, Collection>) request.getSession().getAttribute("scenarioMessageFormatsInSession");
//
//				scenarioMessageFormatsInSessionNew = new HashMap<ScenarioMessageFormats, Collection>();
//				Collection messageFieldDetails = null;
//				ScenarioMessageFormats msgFormat = null;
//	
//				for (Entry<ScenarioMessageFormats, Collection> entry : scenarioMessageFormatsInSession.entrySet()) {
//					msgFormat = entry.getKey();
//					messageFieldDetails = entry.getValue();
//					if(!formatId.equalsIgnoreCase(msgFormat.getId().getFormatId())){
//						scenarioMessageFormatsInSessionNew.put(msgFormat, messageFieldDetails);
//					}		
//				}
//				
//				scenarioMessageFormatsInSessionNew.put(msgfmt, collMsgFlds);
//				request.getSession().setAttribute("scenarioMessageFormatsInSession",scenarioMessageFormatsInSessionNew);
//			}
			request.getSession().setAttribute("methodName", "update");
			request.setAttribute("parentFormRefresh", "yes");
		} catch (SwtException swtexp) {
			log
					.error("SwtException Catch in MessageFormatsAction.'update' method : "
							+ swtexp.getMessage());

			saveErrors(request, SwtUtil.logException(swtexp, request, ""));

			return ("addScenarioFormat");
		} catch (Exception exp) {
			log
					.error("Exception Catch in MessageFormatsAction.'update' method : "
							+ exp.getMessage());

			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "update", MessageFormatsAction.class), request, "");

			return ("fail");
		} finally {
			log.debug("Exiting MessageFormatsAction.'update' method");
			errors = null;
			msgFmtDataBase = null;
			currentFormatType = null;
			interfaceId = null;
			hostId = null;
			formatId = null;
			scenarioId = null;
			collMsgFlds = null;
			msgFldExisting = null;

			request.setAttribute("methodName", "update");
			request.setAttribute("screenFieldsStatus", "true");

			interfaceId = messageFormatsManager.getInterfaceId();
			request.setAttribute("interfaceId", interfaceId);

			putEntityListInReq(request);
		}

		log.debug("Exiting MessageFormatsAction.'update' method");

		return ("addScenarioFormat");
	}

	
	/**
	 * TODO: Provide the details.Called while updating the message format.
	 * 
	 * @param request
	 * @param msgFmt
	 * @throws SwtException
	 * <AUTHOR> Tripathi has re-written this method on 6th Aug 07
	 */
	private void updateScenarioWhenFormatTypeChanged(HttpServletRequest request,
			ScenarioMessageFormats msgFmt) throws SwtException {
		log
				.debug("Entering the MessageFormatsAction.updateWhenFormatTypeChanged method");

		// First deleting all the details
//		String hostId = CacheManager.getInstance().getHostId();
//		String scenarioId = msgFmt.getId().getScenarioId();
		String formatId = msgFmt.getId().getFormatId();
		MessageFieldsManager msgFldMgr = (MessageFieldsManager) (SwtUtil
				.getBean("messageFieldsManager"));

		Collection exitingMsgFldDetails = msgFldMgr.getScenarioMsgFieldDetailList(
				 formatId);

		if (exitingMsgFldDetails != null) {
			Iterator itrExisting = exitingMsgFldDetails.iterator();

			while (itrExisting.hasNext()) {
				ScenarioMessageFields msgFld = (ScenarioMessageFields) (itrExisting.next());
				msgFldMgr.deleteScenarioMessageFieldDetail(msgFld, null);
			}
		}

		Collection messageFieldDetails = (Collection) request.getSession()
				.getAttribute("scenarioMessageFieldDetailsInSession");
		int serialNoPrimitive = 1;

		if (messageFieldDetails != null) {
			ScenarioMessageFields msgFld = new ScenarioMessageFields();
			Iterator itr = messageFieldDetails.iterator();

			while (itr.hasNext()) {
				msgFld = (ScenarioMessageFields) (itr.next());
//				msgFld.getId().setHostId(hostId);
//				msgFld.getId().setScenarioId(scenarioId);
				msgFld.getId().setFormatId(formatId);

				Integer serialNoObject = new Integer(serialNoPrimitive);
				msgFld.getId().setSerialNo(serialNoObject);

				String seqNoAsString = msgFld.getSeqNoAsString();

				if ((seqNoAsString != null) && !seqNoAsString.equals("")) {
					Integer seqNoObject = new Integer(msgFld.getSeqNoAsString());
					msgFld.setSeqNo(seqNoObject);
				}

				serialNoPrimitive++;
			}
		}

		messageFormatsManager.updateScenarioMsgFormatDetails(msgFmt,
				messageFieldDetails, null, null);
		log
				.debug("Exiting the MessageFormatsAction.updateWhenFormatTypeChanged method");
	}

	/**
	 * TODO: Provide the details.Called while updating the message format.
	 * 
	 * @param request
	 * @param msgFmt
	 * @throws SwtException
	 * <AUTHOR> Tripathi has re-written this method on 6th Aug 07
	 */
	private void updateScenarioWhenFormatTypeNotChanged(HttpServletRequest request,
			ScenarioMessageFormats msgFmt) throws SwtException {
		log
				.debug("Entering the MessageFormatsAction.updateWhenFormatTypeNotChanged method");

		Collection messageFieldDetails = (Collection) request.getSession()
				.getAttribute("scenarioMessageFieldDetailsInSession");
		Collection recordsToBeAdded = new ArrayList();
		Collection recordsToBeUpdated = new ArrayList();

//		String hostId = CacheManager.getInstance().getHostId();
		String formatId = msgFmt.getId().getFormatId().trim();
//		String scenenarioId = msgFmt.getId().getScenarioId().trim();

		if (messageFieldDetails != null) {
			Iterator itrCurrent = messageFieldDetails.iterator();

			while (itrCurrent.hasNext()) {
				ScenarioMessageFields msgFld = (ScenarioMessageFields) (itrCurrent.next());
//				msgFld.getId().setHostId(hostId);
				msgFld.getId().setFormatId(formatId);
//				msgFld.getId().setScenarioId(scenenarioId);

				if (msgFld.getId().getSerialNo() == null) {
					recordsToBeAdded.add(msgFld);
				} else {
					recordsToBeUpdated.add(msgFld);
				}
			}
		}

		Collection messageFieldDetailsPrevious = (Collection) request
				.getSession().getAttribute(
						"scenarioMessageFieldDetailsInSessionPrevious");

		if (recordsToBeUpdated != null) {
			Iterator itrCurrent = recordsToBeUpdated.iterator();

			while (itrCurrent.hasNext()) {
				ScenarioMessageFields msgFldCurrent = (ScenarioMessageFields) (itrCurrent
						.next());

				if (messageFieldDetailsPrevious != null) {
					ScenarioMessageFields temp = new ScenarioMessageFields();
					Iterator itrPrevious = messageFieldDetailsPrevious
							.iterator();

					while (itrPrevious.hasNext()) {
						ScenarioMessageFields msgFldPrevious = (ScenarioMessageFields) (itrPrevious
								.next());
						temp = msgFldPrevious;

						if (msgFldPrevious.getId().getSerialNo().intValue() == msgFldCurrent
								.getId().getSerialNo().intValue()) {
							break;
						}
					}

					messageFieldDetailsPrevious.remove(temp);
				}
			}
		}

		MessageFieldsManager msgFldMgr = (MessageFieldsManager) (SwtUtil
				.getBean("messageFieldsManager"));
		Integer maxSerialNo = msgFldMgr.getScenarioMaxSerialNo( formatId);
		int maxSerialNoExisting = maxSerialNo.intValue();

		// Adding the record of message feilds to the database
		if (recordsToBeAdded != null) {
			Iterator itrAdd = recordsToBeAdded.iterator();

			while (itrAdd.hasNext()) {
				ScenarioMessageFields msgFldAdd = (ScenarioMessageFields) (itrAdd.next());
				msgFldAdd.getId().setSerialNo(
						new Integer(maxSerialNoExisting + 1));
				maxSerialNoExisting++;
			}
		}

//		msgFmt.getId().setHostId(hostId);
		messageFormatsManager.updateScenarioMsgFormatDetails(msgFmt, recordsToBeAdded,
				recordsToBeUpdated, messageFieldDetailsPrevious);
		log
				.debug("Exiting the MessageFormatsAction.updateWhenFormatTypeNotChanged method");
	}
	
	
	/**
	 * This method is called on click of copyForm button of Message Format Add
	 * screen. This method is used when user wants to populate the Message
	 * Format data from some other existing Message Format.
	 * 
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return ActionForward
	 * @throws SwtException
	 */
	/*
	 * Start:code modified by sandeepkumar for mantis 2095 :Allow user to copy
	 * Message format across entity
	 */
	public String copyFromScenario()
			throws SwtException {
HttpServletRequest request = ServletActionContext.getRequest();
HttpServletResponse response = ServletActionContext.getResponse();

		// variable to declare Form Validator
// To remove: 		DynaValidatorForm dyForm = null;
		// variable to hold message format
		ScenarioMessageFormats msgFormat = null;
		// variable to hold entity id
		String scenarioId = null;
		// variable to hold format id
		String formatId = null;
		// variable to hold host id
		String hostId = null;
		// collection for message format id
		Collection collmsgFormatId = null;
		// variable to hold method value
		String methodValue = null;
		// variable to hold selected entity code
//		String selectedScenarioId = null;
		// variable to hold new format id
		String newFormatId = null;
		try {
			log.debug("Entring MessageFormatsAction.'copyFrom' method");
			// get dyna validator form
// To remove: 			dyForm = (DynaValidatorForm) form;
			// instance for message formats
			msgFormat = new ScenarioMessageFormats();
			// get the entity id from request
//			scenarioId = request.getParameter("scenarioId");
//			// get the selected entity code from request
//			selectedScenarioId = request.getParameter("selectedScenarioId");
			// get previous format id from request
			formatId = request.getParameter("formatId");
			// if format id is empty get the new format id from request
			if (SwtUtil.isEmptyOrNull(formatId)) {
				formatId = request.getParameter("newFormatId");
			}
			// get format id from request
			newFormatId = request.getParameter("newFormatId");
			// get host id from chache manager instance
			hostId = CacheManager.getInstance().getHostId();
			// get method value from request
			methodValue = request.getParameter("copyFromAddOrChange");
			// if selected entity code is null get the entity code from request
//			if (SwtUtil.isEmptyOrNull(selectedScenarioId)) {
//				selectedScenarioId = request.getParameter("scenarioId");
//			}
			// check the method value is equal to add or save if not update send
			// formatid as empty
			if (methodValue.equals("add") || methodValue.equals("save")) {
				formatId = SwtConstants.EMPTY_STRING;
			}
			/*
			 * Getting all MessageFormats in label value bean collection by
			 * passing entity id host id and new format id
			 */
			collmsgFormatId = messageFormatsManager.getScenarioMsgFormatId(formatId);
			// set selected entity id in bean
//			msgFormat.getId().setScenarioId(selectedScenarioId);
			// put entity list in request
			putScenarioListInReq(request);
			// set collection of message format id in request
			request.setAttribute("collmsgFormatId", collmsgFormatId);
			// set new format id in request
			request.setAttribute("newFormatId", newFormatId);
			// set previous format id in request
			request.setAttribute("previousFormatId", request
					.getParameter("formatId"));
			// set copy form add or change value in request
			request.setAttribute("copyFromAddOrChange", methodValue);
			// set entity id in request
//			request.setAttribute("scenarioId", scenarioId);
			// set selected entity code in request
//			request.setAttribute("selectedScenarioId", selectedScenarioId);
			// set selected entity name in request
//			request.setAttribute("scenarioDesc", request
//					.getParameter("scenarioDesc"));
			// set message formats in form
			setScenarioMessageFormats(msgFormat);
			log.debug("Exiting MessageFormatsAction.'copyFromScenario' method");
			return ("copyFromScenario");
			/*
			 * End:code modified by sandeepkumar for mantis 2095 :Allow user to copy
			 * Message format across entity
			 */
		} catch (SwtException swtexp) {
			log
					.error("SwtException Catch in MessageFormatsAction.'copyFromScenario' method : "
							+ swtexp.getMessage());

			SwtUtil.logException(swtexp, request, "");

			return ("fail");
		} catch (Exception exp) {
			log
					.error("Exception Catch in MessageFormatsAction.'copyFromScenario' method : "
							+ exp.getMessage());

			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "copyFromScenario", MessageFormatsAction.class), request, "");

			return ("fail");
		} finally {
			// nullyfiying objects
			msgFormat = null;
			scenarioId = null;
			formatId = null;
			hostId = null;
			methodValue = null;
			collmsgFormatId = null;
		}

	}

	/**
	 * This function is called when user selects a formatId from the dropdwonn
	 * and clicks on the copy button while adding new format using copyFormat.
	 * 
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return ActionForward
	 * @throws SwtException
	 */
	public String copyScenario()
			throws SwtException {
HttpServletRequest request = ServletActionContext.getRequest();
HttpServletResponse response = ServletActionContext.getResponse();

		log.debug("Entering MessageFormatsAction.copy method");
		// variable declaration for dyform
// To remove: 		DynaValidatorForm dyForm = null;
		// variable declaration for message formats
		ScenarioMessageFormats msgfmt = null;
		// variable to hold new format id
		String newFormatId = null;
		// variable to hold format id
		String formatId = null;
		// variable to hold method name
		String copyFromAddOrChange = null;
		// variable to hold previous format id
		String previousFormatId = null;
		// variable to hold entity id
		String scenarioId = null;
		// variable to hold host id
		String hostId = null;
		// variable declaration for collection
		Collection msgFormatIdColl = null;
		// variable to hold selected entity code
		String selectedScenarioId = null;
		try {
			// get the form details from dyna validator form
// To remove: 			dyForm = (DynaValidatorForm) form;
			// get message format details from form
			msgfmt = (ScenarioMessageFormats) getScenarioMessageFormats();
			// get new format id from request
			newFormatId = request.getParameter("newFormatId");
			// get format id from request
			formatId = request.getParameter("copiedFormatId");
			// get method name value from request
			copyFromAddOrChange = request.getParameter("copyFromAddOrChange");
			// get previous format id from request
			previousFormatId = request.getParameter("previousFormatId");
			// get entity id from request
//			scenarioId = request.getParameter("scenarioId");
			// get selected entity code from request
//			selectedScenarioId = request.getParameter("selectedScenarioId");
			// get host id from cache manager
			hostId = CacheManager.getInstance().getHostId();
			// get message format details from manager
			msgfmt = messageFormatsManager.getScenarioMsgFormatDetail(formatId);
			// put entity list in request
			putEntityListInReq(request);
			// set message format details in dyform
			setScenarioMessageFormats(msgfmt);
			// instance for message format id collection
			msgFormatIdColl = new ArrayList();
			// add empty values to lablel value bean
			msgFormatIdColl.add(new LabelValueBean("", ""));
			// set format id in request
			request.setAttribute("selectedFormatId", msgfmt.getId()
					.getFormatId());
			// set entity id in request
//			request.setAttribute("selectedScenarioId", scenarioId);
			// set new message format id in request
			request.setAttribute("newFormatId", newFormatId);
			// set method value in request
			request.setAttribute("copyFromAddOrChange", copyFromAddOrChange);
			// code modified by sandeep kumar for mantis 2092
			// set entity name in request
//			request.setAttribute("scenarioDesc", request
//					.getParameter("scenarioDesc"));
			// set message format collection in request
			request.setAttribute("collmsgFormatId", msgFormatIdColl);
			// set method name in request
			request.setAttribute("methodName", "save");
			// set previous format id in request
			request.setAttribute("previousFormatId", previousFormatId);
			// set selected entity code in request
//			request.setAttribute("selectedScenarioCode", selectedScenarioId);
			// set parent form refresh in request
			request.setAttribute("parentFormRefresh", "yes");
			log.debug("exiting MessageFormatsAction.'copyScenario' method");
			return ("copyFromScenario");
		} catch (SwtException swtexp) {
			log
					.error("SwtException Catch in MessageFormatsAction.'copyScenario' method : "
							+ swtexp.getMessage());

			SwtUtil.logException(swtexp, request, "");

			return ("fail");
		} catch (Exception exp) {
			log
					.error("Exception Catch in MessageFormatsAction.'copyScenario' method : "
							+ exp.getMessage());

			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "copyScenario", MessageFormatsAction.class), request, "");

			return ("fail");
		} finally {
			// nullifying objects
			newFormatId = null;
			formatId = null;
			copyFromAddOrChange = null;
			previousFormatId = null;
			scenarioId = null;
			hostId = null;
		}

	}
	
	
	/**
	 * This method is called on the click of delete button of sweep message
	 * format screen.This method is used to delete the data from
	 * P_MESSAGE_FORMATS table. It also deletes the associated message feilds
	 * with the given format id.
	 * 
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return ActionForward
	 * @throws SwtException
	 * <AUTHOR> Tripathi has re-written this method on 6th Aug 07
	 */
	public String deleteScenario()
			throws SwtException {
HttpServletRequest request = ServletActionContext.getRequest();
HttpServletResponse response = ServletActionContext.getResponse();

		// variable declaration for dynavalidator form
// To remove: 		DynaValidatorForm dyForm = null;
		// variable declaration for message format
		ScenarioMessageFormats msgfmt = null;
		// varible to hold format id
		String formatId = null;
		// varible to hold host id
		String hostId = null;
		HashMap<ScenarioMessageFormats, Collection> scenarioMessageFormatsInSessionNew = null;

		try {
			log.debug("Entering MessageFormatsAction.'deleteScenario' method");
			// get dyna validator form
// To remove: 			dyForm = (DynaValidatorForm) form;
			// instance for message formats
			msgfmt = new ScenarioMessageFormats();
			// get format id from request
			formatId = request.getParameter("selectedFormatId");
			// get host id from request
			hostId = CacheManager.getInstance().getHostId();
			// set host id in message formats
//			msgfmt.getId().setHostId(hostId);
			formatId = formatId.trim();
			// set entity id in message formats
//			msgfmt.getId()
//					.setScenarioId(request.getParameter("selectedScenarioId"));
			// set format id in message formats
			msgfmt.getId().setFormatId(formatId);
			// set message formats in dyform
			setScenarioMessageFormats(msgfmt);
			messageFormatsManager.deleteScenarioMsgFormatDetail(msgfmt);
			log.debug("Exiting MessageFormatsAction.'Delete' method");
			
			//delete selected scenario message format from session			
			HashMap<ScenarioMessageFormats, Collection> scenarioMessageFormatsInSession = (HashMap<ScenarioMessageFormats, Collection>) request.getSession().getAttribute("scenarioMessageFormatsInSession");

			scenarioMessageFormatsInSessionNew = new HashMap<ScenarioMessageFormats, Collection>();
			Collection messageFieldDetails = null;
			ScenarioMessageFormats msgFormat = null;
			if(scenarioMessageFormatsInSession != null && scenarioMessageFormatsInSession.size()>0) {
				for (Entry<ScenarioMessageFormats, Collection> entry : scenarioMessageFormatsInSession.entrySet()) {
					msgFormat = entry.getKey();
					messageFieldDetails = entry.getValue();
					if(!formatId.equalsIgnoreCase(msgFormat.getId().getFormatId())){
						scenarioMessageFormatsInSessionNew.put(msgFormat, messageFieldDetails);
					}		
				}
				
				request.getSession().setAttribute("scenarioMessageFormatsInSession",scenarioMessageFormatsInSessionNew);
			}
			return displayScenarioFormats();
		} catch (SwtException swtexp) {
			swtexp.printStackTrace();
			log
					.error("SwtException Catch in MessageFormatsAction.'deleteScenario' method : "
							+ swtexp.getMessage());

			saveErrors(request, SwtUtil.logException(swtexp, request, ""));

			return display();
		} catch (Exception exp) {
			exp.printStackTrace();
			log
					.error("Exception Catch in MessageFormatsAction.'deleteScenario' method : "
							+ exp.getMessage());

			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "deleteScenario", MessageFormatsAction.class), request, "");

			return ("fail");
		} finally {
			// nullifying objects
			msgfmt = null;
			formatId = null;
			hostId = null;
		}
	}
	
}