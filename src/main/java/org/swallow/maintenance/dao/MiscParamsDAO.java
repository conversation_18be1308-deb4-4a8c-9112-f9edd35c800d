/*
 * Created on Nov 4, 2005
 *
 * TODO To change the template for this generated file go to
 * Window - Preferences - Java - Code Style - Code Templates
 */
package org.swallow.maintenance.dao;

import org.swallow.dao.*;

import java.util.*;

/**
 * MiscParamsDAO.java
 * 
 * This is DAO class for accessing the MiscParams details.
 * 
 */
public interface MiscParamsDAO extends DAO {
	/**
	 * This method is used to get the all values from Misc params table
	 * 
	 * @param hostId
	 *            String
	 * @return Collection
	 */
	@SuppressWarnings("unchecked")
	public Collection getAllValues(String hostId);

}