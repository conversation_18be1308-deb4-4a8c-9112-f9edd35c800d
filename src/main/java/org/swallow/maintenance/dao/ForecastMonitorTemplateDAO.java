/**
 * @(#)ForecastMonitorTemplateDAO.java 1.0 24/05/11
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.maintenance.dao;

import java.util.List;

import org.swallow.exception.SwtException;
import org.swallow.maintenance.model.BookCode;
import org.swallow.maintenance.model.ForecastMonitorTemplate;
import org.swallow.maintenance.model.ForecastMonitorTemplateCol;
import org.swallow.maintenance.model.ForecastMonitorTemplateColSrc;
import org.swallow.maintenance.model.Group;
import org.swallow.maintenance.model.MetaGroup;
import org.swallow.work.model.UserTemplate;

/**
 * ForecastMonitorTemplateDAO.java
 * 
 * ForecastMonitorTemplateDAO class is used for ForecastMonitorTemplate screen
 * that will display ForecastMonitor Templates<br>
 * 
 * <AUTHOR> A
 * @date May 24, 2011
 */
public interface ForecastMonitorTemplateDAO {

	/**
	 * 
	 * Method to get Forecast monitor Templates
	 * 
	 * @param hostId
	 * @param currentUserId
	 * @return List of templates
	 */
	public List<ForecastMonitorTemplate> getForecastMonitorTemplate(
			String hostId, String currentUserId) throws SwtException;

	/**
	 * Method to delete Forecast monitor Template
	 * 
	 * @param forecastMonitorTemplate
	 * @return
	 */
	public void deleteForecastMonitorTemplate(
			ForecastMonitorTemplate forecastMonitorTemplate)
			throws SwtException;

	/**
	 * 
	 * Method to get Forecast monitor Template Columns
	 * 
	 * @param forecastMonitorTemplateCol
	 * @return List of template columns
	 */
	public List<ForecastMonitorTemplateCol> getForecastMonitorTemplateCol(
			ForecastMonitorTemplateCol forecastMonitorTemplateCol)
			throws SwtException;

	/**
	 * 
	 * Method to get Forecast monitor Template Columns
	 * 
	 * @param forecastMonitorTemplateColSrc
	 * @return List of template column sources
	 */
	public List<ForecastMonitorTemplateColSrc> getForecastMonitorTemplateColSrc(
			ForecastMonitorTemplateColSrc forecastMonitorTemplateColSrc)
			throws SwtException;

	/**
	 * Method to delete Forecast monitor Template Columns
	 * 
	 * @param forecastMonitorTemplateColList
	 * @return
	 */
	public void deleteAllForecastMonitorTemplateCol(
			List<ForecastMonitorTemplateCol> forecastMonitorTemplateColList)
			throws SwtException;

	/**
	 * Method to delete Forecast monitor Template Column Sources
	 * 
	 * @param forecastMonitorTemplateColSrcList
	 * @return
	 */
	public void deleteAllForecastMonitorTemplateColSrc(
			List<ForecastMonitorTemplateColSrc> forecastMonitorTemplateColSrcList)
			throws SwtException;

	/**
	 * 
	 * Method to get Forecast monitor Template Columns
	 * 
	 * @return List of template columns
	 */
	public List<ForecastMonitorTemplateCol> getForecastMonitorTemplateCol()
			throws SwtException;

	/**
	 * 
	 * Method to get Book Code details
	 * 
	 * @param hostId
	 * @param entityId
	 * @param fieldId
	 * @param fieldName
	 * @return List of bokk code
	 */
	public List<BookCode> getBookCollection(String hostId, String entityId,
			String fieldId, String fieldName) throws SwtException;

	/**
	 * This is used to get the group details from P_GROUP table.
	 * 
	 * @param hostId
	 * @param entityId
	 * @param fieldId
	 * @param fieldName
	 * @return List of group
	 */
	public List<Group> getGroupDetails(String hostId, String entityId,
			String fieldId, String fieldName) throws SwtException;

	/**
	 * This is used to get meta group details
	 * 
	 * @param hostId
	 * @param entityId
	 * @param fieldId
	 * @param fieldName
	 * @return List of MetaGroup
	 */
	public List<MetaGroup> getMetaGroupDetails(String hostId, String entityId,
			String fieldId, String fieldName) throws SwtException;

	/**
	 * 
	 * Method to get Forecast monitor Template Columns sources for change screen
	 * 
	 * @param forecastMonitorTemplateColSrc
	 * @return List of template column sources
	 */
	public List<ForecastMonitorTemplateColSrc> getChangeForecastMonitorTemplateColSrc(
			ForecastMonitorTemplateColSrc forecastMonitorTemplateColSrc)
			throws SwtException;

	/**
	 * 
	 * Method to get Forecast monitor Template Columns sources for change screen
	 * for subtotal
	 * 
	 * @param forecastMonitorTemplateColSrc
	 * @return List of template columns
	 */
	public List<ForecastMonitorTemplateCol> getChangeForecastMonitorTemplateColSrcForSubTotal(
			ForecastMonitorTemplateColSrc forecastMonitorTemplateColSrc)
			throws SwtException;

	/**
	 * 
	 * Method to lock template for user
	 * 
	 * @param forecastMonitorTemplate
	 * @return boolean - locked result
	 */
	public void lockTemplate(ForecastMonitorTemplate forecastMonitorTemplate)
			throws SwtException;

	/**
	 * 
	 * Method to check whether the template is locked
	 * 
	 * @param forecastMonitorTemplate
	 * @return List
	 */
	public List<ForecastMonitorTemplate> checkTemplateLocked(
			ForecastMonitorTemplate forecastMonitorTemplate)
			throws SwtException;

	/**
	 * 
	 * Method to get All locked templates for selected user id
	 * 
	 * @param lockedUserId
	 * @return List
	 */
	public List<ForecastMonitorTemplate> getAllLock(String lockedUserId)
			throws SwtException;

	/**
	 * 
	 * Method to get Forecast monitor Templates for copy from
	 * 
	 * @param hostId
	 * @param currentUserId
	 * 
	 * @return List of templates
	 */
	public List<ForecastMonitorTemplate> getTemplatesCopyFrom(String hostId,
			String currentUserId) throws SwtException;

	/**
	 * 
	 * Method to save forecast template
	 * 
	 * @param forecastMonitorTemplate
	 * @return
	 * @throws SwtException
	 */
	public void saveTemplate(ForecastMonitorTemplate forecastMonitorTemplate)
			throws SwtException;

	/**
	 * 
	 * Method to save forecast template columns
	 * 
	 * @param forecastMonitorTemplateCol
	 * @return
	 * @throws SwtException
	 */
	public void saveTemplateCol(
			ForecastMonitorTemplateCol forecastMonitorTemplateCol)
			throws SwtException;

	/**
	 * 
	 * Method to save forecast template column Sources
	 * 
	 * @param forecastMonitorTemplateColSrc
	 * @return
	 * @throws SwtException
	 */
	public void saveTemplateColSrc(
			ForecastMonitorTemplateColSrc forecastMonitorTemplateColSrc)
			throws SwtException;

	/**
	 * 
	 * Method to update forecast template
	 * 
	 * @param forecastMonitorTemplate
	 * @return
	 * @throws SwtException
	 */
	public void updateTemplate(ForecastMonitorTemplate forecastMonitorTemplate)
			throws SwtException;

	/**
	 * 
	 * Method to save forecast template col
	 * 
	 * @param forecastMonitorTemplateCol
	 * @return
	 * @throws SwtException
	 */
	public void updateTemplateCol(
			ForecastMonitorTemplateCol forecastMonitorTemplateCol)
			throws SwtException;

	/**
	 * 
	 * Method to save forecast template col Src
	 * 
	 * @param forecastMonitorTemplateColSrc
	 * @return
	 * @throws SwtException
	 */
	public void updateTemplateColSrc(
			ForecastMonitorTemplateColSrc forecastMonitorTemplateColSrc)
			throws SwtException;

	/**
	 * 
	 * Method to delete forecast template col
	 * 
	 * @param forecastMonitorTemplateCol
	 * @throws SwtException
	 */
	public void deleteTemplateCol(
			ForecastMonitorTemplateCol forecastMonitorTemplateCol)
			throws SwtException;

	/**
	 * 
	 * Method to delete forecast template column source
	 * 
	 * @param forecastMonitorTemplateColSrc
	 * @throws SwtException
	 */
	public void deleteTemplateColSrc(
			ForecastMonitorTemplateColSrc forecastMonitorTemplateColSrc)
			throws SwtException;

	/**
	 * 
	 * Method to get Forecast monitor Template Columns
	 * 
	 * @param forecastMonitorTemplateColSrc
	 * @param columnType
	 * @return List of templates
	 */
	public List<ForecastMonitorTemplateColSrc> getForecastMonitorTemplateColSrcForColumnId(
			ForecastMonitorTemplateColSrc forecastMonitorTemplateColSrc,
			String columnType) throws SwtException;

	/**
	 * 
	 * Method to get User Templates
	 * 
	 * @return List of user templates
	 */
	public List<UserTemplate> getUserTemplates(String templateId, String hostId)
			throws SwtException;

	/**
	 * Method to delete User Template list
	 * 
	 * @param userTemplateList
	 * @return
	 */
	public void deleteAllUserTemplate(List<UserTemplate> userTemplateList)
			throws SwtException;

	/**
	 * 
	 * Method to get Forecast monitor Template Columns to get column id
	 * 
	 * @param forecastMonitorTemplateCol
	 * @return List of template columns
	 */
	public List<ForecastMonitorTemplateCol> getForecastMonitorTemplateColForColumnId(
			ForecastMonitorTemplateCol forecastMonitorTemplateCol)
			throws SwtException;

	/**
	 * 
	 * Method to check Forecast monitor Template Columns sources exists
	 * 
	 * @param forecastMonitorTemplateColSrc
	 * @return List of template column sources
	 */
	public List<ForecastMonitorTemplateColSrc> checkForecastExist(
			ForecastMonitorTemplateColSrc forecastMonitorTemplateColSrc)
			throws SwtException;
}
