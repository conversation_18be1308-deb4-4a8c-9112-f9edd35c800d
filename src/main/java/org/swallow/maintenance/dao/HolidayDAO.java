/*
 * @(#)HolidayDAO.java 1.0 23/08/06
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.maintenance.dao;

import java.util.Calendar;
import java.util.Collection;
import java.util.Date;

import org.swallow.dao.DAO;
import org.swallow.exception.SwtException;
import org.swallow.maintenance.model.Holiday;

/**
 * DAO layer for Mainataing Holiday list
 * @ Modified Vivekanandan.A
 *
 */
public interface HolidayDAO extends DAO {
	/**
	 * This is used to delete selected country's holiday details from S_HOLIDAY
	 * table.
	 * 
	 * @param holiday
	 * @return
	 * @throws SwtException
	 */
	public void deleteHoliday(Holiday holiday) throws SwtException;

	/**
	 * This is used to save holidays details in S_HOLIDAY table.
	 * 
	 * @param holiday
	 * @return
	 * @throws SwtException
	 */
	public void saveHoliday(Holiday holiday) throws SwtException;

	/**
	 * This is used to fetches the Business day from database and it is used in
	 * Work Flow Monitor action.
	 * 
	 * @param entityId
	 * @param hostId
	 * @param currencyCode
	 * @throws SwtException
	 */
	public Calendar getBusinessDate(String facility, Calendar cal,
			String entityId, String hostId, String currencyCode,
			int tabPosition, String accountId) throws SwtException;

	/* Modified by Vivekanandan for Mantis 1306 on 08-03-2011 */
	/**
	 * Method to Get previous Buisness date from
	 * PKG_NON_WORKDAY.GETPREVBUSINESSDATE
	 * 
	 * @param cal
	 * @param entityId
	 * @param hostId
	 * @param currencyCode
	 * @param facility
	 * @return Calender - to return the previous buisness date
	 * @throws SwtException
	 */
	public Calendar getBusinessDateMinusOne(Calendar cal, String entityId,
			String hostId, String currencyCode, String facility)
			throws SwtException;

	/* END:Code added by Mahesh on 18-Feb-2009 for Mantis 738 */

	/**
	 * This is used to fetches the holiday's details from S_HOLIDAY table.
	 * 
	 * @param entity
	 * @param hostId
	 * @return Collection
	 * @throws SwtException
	 */
	public Collection getHolidayList(String entity, String hostId)
			throws SwtException;

	/*
	 * START : Added by Vivekanandan to check selected date is weekened for
	 * Mantis 1203 on 09-08-2010
	 */
	/**
	 * Method to check given date is weekend for Opportunity cost
	 * 
	 * @param cal
	 * @param entityId
	 * @param hostId
	 * @param currencyCode
	 * @return boolean
	 * @throws SwtException
	 */
	public boolean isWeekend(String facility, Calendar cal, String entityId,
			String hostId, String currencyCode) throws SwtException;

	/*
	 * END : Added by Vivekanandan to check selected date is weekened for Mantis
	 * 1203 on 09-08-2010
	 */

	/* Modified by Vivekanandan for Mantis 1306 on 08-03-2011 */
	/**
	 * Method to get next working day for given facility
	 * 
	 * @param hostId
	 * @param entityId
	 * @param inputDate
	 * @param currencyCode
	 * @param selectedTabIndex
	 * @param facility
	 * @return Calendar
	 * @throws SwtException
	 */
	public Date getNextBusinessDate(String hostId, String entityId,
			Date inputDate, String currencyCode, String selectedTabIndex,
			String facility) throws SwtException;
}
