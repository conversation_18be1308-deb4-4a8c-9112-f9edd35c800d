/*
 * @(#)MatchQualityDAO.java 1.0 23/08/06
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.maintenance.dao;

import java.util.Collection;

import org.swallow.dao.DAO;
import org.swallow.exception.SwtException;
import org.swallow.maintenance.model.MatchQuality;
import org.swallow.maintenance.model.QualityAction;


public interface MatchQualityDAO extends DAO {

//unnecessary code Removed by Arumugam on 03-Dec-2010 for Mantis:0001296- Misc Param to added the entityId field in table and check the entityId	
    /**
     * Collect the Match list from data base table P_MATCH_ACTION
     * @param entityId
     * @param hostId
     * @return Collection
     * @throws SwtException
     */
    public Collection getMatchList(String entityId, String hostId)
        throws SwtException;

    
    /**
     * Collect the matchQuality list from  the database table P_MATCH_ACTION
     * @param hostId
     * @param entityId
     * @param currencyCode
     * @param posLevel
     * @return MatchQuality
     * @throws SwtException
     */
    public MatchQuality getMatchQualityList(String hostId, String entityId,
        String currencyCode, Integer posLevel) throws SwtException;

    /**
     * Get the collection of parameter description from database table P_MATCH_PARAMETERS
     * @return Collection
     * @throws SwtException
     */
    public Collection getParamDescAll() throws SwtException;

    /**
     * Save the values of Quality action bean  in data base table P_MATCH_QUALITY
     * @param QualityAction
     * @return None
     * @throws SwtException
     */
    public void addMatchQuality(QualityAction matchQualityObj)
        throws SwtException;

    /**
     * Update the values of Quality action from the data base table P_MATCH_ACTION
     * @param Collection
     * @return None
     * @throws SwtException
     */
    public void updateMatchQuality(QualityAction matchQualityObj)
        throws SwtException;

    /**
     * Delete the values of Quality action from the data base table P_MATCH_ACTION
     * @param matchQuality
     * @return None
     * @throws SwtException
     */
    public void deleteMatchQuality(MatchQuality matchQuality)
        throws SwtException;

    
    /**
     * Get the collection of filterPosition level from the data base table P_MATCH_ACTION
     * @param hostId
     * @param entityId
     * @param currencyCode
     * @return Collection
     * @throws SwtException
     */
    public Collection getfilterPositionlevel(String hostId, String entityId,
        String currencyCode) throws SwtException;
}
