/*
 * @(#)GroupDAO.java 1.0 21/12/05
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.maintenance.dao;

import org.swallow.dao.DAO;

import org.swallow.exception.SwtException;

import org.swallow.maintenance.model.Group;

//import java.util.Collection;
import java.util.Collection;


public interface GroupDAO extends DAO {
    /**
     * This is used to get the group details from P_GROUP table
     * @param entityId
     * @param hostId
     * @return Collection
     * @throws SwtException
     */
    public Collection getGroupList(String entityId, String hostId, Integer grpLvlCode) throws SwtException;

    /**
     * This is used to get the meta group details from P_METAGROUP table
     * @param entityId
     * @param hostId
     * @return Collection
     * @throws SwtException
     */
    public Collection getMetaGroupList(String entityId, String hostId)
        throws SwtException;

    /**
     * This is used to save the group details in P_GROUP table
     * @param group
     * @return
     * @throws SwtException
     */
    public void saveGroupDetail(Group group) throws SwtException;

    /**
     * This is used to change the  details in P_GROUP table
     * @param group
     * @return
     * @throws SwtException
     */
    public void updateGroupDetail(Group group) throws SwtException;

    /**
     * This is used to delete the group details from P_GROUP table
     * @param group
     * @return
     * @throws SwtException
     */
    public void deleteGroupDetail(Group group) throws SwtException;
    /**
     * This is used to edit the required fields in P_GROUP table
     * @param  hostId
     * @param entityId
     * @param groupId
     * @return Group
     * @throws SwtException*/
    public Group getEditableData(String hostId, String entityId, String groupId)
        throws SwtException;
}
