/*
 * @(#)MessageFieldsDAO.java 21/12/05
 * 
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 * 
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */

package org.swallow.maintenance.dao;

import org.swallow.dao.DAO;

import org.swallow.exception.SwtException;

import org.swallow.maintenance.model.MessageFields;
import org.swallow.maintenance.model.ScenarioMessageFields;
import org.swallow.util.SystemInfo;

import java.util.Collection;



public interface MessageFieldsDAO extends DAO {
    /**
     * @param hostId
     * @param entityId
     * @param formatId
     * @return
     * @throws SwtException
     */
    public Collection getMsgFieldDetailList(String hostId, String formatId,
        String entityId) throws SwtException;

    /**
     * @param msgfld
     * @throws SwtException
     */
    public void saveMsgFieldDetails(MessageFields msgfld)
        throws SwtException;

    /**
     * @param msgfld
     * @throws SwtException
     */
    public void updateMsgFieldDetails(MessageFields msgfld)
        throws SwtException;

    /**
     * @param msgfld
     * @throws SwtException
     */
    public void deleteMessageFieldDetail(MessageFields msgfld)
        throws SwtException;

    /**
     *
     * @return
     * @throws SwtException
     */
    public Collection getMaxSerialNo(String hostId, String formatId,
        String entityId) throws SwtException;

    /**
     *
     * @return
     * @throws SwtException
     */
    public Collection getKeyWordsList() throws SwtException;
    
    
    
    /**
     * Scenario Format methods 
     */
    
    
    /**
     * @param hostId
     * @param entityId
     * @param formatId
     * @return
     * @throws SwtException
     */
    public Collection getScenarioMsgFieldDetailList(String formatId) throws SwtException;
    
    
    /**
     * @param msgfld
     * @throws SwtException
     */
    public void saveScenarioMsgFieldDetails(ScenarioMessageFields msgfld)
        throws SwtException;

    /**
     * @param msgfld
     * @throws SwtException
     */
    public void updateScenarioMsgFieldDetails(ScenarioMessageFields msgfld)
        throws SwtException;

    /**
     * @param msgfld
     * @throws SwtException
     */
    public void deleteScenarioMessageFieldDetail(ScenarioMessageFields msgfld)
        throws SwtException;

    /**
     *
     * @return
     * @throws SwtException
     */
    public Collection getScenarioMaxSerialNo(String formatId) throws SwtException;

    /**
     *
     * @return
     * @throws SwtException
     */
    public Collection getScenarioKeyWordsList() throws SwtException;
}
