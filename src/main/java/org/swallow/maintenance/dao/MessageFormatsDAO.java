/*
 * (c) MessageFormatsDAO.java 
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */

package org.swallow.maintenance.dao;

import java.util.Collection;

import org.swallow.dao.DAO;
import org.swallow.exception.SwtException;
import org.swallow.maintenance.model.MessageFields;
import org.swallow.maintenance.model.MessageFormats;
import org.swallow.maintenance.model.ScenarioMessageFields;
import org.swallow.maintenance.model.ScenarioMessageFormats;
import org.swallow.util.LabelValueBean;

/**
 * This Class is DAO layer for the Message formats Screen
 * 
 * <AUTHOR>
 * 
 */
public interface MessageFormatsDAO extends DAO {
	/**
	 * @param hostId
	 * @param entityId
	 * @return Collection of MessageFormat objects for given entity
	 * @throws SwtException
	 */
	public Collection getMsgFormatDetailList(String hostId, String entityId)
			throws SwtException;
	


	/**
	 * This method is used to save the Message Format object into database
	 * 
	 * @param msgfmt
	 *            MessageFormats
	 * @throws SwtException
	 */
	public void saveMsgFormatDetails(MessageFormats msgfmt) throws SwtException;

	/**
	 * This method is used to update the Message Format object into database
	 * 
	 * @param msgfmt
	 *            MessageFormats
	 * @throws SwtException
	 */
	public void updateMsgFormatDetails(MessageFormats msgfmt)
			throws SwtException;

	/**
	 * @param msgfmt
	 * @throws SwtException
	 */
	public void deleteMsgFormatDetail(MessageFormats msgfmt)
			throws SwtException;

	/**
	 * @param msgfld
	 * @throws SwtException
	 */
	public void saveMsgFieldDetails(MessageFields msgfld) throws SwtException;

	/**
	 * @param msgfld
	 * @throws SwtException
	 */
	public void updateMsgFieldDetails(MessageFields msgfld) throws SwtException;

	/**
	 * @param msgfld
	 * @throws SwtException
	 */
	public void deleteMessageFieldDetail(MessageFields msgfld)
			throws SwtException;

	/**
	 * This method is used to fetch the message format object from the database
	 * with given format id.
	 * 
	 * @param entityId
	 * @param hostId
	 * @param formatId
	 * @return
	 * @throws SwtException
	 * <AUTHOR> Tripathi has weritten this method on 7th Aug 07
	 */
	public MessageFormats getMsgFormatDetail(String entityId, String hostId,
			String formatId) throws SwtException;
	
	

	/*
	 * Start:Code Modified by Chinniah on 16-AUG-2011 for Mantis 1521:Sweeping
	 * Process: New option to write messages to database
	 */
	/**
	 * This Method is used for Fetch interfaceId from database
	 * 
	 * @return Collection
	 * @throws SwtException
	 */

	public Collection getInterfaceId() throws SwtException;

	/**
	 * This method is used to fetch data message path from database
	 * 
	 * @return String
	 * @throws SwtException
	 */
	public String getMessageFormatPath() throws SwtException;
	/*
	 * End:Code Modified by Chinniah on 16-AUG-2011 for Mantis 1521:Sweeping
	 * Process: New option to write messages to database
	 */
	
	/**
	 * This method is used to fetch the message format object from the database
	 * with given format id.
	 * 
	 * @param scenarioId
	 * @param hostId
	 * @param formatId
	 * @return
	 * @throws SwtException
	 * <AUTHOR> Sridi
	 */
	public ScenarioMessageFormats getScenarioMsgFormatDetail(String formatId) throws SwtException;
	
	/**
	 * @param hostId
	 * @param scenarioId
	 * @return Collection of MessageFormat objects for given scenarioId
	 * @throws SwtException
	 */
	public Collection getScenarioMsgFormatDetailList()
			throws SwtException;
	
	
	
	

	/**
	 * This method is used to save the Message Format object into database
	 * 
	 * @param msgfmt
	 *            MessageFormats
	 * @throws SwtException
	 */
	public void saveScenarioMsgFormatDetails(ScenarioMessageFormats msgfmt) throws SwtException;

	/**
	 * This method is used to update the Message Format object into database
	 * 
	 * @param msgfmt
	 *            MessageFormats
	 * @throws SwtException
	 */
	public void updateScenarioMsgFormatDetails(ScenarioMessageFormats msgfmt)
			throws SwtException;

	/**
	 * @param msgfmt
	 * @throws SwtException
	 */
	public void deleteScenarioMsgFormatDetail(ScenarioMessageFormats msgfmt)
			throws SwtException;

	/**
	 * @param msgfld
	 * @throws SwtException
	 */
	public void saveScenarioMsgFieldDetails(ScenarioMessageFields msgfld) throws SwtException;

	/**
	 * @param msgfld
	 * @throws SwtException
	 */
	public void updateScenarioMsgFieldDetails(ScenarioMessageFields msgfld) throws SwtException;

	/**
	 * @param msgfld
	 * @throws SwtException
	 */
	public void deleteScenarioMessageFieldDetail(ScenarioMessageFields msgfld)
			throws SwtException;
	
	
	/**
	 * This method is used to get scenario message formats from P_SCN_MESSAGE_FORMAT
	 */
	public Collection<LabelValueBean> getScenMsgFormats() throws SwtException;
	
	/**
	 * This method is used to delete scenario message formats from P_SCN_MESSAGE_FORMAT and P_SCN_MESSAGE_FIELDS
	 */
	public void deleteScenMsgFormatById(String formatId) throws SwtException;
}
