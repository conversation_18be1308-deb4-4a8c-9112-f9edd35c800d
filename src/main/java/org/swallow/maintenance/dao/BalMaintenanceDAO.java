/*
 * @(#)BalMaintenanceDAO.java 1.0 23/08/06
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.maintenance.dao;

import java.util.Collection;
import java.util.Date;

import org.swallow.dao.DAO;
import org.swallow.exception.SwtException;
import org.swallow.maintenance.model.BalMaintenance;
import org.swallow.maintenance.model.BalType;

/**
 * BalMaintenanceDAO.java
 * 
 * This interface has methods that are used for accessing the persistent storage
 * such as database <br>
 * which helps client to create, retrieve and persists data to the Persistent
 * Object.<br>
 * 
 */

public interface BalMaintenanceDAO extends DAO {

	/**
	 * Returns the collection of balance list from Database
	 * 
	 * @param hostId
	 * @param entityId
	 * @return Collection
	 * @throws SwtException
	 */
	public Collection getBalanceList(String hostId, String entityId)
			throws SwtException;

	/**
	 * Update the changed balance to the table P_BALANCE
	 * 
	 * @param balmaintenance
	 * @param startBalance
	 * @param hostId
	 * @param entityId
	 * @param balanceType
	 * @param replacebalanceDate
	 * @return
	 * @throws SwtException
	 */
	public void updateBalanceDetail(BalMaintenance balmaintenance,
			String startBalance, String hostId, String entityId,
			String balanceType, Date replacebalanceDate) throws SwtException;

	/**
	 * Returns the list of balmaintenance from the table P_BALANCE to the
	 * BalmaintenanceManagerImpl
	 * 
	 * @param hostId
	 * @param entityId
	 * @param balanceTypeId
	 * @param balType
	 * @param replaceDate
	 * @return BalMaintenance
	 * @throws SwtException
	 */
	public BalMaintenance getEditableData(String hostId, String entityId,
			String balanceTypeId, String balType, Date replaceDate)
			throws SwtException;

	/**
	 * Passes the value to the stored procedure sp_balance_maintenance and
	 * returns the collection of Balance details
	 * 
	 * @param hostId
	 * @param entityId
	 * @param balanceType
	 * @param currency
	 * @param selectedDate
	 * @param startRowNumber
	 * @param endRowNumber
	 * @param filterValue
	 * @param roleId
	 * @return Collection
	 * @throws SwtException
	 */
	public Collection<BalType> accountDetailUsingStoredProc(String hostId,
			String entityId, String balanceType, String currency,
			Date selectedDate, int startRowNumber, int endRowNumber,
			String filterValue, String roleId) throws SwtException;

}
