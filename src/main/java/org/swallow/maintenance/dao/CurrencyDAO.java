/**
 * @(#)CurrencyDAO.java 1.0 23/08/06
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.maintenance.dao;

import org.swallow.dao.DAO;

import org.swallow.exception.SwtException;

import org.swallow.maintenance.model.Currency;
import org.swallow.maintenance.model.CurrencyAccessTO;

import java.util.Collection;
import java.util.List;

/**
 * This is DAO interface for Currency screen
 */
public interface CurrencyDAO extends DAO {

	/**
	 * Returns the collection of currency detail list from currency table for an
	 * entity
	 * 
	 * @param entityId
	 * @param hostId
	 * @return Collection
	 * @throws SwtException
	 */
	public Collection getCurrencyDetailList(String entityId, String hostId)
			throws SwtException;

	/**
	 * Save the added new currency detail in the DataBase
	 * 
	 * @param curr
	 * @return
	 * @throws SwtException
	 */
	public void saveCurrencyDetail(Currency currency) throws SwtException;

	/**
	 * Update the changed currency detail in the DataBase
	 * 
	 * @param curr
	 * @return
	 * @throws SwtException
	 */
	public void updateCurrencyDetail(Currency currency) throws SwtException;

	/**
	 * Deletes the selected currency detail from the DataBase
	 * 
	 * @param curr
	 * @return
	 * @throws SwtException
	 */
	public void deleteCurrencyDetail(Currency currency) throws SwtException;

	/**
	 * Returns the collection of currency detail list from currency table
	 * 
	 * @param entityLvls
	 * @param hostId
	 * @return collection
	 * @throws SwtException
	 */
	public Collection getCurrencyDetailList(Collection entityLvls, String hostId)
			throws SwtException;

	/**
	 * This method fetches all the CurrencyExchange defined for entityId,
	 * currency code and for date <= today
	 * 
	 * @param entityId
	 * @param currencyCode
	 * @return List
	 * @throws SwtException
	 */
	public List getTodayExchangeRate(String entityId, String currencyCode)
			throws SwtException;

	/**
	 * Method to get the collection of currency for the given entityId and
	 * currency groupId in order to the priority order of the currency
	 * 
	 * @param entityId
	 * @param currencyGroupId
	 * @return collection
	 * @throws SwtException
	 */
	public Collection getCurrencies(String entityId, String currencyGroupId)
			throws SwtException;

	/**
	 * Method to get the collection of currency for the given entityId and
	 * currency groupId in order to the priority order and currency code of the
	 * currency
	 * 
	 * this method is Used
	 * 
	 * @param entityId
	 * @param currencyGroupId
	 * @return Collection
	 */
	public Collection getAllCurrencies(String entityId, String currencyGroupId)
			throws SwtException;

	/*
	 * Start: Code added by RK on 08-Mar-2012 for Mantis 1645
	 */
	/**
	 * This method invokes stored procedure "pk_application.spEntityCcyAccess"
	 * and gets list of currencies belongs to given hostId, entityId and roleId.
	 * The currencies should have either view or full access.
	 * 
	 * @param entityId
	 * @param roleId
	 * @return Collection<CurrencyAccessTO>
	 * @throws SwtException
	 */
	public Collection<CurrencyAccessTO> getEntityCurrency(String entityId,
			String roleId) throws SwtException;
	/*
	 * End: Code added by RK on 08-Mar-2012 for Mantis 1645
	 */
	
	/**
	 * @param hostId
	 * @param currencyCode
	 * @return String
	 * @throws SwtException 
	 */
	public boolean checkIfCurrencyExists(String hostId,String currencyCode, String entity) throws SwtException;
	
	/**
	 * This method is used to get the currency tolerance
	 */

	public String getCcyTolerance(String hostId, String ccy ,String entity)
			throws SwtException;
	
	/**
	 * This method is used to get the currency Tzname And tz_offset
	 */	
	public String getCurrencyTznameAndTzOffset(String currencyCode) throws SwtException;
}
