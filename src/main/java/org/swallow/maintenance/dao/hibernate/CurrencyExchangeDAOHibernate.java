/*
 * @(#)CurrencyExchangeDAOHibernate.java 1.0 23/08/06
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.maintenance.dao.hibernate;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.List;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.hibernate.HibernateException;
import org.hibernate.Query;
import org.hibernate.Session;
import org.hibernate.SessionFactory;
import org.hibernate.Transaction;
import org.springframework.context.annotation.Lazy;
import org.springframework.orm.hibernate5.support.HibernateDaoSupport;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Repository;
import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.maintenance.dao.CurrencyExchangeDAO;
import org.swallow.maintenance.model.CurrencyExchange;
import org.swallow.maintenance.model.CurrencyMaster;
import org.swallow.maintenance.model.Entity;
import org.swallow.util.JDBCCloser;
import org.swallow.util.SwtInterceptor;
import org.swallow.util.SwtUtil;
import org.swallow.util.SystemFormats;
/**
 *This is DAO class for currency exchange screen 
 */
@Component ("CurrencyExchangeDAO")
@Repository
public class CurrencyExchangeDAOHibernate extends HibernateDaoSupport implements
		CurrencyExchangeDAO {
	
	public CurrencyExchangeDAOHibernate(@Lazy SessionFactory sessionfactory){
	    setSessionFactory(sessionfactory);
	}
	/**
	 * Final instance for log
	 */
	private final Log log = LogFactory
			.getLog(CurrencyExchangeDAOHibernate.class);
	private int page = 0;

	/**
	 * This is used to fetches currency code details from database
	 * 
	 * @param currencyCode
	 * @return Collection
	 * @throws SwtException
	 */

	public Collection<CurrencyMaster> getCurrencyCodeDetailList(
			String currencyCode) throws SwtException {
		try {
			log.debug(this.getClass().getName()
					+ "- [getCurrencyCodeDetailList] - " + "Entry");
			List list = null;
			/* Condition to check currency code is "ALL" */
			if (currencyCode.equals("All")) {
				list = getHibernateTemplate().find("from CurrencyMaster");
			} else {
				list = getHibernateTemplate().find(
						"from CurrencyMaster c where c.currencyCode=?0",
						new Object[] { currencyCode });
			}
			log.debug(this.getClass().getName()
					+ "- [getCurrencyCodeDetailList] - " + "Exit");
			return list;
		} catch (Exception exp) {
			log.debug(this.getClass().getName()
							+ " - Exception Catched in [getCurrencyCodeDetailList] method : - "
							+ exp.getMessage());

			log.error(this.getClass().getName()
							+ " - Exception Catched in [getCurrencyCodeDetailList] method : - "
							+ exp.getMessage());
			exp.printStackTrace();
			throw SwtErrorHandler.getInstance().handleException(exp,
					"getCurrencyCodeDetailList",
					CurrencyExchangeDAOHibernate.class);
		}
	}

	/**
	 * This is used to fetch the currency details from S_CURRENCY table.
	 * 
	 * @param hostId
	 * @param entityId
	 * @return collection
	 * @throws SwtException
	 */

	public Collection getCurrencyList(String hostId, String entityId)
			throws SwtException {
		try {
			log.debug(this.getClass().getName() + "- [getCurrencyList] - "
					+ "Entry");
			List list = null;
			/* Currency code are retrieved from database */
			list = getHibernateTemplate()
					.find(
							"from Currency c where c.id.hostId =?0 and c.id.entityId=?1 ",
							new Object[] { hostId, entityId });
			log.debug(this.getClass().getName() + "- [getCurrencyList] - "
					+ "Exit");
			return list;
		} catch (Exception exp) {
			log.debug(this.getClass().getName()
					+ " - Exception Catched in [getCurrencyList] method : - "
					+ exp.getMessage());

			log.error(this.getClass().getName()
					+ " - Exception Catched in [getCurrencyList] method : - "
					+ exp.getMessage());
			exp.printStackTrace();
			throw SwtErrorHandler.getInstance().handleException(exp,
					"getCurrencyList", CurrencyExchangeDAOHibernate.class);
		}
	}

	/**
	 * This is used to save newly added exchange rate details in
	 * S_CURRENCY_EXCHANGE_RATE.
	 * 
	 * @param currencyExchange
	 * @return
	 * @throws SwtException
	 */
	@SuppressWarnings("unchecked")
	public void saveExchangeDetail(CurrencyExchange currencyExchange)
			throws SwtException {
		/* Methods local variable declaration */
		List records = null;
		Session session = null;
		Transaction tx = null;
		SwtInterceptor interceptor = null;
		try {
			log.debug(this.getClass().getName() + "- [saveExchangeDetail] - "
					+ "Entry");

			/*
			 * Start : Modified for Mantis 1366-Remove entry to log if duplicate
			 * records added by betcy on 18-02-2011
			 */
			records = getHibernateTemplate()
					.find(
							"from CurrencyExchange m where m.id.hostId=?0 and m.id.entityId=?1 "
									+ " and m.id.currencyCode=?2 and m.id.exchangeRateDate=?3",
							new Object[] {
									currencyExchange.getId().getHostId(),
									currencyExchange.getId().getEntityId(),
									currencyExchange.getId().getCurrencyCode(),
									currencyExchange.getId()
											.getExchangeRateDate() });
			/* Condition to check the list size */
			if (records.size() == 0) {
				interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");
				session = getHibernateTemplate()
							.getSessionFactory()
							.openSession();

				tx = session.beginTransaction();
				session.save(currencyExchange);
				tx.commit();
				session.close();
				
				log.debug(this.getClass().getName()
						+ "- [saveExchangeDetail] - " + "Exit");
			} else {
				throw new SwtException(
						"errors.DataIntegrityViolationExceptioninAdd");
			}
			/*
			 * End : Modified for Mantis 1366-Remove entry to log if duplicate
			 * records added by betcy on 18-02-2011
			 */
		} catch (Exception exp) {
			log.error(this.getClass().getName()
							+ " - Exception Catched in [saveExchangeDetail] method : - "
							+ exp.getMessage());

			throw SwtErrorHandler.getInstance().handleException(exp,
					"saveExchangeDetail", CurrencyExchangeDAOHibernate.class);
		} finally {
			records = null;
		}
	}

	/**
	 * This is used to save updated exchange details in S_CURRENCY_EXCHANGE_RATE
	 * table.
	 * 
	 * @param currencyExchange
	 * @return
	 * @throws SwtException
	 */
	public void updateExchangeDetail(CurrencyExchange currencyExchange)
			throws SwtException {
		Session session = null;
		Transaction tx = null;
		SwtInterceptor interceptor = null;
		try {
			log.debug(this.getClass().getName() + "- [updateExchangeDetail] - "
					+ "Entry");
			interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");
			session = getHibernateTemplate().getSessionFactory().openSession();
			tx = session.beginTransaction();
			session.update(currencyExchange);
			tx.commit();
			session.close();
			log.debug(this.getClass().getName() + "- [updateExchangeDetail] - "
					+ "Exit");
		} catch (Exception exp) {
			log.debug(this.getClass().getName()
							+ " - Exception Catched in [updateExchangeDetail] method : - "
							+ exp.getMessage());

			log.error(this.getClass().getName()
							+ " - Exception Catched in [updateExchangeDetail] method : - "
							+ exp.getMessage());
			exp.printStackTrace();
			throw SwtErrorHandler.getInstance().handleException(exp,
					"updateExchangeDetail", CurrencyExchangeDAOHibernate.class);
		}
	}

	/**
	 * This method added to return total count values from
	 * S_CURRENCY_EXCHANGE_RATE table.
	 * 
	 * @param CurrencyExchange -
	 * @return int
	 * @throws SwtException
	 */
	public int getTotalCount(CurrencyExchange currencyexchange)
			throws SwtException {
		try {
			log.debug(this.getClass().getName() + "- [getTotalCount] - "
					+ "Entry");
			/* Method's local variable declaration */
			String hostId;
			String entityId;
			Date exchangeFromDate;
			Date exchangeToDate;
			String currencyCode;
			List list = null;
			int totalCount;
			/* Retrieve host id and entity id using bean class */
			hostId = currencyexchange.getId().getHostId();
			entityId = currencyexchange.getId().getEntityId();
			/* Retrieve currency exchange from and to date using bean class */
			exchangeFromDate = currencyexchange.getExchangeRateFromDate();
			exchangeToDate = currencyexchange.getExchangeRateToDate();
			/* Retrieve currency code using bean class */
			currencyCode = currencyexchange.getId().getCurrencyCode();
			/* Condition to check currency code is "ALL" */
			if (currencyCode.equals("All")) {
				list = getHibernateTemplate()
						.find(
								"from CurrencyExchange c where c.id.hostId =?0 and c.id.entityId=?1 and trunc(c.id.exchangeRateDate) between ?2 and ?3 ",
								new Object[] { hostId, entityId,
										exchangeFromDate, exchangeToDate });
			} else {
				list = getHibernateTemplate()
						.find(
								"from CurrencyExchange c "
										+ "where c.id.hostId =?0 and c.id.entityId=?1 and c.id.currencyCode=?2 and trunc(c.id.exchangeRateDate) between ?3 and ?4 ",
								new Object[] { hostId, entityId, currencyCode,
										exchangeFromDate, exchangeToDate });
				// Commented by Selva to avoid unnecessary postflush calls on
				// 04-May
				// getHibernateTemplate().flush();

			}
			totalCount = list.size();
			log.debug(this.getClass().getName() + "- [getTotalCount] - "
					+ "Exit");
			return totalCount;
		} catch (Exception exp) {
			log.debug(this.getClass().getName()
					+ " - Exception Catched in [getTotalCount] method : - "
					+ exp.getMessage());

			log.error(this.getClass().getName()
					+ " - Exception Catched in [getTotalCount] method : - "
					+ exp.getMessage());
			exp.printStackTrace();
			throw SwtErrorHandler.getInstance().handleException(exp,
					"updateExchangeDetail", CurrencyExchangeDAOHibernate.class);
		}
	}

	/**
	 * This is used to fetches the currency exchange details from
	 * S_CURRENCY_EXCHANGE_RATE
	 * 
	 * @param hostId
	 * @param entityId
	 * @param currencyCode
	 * @param exchangeFromDate
	 * @param exchangeToRate
	 * @param sysforma
	 * @param pageSize
	 * @param currentPage
	 * @param col_currencyCode
	 * @param exchangeRateDate
	 * @param sortBy
	 * @param orderBy
	 * @return
	 * @throws SwtException
	 */

	public Collection<CurrencyExchange> getCurrencyExchangeDetailList(
			String hostId, String entityId, String currencyCode,
			Date exchangeFromDate, Date exchangeToDate, SystemFormats sysforma,
			int pageSize, int currentPage, String col_currencyCode,
			Date exchangeRateDate, String sortBy, String orderBy)
			throws SwtException {
		log.debug(this.getClass().getName()
				+ "- [getCurrencyExchangeDetailList] - " + "Entry");
		/* Method's local variable declaration */
		List list = null;
		Session session = null;
		Query query;
		page = currentPage - 1;
		/* Condition to check currency code is null */
		if (col_currencyCode == null) {
			/* Set the currency code t0 "ALL" */
			col_currencyCode = "All";
		}
		if (currencyCode.trim().toUpperCase().equals("ALL")) {
			if (!(col_currencyCode.trim().toUpperCase().equals("ALL"))) {
				currencyCode = col_currencyCode;
			}
		}
		try {
			/* Obtain the session in a session factory */
			session = getHibernateTemplate().getSessionFactory().openSession();
			/* Condition to check currency code is "All' */
			if (currencyCode.equals("All")) {
				StringBuffer qry = new StringBuffer(
						"from CurrencyExchange c where c.id.hostId =?0 and c.id.entityId=?1 and trunc(c.id.exchangeRateDate) between ?2 and ?3 ");
				/* Condition to check exchange rate date is not equal to null */
				if (exchangeRateDate != null) {
					qry.append(" and c.id.exchangeRateDate=?4");
					qry.append(" order by ");
					qry.append(orderBy);
					qry.append(" ");
					qry.append(sortBy);
					query = session.createQuery(qry.toString());
					query.setParameter(0, hostId);
					query.setParameter(1, entityId);
					query.setParameter(2, exchangeFromDate);
					query.setParameter(3, exchangeToDate);
					query.setParameter(4, exchangeRateDate);
					list = query.setFirstResult(page * pageSize).setMaxResults(
							pageSize).list();

				} else {
					qry.append(" order by ");
					qry.append(orderBy);
					qry.append(" ");
					qry.append(sortBy);
					query = session.createQuery(qry.toString());
					query.setParameter(0, hostId);
					query.setParameter(1, entityId);
					query.setParameter(2, exchangeFromDate);
					query.setParameter(3, exchangeToDate);
					list = query.setFirstResult(page * pageSize).setMaxResults(
							pageSize).list();
					log.debug("Size - Exchange%%%" + list);
				}
			} else {
				log.debug("&&&Currency Exchange&&");
				StringBuffer qry = new StringBuffer(
						"from CurrencyExchange c where c.id.hostId =?0 and c.id.entityId=?1 and c.id.currencyCode=?2 and trunc(c.id.exchangeRateDate) between ?3 and ?4 ");
				/* Condition to check exchange rate date is not equal to null */
				if (exchangeRateDate != null) {
					qry.append(" and c.id.exchangeRateDate=?5");
					qry.append(" order by ");
					qry.append(orderBy);
					qry.append(" ");
					qry.append(sortBy);
					query = session.createQuery(qry.toString());
					query.setParameter(0, hostId);
					query.setParameter(1, entityId);
					query.setParameter(2, currencyCode);
					query.setParameter(3, exchangeFromDate);
					query.setParameter(4, exchangeToDate);
					query.setParameter(5, exchangeRateDate);
					list = query.setFirstResult(page * pageSize).setMaxResults(
							pageSize).list();
				} else {
					qry.append(" order by ");
					qry.append(orderBy);
					qry.append(" ");
					qry.append(sortBy);
					query = session.createQuery(qry.toString());
					query.setParameter(0, hostId);
					query.setParameter(1, entityId);
					query.setParameter(2, currencyCode);
					query.setParameter(3, exchangeFromDate);
					query.setParameter(4, exchangeToDate);
					list = query.setFirstResult(page * pageSize).setMaxResults(
							pageSize).list();
				}
			}
		} catch (HibernateException hibernateException) {
			log.debug(this.getClass().getName()
							+ " - Exception Catched in [getCurrencyExchangeDetailList] method : - "
							+ hibernateException.getMessage());

			log.error(this.getClass().getName()
							+ " - Exception Catched in [getCurrencyExchangeDetailList] method : - "
							+ hibernateException.getMessage());
			hibernateException.printStackTrace();
			throw new SwtException(hibernateException.getMessage());
		} finally {
		
			//edited by KaisBS for mantis 1745 : introducing the implementation of the JDBCCloser class
			JDBCCloser.close(session);
			
		}
		// Commented by Selva to avoid unnecessary postflush calls on 04-May
		// getHibernateTemplate().flush();
		log.debug(this.getClass().getName()
				+ "- [getCurrencyExchangeDetailList] - " + "Exit");
		return list;
	}

	/**
	 * This method Returns the Exchange rate format from the S_Entity table
	 * 
	 * @param CurrencyExchange
	 * @return boolean
	 * @throws SwtException
	 */
	public boolean getEntityCurrencyFlag(CurrencyExchange currencyexchange)
			throws SwtException {
		try {
			log.debug(this.getClass().getName()
					+ "- [getEntityCurrencyFlag] - " + "Entry");
			/* Method's local variable declaration */
			List list = null;
			String hostId;
			String entityId;
			boolean entityFlag = false;
			/* Retrieve the host id using bean class */
			hostId = currencyexchange.getId().getHostId();
			/* Retrieve the entity id using bean class */
			entityId = currencyexchange.getId().getEntityId();
			list = getHibernateTemplate().find(
					"from Entity e "
							+ "where e.id.hostId =?0 and e.id.entityId=?1 ",
					new Object[] { hostId, entityId });
			/* Condition to check list not equal to null */
			if (list != null && list.size() > 0) {
				Entity entity = (Entity) list.get(0);
				if (entity.getExchangeRateFormat().trim().equals("2")) {
					entityFlag = true;
				}
			}
			log.debug(this.getClass().getName()
					+ "- [getEntityCurrencyFlag] - " + "Exit");
			return entityFlag;
		} catch (Exception exp) {

			log.debug(this.getClass().getName()
							+ " - Exception Catched in [getEntityCurrencyFlag] method : - "
							+ exp.getMessage());

			log.error(this.getClass().getName()
							+ " - Exception Catched in [getEntityCurrencyFlag] method : - "
							+ exp.getMessage());
			exp.printStackTrace();
			throw SwtErrorHandler.getInstance()
					.handleException(exp, "getEntityCurrencyFlag",
							CurrencyExchangeDAOHibernate.class);
		}
	}

	/**
	 * This is used to fetches filtered count values from
	 * S_CURRENCY_EXCHANGE_RATE
	 * 
	 * @param CurrencyExchange
	 * @return int
	 * @throws SwtException
	 */
	public int getFilterCount(CurrencyExchange currencyexchange)
			throws SwtException {
		try {
			log.debug(this.getClass().getName() + "- [getFilterCount] - "
					+ "Entry");
			/* Method's local variable declaration */
			List list = null;
			String currencyCode;
			StringBuffer query;
			int totalCount;
			Object[] paramArr = new Object[4];
			list = new ArrayList<Object>();

			/* Retrieve the currency id from bean class */
			currencyCode = currencyexchange.getId().getCurrencyCode();
			/* Condition to check currency id is"ALL" */
			if (currencyCode.trim().toUpperCase().equals("ALL")) {
				if (!(currencyexchange.getColCurrencyCode().trim()
						.toUpperCase().equals("ALL"))) {
					/* Read the currency code from bean class */
					currencyCode = currencyexchange.getColCurrencyCode();
				}
			}
			/* Condition to check currency id is"ALL" */
			if (currencyCode.equals("All")) {
				/* Retrieve currency exchange details from database */
				query = new StringBuffer(
						"from CurrencyExchange c where c.id.hostId =?0 and c.id.entityId=?1 and trunc(c.id.exchangeRateDate) between ?2 and ?3 ");
				/* Condition to check Date of exchange rate is not null */
				if (currencyexchange.getId().getExchangeRateDate() != null) {
					query.append(" and c.id.exchangeRateDate=?4");
					list = getHibernateTemplate().find(
							query.toString(),
							new Object[] {
									currencyexchange.getId().getHostId(),
									currencyexchange.getId().getEntityId(),
									currencyexchange.getExchangeRateFromDate(),
									currencyexchange.getExchangeRateToDate(),
									currencyexchange.getId()
											.getExchangeRateDate() });
				} else {
					list = getHibernateTemplate().find(
							query.toString(),
							new Object[] {
									currencyexchange.getId().getHostId(),
									currencyexchange.getId().getEntityId(),
									currencyexchange.getExchangeRateFromDate(),
									currencyexchange.getExchangeRateToDate() });
				}
			} else {

				query = new StringBuffer(
						"from CurrencyExchange c where c.id.hostId =?0 and c.id.entityId=?1 and c.id.currencyCode=?2 and trunc(c.id.exchangeRateDate) between ?3 and ?4 ");
				/*
				 * Condition to check date of currency exchange rate is not
				 * equal to null
				 */
				if (currencyexchange.getId().getExchangeRateDate() != null) {
					query.append(" and c.id.exchangeRateDate=?5");
					list = getHibernateTemplate().find(
							query.toString(),
							new Object[] {
									currencyexchange.getId().getHostId(),
									currencyexchange.getId().getEntityId(),
									currencyCode,
									currencyexchange.getExchangeRateFromDate(),
									currencyexchange.getExchangeRateToDate(),
									currencyexchange.getId()
											.getExchangeRateDate() });
				} else {
					list = getHibernateTemplate().find(
							query.toString(),
							new Object[] {
									currencyexchange.getId().getHostId(),
									currencyexchange.getId().getEntityId(),
									currencyCode,
									currencyexchange.getExchangeRateFromDate(),
									currencyexchange.getExchangeRateToDate() });
				}
			}
			totalCount = list.size();
			log.debug(this.getClass().getName() + "- [getFilterCount] - "
					+ "Exit");
			return totalCount;
		} catch (Exception exp) {

			log.debug(this.getClass().getName()
					+ " - Exception Catched in [getFilterCount] method : - "
					+ exp.getMessage());

			log.error(this.getClass().getName()
					+ " - Exception Catched in [getFilterCount] method : - "
					+ exp.getMessage());
			exp.printStackTrace();
			throw SwtErrorHandler.getInstance().handleException(exp,
					"getFilterCount", CurrencyExchangeDAOHibernate.class);
		}
	}

	/**
	 * This is used to remove exchange rate details from
	 * S_CURRENCY_EXCHANGE_RATE table
	 * 
	 * @param currencyExchange
	 * @return
	 * @throws SwtException
	 */

	public void deleteExchangeDetail(CurrencyExchange currencyExchange)
			throws SwtException {
		Session session = null;
		Transaction tx = null;
		SwtInterceptor interceptor = null;
		try {
			log.debug(this.getClass().getName() + "- [deleteExchangeDetail] - "
					+ "Entry");
			/* Method's local variable declaration */
			String deleteHQL;
			int deleteCounter;
			/* Remove the selected records from database */
			deleteHQL = "delete from CurrencyExchange m where m.id.hostId= :hostId and m.id.entityId= :entityId "
					+ " and m.id.currencyCode= :ccyCode and m.id.exchangeRateDate= :exchangeRateDate";

			interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");
			session = getHibernateTemplate().getSessionFactory().openSession();
			tx = session.beginTransaction();
			deleteCounter = session.createQuery(deleteHQL)
            .setParameter("hostId", currencyExchange.getId().getHostId())
            .setParameter("entityId", currencyExchange.getId().getEntityId())
            .setParameter("ccyCode", currencyExchange.getId().getCurrencyCode())
            .setParameter("exchangeRateDate", currencyExchange.getId().getExchangeRateDate())
            .executeUpdate();
			tx.commit();
			session.close();

			log.debug(this.getClass().getName() + "- [deleteExchangeDetail] - "
					+ "Exit");
		} catch (Exception exp) {

			log.debug(this.getClass().getName()
							+ " - Exception Catched in [deleteExchangeDetail] method : - "
							+ exp.getMessage());

			log.error(this.getClass().getName()
							+ " - Exception Catched in [deleteExchangeDetail] method : - "
							+ exp.getMessage());
			exp.printStackTrace();
			throw SwtErrorHandler.getInstance().handleException(exp,
					"deleteExchangeDetail", CurrencyExchangeDAOHibernate.class);
		}
	}

}
