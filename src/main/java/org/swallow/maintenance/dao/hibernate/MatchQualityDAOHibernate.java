/*
 * @(#)MatchQualityDAOHibernate.java 1.0 23/08/06
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.maintenance.dao.hibernate;

import java.util.Collection;
import java.util.List;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.exception.SwtRecordNotExist;
import org.swallow.maintenance.dao.MatchQualityDAO;
import org.swallow.maintenance.model.MatchParams;
import org.swallow.maintenance.model.MatchQuality;
import org.swallow.maintenance.model.QualityAction;
import org.swallow.util.SwtInterceptor;
import org.swallow.util.SwtUtil;
import org.springframework.context.annotation.Lazy;
import org.springframework.orm.hibernate5.support.HibernateDaoSupport;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.stereotype.Repository;
import org.springframework.beans.factory.annotation.Autowired;
import org.hibernate.HibernateException;
import org.hibernate.SessionFactory;
import org.hibernate.Transaction;
import org.hibernate.Session;

/**
 * MatchQualityDAOHibernate.java<br>
 * 
 * This class implements the MatchQualityDAO and acts as DAO layer for all
 * database operations related to Match Quality operations.<br>
 * 
 * @Modified: Marshal on 31-March-2011
 */
@Repository ("matchQualityDAO")
@Transactional
public class MatchQualityDAOHibernate extends HibernateDaoSupport implements
		MatchQualityDAO {
	public MatchQualityDAOHibernate(@Lazy SessionFactory sessionfactory) {
	    setSessionFactory(sessionfactory);
	}


	/**
	 * Initializing logger object for this class
	 */

	private final Log log = LogFactory.getLog(MatchQualityDAOHibernate.class);

	/**
	 * Collect the Match list from P_MATCH_ACTION
	 * 
	 * @param entityId
	 * @param hostId
	 * @return Collection
	 * @throws SwtException
	 */
	public Collection getMatchList(String entityId, String hostId)
			throws SwtException {

		log.debug(this.getClass().getName() + " - [getMatchList] - " + "Entry");

		List list = getHibernateTemplate()
				.find(
						"from MatchQuality c "
								+ " where c.id.hostId=?0 and c.id.entityId =?1 order by c.id.hostId,c.id.entityId,c.id.currencyCode,c.id.posLevel",
						new Object[] { hostId, entityId });
		log.debug(this.getClass().getName() + " - [getMatchList] - " + "Exit");
		return list;
	}

	/**
	 * Collect the matchQuality list from the database table P_MATCH_ACTION
	 * 
	 * @param hostId
	 * @param entityId
	 * @param currencyCode
	 * @param posLevel
	 * @return MatchQuality
	 * @throws SwtException
	 */
	public MatchQuality getMatchQualityList(String hostId, String entityId,
			String currencyCode, Integer posLevel) throws SwtException {

		log.debug(this.getClass().getName() + " - [getMatchQualityList] - "
				+ "Entry");

		MatchQuality matchQualityObj = null;

		List coll = getHibernateTemplate()
				.find(
						"from MatchQuality c where c.id.hostId=?0 and c.id.entityId =?1 and c.id.currencyCode=?2 and c.id.posLevel=?3",
						new Object[] { hostId, entityId, currencyCode, posLevel });

		if (coll.size() > 0) {
			matchQualityObj = (MatchQuality) coll.get(0);
		}

		log.debug(this.getClass().getName() + " - [getMatchQualityList] - "
				+ "Exit");
		return matchQualityObj;
	}

	/**
	 * Get the collection of parameter description from data base table
	 * P_MATCH_PARAMETERS
	 * 
	 * @return Collection
	 * @throws SwtException
	 */
	public Collection getParamDescAll() throws SwtException {

		log.debug(this.getClass().getName() + " - [getParamDescAll] - "
				+ "Entry");

		List list = getHibernateTemplate().find(
				"from MatchParams c order by c.id.paramCode", new Object[] {});
		log.debug(this.getClass().getName() + " - [getParamDescAll] - "
				+ "Exit");

		return list;
	}

	/**
	 * Save the values of Quality action bean in data base table P_MATCH_QUALITY
	 * 
	 * @param QualityAction
	 * @return None
	 * @throws SwtException
	 */
	@SuppressWarnings("unchecked")
	public void addMatchQuality(QualityAction matchQuality) throws SwtException {
		/* Methods local variable declaration */
		List records = null;
		List list=null;
		Session session = null;
		Transaction tx = null;
		SwtInterceptor interceptor = null;
		try {
			log.debug(this.getClass().getName() + " - [addMatchQuality] - "
					+ "Entry");
			/*
			 * Start : Modified for Mantis 1366-Remove entry to log if duplicate
			 * records added by betcy on 18-02-2011
			 */
			records = (List ) getHibernateTemplate().find(
							"from MatchQuality c where c.id.hostId=?0 and c.id.entityId =?1 and c.id.currencyCode=?2 and c.id.posLevel=?3",
							new Object[] { matchQuality.getId().getHostId(),
									matchQuality.getId().getEntityId(),
									matchQuality.getId().getCurrencyCode(),
									matchQuality.getId().getPositionLevel() });

			if (records.size() == 0) {
				list = getHibernateTemplate().find(
						"from MatchParams c where c.id.paramCode = ?0",
						new Object[] { matchQuality.getId().getParameterId() });

				if ((list != null) && (list.size() > 0)) {
					matchQuality.setParamIdRef((MatchParams) list.get(0));
				}

				interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");
				session = getHibernateTemplate()
							.getSessionFactory()
							.withOptions().interceptor(interceptor).openSession();
				tx = session.beginTransaction();
				session.save(matchQuality);
				tx.commit();
				session.close();
				log.debug(this.getClass().getName() + " - [addMatchQuality] - "
						+ "Exit");
			} else {
				throw new SwtException(
						"errors.DataIntegrityViolationExceptioninAdd");
			}
			/*
			 * End : Modified for Mantis 1366-Remove entry to log if duplicate
			 * records added by betcy on 18-02-2011
			 */
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					+ "- [addMatchQuality] - Exception " + exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp,
					"addMatchQuality", MatchQualityDAOHibernate.class);
		} finally {
			records = null;
			list=null;
		}
	}

	/**
	 * Update the values of Quality action in the data base table
	 * P_MATCH_QUALITY
	 * 
	 * @param QualityAction
	 * @return None
	 * @throws SwtException
	 */

	public void updateMatchQuality(QualityAction matchQuality)
			throws SwtException {
		Session session = null;
		Transaction tx = null;
		SwtInterceptor interceptor = null;
		try {
			log.debug(this.getClass().getName() + " - [updateMatchQuality] - "
					+ "Entry");
			interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");
			session = getHibernateTemplate()
						.getSessionFactory()
						.withOptions().interceptor(interceptor).openSession();
			tx = session.beginTransaction();
			session.update(matchQuality);
			tx.commit();
			session.close();
			log.debug(this.getClass().getName() + " - [updateMatchQuality] - "
					+ "Exit");
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					+ "- [updateMatchQuality] - Exception " + exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp,
					"updateMatchQuality", MatchQualityDAOHibernate.class);
		}

	}

	/**
	 * Delete the values of Quality action from the data base table
	 * P_MATCH_ACTION
	 * 
	 * @param QualityAction
	 * @return None
	 * @throws SwtException
	 */
	public void deleteMatchQuality(MatchQuality matchQuality)
			throws SwtException {
		Session session = null;
		Transaction tx = null;
		SwtInterceptor interceptor = null;
		try {
			log.debug(this.getClass().getName() + " - [deleteMatchQuality] - "
					+ "Entry");
			interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");
			session = getHibernateTemplate().getSessionFactory().withOptions().interceptor(interceptor).openSession();
			tx = session.beginTransaction();
			int i = session
					.createQuery(
							" delete from MatchQuality c where c.id.hostId= :hostId and c.id.entityId = :entityId and c.id.currencyCode= :ccyCode and c.id.posLevel=  :posLevel")
            .setParameter("hostId", matchQuality.getId().getHostId())
            .setParameter("entityId", matchQuality.getId().getEntityId())
            .setParameter("ccyCode", matchQuality.getId().getCurrencyCode())
            .setParameter("posLevel", matchQuality.getId().getPosLevel())
            .executeUpdate();
			tx.commit();
			session.close();
	
			if (i == 0) {
				throw new SwtRecordNotExist();
			}
	
			log.debug(this.getClass().getName() + " - [deleteMatchQuality] - "
					+ "Exit");
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					+ "- [deleteMatchQuality] - Exception " + exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp,
					"deleteMatchQuality", MatchQualityDAOHibernate.class);
		}

	}

	/*
	 * Start: Code modified for Mantis 1238: Copy Match Qualities functionality
	 * generates error - by Marshal on 31-Mar-2011
	 */
	/**
	 * This method gets the collection of filterPosition level from the data
	 * base table P_MATCH_ACTION.<br>
	 * 
	 * @param hostId
	 * @param entityId
	 * @param currencyCode
	 * @return Collection
	 * @throws SwtException
	 */
	@SuppressWarnings("unchecked")
	public Collection getfilterPositionlevel(String hostId, String entityId,
			String currencyCode) throws SwtException {
		// Declares the collection object that retrieves the list of Position
		// level(s)
		List collection = null;
		try {
			log.debug(this.getClass().getName()
					+ " - [getfilterPositionlevel] - " + "Entry");

			// Retrieves the position level(s) from the database and adds to the
			// collection
			collection = (List ) getHibernateTemplate().find(
							"select distinct c.id.posLevel from MatchQuality c where c.id.hostId=?0 and c.id.entityId =?1 and c.id.currencyCode=?2",
							new Object[] { hostId, entityId, currencyCode });

			log.debug(this.getClass().getName()
					+ " - [getfilterPositionlevel] - " + "Exit");
		} catch (Exception exception) {
			log.error(this.getClass().getName()
					+ " - [getfilterPositionlevel] - Exception -"
					+ exception.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exception,
					"getfilterPositionlevel", MatchQualityDAOHibernate.class);
		}
		return collection;
	}
	/*
	 * End: Code modified for Mantis 1238: Copy Match Qualities functionality
	 * generates error - by Marshal on 31-Mar-2011
	 */
}
