package org.swallow.maintenance.dao.hibernate;

import java.sql.CallableStatement;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Iterator;
import java.util.List;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.hibernate.HibernateException;
import org.hibernate.Session;
import org.hibernate.SessionFactory;
import org.hibernate.Transaction;
import org.hibernate.criterion.Expression;
import org.hibernate.query.Query;
import org.springframework.context.annotation.Lazy;
import org.springframework.orm.hibernate5.support.HibernateDaoSupport;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;
import org.swallow.batchScheduler.ConnectionManager;
import org.swallow.control.model.JobStatus;
import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.maintenance.dao.CorrespondentAcctMaintenanceDAO;
import org.swallow.maintenance.model.CorrespondentAcct;
import org.swallow.util.JDBCCloser;
import org.swallow.util.SwtConstants;
import org.swallow.util.SwtInterceptor;
import org.swallow.util.SwtUtil;

import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.Root;

/**
 * 
 * This is DAO class for Correspondent Account Alias screen.
 * 
 */
@Repository ("correspondentAcctMaintenanceDAO")
@Transactional
public class CorrespondentAcctMaintenanceDAOHibernate extends
		HibernateDaoSupport implements CorrespondentAcctMaintenanceDAO {
	public CorrespondentAcctMaintenanceDAOHibernate(@Lazy SessionFactory sessionfactory) {
	    setSessionFactory(sessionfactory);
	}

	private final Log log = LogFactory
			.getLog(CorrespondentAcctMaintenanceDAO.class);

	/**
	 * Queries hibernate for a collection of CorrespondentAcct objects matching
	 * the supplied entityId and hostId parameters
	 * 
	 * @param entityId
	 * @param hostId
	 * @return Collection
	 * @throws SwtException
	 */
	public Collection getList(String entityId, String hostId, int startRowNumber, int endRowNumber , String messageType, String currencyCode, String userId, String selectedSort)
			throws SwtException {
		log.debug(this.getClass().getName() + " - [ getList ] - Entry ");
	
		ArrayList<CorrespondentAcct> returnList = new ArrayList<CorrespondentAcct>();
		Session session = null;
		Connection conn=null;
		CallableStatement pstmt=null;
		String order ="-1";
		String direction = "desc";
		ResultSet rs = null;
		Integer rowCount = null;
		try {
			session = getHibernateTemplate().getSessionFactory().openSession();
			conn = SwtUtil.connection(session);
			if( !SwtUtil.isEmptyOrNull(selectedSort)) {
				String[] result = selectedSort.split("\\|");
				order = result[0];
				direction = result[1];
				if(result[1].equals("true"))
					direction = "desc";
				else 
					direction = "asc";
			}
			
			/* Using Callable statement to execute the Stored Procedure */
			pstmt=conn.prepareCall("{call sp_Get_list_corresp_acc(?,?,?,?,?,?,?,?,?,?,?,?)}");
			pstmt.setString(1,hostId);
			pstmt.setString(2,entityId);
			pstmt.setString(3,null);
			pstmt.setString(4,messageType);
			pstmt.setString(5,currencyCode);
			pstmt.setString(6,order);
			pstmt.setInt(7,startRowNumber);
			pstmt.setInt(8,endRowNumber);
			pstmt.setString(9,direction);
			pstmt.setString(10,userId);
			pstmt.registerOutParameter(11, oracle.jdbc.OracleTypes.CURSOR);
			pstmt.registerOutParameter(12,oracle.jdbc.OracleTypes.INTEGER);
		
			pstmt.execute();
			rs = (ResultSet) pstmt.getObject(11);
			rowCount = pstmt.getInt(12);
			if(rs != null) {
				while(rs.next()) {
					
					CorrespondentAcct corrAcc =  new CorrespondentAcct();
					corrAcc.getId().setCorresAccId(rs.getString(4));
					corrAcc.setAccountId(rs.getString(7));
					corrAcc.getId().setMessageType(rs.getString(2));
					corrAcc.getId().setEntityId(rs.getString(6));
					corrAcc.getId().setHostId(rs.getString(5));
					corrAcc.getId().setCurrencyCode(rs.getString(3));
					corrAcc.setAccountName(rs.getString(8));
					corrAcc.setRowCount(rowCount);
					returnList.add(corrAcc);
				}
			}
			log.debug(this.getClass().getName() + " - [ getList ] - Exit ");
			return returnList;
		} catch (Exception e) {
			SwtErrorHandler swtErrorHandler = SwtErrorHandler.getInstance();
			SwtException swtexp = swtErrorHandler.handleException(e, "getList",
					CorrespondentAcctMaintenanceDAO.class);
			throw swtexp;
		}
       finally {
			Object[] exceptions = JDBCCloser.close(rs, pstmt, conn, session);
			if (exceptions[1]!=null){
				throw new SwtException(((HibernateException) exceptions[1]).getMessage());
			}	
			
			}
	}

	/**
	 * Deletes the unique account matching the PK data in the given
	 * CorrespondentAcct object
	 * 
	 * @param acct
	 * @throws SwtException
	 */
	public void deleteCorrespondentAcct(CorrespondentAcct acct)
			throws SwtException {
		log.debug(this.getClass().getName()
				+ " - [ deleteCorrespondentAcct ] - Entry ");
		Session session = null;
		Transaction tx = null;
		SwtInterceptor interceptor = null;
		try {
			
			interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");
			session = getHibernateTemplate().getSessionFactory().withOptions().interceptor(interceptor).openSession();
			tx = session.beginTransaction();
			session.delete(getCorrespondentAcct(acct));
			tx.commit();
			
			log.debug(this.getClass().getName()
					+ " - [ deleteCorrespondentAcct ] - Exit ");
		} catch (Exception e) {
			SwtErrorHandler swtErrorHandler = SwtErrorHandler.getInstance();
			SwtException swtExp = swtErrorHandler.handleException(e,
					"deleteCorrespondentAcct",
					CorrespondentAcctMaintenanceDAO.class);
			throw swtExp;
		}  finally {
			JDBCCloser.close(session);
		}
	}

	/**
	 * Queries hibernate for a unique record matching the entityId, hostId,
	 * messageType, currencyCode and corresAccId contained in the given
	 * CorrespondentAcct object
	 * 
	 * @param acct
	 * @return CorrespondentAcct
	 * @throws SwtException
	 */
	public CorrespondentAcct getCorrespondentAcct(CorrespondentAcct acct) throws SwtException {
		log.debug(this.getClass().getName() + " - [ getCorrespondentAcct ] - Entry ");
		CorrespondentAcct obj = null;
		Session session = null;
		try {
			session = getHibernateTemplate().getSessionFactory().openSession();
			String queryString = "FROM CorrespondentAcct ca WHERE ca.id.entityId = :entityId" +
					" AND ca.id.hostId = :hostId" +
					" AND ca.id.messageType = :messageType" +
					" AND ca.id.currencyCode = :currencyCode" +
					" AND ca.id.corresAccId = :corresAccId";
			Query<CorrespondentAcct> query = session.createQuery(queryString);
			query.setParameter("entityId", acct.getId().getEntityId());
			query.setParameter("hostId", acct.getId().getHostId());
			query.setParameter("messageType", acct.getId().getMessageType().replace("%20", " "));
			query.setParameter("currencyCode", acct.getId().getCurrencyCode());
			query.setParameter("corresAccId", acct.getId().getCorresAccId());
			List<CorrespondentAcct> results = query.list();

			Iterator itr = results.iterator();
			while (itr.hasNext()) {
				obj = (CorrespondentAcct) itr.next();
			}
		} catch (Exception e) {
			SwtErrorHandler swtErrorHandler = SwtErrorHandler.getInstance();
			SwtException swtExp = swtErrorHandler.handleException(e, "getCorrespondentAcct", CorrespondentAcctMaintenanceDAO.class);
			throw swtExp;
		}finally {
			JDBCCloser.close(session);
		}
		log.debug(this.getClass().getName() + " - [ getCorrespondentAcct ] - Exit ");
		return obj;
	}

	/**
	 * Queries hibernate for a collection of CorrespondentAcct objects matching
	 * the given entityId, hostId, messageType, currencyCode and corresAccId
	 * parameters
	 * 
	 * @param hostId
	 * @param entityId
	 * @param messageType
	 * @param currencyCode
	 * @return Collection
	 * @throws SwtException
	 */
	public Collection getCorrespondentAcctList(String hostId, String entityId,
			String messageType, String currencyCode) throws SwtException {
		log.debug(this.getClass().getName()
				+ " - [ getCorrespondentAcctList ] - Entry ");
		Collection<CorrespondentAcct> list = null;
		StringBuffer query = new StringBuffer();
		int index = 0;
		try {
			query
					.append("from CorrespondentAcct a where a.id.hostId=?0 and a.id.entityId=?1 ");

			if (currencyCode.equalsIgnoreCase(SwtConstants.ALL_VALUE)) {

				if (messageType != null && messageType.trim().length() > 0) {
					index = messageType.indexOf("%");
					if (index != -1) {
						query
								.append(" and UPPER(a.id.messageType) LIKE UPPER(?2) ");
					} else {
						query.append(" and UPPER(a.id.messageType)=UPPER(?2) ");
					}
					list = (Collection<CorrespondentAcct> ) getHibernateTemplate().find(query.toString(),
							new Object[] { hostId, entityId, messageType });
				} else {
					list = (Collection<CorrespondentAcct> ) getHibernateTemplate().find(query.toString(),
							new Object[] { hostId, entityId });
				}
			} else {
				query.append("and a.id.currencyCode=?2 ");
				if (messageType != null && messageType.trim().length() > 0) {
					index = messageType.indexOf("%");
					if (index != -1) {
						query
								.append(" and UPPER(a.id.messageType) LIKE UPPER(?3) ");
					} else {
						query.append(" and UPPER(a.id.messageType)=UPPER(?3) ");
					}
					list = (Collection<CorrespondentAcct> ) getHibernateTemplate().find(
							query.toString(),
							new Object[] { hostId, entityId, currencyCode,
									messageType, });
				} else {
					list = (Collection<CorrespondentAcct> ) getHibernateTemplate().find(query.toString(),
							new Object[] { hostId, entityId, currencyCode });
				}
			}
		} catch (Exception e) {
			SwtErrorHandler swtErrorHandler = SwtErrorHandler.getInstance();
			SwtException swtExp = swtErrorHandler.handleException(e,
					"getCorrespondentAcctList",
					CorrespondentAcctMaintenanceDAO.class);
			throw swtExp;
		}
		log.debug(this.getClass().getName()
				+ " - [ getCorrespondentAcctList ] - Exit ");
		return list;

	}

	/**
	 * This is used to save the records in database
	 * 
	 * @param acct
	 * @throws SwtException
	 */
	@SuppressWarnings("unchecked")
	public void saveCorrespondentAcct(CorrespondentAcct acct)
			throws SwtException {
		/* Methods local variable declaration */
		List records = null;
		Session session = null;
		Transaction tx = null;
		SwtInterceptor interceptor = null;
		/*
		 * Start Code added by Chidambaranathan for issues found on V1051 beta
		 * testing-for same data exists on 10-1-2011
		 */
		try {
			log.debug(this.getClass().getName()
					+ " - [ saveCorrespondentAcct ] - Entering ");
			/*
			 * Start : Modified for Mantis 1366-Remove entry to log if duplicate
			 * records added by betcy on 18-02-2011
			 */
			/* Query to fetches the records */
			records = (List ) getHibernateTemplate().find(
							"from CorrespondentAcct a where a.id.hostId=?0 and a.id.entityId=?1  and a.id.messageType=?2"
									+ " and a.id.currencyCode=?3 and a.id.corresAccId=?4 ",
							new Object[] { acct.getId().getHostId(),
									acct.getId().getEntityId(),
									acct.getId().getMessageType(),
									acct.getId().getCurrencyCode(),
									acct.getId().getCorresAccId() });
			/* Condition to check list size */
			if (records.size() == 0) {
				interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");
				session = getHibernateTemplate()
						.getSessionFactory()
						.withOptions().interceptor(interceptor).openSession();
				tx = session.beginTransaction();
				session.save(acct);
				tx.commit();
			
			} else {
				throw new SwtException(
						"errors.DataIntegrityViolationExceptioninAdd");
			}
			/*
			 * End : Modified for Mantis 1366-Remove entry to log if duplicate
			 * records added by betcy on 18-02-2011
			 */
			log.debug(this.getClass().getName()
					+ " - [ saveCorrespondentAcct ] - Exiting ");
		} catch (Exception exp) {
			log.error(this.getClass().getName()
							+ " - Exception Catched in [saveCorrespondentAcct] method : - "
							+ exp.getMessage());

			throw SwtErrorHandler.getInstance().handleException(exp,
					"saveCorrespondentAcct",
					CorrespondentAcctMaintenanceDAOHibernate.class);
		} finally {
			records = null;
			JDBCCloser.close(session);
		}

		/*
		 * End Code added by Chidambaranathan for issues found on V1051 beta
		 * testing-for same data exists on 10-1-2011
		 */
	}

	/**
	 * Updates the persistent storage with the existing CorrespondentAcct object
	 * given. The PK data contained therein is used to locate the record to
	 * update.
	 * 
	 * @param acct
	 * @throws SwtException
	 */
	public void updateRecord(CorrespondentAcct newAcct) throws SwtException {
		log.debug(this.getClass().getName() + " - [ updateRecord ] - Entry ");
		Session session = null;
		Transaction tx = null;
		SwtInterceptor interceptor = null;
		
		try {
			interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");
			session = getHibernateTemplate().getSessionFactory().withOptions().interceptor(interceptor).openSession();
			tx = session.beginTransaction();
			session.update(newAcct);
			tx.commit();
		} catch (Exception e) {
			SwtErrorHandler swtErrorHandler = SwtErrorHandler.getInstance();
			SwtException swtexp = swtErrorHandler.handleException(e,
					"updateRecord", CorrespondentAcctMaintenanceDAO.class);
			throw swtexp;
		}  finally {
			JDBCCloser.close(session);
		}
		log.debug(this.getClass().getName() + " - [ updateRecord ] - Exit ");
	}


	/**
	 * To find list of accounts This function appends various filtering
	 * condition in the select query
	 */
	public Collection getAccountIdDetails(String hostId, String entityId,
			String currencyCode) throws SwtException {
		log.debug(this.getClass().getName()
				+ " - [ getAccountIdDetails ] - Entry ");
		List accountIdList = null;
		try {
			if (currencyCode != null
					&& currencyCode.equalsIgnoreCase(SwtConstants.ALL_VALUE)) {
				accountIdList = (List ) getHibernateTemplate().find(
								"from AcctMaintenance acct where acct.id.hostId = ?0 and acct.id.entityId = ?1 order by acct.id.accountId",
								new Object[] { hostId, entityId });
			} else {
				accountIdList = (List ) getHibernateTemplate().find(
								"from AcctMaintenance acct where acct.id.hostId = ?0 and acct.id.entityId = ?1 and acct.currcode = ?2 order by acct.id.accountId",
								new Object[] { hostId, entityId, currencyCode });
			}
		} catch (Exception e) {
			SwtErrorHandler swtErrorHandler = SwtErrorHandler.getInstance();
			SwtException swtexp = swtErrorHandler.handleException(e,
					"getAccountIdDetails",
					CorrespondentAcctMaintenanceDAO.class);
			throw swtexp;
		}
		log.debug(this.getClass().getName()
				+ " - [ getAccountIdDetails ] - Exit ");
		return accountIdList;
	}

	/**
	 * 
	 * @return
	 * @throws SwtException
	 */
	public Collection<String> getMessageTypesList() throws SwtException {
		log.debug(this.getClass().getName()
				+ " - [ getMessageTypesList ] - Entry ");
		List<String> messageTypes = null;
		Connection conn = null;
		Statement stmt = null;
		ResultSet rs = null;
		StringBuffer query = null;
		try {
			conn = ConnectionManager.getInstance().databaseCon();
			stmt = conn.createStatement();
			messageTypes = new ArrayList<String>();
			// pass the query in string buffer
			query = new StringBuffer(
					"SELECT UPPER(MESSAGEID) FROM I_INTERFACEMESSAGE UNION SELECT UPPER(INTERFACEID) FROM I_INTERFACE");
			rs = stmt.executeQuery(query.toString());

			/* Getting the accountId,accountName and put it in List */

			while (rs.next()) {
				messageTypes.add(rs.getString(1));
			}
		} catch (Exception e) {
			SwtErrorHandler swtErrorHandler = SwtErrorHandler.getInstance();
			SwtException swtexp = swtErrorHandler.handleException(e,
					"getMessageTypesList",
					CorrespondentAcctMaintenanceDAO.class);
			throw swtexp;
		}
		// Start Code added by Chidambaranathan for issue found on 20-01-2011 -
		// V1051 beta testing-closing connection
		finally {
			
			//edited by KaisBS for mantis 1745 : introducing the implementation of the JDBCCloser class
			JDBCCloser.close(rs, stmt, null, null);
					
			try{
				if ((conn != null) && (!conn.isClosed()))
					ConnectionManager.getInstance().retrunConnection(conn);
			} catch (Exception e) {
				logger.error("org.swallow.maintenance.dao.CorrespondentAcctMaintenanceDAO - " +
						"[getMessageTypesList] - Exception - " + e.getMessage());  
			}
		}
		log.debug(this.getClass().getName()
				+ " - [ getMessageTypesList ] - Exit ");
		return messageTypes;
	}
	// End Code added by Chidambaranathan for issue found on 20-01-2011 - V1051
	// beta testing-closing connection
}