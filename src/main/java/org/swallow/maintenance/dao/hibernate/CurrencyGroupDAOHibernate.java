/*
 * @(#)CurrencyGroupDAOHibernate.java 1.0 23/08/06
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.maintenance.dao.hibernate;

import java.util.Collection;
import java.util.Iterator;
import java.util.List;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.maintenance.dao.CurrencyGroupDAO;
import org.swallow.maintenance.model.CurrencyGroup;
import org.swallow.util.JDBCCloser;
import org.swallow.util.SwtInterceptor;
import org.swallow.util.SwtUtil;
import org.springframework.context.annotation.Lazy;
import org.springframework.orm.hibernate5.support.HibernateDaoSupport;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.stereotype.Repository;
import org.springframework.beans.factory.annotation.Autowired;
import org.hibernate.HibernateException;
import org.hibernate.SessionFactory;
import org.hibernate.Transaction;
import org.hibernate.Session;




/**
 * 
 * This is DAO class for Currency Group screen.
 * 
 */
@Repository ("currencyGroupDAO")
@Transactional
public class CurrencyGroupDAOHibernate extends HibernateDaoSupport implements
		CurrencyGroupDAO {
	public CurrencyGroupDAOHibernate(@Lazy SessionFactory sessionfactory) {
	    setSessionFactory(sessionfactory);
	}

	/**
	 * Initializing logger object for this class
	 */
	private final Log log = LogFactory.getLog(CurrencyGroupDAOHibernate.class);

	/**
	 * Retrieves the list of currency group from the dataBase table
	 * S_CURRENCY_GROUP
	 * 
	 * @param hostId
	 * @param entityId
	 * @return Collection
	 * @throws SwtException
	 */
	public Collection getCurrencyGroupList(String hostId, String entityId)
			throws SwtException {
		try {
			log.debug(this.getClass().getName()
					+ " - [getCurrencyGroupList] - " + "Entry");
			/* Method's local variable declaration */
			List coll;
			/*
			 * Retrieve and store the collection of currency group details from
			 * currency group table
			 */
			coll = (List ) getHibernateTemplate().find(
							"from CurrencyGroup c where c.id.hostId = ?0 and c.id.entityId=?1",
							new Object[] { hostId, entityId });

			log.debug(this.getClass().getName()
					+ " - [getCurrencyGroupList] - " + "Exit");
			return coll;
		} catch (Exception exp) {
			log.debug(this.getClass().getName()
							+ " - Exception Catched in [getCurrencyGroupList] method : - "
							+ exp.getMessage());

			log.error(this.getClass().getName()
							+ " - Exception Catched in [getCurrencyGroupList] method : - "
							+ exp.getMessage());
			exp.printStackTrace();
			throw SwtErrorHandler.getInstance().handleException(exp,
					"getCurrencyGroupList", CurrencyGroupDAOHibernate.class);
		}
	}

	/**
	 * Get the number of currencies for the currency group from the Database
	 * table S_CURRENCY
	 * 
	 * @param hostId
	 * @param entityId
	 * @param currencyGroupId
	 * @return Integer
	 * @throws SwtException
	 */
	public Integer getNoOfCurrencies(String hostId, String entityId,
			String currencyGroupId) throws SwtException {
		try {
			log.debug(this.getClass().getName() + " - [getNoOfCurrencies] - "
					+ "Entry");
			/* Method's local variable declaration */
			List coll = null;
			Integer noOfRecords = null;
			/* Retrieves and store the list of currencies in the Collection */
			coll = (List ) getHibernateTemplate().find(
							"select count(*) from Currency c where c.id.hostId = ?0 and "
									+ "c.id.entityId=?1 and c.currencyGroupId=?2 and c.id.currencyCode != '*'",
							new Object[] { hostId, entityId, currencyGroupId });

			noOfRecords = new Integer(0);

			/* Condition to check the collection is null */
			if (coll != null) {
				/* The total number of records is stored in the Integer object */
				noOfRecords = ((Long) coll.get(0)).intValue();
			}
			log.debug(this.getClass().getName() + " - [getNoOfCurrencies] - "
					+ "Exit");

			return noOfRecords;
		} catch (Exception exp) {
			log.debug(this.getClass().getName()
					+ " - Exception Catched in [getNoOfCurrencies] method : - "
					+ exp.getMessage());

			log.error(this.getClass().getName()
					+ " - Exception Catched in [getNoOfCurrencies] method : - "
					+ exp.getMessage());
			exp.printStackTrace();
			throw SwtErrorHandler.getInstance().handleException(exp,
					"getNoOfCurrencies", CurrencyGroupDAOHibernate.class);
		}
	}

	/**
	 * Save New CurrencyGroup Details for an entity into the Database table
	 * S_CURRENCY_GROUP
	 * 
	 * @param currencyGroup
	 * @return
	 * @throws SwtException
	 */
	@SuppressWarnings("unchecked")
	public void saveCurrencyGroupDetails(CurrencyGroup currencyGroup)
			throws SwtException {
		/*Methods local variable declaration*/
		List records =null;
		Session session = null;
		Transaction tx = null;
		SwtInterceptor interceptor = null;
		try {
			log.debug(this.getClass().getName()
					+ " - [saveCurrencyGroupDetails] - " + "Entry");
			/*
			 * Start : Modified for Mantis 1366-Remove entry to log if duplicate
			 * records added by betcy on 18-02-2011
			 */
			records = getHibernateTemplate()
					.find(
							"from CurrencyGroup c where c.id.hostId = ?0 and c.id.entityId=?1 and c.id.currencyGroupId=?2 ",
							new Object[] { currencyGroup.getId().getHostId(),
									currencyGroup.getId().getEntityId(),
									currencyGroup.getId().getCurrencyGroupId() });
			/*Condition to check list size*/
			if (records.size() == 0) {
				/* Save the currencyGroup In database through hibernate */
				interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");
				session = getHibernateTemplate()
							.getSessionFactory()
							.withOptions().interceptor(interceptor).openSession();
				tx = session.beginTransaction();
				session.save(currencyGroup);
				tx.commit();
				log.debug(this.getClass().getName()
						+ " - [saveCurrencyGroupDetails] - " + "Exit");
			} else {
				throw new SwtException(
						"errors.DataIntegrityViolationExceptioninAdd");
			}
			/*
			 * End : Modified for Mantis 1366-Remove entry to log if duplicate
			 * records added by betcy on 18-02-2011
			 */
		} catch (Exception exp) {
			/*
			 * Start : Modified for Mantis 1366-Remove entry to log if duplicate
			 * records added by betcy on 18-02-2011
			 */
			log.error(this.getClass().getName()
							+ " - Exception Catched in [saveCurrencyGroupDetails] method : - "
							+ exp.getMessage());
			throw SwtErrorHandler.getInstance()
					.handleException(exp, "saveCurrencyGroupDetails",
							CurrencyGroupDAOHibernate.class);
			/*
			 * End : Modified for Mantis 1366-Remove entry to log if duplicate
			 * records added by betcy on 18-02-2011
			 */
		}finally
		{
			JDBCCloser.close(session);
			records=null;
		}
	}

	/**
	 * Update currency group details in S_CURRENCY_GROUP table
	 * 
	 * @param currencyGroup
	 * @return
	 * @throws SwtException
	 */
	public void updateCurrencyGroupDetails(CurrencyGroup currencyGroup)
			throws SwtException {
		Session session = null;
		Transaction tx = null;
		SwtInterceptor interceptor = null;
		try {
			log.debug(this.getClass().getName()
					+ " - [updateCurrencyGroupDetails] - " + "Entry");
			/* Update the currencyGroup details in database */
			interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");
			session = getHibernateTemplate().getSessionFactory().withOptions().interceptor(interceptor).openSession();
			tx = session.beginTransaction();
			session.update(currencyGroup);
			tx.commit();
			log.debug(this.getClass().getName()
					+ " - [updateCurrencyGroupDetails] - " + "Exit");
		} catch (Exception exp) {
			log.debug(this.getClass().getName()
							+ " - Exception Catched in [updateCurrencyGroupDetails] method : - "
							+ exp.getMessage());

			log.error(this.getClass().getName()
							+ " - Exception Catched in [updateCurrencyGroupDetails] method : - "
							+ exp.getMessage());
			exp.printStackTrace();
			throw SwtErrorHandler.getInstance().handleException(exp,
					"updateCurrencyGroupDetails",
					CurrencyGroupDAOHibernate.class);
		}  finally {
			JDBCCloser.close(session);
		}

	}

	/**
	 * Delete the CurrencyGroup Details from S_CURRENCY_GROUP table
	 * 
	 * @param currencyGroup
	 * @return
	 * @throws SwtException
	 */
	public void deleteCurrencyGroupRecord(CurrencyGroup currencyGroup)
			throws SwtException {
		Session session = null;
		Transaction tx = null;
		SwtInterceptor interceptor = null;
		try {
			log.debug(this.getClass().getName()
					+ " - [deleteCurrencyGroupRecord] - " + "Entry");
			/* Delete the CurrencyGroup Details from DataBase */
			interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");
			session = getHibernateTemplate().getSessionFactory().withOptions().interceptor(interceptor).openSession();
			tx = session.beginTransaction();
			session.delete(currencyGroup);
			tx.commit();
			log.debug(this.getClass().getName()
					+ " - [deleteCurrencyGroupRecord] - " + "Exit");
		} catch (Exception exp) {
			log.debug(this.getClass().getName()
							+ " - Exception Catched in [deleteCurrencyGroupRecord] method : - "
							+ exp.getMessage());

			log.error(this.getClass().getName()
							+ " - Exception Catched in [deleteCurrencyGroupRecord] method : - "
							+ exp.getMessage());
			exp.printStackTrace();
			throw SwtErrorHandler.getInstance().handleException(exp,
					"deleteCurrencyGroupRecord",
					CurrencyGroupDAOHibernate.class);
		}  finally {
			JDBCCloser.close(session);
		}

	}

	/**
	 * Get the list of currencies for the currency group from the Database table
	 * S_CURRENCY
	 * 
	 * @param hostId
	 * @param entityId
	 * @param currGrpId
	 * @return Collection
	 * @throws SwtException
	 */
	public Collection getCurrencyGroupCurrenciesList(String hostId,
			String entityId, String currGrpId) throws SwtException {
		try {
			log.debug(this.getClass().getName()
					+ " - [getCurrencyGroupCurrenciesList] - " + "Entry");
			/* Method's local variable declaration */
			List coll;
			/* Collects the list of currencies for currencyGroup List */
			coll = (List ) getHibernateTemplate().find(
					"from Currency c "
							+ "where c.id.hostId =?0 and c.id.entityId=?1 and "
							+ "c.currencyGroupId =?2 and "
							+ "c.id.currencyCode != '*'",
					new Object[] { hostId, entityId, currGrpId });

			log.debug(this.getClass().getName()
					+ " - [getCurrencyGroupCurrenciesList] - " + "Exit");
			return coll;
		} catch (Exception exp) {
			log.debug(this.getClass().getName()
							+ " - Exception Catched in [getCurrencyGroupCurrenciesList] method : - "
							+ exp.getMessage());

			log.error(this.getClass().getName()
							+ " - Exception Catched in [getCurrencyGroupCurrenciesList] method : - "
							+ exp.getMessage());
			exp.printStackTrace();
			throw SwtErrorHandler.getInstance().handleException(exp,
					"getCurrencyGroupCurrenciesList",
					CurrencyGroupDAOHibernate.class);
		}
	}

	/**
	 * Get the currency group Name from DataBase table S_CURRENCY_GROUP, This
	 * method is used in SwtMaintenanceCache
	 * 
	 * @param entityId
	 * @param currencyGroupId
	 * @return StringCurrencyGroupName
	 * @throws SwtException
	 */
	public String getCurrencyGroupName(String entityId, String currencyGroupId)
			throws SwtException {
		try {
			log.debug(this.getClass().getName()
					+ " - [getCurrencyGroupName] - " + "Entry");

			/* Method's local variable declaration */
			String currencyGroupName = "";
			String hostId;
			List coll;

			/* Retrieve the current host id from SwtUtil */
			hostId = SwtUtil.getCurrentHostId();

			/* Retrieves and store the collection of currency group */
			coll = (List ) getHibernateTemplate().find(
							"from CurrencyGroup c "
									+ "where c.id.hostId =?0 and c.id.entityId=?1 and c.id.currencyGroupId =?2",
							new Object[] { hostId, entityId, currencyGroupId });

			/* Condition to check the collection is null */
			if (coll != null) {
				/* Iterate and store the collection */
				Iterator itr = (Iterator) coll.iterator();
				/* Condition to check the iterator has the next value */
				if (itr.hasNext()) {
					/* Get the currencyGroupName and store in currencyGroup bean */
					currencyGroupName = ((CurrencyGroup) itr.next())
							.getCurrencyGroupName();
				}
			}

			log.debug(this.getClass().getName()
					+ " - [getCurrencyGroupName] - " + "Exit");

			return currencyGroupName;
		} catch (Exception exp) {
			log.debug(this.getClass().getName()
							+ " - Exception Catched in [getCurrencyGroupName] method : - "
							+ exp.getMessage());

			log.error(this.getClass().getName()
							+ " - Exception Catched in [getCurrencyGroupName] method : - "
							+ exp.getMessage());
			exp.printStackTrace();
			throw SwtErrorHandler.getInstance().handleException(exp,
					"getCurrencyGroupName", CurrencyGroupDAOHibernate.class);
		}
	}
}
