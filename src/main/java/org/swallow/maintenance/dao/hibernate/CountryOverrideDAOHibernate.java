/*
 * @(#)CountryOverrideDAOHibernate.java 1.0 28/02/2010
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */

package org.swallow.maintenance.dao.hibernate;

import java.sql.CallableStatement;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Collection;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.hibernate.HibernateException;
import org.hibernate.Session;
import org.hibernate.SessionFactory;
import org.hibernate.Transaction;
import org.springframework.context.annotation.Lazy;
import org.springframework.orm.hibernate5.support.HibernateDaoSupport;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;
import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.maintenance.dao.CountryOverrideDAO;
import org.swallow.maintenance.model.CountryOverride;
import org.swallow.util.JDBCCloser;
import org.swallow.util.LabelValueBean;
import org.swallow.util.SwtInterceptor;
import org.swallow.util.SwtUtil;

/**
 * <AUTHOR> A
 * 
 * Data Access Object Implementations layer for S_COUNTRY_OVERRIDE
 */
@Repository ("countryOverrideDAO")
@Transactional
public class CountryOverrideDAOHibernate extends HibernateDaoSupport implements
		CountryOverrideDAO {
	public CountryOverrideDAOHibernate(@Lazy SessionFactory sessionfactory) {
	    setSessionFactory(sessionfactory);
	}

	/* Final instance for log */
	private final Log log = LogFactory.getLog(this.getClass());

	/**
	 * Method to get list of non working days
	 * 
	 * @return Collection
	 * @throws SwtException
	 */
	public Collection<LabelValueBean> getDayList() throws SwtException {

		// Hibarnate Session instance
		Session session = null;
		// Connection instance
		Connection conn = null;
		// Callable Statement Instance
		CallableStatement cstmt = null;
		// Result Set Instance
		ResultSet rs = null;
		// Label Value Bean Collection to hold list of non working day
		Collection<LabelValueBean> dayListColl = null;

		try {

			log.debug(this.getClass().getName() + " - [getDayList] - Entry");
			// Initializing Array list
			dayListColl = new ArrayList<LabelValueBean>();
			// Add empty value for label and value
			dayListColl.add(new LabelValueBean("", ""));

			// Open hibernate session
			session = getHibernateTemplate().getSessionFactory().openSession();
			// Get Database connection from hibernate
			conn = SwtUtil.connection(session);
			// call non working day list
			cstmt = conn.prepareCall("{call pkg_non_workday.DaysList(?)}");
			// Set cursor for output
			cstmt.registerOutParameter(1, oracle.jdbc.OracleTypes.CURSOR);
			// execute statement
			cstmt.execute();
			// Get result set
			rs = (ResultSet) cstmt.getObject(1);

			// condition to check result set not null
			if (rs != null) {

				while (rs.next()) {
					// Set label and value in collection
					dayListColl.add(new LabelValueBean(rs.getString(2), rs
							.getString(1)));
				}

			}

		} catch (SQLException sqlException) {

			log.error("sqlException in CountryOverrideDAOHibernate.getDayList");
			throw SwtErrorHandler.getInstance().handleException(sqlException,
					"getDayList", this.getClass());
		} catch (HibernateException hibernateException) {

			log
					.error("hibernateException in CountryOverrideDAOHibernate.getDayList");
			throw SwtErrorHandler.getInstance().handleException(
					hibernateException, "getDayList", this.getClass());
		} catch (Exception exception) {

			log.error("exception in CountryOverrideDAOHibernate.getDayList");
			throw SwtErrorHandler.getInstance().handleException(exception,
					"getDayList", this.getClass());
		} finally {
			
			//edited by KaisBS for mantis 1745 : introducing the implementation of the JDBCCloser class
			SwtException thrownException = null;
			Object[] exceptions = JDBCCloser.close(rs, cstmt, conn, session);

			if (exceptions[0] != null)
			    thrownException = SwtErrorHandler.getInstance().handleException((SQLException) exceptions[0], "getDayList", this.getClass());

			if (thrownException == null && exceptions[1] !=null) 
			    thrownException = SwtErrorHandler.getInstance().handleException((HibernateException) exceptions[1], "getDayList", this.getClass());
			
			if(thrownException!=null)
				throw thrownException;
			
		}

		// return day list collection
		return dayListColl;

	}

	/**
	 * Method to get country list
	 * 
	 * @param entity
	 * @param hostId
	 * @return Collection
	 * @throws SwtException
	 */
	public Collection<CountryOverride> getCountryList(String entity,
			String hostId) throws SwtException {

		// Hibarnate Session instance
		Session session = null;
		// Connection instance
		Connection conn = null;
		// Callable Statement Instance
		CallableStatement cstmt = null;
		// Result Set Instance
		ResultSet rs = null;
		// Collection to hold country over ride list
		Collection<CountryOverride> countryOverrideColl = null;

		try {

			log
					.debug(this.getClass().getName()
							+ " - [getCountryList] - Entry");

			// initialize country override list
			countryOverrideColl = new ArrayList<CountryOverride>();
			// Obtain session from hibernate
			session = getHibernateTemplate().getSessionFactory().openSession();
			// Get datat base connection from hibernate sesssion
			conn = SwtUtil.connection(session);
			// call country maintenance function
			cstmt = conn
					.prepareCall("{call pkg_non_workday.CountryMaintenance(?,?,?)}");
			// pass host Id
			cstmt.setString(1, hostId);
			// pass Entity Id
			cstmt.setString(2, entity);
			// Set cursor to hold values
			cstmt.registerOutParameter(3, oracle.jdbc.OracleTypes.CURSOR);
			// execute statement
			cstmt.execute();
			// get result set
			rs = (ResultSet) cstmt.getObject(3);

			// Condition to check result set not null
			if (rs != null) {
				// set values in country override bean
				while (rs.next()) {
					CountryOverride countryOverride = new CountryOverride();
					countryOverride.getId().setCountryCode(rs.getString(1));
					countryOverride.setCountryName(rs.getString(2));
					countryOverride.setWeekend1(rs.getString(3));
					countryOverride.setWeekend2(rs.getString(4));
					countryOverride.setOverrideWeekend1(rs.getString(5));
					countryOverride.setOverrideWeekend2(rs.getString(6));
					countryOverrideColl.add(countryOverride);
				}

			}

		} catch (SQLException sqlException) {
			log
					.error("sqlException in CountryOverrideDAOHibernate.getCountryList");
			sqlException.printStackTrace();
			throw SwtErrorHandler.getInstance().handleException(sqlException,
					"getCountryList", this.getClass());
		} catch (HibernateException hibernateException) {
			log
					.error("hibernateException in CountryOverrideDAOHibernate.getCountryList");
			hibernateException.printStackTrace();
			throw SwtErrorHandler.getInstance().handleException(
					hibernateException, "getCountryList", this.getClass());
		} catch (Exception exception) {
			log
					.error("exception in CountryOverrideDAOHibernate.getCountryList");
			throw SwtErrorHandler.getInstance().handleException(exception,
					"getCountryList", this.getClass());
		} finally {
			
			//edited by KaisBS for mantis 1745 : introducing the implementation of the JDBCCloser class
			SwtException thrownException = null;
			Object[] exceptions = JDBCCloser.close(rs, cstmt, conn, session);

			if (exceptions[0] != null)
			    thrownException = SwtErrorHandler.getInstance().handleException((SQLException) exceptions[0], "getCountryList", CountryOverrideDAOHibernate.class);

			if (thrownException == null && exceptions[1] !=null) 
			    thrownException = SwtErrorHandler.getInstance().handleException((HibernateException) exceptions[1], "getCountryList", CountryOverrideDAOHibernate.class);

			if(thrownException!=null)
				throw thrownException;
				
			log.debug(this.getClass().getName() + " - [getCountryList] - Exit");
		}

		return countryOverrideColl;

	}

	/**
	 * Method to save or delete or update country override values in
	 * S_COUNTRY_OVERRIDE
	 * 
	 * @param countryOverride
	 * @param saveStatus
	 * @return
	 * @throws SwtException
	 */
	@SuppressWarnings("unchecked")
	public void save(CountryOverride countryOverride, String saveStatus)
			throws SwtException {
		/*
		 * Start Code added by Chidambaranathan Mantis 1461 for Save the same
		 * records on 12-May-2011
		 */
		// Collection to hold the records
		Collection<CountryOverride> records = null;
		Session session = null;
		Transaction tx = null;
		SwtInterceptor interceptor = null;
		try {
			log.debug(this.getClass().getName() + "-[save]-Entry");
			// Fetch the query for Save the same records
			records = (Collection<CountryOverride> ) getHibernateTemplate().find(
							"from CountryOverride c  where  c.id.hostId=?0 and c.id.entityId=?1 and c.id.countryCode=?2",
							new Object[] { countryOverride.getId().getHostId(),
									countryOverride.getId().getEntityId(),
									countryOverride.getId().getCountryCode() });
			if (records != null && !saveStatus.equals("delete")) {
				// Iterate country details
				for (CountryOverride country : records) {
					country.setOverrideWeekend1(countryOverride
							.getOverrideWeekend1());
					country.setOverrideWeekend2(countryOverride
							.getOverrideWeekend2());
					countryOverride = country;
				}
			}
			interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");
			session = getHibernateTemplate()
					.getSessionFactory()
					.withOptions().interceptor(interceptor).openSession();
			tx = session.beginTransaction();
		
			// Condition to check status is delete to
			if (saveStatus.equals("delete")) {
				log.debug(this.getClass().getName()
						+ "-[save]-delete country override ");
				session.delete(countryOverride);
				// Condition to check status is save
			} else if (records == null || records.size() == 0) {
				log.debug(this.getClass().getName()
						+ "-[save]- save country override ");
				session.save(countryOverride);
				// Condition to check status is update
			} else if (records.size() != 0) {
				log.debug(this.getClass().getName()
						+ "-[save]-save or update country override ");
				session.update(countryOverride);
			}
			tx.commit();
			log.debug(this.getClass().getName() + "-[save]-Exit");
		} catch (Exception exception) {
			log.error("exception in CountryOverrideDAOHibernate.save");
			throw SwtErrorHandler.getInstance().handleException(exception,
					"save", this.getClass());
		} finally {
			records = null;
			JDBCCloser.close(session);
		}
		/*
		 * End Code added by Chidambaranathan for Mantis 1461 for Save the
		 * existing records on 12-May-2011
		 */
	}

}
