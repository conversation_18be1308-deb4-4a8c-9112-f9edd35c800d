/*
 * @(#) CurrencyAliasDAOHibernate .java 01/10/07
 * 
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 * 
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */

package org.swallow.maintenance.dao.hibernate;

import java.util.Collection;
import java.util.List;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.maintenance.dao.CurrencyAliasDAO;
import org.swallow.maintenance.model.CurrencyAlias;
import org.swallow.util.JDBCCloser;
import org.swallow.util.SwtInterceptor;
import org.swallow.util.SwtUtil;
import org.springframework.context.annotation.Lazy;
import org.springframework.orm.hibernate5.support.HibernateDaoSupport;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.stereotype.Repository;
import org.springframework.beans.factory.annotation.Autowired;
import org.hibernate.HibernateException;
import org.hibernate.SessionFactory;
import org.hibernate.Transaction;
import org.hibernate.Session;





/*
 * This is DAO class for Currency alias screen
 * 
 */
@Repository ("currencyAliasDAO")
@Transactional
public class CurrencyAliasDAOHibernate extends HibernateDaoSupport implements
		CurrencyAliasDAO {
	public CurrencyAliasDAOHibernate(@Lazy SessionFactory sessionfactory) {
	    setSessionFactory(sessionfactory);
	}


	private final Log log = LogFactory.getLog(CurrencyAliasDAOHibernate.class);

	public Collection getCurrencyAliasList(String hostId, String entityId)
			throws SwtException {
		log
				.debug("Entering into CurrencyAliasDAOHibernate.getCurrencyAliasList method");
		StringBuffer queryBuffer = new StringBuffer();
		queryBuffer
				.append("select ca.id.alias, ca.currencyCode, cm.currencyName ");
		queryBuffer.append("from CurrencyAlias ca, CurrencyMaster cm ");
		queryBuffer.append("where ca.id.hostId = ?0 ");
		queryBuffer.append("and ca.id.entityId = ?1 ");
		queryBuffer.append("and ca.currencyCode = cm.currencyCode");
		List coll = getHibernateTemplate().find(queryBuffer.toString(),
				new Object[] { hostId, entityId });
		log.debug("Exiting from CurrencyAliasDAOHibernate.getCurrencyAliasList method");
		return coll;
	}

	/**
	 * This is used to save the records in database
	 * 
	 * @param currencyAlias
	 * @return none
	 * @throws SwtException
	 */
	@SuppressWarnings("unchecked")
	public void saveCurrencyAliasDetails(CurrencyAlias currencyAlias)
			throws SwtException {
		/* Methods local variable declaration */
		List records = null;
		Session session = null;
		Transaction tx = null;
		SwtInterceptor interceptor = null;
		try {
			log.debug(this.getClass().getName() + " - [save] - " + "Entering");
			/*
			 * Start : Modified for Mantis 1366-Remove entry to log if duplicate
			 * records added by betcy on 18-02-2011
			 */
			/* Query to execute the records */
			records = (List ) getHibernateTemplate().find(
							"from  CurrencyAlias ca where  ca.id.hostId=?0 and ca.id.entityId=?1 and ca.id.alias=?2",
							new Object[] { currencyAlias.getId().getHostId(),
									currencyAlias.getId().getEntityId(),
									currencyAlias.getId().getAlias() });
			/* Condition to check list size */
			if (records.size() == 0) {
				interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");
				session = getHibernateTemplate()
						.getSessionFactory()
						.withOptions().interceptor(interceptor).openSession();
				tx = session.beginTransaction();
				session.save(currencyAlias);
				tx.commit();
			
			} else {
				throw new SwtException(
						"errors.DataIntegrityViolationExceptioninAdd");
			}
			log.debug(this.getClass().getName() + " - [save] - " + "Exititng");
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					+ "- [saveCurrencyAliasDetails] - Exception "
					+ exp.getMessage());
			throw SwtErrorHandler.getInstance()
					.handleException(exp, "saveCurrencyAliasDetails",
							CurrencyAliasDAOHibernate.class);
		}  finally {
			JDBCCloser.close(session);
		}
		/*
		 * End : Modified for Mantis 1366-Remove entry to log if duplicate
		 * records added by betcy on 18-02-2011
		 */
	}

	public void deleteCurrencyAliasRecord(CurrencyAlias currencyAlias)
			throws SwtException {
		Session session = null;
		Transaction tx = null;
		SwtInterceptor interceptor = null;
		try {
			log.debug("Entering into CurrencyAliasDAOHibernate.deleteCurrencyAliasRecord method");
			interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");
			session = getHibernateTemplate().getSessionFactory().withOptions().interceptor(interceptor).openSession();
			tx = session.beginTransaction();
			session.delete(currencyAlias);
			tx.commit();
			log.debug("Exiting from CurrencyAliasDAOHibernate.deleteCurrencyAliasRecord method");
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					+ "- [saveCurrencyAliasDetails] - Exception "
					+ exp.getMessage());
			throw SwtErrorHandler.getInstance()
					.handleException(exp, "saveCurrencyAliasDetails",
							CurrencyAliasDAOHibernate.class);
		}  finally {
			JDBCCloser.close(session);
		}
	}
}
