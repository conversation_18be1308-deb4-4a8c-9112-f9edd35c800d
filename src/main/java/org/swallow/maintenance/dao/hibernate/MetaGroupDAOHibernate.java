/*
 * @(#)MetaGroupDAOHibernate.java 1.0 23/08/06
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.maintenance.dao.hibernate;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Iterator;
import java.util.List;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.orm.hibernate5.support.HibernateDaoSupport;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Repository;
import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.exception.SwtRecordNotExist;
import org.swallow.maintenance.dao.MetaGroupDAO;
import org.swallow.maintenance.model.BookCode;
import org.swallow.maintenance.model.Group;
import org.swallow.maintenance.model.MetaGroup;
import org.swallow.maintenance.model.MetaGroupLevel;
import org.swallow.util.SwtInterceptor;
import org.swallow.util.SwtUtil;
import org.hibernate.Session;
import org.hibernate.SessionFactory;
import org.hibernate.Transaction;

/**
 * 
 * This is DAO class for Metagroup screen.
 * 
 */
@Component ("metaGroupDAO")
@Repository
public class MetaGroupDAOHibernate extends HibernateDaoSupport implements
		MetaGroupDAO {
	
	
	public MetaGroupDAOHibernate(@Lazy SessionFactory sessionfactory){
	    setSessionFactory(sessionfactory);
	}
	
	
	private static final Log log = LogFactory
			.getLog(MetaGroupDAOHibernate.class);
	private static final String HQL_METAGROUPDETAILS = "from MetaGroup c where c.id.hostId = ?0 and c.id.entityId=?1 and c.mgrpLvlCode=?2";
	private static final String HQL_CHANGE = "from MetaGroup c where c.id.hostId=?0 and c.id.entityId=?1 and c.id.mgroupId=?2";
	private static final String HQL_MGRPLVLCODE = "from MetaGroupLevel a where a.id.hostId=?0 and a.id.entityId=?1 order by a.id.mgrpLvlCode";
	private static final String HQL_ALLMGRPLVLCODE = "from MetaGroupLevel a where a.id.hostId=?0";
	private static final String HQL_GROUPDETAILS = "from Group p where p.id.hostId=?0 and p.id.entityId=?1 and p.mgroupId=?2";
	private static final String HQL_NOOFBOOKCODES = "from BookCode p where p.id.hostId=?0 AND p.id.entityId=?1 AND (p.groupIdLevel1=?2 OR p.groupIdLevel2=?3 OR p.groupIdLevel3=?4) ";
	private static final String HQL_METAGROUPDETAILS_NOOFGROUPS = "from Group p where p.id.hostId=?0 and p.id.entityId=?1 and p.mgroupId=?2";

	/**
	 * This is used to get the meta group details from P_METAGROUP ,P_GROUP
	 * table
	 * 
	 * @param hostId
	 * @param entityId
	 * @param mGroupLevel
	 * @return Collection
	 */
	public Collection getMetaGroupDetails(String hostId, String entityId,
			Integer mGroupLevel) {

		log.debug(this.getClass().getName()
				+ "- [getMetaGroupDetails] - Entering");
		/* Method's local variable declaration */
		List metaGroupcoll = null;
		List groupList = null;
		String mgroupId;
		Iterator itr;
		/* Condition to check the list is null */
		if (mGroupLevel == null) {
			metaGroupcoll = new ArrayList();
		} else {
			/* Retrieve the meta group details from P_METAGROUP_LEVEL table */
			metaGroupcoll = getHibernateTemplate().find(HQL_METAGROUPDETAILS,
					new Object[] { hostId, entityId, mGroupLevel });
		}
		itr = metaGroupcoll.iterator();
		MetaGroup metaGroup = new MetaGroup();
		while (itr.hasNext()) {
			metaGroup = (MetaGroup) itr.next();
			/* Reading meta group id from bean class */
			mgroupId = metaGroup.getId().getMgroupId();
			/* Retrieve the group details from P_GROUP table */
			groupList = getHibernateTemplate().find(
					HQL_METAGROUPDETAILS_NOOFGROUPS,
					new Object[] { hostId, entityId, mgroupId });
			/* Setting the no of groups */
			metaGroup.setNoofGroups(new Integer(groupList.size()));
		}
		log.debug(this.getClass().getName()
				+ "- [getMetaGroupDetails] - Exiting");
		return metaGroupcoll;
	}

	/**
	 * This is used to save the updated meta group details
	 * 
	 * @param hostId
	 * @param entityId
	 * @param mgroupId
	 * @return MetaGroup
	 */
	public MetaGroup change(String hostId, String entityId, String mgroupId) {
		log.debug(this.getClass().getName() + "- [change] - Entering");
		/* Method's local variable declaration */
		List metaGroupcoll = null;
		metaGroupcoll = getHibernateTemplate().find(HQL_CHANGE,
				new Object[] { hostId, entityId, mgroupId });
		log.debug(this.getClass().getName() + "- [change] - Exiting");
		if (metaGroupcoll.size() == 1) {
			return (MetaGroup) (metaGroupcoll.get(0));
		} else {
			return null;

		}
	}

	/**
	 * This is used to fetches the meta group level code from P_METAGROUP_LEVEL
	 * table
	 * 
	 * @param hostId
	 * @param entityId
	 * @return Collection
	 */
	public Collection getMgrpLvlCode(String hostId, String entityId) {
		log.debug(this.getClass().getName() + "- [getMgrpLvlCode] - Entering");
		/* Method's local variable declaration */
		List metaGroupLevelList = null;
		Iterator itr = null;
		/* Class instance declaration */
		MetaGroupLevel metaGroupLvl = null;
		/* Retrieve the metagroup details from P_METAGROUP_LEVEL table */
		metaGroupLevelList = getHibernateTemplate().find(HQL_MGRPLVLCODE,
				new Object[] { hostId, entityId });
		itr = metaGroupLevelList.iterator();
		while (itr.hasNext()) {
			metaGroupLvl = (MetaGroupLevel) itr.next();
			log.debug(this.getClass().getName()
					+ "- [getMgrpLvlCode] - Exiting");
		}
		return (metaGroupLevelList != null && metaGroupLevelList.size() > 0) ? metaGroupLevelList
				: new ArrayList();

	}

	/**
	 * This is used to save the updated metagroup details in P_METAGROUP table
	 * 
	 * @param metaGroup
	 * @return
	 * @throws SwtException
	 */
	public void updateMetaGroupDetails(MetaGroup metaGroup) throws SwtException {
		Session session = null;
		Transaction tx = null;
		SwtInterceptor interceptor = null;
		try {
			log.debug(this.getClass().getName()
					+ "- [updateMetaGroupDetails] - Entering");
			interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");
			session = getHibernateTemplate().getSessionFactory().withOptions().interceptor(interceptor).openSession();;
			tx = session.beginTransaction();
			session.update(metaGroup);
			tx.commit();
			session.close();
			// this is used to flush the exception
			// to the manager implementation.
			log.debug(this.getClass().getName()
					+ "- [updateMetaGroupDetails] - Exiting");
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					+ "- [updateMetaGroupDetails] - Exception " + exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp,
					"updateMetaGroupDetails", MetaGroupDAOHibernate.class);
		}
	}

	/**
	 * This is used to save the newly added details in P_METAGROUP table
	 * 
	 * @param metaGroup
	 * @return
	 * @exception SwtException
	 */
	@SuppressWarnings("unchecked")
	public void saveMetaGroup(MetaGroup metaGroup) throws SwtException {
		/* Methods local variable declaration */
		List records = null;
		Session session = null;
		Transaction tx = null;
		SwtInterceptor interceptor = null;
		try {
			log.debug(this.getClass().getName()
					+ "- [updateMetaGroupDetails] - Entering");
			/*
			 * Start : Modified for Mantis 1366-Remove entry to log if duplicate
			 * records added by betcy on 18-02-2011
			 */
			records = getHibernateTemplate()
					.find(
							"from MetaGroup m where m.id.hostId=?0 and m.id.entityId=?1 and m.id.mgroupId=?2",
							new Object[] { metaGroup.getId().getHostId(),
									metaGroup.getId().getEntityId(),
									metaGroup.getId().getMgroupId() });
			/* Condition to check list size */
			if (records.size() == 0) {
				interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");
				session = getHibernateTemplate()
							.getSessionFactory()
							.withOptions().interceptor(interceptor).openSession();
				tx = session.beginTransaction();
				session.save(metaGroup);
				tx.commit();
				session.close();
				// this is used to flush the
												// exception to the manager
												// implementation.
				log.debug(this.getClass().getName()
						+ "- [saveMetaGroup] - Exiting");
			} else {
				throw new SwtException(
						"errors.DataIntegrityViolationExceptioninAdd");
			}
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					+ "- [saveMetaGroup] - Exception " + exp.getMessage());
			SwtErrorHandler swtErrorHandler = SwtErrorHandler.getInstance();
			SwtException swtExp = swtErrorHandler.handleException(exp,
					"saveMetaGroup", MetaGroupDAOHibernate.class);
			throw swtExp;

		} finally {
			records = null;
		}
		/*
		 * End : Modified for Mantis 1366-Remove entry to log if duplicate
		 * records added by betcy on 18-02-2011
		 */
	}

	/**
	 * This is used to delete the meta group details from P_METAGROUP table.
	 * 
	 * @param metaGroup
	 * @return
	 * @throws SwtException
	 */
	public void deleteMetaGroup(MetaGroup metaGroup) throws SwtException {
		Session session = null;
		Transaction tx = null;
		SwtInterceptor interceptor = null;
		try {
			log.debug(this.getClass().getName() + "- [deleteMetaGroup] - Entering");
			/* Method's local variable declaration */
			String deleteHQL;
			int deleteCounter;
			deleteHQL = "DELETE FROM MetaGroup m WHERE m.id.hostId = :hostId " +
	                   "AND m.id.entityId = :entityId " +
	                   "AND m.id.mgroupId = :mgroupId";
			
			log.debug(this.getClass().getName()
					+ "- [deleteMetaGroup] - Before Delete");
			interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");
			session = getHibernateTemplate().getSessionFactory().withOptions().interceptor(interceptor).openSession();;
			tx = session.beginTransaction();
			
			
			deleteCounter = session.createQuery(deleteHQL)
			        .setParameter("hostId", metaGroup.getId().getHostId())
			        .setParameter("entityId", metaGroup.getId().getEntityId())
			        .setParameter("mgroupId", metaGroup.getId().getMgroupId())
			        .executeUpdate();
			
			
			tx.commit();
			session.close();
			log.debug(this.getClass().getName() + "- [deleteMetaGroup] - Exiting");
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					+ "- [deleteMetaGroup] - Exception " + exp.getMessage());
			SwtErrorHandler swtErrorHandler = SwtErrorHandler.getInstance();
			SwtException swtExp = swtErrorHandler.handleException(exp,
					"deleteMetaGroup", MetaGroupDAOHibernate.class);
			throw swtExp;

		}
	}

	/**
	 * This is used to get the group details from P_METAGROUP, P_GROUP tables
	 * 
	 * @param hostId
	 * @param entityId
	 * @param mgroupId
	 * @return Collection
	 */
	public Collection groupDetails(String hostId, String entityId,
			String mgroupId) {
		log.debug(this.getClass().getName() + "- [groupDetails] - Entering");
		/* Method's local variable declaration */
		String groupId;
		List groupcoll = null;
		Iterator itr = null;
		List bookCodeList = null;
		/* Class instance declaration */
		Group group;
		BookCode bookcode = null;
		groupcoll = getHibernateTemplate().find(HQL_GROUPDETAILS,
				new Object[] { hostId, entityId, mgroupId });
		itr = groupcoll.iterator();
		group = new Group();
		while (itr.hasNext()) {
			group = (Group) itr.next();
			/* Retrieve the group id from bean class */
			groupId = group.getId().getGroupId();
			/* Retrieve the book code details from P_BOOKCODE table */
			bookCodeList = getHibernateTemplate()
					.find(
							HQL_NOOFBOOKCODES,
							new Object[] { hostId, entityId, groupId, groupId,
									groupId });
			/* Setting the book code */
			group.setBookCodes(bookCodeList);
			/* Setting the no of book codes */
			group.setNoOfBookCode(new Integer(bookCodeList.size()));
		}
		log.debug(this.getClass().getName() + "- [groupDetails] - Exiting");
		return groupcoll;
	}
}
