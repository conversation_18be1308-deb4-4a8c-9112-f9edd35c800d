/*
 * @(#)CountryDAOHibernate.java 1.0 23/08/06
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.maintenance.dao.hibernate;

import java.util.Collection;
import java.util.List;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.hibernate.SessionFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.orm.hibernate5.support.HibernateDaoSupport;
import org.springframework.stereotype.Repository;
import org.swallow.maintenance.dao.CountryDAO;

@Repository ("countryDAO")
public class CountryDAOHibernate extends HibernateDaoSupport implements CountryDAO {

	/**
	 * Initializing logger object for this class
	 */
	private final Log log = LogFactory.getLog(CountryDAOHibernate.class);
	
	public CountryDAOHibernate(@Lazy SessionFactory sessionfactory){
	    setSessionFactory(sessionfactory);
	}
	
	/**
	 * Method returns the collection of country list
	 * @returnCollection
	 */
	public Collection getCountries(){

		 log.debug(this.getClass().getName() + " - [getCountries] - "+ "Entry");
		 List countryList = getHibernateTemplate().find(" from Country c order by c.countryCode");
		 log.debug(this.getClass().getName() + " - [getCountries] - "+ "Exit");
		 return countryList;
        }


	}

