package org.swallow.maintenance.dao.hibernate;

import java.sql.CallableStatement;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;


import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

import org.swallow.batchScheduler.ConnectionManager;
import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.maintenance.dao.InterfaceRulesDAO;
import org.swallow.maintenance.model.InterfaceRule;
import org.swallow.util.JDBCCloser;
import org.swallow.util.SwtConstants;
import org.swallow.util.SwtInterceptor;
import org.swallow.util.SwtUtil;
import org.springframework.context.annotation.Lazy;
import org.springframework.orm.hibernate5.support.HibernateDaoSupport;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.stereotype.Repository;
import org.hibernate.HibernateException;
import org.hibernate.SessionFactory;
import org.hibernate.Transaction;
import org.hibernate.query.Query;
import org.hibernate.Session;

/**
 * This class is used to interact with DB for add, delete, view the Interface
 * Rules.
 */
@Repository ("interfaceRulesDAO")
@Transactional
public class InterfaceRulesDAOHibernate extends HibernateDaoSupport implements
		InterfaceRulesDAO {
	public InterfaceRulesDAOHibernate(@Lazy SessionFactory sessionfactory) {
	    setSessionFactory(sessionfactory);
	}

	private final Log log = LogFactory.getLog(InterfaceRulesDAOHibernate.class);

	/**
	 * Queries hibernate for a collection of InterfaceRule objects.
	 * 
	 * @return Collection
	 * @throws SwtException
	 */
	@SuppressWarnings("unchecked")
	public Collection<InterfaceRule> getList() throws SwtException {
		log.debug(this.getClass().getName() + " - [ getList ] - Entry ");
		Collection<InterfaceRule> list = null;
		try {
			list = (Collection<InterfaceRule> ) getHibernateTemplate().find(
					"from InterfaceRule ir order by ir.id.messageType");
			log.debug(this.getClass().getName() + " - [ getList ] - Exit ");
			return list;
		} catch (Exception e) {
			SwtErrorHandler swtErrorHandler = SwtErrorHandler.getInstance();
			SwtException swtexp = swtErrorHandler.handleException(e, "getList",
					InterfaceRulesDAO.class);
			throw swtexp;
		}
	}

	/**
	 * Queries hibernate for a collection of Interface rules objects matching
	 * the supplied message type parameter
	 * 
	 * @param messageType
	 * @return Collection
	 * @throws SwtException
	 */
	/*
	 * Code modified by Venkat on 24-Feb-2011 for mantis 1365:"Allow Special
	 * characters in Interface Rules Screen."
	 */
	@SuppressWarnings("unchecked")
	public Collection<InterfaceRule> getInterfaceRulesList(String messageType,
			String ruleId, String partialRuleId) throws SwtException {
		Collection<InterfaceRule> list = null;
		StringBuffer query = new StringBuffer();
		int index = 0;
		int paramIndex = 0;
		String ruleQuery = null;
		boolean addRuleIdSearch = false;
		try {
			query.append("from InterfaceRule ir ");
			ruleQuery = SwtConstants.EMPTY_STRING;
			if (ruleId != null
					&& !ruleId.equalsIgnoreCase(SwtConstants.EMPTY_STRING)) {
				addRuleIdSearch = true;
				/*
				 * Start:code modified by venkat on 11-jan-2010 for issues found
				 * on V1051 beta testing- interface rules issues.
				 */
				ruleId = ruleId.toUpperCase();
				/*
				 * End:code modified by venkat on 11-jan-2010 for issues found
				 * on V1051 beta testing- interface rules issues.
				 */
				/*
				 * Start:Code modified by Venkat on 24-Feb-2011 for mantis
				 * 1365:"Allow Special characters in Interface Rules Screen."
				 */
				if (partialRuleId != null
						&& partialRuleId.equalsIgnoreCase(SwtConstants.YES)) {
					/*
					 * End:Code modified by Venkat on 24-Feb-2011 for mantis
					 * 1365:"Allow Special characters in Interface Rules
					 * Screen."
					 */
					ruleQuery = " UPPER(ir.id.ruleId) LIKE UPPER(?"+paramIndex+") ";
					paramIndex++;
				} else {
					ruleQuery = " UPPER(ir.id.ruleId) = ?"+paramIndex+" ";
					paramIndex++;
				}
			}
			if (messageType != null && messageType.trim().length() > 0) {
				index = messageType.indexOf("%");
				if (index != -1) {
					query
							.append(" where UPPER(ir.id.messageType) LIKE UPPER(?"+paramIndex+")");
				} else {
					query.append(" where UPPER(ir.id.messageType)=UPPER(?"+paramIndex+")");
				}
				if (addRuleIdSearch) {
					query.append(" AND " + ruleQuery
							+ " ORDER BY ir.id.messageType");
					list = (Collection<InterfaceRule> ) getHibernateTemplate().find(query.toString(),
							new Object[] { messageType, ruleId });
				} else {
					query.append(" ORDER BY ir.id.messageType");
					list = (Collection<InterfaceRule> ) getHibernateTemplate().find(query.toString(),
							new Object[] { messageType });
				}
			} else {
				if (addRuleIdSearch) {
					query.append(" WHERE " + ruleQuery
							+ "order by ir.id.messageType");
					list = (Collection<InterfaceRule> ) getHibernateTemplate().find(query.toString(),
							new Object[] { ruleId });
				} else {
					query.append("order by ir.id.messageType");
					list = (Collection<InterfaceRule> ) getHibernateTemplate().find(query.toString());
				}
			}
			log.debug(this.getClass().getName()
					+ " - [ getInterfaceRulesList ] - Exit ");
			return list;
		} catch (Exception e) {
			SwtErrorHandler swtErrorHandler = SwtErrorHandler.getInstance();
			SwtException swtexp = swtErrorHandler.handleException(e,
					"getInterfaceRulesList", InterfaceRulesDAO.class);
			throw swtexp;
		} finally {
			try {
				query = null;
				ruleQuery = null;
			} catch (Exception e) {
				SwtErrorHandler swtErrorHandler = SwtErrorHandler.getInstance();
				SwtException swtexp = swtErrorHandler.handleException(e,
						"getInterfaceRulesList", InterfaceRulesDAO.class);
				throw swtexp;
			}
		}
	}

	@SuppressWarnings("unchecked")
	public Collection<InterfaceRule> getInterfaceRulesList(String messageType)
			throws SwtException {
		Collection<InterfaceRule> list = null;
		StringBuffer query = new StringBuffer();
		int index = 0;
		try {
			query.append("from InterfaceRule ir ");

			if (messageType != null && messageType.trim().length() > 0) {
				index = messageType.indexOf("%");
				if (index != -1) {
					query
							.append(" where UPPER(ir.id.messageType) LIKE UPPER(?0) ORDER BY ir.id.messageType");
				} else {
					query
							.append(" where UPPER(ir.id.messageType)=UPPER(?0) ORDER BY ir.id.messageType");
				}
				list = (Collection<InterfaceRule> ) getHibernateTemplate().find(query.toString(),
						new Object[] { messageType });
			} else {
				query.append("order by ir.id.messageType");
				list = (Collection<InterfaceRule> ) getHibernateTemplate().find(query.toString());
			}

			log.debug(this.getClass().getName()
					+ " - [ getInterfaceRulesList ] - Exit ");
			return list;
		} catch (Exception e) {
			SwtErrorHandler swtErrorHandler = SwtErrorHandler.getInstance();
			SwtException swtexp = swtErrorHandler.handleException(e,
					"getInterfaceRulesList", InterfaceRulesDAO.class);
			throw swtexp;
		} finally {
			try {
				query = null;
			} catch (Exception e) {
				SwtErrorHandler swtErrorHandler = SwtErrorHandler.getInstance();
				SwtException swtexp = swtErrorHandler.handleException(e,
						"getInterfaceRulesList", InterfaceRulesDAO.class);
				throw swtexp;
			}
		}
	}

	/**
	 * Deletes the unique interface rule matching the data in the given
	 * InterfaceRule object
	 * 
	 * @param interfaceRule
	 * @throws SwtException
	 */
	public void deleteInterfaceRule(InterfaceRule interfaceRule)
			throws SwtException {
		log.debug(this.getClass().getName()
				+ " - [ deleteInterfaceRule ] - Entry ");
		Session session = null;
		Transaction tx = null;
		SwtInterceptor interceptor = null;
		try {
			interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");
			session = getHibernateTemplate().getSessionFactory().withOptions().interceptor(interceptor).openSession();
			tx = session.beginTransaction();
			session.delete(getInterfaceRule(interfaceRule));
			tx.commit();
			log.debug(this.getClass().getName()
					+ " - [ deleteInterfaceRule ] - Exit ");
		} catch (Exception e) {
			SwtErrorHandler swtErrorHandler = SwtErrorHandler.getInstance();
			SwtException swtExp = swtErrorHandler.handleException(e,
					"deleteInterfaceRule", InterfaceRulesDAO.class);
			throw swtExp;
		}  finally {
			JDBCCloser.close(session);
		}
	}

	/**
	 * Queries hibernate for a unique record matching the messageType, ruleId
	 * and ruleKey contained in the given InterfaceRule object
	 * 
	 * @param interfaceRule
	 * @return InterfaceRule
	 * @throws SwtException
	 */
	public InterfaceRule getInterfaceRule(InterfaceRule interfaceRule)
			throws SwtException {
		log.debug(this.getClass().getName()
				+ " - [ getInterfaceRule ] - Entry ");
		InterfaceRule interfaRuleObj = null;
		try {
			/*
			 * START: Added/modified for Mantis 1365:"Allow Special characters
			 * in Interface Rules Screen." by venkatesan on 16-feb-2011
			 */
			/*
			 * Expression.eq method is replaced for Expression.like method. for
			 * to get unique record.
			 */
//			Criteria criteria = getSession()
//					.createCriteria(InterfaceRule.class).add(
//							Expression.eq("id.messageType", interfaceRule
//									.getId().getMessageType().replace("%20",
//											" "))).add(
//							Expression.eq("id.ruleId", interfaceRule.getId()
//									.getRuleId())).add(
//							Expression.eq("id.ruleKey", interfaceRule.getId()
//									.getRuleKey()));
			/*
			 * END: Added/modified for Mantis 1365:"Allow Special characters in
			 * Interface Rules Screen." by venkatesan on 16-feb-2011
			 */
//			interfaRuleObj = (InterfaceRule) criteria.uniqueResult();
   			
			String hql = "from InterfaceRule ir where ir.id.messageType = :messageType and ir.id.ruleId = :ruleId and ir.id.ruleKey = :ruleKey";
			 interfaRuleObj = (InterfaceRule) getHibernateTemplate().execute((session) -> {
			    Query query = session.createQuery(hql);
			    query.setParameter("messageType", interfaceRule.getId().getMessageType().replace("%20", " "));
			    query.setParameter("ruleId", interfaceRule.getId().getRuleId());
			    query.setParameter("ruleKey", interfaceRule.getId().getRuleKey());
			    return query.uniqueResult();
			});

		} catch (Exception e) {
			SwtErrorHandler swtErrorHandler = SwtErrorHandler.getInstance();
			SwtException swtExp = swtErrorHandler.handleException(e,
					"getInterfaceRule", InterfaceRulesDAO.class);
			throw swtExp;
		}
		log
				.debug(this.getClass().getName()
						+ " - [ getInterfaceRule ] - Exit ");
		return interfaRuleObj;
	}

	/**
	 * Updates the persistent storage with the existing InterfaceRule object
	 * given. The PK data contained therein is used to locate the record to
	 * update.
	 * 
	 * @param interfaceRule
	 * @throws SwtException
	 */
	public void updateInterfaceRule(InterfaceRule interfaceRule)
			throws SwtException {
		log.debug(this.getClass().getName()
				+ " - [ updateInterfaceRule ] - Entry ");
		Session session = null;
		Transaction tx = null;
		SwtInterceptor interceptor = null;
		try {
			log.debug(this.getClass().getName()
					+ "- [updateInterfaceRule] - Entering");
			interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");
			session = getHibernateTemplate().getSessionFactory().withOptions().interceptor(interceptor).openSession();
			tx = session.beginTransaction();
			session.update(interfaceRule);
			tx.commit();
			log.debug(this.getClass().getName()
					+ "- [updateInterfaceRule] - Exiting");
		} catch (Exception exp) {
			log
					.error(this.getClass().getName()
							+ " - Exception Catched in [updateInterfaceRule] method : - "
							+ exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp,
					"updateInterfaceRule", InterfaceRulesDAOHibernate.class);
		}  finally {
			JDBCCloser.close(session);
		}
		log.debug(this.getClass().getName()
				+ " - [ updateInterfaceRule ] - Exit ");
	}

	/**
	 * This is used to save the interface rule details in I_INTERFACE_RULES
	 * table.
	 * 
	 * @param interfaceRule
	 * @return none
	 * @throws SwtException
	 */
	@SuppressWarnings("unchecked")
	public void saveInterfaceRule(InterfaceRule interfaceRule)
			throws SwtException {
		/* Methods local variable declaration */
		List records = null;
		Session session = null;
		Transaction tx = null;
		SwtInterceptor interceptor = null;
		try {
			log.debug(this.getClass().getName() + "- [saveHoliday] - Entering");
			/*
			 * Start : Modified for Mantis 1366-Remove entry to log if duplicate
			 * records added by betcy on 18-02-2011
			 */
			 records = (List ) getHibernateTemplate().find(
							"from InterfaceRule ir where ir.id.messageType=?0 and ir.id.ruleId=?1 and ir.id.ruleKey=?2",
							new Object[] {
									interfaceRule.getId().getMessageType(),
									interfaceRule.getId().getRuleId(),
									interfaceRule.getId().getRuleKey() });
			 /*Condition to check the list size*/
			if (records.size() == 0) {
				interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");
				session = getHibernateTemplate()
							.getSessionFactory()
							.withOptions().interceptor(interceptor).openSession();
				tx = session.beginTransaction();
				session.save(interfaceRule);
				tx.commit();
				log.debug(this.getClass().getName()
						+ "- [saveInterfaceRule] - Exiting");
			} else {
				throw new SwtException(
						"errors.DataIntegrityViolationExceptioninAdd");
			}
			/*
			 * End : Modified for Mantis 1366-Remove entry to log if duplicate
			 * records added by betcy on 18-02-2011
			 */
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [saveInterfaceRule] method : - "
					+ exp.getMessage());

			throw SwtErrorHandler.getInstance().handleException(exp,
					"saveInterfaceRule", InterfaceRulesDAOHibernate.class);
		}  finally {
			JDBCCloser.close(session);
		}
	}


	/**
	 * This is used to populate the interface rules message types drop down
	 * values.
	 * 
	 * @return messagrTypes
	 * @throws SwtException
	 */
	public Collection<String> getMessageTypesList() throws SwtException {
		log.debug(this.getClass().getName()
				+ " - [ getMessageTypesList ] - Entry ");
		List<String> messageTypes = null;
		Connection conn = null;
		Statement stmt = null;
		ResultSet rs = null;
		StringBuffer query = null;
		try {
			conn = ConnectionManager.getInstance().databaseCon();
			stmt = conn.createStatement();
			messageTypes = new ArrayList<String>();
			// pass the query in string buffer
			query = new StringBuffer(
					"SELECT DISTINCT UPPER(MESSAGE_TYPE) FROM I_INTERFACE_RULES");
			rs = stmt.executeQuery(query.toString());

			/* Getting the message type and put it in List */
			while (rs.next()) {
				messageTypes.add(rs.getString(1));
			}
		} catch (Exception e) {
			SwtErrorHandler swtErrorHandler = SwtErrorHandler.getInstance();
			SwtException swtexp = swtErrorHandler.handleException(e,
					"getMessageTypesList", InterfaceRulesDAO.class);
			throw swtexp;
			/*
			 * Start:code modified by venkat on 20_jan_2011 for issues found on
			 * V1051 beta testing- interface rules issues.
			 */
		} finally {
			
			//edited by KaisBS for mantis 1745 : introducing the implementation of the JDBCCloser class

			Object[] exceptions = JDBCCloser.close(rs, stmt, conn, null);
			if (exceptions[0]!=null)
				throw SwtErrorHandler.getInstance().handleException((SQLException) exceptions[0], "getMessageTypesList", InterfaceRulesDAO.class);
			
		}
		/*
		 * End:code modified by venkat on 20_jan_2011 for issues found on V1051
		 * beta testing- interface rules issues.
		 */
		log.debug(this.getClass().getName()
				+ " - [ getMessageTypesList ] - Exit ");
		return messageTypes;
	}

	/*
	 * Code modified by Venkat on 23-jan-2011 for mantis 1365:"Allow Special
	 * characters in Interface Rules Screen."
	 */
	/**
	 * This is used to get Interface Rules List and count value.
	 * 
	 * @param messageType
	 * @param ruleId
	 * @param currentPage
	 * @param maxPage
	 * @param interfaceRulesList
	 * @param filterSortStatus
	 * @param partialRuleId
	 * @return record count
	 * @throws SwtException
	 */
	public int getInterfaceRulesListUsingStoredProc(String messageType,
			String ruleId, int currentPage, int maxPage,
			ArrayList<InterfaceRule> interfaceRulesList,
			String filterSortStatus, String partialRuleId) throws SwtException {
		log.debug(this.getClass().getName()
				+ " - [ getInterfaceRulesListUsingStoredProc ] - Entry ");
		/* Variable Declaration for session */
		Session session = null;
		/* Variable Declaration for conn */
		Connection conn = null;
		/* Variable Declaration for cstmt */
		CallableStatement cstmt = null;
		/* Variable Declaration for pagesize */
		int pageSize = 0;
		/* Variable Declaration for showdata */
		String showdata = null;
		/* Variable Declaration for recordcount */
		int recordcount = 0;
		/* Variable Declaration for ResultSet */
		ResultSet rs = null;
		ResultSet rsTotal = null;
		/* Variable Declaration for filterSortArr */
		String[] filterSortArr = filterSortStatus.split("<split>");
		/* Variable Declaration for filterCriteria */
		String filterCriteria = null;
		/* Variable Declaration for sortCriteria */
		String sortCriteria = filterSortArr[1];
		/* Variable Declaration for delimiterSymbol/size */
		String delimiterSymbol = null;
		int delimiterSize = 0;

		try {
			/* get session from session Factory */
			session = getHibernateTemplate().getSessionFactory().openSession();
			/* get connection from session */
			conn = SwtUtil.connection(session);
			/* Delimiter Changed for filterCriteria and sortCriteria */
			sortCriteria = sortCriteria + SwtConstants.DELIMITER_SYMBOL;
			delimiterSymbol = SwtConstants.DELIMITER_SYMBOL;
			delimiterSize = SwtConstants.DELIMITER_SIZE;
			showdata = "Y";
			/*
			 * start: Code added by Venkat on 11-mar-2011 for mantis 1365:"Allow
			 * Special characters in Interface Rules Screen."
			 */
			/* check messageType is not null and trim */
			if (messageType != null) {
				messageType = messageType.trim();
			}
			/* get pageSize from Property */
			pageSize=SwtUtil.getPageSizeFromProperty(SwtConstants.INTERFACERULES_MAINTENANCE_PAGE_SIZE);
			filterCriteria = filterSortArr[0].replaceAll("&gt;", ">")
					.replaceAll("<quot;>", "\"").replaceAll("&amp;", "&")
					.replaceAll("&lt;", "<");
			/*
			 * Start:Code added by Venkat on 16-Feb-2011 for mantis 1365:"Allow
			 * Special characters in Interface Rules Screen."
			 */
			/* call the Stored Procedure */
			cstmt = conn
					.prepareCall("{call SP_INTERFACE_RULES_MAINTENANCE(?,?,?,?,?,?,?,?,?,?,?,?)}");
			/*
			 * End:Code added by Venkat on 16-Feb-2011 for mantis 1365:"Allow
			 * Special characters in Interface Rules Screen."
			 */
			cstmt.setString(1, messageType);
			cstmt.setString(2, ruleId);
			cstmt.setInt(3, pageSize);
			cstmt.setInt(4, currentPage);
			cstmt.setString(5, filterCriteria);
			cstmt.setString(6, sortCriteria);
			cstmt.setString(7, showdata);
			cstmt.registerOutParameter(8, oracle.jdbc.OracleTypes.CURSOR);
			cstmt.registerOutParameter(9, oracle.jdbc.OracleTypes.CURSOR);
			/*
			 * Start:Code added by Venkat on 23-Feb-2011 for mantis 1365:"Allow
			 * Special characters in Interface Rules Screen."
			 */
			/* Delimiter Changed for filterCriteria and sortCriteria */
			cstmt.setString(10, delimiterSymbol);
			cstmt.setInt(11, delimiterSize);
			cstmt.setString(12, partialRuleId);
			/*
			 * End:Code added by Venkat on 23-Feb-2011 for mantis 1365:"Allow
			 * Special characters in Interface Rules Screen."
			 */
			cstmt.execute();
			rs = (ResultSet) cstmt.getObject(8);
			rsTotal = (ResultSet) cstmt.getObject(9);
			/* Check whether resultset is not null */
			if (rs != null) {
				while (rs.next()) {
					InterfaceRule interfaceRule = new InterfaceRule();
					String rsMessageType = rs.getString(1);
					String rsRuleId = rs.getString(2);
					String rsRuleKey = rs.getString(3);
					String rsRuleValue = rs.getString(4);
					interfaceRule.getId().setMessageType(rsMessageType);
					interfaceRule.getId().setRuleId(rsRuleId);
					interfaceRule.getId().setRuleKey(rsRuleKey);
					interfaceRule.setRuleValue(rsRuleValue);
					interfaceRulesList.add(interfaceRule);
				}
			}
			/* check whether result set total is not null */
			if (rsTotal != null) {
				while (rsTotal.next()) {
					recordcount = rsTotal.getInt(1);
				}
			}
			log.debug(this.getClass().getName()
					+ " - [ getInterfaceRulesListUsingStoredProc ] - Exit ");
		} catch (Exception e) {
			SwtErrorHandler swtErrorHandler = SwtErrorHandler.getInstance();
			SwtException swtExp = swtErrorHandler.handleException(e,
					"getMessageTypesList", InterfaceRulesDAO.class);
			throw swtExp;
		} finally {
			
			showdata = null;
			filterCriteria = null;
			delimiterSymbol = null;
			
			//edited by KaisBS for mantis 1745 : introducing the implementation of the JDBCCloser class
			SwtException thrownException = null;
			SQLException sqlException = null;
			
			sqlException = JDBCCloser.close(rs, rsTotal);
			if (sqlException!=null)
				thrownException = SwtErrorHandler.getInstance().handleException(sqlException, "getInterfaceRulesListUsingStoredProc", InterfaceRulesDAO.class);
			
			Object[] exceptions = JDBCCloser.close(null, cstmt, conn, session);
			
			if (thrownException == null && exceptions[0] != null)
				thrownException = SwtErrorHandler.getInstance().handleException((SQLException) exceptions[0], "getInterfaceRulesListUsingStoredProc", InterfaceRulesDAO.class); 
			
			if (thrownException == null && exceptions[1] !=null) 
				thrownException = SwtErrorHandler.getInstance().handleException((HibernateException) exceptions[1], "getInterfaceRulesListUsingStoredProc", InterfaceRulesDAO.class); 
			
				if(thrownException!=null)
					throw thrownException;
			
		}
		return recordcount;
	}

}