/*
 * @(#)SysParamsDAOHibernate.java 1.0 12/12/2005
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.maintenance.dao.hibernate;

import java.sql.CallableStatement;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.List;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.hibernate.HibernateException;
import org.hibernate.Session;
import org.hibernate.SessionFactory;
import org.hibernate.Transaction;
import org.springframework.context.annotation.Lazy;
import org.springframework.orm.hibernate5.support.HibernateDaoSupport;
import org.springframework.stereotype.Repository;
import org.swallow.batchScheduler.ConnectionManager;
import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.maintenance.dao.SysParamsDAO;
import org.swallow.maintenance.model.SysParams;
import org.swallow.util.JDBCCloser;
import org.swallow.util.SwtInterceptor;
import org.swallow.util.SwtUtil;

/**
 * <AUTHOR>
 * 
 * 
 * This Class that implements the SysParamsDAO and acts as DAO layer for all
 * database operations
 */
@Repository
public class SysParamsDAOHibernate extends HibernateDaoSupport implements
		SysParamsDAO {
	
	public SysParamsDAOHibernate(@Lazy SessionFactory sessionfactory){
	    setSessionFactory(sessionfactory);
	}
	
	/**
	 * Comment for <code>log</code>
	 */
	private final Log log = LogFactory.getLog(SysParamsDAOHibernate.class);
	private final String SYSTEMPARAMETERSQUERY = "from SysParams c where c.hostId = ?0";

	public static HashMap<String, String> keywordQuery = new HashMap<String, String>();
	
	static {
		keywordQuery.put("RUN_DATE","TRUNC(?)");
		keywordQuery.put("RUN_DATE-1","TRUNC(?)-1");
		keywordQuery.put("RUN_DATE-2","TRUNC(?)-2");
		keywordQuery.put("RUN_DATE-3","TRUNC(?)-3");
		keywordQuery.put("RUN_DATE-4","TRUNC(?)-4");
		keywordQuery.put("RUN_DATE-5","TRUNC(?)-5");
		keywordQuery.put("RUN_DATE-6","TRUNC(?)-6");
		keywordQuery.put("RUN_DATE-7","TRUNC(?)-7");
		keywordQuery.put("START_OF_CURRENT_WEEK","TRUNC(?,'iw')");
		keywordQuery.put("END_OF_CURRENT_WEEK","TRUNC(?,'iw')+6");
		keywordQuery.put("START_OF_PREVIOUS_WEEK","TRUNC(?-7,'iw')");
		keywordQuery.put("END_OF_PREVIOUS_WEEK","TRUNC(?-7,'iw')+6");
		keywordQuery.put("START_OF_CURRENT_MONTH","TRUNC(?,'MM')");
		keywordQuery.put("END_OF_CURRENT_MONTH","LAST_DAY(TRUNC(?))");
		keywordQuery.put("START_OF_PREVIOUS_MONTH","TRUNC(ADD_MONTHS(?, -1), 'MM')");
		keywordQuery.put("END_OF_PREVIOUS_MONTH","ADD_MONTHS(LAST_DAY(TRUNC(?)),-1)");
		keywordQuery.put("START_OF_CURRENT_QUARTER","TRUNC(?,'Q')");
		keywordQuery.put("END_OF_CURRENT_QUARTER","ADD_MONTHS(TRUNC(?,'Q'),3)-1");
		keywordQuery.put("START_OF_PREVIOUS_QUARTER","ADD_MONTHS(TRUNC(?,'Q'),-3)");
		keywordQuery.put("END_OF_PREVIOUS_QUARTER","TRUNC(?,'Q')-1");
		keywordQuery.put("START_OF_CURRENT_YEAR","TRUNC(?,'YEAR')");
		keywordQuery.put("END_OF_CURRENT_YEAR","ADD_MONTHS(TRUNC(?,'YEAR'),12)-1");
		keywordQuery.put("START_OF_PREVIOUS_YEAR","ADD_MONTHS(TRUNC(?,'YEAR'),-12)");
		keywordQuery.put("END_OF_PREVIOUS_YEAR","TRUNC(?,'YEAR')-1");
	}

	@Override
	public SysParams getSysParamsDetail(String hostId) {
			// TODO Auto-generated method stub
			log.debug("Entering getSysParamsDetail");
			List sysparamscoll = null;

			sysparamscoll = getHibernateTemplate().find(SYSTEMPARAMETERSQUERY,
					new Object[] { hostId });

			Iterator itr = sysparamscoll.iterator();
			SysParams sys = null;

			while (itr.hasNext()) {

				sys = (SysParams) itr.next();

			}

			log.debug("exiting getSysParamsDetail");

			if (sysparamscoll.size() == 1) {
				return (SysParams) (sysparamscoll.get(0));
			} else {
				return null;
			}
	}

	@Override
	public void updateSysParameter(SysParams sysParams) throws SwtException {
		Session session = null;
		Transaction tx = null;
		SwtInterceptor interceptor = null;
		try {
			log.debug("Entering updateSysParameter method");
			log.debug(sysParams.getTestDate());
			interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");
			session = getHibernateTemplate().getSessionFactory().withOptions().interceptor(interceptor).openSession();
			tx = session.beginTransaction();
			session.update(sysParams);
			tx.commit();
			session.close();;
			log.debug("exiting updateSysParameter");		
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					+ "- [updateSysParameter] - Exception " + exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp,
					"updateSysParameter", SysParamsDAOHibernate.class);
		}
	}

	@Override
	public Date getDBSytemDate() throws SwtException {
		log.debug(this.getClass().getName() + " - [getDBSytemDate] - "
				+ "Entry");
		/* Method's local variable declaration */
		Connection conn = null;
		Session session = null;
		Statement stmt = null;
		ResultSet rs = null;
		Date sysdate = null;
		try {
			try {
			session = getHibernateTemplate().getSessionFactory().openSession();
			}catch(Exception e) {
				return null;
			}
			conn = SwtUtil.connection(session);
			stmt = conn.createStatement();
			stmt.execute("select GLOBAL_VAR.SYS_DATE from dual");
			rs = stmt.getResultSet();
			rs.next();
			sysdate = (Date) rs.getObject(1);
			log.debug(this.getClass().getName() + " - [getDBSytemDate] - "
					+ "Exit");
			return sysdate;
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [getDBSytemDate] method : - "
					+ exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp,
					"getDBSytemDate", SysParamsDAOHibernate.class);
		} finally {
			//edited by KaisBS for mantis 1745 : introducing the implementation of the JDBCCloser class
			SwtException thrownException = null;
			Object[] exceptions = JDBCCloser.close(rs, stmt, conn, session); 

			if (exceptions[0] != null)
			    thrownException = new SwtException(((SQLException) exceptions[0]).getMessage());

			if (thrownException == null && exceptions[1] !=null) 
			    thrownException = new SwtException(((HibernateException) exceptions[1]).getMessage());

			if (thrownException != null)
				throw thrownException;

		}
	}

	/**
	 * This is used to retrieve entity date
	 * 
	 * @param systemDate
	 * @param entityId
	 * @return HashMap
	 * @throws SwtException
	 */

	public HashMap<String, Object> getEntityDate(Date systemDate,
			String entityId) throws SwtException {
		// Hibarnate Session instance
		Session session = null;
		// Connection instance
		Connection conn = null;
		// prepared Statement Instance
		PreparedStatement pstmt = null;
		// Result Set Instance
		ResultSet resultSet = null;
		// Variable to hold the Date value
		Date date = null;
		// HashMap instance
		HashMap<String, Object> entmap = null;
		// SimpleDateFormat Instance
		SimpleDateFormat simpleDateFormat = null;
		// Variable to hold the query
		String SQLQuery = null;
		// Variable to hold the time stamp
		Timestamp timeStamp = null;
		// Variable to hold the offset
		String offSet = null;
		try {
			log.debug(this.getClass().getName() + "-[getEntityDate]-Entry");
			// To initialize the object
			entmap = new HashMap<String, Object>();
			// To initialize the object for simple date format
			simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
			// To create the query
			SQLQuery = "select global_var.FN_GET_OFFSET_DATE_ENT(to_date(?, 'YYYY-MM-DD HH24:MI:SS'),:p_entity_id), tz_offset(SYSTEM_TZNAME) from s_system_parameters,S_ENTITY where entity_id=?";
			// Obtain session from hibernate
			session = getHibernateTemplate().getSessionFactory().openSession();
			// Get data base connection from hibernate sesssion
			conn = SwtUtil.connection(session);
			// To create the prepar statement
			pstmt = conn.prepareStatement(SQLQuery);
			// To set the parameter for prepared statement
			pstmt.setString(1, simpleDateFormat.format(systemDate));
			// To set the parameter for prepared statement
			pstmt.setString(2, entityId);
			// To set the parameter for prepared statement
			pstmt.setString(3, entityId);
			// To execute the prepared statement query
			resultSet = pstmt.executeQuery();
			// To check the result set object
			if (resultSet != null) {
				// To fetch the result set object
				while (resultSet.next()) {
					timeStamp = resultSet.getTimestamp(1);
					offSet = resultSet.getString(2);
					date = new Date(timeStamp.getTime());
					// To set entity date
					entmap.put("entityDate", date);
					// To set Offset value
					entmap.put("offSet", offSet);
				}
			}
			log.debug(this.getClass().getName() + "-[getEntityDate]-Exit");
		} catch (SQLException sqlException) {
			log.error("sqlException in SysParamsDAOHibernate.getEntityDate");
			throw SwtErrorHandler.getInstance().handleException(sqlException,
					"getEntityDate", SysParamsDAOHibernate.class);
		} catch (HibernateException hibernateException) {
			log
					.error("hibernateException in SysParamsDAOHibernate.getEntityDate");
			throw SwtErrorHandler.getInstance().handleException(
					hibernateException, "getEntityDate",
					SysParamsDAOHibernate.class);
		} catch (Exception exception) {
			log.error("exception in SysParamsDAOHibernate.getEntityDate");
			throw SwtErrorHandler.getInstance().handleException(exception,
					"getEntityDate", SysParamsDAOHibernate.class);
		} finally {
			//edited by KaisBS for mantis 1745 : introducing the implementation of the JDBCCloser class
			SwtException thrownException = null;
			Object[] exceptions = JDBCCloser.close(resultSet, pstmt, conn, session);

			if (exceptions[0] != null)
			    thrownException = SwtErrorHandler.getInstance().handleException((SQLException) exceptions[0], "getEntityDate",SysParamsDAOHibernate.class);

			if (thrownException == null && exceptions[1] !=null) 
			    thrownException = SwtErrorHandler.getInstance().handleException((HibernateException) exceptions[1], "getEntityDate",SysParamsDAOHibernate.class);

			if (thrownException != null)
				throw thrownException;

			simpleDateFormat = null;
			SQLQuery = null;
			timeStamp = null;
			offSet = null;
			date = null;

		}
		return entmap;
	}

	/* Start: Method added by Balaji for Mantis 1991 */
	/**
	 * This method is used to retrieve the system date for after applying its
	 * offset value.
	 * 
	 * @param entityId
	 * @return Date
	 * @throws SwtException
	 */
	public Date getDBSytemDateWithEntityOffset(String entityId)
			throws SwtException {
		log.debug(this.getClass().getName()
				+ " - [getDBSytemDateWithEntityOffset] -  Entry");
		/* Method's local variable declaration */
		Connection conn = null;
		Session session = null;
		PreparedStatement stmt = null;
		ResultSet rs = null;
		Date sysdate = null;
		try {
			session = getHibernateTemplate().getSessionFactory().openSession();
			// to get entity date with offset time
			conn = SwtUtil.connection(session);
			stmt = conn
					.prepareStatement("select GLOBAL_VAR.FN_GET_OFFSET_DATE_ENT(GLOBAL_VAR.SYS_DATE,?) from dual");
			stmt.setString(1, entityId);
			rs = stmt.executeQuery();
			rs.next();
			sysdate = (Date) rs.getObject(1);
			return sysdate;
		} catch (Exception exp) {
			log
					.error(this.getClass().getName()
							+ " - Exception Catched in [getDBSytemDateWithEntityOffset] method : - "
							+ exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp,
					"getDBSytemDate", SysParamsDAOHibernate.class);
		} finally {
			//edited by KaisBS for mantis 1745 : introducing the implementation of the JDBCCloser class
			SwtException thrownException = null;
			Object[] exceptions = JDBCCloser.close(rs, stmt, conn, session);

			if (exceptions[0] != null)
			    thrownException = new SwtException(((SQLException) exceptions[0]).getMessage());

			if (thrownException == null && exceptions[1] !=null) 
			    thrownException = new SwtException(((HibernateException) exceptions[1]).getMessage());

			if (thrownException != null)
				throw thrownException;
			
			
			log.debug(this.getClass().getName()
					+ " - [getDBSytemDateWithEntityOffset] - Exit");
		}
	}
	/* End: Method added by Balaji for Mantis 1991 */
	
	/**
	 * Added by Mefteh for Mantis 2016
	 * To retrieve N_DAYS_PRIOR_TO_TODAY , N_DAYS_AHEAD_TO_TODAY defined in PKG_PREDICT_JOBS 
	 * @param none
	 * @return List
	 * @throws SwtException
	 */
	public List<Integer> getPriorAndAheadDaysToToday() throws SwtException {
		log.debug(this.getClass().getName() + " - [getPriorAndAheadDaysToToday] - "
				+ "Entry");
		/* Method's local variable declaration */
		Connection conn = null;
		Session session = null;
		Statement stmt = null;
		ResultSet rs = null;
		List<Integer> listPriorAndAheadDays = new ArrayList<Integer>();
		try {
			session = getHibernateTemplate().getSessionFactory().openSession();
			conn = SwtUtil.connection(session);
			stmt = conn.createStatement();
			stmt.execute("select PKG_PREDICT_JOBS.GET_DAYS_BEFORE_TODAY, PKG_PREDICT_JOBS.GET_DAYS_AFTER_TODAY from dual");
			rs = stmt.getResultSet();			
			while(rs!=null && rs.next()){
				listPriorAndAheadDays.add(rs.getBigDecimal(1).intValueExact());
				listPriorAndAheadDays.add(rs.getBigDecimal(2).intValueExact());			
			}
		log.debug(this.getClass().getName() + " - [getPriorAndAheadDaysToToday] - "
					+ "Exit");
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [getPriorAndAheadDaysToToday] method : - "
					+ exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp,
					"getPriorAndAheadDaysToToday", SysParamsDAOHibernate.class);
		} finally {
			SwtException thrownException = null;
			Object[] exceptions = JDBCCloser.close(rs, stmt, conn, session);

			if (exceptions[0] != null)
			    thrownException = SwtErrorHandler.getInstance().handleException((SQLException) exceptions[0], "getPriorAndAheadDaysToToday",SysParamsDAOHibernate.class);

			if (thrownException == null && exceptions[1] !=null) 
			    thrownException = SwtErrorHandler.getInstance().handleException((HibernateException) exceptions[1], "getPriorAndAheadDaysToToday",SysParamsDAOHibernate.class);

			if (thrownException != null)
				throw thrownException;

		}
		
		return listPriorAndAheadDays;
	}
	
	@Override
	public String getCurrencyGroup(String entityId, String currencyId) throws SwtException {
		log.debug(this.getClass().getName() + " - [getDBSytemDate] - "
				+ "Entry");
		/* Method's local variable declaration */
		// Declare Session
		Session session = null;
		// Declare Connection
		Connection conn = null;
		// Declare Callable Statement
		CallableStatement cstmt = null;
		// Declare ResultSet
		ResultSet rs = null;
		// Declare Result 
		String result= null;
		try {
			log.debug(this.getClass().getName()
					+ " - [getCurrencyGroup] - " + "Entry");
			// open the session
			session = getHibernateTemplate().getSessionFactory().openSession();
			// get the connection
			conn = SwtUtil.connection(session);
			/* Using Callable statement to execute stored function */
			cstmt = conn
					.prepareCall("select pk_application.fnGetCcyGroupForCcy (?,?) from dual");
			cstmt.setString(1, entityId);
			cstmt.setString(2, currencyId);
			rs= cstmt.executeQuery();
			rs.next();
			// Get last run date and last run duration
			result = (String) rs.getString(1);	
			result = result!=null?result:"";

		} catch (Exception exp) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [getCurrencyGroup] method : - "
					+ exp.getMessage());
		} finally {
			SwtException thrownException = null;
			Object[] exceptions = JDBCCloser.close(rs, cstmt, conn, session);
			
			if (exceptions[0] != null)
				thrownException = SwtErrorHandler.getInstance().handleException((SQLException) exceptions[0], "getCurrencyGroup",SysParamsDAOHibernate.class);
			
			if (thrownException == null && exceptions[1] !=null) 
				thrownException = SwtErrorHandler.getInstance().handleException((HibernateException) exceptions[1], "getCurrencyGroup",SysParamsDAOHibernate.class);
			
			if (thrownException != null)
				throw thrownException;
			
		}
		return result.toString();
	}


	@Override
	public String getDateFormat() throws SwtException {
		String dateFormat = new String();
		Connection conn = null;
		Session session = null;
		Statement stmt = null;
		ResultSet rs = null;
		try {
			log.debug(this.getClass().getName() + " - [getDateFormat] - "
					+ "Entry");
			session = getHibernateTemplate().getSessionFactory().openSession();
			conn = SwtUtil.connection(session);
			stmt = conn.createStatement();
			stmt.execute("SELECT date_format FROM S_SYSTEM_PARAMETERS WHERE host_id=GLOBAL_VAR.FN_GET_HOST");
			rs = stmt.getResultSet();
			rs.next();
			dateFormat = (String) rs.getObject(1);
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [getDateFormat] method : - "
					+ exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp,
					"getDateFormat", SysParamsDAOHibernate.class);
		}
		finally {
			JDBCCloser.close(rs, stmt, conn, session);
		}
		log.debug(this.getClass().getName() + " - [getDateFormat] - "
				+ "Exit");
		return dateFormat;
	
	}

	
	/**
	 * This is used to applicate a keyword on a date to get for example the first day of this week or the last date of the previous month
	 * @param keyword
	 * @param date
	 * @return
	 * @throws SwtException
	 */
		  
	public Date applicateKeywordOnDate(String keyword, Date date)throws SwtException{
		log.debug(this.getClass().getName() + " - [applicateKeywordOnDate] - "
				+ "Entry");
		/* Method's local variable declaration */
		Connection conn = null;
		Session session = null;
		PreparedStatement stmt = null;
		ResultSet rs = null;
		Date dateResult = null;
		try {
			session = getHibernateTemplate().getSessionFactory().openSession();
			// to get entity date with offset time
			conn = SwtUtil.connection(session);
			stmt = conn
					.prepareStatement("select "+keywordQuery.get(keyword) +"  from dual");
			stmt.setDate(1, SwtUtil.truncateDateTime(date));
			rs = stmt.executeQuery();
			rs.next();
			dateResult = (Date) rs.getObject(1);
			return dateResult;
		} catch (Exception exp) {
			log
					.error(this.getClass().getName()
							+ " - Exception Catched in [applicateKeywordOnDate] method : - "
							+ exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp,
					"getDBSytemDate", SysParamsDAOHibernate.class);
		} finally {
			//edited by KaisBS for mantis 1745 : introducing the implementation of the JDBCCloser class
			SwtException thrownException = null;
			Object[] exceptions = JDBCCloser.close(rs, stmt, conn, session);

			if (exceptions[0] != null)
			    thrownException = new SwtException(((SQLException) exceptions[0]).getMessage());
									
			  
  

			if (thrownException == null && exceptions[1] !=null) 
			    thrownException = new SwtException(((HibernateException) exceptions[1]).getMessage());
									
  
  

			if (thrownException != null)
				throw thrownException;
			
			
			log.debug(this.getClass().getName()
					+ " - [applicateKeywordOnDate] - Exit");
		}
	}
	
	
	public HashMap<String, String> getTimeZoneRegionsList(Date systemDate) throws SwtException{

		log.debug(this.getClass().getName() + "- [getTimeZoneRegionsList] - Entry ");
		/* Local variable declaration */
		Connection conn = null;
		PreparedStatement pstmt = null;
		ResultSet res = null;
		LinkedHashMap<String, String> timeZoneRegionsList = new LinkedHashMap<String, String>();				  
						  
		try {
			SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
			/* Establish the connection using connection manager */
			conn = ConnectionManager.getInstance().databaseCon();
			/* pass the query in string buffer */
			String regionsQuery = "WITH TZ_DATA AS (SELECT DISTINCT TZNAME FROM V$TIMEZONE_NAMES), OFFSETS AS (" +
					"SELECT TZNAME," +
					"TO_CHAR(FROM_TZ(TIMESTAMP '"+sdf.format(systemDate)+" 01:00:00', TZNAME), 'TZH:TZM') AS TIMEZONE_OFFSET " +
					"FROM TZ_DATA )" +
					"SELECT TZNAME, TIMEZONE_OFFSET " +
					"FROM OFFSETS " +
					"GROUP BY TZNAME, TIMEZONE_OFFSET " +
					"ORDER BY TZNAME";

			pstmt = conn.prepareStatement(regionsQuery);
			/* execute the statement */
			res = pstmt.executeQuery();

			while (res.next()) {
				timeZoneRegionsList.put(res.getString(1),res.getString(2));

							 
	 
			}
		} catch (SQLException e) {

			log.debug(this.getClass().getName()
					+ "- [getTimeZoneRegionsList] - Exception " + e.getMessage());
			log.error(this.getClass().getName()
															   
					+ "- [getTimeZoneRegionsList] - Exception " + e.getMessage());
			e.printStackTrace();
		} finally {
			
			JDBCCloser.close(res, pstmt, conn, null);
			
			try{				
				if ((conn != null) && (!conn.isClosed()))
					ConnectionManager.getInstance().retrunConnection(conn);
			} catch (Exception e) {
				log.error(this.getClass().getName()
						+ "- [getTimeZoneRegionsList] - Exception " + e.getMessage());        
			}
		}
		log.debug(this.getClass().getName() + "- [getTimeZoneRegionsList] - Exit ");
		return timeZoneRegionsList;
  
									
			  
	}

	
	
}