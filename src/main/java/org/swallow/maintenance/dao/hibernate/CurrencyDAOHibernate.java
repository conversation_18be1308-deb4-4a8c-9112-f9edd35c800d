/**
 * @(#)CurrencyDAOHibernate.java 1.0 23/08/06
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.maintenance.dao.hibernate;

import java.sql.CallableStatement;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.Iterator;
import java.util.List;







import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.dao.DataAccessResourceFailureException;

import org.swallow.batchScheduler.ConnectionManager;
import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.exception.SwtRecordNotExist;
import org.swallow.maintenance.dao.CurrencyDAO;
import org.swallow.maintenance.model.Currency;
import org.swallow.maintenance.model.CurrencyAccessTO;
import org.swallow.util.CacheManager;
import org.swallow.util.JDBCCloser;
import org.swallow.util.LabelValueBean;
import org.swallow.util.SwtConstants;
import org.swallow.util.SwtInterceptor;
import org.swallow.util.SwtUtil;
import org.springframework.context.annotation.Lazy;
import org.springframework.orm.hibernate5.support.HibernateDaoSupport;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.stereotype.Repository;
import org.springframework.beans.factory.annotation.Autowired;
import org.hibernate.HibernateException;
import org.hibernate.SessionFactory;
import org.hibernate.Transaction;
import org.hibernate.Session;

/**
 * CurrencyDAOHibernate.java
 * 
 * This is DAO class for Currency screen
 */
@Repository ("currencyDAO")
@Transactional
public class CurrencyDAOHibernate extends HibernateDaoSupport implements
		CurrencyDAO {
	public CurrencyDAOHibernate(@Lazy SessionFactory sessionfactory) {
	    setSessionFactory(sessionfactory);
	}


	/**
	 * Initializing logger object for this class
	 */
	private final Log log = LogFactory.getLog(CurrencyDAOHibernate.class);

	/**
	 * Returns the collection of currency detail list from currency table for an
	 * entity
	 * 
	 * @param entityId
	 * @param hostId
	 * @return Collection
	 * @throws SwtException
	 */
	public Collection getCurrencyDetailList(String entityId, String hostId)
			throws SwtException {
		try {
			log.debug(this.getClass().getName()
					+ " - [getCurrencyDetailList] - " + "Entry");

			/* Method's Local variable declaration */
			List list = null;

			/* Query to get the currency detail list */
			list = (List ) getHibernateTemplate().find("from Currency c "
							+ "where c.id.entityId =?0 and c.id.hostId=?1 and c.id.currencyCode!='*' order by c.id.currencyCode",
							new Object[] { entityId, hostId });

			log.debug(this.getClass().getName()
					+ " - [getCurrencyDetailList] - " + "Exit");
			return list;
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [getCurrencyDetailList] method : - "
					+ exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp,
					"getCurrencyDetailList", CurrencyDAOHibernate.class);
		}

	}

	/**
	 * Returns the collection of currency detail list from currency table
	 * 
	 * @param entityLvls
	 * @param hostId
	 * @return Collection
	 * @throws SwtException
	 */
	public Collection getCurrencyDetailList(Collection entityLvls, String hostId)
			throws SwtException {
		try {
			log.debug(this.getClass().getName()
					+ " - [getCurrencyDetailList] - " + "Entry");

			/* Method's local variable declaration */
			String query;
			Iterator itr;
			LabelValueBean labelValueBean = null;
			StringBuffer entStr;
			List list = null;

			/* Assigning the query statement to the string object */
			query = " from Currency c where c.id.hostId=?0 and c.id.currencyCode!='*' ";

			/* Iterating the entity and store in iterator object */
			itr = entityLvls.iterator();
			labelValueBean = null;
			entStr = new StringBuffer();

			/*
			 * Loop to iterate the each value of the collection and collect the
			 * label value bean of the entity
			 */
			while (itr.hasNext()) {
				labelValueBean = (LabelValueBean) itr.next();
				entStr.append("'" + labelValueBean.getValue() + "',");
			}

			/*
			 * Condition to check the length of the entity String is greater
			 * than zero
			 */
			if (entStr.length() > 0) {
				/* Condition is success the Query will add the entity id */
				query = query + " and c.id.entityId in (" + entStr.toString()
						+ "'')";
			}

			/* Query to display the currency record in order to currency code */
			query = query + " order by c.id.currencyCode";

			/* Execute the query to select the currency */
			list = (List ) getHibernateTemplate().find(query, new Object[] { hostId });

			log.debug(this.getClass().getName()
					+ " - [getCurrencyDetailList] - " + "Exit");
			return list;
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [getCurrencyDetailList] method : - "
					+ exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp,
					"getCurrencyDetailList", CurrencyDAOHibernate.class);
		}

	}

	/**
	 * Save the newly added currency detail to the DataBase
	 * 
	 * @param currency
	 * @return
	 * @throws SwtException
	 */
	@SuppressWarnings("unchecked")
	public void saveCurrencyDetail(Currency currency) throws SwtException {
		/* Methods local variable declaration */
		List records = null;
		Session session = null;
		Transaction tx = null;
		SwtInterceptor interceptor = null;
		try {
			log.debug(this.getClass().getName() + " - [saveCurrencyDetail] - "
					+ "Entry");
			/* Query to execute the records */
			records = (List ) getHibernateTemplate().find("from Currency c "
					+ "where c.id.hostId =?0 and c.id.entityId=?1 and c.id.currencyCode=?2",
							new Object[] { currency.getId().getHostId(),
									currency.getId().getEntityId(),
									currency.getId().getCurrencyCode() });
			/* Condition to check list size */
			if (records.size() == 0) {
				
				interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");
				session = getHibernateTemplate()
							.getSessionFactory()
							.withOptions().interceptor(interceptor).openSession();
				tx = session.beginTransaction();
				session.save(currency);
				tx.commit();
				
				log.debug(this.getClass().getName()
						+ " - [saveCurrencyDetail] - " + "Exit");
			} else {
				throw new SwtException(
						"errors.DataIntegrityViolationExceptioninAdd");
			}
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [saveCurrencyDetail] method : - "
					+ exp.getMessage());

			throw SwtErrorHandler.getInstance().handleException(exp,
					"saveCurrencyDetail", CurrencyDAOHibernate.class);
		} finally {
			records = null;
			JDBCCloser.close(session);
		}
	}

	/**
	 * Update the changed currency detail in the DataBase
	 * 
	 * @param currency
	 * @return none
	 * @throws SwtException
	 */
	public void updateCurrencyDetail(Currency currency) throws SwtException {
		Session session = null;
		Transaction tx = null;
		SwtInterceptor interceptor = null;
		try {
			log.debug(this.getClass().getName()
					+ " - [updateCurrencyDetail] - " + "Entry");

			/* Update the currency bean in to the database table */
			interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");
			session = getHibernateTemplate().getSessionFactory().withOptions().interceptor(interceptor).openSession();
			tx = session.beginTransaction();
			session.update(currency);
			tx.commit();

			log.debug(this.getClass().getName()
					+ " - [updateCurrencyDetail] - " + "Exit");
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [updateCurrencyDetail] method : - "
					+ exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp,
					"updateCurrencyDetail", CurrencyDAOHibernate.class);
		}  finally {
			JDBCCloser.close(session);
		}
	}

	/**
	 * Deletes the selected currency detail from the DataBase
	 * 
	 * @param currency
	 * @return
	 * @throws SwtException
	 */
	public void deleteCurrencyDetail(Currency currency) throws SwtException {
		Session session = null;
		Transaction tx = null;
		SwtInterceptor interceptor = null;
		try {
			log.debug(this.getClass().getName()
					+ " - [deleteCurrencyDetail] - " + "Entry");

			/* Method's local variable declaration */
			String deleteHQL;
			int deleteCounter;

			/* Assigning the query statement to the string variable */
			deleteHQL = "DELETE FROM Currency m " +
			        "WHERE m.id.hostId = :hostId " +
			        "AND m.id.entityId = :entityId " +
			        "AND m.id.currencyCode = :currencyCode";
			/* Deletes the currency detail for the given entity */

			interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");
			session = getHibernateTemplate().getSessionFactory().withOptions().interceptor(interceptor).openSession();
			tx = session.beginTransaction();
			 deleteCounter = session.createQuery(deleteHQL)
			            .setParameter("hostId", currency.getId().getHostId())
			            .setParameter("entityId", currency.getId().getEntityId())
			            .setParameter("currencyCode", currency.getId().getCurrencyCode())
			            .executeUpdate();
			tx.commit();
			

			if (deleteCounter == 0) {
				throw new SwtRecordNotExist();
			}

			log.debug(this.getClass().getName()
					+ " - [deleteCurrencyDetail] - " + "Exit");
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [deleteCurrencyDetail] method : - "
					+ exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp,
					"deleteCurrencyDetail", CurrencyDAOHibernate.class);
		}  finally {
			JDBCCloser.close(session);
		}

	}

	/**
	 * This method fetches all the CurrencyExchange defined for entityId,
	 * currency code and for date <= today
	 * 
	 * @param entityId
	 * @param currencyCode
	 * @return List
	 * @throws SwtException
	 */
	public List getTodayExchangeRate(String entityId, String currencyCode)
			throws SwtException {
		try {
			log.debug(this.getClass().getName()
					+ " - [getTodayExchangeRate] - " + "Entry");

			/* Method's local variable declaration */
			String hostId;
			Date todayDate;
			String date;

			/* Method's class instance declaration */
			StringBuffer strQuery;
			ArrayList collExchangeRates;

			/* Retrieves and store the hostId from Swtutil */
			hostId = SwtUtil.getCurrentHostId();
			/* Collects the system date from SwtUtil */
			todayDate = SwtUtil.getSystemDatewithoutTime();
			/* Format the date as dd/mm/yy */
			date = SwtUtil.formatDate(todayDate, "dd/MM/yy");

			/* Assigning the query statement to the String buffer */
			strQuery = new StringBuffer(
					"from CurrencyExchange e where e.id.hostId=?0 and e.id.entityId=?1 and e.id.currencyCode =?2 and to_char(e.id.exchangeRateDate,'dd/MM/yy') <=?3 order by e.id.exchangeRateDate desc");

			/* Collects the exchange rate of the given currency */
			collExchangeRates = (ArrayList) getHibernateTemplate().find(
					strQuery.toString(),
					new Object[] { hostId, entityId, currencyCode, date });

			log.debug(this.getClass().getName()
					+ " - [getTodayExchangeRate] - " + "Exit");
			return collExchangeRates;
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [getTodayExchangeRate] method : - "
					+ exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp,
					"getTodayExchangeRate", CurrencyDAOHibernate.class);
		}
	}

	/**
	 * Method to get the collection of currency for the given entityId and
	 * currency groupId in order to the priority order and currency code of the
	 * currency
	 * 
	 * @param entityId
	 * @param currencyGroupId
	 * @return Collection
	 * @throws SwtException
	 */
	public Collection getCurrencies(String entityId, String currencyGroupId)
			throws SwtException {
		try {
			log.debug(this.getClass().getName() + " - [getCurrencies] - "
					+ "Entry");

			/* Method's variable and class instance declaration */
			String hostId;
			List list = null;

			/* Retrieves the host id from SwtUtil */
			hostId = SwtUtil.getCurrentHostId();

			/* Get the list of currency from the database */
			list = (List ) getHibernateTemplate().find("from Currency c where c.id.hostId =?0 and c.id.entityId=?1 and c.currencyGroupId = ?2"
							+ " order by c.priorityOrder,c.id.currencyCode",
							new Object[] { hostId, entityId, currencyGroupId });

			log.debug(this.getClass().getName() + " - [getCurrencies] - "
					+ "Exit");

			return list;
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [getCurrencies] method : - "
					+ exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp,
					"getCurrencies", CurrencyDAOHibernate.class);
		}
	}

	/**
	 * Method to get the collection of currency for the given entityId and
	 * currency groupId in order to the priority order and currency code of the
	 * currency
	 * 
	 * @param entityId
	 * @param currencyGroupId
	 * @return Collection
	 * @throws SwtException
	 */
	public Collection getAllCurrencies(String entityId, String currencyGroupId)
			throws SwtException {

		/* Method's variable and class instance declaration */
		String hostId;
		List list = null;
		try {
			log.debug(this.getClass().getName() + " - [getAllCurrencies] - "
					+ "Entry");
			/* Retrieves the host id from SwtUtil */
			hostId = SwtUtil.getCurrentHostId();
			if(!"All".equalsIgnoreCase(entityId)) {
			/* Query to select all currencies */
			list = (List ) getHibernateTemplate().find("from Currency c where c.id.hostId =?0 and c.id.entityId=?1 and c.currencyGroupId in ("
							+ currencyGroupId
							+ ")"
							+ " order by c.priorityOrder,c.id.currencyCode",
							new Object[] { hostId, entityId });
			}else {
				
			list = (List ) getHibernateTemplate().find("from Currency c where c.id.hostId =?0 and c.currencyGroupId in ("
						+ currencyGroupId
						+ ")"
						+ " order by c.priorityOrder,c.id.currencyCode",
						new Object[] { hostId });				
			}

			log.debug(this.getClass().getName() + " - [getAllCurrencies] - "
					+ "Exit");
			return list;
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [getAllCurrencies] method : - "
					+ exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp,
					"getAllCurrencies", CurrencyDAOHibernate.class);
		}
	}

	/*
	 * Start: Code added by RK on 08-Mar-2012 for Mantis 1645
	 */
	/**
	 * This method invokes stored procedure "pk_application.spEntityCcyAccess"
	 * and gets list of currencies belongs to given hostId, entityId and roleId.
	 * The currencies should have either view or full access.
	 * 
	 * @param entityId
	 * @param roleId
	 * @return Collection<CurrencyAccessTO>
	 * @throws SwtException
	 */
	public Collection<CurrencyAccessTO> getEntityCurrency(String entityId,
			String roleId) throws SwtException {
		// To hold currencies belongs to host, entity and role
		Collection<CurrencyAccessTO> colCurrency = null;
		// Hibernate session to execute stored procedure
		Session session = null;
		// Database connection
		Connection conn = null;
		// CallableStatement instance to execute stored procedure
		CallableStatement cstmt = null;
		// ResultSet to get currencies
		ResultSet rsCurency = null;
		// Parameter index
		int paramIndex;

		try {
			// log debug message
			log.debug(this.getClass().getName()
					+ " - [getEntityCurrency] - Enter");

			// Get the hibernate session
			session = getHibernateTemplate().getSessionFactory().openSession();
			// Get the database connection object
			conn = SwtUtil.connection(session);
			// Make a callable statement for executing the procedure
			cstmt = conn
					.prepareCall("{call PK_APPLICATION.spEntityCcyAccess(?,?,?,?)}");
			// Set the parameters for stored procedure
			paramIndex = 1;
			cstmt.setString(paramIndex++, CacheManager.getInstance()
					.getHostId());
			cstmt.setString(paramIndex++, entityId);
			cstmt.setString(paramIndex++, roleId);

			// Register the output parameter for currency details
			cstmt.registerOutParameter(paramIndex++,
					oracle.jdbc.OracleTypes.CURSOR);

			// Execute the callable statement
			cstmt.execute();

			// Get the result set of currency details
			rsCurency = (ResultSet) cstmt.getObject(4);
			// Initialize collection to hold currency details
			colCurrency = new ArrayList<CurrencyAccessTO>();
			// Iterate through currency result set and currency code and
			// currency name.
			while (rsCurency.next()) {
				// Add currency details in a collection and give fill access
				// right to currency
				colCurrency.add(new CurrencyAccessTO(entityId, rsCurency
						.getString(1), rsCurency.getString(2),
						SwtConstants.CURRENCYGRP_FULL_ACCESS));
			}
			return colCurrency;
		} catch (DataAccessResourceFailureException ex) {
			// log error message
			log.error(this.getClass().getName()
					+ " - [getEntityCurrency] - DataAccessResourceFailureException: "
					+ ex.getMessage());
			// Re-throw as SwtException
			throw SwtErrorHandler.getInstance().handleException(ex,
					"getEntityCurrency", this.getClass());
		} catch (IllegalStateException ex) {
			// log error message
			log.error(this.getClass().getName()
					+ " - [getEntityCurrency] - IllegalStateException: "
					+ ex.getMessage());
			// Re-throw as SwtException
			throw SwtErrorHandler.getInstance().handleException(ex,
					"getEntityCurrency", this.getClass());
		} catch (HibernateException ex) {
			// log error message
			log.error(this.getClass().getName()
					+ " - [getEntityCurrency] - HibernateException: "
					+ ex.getMessage());
			// Re-throw as SwtException
			throw SwtErrorHandler.getInstance().handleException(ex,
					"getEntityCurrency", this.getClass());
		} catch (SQLException ex) {
			// log error message
			log.error(this.getClass().getName()
					+ " - [getEntityCurrency] - SQLException: "
					+ ex.getMessage());
			// Re-throw as SwtException
			throw SwtErrorHandler.getInstance().handleException(ex,
					"getEntityCurrency", this.getClass());
		} catch (SwtException ex) {
			// log error message
			log.error(this.getClass().getName()
					+ " - [getEntityCurrency] - SwtException: "
					+ ex.getMessage());
			// Re-throw SwtException
			throw ex;
		} catch (Exception ex) {
			// log error message
			log.error(this.getClass().getName()
					+ " - [getEntityCurrency] - Exception: " + ex.getMessage());
			// Re-throw as SwtException
			throw SwtErrorHandler.getInstance().handleException(ex,
					"getEntityCurrency", this.getClass());
		} finally {
			// close result sets, statements and hibernate session and nullify
			// objects

			// Edited by KaisBS for mantis 1745 : introducing the implementation
			// of the JDBCCloser class
			JDBCCloser.close(rsCurency, cstmt, conn, session);

			// nullify objects
			session = null;
			conn = null;
			cstmt = null;
			rsCurency = null;
			// log debug message
			log.debug(this.getClass().getName()
					+ " - [getEntityCurrency] - Exit");
		}
	}


	/**
	 * This method is used to check if provided currency exists in s_currency
	 */

	public boolean checkIfCurrencyExists(String hostId, String ccy ,String entity)
			throws SwtException {
		Connection conn = null;
		ResultSet res = null;
		PreparedStatement stmt = null;
		ResultSet rs = null;
		boolean result = false;

		try {
			log.debug(this.getClass().getName() + " - [checkIfCurrencyExists] - "
					+ "Entry");

			conn = ConnectionManager.getInstance().databaseCon();
			String checkEntityQuery =  "select 1 from s_currency ccy where ccy.host_id=? and ccy.currency_code=? and ccy.entity_id=?";

			stmt = conn.prepareStatement(checkEntityQuery);
			stmt.setString(1, hostId);
			stmt.setString(2, ccy);
			stmt.setString(3, entity);
			stmt.execute();
			/* Fetching the Result */
			rs = (ResultSet) stmt.getResultSet();
			if (rs != null) {
				if(rs.next() == false) {
					result=false;
				}else {
					result=true;
				}
					
			}
			log.debug(this.getClass().getName() + " - [checkIfCurrencyExists] - "
					+ "Exit");
		} catch (SQLException ex) {
			try {
				log.error(this.getClass().getName() + " - [checkIfCurrencyExists] -  Exception: " + ex.getMessage());
				// rollBack
				conn.rollback();
			} catch (SQLException e) {

			}

		} catch (Exception e) {
			log.error("An exception occured in " + this.getClass().getName() + " - [checkIfCurrencyExists] - "
					+ e.getMessage());
			throw new SwtException(e.getMessage());

		} finally {
			// Close conn and nullify objects
			Object[] exceptions = JDBCCloser.close(res, stmt, conn, null);
		}

		return result;
	}
	
	/**
	 * This method is used to get the currency tolerance
	 */

	public String getCcyTolerance(String hostId, String ccy ,String entity)
			throws SwtException {
		Connection conn = null;
		ResultSet res = null;
		PreparedStatement stmt = null;
		ResultSet rs = null;
		String tolValue = null;

		try {
			log.debug(this.getClass().getName() + " - [getCcyTolerance] - "
					+ "Entry");
			conn = ConnectionManager.getInstance().databaseCon();
			String getCcyTolerance = "select tolerance from s_currency ccy where ccy.host_id=? and ccy.currency_code=? and ccy.entity_id=?";
			stmt = conn.prepareStatement(getCcyTolerance);
			stmt.setString(1, hostId);
			stmt.setString(2, ccy);
			stmt.setString(3, entity);
			stmt.execute();
			/* Fetching the Result */
			rs = (ResultSet) stmt.getResultSet();
			if (rs != null) {
				/* Loop to iterate result set */
				while (rs.next()) {
					tolValue = rs.getString(1);
				}
			}			
			log.debug(this.getClass().getName() + " - [getCcyTolerance] - "
					+ "Exit");
			//return mvtType;
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [getCcyTolerance] method : - "
					+ exp.getMessage());
		} finally {
			JDBCCloser.close(rs, stmt, conn, null);

		}
		return tolValue;
	}
	/**
	 * This method is used to get the currency Tzname And tz_offset
	 */
	public String getCurrencyTznameAndTzOffset(String currencyCode) throws SwtException {
		String rtn = "";

		Session session = null;
		Connection conn = null;
		PreparedStatement statement = null;
		ResultSet resultSet = null;
		try {
		    java.util.Date utilDate = SwtUtil.getSystemDatewithoutTime();
		    java.sql.Date sqlDate = new java.sql.Date(utilDate.getTime());
			String SQLQuery = "select currency_tzname, TO_CHAR (FROM_TZ (TO_TIMESTAMP (?), currency_tzname), 'TZH:TZM')  from s_currency_master where currency_code=?";
			session = getHibernateTemplate().getSessionFactory().openSession();
			conn = SwtUtil.connection(session);
			statement = conn.prepareStatement(SQLQuery);
			statement.setDate(1, sqlDate);
			statement.setString(2, currencyCode);
			statement.execute();
			resultSet = statement.getResultSet();

			if (resultSet != null) {
				while (resultSet.next()) {
					rtn = String.format("%s**%s", resultSet.getString(1), resultSet.getString(2));
				}
			}
		} catch (HibernateException hibernateException) {
			log.error("Problem in accessing Hibernate properties");
			throw SwtErrorHandler.getInstance().handleException(
					hibernateException, "getOracleSystemTime",
					CurrencyDAOHibernate.class);
		} catch (SQLException sqlException) {
			log.error("Problem in executing Query");
			throw SwtErrorHandler.getInstance().handleException(sqlException,
					"getOracleSystemTime", CurrencyDAOHibernate.class);
		} finally {
			
			SwtException thrownException = null;
			Object[] exceptions = JDBCCloser.close(resultSet, statement, conn, session);

			if (exceptions[0] != null)
			    thrownException = SwtErrorHandler.getInstance().handleException((SQLException) exceptions[0], "getCurrencyTznameAndTzOffset",
			    		CurrencyDAOHibernate.class);

			if (thrownException == null && exceptions[1] !=null) 
			    thrownException = SwtErrorHandler.getInstance().handleException((HibernateException) exceptions[1], "getCurrencyTznameAndTzOffset",
			    		CurrencyDAOHibernate.class);

			if (thrownException!=null)
			    throw thrownException;
			
		}
		return rtn;
	}
}