/*
 * @(#)AcctMaintenanceDAOHibernate.java 1.0 20/12/05
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */

package org.swallow.maintenance.dao.hibernate;

import java.sql.CallableStatement;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.text.SimpleDateFormat;
import java.time.Duration;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.Iterator;
import java.util.List;

import javax.persistence.Query;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.json.JSONObject;

import org.swallow.batchScheduler.ConnectionManager;
import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.exception.SwtRecordNotExist;
import org.swallow.maintenance.dao.AcctMaintenanceDAO;
import org.swallow.maintenance.model.AccSweepSchedule;
import org.swallow.maintenance.model.AccountInterestRate;
import org.swallow.maintenance.model.AccountSpecificSweepFormat;
import org.swallow.maintenance.model.AccountSweepBalanceGroup;
import org.swallow.maintenance.model.AcctMaintenance;
import org.swallow.maintenance.model.Country;
import org.swallow.maintenance.model.Currency;
import org.swallow.maintenance.model.Party;
import org.swallow.util.JDBCCloser;
import org.swallow.util.SwtConstants;
import org.swallow.util.SwtInterceptor;
import org.swallow.util.SwtUtil;
import org.swallow.util.SystemFormats;
import org.swallow.work.model.AccountMonitorNew;
import org.springframework.context.annotation.Lazy;
import org.springframework.orm.hibernate5.support.HibernateDaoSupport;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.stereotype.Repository;
import org.springframework.beans.factory.annotation.Autowired;
import org.hibernate.Hibernate;
import org.hibernate.HibernateException;
import org.hibernate.SessionFactory;
import org.hibernate.Transaction;
import org.hibernate.exception.GenericJDBCException;
import org.hibernate.type.StringType;
import org.hibernate.type.Type;
import org.hibernate.Session;


/**
 * AcctMaintenanceDAOHibernate.java
 * 
 * Class that implements the AcctMaintenanceDAO and acts as DAO layer for all
 * database operations related to Account Maintenance This interface has methods
 * that are used for accessing the persistent storage such as database which
 * helps client to create, retrieve and persists data to the Persistent Object.
 * 
 */
@Repository ("acctMaintenanceDAO")
@Transactional
public class AcctMaintenanceDAOHibernate extends HibernateDaoSupport implements
		AcctMaintenanceDAO {
	public AcctMaintenanceDAOHibernate(@Lazy SessionFactory sessionfactory) {
	    setSessionFactory(sessionfactory);
	}

	/**
	 * Comment for <code>log</code>
	 */
	private final Log log = LogFactory
			.getLog(AcctMaintenanceDAOHibernate.class);

	/**
	 * 
	 * This method is used to retrieve the account details persistent object
	 * from database through stored procedure
	 * PK_APPLICATION.SPACMAINTENANCEACCESS .
	 * 
	 * @param entityId
	 * @param hostId
	 * @param roleId
	 * @return accountList
	 */

	public Collection<AcctMaintenance> getAcctMaintenanceDetailList(
			String entityId, String hostId, String roleId, String currencyId, String selectedFiltredByStatus, String acctType) throws SwtException {
			// Variable to hold the accountList object
			Collection<AcctMaintenance> accountList = null;
			// Variable to hold the accountMaintenance object
			AcctMaintenance accountMaintenance = null;
			// Variable to hold the session object
			Session session = null;
			// Variable to hold the connection object
			Connection connection = null;
			// Variable to hold the callStatement object
			CallableStatement callStatement = null;
			// Variable to hold the resultSetAccountDetails object
			ResultSet resultSetAccountDetails = null;

			try {
				log.debug(this.getClass().getName()
						+ "- [getAcctMaintenanceDetailList] - Entering ");

				// Gets the current session
				session = getHibernateTemplate().getSessionFactory().openSession();
				// Establishes the dataBase connection from session
				connection = SwtUtil.connection(session);
				// Calls the Stored Procedure
				callStatement = connection
						.prepareCall("{call PK_APPLICATION.SPACMAINTENANCEACCESS(?,?,?,?,?,?)}");
				callStatement.setString(1, hostId);
				callStatement.setString(2, entityId);
				callStatement.setString(3, roleId);
				
				// Registers the CURSOR as OUT parameter
				callStatement.registerOutParameter(4,
						oracle.jdbc.OracleTypes.CURSOR);
				callStatement.setString(5, selectedFiltredByStatus);
				callStatement.setString(6, acctType);
				// Executes the CallableStatement
				callStatement.execute();
				// Gets the CURSOR value in the ResultSet
				resultSetAccountDetails = (ResultSet) callStatement.getObject(4);

				if (resultSetAccountDetails != null) {
					// Instantiate the accountList
					accountList = new ArrayList<AcctMaintenance>();

					// iterate the account details and set into accountMaintenance
					// object
					while (resultSetAccountDetails.next()) {

						// Instantiate the accountMaintenance
						accountMaintenance = new AcctMaintenance();
						// Set resultset values into accountMaintenance object
						// Instantiate the accountMaintenance
						// Set resultset values into accountMaintenance object
						accountMaintenance.getId().setHostId(
								resultSetAccountDetails.getString("HOST_ID"));
						accountMaintenance.getId().setEntityId(
								resultSetAccountDetails.getString("ENTITY_ID"));

						accountMaintenance.getId().setAccountId(
								resultSetAccountDetails.getString("ACCOUNT_ID"));
						accountMaintenance.setAcctname(resultSetAccountDetails.getString("ACCOUNT_NAME"));
						accountMaintenance.setCurrcode(resultSetAccountDetails.getString("currency_code"));
						accountMaintenance.setAccttype(resultSetAccountDetails.getString("account_type"));
						accountMaintenance.setAcctlevel(resultSetAccountDetails.getString("account_level"));
						accountMaintenance.setAcctClass(resultSetAccountDetails.getString("account_class"));
						accountMaintenance.setAcctIBAN(resultSetAccountDetails.getString("iban"));
						accountMaintenance.setAcctstatusflg(resultSetAccountDetails.getString("account_status_flag"));
						accountMaintenance.setCurrAccess(resultSetAccountDetails
								.getInt("access_id"));
						
						accountMaintenance.setCorresacccode(resultSetAccountDetails.getString("corres_acc_id"));
						accountMaintenance.setMinacctcode(resultSetAccountDetails.getString("main_account_id"));
						accountMaintenance.setCutoff(resultSetAccountDetails.getString("cut_off"));
						accountMaintenance.setLinkAccID(resultSetAccountDetails.getString("link_account_id"));
						accountMaintenance.setAccountPartyId(resultSetAccountDetails.getString("account_party_id"));
						accountMaintenance.setAcctbiccode(resultSetAccountDetails.getString("account_bic_id"));
						accountMaintenance.setIsIlmLiqContributor(resultSetAccountDetails.getString("is_ilm_liq_contributor"));
						accountList.add(accountMaintenance);
					}
				}
				log.debug(this.getClass().getName()
						+ "- [getAcctMaintenanceDetailList] - Exit");
			} catch (Exception exp) {
				log
						.error("Exception Catch in AcctMaintenanceDAOHibernate.'getAcctMaintenanceDetailList' method : "
								+ exp.getMessage());
				throw SwtErrorHandler.getInstance().handleException(exp,
						"getAcctMaintenanceDetailList",
						AcctMaintenanceDAOHibernate.class);
			} finally {

				// edited by KaisBS for mantis 1745 : introducing the implementation
				// of the JDBCCloser class
				SwtException thrownException = null;
				Object[] exceptions = JDBCCloser.close(resultSetAccountDetails,
						callStatement, connection, session);

				if (exceptions[0] != null)
					thrownException = SwtErrorHandler.getInstance()
							.handleException((SQLException) exceptions[0],
									"getAcctMaintenanceDetailList",
									AcctMaintenanceDAOHibernate.class);

				if (thrownException == null && exceptions[1] != null)
					thrownException = SwtErrorHandler.getInstance()
							.handleException((HibernateException) exceptions[1],
									"getAcctMaintenanceDetailList",
									AcctMaintenanceDAOHibernate.class);

				if (thrownException != null)
					throw thrownException;
				// nullify objects
				accountMaintenance = null;
			}
			return accountList;
		}

	/**
	 * This is used to save the records in database
	 * 
	 * @param acct
	 * @param accountInterestRateList
	 * @return none
	 * @throws SwtException
	 */
	@SuppressWarnings("unchecked")
	public void saveAcctMaintDetail(AcctMaintenance acct,
			ArrayList accountInterestRateList) throws SwtException {
		// Variable to hold acctMaintenanceRecords object
		List acctMaintenanceRecords = null;
		// Variable to hold the InterestRate Iterator object
		Iterator interestRateItr = null;
		// Object for interestRate
		Object interestRateObj = null;
		Session session = null;
		Transaction tx = null;
		SwtInterceptor interceptor = null;
		try {
			log.debug(this.getClass().getName()
					+ "- [saveAcctMaintDetail] - Entering ");
			/** Query to fetches from database */
			acctMaintenanceRecords = (List ) getHibernateTemplate().find(
							"from AcctMaintenance acct where  acct.id.hostId=?0 and acct.id.entityId=?1 and acct.id.accountId=?2",
							new Object[] { acct.getId().getHostId(),
									acct.getId().getEntityId(),
									acct.getId().getAccountId() });
			/* Condition to check list size */
			if (acctMaintenanceRecords.size() == 0) {
				interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");
				session = getHibernateTemplate()
						.getSessionFactory()
						.withOptions().interceptor(interceptor).openSession();
				tx = session.beginTransaction();
				session.save(acct);
				if(accountInterestRateList.size() > 0) {
					interestRateItr = accountInterestRateList.iterator();
					while (interestRateItr.hasNext()) {
						interestRateObj = interestRateItr.next();
						if (interestRateObj != null) {
								session.save(interestRateObj);
						}
					}
				}
				tx.commit();
			} else {
				throw new SwtException(
						"errors.DataIntegrityViolationExceptioninAdd");
			}
			log.debug(this.getClass().getName()
					+ "- [saveAcctMaintDetail] - Exiting ");
		} catch (Exception exp) {
			try {
				if (tx != null) {
					tx.rollback();
				}
			} catch (HibernateException exception) {
				log
						.error("HibernateException occured in rolling back transaction. Cause : "
								+ exception.getMessage());
			}
			log.error(this.getClass().getName()
					+ " - Exception caught in [saveAcctMaintDetail] method "
					+ exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp,
					"saveAcctMaintDetail", AcctMaintenanceDAOHibernate.class);

		} finally {
			HibernateException hThrownException = JDBCCloser.close(session);

			if (hThrownException != null)
				throw new SwtException(hThrownException.getMessage());
		}
	}

	/**
	 * @param hostId
	 * @param entityId
	 * @param accountId
	 * @return AcctMaintenace
	 */
	public AcctMaintenance getEditableData(String hostId, String entityId,
			String accountId) throws SwtException {
		// Variable to hold the AcctMaintenance object
		AcctMaintenance acctMaint = null;
		// Variable to hold Iterator object
		Iterator balMaintListItr = null;
		java.util.List balMaintList = getHibernateTemplate()
				.find(
						"from AcctMaintenance acct where  acct.id.hostId=?0 and acct.id.entityId=?1 and acct.id.accountId=?2",
						new Object[] { hostId, entityId, accountId });
		balMaintListItr = balMaintList.iterator();
		while (balMaintListItr.hasNext()) {
			acctMaint = (AcctMaintenance) balMaintListItr.next();
		}
		return acctMaint;
	}

	/**
	 * 
	 * @param acct
	 * @param collAcctIntRateDeleted
	 * @param collAcctIntRateAdded
	 * @param collAcctIntRateUpdated
	 * @param sysforma
	 * @throws SwtException
	 */
	@SuppressWarnings("unchecked")
	public void updateAcctDetail(AcctMaintenance acct,
			Collection collAcctIntRateDeleted, Collection collAcctIntRateAdded,
			Collection collAcctIntRateUpdated, SystemFormats sysforma)
			throws SwtException {
		/*
		 * Start:Code Modified for MAntis 1690 by sandeepkumar on 3-oct-2012
		 * :Null pointer exception raised while Saving account after deleting
		 * the lone record in the Account interest rate screen
		 */
		// Variable to hold the InterestRate Iterator object
		Iterator interestRateItr = null;
		// Variable to hold the deleted number of records for
		// AccountInterestRate
		int rowsdeleted = 0;
		// Variable to hold AccountInterestRate object
		AccountInterestRate accIntRate = null;
		// variable to hold interest rate with time
		java.sql.Date interestRateTime = null;
		// variable to hold the hql query
		String deleteAcctIntRateHQL = null;
		
		Session session = null;
		Transaction tx = null;
		SwtInterceptor interceptor = null;
		try {
			log.debug(this.getClass().getName()
					+ "- [updateAcctDetail] - Entry");
			
			interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");
			session = getHibernateTemplate()
					.getSessionFactory()
					.withOptions().interceptor(interceptor).openSession();
			tx = session.beginTransaction();
			
			session.update(acct);
			// condition for Deleted AccountInterestRate
			if (collAcctIntRateDeleted.size() > 0) {
				interestRateItr = collAcctIntRateDeleted.iterator();
				// instance for account interest rate
				accIntRate = new AccountInterestRate();
				while (interestRateItr.hasNext()) {
					accIntRate = (AccountInterestRate) interestRateItr.next();
					// get the interest date and time into interestRateTime
					interestRateTime = SwtUtil.truncateDateTime(accIntRate.getId()
							.getInterestDateRate());
					// pass the query to execute
					deleteAcctIntRateHQL = "from AccountInterestRate  m where m.id.hostId=?0 and m.id.entityId=?1 and m.id.accountId=?2 and m.id.interestDateRate = ?3 ";

		            Query query = session.createQuery(deleteAcctIntRateHQL);
		            query.setParameter(0, accIntRate.getId().getHostId());
		            query.setParameter(1, accIntRate.getId().getEntityId());
		            query.setParameter(2, accIntRate.getId().getAccountId());
		            query.setParameter(3,interestRateTime);
		            query.executeUpdate();
					/*
					 * End:Code Modified for MAntis 1690 by sandeepkumar on 3-oct-2012
					 * :Null pointer exception raised while Saving account after deleting
					 * the lone record in the Account interest rate screen
					 */
					// check the rows selected is equal to zero
					if (rowsdeleted == 0) {
						throw new SwtRecordNotExist();
					}
				}
			}
			// condition for Added AccountInterestRate
			if (collAcctIntRateAdded.size() > 0) {
				interestRateItr = collAcctIntRateAdded.iterator();
				while (interestRateItr.hasNext()) {
					accIntRate = (AccountInterestRate) interestRateItr.next();
					if (accIntRate != null) {
						session.save(accIntRate);
					}
				}
			}
			// condition for uddated AccountInterestRate
			if (collAcctIntRateUpdated.size() > 0) {
				interestRateItr = collAcctIntRateUpdated.iterator();
				accIntRate = new AccountInterestRate();
				while (interestRateItr.hasNext()) {
					accIntRate = (AccountInterestRate) interestRateItr.next();
					accIntRate = (AccountInterestRate) accIntRate;
					if (accIntRate != null) {
						session.update(accIntRate);
					}
				}
			}
			tx.commit();
			log
					.debug(this.getClass().getName()
							+ "- [updateAcctDetail] - Exit");
		} catch (Exception exp) {
			try {
				if (tx != null) {
					tx.rollback();
				}
			} catch (HibernateException exception) {
				log
						.error("HibernateException occured in rolling back transaction. Cause : "
								+ exception.getMessage());
			}
			log.error(this.getClass().getName()
					+ " - Exception caught in [updateAcctDetail] method "
					+ exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp,
					"updateAcctDetail", AcctMaintenanceDAOHibernate.class);

		} finally {
			HibernateException hThrownException = JDBCCloser.close(session);

			if (hThrownException != null)
				throw new SwtException(hThrownException.getMessage());
		}

	}

	/**
	 * @param hostId
	 * @param entityId
	 * @return Collection - Booklist
	 */
	public Collection getBookListColl(String hostId, String entityId)
			throws SwtException {
		log.debug("hostID for getBookListColl: " + hostId);
		java.util.List noofRecords = (List ) getHibernateTemplate().find(
						"from BookCode m where m.id.hostId=?0 and m.id.entityId=?1 order by m.id.bookCode",
						new Object[] { hostId, entityId });
		log.debug("exiting getBookListColl");
		return noofRecords;
	}

	/**
	 * @param hostId
	 * @param entityId
	 * @return Collection - getFormatListColl
	 */
	public Collection getFormatListColl(String hostId, String entityId)
			throws SwtException {
		log.debug("hostID for getFormatListColl: " + hostId);
		java.util.List noofRecords = (List ) getHibernateTemplate().find(
						"from MessageFormats m where m.id.hostId=?0 and m.id.entityId=?1  and m.usage = 'S' order by LOWER(m.id.formatId)",
						new Object[] { hostId, entityId });
		log.debug("noofRecords.size : " + noofRecords.size());
		return noofRecords;
	}

	/**
	 * @param hostId
	 * @param entityId
	 * @return Collection - getMainAcctListColl
	 */
	public Collection getMainAcctListColl(String hostId, String entityId)
			throws SwtException {
		log.debug("hostID for getFormatListColl: " + hostId);
		java.util.List noofRecords = (List ) getHibernateTemplate().find(
						"from AcctMaintenance acct where  acct.id.hostId=?0 and acct.id.entityId=?1 and acct.acctlevel='M' and acct.id.accountId != '*'",
						new Object[] { hostId, entityId });
		log.debug("exiting getFormatListColl");
		return noofRecords;
	}

	/**
	 * 
	 * @param acct
	 * @param collAcctIntRateDeleted
	 * @param sysforma
	 * @throws SwtException
	 */
	@SuppressWarnings("unchecked")
	public void deleteAcctDetail(AcctMaintenance acct,
			Collection collAcctIntRateDeleted, SystemFormats sysforma)
			throws SwtException {
		// Variable to hold the InterestRate Iterator object
		Iterator interestRateItr = null;
		Iterator sweepFormatItr = null;
		Iterator sweepSchedItr = null;
		Iterator sweepBalGrpItr = null;
		// Variable to hold AccountInterestRate object
		AccountInterestRate accIntRate = null;
		// Variable to hold HQL delete query
		String deleteHQL = null;
		// Variable to hold the deleted number of records for AcctMaintenance
		int deleteCount = 0;
		// Variable to hold the deleted number of records for
		// AccountInterestRate
		int rowsdeleted = 0;
		Collection sweepFormatForAccounts = null;
		Collection acctSweepScheduler = null;
		Collection acctSweepBalGrp = null;
		AccountSpecificSweepFormat specificFormat = null;
		AccSweepSchedule accSweepSchedule = null;
		AccountSweepBalanceGroup accSweepBalGrp = null;
		Session session = null;
		Transaction tx = null;
		SwtInterceptor interceptor = null;
		
		try {
			log.debug(this.getClass().getName()
					+ "- [deleteAcctDetail] - Entry");
			
			interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");
			session = getHibernateTemplate()
					.getSessionFactory()
					.withOptions().interceptor(interceptor).openSession();
			tx = session.beginTransaction();
						
			if (collAcctIntRateDeleted.size() > 0) {
				interestRateItr = collAcctIntRateDeleted.iterator();
				accIntRate = new AccountInterestRate();
				while (interestRateItr.hasNext()) {
					String deleteAcctIntRateHQL = "delete from AccountInterestRate  m where m.id.hostId=?0 and m.id.entityId=?1 and m.id.accountId=?2 ";
					accIntRate = (AccountInterestRate) interestRateItr.next();

					if (sysforma.getDateFormatValue().equalsIgnoreCase("MM/dd/yyyy")) {
						deleteAcctIntRateHQL += (" and to_date(to_char(m.id.interestDateRate,'MM-DD-YYYY HH24:MI:SS'),'MM-DD-YYYY HH24:MI:SS') = to_date(?3,'MM-DD-YYYY HH24:MI:SS')");

					} else {
						deleteAcctIntRateHQL += (" and to_date(to_char(m.id.interestDateRate,'DD-MM-YYYY HH24:MI:SS'),'DD-MM-YYYY HH24:MI:SS') = to_date(?3,'DD-MM-YYYY HH24:MI:SS')");

					}
					
					rowsdeleted = session.createQuery(deleteAcctIntRateHQL)
		            .setParameter(0, accIntRate.getId().getHostId())
		            .setParameter(1, accIntRate.getId().getEntityId())
		            .setParameter(2, accIntRate.getId().getAccountId())
		            .setParameter(3, accIntRate.getInterestDateRateAsString())
		            .executeUpdate();
					
					if (rowsdeleted == 0) {
						throw new SwtRecordNotExist();
					}
				}
			}
			
			
			sweepFormatForAccounts = getHibernateTemplate()
					.find("from AccountSpecificSweepFormat accountSpecificSweepFormat where accountSpecificSweepFormat.id.hostId=?0 and accountSpecificSweepFormat.id.entityId=?1 and accountSpecificSweepFormat.id.accountId=?2",
							new Object[]{acct.getId().getHostId(), acct.getId().getEntityId(), acct.getId().getAccountId()});
			
			
			if (sweepFormatForAccounts.size() > 0) {
				sweepFormatItr = sweepFormatForAccounts.iterator();
				while (sweepFormatItr.hasNext()) {
					specificFormat = (AccountSpecificSweepFormat) sweepFormatItr.next();
					
					session.delete(specificFormat);
					
				}
			}
			
			acctSweepScheduler = getHibernateTemplate()
					.find("from AccSweepSchedule  accSweep where accSweep.hostId=?0 and accSweep.entityId=?1 and accSweep.accountId=?2" ,
							new Object[]{acct.getId().getHostId(), acct.getId().getEntityId(), acct.getId().getAccountId()});
			
			if (acctSweepScheduler.size() > 0) {
				sweepSchedItr = acctSweepScheduler.iterator();
				while (sweepSchedItr.hasNext()) {
					accSweepSchedule = (AccSweepSchedule) sweepSchedItr.next();
					
					session.delete(accSweepSchedule);
					
				}
			}
			
			acctSweepBalGrp = getHibernateTemplate()
					.find("from AccountSweepBalanceGroup  accSwpBalGrp where accSwpBalGrp.id.hostId=?0 and accSwpBalGrp.id.entityId=?1 and accSwpBalGrp.id.accountId=?2" ,
							new Object[]{acct.getId().getHostId(), acct.getId().getEntityId(), acct.getId().getAccountId()});
			
			if (acctSweepBalGrp.size() > 0) {
				sweepBalGrpItr = acctSweepBalGrp.iterator();
				while (sweepBalGrpItr.hasNext()) {
					accSweepBalGrp = (AccountSweepBalanceGroup) sweepBalGrpItr.next();
					
					session.delete(accSweepBalGrp);
					
				}
			}
			
			deleteHQL = "delete from AcctMaintenance m where m.id.hostId=?0 and m.id.entityId=?1 and m.id.accountId=?2";

			deleteCount = session.createQuery(deleteHQL)
		            .setParameter(0, acct.getId().getHostId())
		            .setParameter(1, acct.getId().getEntityId())
		            .setParameter(2, acct.getId().getAccountId())
		            .executeUpdate();
			
			if (deleteCount == 0) {
				throw new SwtRecordNotExist();
			}
			tx.commit();
			log
					.debug(this.getClass().getName()
							+ "- [deleteAcctDetail] - Exit");
		} catch (Exception exp) {
			try {
				if (tx != null) {
					tx.rollback();
				}
			} catch (HibernateException exception) {
				log
						.error("HibernateException occured in rolling back transaction. Cause : "
								+ exception.getMessage());
			}
			  if (exp instanceof GenericJDBCException) {
				  GenericJDBCException gExp = (GenericJDBCException) exp;
				  
				 if(gExp.getCause() != null && !SwtUtil.isEmptyOrNull(gExp.getCause().getLocalizedMessage()) &&
						 gExp.getCause().getLocalizedMessage().indexOf(SwtErrorHandler.CHILD_RECORD_FOUND_ERROR) > -1) {
					 throw new SwtException("errors.DataIntegrityViolationExceptioninDelete");
				 }
					  
			  }
			log.error(this.getClass().getName()
					+ " - Exception caught in [deleteAcctDetail] method "
					+ exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp,
					"deleteAcctDetail", AcctMaintenanceDAOHibernate.class);

		} finally {
			HibernateException hThrownException = JDBCCloser.close(session);

			if (hThrownException != null)
				throw new SwtException(hThrownException.getMessage());
		}
	}

	/**
	 * @param hostId
	 * @param entityId
	 * @param accountId
	 * @return Collection - getSubColumnDataDetailList
	 */
	public Collection getSubColumnDataDetailList(String hostId,
			String entityId, String accountId) throws SwtException {
		log.debug("Entering getSubColumnDataDetailList(Currency curr): ");
		java.util.List list = (List ) getHibernateTemplate().find(
						"from AcctMaintenance acct where acct.id.entityId =?0 and acct.id.hostId=?1 and acct.minacctcode=?2 and acct.id.accountId != '*' ",
						new Object[] { entityId, hostId, accountId });
		return list;
	}

	/**
	 * @param entityId
	 * @param hostId
	 * @param currencyId
	 * @param accttype
	 * @return Collection - getAccountTypeDetailList
	 */
	public Collection getAccountTypeDetailList(String entityId, String hostId,
			String currencyId, String accttype) throws SwtException {
		log.debug("Entering getAccountTypeDetailList(Currency curr): ");
		java.util.List list = (List ) getHibernateTemplate().find(
						"from AcctMaintenance acct where acct.id.entityId =?0 and acct.id.hostId=?1 and acct.currcode=?2 and acct.accttype=?3",
						new Object[] { entityId, hostId, currencyId, accttype });
		return list;
	}

	/**
	 * @param entityId
	 * @param hostId
	 * 
	 * @param accttype
	 * @return Collection - getAccountTypeList
	 */
	public Collection getAccountTypeList(String entityId, String hostId,
			String accttype) throws SwtException {
		log.debug("Entering getAccountTypeList(Currency curr): ");
		java.util.List list = (List ) getHibernateTemplate().find(
						"from AcctMaintenance acct where acct.id.entityId =?0 and acct.id.hostId=?1 and acct.accttype=?2 and acct.id.accountId != '*'",
						new Object[] { entityId, hostId, accttype });
		return list;
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see org.swallow.maintenance.dao.AcctMaintenanceDAO#getAccountIDDropDown(java.lang.String,
	 *      java.lang.String)
	 */
	public Collection getAccountIDDropDown(String entityId, String hostId)
			throws SwtException {
		java.util.List list = (List ) getHibernateTemplate().find(
						"from AcctMaintenance acct where acct.id.entityId =?0 and acct.id.hostId=?1 and acct.id.accountId != '*' order by acct.id.accountId",
						new Object[] { entityId, hostId });
		return list;
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see org.swallow.maintenance.dao.AcctMaintenanceDAO#copyAccountIdDetails(java.lang.String)
	 */
	public AcctMaintenance copyAccountIdDetails(String hostId, String entityId,
			String accountId) throws SwtException {
		// Variable to hold AcctMaintenance object
		AcctMaintenance acctMaint = null;
		// Variable to hold Iterator object balMaintList
		Iterator balMaintListItr = null;
		java.util.List balMaintList = getHibernateTemplate()
				.find(
						"from AcctMaintenance acct where acct.id.hostId=?0 and acct.id.entityId=?1 and acct.id.accountId=?2 ",
						new Object[] { hostId, entityId, accountId });
		balMaintListItr = balMaintList.iterator();
		while (balMaintListItr.hasNext()) {
			acctMaint = (AcctMaintenance) balMaintListItr.next();
		}
		return acctMaint;
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see org.swallow.maintenance.dao.AcctMaintenanceDAO#getAccountTypeCurrencyList(java.lang.String,
	 *      java.lang.String, java.lang.String)
	 */
	public Collection getAccountTypeCurrencyList(String entityId,
			String hostId, String currencyId) throws SwtException {
		log.debug("Entering getAccountTypeCurrencyList(Currency curr): ");
		java.util.List list = (List ) getHibernateTemplate().find(
						"from AcctMaintenance acct where acct.id.entityId =?0 and acct.id.hostId=?1 and acct.currcode=?2 and acct.id.accountId != '*'",
						new Object[] { entityId, hostId, currencyId });
		return list;
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see org.swallow.maintenance.dao.AcctMaintenanceDAO#getCurrencyMasterList(java.lang.String,
	 *      java.lang.String)
	 */
	public ArrayList getCurrencyMasterList(String entityId, String hostId)
			throws SwtException {
		log.debug("Entering getCurrencyMasterList(Currency curr): ");
		ArrayList list = (ArrayList) getHibernateTemplate()
				.find(
						"select acct.id.currencyCode  from Currency acct where acct.id.entityId =?0 and acct.id.hostId=?1",
						new Object[] { entityId, hostId });
		return list;
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see org.swallow.maintenance.dao.AcctMaintenanceDAO#getMainAcctDetails(java.lang.String,
	 *      java.lang.String, java.lang.String)
	 */
	public AcctMaintenance getMainAcctDetails(String entityId, String hostId,
			String accountId) throws SwtException {
		// Variable to hold AcctMaintenance object
		AcctMaintenance acctMaint = null;
		// Variable to hold Iterator object balMaintList
		Iterator balMaintListItr = null;
		java.util.List balMaintList = getHibernateTemplate()
				.find(
						"from AcctMaintenance acct where  acct.id.hostId=?0 and acct.id.entityId=?1 and acct.id.accountId=?2",
						new Object[] { hostId, entityId, accountId });
		balMaintListItr = balMaintList.iterator();
		while (balMaintListItr.hasNext()) {
			acctMaint = (AcctMaintenance) balMaintListItr.next();
		}
		return acctMaint;
	}

	public Collection getCountryList() throws SwtException {
		log.debug("Entering 'getCountryList' method");
		java.util.List countryList = (List ) getHibernateTemplate().find(
				" from Country c order by c.countryName");
		return countryList;
	}

	/**
	 * @desc This method fetches a Collection of Account Objects which
	 * @param hostId -
	 *            HostID
	 * @param entityId -
	 *            EntityID
	 * @param currencyCode -
	 *            CurrencyCode
	 * @return Collection - Collection of AcctMaintenance Objects
	 * @throws SwtException -
	 *             SwtException
	 */
	public Collection getLinkAccountList(String hostId, String entityId,
			String currencyCode) throws SwtException {
		log.debug("Entering getLinkAccountList method");
		String HQLGETLINKACCOUNTS = " from AcctMaintenance acct where  acct.id.hostId=?0 and acct.id.entityId=?1 and acct.currcode=?2  and acct.acctstatusflg='O' and acct.id.accountId != '*'";
		HQLGETLINKACCOUNTS += " and acct.id.accountId Not In ( select p.id.accountId from AcctMaintenance p where p.id.entityId=?3 and p.linkAccID is not null) order by acct.id.accountId ";
		ArrayList linkAccountColl = new ArrayList();
		linkAccountColl = (ArrayList) getHibernateTemplate().find(
				HQLGETLINKACCOUNTS,
				new Object[] { hostId, entityId, currencyCode, entityId});
		log.debug("Exiting getLinkAccountList method");
		return linkAccountColl;
	}

	/**
	 * 
	 * @param hostId -
	 *            HostID
	 * @param entityId -
	 *            EntityID
	 * @param currencyCode -
	 *            CurrencyCode
	 * @param accountId -
	 *            accountId
	 * @return Collection - Collection of AcctMaintenance Objects
	 * @throws SwtException -
	 *             SwtException
	 */
	public Collection getLinkAccountListInChange(String hostId,
			String entityId, String currencyCode, String accountId)
			throws SwtException {
		log.debug("Entering getLinkAccountListInChange method");
		StringBuffer HqlLinkAccounts = new StringBuffer();
		ArrayList linkAccountColl = new ArrayList();
		if (currencyCode.equals(SwtConstants.ALL_VALUE)) {
			HqlLinkAccounts
					.append(" from AcctMaintenance acct where  acct.id.hostId=?0 and acct.id.entityId=?1 and acct.acctstatusflg='O'");
			HqlLinkAccounts
					.append(" and acct.id.accountId Not in ('*',?2) and acct.id.accountId Not In ( select p.id.accountId from AcctMaintenance p where p.linkAccID is not null) order by acct.id.accountId");
			linkAccountColl = (ArrayList) getHibernateTemplate().find(
					HqlLinkAccounts.toString(),
					new Object[] { hostId, entityId, accountId });
		} else {
			HqlLinkAccounts
					.append(" from AcctMaintenance acct where  acct.id.hostId=?0 and acct.id.entityId=?1 and acct.currcode=?2  and acct.acctstatusflg='O'");
			HqlLinkAccounts
					.append(" and acct.id.accountId Not in ('*',?3) and acct.id.accountId Not In ( select p.id.accountId from AcctMaintenance p where p.linkAccID is not null) order by acct.id.accountId");
			linkAccountColl = (ArrayList) getHibernateTemplate().find(
					HqlLinkAccounts.toString(),
					new Object[] { hostId, entityId, currencyCode, accountId });
		}
		log.debug("Exiting getLinkAccountListInChange method");
		return linkAccountColl;
	}

	/**
	 * @desc - This method finds all the Accounts linked with this account
	 * @param hostId -
	 *            HostID
	 * @param entityId -
	 *            EntityID
	 * @param accountId -
	 *            AccountID
	 * @return Collection - Collection of Accounts Objects
	 * @throws SwtException -
	 *             SwtException
	 * 
	 */
	public Collection getLinkedAccounts(String hostId, String entityId,
			String accountId) throws SwtException {
		log.debug("Entering getLinkedAccounts method");
		String HQLGETLINKACCOUNTS = " from AcctMaintenance acct where  acct.id.hostId=?0 and acct.id.entityId=?1 and acct.linkAccID = ?2";
		List linkedAccountsDetailsList = (ArrayList) getHibernateTemplate()
				.find(HQLGETLINKACCOUNTS,
						new Object[] { hostId, entityId, accountId });
		log.debug("Exiting getLinkedAccounts method");
		return linkedAccountsDetailsList;
	}

	/**
	 * @desc This function is used to get the account interest rate list which
	 *       comes from P_ACCOUNT_INTEREST_RATE
	 * @param hostId-HostID
	 * @param entityId-EntityID
	 * @param accountId-AccountID
	 * @return- accInterestRateListcoll --account Interest rate collection
	 * @throws SwtException
	 */
	public Collection getAccInterestRateList(String hostId, String entityId,
			String accountId) throws SwtException {
		log.debug("Entering 'getAccInterestRateList' method");
		final String HQL_GETACCOUNTINTERESTRATES = "from AccountInterestRate  accIntRate where accIntRate.id.hostId=?0 and accIntRate.id.entityId=?1 and accIntRate.id.accountId=?2 order by accIntRate.id.interestDateRate desc";
		List accInterestRateListcoll = (ArrayList) getHibernateTemplate().find(
				HQL_GETACCOUNTINTERESTRATES,
				new Object[] { hostId, entityId, accountId });
		log.debug("Exiting getAccInterestRateList method");
		return accInterestRateListcoll;
	}

	/**
	 * 
	 * @param hostId -
	 *            hostId
	 * @param entityId -
	 *            entityId
	 * @param currencyCode -
	 *            CurrencyCode
	 * @return Collection - returns Collection of AcctMaintenance objects
	 * @throws SwtException -
	 *             SwtException
	 */
	public Collection getMainAcctListByCurr(String hostId, String entityId,
			String currencyCode) throws SwtException {
		log.debug("Entering getMainAcctListByCurr method ");
		final StringBuffer HQL_QUERY = new StringBuffer();
		java.util.List acctList = new ArrayList();
		if (currencyCode.equals(SwtConstants.ALL_VALUE)) {
			HQL_QUERY
					.append(" from AcctMaintenance acct where  acct.id.hostId=?0 and acct.id.entityId=?1 "
							+ " and acct.acctlevel='M' and acct.id.accountId != '*' order by acct.id.accountId asc");
			acctList = (List ) getHibernateTemplate().find(HQL_QUERY.toString(),
					new Object[] { hostId, entityId });
		} else {
			HQL_QUERY
					.append("from AcctMaintenance acct where  acct.id.hostId=?0 and acct.id.entityId=?1 "
							+ "and acct.currcode=?2 and acct.acctlevel='M' and acct.id.accountId != '*' order by acct.id.accountId asc");
			acctList = (List ) getHibernateTemplate().find(HQL_QUERY.toString(),
					new Object[] { hostId, entityId, currencyCode });
		}
		log.debug("Exiting getMainAcctListByCurr method");
		return acctList;
	}

	public Collection getIntermediaryRecord(String hostId, String entityId,
			String currencyCode, String acctBicCode) throws SwtException {
		log.debug("Entering getIntermediaryRecord method");
		java.util.List records = getHibernateTemplate()
				.find(
						"from SweepIntermediaries acct where  acct.id.hostId=?0 and acct.id.entityId=?1 "
								+ " and acct.id.currencyCode=?2 and acct.id.targetBic=?3",
						new Object[] { hostId, entityId, currencyCode,
								acctBicCode });
		log.debug("Exiting getIntermediaryRecord method");
		return records;
	}

	/**
	 * This function is used to get the details of country for given countryId
	 * 
	 * @param countryId -
	 *            String
	 * @return -Country
	 * @throws SwtException
	 */
	public Country getCountryDetail(String countryId) throws SwtException {
		// Country
		Country country = null;
		// Country List
		List countryList = null;
		// Iterator for Country List
		Iterator countryListItr = null;
		try {
			log.debug(this.getClass().getName()
					+ " - [getCountryDetail] - Entry");
			// get the country details for given country code
			countryList = (List ) getHibernateTemplate().find(
					"from Country country where country.countryCode=?0",
					new Object[] { countryId });
			// iterate the country list
			countryListItr = countryList.iterator();
			while (countryListItr.hasNext()) {
				// get the country
				country = (Country) countryListItr.next();
			}
			log.debug(this.getClass().getName()
					+ " - [getCountryDetail] - Exit");
		} catch (Exception e) {
			log.error("An exception occured in "
					+ "AcctMaintenanceDAOHibernate : getCountryDetail "
					+ e.getMessage());
			throw new SwtException(e.getMessage());
		} finally {
			// nullify objects
			countryList = null;
			countryListItr = null;
		}
		return country;
	}

	/**
	 * This function is used to get the details of currency for given currency
	 * code
	 * 
	 * @param entityId -
	 *            String
	 * @param hostId -
	 *            String
	 * @param currencyCode -
	 *            String
	 * @return -Currency
	 * @throws SwtException
	 */
	public Currency getCurrencyDetail(String entityId, String hostId,
			String currencyCode) throws SwtException {
		// Currency
		Currency currency = null;
		// currencyList
		List currencyList = null;
		// Iterator
		Iterator currencyListItr = null;
		try {
			log.debug(this.getClass().getName()
					+ " - [getCurrencyDetail] - Entry");
			// get the currency list for given currency code
			currencyList = (List ) getHibernateTemplate().find(
							"from Currency c where c.id.hostId=?0 and c.id.entityId=?1 and c.id.currencyCode=?2",
							new Object[] { hostId, entityId, currencyCode });
			// iterate the currency list
			currencyListItr = currencyList.iterator();
			while (currencyListItr.hasNext()) {
				// get the currency
				currency = (Currency) currencyListItr.next();
			}
			log.debug(this.getClass().getName()
					+ " - [getCurrencyDetail] - Exit");
		} catch (Exception e) {
			log.error("An exception occured in "
					+ "AcctMaintenanceDAOHibernate : getCurrencyDetail "
					+ e.getMessage());
			throw new SwtException(e.getMessage());

		} finally {
			// nullify objects
			currencyList = null;
			currencyListItr = null;
		}
		return currency;
	}
	
	public String checkIfPartyIdExists(String hostId, String entityId,
			String partyId) throws SwtException {
		log.debug("Entering checkIfPartyIdExists method");
		java.util.List records = getHibernateTemplate()
				.find(
						"from Party p where  p.id.hostId=?0 and p.id.entityId=?1 "
								+ " and p.id.partyId=?2",
						new Object[] { hostId, entityId, partyId });
		log.debug("Exiting getIntermediaryRecord method");
		return (records.size() == 0) ? "false" : "true";
	}

	public String checkAccountIlmDataMember(String hostId, String entityId, String currency, String account)
			throws SwtException {
		Connection conn = null;
		ResultSet res = null;
		PreparedStatement stmt = null;
		ResultSet rs = null;
		String result = "";
		
		try {
			log.debug("AcctMaintenanceDAOHibernate  - [checkAccountIlmDataMember] - Enter");
			
			conn = ConnectionManager.getInstance().databaseCon();
			String checkAccountQuery = "SELECT DECODE (COUNT (*), 0, 'N', 'Y') IS_ILM_ACCT"
			  + " FROM TABLE ("
			          +"PKG_ILM.FN_GET_ACCS ( ?, ?, ?)) T"
			 +" WHERE ACCOUNT_Id = ?";
			
			stmt = conn.prepareStatement(checkAccountQuery);
			stmt.setString(1, hostId);
			stmt.setString(2, entityId);
			stmt.setString(3, currency);
			stmt.setString(4, account);
			stmt.execute();
			
			/* Fetching the Result */
			rs = (ResultSet) stmt.getResultSet();
			if (rs != null) {
				/* Loop to iterate result set */
				while (rs.next()) {
					result = rs.getString(1);
				}
			}
			
			log.debug("AcctMaintenanceDAOHibernate  - [checkAccountIlmDataMember] - Exit");
		} catch (SQLException ex) {
			try {
				log.error("AcctMaintenanceDAOHibernate  - [checkAccountIlmDataMember]  Exception: "
					+ ex.getMessage());
				// rollBack
				conn.rollback();
			} catch (SQLException e) {
			
			}

		} catch (Exception e) {
			log.error("An exception occured in "
					+ "AcctMaintenanceDAOHibernate : checkAccountIlmDataMember "
					+ e.getMessage());
			throw new SwtException(e.getMessage());
			
		} finally {
			//Close conn and nullify objects
			Object[] exceptions = JDBCCloser.close(res, stmt, conn, null);
		}
		
		return result;
	}
	
	
	public boolean checkIfAccountExistForEntity(String hostId, String entityId, String currency, String account)
			throws SwtException {
		Connection conn = null;
		ResultSet res = null;
		PreparedStatement stmt = null;
		ResultSet rs = null;
		boolean result = false;

		try {
			log.debug("AcctMaintenanceDAOHibernate  - [checkIfAccountExistForEntity] - Enter");

			conn = ConnectionManager.getInstance().databaseCon();
			String checkAccountQuery = "select 1 from p_account acct where  acct.host_id=? and acct.entity_id=? and acct.currency_code=? and acct.account_id=?";

			stmt = conn.prepareStatement(checkAccountQuery);
			stmt.setString(1, hostId);
			stmt.setString(2, entityId);
			stmt.setString(3, currency);
			stmt.setString(4, account);
			stmt.execute();
			/* Fetching the Result */
			rs = (ResultSet) stmt.getResultSet();
			if (rs != null) {
				if(rs.next() == false) {
					result=false;
				}else {
					result=true;
				}
					
			}

			log.debug("AcctMaintenanceDAOHibernate  - [checkIfAccountExistForEntity] - Exit");
		} catch (SQLException ex) {
			try {
				log.error("AcctMaintenanceDAOHibernate  - [checkIfAccountExistForEntity]  Exception: " + ex.getMessage());
				// rollBack
				conn.rollback();
			} catch (SQLException e) {

			}

		} catch (Exception e) {
			log.error("An exception occured in " + "AcctMaintenanceDAOHibernate : checkIfAccountExistForEntity "
					+ e.getMessage());
			throw new SwtException(e.getMessage());

		} finally {
			// Close conn and nullify objects
			Object[] exceptions = JDBCCloser.close(res, stmt, conn, null);
		}

		return result;
	}
	
	
	/**
	 * This method is used to check if book code exists in P_ACCOUNT
	 */

	public boolean checkIfAccountExists(String hostId, String accountId)
			throws SwtException {
		Connection conn = null;
		ResultSet res = null;
		PreparedStatement stmt = null;
		ResultSet rs = null;
		boolean result = false;

		try {
			log.debug("AcctMaintenanceDAOHibernate  - [checkIfAccountExists] - Enter");

			conn = ConnectionManager.getInstance().databaseCon();
			String checkAccountQuery =  "select 1 from p_account acc where acc.host_id=?0 and acc.account_id=?1";

			stmt = conn.prepareStatement(checkAccountQuery);
			stmt.setString(1, hostId);
			stmt.setString(2, accountId);
			stmt.execute();
			/* Fetching the Result */
			rs = (ResultSet) stmt.getResultSet();
			if (rs != null) {
				if(rs.next() == false) {
					result=false;
				}else {
					result=true;
				}
					
			}

			log.debug("AcctMaintenanceDAOHibernate  - [checkIfAccountExists] - Exit");
		} catch (SQLException ex) {
			try {
				log.error("AcctMaintenanceDAOHibernate  - [checkIfAccountExists]  Exception: " + ex.getMessage());
				// rollBack
				conn.rollback();
			} catch (SQLException e) {

			}

		} catch (Exception e) {
			log.error("An exception occured in " + "AcctMaintenanceDAOHibernate : checkIfAccountExists "
					+ e.getMessage());
			throw new SwtException(e.getMessage());

		} finally {
			// Close conn and nullify objects
			Object[] exceptions = JDBCCloser.close(res, stmt, conn, null);
		}

		return result;
	}
	
	public Collection<AccSweepSchedule> getAcctSweepScheduleList(String hostId, String entityId, String accountId, boolean fromAcctMaintenace) throws SwtException{
		log.debug("Entering 'getAccInterestRateList' method");
		List accInterestRateListcoll = null;
		try {
			if(fromAcctMaintenace) {
			final String HQL_GETACCOUNTINTERESTRATES = "from AccSweepSchedule  accSweep where accSweep.hostId=?0 and accSweep.entityId=?1 and accSweep.accountId=?2 order by accSweep.sweepScheduleId asc";
			accInterestRateListcoll = (ArrayList) getHibernateTemplate().find(
					HQL_GETACCOUNTINTERESTRATES,
					new Object[] { hostId, entityId, accountId });
			}else {
				
				final String HQL_GETACCOUNTINTERESTRATES = "from AccSweepSchedule  accSweep where accSweep.sweepAccountHostId=?0 and accSweep.sweepAccountEntity=?1 and accSweep.sweepAccountId=?2 order by accSweep.sweepScheduleId asc";
				accInterestRateListcoll = (ArrayList) getHibernateTemplate().find(
						HQL_GETACCOUNTINTERESTRATES,
						new Object[] { hostId, entityId, accountId });
			}
			log.debug("Exiting getAccInterestRateList method");
		
		} catch (Exception e) {
			// TODO: handle exception
			e.printStackTrace();
		}
		return accInterestRateListcoll;
	}
	
	public Collection<AccSweepSchedule> getAcctSweepScheduleListBetweenAccounts(String hostId, String entityId, String currencyCode, String accountId,
			String otherEntityId, String otherAccountId) throws SwtException{
		log.debug("Entering 'getAcctSweepScheduleListBetweenAccounts' method");
		/* Method's local variable declaration */
		Session session = null;
		Connection conn=null;
		CallableStatement pstmt=null;
		ResultSet rsData = null;
		AccSweepSchedule accSweepSchedule = null;
		
		ArrayList<AccSweepSchedule> result  = new ArrayList<AccSweepSchedule>();
		try {
			
			log.debug(this.getClass().getName()
					+ " - [getAcctSweepScheduleListBetweenAccounts] - " + "Entry");

			/*
			 * Returns the HibernateTemplate and session factory for this DAO,
			 * and opening a Hibernate Session
			 */
			session = getHibernateTemplate().getSessionFactory().openSession();
			conn = SwtUtil.connection(session);
			/* Using Callable statement to execute the Stored Procedure */
			pstmt=conn.prepareCall("{ ? = call sweeping_process.FN_GET_ACC_SWP_SCHEDULE(?,?,?,?,?)}");
			pstmt.registerOutParameter(1, oracle.jdbc.OracleTypes.CURSOR);
			pstmt.setString(2,hostId);
			pstmt.setString(3,entityId);
			pstmt.setString(4,accountId);
			pstmt.setString(5,otherEntityId);
			pstmt.setString(6,otherAccountId);
		
			pstmt.execute();
			/* Fetching the Result */
			// Account details
			rsData = (ResultSet) pstmt.getObject(1);
			
			if (rsData != null) {
				// Iterate through data RS, get account monitor details and put
				// them in a collection
				while (rsData.next()) {
					// Initialize new account monitor bean to hold account
					// details
					accSweepSchedule = new AccSweepSchedule();
					accSweepSchedule.setCurrencyCode(rsData.getString(1));

					accSweepSchedule.setAccountId(rsData.getString("ACCOUNT_ID"));
					accSweepSchedule.setAllowMultiple(rsData.getString("ALLOW_MULTIPLE"));
					accSweepSchedule.setEntityId(rsData.getString("ENTITY_ID"));
					accSweepSchedule.setHostId(rsData.getString("HOST_ID"));
					accSweepSchedule.setMinAmount(rsData.getDouble("MIN_AMOUNT"));
					accSweepSchedule.setOtherAccSettleMethodCr(rsData.getString("OTHER_ACC_SETTLE_METHOD_CR"));
					accSweepSchedule.setOtherAccSettleMethodDr(rsData.getString("OTHER_ACC_SETTLE_METHOD_DR"));
					accSweepSchedule.setOtherAccSweepBookcodeCr(rsData.getString("OTHER_ACC_SWEEP_BOOKCODE_CR"));
					accSweepSchedule.setOtherAccSweepBookcodeDr(rsData.getString("OTHER_ACC_SWEEP_BOOKCODE_DR"));
					accSweepSchedule.setScheduleFrom(rsData.getString("SCHEDULE_FROM"));
					accSweepSchedule.setScheduleTo(rsData.getString("SCHEDULE_TO"));
					accSweepSchedule.setSweepAccountEntity(rsData.getString("SWEEP_ACCOUNT_ENTITY"));
					accSweepSchedule.setSweepAccountHostId(rsData.getString("HOST_ID"));
					accSweepSchedule.setSweepAccountId(rsData.getString("SWEEP_ACCOUNT_ID"));
					accSweepSchedule.setSweepDirection(rsData.getString("SWEEP_DIRECTION"));
					accSweepSchedule.setSweepFromBalanceType(rsData.getString("SWEEP_FROM_BALANCE_TYPE"));
					//Mantis 6298
					accSweepSchedule.setOtherSweepFromBalType(rsData.getString("OTHER_SWEEP_FROM_BAL_TYPE"));
					accSweepSchedule.setSweepOnGroupBalance(rsData.getString("SWEEP_ON_GROUP_BALANCE"));
					accSweepSchedule.setSweepScheduleId(rsData.getLong("SWEEP_SCHEDULE_ID"));
					accSweepSchedule.setTargetBalance(rsData.getDouble("TARGET_BALANCE"));
					accSweepSchedule.setTargetBalanceType(rsData.getString("TARGET_BALANCE_TYPE"));
					accSweepSchedule.setTargetBalanceTypeId(rsData.getString("TARGET_BALANCE_TYPE_ID"));
					accSweepSchedule.setThisAccSettleMethodCr(rsData.getString("THIS_ACC_SETTLE_METHOD_CR"));
					accSweepSchedule.setThisAccSettleMethodDr(rsData.getString("THIS_ACC_SETTLE_METHOD_DR"));
					accSweepSchedule.setThisAccSweepBookcodeCr(rsData.getString("THIS_ACC_SWEEP_BOOKCODE_CR"));
					accSweepSchedule.setThisAccSweepBookcodeDr(rsData.getString("THIS_ACC_SWEEP_BOOKCODE_DR"));

					result.add(accSweepSchedule);
				}
			}
			
		} catch (Exception e) {
			e.printStackTrace();
			log
					.error(this.getClass().getName()
							+ " - Exception Catched in [getAcctSweepScheduleListBetweenAccounts] method : - "
							+ e.getMessage());
			 throw SwtErrorHandler.getInstance().handleException(e, "getAcctSweepScheduleListBetweenAccounts", AcctMaintenanceDAOHibernate.class);
		} finally {
			
			SwtException thrownException = null;
			Object[] exceptions = JDBCCloser.close(rsData,pstmt, conn, session);

			if (exceptions[0] != null)
				thrownException = SwtErrorHandler.getInstance()
						.handleException((SQLException) exceptions[0],
								"getAcctSweepScheduleListBetweenAccounts",
								AcctMaintenanceDAOHibernate.class);
		
			if (thrownException == null && exceptions[1] != null)
				thrownException = SwtErrorHandler.getInstance()
						.handleException((HibernateException) exceptions[1],
								"getAcctSweepScheduleListBetweenAccounts",
								AcctMaintenanceDAOHibernate.class);

			if (thrownException != null)
				throw thrownException;
		}
		log.debug("Exiting getAcctSweepScheduleListBetweenAccounts method");
		return result;
	}

	@Override
	public long getAcctSweepScheduleUsedinCount(String hostId, String entityId, String accountId) throws SwtException {
		
		java.util.List records = getHibernateTemplate()
				.find(
						"select count(*) from AccSweepSchedule c where c.sweepAccountHostId=?0 and c.sweepAccountEntity=?1 and c.sweepAccountId=?2",
						new Object[] { hostId, entityId, accountId });
		Long noofRecords = (Long) records.get(0);
		
		return noofRecords;
	}

	@Override
	public AccSweepSchedule getAcctSweepScheduleDetails(String hostId, String seqNumber) throws SwtException {
		AccSweepSchedule acctMaint = null;
		// Variable to hold Iterator object balMaintList
		Iterator balMaintListItr = null;
		java.util.List balMaintList = getHibernateTemplate()
				.find(
						"from AccSweepSchedule  accSweep where accSweep.sweepAccountHostId=?0 and accSweep.sweepScheduleId=?1 ",
						new Object[] { hostId, seqNumber });
		balMaintListItr = balMaintList.iterator();
		while (balMaintListItr.hasNext()) {
			acctMaint = (AccSweepSchedule) balMaintListItr.next();
		}
		return acctMaint;
	}

	public void saveOrUpdateAcctScheduleSweep(AccSweepSchedule accountSchedule, String methodName) throws SwtException {
		Session session = null;
		Transaction tx = null;
		SwtInterceptor interceptor = null;
		try {
			if ("save".equalsIgnoreCase(methodName)) {
				interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");
				session = getHibernateTemplate().getSessionFactory().withOptions().interceptor(interceptor).openSession();
				tx = session.beginTransaction();
				session.save(accountSchedule);
				tx.commit();
			} else {
				java.util.List records = getHibernateTemplate().find(
						"select count(*) from AccSweepSchedule accSweep where accSweep.sweepScheduleId =?0",
						new Object[] { accountSchedule.getSweepScheduleId() });
				Long noofRecords = (Long) records.get(0);
				if(noofRecords  > 0) {
					interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");
					session = getHibernateTemplate().getSessionFactory().withOptions().interceptor(interceptor).openSession();
					tx = session.beginTransaction();
					session.update(accountSchedule);
					tx.commit();
				}else {
					throw new SwtRecordNotExist();
				}
			}

		} catch (Exception exp) {
			try {
				if (tx != null) {
					tx.rollback();
				}
			} catch (HibernateException exception) {
				log.error("HibernateException occured in rolling back transaction. Cause : " + exception.getMessage());
			}
			log.error(this.getClass().getName() + " - Exception caught in [saveOrUpdateAcctScheduleSweep] method "
					+ exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp, "saveOrUpdateAcctScheduleSweep",
					AcctMaintenanceDAOHibernate.class);

		} finally {
			HibernateException hThrownException = JDBCCloser.close(session);

			if (hThrownException != null)
				throw new SwtException(hThrownException.getMessage());
		}

	}
	/**
	 * Delete account schedule sweep record using passed seq number
	 * @param accountSchedule
	 * @throws SwtException
	 */
	public void deleteAcctScheduleSweep(AccSweepSchedule accountSchedule) throws SwtException {
		Session session = null;
		Transaction tx = null;
		SwtInterceptor interceptor = null;
		String deleteHQL = null;
		 int deleteCount = 0;
		try {
			interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");
			session = getHibernateTemplate().getSessionFactory().withOptions().interceptor(interceptor).openSession();
			tx = session.beginTransaction();
			deleteHQL = "delete from AccSweepSchedule accSweep where accSweep.sweepScheduleId = :sweepScheduleId";

			deleteCount = session.createQuery(deleteHQL)
					.setParameter("sweepScheduleId", accountSchedule.getSweepScheduleId())
					.executeUpdate();
			
			if (deleteCount == 0) {
				throw new SwtRecordNotExist();
			}
			tx.commit();
		} catch (Exception exp) {
			exp.printStackTrace();
			try {
				if (tx != null) {
					tx.rollback();
				}
			} catch (HibernateException exception) {
				log.error("HibernateException occured in rolling back transaction. Cause : " + exception.getMessage());
			}
			log.error(this.getClass().getName() + " - Exception caught in [deleteAcctScheduleSweep] method "
					+ exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp, "deleteAcctScheduleSweep",
					AcctMaintenanceDAOHibernate.class);

		} finally {
			HibernateException hThrownException = JDBCCloser.close(session);

			if (hThrownException != null)
				throw new SwtException(hThrownException.getMessage());
		}

	}
	
	public Collection getAcctSweepBalGrpcoll(String hostId, String entityId, String accountId)
			throws SwtException {
		log.debug("hostID for getAcctSweepBalGrpcoll: " + hostId);
		java.util.List noofRecords = (List ) getHibernateTemplate().find(
						"from AccountSweepBalanceGroup acct where  acct.id.hostId=?0 and acct.id.entityId=?1  and acct.id.accountId =?2",
						new Object[] { hostId, entityId, accountId });
		log.debug("exiting getAcctSweepBalGrpcoll");
		return noofRecords;
	}
	
	
	

	public void saveAcctSweepBalGrp(AccountSweepBalanceGroup acctSweepBalGrp) throws SwtException {
		Session session = null;
		Transaction tx = null;
		SwtInterceptor interceptor = null;
		try {
				interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");
				session = getHibernateTemplate().getSessionFactory().withOptions().interceptor(interceptor).openSession();
				tx = session.beginTransaction();
				session.save(acctSweepBalGrp);
				tx.commit();

		} catch (Exception exp) {
			try {
				if (tx != null) {
					tx.rollback();
				}
			} catch (HibernateException exception) {
				log.error("HibernateException occured in rolling back transaction. Cause : " + exception.getMessage());
			}
			log.error(this.getClass().getName() + " - Exception caught in [saveAcctSweepBalGrp] method "
					+ exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp, "saveAcctSweepBalGrp",
					AcctMaintenanceDAOHibernate.class);

		} finally {
			HibernateException hThrownException = JDBCCloser.close(session);

			if (hThrownException != null)
				throw new SwtException(hThrownException.getMessage());
		}

	}
	/**
	 * Delete account sweep balance group record using passed sweep account id
	 * @param accountSchedule
	 * @throws SwtException
	 */
        public void deleteAcctSweepBalGrp(AccountSweepBalanceGroup acctSweepBalGrp)
			throws SwtException {
		Session session = null;
		Transaction tx = null;
		SwtInterceptor interceptor = null;
		try {
			log.debug(this.getClass().getName() + " - [deleteAcctSweepBalGrp] - Enter");
			interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");
			session = getHibernateTemplate().getSessionFactory().withOptions().interceptor(interceptor).openSession();
			tx = session.beginTransaction();
			session.delete(acctSweepBalGrp);
			tx.commit();
			log.debug(this.getClass().getName() + " - [deleteAcctSweepBalGrp] - Exit");
		} catch (Exception e) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [deleteAcctSweepBalGrp] method : - "
					+ e.getMessage());

			throw SwtErrorHandler.getInstance().handleException(e,
					"deleteAcctSweepBalGrp",
					AcctMaintenanceDAOHibernate.class);
		}  finally {
			JDBCCloser.close(session);
		}
	}
	
        
        /**
    	 * @desc This method fetches a Collection of Account Objects which
    	 * @param hostId -
    	 *            HostID
    	 * @param entityId -
    	 *            EntityID
    	 * @param currencyCode -
    	 *            CurrencyCode
    	 * @return Collection - Collection of AcctMaintenance Objects
    	 * @throws SwtException -
    	 *             SwtException
    	 */
    	public Collection getLinkAccountFullList(String hostId, String entityId,
    			String currencyCode) throws SwtException {
    		log.debug("Entering getLinkAccountFullList method");
    		String HQLGETLINKACCOUNTS = " from AcctMaintenance acct where  acct.id.hostId=?0 and acct.id.entityId=?1 "
    				+ "and acct.currcode=?2  and acct.acctstatusflg='O' and acct.id.accountId != '*' order by acct.id.accountId ";
    		ArrayList linkAccountColl = new ArrayList();
    		linkAccountColl = (ArrayList) getHibernateTemplate().find(
    				HQLGETLINKACCOUNTS,
    				new Object[] { hostId, entityId, currencyCode });
    		log.debug("Exiting getLinkAccountFullList method");
    		return linkAccountColl;
    	}    
    	
    	
    	public Collection getAcctAttributesList() throws SwtException {
    		log.debug("Entering getAcctAttributesList method");
    		String HQLGETACCTATTRIBUTES = " from AccountAttributeFuncGroup acctAtt  where acctAtt.functionalGroup = 'SWEEP_TARGET_BALANCE'";
    		ArrayList acctAttributesColl = new ArrayList();
    		acctAttributesColl = (ArrayList) getHibernateTemplate().find(
    				HQLGETACCTATTRIBUTES,
    				new Object[] {});
    		log.debug("Exiting getAcctAttributesList method");
    		return acctAttributesColl;
    	}    

    	
    	public Collection getAllowedTrgBalRulesList() throws SwtException {
    		log.debug("Entering getAllowedTrgBalRulesList method");
    		String HQLGETACCTATTRIBUTES = " from SweepRule swpRule";
    		ArrayList sweepRuleColl = new ArrayList();
    		sweepRuleColl = (ArrayList) getHibernateTemplate().find(
    				HQLGETACCTATTRIBUTES,
    				new Object[] {});
    		log.debug("Exiting getAllowedTrgBalRulesList method");
    		return sweepRuleColl;
    	}  
    	
    	public ArrayList<Party> getPartySearchResult(String partyId, String partyName, String entityId, int pageSize,
    			int currentPage, String selectedsort) throws SwtException {

    		Party party = null;
    		ArrayList<Party> partiesCollection = null;
    		String sort = null;
    		String[] selectedSortArray = null;
    		String query = null;
    		PreparedStatement pst = null;
    		ResultSet rs = null;
    		Connection conn = null;
    		Session session= null;
    		try {
    			log.debug(this.getClass().getName() + " - [getPartySearchResult] - " + "Entry");
    			partiesCollection = new ArrayList();
    			selectedsort = "0|ASC";
    			if (!SwtUtil.isEmptyOrNull(selectedsort)) {
    				selectedSortArray = selectedsort.split("\\|");
    				if ("0".equals(selectedSortArray[0])) {
    					sort = "PARTY_ID ";
    				} else {
    					sort = "PARTY_NAME ";
    				}
    				if ("true".equals(selectedSortArray[1])) {
    					sort += "DESC";
    				} else {
    					sort += "ASC";
    				}
    				
    			}
    			conn = SwtUtil.connection(session);
    			/*
    			  SELECT *
    			    FROM (SELECT ROW_NUMBER () OVER (ORDER BY PARTY_ID ASC) ROW_, query.*
    			            FROM (SELECT *
    			                    FROM p_party
    			                   WHERE     entity_id = :entityId
    			                         AND UPPER (party_id) LIKE UPPER ( :partyId || '%')
    			                         AND UPPER (party_name) LIKE UPPER ( :partyName || '%'))
    			                 query)
    			   WHERE     ROW_ > ( :currentPage - 1) * :pageSize
    			         AND ROW_ < :currentPage * :pageSize + 1
    			 */
    			query = "SELECT * "+
    				    "FROM (SELECT ROW_NUMBER () OVER (ORDER BY " + sort +") ROW_, query.* "+
    		            "FROM (SELECT * "+
    		                    "FROM p_party "+
    		                   "WHERE     entity_id = ? "+
    		                         "AND UPPER (party_id) LIKE UPPER ( ? || '%') "+
    		                         "AND UPPER (party_name) LIKE UPPER ( ? || '%')) "+
    		                 "query) "+
    		            "WHERE     ROW_ > ( ? - 1) * ? "+
    		            "AND ROW_ < ? * ? + 1";
    			
    			if(SwtUtil.isEmptyOrNull(entityId)) {
    				query = "SELECT * "+
    					    "FROM (SELECT ROW_NUMBER () OVER (ORDER BY " + sort +") ROW_, query.* "+
    			            "FROM (SELECT * "+
    			                    "FROM p_party "+
    			                   "WHERE  UPPER (party_id) LIKE UPPER ( ? || '%') "+
    			                         "AND UPPER (party_name) LIKE UPPER ( ? || '%')) "+
    			                 "query) "+
    			            "WHERE     ROW_ > ( ? - 1) * ? "+
    			            "AND ROW_ < ? * ? + 1";
    				pst = conn.prepareStatement(query);
    				pst.setString(1, partyId);
    				pst.setString(2, partyName);
    				pst.setInt(3, currentPage);
    				pst.setInt(4, pageSize);
    				pst.setInt(5, currentPage);
    				pst.setInt(6, pageSize);
    				
    			}else {
    				query = "SELECT * "+
    					    "FROM (SELECT ROW_NUMBER () OVER (ORDER BY " + sort +") ROW_, query.* "+
    			            "FROM (SELECT * "+
    			                    "FROM p_party "+
    			                   "WHERE     entity_id = ? "+
    			                         "AND UPPER (party_id) LIKE UPPER ( ? || '%') "+
    			                         "AND UPPER (party_name) LIKE UPPER ( ? || '%')) "+
    			                 "query) "+
    			            "WHERE     ROW_ > ( ? - 1) * ? "+
    			            "AND ROW_ < ? * ? + 1";
    				
    				pst = conn.prepareStatement(query);
    				pst.setString(1, entityId);
    				pst.setString(2, partyId);
    				pst.setString(3, partyName);
    				pst.setInt(4, currentPage);
    				pst.setInt(5, pageSize);
    				pst.setInt(6, currentPage);
    				pst.setInt(7, pageSize);
    				
    			}
    			
    			rs = pst.executeQuery();
    			
    			while (rs.next()) {
    				party = new Party();
    				party.getId().setPartyId(rs.getString("PARTY_ID"));
    				party.setPartyName(rs.getString("PARTY_NAME"));
    				partiesCollection.add(party);
    			}

    			log.debug(this.getClass().getName() + " - [getPartySearchResult] - " + "Exit");
    			return partiesCollection;

    		} catch (Exception exp) {
    			exp.printStackTrace();
    			// log error message
    			log.error(this.getClass().getName() + " - Exception Caught in [getPartySearchResult] method : - "
    					+ exp.getMessage());
    			throw SwtErrorHandler.getInstance().handleException(exp, "getPartySearchResult", this.getClass());
    		} finally {
    			Object[] exceptions = JDBCCloser.close(rs, pst, conn, null);
    			// Debug Message
    			log.debug(this.getClass().getName() + "- [ getPartySearchResult ] - Exit");
    		}
    	}

    	
    	public int getTotalCount(String partyName, String partyId, String entityId) throws SwtException {

    		String query = null;
    		ArrayList<Object> paramArr = null;
    		ArrayList<Type> paramType = null;
    		// Variable Declaration for noofRecords.
    		List<Integer> noofRecords = null;
    		int totalCount = 0;
    		Session session = null;
    		boolean whereAdded = false;
    		try {
    			log.debug(this.getClass().getName() + "- [getTotalCount] - Entering");
    			
    			paramArr = new ArrayList<Object>();
    			paramType = new ArrayList<Type>();
    			if(!SwtUtil.isEmptyOrNull(entityId)) {
    				query = "select count(*) from Party c where c.id.entityId =?0  and ";
    				paramArr.add(entityId);
    				paramType.add(StringType.INSTANCE);
    				whereAdded = true;
    			}else {
    				query = "select count(*) from Party c ";
    			}

    			if ((partyId != null) && (partyId.trim().length() > 0)) {
    				if(!whereAdded) {
    					query+=" where ";
    					whereAdded = true;
    				}
    				query += "  upper(c.id.partyId) like upper(?"+paramArr.size()+" || '%')";
    				paramArr.add(partyId);
    				paramType.add(StringType.INSTANCE);
    			}
    			if ((partyName != null) && (partyName.trim().length() > 0)) {
    				if(!whereAdded) {
    					query+=" where ";
    					whereAdded = true;
    				}
    				query += " and upper(c.partyName) like upper(?"+paramArr.size()+" || '%')";
    				paramArr.add(partyName);
    				paramType.add(StringType.INSTANCE);
    			}
    			session = SwtUtil.sessionFactory.openSession();

    			noofRecords = session.createQuery(
    					query).setParameters( paramArr.toArray(),
    					paramType.toArray(new Type[paramType.size()])).getResultList();

    			if ((noofRecords.get(0) != null) && (((Integer) noofRecords.get(0)).intValue() > 0)) {
    				totalCount = (Integer) noofRecords.get(0);
    			}
    			return totalCount;
    		} catch (Exception exp) {

    			log.error(this.getClass().getName() + " - Exception Catched in [getTotalCount] method : - "
    					+ exp.getMessage());

    			throw SwtErrorHandler.getInstance().handleException(exp, "getTotalCount", this.getClass());
    		} finally {
    			JDBCCloser.close(session);
    			log.debug(this.getClass().getName() + "- [getTotalCount] - Exiting");
    		}

    	}
    	
    	
    	/**
    	 * This is used to check if current account is linked to another account
    	 * 
    	 * @param accountAccess
    	 * @return boolean
    	 * @throws SwtException
    	 */
    	public boolean checkAccountLinkedList(String accountId, String entityId) throws SwtException {
    		/* Method's local variable declaration */
    		List acctlist = null;
    		boolean acctFlag = false;
    		try {
    			log.debug(this.getClass().getName() + " - [checkAccountLinkedList] - "
    					+ "Entry");
    			/* Query to fetched role value from Role */
    			acctlist = (List ) getHibernateTemplate().find(
    							"from AcctMaintenance a where a.linkAccID=?0 and a.id.entityId=?1",
    							new Object[] { accountId, entityId});
    			if (acctlist.size() > 0){
    				acctFlag = true;
    			}
    			log.debug(this.getClass().getName() + " - [checkAccountLinkedList] - "
    					+ "Exit");
    			return acctFlag;
    		} catch (Exception exp) {
    			log.error(this.getClass().getName()
    					+ " - Exception Catched in [checkAccountLinkedList] method : - "
    					+ exp.getMessage());
    			exp.printStackTrace();
    			throw SwtErrorHandler.getInstance().handleException(exp,
    					"checkAccountLinkedList", AcctMaintenanceDAOHibernate.class);
    		}
    	}

	@Override
	public String getAccountStatus(String hostId, String entityId, String accountId) throws SwtException {
		Connection conn = null;
		PreparedStatement stmt = null;
		ResultSet rs = null;
		String result = "";

		try {
			log.debug("AcctMaintenanceDAOHibernate  - [getAccountStatus] - Enter");

			conn = ConnectionManager.getInstance().databaseCon();
			String checkAccountQuery = "select ACCOUNT_STATUS_FLAG from p_account acct where  acct.host_id=? and acct.entity_id=? and  acct.account_id=?";

			stmt = conn.prepareStatement(checkAccountQuery);
			stmt.setString(1, hostId);
			stmt.setString(2, entityId);
			stmt.setString(3, accountId);
			stmt.execute();
			/* Fetching the Result */
			rs = (ResultSet) stmt.getResultSet();
			if (rs != null) {
				rs.next();
				result= (String) rs.getString(1);
			}

			log.debug("AcctMaintenanceDAOHibernate  - [getAccountStatus] - Exit");
		} catch (SQLException ex) {
			try {
				log.error("AcctMaintenanceDAOHibernate  - [getAccountStatus]  Exception: " + ex.getMessage());
				// rollBack
				conn.rollback();
			} catch (SQLException e) {

			}

		} catch (Exception e) {
			log.error("An exception occured in " + "AcctMaintenanceDAOHibernate : getAccountStatus "
					+ e.getMessage());
			throw new SwtException(e.getMessage());

		} finally {
			// Close conn and nullify objects
			Object[] exceptions = JDBCCloser.close(rs, stmt, conn, null);
		}

		return result;
	}


}
