/*
 * @(#)CurrenyInterestDAOHibernate.java 1.0 23/08/06
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.maintenance.dao.hibernate;

import java.util.Collection;
import java.util.List;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.maintenance.dao.CurrencyInterestDAO;
import org.swallow.maintenance.model.CurrencyInterest;
import org.swallow.maintenance.model.CurrencyMaster;
import org.swallow.util.JDBCCloser;
import org.swallow.util.SwtInterceptor;
import org.swallow.util.SwtUtil;
import org.springframework.context.annotation.Lazy;
import org.springframework.orm.hibernate5.support.HibernateDaoSupport;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.stereotype.Repository;
import org.springframework.beans.factory.annotation.Autowired;
import org.hibernate.HibernateException;
import org.hibernate.SessionFactory;
import org.hibernate.Transaction;
import org.hibernate.Session;




/**
 * 
 * This is DAO class for Currency Interest screen.
 * 
 */
@Repository ("currencyInterestDAO")
@Transactional
public class CurrencyInterestDAOHibernate extends HibernateDaoSupport implements
		CurrencyInterestDAO {
	public CurrencyInterestDAOHibernate(@Lazy SessionFactory sessionfactory) {
	    setSessionFactory(sessionfactory);
	}

	/**
	 * Initializing logger object for this class
	 */
	private final Log log = LogFactory.getLog(this.getClass());

	/**
	 * This method is used to save CurrencyInterest Details in database.
	 * 
	 * @param currencyInterest
	 * @return
	 * @throws SwtException
	 */
	@SuppressWarnings("unchecked")
	public void saveCurrencyInterest(CurrencyInterest currencyInterest)
			throws SwtException {
		/* Methods local variable declaration */
		List records = null;
		Session session = null;
		Transaction tx = null;
		SwtInterceptor interceptor = null;
		try {
			log.debug(this.getClass().getName()
					+ " - [saveCurrencyInterest] - " + "Entry");

			/*
			 * Start : Modified for Mantis 1366-Remove entry to log if duplicate
			 * records added by betcy on 18-02-2011
			 */
			records = (List ) getHibernateTemplate().find(
							"from CurrencyInterest h where h.id.hostId=?0 and h.id.entityId=?1 and h.id.currencyCode=?2 and h.id.interestRateDate= ?3",
							new Object[] {
									currencyInterest.getId().getHostId(),
									currencyInterest.getId().getEntityId(),
									currencyInterest.getId().getCurrencyCode(),
									currencyInterest.getId()
											.getInterestRateDate() });
			/* Condition to check list size */
			if (records.size() == 0) {
				/*
				 * To save the values in the currency Interest bean in to the
				 * database
				 */
				interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");
				session = getHibernateTemplate()
							.getSessionFactory()
							.withOptions().interceptor(interceptor).openSession();
				tx = session.beginTransaction();
				session.save(currencyInterest);
				tx.commit();
				log.debug(this.getClass().getName()
						+ " - [saveCurrencyInterest] - " + "Exit");
			} else {
				throw new SwtException(
						"errors.DataIntegrityViolationExceptioninAdd");
			}
			/*
			 * End: Modified for Mantis 1366-Remove entry to log if duplicate
			 * records added by betcy on 18-02-2011
			 */
		} catch (Exception exp) {
			log.error(this.getClass().getName()
							+ " - Exception Catched in [saveCurrencyInterest] method : - "
							+ exp.getMessage());

			throw SwtErrorHandler.getInstance().handleException(exp,
					"saveCurrencyInterest", CurrencyInterestDAOHibernate.class);
		} finally {
			records = null;
			JDBCCloser.close(session);
		}
	}

	/**
	 * This method is used to delete CurrencyInterest Details from database.
	 * 
	 * @param currencyInterest
	 * @return
	 * @throws SwtException
	 */
	public void deleteCurrencyInterest(CurrencyInterest currencyInterest)
			throws SwtException {
		Session session = null;
		Transaction tx = null;
		SwtInterceptor interceptor = null;
		try {
			log.debug(this.getClass().getName()
					+ " - [deleteCurrencyInterest] - " + "Entry");
			/*
			 * To delete the values in the currency Interest bean in to the
			 * database
			 */
			interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");
			session = getHibernateTemplate().getSessionFactory().withOptions().interceptor(interceptor).openSession();
			tx = session.beginTransaction();
			session.delete(currencyInterest);
			tx.commit();
			log.debug(this.getClass().getName()
					+ " - [deleteCurrencyInterest] - " + "Exit");
		} catch (Exception exp) {
			log.debug(this.getClass().getName()
							+ " - Exception Catched in [deleteCurrencyInterest] method : - "
							+ exp.getMessage());

			log.error(this.getClass().getName()
							+ " - Exception Catched in [deleteCurrencyInterest] method : - "
							+ exp.getMessage());

			exp.printStackTrace();
			throw SwtErrorHandler.getInstance().handleException(exp,
					"deleteCurrencyInterest",
					CurrencyInterestDAOHibernate.class);
		}  finally {
			JDBCCloser.close(session);
		}

	}

	/**
	 * This method is used to update CurrencyInterest details in database.
	 * 
	 * @param currencyInterest
	 * @return
	 * @throws SwtException
	 */
	public void updateCurrencyInterestDetail(CurrencyInterest currencyInterest)
			throws SwtException {
		Session session = null;
		Transaction tx = null;
		SwtInterceptor interceptor = null;
		try {
			log.debug(this.getClass().getName()
					+ " - [updateCurrencyInterestDetail] - " + "Entry");
			/*
			 * To update the values in the currency Interest bean in to the
			 * database
			 */
			interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");
			session = getHibernateTemplate().getSessionFactory().withOptions().interceptor(interceptor).openSession();
			tx = session.beginTransaction();
			session.update(currencyInterest);
			tx.commit();
			log.debug(this.getClass().getName()
					+ " - [updateCurrencyInterestDetail] - " + "Exit");
		} catch (Exception exp) {
			log.debug(this.getClass().getName()
							+ " - Exception Catched in [updateCurrencyInterestDetail] method : - "
							+ exp.getMessage());

			log.error(this.getClass().getName()
							+ " - Exception Catched in [updateCurrencyInterestDetail] method : - "
							+ exp.getMessage());

			exp.printStackTrace();
			throw SwtErrorHandler.getInstance().handleException(exp,
					"updateCurrencyInterestDetail",
					CurrencyInterestDAOHibernate.class);
		}  finally {
			JDBCCloser.close(session);
		}
	}

	/**
	 * This method is used to extract CurrencyInterest object from database for
	 * given date.
	 * 
	 * @param entity
	 * @param hostId
	 * @param currencyCode
	 * @param fromDate
	 * @param toDate
	 * @param dateFormat
	 * @return Collection
	 * @throws SwtException
	 */
	public Collection<CurrencyInterest> getCurrencyInterestList(String entity,
			String hostId, String currencyCode, String fromDate, String toDate,
			String dateFormat) throws SwtException {
		try {
			log.debug(this.getClass().getName()
					+ " - [getCurrencyInterestList] - " + "Entry");
			/* Method,s local variable declaration */
			List currencyInterestList;
			/* Collect the list of currency interest details from the database */

			currencyInterestList = getHibernateTemplate()
					.find(
							"from CurrencyInterest h where h.id.entityId=?0 and h.id.currencyCode like ?1 and h.id.hostId=?2 and "
									+ "h.id.interestRateDate between to_date(?3,?4) and to_date(?5,?6) ",
							new Object[] { entity, currencyCode, hostId,
									fromDate, dateFormat, toDate, dateFormat });

			log.debug(this.getClass().getName()
					+ " - [getCurrencyInterestList] - " + "Exit");

			return currencyInterestList;
		} catch (Exception exp) {
			log.debug(this.getClass().getName()
							+ " - Exception Catched in [getCurrencyInterestList] method : - "
							+ exp.getMessage());

			log.error(this.getClass().getName()
							+ " - Exception Catched in [getCurrencyInterestList] method : - "
							+ exp.getMessage());

			exp.printStackTrace();
			throw SwtErrorHandler.getInstance().handleException(exp,
					"getCurrencyInterestList",
					CurrencyInterestDAOHibernate.class);
		}

	}

	/**
	 * This method is used to extract currency name for given code.
	 * 
	 * @param currencyCode
	 * @return string
	 * @throws SwtException
	 */
	public String getCurrencyName(String currencyCode) throws SwtException {
		try {
			log.debug(this.getClass().getName() + " - [getCurrencyName] - "
					+ "Entry");
			/* Method'slocal variable declaration */
			List list = null;
			/*
			 * Collects the list of currency name from the given currency code
			 * from the database
			 */

			list = (List ) getHibernateTemplate().find(
					"from CurrencyMaster cm where cm.currencyCode=?0 ",
					new Object[] { currencyCode });
			/* Store the list values to the currency master bean */
			CurrencyMaster currencyMaster = (CurrencyMaster) list.get(0);

			log.debug(this.getClass().getName() + " - [getCurrencyName] - "
					+ "Exit");
			return currencyMaster.getCurrencyName();
		} catch (Exception exp) {
			log.debug(this.getClass().getName()
					+ " - Exception Catched in [getCurrencyName] method : - "
					+ exp.getMessage());

			log.error(this.getClass().getName()
					+ " - Exception Catched in [getCurrencyName] method : - "
					+ exp.getMessage());

			exp.printStackTrace();
			throw SwtErrorHandler.getInstance().handleException(exp,
					"getCurrencyName", CurrencyInterestDAOHibernate.class);
		}
	}
}
