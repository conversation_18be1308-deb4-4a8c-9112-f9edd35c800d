
package org.swallow.maintenance.dao.hibernate;

import java.math.BigDecimal;
import java.sql.CallableStatement;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.LinkedHashMap;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

import org.swallow.batchScheduler.ConnectionManager;
import org.swallow.exception.SwtException;
import org.swallow.maintenance.dao.CashRsrvBalManagmntDAO;
import org.swallow.maintenance.model.AcctMaintenance;
import org.swallow.util.JDBCCloser;
import org.swallow.util.SwtUtil;
import org.swallow.work.model.CashManagementDateRecord;
import org.swallow.work.model.CashManagementVO;
import org.springframework.context.annotation.Lazy;
import org.springframework.orm.hibernate5.support.HibernateDaoSupport;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.stereotype.Repository;
import org.springframework.beans.factory.annotation.Autowired;
import org.hibernate.HibernateException;
import org.hibernate.SessionFactory;
import org.hibernate.Transaction;
import org.hibernate.Session;




@Repository ("cashRsrvBalManagmntDAO")
@Transactional
public class CashRsrvBalManagmntDAOHibernate extends HibernateDaoSupport implements CashRsrvBalManagmntDAO {
	public CashRsrvBalManagmntDAOHibernate(@Lazy SessionFactory sessionfactory) {
	    setSessionFactory(sessionfactory);
	}


	/*
	 * Initialising Log variable for logging comments
	 */
	private static final Log log = LogFactory.getLog(CashRsrvBalManagmntDAOHibernate.class);

	/**
	 * This function returns status for Manual Sweeping sweeping process while
	 * getting the data from database.
	 * 
	 * 
	 * @param sweep
	 * @param systemFormats
	 * @return status
	 */
	public CashManagementVO getCashRsvBalanceGridData(CashManagementVO cashManagementVO) throws SwtException {

		// variable declared for session
		Session session = null;
		// variable declared for Connection
		Connection conn = null;
		// variable declared for CallableStatment
		CallableStatement cstmt = null;
		ResultSet rs = null;
		CashManagementDateRecord record = null;
		ArrayList<CashManagementDateRecord> grid  = null;
		try {
			log.debug(this.getClass().getName() + " - [getCashRsvBalanceGridData] - Entry");
			/*
			 * start: Code modified by sunil for Mantis 1438 - removing the Directory path
			 * are never used
			 */
			if (!SwtUtil.isEmptyOrNull(cashManagementVO.getAccountId())) {
			session =  getHibernateTemplate().getSessionFactory().openSession();
			conn = SwtUtil.connection(session);
			// Prepared statement for the cursor
			cstmt = conn.prepareCall("{call PKG_SWEEP_RULES.SP_GET_MAINT_PERIOD_GRID(?,?,?,?,?,?,?,?,?,?,?)}");
			// Setting hostid value
			cstmt.setString(1, cashManagementVO.getHost_id());
			// Setting AccountID debit Value
			cstmt.setString(2, cashManagementVO.getEntityId());
			// Setting AccountId Credit Value
			cstmt.setString(3, cashManagementVO.getAccountId());
			// Setting Amount value
			cstmt.setInt(4, cashManagementVO.getPeriod());
			// Setting request user
			cstmt.registerOutParameter(5, oracle.jdbc.OracleTypes.DATE);
			// Setting request user
			cstmt.registerOutParameter(6, oracle.jdbc.OracleTypes.DATE);
			// Setting request user
			cstmt.registerOutParameter(7, oracle.jdbc.OracleTypes.DOUBLE);
			// Setting request user
			cstmt.registerOutParameter(8, oracle.jdbc.OracleTypes.INTEGER);
			// Setting request user
			cstmt.registerOutParameter(9, oracle.jdbc.OracleTypes.DOUBLE);
			// Setting request user
			cstmt.registerOutParameter(10, oracle.jdbc.OracleTypes.DOUBLE);
			// Setting request user
			cstmt.registerOutParameter(11, oracle.jdbc.OracleTypes.CURSOR);
			// execute the statement
			cstmt.execute();

			cashManagementVO.setStartDate(cstmt.getDate(5));
			cashManagementVO.setEndDate(cstmt.getDate(6));
			cashManagementVO.setTargetAvgBal(BigDecimal.valueOf(cstmt.getDouble(7)));
			cashManagementVO.setFillInDays(cstmt.getInt(8));
			cashManagementVO.setFillInBal(BigDecimal.valueOf(cstmt.getDouble((9))));
			if(!SwtUtil.isEmptyOrNull(cstmt.getString((10)))){
				cashManagementVO.setMinTargetBalance(BigDecimal.valueOf(cstmt.getDouble((10))));
			}
			
			
			rs = (ResultSet) cstmt.getObject(11);
//			// get the Multipler Type
//			multiplierType = (String) cstmt.getObject(10);
			grid = new ArrayList<CashManagementDateRecord>();
			Date previousDay = null;
			LinkedHashMap<String, BigDecimal> accountsData = new LinkedHashMap<String, BigDecimal>();			
			if (rs != null) {
				record = new CashManagementDateRecord(); 
				while (rs.next()) {
					if((previousDay == null) || (!rs.getDate("row_date").equals(previousDay))) {
						if(previousDay != null) {
							record.setAccountBalanceDetails(accountsData);
							grid.add(record);
						}
						record = new CashManagementDateRecord();
						accountsData = new LinkedHashMap<String, BigDecimal>();
						record.setValueDate(rs.getDate("row_date"));
						if(rs.getString("target_balance") != null) {
							record.setTargetBalance(new BigDecimal(rs.getDouble("target_balance")));
						}else {
							record.setTargetBalance(null);
						}
						
						
						if(rs.getString("avg_target_balance") != null) {
							record.setRunningAvg(new BigDecimal(rs.getDouble("avg_target_balance")));
						}else {
							record.setRunningAvg(null);
						}
						
//						record.setRunningAvg(new BigDecimal(rs.getDouble("avg_target_balance")));
						record.setValueDate(rs.getDate("row_date"));
					}else {
						if(0 == cashManagementVO.getPeriod()) {
							if(rs.getString("is_min_bal_reachable") != null) {
								record.setMinBalanceReached(rs.getString("is_min_bal_reachable"));
							}
						}
						if(rs.getString("target_balance") != null) {
							accountsData.put(rs.getString("account_id"), new BigDecimal(rs.getDouble("target_balance")));
						}else {
							accountsData.put(rs.getString("account_id"), null);
						}
						
						
					}
					previousDay = rs.getDate("row_date");
					
					
					
				}
			}
			
			
			if(record != null && record.getValueDate() != null) {
				record.setAccountBalanceDetails(accountsData);
				grid.add(record);
			}
			if(grid.size()==0) {
				record = new CashManagementDateRecord(); 
				grid.add(record);
			}
			cashManagementVO.setRefGrid(grid);
			
			}else {
				grid = new ArrayList<CashManagementDateRecord>();
				if(grid.size()==0) {
					record = new CashManagementDateRecord(); 
					grid.add(record);
				}
				cashManagementVO.setRefGrid(grid);
				cashManagementVO.setFillInDays(0);
			}
//			
//			row_date,
//			entity_id, 
//			account_id, 
//			target_balance, 
//			avg_target_balance
			
			return cashManagementVO;
			/*
			 * End : Code modified by sunil for Mantis 1438 - removing the Directory path
			 * are never used
			 */

		} catch (SQLException sqle) {
			sqle.printStackTrace();
			log.error(this.getClass().getName() + " - Exception Catched in [getCashRsvBalanceGridData] method : - "
					+ sqle.getMessage());
			throw new SwtException(sqle.getMessage());
		} catch (Exception e) {
			e.printStackTrace();
			log.error(
					this.getClass().getName() + " - Exception Catched in [getCashRsvBalanceGridData] method : - " + e.getMessage());
			throw new SwtException(e.getMessage());
		} finally {

			// edited by KaisBS for mantis 1745 : introducing the implementation of the
			// JDBCCloser class
			SwtException thrownException = null;
			Object[] exceptions = JDBCCloser.close(rs, cstmt, conn, session);

			if (exceptions[0] != null)
				thrownException = new SwtException(((SQLException) exceptions[0]).getMessage());

			if (thrownException == null && exceptions[1] != null)
				thrownException = new SwtException(((HibernateException) exceptions[1]).getMessage());

			if (thrownException != null)
				throw thrownException;

			log.debug(this.getClass().getName() + " - [getCashRsvBalanceGridData] - Exit");
		}

	}
	
	public CashManagementVO getCashRsvBalancePeriod(CashManagementVO cashManagementVO) throws SwtException {

		// variable declared for session
		Session session = null;
		// variable declared for Connection
		Connection conn = null;
		// variable declared for CallableStatment
		CallableStatement cstmt = null;
		ResultSet rs = null;
		CashManagementDateRecord record = null;
		try {
			log.debug(this.getClass().getName() + " - [getCashRsvBalancePeriod] - Entry");
	
			if (!SwtUtil.isEmptyOrNull(cashManagementVO.getAccountId())) {
				session =  getHibernateTemplate().getSessionFactory().openSession();
				conn = SwtUtil.connection(session);
				// Prepared statement for the cursor
				cstmt = conn.prepareCall("{call PKG_SWEEP_RULES.SP_GET_MAINT_PERIOD_GRID(?,?,?,?,?,?,?,?,?,?,?)}");
				// Setting hostid value
				cstmt.setString(1, cashManagementVO.getHost_id());
				// Setting EntityID debit Value
				cstmt.setString(2, cashManagementVO.getEntityId());
				// Setting AccountId Credit Value
				cstmt.setString(3, cashManagementVO.getAccountId());
				// Setting Period value
				cstmt.setInt(4, cashManagementVO.getPeriod());
				// Setting request user
				cstmt.registerOutParameter(5, oracle.jdbc.OracleTypes.DATE);
				// Setting request user
				cstmt.registerOutParameter(6, oracle.jdbc.OracleTypes.DATE);
				// Setting request user
				cstmt.registerOutParameter(7, oracle.jdbc.OracleTypes.DOUBLE);
				// Setting request user
				cstmt.registerOutParameter(8, oracle.jdbc.OracleTypes.INTEGER);
				// Setting request user
				cstmt.registerOutParameter(9, oracle.jdbc.OracleTypes.DOUBLE);
				// Setting request user
				cstmt.registerOutParameter(10, oracle.jdbc.OracleTypes.DOUBLE);
				// Setting request user
				cstmt.registerOutParameter(11, oracle.jdbc.OracleTypes.CURSOR);
				// execute the statement
				cstmt.execute();

				cashManagementVO.setStartDate(cstmt.getDate(5));
			}
			return cashManagementVO;
			
		} catch (SQLException sqle) {
			sqle.printStackTrace();
			log.error(this.getClass().getName() + " - Exception Catched in [getCashRsvBalancePeriod] method : - "
					+ sqle.getMessage());
			throw new SwtException(sqle.getMessage());
		} catch (Exception e) {
			e.printStackTrace();
			log.error(this.getClass().getName() + " - Exception Catched in [getCashRsvBalancePeriod] method : - "
					+ e.getMessage());
			throw new SwtException(e.getMessage());
		} finally {
			// JDBCCloser class
			SwtException thrownException = null;
			Object[] exceptions = JDBCCloser.close(rs, cstmt, conn, session);

			if (exceptions[0] != null)
				thrownException = new SwtException(((SQLException) exceptions[0]).getMessage());

			if (thrownException == null && exceptions[1] != null)
				thrownException = new SwtException(((HibernateException) exceptions[1]).getMessage());

			if (thrownException != null)
				throw thrownException;

			log.debug(this.getClass().getName() + " - [getCashRsvBalancePeriod] - Exit");
		}

	}
	

	public Collection getAccountIDDropDown(String hostId, String entityId, String currencyCode, String accountType) throws SwtException {

		/* Method's local variable declaration */
		Connection conn = null;
		Session session = null;
		ResultSet res = null;
		PreparedStatement stmt = null;
		ResultSet rs = null;
		boolean result = false;
		String acctQuery = null;

		Collection<AcctMaintenance> acctList = new ArrayList<AcctMaintenance>();

		log.debug(this.getClass().getName() + " - getAccountIDDropDown () - " + " Entry ");

		try {

			session = getHibernateTemplate().getSessionFactory().openSession();
			conn = SwtUtil.connection(session);
			if("C".equals(accountType)){
				 acctQuery = "select distinct (acct.account_id) , acct.account_name, acct.account_status_flag  from p_account acct where "
						+ "acct.account_id in (select distinct(account_id) from P_CCY_ACC_MAINT_PERIOD acctCcyP ) "
						+ "and acct.entity_id=? and acct.host_id=? and acct.currency_code=? and acct.account_status_flag='C'";
			}else if("O".equals(accountType)){
					 acctQuery = "select distinct (acct.account_id) , acct.account_name, acct.account_status_flag  from p_account acct where "
								+ "acct.account_id in (select distinct(account_id) from P_CCY_ACC_MAINT_PERIOD acctCcyP ) "
								+ "and acct.entity_id=? and acct.host_id=? and acct.currency_code=? and acct.account_status_flag='O'";
			}else if("B".equals(accountType)){
				 acctQuery = "select distinct (acct.account_id) , acct.account_name, acct.account_status_flag  from p_account acct where "
							+ "acct.account_id in (select distinct(account_id) from P_CCY_ACC_MAINT_PERIOD acctCcyP ) "
							+ "and acct.entity_id=? and acct.host_id=? and acct.currency_code=? and acct.account_status_flag='B'";
			}else {
				 acctQuery = "select distinct (acct.account_id) , acct.account_name, acct.account_status_flag  from p_account acct where "
						+ "acct.account_id in (select distinct(account_id) from P_CCY_ACC_MAINT_PERIOD acctCcyP ) "
						+ "and acct.entity_id=? and acct.host_id=? and acct.currency_code=?";	
			}
			stmt = conn.prepareStatement(acctQuery);
			stmt.setString(1, entityId);
			stmt.setString(2, hostId);
			stmt.setString(3, currencyCode);
			stmt.execute();

			/* Fetching the Result */
			rs = (ResultSet) stmt.getResultSet();
			if (rs != null) {
				while (rs.next()) {
					AcctMaintenance acct = new AcctMaintenance();
					acct.getId().setAccountId(rs.getString("account_id"));
					acct.setAcctname(rs.getString("account_name"));
					acctList.add(acct);
				}

			}
		} catch (Exception e) {
			log.debug(this.getClass().getName() + " - getAccountIDDropDown () - " + " Exception - " + e.getMessage());
			log.error(this.getClass().getName() + " - getAccountIDDropDown () - " + " Exception - " + e.getMessage());
		} finally {

			// edited by KaisBS for mantis 1745 : introducing the implementation of the
			// JDBCCloser class
			Object[] exceptions = JDBCCloser.close(rs, stmt, conn, session);

			if (exceptions[1] != null)
				throw new SwtException(((HibernateException) exceptions[1]).getMessage());

		}

		log.debug(this.getClass().getName() + " - getAccountIDDropDown () - " + " Exit");
		return acctList;
	}

}
