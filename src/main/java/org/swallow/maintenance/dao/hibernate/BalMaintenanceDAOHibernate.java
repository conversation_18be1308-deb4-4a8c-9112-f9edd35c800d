/*
 * @(#)BalMaintenanceDAOHibernate.java 1.0 23/08/06
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.maintenance.dao.hibernate;

import java.math.BigDecimal;
import java.sql.CallableStatement;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.Iterator;
import java.util.List;





import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.maintenance.dao.BalMaintenanceDAO;
import org.swallow.maintenance.model.BalMaintenance;
import org.swallow.maintenance.model.BalType;
import org.swallow.util.JDBCCloser;
import org.swallow.util.SwtConstants;
import org.swallow.util.SwtInterceptor;
import org.swallow.util.SwtUtil;
import org.springframework.context.annotation.Lazy;
import org.springframework.orm.hibernate5.support.HibernateDaoSupport;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.stereotype.Repository;
import org.springframework.beans.factory.annotation.Autowired;
import org.hibernate.HibernateException;
import org.hibernate.SessionFactory;
import org.hibernate.Transaction;
import org.hibernate.Session;

/**
 * BalMaintenanceDAOHibernate.java
 * 
 * Class that implements the BalMaintenanceDAO and acts as DAO layer for all
 * database operations related to Balance Maintenance<br>
 * This interface has methods that are used for accessing the persistent storage
 * such as database <br>
 * which helps client to create, retrieve and persists data to the Persistent
 * Object.<br>
 * 
 */
@Repository ("balMaintDAO")
@Transactional
public class BalMaintenanceDAOHibernate extends HibernateDaoSupport implements
		BalMaintenanceDAO {
	public BalMaintenanceDAOHibernate(@Lazy SessionFactory sessionfactory) {
	    setSessionFactory(sessionfactory);
	}

	/**
	 * Initializing logger object for this class
	 */
	private final Log log = LogFactory.getLog(BalMaintenanceDAOHibernate.class);

	/**
	 * Returns the collection of balance list from the Table P_BALANCE
	 * 
	 * @param hostId
	 * @param entityId
	 * @return Collection
	 * @throws SwtException
	 */
	public Collection getBalanceList(String hostId, String entityId)
			throws SwtException {
		try {
			log.debug(this.getClass().getName() + " - [getBalanceList] - "
					+ "Entry");
			/* Methods local variable declaration */
			List noofRecords = null;
			/* Collects the list of balance list from the table */
			noofRecords = (List ) getHibernateTemplate().find(
							"from BalMaintenance bal where  bal.id.hostId=?0 and bal.id.entityId=?1",
							new Object[] { hostId, entityId });

			/* Condition to check the number of records is equal to one */
			if (noofRecords.size() == 1) {
				// Commented by Selva to avoid unnecessary postflush calls on
				// 04-May
				// getHibernateTemplate().flush();

				return noofRecords;
			} else {
				// Commented by Selva to avoid unnecessary postflush calls on
				// 04-May
				// getHibernateTemplate().flush();
				log.debug(this.getClass().getName() + " - [getBalanceList] - "
						+ "Exit");
				return noofRecords;
			}

		} catch (Exception exp) {
			log.debug(this.getClass().getName()
					+ " - Exception Catched in [getBalanceList] method : - "
					+ exp.getMessage());

			log.error(this.getClass().getName()
					+ " - Exception Catched in [getBalanceList] method : - "
					+ exp.getMessage());
			exp.printStackTrace();
			throw SwtErrorHandler.getInstance().handleException(exp,
					"getBalanceList", BalMaintenanceDAOHibernate.class);
		}
	}

	/**
	 * Update the changed balance to P_BALANCE table.
	 * 
	 * @param balmaintenance
	 * @param startBalance
	 * @param hostId
	 * @param entityId
	 * @param balanceType
	 * @param replacebalanceDate
	 * @return
	 * @throws SwtException
	 */

	/*
	 * Start:Parameter modified by Arumugam for Mantis 997:Allow Internal and
	 * External forecasts to have independent SOD's
	 */
	public void updateBalanceDetail(BalMaintenance balmaintenance,
			String sodBalance, String hostId, String entityId,
			String balanceType, Date replacebalanceDate) throws SwtException {
		/*
		 * End:Parameter modified by Arumugam for Mantis 997:Allow Internal and
		 * External forecasts to have independent SOD's
		 */
		Session session = null;
		Transaction tx = null;
		SwtInterceptor interceptor = null;
		try {
			log.debug(this.getClass().getName() + " - [updateBalanceDetail] - "
					+ "Entry");

			/*
			 * Start:Variable modified by Arumugam for Mantis 997:Allow Internal
			 * and External forecasts to have independent SOD's
			 */
			/*
			 * Condition to check start balance is 'Y', result on the condition
			 * it saves or updates the balMaintenance bean
			 */
			interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");
			if (sodBalance.equalsIgnoreCase("Y")) {
				/*
				 * End:Variable modified by Arumugam for Mantis 997:Allow
				 * Internal and External forecasts to have independent SOD's
				 */
				session = getHibernateTemplate()
						.getSessionFactory()
						.withOptions().interceptor(interceptor).openSession();
				tx = session.beginTransaction();
				session.save(balmaintenance);
				tx.commit();
				session.close();
			
			} else {
				session = getHibernateTemplate().getSessionFactory().withOptions().interceptor(interceptor).openSession();
				tx = session.beginTransaction();
				session.update(balmaintenance);
				tx.commit();
				session.close();
			}

			log.debug(this.getClass().getName() + " - [updateBalanceDetail] - "
					+ "Exit");
		} catch (Exception exp) {
			log
					.debug(this.getClass().getName()
							+ " - Exception Catched in [updateBalanceDetail] method : - "
							+ exp.getMessage());

			log
					.error(this.getClass().getName()
							+ " - Exception Catched in [updateBalanceDetail] method : - "
							+ exp.getMessage());
			exp.printStackTrace();
			throw SwtErrorHandler.getInstance().handleException(exp,
					"updateBalanceDetail", BalMaintenanceDAOHibernate.class);
		}

	}

	/**
	 * Returns the list of balmaintenance from the table P_BALANCE to the
	 * BalmaintenanceManagerImpl
	 * 
	 * @param hostId
	 * @param entityId
	 * @param balanceTypeId
	 * @param balType
	 * @param replaceDate
	 * @return BalMaintenance
	 * @throws SwtException
	 */
	public BalMaintenance getEditableData(String hostId, String entityId,
			String balanceTypeId, String balType, Date replaceDate)
			throws SwtException {
		try {
			log.debug(this.getClass().getName() + " - [getEditableData] - "
					+ "Entry");
			/* Method's local variable and class instance declarations */
			BalMaintenance balMaint = null;
			List balMaintList = null;
			Iterator itr;
			/* Select the editable data from the database. */
			/**
			 * <pre>
			 * START: Added and Commented by RK on 04-Dec-2009
			 * Mantis Issue-1083 : Amend primary key defintion on P_balance. The balance
			 * type is not necessary in the index and it presence is causing the index
			 * not to be used, or poor range selections to be employed.
			 * </pre>
			 */
			balMaintList = (List ) getHibernateTemplate().find(
							"from BalMaintenance bal where  bal.id.hostId=?0 and bal.id.entityId=?1 and bal.id.balanceTypeId=?2 and bal.balanceType=?3 and bal.id.balanceDate=?4",
							new Object[] { hostId, entityId, balanceTypeId,
									balType, replaceDate });
			// balMaintList = (List ) getHibernateTemplate().find("from BalMaintenance
			// bal where bal.id.hostId=? and bal.id.entityId=? and
			// bal.id.balanceTypeId=? and bal.id.balanceType=? and
			// bal.id.balanceDate=?",
			// new Object[] {
			// hostId, entityId, balanceTypeId, balType, replaceDate
			// });
			/** END: Added and Commented by RK on 04-Dec-2009 Mantis Issue-1083 */

			itr = balMaintList.iterator();
			/*
			 * Loop to iterate the collection from table and store the values in
			 * the bean
			 */
			while (itr.hasNext()) {
				balMaint = (BalMaintenance) itr.next();
			}

			log.debug(this.getClass().getName() + " - [getEditableData] - "
					+ "Exit");
			return balMaint;
		} catch (Exception exp) {
			log.debug(this.getClass().getName()
					+ " - Exception Catched in [getEditableData] method : - "
					+ exp.getMessage());

			log.error(this.getClass().getName()
					+ " - Exception Catched in [getEditableData] method : - "
					+ exp.getMessage());
			exp.printStackTrace();
			throw SwtErrorHandler.getInstance().handleException(exp,
					"getEditableData", BalMaintenanceDAOHibernate.class);
		}
	}

	// Start: Code modified by Bala for Mantis 1406 Currency Group Access on
	// 05-Apr-2011
	/**
	 * Passes the value to the stored procedure sp_balance_maintenance and
	 * returns the collection of Balance details
	 * 
	 * @param hostId
	 * @param entityId
	 * @param balanceType
	 * @param currency
	 * @param selectedDate
	 * @param startRowNumber
	 * @param endRowNumber
	 * @param filterValue
	 * @param roleId
	 * @return Collection
	 * @throws SwtException
	 * 
	 */
	public Collection<BalType> accountDetailUsingStoredProc(String hostId,
			String entityId, String balanceType, String currency,
			Date selectedDate, int startRowNumber, int endRowNumber,
			String filterValue, String roleId) throws SwtException {

		// List to hold the balance details
		ArrayList<BalType> acctBalance = null;
		// Connection object
		Connection conn = null;
		// Session object
		Session session = null;
		// CallableStatement object
		CallableStatement cstmt = null;
		// ResultSet object
		ResultSet rs = null;
		// row count
		int rowCount = 0;
		// variable to hold the forecast SOD Type
		String forecastSODType = null;
		// variable to hold the user
		String user = null;
		// input date
		Date inputDate = null;
		// BalType object
		BalType balType = null;
		// variable forecastSOD
		BigDecimal forecastSOD = null;
		// variable to hold the account id
		String acctId = null;
		// variable to hold the account name
		String acctName = null;
		// variable to hold the currency code
		String acctCurrencyCode = null;
		// variable to hold the reason code
		String reasonCode = null;
		// variable to hold the reason description
		String reasonDesc = null;
		// variable to hold the user notes
		String userNotes = null;
		// variable externalWorkingSOD
		BigDecimal externalWorkingSOD = null;
		// variable to hold the external SOD Type
		String externalSODType = null;

		try {
			log.debug(this.getClass().getName()
					+ "- [accountDetailUsingStoredProc] - " + "Entry");
			// create instance for ArrayList
			acctBalance = new ArrayList<BalType>();
			/* Session collect from the session factory */
			session = getHibernateTemplate().getSessionFactory().openSession();
			/* Connection to database through session */
			conn = SwtUtil.connection(session);
			/* Assigning the query for a callable statement */
			cstmt = conn
					.prepareCall("{call sp_balance_maintenance(?,?,?,?,?,?,?,?,?,?,?)}");
			/* Set the host id to the callableStatement */
			cstmt.setString(1, hostId);
			/* Set the entity id to the callableStatement */
			cstmt.setString(2, entityId);
			/* Set the currency to the callableStatement */
			cstmt.setString(3, currency);
			/* Set the balanceType to the callableStatement */
			cstmt.setString(4, balanceType);
			/* Set the Date to the callableStatement */
			cstmt.setDate(5, SwtUtil.truncateDateTime(selectedDate));
			/* Set the startRowNumber to the callableStatement */
			cstmt.setInt(6, startRowNumber);
			/* Set the endRowNumber to the callableStatement */
			cstmt.setInt(7, endRowNumber);
			/* Set the filterValue to the callableStatement */
			cstmt.setString(8, filterValue);
			/* Register out the parameter for oracle cursor */
			cstmt.registerOutParameter(9, oracle.jdbc.OracleTypes.CURSOR);
			/* Register out the parameter for oracle Integer */
			cstmt.registerOutParameter(10, oracle.jdbc.OracleTypes.INTEGER);
			/* Set the Role id to the callableStatement */
			cstmt.setString(11, roleId);
			/* Execute the callableStatement */
			cstmt.execute();
			// Retrieves the ResultSet value
			rs = (ResultSet) cstmt.getObject(9);
			/*
			 * Retrieves the integer value of row count from the callable get
			 * int
			 */
			rowCount = cstmt.getInt(10);
			/* Condition to check result set is not null */
			if (rs != null) {
				/* Loop until the last record in result set is found */
				while (rs.next()) {
					// create instance for BalType
					balType = new BalType();
					/* Retrieves the account id from the result set */
					acctId = rs.getString(1);
					/* Retrieves the account name from the result set */
					acctName = rs.getString(2);
					/* Retrieves the currency code from the result set */
					acctCurrencyCode = rs.getString(3);
					// set the forecast SOD
					if (rs.getString(4) == null) {
						forecastSOD = null;
					} else {
						forecastSOD = new BigDecimal(rs.getString(4));
					}
					/* Condition to check balance source is null */
					if (rs.getString(5) == null) {
						/* Assign the empty value for balance source */
						forecastSODType = " ";
					} else {
						/* Assign the value of balance source from result set */
						forecastSODType = rs.getString(5);
					}
					// set the externalWorkingSOD
					if (rs.getString(6) != null) {
						externalWorkingSOD = new BigDecimal(rs.getString(6));
					} else {
						externalWorkingSOD = null;
					}
					// set the externalWorkingSOD to balType
					balType.setExternalSOD(externalWorkingSOD);
					if (rs.getString(7) == null) {
						/* Assign the empty value for balance source */
						externalSODType = " ";
					} else {
						/* Assign the value of balance source from result set */
						externalSODType = rs.getString(7);
						balType.setExternalSODType(externalSODType);
					}
					// set the reasonCode
					if (rs.getString(8) == null) {
						reasonCode = "";
					} else {
						reasonCode = rs.getString(8);
					}
					// set the reason description
					if (rs.getString(9) == null) {
						reasonDesc = "";
					} else {
						reasonDesc = rs.getString(9);
					}
					// set the reason user notes
					if (rs.getString(10) == null) {
						userNotes = "";
					} else {
						userNotes = rs.getString(10);
					}
					// set the user
					if (rs.getString(11) == null) {
						user = " ";
					} else {
						/* Assign the value of user id from result set */
						user = rs.getString(11);
					}
					// set the input date
					inputDate = (Date) rs.getObject(12);
					/* Set the account id as identifier in balance type bean */
					balType.setAccountId(acctId);
					/* Set the account name as Name in balance type bean */
					balType.setName(acctName);
					/*
					 * Set the acctCurrencyCode as BalCurrencyCode in balance
					 * type bean
					 */
					balType.setBalCurrencyCode(acctCurrencyCode);
					/* Set the user as Userid balance type bean */
					balType.setUser(user);
					// set the forecastSOD to balType
					balType.setForecastSOD(forecastSOD);
					// set the Forecast SOD Type As String
					if (!forecastSODType.equals("") && forecastSODType != null) {
						if (forecastSODType.equals("M"))
							balType
									.setForecastSODTypeAsString(SwtConstants.BALTYPE_MANUAL);
						else if (forecastSODType.equals("Z"))
							balType
									.setForecastSODTypeAsString(SwtConstants.BALTYPE_ZERO);
						else if (forecastSODType.equals("I"))
							balType
									.setForecastSODTypeAsString(SwtConstants.BALTYPE_INTERNAL);
						else if (forecastSODType.equals("E"))
							balType
									.setForecastSODTypeAsString(SwtConstants.BALTYPE_EXTERNAL);
						else if (forecastSODType.equals("P"))
							balType
									.setForecastSODTypeAsString(SwtConstants.BALTYPE_PREDICTED);
						else
							balType
									.setForecastSODTypeAsString(SwtConstants.EMPTY_STRING);
					}
					// set the External SOD Type As String
					if (!externalSODType.equals("") && forecastSODType != null) {
						if (externalSODType.equals("M"))
							balType
									.setExternalSODTypeAsString(SwtConstants.BALTYPE_MANUAL);
						else if (externalSODType.equals("Z"))
							balType
									.setExternalSODTypeAsString(SwtConstants.BALTYPE_ZERO);
						else if (externalSODType.equals("I"))
							balType
									.setExternalSODTypeAsString(SwtConstants.BALTYPE_INTERNAL);
						else if (externalSODType.equals("E"))
							balType
									.setExternalSODTypeAsString(SwtConstants.BALTYPE_EXTERNAL);
						else if (externalSODType.equals("P"))
							balType
									.setExternalSODTypeAsString(SwtConstants.BALTYPE_PREDICTED);
						else
							balType
									.setExternalSODTypeAsString(SwtConstants.EMPTY_STRING);
					}
					// set the inputDate
					balType.setInputDate(inputDate);
					/* Set rowCount as rowCount in baltype bean */
					balType.setRowCount(rowCount);
					// set the External SOD As String
					if (balType.getExternalSOD() != null) {
						balType.setExternalSODAsString(SwtUtil.formatCurrency(
								balType.getBalCurrencyCode(), balType
										.getExternalSOD()));
					}
					// set the values to balType object
					balType.setReasonCode(reasonCode);
					balType.setReasonDesc(reasonDesc);
					balType.setUserNotes(userNotes);
					if (!balType.getAccountId().equals("*")
							&& !balType.getName().equals("DEFAULT")) {
						/* Add bal type bean to acctBalance List */
						acctBalance.add(balType);
					}
					balType.setScenarioHighlighted(rs.getString("SCENARIO_HIGHLIGHTING"));
				}
			}
		} catch (HibernateException hibernateException) {
			log
					.error(this.getClass().getName()
							+ " - Exception Catched in [accountDetailUsingStoredProc] method : - "
							+ hibernateException.getMessage());
			throw new SwtException(hibernateException.getMessage());
		} catch (SQLException sqlException) {
			log
					.error(this.getClass().getName()
							+ " - Exception Catched in [accountDetailUsingStoredProc] method : - "
							+ sqlException.getMessage());
			throw new SwtException(sqlException.getMessage());
		} finally {
			
			//edited by KaisBS for mantis 1745 : introducing the implementation of the JDBCCloser class
			SwtException thrownException = null;
			Object[] exceptions = JDBCCloser.close(rs, cstmt, conn, session);

			if (exceptions[0] != null)
			    thrownException = SwtErrorHandler.getInstance().handleException((SQLException) exceptions[0], "accountDetailUsingStoredProc",BalMaintenanceDAOHibernate.class);

			if (thrownException == null && exceptions[1] !=null)
			    thrownException = SwtErrorHandler.getInstance().handleException((HibernateException) exceptions[1], "accountDetailUsingStoredProc",BalMaintenanceDAOHibernate.class);

			if(thrownException!=null)
			throw thrownException;
			      
		}
		log.debug(this.getClass().getName()
				+ " - [accountDetailUsingStoredProc] - " + "Exit");
		return acctBalance;
	}
	// End: Code modified by Bala for Mantis 1406 Currency Group Access on
	// 05-Apr-2011
}
