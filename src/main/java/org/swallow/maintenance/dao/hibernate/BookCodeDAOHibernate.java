/*
 * @(#)BookCodeDAOHibernate.java 1.0 21/12/05
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */

package org.swallow.maintenance.dao.hibernate;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.Collection;
import java.util.Iterator;
import java.util.List;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.hibernate.SessionFactory;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;
import org.swallow.batchScheduler.ConnectionManager;
import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.exception.SwtRecordNotExist;
import org.swallow.maintenance.dao.BookCodeDAO;
import org.swallow.maintenance.model.BookCode;
import org.swallow.util.JDBCCloser;
import org.swallow.util.SwtInterceptor;
import org.swallow.util.SwtUtil;
import org.hibernate.Hibernate;
import org.hibernate.HibernateException;
import org.hibernate.Session;
import org.hibernate.Transaction;
import org.hibernate.query.Query;
import org.hibernate.type.StringType;
import org.hibernate.type.Type;
import org.springframework.context.annotation.Lazy;
import org.springframework.orm.hibernate5.support.HibernateDaoSupport;

/**
 * 
 * This is DAO class for Book screen.
 * 
 */
@Repository ("bookCodeDAO")
@Transactional
public class BookCodeDAOHibernate extends HibernateDaoSupport implements
		BookCodeDAO {
	/**
	 * Query for fetching the Book code details based on Hostid,Entityid and
	 * group level 1 or 2 or 3
	 */
	private final static String HQL_BOOKCODEDETAIL = "from BookCode c where c.id.entityId =?0 "
			+ "and c.id.hostId =?1 and (c.groupIdLevel1 =?2 OR c.groupIdLevel2=?3 OR c.groupIdLevel3=?4 )";

	/**
	 * Query for fetching the Book code details based on Hostid and Entityid
	 */
	private static final String HQL_ALLBOOKCODEDETAIL = "from BookCode c where c.id.hostId =?0 "
			+ "and c.id.entityId =?1";

	/**
	 * Query for fetching the Group code details based on Hostid and Entityid
	 * and group level id
	 */
	private static final String HQL_GROUPLEVEL1DETAIL = "from Group c where c.id.hostId =?0 "
			+ "and c.id.entityId =?1 and c.groupLvlCode =?2 order by c.id.groupId";

	
	/**
	 * Query for fetching the Group code details based on Hostid and Entityid
	 * and group level id
	 */
	private static final String HQL_BOOKSLOCATIONID = "from  BookCode c where c.bookLocation =?0 ";

	/**
	 * Final log instance for logging this class
	 */
	private final Log log = LogFactory.getLog(BookCodeDAOHibernate.class);

	public BookCodeDAOHibernate(@Lazy SessionFactory sessionfactory){
	    setSessionFactory(sessionfactory);
	}
	/**
	 * This is used to get the Book code details from database and is used in
	 * Group and Metagroup action
	 * 
	 * @param hostId
	 * @param entityId
	 * @param groupId
	 * @return Collection
	 */
	public Collection getBookCodeDetail(String hostId, String entityId,
			String groupId) {
		log.debug(this.getClass().getName()
				+ "- [getBookCodeDetail] - Entering");
		/* Retrieve Book code details from DB */
		List bookCodeCollection = getHibernateTemplate().find(
				HQL_BOOKCODEDETAIL,
				new Object[] { entityId, hostId, groupId, groupId, groupId });
		log.debug(this.getClass().getName()
						+ "- [getBookCodeDetail] - Exiting");
		return bookCodeCollection;
	}

	/**
	 * This is used to get the book code details from P_BOOKCODE table
	 * 
	 * @param hostId
	 * @param entityId
	 * @return collection
	 */
	public Collection getAllBookCodeDetail(String hostId, String entityId) {
		log.debug(this.getClass().getName()
				+ "- [getAllBookCodeDetail] - Entering");
		/* Fetch the book code details from DB */
		List bookCodeCollection = getHibernateTemplate().find(
				HQL_ALLBOOKCODEDETAIL, new Object[] { hostId, entityId });
		log.debug(this.getClass().getName()
				+ "- [getAllBookCodeDetail] - Exiting");
		return bookCodeCollection;
	}

	/**
	 * This is used to get the group details from P_GROUP table.
	 * 
	 * @param hostId
	 * @param entityId
	 * @param groupLevel
	 * @return Collection
	 */
	public Collection getGroupLevelDetails(String hostId, String entityId,
			Integer groupLevel) {
		/* Fetch the group levels from DB */
		List GroupLevelCollection = getHibernateTemplate().find(
				HQL_GROUPLEVEL1DETAIL,
				new Object[] { hostId, entityId, groupLevel });
		log.debug(this.getClass().getName()
				+ "- [getGroupLevelDetails] - Exiting");
		return GroupLevelCollection;
	}

	/**
	 * This is used to save the bookcode details in P_BOOKCODE table.
	 * 
	 * @param bookcode
	 * @return none
	 * @throws SwtException
	 */
	@SuppressWarnings("unchecked")
	public void saveBookCodeDetail(BookCode bookcode) throws SwtException {
		/*Methods local variable declaration*/
		List records=null;
		Session session = null;
		try {
			session = getHibernateTemplate().getSessionFactory().openSession();
	        Transaction tx = session.beginTransaction();
			/*
			 * Start : Modified for Mantis 1366-Remove entry to log if duplicate
			 * records added by krishna on 18-02-2011
			 */
			 records = getHibernateTemplate().find(
					"from BookCode c where c.id.hostId =?0 "
							+ "and c.id.entityId =?1 and c.id.bookCode =?2",
					new Object[] { bookcode.getId().getHostId(),
							bookcode.getId().getEntityId(),
							bookcode.getId().getBookCode() });

			if (records.size() == 0) {
				log.debug(this.getClass().getName()
						+ "- [saveBookCodeDetail] - Entering");
				/* Used to save the newly added details in DB */
				session.save(bookcode);
				tx.commit();
			    
				log.debug(this.getClass().getName()
						+ "- [saveBookCodeDetail] - Exiting");
			} else {
				throw new SwtException(
						"errors.DataIntegrityViolationExceptioninAdd");
			}
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					+ "- [saveBookCodeDetail] - Exception " + exp.getMessage());
			SwtErrorHandler swtErrorHandler = SwtErrorHandler.getInstance();
			SwtException swtExp = swtErrorHandler.handleException(exp,
					"saveBookCodeDetail", BookCodeDAOHibernate.class);
			throw swtExp;
		}finally
		{
			JDBCCloser.close(session);
			records=null;
		}
		/*
		 * End : Modified for Mantis 1366-Remove entry to log if duplicate
		 * records added by krishna on 18-02-2011
		 */
	}

	/**
	 * This is used to save the updated book details in P_BOOKCODE table.
	 * 
	 * @param bookcode
	 * @return
	 * @throws SwtException
	 */
	public void updateBookCodeDetail(BookCode bookcode) throws SwtException {
	    log.debug(this.getClass().getName() + "- [updateBookCodeDetail] - Entering");
	    
	        Session session = getHibernateTemplate().getSessionFactory().openSession();
	        Transaction tx = session.beginTransaction();
	        session.update(bookcode);
	        tx.commit();
	        session.close();
	    
	    log.debug(this.getClass().getName() + "- [updateBookCodeDetail] - Exiting");
	}

	/**
	 * This is used to delete the book details from P_BOOKCODE table.
	 * 
	 * @param bookcode
	 * @return
	 * @throws SwtException
	 */
	public void deleteBookCodeDetail(BookCode bookcode) throws SwtException {
		log.debug(this.getClass().getName()
				+ "- [deleteBookCodeDetail] - Entering");
		/* Method's local variable declaration */
		Session session = null;
		Transaction tx = null;
		SwtInterceptor interceptor = null;
		String deleteHQL;
		int deleteCounter = 0;
		
		deleteHQL = "DELETE FROM BookCode m " +
		        "WHERE m.id.hostId = :hostId " +
		        "AND m.id.entityId = :entityId " +
		        "AND m.id.bookCode = :bookCode";
		try {
		    log.debug(this.getClass().getName() + "- [deleteBookCode] - Before Delete");
		    interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");
		    session = getHibernateTemplate().getSessionFactory().withOptions().interceptor(interceptor).openSession();
		    tx = session.beginTransaction();

		    deleteCounter = session.createQuery(deleteHQL)
		            .setParameter("hostId", bookcode.getId().getHostId())
		            .setParameter("entityId", bookcode.getId().getEntityId())
		            .setParameter("bookCode", bookcode.getId().getBookCode())
		            .executeUpdate();

		    tx.commit();
		} catch (Exception e) {
		    if (tx != null) {
		        tx.rollback();
		    }
			throw SwtErrorHandler.getInstance().handleException(e, "deleteBookCode", BookCodeDAOHibernate.class);

			// Handle the exception appropriately
		} finally {
		    if (session != null) {
		        session.close();
		    }
		}

		if (deleteCounter == 0) {
		    throw new SwtRecordNotExist();
		}
	}
	/**
	 * This is used to edit the required fields in P_BOOKCODE table.
	 * 
	 * @param hostId
	 * @param entityId
	 * @param groupId
	 * @return BookCode
	 * @throws SwtException
	 */
	public BookCode getEditableData(String hostId, String entityId,
			String groupId) throws SwtException {
		log.debug(this.getClass().getName()
				+ "- [deleteBookCodeDetail] - Entering");
		/* Class instance declaration */
		BookCode group = null;
		/* Editable fields are fetches from DB */
		List balMaintList = getHibernateTemplate()
				.find(
						"from BookCode p "
								+ "where p.id.hostId=?0 and p.id.entityId=?1 and p.id.bookCode=?2",
						new Object[] { hostId, entityId, groupId });
		Iterator itr = balMaintList.iterator();
		while (itr.hasNext()) {
			group = (BookCode) itr.next();
		}
		log.debug(this.getClass().getName()
				+ "- [deleteBookCodeDetail] - Exiting");
		return group;
	}

	/**
	 * This is used to get the book code locations from P_LOCATION table.
	 * 
	 * @param hostId
	 * @param entityId
	 * @return Collection
	 * @throws SwtException
	 */
	public Collection getBookLocations(String hostId, String entityId) {
		log.debug(this.getClass().getName()
				+ " - [getBookLocations] - Entering");
		List BookLocations = getHibernateTemplate().find(
				"from Location loc "
						+ "where loc.id.hostId =?0 and loc.id.entityId = ?1 "
						+ "order by loc.id.locationId",
				new Object[] { hostId, entityId });
		log.debug(this.getClass().getName() + "- [getBookLocations] - Exiting");
		return BookLocations;
	}
	
	
	/**
	 * This is used to get the book code locations from P_LOCATION table.
	 * 
	 * @param locationId
	 * @return Collection
	 * @throws SwtException
	 */
	public Collection getBooksLocationId(String locationId) {
		log.debug(this.getClass().getName()
				+ " - [getBooksLocationId] - Entering");
		List BookLocations = getHibernateTemplate().find(
				HQL_BOOKSLOCATIONID, new Object[] {locationId});
		log.debug(this.getClass().getName() + "- [getBooksLocationId] - Exiting");
		return BookLocations;
	}
	
	
	/**
	 * This is used to check if book code exists for the relevant entity
	 * 
	 * @param entityId
	 * @throws SwtException
	 */
	
	public boolean checkBookCodeAndEntity(String hostId, String entityId, String bookCode) throws SwtException {
		Connection conn = null;
		ResultSet res = null;
		PreparedStatement stmt = null;
		ResultSet rs = null;
		boolean result = false;

		try {
			log.debug("BookCodeDAOHibernate  - [checkBookCodeAndEntity] - Enter");

			conn = ConnectionManager.getInstance().databaseCon();
			String checkAccountQuery = "select 1 from p_BookCode book where  book.host_id=? and book.entity_id=? and book.bookcode=?";

			stmt = conn.prepareStatement(checkAccountQuery);
			stmt.setString(1, hostId);
			stmt.setString(2, entityId);
			stmt.setString(3, bookCode);
			stmt.execute();
			/* Fetching the Result */
			rs = (ResultSet) stmt.getResultSet();
			if (rs != null) {
				if (rs.next() == false) {
					result = false;
				} else {
					result = true;
				}

			}

			log.debug("BookCodeDAOHibernate  - [checkBookCodeAndEntity] - Exit");
		} catch (SQLException ex) {
			try {
				log.error(
						"BookCodeDAOHibernate  - [checkBookCodeAndEntity]  Exception: " + ex.getMessage());
				// rollBack
				conn.rollback();
			} catch (SQLException e) {

			}

		} catch (Exception e) {
			log.error("An exception occured in " + "BookCodeDAOHibernate : checkBookCodeAndEntity "
					+ e.getMessage());
			throw new SwtException(e.getMessage());

		} finally {
			// Close conn and nullify objects
			Object[] exceptions = JDBCCloser.close(res, stmt, conn, null);
		}

		return result;
	}
	
	
	
	/**
	 * This method is used to check if book code exists in P_BOOKCODE
	 */

	public boolean checkIfBookCodeExists(String hostId, String bookCode)
			throws SwtException {
		Connection conn = null;
		ResultSet res = null;
		PreparedStatement stmt = null;
		ResultSet rs = null;
		boolean result = false;

		try {
			log.debug("BookCodeDAOHibernate  - [checkIfBookCodeExists] - Enter");

			conn = ConnectionManager.getInstance().databaseCon();
			String checkEntityQuery =  "select 1 from p_bookcode bc where bc.host_id=? and bc.bookcode=?";

			stmt = conn.prepareStatement(checkEntityQuery);
			stmt.setString(1, hostId);
			stmt.setString(2, bookCode);
			stmt.execute();
			/* Fetching the Result */
			rs = (ResultSet) stmt.getResultSet();
			if (rs != null) {
				if(rs.next() == false) {
					result=false;
				}else {
					result=true;
				}
					
			}

			log.debug("BookCodeDAOHibernate  - [checkIfBookCodeExists] - Exit");
		} catch (SQLException ex) {
			try {
				log.error("BookCodeDAOHibernate  - [checkIfBookCodeExists]  Exception: " + ex.getMessage());
				// rollBack
				conn.rollback();
			} catch (SQLException e) {

			}

		} catch (Exception e) {
			log.error("An exception occured in " + "BookCodeDAOHibernate : checkIfBookCodeExists "
					+ e.getMessage());
			throw new SwtException(e.getMessage());

		} finally {
			// Close conn and nullify objects
			Object[] exceptions = JDBCCloser.close(res, stmt, conn, null);
		}

		return result;
	}
	
	
}