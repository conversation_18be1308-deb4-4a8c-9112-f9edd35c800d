/*
 * Created on Nov 4, 2005
 *
 * TODO To change the template for this generated file go to
 * Window - Preferences - Java - Code Style - Code Templates
 */
package org.swallow.maintenance.dao.hibernate;

import java.util.Collection;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.hibernate.SessionFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.orm.hibernate5.support.HibernateDaoSupport;
import org.springframework.stereotype.Repository;
import org.swallow.maintenance.dao.CurrencyMasterDAO;

/**
 * <AUTHOR>
 *
 * TODO To change the template for this generated type comment go to
 * Window - Preferences - Java - Code Style - Code Templates
 */
@Repository ("currencyMasterDAO")
public class CurrencyMasterDAOHibernate extends HibernateDaoSupport implements CurrencyMasterDAO {

	private final Log log = LogFactory.getLog(CurrencyMasterDAOHibernate.class);
	
	public CurrencyMasterDAOHibernate(@Lazy SessionFactory sessionfactory){
	    setSessionFactory(sessionfactory);
	}
	
	public Collection getCurrenciesFromMst(){

		  log.debug("entering 'getCurrenciesFromMst' method ");
		   
		  java.util.List currencyList = getHibernateTemplate().find(" from CurrencyMaster c where c.currencyCode != '*' order by c.currencyCode");

         log.debug("Total Number of currencies : " + currencyList.size());
		 log.debug("exiting 'getCurrenciesFromMst' method ");
		 return currencyList;
        }


	}

