/*
 * (c) MessageFormatsDAOHibernate.java 
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */

package org.swallow.maintenance.dao.hibernate;

import java.sql.CallableStatement;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.swallow.util.LabelValueBean;

import org.swallow.batchScheduler.ConnectionManager;
import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.maintenance.dao.MessageFormatsDAO;
import org.swallow.maintenance.model.MessageFields;
import org.swallow.maintenance.model.MessageFormats;
import org.swallow.maintenance.model.ScenarioMessageFields;
import org.swallow.maintenance.model.ScenarioMessageFormats;
import org.swallow.util.JDBCCloser;
import org.swallow.util.SwtInterceptor;
import org.swallow.util.SwtUtil;


import org.springframework.context.annotation.Lazy;
import org.springframework.orm.hibernate5.support.HibernateDaoSupport;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.stereotype.Repository;
import org.hibernate.HibernateException;
import org.hibernate.SessionFactory;
import org.hibernate.Transaction;
import org.hibernate.query.Query;
import org.hibernate.Session;


/**
 * 
 * This class provides the DAO service for handling MessageFormat objects and
 * MessageFeilds object.
 * 
 * <AUTHOR>
 */
@Repository ("messageFormatsDAO")
@Transactional
public class MessageFormatsDAOHibernate extends HibernateDaoSupport implements
		MessageFormatsDAO {
	public MessageFormatsDAOHibernate(@Lazy SessionFactory sessionfactory) {
	    setSessionFactory(sessionfactory);
	}

	private final Log log = LogFactory.getLog(MessageFormatsDAOHibernate.class);

	/**
	 * Method is used to fecth the objects of message format as a collection
	 * from database.
	 * 
	 * @param hostId
	 * @param entityId
	 * @return collection of message format objects
	 * @throws SwtException
	 * <AUTHOR> has written this method on 7 Aug 2007
	 */
	public Collection getMsgFormatDetailList(String entityId, String hostId)
			throws SwtException {
		log
				.debug("Inside MessageFormatsDAOHibernate.getMsgFormatDetailList method");

		Collection messageFormatsList = null;

		try {

			String hql = "from MessageFormats m where m.id.hostId = :hostId and m.id.entityId = :entityId order by m.id.formatId asc";
		    messageFormatsList = (Collection<MessageFormats>) getHibernateTemplate().execute((session) -> {
			    Query query = session.createQuery(hql);
			    query.setParameter("hostId", hostId);
			    query.setParameter("entityId", entityId);
			    return query.list();
			});

		} catch (HibernateException e) {
			log
					.error("Problem in MessageFormatsDAOHibernate.getMsgFormatDetailList method");

			throw (SwtErrorHandler.getInstance().handleException(e,
					"getMsgFormatDetailList", MessageFormatsDAOHibernate.class));
		}

		log
				.debug("Exit MessageFormatsDAOHibernate.getMsgFormatDetailList method");

		return (messageFormatsList != null) ? messageFormatsList
				: new ArrayList();
	}
	

	/**
	 * This method is used to fetch the message format object from the database
	 * with given format id.
	 * 
	 * @param entityId
	 * @param hostId
	 * @param formatId
	 * @return MessageFormats object for given formatId
	 * @throws SwtException
	 * <AUTHOR> Tripathi has Re-Written this method on 7th Aug 07
	 */
	public MessageFormats getMsgFormatDetail(String entityId, String hostId,
			String formatId) throws SwtException {
		log
				.debug("Inside MessageFormatsDAOHibernate.getMsgFormatDetail method");

		MessageFormats messageFormats = new MessageFormats();

		try {
	
			String hql = "from MessageFormats m where m.id.hostId = :hostId and m.id.entityId = :entityId and m.id.formatId = :formatId";
			messageFormats = (MessageFormats) getHibernateTemplate().execute((session) -> {
		        Query query = session.createQuery(hql);
		        query.setParameter("hostId", hostId);
		        query.setParameter("entityId", entityId);
		        query.setParameter("formatId", formatId);
		        return query.uniqueResult();
		    });
		} catch (HibernateException e) {
			log
					.error("Problem in MessageFormatsDAOHibernate.getMsgFormatDetail method");

			throw (SwtErrorHandler.getInstance().handleException(e,
					"getMsgFormatDetail", MessageFormatsDAOHibernate.class));
		}

		log.debug("Exit MessageFormatsDAOHibernate.getMsgFormatDetail method");

		return messageFormats;
	}
	

	/**
	 * This method is used to save the Message Format object into database
	 * 
	 * @param msgfmt
	 * @return none
	 * @throws SwtException
	 */
	@SuppressWarnings("unchecked")
	public void saveMsgFormatDetails(MessageFormats msgfmt) throws SwtException {
		/* Methods local variable declaration */
		List records = null;
		Session session = null;
		Transaction tx = null;
		SwtInterceptor interceptor = null;
		try {
			log.debug(this.getClass().getName()
					+ " - [saveMsgFormatDetails] - " + "Entering");
			/*
			 * Start : Modified for Mantis 1366-Remove entry to log if duplicate
			 * records added by betcy on 18-02-2011
			 */
			/* Query to fetches the records */
			records = (List ) getHibernateTemplate().find(
							"from MessageFormats m where m.id.hostId=?0 and m.id.entityId=?1 and m.id.formatId=?2",
							new Object[] { msgfmt.getId().getHostId(),
									msgfmt.getId().getEntityId(),
									msgfmt.getId().getFormatId() });
			/* Condition to check list size */
			if (records.size() == 0) {
				interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");
				session = getHibernateTemplate()
							.getSessionFactory()
							.withOptions().interceptor(interceptor).openSession();
				tx = session.beginTransaction();
				session.save(msgfmt);
				tx.commit();
			} else {
				throw new SwtException(
						"errors.DataIntegrityViolationExceptioninAdd");
			}
			log.debug(this.getClass().getName()
					+ " - [saveMsgFormatDetails] - " + "Exiting");
		} catch (Exception e) {
			e.printStackTrace();
			log
					.error(this.getClass().getName()
							+ " - Exception Catched in [saveMsgFormatDetails] method - "
							+ e.getMessage());
			throw (SwtErrorHandler.getInstance().handleException(e,
					"saveMsgFormatDetails", MessageFormatsDAOHibernate.class));
		} finally {
			JDBCCloser.close(session);
			records = null;
		}
		/*
		 * End : Modified for Mantis 1366-Remove entry to log if duplicate
		 * records added by betcy on 18-02-2011
		 */
	}

	/**
	 * This method is used to update the Message Format object into database
	 * 
	 * @param msgfmt
	 *            MessageFormats
	 * @throws SwtException
	 */
	public void updateMsgFormatDetails(MessageFormats msgfmt)
			throws SwtException {
		Session session = null;
		Transaction tx = null;
		SwtInterceptor interceptor = null;
		try {
			log
					.debug("entering MessageFormatsDAOHibernate.'updateMsgFormatDetails' method");
			interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");
			session = getHibernateTemplate().getSessionFactory().withOptions().interceptor(interceptor).openSession();
			tx = session.beginTransaction();
			session.update(msgfmt);
			tx.commit();
			log
					.debug("exiting MessageFormatsDAOHibernate.'updateMsgFormatDetails' method");
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					+ "- [updateMsgFormatDetails] - Exception " + exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp,
					"updateMsgFormatDetails", MessageFormatsDAOHibernate.class);
		}  finally {
			JDBCCloser.close(session);
		}
	}

	/**
	 * This method is used to delete the data from P_MESSAGE_FORMATS table. It
	 * also deletes the associated message fields with the given format id.
	 * 
	 * @param msgfmt
	 *            MessageFormats
	 * @throws SwtException
	 * <AUTHOR> Tripathi has re-written this class.
	 */
	public void deleteMsgFormatDetail(MessageFormats msgfmt)
			throws SwtException {
		Session session = null;
		Transaction tx = null;
		SwtInterceptor interceptor = null;
		try {
			log
					.debug("entering MessageFormatsDAOHibernate.'deleteMsgFormatDetail' method");

			String deleteHQLMessageFeild = "delete MessageFields m where m.id.hostId=:hostId and m.id.entityId=:entityId and m.id.formatId=:formatId";
			String deleteHQLMessageFormat = "delete MessageFormats m where m.id.hostId=:hostId and m.id.entityId=:entityId and m.id.formatId=:formatId";

			log.debug("Before delete Query");
			interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");
			session = getHibernateTemplate().getSessionFactory().withOptions().interceptor(interceptor).openSession();
			tx = session.beginTransaction();

			Query query1 = session.createQuery(deleteHQLMessageFeild)
					.setParameter("hostId", msgfmt.getId().getHostId())
					.setParameter("entityId", msgfmt.getId().getEntityId())
					.setParameter("formatId", msgfmt.getId().getFormatId());
			query1.executeUpdate();

			Query query2 = session.createQuery(deleteHQLMessageFormat)
					.setParameter("hostId", msgfmt.getId().getHostId())
					.setParameter("entityId", msgfmt.getId().getEntityId())
					.setParameter("formatId", msgfmt.getId().getFormatId());
			query2.executeUpdate();

			tx.commit();
			log
					.debug("exiting MessageFormatsDAOHibernate.'deleteMsgFormatDetail' method");
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					+ "- [deleteMsgFormatDetail] - Exception " + exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp,
					"deleteMsgFormatDetail", MessageFormatsDAOHibernate.class);
		}  finally {
			JDBCCloser.close(session);
		}
	}

	/**
	 * @param msgfld
	 * @throws SwtException
	 */
	public void saveMsgFieldDetails(MessageFields msgfld) throws SwtException {
		Session session = null;
		Transaction tx = null;
		SwtInterceptor interceptor = null;
		try {
			log.debug("entering 'saveMsgFieldDetails' method");
			interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");
			session = getHibernateTemplate()
						.getSessionFactory()
						.withOptions().interceptor(interceptor).openSession();
			tx = session.beginTransaction();
			session.save(msgfld);
			tx.commit();
			log.debug("exiting 'saveMsgFieldDetails' method");
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					+ "- [saveMsgFieldDetails] - Exception " + exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp,
					"saveMsgFieldDetails", MessageFormatsDAOHibernate.class);
		}  finally {
			JDBCCloser.close(session);
		}
	}

	/**
	 * @param msgfld
	 * @throws SwtException
	 */
	public void updateMsgFieldDetails(MessageFields msgfld) throws SwtException {
		Session session = null;
		Transaction tx = null;
		SwtInterceptor interceptor = null;
		try {
			log.debug("entering 'updateMsgFieldDetails' method");
			interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");
			session = getHibernateTemplate().getSessionFactory().withOptions().interceptor(interceptor).openSession();
			tx = session.beginTransaction();
			session.update(msgfld);
			tx.commit();
			log.debug("exiting 'updateMsgFieldDetails' method");
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					+ "- [updateMsgFieldDetails] - Exception " + exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp,
					"updateMsgFieldDetails", MessageFormatsDAOHibernate.class);
		}  finally {
			JDBCCloser.close(session);
		}
	}

	/**
	 * @param msgfld
	 * @throws SwtException
	 */
	public void deleteMessageFieldDetail(MessageFields msgfld)
			throws SwtException {
		Session session = null;
		Transaction tx = null;
		SwtInterceptor interceptor = null;
		try {
			log.debug("entering 'deleteMessageFieldDetail' method");
			interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");
			session = getHibernateTemplate().getSessionFactory().withOptions().interceptor(interceptor).openSession();
			tx = session.beginTransaction();
			session.delete(msgfld);
			tx.commit();
			log.debug("exiting 'deleteMessageFieldDetail' method");
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					+ "- [deleteMessageFieldDetail] - Exception " + exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp,
					"deleteMessageFieldDetail", MessageFormatsDAOHibernate.class);
		}  finally {
			JDBCCloser.close(session);
		}

	}

	/*
	 * Start:Code Modified by Chinniah on 16-AUG-2011 for Mantis 1521:Sweeping
	 * Process: New option to write messages to database
	 */
	/**
	 * This Method is used for Fetch interfaceId from database
	 * 
	 * @return collInterfaceId
	 * @throws SwtException
	 */
	public Collection getInterfaceId() throws SwtException {

		try {
			log.debug("entering 'getInterfaceId' method");
			// get the InterfaceId from the Tabel
			return getHibernateTemplate().find(
					"select i.interfaceId from InputInterface i");
		} catch (Exception e) {
			log
					.error("Exception Catch in MessageFormatsDAOHibernate.'getInterfaceId' method : "
							+ e.getMessage());
			throw (SwtErrorHandler.getInstance().handleException(e,
					"getInterfaceId", MessageFormatsDAOHibernate.class));
		} finally {
			log.debug("Exit 'getInterfaceId' method");
		}

	}

	/**
	 * This method is used to fetch data message path from database
	 * 
	 * @return String
	 * @throws SwtException
	 */
	public String getMessageFormatPath() throws SwtException {
		// Variable Decleration for path
		String path = null;
		// Variable Decleration for session
		Session session = null;
		// Variable Decleration for conn
		Connection conn = null;
		// Variable Decleration for cs
		CallableStatement cs = null;
		// Variable Decleration for rs
		ResultSet rs = null;
		try {
			log.debug("entering 'getMessageFormatPath' method");
			// Get session
			session = getHibernateTemplate().getSessionFactory().openSession();
			// Get connection
			conn = SwtUtil.connection(session);

			cs = conn
					.prepareCall("select ALL_DIRECTORIES.DIRECTORY_PATH from ALL_DIRECTORIES where ALL_DIRECTORIES.DIRECTORY_NAME='MESSAGES_DIR'");
			// Execute query
			rs = cs.executeQuery();
			while (rs.next()) {
				// Get the value from the tabel
				path = rs.getString(1);

			}

		} catch (Exception e) {
			log
					.error("Exception Catch in MessageFormatsDAOHibernate.'getMessageFormatPath' method : "
							+ e.getMessage());
			throw (SwtErrorHandler.getInstance().handleException(e,
					"getMessageFormatPath", MessageFormatsDAOHibernate.class));
		} finally {
			
			//edited by KaisBS for mantis 1745 : introducing the implementation of the JDBCCloser class
			SwtException thrownException = null;
			Object[] exceptions = JDBCCloser.close(rs, cs, conn, session);

			if (exceptions[0] != null)
			    thrownException = (SwtErrorHandler.getInstance().handleException((SQLException) exceptions[0],	"getMessageFormatPath",	MessageFormatsDAOHibernate.class)); 

			if (thrownException == null && exceptions[1] !=null) 
				thrownException = (SwtErrorHandler.getInstance().handleException((HibernateException) exceptions[1], "getMessageFormatPath", MessageFormatsDAOHibernate.class)); 

			if(thrownException!=null)
				throw thrownException;

			log.debug("Exit 'getMessageFormatPath' method");
		}
		return path;
	}
	/*
	 * End:Code Modified by Chinniah on 16-AUG-2011 for Mantis 1521:Sweeping
	 * Process: New option to write messages to database
	 */
	
	/**
	 * Method is used to fecth the objects of message format as a collection
	 * from database.
	 * 
	 * @param hostId
	 * @param entityId
	 * @return collection of message format objects
	 * @throws SwtException
	 * <AUTHOR> has written this method on 7 Aug 2007
	 */
	public Collection getScenarioMsgFormatDetailList()
			throws SwtException{
		log
		.debug("Inside MessageFormatsDAOHibernate.getMsgFormatDetailList method");
		
		Collection messageFormatsList = null;
		Session sessionObj = null;
		
		try {

	        String hql = "from ScenarioMessageFormats sc order by sc.id.formatId asc";
	        messageFormatsList = (Collection<ScenarioMessageFormats>) getHibernateTemplate().execute((session) -> {
	            Query query = session.createQuery(hql);
	            return query.list();
	        });
		} catch (HibernateException e) {
			log
			.error("Problem in MessageFormatsDAOHibernate.getMsgFormatDetailList method");
			
			throw (SwtErrorHandler.getInstance().handleException(e,
					"getMsgFormatDetailList", MessageFormatsDAOHibernate.class));
		} finally {
			JDBCCloser.close(null, null, sessionObj);
		}
		
		log
		.debug("Exit MessageFormatsDAOHibernate.getMsgFormatDetailList method");
		
		return (messageFormatsList != null) ? messageFormatsList
				: new ArrayList();
	}
	
	/**
	 * This method is used to fetch the message format object from the database
	 * with given format id.
	 * 
	 * @param entityId
	 * @param hostId
	 * @param formatId
	 * @return MessageFormats object for given formatId
	 * @throws SwtException
	 * <AUTHOR> Tripathi has Re-Written this method on 7th Aug 07
	 */
	public ScenarioMessageFormats getScenarioMsgFormatDetail(String formatId) throws SwtException {
		log
		.debug("Inside MessageFormatsDAOHibernate.getMsgFormatDetail method");
		Session session = null;
		ScenarioMessageFormats messageFormats = new ScenarioMessageFormats();
		
		try {

			String hql = "from ScenarioMessageFormats sc where  sc.id.formatId=:formatId" ;

		    messageFormats = (ScenarioMessageFormats) getHibernateTemplate().execute((sessionObj) -> {
		        Query query = sessionObj.createQuery(hql);
		        query.setParameter("formatId", formatId);
		        return query.uniqueResult();
		    });
		} catch (HibernateException e) {
			log
			.error("Problem in MessageFormatsDAOHibernate.getMsgFormatDetail method");
			
			throw (SwtErrorHandler.getInstance().handleException(e,
					"getMsgFormatDetail", MessageFormatsDAOHibernate.class));
		}finally {
			JDBCCloser.close(session);
		}
		
		log.debug("Exit MessageFormatsDAOHibernate.getMsgFormatDetail method");
		
		return messageFormats;
	}
	
	
	
	
	
	
	
	
	/**
	 * This method is used to save the Message Format object into database
	 * 
	 * @param msgfmt
	 * @return none
	 * @throws SwtException
	 */
	@SuppressWarnings("unchecked")
	public void saveScenarioMsgFormatDetails(ScenarioMessageFormats msgfmt) throws SwtException {
		/* Methods local variable declaration */
		List records = null;
		Session session = null;
		Transaction tx = null;
		SwtInterceptor interceptor = null;
		try {
			log.debug(this.getClass().getName()
					+ " - [saveMsgFormatDetails] - " + "Entering");
			/*
			 * Start : Modified for Mantis 1366-Remove entry to log if duplicate
			 * records added by betcy on 18-02-2011
			 */
			/* Query to fetches the records */
			records = (List ) getHibernateTemplate().find(
							"from ScenarioMessageFields m where  m.id.formatId=?0",
							new Object[] { 
									msgfmt.getId().getFormatId() });
			/* Condition to check list size */
			if (records.size() == 0) {
				interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");
				session = getHibernateTemplate()
							.getSessionFactory()
							.withOptions().interceptor(interceptor).openSession();
				tx = session.beginTransaction();
				session.save(msgfmt);
				tx.commit();
			} else {
				throw new SwtException(
						"errors.DataIntegrityViolationExceptioninAdd");
			}
			log.debug(this.getClass().getName()
					+ " - [saveMsgFormatDetails] - " + "Exiting");
		} catch (Exception e) {
			log
					.error(this.getClass().getName()
							+ " - Exception Catched in [saveMsgFormatDetails] method - "
							+ e.getMessage());
			throw (SwtErrorHandler.getInstance().handleException(e,
					"saveMsgFormatDetails", MessageFormatsDAOHibernate.class));
		} finally {
			JDBCCloser.close(session);
			records = null;
		}
		/*
		 * End : Modified for Mantis 1366-Remove entry to log if duplicate
		 * records added by betcy on 18-02-2011
		 */
	}

	/**
	 * This method is used to update the Message Format object into database
	 * 
	 * @param msgfmt
	 *            MessageFormats
	 * @throws SwtException
	 */
	public void updateScenarioMsgFormatDetails(ScenarioMessageFormats msgfmt)
			throws SwtException {
		Session session = null;
		Transaction tx = null;
		SwtInterceptor interceptor = null;
		try {
			log
					.debug("entering MessageFormatsDAOHibernate.'updateMsgFormatDetails' method");
			interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");
			session = getHibernateTemplate().getSessionFactory().withOptions().interceptor(interceptor).openSession();
			tx = session.beginTransaction();
			session.update(msgfmt);
			tx.commit();
			log
					.debug("exiting MessageFormatsDAOHibernate.'updateMsgFormatDetails' method");
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					+ "- [updateMsgFormatDetails] - Exception " + exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp,
					"updateMsgFormatDetails", MessageFormatsDAOHibernate.class);
		}  finally {
			JDBCCloser.close(session);
		}
	}

	/**
	 * This method is used to delete the data from P_MESSAGE_FORMATS table. It
	 * also deletes the associated message fields with the given format id.
	 * 
	 * @param msgfmt
	 *            MessageFormats
	 * @throws SwtException
	 * <AUTHOR> Tripathi has re-written this class.
	 */
	public void deleteScenarioMsgFormatDetail(ScenarioMessageFormats msgfmt)
			throws SwtException {
		Session session = null;
		Transaction tx = null;
		SwtInterceptor interceptor = null;
		try {
			log
					.debug("entering MessageFormatsDAOHibernate.'deleteMsgFormatDetail' method");

			String deleteHQLMessageFeild = "delete ScenarioMessageFields m where m.id.formatId=:formatId";
			String deleteHQLMessageFormat = "delete ScenarioMessageFormats m where m.id.formatId=:formatId";

			log.debug("Before delete Query");
			interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");
			session = getHibernateTemplate().getSessionFactory().withOptions().interceptor(interceptor).openSession();
			tx = session.beginTransaction();

			Query query1 = session.createQuery(deleteHQLMessageFeild)
					.setParameter("formatId", msgfmt.getId().getFormatId());
			query1.executeUpdate();

			Query query2 = session.createQuery(deleteHQLMessageFormat)
					.setParameter("formatId", msgfmt.getId().getFormatId());
			query2.executeUpdate();

			tx.commit();
			log
					.debug("exiting MessageFormatsDAOHibernate.'deleteMsgFormatDetail' method");
		} catch (Exception exp) {
			exp.printStackTrace();
			log.error(this.getClass().getName()
					+ "- [deleteMsgFormatDetail] - Exception " + exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp,
					"deleteMsgFormatDetail", MessageFormatsDAOHibernate.class);
		}  finally {
			JDBCCloser.close(session);
		}
	}

	/**
	 * @param msgfld
	 * @throws SwtException
	 */
	public void saveScenarioMsgFieldDetails(ScenarioMessageFields msgfld) throws SwtException {
		Session session = null;
		Transaction tx = null;
		SwtInterceptor interceptor = null;
		try {
			log.debug("entering 'saveMsgFieldDetails' method");
			interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");
			session = getHibernateTemplate()
						.getSessionFactory()
						.withOptions().interceptor(interceptor).openSession();
			tx = session.beginTransaction();
			session.save(msgfld);
			tx.commit();
			log.debug("exiting 'saveMsgFieldDetails' method");
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					+ "- [saveMsgFieldDetails] - Exception " + exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp,
					"saveMsgFieldDetails", MessageFormatsDAOHibernate.class);
		}  finally {
			JDBCCloser.close(session);
		}
	}

	/**
	 * @param msgfld
	 * @throws SwtException
	 */
	public void updateScenarioMsgFieldDetails(ScenarioMessageFields msgfld) throws SwtException {
		Session session = null;
		Transaction tx = null;
		SwtInterceptor interceptor = null;
		try {
			log.debug("entering 'updateMsgFieldDetails' method");
			interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");
			session = getHibernateTemplate().getSessionFactory().withOptions().interceptor(interceptor).openSession();
			tx = session.beginTransaction();
			session.update(msgfld);
			tx.commit();
			log.debug("exiting 'updateMsgFieldDetails' method");
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					+ "- [updateMsgFieldDetails] - Exception " + exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp,
					"updateMsgFieldDetails", MessageFormatsDAOHibernate.class);
		}  finally {
			JDBCCloser.close(session);
		}
	}

	/**
	 * @param msgfld
	 * @throws SwtException
	 */
	public void deleteScenarioMessageFieldDetail(ScenarioMessageFields msgfld)
			throws SwtException {
		Session session = null;
		Transaction tx = null;
		SwtInterceptor interceptor = null;
		try {
			log.debug("entering 'deleteMessageFieldDetail' method");
			interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");
			session = getHibernateTemplate().getSessionFactory().withOptions().interceptor(interceptor).openSession();
			tx = session.beginTransaction();
			session.delete(msgfld);
			tx.commit();
			log.debug("exiting 'deleteMessageFieldDetail' method");
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					+ "- [deleteMessageFieldDetail] - Exception " + exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp,
					"deleteMessageFieldDetail", MessageFormatsDAOHibernate.class);
		}  finally {
			JDBCCloser.close(session);
		}

	}
	
	
	/**
	 * This method is used to get scenario message formats from P_SCN_MESSAGE_FORMAT
	 */
	public Collection<LabelValueBean>  getScenMsgFormats() throws SwtException{
		log.debug(this.getClass().getName() + " - [getScenMsgFormats] - "
				+ "Entry");
		/* Method's local variable declaration */
		Connection conn = null;
		Session session = null;
		PreparedStatement stmt = null;
		ResultSet rs = null;
		Collection<LabelValueBean> msgFormats =null;
		try {
			msgFormats = new ArrayList<LabelValueBean>();
			conn = ConnectionManager.getInstance().databaseCon();
			String getAccountStatus = "select FORMAT_ID, FORMAT_NAME from P_SCN_MESSAGE_FORMAT frmt";
			stmt = conn.prepareStatement(getAccountStatus);
//			stmt.setString(1, scenarioId);
			stmt.execute();
			/* Fetching the Result */
			rs = (ResultSet) stmt.getResultSet();
			if (rs != null) {
				/* Loop to iterate result set */
				while (rs.next()) {
					ScenarioMessageFormats messageFormat = new ScenarioMessageFormats();
					messageFormat.getId().setFormatId(rs.getString(1));	
					messageFormat.setFormatName(rs.getString(2));
					
					msgFormats.add(new LabelValueBean(rs.getString(1),rs.getString(2)));
				}
			}	
			log.debug(this.getClass().getName() + " - [getScenMsgFormats] - "
					+ "Exit");
			//return mvtType;
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [getScenMsgFormats] method : - "
					+ exp.getMessage());
		} finally {
			JDBCCloser.close(rs, stmt, conn, session);

		}
		return msgFormats;
	}
	
	
	/**
	 * This method is used to delete scenario message formats from P_SCN_MESSAGE_FORMAT and P_SCN_MESSAGE_FIELDS
	 */
	public void deleteScenMsgFormatById(String formatId)
			throws SwtException {
		Session session = null;
		Transaction tx = null;
		SwtInterceptor interceptor = null;
		try {
			log
					.debug("entering MessageFormatsDAOHibernate.'deleteScenMsgFormatById' method");
	
			String deleteHQLMessageField = "from ScenarioMessageFields m where m.id.formatId=?0";
			String deleteHQLMessageFormat = "from ScenarioMessageFormats m where m.id.formatId=?0";
	
			log.debug("Before delete Query");
			interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");
			session = getHibernateTemplate().getSessionFactory().withOptions().interceptor(interceptor).openSession();
			tx = session.beginTransaction();

			Query query1 = session.createQuery(deleteHQLMessageField)
			.setParameter(0, formatId);
			query1.executeUpdate();

			Query query2 = session.createQuery(deleteHQLMessageFormat)
			.setParameter(0, formatId);
			query2.executeUpdate();


			tx.commit();
			log
					.debug("exiting MessageFormatsDAOHibernate.'deleteScenMsgFormatById' method");
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					+ "- [deleteScenMsgFormatById] - Exception " + exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp,
					"deleteScenMsgFormatById", MessageFormatsDAOHibernate.class);
		}  finally {
			JDBCCloser.close(session);
		}
	}
}