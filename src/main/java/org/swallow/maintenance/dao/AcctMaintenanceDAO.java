/*
 * @(#)AcctMaintenanceDAO.java 1.0 20/12/05
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */

package org.swallow.maintenance.dao;

import java.util.ArrayList;
import java.util.Collection;

import org.swallow.dao.DAO;
import org.swallow.exception.SwtException;
import org.swallow.maintenance.model.AccSweepSchedule;
import org.swallow.maintenance.model.AccountSweepBalanceGroup;
import org.swallow.maintenance.model.AcctMaintenance;
import org.swallow.maintenance.model.Country;
import org.swallow.maintenance.model.Currency;
import org.swallow.maintenance.model.Party;
import org.swallow.util.SystemFormats;

/**
 * AcctMaintenanceDAO.java
 * 
 * This interface has methods that are used for accessing the persistent storage
 * such as database which helps client to create, retrieve and persists data to
 * the Persistent Object.
 * 
 */
public interface AcctMaintenanceDAO extends DAO {

	/**
	 * @param entityId
	 * @param hostId
	 * @return
	 * @throws SwtException
	 */
	/*
	 * Method parameter modified For Mantis 1592 by Sudhakar on
	 * 22-12-2011:Account Maintenance screen allows to create account for entity
	 * that has no currency access
	 */
	public Collection<AcctMaintenance> getAcctMaintenanceDetailList(
			String entityId, String hostId, String roleId, String currencyId, String quickFilter, String acctType) throws SwtException;

	/**
	 * @param entityId
	 * @param hostId
	 * @return
	 * @throws SwtException
	 */
	public ArrayList getCurrencyMasterList(String entityId, String hostId)
			throws SwtException;

	/**
	 * 
	 * @param acct
	 * @param accountInterestRateList
	 * @throws SwtException
	 */
	public void saveAcctMaintDetail(AcctMaintenance acct,
			ArrayList accountInterestRateList) throws SwtException;

	/**
	 * @param hostId
	 * @param entityId
	 * @param accountId
	 * @return
	 * @throws SwtException
	 */
	public AcctMaintenance getEditableData(String hostId, String entityId,
			String accountId) throws SwtException;

	/**
	 * 
	 * @param acct
	 * @param collAcctIntRateDeleted
	 * @param collAcctIntRateAdded
	 * @param collAcctIntRateUpdated
	 * @throws SwtException
	 */
	void updateAcctDetail(AcctMaintenance acct,
			Collection collAcctIntRateDeleted, Collection collAcctIntRateAdded,
			Collection collAcctIntRateUpdated, SystemFormats sysforma)
			throws SwtException;

	/**
	 * @param hostId
	 * @param entityId
	 * @return
	 * @throws SwtException
	 */
	public Collection getBookListColl(String hostId, String entityId)
			throws SwtException;

	/**
	 * @param hostId
	 * @param entityId
	 * @return
	 * @throws SwtException
	 */
	public Collection getFormatListColl(String hostId, String entityId)
			throws SwtException;

	/**
	 * @param hostId
	 * @param entityId
	 * @return
	 * @throws SwtException
	 */
	public Collection getMainAcctListColl(String hostId, String entityId)
			throws SwtException;

	/**
	 * 
	 * @param acct
	 * @param collAcctIntRateDeleted
	 * @param sysforma
	 * @throws SwtException
	 */
	void deleteAcctDetail(AcctMaintenance acct,
			Collection collAcctIntRateDeleted, SystemFormats sysforma)
			throws SwtException;

	/**
	 * @param entityId
	 * @param hostId
	 * @param accttype
	 * @return
	 * @throws SwtException
	 */
	public Collection getAccountTypeList(String entityId, String hostId,
			String accttype) throws SwtException;

	/**
	 * @param entityId
	 * @param hostId
	 * @param accountId
	 * @return
	 * @throws SwtException
	 */
	public Collection getSubColumnDataDetailList(String entityId,
			String hostId, String accountId) throws SwtException;

	/**
	 * @param entityId
	 * @param hostId
	 * @param currencyId
	 * @param accttype
	 * @return
	 * @throws SwtException
	 */
	public Collection getAccountTypeDetailList(String entityId, String hostId,
			String currencyId, String accttype) throws SwtException;

	/**
	 * @param entityId
	 * @param hostId
	 * @return
	 * @throws SwtException
	 */
	public Collection getAccountIDDropDown(String entityId, String hostId)
			throws SwtException;

	/**
	 * @param hostId
	 * @param entityId
	 * @param accountId
	 * @return
	 * @throws SwtException
	 */
	public AcctMaintenance copyAccountIdDetails(String hostId, String entityId,
			String accountId) throws SwtException;

	/**
	 * @param entityId
	 * @param hostId
	 * @param currencyId
	 * @return
	 * @throws SwtException
	 */
	public Collection getAccountTypeCurrencyList(String entityId,
			String hostId, String currencyId) throws SwtException;

	/**
	 * @param entityId
	 * @param hostId
	 * @param accountId
	 * @return
	 * @throws SwtException
	 */
	public AcctMaintenance getMainAcctDetails(String entityId, String hostId,
			String accountId) throws SwtException;

	/**
	 * 
	 * @return
	 * @throws SwtException
	 */
	public Collection getCountryList() throws SwtException;

	/**
	 * 
	 * @param hostId -
	 *            HostID
	 * @param entityId -
	 *            EntityID
	 * @param currencyCode -
	 *            CurrencyCode
	 * @return Collection - Collection of AcctMaintenance Objects
	 * @throws SwtException -
	 *             SwtException
	 */
	Collection getLinkAccountList(String hostId, String entityId,
			String currencyCode) throws SwtException;

	/**
	 * 
	 * @param hostId -
	 *            HostID
	 * @param entityId -
	 *            EntityID
	 * @param currencyCode -
	 *            CurrencyCode
	 * @param accountId -
	 *            accountId
	 * @return Collection - Collection of AcctMaintenance Objects
	 * @throws SwtException -
	 *             SwtException
	 */
	Collection getLinkAccountListInChange(String hostId, String entityId,
			String currencyCode, String accountId) throws SwtException;

	/**
	 * @desc - This method finds all the Accounts linked with this account
	 * @param hostId -
	 *            HostID
	 * @param entityId -
	 *            EntityID
	 * @param accountId -
	 *            AccountID
	 * @return Collection - Collection of Accounts Objects
	 * @throws SwtException -
	 *             SwtException
	 * 
	 */
	Collection getLinkedAccounts(String hostId, String entityId,
			String accountId) throws SwtException;

	/**
	 * @desc This function is used to get the account interest rate list which
	 *       comes from P_ACCOUNT_INTEREST_RATE
	 * @param hostId-HostID
	 * @param entityId-EntityID
	 * @param accountId-AccountID
	 * @return- accInterestRateList --Collection ofaccount Interest rate
	 * @throws SwtException
	 */
	Collection getAccInterestRateList(String hostId, String entityId,
			String accountId) throws SwtException;

	/**
	 * 
	 * @param hostId
	 * @param entityId
	 * @param currencyCode
	 * @return
	 * @throws SwtException
	 */
	Collection getMainAcctListByCurr(String hostId, String entityId,
			String currencyCode) throws SwtException;

	public Collection getIntermediaryRecord(String hostId, String entityId,
			String currencyCode, String acctBicCode) throws SwtException;

	/**
	 * This function is used to get the details of country for given countryId
	 * 
	 * @param countryId -
	 *            String
	 * @return -Country
	 * @throws SwtException
	 */
	public Country getCountryDetail(String countryId) throws SwtException;

	/**
	 * This function is used to get the details of currency for given currency
	 * code
	 * 
	 * @param entityId -
	 *            String
	 * @param hostId -
	 *            String
	 * @param currencyCode -
	 *            String
	 * @return -Currency
	 * @throws SwtException
	 */
	public Currency getCurrencyDetail(String entityId, String hostId,
			String currencyCode) throws SwtException;

	/**
	 * Returns false if the required party id does not exist, and true if not
	 * @param hostId
	 * @param entityId
	 * @param partyId
	 * @return String
	 * @throws SwtException 
	 */
	public String checkIfPartyIdExists(String hostId, String entityId,
			String partyId) throws SwtException;

	/**
	 * @param hostId
	 * @param entityId
	 * @param currency
	 * @param account
	 * @return String
	 * @throws SwtException 
	 */
	public String checkAccountIlmDataMember(String hostId, String entityId, String currency, String account) throws SwtException;
	

	/**
	 * @param hostId
	 * @param entityId
	 * @param currency
	 * @return String
	 * @throws SwtException 
	 */
	public boolean checkIfAccountExistForEntity(String hostId, String entityId, String currency, String account) throws SwtException;
	
	/**
	 * @param hostId
	 * @param accountId
	 * @return String
	 * @throws SwtException 
	 */
	public boolean checkIfAccountExists(String hostId,String account) throws SwtException;

	/**
	 * 
	 * @param hostId
	 * @param entityId
	 * @param accountId
	 * @return
	 * @throws SwtException
	 */
	public Collection<AccSweepSchedule> getAcctSweepScheduleList(String hostId, String entityId, String accountId, boolean fromAcctMaintenace) throws SwtException;
	
	/**
	 * 
	 * @param hostId
	 * @param entityId
	 * @param currencyCode
	 * @param accountId
	 * @param otherAccountId
	 * @return
	 * @throws SwtException
	 */
	public Collection<AccSweepSchedule> getAcctSweepScheduleListBetweenAccounts(String hostId, String entityId, String currencyCode,  String accountId,
			String otherEntityId, String otherAccountId) throws SwtException;

	/**
	 * 
	 * @param hostId
	 * @param entityId
	 * @param accountId
	 * @return
	 * @throws SwtException
	 */
	public long getAcctSweepScheduleUsedinCount(String hostId, String entityId, String accountId) throws SwtException;
	/**
	 * 
	 * @param hostId
	 * @param seqNumber
	 * @return
	 */
	public AccSweepSchedule getAcctSweepScheduleDetails(String hostId, String seqNumber) throws SwtException;
	/**
	 * 
	 * @param accountSchedule
	 * @param methodName
	 * @throws SwtException
	 */
	public void saveOrUpdateAcctScheduleSweep(AccSweepSchedule accountSchedule, String methodName)  throws SwtException;
	
	/**
	 * 
	 * @param accountSchedule
	 * @throws SwtException
	 */
	public void deleteAcctScheduleSweep(AccSweepSchedule accountSchedule) throws SwtException ;
	
	public Collection<AccountSweepBalanceGroup> getAcctSweepBalGrpcoll(String hostId, String entityId, String accountId) throws SwtException;
	
	/**
	 * 
	 * @param acctSweepBalGrp
	 * @throws SwtException
	 */
	public void saveAcctSweepBalGrp(AccountSweepBalanceGroup acctSweepBalGrp)  throws SwtException;
	
	/**
	 * 
	 * @param acctSweepBalGrp
	 * @throws SwtException
	 */
	public void deleteAcctSweepBalGrp(AccountSweepBalanceGroup acctSweepBalGrp) throws SwtException ;
	
	/**
	 * 
	 * @param hostId -
	 *            HostID
	 * @param entityId -
	 *            EntityID
	 * @param currencyCode -
	 *            CurrencyCode
	 * @return Collection - Collection of AcctMaintenance Objects
	 * @throws SwtException -
	 *             SwtException
	 */
	Collection getLinkAccountFullList(String hostId, String entityId,
			String currencyCode) throws SwtException;

	Collection getAcctAttributesList() throws SwtException; 
	
	Collection getAllowedTrgBalRulesList() throws SwtException; 
	
	public ArrayList<Party> getPartySearchResult(String partyId, String partyName, String entityId,
			int pageSize, int currentPage, String selectedsort) throws SwtException;
	/**
	 * 
	 * @param partyName
	 * @param partyId
	 * @param entityId
	 * @return int
	 * @throws SwtException
	 */
	public int getTotalCount(String partyName, String partyId, String entityId) throws SwtException;

	
	/**
	 * This is used to check if current account is linked to another account
	 * 
	 * @param accountAccess
	 * @return boolean
	 * @throws SwtException
	 */
	public boolean checkAccountLinkedList(String accountId, String entityId) throws SwtException;

	/**+
	 *
	 * @param hostId
	 * @param entityId
	 * @param accountId
	 * @return
	 * @throws SwtException
	 */
	public String getAccountStatus(String hostId, String entityId, String accountId) throws SwtException;

}
