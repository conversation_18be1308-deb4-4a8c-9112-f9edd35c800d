/*
 * @(#)CountryDAO.java 1.0 23/08/06
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.maintenance.dao;

import org.swallow.dao.*;
import java.util.*;

public interface CountryDAO extends DAO{
	/**
	 * Method get the Country List from DB
	 * @return Collection
	 */
	public Collection getCountries();
}