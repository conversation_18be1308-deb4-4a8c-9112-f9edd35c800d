/*
 * @(#)SweepIntermediariesDAO.java  02/07/07
 * 
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 * 
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */

package org.swallow.maintenance.dao;

import org.swallow.dao.DAO;

import org.swallow.exception.SwtException;

import org.swallow.maintenance.model.SweepIntermediaries;

import java.util.Collection;



public interface SweepIntermediariesDAO extends DAO {
    public Collection getSweepIntermediariesDetails(String hostId,
        String entityId) throws SwtException;

    public String getCurrencyNameFromMaster(String currencyCode)
        throws SwtException;

    public void saveSweepIntermediariesDetail(
        SweepIntermediaries sweepIntermediaries) throws SwtException;
    
    /*START:code added by <PERSON><PERSON><PERSON> on 08-Oct-2009 for Mantis 1028: added change button in UI screen  */
    public void updateSweepIntermediariesDetail(
            SweepIntermediaries sweepIntermediaries) throws SwtException;
    /*END:code added by <PERSON><PERSON><PERSON> on 08-Oct-2009 for Mantis 1028: added change button in UI screen  */

    public void deleteSweepIntermediariesDetail(
        SweepIntermediaries sweepIntermediaries) throws SwtException;
}
