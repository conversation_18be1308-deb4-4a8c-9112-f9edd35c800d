/*
 * @(#)GroupLevelDAO.java 1.0 21/12/05
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.maintenance.dao;

import java.util.Collection;

import org.swallow.exception.SwtException;

public interface GroupLevelDAO {
	/**
     * This is used to fetched group level detaild from P_GROUP table
     * @param hostId
     * @param entityId
     * @throws SwtException
     * @return Collection*/
	
    public Collection getGroupLevelDetail(String hostId, String entityId)throws SwtException;
}
