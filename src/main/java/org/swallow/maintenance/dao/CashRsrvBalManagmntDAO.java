
 
package org.swallow.maintenance.dao;

import java.util.Collection;

import org.swallow.dao.DAO;
import org.swallow.exception.SwtException;
import org.swallow.work.model.CashManagementVO;


public interface CashRsrvBalManagmntDAO extends DAO {
	
	public CashManagementVO getCashRsvBalanceGridData(CashManagementVO cashManagementVO) throws SwtException;
	public CashManagementVO getCashRsvBalancePeriod(CashManagementVO cashManagementVO) throws SwtException;
	/**
	 * @param entityId
	 * @param hostId
	 * @param currencyCode
	 * @return
	 * @throws SwtException
	 */
	public Collection getAccountIDDropDown(String hostId , String entityId, String currencyCode, String accountType)
			throws SwtException;
}
