/*
 * @(#)DefaultAcctMaintenanceDAO .java 20/12/05
 * 
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 * 
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */

package org.swallow.maintenance.dao;

import java.util.ArrayList;
import java.util.Collection;
import org.swallow.dao.DAO;
import org.swallow.exception.SwtException;
import org.swallow.maintenance.model.DefaultAcct;
import org.swallow.maintenance.model.Entity;
import org.swallow.util.SystemFormats;


public interface DefaultAcctMaintenanceDAO extends DAO {

	/**
	 * Queries hibernate for a collection of defaultAcct objects matching the supplied entityId and hostId parameters
	 * @param entityId
	 * @param hostId
	 * @return Collection
	 * @throws SwtException
	 */
	public Collection getList(String entityId, String hostId) throws SwtException;
	
	/**
	 * Deletes the unique account matching the PK data in the given DefaultAcct object
	 * @param acct
	 * @throws SwtException
	 */
	public void deleteRecord (DefaultAcct acct) throws SwtException;
	
	/**
	 * Queries hibernate for a unique record matching the entityId, hostId, xrefCode and currencyCode contained in the given DefaultAcct object
	 * @param acct
	 * @return DefaultAcct
	 * @throws SwtException
	 */
	public DefaultAcct getRecord (DefaultAcct acct) throws SwtException;

	/**
	 * Queries hibernate for a collection of accountMaintenance objects matching the given currencyCode, hostId and entityId parameters
	 * @param hostId
	 * @param entityId
	 * @param currencyCode
	 * @return Collection
	 * @throws SwtException
	 */
	public Collection getAccountList (String hostId, String entityId, String currencyCode) throws SwtException;
	
	/**
	 * Persists the DefaultAcct given to hibernate
	 * @param entityId
	 * @param hostId
	 * @throws SwtException
	 */
	public void saveRecord (DefaultAcct acct) throws SwtException;
	
	/**
	 * Updates the persistent storage with the existing DefaultAcct object given. The PK data contained therein is used to locate the record to update.
	 * @param acct
	 * @throws SwtException
	 */
	public void updateRecord (DefaultAcct acct) throws SwtException;
}
