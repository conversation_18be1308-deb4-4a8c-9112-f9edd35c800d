package org.swallow.maintenance.model;

import java.math.BigDecimal;
import java.util.Date;
import java.util.Hashtable;

import org.swallow.model.BaseObject;
import org.swallow.util.SwtUtil;

public class AccountAttributeHDR extends BaseObject implements org.swallow.model.AuditComponent{

	private static final Long serialVersionUID = 1L;
	private Id id = new Id();
	
	private String attributeName;
	private String tooltipText;
	private String valueType;
	private String effectiveDateRequired;
	private String effectiveDateAllowTime;
	private BigDecimal validateNumMin;
	private BigDecimal validateNumMax;
	private String validateDateAllowTime;
	private BigDecimal validateTextMinLen;
	private BigDecimal validateTextMaxLen;
	private String validateTextRegex;
	private String validateTextRegexMsg;
	private Date updateDate;
	private String updateUser;
	private String updateDateAsString;
	private String systemFlag;
	
	public static Hashtable logTable = new Hashtable();
	static {
		logTable.put("attributeName", "Attribute Name");
		logTable.put("tooltipText", "Tooltip Text");
		logTable.put("effectiveDateRequired", "Effective DateRequired");
		logTable.put("effectiveDateAllowTime", "Effective Date Allow Time");
		logTable.put("valueType", "Type");
		logTable.put("validateTextMinLen", "Minimum Length");
		logTable.put("validateTextMaxLen", "Maximum Length");
		logTable.put("validateNumMin", "Minimum Value");
		logTable.put("validateNumMax", "Maximum Value");
		logTable.put("validateTextRegex", "Validation Regex");
		logTable.put("validateTextRegexMsg", "Validation Message");
		logTable.put("systemFlag", "System Flag");
		
	}

	
	public Id getId() {
		return id;
	}
	public void setId(Id id) {
		this.id = id;
	}
	public static class Id extends BaseObject{
		private String attributeId;
		public Id() {};
		public Id(String attributeId) {
			this.attributeId = attributeId;
		};
		public String getAttributeId() {
			return attributeId;
		}
		public void setAttributeId(String attributeId) {
			this.attributeId = attributeId;
		}
	}
	public String getAttributeName() {
		return attributeName;
	}
	public void setAttributeName(String attributeName) {
		this.attributeName = attributeName;
	}
	public String getTooltipText() {
		return tooltipText;
	}
	public void setTooltipText(String tooltipText) {
		this.tooltipText = tooltipText;
	}
	public String getValueType() {
		return valueType;
	}
	public void setValueType(String valueType) {
		this.valueType = valueType;
	}
	public String getEffectiveDateRequired() {
		return effectiveDateRequired;
	}
	public void setEffectiveDateRequired(String effectiveDateRequired) {
		this.effectiveDateRequired = effectiveDateRequired;
	}
	public String getEffectiveDateAllowTime() {
		return effectiveDateAllowTime;
	}
	public void setEffectiveDateAllowTime(String effectiveDateAllowTime) {
		this.effectiveDateAllowTime = effectiveDateAllowTime;
	}
	public BigDecimal getValidateNumMin() {
		return validateNumMin;
	}
	public void setValidateNumMin(BigDecimal validateNumMin) {
		this.validateNumMin = validateNumMin;
	}
	public BigDecimal getValidateNumMax() {
		return validateNumMax;
	}
	public void setValidateNumMax(BigDecimal validateNumMax) {
		this.validateNumMax = validateNumMax;
	}
	public String getValidateDateAllowTime() {
		return validateDateAllowTime;
	}
	public void setValidateDateAllowTime(String validateDateAllowTime) {
		this.validateDateAllowTime = validateDateAllowTime;
	}
	public BigDecimal getValidateTextMinLen() {
		return validateTextMinLen;
	}
	public void setValidateTextMinLen(BigDecimal validateTextMinLen) {
		this.validateTextMinLen = validateTextMinLen;
	}
	public BigDecimal getValidateTextMaxLen() {
		return validateTextMaxLen;
	}
	public void setValidateTextMaxLen(BigDecimal validateTextMaxLen) {
		this.validateTextMaxLen = validateTextMaxLen;
	}
	public String getValidateTextRegex() {
		return validateTextRegex;
	}
	public void setValidateTextRegex(String validateTextRegex) {
		this.validateTextRegex = validateTextRegex;
	}
	
	public String getValidateTextRegexMsg() {
		return validateTextRegexMsg;
	}
	public void setValidateTextRegexMsg(String validateTextRegexMsg) {
		this.validateTextRegexMsg = validateTextRegexMsg;
	}
	public Date getUpdateDate() {
		return updateDate;
	}
	public void setUpdateDate(Date updateDate) {
		this.updateDate = updateDate;
	}
	public String getUpdateUser() {
		return updateUser;
	}
	public void setUpdateUser(String updateUser) {
		this.updateUser = updateUser;
	}
	public String getUpdateDateAsString() {
		return updateDateAsString;
	}
	public void setUpdateDateAsString(String updateDateAsString) {
		this.updateDateAsString = updateDateAsString;
	}
	public String getSystemFlag() {
		return systemFlag;
	}
	public void setSystemFlag(String systemFlag) {
		// if the system flag is null, ie, the value saved in the DB is empty then set it by default to "N"
		this.systemFlag = SwtUtil.isEmptyOrNull(systemFlag) ? "N" : systemFlag;
	}
	
	
	
}
