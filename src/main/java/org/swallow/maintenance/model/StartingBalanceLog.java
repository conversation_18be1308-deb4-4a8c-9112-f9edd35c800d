/*
 * @(#)StartingBalanceLog.java 1.0 04/08/06
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
/*
 * Created on Aug 4, 2006
 *
 * TODO To change the template for this generated file go to
 * Window - Preferences - Java - Code Style - Code Templates
 */
package org.swallow.maintenance.model;

import org.swallow.model.BaseObject;

import java.util.Date;


/**
 * This is the Starting Balance Log bean class
 *
 * <AUTHOR> Arora
 * @version 1.0
 */
public class StartingBalanceLog extends BaseObject {
    /**
     * Log Sequence
     */
    private Long balanceLogSeq;

    /**
     * Host Id
     */
    private String hostId;

    /**
     * Entity Id
     */
    private String entityId;

    /**
     * Balance Type
     */
    private String balanceType;

    /**
     * Balance Date
     */
    private Date balanceDate;

    /**
     * Balance Date As String
     */
    private String balanceDateAsString;

    /**
     * Balance Identifier
     */
    private String balanceTypeId;

    /**
     * Identifier Name
     */
    private String balanceTypeName;
	/*
	 * Start: Commented by Arumugam for Mantis 997:Allow Internal and External
	 * forecasts to have independent SOD's
	 */
    /**
     * Old value Of Start Balance
    */
//    private Double oldStartBalance;

     /**
      * Old value Of Start Balance in Currency Format
      */
//    private String oldStartBalanceAsString;

     /**
      * New value Of Start Balance in Currency Format
      */
//    private String newStartBalanceAsString;

     /**
      * New value Of Start Balance
      */
//    private Double newStartBalance;
	/*
	 * End: Commented by Arumugam for Mantis 997:Allow Internal and External
	 * forecasts to have independent SOD's
	 */

    /**
	* Update User
	*/
    private String updateUser;

    /**
     *  Balance Source Id !
     */
    private String balanceSourceId;

    /**
     * Update Date
     */
    private Date updateDate;

    /**
     * Update Date As String
     */
    private String updateDateAsString;

    /**
     * Date in Time Format
     */
    private String updateTimeAsString;

    /**
     * DOCUMENT ME!
     */
    private boolean oldBalanceNegative;

    /**
     * DOCUMENT ME!
     */
    private boolean newBalanceNegative;
    
    /*Start:Variable added by Arumugam on 01-Sep-2009 for Mantis 997:Allow Internal and External forecasts to have independent SOD's */
    private Double workingForcastSODChange;
    private Double newWorkingForcastSOD;
    private String newWorkingForcastSODType;
    private Double bvForecastAdjustChange;
    private Double newBVForecastAdjust;
    private Long bvForecastAdjustLastId;
    private Double workingExternalSODChange;
    private Double newWorkingExternalSOD;
    private String newWorkingExternalSODType;
    private Double bvExternalAdjustChange;
    private Double newBVExternalAdjust;
    private Long bvExternalAdjustLastId;
    private String newWorkingForcastSODTypeAsString;
    private String newWorkingExternalSODTypeAsString;
    
    private String workingForcastSODChangeAsString;
    private String newWorkingForcastSODAsString;
    private String bvForecastAdjustChangeAsString;
    private String newBVForecastAdjustAsString;
    private String workingExternalSODChangeAsString;
    private String newWorkingExternalSODAsString;
    private String bvExternalAdjustChangeAsString;
    private String newBVExternalAdjustAsString;
    
    private boolean workingForcastSODChangeNegative;
    private boolean newWorkingForcastSODNegative;
    private boolean bvForecastAdjustChangeNegative;
    private boolean newBVForecastAdjustNegative;
    private boolean workingExternalSODChangeNegative;
    private boolean newWorkingExternalSODNegative;
    private boolean bvExternalAdjustChangeNegative;
    private boolean newBVExternalAdjustNegative;
    private String newInternalBalSource;
    private String newExternalBalSource;
    
    
    private Double suppliedExternalSODChange;
    private Double newSuppliedExternalSOD;
    private String suppliedExternalSODChangeAsString;
    private String newSuppliedExternalSODAsString;
    private boolean newSuppliedExternalSODNegative;
    private boolean suppliedExternalSODChangeNegative;
    
    private Double suppliedInternalSODChange;
    private Double newSuppliedInternalSOD;
    private String suppliedInternalSODChangeAsString;
    private String newSuppliedInternalSODAsString;
    private boolean newSuppliedInternalSODNegative;
    private boolean suppliedInternalSODChangeNegative;
    
//    private String newSuppliedExternalSODTypeAsString;
//    private String newSuppliedExternalSODType;
    
    /*End:Variable added by Arumugam on 01-Sep-2009 for Mantis 997:Allow Internal and External forecasts to have independent SOD's */
      
 

	/** Start:  Refer to Mail , Subject :- New column for P_BALANCE table , Dated : Tuesday, January 23**/
    private String balanceSource;

    /**
     * @return Returns the newBalanceNegative.
     */
    public boolean isNewBalanceNegative() {
        return newBalanceNegative;
    }

    /**
     * @param newBalanceNegative The newBalanceNegative to set.
     */
    public void setNewBalanceNegative(boolean newBalanceNegative) {
        this.newBalanceNegative = newBalanceNegative;
    }

    /**
     * @return Returns the oldBalanceNegative.
     */
    public boolean isOldBalanceNegative() {
        return oldBalanceNegative;
    }

    /**
     * @param oldBalanceNegative The oldBalanceNegative to set.
     */
    public void setOldBalanceNegative(boolean oldBalanceNegative) {
        this.oldBalanceNegative = oldBalanceNegative;
    }

    /**
     * @return Returns the balanceSource.
     */
    public String getBalanceSource() {
        return balanceSource;
    }

    /**
     * @param balanceSource The balanceSource to set.
     */
    public void setBalanceSource(String balanceSource) {
        this.balanceSource = balanceSource;
    }

    /** End :  Refer to Mail , Subject :- New column for P_BALANCE table , Dated : Tuesday, January 23**/
    /**
     * @return Returns the balanceDateAsString.
     */
    public String getBalanceDateAsString() {
        return balanceDateAsString;
    }

    /**
     * @param balanceDateAsString The balanceDateAsString to set.
     */
    public void setBalanceDateAsString(String balanceDateAsString) {
        this.balanceDateAsString = balanceDateAsString;
    }

    /**
     * @return Returns the balanceDate.
     */
    public Date getBalanceDate() {
        return balanceDate;
    }

    /**
     * @param balanceDate The balanceDate to set.
     */
    public void setBalanceDate(Date balanceDate) {
        this.balanceDate = balanceDate;
    }

    /**
     * @return Returns the balanceType.
     */
    public String getBalanceType() {
        return balanceType;
    }

    /**
     * @param balanceType The balanceType to set.
     */
    public void setBalanceType(String balanceType) {
        this.balanceType = balanceType;
    }

    /**
     * @return Returns the balanceTypeId.
     */
    public String getBalanceTypeId() {
        return balanceTypeId;
    }

    /**
     * @param balanceTypeId The balanceTypeId to set.
     */
    public void setBalanceTypeId(String balanceTypeId) {
        this.balanceTypeId = balanceTypeId;
    }

    /**
     * @return Returns the balanceLogSeq.
     */
    public Long getBalanceLogSeq() {
        return balanceLogSeq;
    }

    /**
     * @param balanceLogSeq The balanceLogSeq to set.
     */
    public void setBalanceLogSeq(Long balanceLogSeq) {
        this.balanceLogSeq = balanceLogSeq;
    }

    /**
     * @return Returns the entityId.
     */
    public String getEntityId() {
        return entityId;
    }

    /**
     * @param entityId The entityId to set.
     */
    public void setEntityId(String entityId) {
        this.entityId = entityId;
    }

    /**
     * @return Returns the hostId.
     */
    public String getHostId() {
        return hostId;
    }

    /**
     * @param hostId The hostId to set.
     */
    public void setHostId(String hostId) {
        this.hostId = hostId;
    }
	 /*
	  * Start: Commented by Arumugam for Mantis 997:Allow Internal and External
	  * forecasts to have independent SOD's
	  */
      /**
       * @return Returns the newStartBalance.
       */
//    public Double getNewStartBalance() {
//        return newStartBalance;
//    }
//
     /**
      * @param newStartBalance The newStartBalance to set.
      */
//    public void setNewStartBalance(Double newStartBalance) {
//        this.newStartBalance = newStartBalance;
//    }

    /**
     * @return Returns the oldStartBalance.
     */
//    public Double getOldStartBalance() {
//        return oldStartBalance;
//    }

     /**
      * @param oldStartBalance The oldStartBalance to set.
      */
//    public void setOldStartBalance(Double oldStartBalance) {
//        this.oldStartBalance = oldStartBalance;
//    }
	/*
	 * End: Commented by Arumugam for Mantis 997:Allow Internal and External
	 * forecasts to have independent SOD's
	 */

    /**
     * @return Returns the updateDate.
     */
    public Date getUpdateDate() {
        return updateDate;
    }

    /**
     * @param updateDate The updateDate to set.
     */
    public void setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
    }

    /**
     * @return Returns the updateUser.
     */
    public String getUpdateUser() {
        return updateUser;
    }

    /**
     * @param updateUser The updateUser to set.
     */
    public void setUpdateUser(String updateUser) {
        this.updateUser = updateUser;
    }

    /**
     * @return Returns the updateDateAsString.
     */
    public String getUpdateDateAsString() {
        return updateDateAsString;
    }

    /**
     * @param updateDateAsString The updateDateAsString to set.
     */
    public void setUpdateDateAsString(String updateDateAsString) {
        this.updateDateAsString = updateDateAsString;
    }

    /**
     * @return Returns the updateTimeAsString.
     */
    public String getUpdateTimeAsString() {
        return updateTimeAsString;
    }

    /**
     * @param updateTimeAsString The updateTimeAsString to set.
     */
    public void setUpdateTimeAsString(String updateTimeAsString) {
        this.updateTimeAsString = updateTimeAsString;
    }
	/*
	 * Start: Commented by Arumugam for Mantis 997:Allow Internal and External
	 * forecasts to have independent SOD's
	 */
     /**
      * @return Returns the newStartBalanceAsString.
      */
//    public String getNewStartBalanceAsString() {
//        return newStartBalanceAsString;
//    }

     /**
      * @param newStartBalanceAsString The newStartBalanceAsString to set.
      */
//    public void setNewStartBalanceAsString(String newStartBalanceAsString) {
//        this.newStartBalanceAsString = newStartBalanceAsString;
//    }

     /**
      * @return Returns the oldStartBalanceAsString.
      */
//    public String getOldStartBalanceAsString() {
//        return oldStartBalanceAsString;
//    }
//
     /**
      * @param oldStartBalanceAsString The oldStartBalanceAsString to set.
      */
//    public void setOldStartBalanceAsString(String oldStartBalanceAsString) {
//        this.oldStartBalanceAsString = oldStartBalanceAsString;
//    }
	/*
	 * End: Commented by Arumugam for Mantis 997:Allow Internal and External
	 * forecasts to have independent SOD's
	 */

    /**
     * @return Returns the balanceTypeName.
     */
    public String getBalanceTypeName() {
        return balanceTypeName;
    }

    /**
     * @param balanceTypeName The balanceTypeName to set.
     */
    public void setBalanceTypeName(String balanceTypeName) {
        this.balanceTypeName = balanceTypeName;
    }
	/**
	 * @return Returns the balanceSourceId.
	 */
	public String getBalanceSourceId() {
		return balanceSourceId;
	}
	/**
	 * @param balanceSourceId 
	 * The balanceSourceId to set.
	 */
	public void setBalanceSourceId(String balanceSourceId) {
		this.balanceSourceId = balanceSourceId;
	}
	/*Start:Added by Arumugam for Mantis 997:Allow Internal and External forecasts to have independent SOD's */
    /**
     * This is used to get working forecast SOD
     * 
     * @param none
     * @return Double
     */
	public Double getWorkingForcastSODChange() {
		return workingForcastSODChange;
	}
	/**
     * This is used to set working forecast SOD
     * 
     * @param workingForcastSODChange
     * @return none
     */
	public void setWorkingForcastSODChange(Double workingForcastSODChange) {
		this.workingForcastSODChange = workingForcastSODChange;
	}
	/**
     * This is used to get new working forecast SOD
     * 
     * @param 
     * @return Double
     */
	public Double getNewWorkingForcastSOD() {
		return newWorkingForcastSOD;
	}
	/**
     * This is used to set new working forecast SOD
     * 
     * @param workingForcastSODChange
     * @return none
     */
	public void setNewWorkingForcastSOD(Double newWorkingForcastSOD) {
		this.newWorkingForcastSOD = newWorkingForcastSOD;
	}
	/**
     * This is used to get new working forecast SOD type
     * 
     * @param 
     * @return String
     */
	public String getNewWorkingForcastSODType() {
		return newWorkingForcastSODType;
	}
	/**
     * This is used to set new working forecast SOD type
     * 
     * @param newWorkingForcastSODType
     * @return none
     */
	public void setNewWorkingForcastSODType(String newWorkingForcastSODType) {
		this.newWorkingForcastSODType = newWorkingForcastSODType;
	}
	/**
     * This is used to get Bv forecast Adjust change
     * 
     * @param 
     * @return Double
     */
	public Double getBvForecastAdjustChange() {
		return bvForecastAdjustChange;
	}
	/**
     * This is used to set new working forecast SOD type
     * 
     * @param bvForecastAdjustChange
     * @return none
     */
	public void setBvForecastAdjustChange(Double bvForecastAdjustChange) {
		this.bvForecastAdjustChange = bvForecastAdjustChange;
	}
	/**
     * This is used to get new Bv forecast Adjust change
     * 
     * @param 
     * @return Double
     */
	public Double getNewBVForecastAdjust() {
		return newBVForecastAdjust;
	}
	/**
     * This is used to set new working forecast SOD type
     * 
     * @param newBVForecastAdjust
     * @return none
     */
	public void setNewBVForecastAdjust(Double newBVForecastAdjust) {
		this.newBVForecastAdjust = newBVForecastAdjust;
	}
	/**
     * This is used to get new Bv forecast Adjust lastid
     * 
     * @param 
     * @return Long
     */
	public Long getBvForecastAdjustLastId() {
		return bvForecastAdjustLastId;
	}
	/**
     * This is used to set new Bv forecast Adjust lastid
     * 
     * @param bvForecastAdjustLastId
     * @return none
     */
	public void setBvForecastAdjustLastId(Long bvForecastAdjustLastId) {
		this.bvForecastAdjustLastId = bvForecastAdjustLastId;
	}
	/**
     * This is used to get working forecast external SOD change
     * 
     * @param 
     * @return Double
     */
	public Double getWorkingExternalSODChange() {
		return workingExternalSODChange;
	}
	/**
     * This is used to set working forecast external SOD change
     * 
     * @param workingExternalSODChange
     * @return none
     */
	public void setWorkingExternalSODChange(Double workingExternalSODChange) {
		this.workingExternalSODChange = workingExternalSODChange;
	}
	/**
     * This is used to get new working external SOD change
     * 
     * @param 
     * @return Double
     */
	public Double getNewWorkingExternalSOD() {
		return newWorkingExternalSOD;
	}
	/**
     * This is used to set new working external SOD change
     * 
     * @param newWorkingExternalSOD
     * @return none
     */
	public void setNewWorkingExternalSOD(Double newWorkingExternalSOD) {
		this.newWorkingExternalSOD = newWorkingExternalSOD;
	}
	
	/**
     * This is used to get new working external SOD type
     * 
     * @param 
     * @return String
     */
	public String getNewWorkingExternalSODType() {
		return newWorkingExternalSODType;
	}
	/**
     * This is used to set new working external SOD change
     * 
     * @param newWorkingExternalSODType
     * @return none
     */
	public void setNewWorkingExternalSODType(String newWorkingExternalSODType) {
		this.newWorkingExternalSODType = newWorkingExternalSODType;
	}
	/**
     * This is used to get BV external Adjust change
     * 
     * @param 
     * @return Double
     */
	public Double getBvExternalAdjustChange() {
		return bvExternalAdjustChange;
	}
	/**
     * This is used to set BV external Adjust change
     * 
     * @param bvExternalAdjustChange
     * @return none
     */
	public void setBvExternalAdjustChange(Double bvExternalAdjustChange) {
		this.bvExternalAdjustChange = bvExternalAdjustChange;
	}
	/**
     * This is used to get new BV external Adjust change
     * 
     * @param 
     * @return Double
     */
	public Double getNewBVExternalAdjust() {
		return newBVExternalAdjust;
	}
	/**
     * This is used to set new BV external Adjust change
     * 
     * @param newBVExternalAdjust
     * @return none
     */
	public void setNewBVExternalAdjust(Double newBVExternalAdjust) {
		this.newBVExternalAdjust = newBVExternalAdjust;
	}
	/**
     * This is used to get BV external Adjust Last id
     * 
     * @param 
     * @return Long
     */
	public Long getBvExternalAdjustLastId() {
		return bvExternalAdjustLastId;
	}
	/**
     * This is used to set BV external Adjust Last id
     * 
     * @param bvExternalAdjustLastId
     * @return none
     */
	public void setBvExternalAdjustLastId(Long bvExternalAdjustLastId) {
		this.bvExternalAdjustLastId = bvExternalAdjustLastId;
	}
	/**
     * This is used to get working forecast SOD type
     * 
     * @param 
     * @return String
     */
	public String getNewWorkingExternalSODTypeAsString() {
		return newWorkingExternalSODTypeAsString;
	}
	/**
     * This is used to set working forecast SOD type
     * 
     * @param bvExternalAdjustLastId
     * @return none
     */
	public void setNewWorkingExternalSODTypeAsString(
			String newWorkingExternalSODTypeAsString) {
		this.newWorkingExternalSODTypeAsString = newWorkingExternalSODTypeAsString;
	}
	/**
     * This is used to get new working forecast SOD type as string
     * 
     * @param 
     * @return String
     */
	public String getNewWorkingForcastSODTypeAsString() {
		return newWorkingForcastSODTypeAsString;
	}
	/**
     * This is used to set new working forecast SOD type as string
     * 
     * @param setNewWorkingForcastSODTypeAsString
     * @return none
     */
	public void setNewWorkingForcastSODTypeAsString(
			String newWorkingForcastSODTypeAsString) {
		this.newWorkingForcastSODTypeAsString = newWorkingForcastSODTypeAsString;
	}
	/**
     * This is used to get  working forecast SOD change as string
     * 
     * @param 
     * @return String
     */

	public String getWorkingForcastSODChangeAsString() {
		return workingForcastSODChangeAsString;
	}
	/**
     * This is used to set  working forecast SOD change as string
     * 
     * @param workingForcastSODChangeAsString
     * @return none
     */
	public void setWorkingForcastSODChangeAsString(
			String workingForcastSODChangeAsString) {
		this.workingForcastSODChangeAsString = workingForcastSODChangeAsString;
	}
	/**
     * This is used to get  working forecast SOD  as string
     * 
     * @param 
     * @return String
     */
	public String getNewWorkingForcastSODAsString() {
		return newWorkingForcastSODAsString;
	}
	/**
     * This is used to set  working forecast SOD  as string
     * 
     * @param newWorkingForcastSODAsString
     * @return none
     */

	public void setNewWorkingForcastSODAsString(String newWorkingForcastSODAsString) {
		this.newWorkingForcastSODAsString = newWorkingForcastSODAsString;
	}
	/**
     * This is used to get  BV forecast adjust change as String
     * 
     * @param 
     * @return String
     */

	public String getBvForecastAdjustChangeAsString() {
		return bvForecastAdjustChangeAsString;
	}
	/**
     * This is used to set  BV forecast adjust change as String
     * 
     * @param bvForecastAdjustChangeAsString
     * @return none
     */

	public void setBvForecastAdjustChangeAsString(
			String bvForecastAdjustChangeAsString) {
		this.bvForecastAdjustChangeAsString = bvForecastAdjustChangeAsString;
	}
	/**
     * This is used to get new BV forecast adjust  as String
     * 
     * @param 
     * @return String
     */
	public String getNewBVForecastAdjustAsString() {
		return newBVForecastAdjustAsString;
	}
	/**
     * This is used to set  new BV forecast adjust  as String
     * 
     * @param newBVForecastAdjustAsString
     * @return none
     */
	public void setNewBVForecastAdjustAsString(String newBVForecastAdjustAsString) {
		this.newBVForecastAdjustAsString = newBVForecastAdjustAsString;
	}
	/**
     * This is used to get working external SOD change  as String
     * 
     * @param 
     * @return String
     */
	public String getWorkingExternalSODChangeAsString() {
		return workingExternalSODChangeAsString;
	}
	/**
     * This is used to set  working external SOD change  as String
     * 
     * @param newBVForecastAdjustAsString
     * @return none
     */
	public void setWorkingExternalSODChangeAsString(
			String workingExternalSODChangeAsString) {
		this.workingExternalSODChangeAsString = workingExternalSODChangeAsString;
	}
	/**
     * This is used to get working external SOD   as String
     * 
     * @param 
     * @return String
     */
	public String getNewWorkingExternalSODAsString() {
		return newWorkingExternalSODAsString;
	}
	/**
     * This is used to set  working external SOD   as String
     * 
     * @param newWorkingExternalSODAsString
     * @return none
     */
	public void setNewWorkingExternalSODAsString(
			String newWorkingExternalSODAsString) {
		this.newWorkingExternalSODAsString = newWorkingExternalSODAsString;
	}
	/**
     * This is used to get Bv external change adjust   as String
     * 
     * @param 
     * @return String
     */
	public String getBvExternalAdjustChangeAsString() {
		return bvExternalAdjustChangeAsString;
	}
	/**
     * This is used to set  Bv external change adjust   as String
     * 
     * @param bvExternalAdjustChangeAsString
     * @return none
     */
	public void setBvExternalAdjustChangeAsString(
			String bvExternalAdjustChangeAsString) {
		this.bvExternalAdjustChangeAsString = bvExternalAdjustChangeAsString;
	}
	/**
     * This is used to get Bv external change adjust   as String
     * 
     * @param 
     * @return String
     */
	public String getNewBVExternalAdjustAsString() {
		return newBVExternalAdjustAsString;
	}
	/**
     * This is used to set  Bv external adjust   as String
     * 
     * @param bvExternalAdjustChangeAsString
     * @return none
     */
	public void setNewBVExternalAdjustAsString(String newBVExternalAdjustAsString) {
		this.newBVExternalAdjustAsString = newBVExternalAdjustAsString;
	}
	/**
     * This is used to check forecast SOD change as negative
     * 
     * @param 
     * @return String
     */
	public boolean isWorkingForcastSODChangeNegative() {
		return workingForcastSODChangeNegative;
	}
	/**
     * This is used to set  forecast SOD change as negative
     * 
     * @param bvExternalAdjustChangeAsString
     * @return none
     */
	public void setWorkingForcastSODChangeNegative(
			boolean workingForcastSODChangeNegative) {
		this.workingForcastSODChangeNegative = workingForcastSODChangeNegative;
	}
	/**
     * This is used to check working forecast SOD  as negative
     * 
     * @param 
     * @return String
     */
	public boolean isNewWorkingForcastSODNegative() {
		return newWorkingForcastSODNegative;
	}
	/**
     * This is used to set new  working forecast SOD  as negative
     * 
     * @param newWorkingForcastSODNegative
     * @return none
     */
	public void setNewWorkingForcastSODNegative(boolean newWorkingForcastSODNegative) {
		this.newWorkingForcastSODNegative = newWorkingForcastSODNegative;
	}
	/**
     * This is used to check Bv forecast adjust change  as negative
     * 
     * @param 
     * @return String
     */
	public boolean isBvForecastAdjustChangeNegative() {
		return bvForecastAdjustChangeNegative;
	}
	/**
     * This is used to set Bv forecast adjust change  as negative
     * 
     * @param bvForecastAdjustChangeNegative
     * @return none
     */
	public void setBvForecastAdjustChangeNegative(
			boolean bvForecastAdjustChangeNegative) {
		this.bvForecastAdjustChangeNegative = bvForecastAdjustChangeNegative;
	}
	/**
     * This is used to check new Bv forecast adjust change  as negative
     * 
     * @param 
     * @return String
     */
	public boolean isNewBVForecastAdjustNegative() {
		return newBVForecastAdjustNegative;
	}
	/**
     * This is used to set new Bv forecast adjust change  as negative
     * 
     * @param newBVForecastAdjustNegative
     * @return none
     */
	public void setNewBVForecastAdjustNegative(boolean newBVForecastAdjustNegative) {
		this.newBVForecastAdjustNegative = newBVForecastAdjustNegative;
	}
	/**
     * This is used to check working external SOD change  as negative
     * 
     * @param 
     * @return String
     */
	public boolean isWorkingExternalSODChangeNegative() {
		return workingExternalSODChangeNegative;
	}
	/**
     * This is used to set working external SOD change  as negative
     * 
     * @param newBVForecastAdjustNegative
     * @return none
     */
	public void setWorkingExternalSODChangeNegative(
			boolean workingExternalSODChangeNegative) {
		this.workingExternalSODChangeNegative = workingExternalSODChangeNegative;
	}

	public boolean isNewWorkingExternalSODNegative() {
		return newWorkingExternalSODNegative;
	}

	public void setNewWorkingExternalSODNegative(
			boolean newWorkingExternalSODNegative) {
		this.newWorkingExternalSODNegative = newWorkingExternalSODNegative;
	}

	public boolean isBvExternalAdjustChangeNegative() {
		return bvExternalAdjustChangeNegative;
	}

	public void setBvExternalAdjustChangeNegative(
			boolean bvExternalAdjustChangeNegative) {
		this.bvExternalAdjustChangeNegative = bvExternalAdjustChangeNegative;
	}

	public boolean isNewBVExternalAdjustNegative() {
		return newBVExternalAdjustNegative;
	}

	public void setNewBVExternalAdjustNegative(boolean newBVExternalAdjustNegative) {
		this.newBVExternalAdjustNegative = newBVExternalAdjustNegative;
	}
	

	public String getNewInternalBalSource() {
		return newInternalBalSource;
	}

	public void setNewInternalBalSource(String newInternalBalSource) {
		this.newInternalBalSource = newInternalBalSource;
	}

	public String getNewExternalBalSource() {
		return newExternalBalSource;
	}

	public void setNewExternalBalSource(String newExternalBalSource) {
		this.newExternalBalSource = newExternalBalSource;
	}
	/*End:Added by Arumugam for Mantis 997:Allow Internal and External forecasts to have independent SOD's */
	
	
	   public Double getSuppliedExternalSODChange() {
			return suppliedExternalSODChange;
		}

		public void setSuppliedExternalSODChange(Double suppliedExternalSODChange) {
			this.suppliedExternalSODChange = suppliedExternalSODChange;
		}

		public Double getNewSuppliedExternalSOD() {
			return newSuppliedExternalSOD;
		}

		public void setNewSuppliedExternalSOD(Double newSuppliedExternalSOD) {
			this.newSuppliedExternalSOD = newSuppliedExternalSOD;
		}

		public String getSuppliedExternalSODChangeAsString() {
			return suppliedExternalSODChangeAsString;
		}

		public void setSuppliedExternalSODChangeAsString(String suppliedExternalSODChangeAsString) {
			this.suppliedExternalSODChangeAsString = suppliedExternalSODChangeAsString;
		}

		public String getNewSuppliedExternalSODAsString() {
			return newSuppliedExternalSODAsString;
		}

		public void setNewSuppliedExternalSODAsString(String newSuppliedExternalSODAsString) {
			this.newSuppliedExternalSODAsString = newSuppliedExternalSODAsString;
		}

		public boolean isNewSuppliedExternalSODNegative() {
			return newSuppliedExternalSODNegative;
		}

		public void setNewSuppliedExternalSODNegative(boolean newSuppliedExternalSODNegative) {
			this.newSuppliedExternalSODNegative = newSuppliedExternalSODNegative;
		}

		public boolean isSuppliedExternalSODChangeNegative() {
			return suppliedExternalSODChangeNegative;
		}

		public void setSuppliedExternalSODChangeNegative(boolean suppliedExternalSODChangeNegative) {
			this.suppliedExternalSODChangeNegative = suppliedExternalSODChangeNegative;
		}

		public Double getSuppliedInternalSODChange() {
			return suppliedInternalSODChange;
		}

		public void setSuppliedInternalSODChange(Double suppliedInternalSODChange) {
			this.suppliedInternalSODChange = suppliedInternalSODChange;
		}

		public Double getNewSuppliedInternalSOD() {
			return newSuppliedInternalSOD;
		}

		public void setNewSuppliedInternalSOD(Double newSuppliedInternalSOD) {
			this.newSuppliedInternalSOD = newSuppliedInternalSOD;
		}

		public String getSuppliedInternalSODChangeAsString() {
			return suppliedInternalSODChangeAsString;
		}

		public void setSuppliedInternalSODChangeAsString(String suppliedInternalSODChangeAsString) {
			this.suppliedInternalSODChangeAsString = suppliedInternalSODChangeAsString;
		}

		public String getNewSuppliedInternalSODAsString() {
			return newSuppliedInternalSODAsString;
		}

		public void setNewSuppliedInternalSODAsString(String newSuppliedInternalSODAsString) {
			this.newSuppliedInternalSODAsString = newSuppliedInternalSODAsString;
		}

		public boolean isNewSuppliedInternalSODNegative() {
			return newSuppliedInternalSODNegative;
		}

		public void setNewSuppliedInternalSODNegative(boolean newSuppliedInternalSODNegative) {
			this.newSuppliedInternalSODNegative = newSuppliedInternalSODNegative;
		}

		public boolean isSuppliedInternalSODChangeNegative() {
			return suppliedInternalSODChangeNegative;
		}

		public void setSuppliedInternalSODChangeNegative(boolean suppliedInternalSODChangeNegative) {
			this.suppliedInternalSODChangeNegative = suppliedInternalSODChangeNegative;
		}
}
