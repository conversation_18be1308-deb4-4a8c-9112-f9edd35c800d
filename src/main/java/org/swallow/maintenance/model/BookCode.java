/*
 * @(#)BookCode.java  23/12/05
 * 
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 * 
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */

package org.swallow.maintenance.model;

import java.util.Date;
import java.util.Hashtable;

import org.swallow.model.BaseObject;


public class BookCode extends BaseObject implements org.swallow.model.AuditComponent {
	private Id id = new Id();
	private String updateUser;
	private Date updateDate = new Date() ;
	private String bookName;
	/* START: Code changed as par SRS - Currency Monitor for ING, 03-JUL-2007 */
	private String bookLocation;
	/* END: Code changed as par SRS - Currency Monitor for ING, 03-JUL-2007 */
	private String groupIdLevel1;
	private String groupIdLevel2;
	private String groupIdLevel3;

	public static Hashtable  logTable = new Hashtable();
	static {
		logTable.put("bookName","Book Name");
		/* START: Code changed as par SRS - Currency Monitor for ING, 03-JUL-2007 */
		logTable.put("bookLocation","Book Location");
		/* END: Code changed as par SRS - Currency Monitor for ING, 03-JUL-2007 */
		logTable.put("groupIdLevel1","Group Id Level 1");
		logTable.put("groupIdLevel2","Group Id Level 2");
		logTable.put("groupIdLevel3","Group Id Level 3");

	}

	public static class Id extends BaseObject{


		/**
		 * @return Returns the entityId.
		 */
		public String getEntityId() {
			return entityId;
		}
		/**
		 * @param entityId The entityId to set.
		 */
		public void setEntityId(String entityId) {
			this.entityId = entityId;
		}
		/**
		 * @return Returns the hostId.
		 */
		public String getHostId() {
			return hostId;
		}
		/**
		 * @param hostId The hostId to set.
		 */
		public void setHostId(String hostId) {
			this.hostId = hostId;
		}
		private String hostId ;
		private String entityId;
		private String bookCode;
		public Id() {
		}

		public Id(String hostId ,String entityId ,String bookCode ) {
			this.hostId = hostId;
			this.entityId = entityId;
			this.bookCode = bookCode;

		}
		/**
		 * @return Returns the bookCode.
		 */
		public String getBookCode() {
			return bookCode;
		}
		/**
		 * @param bookCode The bookCode to set.
		 */
		public void setBookCode(String bookCode) {
			this.bookCode = bookCode;
		}
	}

	/**
	 * @return Returns the id.
	 */
	public Id getId() {
		return id;
	}
	/**
	 * @param id The id to set.
	 */
	public void setId(Id id) {
		this.id = id;
	}
	/**
	 * @return Returns the updateDate.
	 */
	public Date getUpdateDate() {
		return updateDate;
	}
	/**
	 * @param updateDate The updateDate to set.
	 */
	public void setUpdateDate(Date updateDate) {
		this.updateDate = updateDate;
	}
	/**
	 * @return Returns the updateUser.
	 */
	public String getUpdateUser() {
		return updateUser;
	}
	/**
	 * @param updateUser The updateUser to set.
	 */
	public void setUpdateUser(String updateUser) {
		this.updateUser = updateUser;
	}


	/**
	 * @return Returns the groupIdLevel1.
	 */
	public String getGroupIdLevel1() {
		return groupIdLevel1;
	}
	/**
	 * @param groupIdLevel1 The groupIdLevel1 to set.
	 */
	public void setGroupIdLevel1(String groupIdLevel1) {
		this.groupIdLevel1 = groupIdLevel1;
	}
	/**
	 * @return Returns the groupIdLevel2.
	 */
	public String getGroupIdLevel2() {
		return groupIdLevel2;
	}
	/**
	 * @param groupIdLevel2 The groupIdLevel2 to set.
	 */
	public void setGroupIdLevel2(String groupIdLevel2) {
		this.groupIdLevel2 = groupIdLevel2;
	}
	/**
	 * @return Returns the groupIdLevel3.
	 */
	public String getGroupIdLevel3() {
		return groupIdLevel3;
	}
	/**
	 * @param groupIdLevel3 The groupIdLevel3 to set.
	 */
	public void setGroupIdLevel3(String groupIdLevel3) {
		this.groupIdLevel3 = groupIdLevel3;
	}
	/**
	 * @return Returns the bookName.
	 */
	public String getBookName() {
		return bookName;
	}
	/**
	 * @param bookName The bookName to set.
	 */
	public void setBookName(String bookName) {
		this.bookName = bookName;
	}
	/* START: Code changed as par SRS - Currency Monitor for ING, 03-JUL-2007 */
	/* @return Returns the bookLocation.
	 */
	public String getBookLocation() {
		return bookLocation;
	}
	/**
	 * @param bookLocation The bookLocation to set.
	 */
	public void setBookLocation(String bookLocation) {
		this.bookLocation = bookLocation;
	}
	/* END: Code changed as par SRS - Currency Monitor for ING, 03-JUL-2007 */
}
