<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE hibernate-mapping PUBLIC
        "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
        "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">

<hibernate-mapping>
    <class name="org.swallow.maintenance.model.BalMaintenanceMaster" table="P_BALANCE_MST">
		<composite-id name="id" class="org.swallow.maintenance.model.BalMaintenanceMaster$Id" unsaved-value="any">

        <key-property name="hostId" access="field" column="HOST_ID"/>
        <key-property name="entityId" access="field" column="ENTITY_ID" />
		<key-property name="balanceType" access="field" column="BALANCE_TYPE"/>
		<key-property name="balanceDate" access="field" column="BALANCE_DATE"/>
		
		</composite-id>
	
			
		
		<property name="updateDate" column="UPDATE_DATE" not-null="false"/>
		<property name="updateUser" column="UPDATE_USER" not-null="false"/>
		
					
    </class>
</hibernate-mapping>
