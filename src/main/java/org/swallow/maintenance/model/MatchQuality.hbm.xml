<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE hibernate-mapping PUBLIC
        "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
        "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">
<hibernate-mapping>
 <class name="org.swallow.maintenance.model.MatchQuality" table="P_MATCH_ACTION ">
  <composite-id class="org.swallow.maintenance.model.MatchQuality$Id"
   name="id" unsaved-value="any">
   <key-property name="hostId" type="java.lang.String" column="HOST_ID" length="12"/>
   <key-property name="entityId" type="java.lang.String"  column="ENTITY_ID" length="12"/>
   <key-property name="currencyCode" type="java.lang.String"  column="CURRENCY_CODE" length="3"/>
   <key-property name="posLevel" type="java.lang.Integer" column="POSITION_LEVEL" length="1"/>
 </composite-id>
	  <property name="matchQuaA" type="java.lang.String" column="MATCH_ACTION_A" length="1"/>
	  <property name="matchQuaB" type="java.lang.String" column="MATCH_ACTION_B" length="1"/>
	  <property name="matchQuaC" type="java.lang.String" column="MATCH_ACTION_C" length="1"/>
	  <property name="matchQuaD" type="java.lang.String" column="MATCH_ACTION_D" length="1"/>
	  <property name="matchQuaE" type="java.lang.String" column="MATCH_ACTION_E" length="1"/>
	  <property name="updateDate" type="java.sql.Timestamp" column="UPDATE_DATE" length="7"/>
	  <property name="updateUser" type="java.lang.String" column="UPDATE_USER" length="15"/>
 </class>
</hibernate-mapping>
