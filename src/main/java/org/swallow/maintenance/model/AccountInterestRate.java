/*
 * @(#)AccountInterestRate.java 1.0 
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */

package org.swallow.maintenance.model;

import java.util.Date;
import java.util.Hashtable;

import org.swallow.model.BaseObject;

/**
 * AccountInterestRate class is used to handle the credit and debit margin rates
 * in the account
 * 
 * <AUTHOR>
 * 
 */
public class AccountInterestRate extends BaseObject implements
		org.swallow.model.AuditComponent {

	// Start :Code modified by Prasenjit Maji For Mantis 1690 on 30/08/2012:
	// Account Maintenance: Implement maintenance logging for account interest
	// rates.
	// To hold short name (key) and its description (value)
	public static Hashtable<String, String> logTable = new Hashtable<String, String>();

	/**
	 * Static block puts short name and its description in a map
	 */
	static {
		logTable.put("creditRate", "Credit Margin");
		logTable.put("overdraftRate", "Debit Margin");
		logTable.put("updateDate", "Update Date");
		logTable.put("updateUser", "Update User");
	}
	// End :Code modified by Prasenjit Maji For Mantis 1690 on 30/08/2012:
	// Account Maintenance: Implement maintenance logging for account interest
	// rates.
	// credit rate of account
	private String creditRate = null;
	// over draft rate of account
	private String overdraftRate = null;
	// Account interest rate Updated date
	private Date updateDate = null;
	// Account interest rate Updated user
	private String updateUser = null;
	// Account interest rate Updated date as string type
	private String updateDateAsString = null;
	// Interest rate on specific date
	private String interestDateRateAsString = null;
	// Composite key for identification
	private Id id = new Id();

	public static class Id extends BaseObject {
		// Host Id of the application
		private String hostId = null;
		// Entity Id
		private String entityId = null;
		// Account ID
		private String accountId = null;
		// Interest rate on specific date
		private Date interestDateRate = null;

		// Default Constructor of Id
		public Id() {
		}

		/**
		 * Constructor to create id object
		 * 
		 * @param hostId
		 * @param entityId
		 * @param accountId
		 * @param interestDateRate
		 */
		public Id(String hostId, String entityId, String accountId,
				Date interestDateRate) {
			this.hostId = hostId;
			this.entityId = entityId;
			this.accountId = accountId;
			this.interestDateRate = interestDateRate;
		}

		/**
		 * @return Returns the entityId.
		 */
		public String getEntityId() {
			return entityId;
		}

		/**
		 * @param entityId
		 *            The entityId to set.
		 */
		public void setEntityId(String entityId) {
			this.entityId = entityId;
		}

		/**
		 * @return Returns the hostId.
		 */
		public String getHostId() {
			return hostId;
		}

		/**
		 * @param hostId
		 *            The hostId to set.
		 */
		public void setHostId(String hostId) {
			this.hostId = hostId;
		}

		/**
		 * @return Returns the accountId.
		 */
		public String getAccountId() {
			return accountId;
		}

		/**
		 * @param accountId
		 *            The accountId to set.
		 */
		public void setAccountId(String accountId) {
			this.accountId = accountId;
		}

		/**
		 * @return interestDateRate
		 */
		public Date getInterestDateRate() {
			return interestDateRate;
		}

		/**
		 * @param interestDateRate
		 *            The interestDateRate to set.
		 */
		public void setInterestDateRate(Date interestDateRate) {
			this.interestDateRate = interestDateRate;
		}
	}

	/**
	 * @return Returns the id.
	 */
	public Id getId() {
		return id;
	}

	/**
	 * @param id
	 *            The id to set.
	 */
	public void setId(Id id) {
		this.id = id;
	}

	/**
	 * @return Returns the updateDate.
	 */
	public Date getUpdateDate() {
		return updateDate;
	}

	/**
	 * @param updateDate
	 *            The updateDate to set.
	 */
	public void setUpdateDate(Date updateDate) {
		this.updateDate = updateDate;
	}

	/**
	 * @return Returns the updateUser.
	 */
	public String getUpdateUser() {
		return updateUser;
	}

	/**
	 * @param updateUser
	 *            The updateUser to set.
	 */
	public void setUpdateUser(String updateUser) {
		this.updateUser = updateUser;
	}

	/**
	 * @return Returns the creditRate.
	 */
	public String getCreditRate() {
		return creditRate;
	}

	/**
	 * @param creditRate
	 *            The creditRate to set.
	 */
	public void setCreditRate(String creditRate) {
		this.creditRate = creditRate;
	}

	/**
	 * @return Returns the overdraftRate.
	 */
	public String getOverdraftRate() {
		return overdraftRate;
	}

	/**
	 * @param overdraftRate
	 *            The overdraftRate to set.
	 */
	public void setOverdraftRate(String overdraftRate) {
		this.overdraftRate = overdraftRate;
	}

	/**
	 * @return Returns the interestDateRateAsString.
	 */
	public String getInterestDateRateAsString() {
		return interestDateRateAsString;
	}

	/**
	 * @param interestDateRateAsString
	 *            The interestDateRateAsString to set.
	 */
	public void setInterestDateRateAsString(String interestDateRateAsString) {
		this.interestDateRateAsString = interestDateRateAsString;
	}

	/**
	 * @return Returns the updateDateAsString.
	 */
	public String getUpdateDateAsString() {
		return updateDateAsString;
	}

	/**
	 * @param updateDateAsString
	 *            The updateDateAsString to set.
	 */
	public void setUpdateDateAsString(String updateDateAsString) {
		this.updateDateAsString = updateDateAsString;
	}
}
