package org.swallow.maintenance.model;

import java.util.Date;
import java.util.HashMap;
import java.util.Hashtable;
import java.util.Map;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.swallow.model.BaseObject;
import org.swallow.util.SwtUtil;

public class EntityPositionLevel extends BaseObject implements org.swallow.model.AuditComponent {
	private Id id = new Id();
	private String positionLevelName ;
	private String indicator;	
	private Date updateDate;
	private String updateUser;
	
	// Start of Attributes required for Open Queue Selection Screen 
	private String movementTotalAsString;	
	private String currencyCode;
	private String currencyName;
	private String date;
	private Map<String,String> summaryTotal;
	// Added for Excluded Outstanding 
	private Long movementTotal;
	public static Hashtable  logTable = new Hashtable();
	static {
		logTable.put("positionLevelName","Position Level Name");
		logTable.put("indicator","Internal/External Indicator");
	}
    private final Log log = LogFactory.getLog(EntityPositionLevel.class);

	public static class Id extends BaseObject{
		private String hostId;
		private String entityId;
		private Integer positionLevel;		

		public Id() {}

		public Id(String hostId, String entityId,Integer positionLevel) {
			this.hostId = hostId;
			this.entityId = entityId;
			this.positionLevel = positionLevel;
		}
		public String getEntityId() {
			return entityId;
		}
		public void setEntityId(String entityId) {
			this.entityId = entityId;
		}
		public String getHostId() {
			return hostId;
		}
		public void setHostId(String hostId){
			this.hostId = hostId;
		}
		/**
		 * @return Returns the positionLevel.
		 */
		public Integer getPositionLevel() {
			return positionLevel;
		}
		/**
		 * @param positionLevel The positionLevel to set.
		 */
		public void setPositionLevel(Integer positionLevel) {
			this.positionLevel = positionLevel;
		}
	}

	
	public void setId(Id id){
		this.id = id;
		}
	public Id getId(){
		return id;
		}

	
	/**
	 * @return Returns the updateDate.
	 */
	public Date getUpdateDate() {
		return updateDate;
	}
	/**
	 * @param updateDate The updateDate to set.
	 */
	public void setUpdateDate(Date updateDate) {
		this.updateDate = updateDate;
	}
	/**
	 * @return Returns the updateUser.
	 */
	public String getUpdateUser() {
		return updateUser;
	}
	/**
	 * @param updateUser The updateUser to set.
	 */
	public void setUpdateUser(String updateUser) {
		this.updateUser = updateUser;
	}
	
	/**
	 * @return Returns the positionLevelName.
	 */
	public String getPositionLevelName() {
		return positionLevelName;
	}
	/**
	 * @param positionLevelName The positionLevelName to set.
	 */
	public void setPositionLevelName(String positionLevelName) {
		this.positionLevelName = positionLevelName;
	}
	/**
	 * @return Returns the indicator.
	 */
	public String getIndicator() {
		return indicator;
	}
	/**
	 * @param indicator The indicator to set.
	 */
	public void setIndicator(String indicator) {
		this.indicator = indicator;
	}
	
	
	public void onAdd(){
		log.debug("entering 'onAdd' method");		
		EntityTO entityTo = new EntityTO(getId().entityId);
		Object obj = SwtUtil.getSwtMaintenanceCache().remove(entityTo);
		log.debug("Object removed from cache - " + obj);
	}
	public void onUpdate(Object oldObject, Object newObject){
		log.debug("entering 'onUpdate' method");		
		EntityTO entityTo = new EntityTO(getId().entityId);
		Object obj = SwtUtil.getSwtMaintenanceCache().remove(entityTo);
		log.debug("Object removed from cache - " + obj);		
	}
	public void onDelete(){
		log.debug("entering 'onDelete' method");		
		EntityTO entityTo = new EntityTO(getId().entityId);
		Object obj = SwtUtil.getSwtMaintenanceCache().remove(entityTo);
		log.debug("Object removed from cache - " + obj);
		
	}
	/**
	 * @return
	 */
	public String getMovementTotalAsString() {
		return movementTotalAsString;
	}
	/**
	 * @param movementTotalAsString
	 */
	public void setMovementTotalAsString(String movementTotalAsString) {
		this.movementTotalAsString = movementTotalAsString;
	}
	public static Hashtable getLogTable() {
		return logTable;
	}
	public static void setLogTable(Hashtable logTable) {
		EntityPositionLevel.logTable = logTable;
	}
	
	/**
	 * @return
	 */
	public String getCurrencyCode() {
		return currencyCode;
	}
	/**
	 * @param currencyCode
	 */
	public void setCurrencyCode(String currencyCode) {
		this.currencyCode = currencyCode;
	}
	/**
	 * @return
	 */
	public String getCurrencyName() {
		return currencyName;
	}
	/**
	 * @param currencyName
	 */
	public void setCurrencyName(String currencyName) {
		this.currencyName = currencyName;
	}
	/**
	 * @return
	 */
	public String getDate() {
		return date;
	}
	/**
	 * @param date
	 */
	public void setDate(String date) {
		this.date = date;
	}
	/**
	 * @return
	 */
	public Map getSummaryTotal() {
		summaryTotal = new HashMap();
		summaryTotal.put("entityId", id.entityId);
		summaryTotal.put("posLvlId", ""+id.positionLevel);
		//summaryTotal.put("posLvlName", positionLevelName);
		summaryTotal.put("currencyCode", currencyCode);
		//summaryTotal.put("currencyName", currencyName);		
		summaryTotal.put("date", date);	
		return summaryTotal;
	}
	/**
	 * @param summaryTotal
	 */
	public void setSummaryTotal(Map summaryTotal) {
		this.summaryTotal = summaryTotal;
	}
	
	private String summaryTotalUrlParams = new String();
	public String getSummaryTotalUrlParams() {
		getSummaryTotal();
		summaryTotalUrlParams =  summaryTotal.entrySet().stream()
		          .map(p -> p.getKey() + "=" + p.getValue())
		          .reduce((p1, p2) -> p1 + "&" + p2)
		          .map(s -> "" + s)
		          .orElse("");
		return summaryTotalUrlParams;
	}
	/**
	 * @return Returns the movementTotal.
	 */
	public Long getMovementTotal() {
		return movementTotal;
	}
	/**
	 * @param movementTotal The movementTotal to set.
	 */
	public void setMovementTotal(Long movementTotal) {
		this.movementTotal = movementTotal;
	}
}
