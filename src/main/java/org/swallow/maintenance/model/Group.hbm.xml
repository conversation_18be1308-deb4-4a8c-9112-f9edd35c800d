<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE hibernate-mapping PUBLIC
        "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
        "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">

<hibernate-mapping>
    <class name="org.swallow.maintenance.model.Group" table="P_GROUP ">
		<composite-id name="id" class="org.swallow.maintenance.model.Group$Id" unsaved-value="any">
		<key-property name="hostId" access="field" column="HOST_ID"/>
		<key-property name="entityId" access="field" column="ENTITY_ID" />        
		<key-property name="groupId" access="field" column="GROUP_ID"/>
		</composite-id>
		<property name="groupName" column="GROUP_NAME" not-null="true"/>	
		<property name="groupLvlCode" column="GROUP_LEVEL" not-null="false"/>	
		<property name="mgroupId" column="METAGROUP_ID" not-null="false"/>		
		<property name="cutoffOffset" column="CUT_OFF_OFFSET" not-null="false"/>	
		<property name="updateDate" column="UPDATE_DATE" not-null="false"/>	
		<property name="updateUser" column="UPDATE_USER" not-null="false"/>
   
    </class>
</hibernate-mapping>
