/**
 * Created on July 5, 2010
 */
package org.swallow.maintenance.model;

import java.util.Hashtable;

import org.swallow.model.BaseObject;

/**
 * This class is used to hold the Correspondent Account screen details.
 * 
 * <AUTHOR>
 */
public class CorrespondentAcct extends BaseObject implements
		org.swallow.model.AuditComponent {
	public static String className = "CorrespondentAcct";
	private static final long serialVersionUID = 1L;

	private String accountId;
	private String accountName = null;
	private AccountMaster accountMaster = null;
	private String otherMessageType = null;
	private String searchMessageType = "P";
	private int rowCount;
	private int currentPageNo;
	private int maxPages;
	private Id id = new Id();

	public static Hashtable logTable = new Hashtable();
	static {
		logTable.put("messageType", "Message Type");
		logTable.put("currencyCode", "Currency Code");
		logTable.put("accountId", "Account Id");
		logTable.put("accountName", "Account Name");
		logTable.put("corresAccId", "Correspondent Acc Id");
	}

	public static class Id extends BaseObject {
		private String hostId;
		private String entityId;
		private String messageType;
		private String currencyCode;
		private String corresAccId;

		public Id() {
		}

		public Id(String hostId, String entityId, String messageType,
				String corresAccId) {
			this.hostId = hostId;
			this.entityId = entityId;
			this.messageType = messageType;
			this.corresAccId = corresAccId;
		}

		/**
		 * @return the hostId
		 */
		public String getHostId() {
			return hostId;
		}

		/**
		 * @param hostId
		 *            the hostId to set
		 */
		public void setHostId(String hostId) {
			this.hostId = hostId;
		}

		/**
		 * @return the entityId
		 */
		public String getEntityId() {
			return entityId;
		}

		/**
		 * @param entityId
		 *            the entityId to set
		 */
		public void setEntityId(String entityId) {
			this.entityId = entityId;
		}

		/**
		 * @return the messageType
		 */
		public String getMessageType() {
			return messageType;
		}

		/**
		 * @param messageType
		 *            the messageType to set
		 */
		public void setMessageType(String messageType) {
			this.messageType = messageType;
		}

		/**
		 * @return the currencyCode
		 */
		public String getCurrencyCode() {
			return currencyCode;
		}

		/**
		 * @param currencyCode
		 *            the currencyCode to set
		 */
		public void setCurrencyCode(String currencyCode) {
			this.currencyCode = currencyCode;
		}

		/**
		 * @return the corresAccId
		 */
		public String getCorresAccId() {
			return corresAccId;
		}

		/**
		 * @param corresAccId
		 *            the corresAccId to set
		 */
		public void setCorresAccId(String corresAccId) {
			this.corresAccId = corresAccId;
		}
	}

	/**
	 * @return the accountId
	 */
	public String getAccountId() {
		return accountId;
	}

	/**
	 * @param accountId
	 *            the accountId to set
	 */
	public void setAccountId(String accountId) {
		this.accountId = accountId;
	}

	/**
	 * @return the accountName
	 */
	public String getAccountName() {
		return accountName;
	}

	/**
	 * @param accountName
	 *            the accountName to set
	 */
	public void setAccountName(String accountName) {
		this.accountName = accountName;
	}

	/**
	 * @return the id
	 */
	public Id getId() {
		return id;
	}

	/**
	 * @param id
	 *            the id to set
	 */
	public void setId(Id id) {
		this.id = id;
	}

	/**
	 * @return the accountMaster
	 */
	public AccountMaster getAccountMaster() {
		return accountMaster;
	}

	/**
	 * @param accountMaster
	 *            the accountMaster to set
	 */
	public void setAccountMaster(AccountMaster accountMaster) {
		this.accountMaster = accountMaster;
	}

	/**
	 * @return the otherMessageType
	 */
	public String getOtherMessageType() {
		return otherMessageType;
	}

	/**
	 * @param otherMessageType
	 *            the otherMessageType to set
	 */
	public void setOtherMessageType(String otherMessageType) {
		this.otherMessageType = otherMessageType;
	}

	/**
	 * @return the searchMessageType
	 */
	public String getSearchMessageType() {
		return searchMessageType;
	}

	/**
	 * @param searchMessageType
	 *            the searchMessageType to set
	 */
	public void setSearchMessageType(String searchMessageType) {
		this.searchMessageType = searchMessageType;
	}

	public int getRowCount() {
		return rowCount;
	}

	public void setRowCount(int rowCount) {
		this.rowCount = rowCount;
	}

	public int getCurrentPageNo() {
		return currentPageNo;
	}

	public void setCurrentPageNo(int currentPageNo) {
		this.currentPageNo = currentPageNo;
	}

	public int getMaxPages() {
		return maxPages;
	}

	public void setMaxPages(int maxPages) {
		this.maxPages = maxPages;
	}

}