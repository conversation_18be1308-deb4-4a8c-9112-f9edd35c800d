<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE hibernate-mapping PUBLIC
        "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
        "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">

<hibernate-mapping>
    <class name="org.swallow.maintenance.model.ILMScenario" table="P_ILM_SCENARIO" >
		<composite-id class="org.swallow.maintenance.model.ILMScenario$Id" name="id" unsaved-value="any">
		   <key-property name="ilmScenarioId" access="field" column="ILM_SCENARIO_ID"/>
		</composite-id>
		<property name="ilmScenarioName" column="ILM_SCENARIO_NAME" not-null="false"/>
		<property name="ilmScenarioDescription" column="ILM_SCENARIO_DESCRIPTION" not-null="false"/>
		<property name="hostId" column="HOST_ID" not-null="false"/>
		<property name="entityId" column="ENTITY_ID" not-null="false"/>
		<property name="currencyCode" column="CURRENCY_CODE" not-null="false"/>
		<property name="filterCondition" column="FILTER_CONDITION" not-null="false"/>
		<property name="exclusionCondition" column="EXCLUSION_CONDITION" not-null="false"/>
		<property name="publicPrivate" column="PUBLIC_PRIVATE" not-null="false"/>
		
		
		<property name="creditSuccessRate" column="CREDIT_SUCCESS_RATE" not-null="true"/>
		<property name="creditPctDelayed" column="CREDIT_PCT_DELAYED" not-null="true"/>
		<property name="creditDelayTime" column="CREDIT_DELAY_TIME" not-null="true"/>
		<property name="debitSuccessRate" column="DEBIT_SUCCESS_RATE" not-null="true"/>
		<property name="debitPctDelayed" column="DEBIT_PCT_DELAYED" not-null="true"/>
		<property name="debitDelayTime" column="DEBIT_DELAY_TIME" not-null="true"/>
		<property name="txnSetId" column="TXN_SET_ID" not-null="false"/>
		<property name="createdByUser" column="CREATED_BY_USER" not-null="false"/>
		<property name="createDate" column="CREATE_DATE" not-null="false"/>
		<property name="updateDate" column="UPDATE_DATE" not-null="false"/>
		<property name="nonScnCreditSuccessRate" column="NON_SCN_CREDIT_SUCCESS_RATE" not-null="true"/>
		<property name="nonScnDebitSuccessRate" column="NON_SCN_DEBIT_SUCCESS_RATE" not-null="true"/>
		<property name="defaultLegendText" column="DEFAULT_LEGEND_TEXT" not-null="true"/>
		<property name="allowReporting" column="ALLOW_REPORTING" not-null="true"/>
		<property name="collateralAvlbl" column="COLLATERAL_AVLBL" not-null="true"/>
		<property name="creditlineAvlbl" column="CREDITLINE_AVLBL" not-null="true"/>
		<property name="unencumberedLiqAssetAvlbl" column="UNENCUMBERED_LIQ_ASSET_AVLBL" not-null="true"/>
		<property name="otherSourcesAvlbl" column="OTHER_SOURCES_AVLBL" not-null="true"/>
		<property name="dynamicTsetQuery" column="DYNAMIC_TSET_QUERY" not-null="false"/>
		<property name="activeScenario" column="ACTIVE_SCENARIO" not-null="true"/>
		<property name="systemScenario" column="SYSTEM_SCENARIO" not-null="true"/>
       <property name="throughputMonitor" column="THROUGHPUT_MONITOR" not-null="true"/>
		
<property name="joinMvt" column="MOVEMENT_EXT_JOIN" not-null="true"/>
		
		
		
		
<!-- 		<set name="ILMCcyParameters" table="P_ILM_CCY_PARAMETERS" 	inverse="true" lazy="true"> -->
<!--             <key> -->
<!--                 <column name="GLOBAL_GROUP_ID" not-null="true" /> -->
<!--             </key> -->
<!--             <one-to-many class="org.swallow.maintenance.model.ILMCcyParameters" /> -->
<!--         </set> -->
<!-- 		<set name="accountsInGroup" table="P_ILM_ACC_IN_GROUP" 	inverse="true" lazy="true"> -->
<!--             <key> -->
<!--                 <column name="ILM_GROUP_ID" not-null="true" /> -->
<!--             </key> -->
<!--             <one-to-many class="org.swallow.maintenance.model.ILMAccountInGroups" /> -->
<!--         </set> -->
	</class>
</hibernate-mapping>
