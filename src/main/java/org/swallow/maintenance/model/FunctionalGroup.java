package org.swallow.maintenance.model;

import java.util.Hashtable;

import org.swallow.model.BaseObject;

public class FunctionalGroup extends BaseObject implements org.swallow.model.AuditComponent{


	private static final long serialVersionUID = 1L;
	private Id id = new Id();
	private String restrictType;
	private String functionalGroupDesc;
	
	public static Hashtable logTable = new Hashtable();
	static {
		logTable.put("restrictType", "restrictType");
		logTable.put("functionalGroupDesc", "functionalGroupDesc");
	}
	
	public static class Id extends BaseObject{


		private static final long serialVersionUID = 1L;
		private String functionalGroup = null;
		
		public Id() {}
		
		public Id(String functionalGroup) {
			this.functionalGroup = functionalGroup;
		}
		
		public String getFunctionalGroup() {
			return functionalGroup;
		}

		public void setFunctionalGroup(String functionalGroup) {
			this.functionalGroup = functionalGroup;
		}
		
	}
	
	public Id getId() {
		return id;
	}
	public void setId(Id id) {
		this.id = id;
	}
	
	public String getRestrictType() {
		return restrictType;
	}
	public void setRestrictType(String restrictType) {
		this.restrictType = restrictType;
	}
	
	public String getFunctionalGroupDesc() {
		return functionalGroupDesc;
	}

	public void setFunctionalGroupDesc(String functionalGroupDesc) {
		this.functionalGroupDesc = functionalGroupDesc;
	}	
}
