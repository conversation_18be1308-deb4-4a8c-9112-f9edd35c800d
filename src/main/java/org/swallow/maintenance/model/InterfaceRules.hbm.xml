<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE hibernate-mapping PUBLIC
        "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
        "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">

<hibernate-mapping>
    <class name="org.swallow.maintenance.model.InterfaceRule" table="I_INTERFACE_RULES">
		<composite-id class="org.swallow.maintenance.model.InterfaceRule$Id" name="id" unsaved-value="any">
		 	 	<key-property name="messageType" access="field" column="MESSAGE_TYPE"/>
		  	 	<key-property name="ruleId" access="field" column="RULE_ID"/>
		  	 	<key-property name="ruleKey" access="field" column="RULE_KEY"/>
		</composite-id>
		
		<property name="ruleValue" column="RULE_VALUE" not-null="false" />
		
    </class>
</hibernate-mapping>
