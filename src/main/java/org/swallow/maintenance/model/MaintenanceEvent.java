package org.swallow.maintenance.model;

import org.swallow.model.AuditComponent;
import java.lang.*;
import java.util.*;
import org.swallow.model.BaseObject;

import java.util.Hashtable;
import java.util.Date;

public class MaintenanceEvent extends BaseObject implements AuditComponent {

	private static final long serialVersionUID = 1L;
	public static Hashtable logTable = new Hashtable();
	private String maintFacilityId;
	private String recordId;
	private String requestUser;
	private Date requestDate;
	private String authUser;
	private Date authDate;
	private Long prevId;
	private Long nextId;
	private Long maintEventId;
	private String action;
	private String status;
	
	public MaintenanceEvent() {
		// TODO Auto-generated constructor stub
	}
	
	
	
	public MaintenanceEvent(String maintFacilityId, String recordId, String requestUser, Date requestDate,
			String authUser, Date authDate, Long prevId, Long nextId, Long maintEventId, String action,
			String status) {
		this.maintFacilityId = maintFacilityId;
		this.recordId = recordId;
		this.requestUser = requestUser;
		this.requestDate = requestDate;
		this.authUser = authUser;
		this.authDate = authDate;
		this.prevId = prevId;
		this.nextId = nextId;
		this.maintEventId = maintEventId;
		this.action = action;
		this.status = status;
	}



	public Long getMaintEventId() {
		return maintEventId;
	}

	public void setMaintEventId(Long maintEventId) {
		this.maintEventId = maintEventId;
	}
	
	public String getMaintFacilityId() {
		return maintFacilityId;
	}

	public void setMaintFacilityId(String maintFacilityId) {
		this.maintFacilityId = maintFacilityId;
	}

	public String getRecordId() {
		return recordId;
	}

	public void setRecordId(String recordId) {
		this.recordId = recordId;
	}

	public String getRequestUser() {
		return requestUser;
	}

	public void setRequestUser(String requestUser) {
		this.requestUser = requestUser;
	}

	public Date getRequestDate() {
		return requestDate;
	}

	public void setRequestDate(Date requestDate) {
		this.requestDate = requestDate;
	}

	public String getAuthUser() {
		return authUser;
	}

	public void setAuthUser(String authUser) {
		this.authUser = authUser;
	}

	public Date getAuthDate() {
		return authDate;
	}

	public void setAuthDate(Date authDate) {
		this.authDate = authDate;
	}

	public Long getPrevId() {
		return prevId;
	}

	public void setPrevId(Long prevId) {
		this.prevId = prevId;
	}

	public Long getNextId() {
		return nextId;
	}

	public void setNextId(Long nextId) {
		this.nextId = nextId;
	}

	public String getAction() {
		return action;
	}

	public void setAction(String action) {
		this.action = action;
	}

	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

}