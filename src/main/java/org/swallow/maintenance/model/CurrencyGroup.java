/*
 * @(#)CurrencyGroup.java  01/08/06
 * 
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 * 
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */

package org.swallow.maintenance.model;

import java.util.Date;
import java.util.Hashtable;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.swallow.model.BaseObject;
import org.swallow.util.SwtUtil;


public class CurrencyGroup extends BaseObject implements org.swallow.model.AuditComponent {
	
	private Id id = new Id();	
	private String currencyGroupName;
	private String noOfCurrencies; 
	private Date updateDate ;
	private String updateUser;
	
    private final Log log = LogFactory.getLog(CurrencyGroup.class);
    
	public static Hashtable  logTable = new Hashtable();
	static {
		logTable.put("currencyGroupName","Currency Group Name");
	}
	
	public static class Id extends BaseObject{
		
		private String hostId;
		private String entityId;
		private String currencyGroupId;		
		
		
		/**
		 * @return Returns the currencyGroupId.
		 */
		public String getCurrencyGroupId() {
			return currencyGroupId;
		}
		/**
		 * @param currencyGroupId The currencyGroupId to set.
		 */
		public void setCurrencyGroupId(String currencyGroupId) {
			this.currencyGroupId = currencyGroupId;
		}
		/**
		 * @return Returns the entityId.
		 */
		public String getEntityId() {
			return entityId;
		}
		/**
		 * @param entityId The entityId to set.
		 */
		public void setEntityId(String entityId) {
			this.entityId = entityId;
		}
		/**
		 * @return Returns the hostId.
		 */
		public String getHostId() {
			return hostId;
		}
		/**
		 * @param hostId The hostId to set.
		 */
		public void setHostId(String hostId) {
			this.hostId = hostId;
		}
	}
	
	
	/**
	 * @return Returns the currencyGroupName.
	 */
	public String getCurrencyGroupName() {
		return currencyGroupName;
	}
	/**
	 * @param currencyGroupName The currencyGroupName to set.
	 */
	public void setCurrencyGroupName(String currencyGroupName) {
		this.currencyGroupName = currencyGroupName;
	}
	/**
	 * @return Returns the id.
	 */
	public Id getId() {
		return id;
	}
	/**
	 * @param id The id to set.
	 */
	public void setId(Id id) {
		this.id = id;
	}
	/**
	 * @return Returns the noOfCurrencies.
	 */
	public String getNoOfCurrencies() {
		return noOfCurrencies;
	}
	/**
	 * @param noOfCurrencies The noOfCurrencies to set.
	 */
	public void setNoOfCurrencies(String noOfCurrencies) {
		this.noOfCurrencies = noOfCurrencies;
	}
	/**
	 * @return Returns the updateDate.
	 */
	public Date getUpdateDate() {
		return updateDate;
	}
	/**
	 * @param updateDate The updateDate to set.
	 */
	public void setUpdateDate(Date updateDate) {
		this.updateDate = updateDate;
	}
	/**
	 * @return Returns the updateUser.
	 */
	public String getUpdateUser() {
		return updateUser;
	}
	/**
	 * @param updateUser The updateUser to set.
	 */
	public void setUpdateUser(String updateUser) {
		this.updateUser = updateUser;
	}
	
	public void onUpdate(Object oldObject, Object newObject){
		log.debug("entering 'onUpdate' method");
		log.debug("oldObject - " + oldObject);
		log.debug("newObject - " + newObject);		
		if(oldObject != null && oldObject instanceof CurrencyGroup){
			String currencyGroupId = ((CurrencyGroup)oldObject).getId().getCurrencyGroupId();			
			String entityId = ((CurrencyGroup)oldObject).getId().getEntityId();
			EntityCurrencyGroupTO entityCurrencyGroupTO = new EntityCurrencyGroupTO(entityId, currencyGroupId);
			SwtUtil.getSwtMaintenanceCache().remove(entityCurrencyGroupTO);			
		}
		
		if(newObject != null && newObject instanceof CurrencyGroup){
			String currencyGroupId = ((CurrencyGroup)newObject).getId().getCurrencyGroupId();			
			String entityId = ((CurrencyGroup)newObject).getId().getEntityId();
			EntityCurrencyGroupTO entityCurrencyGroupTO = new EntityCurrencyGroupTO(entityId, currencyGroupId);
			SwtUtil.getSwtMaintenanceCache().remove(entityCurrencyGroupTO);			
		}				
		log.debug("exiting 'onUpdate' method");		
	}
	
	public void onDelete(){
			log.debug("entering 'onDelete' method");		
			EntityCurrencyGroupTO entityCurrencyGroupTO = new EntityCurrencyGroupTO(id.entityId, id.currencyGroupId);
			SwtUtil.getSwtMaintenanceCache().remove(entityCurrencyGroupTO);
			log.debug("exiting 'onDelete' method");			
	}	
}
