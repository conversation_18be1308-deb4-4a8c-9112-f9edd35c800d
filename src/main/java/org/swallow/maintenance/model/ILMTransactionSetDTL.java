/*
 * @(#)ILMTransactionSetDTL.java 1.0 29/11/13
 *
 * Copyright (c) 2006-2013 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.maintenance.model;

import java.util.Hashtable;

import org.swallow.model.BaseObject;

public class ILMTransactionSetDTL extends BaseObject implements org.swallow.model.AuditComponent{
	
	private static final long serialVersionUID = 1L;
	private Id id = new Id();
	// Variable to store credits
	private Double credits;
	// Variable to store debits
	private Double debits;
	// Variable to store creditsAsString
	private String creditsAsString;
	// Variable to store debitsAsString
	private String debitsAsString;
	// Variable to store description
	private String description;
	// Variable to store ilmTransactionSetHDR
	private ILMTransactionSetHDR ilmTransactionSetHDR;
	
	public static Hashtable logTable = new Hashtable();
	static {

		logTable.put("credits", "Credits");
		logTable.put("debits", "Debits");
		logTable.put("description", "Description");

	}
	
	
	public static class Id extends BaseObject{

		private String hostId = null;
		private String entityId = null;
		private String currencyCode = null;
		private String txnSetId = null;
		private String accountId = null;
		private String time = null;

		//default constructor
		public Id() {}

		public void initPrams(String hostId, String entityId, String currencyCode, String txnSetId, String accountId, String time) {

			this.setHostId(hostId);
			this.setEntityId(entityId);
			this.setCurrencyCode(currencyCode);
			this.setTxnSetId(txnSetId);
			this.setAccountId(accountId);
			this.setTime(time);

		}

		
		/**
		 * 
		 * @return hostId
		 */
		public String getHostId() {
			return hostId;
		}
		/**
		 * 
		 * @param hostId
		 */
		public void setHostId(String hostId) {
			this.hostId = hostId;
		}
		/**
		 * 
		 * @return entityId
		 */
		public String getEntityId() {
			return entityId;
		}
		/**
		 * 
		 * @param entityId
		 */
		public void setEntityId(String entityId) {
			this.entityId = entityId;
		}
		/**
		 * 
		 * @return currencyCode
		 */
		public String getCurrencyCode() {
			return currencyCode;
		}
		/**
		 * 
		 * @param currencyCode
		 */
		public void setCurrencyCode(String currencyCode) {
			this.currencyCode = currencyCode;
		}
		/**
		 * 
		 * @return txnSetId
		 */
		public String getTxnSetId() {
			return txnSetId;
		}
		/**
		 * 
		 * @param txnSetId
		 */
		public void setTxnSetId(String txnSetId) {
			this.txnSetId = txnSetId;
		}

		public String getAccountId() {
			return accountId;
		}

		public void setAccountId(String accountId) {
			this.accountId = accountId;
		}

		public String getTime() {
			return time;
		}

		public void setTime(String time) {
			this.time = time;
		}
		
		
		
	}
	/**
	 * 
	 * @return id
	 */
	public Id getId() {
		return id;
	}
	/**
	 * 
	 * @param id
	 */
	public void setId(Id id) {
		this.id = id;
	}
	/**
	 * 
	 * @return credits
	 */
	public Double getCredits() {
		return credits;
	}
	/**
	 * 
	 * @param credits
	 */
	public void setCredits(Double credits) {
		this.credits = credits;
	}
	/**
	 * 
	 * @return debits
	 */
	public Double getDebits() {
		return debits;
	}
	/**
	 * 
	 * @param debits
	 */
	public void setDebits(Double debits) {
		this.debits = debits;
	}
	
	/**
	 * 
	 * @return creditsAsString
	 */
	public String getCreditsAsString() {
		return creditsAsString;
	}
	/**
	 * 
	 * @param creditsAsString
	 */
	public void setCreditsAsString(String creditsAsString) {
		this.creditsAsString = creditsAsString;
	}
	/**
	 * 
	 * @return debitsAsString
	 */
	public String getDebitsAsString() {
		return debitsAsString;
	}
	/**
	 * 
	 * @param debitsAsString
	 */
	public void setDebitsAsString(String debitsAsString) {
		this.debitsAsString = debitsAsString;
	}
	/**
	 * 
	 * @return description
	 */
	public String getDescription() {
		return description;
	}
	/**
	 * 
	 * @param description
	 */
	public void setDescription(String description) {
		this.description = description;
	}
	public ILMTransactionSetHDR getIlmTransactionSetHDR() {
		return ilmTransactionSetHDR;
	}
	public void setIlmTransactionSetHDR(ILMTransactionSetHDR ilmTransactionSetHDR) {
		this.ilmTransactionSetHDR = ilmTransactionSetHDR;
	}

}
