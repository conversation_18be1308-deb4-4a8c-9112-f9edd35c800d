<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE hibernate-mapping PUBLIC
        "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
        "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">

<hibernate-mapping>
	<class name="org.swallow.maintenance.model.Entity"
		table="S_ENTITY">
		<composite-id name="id"
			class="org.swallow.maintenance.model.Entity$Id"
			unsaved-value="any">
			<key-property name="hostId" access="field" column="HOST_ID" />
			<key-property name="entityId" access="field"
				column="ENTITY_ID" />
		</composite-id>

		<property name="entityName" column="ENTITY_NAME"
			not-null="false" />
		<property name="domesticCurrency" column="DOMESTIC_CURRENCY"
			not-null="false" />
		<property name="reprotingCurrency" column="REPORT_CURRENCY"
			not-null="false" />
		<property name="countryId" column="COUNTRY_CODE"
			not-null="false" 
/>	
		<property name="exchangeRateFormat"
			column="EXCHANGE_RATE_FORMAT" not-null="false" />
		<property name="movementRetention" column="MOVEMENT_RETAIN"
			not-null="false" />
		<property name="inputRetention" column="INPUT_RETAIN"
			not-null="false" />
		<property name="outputRetention" column="OUTPUT_RETAIN"
			not-null="false" />
		<property name="cashFilterThreshold" column="CASH_FILTER"
			not-null="true" />
		<property name="securitiesFilterThreshold"
			column="SECURITIES_FILTER" not-null="true" />
		<property name="updateDate" column="UPDATE_DATE"
			not-null="false" />
		<property name="updateUser" column="UPDATE_USER"
			not-null="false" />
		<property name="ststsRetain" column="STATS_RETAIN"
			not-null="false" />
		<property name="interestRateRetain"
			column="INTEREST_RATE_RETAIN" not-null="false" />
		<property name="exchangeRateRetain"
			column="EXCHANGE_RATE_RETAIN" not-null="false" />
		<property name="sweepPosition" column="SWEEP_POSITION"
			not-null="false" />
		<property name="entityBIC" column="ENTITY_BIC" not-null="false" />
		<property name="preAdvicePosition" column="PREADVICE_POSITION"
			not-null="false" />
		
		<property name="internalBalance" column="BALANCE_INT_POSITION"
			not-null="false" />
		<property name="externalBalance" column="BALANCE_EXT_POSITION"
			not-null="false" />
		
		
		<property name="sweepCutoffLeadTime"
			column="SWEEP_CUTOFF_LEAD_TIME" not-null="true" />
		
		<property name="largeSmallMovementThreshold"
			column="SMALL_AMOUNT_THRESHOLD" not-null="true" />
		<property name="smallMovementRetain"
			column="SMALL_MOVEMENT_RETAIN" not-null="false" />
		
		<property name="balance" column="BALANCE_RETAIN"
			not-null="false" />
		<property name="balanceLog" column="BALANCE_LOG_RETAIN"
			not-null="false" />
		
		<property name="centralBankAccount"
			column="CENTRAL_BANK_ACCOUNT" not-null="false" />
		<property name="crrLimit" column="CRR_LIMIT" not-null="false" />
		<property name="crrLimitFromDate" column="CRR_LIMIT_DATE_FROM"
			not-null="false" />
		<property name="crrLimitToDate" column="CRR_LIMIT_DATE_TO"
			not-null="false" />
		<property name="startDay" column="CRR_START_DAY"
			not-null="false" />
		<property name="ilmRetain" column="ILM_RETAIN"
			not-null="false" />
		
	
<property name="ilmCalcPastDays" column="ILM_CALC_PAST_DAYS"
			not-null="false" />
		
<property name="entTimeZone" column="ENTITY_TZNAME" not-null="false"/>	
	</class>
</hibernate-mapping>
