/*
 * @(#)PartyAlias.java 1.0 21/11/06
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.maintenance.model;

import java.util.Date;
import java.util.Hashtable;


import org.swallow.model.BaseObject;

/**
 * <AUTHOR>
 *
 * TODO To change the template for this generated type comment go to
 * Window - Preferences - Java - Code Style - Code Templates
 */
public class PartyAlias extends BaseObject implements org.swallow.model.AuditComponent{

	
	private String partyId;
	private Date updateDate = new Date();
	private String updateUser;	
	private Id id = new Id();
	
	
	public static Hashtable logTable = new Hashtable();

	static {
		logTable.put("partyId", "Party Id");
		//logTable.put("custodianFlag", "Custodian Flag");
	}
	
	public static class Id extends BaseObject {
		private String hostId;
		private String entityId;
		private String partyAlias;	
		
		public Id() {
			super();			
		}
		
		public Id(String hostId, String entityId, String partyAlias) {
			super();
			this.hostId = hostId;
			this.entityId = entityId;
			this.partyAlias = partyAlias;
		}
		
		/**
		 * @return Returns the entityId.
		 */
		public String getEntityId() {
			return entityId;
		}
		/**
		 * @param entityId The entityId to set.
		 */
		public void setEntityId(String entityId) {
			this.entityId = entityId;
		}
		/**
		 * @return Returns the hostId.
		 */
		public String getHostId() {
			return hostId;
		}
		/**
		 * @param hostId The hostId to set.
		 */
		public void setHostId(String hostId) {
			this.hostId = hostId;
		}
		/**
		 * @return Returns the partyAlias.
		 */
		public String getPartyAlias() {
			return partyAlias;
		}
		/**
		 * @param partyAlias The partyAlias to set.
		 */
		public void setPartyAlias(String partyAlias) {
			this.partyAlias = partyAlias;
		}
	}
	

	/**
	 * @return Returns the id.
	 */
	public Id getId() {
		return id;
	}
	/**
	 * @param id The id to set.
	 */
	public void setId(Id id) {
		this.id = id;
	}
	/**
	 * @return Returns the partyId.
	 */
	public String getPartyId() {
		return partyId;
	}
	/**
	 * @param partyId The partyId to set.
	 */
	public void setPartyId(String partyId) {
		this.partyId = partyId;
	}
	/**
	 * @return Returns the updateDate.
	 */
	public Date getUpdateDate() {
		return updateDate;
	}
	/**
	 * @param updateDate The updateDate to set.
	 */
	public void setUpdateDate(Date updateDate) {
		this.updateDate = updateDate;
	}
	/**
	 * @return Returns the updateUser.
	 */
	public String getUpdateUser() {
		return updateUser;
	}
	/**
	 * @param updateUser The updateUser to set.
	 */
	public void setUpdateUser(String updateUser) {
		this.updateUser = updateUser;
	}
}

