<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE hibernate-mapping PUBLIC
        "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
        "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">

<hibernate-mapping>
   <class name="org.swallow.maintenance.model.KeyWords" table="P_KEYWORDS">
		<composite-id name="id" class="org.swallow.maintenance.model.KeyWords$Id" unsaved-value="any">
    	<key-property name="keyWord" access="field" column="KEYWORD"/>
        <key-property name="tableName" access="field" column="TABLE_NAME"/>
        <key-property name="mappedField" access="field" column="MAPPED_FIELD"/>
		</composite-id>
    </class>
</hibernate-mapping>
