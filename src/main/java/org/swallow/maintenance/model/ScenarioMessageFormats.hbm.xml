<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE hibernate-mapping PUBLIC
        "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
        "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">

<hibernate-mapping>
    <class name="org.swallow.maintenance.model.ScenarioMessageFormats" table="P_SCN_MESSAGE_FORMAT">
		<composite-id name="id" class="org.swallow.maintenance.model.ScenarioMessageFormats$Id" unsaved-value="any">

<!--         <key-property name="hostId" access="field" column="HOST_ID"/> -->
<!--         <key-property name="scenarioId" access="field" column="SCENARIO_ID" /> -->
        <key-property name="formatId" access="field" column="FORMAT_ID"/>

		</composite-id>
	
		<property name="formatName" column="FORMAT_NAME" />	
		<property name="formatType" column="FORMAT_TYPE" not-null="false"/>	
		<property name="fieldDelimeter" column="FIELD_DELIMETER" not-null="false"/>	
		<property name="hexaFldDelimeter" column="HEXADECI_FLD_DELIM" not-null="false"/>	
		<property name="msgSeparator" column="MSG_SEPARATOR" not-null="false"/>	
		<property name="hexaMsgSeparator" column="HEXADECI_MSG_SEPARATOR" not-null="false"/>	
		<property name="outputType" column="OUTPUT_TYPE" not-null="false"/>	
		<property name="path" column="PATH" not-null="false"/>	
		<property name="fileName" column="FILE_NAME" not-null="false"/>	
		<property name="authorizeFlag" column="AUTHORIZE_FLAG" not-null="false"/>
		<property name="updateDate" column="UPDATE_DATE" not-null="false"/>
		<property name="updateUser" column="UPDATE_USER" not-null="false"/>
		<property name="overdueTime" column="OVERDUE_TIME" not-null="false"/>	
		<!--Code modified by Chinniah for Mantis 1521 on  16-08-2011:Sweeping Process: New option to write messages to database  -->
		<property name="interfaceId" column="INTERFACE_ID" not-null="false"
/>
		<property name="usage" column="USAGE" not-null="false"/>				
    </class>
</hibernate-mapping>
