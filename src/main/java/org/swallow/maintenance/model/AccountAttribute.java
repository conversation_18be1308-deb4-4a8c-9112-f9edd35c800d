package org.swallow.maintenance.model;

import java.math.BigDecimal;
import java.util.Date;
import java.util.Hashtable;

import org.swallow.model.BaseObject;

public class AccountAttribute extends BaseObject implements org.swallow.model.AuditComponent{


	private static final long serialVersionUID = 1L;
	private Long sequenceKey;
	private BigDecimal numValue;
	private String strValue;
	private Date dateValue;
	private String valueAsString;
	private Date updateDate;
	private String updateDateAsString;
	private String updateUser;
	private String attributeName;
	private String effectiveDateAsString = null;
	public static Hashtable logTable = new Hashtable();
	public String typeAsString = "text";
	
	static {

		logTable.put("numValue", "num Value");
		logTable.put("strValue", "str Value");
		logTable.put("dateValue", "date Value");
		logTable.put("updateUser", "updateUser");

	}
	

		private String hostId ;
		private String accountId ;
		private String entityId ;
		private String attributeId ;
		private Date effectiveDate = null;
	
		
		
		public String getHostId() {
			return hostId;
		}

		public void setHostId(String hostId) {
			this.hostId = hostId;
		}

		public String getAccountId() {
			return accountId;
		}

		public void setAccountId(String accountId) {
			this.accountId = accountId;
		}

		public String getEntityId() {
			return entityId;
		}

		public void setEntityId(String entityId) {
			this.entityId = entityId;
		}

		public String getAttributeId() {
			return attributeId;
		}
		public void setAttributeId(String attributeId) {
			this.attributeId = attributeId;
		}
		public Date getEffectiveDate() {
			return effectiveDate;
		}
		public void setEffectiveDate(Date effectiveDate) {
			this.effectiveDate = effectiveDate;
		}

		
	public BigDecimal getNumValue() {
		return numValue;
	}
	public void setNumValue(BigDecimal numValue) {
		this.numValue = numValue;
	}
	public String getStrValue() {
		return strValue;
	}
	public void setStrValue(String strValue) {
		this.strValue = strValue;
	}
	public Date getDateValue() {
		return dateValue;
	}
	public void setDateValue(Date dateValue) {
		this.dateValue = dateValue;
	}
	public Date getUpdateDate() {
		return updateDate;
	}
	public void setUpdateDate(Date updateDate) {
		this.updateDate = updateDate;
	}
	public String getUpdateUser() {
		return updateUser;
	}
	public void setUpdateUser(String updateUser) {
		this.updateUser = updateUser;
	}
	
	public String getEffectiveDateAsString() {
		return effectiveDateAsString;
	}
	public void setEffectiveDateAsString(String effectiveDateAsString) {
		this.effectiveDateAsString = effectiveDateAsString;
	}
	public String getUpdateDateAsString() {
		return updateDateAsString;
	}
	public void setUpdateDateAsString(String updateDateAsString) {
		this.updateDateAsString = updateDateAsString;
	}
	public String getAttributeName() {
		return attributeName;
	}
	public void setAttributeName(String attributeName) {
		this.attributeName = attributeName;
	}
	public String getValueAsString() {
		return valueAsString;
	}
	public void setValueAsString(String valueAsString) {
		this.valueAsString = valueAsString;
	}
	public Long getSequenceKey() {
		return sequenceKey;
	}
	public void setSequenceKey(Long sequenceKey) {
		this.sequenceKey = sequenceKey;
	}
	
	public String getTypeAsString() {
		return typeAsString;
	}

	public void setTypeAsString(String typeAsString) {
		this.typeAsString = typeAsString;
	}
	
	
	
}
