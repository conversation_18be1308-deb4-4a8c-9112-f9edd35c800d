/*
 * Created on Dec 6, 2005
 *
 * TODO To change the template for this generated file go to
 * Window - Preferences - Java - Code Style - Code Templates
 */
package org.swallow.maintenance.model;


import java.util.Date;

import org.swallow.model.BaseObject;

public class PartyFlagAction extends BaseObject{
	
	/**
	 * @return Returns the id.
	 */
	public Id getId() {
		return id;
	}
	/**
	 * @param id The id to set.
	 */
	public void setId(Id id) {
		this.id = id;
	}
		/**
	 * @return Returns the updateDate.
	 */
	public Date getUpdateDate() {
		return updateDate;
	}
	/**
	 * @param updateDate The updateDate to set.
	 */
	public void setUpdateDate(Date updateDate) {
		this.updateDate = updateDate;
	}
	/**
	 * @return Returns the updateUser.
	 */
	public String getUpdateUser() {
		return updateUser;
	}
	/**
	 * @param updateUser The updateUser to set.
	 */
	public void setUpdateUser(String updateUser) {
		this.updateUser = updateUser;
	}
    /** nullable persistent field */
    private String custodianFlag;
    private String partyName;
 

	
	/**
	 * @return Returns the custodianFlag.
	 */
	public String getCustodianFlag() {
		return custodianFlag;
	}
	/**
	 * @param custodianFlag The custodianFlag to set.
	 */
	public void setCustodianFlag(String custodianFlag) {
		this.custodianFlag = custodianFlag;
	}
    /** nullable persistent field */
    private Date updateDate;

    /** nullable persistent field */
    private String updateUser;

	private Id id = new Id();
	
	
	
	public static class Id extends BaseObject{
	    /** identifier field */
	    private String hostId;

	    /** identifier field */
	    private String entityId;

	    /** identifier field */
	    private String partyId;

	   
		
		
		/**
		 * @param hostId
		 * @param entityId
		 * @param currencyCode
		 * @param positionLevel
		 */
		public Id(String hostId, String entityId, String partyId
				) {
			super();
			this.hostId = hostId;
			this.entityId = entityId;
			this.partyId = partyId;
			
		}
		public Id() {}

		
		/**
		 * @return Returns the entityId.
		 */
		public String getEntityId() {
			return entityId;
		}
		/**
		 * @param entityId The entityId to set.
		 */
		public void setEntityId(String entityId) {
			this.entityId = entityId;
		}
		/**
		 * @return Returns the hostId.
		 */
		public String getHostId() {
			return hostId;
		}
		/**
		 * @param hostId The hostId to set.
		 */
		public void setHostId(String hostId) {
			this.hostId = hostId;
		}
		
		/**
		 * @return Returns the partyId.
		 */
		public String getPartyId() {
			return partyId;
		}
		/**
		 * @param partyId The partyId to set.
		 */
		public void setPartyId(String partyId) {
			this.partyId = partyId;
		}
	}	
	
	
	/**
	 * @return Returns the partyName.
	 */
	public String getPartyName() {
		return partyName;
	}
	/**
	 * @param partyName The partyName to set.
	 */
	public void setPartyName(String partyName) {
		this.partyName = partyName;
	}
}