<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE hibernate-mapping PUBLIC
        "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
        "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">

<hibernate-mapping>
    <class name="org.swallow.maintenance.model.MessageFields" table="P_MESSAGE_FIELDS">
		<composite-id name="id" class="org.swallow.maintenance.model.MessageFields$Id" unsaved-value="any">

        <key-property name="entityId" access="field" column="ENTITY_ID" />
        <key-property name="hostId" access="field" column="HOST_ID"/>
        <key-property name="formatId" access="field" column="FORMAT_ID"/>
        <key-property name="serialNo" access="field" column="SERIAL_NUMBER"/>
		</composite-id>
	
		<property name="lineNo" column="LINE_NO" not-null="false"/>
		<property name="seqNo" column="SEQ_NUMBER" not-null="false"/>	
		<property name="fieldType" column="FIELD_TYPE" not-null="false"/>	
		<property name="value" column="VALUE" not-null="false"/>	
		<property name="startPos" column="START_POSITION" not-null="false"/>	
		<property name="endPos" column="END_POSITION" not-null="false"/>	
		<property name="updateDate" column="UPDATE_DATE" not-null="false"/>	
		<property name="updateUser" column="UPDATE_USER" not-null="false"/>	
		

    </class>
</hibernate-mapping>
