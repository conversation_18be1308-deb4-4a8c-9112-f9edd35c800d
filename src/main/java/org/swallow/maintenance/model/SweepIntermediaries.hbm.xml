<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE hibernate-mapping PUBLIC
        "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
        "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">

<hibernate-mapping>
    <class name="org.swallow.maintenance.model.SweepIntermediaries" table="P_SWEEP_BIC_INTERMEDIARY">
    <composite-id name="id" class="org.swallow.maintenance.model.SweepIntermediaries$Id" unsaved-value="any">
        <key-property name="hostId" access="field" column="HOST_ID"/>
        <key-property name="entityId" access="field" column="ENTITY_ID" />
        <key-property name="currencyCode" access="field" column="CURRENCY_CODE"/>
        <key-property name="targetBic" access="field" column="TARGET_BIC"/>
    </composite-id>
    	<property name="intermediary" column="INTERMEDIARY_BIC" not-null="false"/>
		<!--START:code modified by <PERSON><PERSON> on 04-Mar-2010 for Mantis 1121: added Account field in UI screen  -->		
    	<property name="accountId" column="ACCOUNT" not-null="false"/>
		<!--END:code modified by Bala on 04-Mar-2010 for Mantis 1121: added Account field in UI screen  -->		
    </class>
</hibernate-mapping> 