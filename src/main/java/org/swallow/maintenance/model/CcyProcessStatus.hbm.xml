<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE hibernate-mapping PUBLIC
        "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
        "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">
<hibernate-mapping>
<class name="org.swallow.maintenance.model.CcyProcessStatus" table="P_CCY_PROCESS_STATUS">
   <composite-id class="org.swallow.maintenance.model.CcyProcessStatus$Id" name="id" unsaved-value="any">
      <key-property name="hostId" column="HOST_ID" access="field"></key-property>
      <key-property name="entityId" column="ENTITY_ID" access="field"></key-property>
      <key-property name="ccyCode" column="CURRENCY_CODE" access="field"></key-property>
      <key-property name="processId" column="PROCESS_ID" access="field"></key-property>
      <key-property name="valueDate" column="VALUE_DATE" access="field"></key-property>
   </composite-id>
   <property name="lastStarted" column="LAST_STARTED" not-null="false"/>		
   <property name="lastEnded" column="LAST_ENDED" not-null="false"/>		
   <property name="currentStatus" column="CURRENT_STATUS" not-null="false"/>		
   <property name="lastExecuteStatus" column="LAST_EXECUTE_STATUS" not-null="false"/>		
</class>
</hibernate-mapping>
