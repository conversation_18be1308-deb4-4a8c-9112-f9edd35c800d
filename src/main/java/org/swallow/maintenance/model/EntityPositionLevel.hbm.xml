<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE hibernate-mapping PUBLIC
        "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
        "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">
<hibernate-mapping>
 <class name="org.swallow.maintenance.model.EntityPositionLevel" table="P_POSITION_LEVEL_NAME">
  <composite-id
   class="org.swallow.maintenance.model.EntityPositionLevel$Id"
   name="id" unsaved-value="any">
   <key-property name="hostId" access="field" column="HOST_ID"/>
   <key-property name="entityId" access="field" column="ENTITY_ID"/>
   <key-property name="positionLevel" access="field" column="POSITION_LEVEL"/>
  </composite-id>
  <property name="positionLevelName" column="POSITION_LEVEL_NAME"/>
  <property name="indicator" column="INTERNAL_EXTERNAL"/>
  <property name="updateDate" column="UPDATE_DATE"/>
  <property name="updateUser" column="UPDATE_USER"/>
 </class>
</hibernate-mapping>
