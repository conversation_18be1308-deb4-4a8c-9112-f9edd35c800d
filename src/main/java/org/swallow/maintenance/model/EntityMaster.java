package org.swallow.maintenance.model;

import org.swallow.model.BaseObject;

public class EntityMaster extends BaseObject {

	private String entityName;
	private Id id = new Id();
	

	public static class Id extends BaseObject{
		private String hostId;
		private String entityId;

		public Id() {}

		public Id(String hostId, String entityId) {
			this.hostId = hostId;
			this.entityId = entityId;
		}
		public String getEntityId() {
			return entityId;
		}
		public void setEntityId(String entityId) {
			this.entityId = entityId;
		}		
		public String getHostId() {
			return hostId;
		}
		public void setHostId(String hostId){
			this.hostId = hostId;
		}
	}

	public void setId(Id id){
		this.id = id; 
		}
	

	
	public Id getId(){
		return id; 
		}
	
		public String getEntityName() {
		return entityName;
	}
	/**
	 * @param userName The userName to set.
	 */
	public void setEntityName(String entityName) {
		this.entityName = entityName;
	}
}
