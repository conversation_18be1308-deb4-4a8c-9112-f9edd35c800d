<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE hibernate-mapping PUBLIC
        "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
        "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">
<hibernate-mapping>
 <class name="org.swallow.maintenance.model.CurrencyGroup" table="S_CURRENCY_GROUP">
  <composite-id class="org.swallow.maintenance.model.CurrencyGroup$Id"
   name="id" unsaved-value="any">
   <key-property name="hostId" access="field" column="HOST_ID"/>
   <key-property name="entityId" access="field" column="ENTITY_ID"/>
   <key-property name="currencyGroupId" access="field" column="CURRENCY_GROUP_ID"/>
  </composite-id>
  <property name="currencyGroupName" column="CURRENCY_GROUP_NAME" not-null="true"/>
  <property name="updateDate" column="UPDATE_DATE" not-null="true"/>
  <property name="updateUser" column="UPDATE_USER" not-null="true"/>
   </class>
</hibernate-mapping>
