/*
 * (c) MessageFormats.java 
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.maintenance.model;

import java.util.Date;
import java.util.Hashtable;

import org.swallow.model.BaseObject;
import org.swallow.util.SwtConstants;

/**
 * This is the form bean class for Message Formmats Screen
 * 
 * <AUTHOR>
 * 
 */
public class ScenarioMessageFormats extends BaseObject implements
		org.swallow.model.AuditComponent {

	// Variable Decleration for formatName
	private String formatName = null;
	// Variable Decleration for formatType
	private String formatType = SwtConstants.FORMAT_TYPE_FIXED;
	// Variable Decleration for formatTypeDisplay
	private String formatTypeDisplay = null;
	// Variable Decleration for fieldDelimeter
	private String fieldDelimeter = null;
	// Variable Decleration for hexaFldDelimeter
	private String hexaFldDelimeter = null;
	// Variable Decleration for msgSeparator
	private String msgSeparator = null;
	// Variable Decleration for hexaMsgSeparator
	private String hexaMsgSeparator = null;
	// Variable Decleration for outputType
	private String outputType = SwtConstants.OUTPUT_TYPE;
	// Variable Decleration for outputTypeDisplay
	private String outputTypeDisplay = null;
	// Variable Decleration for path
	private String path = null;
	// Variable Decleration for fileName
	private String fileName = null;
	// Variable Decleration for authorizeFlag
	private String authorizeFlag = null;
	// Variable Decleration for updateDate
	private Date updateDate = new Date();
	// Variable Decleration for updateUser
	private String updateUser = null;
	// Variable Decleration for id
	private Id id = new Id();
	// Variable Decleration for interfaceId
	private String interfaceId = null;
	private String usage = SwtConstants.FORMAT_TYPE_SWEEP;
	// Variable Decleration for overdueTime
	private String overdueTime = null;
	public static Hashtable logTable = new Hashtable();
	static {
		logTable.put("formatName", "Format Name");
		logTable.put("formatType", "Format Type");
		logTable.put("fieldDelimeter", "Field Delimeter");
		logTable.put("hexaFldDelimeter", "Hexa decimal Field Delimiter");
		logTable.put("msgSeparator", "Message Separator");
		logTable.put("hexaMsgSeparator", "Hexa decimal Message Separator");
		logTable.put("outputType", "Output Type");
		logTable.put("path", "Path");
		logTable.put("fileName", "File Name");
		logTable.put("authorizeFlag", "Authorize Flag");

	}

	/**
	 * @return Returns the formatTypeDisplay.
	 */
	/*
	 * Start:Code Modified by Chinniah on 16-AUG-2011 for Mantis 1521:Sweeping
	 * Process: New option to write messages to database
	 */
	/**
	 * Getter method for interfaceId
	 * 
	 * @return interfaceId as String
	 */

	public String getInterfaceId() {
		return interfaceId;
	}

	/**
	 * Setter method for interfaceId
	 * 
	 * @param interfaceId
	 */

	public void setInterfaceId(String interfaceId) {
		this.interfaceId = interfaceId;
	}

	/*
	 * Start:Code Modified by Chinniah on 16-AUG-2011 for Mantis 1521:Sweeping
	 * Process: New option to write messages to database
	 */
	/**
	 * @param formatTypeDisplay
	 *            The formatTypeDisplay to set.
	 */
	public void setFormatTypeDisplay(String formatTypeDisplay) {
		setFormatType(formatTypeDisplay);
		this.formatTypeDisplay = formatTypeDisplay;
	}

	public String getFormatTypeDisplay() {
		if (formatType != null) {
			if (formatType.equalsIgnoreCase(SwtConstants.FORMAT_TYPE_FIXED))

				formatTypeDisplay = SwtConstants.MESSAGE_FIXED;

			else if (formatType
					.equalsIgnoreCase(SwtConstants.FORMAT_TYPE_DELIMITED))

				formatTypeDisplay = SwtConstants.MESSAGE_DELMITED;

			else if (formatType
					.equalsIgnoreCase(SwtConstants.FORMAT_TYPE_TAGGED))

				formatTypeDisplay = SwtConstants.MESSAGE_TAGGED;

			else if (formatType
					.equalsIgnoreCase(SwtConstants.FORMAT_TYPE_TAGGED_VARIABLE))
				formatTypeDisplay = SwtConstants.MESSAGE_TAGGED_VARIABLE;

		}
		return formatTypeDisplay;
	}

	/**
	 * @return Returns the outputTypeDisplay.
	 */
	public String getOutputTypeDisplay() {
		// 1521
		if (outputType != null) {
			if (outputType.equalsIgnoreCase(SwtConstants.FORMAT_TYPE_FIXED))

				outputTypeDisplay = SwtConstants.MESSAGE_FILE;
			/*
			 * Code Modified by Chinniah on 16-AUG-2011 for Mantis 1521:Sweeping
			 * Process: New option to write messages to database
			 */
			else if (outputType.equalsIgnoreCase("Q"))

				outputTypeDisplay = SwtConstants.MESSAGE_MQINTER;

		}
		return outputTypeDisplay;

	}

	/**
	 * @param outputTypeDisplay
	 *            The outputTypeDisplay to set.
	 */
	public void setOutputTypeDisplay(String outputTypeDisplay) {
		setOutputType(outputTypeDisplay);
		this.outputTypeDisplay = outputTypeDisplay;
	}

	public static class Id extends BaseObject {
//		private String hostId = null;
//		private String scenarioId = null;
		private String formatId = null;

		public Id() {
		}

		public Id(String hostId, String scenarioId, String formatId) {
//			this.hostId = hostId;
//			this.scenarioId = scenarioId;
			this.formatId = formatId;
		}

		/**
		 * @return Returns the entityId.
		 */
//		public String getScenarioId() {
//			return scenarioId;
//		}
//
//		/**
//		 * @param entityId
//		 *            The entityId to set.
//		 */
//		public void setScenarioId(String entityId) {
//			this.scenarioId = entityId;
//		}

		/**
		 * @return Returns the formatId.
		 */
		public String getFormatId() {
			return formatId;
		}

		/**
		 * @param formatId
		 *            The formatId to set.
		 */
		public void setFormatId(String formatId) {
			this.formatId = formatId;
		}

//		/**
//		 * @return Returns the hostId.
//		 */
//		public String getHostId() {
//			return hostId;
//		}
//
//		/**
//		 * @param hostId
//		 *            The hostId to set.
//		 */
//		public void setHostId(String hostId) {
//			this.hostId = hostId;
//		}
	}

	public void setId(Id id) {
		this.id = id;
	}

	public Id getId() {
		return id;
	}

	/**
	 * @return Returns the formatName.
	 */
	public String getFormatName() {
		return formatName;
	}

	/**
	 * @param formatName
	 *            The formatName to set.
	 */
	public void setFormatName(String formatName) {
		this.formatName = formatName;
	}

	/**
	 * @return Returns the formatType.
	 */
	public String getFormatType() {
		return formatType;
	}

	/**
	 * @param formatType
	 *            The formatType to set.
	 */
	public void setFormatType(String formatType) {
		this.formatType = formatType;
	}

	/**
	 * @return Returns the authorizeFlag.
	 */
	public String getAuthorizeFlag() {
		return authorizeFlag;
	}

	/**
	 * @param authorizeFlag
	 *            The authorizeFlag to set.
	 */
	public void setAuthorizeFlag(String authorizeFlag) {
		this.authorizeFlag = authorizeFlag;
	}

	/**
	 * @return Returns the fieldDelimeter.
	 */
	public String getFieldDelimeter() {
		return fieldDelimeter;
	}

	/**
	 * @param fieldDelimeter
	 *            The fieldDelimeter to set.
	 */
	public void setFieldDelimeter(String fieldDelimeter) {
		this.fieldDelimeter = fieldDelimeter;
	}

	/**
	 * @return Returns the fileName.
	 */
	public String getFileName() {
		return fileName;
	}

	/**
	 * @param fileName
	 *            The fileName to set.
	 */
	public void setFileName(String fileName) {
		this.fileName = fileName;
	}

	/**
	 * @return Returns the hexaFldDelimeter.
	 */
	public String getHexaFldDelimeter() {
		return hexaFldDelimeter;
	}

	/**
	 * @param hexaFldDelimeter
	 *            The hexaFldDelimeter to set.
	 */
	public void setHexaFldDelimeter(String hexaFldDelimeter) {
		this.hexaFldDelimeter = hexaFldDelimeter;
	}

	/**
	 * @return Returns the hexaMsgSeparator.
	 */
	public String getHexaMsgSeparator() {
		return hexaMsgSeparator;
	}

	/**
	 * @param hexaMsgSeparator
	 *            The hexaMsgSeparator to set.
	 */
	public void setHexaMsgSeparator(String hexaMsgSeparator) {
		this.hexaMsgSeparator = hexaMsgSeparator;
	}

	/**
	 * @return Returns the msgSeparator.
	 */
	public String getMsgSeparator() {
		return msgSeparator;
	}

	/**
	 * @param msgSeparator
	 *            The msgSeparator to set.
	 */
	public void setMsgSeparator(String msgSeparator) {
		this.msgSeparator = msgSeparator;
	}

	/**
	 * @return Returns the outputType.
	 */
	public String getOutputType() {
		return outputType;
	}

	/**
	 * @param outputType
	 *            The outputType to set.
	 */
	public void setOutputType(String outputType) {
		this.outputType = outputType;
	}

	/**
	 * @return Returns the path.
	 */
	public String getPath() {
		return path;
	}

	/**
	 * @param path
	 *            The path to set.
	 */
	public void setPath(String path) {
		this.path = path;
	}

	/**
	 * @return Returns the updateDate.
	 */
	public Date getUpdateDate() {
		return updateDate;
	}

	/**
	 * @param updateDate
	 *            The updateDate to set.
	 */
	public void setUpdateDate(Date updateDate) {
		this.updateDate = updateDate;
	}

	/**
	 * @return Returns the updateUser.
	 */
	public String getUpdateUser() {
		return updateUser;
	}

	/**
	 * @param updateUser
	 *            The updateUser to set.
	 */
	public void setUpdateUser(String updateUser) {
		this.updateUser = updateUser;
	}

	public String getOverdueTime() {
		return overdueTime;
	}

	public void setOverdueTime(String overdueTime) {
		this.overdueTime = overdueTime;
	}

	public String getUsage() {
		return usage;
	}

	public void setUsage(String usage) {
		this.usage = usage;
	}

}
