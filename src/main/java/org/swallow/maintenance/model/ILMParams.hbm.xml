<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE hibernate-mapping PUBLIC
        "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
        "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">
<hibernate-mapping>
    <class name="org.swallow.maintenance.model.ILMParams" table="P_ILM_GENERAL_PARAMETERS ">
        <id name="hostId" column="HOST_ID" unsaved-value="null">
		<generator class="assigned"/>
		</id>
		<property name="timeslotSize" column="TIMESLOT_SIZE" not-null="true"/>
		<property name="systemLoggingLevel" column="SYSTEM_LOGGING_LEVEL" not-null="false"/>	
		<property name="datasetUpdateInterval" column="DATASET_UPDATE_INTERVAL" not-null="false"/>	
			
    </class>
</hibernate-mapping>