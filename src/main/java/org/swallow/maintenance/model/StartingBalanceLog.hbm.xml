<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE hibernate-mapping PUBLIC
        "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
        "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">
<hibernate-mapping>
 <class name="org.swallow.maintenance.model.StartingBalanceLog" table="P_BALANCE_LOG">
  <id name="balanceLogSeq" type="long" column="BALANCE_LOG_SEQUENCE">
		<generator class="sequence">
			<param name="sequence_name">BALANCE_LOG_SEQUENCE</param>
		 </generator>
	</id>

  <property name="hostId" column="HOST_ID"/>
  <property name="entityId" column="ENTITY_ID"/>
  <property name="balanceType" column="BALANCE_TYPE" /> 
  <property name="balanceDate" column="BALANCE_DATE" />
  <property name="balanceTypeId" column="BAL_TYPE_ID" />
  <!-- Start:Commented by Arumugam on 08-Oct-09 for Mantis 997:Allow Internal and External forecasts to have independent SOD's-->
  <!-- <property name="oldStartBalance" column="OLD_START_BALANCE"/>-->
  <!-- <property name="newStartBalance" column="NEW_START_BALANCE"/>  -->
  <!-- End:Commented by Arumugam on 08-Oct-09 for Mantis 997:Allow Internal and External forecasts to have independent SOD's-->
  
  
  <property name="updateDate" column="UPDATE_DATE"/>
  <property name="updateUser" column="UPDATE_USER"/>
  <!-- Start:Commented by Arumugam on 08-Oct-09 for Mantis 997:Allow Internal and External forecasts to have independent SOD's-->
  <!-- <property name="balanceSourceId" column="BALANCE_SOURCE_ID" not-null="false"/> -->
  <!-- End:Commented by Arumugam on 08-Oct-09 for Mantis 997:Allow Internal and External forecasts to have independent SOD's-->
  
  <!-- Start:  Refer to Mail , Subject :- New column for P_BALANCE table , Dated : Tuesday, January 23-->
		 <!-- Start:Commented by Arumugam on 08-Oct-09 for Mantis 997:Allow Internal and External forecasts to have independent SOD's-->
		<!-- <property name="balanceSource" column="BALANCE_SOURCE" not-null="false"/>-->
		 <!-- End:Commented by Arumugam on 08-Oct-09 for Mantis 997:Allow Internal and External forecasts to have independent SOD's-->
  <!-- End:  Refer to Mail , Subject :- New column for P_BALANCE table , Dated : Tuesday, January 23-->
  
  <!-- Start:Added by Arumugam on 08-Oct-09 for Mantis 997:Allow Internal and External forecasts to have independent SOD's-->
  <property name="workingForcastSODChange" column="WKG_FORECAST_SOD_CHANGE" not-null="false"/>
  <property name="newWorkingForcastSOD" column="NEW_WKG_FORECAST_SOD" not-null="false"/>
  <property name="newWorkingForcastSODType" column="NEW_WKG_FORECAST_SOD_TYPE" not-null="false"/>
  <property name="bvForecastAdjustChange" column="BV_FORECAST_ADJUST_CHANGE" not-null="false"/>
  <property name="newBVForecastAdjust" column="NEW_BV_FORECAST_ADJUST" not-null="false"/>
  <property name="bvForecastAdjustLastId" column="BV_FORECAST_ADJUST_LAST_ID" not-null="false"/>
  <property name="workingExternalSODChange" column="WKG_EXTERNAL_SOD_CHANGE" not-null="false"/>
  <property name="newWorkingExternalSOD" column="NEW_WKG_EXTERNAL_SOD" not-null="false"/>
  <property name="newWorkingExternalSODType" column="NEW_WKG_EXTERNAL_SOD_TYPE" not-null="false"/>
  <property name="bvExternalAdjustChange" column="BV_EXTERNAL_ADJUST_CHANGE" not-null="false"/>
  <property name="newBVExternalAdjust" column="NEW_BV_EXTERNAL_ADJUST" not-null="false"/>
  <property name="bvExternalAdjustLastId" column="BV_EXTERNAL_ADJUST_LAST_ID" not-null="false"/>
  <property name="newInternalBalSource" column="NEW_INT_BAL_SOURCE_INFO" not-null="false"/>
  <property name="newExternalBalSource" column="NEW_EXT_BAL_SOURCE_INFO" not-null="false"/>
  <!--End:Added by Arumugam on 08-Oct-09 for Mantis 997:Allow Internal and External forecasts to have independent SOD's-->
  
    <property name="suppliedInternalSODChange" column="SUPPLIED_INTERNAL_CHANGE" not-null="false"/>
  	<property name="suppliedExternalSODChange" column="SUPPLIED_EXTERNAL_CHANGE" not-null="false"/>
    <property name="newSuppliedInternalSOD" column="NEW_SUPPLIED_INTERNAL" not-null="false"/>
  	<property name="newSuppliedExternalSOD" column="NEW_SUPPLIED_EXTERNAL" not-null="false"/>
  </class>
  


  
</hibernate-mapping>
