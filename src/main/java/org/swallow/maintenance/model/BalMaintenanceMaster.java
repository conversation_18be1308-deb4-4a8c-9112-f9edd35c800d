/*
 * @(#)BalMaintenanceMaster.java  14/12/05
 * 
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 * 
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */


package org.swallow.maintenance.model;

import java.util.Date;
import org.swallow.model.BaseObject;

public class BalMaintenanceMaster extends BaseObject {
	
	private Id id = new Id();
	private String updateUser;
	private Date updateDate ;
	
	
	public static class Id extends BaseObject{
	private String hostId;
	private String entityId;
	private String balanceType;
	private Date balanceDate;
	private String balanceDateAsString;
	
	
	/**
	 * 
	 */
	public Id() {
		}

		/**
		 * @param hostId
		 * @param entityId
		 * @param balanceType
		 * @param balanceDate
		 * @param balanceTypeId
		 */
		public Id(String hostId, String entityId, String balanceType,
				Date balanceDate) {
			this.hostId = hostId;
			this.entityId = entityId;
			this.balanceType = balanceType;
			this.balanceDate = balanceDate;
		}

		/**
		 * @return
		 */
		public String getEntityId() {
			return entityId;
		}

		/**
		 * @param entityId
		 */
		public void setEntityId(String entityId) {
			this.entityId = entityId;
		}

		/**
		 * @return
		 */
		public String getHostId() {
			return hostId;
		}

		/**
		 * @param hostId
		 */
		public void setHostId(String hostId) {
			this.hostId = hostId;
		}
	/**
	 * @return Returns the balanceDate.
	 */
	public Date getBalanceDate() {
		return balanceDate;
	}
	/**
	 * @param balanceDate The balanceDate to set.
	 */
	public void setBalanceDate(Date balanceDate) {
		this.balanceDate = balanceDate;
	}
	/**
	 * @return Returns the balanceType.
	 */
	public String getBalanceType() {
		return balanceType;
	}
	/**
	 * @param balanceType The balanceType to set.
	 */
	public void setBalanceType(String balanceType) {
		this.balanceType = balanceType;
	}
	
	/**
	 * @return Returns the balanceDateAsString.
	 */
	public String getBalanceDateAsString() {
		return balanceDateAsString;
	}

	/**
	 * @param balanceDateAsString The balanceDateAsString to set.
	 */
	public void setBalanceDateAsString(String balanceDateAsString) {
		this.balanceDateAsString = balanceDateAsString;
	}

	
    }
	


	/**
	 * @return Returns the id.
	 */
	public Id getId() {
		
		return id;
	}
	/**
	 * @param id The id to set.
	 */
	public void setId(Id id) {
		this.id = id;
	}
	
	/**
	 * @return Returns the updateUser.
	 */
	public String getUpdateUser() {
		return updateUser;
	}
	/**
	 * @param updateUser The updateUser to set.
	 */
	public void setUpdateUser(String updateUser) {
		this.updateUser = updateUser;
	}
	
	
		
	/**
	 * @return Returns the updateDate.
	 */
	public Date getUpdateDate() {
		return updateDate;
	}
	/**
	 * @param updateDate The updateDate to set.
	 */
	public void setUpdateDate(Date updateDate) {
		this.updateDate = updateDate;
	}
}
