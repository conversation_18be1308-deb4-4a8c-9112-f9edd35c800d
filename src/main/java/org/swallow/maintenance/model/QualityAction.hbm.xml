<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE hibernate-mapping PUBLIC
        "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
        "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">
<hibernate-mapping>
 <class name="org.swallow.maintenance.model.QualityAction" table="P_MATCH_QUALITY">
  <composite-id class="org.swallow.maintenance.model.QualityAction$Id"
   name="id" unsaved-value="any">
   <key-property name="hostId" type="java.lang.String" column="HOST_ID" length="12"/>
   <key-property name="entityId" type="java.lang.String"
    column="ENTITY_ID" length="12"/>
   <key-property name="currencyCode" type="java.lang.String"
    column="CURRENCY_CODE" length="3"/>
   <key-property name="positionLevel" type="java.lang.Integer"
    column="POSITION_LEVEL" length="1"/>
   <key-property name="parameterId" type="java.lang.Integer"
    column="PARAMETER_ID" length="1"/>
  </composite-id>
  <property name="matchQualityA" type="java.lang.String"
   column="MATCH_QUALITY_A" length="1"/>
  <property name="matchQualityB" type="java.lang.String"
   column="MATCH_QUALITY_B" length="1"/>
  <property name="matchQualityC" type="java.lang.String"
   column="MATCH_QUALITY_C" length="1"/>
  <property name="matchQualityD" type="java.lang.String"
   column="MATCH_QUALITY_D" length="1"/>
  <property name="matchQualityE" type="java.lang.String"
   column="MATCH_QUALITY_E" length="1"/>
  <property name="updateDate" type="java.sql.Timestamp"
   column="UPDATE_DATE" length="7"/>
  <property name="updateUser" type="java.lang.String"
   column="UPDATE_USER" length="15"/>
  <many-to-one name="paramIdRef" lazy="false"
   class="org.swallow.maintenance.model.MatchParams"
   column="PARAMETER_ID" not-null="true" outer-join="true"
   update="false" insert="false" foreign-key="FK_P_MATCH_QTY_P_MATCH_PARAMS"/>
 </class>
</hibernate-mapping>
