/*
 * @(#)ILMParams.java  29/11/13
 * 
 * Copyright (c) 2006-2013 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 * 
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.maintenance.model;

import java.util.Hashtable;

import org.swallow.model.BaseObject;



public class ILMParams extends BaseObject implements
		org.swallow.model.AuditComponent {
	

	// Variable to store hostId
	private String hostId = null;
	// Variable to store timeslot Size
	private Integer timeslotSize = null;
	// Variable to store system Logging Level
	private String systemLoggingLevel = null;
	// Variable to store dataset Update Interval
	private Integer datasetUpdateInterval = null;

	public static Hashtable logTable = new Hashtable();
	static {

		logTable.put("timeslotSize", "Timeslot Size");
		logTable.put("systemLoggingLevel", "System Logging Level");
		logTable.put("datasetUpdateInterval", "Dataset Update Interval");

	}
	

	/**
	 * 
	 * @return hostId
	 */
	public String getHostId() {
		return hostId;
	}
    /**
     * 
     * @param hostId
     */
	public void setHostId(String hostId) {
		this.hostId = hostId;
	}
	/**
	 * 
	 * @return timeslotSize
	 */
	public Integer getTimeslotSize() {
		return timeslotSize;
	}

	/**
	 * 
	 * @param timeslotSize
	 */
	public void setTimeslotSize(Integer timeslotSize) {
		this.timeslotSize = timeslotSize;
	}

	/**
	 * 
	 * @return systemLoggingLevel
	 */
	public String getSystemLoggingLevel() {
		return systemLoggingLevel;
	}

	/**
	 * 
	 * @param systemLoggingLevel
	 */
	public void setSystemLoggingLevel(String systemLoggingLevel) {
		this.systemLoggingLevel = systemLoggingLevel;
	}

	/**
	 * 
	 * @return datasetUpdateInterval
	 */
	public Integer getDatasetUpdateInterval() {
		return datasetUpdateInterval;
	}

	/**
	 * 
	 * @param datasetUpdateInterval
	 */
	public void setDatasetUpdateInterval(Integer datasetUpdateInterval) {
		this.datasetUpdateInterval = datasetUpdateInterval;
	}


}
