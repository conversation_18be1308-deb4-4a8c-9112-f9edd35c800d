/*
 * @(#)MaintenanceLog.java  20/12/05
 * 
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 * 
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */

package org.swallow.maintenance.model;


import org.apache.commons.lang.builder.EqualsBuilder;
import org.apache.commons.lang.builder.HashCodeBuilder;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.swallow.model.BaseObject;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;



public class MaintenanceLogAuth extends BaseObject implements Comparable{
	
/*    A negative integer if this is less than obj 
    Zero if this and obj are equivalent 
    A positive integer if this is greater than obj
   */

    public int compareTo(Object obj)
    {
        int retValue = 0;
        
        if(obj instanceof MaintenanceLogAuth)
        {
            MaintenanceLogAuth maintObj = (MaintenanceLogAuth)obj;
            if(logDate.getTime() > maintObj.getLogDate().getTime())
                retValue = 1;
            else if(logDate.getTime() < maintObj.getLogDate().getTime())
            	retValue = -1;
        }
        return retValue;
    }


    
	/**
	 * @return Returns the logDate_Date.
	 */
	public String getLogDate_Date() {
		return logDate_Date;
	}
	/**
	 * @param logDate_Date The logDate_Date to set.
	 */
	public void setLogDate_Date(String logDate_Date) {
		this.logDate_Date = logDate_Date;
	}
	/**
	 * @param fromDateAsString The fromDateAsString to set.
	 */
	public void setFromDateAsString(String fromDateAsString) {
		this.fromDateAsString = fromDateAsString;
	}
	/**
	 * @param logDate_Time The logDate_Time to set.
	 */
	public void setLogDate_Time(String logDate_Time) {
		this.logDate_Time = logDate_Time;
	}
	/**
	 * @param toDateAsString The toDateAsString to set.
	 */
	public void setToDateAsString(String toDateAsString) {
		this.toDateAsString = toDateAsString;
	}
	private final Log log = LogFactory.getLog(MaintenanceLogAuth.class);
	private Date fromDate;
	private Date toDate;
	private String fromDateAsString;
	private String toDateAsString;
	private String selectedTableName;
	private String logDate_Date;
	private String logDate_Time;
	private String hostId ;
	private Date logDate;
	private String userId;
	private String ipAddress;
	private String tableName;
	private String reference;
	private String columnName;
	private String action;
	private String oldValue;
	private String newValue;
	private Long mainSeqNo;
	private String dupaction;
	private Long maintEventId;
	
	
	
	public Long getMainSeqNo() {
		return mainSeqNo;
	}
	public void setMainSeqNo(Long mainSeqNo) {
		this.mainSeqNo = mainSeqNo;
	}
	
	public String getHostId() {
		return hostId;
	}
	public void setHostId(String hostId){
		this.hostId = hostId;
	}
	

	/**
	 * @return Returns the errDate_Time.
	 */
	public String getLogDate_Time() {
		SimpleDateFormat  sdf = new SimpleDateFormat("HH:mm:ss");
		return sdf.format(getLogDate());
		}

	
	/**
	 * @param action The action to set.
	 */
	public void setAction(String action) {
		
		this.action = action;
	}
	/**
	 * @return Returns the columnName.
	 */
	public String getColumnName() {
		return columnName;
	}
	/**
	 * @param columnName The columnName to set.
	 */
	public void setColumnName(String columnName) {
		this.columnName = columnName;
	}
	/**
	 * @return Returns the fromDate.
	 */
	public Date getFromDate() {
		return fromDate;
	}
	/**
	 * @param fromDate The fromDate to set.
	 */
	public void setFromDate(Date fromDate) {
		this.fromDate = fromDate;
	}
	/**
	 * @return Returns the fromDateAsString.
	 */
	public String getFromDateAsString() {
		return fromDateAsString;
	}

		
	/**
	 * @return Returns the newValue.
	 */
	public String getNewValue() {
		return newValue;
	}
	/**
	 * @param newValue The newValue to set.
	 */
	public void setNewValue(String newValue) {
		this.newValue = newValue;
	}
	/**
	 * @return Returns the oldValue.
	 */
	public String getOldValue() {
		return oldValue;
	}
	/**
	 * @param oldValue The oldValue to set.
	 */
	public void setOldValue(String oldValue) {
		this.oldValue = oldValue;
	}
	/**
	 * @return Returns the reference.
	 */
	public String getReference() {
		return reference;
	}
	/**
	 * @param reference The reference to set.
	 */
	public void setReference(String reference) {
		this.reference = reference;
	}
	/**
	 * @return Returns the tableName.
	 */
	public String getTableName() {
		return tableName;
	}
	/**
	 * @param tableName The tableName to set.
	 */
	public void setTableName(String tableName) {
		this.tableName = tableName;
	}
	/**
	 * @return Returns the toDate.
	 */
	public Date getToDate() {
		return toDate;
	}
	/**
	 * @param toDate The toDate to set.
	 */
	public void setToDate(Date toDate) {
		this.toDate = toDate;
	}
	/**
	 * @return Returns the toDateAsString.
	 */
	public String getToDateAsString() {
		return toDateAsString;
	}

		/**
	 * @return Returns the ipAddress.
	 */
	public String getIpAddress() {
		return ipAddress;
	}
	/**
	 * @param ipAddress The ipAddress to set.
	 */
	public void setIpAddress(String ipAddress) {
		this.ipAddress = ipAddress;
	}
	/**
	 * @return Returns the selectedTableName.
	 */
	public String getSelectedTableName() {
		return selectedTableName;
	}
	/**
	 * @param selectedTableName The selectedTableName to set.
	 */
	public void setSelectedTableName(String selectedTableName) {
		this.selectedTableName = selectedTableName;
	}
	/**
	 * @return Returns the logDate.
	 */
	public Date getLogDate() {
		return logDate;
	}
	/**
	 * @param logDate The logDate to set.
	 */
	public void setLogDate(Date logDate) {
		this.logDate = logDate;
	}
	/**
	 * @return Returns the userId.
	 */
	public String getuserId() {
		return userId;
	}
	/**
	 * @param userId The userId to set.
	 */
	public void setuserId(String userId) {
		this.userId = userId;
	}
	/**
	 * @return Returns the action.
	 */
	public String getAction() {
		return action;
	}
	
	/**
	 * @return Returns the dupaction.
	 */
	public String getDupaction() {
		log.debug("dupaction======>"+action);
		 if( action!=null){
				if( action.equalsIgnoreCase("d")){
				 
					dupaction="Deleted"; 
					 
				}else if (( action.equalsIgnoreCase("c")) || ( action.equalsIgnoreCase("u"))){
				 
					dupaction="Changed"; 
					 
				}else if (action.equalsIgnoreCase("i")){
					 
					dupaction="Added";
				}		 
		   }
		 log.debug("dupaction==1111111111111111111111====>"+dupaction);
		return dupaction;
	}
	/**
	 * @param dupaction The dupaction to set.
	 */
	public void setDupaction(String dupaction) {
		setAction(dupaction);
		this.dupaction = dupaction;
	}

    public boolean equals(Object o) {
        boolean retVal = false;
        if(o instanceof MaintenanceLogAuth)
         {
            MaintenanceLogAuth oMaintLog = (MaintenanceLogAuth)o;
            
            if(hostId != null && oMaintLog.getHostId() != null && oMaintLog.getHostId().equals(hostId) &&
               ipAddress != null && oMaintLog.getIpAddress() != null && oMaintLog.getIpAddress().equals(ipAddress) &&               
               userId != null && oMaintLog.getuserId() != null && oMaintLog.getuserId().equals(userId) &&
               reference != null && oMaintLog.getReference() != null && oMaintLog.getReference().equals(reference) &&
               logDate != null && oMaintLog.getLogDate() != null && oMaintLog.getLogDate().equals(logDate) && 
			   action != null && oMaintLog.getAction() != null && oMaintLog.getAction().equals(action)
               )
            {
                retVal = true;
            }
            
         }
        
        return retVal;
            
    }

    public int hashCode() {
        
        int hashCode =  0;
        if(hostId != null) hashCode += hostId.hashCode();
        if(ipAddress != null) hashCode += ipAddress.hashCode();
        if(userId != null) hashCode += userId.hashCode();
        if(reference != null) hashCode += reference.hashCode();
        if(logDate != null) hashCode += logDate.hashCode();        
        if(action != null) hashCode += action.hashCode();
        
        return hashCode;
            
    }



	public Long getMaintEventId() {
		return maintEventId;
	}



	public void setMaintEventId(Long maintEventId) {
		this.maintEventId = maintEventId;
	}	
	 
}
