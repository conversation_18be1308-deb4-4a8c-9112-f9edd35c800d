
package org.swallow.maintenance.model;

import java.util.Hashtable;

import org.swallow.model.BaseObject;

public class AccountSweepBalanceGroup extends BaseObject implements
org.swallow.model.AuditComponent{
	private static final long serialVersionUID = 1L;
	// To hold short name (key) and its description (value)
	public static Hashtable<String, String> logTable = new Hashtable<String, String>();
	private Integer uniqueId = null;
	private String valid = null;

	/**
	 * Static block puts short name and its description in a map
	 */
	static {
		logTable.put("hostId", "Host Id");
		logTable.put("entityId", "Entity Id");
		logTable.put("accountId", "Account Id");
		logTable.put("sweepAccountId", "Sweep Account Id");
	}
	 
	private Id id = new Id();
	
	public Id getId() {
		return id;
	}
	public void setId(Id id) {
		this.id = id;
	}
	
	public static class Id extends BaseObject{
		private String hostId = null;
		private String entityId = null;
		private String accountId = null;
		private String sweepAccountId = null;
		public Id() {};
		public Id(String hostId, String entityId, String accountId, String sweepAccountId) {
			this.hostId = hostId;
			this.entityId = entityId;
			this.accountId = accountId;
			this.sweepAccountId = sweepAccountId;
		};
		public String getHostId() {
			return hostId;
		}
		public void setHostId(String hostId) {
			this.hostId = hostId;
		}
		public String getEntityId() {
			return entityId;
		}
		public void setEntityId(String entityId) {
			this.entityId = entityId;
		}
		public String getAccountId() {
			return accountId;
		}
		public void setAccountId(String accountId) {
			this.accountId = accountId;
		}
		public String getSweepAccountId() {
			return sweepAccountId;
		}
		public void setSweepAccountId(String sweepAccountId) {
			this.sweepAccountId = sweepAccountId;
		}
	}
	
	public Integer getUniqueId() {
		return uniqueId;
	}
	public void setUniqueId(Integer uniqueId) {
		this.uniqueId = uniqueId;
	}
	public String getValid() {
		return valid;
	}
	public void setValid(String valid) {
		this.valid = valid;
	}
	
	
	
}
