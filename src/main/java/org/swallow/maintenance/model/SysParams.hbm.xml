<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE hibernate-mapping PUBLIC
        "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
        "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">
<hibernate-mapping>
    <class name="org.swallow.maintenance.model.SysParams" table="S_SYSTEM_PARAMETERS">
        <id name="hostId" column="HOST_ID" unsaved-value="null">
		<generator class="assigned"/>
		</id>
		<property name="dateFormat" column="DATE_FORMAT" not-null="false"/>	
		<property name="amountDelimiter" column="AMOUNT_DELIMITER" not-null="false"/>	
		<property name="testDate" column="TEST_DATE" not-null="false"/>	
		<property name="systemLog" column="SYSTEM_LOG" not-null="false"/>	
		<property name="maintenanceLog" column="MAINTENANCE_LOG" not-null="false"/>	
		<property name="errorLog" column="ERROR_LOG" not-null="false"/>	
		<!--Code added by <PERSON><PERSON><PERSON> on 17-01-2012:1687: Parameterise retention time for suppressed and rejected messages -->
		<property name="rejectedSuppressedInput" column="RETAIN_SUPP_REJ_MSG" not-null="false"/>
		<property name="updateDate" column="UPDATE_DATE" not-null="false"/>	
		<property name="updateUser" column="UPDATE_USER" not-null="false"/>		
		<property name="sysTimeZone" column="SYSTEM_TZNAME" not-null="false"/>	
				
    </class>
</hibernate-mapping>
