<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE hibernate-mapping PUBLIC
        "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
        "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">

<hibernate-mapping>
    <class name="org.swallow.maintenance.model.EditableData" table="P_EDITABLE_DATA">
    <composite-id name="id" class="org.swallow.maintenance.model.EditableData$Id" unsaved-value="any">
        <key-property name="hostId" access="field" column="HOST_ID"/>
        <key-property name="entityId" access="field" column="ENTITY_ID" />
        <key-property name="movementField" access="field" column="MOVEMENT_FIELD"/>
    </composite-id>
    	<property name="editable" column="EDITABLE" not-null="false"/>
    </class>
</hibernate-mapping> 