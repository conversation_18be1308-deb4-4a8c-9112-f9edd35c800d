/*
 * @(#)KeyWords.java 
 * 
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 * 
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.maintenance.model;

import org.swallow.model.BaseObject;

public class KeyWords  extends BaseObject {

	/**
	 * 
	 */
	public KeyWords() {
		super();
		// TODO Auto-generated constructor stub
	}
	private Id id = new Id();
	
	
	
	/**
	 * @param id
	 */
	public KeyWords(Id id) {
		super();
		this.id = id;
	}
	/**
	 * @return Returns the id.
	 */
	public Id getId() {
		return id;
	}
	/**
	 * @param id The id to set.
	 */
	public void setId(Id id) {
		this.id = id;
	}
	public static class Id extends BaseObject{
		private String keyWord;	
		private String tableName;	
		private String mappedField;
		
		
		
		/**
		 * @param keyWord
		 * @param mappedField
		 */
		public Id(String keyWord, String mappedField) {
			super();
			this.keyWord = keyWord;
			this.mappedField = mappedField;
		}
		/**
		 * 
		 */
		public Id() {
			super();
			// TODO Auto-generated constructor stub
		}
		/**
		 * @return Returns the keyWord.
		 */
		public String getKeyWord() {
			return keyWord;
		}
		/**
		 * @param keyWord The keyWord to set.
		 */
		public void setKeyWord(String keyWord) {
			this.keyWord = keyWord;
		}
		/**
		 * @return Returns the mappedField.
		 */
		public String getMappedField() {
			return mappedField;
		}
		/**
		 * @param mappedField The mappedField to set.
		 */
		public void setMappedField(String mappedField) {
			this.mappedField = mappedField;
		}
		/**
		 * @return Returns the tableName.
		 */
		public String getTableName() {
			return tableName;
		}
		/**
		 * @param tableName The tableName to set.
		 */
		public void setTableName(String tableName) {
			this.tableName = tableName;
		}
	}
		
		

}
