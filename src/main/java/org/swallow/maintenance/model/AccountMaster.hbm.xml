<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE hibernate-mapping PUBLIC
        "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
        "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">
<hibernate-mapping>
 <class name="org.swallow.maintenance.model.AccountMaster" table="P_ACCOUNT">
  <composite-id class="org.swallow.maintenance.model.AccountMaster$Id"
   name="id" unsaved-value="any">
   <key-property name="entityId" access="field" column="ENTITY_ID"/>
   <key-property name="hostId" access="field" column="HOST_ID"/>
   <key-property name="accountId" access="field" column="ACCOUNT_ID"/>
  </composite-id>
  <property name="accountName" column="ACCOUNT_NAME"/>
  <property name="currencyCode" column="CURRENCY_CODE"/>
  <property name="accountType" column="ACCOUNT_TYPE"/>
 </class>
</hibernate-mapping>
