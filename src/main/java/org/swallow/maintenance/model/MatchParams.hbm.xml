<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE hibernate-mapping PUBLIC
        "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
        "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">
<hibernate-mapping>
 <class name="org.swallow.maintenance.model.MatchParams" table="P_MATCH_PARAMETERS">
  <composite-id class="org.swallow.maintenance.model.MatchParams$Id"
   name="id" unsaved-value="any">
   <key-property name="paramCode" type="java.lang.Integer"
    column="PARAMETER_ID" length="3"/>
  </composite-id>
  <property name="paramDesc" type="java.lang.String"
   column="PARAMETER_DESC" length="30" not-null="true"/>
  <property name="updateDate" type="java.sql.Timestamp"
   column="UPDATE_DATE" length="7"/>
  <property name="updateUser" type="java.lang.String"
   column="UPDATE_USER" length="12"/>
 </class>
</hibernate-mapping>
