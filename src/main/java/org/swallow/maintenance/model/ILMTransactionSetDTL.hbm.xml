<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE hibernate-mapping PUBLIC
        "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
        "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">

<hibernate-mapping>
	<class name="org.swallow.maintenance.model.ILMTransactionSetDTL"
		table="P_ILM_SCENARIO_XTRA_TXN_DTL">

		<composite-id class="org.swallow.maintenance.model.ILMTransactionSetDTL$Id"
			name="id" unsaved-value="any">
			<key-property name="hostId" column="HOST_ID" access="field" />
			<key-property name="entityId" column="ENTITY_ID" access="field" />
			<key-property name="currencyCode" column="CURRENCY_CODE"
				access="field" />
			<key-property name="txnSetId" column="TXN_SET_ID"
				access="field" />
			<key-property name="accountId" column="ACCOUNT_ID"
				access="field" />
			<key-property name="time" column="TXN_TIME" access="field" />
		</composite-id>

		<property name="credits" column="CREDITS" not-null="false" />
		<property name="debits" column="DEBITS" not-null="false" />
		<property name="description" column="TXN_DESC" not-null="false" />


	</class>
</hibernate-mapping>
