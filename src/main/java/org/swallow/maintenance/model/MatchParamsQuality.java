/*
 * Created on Dec 2, 2005
 *
 * TODO To change the template for this generated file go to
 * Window - Preferences - Java - Code Style - Code Templates
 */
package org.swallow.maintenance.model;


import org.swallow.util.*;
import java.io.Serializable;
import java.util.*;
/**
 * <AUTHOR>
 *
 * TODO To change the template for this generated type comment go to
 * Window - Preferences - Java - Code Style - Code Templates
 */
public class MatchParamsQuality implements Serializable{

	private String paramDesc;
	private Integer paramCode;
	private String matchQualityAHTML;
	private String matchQualityBHTML;
	private String matchQualityCHTML;
	private String matchQualityDHTML;
	private String matchQualityEHTML;
		
	/**
	 * 
	 */
	public MatchParamsQuality() {
	}
	
	
	/**
	 * @return Returns the matchQualityAHTML.
	 */
	public String getMatchQualityAHTML() {
		return matchQualityAHTML;
	}
	/**
	 * @param matchQualityAHTML The matchQualityAHTML to set.
	 */
	public void setMatchQualityAHTML(String matchQualityAHTML) {
		this.matchQualityAHTML = matchQualityAHTML;
	}
	/**
	 * @return Returns the matchQualityBHTML.
	 */
	public String getMatchQualityBHTML() {
		return matchQualityBHTML;
	}
	/**
	 * @param matchQualityBHTML The matchQualityBHTML to set.
	 */
	public void setMatchQualityBHTML(String matchQualityBHTML) {
		this.matchQualityBHTML = matchQualityBHTML;
	}
	/**
	 * @return Returns the matchQualityCHTML.
	 */
	public String getMatchQualityCHTML() {
		return matchQualityCHTML;
	}
	/**
	 * @param matchQualityCHTML The matchQualityCHTML to set.
	 */
	public void setMatchQualityCHTML(String matchQualityCHTML) {
		this.matchQualityCHTML = matchQualityCHTML;
	}
	/**
	 * @return Returns the matchQualityDHTML.
	 */
	public String getMatchQualityDHTML() {
		return matchQualityDHTML;
	}
	/**
	 * @param matchQualityDHTML The matchQualityDHTML to set.
	 */
	public void setMatchQualityDHTML(String matchQualityDHTML) {
		this.matchQualityDHTML = matchQualityDHTML;
	}
	/**
	 * @return Returns the matchQualityEHTML.
	 */
	public String getMatchQualityEHTML() {
		return matchQualityEHTML;
	}
	/**
	 * @param matchQualityEHTML The matchQualityEHTML to set.
	 */
	public void setMatchQualityEHTML(String matchQualityEHTML) {
		this.matchQualityEHTML = matchQualityEHTML;
	}
	/**
	 * @return Returns the paramCode.
	 */
	public Integer getParamCode() {
		return paramCode;
	}
	/**
	 * @param paramCode The paramCode to set.
	 */
	public void setParamCode(Integer paramCode) {
		this.paramCode = paramCode;
	}
	/**
	 * @return Returns the paramDesc.
	 */
	public String getParamDesc() {
		return paramDesc;
	}
	/**
	 * @param paramDesc The paramDesc to set.
	 */
	public void setParamDesc(String paramDesc) {
		this.paramDesc = paramDesc;
	}
	}
