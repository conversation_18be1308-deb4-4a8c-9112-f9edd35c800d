<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE hibernate-mapping PUBLIC
        "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
        "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">
<hibernate-mapping>

	<class
		name="org.swallow.maintenance.model.ForecastMonitorTemplateColSrc"
		table="P_FCAST_TEMPLATE_COL_SRC">

		<composite-id name="id"
			class="org.swallow.maintenance.model.ForecastMonitorTemplateColSrc$Id">

			<key-property name="hostId" access="field" column="HOST_ID" />

			<key-property name="templateId" access="field"
				column="TEMPLATE_ID" />

			<key-property name="columnId" access="field"
				column="COLUMN_ID" />

			<key-property name="sourceType" access="field"
				column="SOURCE_TYPE" />

			<key-property name="sourceId" access="field"
				column="SOURCE_ID" />
		</composite-id>

		<property name="userId" column="USER_ID" not-null="false" />
		<property name="entityId" column="ENTITY_ID" not-null="false" />


		<property name="multiplier" column="MULTIPLIER"
			not-null="false" />


		<many-to-one name="forecastMonitorTemplateCol" update="false" lazy="false"
			insert="false" cascade="none"
			class="org.swallow.maintenance.model.ForecastMonitorTemplateCol"
			not-null="true" outer-join="true"
			foreign-key="FK_P_FCAST_TEMPLATE_COL_SRC">
			<column name="HOST_ID" />
			<column name="TEMPLATE_ID" />
			<column name="COLUMN_ID" />
		</many-to-one>

	</class>
</hibernate-mapping>
