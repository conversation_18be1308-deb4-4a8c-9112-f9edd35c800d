/*
 * Created on Dec 2, 2005
 *
 * TODO To change the template for this generated file go to
 * Window - Preferences - Java - Code Style - Code Templates
 */
package org.swallow.maintenance.model;

import org.swallow.model.BaseObject;

import java.util.Date;

/**
 * <AUTHOR>
 *
 * TODO To change the template for this generated type comment go to
 * Window - Preferences - Java - Code Style - Code Templates
 */
public class CurrencyMaster extends BaseObject {
private String currencyCode;
private String currencyName;
private String decimalPlaces;
private Date updateDate;
private String updateUser;
private String ccyTimeZone= null;

/**
 * @return Returns the currencyCode.
 */
public String getCurrencyCode() {
	return currencyCode;
}
/**
 * @param currencyCode The currencyCode to set.
 */
public void setCurrencyCode(String currencyCode) {
	this.currencyCode = currencyCode;
}
/**
 * @return Returns the currencyName.
 */
public String getCurrencyName() {
	return currencyName;
}
/**
 * @param currencyName The currencyName to set.
 */
public void setCurrencyName(String currencyName) {
	this.currencyName = currencyName;
}
/**
 * @return Returns the decimalPlaces.
 */
public String getDecimalPlaces() {
	return decimalPlaces;
}
/**
 * @param decimalPlaces The decimalPlaces to set.
 */
public void setDecimalPlaces(String decimalPlaces) {
	this.decimalPlaces = decimalPlaces;
}
/**
 * @return Returns the updateDate.
 */
public Date getUpdateDate() {
	return updateDate;
}
/**
 * @param updateDate The updateDate to set.
 */
public void setUpdateDate(Date updateDate) {
	this.updateDate = updateDate;
}
/**
 * @return Returns the updateUser.
 */
public String getUpdateUser() {
	return updateUser;
}
/**
 * @param updateUser The updateUser to set.
 */
public void setUpdateUser(String updateUser) {
	this.updateUser = updateUser;
}

/**
 * @return Returns the ccyTimeZone.
 */
public String getCcyTimeZone() {
	return ccyTimeZone;
}

/**
 * @param ccyTimeZone The ccyTimeZone to set.
 */
public void setCcyTimeZone(String sysTimeZone) {
	this.ccyTimeZone = sysTimeZone;
}

}
