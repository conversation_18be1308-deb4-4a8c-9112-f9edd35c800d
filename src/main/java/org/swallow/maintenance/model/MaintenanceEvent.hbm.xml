<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE hibernate-mapping PUBLIC
        "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
        "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">
<hibernate-mapping>
	<class name="org.swallow.maintenance.model.MaintenanceEvent"
		table="S_MAINT_EVENT">
		<id name="maintEventId" column="MAINT_EVENT_ID" type="long">
			<generator class="sequence">
				<param name="sequence_name">SEQ_S_MAINT_EVENT</param>
			</generator>
		</id>
		<property name="maintFacilityId" not-null="true" column="MAINT_FACILITY_ID" />
		<property name="action" not-null="true" column="ACTION" />
		<property name="recordId" not-null="true" column="RECORD_ID" />
		<property name="status" not-null="true" column="STATUS" />
		<property name="requestUser" not-null="false"	column="REQUEST_USER" />
			<property name="requestDate" not-null="false"			column="REQUEST_DATE" />
		<property name="authUser" not-null="false" column="AUTH_USER" />
		<property name="authDate" not-null="false" column="AUTH_DATE" />
		<property name="prevId" not-null="false" column="PREV_ID" />
		<property name="nextId" not-null="false" column="NEXT_ID" />
	</class>
</hibernate-mapping>
