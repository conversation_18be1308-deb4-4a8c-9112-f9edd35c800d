package org.swallow.maintenance.model;

import java.util.Hashtable;

import org.swallow.model.BaseObject;

public class AccountAttributeFuncGroup extends BaseObject implements org.swallow.model.AuditComponent{


	private static final long serialVersionUID = 1L;
	private Id id = new Id();
	private Integer displayOrder;
	private String contributeTotal;
	
	private AccountAttributeHDR accountAttributeHDR = new AccountAttributeHDR();
	
	private FunctionalGroup functionalGroup = new FunctionalGroup();
	
	public static Hashtable logTable = new Hashtable();
	static {
		logTable.put("displayOrder", "Display Order");
		logTable.put("contributeTotal", "Contribute Total");
	}
	
	public static class Id extends BaseObject{

		private static final long serialVersionUID = 1L;
		private String functionalGroup = null;
		private String attributeId = null;
		
		public Id() {}
		
		public Id(String functionalGroup, String attributeId) {
			this.functionalGroup = functionalGroup;
			this.attributeId = attributeId;
		}
		
		public String getFunctionalGroup() {
			return functionalGroup;
		}

		public void setFunctionalGroup(String functionalGroup) {
			this.functionalGroup = functionalGroup;
		}
		
		public String getAttributeId() {
			return attributeId;
		}
		
		public void setAttributeId(String attributeId) {
			this.attributeId = attributeId;
		}
	}
	
	public Id getId() {
		return id;
	}
	
	public void setId(Id id) {
		this.id = id;
	}
	
	public Integer getDisplayOrder() {
		return displayOrder;
	}
	
	public void setDisplayOrder(Integer displayOrder) {
		this.displayOrder = displayOrder;
	}
	
	public AccountAttributeHDR getAccountAttributeHDR() {
		return accountAttributeHDR;
	}
	
	public void setAccountAttributeHDR(AccountAttributeHDR accountAttributeHDR) {
		this.accountAttributeHDR = accountAttributeHDR;
	}
	
	public FunctionalGroup getFunctionalGroup() {
		return functionalGroup;
	}
	
	public void setFunctionalGroup(FunctionalGroup functionalGroup) {
		this.functionalGroup = functionalGroup;
	}
	
	public String getContributeTotal() {
		return contributeTotal;
	}

	public void setContributeTotal(String contributeTotal) {
		this.contributeTotal = contributeTotal;
	}
	
	
}
