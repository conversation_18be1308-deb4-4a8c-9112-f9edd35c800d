/*
 * @(#)PersonalCurrency.java 1.0 09/07/07
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.maintenance.model;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.HashMap;
import java.util.Hashtable;

import org.swallow.model.BaseObject;

/**
 * PersonalCurrency.java<br>
 * 
 * This java bean has getters and setters for PersonalCurrency details
 * 
 * <AUTHOR>
 * 
 */
public class PersonalCurrency extends BaseObject implements
		org.swallow.model.AuditComponent {

	/**
	 * containing all the getter & setting methods
	 */
	public PersonalCurrency() {
		super();
	}

	/** Default version id */
	private static final long serialVersionUID = 1L;
	// Holds the last updated date
	private Date updateDate = null;
	// Holds the currency name
	private String currencyName = null;
	// Holds the Entity Id
	private String entityId = null;
	// Holds the Order of Display value
	private Integer priorityOrder = null;
	// Holds the User access flag value
	private String accessFlag = null;
	// Instantiating the Id object - which holds the primary keys
	private Id id = new Id();
	// Personal currency Records
	private Collection<PersonalCurrency> personalCurrencyRecords = null;
	// Map currency details
	private HashMap<String, ArrayList<PersonalCurrency>> hashRecords = null;
	// Map Entity flag
	private HashMap<String, String> hashEntityFlag = null;
	// Entity flag
	private String entityFlag = null;
	// Entity all flag
	private String entityAllFlag = null;

	private Collection<PersonalCurrency> entityRecords = null;

	/*
	 * code modified for the problem of Server Log ING NoSuchFieldException:
	 * logTable
	 */
	public static Hashtable<String, String> logTable = new Hashtable<String, String>();
	// static initializer that holds the currency name and order of display
	// values
	static {
		logTable.put("currencyName", "Currency Name");
		logTable.put("priorityOrder", "Priority Order");
	}

	public static class Id extends BaseObject {
		/** Default version id */
		private static final long serialVersionUID = 1L;
		// Holds the host id
		private String hostId = null;
		// Holds the user id
		private String userId = null;
		// Holds the currency code
		private String currencyCode = null;
		// Holds the screen id
		private String screenId = null;

		/**
		 * @return the hostId
		 */
		public String getHostId() {
			return hostId;
		}

		/**
		 * @param hostId
		 *            the hostId to set
		 */
		public void setHostId(String hostId) {
			this.hostId = hostId;
		}

		/**
		 * @return the userId
		 */
		public String getUserId() {
			return userId;
		}

		/**
		 * @param userId
		 *            the userId to set
		 */
		public void setUserId(String userId) {
			this.userId = userId;
		}

		/**
		 * @return the currencyCode
		 */
		public String getCurrencyCode() {
			return currencyCode;
		}

		/**
		 * @param currencyCode
		 *            the currencyCode to set
		 */
		public void setCurrencyCode(String currencyCode) {
			this.currencyCode = currencyCode;
		}

		/**
		 * @return the screenId
		 */
		public String getScreenId() {
			return screenId;
		}

		/**
		 * @param screenId
		 *            the screenId to set
		 */
		public void setScreenId(String screenId) {
			this.screenId = screenId;
		}
	}

	/**
	 * @return the updateDate
	 */
	public Date getUpdateDate() {
		return updateDate;
	}

	/**
	 * @param updateDate
	 *            the updateDate to set
	 */
	public void setUpdateDate(Date updateDate) {
		this.updateDate = updateDate;
	}

	/**
	 * @return the currencyName
	 */
	public String getCurrencyName() {
		return currencyName;
	}

	/**
	 * @param currencyName
	 *            the currencyName to set
	 */
	public void setCurrencyName(String currencyName) {
		this.currencyName = currencyName;
	}

	/**
	 * @return the priorityOrder
	 */
	public Integer getPriorityOrder() {
		return priorityOrder;
	}

	/**
	 * @param priorityOrder
	 *            the priorityOrder to set
	 */
	public void setPriorityOrder(Integer priorityOrder) {
		this.priorityOrder = priorityOrder;
	}

	/**
	 * @return the id
	 */
	public Id getId() {
		return id;
	}

	/**
	 * @param id
	 *            the id to set
	 */
	public void setId(Id id) {
		this.id = id;
	}

	/**
	 * @return the entityId
	 */
	public String getEntityId() {
		return entityId;
	}

	/**
	 * @param entityId
	 *            the entityId to set
	 */
	public void setEntityId(String entityId) {
		this.entityId = entityId;
	}

	/**
	 * @return the personalCurrencyRecords
	 */
	public Collection<PersonalCurrency> getPersonalCurrencyRecords() {
		return personalCurrencyRecords;
	}

	/**
	 * @param personalCurrencyRecords
	 *            the personalCurrencyRecords to set
	 */
	public void setPersonalCurrencyRecords(
			Collection<PersonalCurrency> personalCurrencyRecords) {
		this.personalCurrencyRecords = personalCurrencyRecords;
	}

	/**
	 * @return the entityFlag
	 */
	public String getEntityFlag() {
		return entityFlag;
	}

	/**
	 * @param entityFlag
	 *            the entityFlag to set
	 */
	public void setEntityFlag(String entityFlag) {
		this.entityFlag = entityFlag;
	}

	/**
	 * @return the hashRecords
	 */
	public HashMap<String, ArrayList<PersonalCurrency>> getHashRecords() {
		return hashRecords;
	}

	/**
	 * @param hashRecords
	 *            the hashRecords to set
	 */
	public void setHashRecords(
			HashMap<String, ArrayList<PersonalCurrency>> hashRecords) {
		this.hashRecords = hashRecords;
	}

	/**
	 * @return the entityRecords
	 */
	public Collection<PersonalCurrency> getEntityRecords() {
		return entityRecords;
	}

	/**
	 * @param entityRecords
	 *            the entityRecords to set
	 */
	public void setEntityRecords(Collection<PersonalCurrency> entityRecords) {
		this.entityRecords = entityRecords;
	}

	/**
	 * @return the entityAllFlag
	 */
	public String getEntityAllFlag() {
		return entityAllFlag;
	}

	/**
	 * @param entityAllFlag
	 *            the entityAllFlag to set
	 */
	public void setEntityAllFlag(String entityAllFlag) {
		this.entityAllFlag = entityAllFlag;
	}

	/**
	 * @return the hashEntityFlag
	 */
	public HashMap<String, String> getHashEntityFlag() {
		return hashEntityFlag;
	}

	/**
	 * @param hashEntityFlag
	 *            the hashEntityFlag to set
	 */
	public void setHashEntityFlag(HashMap<String, String> hashEntityFlag) {
		this.hashEntityFlag = hashEntityFlag;
	}

	/**
	 * @return the accessFlag
	 */
	public String getAccessFlag() {
		return accessFlag;
	}

	/**
	 * @param accessFlag
	 *            the accessFlag to set
	 */
	public void setAccessFlag(String accessFlag) {
		this.accessFlag = accessFlag;
	}

}
