<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE hibernate-mapping PUBLIC
        "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
        "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">

<hibernate-mapping>
    <class name="org.swallow.maintenance.model.ILMCcyParameters" table="P_ILM_CCY_PARAMETERS" >
		<composite-id class="org.swallow.maintenance.model.ILMCcyParameters$Id" name="id" unsaved-value="any">
			<key-property name="hostId" column="HOST_ID" access="field"/>
			<key-property name="entityId" column="ENTITY_ID" access="field"/>
			<key-property name="currencyCode" column="CURRENCY_CODE" access="field"/>
		</composite-id>
		<property name="globalGroupId"  column="GLOBAL_GROUP_ID"/>
		<property name="altGlobalGroupId"  column="ALT_GLOBAL_GROUP_ID"/>
		<property name="defaultMapTime" column="DEFAULT_MAP_TIME" not-null="false"/>
		<property name="lvpsName" column="LVPS_NAME" not-null="false"/>
		<property name="centralBankGroupId" column="CENTRAL_BANK_GROUP_ID" not-null="false"/>
		<property name="primaryAccountId" column="PRIMARY_ACCOUNT_ID" not-null="false"/>
		<property name="clearingStartTime" column="CLEARING_START_TIME" not-null="true"/>
		<property name="clearingEndTime" column="CLEARING_END_TIME" not-null="true"/>
	</class>
</hibernate-mapping>