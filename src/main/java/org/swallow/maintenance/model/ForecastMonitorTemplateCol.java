/**
 * @(#)ForecastMonitorTemplateCol.java 1.0 May 25, 2011
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.maintenance.model;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import org.swallow.model.BaseObject;

/**
 * ForecastMonitorTemplateCol.java
 * 
 * <AUTHOR>
 * 
 */
public class ForecastMonitorTemplateCol extends BaseObject {
	

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;

	// Long Variable to hold ordinalPos
	private int ordinalPos;

	// String Variable to hold columnDisplayName
	private String columnDisplayName = null;

	// String Variable to hold columnDescription
	private String columnDescription = null;

	// String Variable to hold columnType
	private String columnType = null;

	// String Variable to hold columnFormula
	private String columnFormula = null;

	// String Variable to hold userEditable
	private String userEditable = null;
	
	// Collection Instance to hold ForecastMonitorTemplateCol
	private List<ForecastMonitorTemplateColSrc> listForecastMonitorTemplateColSrc = new ArrayList<ForecastMonitorTemplateColSrc>();;
	// Collection Instance to hold ForecastMonitorTemplateCol for deletion
	private List<ForecastMonitorTemplateColSrc> deleteForecastMonitorTemplateColSrcList = new ArrayList<ForecastMonitorTemplateColSrc>();
	// Collection Instance to hold User
	private List<String> listUser = null;
	
	// String variable to hold data grid color
	private String rowColor = null;
	
	// String variable to hold data grid modified value
	private String modifiedValue = null;
	
	// Long variable to hold data grid multiplier
	private BigDecimal multiplier = null;
	
	// String variable to hold userId
	private String userId = null;
	//Holds the list of source type selected
	private String[] columnSourceType = null;
	//Holds the entity of the selected source
	private String[] entity = null;
	// Holds the selected source id
	private String[] sourceId = null;
	// Holds the selected source name
	private String[] sourceName = null;
	// Holds the selected sources multipliers
	private String[] sourceMultiplier = null;
	// Holds the contribute to total multiplier value
	private String totalMultiplier = null;
	
	
	
	// Long variable to hoold enterd row id
	Long rowId = null;

	// ID instance
	private Id id = new Id();

	// Id Class to hold primary key values
	public static class Id extends BaseObject {

		/**
		 * 
		 */
		private static final long serialVersionUID = 1L;
		// String variable to hold hostId
		private String hostId = null;
		
		// String variable to hold templateId
		private String templateId = null;
		// String variable to hold columnId
		private String columnId = null;
		// default constructor
		public Id() {
		}

		/**
		 * Constructor to set forign key values
		 * 
		 * @param hostId
		 * @param templateId
		 * @param columnId
		 * @return
		 */
		public Id(String hostId, String templateId,String columnId) {
			this.hostId = hostId;
			this.templateId = templateId;
			this.columnId = columnId;
		}

		/**
		 * Method to Get templateId
		 * 
		 * @return templateId as String
		 */
		public String getTemplateId() {
			return templateId;
		}

		/**
		 * Method to set templateId
		 * 
		 * @param templateId
		 * @return
		 */
		public void setTemplateId(String templateId) {
			this.templateId = templateId;
		}

		/**
		 * Method to Get hostId
		 * 
		 * @return hostId as String
		 */
		public String getHostId() {
			return hostId;
		}

		/**
		 * Method to set hostId
		 * 
		 * @param hostId
		 * @return
		 */
		public void setHostId(String hostId) {
			this.hostId = hostId;
		}

		

		/**
		 * Method to Get columnId
		 * @return columnId as String
		 */
		public String getColumnId() {
			return columnId;
		}

		/**
		 * Method to set columnId
		 * @param columnId 
		 * @return 
		 */
		public void setColumnId(String columnId) {
			this.columnId = columnId;
		}

	}

	/**
	 * Getter method for id
	 * 
	 * @return Id - id
	 */
	public Id getId() {
		return id;
	}

	/**
	 * Setter method for id
	 * 
	 * @param id
	 * @return
	 */
	public void setId(Id id) {
		this.id = id;
	}

	/**
	 * Method to Get ordinalPos
	 * @return ordinalPos as Long
	 */
	public int getOrdinalPos() {
		return ordinalPos;
	}

	/**
	 * Method to set ordinalPos
	 * @param ordinalPos 
	 * @return 
	 */
	public void setOrdinalPos(int ordinalPos) {
		this.ordinalPos = ordinalPos;
	}

	/**
	 * Method to Get columnDisplayName
	 * @return columnDisplayName as String
	 */
	public String getColumnDisplayName() {
		return columnDisplayName;
	}

	/**
	 * Method to set columnDisplayName
	 * @param columnDisplayName 
	 * @return 
	 */
	public void setColumnDisplayName(String columnDisplayName) {
		this.columnDisplayName = columnDisplayName;
	}

	/**
	 * Method to Get columnDescription
	 * @return columnDescription as String
	 */
	public String getColumnDescription() {
		return columnDescription;
	}

	/**
	 * Method to set columnDescription
	 * @param columnDescription 
	 * @return 
	 */
	public void setColumnDescription(String columnDescription) {
		this.columnDescription = columnDescription;
	}

	/**
	 * Method to Get columnType
	 * @return columnType as String
	 */
	public String getColumnType() {
		return columnType;
	}

	/**
	 * Method to set columnType
	 * @param columnType 
	 * @return 
	 */
	public void setColumnType(String columnType) {
		this.columnType = columnType;
	}

	/**
	 * Method to Get columnFormula
	 * @return columnFormula as String
	 */
	public String getColumnFormula() {
		return columnFormula;
	}

	/**
	 * Method to set columnFormula
	 * @param columnFormula 
	 * @return 
	 */
	public void setColumnFormula(String columnFormula) {
		this.columnFormula = columnFormula;
	}

	/**
	 * Method to Get userEditable
	 * @return userEditable as String
	 */
	public String getUserEditable() {
		return userEditable;
	}

	/**
	 * Method to set userEditable
	 * @param userEditable 
	 * @return 
	 */
	public void setUserEditable(String userEditable) {
		this.userEditable = userEditable;
	}

	/**
	 * Method to Get listForecastMonitorTemplateColSrc
	 * @return listForecastMonitorTemplateCol as List<ForecastMonitorTemplateColSrc>
	 */
	public List<ForecastMonitorTemplateColSrc> getListForecastMonitorTemplateColSrc() {
		return listForecastMonitorTemplateColSrc;
	}

	/**
	 * Method to set listForecastMonitorTemplateColSrc
	 * @param listForecastMonitorTemplateColSrc 
	 * @return 
	 */
	public void setListForecastMonitorTemplateColSrc(
			List<ForecastMonitorTemplateColSrc> listForecastMonitorTemplateColSrc) {
		this.listForecastMonitorTemplateColSrc = listForecastMonitorTemplateColSrc;
	}

	/**
	 * Method to Get listUser
	 * @return listUser as List<String>
	 */
	public List<String> getListUser() {
		return listUser;
	}

	/**
	 * Method to set listUser
	 * @param listUser 
	 * @return 
	 */
	public void setListUser(List<String> listUser) {
		this.listUser = listUser;
	}

	/**
	 * Method to Get rowColor
	 * @return rowColor as String
	 */
	public String getRowColor() {
		return rowColor;
	}

	/**
	 * Method to set rowColor
	 * @param rowColor 
	 * @return 
	 */
	public void setRowColor(String rowColor) {
		this.rowColor = rowColor;
	}

	/**
	 * Method to Get modifiedValue
	 * @return modifiedValue as String
	 */
	public String getModifiedValue() {
		return modifiedValue;
	}

	/**
	 * Method to set modifiedValue
	 * @param modifiedValue 
	 * @return 
	 */
	public void setModifiedValue(String modifiedValue) {
		this.modifiedValue = modifiedValue;
	}

	/**
	 * Method to get Row Id
	 * @return  rowId as Long
	 */
	public Long getRowId() {
		return rowId;
	}

	/**
	 * Method to set Row Id
	 * @param rowId the rowId to set
	 */
	public void setRowId(Long rowId) {
		this.rowId = rowId;
	}

	/**
	 * Method to get Multiplier as Big Decimal
	 * @return the multiplier
	 */
	public BigDecimal getMultiplier() {
		return multiplier;
	}

	/**
	 * Method to set multiplier
	 * @param multiplier 
	 */
	public void setMultiplier(BigDecimal multiplier) {
		this.multiplier = multiplier;
	}

	/**
	 * Method to Get userId
	 * @return userId as String
	 */
	public String getUserId() {
		return userId;
	}

	/**
	 * Method to set userId
	 * @param userId 
	 * @return 
	 */
	public void setUserId(String userId) {
		this.userId = userId;
	}

	/**
	 * Getter method for columnSourceType
	 * @return columnSourceType as String[]
	 */
	public String[] getColumnSourceType() {
		return columnSourceType;
	}

	/**
	 * Setter method for columnSourceType
	 * @param columnSourceType 
	 */
	
	public void setColumnSourceType(String[] columnSourceType) {
		this.columnSourceType = columnSourceType;
	}

	/**
	 * Getter method for entity
	 * @return entity as String[]
	 */
	public String[] getEntity() {
		return entity;
	}

	/**
	 * Setter method for entity
	 * @param entity 
	 */
	
	public void setEntity(String[] entity) {
		this.entity = entity;
	}

	/**
	 * Getter method for sourceId
	 * @return sourceId as String[]
	 */
	public String[] getSourceId() {
		return sourceId;
	}

	/**
	 * Setter method for sourceId
	 * @param sourceId 
	 */
	
	public void setSourceId(String[] sourceId) {
		this.sourceId = sourceId;
	}

	/**
	 * Getter method for sourceName
	 * @return sourceName as String[]
	 */
	public String[] getSourceName() {
		return sourceName;
	}

	/**
	 * Setter method for sourceName
	 * @param sourceName 
	 */
	
	public void setSourceName(String[] sourceName) {
		this.sourceName = sourceName;
	}

	/**
	 * Getter method for sourceMultiplier
	 * @return sourceMultiplier as String[]
	 */
	public String[] getSourceMultiplier() {
		return sourceMultiplier;
	}

	/**
	 * Setter method for sourceMultiplier
	 * @param sourceMultiplier 
	 */
	
	public void setSourceMultiplier(String[] sourceMultiplier) {
		this.sourceMultiplier = sourceMultiplier;
	}

	/**
	 * Getter method for deleteForecastMonitorTemplateColSrcList
	 * @return List
	 */
	public List<ForecastMonitorTemplateColSrc> getDeleteForecastMonitorTemplateColSrcList() {
		return deleteForecastMonitorTemplateColSrcList;
	}

	/**
	 * Setter method for deleteForecastMonitorTemplateColSrcList
	 * @param deleteForecastMonitorTemplateColSrcList
	 */
	public void setDeleteForecastMonitorTemplateColSrcList(
			List<ForecastMonitorTemplateColSrc> deleteForecastMonitorTemplateColSrcList) {
		this.deleteForecastMonitorTemplateColSrcList = deleteForecastMonitorTemplateColSrcList;
	}

	/**
	 * Getter method for totalMultiplier
	 * @return the totalMultiplier
	 */
	public String getTotalMultiplier() {
		return totalMultiplier;
	}

	/**
	 * Setter method for totalMultiplier
	 * @param totalMultiplier 
	 */
	public void setTotalMultiplier(String totalMultiplier) {
		this.totalMultiplier = totalMultiplier;
	}
	
}
