/*
 * Created on Dec 20, 2005
 *
 * TODO To change the template for this generated file go to
 * Window - Preferences - Java - Code Style - Code Templates
 */
package org.swallow.maintenance.model;

import java.util.Date;
import java.util.Hashtable;

import org.swallow.model.BaseObject;

/**
 * <AUTHOR>
 *
 * TODO To change the template for this generated type comment go to
 * Window - Preferences - Java - Code Style - Code Templates
 */
public class MetaGroup extends BaseObject implements org.swallow.model.AuditComponent{

	 private String mgroupName;
	 private Integer mgrpLvlCode;
	 private String mgrpLvlName; // Added for displaying megtaGroupLevel Name in movement search screen
	 private String financeTrade = "F";
	 private String displayedFinanceTrade;
	 private Date updateDate ;
	 private String updateUser ;
	 private Integer noofGroups ;
	private Id id = new Id();
	
	public static Hashtable  logTable = new Hashtable();
	static {
		logTable.put("mgroupName","Metagroup Name");
		logTable.put("mgrpLvlCode","Metagroup Level");
		logTable.put("financeTrade","Finance or Trade");

	}
	
	public static class Id extends BaseObject{
		private String hostId ;
		private String entityId ;
		private String mgroupId ;
	
	

		public Id(){}		
		
		public Id(String hostId, String entityId, String mgroupId ){
			this.hostId = hostId ;
			this.entityId = entityId ;
			this.mgroupId = mgroupId ;
		}
		
		
		/**
		 * @return Returns the entityId.
		 */
		public String getEntityId() {
			return entityId;
		}
		/**
		 * @param entityId The entityId to set.
		 */
		public void setEntityId(String entityId) {
			this.entityId = entityId;
		}
		/**
		 * @return Returns the hostId.
		 */
		public String getHostId() {
			return hostId;
		}
		/**
		 * @param hostId The hostId to set.
		 */
		public void setHostId(String hostId) {
			this.hostId = hostId;
		}
		/**
		 * @return Returns the mGroupId.
		 */
		
		/**
		 * @return Returns the mgroupId.
		 */
		public String getMgroupId() {
			return mgroupId;
		}
		/**
		 * @param mgroupId The mgroupId to set.
		 */
		public void setMgroupId(String mgroupId) {
			this.mgroupId = mgroupId;
		}
	}
	
//	public MetaGroup(Id id,String mGroupName,Integer mGrpLvlCode)
//	{
//		this.id = id;
//		this.mGroupName = mGroupName;
//		this.mGrpLvlCode = mGrpLvlCode;
//	}
//	
//	public MetaGroup(){
//		
//	}
	
	/**
	 * @return Returns the financeTrade.
	 */
	public String getFinanceTrade() {
	    	return financeTrade;
	}
	/**
	 * @param financeTrade The financeTrade to set.
	 */
	public void setFinanceTrade(String financeTrade) {
	    this.financeTrade = financeTrade;
	    }
	

	
	
	public String getDisplayedFinanceTrade() {
		
		
	    /*if (financeTrade != null)
	    {
	    if(financeTrade.equalsIgnoreCase("1"))
	    {
	        displayedFinanceTrade="F";
	        }
	    if(financeTrade.equalsIgnoreCase("2"))
	    {
	        displayedFinanceTrade="T";
	        }
		
	    }*/
	    return financeTrade;
	}
	/**
	 * @param dupaction The dupaction to set.
	 */
	public void setDisplayedFinanceTrade(String displayedFinanceTrade) {
	    setFinanceTrade(displayedFinanceTrade);
		this.displayedFinanceTrade = displayedFinanceTrade;
	}
	
	
	
	
	
	/**
	 * @return Returns the updateDate.
	 */
	public Date getUpdateDate() {
		return updateDate;
	}
	/**
	 * @param updateDate The updateDate to set.
	 */
	public void setUpdateDate(Date updateDate) {
		this.updateDate = updateDate;
	}
	/**
	 * @return Returns the updateUser.
	 */
	public String getUpdateUser() {
		return updateUser;
	}
	/**
	 * @param updateUser The updateUser to set.
	 */
	public void setUpdateUser(String updateUser) {
		this.updateUser = updateUser;
	}
	/**
	 * @return Returns the id.
	 */
	public Id getId() {
		return id;
	}
	/**
	 * @param id The id to set.
	 */
	public void setId(Id id) {
		this.id = id;
	}
	/**
	 * @return Returns the mgroupName.
	 */
	public String getMgroupName() {
		return mgroupName;
	}
	/**
	 * @param mgroupName The mgroupName to set.
	 */
	public void setMgroupName(String mgroupName) {
		this.mgroupName = mgroupName;
	}
/**
 * @return Returns the mgrpLvlCode.
 */
public Integer getMgrpLvlCode() {
	return mgrpLvlCode;
}
/**
 * @param mgrpLvlCode The mgrpLvlCode to set.
 */
public void setMgrpLvlCode(Integer mgrpLvlCode) {
	this.mgrpLvlCode = mgrpLvlCode;
}
	/**
	 * @return Returns the noofGroups.
	 */
	public Integer getNoofGroups() {
		return noofGroups;
	}
	/**
	 * @param noofGroups The noofGroups to set.
	 */
	public void setNoofGroups(Integer noofGroups) {
		this.noofGroups = noofGroups;
	}
	public String getMgrpLvlName() {
		return mgrpLvlName;
	}
	public void setMgrpLvlName(String mgrpLvlName) {
		this.mgrpLvlName = mgrpLvlName;
	}
}// End of class MetaGroup
