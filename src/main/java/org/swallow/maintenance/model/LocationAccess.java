/*
 * Created on Jul 5, 2007
 *
 * TODO To change the template for this generated file go to
 * Window - Preferences - Java - Code Style - Code Templates
 */
package org.swallow.maintenance.model;

import org.swallow.model.BaseObject;


/**
 * <AUTHOR>
 *
 * TODO To change the template for this generated type comment go to
 * Window - Preferences - Java - Code Style - Code Templates
 */
public class LocationAccess extends BaseObject
    implements org.swallow.model.AuditComponent {
    private Id id = new Id();

    /**
     * @return Returns the id.
     */
    public Id getId() {
        return id;
    }

    /**
     * @param id The id to set.
     */
    public void setId(Id id) {
        this.id = id;
    }

    public static class Id extends BaseObject {
        private String hostId;
        private String entityId;
        private String locationId;
        private String roleId;

        public Id(String hostId, String entityId, String locationId,
            String roleId) {
            this.hostId = hostId;
            this.entityId = entityId;
            this.locationId = locationId;
            this.roleId = roleId;
        }

        public Id() {
        }

        /**
         * @return Returns the entityId.
         */
        public String getEntityId() {
            return entityId;
        }

        /**
         * @param entityId The entityId to set.
         */
        public void setEntityId(String entityId) {
            this.entityId = entityId;
        }

        /**
         * @return Returns the hostId.
         */
        public String getHostId() {
            return hostId;
        }

        /**
         * @param hostId The hostId to set.
         */
        public void setHostId(String hostId) {
            this.hostId = hostId;
        }

        /**
         * @return Returns the LocationId.
         */
        public String getLocationId() {
            return locationId;
        }

        /**
         * @param LocationId The LocationId to set.
         */
        public void setLocationId(String locationId) {
            this.locationId = locationId;
        }

        public String getRoleId() {
            return roleId;
        }

        /**
         * @param LocationId The LocationId to set.
         */
        public void setRoleId(String roleId) {
            this.roleId = roleId;
            ;
        }
    }
}
