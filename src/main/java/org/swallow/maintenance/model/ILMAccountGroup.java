/*
 * @(#)ILMAccountGroup.java  29/11/13
 * 
 * Copyright (c) 2006-2013 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 * 
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */

package org.swallow.maintenance.model;

import java.util.Date;
import java.util.HashSet;
import java.util.Hashtable;
import java.util.Set;

import org.swallow.model.BaseObject;


public class ILMAccountGroup extends BaseObject implements org.swallow.model.AuditComponent {
	
	private static final long serialVersionUID = 1L;
	
	private String ilmGroupName;
	private String ilmGroupDescription;
	private String hostId;
	private String entityId;
	private String currencyCode;
	private String thresholdMin1;
	private String thresholdMin2;
	private String thresholdMax1;
	private String thresholdMax2;
	private String groupType;
	private String dynamicFixed;
	private String filterCondition;
	private String createdByUser;
	private Date createDate;
	private String publicPrivate;	
	private String publicPrivateAsString;	
	private String mainAgent;	
	private int accs = 0;
	private String global ;
	private String thresh1Time;
	private String thresh2Time;
	private Integer thresh1Percent;
	private Integer thresh2Percent;
	private String createThroughputRatio;
	private Set<ILMCcyParameters> ILMCcyParameters = new HashSet<ILMCcyParameters>(0);
	private Set<ILMAccountInGroups> accountsInGroup = new HashSet<ILMAccountInGroups>(0);
	
	// Added for ilm monitor screen in order to build the group grid 
	private String externalSOD;
	private String externalEOD;
	private String forecastSOD;
	private String forecastEOD;
	private String lastUpdated;
	
	private String openUnexp;
	private String openUnsett;
	
	private String defaultLegendText;
	
	private String allowReporting;
	private String correspondentBank;
	private String collectNetCumPos;
	
	private String minNcpThreshold;
	private String maxNcpThreshold;
	
	

	private Id id = new Id();
	
	public static Hashtable  logTable = new Hashtable();
	static {
		logTable.put("ilmGroupId","Account Group");
		logTable.put("ilmGroupName","Account Group Name");
		logTable.put("thresholdMin1","First Minimum");
		logTable.put("thresholdMin2","Second Minimum");
		logTable.put("thresholdMax1","First Maximum");
		logTable.put("thresholdMax2","Second Maximum");
		logTable.put("accountsInGroup","Accounts In Group");
		logTable.put("allowReporting","Allow Reporting");
		logTable.put("correspondentBank","Correspondent Bank");
		logTable.put("collectNetCumPos","Report Net Cum Position Chart");
		logTable.put("minNcpThreshold","Min Net Cum Pos Thresholds");
		logTable.put("maxNcpThreshold","Max Net Cum Pos Thresholds");
	}
	
	
	public String getIlmGroupName() {
		return ilmGroupName;
	}

	public void setIlmGroupName(String ilmGroupName) {
		this.ilmGroupName = ilmGroupName;
	}


	public String getIlmGroupDescription() {
		return ilmGroupDescription;
	}

	public void setIlmGroupDescription(String ilmGroupDescription) {
		this.ilmGroupDescription = ilmGroupDescription;
	}

	public String getHostId() {
		return hostId;
	}

	public void setHostId(String hostId) {
		this.hostId = hostId;
	}

	public String getEntityId() {
		return entityId;
	}

	public void setEntityId(String entityId) {
		this.entityId = entityId;
	}

	public String getCurrencyCode() {
		return currencyCode;
	}

	public void setCurrencyCode(String currencyCode) {
		this.currencyCode = currencyCode;
	}

	public String getThresholdMin1() {
		return thresholdMin1;
	}

	public void setThresholdMin1(String thresholdMin1) {
		this.thresholdMin1 = thresholdMin1;
	}

	public String getThresholdMin2() {
		return thresholdMin2;
	}

	public void setThresholdMin2(String thresholdMin2) {
		this.thresholdMin2 = thresholdMin2;
	}

	public String getThresholdMax1() {
		return thresholdMax1;
	}

	public void setThresholdMax1(String thresholdMax1) {
		this.thresholdMax1 = thresholdMax1;
	}

	public String getThresholdMax2() {
		return thresholdMax2;
	}

	public void setThresholdMax2(String thresholdMax2) {
		this.thresholdMax2 = thresholdMax2;
	}

	public String getGroupType() {
		return groupType;
	}

	public void setGroupType(String groupType) {
		this.groupType = groupType;
	}

	public String getFilterCondition() {
		return filterCondition;
	}

	public void setFilterCondition(String filterCondition) {
		this.filterCondition = filterCondition;
	}

	public String getCreatedByUser() {
		return createdByUser;
	}

	public void setCreatedByUser(String createdByUser) {
		this.createdByUser = createdByUser;
	}

	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}

	public String getPublicPrivate() {
		return publicPrivate;
	}

	public void setPublicPrivate(String publicPrivate) {
		this.publicPrivate = publicPrivate;
	}

	public Id getId() {
		return id;
	}

	public void setId(Id id) {
		this.id = id;
	}



	/**
	 * Getter method for logTable
	 * 
	 * @return logTable as Hashtable
	 */

	public static Hashtable getLogTable() {
		return logTable;
	}

	/**
	 * Setter method for logTable
	 * 
	 * @param logTable
	 */

	public static void setLogTable(Hashtable logTable) {
		ILMAccountGroup.logTable = logTable;
	}



	public int getAccs() {
		return accs;
	}

	public void setAccs(int accs) {
		this.accs = accs;
	}


	public String getDynamicFixed() {
		return dynamicFixed;
	}

	public void setDynamicFixed(String dynamicFixed) {
		this.dynamicFixed = dynamicFixed;
	}

	public String getPublicPrivateAsString() {
		return publicPrivateAsString;
	}

	public void setPublicPrivateAsString(String publicPrivateAsString) {
		this.publicPrivateAsString = publicPrivateAsString;
	}


	public String getGlobal() {
		return global;
	}

	public void setGlobal(String global) {
		this.global = global;
	}



	public Set<ILMCcyParameters> getILMCcyParameters() {
		return ILMCcyParameters;
	}

	public void setILMCcyParameters(Set<ILMCcyParameters> iLMCcyParameters) {
		ILMCcyParameters = iLMCcyParameters;
	}



	public Set<ILMAccountInGroups> getAccountsInGroup() {
		return accountsInGroup;
	}

	public void setAccountsInGroup(Set<ILMAccountInGroups> accountsInGroup) {
		this.accountsInGroup = accountsInGroup;
	}


	
	
	public static class Id extends BaseObject{

		 
		private String ilmGroupId;
		
		public String getIlmGroupId() {
			return ilmGroupId;
		}

		public void setIlmGroupId(String ilmGroupId) {
			this.ilmGroupId = ilmGroupId;
		}

		public Id() {
		}
		
		public Id(String ilmGroupId) {
			 setIlmGroupId(ilmGroupId);
		}
	}
	
	public String getExternalSOD() {
		return externalSOD;
	}

	public void setExternalSOD(String externalSOD) {
		this.externalSOD = externalSOD;
	}

	public String getExternalEOD() {
		return externalEOD;
	}

	public void setExternalEOD(String externalEOD) {
		this.externalEOD = externalEOD;
	}

	public String getForecastSOD() {
		return forecastSOD;
	}

	public void setForecastSOD(String forecastSOD) {
		this.forecastSOD = forecastSOD;
	}

	public String getForecastEOD() {
		return forecastEOD;
	}

	public void setForecastEOD(String forecastEOD) {
		this.forecastEOD = forecastEOD;
	}
	
	public String getLastUpdated() {
		return lastUpdated;
	}

	public void setLastUpdated(String lastUpdated) {
		this.lastUpdated = lastUpdated;
	}

	public String getOpenUnexp() {
		return openUnexp;
	}

	public void setOpenUnexp(String openUnexp) {
		this.openUnexp = openUnexp;
	}

	public String getOpenUnsett() {
		return openUnsett;
	}

	public void setOpenUnsett(String openUnsett) {
		this.openUnsett = openUnsett;
	}

	public String getDefaultLegendText() {
		return defaultLegendText;
	}

	public void setDefaultLegendText(String defaultLegendText) {
		this.defaultLegendText = defaultLegendText;
	}

	public String getAllowReporting() {
		return allowReporting;
	}

	public void setAllowReporting(String allowReporting) {
		this.allowReporting = allowReporting;
	}

	public String getCorrespondentBank() {
		return correspondentBank;
	}

	public void setCorrespondentBank(String correspondentBank) {
		this.correspondentBank = correspondentBank;
	}

	public String getCollectNetCumPos() {
		return collectNetCumPos;
	}

	public void setCollectNetCumPos(String collectNetCumPos) {
		this.collectNetCumPos = collectNetCumPos;
	}
	
	public String getMainAgent() {
		return mainAgent;
	}

	public void setMainAgent(String mainAgent) {
		this.mainAgent = mainAgent;
	}

	public String getMinNcpThreshold() {
		return minNcpThreshold;
	}

	public void setMinNcpThreshold(String minNcpThreshold) {
		this.minNcpThreshold = minNcpThreshold;
	}

	public String getMaxNcpThreshold() {
		return maxNcpThreshold;
	}

	public void setMaxNcpThreshold(String maxNcpThreshold) {
		this.maxNcpThreshold = maxNcpThreshold;
	}

	public String getThresh2Time() {
		return thresh2Time;
	}

	public void setThresh2Time(String thresh2Time) {
		this.thresh2Time = thresh2Time;
	}

	public String getThresh1Time() {
		return thresh1Time;
	}

	public void setThresh1Time(String thresh1Time) {
		this.thresh1Time = thresh1Time;
	}

	public Integer getThresh1Percent() {
		return thresh1Percent;
	}

	public void setThresh1Percent(Integer thresh1Percent) {
		this.thresh1Percent = thresh1Percent;
	}

	public Integer getThresh2Percent() {
		return thresh2Percent;
	}

	public void setThresh2Percent(Integer thresh2Percent) {
		this.thresh2Percent = thresh2Percent;
	}

	public String getCreateThroughputRatio() {
		return createThroughputRatio;
	}

	public void setCreateThroughputRatio(String createThroughputRatio) {
		this.createThroughputRatio = createThroughputRatio;
	}
	
	
	
}
