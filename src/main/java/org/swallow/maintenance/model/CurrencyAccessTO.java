/*
 * @(#)CurrencyAccessTO.java 1.0 06/07/03
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.maintenance.model;

import org.swallow.util.SwtConstants;


/**
 * This class contains the currency access.
 *
 * <AUTHOR> Systems
 * @version 1.0
 */
public class CurrencyAccessTO extends CurrencyTO {
    /** Holds the currency access. Default is no access  */
    private int access = SwtConstants.CURRENCYGRP_NO_ACCESS;

    /**
     * Creates a new CurrencyAccessTO object.
     *
     * @param entityId Entity id
     * @param currencyId Currency Id
     * @param currencyName Currency Name
     * @param access Access
     */
    public CurrencyAccessTO(String entityId, String currencyId,
        String currencyName, int access) {
        super(entityId, currencyId, currencyName);
        this.access = access;
    }

    /**
     * Creates a new CurrencyAccessTO object.
     *
     * @param parent CurrencyTO object
     * @param access Access
     */
    public CurrencyAccessTO(CurrencyTO parent, int access) {
        super(parent.getEntityId(), parent.getCurrencyId(),
            parent.getCurrencyName());
        this.access = access;
    }

    /**
     * Returns the access.
     *
     * @return Returns the access.
     */
    public int getAccess() {
        return access;
    }

    /**
     * This function overrides the equals function of Object class.
     *
     * @param obj Object to be compared
     *
     * @return true/false
     */
    public boolean equals(Object obj) {
        boolean retValue = false;

        if ((obj != null) && obj instanceof CurrencyAccessTO) {
            CurrencyAccessTO curr = ( CurrencyAccessTO ) obj;
            retValue = curr.getEntityId().equals(getEntityId())
                && curr.getCurrencyId().equals(getCurrencyId())
                && (curr.getAccess() == access);
        }

        return retValue;
    }

    /**
     * This function overrides the hashCode function of Object class.
     *
     * @return Retunrs the Hash Code of object.
     */
    public int hashCode() {
        return super.hashCode() + new Integer(access).hashCode();
    }

    /**
     * This function overrides the toString function of Object class.
     *
     * @return Returns the String repersentation of object.
     */
    public String toString() {
        StringBuffer buf = new StringBuffer(super.toString());
        buf.append(",").append(access);

        return buf.toString();
    }
}
