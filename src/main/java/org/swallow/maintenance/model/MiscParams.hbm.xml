<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE hibernate-mapping PUBLIC
        "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
        "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">
<hibernate-mapping>
 <class name="org.swallow.maintenance.model.MiscParams" table="P_MISC_PARAMS">
  <composite-id class="org.swallow.maintenance.model.MiscParams$Id"
   name="id" unsaved-value="any">
   <!--Start code commented & added by Betcy for mantis 1220 on 22-09-10 Change primary key of P_MISC_PARAMS*-->
   <key-property name="hostId" type="java.lang.String" column="HOST_ID" length="12"/>
    <key-property name="key1" type="java.lang.String" column="KEY1"
   length="30"/>
  <key-property name="key2" type="java.lang.String" column="KEY2" length="30"/>
  <!--Start: code Added/Modified by <PERSON><PERSON><PERSON><PERSON> on 03-Dec-2010 for Mantis:0001296- Mis<PERSON> <PERSON><PERSON> to added the entityId field in table and check the entityId -->
  <key-property name="entityId" type="java.lang.String" column="ENTITY_ID" length="12"/>
  <!--End: code Added/Modified by Arumugam on 03-Dec-2010 for Mantis:0001296- Misc Param to added the entityId field in table and check the entityId -->
  <!--End code commented & added by Betcy for mantis 1220 on 22-09-10 Change primary key of P_MISC_PARAMS*-->
  </composite-id>
  
  <property name="key1Desc" type="java.lang.String" column="KEY1_DESC" length="100"/>
  <property name="key2Desc" type="java.lang.String" column="KEY2_DESC" length="100"/>
  <property name="parValue" type="java.lang.String" column="PAR_VALUE"
   length="50" not-null="true"/>
  <property name="parValDesc" type="java.lang.String"
   column="PAR_VAL_DESC" length="100"/>
  <property name="updateDate" type="java.sql.Timestamp"
   column="UPDATE_DATE" length="7"/>
  <property name="updateUser" type="java.lang.String"
   column="UPDATE_USER" length="15"/>
 </class>
</hibernate-mapping>
