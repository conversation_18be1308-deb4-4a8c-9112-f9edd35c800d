/*
 * @(#)EntityCurrencyGroupAccessTO.java 1.0 06/07/03
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.maintenance.model;

import org.swallow.util.SwtConstants;


/**
 * This class is immutable class and extends the EntityCurrencyGroupTO class. It contains the Currency Group Access.
 *
 * <AUTHOR> Systems
 * @version 1.0
 */
public class EntityCurrencyGroupAccessTO extends EntityCurrencyGroupTO {
    /** Holds the Currency Group Access  */
    private int access = SwtConstants.CURRENCYGRP_NO_ACCESS;

    /**
     * Create the EntityCurrencyGroupAccessTO object.
     *
     * @param entityId Entity Id
     * @param currencyGroupId Currency Group Id
     * @param access Currency Group Access
     */
    public EntityCurrencyGroupAccessTO(String entityId, String currencyGroupId,
        int access) {
        super(entityId, currencyGroupId);
        this.access = access;
    }

    /**
     * Creates a new EntityCurrencyGroupAccessTO object.
     *
     * @param parent EntityCurrencyGroupTO object
     * @param access Currency Group Access
     */
    public EntityCurrencyGroupAccessTO(EntityCurrencyGroupTO parent, int access) {
        super(parent.getEntityId(), parent.getCurrencyGroupId());
        this.access = access;
    }

    /**
     * Returns the Access.
     *
     * @return Returns the access.
     */
    public int getAccess() {
        return access;
    }

    /**
     * This function overrides the equals function of Object class.
     *
     * @param obj Object to be compared
     *
     * @return true/false
     */
    public boolean equals(Object obj) {
        boolean retValue = false;

        if ((obj != null) && obj instanceof EntityCurrencyGroupAccessTO) {
            EntityCurrencyGroupAccessTO curr = ( EntityCurrencyGroupAccessTO ) obj;
            retValue = curr.getEntityId().equals(getEntityId())
                && curr.getCurrencyGroupId().equals(getCurrencyGroupId())
                && (curr.getAccess() == access);
        }

        return retValue;
    }

    /**
     * This function overrides the hashCode function of Object class.
     *
     * @return Retunrs the Hash Code of object.
     */
    public int hashCode() {
        return super.hashCode() + new Integer(access).hashCode();
    }
}
