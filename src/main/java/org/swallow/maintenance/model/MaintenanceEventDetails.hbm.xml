<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE hibernate-mapping PUBLIC
        "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
        "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">
<hibernate-mapping>
	<class name="org.swallow.maintenance.model.MaintenanceEventDetails"
		table="S_MAINT_EVENT_DETAIL">
		<composite-id class="org.swallow.maintenance.model.MaintenanceEventDetails$Id" name="id"
			unsaved-value="any">
			<key-property name="maintEventId"
				column="MAINT_EVENT_ID" access="field"></key-property>
			<key-property name="maintSeq" column="MAINT_SEQ"	access="field"></key-property>
		</composite-id>
		<property name="tableName" not-null="false"
			column="TABLE_NAME" />
		<property name="recordId" not-null="false" column="RECORD_ID" />
		<property name="action" not-null="false" column="ACTION" />
		<property name="oldState" not-null="false" column="OLD_STATE" />
		<property name="newState" not-null="false" column="NEW_STATE" />
	</class>
</hibernate-mapping>
