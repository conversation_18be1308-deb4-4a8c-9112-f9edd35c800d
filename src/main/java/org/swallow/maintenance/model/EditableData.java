/*
 * Created on Jul 19, 2007
 *
 * TODO To change the template for this generated file go to
 * Window - Preferences - Java - Code Style - Code Templates
 */
package org.swallow.maintenance.model;

import java.util.Hashtable;

import org.swallow.model.BaseObject;

/**
 * EdiatableData.java
 * 
 * Hibernate Mapping file associated with P_EDITABLE_DATA table
 * 
 */
public class EditableData extends BaseObject implements
		org.swallow.model.AuditComponent {

	private Id id = new Id();
	private String editable;
	private String editableDataHTML1;
	private String editableDataHTML2;
	private String editableDataHTML3;
	/*
	 * START:Code Modified by <PERSON> on 31-MAR-2011 for Mantis 1244:Apply
	 * "Editable Fields" functionality for matching_party, product_type and
	 * posting_date
	 */
	private boolean saveFlag;
	/*
	 * END:Code Modified by <PERSON> on 31-MAR-2011 for Mantis 1244:Apply
	 * "Editable Fields" functionality for matching_party, product_type and
	 * posting_date
	 */
	public static Hashtable logTable = new Hashtable();
	static {
		logTable.put("movementField", "Movement Field");
		logTable.put("editable", "Editable");

	}

	public static class Id extends BaseObject {
		private String hostId;
		private String entityId;
		private String movementField;

		public Id(String hostId, String entityId) {
			this.hostId = hostId;
			this.entityId = entityId;

		}

		/**
		 * @return Returns the entityId.
		 */
		public String getEntityId() {
			return entityId;
		}

		/**
		 * @param entityId
		 *            The entityId to set.
		 */
		public void setEntityId(String entityId) {
			this.entityId = entityId;
		}

		/**
		 * @return Returns the hostId.
		 */
		public String getHostId() {
			return hostId;
		}

		/**
		 * @param hostId
		 *            The hostId to set.
		 */
		public void setHostId(String hostId) {
			this.hostId = hostId;
		}

		public Id() {
		}

		/**
		 * @return Returns the id.
		 */
		/**
		 * @return Returns the movementField.
		 */
		public String getMovementField() {
			return movementField;
		}

		/**
		 * @param movementField
		 *            The movementField to set.
		 */
		public void setMovementField(String movementField) {
			this.movementField = movementField;
		}
	}

	public Id getId() {
		return id;
	}

	/**
	 * @param id
	 *            The id to set.
	 */
	public void setId(Id id) {
		this.id = id;
	}

	/**
	 * @return Returns the editable.
	 */
	public String getEditable() {
		return editable;
	}

	/**
	 * @param editable
	 *            The editable to set.
	 */
	public void setEditable(String editable) {
		this.editable = editable;
	}

	/**
	 * @return Returns the editableDataHTML1.
	 */
	public String getEditableDataHTML1() {
		return editableDataHTML1;
	}

	/**
	 * @param editableDataHTML1
	 *            The editableDataHTML1 to set.
	 */
	public void setEditableDataHTML1(String editableDataHTML1) {
		this.editableDataHTML1 = editableDataHTML1;
	}

	/**
	 * @return Returns the editableDataHTML2.
	 */
	public String getEditableDataHTML2() {
		return editableDataHTML2;
	}

	/**
	 * @param editableDataHTML2
	 *            The editableDataHTML2 to set.
	 */
	public void setEditableDataHTML2(String editableDataHTML2) {
		this.editableDataHTML2 = editableDataHTML2;
	}

	/**
	 * @return Returns the editableDataHTML3.
	 */
	public String getEditableDataHTML3() {
		return editableDataHTML3;
	}

	/**
	 * @param editableDataHTML3
	 *            The editableDataHTML3 to set.
	 */
	public void setEditableDataHTML3(String editableDataHTML3) {
		this.editableDataHTML3 = editableDataHTML3;
	}

	/**
	 * @return the saveFlag
	 */
	public boolean isSaveFlag() {
		return saveFlag;
	}

	/**
	 * @param saveFlag
	 *            the saveFlag to set
	 */
	public void setSaveFlag(boolean saveFlag) {
		this.saveFlag = saveFlag;
	}

}
