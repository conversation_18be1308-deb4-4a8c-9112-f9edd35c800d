/*
 * Created on Jun 7, 2006
 *
 * TODO To change the template for this generated file go to
 * Window - Preferences - Java - Code Style - Code Templates
 */
package org.swallow.maintenance.model;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 *
 * TODO To change the template for this generated type comment go to
 * Window - Preferences - Java - Code Style - Code Templates
 */
public class SODPageSummary implements Serializable {
	private int pageNo;
	private String entityId;
	private String selectedList;
	private int currentPageNo;
	private int maxPages;
	private int totalCount;
	
	private Map SODpageSummary;
	

	
	public Map getSODpageSummary() {
		SODpageSummary = new HashMap();
		SODpageSummary.put("pageNoValue", Integer.toString(pageNo));
		SODpageSummary.put("currentPage", Integer.toString(currentPageNo));
		SODpageSummary.put("entityId", entityId);
		SODpageSummary.put("maxPages", Integer.toString(maxPages));
		SODpageSummary.put("totalCount", Integer.toString(totalCount));
		return SODpageSummary;
	}
	public void setSODpageSummary(Map SODpageSummary) {
		this.SODpageSummary = SODpageSummary;
	}
	public int getPageNo() {
		return pageNo;
	}
	public void setPageNo(int pageNo) {
		this.pageNo = pageNo;
	}
	public String getEntityId() {
		return entityId;
	}
	public void setEntityId(String entityId) {
		this.entityId = entityId;
	}
	public String getSelectedList() {
		return selectedList;
	}
	public void setSelectedList(String selectedList) {
		this.selectedList = selectedList;
	}
	public int getCurrentPageNo() {
		return currentPageNo;
	}
	public void setCurrentPageNo(int currentPageNo) {
		this.currentPageNo = currentPageNo;
	}
	public int getMaxPages() {
		return maxPages;
	}
	public void setMaxPages(int maxPages) {
		this.maxPages = maxPages;
	}
	public int getTotalCount() {
		return totalCount;
	}
	public void setTotalCount(int totalCount) {
		this.totalCount = totalCount;
	}
}
