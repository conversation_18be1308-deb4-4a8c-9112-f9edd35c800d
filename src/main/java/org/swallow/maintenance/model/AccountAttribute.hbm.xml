<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE hibernate-mapping PUBLIC
        "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
        "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">
<hibernate-mapping>
   <class name="org.swallow.maintenance.model.AccountAttribute" table="P_ACCOUNT_ATTRIBUTE" >
   
	    <id name="sequenceKey" column="SEQUENCE_KEY" type="long">
		   <generator class="sequence">
		    <param name="sequence_name">SEQ_P_ACCOUNT_ATTRIBUTE</param>
		   </generator>
	 	 </id>
	  
	  
   <property name="hostId" not-null="true" column="HOST_ID"  />
   <property name="entityId"  not-null="true" column="ENTITY_ID"/>
   <property name="accountId"  not-null="true" column="ACCOUNT_ID"/>
   <property name="attributeId" not-null="true" column="ATTRIBUTE_ID"/>
   <property name="effectiveDate" not-null="false"  column="EFFECTIVE_DATE" /> 

    <property name="numValue" column="NUM_VALUE" not-null="false"  />	
    <property name="strValue" column="STR_VALUE" not-null="false"/>	
    <property name="dateValue" column="DAT_VALUE" not-null="false"/>	
    <property name="updateDate" column="UPDATE_DATE" not-null="false"/>	
    <property name="updateUser" column="UPDATE_USER" not-null="false"/>

 </class>
</hibernate-mapping>