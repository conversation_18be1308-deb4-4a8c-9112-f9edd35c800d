package org.swallow.service.impl;

import lombok.Setter;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.swallow.model.User;
import org.swallow.repository.UserDao;
import org.swallow.service.UserService;

/**
 * Created by allan on 23/08/16.
 */
@Service
@Transactional(propagation = Propagation.SUPPORTS, readOnly = true)
public class UserServiceImpl implements UserService {

    @Autowired
    @Setter
    private UserDao userDao;

    @Transactional(propagation = Propagation.REQUIRED, readOnly = false)
    public void save(User user) {
        userDao.persist(user);
    }

    public void authenticate(User user) {
        userDao.authenticate(user);
    }

	@Override
	public List<User> findAll() {
		return userDao.findAll();
	}
    
    
}
