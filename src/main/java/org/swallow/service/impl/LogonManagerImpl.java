/*
 * Created on Nov 4, 2005
 * Updated on July 27 2007 by Mayank Tripathi
 *
 * TODO To change the template for this generated file go to
 * Window - Preferences - Java - Code Style - Code Templates
 */
package org.swallow.service.impl;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.Iterator;
import java.util.List;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.swallow.control.dao.UserMaintenanceDAO;
import org.swallow.control.model.Shortcut;
import org.swallow.control.model.UserStatus;
import org.swallow.dao.LogonDAO;
import org.swallow.exception.SwtException;
import org.swallow.model.MenuAccess;
import org.swallow.model.MenuItem;
import org.swallow.model.User;
import org.swallow.model.UserProfile;
import org.swallow.service.LogonManager;
import org.swallow.util.CacheManager;
import org.swallow.util.LabelValueBean;
import org.swallow.util.SwtUtil;

@Component("logonManager")
public class LogonManagerImpl implements LogonManager {
	
	@Autowired
	private LogonDAO dao;
	
    public void setLogonDAO(LogonDAO dao) {
        this.dao = dao;
    }
    /**
     * 
     */
    public ArrayList verifyUser(User user,String ipAddress, int...processInt) 
    throws SwtException{
    	ArrayList arr = dao.verifyUser(user,ipAddress, processInt);
    	return arr;
    }
    
    /**
	 * <pre>
	 * START: Modified and Added by RK on 10-Dec-2009 
	 * Mantis issue-1084: Menu not populated on main screen when No Access on
	 * Menu Item Work Flow Control 
	 * </pre>
	 */
	// Logger object
    private final Log log = LogFactory.getLog(LogonManagerImpl.class);

	/**
	 * <pre>
	 * This method gets the valid menu items for the given user and then forms
	 * the menu item heirarchy based on parent/child relation.
	 * 
	 * It supports 2-level of menu
	 * 	-Main Menu
	 * 			|_ -Menu Item
	 * 					|_ -Sub-menu 
	 * </pre>
	 * 
	 * @param user
	 *            the user object
	 * @throws SwtException
	 */
    public Collection getMenuList(User user) throws SwtException{
    	//build the menu list as heirarchical list
    	
    	/*ArrayList menuList = (ArrayList)dao.getMenuList(user);
    	log.debug("got menuList ");
    	int size = menuList.size();
    	HashMap parentMap = new HashMap();
    	MenuAccess menuAccess = null;
    	MenuItem menuItem = null;
    	MenuItem menuItemTemp = null;
    	ArrayList list = null;
    	ArrayList listReturn = new ArrayList();
    	
    	for(int i=0 ; i<size ; i++){
    		menuAccess = (MenuAccess)menuList.get(i);
    		log.debug("got menuAccess ");
    		menuItem = (MenuItem)menuAccess.getMenuItem();
    		log.debug("got menuItem : "+menuItem.getDescription());
    		parentMap.put(menuItem.getItemId(),menuItem);
    		if(SwtConstants.PARENT_MENU_ITEM_ID.equalsIgnoreCase(menuItem.getParentId())){
    			list = new ArrayList();
    			menuItem.setSubMenuList(list);
    			listReturn.add(menuItem);
    			log.debug("created top menu ");
    		}else{
    			list = new ArrayList();
    			menuItem.setSubMenuList(list);
    			menuItemTemp = (MenuItem)parentMap.get(menuItem.getParentId());
    			log.debug("got parent item  "+menuItemTemp);
    			menuItemTemp.getSubMenuList().add(menuItem);
    			if(menuItem.getProgram() != null){
    				menuItem.setProgramName(menuItem.getProgram().getProgramName());
    			}
    		}
       	}
    	log.debug("returning from  getMenuList "+listReturn);
    	return listReturn;*/
    	return getMenuHeirarchy(dao.getMenuList(user));
    }

	/**
	 * <pre>
	 * This method forms the menu item heirarchy based on parent/child
	 * relation.
	 * It supports 2-level of menu
	 * 	-Main Menu
	 * 			|_ -Menu Item
	 * 					|_ -Sub-menu 
	 * </pre>
	 * 
	 * @param lstMenu
	 * @return
	 * @throws Exception
	 */
	private List getMenuHeirarchy(Collection lstMenu) throws SwtException {
		// list to hold top level menu
		List lstMenuItem = null;
		// To form menu heirarchy
		Iterator iterMenu = null;
		// To form menu item (1st level menu)
		Iterator iterMainMenuItem = null;
		// To form sub-menu item (2nd level menu)
		Iterator iterMenuItem = null;
		// Result list (contains only top level menu)
		List lstResult = null;

		try {
			// Initialize result list
			lstMenuItem = new ArrayList();
			// To process all menu items
			iterMenu = lstMenu.iterator();
			// log debug message
			log.debug("Build menu heirarchy");
			while (iterMenu.hasNext()) {
				// Get menu item object
				MenuItem menuItemObj = (MenuItem) iterMenu.next();

				// Check whether its top level menu or not
				if (menuItemObj.getParentId().equals("0")) {
					// Top level menu, so initiliaze array list for menu items
					menuItemObj.setSubMenuList(new ArrayList());
					// Add main menu
					lstMenuItem.add(menuItemObj);
				}

				// To form menu item
				iterMainMenuItem = lstMenuItem.iterator();
				// To add menu item
				while (iterMainMenuItem.hasNext()) {
					// Get main menu item
					MenuItem mainMenuItem = (MenuItem) iterMainMenuItem.next();
					// If parent id of menu item matches main menu's id then
					// tread them as 1st level menu item
					if (menuItemObj.getParentId().equals(
							mainMenuItem.getItemId())) {
						// Set lowest access right (If parent contains lowest
						// access right, then children should have lowest access
						// right)
						menuItemObj.setAccessId(worstAccess(mainMenuItem
								.getAccessId(), menuItemObj.getAccessId()));
						// This is 1st level menu, so initilize list for
						// sub-menu (2nd level menu)
						menuItemObj.setSubMenuList(new ArrayList());
						// Add menu item
						mainMenuItem.getSubMenuList().add(menuItemObj);
						break;
					}
					// To form sub-menu
					iterMenuItem = mainMenuItem.getSubMenuList().iterator();
					// To add sub-menu item
					while (iterMenuItem.hasNext()) {
						// Get 1st level menu item
						MenuItem menuItem = (MenuItem) iterMenuItem.next();
						// If parent id of menu item matches menu's id then
						// tread them as 2nd level menu item
						if (menuItemObj.getParentId().equals(
								menuItem.getItemId())) {
							// Set lowest access right (If parent contains
							// lowest access right, then children should have
							// lowest access right)
							menuItemObj.setAccessId(worstAccess(menuItem
									.getAccessId(), menuItemObj.getAccessId()));
							// Add sub-menu item
							menuItem.getSubMenuList().add(menuItemObj);
							break;
						}
					}
				}
			}
			// Initialize result list
			lstResult = new ArrayList();
			// log debug message
			log.debug("Check menu item valid or not");
			// Check the menu items are valid or not. First it checks sub-menu
			// items (2nd level), if they are valid then add them to first level
			// menu item. Then it checks the 1st level menu item. if they are
			// valid then add to main menu item. Then check main menu item, if
			// it is valid then add it to result list
			for (int i = 0; i < lstMenuItem.size(); i++) {
				// To check main menu item
				MenuItem mainMenuItem = (MenuItem) lstMenuItem.get(i);
				iterMenuItem = mainMenuItem.getSubMenuList().iterator();
				// Temp variable to hold 1st level menu item
				ArrayList tmpMenuItem = new ArrayList();
				while (iterMenuItem.hasNext()) {
					// To validate 1st level menu item
					MenuItem menuItem = (MenuItem) iterMenuItem.next();
					// Temp variable to hold 2nd level menu item
					ArrayList tmpSubMenuItem = new ArrayList();
					Iterator iterSubMenuItem = menuItem.getSubMenuList()
							.iterator();
					while (iterSubMenuItem.hasNext()) {
						// To validate 2nd level menu item
						MenuItem subMenItem = (MenuItem) iterSubMenuItem.next();
						// 2nd level menu item should have valid program name
						if (hasMenuItem(subMenItem)) {
							tmpSubMenuItem.add(subMenItem);
						}
					}
					// Add 2nd level menu item to 1st level menu item
					menuItem.setSubMenuList(tmpSubMenuItem);
					// Check first level menu item, which should have either
					// valid program name or valid sub-menu(2nd level) items
					if (hasMenuItem(menuItem)) {
						tmpMenuItem.add(menuItem);
					}
				}
				// Add 1st level menu item to main menu
				mainMenuItem.setSubMenuList(tmpMenuItem);
				// Check main menu, which should have valid 1st level menu
				if (hasMenu(mainMenuItem)) {
					lstResult.add(mainMenuItem);
				}
			}
			// log debug message
			log.debug("Got menu heirarchy");
			// Sort the result by menu item id, as in database the menu item 
			// order is not proper. This will not be required, if the there are
			// proper records in database
			Collections.sort(lstResult);
			// log debug message
			log.debug("Send menu list");
			return lstResult;
		} catch (Exception ex) {
			//log error message
			log.error("Error in getMenuHeirarchy - " + ex.getMessage());
			throw new SwtException(ex.getMessage());
		} finally {
			// nullify objects
			iterMenu = null;
			iterMainMenuItem = null;
			iterMenuItem = null;
		}
	}

	/**
	 * This method finds the lowest access right and returns the same
	 * 
	 * @param mainMenuAccess
	 * @param subMenuAccess
	 * @return String
	 */
	private String worstAccess(String mainMenuAccess, String subMenuAccess) {
		return (Integer.parseInt(mainMenuAccess) > Integer
				.parseInt(subMenuAccess) ? mainMenuAccess : subMenuAccess);
	}

	/**
	 * This method checks the given main menu item has valid menu items (1st
	 * level menu)
	 * 
	 * @param mainMenuItem
	 * @return boolean
	 */
	private boolean hasMenu(MenuItem mainMenuItem) {
		// To validate menu items
		Iterator iterMenuItem = mainMenuItem.getSubMenuList().iterator();
		while (iterMenuItem.hasNext()) {
			// Validate menu item (1st level menu)
			if (hasMenuItem((MenuItem) iterMenuItem.next())) {
				return true;
			}
		}
		return false;
	}

	/**
	 * This method checks the given menu item(1st level menu) has valid sub-menu
	 * items (2nd level menu). The menu item should have either program name or
	 * valid sub-menu items
	 * 
	 * @param menuItem
	 * @return boolean
	 */
	private boolean hasMenuItem(MenuItem menuItem) {
		// Check program name
		if (!(menuItem.getProgramName() == null || menuItem.getProgramName()
				.trim().length() == 0)) {
			return true;
		} else if (menuItem.getSubMenuList().size() > 0) {
			// Check sub-menu item
			Iterator iterSubMenu = menuItem.getSubMenuList().iterator();
			while (iterSubMenu.hasNext()) {
				//Get sub-menu item
				MenuItem subMenuItem = (MenuItem) iterSubMenu.next();
				//Check the sub-menu has valid program name or not
				if (!(subMenuItem.getProgramName() == null || subMenuItem
						.getProgramName().trim().length() == 0)) {
					return true;
				}
			}
		}
		return false;
	}
	/**
	 * END: Added by RK on 10-Dec-2009 for mantis issue-1084
	 */
    
    public Collection getUserProfileDetails(User user) throws SwtException{
    	return dao.getUserProfileDetails(user);
    }
    
    public Collection getScreenList(User user) throws SwtException{
    	ArrayList list = new ArrayList();
    	Iterator itr = (dao.getScreenList(user)).iterator(); 
		MenuAccess menuAccess = null;
		while(itr.hasNext()){
			menuAccess = (MenuAccess)(itr.next());
			list.add(new LabelValueBean(menuAccess.getMenuItem().getDescription() ,menuAccess.getId().getItemId()));
		}
		return list;    	
    }
    
 
    public Collection getShortcutList(User user) throws SwtException{
    	
    	ArrayList list = (ArrayList)dao.getShortcutList(user);
    	int size = list.size();
    	/*Start: code Added/Modified by Arumugam on 03-Dec-2010 for Mantis:0001296- Misc Param to added the entityId field in table and check the entityId */	
    	ArrayList paramsList = (ArrayList)CacheManager.getInstance().getMiscParams("SHORTCUT",user.getCurrentEntity()) ;
		/*End: code Added/Modified by Arumugam on 03-Dec-2010 for Mantis:0001296- Misc Param to added the entityId field in table and check the entityId */
    	int paramsSize = paramsList.size();
    	Shortcut shortcut = null;
    	//Stores only those items which do not have 'No access'
    	ArrayList shortCutListWithAccess = new ArrayList();
    	
    	for(int i=0; i< size; i++){
    		shortcut = (Shortcut)list.get(i);    		
    		Collection accList = dao.getAccessIdOfShortcut(user.getRoleId(), shortcut.getMenuItemId());    		
    		if (accList != null && accList.size() > 0) {
    			shortcut.setAction(shortcut.getMenuItem().getProgram().getProgramName());
    			shortcut.setImageName(shortcut.getMenuItem().getImageName());
    			shortcut.setWidth(shortcut.getMenuItem().getWidth().intValue());
    			shortcut.setHeight(shortcut.getMenuItem().getHeight().intValue());
    			shortCutListWithAccess.add(shortcut);
    		}
       	}
    	
    	size = shortCutListWithAccess.size();
    	for(; size<paramsSize; size++){
    		shortCutListWithAccess.add(size,new Shortcut());
    	}
    	return shortCutListWithAccess;
    }
    
    /**
     * This method is used to get the user's profile from the database.
     * @param User
     * @throws SwtException
     */
    public Collection getProfileList(User user) 
    throws SwtException{
    	ArrayList list = new ArrayList();
    	Iterator itr = (dao.getProfileList(user)).iterator(); 
		UserProfile profile = null;
		list.add(new LabelValueBean("Select Profile",""));
		while(itr.hasNext()){
			profile = (UserProfile)(itr.next());
			list.add(new LabelValueBean(profile.getUserProfileName() ,profile.getId().getProfileId()));
		}
		return list;
    }
    
    /**
     * 
     */
    public Collection getEntityAccessList(String hostId, String roleId) 
    throws SwtException{
    	Collection coll = dao.getEntityAccessList(hostId, roleId);
    	return coll;
    }
    /**
     * 
     */
    public void saveUserStatus(UserStatus userStatus) 
    throws SwtException{
    	dao.saveUserStatus(userStatus);
    }
    
    public void updateUserStatus(UserStatus userStatus) 
    throws SwtException{
    	dao.updateUserStatus(userStatus);
    }
    
    //  Method logs the status of already logged-in user
    public void enterLoggedInUserStatus(User user,String ipAddress)
    throws SwtException{
        dao.enterLoggedInUserStatus(user,ipAddress);
    }
    /* (non-Javadoc)
     * @see org.swallow.service.LogonManager#saveUserStatusRow(org.swallow.control.model.UserStatus)
     */
    public void saveUserStatusRow(UserStatus userStatus) 
    throws SwtException {
        dao.saveUserStatusRow(userStatus);
    }
    
    /* Start: Code Chnaged by Mayank for Making User Login and Add User working */
    
    /**
     * This method update the user details to the database.
     * @param user
     * @throws SwtException
     */
    public void updateUserDetail(User user) 
    throws SwtException{
    	dao.updateUserDetail(user);
    }
    
    /* END: Code Chnaged by Mayank for Making User Login and Add User working */
    
    public User getUserDetail(String hostId, String userId) 
    throws SwtException{
    	return dao.getUserDetail(hostId, userId);
    }
    /*-- START:- CODE CHANGED FOR DEFECT NO.39 IN MENTISS --*/
    public Integer isNewUser(String hostId, String userId) 
    throws SwtException{
    	UserMaintenanceDAO mgr = (UserMaintenanceDAO) (SwtUtil.getBean("usermaintenanceDAO"));
    	List seqNo =  mgr.getLastSeqNo(hostId, userId);
    	Integer seqNoObj = null;
    	//int No;
		if (seqNo.get(0) != null){
			//No=Integer.parseInt(seqNo.get(0).toString());
			seqNoObj = Integer.valueOf(seqNo.get(0).toString());
		} 
    	return seqNoObj;
    }
    /*-- END:- CODE CHANGED FOR DEFECT NO.39 IN MENTISS --*/
    
    /*Start: Refer to Mantis issue : 0000391: Various login issues */
	/*If zero seqNo. is existing ==> the password is changed from the control/user option
	Once user successfully changes password this entry is deleted*/
    public boolean isZeroSeqNoExisting(String hostId, String userId) 
    throws SwtException{
    	UserMaintenanceDAO userMaintDao = (UserMaintenanceDAO) (SwtUtil.getBean("usermaintenanceDAO"));
    	List seqNo =  userMaintDao.getMinSeqNo(hostId, userId);
    	boolean passWordChangedFrmMenuOption = false;
    	
		if (seqNo.get(0) != null){			
			if (Integer.valueOf(seqNo.get(0).toString()).intValue() == 0){
				passWordChangedFrmMenuOption = true;
			}
		} 		
    	return passWordChangedFrmMenuOption;
    }
    /*End: Refer to Mantis issue : 0000391: Various login issues */

    /*Start : Setting User Preference for Flex Screens Done on 12-02-2008*/
    public List getUserPreference(String hostId, String entityId, String userId){
    	List userPreferenceList = dao.getUserPreference(hostId, entityId, userId);
    	return userPreferenceList;
    }
    public void setUserPreference(List userPreferenceList, String hostId, String entityId, String userId) 
    throws SwtException {
    	dao.setUserPreference(userPreferenceList, hostId, entityId, userId);
    }
	
	/*End : Setting User Preference for Flex Screens Done on 12-02-2008*/
    
    
    public MenuItem getMenuItem(String menuItemId, User user) throws SwtException {
    	return dao.getMenuItem(menuItemId, user);
    }
    
	public MenuItem getMenuItem(String menuItemId) throws SwtException {
		return dao.getMenuItem(menuItemId);
	}
	
	public MenuItem getMenuItemForRole(String menuItemId, String roleId) throws SwtException {
		
		return dao.getMenuItemForRole(menuItemId, roleId);
	}
	@Override
	public User getUserDetailByExtAuthId(String hostId, String extAuthId) throws SwtException {
	    	return dao.getUserDetailByExtAuthId(hostId, extAuthId);
	}
	
	public Collection getMenuListUpdated(User user) throws SwtException {
		// build the menu list as heirarchical list
		return getMenuHeirarchy(dao.getMenuList(user));
	}
  }
