/*
 * @(#)MatchingProcess.java 30/01/06
 * 
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 * 
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */

package org.swallow.batchScheduler;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Iterator;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.swallow.control.model.Scheduler;
import org.swallow.control.service.SchedulerManager;
import org.swallow.exception.SwtException;
import org.swallow.util.JDBCCloser;
import org.swallow.util.PropertiesFileLoader;
import org.swallow.util.SwtConstants;
import org.swallow.util.SwtUtil;
public class MatchingProcess extends SwtBasicJob {

	private final Log log = LogFactory.getLog(MatchingProcess.class);

	public String executeJob(Integer schedulerId) {
		String retValue = "F";
		Collection tasks = getAllMatchingCombinations();
		Collection results = runAllJobs(tasks);
		retValue = getFinalStatus(results);
		return retValue;
	}

	/**
	 * This method is used to create the matching process key parameters in a
	 * collection. Key contains hostId, entityId and currecnyCode for which
	 * matching has to be run.
	 * 
	 * @return matching parameters in a collection
	 */
	/*
	 * Start Code modified by Chidambaranathan for Mantis_1341 for coverting
	 * processing flag wait time in hours into minutes on1-July-2011
	 */
	private Collection<MatchingTask> getAllMatchingCombinations() {
		/* Local Variable Declarations */
		// Collection to hold matching parameter keys
		Collection<MatchingTask> matchingParameterKeys = null;
		// Statement object
		Statement st = null;
		// variable to hold time
		String processingFlagWaitTime_InMinutes = null;
		// variable to hold update query
		String query_update = null;
		// Connection object
		Connection conn = null;
		// last run order
		// variable to hold the non cyclic query
		String nonCyclicQuery = null;
		// variable to hold the cyclic query
		String cyclicQuery = null;
		// variable to hold st
		st = null;
		// variable to hold rs
		ResultSet rs = null;
		// SchedulerManager object
		SchedulerManager schedulerManager = null;
		// Scheduler object
		Scheduler scheduler = null;
		// Variable to hold the host Id
		String hostId = null;
		// Variable to hold the entity Id
		String entityId = null;
		// Variable to hold the currency code
		String currencycode = null;
		// Declare the MatchingTask object
		MatchingTask matchingTask = null;
		// Declare the SwtException object
		SwtException swtexp = null;
		// Variable to hold scheduler error message
		String schedulerErrorMsg = null;
		// Variable to hold match quality message
		String matchQualMsg = null;
		// To hold error message
		StringBuffer bufferObj = null;
		try {
			log.debug(this.getClass().getName()
					+ " - [getAllMatchingCombinations()] - " + "Entering");
			// get the connection
			conn = getConnection();
			// get the processing wait time
			processingFlagWaitTime_InMinutes = PropertiesFileLoader
					.getInstance().getPropertiesValue(
							SwtConstants.PROCESSINGFLAGWAITTIME_INMINUTES);
			// create instance for ArrayList
			matchingParameterKeys = new ArrayList<MatchingTask>();
			// create a statement
			st = conn.createStatement();
			// update query
			query_update = "update p_match_driver set PROCESSING_FLAG = 'N',"
					+ " UPDATE_DATE=GLOBAL_VAR.SYS_DATE,UPDATE_USER='SYSTEM' "
					+ " WHERE PROCESSING_FLAG = 'Y'" + " AND (UPDATE_DATE + "
					+ processingFlagWaitTime_InMinutes
					+ "/1440) < GLOBAL_VAR.SYS_DATE";
			st.executeUpdate(query_update);
		} catch (SQLException e) {
			log.error("SQLEXCEPTION IN MatchingProcess.ResetprocessingFlag "
					+ e.getMessage());
		} catch (Exception e) {
			log
					.error("EXCEPTION PROBLEM IN MatchingProcess.ResetprocessingFlag :  "
							+ e.getMessage());
		} finally {
			try {
				// Connection closing
				if (conn != null && st != null) {
					conn.commit();
					st.close();
				}
			} catch (SQLException e) {
				log
						.error("Final Exception IN MatchingProcess.ResetprocessingFlag : Can't close the session or statement - "
								+ e.getMessage());
			}
		}

		try {
			// get the SchedulerManager bean
			schedulerManager = (SchedulerManager) SwtUtil
					.getBean("schedulerManager");
			// get the scheduler job type
			scheduler = schedulerManager.getJobType();
			// query to get the cyclic job details
			cyclicQuery = "select match.host_id, match.entity_id, match.currency_code from "
					+ "p_match_driver match where match.new_move_flag = 'Y' and match.processing_flag = 'N' "
					+ "and currency_code != '*' and ((((global_var.sys_date - "
					+ "nvl(last_started, trunc(global_var.sys_date))) * 86400) >=(select nvl((duration_hours * 3600) "
					+ "+ (duration_mins * 60) + duration_secs, 0) from s_scheduler a, s_job b where "
					+ "a.host_id = b.host_id and a.job_id = b.job_id and "
					+ "b.program_name = 'org.swallow.batchScheduler.MatchingProcess')) "
					+ "or (global_var.sys_date <= nvl(last_started,global_var.sys_date))) "
					+ "order by last_started nulls first";
			// query to get the non cyclic job details
			nonCyclicQuery = "select match.host_id, match.entity_id, match.currency_code "
					+ "from p_match_driver match  where match.new_move_flag = 'Y' and "
					+ "match.processing_flag = 'N' and currency_code != '*' "
					+ "order by last_started nulls first";

			if (conn != null && !conn.isClosed()) {
				st = conn.createStatement();
				// If job type equals manual and test date not equal to null
				if (scheduler.getJobType().equals("C")) {
					// execute the query
					rs = st.executeQuery(cyclicQuery);
				} else {
					// execute the query
					rs = st.executeQuery(nonCyclicQuery);
				}
				while (rs.next()) {
					// Setting position
					hostId = rs.getString(1);
					entityId = rs.getString(2);
					currencycode = rs.getString(3);
					/*
					 * isDataPresent -- is true or false depending upon whether
					 * data exists in match quality table for given
					 * hostId/entityId/currencyCode combination
					 */
					boolean isDataPresent = checkDataInMatchQualityTable(
							hostId, entityId, currencycode, null);
					/*
					 * If data is present -- call the procedure else -- insert
					 * an entry into errorlog
					 */
					if (isDataPresent) {
						matchingTask = new MatchingTask(hostId, entityId,
								currencycode);
						matchingParameterKeys.add(matchingTask);
					} else {
						swtexp = new SwtException();
						schedulerErrorMsg = SwtUtil
								.getMessage("schedulerError", null);
						matchQualMsg = SwtUtil
								.getMessage("matchQualityNotDefined", null);
						// Creating Key Message for error log
						bufferObj = new StringBuffer(matchQualMsg);
						bufferObj.append(hostId);
						bufferObj.append("/");
						bufferObj.append(entityId);
						bufferObj.append("/");
						bufferObj.append(currencycode);
						// Inserting row in Error Log table
						swtexp.setErrorDesc(bufferObj.toString());
						swtexp.setSrcCodeLocation(schedulerErrorMsg);
						swtexp.setErrorLogFlag("Y");
						SwtUtil.logErrorInDatabase(swtexp);
					}
				}
			}
		} catch (SQLException e) {
			log
					.error("SQLEXCEPTION IN MatchingProcess.getAllMatchingCombinations : "
							+ e.getMessage());
		} catch (Exception e) {
			log
					.error("EXCEPTION PROBLEM IN MatchingProcess.getAllMatchingCombinations :  "
							+ e.getMessage());
		} finally {
			log.debug(this.getClass().getName()
					+ "- [getAllMatchingCombinations] - Exiting ");
			//edited by KaisBS for mantis 1745 : introducing the implementation of the JDBCCloser class			
			JDBCCloser.close(rs, st, null, null);
			// Nullifying the already created objects
			schedulerManager = null;
			matchingTask = null;
			scheduler = null;
			hostId = null;
			entityId = null;
			currencycode = null;
			swtexp = null;
			schedulerErrorMsg = null;
			matchQualMsg = null;
			bufferObj = null;
			processingFlagWaitTime_InMinutes = null;
			swtexp = null;
		}
		return matchingParameterKeys;
	}

	/*
	 * End Code modified by Chidambaranathan for Mantis_1341 for coverting
	 * processing flag wait time in hours into minutes on1-July-2011
	 */

	/**
	 * This methos is used to initiate the matching process from the collection
	 * of key parameters. It checks the number of maximum thread allowed from
	 * property file and creates that many thread for matching process.
	 * 
	 * @param coll
	 * @return
	 */
	private Collection runAllJobs(Collection coll) {
		log.debug("entering MatchingProcess.'runAllJobs' method");
		int maxConcurrentProcess = Integer.valueOf(SwtUtil
				.getMaxConcurrentMatchingProcess());
		Collection results = new ArrayList();
		if (coll != null && coll.size() > 0) {
			TaskManager taskManager = new TaskManager(maxConcurrentProcess,
					coll);
			results = taskManager.run();
		}
		log.debug("exiting MatchingProcess.'runAllJobs' method");
		return results;
	}

	/**
	 * 
	 * @param results
	 * @return
	 */
	private String getFinalStatus(Collection results) {
		log.debug("entering MatchingProcess.'getFinalStatus' method");
		String finalStatus = "S";
		if (results != null && results.size() > 0) {
			Iterator itr = results.iterator();
			while (itr.hasNext()) {
				TaskResult taskResult = (TaskResult) itr.next();
				String code = taskResult.getErrorCode();
				log.debug("code is" + code);
				if (code != null && code.equals("F")) {
					finalStatus = "F";
					break;
				}
			}
		}
		log.debug("exiting MatchingProcess.'getFinalStatus' method");
		return finalStatus;
	}

	/**
	 * This function checks whether data exists in P_MATCH_QUALITY Table for
	 * given hostId/entityId/currencyCode combination
	 * 
	 * @param hostId --
	 *            hostId
	 * @param entityId --
	 *            entityId
	 * @param currencyCode --
	 *            currencyCode
	 * @return -- boolean value
	 */

	public boolean checkDataInMatchQualityTable(String hostId, String entityId,
			String currencyCode, Connection conn) {
		log.debug("Entering into checkDataInMatchQualityTable method");
		boolean isDataPresent = false;
		String query = "select count(*) from P_MATCH_QUALITY m where "
				+ "m.HOST_ID =? and m.ENTITY_ID =? and m.CURRENCY_CODE in (? , '*') ";
		// Check condition if connection is null, get the job connection
		if (conn == null)
			conn = getConnection();

		PreparedStatement pst = null;
		ResultSet rs = null;
		try {
			pst = conn.prepareStatement(query);
			pst.setString(1, hostId);
			pst.setString(2, entityId);
			pst.setString(3, currencyCode);
			rs = pst.executeQuery();
			int count = 0;
			if (rs != null && rs.next()) {
				count = rs.getInt(1);
			}
			isDataPresent = count == 0 ? false : true;
		} catch (SQLException e) {
			log.error("SQLEXCEPTION IN MatchingProcess.ResetprocessingFlag "
					+ e.getMessage());
		} catch (Exception e) {
			log.error("SQLEXCEPTION IN MatchingProcess.ResetprocessingFlag "
					+ e.getMessage());

		} finally {
			
			//edited by KaisBS for mantis 1745 : introducing the implementation of the JDBCCloser class
			JDBCCloser.close(rs, pst, null, null);
			
		}

		log
				.debug("Exiting MatchingProcess.checkDataInMatchQualityTable method");

		return isDataPresent;
	}
}
