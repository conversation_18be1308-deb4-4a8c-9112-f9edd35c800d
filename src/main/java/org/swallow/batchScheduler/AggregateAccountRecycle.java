package org.swallow.batchScheduler;

import java.sql.CallableStatement;
import java.sql.Connection;
import java.sql.Timestamp;
import java.sql.Types;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.swallow.exception.SwtErrorHandler;
import org.swallow.util.CacheManager;
import org.swallow.util.JDBCCloser;
import org.swallow.util.SwtUtil;

/**
 *
 * <AUTHOR>
 *
 * TODO To change the template for this generated type comment go to
 * Window - Preferences - Java - Code Style - Code Templates
 */
public class AggregateAccountRecycle extends SwtBasicJob {

	private final Log log = LogFactory.getLog(AggregateAccountRecycle.class);

	public String executeJob(Integer schedulerId){
		log.debug("Start executeJob for AggregateAccountRecycle process");
		String retValue="F";
		Connection conn=null;
		CallableStatement pstmt=null;
		try{
			Timestamp timeStamp=new Timestamp(new java.util.Date().getTime());
			conn = getConnection();
			String hostId= CacheManager.getInstance().getHostId();
			//Log Added for calculating the Time taken for the stored procedure to execute on 25-03-2008
			log.debug(" $$$$$$$$$  pk_application.sp_AGGREGATE_ACCOUNT_RECYCLE Called at "+SwtUtil.getTimeWithMilliseconds());
			pstmt=conn.prepareCall("{call pk_application.sp_AGGREGATE_ACCOUNT_RECYCLE(?,?)}");
			pstmt.setString(1,hostId);
			pstmt.registerOutParameter(2, Types.VARCHAR);
			pstmt.executeUpdate();
			//Log Added for calculating the Time taken for the stored procedure to execute on 25-03-2008
			log.debug(" $$$$$$$$$  pk_application.sp_AGGREGATE_ACCOUNT_RECYCLE Ended at "+SwtUtil.getTimeWithMilliseconds());
			retValue=pstmt.getString(2);
			int result=Integer.parseInt(retValue);
			if(result==0)
				retValue="S";
			else
				retValue="F";
			log.debug("Result of execution of AggregateAccountRecycle "+retValue);
		}catch(Exception  e){
			SwtUtil.logErrorInDatabase(SwtErrorHandler.getInstance()
													  .handleException(e,"executeJob", AggregateAccountRecycle.class));
			log.debug("AggregateAccountRecycle: Error in executeJob - " + e.getMessage());
		}finally{
			// edited by KaisBS for mantis 1745 : introducing the implementation of the JDBCCloser class
			JDBCCloser.close(pstmt);
				
		}
		return retValue;
	}
}
