/*
 * @(#)TaskManager.java
 * 
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 * 
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */

package org.swallow.batchScheduler;

import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Hashtable;
import java.util.Iterator;
import java.util.concurrent.Callable;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;
import java.util.concurrent.FutureTask;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.swallow.control.model.Scheduler;
import org.swallow.control.service.SchedulerManager;
import org.swallow.exception.SwtException;
import org.swallow.util.JDBCCloser;
import org.swallow.util.SwtUtil;

public class TaskManager {
	/* Logger object to write log into Server.log file */
	private static final Log log = LogFactory.getLog(TaskManager.class);

	/*
	 * Maximum number of task that can run simultaneously. This is defined in
	 * swtcommon.properties file
	 */
	private int maxConcurrentTasks;
	/* Collection of Task(Job) objects */
	private Collection tasks;
	// variable to hold connection object
	private Connection conn = null;
	// variable to hold collection
	private Collection results = new ArrayList();
	// object used to assign tasks
	ExecutorService executor = null;
	// Variable used to store the currencyList
	ArrayList currencyList = new ArrayList();
	// Variable used to store the total currency
	ArrayList<String> totalCurrency = new ArrayList<String>();
	// used to store the taskresult & currencycode
	Hashtable addresult = new Hashtable();

	/**
	 * @param maxConcurrentTasks
	 * @param tasks
	 */
	public TaskManager(int maxConcurrentTasks, Collection tasks) {
		super();
		this.maxConcurrentTasks = maxConcurrentTasks;
		this.tasks = tasks;
		if (!(tasks != null && tasks.size() > 0)) {
			throw new NullPointerException(
					"Task List is empty. No jobs to execute.");
		}
	}

	/* Start:code modified by sudhakar for 1054_STL_039/040 on 24-May-2012 */
	/**
	 * This method is used to fetch the matching tasks which is getting by
	 * match recovering settings
	 * 
	 * @param
	 * @return Collection
	 */
	public Collection<MatchingTask> fetchMatchingTasks() {

		// Declare the Statement
		Statement statement = null;
		// variable to hold the non cyclic query
		String nonCyclicQuery = null;
		// variable to hold the cyclic query
		String cyclicQuery = null;
		// Declare the ResultSet
		ResultSet resultSet = null;
		// used to store the matching tasks
		Collection<MatchingTask> matchingParameterKeys = null;
		// SchedulerManager object
		SchedulerManager schedulerManager = null;
		// Scheduler object
		Scheduler scheduler = null;
		// Declare the MatchingProcess object
		MatchingProcess matchingProcess = null;
		// Variable to hold the host Id
		String hostId = null;
		// Variable to hold the entity Id
		String entityId = null;
		// Variable to hold the currency code
		String currencycode = null;
		// Declare the MatchingTask object
		MatchingTask matchingTask = null;
		// Declare the SwtException object
		SwtException swtException = null;
		// Variable to hold scheduler error message
		String schedulerErrorMsg = null;
		// Variable to hold match quality message
		String matchQualMsg = null;
		// To hold error message
		StringBuffer bufferObj = null;
		try {
			log.debug(this.getClass().getName()
					+ "- [fetchMatchingTasks] - Entering ");
			// Initialize the ArrayList
			matchingParameterKeys = new ArrayList<MatchingTask>();
			// get the SchedulerManager bean
			schedulerManager = (SchedulerManager) SwtUtil
					.getBean("schedulerManager");
			// get the scheduler job type
			scheduler = schedulerManager.getJobType();
			// query to get the cyclic job details
			cyclicQuery = "select match.host_id, match.entity_id, match.currency_code from "
					+ "p_match_driver match where match.new_move_flag = 'Y' and match.processing_flag = 'N' "
					+ "and currency_code != '*' and ((((global_var.sys_date - "
					+ "nvl(last_started, trunc(global_var.sys_date))) * 86400) >=(select nvl((duration_hours * 3600) "
					+ "+ (duration_mins * 60) + duration_secs, 0) from s_scheduler a, s_job b where "
					+ "a.host_id = b.host_id and a.job_id = b.job_id and "
					+ "b.program_name = 'org.swallow.batchScheduler.MatchingProcess')) "
					+ "or (global_var.sys_date <= nvl(last_started,global_var.sys_date))) "
					+ "order by last_started nulls first";
			// query to get the non cyclic job details
			nonCyclicQuery = "select match.host_id, match.entity_id, match.currency_code "
					+ "from p_match_driver match  where match.new_move_flag = 'Y' and "
					+ "match.processing_flag = 'N' and currency_code != '*' "
					+ "order by last_started nulls first";
			// get the connection
			conn = ConnectionManager.getInstance().databaseCon();
			// Execute the cyclicQuery and nonCyclicQuery based on the
			// database connection and scheduler job status
			if (conn != null && !conn.isClosed()
					&& !scheduler.getJobStatus().equals("D")) {
				// create the statement
				statement = conn.createStatement();
				// If job type equals manual and test date not equal to null
				if (scheduler.getJobType().equals("C")) {
					// execute the cyclicQuery query
					resultSet = statement.executeQuery(cyclicQuery);
				} else {
					// execute the nonCyclicQuery query
					resultSet = statement.executeQuery(nonCyclicQuery);
				}
				while (resultSet.next()) {
					// get the hostId
					hostId = resultSet.getString(1);
					// get the entityId
					entityId = resultSet.getString(2);
					// get the currencycode
					currencycode = resultSet.getString(3);
					/*
					 * isDataPresent -- is true or false depending upon whether
					 * data exists in match quality table for given
					 * hostId/entityId/currencyCode combination
					 */
					matchingProcess = new MatchingProcess();
					boolean isDataPresent = matchingProcess
							.checkDataInMatchQualityTable(hostId, entityId,
									currencycode, conn);
					/*
					 * If data is present -- call the procedure else -- insert
					 * an entry into errorlog
					 */
					if (isDataPresent) {
						matchingTask = new MatchingTask(hostId, entityId,
								currencycode);
						if (currencyList.contains(matchingTask
								.getCurrencyCode())) {
							matchingParameterKeys.add(matchingTask);
							currencyList.remove(matchingTask.getCurrencyCode());
						}
						if (!totalCurrency.contains(matchingTask
								.getCurrencyCode())) {
							totalCurrency.add(matchingTask.getCurrencyCode());
							matchingParameterKeys.add(matchingTask);
						}
					} else {
						swtException = new SwtException();
						schedulerErrorMsg = SwtUtil
								.getMessage("schedulerError", null);
						matchQualMsg = SwtUtil
								.getMessage("matchQualityNotDefined", null);
						// Creating Key Message for error log
						bufferObj = new StringBuffer(matchQualMsg);
						bufferObj.append(hostId);
						bufferObj.append("/");
						bufferObj.append(entityId);
						bufferObj.append("/");
						bufferObj.append(currencycode);
						// Inserting row in Error Log table
						swtException.setErrorDesc(bufferObj.toString());
						swtException.setSrcCodeLocation(schedulerErrorMsg);
						swtException.setErrorLogFlag("Y");
						SwtUtil.logErrorInDatabase(swtException);
					}
				}
			}
			log.debug(this.getClass().getName()
					+ "- [fetchMatchingTasks] - Exiting ");
		} catch (SQLException e) {
			log.error("SQLEXCEPTION IN TaskManager.fetchMatchingTasks: "
					+ e.getMessage());
		} catch (Exception e) {
			log.error("EXCEPTION PROBLEM IN TaskManager.fetchMatchingTasks :  "
					+ e.getMessage());
		} finally {
			//edited by KaisBS for mantis 1745 : introducing the implementation of the JDBCCloser class
			JDBCCloser.close(resultSet, statement, null, null);
			try {
				conn.commit();
				// return the connection
				if ((conn != null) && (!conn.isClosed())) {
					ConnectionManager.getInstance().retrunConnection(conn);
				}
			} catch (SQLException sqlExp) {
				 log.error("org.swallow.batchScheduler.SwtJobScheduler - [fetchMatchingTasks] - exception :  " + sqlExp.getMessage());
			}
			// nullify the objects
			schedulerManager = null;
			matchingTask = null;
			scheduler = null;
			hostId = null;
			entityId = null;
			currencycode = null;
			swtException = null;
			schedulerErrorMsg = null;
			matchQualMsg = null;
			bufferObj = null;
		}
		return matchingParameterKeys;
	}

	/* End:code modified by sudhakar for 1054_STL_039/040 on 24-May-2012 */

	/**
	 * This method is called for starting execution of a job(Task).
	 * 
	 * @return results
	 */
	public Collection fetchResults() {
		log.debug(this.getClass().getName() + "- [fetchResults] - Entering ");
		// iterate the tasks
		Iterator itr = tasks.iterator();
		// obj used to get the itr object
		Object obj = new Object();
		while (itr.hasNext()) {
			// Assign the task
			obj = itr.next();
			Callable<TaskResult> task = ((Callable) obj);
			Future<TaskResult> result = new FutureTask(task);
			// submit the task
			result = executor.submit(task);
			MatchingTask matchingTask = (MatchingTask) obj;
			// add the result to collection
			results.add(result);
			// store the result and currencycode
			addresult.put(result.toString(), matchingTask.getCurrencyCode());
			if (!totalCurrency.contains(matchingTask.getCurrencyCode())) {
				totalCurrency.add(matchingTask.getCurrencyCode());
			}
		}
		log.debug(this.getClass().getName() + "- [fetchResults] - Exiting ");
		return results;
	}

	/**
	 * This method is called for starting execution of a job(Task).
	 * 
	 * @return
	 */
	public Collection run() {
		log.debug(this.getClass().getName() + "- [run] - Entering ");
		Collection retColl = new ArrayList();
		// assign the maxConcurrentTasks to threadpool
		executor = Executors.newFixedThreadPool(maxConcurrentTasks);
		// call the fetchResults
		fetchResults();
		// assign the starttime value
		long startTime = 0;
		// flag used to check the threadpool size
		boolean flag = false;
		// flag used to check result whether present the existing
		boolean runFlag = false;
		while (true) {
			// iterate the results
			Iterator itrResult = results.iterator();
			// collection used to store the removable object
			Collection removeObj = new ArrayList();
			for (int j = 0; j < results.size(); j++) {
				Object taskResult = null;
				Future<TaskResult> obj = (Future) itrResult.next();
				// if task is done, remove the obj from the collection and add
				// the retcoll collection
				if (obj.isDone()) {
					try {
						currencyList.add(addresult.get(obj.toString()));
						taskResult = ((Future) obj).get();
						retColl.add(taskResult);
						removeObj.add(obj);
						runFlag = true;
					} catch (Exception e) {
						log.debug(this.getClass().getName()
								+ "exception catched in run method"
								+ e.getMessage());
					}
				}
			}
			// remove the object from collection which process is done
			Iterator remObj = removeObj.iterator();
			while (remObj.hasNext()) {
				results.remove(remObj.next());
			}
			// call the fetchcurrency when complete the task
			if (runFlag) {
				tasks = fetchMatchingTasks();
				fetchResults();
				runFlag = false;
			}
			// check the thread pool size, if pool is free then call the
			// fetchCurrency and assign the task if new task available
			// This is called for every 10 seconds
			if (results.size() < maxConcurrentTasks) {
				if (SwtUtil.getSystemDatewithTime().getTime() - startTime > 10000) {
					startTime = SwtUtil.getSystemDatewithTime().getTime();
					flag = true;
				} else {
					flag = false;
				}
				if (flag) {
					tasks = fetchMatchingTasks();
					fetchResults();
				}
			}
			// if all tasks completed then the process completed
			if (results.size() == 0) {
				break;
			}
			try {
				Thread.sleep(1000);
			} catch (InterruptedException e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
			}
		}
		executor.shutdown();
		log.debug(this.getClass().getName() + "- [run] - Exiting ");
		return retColl;
	}
}
