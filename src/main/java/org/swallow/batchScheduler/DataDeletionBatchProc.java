

package org.swallow.batchScheduler;
import java.io.File;
import java.sql.CallableStatement;
import java.sql.Connection;
import java.sql.SQLException;
import java.sql.Types;
import java.util.ArrayList;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.swallow.exception.SwtException;
import org.swallow.reports.service.SchedReportHistManager;
import org.swallow.util.CacheManager;
import org.swallow.util.JDBCCloser;
import org.swallow.util.PropertiesFileLoader;
import org.swallow.util.SwtConstants;
import org.swallow.util.SwtUtil;

/*
 * Created on Jan 30, 2006
 *
 * TODO To change the template for this generated file go to
 * Window - Preferences - Java - Code Style - Code Templates
 */

/**
 * <AUTHOR>
 *
 * TODO To change the template for this generated type comment go to
 * Window - Preferences - Java - Code Style - Code Templates
 */
public class DataDeletionBatchProc extends SwtBasicJob {
	
	private final Log log = LogFactory.getLog(DataDeletionBatchProc.class);
	
	/**
	 * This method is used to execute the Archiveing Process from database.
	 */
	public String executeJob(Integer schedulerId)
	{
		String retValue="F";
		Connection conn=null;
		CallableStatement pstmt=null;
		ArrayList<String> reportsPathList = null;
		try
		{
			conn = ConnectionManager.getInstance().databaseCon();
			String hostId= CacheManager.getInstance().getHostId();
			String archiveOption =PropertiesFileLoader.getInstance().getPropertiesValue(SwtConstants.ARCHIVE_OPTION);
			
			/* Checking the archive option mentioned in the Swtcommon.properties file. In case it is missing or having wrong option value,
			inserting a row in S_error_log table */
			if(!(archiveOption.equals("0")||archiveOption.equals("1")||archiveOption.equals("2")))
			{
				//Log Added for calculating the Time taken for the stored procedure to execute on 25-03-2008
                log.debug(" $$$$$$$$$  Sp_Error_Log Called at "+SwtUtil.getTimeWithMilliseconds());
			    pstmt=conn.prepareCall("{call  Sp_Error_Log(?,?,?,?,?)}");
			    pstmt.setString(1,"SYSTEM");
				pstmt.setString(2,"SYS_DBSERVER");
				pstmt.setString(3,"DataDeletionBatchProc");
				pstmt.setString(4,"0");
				pstmt.setString(5,"Invalid archive.option value");
				retValue="F";
			}else{
				reportsPathList = new ArrayList<String>();
				
				ArrayList<Object[]> histResult = null;
				Object[] row = null;
				String location= "";
				String Filename="";
				String pathfile="";
				SchedReportHistManager schedReportHistManager = null;
				schedReportHistManager = (SchedReportHistManager) SwtUtil.getBean("schedReportHistManager");
				
				// reports in line with retention times
				histResult = schedReportHistManager.getSchedReportHistListRetentionTime(null);
				if (histResult != null && histResult.size()>0) {
					log.debug("RemoveReportsDir- Entering into the iterator loop for delete reports in line with retention times");
					for (int i = 0; i < histResult.size(); i++) {
						row =  histResult.get(i);
						Filename=(String) (row[0]);
						location= (String) row[1];
						pathfile=location+"/"+Filename;
						reportsPathList.add(pathfile);
					}
				}
				
				String archiveDirectoryPath =PropertiesFileLoader.getInstance().getPropertiesValue(SwtConstants.ARCHIVE_DIRECTORY_PATH);
				//Log Added for calculating the Time taken for the stored procedure to execute on 25-03-2008
                log.debug(" $$$$$$$$$  PK_ARCHIVE.execute_process Called at "+SwtUtil.getTimeWithMilliseconds());
				pstmt=conn.prepareCall("{call  PK_ARCHIVE.execute_process(?,?,?,?)}");
				pstmt.setString(1,hostId);
				pstmt.setString(2,archiveOption);
				pstmt.setString(3,archiveDirectoryPath);
				pstmt.registerOutParameter(4, Types.VARCHAR);
				pstmt.executeUpdate();
				//Log Added for calculating the Time taken for the stored procedure to execute on 25-03-2008
                log.debug(" $$$$$$$$$  PK_ARCHIVE.execute_process Ended at "+SwtUtil.getTimeWithMilliseconds());
				retValue=pstmt.getString(4);
				int result=Integer.parseInt(retValue);
				retValue = (result==0) ? "S" : "F";
				if(retValue.equals("S")) {
					removeReportsDir(reportsPathList);
				}
			}
			//If all is ok, delete the reports
		}catch(Exception  e){
			log.debug("PROBLEM IN DataDeletionBatchProc.executeJob : " + e.getMessage());
			SwtException se = new SwtException();
			se.setUserId("SYSTEM");
			se.setErrorLogFlag("Y");
			se.setErrorDesc(e.getMessage());
			se.setSrcCodeLocation("DataDeletionBatchProc.executeJob");
			SwtUtil.logErrorInDatabase(se);
			retValue="F";
			e.printStackTrace();
		}finally{
					
			//edited by KaisBS for mantis 1745 : introducing the implementation of the JDBCCloser class
			JDBCCloser.close(pstmt);
				
				try{
				//Returning the connection object 
				// Start: Code modified by Kalidass G to check whether connection is open on 26-Jan-10 for the Mantis 1114
				if((conn != null) && (!conn.isClosed())){
					ConnectionManager.getInstance().retrunConnection(conn);	
				}	
				// End: Code modified by Kalidass G to check whether connection is open on 26-Jan-10 for the Mantis 1114
			} catch (SQLException e1) {
				log.error("org.swallow.batchScheduler.DataDeletionBatchProc - [executeJob] - Exception - " + e1.getMessage());
			}
			
		}
		return retValue;
	}
	
	
	/**
	 * This method will remove the associated report files (in OS directories) in line with retention times
	 * @throws SwtException 
	 * 
	 */

	public void removeReportsDir(ArrayList<String> pathList) throws SwtException { 
		String pathfile="";
		
		// reports in line with retention times
		if (pathList != null && pathList.size()>0) {
			log.debug("RemoveReportsDir- Entering into the iterator loop for delete reports in line with retention times");
			for (int i = 0; i < pathList.size(); i++) {
				pathfile =  pathList.get(i);
				File file = new File(pathfile);
				if (file.exists()) {
					file.delete();
				}
			}
		}
	}
	
}
