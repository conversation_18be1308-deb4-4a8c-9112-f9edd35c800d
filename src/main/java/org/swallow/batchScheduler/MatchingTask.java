/*
 * @(#)MatchingTask.java 24/08/06
 * 
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 * 
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */


package org.swallow.batchScheduler;

import java.sql.CallableStatement;
import java.sql.Connection;
import java.sql.Types;
import java.util.Date;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.swallow.control.dao.SchedulerDAO;
import org.swallow.control.model.Job;
import org.swallow.control.model.JobStatus;
import org.swallow.control.model.Scheduler;
import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.util.JDBCCloser;
import org.swallow.util.SwtConstants;
import org.swallow.util.SwtUtil;

public class MatchingTask extends SwtTask {

	private static final Log log = LogFactory.getLog(MatchingTask.class);

	private String hostId;

	private String entityId;

	private String currencyCode;
	/**
	 * This is constructor method to intiate a matching thread for given hostId,entityId and currencyCode
	 * @param hostId
	 * @param entityId
	 * @param currencyCode
	 */
	public MatchingTask(String hostId, String entityId, String currencyCode) {
		super();
		this.hostId = hostId;
		this.entityId = entityId;
		this.currencyCode = currencyCode;
	}

	// Start: Code added by Bala for mantis 1206 on 01122010
	/**
	 * This is used to return the CurrencyCode
	 * @return currencyCode
	 */
	public String getCurrencyCode(){
		return currencyCode;
	}
	// End: Code added by Bala for mantis 1206 on 01122010
	
	/**
	 * This methos is used to call the Matching Process procedure of database.
	 */
	public TaskResult call() {
		log.debug("START : MATCHING PROCESS CALL -> FOR HostId - " + hostId + " EntityId - " + entityId + " CurrencyCode - " + currencyCode);
		Connection conn = null;
		CallableStatement pstmt = null;
		String retValue = "-1";
		TaskResult taskResult = new TaskResult();
		Date applicationDate = null;
		// start check for closing flag in database
		try {
			if (isJobClosing()) {
				retValue = "S";
			} else {
				/* START : Code Modified to provide the testdate */
				// Code Added BY sumit to pass
				applicationDate = SwtUtil.getTestDateFromParams(SwtUtil.getCurrentHostId());
				/* END : Code Modified to provide the testdate */
				conn = ConnectionManager.getInstance().databaseCon();
				//Log Added for calculating the Time taken for the stored procedure to execute on 25-03-2008
                log.debug(" $$$$$$$$$  pk_matching_process.Matching_Proc_Driver_Parallel Called at "+SwtUtil.getTimeWithMilliseconds());
				pstmt = conn.prepareCall("{call pk_matching_process.Matching_Proc_Driver_Parallel(?,?,?,?,?)}");
				pstmt.setString(1, hostId);
				pstmt.setString(2, entityId);
				pstmt.setString(3, currencyCode);
				/* START : Code Modified to provide the testdate */
				pstmt.setDate(4, SwtUtil.truncateDateTime(applicationDate)); 
				/* END : Code Modified to provide the testdate */				
				pstmt.registerOutParameter(5, Types.VARCHAR);
				pstmt.executeUpdate();
				//Log Added for calculating the Time taken for the stored procedure to execute on 25-03-2008
                log.debug(" $$$$$$$$$  pk_matching_process.Matching_Proc_Driver_Parallel Ended at "+SwtUtil.getTimeWithMilliseconds());
				/* START: Code fixed to extract correct out parameter, 29-MAY-2007 */
				retValue = pstmt.getString(5);
				/* END: Code fixed to extract correct out parameter, 29-MAY-2007 */
				int result = Integer.parseInt(retValue);
				retValue = result == 0 ? "S" : "F";
			}
		} catch (Exception e) {retValue = "F";
			StringBuffer methodSignature = new StringBuffer("Matching_Proc_Driver_Parallel(");
			methodSignature.append(hostId+",");
			methodSignature.append(entityId+",");
			methodSignature.append(currencyCode+",");
			if (applicationDate != null) {
				methodSignature.append(SwtUtil.truncateDateTime(applicationDate)+",");
			}
			methodSignature.append(")");
			SwtException se = SwtErrorHandler.getInstance()
				 .handleException(e,methodSignature.toString(), MatchingTask.class);
			se.setSrcCodeLocation(se.getSrcCodeLocation()+ "[" + methodSignature.toString()+"]");
			SwtUtil.logErrorInDatabase(se);
			log.debug("MatchingTask: Exception in call():- " + e.getMessage());
		} finally {
			
			//edited by KaisBS for mantis 1745 : introducing the implementation of the JDBCCloser class
			JDBCCloser.close(null, pstmt, conn, null);
			
		}
		
		log.debug("END : MATCHING PROCESS CALL -> FOR HostId - " + hostId + " EntityId - " + entityId + " CurrencyCode - " + currencyCode);
		taskResult.setErrorCode(retValue);
		return taskResult;
	}

	/**
	 * Start to check closing flag in database so that threads manage to run
	 * after < ExecutorService shutdown > in TaskManager not call Matching proc.
	 * <AUTHOR>
	 */
	public boolean isJobClosing() {
		SchedulerDAO schedulerDAO = (SchedulerDAO) (SwtUtil.getBean("schedulerDAO"));
		JobStatus jobStatus = new JobStatus();
		try {
			/* JobId has been hardcoded to 3 for MatchingProcess. In case of any change in Predict Main database script 
			 * we need to cross check this value.
			 */
			Scheduler scheduler = schedulerDAO.getJobType(SwtUtil.getCurrentHostId());
			jobStatus = schedulerDAO.getJobStatus(scheduler.getScheduleId());
		} catch (SwtException e) {
			log.debug("PROBLEM IN MATCHING PROCESS isJobClosing : " + e.getMessage());
			e.printStackTrace();
		}catch (Exception e) {
			log.debug("PROBLEM IN MATCHING PROCESS isJobClosing : " + e.getMessage());
			e.printStackTrace();
		} 
		String status = jobStatus.getCurrentStatus();
		return status.equals(SwtConstants.JOB_STATUS_CLOSING);
	}
	
	
}
