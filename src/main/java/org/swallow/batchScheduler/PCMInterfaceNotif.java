/*
 * @(#)PCMInterfaceNotif.java 1.0 20/06/2019
 *
 * Copyright (c) 2006-2019 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.batchScheduler;

import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Collection;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.swallow.control.dao.InterfaceInterruptDAO;
import org.swallow.control.dao.soap.Heartbeat;
import org.swallow.control.dao.soap.Heartbeat.InterfaceInfo.Interface;
import org.swallow.control.dao.soap.Heartbeat.InterfaceInfo.Interface.Beans.Bean;
import org.swallow.control.dao.soap.Heartbeat.InterfaceInfo.Interface.Messages.Message;
import org.swallow.control.model.Notifications;
import org.swallow.control.service.InterfaceInterruptManager;
import org.swallow.control.service.impl.InterfaceInterruptManagerImpl;
import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.util.CacheManager;
import org.swallow.util.PropertiesFileLoader;
import org.swallow.util.SwtConstants;
import org.swallow.util.SwtUtil;


/**
 * PCMInterfaceNotif: Calls the InputController implemented as web
 * services and gets all statistics for PCM. Filter the statistics to get a collection
 * of interface name and respective filemanager_lastcommit time. Then calls the
 * respective manager method to process the statistics.
 *
 */
public class PCMInterfaceNotif extends SwtBasicJob {

	/*
	 * Log instance for InterfaceNotification
	 */
	private final Log log = LogFactory.getLog(PCMInterfaceNotif.class);
	/*
	 * Code modified by Saminathan to log input notification errors reported by
	 * James on 14-Nov-2008
	 */
	private final Log logtemp = LogFactory.getLog("PCM-INTERFACE-MONITOR");
	/* Reference variable for InterfaceInterruptDAO */
	private InterfaceInterruptDAO dao;
	/**
	 * Get the response from soap service, filter the statistics and call the
	 * respective manager method.
	 * 
	 * @overridden
	 * @param
	 * @return String
	 */
	public String executeJob(Integer schedulerId) {

		log.debug(this.getClass().getName() + "- [executeJob] - Entry");

		// Object array to store the soap response
		Object[] soapResponse = null;
		// Object array to read the arrays contained in the soap array
		Object[] rawRow = null;
		// A map containing, statistics of each interface, in turn stored in
		// listOfStatistics
		Map<Object, Object> statisticsMap = null;
		// Instance of InterfaceInterruptionsManager class
		InterfaceInterruptManager interfaceInterruptManager = null;
		// Boolean variable returned on manager process call.
		boolean processStatistics = false;
		// reference variable for Notifications
		Notifications notification = null;
		// Dates are provided by SI on ISO format
		SimpleDateFormat sdf = new SimpleDateFormat (SwtConstants.ISO_DATE_FORMAT); 

		Date engineStartDate = null;
		Date engineCurrentDate = null;		
		//Heartbeat object created using jaxb
		Heartbeat heartbeatInfo = null;

		// Message type for an interface
		String msgType = null;
		try {
			// Instantiate the object
			statisticsMap = new HashMap();
			// Instantiate the object to hold statistics of interface
			// Object reference for InterfaceInterruptManager
			interfaceInterruptManager = new InterfaceInterruptManagerImpl();
			// set the object using get bean method then gets it here
			dao = (InterfaceInterruptDAO) SwtUtil.getBean("interfaceInterruptDAO");
			// Create an object for Notfications to set values
			notification = new Notifications();
			/* Calling DAO to get job status */
			processStatistics = dao.notificationJobStatus(this.getClass()
					.getName(), true);
			/*
			 * If job status is 'D'[Disable] the whole condition block will not
			 * be executed
			 */
			if (!(processStatistics)) {
				// Call the service and get the object array as response
				try {
					heartbeatInfo = SwtUtil.getHeartBeat(true);
					if (heartbeatInfo == null) {
						throw new Exception("RPC: Unable to connect to Smart Engine for PCM");
					}
					// Get the Engine Start/Current Date
					engineStartDate = sdf.parse(heartbeatInfo.getEngine().getStartDate());
					engineCurrentDate = sdf.parse(heartbeatInfo.getEngine().getCurrDate());
					 
					Calendar engineCurrentDateCal = Calendar.getInstance();
					engineCurrentDateCal.setTime(engineCurrentDate);
					
					// Get the list of running interfaces on SI engine
					 List<Interface> interfaces = heartbeatInfo.getInterfaceInfo().getInterface();
					 // Loop on interfaces
					 for(Iterator<Interface> iIt = interfaces.iterator();iIt.hasNext();){
						Interface iface = iIt.next();
						// Iterate into active interfaces only
						if (iface.getActive() == 1){
							String ifaceId = iface.getId();
							// Last message date relative to Interface
							Date lastMsgDate = null;
							// List of beans in the interface
							List<Bean> beans  = null;	
							// List of messages in the interface
							List<Message> messages  = null;	
							
							if(iface.getMessages() != null && iface.getMessages().getMessage() != null && iface.getMessages().getMessage().size() >= 2){
								// Get the list of messages
								messages  = iface.getMessages().getMessage();

								for(Iterator<Message> mIt = messages.iterator();mIt.hasNext();){
									// Get the message 
									Message message = mIt.next();
									// Get the message type
									msgType = message.getMsgType();
									// Get the last message date if exists, if not we will retrieve the engine start date
									if(!SwtUtil.isEmptyOrNull(message.getLastmsgDate())){
										lastMsgDate = sdf.parse(message.getLastmsgDate());
									}
									
									// Set the lastMsgDate as engineStartDate if it is null 
									if(lastMsgDate == null)
										lastMsgDate = engineStartDate;
									
									// Add to the map the msgType with an object which contains as the first element is the elapsed time, 
									// the second element as false (is not an interface) and the third element is the interface id 
									statisticsMap.put(msgType, new Object[]{engineCurrentDate.getTime() - lastMsgDate.getTime(), false, ifaceId});	
								}
							}else{
								// Loop on the list of beans
							     if(iface.getBeans() != null && iface.getBeans().getBean() != null ){
									 beans  = iface.getBeans().getBean();
									 for(Iterator<Bean> bIt = beans.iterator();bIt.hasNext();){
										 Bean bean = bIt.next();
										 if (bean.getName().contains("updater")){
											 if(!SwtUtil.isEmptyOrNull(bean.getLastmsgDate())){
												 lastMsgDate = sdf.parse(bean.getLastmsgDate());		 
											 }
										 }
									 }
								 }
								 
								if(lastMsgDate == null)
									lastMsgDate = engineStartDate;
								
								// Add to the map the ifaceId with an object which contains as the first element is the elapsed time, 
								// the second element as true (is  an interface) and the third element is the interface id 
								statisticsMap.put(ifaceId, new Object[]{engineCurrentDate.getTime() - lastMsgDate.getTime(), true, ifaceId});
							}
						}
					}
		
					// Check response object has value or not 
					// Condition modified by Saber Chebka
					if (statisticsMap==null||statisticsMap.size()==0) {
						/*
						 * Code modified by Saminathan to log input notification
						 * errors reported by James on 14-Nov-2008
						 */
						logtemp
								.error("Unable to get SOAP response from service ");
						throw new SwtException(
								"Unable to get SOAP response from service ");
					}
				} catch (Exception e) {
					/*
					 * When unable to connect service properly or get the
					 * response,an error message has to be alerted in JSP,for
					 * this purpose insert the error message to the DB after
					 * delete all notifications detail.
					 */
					try {
						dao = (InterfaceInterruptDAO) SwtUtil.getBean("interfaceInterruptDAOPCM");
						// get current host id then set into object
						notification.setHostId(CacheManager.getInstance()
								.getHostId());
						notification.setEntityId("");
						// set relational id is retrieved from swtconstants
						notification
								.setRelationId(SwtConstants.INTERFACE_NOTIFICATION_ERRTYPE);
						// set interface type is retrieved from swtconstants
						notification
								.setNotificationType(SwtConstants.INTERFACE_NOTIFICATION_TYPE);
						// set error alert message is retrieved from swtconstants
						notification.setNotificationMessage(SwtUtil
								.getMessage("interfaceNotificationAlertPCM", null));

						// delete all notifications details before insert the error message
						dao.deleteNotification(notification, true);
						// insert error message details to database
						dao.insertNotifications(notification, true);
						// Return "F" to set the job execution as failed
						return "F";
					} catch (Exception exe) {
						logtemp
						.error("Error contacting the webservice at "
								+ PropertiesFileLoader.getInstance().getPropertiesValue(SwtConstants.PROPERTY_INTERFACES_RPC_SERVICE_PCM));
			      
						throw SwtErrorHandler.getInstance().handleException(e, "executeJob", PCMInterfaceNotif.class);
					}
				}
				// set relational id when service connected successfully
				notification
						.setRelationId(SwtConstants.INTERFACE_NOTIFICATION_TYPE);
				// delete notification error details
				dao.deleteNotification(notification, true);

				// Call the manager layer class with sorted statistics.
				// Filter statistics method returns a hash map with just
				// interface
				// names and filemanager_lastcommit
				processStatistics = interfaceInterruptManager
						.processInterfaceStatistics(statisticsMap, true);
			}
			log.debug(this.getClass().getName() + " -[executeJob] - Exit ");
		} catch (SwtException swtException) {
			log.debug(this.getClass().getName()
					+ " - [executeJob] - SwtException -"
					+ swtException.getMessage());
			log.error(this.getClass().getName()
					+ " - [executeJob] - SwtException - "
					+ swtException.getMessage());
			return "F";
		} catch (Exception e) {
			log.debug(this.getClass().getName()
					+ " - [executeJob] - Exception - " + e.getMessage());
			log.error(this.getClass().getName()
					+ " - [executeJob] - Exception -" + e.getMessage());
			return "F";
		} finally {
			// Null all object
			soapResponse = null;
			rawRow = null;
			statisticsMap = null;
			interfaceInterruptManager = null;
		}
		return "S";
	}

	/**
	 * filterStatistics - This method is used to filter out a hash map
	 * containing interface names as keys and their filemanager_lastcommit as
	 * values, from a collection containing all statistics of all interfaces
	 * 
	 * @param Collection
	 *            <Map<String, Object>> listOfStatistics
	 * @return Map<Object, Object>
	 */
	@Deprecated
	private Map<Object, Object> filterStatistics(
			Collection<Map<String, Object>> listOfStatistics) {
		log.debug(this.getClass().getName() + "- [filterStatistics] - Enter");
		// Map to be returned which will have the interface names and its
		// filemanager_lastcommit values
		Map<Object, Object> notificationsMap = null;
		// Map which will hold each interface's statistics for processing at
		// given instance of time
		Map<String, Object> statisticsMap = null;
		// Iterator instance for iteration
		Iterator itr = null;

		try {
			// Instantiate the maps
			notificationsMap = new HashMap();
			statisticsMap = new HashMap();

			// Iterator for collection having all statistics
			itr = listOfStatistics.iterator();
			// Iterate through the collection
			while (itr.hasNext()) {
				// Get the map for each interface, which has all statistics
				statisticsMap = (Map<String, Object>) itr.next();
				// if the thread_functionalid is filemanager and
				// filemanager_implemented is true
				if ((statisticsMap.get("thread_functionalid")
						.equals("filemanager"))
						&& (statisticsMap.get("filemanager_implemented")
								.equals("true"))) {
					// Get the thread_interface(name of interface) and
					// filemanager_lastcommit as value
					notificationsMap.put(statisticsMap.get("thread_interface")
							.toString(), statisticsMap
							.get("filemanager_lastcommit"));
				}
			}
			log.debug(this.getClass().getName()
					+ "- [filterStatistics] - Exit ");
		} catch (Exception e) {
			log.debug(this.getClass().getName()
					+ " - [filterStatistics] - Exception -" + e.getMessage());
			log.error(this.getClass().getName()
					+ " - [filterStatistics] - Exception -" + e.getMessage());
		} finally {
			// NULL the objects
			statisticsMap = null;
			itr = null;
		}

		return notificationsMap;
	}
}