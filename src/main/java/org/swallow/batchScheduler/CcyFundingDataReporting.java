
package org.swallow.batchScheduler;

import java.io.File;
import java.io.FileOutputStream;
import java.io.InputStream;
import java.io.StringReader;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang.StringUtils;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.swallow.util.struts.ActionMessages ;
import org.swallow.util.struts.ActionMessage ;
import org.apache.xerces.parsers.DOMParser;
import org.swallow.control.model.Job;
import org.swallow.control.model.ScheduledReportParams;
import org.swallow.control.model.ScheduledReportType;
import org.swallow.control.model.Scheduler;
import org.swallow.control.model.SystemLog;
import org.swallow.control.service.SchedulerManager;
import org.swallow.control.service.SystemLogManager;
import org.swallow.exception.SwtException;
import org.swallow.maintenance.dao.hibernate.SysParamsDAOHibernate;
import org.swallow.maintenance.model.EntityUserAccess;
import org.swallow.maintenance.service.ILMGeneralMaintenanceManager;
import org.swallow.reports.model.SchedReportHist;
import org.swallow.reports.service.CurrencyFundingManager;
import org.swallow.reports.service.SchedReportHistManager;
import org.swallow.util.CacheManager;
import org.swallow.util.JDBCCloser;
import org.swallow.util.PropertiesFileLoader;
import org.swallow.util.SequenceFactory;
import org.swallow.util.SwtConstants;
import org.swallow.util.SwtRecipient;
import org.swallow.util.SwtScenario;
import org.swallow.util.SwtUtil;
import org.swallow.util.UserThreadLocalHolder;
import org.w3c.dom.Document;
import org.w3c.dom.Element;
import org.w3c.dom.Node;
import org.w3c.dom.NodeList;
import org.xml.sax.InputSource;

import net.sf.jasperreports.engine.JRExporterParameter;
import net.sf.jasperreports.engine.JasperPrint;
import net.sf.jasperreports.engine.export.JRPdfExporter;

public class CcyFundingDataReporting extends SwtBasicJob {

	private final static Log log = LogFactory.getLog(CcyFundingDataReporting.class);

	/**
	 * This method is used to execute the ILM Process from database.
	 */
	public String executeJob(Integer schedulerId) {

		String retValue = "F";
		String dbLink = null;
//		Reports report = null;
		// Declaring the jasperPrint
		JasperPrint jasperPrint = null;
		// Initializing the outputstream
		FileOutputStream out = null;
		// Initializing the PDF Exporter.
		JRPdfExporter exporter = null;
		String hostId = null;
		ILMGeneralMaintenanceManager ilmGeneralMaintenanceManager = null;
		String roleId = null;
		String documentName = null;
		InputStream fileIn = null;
		List<String> sheetNamesList = null;
		List<String> templateSheetNamesList = null;
		ArrayList<Map<String, Object>> beansList = null;
		ArrayList<Map<String, Object>> scenarioBeansList = null;
		ArrayList<Map<String, Object>> allBeansList = null;
		File file = null;
		Date startDate = new Date();
		Date finishDate = null;
		SchedulerManager schedulerManager = null;
		ArrayList<Job> jobList = null;
		ArrayList<ScheduledReportType> reportTypes = null;
		String reportTypeId = null;
		String userList = null;
		String roleList = null;
		SchedReportHistManager schedReportHistManager = null;
		CurrencyFundingManager currencyFundingManager  = null;
		String errorMessage = null;
		String jobName = null;
		String reportTypeName = null;
		Long fileId = null;
		Scheduler schedulerRec = null;
		Collection schedulerDetails = null;
		ScheduledReportParams params = null;
		String reportConfigAsString = null;
		Document reportConfigAsXML = null;
		HashMap<String, String> reportConfigAsHashMap = null;
		DOMParser parser = null;
		String entityId = null;
		String currencyId = null;
		int access = 0;
		boolean mapDateMethodAsEntityTimeFrame = false;
		String outputLocationProtieValue = null;
		String accessErrorLocation = null;
		SystemLogManager systemLogManager = null;
		SystemLog systemLog = null;
		try {

			log.debug("Start executeJob for Currency Funding Reporting");
			retValue = "S";

			
			systemLogManager = (SystemLogManager) SwtUtil.getBean("systemLogManager");
			
			try {
				log.debug(this.getClass().getName() + "- [CurrencyFundingReport] - Entering ");
				schedReportHistManager = (SchedReportHistManager) SwtUtil.getBean("schedReportHistManager");
				schedulerManager = (SchedulerManager) SwtUtil.getBean("schedulerManager");
				currencyFundingManager = (CurrencyFundingManager) SwtUtil.getBean("currencyFundingManager");
				// Initializing the Dynavalidator Form
				outputLocationProtieValue = PropertiesFileLoader.getInstance()
						.getPropertiesValue(SwtConstants.SCHEDULED_REPORT_LOCATION_PROPERTIE_NAME);

//				TimeUnit.SECONDS.sleep(2);
				/*
				 * Setting the original starting and ending date in the scheduler object
				 */
				schedulerDetails = schedulerManager.getSchedulerDetails(schedulerId, null);

				if (schedulerDetails != null) {
					Iterator itr = schedulerDetails.iterator();

					while (itr.hasNext()) {
						log.debug("Entering into the iterator loop");
						schedulerRec = (Scheduler) (itr.next());

						break;
					}
				}

				if (!SwtUtil.isEmptyOrNull(outputLocationProtieValue)) {
					if (schedulerRec != null && schedulerRec.getScheduledReportParams() != null) {
						params = schedulerRec.getScheduledReportParams();

						
						File outputLocationDirectory = new File(outputLocationProtieValue);
						if (outputLocationDirectory.canWrite()) {

							reportTypeId = params.getReportTypeId();

							// manipulate XML
							parser = new DOMParser();
							reportConfigAsString = params.getReportConfig();
							reportConfigAsString = SwtUtil.decode64(reportConfigAsString);
							// DOMParser traverse through the given input xml data
							if (!SwtUtil.isEmptyOrNull(reportConfigAsString)) {
								reportConfigAsHashMap = new HashMap<String, String>();
								parser.parse(new InputSource(new StringReader(reportConfigAsString)));
								// Assigns the parsed data to the Document class
								reportConfigAsXML = parser.getDocument();
								NodeList nodes = reportConfigAsXML.getDocumentElement().getChildNodes();

								for (int i = 0; i < nodes.getLength(); i++) {
									Node node = nodes.item(i);

									if (node instanceof Element) {
										// a child element to process
										Element child = (Element) node;
										reportConfigAsHashMap.put(child.getLocalName(), child.getTextContent());
									}
								}


								entityId =  reportConfigAsHashMap.get("entityid");
								if (params != null && "S".equals(params.getMapDateMethod())) {
									mapDateMethodAsEntityTimeFrame = false;
								} else {
									mapDateMethodAsEntityTimeFrame = true;
								}
								roleId = params.getExecutionRole();
								// Get the role ID from session

								Collection entityAccess = SwtUtil.getSwtMaintenanceCache()
										.getFullEntityAccessCollection(roleId);
								Iterator itr = entityAccess.iterator();
								while (itr.hasNext()) {
									EntityUserAccess entityUserAccess = (EntityUserAccess) itr.next();

									if (entityUserAccess.getEntityId().equals(entityId)) {
										access = entityUserAccess.getAccess();
									}
								}
								
								if(access == 2) {
									accessErrorLocation = "entityCurrency";
								}
								
								
								if (access == 0) {
									
									
									/* Method's local variable declaration. */
									String entityName =null;
									String selectedAcctId=null;
									String accountName=null;
									String currencyCode=null;
									String currencyName = null;
									String valueDate=null;
									String thresholdValue=null;
									String showDR = null;
									String showCR = null;
									JRPdfExporter pdfexporter;
									String sysDateformat = null;
									String dateFormat = null;
								    
									 String sqlQueryAsString = null; 
									try {

										/* Getting the hostId from the cacheManager */
										hostId = CacheManager.getInstance().getHostId();
										
										/*
										 * Getting the entityId ,entityName,reportDate,showMain,showSub from
										 * request
										 */
										entityId = reportConfigAsHashMap.get("entityid");
										entityName = reportConfigAsHashMap.get("entityname");
										currencyCode = reportConfigAsHashMap.get("currencycode");
										currencyName = reportConfigAsHashMap.get("currencyname");
										selectedAcctId = reportConfigAsHashMap.get("selectedacctid");
										valueDate = reportConfigAsHashMap.get("valuedate");
										thresholdValue = reportConfigAsHashMap.get("threshold");
										accountName = reportConfigAsHashMap.get("account");
										showDR = reportConfigAsHashMap.get("selectedshowdr");
										showCR = reportConfigAsHashMap.get("selectedshowcr");
										dateFormat = reportConfigAsHashMap.get("dateformatasstring");
										sysDateformat = SwtUtil.getCurrentDateFormat(null);
										   	
										
										if (SysParamsDAOHibernate.keywordQuery
												.containsKey(valueDate)) {
											valueDate = SwtUtil.convertKeywordToDate(null,
													valueDate, mapDateMethodAsEntityTimeFrame,
													entityId);
										}
										
										
//										out = response.getOutputStream();
										/*
										 * In UI, if the user selected showMain option, store the Main value
										 * as M, same like Sub is S
										 */
										if (showDR.equals("Y")) {
											showDR = "D";
										} else if (showDR.equals("N")) {
											showDR = "";
										}

										if (showCR.equals("Y")) {
											showCR = "C";
										} else if (showCR.equals("N")) {
											showCR = "";
										}
										
										
											sqlQueryAsString = SwtUtil.getNamedQuery("currency_funding_report_query");
											/* To get the filled report form reportsManager */
											/* To get the filled report form reportsManager */
											jasperPrint = currencyFundingManager.getCurrencyFundingReport(
													null, hostId, entityId, entityName, currencyCode,
													currencyName, selectedAcctId, accountName, valueDate, thresholdValue,
													showDR, showCR, dateFormat);
	
//											/* Initializing the JRDFExporter */
//											pdfexporter = new JRPdfExporter();
//											/* To set the output type as PDF file */
//											response.setContentType("application/pdf");
//											/* To set the content as attachment */
//											response.setHeader("Content-disposition", "attachment; filename="
//													+ jasperPrint.getName() + "-SmartPredict_"
//													+ SwtUtil.formatDate(new Date(), "yyyyMMdd") + "_"
//													+ SwtUtil.formatDate(new Date(), "HHmmss") + ".pdf");
//											/* To pass the filled report */
//											pdfexporter.setParameter(JRExporterParameter.JASPER_PRINT,
//													jasperPrint);
//											/* Providing the output stream */
//											pdfexporter.setParameter(JRExporterParameter.OUTPUT_STREAM, out);
//											/* Exporting as UTF-8 */
//											pdfexporter.setParameter(JRExporterParameter.CHARACTER_ENCODING,
//													"UTF-8");
//											/* Export Report */
//											pdfexporter.exportReport();
											
											
											
											
											
											// Instance for JRPDFExporter.
											exporter = new JRPdfExporter();

											documentName = new String();
											if (params != null && params.getFileNamePrefix() != null) {
												documentName = params.getFileNamePrefix() + "_";
											}
											boolean diretoryCreated = true;
											if (params.getOutputLocation() != null) {
												File directory = new File(
														outputLocationProtieValue + "/" + params.getOutputLocation());
												if (!directory.exists()) {
													diretoryCreated = directory.mkdirs();
													// If you require it to make the entire directory path including
													// parents,
													// use directory.mkdirs(); here instead.
												}
											} else {
												File directory = new File(outputLocationProtieValue + "/");
												if (!directory.exists()) {
													diretoryCreated = directory.mkdirs();
													
													
													// If you require it to make the entire directory path including
													// parents,
													// use directory.mkdirs(); here instead.
												}
											}
											
											if(diretoryCreated) {
												// To set the content as attachment
												file = new File(outputLocationProtieValue + "/"
														+ (params.getOutputLocation() != null ? params.getOutputLocation()
																: "")
														+ "/" + documentName + SwtUtil.formatDate(new Date(), "yyyyMMdd")
														+ "_" + SwtUtil.formatDate(new Date(), "HHmmss") + ".pdf");
												file.createNewFile(); // if file already exists will do nothing
												out = new FileOutputStream(file, false);
												// To pass the filled report
												exporter.setParameter(JRExporterParameter.JASPER_PRINT, jasperPrint);
												// Providing the output stream
												exporter.setParameter(JRExporterParameter.OUTPUT_STREAM, out);
												// Exporting as UTF-8
												exporter.setParameter(JRExporterParameter.CHARACTER_ENCODING, "UTF-8");
												// Export Report
												exporter.exportReport();
												out.close();
											}else {
												errorMessage = "Error occured during exporting report : Scheduler Output Location is an invalid or is not writable.";
												SwtUtil.logErrorInDatabase(new SwtException("ILMDataReporting.executeJob:"+schedulerRec.getScheduledReportParams().getReportName(), "Y","INVALID_PATH",
														"Error occured during exporting report : Scheduler Output Location is an invalid or is not writable.",""));
												log.error(this.getClass().getName()
														+ "- [executeJob] - Error occured during exporting report : Scheduler Output Location is an invalid or is not writable");
											}
											
										} catch (SwtException swtexp) {
											log.debug(this.getClass().getName()
													+ " - Exception Catched in [report] method - "
													+ swtexp.getMessage());

											log.error(this.getClass().getName()
													+ " - Exception Catched in [report] method - "
													+ swtexp.getMessage());
										} catch (Exception exp) {
											log.error(this.getClass().getName()
													+ " - Exception Catched in [report] method - "
													+  exp.getMessage());
										}
									

//											documentName = new String();
//											if (params != null && params.getFileNamePrefix() != null) {
//												documentName = params.getFileNamePrefix() + "_";
//											}
//											
//											boolean diretoryCreated = true;
//											
//											if (params.getOutputLocation() != null) {
//												File directory = new File(
//														outputLocationProtieValue + "/" + params.getOutputLocation());
//												if (!directory.exists()) {
//													diretoryCreated = directory.mkdirs();
//													// If you require it to make the entire directory path including
//													// parents,
//													// use directory.mkdirs(); here instead.
//												}
//											} else {
//												File directory = new File(outputLocationProtieValue);
//												if (!directory.exists()) {
//													diretoryCreated = directory.mkdirs();
//													// If you require it to make the entire directory path including
//													// parents,
//													// use directory.mkdirs(); here instead.
//												}
//											}
//
//											if(diretoryCreated) {
//												// To set the content as attachment
//												file = new File(outputLocationProtieValue + "/"
//														+ (params.getOutputLocation() != null ? params.getOutputLocation()
//																: "")
//														+ "/" + documentName + SwtUtil.formatDate(new Date(), "yyyyMMdd")
//														+ "_" + SwtUtil.formatDate(new Date(), "HHmmss") + ".xlsx");
//												file.createNewFile(); // if file already exists will do nothing
//												out = new FileOutputStream(file, false);
//												workbook.write(out);
//												out.close();

												
												
												
												
											// Instance for JRPDFExporter.
											exporter = new JRPdfExporter();

											documentName = new String();
											if (params != null && params.getFileNamePrefix() != null) {
												documentName = params.getFileNamePrefix() + "_";
											}
											boolean diretoryCreated = true;
											if (params.getOutputLocation() != null) {
												File directory = new File(
														outputLocationProtieValue + "/" + params.getOutputLocation());
												if (!directory.exists()) {
													diretoryCreated = directory.mkdirs();
													// If you require it to make the entire directory path including
													// parents,
													// use directory.mkdirs(); here instead.
												}
											} else {
												File directory = new File(outputLocationProtieValue + "/");
												if (!directory.exists()) {
													diretoryCreated = directory.mkdirs();
													
													
													// If you require it to make the entire directory path including
													// parents,
													// use directory.mkdirs(); here instead.
												}
											}
											
											if(diretoryCreated) {
												// To set the content as attachment
												file = new File(outputLocationProtieValue + "/"
														+ (params.getOutputLocation() != null ? params.getOutputLocation()
																: "")
														+ "/" + documentName + SwtUtil.formatDate(new Date(), "yyyyMMdd")
														+ "_" + SwtUtil.formatDate(new Date(), "HHmmss") + ".pdf");
												file.createNewFile(); // if file already exists will do nothing
												out = new FileOutputStream(file, false);
												// To pass the filled report
												exporter.setParameter(JRExporterParameter.JASPER_PRINT, jasperPrint);
												// Providing the output stream
												exporter.setParameter(JRExporterParameter.OUTPUT_STREAM, out);
												// Exporting as UTF-8
												exporter.setParameter(JRExporterParameter.CHARACTER_ENCODING, "UTF-8");
												// Export Report
												exporter.exportReport();
												out.close();
											}else {
												errorMessage = "Error occured during exporting report : Scheduler Output Location is an invalid or is not writable.";
												SwtUtil.logErrorInDatabase(new SwtException("ILMDataReporting.executeJob:"+schedulerRec.getScheduledReportParams().getReportName(), "Y","INVALID_PATH",
														"Error occured during exporting report : Scheduler Output Location is an invalid or is not writable.",""));
												log.error(this.getClass().getName()
														+ "- [executeJob] - Error occured during exporting report : Scheduler Output Location is an invalid or is not writable");
											}
//										}

									}
								} else {
									String cause = null;
									if(!SwtUtil.isEmptyOrNull(accessErrorLocation)) {
										if(("entity").equals(accessErrorLocation)) 
											cause ="The selected role does not have access in combination of the selected entity";
										
									}
									errorMessage = "Error occured during exporting report : Role used has not enough access to generate this report:"+cause;
									SwtUtil.logErrorInDatabase(new SwtException("ILMDataReporting.executeJob:"+schedulerRec.getScheduledReportParams().getReportName(), "Y","ROLE_NO_ACCESS",
											"Error occured during exporting report : Role used has not enough access to generate this report:"+cause,""));
									log.error(this.getClass().getName()
											+ "- [executeJob] - Error occured during exporting report : Role used has not enough access to generate this report:"+cause);
									
								}
						} else {
							errorMessage = "Error occured during exporting report : ScheduledReportLocation is an invalid path or is not writable.";
							SwtUtil.logErrorInDatabase(new SwtException("ILMDataReporting.executeJob:"+schedulerRec.getScheduledReportParams().getReportName(), "Y","INVALID_PATH",
									"Error occured during exporting report : ScheduledReportLocation is an invalid path or is not writable.",""));
							log.error(this.getClass().getName()
									+ "- [executeJob] - Error occured during exporting report : ScheduledReportLocation is an invalid path or is not writable");
						}
					} else {
						errorMessage = "Error occured during exporting report : ILM Report config is missing or invalide, more details can be found in error logs.";
						SwtUtil.logErrorInDatabase(new SwtException("ILMDataReporting.executeJob:"+schedulerRec.getScheduleId(), "Y","INVALID_PATH",
								"Error occured during exporting report : ILM Report config is missing or invalide, more details can be found in error logs.",""));
						log.error(this.getClass().getName()
								+ "- [executeJob] - Exception Error occured during exporting report : ILM Report config is missing or invalide, more details can be found in error logs.");
					}
				} else {
					SwtUtil.logErrorInDatabase(new SwtException("ILMDataReporting.executeJob:"+schedulerRec.getScheduleId(), "Y","LOCATION_ERR",
							"Error occured during exporting report : ScheduledReportLocation must be configured in predict.properties to enable scheduled reporting.",""));
					log.error(this.getClass().getName()
							+ "- [executeJob] - Error occured during exporting report : ScheduledReportLocation must be configured in predict.properties to enable scheduled reporting.");
				}

			} catch (OutOfMemoryError exp) {
				errorMessage = "Error occured during exporting report, " + exp.getMessage()
						+ ", more details can be found in error logs.";
				ActionMessages errors = new ActionMessages();
				errors.add("", new ActionMessage(exp.getMessage()));
				log.debug(this.getClass().getName() + "- [CurrencyFundingReport] - Exception " + exp.getMessage());
				SwtUtil.logErrorInDatabase(new SwtException("ILMDataReporting.executeJob", "Y","OutOfMemoryError","OutOfMemoryError Exception during export",""));
			} catch (SwtException exp) {
				errorMessage = "Error occured during exporting report : " + exp.getMessage()
						+ ", more details can be found in error logs.";
				ActionMessages errors = null;
				errors = new ActionMessages();
				errors.add("", new ActionMessage(exp.getMessage()));
				log.error(this.getClass().getName() + "- [executeJob] - Exception " + exp.getMessage());
				exp.setUserId("SYSTEM");
				exp.setErrorLogFlag("Y");
				exp.setErrorDesc(errorMessage);
				exp.setSrcCodeLocation("ILMDataReporting.executeJob");
				
				SwtUtil.logErrorInDatabase(exp);
			} finally {
				jasperPrint = null;
				out = null;
				exporter = null;
				hostId = null;
				ilmGeneralMaintenanceManager = null;
				dbLink = null;
				roleId = null;
				documentName = null;
			}
			log.debug(this.getClass().getName() + "- [CurrencyFundingReport] - Exiting ");

			finishDate = new Date();

			jobList = schedulerManager.getReportsJobList(SwtUtil.getCurrentHostId());
			reportTypes = schedulerManager.getReportTypes(SwtUtil.getCurrentHostId(), "all");

			for (int i = 0; i < jobList.size(); i++) {
				if (jobList.get(i).getId().getJobId().equals(schedulerRec.getJobId())) {
					jobName = jobList.get(i).getJobDescription();
				}
			}

			for (int i = 0; i < reportTypes.size(); i++) {
				if (reportTypes.get(i).getReportTypeId().equals(reportTypeId)) {
					reportTypeName = reportTypes.get(i).getReportName();
				}
			}

			Long timeTaken = (finishDate.getTime() - startDate.getTime()) / 1000;
			fileId = SequenceFactory.getSequenceFromDbAsLong("SEQ_S_SCHEDULED_REPORT_HIST");
			SchedReportHist schedReportHist = new SchedReportHist();
			schedReportHist.getId().setFileId(fileId);
			hostId = SwtUtil.getCurrentHostId();
			schedReportHist.setHostId(hostId);
			schedReportHist.setJobId(params.getJobId());
			schedReportHist.setJobName(jobName);
			schedReportHist.setReportTypeId(reportTypeId);
			schedReportHist.setReportTypeName(reportTypeName);
			schedReportHist.setReportName(schedulerRec.getScheduledReportParams().getReportName());
			schedReportHist.setReportDescription(schedulerRec.getScheduledReportParams().getReportDesc());
			try {
				schedReportHist.setRunDate(SwtUtil.getSystemDateFromDB());
			} catch (SwtException e1) {
				schedReportHist.setRunDate(new Date());
			}
			schedReportHist.setScheduleId(schedulerRec.getScheduleId());
			schedReportHist.setScheduleName(params.getReportName());
			schedReportHist.setElapsedTime(timeTaken);

			if (file != null && !SwtUtil.isEmptyOrNull(file.getAbsolutePath())) {

//				String absolutePath = file.getAbsolutePath();
//				String filePath = absolutePath.substring(0, absolutePath.lastIndexOf(File.separator));

				String fileName = file.getName();
				double sizeInBytes = file.length();
				Double fileSize = (sizeInBytes / 1024);

				schedReportHist.setFileName(fileName);
				schedReportHist.setFileSize("" + Math.round(fileSize));
				schedReportHist.setOutputLocation(params.getOutputLocation());

				schedReportHist.setExportStatus("S");
				schedReportHist.setExportError("");

				userList = params.getEmailDistListUsers();
				roleList = params.getEmailDistListRoles();
				ArrayList<String> mailList = new ArrayList<String>();

				if (SwtUtil.isEmptyOrNull(userList) && SwtUtil.isEmptyOrNull(roleList)) {
					schedReportHist.setMailStatus("");
				}else {
					boolean resultStatus = sendReporAsEmail(userList, roleList, file, mailList, schedReportHist, "SYSTEM");
					String mailListAsString = "";
					if (mailList.size() > 0) {
						for (String s : mailList) {
							mailListAsString += s + ",";
						}
					}
					if (!SwtUtil.isEmptyOrNull(mailListAsString)) {
						mailListAsString = mailListAsString.substring(0, mailListAsString.length() - 1);
					}
					if (resultStatus) {
						schedReportHist.setMailStatus("S");
						schedReportHist.setMailRsult(mailListAsString);
	
					} else {
						schedReportHist.setMailStatus("F");
						schedReportHist
								.setMailRsult("Error occured when sending mail, more details can be found in error logs.");
	
					}
				}

				schedReportHistManager.saveSchedReportHist(schedReportHist);

			} else {
				if (SwtUtil.isEmptyOrNull(errorMessage)) {
					errorMessage = "Error occured when creating the report, more details can be found in error logs";
				}
				schedReportHist.setExportStatus("F");
				schedReportHist.setExportError(errorMessage);
				schedReportHistManager.saveSchedReportHist(schedReportHist);
				retValue = "F";
			}

		} catch (Exception e) {
			retValue = "F";
			try {
				fileId = SequenceFactory.getSequenceFromDbAsLong("SEQ_S_SCHEDULED_REPORT_HIST");

				schedulerManager = (SchedulerManager) SwtUtil.getBean("schedulerManager");
				jobList = schedulerManager.getReportsJobList(SwtUtil.getCurrentHostId());
				reportTypes = schedulerManager.getReportTypes(SwtUtil.getCurrentHostId(), "all");

				for (int i = 0; i < jobList.size(); i++) {
					if (jobList.get(i).getId().getJobId().equals(schedulerRec.getJobId())) {
						jobName = jobList.get(i).getJobDescription();
					}
				}

				for (int i = 0; i < reportTypes.size(); i++) {
					if (reportTypes.get(i).getReportTypeId().equals(reportTypeId)) {
						reportTypeName = reportTypes.get(i).getReportName();
					}
				}
			} catch (SwtException e1) {
				log.error(this.getClass().getName() + "- [executeJob] - Exception " + e1.getMessage());
				e1.setUserId("SYSTEM");
				e1.setErrorLogFlag("Y");
				e1.setErrorDesc(e1.getMessage());
				e1.setSrcCodeLocation("ILMDataReporting.executeJob");
				
				SwtUtil.logErrorInDatabase(e1);
			}

			SchedReportHist schedReportHist = new SchedReportHist();
			schedReportHist.getId().setFileId(fileId);
			hostId = SwtUtil.getCurrentHostId();
			schedReportHist.setHostId(hostId);
			schedReportHist.setJobId(schedulerRec != null ? schedulerRec.getJobId() : null);
			schedReportHist.setJobName(jobName);
			schedReportHist.setReportTypeId(reportTypeId);
			schedReportHist.setReportName(schedulerRec.getScheduledReportParams().getReportName());
			try {
				schedReportHist.setRunDate(SwtUtil.getSystemDateFromDB());
			} catch (SwtException e1) {
				schedReportHist.setRunDate(new Date());
			}
			schedReportHist.setScheduleId(schedulerRec != null ? schedulerRec.getScheduleId() : null);
			schedReportHist.setScheduleName(params != null ? params.getReportName() : null);
			schedReportHist.setExportStatus("F");
			schedReportHist.setExportError(errorMessage);
			try {
				schedReportHistManager.saveSchedReportHist(schedReportHist);
			} catch (SwtException ex) {
				log.error(this.getClass().getName() + "- [executeJob] - Exception " + ex.getMessage());
				ex.setUserId("SYSTEM");
				ex.setErrorLogFlag("Y");
				ex.setErrorDesc(ex.getMessage());
				ex.setSrcCodeLocation("ILMDataReporting.executeJob");
				SwtUtil.logErrorInDatabase(ex);

			}
			

		} finally {
			systemLog = new SystemLog();
			systemLog.setHostId(SwtUtil.getCurrentHostId());
			systemLog.setUserId("SYSTEM");
			systemLog.setIpAddress("");
			systemLog.setProcess("Report: "+jobName+":"+schedulerRec.getScheduledReportParams().getReportName());
			systemLog.setAction("Report");
			systemLog.setLogDate(SwtUtil.getSystemDatewithTime());
			systemLog.setUpdateDate(SwtUtil.getSystemDatewithTime());
			systemLog.setUpdateUser("SYSTEM");
			try {
				systemLogManager.saveSystemLog(systemLog);
			} catch (SwtException e1) {
			}
		}
		log.debug("End executeJob for Currency Funding Data Reporting");
		return retValue;
	}

	public static boolean sendReporAsEmail(String userList, String roleList, File reportFile,
			ArrayList<String> mailList, SchedReportHist schedReportHist , String userId) throws SwtException {
		boolean allMailsSent = true;
		Connection conn = null;
		ResultSet res = null;
		PreparedStatement stmt = null;
		// Used to retrieve user scenarios
		HashMap<String, ArrayList<SwtScenario>> userEmails = new HashMap<String, ArrayList<SwtScenario>>();
		// Used to retrieve user properties like (user name, email address , user
		// language)
		HashMap<String, String> userProperties = new HashMap<String, String>();
		ArrayList<SwtScenario> scenarios = null;
		HashMap<String, Object> scenarioRootMap = null;
		String emailListQuery = null;
		SystemLogManager systemLogManager = null;
		SystemLog systemLog = null;
		if (!SwtUtil.isEmptyOrNull(userList) || !SwtUtil.isEmptyOrNull(roleList)) {
			try {
				log.info("SendingEmailsJob  - [sendReporAsEmail] - Entering");
				userList = convertListToInParameterForQuery(userList);
				roleList = convertListToInParameterForQuery(roleList);
				if (SwtUtil.isEmptyOrNull(userList)) {
					emailListQuery = "select distinct( USER_ID), USER_NAME ,ROLE_ID, LANG , email_address   from s_users where (ROLE_ID in ("
							+ roleList + ")) ";
				} else {
					if (SwtUtil.isEmptyOrNull(roleList)) {
						emailListQuery = "select distinct( USER_ID), USER_NAME ,ROLE_ID, LANG , email_address   from s_users where (user_id in ("
								+ userList + "))";
					} else {
						emailListQuery = "select distinct( USER_ID), USER_NAME ,ROLE_ID, LANG , email_address   from s_users where (user_id in ("
								+ userList + ") OR   ROLE_ID in (" + roleList + ")) ";
					}
				}

				emailListQuery += " AND  email_address is not null AND email_address IS NOT NULL  AND (   (STATUS = 1)  OR (STATUS = 2 AND NVL (INV_PASS_ATTEMPT, 0) > 0))";

				conn = ConnectionManager.getInstance().databaseCon();
				stmt = conn.prepareStatement(emailListQuery);
				res = stmt.executeQuery();

				while (res.next()) {
					// get User information
					String userKey = res.getString("USER_ID");
					String userProp = res.getString("USER_NAME") + "/" + res.getString("EMAIL_ADDRESS") + "/"
							+ res.getString("ROLE_ID") + "/" + res.getString("LANG");

					if (userEmails.containsKey(userKey)) {
						ArrayList<SwtScenario> userScenarios = userEmails.get(userKey);
						userScenarios.addAll(scenarios);
					} else {
						userEmails.put(userKey, scenarios);
						userProperties.put(userKey, userProp);
					}

				}

				if (mailList == null) {
					mailList = new ArrayList<String>();
				}
				for (String key : userEmails.keySet()) {

					String recipientInfo[] = userProperties.get(key).split("/");
					String recipientName = recipientInfo[0];
					String recipientEmail = recipientInfo[1];
					String recipientRole = recipientInfo[2];
					String recipientLang = recipientInfo[3];
					SwtRecipient recipient = new SwtRecipient(recipientName, recipientEmail, recipientRole,
							recipientLang);
					scenarioRootMap = new HashMap<String, Object>();
					scenarioRootMap.put("scenarios", userEmails.get(key));
					scenarioRootMap.put("report_name", !SwtUtil.isEmptyOrNull(schedReportHist.getReportName())?schedReportHist.getReportName():"");
					scenarioRootMap.put("job_name", !SwtUtil.isEmptyOrNull(schedReportHist.getJobName())?schedReportHist.getJobName():"");
					scenarioRootMap.put("report_description", !SwtUtil.isEmptyOrNull(schedReportHist.getReportDescription())?schedReportHist.getReportDescription():"");
					
					
					// Send an Email that includes a summary of scenarios raised by 'Alerting
					// Framework'
					mailList.add(recipientEmail);
					if (!SwtUtil.sendReportAsEmail(recipient, scenarioRootMap, reportFile)) {
						allMailsSent = false;
					}else {
						if (!SwtUtil.isEmptyOrNull(userId) && !"SYSTEM".equals(userId)) {
							systemLogManager = (SystemLogManager) SwtUtil.getBean("systemLogManager");
							systemLog = new SystemLog();
							systemLog.setHostId(SwtUtil.getCurrentHostId());
							systemLog.setUserId(userId);
							systemLog.setIpAddress(UserThreadLocalHolder.getUserIPAddress());
							systemLog.setProcess("Resend report email to User:"+key+" File ID: "+schedReportHist.getId().getFileId()+" ScheduleID: "+schedReportHist.getScheduleId()+" FileName: "+schedReportHist.getFileName());
							systemLog.setAction("Send Mail");
							systemLog.setLogDate(SwtUtil.getSystemDatewithTime());
							systemLog.setUpdateDate(SwtUtil.getSystemDatewithTime());
							systemLog.setUpdateUser(userId);
							systemLogManager.saveSystemLog(systemLog);
							
						}
					}
					
				}
				
				
				
				log.info("SendingEmailsJob  - [checkAlertingScenarios] - Exit");
				return allMailsSent;
			} catch (Exception e) {
				allMailsSent = false;
				// log error message
				log.error("SendingEmailsJob - [checkAlertingScenarios] - Exception: " + e.getMessage());

				throw new SwtException("Exception occured while checking Alerting Scenarios : " + e.getMessage());

			} finally {
				// close statement and cnx.
				Object[] exceptions = JDBCCloser.close(res, stmt, conn, null);
				// nullify objects
				userEmails = null;
				scenarioRootMap = null;
				scenarios = null;

			}
		}
		return allMailsSent;

	}

	private static String convertListToInParameterForQuery(String listAsString) {
		String result = "";
		if (!SwtUtil.isEmptyOrNull(listAsString)) {
			if (listAsString.substring(listAsString.length() - 1).equals(",")) {
				listAsString = listAsString.substring(0, (listAsString.length() - 1));
			}
			String[] listArray = listAsString.split(",(\\s)?");
			result = StringUtils.join( listArray, "','");
			result = "'" + result + "'";

		}

		return result;
	}
}
