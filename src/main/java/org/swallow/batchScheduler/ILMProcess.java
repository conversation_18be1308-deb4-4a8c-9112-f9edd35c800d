package org.swallow.batchScheduler;

import java.sql.CallableStatement;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.sql.Types;
import java.util.HashMap;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.jdbc.support.JdbcUtils;
import org.swallow.control.service.ArchiveManager;
import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.util.JDBCCloser;
import org.swallow.util.SequenceFactory;
import org.swallow.util.SwtConstants;
import org.swallow.util.SwtUtil;
import org.swallow.work.service.ILMAnalysisMonitorManager;

public class ILMProcess extends SwtBasicJob {

	
	private final Log log = LogFactory.getLog(ILMProcess.class);
	public static HashMap<String, CallableStatement> cancelableILMProcess;
	public static final  String ILM_JOB_ID = "20";
	public static String processId;
	
	public void setProcessId() {
		try {
			ILMProcess.processId = SequenceFactory.getSequenceFromDb(SwtConstants.ILM_PROCESS_DRIVER_SEQUENCE_NAME);
		} catch (SwtException e) {
			SwtUtil.logErrorInDatabase(SwtErrorHandler.getInstance().handleException(e, "setProcessId", ILMProcess.class));
			log.error(this.getClass().getName()	+ " - Exception Catched in [setProcessId] method : - "+  e.getMessage());
		}
	}
	
	static {
			ILMProcess ilmProcess = new ILMProcess();
			ilmProcess.setProcessId();
    }
	/**
	 * This method is used to execute the ILM Process from database.
	 */
	public String executeJob(Integer schedulerId){
		
		String retValue="F";
		Connection conn=null;
		CallableStatement pstmt=null;
		String dbLink = null;
		Statement st = null;
		ResultSet rs = null;
		try{
			log.debug("Start executeJob for ILM process");
			ArchiveManager archiveManager = (ArchiveManager) SwtUtil.getBean("archiveManager");
			dbLink = archiveManager.getActiveDBLink(SwtUtil.getCurrentHostId(), "Predict");
			// Check condition if connection is null, get the job connection
			if (conn == null)
				conn = getConnection();
			
			log.debug(this.getClass().getName() + "-  [executeJob - Proc-Start-Time] - "+SwtUtil.getTimeWithMilliseconds());
			//Check if ilm connection details is not null otherwise set a new HashMap
			if(cancelableILMProcess == null) {
				cancelableILMProcess = new HashMap<String, CallableStatement>();
			}
			//Save the callable statement, this will help to cancel it when disabling the ILM recalculating process and this job is running
			pstmt = conn.prepareCall("{call PKG_ILM.ILM_MAIN_PROCESSING(?,?,?) }");
			cancelableILMProcess.put(processId, pstmt);
			// Setting dbLink value
			pstmt.setString(1, dbLink);
			pstmt.setString(2, processId);
			// Receiving the output from Procedure 
			pstmt.registerOutParameter(3, Types.VARCHAR);
			pstmt.executeUpdate();
			log.debug(this.getClass().getName() + "-  [executeJob - Proc-End-Time] - "+SwtUtil.getTimeWithMilliseconds());
			retValue=pstmt.getString(3);
			/* If output is 'Y' the status of the job is 'S'means Successful
			 * otherwise it is 'F' means Failed */			
			if(retValue.equalsIgnoreCase("Y")){
				retValue="S";
			}else{
				retValue="F";
			}	
			
			log.debug("result of execution of PKG_ILM.ILM_MAIN_PROCESSING "+retValue);
		} catch (Exception e) {
			// Rollback if error occures
			try {
				if (conn != null) {
					conn.rollback();
				}
			} catch (SQLException e1) {
			}
			
			// Do not log error when user requested cancelling the current calculation
			if( (cancelableILMProcess.get(processId) == null )){
				retValue = "Cancel";
			}else{
				log.error("ILMProcess: Error in executeJob - " + e.getMessage());
				SwtErrorHandler.getInstance().handleException(e, "executeJob", ILMProcess.class);
			}
		} finally {

			ILMAnalysisMonitorManager ilmAnalysisMonitorManager = (ILMAnalysisMonitorManager) (SwtUtil
					.getBean("ilmAnalysisMonitorManager"));
			try {
				ilmAnalysisMonitorManager.cleanUpProcessDriver(processId,SwtConstants.USER_SYSTEM);
			} catch (SwtException e) {
			}
			JDBCCloser.close(rs);
			JDBCCloser.close(st, pstmt);
			
			if(cancelableILMProcess != null && cancelableILMProcess.get(processId) != null) {
				cancelableILMProcess.remove(processId);
			}
		}
		log.debug("End executeJob for ILM process");
		return retValue;
	}
	/**
	 * This method will cancel all running ILM jobs ran from the scheduler screen
	 * using the saved callable Statement
	 */
	public static synchronized void cancelILMJobs() {
		if(ILMProcess.cancelableILMProcess != null) {
			for (String key : ILMProcess.cancelableILMProcess.keySet()) {
				CallableStatement cstmt = ILMProcess.cancelableILMProcess.get(key);
				try {
					ILMProcess.cancelableILMProcess.remove(key);
					if(cstmt != null && !cstmt.isClosed())
						cstmt.cancel();
					JdbcUtils.closeStatement(cstmt);
				} catch (SQLException e) {
				}
			}
		}
	}
}
