/*
 * @(#)StartAutoSweeping.java 
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */

package org.swallow.batchScheduler;

import java.sql.CallableStatement;
import java.sql.Types;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.swallow.exception.SwtErrorHandler;
import org.swallow.util.CacheManager;
import org.swallow.util.JDBCCloser;
import org.swallow.util.SwtConstants;
import org.swallow.util.SwtUtil;

/**
 * StartAutoSweeping.java
 * 
 * This class is used to Start Auto sweeping.
 * 
 */
public class StartAutoSweeping extends SwtBasicJob {
	/*
	 * Logger object for logging details to server.log file
	 */
	private final Log log = LogFactory.getLog(StartAutoSweeping.class);

	/**
	 * This method is used to call Data extraction Process from scheduler screen
	 * based upon the time set in scheduler job has to be run and auto sweep
	 * processs has to be done
	 * 
	 * @return String
	 */
	public String executeJob(Integer schedulerId) {
		// Variable decleration for retValue
		String retValue = null;
		// Variable decleration for Callable Statement
		CallableStatement cstmtJob = null;
		// Variable decleration for result
		int result;
		try {
			log.debug(this.getClass().getName() + " - [executeJob] - Entry");
			/* Start : Code modified by sunil for Mantis 1438 - removing the
			  Directory path are never used
			 */
			// Prepared statement for the cursor
			cstmtJob = getConnection().prepareCall(
					"{call  SWEEPING_PROCESS.Start_Auto_Sweeping(?,?)}");
			// Setting hostId value
			cstmtJob.setString(1, CacheManager.getInstance().getHostId());
			// Getting output from the cursor
			cstmtJob.registerOutParameter(2, Types.VARCHAR);
			// Excecute cursor
			cstmtJob.executeUpdate();
			// Getting the output value in cursor
			retValue = cstmtJob.getString(2);
			/* End : Code modified by sunil for Mantis 1438 - removing the
			  Directory path are never used
			 */
			// Type casting the string
			result = Integer.parseInt(retValue);
			if (result == 0)
				retValue = SwtConstants.SWEEP_STATUS_STP;
			else
				retValue = SwtConstants.FORMAT_TYPE_FIXED;

		} catch (Exception e) {
			SwtUtil.logErrorInDatabase(SwtErrorHandler.getInstance()
					.handleException(e, "executeJob", StartAutoSweeping.class));
			log.error(this.getClass().getName()
					+ " - Exception Catched in [executeJob] method : - "
					+ e.getMessage());
			retValue = SwtConstants.FORMAT_TYPE_FIXED;
		} finally {
			//edited by KaisBS for mantis 1745 : introducing the implementation of the JDBCCloser class
			JDBCCloser.close(cstmtJob);

			log.debug(this.getClass().getName() + " - [executeJob] - Exit");
		}
		return retValue;

	}

}
