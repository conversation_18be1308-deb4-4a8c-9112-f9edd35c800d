package org.swallow.batchScheduler;

import java.io.File;
import java.io.StringWriter;
import java.io.Writer;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.swallow.exception.SwtException;
import org.swallow.util.JDBCCloser;
import org.swallow.util.PropertiesFileLoader;
import org.swallow.util.SwtConstants;
import org.swallow.util.SwtNotification;
import org.swallow.util.SwtRecipient;
import org.swallow.util.SwtScenario;
import org.swallow.util.SwtUtil;

import freemarker.template.Configuration;
import freemarker.template.Template;

/**
 * <AUTHOR> ,SwallowTech Tunisia
 * 
 *  This class is used for sending 'background' support email 
 *   when an alert (via the Alerting framework) is raised,
 *   or when an interface interruption occurs.
 *
 */
public class SendingEmailsJob  extends SwtBasicJob {
	
	private static final Log log = LogFactory.getLog(SendingEmailsJob.class);
	public static final  String ILM_JOB_ID = "21";
	public static  boolean EMAIL_DISABLED_LOGGED = false;
	public static String lastStatus="S";
	@Override
	public String executeJob(Integer schedulerId) throws SQLException {
		String result = "F";
		try {
			if(SwtUtil.isMailEnabled()){
				
				// Check Alerting Scenarios 
				checkAlertingScenarios();
				// Check interface interruptions 
				checkInterfaceInterruption();
				result = "S";
				lastStatus = result;
			} else {
				if (!EMAIL_DISABLED_LOGGED) {
					log.error("SendingEmailsJob - [executeJob] - Email Sending process is enabled whereas property mail.enabled is set to FALSE, " +
							"This log is writen once.");
					EMAIL_DISABLED_LOGGED = true;
				}
				result = "S";
			}
		} catch (Exception e) {
			// log error message
			log.error("SendingEmailsJob - [executeJob] - Could not sending mail caused by this exception: "
					+ e.getMessage());
			lastStatus = result;
		}
		return result;
	}

	/**
	 *  Check if 'Alerting Framework' raises an alert scenario , 
	 *  in this case a 'background' mails will be send to required users
	 * @throws SwtException
	 */
	public void checkAlertingScenarios() throws SwtException {
		
		Connection conn = null;
		ResultSet res = null;
		PreparedStatement stmt = null;
		//Used to retrieve user scenarios
		HashMap<String, ArrayList<SwtScenario>> userEmails = new HashMap<String, ArrayList<SwtScenario>>();
		//Used to retrieve user properties like (user name, email address , user language)
		HashMap<String, String> userProperties = new HashMap<String, String>();
		ArrayList<SwtScenario> scenarios = null;
		HashMap<String, Object> scenarioRootMap = null;
		
		try {
			log.info("SendingEmailsJob  - [checkAlertingScenarios] - Entering");
			conn = ConnectionManager.getInstance().databaseCon();
			String query = SwtUtil.getNamedQuery("emailScenarioQuery");
			stmt = conn.prepareStatement(query);
			res = stmt.executeQuery();
		
			while (res.next()) {
				// Set scenario informations
				SwtScenario scenario = new SwtScenario();
				scenario.setScenarioId(res.getString("SCENARIO_ID"));
				scenario.setEntityId(res.getString("ENTITY_ID"));
				scenario.setHostId(res.getString("HOST_ID"));
				scenario.setCurrencyCode(res.getString("CURRENCY_CODE"));
				scenario.setRoleId(res.getString("ROLE_ID"));
				scenario.setEmailedScenarioCountOverT(res.getString("EMAILED_SC_COUNT_OVER_T"));
				scenario.setEmailedScenarioCount(res.getString("EMAILED_SC_COUNT"));
				scenario.setScenarioCountOverT(res.getString("SCENARIO_COUNT_OVER_T"));
				scenario.setScenarioCount(res.getString("SCENARIO_COUNT"));
				scenario.setScenarioTitle(res.getString("TITLE"));
				scenario.setScenarioDescription(res.getString("DESCRIPTION"));
				scenario.setScenarioCategory(res.getString("CATEGORY"));
				scenario.setLastRun(res.getString("LAST_RUN_DATE"));
				scenarios = new ArrayList<SwtScenario>();
				scenarios.add(scenario);
				
				String userKey = res.getString("USER_ID");
				String userProp = res.getString("USER_NAME")+"/"+res.getString("EMAIL_ADDRESS")+"/"+res.getString("ROLE_ID")+"/"+res.getString("LANG");
				
				if (userEmails.containsKey(userKey)) {
					ArrayList<SwtScenario>	userScenarios = userEmails.get(userKey);
					userScenarios.addAll(scenarios);
				} else {
					userEmails.put(userKey, scenarios);
					userProperties.put(userKey, userProp);
				}

			}
			
			for (String key : userEmails.keySet()) {
				
				String recipientInfo[] = userProperties.get(key).split("/");
				String recipientName = recipientInfo[0];
				String recipientEmail = recipientInfo[1];
				String recipientRole = recipientInfo[2];
				String recipientLang = recipientInfo[3];
				SwtRecipient recipient = new SwtRecipient(recipientName, recipientEmail, recipientRole, recipientLang);
				scenarioRootMap = new HashMap<String, Object>();
				scenarioRootMap.put("scenarios", userEmails.get(key));
				//Send an Email that includes a summary of scenarios raised by 'Alerting Framework'
				sendEmail(recipient, scenarioRootMap , true);
				
					
			}
			log.info("SendingEmailsJob  - [checkAlertingScenarios] - Exit");
			
		} catch (Exception e) {
			if(!lastStatus.equalsIgnoreCase("F"))
				// log error message
				log.error("SendingEmailsJob - [checkAlertingScenarios] - Exception: "
						+ e.getMessage());
			
				throw new SwtException("Exception occured while checking Alerting Scenarios : "
						+ e.getMessage());
			
		} finally {
			// close statement and cnx.
			Object[] exceptions = JDBCCloser.close(res, stmt, conn, null);
			//nullify objects 
			userEmails = null;
			scenarioRootMap = null;
			scenarios = null;
			
		}
	}
	/**
	 *  According to email Flag (S,F) , an appropriate update should be done
	 *  to prevent bombarding users with repeat mails for a live alert scenario.
	 * @param scenarios
	 * @param flag
	 * @throws SwtException 
	 */
	public void updateScenarioEmailFlag(ArrayList<SwtScenario> scenarios,
			String flag) throws SwtException {
		
		Connection conn = null;
		ResultSet res = null;
		PreparedStatement stmt = null;
		
		try {
			log.info("SendingEmailsJob  - [updateScenarioEmailFlag] - Enter");
			conn = ConnectionManager.getInstance().databaseCon();
			
			String successUpdateQuery = "UPDATE  P_SCENARIO_COUNTS SET EMAIL_FLAG = ?, EMAILED_SC_COUNT = ?, EMAILED_SC_COUNT_OVER_T = ? "
					+ "WHERE SCENARIO_ID = ? AND ENTITY_ID = ? AND HOST_ID = ? ";
			
			String failedUpdateQuery = "UPDATE  P_SCENARIO_COUNTS SET EMAIL_FLAG = ? WHERE SCENARIO_ID = ? AND ENTITY_ID = ? AND HOST_ID = ? ";
			
			String updateQuery = "S".equalsIgnoreCase(flag)? successUpdateQuery : failedUpdateQuery;
			stmt = conn.prepareStatement(updateQuery);
			for (SwtScenario scen : scenarios) {
				stmt.setString(1, flag);
				if ("S".equalsIgnoreCase(flag)) {//When email Flag is 'S' ,i.e email was sent successfully
					stmt.setInt(2, Integer.parseInt(scen.getScenarioCount()));
					stmt.setInt(3,Integer.parseInt(scen.getScenarioCountOverT()));
					stmt.setString(4, scen.getScenarioId());
					stmt.setString(5, scen.getEntityId());
					stmt.setString(6, scen.getHostId());
				} else {//When email Flag is 'F' , i.e inability to send mail 
					stmt.setString(2, scen.getScenarioId());
					stmt.setString(3, scen.getEntityId());
					stmt.setString(4, scen.getHostId());
				}
				stmt.execute();
			}
			conn.commit();
			
			log.info("SendingEmailsJob  - [updateScenarioEmailFlag] - Exit");
		} catch (SQLException ex) {
			try {
				log.error("SendingEmailsJob  - [updateScenarioEmailFlag]  Exception: "
					+ ex.getMessage());
				// rollBack
				conn.rollback();
			} catch (SQLException e) {
			
			}

		} catch (Exception e) {
				
				try {
					if(!lastStatus.equalsIgnoreCase("F"))
						log.error("SendingEmailsJob  - [updateScenarioEmailFlag]  Exception: "
								+ e.getMessage());
					// rollBack
					conn.rollback();
				} catch (SQLException exp) {
					
				}
				throw new SwtException("Exception occured while updating Scenario 'Email_Flag' : "
						+ e.getMessage());
			
			
		} finally {
			//Close cnx and nullify objects
			Object[] exceptions = JDBCCloser.close(res, stmt, conn, null);
		}
	}
	/**
	 *  Check if an interface interruption occurs , 
	 *  in this case a 'background' mails will be send to required users
	 * @throws SwtException
	 */
	public void checkInterfaceInterruption() throws SwtException {
		
		ArrayList<SwtNotification> notifications = null;
		ArrayList<SwtRecipient> recipients = null;
		HashMap<String, Object> templateMapping = null;
		
		try {
			log.info("SendingEmailsJob  - [checkInterfaceInterruption] - Enter");
			notifications = listNotifications();
			recipients = interfacesInterruptionsRecipients();
			if(!notifications.isEmpty() && !recipients.isEmpty()){
				
				templateMapping = new HashMap<String, Object>();
				templateMapping.put("notifications", notifications);
				
				for (SwtRecipient recipient : recipients ) {
					sendEmail(recipient, templateMapping, false);
				}
			}
			log.info("SendingEmailsJob  - [checkInterfaceInterruption] - Exit");
			
		} catch (Exception e) {
			if(!lastStatus.equalsIgnoreCase("F"))
				// log error message
				log.error("SendingEmailsJob - [checkInterfaceInterruption] - Exception: "
						+ e.getMessage());
				throw new SwtException("Exception occured while checking Interface Interruptions : "
						+ e.getMessage());
			
		}
		
	}
	/**
	 *  Used to retrieve notifications list having the ability to send mail (EMAIL_FLAG != S)
	 * @return ArrayList<SwtNotification>
	 * @throws SwtException
	 */
    public ArrayList<SwtNotification> listNotifications() throws SwtException{
    	
    	Connection conn = null;
		ResultSet res = null;
		PreparedStatement stmt = null;
		ArrayList<SwtNotification> notificationList =new ArrayList<SwtNotification>();
		
		try {
			log.info("SendingEmailsJob  - [listNotifications] - Enter");
			
			conn = ConnectionManager.getInstance().databaseCon();
			String query = "SELECT * FROM P_NOTIFICATIONS WHERE EMAIL_FLAG <> 'S' ";
			stmt = conn.prepareStatement(query);
			res = stmt.executeQuery();
			
			while (res.next()) {
				SwtNotification  notification = new SwtNotification();
				notification.setHostId(res.getString("HOST_ID"));
				notification.setEntityId(res.getString("ENTITY_ID"));
				notification.setNotificationId(res.getString("NOTIFICATION_ID"));
				notification.setRelationId(res.getString("RELATION_ID"));
				notification.setNotificationType(res.getString("NOTIFICATION_TYPE"));
				notification.setPriority(res.getString("PRIORITY"));
				notification.setNotificationMsg(res.getString("NOTIFICATION_MESSAGE"));
				notificationList.add(notification);
			}
						  
			log.info("SendingEmailsJob  - [listNotifications] - Exit");
		} catch (Exception e) {
			if(!lastStatus.equalsIgnoreCase("F"))
				// log error message
				log.error("SendingEmailsJob - [listNotifications] - Exception: "
						+ e.getMessage());
				throw new SwtException("Exception occured while retrieve notifications list : "
						+ e.getMessage());
			
		} finally {
			//Close cnx and nullify objects
			Object[] exceptions = JDBCCloser.close(res, stmt, conn, null);
		}
		  return notificationList;
    }
    
    /**
     *  Used to get the list of recipients having the criteria (INTERFACE_INTERRUPTION ='Y')
     * @return
     * @throws SwtException
     */
    public ArrayList<SwtRecipient> interfacesInterruptionsRecipients() throws SwtException{
    	
    	ArrayList<SwtRecipient> recipients = null;
    	Connection conn = null;
		ResultSet res = null;
		PreparedStatement stmt = null;
		
    	try {
    		log.info("SendingEmailsJob  - [interfacesInterruptionsRecipients] - Enter");
    		
    		conn = ConnectionManager.getInstance().databaseCon();
			String query = " SELECT LANG,EMAIL_ADDRESS,USER_NAME  FROM S_USERS U INNER JOIN S_ROLE R "
						 +" ON U.ROLE_ID = R.ROLE_ID AND U.HOST_ID = R.HOST_ID AND INTERFACE_INTERRUPTION ='Y' AND INPUT_INTERRUPTION_ALERT_TYPE IN (0, 1)"
				  		 +" WHERE EMAIL_ADDRESS IS NOT NULL";
			stmt = conn.prepareStatement(query);
			res = stmt.executeQuery();
			recipients = new ArrayList<SwtRecipient>();
			while (res.next()) {
				String recipName = res.getString("USER_NAME");
				String emailAddr = res.getString("EMAIL_ADDRESS");
				String language = res.getString("LANG");
				
				SwtRecipient recipient = new SwtRecipient();
				recipient.setRecipientEmail(emailAddr);
				recipient.setRecipientLang(language);
				recipient.setRecipientName(recipName);
					  
				recipients.add(recipient);
			}
			log.info("SendingEmailsJob  - [interfacesInterruptionsRecipients] - Exit");  
		} catch (Exception e) {
			if(!lastStatus.equalsIgnoreCase("F"))
				// log error message
				log.error("SendingEmailsJob - [interfacesInterruptionsRecipients] - Exception: "
						+ e.getMessage());
			
				throw new SwtException("Exception occured while retrieve recipients list : "
						+ e.getMessage());
			
		} finally {
			//Close cnx and nullify objects
			Object[] exceptions = JDBCCloser.close(res, stmt, conn, null);
		}
    
    	return recipients;
    }

    /**
     * Used to update EMAIL_FLAG in P_NOTIFICATIONS table
     * @param notifications
     * @param flag
     * @throws SwtException
     */
	public void updateInterfaceEmailFlag(ArrayList<SwtNotification>notifications , String flag) throws SwtException {

		ResultSet res = null;
		PreparedStatement stmt = null;
		Connection conn = null;
		
		try {
			log.info("SendingEmailsJob  - [updateInterfaceEmailFlag] - Enter");
			
			conn = ConnectionManager.getInstance().databaseCon();
			String updateQuery = "UPDATE  P_NOTIFICATIONS SET EMAIL_FLAG = ? WHERE NOTIFICATION_ID = ?  AND HOST_ID = ? ";
			stmt = conn.prepareStatement(updateQuery);
			
			for (SwtNotification notification :notifications){
				
				stmt.setString(1, flag);
				stmt.setString(2, notification.getNotificationId());
				stmt.setString(3, notification.getHostId());
				stmt.executeUpdate();
			}
			conn.commit();
			log.info("SendingEmailsJob  - [updateInterfaceEmailFlag] - Exit");
			
		} catch (SQLException sqlE) {
			// log error message
			log.error("SendingEmailsJob - [updateInterfaceEmailFlag] - Exception: "
								+ sqlE.getMessage());
			// rollBack
			try {
				conn.rollback();
			} catch (SQLException e) {
			
			}
		} catch (Exception e) {
			if(!lastStatus.equalsIgnoreCase("F"))
				// log error message
				log.error("SendingEmailsJob - [updateInterfaceEmailFlag] - Exception: "
						+ e.getMessage());
				// rollBack
				try {
					conn.rollback();
				} catch (SQLException e1) {
					// TODO Auto-generated catch block
				}
				
				throw new SwtException("Exception occured while updating notification 'Email_Flag' : "
						+ e.getMessage());
			
		} finally {
			//Close cnx and nullify objects
			Object[] exceptions = JDBCCloser.close(res, stmt, conn, null);
			}
		
	}
	
	
	
	/**
	 * Used to send support mail 
	 * @param recipient
	 * @param templateMapping
	 * @param IsScenario
	 * @throws SwtException
	 */
	public void sendEmail(SwtRecipient recipient, HashMap<String, Object> templateMapping,
			boolean IsScenario) throws SwtException {
		
		// Email's properties
		String templateFilename = null;
		String subject = null;
		String recipientsLang = null;
		
		try {
			log.info("SendingEmailsJob  - [sendEmail] - Entering");
			
			templateMapping.put("recipient_name", recipient.getRecipientName());
			templateMapping.put("role_id", recipient.getRecipientRole());
			recipientsLang = recipient.getRecipientLang();
			
			if (IsScenario) {
			
				// Add customer email (body/signature) according to recipient language (EN or FR )
				if (!SwtUtil.isEmptyOrNull(recipientsLang)) {
					if ("EN".equalsIgnoreCase(recipientsLang)) {
						subject = PropertiesFileLoader.getInstance().getPropertiesValue(SwtConstants.SCEN_SUM_ALERT_SUBJ);
						templateFilename = SwtUtil.contextRealPath+SwtConstants.ALERT_SCENARIOS_TEMPLATE_EN;
					
					} else if ("FR".equalsIgnoreCase(recipientsLang)) {
						subject = PropertiesFileLoader.getInstance().getPropertiesValue(SwtConstants.SCEN_SUM_ALERT_SUBJ);
						templateFilename = SwtUtil.contextRealPath+SwtConstants.ALERT_SCENARIOS_TEMPLATE_FR;
					}
				} else {
					templateFilename = SwtUtil.contextRealPath+SwtConstants.ALERT_SCENARIOS_TEMPLATE_EN;
				}
					
			} else {
				if (!SwtUtil.isEmptyOrNull(recipientsLang)) {
					if ("EN".equalsIgnoreCase(recipientsLang)) {
						subject = "Interrupt Notification";//according to user language.
						templateFilename = SwtUtil.contextRealPath+SwtConstants.INTERRUPT_NOTIF_TEMPLATE_EN;
					
					} else if ("FR".equalsIgnoreCase(recipientsLang)) {
						subject = "Notification D'interruption";//according to user language.
						templateFilename = SwtUtil.contextRealPath+SwtConstants.INTERRUPT_NOTIF_TEMPLATE_FR;
					}
				} else {
					templateFilename = SwtUtil.contextRealPath+SwtConstants.INTERRUPT_NOTIF_TEMPLATE_EN;
				}
			}
			
			// Write content into HTML mail template
			Configuration cfg = new Configuration();
			File templateFile = new File(templateFilename);
			cfg.setDirectoryForTemplateLoading(new  File(SwtUtil.contextRealPath+"/templates"));
			Template template = cfg.getTemplate(templateFile.getName());

			Writer out = new StringWriter();
			template.process(templateMapping, out);
			
			ArrayList<String> recipients = new ArrayList<String>();
			recipients.add(recipient.getRecipientEmail());
			//Send Email 
			SwtUtil.sendEmail(recipients, null, subject, out.toString(), null);

			if (IsScenario) {
				ArrayList<SwtScenario> scenarios = (ArrayList<SwtScenario>) templateMapping.get("scenarios");
				updateScenarioEmailFlag(scenarios, "S");
			} else {
				ArrayList<SwtNotification> notifications = (ArrayList<SwtNotification>) templateMapping.get("notifications");
				updateInterfaceEmailFlag(notifications, "S");
			}
			System.out.println("Sent mail and Scenario Email Flag  updated successfully...");
			log.info("SendingEmailsJob  - [sendEmail] - Exit");
	
		} catch (Exception e) {
			if(!lastStatus.equalsIgnoreCase("F")){
				// log error message
				log.error("SendingEmailsJob - [sendEmail] - Exception: "+ e.getMessage());
				if (IsScenario) {
					ArrayList<SwtScenario> scenarios = (ArrayList<SwtScenario>) templateMapping.get("scenarios");
					updateScenarioEmailFlag(scenarios, "F");
					
				} else {
					ArrayList<SwtNotification> notifications = (ArrayList<SwtNotification>) templateMapping.get("notifications");
					updateInterfaceEmailFlag(notifications, "F");
				}
				
			}
			throw new SwtException("Exception occured while sending mail : "+ e.getMessage());
		} finally {
			templateFilename = null;
			 subject = null;
		}
	}
}
