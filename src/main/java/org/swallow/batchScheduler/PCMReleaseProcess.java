/*
 * @(#)PCMReleaseProcess.java 
 *
 * Copyright (c) 2006-2019 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */

package org.swallow.batchScheduler;

import java.sql.CallableStatement;
import java.sql.Connection;
import java.sql.Types;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.swallow.exception.SwtErrorHandler;
import org.swallow.util.CacheManager;
import org.swallow.util.JDBCCloser;
import org.swallow.util.SwtConstants;
import org.swallow.util.SwtUtil;

import org.hibernate.Session;

/**
 * PCMReleaseProcess.java
 * 
 * This class is used for releasing spread payment requests in payment control module.
 * 
 */
public class PCMReleaseProcess extends SwtBasicJob {
	/*
	 * Logger object for logging details to server.log file
	 */
	private final Log log = LogFactory.getLog(PCMReleaseProcess.class);

	/**
	 * This method is used to call spreading payment request Process from scheduler screen
	 * based upon the time set in scheduler job 
	 * 
	 * @return String
	 */
	public String executeJob(Integer schedulerId) {
		// Variable decleration for retValue
		String retValue = null;
		// Variable decleration for Callable Statement
		CallableStatement cstmtJob = null;
		// Variable decleration for result
		int result;
		Connection connPCM = null;
		Session session = null;
		try {
			log.debug(this.getClass().getName() + " - [executeJob] - Entry");
			session = SwtUtil.pcSessionFactory.openSession();
			connPCM = SwtUtil.connection(session);
			// Prepared statement for the cursor
			cstmtJob = connPCM.prepareCall(
					"{call  PKG_PC_PROCESS.run_spreading(?,?)}");
			// Setting hostId value
			cstmtJob.setString(1, CacheManager.getInstance().getHostId());
			// Getting output from the cursor
			cstmtJob.registerOutParameter(2, Types.VARCHAR);
			// Excecute cursor
			cstmtJob.executeUpdate();
			// Getting the output value in cursor
			retValue = cstmtJob.getString(2);
			// Type casting the string
			result = Integer.parseInt(retValue);
			if (result == 0)
				retValue = SwtConstants.SUCCESS;
			else
				retValue = SwtConstants.FAIL;

		} catch (Exception e) {
			retValue = SwtConstants.FAIL;
			log.error(this.getClass().getName()
					+ " - Exception Catched in [executeJob] method : - "
					+ e.getMessage());
		} finally {
			JDBCCloser.close(null, cstmtJob, connPCM, session);
			log.debug(this.getClass().getName() + " - [executeJob] - Exit");
		}
		
		return retValue;
	}
}
