package org.swallow.job;

import java.util.*;
import java.util.Calendar;
import java.text.*;

import org.quartz.impl.*;
import org.quartz.impl.triggers.CronTriggerImpl;
import org.quartz.impl.triggers.SimpleTriggerImpl;
import org.quartz.*;

//Logging Related classes
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;


public class BatchSchedular {
	
	private static BatchSchedular instance;
	private Scheduler schedular;
	//private Hashtable jobdetail;
	//private Hashtable triggerdetail;
	private Hashtable jobinfoHashtable;

	private final Log log = LogFactory.getLog(BatchSchedular.class);

	public static BatchSchedular getInstance()
	{
		if (instance == null)
		{
			instance = new BatchSchedular(); 
		}
		return instance;
	}
	
	private BatchSchedular()
	{
		init();
		log.debug("BatchSchedular's constructor");
	}
	
	private void init() 
	{
		// Read the all job details from database & start the schedular

		getSchedular();
		readJobsInfofromDb();
		scheduleJobs();
		startSchedular();

	}

	private void getSchedular()  
	{
		// First we must get a reference to a scheduler
		try
		{
			SchedulerFactory sf = new StdSchedulerFactory();
			schedular = sf.getScheduler();
		}
		catch (SchedulerException ex)
		{
		}
	}

	private void readJobsInfofromDb()
	{
		try
		{
//			Date runTime = TriggerUtils.getEvenSecondDate(new Date());
			
			Calendar calendar = Calendar.getInstance(); // gets a calendar using the default time zone and locale.
			calendar.add(Calendar.SECOND, 1);
			calendar.set(Calendar.MILLISECOND, 0);
			
			
			//jobdetail = new Hashtable();
			//triggerdetail = new Hashtable();
			jobinfoHashtable = new Hashtable();

			JobDetailImpl job1 = new JobDetailImpl("job1", "group1", HelloJob.class);
			SimpleTriggerImpl trigger1 = new SimpleTriggerImpl("trigger1", "group1", calendar.getTime(), null, SimpleTrigger.REPEAT_INDEFINITELY,2000L);
			//jobdetail.put("job1",job1);
			//triggerdetail.put("job1",trigger1);
			JobInfo jobInfo1 = new JobInfo();
			jobInfo1.setJobDetail(job1);
			jobInfo1.setTrigger(trigger1);
			jobinfoHashtable.put("job1",jobInfo1);

			JobDetailImpl job2 = new JobDetailImpl("job2", "group1", HelloJob1.class);
			//SimpleTrigger trigger2 = new SimpleTrigger("trigger2", "group1", runTime, null, SimpleTrigger.REPEAT_INDEFINITELY,3000L);
			CronTriggerImpl  trigger2 = new CronTriggerImpl("trigger2", "group1","job2","group1",calendar.getTime(),null,"0 8 12 * * ?");
			JobInfo jobInfo2 = new JobInfo();
			jobInfo2.setJobDetail(job2);
			jobInfo2.setTrigger(trigger2);
			jobinfoHashtable.put("job2",jobInfo2);
		}
		catch(ParseException ex)
		{
			ex.printStackTrace();
		}

	}

	private void scheduleJobs()
	{
		try
		{		
			for (Enumeration eJobInfo = jobinfoHashtable.elements(); eJobInfo.hasMoreElements(); ) 
			{
				JobInfo jobInfo = (JobInfo) eJobInfo.nextElement();
				schedular.scheduleJob(jobInfo.getJobDetail(), jobInfo.getTrigger());
			}
		}
		catch(SchedulerException ex)
		{

		}
	}

	private void startSchedular()
	{
		try{
			schedular.start();
		}
		catch(SchedulerException ex)
		{

		}

	}

	public boolean reScheduleJob(String jobName, SimpleTrigger trigger1)
	{
		/*
		triggerdetail.remove(jobName);
		triggerdetail.put(jobName,trigger);
		

		try{
			schedular.rescheduleJob("trigger1", "group1",trigger);
		}
		catch(SchedulerException ex)
		{
			log.debug("SchedulerException  in rescheduling the job >> " + ex);	
			return false;
		}
		*/
		/*
		try
		{			
			for (Enumeration eTrigger = triggerdetail.elements(); eTrigger.hasMoreElements(); ) 
			{
				SimpleTrigger trigger =  (SimpleTrigger) eTrigger.nextElement();
				trigger.setRepeatInterval(10000L);				
				schedular.rescheduleJob("trigger1", "group1",trigger);
				break;
			}
		}
		catch(SchedulerException ex)
		{

		}
		*/
		return true;
	}
}
