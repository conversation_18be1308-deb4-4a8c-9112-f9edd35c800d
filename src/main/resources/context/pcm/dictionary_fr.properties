# ----------  generic label --------------------
#label.moreItems = ...
label.moreItems = ...
#label.entity = Entity
label.entity = Entity
#label.date= Date
label.date= Date
#label.Currency= Currency
label.Currency= Currency
#label.genericException=An error is occurred, Please contact Administrator
label.genericException=Une erreur est survenue, veuillez contacter l'administrateur
#label.fourEyes=Four Eyes Process
label.fourEyes=Processus de quatre yeux
#label.selectAll=Select All
label.selectAll=Sélectionner tout
#label.pdf=PDF
label.pdf=PDF
#label.excel=Excel
label.excel=Excel
#label.csv=CSV
label.csv=CSV
#label.to=To
label.to=À
#label.valueDate = Value Date
label.valueDate = Value Date

# ----------  generic buttons --------------------
#button.cancel= Cancel
button.cancel= Annuler
#button.close= Close
button.close= Fermer
#button.save= Save
button.save= Enregistrer
#button.add= Add
button.add= Ajouter
#button.change = Change
button.change = Changer
#button.view = View
button.view = Voir
#button.delete = Delete
button.delete = Effacer
# button.ok = OK
button.ok= Valider
#button.reset= Reset
button.reset= Reset
#button.undo = Undo
button.undo = Annuler
#button.replace = Replace
button.replace = remplacer
#button.refresh = Refresh
button.refresh = Refresh
#button.rate = Rate
button.rate = Rate
#button.options = Options
button.options = Options
#button.report=Report
button.report=Rapporter

# ----------  generic alert --------------------
#alert.delete = Do you wish to delete this row?
alert.delete = Êtes-vous sûr de vouloir supprimer la ligne
#alert.validTime = Please enter a valid time
alert.validTime = S\u2019il vous plaît entrer une heure valide
#alert.mandatoryField = Please fill mandatory fields
alert.mandatoryField = S\u2019il vous plaît entrer Champ obligatoire
#alert.yes.label = Yes
alert.yes.label = Oui
#alert.no.label = No
alert.no.label = Non
#alert.validTime = Please enter a valid time
alert.validTime = Please enter a valid time
#alert.TimeAlreadyExist = Time already exists
alert.TimeAlreadyExist = Time already exists
#alert.validAmount = Please enter a valid amount
alert.validAmount = Please enter a valid amount
#alert.validDate = Please enter  a valid date 
alert.validDate = S\u2019il vous plaît entrer une date valide

# ----------  generic error --------------------
# error.contactAdmin=Please contact your System Administrator...
error.contactAdmin=Contactez votre administrateur système...
#error.unexpectedError=An unexpected error has occurred.
error.unexpectedError=Une erreur imprévue s'est produite.
# errors.DataIntegrityViolationExceptioninAdd=Record already exists
errors.DataIntegrityViolationExceptioninAdd=Enregistrement existe déjà 
# errors.DataIntegrityViolationException=A data integrity violation occurred
errors.DataIntegrityViolationException=Une violation de l\u2019intégrité des données produites
# errors.DataIntegrityViolationExceptioninDelete=Record cannot be deleted as other transactions depend on it
errors.DataIntegrityViolationExceptioninDelete=Enregistrement ne peut pas être supprimé car d\u2019autres opérations dépendent

# ----------  generic tooltip --------------------
# tooltip.cancel=Cancel changes and exit
tooltip.cancel=Annuler les modifications et quitter
# tooltip.close=Close window
tooltip.close=Fermer la fenêtre
# tooltip.save=Save changes and exit
tooltip.save=Enregistrer les modifications et quitter
# tooltip.add=Add new record
tooltip.add=Ajouter nouveau record
# tooltip.change=Change selected record
tooltip.change=Changer l'enregistrement sélectionné
# tooltip.view=Display selected record
tooltip.view=Afficher l'enregistrement sélectionné
# tooltip.delete=Delete selected record
tooltip.delete=Supprimer l'enregistrement sélectionné
#tooltip.ok=Save changes and exit
tooltip.ok=Enregistrer les modifications et quitter
#tooltip.undo = Cancel changes
tooltip.undo = Annuler les modifications
#tooltip.replace = Replace
tooltip.replace = remplacer
#tooltip.saveRefresh=Save refresh rate
tooltip.saveRefresh=Enregistrer le taux de rafraîchissement
#tooltip.refresh = Refresh window
tooltip.refresh = Refresh window
#tooltip.options = Change Options
tooltip.options = Change Options
#tooltip.print =print
tooltip.print =print
#tooltip.help = help
tooltip.help =help
#tooltip.pdf = pdf
tooltip.pdf = pdf
#tooltip.csv = csv
tooltip.csv = csv
#tooltip.excel = excel
tooltip.excel = excel
#tooltip.from=From
tooltip.from=From
#tooltip.to=To
tooltip.to=To
#tooltip.rate = Rate window
tooltip.rate = Rate window
# ---------- spreadProfilesMaintenance --------------------
spreadProfilesMaintenance.processPoint.All=All
spreadProfilesMaintenance.processPoint.Only=Only
spreadProfilesMaintenance.processPoint.ExceptAll=All Except

# ---------- stopRuleMaintenance --------------------
stopRuleMaintenance.title.window = Stop Rule Maintenance
stopRuleMaintenanceDetails.title.window = Stop Rule Details
stopRuleMaintenance.title.window = Stop Rule Maintenance
stopRuleMaintenance.title.window = Stop Rule Details
stopRuleMaintenance.label.statusGroup = Status
stopRuleMaintenance.label.activeRadioButton = Active
stopRuleMaintenance.label.inactiveRadioButton = Inactive
stopRuleMaintenance.label.bothRadioButton = Both
stopRuleMaintenance.activateButton.label = Activate
stopRuleMaintenance.deactivateButton.label = Deactivate
alert.stopRuleMaintenance.ProcessRunning = Stop rule Process is running on the selected Rule , the selected Rule cannot be deleted or deactivated for the moment
alert.stopRuleMaintenance.recordDepend = Cannot delete a STOP rule that has previously stopped a Payment Request
alert.stopRuleMaintenance.errorOcurred = Error occurred, Please contact your System Administrator
alert.stopRuleMaintenance.deactivatePayment = When deactivated the attached Payments will be
alert.stopRuleMaintenance.wantContinue = . Do you want to continue?
alert.stopRuleMaintenance.deleteStopRule =Cannot delete Stop Rule: Stop Rule has stopped Payment Request(s).

stopRuleMaintenanceDetails.ruleId.label = Stop Rule ID*
stopRuleMaintenanceDetails.ruleId.tooltip = Enter a unique ID for the STOP Rule
stopRuleMaintenanceDetails.ruleName.label = Stop Rule Name*
stopRuleMaintenanceDetails.ruleName.tooltip = Enter the STOP Rule Name
stopRuleMaintenanceDetails.active.label = Active
stopRuleMaintenanceDetails.active.tooltip = Select whether Rule is Active or not
stopRuleMaintenanceDetails.rulesActionOnDeactivation.label = Action On Deactivation
stopRuleMaintenanceDetails.radioSetWaiting.label = Set To Initial Status
stopRuleMaintenanceDetails.radioLeaveStoped.label =Leave Stopped
stopRuleMaintenanceDetails.rulesType.label = Type
stopRuleMaintenanceDetails.radioquickExpression.label = Quick Expression
stopRuleMaintenanceDetails.radioAdvancedExpression.label = Advanced Expression
stopRuleMaintenanceDetails.valueDates.label = Apply to Value Dates
stopRuleMaintenanceDetails.from.label = From
stopRuleMaintenanceDetails.from.tooltip = Enter start date (if applicable)
stopRuleMaintenanceDetails.to.label = To
stopRuleMaintenanceDetails.to.tooltip = Enter end date (if applicable)
stopRuleMaintenanceDetails.stopAllCheckbox.tooltip= Click if all Payment Requests are to be stopped
stopRuleMaintenanceDetails.stopAllCheckbox.label = Stop ALL
stopRuleMaintenanceDetails.quickExpressionPanel.title = Quick Expression
stopRuleMaintenanceDetails.currency.label = Currency
stopRuleMaintenanceDetails.currency.toolTip = Select for Currency
stopRuleMaintenanceDetails.amountOperatorCombo.label = Amount
stopRuleMaintenanceDetails.amountOperatorCombo.toolTip = Select for Amount Operator
stopRuleMaintenanceDetails.amountOperatorText.toolTip = Enter an Amount
stopRuleMaintenanceDetails.country.label = Country
stopRuleMaintenanceDetails.country.toolTip = Select for Country
stopRuleMaintenanceDetails.partyBic.label = Party BIC
stopRuleMaintenanceDetails.partyBic.toolTip = Enter an Party BIC
stopRuleMaintenanceDetails.source.label = Source
stopRuleMaintenanceDetails.source.toolTip = Select for Payment Source
stopRuleMaintenanceDetails.messageType.label = Message Type
stopRuleMaintenanceDetails.messageType.toolTip = Select for Message Type
stopRuleMaintenanceDetails.accountGroup.label = Account Group
stopRuleMaintenanceDetails.accountGroup.toolTip = Select for Account Group
stopRuleMaintenanceDetails.ruleTextPanel.title = Rule Text
stopRuleMaintenanceDetails.ruleExpression.button.label= Rule Builder
stopRuleMaintenanceDetails.activationInfoPanel.title = Activation Info
stopRuleMaintenanceDetails.activated.label= Activated At 
stopRuleMaintenanceDetails.by.label= By
stopRuleMaintenanceDetails.deactivated.label = De-Activated At

# ---------- queryBuilder --------------------
#queryBuilderScreen.title.window = Query Builder
queryBuilderScreen.title.window = Query Builder


# ---------- CurrencyMaintenance --------------------
#currencyMaintenance.title.window = Currency Maintenance
currencyMaintenance.title.window = Currency Maintenance
#currencyMaintenanceDetails.title.window = Currency Maintenance Details
currencyMaintenanceDetails.title.window = Currency Maintenance Details
#currencyMaintenanceDetails.reorder.alert = This order already exists with another currency, do you wish to reorder?
currencyMaintenanceDetails.reorder.alert = This order already exists with another currency, do you wish to reorder?
#currencyMaintenanceDetails.amount.alert = Please enter a valid amount?
currencyMaintenanceDetails.amount.alert = Please enter a valid amount?
#currencyMaintenanceDetails.label.ccy = Currency*
currencyMaintenanceDetails.label.ccy = Devise*
#currencyMaintenanceDetails.tooltip.ccy = Enter the Currency Code
currencyMaintenanceDetails.tooltip.ccy = Entrez le code de la devise
#currencyMaintenanceDetails.label.name = Currency Name
currencyMaintenanceDetails.label.name = Nom de la devise
#currencyMaintenanceDetails.label.ordinal = Ordinal
currencyMaintenanceDetails.label.ordinal = Ordinal
#currencyMaintenanceDetails.tooltip.ordinal = Enter the Processing Order for the Currency
currencyMaintenanceDetails.tooltip.ordinal = Entrez l'ordre de traitement pour la devise
#currencyMaintenanceDetails.label.multiplier = Multiplier
currencyMaintenanceDetails.label.multiplier = Multiplicateur
#currencyMaintenanceDetails.tooltip.multiplier = Select the appropriate multiplier from the dropdown list
currencyMaintenanceDetails.tooltip.multiplier = Sélectionnez le multiplicateur approprié dans la liste
#currencyMaintenanceDetails.label.largeAmount = Large Amount Threshold
currencyMaintenanceDetails.label.largeAmount = Large montant Threshold
#currencyMaintenanceDetails.tooltip.largeAmount = Enter the Amount above which Payment Requests are considered \u2018Large\u2019
currencyMaintenanceDetails.tooltip.largeAmount = Entrez le montant au-dessus duquel les demandes de paiement sont considérées \u2018Large\u2019
#currencyMaintenanceDetails.notSaved.alert = Currency could not be saved successfully.<br>Please contact your System Administrator!
currencyMaintenanceDetails.notSaved.alert = Currency could not be saved successfully.<br>Please contact your System Administrator!
# ---------- CategoryMaintenance --------------------
#categoryMaintenance.title.window = Payment Categories Maintenance
categoryMaintenance.title.window = Payment Categories Maintenance
#categoryMaintenanceDetails.title.window = Payment Category Details
categoryMaintenanceDetails.title.window = Payment Category Details
#categoryMaintenanceDetails.label.id = Category ID*
categoryMaintenanceDetails.label.id = Category ID*
#categoryMaintenanceDetails.tooltip.id = Enter an ID for this Category
categoryMaintenanceDetails.tooltip.id = Enter an ID for this Category
#categoryMaintenanceDetails.label.name = Name
categoryMaintenanceDetails.label.name = Name*
#categoryMaintenanceDetails.tooltip.name = Enter the Category Name
categoryMaintenanceDetails.tooltip.name = Enter the Category Name
#categoryMaintenanceDetails.label.active = Active
categoryMaintenanceDetails.label.active = Active
#categoryMaintenanceDetails.tooltip.active = If Active, click the box
categoryMaintenanceDetails.tooltip.active = If Active, click the box
#categoryMaintenanceDetails.label.order = Processing Order
categoryMaintenanceDetails.label.order = Processing Order*
#categoryMaintenanceDetails.label.ruleAssignmentPriority = Rule Assignment Priority*
categoryMaintenanceDetails.label.ruleAssignmentPriority = Rule Assignment Priority*
#categoryMaintenanceDetails.tooltip.order = Enter the processing order
categoryMaintenanceDetails.tooltip.order = Enter the processing order
#categoryMaintenanceDetails.tooltip.ruleAssignmentPriority = Enter the rule assignment priority
categoryMaintenanceDetails.tooltip.ruleAssignmentPriority = Enter the rule assignment priority
#categoryMaintenanceDetails.label.type = Type
categoryMaintenanceDetails.label.type = Type
#categoryMaintenanceDetails.tooltip.type = Click whether the Category is Urgent or Spread
categoryMaintenanceDetails.tooltip.type = Click whether the Category is Urgent or Spread
#categoryMaintenanceDetails.label.urgent = Urgent
categoryMaintenanceDetails.label.urgent = Urgent
#categoryMaintenanceDetails.label.spreadable = Spreadable
categoryMaintenanceDetails.label.spreadable = Spreadable
#categoryMaintenanceDetails.label.releaseTime = Release time
categoryMaintenanceDetails.label.releaseTime = Release time
#categoryMaintenanceDetails.tooltip.releaseTime = Click for how to process timed payment requests
categoryMaintenanceDetails.tooltip.releaseTime = Click for how to process timed payment requests
#categoryMaintenanceDetails.label.kickOff = Kick-off
categoryMaintenanceDetails.label.kickOff = Kick-off
#categoryMaintenanceDetails.label.froPaymentRequest = From Payment Request
categoryMaintenanceDetails.label.froPaymentRequest = From Payment Request
#categoryMaintenanceDetails.label.immediate = Immediate
categoryMaintenanceDetails.label.immediate = Immediate
#categoryMaintenanceDetails.label.specificTime = Specific time
categoryMaintenanceDetails.label.specificTime = Specific time
#categoryMaintenanceDetails.tooltip.time = Time
categoryMaintenanceDetails.tooltip.time = Time
#categoryMaintenanceDetails.label.daysOffset = Release value date offset
categoryMaintenanceDetails.label.daysOffset = Release value date offset
#categoryMaintenanceDetails.tooltip.daysOffset  = Number of days ahead of value date to release
categoryMaintenanceDetails.tooltip.daysOffset  = Number of days ahead of value date to release
#categoryMaintenanceDetails.label.assignmentMethod = Assignment Method
categoryMaintenanceDetails.label.assignmentMethod = Assignment Method
#categoryMaintenanceDetails.tooltip.assignmentMethod = Click for Manual, System or Both who can use this Category
categoryMaintenanceDetails.tooltip.assignmentMethod = Click for Manual, System or Both who can use this Category
#categoryMaintenanceDetails.label.manual = Manual
categoryMaintenanceDetails.label.manual = Manual
#categoryMaintenanceDetails.label.system = System
categoryMaintenanceDetails.label.system = System
#categoryMaintenanceDetails.label.both = Both
categoryMaintenanceDetails.label.both = Both
#categoryMaintenanceDetails.label.liqCheck = Use Liquidity Check
categoryMaintenanceDetails.label.liqCheck = Use Liquidity Check
#categoryMaintenanceDetails.tooltip.liqCheck = Click if Liquidity Check is required
categoryMaintenanceDetails.tooltip.liqCheck = Click if Liquidity Check is required
#categoryMaintenanceDetails.label.target = Include in % Payment Target
categoryMaintenanceDetails.label.target = Include in % Payment Target
#categoryMaintenanceDetails.tooltip.target = Click if the Payment Requests are to be included in the percentage calculations
categoryMaintenanceDetails.tooltip.target = Click if the Payment Requests are to be included in the percentage calculations
#categoryMaintenanceDetails.label.target = Include in avail. Liquidity
categoryMaintenanceDetails.label.incAvailLiq = Include in avail. Liquidity
#categoryMaintenanceDetails.tooltip.target = Click where Payment Requests are included in the debit calculation for balance determination
categoryMaintenanceDetails.tooltip.incAvailLiq = Click where Payment Requests are included in the debit calculation for balance determination
#categoryMaintenanceDetails.rulePanel.title = Category Rules
categoryMaintenanceDetails.rulePanel.title = Category Rules
#categoryMaintenanceDetails.spreadPanel.title = Associated Spread Profiles
categoryMaintenanceDetails.spreadPanel.title = Associated Spread Profiles
#alert.contactAdminForcategoryMaintenance = Category could not be saved successfully.<br>Please contact your System Administrator!
alert.contactAdminForcategoryMaintenance = Category could not be saved successfully.<br>Please contact your System Administrator!
#alert.CategoryMaintenanceAddRulePriority = An other category has the same rule assignment priority. Do you want to continue?
alert.CategoryMaintenanceAddRulePriority = An other category has the same rule assignment priority. Do you want to continue?

# ---------- CategoryRuleMaintenance --------------------
#categoryRuleDetails.title.window = Category Rule Details
categoryRuleDetails.title.window = Category Rule Details
#errors.CouldNotSaveCategoryRuleNameWithSameNameExceptioninAdd=Category rule record with Same name already exists
errors.CouldNotSaveCategoryRuleNameWithSameNameExceptioninAdd= Le nom de la règle de catégorie existe déjà

#categoryRuleDetails.label.name = Name
categoryRuleDetails.label.name = Name
#categoryRuleDetails.tooltip.name = Enter the Rule Name
categoryRuleDetails.tooltip.name = Enter the Rule Name
#categoryRuleDetails.label.order = Processing Order
categoryRuleDetails.label.order = Processing Order
#categoryRuleDetails.tooltip.order = Enter the processing order for this Rule
categoryRuleDetails.tooltip.order = Enter the processing order for this Rule

#categoryRuleDetails.label.source = Apply to Source
categoryRuleDetails.label.source = Apply to Source
#categoryRuleDetails.tooltip.source = If this Rule only applies to a specific Source then select from the dropdown list
categoryRuleDetails.tooltip.source = If this Rule only applies to a specific Source then select from the dropdown list
#categoryRuleDetails.label.entity = Apply to Entity
categoryRuleDetails.label.entity = Apply to Entity
#categoryRuleDetails.tooltip.entity = If this Rule only applies to a specific Entity then select from the dropdown list
categoryRuleDetails.tooltip.entity = If this Rule only applies to a specific Entity then select from the dropdown list
#categoryRuleDetails.label.ccy = Apply to Ccy
categoryRuleDetails.label.ccy = Apply to Ccy
#categoryRuleDetails.tooltip.ccy = If this Rule only applies to a specific Currency then select from the dropdown list
categoryRuleDetails.tooltip.ccy = If this Rule only applies to a specific Currency then select from the dropdown list
#categoryRuleDetails.rule.expression.title = Payment Category Rule Expression
categoryRuleDetails.rule.expression.title = Payment Category Rule Expression
#categoryRuleDetails.ruleExpression.button.label = Rule Builder
categoryRuleDetails.ruleExpression.button.label = Rule Builder
#categoryRuleDetails.tooltip.ccy = If this Rule only applies to a specific Currency then select from the dropdown list
categoryRuleDetails.tooltip.ccy = If this Rule only applies to a specific Currency then select from the dropdown list
categoryRuleDetails.rule.expression.title = Payment Category Rule Expression
categoryRuleDetails.rule.expression.button.label = Rule Builder
errors.CouldNotSaveCategoryRuleNameWithSameNameExceptioninAdd = Category rule record with Same name already exists
alert.CategoryRuleNameCannotEmpty = Category Rule Name cannot be empty!
alert.CategoryRuleReorder = This order already exists with another Category rule, do you wish to reorder?

# ---------- Dashboard --------------------
#dashboard.title.window = PCM Dashboard Monitor - SMART-Predict
dashboard.title.window = PCM Monitor - SMART-Predict
#dashboard.refreshRate.alert=Enter a valid refresh rate
dashboard.refreshRate.alert=Entrer un taux de rafraéchissement valide
dashboard.lastRefresh.label = Last Refresh:
dashboard.selected.label = Selected
dashboard.entity.tooltip = select an entity ID
dashboard.currencyThreshold.label = Apply currency threshold
dashboard.currencyThreshold.tooltip = Apply currency threshold
dashboard.currencyMultiplier.label = Apply currency multiplier
dashboard.currencyMultiplier.tooltip = Apply currency multiplier
dashboard.spreadOnly.label = Spread Only
dashboard.spreadOnly.tooltip = Select whether display the totals for Spread only
dashboard.displayValue.label = Value/Volume
dashboard.displayValue.tooltip = This will Toggle between the Value or Volume of PR\u2019s for each cell.
dashboard.radioValue.label = Value
dashboard.radioVolume.label = Volume

# ---------- Dashboard Details --------------------
#dashboardDetails.title.window = PCM Breakdown Monitor - SMART-Predict
dashboardDetails.title.window = PCM Breakdown Monitor - SMART-Predict
#dashboardDetails.currency.label = Currency
dashboardDetails.currency.label = Currency
#dashboardDetails.currency.tooltip = select currency code
dashboardDetails.currency.tooltip = select currency code
#dashboardDetails.entity.label = Entity
dashboardDetails.entity.label = Entity
#dashboardDetails.entity.tooltip = select entity
dashboardDetails.entity.tooltip = select entity
#dashboardDetails.accountGroup.label = Account Group
dashboardDetails.accountGroup.label = Account Group
#dashboardDetails.accountGroup.tooltip = select account group
dashboardDetails.accountGroup.tooltip = select account group
#dashboardDetails.account.label = Account
dashboardDetails.account.label = Account
#dashboardDetails.account.tooltip = select account
dashboardDetails.account.tooltip = select account
#dashboardDetails.status.label = Statu
dashboardDetails.status.label = Status
#dashboardDetails.status.tooltip = select status
dashboardDetails.status.tooltip = select status
#dashboardDetails.currencyThreshold.label = Apply currency threshold
dashboardDetails.currencyThreshold.label = Apply currency threshold
#dashboardDetails.currencyMultiplier.label = Apply currency multiplier
dashboardDetails.currencyMultiplier.label = Apply currency multiplier
#dashboardDetails.TimeFrame.label= TimeFrame
dashboardDetails.TimeFrame.label= TimeFrame
#dashboardDetails.radioCurrency.label= Currency
dashboardDetails.radioCurrency.label= Currency
#dashboardDetails.radioEntity.label= Entity
dashboardDetails.radioEntity.label= Entity
#dashboardDetails.radiosystem.label= System
dashboardDetails.radiosystem.label= System
#dashboardDetails.startDayBalance.label= Start of Day Balance
dashboardDetails.startDayBalance.label= Start of Day Balance
#dashboardDetails.confirmedCredits.label= Confirmed Credits
dashboardDetails.confirmedCredits.label= Confirmed Credits
#dashboardDetails.creditLine.label= Credit Line
dashboardDetails.creditLine.label= Credit Line
#dashboardDetails.releasedPayments.label= Released Payments
dashboardDetails.releasedPayments.label= Released Payments
#dashboardDetails.otherPayments.label= Other Payments
dashboardDetails.otherPayments.label= Other Payments
#dashboardDetails.availableLiquidityEx.label= Available Liquidity (ex Credit Line)
dashboardDetails.availableLiquidityEx.label= Available Liquidity (ex Credit Line)
#dashboardDetails.availableLiquidityInc.label= Available Liquidity (inc Credit Line)
dashboardDetails.availableLiquidityInc.label= Available Liquidity (inc Credit Line)
#dashboardDetails.reserve.label= Reserve
dashboardDetails.reserve.label= Reserve
#dashboardDetails.buttonDisplay.label = Display Payment
dashboardDetails.buttonDisplay.label = Display Payment
#dashboardDetails.buttonDisplay.tooltip = Display Payment
dashboardDetails.buttonDisplay.tooltip = Display Payment
#dashboardDetails.buttonRelease.label = Release Payment
dashboardDetails.buttonRelease.label = Release Payment
#dashboardDetails.buttonRelease.tooltip = Release Payment
dashboardDetails.buttonRelease.tooltip = Release Payment
#dashboardDetails.spreadDisplay.label = Spread Display
dashboardDetails.spreadDisplay.label = Spread Display
#dashboardDetails.spreadDisplay.tooltip = Spread Display
dashboardDetails.spreadDisplay.tooltip = Spread Display
#dashboardDetails.buttonRelease.label = Release Payment
dashboardDetails.buttonRelease.label = Release Payment
#dashboardDetails.buttonRelease.tooltip = Release Payment
dashboardDetails.buttonRelease.tooltip = Release Payment
#dashboardDetails.unStopButton.label = UnStop
dashboardDetails.unStopButton.label = UnStop
#dashboardDetails.unStopButton.tooltip = UnStop
dashboardDetails.unStopButton.tooltip = UnStop
#dashboardDetails.changeCatButton.label = Change Category
dashboardDetails.changeCatButton.label = Change Category
#dashboardDetails.changeCatButton.tooltip = Change Category
dashboardDetails.changeCatButton.tooltip = Change Category

# ---------- Account Groups Maintenance -----------------
#acctGroupsMaintenance.title.window = Account Groups Maintenance - SMART-Predict
acctGroupsMaintenance.title.window = Account Groups Maintenance - SMART-Predict
#acctGroupsMaintenance.title.add = Account Groups Maintenance Add - SMART-Predict
acctGroupsMaintenance.title.add = Account Groups Maintenance Add - SMART-Predict
#acctGroupsMaintenance.title.change = Account Groups Maintenance Change - SMART-Predict
acctGroupsMaintenance.title.change = Account Groups Maintenance Change - SMART-Predict
#acctGroupsMaintenance.title.view = Account Groups Maintenance View - SMART-Predict
acctGroupsMaintenance.title.view = Account Groups Maintenance View - SMART-Predict
#acctGroupsMaintenanceDetails.label.accountGrpId = Account Group ID*
acctGroupsMaintenanceDetails.label.accountGrpId = Account Group ID*
#acctGroupsMaintenanceDetails.tootlip.accountGrpId = Enter a unique ID for the Account Group
acctGroupsMaintenanceDetails.tootlip.accountGrpId = Enter a unique ID for the Account Group
#acctGroupsMaintenanceDetails.label.accountGrpName = Account Group Name*
acctGroupsMaintenanceDetails.label.accountGrpName = Account Group Name*
#acctGroupsMaintenanceDetails.tootlip.accountGrpName = Enter a name to be associated with the Account Group
acctGroupsMaintenanceDetails.tootlip.accountGrpName = Enter a name to be associated with the Account Group
#acctGroupsMaintenanceDetails.label.currency = Currency*
acctGroupsMaintenanceDetails.label.currency = Currency*
#acctGroupsMaintenanceDetails.tootlip.currency = Enter Currency Code for Account Group
acctGroupsMaintenanceDetails.tootlip.currency = Enter Currency Code for Account Group
#acctGroupsMaintenanceDetails.label.processingOrder = Processing Order
acctGroupsMaintenanceDetails.label.processingOrder = Processing Order
#acctGroupsMaintenanceDetails.tootlip.currency = Enter the Processing Order of the Account Group within the Currency
acctGroupsMaintenanceDetails.tootlip.currency = Enter the Processing Order of the Account Group within the Currency

# ---------- PCThresholdAmounts --------------------
#thresholdAmounts.label.time = Time*
thresholdAmounts.label.time = Time*
#thresholdAmounts.label.reserve = Reserve*
thresholdAmounts.label.reserve = Reserve*
#thresholdAmounts.tootlip.reserve = Enter the Reserve amount
thresholdAmounts.tootlip.reserve = Enter the Reserve amount
#thresholdAmounts.label.creditLine = Use Credit Line
thresholdAmounts.label.creditLine = Use Credit Line

# ---------- PCSpreadingTab --------------------
#spreadingTab.label.spreadProfile = Spread Profile
spreadingTab.label.spreadProfile = Spread Profile
#spreadingTab.label.targetCalculation = Target Percent Calculation Method
spreadingTab.label.targetCalculation = Target Percent Calculation Method
#spreadingTab.tooltip.targetCalculation = Enter the method for percentage calculation (All or Outstanding)
spreadingTab.tooltip.targetCalculation = Enter the method for percentage calculation (All or Outstanding)


# ---------- PCLiquidityTab --------------------
#liquidityTab.alert.selectCurrency = Please select currency
liquidityTab.alert.selectCurrency = Please select currency
#liquidityTab.error.changingRow = error while changing row
liquidityTab.error.changingRow = error while changing row


# ---------- PCGeneralTab --------------------
#generalTab.label.quickCategory = Quick Category ID
generalTab.label.quickCategory = Quick Category ID
#generalTab.tooltip.quickCategory = Enter a quick Category to use for all accounts within this Account Group
generalTab.tooltip.quickCategory = Enter a quick Category to use for all accounts within this Account Group
#generalTab.label.defaultCategory = Default Category ID
generalTab.label.defaultCategory = Default Category ID
#generalTab.tooltip.defaultCategory =  Enter a default Category to use when no other Category can be determined
generalTab.tooltip.defaultCategory =  Enter a default Category to use when no other Category can be determined
#generalTab.label.countIDNotInGroup = Accounts Not Assigned to a Group
generalTab.label.countIDNotInGroup = Accounts Not Assigned to a Group
#generalTab.tooltip.countIDNotInGroup = Select Accounts to include in the Account Group
generalTab.tooltip.countIDNotInGroup = Select Accounts to include in the Account Group
#generalTab.tooltip.processingOrder  = Enter the Processing Order of the Account Group within the Currency
generalTab.tooltip.processingOrder  = Enter the Processing Order of the Account Group within the Currency
#generalTab.toolTip.buttonMoveRight = Moves selected account(s) into the Account Group
generalTab.toolTip.buttonMoveRight = Moves selected account(s) into the Account Group
#generalTab.toolTip.buttonMoveAllRight = Moves all unassigned accounts into the Account Group
generalTab.toolTip.buttonMoveAllRight = Moves all unassigned accounts into the Account Group
#generalTab.toolTip.buttonMoveLeft = Moves selected account(s) out of the Account Group
generalTab.toolTip.buttonMoveLeft = Moves selected account(s) out of the Account Group
#generalTab.toolTip.buttonMoveAllLeft = Moves all assigned accounts out of the Account Group
generalTab.toolTip.buttonMoveAllLeft = Moves all assigned accounts out of the Account Group
#generalTab.label.countIDInGroup = Accounts in this Account Group
generalTab.label.countIDInGroup = Accounts in this Account Group
#generalTab.tooltip.countIDInGroup  = Select Accounts to remove from this Account Group
generalTab.tooltip.countIDInGroup  = Select Accounts to remove from this Account Group
#generalTab.label.filterTextLeft = Quick Search
generalTab.label.filterTextLeft = Quick Search
#generalTab.tooltip.filterTextLeft  = Enter text to help find the account(s)
generalTab.tooltip.filterTextLeft  = Enter text to help find the account(s)
#generalTab.alert.orderZero = Order must be higher than 0
generalTab.alert.orderZero = Order must be higher than 0
#generalTab.alert.existOrder =This order already exists with the selected currency
generalTab.alert.existOrder =This order already exists with the selected currency
#generalTab.error.changingRow = error while changing row
generalTab.error.changingRow = error while changing row
#generalTab.error.movingLeft = error in Move left
generalTab.error.movingLeft = error in Move left

# ---------- Spread Profiles --------------------
#spreadProfiles.alert.UnableToDelete=Unable to delete, this spread profile is linked to an existing account group
spreadProfiles.alert.UnableToDelete=Unable to delete, this spread profile is linked to an existing account group
#spreadProfiles.alert.ConfirmDelete=Are you sure to delete selected spread profile ID ? : \n
spreadProfiles.alert.ConfirmDelete=Are you sure to delete selected spread profile ID ? : \n
#spreadProfilesAdd.title.processPointsPanel=Spread Process Points
spreadProfilesAdd.title.processPointsPanel=Spread Process Points
#spreadProfilesAdd.title.accountGroupsPanel=Associated Account Groups
spreadProfilesAdd.title.accountGroupsPanel=Associated Account Groups
#spreadProfilesAdd.label.spreadId=Spread Id
spreadProfilesAdd.label.spreadId=Spread Id
#spreadProfilesAdd.label.SpreadName=Spread Name
spreadProfilesAdd.label.SpreadName=Spread Name
#spreadProfilesAdd.label.currency=Currency
spreadProfilesAdd.label.currency=Currency
#spreadProfilesAdd.tooltip.spreadId=Spread Id
spreadProfilesAdd.tooltip.spreadId=Spread Id
#spreadProfilesAdd.tooltip.SpreadName=Spread Name
spreadProfilesAdd.tooltip.SpreadName=Spread Name
#spreadProfilesAdd.tooltip.currency=Currency
spreadProfilesAdd.tooltip.currency=Currency
#spreadProfilesAdd.alert.SpreadProfileWithSpaces=Spread Profile Name can not be saved as just spaces
spreadProfilesAdd.alert.SpreadProfileWithSpaces=Spread Profile Name can not be saved as just spaces
#spreadProfilesAdd.alert.SpreadProfileWithoutProcess=Cannot add a spread profile without any process point
spreadProfilesAdd.alert.SpreadProfileWithoutProcess=Cannot add a spread profile without any process point
#spreadProfilesAdd.alert.SpreadAlreadyExists=Unable to save, spread Profile Id already exists
spreadProfilesAdd.alert.SpreadAlreadyExists=Unable to save, spread Profile Id already exists
#spreadProfilesAdd.alert.TimeOutsideOfRange=Could not add a spread process point with time outside <br> kick-off and EOD begin phase for the following account groups : <br>
spreadProfilesAdd.alert.TimeOutsideOfRange=Could not add a spread process point with time outside <br> kick-off and EOD begin phase for the following account groups : <br>
#spreadDetails.add.title=Spread Process Points - Add
spreadDetails.add.title=Spread Process Points - Add
#spreadDetails.change.title=Spread Process Points - Change
spreadDetails.change.title=Spread Process Points - Change
#spreadDetails.view.title=Spread Process Points - View
spreadDetails.view.title=Spread Process Points - View
#spreadDetails.label.processName=Process Name
spreadDetails.label.processName=Process Name
#spreadDetails.label.time=Time
spreadDetails.label.time=Time
#spreadDetails.label.target=Target %
spreadDetails.label.target=Target %
#spreadDetails.label.processCategory=Process Category
spreadDetails.label.processCategory=Process Category
#spreadDetails.label.AllExceptSelected=Please select all categories which you do NOT wish to be processed
spreadDetails.label.AllExceptSelected=Please select all categories which you do NOT wish to be processed
#spreadDetails.label.onlySelected=Please select all categories which you wish to be processed
spreadDetails.label.onlySelected=Please select all categories which you wish to be processed
#spreadDetails.tooltip.processName=Process Name
spreadDetails.tooltip.processName=Process Name
#spreadDetails.tooltip.time=Time
spreadDetails.tooltip.time=Time
#spreadDetails.tooltip.target=Target %
spreadDetails.tooltip.target=Target %
#spreadDetails.tooltip.processCategory=Select Process Category
spreadDetails.tooltip.processCategory=Select Process Category
#spreadDetails.title.categoriesPanel=Categories
spreadDetails.title.categoriesPanel=Categories
#spreadDetails.alert.NameWithSpaces=Process Name can not be saved as just spaces
spreadDetails.alert.NameWithSpaces=Process Name can not be saved as just spaces
#spreadDetails.alert.OnlyWithnoCategorySelected=Based on selected process, you need to choose at least one category
spreadDetails.alert.OnlyWithnoCategorySelected=Based on selected process, you need to choose at least one category
#spreadDetails.alert.AllExceptWithAllCategoriesSelected=Based on selected process category, you need to exclude at least one category
spreadDetails.alert.AllExceptWithAllCategoriesSelected=Based on selected process category, you need to exclude at least one category
#spreadDetails.alert.sameTime=Cannot have two process points being defined with same time
spreadDetails.alert.sameTime=Cannot have two process points being defined with same time
#spreadDetails.alert.sameName=Cannot have two process points being defined with same name
spreadDetails.alert.sameName=Cannot have two process points being defined with same name

# ---------- PCM Report -------------------------
#pcmReport.label.repoortType=Report Type
pcmReport.label.repoortType=Report Type
#pcmReport.label.entity=Entity
pcmReport.label.entity=Entity
#pcmReport.label.currency=Currency
pcmReport.label.currency=Currency
#pcmReport.label.accountGroup=Account Group
pcmReport.label.accountGroup=Account Group
#pcmReport.label.source=Source
pcmReport.label.source=Source
#pcmReport.label.date=Date
pcmReport.label.date=Date
#pcmReport.label.date.singleDay=Single Day
pcmReport.label.date.singleDay=Single Day
#pcmReport.label.date.dateRange=Date Range
pcmReport.label.date.dateRange=Date Range
#pcmReport.label.useCcyMultiplier=Use Ccy Multiplier
pcmReport.label.useCcyMultiplier=Use Ccy Multiplier
#pcmReport.label.exportType=Export type
pcmReport.label.exportType=Export type
#pcmReport.tooltip.repoortType=Select Report Type
pcmReport.tooltip.repoortType=Select Report Type
#pcmReport.tooltip.entity=Select Entity
pcmReport.tooltip.entity=Select Entity
#pcmReport.tooltip.currency=Select Currency Code
pcmReport.tooltip.currency=Select Currency Code
#pcmReport.tooltip.accountGroup=Select Account Group
pcmReport.tooltip.accountGroup=Select Account Group
#pcmReport.tooltip.source=Select Source
pcmReport.tooltip.source=Select Source
#pcmReport.tooltip.date=Select Date
pcmReport.tooltip.date=Select Date
#pcmReport.tooltip.date.singleDay=Single Day
pcmReport.tooltip.date.singleDay=Single Day
#pcmReport.tooltip.date.dateRange=Date Range
pcmReport.tooltip.date.dateRange=Date Range
#pcmReport.tooltip.useCcyMultiplier=Use Ccy Multiplier
pcmReport.tooltip.useCcyMultiplier=Use Ccy Multiplier
#pcmReport.tooltip.exportType=Export type
pcmReport.tooltip.exportType=Export type

# ---------- CategoryList -------------------------
#categoryList.label.paymentCategory = Payment Category
categoryList.label.paymentCategory = Payment Category
#categoryList.tooltip.paymentCategory = Select payment category
categoryList.tooltip.paymentCategory = Select payment category


# ---------- ConfirmRelease -------------------------
#confirmRelease.label.paymentSelectedForRelease = payments are selected for release
confirmRelease.label.paymentSelectedForRelease = payments are selected for release
#confirmRelease.tooltip.waiting = Waiting
confirmRelease.tooltip.waiting = Waiting
#confirmRelease.label.stopped =Stopped
confirmRelease.label.stopped =Stopped
#confirmRelease.label.blocked = Blocked
confirmRelease.label.blocked = Blocked
#confirmRelease.label.backValued = Input as Back Valued
confirmRelease.label.backValued = Input as Back Valued
#confirmRelease.label.inputCutOff = Input after Cut-off
confirmRelease.label.inputCutOff = Input after Cut-off
#confirmRelease.label.stoppedCutOff = Stopped and cut-off passed
confirmRelease.label.stoppedCutOff = Stopped and cut-off passed
#confirmRelease.label.cutOffPassed = Cut-off passed
confirmRelease.label.cutOffPassed = Cut-off passed
#confirmRelease.label.blockedStopped = Blocked and Stopped
confirmRelease.label.blockedStopped = Blocked and Stopped
#confirmRelease.label.stoppedCutOff = Stopped and cut-off passed
confirmRelease.label.stoppedCutOff = Stopped and cut-off passed
#confirmRelease.label.cancelled =Cancelled (will not be released)
confirmRelease.label.cancelled =Cancelled (will not be released)
#confirmRelease.label.cancelled = Released already
confirmRelease.label.releasedAlready = Released already
#confirmRelease.buttonRelease.label = Release
confirmRelease.buttonRelease.label = Release

# ---------- ErrorsUpdatePayments -------------------------
#errorsUpdatePayments.releaseAllButton.label = Release All
errorsUpdatePayments.releaseAllButton.label = Release All
#errorsUpdatePayments.releaseButton.label = Release
errorsUpdatePayments.releaseButton.label = Release
#errorsUpdatePayments.UnStopButton.label = UnStopButton
errorsUpdatePayments.UnStopButton.label = UnStopButton


# ---------- ExportPages -------------------------
#exportPages.label.current = Current Page
exportPages.label.current = Current Page
#exportPages.label.allPages = All Pages
exportPages.label.allPages = All Pages

# ---------- FourEyesProcess -------------------------
#fourEyesProcess.label.userName = Approver ID*
fourEyesProcess.label.userName = Approver ID*
#fourEyesProcess.label.password = Password*
fourEyesProcess.label.password = Password*

# ---------- OptionsPopUp -------------------------
#optionsPopUp.label.refresh = Auto-refresh rate
optionsPopUp.label.refresh = Auto-refresh rate
#optionsPopUp.label.seconds = seconds
optionsPopUp.label.seconds = seconds

# ---------- PartySearch -------------------------
#partySearch.label.partyId = Party Id
partySearch.label.partyId = Party Id
#partySearch.tooltip.partyId = Party Id
partySearch.tooltip.partyId = Party Id
#partySearch.label.partyName = Party Name
partySearch.label.partyName = Party Name
#partySearch.tooltip.partyName = Party Name
partySearch.tooltip.partyName = Party Name
#partySearch.label.searchButton = Search
partySearch.label.searchButton = Search

# ---------- Payment Archive Search ----------------------

# ---------- Payment Request Display ---------------------

# ---------- Payment Request Log -------------------------

# ---------- Payment Request Message Summary -------------

# ---------- Payment Request Search ----------------------

# ---------- Payment Request Responses Summary -----------

# ---------- Payment Request Stop Rules Summary ----------

# ---------- PC Message Details --------------------------
