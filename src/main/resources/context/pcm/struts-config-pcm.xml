<?xml version="1.0" encoding="ISO-8859-1" ?>
<!DOCTYPE struts-config PUBLIC "-//Apache Software Foundation//DTD Struts Configuration 1.1//EN" "http://jakarta.apache.org/struts/dtds/struts-config_1_1.dtd">

<struts-config>

	<action-mappings>
		<action path="/paymentControl"
			type="org.springframework.web.struts.DelegatingActionProxy"
			scope="request" parameter="method" validate="false"
			input="/login.jsp">
			<forward name="fail" path="/error.jsp" />
			<forward name="success" path="/jsp/pc/maintenance/paymentcontrol.jsp" />
		</action>
		<action path="/accountGroupsPCM"
			type="org.springframework.web.struts.DelegatingActionProxy"
			scope="request" parameter="method" validate="false"
			input="/login.jsp">
			<forward name="fail" path="/error.jsp" />
			<forward name="success" path="/jsp/pc/maintenance/accountgroups.jsp" />
			<forward name="addScreen" path="/jsp/pc/maintenance/accountgroupsadd.jsp" />
			<forward name="expressionBuilder" path="/jsp/pc/maintenance/expressionbuilder.jsp" />
			<forward name="data" path="/jsp/data.jsp" />
			<forward name="statechange" path="/jsp/flexstatechange.jsp" />
		</action>
		<action path="/currencyPCM"
			type="org.springframework.web.struts.DelegatingActionProxy"
			scope="request" parameter="method" validate="false"
			input="/login.jsp">
			<forward name="fail" path="/error.jsp" />
			<forward name="success" path="/jsp/pc/maintenance/currencymaintenance.jsp" />
			<forward name="data"  path="/jsp/data.jsp" />	
			<forward name="statechange" path="/jsp/flexstatechange.jsp" />
		</action>
		<action path="/expressionBuilderPCM"
			type="org.springframework.web.struts.DelegatingActionProxy"
			scope="request" parameter="method" validate="false"
			input="/login.jsp">
			<forward name="fail" path="/error.jsp" />
			<forward name="success" path="/jsp/pc/maintenance/expressionbuilder.jsp" />
			<forward name="data" path="/jsp/data.jsp" />
			<forward name="statechange" path="/jsp/flexstatechange.jsp" />
		</action>
		<action path="/categoryRulesPCM"
			type="org.springframework.web.struts.DelegatingActionProxy"
			scope="request" parameter="method" validate="false"
			input="/login.jsp">
			<forward name="fail" path="/error.jsp" />
			<forward name="add" path="/jsp/pc/maintenance/categoryruleadd.jsp" />
			<forward name="data"  path="/jsp/data.jsp" />
			<forward name="statechange" path="/jsp/flexstatechange.jsp" />
		</action>
		<action path="/categoryPCM"
			type="org.springframework.web.struts.DelegatingActionProxy"
			scope="request" parameter="method" validate="false"
			input="/login.jsp">
			<forward name="fail" path="/error.jsp" />
			<forward name="success" path="/jsp/pc/maintenance/categorymaintenance.jsp" />
			<forward name="add" path="/jsp/pc/maintenance/categorymaintenanceadd.jsp" />
			<forward name="data"  path="/jsp/data.jsp" />
			<forward name="expressionBuilder" path="/jsp/pc/maintenance/expressionbuilder.jsp" />
			<forward name="statechange" path="/jsp/flexstatechange.jsp" />
		</action>
		<action path="/spreadProfilesPCM"
			type="org.springframework.web.struts.DelegatingActionProxy"
			scope="request" parameter="method" validate="false"
			input="/login.jsp">
			<forward name="fail" path="/error.jsp" />
			<forward name="success" path="/jsp/pc/maintenance/spreadprofiles.jsp" />
			<forward name="add" path="/jsp/pc/maintenance/spreadprofilesadd.jsp" />
			<forward name="data"  path="/jsp/data.jsp" />
			<forward name="statechange" path="/jsp/flexstatechange.jsp" />
		</action>
		<action path="/stopRulesPCM"
			type="org.springframework.web.struts.DelegatingActionProxy"
			scope="request" parameter="method" validate="false"
			input="/login.jsp">
			<forward name="fail" path="/error.jsp" />
			<forward name="success" path="/jsp/pc/maintenance/stoprulemaintenance.jsp" />
			<forward name="add" path="/jsp/pc/maintenance/stoprulemaintenanceadd.jsp" />
			<forward name="data" path="/jsp/data.jsp" />
			<forward name="expressionBuilder" path="/jsp/pc/maintenance/expressionbuilder.jsp" />
			<forward name="statechange" path="/jsp/flexstatechange.jsp" />
			
		</action>
		<action path="/fourEyes"
			type="org.springframework.web.struts.DelegatingActionProxy"
			scope="request" parameter="method" validate="false"
			input="/login.jsp">
			<forward name="fail" path="/error.jsp" />
			<forward name="data" path="/jsp/data.jsp" />
			<forward name="statechange" path="/jsp/flexstatechange.jsp" />
		</action>
		<action path="/paymentDisplayPCM"
			type="org.springframework.web.struts.DelegatingActionProxy"
			scope="request" parameter="method" validate="false"
			input="/login.jsp">
			<forward name="fail" path="/error.jsp" />
			<forward name="success" path="/jsp/pc/work/paymentdisplay.jsp" />
			<forward name="messageOut" path="/jsp/pc/work/messageOut.jsp" />
			<forward name="messageList" path="/jsp/pc/work/messageList.jsp" />
			<forward name="stopRulesList" path="/jsp/pc/work/stopRulesList.jsp" />
			<forward name="logs" path="/jsp/pc/work/logs.jsp" />
			<forward name="data" path="/jsp/data.jsp" />
			<forward name="statechange" path="/jsp/flexstatechange.jsp" />
			<forward name="paymentDisplay" path="/jsp/pc/work/paymentdisplay.jsp" />
		</action>
		<action path="/paymentSearchPCM"
			type="org.springframework.web.struts.DelegatingActionProxy"
			scope="request" parameter="method" validate="false"
			input="/login.jsp">
			<forward name="fail" path="/error.jsp" />
			<forward name="success" path="/jsp/pc/work/paymentsearch.jsp" />
			<forward name="displayArchive" path="/jsp/pc/work/paymentarchivesearch.jsp" />
			<forward name="partysearch" path="/jsp/pc/work/partysearch.jsp" />
			<forward name="data" path="/jsp/data.jsp" />
			<forward name="statechange" path="/jsp/flexstatechange.jsp" />
		</action>
		<action path="/dashboardPCM"
			type="org.springframework.web.struts.DelegatingActionProxy"
			scope="request" parameter="method" validate="false"
			input="/login.jsp">
			<forward name="success" path="/jsp/pc/monitor/dashboardPCM.jsp" />
			<forward name="add" path="/jsp/pc/monitor/dashboarddetailsPCM.jsp" />
			<forward name="fail" path="/error.jsp" />
			<forward name="data" path="/jsp/data.jsp" />
			<forward name="statechange" path="/jsp/flexstatechange.jsp" />
			<forward name="successDetails" path="/jsp/pc/monitor/dashboarddetailsPCM.jsp" />
			<forward name="spreadDisplay" path="/jsp/pc/maintenance/spreadprofilesadd.jsp" />
		</action>
		<action path="/reportPCM"
			type="org.springframework.web.struts.DelegatingActionProxy"
			scope="request" parameter="method" validate="false"
			input="/login.jsp">
			<forward name="fail" path="/error.jsp" />
			<forward name="success" path="/jsp/pc/report/report.jsp" />
			<forward name="data" path="/jsp/data.jsp" />
			<forward name="statechange" path="/jsp/flexstatechange.jsp" />
		</action>
	</action-mappings>
	
	<message-resources parameter="dictionary" />
</struts-config>