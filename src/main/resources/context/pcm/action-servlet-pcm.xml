<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE beans PUBLIC "-//SPRING//DTD BEAN//EN"
    "http://www.springframework.org/dtd/spring-beans.dtd">

<beans>
	<bean name="/paymentControl" class="org.swallow.pcm.maintenance.web.PaymentControlAction"
		singleton="false">
		<property name="paymentControlManager">
			<ref bean="paymentControlManager" />
		</property>
	</bean>
	<bean name="/accountGroupsPCM" class="org.swallow.pcm.maintenance.web.AccountGroupsMaintenanceAction"
		singleton="false">
		<property name="accountGroupsMaintenanceManager">
			<ref bean="accountGroupsMaintenanceManager" />
		</property>
	</bean>
	<bean name="/currencyPCM" class="org.swallow.pcm.maintenance.web.CurrencyMaintenanceAction"
		singleton="false">
		<property name="currencyMaintenanceManager">
			<ref bean="currencyMaintenanceManager" />
		</property>
	</bean>
	<bean name="/expressionBuilderPCM" class="org.swallow.pcm.maintenance.web.ExpressionBuilderAction"
		singleton="false">
		<property name="expressionBuilderManager">
			<ref bean="expressionBuilderManager" />
		</property>
	</bean>
	<bean name="/categoryRulesPCM" class="org.swallow.pcm.maintenance.web.CategoryRulesMaintenanceAction"
		singleton="false">
		<property name="categoryRulesMaintenanceManager">
			<ref bean="categoryRulesMaintenanceManager" />
		</property>
	</bean>
	<bean name="/categoryPCM" class="org.swallow.pcm.maintenance.web.CategoryMaintenanceAction"
		singleton="false">
		<property name="categoryMaintenanceManager">
			<ref bean="categoryMaintenanceManager" />
		</property>
	</bean>
	<bean name="/spreadProfilesPCM" class="org.swallow.pcm.maintenance.web.SpreadProfilesMaintenanceAction"
		singleton="false">
		<property name="spreadProfilesMaintenanceManager">
			<ref bean="spreadProfilesMaintenanceManager" />
		</property>
	</bean>
	<bean name="/stopRulesPCM" class="org.swallow.pcm.maintenance.web.StopRulesMaintenanceAction"
		singleton="false">
		<property name="stopRulesMaintenanceManager">
			<ref bean="stopRulesMaintenanceManager" />
		</property>
	</bean>
	<bean name="/fourEyes" class="org.swallow.pcm.maintenance.web.FourEyesProcessAction"
		singleton="false">
	</bean>
	<bean name="/paymentDisplayPCM" class="org.swallow.pcm.work.web.PaymentDisplayAction"
		singleton="false">
		<property name="paymentDisplayManager">
			<ref bean="paymentDisplayManager" />
		</property>
	</bean>
	<bean name="/paymentSearchPCM" class="org.swallow.pcm.work.web.PaymentSearchAction"
		singleton="false">
		<property name="paymentSearchManager">
			<ref bean="paymentSearchManager" />
		</property>
	</bean>
	<bean name="/dashboardPCM" class="org.swallow.pcm.work.web.DashboardAction"
		singleton="false">
		<property name="dashboardManager">
			<ref bean="dashboardManager" />
		</property>
	</bean>
	<bean name="/reportPCM" class="org.swallow.pcm.report.web.ReportAction"
		singleton="false">
		<property name="reportManager">
			<ref bean="reportManager" />
		</property>
	</bean>
</beans>