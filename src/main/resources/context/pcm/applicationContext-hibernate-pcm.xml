<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE beans PUBLIC "-//SPRING//DTD BEAN//EN"
   "http://www.springframework.org/dtd/spring-beans.dtd">
<beans>


<!-- Configuration for encryptor, extended from SimplePBEConfig -->
	<bean id="SwtPBEConfiguration"
		class="org.swallow.security.SwtPBEConfig">
		<property name="algorithm">
			<value>PBEWithMD5AndDES</value>
		</property>
	</bean>

	<!-- This will be the encryptor used for decrypting configuration values -->
	<bean id="configurationEncryptor"
		class="org.jasypt.encryption.pbe.StandardPBEStringEncryptor">
		<property name="config">
			<ref local="SwtPBEConfiguration"/>
		</property>
	</bean>
	<!-- The EncryptablePropertyPlaceholderConfigurer will read the .properties files and make their values accessible as ${var} -->
	<!-- Our "configurationEncryptor" bean (which implements org.jasypt.encryption.StringEncryptor) is set as a constructor arg. -->
	<bean id="propertyConfigurerPC"
		class="org.jasypt.spring.properties.EncryptablePropertyPlaceholderConfigurer">
		<constructor-arg>
			<ref bean="configurationEncryptor"/>
		</constructor-arg>
		<property name="location">
			<!-- Here define the full path for your connection.properties file, remember it will be used outside Predict project -->
			<!-- NOTE: On Windows, use / instead of \ (which is an escape character) as the separator -->
			<value>connection-pcm.properties</value>
		</property>
	
<property name="systemPropertiesMode">
			<!-- Check system properties first, before trying the specified properties. Docker will read properties as Env variables -->
			<value>2</value>
		</property>
	</bean>

	<bean id="dataSource-pc"
		class="org.swallow.security.SwtSecureBasicDataSource"
		destroy-method="close">
		<property name="encryptor"><ref bean="configurationEncryptor"/></property>
		<property name="driverClassName"><value>${pcm.datasource.driver}</value></property>
		<property name="url"><value>${pcm.datasource.url}</value></property>
		<property name="username"><value>${pcm.datasource.username}</value></property>
		<property name="password"><value>${pcm.datasource.password}</value></property>
		<property name="maxActive"><value>100</value></property>
		<property name="maxIdle"><value>25</value></property>
		<property name="maxWait"><value>10000</value></property>
		<property name="defaultAutoCommit"><value>false</value></property>
		<property name="removeAbandoned"><value>true</value></property>
		<property name="removeAbandonedTimeout"><value>60</value></property>
		<property name="logAbandoned"><value>true</value></property>
		<property name="validationQuery">
			<value>select 1 from dual</value>
		</property>
	</bean>

	<bean id="dataSource2" class="org.swallow.pcm.util.SwtPCDataSource">
		<property name="pcDataSource">
		   <ref bean="dataSource-pc"/>
		</property>
	</bean>

	<bean id="sessionFactoryPC"
		class="org.springframework.orm.hibernate.LocalSessionFactoryBean">
		<property name="dataSource">
			<ref bean="dataSource2"/>
		</property>
		<property name="mappingResources">
			<list>
				<!-- PCM context -->
				<value>org/swallow/pcm/maintenance/model/AccountGroup.hbm.xml</value>
				<value>org/swallow/pcm/maintenance/model/AccountInGroup.hbm.xml</value>
				<value>org/swallow/pcm/maintenance/model/PCMCurrency.hbm.xml</value>
				<value>org/swallow/pcm/maintenance/model/Category.hbm.xml</value>
				<value>org/swallow/pcm/maintenance/model/CategoryRule.hbm.xml</value>
				<value>org/swallow/pcm/maintenance/model/SpreadProfile.hbm.xml</value>
				<value>org/swallow/pcm/maintenance/model/SpreadProcessPoint.hbm.xml</value>
				<value>org/swallow/pcm/maintenance/model/ProcessPointCategory.hbm.xml</value>
				<value>org/swallow/pcm/maintenance/model/Reserve.hbm.xml</value>
				<value>org/swallow/pcm/maintenance/model/AccountGroupCutoff.hbm.xml</value>
				<value>org/swallow/pcm/maintenance/model/StopRule.hbm.xml</value>
				<value>org/swallow/pcm/work/model/MessagesOutput.hbm.xml</value>
				<value>org/swallow/pcm/work/model/PaymentStop.hbm.xml</value>
				<value>org/swallow/pcm/work/model/PaymentRequest.hbm.xml</value>
				<value>org/swallow/pcm/work/model/PaymentRequestInfo.hbm.xml</value>
				<value>org/swallow/pcm/work/model/PaymentLog.hbm.xml</value>
				<value>org/swallow/pcm/work/model/PaymentMessage.hbm.xml</value>
				<value>org/swallow/pcm/work/model/MessageArchivePCM.hbm.xml</value>
				<value>org/swallow/pcm/control/model/PCNotifications.hbm.xml</value>
				<value>org/swallow/pcm/control/model/PCInterfaceInterrupt.hbm.xml</value>
				
				<!-- Synonyms -->
				<!-- Predict context equivalent -->
				<value>org/swallow/maintenance/model/AcctMaintenance.hbm.xml</value>
				<value>org/swallow/maintenance/model/Currency.hbm.xml</value>
				<value>org/swallow/maintenance/model/CurrencyMaster.hbm.xml</value>
				<value>org/swallow/maintenance/model/Country.hbm.xml</value>
 				<value>org/swallow/maintenance/model/Entity.hbm.xml</value>
 				<value>org/swallow/maintenance/model/InterfaceRules.hbm.xml</value>
 				<value>org/swallow/maintenance/model/SysParams.hbm.xml</value>
				
				<value>org/swallow/pcm/maintenance/model/Dictionary.hbm.xml</value>
 				<value>org/swallow/pcm/maintenance/model/TypeValues.hbm.xml</value> 
 				<value>org/swallow/pcm/maintenance/model/RulesDefinition.hbm.xml</value> 
 				<value>org/swallow/pcm/maintenance/model/RuleConditions.hbm.xml</value> 
 				<value>org/swallow/pcm/maintenance/model/ListTypes.hbm.xml</value>
				<value>org/swallow/work/model/InputExceptions.hbm.xml</value>
				
				<value>org/swallow/control/model/InputInterface.hbm.xml</value>
				<value>org/swallow/control/model/InputInterfaceProperty.hbm.xml</value>
				<value>org/swallow/control/model/InputInterfaceSBeanProperty.hbm.xml</value>
				<value>org/swallow/control/model/InterfaceInterrupt.hbm.xml</value>
				<value>org/swallow/model/ScreenInfo.hbm.xml</value>
				<!-- <value>org/swallow/control/model/InterfaceMonitor.hbm.xml</value> -->
				
			</list>
		</property>

		<property name="hibernateProperties">
			<props>
				<prop key="hibernate.dialect">net.sf.hibernate.dialect.Oracle9Dialect</prop>
				<prop key="hibernate.generate_statistics">false</prop>
				<prop key="hibernate.show_sql">false</prop>
				<prop key="hibernate.format_sql">true</prop>
				<prop key="hibernate.use_sql_comments">true</prop>
				<prop key="hibernate.synonyms">true</prop>
			</props>
		</property>
	</bean>

	<bean id="SwtInterceptor"
	   class="org.swallow.util.SwtInterceptor" singleton="false">
		<property name="sessionFactory"><ref bean="sessionFactoryPC"/></property>
	</bean>
	
	<bean id="transactionManager"
		class="org.springframework.orm.hibernate.HibernateTransactionManager">
		<property name="sessionFactory"><ref bean="sessionFactoryPC"/></property>
		<property name="entityInterceptor"><ref local="SwtInterceptor"/></property>
	</bean>
	
	<bean id="paymentControlDAO"
		class="org.swallow.pcm.maintenance.dao.hibernate.PaymentControlDAOHibernate">
		<property name="sessionFactory"><ref bean="sessionFactoryPC"/></property>
	</bean>
	
	<bean id="accountGroupsMaintenanceDAO"
		class="org.swallow.pcm.maintenance.dao.hibernate.AccountGroupsMaintenanceDAOHibernate">
		<property name="sessionFactory"><ref bean="sessionFactoryPC"/></property>
	</bean>
	
	<bean id="currencyMaintenanceDAO"
		class="org.swallow.pcm.maintenance.dao.hibernate.CurrencyMaintenanceDAOHibernate">
		<property name="sessionFactory"><ref bean="sessionFactoryPC"/></property>
	</bean>
	
	<bean id="expressionBuilderDAO"
		class="org.swallow.pcm.maintenance.dao.hibernate.ExpressionBuilderDAOHibernate">
		<property name="sessionFactory"><ref bean="sessionFactoryPC"/></property>
	</bean>
	
	<bean id="categoryRulesMaintenanceDAO"
		class="org.swallow.pcm.maintenance.dao.hibernate.CategoryRulesMaintenanceDAOHibernate">
		<property name="sessionFactory"><ref bean="sessionFactoryPC"/></property>
	</bean>
	
	<bean id="categoryMaintenanceDAO"
		class="org.swallow.pcm.maintenance.dao.hibernate.CategoryMaintenanceDAOHibernate">
		<property name="sessionFactory"><ref bean="sessionFactoryPC"/></property>
	</bean>
	
	<bean id="spreadProfilesMaintenanceDAO"
		class="org.swallow.pcm.maintenance.dao.hibernate.SpreadProfilesMaintenanceDAOHibernate">
		<property name="sessionFactory"><ref bean="sessionFactoryPC"/></property>
	</bean>
	
	<bean id="stopRulesMaintenanceDAO"
		class="org.swallow.pcm.maintenance.dao.hibernate.StopRulesMaintenanceDAOHibernate">
		<property name="sessionFactory"><ref bean="sessionFactoryPC"/></property>
	</bean>
	
	<!-- Input Exceptions -->
	<bean id="inputExceptionsDataDAOPCM"
		class="org.swallow.work.dao.hibernate.InputExceptionsDataDAOHibernate">
		<property name="sessionFactory"><ref bean="sessionFactoryPC"/></property>
	</bean>
	
	<bean id="inputExceptionsMessagesDAOPCM"
		class="org.swallow.work.dao.hibernate.InputExceptionsMessagesDAOHibernate">
		<property name="sessionFactory"><ref bean="sessionFactoryPC"/></property>
	</bean>
	
	<!-- Input Configuration -->
	<bean id="inputConfigurationDAOPCM"
		class="org.swallow.control.dao.hibernate.InputConfigurationDAOHibernate">
		<property name="sessionFactory"><ref bean="sessionFactoryPC"/></property>
	</bean>
	
	<!-- Interface Monitor -->
	<bean id="interfaceMonitorDAOPCM"
		class="org.swallow.control.dao.soap.InterfaceMonitorDAOSoap">
		<property name="sessionFactory"><ref bean="sessionFactoryPC"/></property>
	</bean>
	
	<!-- Interface Exceptions -->
	<bean id="interfaceExceptionsDAOPCM"
		class="org.swallow.control.dao.hibernate.InterfaceExceptionsDAOHibernate">
		<property name="sessionFactory"><ref bean="sessionFactoryPC"/></property>
	</bean>
	
	<bean id="paymentDisplayDAO"
		class="org.swallow.pcm.work.dao.hibernate.PaymentDisplayDAOHibernate">
		<property name="sessionFactory"><ref bean="sessionFactoryPC"/></property>
	</bean>
	<bean id="paymentSearchDAO"
		class="org.swallow.pcm.work.dao.hibernate.PaymentSearchDAOHibernate">
		<property name="sessionFactory"><ref bean="sessionFactoryPC"/></property>
	</bean>
	<bean id="dashboardDAO"
		class="org.swallow.pcm.work.dao.hibernate.DashboardDAOHibernate">
		<property name="sessionFactory"><ref bean="sessionFactoryPC"/></property>
	</bean>
	<bean id="archiveDAOPCM"
		class="org.swallow.control.dao.hibernate.ArchiveDAOHibernate">
		<property name="sessionFactory"><ref bean="sessionFactoryPC"/></property>
	</bean>
	<bean id="interfaceInterruptDAOPCM"
		class="org.swallow.control.dao.hibernate.InterfaceInterruptDAOHibernate">
		<property name="sessionFactory"><ref bean="sessionFactoryPC"/></property>
	</bean>
	<bean id="reportDAO"
		class="org.swallow.pcm.report.dao.hibernate.ReportDAOHibernate">
		<property name="sessionFactory"><ref bean="sessionFactoryPC"/></property>
	</bean>
</beans>