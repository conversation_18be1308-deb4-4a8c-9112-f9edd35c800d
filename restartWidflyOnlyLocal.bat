@echo off

REM Run Maven clean, compile, and package
call mvn clean compile package


REM Check if Maven build was successful

REM Define SSH connection details
set HOST=**************
set USER=root
set PASSWORD=Efgh1234

REM Copy the file to the remote server


xcopy /y target\swallowtech.war  C:\Wildfly-26\standalone\deployments

cd C:\Wildfly-26\
JBoss_Launcher.bat
REM SSH commands to stop and start WildFly
REM sshpass -p %PASSWORD% ssh %USER%@%HOST% "/home/<USER>/Wildfly-node1/bin/wildfly.sh stop --cluster"
REM sshpass -p %PASSWORD% ssh %USER%@%HOST% "/home/<USER>/Wildfly-node1/bin/wildfly.sh start --cluster"


echo File upload complete.