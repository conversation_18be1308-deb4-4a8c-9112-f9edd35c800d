@echo off


REM Check if Maven build was successful

REM Define SSH connection details
set HOST=**************
set USER=root
set PASSWORD=Efgh1234

REM Copy the file to the remote server





REM SSH commands to stop and start WildFly
REM sshpass -p %PASSWORD% ssh %USER%@%HOST% "/home/<USER>/Wildfly-node1/bin/wildfly.sh stop --cluster"
REM sshpass -p %PASSWORD% ssh %USER%@%HOST% "/home/<USER>/Wildfly-node1/bin/wildfly.sh start --cluster"

plink -batch -ssh root@************** -pw Efgh1234 -m commandClusterFull_ING.txt


echo File upload complete.