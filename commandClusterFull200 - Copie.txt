kill -9 $(lsof -t -i:2181 -sTCP:LISTEN)
kill -9 $(lsof -t -i:28443 -sTCP:LISTEN)
kill -9 $(lsof -t -i:38443 -sTCP:LISTEN)
kill -9 $(lsof -t -i:18443 -sTCP:LISTEN)
kill -9 $(lsof -t -i:11150 -sTCP:LISTEN)


unzip /home/<USER>/Wildfly-node1/standalone/deployments/swallowtech.war -d /home/<USER>/Wildfly-node1/standalone/deployments/swallowtech
rm /home/<USER>/Wildfly-node1/standalone/deployments/swallowtech.war
mv /home/<USER>/Wildfly-node1/standalone/deployments/swallowtech /home/<USER>/Wildfly-node1/standalone/deployments/swallowtech.war


unzip /home/<USER>/Wildfly-node2/standalone/deployments/swallowtech.war -d /home/<USER>/Wildfly-node2/standalone/deployments/swallowtech
rm /home/<USER>/Wildfly-node2/standalone/deployments/swallowtech.war
mv /home/<USER>/Wildfly-node2/standalone/deployments/swallowtech /home/<USER>/Wildfly-node2/standalone/deployments/swallowtech.war


cd  /home/<USER>/apache-zookeeper-3.6.2-bin/bin
./zkServer.sh stop
./zkServer.sh start
cd /home/<USER>/Clustermanager_1.0.4
./clustermanager.sh stop
./clustermanager.sh start
cd /home/<USER>/Wildfly-proxy/bin
echo "yes" | ./wildfly.sh stop --proxy
./wildfly.sh start --proxy
cd /home/<USER>/Wildfly-node1/bin 
echo "yes" | ./wildfly.sh stop --cluster
./wildfly.sh start --cluster
cd /home/<USER>/Wildfly-node2/bin 
echo "yes" | ./wildfly.sh stop --cluster
./wildfly.sh start --cluster



