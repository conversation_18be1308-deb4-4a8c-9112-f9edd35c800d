import {Component, ElementRef, NgModule, OnDestroy, OnInit, ViewChild} from '@angular/core';
import {
  CommonService,
  HTTPComms,
  JSONReader,
  SwtLoadingImage,
  SwtComboBox,
  SwtLabel,
  SwtModule,
  SwtButton,
  SwtAlert,
  SwtTabNavigator,
  Tab,
  ExternalInterface,
  SwtUtil,
  SwtRadioItem,
  SwtRadioButtonGroup,
  SwtDateField,
  SwtCheckBox,
  SwtPopUpManager,
  TitleWindow,
  AdvancedDataGrid,
  AdvancedDataGridRow, genericEvent, SwtToolBoxModule, CommonUtil, SwtDataExport, ExportEvent

} from "swt-tool-box";
declare var instanceElement: any;
import moment from "moment";
import {ListValues} from "../ListValues/ListValues";
import {OptionsPopUp} from "../OptionsPopUp/OptionsPopUp";
import {RouterModule, Routes} from "@angular/router";
import {ModuleWithProviders} from "@angular/compiler/src/core";

@Component({
  selector: 'app-pcdashboard2',
  templateUrl: './PCMMonitor.html',
  styleUrls: ['./PCMMonitor.css']
})
export class PCMMonitor extends  SwtModule implements  OnInit, OnDestroy{


  /*********AdvancedDataGrid*********/
  @ViewChild('advancedDataGrid') advancedGrid: AdvancedDataGrid;
  /*********SwtTabNavigator*********/
  @ViewChild("tabs") tabs: SwtTabNavigator;

  /*********Tab*********/
  @ViewChild("displayContainerToday") displayContainerToday: Tab;
  @ViewChild("displayContainerTodayPlus") displayContainerTodayPlus: Tab;
  @ViewChild("displayContainerTodayPlusPlus") displayContainerTodayPlusPlus: Tab;
  @ViewChild("displayContainerTodayPlusThree") displayContainerTodayPlusThree: Tab;
  @ViewChild("displayContainerTodayPlusFour") displayContainerTodayPlusFour: Tab;
  @ViewChild("displayContainerTodayPlusFive") displayContainerTodayPlusFive: Tab;
  @ViewChild("displayContainerTodayPlusSix") displayContainerTodayPlusSix: Tab;
  @ViewChild("displayContainerTodayPlusSeven") displayContainerTodayPlusSeven: Tab;
  @ViewChild("displayContainerSelected") displayContainerSelected: Tab;

  /*********SwtLoadingImage*********/
  @ViewChild("loadingImage") loadingImage: SwtLoadingImage;

  /*********SwtComboBox*********/
  @ViewChild('entityCombo') entityCombo: SwtComboBox;

  /*********SwtRadioButtonGroup*************/
  @ViewChild('displayValueGroup') displayValueGroup: SwtRadioButtonGroup;
  /*********SwtRadioItem*************/
  @ViewChild('value') value: SwtRadioItem;
  @ViewChild('volume') volume: SwtRadioItem;

  /*********SwtLabel*********/
  @ViewChild('dateLabel') dateLabel: SwtLabel;
  @ViewChild('entityLabel') entityLabel: SwtLabel;
  @ViewChild('selectedEntity') selectedEntity: SwtLabel;
  @ViewChild('applyThresholdLabel') applyThresholdLabel: SwtLabel;
  @ViewChild('applyMultiplierLabel') applyMultiplierLabel: SwtLabel;
  @ViewChild('spreadLabel') spreadLabel: SwtLabel;
  @ViewChild('valueVolume') valueVolume: SwtLabel;

  /*********SwtDateField*********/
  @ViewChild('startDate') startDate: SwtDateField;

  /*********SwtText*********/
  @ViewChild('lastRef') lastRef: SwtLabel;
  @ViewChild('lastRefTime') lastRefTime: SwtLabel;

  /*********SwtCheckBox*********/
  @ViewChild('applyThresholdCheck') applyThresholdCheck: SwtCheckBox;
  @ViewChild('applyMultiplierCheck') applyMultiplierCheck: SwtCheckBox;
  @ViewChild('spreadCheck') spreadCheck: SwtCheckBox;

  /*********SwtButton*********/
  @ViewChild('rateButton') rateButton: SwtButton;
  @ViewChild('closeButton') closeButton: SwtButton;
  @ViewChild('refreshButton') refreshButton: SwtButton;
  @ViewChild('divColor') divColor: ElementRef;
  @ViewChild('entityMoreItemsButton') entityMoreItemsButton: SwtButton;
  @ViewChild('printIcon') printIcon: SwtButton;
  /*********SwtDataExport*********/
  @ViewChild('dataExport') dataExport: SwtDataExport;


  /**
   * Communication Objects
   **/
  private inputData = new HTTPComms(this.commonService);
  private rateData = new HTTPComms(this.commonService);
  private faultData = new HTTPComms(this.commonService);

  private baseURL= SwtUtil.getBaseURL();
  private actionMethod = '';
  private actionPath = '';
  public  requestParams = [];
  public selectedItemsListEntity: string = null;
  public selectedItemsList: string = null;
  private selectedTabId =0;
  private states: any = {};
  private selectedItem = null;
  private selectedRow = null;
  private verticalPosition = 0;
  /**
   * Timer Objects
   **/
    // Main Timer.
  private refreshRate = 30;
  private refreshRateStr = null;

  /**
   * Logic Objects
   **/
  private tabIsChanged = false;

  private jsonReader: JSONReader = new JSONReader();
  private lastRecievedJSON;
  private prevRecievedJSON;


  private swtAlert: SwtAlert;
  private win: TitleWindow;

  public sysDateFrmSession: string;
  public selectedDate: string;
  private interval = null;
  private moduleId="";
  private errorLocation = 0;
  private status;
  private entity: string= null;
  private level1;
  private level2;
  private level3;
  private currencyFormat = "";
  private dateFormat = "";
  public dateFormatUpper = "";
  private systemStatus = "";
  private valueVol;
  private spreadOnly;
  private ccyThreshold;
  private ccyMultiplier;
  /* - START -- Screen Name and Version Number ---- */
  private moduleName = 'PCM Dashboard Monitor';
  private versionNumber = '1.00.00';
  private releaseDate = '20 May 2019';
  /* - END -- Screen Name and Version Number ---- */


  private  menuAccess = 2;
  private inputSince: string;
  private isBackValueClicked : boolean = false;

  constructor(private commonService: CommonService, private element: ElementRef) {
    super(element,commonService);
    this.swtAlert = new SwtAlert(commonService);
  }


  ngOnDestroy(): any {
    instanceElement = null;
  }

  ngOnInit(): void {
    instanceElement = this;
    // Assining properties for controls
    this.entityLabel.text = SwtUtil.getPredictMessage('label.entity', null);
    this.entityCombo.toolTip = SwtUtil.getPredictMessage('dashboard.entity.tooltip', null);
    this.entityMoreItemsButton.label = SwtUtil.getPredictMessage('label.moreItems', null);
    this.dateLabel.text = SwtUtil.getPredictMessage('label.valueDate', null);
    this.applyThresholdCheck.toolTip = SwtUtil.getPredictMessage('dashboard.currencyThreshold.tooltip', null);
    this.applyThresholdLabel.text = SwtUtil.getPredictMessage('dashboard.currencyThreshold.label', null);
    this.applyMultiplierCheck.toolTip = SwtUtil.getPredictMessage('dashboard.currencyMultiplier.tooltip', null);
    this.applyMultiplierLabel.text = SwtUtil.getPredictMessage('dashboard.currencyMultiplier.label', null);
    this.spreadCheck.toolTip = SwtUtil.getPredictMessage('dashboard.spreadOnly.tooltip', null);
    this.spreadLabel.text = SwtUtil.getPredictMessage('dashboard.spreadOnly.label', null);
    this.displayValueGroup.toolTip = SwtUtil.getPredictMessage('dashboard.displayValue.tooltip', null);
    this.valueVolume.text = SwtUtil.getPredictMessage('dashboard.displayValue.label', null);
    this.value.label = SwtUtil.getPredictMessage('dashboard.radioValue.label', null);
    this.volume.label = SwtUtil.getPredictMessage('dashboard.radioVolume.label', null);
    this.refreshButton.label = SwtUtil.getPredictMessage('button.refresh', null);
    this.refreshButton.toolTip = SwtUtil.getPredictMessage('tooltip.refresh', null);
    this.rateButton.label = SwtUtil.getPredictMessage('button.rate', null);
    this.rateButton.toolTip = SwtUtil.getPredictMessage('tooltip.rate', null);
    this.closeButton.label = SwtUtil.getPredictMessage('button.close', null);
    this.closeButton.toolTip = SwtUtil.getPredictMessage('tooltip.close', null);
    this.lastRef.text = SwtUtil.getPredictMessage('dashboard.lastRefresh.label', null);
    this.printIcon.toolTip = SwtUtil.getPredictMessage('tooltip.print', null);
    this.printIcon.enabled= true;
    this.entityMoreItemsButton.enabled = false;
    this.refreshButton.setFocus();
    ExportEvent.subscribe((type) => {
      this.report(type);
    });
  }

  onLoad() {
    try {
      this.advancedGrid.showTreeHeader = true;
      this.advancedGrid.addEventListener(genericEvent.ROW_CLICK, (data) => {
        const row = data as AdvancedDataGridRow;
        this.selectedRow = row.getRowData();
      });
      this.advancedGrid.addEventListener(genericEvent.CELL_CLICK, (cell) => {
        if (cell.getItemRander().type === "link:num" ) {
          if(cell.getParentRow().getParentItem().title === "root") {
            this.level1= cell.getParentRow().title;
            this.level2= null;
            this.level3= null;
          } else if(cell.getParentRow().getParentItem().getParentItem().title === "root") {
            this.level1= cell.getParentRow().getParentItem().title;
            this.level2=   cell.getParentRow().title;
            this.level3= null;
          } else {
            this.level1= cell.getParentRow().getParentItem().getParentItem().title;
            this.level2= cell.getParentRow().getParentItem().title;
            this.level3= cell.getParentRow().title;
          }
          this.level1 =this.level1.substr(0,3);
          let name = (cell.getColumnHeader().dataelement).split("_")[0];
          this.clickLinkHandler(name,cell.getItemRander());
        }
      });
      this.advancedGrid.groupItemRenderer = (data) => {
        const row = data as AdvancedDataGridRow;
        if (row.getRowData().highlighted === "Y") {
          row.setStyle("background", "lightgray");
        }
      };
      this.requestParams = [];
      this.actionMethod = 'method=display';
      this.actionPath = 'dashboardPCM.do?';
      this.title = "PCM Dashboard Monitor";
      this.inputData.cbStart= this.startOfComms.bind(this);
      this.inputData.cbStop= this.endOfComms.bind(this);
      this.inputData.cbResult = (event) => {
        this.inputDataResult(event);
      };
      this.inputData.cbFault = this.inputDataFault.bind(this);
      this.inputData.encodeURL = false;
      this.inputData.url =  this.baseURL + this.actionPath + this.actionMethod;
      this.inputData.send(this.requestParams);

    } catch (e) {
      // log the error in ERROR LOG
      console.log(e, this.moduleId, 'PCM Monitor', 'onLoad');
    }
  }

  startOfComms(): void {
    this.loadingImage.setVisible(true);
    this.dataExport.enabled = false;
  }

  endOfComms(): void {
    this.loadingImage.setVisible(false);
    this.dataExport.enabled= true;
  }

  inputDataResult(event) {
    try {
      let selectedTabDateId = 0;
      this.errorLocation = 0;
      if (this.inputData.isBusy()) {
        this.inputData.cbStop();
      } else {
        // Retrieves the Json from ResultEvent
        this.lastRecievedJSON = event;
        this.jsonReader.setInputJSON(this.lastRecievedJSON);
        if (!(JSON.stringify(this.lastRecievedJSON) === JSON.stringify(this.prevRecievedJSON))) {
          if ((this.jsonReader.getRequestReplyStatus()) && this.jsonReader.getRequestReplyStatus() ) {
            this.menuAccess = Number(this.jsonReader.getScreenAttributes()["menuaccess"]);
            this.dateFormat = this.jsonReader.getScreenAttributes()["dateformat"];
            this.dateFormatUpper = this.dateFormat.toUpperCase();
            this.currencyFormat = this.jsonReader.getScreenAttributes()["currencyPattern"];
            this.errorLocation = 294;
            this.startDate.formatString = this.dateFormat.toLowerCase();
            // Get lastRefTime
            let lastRef = this.jsonReader.getScreenAttributes()["lastRefTime"];
            this.lastRefTime.text = lastRef.replace(/\\u0028/g, '(').replace(/\\u0029/g, ')');
            this.refreshRateStr = this.jsonReader.getScreenAttributes()["refresh"];
            if(this.refreshRateStr) {
              if(this.refreshRateStr !== "") {
                this.refreshRate = Number(this.refreshRateStr);
              } else {
                this.refreshRate = 30;
              }
            }

            this.sysDateFrmSession  = this.jsonReader.getSingletons().sysDateFrmSession;
            this.inputSince  = this.jsonReader.getSingletons().inputSince;
            this.selectedDate = this.jsonReader.getSingletons().valueDate;
            this.errorLocation = 312;
            this.startDate.text =  this.selectedDate;
            this.systemStatus = this.jsonReader.getSingletons().systemStatus;
            if (this.systemStatus === "G") {
              this.divColor.nativeElement.style.backgroundColor = "green";
            } else if(this.systemStatus === "R") {
              this.divColor.nativeElement.style.backgroundColor = "red";
            } else {
              this.divColor.nativeElement.style.backgroundColor = "#FFBF00";
            }
            this.errorLocation = 319;
            this.valueVol = this.jsonReader.getSingletons().volume;
            this.value.selected = this.valueVol === "N";
            this.volume.selected = this.valueVol === "Y";

            this.spreadOnly = this.jsonReader.getSingletons().spreadOnly;
            this.spreadCheck.selected = this.spreadOnly === "Y";
            this.ccyThreshold = this.jsonReader.getSingletons().applyCurrencyThreshold;
            this.applyThresholdCheck.selected = this.ccyThreshold === "Y";
            this.ccyMultiplier = this.jsonReader.getSingletons().applyCurrencyMultiplier;
            this.applyMultiplierCheck.selected = this.ccyMultiplier === "Y";
            this.errorLocation = 330;
            if(this.entityChanged && this.tabs.getTabChildren().length> 0)  {
               // Create a copy of the tab children array
              const tabChildren = this.tabs.getTabChildren().slice();
              // Iterate over the copy and remove each child from the original list
              for (let i = 0; i < tabChildren.length; i++) {
                  this.tabs.removeChild(tabChildren[i]);
              }

            }
            this.entityChanged = false;
            if(this.tabs.getTabChildren().length == 0 ) {
              if (this.lastRecievedJSON.dashboard.advancedGrid && this.lastRecievedJSON.dashboard.advancedGrid.tabs) {
                setTimeout( () => {
                  this.displayContainerToday = this.tabs.addChild(Tab) as Tab;
                  this.displayContainerTodayPlus = this.tabs.addChild(Tab) as Tab;
                  this.displayContainerTodayPlusPlus = this.tabs.addChild(Tab) as Tab;
                  this.displayContainerTodayPlusThree = this.tabs.addChild(Tab) as Tab;
                  this.displayContainerTodayPlusFour = this.tabs.addChild(Tab) as Tab;
                  this.displayContainerTodayPlusFive = this.tabs.addChild(Tab) as Tab;
                  this.displayContainerTodayPlusSix = this.tabs.addChild(Tab) as Tab;
                  this.displayContainerTodayPlusSeven = this.tabs.addChild(Tab) as Tab;
                  this.displayContainerSelected = this.tabs.addChild(Tab) as Tab;

                }, 0);
                this.errorLocation = 345;
                setTimeout( () => {
                  selectedTabDateId = 8;
                  for (let i = 0; i < this.lastRecievedJSON.dashboard.advancedGrid.tabs.row.length; i++) {
                    this.tabs.getChildAt(i).id = i;
                    this.tabs.getChildAt(i).label = this.lastRecievedJSON.dashboard.advancedGrid.tabs.row[i].dateLabel;
                    this.tabs.getChildAt(i).businessday = this.lastRecievedJSON.dashboard.advancedGrid.tabs.row[i].businessday;

                    if (this.selectedDate.startsWith(this.lastRecievedJSON.dashboard.advancedGrid.tabs.row[i].dateLabel)) {
                      selectedTabDateId =  i;
                    }
                    this.tabs.getChildAt(i).dateValue = this.lastRecievedJSON.dashboard.advancedGrid.tabs.row[i].content;
                    if(  this.tabs.getChildAt(i).businessday == false) {
                      this.tabs.getChildAt(i).setTabHeaderStyle("color","darkgray");
                    }else
                      this.tabs.getChildAt(i).setTabHeaderStyle("color","black");
                  }
                  this.tabs.getChildAt(this.tabs.getTabChildren().length-1).label = 'Selected';

                  setTimeout( () => {
                  this.selectedTabId = selectedTabDateId;
                  this.tabs.selectedIndex = this.selectedTabId;
                 }, 0);

                }, 0);
              }
            } else {
              selectedTabDateId = 8;
                for (let i = 0; i < this.lastRecievedJSON.dashboard.advancedGrid.tabs.row.length; i++) {
                    if (this.selectedDate.startsWith(this.lastRecievedJSON.dashboard.advancedGrid.tabs.row[i].dateLabel)) {
                      selectedTabDateId =  i;
                    } 
              }
                this.selectedTabId = selectedTabDateId;
                this.tabs.selectedIndex = this.selectedTabId;
              this.errorLocation = 364;
              
            }

            /*Condition to check jSonReader databuilding*/
            if (!this.jsonReader.isDataBuilding()) {
              // Gets the combo box value from Json
              this.entityCombo.setComboData(this.jsonReader.getSelects(), false);
              if(this.jsonReader.getSingletons().selectedEntity) {
                this.selectedItemsListEntity  = this.jsonReader.getSingletons().selectedEntity;

                if(this.jsonReader.getSingletons().selectedEntity.indexOf(',') != -1) {
                  this.entityMoreItemsButton.enabled = true;
                  this.selectedEntity.text = this.get2FirstItemsFromList(this.jsonReader.getSingletons().selectedEntity);
                  this.selectedEntity.toolTip = this.jsonReader.getSingletons().selectedEntity;
                } else {
                  this.selectedEntity.text = this.entityCombo.selectedItem.value;
                  this.selectedItemsList = null;
                  this.entityMoreItemsButton.enabled = false;
                }
              }
              this.errorLocation = 384;
              if(this.lastRecievedJSON.dashboard.advancedGrid) {
                this.printIcon.enabled= this.lastRecievedJSON.dashboard.advancedGrid.grid_data.rows.size > 0;
                this.dataExport.enabled= this.lastRecievedJSON.dashboard.advancedGrid.grid_data.rows.size > 0;
                if(this.lastRecievedJSON.dashboard.advancedGrid.grid_data.rows.size> 0) {
                  this.advancedGrid.dataProvider = this.lastRecievedJSON.dashboard.advancedGrid;

                  if(this.tabs.getSelectedTab()) {
                    if(this.states[this.tabs.getSelectedTab().id]) {
                      this.advancedGrid.openSavedTreeState(this.states[this.tabs.getSelectedTab().id]);
                    }
                  } else {
                    if(this.states[0]) {
                      this.advancedGrid.openSavedTreeState(this.states[0]);
                    }
                  }

                } else {
                  this.advancedGrid.dataProvider = this.lastRecievedJSON.dashboard.advancedGrid;
                }

                if(this.selectedItem != null) {
                  this.advancedGrid.setSelectedRow(this.selectedItem);
                }
                this.advancedGrid.verticalScrollPosition = this.verticalPosition;
              }
              this.errorLocation = 412;
              let timeLeft = this.refreshRate;
              clearInterval(this.interval);
              this.interval = setInterval(() => {
                this.dataRefresh();
                this.errorLocation = 417;
              }, timeLeft*1000);
            }
            this.prevRecievedJSON = this.lastRecievedJSON;
          } else {
            // log the error in ERROR LOG
            this.swtAlert.error(SwtUtil.getPredictMessage('label.errorContactSystemAdmin', null)+' \n'+this.jsonReader.getRequestReplyMessage());
            this.actionPath = 'dashboardPCM.do?';
            this.actionMethod = "method=saveLogError";
            this.requestParams = [];
            this.requestParams["error"] = "PCM Monitor inputDataResult["+ this.errorLocation+ "]" ;
            this.requestParams["text"] = this.jsonReader.getRequestReplyMessage();
            this.faultData.encodeURL = false;
            this.faultData.url =  this.baseURL + this.actionPath + this.actionMethod;
            this.faultData.send(this.requestParams);
            this.faultData.cbResult = (event) => {
              this.faultDateResult(event);
            };
          }
        } else {
          if (this.jsonReader.getRequestReplyStatus()) {
            if (!this.jsonReader.isDataBuilding()) {
              if(this.lastRecievedJSON.dashboard.advancedGrid) {
                this.printIcon.enabled= this.lastRecievedJSON.dashboard.advancedGrid.grid_data.rows.size > 0;
                this.dataExport.enabled= this.lastRecievedJSON.dashboard.advancedGrid.grid_data.rows.size > 0;

              }
              this.errorLocation = 445;
            }
          } else {
            this.dataExport.enabled= false;
          }
        }
      }
    } catch (e) {
      // log the error in ERROR LOG
      this.swtAlert.error(SwtUtil.getPredictMessage('label.errorContactSystemAdmin', null)+' \n'+ "[" + this.errorLocation + "]:"+ e);
      this.actionPath = 'dashboardPCM.do?';
      this.actionMethod = "method=saveLogError";
      this.requestParams = [];
      this.requestParams["error"] = "PCM Monitor inputDataResult["+ this.errorLocation+ "]" ;
      this.requestParams["text"] = e;
      this.faultData.encodeURL = false;
      this.faultData.url =  this.baseURL + this.actionPath + this.actionMethod;
      this.faultData.send(this.requestParams);
      this.faultData.cbResult = (event) => {
        this.faultDateResult(event);
      };
    }

  }

  /**
   * This function serves when the rate button is clicked it opens the popup withthe
   * autorefresh rate set.
   **/
  optionsHandler(): void {
    try {
      clearInterval(this.interval);
      this.states[this.selectedTabId] = this.advancedGrid.saveOpenTreeState();
      // Instantiates the OptionPopUp object
      this.win= SwtPopUpManager.createPopUp(this,
        OptionsPopUp,
        {
          title: "Auto-refresh Rate", // childTitle,
          refreshText:this.refreshRate
        });
      this.win.width = "340";
      this.win.height = "150";
      this.win.id = "myOptionsPopUp";
      this.win.enableResize = false;
      this.win.showControls = true;
      this.win.isModal = true;
      this.win.onClose.subscribe(() => {
        this.autoRefreshAfterStop();
      }, error => {
        console.log(error);
      });
      this.win.display();
    } catch (error) {
      SwtUtil.logError(error, this.moduleId,"Dashboard"  , "optionsHandler", this.errorLocation);
    }
  }

  /**
   * clickLinkHandler
   * On hyperlink click this method is called
   */
  clickLinkHandler(nameColumn, selectedItem): void {
    try {
      if(nameColumn == "repair" || nameColumn== "suppressed" || nameColumn == "rejected" ) {
        let status;
        switch (nameColumn) {
          case "repair":
            status = '10';
            break;
          case "rejected":
            status = '4';
            break;
          case "suppressed":
            status = '9';
            break;
        }
        this.goToPCMInputException(status, selectedItem.text);

      } else {
        if(nameColumn == "Back")
          this.isBackValueClicked = true;
        else
          this.isBackValueClicked = false;
        this.goToPCDashboardDetails((nameColumn.substring(0,1)).toUpperCase());
      }
    } catch (error) {
      SwtUtil.logError(error, this.moduleId, "Dashboard", "clickLinkHandler", this.errorLocation);
    }
  }


  changeRadioGroup() {
    try {
      this.updateData("no");
    } catch (error) {
      SwtUtil.logError(error, this.moduleId, "Dashboard", "changeRadioGroup", this.errorLocation);
    }
  }

  /**
   * goToPCMInputException
   */
  goToPCMInputException(field,value) {
    try {
      this.status = field;
      this.actionPath = 'inputexceptionsmessages.do?';
      // Get the current Module Id.

      this.actionMethod = 'fromPCM=' + 'yes';
      this.actionMethod = this.actionMethod + '&fromDashboard=' + 'yes';
      this.actionMethod = this.actionMethod + '&currencyCode='+ this.level1;
      this.actionMethod = this.actionMethod + '&p=1' ;
      this.actionMethod = this.actionMethod + '&fromFlex=true';
      this.actionMethod = this.actionMethod + '&fromDate=' + this.startDate.text;
      this.actionMethod = this.actionMethod + '&status=' + field;
      this.actionMethod = this.actionMethod + '&m='+ value.toString();
      this.actionMethod = this.actionMethod + '&toDate=' +  this.startDate.text;
      this.actionMethod = this.actionMethod + '&n=50';
      this.actionMethod = this.actionMethod + '&type=All';

      ExternalInterface.call('openChildWindowExceptions', this.actionPath + this.actionMethod);
    } catch (error) {
      SwtUtil.logError(error, this.moduleId, "Dashboard", "goToPCMInputException", this.errorLocation);
    }
  }

  /**
   * goToPCDashboardDetails
   *when hyperlink is clicked the popUp with correspandant facilty is open
   */
  goToPCDashboardDetails(field) {
    try {
      this.status = field;
      // let newWindow = window.open("/details", 'Dashboard Details', 'height=700,width=1250,left=100,top=100,resizable=yes,scrollbars=yes,toolbar=yes,menubar=no,location=no,directories=no, status=yes' );
      // if (window.focus) {
      //   newWindow.focus();
      // }


      ExternalInterface.call('openChildWindow', 'dashboardDetails');
    } catch (error) {
      SwtUtil.logError(error, this.moduleId, "Dashboard", "goToPCDashboardDetails", this.errorLocation);
    }
  }

  getParamsFromParent() {
    this.entity  =  (this.selectedRow.ENTITY_ID) ? this.selectedRow.ENTITY_ID :this.entityCombo.selectedItem.content.indexOf('<')>-1?this.selectedItemsList:this.entityCombo.selectedItem.content;
    return  [
      {screenName: "dashboard",status: this.status, currencyCode: this.level1, accountGroup: this.level2, account: this.level3, entity: this.entity,
        listEntity:this.selectedItemsList, valueDate: this.startDate.text, ccyThreshold:this.applyThresholdCheck.selected ? 'Y': 'N',
        spreadOnly:this.spreadCheck.selected ? 'Y': 'N', ccyMultiplier:this.applyMultiplierCheck.selected ? 'Y': 'N',
        'inputSince': this.inputSince, 'isBackValueClicked': this.isBackValueClicked}];
  }


  public saveRefreshRate(rate) {
    try {
      this.actionPath = 'dashboardPCM.do?';
      this.actionMethod = "method=saveRefreshRate" ;
      this.requestParams = [];
      // Define method the request to access
      this.requestParams['refresh'] = rate;
      this.rateData.encodeURL = false;
      this.rateData.url = this.baseURL + this.actionPath + this.actionMethod;
      this.rateData.cbResult = () => {
        this.optionDateResult();
      };
      this.rateData.send(this.requestParams);
    } catch(e) {
      console.log(e, this.moduleId, 'PCM Monitor', 'saveRefreshRate');
    }
  }

  optionDateResult() {
    this.updateData("no");
  }



  autoRefreshAfterStop() {
    let timeLeft = this.refreshRate;
    clearInterval(this.interval);
    this.interval = setInterval(() => {
      this.dataRefresh();
    }, timeLeft*1000);
  }



  /**
   * report
   *
   * @param type: String
   *
   * This is a report icon action handler method
   */
  report(type: string): void {
    try {
      ExternalInterface.call("report", this.selectedItemsListEntity, this.selectedEntity.text, type, this.dateFormat,this.startDate.text, this.applyThresholdCheck.selected ? 'Y': 'N', this.applyMultiplierCheck.selected ? 'Y': 'N', this.spreadCheck.selected ? 'Y': 'N', this.value.selected ? 'Y': 'N');
    } catch (error) {
      console.log(error, this.moduleId, 'PCM Monitor', 'report');
      SwtUtil.logError(error,   this.moduleId, 'Dashboard', 'report',   this.errorLocation);
    }
  }

  /**
   * printPage
   *
   * @param event:Event
   *
   * Method to get call the action to get reports
   */
  printPage(event): void {
    try {
      ExternalInterface.call('printPage');

    } catch (error) {
      // log the error in ERROR LOG
      SwtUtil.logError(error,   this.moduleId, 'Dashboard', 'printPage',   this.errorLocation);
    }
  }

  /**
   * dataRefresh()
   * Timing result methods
   **/
  dataRefresh(): void {
    try {
      this.updateData("no");
    } catch ( error ) {
      // log the error in ERROR LOG
      SwtUtil.logError(error,   this.moduleId, 'Dashboard', 'dataRefresh',   this.errorLocation);
    }
  }


  tabIndexChangeHandler() {
    const selectedTabFromIndex = this.tabs.getChildAt(this.tabs.selectedIndex);
    if (selectedTabFromIndex.id !== this.selectedTabId) {
      this.states[this.selectedTabId] = this.advancedGrid.saveOpenTreeState();
    }
    let todayDate = new Date(CommonUtil.parseDate(selectedTabFromIndex["dateValue"], "YYYY-MM-DD"));
    todayDate.setHours(12, 0, 0);
    this.tabIsChanged = true;
    if(this.tabs.selectedLabel !== 'Selected') {
      this.startDate.selectedDate = todayDate;
    }
    this.updateData("no");
  }



  get2FirstItemsFromList(list) {
    let result = "";
    let occurrenceOfComma = (list.match(new RegExp(",", "g")) || []).length;
    if (list && list.indexOf(",") !== -1 && occurrenceOfComma >= 2) {
      result = list.split(",")[0].toString() + ' ,' + list.split(",")[1].toString() + ",...";
    } else {
      result = list;
    }
    return result;
  }

  changeCombo(combo, button, label) {
    try {
      if (combo.selectedValue == "<<Multiple Values>>") {
        button.enabled = true;
        label.text = "";
        this.selectedItemsListEntity = '';

      } else {
        this.selectedItemsListEntity = combo.selectedItem.content;
        button.enabled = false;
        label.text = combo.selectedValue;
        this.entityChanged = true;
        this.updateData("no");
      }

    } catch (e) {
      console.log(e, this.moduleId, 'PCM Monitor', 'changeCombo');
    }

  }

  multipleListSelect(comboSelectList, label) {
    try {
      this.win =  SwtPopUpManager.createPopUp(this, ListValues, {
        title: "Entity",
        operation: "in",
        dataSource: "fromDashboard",
        columnLabel: "entityList",
        columnCode: "entityList",
        viewOnly : false,
      });
      this.win.enableResize = false;
      this.win.id = "listValuesPopup";
      this.win.width = '500';
      this.win.height = '500';
      this.win.showControls = true;
      this.win.isModal = true;
      this.win.onClose.subscribe(() => {
        if (this.win.getChild().result) {
          this.selectedItemsList = this.selectedItemsListEntity;
          let occurrenceOfComma = (this.selectedItemsList.match(new RegExp(",", "g")) || []).length;
          if (this.selectedItemsList && this.selectedItemsList.indexOf(",") !== -1 && occurrenceOfComma >= 2) {
            label.text = this.selectedItemsList.split(",")[0].toString() + ' ,' + this.selectedItemsList.split(",")[1].toString() + ",...";
          } else {
            label.text = this.selectedItemsList;
          }
          this.entityChanged = true;
          this.updateData("no");
        }
      });
      this.win.display();
    } catch (e) {
      console.log(e, this.moduleId, "PCM Monitor", "multipleListSelect");
    }

  }
  private entityChanged = false;
  /**
   * Update the data, this is called whenever a fresh of the data is required.
   **/
  public updateData(autorefresh): void {
    try {
      this.states[this.selectedTabId] = this.advancedGrid.saveOpenTreeState();
      this.verticalPosition = this.advancedGrid.verticalScrollPosition;
      this.selectedItem = this.advancedGrid.getSelectedRow();
      /*Define the action to send the request*/
      this.actionPath = 'dashboardPCM.do?';
      this.actionMethod = 'method=display';
      this.requestParams = [];
      this.requestParams["dateAsString"] = this.startDate.text;
      this.requestParams["formatDate"] = this.dateFormat;
      this.requestParams["entity"] = this.selectedItemsListEntity;
      this.requestParams["applyCurrencyThreshold"]= this.applyThresholdCheck.selected ? 'Y': 'N';
      this.requestParams["applyCurrencyMultiplier"]= this.applyMultiplierCheck.selected ? 'Y': 'N';
      this.requestParams["spreadOnly"]=this.spreadCheck.selected ? 'Y': 'N';
      this.requestParams["volume"]=this.value.selected ? 'N': 'Y';
      this.requestParams["tabIsChanged"] = this.tabIsChanged;
      this.requestParams["entityChanged"] = this.entityChanged;
      this.requestParams["autoRefresh"]=(autorefresh) ? 'yes': autorefresh;
      this.inputData.cbStart= this.startOfComms.bind(this);
      this.inputData.cbStop= this.endOfComms.bind(this);
      this.inputData.cbFault = this.inputDataFault.bind(this);
      this.inputData.encodeURL = false;
      this.inputData.cbResult = (event) => {
        this.inputDataResult(event);
      };
      this.inputData.url =  this.baseURL +  this.actionPath +  this.actionMethod;
      this.inputData.send(this.requestParams);

    } catch (e) {
      // log the error in ERROR LOG
      console.log(e, this.moduleId, "PCM Monitor", "updateData");
      SwtUtil.logError(e, SwtUtil.AML_MODULE_ID, 'Dashboard', 'updateData',  this.errorLocation);
    }
  }

  /**
   * handleDate
   */
  handleDate(): void {
    let exist = false;
    if(this.validateDateField(this.startDate)) {
      let tempDate= moment(this.sysDateFrmSession, this.dateFormat.toUpperCase());
      let selectedDate = moment(this.startDate.text, this.dateFormat.toUpperCase());
      if(selectedDate) {
        for (let i = 0; i < this.tabs.getTabChildren().length - 1; i++) {
          
          
          const selectedTabFromIndex = this.tabs.getChildAt(0);
          
          tempDate= moment(selectedTabFromIndex["dateValue"], "YYYY-MM-DD").add(i, 'days');

          if(selectedDate.diff(tempDate) == 0) {
            exist = true;
            this.tabs.selectedIndex = i;
            break;
          }
        }
      }

      this.tabIsChanged = true;
      if(!exist) {
        this.tabs.selectedIndex = this.tabs.getTabChildren().length-1;
      }
      if (this.tabs.getSelectedTab().id !== this.selectedTabId) {
        this.states[this.selectedTabId] = this.advancedGrid.saveOpenTreeState();
      }
      this.updateData("no");
    }
  }

  validateDateField(dateField) {
    try {
      let date;
      if(dateField.text) {
        date = moment(dateField.text, this.dateFormatUpper , true);

        if(!date.isValid()) {
          this.swtAlert.warning('Date must be in the format '+this.dateFormatUpper);
          return false;
        }
      }
      dateField.selectedDate = date.toDate();
    } catch(error) {
      SwtUtil.logError(error, SwtUtil.SYSTEM_MODULE_ID, 'PCM Monitor', ' validateDateField', this.errorLocation);
    }

    return true;
  }

  /**
   * doHelp
   *
   * @param event: Event
   *
   * Function is called when "Help" button is click. Displays help window
   */

  doHelp(event): void {
    ExternalInterface.call("help");
  }


  /**
   * This function is used to set the refresh rate for the monitors.
   */



  closeHandler() {
    try {
      this.dispose();
    } catch (e) {
      SwtUtil.logError(e, SwtUtil.SYSTEM_MODULE_ID, 'PCM Monitor', ' closeHandler', this.errorLocation);
    }
  }


  /**
   * dispose
   *
   * This is an event handler, used to close the current tab/window
   */
  dispose(): void {
    try {
      this.advancedGrid = null;
      this.requestParams = null;
      this.inputData = null;
      this.jsonReader = null;
      this.menuAccess = null;
      this.selectedItem = null;
      this.lastRecievedJSON = null;
      this.prevRecievedJSON = null;
      ExternalInterface.call("close");
    } catch (e) {
      SwtUtil.logError(e, this.moduleId, 'PCM Monitor', 'dispose', this.errorLocation);
    }
  }


  /**
   * If a fault occurs with the connection with the server then display the lost connection label
   **/
  public inputDataFault(event): void {
    try {
    this.actionPath = 'dashboardPCM.do?';
    this.actionMethod = "method=saveLogError";
    this.requestParams = [];
    this.requestParams["error"] = event.error.error;
    this.requestParams["text"] = event.error.text;
    this.faultData.encodeURL = false;
    this.faultData.url =  this.baseURL + this.actionPath + this.actionMethod;
    this.faultData.send(this.requestParams);
    this.faultData.cbResult = (event) => {
      this.faultDateResult(event);
    };
    this.swtAlert.error(SwtUtil.getPredictMessage('label.genericException', null));

    } catch(e) {
      console.log(e, this.moduleId, 'PCM Monitor', 'inputDataFault');
    }
  }

  faultDateResult(event) {

  }


}

// Define lazy loading routes
const routes: Routes = [
  { path: '', component: PCMMonitor }
];
export const routing: ModuleWithProviders = RouterModule.forChild(routes);
// Define an NgModule that will be loaded by app module
@NgModule({
  imports: [routing, SwtToolBoxModule],
  declarations: [PCMMonitor],
  entryComponents: []
})
export class PCMMonitorModule {}
