<SwtModule (creationComplete)="onLoad()" width="100%" height="100%">
  <VBox width="100%" height="100%" paddingLeft="5" paddingRight="5" paddingTop="5">
    <SwtCanvas width="100%" height="85%">
      <VBox width="100%" height="100%" verticalGap="0">
      <HBox>
        <SwtLabel text="Report Type" width="130">
        </SwtLabel>
        <SwtComboBox id="reportTypeCombo" #reportTypeCombo toolTip="Select Report Type"  dataLabel="reportTypeList"
          width="350">
        </SwtComboBox>
      </HBox>
      <HBox width="100%">
        <SwtLabel id="entityLabel" #currencyLabel text="Entity" width="130">
        </SwtLabel>
        <SwtComboBox id="entityCombo" #entityCombo toolTip="Select Entity" dataLabel="entityList" (change)="changeCombo($event)" width="150">
        </SwtComboBox>
        <SwtLabel paddingLeft="20" id="selectedEntity" #selectedEntity>
        </SwtLabel>
      </HBox>
      <HBox width="100%">
        <SwtLabel id="currencyLabel" #currencyLabel text="Currency" width="130">
        </SwtLabel>
        <SwtComboBox id="ccyCombo" #ccyCombo (change)="changeCombo($event)" toolTip="Select currency code"
          dataLabel="currencyList" width="80">
        </SwtComboBox>
        <SwtLabel paddingLeft="20" id="selectedCcy" #selectedCcy paddingLeft="90">
        </SwtLabel>
      </HBox>
      <HBox width="100%">
        <SwtLabel #acagLabel id="acagLabel" text="Account Group" width="130">
        </SwtLabel>
        <SwtComboBox id="acctGrpCombo" #acctGrpCombo toolTip="Select account group" dataLabel="AcctGrpList" (change)="changeCombo($event)" width="150">
        </SwtComboBox>
        <SwtLabel paddingLeft="20" id="selectedAcctGrp" #selectedAcctGrp>
        </SwtLabel>
      </HBox>
      <HBox width="100%">
        <SwtLabel text="Source" width="130">
        </SwtLabel>
        <SwtComboBox id="sourceCombo" #sourceCombo dataLabel="source" toolTip="Select Source" (change)="changeCombo($event)" width="150">
        </SwtComboBox>
        <SwtLabel paddingLeft="20" id="selectedSource" #selectedSource>
        </SwtLabel>
      </HBox>
      <HBox width="100%" height="30">
        <SwtLabel text="Date" width="130"></SwtLabel>
        <SwtRadioButtonGroup #dateGroup id="dateGroup" align="horizontal" (change)="onDateChange()">
          <SwtRadioItem #radioSingleDay id="radioSingleDay" value="S" label="Single day" selected="true"
            groupName="dateGroup">
          </SwtRadioItem>
          <spacer width="30"></spacer>
          <SwtRadioItem #radioDateRange id="radioDateRange" value="D" label="Date Range" groupName="dateGroup">
          </SwtRadioItem>
        </SwtRadioButtonGroup>
      </HBox>
      <HBox width="100%">
        <SwtLabel text="" width="130"></SwtLabel>
        <SwtDateField #frmDateChooser id="frmDateChooser" textAlign="right" width="70" editable="true" toolTip="From">
        </SwtDateField>
        <spacer width="15"></spacer>
        <SwtLabel text="To" id="toLabel" #toLabel width="40"></SwtLabel>
        <SwtDateField #toDateChooser id="toDateChooser" textAlign="right" width="70" editable="true" toolTip="To">
        </SwtDateField>
      </HBox>
      <HBox width="100%">
        <SwtLabel text="Use Ccy Multiplier" width="128"></SwtLabel>
        <SwtCheckBox #useCcyMultiplier id="useCcyMultiplier" width="70" selected="false" styleName="checkbox">
        </SwtCheckBox>
      </HBox>
      <HBox width="100%" height="30">
        <SwtLabel text="Export Type" width="130"></SwtLabel>
        <SwtRadioButtonGroup #exportTypeGroup id="exportTypeGroup" align="horizontal" >
          <SwtRadioItem #pdf id="pdf" value="pdf" label="PDF" selected="true"
            groupName="exportTypeGroup">
          </SwtRadioItem>
          <spacer width="30"></spacer>
          <SwtRadioItem #excel id="excel" value="excel" label="Excel" groupName="exportTypeGroup">
          </SwtRadioItem>
          <spacer width="30"></spacer>
          <SwtRadioItem #csv id="csv" value="csv" label="CSV" groupName="exportTypeGroup">
          </SwtRadioItem>
        </SwtRadioButtonGroup>
      </HBox>
      </VBox>
    </SwtCanvas>
    <SwtCanvas width="100%" height="11%">
      <HBox width="100%" paddingTop="3">
        <HBox width="100%">
          <SwtButton #reportButton label="Report" width="60" (click)="doReport($event)" enabled="true"></SwtButton>
          <SwtButton #cancelButton label="Cancel" width="60" (click)="cancelReport($event)" enabled="false"></SwtButton>
          <SwtButton #closeButton label="Close" width="60" (click)="cancelHandler($event)"></SwtButton>
        </HBox>
        <HBox horizontalAlign="right" paddingRight="10">
            <SwtLoadingImage #loadingImage></SwtLoadingImage>
          <SwtHelpButton #help id="help" enabled="true" (click)="doHelp()"></SwtHelpButton>
        </HBox>
      </HBox>
    </SwtCanvas>
  </VBox>
</SwtModule>
