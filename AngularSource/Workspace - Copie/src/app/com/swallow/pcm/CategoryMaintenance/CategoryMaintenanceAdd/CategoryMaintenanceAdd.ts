import {Component, ElementRef, NgModule, ViewChild} from '@angular/core';
import {
  Alert,
  CommonService,
  ExternalInterface,
  focusManager, GridRow,
  HTTPComms,
  JSONReader,
  Keyboard,
  StringUtils,
  SwtAlert,
  SwtButton,
  SwtCanvas,
  SwtCheckBox,
  SwtCommonGrid,
  SwtLabel,
  SwtModule,
  SwtNumericInput,
  SwtPanel,
  SwtRadioButtonGroup,
  SwtRadioItem,
  SwtTextArea,
  SwtTextInput,
  SwtToolBoxModule,
  SwtUtil,
  XML
} from 'swt-tool-box';
import {RouterModule, Routes} from "@angular/router";
import {ModuleWithProviders} from "@angular/compiler/src/core";



declare function validateFormatTime(strField): any;
declare var instanceElement: any;
@Component({
  selector: 'app-pccategory-maintenance-add',
  templateUrl: './CategoryMaintenanceAdd.html',
  styleUrls: ['./CategoryMaintenanceAdd.css']
})
export class CategoryMaintenanceAdd extends  SwtModule {

  private swtAlert: SwtAlert;
  private  spreadGrid: SwtCommonGrid;
  private  rulesGrid: SwtCommonGrid;

  /**
   * Data Objects
   **/
  public jsonReader= new JSONReader();
  public lastRecievedJSON;
  public prevRecievedJSON;

  /**
   * Communication Objects
   **/
  private  inputData = new HTTPComms(this.commonService);
  private logicUpdate = new HTTPComms(this.commonService);
  private baseURL =SwtUtil.getBaseURL();
  private  actionMethod= "";
  private  actionPath= "";
  private  requestParams = [];
  public screenName = null;
  public helpURL=null;
  public  title=null;
  public  categoryRuleId=null;
  public  ruleId=null;
  public errorLocation=0;
  public categoryId= '';
  public listOrder;
  public listRuleAssignPriority;
  public paymentRequest;
  public maxOrder;
  public listOrderCR;
  public maxOrderCR = 1;
  private ordinal= null;
  private ruleAssignPriority= null;
  private  childScreenName = null;
  private tableToJoinQueryBuilder: any = [];
  private from = "";
  private reOrder = false;
  private fourEyesRequired = false;
  private changesInRule= false;
  /* - START -- Screen Name and Version number ---- */
  private moduleName = 'PC Category Maintenance Details';
  private versionNumber = '1.00.00';
  private releaseDate = '20 Feb 2019';
  private moduleId='PC';
  private spreadId = '';
  public ruleGridList: any = null;
  public operationsList: XML = new XML( "<operationsList/>");
  private stateBeforeChange = false;
  public pcmInputFlag;
  /************SwtTextInput********/
  @ViewChild('categoryNameTxtInput') categoryNameTxtInput: SwtTextInput;
  @ViewChild('categoryIdTxtInput') categoryIdTxtInput: SwtTextInput;
  @ViewChild('ordinalNumInput') ordinalNumInput: SwtNumericInput;
  @ViewChild('ruleAssignPriorityInput') ruleAssignPriorityInput: SwtNumericInput;
  @ViewChild('timeInput') timeInput: SwtTextInput;
  @ViewChild('offsetNum') offsetNum: SwtNumericInput;
  /************SwtLabel********/
  @ViewChild('categoryNameLabel') categoryNameLabel: SwtLabel;
  @ViewChild('categoryIdLabel') categoryIdLabel: SwtLabel;
  @ViewChild('orderLabel') orderLabel: SwtLabel;
  @ViewChild('instRelGroupLabel') instRelGroupLabel: SwtLabel;
  @ViewChild('labelRelTime') labelRelTime: SwtLabel;
  @ViewChild('labelOffsetDays') labelOffsetDays: SwtLabel;
  @ViewChild('assigMetLabel') assigMetLabel: SwtLabel;
  @ViewChild('incTargLabel') incTargLabel: SwtLabel;
  @ViewChild('useliqLabel') useliqLabel: SwtLabel;
  @ViewChild('incLiqLabel') incLiqLabel: SwtLabel;
  @ViewChild('ruleAssignPriorityLabel') ruleAssignPriorityLabel: SwtLabel;

  /***LodingImage*******/
  // @ViewChild('loadingImage') loadingImage: SwtLoadingImage;
  /*********SWtButton*************/
  @ViewChild('saveButton') saveButton: SwtButton;
  @ViewChild('cancelButton') cancelButton: SwtButton;
  @ViewChild('addRuleButton') addRuleButton: SwtButton;
  @ViewChild('changeRuleButton') changeRuleButton: SwtButton;
  @ViewChild('viewRuleButton') viewRuleButton: SwtButton;
  @ViewChild('deleteRuleButton') deleteRuleButton: SwtButton;

  /*********SwtCheckBox*************/
  @ViewChild('incTargCheckBox') incTargCheckBox: SwtCheckBox;
  @ViewChild('incLiqCheckBox') incLiqCheckBox: SwtCheckBox;
  @ViewChild('actCheckbox') actCheckbox: SwtCheckBox;
  @ViewChild('useliqCheckBox') useliqCheckBox: SwtCheckBox;
  /*********SwtRadioButtonGroup*************/
  @ViewChild('instRelGroup') instRelGroup: SwtRadioButtonGroup;
  @ViewChild('assigMetGroup') assigMetGroup: SwtRadioButtonGroup;
  @ViewChild('relTimeGroup') relTimeGroup: SwtRadioButtonGroup;

  /*********SwtRadioItem*************/
  @ViewChild('urgent') urgent: SwtRadioItem;
  @ViewChild('spread') spread: SwtRadioItem;
  @ViewChild('radioB') radioB: SwtRadioItem;
  @ViewChild('radioM') radioM: SwtRadioItem;
  @ViewChild('radioS') radioS: SwtRadioItem;
  @ViewChild('radioK') radioK: SwtRadioItem;
  @ViewChild('radioU') radioU: SwtRadioItem;
  @ViewChild('radioI') radioI: SwtRadioItem;
  @ViewChild('radioT') radioT: SwtRadioItem;
  /*********SwtTextArea*************/
  @ViewChild('queryText') queryText: SwtTextArea;
  /*********SwtCanvas*************/
  @ViewChild('canvasSpreadGrid') canvasSpreadGrid: SwtCanvas;
  @ViewChild('canvasRulesGrid') canvasRulesGrid: SwtCanvas;

  /*********SwtPanel*************/
  @ViewChild('panelSpreadGrid') panelSpreadGrid: SwtPanel;
  @ViewChild('panelRulesGrid') panelRulesGrid: SwtPanel;
  /*********GridRow*************/
  @ViewChild('gridRowRelTime') gridRowRelTime: GridRow;
  @ViewChild('gridRowOffsetDays') gridRowOffsetDays: GridRow;

  /***Module****/
  @ViewChild('swtModule') swtModule: SwtModule;


  constructor( private commonService: CommonService, private element: ElementRef) {
    super(element, commonService);
    this.swtAlert = new SwtAlert(commonService);
  }


  ngOnDestroy(): any {
    instanceElement = null;
  }


  ngOnInit(): void {
    let paramsFromParent = [];
    if(window.opener.instanceElement) {
      paramsFromParent =  window.opener.instanceElement.getParamsFromParent();
      if(paramsFromParent) {
        this.screenName =  paramsFromParent[0].screenName;
        this.categoryId =  paramsFromParent[0].categoryId;
        this.maxOrder = paramsFromParent[0].maxOrder;
        this.listOrder = paramsFromParent[0].listOrder;
        this.listRuleAssignPriority = paramsFromParent[0].listRuleAssignPriority;
        this. paymentRequest = paramsFromParent[0].paymentRequest === true;
        this.pcmInputFlag = paramsFromParent[0].pcmInputFlag;
      }
    }
    instanceElement = this;
    this.timeInput.enabled=true;
    this.gridRowRelTime.visible=false;
    this.gridRowRelTime.includeInLayout=false;
    this.gridRowOffsetDays.visible=false;
    this.gridRowOffsetDays.includeInLayout=false;
    this.relTimeGroup.enabled=true;
    this.timeInput.visible=false;
    this.saveButton.label = SwtUtil.getPredictMessage('button.save', null);
    this.cancelButton.label = SwtUtil.getPredictMessage('button.cancel', null);
    this.categoryNameLabel.text = SwtUtil.getPredictMessage('categoryMaintenanceDetails.label.name', null);
    this.categoryNameTxtInput.toolTip = SwtUtil.getPredictMessage('categoryMaintenanceDetails.tooltip.name', null);
    this.categoryIdLabel.text = SwtUtil.getPredictMessage('categoryMaintenanceDetails.label.id', null);
    this.categoryIdTxtInput.toolTip = SwtUtil.getPredictMessage('categoryMaintenanceDetails.tooltip.id', null);
    this.actCheckbox.label = SwtUtil.getPredictMessage('categoryMaintenanceDetails.label.active', null);
    this.actCheckbox.toolTip = SwtUtil.getPredictMessage('categoryMaintenanceDetails.tooltip.active', null);
    this.orderLabel.text = SwtUtil.getPredictMessage('categoryMaintenanceDetails.label.order', null);
    this.ordinalNumInput.toolTip = SwtUtil.getPredictMessage('categoryMaintenanceDetails.tooltip.order', null);
    this.ruleAssignPriorityLabel.text = SwtUtil.getPredictMessage('categoryMaintenanceDetails.label.ruleAssignmentPriority', null);
    this.ruleAssignPriorityInput.toolTip = SwtUtil.getPredictMessage('categoryMaintenanceDetails.tooltip.ruleAssignmentPriority', null);
    this.instRelGroupLabel.text = SwtUtil.getPredictMessage('categoryMaintenanceDetails.label.type', null);
    this.instRelGroup.toolTip = SwtUtil.getPredictMessage('categoryMaintenanceDetails.tooltip.type', null);
    this.urgent.label = SwtUtil.getPredictMessage('categoryMaintenanceDetails.label.urgent', null);
    this.spread.label = SwtUtil.getPredictMessage('categoryMaintenanceDetails.label.spreadable', null);
    this.labelRelTime.text = SwtUtil.getPredictMessage('categoryMaintenanceDetails.label.releaseTime', null);
    this.relTimeGroup.toolTip = SwtUtil.getPredictMessage('categoryMaintenanceDetails.tooltip.releaseTime', null);
    this.radioK.label = SwtUtil.getPredictMessage('categoryMaintenanceDetails.label.kickOff', null);
    this.radioU.label = SwtUtil.getPredictMessage('categoryMaintenanceDetails.label.froPaymentRequest', null);
    this.radioI.label = SwtUtil.getPredictMessage('categoryMaintenanceDetails.label.immediate', null);
    this.radioT.label = SwtUtil.getPredictMessage('categoryMaintenanceDetails.label.specificTime', null);
    this.timeInput.toolTip = SwtUtil.getPredictMessage('categoryMaintenanceDetails.tooltip.time', null);
    this.labelOffsetDays.text = SwtUtil.getPredictMessage('categoryMaintenanceDetails.label.daysOffset', null);
    this.offsetNum.toolTip = SwtUtil.getPredictMessage('categoryMaintenanceDetails.tooltip.daysOffset', null);
    this.assigMetLabel.text = SwtUtil.getPredictMessage('categoryMaintenanceDetails.label.assignmentMethod', null);
    this.assigMetGroup.toolTip = SwtUtil.getPredictMessage('categoryMaintenanceDetails.tooltip.assignmentMethod', null);
    this.radioB.label = SwtUtil.getPredictMessage('categoryMaintenanceDetails.label.both', null);
    this.radioM.label = SwtUtil.getPredictMessage('categoryMaintenanceDetails.label.manual', null);
    this.radioS.label = SwtUtil.getPredictMessage('categoryMaintenanceDetails.label.system', null);
    this.useliqLabel.text = SwtUtil.getPredictMessage('categoryMaintenanceDetails.label.liqCheck', null);
    this.useliqCheckBox.toolTip = SwtUtil.getPredictMessage('categoryMaintenanceDetails.tooltip.liqCheck', null);
    this.incTargLabel.text = SwtUtil.getPredictMessage('categoryMaintenanceDetails.label.target', null);
    this.incTargCheckBox.toolTip = SwtUtil.getPredictMessage('categoryMaintenanceDetails.tooltip.target', null);
    this.incLiqLabel.text = SwtUtil.getPredictMessage('categoryMaintenanceDetails.label.incAvailLiq', null);
    this.incLiqCheckBox.toolTip = SwtUtil.getPredictMessage('categoryMaintenanceDetails.tooltip.incAvailLiq', null);
    this.panelRulesGrid.title = SwtUtil.getPredictMessage('categoryMaintenanceDetails.rulePanel.title', null);
    this.panelSpreadGrid.title = SwtUtil.getPredictMessage('categoryMaintenanceDetails.spreadPanel.title', null);
    this.addRuleButton.label = SwtUtil.getPredictMessage('button.add', null);
    this.changeRuleButton.label = SwtUtil.getPredictMessage('button.change', null);
    this.deleteRuleButton.label = SwtUtil.getPredictMessage('button.delete', null);
    this.viewRuleButton.label=SwtUtil.getPredictMessage('button.view', null);

    this.categoryIdTxtInput.enabled=true;
    this.viewRuleButton.enabled= false;

    this.disableOrEnableButtons(false);
    if(this.screenName =="add") {
      this.categoryIdTxtInput.setFocus();
      this.actCheckbox.enabled = true;
      this.categoryNameTxtInput.enabled = true;
      this.categoryIdTxtInput.enabled = true;
      this.ordinalNumInput.enabled = true;
     // this.ordinalNumInput.text = this.maxOrder== null? 1: Number(this.maxOrder) + 1;
      this.ruleAssignPriorityInput.enabled = this.pcmInputFlag=='Y' ? true: false;
      this.saveButton.visible = true;
      this.instRelGroup.enabled = true;
      this.assigMetGroup.enabled = true;
      this.incTargCheckBox.enabled = true;
      this.incLiqCheckBox.enabled = true;
      this.addRuleButton.enabled = true;
    } else if(this.screenName =="view") {
      this.categoryNameTxtInput.enabled = false;
      this.categoryIdTxtInput.enabled = false;
      this.ordinalNumInput.enabled = false;
      this.ruleAssignPriorityInput.enabled = false;
      this.useliqCheckBox.enabled = false;
      this.actCheckbox.enabled = false;
      this.incTargCheckBox.enabled = false;
      this.incLiqCheckBox.enabled = false;
      this.relTimeGroup.enabled=false;
      this.instRelGroup.enabled = false;
      this.assigMetGroup.enabled = false;
      this.timeInput.enabled=false;
      this.addRuleButton.enabled = false;
      this. saveButton.visible = false;
      this.offsetNum.enabled = false;
    } else if(this.screenName =="change") {
      this.categoryNameTxtInput.enabled = true;
      this.ordinalNumInput.enabled = true;
      this.ruleAssignPriorityInput.enabled = this.pcmInputFlag=='Y' ? true: false;
      this.categoryIdTxtInput.enabled = false;
      this. saveButton.visible = true;
      this.instRelGroup.enabled = true;
      this.actCheckbox.enabled = true;
      this.assigMetGroup.enabled = true;
      this.incTargCheckBox.enabled = true;
      this.incLiqCheckBox.enabled = true;
      this.addRuleButton.enabled = true;
    }
  }

  /**
   * onLoad
   * Initializer (multilingual, fetch list details )
   */
  onLoad(): void {
    try {
      this.spreadGrid = this.canvasSpreadGrid.addChild(SwtCommonGrid) as SwtCommonGrid;
      this.rulesGrid = this.canvasRulesGrid.addChild(SwtCommonGrid) as SwtCommonGrid;
      this.rulesGrid.onFilterChanged = this.disableButtons.bind(this);
      this.rulesGrid.onSortChanged =  this.disableButtons.bind(this);
      this.requestParams = [];

      this.actionPath='categoryPCM.do?';

      // Checks the screen is add and calls the add action
      if(this.screenName != "add") {
        this.requestParams["categoryId"] = this.categoryId;
        this.actionMethod='method=view';
      } else {
        this.actionMethod='method=add';
      }
      this.inputData.cbStart = this.startOfComms.bind(this);
      this.inputData.cbStop = this.endOfComms.bind(this);

      this.inputData.cbResult = (data) => {
        this.inputDataResult(data);
      };

      this.inputData.cbFault=this.inputDataFault.bind(this);
      this.inputData.encodeURL=false;
      this.inputData.url=this.baseURL + this.actionPath + this.actionMethod;
      this.inputData.send(this.requestParams);

      this.rulesGrid.onRowClick = (event) => {
        this.cellClickEventHandler(event);
      };



      this.spreadGrid.cellLinkClick.subscribe((selectedRowData) => {
        this.clickLinkHandler(selectedRowData);
      });


      this.swtModule.subscribeSpy([this.rulesGrid]);
      this.swtModule.onSpyChange.subscribe((e) => {
        /**********General Tab cutoff  and p_rule tables*************/
        this.ruleGridList = this.rulesGrid.changes;
      });

    } catch(error) {
      SwtUtil.logError(error, this.moduleId, "CategoryMaintenance", "onLoad", this.errorLocation);
    }
  }
  /**
   * startOfComms
   * Part of a callback  to all for control of the loading swf from the HTTPComms Object
   */
  startOfComms(): void {
    this.disableComponents(false);
  }

  /**
   * endOfComms
   * Part of a callback  to all for control of the loading swf from the HTTPComms Object
   */
  endOfComms(): void {
    this.disableComponents(true);
  }

  /**
   * inputDataResult
   * param event: ResultEvent
   * This is a callback method, to handle result event
   *
   */
  inputDataResult(event): void {
    try {

      if (this.inputData.isBusy()) {
        this.inputData.cbStop();
      } else {
        /* Get result as xml */
        this.lastRecievedJSON =event;
        this.jsonReader.setInputJSON(this.lastRecievedJSON);
        /* Condition to check lastRecievedXML not equal to prevRecievedXML */
        if (!(JSON.stringify(this.lastRecievedJSON) === JSON.stringify(this.prevRecievedJSON))) {
          /* Condition to check request reply status is true*/
          if (this.jsonReader.getRequestReplyStatus()) {
            // gets the help url
            this.paymentRequest = this.jsonReader.getSingletons().existPayReq === "true";
            this.helpURL =  this.jsonReader.getSingletons().helpurl;
            if (!this.jsonReader.isDataBuilding()) {
              this.fourEyesRequired = this.jsonReader.getScreenAttributes()["fourEyesRequired"];
              // Rules Grid
              this.rulesGrid.CustomGrid(this.lastRecievedJSON.categoryRule.gridRules.metadata);
              // Spread Grid
              this.spreadGrid.CustomGrid(this.lastRecievedJSON.categoryRule.gridSpread.metadata);
              if(this.jsonReader.getSingletons().ruleAssignPriority) {
                this.ruleAssignPriority = Number(this.jsonReader.getSingletons().ruleAssignPriority);
                this.ruleAssignPriorityInput.text=  this.ruleAssignPriority;
              }else{
                this.ruleAssignPriorityInput.text=  1;
                this.ruleAssignPriority= 1;
              }
              if(this.screenName == "add") {
                  this.listOrderCR = [];
             }

              if(this.screenName !== "add") {
                if(this.screenName === "change") {
                  this.instRelGroup.enabled = this.paymentRequest == false;
                  this.relTimeGroup.enabled = this.paymentRequest == false;
                  this.timeInput.enabled = this.paymentRequest == false;
                } else {
                  this.useliqCheckBox.enabled = false;
                  this.actCheckbox.enabled = false;
                  this.incTargCheckBox.enabled = false;
                  this.incLiqCheckBox.enabled = false;
                  this.relTimeGroup.enabled=false;
                  this.instRelGroup.enabled = false;
                  this.assigMetGroup.enabled = false;
                  this.timeInput.enabled=false;
                }
                let ordinalCR = this.jsonReader.getSingletons().maxOrdinalCR;
                if(ordinalCR != "") {
                  this.maxOrderCR = Number(ordinalCR);
               }
                this.listOrderCR = this.jsonReader.getSingletons().ordinaListCR;
                this.listOrderCR = this.listOrderCR.replace("[","").replace("]","");
                if(this.listOrderCR != "") {
                 this.listOrderCR = this.listOrderCR.split(',').map(Number);
               } else {
                 this.listOrderCR = [];
               }

               //remove current category priority from list
                let index = this.listRuleAssignPriority.indexOf(Number(this.ruleAssignPriority));
                if (index !== -1) {
                  this.listRuleAssignPriority.splice(index, 1)
                }

                // Rules Grid
                if (this.rulesGrid === null || this.rulesGrid === undefined) {
                  this.rulesGrid.CustomGrid(this.lastRecievedJSON.categoryRule.gridRules.metadata);
                }

                this.rulesGrid.doubleClickEnabled = false;
                this.rulesGrid.CustomGrid(this.lastRecievedJSON.categoryRule.gridRules.metadata);
                if (this.lastRecievedJSON.categoryRule.gridRules.rows.size > 0) {
                  this.rulesGrid.gridData = this.lastRecievedJSON.categoryRule.gridRules.rows;
                  this.rulesGrid.setRowSize = this.lastRecievedJSON.categoryRule.gridRules.rows.size;
                } else {
                  // this.rulesGrid.dataProvider = [];
                  // this.rulesGrid.selectedIndex = -1;
                }
                // Spread Grid
                if (this.spreadGrid === null || this.spreadGrid === undefined) {
                  this.spreadGrid.CustomGrid(this.lastRecievedJSON.categoryRule.gridSpread.metadata);
                }
                this.spreadGrid.doubleClickEnabled = false;
                this.spreadGrid.CustomGrid(this.lastRecievedJSON.categoryRule.gridSpread.metadata);
                if (this.lastRecievedJSON.categoryRule.gridSpread.rows.size > 0) {
                  this.spreadGrid.gridData = this.lastRecievedJSON.categoryRule.gridSpread.rows;
                  this.spreadGrid.setRowSize = this.lastRecievedJSON.categoryRule.gridSpread.rows.size;
                } else {
                  this.spreadGrid.dataProvider = null;
                  this.spreadGrid.selectedIndex = -1;
                }
                this.categoryNameTxtInput.text =  this.jsonReader.getSingletons().categoryName;
                this.categoryIdTxtInput.text =  this.jsonReader.getSingletons().categoryId;
                if(this.jsonReader.getSingletons().ordinal.content != "") {
                  this.ordinal = Number(this.jsonReader.getSingletons().ordinal);
                  this.ordinalNumInput.text=  this.ordinal;
                }
                this.assigMetGroup.selectedValue = this.jsonReader.getSingletons().assignMethod;
                this.instRelGroup.selectedValue = this.jsonReader.getSingletons().type;
                // this.urgent.selected = this.instRelGroup.selectedValue=="U";
                this.gridRowRelTime.visible=  this.urgent.selected;
                this.gridRowRelTime.includeInLayout=  this.urgent.selected;
                this.gridRowOffsetDays.visible=  this.urgent.selected;
                this.gridRowOffsetDays.includeInLayout=  this.urgent.selected;
                if(this.urgent.selected) {
                  this.relTimeGroup.selectedValue= this.jsonReader.getSingletons().setReleaseTime;
                  if(this.radioT.selected) {
                    this.timeInput.visible=true;
                    this.timeInput.includeInLayout=true;
                    this.timeInput.text=this.jsonReader.getSingletons().specifiedTime;
                  }
                  this.offsetNum.text = (this.jsonReader.getSingletons().offsetDays)? this.jsonReader.getSingletons().offsetDays : "";
                  this.validateUseLiquidity();
                }
                this.incTargCheckBox.selected=this.jsonReader.getSingletons().inclTargetPercent=='Y';
                this.incLiqCheckBox.selected=this.jsonReader.getSingletons().inclInAvailableLiq=='Y';
                this.useliqCheckBox.selected = this.jsonReader.getSingletons().useLiqCheck =='Y';
                this.actCheckbox.selected = this.jsonReader.getSingletons().isActive=='Y';
                this.stateBeforeChange = this.actCheckbox.selected;

              }
            }
          }
        }
      }
    } catch(error) {
      console.log(error, this.moduleId, "CategoryMaintenance", "inputDataResult");
      SwtUtil.logError(error, this.moduleId, "CategoryMaintenance", "inputDataResult", this.errorLocation);
    }
  }

  /**
   * inputDataFault
   * param event:  FaultEvent
   * This is a callback , used to handle fault event.
   * Shows fault message in alert window.
   */
  inputDataFault(event): void {
    let message = SwtUtil.getPredictMessage('label.genericException', null);
    this.swtAlert.error(message);
  }

  /**
   * clickLinkHandler
   * On hyperlink click this method is called
   */
  clickLinkHandler(selectedRowData): void {
    let field = selectedRowData.target.name;
    try {
      this.goToSpreadProfileDetails();
    } catch (error) {
      SwtUtil.logError(error, this.moduleId, "CategoryMaintenance", "clickLinkHandler", this.errorLocation);
    }
  }


  /**
   * cellClickEventHandler
   *
   * @param event: Event
   *
   * This method is used to manumberain the button status when a row is clicked
   */
  cellClickEventHandler(event): void {
    try {
      if (this.rulesGrid.selectedIndex >= 0 && this.rulesGrid.selectable) {
        if (this.screenName == 'view') {
          this.disableOrEnableButtons(false);
        } else {
          this.disableOrEnableButtons(true);
        }
        this.viewRuleButton.enabled=true;
      } else {
        this.disableOrEnableButtons(false);
      }
      event.stopPropagation();

    } catch (e) {
      // log the error in ERROR LOG
      SwtUtil.logError(e, this.moduleId, 'CategoryMaintenance', 'cellClickEventHandler', this.errorLocation);
    }
  }
  /**
   * goToSearchTool
   *when hyperlink is clicked the popUp with correspandant facilty is open
   */
  goToSpreadProfileDetails() {

    try {
      this.from= 'Spread';
      this.spreadId = this.spreadGrid.selectedItem.spreadId.content;
      ExternalInterface.call('openSpreadProfile', 'spreadProfilesAdd', 'view');
    } catch (error) {
      SwtUtil.logError(error, this.moduleId, "CategoryMaintenance", "goToSearchTool", this.errorLocation);
    }
  }

  saveEventHandler() {
    Alert.okLabel = "OK";
    if(this.categoryIdTxtInput.text.length==0 || this.categoryNameTxtInput.text.length==0 || !this.ordinalNumInput.text || !this.ruleAssignPriorityInput.text ) {
      this.swtAlert.warning('Please fill all mandatory fields (marked with *)');
      return;
    }
    if(this.categoryNameTxtInput.text.trim().length == 0) {
      this.swtAlert.warning('Category Name can not be saved as just spaces');
      return;
    }

    if (!(this.spread.selected) && this.radioT.selected && !this.validateTime(this.timeInput)) {
      return;
    }

    this.validateUseLiquidity();
    this.xmlData();
    this.save();
    // if(this.fourEyesRequired == true && (this.actCheckbox.selected || this.activeDesactive))     {
    //   this.win =  SwtPopUpManager.createPopUp(this, FourEyesProcess, {
    //     title:'Four Eyes Process',
    //   });
    //
    //   this.win.enableResize = false;
    //   this.win.width = '510';
    //   this.win.height = '215';
    //   this.win.showControls = true;
    //   this.win.isModal = true;
    //   this.win.onClose.subscribe((res) => {
    //     if (this.win.getChild().result) {
    //       if(this.win.getChild().result.login == "SUCCESS") {
    //         this.save();
    //       }
    //     }
    //   });
    //   this.win.display();
    // }else {
    //   this.save();
    // }
  }

  /**
   * save()
   * Method called on save button clicked
   */
  save(): void {
    this.actionPath='categoryPCM.do?';
    this.logicUpdate.cbResult = (data) => {
      this.logicUpdateResult(data);
    };
    this.logicUpdate.cbFault = this.inputDataFault.bind(this);
    this.logicUpdate.encodeURL = false;

    this.requestParams=[];
    try {
      if (this.screenName == "add") {
        this.actionMethod="method=save";
      } else {
        this.actionMethod="method=update";
      }
      if(this.operationsList.toString() != "<operationsList/>") {
        this.requestParams['xmlData'] = this.operationsList.toString();
      }
      if(this.urgent.selected) {
        this.requestParams["releaseTime"]= this.relTimeGroup.selectedValue;
        if(this.radioT.selected) {
          this.requestParams["specifiedTime"]= this.timeInput.text;
        }
      }
      this.requestParams["isActive"]= this.actCheckbox.selected ? "Y" : "N";
      this.requestParams["ordinal"]= (this.ordinalNumInput.text)? this.ordinalNumInput.text: "" ;
      this.requestParams["ruleAssignPriority"]= (this.ruleAssignPriorityInput.text)? this.ruleAssignPriorityInput.text: "1" ;
      this.requestParams["categoryId"]=this.categoryIdTxtInput.text;
      this.requestParams["categoryName"]=this.categoryNameTxtInput.text;
      this.requestParams["instantRelease"] = this.instRelGroup.selectedValue;
      this.requestParams["includeInTarget"]=this.incTargCheckBox.selected ? "Y" : "N";
      this.requestParams["includeInLiquidity"]=this.incLiqCheckBox.selected ? "Y" : "N";
      this.requestParams["useLiquidity"]=this.useliqCheckBox.selected == true ? "Y" : "N";
      this.requestParams["assignMethod"]=this.assigMetGroup.selectedValue;
      this.requestParams["offsetDays"]= (this.offsetNum.text) ? this.offsetNum.text : "" ;
      this.logicUpdate.url=this.baseURL + this.actionPath + this.actionMethod;
      this.logicUpdate.send(this.requestParams);
      this.operationsList = new XML( "<operationsList/>");
    } catch (error) {
      console.log(error, this.moduleId, "CategoryMaintenance", "save");
      SwtUtil.logError(error, this.moduleId, 'CategoryMaintenance', 'save', this.errorLocation);
    }

  }


  xmlData(): void {
    if(this.ruleGridList) {
      let row;
      for (let i= 0; i < this.ruleGridList.getValues().length; i++) {
        row = [];
        if (this.screenName != "add") {
          row['RULE_ID'] = this.ruleGridList.getValues()[i].crud_data.ruleId;
          row['CATEGORY_RULE_ID'] = this.ruleGridList.getValues()[i].crud_data.categoryRuleId;
        }

        // row['APPLY_ONLY_TO_SOURCE'] = this.ruleGridList.getValues()[i].crud_data.toSource  ;
        // row['APPLY_ONLY_TO_CCY'] =  this.ruleGridList.getValues()[i].crud_data.toCcy ;
        // row['APPLY_ONLY_TO_ENTITY'] =this.ruleGridList.getValues()[i].crud_data.toEntity ;

        row['APPLY_ONLY_TO_SOURCE'] = typeof(this.ruleGridList.getValues()[i].crud_data.toSource) == "string" ? this.ruleGridList.getValues()[i].crud_data.toSource : "" ;
        row['APPLY_ONLY_TO_CCY'] = typeof(this.ruleGridList.getValues()[i].crud_data.toCcy) == "string" ? this.ruleGridList.getValues()[i].crud_data.toCcy : "" ;
        row['APPLY_ONLY_TO_ENTITY'] =typeof(this.ruleGridList.getValues()[i].crud_data.toEntity) == "string" ? this.ruleGridList.getValues()[i].crud_data.toEntity : "" ;
        row['ORDINAL'] = this.ruleGridList.getValues()[i].crud_data.ordinal;
        row['NAME'] = this.ruleGridList.getValues()[i].crud_data.categoryRuleName;
        row['RULE_TYPE'] = "G";
        row['RULE_QUERY'] = this.ruleGridList.getValues()[i].crud_data.ruleQuery;
        row['RULE_TEXT'] = this.ruleGridList.getValues()[i].crud_data.ruleText;
        row['TAB_CONDITION'] = this.ruleGridList.getValues()[i].crud_data.ruleConditions;
        if (this.ruleGridList.getValues()[i].crud_operation == "I") {
          this.operationsList.appendChild(StringUtils.getKVTypeTabAsXML(row, 'PC_CATEGORY_RULE', 'I', 'M'));
          this.changesInRule = true;
        }
        if (this.ruleGridList.getValues()[i].crud_operation.substring(0, 1) == "U") {
          this.operationsList.appendChild(StringUtils.getKVTypeTabAsXML(row, 'PC_CATEGORY_RULE', 'U', 'M'));
          this.changesInRule = this.ruleGridList.getValues()[i].crud_operation.indexOf("ruleQuery")!= -1 ||this.ruleGridList.getValues()[i].crud_operation.indexOf("ruleText")!= -1  || this.ruleGridList.getValues()[i].crud_operation.indexOf("ruleConditions")!= -1;
        }
        if (this.ruleGridList.getValues()[i].crud_operation.substring(0, 1) == "D") {
          this.operationsList.appendChild(StringUtils.getKVTypeTabAsXML(row, 'PC_CATEGORY_RULE', 'D', 'M'));
          this.changesInRule = true;
        }
      }
    }

  }

  /**
   * logicUpdateResult
   *
   * @param event: ResultEvent
   *
   * Method to get result of the group rules
   */
  logicUpdateResult(event): void {
    try {
      let message;
      if (this.logicUpdate.isBusy()) {
        this.logicUpdate.cbStop();
      } else {
        const JsonResponse = event;
        const JsonResult: JSONReader = new JSONReader();
        JsonResult.setInputJSON(JsonResponse);
        if (JsonResult.getRequestReplyMessage() == "RECORD_EXIST") {
          message = SwtUtil.getPredictMessage('errors.DataIntegrityViolationExceptioninAdd', null);
          this.swtAlert.error(message);
        } else if (JsonResult.getRequestReplyMessage() == "ERROR_SAVE") {
          message = SwtUtil.getPredictMessage('alert.contactAdminForcategoryMaintenance', null);
          this.swtAlert.error(message);
        } else if (JsonResult.getRequestReplyMessage() == "ERROR_NAME_RULE") { // Category Rule Name Already Exists!
          message = SwtUtil.getPredictMessage('errors.CouldNotSaveCategoryRuleNameWithSameNameExceptioninAdd', null);
          this.swtAlert.error(message);
        } else {
          if(window.opener.instanceElement) {
            window.opener.instanceElement.updateData();
            this.popupClosed();
          }
        }
      }

    } catch (e) {
      // log the error in ERROR LOG
      SwtUtil.logError(e, this.moduleId, 'CategoryMaintenance', 'logicUpdateResult', this.errorLocation);
    }
  }


  disableOrEnableButtons(isRowSelected: boolean): void {
    try {
      this.enableChangeButton(isRowSelected);
      this.enableViewButton(isRowSelected);
      this.enableDeleteButton(isRowSelected);
    } catch (e) {
      // log the error in ERROR LOG
      SwtUtil.logError(e, this.moduleId, 'CategoryMaintenance', 'disableOrEnableButtons', this.errorLocation);
    }

  }

  disableButtons() {
    if(this.rulesGrid.selectedIndex == -1) {
      this.disableOrEnableButtons(false);
    } else {
      this.disableOrEnableButtons(true);
    }
  }

  enableButtons() {
    this.saveButton.enabled = true;
  }

  /**
   * enableAddButton
   *
   */
  enableAddButton(value): void {
    this.addRuleButton.enabled = value;
    this.addRuleButton.buttonMode = value;
  }
  /**
   * enableChangeButton
   *
   */
  enableChangeButton(value): void {
    this.changeRuleButton.enabled = value;
    this.changeRuleButton.buttonMode = value;
  }
  /**
   * enableViewButton
   *
   */
  enableViewButton(value): void {
    this.viewRuleButton.enabled = value;
    this.viewRuleButton.buttonMode = value;
  }
  /**
   * enableDeleteButton
   *
   */
  enableDeleteButton(value): void {
    this.deleteRuleButton.enabled = value;
    this.deleteRuleButton.buttonMode = value;
  }
  public refreshGrid() {
    this.rulesGrid.selectedIndex = -1;
    this.rulesGrid.selectable= true;
    this.disableOrEnableButtons(false);
  }

  /**
   * disableComponents()
   *
   * Method called to disable components
   *
   */
  disableComponents(value): void {
    try {

    } catch (e) {
      // log the error in ERROR LOG
      SwtUtil.logError(e, this.moduleId, 'CategoryMaintenance', 'disableComponents', this.errorLocation);
    }
  }

  /**
   * keyDownEventHandler
   * param event: KeyboardEvent
   * This is a key event listener, used to perform the operation
   * when hit the enter key based on the currently focused property(button)
   */
  keyDownEventHandler(event): void {
    try {
      if (event) {
        // Currently focussed property name
        let eventString: string = Object(focusManager.getFocus()).name;
        if ((event.keyCode == Keyboard.ENTER)) {
          if (eventString == "saveButton") {
            this.checkPriorityBeforeSave();
          } else if (eventString == "cancelButton") {
            this.popupClosed();
          }
        }
      }
    } catch (error) {
      // log the error in ERROR LOG
      console.log(error, this.moduleId, "CategoryMaintenance", "keyDownEventHandler");
    }
  }


  /**
   * Used to validate time input (HH:mm)
   */
  validateTime(textInput: SwtTextInput): boolean {
    if(textInput.text.endsWith(":")) {
      textInput.text = textInput.text + "00";
    }
    if (validateFormatTime(textInput) == false) {
      this.swtAlert.warning('Please enter a valid time', null);
      textInput.text = "";
      return false;
    } else {
      textInput.text = textInput.text.substring(0,5);
      return true;
    }
  }
  validateUseLiquidity(): boolean {
    if(this.screenName !== 'view') {
    if(this.offsetNum.text === "") {
      this.useliqCheckBox.enabled = true;
    } else {
      if(this.offsetNum.text >= 1  ) {
        this.useliqCheckBox.enabled = false;
        this.useliqCheckBox.selected = false;
        return false;
      } else {
        this.useliqCheckBox.enabled = true;
        return true;
      }
    }
    }
  }

  /**
   * focusOutOrdinalInput
   * This is a key event listener, used to perform the operation
   * when hit the enter key based on the currently focused property(button)
   */
  // focusOutOrdinalInput():void {
  //   try {
  //     if(this.ordinal !== Number(this.ordinalNumInput.text)){
  //       Alert.yesLabel = 'Yes';
  //       Alert.noLabel = 'No';
  //       const message: string ="This order already exists with another category, do you wish to reorder?";
  //       if(this.screenName =="add"){
  //         if(!(Number(this.ordinalNumInput.text) >= this.maxOrder )){
  //           if( this.listOrder.indexOf(Number(this.ordinalNumInput.text)) !== -1){
  //             this.swtAlert.confirm(message, 'Alert', Alert.YES | Alert.NO, null, this.ordinalAlertListener.bind(this));
  //           }
  //         }
  //       }else{//change
  //         if( this.listOrder.indexOf(this.ordinalNumInput.text) !== -1){
  //           this.swtAlert.confirm(message, 'Alert', Alert.YES | Alert.NO, null, this.ordinalAlertListener.bind(this));
  //         }
  //       }
  //     }
  //
  //   } catch (error) {
  //     // log the error in ERROR LOG
  //     console.log(error, this.moduleId, "CategoryMaintenance", "focusOutOrdinalInput");
  //   }
  // }

  /**
   * ordinalAlertListener
   * @param event:
   *
   * Method to Alert
   */
  ordinalAlertListener(event): void {
    try {
      if (event.detail === Alert.YES) {
        this.reOrder = true;
      } else {
        this.reOrder = false;
        if(this.screenName=="add") {
          this.ordinalNumInput.text=this.maxOrder+1;
        } else {
          let order= String(this.jsonReader.getSingletons().ordinal);
          if(order == "") {
            this.ordinalNumInput.text = this.maxOrder+1;
          } else {
            this.ordinalNumInput.text=this.jsonReader.getSingletons().ordinal;
          }
        }

      }
    } catch (error) {
      // log the error in ERROR LOG
      console.log(error, this.moduleId, "CategoryMaintenance", "ordinalAlertListener");
    }
  }

  /**
   * changeEventHandler
   * param event:
   */
  changeInstantRelease(): void {
    if(this.urgent.selected) {
      this.validateUseLiquidity();
    } else {
      this.useliqCheckBox.enabled = true;
    }
    this.gridRowRelTime.visible = this.urgent.selected;
    this.gridRowRelTime.includeInLayout = this.urgent.selected;
    this.gridRowOffsetDays.visible=this.urgent.selected;
    this.gridRowOffsetDays.includeInLayout=this.urgent.selected;
    if(this.spread.selected) {
      this.radioT.selected = false;
      this.timeInput.visible= false;
      this.timeInput.includeInLayout= false;
      this.radioK.selected = true;
    }

  }

  /**
   * activeEventHandler
   * param event:
   */
  activeEventHandler(): void {
   // this.activeDesactive= this.actCheckbox.selected !== this.stateBeforeChange;
  }


  /**
   * doAddRule
   * param event:
   */
  doAddCategoryRule(event): void {
    try {
      this.rulesGrid.selectedIndex= -1;
      this.childScreenName = 'add';
      this.saveButton.enabled = false;
      // let newWindow = window.open("/categoryRuleAdd", 'Category Rule Add', 'height=420,width=850,left=100,top=100,resizable=yes,scrollbars=yes,toolbar=yes,menubar=no,location=no,directories=no, status=yes' );
      // if (window.focus) {
      //   newWindow.focus();
      // }
      ExternalInterface.call('openChildWindow', 'categoryRuleAdd');
    } catch (error) {
      // log the error in ERROR LOG
      console.log(error, this.moduleId, "CategoryMaintenance", "doAddRule");
    }
  }


  /**
   * doChangeRule
   * param event:
   */
  doChangeCategoryRule(event): void {
    try {
      let index =  this.rulesGrid.dataProvider.findIndex(x=>x.num == this.rulesGrid.selectedItem.num.content );
      this.childScreenName = 'change';
      this.categoryRuleId= this.rulesGrid.dataProvider[index].categoryRuleId;
      this.saveButton.enabled = false;
      // let newWindow = window.open("/categoryRuleAdd", 'Category Rule Add', 'height=420,width=850,left=100,top=100,resizable=yes,scrollbars=yes,toolbar=yes,menubar=no,location=no,directories=no, status=yes' );
      // if (window.focus) {
      //   newWindow.focus();
      // }
      ExternalInterface.call('openChildWindow', 'categoryRuleAdd');

    } catch (error) {
      // log the error in ERROR LOG
      console.log(error, this.moduleId, "CategoryMaintenance", "doChangeRule");
    }
  }


  /**
   * doViewRule
   * param event:
   */
  doViewCategoryRule(event): void {
    try {
      let index =  this.rulesGrid.dataProvider.findIndex(x=>x.num == this.rulesGrid.selectedItem.num.content );
      this.childScreenName = 'view';
      this.categoryRuleId= this.rulesGrid.dataProvider[index].categoryRuleId;
      // let newWindow = window.open("/categoryRuleAdd", 'Category Rule Add', 'height=420,width=850,left=100,top=100,resizable=yes,scrollbars=yes,toolbar=yes,menubar=no,location=no,directories=no, status=yes' );
      // if (window.focus) {
      //   newWindow.focus();
      // }
      ExternalInterface.call('openChildWindow', 'categoryRuleAdd');
    } catch (error) {
      // log the error in ERROR LOG
      console.log(error, this.moduleId, "CategoryMaintenance", "doViewCategoryRule");
    }
  }

  refreshParent(reorderCr,ordinalIni, categoryRuleId, categoryRuleName, ordinal, toSource, toCcy,toEntity, ruleId, queryText, ruleQuery, tabConditions, tabToJoin) {
    try {
      let index;
      let array=[];
      if(reorderCr == true) {
        for(let i=0; i< this.rulesGrid.dataProvider.length; i++) {
          if((Number(this.rulesGrid.dataProvider[i].ordinal) >= Number(ordinal)) && Number(this.rulesGrid.dataProvider[i].ordinal) != ordinalIni ) {
            this.rulesGrid.dataProvider[i].ordinal= Number(this.rulesGrid.dataProvider[i].ordinal)+1;
            this.rulesGrid.dataProvider[i]['slickgrid_rowcontent']['ordinal'] = {content :  Number(this.rulesGrid.dataProvider[i].ordinal)+1};
            array.push(Number(this.rulesGrid.dataProvider[i].ordinal));
          }
        }
        this.listOrderCR = array;
      }
      if(this.childScreenName == 'add') {
        let newRow = {
          categoryRuleName : {content : categoryRuleName},
          ordinal :{content :  String(ordinal)},
          toSource :{content :  String(toSource)},
          toCcy :{content :  String(toCcy)},
          toEntity :{content :  String(toEntity)},
          ruleText :{content :  String(queryText)},
          ruleQuery :{content :  String(ruleQuery)},
          tabToJoin :{content :  String(tabToJoin)},
          ruleConditions :{content : tabConditions}
      };
        this.rulesGrid.appendRow(newRow);
       // index = this.rulesGrid.dataProvider.length-1;
      } else {
        index =   this.rulesGrid.dataProvider.findIndex(x=>x.num == this.rulesGrid.selectedItem.num.content );
        this.rulesGrid.dataProvider[index].categoryRuleId = Number(categoryRuleId);
        this.rulesGrid.dataProvider[index].ruleId = ruleId;
        this.rulesGrid.dataProvider[index].categoryRuleName = categoryRuleName;
        this.rulesGrid.dataProvider[index].ordinal = ordinal;
        this.rulesGrid.dataProvider[index].toSource = toSource;
        this.rulesGrid.dataProvider[index].toCcy = toCcy;
        this.rulesGrid.dataProvider[index].toEntity = toEntity;
        this.rulesGrid.dataProvider[index].ruleText = queryText;
        this.rulesGrid.dataProvider[index].ruleQuery = ruleQuery;
        this.rulesGrid.dataProvider[index].tabToJoin = tabToJoin;
        this.rulesGrid.dataProvider[index].ruleConditions = tabConditions;
        this.rulesGrid.dataProvider[index]['slickgrid_rowcontent']['categoryRuleName'] = {content : categoryRuleName};
        this.rulesGrid.dataProvider[index]['slickgrid_rowcontent']['ordinal'] = {content : ordinal};
        this.rulesGrid.dataProvider[index]['slickgrid_rowcontent']['toSource'] = {content : toSource};
        this.rulesGrid.dataProvider[index]['slickgrid_rowcontent']['toCcy'] = {content : toCcy};
        this.rulesGrid.dataProvider[index]['slickgrid_rowcontent']['toEntity'] = {content : toEntity};
        this.rulesGrid.dataProvider[index]['slickgrid_rowcontent']['ruleText'] = {content : queryText};
        this.rulesGrid.dataProvider[index]['slickgrid_rowcontent']['ruleQuery'] = {content : ruleQuery};
        this.rulesGrid.dataProvider[index]['slickgrid_rowcontent']['tabToJoin'] = {content : tabToJoin};
        this.rulesGrid.dataProvider[index]['slickgrid_rowcontent']['ruleConditions'] = {content : tabConditions};
        this.rulesGrid.refresh();
      }

      this.listOrderCR.push(Number(ordinal));
      this.maxOrderCR = Math.max.apply(null,this.listOrderCR);
      this.maxOrderCR += 1;
      this.tableToJoinQueryBuilder = JSON.parse(tabToJoin);
        // this.rulesGrid.refresh();
      this.rulesGrid.selectedIndex = -1 ;

    } catch(error) {
      console.log(error, this.moduleId, "CategoryMaintenance", "refreshParent");
    }
  }

  getParamsFromParent() {
    let params = [];
    if(this.from == 'Spread') {
      this.rulesGrid.selectedIndex= -1;
      this.spreadId = this.spreadGrid.selectedItem.spreadId.content;
      params = [{screenName: "view", spreadId:this.spreadId }];
      this.from="";

    } else {
      if(this.childScreenName == "add") {
        params = [
          { screenName: this.childScreenName, categoryRuleId: '', ruleId: '', dataProviderSelectedIndex:[], maxOrder: this.maxOrderCR,  listOrder: this.listOrderCR, listPriority: this.listRuleAssignPriority}];
      } else {
        let index =   this.rulesGrid.dataProvider.findIndex(x=>x.num == this.rulesGrid.selectedItem.num.content);
        this.categoryRuleId= this.rulesGrid.dataProvider[index].categoryRuleId;
        this.ruleId=this.rulesGrid.dataProvider[index].ruleId;
        params = [
          { screenName: this.childScreenName, categoryRuleId:  this.categoryRuleId, ruleId: this.ruleId ,  dataProviderSelectedIndex:this.rulesGrid.dataProvider[index], maxOrder: this.maxOrderCR,  listOrder: this.listOrderCR, listPriority: this.listRuleAssignPriority},
        ];
      }
    }
    return params;
  }

  /**
   * doDeleteRule
   *
   * @param event: Event
   *
   * Method to pop up delete confirmation
   *
   */
  doDeleteCategoryRule(event): void {
    try {
      Alert.yesLabel = "Yes";
      Alert.noLabel = "No";
      const message = "Do you wish to delete this row?";
      this.swtAlert.confirm(message, 'Alert', Alert.OK | Alert.CANCEL, null, this.deleteRule.bind(this));
    } catch (e) {
      // log the error in ERROR LOG
      SwtUtil.logError(e, this.moduleId, 'CategoryMaintenance', 'doDeleteRule', this.errorLocation);
    }
  }
  /**
   * deleteRule
   *
   * @param event:
   *
   * Method to remove selected Category Rule
   */
  deleteRule(event): void {
    try {
      let index =   this.rulesGrid.dataProvider.findIndex(x=>x.num == this.rulesGrid.selectedItem.num.content);
      // Condition to check Ok Button is selected
      if (event.detail === Alert.OK) {
        let order = this.rulesGrid.dataProvider[index].ordinal;
        this.removeElement(this.listOrderCR, order);
        if(this.listOrderCR.length ==0) {
          this.maxOrderCR =1;
        } else {
          this.maxOrderCR = Math.max.apply(null,this.listOrderCR);
          this.maxOrderCR ++;
        }
        this.rulesGrid.removeSelected();
        // this.rulesGrid.refresh();
        this.rulesGrid.selectedIndex = -1;
        this.disableOrEnableButtons(false);
      }
    } catch (e) {
      SwtUtil.logError(e, this.moduleId, 'CategoryMaintenance', 'deleteRule', this.errorLocation);
    }
  }

  removeElement(array, element) {
    let index = array.indexOf(Number(element));
    if (index >= -1) {
      array.splice(index, 1);
      this.listOrderCR = array;
    }
  }

  /**
   * changeTimeGroup
   * param event:
   */
  changeTimeGroup(event): void {
    if(this.radioT.selected) {
      this.timeInput.visible= true;

    } else {
      this.timeInput.visible= false;
      this.timeInput.text="";
    }

  }



  /**
   * popupClosed
   * Method to close child windows when this screen is closed
   */
  popupClosed(): void {
    this.dispose();
  }

  /**
   * dispose
   * This is a event handler, used to close the current tab/window
   */
  dispose(): void {
    try {
      this.requestParams=null;
      this.baseURL=null;
      this.actionMethod=null;
      this.actionPath=null;
      if(window.opener.instanceElement) {
        window.opener.instanceElement.refreshGrid();
      }
      if(this.titleWindow) {
        this.close();
      } else {
        window.close();
      }
    } catch (error) {
      console.log(error, this.moduleId, "CategoryMaintenance", "dispose");
    }
  }

  checkIfPriorityExists(){
    Alert.yesLabel = SwtUtil.getPredictMessage('alert.yes.label');
    Alert.noLabel = SwtUtil.getPredictMessage('alert.no.label');
    const message: string = SwtUtil.getPredictMessage('alert.CategoryMaintenanceAddRulePriority', null);
    if (this.ruleAssignPriorityInput.text) {
      if (this.ruleAssignPriorityInput.text == '0') {
        this.ruleAssignPriorityInput.text = this.ruleAssignPriority ? this.ruleAssignPriority : "1";
      }
      else if (this.pcmInputFlag != 'N' && (this.listRuleAssignPriority.indexOf(Number(this.ruleAssignPriorityInput.text)) !== -1)) {
        this.swtAlert.confirm(message, SwtUtil.getPredictMessage('alert_header.confirm'), Alert.YES | Alert.NO, null, this.priorityAlertListener.bind(this));
      }

    }
}

  /**
   * priorityAlertListener
   * @param event:
   *
   * Method to Alert
   */
  priorityAlertListener(event):void {
      if (event.detail === Alert.NO) {
        this.ruleAssignPriorityInput.text=this.ruleAssignPriority?this.ruleAssignPriority:"1";
      }
  }

  checkPriorityBeforeSave(){
    Alert.yesLabel = SwtUtil.getPredictMessage('alert.yes.label');
    Alert.noLabel = SwtUtil.getPredictMessage('alert.no.label');
    const message: string = SwtUtil.getPredictMessage('alert.CategoryMaintenanceAddRulePriority', null);
    if(this.ruleAssignPriorityInput.text){
    if( this.pcmInputFlag !='N' && (this.listRuleAssignPriority.indexOf(Number(this.ruleAssignPriorityInput.text)) !== -1)){
    this.swtAlert.confirm(message, SwtUtil.getPredictMessage('alert_header.confirm'), Alert.YES | Alert.NO, null, this.savePriorityAlertListener.bind(this));  
    }else{
    this.saveEventHandler(); 
    }
  }else{
    this.saveEventHandler(); 
    }
}

savePriorityAlertListener(event):void {
  if (event.detail === Alert.NO) {
    this.ruleAssignPriorityInput.text=this.ruleAssignPriority?this.ruleAssignPriority:"1";
  }else{
    this.saveEventHandler();
  }
}

}

// Define lazy loading routes
const routes: Routes = [
  { path: '', component: CategoryMaintenanceAdd }
];
export const routing: ModuleWithProviders = RouterModule.forChild(routes);
// Define an NgModule that will be loaded by app module
@NgModule({
  imports: [routing, SwtToolBoxModule],
  declarations: [CategoryMaintenanceAdd],
  entryComponents: []
})
export class CategoryMaintenanceAddModule {}