<SwtModule (creationComplete)="onLoad()" width="100%" height="100%">
  <VBox width="100%" height="100%">
    <HBox width="100%" height="30%" paddingLeft="10" paddingTop="10">
      <VBox width="30%" verticalGap="0">
        <HBox>
          <SwtLabel id="kickoffLabel" #kickoffLabel width="270"></SwtLabel>
          <SwtTextInput #kickoffTimeInput pattern="^(0[0-9]|1[0-9]|2[0-3]|[0-9]):[0-5][0-9]$"
                        (focusOut)="validateTime(kickoffTimeInput)" width="70" textAlign="center"></SwtTextInput>
        </HBox>
        <HBox>
          <SwtLabel id="eodLabel" #eodLabel  width="270"></SwtLabel>
          <SwtTextInput #eodBeginTimeInput pattern="^(0[0-9]|1[0-9]|2[0-3]|[0-9]):[0-5][0-9]$"
                        (focusOut)="validateTime(eodBeginTimeInput)" width="70" maxChars="5" textAlign="center"></SwtTextInput>
        </HBox>
        <HBox>
          <SwtLabel id="cobLabel" #cobLabel width="270"></SwtLabel>
          <SwtTextInput #cobTimeInput pattern="^(0[0-9]|1[0-9]|2[0-3]|[0-9]):[0-5][0-9]$"
                        (focusOut)="validateTime(cobTimeInput)" width="70" textAlign="center" maxChars="5"></SwtTextInput>
        </HBox>
      </VBox>
      <VBox width="2%" verticalGap="6" marginTop="8">
        <div style="border: 2px solid black; width: 5px; height: 32px; border-left: none; padding-top: 5px" ></div>
        <div style="border: 2px solid black; width: 5px; height: 32px; border-left: none; margin-top: 5px"></div>
      </VBox>
      <VBox width="68%" verticalGap="0">
        <HBox paddingTop="10">
          <SwtLabel #intradayLabel width="230" ></SwtLabel>
        </HBox>
        <HBox paddingTop="10">
          <SwtLabel #endOfDayLabel width="230"></SwtLabel>
        </HBox>
      </VBox>

    </HBox>
    <HBox width="100%" height="70%" paddingLeft="10">
      <SwtCanvas id="cutOffCanvas" #cutOffCanvas width="92%" height="100%"></SwtCanvas>
      <VBox id="gridButtons" #gridButtons height="100%" width="8%" horizontalAlign="center" verticalAlign="middle" verticalGap="5">
        <SwtButton #addRow (click)="addRowCutOff()" width="60" enabled="true" marginBottom="6"></SwtButton>
        <SwtButton #changeRow (click)="changeRowCutOff()" width="60" enabled="false" marginBottom="6"></SwtButton>
        <SwtButton #viewRow (click)="viewRowCutOff()"
                   label="View" width="60" enabled="false"  marginBottom="6"></SwtButton>
        <SwtButton #deleteRow (click)="deleteRowCutOff()" width="60" enabled="false"></SwtButton>
      </VBox>
    </HBox>
  </VBox>
</SwtModule>
