<SwtModule (close)='popupClosed()' (creationComplete)='onLoad()'   height='100%'  title='{{title}}' width='100%'>
  <VBox id="vBox1" width='100%' height='100%' paddingBottom="5" paddingLeft="5" paddingTop="5" paddingRight="5">
    <SwtCanvas width="100%" height="85%">
      <Grid width="100%" height="100%">
        <GridRow>
          <GridItem width="40%">
            <SwtLabel  #currencyLabel>
            </SwtLabel>
          </GridItem>
          <GridItem width="60%">
            <SwtComboBox id="currencyComboBox"
                         #currencyComboBox
                         dataLabel="currencyList"
                         width="250"
                         (change)="changeComboCurrency($event)"
                         required="true">
            </SwtComboBox>
          </GridItem>
        </GridRow>
        <GridRow>
          <GridItem width="40%">
            <SwtLabel  #currencyNameLabel>
            </SwtLabel>
          </GridItem>
          <GridItem width="60%">
            <SwtTextInput id="currencyNameTxtInput"
                          #currencyNameTxtInput
                          width="250">
            </SwtTextInput>
          </GridItem>
        </GridRow>
        <GridRow>
          <GridItem width="40%">
            <SwtLabel  #ordinalLabel>
            </SwtLabel>
          </GridItem>
          <GridItem width="60%">
            <SwtNumericInput  class="numericInput" id="ordinalNumInput"
                              #ordinalNumInput
                              height="22"
                              width="60"
                              (focusOut)="focusOutOrdinalInput()"
                              textAlign="right">
            </SwtNumericInput>
          </GridItem>
        </GridRow>
        <GridRow>
          <GridItem width="40%">
            <SwtLabel  #multiplierLabel>
            </SwtLabel>
          </GridItem>
          <GridItem width="60%">
            <SwtComboBox id="multiplierComboBox"
                         #multiplierComboBox
                         dataLabel="multiplierList"
                         width="250">
            </SwtComboBox>
          </GridItem>
        </GridRow>
        <GridRow>
          <GridItem width="40%">
            <SwtLabel   #largeAmtLabel>
            </SwtLabel>
          </GridItem>
          <GridItem width="60%">
            <SwtTextInput  class="numericInput" id="largeAmtNumInput"
                           #largeAmtTextInput
                           height="22"
                           (focusOut)="validateAmount()"
                           textAlign="right"
                           width="250">
            </SwtTextInput>
          </GridItem>
        </GridRow>
      </Grid>

    </SwtCanvas>
    <SwtCanvas id="canvasButtons" width="100%" height="15%">
      <HBox  width="100%" height="100%">
        <HBox paddingLeft="5" width="100%">
          <SwtButton #saveButton
                     (click)="save()"
                     (keyDown)="keyDownEventHandler($event)"
                     id="saveButton">
          </SwtButton>
          <SwtButton buttonMode="true"
                     id="cancelButton"
                     #cancelButton
                     (click)="popupClosed();"
                     (keyDown)="keyDownEventHandler($event)">
          </SwtButton>
        </HBox>
        <HBox horizontalAlign="right" paddingRight="5">
          <SwtHelpButton id="helpIcon"
                         [buttonMode]="true"
                         enabled="true"
                         (click)="doHelp()">
          </SwtHelpButton>
        </HBox>
      </HBox>
    </SwtCanvas>
  </VBox>
</SwtModule>
