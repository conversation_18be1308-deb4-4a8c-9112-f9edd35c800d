import {
  Compo<PERSON>,
  ElementRef, ModuleWithProviders, NgModule,
  ViewChild
} from '@angular/core';
import {
  HTTPComms,
  SwtButton,
  JSONReader,
  SwtAlert,
  SwtUtil,
  SwtTextInput,
  SwtLoadingImage,
  CommonService,
  SwtComboBox,
  Keyboard,
  focusManager, SwtModule, SwtNumericInput, Alert, ExternalInterface, TitleWindow, SwtLabel, SwtToolBoxModule
} from 'swt-tool-box';
import {RouterModule, Routes} from "@angular/router";
declare  function validateCurrencyPlaces(strField, strPat, currCode): any;

@Component({
  selector: 'app-currency-maintenance-add',
  templateUrl: './CurrencyMaintenanceAdd.html',
  styleUrls: ['./CurrencyMaintenanceAdd.css']
})
export class CurrencyMaintenanceAdd extends  SwtModule {
  private swtAlert: SwtAlert;

  /**
   * Data Objects
   **/
  public jsonReader= new JSONReader();
  public lastRecievedJSON;
  public prevRecievedJSON;


  /**
   * Communication Objects
   **/
  private  inputData = new HTTPComms(this.commonService);
  private logicUpdate = new HTTPComms(this.commonService);
  private baseURL =  SwtUtil.getBaseURL();
  private  actionMethod= "";
  private  actionPath= "";
  private  requestParams = [];
  private  message: string=null;
  public screenName: string = null;
  public helpURL: string=null;
  public  title: string=null;
  public searchQuery="";
  public queryToDisplay="";
  public errorLocation=0;
  public ccy="";
  public ccyName="";
  public listOrder;
  public maxOrder;
  private win: TitleWindow ;
  public currencyPattern: string;
  /* - START -- Screen Name and Version number ---- */
  private moduleName = 'PC Currency Maintenance Details';
  private versionNumber = '1.00.00';
  private releaseDate = '20 Feb 2019';
  private moduleId='';
  private ordinal= null;
  private reOrder = false;
  /************SwtNumericInput********/
  @ViewChild('ordinalNumInput') ordinalNumInput: SwtNumericInput;

  /************SwtTextInput********/
  @ViewChild('currencyNameTxtInput') currencyNameTxtInput: SwtTextInput;
  @ViewChild('largeAmtTextInput') largeAmtTextInput: SwtTextInput;
  /*********Combobox*********/
  @ViewChild('multiplierComboBox') multiplierComboBox: SwtComboBox;
  @ViewChild('currencyComboBox') currencyComboBox: SwtComboBox;
  /***LodingImage*******/
  @ViewChild('loadingImage') loadingImage: SwtLoadingImage;
  /*********SWtButton*************/
  @ViewChild('saveButton') saveButton: SwtButton;
  @ViewChild('cancelButton') cancelButton: SwtButton;
  /*********SwtLabel*************/
  @ViewChild('currencyLabel') currencyLabel: SwtLabel;
  @ViewChild('currencyNameLabel') currencyNameLabel: SwtLabel;
  @ViewChild('ordinalLabel') ordinalLabel: SwtLabel;
  @ViewChild('multiplierLabel') multiplierLabel: SwtLabel;
  @ViewChild('largeAmtLabel') largeAmtLabel: SwtLabel;

  constructor(private commonService: CommonService, private element: ElementRef) {
    super(element,commonService);
    this.swtAlert = new SwtAlert(commonService);
  }



  ngOnInit(): void {
    try {
      this.currencyComboBox.toolTip = SwtUtil.getPredictMessage('currencyMaintenanceDetails.tooltip.ccy', null);
      this.currencyLabel.text = SwtUtil.getPredictMessage('currencyMaintenanceDetails.label.ccy', null);
      this.currencyNameLabel.text = SwtUtil.getPredictMessage('currencyMaintenanceDetails.label.name', null);
      this.ordinalNumInput.toolTip = SwtUtil.getPredictMessage('currencyMaintenanceDetails.tooltip.ordinal', null);
      this.ordinalLabel.text = SwtUtil.getPredictMessage('currencyMaintenanceDetails.label.ordinal', null);
      this.multiplierLabel.text = SwtUtil.getPredictMessage('currencyMaintenanceDetails.label.multiplier', null);
      this.multiplierComboBox.toolTip = SwtUtil.getPredictMessage('currencyMaintenanceDetails.tooltip.multiplier', null);
      this.largeAmtLabel.text = SwtUtil.getPredictMessage('currencyMaintenanceDetails.label.largeAmount', null);
      this.largeAmtTextInput.toolTip = SwtUtil.getPredictMessage('currencyMaintenanceDetails.tooltip.largeAmount', null);
      this.saveButton.label =  SwtUtil.getPredictMessage('button.save', null);
      this.cancelButton.label =  SwtUtil.getPredictMessage('button.cancel', null);
      this.currencyNameTxtInput.enabled=false;
      this.currencyComboBox.enabled = false;
      this. saveButton.visible=true;
      if(this.screenName =="add") {
        this.currencyComboBox.enabled = true;
        this.currencyNameTxtInput.setFocus();
        this.multiplierComboBox.enabled = true;
        this.ordinalNumInput.enabled = true;
        this.ordinalNumInput.text = this.maxOrder;
        this.saveButton.enabled = false;
        this.saveButton.buttonMode = false;
      } else if(this.screenName =="change") {
        this.multiplierComboBox.enabled = true;
        this.ordinalNumInput.enabled = true;
      } else if(this.screenName =="view") {
        this.multiplierComboBox.enabled = false;
        this.ordinalNumInput.enabled = false;
        this.largeAmtTextInput.enabled= false;
        this.saveButton.visible=false;
        this.saveButton.includeInLayout=false;
      }
    } catch(error) {
      SwtUtil.logError(error, this.moduleId, "CurrencyMaintenance", "ngOnInit", this.errorLocation);
    }
  }


  /**
   *   onLoad()
   */
  onLoad(): void {
    try {
      // Checks the screen is add and calls the add action
      this.requestParams = [];
      this.actionPath = 'currencyPCM.do?';

      if(this.screenName != "add") {
        this.actionMethod='method=view';
        this.requestParams["ccy"]=this.ccy;
        this.requestParams["ccyName"]=this.ccyName;
      } else {
        this.actionMethod='method=add';
      }
      this.inputData.cbResult = (data) => {
        this.inputDataResult(data);
      };
      // this.inputData.cbFault=this.inputDataFault.bind(this);
      this.inputData.encodeURL=false;
      this.inputData.url=this.baseURL + this.actionPath + this.actionMethod;
      this.inputData.send(this.requestParams);

    } catch(error) {
      SwtUtil.logError(error, this.moduleId, "CurrencyMaintenance", "onLoad", this.errorLocation);
    }
  }

  /**
   * inputDataResult
   * param event: ResultEvent
   * This is a callback method, to handle result event
   *
   */
  inputDataResult(event): void {
    try {
      if (this.inputData.isBusy()) {
        this.inputData.cbStop();
      } else {
        /* Get result as xml */
        this.lastRecievedJSON =event;
        this.jsonReader.setInputJSON(this.lastRecievedJSON);

        /* Condition to check lastRecievedXML not equal to prevRecievedXML */
        if (!(JSON.stringify(this.lastRecievedJSON) === JSON.stringify(this.prevRecievedJSON))) {
          /* Condition to check request reply status is true*/
          if (this.jsonReader.getRequestReplyStatus()) {
            if (!this.jsonReader.isDataBuilding()) {
              this.currencyPattern  = this.jsonReader.getSingletons().currencyPattern;
              this.currencyComboBox.setComboDataAndForceSelected(this.jsonReader.getSelects(), false, "");
              this.multiplierComboBox.setComboData(this.jsonReader.getSelects(), true);
              if(this.screenName !== "add") {
                this.ordinal = Number(this.jsonReader.getSingletons().ordinal);
                this.currencyComboBox.selectedLabel=this.jsonReader.getSingletons().ccy;
                this.currencyComboBox.selectedItem.content=this.jsonReader.getSingletons().ccy;
                this.currencyNameTxtInput.text = String(this.jsonReader.getSingletons().ccyName);
                if(this.screenName === "change") {
                  if(String(this.jsonReader.getSingletons().ordinal) == "") {
                    this.ordinalNumInput.text = this.maxOrder;
                  } else {
                    this.ordinalNumInput.text = Number(this.jsonReader.getSingletons().ordinal);
                  }
                } else {
                  this.ordinalNumInput.text = this.jsonReader.getSingletons().ordinal;
                }
                this.multiplierComboBox.selectedLabel = this.jsonReader.getSingletons().multiplier;
                if(String(this.jsonReader.getSingletons().largeAmountThr) != "") {
                  this.largeAmtTextInput.text = this.jsonReader.getSingletons().largeAmountThr;
                }
              }
            }
          } else {
            this.swtAlert.error(this.jsonReader.getRequestReplyMessage());
          }
        }
      }
    } catch(error) {
      SwtUtil.logError(error, this.moduleId, "CurrencyMaintenance", "inputDataResult", this.errorLocation);
    }
  }



  /**
   * save()
   * Method called on save button clicked
   */
  save(): void {
    this.requestParams=[];
    try {
      if(!(this.currencyComboBox.selectedItem)) {
        this.swtAlert.error(SwtUtil.getPredictMessage('alert.mandatoryField', null));
        return;
      }
      if (!(validateCurrencyPlaces(this.largeAmtTextInput, this.currencyPattern, this.currencyComboBox.selectedLabel)) ) {
        this.swtAlert.warning(SwtUtil.getPredictMessage('alert.validAmount', null));
        return;
      }

      if (this.screenName == "add") {
        this.actionMethod="method=save";
      } else {
        this.actionMethod="method=update";
      }

      this.logicUpdate.cbResult = (event) => {
        this.logicUpdateResult(event);
      };

      this.requestParams["ordinal"]= this.ordinalNumInput.text;
      this.requestParams["multiplier"]=this.multiplierComboBox.selectedItem.value;
      this.requestParams["ccy"]=this.currencyComboBox.selectedItem.content;
      this.requestParams["largeAmountThr"]=this.largeAmtTextInput.text;
      this.requestParams["order"]= this.ordinal== null? this.maxOrder: this.ordinal;
      this.requestParams["reOrder"]= this.reOrder;
      this.logicUpdate.url=this.baseURL + this.actionPath + this.actionMethod;
      this.logicUpdate.send(this.requestParams);

    } catch (error) {
      console.error("", error);      // log the error in ERROR LOG
      SwtUtil.logError(error, this.moduleId, "CurrencyMaintenance", "save", this.errorLocation);
    }
  }


  /**
   * logicUpdateResult
   * @param event: ResultEvent
   *
   * Method to get result of the group rules
   */
  logicUpdateResult(event): void {
    try {
      const JsonResponse = event;
      const JsonResult: JSONReader = new JSONReader();
      JsonResult.setInputJSON(JsonResponse);
      if (JsonResult.getRequestReplyMessage() == "RECORD_EXIST") {
        this.swtAlert.error(SwtUtil.getPredictMessage('errors.DataIntegrityViolationExceptioninAdd', null));
      } else if (JsonResult.getRequestReplyMessage() == "ERROR_SAVE") {
        this.swtAlert.error(SwtUtil.getPredictMessage('currencyMaintenanceDetails.notSaved.alert', null));
      } else {
        this.updateData();
        this.popupClosed();
      }

    } catch (e) {
      // log the error in ERROR LOG
      SwtUtil.logError(e, this.moduleId, 'CurrencyMaintenance', 'logicUpdateResult', this.errorLocation);
    }
  }


  /**
   * Update the data, this is called whenever a fresh of the data is required.
   **/
  public updateData(): void {
    try {
      this.parentDocument.updateData();
    } catch (e) {
      // log the error in ERROR LOG
      SwtUtil.logError(e, SwtUtil.AML_MODULE_ID, 'CurrencyMaintenance', 'updateData',  this.errorLocation);
    }
  }

  /**
   * changeComboCurrency
   * Method to change a label entity
   */
  changeComboCurrency(event): void {
    this.currencyNameTxtInput.text = this.currencyComboBox.selectedItem.value;
    this.saveButton.enabled = this.currencyComboBox.selectedItem.content !== "";
    this.saveButton.buttonMode = this.currencyComboBox.selectedItem.content !== "";
  }

  /**
   * focusOutOrdinalInput
   * param event: KeyboardEvent
   * This is a key event listener, used to perform the operation
   * when hit the enter key based on the currently focused property(button)
   */
  focusOutOrdinalInput(): void {
    try {
      if(this.ordinal !== Number(this.ordinalNumInput.text)) {
        Alert.yesLabel = SwtUtil.getPredictMessage('alert.yes.label', null);
        Alert.noLabel = SwtUtil.getPredictMessage('alert.no.label', null);
        if(this.screenName =="add") {
          if(!(Number(this.ordinalNumInput.text) >= this.maxOrder )) {
            if( this.listOrder.indexOf(Number(this.ordinalNumInput.text)) !== -1) {
              this.swtAlert.confirm(SwtUtil.getPredictMessage('currencyMaintenanceDetails.reorder.alert', null), 'Alert', Alert.YES | Alert.NO, null, this.ordinalAlertListener.bind(this));
            }
          }
        } else {// change
          if( this.listOrder.indexOf(this.ordinalNumInput.text) !== -1) {
            this.swtAlert.confirm(SwtUtil.getPredictMessage('currencyMaintenanceDetails.reorder.alert', null), 'Alert', Alert.YES | Alert.NO, null, this.ordinalAlertListener.bind(this));
          }
        }
      }
    } catch (error) {
      // log the error in ERROR LOG
      console.log(error, this.moduleId, "CurrencyMaintenance", "focusOutOrdinalInput");
    }
  }


  /**
   * ordinalAlertListener
   * @param event:
   *
   * Method to Alert
   */
  ordinalAlertListener(event): void {
    try {
      if (event.detail === Alert.YES) {
        this.reOrder = true;

      } else {
        this.reOrder = false;
        if(this.screenName=="add") {
          this.ordinalNumInput.text=this.maxOrder;
        } else {
          let order= String(this.jsonReader.getSingletons().ordinal);
          if(order == "") {
            this.ordinalNumInput.text = this.maxOrder;
          } else {
            this.ordinalNumInput.text=this.jsonReader.getSingletons().ordinal;
          }
        }

      }
    } catch (error) {
      // log the error in ERROR LOG
      console.log(error, this.moduleId, "CurrencyMaintenance", "ordinalAlertListener");
    }
  }


  validateAmount(): any {
    if (!(validateCurrencyPlaces(this.largeAmtTextInput, this.currencyPattern, this.currencyComboBox.selectedLabel)) ) {
      this.swtAlert.warning(SwtUtil.getPredictMessage('alert.validAmount', null));
      return false;
    } else {
      return true;
    }
  }


  /**
   * doHelp
   * Function is called when "Help" button is click. Displays help window
   */
  doHelp(): void {
    try {
      // SwtHelpWindow.open(this.baseURL + "help.do?method=print&screenName=Currency+Maintenance+Details");
      ExternalInterface.call("help");
    } catch (e) {
      // log the error in ERROR LOG
      SwtUtil.logError(e,   this.moduleId, 'CurrencyMaintenance', 'doHelp',   this.errorLocation);
    }
  }


  /**
   * keyDownEventHandler
   * param event
   * This is a key event listener, used to perform the operation
   * when hit the enter key based on the currently focused property(button)
   */
  keyDownEventHandler(event): void {
    try {
      // Currently focussed property name
      let eventString: string=Object(focusManager.getFocus()).name;
      if ((event.keyCode == Keyboard.ENTER)) {
        if (eventString == "saveButton") {
          this.save();
        } else if (eventString == "cancelButton") {
          this.popupClosed();
        } else if (eventString === 'helpIcon') {
          this.doHelp();
        }
      }
    } catch (error) {
      // log the error in ERROR LOG
      console.log(error, this.moduleId, "CurrencyMaintenance", "keyDownEventHandler");
    }
  }


  /**
   * popupClosed
   * Method to close child windows when this screen is closed
   */
  popupClosed(): void {
    this.dispose();
  }
  /**
   * dispose
   * This is a event handler, used to close the current tab/window
   */
  dispose(): void {
    try {
      this.requestParams=null;
      this.baseURL=null;
      this.actionMethod=null;
      this.actionPath=null;
      this.parentDocument.refreshScreen();
      if(this.titleWindow) {
        this.close();
      } else {
        window.close();
      }
    } catch (error) {
      console.log(error, this.moduleId, "CurrencyMaintenance", "dispose");
    }
  }
}

// Define lazy loading routes
const routes: Routes = [
  { path: '', component: CurrencyMaintenanceAdd }
];
export const routing: ModuleWithProviders = RouterModule.forChild(routes);
// Define an NgModule that will be loaded by app module
@NgModule({
  imports: [routing, SwtToolBoxModule],
  declarations: [CurrencyMaintenanceAdd],
  entryComponents: [CurrencyMaintenanceAdd],
  providers: [{provide: 'currencyMaintenanceAdd', useValue: CurrencyMaintenanceAdd}]
})
export class CurrencyMaintenanceAddModule { }
