<SwtModule (close)='popupClosed()' (creationComplete)='onLoad()' height='100%' width='610'>
  <VBox  width='90%' height='90%'>
      <SwtCanvas width="100%" height="60%" paddingTop="5" paddingRight="5" paddingLeft="5" paddingBottom="5">
        <HBox width="100%">
          <SwtLabel  text="Payment Category" width="150">
          </SwtLabel>
          <SwtComboBox id="categoryCombo"
                        #categoryCombo
                       dataLabel="categoryList"
                       width="200"
                       (change) = 'changeCombo()'
                        toolTip="Select payment category">
          </SwtComboBox>
          <SwtLabel #selectedCategory  width="200">
          </SwtLabel>
        </HBox>
      </SwtCanvas>
    <SwtCanvas id="canvasContainer"
               width="100%">
      <HBox>
        <SwtButton #okButton
                   (click)="save()"
                   id="okButton"
                   label="Save"
                   width="70" [buttonMode]="false" enabled="true"></SwtButton>
        <SwtButton buttonMode="true"
                   id="cancelButton"
                   width="70"
                   label="Cancel"
                   #cancelButton
                   (click)="popupClosed();"></SwtButton>
      </HBox>
    </SwtCanvas>
  </VBox>
</SwtModule>
