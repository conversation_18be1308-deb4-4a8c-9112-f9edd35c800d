import {Component, ElementRef, OnInit, ViewChild} from '@angular/core';
import {
  Alert,
  CommonService, focusManager, HTTPComms, JSONReader,
  SwtAlert,
  SwtButton,
  SwtCanvas,
  SwtComboBox,
  SwtCommonGrid,
  SwtLabel,
  SwtModule, SwtPopUpManager, SwtUtil
} from "swt-tool-box";

@Component({
  selector: 'app-errors-update-payments',
  templateUrl: './ErrorsUpdatePayments.html',
  styleUrls: ['./ErrorsUpdatePayments.css']
})
export class ErrorsUpdatePayments extends SwtModule implements OnInit {
  @ViewChild('gridCanvas') gridCanvas: SwtCanvas;

          /***Buttons********/

  @ViewChild('releaseAllButton') releaseAllButton: SwtButton;
  @ViewChild('releasePayButton') releasePayButton: SwtButton;
  @ViewChild('UnStopButton') UnStopButton: SwtButton;
  @ViewChild('cancelButton') cancelButton: SwtButton;
            /***Variable*****/
  public errorsGrid : SwtCommonGrid;
  public rowsData: any;
  public columnsData: any;
  public isReleaseAction: boolean = false;
  private logicUpdate = new HTTPComms(this.commonService);
  private actionPath;
  private actionMethod;
  private baseURL = SwtUtil.getBaseURL();
  private requestParams;
  private swtAlert: SwtAlert;
  constructor( private commonService: CommonService, private element: ElementRef) {
    super(element,commonService);
    this.swtAlert = new SwtAlert(commonService);
  }

  ngOnInit() {
    this.errorsGrid = <SwtCommonGrid>this.gridCanvas.addChild(SwtCommonGrid);
    this.releaseAllButton.label = SwtUtil.getPredictMessage('errorsUpdatePayments.releaseAllButton.label', null);
    this.releasePayButton.label = SwtUtil.getPredictMessage('errorsUpdatePayments.releaseButton.label', null);
    this.UnStopButton.label = SwtUtil.getPredictMessage('errorsUpdatePayments.unStopButton.label', null);
    this.cancelButton.label = SwtUtil.getPredictMessage('button.cancel', null);

  }
  onLoad() {
    const obj = {columns: this.columnsData};
    this.errorsGrid.CustomGrid(obj);
    this.errorsGrid.gridData = this.rowsData;
    this.errorsGrid.allowMultipleSelection = true;
    if(!this.isReleaseAction) {
      this.releaseAllButton.visible= this.releaseAllButton.includeInLayout=false;
      this.releasePayButton.visible =this.releasePayButton.includeInLayout = false;
      this.UnStopButton.visible = true;
      this.UnStopButton.enabled = true;
      for (let i= 0; i < this.errorsGrid.dataProvider.length; i++) {
        if(this.errorsGrid.dataProvider[i].status == "Waiting") {
          this.UnStopButton.enabled = false;
          break;
        }
      }
    }
    for (let i= 0; i < this.errorsGrid.dataProvider.length; i++) {
      if(this.errorsGrid.dataProvider[i].status == "Released") {
        this.releaseAllButton.enabled = false;
        break;
      }
    }
    this.errorsGrid.selectedIndex = -1;
    this.errorsGrid.onRowClick = (event) => {
      this.rowClickEventHandler(event);
    };
  }
  rowClickEventHandler(event): void {
    if(this.errorsGrid.selectedIndex >= 0) {
      this.UnStopButton.enabled = true;
      if(this.releasePayButton.visible)
        this.releasePayButton.enabled = true;
      if(this.errorsGrid.selectedItem.status.content == "Released") {
        this.releasePayButton.enabled = false;
      } else
        this.releasePayButton.enabled = true;
      if(this.errorsGrid.selectedItem.status.content == "Waiting") {
        this.UnStopButton.enabled = false;
      } else
        this.UnStopButton.enabled = true;
    } else {
      this.UnStopButton.enabled = false;
      this.releasePayButton.enabled = false;
    }



  }
  /*
  Release one or more Payments
   */
  release() {
    let arrayofPaymentId = [];
    let arrayofStatus = [];
    this.requestParams = [];
    for (let i =0; i <this.errorsGrid.selectedItems.length; i++) {
      arrayofPaymentId.push(this.errorsGrid.selectedItems[i].paymentId.content);
      arrayofStatus.push(this.decodeStatus(this.errorsGrid.selectedItems[i].status.content));
    }
    this.actionMethod = 'method=unStopReleasePayment';
    this.actionPath = 'dashboardPCM.do?';
    this.logicUpdate.cbResult = (event) => {
      this.logicUpdateResult(event);
    };
    this.requestParams['paymentId'] = arrayofPaymentId.toString();
    this.requestParams['previousStatus'] =  arrayofStatus.toString();
    this.requestParams['paymentAction'] =  "R";
    this.requestParams['method'] = 'unStopReleasePayment';
    this.logicUpdate.encodeURL = false;
    this.logicUpdate.url = this.baseURL + this.actionPath + this.actionMethod;
    this.logicUpdate.send(this.requestParams);

  }
  checkBeforeReleaseUnstop(event) {
    this.requestParams=[];
    this.actionMethod = 'method=checkStopProcess';
    this.actionPath = 'dashboardPCM.do?';
    this.logicUpdate.cbResult = (event) => {
      this.isStopProcessRunning(event);
    };
    this.requestParams['method'] = 'checkStopProcess';
    this.logicUpdate.encodeURL = false;
    this.logicUpdate.url = this.baseURL + this.actionPath + this.actionMethod;
    this.logicUpdate.send(this.requestParams);
  }
  isStopProcessRunning(resultEvent): void {
    let result: string = null;
    let message =  SwtUtil.getPredictMessage("dashboardDetails.stopProcessRun", null);
    Alert.okLabel = SwtUtil.getPredictMessage("button.retry", null);
    Alert.cancelLabel =  SwtUtil.getPredictMessage("button.cancel", null);
    const eventstring = Object(focusManager.getFocus()).name;
    try {
      if (this.logicUpdate.isBusy()) {
        this.logicUpdate.cbStop();
      } else {
        const JsonResponse = resultEvent;
        const JsonResult: JSONReader = new JSONReader();
        JsonResult.setInputJSON(JsonResponse);
        result = JsonResult.getSingletons().isStopProcRun;
        if(result == "N") {

          if(eventstring == "releaseAllButton") {
            this.releaseAll()
          } else if(eventstring == "releasePayButton") {
            this.release()
          }else if(eventstring == "UnStopButton") {
            this.unStopPay()
          }

        } else {
          this.swtAlert.confirm(message, "", Alert.OK |  Alert.CANCEL, null, this.reCheckBeforeRelease.bind(this));
        }

      }
    }
    catch(e) {
    }
  }
  reCheckBeforeRelease(event) {
    if(event.detail == Alert.OK ) {
      this.checkBeforeReleaseUnstop(event)
    } else {

    }
  }

  /*
  Release All Payments
   */
  releaseAll() {
    let arrayofPaymentId = [];
    let arrayofStatus = [];
    this.requestParams = [];
    for (let i =0; i <this.errorsGrid.dataProvider.length; i++) {
      arrayofPaymentId.push(this.errorsGrid.dataProvider[i].paymentId.content);
      arrayofStatus.push(this.decodeStatus(this.errorsGrid.dataProvider[i].status.content));
    }
    this.actionMethod = 'method=unStopReleasePayment';
    this.actionPath = 'dashboardPCM.do?';
    this.logicUpdate.cbResult = (event) => {
      this.logicUpdateResult(event);
    };
    this.requestParams['paymentId'] = arrayofPaymentId.toString();
    this.requestParams['previousStatus'] =  arrayofStatus.toString();
    this.requestParams['paymentAction'] =  "R";
    this.requestParams['method'] = 'unStopReleasePayment';
    this.logicUpdate.encodeURL = false;
    this.logicUpdate.url = this.baseURL + this.actionPath + this.actionMethod;
    this.logicUpdate.send(this.requestParams);
  }
  /*
  unstop one payment
   */
  unStopPay(){
    let arrayOfStatus = [];
    let arrayofPaymentId = [];
    this.requestParams = [];
    try {
      for (let i =0; i <this.errorsGrid.selectedItems.length; i++) {
        arrayOfStatus.push(this.decodeStatus(this.errorsGrid.selectedItems[i].status.content));
        arrayofPaymentId.push(this.errorsGrid.selectedItems[i].paymentId.content);
      }
      this.actionMethod = 'method=unStopReleasePayment';
      this.actionPath = 'dashboardPCM.do?';
      this.logicUpdate.cbResult = (event) => {
        this.logicUpdateResult(event);
      };
      this.requestParams['paymentId'] =  arrayofPaymentId.toString();
      this.requestParams['previousStatus'] =  arrayOfStatus.toString();
      this.requestParams['paymentAction'] =  "U";
      this.requestParams['method'] = 'unStopReleasePayment';
      this.logicUpdate.encodeURL = false;
      this.logicUpdate.url = this.baseURL + this.actionPath + this.actionMethod;
      this.logicUpdate.send(this.requestParams);
    } catch(e) {
      console.log('errror in unStopPay ', e)
    }

  }

  logicUpdateResult(event): void {
    try {
      if (this.logicUpdate.isBusy()) {
        this.logicUpdate.cbStop();
      } else {
        const JsonResponse = event;
        const JsonResult: JSONReader = new JSONReader();
        JsonResult.setInputJSON(JsonResponse);

        if (JsonResult.getRequestReplyMessage() == "ERROR_PAYMENT_ENGINE") {
          this.swtAlert.error("Fail when sending response to the payment engine");
        }else if (JsonResult.getRequestReplyMessage() == "GENERIC_ERROR") {
          this.swtAlert.error("An exception is raised");
        } else {
          if(event.ErrorsOfUpdate.grid && JsonResult.getGridData().size > 0 ) {
            const obj = {columns: JsonResult.getColumnData()};
            this.errorsGrid.CustomGrid(obj);
            this.errorsGrid.gridData = JsonResult.getGridData();
            this.errorsGrid.allowMultipleSelection = true;
          }
          else {
            this.close();
            this.parentDocument.updateData(null);
          }

        }
      }
    } catch (e) {
      // log the error in ERROR LOG
    }
  }
  popupClosed(): void {
    this.close();
  }
  decodeStatus(status): string {
   var statusCode: string;
  switch (status) {
  case "Stopped":
    statusCode = "S";
    break;
  case "Waiting":
    statusCode = "W";
    break;
  case "Blocked":
    statusCode = "B";
    break;
  case "Released":
    statusCode = "R";
    break;
  case "Cancelled":
    statusCode = "C";
    break;

  default:
    statusCode = "All";
    break;
  }

  return statusCode;

}

}
