import {Component, ElementRef, OnInit, ViewChild} from '@angular/core';
import {
  Alert,
  CommonService,
  HBox,
  HTTPComms,
  JSONReader,
  SwtAlert, SwtButton,
  SwtCheckBox,
  SwtComboBox,
  SwtLabel, SwtLoadingImage,
  SwtModule, SwtUtil
} from "swt-tool-box";

@Component({
  selector: 'app-confirm-release',
  templateUrl: './ConfirmRelease.html',
  styleUrls: ['./ConfirmRelease.css']
})
export class ConfirmRelease extends SwtModule implements OnInit {
  public totalPay: number = 0;
  public totalWaiting: number = 0;
  public totalStopped: number = 0;
  public totalBlocked: number = 0;
  public totalBackValued: number = 0;
  public totalInputCutOff: number = 0;
  public totalStoppedCutOff: number = 0;
  public totalCutOffPassed: number = 0;
  public totalBackValuedS: number = 0;
  public totalInputCutOffS: number = 0;
  public totalStoppedCutOffS: number = 0;
  public totalCutOffPassedS: number = 0;
  public totalCancel: number = 0;
  public totalReleased: number = 0;
  public totalalreadyStoppedBlocked: number = 0;

  public totalBlockedPrevS: number = 0;
  public totalBackValuedPrevS: number = 0;
  public totalInputCutOffPrevS: number = 0;
  public totalStoppedCutOffPrevS: number = 0;
  public totalCutOffPassedPrevS: number = 0;

  public statusPayArray: any;
  private logicUpdate = new HTTPComms(this.commonService);
  private actionPath;
  private actionMethod;
  private baseURL = SwtUtil.getBaseURL();
  private requestParams;
  private swtAlert: SwtAlert;
  /***HBOX*********/
  @ViewChild('waitingPay') waitingPay: HBox;
  @ViewChild('stoppedPay') stoppedPay: HBox;
  @ViewChild('blockedPay') blockedPay: HBox;
  @ViewChild('backValuedPay') backValuedPay: HBox;
  @ViewChild('inputCutOffPay') inputCutOffPay: HBox;
  @ViewChild('stoppedCutOffPay') stoppedCutOffPay: HBox;
  @ViewChild('cutOffPassedPay') cutOffPassedPay: HBox;
  @ViewChild('backValuedPayS') backValuedPayS: HBox;
  @ViewChild('inputCutOffPayS') inputCutOffPayS: HBox;
  @ViewChild('stoppedCutOffPayS') stoppedCutOffPayS: HBox;
  @ViewChild('cutOffPassedPayS') cutOffPassedPayS: HBox;
  @ViewChild('cancelledPay') cancelledPay: HBox;
  @ViewChild('releasedPay') releasedPay: HBox;
  @ViewChild('blockedStoppedPay') blockedStoppedPay: HBox;

  @ViewChild('blockedPrevStoppedPay') blockedPrevStoppedPay: HBox;
  @ViewChild('backValuedPayPrevS') backValuedPayPrevS: HBox;
  @ViewChild('inputCutOffPayPrevS') inputCutOffPayPrevS: HBox;
  @ViewChild('stoppedCutOffPayPrevS') stoppedCutOffPayPrevS: HBox;
  @ViewChild('cutOffPassedPayPrevS') cutOffPassedPayPrevS: HBox;

  /*****CheckBox**********/
  @ViewChild('waitingCheck') waitingCheck: SwtCheckBox;
  @ViewChild('stoppedCheck') stoppedCheck: SwtCheckBox;
  @ViewChild('blockedCheck') blockedCheck: SwtCheckBox;
  @ViewChild('backValuedCheck') backValuedCheck: SwtCheckBox;
  @ViewChild('inputCutOffCheck') inputCutOffCheck: SwtCheckBox;
  @ViewChild('cutOffPassedCheck') cutOffPassedCheck: SwtCheckBox;
  @ViewChild('stoppedCutOffCheck') stoppedCutOffCheck: SwtCheckBox;

  @ViewChild('blockedStoppedCheck') blockedStoppedCheck: SwtCheckBox;
  @ViewChild('backValuedCheckS') backValuedCheckS: SwtCheckBox;
  @ViewChild('inputCutOffCheckS') inputCutOffCheckS: SwtCheckBox;
  @ViewChild('cutOffPassedCheckS') cutOffPassedCheckS: SwtCheckBox;
  @ViewChild('stoppedCutOffCheckS') stoppedCutOffCheckS: SwtCheckBox;

  @ViewChild('blockedPrevStoppedCheck') blockedPrevStoppedCheck: SwtCheckBox;
  @ViewChild('backValuedCheckPrevS') backValuedCheckPrevS: SwtCheckBox;
  @ViewChild('inputCutOffCheckPrevS') inputCutOffCheckPrevS: SwtCheckBox;
  @ViewChild('stoppedCutOffCheckPrevS') stoppedCutOffCheckPrevS: SwtCheckBox;
  @ViewChild('cutOffPassedCheckPrevS') cutOffPassedCheckPrevS: SwtCheckBox;

  @ViewChild('loadingImage') loadingImage: SwtLoadingImage;
  @ViewChild('releaseButton') releaseButton: SwtButton;
  /**********Labels**************/
  @ViewChild('totalPayLabel') totalPayLabel: SwtLabel;
  @ViewChild('paymentsToRelease') paymentsToRelease: SwtLabel;
  @ViewChild('selectedWaiting') selectedWaiting: SwtLabel;
  @ViewChild('selectedStopped') selectedStopped: SwtLabel;
  @ViewChild('selectedBlocked') selectedBlocked: SwtLabel;
  @ViewChild('selectedBValued') selectedBValued: SwtLabel;
  @ViewChild('selectedI') selectedI: SwtLabel;
  @ViewChild('selectedS') selectedS: SwtLabel;
  @ViewChild('selectedC') selectedC: SwtLabel;
  @ViewChild('selectedBS') selectedBS: SwtLabel;

  @ViewChild('selectedBStopped') selectedBStopped: SwtLabel;
  @ViewChild('selectedIStopped') selectedIStopped: SwtLabel;
  @ViewChild('selectedSStopped') selectedSStopped: SwtLabel;
  @ViewChild('selectedCStopped') selectedCStopped: SwtLabel;

  @ViewChild('selectedBlockedPrevS') selectedBlockedPrevS: SwtLabel;
  @ViewChild('selectedBPrevStopped') selectedBPrevStopped: SwtLabel;
  @ViewChild('selectedIPrevStopped') selectedIPrevStopped: SwtLabel;
  @ViewChild('selectedPrevStopped') selectedPrevStopped: SwtLabel;
  @ViewChild('selectedCPrevStopped') selectedCPrevStopped: SwtLabel;

  @ViewChild('selectedCancelled') selectedCancelled: SwtLabel;
  @ViewChild('selectedReleased') selectedReleased: SwtLabel;

  constructor( private commonService: CommonService, private element: ElementRef) {
    super(element, commonService);
    this.swtAlert = new SwtAlert(commonService);

  }

  ngOnInit() {
    try {
      this.loadingImage.setVisible(false);
      this.totalPay = this.statusPayArray.length;
      for(var i=0;i<this.statusPayArray.length; i++){
        if(this.statusPayArray[i].status == "R")
          this.totalReleased++;
        else if(this.statusPayArray[i].status == "C")
          this.totalCancel++;
        else if(this.statusPayArray[i].status == "W")
          this.totalWaiting ++;
        else if(this.statusPayArray[i].status == "S")
          this.totalStopped ++;
        else if(this.statusPayArray[i].status == "B" && this.statusPayArray[i].blockReason == "B" && this.statusPayArray[i].alreadyStopped == "")
          this.totalBackValued ++;
        else if(this.statusPayArray[i].status == "B" && this.statusPayArray[i].blockReason == "I" && this.statusPayArray[i].alreadyStopped == "")
          this.totalInputCutOff ++;
        else if(this.statusPayArray[i].status == "B" && this.statusPayArray[i].blockReason == "S" && this.statusPayArray[i].alreadyStopped== "")
          this.totalStoppedCutOff ++;
        else if(this.statusPayArray[i].status == "B" && this.statusPayArray[i].blockReason == "C" && this.statusPayArray[i].alreadyStopped == "")
          this.totalCutOffPassed ++;

        else if(this.statusPayArray[i].status == "B" && this.statusPayArray[i].blockReason == "B" && this.statusPayArray[i].alreadyStopped == "Stopped" )
          this.totalBackValuedS ++;
        else if(this.statusPayArray[i].status == "B" && this.statusPayArray[i].blockReason == "I" && this.statusPayArray[i].alreadyStopped == "Stopped")
          this.totalInputCutOffS ++;
        else if(this.statusPayArray[i].status == "B" && this.statusPayArray[i].blockReason == "S" && this.statusPayArray[i].alreadyStopped == "Stopped")
          this.totalStoppedCutOffS ++;
        else if(this.statusPayArray[i].status == "B" && this.statusPayArray[i].blockReason == "C" && this.statusPayArray[i].alreadyStopped == "Stopped")
          this.totalCutOffPassedS ++;

        else if(this.statusPayArray[i].status == "B" && this.statusPayArray[i].blockReason == "B" && this.statusPayArray[i].alreadyStopped == "PrevStopped" )
          this.totalBackValuedPrevS ++;
        else if(this.statusPayArray[i].status == "B" && this.statusPayArray[i].blockReason == "I" && this.statusPayArray[i].alreadyStopped == "PrevStopped")
          this.totalInputCutOffPrevS ++;
        else if(this.statusPayArray[i].status == "B" && this.statusPayArray[i].blockReason == "S" && this.statusPayArray[i].alreadyStopped == "PrevStopped")
          this.totalStoppedCutOffPrevS ++;
        else if(this.statusPayArray[i].status == "B" && this.statusPayArray[i].blockReason == "C" && this.statusPayArray[i].alreadyStopped == "PrevStopped")
          this.totalCutOffPassedPrevS ++;
      }
      this.totalBlocked = this.totalBackValued + this.totalInputCutOff +  this.totalStoppedCutOff + this.totalCutOffPassed;
      this.totalalreadyStoppedBlocked = this.totalBackValuedS + this.totalInputCutOffS +  this.totalStoppedCutOffS + this.totalCutOffPassedS;
      this.totalBlockedPrevS = this.totalBackValuedPrevS + this.totalInputCutOffPrevS +  this.totalStoppedCutOffPrevS + this.totalCutOffPassedPrevS;
      if(this.totalWaiting == 0) {
        this.waitingPay.visible = this.waitingPay.includeInLayout = false;
      }
      if(this.totalStopped == 0) {
        this.stoppedPay.visible =this.stoppedPay.includeInLayout = false;
      }
      if(this.totalBlocked == 0) {
        this.blockedPay.visible = this.blockedPay.includeInLayout = false;
      }
      if(this.totalBackValued == 0) {
        this.backValuedPay.visible =this.backValuedPay.includeInLayout = false;
      }
      if(this.totalInputCutOff == 0) {
        this.inputCutOffPay.visible =this.inputCutOffPay.includeInLayout = false;
      }
      if(this.totalStoppedCutOff == 0) {
        this.stoppedCutOffPay.visible =this.stoppedCutOffPay.includeInLayout = false;
      }
      if(this.totalCutOffPassed == 0) {
        this.cutOffPassedPay.visible =this.cutOffPassedPay.includeInLayout = false;
      }
      if(this.totalCancel == 0) {
        this.cancelledPay.visible =this.cancelledPay.includeInLayout = false;
      }
      if(this.totalReleased == 0) {
        this.releasedPay.visible = this.releasedPay.includeInLayout = false;
      }
      if(this.totalalreadyStoppedBlocked == 0) {
        this.blockedStoppedPay.visible =this.blockedStoppedPay.includeInLayout = false;
      }
      if(this.totalBackValuedS == 0) {
        this.backValuedPayS.visible =this.backValuedPayS.includeInLayout = false;
      }
      if(this.totalInputCutOffS == 0) {
        this.inputCutOffPayS.visible =this.inputCutOffPayS.includeInLayout = false;
      }
      if(this.totalStoppedCutOffS == 0) {
        this.stoppedCutOffPayS.visible =  this.stoppedCutOffPayS.includeInLayout = false;
      }
      if(this.totalCutOffPassedS == 0) {
        this.cutOffPassedPayS.visible =this.cutOffPassedPayS.includeInLayout = false;
      }

      if(this.totalBlockedPrevS == 0) {
        this.blockedPrevStoppedPay.visible =this.blockedPrevStoppedPay.includeInLayout = false;
      }
      if(this.totalBackValuedPrevS == 0) {
        this.backValuedPayPrevS.visible = this.backValuedPayPrevS.includeInLayout = false;
      }
      if(this.totalInputCutOffPrevS == 0) {
        this.inputCutOffPayPrevS.visible =this.inputCutOffPayPrevS.includeInLayout = false;
      }
      if(this.totalStoppedCutOffPrevS == 0) {
        this.stoppedCutOffPayPrevS.visible = this.stoppedCutOffPayPrevS.includeInLayout = false;
      }
      if(this.totalCutOffPassedPrevS == 0) {
        this.cutOffPassedPayPrevS.visible = this.cutOffPassedPayPrevS.includeInLayout = false;
      }
      if(this.totalCancel == 0) {
        this.cancelledPay.visible =  this.cancelledPay.includeInLayout = false;
      }
      if(this.totalReleased == 0) {
        this.releasedPay.visible =  this.releasedPay.includeInLayout = false;
      }
      this.paymentsToRelease.text = SwtUtil.getPredictMessage('confirmRelease.label.paymentSelectedForRelease');
      this.selectedWaiting.text = SwtUtil.getPredictMessage('confirmRelease.label.waiting');
      this.selectedStopped.text = SwtUtil.getPredictMessage('confirmRelease.label.stopped');
      this.selectedBlocked.text = SwtUtil.getPredictMessage('confirmRelease.label.blocked');

      this.selectedBValued.text = this.selectedBStopped.text = this.selectedBPrevStopped.text = SwtUtil.getPredictMessage('confirmRelease.label.backValued');
      this.selectedI.text = this.selectedIStopped.text = this.selectedIPrevStopped.text = SwtUtil.getPredictMessage('confirmRelease.label.inputCutOff');
      this.selectedS.text = this.selectedSStopped.text = this.selectedPrevStopped.text = SwtUtil.getPredictMessage('confirmRelease.label.stoppedCutOff');
      this.selectedC.text = this.selectedCStopped.text = this.selectedCPrevStopped.text = SwtUtil.getPredictMessage('confirmRelease.label.cutOffPassed');

      this.selectedBS.text = SwtUtil.getPredictMessage('confirmRelease.label.blockedStopped');
      this.selectedBlockedPrevS.text = SwtUtil.getPredictMessage('confirmRelease.label.blockedPreviosulyStopped');
      this.selectedCancelled.text = SwtUtil.getPredictMessage('confirmRelease.label.cancelled');
      this.selectedReleased.text = SwtUtil.getPredictMessage('confirmRelease.label.releasedAlready');

    } catch(e) {
      console.log('erggrga', e)
    }
  }
  onLoad(): void {
  }
  popupClosed(): void {
    this.close();
  }
  checkBeforeRelease(event) {
    this.requestParams=[];
    this.actionMethod = 'method=checkStopProcess';
    this.actionPath = 'dashboardPCM.do?';
    this.logicUpdate.cbResult = (event) => {
      this.isStopProcessRunning(event);
    };
    this.requestParams['method'] = 'checkStopProcess';
    this.logicUpdate.cbStart = this.startOfComms.bind(this);
    this.logicUpdate.cbStop = this.endOfComms.bind(this);
    this.logicUpdate.encodeURL = false;
    this.logicUpdate.url = this.baseURL + this.actionPath + this.actionMethod;
    this.logicUpdate.send(this.requestParams);
  }
  isStopProcessRunning(resultEvent): void {
    let result: string = null;
    let message =  SwtUtil.getPredictMessage("dashboardDetails.stopProcessRun", null);
    Alert.okLabel = SwtUtil.getPredictMessage("button.retry", null);
    Alert.cancelLabel =  SwtUtil.getPredictMessage("button.cancel", null);
    try {
      if (this.logicUpdate.isBusy()) {
        this.logicUpdate.cbStop();
      } else {
        const JsonResponse = resultEvent;
        const JsonResult: JSONReader = new JSONReader();
        JsonResult.setInputJSON(JsonResponse);
        result = JsonResult.getSingletons().isStopProcRun;
        if(result == "N") {
          this.release()
        } else {
          this.swtAlert.confirm(message, "", Alert.OK |  Alert.CANCEL, null, this.reCheckBeforeRelease.bind(this));
        }

      }
    }
    catch(e) {
    }
  }
  reCheckBeforeRelease(event) {
    if(event.detail == Alert.OK ) {
      this.checkBeforeRelease(event)
    } else {

    }
  }
  release() : void {
    var result = this.statusPayArray;
    var arrayofPaymentId = [];
    var arrayOfStatus= [];
    var srt:string = '';
    this.requestParams = [];
    if (this.waitingCheck.selected) {
      if(srt.length >0) {
        srt+= "|| @.status =="+"'W'";
      }else {
        srt+= "@.status =="+"'W'";
      }
    }
    if (this.stoppedCheck.selected) {
      if(srt.length >0) {
        srt+= "|| @.status =="+"'S'";
      }else {
        srt+= "@.status =="+"'S'";
      }
    }
    /* if (this.blockedCheck.selected) {
       if(srt.length >0) {
         srt+= "|| @.status =="+"'B'";
       }else {
         srt+= "@.status =="+"'B'";
       }
     }*/
    if (this.backValuedCheck.selected) {
      if(srt.length >0) {
        srt+= "|| @.blockReason =="+"'B' && @.alreadyStopped ==''";
      }else {
        srt+= "@.blockReason =="+"'B' && @.alreadyStopped ==''";
      }
    }
    if (this.inputCutOffCheck.selected) {
      if(srt.length >0) {
        srt+= "|| @.blockReason =="+"'I' && @.alreadyStopped ==''";
      }else {
        srt+= "@.blockReason =="+"'I' && @.alreadyStopped ==''";
      }
    }
    if (this.stoppedCutOffCheck.selected) {
      if(srt.length >0) {
        srt+= "|| @.blockReason =="+"'S' && @.alreadyStopped ==''";
      }else {
        srt+= "@.blockReason =="+"'S' && @.alreadyStopped ==''";
      }
    }
    if (this.cutOffPassedCheck.selected) {
      if(srt.length >0) {
        srt+= "|| @.blockReason =="+"'C' && @.alreadyStopped ==''";
      }else {
        srt+= "@.blockReason =="+"'C' && @.alreadyStopped ==''";
      }
    }

    if (this.backValuedCheckS.selected) {
      if(srt.length >0) {
        srt+= "|| @.blockReason =="+"'B' && @.alreadyStopped =="+"'Stopped'";
      }else {
        srt+= "@.blockReason =="+"'B' && @.alreadyStopped =="+"'Stopped'";
      }
    }
    if (this.inputCutOffCheckS.selected) {
      if(srt.length >0) {
        srt+= "|| @.blockReason =="+"'I' && @.alreadyStopped =="+"'Stopped'";
      }else {
        srt+= "@.blockReason =="+"'I' && @.alreadyStopped =="+"'Stopped'";
      }
    }
    if (this.stoppedCutOffCheckS.selected) {
      if(srt.length >0) {
        srt+= "|| @.blockReason =="+"'S' && @.alreadyStopped =="+"'Stopped'";
      }else {
        srt+= "@.blockReason =="+"'St' && @.alreadyStopped =="+"'Stopped'";
      }
    }
    if (this.cutOffPassedCheckS.selected) {
      if(srt.length >0) {
        srt+= "|| @.blockReason =="+"'C' && @.alreadyStopped =="+"'Stopped'";
      }else {
        srt+= "@.blockReason =="+"'C' && @.alreadyStopped =="+"'Stopped'";
      }
    }
    if (this.backValuedCheckPrevS.selected) {
      if(srt.length >0) {
        srt+= "|| @.blockReason =="+"'B' && @.alreadyStopped =="+"'PrevStopped'";
      }else {
        srt+= "@.blockReason =="+"'B' && @.alreadyStopped =="+"'PrevStopped'";
      }
    }
    if (this.inputCutOffCheckPrevS.selected) {
      if(srt.length >0) {
        srt+= "|| @.blockReason =="+"'I' && @.alreadyStopped =="+"'PrevStopped'";
      }else {
        srt+= "@.blockReason =="+"'I' && @.alreadyStopped =="+"'PrevStopped'";
      }
    }
    if (this.stoppedCutOffCheckPrevS.selected) {
      if(srt.length >0) {
        srt+= "|| @.blockReason =="+"'S' && @.alreadyStopped =="+"'PrevStopped'";
      }else {
        srt+= "@.blockReason =="+"'St' && @.alreadyStopped =="+"'PrevStopped'";
      }
    }
    if (this.cutOffPassedCheckPrevS.selected) {
      if(srt.length >0) {
        srt+= "|| @.blockReason =="+"'C' && @.alreadyStopped =="+"'Stopped'";
      }else {
        srt+= "@.blockReason =="+"'C' && @.alreadyStopped =="+"'Stopped'";
      }
    }

    result = JSONReader.jsonpath(this.statusPayArray, '$..[?('+srt+')]');
    for (var i= 0; i< result.length; i ++) {
      arrayofPaymentId.push(result[i].id);
      arrayOfStatus.push(result[i].status)
    }
    this.actionMethod = 'method=unStopReleasePayment';
    this.actionPath = 'dashboardPCM.do?';
    this.logicUpdate.cbResult = (event) => {
      this.parentDocument.commonGrid.selectedIndex = -1 ;
      this.close();
      this.parentDocument.logicUpdateResult(event);
    };
    this.requestParams['paymentId'] = arrayofPaymentId.toString();
    this.requestParams['previousStatus'] =  arrayOfStatus.toString();
    this.requestParams['paymentAction'] =  "R";
    this.requestParams['method'] = 'unStopReleasePayment';
    this.logicUpdate.cbStart = this.startOfComms.bind(this);
    this.logicUpdate.cbStop = this.endOfComms.bind(this);
    this.logicUpdate.encodeURL = false;
    this.logicUpdate.url = this.baseURL + this.actionPath + this.actionMethod;
    this.logicUpdate.send(this.requestParams);

  }
  startOfComms(): void {
    this.loadingImage.setVisible(true);
  }

  endOfComms(): void {
    this.loadingImage.setVisible(false);
  }

  changeCheckBoxWaiting() : void {
    if (!this.waitingCheck.selected ) {
      this.totalPay -= this.totalWaiting;
    } else if (this.waitingCheck.selected) {
      this.totalPay = this.statusPayArray.length - ((!this.stoppedCheck.selected && this.stoppedPay.visible) ? this.totalStopped : 0) - ((!this.blockedCheck.selected && this.blockedPay.visible) ? this.totalBlocked : 0) - ((!this.blockedStoppedCheck.selected && this.blockedStoppedPay.visible) ? this.totalalreadyStoppedBlocked : 0) - ((!this.blockedPrevStoppedCheck.selected && this.blockedPrevStoppedPay.visible) ? this.totalBlockedPrevS : 0);
    }
    this.disableEnableReleaseButton();

  }
  changeCheckBoxStopped() : void {
    if(!this.stoppedCheck.selected) {
      this.totalPay -= this.totalStopped;
    }
    else if(this.stoppedCheck.selected) {
      this.totalPay = this.statusPayArray.length - ((!this.waitingCheck.selected && this.waitingPay.visible) ? this.totalWaiting : 0 ) - ((!this.blockedCheck.selected && this.blockedPay.visible) ? this.totalBlocked : 0) - ((!this.blockedStoppedCheck.selected && this.blockedStoppedPay.visible) ? this.totalalreadyStoppedBlocked : 0) - ((!this.blockedPrevStoppedCheck.selected && this.blockedPrevStoppedPay.visible) ? this.totalBlockedPrevS : 0);
    }
    this.disableEnableReleaseButton();
  }
  changeCheckBoxBlocked() : void {
    if(!this.blockedCheck.selected) {
      this.totalPay -= this.totalBlocked;
      this.backValuedCheck.selected = false;
      this.stoppedCutOffCheck.selected = false;
      this.cutOffPassedCheck.selected = false;
      this.inputCutOffCheck.selected = false;
    }
    else if(this.blockedCheck.selected) {
      this.backValuedCheck.selected = this.backValuedPay.visible;
      this.stoppedCutOffCheck.selected = this.stoppedCutOffPay.visible;
      this.inputCutOffCheck.selected = this.inputCutOffPay.visible;
      this.cutOffPassedCheck.selected = this.cutOffPassedPay.visible;
      this.totalBlocked = ((this.inputCutOffCheck.selected && this.inputCutOffPay.visible) ? this.totalInputCutOff : 0 ) + ((this.backValuedCheck.selected && this.backValuedPay.visible) ? this.totalBackValued : 0 ) + ((this.stoppedCutOffCheck.selected) ? this.totalStoppedCutOff : 0 )+ ((this.cutOffPassedCheck.selected) ?  this.totalCutOffPassed : 0);
      this.totalPay = this.statusPayArray.length - ((!this.waitingCheck.selected && this.waitingPay.visible) ? this.totalWaiting : 0 ) - ((!this.stoppedCheck.selected && this.stoppedPay.visible) ? this.totalStopped : 0)  - ((!this.blockedStoppedCheck.selected && this.blockedStoppedPay.visible) ? this.totalalreadyStoppedBlocked : 0) - ((!this.blockedPrevStoppedCheck.selected && this.blockedPrevStoppedPay.visible) ? this.totalBlockedPrevS : 0) ;
    }
    this.disableEnableReleaseButton();
  }
  changeCheckBoxBackValued() : void {
    if(!this.backValuedCheck.selected) {
      this.totalBlocked -= this.totalBackValued;
      this.totalPay -= this.totalBackValued;
    }
    else {
      this.totalBlocked = ((this.inputCutOffCheck.selected && this.inputCutOffPay.visible) ? this.totalInputCutOff : 0 ) + ((this.backValuedCheck.selected && this.backValuedPay.visible) ? this.totalBackValued : 0 ) + ((this.stoppedCutOffCheck.selected) ? this.totalStoppedCutOff : 0 )+ ((this.cutOffPassedCheck.selected) ?  this.totalCutOffPassed : 0);
      this.totalPay = this.statusPayArray.length - ((!this.waitingCheck.selected && this.waitingPay.visible) ? this.totalWaiting : 0 ) - ((!this.stoppedCheck.selected && this.stoppedPay.visible) ? this.totalStopped : 0)  - ((!this.blockedStoppedCheck.selected && this.blockedStoppedPay.visible) ? this.totalalreadyStoppedBlocked : 0) - ((!this.blockedPrevStoppedCheck.selected && this.blockedPrevStoppedPay.visible) ? this.totalBlockedPrevS : 0) ;
    }
    this.disableEnableReleaseButton();
  }
  changeCheckBoxInputCutOff() : void {
    if(!this.inputCutOffCheck.selected) {
      this.totalBlocked -= this.totalInputCutOff;
      this.totalPay -= this.totalInputCutOff;

    }
    else  {
      this.totalBlocked = ((this.inputCutOffCheck.selected && this.inputCutOffPay.visible) ? this.totalInputCutOff : 0 ) + ((this.backValuedCheck.selected && this.backValuedPay.visible) ? this.totalBackValued : 0 ) + ((this.stoppedCutOffCheck.selected) ? this.totalStoppedCutOff : 0 )+ ((this.cutOffPassedCheck.selected) ?  this.totalCutOffPassed : 0);
      this.totalPay = this.statusPayArray.length - ((!this.waitingCheck.selected && this.waitingPay.visible) ? this.totalWaiting : 0 ) - ((!this.stoppedCheck.selected && this.stoppedPay.visible) ? this.totalStopped : 0)  - ((!this.blockedStoppedCheck.selected && this.blockedStoppedPay.visible) ? this.totalalreadyStoppedBlocked : 0) - ((!this.blockedPrevStoppedCheck.selected && this.blockedPrevStoppedPay.visible) ? this.totalBlockedPrevS : 0) ;

    }
    this.disableEnableReleaseButton();
  }
  changeCheckBoxStoppedCutOff() : void {
    if(!this.stoppedCutOffCheck.selected) {
      this.totalBlocked -= this.totalStoppedCutOff;
      this.totalPay -= this.totalStoppedCutOff;

    }
    else {
      this.totalBlocked = ((this.inputCutOffCheck.selected && this.inputCutOffPay.visible) ? this.totalInputCutOff : 0 ) + ((this.backValuedCheck.selected && this.backValuedPay.visible) ? this.totalBackValued : 0 ) + ((this.stoppedCutOffCheck.selected) ? this.totalStoppedCutOff : 0 )+ ((this.cutOffPassedCheck.selected) ?  this.totalCutOffPassed : 0);
      this.totalPay = this.statusPayArray.length - ((!this.waitingCheck.selected && this.waitingPay.visible) ? this.totalWaiting : 0 ) - ((!this.stoppedCheck.selected && this.stoppedPay.visible) ? this.totalStopped : 0)  - ((!this.blockedStoppedCheck.selected && this.blockedStoppedPay.visible) ? this.totalalreadyStoppedBlocked : 0) - ((!this.blockedPrevStoppedCheck.selected && this.blockedPrevStoppedPay.visible) ? this.totalBlockedPrevS : 0);
    }
    this.disableEnableReleaseButton();
  }
  changeCheckBoxCutOffPassed() : void {
    if(!this.cutOffPassedCheck.selected) {
      this.totalBlocked -= this.totalCutOffPassed;
      this.totalPay -= this.totalCutOffPassed;
    }
    else {
      this.totalBlocked = ((this.inputCutOffCheck.selected && this.inputCutOffPay.visible) ? this.totalInputCutOff : 0 ) + ((this.backValuedCheck.selected && this.backValuedPay.visible) ? this.totalBackValued : 0 ) + ((this.stoppedCutOffCheck.selected) ? this.totalStoppedCutOff : 0 )+ ((this.cutOffPassedCheck.selected) ?  this.totalCutOffPassed : 0);
      this.totalPay = this.statusPayArray.length - ((!this.waitingCheck.selected && this.waitingPay.visible) ? this.totalWaiting : 0 ) - ((!this.stoppedCheck.selected && this.stoppedPay.visible) ? this.totalStopped : 0)  - ((!this.blockedStoppedCheck.selected && this.blockedStoppedPay.visible) ? this.totalalreadyStoppedBlocked : 0) - ((!this.blockedPrevStoppedCheck.selected && this.blockedPrevStoppedPay.visible) ? this.totalBlockedPrevS : 0) ;
    }
    this.disableEnableReleaseButton();
  }

  /****Blocked and Stopped***********/
  changeCheckBoxBlockedStopped() : void {
    if(!this.blockedStoppedCheck.selected) {
      this.totalPay -= this.totalalreadyStoppedBlocked;
      this.backValuedCheckS.selected = false;
      this.stoppedCutOffCheckS.selected = false;
      this.cutOffPassedCheckS.selected = false;
      this.inputCutOffCheckS.selected = false;
    }
    else {
      this.backValuedCheckS.selected = this.backValuedPayS.visible;
      this.stoppedCutOffCheckS.selected = this.stoppedCutOffPayS.visible;
      this.inputCutOffCheckS.selected = this.inputCutOffPayS.visible;
      this.cutOffPassedCheckS.selected = this.cutOffPassedPayS.visible;
      this.totalalreadyStoppedBlocked = ((this.inputCutOffCheckS.selected && this.inputCutOffPayS.visible) ? this.totalInputCutOffS : 0 ) + ((this.backValuedCheckS.selected && this.backValuedPayS.visible) ? this.totalBackValuedS : 0 )+ ((this.stoppedCutOffCheckS.selected && this.stoppedCutOffPayS.visible) ? this.totalStoppedCutOffS : 0 )+ ((this.cutOffPassedCheckS.selected && this.cutOffPassedPayS.visible) ?  this.totalCutOffPassedS : 0);
      this.totalPay = this.statusPayArray.length - ((!this.waitingCheck.selected && this.waitingPay.visible) ? this.totalWaiting : 0 ) - ((!this.stoppedCheck.selected && this.stoppedPay.visible) ? this.totalStopped : 0) - ((!this.blockedCheck.selected && this.blockedPay.visible) ? this.totalBlocked : 0) - ((!this.blockedPrevStoppedCheck.selected && this.blockedPrevStoppedPay.visible) ? this.totalBlockedPrevS : 0)  ;
    }
    this.disableEnableReleaseButton();
  }

  changeCheckBoxBackValuedS() : void {
    if(!this.backValuedCheckS.selected) {
      this.totalalreadyStoppedBlocked -= this.totalBackValuedS;
      this.totalPay -= this.totalBackValuedS;
    }
    else {
      this.totalalreadyStoppedBlocked = ((this.inputCutOffCheckS.selected && this.inputCutOffPayS.visible) ? this.totalInputCutOffS : 0 ) + ((this.backValuedCheckS.selected && this.backValuedPayS.visible) ? this.totalBackValuedS : 0 )+ ((this.stoppedCutOffCheckS.selected && this.stoppedCutOffPayS.visible) ? this.totalStoppedCutOffS : 0 )+ ((this.cutOffPassedCheckS.selected && this.cutOffPassedPayS.visible) ?  this.totalCutOffPassedS : 0);
      this.totalPay = this.statusPayArray.length - ((!this.waitingCheck.selected && this.waitingPay.visible) ? this.totalWaiting : 0 ) - ((!this.stoppedCheck.selected && this.stoppedPay.visible) ? this.totalStopped : 0) - ((!this.blockedCheck.selected && this.blockedPay.visible) ? this.totalBlocked : 0) - ((!this.blockedPrevStoppedCheck.selected && this.blockedPrevStoppedPay.visible) ? this.totalBlockedPrevS : 0);
    }
    this.disableEnableReleaseButton();
  }
  changeCheckBoxInputCutOffS() : void {
    if(!this.inputCutOffCheckS.selected) {
      this.totalalreadyStoppedBlocked -= this.totalInputCutOffS;
      this.totalPay -= this.totalInputCutOffS;
    }
    else {
      this.totalalreadyStoppedBlocked = ((this.inputCutOffCheckS.selected && this.inputCutOffPayS.visible) ? this.totalInputCutOffS : 0 ) + ((this.backValuedCheckS.selected && this.backValuedPayS.visible) ? this.totalBackValuedS : 0 )+ ((this.stoppedCutOffCheckS.selected && this.stoppedCutOffPayS.visible) ? this.totalStoppedCutOffS : 0 )+ ((this.cutOffPassedCheckS.selected && this.cutOffPassedPayS.visible) ?  this.totalCutOffPassedS : 0);
      this.totalPay = this.statusPayArray.length - ((!this.waitingCheck.selected && this.waitingPay.visible) ? this.totalWaiting : 0 ) - ((!this.stoppedCheck.selected && this.stoppedPay.visible) ? this.totalStopped : 0) - ((!this.blockedCheck.selected && this.blockedPay.visible) ? this.totalBlocked : 0) - ((!this.blockedPrevStoppedCheck.selected && this.blockedPrevStoppedPay.visible) ? this.totalBlockedPrevS : 0);
    }
    this.disableEnableReleaseButton();
  }
  changeCheckBoxStoppedCutOffS() : void {
    if(!this.stoppedCutOffCheckS.selected) {
      this.totalalreadyStoppedBlocked -= this.totalStoppedCutOffS;
      this.totalPay -= this.totalStoppedCutOffS;
    }
    else {
      this.totalalreadyStoppedBlocked = ((this.inputCutOffCheckS.selected && this.inputCutOffPayS.visible) ? this.totalInputCutOffS : 0 ) + ((this.backValuedCheckS.selected && this.backValuedPayS.visible) ? this.totalBackValuedS : 0 )+ ((this.stoppedCutOffCheckS.selected && this.stoppedCutOffPayS.visible) ? this.totalStoppedCutOffS : 0 )+ ((this.cutOffPassedCheckS.selected && this.cutOffPassedPayS.visible) ?  this.totalCutOffPassedS : 0);
      this.totalPay = this.statusPayArray.length - ((!this.waitingCheck.selected && this.waitingPay.visible) ? this.totalWaiting : 0 ) - ((!this.stoppedCheck.selected && this.stoppedPay.visible) ? this.totalStopped : 0) - ((!this.blockedCheck.selected && this.blockedPay.visible) ? this.totalBlocked : 0) - ((!this.blockedPrevStoppedCheck.selected && this.blockedPrevStoppedPay.visible) ? this.totalBlockedPrevS : 0);
    }
    this.disableEnableReleaseButton();
  }
  changeCheckBoxCutOffPassedS() : void {
    if(!this.cutOffPassedCheckS.selected) {
      this.totalalreadyStoppedBlocked -= this.totalCutOffPassedS;
      this.totalPay -= this.totalCutOffPassedS;
    }
    else {
      this.totalalreadyStoppedBlocked = ((this.inputCutOffCheckS.selected && this.inputCutOffPayS.visible) ? this.totalInputCutOffS : 0 ) + ((this.backValuedCheckS.selected && this.backValuedPayS.visible) ? this.totalBackValuedS : 0 )+ ((this.stoppedCutOffCheckS.selected && this.stoppedCutOffPayS.visible) ? this.totalStoppedCutOffS : 0 )+ ((this.cutOffPassedCheckS.selected && this.cutOffPassedPayS.visible) ?  this.totalCutOffPassedS : 0);
      this.totalPay = this.statusPayArray.length - ((!this.waitingCheck.selected) ? this.totalWaiting : 0 ) - ((!this.stoppedCheck.selected) ? this.totalStopped : 0) - ((!this.blockedCheck.selected) ? this.totalBlocked : 0) - ((!this.blockedPrevStoppedCheck.selected && this.blockedPrevStoppedPay.visible) ? this.totalBlockedPrevS : 0);
    }
    this.disableEnableReleaseButton();
  }


  /****************Blocked and Previsouly Stopped************/

  changeCheckBoxBlockedPrevStopped() : void {
    if(!this.blockedPrevStoppedCheck.selected) {
      this.totalPay -= this.totalBlockedPrevS;
      this.backValuedCheckPrevS.selected = false;
      this.stoppedCutOffCheckPrevS.selected = false;
      this.cutOffPassedCheckPrevS.selected = false;
      this.inputCutOffCheckPrevS.selected = false;
    }
    else {
      this.backValuedCheckPrevS.selected = this.backValuedPayPrevS.visible;
      this.stoppedCutOffCheckPrevS.selected = this.stoppedCutOffPayPrevS.visible;
      this.inputCutOffCheckPrevS.selected = this.inputCutOffPayPrevS.visible;
      this.cutOffPassedCheckPrevS.selected = this.cutOffPassedPayPrevS.visible;
      this.totalBlockedPrevS = ((this.inputCutOffCheckPrevS.selected && this.inputCutOffPayPrevS.visible) ? this.totalInputCutOffPrevS : 0 ) + ((this.backValuedCheckPrevS.selected && this.backValuedPayPrevS.visible) ? this.totalBackValuedPrevS : 0 )+ ((this.stoppedCutOffCheckPrevS.selected && this.stoppedCutOffPayPrevS.visible) ? this.totalStoppedCutOffPrevS : 0 )+ ((this.cutOffPassedCheckPrevS.selected && this.cutOffPassedPayPrevS.visible) ?  this.totalCutOffPassedPrevS : 0);
      this.totalPay = this.statusPayArray.length - ((!this.waitingCheck.selected && this.waitingPay.visible) ? this.totalWaiting : 0 ) - ((!this.stoppedCheck.selected && this.stoppedPay.visible) ? this.totalStopped : 0) - ((!this.blockedCheck.selected && this.blockedPay.visible) ? this.totalBlocked : 0) - ((!this.blockedStoppedCheck.selected && this.blockedStoppedPay.visible) ? this.totalalreadyStoppedBlocked : 0);
    }
    this.disableEnableReleaseButton();
  }

  changeCheckBoxBackValuedPrevS() : void {
    if(!this.backValuedCheckPrevS.selected) {
      this.totalBlockedPrevS -= this.totalBackValuedPrevS;
      this.totalPay -= this.totalBackValuedPrevS;
    }
    else {
      this.totalBlockedPrevS = ((this.inputCutOffCheckPrevS.selected && this.inputCutOffPayPrevS.visible) ? this.totalInputCutOffPrevS : 0 ) + ((this.backValuedCheckPrevS.selected && this.backValuedPayPrevS.visible) ? this.totalBackValuedPrevS : 0 )+ ((this.stoppedCutOffCheckPrevS.selected && this.stoppedCutOffPayPrevS.visible) ? this.totalStoppedCutOffPrevS : 0 )+ ((this.cutOffPassedCheckPrevS.selected && this.cutOffPassedPayPrevS.visible) ?  this.totalCutOffPassedPrevS : 0);
      this.totalPay = this.statusPayArray.length - ((!this.waitingCheck.selected && this.waitingPay.visible) ? this.totalWaiting : 0 ) - ((!this.stoppedCheck.selected && this.stoppedPay.visible) ? this.totalStopped : 0) - ((!this.blockedCheck.selected && this.blockedPay.visible) ? this.totalBlocked : 0) - ((!this.blockedStoppedCheck.selected && this.blockedStoppedPay.visible) ? this.totalalreadyStoppedBlocked : 0);
    }
    this.disableEnableReleaseButton();
  }
  changeCheckBoxInputCutOffPrevS() : void {
    if(!this.inputCutOffCheckPrevS.selected) {
      this.totalBlockedPrevS -= this.totalInputCutOffPrevS;
      this.totalPay -= this.totalInputCutOffPrevS;
    }
    else {
      this.totalBlockedPrevS = ((this.inputCutOffCheckPrevS.selected && this.inputCutOffPayPrevS.visible) ? this.totalInputCutOffPrevS : 0 ) + ((this.backValuedCheckPrevS.selected && this.backValuedPayPrevS.visible) ? this.totalBackValuedPrevS : 0 )+ ((this.stoppedCutOffCheckPrevS.selected && this.stoppedCutOffPayPrevS.visible) ? this.totalStoppedCutOffPrevS : 0 )+ ((this.cutOffPassedCheckPrevS.selected && this.cutOffPassedPayPrevS.visible) ?  this.totalCutOffPassedPrevS : 0);
      this.totalPay = this.statusPayArray.length - ((!this.waitingCheck.selected && this.waitingPay.visible) ? this.totalWaiting : 0 ) - ((!this.stoppedCheck.selected && this.stoppedPay.visible) ? this.totalStopped : 0) - ((!this.blockedCheck.selected && this.blockedPay.visible) ? this.totalBlocked : 0) - ((!this.blockedStoppedCheck.selected && this.blockedStoppedPay.visible) ? this.totalalreadyStoppedBlocked : 0);
    }
    this.disableEnableReleaseButton();
  }
  changeCheckBoxStoppedCutOffPrevS() : void {
    if(!this.stoppedCutOffCheckPrevS.selected) {
      this.totalBlockedPrevS -= this.totalStoppedCutOffPrevS;
      this.totalPay -= this.totalStoppedCutOffPrevS;
    }
    else {
      this.totalBlockedPrevS = ((this.inputCutOffCheckPrevS.selected && this.inputCutOffPayPrevS.visible) ? this.totalInputCutOffPrevS : 0 ) + ((this.backValuedCheckPrevS.selected && this.backValuedPayPrevS.visible) ? this.totalBackValuedPrevS : 0 )+ ((this.stoppedCutOffCheckPrevS.selected && this.stoppedCutOffPayPrevS.visible) ? this.totalStoppedCutOffPrevS : 0 )+ ((this.cutOffPassedCheckPrevS.selected && this.cutOffPassedPayPrevS.visible) ?  this.totalCutOffPassedPrevS : 0);
      this.totalPay = this.statusPayArray.length - ((!this.waitingCheck.selected && this.waitingPay.visible) ? this.totalWaiting : 0 ) - ((!this.stoppedCheck.selected && this.stoppedPay.visible) ? this.totalStopped : 0) - ((!this.blockedCheck.selected && this.blockedPay.visible) ? this.totalBlocked : 0) - ((!this.blockedStoppedCheck.selected && this.blockedStoppedPay.visible) ? this.totalalreadyStoppedBlocked : 0);
    }
    this.disableEnableReleaseButton();
  }
  changeCheckBoxCutOffPassedPrevS() : void {
    if(!this.cutOffPassedCheckPrevS.selected) {
      this.totalBlockedPrevS -= this.totalCutOffPassedPrevS;
      this.totalPay -= this.totalCutOffPassedPrevS;
    }
    else {
      this.totalBlockedPrevS = ((this.inputCutOffCheckPrevS.selected && this.inputCutOffPayPrevS.visible) ? this.totalInputCutOffPrevS : 0 ) + ((this.backValuedCheckPrevS.selected && this.backValuedPayPrevS.visible) ? this.totalBackValuedPrevS : 0 )+ ((this.stoppedCutOffCheckPrevS.selected && this.stoppedCutOffPayPrevS.visible) ? this.totalStoppedCutOffPrevS : 0 )+ ((this.cutOffPassedCheckPrevS.selected && this.cutOffPassedPayPrevS.visible) ?  this.totalCutOffPassedPrevS : 0);
      this.totalPay = this.statusPayArray.length - ((!this.waitingCheck.selected && this.waitingPay.visible) ? this.totalWaiting : 0 ) - ((!this.stoppedCheck.selected && this.stoppedPay.visible) ? this.totalStopped : 0) - ((!this.blockedCheck.selected && this.blockedPay.visible) ? this.totalBlocked : 0) - ((!this.blockedStoppedCheck.selected && this.blockedStoppedPay.visible) ? this.totalalreadyStoppedBlocked : 0);
    }
    this.disableEnableReleaseButton();
  }

  disableEnableReleaseButton() :void {
    if(this.totalPay == 0 || this.totalPay == (this.totalReleased + this.totalCancel)) {
      this.releaseButton.enabled = false;
    } else {
      this.releaseButton.enabled = true;
    }
  }

}
