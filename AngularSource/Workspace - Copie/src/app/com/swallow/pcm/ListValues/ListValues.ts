import { Component, OnInit, ViewContainerRef, ViewChild, ComponentFactoryResolver, NgModule, OnChanges, ElementRef } from '@angular/core';
import {
  SwtLocalStorage,
  HTTPComms,
  SwtButton,
  SwtCommonGrid,
  SwtHelpButton,
  SwtCheckBox,
  SwtToolBoxModule,
  Logger,
  JSONReader,
  SwtAlert,
  Alert,
  SwtUtil,
  SwtTextInput,
  SwtStepper,
  SwtLabel,
  SwtLoadingImage,
  SwtHelpWindow,
  CommonService,
  SwtTabNavigatorHandler,
  HBox,
  VBox,
  SwtRadioItem,
  SwtComboBox,
  SwtDateField,
  SwtPanel,
  SwtPopUpManager,
  SwtModule,
  SwtCanvas,
  StringUtils,
  ExternalInterface,
} from "swt-tool-box";
import { HttpClient } from "@angular/common/http";
import { Routes, RouterModule } from "@angular/router";
import { ModuleWithProviders } from "@angular/compiler/src/core";
import { forEach } from '@angular/router/src/utils/collection';
//import { Help } from '../../system/help/help/help';
/**
 *  ListValues.ts
 *  screen used to get the list values of the selected item in the search screen  
 *  <AUTHOR>
 *  @reviewed by Rihab JABALLAH on 03-08-2018
 *  @version
 *  @date April-2018
 */
@Component( {
    selector: 'app-list-values',
    templateUrl: './ListValues.html',
    styleUrls: ['./ListValues.css']
} )
export class ListValues extends SwtModule implements OnInit{
    rows: any;
    _selectedRowData: any;
    listValues: SwtCommonGrid;
    //**********************  Data Grid ***************************************************/ 
    @ViewChild( 'customGrid' ) customGrid: SwtCanvas;
    //**********************  TextInput ***************************************************/ 
    @ViewChild( 'filterText' ) filterText: SwtTextInput;
    @ViewChild( 'labelFilter' ) labelFilter: SwtLabel;
    //**********************  Buttons ****************************************************/ 
    @ViewChild( 'okButton', { read: SwtButton } ) okButton: SwtButton;
    @ViewChild( 'closeButton', { read: SwtButton } ) closeButton: SwtButton;
    /*********HBox*****************/
    @ViewChild('filterHbox') filterHbox: HBox;
    gridJSONList:any ;
    gridJSONListValue: any;
    // get code of the selected column in search screen
    private columnCode: string = null;
    // get the current operation used in search screen
    private operation: string = null;
    // get label of the selected column in search screen
    private columnLabel: string = null;
    //To hold the help message
    private message: string = null;
    //To hold the dataSource
    private dataSource: string = null;
    /** 
  * Private parameters 
  **/
    private logger: Logger;
    private programId: string = "404";  //set program
    private componentId: string = null; //hold componentId
    private menuAccess: string = "0";//To store menu access
    private alertMsg: string;
    private alertMsgToShow: string;
    /**
     * Data Objects
     **/
    private jsonReader: JSONReader = new JSONReader();
    private lastRecievedJSON;
    private prevRecievedJSON;
    private swtAlert: SwtAlert;
    /**
     * Communication Objects
     **/
    private addHelpURL: string = null;
    private addContent: string = null;
    private helpURL: string = null;
    private moduleReportURL: string = null;
    public viewOnly : boolean = false;
    private filterTextOfInput: string = "";


    dataProviderTemp: any;
    constructor( private element: ElementRef, httpClient: HttpClient, private commonService: CommonService ) {
        super( element, commonService );
        this.logger = new Logger( "ListValues", httpClient );
    }


  ngOnInit(): void{
    if (this.dataSource == 'fromStopRules' || this.dataSource == 'fromDashboard') {
      this.filterHbox.visible = false;
    }

    if(this.viewOnly) {
      this.okButton.enabled = false;
    }
  }
    onLoad() {
        var errorLocation = 0;
        try {
            this.logger.info( "method [onLoad] - START " );
            this.listValues = <SwtCommonGrid>this.customGrid.addChild(SwtCommonGrid);
            this.close = this.escapeHandler;
            this.helpURL = this.parentDocument.helpURL;
            if(this.listValues.selectedItem != null && !this.viewOnly) {
              this.okButton.enabled = true;
            }
            //Gets the help window title
            this.message = SwtUtil.getCommonMessages( 'searchScreen.message.help_message_add_value' );
            //Gets the filter label
            this.labelFilter.text =  "Filter"; //this.getCommonMessages( 'searchScreen.label.filter' );
            //this.gridJSONList = this.parentDocument.jsonReader.getSelects();

            if ( this.dataSource == "fromGridData" ) {
              let metadata: any = {
                "columns": {
                  "column": [
                    {
                      columnorder: 0,
                      dataelement: "label",
                      draggable: true,
                      filterable: true,
                      format: "",
                      heading: this.columnLabel,
                      sort: true,
                      type: "str",
                      visible: true,
                      visible_default: true,
                      width: 315
                    }
                  ]
                }
              };
                this.gridJSONList = this.parentDocument.originalJSONList; //.toChange == "N";
                //this.listValues.dataProvider = this.gridJSONList;
                
              let datarows = [];
              for ( let index in  this.gridJSONList) {
                if((this.gridJSONList[index].toChange == undefined || this.gridJSONList[index].toChange == 'N' ) && typeof this.gridJSONList[index] === 'object' ) {
                  datarows.push( { "label": this.gridJSONList[index]} );
                }
              }
              this.gridJSONList = datarows;
                setTimeout( () => {
                  this.listValues.CustomGrid( metadata );
                  this.listValues.gridData = { row: this.gridJSONList, size: this.gridJSONList.length};
                  this.dataProviderTemp = this.listValues.dataProvider;
                }, 0);

            }
            else {

                setTimeout(() => {
                    // allow datagrid miltiselection if the operation is "in" or "not in"
                    if ( this.operation == "in" || this.operation == "not in" ) {
                        errorLocation = 60;
                        this.listValues.allowMultipleSelection = true;
                    }
                    //parse and add data to the listValues grid
                    this.loadDataGrid();
                }, 0 );

            }

            if(!this.viewOnly){
              this.listValues.onRowDoubleClick = ( selectedRowData ) => {
            //this.okButton.enabled = true;
                this.getSelectedValues( selectedRowData );
              };
            }
          this.listValues.onRowClick = ( selectedRowData ) => {
            if(!this.viewOnly) {
              if(this.listValues.selectedIndices.length >0){
                this.okButton.enabled = true;
              }else {
                this.okButton.enabled = false;
              }
            }
            //this.getSelectedValues( selectedRowData );
          };
            errorLocation = 80;
            
        }
        catch ( error ) {
            this.logger.error( "method [onLoad] - error : ", error, "- errorLocation :", errorLocation );

            SwtUtil.logError( error, SwtUtil.SYSTEM_MODULE_ID, this.commonService.getQualifiedClassName( this ) + ".ts", "onLoad", errorLocation );
        }
        this.logger.info( "method [onLoad] - END " );
    }


    /**
     * This method is used to handle escape event.
     */
    escapeHandler() {
      if(this.parentDocument && this.parentDocument.msgTypeButton)
        this.parentDocument.msgTypeButton.enabled = true;
    }

    /**
     * This method is used to load the POPUP 
     * dataGrid.
     */
    loadDataGrid() {
        var errorLocation = 0;
        var selectedIndexes = [];
        let metadata: any;
        var tempGridJson : any;
        let datarows = [];
        try {

            this.logger.info( "method [loadDataGrid] - START " );
            
            // adapt the data received from server to custom grid.
            
            if (this.parentDocument.jsonReader.getSelects()["select"].length) {
                this.gridJSONList = this.parentDocument.jsonReader.getSelects()["select"].find(x => x.id == this.columnCode);

                tempGridJson = this.deepCopy(this.parentDocument.jsonReader.getSelects()["select"].find(x => x.id == this.columnCode));

              this.gridJSONListValue = this.reverse(this.deepCopy(tempGridJson));


            }
            else {
                this.gridJSONList = [this.parentDocument.jsonReader.getSelects()["select"]].find(x => x.id == this.columnCode)

            }
          if (this.dataSource == 'fromStopRules'|| this.dataSource == 'fromDashboard' ) {
             // this.filterHbox.visible = false;
               metadata= {
                "columns": {
                  "column": [
                    {
                      columnorder: 0,
                      dataelement: "label",
                      draggable: true,
                      filterable: true,
                      format: "",
                      heading: "Key",
                      sort: true,
                      type: "str",
                      visible: true,
                      visible_default: true,
                      width: 150
                    },
                    {
                      columnorder: 0,
                      dataelement: "name",
                      draggable: true,
                      filterable: true,
                      format: "",
                      heading: "Value",
                      sort: true,
                      type: "str",
                      visible: true,
                      visible_default: true,
                      width: 250
                    }
                  ]
                }
              };
              if(this.dataSource == 'fromStopRules'){
                for ( let index = 2; index < this.gridJSONList.option.length; index++ ) {
                  if ( this.gridJSONListValue[index].value !== "All" ) {
                    datarows.push( { "label": this.gridJSONListValue[index], "name": this.gridJSONList.option[index]} );
                  }
                }
              }



              if(this.dataSource == 'fromDashboard'){
                for ( let index = 1; index < this.gridJSONList.option.length; index++ ) {
                  if ( this.gridJSONListValue[index].value !== "All" ) {
                    datarows.push( { "label": this.gridJSONListValue[index], "name": this.gridJSONList.option[index]} );
                  }
                }
              }

              this.gridJSONList = datarows;
              this.listValues.CustomGrid( metadata );
              this.listValues.gridData = { row: this.gridJSONList, size: this.gridJSONList.length };

              if(this.dataSource == 'fromDashboard'){
                 let selectedEntities = this.parentDocument.selectedItemsListEntity.split(',').map(String);
                  for ( let index = 0; index < this.gridJSONList.length; index++ ) {
                    if (this.columnCode == 'entityList' && this.parentDocument.selectedItemsListEntity != null && selectedEntities.indexOf(this.gridJSONList[index].label.content) !== -1) {
                      selectedIndexes.push(index);
                      this.listValues.setSelectedIndices(selectedIndexes);
                    }
                  }
                }
              //this.dataProviderTemp = this.listValues.dataProvider;
              for ( let index = 0; index < this.gridJSONList.length; index++ ) {
                if (this.columnCode == 'currencyList' && this.parentDocument.selectedItemsListCcy != null && this.parentDocument.selectedItemsListCcy.indexOf(this.gridJSONList[index].label.content) !== -1) {
                  selectedIndexes.push(index);
                  this.listValues.setSelectedIndices(selectedIndexes);
                } else if (this.columnCode == 'country' && this.parentDocument.selectedItemsListCountry != null && this.parentDocument.selectedItemsListCountry.indexOf(this.gridJSONList[index].label.content) !== -1) {
                  selectedIndexes.push(index);
                  this.listValues.setSelectedIndices(selectedIndexes);
                } else if (this.columnCode == 'counterParties' && this.parentDocument.selectedItemsListCounterParty != null && this.parentDocument.selectedItemsListCounterParty.indexOf(this.gridJSONList[index].label.content) !== -1) {
                  selectedIndexes.push(index);
                  this.listValues.setSelectedIndices(selectedIndexes);
                } else if (this.columnCode == 'source' && this.parentDocument.selectedItemsListSource != null && this.parentDocument.selectedItemsListSource.indexOf(this.gridJSONList[index].label.content) !== -1) {
                  selectedIndexes.push(index);
                  this.listValues.setSelectedIndices(selectedIndexes);
                } else if (this.columnCode == 'messageType' && this.parentDocument.selectedItemsListMessageType != null && this.parentDocument.selectedItemsListMessageType.indexOf(this.gridJSONList[index].label.content) !== -1) {
                  selectedIndexes.push(index);
                  this.listValues.setSelectedIndices(selectedIndexes);
                } else if (this.columnCode == 'AcctGrpList' && this.parentDocument.selectedItemsListAcctGrp != null && this.parentDocument.selectedItemsListAcctGrp.indexOf(this.gridJSONList[index].label.content) !== -1) {
                  selectedIndexes.push(index);
                  this.listValues.setSelectedIndices(selectedIndexes);
                }
              }
            } else {
               metadata= {
                "columns": {
                  "column": [
                    {
                      columnorder: 0,
                      dataelement: "label",
                      draggable: true,
                      filterable: true,
                      format: "",
                      heading: this.columnLabel,
                      sort: true,
                      type: "str",
                      visible: true,
                      visible_default: true,
                      width: 315
                    }
                  ]
                }
              };
              for ( let index = 0; index < this.gridJSONList.option.length; index++ ) {
                datarows.push( { "label": this.gridJSONList.option[index] } );
              }
              this.gridJSONList = datarows;
              this.listValues.CustomGrid( metadata );
              this.listValues.gridData = { row: this.gridJSONList, size: this.gridJSONList.length };
              this.dataProviderTemp = this.listValues.dataProvider;
            }


    


        }
        catch ( error ) {
            this.logger.error( "method [loadDataGrid] - error : ", error, "- errorLocation :", errorLocation );
    
            SwtUtil.logError( error, SwtUtil.SYSTEM_MODULE_ID, this.commonService.getQualifiedClassName( this ) + ".ts", "loadDataGrid", errorLocation );
        }
        this.logger.info( "method [loadDataGrid] - END " );

    }
    /**
     * getSelectedValues()
     * 
     * @param even:Event
     * 
     * Method called to get selected rows in the datagrid
     **/
    getSelectedValues(event): any {
        try {
            this.logger.info( "method [getSelectedValues] - START " );
            var errorLocation = 0;
            if ( this.parentDocument.selectedItemsListCodes ) {

                this.parentDocument.selectedItemsListCodes = "";
                for ( var i = 0; i < this.listValues.selectedItems.length; i++ ) {
                    this.parentDocument.selectedItemsListCodes += this.listValues.selectedItems[i].value;
                    if ( i != this.listValues.selectedItems.length - 1 )
                        this.parentDocument.selectedItemsListCodes += ",";
                }
            }

            errorLocation = 10;
            if ( this.dataSource == "fromGridData" ) {
                this.parentDocument.selectedItemsList = this.listValues.selectedItem.label.code;
            } else if (this.dataSource == 'fromStopRules' || this.dataSource == 'fromDashboard') {
              let chosenValues = '';
              for ( let index = 0; index < this.listValues.selectedItems.length; index++ ) {
                chosenValues += this.listValues.selectedItems[index].label.content + ',';
              }
              if ( chosenValues[chosenValues.length - 1] == "," ) {
                chosenValues = chosenValues.slice( 0, chosenValues.length - 1 );
              }

                if (this.columnCode == 'currencyList') {
                this.parentDocument.selectedItemsListCcy = chosenValues;

                } else if (this.columnCode == 'country') {
                this.parentDocument.selectedItemsListCountry = chosenValues;

                } else if (this.columnCode == 'counterParties') {
                this.parentDocument.selectedItemsListCounterParty = chosenValues;

                } else if (this.columnCode == 'source') {
                this.parentDocument.selectedItemsListSource = chosenValues;

                } else if (this.columnCode == 'messageType') {
                this.parentDocument.selectedItemsListMessageType = chosenValues;

                } else if (this.columnCode == 'AcctGrpList') {
                this.parentDocument.selectedItemsListAcctGrp = chosenValues;

                }else if (this.columnCode == 'entityList') {
                  this.parentDocument.selectedItemsListEntity= chosenValues;

                }
            }
            else {
                this.parentDocument.selectedItemsList = '';
                for ( let index = 0; index < this.listValues.selectedItems.length; index++ ) {
                    this.parentDocument.selectedItemsList += this.listValues.selectedItems[index].label.content + ',';
                }
              if ( this.parentDocument.selectedItemsList[this.parentDocument.selectedItemsList.length - 1] == "," ) {
                  this.parentDocument.selectedItemsList = this.parentDocument.selectedItemsList.slice( 0, this.parentDocument.selectedItemsList.length - 1 )
              }
            }

          this.result = {res: this.parentDocument.selectedItemsList, buttonClicked: 'okButton'};
          // this.close();
          SwtPopUpManager.getPopUpById("listValuesPopup").close();
          
        }
        catch ( error ) {
            this.logger.error( "method [getSelectedValues] - error : ", error, "- errorLocation :", errorLocation );

            SwtUtil.logError( error, SwtUtil.SYSTEM_MODULE_ID, this.commonService.getQualifiedClassName( this ) + ".ts", "getSelectedValues", errorLocation );
        }
        this.logger.info( "method [getSelectedValues] - END " );
    }
    


    doHelp() {
      ExternalInterface.call("help");
    }


    /**
     * close
     *
     * @param  event:Event
     *
     * Method to close this module
     */
    cancelHandler( event ): void {
        //Variable for errorLocation
        var errorLocation = 0;
        try
        {
            this.logger.info( "method [cancelHandler] - START " );
            if(this.parentDocument.msgTypeButton)
            this.parentDocument.msgTypeButton.enabled = true;
            SwtPopUpManager.getPopUpById("listValuesPopup").close();
            this.close();
        }
        catch ( error ) {
            this.logger.error( "method [cancelHandler] - error : ", error, "- errorLocation :", errorLocation );
    
            SwtUtil.logError( error, SwtUtil.SYSTEM_MODULE_ID, this.commonService.getQualifiedClassName( this ) + ".ts", "cancelHandler", errorLocation );
        }
        this.logger.info( "method [cancelHandler] - END " );
    }


    loadingHelpContent() {
        //Variable for errorLocation
        var errorLocation = 0;
        try
        {
            this.logger.info( "method [loadingHelpContent] - START " );
            this.parentDocument.loadingHelpContent();
        }
        catch ( error ) {
            this.logger.error( "method [loadingHelpContent] - error : ", error, "- errorLocation :", errorLocation );
    
            SwtUtil.logError( error, SwtUtil.SYSTEM_MODULE_ID, this.commonService.getQualifiedClassName( this ) + ".ts", "loadingHelpContent", errorLocation );
        }
        this.logger.info( "method [loadingHelpContent] - END " );
    }
    
    

   reverse(data) {
    try {
      let tempData = [];
      let tempVar;
      for (var index = 0; index < data.option.length; index++) {
        tempVar = {};
        if(data.option[index]) {
          tempVar.content = ''+ data.option[index].value;
          tempVar.value = ''+ data.option[index].content;

          tempData.push(tempVar);
        }
      }
      
      return tempData;
    } catch(error) {
      this.logger.error("method [reverse] - error ",error);
    }
  }
   deepCopy( mainObj ) {
    const objCopy = []; // objCopy will store a copy of the mainObj
    let key;

    for ( key in mainObj ) {
      objCopy[key] = mainObj[key]; // copies each property to the objCopy object
    }
    return objCopy;
  }
    
   /**
   * filtringGrid()
   *
   * @param item: string
   *
   * Method used to filter the data from grid
   **/
  filtringGrid( search: string ): void {
    let event: Event;
    try {
        this.filterTextOfInput = search;
        this.updateFilter();

    }
    catch ( error ) {
      console.log('error', error);
    }
  }
  updateFilter() {
  try {
    this.listValues.dataviewObj.beginUpdate();
    this.listValues.dataviewObj.setItems(this.listValues.gridData);
    this.listValues.dataviewObj.setFilterArgs({
      searchString: this.filterTextOfInput
    });
    this.listValues.dataviewObj.setFilter(this.filterFunction);
    this.listValues.dataviewObj.endUpdate();

    this.listValues.selectedIndex = -1;
}catch (e) {
    console.log('errror', e)
  }

  }
  filterFunction(item, args) {
    if (item.label && args.searchString && item.label.toLowerCase().indexOf(args.searchString.toLowerCase()) == -1) {
      return false;
    }
    return true;
  }

}
