<SwtModule (creationComplete)='onLoad()' width="100%" height="100%">
  <VBox width='100%' height='100%' paddingLeft="5" paddingRight="5" paddingTop="5">
    <Grid width="100%" height="94%" paddingLeft="5">

      <GridRow width="100%" height="28">
        <GridItem width="65%">
          <GridItem width="340">
            <GridItem width="180">
              <SwtLabel id="entity" #entity></SwtLabel>
            </GridItem>
            <GridItem>
              <SwtComboBox id="entityCombo" #entityCombo width="200" dataLabel="entityList" (change)="refreshComboList()">
              </SwtComboBox>
            </GridItem>
          </GridItem>
          <GridItem paddingLeft="50">
            <SwtLabel id="entityDesc" #entityDesc fontWeight="normal"></SwtLabel>
          </GridItem>
        </GridItem>
      </GridRow>

      <GridRow width="100%" height="28">
        <GridItem width="65%">
          <GridItem width="340">
            <GridItem width="180">
              <SwtLabel id="ccy" #ccy></SwtLabel>
            </GridItem>
            <GridItem>
              <SwtComboBox id="ccyCombo" #ccyCombo width="200" dataLabel="currencyList" (change)="refreshAccountComboList()">
              </SwtComboBox>
            </GridItem>
          </GridItem>
          <GridItem paddingLeft="50">
            <SwtLabel id="ccyDesc" #ccyDesc fontWeight="normal"></SwtLabel>
          </GridItem>
        </GridItem>
      </GridRow>

      <GridRow width="100%" height="28">
        <GridItem width="65%">
          <GridItem width="340">
            <GridItem width="180">
              <SwtLabel id="acct" #acct></SwtLabel>
            </GridItem>
            <GridItem>
              <SwtComboBox id="acctCombo" #acctCombo width="200" dataLabel="accountList" (change)="refreshLabel()">
              </SwtComboBox>
            </GridItem>
          </GridItem>
          <GridItem paddingLeft="50">
            <SwtLabel id="acctDesc" #acctDesc fontWeight="normal"></SwtLabel>
          </GridItem>
        </GridItem>
      </GridRow>


      <GridRow width="100%" height="28">
          <GridItem width="400">
            <GridItem width="180">
              <SwtLabel id="startDateLabel" #startDateLabel></SwtLabel>
            </GridItem>
            <GridItem>
              <SwtDateField id="startDateField" #startDateField (change)="validateDateField(startDateField)"
              width="70"></SwtDateField>
            </GridItem>
          </GridItem>
      </GridRow>

      <GridRow width="100%" height="28">
          <GridItem width="400">
            <GridItem width="180">
              <SwtLabel id="endDateLabel" #endDateLabel></SwtLabel>
            </GridItem>
            <GridItem>
              <SwtDateField id="endDateField" #endDateField (change)="validateDateField(endDateField)"
              width="70"></SwtDateField>
            </GridItem>
          </GridItem>
      </GridRow>

      
      <GridRow width="100%" height="28">
          <GridItem width="300">
            <GridItem width="180">
              <SwtLabel id="tier" #tier></SwtLabel>
            </GridItem>
            <GridItem>
              <SwtTextInput #tierTxtInput id="tierTxtInput"
              (focusOut)="validateReserve(tierTxtInput)"
              editable="true" width="200" textAlign="right" restrict="0-9mtb.,"></SwtTextInput>
            </GridItem>
          </GridItem>
      </GridRow>

      <GridRow width="100%" height="28">
          <GridItem width="300">
            <GridItem width="180">
              <SwtLabel id="minReserve" #minReserve></SwtLabel>
            </GridItem>
            <GridItem>
              <SwtTextInput #minReserveTxtInput id="minReserveTxtInput"(focusOut)="validateReserve(minReserveTxtInput)" restrict="0-9-,.TBMtbm"
              editable="true" width="200" textAlign="right"></SwtTextInput>
            </GridItem>
          </GridItem>
      </GridRow>

      <GridRow width="100%" height="28">
        <GridItem width="65%">
          <GridItem width="340">
            <GridItem width="180">
              <SwtLabel id="chargeThres" #chargeThres></SwtLabel>
            </GridItem>
            <GridItem>
              <SwtLabel id="chargeThresLabel" #chargeThresLabel width="200"
              fontWeight="normal">
              </SwtLabel>
            </GridItem>
          </GridItem>
          <GridItem paddingLeft="50">
            <SwtLabel id="chargeThresDesc" #chargeThresDesc fontWeight="normal"></SwtLabel>
          </GridItem>
        </GridItem>
      </GridRow>

      <GridRow width="100%" height="28">
          <GridItem width="300">
            <GridItem width="180">
              <SwtLabel id="targetAvgBalance" #targetAvgBalance></SwtLabel>
            </GridItem>
            <GridItem>
              <SwtTextInput #targetAvgBalTxtInput id="targetAvgBalTxtInput" (focusOut)="validateReserve(targetAvgBalTxtInput)" restrict="0-9-,.TBMtbm"
              editable="true" width="200" textAlign="right"></SwtTextInput>
            </GridItem>
          </GridItem>
      </GridRow>
      <GridRow width="100%" height="28">
        <GridItem width="300">
          <GridItem width="180">
            <SwtLabel id="mintargetBalance" #mintargetBalance></SwtLabel>
          </GridItem>
          <GridItem>
            <SwtTextInput #mintargetBalanceTxtInput id="mintargetBalanceTxtInput" (focusOut)="validateReserve(mintargetBalanceTxtInput)" restrict="0-9-,.TBMtbm"
            editable="true" width="200" textAlign="right"></SwtTextInput>
          </GridItem>
        </GridItem>
    </GridRow>

      <GridRow width="100%" height="28">
          <GridItem width="300">
            <GridItem width="180">
              <SwtLabel id="fillDays" #fillDays></SwtLabel>
            </GridItem>
            <GridItem>
              <SwtTextInput #fillDaysTxtInput id="fillDaysTxtInput" (change)="getDifferenceInDays()"
              editable="true" width="200" textAlign="right" restrict="0-9"></SwtTextInput>
            </GridItem>
          </GridItem>
      </GridRow>

      <GridRow width="100%" height="28">
          <GridItem width="300">
            <GridItem width="180">
              <SwtLabel id="fillBalance" #fillBalance></SwtLabel>
            </GridItem>
            <GridItem>
              <SwtTextInput #fillBalanceTxtInput id="fillBalanceTxtInput" (focusOut)="validateReserve(fillBalanceTxtInput)" restrict="0-9mtb.,"
              editable="true" width="200" textAlign="right"></SwtTextInput>
            </GridItem>
          </GridItem>
      </GridRow>

      <GridRow width="100%" height="28">
        <GridItem width="300">
      
          <GridItem width="180">
            <SwtLabel id="eodBalSrcLbl" #eodBalSrcLbl></SwtLabel>
          </GridItem>
      
          <GridItem>
            <SwtRadioButtonGroup #eodBalSrcOptions id="eodBalSrcOptions"  align="horizontal" width="100%">
              <SwtRadioItem value="I" width="135" groupName="eodBalSrcOptions"  id="interBal" #interBal>
              </SwtRadioItem>
              <SwtRadioItem value="E" width="100" groupName="eodBalSrcOptions" selected="true" id="extBal" #extBal></SwtRadioItem>
            </SwtRadioButtonGroup>
          </GridItem>
      
        </GridItem>
      </GridRow>


      <GridRow width="100%" height="28">
          <GridItem width="30">
            <SwtCheckBox #excludeFillPeriodFromAvg id="excludeFillPeriodFromAvg"  selected="false"></SwtCheckBox>
          </GridItem>
          <GridItem>
            <SwtLabel id="excludeFillPeriodFromAvgLabel" #excludeFillPeriodFromAvgLabel ></SwtLabel>
          </GridItem>
    </GridRow>


    </Grid>

   
    <SwtCanvas width="100%" height="35">
      <HBox width="100%">
        <HBox paddingLeft="5" width="90%">
          <SwtButton [buttonMode]="true" id="saveButton" #saveButton (click)="checkIfRecordExists()" >
          </SwtButton>
          <SwtButton [buttonMode]="true" id="cancelButton" #cancelButton (click)=" popupClosed()">
          </SwtButton>
        </HBox>
        <HBox width="10%" horizontalAlign="right" paddingLeft="5">
          <SwtLoadingImage #loadingImage></SwtLoadingImage>
        </HBox>
    
      </HBox>
    </SwtCanvas>
  </VBox>
</SwtModule>
