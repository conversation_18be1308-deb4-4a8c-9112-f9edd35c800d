import { Component, OnInit, ModuleWithProviders, NgModule, ElementRef, ViewChild } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';
import { SwtToolBoxModule, SwtUtil, CommonService, SwtAlert, JSONReader, HTTPComms, SwtDateField, SwtButton, SwtLabel, SwtCanvas, SwtCommonGrid, ExternalInterface, SwtLoadingImage, Logger } from 'swt-tool-box';
import moment from 'moment';
declare var instanceElement: any;

@Component({
  selector: 'app-acct-ccy-period-maint-log',
  templateUrl: './AcctCcyPeriodMaintLog.html',
  styleUrls: ['./AcctCcyPeriodMaintLog.css']
})
export class AcctCcyPeriodMaintLog implements OnInit {

  @ViewChild('loadingImage') loadingImage: SwtLoadingImage;
  
  /***********SwtCanvas***********/
  @ViewChild('logGridContainer') logGridContainer: SwtCanvas;

  /***********SwtDateField***********/
  @ViewChild('fromDateField') fromDateField: SwtDateField;
  @ViewChild('toDateField') toDateField: SwtDateField;

  /***********SwtButton***********/
  @ViewChild('refreshButton') refreshButton: SwtButton;
  @ViewChild('viewButton') viewButton: SwtButton;
  @ViewChild('closeButton') closeButton: SwtButton;

  /***********SwtLabel***********/
  @ViewChild('fromDateLabel') fromDateLabel: SwtLabel;
  @ViewChild('toDateLabel') toDateLabel: SwtLabel;

  private swtAlert: SwtAlert;
  private logger: Logger = null;
  private menuAccessId;
  private screenName;
  private parameters;
   /**
   * Data Objects
   **/
  public jsonReader: JSONReader = new JSONReader();
  public lastRecievedJSON;
  public prevRecievedJSON;
  public initReceivedJSON;
  /**
    * Communication Objects
    **/
  public inputData = new HTTPComms(this.commonService);
  public baseURL: string = SwtUtil.getBaseURL();
  private actionMethod: string = "";
  private actionPath: string = "";
  private requestParams = [];
  private _invalidComms: string;
  private logGrid: SwtCommonGrid;
  private currencyPattern: string;
  private dateFormat: string;
  private fromDate: string;
  private toDate: string;
  private selectedtRow;
  private action: string;
  private reference: string;
  private ccyCode: string;
  constructor(private commonService: CommonService, private element: ElementRef) {
    //super(element, commonService);
    this.logger = new Logger('Account Currency Period maintenance', this.commonService.httpclient);
    this.swtAlert = new SwtAlert(commonService);
  }

  ngOnInit() {
    instanceElement = this;
    this.logGrid = <SwtCommonGrid>this.logGridContainer.addChild(SwtCommonGrid);
    this.toDateLabel.text= SwtUtil.getPredictMessage('ccyAccMaintPeriod.tooltip.to', null);
    this.fromDateLabel.text= SwtUtil.getPredictMessage('ccyAccMaintPeriod.tooltip.from', null); 
    this.refreshButton.label = SwtUtil.getPredictMessage('button.refresh', null);
    this.refreshButton.toolTip = SwtUtil.getPredictMessage('tooltip.refresh', null);
    this.viewButton.label = SwtUtil.getPredictMessage('button.view', null);
    this.viewButton.toolTip = SwtUtil.getPredictMessage('ccyAccMaintPeriod.tooltip.view', null);
    this.closeButton.label = SwtUtil.getPredictMessage('button.close', null);
    this.closeButton.toolTip = SwtUtil.getPredictMessage('tooltip.close', null);
  }

  
  onLoad(){
    //Variable for errorLocation
    let errorLocation = 0;
    try {
    this.requestParams = [];
    this.action= window.opener.instanceElement.screenName;
    errorLocation = 10;
    this.reference= window.opener.instanceElement.reference;
    //Mantis 6135
    this.ccyCode= window.opener.instanceElement.ccyCode;
    errorLocation = 20;
    this.menuAccessId = ExternalInterface.call('eval', 'menuAccessId');
    if (this.menuAccessId) {
      if (this.menuAccessId !== "") {
        this.menuAccessId = Number(this.menuAccessId);
      }
    }
    errorLocation = 30;
    this.inputData.cbStart = this.startOfComms.bind(this);
    this.inputData.cbStop = this.endOfComms.bind(this);
    this.inputData.cbResult = (event) => {
      this.inputDataResult(event);
    };
    errorLocation = 40;
    this.inputData.cbFault = this.inputDataFault.bind(this);
    this.inputData.encodeURL = false;
    this.actionPath = "accountPeriod.do?";
    this.actionMethod = 'method=displayLog';
    errorLocation = 50;
    this.requestParams['menuAccessId'] = this.menuAccessId;
    errorLocation = 60;
    this.requestParams['fromDate'] = this.fromDateField.text;
    errorLocation = 70;
    this.requestParams['toDate'] =  this.toDateField.text;
    errorLocation = 80;
    this.requestParams['reference'] =  this.reference;
    errorLocation = 90;
    this.requestParams['action'] =  this.action;
    this.inputData.url = this.baseURL + this.actionPath + this.actionMethod;
    errorLocation = 100;
    this.inputData.send(this.requestParams);

      this.logGrid.onRowClick = (event) => {
        this.cellClickEventHandler(event);
      };
    } catch (error) {
      // log the error in ERROR LOG
      this.logger.error('method [onLoad] - error: ', error, 'errorLocation: ', errorLocation);
      SwtUtil.logError(error, SwtUtil.PREDICT_MODULE_ID, 'AcctCcyPeriodMaintLog.ts', "onLoad", errorLocation);
    }
  }

  /** This method is called when selecting a row from bottom grid**/

  cellClickEventHandler(event): void {
    //Variable for errorLocation
    let errorLocation = 0;
    try {
      this.logGrid.refresh();
      errorLocation = 10;
      if (this.logGrid.selectedIndex >= 0) {
        errorLocation = 20;
        this.viewButton.enabled = true;
        this.viewButton.buttonMode = true;
        this.selectedtRow = this.logGrid.selectedItem;
      } else {
        errorLocation = 30;
        this.viewButton.enabled = false;
        this.viewButton.buttonMode = false;

      }
    } catch (error) {
      // log the error in ERROR LOG
      this.logger.error('method [cellClickEventHandler] - error: ', error, 'errorLocation: ', errorLocation);
      SwtUtil.logError(error, SwtUtil.PREDICT_MODULE_ID, 'AcctCcyPeriodMaintLog.ts', "cellClickEventHandler", errorLocation);
    }
  }

  inputDataResult(event): void {
    //Variable for errorLocation
    let errorLocation = 0;
    try {
    // Checks the inputData and stops the communication
    if (this.inputData.isBusy()) {
      this.inputData.cbStop();
    } else {
      this.lastRecievedJSON = event;
      this.jsonReader.setInputJSON(this.lastRecievedJSON);
      errorLocation = 10;
      //this.dataExport.enabled = true;

      if (this.jsonReader.getRequestReplyStatus()) {
        if ((this.lastRecievedJSON != this.prevRecievedJSON)) {
          this.logGrid.selectedIndex=-1;
          //this.currencyPattern = this.jsonReader.getSingletons().currencyPattern;
          this.dateFormat = this.jsonReader.getSingletons().dateFormat;
          errorLocation = 20;
          this.fromDate=this.jsonReader.getSingletons().fromDate;
          this.toDate=this.jsonReader.getSingletons().toDate;
          errorLocation = 30;
          this.fromDateField.formatString = this.dateFormat.toLowerCase();
          this.fromDateField.text = this.fromDate;
          errorLocation = 40;
          this.toDateField.formatString = this.dateFormat.toLowerCase();
          this.toDateField.text = this.toDate;
          errorLocation = 50;
          if (!this.jsonReader.isDataBuilding()) {
            const obj = { columns: this.lastRecievedJSON.AcctCcyMaintPeriod.acctCcyMaintPeriodLogGrid.metadata.columns };
            errorLocation = 60;
            this.logGrid.CustomGrid(obj);
            errorLocation = 70;
            var gridRows = this.lastRecievedJSON.AcctCcyMaintPeriod.acctCcyMaintPeriodLogGrid.rows;
            errorLocation = 80;
            if (gridRows.size > 0) {
              this.logGrid.gridData = gridRows;
              errorLocation = 90;
              this.logGrid.setRowSize = this.jsonReader.getRowSize();
              this.logGrid.refresh();
              //this.dataExport.enabled = true;
            }
            else {
              this.logGrid.gridData = { size: 0, row: [] };
              //this.dataExport.enabled = false;
            }
            this.prevRecievedJSON = this.lastRecievedJSON;
          }
        }
      } else {
        if (this.lastRecievedJSON.hasOwnProperty("request_reply")) {
          this.swtAlert.error(this.jsonReader.getRequestReplyMessage() + "\n" + this.jsonReader.getRequestReplyLocation(), "Error");
          }

        }
      }
    } catch (error) {
      // log the error in ERROR LOG
      this.logger.error('method [cellClickEventHandler] - error: ', error, 'errorLocation: ', errorLocation);
      SwtUtil.logError(error, SwtUtil.PREDICT_MODULE_ID, 'AcctCcyPeriodMaintLog.ts', "cellClickEventHandler", errorLocation);
    }
  }

  validateDateField(dateField) {
    //Variable for errorLocation
    let errorLocation = 0;
    try {
      let date;
      let alert = SwtUtil.getPredictMessage('alert.enterValidDate', null);
      errorLocation = 10;
      if (dateField.text) {

        date = moment(dateField.text, this.dateFormat.toUpperCase(), true);
        errorLocation = 20;
        if (!date.isValid()) {
          this.swtAlert.error(alert, null, null, null, () => {
            errorLocation = 30;
            this.setFocusDateField(dateField)
          });
          return false;
        }
      } else {
        this.swtAlert.error(alert, null, null, null, () => {
          errorLocation = 40;
          this.setFocusDateField(dateField)
        });
        return false;
      }
      dateField.selectedDate = date.toDate();
      errorLocation = 50;  
      this.updateData();   
    } catch (error) {
      // log the error in ERROR LOG
      this.logger.error('method [validateDateField] - error: ', error, 'errorLocation: ', errorLocation);
      SwtUtil.logError(error, SwtUtil.PREDICT_MODULE_ID, 'AcctCcyPeriodMaintLog.ts', "validateDateField", errorLocation);
    }

    return true;
  }

  setFocusDateField(dateField) {
    //Variable for errorLocation
    let errorLocation = 0;
    try {
      dateField.setFocus();
      //dateField.text = this.jsonReader.getSingletons().displayedDate;
    } catch (error) {
      // log the error in ERROR LOG
      this.logger.error('method [validateDateField] - error: ', error, 'errorLocation: ', errorLocation);
      SwtUtil.logError(error, SwtUtil.PREDICT_MODULE_ID, 'AcctCcyPeriodMaintLog.ts', "validateDateField", errorLocation);
    }
  }

  updateData(){
    //Variable for errorLocation
    let errorLocation = 0;
    try {
    this.requestParams = [];
    this.menuAccessId = ExternalInterface.call('eval', 'menuAccessId');
    errorLocation = 10;
    if (this.menuAccessId) {
      if (this.menuAccessId !== "") {
        this.menuAccessId = Number(this.menuAccessId);
      }
    }
    errorLocation = 20;
    this.inputData.cbStart = this.startOfComms.bind(this);
    this.inputData.cbStop = this.endOfComms.bind(this);
    this.inputData.cbResult = (event) => {
      this.inputDataResult(event);
    };
    errorLocation = 30;
    this.inputData.cbFault = this.inputDataFault.bind(this);
    this.inputData.encodeURL = false;
    this.actionPath = "accountPeriod.do?";
    this.actionMethod = 'method=displayLog';
    errorLocation = 40;
    this.requestParams['menuAccessId'] = this.menuAccessId;
    this.requestParams['fromDate'] = this.fromDateField.text;
    this.requestParams['toDate'] =  this.toDateField.text;
    this.requestParams['reference'] =  this.reference;
    this.requestParams['action'] =  this.action;
    this.inputData.url = this.baseURL + this.actionPath + this.actionMethod;
    errorLocation = 50;
    this.inputData.send(this.requestParams);
    } catch (error) {
      // log the error in ERROR LOG
      this.logger.error('method [updateData] - error: ', error, 'errorLocation: ', errorLocation);
      SwtUtil.logError(error, SwtUtil.PREDICT_MODULE_ID, 'AcctCcyPeriodMaintLog.ts', "updateData", errorLocation);
    }
  }

  startOfComms(): void {
    this.loadingImage.setVisible(true);
  }

  /**
   * Part of a callback function to all for control of the loading swf from the HTTPComms Object
   */
  endOfComms(): void {
    this.loadingImage.setVisible(false);
  }


	/**                                                                                                                  
	 * If a fault occurs with the connection with the server then display the lost connection label                      
	 * @param event:FaultEvent                                                                                           
	 **/
  private inputDataFault(event): void {
    this._invalidComms = event.fault.faultString + "\n" + event.fault.faultCode + "\n" + event.fault.faultDetail;
    this.swtAlert.show("fault " + this._invalidComms);
  }

  viewHandler() {
    let params = [];
    //Variable for errorLocation
    let errorLocation = 0;
    try {
      params.push({
        reference: this.selectedtRow.reference.content,
        date: this.selectedtRow.date.content,
        time: this.selectedtRow.time.content,
        userId: this.selectedtRow.user.content,
        ipAddress: this.selectedtRow.ipAddress.content,
        action: this.selectedtRow.action.content
      });
      errorLocation = 10;
      ExternalInterface.call('openViewLogScreen', 'displayViewLogScreen', JSON.stringify(params));
    } catch (error) {
      // log the error in ERROR LOG
      this.logger.error('method [viewHandler] - error: ', error, 'errorLocation: ', errorLocation);
      SwtUtil.logError(error, SwtUtil.PREDICT_MODULE_ID, 'AcctCcyPeriodMaintLog.ts', "viewHandler", errorLocation);
    }
  }

  popupClosed(): void {
    window.close();
  } 

}

//Define lazy loading routes
const routes: Routes = [
  { path: '', component: AcctCcyPeriodMaintLog}
];
export const routing: ModuleWithProviders = RouterModule.forChild(routes);
//Define an NgModule that will be loaded by app module
@NgModule({
  imports: [routing, SwtToolBoxModule],
  declarations: [AcctCcyPeriodMaintLog],
  entryComponents: []
})
export class AcctCcyPeriodMaintLogModule {
 }