import { Component, OnInit, ViewEncapsulation, ModuleWithProviders, NgModule, ElementRef, ViewChild } from '@angular/core';
import {
  SwtToolBoxModule, HTTPComms, JSONReader, Timer, ExternalInterface,
  SwtCommonGrid, CommonService, SwtAlert, SwtModule, SwtCanvas, SwtDateField,
  ExportEvent, SwtComboBox, SwtTabNavigator, CommonUtil, focusManager, SwtDataExport,
  SwtTextInput, SwtLabel, SwtButton, SwtLoadingImage, StringUtils, Tab, SwtUtil, Alert, SwtPopUpManager,
  TitleWindow, ScreenVersion, ContextMenuItem, JSONViewer, GridRow, SwtTotalCommonGrid
} from 'swt-tool-box';
import { RouterModule, Routes } from '@angular/router';
import moment from "moment";
import { OptionsSetting } from '../OptionsSetting/OptionsSetting';
declare var require: any;
const $ = require('jquery');
@Component({
  selector: 'app-book-group-monitor',
  templateUrl: './BookGroupMonitor.html',
  styleUrls: ['./BookGroupMonitor.css'],
  encapsulation: ViewEncapsulation.None
})
export class BookGroupMonitor extends SwtModule implements OnInit {
  @ViewChild('totalsContainer') totalsContainer: SwtCanvas;
  @ViewChild('displaycontainer') displaycontainer: SwtCanvas;


  @ViewChild('refreshButton') refreshButton: SwtButton;
  @ViewChild('optionsButton') optionsButton: SwtButton;
  @ViewChild('closeButton') closeButton: SwtButton;

  @ViewChild('tabs') tabs: SwtTabNavigator;


  @ViewChild("displayContainerToday") displayContainerToday: Tab;
  @ViewChild("displayContainerTodayPlus") displayContainerTodayPlus: Tab;
  @ViewChild("displayContainerTodayPlusPlus") displayContainerTodayPlusPlus: Tab;
  @ViewChild("displayContainerTodayPlusThree") displayContainerTodayPlusThree: Tab;
  @ViewChild("displayContainerTodayPlusFour") displayContainerTodayPlusFour: Tab;
  @ViewChild("displayContainerTodayPlusFive") displayContainerTodayPlusFive: Tab;
  @ViewChild("displayContainerTodayPlusSix") displayContainerTodayPlusSix: Tab;
  @ViewChild("displayContainerSelected") displayContainerSelected: Tab;




  @ViewChild('ccyCombo') ccyCombo: SwtComboBox;
  @ViewChild('entityCombo') entityCombo: SwtComboBox;
  @ViewChild('locationCombo') locationCombo: SwtComboBox;
  @ViewChild('monitorCombo') monitorCombo: SwtComboBox;
  @ViewChild('groupCombo') groupCombo: SwtComboBox;

  @ViewChild('groupcomboLabel') groupcomboLabel: SwtLabel;
  @ViewChild('lostConnectionText') lostConnectionText: SwtLabel;
  @ViewChild('selectedEntity') selectedEntity: SwtLabel;
  @ViewChild('selectedCcy') selectedCcy: SwtLabel;
  @ViewChild('selectedGroup') selectedGroup: SwtLabel;
  @ViewChild('locationType') locationType: SwtLabel;
  @ViewChild('dataBuildingText') dataBuildingText: SwtLabel;
  
  @ViewChild('dataExport') dataExport: SwtDataExport;

  @ViewChild('lastRefTime') lastRefTime: SwtLabel;

  @ViewChild('loadingImage') loadingImage: SwtLoadingImage;


  @ViewChild('sodText') sodText: SwtTextInput;

  /***********SwtDateField***********/
  @ViewChild('startDate') startDate: SwtDateField;

  @ViewChild('selectGroup') selectGroup: GridRow;



  private jsonReader: JSONReader = new JSONReader();
  private lastRecievedJSON;
  private prevRecievedJSON;
  private win:TitleWindow;
  /**
   * Communication Objects
   **/
  private inputData = new HTTPComms(this.commonService);
  private updateRefreshRate = new HTTPComms(this.commonService);
  private baseURL = SwtUtil.getBaseURL();
  private actionMethod: string = "";
  private actionPath: string = "";
  private requestParams = [];
  private autoRefresh: Timer;
  private refreshRate: number = 10;
  private backGroundTimer: Timer;
  private comboOpen: Boolean = false;
  private comboChange: Boolean = false;
  private monitorcomboChange: Boolean = false;
  private groupLogic:GroupLogic;
  private invalidComms: string = "";
   private versionDate: string = "04/11/2019";
  private versionNumber: string = "1.1.0026";
  private cGrid: SwtCommonGrid;
  private totalsGrid: SwtTotalCommonGrid;


  private lastmonitortype: string;
  private monitortype: string;
  private groupCode: string;
  private metagroupId: string;
  private gridData;
  private baseURLMETHOD: string = "";
  private screenId: string = "";
  private menuItemId: string = "";
  private monitorTypeOnLoad: string = "";
  private callFromParent: string = "";
  private entityId1: string = "";
  private currencyId1: string = "";
  private datefr: string = "";
  private dateSelected: Date = new Date();
  private bolDateChange: Boolean = false;
  private bolTabChange: Boolean = false;


  private systemDate: string;
  private dateCompare: string = "";

  // private commonLogic:CommonLogic=new CommonLogic;
  private entitycomboChange: Boolean = false;

  // Instantiates HTTPComms
  private updateFontSize = new HTTPComms(this.commonService);
  // Initializes fontValue
  private fontValue: string = "";
  // Initializes fontLabel
  private fontLabel: string = "";
  // Initializes fontRequest
  private fontRequest: string = "";
  // Declares selectedFont
  private selectedFont: number;
  // Initializes tempFontSize
  private tempFontSize: string = "";
  private existingEntityId: string = "";
  private showXMLPopup : any;


  private swtAlert: SwtAlert;
  private dateFormat;

  private arrayOftabs = [];
  public screenVersion  = new ScreenVersion(this.commonService) ;
  private currencyPattern: string = "";
  constructor(private commonService: CommonService, private element: ElementRef) {
    super(element, commonService);
    this.swtAlert = new SwtAlert(commonService);
  }

  ngOnInit() {

    this.refreshButton.toolTip = ExternalInterface.call('getBundle', 'tip', 'button-refresh', 'Refresh window');
    this.refreshButton.label = ExternalInterface.call('getBundle', 'text', 'button-refresh', 'Refresh');

    this.optionsButton.toolTip = ExternalInterface.call('getBundle', 'tip', 'button-options', 'Refresh window');
    this.optionsButton.label = ExternalInterface.call('getBundle', 'text', 'button-options', 'Refresh');

    this.closeButton.toolTip = ExternalInterface.call('getBundle', 'tip', 'button-close', 'Refresh window');
    this.closeButton.label = ExternalInterface.call('getBundle', 'text', 'button-close', 'Refresh');


    this.entityCombo.toolTip = ExternalInterface.call('getBundle', 'tip', 'entity', 'Select an entity ID');
    this.ccyCombo.toolTip = ExternalInterface.call('getBundle', 'tip', 'currency', 'Select currency code');
    this.locationCombo.toolTip = ExternalInterface.call('getBundle', 'tip', 'location', 'Select location id');
    this.monitorCombo.toolTip = ExternalInterface.call('getBundle', 'tip', 'monitor', 'Select Monitor Type');
    this.sodText.toolTip = ExternalInterface.call('getBundle', 'tip', 'balance', 'Total');
  }


  /**The functon is used when the page loads initially when we click the metagroup monitor  menu
   Note : The menu name to be changed	 */
  onLoad(): void {
    this.cGrid = <SwtCommonGrid>this.displaycontainer.addChild(SwtCommonGrid);
    this.totalsGrid = <SwtTotalCommonGrid>this.totalsContainer.addChild(SwtTotalCommonGrid);
    this.cGrid.lockedColumnCount = 1;
    this.totalsGrid.lockedColumnCount = 1;
    this.totalsGrid.selectable = false;
    this.cGrid.columnWidthChanged.subscribe((event) => {
      this.resizeGrids(event);
    });
    this.cGrid.columnOrderChanged.subscribe((event) => {
      this.resizeGrids(event);
    });
    this.cGrid.listenHorizontalScrollEvent = true;
    this.totalsGrid.fireHorizontalScrollEvent = true;
    this.refreshButton.label = "Refresh";

    /*Method to initialize the menu to be shown in the flex screen*/
    this.groupLogic = new GroupLogic;
    this.initializeMenus();
    this.dateFormat = ExternalInterface.call('eval', 'dateFormat').toLowerCase();
    this.systemDate = ExternalInterface.call('eval', 'dbDate');
    // groupLogic.testDate = systemDate;

    this.monitorTypeOnLoad = ExternalInterface.call('eval', 'monitorType');
    this.callFromParent = ""+ExternalInterface.call('eval', 'calledFromParent');
    /*It defines the format to be displayed in Date field*/
    this.startDate.formatString = this.dateFormat;

    if (this.dateFormat == "dd/mm/yyyy") {
      this.startDate.toolTip = ExternalInterface.call('getBundle', 'tip', 'dateMMDDYY', 'MM/dd/yyyy');
    }
    else {
      this.startDate.toolTip = ExternalInterface.call('getBundle', 'tip', 'date', 'Enter date');
    }


    /*Initialize the communication objects*/
    this.inputData.cbStart = this.startOfComms.bind(this);
    this.inputData.cbStop = this.endOfComms.bind(this);
    this.inputData.cbResult = (data) => {
      this.inputDataResult(data);
    };
    this.inputData.cbFault = this.inputDataFault.bind(this);
    this.inputData.encodeURL = false;
    if (this.callFromParent == "true") {

      if (this.monitorTypeOnLoad == "Group") {
        /*Define the action to send the request*/
        this.actionPath = "metagroupmonitor.do?";
        /*Define method the request to access*/
        this.actionMethod = "method=displayGroupMonitorDetails&callstatus=y&systemDate=" + this.systemDate;
      }
      else if (this.monitorTypeOnLoad == "Book") {
        /*Define the action to send the request*/
        this.actionPath = "metagroupmonitor.do?";
        /*Define method the request to access*/
        this.actionMethod = "method=displayBookMonitorDetails&callstatus=y&systemDate=" + this.systemDate;
      }
      else if (this.monitorTypeOnLoad == "Metagroup") {
        /*Define the action to send the request*/
        this.actionPath = "metagroupmonitor.do?";
        /*Define method the request to access*/
        this.actionMethod = "method=displayMetagroupMonitorDetails&callstatus=y&systemDate=" + this.systemDate;
      }
      this.entityId1 = ExternalInterface.call('eval', 'entityId1');
      this.currencyId1 = ExternalInterface.call('eval', 'currencyId1');
      this.datefr = ExternalInterface.call('eval', 'datefr');
      this.requestParams["metagroupMonitor.date"] = this.datefr;
      this.requestParams["metagroupMonitor.entityId"] = this.entityId1;
      this.requestParams["metagroupMonitor.currencyId"] = this.currencyId1;

    }
    else {
      
      if (this.monitorTypeOnLoad) {
        if (this.monitorTypeOnLoad == "Group") {
          /*Define the action to send the request*/
          this.actionPath = "metagroupmonitor.do?";
          /*Define method the request to access*/
          this.actionMethod = "method=displayGroupMonitorDetails&systemDate=" + this.systemDate;
        }
        else if (this.monitorTypeOnLoad == "Book") {
          /*Define the action to send the request*/
          this.actionPath = "metagroupmonitor.do?";
          /*Define method the request to access*/
          this.actionMethod = "method=displayBookMonitorDetails&systemDate=" + this.systemDate;
        }
        else if (this.monitorTypeOnLoad == "Metagroup") {
          /*Define the action to send the request*/
          this.actionPath = "metagroupmonitor.do?";
          /*Define method the request to access*/
          this.actionMethod = "";
        }
      }
      else {
        
        /*Define the action to send the request*/
        this.actionPath = "metagroupmonitor.do?";
        /*Define method the request to access*/
        this.actionMethod = "";
      }
    }

    this.backGroundTimer = new Timer((1000), 0);
    this.backGroundTimer.addEventListener("BackgroundTimer", this.backgroundFlagCheck.bind(this));
    this.backGroundTimer.start();
    // backGroundTimer.addEventListener("BackgroundTimer", (function handle(evt: Event): void {
    //   this.updateData("no")
    // }));
    /*Set the complete URL for inputData Http*/
    // Mantis 1851 (STL_016)
    this.actionMethod = (this.actionMethod == "") ? "method=unspecified" : this.actionMethod;

    this.inputData.url = this.baseURL + this.actionPath + this.actionMethod;
    /*Make the request with requestParams if any*/
    this.inputData.send(this.requestParams);

    ExportEvent.subscribe((type) => {
      this.export(type)
    });


    this.cGrid.ITEM_CLICK.subscribe((selectedRowData) => {
      this.obtainCell(selectedRowData);
    });


  }

  backgroundFlagCheck(event) :void {
    
    var messageFlagSet :Boolean = ExternalInterface.call('eval','refreshPending');
    if (messageFlagSet) {
      // dispatchEvent(event);
      this.updateData('no');
      ExternalInterface.call('eval','refreshPending=false');
    }
  }


  resizeGrids(event) {
    try {
      this.totalsGrid.setRefreshColumnWidths(this.cGrid.gridObj.getColumns());

    } catch(e) {
      console.log("resizeGrids", e)
    }

  }





			/**This function is used to load the retrieved datas from xml using the xmlReader and process it
			 * The Column datas are retieved and rendered using GroupGrid and are located into the display
			 * containes.
			 * The totals grid is populated for Book montor and updated with column data so that the Total
			 * row will be made visible for the Book monitor
			 * */
			inputDataResult(data):void
			{
        try{
				/*Condition to check the inputData HTTPcomms is busy ie to ensure the data load*/
				if (this.inputData.isBusy())
				{
					this.inputData.cbStop();
				}
				else
				{
					/*Retrieves the XML from ResultEvent*/
					this.lastRecievedJSON = data;
					/*Pass the XML received to xmlReader*/
					this.jsonReader.setInputJSON(this.lastRecievedJSON);
					/*Disabling visibility of lostConnectionText*/
          this.lostConnectionText.visible=false;
          this.currencyPattern = (this.jsonReader.getScreenAttributes()["currencyformat"]);
          let lastRefAsString: string = this.jsonReader.getScreenAttributes()["lastRefTime"];
          lastRefAsString = this.groupLogic.convertFromUnicodeToString(lastRefAsString);
          this.lastRefTime.text = lastRefAsString;

					/*Check the requst reply status in XML*/
					if (this.jsonReader.getRequestReplyStatus())
					{
						/* Get the Currency from the XML received*/
						this.ccyCombo.setComboData(this.jsonReader.getSelects());
						//// modified because the text is always null and the equivalent of the combobox spark is selectedItem.content
						// Modified by KaisBS for mantis 2004: message not being displayed when user has no access to currencies
						// Add the trim operation to remove spaces from the selected label of the currency combo.
						if (StringUtils.trim(this.ccyCombo.selectedItem.content) == "")
						{
							//check the condition autoRefresh is not equal to null
							if (this.autoRefresh != null)
							{
								this.autoRefresh.stop();
							}
							this.comboOpen=true;
							this.swtAlert.show(ExternalInterface.call('getBundle', 'text', 'alert.currencyAccess', 'Invalid: your role does not specify access to currencies/groups for this entity'), ExternalInterface.call('getBundle', 'text', 'alert-error', 'Error'));
						}
						/* Condition to check the last received and previous xml to build grid data.*/
						if (this.lastRecievedJSON != this.prevRecievedJSON)
						{
							
							this.startDate.showToday=false;
							this.systemDate=this.jsonReader.getScreenAttributes()["sysDateFrmSession"];
							// Start Code Added by Vivekanandan A for Mantis 1991 on 19-07-2012
							this.existingEntityId=this.jsonReader.getScreenAttributes()["existingEntityId"];
							// End Code Added by Vivekanandan A for Mantis 1991 on 19-07-2012
							// Set the system date to testDate
							this.groupLogic.dateFormat=this.dateFormat;
              /*Get and set the start date from the XML received*/
							this.startDate.selectedDate=new Date(this.groupLogic.convertDate(this.jsonReader.getScreenAttributes()["datefrom"]));
              this.entityCombo.setComboData(this.jsonReader.getSelects());
							this.selectedEntity.text=this.entityCombo.selectedValue;
							this.selectedCcy.text=this.ccyCombo.selectedValue;
							this.monitorCombo.setComboData(this.jsonReader.getSelects());
              if(this.jsonReader.getSelects()['select'].find(x => x.id === 'parent-container'))
                this.lastmonitortype = this.jsonReader.getSelects()['select'].find(x => x.id === 'parent-container').label;
              this.monitorCombo.selectedLabel = this.lastmonitortype;
              // var selects:XMLList=lastRecievedXML.selects.children().(@id.search("parent-container") != -1);
							// for (var m:number=0; m < this.monitorCombo.dataProvider.length; m++)
							// {
							// 	if (this.monitorCombo.dataProvider.getItemAt(m)== selects.@label)
							// 	{
							// 		lastmonitortype=selects.@label;
							// 		monitorCombo.selectedIndex=m;
							// 		break;
							// 	}
							// }
							this.locationCombo.setComboData(this.jsonReader.getSelects());
							this.locationType.text=this.locationCombo.selectedValue;
              /*Get and set the start of day balance from XML*/
              
              this.sodText.text=this.jsonReader.getSingletons().sod?this.jsonReader.getSingletons().sod.content:"";
							if (""+this.jsonReader.getSingletons().sod.negative == "true")
							{
                
								this.sodText.setStyle("color", "red");
							}
							else
							{
								this.sodText.setStyle("color", "black");
							}
              // Set the tab label to container
              if (this.lastRecievedJSON.groupmonitor.tabs != "") {
                if(this.arrayOftabs.length == 0){
                if(this.tabs.getTabChildren().length > 0) {
                  for(let i =0; i < this.arrayOftabs.length; i++) {
                    this.tabs.removeChild(this.arrayOftabs[i] );
                  }
                }
              this.displayContainerToday = <Tab>this.tabs.addChild(Tab);
              this.displayContainerTodayPlus = <Tab>this.tabs.addChild(Tab);
              this.displayContainerTodayPlusPlus = <Tab>this.tabs.addChild(Tab);
              this.displayContainerTodayPlusThree = <Tab>this.tabs.addChild(Tab);
              this.displayContainerTodayPlusFour = <Tab>this.tabs.addChild(Tab);
              this.displayContainerTodayPlusFive = <Tab>this.tabs.addChild(Tab);
              this.displayContainerTodayPlusSix = <Tab>this.tabs.addChild(Tab);
              this.displayContainerSelected = <Tab>this.tabs.addChild(Tab);

              this.arrayOftabs = [];
              this.arrayOftabs = [this.displayContainerToday, this.displayContainerTodayPlus, this.displayContainerTodayPlusPlus, this.displayContainerTodayPlusThree,
                this.displayContainerTodayPlusFour, this.displayContainerTodayPlusFive, this.displayContainerTodayPlusSix, this.displayContainerSelected];
              }
              for (var i = 0; i < this.lastRecievedJSON.groupmonitor.tabs.predictdate.length; i++) {
                this.arrayOftabs[i].label = this.lastRecievedJSON.groupmonitor.tabs.predictdate[i].dateLabel;
                this.arrayOftabs[i].businessday = this.lastRecievedJSON.groupmonitor.tabs.predictdate[i].businessday;
                if(this.arrayOftabs[i].businessday ==false){
                    this.arrayOftabs[i].setTabHeaderStyle("color","darkgray");
                }else
                  this.arrayOftabs[i].setTabHeaderStyle("color","black");
              }
              //this.arrayOftabs[7].label = ExternalInterface.call('getBundle', 'text', 'tab4', 'Selected');
              this.arrayOftabs[7].label = ExternalInterface.call('getBundle', 'text', 'tab4', 'Selected');
            }
            
            
            
            
              var index:number=0;
              var tab:Tab;
              
              
              
							// displayContainerSelected.label=ExternalInterface.call('getBundle', 'text', 'tab4', 'Selected');
              
              this.monitortype=this.monitorCombo.selectedItem.content;
							this.dateCompare=""+(this.jsonReader.getScreenAttributes()["dateComparing"]);
							// Checks and removes totalsGrid if monitor type is Metagroup
							if (this.monitortype == "Metagroup")
							{
              this.totalsContainer.visible = false;
              this.totalsContainer.includeInLayout = false;
                this.totalsGrid.gridData = null;
								this.menuItemId=ExternalInterface.call('eval', 'metagroupMenuItemId');
								this.baseURLMETHOD=this.baseURL + "metagroupmonitor.do?menuItemId=" + this.menuItemId + "&";
								this.selectGroup.visible=false;
							}
							// Checks and removes totalsGrid if monitor type is Group
							if (this.monitortype == "Group")
							{
                this.totalsContainer.visible = false;
                this.totalsContainer.includeInLayout = false;
                this.totalsGrid.gridData = null;
								this.menuItemId=ExternalInterface.call('eval', 'groupMenuItemId');
								this.baseURLMETHOD=this.baseURL + "metagroupmonitor.do?menuItemId=" + this.menuItemId + "&";
								this.selectGroup.visible=true;
								this.groupCombo.dataLabel="metagroup";
								this.groupCombo.toolTip=ExternalInterface.call('getBundle', 'tip', 'metagroup', 'Select metagroup id');
								this.groupcomboLabel.text=ExternalInterface.call('getBundle', 'text', 'metagroup', 'Metagroup');
								this.groupCombo.setComboData(this.jsonReader.getSelects());
								this.selectedGroup.text=this.groupCombo.selectedValue;
              }
              
							// Checks and enables totalsGrid if monitor type is Book
							if (this.monitortype == "Book")
							{
								this.menuItemId=ExternalInterface.call('eval', 'bookMenuItemId');
								this.baseURLMETHOD=this.baseURL + "metagroupmonitor.do?menuItemId=" + this.menuItemId + "&";
								this.selectGroup.visible=true;
								this.groupCombo.dataLabel="groupCode";
								this.groupCombo.toolTip=ExternalInterface.call('getBundle', 'tip', 'group', 'Select group id');
								this.groupcomboLabel.text=ExternalInterface.call('getBundle', 'text', 'group', 'Group');
								this.groupCombo.setComboData(this.jsonReader.getSelects());
								this.selectedGroup.text=this.groupCombo.selectedItem.content
								
							}
							/*Condition to check xmlreader databuilding*/
							if (!this.jsonReader.isDataBuilding())
							{
								/*Disabling visibility of dataBuildingText*/
								this.dataBuildingText.visible=false;
								// Checks the row size: if it's less than 1. Export icons will be disabled, else, enabled
								if (this.jsonReader.getRowSize() < 1)
								{
									this.dataExport.enabled=false;
								}
								else
								{
                  this.dataExport.enabled = true;
                }
                this.cGrid.currencyFormat = this.currencyPattern;
                this.cGrid.hideHorizontalScrollBar = true;
								if (this.monitorcomboChange == true)
								{
                  this.cGrid.uniqueColumn = "group";
                 // this.cGrid.componentID = "13";
									// if(this.cGrid == null) {
                  /*Pass column data  to GroupGrid with baseURL and monitor type */
                    this.cGrid.CustomGrid(data.groupmonitor.grid.metadata);
									// }
                this.handleDate();
                this.cGrid.gridData = this.jsonReader.getGridData();
                this.cGrid.setRowSize = this.jsonReader.getRowSize();
    
                /*This method is used by GroupGrid to set the column width and colum order*/
                //FIXME:CHECK IF WORKING
									this.cGrid.entityID=this.entityCombo.selectedItem.content;
									
									if (this.monitortype == "Book")
									{
                    
                    /*Pass column data  to totlas grid to set the totals grid columns */
                    this.totalsContainer.visible = true;
                    this.totalsGrid.CustomGrid(data.groupmonitor.grid.metadata);
                    /*Update the totalsgrid with totals data and column data */
                    this.totalsGrid.gridData = this.jsonReader.getTotalsData();
                    //FIXME:CHECK IF WORKING FIX WHEN Column changed place!
										//totalsGrid.updateGrid(this.jsonReader.getTotalsData(), data.groupmonitor.grid.metadata);
										// for (var i:int=1; i < cGrid.columnCount; i++)
										// {
										// 	if (!cGrid.columns[i].visible)
										// 	{
										// 		totalsGrid.columns[i - 0].visible=false;
										// 	}
                    // }
                    //FIXME:
										// totalsGrid.horizontalScrollPosition=totalsGridHBar;
										this.cGrid.entityID=this.entityCombo.selectedItem.content;
									}
								}
								else
								{
									if (!this.cGrid.metaData)
									{
                    
                    this.cGrid.CustomGrid(data.groupmonitor.grid.metadata);
                    //FIXME: NEED TO CHECK monitortype !! and base URL
										// this.cGrid=new GroupGrid(data.groupmonitor.grid.metadata, baseURLMETHOD, monitortype);
                    // displayContainerToday.addElement(cGrid);
                    
                    this.handleDate();
										
										if (this.monitortype == "Book")
										{
                      /*Pass column data  to totlas grid to set the totals grid columns */
                      this.totalsContainer.visible = true;
                      this.totalsGrid.CustomGrid(data.groupmonitor.grid.metadata);
                      this.totalsGrid.gridData = this.jsonReader.getTotalsData();
                      //FIXME:CHECK IF WORKING "1" what it is !!
											//totalsGrid=new SwtTotalsGrid(data.groupmonitor.grid.metadata, 1);
											/*Add totalsGrid to totals container*/
											// totalsContainer.addElement(totalsGrid);
											// totalsGrid.setListeners();
											// var totalsGridHBar:int=totalsGrid.horizontalScrollPosition;
                      /*Update the totalsgrid with totals data and column data */
                      FIXME:
											// totalsGrid.updateGrid(this.jsonReader.getTotalsData(), data.groupmonitor.grid.metadata);
											// for (var i:int=1; i < cGrid.columnCount; i++)
											// {
											// 	if (!cGrid.columns[i].visible)
											// 	{
											// 		totalsGrid.columns[i - 0].visible=false;
											// 	}
											// }
											// totalsGrid.horizontalScrollPosition=totalsGridHBar;
                      this.cGrid.entityID=this.entityCombo.selectedItem.content;

										}
										// Gets the font size from xml
                    this.tempFontSize=this.jsonReader.getScreenAttributes()["currfontsize"];

                    
										// Sets the grid style as normal as per the font size
										if (this.tempFontSize == "Normal")
										{
											this.cGrid.styleName="dataGridNormal";
											this.cGrid.rowHeight=18;
											if (this.monitortype == "Book")
											{

												this.totalsGrid.styleName="dataGridNormal";
												this.totalsGrid.rowHeight=18;
											}
										}
											// Sets the grid style as small as per the font size
										else if (this.tempFontSize == "Small")
										{
											this.cGrid.styleName="dataGridSmall";
											this.cGrid.rowHeight=15;
											if (this.monitortype == "Book")
											{
												this.totalsGrid.styleName="dataGridSmall";
												this.totalsGrid.rowHeight=15;
											}
										}
										//FIXME:CHECK ::
										/*This method is used to set Filters and Sorting to the cGrid Column headers*/
										// cGrid.setListeners();
										/*Set event listeners to TabNavigator */
										// tabs.addEventListener("change", tabChange);
									}
									else
									{
										if (this.dateCompare == "false")
										{
                      //FIXME:Check if needed
                      this.handleDate();
										}
										else if (this.dateCompare == "true")
										{
											var daysInMilliseconds=1000 * 60 * 60 * 24;
                      var todayDate:Date;
                      var today = this.systemDate;
                      todayDate = new Date(CommonUtil.parseDate(today, this.dateFormat));
                      this.handleDate();
										}
									}
									/*Set event listeners to handle cell click*/
									// this.cGrid.addEventListener(ListEvent.ITEM_CLICK, obtainCell);
									this.cGrid.gridData=this.jsonReader.getGridData();
									this.cGrid.setRowSize=this.jsonReader.getRowSize();
                  /*This method is used by GroupGrid to set the column width and colum order*/
                  //
									this.cGrid.entityID=this.entityCombo.selectedItem.content;
									
									if (this.monitortype == "Book")
									{
										/*Pass column data  to totlas grid to set the totals grid columns */
										// totalsGrid=new SwtTotalsGrid(data.groupmonitor.grid.metadata, 1);
										// /*Add totalsGrid to totals container*/
										// totalsContainer.addElement(totalsGrid);
                    // totalsGrid.setListeners();
                    this.totalsContainer.visible = true;
                    
                    this.totalsGrid.CustomGrid(data.groupmonitor.grid.metadata);
                    this.totalsGrid.gridData = this.jsonReader.getTotalsData();
                    //FIXME:
										// var totalsGridHBar:int=totalsGrid.horizontalScrollPosition;
										// /*Update the totalsgrid with totals data and column data */
										// totalsGrid.updateGrid(this.jsonReader.getTotalsData(), data.groupmonitor.grid.metadata);
										// for (var i:int=1; i < cGrid.columnCount; i++)
										// {
										// 	if (!cGrid.columns[i].visible)
										// 	{
										// 		totalsGrid.columns[i - 0].visible=false;
										// 	}
										// }
										// totalsGrid.horizontalScrollPosition=totalsGridHBar;
										this.cGrid.entityID=this.entityCombo.selectedItem.content;
									}
								}
								// Gets the font size from the xmlReader which has taken from request
                this.tempFontSize=this.jsonReader.getScreenAttributes()["currfontsize"];
								// Checks the font size and changes the font style of data grid and totals grid accordingly
								if (this.tempFontSize == "Normal")
								{
									this.selectedFont=0;
									this.cGrid.styleName="dataGridNormal";
									this.cGrid.rowHeight=18;
									if (this.monitortype == "Book")
									{
										this.totalsGrid.styleName="dataGridNormal";
										this.totalsGrid.rowHeight=18;
									}
								}
									// Checks the font size and changes the font style of data grid and totals grid accordingly
								else if (this.tempFontSize == "Small")
								{
									this.selectedFont=1;
									this.cGrid.styleName="dataGridSmall";
									this.cGrid.rowHeight=15;
									if (this.monitortype == "Book")
									{
										this.totalsGrid.styleName="dataGridSmall";
										this.totalsGrid.rowHeight=15;
									}
								}
							}
							else
							{
								this.dataBuildingText.visible=true;
							}
							if (this.autoRefresh == null)
							{
								
								
								
								// Gets the refreshRate from xmlReader
								this.refreshRate=parseInt(this.jsonReader.getRefreshRate());
								this.autoRefresh=new Timer((this.refreshRate * 1000), 0);
								/*Set event listener to dataRefresh*/
								this.autoRefresh.addEventListener("timer", this.dataRefresh.bind(this));
							}
							if (this.monitorcomboChange == true)
							{
                this.	monitorcomboChange=false;
								this.prevRecievedJSON= new Object();
							}
							else
							{
								this.prevRecievedJSON=new Object();
								this.prevRecievedJSON=this.lastRecievedJSON;
								
							}
						}
						
						
					}
					else
					{
						/*Condition check for the initial load of flex with data error*/
						if (this.lastmonitortype != null)
						{
							this.monitorCombo.selectedItem=this.lastmonitortype;
							this.updateData("no");
						}
					}
					if (this.autoRefresh != null)
					{
						if (!this.autoRefresh.running)
						{
							this.autoRefresh.start();
						}
						if (this.cGrid != null && this.totalsGrid != null)
						{
							// Gets the font size from the xmlReader which has taken from request
							this.tempFontSize=this.jsonReader.getScreenAttributes()["currfontsize"];
							// Checks the font size and changes the font style of data grid and totals grid accordingly
							if (this.tempFontSize == "Normal")
							{
								this.selectedFont=0;
								this.cGrid.styleName="dataGridNormal";
								this.cGrid.rowHeight=18;
								if (this.monitortype == "Book")
								{
									this.totalsGrid.styleName="dataGridNormal";
									this.totalsGrid.rowHeight=18;
								}
							}
								// Checks the font size and changes the font style of data grid and totals grid accordingly
							else if (this.tempFontSize == "Small")
							{
								this.selectedFont=1;
								this.cGrid.styleName="dataGridSmall";
								this.cGrid.rowHeight=15;
								if (this.monitortype == "Book")
								{
									this.totalsGrid.styleName="dataGridSmall";
									this.totalsGrid.rowHeight=15;
								}
							}
						}
            this.cGrid.colWidthURL ( this.baseURLMETHOD);
            this.cGrid.colOrderURL ( this.baseURLMETHOD);
            this.cGrid.saveWidths = true;
            this.cGrid.saveColumnOrder = true;
					}
        }

      }catch(e){
      console.log("TCL: BookGroupMonitor -> e", e)
        
      }
			}










    handleDate() :void {
      try{
        
          let exist = false;
          let sysDateAsDate:Date = this.groupLogic.convertDate(this.systemDate);
          let tempDate:Date = new Date(sysDateAsDate);
          let selectedDate = this.groupLogic.convertDate(this.startDate.text);
          if(selectedDate) {
            for (let index = 0; index < 7; index++) {
              
              tempDate.setDate(sysDateAsDate.getDate() + index);
                if(selectedDate.getTime() == tempDate.getTime()){
                  exist = true;
                  this.tabs.selectedIndex = index;
                  break;
                }
                tempDate = new Date(sysDateAsDate);
            }
          }
          if(!exist){
            this.tabs.selectedIndex = 7;
          }
        }
        catch(e){

        }


      }




  /**This function is called my the links clicked in the Datagrid Total column
   * It recieves the event triggered,
   * retrieves the key row from the event,
   * checks  the monitor type,
   * creates the complete URL,
   * use inputData Http to send the request and
   * loads the data
   * */
  obtainCell(e): void {

try{
    if (e != null) {

      let fieldName = e.target.field;
      let isClickable = e.target.data.slickgrid_rowcontent[fieldName].clickable;
        
      /*Retrieves the column Array of the cell clicked*/
      // var columnArray=e.target.columns;
      /*Retrieves the key column from which the column data is to be fetched*/
      var metagroupIdColumn:string=e.target.field;
      
      /*Retrieve the column value used to forward the request to GROUP MONITOR or BOOK MONITOR*/
      this.metagroupId=e.target.data["group"];
      
      /*Retrieves the selected date*/
      var d1:Date=this.startDate.selectedDate;
      /*Convert the date into sting*/

      var sD1: string = this.dateFormatConversion(this.dateFormat.toUpperCase(), d1);

      /*Retrieve currency details from Currency combo*/
      var currGrp:string=this.ccyCombo.selectedItem.content;
      /*Retrieve entity details from entity combo*/
      var entityId:string=this.entityCombo.selectedItem.content;
      /*Retrieve location details from location combo*/
      var location:string=this.locationCombo.selectedItem.content;
      /*Retrieve monitor type from monitor combo*/
      this.monitortype=this.monitorCombo.selectedItem.content;
      /*Retrieves the column where the click event has been fired*/
      // var clickedColumn:string=(columnArray[e.columnIndex] as DataGridColumn).dataField;
      // e.stopPropagation();
      /*Condition check for Group,Metagroup ang Book monitors added action path, methods and request params
      to navigate to Book monitor,Group monitor and movement display summary respectively*/
      if (this.monitortype == "Group")
      {
        this.monitorcomboChange=true;
        this.actionPath="metagroupmonitor.do?";
        this.actionMethod="method=displayBookMonitorDetails";
        this.requestParams["metagroupMonitor.selectedTabIndex"]=(this.tabs.selectedIndex + 1);
        this.requestParams["metagroupMonitor.date"]=sD1;
        this.requestParams["metagroupMonitor.locationId"]=location;
        this.requestParams["metagroupMonitor.entityId"]=entityId;
        this.requestParams["metagroupMonitor.currencyId"]=currGrp;
        this.requestParams["metagroupMonitor.groupCode"]=this.metagroupId;
        this.monitortype="Book";
      }
      else if (this.monitortype == "Metagroup")
      {
        this.monitorcomboChange=true;
        this.actionPath="metagroupmonitor.do?";
        this.actionMethod="method=displayGroupMonitorDetails";
        this.requestParams["metagroupMonitor.selectedTabIndex"]=(this.tabs.selectedIndex + 1);
        this.requestParams["metagroupMonitor.date"]=sD1;
        this.requestParams["metagroupMonitor.locationId"]=location;
        this.requestParams["metagroupMonitor.entityId"]=entityId;
        this.requestParams["metagroupMonitor.currencyId"]=currGrp;
        this.requestParams["metagroupMonitor.metagroupId"]=this.metagroupId;
        this.monitortype="Group";
      }
      else if (this.monitortype == "Book")
      {
        var selectetabIndex:string=(this.tabs.selectedIndex + 1).toString();
        this.groupCode=this.groupCombo.selectedItem.content;
        /*Conditon to check whether the clicked column is clickable*/
        if (isClickable)
        {
          /*function call to display the Movement summary screen*/
          this.groupLogic.getMovementDetails(entityId, currGrp, selectetabIndex, sD1, this.metagroupId, this.baseURL);
        }
      }
      /*Conditon to check whether the clicked column is clickable*/
      if (isClickable)
      {
        /*If clickable forward the request to the specified URL*/
        this.inputData.url=this.baseURL + this.actionPath + this.actionMethod;
        this.inputData.send(this.requestParams);
      }
    }
  }catch(e){
  console.log("TCL: BookGroupMonitor -> e", e)
    
  }
  }


  /**The function initializes the menus in the right click event on the metagroup monitor screen.
   * The links are redirected to their respective pages.
   */
  initializeMenus(): void {
    this.screenVersion.loadScreenVersion(this, 'Book Group', this.versionNumber, this.versionDate);
    // this.screenVersion.pushJsonData(this, null);

    var addMenuItem:ContextMenuItem=null;
    addMenuItem=new ContextMenuItem(SwtUtil.getPredictMessage("screen.showJSON", null));
    // add the listener to addMenuItem
    addMenuItem.MenuItemSelect = this.showXMLSelect.bind(this);
    this.screenVersion.svContextMenu.customItems.push(addMenuItem);
    this.contextMenu=this.screenVersion.svContextMenu;
  }

  /** This function is used to display the XML for the monitor selected in the monitor combo
   */
  showXMLSelect(event): void {

    this.showXMLPopup = SwtPopUpManager.createPopUp(this,
      JSONViewer,
      {
        jsonData: this.lastRecievedJSON,
    });
    this.showXMLPopup.width = "700";
    this.showXMLPopup.title = "Last Received JSON";
    this.showXMLPopup.height = "500";
    this.showXMLPopup.enableResize = false;
    this.showXMLPopup.showControls = true;
    this.showXMLPopup.isModal = true;
    this.showXMLPopup.display();

    // showXML=ShowXML(PopUpManager.createPopUp(this, ShowXML, true));
    // showXML.x=(this.width / 2) - (showXML.width / 2);
    // showXML.y=(this.height / 50) - (showXML.height / 50);
    // requestParams=new Array();
    // /*Retrieves the selected date*/
    // var d1:Date=startDate.selectedDate;
    // /*Convert the date into sting*/
    // var sD1:string=d1 ? (d1.getDate() < 10 ? "0" + d1.getDate() : d1.getDate()) + "/" + (d1.getMonth() + 1 < 10 ? "0" + (d1.getMonth() + 1) : d1.getMonth() + 1) + "/" + d1.getFullYear() : null;
    // /*Retrieve currency details from Currency combo*/
    // var currGrp:string=ccyCombo.selectedItem.content;
    // /*Retrieve entity details from entity combo*/
    // var entityId:string=entityCombo.selectedItem.content;
    // /*Retrieve location details from location combo*/
    // var location:string=null;
    // if (locationCombo.selectedIndex > -1)
    // {
    // 	location=locationCombo.selectedItem.content;
    // }
    // /*Retrieve monitor type from monitor combo*/
    // monitortype=monitorCombo.selectedItem.content;

    // if (monitortype == "Group")
    // {
    // 	showXML.baseURL=baseURL + "metagroupmonitor.do?method=displayGroupMonitorDetails&selectedTabIndex=" + requestParams["selectedTabIndex"] + "&metagroupMonitor.date=" + requestParams["metagroupMonitor.date"] + "&metagroupMonitor.locationId=" + requestParams["metagroupMonitor.locationId"] + "&metagroupMonitor.entityId=" + requestParams["metagroupMonitor.entityId"] + "&metagroupMonitor.currencyCode=" + requestParams["metagroupMonitor.currencyCode"] + "&metagroupMonitor.metagroupId=" + requestParams["metagroupMonitor.metagroupId"] + "&metagroupMonitor.groupCode=" + requestParams["metagroupMonitor.groupCode"];
    // }
    // if (monitortype == "Metagroup")
    // {
    // 	showXML.baseURL=baseURL + "metagroupmonitor.do?&selectedTabIndex=" + requestParams["selectedTabIndex"] + "&metagroupMonitor.date=" + requestParams["metagroupMonitor.date"] + "&metagroupMonitor.locationId=" + requestParams["metagroupMonitor.locationId"] + "&metagroupMonitor.entityId=" + requestParams["metagroupMonitor.entityId"] + "&metagroupMonitor.currencyCode=" + requestParams["metagroupMonitor.currencyCode"];
    // }
    // if (monitortype == "Book")
    // {
    // 	groupCode=groupCombo.selectedItem.content;
    // 	showXML.baseURL=baseURL + "metagroupmonitor.do?method=displayBookMonitorDetails&selectedTabIndex=" + requestParams["selectedTabIndex"] + "&metagroupMonitor.date=" + requestParams["metagroupMonitor.date"] + "&metagroupMonitor.locationId=" + requestParams["metagroupMonitor.locationId"] + "&metagroupMonitor.entityId=" + requestParams["metagroupMonitor.entityId"] + "&metagroupMonitor.currencyCode=" + requestParams["metagroupMonitor.currencyCode"] + "&metagroupMonitor.groupCode=" + requestParams["metagroupMonitor.groupCode"];
    // }
    // /*This displays the lastt received xml*/

    // showXML.setXML(lastRecievedXML);
  }

  /**
   * This function is called by auto refresh rate to autorefresh the page
   **/
  dataRefresh(event): void {
    /*Check on the comboOpen flag, do not want to update if there is a combobox open as this would cause it to close*/
    if (!this.comboOpen) {
      this.updateData("yes");
    }
    // stop the timer
    this.autoRefresh.stop();
  }



  /**
   * This function servs when the rate button is clicked it opens the popup withthe
   * autorefresh rate set.
   **/
  optionsHandler(): void {
    /*Get and store the auto refresh rate for the monitors*/
    this.refreshRate = parseInt(this.jsonReader.getScreenAttributes()["refresh"]);
    
    this.win =  SwtPopUpManager.createPopUp(this, OptionsSetting, {
      title: "Options", //SwtUtil.getPredictMessage('cutOff.title.addRule', null), // childTitle,
      fontSizeValue: this.selectedFont,
      refreshText: this.refreshRate.toString()
    });
    this.win.isModal = true;
    this.win.enableResize = false;
    this.win.width = '360';
    this.win.height = '190';
    this.win.showControls = true;
    this.win.id = "optionsWindow";
    this.win.onClose.subscribe((res) => {
      this.submitFontSize(res);
        setTimeout(() => {
      this.submitRate(res);
      }, 1000)
    });

    this.win.display();
  }


  submitFontSize(event): void {
    
    // Gets the RadioButton value from RefreshPopUp
    this.fontValue=event.fontSize.value;
    /*Sets the screen Id based on the monitor type*/
    this.monitortype=this.monitorCombo.selectedItem.content;
    if (this.monitortype == "Metagroup")
    {
    	this.screenId=ExternalInterface.call('eval', 'metagroupScreenId');
    }
    if (this.monitortype == "Group")
    {
    	this.screenId=ExternalInterface.call('eval', 'groupScreenId');
    }
    if (this.monitortype == "Book")
    {
    	this.screenId=ExternalInterface.call('eval', 'bookScreenId');
    }

    // // Checks the font value and assigning the datagrid style accordingly
    if (this.fontValue == "N")
    {
      this.selectedFont=0;
      
    	this.fontLabel =event.fontSize.label;
    	this.cGrid.styleName="dataGridNormal";
    	this.cGrid.rowHeight=18;
    	// Sets the style for totalsGrid if the monitor type is Book
    	if (this.monitortype == "Book")
    	{
    		this.totalsGrid.styleName="dataGridNormal";
    		this.totalsGrid.rowHeight=18;
    	}
    	this.fontRequest=ExternalInterface.call("getUpdateFontSize", this.fontLabel, this.screenId);
    }
    else if (this.fontValue == "S")
    {
    	this.selectedFont=1;
    	this.fontLabel= event.fontSize.label;
    	this.cGrid.styleName="dataGridSmall";
    	this.cGrid.rowHeight=15;
    	// Sets the style for totalsGrid if the monitor type is Book
    	if (this.monitortype == "Book")
    	{
    		this.totalsGrid.styleName="dataGridSmall";
    		this.totalsGrid.rowHeight=15;
    	}
    	this.fontRequest=ExternalInterface.call("getUpdateFontSize", this.fontLabel, this.screenId);
    }
    // Updates the HttpComms with the given request if its available
    if (this.fontRequest != null && this.fontRequest != "")
    {
      var originalURL ;
      // Sets the original URL
			originalURL=this.updateFontSize.url;
			// Sets the request in the URL
			this.updateFontSize.url = this.fontRequest;
			// Calls the send function
			this.updateFontSize.send();
			// Sets the URL
			this.updateFontSize.url=originalURL;
      
      
    	// this.updateFontSize.getThis(this.baseURL + this.fontRequest);
    }
  }



  /**
   *Fault event handler for inputData HTTPComms*/
  inputDataFault(event): void {
    this.lostConnectionText.visible = true;
    let message = SwtUtil.getPredictMessage('label.genericException', null);
    this.swtAlert.error(message);
    // this.invalidComms = event.fault.faultString + "\n" + event.fault.faultCode + "\n" + event.fault.faultDetail;
    // If autoRefresh is not equal to null, start the timer.
    if (this.autoRefresh != null) {
      if (!this.autoRefresh.running) {
        this.autoRefresh.start();
      }
    }
  }

  /**
   *Mouse event to handle check the availability of connection */
  connError(event): void {
    this.swtAlert.show("" + this.invalidComms, ExternalInterface.call('getBundle', 'text', 'alert-error', 'Error'));
  }

  /**
   *  This function is used to set the refresh rate for the monitors. Here the refresh rates for
   * all the three monitors are handled using the screenId.
   */
  submitRate(event): void {
    
    if (isNaN(parseInt(event.refreshRate))) {
      this.swtAlert.show(ExternalInterface.call('getBundle', 'text', 'label-notANumber', 'Not a number'));
    }
    else {
      var selectedRateBelowMinimum: Boolean = false;
      this.refreshRate = parseInt(event.refreshRate);
      /*Condition to check the refresh rate is less than 5 if so set the refresh rate to 5 */
      if (this.refreshRate < 5) {
        this.refreshRate = 5;
        selectedRateBelowMinimum = true;
      }
      if (this.autoRefresh)
        this.autoRefresh.delay(this.refreshRate * 1000);
      this.monitortype = this.monitorCombo.selectedItem.content;
      /*Check monitor type and call the function to store the refresh rate for each screen based on ScreenId*/
      if (this.monitortype == "Metagroup") {
        this.screenId = ExternalInterface.call('eval', 'metagroupScreenId');
      }
      if (this.monitortype == "Group") {
        this.screenId = ExternalInterface.call('eval', 'groupScreenId');
      }
      if (this.monitortype == "Book") {
        this.screenId = ExternalInterface.call('eval', 'bookScreenId');
      }
      var request: string = ExternalInterface.call("getUpdateRefreshRequest", this.refreshRate, this.screenId);
      /*If request is not null set the update the refresh rate*/
      if (request != null && request != "")
      {
        // this.updateRefreshRate.getThis(this.baseURL + request);

        var originalURL ;
        // Sets the original URL
        originalURL=this.updateFontSize.url;
        // Sets the request in the URL
        this.updateRefreshRate.url = request;
        // Calls the send function
        this.updateRefreshRate.send();
        // Sets the URL
        this.updateRefreshRate.url=originalURL;
        

      }
      // PopUpManager.removePopUp(refreshRatePopup);
      this.updateData("no");
      /*Condition check for refresh rate below minimum and give alert.*/
      if (selectedRateBelowMinimum) {
        this.swtAlert.show(ExternalInterface.call('getBundle', 'text', 'label-refreshRateSelected', 'Refresh rate selected was below minimum.\nSet to 5 seconds'), ExternalInterface.call('getBundle', 'text', 'alert-warning', 'Warning'));
      }
    }
  }

  /**
   * Part of a callback function to all for control of the loading swf from the HTTPComms Object
   **/
  startOfComms(): void {
    this.loadingImage.setVisible(true);
    this.disableInterface();
    this.startDate.enabled = false;
  }

  /**
   * Part of a callback function to all for control of the loading swf from the HTTPComms Object
   **/
  endOfComms(): void {
    this.loadingImage.setVisible(false);
    this.enableInterface();
    this.startDate.enabled = true;
  }


  /**
   * Disable interface, turn off certain UI elements when a request is made to the server
   **/
  disableInterface(): void {
    this.refreshButton.enabled = false;
    this.refreshButton.buttonMode = false;
  }

  /**
   * Enable interface, turn on certain UI elements when a request is made to the server
   **/
  enableInterface(): void {
    this.refreshButton.enabled = true;
    this.refreshButton.buttonMode = true;
  }

  /**
   * Function called for combo change event.
   **/
  changeCombo(e): void {
    // e.stopImmediatePropagation();
    this.comboChange = true;
    this.entitycomboChange = false;
    this.updateData("no");
  }

  /**
   * Function called for combo change event.
   **/
  entityChangeCombo(e): void {
    // e.stopImmediatePropagation();
    this.comboChange = true;
    this.entitycomboChange = true;
    this.updateData("no");
  }

  /**
   * When a combobox is open then any requests to the server need to be cancelled
   **/
  openedCombo(event): void {
    this.comboOpen = true;
    // if (this.inputData.isBusy()) {
    //   let objectType = event.currentTarget && event.currentTarget.constructor?event.currentTarget.constructor.name:"";
    //   this.enableInterface();
    //   this.inputData.cancel();
    //   if (objectType == "SwtComboBox")
    //   {
    //   	event.currentTarget.interruptComms=true;
    //   }
    //   else if (objectType == "SwtDateField")
    //   {
    //   	event.currentTarget.interruptComms=true;
    //   }
    // }
    
  }

  /**
   * When the combobox has closed, we need to know if the closure was caused by the user clicking away from the box
   **/
  closedCombo(event): void {
    this.comboOpen = false;
    // let objectType = event.currentTargetObject && event.currentTargetObject.constructor?event.currentTargetObject.constructor.name:"";
    // let focusId = event.currentTargetObject ? event.currentTargetObject.id :"";
    
    // if(objectType) {
    //   if(!event.currentTargetObject.interruptComms)
    //     event.currentTargetObject.interruptComms=false;
    // }
    //FIXME:
    // if ((event.triggerEvent != null) && (event.triggerEvent.type == "mouseDownOutside"))
    // {
    // 	if (event.currentTarget is SwtComboBox)
    // 	{
    // 		if ((event.currentTarget as SwtComboBox).interruptComms)
    // 		{
    // 			(event.currentTarget as SwtComboBox).interruptComms=false;
    // 			this.updateData("no");
    // 		}
    // 	}

    // }
  }

  /**
   * When the datefield has closed, we need to know if the closure was caused by the user clicking away from the box
   **/
  closedDateField(event): void {
    this.comboOpen = false;
    this.startDate.interruptComms=false;
  }


  /**
   * Update the data, this is called whenever a fresh of the data is required.
   * This could be called from either a change in a combobox selection of from the timer
   * This methods checks for the monitor type and loads the data accordingly.
   **/
  updateData(autorefresh: string): void {
    this.bolTabChange = false;
  
    var d1: Date ;

    this.requestParams = [];
    this.actionMethod = "";
    
    /*Retrieve date,Entity, Currency, Location and Monitor datas from combo */
    if (this.bolDateChange == true) {
      d1 = new Date(this.dateSelected);
      this.bolDateChange = false;
    }
    else {
      d1= new Date(this.startDate.selectedDate);
    }
    this.bolTabChange = false;
    if (d1 != null && this.ccyCombo.selectedItem.content && this.entityCombo.selectedItem.content && this.locationCombo.selectedItem.content && this.monitorCombo.selectedItem.content) {
      var sD1: string = this.dateFormatConversion(this.dateFormat.toUpperCase(), d1);
      var currGrp: string = this.ccyCombo.selectedItem.content;
      var entityId: string = this.entityCombo.selectedItem.content;
      var location: string = this.locationCombo.selectedItem.content;
      this.monitortype = this.monitorCombo.selectedItem.content;
      if (this.monitortype == "Metagroup") {
        this.actionPath = "metagroupmonitor.do?";
        this.actionMethod = "";
        this.actionMethod = "method=displayMetagroupMonitorDetails";
        this.requestParams["metagroupMonitor.selectedTabIndex"] = this.tabs.selectedIndex + 1;
        this.requestParams["metagroupMonitor.date"] = sD1;
        this.requestParams["metagroupMonitor.locationId"] = location;
        this.requestParams["metagroupMonitor.entityId"] = entityId;
        if (!this.entitycomboChange)
          this.requestParams["metagroupMonitor.currencyId"] = currGrp;
        this.requestParams["systemDate"] = this.systemDate;
        this.requestParams["autoRefresh"] = autorefresh;
      }
      if (this.monitortype == "Group") {
        if(this.groupCombo.selectedItem.content == null) {
          return;
        }
        this.metagroupId = this.groupCombo.selectedItem.content;
        this.actionPath = "metagroupmonitor.do?";
        this.actionMethod = "method=displayGroupMonitorDetails";
        this.requestParams["metagroupMonitor.selectedTabIndex"] = this.tabs.selectedIndex + 1;
        this.requestParams["metagroupMonitor.date"] = sD1;
        this.requestParams["metagroupMonitor.locationId"] = location;
        this.requestParams["metagroupMonitor.entityId"] = entityId;
        if (!this.entitycomboChange)
          this.requestParams["metagroupMonitor.currencyId"] = currGrp;
        this.requestParams["metagroupMonitor.metagroupId"] = this.metagroupId;
        this.requestParams["systemDate"] = this.systemDate;
        this.requestParams["autoRefresh"] = autorefresh;
      }
      if (this.monitortype == "Book") {
        if(this.groupCombo.selectedItem.content == null) {
          return;
        }
        this.groupCode = this.groupCombo.selectedItem.content;
        this.actionPath = "metagroupmonitor.do?";
        this.actionMethod = "";
        this.actionMethod = "method=displayBookMonitorDetails";
        this.requestParams["metagroupMonitor.selectedTabIndex"] = this.tabs.selectedIndex + 1;
        this.requestParams["metagroupMonitor.date"] = sD1;
        this.requestParams["metagroupMonitor.locationId"] = location;
        this.requestParams["metagroupMonitor.entityId"] = entityId;
        if (!this.entitycomboChange)
          this.requestParams["metagroupMonitor.currencyId"] = currGrp;
        this.requestParams["metagroupMonitor.groupCode"] = this.groupCode;
        this.requestParams["systemDate"] = this.systemDate;
        this.requestParams["autoRefresh"] = autorefresh;
      }

      // Start Code Added by Vivekanandan A for Mantis 1991 on 19-07-2012
      this.requestParams["existingEntityId"] = this.existingEntityId;
      // End Code Added by Vivekanandan A for Mantis 1991 on 19-07-2012

      // Mantis 1851 (STL_016)
      this.actionMethod = (this.actionMethod == "") ? "method=unspecified" : this.actionMethod;

      this.inputData.url = this.baseURL + this.actionPath + this.actionMethod;
      this.inputData.send(this.requestParams);
    }

  }




  /**The below method is used to format the date type being set in the application.
   * The date d1 is the selected date in the screen by the user and formatString is
   * the format type which is to be converted.If the date format type is being set as
   * 'MM/dd/yyyy'in the application then the selected date in the screen will be
   * formated for this type.
   * */
  dateFormatConversion(formatString: string, d1: Date): string {

    var sD1: string = "";
    if (formatString == "DD/MM/YYYY") {
      sD1 = d1 ? (d1.getDate() < 10 ? "0" + d1.getDate() : d1.getDate()) + "/" + (d1.getMonth() + 1 < 10 ? "0" + (d1.getMonth() + 1) : d1.getMonth() + 1) + "/" + d1.getFullYear() : null;
    }
    else {
      sD1 = d1 ? ((d1.getMonth() + 1) < 10 ? "0" + (d1.getMonth() + 1) : (d1.getMonth() + 1)) + "/" + (d1.getDate() < 10 ? "0" + (d1.getDate()) : d1.getDate()) + "/" + d1.getFullYear() : null;
    }

    return sD1;
  }

  /**
   * Function called to close the window when close button is clicked
   **/
  closeHandler(): void {
    ExternalInterface.call("close");
  }

  /**
   * Function called for mouse over event in grid
   **/
  mouseOverHandler(event): void {
    event.preventDefault();
    event.currentTarget.setStyle("styleName", "myItemHover");
  }

  /**
   * Function called for mouse out event in grid
   **/
  mouseOutHandler(event): void {
    event.preventDefault();
    event.currentTarget.setStyle("styleName", "myItem");
  }




  /**
   * Funtion called for tab navigation when a tab is clicked.
   * The totals grid is removed for Group and Metagroup monitors.
   * The tab index is set based  on the date seleted.
   * */
  tabChange(event): void {
    var boltab: Boolean = false;
    this.monitorcomboChange = true;

    if (this.callFromParent == "true") {
      this.callFromParent="false";
    }
    var todayDate: Date;

    todayDate = this.groupLogic.convertDate(this.systemDate, this.dateFormat);
    // Added by Vivekanandan A for Mantis 1627: Tab click displays wrong date and data when day light saving time changed  
    todayDate.setHours(12, 0, 0);
    
    if (this.tabs.selectedIndex < 7) {
      this.startDate.selectedDate = new Date(todayDate.setDate(todayDate.getDate() + this.tabs.selectedIndex));
    }
    
    if (this.tabs.selectedIndex == 7) {
      this.prevRecievedJSON = null;
      if (this.startDate.selectedDate != null)
        this.startDate.selectedDate = new Date(this.startDate.selectedDate.getTime());
      else
        this.bolTabChange = true;
      if (this.bolTabChange == false) {
        this.updateData("no");
        boltab = true;
      }
      else {
        this.bolTabChange = false;
        boltab = true;
      }
    }

    if (boltab == false) {
      if (this.bolTabChange == false) {
        this.updateData("no");

      }
      else {
        this.bolTabChange = false;

      }
    }
  }
  // Method modified by Vivekanandan A for Mantis 1627: Tab click displays wrong date and data when day light saving time changed
  /**Function called when the start date is changed.
   * It also checks condition for date range more than 30 days.
   * */
  timeUpdate(event/*: CalendarLayoutChangeEvent*/): void {

    this.bolDateChange = true;
    var bolupdate: boolean = false;
    try {
      this.monitorcomboChange = true;
      var daysInMilliseconds: number = 1000 * 60 * 60 * 24;
      var today: string = "";
      var todayDate: Date;
      var selectedDate: Date;

      today = this.systemDate;
      todayDate = new Date(CommonUtil.parseDate(today, this.dateFormat));
      todayDate.setHours(12, 0, 0);
      
      selectedDate = this.groupLogic.convertDate(this.startDate.text);
      selectedDate.setHours(12, 0, 0);
      var d1: Date = this.groupLogic.convertDate(this.startDate.text);

      var pattern: RegExp = null;

      this.dateSelected = d1;
      var sD1: string = "";

      if (this.dateFormat.toUpperCase() == "DD/MM/YYYY") {
        pattern = /^(0[1-9]|[12][0-9]|3[01]|[1-9])\/(0[1-9]|1[012]|[1-9])\/(\d{4}|\d{2})$/
      }
      else {
        pattern = /^(0[1-9]|1[012]|[1-9])\/(0[1-9]|[12][0-9]|3[01]|[1-9])\/(\d{4}|\d{2})$/
      }

      if (this.dateFormat.toUpperCase() == "DD/MM/YYYY") {
        sD1 = d1 ? (d1.getDate() < 10 ? "0" + d1.getDate() : d1.getDate()) + "/" + (d1.getMonth() + 1 < 10 ? "0" + (d1.getMonth() + 1) : d1.getMonth() + 1) + "/" + d1.getFullYear() : null;
      }
      else {
        sD1 = d1 ? ((d1.getMonth() + 1) < 10 ? "0" + (d1.getMonth() + 1) : (d1.getMonth() + 1)) + "/" + (d1.getDate() < 10 ? "0" + (d1.getDate()) : d1.getDate()) + "/" + d1.getFullYear() : null;
      }

      
      if (sD1 != null) {
        
        if (""+pattern.test(this.startDate.text).toString() == "true") {
          /*Condition checks the date selected and passes to javascript,
          checks the date condition not more than 30 days */
          if (!this.groupLogic.CheckDate(sD1)) {
            if (this.jsonReader.getScreenAttributes()["datefrom"]) {
              todayDate = this.groupLogic.convertDate(this.jsonReader.getScreenAttributes()["datefrom"].toString(), this.dateFormat);
            }


            /*If more than 30 days revert the previous date*/
            this.startDate.selectedDate = new Date(todayDate.getTime());
            var d2: Date = this.startDate.selectedDate;
            this.dateSelected = d2;
            this.tabs.selectedIndex = this.tabs.selectedIndex;
            this.updateData("no");
            bolupdate = false;
            this.bolTabChange = false;

          }
          else {
            this.handleDate();
            this.updateData("no");
          }
        }
      }
    } catch (error) {
      // Protect code against null values (selected date may be null when entering non valid date)
    }
    //Modified By Imed B
  }



  /**Function called when monitor combo is changed. It forwards the rewuest to specified URL based on monitorTYPE
   * */
  monitorComboChangeHandle(e): void {

  try {
  this.requestParams = [];
  this.comboChange = true;
  this.monitorcomboChange = true;
  /*//FIXME:
  //this.displayContainerToday.removeAllElements();
  var d1: Date = this.startDate.selectedDate;
  //below line is used to get the date from the text box as it is.*/
  var sD1: string = this.startDate.text;
    var fontSize = this.jsonReader.getScreenAttributes()["currfontsize"];
    // Checks and disables the totalsgrid
    if (this.monitortype == "Book") {
      if (this.totalsGrid != null) {
        //FIXME:need to hide not to set data null
        //this.totalsContainer.visible = false;
        this.totalsGrid.gridData = null;
      }
    }
    this.monitortype = this.monitorCombo.selectedItem.content;
    if (this.monitortype == "Metagroup") {
      this.actionPath = "metagroupmonitor.do?";
      this.actionMethod = "";
      this.actionMethod = "method=displayMetagroupMonitorDetails";
      this.requestParams["metagroupMonitor.selectedTabIndex"] = this.tabs.selectedIndex + 1;
      this.requestParams["metagroupMonitor.date"] = sD1;
      this.requestParams["metagroupMonitor.locationId"] = this.locationCombo.selectedItem.content;
      this.requestParams["metagroupMonitor.entityId"] = this.entityCombo.selectedItem.content;
      this.requestParams["metagroupMonitor.currencyId"] = this.ccyCombo.selectedItem.content;
      this.requestParams["systemDate"] = this.systemDate;

    }
    // Checks and sets the request params for calling the service method
    if (this.monitortype == "Group") {
      this.metagroupId = this.groupCombo.selectedItem.content;
      this.actionPath = "metagroupmonitor.do?";
      this.actionMethod = "method=displayGroupMonitorDetails";
      this.requestParams["metagroupMonitor.selectedTabIndex"] = this.tabs.selectedIndex + 1;
      this.requestParams["metagroupMonitor.date"] = sD1;
      this.requestParams["metagroupMonitor.locationId"] = this.locationCombo.selectedItem.content;
      this.requestParams["metagroupMonitor.entityId"] = this.entityCombo.selectedItem.content;
      this.requestParams["metagroupMonitor.currencyId"] = this.ccyCombo.selectedItem.content;
      this.requestParams["metagroupMonitor.metagroupId"] = this.metagroupId;
      this.requestParams["systemDate"] = this.systemDate;
    }
    // Checks and sets the request params for calling the service method
    if (this.monitortype == "Book") {
      this.groupCode = this.groupCombo.selectedItem.content;
      this.actionPath = "metagroupmonitor.do?";
      this.actionMethod = "";
      this.actionMethod = "method=displayBookMonitorDetails";
      this.requestParams["metagroupMonitor.selectedTabIndex"] = this.tabs.selectedIndex + 1;
      this.requestParams["metagroupMonitor.date"] = sD1;
      this.requestParams["metagroupMonitor.locationId"] = this.locationCombo.selectedItem.content;
      this.requestParams["metagroupMonitor.entityId"] = this.entityCombo.selectedItem.content;
      this.requestParams["metagroupMonitor.currencyId"] = this.ccyCombo.selectedItem.content;
      this.requestParams["metagroupMonitor.groupCode"] = this.groupCode;
      this.requestParams["systemDate"] = this.systemDate;
    }

    // Checks and sets the font style to the data grid
    if (this.monitortype == "Book" || this.monitortype == "Group" || this.monitortype == "Metagroup") {
      // Sets the grid style as normal as per the font size
      if (fontSize == "Normal") {
        this.selectedFont = 0;
        this.cGrid.styleName="dataGridNormal";
        this.cGrid.rowHeight = 18;
      }
      // Sets the grid style as small as per the font size
      else if (fontSize == "Small") {
        this.selectedFont = 1;
        this.	cGrid.styleName="dataGridSmall";
        this.cGrid.rowHeight = 15;
      }
    }
    // Start Code Added by Vivekanandan A for Mantis 1991 on 19-07-2012
    this.requestParams["existingEntityId"] = this.existingEntityId;
    // End Code Added by Vivekanandan A for Mantis 1991 on 19-07-2012
    this.inputData.url = this.baseURL + this.actionPath + this.actionMethod;
    this.inputData.send(this.requestParams);


 } catch (e) {
    console.log('errro in chnage combo of monitor type', e)
  }
  }



  /**
 * Function called when data export button is clicked.
 * Condition checked for data grid null.
 * */
  export(event): void {
  try{
      var selects = new Array();
      var filter: Object = new Object();
      var isTotalGrid: boolean = false;
      selects.push(ExternalInterface.call('getBundle', 'text', 'label-entity', 'Entity') + "=" + this.entityCombo.selectedItem.content);
      selects.push("Ccy=" + this.ccyCombo.selectedItem.content);
      selects.push(ExternalInterface.call('getBundle', 'text', 'label-location', 'Location') + "=" + this.locationCombo.selectedItem.content);
      /*Conditon to check the monitor type Metagroup it dosent have any groupcombo datas*/
      if (this.monitortype != "Metagroup") {
        selects.push(this.groupcomboLabel.text + "=" + this.groupCombo.selectedItem.content);
      }

      var d1: Date = this.startDate.selectedDate;
      var sD1: string = this.dateFormatConversion(this.dateFormat.toUpperCase(), d1);

      selects.push("Date=" + sD1);
      selects.push(ExternalInterface.call('getBundle', 'text', 'label-entity', 'Entity') + "=" + (this.sodText.text != "" ? this.sodText.text : "None"));
      if(this.lastRecievedJSON.groupmonitor.grid.totals) {
        isTotalGrid = true
      }

      this.dataExport.convertData(this.lastRecievedJSON.groupmonitor.grid.metadata.columns, this.cGrid, this.totalsGrid.gridData, selects, event, isTotalGrid);
    }
    catch(e){
    console.log("TCL: BookGroupMonitor -> e", e)
      
    }

  }

  helphandler(): void {
    ExternalInterface.call("help");
  }
  ////we don't need this function bcause in this case dropDown = null, so we add some code in the openedCombo function
  /*public function calldropdown():void
  {
  entityCombo.dropDown.height=215;
  ccyCombo.dropDown.height=215;
  monitorCombo.dropDown.height=215;
  groupCombo.dropDown.height=215;
  locationCombo.dropDown.height=215;
  }*/
  // Modified By Imed B on 28-03-2012 start
  validateStartDate(event): void {
    if (Object(focusManager.getFocus()).id == "closeButton") {
      this.closeHandler();
    }
    else {
      //Stop the autorefresh
      this.autoRefresh.stop();
      this.validateDate( this.startDate);

    }
  }
  
  /**
		 * This function validates the date field - Mantis 1262. <br>
		 * The date field is an editable one where the user can type the desired date.<br>
		 * The date is taken as an argument and it is validate against certain rules.<br>
		 *
		 * Author: Marshal.<br>
		 * Date: 19-10-2010<br>
		 */
  //FIXME:   Fix method!
  validateDate(dateField) {

      if (dateField.text) {
        let date =  moment(dateField.text, this.dateFormat.toUpperCase() , true);
        if(!date.isValid()){
          this.swtAlert.error(SwtUtil.getPredictMessage("alert.enterValidDate", null), null, Alert.OK, null, () => {
            //provisoirement
            this.startDate.text = this.jsonReader.getScreenAttributes()["datefrom"].toString();
            //dateField.setFocus();
          });

          //this.swtAlert.warning('Date must be in the format ' + this.dateFormat.toUpperCase(), null, Alert.OK , null, this.closeAlert.bind(this));
        }
      } else  {
        this.swtAlert.error(SwtUtil.getPredictMessage("alert.enterValidDate", null), null, Alert.OK, null, () => {
          //provisoirement
          this.startDate.text = this.jsonReader.getScreenAttributes()["datefrom"].toString();
          //dateField.setFocus();
        });
      }
  
  }

  closeAlert(event): any {
    this.startDate.setFocus();
  }
  doHelp() {
      ExternalInterface.call('help');
  }


}
class GroupLogic {

  public dateFormat:string ;
 
 
  GroupLogic() {
  }
  /**This method is used to forward the Total link clicked on Book monitor to the javascript method getMovementDetails.
   * */
   
  getMovementDetails ( entityId :string, currGrp :string, selectetabIndex :string,  sDate:string, metagroupId:string, baseURL:string ) :void {
    ExternalInterface.call("getMovementDetails", entityId, currGrp, selectetabIndex, sDate, metagroupId,baseURL);
  }
  /**This method is used to forward the date selected to onDateKeyPress function of  javascript
   * to validate the date.
   * */		
  CheckDate (sD1 :string) :Boolean {
    return ExternalInterface.call("onDateKeyPress", sD1);
  }

  convertDate(dateAsString:string, dateFormat?:string):Date
  {
    if(dateFormat){
      // return CommonUtil.parseDate(dateAsString, dateFormat)
      return moment(dateAsString, dateFormat.toUpperCase()).toDate();
    }
    else {
     return moment(dateAsString, this.dateFormat.toUpperCase()).toDate();
      // return CommonUtil.parseDate(dateAsString, this.dateFormat)
    }
  }

  
  convertFromUnicodeToString(strTmp) {

    if(strTmp){
      strTmp = unescape(strTmp);
    }
    return strTmp;
  }
  doHelp() {
    ExternalInterface.call('help');
  }

}



//Define lazy loading routes
const routes: Routes = [
  { path: '', component: BookGroupMonitor }
];
export const routing: ModuleWithProviders = RouterModule.forChild(routes);
//Define an NgModule that will be loaded by app module
@NgModule({
  imports: [routing, SwtToolBoxModule],
  declarations: [BookGroupMonitor],
  entryComponents: []
})
export class BookGroupMonitorModule { }
