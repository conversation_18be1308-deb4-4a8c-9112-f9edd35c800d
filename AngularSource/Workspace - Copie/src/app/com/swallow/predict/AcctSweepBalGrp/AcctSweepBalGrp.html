<SwtModule (creationComplete)='onLoad()' width="100%" height="100%">
  <VBox width='100%' height='100%' paddingLeft="5" paddingRight="5" paddingTop="5">
    <GridRow width="100%" height="100%" paddingBottom="10">
      <SwtCanvas #acctSweepBalGrpGridContainer id="acctSweepBalGrpGridContainer" styleName="canvasWithGreyBorder" width="100%" height="100%"
        border="false"></SwtCanvas>
    </GridRow>

    <SwtCanvas width="100%" height="35">
      <HBox width="100%">
        <HBox paddingLeft="5" width="90%">
          <SwtButton [buttonMode]="true" id="addButton" #addButton (click)="addHandler()">
          </SwtButton>
          <SwtButton [buttonMode]="true" id="deleteButton" #deleteButton (click)="deleteHandler()" enabled="false" >
          </SwtButton>
        </HBox>
        <HBox width="10%" horizontalAlign="right" paddingLeft="5">
          <SwtButton [buttonMode]="true" id="closeButton" #closeButton (click)="closeHandler()">
          </SwtButton>
          <SwtLoadingImage #loadingImage></SwtLoadingImage>
        </HBox>
    
      </HBox>
    </SwtCanvas>

  </VBox>
</SwtModule>
