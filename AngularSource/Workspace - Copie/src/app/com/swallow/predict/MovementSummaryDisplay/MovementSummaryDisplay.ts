
import { Component, ElementRef, ModuleWithProviders, NgModule, ViewChild } from '@angular/core';
import { RouterModule, Routes } from "@angular/router";
import { Observable } from 'rxjs';
import 'rxjs/add/observable/fromEvent';
import { Alert, CommonService, ContextMenuItem, DataExportMultiPage, Encryptor, EnhancedAlertingTooltip, ExportEvent, ExternalInterface, HBox, HTTPComms, JSONReader, JSONViewer, ScreenVersion, StringUtils, SwtAlert, SwtButton, SwtCanvas, SwtCheckBox, SwtComboBox, SwtCommonGrid, SwtCommonGridPagination, SwtHelpButton, SwtLabel, SwtLoadingImage, SwtModule, SwtPopUpManager, SwtTextInput, SwtToolBoxModule, SwtUtil } from "swt-tool-box";
import { AlertingRenderer } from '../EnhancedAlerting/Render/AlertingRenderer';
import { MsdFontSetting } from './MsdFontSetting/MsdFontSetting';
import { SaveFilterPopUp } from "./SaveFilterPopUp/SaveFilterPopUp";
import { BooleanParser } from '../SwtUtills/BooleanParser';

declare var instanceElement: any;
declare var require: any;
const $ = require('jquery');
var prettyData = require('pretty-data');

@Component({
  selector: 'app-movement-summary-display',
  templateUrl: './MovementSummaryDisplay.html',
  styleUrls: ['./MovementSummaryDisplay.css']
})
export class MovementSummaryDisplay extends SwtModule {

  @ViewChild('filterContainer') filterContainer: HBox;
  @ViewChild('filterArea') filterArea: HBox;
  @ViewChild('buttonBox') buttonBox: HBox;
  @ViewChild('entityCombo') entityCombo: SwtComboBox;
  @ViewChild('selectedEntity') selectedEntity: SwtLabel;
  @ViewChild('paginationData') paginationData: HBox;
  @ViewChild('numStepper') numStepper: SwtCommonGridPagination;
  @ViewChild('currencyThreshold') currencyThreshold: SwtCheckBox;
  @ViewChild('filterComboMSD') filterComboMSD: SwtComboBox;
  @ViewChild('saveFilterImage') saveFilterImage: SwtButton;
  @ViewChild('deleteFilterImage') deleteFilterImage: SwtButton;
  @ViewChild('lastRefTime') lastRefTime: SwtLabel;
  @ViewChild('exportContainer') exportContainer: DataExportMultiPage;
  @ViewChild('helpIcon') helpIcon: SwtHelpButton;
  @ViewChild('loadingImage') loadingImage: SwtLoadingImage;

  @ViewChild('headerContainer') headerContainer: SwtCanvas;
  @ViewChild('dataGridContainer') dataGridContainer: SwtCanvas;
  @ViewChild('dataGridContainer2') dataGridContainer2: SwtCanvas;
  @ViewChild('totalsPanel') totalsPanel: SwtCanvas;
  @ViewChild('totalSelectedValue') totalSelectedValue: SwtLabel;
  @ViewChild('totalInPageValue') totalInPageValue: SwtLabel;
  @ViewChild('totalOverPagesValue') totalOverPagesValue: SwtLabel;
  @ViewChild('lastRefTimeLabel') lastRefTimeLabel: SwtLabel;
  @ViewChild('diffText') diffText: SwtLabel;
//@ViewChild('spacerForGrid') spacerForGrid: Spacer;
  @ViewChild('imgShowHideButtonBar') imgShowHideButtonBar: SwtButton;
  /**
   * Data Objects
   **/
  public jsonReader: JSONReader = new JSONReader();
  public lastRecievedJSON;
  public prevRecievedJSON;
  public initReceivedJSON;
  /**
   * Communication Objects
   **/
  public inputData = new HTTPComms(this.commonService);
  private widthData = new HTTPComms(this.commonService);
  private ordertData = new HTTPComms(this.commonService);
  public alertingData = new HTTPComms(this.commonService);
  public msdColsData = new HTTPComms(this.commonService);

  public cancelExport = new HTTPComms(this.commonService);
  public doExport = new HTTPComms(this.commonService);
  private lockMovements = new HTTPComms(this.commonService);
  public filterConfData = new HTTPComms(this.commonService);
  private updateRefreshRate = new HTTPComms(this.commonService);
  public baseURL: string = SwtUtil.getBaseURL();
  private actionMethod: string = "";
  private actionPath: string = "";
  private requestParams = [];
  //private  backGroundTimer:BackgroundTimer=new BackgroundTimer();
  //private  msdLogic:MSDLogic;
  private comboOpen: boolean = false;
  private comboChange: boolean = false;
  private obtainCellStatus: boolean = false;
  // private  showXML:ShowXML;
  //private  refreshRatePopup:RefreshPopUp;

  private versionNumber: string = "1.1.037";

  private cGrid: SwtCommonGrid;
  public bottomGrid: SwtCommonGrid;
  private testDate: string = "";
  private lastmonitortype: string;
  private metaBaseURL: string = "";
  private groupBaseURL: string = "";
  private bookBaseURL: string = "";
  private method: string = "";
  private pageUrl: string = "";
  private monitortype: string;
  private locationLabel: string;
  private metagroupLabel: string;
  private groupLabel: string;
  private monitorLabel: string;
  private groupCode: string;
  private metagroupId: string;
  private gridData: any;
  // private  result: any;
  private screenId: string = "";
  private menuItemId: string = "";
  private totalsGrid: SwtCommonGrid;
  private monitorTypeOnLoad: string = "";
  private callFromParent: string = "";
  private entityId1: string = "";
  private currencyId1: string = "";
  private datefr: string = "";
  private refreshButton: SwtButton;
  private optionButton: SwtButton;
  private filterButton: SwtButton;
  //private AddColsButton: SwtButton;
  private noteButton: SwtButton;
  private movementButton: SwtButton;
  private messageButton: SwtButton;
  private matchButton: SwtButton;
  private confirmButton: SwtButton;
  private suspendButton: SwtButton;
  private removeButton: SwtButton;
  private reconButton: SwtButton;
  private closeButton: SwtButton;
  private methodName: string = "";
  private access: string = "";
  private initialinputscreen: string = "";
  private initialscreen: string = "";
  private differenceText: SwtTextInput;
  private confirmFlag: boolean = false;
  private matchFlag: boolean = false;
  private suspendFlag: boolean = false;
  // private  numStepper:NumericStepper=new NumericStepper();
  private reconFlag: boolean = false;
  private selectedMovements = [];
  private deselectedMovements = [];
  public columnNamesArr = [];
  public win: any;
  private faultEvntFlag: boolean = false;
  private currentUser: string = ExternalInterface.call('eval', 'currentUser');
  private cGridSelMvmt: string = null;
  //Code Modified for Mantis 1598 by sudhakar on 1-12-2011: Movement Summary Display' screen returns 'Connection Error
  //get the menu access id
  private menuAccessId: string = ExternalInterface.call('eval', 'menuAccessId');

  // Instantiates the HTTPComms
  private updateFontSize: HTTPComms = new HTTPComms(this.commonService);
  // Declares FontSetting object
  //private  fontPopUp:FontSetting;
  // Declares the fontValue
  private fontValue: string = "";
  // Declares fontLabel
  private fontLabel: string = "";
  // Declares fontRequest
  private fontRequest: string = "";
  // Declares selectedFont
  private selectedFont: number;
  // Initializes currencyFontSize
  private currentFontSize: string = "";

  private actionMethodName: string = "";

  // Declaring the flag that sets the status for Collapsing the Footer(Button Bar)
  private buttonBarHideFlag: boolean = true;
  private invalidComms: string;


  // private  collapseColumn:Class;

  public isCalculationFinished: boolean = true;
  public noneLabel: string = SwtUtil.getPredictMessage('msd.noneFilter', null);
  public adhocLabel: string = SwtUtil.getPredictMessage('msd.adhocFilter', null);
  public currentFilterConf: string = "";
  public dateBehaviour: string = "";
  private lastFilterAction: string = null;
  public lastSelectedIndex: number;
  private lastUsedFilter: string = null;
  private fromDeleteFilter: boolean = false;
  private dateFormat: string = "";
  private swtAlert: SwtAlert;
  private screenVersion = new ScreenVersion(this.commonService);
  private showJSONPopup: any;
  private  screenName: string=ExternalInterface.call('getBundle', 'text', 'label-movementSummaryDisplay', 'Movement Summary Display');
  private selectedList: string = "";
  private selectedMvmts = [];
  private count = 1;
  tooltipMvtId = null;
  tooltipFacilityId = null;
  private positionX:number;
  private positionY:number;
  private hostId;
  public entityId;
  private currencyId;
  private selectedNodeId = null;
  private treeLevelValue = null;
  tooltipOtherParams = [];
  public profilesList = [];
  private columnsNewWidths : string ="";
  private columnsNewOrders : string ="";
  private columnDefinitionsTempArray = [];
  private currentProfile;
  private savedProfileId;
  private useAddColsCheck;
  private profileAddCols =[];
  public openFlag=false;
  private formatIsoTime : string = "yyyy-mm-dd hh24:mi:ss";
  public addColumnsGridData= [];
  public msdDisplayColumnsData= [];
  constructor(private commonService: CommonService, private element: ElementRef) {
    super(element, commonService);
    this.swtAlert = new SwtAlert(commonService);
    window['Main'] = this;
  }

  ngOnInit() {
    instanceElement = this;
    this.cGrid = <SwtCommonGrid>this.dataGridContainer.addChild(SwtCommonGrid);
    this.cGrid.id = 'msdGrid';
    this.cGrid.allowMultipleSelection = true;
    this.cGrid.paginationComponent = this.numStepper;
    this.cGrid.onPaginationChanged = this.numpager.bind(this);
    this.cGrid.lockedColumnCount = 2;
    //this.cGrid.uniqueColumn = "movement";
    this.exportContainer.enabled=false;
    this.cGrid.clientSideFilter = false;
    this.cGrid.clientSideSort = false;
  }

  public static ngOnDestroy(): any {
    instanceElement = null;
    window['Main'] = null;
  }


  onLoad() {
    //Ajouter ça ai autre classe entity ccy....
    this.msdDisplayColumnsData= ExternalInterface.call('eval', 'msdDisplayColsList')?JSON.parse(ExternalInterface.call('eval', 'msdDisplayColsList')):[];
    // Initialize the http communication  for ilm conf.
    this.filterConfData.cbResult = (event) => {
      this.saveResult(event);
    }
    this.filterConfData.cbFault = this.saveFault.bind(this);
    this.filterConfData.encodeURL = false;
    this.initializeMenus();
    this.method = ExternalInterface.call('eval', 'method');
    /*Get and stores the dateformat */
    this.dateFormat = ExternalInterface.call('eval', 'dateFormat');
    this.cGrid.onRowClick = (event) => {
      if(this.isPageChanged && (this.bottomGrid != undefined && this.bottomGrid.dataProvider.length !=0)) {
        this.isPageChanged = false;
      } else {
        setTimeout(()=> {
          this.cellLogic(event)
        },0)
      }

    };
     
    Observable.fromEvent(document.body, 'click').subscribe(e => {
      this.positionX = e["clientX"];
      this.positionY = e["clientY"];
    });

    this.cGrid.ITEM_CLICK.subscribe((selectedRowData) => {
      setTimeout(() => {

      this.itemClickFunction(selectedRowData);
    }, 0);

  });

  this.cGrid.columnWidthChanged.subscribe((event) => {
    this.columnWidthChange(event);
  });

  this.cGrid.columnOrderChanged.subscribe((event) => {
      this.columnOrderChange(event);  
  });

    this.testDate = ExternalInterface.call('eval', 'testDate');
    this.monitorTypeOnLoad = ExternalInterface.call('eval', 'monitorType');
    this.callFromParent = ExternalInterface.call('eval', 'calledFromParent');
    //this.addEventListener("MenuClick", hideShowColumn, true);
    /*Initialize the communication objects*/
    this.inputData.cbStart = this.startOfComms.bind(this);
    this.inputData.cbStop = this.endOfComms.bind(this);
    this.inputData.cbResult = (event) => {
      this.inputDataResult(event);
    }
    this.inputData.cbFault = this.inputDataFault.bind(this);
    this.inputData.encodeURL = false;
    // Added by Med.amine for Mantis 1988 :Make the checkBox "currencyThreshold" disable during the loading
    this.currencyThreshold.enabled = false;
    //set the timer instance for every 1 min
    /*exportTimer=new Timer(100000);
    //add the listener for export timer
    exportTimer.addEventListener(TimerEvent.TIMER, exportTimerEventHandler);*/

    this.imgShowHideButtonBar.toolTip=SwtUtil.getPredictMessage('label.showButtonBar',null);

    /* Checking the method name for Excluded outstanding */

    this.methodName = ExternalInterface.call('eval', 'methodName');
    /*Define method the request to access*/
    this.actionMethodName = ExternalInterface.call('eval', 'outstandingMethodName');
    /* Checks the method name and creating the corresponding buttons for MSD screen */
    if (this.methodName != 'search' && ExternalInterface.call('eval', 'initialscreen') == 'E') {
      /* this.differenceText.text= 'Difference:' ; //ExternalInterface.call('getBundle', 'tip', 'label-difference', 'Difference:');
       this.differenceText.height=16;*/
      /*Displays the Refresh button*/
      this.refreshButton = <SwtButton>this.buttonBox.addChild(SwtButton);
      this.refreshButton.id = "refreshButton";
      this.refreshButton.width = "70";
      this.refreshButton.toolTip = ExternalInterface.call('getBundle', 'tip', 'button-refresh', 'Refresh window');
      this.refreshButton.label = ExternalInterface.call('getBundle', 'text', 'button-refresh', 'Refresh');
      this.refreshButton.click = (data) => {
        this.keepSelected = true
        this.dataRefresh(null);
      }

      /*Displays the Add button*/
      /*this.AddColsButton = <SwtButton>this.buttonBox.addChild(SwtButton);
      this.AddColsButton.id = "addColsButton";
      //this.AddColsButton.width = "60";
      this.AddColsButton.toolTip = ExternalInterface.call('getBundle', 'tip', 'button-addCols', 'Additional Columns');
      this.AddColsButton.label =ExternalInterface.call('getBundle', 'text', 'button-addCols', 'Additional Columns');
      this.AddColsButton.click = (data) => {
          this.addColumn("<None>", "msdScreen");
      }*/


      /*Displays the Filter button*/
      this.filterButton = <SwtButton>this.buttonBox.addChild(SwtButton);
      this.filterButton.id = "filterButton";
      this.filterButton.width = "60";
      this.filterButton.toolTip = ExternalInterface.call('getBundle', 'tip', 'button-filter', 'Search movements');
      this.filterButton.label =ExternalInterface.call('getBundle', 'text', 'button-filter', 'Filter');
      this.filterButton.click = (data) => {
        this.filter();
      }

      /*Displays the Note button*/
      this.noteButton = <SwtButton>this.buttonBox.addChild(SwtButton);
      this.noteButton.id = "noteButton";
      this.noteButton.width = "60";// added By Mefteh for Mantis xxx: Issue 1054_STL_037.
      this.noteButton.toolTip = ExternalInterface.call('getBundle', 'tip', 'button-notes', 'Movement notes');
      this.noteButton.label = ExternalInterface.call('getBundle', 'text', 'button-notes', 'Notes');
      this.noteButton.enabled = false;
      this.noteButton.click = (data) => {
        this.openNotes();
      }

      /*Displays the Movement button*/
      this.movementButton = <SwtButton>this.buttonBox.addChild(SwtButton);
      this.movementButton.id = "movementButton";
      this.movementButton.width = "70";
      this.movementButton.toolTip = ExternalInterface.call('getBundle', 'tip', 'button-movement', 'Show selected movement in detail');
      this.movementButton.label = ExternalInterface.call('getBundle', 'text', 'button-movement', 'Mvmnt');
      this.movementButton.enabled = false;
      this.movementButton.click = (event) => {
        this.openMovement();
      }

      /*Displays the Message button*/
      this.messageButton = <SwtButton>this.buttonBox.addChild(SwtButton);
      this.messageButton.id = "messageButton";
      this.messageButton.width = "70";
      this.messageButton.toolTip = ExternalInterface.call('getBundle', 'tip', 'button-message', 'Messages on selected movement');
      this.messageButton.label = ExternalInterface.call('getBundle', 'text', 'button-message', 'Message');
      this.messageButton.enabled = false;
      this.messageButton.click = (event) => {
        this.openMessages();
      }

      /*Displays the Match button*/
      this.matchButton = <SwtButton>this.buttonBox.addChild(SwtButton);
      this.matchButton.id = "matchButton";
      this.matchButton.width = "60";// added By Mefteh for Mantis xxx: Issue 1054_STL_037.
      this.matchButton.toolTip = ExternalInterface.call('getBundle', 'tip', 'button-makeOfferedMatch', 'Make Offered  Match');
      this.matchButton.label = ExternalInterface.call('getBundle', 'text', 'button-match', 'Match');
      this.matchButton.click = (event) => {
        this.match();
      }
      this.matchButton.enabled = false;

      /*Displays the Confirm button*/
      this.confirmButton = <SwtButton>this.buttonBox.addChild(SwtButton);
      this.confirmButton.id = "confirmButton";
      this.confirmButton.width = "70";
      this.confirmButton.toolTip = ExternalInterface.call('getBundle', 'tip', 'button-confirm', 'Confirm');
      this.confirmButton.label = ExternalInterface.call('getBundle', 'text', 'button-confirm', 'Confirm match');
      this.confirmButton.click = (event) => {
        this.confirm();
      }
      this.confirmButton.enabled = false;

      /*Displays the Suspend button*/
      this.suspendButton = <SwtButton>this.buttonBox.addChild(SwtButton);
      this.suspendButton.id = "suspendButton";
      this.suspendButton.width = "70";
      this.suspendButton.toolTip = ExternalInterface.call('getBundle', 'tip', 'button-suspend', 'Suspend match');
      this.suspendButton.label = ExternalInterface.call('getBundle', 'text', 'button-suspend', 'Suspend');
      this.suspendButton.click = (event) => {
        this.suspend();
      }
      this.suspendButton.enabled = false;

      /*Displays the Reconcile button*/
      this.reconButton = <SwtButton>this.buttonBox.addChild(SwtButton);
      this.reconButton.id = "reconButton";
      this.reconButton.width = "70";
      this.reconButton.toolTip = ExternalInterface.call('getBundle', 'tip', 'button-recon', 'Reconcile Match');
      this.reconButton.label = ExternalInterface.call('getBundle', 'text', 'button-recon', 'Recon');
      this.reconButton.click = (event) => {
        this.reconcile();
      };
      this.reconButton.enabled = false;

      /*Displays the Remove button*/
      this.removeButton = <SwtButton>this.buttonBox.addChild(SwtButton);
      this.removeButton.id = "removeButton";
      this.removeButton.width = "70";
      this.removeButton.toolTip = ExternalInterface.call('getBundle', 'tip', 'button-remove', 'Remove');
      this.removeButton.label = ExternalInterface.call('getBundle', 'text', 'button-remove', 'Remove');
      this.removeButton.click = (event) => {
        this.remove();
      };
      this.removeButton.enabled = false;

      /*Displays the Option button*/
      this.optionButton = <SwtButton>this.buttonBox.addChild(SwtButton);
      this.optionButton.id = "optionButton";
      this.optionButton.width = "70";
      this.optionButton.toolTip = ExternalInterface.call('getBundle', 'tip', 'button-options', 'Options');
      this.optionButton.label = ExternalInterface.call('getBundle', 'text', 'button-options', 'Options');
      this.optionButton.click = (event) => {
        this.fontSettingHandler();
      };

      /*Displays the Close button*/
      this.closeButton = <SwtButton>this.buttonBox.addChild(SwtButton);
      this.closeButton.id = "closeButton";
      this.closeButton.width = "60";// added By Mefteh for Mantis xxx: Issue 1054_STL_037.
      this.closeButton.toolTip = ExternalInterface.call('getBundle', 'tip', 'button-close', 'Close');
      this.closeButton.label = ExternalInterface.call('getBundle', 'text', 'button-close', 'Close window');
      this.closeButton.click = (event) => {
        this.closeHandler();
      };

    } else {
      //this.buttonBox.addChild(this.refreshButton);
      /*Displays the Refresh button*/
      this.refreshButton = <SwtButton>this.buttonBox.addChild(SwtButton);
      this.refreshButton.width = "70"
      this.refreshButton.id = "refreshButton";
      this.refreshButton.toolTip=ExternalInterface.call('getBundle', 'tip', 'button-refresh', 'Refresh window');
      this.refreshButton.label =  ExternalInterface.call('getBundle', 'text', 'button-refresh', 'Refresh');
      this.refreshButton.click = (event) => {
        this.keepSelected = true;
        this.dataRefresh()
      };
      /*Displays the Add button*/
      /*this.AddColsButton = <SwtButton>this.buttonBox.addChild(SwtButton);
      this.AddColsButton.id = "addColsButton";
      //this.AddColsButton.width = "60";
      this.AddColsButton.toolTip = ExternalInterface.call('getBundle', 'tip', 'button-addCols', 'Additional Columns');
      this.AddColsButton.label = ExternalInterface.call('getBundle', 'text', 'button-addCols', 'Additional Columns');
      this.AddColsButton.click = (data) => {
        this.addColumn("<None>", "msdScreen");
      };*/

            
      if (this.methodName != 'search' || (ExternalInterface.call('eval', 'initialscreen') == 'E') || (ExternalInterface.call('eval', 'initialscreen') == 'bookmonitor')) {
        this.filterButton = <SwtButton>this.buttonBox.addChild(SwtButton);
        this.filterButton.id = "filterButton";
        this.filterButton.width = "60";
        this.filterButton.toolTip = ExternalInterface.call('getBundle', 'tip', 'button-filter', 'Search movements');
        this.filterButton.label = ExternalInterface.call('getBundle', 'text', 'button-filter', 'Filter');
        this.filterButton.click = (event) => {
          this.filter()
        };

      }
      /*Displays the Note button*/
      this.noteButton = <SwtButton>this.buttonBox.addChild(SwtButton);
      this.noteButton.id = "noteButton";
      this.noteButton.width = "60";
      this.noteButton.toolTip=ExternalInterface.call('getBundle', 'tip', 'button-notes', 'Movement notes');
      this.noteButton.label = ExternalInterface.call('getBundle', 'text', 'button-notes', 'Notes');
      this.noteButton.enabled = false;
      this.noteButton.click = (event) => {
        this.openNotes();
      };

      /*Displays the Movement button*/
      this.movementButton = <SwtButton>this.buttonBox.addChild(SwtButton);
      this.movementButton.id = "movementButton";
      this.movementButton.toolTip=ExternalInterface.call('getBundle', 'tip', 'button-movement', 'Show selected movement in detail');
      this.movementButton.label = ExternalInterface.call('getBundle', 'text', 'button-movement', 'Mvmnt');
      this.movementButton.enabled = false;
      this.movementButton.width = "70";
      this.movementButton.click = (event) => {
        this.openMovement();
      };
      /*Displays the Message button*/
      // this.buttonBox.addChild(this.messageButton);
      this.messageButton = <SwtButton>this.buttonBox.addChild(SwtButton);
      this.messageButton.id = "messageButton";
      this.messageButton.width = "70";
      this.messageButton.toolTip=ExternalInterface.call('getBundle', 'tip', 'button-message', 'Messages on selected movement');
      this.messageButton.label = ExternalInterface.call('getBundle', 'text', 'button-message', 'Message');
      this.messageButton.enabled = false;
      this.messageButton.click = (event) => {
        this.openMessages();
      };
      /*Displays the Option button*/
      this.optionButton = <SwtButton>this.buttonBox.addChild(SwtButton);
      this.optionButton.id = "optionButton";
      this.optionButton.width = "70";
      this.optionButton.toolTip=ExternalInterface.call('getBundle', 'tip', 'button-options', 'Change Options');
      this.optionButton.label = ExternalInterface.call('getBundle', 'text', 'button-options', 'Options');
      this.optionButton.click = (event) => {
        this.fontSettingHandler();
      };
      /*Displays the Close button*/
      this.closeButton = <SwtButton>this.buttonBox.addChild(SwtButton);
      this.closeButton.id = "closeButton";
      this.closeButton.width = "60";
      this.closeButton.toolTip=ExternalInterface.call('getBundle', 'tip', 'button-close', 'Close');
      this.closeButton.label = ExternalInterface.call('getBundle', 'text', 'button-close', 'Close window');
      this.closeButton.click = (event) => {
        this.closeHandler();
      };

    }
    /* Ends checking the method name for Excluded outstanding  */

    /*Define the action to send the request*/
    this.actionPath = "outstandingmovement.do?";
    this.actionMethod = "method=" + this.actionMethodName;
    if (this.actionMethodName == "search") {
      this.filterArea.removeAllChildren();
      //this.headerContainer.height = "40";
      this.actionMethod = this.actionMethod + "&entityId=" + ExternalInterface.call('eval', 'entityIdSearch') + "&movementType=" + ExternalInterface.call('eval', 'movementTypeSearch') + "&sign=" + ExternalInterface.call('eval', 'signSearch') + "&predictStatus=" + ExternalInterface.call('eval', 'predictStatusSearch') + "&amountover=" + ExternalInterface.call('eval', 'amountoverSearch') + "&amountunder=" + ExternalInterface.call('eval', 'amountunderSearch') + "&archiveId=" + ExternalInterface.call('eval', 'archiveIdSearch') + "&currencyCode=" + ExternalInterface.call('eval', 'currencyCode') + "&paymentChannelId=" + ExternalInterface.call('eval', 'paymentChannelIdSearch') + "&beneficiaryId=" + ExternalInterface.call('eval', 'beneficiaryIdSearch') + "&custodianId=" + ExternalInterface.call('eval', 'custodianIdSearch') + "&positionlevel=" + ExternalInterface.call('eval', 'positionlevelSearch') + "&accountId=" + ExternalInterface.call('eval', 'accountIdSearch') + "&group=" + ExternalInterface.call('eval', 'groupSearch') + "&metaGroup=" + ExternalInterface.call('eval', 'metaGroupSearch') + "&bookCode=" + ExternalInterface.call('eval', 'bookCodeSearch') + "&valueFromDateAsString=" + ExternalInterface.call('eval', 'valueFromDateAsStringSearch') + "&valueToDateAsString=" + ExternalInterface.call('eval', 'valueToDateAsStringSearch') + "&timefrom=" + ExternalInterface.call('eval', 'timefromSearch') + "&timeto=" + ExternalInterface.call('eval', 'timetoSearch') + "&reference=" + ExternalInterface.call('eval', 'referenceSearch') + "&messageId=" + ExternalInterface.call('eval', 'messageIdSearch') + "&inputDateAsString=" + ExternalInterface.call('eval', 'inputDateAsStringSearch') + "&counterPartyId=" + ExternalInterface.call('eval', 'counterPartyIdSearch') +

        "&matchStatus=" + ExternalInterface.call('eval', 'matchStatusSearch') + "&initialinputscreen=" + ExternalInterface.call('eval', 'initialinputscreenSearch') + "&accountClass=" + ExternalInterface.call('eval', 'accountClassSearch') + "&isAmountDiffer=" + ExternalInterface.call('eval', 'isAmountDifferSearch') + "&referenceFlag=" + ExternalInterface.call('eval', 'referenceFlagSearch') + "&matchingparty=" + ExternalInterface.call('eval', 'matchingparty') + "&uetr=" + ExternalInterface.call('eval', 'uetr') + "&producttype=" + ExternalInterface.call('eval', 'producttype') + "&postingDateFrom=" + ExternalInterface.call('eval', 'postingDateFrom') + "&postingDateTo=" + ExternalInterface.call('eval', 'postingDateTo') + "&selectedMovementsAmount=" + ExternalInterface.call('eval', 'selectedMovementsAmountSearch') + "&openFlag=" + ExternalInterface.call('eval', 'openFlag') + "&extBalStatus=" + ExternalInterface.call('eval', 'extBalStatusSearch');
        this.actionMethod += "&extraFilter=" + ExternalInterface.call('eval', 'extraFilter');
        // Added for mantis 1443, send the scenario id to get movements with its query
      if (ExternalInterface.call('eval', 'scenarioId') != null) {
        this.actionMethod += "&scenarioId=" + ExternalInterface.call('eval', 'scenarioId');
        this.actionMethod += "&applyCurrencyThreshold=" + ExternalInterface.call('eval', 'applyCurrencyThreshold');
        this.actionMethod += "&currGrp=" + ExternalInterface.call('eval', 'currGrp');
      }
    } else if (this.actionMethodName == 'getBookMonitorMvmnts') {
      this.actionMethod = this.actionMethod + "&entityId=" + ExternalInterface.call('eval', 'entityIdBook') + "&currencyCode=" + ExternalInterface.call('eval', 'currencyCode') + "&valueDate=" + ExternalInterface.call('eval', 'valueDate') + "&bookCode=" + ExternalInterface.call('eval', 'bookCodeBook') + "&selectedTabIndex=" + ExternalInterface.call('eval', 'selectedTabIndexBook') + "&initialinputscreen=" + ExternalInterface.call('eval', 'initialinputscreenBook');
    } else if (this.actionMethodName == 'displayOpenMovements') {
      //Code Modified for Mantis 1598 by sudhakar on 1-12-2011: Movement Summary Display' screen returns 'Connection Error
      this.actionMethod = this.actionMethod + "&initialinputscreen=" + ExternalInterface.call('eval', 'initialinputscreenOpenMovements') + "&totalFlag=" + ExternalInterface.call('eval', 'totalFlagOpenMovements') + "&posLvlId=" + ExternalInterface.call('eval', 'posLvlId') + "&currencyCode=" + ExternalInterface.call('eval', 'currencyCode') + "&date=" + ExternalInterface.call('eval', 'dateOpenMovements') + "&entityId=" + ExternalInterface.call('eval', 'entityIdOpenMovements') + "&menuAccessId=" + this.menuAccessId + "&applyCurrencyThreshold=" + ExternalInterface.call('eval', 'applyCurrencyThresholdOpenMovements');
      if (ExternalInterface.call('eval', 'workflow') != null)
        this.actionMethod += "&workflow=" + ExternalInterface.call('eval', 'workflow');
    } else if (this.actionMethodName == 'getCurrencyMonitorMvmnts') {
      this.actionMethod = this.actionMethod + "&initialinputscreen=" + ExternalInterface.call('eval', 'initialinputscreenCurrency') + "&entityId=" + ExternalInterface.call('eval', 'entityIdCurrency') + "&currencyCode=" + ExternalInterface.call('eval', 'currencyCode') + "&valueDate=" + ExternalInterface.call('eval', 'valueDate') + "&callfromcurrency=y";
    } else if (this.actionMethodName == 'getAccountMonitorMvmnts') {
      this.actionMethod = this.actionMethod + "&initialinputscreen=" + ExternalInterface.call('eval', 'initialinputscreenAccount') + "&entityId=" + ExternalInterface.call('eval', 'entityIdAccount') + "&currencyCode=" + ExternalInterface.call('eval', 'currencyCode') + "&valueDate=" + ExternalInterface.call('eval', 'valueDate') + "&balanceType=" + ExternalInterface.call('eval', 'balanceTypeAccount') + "&accountId=" + ExternalInterface.call('eval', 'accountIdAccount') + '&accountType=' + ExternalInterface.call('eval', 'accountTypeAccount') + "&applyCurrencyThreshold=" + ExternalInterface.call('eval', 'applyCurrencyThresholdAccount');
      "&applyCurrencyThresholdInd=" + ExternalInterface.call('eval', 'applyCurrencyThresholdIndAccount');

    } else if (this.actionMethodName == 'getUnsettledYesterdayMvmnts') {
      this.actionMethod = this.actionMethod + "&entityId=" + ExternalInterface.call('eval', 'entityIdUnsetteled') + "&currGrp=" + ExternalInterface.call('eval', 'currGrp') + "&totalCount=" + ExternalInterface.call('eval', 'totalCountUnsetteled') + "&roleId=" + ExternalInterface.call('eval', 'roleId') + "&applyCurrencyThreshold=" + ExternalInterface.call('eval', 'applyCurrencyThresholdUnsetteled');
    } else if (this.actionMethodName == 'getOpenUnexpectedMvmnts') {
      this.actionMethod = this.actionMethod + "&entityId=" + ExternalInterface.call('eval', 'entityIdUnexpected') + "&currGrp=" + ExternalInterface.call('eval', 'currGrp') + "&valueDate=" + ExternalInterface.call('eval', 'valueDate') + "&totalCount=" + ExternalInterface.call('eval', 'totalCountUnexpected') + "&roleId=" + ExternalInterface.call('eval', 'roleId') + "&applyCurrencyThreshold=" + ExternalInterface.call('eval', 'applyCurrencyThresholdUnexpected');
    } else if (this.actionMethodName == 'getBackValuedMvmnts') {
      this.actionMethod = this.actionMethod + "&entityId=" + ExternalInterface.call('eval', 'entityIdBackvalue') + "&currGrp=" + ExternalInterface.call('eval', 'currGrp') + "&valueDate=" + ExternalInterface.call('eval', 'valueDate') + "&totalCount=" + ExternalInterface.call('eval', 'totalCountBackvalue') + "&roleId=" + ExternalInterface.call('eval', 'roleId') + "&applyCurrencyThreshold=" + ExternalInterface.call('eval', 'applyCurrencyThresholdBackvalue');
    } else if (this.actionMethodName == 'getMvmntsfromWorkFlowMonitor') {
      this.actionMethod = this.actionMethod + "&entityId=" + ExternalInterface.call('eval', 'entityIdWorkflow') + "&currGrp=" + ExternalInterface.call('eval', 'currGrp') + "&totalCount=" + ExternalInterface.call('eval', 'totalCountWorkflow') + "&roleId=" + ExternalInterface.call('eval', 'roleId') + "&posLvlId=" + ExternalInterface.call('eval', 'posLvlId') + "&predictStatus=" + ExternalInterface.call('eval', 'predictStatusWorkflow') + "&matchStatus=" + ExternalInterface.call('eval', 'matchStatusWorkflow') + "&applyCurrencyThreshold=" + ExternalInterface.call('eval', 'applyCurrencyThresholdWorkflow') + "&tabIndicator=" + ExternalInterface.call('eval', 'tabIndicatorWorkflow') + "&valueDate=" + (ExternalInterface.call('eval', 'valueDate')) + "&linkFlag=" + (ExternalInterface.call('eval', 'linkFlagWorkflow'));
    }
    this.actionMethod += "&currentFilterConf=" + StringUtils.encode64(this.noneLabel);
    this.currentFilterConf = this.noneLabel;
    if(this.filterButton)
      this.filterButton.setStyle("color", "#0B333C", this.filterButton.domElement);
    /*Set the complete URL for inputData Http*/
    this.inputData.url = this.baseURL + this.actionPath + this.actionMethod;

    /*Make the request with requestParams if any*/
    this.inputData.send(this.requestParams);
    this.cGrid.onFilterChanged = this.dataRefreshGrid.bind(this);
    this.cGrid.onSortChanged = this.dataRefreshGrid.bind(this);

    ExportEvent.subscribe((exportEvent) => {
      this.export(exportEvent.type, exportEvent.startPage, exportEvent.noOfPages);
    });

    this.exportContainer.exportCancelFunction = this.exportCancel.bind(this);

  }

  initializeMenus(): void {
    this.screenVersion.loadScreenVersion(this, this.screenName, this.versionNumber, null);
    let addMenuItem: ContextMenuItem = new ContextMenuItem('Show JSON');
    addMenuItem.MenuItemSelect = this.showGridJSON.bind(this);
    this.screenVersion.svContextMenu.customItems.push(addMenuItem);
    this.contextMenu=this.screenVersion.svContextMenu;
  }

  showGridJSON(event): void {

    this.showJSONPopup = SwtPopUpManager.createPopUp(this,
      JSONViewer,
      {
        jsonData: this.lastRecievedJSON,
      });
    this.showJSONPopup.width = "700";
    this.showJSONPopup.title = SwtUtil.getPredictMessage('screen.showJSON', null);
    this.showJSONPopup.height = "400";
    this.showJSONPopup.enableResize = false;
    this.showJSONPopup.showControls = true;
    this.showJSONPopup.display();
  }

  unlockMvmnt(Unlock: string): void {
    if (this.methodName != "search" && this.initialinputscreen == 'E') {
      for (let selectCount: number = 0; selectCount < this.bottomGrid.dataProvider.length; selectCount++) {
        let lockflag1: any;
        let lock1: string = "";
        let lockUser: string = ExternalInterface.call("checkLockOnServer", this.bottomGrid.dataProvider[selectCount].movement);
        if (this.currentUser.toLowerCase() == lockUser.toLowerCase() || lockUser.toString() == "true") {
          lockflag1 = ExternalInterface.call("unlockMovementOnServer", this.bottomGrid.dataProvider[selectCount].movement);
          lock1 = lockflag1.toString();
          if (!BooleanParser.parseBooleanValue(lock1)) {
            this.swtAlert.warning(SwtUtil.getPredictMessage('label.movementCannotBeUnlocked', null), 'Warning');
          }
        }
      }
    }
  }

  refreshParent(): void {
    this.dataRefresh(null);
  }

  /*Call back method for Filter screen */

  /* Called also when we save a filter config */
  filterScreen(s: string, filterName: string = null): void {
    this.requestParams = [];
    if (s == "Reset") {
      this.filterButton.setStyle("color", "#0B333C", this.filterButton.domElement);
      this.currentFilterConf = this.noneLabel;
    } else if (s == "Filter") {
      this.currentFilterConf = this.adhocLabel;
    } else if (s != "saveFilter") {
      this.filterButton.setStyle("color", "red", this.filterButton.domElement);
      if (this.currentFilterConf == this.noneLabel) {
        this.currentFilterConf = this.adhocLabel;
      }
    }
    let filterQuery: string = "";
    let selectedList: string = "";
    let applyThresholdFlag: string = "";
    if (this.currencyThreshold.selected) {
      applyThresholdFlag = "Y";
    } else {
      applyThresholdFlag = "N";
    }
    if (this.bottomGrid != null) {
      for (let selectedListCounter: number = 0; selectedListCounter < this.bottomGrid.dataProvider.length; selectedListCounter++) {
        selectedList += this.bottomGrid.dataProvider[selectedListCounter].movement + ",";
      }
      for (let selectCount: number = 0; selectCount < this.bottomGrid.dataProvider.length; selectCount++) {
        let lockflag1: any;
        let lock1: string = "";
        let lockUser: string = ExternalInterface.call("checkLockOnServer", this.bottomGrid.dataProvider[selectCount].movement);
        if (this.currentUser.toLowerCase() == lockUser.toLowerCase() || lockUser.toString() == "true") {
          lockflag1 = ExternalInterface.call("unlockMovementOnServer", this.bottomGrid.dataProvider[selectCount].movement);
          lock1 = lockflag1.toString();
          if (!BooleanParser.parseBooleanValue(lock1)) {
            this.swtAlert.warning(SwtUtil.getPredictMessage('label.movementCannotBeUnlocked', null), 'Warning');
          }
        }
      }
    }
    this.actionPath = "outstandingmovement.do?";
    if (s == "saveFilter") {
      this.lastFilterAction = "saveFilter";
      filterQuery += "method=saveMSDFilterConfig";
      this.requestParams['filterId'] = filterName;
      filterQuery += '&filterAccountType=' + this.jsonReader.getScreenAttributes()["filterAcctType"].toString().replace(/\%/g, "per;");
      filterQuery+='&searchDateBehaviour=' + this.dateBehaviour;
      filterQuery += '&dateFormat=' + this.dateFormat;
      filterQuery += '&currGrp=' + ExternalInterface.call('eval', 'currGrp');

    } else if (s == "deleteFilter") {
      this.lastFilterAction = "deleteFilter";
      filterQuery += "method=deleteFilter";
      this.requestParams['filterId'] = filterName;
      filterQuery += '&filterAccountType=' + this.jsonReader.getScreenAttributes()["filterAcctType"].toString().replace(/\%/g, "per;");
    } else {
      filterQuery += "method=filterOutMovementSummary";
    }
    filterQuery += '&entityId=' + this.entityCombo.selectedLabel;
    filterQuery += '&currencyCode=' + ExternalInterface.call('eval', 'currencyCode');
    filterQuery += '&selectedList=' + selectedList;
    filterQuery += '&applyCurrencyThreshold=' + applyThresholdFlag;
    filterQuery += '&selectedSort=' + this.jsonReader.getScreenAttributes()["selectedSort"];
    filterQuery += '&selectedFilter=' + this.jsonReader.getScreenAttributes()["selectedFilter"];
    filterQuery += "&selectedTab=" + ExternalInterface.call('eval', 'tabPressed');
    filterQuery += "&posLvlId=" + ExternalInterface.call('eval', 'posLvlId');
    filterQuery += '&amountover=' + ExternalInterface.call('amountOver');
    filterQuery += '&amountunder=' + ExternalInterface.call('amountUnder');
    filterQuery += '&group=' + ExternalInterface.call('getGroup');
    filterQuery += '&metaGroup=' + ExternalInterface.call('getMetagroup');
    filterQuery += '&initialscreen=' + ExternalInterface.call('eval', 'initialscreen');
    filterQuery += '&extraFilter=' + ExternalInterface.call('eval', 'extraFilter');
    if (ExternalInterface.call('valueFromDate') == "") {
      filterQuery += '&valueFromDate=' + ExternalInterface.call('eval', 'tabPressed');
    } else {
      filterQuery += '&valueFromDate=' + ExternalInterface.call('valueFromDate');
    }
    if (ExternalInterface.call('valueToDate') == "" && this.initialinputscreen != "E") {
      filterQuery += '&valueToDate=' + ExternalInterface.call('eval', 'tabPressed');
    } else {
      filterQuery += '&valueToDate=' + ExternalInterface.call('valueToDate');
    }
    filterQuery += '&timefrom=' + ExternalInterface.call('timefrom');
    filterQuery += '&timeto=' + ExternalInterface.call('timeto');
    filterQuery += '&inputDate=' + ExternalInterface.call('inputDate');
    filterQuery += '&reference=' + ExternalInterface.call('reference');
    filterQuery += '&totalFlag=' + ExternalInterface.call('eval', 'totalFlagOpenMovements');
    filterQuery += '&openMovementFlagSearch=' + ExternalInterface.call('openMovementFlag'); // Added by KaisBS for mantis 1756
    filterQuery += '&refFlagFilterSearch=' + ExternalInterface.call('referenceFlag');
    filterQuery += '&currentFilterConf=' + StringUtils.encode64(this.currentFilterConf);
    if (ExternalInterface.call('eval', 'workflow') != null)
      filterQuery += "&workflow=" + ExternalInterface.call('eval', 'workflow');
    if (this.initialinputscreen != 'E') {
      filterQuery += '&nodata=Y';
    } else {
      filterQuery += '&nodata=N';
    }
    if (s == "saveFilter") {
      this.filterConfData.url = this.baseURL + this.actionPath + filterQuery;
      this.filterConfData.send(this.requestParams);
    } else if (s == "deleteFilter") {
      this.filterConfData.url = this.baseURL + this.actionPath + filterQuery;
      this.filterConfData.send(this.requestParams);
    } else {
      this.inputData.url = this.baseURL + this.actionPath + filterQuery;
      this.inputData.send(this.requestParams);
    }
  }

  /** This function is used to display notes avialable for the selected movements.
   */
  openNotes(): void {

    let tempFlag: boolean = false;
    if (this.cGrid.selectedIndices.length > 0) {
      tempFlag = true;
    }
    if (tempFlag) {
      let sMovementId: string = this.cGrid.dataProvider[this.cGrid.selectedIndex].movement;
      ExternalInterface.call("movementNotesFromSearch", "notes", sMovementId, "");
    } else {
      if (this.bottomGrid.dataProvider.length == 1 && this.cGrid.selectedIndices.length == 0) {
        let MvmntId: string;
        for (let btmCount: number = 0; btmCount < this.bottomGrid.dataProvider.length; btmCount++) {
          MvmntId = this.bottomGrid.dataProvider[btmCount].movement;
        }
        ExternalInterface.call("movementNotesFromSearch", "notes", MvmntId, "");
      }
    }
  }

  /**
   * This method is used to check whether the selected movement has messages or
   * not, If it has messages then the messages will displayed in a separate
   * window.
   */
  openMessages(): void {
    let result: string;
    let noOfMovements: string;
    let msgId: string;
    let tempFlag: boolean = false;
    if (this.cGrid.selectedIndices.length > 0) {
      tempFlag = true;
    }
    if (tempFlag) {
      let sMovementId: string = this.cGrid.dataProvider[this.cGrid.selectedIndex].movement;
      result = ExternalInterface.call("setMsgButtonStatus", sMovementId);
      noOfMovements = result.substring(0, result.indexOf('|'));
      msgId = result.substring(result.indexOf('|') + 1, result.length);
      ExternalInterface.call("openMsgDisplayWindow", sMovementId, noOfMovements, msgId);
    } else {
      if (this.bottomGrid.dataProvider.length == 1 && this.cGrid.selectedIndices.length == 0) {
        let MvmntId: string;
        for (let btmCount: number = 0; btmCount < this.bottomGrid.dataProvider.length; btmCount++) {
          MvmntId = this.bottomGrid.dataProvider[btmCount].movement;
        }
        result = ExternalInterface.call("setMsgButtonStatus", MvmntId);
        noOfMovements = result.substring(0, result.indexOf('|'));
        msgId = result.substring(result.indexOf('|') + 1, result.length);
        ExternalInterface.call("openMsgDisplayWindow", MvmntId, noOfMovements, msgId);
      }
    }
  }

  /**
   * This method is used to display the selected movement details in a new
   * window.
   */
  openMovement(): void {
    let sEntityId: string;
    let tempFlag: boolean = false;
    if (this.cGrid.selectedIndices.length > 0) {
      tempFlag = true;
    }
    if (this.methodName == 'search') {
      //get the selected movement when clicking the Mvmnt button.
      this.cGridSelMvmt = this.cGrid.dataProvider[this.cGrid.selectedIndex].movement;
    }
    if (tempFlag) {

      let sMovementId: string = this.cGrid.dataProvider[this.cGrid.selectedIndex].movement;
      sEntityId = (this.entityCombo.selectedLabel).toString();
      ExternalInterface.call("showMvmnt", "showMovementDetails", sMovementId, sEntityId, "");
    } else {
      if (this.bottomGrid.dataProvider.length == 1 && this.cGrid.selectedIndices.length == 0) {
        let MvmntId: string;
        for (let btmCount: number = 0; btmCount < this.bottomGrid.dataProvider.length; btmCount++) {
          MvmntId = this.bottomGrid.dataProvider[btmCount].movement;
        }
        sEntityId = (this.entityCombo.selectedLabel).toString();
        ExternalInterface.call("showMvmnt", "showMovementDetails", MvmntId, sEntityId, "");
      }
    }
  }

  /**
   * This method is used to maintain the button status when a row is clicked as well as checks
   * the clickable status of the cell and opens the appropriate pade based on the breakdown.
   */

  cellLogic(e): void {
    try {

    //variable declarations
    let selectedStatus: string = "";
    let statusConfirmFlag: boolean = false;
    let statusSuspendFlag: boolean = false;
    let statusOutStandingFlag: boolean = false;
    var acctFlag: boolean = true;
    let deselectedMovement: string = "";
    let deselectedFlag: boolean = false;
    let selectedFlag: boolean = false;
    let rowArrayColection = [];
    let nonEmptyMatch = [];
    let arrayToRemove = [];
    let currencyCode: string = "";
    let totalSelectedAmount: number = 0;
    let amountSign: string;
    let amountAsstring: string;
    let amountAsNumber: number = 0;
    let amountSplitArr = [];
    let amountArr = [];
    let ccyFormat: string = this.jsonReader.getScreenAttributes()["currencyFormat"];
    let ccyCode: string = "";
    let formatedAmount: string = "";
    try {
      if (this.methodName != "search" && this.initialinputscreen == 'E') {
        this.selectedMovements= [];
        for (let selMvmtCounter:number=0; selMvmtCounter < this.bottomGrid.dataProvider.length; selMvmtCounter++)
        {
          this.selectedMovements.push(""+this.bottomGrid.dataProvider[selMvmtCounter].movement);
        }
        if (this.cGrid.selectedIndices.length > 0 && ExternalInterface.call('eval', 'archiveIdSearch') == "") {
          for (let chkselectLockCount:number=0; chkselectLockCount < this.cGrid.selectedIndices.length; chkselectLockCount++){
            let lockFlag:boolean=false;
            for (let selectLockCount:number=0; selectLockCount < this.selectedMovements.length; selectLockCount++) {
              if (this.selectedMovements[selectLockCount].toString() == this.cGrid.dataProvider[this.cGrid.selectedIndices[chkselectLockCount]].movement)
              {
                lockFlag=true;
              }
            }
            if (!lockFlag) {
              let lockflag:Object=new Object();
              let lock:string="";
              //Call to check wheather the selected movement is locked or not
              lockflag=ExternalInterface.call("lockMovementOnServer", this.cGrid.dataProvider[this.cGrid.selectedIndices[chkselectLockCount]].movement);
              lock=lockflag.toString();
              if (!BooleanParser.parseBooleanValue(lock) && this.currentUser.toLowerCase() != lock.toLowerCase())
              {
                this.swtAlert.warning('Movement'+ " " + this.cGrid.dataProvider[this.cGrid.selectedIndices[chkselectLockCount]].movement + " " + 'is in use by' + " " + lock,'Warning');
                let selectedIndexes= this.cGrid.selectedIndices;
                for (let selectedCounter:number=0; selectedCounter < selectedIndexes.length; selectedCounter++)
                {
                  if (selectedIndexes[selectedCounter] == this.cGrid.selectedIndices[chkselectLockCount])
                  {
                    selectedIndexes.splice(selectedCounter, 1);
                    chkselectLockCount=chkselectLockCount - 1;
                  }
                }
                this.cGrid.selectedIndices= [];
                this.cGrid.selectedIndices=selectedIndexes;
              }
            }
          }
        }
        let chkSelectedMovements = [];
        let chkSelectedAccount = [];
        for (let chkSelectCounter:number=0; chkSelectCounter < this.cGrid.selectedIndices.length; chkSelectCounter++)
        {
          chkSelectedMovements.push(""+this.cGrid.dataProvider[this.cGrid.selectedIndices[chkSelectCounter]].movement);
          chkSelectedAccount.push(this.cGrid.dataProvider[this.cGrid.selectedIndices[chkSelectCounter]].account);

        }
        for (let selectCount:number=0; selectCount < this.selectedMovements.length; selectCount++) {
          let unlockFlag:boolean=false;
          let currenctPageFlag:boolean=false;
          for (let chkBtmSelectCount:number=0; chkBtmSelectCount < this.cGrid.dataProvider.length; chkBtmSelectCount++) {
            if (this.selectedMovements[selectCount] == this.cGrid.dataProvider[chkBtmSelectCount].movement) {
              currenctPageFlag=true;
            }
          }
          if (!currenctPageFlag)
            unlockFlag=true;
          for (let chkSelectCount:number=0; chkSelectCount < chkSelectedMovements.length; chkSelectCount++) {
            if (currenctPageFlag && this.selectedMovements[selectCount] == chkSelectedMovements[chkSelectCount]) {
              unlockFlag=true;
            }
          }
          if (!unlockFlag)
          {
            let lockflag1:Object=new Object();
            let lock1:string="";
            let lockUser:string=ExternalInterface.call("checkLockOnServer", this.selectedMovements[selectCount]);
            if (this.currentUser.toLowerCase() == lockUser.toLowerCase() || lockUser.toString() == "true")
            {
              lockflag1=ExternalInterface.call("unlockMovementOnServer", this.selectedMovements[selectCount]);
              lock1=lockflag1.toString();
              if (!BooleanParser.parseBooleanValue(lock1))
              {
                this.swtAlert.warning(SwtUtil.getPredictMessage('label.movementCannotBeUnlocked', null), 'Warning');
              }
            }

          }
        }

          let lockflag2:Object=new Object();
          let lock2:string="";
          if (chkSelectedMovements.length > 0)
          {
            for (let chkSelectCount:number=0; chkSelectCount < chkSelectedMovements.length; chkSelectCount++)
            {
              lockflag2=ExternalInterface.call("accountAccessConfirm", (chkSelectedMovements[chkSelectCount]).toString(), (this.entityCombo.selectedLabel).toString());
              lock2=lockflag2.toString();
              if (!BooleanParser.parseBooleanValue(lock2))
                acctFlag=false;
            }
          }
        }
      
      this.selectedMovements= [];

      for (let selMvmtCounter:number=0; selMvmtCounter < this.cGrid.selectedIndices.length; selMvmtCounter++) {
        this.selectedMovements.push(""+this.cGrid.dataProvider[this.cGrid.selectedIndices[selMvmtCounter]].movement);
      }
      if (this.methodName != 'search' && (this.actionMethodName == 'displayOpenMovements')) {
        let btmCheckCount: number = 0;
        //push items to bottomgrid

        if (this.bottomGrid.selectedIndex >= 0) {
          this.removeButton.enabled = true;
          this.removeButton.buttonMode = true;
        } else {
          this.removeButton.enabled = false;
          this.removeButton.buttonMode = false;
        }
        //if (e.target.valueOf().id != "bottom") FIXME
        for (let btmCount: number = 0; btmCount < this.bottomGrid.dataProvider.length; btmCount++) {
          let btmflag: boolean = true;
          for (let cGridCount: number = 0; cGridCount < this.cGrid.dataProvider.length; cGridCount++) {
            if (this.bottomGrid.dataProvider[btmCount].movement == this.cGrid.dataProvider[cGridCount].movement) {
              btmflag = false;
            }
          }
          if (btmflag) {
            btmCheckCount++;
          }
        }
        if (  ((this.bottomGrid.dataProvider.length - btmCheckCount) != this.cGrid.selectedItems.length || this.cGrid.selectedItems.length == 1)) {
          /* Removing the deselected movements from the bottom grid */
          for (let btmCounter: number = 0; btmCounter < this.bottomGrid.dataProvider.length; btmCounter++) {

            let row = {
              'pos': {'content': this.bottomGrid.dataProvider[btmCounter].pos},
              'value': {'content': this.bottomGrid.dataProvider[btmCounter].value},
              'amount': {'content': this.bottomGrid.dataProvider[btmCounter].amount},
              'sign': {'content': this.bottomGrid.dataProvider[btmCounter].sign},
              'ccy': {'content': this.bottomGrid.dataProvider[btmCounter].ccy},
              'ref1': {'content': this.bottomGrid.dataProvider[btmCounter].ref1},
              'account': {'content': this.bottomGrid.dataProvider[btmCounter].account},
              'input': {'content': this.bottomGrid.dataProvider[btmCounter].input},
              'cparty': {'content': this.bottomGrid.dataProvider[btmCounter].cparty},
              'pred': {'content': this.bottomGrid.dataProvider[btmCounter].pred},
              'ext': {'content': this.bottomGrid.dataProvider[btmCounter].ext},
              'status': {'content': this.bottomGrid.dataProvider[btmCounter].status},
              'matchid': {'content': this.bottomGrid.dataProvider[btmCounter].matchid},
              'source': {'content': this.bottomGrid.dataProvider[btmCounter].source},
              'format': {'content': this.bottomGrid.dataProvider[btmCounter].format},
              'notes': {'content': this.bottomGrid.dataProvider[btmCounter].notes},
              'beneficiary': {'content': this.bottomGrid.dataProvider[btmCounter].beneficiary},
              'ref2': {'content': this.bottomGrid.dataProvider[btmCounter].ref2},
              'ref3': {'content': this.bottomGrid.dataProvider[btmCounter].ref3},
              'movement': {'content': this.bottomGrid.dataProvider[btmCounter].movement},
              'book': {'content': this.bottomGrid.dataProvider[btmCounter].book},
              'custodian': {'content': this.bottomGrid.dataProvider[btmCounter].custodian},
              'xref': {'content': this.bottomGrid.dataProvider[btmCounter].xref},
              'update_date': {'content': this.bottomGrid.dataProvider[btmCounter].update_date},
              'matchingparty': {'content': this.bottomGrid.dataProvider[btmCounter].matchingparty},
              'producttype': {'content': this.bottomGrid.dataProvider[btmCounter].producttype},
              'postingdate': {'content': this.bottomGrid.dataProvider[btmCounter].postingdate},
              'extra_text1': {'content': this.bottomGrid.dataProvider[btmCounter].extra_text1},
              'alerting': {'content': this.bottomGrid.dataProvider[btmCounter].alerting},
              'ilmfcast' : {'content' :this.bottomGrid.dataProvider[btmCounter].ilmfcast },
              'uetr' : {'content' :this.bottomGrid.dataProvider[btmCounter].uetr }

            };

            for (let i: number = 0; i < this.profileAddCols.length; i++) {
              let col= ((this.profileAddCols[i].toString()).replace('[','')).replace(']','');
              const val= {'content' :this.bottomGrid.dataProvider[btmCounter][col]};
              row[col]=val;
            }
            
            rowArrayColection.push(row)
            arrayToRemove.push(row);
            if(this.bottomGrid.dataProvider[btmCounter].matchid != "") {
                nonEmptyMatch.push(""+this.bottomGrid.dataProvider[btmCounter].matchid)
            }

          }
          let removeArr = [];
          for (let x = 0; x <rowArrayColection.length; x++ ) {
            for (let l: number = 0; l < this.cGrid.dataProvider.length; l++) {
              if (rowArrayColection[x].movement.content == this.cGrid.dataProvider[l].movement) {
                let removeFlag: boolean = true;
                for (let m: number = 0; m < this.cGrid.selectedIndices.length; m++) {
                  if (this.cGrid.dataProvider[this.cGrid.selectedIndices[m]].movement == rowArrayColection[x].movement.content) {
                    removeFlag = false;
                  }
                }
                if (removeFlag) {
                  removeArr.push(""+ rowArrayColection[x].movement.content);
                }
              }
            }
          }
          let items = [];
          if (removeArr.length > 0) {
            for (let rmCounter = 0; rmCounter < removeArr.length; rmCounter++) {

              for (let remCounter = 0; remCounter < arrayToRemove.length; remCounter++) {
                if (removeArr[rmCounter] == arrayToRemove[remCounter].movement.content) {
                 // rowArrayColection.splice(remCounter, 1);
                  let item = this.bottomGrid.getFilteredItems().find(x=>x.movement == removeArr[rmCounter]);
                  if(item != undefined)
                    items.push(item);
                  const index = this.selectedMvmts.indexOf(arrayToRemove[remCounter].movement.content.toString(), 0);
                  if (index > -1) {
                    this.selectedMvmts.splice(index, 1);
                  }
                }
              }
            }
            try{
              if (items.length > 0) {
                this.bottomGrid.dataviewObj.beginUpdate();
                this.bottomGrid.angularGridInstance.gridService.deleteItems(items);
                this.bottomGrid.dataviewObj.endUpdate();
              }
            }catch(e) {
              console.log('error in delete item', e)
            }

          }
          let deSelectFlag: boolean = false;
          // let nonEmptyMatch = []
          let selectedMovementBeforeUnselect= [];
          let selectedMovementAfterUnselect= [];
          let k = 0;
          for (var i: number =0; i< this.cGrid.selectedIndices.length; i ++ ) {
            selectedMovementBeforeUnselect.push(""+this.cGrid.dataProvider[this.cGrid.selectedIndices[i]].movement);

            if(this.cGrid.dataProvider[this.cGrid.selectedIndices[i]].matchid!= "") {
              nonEmptyMatch.push(""+this.cGrid.dataProvider[this.cGrid.selectedIndices[i]].matchid)
            }
            if(nonEmptyMatch.length > 1){
              for(let k=0 ; k< nonEmptyMatch.length-1; k++) {
                if(nonEmptyMatch[k] != nonEmptyMatch[k+1]) {
                  deSelectFlag= true;
                  break;
                }
              }
            }
/*
            for (var selMatchCount: number = 0; selMatchCount < rowArrayColection.length; selMatchCount++) {
              if ((rowArrayColection[selMatchCount].matchid != null && rowArrayColection[selMatchCount].matchid.content != "") && (this.cGrid.dataProvider[this.cGrid.selectedIndices[i]].matchid != null && this.cGrid.dataProvider[this.cGrid.selectedIndices[i]].matchid != "")) {
                if (rowArrayColection[selMatchCount].matchid.content != this.cGrid.dataProvider[this.cGrid.selectedIndices[i]].matchid) {
                  deSelectFlag = true;
                  break
                }
              } else {
                if(nonEmptyMatch.length > 1){
                  for(let k=0 ; k< nonEmptyMatch.length; i++) {
                    if(nonEmptyMatch[k] != nonEmptyMatch[k+1]) {
                      deSelectFlag= true;
                      break;
                    }
                  }
                }
              }

            }
*/
          }
          if (deSelectFlag) {
            let newGridSelection = [];
            let resetCgridCounter: number = 0;
            let btmCounter: number = 0;
            for (btmCounter = 0; btmCounter < this.bottomGrid.dataProvider.length; btmCounter++) {
              for (resetCgridCounter = 0; resetCgridCounter < this.cGrid.dataProvider.length; resetCgridCounter++) {
                if (this.bottomGrid.dataProvider[btmCounter].movement == this.cGrid.dataProvider[resetCgridCounter].movement) {
                  newGridSelection.push(resetCgridCounter);
                  selectedMovementAfterUnselect.push(this.cGrid.dataProvider[resetCgridCounter].movement)
                }
              }
            }
            this.cGrid.selectedIndices = newGridSelection;
            //let unlockFlag=ExternalInterface.call("unlockMovementOnServer", this.bottomGrid.dataProvider[i].movement);

            // Find the difference between two arrays
              const differenceArray = selectedMovementBeforeUnselect.filter(
                item => !selectedMovementAfterUnselect.includes(item)
              );

              // Iterate over the differenceArray and run a function
              for (const element of differenceArray) {
                // Replace the following line with your actual function
                let unlockFlag=ExternalInterface.call("unlockMovementOnServer", element);

              }

            this.swtAlert.warning(SwtUtil.getPredictMessage('label.selectedOnlyMatchedItems', null), 'Warning');

          } else {
            for (let z: number = 0; z < this.cGrid.selectedIndices.length; z++) {
              let addFlag: boolean = true;
              for (var r=0; r < rowArrayColection.length; r++) {
                if (this.cGrid.dataProvider[this.cGrid.selectedIndices[z]].movement == rowArrayColection[r].movement.content) {
                  addFlag = false;
                  break;
                }
              }
              if (addFlag) {
                let item: {}; 
                  item = {
                  'pos': {'content': this.cGrid.dataProvider[this.cGrid.selectedIndices[z]].pos},
                  'value': {'content': this.cGrid.dataProvider[this.cGrid.selectedIndices[z]].value},
                  'amount': {'content': this.cGrid.dataProvider[this.cGrid.selectedIndices[z]].amount},
                  'sign': {'content': this.cGrid.dataProvider[this.cGrid.selectedIndices[z]].sign},
                  'ccy': {'content': this.cGrid.dataProvider[this.cGrid.selectedIndices[z]].ccy},
                  'ref1': {'content': this.cGrid.dataProvider[this.cGrid.selectedIndices[z]].ref1},
                  'account': {'content': this.cGrid.dataProvider[this.cGrid.selectedIndices[z]].account},
                  'input': {'content': this.cGrid.dataProvider[this.cGrid.selectedIndices[z]].input},
                  'cparty': {'content': this.cGrid.dataProvider[this.cGrid.selectedIndices[z]].cparty},
                  'pred': {'content': this.cGrid.dataProvider[this.cGrid.selectedIndices[z]].pred},
                  'ext': {'content': this.cGrid.dataProvider[this.cGrid.selectedIndices[z]].ext},
                  'status': {'content': this.cGrid.dataProvider[this.cGrid.selectedIndices[z]].status},
                  'matchid': {'content': this.cGrid.dataProvider[this.cGrid.selectedIndices[z]].matchid},
                  'source': {'content': this.cGrid.dataProvider[this.cGrid.selectedIndices[z]].source},
                  'format': {'content': this.cGrid.dataProvider[this.cGrid.selectedIndices[z]].format},
                  'notes': {'content': this.cGrid.dataProvider[this.cGrid.selectedIndices[z]].notes},
                  'beneficiary': {'content': this.cGrid.dataProvider[this.cGrid.selectedIndices[z]].beneficiary},
                  'ref2': {'content': this.cGrid.dataProvider[this.cGrid.selectedIndices[z]].ref2},
                  'ref3': {'content': this.cGrid.dataProvider[this.cGrid.selectedIndices[z]].ref3},
                  'movement': {'content': this.cGrid.dataProvider[this.cGrid.selectedIndices[z]].movement},
                  'book': {'content': this.cGrid.dataProvider[this.cGrid.selectedIndices[z]].book},
                  'custodian': {'content': this.cGrid.dataProvider[this.cGrid.selectedIndices[z]].custodian},
                  'xref': {'content': this.cGrid.dataProvider[this.cGrid.selectedIndices[z]].xref},
                  'update_date': {'content': this.cGrid.dataProvider[this.cGrid.selectedIndices[z]].update_date},
                  'matchingparty': {'content': this.cGrid.dataProvider[this.cGrid.selectedIndices[z]].matchingparty},
                  'producttype': {'content': this.cGrid.dataProvider[this.cGrid.selectedIndices[z]].producttype},
                  'postingdate': {'content': this.cGrid.dataProvider[this.cGrid.selectedIndices[z]].postingdate},
                  'extra_text1': {'content': this.cGrid.dataProvider[this.cGrid.selectedIndices[z]].extra_text1},
                  'alerting': {'content': this.cGrid.dataProvider[this.cGrid.selectedIndices[z]].alerting},
                  'positionlevel': {'content': this.cGrid.dataProvider[this.cGrid.selectedIndices[z]].slickgrid_rowcontent.pos.positionlevel},
                  'id' : {'content' :this.cGrid.dataProvider[this.cGrid.selectedIndices[z]].movement },
                  'ilmfcast' : {'content' :this.cGrid.dataProvider[this.cGrid.selectedIndices[z]].ilmfcast },
                  'uetr' : {'content' :this.cGrid.dataProvider[this.cGrid.selectedIndices[z]].uetr }

                };
                //Mantis 
                for (let i: number = 0; i < this.profileAddCols.length; i++) {
                  let col= this.profileAddCols[i].toString().trim();
                  const val= {'content' :this.cGrid.dataProvider[this.cGrid.selectedIndices[z]][col]};
                item[col]=val;
                }
                this.bottomGrid.appendRow(item, true);
                this.bottomGrid.dataProvider[this.bottomGrid.dataProvider.length -1].slickgrid_rowcontent.amount.negative = this.cGrid.dataProvider[this.cGrid.selectedIndices[z]].slickgrid_rowcontent.amount.negative;
                this.bottomGrid.selectedIndex = -1;

              }
            }

          }
        }
        if ( this.bottomGrid != null &&  this.bottomGrid.dataProvider.length > 0){
          this.bottomGrid.dataset = this.bottomGrid.dataProvider
          var var_positionLevel:number;
          var var_sign: string;
          var var_amount: number=0;
          var amount:string="";
          var var_max:number=0;
          var var_min:number=0;
          var diffArr = [];
          var arrayPos = [];
          for (var high=0; high < this.bottomGrid.dataProvider.length; high++)
          {
            this.bottomGrid.dataProvider[high].id = high;
            if(this.bottomGrid.dataProvider[high].slickgrid_rowcontent && this.bottomGrid.dataProvider[high].slickgrid_rowcontent.id)
             this.bottomGrid.dataProvider[high].slickgrid_rowcontent.id.content = high;
            this.bottomGrid.dataProvider[high].num = high;
            if(this.bottomGrid.dataProvider[high].slickgrid_rowcontent && this.bottomGrid.dataProvider[high].slickgrid_rowcontent.num)
             this.bottomGrid.dataProvider[high].slickgrid_rowcontent.num.content = high;
            if(this.bottomGrid.dataProvider[high].positionlevel)
              var_positionLevel=parseInt(this.bottomGrid.dataProvider[high].positionlevel);
            else
              var_positionLevel=parseInt(this.bottomGrid.dataProvider[high].slickgrid_rowcontent.pos.positionlevel);
            arrayPos.push(var_positionLevel);
            var_sign=this.bottomGrid.dataProvider[high].sign;
            amount= this.bottomGrid.dataProvider[high].amount;
            /*let amountFormatted = this.replaceAt(amount, (amount.length)-3, '');
            let re = /\./gi;
             amountFormatted = amountFormatted.replace(re, ',')*/
            if (ccyFormat == "currencyPat1")
            {
              amountArr=amount.split(',');
              amount="";
              for (var commaCounter=0; commaCounter < amountArr.length; commaCounter++)
              {
                amount+=amountArr[commaCounter];
              }
            }
            else
            {
              amountArr=amount.split('.');
              amount="";
              for (var dotCounter=0; dotCounter < amountArr.length; dotCounter++)
              {
                amount+=amountArr[dotCounter];
              }
              amount=amount.replace(',', '.');
            }
            var_amount= parseFloat(amount);
            if(var_sign == "D")
              totalSelectedAmount-=var_amount;
            else
              totalSelectedAmount+=var_amount;
            switch (var_positionLevel)
            {
              case 1:
                if (diffArr[0] == undefined)
                {
                  diffArr[0]=0;
                }
                if (var_sign == "D")
                {
                  diffArr[0]=diffArr[0] - var_amount;
                }
                else
                {
                  diffArr[0]=diffArr[0] + var_amount;

                }
                break;
              case 2:
                if (diffArr[1] == undefined)
                {
                  diffArr[1]=0;
                }
                if (var_sign == "D")
                {
                  diffArr[1]=diffArr[1] - var_amount;
                }
                else
                {
                  diffArr[1]=diffArr[1] + var_amount;
                }
                break;
              case 3:
                if (diffArr[2] == undefined)
                {
                  diffArr[2]=0;
                }
                if (var_sign == "D")
                {
                  diffArr[2]=diffArr[2] - var_amount;
                }
                else
                {
                  diffArr[2]=diffArr[2] + var_amount;
                }
                break;
              case 4:
                if (diffArr[3] == undefined)
                {
                  diffArr[3]=0;
                }
                if (var_sign == "D")
                {
                  diffArr[3]=diffArr[3] - var_amount;
                }
                else
                {
                  diffArr[3]=diffArr[3] + var_amount;
                }
                break;
              case 5:
                if (diffArr[4] == undefined)
                {
                  diffArr[4]=0;
                }
                if (var_sign == "D")
                {
                  diffArr[4]=diffArr[4] - var_amount;
                }
                else
                {
                  diffArr[4]=diffArr[4] + var_amount;
                }
                break;
              case 6:
                if (diffArr[5] == undefined)
                {
                  diffArr[5]=0;
                }
                if (var_sign == "D")
                {
                  diffArr[5]=diffArr[5] - var_amount;
                }
                else
                {
                  diffArr[5]=diffArr[5] + var_amount;
                }
                break;
              case 7:
                if (diffArr[6] == undefined)
                {
                  diffArr[6]=0;
                }
                if (var_sign == "D")
                {
                  diffArr[6]=diffArr[6] - var_amount;

                }
                else
                {
                  diffArr[6]=diffArr[6] + var_amount;

                }
                break;
              case 8:
                if (diffArr[7] == undefined)
                {
                  diffArr[7]=0;
                }
                if (var_sign == "D")
                {
                  diffArr[7]=diffArr[7] - var_amount;
                }
                else
                {
                  diffArr[7]=diffArr[7] + var_amount;
                }
                break;
              case 9:
                if (diffArr[8] == undefined)
                {
                  diffArr[8]=0;
                }
                if (var_sign == "D")
                {
                  diffArr[8]=diffArr[8] - var_amount;
                }
                else
                {
                  diffArr[8]=diffArr[8] + var_amount;
                }
            }
          }
          var count=0;
          var diffPositons: string="";
          for (var i=0; i < diffArr.length; i++)
          {
            if (diffArr[i] != null)
            {
              if (count == 0)
              {
                count=-1;
                var_max=diffArr[i];
                var_min=diffArr[i];
              }
              else
              {
                diffPositons='Y';
                if (diffArr[i] < var_min)
                {
                  var_min=diffArr[i];
                }
                if (diffArr[i] > var_max)
                {
                  var_max=diffArr[i];
                }
              }
            }
          }
          var numFormat ={"precision": "", "rounding": ""};
          numFormat.precision="2";
          numFormat.rounding="nearest";
          var difference:number=0;
          var isDifferentPosition: boolean = false;
          for(let i =0; i< arrayPos.length; i++) {
            if(arrayPos[i] && arrayPos[i+1] && (arrayPos[i] !== arrayPos[i+1])) {
              isDifferentPosition = true;
              break;
            }
          }
          difference=var_max - var_min;
          if (difference == 0)
          {

            if (isDifferentPosition)
            {
              if (ccyFormat == "currencyPat1")
              this.diffText.text="0.00";
              else
              this.diffText.text="0,00";
            }
            else
            {
              this.diffText.text="";
            }
          }
          else
          {
            var expandDiffAmt:string= this.addZeroes(difference.toString()); //numFormat.format(difference);
            if (expandDiffAmt.indexOf(".") == -1)
            {
              expandDiffAmt+="00";
            }
            else
            {
              var expandDiffArr = [];
              expandDiffArr=expandDiffAmt.split(".");
              expandDiffAmt=expandDiffArr[0] + expandDiffArr[1]
            }
            if(isDifferentPosition) {
              this.diffText.text=ExternalInterface.call("expandAmtDifference", expandDiffAmt, ccyFormat, ccyCode);
            } else {
              this.diffText.text = "";
            }

          }
        }

      } else {
        var amountAsString: string;


        for (var selMvmtCounter=0; selMvmtCounter < this.cGrid.selectedIndices.length; selMvmtCounter++)
        {
          amountSign= this.cGrid.dataProvider[this.cGrid.selectedIndices[selMvmtCounter]].sign;
          amountAsString= this.cGrid.dataProvider[this.cGrid.selectedIndices[selMvmtCounter]].amount;
          if (ccyFormat == "currencyPat1")
          {
            amountSplitArr=amountAsString.split(',');
            amountAsString="";

            for (var commaCounter=0; commaCounter < amountSplitArr.length; commaCounter++)
            {
              amountAsString+=amountSplitArr[commaCounter];
            }
          }
          else
          {
            amountSplitArr=amountAsString.split('.');
            amountAsString="";

            for (var dotCounter=0; dotCounter < amountSplitArr.length; dotCounter++)
            {
              amountAsString+=amountSplitArr[dotCounter];
            }

            amountAsString=amountAsString.replace(',', '.');

          }
          amountAsNumber=parseFloat(amountAsString);


          if(amountSign == "D")
            totalSelectedAmount-=amountAsNumber;
          else
            totalSelectedAmount+=amountAsNumber;
        }

      }
      if (this.bottomGrid != undefined )// != null)
      {
        for (let selMatchCount = 0;  selMatchCount < this.bottomGrid.dataProvider.length; selMatchCount++)
        {
          selectedStatus=this.bottomGrid.dataProvider[selMatchCount].status;
          if (selectedStatus.split(" ")[0] == "CONFIRMED")
          {
            statusConfirmFlag=true;
          }
          else if (selectedStatus.split(" ")[0] == "SUSPENDED")
          {
            statusSuspendFlag=true;
          }
          else if (selectedStatus.split(" ")[0] == "OUTSTANDING")
          {
            statusOutStandingFlag=true;
          }
        }
      }
      if (acctFlag && this.bottomGrid != undefined) {
        this.reconButton.enabled=true;
        this.reconButton.buttonMode=true;

        if (statusOutStandingFlag == false)
        {
          if (statusConfirmFlag == false)
          {
            this.confirmButton.enabled=true;
            this.confirmButton.buttonMode=true;
          }
          else
          {
            this.confirmButton.enabled=false;
            this.confirmButton.buttonMode=false;
          }
          if (statusSuspendFlag == false)
          {
            this.suspendButton.enabled=true;
            this.suspendButton.buttonMode=true;
          }
          else
          {
            this.suspendButton.enabled=false;
            this.suspendButton.buttonMode=false;
          }
        }
        else
        {
          this.confirmButton.enabled=true;
          this.confirmButton.buttonMode=true;
          this.suspendButton.enabled=true;
          this.suspendButton.buttonMode=true;
        }
        this.matchButton.enabled=true;
        this.matchButton.buttonMode=true;
      }

      if (this.cGrid.selectedIndices.length > 1) {
        //multiple select
        this.messageButton.enabled = false;
        this.messageButton.buttonMode = false;
        this.movementButton.enabled = false;
        this.movementButton.buttonMode = false;
        this.noteButton.enabled = false;
        this.noteButton.buttonMode = false;
        if (this.reconButton) {
          this.reconButton.enabled = true;
          this.reconButton.buttonMode = false;
        }

      } else if (this.cGrid.selectedIndices.length == 1) {
        let movId: string = this.cGrid.dataProvider[this.cGrid.selectedIndex].movement;
        let result: string = ExternalInterface.call("setMsgButtonStatus", movId);
        let noOfMovements: string = result.substring(0, result.indexOf('|'));
        if (noOfMovements == "0" || ExternalInterface.call('eval', 'archiveIdSearch') != "") {
          this.messageButton.enabled = false;
          this.messageButton.buttonMode = false;
        } else {
          this.messageButton.enabled = true;
          this.messageButton.buttonMode = true;
        }
        let msgId: string = result.substring(result.indexOf('|') + 1, result.length);
        this.movementButton.enabled = true;
        this.movementButton.buttonMode = true;
        this.noteButton.enabled = true;
        this.noteButton.buttonMode = true;

        if (this.bottomGrid != null) {
          if (this.bottomGrid.dataProvider.length > 1) {
            this.movementButton.enabled = false;
            this.movementButton.buttonMode = false;
            this.noteButton.enabled = false;
            this.noteButton.buttonMode = false;
            this.messageButton.enabled = false;
            this.messageButton.buttonMode = false;
          }
          if (this.bottomGrid.selectedIndex < 0) {

            this.removeButton.enabled = false;
            this.removeButton.buttonMode = false;
          }
          let status: string = this.cGrid.dataProvider[this.cGrid.selectedIndex].status;
          let emptySpace = [];
          emptySpace = status.split(" ");
          let selStatus: string = emptySpace[0];
          if (selStatus != "CONFIRMED" && this.confirmButton) {
            this.confirmButton.enabled = true;
            this.confirmButton.buttonMode = true;
          }
          if (selStatus != "SUSPENDED" && this.suspendButton) {
            this.suspendButton.enabled = true;
            this.suspendButton.buttonMode = true;
          }
          if (this.matchButton) {
            this.matchButton.enabled = true;
            this.matchButton.buttonMode = true;
          }
        }
      } else {
        //no select
        this.messageButton.enabled = false;
        this.messageButton.buttonMode = false;
        this.movementButton.enabled = false;
        this.movementButton.buttonMode = false;
        this.noteButton.enabled = false;
        this.noteButton.buttonMode = false;

        if (this.bottomGrid != null) {
          if (this.bottomGrid.dataProvider.length >= 1) {
            for (let selStatusCount: number = 0; selStatusCount < this.bottomGrid.dataProvider.length; selStatusCount++) {

              selectedStatus = this.bottomGrid.dataProvider[selStatusCount].status;
              if (selectedStatus.split(" ")[0] == "CONFIRMED") {
                statusConfirmFlag = true;
              } else if (selectedStatus.split(" ")[0] == "SUSPENDED") {
                statusSuspendFlag = true;
              } else if (selectedStatus.split(" ")[0] == "OUTSTANDING") {
                statusOutStandingFlag = true;
              }

            }
            if (statusOutStandingFlag == false) {
              if (statusConfirmFlag == false) {
                this.confirmButton.enabled = true;
                this.confirmButton.buttonMode = true;
              } else {
                this.confirmButton.enabled = false;
                this.confirmButton.buttonMode = false;
              }
              if (statusSuspendFlag == false) {
                this.suspendButton.enabled = true;
                this.suspendButton.buttonMode = true;
              } else {
                this.suspendButton.enabled = false;
                this.suspendButton.buttonMode = false;
              }
            } else {
              this.confirmButton.enabled = true;
              this.confirmButton.buttonMode = true;
              this.suspendButton.enabled = true;
              this.suspendButton.buttonMode = true;
            }
            this.matchButton.enabled = true;
            this.matchButton.buttonMode = true;
          } else {
            this.reconButton.enabled = false;
            this.reconButton.buttonMode = false;
            this.confirmButton.enabled = false;
            this.confirmButton.buttonMode = false;
            this.suspendButton.enabled = false;
            this.suspendButton.buttonMode = false;
            this.matchButton.enabled = false;
            this.matchButton.buttonMode = false;
          }
          if (this.bottomGrid.dataProvider.length == 1) {
            this.movementButton.enabled = true;
            this.movementButton.buttonMode = true;
            this.noteButton.enabled = true;
            this.noteButton.buttonMode = true;
            this.messageButton.enabled = true;
            this.messageButton.buttonMode = true;
          } else if (this.bottomGrid.dataProvider.length == 0) {
            this.movementButton.enabled = false;
            this.movementButton.buttonMode = false;
            this.noteButton.enabled = false;
            this.noteButton.buttonMode = false;
            this.messageButton.enabled = false;
            this.messageButton.buttonMode = false;
          }
        }
      }
      var matchFlag: boolean = false;
      let matchId: string = null;
      // Declare the variable to check the matckId which is null
      let matchNullId: boolean = false;
      let openFlag: boolean = false;
      let emptyFlag: boolean = false;
      let nonEmptyFlag: boolean = false;
      if (this.bottomGrid != null && this.bottomGrid.dataProvider.length > 0) {
        for (var i= 0;  i < this.bottomGrid.dataProvider.length; i++) {

          if (this.bottomGrid.dataProvider[i].matchid.toString().length >= 0) {
            nonEmptyFlag = true;
          }
          if (emptyFlag && nonEmptyFlag) {
            openFlag = true;
          }
          if ((matchId == null) && this.bottomGrid.dataProvider[i].matchid != "") {
            matchId = this.bottomGrid.dataProvider[i].matchid;
          } else if (this.bottomGrid.dataProvider[i].matchid != "") {
            if (matchId != this.bottomGrid.dataProvider[i].matchid) {
              matchFlag = true;
              break;
            }
          }
        }
        if (openFlag || emptyFlag) {
          this.matchButton.toolTip = SwtUtil.getPredictMessage('tooltip.makeOfferedMatch', null);
        } else if (!matchFlag && this.bottomGrid.dataProvider.length > 0) {
          this.matchButton.toolTip = SwtUtil.getPredictMessage('tooltip.displayMatch', null);

        } else {
          this.matchButton.toolTip = "";
        }
        if (matchFlag) {
          
          this.matchButton.enabled = false;
          this.matchButton.buttonMode = false;
          this.matchButton.toolTip = "";
          let unlockFlag=ExternalInterface.call("unlockMovementOnServer", this.bottomGrid.dataProvider[i].movement);
          this.swtAlert.warning(SwtUtil.getPredictMessage('label.selectedOnlyMatchedItems', null), 'Warning');
          
        } else if (this.bottomGrid.dataProvider.length >= 1) {
          this.matchButton.enabled = true;
          this.matchButton.buttonMode = true;
        }
      }

      let flag: boolean = false;
      if (this.bottomGrid != null && this.bottomGrid.dataProvider.length > 0) {
        for (let high: number = 0; high < this.bottomGrid.dataProvider.length; high++) {
          if ((this.bottomGrid.dataProvider[high].matchid).length > 0)
            flag = true;
        }
        if (flag) {
          this.reconButton.enabled = false;
          this.reconButton.buttonMode = false;
        } else {
          if (this.bottomGrid.dataProvider.length != 0) {
            this.reconButton.enabled = true;
            this.reconButton.buttonMode = true;
          }
        }
      }

      let lockflag2: Object = new Object();
      let lock2: string = "";
      if (this.bottomGrid != null && this.bottomGrid.dataProvider.length > 0) {
        for (let high: number = 0; high < this.bottomGrid.dataProvider.length; high++) {
          lockflag2 = ExternalInterface.call("accountAccessConfirm", (this.bottomGrid.dataProvider[high].movement).toString(), (this.entityCombo.selectedLabel).toString());
          lock2 = lockflag2.toString();
          if (!BooleanParser.parseBooleanValue(lock2))
            acctFlag = false;
        }
      }
      if (!acctFlag) {
        this.reconButton.enabled = false;
        this.reconButton.buttonMode = false;
        this.confirmButton.enabled = false;
        this.confirmButton.buttonMode = false;
        this.suspendButton.enabled = false;
        this.suspendButton.buttonMode = false;
        this.matchButton.enabled = false;
        this.matchButton.buttonMode = false;
      }
      //This boolean indicates whether the selected movements contain an outstanding movement
      let outStandingFlag: boolean = false;
      
    
      if (this.bottomGrid != null && this.bottomGrid.dataProvider.length > 0) {
        for (let selMatchCount: number=0; selMatchCount <this.bottomGrid.dataProvider.length; selMatchCount++) {
          selectedStatus = this.bottomGrid.dataProvider[selMatchCount].status;
          if (selectedStatus.split(" ")[0] == "OUTSTANDING") {
            outStandingFlag = true;
            break;
          }
        }

        if ((acctFlag && !matchFlag && this.bottomGrid.dataProvider.length > 0)
          || (!acctFlag && !outStandingFlag && !matchFlag)) {
          this.matchButton.enabled = true;
        } else {
          this.matchButton.enabled = false;
        }
      }

      /* let numFormatTotal:any; //NumberFormatter=new NumberFormatter();
       numFormatTotal.precision="2";
       numFormatTotal.rounding="nearest";
   */
      let expandTotalAmt: string = this.addZeroes(totalSelectedAmount.toString());
      if (expandTotalAmt.indexOf(".") == -1) {
        expandTotalAmt += "00";
      } else {
        let expandDiffArr = [];
        expandDiffArr = expandTotalAmt.split(".");
        expandTotalAmt = expandDiffArr[0] + expandDiffArr[1];
      }
      formatedAmount = ExternalInterface.call("expandAmtDifference", expandTotalAmt, ccyFormat, ccyCode);
      if (totalSelectedAmount < 0 && formatedAmount.length > 0 && formatedAmount.charAt(0) != "-") {
        this.totalSelectedValue.text = "-" + ExternalInterface.call("expandAmtDifference", expandTotalAmt, ccyFormat, ccyCode);
      } else {
        this.totalSelectedValue.text = ExternalInterface.call("expandAmtDifference", expandTotalAmt, ccyFormat, ccyCode);
      }
      //Save selected movmeents for filter /refresh grid
      if(this.cGrid.selectedIndices.length > 0) {
        // if (this.bottomGrid.dataProvider.length > 0) {
        this.selectedList = "";
        //this.selectedMvmts = [];
        for (let i = 0; i < this.cGrid.selectedIndices.length; i++) {
          if (this.selectedList == "" || this.selectedList.indexOf(this.cGrid.dataProvider[this.cGrid.selectedIndices[i]].movement) == -1 && this.selectedMvmts.indexOf(this.cGrid.dataProvider[this.cGrid.selectedIndices[i]].movement) == -1) {
            this.selectedMvmts.push(""+this.cGrid.dataProvider[this.cGrid.selectedIndices[i]].movement);
            this.selectedList += this.cGrid.dataProvider[this.cGrid.selectedIndices[i]].movement + ','
          }

        }
        this.selectedMvmts = this.selectedMvmts.filter((elem, i, arr) => {
          if (arr.indexOf(elem) === i) {
            return elem
          }
        })
        //}

      }

      if (this.bottomGrid != null){

      let selectedList ="";
      for(let z: number = 0; z < this.bottomGrid.dataProvider.length; z++ ) {
        selectedList+= this.bottomGrid.dataProvider[z].movement +',';
      }
      ExternalInterface.call('setSelectedMovementForLock',selectedList);

      }
    } catch(e) {
      console.log('errror', e)
    }
    } catch(e) {
      console.log('error in ', e)
    }
  }
   addZeroes(num) {
    const dec = num.split('.')[1]
    const len = 2; // dec && dec.length > 2 ? dec.length : 2;
    return Number(num).toFixed(len)
  }


/* Pagination Methods Starts*/

  /**
   * This function used to done pagination
   * **/
  paginationURLConstructor():string
  {

    this.keepSelected = true;
    let paginationUrl:string="";
    //	let menuAccessId:string="0";
    let applyThresholdFlag:string="";
    //get access status of the screen
    let access:string=this.jsonReader.getScreenAttributes()["access"];
    if (this.currencyThreshold.selected)
    {
      applyThresholdFlag="Y";
    }
    else
    {
      applyThresholdFlag="N";
    }
    let selectedList:string="";
    this.initialinputscreen=this.jsonReader.getScreenAttributes()["initialinputscreen"];
    if (this.methodName != 'search' && this.initialinputscreen == 'E'){
      for(let z: number = 0; z < this.bottomGrid.dataProvider.length; z++ ) {

        selectedList+= this.bottomGrid.dataProvider[z].movement +',';
      }
    } else {
      //restart select mvmts when changing page
      this.selectedMvmts = [];
    }
    paginationUrl+='&selectedList=';
    paginationUrl+=selectedList;
    paginationUrl+='&selectedSort=' + this.jsonReader.getScreenAttributes()["selectedSort"];
    paginationUrl+='&selectedFilter=' + this.jsonReader.getScreenAttributes()["selectedFilter"];
    paginationUrl+='&archiveId=' + ExternalInterface.call('eval', 'archiveIdSearch');
    paginationUrl+='&initialinputscreen=' + this.initialinputscreen;
    paginationUrl+='&fromFlex=' + "true";
    //framing URL based on the parent screen
    if (this.methodName == "search" || this.initialinputscreen != 'E')
    {
      paginationUrl+="&filterAcctType=" + this.jsonReader.getScreenAttributes()["filterAcctType"].toString().replace(/\%/g, "per;");
      if (this.initialscreen == "E")
      {
        paginationUrl+='&method=displayOpenMovements';
      }
      if (access == "readOnly")
      {
        paginationUrl+='&method=next';
      }
      if (access != "readOnly")
      {
        if (this.initialinputscreen == "currencymonitor" || this.initialinputscreen == "entitymonitor")
        {
          paginationUrl+='&valueDate=' + ExternalInterface.call('eval', 'valueDate');
          paginationUrl+='&balanceType=' + "";
          paginationUrl+='&locationId=' + ExternalInterface.call('eval', 'locationIdCurrency');
          paginationUrl+='&method=getCurrencyMonitorMvmnts';
        }
        if (this.initialinputscreen != "currencymonitor" && this.initialinputscreen != "entitymonitor")
        {
          if (this.initialinputscreen == "accountmonitor")
          {
            paginationUrl+='&valueDate=' + ExternalInterface.call('eval', 'valueDate');
            paginationUrl+='&balanceType=' + ExternalInterface.call('eval', 'balanceTypeAccount');
            paginationUrl+='&accountId=' + ExternalInterface.call('eval', 'accountIdAccount');
            paginationUrl+='&accountType=' + ExternalInterface.call('eval', 'accountTypeAccount');
            paginationUrl+='&method=getAccountMonitorMvmnts';
          }
          if (this.initialinputscreen != "accountmonitor")
          {
            if (this.initialinputscreen == "accountbreakdownmonitor")
            {
              paginationUrl+='&valueDate=' + ExternalInterface.call('eval', 'valueDate');
              paginationUrl+='&balanceType=' + ExternalInterface.call('eval', 'balanceTypeAccount');
              paginationUrl+='&accountId=' + ExternalInterface.call('eval', 'accountIdAccount');
              paginationUrl+='&method=getAccountMonitorMvmnts';
            }
            if (this.initialinputscreen != "accountbreakdownmonitor")
            {
              if (this.initialinputscreen == "bookmonitor")
              {
                paginationUrl+='&valueDate=' + ExternalInterface.call('eval', 'valueDate');
                paginationUrl+='&bookCode=' + ExternalInterface.call('eval', 'bookCodeBook');
                paginationUrl+='&method=getBookMonitorMvmnts';
              }
              if (this.initialinputscreen != "bookmonitor")
              {
                if (this.initialinputscreen == "mvmntsfromWorkFlowMonitor")
                {
                  paginationUrl+='&method=getMvmntsfromWorkFlowMonitor';
                  paginationUrl+='&linkFlag=' + ExternalInterface.call('eval', 'linkFlagWorkflow');
                  paginationUrl+='&tabIndicator=' + ExternalInterface.call('eval', 'tabIndicatorWorkflow');
                }
                if (this.initialinputscreen != "mvmntsfromWorkFlowMonitor")
                {
                  if (this.initialinputscreen == "unSettledYesterday")
                  {
                    paginationUrl+='&method=getUnsettledYesterdayMvmnts';
                  }
                  if (this.initialinputscreen != "unSettledYesterday")
                  {
                    if (this.initialinputscreen == "backValuedMvmnts")
                    {
                      paginationUrl+='&method=getBackValuedMvmnts';
                      paginationUrl+='&valueDate=' + ExternalInterface.call('eval', 'valueDate');
                    }
                    if (this.initialinputscreen != "backValuedMvmnts")
                    {
                      if (this.initialinputscreen == "openUnexpectedMvmnts")
                      {
                        paginationUrl+='&method=getOpenUnexpectedMvmnts';
                        paginationUrl+='&valueDate=' + ExternalInterface.call('eval', 'valueDate');
                      }
                      if (this.initialinputscreen != "openUnexpectedMvmnts")
                      {
                        paginationUrl+='&method=searchnext';
                        let filterCriteria:string="";
                        filterCriteria="entityId=" + ExternalInterface.call('eval', 'entityIdSearch') +

                          "|movementType=" + ExternalInterface.call('eval', 'movementTypeSearch') + "|sign=" + ExternalInterface.call('eval', 'signSearch') + "|predictStatus=" + ExternalInterface.call('eval', 'predictStatusSearch') + "|amountover=" + ExternalInterface.call('eval', 'amountoverSearch') + "|amountunder=" + ExternalInterface.call('eval', 'amountunderSearch') + "|currencyCode=" + ExternalInterface.call('eval', 'currencyCode') + "|paymentChannelId=" + ExternalInterface.call('eval', 'paymentChannelIdSearch') + "|beneficiaryId=" + ExternalInterface.call('eval', 'beneficiaryIdSearch') + "|custodianId=" + ExternalInterface.call('eval', 'custodianIdSearch') + "|positionlevel=" + ExternalInterface.call('eval', 'positionlevelSearch') + "|accountId=" + ExternalInterface.call('eval', 'accountIdSearch') + "|group=" + ExternalInterface.call('eval', 'groupSearch') + "|metaGroup=" + ExternalInterface.call('eval', 'metaGroupSearch') + "|bookCode=" + ExternalInterface.call('eval', 'bookCodeSearch') + "|valueFromDateAsString=" + ExternalInterface.call('eval', 'valueFromDateAsStringSearch') + "|valueToDateAsString=" + ExternalInterface.call('eval', 'valueToDateAsStringSearch') + "|timefrom=" + ExternalInterface.call('eval', 'timefromSearch') + "|timeto=" + ExternalInterface.call('eval', 'timetoSearch') + "|reference=" + ExternalInterface.call('eval', 'referenceSearch') + "|messageId=" + ExternalInterface.call('eval', 'messageIdSearch') + "|inputDateAsString=" + ExternalInterface.call('eval', 'inputDateAsStringSearch') + "|counterPartyId=" + ExternalInterface.call('eval', 'counterPartyIdSearch') +

                          "|matchStatus=" + ExternalInterface.call('eval', 'matchStatusSearch') + "|initialinputscreen=" + this.jsonReader.getScreenAttributes()["initialinputscreen"] +

                          "|accountClass=" + ExternalInterface.call('eval', 'accountClassSearch') +  "|producttype=" + ExternalInterface.call('eval', 'producttype') + "|uetr=" + ExternalInterface.call('eval', 'uetr') + "|matchingparty=" + ExternalInterface.call('eval', 'matchingparty') + "|postingDateFrom=" + ExternalInterface.call('eval', 'postingDateFrom') + "|postingDateTo=" + ExternalInterface.call('eval', 'postingDateTo') + "|positionlevel=" + ExternalInterface.call('eval', 'positionlevelSearch');

                        paginationUrl+='&filterCriteria=' + filterCriteria;
                        paginationUrl += "&extraFilter=" + ExternalInterface.call('eval', 'extraFilter');
                        paginationUrl+="&referenceFlag=" + ExternalInterface.call('eval', 'referenceFlagSearch');
                        paginationUrl+="&openFlag=" + ExternalInterface.call('eval', 'openFlag');
                        // Added for mantis 1443, send the scenario id to get movements with its query
                        if (ExternalInterface.call('eval', 'scenarioId') != null){
                          paginationUrl+="&scenarioId=" + ExternalInterface.call('eval', 'scenarioId');
                          paginationUrl+="&currGrp=" + ExternalInterface.call('eval', 'currGrp');

                        }
                      }
                    }
                  }
                }
              }
            }
          }
        }
      }
      paginationUrl+="&extBalStatus=" + ExternalInterface.call('eval', 'extBalStatusSearch');
    }
    if (this.methodName != "search")// && this.initialinputscreen == "E"
    {
      if (this.initialinputscreen == "filterscreen") // TOCheck  if (this.initialinputscreen == "filterscreen")
      {
        paginationUrl+='&amountover=' + ExternalInterface.call('amountOver');
        paginationUrl+='&amountunder=' + ExternalInterface.call('amountUnder');
        paginationUrl+='&group=' + ExternalInterface.call('getGroup');
        paginationUrl+='&metaGroup=' + ExternalInterface.call('getMetagroup');
        //paginationUrl+='&valueFromDate=' + ExternalInterface.call('valueFromDate');
        // Added by Imed B on 30/04/2012
        if (ExternalInterface.call('valueFromDate') == ""){
          paginationUrl+='&valueFromDate=' + ExternalInterface.call('eval', 'tabPressed');
        }else
          paginationUrl+='&valueFromDate=' + ExternalInterface.call('valueFromDate');
        //End Added by Imed B on 30/04/2012

        paginationUrl+='&valueToDate=' + ExternalInterface.call('valueToDate');
        paginationUrl+='&timefrom=' + ExternalInterface.call('timefrom');
        paginationUrl+='&timeto=' + ExternalInterface.call('timeto');
        paginationUrl+='&inputDate=' + ExternalInterface.call('inputDate');
        paginationUrl+='&reference=' + ExternalInterface.call('reference');
        paginationUrl+='&refFlagFilterSearch=' + ExternalInterface.call('referenceFlag');
        paginationUrl+='&openMovementFlagSearch=' + ExternalInterface.call('openMovementFlag');// Added by KaisBS for mantis 1756
        paginationUrl+='&totalFlag=' + ExternalInterface.call('eval', 'totalFlagOpenMovements');
        paginationUrl+='&method=nextfilterOutMovementSummary';
        if (ExternalInterface.call('eval', 'workflow') != null)
          paginationUrl+="&workflow=" + ExternalInterface.call('eval', 'workflow');

      }
      if (this.initialinputscreen != "filterscreen") //TOCheck if (this.initialinputscreen != "filterscreen")
      {
        paginationUrl+='&method=displayOpenMovements';
        paginationUrl+='&initialinputscreen=' + this.initialscreen;
        paginationUrl+="&filterAcctType=" + this.jsonReader.getScreenAttributes()["filterAcctType"].toString().replace(/\%/g, "per;");
      }
    }
    paginationUrl+='&menuAccessId=' + this.menuAccessId;
    paginationUrl+='&applyCurrencyThreshold=' + applyThresholdFlag;
    paginationUrl+='&totalFlag=' + ExternalInterface.call('eval', 'totalFlagOpenMovements');
    paginationUrl+='&openMovementFlagSearch=' + ExternalInterface.call('openMovementFlag');
    paginationUrl+='&isAmountDiffer=' + this.diffText.text;
    paginationUrl+='&selectedMovementsAmount=';
    paginationUrl+="&currentFilterConf=" + Encryptor.encode64(this.currentFilterConf);
    paginationUrl+="&extraFilter=" + ExternalInterface.call('eval', 'extraFilter');
    return paginationUrl;
  }
  private isPageChanged: boolean = false
  numpager(event):void
  {
    this.isPageChanged = true;
    if (this.jsonReader.getScreenAttributes()["pages"].currentPage != this.numStepper.value)
    {
      //Start :Added by Imed B on 04/30/2012 for Issue 1054_SEL_161,Updated on 23/05/2012
      if(isNaN(this.numStepper.value))
      {
        this.numStepper.value=this.jsonReader.getScreenAttributes()["pages"].currentPage;
        this.swtAlert.warning('Please enter a valid page number'); //(ExternalInterface.call('getBundle', 'text', 'label-validPageNumber', 'Please enter a valid page number'), ExternalInterface.call('getBundle', 'text', 'alert-error', 'Error'));
      }
      //End :Added by Imed B on 04/30/2012 for Issue 1054_SEL_161
      else
      {
        let url:string= this.paginationURLConstructor();
        this.pageUrl="maxPage=" + this.jsonReader.getScreenAttributes()["pages"].maxPage +
          "&matchStatus=" + ExternalInterface.call('eval', 'matchStatusWorkflow') +
          "&predictStatus=" + ExternalInterface.call('eval', 'predictStatusWorkflow') +
          "&currencyCode=" + ExternalInterface.call('eval', 'currencyCode') +
          "&totalCount=" + this.jsonReader.getRowSize() +
          "&roleId=" + ExternalInterface.call('eval', 'roleId') +
          "&date=" + ExternalInterface.call('eval', 'dateOpenMovements') + "" +
          "&pageNoValue=" + this.numStepper.value +
          "&posLvlId=" + ExternalInterface.call('eval', 'posLvlId') +
          "&currGrp=" + ExternalInterface.call('eval', 'currGrp') +
          "&currentPage=" + this.jsonReader.getScreenAttributes()["pages"].currentPage +
          "&entityId=" + ExternalInterface.call('eval', 'entityIdOpenMovements')+
          '&openMovementFlagSearch=' + ExternalInterface.call('openMovementFlag');
        if (ExternalInterface.call('eval', 'workflow') != null)
          this.pageUrl+="&workflow=" + ExternalInterface.call('eval', 'workflow');


        this.inputData.url=this.baseURL + this.actionPath + this.pageUrl + url;
        this.inputData.send(this.requestParams);
        //this.cGrid.selectedIndex = -1; //FIXME
      }
    }
  }



  /**
   * This function is called by when on click of refresh button
   **/
  private keepSelected = false;

  dataRefresh(cameFrom:string = null):void
  {
    this.actionPath="outstandingmovement.do?";
    this.requestParams = [];
    if (this.method == 'search' || (this.initialinputscreen != 'E'))
    {
      this.actionMethod="method=searchrefreshMvmntsSummaryDisplay";
      this.actionMethod+="&openFlag=" + ExternalInterface.call('eval', 'openFlag');
      // Added for mantis 1443, send the scenario id to get movements with its query
      if (ExternalInterface.call('eval', 'scenarioId') != null){
        this.actionMethod+="&scenarioId=" + ExternalInterface.call('eval', 'scenarioId');
        this.actionMethod+="&currGrp=" + ExternalInterface.call('eval', 'currGrp');

      }

    }
    else
    {
      this.actionMethod="method=refreshMvmntsSummaryDisplay";
      this.actionMethod+="&openFlag=" + ExternalInterface.call('eval', 'openFlag');

    }
    let selectedList:string="";

    if (this.methodName != "search" && this.initialinputscreen == 'E')
    {
      for (let i=0; i <this.bottomGrid.dataProvider.length; i++)
      {
        selectedList+=this.bottomGrid.dataProvider[i].movement + ",";
      }
    }
    if (cameFrom == "filterCombo")
    {
      this.actionMethod+="&currentPage=1";
    }
    else
    {
      this.actionMethod+="&currentPage=" + this.jsonReader.getScreenAttributes()["pages"].currentPage;
    }
    this.actionMethod+="&selectedList=" + selectedList;
    this.actionMethod+="&applyCurrencyThresholdInd=1";
    this.actionMethod+="&filterAcctType=" + this.jsonReader.getScreenAttributes()["filterAcctType"].toString().replace(/\%/g, "per;");

    if (this.currencyThreshold.selected)
    {
      this.actionMethod+="&applyCurrencyThreshold=N";
    }
    else
    {
      this.actionMethod+="&applyCurrencyThreshold=Y";
    }
    this.actionMethod+="&entityId=" + this.entityCombo.selectedLabel;
    this.actionMethod+="&currencyCode=" + ExternalInterface.call('eval', 'currencyCode');
    this.actionMethod+="&date=" + ExternalInterface.call('eval', 'dateStr');
    this.actionMethod+='&selectedSort=' + this.jsonReader.getScreenAttributes()["selectedSort"];
    this.actionMethod+='&selectedFilter=' + this.jsonReader.getScreenAttributes()["selectedFilter"];

    this.actionMethod+="&posLvlId=" + ExternalInterface.call('eval', 'posLvlId');
    let access:string=this.jsonReader.getScreenAttributes()["access"];
    this.initialinputscreen= this.jsonReader.getScreenAttributes()["initialinputscreen"];
    if ((this.initialinputscreen == 'filterscreen') && ExternalInterface.call('eval', 'initialscreen') == "E")
    {
      this.initialinputscreen='E';
    }
    this.actionMethod+="&initialinputscreen=" + this.initialinputscreen;
    if (this.initialinputscreen == 'E')
      this.actionMethod+="&totalFlag=" + ExternalInterface.call('eval', 'totalFlagOpenMovements');
    if (ExternalInterface.call('eval', 'workflow') != null)
      this.actionMethod+="&workflow=" + ExternalInterface.call('eval', 'workflow');
    if (this.methodName == "search" || this.initialinputscreen != 'E')
    {
      this.actionMethod+="&maxPage=" + this.jsonReader.getScreenAttributes()["pages"].maxPage;
      this.actionMethod+="&totalCount=" + this.jsonReader.getRowSize();
      this.actionMethod+="&extBalStatus=" + ExternalInterface.call('eval', 'extBalStatusSearch');
      if (access != "readOnly")
      {

        if (this.initialinputscreen == "currencymonitor" || this.initialinputscreen == "entitymonitor")
        {
          this.actionMethod+="&valueDate=" + ExternalInterface.call('eval', 'valueDate');
          this.actionMethod+="&balanceType=";
          this.actionMethod+="&locationId=" + ExternalInterface.call('eval', 'locationIdCurrency');
        }
        if (this.initialinputscreen != "currencymonitor" && this.initialinputscreen != "entitymonitor")
        {
          if (this.initialinputscreen == "accountmonitor")
          {
            this.actionMethod+="&valueDate=" + ExternalInterface.call('eval', 'valueDate');
            this.actionMethod+="&balanceType=" + ExternalInterface.call('eval', 'balanceTypeAccount');
            this.actionMethod+="&accountId=" + ExternalInterface.call('eval', 'accountIdAccount');
            this.actionMethod+="&accountType=" + ExternalInterface.call('eval', 'accountTypeAccount');
          }
          if (this.initialinputscreen != "accountmonitor")
          {
            if (this.initialinputscreen == "accountbreakdownmonitor")
            {
              this.actionMethod+="&valueDate=" + ExternalInterface.call('eval', 'valueDate');
              this.actionMethod+="&balanceType=" + ExternalInterface.call('eval', 'balanceTypeAccount');
              this.actionMethod+="&accountId=" + ExternalInterface.call('eval', 'accountIdAccount');
            }
            if (this.initialinputscreen != "accountbreakdownmonitor")
            {
              if (this.initialinputscreen == "bookmonitor")
              {
                this.actionMethod+="&valueDate=" + ExternalInterface.call('eval', 'valueDate');
                this.actionMethod+="&bookCode=" + ExternalInterface.call('eval', 'bookCodeBook');
              }
              if (this.initialinputscreen != "bookmonitor")
              {
                if (this.initialinputscreen == "mvmntsfromWorkFlowMonitor")
                {
                  this.actionMethod+="&valueDate=" + ExternalInterface.call('eval', 'valueDate');
                  this.actionMethod+="&tabIndicator=" + ExternalInterface.call('eval', 'tabIndicatorWorkflow');
                  this.actionMethod+="&currGrp=" + ExternalInterface.call('eval', 'currGrp');
                  this.actionMethod+="&roleId=" + ExternalInterface.call('eval', 'roleId');
                  this.actionMethod+="&matchStatus=" + ExternalInterface.call('eval', 'matchStatusWorkflow');
                  this.actionMethod+="&predictStatus=" + ExternalInterface.call('eval', 'predictStatusWorkflow');
                  this.actionMethod+="&linkFlag=" + ExternalInterface.call('eval', 'linkFlagWorkflow');
                }
                if (this.initialinputscreen != "mvmntsfromWorkFlowMonitor")
                {
                  if (this.initialinputscreen == "unSettledYesterday")
                  {
                    this.actionMethod+="&valueDate=" + "";
                    this.actionMethod+="&currGrp=" + ExternalInterface.call('eval', 'currGrp');
                    this.actionMethod+="&roleId=" + ExternalInterface.call('eval', 'roleId');
                  }
                  if (this.initialinputscreen != "unSettledYesterday")
                  {
                    if (this.initialinputscreen == "backValuedMvmnts")
                    {
                      this.actionMethod+="&valueDate=" + ExternalInterface.call('eval', 'valueDate');
                      this.actionMethod+="&currGrp=" + ExternalInterface.call('eval', 'currGrp');
                      this.actionMethod+="&roleId=" + ExternalInterface.call('eval', 'roleId');
                    }
                    if (this.initialinputscreen != "backValuedMvmnts")
                    {
                      if (this.initialinputscreen == "openUnexpectedMvmnts")
                      {
                        this.actionMethod+="&valueDate=" + ExternalInterface.call('eval', 'valueDate');
                        this.actionMethod+="&currGrp=" + ExternalInterface.call('eval', 'currGrp');
                        this.actionMethod+="&roleId=" + ExternalInterface.call('eval', 'roleId');
                      }
                      if (this.initialinputscreen != "openUnexpectedMvmnts")
                      {
                        this.actionMethod+="&archiveId=" + ExternalInterface.call('eval', 'archiveIdSearch');
                        this.actionMethod+="&entityId=" + ExternalInterface.call('eval', 'entityIdSearch') + "&movementType=" + ExternalInterface.call('eval', 'movementTypeSearch') + "&sign=" + ExternalInterface.call('eval', 'signSearch') + "&predictStatus=" + ExternalInterface.call('eval', 'predictStatusSearch') + "&amountover=" + ExternalInterface.call('eval', 'amountoverSearch') + "&amountunder=" + ExternalInterface.call('eval', 'amountunderSearch') + "&currencyCode=" + ExternalInterface.call('eval', 'currencyCode') + "&paymentChannelId=" + ExternalInterface.call('eval', 'paymentChannelIdSearch') + "&beneficiaryId=" + ExternalInterface.call('eval', 'beneficiaryIdSearch') + "&custodianId=" + ExternalInterface.call('eval', 'custodianIdSearch') + "&positionlevel=" + ExternalInterface.call('eval', 'positionlevelSearch') + "&accountId=" + ExternalInterface.call('eval', 'accountIdSearch') + "&group=" + ExternalInterface.call('eval', 'groupSearch') + "&metaGroup=" + ExternalInterface.call('eval', 'metaGroupSearch') + "&bookCode=" + ExternalInterface.call('eval', 'bookCodeSearch') + "&valueFromDateAsString=" + ExternalInterface.call('eval', 'valueFromDateAsStringSearch') + "&valueToDateAsString=" + ExternalInterface.call('eval', 'valueToDateAsStringSearch') + "&timefrom=" + ExternalInterface.call('eval', 'timefromSearch') + "&timeto=" + ExternalInterface.call('eval', 'timetoSearch') + "&reference=" + ExternalInterface.call('eval', 'referenceSearch') + "&messageId=" + ExternalInterface.call('eval', 'messageIdSearch') + "&inputDateAsString=" + ExternalInterface.call('eval', 'inputDateAsStringSearch') + "&counterPartyId=" + ExternalInterface.call('eval', 'counterPartyIdSearch') +

                          "&matchStatus=" + ExternalInterface.call('eval', 'matchStatusSearch') + "&initialinputscreen=" + ""; // this.jsonReader.getScreenAttributes()["initialinputscreen"]
                        + "&referenceFlag=" + ExternalInterface.call('eval', 'referenceFlagSearch') + "&accountClass=" + ExternalInterface.call('eval', 'accountClassSearch');
                        let filterCriteria:string="";
                        filterCriteria="entityId=" + ExternalInterface.call('eval', 'entityIdSearch') +

                          "|movementType=" + ExternalInterface.call('eval', 'movementTypeSearch') + "|sign=" + ExternalInterface.call('eval', 'signSearch') + "|predictStatus=" + ExternalInterface.call('eval', 'predictStatusSearch') + "|amountover=" + ExternalInterface.call('eval', 'amountoverSearch') + "|amountunder=" + ExternalInterface.call('eval', 'amountunderSearch') + "|currencyCode=" + ExternalInterface.call('eval', 'currencyCode') + "|paymentChannelId=" + ExternalInterface.call('eval', 'paymentChannelIdSearch') + "|beneficiaryId=" + ExternalInterface.call('eval', 'beneficiaryIdSearch') + "|custodianId=" + ExternalInterface.call('eval', 'custodianIdSearch') + "|positionlevel=" + ExternalInterface.call('eval', 'positionlevelSearch') + "|accountId=" + ExternalInterface.call('eval', 'accountIdSearch') + "|group=" + ExternalInterface.call('eval', 'groupSearch') + "|metaGroup=" + ExternalInterface.call('eval', 'metaGroupSearch') + "|bookCode=" + ExternalInterface.call('eval', 'bookCodeSearch') + "|valueFromDateAsString=" + ExternalInterface.call('eval', 'valueFromDateAsStringSearch') + "|valueToDateAsString=" + ExternalInterface.call('eval', 'valueToDateAsStringSearch') + "|timefrom=" + ExternalInterface.call('eval', 'timefromSearch') + "|timeto=" + ExternalInterface.call('eval', 'timetoSearch') + "|reference=" + ExternalInterface.call('eval', 'referenceSearch') + "|messageId=" + ExternalInterface.call('eval', 'messageIdSearch') + "|inputDateAsString=" + ExternalInterface.call('eval', 'inputDateAsStringSearch') + "|counterPartyId=" + ExternalInterface.call('eval', 'counterPartyIdSearch') + "|fintrade=" + ExternalInterface.call('eval', 'fintradeSearch') + "|matchStatus=" + ExternalInterface.call('eval', 'matchStatusSearch') + "|initialinputscreen=" + this.jsonReader.getScreenAttributes()["initialinputscreen"] + "|accountClass=" + ExternalInterface.call('eval', 'accountClassSearch')
                          + "|producttype=" + ExternalInterface.call('eval', 'producttype') + "|uetr=" + ExternalInterface.call('eval', 'uetr') + "|matchingparty=" + ExternalInterface.call('eval', 'matchingparty') + "|postingDateFrom=" + ExternalInterface.call('eval', 'postingDateFrom') + "|postingDateTo=" + ExternalInterface.call('eval', 'postingDateTo') + "|positionlevel=" + ExternalInterface.call('eval', 'positionlevelSearch');
                        this.actionMethod+="&filterCriteria=" + filterCriteria;
                        this.actionMethod+="&filterFromSerach=" + ExternalInterface.call('eval', 'filterFromSerachSearch');
                        this.actionMethod+="&matchingparty=" + ExternalInterface.call('eval', 'matchingparty') + "&producttype=" + ExternalInterface.call('eval', 'producttype') +"&uetr=" + ExternalInterface.call('eval', 'uetr') + "&postingDateFrom=" + ExternalInterface.call('eval', 'postingDateFrom') + "&postingDateTo=" + ExternalInterface.call('eval', 'postingDateTo');


                      }
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
    //Code Modified for Mantis 1598 by sudhakar on 1-12-2011: Movement Summary Display' screen returns 'Connection Error
    this.actionMethod+="&tableScrollbarLeft=" + "&tableScrollbarTop=" + "&scrollbarLeft=" + "&scrollbarTop=" + "&isAmountDiffer=" + "&selectedMovementsAmount=" + "&menuAccessId=" + this.menuAccessId+'&openMovementFlagSearch=' + ExternalInterface.call('openMovementFlag');
    this.actionMethod += "&extraFilter=" + ExternalInterface.call('eval', 'extraFilter');
    this.requestParams["currentFilterConf"] = Encryptor.encode64(this.currentFilterConf);
    this.keepSelected=true;
    //framing final url
    this.inputData.url=this.baseURL + this.actionPath + this.actionMethod;
    //sending request
    this.inputData.send(this.requestParams);
  }
  /**This function is used to load the retrieved datas from xml using the this.jsonReader and process it
   * The Column datas are retieved and rendered using MSDGrid and are located into the display
   * containes.
   * The totals grid is populated for Book montor and updated with column data so that the Total
   * row will be made visible for the Book monitor
   * */


  inputDataResult(event):void
  {
    let sortDir:boolean=false;
    let sortDirection:string=null;
    let totalSelectedAmount:number = 0;
    let formatedAmount:string = "";
    /*Condition to check the inputData HTTPcomms is busy ie to ensure the data load*/
    if (this.inputData.isBusy())
    {
      this.inputData.cbStop();
    }
    else
    {
      /*Retrieves the XML from ResultEvent*/
      this.lastRecievedJSON=event;
      /*Pass the XML received to this.jsonReader*/
      this.jsonReader.setInputJSON(this.lastRecievedJSON);
      this.entityId= this.entityCombo.selectedLabel;
      this.currentFilterConf = this.jsonReader.getScreenAttributes()["currentFilterConf"];
      if (this.jsonReader.getScreenAttributes()["filterAcctType"].toString().replace(/\%/g, "per;") != "")
      {
        this.updateValues(Encryptor.decode64(this.jsonReader.getScreenAttributes()["filterAcctType"].toString().replace(/\%/g, "per;")));
      }
      else
      {
        this.initializeValues();
      }
      if (this.jsonReader.getScreenAttributes()["nodata"] == 'Y')
      {
        this.dataRefresh(null);
        return;
      }
      this.lastRefTimeLabel.visible=true;
      this.lastRefTime.visible=true;
      //BEGIN: added by KaisBS to disappear the Connection Error message once the connection is resumed
      this.lastRefTimeLabel.text=ExternalInterface.call('getBundle', 'text', 'label-lastRefresh', 'Last Refresh:');
      this.lastRefTimeLabel.setStyle("color","black", this.lastRefTimeLabel.domElement);
      //END: added by KaisBS to disappear the Connection Error message once the connection is resumed
      this.lastRefTime.text=this.jsonReader.getScreenAttributes()["lastRefTime"];

      this.totalOverPagesValue.text=this.jsonReader.getScreenAttributes()["totalOverPages"];
      this.totalInPageValue.text=this.jsonReader.getScreenAttributes()["totalInPage"];
      let ccyFormat:string=this.jsonReader.getScreenAttributes()["currencyFormat"];
      /*Enabling export container*/
      if (this.jsonReader.getRowSize() != 0)
      {
        this.exportContainer.enabled=true;
      }
      else
      {
        this.exportContainer.enabled=false;
      }
      /*Check the requst reply status in XML*/
      if (this.jsonReader.getRequestReplyStatus())
      {
        /* Condition to check the last received and previous xml to build grid data.*/
        if (this.lastRecievedJSON != this.prevRecievedJSON)
        {
          /*Get and set the profile from the XML received*/
          this.profilesList= this.jsonReader.getSelects().select[1].option;
          this.currentProfile= this.jsonReader.getScreenAttributes()["currentProfile"];
          this.savedProfileId= this.jsonReader.getScreenAttributes()["currentProfile"];
          this.useAddColsCheck= this.jsonReader.getScreenAttributes()["useAddCols"];
          this.profileAddCols=  this.jsonReader.getScreenAttributes()["profileAddColsList"]?
          (this.jsonReader.getScreenAttributes()["profileAddColsList"]).replace('[','').replace(']','').split(","):[];
          this.addColumnsGridData= this.jsonReader.getScreenAttributes()["addColsForFilter"]?JSON.parse(this.jsonReader.getScreenAttributes()["addColsForFilter"]):"";   
          let maxPage = this.jsonReader.getScreenAttributes()["pages"].maxPage;     
          /*Get and set the entity from the XML received*/
          this.entityCombo.setComboData(this.jsonReader.getSelects());
          this.filterComboMSD.setComboData(this.jsonReader.getSelects());

          if (this.currentFilterConf == this.adhocLabel)
          {
            this.filterComboMSD.setPrompt(this.adhocLabel);
            this.filterComboMSD.selectedIndex = -1;
            this.filterComboMSD.text = "";
          }
          this.lastSelectedIndex = this.filterComboMSD.selectedIndex;
          this.enableDisableFilterCombo();
          this.selectedEntity.text=this.entityCombo.selectedValue;
          this.currencyThreshold.selected= this.jsonReader.getScreenAttributes()["currencythreshold"] ;
          this.initialscreen=this.jsonReader.getScreenAttributes()["initialinputscreen"];
          this.initialinputscreen=this.jsonReader.getScreenAttributes()["initialinputscreen"];
          this.currentFontSize=  this.jsonReader.getScreenAttributes()["currfontsize"];
          /*Condition to check this.jsonReader databuilding*/
          if (!this.jsonReader.isDataBuilding())
          {
            //TODO check this
            //this.dataGridContainer.removeAllChildren();
            //this.paginationData.removeAllElements();

            /*this.exportContainer.maxPages=ExternalInterface.call('eval', 'exportMaxPages');
            exportContainer.totalPages=this.jsonReader.getScreenAttributes()["maxPage"];
            exportContainer.currentPage=this.jsonReader.getScreenAttributes()["pages"].currentPage;*/

            /*Assigning the cGRid to appropriate container*/
            /****we will use events instead of these 2 lines */
            //this.cGrid.colWidthURL ( this.baseURL + "outstandingmovement.do?");
            //this.cGrid.colOrderURL ( this.baseURL + "outstandingmovement.do?");
            this.cGrid.entityID = this.entityCombo.selectedLabel;
            this.cGrid.saveColumnOrder = true;
            this.cGrid.saveWidths = true;
            let methodName:string=ExternalInterface.call('eval', 'methodName');
            const obj = {columns: this.jsonReader.getColumnData()};
            
        //Multiselect filter
          //Value Date: not always filtered
          const valueColumn = obj.columns.column.find(column => column.dataelement === "value");
          if (valueColumn) {
            // Get the data provider with original values
            const valueDateDataProvider = this.getDataProvider(this.jsonReader.getGridData().row, "value");
        
            // Sort the values in the data provider
            const sortedValues = valueDateDataProvider.sort((a, b) => {
                // Special handling for "(EMPTY)" and "(NOT EMPTY)" cases
                if (a.value === "(EMPTY)") return -1;
                if (b.value === "(EMPTY)") return 1;
                if (a.value === "(NOT EMPTY)") return -1;
                if (b.value === "(NOT EMPTY)") return 1;
        
                // Handle null/undefined cases
                if (!a.value) return 1;
                if (!b.value) return -1;
        
                // Helper function to convert the date string
                const convertDate = (dateStr) => {
                    if (typeof dateStr !== 'string') return '';
                    return dateStr.includes(' ') ? 
                        this.convertToIsoWithTime(dateStr) : 
                        this.convertToIso(dateStr);
                };
        
                const isoDateA = convertDate(a.value);
                const isoDateB = convertDate(b.value);
        
                return isoDateA.localeCompare(isoDateB);
            });
        
            // Set column properties with sorted data provider
            valueColumn['FilterType'] = 'MultipleSelect';
            valueColumn['dataProvider'] = sortedValues;
            valueColumn['FilterInputSearch'] = true;
        }
         // account ID
          const accountIdColumn = obj.columns.column.find(column => column.dataelement === "account");
          if (accountIdColumn) {
            const accountIdDataProvider = this.getDataProvider(this.jsonReader.getGridData().row, "account",false);
            accountIdColumn['FilterType'] = 'MultipleSelect'
            accountIdColumn['dataProvider'] = accountIdDataProvider;
            accountIdColumn['FilterInputSearch'] = true;
          }

          // currency
          const ccyColumn = obj.columns.column.find(column => column.dataelement === "ccy");
          if (ccyColumn) {
            const ccyDataProvider = this.getDataProvider(this.jsonReader.getGridData().row, "ccy", false);
            ccyColumn['FilterType'] = 'MultipleSelect'
            ccyColumn['dataProvider'] = ccyDataProvider;
            ccyColumn['FilterInputSearch'] = false;
          }

            // entity
            if(this.entityCombo.selectedLabel=="All"){

            const entityList = ExternalInterface.call('eval', 'entityList') ? JSON.parse(ExternalInterface.call('eval', 'entityList')) : [];
            const entityColumn = obj.columns.column.find(column => column.dataelement === "entity");
            const entityDataProvider = this.prepareDataProvider(entityList);
            entityColumn['FilterType'] = 'MultipleSelect'
            entityColumn['dataProvider'] = entityDataProvider;
            entityColumn['FilterInputSearch'] = false;

            }else{
            const entityColumn = obj.columns.column.find(column => column.dataelement === "entity");
            if (entityColumn) {
              const entityDataProvider = this.getDataProvider(this.jsonReader.getGridData().row, "entity",false);
              entityColumn['FilterType'] = 'MultipleSelect'
              entityColumn['dataProvider'] = entityDataProvider;
              entityColumn['FilterInputSearch'] = true;
            }
          }
          // book code
          const bookCodeColumn = obj.columns.column.find(column => column.dataelement === "book");
          if (bookCodeColumn) {
            const bookCodeDataProvider = this.getDataProvider(this.jsonReader.getGridData().row, "book");
            bookCodeColumn['FilterType'] = 'MultipleSelect'
            bookCodeColumn['dataProvider'] = bookCodeDataProvider;
            bookCodeColumn['FilterInputSearch'] = true;
          }

          // cparty
          const cpartyColumn = obj.columns.column.find(column => column.dataelement === "cparty");
          if (cpartyColumn) {
            const cpartyDataProvider = this.getDataProvider(this.jsonReader.getGridData().row, "cparty");
            cpartyColumn['FilterType'] = 'MultipleSelect'
            cpartyColumn['dataProvider'] = cpartyDataProvider;
            cpartyColumn['FilterInputSearch'] = true;
          }

          // beneficiary
          const beneficiaryColumn = obj.columns.column.find(column => column.dataelement === "beneficiary");
          if (beneficiaryColumn) {
            const beneficiaryDataProvider = this.getDataProvider(this.jsonReader.getGridData().row, "beneficiary");
            beneficiaryColumn['FilterType'] = 'MultipleSelect'
            beneficiaryColumn['dataProvider'] = beneficiaryDataProvider;
            beneficiaryColumn['FilterInputSearch'] = true;
          }

          // custodian
          const custodianColumn = obj.columns.column.find(column => column.dataelement === "custodian");
          if (custodianColumn) {
            const custodianDataProvider = this.getDataProvider(this.jsonReader.getGridData().row, "custodian");
            custodianColumn['FilterType'] = 'MultipleSelect'
            custodianColumn['dataProvider'] = custodianDataProvider;
            custodianColumn['FilterInputSearch'] = true;
          }

          // matchingparty
          const matchingPartyColumn = obj.columns.column.find(column => column.dataelement === "matchingparty");
          if (matchingPartyColumn) {
            const matchingPartyDataProvider = this.getDataProvider(this.jsonReader.getGridData().row, "matchingparty");
            matchingPartyColumn['FilterType'] = 'MultipleSelect'
            matchingPartyColumn['dataProvider'] = matchingPartyDataProvider;
            matchingPartyColumn['FilterInputSearch'] = true;
          }

          // producttype
          const productTypeColumn = obj.columns.column.find(column => column.dataelement === "producttype");
          if (productTypeColumn) {
            const productTypeDataProvider = this.getDataProvider(this.jsonReader.getGridData().row, "producttype");
            productTypeColumn['FilterType'] = 'MultipleSelect'
            productTypeColumn['dataProvider'] = productTypeDataProvider;
            productTypeColumn['FilterInputSearch'] = true;
          }

            // postingdate

          const postDateColumn = obj.columns.column.find(column => column.dataelement === "postingdate");

          if (postDateColumn) {
                // Get the data provider with original values
            const postDateDataProvider = this.getDataProvider(this.jsonReader.getGridData().row, "postingdate");
                
                // Sort the values in the data provider
                const sortedValues = postDateDataProvider.sort((a, b) => {
                    // Special handling for "(EMPTY)" and "(NOT EMPTY)" cases
                    if (a.value === "(EMPTY)") return -1;
                    if (b.value === "(EMPTY)") return 1;
                    if (a.value === "(NOT EMPTY)") return -1;
                    if (b.value === "(NOT EMPTY)") return 1;
                    
                    // Handle null/undefined cases
                    if (!a.value) return 1;
                    if (!b.value) return -1;
                    
                    // Helper function to convert the date string
                    const convertDate = (dateStr) => {
                        if (typeof dateStr !== 'string') return '';
                        return dateStr.includes(' ') ? 
                            this.convertToIsoWithTime(dateStr) : 
                            this.convertToIso(dateStr);
                    };
                    
                    const isoDateA = convertDate(a.value);
                    const isoDateB = convertDate(b.value);
                    
                    return isoDateA.localeCompare(isoDateB);
                });
                
                // Set column properties with sorted data provider
                postDateColumn['FilterType'] = 'MultipleSelect';
                postDateColumn['dataProvider'] = sortedValues;
            postDateColumn['FilterInputSearch'] = true;
          }

          // matchid
          const matchIdColumn = obj.columns.column.find(column => column.dataelement === "matchid");
          if (matchIdColumn) {
            const matchIdDataProvider = this.getDataProvider(this.jsonReader.getGridData().row, "matchid");
            matchIdColumn['FilterType'] = 'MultipleSelect'
            matchIdColumn['dataProvider'] = matchIdDataProvider;
            matchIdColumn['FilterInputSearch'] = true;
          }




          // extra_text1
          const extraTextColumn = obj.columns.column.find(column => column.dataelement === "extra_text1");
          if (extraTextColumn) {
            const extraTextDataProvider = this.getDataProvider(this.jsonReader.getGridData().row, "extra_text1");
            extraTextColumn['FilterType'] = 'MultipleSelect'
            extraTextColumn['dataProvider'] = extraTextDataProvider;
            extraTextColumn['FilterInputSearch'] = true;
          }

            // UETR
            const uetrColumn = obj.columns.column.find(column => column.dataelement === "uetr");
            if (uetrColumn) {
              const uetrDataProvider = this.getDataProvider(this.jsonReader.getGridData().row, "uetr");
              uetrColumn['FilterType'] = 'MultipleSelect'
              uetrColumn['dataProvider'] = uetrDataProvider;
              uetrColumn['FilterInputSearch'] = true;
          }            
          
            // Movement ID
          const mvtIdColumn = obj.columns.column.find(column => column.dataelement === "movement");
          if (mvtIdColumn) {
            const mvtDataProvider = this.getDataProvider(this.jsonReader.getGridData().row, "movement",false);
            mvtIdColumn['FilterType'] = 'MultipleSelect'
            mvtIdColumn['dataProvider'] = mvtDataProvider;
            mvtIdColumn['FilterInputSearch'] = true;
          }
          // Match Status
          const matchStsList = ["CONFIRMED", "OFFERED", "OUTSTANDING", "SUSPENDED", "RECONCILED"];
          const statusColumn = obj.columns.column.find(column => column.dataelement === "status");
          if (statusColumn) {
            const statusDataProvider = this.prepareDataProvider(matchStsList);
            statusColumn['FilterType'] = 'MultipleSelect'
            statusColumn['dataProvider'] = statusDataProvider;
            statusColumn['FilterInputSearch'] = false;
          }
            //Position Level
            const posLevelList = ExternalInterface.call('eval', 'posLevelList') ? JSON.parse(ExternalInterface.call('eval', 'posLevelList')) : [];
            const posLevelColumn = obj.columns.column.find(column => column.dataelement === "pos");
            const posLevelDataProvider = this.prepareDataProvider(posLevelList);
            posLevelColumn['FilterType'] = 'MultipleSelect'
            posLevelColumn['dataProvider'] = posLevelDataProvider;
            posLevelColumn['FilterInputSearch'] = false;


              //Sign
          const signList=["D", "C"];
          const signColumn = obj.columns.column.find(column => column.dataelement === "sign");
          const signDataProvider = this.prepareDataProvider(signList);
          signColumn['FilterType'] = 'MultipleSelect'
          signColumn['dataProvider'] = signDataProvider;
          signColumn['FilterInputSearch'] = false;

          //Predict Status
          const predStsList=["I", "E", "C"];
          const predStsColumn = obj.columns.column.find(column => column.dataelement === "pred");
          const predictStsDataProvider = this.prepareDataProvider(predStsList);
          predStsColumn['FilterType'] = 'MultipleSelect'
          predStsColumn['dataProvider'] = predictStsDataProvider;
          predStsColumn['FilterInputSearch'] = false;

          //External Status
          const extStsList=["(EMPTY)", "(NOT EMPTY)", "I", "E"];
          const externalStsColumn = obj.columns.column.find(column => column.dataelement === "ext");
          const externalStsDataProvider = this.prepareDataProvider(extStsList);
          externalStsColumn['FilterType'] = 'MultipleSelect'
          externalStsColumn['dataProvider'] = externalStsDataProvider;
          externalStsColumn['FilterInputSearch'] = false;

          //Ilm Fcast status
          const ilmFcastStsList=["(EMPTY)", "(NOT EMPTY)", "I","E"];
          const ilmFcastColumn = obj.columns.column.find(column => column.dataelement === "ilmfcast");
          const ilmFcastStsDataProvider = this.prepareDataProvider(ilmFcastStsList);
          ilmFcastColumn['FilterType'] = 'MultipleSelect'
          ilmFcastColumn['dataProvider'] = ilmFcastStsDataProvider;
          ilmFcastColumn['FilterInputSearch'] = false;


          // Input search filter
          // Ref1
          const ref1Column = obj.columns.column.find(column => column.dataelement === "ref1");
          if (ref1Column) {
            ref1Column['FilterType'] = "InputSearch";
          }

          // Ref2
          const ref2Column = obj.columns.column.find(column => column.dataelement === "ref2");
          if (ref2Column) {
            ref2Column['FilterType'] = "InputSearch";
          }

          // Ref3
          const ref3Column = obj.columns.column.find(column => column.dataelement === "ref3");
          if (ref3Column) {
            ref3Column['FilterType'] = "InputSearch";
          }

          // Extra Ref
          const xrefColumn = obj.columns.column.find(column => column.dataelement === "xref");
          if (xrefColumn) {
            xrefColumn['FilterType'] = "InputSearch";
          }

          if(maxPage<=1){
          //Sign

          const sourceColumn = obj.columns.column.find(column => column.dataelement === "source");
          const sourceLevelList = ExternalInterface.call('eval', 'sourcelList') ? JSON.parse(ExternalInterface.call('eval', 'sourcelList')) : [];
          if (sourceColumn) {
            const sourceColumn = obj.columns.column.find(column => column.dataelement === "source");
            const sourceLevelDataProvider = this.prepareDataProvider(sourceLevelList);
            sourceColumn['FilterType'] = 'MultipleSelect';
            sourceColumn['dataProvider'] = sourceLevelDataProvider;
            sourceColumn['FilterInputSearch'] = false;
          }

          // format
          const formatColumn = obj.columns.column.find(column => column.dataelement === "format");
          if (formatColumn) {
            const formatDataProvider = this.getDataProvider(this.jsonReader.getGridData().row, "format");
            formatColumn['FilterType'] = 'MultipleSelect'
            formatColumn['dataProvider'] = formatDataProvider;
            formatColumn['FilterInputSearch'] = true;
          }

          }else{
          //columns with static dataprovider
          const sourceColumn = obj.columns.column.find(column => column.dataelement === "source");
         const sourceLevelList = ExternalInterface.call('eval', 'sourcelList') ? JSON.parse(ExternalInterface.call('eval', 'sourcelList')) : [];
            if (sourceColumn) {
              const sourceColumn = obj.columns.column.find(column => column.dataelement === "source");
              const sourceLevelDataProvider = this.prepareDataProvider(sourceLevelList);
              sourceColumn['FilterType'] = 'MultipleSelect';
              sourceColumn['dataProvider'] = sourceLevelDataProvider;
              sourceColumn['FilterInputSearch'] = false;
          }

          const formatList = ExternalInterface.call('eval', 'formatList') ? JSON.parse(ExternalInterface.call('eval', 'formatList')) : [];
  const formatColumn = obj.columns.column.find(column => column.dataelement === "format");
          if (sourceColumn) {
              const formatDataProvider = this.prepareDataProvider(formatList);
              formatColumn['FilterType'] = 'MultipleSelect';
              formatColumn['dataProvider'] = formatDataProvider;
              formatColumn['FilterInputSearch'] = false;
            }

        
          }

          let  index= 29;
          //additional columns new filter
          if (this.useAddColsCheck != 'N') {
            for (let key in this.addColumnsGridData) {
              index++;
              let dataelement = key.split("*")[0];
              const addColColumn = obj.columns.column.find(column => column.dataelement === dataelement);
              if(addColColumn) {
                const addColsDataProvider = this.getDataProvider(this.jsonReader.getGridData().row, dataelement);
                addColColumn['FilterType'] = 'MultipleSelect'
                addColColumn['dataProvider'] = addColsDataProvider;
                addColColumn['FilterInputSearch'] = true;
              }
            }
          }

            this.cGrid.CustomGrid(obj);
            for (let i = 0; i < this.cGrid.columnDefinitions.length; i++) {
              let column = this.cGrid.columnDefinitions[i];
              if (column.field == "alerting") {
                const alertUrl = "./"+ ExternalInterface.call('eval', 'alertOrangeImage');
                const alerCrittUrl = "./"+ ExternalInterface.call('eval', 'alertRedImage');
                if (this.currentFontSize == "Normal"){
                 column['properties'] = {
                  enabled: false,
                  columnName: 'alerting',
                  imageEnabled: alertUrl,
                  imageCritEnabled:alerCrittUrl,
                  imageDisabled: "",
                  _toolTipFlag: true,
                  style: ' display: block; margin-left: auto; margin-right: auto;'

                };

                }else{
                  column['properties'] = {
                    enabled: false,
                    columnName: 'alerting',
                    imageEnabled: alertUrl,
                    imageCritEnabled:alerCrittUrl,
                    imageDisabled: "",
                    _toolTipFlag: true,
                    style: 'height:15px; width:15px; display: block; margin-left: auto; margin-right: auto;'

                  };
				  
		  }
				
                this.cGrid.columnDefinitions[i].editor = null;
                this.cGrid.columnDefinitions[i].formatter = AlertingRenderer;
              }
            }
            if (methodName != 'search' && (this.actionMethodName == 'displayOpenMovements'))
            {
              this.dataGridContainer.height = "65%";
            } else {
              this.dataGridContainer.height = "100%";
              this.keepSelected = false;
            }
            this.cGrid.gridData=this.jsonReader.getGridData();
            if(!this.keepSelected) {
              this.cGrid.selectedIndices = [];
              this.cGrid.selectedIndex = -1;
              this.isPageChanged = false;
            } else {
              try {
                if(this.selectedMvmts.length > 0) {
                  let array = [];
                  let gridDataAsArray: any[] = Array.of(this.jsonReader.getGridData().row);
                  gridDataAsArray = gridDataAsArray[0];
                  for (let i = 0; i < this.selectedMvmts.length; i++) {
                    if(gridDataAsArray) {
                      let columnIndex = gridDataAsArray.findIndex(( x ) => x.movement.content == this.selectedMvmts[i] );
                      if(columnIndex != -1)
                        array.push(columnIndex)
                    }

                  }

                  setTimeout(()=> {
                    if(array.length>0)
                      this.cGrid.selectedIndices = array;
                    else
                      this.isPageChanged = false;
                  }, 0)

                }else {
                  this.isPageChanged = false;
                }


              } catch(e) {
                console.log('error in set selecteedindeices after grid recreate', e)
              }
            }

            this.cGrid.setRowSize=this.jsonReader.getRowSize();

            let selectedFilteredValue:string= this.jsonReader.getScreenAttributes()["selectedFilter"];
            this.numStepper.value=this.jsonReader.getScreenAttributes()["pages"].currentPage;

            //let selectedFilteredValueArr=selectedFilteredValue.split('|');
            let selectedSort:string=this.jsonReader.getScreenAttributes()["selectedSort"];
            let selectedSortColumn:string=selectedSort.substr(0, selectedSort.indexOf("|"));
            let colOrder = [];
            let colNames = [];
            let pname:string;
            for (pname in this.jsonReader.getColumnData().column.dataelement)
            {
              let num:string=this.jsonReader.getColumnData().column.columnNumber[pname];
              let name:string=this.jsonReader.getColumnData().column.dataelement[pname];
              colOrder[pname]=num;
              colNames[pname]=name;
            }
            for (let sortColCounter:number=0; sortColCounter < colOrder.length; sortColCounter++)
            {
              if (colOrder[sortColCounter] == selectedSortColumn)
              {
                this.cGrid.sortedGridColumn=colNames[sortColCounter];
              }
            }
            var acctFlag:boolean=true;
            let lockFlag2:Object=new Object();
            let lock2:string="";
            if (this.cGrid.selectedIndices.length > 0)
            {
              for (let chkSelectCount:number=0; chkSelectCount < this.cGrid.selectedIndices.length; chkSelectCount++)
              {
                if(this.cGrid.dataProvider[this.cGrid.selectedIndices[chkSelectCount]] != undefined) {
                  lockFlag2=ExternalInterface.call("accountAccessConfirm", (this.cGrid.dataProvider[this.cGrid.selectedIndices[chkSelectCount]].movement).toString(), (this.entityCombo.selectedLabel).toString());
                  lock2=lockFlag2.toString();
                  if (!BooleanParser.parseBooleanValue(lock2))
                    acctFlag=false;
                }

              }
            }



            sortDirection=selectedSort.substr((selectedSort.indexOf("|") + 1), ((selectedSort.length) - 3));

            if ((BooleanParser.parseBooleanValue(sortDirection)) || (sortDirection == "true|"))
            {
              sortDir=true;
            }
            else
            {
              sortDir=false;
            }
            //this.cGrid.refreshHeadersforSortArrow(Number(selectedSortColumn), sortDir, this.cGrid.selectedFilteredColumns, colOrder);

            if (this.cGrid.selectedIndices.length == 0)
            {
              this.movementButton.enabled=false;
              this.movementButton.buttonMode=false;
              this.noteButton.enabled=false;
              this.noteButton.buttonMode=false;
              this.messageButton.enabled=false;
              this.messageButton.buttonMode=false;
            }

            /* Bottom Panel for MSD EO */
            if (methodName != 'search' && (this.actionMethodName == 'displayOpenMovements')) {
              this.dataGridContainer2.visible = true;
              this.dataGridContainer2.includeInLayout = true;
              this.dataGridContainer2.height = "20%";

              if(this.bottomGrid == undefined) {
                this.bottomGrid = <SwtCommonGrid>this.dataGridContainer2.addChild(SwtCommonGrid);
                this.bottomGrid.onRowClick = (event) => {
                  this.cellLogic(event)
                };
                this.bottomGrid.lockedColumnCount =2;
                this.bottomGrid.uniqueColumn = 'movement'
                this.bottomGrid.id = "bottom";
              }
              const obj = {columns: this.jsonReader.getColumnData()}
              this.bottomGrid.CustomGrid(obj);

              this.bottomGrid.ITEM_CLICK.subscribe((selectedRowData) => {
                this.itemClickFunction2(selectedRowData);
              }); 

              /*Set grid data and row size from XML*/
              if(this.jsonReader.getBottomGridData().row) {
                let selectedIndexArr = [];
                let arrayBottomGrid: any[] = Array.of(this.jsonReader.getBottomGridData().row);
                if(arrayBottomGrid[0].length > 1) {
                  arrayBottomGrid = arrayBottomGrid[0];
                }
                for (let bottomSelectedCounter: number = 0; bottomSelectedCounter < arrayBottomGrid.length; bottomSelectedCounter++) {
                  for (let resetSelectedCounter: number = 0; resetSelectedCounter < this.cGrid.dataProvider.length; resetSelectedCounter++) {
                    if (arrayBottomGrid[bottomSelectedCounter].movement.content == this.cGrid.dataProvider[resetSelectedCounter].movement) {
                      selectedIndexArr.push(resetSelectedCounter);
                    }
                  }
                }
                // this.cGrid.selectedIndices = selectedIndexArr;
                this.bottomGrid.gridData = this.jsonReader.getBottomGridData();
                this.bottomGrid.setRowSize = this.jsonReader.getBottomGridData().size;
              } else{
                this.bottomGrid.gridData = {row: [], size: 0}
              }



              let rowArrayColection = [];
              for (let btmCounter: number = 0; btmCounter < this.bottomGrid.dataProvider.length; btmCounter++) {
                // Modified by Balaji for Mantis 1998 on 23-July-2012
                for (let i: number = 0; i < this.profileAddCols.length; i++) {
                  let col=this.profileAddCols[i];
                  rowArrayColection.push({
                    col: this.bottomGrid.dataProvider[btmCounter].col,})   

                }

                rowArrayColection.push({
                  pos: this.bottomGrid.dataProvider[btmCounter].pos,
                  value: this.bottomGrid.dataProvider[btmCounter].value,
                  amount: this.bottomGrid.dataProvider[btmCounter].amount,
                  sign: this.bottomGrid.dataProvider[btmCounter].sign,
                  ccy: this.bottomGrid.dataProvider[btmCounter].ccy,
                  ref1: this.bottomGrid.dataProvider[btmCounter].ref1,
                  account: this.bottomGrid.dataProvider[btmCounter].account,
                  input: this.bottomGrid.dataProvider[btmCounter].input,
                  cparty: this.bottomGrid.dataProvider[btmCounter].cparty,
                  pred: this.bottomGrid.dataProvider[btmCounter].pred,
                  ext: this.bottomGrid.dataProvider[btmCounter].ext,
                  status: this.bottomGrid.dataProvider[btmCounter].status,
                  matchid: this.bottomGrid.dataProvider[btmCounter].matchid,
                  source: this.bottomGrid.dataProvider[btmCounter].source,
                  format: this.bottomGrid.dataProvider[btmCounter].format,
                  notes: this.bottomGrid.dataProvider[btmCounter].notes,
                  beneficiary: this.bottomGrid.dataProvider[btmCounter].beneficiary,
                  ref2: this.bottomGrid.dataProvider[btmCounter].ref2,
                  ref3: this.bottomGrid.dataProvider[btmCounter].ref3,
                  movement: this.bottomGrid.dataProvider[btmCounter].movement,
                  book: this.bottomGrid.dataProvider[btmCounter].book,
                  custodian: this.bottomGrid.dataProvider[btmCounter].custodian,
                  xref: this.bottomGrid.dataProvider[btmCounter].xref,
                  update_date: this.bottomGrid.dataProvider[btmCounter].update_date,
                  matchingparty: this.bottomGrid.dataProvider[btmCounter].matchingparty,
                  producttype: this.bottomGrid.dataProvider[btmCounter].producttype,
                  postingdate: this.bottomGrid.dataProvider[btmCounter].postingdate,
                  extra_text1: this.bottomGrid.dataProvider[btmCounter].extra_text1,
                  alerting: this.bottomGrid.dataProvider[btmCounter].alerting,
                  ilmfcast: this.bottomGrid.dataProvider[btmCounter].ilmfcast,
                  uetr: this.bottomGrid.dataProvider[btmCounter].uetr
                });
              }
              // this.bottomGrid.dataProvider = rowArrayColection;
              if (this.bottomGrid.dataProvider.length > 0) {

                let varPositionLevel: number;
                let varSign: string;
                let varAmount: number = 0;
                let amount: string = "";
                let varMax: number = 0;
                let varMin: number = 0;
                let amountArr = [];
                let diffArr = [];
                let ccyFormat: string = this.jsonReader.getScreenAttributes()["currencyFormat"];
                let ccyCode: string = "";

                for (let high: number = 0; high < this.bottomGrid.dataProvider.length; high++) {
                  varPositionLevel = parseInt(this.bottomGrid.dataProvider[high].positionlevel);
                  varSign = this.bottomGrid.dataProvider[high].sign;
                  amount = this.bottomGrid.dataProvider[high].amount;
                  if (ccyFormat == "currencyPat1") {
                    amountArr = amount.split(',');
                    amount = "";
                    for (let commaCounter: number = 0; commaCounter < amountArr.length; commaCounter++) {
                      amount += amountArr[commaCounter];
                    }
                  } else {
                    amountArr = amount.split('.');
                    amount = "";
                    for (let dotCounter: number = 0; dotCounter < amountArr.length; dotCounter++) {
                      amount += amountArr[dotCounter];
                    }
                    amount = amount.replace(',', '.');
                  }
                  varAmount = parseFloat(amount);

                  if (varSign == "D")
                    totalSelectedAmount -= varAmount;
                  else
                    totalSelectedAmount += varAmount;

                  switch (varPositionLevel) {
                    case 1:
                      if (diffArr[0] == null) {
                        diffArr[0] = 0;
                      }
                      if (varSign == "D") {
                        diffArr[0] = diffArr[0] - varAmount;
                      } else {
                        diffArr[0] = diffArr[0] + varAmount;

                      }
                      break;
                    case 2:
                      if (diffArr[1] == null) {
                        diffArr[1] = 0;
                      }
                      if (varSign == "D") {
                        diffArr[1] = diffArr[1] - varAmount;
                      } else {
                        diffArr[1] = diffArr[1] + varAmount;
                      }
                      break;
                    case 3:
                      if (diffArr[2] == null) {
                        diffArr[2] = 0;
                      }
                      if (varSign == "D") {
                        diffArr[2] = diffArr[2] - varAmount;
                      } else {
                        diffArr[2] = diffArr[2] + varAmount;
                      }
                      break;
                    case 4:
                      if (diffArr[3] == null) {
                        diffArr[3] = 0;
                      }
                      if (varSign == "D") {
                        diffArr[3] = diffArr[3] - varAmount;
                      } else {
                        diffArr[3] = diffArr[3] + varAmount;
                      }
                      break;
                    case 5:
                      if (diffArr[4] == null) {
                        diffArr[4] = 0;
                      }
                      if (varSign == "D") {
                        diffArr[4] = diffArr[4] - varAmount;
                      } else {
                        diffArr[4] = diffArr[4] + varAmount;
                      }
                      break;
                    case 6:
                      if (diffArr[5] == null) {
                        diffArr[5] = 0;
                      }
                      if (varSign == "D") {
                        diffArr[5] = diffArr[5] - varAmount;
                      } else {
                        diffArr[5] = diffArr[5] + varAmount;
                      }
                      break;
                    case 7:
                      if (diffArr[6] == null) {
                        diffArr[6] = 0;
                      }
                      if (varSign == "D") {
                        diffArr[6] = diffArr[6] - varAmount;
                      } else {
                        diffArr[6] = diffArr[6] + varAmount;
                      }
                      break;
                    case 8:
                      if (diffArr[7] == null) {
                        diffArr[7] = 0;
                      }
                      if (varSign == "D") {
                        diffArr[7] = diffArr[7] - varAmount;
                      } else {
                        diffArr[7] = diffArr[7] + varAmount;
                      }
                      break;
                    case 9:
                      if (diffArr[8] == null) {
                        diffArr[8] = 0;
                      }
                      if (varSign == "D") {
                        diffArr[8] = diffArr[8] - varAmount;
                      } else {
                        diffArr[8] = diffArr[8] + varAmount;
                      }
                  }
                }
                let diffPositions: string = "";
                let count: number = 0
                for (let i: number = 0; i < diffArr.length; i++) {
                  if (diffArr[i] != null) {
                    if (count == 0) {
                      count = -1;
                      varMax = diffArr[i];
                      varMin = diffArr[i];
                    } else {
                      diffPositions = 'Y';
                      if (diffArr[i] < varMin) {
                        varMin = diffArr[i];
                      }
                      if (diffArr[i] > varMax) {
                        varMax = diffArr[i];
                      }
                    }
                  }
                }
                //TODO check precision and round
                /*let numFormat: any; //NumberFormatter=new NumberFormatter();
                numFormat.precision="2";
                numFormat.rounding="nearest";
                let difference:Number=0;

                difference=varMax - varMin;

                if (difference == 0)
                {
                  if (diffPositions == 'Y')
                  {
                    this.diffText.text="0.00";
                  }
                  else
                  {
                    this.diffText.text="";
                  }
                }
                else
                {
                  let expandDiffAmt:string=numFormat.format(difference);
                  if (expandDiffAmt.indexOf(".") == -1)
                  {
                    expandDiffAmt+="00";
                  }
                  else
                  {
                    let expandDiffArr= [];
                    expandDiffArr = expandDiffAmt.split(".");
                    expandDiffAmt=expandDiffArr[0] + expandDiffArr[1]
                  }
                  this.diffText.text=ExternalInterface.call("expandAmtDifference", expandDiffAmt, ccyFormat, ccyCode);
                }*/


              }
              let selectedStatus: string = "";
              let statusConfirmFlag: boolean = false;
              let statusSuspendFlag: boolean = false;
              let statusOutStandingFlag: boolean = false;
              if (this.bottomGrid != null) {
                if (this.bottomGrid.dataProvider.length > 0) {
                  for (let selMatchCount: number = 0; selMatchCount < this.bottomGrid.dataProvider.length; selMatchCount++) {
                    selectedStatus = this.bottomGrid.dataProvider[selMatchCount].status;
                    if (selectedStatus.split(" ")[0] == "CONFIRMED") {
                      statusConfirmFlag = true;
                    } else if (selectedStatus.split(" ")[0] == "SUSPENDED") {
                      statusSuspendFlag = true;
                    } else if (selectedStatus.split(" ")[0] == "OUTSTANDING") {
                      statusOutStandingFlag = true;
                    } else {// added by Med Amine for Mantis 1889
                      let unlockflag: any;
                      //check the whether movement id is locked or not
                      unlockflag = ExternalInterface.call("unlockMovementOnServer", this.bottomGrid.dataProvider[selMatchCount].movement);
                      this.bottomGrid.dataProvider = null;
                    }
                  }
                  if (acctFlag) {
                    if (statusOutStandingFlag == false) {
                      if (statusConfirmFlag == false) {
                        this.confirmButton.enabled = true;
                        this.confirmButton.buttonMode = true;
                      } else {
                        this.confirmButton.enabled = false;
                        this.confirmButton.buttonMode = false;
                      }
                      if (statusSuspendFlag == false) {
                        this.suspendButton.enabled = true;
                        this.suspendButton.buttonMode = true;
                      } else {
                        this.suspendButton.enabled = false;
                        this.suspendButton.buttonMode = false;
                      }
                    } else {
                      this.confirmButton.enabled = true;
                      this.confirmButton.buttonMode = true;
                      this.suspendButton.enabled = true;
                      this.suspendButton.buttonMode = true;
                    }

                    this.matchButton.enabled = true;
                    this.matchButton.buttonMode = true;
                  }
                } else {
                  this.reconButton.enabled = false;
                  this.reconButton.buttonMode = false;
                  this.confirmButton.enabled = false;
                  this.confirmButton.buttonMode = false;
                  this.suspendButton.enabled = false;
                  this.suspendButton.buttonMode = false;
                  this.matchButton.enabled = false;
                  this.matchButton.buttonMode = false;
                }
              }

              this.selectedMovements= [];
              for (let selMvmtCounter:number=0; selMvmtCounter < this.bottomGrid.dataProvider.length; selMvmtCounter++)
              {
                this.selectedMovements.push(""+this.bottomGrid.dataProvider[selMvmtCounter].movement);
              }
              let flag:boolean=false;
              for (let high:number=0; high < this.bottomGrid.dataProvider.length; high++)
              {
                if ((this.bottomGrid.dataProvider[high].matchid).toString().length > 0)
                  flag=true;
              }
              if (flag)
              {
                this.reconButton.enabled=false;
                this.reconButton.buttonMode=false;
              }

              else
              {
                if (this.bottomGrid.dataProvider.length != 0)
                {
                  this.reconButton.enabled=true;
                  this.reconButton.buttonMode=true;
                }

              }

            }else {

              let amountSign:string;
              let amountAsstring:string;
              let amountAsNumber:number=0;
              let amountSplitArr= [];
              let amountArr = [];
              let ccyFormat:string=this.jsonReader.getScreenAttributes()["currencyFormat"];
              var ccyCode:string="";

              let formatedAmount:string = "";

              for (let selMvmtCounter:number=0; selMvmtCounter < this.cGrid.selectedIndices.length; selMvmtCounter++)
              {
                amountSign=this.cGrid.dataProvider[this.cGrid.selectedIndices[selMvmtCounter]].sign;
                amountAsstring=this.cGrid.dataProvider[this.cGrid.selectedIndices[selMvmtCounter]].amount;
                if (ccyFormat == "currencyPat1")
                {
                  amountSplitArr=amountAsstring.split(',');
                  amountAsstring="";

                  for (let commaCounter:number=0; commaCounter < amountSplitArr.length; commaCounter++)
                  {
                    amountAsstring+=amountSplitArr[commaCounter];
                  }
                }
                else
                {
                  amountSplitArr=amountAsstring.split('.');
                  amountAsstring="";

                  for (let dotCounter:number=0; dotCounter < amountSplitArr.length; dotCounter++)
                  {
                    amountAsstring+=amountSplitArr[dotCounter];
                  }

                  amountAsstring=amountAsstring.replace(',', '.');

                }
                amountAsNumber=parseFloat(amountAsstring);


                if(amountSign == "D")
                  totalSelectedAmount-=amountAsNumber;
                else
                  totalSelectedAmount+=amountAsNumber;
              }
            }

            var matchFlag:boolean=false;
            let matchId:string=null;
            if (this.bottomGrid != null)
            {
              for (var selMatchCount:number=0; selMatchCount < this.bottomGrid.dataProvider.length; selMatchCount++)
              {
                if ((matchId == null) && ((this.bottomGrid.dataProvider[selMatchCount].matchid).toString().length > 0))
                {
                  matchId=this.bottomGrid.dataProvider[selMatchCount].matchid;
                }
                else if ((this.bottomGrid.dataProvider[selMatchCount].matchid).toString().length > 0)
                {
                  if (matchId != this.bottomGrid.dataProvider[selMatchCount].matchid)
                  {
                    matchFlag=true;
                    break;
                  }
                }
              }
              if (matchFlag)
              {
                this.matchButton.enabled=false;
                this.matchButton.buttonMode=false;
                let unlockFlag: any;
                unlockFlag=ExternalInterface.call("unlockMovementOnServer", this.bottomGrid.dataProvider[selMatchCount].movement);
                this.swtAlert.warning(SwtUtil.getPredictMessage('label.selectedOnlyMatchedItems', null), 'Warning');
              }
              else if (this.bottomGrid.dataProvider.length >= 1)
              {
                this.matchButton.enabled=true;
                this.matchButton.buttonMode=true;
              }
            }
            //let maxPage = this.jsonReader.getScreenAttributes()["pages"].maxPage;
            if(maxPage > 1) {
              this.paginationData.visible = true;
              this.numStepper.minimum=1;
              this.numStepper.maximum=this.jsonReader.getScreenAttributes()["pages"].maxPage;
            } else {
              this.paginationData.visible = false;
            }
            this.exportContainer.maxPages=ExternalInterface.call('eval', 'exportMaxPages');
            this.exportContainer.totalPages=maxPage;
            this.exportContainer.currentPage = this.jsonReader.getScreenAttributes()["pages"].currentPage;

//TODO =this.jsonReader.getPages();
            /* let paginationXMLList:any ;
             if (paginationXMLList.length() > 0)
             {
               let page: SwtLabel;
               page.setStyle("paddingTop","6");
               page.text="             Page ";
               page.setStyle("fontWeight", "bold");
               this.paginationData.addChild(page);
               this.numStepper.minimum=1;
               this.numStepper.maximum=this.jsonReader.getScreenAttributes()["pages"].maxPage;
               // TODO
              /!* this.numStepper.addEventListener(MouseEvent.CLICK, numpager);
               numStepper.addEventListener(KeyboardEvent.KEY_DOWN, keyDownPager);
               numStepper.width=75;
               paginationData.addElement(numStepper);*!/
               let pageLeft: SwtLabel;
               pageLeft.setStyle("paddingTop","6");
               pageLeft.text=" "+ExternalInterface.call('getBundle', 'text', 'label-of', 'of')+"  " + this.jsonReader.getScreenAttributes()["maxPage"];
               //TODO this.paginationData.addElement(pageLeft);

             }*/
            /* Pagination Ends. */
            if (methodName == "search")
            {
              if (this.cGrid != null && this.cGrid.dataProvider != null && this.cGridSelMvmt != null)
              {
                let removeSel:boolean=true;
                //get the msdGrid data and check the selected movement is present or not
                for (let cGridIndex:number=0; this.cGrid.dataProvider.length > cGridIndex; cGridIndex++)
                {
                  //check the movement
                  if (this.cGrid.dataProvider[cGridIndex].movement == this.cGridSelMvmt)
                  {
                    //set the selected index for msd grid.
                    this.cGrid.selectedIndex=cGridIndex;
                    removeSel=false;
                    //get the selected movement id
                    let movId:string=this.cGrid.dataProvider[this.cGrid.selectedIndex].movement;
                    //get the message status
                    let result:string=ExternalInterface.call("setMsgButtonStatus", movId);
                    let noOfMovements:string=result.substring(0, result.indexOf('|'));
                    //Disable the Message button
                    if (noOfMovements == "0")
                    {
                      this.messageButton.enabled=false;
                      this.messageButton.buttonMode=false;
                    }
                    //Enable the Message button
                    else
                    {
                      this.messageButton.enabled=true;
                      this.messageButton.buttonMode=true;
                    }
                    //enable the Notes,Mvmt button
                    this.noteButton.enabled=true;
                    this.noteButton.buttonMode=true;
                    this.movementButton.enabled=true;
                    this.movementButton.buttonMode=true;
                    break;
                  }
                }
                //nullify the selected movement if not present.
                if (removeSel)
                  this.cGridSelMvmt=null;

              }
            }
          }
          // Gets the font size from xml reader
          /* if (ExternalInterface.)
           {
             // Gets the current font size from the this.jsonReader
             this.currentFontSize= "Normal"; //TODO this.jsonReader.getFontSize();
           }*/
          this.currentFontSize=  this.jsonReader.getScreenAttributes()["currfontsize"];
          // Sets the data grid style based on the font size
          if (this.currentFontSize == "Normal")
          {
            this.selectedFont=0;
            this.cGrid.styleName="dataGridNormal";
            this.cGrid.rowHeight=18;

            // check the bottom grid is not null, if it is not null then only set style name and row Height.
            if (this.bottomGrid != null)
            {
              this.bottomGrid.styleName="dataGridNormal";
              this.bottomGrid.rowHeight=18;
            }

          }
          else if (this.currentFontSize == "Small")
          {
            this.selectedFont=1;
            this.cGrid.styleName="dataGridSmall";
            this.cGrid.rowHeight=15;

            // check the bottom grid is not null, if it is not null then only set style name and row Height.
            if (this.bottomGrid != null)
            {
              this.bottomGrid.styleName="dataGridSmall";
              this.bottomGrid.rowHeight=15;
            }

          }
        }
      }
      else
      {
        /*Alert to display the error and its location*/
        this.swtAlert.error(this.jsonReader.getRequestReplyMessage() + "\n" + this.jsonReader.getRequestReplyLocation(), "Error"); //ExternalInterface.call('getBundle', 'text', 'alert-error', 'Error')
        /*Condition check for the initial load of flex with data error*/
        if (this.lastmonitortype != null)
        {
          this.updateData();
        }
      }
      if (this.bottomGrid != null)
      {
        if (this.bottomGrid.dataProvider.length == 1)
        {
          this.movementButton.enabled=true;
          this.movementButton.buttonMode=true;
          this.noteButton.enabled=true;
          this.noteButton.buttonMode=true;
          this.messageButton.enabled=true;
          this.messageButton.buttonMode=true;
        }
        else if (this.bottomGrid.dataProvider.length == 0)
        {
          this.movementButton.enabled=false;
          this.movementButton.buttonMode=false;
          this.noteButton.enabled=false;
          this.noteButton.buttonMode=false;
          this.messageButton.enabled=false;
          this.messageButton.buttonMode=false;
        }
        if ((this.bottomGrid.dataProvider.length == 0) || (this.bottomGrid.selectedIndex == -1))
        {
          this.removeButton.enabled = false;
          this.removeButton.buttonMode=false;
        }
      }
      let lockFlag2:Object = new Object();
      let lock2:string="";

      if (this.bottomGrid != null && this.bottomGrid.dataProvider.length > 0)
      {
        for (let high:number=0; high < this.bottomGrid.dataProvider.length; high++)
        {
          lockFlag2=ExternalInterface.call("accountAccessConfirm", (this.bottomGrid.dataProvider[high].movement).toString(), (this.entityCombo.selectedLabel).toString());
          lock2=lockFlag2.toString();
          if (!BooleanParser.parseBooleanValue(lock2))
            acctFlag=false;
        }
      }
      if (!acctFlag)
      {
        this.reconButton.enabled=false;
        this.reconButton.buttonMode=false;
        this.confirmButton.enabled=false;
        this.confirmButton.buttonMode=false;
        this.suspendButton.enabled=false;
        this.suspendButton.buttonMode=false;
        this.matchButton.enabled=false;
        this.matchButton.buttonMode=false;
      }
      let outStandingFlag:boolean = false;
      if (this.bottomGrid != null && this.bottomGrid.dataProvider.length > 0)
      {
        for (var selMatchCount:number=0; selMatchCount < this.bottomGrid.dataProvider.length; selMatchCount++)
        {
          let selectedStatus=this.bottomGrid.dataProvider[selMatchCount].status;
          if (selectedStatus.split(" ")[0] == "OUTSTANDING")
          {
            outStandingFlag=true;
            break;
          }
        }

        if (!acctFlag && !outStandingFlag && !matchFlag)
        {
          this.matchButton.enabled=true;
        }
      }
    }
    if (this.initReceivedJSON == null && this.lastRecievedJSON != null)
    {
      this.initReceivedJSON=this.lastRecievedJSON ;
    }

    if(!this.currencyThreshold.enabled) {
      this.currencyThreshold.enabled=true;
    }
    setTimeout(() => {
      if (this.cGrid.selectedIndices.length <= 0)
        {
          this.messageButton.enabled=false;
          this.messageButton.buttonMode=false;
          this.movementButton.enabled=false;
          this.movementButton.buttonMode=false;
          this.noteButton.enabled=false;
          this.noteButton.buttonMode=false;
          if(this.bottomGrid != null) {
            this.reconButton.enabled=false;
            this.reconButton.buttonMode=false;
            this.confirmButton.enabled=false;
            this.suspendButton.buttonMode=false;
            this.suspendButton.enabled=false;
            this.matchButton.buttonMode=false;
            this.matchButton.enabled=false;
          }
    
        }
    }, 0);
    //Added by Med Amine for Mantis 1889

//TODO
    /*let numFormatTotal:any;
    numFormatTotal.precision="2";
    numFormatTotal.rounding="nearest";*/

    let ccyFormatForTotal:string = this.jsonReader.getScreenAttributes()["currencyFormat"];

    let expandTotalAmt:string= this.addZeroes(totalSelectedAmount.toString());
    if (expandTotalAmt.indexOf(".") == -1)
    {
      expandTotalAmt+="00";
    }
    else
    {
      let expandDiffArr = [];
      expandDiffArr=expandTotalAmt.split(".");
      expandTotalAmt=expandDiffArr[0] + expandDiffArr[1]
    }
    formatedAmount = ExternalInterface.call("expandAmtDifference", expandTotalAmt, ccyFormatForTotal, ccyCode);
    if(totalSelectedAmount < 0 && formatedAmount.length>0 && formatedAmount.charAt(0) != "-") {
      this.totalSelectedValue.text="-"+ExternalInterface.call("expandAmtDifference", expandTotalAmt, ccyFormatForTotal, ccyCode);
    }else {
      this.totalSelectedValue.text=ExternalInterface.call("expandAmtDifference", expandTotalAmt, ccyFormatForTotal, ccyCode);
    }

    setTimeout(() => {

      if ( this.bottomGrid != null &&  this.bottomGrid.dataProvider.length > 0){
        for (let i:number=0; i < this.bottomGrid.dataProvider.length; i++)
        {
          let lockflag=ExternalInterface.call("lockMovementOnServer", this.bottomGrid.dataProvider[i].movement );
        }
      }
      
    }, 0);
  }

  /**
   *Fault event handler for inputData HTTPComms*/
  inputDataFault(event):void
  {
    this.lastRefTime.visible=false;
    this.lastRefTimeLabel.text= 'Connection Error'; //ExternalInterface.call('getBundle', 'text', 'label-ConnectionError', 'Connection Error');
    this.lastRefTimeLabel.setStyle("color", "Red", this.lastRefTimeLabel.domElement);
    this.invalidComms=event; //event.fault.faultstring + "\n" + event.fault.faultCode + "\n" + event.fault.faultDetail;
  }

  /**
   *Mouse event to handle check the availability of connection */
  connError(event):void
  {
    this.swtAlert.error("" + this.invalidComms, ExternalInterface.call('getBundle', 'text', 'alert-error', 'Error'));
  }

  /**
   * Part of a callback function to all for control of the loading swf from the HTTPComms Object
   **/
  startOfComms():void
  {
    this.loadingImage.setVisible(true);
    this.disableInterface();
  }

  /**
   * Part of a callback function to all for control of the loading swf from the HTTPComms Object
   **/
  endOfComms():void
  {
    this.loadingImage.setVisible(false);
    this.enableInterface();
  }


  /**
   * Disable interface, turn off certain UI elements when a request is made to the server
   **/
  disableInterface():void
  {
    this.paginationData.enabled=false;
    this.refreshButton.enabled=false;
    this.refreshButton.buttonMode=false;
  }

  /**
   * Enable interface, turn on certain UI elements when a request is made to the server
   **/
  enableInterface():void
  {
    this.paginationData.enabled=true;
    this.refreshButton.enabled=true;
    this.refreshButton.buttonMode=true;
  }

  /**
   * Function called for Currency Threshold Checkbox Change event.
   **/
  currencyThresholdChange(e:Event):void
  {
    if (this.jsonReader.getScreenAttributes()["initialinputscreen"] != "filterscreen")
    {
      this.actionPath="outstandingmovement.do?";
      if (this.method == 'search' || this.initialinputscreen != 'E')
      {
        this.actionMethod="method=searchrefreshMvmntsSummaryDisplay";
        // Added for mantis 1443, send the scenario id to get movements with its query
        if (ExternalInterface.call('eval', 'scenarioId') != null){
          this.actionMethod+="&scenarioId=" + ExternalInterface.call('eval', 'scenarioId');
          this.actionMethod+="&currGrp=" + ExternalInterface.call('eval', 'currGrp');
          this.actionMethod+="&filterAcctType=" + this.jsonReader.getScreenAttributes()["filterAcctType"].toString().replace(/\%/g, "per;");
        }
      }
      else
      {
        this.actionMethod="method=refreshMvmntsSummaryDisplay";
        this.actionMethod+="&filterAcctType=" + this.jsonReader.getScreenAttributes()["filterAcctType"].toString().replace(/\%/g, "per;");
      }
      this.actionMethod+="&currentPage=" + 1;
      this.actionMethod+="&applyCurrencyThresholdInd=" + "1";
      if (this.currencyThreshold.selected)
      {
        this.actionMethod+="&applyCurrencyThreshold=N";
      }
      else
      {
        this.actionMethod+="&applyCurrencyThreshold=Y";
      }
      this.actionMethod+="&openFlag=" + ExternalInterface.call('eval', 'openFlag');

      let selectedList:string="";
      if (this.methodName != "search" && this.initialinputscreen == 'E')
      {
        for (let i:number=0; i < this.bottomGrid.dataProvider.length; i++)
        {
          selectedList+=this.bottomGrid.dataProvider[i].movement + ",";
        }
      }
      this.actionMethod+="&selectedList=" + selectedList;
      this.actionMethod+="&entityId=" + this.entityCombo.selectedLabel;
      this.actionMethod+="&currencyCode=" + ExternalInterface.call('eval', 'currencyCode');
      this.actionMethod+="&date=" + ExternalInterface.call('eval', 'dateStr');

      this.actionMethod+="&initialinputscreen=" + this.jsonReader.getScreenAttributes()["initialinputscreen"];
      this.actionMethod+="&posLvlId=" + ExternalInterface.call('eval', 'posLvlId');
      //getting access from the xml
      let access:string=this.jsonReader.getScreenAttributes()["access"];
      this.initialinputscreen= this.jsonReader.getScreenAttributes()["initialinputscreen"];
      if (this.initialinputscreen == 'E')
        this.actionMethod+="&totalFlag=" + ExternalInterface.call('eval', 'totalFlagOpenMovements');
      if (ExternalInterface.call('eval', 'workflow') != null)
        this.actionMethod+="&workflow=" + ExternalInterface.call('eval', 'workflow');
      if (this.methodName == "search" || this.initialinputscreen != 'E')
      {
        this.actionMethod+="&maxPage=" + this.jsonReader.getScreenAttributes()["pages"].maxPage;
        this.actionMethod+="&totalCount=" + this.jsonReader.getRowSize() + "&extBalStatus=" + ExternalInterface.call('eval', 'extBalStatusSearch');
        //check for access
        if (access != "readOnly")
        {

          if (this.initialinputscreen == "currencymonitor" || this.initialinputscreen == "entitymonitor")
          {
            this.actionMethod+="&valueDate=" + ExternalInterface.call('eval', 'valueDate');
            this.actionMethod+="&balanceType=";
            this.actionMethod+="&locationId=" + ExternalInterface.call('eval', 'locationIdCurrency');
          }
          if (this.initialinputscreen != "currencymonitor" && this.initialinputscreen != "entitymonitor")
          {
            if (this.initialinputscreen == "accountmonitor")
            {
              this.actionMethod+="&valueDate=" + ExternalInterface.call('eval', 'valueDate');
              this.actionMethod+="&balanceType=" + ExternalInterface.call('eval', 'balanceTypeAccount');
              this.actionMethod+="&accountId=" + ExternalInterface.call('eval', 'accountIdAccount');
              this.actionMethod+="&accountType=" + ExternalInterface.call('eval', 'accountTypeAccount');
            }
            if (this.initialinputscreen != "accountmonitor")
            {
              if (this.initialinputscreen == "accountbreakdownmonitor")
              {
                this.actionMethod+="&valueDate=" + ExternalInterface.call('eval', 'valueDate');
                this.actionMethod+="&balanceType=" + ExternalInterface.call('eval', 'balanceTypeAccount');
                this.actionMethod+="&accountId=" + ExternalInterface.call('eval', 'accountIdAccount');
              }
              if (this.initialinputscreen != "accountbreakdownmonitor")
              {
                if (this.initialinputscreen == "bookmonitor")
                {
                  this.actionMethod+="&valueDate=" + ExternalInterface.call('eval', 'valueDate');
                  this.actionMethod+="&bookCode=" + ExternalInterface.call('eval', 'bookCodeBook');
                }
                if (this.initialinputscreen != "bookmonitor")
                {
                  if (this.initialinputscreen == "mvmntsfromWorkFlowMonitor")
                  {
                    this.actionMethod+="&valueDate=" + ExternalInterface.call('eval', 'valueDate');
                    this.actionMethod+="&tabIndicator=" + ExternalInterface.call('eval', 'tabIndicatorWorkflow');
                    this.actionMethod+="&currGrp=" + ExternalInterface.call('eval', 'currGrp');
                    this.actionMethod+="&roleId=" + ExternalInterface.call('eval', 'roleId');
                    this.actionMethod+="&matchStatus=" + ExternalInterface.call('eval', 'matchStatusWorkflow');
                    this.actionMethod+="&predictStatus=" + ExternalInterface.call('eval', 'predictStatusWorkflow');
                    this.actionMethod+="&linkFlag=" + ExternalInterface.call('eval', 'linkFlagWorkflow');
                  }
                  if (this.initialinputscreen != "mvmntsfromWorkFlowMonitor")
                  {
                    if (this.initialinputscreen == "unSettledYesterday")
                    {
                      this.actionMethod+="&valueDate=" + ExternalInterface.call('eval', 'valueDate');
                      this.actionMethod+="&currGrp=" + ExternalInterface.call('eval', 'currGrp');
                      this.actionMethod+="&roleId=" + ExternalInterface.call('eval', 'roleId');
                    }
                    if (this.initialinputscreen != "unSettledYesterday")
                    {
                      if (this.initialinputscreen == "backValuedMvmnts")
                      {
                        this.actionMethod+="&valueDate=" + ExternalInterface.call('eval', 'valueDate');
                        this.actionMethod+="&currGrp=" + ExternalInterface.call('eval', 'currGrp');
                        this.actionMethod+="&roleId=" + ExternalInterface.call('eval', 'roleId');
                      }
                      if (this.initialinputscreen != "backValuedMvmnts")
                      {
                        if (this.initialinputscreen == "openUnexpectedMvmnts")
                        {
                          this.actionMethod+="&valueDate=" + ExternalInterface.call('eval', 'valueDate');
                          this.actionMethod+="&currGrp=" + ExternalInterface.call('eval', 'currGrp');
                          this.actionMethod+="&roleId=" + ExternalInterface.call('eval', 'roleId');
                        }
                        if (this.initialinputscreen != "openUnexpectedMvmnts")
                        {
                          this.actionMethod+="&archiveId=" + ExternalInterface.call('eval', 'archiveIdSearch');
                          this.actionMethod+="&filterFromSerach=" + ExternalInterface.call('eval', 'filterFromSerachSearch');
                          this.actionMethod+="&entityId=" + ExternalInterface.call('eval', 'entityIdSearch') + "&movementType=" + ExternalInterface.call('eval', 'movementTypeSearch') + "&sign=" + ExternalInterface.call('eval', 'signSearch') + "&predictStatus=" + ExternalInterface.call('eval', 'predictStatusSearch') + "&amountover=" + ExternalInterface.call('eval', 'amountoverSearch') + "&amountunder=" + ExternalInterface.call('eval', 'amountunderSearch') + "&currencyCode=" + ExternalInterface.call('eval', 'currencyCode') + "&paymentChannelId=" + ExternalInterface.call('eval', 'paymentChannelIdSearch') + "&beneficiaryId=" + ExternalInterface.call('eval', 'beneficiaryIdSearch') + "&custodianId=" + ExternalInterface.call('eval', 'custodianIdSearch') + "&positionlevel=" + ExternalInterface.call('eval', 'positionlevelSearch') + "&accountId=" + ExternalInterface.call('eval', 'accountIdSearch') + "&group=" + ExternalInterface.call('eval', 'groupSearch') + "&metaGroup=" + ExternalInterface.call('eval', 'metaGroupSearch') + "&bookCode=" + ExternalInterface.call('eval', 'bookCodeSearch') + "&valueFromDateAsString=" + ExternalInterface.call('eval', 'valueFromDateAsStringSearch') + "&valueToDateAsString=" + ExternalInterface.call('eval', 'valueToDateAsStringSearch') + "&timefrom=" + ExternalInterface.call('eval', 'timefromSearch') + "&timeto=" + ExternalInterface.call('eval', 'timetoSearch') + "&reference=" + ExternalInterface.call('eval', 'referenceSearch') + "&messageId=" + ExternalInterface.call('eval', 'messageIdSearch') + "&inputDateAsString=" + ExternalInterface.call('eval', 'inputDateAsStringSearch') + "&counterPartyId=" + ExternalInterface.call('eval', 'counterPartyIdSearch') +

                            "&matchStatus=" + ExternalInterface.call('eval', 'matchStatusSearch') + "&initialinputscreen=" + ""; //this.jsonReader.getScreenAttributes()["initialinputscreen"] + "&referenceFlag=" + ExternalInterface.call('eval', 'referenceFlagSearch') + "&accountClass=" + ExternalInterface.call('eval', 'accountClassSearch') + "&matchingparty=" + ExternalInterface.call('eval', 'matchingparty') + "&producttype=" + ExternalInterface.call('eval', 'producttype') +"&uetr=" + ExternalInterface.call('eval', 'uetr') + "&postingDateFrom=" + ExternalInterface.call('eval', 'postingDateFrom') + "&postingDateTo=" + ExternalInterface.call('eval', 'postingDateTo');

                        }
                      }
                    }
                  }
                }
              }
            }
          }
        }
      }

      this.actionMethod+="&selectedFilter=" + this.jsonReader.getScreenAttributes()["selectedFilter"] + "&selectedSort=" +  this.jsonReader.getScreenAttributes()["selectedSort"];
      //Code Modified for Mantis 1598 by sudhakar on 1-12-2011: Movement Summary Display' screen returns 'Connection Error
      this.actionMethod+="&tableScrollbarLeft=" + "&tableScrollbarTop=" + "&scrollbarLeft=" + "&scrollbarTop=" + "&isAmountDiffer=" + "&selectedMovementsAmount=" + "&menuAccessId=" + this.menuAccessId+'&openMovementFlagSearch=' + ExternalInterface.call('openMovementFlag');
      if (this.filterComboMSD.selectedIndex == -1)
      {
        this.requestParams["currentFilterConf"]=Encryptor.encode64(this.adhocLabel);
      }
      else
      {
        this.requestParams["currentFilterConf"]=Encryptor.encode64(this.filterComboMSD.selectedLabel);
      }
      if (ExternalInterface.call('eval', 'uetr'))
        this.actionMethod+="&uetr=" + ExternalInterface.call('eval', 'uetr');
        
      this.actionMethod+="&extraFilter=" + ExternalInterface.call('eval', 'extraFilter');

      if (this.methodName != "search" && this.initialinputscreen == 'E')
      {
        for (let i=0; i <this.bottomGrid.dataProvider.length; i++)
        {
          selectedList+=this.bottomGrid.dataProvider[i].movement + ",";
        }
      }
      this.actionMethod+="&selectedList=" + selectedList;
      this.keepSelected = true;
      this.inputData.url=this.baseURL + this.actionPath + this.actionMethod;
      //sending request
      this.inputData.send(this.requestParams);
    }
    else
    {
      this.filterScreen("Filter");
    }
  }

  /**
   * When a combobox is open then any requests to the server need to be cancelled
   **/
  openedCombo(event):void
  {
    this.comboOpen=true;
    if (this.inputData.isBusy())
    {
      this.enableInterface();
      this.inputData.cancel();
      if (event.currentTarget== 'SwtComboBox')
      {
        (event.currentTarget as SwtComboBox).interruptComms=true;
      }
    }
  }

  /**
   * When the combobox has closed, we need to know if the closure was caused by the user clicking away from the box
   **/
  closedCombo(event):void
  {
    this.comboOpen=false;
    if ((event.triggerEvent != null) && (event.triggerEvent.type == "mouseDownOutside"))
    {
      if (event.currentTarget =="SwtComboBox")
      {
        if ((event.currentTarget as SwtComboBox).interruptComms)
        {
          (event.currentTarget as SwtComboBox).interruptComms=false;
          this.updateData();
        }
      }
    }
  }

  /**
   * Update the data, this is called whenever a fresh of the data is required.
   * This could be called from either a change in a combobox selection of from the timer
   * This methods checks for the monitor type and loads the data accordingly.
   **/
  updateData():void
  {
    this.requestParams= [];
    this.actionMethod="";
    let entityId:string=this.entityCombo.selectedLabel;
    this.actionPath="outstandingmovement.do?";
    /*Define method the request to access*/
    let actionMethodName:string=ExternalInterface.call('eval', 'outstandingMethodName');
    this.actionMethod="method=" + actionMethodName;
    if (actionMethodName == "search")
    {
      this.actionMethod=this.actionMethod + "&entityId=" + ExternalInterface.call('eval', 'entityIdSearch') + "&movementType=" + ExternalInterface.call('eval', 'movementTypeSearch') + "&sign=" + ExternalInterface.call('eval', 'signSearch') + "&predictStatus=" + ExternalInterface.call('eval', 'predictStatusSearch') + "&amountover=" + ExternalInterface.call('eval', 'amountoverSearch') + "&amountunder=" + ExternalInterface.call('eval', 'amountunderSearch') + "&currencyCode=" + ExternalInterface.call('eval', 'currencyCode') + "&paymentChannelId=" + ExternalInterface.call('eval', 'paymentChannelIdSearch') + "&beneficiaryId=" + ExternalInterface.call('eval', 'beneficiaryIdSearch') + "&custodianId=" + ExternalInterface.call('eval', 'custodianIdSearch') + "&positionlevel=" + ExternalInterface.call('eval', 'positionlevelSearch') + "&accountId=" + ExternalInterface.call('eval', 'accountIdSearch') + "&group=" + ExternalInterface.call('eval', 'groupSearch') + "&metaGroup=" + ExternalInterface.call('eval', 'metaGroupSearch') + "&bookCode=" + ExternalInterface.call('eval', 'bookCodeSearch') + "&valueFromDateAsString=" + ExternalInterface.call('eval', 'valueFromDateAsStringSearch') + "&valueToDateAsString=" + ExternalInterface.call('eval', 'valueToDateAsStringSearch') + "&timefrom=" + ExternalInterface.call('eval', 'timefromSearch') + "&timeto=" + ExternalInterface.call('eval', 'timetoSearch') + "&reference=" + ExternalInterface.call('eval', 'referenceSearch') + "&messageId=" + ExternalInterface.call('eval', 'messageIdSearch') + "&inputDateAsString=" + ExternalInterface.call('eval', 'inputDateAsStringSearch') + "&counterPartyId=" + ExternalInterface.call('eval', 'counterPartyIdSearch') +

        "&matchStatus=" + ExternalInterface.call('eval', 'matchStatusSearch') + "&initialinputscreen=" +""// this.jsonReader.getScreenAttributes()["initialinputscreen"]
        + "&accountClass=" + ExternalInterface.call('eval', 'accountClassSearch') + "&isAmountDiffer=" + ExternalInterface.call('eval', 'isAmountDifferSearch') + "&referenceFlag=" + ExternalInterface.call('eval', 'referenceFlagSearch') + "&selectedMovementsAmount=" + ExternalInterface.call('eval', 'selectedMovementsAmountSearch');
    }
    else if (actionMethodName == 'getBookMonitorMvmnts')
    {
      this.actionMethod=this.actionMethod + "&entityId=" + ExternalInterface.call('eval', 'entityIdBook') + "&currencyCode=" + ExternalInterface.call('eval', 'currencyCode') + "&valueDate=" + ExternalInterface.call('eval', 'valueDate') + "&bookCode=" + ExternalInterface.call('eval', 'bookCodeBook') + "&selectedTabIndex=" + ExternalInterface.call('eval', 'selectedTabIndexBook') + "&initialinputscreen=" + this.jsonReader.getScreenAttributes()["initialinputscreen"];
    }
    this.inputData.url=this.baseURL + this.actionPath + this.actionMethod;
    this.inputData.send(this.requestParams);
  }

  /**
   * Function called to close the window when close button is clicked
   **/
  closeHandler():void
  {
    if (this.methodName != "search" && this.initialinputscreen == 'E')
    {
      for (let selectCount:number=0; selectCount < this.bottomGrid.dataProvider.length; selectCount++)
      {
        let lockflag1:any;
        let lock1:string="";
        let lockUser:string=ExternalInterface.call("checkLockOnServer", this.bottomGrid.dataProvider[selectCount].movement);
        if (this.currentUser.toLowerCase() == lockUser.toLowerCase() || lockUser.toString() == "true")
        {
          lockflag1=ExternalInterface.call("unlockMovementOnServer", this.bottomGrid.dataProvider[selectCount].movement);
          lock1=lockflag1.toString();
          if (!BooleanParser.parseBooleanValue(lock1))
          {
            this.swtAlert.warning(SwtUtil.getPredictMessage('label.movementCannotBeUnlocked', null), 'Warning');
          }
        }
      }
    }
    ExternalInterface.call("close");
  }


  /** This method used to reload the data or refresh data, whenever out of Memory exception is raised
   * gives an alert to the user and close the progress bar window and stop the timer */

  exportTimerEventHandler(evnt):void
  {


    /* let errorFlag:string="false";
     //get the memory flag string
     errorFlag=this.jsonReader.getMemoryErrorFlag();


     if (this.exportContainer.totalPages > exportContainer.maxPages || exportContainer.totalPages > 450)
     {
       //call the reloadData function
       if (errorFlag != "true")
       {
         dataRefresh(null);
       }

     }
     else if (exportContainer.totalPages < 200)
     {
       if (errorFlag != "true")
       {
         dataRefresh(null);
       }
       errorFlag="false";
       exportTimer.stop();
     }
     if (BooleanParser.parseBooleanValue(errorFlag))
     {

       let confirmMsg:string=stringUtil.substitute(ExternalInterface.call('getBundle', 'text', 'label-serverHasRunOutOfMemory', 'Server has run out of memory. Please contact your system administrator.  The following parameters should be reviewed, ') + "movementsummarydisplay.PageSize:" + ExternalInterface.call('eval', 'msdPageSize') + ExternalInterface.call('getBundle', 'text', 'label-maxNumberPages', ', maximum number of pages :') + ExternalInterface.call('eval', 'msdMaxPageSize') + "");
       //alert message
       SwtAlert.getInstance().show(confirmMsg, "OutOfMemoryError");
       //close the cancel popup window
       exportContainer.closeCancelPopup();
       //stop the timer
       exportTimer.stop();
     }*/

  }




  /**
   * This function is invoked when export button is clicked,
   * used to export the grid data.
   *
   * @param reportType: string - Report type CSV/Excel/PDF
   * @param startPage: int - Start page no
   * @param noOfPages: int - No of pages to be exported
   */

  export(reportType:string, startPage:number, noOfPages:number):void
  {


    let currencyThres:string=this.currencyThreshold.selected ? "Yes" : "No";

    let sourceScreen:string=this.jsonReader.getScreenAttributes()["initialinputscreen"];
    let accountId:string=ExternalInterface.call('eval', 'accountIdAccount');
    let balType:string=ExternalInterface.call('eval', 'balanceTypeAccount');
    let bookcode:string=ExternalInterface.call('eval', 'bookCodeBook');
    let roleId:string=ExternalInterface.call('eval', 'roleId');
    let workflow:string=ExternalInterface.call('eval', 'workflow');
    let accountTypeAccount:string = (this.jsonReader.getScreenAttributes()["pages"].accountTypeAccount == null)? "" : this.jsonReader.getScreenAttributes()["pages"].accountTypeAccount;
    this.requestParams['applyCurrencyThreshold']=  currencyThres ;
    this.requestParams['selectedFilter']=  (!(this.jsonReader.getScreenAttributes()["selectedFilter"])? "": this.jsonReader.getScreenAttributes()["selectedFilter"]) .replace(/&/g, "amp;").replace(/\+/g, "plus;") ;
    this.requestParams['selectedSort']=  this.jsonReader.getScreenAttributes()["selectedSort"] ;
    this.requestParams['exportType']=  reportType ;
    this.requestParams['pageCount']= noOfPages ;
    this.requestParams['currentPage']= startPage ;
    this.requestParams['initialinputscreen']=  sourceScreen ;
    this.requestParams['totalOverPagesValue']=  this.totalOverPagesValue.text ;
    this.requestParams['totalInPageValue']=  this.totalInPageValue.text ;
    //ExternalInterface.call("blockUIForDownload");
    if (sourceScreen == "filterscreen")
    {
      this.requestParams['accountId']= accountId ;
      this.requestParams['balanceType']= balType ;
      this.requestParams['accountType']= accountTypeAccount.replace(/&/g, "amp;").replace(/\+/g, "plus;") ;
      this.requestParams['selectedFilter']= ((this.jsonReader.getScreenAttributes()["selectedFilter"]) ? "" : this.jsonReader.getScreenAttributes()["selectedFilter"]).replace(/&/g, "amp;").replace(/\+/g, "plus;") ;
      this.requestParams['selectedSort']= this.jsonReader.getScreenAttributes()["selectedSort"] ;
      if (workflow != null)
      {
        this.requestParams['workflow']= workflow ;
      }
      this.requestParams['totalFlag']= ExternalInterface.call('eval', 'totalFlagOpenMovements') ;
    } else if(sourceScreen == "S") {
      this.requestParams['accountType']= accountTypeAccount.replace(/&/g, "amp;").replace(/\+/g, "plus;") ;
    } else if(sourceScreen == "R") {
      this.requestParams['accountType']= accountTypeAccount.replace(/&/g, "amp;").replace(/\+/g, "plus;") ;
      this.requestParams['accountId']= this.jsonReader.getScreenAttributes()["pages"].accountIdSearch;
    } else if(sourceScreen == "currencymonitor" || sourceScreen == "entitymonitor") {
      this.requestParams['valueDate']= ExternalInterface.call('eval', 'valueDate') ;
      this.requestParams['accountId']= accountId ;
      this.requestParams['balanceType']= balType ;
    } else if(sourceScreen == "accountmonitor" || sourceScreen == "accountbreakdownmonitor") {
      this.requestParams['valueDate']= ExternalInterface.call('eval', 'valueDate') ;
      this.requestParams['accountId']= accountId ;
      this.requestParams['balanceType']= balType ;
      this.requestParams['accountType']= accountTypeAccount.replace(/&/g, "amp;").replace(/\+/g, "plus;") ;
    } else if(sourceScreen == "bookmonitor") {
      this.requestParams['valueDate']= ExternalInterface.call('eval', 'valueDate') ;
      this.requestParams['bookCode']= bookcode ;
    } else if (sourceScreen == "mvmntsfromWorkFlowMonitor") {
      this.requestParams['tabIndicator']= ExternalInterface.call('eval', 'tabIndicatorWorkflow') ;
      this.requestParams['currGrp']= ExternalInterface.call('eval', 'currGrp') ;
      this.requestParams['roleId']= roleId ;
      this.requestParams['matchStatus']= ExternalInterface.call('eval', 'matchStatusWorkflow') ;
      this.requestParams['predictStatus']= ExternalInterface.call('eval', 'predictStatusWorkflow') ;
    } else if (sourceScreen == "unSettledYesterday" || sourceScreen == "backValuedMvmnts" || sourceScreen == "openUnexpectedMvmnts") {
      this.requestParams['currGrp']= ExternalInterface.call('eval', 'currGrp') ;
      this.requestParams['roleId']= roleId ;
      this.requestParams['balanceType']= ExternalInterface.call('eval', 'balanceTypeAccount') ;
    } else if(sourceScreen == "E") {
      this.requestParams['currentFilterConf']= Encryptor.encode64(this.filterComboMSD.selectedLabel) ;
      this.requestParams['accountType']= accountTypeAccount.replace(/&/g, "amp;").replace(/\+/g, "plus;") ;
      if (workflow != null)
      {
        this.requestParams['workflow']= workflow ;
      }
      this.requestParams['totalFlag']= ExternalInterface.call('eval', 'totalFlagOpenMovements') ;
    } else if(sourceScreen == "X"){
      this.requestParams['accountType']= accountTypeAccount.replace(/&/g, "amp;").replace(/\+/g, "plus;") ;
      this.requestParams['totalFlag']= ExternalInterface.call('eval', 'totalFlagOpenMovements') ;
      if (ExternalInterface.call('eval', 'scenarioId') != null){
        this.requestParams['scenarioId']= ExternalInterface.call('eval', 'scenarioId') ;
        this.requestParams['currGrp']= ExternalInterface.call('eval', 'currGrp') ;
      }

    }



    this.requestParams['filterAcctType']= this.jsonReader.getScreenAttributes()["filterAcctType"].toString().replace(/\%/g, "per;");
   /* this.requestParams['selectedMovementId']= this.requestParams['selectedList']= this.requestParams['selectedFilterStatus']= this.requestParams['selectedSortStatus']= this.requestParams['selectedSortDescending']= "";
    this.requestParams['method']=  ExternalInterface.call('eval', 'method');
    this.requestParams['selectedTab']= this.requestParams['date']= ExternalInterface.call('eval', 'dateStr');
    this.requestParams['selectedCurrencyCode']=this.requestParams['currencyCode']= ExternalInterface.call('eval', 'currencyCode');
    this.requestParams['selectedEntityId']= this.requestParams['selectedEntityName']= this.requestParams['selectedNextMovId']= this.requestParams['inputFrom']= this.requestParams['isNotesPresent']= "";
    this.requestParams['entityId']= this.entityCombo.selectedLabel;
    this.requestParams['prevEnabled']= this.requestParams['nextEnabled']= this.requestParams['messageId']= this.requestParams['filterFromSerach']="";
    this.requestParams['filterCriteria']= ExternalInterface.call('eval', 'filterCriteria');
    this.requestParams['balanceType']= ExternalInterface.call('eval', 'balanceTypeAccount');
    this.requestParams['posLvlId']= ExternalInterface.call('eval', 'posLvlId');
    this.requestParams['archiveId']= ExternalInterface.call('eval', 'archiveIdSearch');
    this.requestParams['amountover'] = ExternalInterface.call('amountOver');
    this.requestParams['amountunder'] = ExternalInterface.call('amountUnder');
    this.requestParams['group'] = ExternalInterface.call('getGroup');
    this.requestParams['metaGroup'] = ExternalInterface.call('getMetagroup');
    this.requestParams['valueFromDate'] = ExternalInterface.call('valueFromDate');
    this.requestParams['valueToDate'] = ExternalInterface.call('valueToDate');


    this.requestParams['timefrom'] = ExternalInterface.call('timefrom');
    this.requestParams['timeto'] = ExternalInterface.call('timeto');
    this.requestParams['inputDate'] = ExternalInterface.call('inputDate');
    this.requestParams['fintrade'] = ExternalInterface.call('eval', 'fintradeSearch');
    this.requestParams['reference'] = ExternalInterface.call('reference');
    this.requestParams['amountDifference'] = "0";
    this.requestParams['posLvlSelectedList'] = this.requestParams['refreshScreen']  = this.requestParams['tableScrollbarLeft']  = this.requestParams['tableScrollbarTop']  = this.requestParams['scrollbarLeft']= this.requestParams['scrollbarTop'] = "";
    this.requestParams['matchStatus']= ExternalInterface.call('eval', 'matchStatusWorkflow');
    this.requestParams['predictStatus']= ExternalInterface.call('eval', 'predictStatusWorkflow');
    this.requestParams['linkFlag']= ExternalInterface.call('eval', 'linkFlagWorkflow');
    this.requestParams['maxPage']= this.jsonReader.getScreenAttributes()["pages"].maxPage;
    this.requestParams['totalCount']= this.jsonReader.getRowSize();
    this.requestParams['refreshFromMatchScreen']= "";
    this.requestParams['menuAccessId'] =ExternalInterface.call('eval', 'menuAccessId');
    this.requestParams['isAmountDiffer'] = ExternalInterface.call('eval', 'isAmountDifferSearch');
    this.requestParams['selectedMovementsAmount']= ExternalInterface.call('eval', 'selectedMovementsAmountSearch');
    this.requestParams['applyCurrencyThresholdInd']= "0";
    this.requestParams['locationId']= "";
    this.requestParams['refFlagFilterSearch'] = ExternalInterface.call('referenceFlag');
    this.requestParams['openMovementFlagSearch'] = ExternalInterface.call('openMovementFlag');
    this.requestParams['matchingparty'] = ExternalInterface.call('eval', 'matchingparty');
    this.requestParams['uetr'] = ExternalInterface.call('eval', 'uetr');
    this.requestParams['producttype'] = ExternalInterface.call('eval', 'producttype');
    this.requestParams['postingDateFrom'] = ExternalInterface.call('eval', 'postingDateFrom');
    this.requestParams['postingDateTo'] = ExternalInterface.call('eval', 'postingDateTo');*/
    this.requestParams['tokenForDownload'] = new Date().getTime();
    this.requestParams['screen'] = SwtUtil.getPredictMessage('movementSummDisplay.title.Window', null);
    let requestParams =Object.assign({},this.requestParams);
    ExternalInterface.call("sendParams", requestParams)


  }

  /**
   * This function is used to close the pop up window
   **/
  closePopup(source:string = null):void
  {
    try {
      this.exportContainer.closeCancelPopup();  
    } catch (error) {
    }
    if(!StringUtils.isEmpty(source)) {
      if(source == "mem") {
        let confirmMsg:string=StringUtils.substitute(ExternalInterface.call('getBundle', 'text', 'label-serverHasRunOutOfMemory', 'Server has run out of memory. Please contact your system administrator.  The following parameters should be reviewed, ') + "movementsummarydisplay.PageSize:" + ExternalInterface.call('eval', 'msdPageSize') + ExternalInterface.call('getBundle', 'text', 'label-maxNumberPages', ', maximum number of pages :') + ExternalInterface.call('eval', 'msdMaxPageSize') + "");
        //alert message
        this.swtAlert.show(confirmMsg, "OutOfMemoryError");
      }else if(source == "err") {
        this.swtAlert.show(ExternalInterface.call('getBundle', 'text', 'alert-contactAdmin', 'An Error occurred while exporting, Please contact your System Administrator') , ExternalInterface.call('getBundle', 'text', 'alert-error', 'Error'));
      }
    }

  }

  /**
   * This function is used to call the data refresh, whenever click on cancel button
   **/
  exportCancel(event):void
  {
    //exportTimer.stop();
    this.dataRefresh(event);

    // Calls to start the Http communication
    this.cancelExport.cbStart=this.startOfComms.bind(this);
    // Calls to stop the Http communication
    this.cancelExport.cbStop=this.endOfComms.bind(this);
    // Calls the inputDataResult function to load the datagrid
    this.cancelExport.cbResult= (event) => {
      this.ExportCanceled(event);
    }
    // Sets the action path for Interface Monitor
    this.actionPath="outstandingmovement.do?";
    // Sets the action method to get the Interface Monitor Details
    this.actionMethod="method=cancelILMExport";
    // Sets the full URL for Interface Monitor
    this.cancelExport.url=this.baseURL + this.actionPath + this.actionMethod;
    // Sets the flag for encoding URL to false
    this.cancelExport.encodeURL=false;
    // Calls the inputDataFault function
    this.cancelExport.cbFault=this.inputDataFault.bind(this);
    this.requestParams["cancelExport"] = "true";
    // Send the request to the server
    this.cancelExport.send(this.requestParams);

  }

  ExportCanceled(event):void
  {
  }

  checkSelectedMovementsAmounts():boolean
  {

    let allowMatch:boolean=true;
    let amt0:number=parseFloat(this.bottomGrid.dataProvider[0].amount);
    let sign0:string=this.bottomGrid.dataProvider[0].sign;
    for (let i:number=1; i < this.bottomGrid.dataProvider.length; i++)
    {
      let amt1:number=parseFloat(this.bottomGrid.dataProvider[i].amount);
      let sign1:string=this.bottomGrid.dataProvider[i].sign;
      if (amt0 != amt1 || sign0 != sign1)
      {
        allowMatch=false;
      }

    }
    return allowMatch;
  }

  alertListener(eventObj):void
  {
    // Check to see if the OK button was pressed.
    let allowMatch:boolean=false;
    if (eventObj.detail == Alert.OK)
    {
      allowMatch=true;
    }
    if (allowMatch)
    {
      let selectedList:string="";
      for (let i:number=0; i < this.bottomGrid.dataProvider.length; i++)
      {
        selectedList+=this.bottomGrid.dataProvider[i].movement + ",";
      }
      let entityId:string=this.entityCombo.selectedLabel;
      this.actionPath="outstandingmovement.do?";
      let threshold:string="N";
      let thresholdInd:string="1";
      if (!this.currencyThreshold.selected)
      {
        threshold="Y";
        thresholdInd="1";
      }
      if (this.confirmFlag)
      {
        this.diffText.text="";
        this.requestParams= [];
        let initialScreen:string =this.jsonReader.getScreenAttributes()["initialinputscreen"];
        if (initialScreen == "filterscreen")
        {
          initialScreen="E";
        }
        this.actionMethod="method=confirmMatch" + "&initialinputscreen=" + initialScreen + "&totalFlag=" + ExternalInterface.call('eval', 'totalFlagOpenMovements') + "&posLvlId=" + ExternalInterface.call('eval', 'posLvlId') + "&selectedCurrencyCode=" + ExternalInterface.call('eval', 'currencyCode') + "&selectedList=" + selectedList + "&selectedTab=" + ExternalInterface.call('eval', 'tabPressed') + "&selectedFilter=" + this.jsonReader.getScreenAttributes()["selectedFilter"] + "&selectedSort=" + this.jsonReader.getScreenAttributes()["selectedSort"] + "&currentPage=" + this.jsonReader.getScreenAttributes()["pages"].currentPage + "&applyCurrencyThreshold=" + threshold + "&applyCurrencyThresholdInd=" + thresholdInd + "&totalCount=" + this.jsonReader.getRowSize() + "&tableScrollbarLeft=" + "&entityId=" + entityId + "&tableScrollbarTop=" + "&scrollbarLeft=" + "&scrollbarTop="+"&openMovementFlagSearch=" + ExternalInterface.call('openMovementFlag');
        this.actionMethod+="&filterAcctType=" + this.jsonReader.getScreenAttributes()["filterAcctType"].toString().replace(/\%/g, "per;");
        if (ExternalInterface.call('eval', 'workflow') != null)
          this.actionMethod+="&workflow=" + ExternalInterface.call('eval', 'workflow');
        this.inputData.url=this.baseURL + this.actionPath + this.actionMethod;
        this.requestParams["movement.id.entityId"]=entityId;
        if (this.filterComboMSD.selectedIndex == -1)
        {
          this.requestParams["currentFilterConf"]=Encryptor.encode64(this.adhocLabel);
        }
        else
        {
          this.requestParams["currentFilterConf"]=Encryptor.encode64(this.filterComboMSD.selectedLabel);
        }
        this.requestParams["openMovementFlagSearch"]=ExternalInterface.call('openMovementFlag');
        /*Make the request with requestParams if any*/
        this.inputData.send(this.requestParams);
        this.selectedMvmts=[];
        this.cGrid.selectedIndex = -1;
        this.cGrid.selectedIndices = [];
      }
      if (this.matchFlag)
      {
        allowMatch=false;
        let matchIdFlag:boolean=false;
        let matchId:string="";
        for (let selMatchCount:number=0; selMatchCount < this.bottomGrid.dataProvider.length; selMatchCount++)
        {
          if ((this.bottomGrid.dataProvider[selMatchCount].matchid).toString().length <= 0)
          {
            matchIdFlag=true;
            break;
          }
          else
          {
            matchId=this.bottomGrid.dataProvider[selMatchCount].matchid;
          }
        }
        this.diffText.text="";
        let selectedList:string="";
        for (let i:number=0; i < this.bottomGrid.dataProvider.length; i++)
        {
          selectedList+=this.bottomGrid.dataProvider[i].movement + ",";
        }
        let entityId:string=this.entityCombo.selectedLabel;
        this.actionPath="movementmatchdisplay.do?";
        let threshold:string="N";
        let thresholdInd:string="1";
        if (!this.currencyThreshold.selected)
        {
          threshold="Y";
          thresholdInd="1";
        }
        this.actionMethod="method=offeredMatch&selectedList=" + selectedList + "&selectedTab=" + ExternalInterface.call('eval', 'tabPressed') + "&selectedCurrencyCode=" + ExternalInterface.call('eval', 'currencyCode') + "&entityCode=" + entityId + "&currentPage=" + this.jsonReader.getScreenAttributes()["pages"].currentPage + "&tableScrollbarLeft=" + "&tableScrollbarTop=" + "&scrollbarLeft=" + "&scrollbarTop=";
        if (ExternalInterface.call('eval', 'workflow') != null)
          this.actionMethod+="&workflow=" + ExternalInterface.call('eval', 'workflow');
        if (!matchIdFlag)
          this.actionMethod+="&matchId=" + matchId;
        this.inputData.url=this.baseURL + this.actionPath + this.actionMethod;
        /*Make the request with requestParams if any*/
        ExternalInterface.call("offeredMatches", this.inputData.url);
        this.cGrid.selectedIndices= [];
        this.bottomGrid.dataProvider= [];
        let array = [];
        for (let i = 0; i <this.bottomGrid.dataProvider.length; i++) {
          array.push(this.bottomGrid.angularGridInstance.gridService.getDataItemByRowNumber(i));
        }
        this.bottomGrid.angularGridInstance.gridService.deleteItems(array);
        this.selectedMvmts=[];
        this.cGrid.selectedIndex = -1;
        this.cGrid.selectedIndices = [];
        if (matchIdFlag)
        {
          this.actionPath="outstandingmovement.do?";
          this.actionMethod="method=refreshMvmntsSummaryDisplay";
          let selectedList:string="";
          if (this.methodName != "search" && this.initialinputscreen == 'E')
          {
            for (let i:number=0; i < this.bottomGrid.dataProvider.length; i++)
            {
              selectedList+=this.bottomGrid.dataProvider[i].movement + ",";
            }
          }
          this.actionMethod+="&currentPage=" + this.jsonReader.getScreenAttributes()["pages"].currentPage;
          this.actionMethod+="&selectedList=" + selectedList;
          this.actionMethod+="&applyCurrencyThresholdInd=1";
          if (this.currencyThreshold.selected)
          {
            this.actionMethod+="&applyCurrencyThreshold=N";
          }
          else
          {
            this.actionMethod+="&applyCurrencyThreshold=Y";
          }
          this.actionMethod+="&entityId=" + this.entityCombo.selectedLabel;
          this.actionMethod+="&currencyCode=" + ExternalInterface.call('eval', 'currencyCode');
          this.actionMethod+="&date=" + ExternalInterface.call('eval', 'dateStr');
          this.actionMethod+='&selectedSort=' + this.jsonReader.getScreenAttributes()["selectedSort"];
          this.actionMethod+='&selectedFilter=' + this.jsonReader.getScreenAttributes()["selectedFilter"];

          this.actionMethod+="&initialinputscreen=" +this.jsonReader.getScreenAttributes()["initialinputscreen"];
          this.actionMethod+="&posLvlId=" + ExternalInterface.call('eval', 'posLvlId');
          let access:string=this.jsonReader.getScreenAttributes()["access"];
          this.initialinputscreen= this.jsonReader.getScreenAttributes()["initialinputscreen"];
          if (this.initialinputscreen == 'E')
            this.actionMethod+="&totalFlag=" + ExternalInterface.call('eval', 'totalFlagOpenMovements');

          this.actionMethod+="&tableScrollbarLeft=" + "&tableScrollbarTop=" + "&scrollbarLeft=" + "&scrollbarTop=" + "&isAmountDiffer=" + "&selectedMovementsAmount=";
          this.inputData.url=this.baseURL + this.actionPath + this.actionMethod;
          this.requestParams["currentFilterConf"]=Encryptor.encode64(this.filterComboMSD.selectedLabel);
          this.requestParams["openMovementFlagSearch"]=ExternalInterface.call('openMovementFlag');
          this.inputData.send(this.requestParams);
        }
      }
      if (this.suspendFlag)
      {
        this.diffText.text="";
        let initialScreen:string= this.jsonReader.getScreenAttributes()["initialinputscreen"];
        if (initialScreen == "filterscreen")
        {
          initialScreen="E";
        }
        this.actionMethod="method=suspendMatch" + "&initialinputscreen=" + initialScreen + "&totalFlag=" + ExternalInterface.call('eval', 'totalFlagOpenMovements') + "&posLvlId=" + ExternalInterface.call('eval', 'posLvlId') + "&selectedCurrencyCode=" + ExternalInterface.call('eval', 'currencyCode') + "&selectedList=" + selectedList + "&selectedTab=" + ExternalInterface.call('eval', 'tabPressed') + "&selectedFilter=" + this.jsonReader.getScreenAttributes()["selectedFilter"] + "&selectedSort=" + this.jsonReader.getScreenAttributes()["selectedSort"] + "&currentPage=" + this.jsonReader.getScreenAttributes()["pages"].currentPage + "&applyCurrencyThreshold=" + threshold + "&applyCurrencyThresholdInd=" + thresholdInd + "&totalCount=" + this.jsonReader.getRowSize() + "&tableScrollbarLeft=" + "&tableScrollbarTop=" + "&scrollbarLeft=" + "&entityId=" + entityId + "&scrollbarTop="+"&openMovementFlagSearch=" + ExternalInterface.call('openMovementFlag');
        this.actionMethod+="&filterAcctType=" + this.jsonReader.getScreenAttributes()["filterAcctType"].toString().replace(/\%/g, "per;");
        if (ExternalInterface.call('eval', 'workflow') != null)
          this.actionMethod+="&workflow=" + ExternalInterface.call('eval', 'workflow');
        this.inputData.url=this.baseURL + this.actionPath + this.actionMethod;
        this.requestParams["movement.id.entityId"]=entityId;
        if (this.filterComboMSD.selectedIndex == -1)
        {
          this.requestParams["currentFilterConf"]=Encryptor.encode64(this.adhocLabel);
        }
        else
        {
          this.requestParams["currentFilterConf"]=Encryptor.encode64(this.filterComboMSD.selectedLabel);
        }
        this.requestParams["openMovementFlagSearch"]=ExternalInterface.call('openMovementFlag');
        /*Make the request with requestParams if any*/

        this.inputData.send(this.requestParams);
        this.selectedMvmts=[];
        this.cGrid.selectedIndex = -1;
        this.cGrid.selectedIndices = [];
      }
      if (this.reconFlag)
      {
        this.diffText.text="";
        let initialScreen:string = this.jsonReader.getScreenAttributes()["initialinputscreen"];
        if (initialScreen == "filterscreen")
        {
          initialScreen="E";
        }
        this.actionMethod="method=reconMatch" + "&initialinputscreen=" + initialScreen + "&totalFlag=" + ExternalInterface.call('eval', 'totalFlagOpenMovements') + "&posLvlId=" + ExternalInterface.call('eval', 'posLvlId') + "&selectedCurrencyCode=" + ExternalInterface.call('eval', 'currencyCode') + "&selectedList=" + selectedList + "&selectedTab=" + ExternalInterface.call('eval', 'tabPressed') + "&selectedFilter=" + this.jsonReader.getScreenAttributes()["selectedFilter"] + "&selectedSort=" + this.jsonReader.getScreenAttributes()["selectedSort"] + "&currentPage=" + this.jsonReader.getScreenAttributes()["pages"].currentPage + "&applyCurrencyThreshold=" + threshold + "&applyCurrencyThresholdInd=" + thresholdInd + "&totalCount=" + this.jsonReader.getRowSize() + "&tableScrollbarLeft=" + "&tableScrollbarTop=" + "&entityId=" + entityId + "&scrollbarLeft=" + "&scrollbarTop="+"&openMovementFlagSearch=" + ExternalInterface.call('openMovementFlag');
        this.actionMethod+="&filterAcctType=" + this.jsonReader.getScreenAttributes()["filterAcctType"].toString().replace(/\%/g, "per;");
        if (ExternalInterface.call('eval', 'workflow') != null)
          this.actionMethod+="&workflow=" + ExternalInterface.call('eval', 'workflow');
        this.inputData.url=this.baseURL + this.actionPath + this.actionMethod;
        this.requestParams["movement.id.entityId"]=entityId;
        if (this.filterComboMSD.selectedIndex == -1)
        {
          this.requestParams["currentFilterConf"]=Encryptor.encode64(this.adhocLabel);
        }
        else
        {
          this.requestParams["currentFilterConf"]=Encryptor.encode64(this.filterComboMSD.selectedLabel);
        }
        this.requestParams["openMovementFlagSearch"]=ExternalInterface.call('openMovementFlag');
        /*Make the request with requestParams if any*/

        this.inputData.send(this.requestParams);
        this.selectedMvmts=[];
        this.cGrid.selectedIndex = -1;
        this.cGrid.selectedIndices = [];
      }
    }
  }


  confirmResultevent(event):void
  {
    this.lastRecievedJSON = event;
  }

  confirm():void
  {
    this.confirmFlag=true;
    this.matchFlag=false;
    this.suspendFlag=false;
    this.reconFlag=false;
    let allowMatch:boolean=true;
    if (this.checkMvtStatus())
    {
      if (!this.checkSelectedMovementsAmounts())
      {
        allowMatch=false;
        let title:string='Microsoft Internet Explorer';
        let testMessageY:string=  SwtUtil.getPredictMessage('label.amountSelectedMovementsDiffer', null)
        this.swtAlert.warning(testMessageY, title, Alert.OK | Alert.CANCEL, this, this.alertListener.bind(this), null);
      }
      else if (allowMatch)
      {
        let selectedList:string="";
        for (let i:number=0; i < this.bottomGrid.dataProvider.length; i++)
        {
          selectedList+=this.bottomGrid.dataProvider[i].movement + ",";
        }
        let entityId:string=this.entityCombo.selectedLabel;
        this.actionPath="outstandingmovement.do?";

        let threshold:string="N";
        let thresholdInd:string="1";
        if (this.currencyThreshold.selected == false)
        {
          threshold="Y";
          thresholdInd="1";
        }
        let initialScreen:string = this.jsonReader.getScreenAttributes()["initialinputscreen"];
        if (initialScreen == "filterscreen")
        {
          initialScreen="E";
        }
        this.actionMethod="method=confirmMatch" + "&initialinputscreen=" + initialScreen + "&totalFlag=" + ExternalInterface.call('eval', 'totalFlagOpenMovements') + "&posLvlId=" + ExternalInterface.call('eval', 'posLvlId') + "&selectedCurrencyCode=" + ExternalInterface.call('eval', 'currencyCode') + "&selectedList=" + selectedList + "&selectedTab=" + ExternalInterface.call('eval', 'tabPressed') + "&selectedFilter=" + this.jsonReader.getScreenAttributes()["selectedFilter"] + "&selectedSort=" + this.jsonReader.getScreenAttributes()["selectedSort"] + "&currentPage=" + this.jsonReader.getScreenAttributes()["pages"].currentPage + "&applyCurrencyThreshold=" + threshold + "&applyCurrencyThresholdInd=" + thresholdInd + "&totalCount=" + this.jsonReader.getRowSize() + "&tableScrollbarLeft=" + "&tableScrollbarTop=" + "&entityId=" + entityId + "&scrollbarLeft=" + "&scrollbarTop=";
        this.actionMethod+="&filterAcctType=" + this.jsonReader.getScreenAttributes()["filterAcctType"].toString().replace(/\%/g, "per;");
        if (ExternalInterface.call('eval', 'workflow') != null)
          this.actionMethod+="&workflow=" + ExternalInterface.call('eval', 'workflow');
        this.inputData.url=this.baseURL + this.actionPath + this.actionMethod;
        this.requestParams["movement.id.entityId"]=entityId;
        if (this.filterComboMSD.selectedIndex == -1)
        {
          this.requestParams["currentFilterConf"]=Encryptor.encode64(this.adhocLabel);
        }
        else
        {
          this.requestParams["currentFilterConf"]=Encryptor.encode64(this.filterComboMSD.selectedLabel);
        }
        this.requestParams["openMovementFlagSearch"]=ExternalInterface.call('openMovementFlag');
        /*Make the request with requestParams if any*/

        this.inputData.send(this.requestParams);
        this.cGrid.selectedIndex = -1;
        this.cGrid.selectedIndices = [];
        this.selectedMvmts=[];

      }
    }
  }

  reconcile():void
  {
    this.confirmFlag=false;
    this.matchFlag=false;
    this.suspendFlag=false;
    this.reconFlag=true;
    let allowMatch:boolean=true;
    if (this.checkMvtStatus())
    {
      if (!this.checkSelectedMovementsAmounts())
      {
        allowMatch=false;
        let title:string='Microsoft Internet Explorer';
        let testMessageY:string='The amounts of the selected movements differ. Do you want to continue?';
        this.swtAlert.question(testMessageY, title, Alert.OK | Alert.CANCEL, this, this.alertListener.bind(this), null);
      }
      else if (allowMatch)
      {
        let selectedList:string="";
        for (let i:number=0; i < this.bottomGrid.dataProvider.length; i++)
        {
          selectedList+=this.bottomGrid.dataProvider[i].movement + ",";
        }
        let entityId:string= this.entityCombo.selectedLabel;
        this.actionPath="outstandingmovement.do?";
        let threshold:string="N";
        let thresholdInd:string="1";
        if (!this.currencyThreshold.selected)
        {
          threshold="Y";
          thresholdInd="1";
        }
        let initialScreen:string = this.jsonReader.getScreenAttributes()["initialinputscreen"];
        if (initialScreen == "filterscreen")
        {
          initialScreen="E";
        }
        //TODO
        this.actionMethod="method=reconMatch" + "&initialinputscreen=" + initialScreen + "&totalFlag=" + ExternalInterface.call('eval', 'totalFlagOpenMovements') + "&posLvlId=" + ExternalInterface.call('eval', 'posLvlId') + "&selectedCurrencyCode=" + ExternalInterface.call('eval', 'currencyCode') + "&selectedList=" + selectedList + "&selectedTab=" + ExternalInterface.call('eval', 'tabPressed') + "&selectedFilter=" + this.jsonReader.getScreenAttributes()["selectedFilter"] + "&selectedSort=" + this.jsonReader.getScreenAttributes()["selectedSort"] + "&currentPage=" + this.jsonReader.getScreenAttributes()["pages"].currentPage + "&applyCurrencyThreshold=" + threshold + "&applyCurrencyThresholdInd=" + thresholdInd + "&totalCount=" + this.jsonReader.getRowSize() + "&tableScrollbarLeft=" + "&tableScrollbarTop=" + "&scrollbarLeft=" + "&entityId=" + entityId + "&scrollbarTop=";
        this.actionMethod+="&filterAcctType=" + this.jsonReader.getScreenAttributes()["filterAcctType"].toString().replace(/\%/g, "per;");
        if (ExternalInterface.call('eval', 'workflow') != null)
          this.actionMethod+="&workflow=" + ExternalInterface.call('eval', 'workflow');
        this.inputData.url=this.baseURL + this.actionPath + this.actionMethod;
        this.requestParams["movement.id.entityId"]=entityId;
        if (this.filterComboMSD.selectedIndex == -1)
        {
          this.requestParams["currentFilterConf"]=Encryptor.encode64(this.adhocLabel);
        }
        else
        {
          this.requestParams["currentFilterConf"]=Encryptor.encode64(this.filterComboMSD.selectedLabel);
        }
        this.requestParams["openMovementFlagSearch"]=ExternalInterface.call('openMovementFlag');
        this.inputData.send(this.requestParams);
        this.cGrid.selectedIndex = -1;
        this.cGrid.selectedIndices = [];
        this.selectedMvmts=[];

      }
    }
  }

  suspend():void
  {
    this.confirmFlag=false;
    this.matchFlag=false;
    this.suspendFlag=true;
    this.reconFlag=false;
    let allowMatch:boolean=true;
    if (this.checkMvtStatus())
    {
      if (!this.checkSelectedMovementsAmounts())
      {
        allowMatch=false;
        let title:string='Microsoft Internet Explorer';
        let testMessageY:string= 'The amounts of the selected movements differ. Do you want to continue?'; //ExternalInterface.call('getBundle', 'text', 'label-amountsDiffer', 'The amounts of the selected movements differ. Do you want to continue?');
        this.swtAlert.warning(testMessageY, title, Alert.OK | Alert.CANCEL, this, this.alertListener.bind(this), null);
      }
      else if (allowMatch)
      {
        let selectedList:string="";
        for (let i:number=0; i < this.bottomGrid.dataProvider.length; i++)
        {
          selectedList+=this.bottomGrid.dataProvider[i].movement + ",";
        }
        let entityId:string=this.entityCombo.selectedLabel;
        this.actionPath="outstandingmovement.do?";
        let threshold:string="N";
        let thresholdInd:string="1";
        if (!this.currencyThreshold.selected)
        {
          threshold="Y";
          thresholdInd="1";
        }
        let initialScreen =this.jsonReader.getScreenAttributes()["initialinputscreen"];
        if (initialScreen == "filterscreen")
        {
          initialScreen="E";
        }
        this.actionMethod="method=suspendMatch" + "&initialinputscreen=" + initialScreen + "&totalFlag=" + ExternalInterface.call('eval', 'totalFlagOpenMovements') + "&posLvlId=" + ExternalInterface.call('eval', 'posLvlId') + "&selectedCurrencyCode=" + ExternalInterface.call('eval', 'currencyCode') + "&selectedList=" + selectedList + "&selectedTab=" + ExternalInterface.call('eval', 'tabPressed') + "&selectedFilter=" + this.jsonReader.getScreenAttributes()["selectedFilter"] + "&selectedSort=" + this.jsonReader.getScreenAttributes()["selectedSort"] + "&currentPage=" + this.jsonReader.getScreenAttributes()["pages"].currentPage + "&applyCurrencyThreshold=" + threshold + "&applyCurrencyThresholdInd=" + thresholdInd + "&totalCount=" + this.jsonReader.getRowSize() + "&tableScrollbarLeft=" + "&entityId=" + entityId + "&tableScrollbarTop=" + "&scrollbarLeft=" + "&scrollbarTop=";
        this.actionMethod+="&filterAcctType=" + this.jsonReader.getScreenAttributes()["filterAcctType"].toString().replace(/\%/g, "per;");
        if (ExternalInterface.call('eval', 'workflow') != null)
          this.actionMethod+="&workflow=" + ExternalInterface.call('eval', 'workflow');
        this.inputData.url=this.baseURL + this.actionPath + this.actionMethod;
        this.requestParams["movement.id.entityId"]=entityId;
        if (this.filterComboMSD.selectedIndex == -1)
        {
          this.requestParams["currentFilterConf"]=Encryptor.encode64(this.adhocLabel);
        }
        else
        {
          this.requestParams["currentFilterConf"]=Encryptor.encode64(this.filterComboMSD.selectedLabel);
        }
        this.requestParams["openMovementFlagSearch"]=ExternalInterface.call('openMovementFlag');
        this.inputData.send(this.requestParams);
        this.cGrid.selectedIndex = -1;
        this.cGrid.selectedIndices = [];
        this.selectedMvmts=[];

      }
    }
  }

  checkMvtStatus():boolean
  {
    // variable to hold the flag
    let checkFlag:boolean=true;
    // variable to hold the flag status
    let mvtFlagStatus: any;
    // variable to hold the flag status in string
    let mvtFlag:string="";
    // get the movement flag status
    mvtFlagStatus=ExternalInterface.call("checkMovementStatus", this.selectedMovements, this.entityCombo.selectedLabel);
    mvtFlag=mvtFlagStatus.toString();
    if (!BooleanParser.parseBooleanValue(mvtFlag))
    {
      checkFlag=false;
      this.swtAlert.warning('This movement has been changed', "", Alert.OK, this, this.refreshScreen.bind(this), null); //ExternalInterface.call('getBundle', 'text', 'label-movementChanged', 'This movement has been changed')
    }
    return checkFlag;
  }

  refreshScreen(event):void
  {
    this.cGrid.selectedIndex=-1;
    // unlock the selected movement
    for (let selectCount:number=0; selectCount < this.bottomGrid.dataProvider.length; selectCount++)
    {
      let lockflag1:any;
      let lockUser:string=ExternalInterface.call("checkLockOnServer", this.bottomGrid.dataProvider[selectCount].movement);
      if (this.currentUser.toLowerCase() == lockUser.toLowerCase() || lockUser.toString() == "true")
        lockflag1=ExternalInterface.call("unlockMovementOnServer", this.bottomGrid.dataProvider[selectCount].movement);
    }
    if ( this.bottomGrid != null &&  this.bottomGrid.dataProvider.length > 0){
    this.bottomGrid.dataProvider=[];
    let selectedList ="";
      for(let z: number = 0; z < this.bottomGrid.dataProvider.length; z++ ) {
        selectedList+= this.bottomGrid.dataProvider[z].movement +',';
      }
      ExternalInterface.call('setSelectedMovementForLock',selectedList);
    }
    this.dataRefresh(null);
  }

  match():void
  {
    this.confirmFlag=false;
    this.matchFlag=true;
    this.suspendFlag=false;
    this.reconFlag=false;
    let allowMatch:boolean=true;
    let ctPage:string=this.jsonReader.getScreenAttributes()["pages"].currentPage;
    if (this.checkMvtStatus())
    {
      if (allowMatch)
      {
        var matchFlag:boolean=false;
        let openFlag:boolean=false;
        let emptyFlag:boolean=false;
        let nonEmptyFlag:boolean=false;
        let matchId:string=null;
        for (let selMatchCount:number=0; selMatchCount < this.bottomGrid.dataProvider.length; selMatchCount++)
        {
          if (this.bottomGrid.dataProvider[selMatchCount].matchid.toString().length <= 0)
          {
            emptyFlag=true;
          }
          if (this.bottomGrid.dataProvider[selMatchCount].matchid.toString().length > 0)
          {
            nonEmptyFlag=true;
          }
          if (emptyFlag && nonEmptyFlag)
          {
            openFlag=true;
          }
          if ((matchId == null) && (this.bottomGrid.dataProvider[selMatchCount].matchid.toString().length > 0))
          {
            matchId=this.bottomGrid.dataProvider[selMatchCount].matchid;
          }
          else if (this.bottomGrid.dataProvider[selMatchCount].matchid.toString().length > 0)
          {
            if (matchId != this.bottomGrid.dataProvider[selMatchCount].matchid)
            {
              matchFlag=true;
              break;
            }
          }
        }
        if (openFlag)
        {
          let title:string='Microsoft Internet Explorer';
          let testMessageY:string= 'Matching these movements will change composition and status of an existing match. Are you sure that you wish to continue?'; //ExternalInterface.call('getBundle', 'text', 'label-matchingMovementWillChange', 'Matching these movements will change composition and status of an existing match. Are you sure that you wish to continue?');
          this.swtAlert.warning(testMessageY, title, Alert.OK | Alert.CANCEL, this, this.alertListenerMatch.bind(this), null);
        }
        else
        {
          let matchIdFlag:boolean=false;
          let matchId:string="";
          for (let selMatchCnt:number=0; selMatchCnt < this.bottomGrid.dataProvider.length; selMatchCnt++)
          {
            if (this.bottomGrid.dataProvider[selMatchCnt].matchid.toString().length <= 0)
            {
              matchIdFlag=true;
              break;
            }
            else
            {
              matchId=this.bottomGrid.dataProvider[selMatchCnt].matchid;
            }
          }
          let selectedList:string="";
          for (let i:number=0; i < this.bottomGrid.dataProvider.length; i++)
          {
            selectedList+=this.bottomGrid.dataProvider[i].movement + ",";
          }
          let entityId:string=this.entityCombo.selectedLabel;
          this.actionPath="movementmatchdisplay.do?";
          let threshold:string="N";
          let thresholdInd:string="1";
          if (!this.currencyThreshold.selected)
          {
            threshold="Y";
            thresholdInd="1";
          }
          this.actionMethod="method=offeredMatch&selectedList=" + selectedList + "&selectedTab=" + ExternalInterface.call('eval', 'tabPressed') + "&selectedCurrencyCode=" + ExternalInterface.call('eval', 'currencyCode') + "&entityCode=" + entityId + "&currentPage=" + this.jsonReader.getScreenAttributes()["pages"].currentPage + "&tableScrollbarLeft=" + "&tableScrollbarTop=" + "&scrollbarLeft=" + "&scrollbarTop=";
          if (ExternalInterface.call('eval', 'workflow') != null)
            this.actionMethod+="&workflow=" + ExternalInterface.call('eval', 'workflow');
          if (!matchIdFlag)
            this.actionMethod+="&matchId=" + matchId;
          if (!matchIdFlag)
            this.unlockMvmnt(null);
          this.inputData.url=this.baseURL + this.actionPath + this.actionMethod;
          /*Make the request with requestParams if any*/
          this.cGrid.selectedIndices= [];
          this.bottomGrid.dataProvider= [];
          //remove selected indices of cgrid and reset bottom grid
          let array = [];
          for (let i = 0; i <this.bottomGrid.dataProvider.length; i++) {
            array.push(this.bottomGrid.angularGridInstance.gridService.getDataItemByRowNumber(i));
          }
          this.bottomGrid.angularGridInstance.gridService.deleteItems(array);
          this.selectedMvmts=[];
          ExternalInterface.call("offeredMatches", this.inputData.url);

          if (this.cGrid.selectedIndices.length <= 0)
          {
            this.messageButton.enabled=false;
            this.messageButton.buttonMode=false;
            this.movementButton.enabled=false;
            this.movementButton.buttonMode=false;
            this.noteButton.enabled=false;
            this.noteButton.buttonMode=false;
            this.reconButton.enabled=false;
            this.reconButton.buttonMode=false;
            this.confirmButton.enabled=false;
            this.confirmButton.buttonMode=false;
            this.suspendButton.enabled=false;
            this.suspendButton.buttonMode=false;
            this.matchButton.enabled=false;
            this.matchButton.buttonMode=false;
            this.removeButton.enabled=false;
            this.removeButton.buttonMode=false;
          }


          this.dataRefresh();
        }
      }
    }
  }

  /**
   * This method is used to remove the sected records from the Bottom grid
   * */
  remove():void
  {
    try {
    let totalSelectedAmount:number = 0;
    let formatedAmount:string = "";
    //Begin, Added by KaisBS for Mantis 1656: Movement locking fails in Movement Summary Display
    for (let i =0; i<this.selectedMovements.length; i++ )
    {
      if ( this.selectedMovements[i] == this.bottomGrid.dataProvider[this.bottomGrid.selectedIndex].movement ) {
        // remove the selected movement from the array to be solve the lock problem
        this.selectedMovements.splice(i, 1);
      }

    }
    //End.
    let selectedIndexArr = [];
    let lockflag: any;
    let lockUser:string=ExternalInterface.call("checkLockOnServer", this.bottomGrid.dataProvider[this.bottomGrid.selectedIndex].movement);
    if (this.currentUser.toLowerCase() == lockUser.toLowerCase() || lockUser.toString() == "true")
      lockflag=ExternalInterface.call("unlockMovementOnServer", this.bottomGrid.dataProvider[this.bottomGrid.selectedIndex].movement);
    let lock:string=lockflag.toString();
      const index = this.selectedMvmts.indexOf(this.bottomGrid.dataProvider[this.bottomGrid.selectedIndex].movement.toString(), 0);
      if (index > -1) {
        this.selectedMvmts.splice(index, 1);
      }
    this.bottomGrid.angularGridInstance.gridService.deleteItem(this.bottomGrid.angularGridInstance.gridService.getDataItemByRowNumber(this.bottomGrid.selectedIndex));
    let selectedList ="";
    for(let z: number = 0; z < this.bottomGrid.dataProvider.length; z++ ) {
      selectedList+= this.bottomGrid.dataProvider[z].movement +',';
    }
    ExternalInterface.call('setSelectedMovementForLock',selectedList);
    //this.bottomGrid.refresh();
    if (!BooleanParser.parseBooleanValue(lock))
    {
      this.swtAlert.warning(SwtUtil.getPredictMessage('label.movementCannotBeUnlocked', null), 'Warning');
    }
    else
    {
      for (let bottomSelectedCounter:number=0; bottomSelectedCounter < this.bottomGrid.dataProvider.length; bottomSelectedCounter++)
      {
        for (let resetSelectedCounter:number=0; resetSelectedCounter < this.cGrid.dataProvider.length; resetSelectedCounter++)
        {
          if (this.bottomGrid.dataProvider[bottomSelectedCounter].movement == this.cGrid.dataProvider[resetSelectedCounter].movement)
          {
            selectedIndexArr.push(resetSelectedCounter);
          }
        }
      }
      this.cGrid.selectedIndices=selectedIndexArr;
      this.removeButton.enabled=false;
      this.removeButton.buttonMode=false;
      //deciding the status of the buttons based on the grid size
      if (this.cGrid.selectedIndices.length <= 0)
      {
        this.messageButton.enabled=false;
        this.messageButton.buttonMode=false;
        this.movementButton.enabled=false;
        this.movementButton.buttonMode=false;
        this.noteButton.enabled=false;
        this.noteButton.buttonMode=false;
        this.reconButton.enabled=false;
        this.reconButton.buttonMode=false;
        this.confirmButton.enabled=false;
        this.confirmButton.buttonMode=false;
        this.suspendButton.enabled=false;
        this.suspendButton.buttonMode=false;
        this.matchButton.enabled=false;
        this.matchButton.buttonMode=false;
        if ( this.bottomGrid != null)
        {
          if ( this.bottomGrid.dataProvider.length == 1)
          {
            this.movementButton.enabled=true;
            this.movementButton.buttonMode=true;
            this.noteButton.enabled=true;
            this.noteButton.buttonMode=true;
            this.messageButton.enabled=true;
            this.messageButton.buttonMode=true;
            this.confirmButton.enabled=true;
            this.confirmButton.buttonMode=true;
            this.suspendButton.enabled=true;
            this.suspendButton.buttonMode=true;
            this.matchButton.enabled=true;
            this.matchButton.buttonMode=true;
          }
          else if ( this.bottomGrid.dataProvider.length > 1)
          {
            this.confirmButton.enabled=true;
            this.confirmButton.buttonMode=true;
            this.suspendButton.enabled=true;
            this.suspendButton.buttonMode=true;
            this.matchButton.enabled=true;
            this.matchButton.buttonMode=true;
          }
        }
      }
      if ( this.bottomGrid != null)
      {
        if ( this.bottomGrid.dataProvider.length == 1)
        {
          this.movementButton.enabled=true;
          this.movementButton.buttonMode=true;
          this.noteButton.enabled=true;
          this.noteButton.buttonMode=true;
          this.messageButton.enabled=true;
          this.messageButton.buttonMode=true;
        }
        else if ( this.bottomGrid.dataProvider.length == 0)
        {
          this.movementButton.enabled=false;
          this.movementButton.buttonMode=false;
          this.noteButton.enabled=false;
          this.noteButton.buttonMode=false;
          this.messageButton.enabled=false;
          this.messageButton.buttonMode=false;
          this.matchButton.enabled=false;
          this.matchButton.buttonMode=false;
        }

      }
      let flag:boolean=false;
      if ( this.bottomGrid != null &&  this.bottomGrid.dataProvider.length > 0)
      {
        for (let high:number=0; high <  this.bottomGrid.dataProvider.length; high++)
        {
          if (( this.bottomGrid.dataProvider[high].matchid).toString().length > 0)
            flag=true;
        }
        //reconcile button status
        if (flag)
        {
          this.reconButton.enabled=false;
          this.reconButton.buttonMode=false;
        }

        else
        {
          if ( this.bottomGrid.dataProvider.length != 0)
          {
            this.reconButton.enabled=true;
            this.reconButton.buttonMode=true;
          }
        }
      }
      var matchFlag:boolean=false;
      let match_id:string=null;
      if ( this.bottomGrid != null &&  this.bottomGrid.dataProvider.length > 0)
      {
        for (var selMatchCount:number=0; selMatchCount <  this.bottomGrid.dataProvider.length; selMatchCount++)
        {
          if ((match_id == null) && (( this.bottomGrid.dataProvider[selMatchCount].matchid).toString().length > 0))
          {
            match_id= this.bottomGrid.dataProvider[selMatchCount].matchid;
          }
          else if (( this.bottomGrid.dataProvider[selMatchCount].matchid).toString().length > 0)
          {
            if (match_id !=  this.bottomGrid.dataProvider[selMatchCount].matchid)
            {
              matchFlag=true;
              break;
            }
          }
        }
        if (matchFlag)
        {
          this.matchButton.enabled=false;
          this.matchButton.buttonMode=false;
          let unlockflag:any;
          //check the whether movement id is locked or not
          unlockflag=ExternalInterface.call("unlockMovementOnServer", this.bottomGrid.dataProvider[selMatchCount].movement);
          this.swtAlert.warning(SwtUtil.getPredictMessage('label.selectedOnlyMatchedItems', null), 'Warning');
        }
        else
        {

          this.matchButton.enabled=true;
          this.matchButton.buttonMode=true;
        }
      }

    }
    let lockFlag:boolean=true;
    let lockflag2: any;
    let lock2:string="";
    if (this.bottomGrid != null && this.bottomGrid.dataProvider.length > 0)
    {
      for (let intCounter:number=0; intCounter < this.bottomGrid.dataProvider.length; intCounter++)
      {
        lockflag2=ExternalInterface.call("accountAccessConfirm", (this.bottomGrid.dataProvider[intCounter].movement).toString(), (this.entityCombo.selectedLabel).toString());
        lock2=lockflag2.toString();
        if (!BooleanParser.parseBooleanValue(lock2))
          lockFlag=false;
      }
    }
    if (lockFlag)
    {
      if (this.bottomGrid.dataProvider.length != 0)
      {
        this.confirmButton.enabled=true;
        this.confirmButton.buttonMode=true;
        this.suspendButton.enabled=true;
        this.suspendButton.buttonMode=true;
        this.matchButton.enabled=true;
        this.matchButton.buttonMode=true;
      }
    }
    else
    {
      if (this.bottomGrid.dataProvider.length != 0)
      {
        this.confirmButton.enabled=false;
        this.confirmButton.buttonMode=false;
        this.suspendButton.enabled=false;
        this.suspendButton.buttonMode=false;
        this.matchButton.enabled=false;
        this.matchButton.buttonMode=false;
        this.reconButton.enabled=false;
        this.reconButton.buttonMode=false;
      }
    }
    //Code MOdified By Chinniah on 10-Nov-2011 for Mantis 1613:Bottom grid doesn't show the no of records correctly. It displays the main grid records count
    //set the row size based size of the bottom grid based on the no of datas in the grid
    this.bottomGrid.setRowSize=this.bottomGrid.dataProvider.length;

    if (this.bottomGrid.dataProvider.length > 0)
    {

      let varSign:string;
      let varAmount:number=0;
      let amount:string="";
      let amountArr = [];
      let diffArr = [];
      let ccyFormat:string =this.jsonReader.getScreenAttributes()["currencyFormat"];
      var ccyCode:string="";

      for (var high:number=0; high < this.bottomGrid.dataProvider.length; high++)
      {
        varSign= this.bottomGrid.dataProvider[high].sign;
        amount=this.bottomGrid.dataProvider[high].amount;
        if (ccyFormat == "currencyPat1")
        {
          amountArr=amount.split(',');
          amount="";
          for (let commaCounter:number=0; commaCounter < amountArr.length; commaCounter++)
          {
            amount+=amountArr[commaCounter];
          }
        }
        else
        {
          amountArr=amount.split('.');
          amount="";
          for (let dotCounter:number=0; dotCounter < amountArr.length; dotCounter++)
          {
            amount+=amountArr[dotCounter];
          }
          amount=amount.replace(',', '.');
        }
        varAmount=parseFloat(amount);

        if(varSign == "D")
          totalSelectedAmount-=varAmount;
        else
          totalSelectedAmount+=varAmount;
      }
    }


    /*let numFormatTotal: any; //TODO NumberFormatter=new NumberFormatter();
    numFormatTotal.precision="2";
    numFormatTotal.rounding="nearest";*/

    let ccyFormatForTotal:string = this.jsonReader.getScreenAttributes()["currencyFormat"];

    let expandTotalAmt:string= this.addZeroes(totalSelectedAmount.toString());
    if (expandTotalAmt.indexOf(".") == -1)
    {
      expandTotalAmt+="00";
    }
    else
    {
      let expandDiffArr = [];
      expandDiffArr =expandTotalAmt.split(".");
      expandTotalAmt=expandDiffArr[0] + expandDiffArr[1]
    }
    formatedAmount = ExternalInterface.call("expandAmtDifference", expandTotalAmt, ccyFormatForTotal, ccyCode);
    if(totalSelectedAmount < 0 && formatedAmount.length>0 && formatedAmount.charAt(0) != "-") {
      this.totalSelectedValue.text="-"+ExternalInterface.call("expandAmtDifference", expandTotalAmt, ccyFormatForTotal, ccyCode);
    }else {
      this.totalSelectedValue.text=ExternalInterface.call("expandAmtDifference", expandTotalAmt, ccyFormatForTotal, ccyCode);
    }
    this.bottomGrid.selectedIndex = -1;

    
    } catch(e) {
      console.log('e', e)
    }

  }

  /**
   *	This method is used to open the Filter Movement Search from Movement summary display.
   *
   */
  filter():void
  {
    let filterParam:string="";
    try {


      filterParam+='&amountover=' + ExternalInterface.call('amountOver');
      filterParam+='&amountunder=' + ExternalInterface.call('amountUnder');
      filterParam+='&group=' + ExternalInterface.call('getGroup');
      filterParam+='&metaGroup=' + ExternalInterface.call('getMetagroup');
      //Added By Imed B on 26 april  2012
      if (ExternalInterface.call('valueFromDate') == ""){
        filterParam+='&valueFromDate=' + ExternalInterface.call('eval', 'tabPressed');
        filterParam+='&valueToDate=' + ExternalInterface.call('eval', 'tabPressed');
      }else{
        filterParam+='&valueFromDate=' + ExternalInterface.call('valueFromDate');
        filterParam+='&valueToDate=' + ExternalInterface.call('valueToDate');
      }
      //End Added By Imed B on 26 april 2012

      filterParam+='&timefrom=' + ExternalInterface.call('timefrom');
      filterParam+='&timeto=' + ExternalInterface.call('timeto');
      filterParam+='&inputDate=' + ExternalInterface.call('inputDate');
      filterParam+='&reference=' + ExternalInterface.call('reference');
      filterParam+='&refFlagFilterSearch=' + ExternalInterface.call('referenceFlag');
      filterParam+='&openMovementFlagSearch=' + ExternalInterface.call('openMovementFlag');
      filterParam+='&currentFilterConf=' + StringUtils.encode64(this.filterComboMSD.selectedLabel);
      filterParam+='&filterAcctType=' + this.jsonReader.getScreenAttributes()["filterAcctType"].toString().replace(/\%/g, "per;");
      filterParam+="&initialinputscreen=" + this.jsonReader.getScreenAttributes()["initialinputscreen"];
      ExternalInterface.call("searchmovements", "filterOutMov", this.entityCombo.selectedLabel, ExternalInterface.call('eval', 'currencyCode'), this.jsonReader.getScreenAttributes()["selectedSort"], this.jsonReader.getScreenAttributes()["selectedFilter"], ExternalInterface.call('eval', 'posLvlId'), ExternalInterface.call('eval', 'workflow'), filterParam);
    } catch (e) {
      console.log('error in filter', e)
    }
  }

  helphandler():void
  {
    ExternalInterface.call("help");
  }

  alertListenerMatch(eventObj):void
  {
    let allowMatch:boolean=false;
    if (eventObj.detail == Alert.OK)
    {
      allowMatch=true;
    }
    if (allowMatch)
    {
      if (!this.checkSelectedMovementsAmounts())
      {
        allowMatch=false;
        let testMessageY:string= 'The amounts of the selected movements differ. Do you want to continue?'; //ExternalInterface.call('getBundle', 'text', 'label-amountSelectedMovementsDiffer', 'The amounts of the selected movements differ. Do you want to continue?');
        this.swtAlert.question(testMessageY, "", Alert.OK | Alert.CANCEL, this, this.alertListener.bind(this), null);
      }
      else
      {
        let matchIdFlag:boolean=false;
        let matchId:string="";
        for (let selMatchCount:number=0; selMatchCount < this.bottomGrid.dataProvider.length; selMatchCount++)
        {
          if ((this.bottomGrid.dataProvider[selMatchCount].matchid).toString.length <= 0)
          {
            matchIdFlag=true;
            break;
          }
          else
          {
            matchId=this.bottomGrid.dataProvider[selMatchCount].matchid;
          }
        }
        let selectedList:string="";
        for (let i:number=0; i < this.bottomGrid.dataProvider.length; i++)
        {
          selectedList+=this.bottomGrid.dataProvider[i].movement + ",";
        }
        let entityId:string=this.entityCombo.selectedLabel;
        this.actionPath="movementmatchdisplay.do?";
        let threshold:string="N";
        let thresholdInd:string="1";
        if (!this.currencyThreshold.selected)
        {
          threshold="Y";
          thresholdInd="1";
        }
        this.actionMethod="method=offeredMatch&selectedList=" + selectedList + "&selectedTab=" + ExternalInterface.call('eval', 'tabPressed') + "&selectedCurrencyCode=" + ExternalInterface.call('eval', 'currencyCode') + "&entityCode=" + entityId + "&currentPage=" + this.jsonReader.getScreenAttributes()["pages"].currentPage + "&tableScrollbarLeft=" + "&tableScrollbarTop=" + "&scrollbarLeft=" + "&scrollbarTop=";
        if (ExternalInterface.call('eval', 'workflow') != null)
          this.actionMethod+="&workflow=" + ExternalInterface.call('eval', 'workflow');
        if (!matchIdFlag)
          this.actionMethod+="&matchId=" + matchId;
        this.inputData.url=this.baseURL + this.actionPath + this.actionMethod;
        /*Make the request with requestParams if any*/
        ExternalInterface.call("offeredMatches", this.inputData.url);
        this.cGrid.selectedIndices=[];
        this.bottomGrid.dataProvider= [];
        let array = [];
        for (let i = 0; i <this.bottomGrid.dataProvider.length; i++) {
          array.push(this.bottomGrid.angularGridInstance.gridService.getDataItemByRowNumber(i));
        }
        this.bottomGrid.angularGridInstance.gridService.deleteItems(array);
        this.selectedMvmts=[];
        if (matchIdFlag)
        {
          if (this.cGrid.selectedIndices.length <= 0)
          {
            this.messageButton.enabled=false;
            this.messageButton.buttonMode=false;
            this.movementButton.enabled=false;
            this.movementButton.buttonMode=false;
            this.noteButton.enabled=false;
            this.noteButton.buttonMode=false;
            this.reconButton.enabled=false;
            this.reconButton.buttonMode=false;
            this.confirmButton.enabled=false;
            this.confirmButton.buttonMode=false;
            this.suspendButton.enabled=false;
            this.suspendButton.buttonMode=false;
            this.matchButton.enabled=false;
            this.matchButton.buttonMode=false;
            this.removeButton.enabled=false;
            this.removeButton.buttonMode=false;
          }
          this.dataRefresh(null);
        }
      }
    }
  }

  /**
   * This function helps in instantiating the Font setting window with the event handling functionality for setting up font size.
   */
  fontSettingHandler():void
  {
    this.win =  SwtPopUpManager.createPopUp(this, MsdFontSetting, {
      title: "Options", //SwtUtil.getPredictMessage('cutOff.title.addRule', null), // childTitle,
      fontSizeValue: this.selectedFont,
    });
    this.win.isModal = true;
    this.win.enableResize = false;
    this.win.width = '550';
    this.win.height = '170';
    this.win.showControls = true;
    this.win.id = "optionsWindow";
    this.win.onClose.subscribe((res) => {
      this.submitFontSize(res);
    });


    this.win.display();
  }

  /**
   * This function is used in setting font size of the datagrid on MSD screen based on the user's choice.
   */
  submitFontSize(res):void
  {
    this.fontValue=res.fontSize.value;
    this.fontLabel=res.fontSize.label;
    // Sets the datagrid style based on the font value
    if (this.fontValue == "N")
    {
      this.selectedFont=0;
      this.cGrid.styleName="dataGridNormal";
      this.cGrid.rowHeight=18;

      if (this.bottomGrid != null)
      {
        this.bottomGrid.styleName="dataGridNormal";
        this.bottomGrid.rowHeight=18;
      }

      this.fontRequest=ExternalInterface.call("getUpdateFontSize", this.fontLabel);
    }
    else if (this.fontValue == "S")
    {
      this.selectedFont=1;
      this.cGrid.styleName="dataGridSmall";
      this.cGrid.rowHeight=15;

      // check the bottom grid is not null, if it is not null then only set style name and row Height.
      if (this.bottomGrid != null)
      {
        this.bottomGrid.styleName="dataGridSmall";
        this.bottomGrid.rowHeight=15;
      }

      this.fontRequest=ExternalInterface.call("getUpdateFontSize", this.fontLabel);

    }
    if (this.fontRequest != null && this.fontRequest != "")
    {
      this.updateFontSize.url = this.baseURL + this.fontRequest;
      this.updateFontSize.send();
    }
  }

  /**
   * This function shows/hides the button bar
   *
   * @param event:MouseEvent
   */
  showHideButtonBar():void
  {
    // Checks the status of the Button Bar
    if (this.buttonBarHideFlag)
    {
      //Container.addElementAt(totalsPanel, Container.numElements-1);
      this.totalsPanel.includeInLayout = true;
      //this.dataGridContainer.height = "70%"; //(parseInt(this.dataGridContainer.height )- parseInt(this.totalsPanel.height)).toString();
      this.totalsPanel.visible = true;
      this.imgShowHideButtonBar.styleName = "minusIcon";
    }
    else
    {
      this.totalsPanel.visible = false;
      this.imgShowHideButtonBar.styleName = "plusIcon";
      this.totalsPanel.includeInLayout = false;
    }
    this.cGrid.resizeGrid();
    if(this.bottomGrid != undefined)
      this.bottomGrid.resizeGrid();
    this.imgShowHideButtonBar.toolTip=this.buttonBarHideFlag ? 'Hide Button Bar' : 'Show Button Bar';  // ExternalInterface.call('getBundle', 'text', 'label-hideButtonBar', 'Hide Button Bar') : ExternalInterface.call('getBundle', 'text', 'label-showButtonBar', 'Show Button Bar');
    this.buttonBarHideFlag=!this.buttonBarHideFlag;
  }

  /*protected function img_rollOverHandler(evt:MouseEvent):void {
    if(evt.target.id == "deleteFilterImage" && filterCombo.selectedIndex == 0)
      Image(evt.currentTarget).filters = [];
    else
      Image(evt.currentTarget).filters = [new spark.filters.GlowFilter(0x000000,0.2)];
  }

  protected function img1_rollOutHandler(evt:MouseEvent):void {
    Image(evt.currentTarget).filters = [];
  }*/

  saveFilter(event) {
    let saveProfileArrayCollection = [];
    for (let i = 0; i < this.filterComboMSD.dataProvider.length; i++) {
      saveProfileArrayCollection.push(this.filterComboMSD.dataProvider[i].content.toString());
    }
    const index = saveProfileArrayCollection.indexOf(this.noneLabel);
    if (index > -1) {
      saveProfileArrayCollection.splice(index, 1);
    }
    let lastLabel:string = null;
    this.win =  SwtPopUpManager.createPopUp(this, SaveFilterPopUp, {
      title: "Save Filter", //SwtUtil.getPredictMessage('cutOff.title.addRule', null), // childTitle,
      selectedComboItem: this.filterComboMSD.selectedIndex > 0 ? this.filterComboMSD.selectedLabel : "",
      saveProfileCollection : saveProfileArrayCollection,
      filterCombo: this.filterComboMSD
    });
    this.win.isModal = true;
    this.win.enableResize = false;
    this.win.width = '360';
    this.win.height = '220';
    this.win.showControls = true;
    this.win.id = "filterWindow";
    this.win.onClose.subscribe((res) => {
      //this.submitFontSize(res);
    });

    this.win.display();
    /*saveFilterPopup=SaveFilterPopup(PopUpManager.createPopUp(this, SaveFilterPopup, true));
    saveFilterPopup.x=(this.width / 2) - (saveFilterPopup.width / 2);
    saveFilterPopup.y=(this.height / 2) - (saveFilterPopup.height / 2);
    saveFilterPopup["okButton"].addEventListener("click", saveFilterClickHandler);
    let saveFilterArrayCollection:ArrayCollection  = new ArrayCollection();
    for(let i:int = 0; i < filterCombo.dataProvider.length ; i++)
    {
      saveFilterArrayCollection.addItem(filterCombo.dataProvider[i]);
    }
    saveFilterArrayCollection.removeItemAt(saveFilterArrayCollection.getItemIndex(noneLabel));
    saveFilterPopup.comboDataProvider = saveFilterArrayCollection;
    if(filterCombo.selectedIndex > 0)
      saveFilterPopup.selectedComboItem = filterCombo.selectedItem.toString();*/
  }

  /**
   * This function is used to confirm the calculation process after clicking on the link button
   */
  deleteFilterClickHandler(event:Event):void
  {
    this.swtAlert.warning('Are you sure you want to delete the filter'+ " "+ this.filterComboMSD.selectedLabel + "?", //text
      ExternalInterface.call('getBundle', 'text', 'alert-warning', 'Warning'), Alert.OK | Alert.CANCEL,
      this, //parent
      this.deleteAlertListener.bind(this), //close handler
      null); //icon and default button
  }


  /**
   * This function is used to listen to the alert
   *
   * @param eventObj CloseEvent
   */
  deleteAlertListener(eventObj):void
  {
    // Checks for Alert OK
    if (eventObj.detail == Alert.OK)
    {
      // Recalculate data if "OK" is clicked
      this.deleteFilter();
    }
  }

  deleteFilter():void {
    this.fromDeleteFilter = true;
    this.currentFilterConf = this.filterComboMSD.selectedLabel;
    this.filterScreen("deleteFilter", this.currentFilterConf);
  }


  doChangeCombo() {
    if (this.count%2 == 0) {
      this.changeFilter();
    }
    this.count ++
  }
  changeFilter():void
  {
    this.keepSelected = true;
    if(this.cGrid.selectedIndices.length > 0)
      this.lastFilterAction = "changeFilter";
    if (this.filterComboMSD.selectedIndex == -1)
    {
      this.currentFilterConf = this.adhocLabel;
    }
    else
    {
      this.lastSelectedIndex = this.filterComboMSD.selectedIndex;
      this.currentFilterConf = this.filterComboMSD.selectedItem.content;
      this.dataRefresh("filterCombo");
    }

    this.enableDisableFilterCombo();
  }

  enableDisableFilterCombo():void{
    if(this.currentFilterConf == this.noneLabel)
    {
      this.deleteFilterImage.enabled = false;
      //deleteFilterImage.source = deleteFilterDisabledImageIcon;
      this.saveFilterImage.enabled = false;
      //saveFilterImage.source = saveFilterDisabledImageIcon;
      if(this.filterButton)
        this.filterButton.setStyle("color", "#0B333C", this.filterButton.domElement);
    }
    else
    {
      if (this.currentFilterConf == this.adhocLabel)
      {
        this.deleteFilterImage.enabled = false;
        //deleteFilterImage.source = deleteFilterDisabledImageIcon;
      }
      else
      {
        this.deleteFilterImage.enabled = true;
        //deleteFilterImage.source = deleteFilterImageIcon;
      }
      this.saveFilterImage.enabled = true;
      //this.saveFilterImage.source = saveFilterImageIcon;
      if(this.filterButton)
        this.filterButton.setStyle("color", "red", this.filterButton.domElement);
    }

  }

  /**
   * The result when saving the ilm configuration: withRefresh,
   * refreshRate and ranger delimiters
   * @param event
   */
  saveResult(event):void
  {

    let jsonReader : JSONReader = new JSONReader()
    jsonReader.setInputJSON(event)
    if (!jsonReader.getRequestReplyStatus())
      this.swtAlert.error('Error occurred, Please contact your System Administrator: \n'+jsonReader.getRequestReplyMessage(), 'Error');
    else {
      if(this.lastFilterAction != null)
      {
        if (this.lastFilterAction == "saveFilter")
        {
          this.swtAlert.show('The filter was successfully saved','Warning');
        }
        else if (this.lastFilterAction == "deleteFilter")
        {
          this.currentFilterConf = this.noneLabel;
          this.swtAlert.show('The filter was successfully deleted');
        }
        this.dataRefresh(null);
      }
    }
  }

  /**
   * The result when saving the ilm configuration: withRefresh,
   * refreshRate and ranger delimiters
   * @param event
   */
  saveFault(event):void
  {
    this.swtAlert.error('Unable to save to server\nPossible loss of connection', 'Error');
  }

  updateValues(accntFilterType:string):void
  {
    let accntFilterList = [];
    accntFilterList = accntFilterType.split("\|");
    let amountOver:string = accntFilterList[2] != "All" ? accntFilterList[2] : "";
    let amountUnder:string = accntFilterList[3]!= "All" ? accntFilterList[3] : "";
    let valueFromDate:string = accntFilterList[4] != "All" ? accntFilterList[4] : "";
    let valueToDate:string = accntFilterList[5] != "All" ? accntFilterList[5] : "";
    let group:string = accntFilterList[12] != "All" ? accntFilterList[12] : "";
    let metaGroup:string = accntFilterList[13] != "All" ? accntFilterList[13] : "";
    let timeFrom:string = accntFilterList[15] != "All" ? accntFilterList[15] : "";
    let timeTo:string = accntFilterList[16] != "All" ? accntFilterList[16] : "";
    let inputDate:string = accntFilterList[14] != "All" ? accntFilterList[14] : "";
    let reference:string = StringUtils.encode64(accntFilterList[18]);
    let openMovementFlag:string = this.jsonReader.getScreenAttributes()["openMovementFlagSearch"];
    ExternalInterface.call("updateValues", amountOver, amountUnder, valueFromDate, valueToDate, group, metaGroup,
      timeFrom, timeTo, inputDate, reference, openMovementFlag);
  }

  initializeValues():void
  {
    let amountOver:string = "";
    let amountUnder:string = "";
    let valueFromDate:string = ExternalInterface.call('eval', 'tabPressed');
    let valueToDate:string = ExternalInterface.call('eval', 'tabPressed');
    let group:string = "";
    let metaGroup:string = "";
    let timeFrom:string = "";
    let timeTo:string = "";
    let inputDate:string = "";
    let ref:string = "<refparams><include ref1='Y' ref2='Y' ref3='Y' ref4='Y' like='N' ></include><exclude ref1='Y' ref2='Y' ref3='Y' ref4='Y' like='N' ></exclude></refparams>";
    let reference:string = StringUtils.encode64(ref);
    let openMovementFlag:string = "Y";
    ExternalInterface.call("initializeValues", amountOver, amountUnder, valueFromDate, valueToDate, group, metaGroup,
      timeFrom, timeTo, inputDate, reference, openMovementFlag);
  }
  doHelp() {
    ExternalInterface.call('help')
  }
  dataRefreshGrid():void
  {
    this.keepSelected = true;
    let actionMethod:string="";
    //String Variable to hold access from xmlReader
    let access:string= this.jsonReader.getScreenAttributes()["access"];
    //String Variable to hold initialinputscreen from ExternalInterface
    let initialinputscreen: string=ExternalInterface.call('eval', 'initialscreen');
    //String Variable to hold method from ExternalInterface
    let method: string=ExternalInterface.call('eval', 'method');
    //String Variable to hold actionPath
    let actionPath:string="outstandingmovement.do?";
    //String Variable to hold empty applyThresholdFlag
    let applyThresholdFlag:string="";
    let previousSort = this.jsonReader.getScreenAttributes()['selectedSort'];
    if(this.currencyThreshold.selected)
    {
      applyThresholdFlag="Y";
    }
    else
    {
      applyThresholdFlag="N";
    }

    let selectedSort = (this.cGrid.getMsdSortedGridColumn()) ? this.cGrid.getMsdSortedGridColumn() +'|' : previousSort;
    let selectedFilter = this.cGrid.getFilteredGridColumns();
    let addColsFilter= "";
    let oldColsFilter="";
    let columns=[];
    const n= Object.keys(this.addColumnsGridData).length;
    //remove the additional columns from the selectedFilter if useAddCols checkbox is checked
    if (selectedFilter && this.useAddColsCheck=='Y') {
      columns = selectedFilter.split("|");
      for (let i = 0; i < columns.length; i++) {
        if (i < columns.length - n) {
          oldColsFilter = oldColsFilter + columns[i] + ("|");
        } else {
          addColsFilter = addColsFilter + columns[i] + ("|");
        }
      }
    }
    selectedFilter=oldColsFilter!=""?oldColsFilter.slice(0, -1):selectedFilter;

    //provisoire jusqu'a ajout de empty not empty in filter
    if(selectedFilter.indexOf('||') != -1) {
      selectedFilter = selectedFilter.replace('||', '|(EMPTY)|')
    }
    
    if(selectedFilter.charAt(selectedFilter.length - 1) == '|') {
      selectedFilter = selectedFilter+='(EMPTY)';
    }

    if(selectedFilter.charAt(0) == '|') {
      selectedFilter = '(EMPTY)'+selectedFilter;
    }

    let selectedList:string="";
    if (this.methodName != 'search' && this.initialinputscreen == 'E'){
      for(let z: number = 0; z < this.bottomGrid.dataProvider.length; z++ ) {

        selectedList+= this.bottomGrid.dataProvider[z].movement +',';
        this.selectedMvmts.push(""+this.bottomGrid.dataProvider[z].movement);
      }
    } else {
      //restart select mvmts when changing page
      this.selectedMvmts = [];
    }

    //Assign url value in actionMethod
    actionMethod+="&selectedList=" + selectedList;
    actionMethod+="&entityId=" + this.entityCombo.selectedLabel;
    actionMethod+="&currencyCode=" + ExternalInterface.call('eval', 'currencyCode');
    actionMethod+="&date=" + ExternalInterface.call('eval', 'dateStr');
    actionMethod+="&posLvlId=" + ExternalInterface.call('eval', 'posLvlId');
    actionMethod+="&totalCount=" + this.jsonReader.getRowSize();
    actionMethod+="&applyCurrencyThreshold=" + applyThresholdFlag;
    actionMethod+="&filterAcctType=" + this.jsonReader.getScreenAttributes()["filterAcctType"].toString().replace(/\%/g, "per;");
    actionMethod+="&initialinputscreen=" + ExternalInterface.call('eval', 'initialscreen');
    actionMethod+="&openFlag=" + ExternalInterface.call('eval', 'openFlag');
    actionMethod+="&openMovementFlagSearch=" + ExternalInterface.call('openMovementFlag');
    if (initialinputscreen == 'E')
      actionMethod+="&totalFlag=" + ExternalInterface.call('eval', 'totalFlagOpenMovements');
    if (ExternalInterface.call('eval', 'workflow') != null)
      actionMethod+="&workflow=" + ExternalInterface.call('eval', 'workflow');
    if ((method != 'search') && (initialinputscreen == 'E'))
    {
      actionMethod="method=displayOpenMovements" + actionMethod ;
    }
    if ((method == 'search') || (initialinputscreen != 'E'))
    {
      if (access == "readOnly")
      {
        //Code Modified for Mantis 1598 by Sudhakar on 5-12-2011: Movement Summary Display' screen returns 'Connection Error
        actionMethod="method=displayOpenMovements" + actionMethod + "&menuAccessId=" + ExternalInterface.call('eval', 'menuAccessId');
      }
      if (access != "readOnly")
      {
        if (initialinputscreen == "currencymonitor" || initialinputscreen == "entitymonitor")
        {
          actionMethod="method=getCurrencyMonitorMvmnts" + actionMethod;
          actionMethod+="&valueDate=" + ExternalInterface.call('eval', 'valueDate');
          actionMethod+="&balanceType=";
          actionMethod+="&locationId=" + ExternalInterface.call('eval', 'locationIdCurrency');

        }
        if (initialinputscreen != "currencymonitor" && initialinputscreen != "entitymonitor")
        {
          if (initialinputscreen == "accountmonitor")
          {
            actionMethod="method=getAccountMonitorMvmnts" + actionMethod;
            actionMethod+="&valueDate=" + ExternalInterface.call('eval', 'valueDate');
            actionMethod+="&balanceType=" + ExternalInterface.call('eval', 'balanceTypeAccount');
            actionMethod+="&accountId=" + ExternalInterface.call('eval', 'accountIdAccount');
            actionMethod+="&accountType=" + ExternalInterface.call('eval', 'accountTypeAccount');
          }
          if (initialinputscreen != "accountmonitor")
          {
            if (initialinputscreen == "accountbreakdownmonitor")
            {
              actionMethod="method=getAccountMonitorMvmnts" + actionMethod;
              actionMethod+="&valueDate=" + ExternalInterface.call('eval', 'valueDate');
              actionMethod+="&balanceType=" + ExternalInterface.call('eval', 'balanceTypeAccount');
              actionMethod+="&accountId=" + ExternalInterface.call('eval', 'accountIdAccount');
            }
            if (initialinputscreen != "accountbreakdownmonitor")
            {
              if (initialinputscreen == "bookmonitor")
              {
                actionMethod="method=getBookMonitorMvmnts" + actionMethod;
                actionMethod+="&valueDate=" + ExternalInterface.call('eval', 'valueDate');
                actionMethod+="&bookCode=" + ExternalInterface.call('eval', 'bookCodeBook');
              }
              if (initialinputscreen != "bookmonitor")
              {
                if (initialinputscreen == "mvmntsfromWorkFlowMonitor")
                {
                  actionMethod="method=getMvmntsfromWorkFlowMonitor" + actionMethod;
                  actionMethod+="&valueDate=" + ExternalInterface.call('eval', 'valueDate');
                  actionMethod+="&tabIndicator=" + ExternalInterface.call('eval', 'tabIndicatorWorkflow');
                  actionMethod+="&currGrp=" + ExternalInterface.call('eval', 'currGrp');
                  actionMethod+="&roleId=" + ExternalInterface.call('eval', 'roleId');
                  actionMethod+="&matchStatus=" + ExternalInterface.call('eval', 'matchStatusWorkflow');
                  actionMethod+="&predictStatus=" + ExternalInterface.call('eval', 'predictStatusWorkflow');
                  actionMethod+="&linkFlag=" + ExternalInterface.call('eval', 'linkFlagWorkflow');
                }
                if (initialinputscreen != "mvmntsfromWorkFlowMonitor")
                {
                  if (initialinputscreen == "unSettledYesterday")
                  {
                    actionMethod="method=getUnsettledYesterdayMvmnts" + actionMethod;
                    actionMethod+="&valueDate=" + ExternalInterface.call('eval', 'valueDate');
                    actionMethod+="&currGrp=" + ExternalInterface.call('eval', 'currGrp');
                    actionMethod+="&roleId=" + ExternalInterface.call('eval', 'roleId');
                  }
                  if (initialinputscreen != "unSettledYesterday")
                  {
                    if (initialinputscreen == "backValuedMvmnts")
                    {
                      actionMethod="method=getBackValuedMvmnts" + actionMethod;
                      actionMethod+="&valueDate=" + ExternalInterface.call('eval', 'valueDate');
                      actionMethod+="&currGrp=" + ExternalInterface.call('eval', 'currGrp');
                      actionMethod+="&roleId=" + ExternalInterface.call('eval', 'roleId');
                    }
                    if (initialinputscreen != "backValuedMvmnts")
                    {
                      if (initialinputscreen == "openUnexpectedMvmnts")
                      {
                        actionMethod="method=getOpenUnexpectedMvmnts" + actionMethod;
                        actionMethod+="&valueDate=" + ExternalInterface.call('eval', 'valueDate');
                        actionMethod+="&currGrp=" + ExternalInterface.call('eval', 'currGrp');
                        actionMethod+="&roleId=" + ExternalInterface.call('eval', 'roleId');
                      }
                      if (initialinputscreen != "openUnexpectedMvmnts")
                      {
                        actionMethod="method=search" + actionMethod;
                        actionMethod+="&archiveId=" + ExternalInterface.call('eval', 'archiveIdSearch');
                        actionMethod+="&referenceFlag=" + ExternalInterface.call('eval', 'referenceFlagSearch');
                        let filterCriteria:string="";
                        filterCriteria="entityId=" + ExternalInterface.call('eval', 'entityIdSearch') + "|movementType=" + ExternalInterface.call('eval', 'movementTypeSearch') + "|sign=" + ExternalInterface.call('eval', 'signSearch') + "|predictStatus=" + ExternalInterface.call('eval', 'predictStatusSearch') + "|amountover=" + ExternalInterface.call('eval', 'amountoverSearch') + "|amountunder=" + ExternalInterface.call('eval', 'amountunderSearch') + "|currencyCode=" + ExternalInterface.call('eval', 'currencyCode') + "|paymentChannelId=" + ExternalInterface.call('eval', 'paymentChannelIdSearch') + "|beneficiaryId=" + ExternalInterface.call('eval', 'beneficiaryIdSearch') + "|custodianId=" + ExternalInterface.call('eval', 'custodianIdSearch') + "|positionlevel=" + ExternalInterface.call('eval', 'positionlevelSearch') + "|accountId=" + ExternalInterface.call('eval', 'accountIdSearch') + "|group=" + ExternalInterface.call('eval', 'groupSearch') + "|metaGroup=" + ExternalInterface.call('eval', 'metaGroupSearch') + "|bookCode=" + ExternalInterface.call('eval', 'bookCodeSearch') + "|valueFromDateAsString=" + ExternalInterface.call('eval', 'valueFromDateAsStringSearch') + "|valueToDateAsString=" + ExternalInterface.call('eval', 'valueToDateAsStringSearch') + "|timefrom=" + ExternalInterface.call('eval', 'timefromSearch') + "|timeto=" + ExternalInterface.call('eval', 'timetoSearch') + "|reference=" + ExternalInterface.call('eval', 'referenceSearch') + "|messageId=" + ExternalInterface.call('eval', 'messageIdSearch') + "|inputDateAsString=" + ExternalInterface.call('eval', 'inputDateAsStringSearch') + "|counterPartyId=" + ExternalInterface.call('eval', 'counterPartyIdSearch') + "|fintrade=" + ExternalInterface.call('eval', 'fintradeSearch') + "|matchStatus=" + ExternalInterface.call('eval', 'matchStatusSearch') + "|initialinputscreen=" + this.jsonReader.getScreenAttributes()["initialinputscreen"] + "|accountClass=" + ExternalInterface.call('eval', 'accountClassSearch')
                          + "|producttype=" + ExternalInterface.call('eval', 'producttype') +"|uetr=" + ExternalInterface.call('eval', 'uetr') + "|matchingparty=" + ExternalInterface.call('eval', 'matchingparty') + "|postingDateFrom=" + ExternalInterface.call('eval', 'postingDateFrom') + "|postingDateTo=" + ExternalInterface.call('eval', 'postingDateTo') + "|positionlevel=" + ExternalInterface.call('eval', 'positionlevelSearch');
                        actionMethod+="&filterCriteria=" + filterCriteria;
                        actionMethod+="&filterFromSerach=true";
                        actionMethod+="&filterFromSerach=true";
                        actionMethod+="&extraFilter=" + ExternalInterface.call('eval', 'extraFilter');
                      }
                    }
                  }
                }
              }
            }
          }
        }
      }
      // set External Balance Statues in parameter, When filter or sort the column.
      actionMethod+="&extBalStatus=" + ExternalInterface.call('eval', 'extBalStatusSearch');
      actionMethod+="&extraFilter=" + ExternalInterface.call('eval', 'extraFilter');
    }

    // Added for mantis 1443 by KaisBS, If the initial input is 'X' (scenario summary) then
    // send the scenario id and total flag values.
    // Begin
    if (initialinputscreen == 'X'){
      // Send the scenario id to get movements with its query
      if (ExternalInterface.call('eval', 'scenarioId') != null)
      {
        actionMethod+="&scenarioId=" + ExternalInterface.call('eval', 'scenarioId');
        actionMethod+="&currGrp=" + ExternalInterface.call('eval', 'currGrp');
      }
      actionMethod+="&totalFlag=" + ExternalInterface.call('eval', 'totalFlagOpenMovements');
    }
//to resolve issue when making sort and not filter
    const pattern = /^(All\|)+All\|?$/;
    const updatedFilter= addColsFilter!=""?selectedFilter+'|'+addColsFilter:selectedFilter;
   var filter=!pattern.test(updatedFilter)?StringUtils.encode64(this.gridColsToXmlFilter(selectedFilter, addColsFilter)):"All";
    actionMethod+="&currentPage=1";
    actionMethod+="&selectedSort=" + StringUtils.encode64(this.getSortAddColumns(selectedSort.slice(0, -1)));
    actionMethod+="&addColsSort=" +  StringUtils.encode64(this.getSortAddColumns(selectedSort.slice(0, -1)));
    actionMethod+="&selectedFilter=" + filter;//selectedFilter;
    actionMethod+="&addColsFilter=" +  StringUtils.encode64(this.getFilterAddColumns(addColsFilter));
    actionMethod+="&currentFilterConf=" + Encryptor.encode64(this.currentFilterConf);
    this.inputData.url=this.baseURL + actionPath + actionMethod;
    let requestParams = [];
    this.inputData.send(requestParams);
  }
  replaceAt(string, index, replace) {
    return string.substring(0, index) + replace + string.substring(index + 1);
  }


  private lastSelectedTooltipParams = null;
  getParamsFromParent() {
    const params = { sqlParams: this.lastSelectedTooltipParams, facilityId: this.tooltipFacilityId,selectedNodeId:this.selectedNodeId, treeLevelValue:this.treeLevelValue ,
    tooltipMvtId :this.tooltipMvtId , tooltipOtherParams:this.tooltipOtherParams};
    return params;
  }

  private eventsCreated = false;
  private customTooltip: any = null;
  public createTooltip (event){
      if(this.customTooltip && this.customTooltip.close)
        this.removeTooltip();
      try {    
      const toolTipWidth = 420;
      this.customTooltip = SwtPopUpManager.createPopUp(parent, EnhancedAlertingTooltip, {
      });
      this.customTooltip.enableResize = false;
      this.customTooltip.width = ''+toolTipWidth;
      this.customTooltip.height = "450";
      this.customTooltip.enableResize = false;
      this.customTooltip.title = "Alert Summary Tooltip";
      this.customTooltip.showControls = true;
      if (window.innerHeight < this.positionY+450)
      this.positionY=120;
      this.customTooltip.setWindowXY( this.positionX+20, this.positionY);
      this.customTooltip.showHeader = false;
      this.customTooltip.parentDocument = this;
      this.customTooltip.processBox = this;
      this.customTooltip.display();
      //event for display list button click
        setTimeout(() => {

          if (!this.eventsCreated) {
            this.customTooltip.getChild().DISPLAY_LIST_CLICK.subscribe((target) => {
              this.lastSelectedTooltipParams = target.noode.data
              ExternalInterface.call("openAlertInstanceSummary", "openAlertInstSummary", this.selectedNodeId, this.treeLevelValue);
            });
          }
        }, 0);

        //event for link to specific button click
        setTimeout(() => {
          if (!this.eventsCreated) {
            this.customTooltip.getChild().LINK_TO_SPECIF_CLICK.subscribe((target) => {
              this.getScenarioFacility(target.noode.data.scenario_id);
              this.lastSelectedTooltipParams = target.noode.data
              this.hostId = target.hostId;
            });
          }
        }, 0);

        //event for tree item click
        setTimeout(() => {

          if (!this.eventsCreated) {
            this.customTooltip.getChild().ITEM_CLICK.subscribe((target) => {
              this.selectedNodeId= target.noode.data.id;
              this.treeLevelValue= target.noode.data.treeLevelValue;
              this.customTooltip.getChild().linkToSpecificButton.enabled = false;
              if (target.noode.data.count == 1 && target.noode.isBranch ==false ) {
                this.customTooltip.getChild().linkToSpecificButton.enabled = true;
              }
            });
          }
        }, 0);

      } catch (error) {
          console.log("SwtCommonGrid -> createTooltip -> error", error)
              
      }
  }

  getScenarioFacility(scenarioId) {
    this.requestParams = [];
    this.alertingData.cbStart = this.startOfComms.bind(this);
    this.alertingData.cbStop = this.endOfComms.bind(this);
    this.alertingData.cbFault = this.inputDataFault.bind(this);
    this.alertingData.encodeURL = false;
    // Define the action to send the request
    this.actionPath = "scenarioSummary.do?";
    // Define method the request to access
    this.actionMethod = 'method=getScenarioFacility';
    this.requestParams['scenarioId'] = scenarioId;
    this.alertingData.url = this.baseURL + this.actionPath + this.actionMethod;
    this.alertingData.cbResult = (event) => {
      this.openGoToScreen(event);
    };
    this.alertingData.send(this.requestParams);
  }

  openGoToScreen(event) {
    if(event && event.ScenarioSummary && event.ScenarioSummary.scenarioFacility){
    var facilityId = event.ScenarioSummary.scenarioFacility;

    const selectedEntity = this.lastSelectedTooltipParams != null && this.lastSelectedTooltipParams['entity_id'] != null?this.lastSelectedTooltipParams['entity_id']:this.entityCombo.selectedLabel;
    const selectedcurrency_code = this.lastSelectedTooltipParams != null && this.lastSelectedTooltipParams['currency_code'] != null?this.lastSelectedTooltipParams['currency_code']:'All'
    const selectedmatch_id = this.lastSelectedTooltipParams != null && this.lastSelectedTooltipParams['match_id'] != null?this.lastSelectedTooltipParams['match_id']:null
    const selectedmovement_id = this.lastSelectedTooltipParams != null && this.lastSelectedTooltipParams['movement_id'] != null?this.lastSelectedTooltipParams['movement_id']:this.tooltipMvtId
    const selectedsweep_id = this.lastSelectedTooltipParams != null && this.lastSelectedTooltipParams['sweep_id'] != null?this.lastSelectedTooltipParams['sweep_id']:null

    ExternalInterface.call("goTo", facilityId, this.hostId, selectedEntity, selectedmatch_id, selectedcurrency_code, selectedmovement_id, selectedsweep_id, "");
    }

    // ExternalInterface.call("goTo", facilityId, this.hostId, this.entityCombo.selectedLabel, "", 'All', this.tooltipMvtId, "", "");

  }

  public removeTooltip (){
    if(this.customTooltip != null)
      this.customTooltip.close();
  }


  itemClickFunction(event){        
    if( event.selectedCellTarget !=null && event.selectedCellTarget.field!=null && event.selectedCellTarget.field== "alerting" && (event.selectedCellTarget.data.alerting=="C" || event.selectedCellTarget.data.alerting=="Y")) {
        this.tooltipMvtId = event.selectedCellTarget.data.movement;
        this.tooltipOtherParams = [];
        this.tooltipOtherParams["currencythresholdFlag"] = (this.currencyThreshold.selected ? "Y" : "N");
        this.tooltipFacilityId = "MOVEMENT_SUMMARY_DISPLAY_ROW"; 
        this.tooltipOtherParams = [];      
      setTimeout(() => {
        this.createTooltip(null);
      }, 100);
    } else {
      this.removeTooltip();
    }
    
  }

  itemClickFunction2(event) {
    if (event.target != null && event.target.field != null && event.target.field == "alerting" && (event.target.data.alerting=="C" || event.target.data.alerting=="Y")) {
      this.tooltipMvtId = event.target.data.movement;
      this.tooltipFacilityId = "MOVEMENT_SUMMARY_DISPLAY_ROW";
      this.tooltipOtherParams = [];
      setTimeout(() => {
        this.createTooltip(null);
      }, 100);
    } else {
      this.removeTooltip();
    }
  }

  public addColumn(profileId, source){
    /*if(this.openFlag==true){
    this.swtAlert.error(SwtUtil.getPredictMessage('additionalColumns.alert.alreadyOpened', null));
    }else{
    this.openFlag=true;*/
    ExternalInterface.call("addColumns", "addColumnsScreen",profileId, source);  
    //}
  }

  

  columnWidthChange(event): void {
    //let method=this.currentProfile=="<None>"? 'saveColumnWidth':'saveProfileConfig'
    this.columnsNewWidths = "";
    let gridColumns = this.cGrid.gridObj.getColumns();
    for (let i=0; i< gridColumns.length; i++) {
      if(gridColumns[i].id != "dummy")
      this.columnsNewWidths = this.columnsNewWidths + gridColumns[i].field + "=" + gridColumns[i].width + ',';
    }
    this.columnsNewWidths = this.columnsNewWidths.substring(0, this.columnsNewWidths.length - 1);
    this.requestParams=[];
    this.widthData.encodeURL = false;
    this.actionMethod = 'method=saveColumnWidth';//+method;
    this.actionPath = 'outstandingmovement.do?';
    this.requestParams['width'] = this.columnsNewWidths;
    this.requestParams['entityid'] = this.entityCombo.selectedLabel;
    this.requestParams['currentProfile'] = (this.currentProfile && this.useAddColsCheck=='Y')?this.currentProfile:'<None>';
    this.widthData.cbStart= this.startOfComms.bind(this);
    this.widthData.cbStop= this.endOfComms.bind(this);
    this.widthData.cbResult = (event) => {
      //this.inputDataResultColumnsChange(event);
    };
    this.widthData.url = this.baseURL + this.actionPath +this.actionMethod;
    this.widthData.send(this.requestParams, null);
  }


  columnOrderChange(event): void {
    let method=this.currentProfile=="<None>"? 'saveColumnOrder':'saveProfileConfig'
    this.columnsNewOrders = "";
    this.columnDefinitionsTempArray = event;
    for (let i=0; i< this.columnDefinitionsTempArray.length; i++) {

      if(this.columnDefinitionsTempArray[i].id != "dummy")
        this.columnsNewOrders = this.columnsNewOrders + this.columnDefinitionsTempArray[i].field + ',';
    }
    this.columnsNewOrders = this.columnsNewOrders.substring(0, this.columnsNewOrders.length - 1);
    this.requestParams=[];
    this.ordertData.encodeURL = false;
    this.actionMethod = 'method=saveColumnOrder';//+method;
    this.actionPath = 'outstandingmovement.do?';
    this.requestParams['order'] = this.columnsNewOrders;
    this.requestParams['entityid'] = this.entityCombo.selectedLabel;
    this.requestParams['currentProfile'] = (this.currentProfile && this.useAddColsCheck=='Y')?this.currentProfile:'<None>';
    this.ordertData.cbStart= this.startOfComms.bind(this);
    this.ordertData.cbStop= this.endOfComms.bind(this);
    this.ordertData.cbResult = (event) => {
      //this.inputDataResultColumnsChange(event);
    };
    this.ordertData.url = this.baseURL + this.actionPath +this.actionMethod;
    this.ordertData.send(this.requestParams, null);
  }

  saveLastProfile(lastProfile, useAdditionalCols){
    this.requestParams=[];
    this.currentProfile=lastProfile;
    this.useAddColsCheck=useAdditionalCols;
    this.msdColsData.encodeURL = false;
    this.actionMethod = 'method=saveLastProfile';
    this.actionPath = 'outstandingmovement.do?';
    this.requestParams['entityId'] = this.entityCombo.selectedLabel;
    this.requestParams['lastProfile'] = lastProfile;
    this.requestParams['useAdditionalCols'] = useAdditionalCols;
    this.requestParams['currentFilterConf'] = this.currentFilterConf;
    this.msdColsData.cbStart= this.startOfComms.bind(this);
    this.msdColsData.cbStop= this.endOfComms.bind(this);
    this.msdColsData.cbResult = (event) => {
      this.updateGridData();
    };
    this.msdColsData.url = this.baseURL + this.actionPath +this.actionMethod;
    this.msdColsData.send(this.requestParams, null);  
  }

   updateGridData(){
    this.requestParams=[];
     /*Initialize the communication objects*/
     this.inputData.cbStart = this.startOfComms.bind(this);
     this.inputData.cbStop = this.endOfComms.bind(this);
     this.inputData.cbResult = (event) => {
       this.inputDataResult(event);
     }
     this.inputData.cbFault = this.inputDataFault.bind(this);
     this.inputData.encodeURL = false;

     /*Define the action to send the request*/
     this.actionPath = "outstandingmovement.do?";
     this.actionMethod = "method=" + this.actionMethodName;
        //reset the filter
        this.cGrid.resetFilter();
     if (this.actionMethodName == "search") {
       this.filterArea.removeAllChildren();
       //this.headerContainer.height = "40";
       this.actionMethod = this.actionMethod + "&entityId=" + ExternalInterface.call('eval', 'entityIdSearch') + "&movementType=" + ExternalInterface.call('eval', 'movementTypeSearch') + "&sign=" + ExternalInterface.call('eval', 'signSearch') + "&predictStatus=" + ExternalInterface.call('eval', 'predictStatusSearch') + "&amountover=" + ExternalInterface.call('eval', 'amountoverSearch') + "&amountunder=" + ExternalInterface.call('eval', 'amountunderSearch') + "&archiveId=" + ExternalInterface.call('eval', 'archiveIdSearch') + "&currencyCode=" + ExternalInterface.call('eval', 'currencyCode') + "&paymentChannelId=" + ExternalInterface.call('eval', 'paymentChannelIdSearch') + "&beneficiaryId=" + ExternalInterface.call('eval', 'beneficiaryIdSearch') + "&custodianId=" + ExternalInterface.call('eval', 'custodianIdSearch') + "&positionlevel=" + ExternalInterface.call('eval', 'positionlevelSearch') + "&accountId=" + ExternalInterface.call('eval', 'accountIdSearch') + "&group=" + ExternalInterface.call('eval', 'groupSearch') + "&metaGroup=" + ExternalInterface.call('eval', 'metaGroupSearch') + "&bookCode=" + ExternalInterface.call('eval', 'bookCodeSearch') + "&valueFromDateAsString=" + ExternalInterface.call('eval', 'valueFromDateAsStringSearch') + "&valueToDateAsString=" + ExternalInterface.call('eval', 'valueToDateAsStringSearch') + "&timefrom=" + ExternalInterface.call('eval', 'timefromSearch') + "&timeto=" + ExternalInterface.call('eval', 'timetoSearch') + "&reference=" + ExternalInterface.call('eval', 'referenceSearch') + "&messageId=" + ExternalInterface.call('eval', 'messageIdSearch') + "&inputDateAsString=" + ExternalInterface.call('eval', 'inputDateAsStringSearch') + "&counterPartyId=" + ExternalInterface.call('eval', 'counterPartyIdSearch') +

         "&matchStatus=" + ExternalInterface.call('eval', 'matchStatusSearch') + "&initialinputscreen=" + ExternalInterface.call('eval', 'initialinputscreenSearch') + "&accountClass=" + ExternalInterface.call('eval', 'accountClassSearch') + "&isAmountDiffer=" + ExternalInterface.call('eval', 'isAmountDifferSearch') + "&referenceFlag=" + ExternalInterface.call('eval', 'referenceFlagSearch') + "&matchingparty=" + ExternalInterface.call('eval', 'matchingparty') + "&producttype=" + ExternalInterface.call('eval', 'producttype') +"&uetr=" + ExternalInterface.call('eval', 'uetr') + "&postingDateFrom=" + ExternalInterface.call('eval', 'postingDateFrom') + "&postingDateTo=" + ExternalInterface.call('eval', 'postingDateTo') + "&selectedMovementsAmount=" + ExternalInterface.call('eval', 'selectedMovementsAmountSearch') + "&openFlag=" + ExternalInterface.call('eval', 'openFlag') + "&extBalStatus=" + ExternalInterface.call('eval', 'extBalStatusSearch');
       // Added for mantis 1443, send the scenario id to get movements with its query
       if (ExternalInterface.call('eval', 'scenarioId') != null) {
         this.actionMethod += "&scenarioId=" + ExternalInterface.call('eval', 'scenarioId');
         this.actionMethod += "&applyCurrencyThreshold=" + ExternalInterface.call('eval', 'applyCurrencyThreshold');
         this.actionMethod += "&currGrp=" + ExternalInterface.call('eval', 'currGrp');
       }
     } else if (this.actionMethodName == 'getBookMonitorMvmnts') {
       this.actionMethod = this.actionMethod + "&entityId=" + ExternalInterface.call('eval', 'entityIdBook') + "&currencyCode=" + ExternalInterface.call('eval', 'currencyCode') + "&valueDate=" + ExternalInterface.call('eval', 'valueDate') + "&bookCode=" + ExternalInterface.call('eval', 'bookCodeBook') + "&selectedTabIndex=" + ExternalInterface.call('eval', 'selectedTabIndexBook') + "&initialinputscreen=" + ExternalInterface.call('eval', 'initialinputscreenBook');
     } else if (this.actionMethodName == 'displayOpenMovements') {
       //Code Modified for Mantis 1598 by sudhakar on 1-12-2011: Movement Summary Display' screen returns 'Connection Error
       this.actionMethod = this.actionMethod + "&initialinputscreen=" + ExternalInterface.call('eval', 'initialinputscreenOpenMovements') + "&totalFlag=" + ExternalInterface.call('eval', 'totalFlagOpenMovements') + "&posLvlId=" + ExternalInterface.call('eval', 'posLvlId') + "&currencyCode=" + ExternalInterface.call('eval', 'currencyCode') + "&date=" + ExternalInterface.call('eval', 'dateOpenMovements') + "&entityId=" + ExternalInterface.call('eval', 'entityIdOpenMovements') + "&menuAccessId=" + this.menuAccessId + "&applyCurrencyThreshold=" + ExternalInterface.call('eval', 'applyCurrencyThresholdOpenMovements');
       if (ExternalInterface.call('eval', 'workflow') != null)
         this.actionMethod += "&workflow=" + ExternalInterface.call('eval', 'workflow');
     } else if (this.actionMethodName == 'getCurrencyMonitorMvmnts') {
       this.actionMethod = this.actionMethod + "&initialinputscreen=" + ExternalInterface.call('eval', 'initialinputscreenCurrency') + "&entityId=" + ExternalInterface.call('eval', 'entityIdCurrency') + "&currencyCode=" + ExternalInterface.call('eval', 'currencyCode') + "&valueDate=" + ExternalInterface.call('eval', 'valueDate') + "&callfromcurrency=y";
     } else if (this.actionMethodName == 'getAccountMonitorMvmnts') {
       this.actionMethod = this.actionMethod + "&initialinputscreen=" + ExternalInterface.call('eval', 'initialinputscreenAccount') + "&entityId=" + ExternalInterface.call('eval', 'entityIdAccount') + "&currencyCode=" + ExternalInterface.call('eval', 'currencyCode') + "&valueDate=" + ExternalInterface.call('eval', 'valueDate') + "&balanceType=" + ExternalInterface.call('eval', 'balanceTypeAccount') + "&accountId=" + ExternalInterface.call('eval', 'accountIdAccount') + '&accountType=' + ExternalInterface.call('eval', 'accountTypeAccount') + "&applyCurrencyThreshold=" + ExternalInterface.call('eval', 'applyCurrencyThresholdAccount');
       "&applyCurrencyThresholdInd=" + ExternalInterface.call('eval', 'applyCurrencyThresholdIndAccount');

     } else if (this.actionMethodName == 'getUnsettledYesterdayMvmnts') {
       this.actionMethod = this.actionMethod + "&entityId=" + ExternalInterface.call('eval', 'entityIdUnsetteled') + "&currGrp=" + ExternalInterface.call('eval', 'currGrp') + "&totalCount=" + ExternalInterface.call('eval', 'totalCountUnsetteled') + "&roleId=" + ExternalInterface.call('eval', 'roleId') + "&applyCurrencyThreshold=" + ExternalInterface.call('eval', 'applyCurrencyThresholdUnsetteled');
     } else if (this.actionMethodName == 'getOpenUnexpectedMvmnts') {
       this.actionMethod = this.actionMethod + "&entityId=" + ExternalInterface.call('eval', 'entityIdUnexpected') + "&currGrp=" + ExternalInterface.call('eval', 'currGrp') + "&valueDate=" + ExternalInterface.call('eval', 'valueDate') + "&totalCount=" + ExternalInterface.call('eval', 'totalCountUnexpected') + "&roleId=" + ExternalInterface.call('eval', 'roleId') + "&applyCurrencyThreshold=" + ExternalInterface.call('eval', 'applyCurrencyThresholdUnexpected');
     } else if (this.actionMethodName == 'getBackValuedMvmnts') {
       this.actionMethod = this.actionMethod + "&entityId=" + ExternalInterface.call('eval', 'entityIdBackvalue') + "&currGrp=" + ExternalInterface.call('eval', 'currGrp') + "&valueDate=" + ExternalInterface.call('eval', 'valueDate') + "&totalCount=" + ExternalInterface.call('eval', 'totalCountBackvalue') + "&roleId=" + ExternalInterface.call('eval', 'roleId') + "&applyCurrencyThreshold=" + ExternalInterface.call('eval', 'applyCurrencyThresholdBackvalue');
     } else if (this.actionMethodName == 'getMvmntsfromWorkFlowMonitor') {
       this.actionMethod = this.actionMethod + "&entityId=" + ExternalInterface.call('eval', 'entityIdWorkflow') + "&currGrp=" + ExternalInterface.call('eval', 'currGrp') + "&totalCount=" + ExternalInterface.call('eval', 'totalCountWorkflow') + "&roleId=" + ExternalInterface.call('eval', 'roleId') + "&posLvlId=" + ExternalInterface.call('eval', 'posLvlId') + "&predictStatus=" + ExternalInterface.call('eval', 'predictStatusWorkflow') + "&matchStatus=" + ExternalInterface.call('eval', 'matchStatusWorkflow') + "&applyCurrencyThreshold=" + ExternalInterface.call('eval', 'applyCurrencyThresholdWorkflow') + "&tabIndicator=" + ExternalInterface.call('eval', 'tabIndicatorWorkflow') + "&valueDate=" + (ExternalInterface.call('eval', 'valueDate')) + "&linkFlag=" + (ExternalInterface.call('eval', 'linkFlagWorkflow'));
     }
     this.actionMethod += "&currentFilterConf=" + StringUtils.encode64(this.noneLabel);
     this.actionMethod += "&extraFilter=" + ExternalInterface.call('eval', 'extraFilter');
     this.currentFilterConf = this.noneLabel;
     let selectedList ='';
     if (this.methodName != "search" && this.initialinputscreen == 'E')
      {
        for (let i:number=0; i < this.bottomGrid.dataProvider.length; i++)
        {
          selectedList+=this.bottomGrid.dataProvider[i].movement + ",";
        }
      }
      this.actionMethod+="&selectedList=" + selectedList;
      
     /*Set the complete URL for inputData Http*/
     this.inputData.url = this.baseURL + this.actionPath + this.actionMethod;
     this.keepSelected = true;
     /*Make the request with requestParams if any*/
     this.inputData.send(this.requestParams);
   }

   updateProfileCombo(updatedList, selectedProfile){
     this.profilesList= updatedList;
     //this.currentProfile= selectedProfile;
   }


  private getFilterAddColumns(addColsFilter): string {
    try {
      //prepare additional columns filter
      let filterAddColumns = "";
      let i = 0;
      if (addColsFilter != '' && this.useAddColsCheck == 'Y') {
        let filterdValues = addColsFilter.split('|');
        for (let key in this.addColumnsGridData) {
          if (filterdValues[i] != "") {
            if (filterdValues[i] != "All" && filterdValues[i] != undefined) {
              let dbTable = key.split("*")[1];//this.getDbTable(key);
              let column = this.addColumnsGridData[key];
              //i need to check the type of the column
              if (column == "UPDATE_DATE" || column == "VALUE_DATE" || column == "INPUT_DATE" ||
                column == "ORIG_VALUE_DATE" || column == "TO_MATCH_DATE" || column == "TO_MATCH_DATE" ||
                column == "POSTING_DATE" || column == "SETTLEMENT_DATE" || column == "TIMEEXPECTED_SETTLEMENT_DATE" ||
                column == "TIMEORIGINAL_EXPECTED_SETTLEMNT_DT") {
                filterAddColumns = filterAddColumns + dbTable + "." + column + "=" + "TO_DATE ('"+filterdValues[i]  +"' , '"+this.formatIsoTime+ "')" + "AND ";
              }
              else if (column == "required_release_time" || column == "cutoff_time") {
                filterAddColumns = filterAddColumns + dbTable + "." + column + "=" +"'"+ filterdValues[i].substring(11,16) + "'" +  " AND ";
              }
              else
                filterAddColumns = filterAddColumns + dbTable + "." + column + "=" +"'"+ filterdValues[i] +"'"+  " AND ";
            }
          }
          i++;
        }
      }
      const lastIndex = filterAddColumns.lastIndexOf("AND ");
      const FilterResult = filterAddColumns.substring(0, lastIndex);

      return FilterResult;
    }
    catch (error) {
      console.log('error', error)
    }
  }

  gridColsToXmlFilter(selectedFilter, addColsFilter) {
    this.cGrid.columnDefinitions.sort((a:any, b:any) => a.columnnumber - b.columnnumber);
    let filtertXml = "<filters>";
    let i = 0;
    let operator="";
    if (selectedFilter != '' && selectedFilter!='All|All|All|All|All|All|All|All|All|All|All|All|All|All|All|All|All|All|All|All|All|All|All|All|All|All|All|All|All|All') {
      let filteredValues = selectedFilter.split('|');
      for (let i = 0; i < this.cGrid.columnDefinitions.length; i++) {
        if (filteredValues[i] != "All" && filteredValues[i] != undefined) {
          //check which operator we will user equals, like or in
          const filterType= this.cGrid.columnDefinitions[i]['FilterType'];
          if(filterType=="MultipleSelect"){
            const separatorRegex = /[,;:]/;
            if (filteredValues[i].includes("#$#")) {
              operator= "In";
            }else{
              operator= "Equals";
            }           
          }else {
            //InputSearch
            operator= "Like";
          }
          let colName = this.getColumnName(this.cGrid.columnDefinitions[i]['field']);
          let tableName = "";
          let dataType = "";
          for (const item of this.msdDisplayColumnsData) {
            if (colName == item.columnName) {
              tableName = item.tableName;
              dataType = item.dataType;
              break;
            }
          }
          filtertXml += "<filter>";
          filtertXml += "<column_name><![CDATA[" + colName + "]]></column_name>";
          filtertXml += "<table_name><![CDATA[" + tableName + "]]></table_name>";
          filtertXml += "<type><![CDATA[" + dataType + "]]></type>";
          filtertXml += "<operator><![CDATA[" + operator + "]]></operator>";
          filtertXml += "<values>";
          let values= filteredValues[i].split("#$#");
          for (let j = 0; j < values.length; j++) {
            if(values[j]==''){
              values[j]='(EMPTY)';
            }
          if(colName=='MATCH_STATUS'  && values[j]!='(NOT EMPTY)' && values[j]!='(EMPTY)'){
              filtertXml += "<value><![CDATA[" + this.getMatchStatusCode(((values[j]).split(' '))[0]) + "]]></value>";
            }
          else if (dataType == "date") {
            const val=values[j]!='(EMPTY)'?this.convertToIso(values[j]):values[j] ;
            filtertXml += "<value><![CDATA[" + val +  "]]></value>";
            } else if (dataType == "datetime") {
            const val=values[j]!='(EMPTY)'?this.convertToIsoWithTime(values[j]):values[j] ;
            filtertXml += "<value><![CDATA[" + val + "]]></value>";
            }else if (dataType == "num" && colName == "AMOUNT") {
            let ccyFormat:string=this.jsonReader.getScreenAttributes()["currencyFormat"];
            if (values[j] != '(EMPTY)') {
              if (ccyFormat == "currencyPat2") {
                values[j] = Number(values[j].replace(/\./g, '').replace(/,/g, '.'));
              } else if (ccyFormat == "currencyPat1") {
                values[j] = Number(values[j].replace(/,/g, ''));
              }
            }
            filtertXml += "<value><![CDATA[" +values[j] + "]]></value>";
            }else{
            filtertXml += "<value><![CDATA[" + values[j] + "]]></value>"; 
            }
            }
        
          filtertXml += "</values>";
          filtertXml += "</filter>";
        }
      }
    }
    // prepare xml for additional columns
    if (addColsFilter != '' && this.useAddColsCheck == 'Y') {
      let filterdAddColsValues = addColsFilter.split('|');
      for (let key in this.addColumnsGridData) {

        if (filterdAddColsValues[i] != "All" && filterdAddColsValues[i] != undefined) {
          if (filterdAddColsValues[i].includes("#$#")) {
            operator= "In";
          }else{
            operator= "Equals";
          } 
          let dbTable = key.split("*")[1];
          let column = this.addColumnsGridData[key];
          let dbType= this.getColumnType(key.split("*")[0]);
          filtertXml += "<filter>";
          filtertXml += "<column_name><![CDATA[" + column + "]]></column_name>";
          filtertXml += "<table_name><![CDATA[" + dbTable + "]]></table_name>";
          filtertXml += "<type><![CDATA[" + dbType + "]]></type>";
          filtertXml += "<operator><![CDATA[" + operator + "]]></operator>";
          filtertXml += "<values>";
            let values= filterdAddColsValues[i].split("#$#");
            for (let j = 0; j < values.length; j++) {
              if(values[j]==''){
                values[j]='(EMPTY)';
              }
              if(column=='MATCH_STATUS'){
                filtertXml += "<value><![CDATA[" +this.getMatchStatusCode(((values[j]).split(' '))[0]) + "]]></value>"; 
              }
              else if (dbType == "date") {
                const val=values[j]!='(EMPTY)'?this.convertToIso(values[j]):values[j] ;
                filtertXml += "<value><![CDATA[" + val +  "]]></value>";
              }else if (dbType == "datetime") {
                const val=values[j]!='(EMPTY)'?this.convertToIsoWithTime(values[j]):values[j] ;
                filtertXml += "<value><![CDATA[" +val+ "]]></value>";
              }else if (dbType == "num" && column == "AMOUNT") {
                let ccyFormat:string=this.jsonReader.getScreenAttributes()["currencyFormat"];
                if (values[j] != '(EMPTY)') {
                  if (ccyFormat == "currencyPat2") {
                    values[j] = Number(values[j].replace(/\./g, '').replace(/,/g, '.'));
                  } else if (ccyFormat == "currencyPat1") {
                    values[j] = Number(values[j].replace(/,/g, ''));
                  }
                }
                filtertXml += "<value><![CDATA[" +values[j] + "]]></value>";
              }else{
                filtertXml += "<value><![CDATA[" + values[j] + "]]></value>"; 
                }

            }
          
          filtertXml += "</values>";
          filtertXml += "</filter>";
        }
        i++;
      }
    }

    filtertXml += "</filters>";
    filtertXml = prettyData.pd.xml(filtertXml);
    return filtertXml;
  }

  private getSortAddColumns(selectedSort) {
    const gridColsNbr = this.jsonReader.getColumnData().column.length - 1;
    const addColsNbr = Object.keys(this.addColumnsGridData).length;
    let addColsSort = "";
    if (selectedSort.split("|")[0] > (gridColsNbr - addColsNbr)) {
      for (let i = gridColsNbr; i > gridColsNbr - addColsNbr; i--) {
        let dataelement = this.jsonReader.getColumnData().column[i].dataelement;
        let columnNbr = this.jsonReader.getColumnData().column[i].columnNumber;
        for (let key in this.addColumnsGridData) {
          let column = this.addColumnsGridData[key];
          let table = key.split("*")[1];
          let seq = key.split("*")[0]
          if (seq == dataelement && selectedSort.split("|")[0] == columnNbr) {
            if (selectedSort.split("|")[1] == "true") {
              //addColsSort = table + "." + column + " ASC";
              addColsSort = column + " DESC";
            } else {
              //addColsSort = table + "." + column + " DESC";
              addColsSort = column + " ASC";
            }
          }
        }
      }
    } else {
      for (let i = 0; i < this.cGrid.columnDefinitions.length; i++) {
        let colNbr = this.cGrid.columnDefinitions[i]['columnnumber'];
        let colName = this.getColumnName(this.cGrid.columnDefinitions[i]['field']);
        let tableName = "";
        let dataType = "";
        for (const item of this.msdDisplayColumnsData) {
          if (colName == item.columnName && selectedSort.split("|")[0] == colNbr) {
            tableName = item.tableName;
            dataType = item.dataType;
            if (selectedSort.split("|")[1] == "true") {
              //addColsSort = tableName + "." + colName + " ASC";
              addColsSort = colName + " DESC";
            } else {
              //addColsSort = tableName + "." + colName + " DESC";
              addColsSort = colName + " ASC";
            }
          }
        }
      }
    }
    return addColsSort;
  }

  getColumnType(dataElement) {
    let dbType = "";
    for (let i = 0; i < this.cGrid.columnDefinitions.length; i++) {
      let columnData = this.cGrid.columnDefinitions[i];
      if (this.cGrid.columnDefinitions[i]['field'] == dataElement) {
        //dbType = this.getTypeMapping(this.cGrid.columnDefinitions[i]['columnType']);
        dbType = this.cGrid.columnDefinitions[i]['columnType'];
      }
    }
    return dbType;
  }

 getTypeMapping(type){
  var dbType = null;

  switch (type) {
    case "str":
      dbType = "VARCHAR2";
      break;
    case "num":
      dbType = "NUMBER";
      break;  
    case "date":
        dbType = "Date";
      break;
    case "amt":
        dbType = "AMOUNT";
      break;
    default:
      break;
    }
    return dbType;
 }
  
  getColumnName(dataElement) {
    var colName = null;

    switch (dataElement) {
      case "pos":
        colName = this.entityCombo.selectedLabel=="All"?"POSITION_LEVEL":"POSITION_LEVEL_NAME";
        break;
      case "value":
        colName = "VALUE_DATE";
        break;
      case "amount":
        colName = "AMOUNT";
        break;
      case "sign":
        colName = "SIGN";
        break;
      case "entity":
          colName = "ENTITY_ID";
        break;
      case "ccy":
        colName = "CURRENCY_CODE";
        break;
      case "ref1":
        colName = "REFERENCE1";
        break;
      case "account":
        colName = "ACCOUNT_ID";
        break;
      case "input":
        colName = "INPUT_DATE";
        break;
      case "cparty":
        colName = "COUNTERPARTY_ID";
        break;
      case "pred":
        colName = "PREDICT_STATUS";
        break;
      case "status":
        colName = "MATCH_STATUS";
        break;
      case "ext":
        colName = "EXT_BAL_STATUS";
        break;

      case "matchid":
        colName = "MATCH_ID";
        break;

      case "source":
        colName = "INPUT_SOURCE";
        break;

      case "format":
        colName = "MESSAGE_FORMAT";
        break;

      case "beneficiary":
        colName = "BENEFICIARY_ID";
        break;
      case "ref2":
        colName = "REFERENCE2";
        break;
      case "ref3":
        colName = "REFERENCE3";
        break;
      case "xref":
        colName = "REFERENCE4";
        break;
      case "movement":
        colName = "MOVEMENT_ID";
        break;

      case "book":
        colName = "BOOKCODE";
        break;

      case "custodian":
        colName = "CUSTODIAN_ID";
        break;

      case "update_date":
        colName = "UPDATE_DATE";
        break;
      case "matchingparty":
        colName = "MATCHING_PARTY";
        break;

      case "producttype":
        colName = "PRODUCT_TYPE";
        break;


      case "postingdate":
        colName = "POSTING_DATE";
        break;


      case "extra_text1":
        colName = "EXTRA_TEXT1";
        break;

      case "ilmfcast":
        colName = "ILM_FCAST_STATUS";
        break;

      case "uetr":
        colName = "UETR";
        break;
      default:
        break;
    }
    return colName;

  }

getDbTable(table) {
    var tableDB="";
    switch (table) {
      case "Account":
        tableDB = "P_ACCOUNT";
        break;
      case "Movement":
        tableDB = "P_MOVEMENT";
        break;
      case "Movement_Ext":
        tableDB = "P_MOVEMENT_EXT";
        break;

      default:
        break;
    }
    return tableDB;
  }

  getDataProvider(rows, columnName, includeEmptyValues = true) {
    if (!rows) return [];
  
    if (!Array.isArray(rows)) {
      rows = [rows];
    }
  
    let dataProvider = [];
  
    rows.forEach(row => {
      let content = row[columnName] && row[columnName].content ? row[columnName].content : "";
      let isEmpty = !content;
  
      if (isEmpty) {
        if (includeEmptyValues && !dataProvider.some(item => item.value === "(EMPTY)")) {
          dataProvider.push({ value: "(EMPTY)", label: "(EMPTY)" });
        }
        return; // skip further processing if content is empty
      }
  
      if (includeEmptyValues && !dataProvider.some(item => item.value === "(NOT EMPTY)")) {
        dataProvider.push({ value: "(NOT EMPTY)", label: "(NOT EMPTY)" });
      }
  
      // Custom logic for 'status'
      let displayValue = columnName === 'status' ? content.split(" ")[0] : content;
  
      if (!dataProvider.some(item => item.value === displayValue)) {
        dataProvider.push({ value: displayValue, label: displayValue });
      }
    });
  
    dataProvider.sort((a, b) => a.label.localeCompare(b.label)); // sort alphabetically
    return dataProvider;
  }
  
  
  prepareDataProvider(listValues){
    const dataProvider= [];
    for (let i: number = 0; i < listValues.length; i++) {
    dataProvider.push({ value: listValues[i], label: listValues[i] });
    }
    return dataProvider;
  }

  convertToIso(dateAsString) {
    let formattedDate="" ;
    if(this.dateFormat=='dd/MM/yyyy'){
    const [day, month, year] = dateAsString.split("/"); // split the string into day, month, and year components
    formattedDate = `${year}-${month}-${day}`;
    }else{
    const [month, day, year] = dateAsString.split("/"); // split the string into day, month, and year components
    formattedDate = `${year}-${month}-${day}`;
    }
    return formattedDate;
  }

  convertToIsoWithTime(dateTimeAsString) {
    let formattedDateTime="" ;
    const [dateString, timeString] = dateTimeAsString.split(' ');
    if(this.dateFormat=='dd/MM/yyyy'){
    const [day, month, year] = dateString.split("/"); // split the string into day, month, and year components
    formattedDateTime = `${year}-${month}-${day} ${timeString}`;
    }else{
    const [month, day, year] = dateString.split("/"); // split the string into day, month, and year components
    formattedDateTime = `${year}-${month}-${day} ${timeString}`;
    }
    return formattedDateTime;
  }

  getMatchStatusCode(desc) {
		let statusCode="";
		
		switch (desc) {
		case "AUTHORISE":
			statusCode = "A";
			break;
		case "CONFIRMED":
			statusCode = "C";
			break;
		case "OFFERED":
			statusCode = "M";
			break;
		case "OUTSTANDING":
			statusCode = "L";
			break;
		case "RECONCILED":
			statusCode = "E";
			break;
		case "REFERRED":
			statusCode = "R";
			break;
    case "SUSPENDED":
        statusCode = "S";
        break;
		default:
			break;
		}

		return statusCode;

	}

}
//Define lazy loading routes
const routes: Routes = [
  { path: '', component: MovementSummaryDisplay }
];
export const routing: ModuleWithProviders = RouterModule.forChild(routes);
//Define an NgModule that will be loaded by app module
@NgModule({
  imports: [routing, SwtToolBoxModule],
  declarations: [MovementSummaryDisplay],
  entryComponents: []
})
export class MovementSummaryDisplayModule {}
