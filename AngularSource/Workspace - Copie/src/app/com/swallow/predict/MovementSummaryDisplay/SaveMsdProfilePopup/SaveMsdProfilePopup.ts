import { Component, OnInit, ElementRef, ModuleWithProviders, NgModule, ViewChild } from '@angular/core';
import { SwtAlert, CommonService, SwtModule, SwtToolBoxModule, SwtEditableComboBox, ExternalInterface, SwtComboBox, Alert, focusManager, Keyboard, SwtUtil, SwtButton, SwtLabel } from 'swt-tool-box';
import { RouterModule, Routes } from '@angular/router';

@Component({
  selector: 'SaveMsdProfilePopup',
  templateUrl: './SaveMsdProfilePopup.html',
  styleUrls: ['./SaveMsdProfilePopup.css']
})
export class SaveMsdProfilePopup extends SwtModule implements OnInit {

  @ViewChild('profileCombo') profileCombo: SwtEditableComboBox;
  @ViewChild('saveButton') saveButton: SwtButton;
  @ViewChild('cancelButton') cancelButton: SwtButton;
  @ViewChild('profileNameLabel') profileNameLabel: SwtLabel;
  private swtAlert: SwtAlert;
  public saveProfileCollection = [];
  public msdAddColsCombo: SwtComboBox;
  public selectedProfileItem = '';

  constructor(private commonService: CommonService, private element: ElementRef) {
    super(element, commonService);
    this.swtAlert = new SwtAlert(commonService);
  }
  ngOnInit() {
    this.saveButton.label = ExternalInterface.call('getBundle','text','button-saveProfile','Save');
    this.saveButton.toolTip = ExternalInterface.call('getBundle','text','button-saveProfile','Save');
  
    this.cancelButton.label = ExternalInterface.call('getBundle','text','button-close','Cancel');
    this.cancelButton.toolTip = ExternalInterface.call('getBundle','tip','button-close','Cancel changes');

    this.profileNameLabel.text = SwtUtil.getPredictMessage('userprofile.name');
    
  }

  onLoad(): void {
    this.profileCombo.dataProvider = this.saveProfileCollection;
    this.profileCombo.selectedLabel = this.selectedProfileItem;
  }



  private temporarySavedProfile: string = null;
  /**
   * This function is used to confirm the calculation process after clicking on the link button
   */
  public saveProfileClickHandlerFromIframe(): void {
    const selectedProfile = this.profileCombo.selectedLabel;
    this.temporarySavedProfile = selectedProfile;
    if (selectedProfile == "") {
      this.swtAlert.show(ExternalInterface.call('getBundle', 'text', 'alert-fillMandatoryFields', 'Please fill the profile name before saving'));
    } else if (this.msdAddColsCombo.getIndexOf(selectedProfile) != -1 || this.msdAddColsCombo.getIndexOf("*" + selectedProfile) != -1) {
      this.swtAlert.show(ExternalInterface.call('getBundle', 'text', 'alert-overwriteProfile', 'Are you sure you want to overwrite this profile?'), //text
        ExternalInterface.call('getBundle', 'text', 'alert-warning', 'Warning'), Alert.OK | Alert.CANCEL, null, this.overwriteAlertListenerIFrame.bind(this),  Alert.CANCEL); //icon and default button
    } else {
      this.parentDocument.saveNewProfile(selectedProfile);
      this.popupClosed();
    }


  }


  /**
 * keyDownEventHandler
 * param event
 * This is a key event listener, used to perform the operation
 * when hit the enter key based on the currently focused property(button)
 */
  keyDownEventHandler(event): void {
    try {
      // Currently focussed property name
      let eventString: string = Object(focusManager.getFocus()).name;
      if ((event.keyCode == Keyboard.ENTER)) {
        if (eventString == "saveButton") {
          this.saveProfileClickHandlerFromIframe();
        } else if (eventString == "cancelButton") {
          this.popupClosed();
        } else if (eventString === 'helpIcon') {
          this.doHelp();
        }
      }
    } catch (error) {
      // log the error in ERROR LOG
      console.log(error, 'Predict', "CurrencyMaintenance", "keyDownEventHandler");
    }
  }


  /**
 * doHelp
 * Function is called when "Help" button is click. Displays help window
 */
  doHelp(): void {
    try {
      // SwtHelpWindow.open(this.baseURL + "help.do?method=print&screenName=Currency+Maintenance+Details");
      ExternalInterface.call("help");
    } catch (e) {
      // log the error in ERROR LOG
      SwtUtil.logError(e, 'Predict', 'CurrencyMaintenance', 'doHelp', 0);
    }
  }


  /**
  * popupClosed
  * Method to close child windows when this screen is closed
  */
  popupClosed(): void {
    this.dispose();
  }
  /**
   * dispose
   * This is a event handler, used to close the current tab/window
   */
  dispose(): void {
    try {
      if (this.titleWindow) {
        this.close();
      } else {
        window.close();
      }
    } catch (error) {
      console.log(error, 'Predict', "CurrencyMaintenance", "dispose");
    }
  }



  /**
   * This function is used to listen to the alert
   *
   * @param eventObj CloseEvent
   */
  private overwriteAlertListenerIFrame(eventObj): void {
    // Checks for Alert OK
    if (eventObj.detail == Alert.OK) {
      // Recalculate data if "OK" is clicked
      this.parentDocument.saveProfile(this.temporarySavedProfile);
      this.popupClosed();
    }
    this.temporarySavedProfile = null;
  }
}


// Define lazy loading routes
const routes: Routes = [
  { path: '', component: SaveMsdProfilePopup }
];
export const routing: ModuleWithProviders = RouterModule.forChild(routes);
// Define an NgModule that will be loaded by app module
@NgModule({
  imports: [routing, SwtToolBoxModule],
  declarations: [SaveMsdProfilePopup],
  entryComponents: [SaveMsdProfilePopup],
  providers: [{ provide: 'saveMsdProfilePopup', useValue: SaveMsdProfilePopup }]
})
export class SaveMsdProfilePopupModule { }
