import { Component, OnInit, ModuleWithProviders, NgModule, ElementRef, ViewChild } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';
import { SwtToolBoxModule, SwtModule, CommonService, SwtCanvas, SwtButton, SwtUtil, SwtCommonGrid, SwtAlert, HTTPComms, JSONReader, ExternalInterface, SwtComboBox, SwtCheckBox, SwtLabel, Logger, Alert, StringUtils, SwtPopUpManager, ModuleLoader, ModuleEvent } from 'swt-tool-box';
import { AddColsPopUp } from './AddColsPopUp/AddColPopUp';
declare var require: any;
const $ = require('jquery');
var prettyData = require('pretty-data');

@Component({
  selector: 'app-additional-columns',
  templateUrl: './AdditionalColumns.html',
  styleUrls: ['./AdditionalColumns.css']
})
export class AdditionalColumns extends SwtModule implements OnInit {

  /***********SwtCanvas***********/
  @ViewChild('colCanvas') colCanvas: SwtCanvas;

  /***********SwtButton***********/
  @ViewChild('addButton') addButton: SwtButton; 
  @ViewChild('deleteButton') deleteButton: SwtButton;
  @ViewChild('saveProfileImage') saveProfileImage: SwtButton;
  @ViewChild('revertProfileImage') revertProfileImage: SwtButton;
  @ViewChild('deleteProfileImage') deleteProfileImage: SwtButton; 
  @ViewChild('closeButton') closeButton: SwtButton;

  /***********SwtComboBox***********/
  @ViewChild('profileCombo') profileCombo: SwtComboBox;
 
  /***********SwtLabel***********/
  @ViewChild('generalProfileLbl') generalProfileLbl: SwtLabel;
  @ViewChild('profileIdLbl') profileIdLbl: SwtLabel;

  /***********SwtCheckBox***********/
  @ViewChild('generalProfilecheck') generalProfilecheck: SwtCheckBox;
  
  private columnsGrid: SwtCommonGrid;
  private colGridRows;
  private gridColumns;
  private menuAccessId;
  private swtAlert: SwtAlert;
  private ordertData = new HTTPComms(this.commonService);
  /**
   * Data Objects
   **/
  public jsonReader: JSONReader = new JSONReader();
  public lastRecievedJSON;
  public prevRecievedJSON;
  public initReceivedJSON;
  /**
    * Communication Objects
    **/
  public inputData = new HTTPComms(this.commonService);
  public baseURL: string = SwtUtil.getBaseURL();
  private actionMethod: string = "";
  private actionPath: string = "";
  private requestParams = [];
  private _invalidComms: string;
  private selectValues;
  private logger: Logger = null;
  private methodName;
  private deletedRow: number = -1;
  public enabledRow: number = -1;
  private addColsData = [];
  private gridMetadata;
  private deletedAccount;
  private rowIndex;
  private dataField;
  private oldComboVal;
  private newComboVal;
  private xml="";
  private additionalColsData= [];
  private addColsForFilter= [];
  private saveProfilePopupWindow;
  private errorLocation = 0;
  private additionalColsList =[];
  private columnsList = [];
  private selectedProfile;
  private profileId;
  private entityId;
  private source;
  public operationsList = [];
  public win : any;
  private deletedProfile;
  constructor(private commonService: CommonService, private element: ElementRef) {
    super(element, commonService);
    this.swtAlert = new SwtAlert(commonService);
    this.logger = new Logger('Account Specific Sweep Format', this.commonService.httpclient);
    window["Main"] = this;
  }

  ngOnInit() {
    this.columnsGrid = <SwtCommonGrid>this.colCanvas.addChild(SwtCommonGrid);

    this.addButton.label = SwtUtil.getPredictMessage('button.add', null);
    this.deleteButton.label = SwtUtil.getPredictMessage('button.delete', null);
    //this.saveButton.label = SwtUtil.getPredictMessage('button.save', null);
    //this.saveAsButton.label = SwtUtil.getPredictMessage('button.msd.saveAs', null);
    this.closeButton.label = SwtUtil.getPredictMessage('button.close', null); 

    //this.generalProfileLbl.text = SwtUtil.getPredictMessage('msd.generalProfile.label', null); 
    this.profileIdLbl.text = SwtUtil.getPredictMessage('msd.profileId.label', null);
    this.addButton.toolTip = SwtUtil.getPredictMessage('button.tooltip.msd.addColumn', null);
    this.deleteButton.toolTip = SwtUtil.getPredictMessage('button.tooltip.msd.deleteColumn', null);
    this.saveProfileImage.toolTip = SwtUtil.getPredictMessage('msdAdditionalColumns.saveProfileImageTooltip', null);
    this.revertProfileImage.toolTip = SwtUtil.getPredictMessage('msdAdditionalColumns.reloadProfileTooltip', null);
    this.deleteProfileImage.toolTip = SwtUtil.getPredictMessage('msdAdditionalColumns.deleteProfileImageTooltip', null);
    this.closeButton.toolTip = SwtUtil.getPredictMessage('tooltip.close', null);

    this.profileCombo.toolTip= SwtUtil.getPredictMessage('msdAdditionalColumns.profileComboTooltip', null);

    //this.generalProfilecheck.toolTip= SwtUtil.getPredictMessage('msdAdditionalColumns.generalProfileCheckTooltip', null);

    this.columnsGrid.editable=true;
  }



onLoad() {
  this.requestParams = [];
  this.menuAccessId = ExternalInterface.call('eval', 'menuAccessId');
  this.profileId = ExternalInterface.call('eval', 'profileId');
  this.entityId= window.opener.instanceElement.entityCombo.selectedLabel;
  this.source = ExternalInterface.call('eval', 'source');
  if (this.menuAccessId) {
    if (this.menuAccessId !== "") {
      this.menuAccessId = Number(this.menuAccessId);
    }
  }
  this.inputData.cbStart = this.startOfComms.bind(this);
  this.inputData.cbStop = this.endOfComms.bind(this);
  this.inputData.cbResult = (event) => {
    this.inputDataResult(event);
  };
  this.inputData.cbFault = this.inputDataFault.bind(this);
  this.inputData.encodeURL = false;
  this.actionPath = "outstandingmovement.do?";
  this.actionMethod = 'method=displayColScreenData';
  this.requestParams['menuAccessId'] = this.menuAccessId;
  this.requestParams['profileId'] = this.profileId?this.profileId:"<None>";
  this.inputData.url = this.baseURL + this.actionPath + this.actionMethod;
  this.inputData.send(this.requestParams);

  this.columnsGrid.ITEM_CHANGED.subscribe((item) => {
    this.methodName="change";
    this.updateColumnDetails(item);
  });
  this.columnsGrid.onRowClick = (event) => {
    this.cellClickEventHandler(event);
  };


}


  updateData() {
    this.columnsGrid.gridData = { size: 0, row: [] };
    this.addColsData=[];
    this.profileCombo.selectedLabel = "<None>";

    this.saveProfileImage.enabled = true;
    this.revertProfileImage.enabled = false;
    this.deleteProfileImage.enabled = false;
    this.deleteButton.enabled=false;
    this.deleteButton.buttonMode=false;
    //update the profile combo data
    this.additionalColsList.splice(this.deletedProfile, 1);
    this.profileCombo.dataProvider = this.additionalColsList;
    this.profileCombo.setComboData(this.additionalColsList);
     //update profile combo of msd option popup
     if(window.opener && window.opener.instanceElement && this.source=="msdScreen")
     window.opener.instanceElement.updateProfileCombo(this.additionalColsList, this.profileCombo.selectedLabel);
     
     if(window.opener && window.opener.instanceElement2 && this.source=="settingScreen")
     window.opener.instanceElement2.updateProfileCombo(this.additionalColsList, this.profileCombo.selectedLabel);
     
  }



inputDataResult(event): void {
  // Checks the inputData and stops the communication
  if (this.inputData.isBusy()) {
    this.inputData.cbStop();
  } else {
    this.lastRecievedJSON = event;
    this.jsonReader.setInputJSON(this.lastRecievedJSON);

    if (this.jsonReader.getRequestReplyStatus()) {
      if ((this.lastRecievedJSON != this.prevRecievedJSON)) {

        if (!this.jsonReader.isDataBuilding()) {
          window.opener.instanceElement.openFlag=true;
          //const savedProfile=window.opener?window.opener.instanceElement.currentProfile:"<None>";
          this.addColsData=[];
          this.newComboVal="";
          this.oldComboVal="";
          const profile= this.jsonReader.getSingletons().profileId;
          this.additionalColsList= this.jsonReader.getSelects().select[0].option;
          this.profileCombo.setComboData(this.jsonReader.getSelects());
          this.profileCombo.selectedLabel= profile;
          const obj = { columns: this.lastRecievedJSON.mvtAdditionalCol.additionalColumnGrid.metadata.columns };
          this.gridMetadata = this.lastRecievedJSON.mvtAdditionalCol.additionalColumnGrid.metadata;
          this.selectValues=this.lastRecievedJSON.mvtAdditionalCol.selects;
          this.columnsGrid.gridComboDataProviders(this.selectValues);

          this.gridColumns=this.lastRecievedJSON.mvtAdditionalCol.additionalColumnGrid.metadata.columns.column;
          this.columnsGrid.CustomGrid(obj);
          this.colGridRows = this.lastRecievedJSON.mvtAdditionalCol.additionalColumnGrid.rows;
          if (this.colGridRows && this.colGridRows.size > 0) {
            /*for( let j = 0 ; j<this.colGridRows.row.length; j++) {
              let columnsList = [];
              let selectedTable = this.colGridRows.row[j].table.content;
              columnsList = this.jsonReader.getSelects()['select'].find(x => x.id === selectedTable).option;
              var col = this.columnsGrid.columnDefinitions.find(x => x.id == "column");
              if (col) {
                col['params']['selectDataSource'] = columnsList;
              }
            }*/
            this.columnsGrid.gridData = this.colGridRows;
            this.columnsGrid.setRowSize = this.jsonReader.getRowSize();

            this.columnsGrid.enableDisableCells = (row, field) => {
              if(field=='label'){
                return true;
                }else{
              return false;
                }
            }
            this.columnsGrid.refresh();
            //populate columns Data array
            for (let i = 0; i < this.colGridRows.size; i++) {
              const table = this.columnsGrid.gridData[i].table;
              const column = this.columnsGrid.gridData[i].column;
              const label = this.columnsGrid.gridData[i].label;
              const sequence = this.columnsGrid.gridData[i].sequence;
              this.addColsData.push({
                table: { clickable: false, content: table, negative: false },
                column: { clickable: false, content: column, negative: false },
                label: { clickable: false, content: label, negative: false },
                sequence: { clickable: false, content: sequence, negative: false }
              });
            }
          }
          else {
            this.columnsGrid.gridData = { size: 0, row: [] };
          }

          if (this.profileCombo.selectedLabel=="<None>"){
            this.saveProfileImage.enabled=true;
            this.deleteProfileImage.enabled=false;
            this.revertProfileImage.enabled=false;
          }else{
            this.saveProfileImage.enabled=true;
            this.deleteProfileImage.enabled=true;
            this.revertProfileImage.enabled=false;
          }
        }
      }
    } else {
      if (this.lastRecievedJSON.hasOwnProperty("request_reply")) {
        this.swtAlert.error(this.jsonReader.getRequestReplyMessage() + "\n" + this.jsonReader.getRequestReplyLocation(), "Error");
      }
    }

  }
 
}

cellClickEventHandler(event) {
  //Variable for errorLocation
  let errorLocation = 0;
  try {
    this.deletedAccount = this.columnsGrid.selectedItem?this.columnsGrid.selectedItem.label.content:"";
    errorLocation = 10;
    if (this.columnsGrid.selectedIndex >= 0) {
      errorLocation = 20;
      this.deleteButton.enabled = true;
      this.deleteButton.buttonMode = true;
    } else {
      errorLocation = 30;
      this.deleteButton.enabled = false;
      this.deleteButton.buttonMode = false;
    }
  } catch (error) {
    // log the error in ERROR LOG
    this.logger.error('method [cellClickEventHandler] - error: ', error, 'errorLocation: ', errorLocation);
    SwtUtil.logError(error, SwtUtil.PREDICT_MODULE_ID, 'AdditionalColumns.ts', "cellClickEventHandler", errorLocation);
  }

}


  addHandler() {

    //Variable for errorLocation
    let errorLocation = 0;
    try {
      this.revertProfileImage.enabled = true;
      this.methodName = "add";
      this.deletedRow = -1;
      this.enabledRow++;
      //reset row selections and disable buttons
      //this.acctSweepBalGrpGrid.selectedIndex = -1;
      this.deleteButton.enabled = true;
      this.deleteButton.buttonMode = true;
     /* if (this.newComboVal && !this.listLabelArr.includes(this.newComboVal))
        this.listLabelArr.push(this.newComboVal);*/
      errorLocation = 30;
      if (this.columnsGrid.gridData.length > 0 && this.columnsGrid.gridData[0].table == ""){
        this.swtAlert.error(SwtUtil.getPredictMessage('additionalColumns.alert.emptyField', null) + " table name");
      }else if (this.columnsGrid.gridData.length > 0 && this.columnsGrid.gridData[0].column == ""){
        this.swtAlert.error(SwtUtil.getPredictMessage('additionalColumns.alert.emptyField', null) + " column name");
      }else if (this.columnsGrid.gridData.length > 0 && this.columnsGrid.gridData[0].label == "") {
        this.swtAlert.error(SwtUtil.getPredictMessage('additionalColumns.alert.emptyLabel', null));
      } else {
        this.addColsData.splice(0, 0, {
          table: { clickable: false, content: "P_ACCOUNT", negative: false },
          column: { clickable: false, content: "", negative: false },
          label: { clickable: false, content: "", negative: false },
          sequence: { clickable: false, content: "", negative: false }
        });
        errorLocation = 40;
        for (let index = 0; index < this.gridMetadata.columns.column.length; index++) {
          errorLocation = 20;
          //if (this.gridMetadata.columns.column[index].dataelement == "label")
          this.gridMetadata.columns.column[index]['editable'] = true;
        }
        errorLocation = 30;
        this.columnsGrid.CustomGrid(this.gridMetadata);
        this.columnsGrid.gridData = { row: this.addColsData, size: this.addColsData.length };
        errorLocation = 50;
        this.columnsGrid.enableDisableCells = (row, field) => {
          return this.enableDisableRow(row, field);
        };

        this.columnsGrid.refresh();
        //this.columnsGrid.selectedIndex=0;
        errorLocation = 60;
        this.profileContentChanged();
      }
    } catch (error) {
      // log the error in ERROR LOG
      this.logger.error('method [addHandler] - error: ', error, 'errorLocation: ', errorLocation);
      SwtUtil.logError(error, SwtUtil.PREDICT_MODULE_ID, 'AdditionalColumns.ts', "addHandler", errorLocation);
    }
  }

  private enableDisableRow(row: any, field: string): boolean {
    //Variable for errorLocation
    let errorLocation = 0;
    try {
      if (row.id == 0) {
        errorLocation = 10;
        return true;
      } else {
        if(field=='label'){
        return true;
        }else{
        return false;
      }
      }
    } catch (error) {
      // log the error in ERROR LOG
      this.logger.error('method [enableDisableRow] - error: ', error, 'errorLocation: ', errorLocation);
      SwtUtil.logError(error, SwtUtil.PREDICT_MODULE_ID, 'AdditionalColumns.ts', "enableDisableRow", errorLocation);
    }
  }

  updateColumnDetails(event) {
    //Variable for errorLocation
    let errorLocation = 0;
    // string to hold the data element 
    var dataElement: string;
    try {
      this.requestParams = [];
      //list of checks in case of change action
      if (event) {
        this.rowIndex = event.rowIndex;
        errorLocation = 10;
        this.dataField = event.dataField;
        errorLocation = 20;
        this.oldComboVal = event.listData.oldValue;
        errorLocation = 30;
        this.newComboVal = event.listData.newValue;
        if (!this.newComboVal && this.dataField != 'label'){
          this.swtAlert.error(SwtUtil.getPredictMessage('additionalColumns.alert.emptyValue', null));
           //revert back old value
           this.columnsGrid.dataProvider[this.rowIndex].slickgrid_rowcontent[this.dataField].content = this.oldComboVal;
           this.columnsGrid.dataProvider[this.rowIndex][this.dataField] = this.oldComboVal;
           errorLocation = 90;
           this.columnsGrid.refresh();
           this.addColsData[this.rowIndex][this.dataField].content = this.oldComboVal;
           return;
        }else if (this.dataField == 'label' && this.columnsGrid.dataProvider.length > 0) {
            for (let i = 0; i < this.columnsGrid.dataProvider.length; i++) {
              let label= this.columnsGrid.dataProvider[i].label;
              if(this.rowIndex!=i && this.newComboVal && label==this.newComboVal){
              this.swtAlert.error(SwtUtil.getPredictMessage('additionalColumns.alert.changeValues', null));
              //revert back old value
              this.columnsGrid.dataProvider[this.rowIndex].slickgrid_rowcontent[this.dataField].content = this.oldComboVal;
              this.columnsGrid.dataProvider[this.rowIndex][this.dataField] = this.oldComboVal;
              errorLocation = 90;
              this.columnsGrid.refresh();
              this.addColsData[this.rowIndex][this.dataField].content = this.oldComboVal;
              return;
              }else{
              this.addColsData[this.rowIndex][this.dataField].content = this.newComboVal;
            }
          }
          } else {
        if (this.dataField=='table'){
          if(this.newComboVal=="Account"){
            this.newComboVal="P_ACCOUNT";
          }
           //reset combo column value
           this.columnsGrid.dataProvider[this.rowIndex].slickgrid_rowcontent['column'].content = "";
           this.columnsGrid.dataProvider[this.rowIndex]['column'] = "";
           this.columnsGrid.dataProvider[this.rowIndex].slickgrid_rowcontent['label'].content = "";
           this.columnsGrid.dataProvider[this.rowIndex]['label'] = "";
           this.columnsGrid.dataProvider[this.rowIndex].slickgrid_rowcontent[this.dataField].content = this.newComboVal;
           this.columnsGrid.dataProvider[this.rowIndex][this.dataField] = this.newComboVal;
           errorLocation = 40;
           this.columnsGrid.refresh();
           this.addColsData[this.rowIndex]['column'].content = "";
           this.addColsData[this.rowIndex]['label'].content = "";
          }
            else if (this.dataField == 'column') {
            //reset label value
            this.columnsGrid.dataProvider[this.rowIndex].slickgrid_rowcontent['label'].content = "";
            this.columnsGrid.dataProvider[this.rowIndex]['label'] = "";
            this.addColsData[this.rowIndex]['label'].content = "";
            this.columnsGrid.refresh();
          } 

          this.addColsData[this.rowIndex][this.dataField].content = this.newComboVal;

        }
        this.profileContentChanged();
      }
    } catch (error) {
      // log the error in ERROR LOG
      this.logger.error('method [updateSweepAccountVal] - error: ', error, 'errorLocation: ', errorLocation);
      SwtUtil.logError(error, SwtUtil.PREDICT_MODULE_ID, 'AdditionalColumns.ts', "updateSweepAccountVal", errorLocation);
    }
  }


  /**
   * This function is used to revert profile changes
   */
  public revertProfileClickHandler(event): void {
    this.swtAlert.show(SwtUtil.getPredictMessage('additionalColumns.alertOverwriteProfile', null), //text
    SwtUtil.getPredictMessage('alert_header.confirm'), Alert.OK | Alert.CANCEL,
      null,
      this.revertAlertListener.bind(this), Alert.CANCEL);
  }

   /**
   * This function is used to listen to the alert
   *
   * @param eventObj CloseEvent
   */
  private revertAlertListener(eventObj): void {
    // Checks for Alert OK
    if (eventObj.detail == Alert.OK) {
      //this.deleteButton.enabled= false;
      //this.deleteButton.buttonMode= false;
      // Recalculate data if "OK" is clicked
      this.changeProfile(null);
    }
  }

deleteHandler() {
  //Variable for errorLocation
  let errorLocation = 0;
  try {
    Alert.yesLabel = SwtUtil.getPredictMessage('alert.yes.label');
    Alert.noLabel = SwtUtil.getPredictMessage('alert.no.label');
    errorLocation = 10;
    var message: string = StringUtils.substitute((SwtUtil.getPredictMessage('additionalColumns.alert.deleteColumn', null)));
    errorLocation = 20;
    this.swtAlert.confirm(message, SwtUtil.getPredictMessage('alert_header.confirm'), Alert.YES | Alert.NO, null, this.proceedWithDelete.bind(this));
  } catch (error) {
    // log the error in ERROR LOG
    this.logger.error('method [deleteHandler] - error: ', error, 'errorLocation: ', errorLocation);
    SwtUtil.logError(error, SwtUtil.PREDICT_MODULE_ID, 'AdditionalColumns.ts', "deleteHandler", errorLocation);
  }
}

proceedWithDelete(event) {
  //Variable for errorLocation
  let errorLocation = 0;
  try {
    if (event.detail == Alert.YES) {
      errorLocation = 0;
      this.revertProfileImage.enabled=true;
      this.methodName = "delete";
      //this.rowDeletedFlag = true;
      this.deletedRow = this.columnsGrid.selectedIndex;
      errorLocation = 10;
      let deletedColumn = this.columnsGrid.selectedItem.label.content;
      errorLocation = 20;
      this.columnsGrid.removeSelected();
      errorLocation = 40;
      this.addColsData.splice(this.deletedRow, 1);
      this.columnsGrid.gridData = { row: this.addColsData, size: this.addColsData.length };
      errorLocation = 50;
      this.columnsGrid.enableDisableCells = (row, field) => {
          return this.enableDisableRow(row, field);
      };

      this.columnsGrid.refresh();
      this.enabledRow--;
      this.deleteButton.enabled= false;
      this.deleteButton.buttonMode= false;
      this.columnsGrid.selectedIndex=-1;
      errorLocation = 60;
      this.profileContentChanged();
     //update profile combo of msd option popup
     if(window.opener && window.opener.instanceElement && this.source=="msdScreen")
     window.opener.instanceElement.updateProfileCombo(this.additionalColsList, this.profileCombo.selectedLabel);
     
     if(window.opener && window.opener.instanceElement2 && this.source=="settingScreen")
     window.opener.instanceElement2.updateProfileCombo(this.additionalColsList, this.profileCombo.selectedLabel);
           
    }
  } catch (error) {
    // log the error in ERROR LOG
    this.logger.error('method [proceedWithDelete] - error: ', error, 'errorLocation: ', errorLocation);
    SwtUtil.logError(error, SwtUtil.PREDICT_MODULE_ID, 'AddtionalColumns.ts', "proceedWithDelete", errorLocation);
  }
}

/**                                                                                                                  
 * close the window from the close button                                                                            
 **/
closeHandler(): void {
  //call for close window                                                                                          
  ExternalInterface.call("closeHandler");
}


  closeAndUpdate() {
    if (this.profileCombo.selectedLabel.startsWith("*")) {
      Alert.yesLabel = SwtUtil.getPredictMessage('alert.yes.label');
      Alert.noLabel = SwtUtil.getPredictMessage('alert.no.label');
      var message: string = StringUtils.substitute((SwtUtil.getPredictMessage('entity.CloseConfirm', null)));
      this.swtAlert.confirm(message, SwtUtil.getPredictMessage('alert_header.confirm'), Alert.YES | Alert.NO, null, this.proceedWithClose.bind(this));

    } else {
      ExternalInterface.call("closeHandler");
      if (window.opener.instanceElement) {
        window.opener.instanceElement.openFlag = false;
      }
      if (window.opener.instanceElement2 && this.source == "settingScreen") {
        window.opener.instanceElement2.addColsCombo.selectedLabel = this.profileCombo.selectedLabel;
        window.opener.instanceElement2.addColsGridData = this.gridColumns.gridData;
      }
    }
  }


  proceedWithClose(event){
      //Variable for errorLocation
  let errorLocation = 0;
  try {
    if (event.detail == Alert.YES) {
  ExternalInterface.call("closeHandler");
  }

  } catch (error) {
    // log the error in ERROR LOG
    this.logger.error('method [proceedWithClose] - error: ', error, 'errorLocation: ', errorLocation);
    SwtUtil.logError(error, SwtUtil.PREDICT_MODULE_ID, 'AddtionalColumns.ts', "proceedWithClose", errorLocation);
  }
}


saveProfile(profileId){
  if(this.profileCombo.dataProvider[this.profileCombo.selectedIndex].content.toString().indexOf("*") != -1) {
    this.profileCombo.dataProvider[this.profileCombo.selectedIndex].content = (this.profileCombo.selectedLabel).replace("*", "");
    this.profileCombo.selectedLabel = this.profileCombo.dataProvider[this.profileCombo.selectedIndex].content;
  }    //Variable for errorLocation
    let errorLocation = 0;
    try {
      this.requestParams = [];
      //this.addColsGridChanges();
      errorLocation = 100;
      this.menuAccessId = ExternalInterface.call('eval', 'menuAccessId');
      errorLocation = 110;
      if (this.menuAccessId) {
        if (this.menuAccessId !== "") {
          this.menuAccessId = Number(this.menuAccessId);
        }
      }
      errorLocation = 10;
      this.inputData.cbStart = this.startOfComms.bind(this);
      this.inputData.cbStop = this.endOfComms.bind(this);
      this.inputData.cbResult = (data) => {
        //this.closeHandler();
        this.changeProfile(data);
      };
      errorLocation = 20;
      this.inputData.cbFault = this.inputDataFault.bind(this);
      this.inputData.encodeURL = false;
      this.actionPath = "outstandingmovement.do?";
      this.actionMethod = 'method=updateProfileAddCols';
      this.requestParams["additionalColumns"] = JSON.stringify(this.prepareGridData());// JSON.stringify(this.operationsList); 
      this.requestParams["profileId"] = profileId//this.profileCombo.selectedValue;
      this.requestParams["entityId"]= this.entityId;
      this.inputData.url = this.baseURL + this.actionPath + this.actionMethod;
      errorLocation = 120;
      this.inputData.send(this.requestParams);
    } catch (error) {
      // log the error in ERROR LOG
      this.logger.error('method [saveProfile] - error: ', error, 'errorLocation: ', errorLocation);
      SwtUtil.logError(error, SwtUtil.PREDICT_MODULE_ID, 'AdditionalColumns.ts', "saveProfile", errorLocation);
    }
}

saveNewProfile(selectedProfile){
    //Variable for errorLocation
    let errorLocation = 0;
    try {
      this.requestParams = [];
      if (!this.additionalColsList.length)
      this.additionalColsList = [this.additionalColsList];
      this.additionalColsList.push( {type: '', value: selectedProfile, selected: 0, content: selectedProfile});
      this.additionalColsList.sort((a, b) => a.content.localeCompare(b.content)); // Sort alphabetically by content
      this.profileCombo.dataProvider=this.additionalColsList;
      this.profileCombo.setComboData(this.additionalColsList);
      this.profileCombo.selectedLabel= selectedProfile;
           //update profile combo of msd option popup
     if(window.opener && window.opener.instanceElement && this.source=="msdScreen")
     window.opener.instanceElement.updateProfileCombo(this.additionalColsList, this.profileCombo.selectedLabel);
     
     if(window.opener && window.opener.instanceElement2 && this.source=="settingScreen")
     window.opener.instanceElement2.updateProfileCombo(this.additionalColsList, this.profileCombo.selectedLabel);
     
      this.revertProfileImage.enabled=false;
      this.saveProfileImage.enabled= true;
      this.deleteProfileImage.enabled= true;
      errorLocation = 100;
      this.menuAccessId = ExternalInterface.call('eval', 'menuAccessId');
      errorLocation = 110;
      if (this.menuAccessId) {
        if (this.menuAccessId !== "") {
          this.menuAccessId = Number(this.menuAccessId);
        }
      }
      errorLocation = 10;
      this.inputData.cbStart = this.startOfComms.bind(this);
      this.inputData.cbStop = this.endOfComms.bind(this);
      this.inputData.cbResult = (data) => {
       this.swtAlert.show(SwtUtil.getPredictMessage('additionalColumns.alertProfileSaved', null));
       this.changeProfile(data);
      };
      errorLocation = 20;
      this.inputData.cbFault = this.inputDataFault.bind(this);
      this.inputData.encodeURL = false;
      this.actionPath = "outstandingmovement.do?";
      this.actionMethod = 'method=saveProfileAddCols';
      this.requestParams["additionalColumns"] = JSON.stringify(this.prepareGridData());
      this.requestParams["profileId"] = this.profileCombo.selectedValue;
      this.inputData.url = this.baseURL + this.actionPath + this.actionMethod;
      errorLocation = 120;
      this.inputData.send(this.requestParams);
    } catch (error) {
      // log the error in ERROR LOG
      this.logger.error('method [saveNew] - error: ', error, 'errorLocation: ', errorLocation);
      SwtUtil.logError(error, SwtUtil.PREDICT_MODULE_ID, 'AdditionalColumns.ts', "saveNew", errorLocation);
    }
  }

  prepareGridData(){
    this.additionalColsData=[];
    if (this.columnsGrid.gridData.length > 0){
      for (let i = 0; i < this.columnsGrid.gridData.length; i++) {
      this.additionalColsData.push({
        Table: this.columnsGrid.gridData[i].table,
        Column: this.columnsGrid.gridData[i].column,
        Label: this.columnsGrid.gridData[i].label,
        Sequence: this.columnsGrid.gridData[i].sequence
      });
    }
    /*  this.xml = "<msdAdditionalColumns>" ;
      for (let i = 0; i < this.gridColumns.gridData.length; i++) {
        if(this.gridColumns.gridData[i].label){
          this.xml += "<additionalColumn>" ;
          this.xml +="<table><![CDATA["  + this.gridColumns.gridData[i].table + "]]></table>";
          this.xml +="<column><![CDATA["  + this.gridColumns.gridData[i].column + "]]></column>";
          this.xml +="<label><![CDATA["  + this.gridColumns.gridData[i].label + "]]></label>";
          this.xml += "</additionalColumn>" ;
        }
      }
      this.xml += "</msdAdditionalColumns>" ;*/
  
    }
    //this.xml = prettyData.pd.xml(this.xml);
    return this.additionalColsData;
  }

  public deleteProfileClickHandler(event): void {
    if (this.profileCombo.selectedIndex > 0) {

      this.swtAlert.show(SwtUtil.getPredictMessage('additionalColumns.alertDeleteProfile', null) + " " + (this.profileCombo.selectedLabel).replace("*", "") + "?", //text
        SwtUtil.getPredictMessage('alert_header.confirm'), Alert.OK | Alert.CANCEL,
        this, //parent
        this.deleteAlertListener.bind(this), //close handler
        null, Alert.CANCEL); //icon and default button
    }
  }
  
  private deleteAlertListener(eventObj): void {

    // Checks for Alert OK
    if (eventObj.detail == Alert.OK) {
      // Recalculate data if "OK" is clicked
      this.deleteProfile();
    }
  }

  deleteProfile(){
    this.requestParams = [];
    this.deletedProfile= this.profileCombo.selectedIndex;//{type: '', value: this.profileCombo.selectedLabel, selected: 0, content: this.profileCombo.selectedLabel};
    this.menuAccessId = ExternalInterface.call('eval', 'menuAccessId');
    if (this.menuAccessId) {
      if (this.menuAccessId !== "") {
        this.menuAccessId = Number(this.menuAccessId);
      }
    }
    this.inputData.cbStart = this.startOfComms.bind(this);
    this.inputData.cbStop = this.endOfComms.bind(this);
    this.inputData.cbResult = (event) => {
      this.updateData();
   };
    this.inputData.cbFault = this.inputDataFault.bind(this);
    this.inputData.encodeURL = false;
    this.actionPath = "outstandingmovement.do?";
    this.actionMethod = 'method=deleteProfileData';
    this.requestParams['menuAccessId'] = this.menuAccessId;
    this.requestParams['entityId'] = this.entityId;
    this.requestParams['profileId'] = this.profileCombo.selectedLabel;
    this.requestParams['savedProfileId'] = window.opener.instanceElement.savedProfileId;
    this.inputData.url = this.baseURL + this.actionPath + this.actionMethod;
    this.inputData.send(this.requestParams);
  }

  public addProfile(event) {
  this.prepareAddColsForFilter();
    

    var noneLabel: String = null;
    var lastLabel: String = null;
    var selectedItem: String = null;
    var saveProfileArrayCollection = [];
    if (this.columnsGrid.gridData.length > 0 && this.columnsGrid.gridData[0].table == ""){
      this.swtAlert.error(SwtUtil.getPredictMessage('additionalColumns.alert.emptyField', null) + " table name");
    }else if (this.columnsGrid.gridData.length > 0 && this.columnsGrid.gridData[0].column == ""){
      this.swtAlert.error(SwtUtil.getPredictMessage('additionalColumns.alert.emptyField', null)+ " column name");
    }else if (this.columnsGrid.gridData.length > 0 && this.columnsGrid.gridData[0].label == "") {
      this.swtAlert.error(SwtUtil.getPredictMessage('additionalColumns.alert.emptyLabel', null));
    } else{
    for (let i = 0; i < this.profileCombo.dataProvider.length; i++) {
      saveProfileArrayCollection.push(this.profileCombo.dataProvider[i].content.toString().replace("*", ""));
    }
    noneLabel = SwtUtil.getPredictMessage('msdAdditionalColumns.noneProfile', null);
    const index = saveProfileArrayCollection.indexOf(noneLabel);
    if (index > -1) {
      saveProfileArrayCollection.splice(index, 1);
    }
    // saveProfileArrayCollection.removeItemAt(saveProfileArrayCollection.getItemIndex(noneLabel));
    if (this.profileCombo.selectedIndex > 0)
      selectedItem = this.profileCombo.selectedItem.content.toString().replace("*", "");


    // ExternalInterface.call("populateProfileList", "saveProfileIFrame_iframe", saveProfileArrayCollection, selectedItem);


    try {
      this.saveProfilePopupWindow = SwtPopUpManager.createPopUp(this);
      this.saveProfilePopupWindow.title = "Save Profile"; // childTitle,
      this.saveProfilePopupWindow.isModal = true;
      this.saveProfilePopupWindow.width = "400";
      this.saveProfilePopupWindow.height = "130";
      this.saveProfilePopupWindow.id = "saveProfilePopupWindow";
      this.saveProfilePopupWindow.enableResize = false;
      this.saveProfilePopupWindow.showControls = true;
      this.saveProfilePopupWindow.saveProfileCollection = saveProfileArrayCollection;
      this.saveProfilePopupWindow.selectedProfileItem = selectedItem;
      this.saveProfilePopupWindow.msdAddColsCombo = this.profileCombo;
      // this.saveProfilePopupWindow['maxOrder'] = this.maxOrder;
      // this.saveProfilePopupWindow['listOrder'] = this.listOrder;
      const mLoader = new ModuleLoader(this.commonService);
      mLoader.addEventListener(ModuleEvent.READY, (event) => this.moduleReadyEventHandler(event));
      mLoader.loadModule("saveMsdProfilePopup");

    } catch (e) {
      SwtUtil.logError(e, 'Predict', 'saveProfilePopupWindow', 'saveProfile', this.errorLocation);
    }
  }
    // ExternalInterface.call('openChildWindow', 'addProfile');
  }

  private moduleReadyEventHandler(event) {
    this.saveProfilePopupWindow.addChild(event.target);
    this.saveProfilePopupWindow.display();
    this.saveProfilePopupWindow.onClose.subscribe(() => {
    }, error => {
      console.log(error);
    });
  }

  /*private saveProfileHandlerFromIFrame(selectedProfile: string): void {

  }*/



  changeProfile(event){
    //update MSD grid if we updated the last saved profile labels

    if(window.opener && window.opener.instanceElement && this.source=="settingScreen"){
    window.opener.instanceElement.updateGridData();
    }
    this.selectedProfile= this.profileCombo.selectedLabel;
    if (this.profileCombo.selectedLabel=="<None>"){
      this.saveProfileImage.enabled=true;
      this.deleteProfileImage.enabled=false;
      this.revertProfileImage.enabled=true;
    }else{
      this.saveProfileImage.enabled=true;
      this.deleteProfileImage.enabled=true;
      this.revertProfileImage.enabled=false;
    }
    this.requestParams = [];
    this.menuAccessId = ExternalInterface.call('eval', 'menuAccessId');
    if (this.menuAccessId) {
      if (this.menuAccessId !== "") {
        this.menuAccessId = Number(this.menuAccessId);
      }
    }
    this.inputData.cbStart = this.startOfComms.bind(this);
    this.inputData.cbStop = this.endOfComms.bind(this);
    this.inputData.cbResult = (event) => {
      this.inputDataResult(event);
    };
    this.inputData.cbFault = this.inputDataFault.bind(this);
    this.inputData.encodeURL = false;
    this.actionPath = "outstandingmovement.do?";
    this.actionMethod = 'method=displayColScreenData';
    this.requestParams['menuAccessId'] = this.menuAccessId;
    this.requestParams['profileId'] = (this.profileCombo.selectedLabel).indexOf("*") != -1?this.profileCombo.selectedLabel.substring(1):this.profileCombo.selectedLabel;
    this.inputData.url = this.baseURL + this.actionPath + this.actionMethod;
    this.inputData.send(this.requestParams);
  }

  private profileContentChanged(): void {
    try {

      if(this.profileCombo.dataProvider[this.profileCombo.selectedIndex].content.toString().indexOf("*") == -1) {
        this.profileCombo.dataProvider[this.profileCombo.selectedIndex].content = "*" + this.profileCombo.selectedLabel;
        this.profileCombo.selectedLabel = this.profileCombo.dataProvider[this.profileCombo.selectedIndex].content;
      }
      this.revertProfileImage.enabled = true;

      this.deleteProfileImage.enabled = false;
    } catch (error) {

    }
  }

  addColsGridChanges() {
    let row = {};
    let gridChanges = this.columnsGrid.changes.getValues();
    for (let i = 0; i < gridChanges.length; i++) {
      row = {
        'OPERATION': gridChanges[i].crud_operation.substring(0, 1), 'Table': gridChanges[i].crud_data.table, 
        'Column': gridChanges[i].crud_data.column, 'Label': gridChanges[i].crud_data.label,
        'Sequence': gridChanges[i].crud_data.sequence
      };
      this.operationsList.push(row)
    }

  }

  

    /**
     * comboBoxChangeHandler
     *
     * @param : event DataGridEvent
     *
     * This method is called when change event occurs in datagrid combo boxes and sets the related cells with
     * values from the XML
     **/
    private comboBoxChangeHandler(event): void {
        
      var errorLocation: number = 0;
      try {
          // set errorLocation    
          this.errorLocation = 10;
          var eventListData: any;
          // string to hold the data element 
          var dataElement: string;
          // number to hold the row index 
          var rowIndex: number;
          // var to hold the rowData 
          var rowData: any = null;
          // string to hold the new data 
          var newData: string = null;
          // var to hold cbo data 
          var data: string = null;
          // var to hold attribute
          var attributeValue: string = null;

          // check if the event target is ComboBoxItemRenderer
          if (event.target == "ComboBoxItemRenderer") {
              this.errorLocation = 20;
              // Get the list data from event
              //TODO             " eventListData = this.DataGridListData.listData;
              // Get the new data value
              newData = event.listData.newValue;
              // get data from item rendrerer 
              data = event.listData.newValue;
              // The data element that will be used as column key
              dataElement = event.dataField;
              // Get the rowindex 
              rowIndex = event.rowIndex;
              // Get the full row data
              rowData = event.listData.new_row;
              // set errorlocation 
              this.errorLocation = 30;
              // if the rec_unit_id cbo is changed 
              if (dataElement == "rec_unit_id") {
                  // set errorlocation
                  this.errorLocation = 40;
                  // var attValuesList = this.lastRecievedJSON.parties.grid1["selects"]["select"].find(x => (String(x.id).indexOf("rec_unit_id|1054.1") != -1)).option;
                  if (this.lastRecievedJSON.parties.grid1["selects"]["select"].find(x => (String(x.id).indexOf("rec_unit_id|1054.1") != -1)).option.length == undefined) {
                      var arr: any = new Array();
                      if (this.lastRecievedJSON.parties.grid1["selects"]["select"].find(x => (String(x.id).indexOf("rec_unit_id|1054.1") != -1)).option) {
                          arr[0] = this.lastRecievedJSON.parties.grid1["selects"]["select"].find(x => (String(x.id).indexOf("rec_unit_id|1054.1") != -1)).option;
                      }
                      this.lastRecievedJSON.parties.grid1["selects"]["select"].find(x => (String(x.id).indexOf("rec_unit_id|1054.1") != -1)).option = arr;
                  }
                  // set attribute value 
                  attributeValue = this.lastRecievedJSON.parties.grid1["selects"]["select"].find(x => (String(x.id).indexOf("rec_unit_id|1054.1") != -1)).option.find(x => x.value == data).attribute

                  this.errorLocation = 45;
                  // set the attribute of rowData 
                  rowData.attribute = (StringUtils.trim(attributeValue).length > 0) ? attributeValue : "EMPTY";
                  this.columnsGrid.updateRow(this.columnsGrid.selectedIndex, "attribute", rowData.attribute);

                  // check if the party is current entity 
                  if (String(rowData.rec_unit_id).length > 0) {
                     /* if (this.partyIsEntity) {
                          // set refOrigibn as origin 
                          rowData.origin = this.RefOrigin;
                      } else {
                          // set Nostro 
                          rowData.origin = this.lastRecievedJSON.parties.grid1["selects"]["select"].find(x => (String(x.id).indexOf("origin|1032") != -1)).option[1].value;
                      }*/
                  }
                  else {
                      rowData.origin = "";
                  }

                  this.columnsGrid.updateRow(this.columnsGrid.selectedIndex, "origin", rowData.origin);

                  // reset data if empty 
                  if (rowData.attribute == "EMPTY")
                      rowData.attribute = null;

                  if (!rowData.new_row)
                      rowData['item'] = { "content": 'Y' };

              }
              else if (dataElement == "attribute") {
                  //-Fix M4944/ISS-172 by Rihab.JB @13/07/2020 -START- 
                  let unfilteredList = [];
                  let filteredList = [];
                  this.errorLocation = 48;
                  unfilteredList = this.lastRecievedJSON.parties.grid1["selects"]["select"].find(x => (String(x.id).indexOf("rec_unit_id|1054.1") != -1)).option;
                  if (unfilteredList.length == undefined) {
                      var arr: any = new Array();
                      if (this.lastRecievedJSON.parties.grid1["selects"]["select"].find(x => (String(x.id).indexOf("rec_unit_id|1054.1") != -1)).option) {
                          arr[0] = this.lastRecievedJSON.parties.grid1["selects"]["select"].find(x => (String(x.id).indexOf("rec_unit_id|1054.1") != -1)).option;
                      }
                      unfilteredList = arr;
                  }
                  this.errorLocation = 49;
                  unfilteredList.filter(x => {
                      //-Fix M4874/Issue 51 by Rihab.JB @14/07/2020 -START- 
                      if (data == "" || x.attribute == data) {
                          filteredList.push(x);
                      }
                      //-END-
                  });
                  this.errorLocation = 491;
                  var col = this.columnsGrid.columnDefinitions.find(x => x.id == "rec_unit_id");
                  if (col) {
                      col['params']['selectDataSource'] = filteredList;
                  }
                  //-END-
                  this.errorLocation = 50;
                  // refresh dataprovider
                  // ComboBoxItemRenderer._externalAttributeFillList = "rec_unit_id";
              }
              else if (dataElement == "origin") {
                  // do nothing 
              }
          }
      }
      catch (error) {
          //Alert.show("error " + error.getStackTrace());
          //SwtUtil.logError(error, this.currActivityId, this.commonService.getQualifiedClassName(this), "comboBoxChangeHandler", this.errorLocation);
      }
  }

  prepareAddColsForFilter(){
    this.addColsForFilter=[];
    if (this.columnsGrid.gridData.length > 0){
      for (let i = 0; i < this.columnsGrid.gridData.length; i++) {
     /* this.additionalColsData.push({
        Table: this.columnsGrid.gridData[i].table,
        Column: this.columnsGrid.gridData[i].column,
        Label: this.columnsGrid.gridData[i].label,
        Sequence: this.columnsGrid.gridData[i].sequence
      });*/
      const seq = "";
      const table = "";
      const column = "";
      const colObject = Object.assign(seq+"*"+table, column);
    }
    }
    return this.additionalColsData;
  }

startOfComms(): void {
  //this.loadingImage.setVisible(true);
}

/**
 * Part of a callback function to all for control of the loading swf from the HTTPComms Object
 */
endOfComms(): void {
  //this.loadingImage.setVisible(false);
}


/**                                                                                                                  
 * If a fault occurs with the connection with the server then display the lost connection label                      
 * @param event:FaultEvent                                                                                           
 **/
private inputDataFault(event): void {
  this._invalidComms = event.fault.faultString + "\n" + event.fault.faultCode + "\n" + event.fault.faultDetail;
  this.swtAlert.show("fault " + this._invalidComms);
}


  /**
  * InputParameter()
  *
  * Method called on (+) button clicked
  */
  addColumns(): void {
  // Variable to hold error location
  var errorLocation: number = 0;
  try {
    this.win =  SwtPopUpManager.createPopUp(this, AddColsPopUp, {
      title: "Add Columns",
      selectCursor: this.jsonReader.getSelects(),
    });
    this.win.isModal = true;
    this.win.enableResize = false;
    this.win.width = '300';
    this.win.height = '180';
    this.win.showControls = true;
    this.win.id = "addCols";
    this.win.display();

  }
  catch (error) {
    // log the error in ERROR LOG
    this.logger.error('method [addColumns] - error: ', error, 'errorLocation: ', errorLocation);
    SwtUtil.logError(error, SwtUtil.PREDICT_MODULE_ID, 'AdditionalColumns.ts', "addColumns", errorLocation);
  }

}

removeColumns(){
  
}

}
//Define lazy loading routes
const routes: Routes = [
  { path: '', component: AdditionalColumns }
];
export const routing: ModuleWithProviders = RouterModule.forChild(routes);
//Define an NgModule that will be loaded by app module
@NgModule({
  imports: [routing, SwtToolBoxModule],
  declarations: [AdditionalColumns],
  entryComponents: []
})
export class AdditionalColumnsModule { }