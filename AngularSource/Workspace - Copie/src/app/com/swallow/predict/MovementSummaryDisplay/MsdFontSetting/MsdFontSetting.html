<SwtModule (creationComplete)="onLoad()" width="100%" height="100%">
  <VBox width="100%" height="100%"  paddingBottom="10" paddingTop="5" paddingLeft="10" paddingRight="10" >
    <SwtCanvas  width="100%" height="70%" paddingBottom="10" paddingRight="10" paddingTop="10" paddingLeft="5">
      <VBox width="100%">
      <HBox width="100%" height="100%" paddingBottom="10">
        <SwtLabel id="addColsLabel" #addColsLabel text="Use Additional Columns" fontWeight="bold" width="180" ></SwtLabel>
        <SwtCheckBox id="addColscheck" #addColscheck selected="false"></SwtCheckBox>
        <SwtComboBox id="addColsCombo" #addColsCombo width="200" dataLabel="listProfiles"> </SwtComboBox>
        <SwtButton #configButton id="configButton" enabled="true"
        (click)="openAdditionalColsScreen()"></SwtButton>
      </HBox>
      <HBox width="100%" height="100%">
      <SwtLabel #fonSizeLabel text="Font Size" fontWeight="bold" width="180"></SwtLabel>
      <SwtRadioButtonGroup #fontSize id="fontSize" align="horizontal">
        <SwtRadioItem   #normalFontSize groupName="fontSize" value="N" width="70" id="normal"  label="Normal" [selected]="true" toolTip="Select Normal Font Size"  ></SwtRadioItem>
        <SwtRadioItem    #smallFontSize groupName="fontSize" value="S" width="100" id="small"  label="Small" toolTip="Select Small Font Size"></SwtRadioItem>
      </SwtRadioButtonGroup>
      </HBox>
    </VBox>
    </SwtCanvas>
    <SwtCanvas width="100%"  height="30%" paddingBottom="10" paddingRight="10" paddingTop="5" paddingLeft="5">
      <HBox width="100%">
        <SwtButton id="okButton" #okButton
                   (click)="saveResult()">
        </SwtButton>
        <SwtButton #cancelButton id="cancelButton"
                   (click)="removePopUp()">
        </SwtButton>
      </HBox>
    </SwtCanvas>
  </VBox>
</SwtModule>


