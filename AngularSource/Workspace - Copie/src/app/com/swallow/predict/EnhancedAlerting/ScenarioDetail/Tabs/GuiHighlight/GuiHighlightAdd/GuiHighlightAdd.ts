import {Component, ElementRef, ViewChild, OnInit, ModuleWithProviders, NgModule} from '@angular/core';
import {
  SwtAlert,
  SwtUtil,
  SwtTextInput,
  CommonService,
  SwtModule,
  SwtLabel,
  SwtComboBox,
  SwtButton,
  SwtCheckBox,
  SwtCanvas,
  SwtText,
  SwtToolBoxModule,
  SwtCommonGrid,
  HTTPComms,
  JSONReader, ExternalInterface, SwtPopUpManager
} from 'swt-tool-box';
import {Routes, RouterModule} from "@angular/router";

import {ShowXML} from "../../ShowXML/ShowXML";
declare var require: any;
var convert = require('xml-js');
@Component({
  selector: 'app-gui-highlight-add',
  templateUrl: './GuiHighlightAdd.html',
  styleUrls: ['./GuiHighlightAdd.css']
})
export class GuiHighlightAdd extends SwtModule implements OnInit {

  @ViewChild('scenarioIdLbl') scenarioIdLbl: SwtLabel;
  @ViewChild('guiFacilityId') guiFacilityId: SwtLabel;
  @ViewChild('selectedGuiFacility') selectedGuiFacility: SwtLabel;
  @ViewChild('reqScenLbl') reqScenLbl: SwtLabel;
  @ViewChild('desLbl') desLbl: SwtLabel;
  @ViewChild('parameterIdLbl') parameterIdLbl: SwtLabel;
  @ViewChild('mapFromLbl') mapFromLbl: SwtLabel;

  @ViewChild('scenarioIdtxt') scenarioIdtxt: SwtTextInput;
  @ViewChild('mapFromTxt') mapFromTxt: SwtTextInput;

  @ViewChild('descTxt') descTxt: SwtText;
  @ViewChild('parameterIdTxt') parameterIdTxt: SwtText;

  @ViewChild('guiFacilityCombo') guiFacilityCombo: SwtComboBox;

  @ViewChild('reqScenCheck') reqScenCheck: SwtCheckBox;
  @ViewChild('subGuiCanvas') subGuiCanvas: SwtCanvas;

  @ViewChild('updateButton') updateButton: SwtButton;
  @ViewChild('saveButton') saveButton: SwtButton;
  @ViewChild('cancelButton') cancelButton: SwtButton;
  @ViewChild('showXMLButton') showXMLButton: SwtButton;

  private swtalert: SwtAlert;
  private subGuiGrid: SwtCommonGrid;
  public win : any;
  /**
  * Communication Objects
  */
  private inputData: HTTPComms = new HTTPComms(this.commonService);
  private requestParams = [];
  public baseURL = SwtUtil.getBaseURL();
  private actionPath = "";
  private actionMethod = "";
  public jsonReader: JSONReader = new JSONReader();
  public lastRecievedJSON;
  public prevRecievedJSON;
private guiFacilityArray = [];
  constructor(private commonService: CommonService, private element: ElementRef) {
    super(element, commonService);
    this.swtalert = new SwtAlert(commonService);
  }

  ngOnInit() {
    this.subGuiGrid = <SwtCommonGrid>this.subGuiCanvas.addChild(SwtCommonGrid);
    this.scenarioIdLbl.text = SwtUtil.getPredictMessage('scenario.scenarioId', null);
    this.guiFacilityId.text = SwtUtil.getPredictMessage('scenario.guiHighlight.facilityId', null);
    this.reqScenLbl.text = SwtUtil.getPredictMessage('scenario.guiHighlight.reqScenario', null);
    this.parameterIdLbl.text = SwtUtil.getPredictMessage('scenario.guiHighlight.paramId', null);
    this.desLbl.text = SwtUtil.getPredictMessage('scenario.guiHighlight.decription', null);
    this.mapFromLbl.text = SwtUtil.getPredictMessage('scenario.guiHighlight.mapFrom', null);
    this.updateButton.label = SwtUtil.getPredictMessage('button.update', null);
    this.saveButton.label = SwtUtil.getPredictMessage('button.save', null);
    this.cancelButton.label = SwtUtil.getPredictMessage('button.cancel', null);
    this.showXMLButton.label = SwtUtil.getPredictMessage('screen.showXML', null);
    this.guiFacilityCombo.required = true;
  }

  onLoad() {
    this.subGuiGrid.onRowClick =()=> {
      this.rowClick();
    };
    let paramsGuiFromParent: any;
    if (window.opener && window.opener.instanceElement) {
      paramsGuiFromParent = window.opener.instanceElement.sendDataToSub();
      this.subGuiGrid.CustomGrid(paramsGuiFromParent.gridData.metadata);
      this.scenarioIdtxt.text = paramsGuiFromParent.scenarioId;
      this.guiFacilityCombo.dataProvider = paramsGuiFromParent.guiFacilityList.option;
      this.guiFacilityArray = paramsGuiFromParent.guiFacilityArray;
      if(paramsGuiFromParent.operation != "add") {
        this.guiFacilityCombo.selectedLabel = paramsGuiFromParent.selctedFacilityId;
        this.selectedGuiFacility.text = paramsGuiFromParent.selctedFacilityDescription;
        this.xmlAsString = paramsGuiFromParent.parameterXML;
        this.showXMLButton.enabled = true;
        setTimeout(()=> {
        this.generateGridRows()
        }, 0)

      }
    }

  }
  changeGuiFacility() {
    this.selectedGuiFacility.text = this.guiFacilityCombo.selectedValue;
    this.subGuiGrid.gridData = {row: [], size: 0};
    if(this.guiFacilityCombo.selectedLabel != "") {
      this.showXMLButton.enabled = true;
      this.requestParams = [];
      this.actionPath = 'scenMaintenance.do?';
      this.actionMethod = 'method=getGuiFacilityData';
      this.inputData.cbResult = (data) => {
        this.inputDataResult(data);
      };
      this.requestParams["selectedGuiFacility"] = this.guiFacilityCombo.selectedLabel;
      this.inputData.cbFault = this.inputDataFault.bind(this);
      this.inputData.encodeURL = false;
      this.inputData.url = this.baseURL + this.actionPath + this.actionMethod;
      this.inputData.send(this.requestParams);
    } else {
      this.showXMLButton.enabled = false;
    }

  }
  rowClick() {
    if(this.subGuiGrid.selectedIndex >=0) {
      this.parameterIdTxt.text = this.subGuiGrid.selectedItem.subGuiId.content;
      this.descTxt.text = this.subGuiGrid.selectedItem.subGuiDescription.content;
      this.mapFromTxt.text = this.subGuiGrid.selectedItem.subGuiMapFrom.content;
      this.updateButton.enabled = true;
      this.mapFromTxt.enabled = true;
    } else {
      this.parameterIdTxt.text = "";
      this.descTxt.text = "";
      this.mapFromTxt.text = "";
      this.updateButton.enabled = false;
      this.mapFromTxt.enabled = false;
    }
  }

  inputDataResult(event) {
    if (this.inputData.isBusy()) {
      this.inputData.cbStop();
    } else {

      this.lastRecievedJSON = event;
      this.jsonReader.setInputJSON(this.lastRecievedJSON);
      this.xmlAsString = this.jsonReader.getSingletons().parameterXML;
      this.generateGridRows();
      this.reqScenCheck.selected = this.jsonReader.getSingletons().requireScInstance == "Y";


    }
  }
  generateGridRows() {
    let options = {
      object: false,
      reversible: false,
      coerce: false,
      sanitize: true,
      trim: true,
      arrayNotation: false,
      alternateTextNode: false,
      compact: true,
      emptyTag: '{}'
    };
    let jsonData = convert.xml2js(this.xmlAsString, options);
    let data = jsonData.requiredParameters.parameter;
    for (let i = 0; i < data.length; i++) {
      let item: any;
      item = {
        'subGuiReq': {'content': data[i].isMandatory._text},
        'subGuiId': {'content': data[i].name._text},
        'subGuiDescription': {'content': data[i].description._text},
        'subGuiType': {'content': data[i].dataType._text},
        'subGuiMapFrom': {'content': (data[i].mapFrom ? data[i].mapFrom._text : "")}
      };
      this.subGuiGrid.appendRow(item, true);

    }
  }
  inputDataFault() {
    this.swtalert.error(SwtUtil.getPredictMessage('alert.generic_exception'));
  }
  public xmlDocument: any;
  public xmlAsString: string = "";
  updateHandle() {
   this.subGuiGrid.dataProvider[this.subGuiGrid.selectedIndex].subGuiMapFrom = this.mapFromTxt.text;
   this.subGuiGrid.dataProvider[this.subGuiGrid.selectedIndex].slickgrid_rowcontent.subGuiMapFrom.content = this.mapFromTxt.text;
    this.subGuiGrid.refresh();
   //add map node to xml
    let parser = new DOMParser();
    this.xmlDocument = parser.parseFromString(this.xmlAsString, 'text/xml');
    let newEle = this.xmlDocument.createElement("mapFrom");
    let x = this.xmlDocument.getElementsByTagName("parameter")[this.subGuiGrid.selectedIndex];
    x.appendChild(newEle);
    x.getElementsByTagName('mapFrom')[0].textContent = this.mapFromTxt.text;
    if(x.getElementsByTagName('mapFrom')[1] != undefined) {
      x.removeChild(x.getElementsByTagName('mapFrom')[1])
    }
    const serializer = new XMLSerializer();
    this.xmlAsString = serializer.serializeToString(this.xmlDocument);
  }
  saveHandler() {
    if(this.guiFacilityCombo.selectedLabel =="") {
      this.swtalert.warning('Please select GUI facility')
    }
   else if(this.guiFacilityArray.indexOf(this.guiFacilityCombo.selectedLabel) != -1) {
      this.swtalert.warning("Gui facility already exists")
    } else {
      if (window.opener && window.opener.instanceElement) {
        window.opener.instanceElement.refreshParent(this.guiFacilityCombo.selectedLabel + '-' + this.guiFacilityCombo.selectedValue, this.xmlAsString);
        ExternalInterface.call('close');
      }
    }
  }
  cancelHandler() {
ExternalInterface.call('close');
  }
  showXmlHandler() {
    this.win =  SwtPopUpManager.createPopUp(this, ShowXML, {
      title: "Show XML",
      xmlData: this.xmlAsString
    });
    this.win.isModal = true;
    this.win.enableResize = false;
    this.win.width = '400';
    this.win.height = '500';
    this.win.showControls = true;
    this.win.id = "guiShowXML";
    this.win.display();
//open pop up this.jsonReader.getSingletons().parameterXML
  }

}
//Define lazy loading routes
const routes: Routes = [
  { path: '', component: GuiHighlightAdd }
];
export const routing: ModuleWithProviders = RouterModule.forChild(routes);
//Define an NgModule that will be loaded by app module
@NgModule({
  imports: [routing, SwtToolBoxModule],
  declarations: [GuiHighlightAdd],
  entryComponents: []
})
export class GuiHighlightAddModule {}
