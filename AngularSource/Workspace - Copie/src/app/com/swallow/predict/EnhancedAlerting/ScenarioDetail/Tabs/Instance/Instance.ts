import {Component, ElementRef, ViewChild, OnInit} from '@angular/core';
import {
  SwtAlert,
  SwtUtil,
  CommonService,
  SwtModule,
  SwtLabel, SwtNumericInput, SwtCheckBox, SwtRadioButtonGroup, SwtRadioItem, SwtTextArea, SwtComboBox, SwtMultiselectCombobox, SwtImage
} from 'swt-tool-box';
declare var instanceElement: any;
@Component({
  selector: 'app-instance',
  templateUrl: './Instance.html',
  styleUrls: ['./Instance.css']
})
export class Instance extends SwtModule implements OnInit {

  @ViewChild('uniqExpLbl') uniqExpLbl: SwtLabel;
  @ViewChild('acctIdLbl') acctIdLbl: SwtLabel;
  @ViewChild('signColLbl') signColLbl: SwtLabel;
  @ViewChild('mvtColLbl') mvtColLbl: SwtLabel;
  @ViewChild('matchColLbl') matchColLbl: SwtLabel;
  @ViewChild('sweepColLbl') sweepColLbl: SwtLabel;
  @ViewChild('payColLbl') payColLbl: SwtLabel;
  @ViewChild('treeBreakDown1Lbl') treeBreakDown1Lbl: SwtLabel;
  @ViewChild('treeBreakDown2Lbl') treeBreakDown2Lbl: SwtLabel;
  @ViewChild('alertInstanceColumnLbl') alertInstanceColumnLbl: SwtLabel;
  @ViewChild('valueDateLbl') valueDateLbl: SwtLabel;
  @ViewChild('otherIdColLbl') otherIdColLbl: SwtLabel;
  @ViewChild('otherIdTypeLbl') otherIdTypeLbl: SwtLabel; 
  @ViewChild('otherIdTypeDesc') otherIdTypeDesc: SwtLabel;

  @ViewChild('instExpLbl') instExpLbl: SwtLabel;
  @ViewChild('raiseLbl') raiseLbl: SwtLabel;
  @ViewChild('minLbl') minLbl: SwtLabel;


  @ViewChild('acctIdCombo') acctIdCombo: SwtComboBox;
  @ViewChild('signColCombo') signColCombo: SwtComboBox;
  @ViewChild('mvtColCombo') mvtColCombo: SwtComboBox;
  @ViewChild('matchColCombo') matchColCombo: SwtComboBox;
  @ViewChild('sweepColCombo') sweepColCombo: SwtComboBox;
  @ViewChild('payColCombo') payColCombo: SwtComboBox;
  @ViewChild('treeBreakDown1Combo') treeBreakDown1Combo: SwtComboBox;
  @ViewChild('treeBreakDown2Combo') treeBreakDown2Combo: SwtComboBox;
  @ViewChild('valueDateCombo') valueDateCombo: SwtComboBox;
  @ViewChild('otherIdColCombo') otherIdColCombo: SwtComboBox;
  @ViewChild('otherIdTypeCombo') otherIdTypeCombo: SwtComboBox;
  
  @ViewChild('alertInstanceColumnCombo') alertInstanceColumnCombo: SwtMultiselectCombobox;

  @ViewChild('instExpTxt') instExpTxt: SwtNumericInput;
  @ViewChild('afterMinTxt') afterMinTxt: SwtNumericInput;

  @ViewChild('uniqueExpression') uniqueExpression: SwtTextArea;

  @ViewChild('raiseRadioGroup') raiseRadioGroup: SwtRadioButtonGroup;
  @ViewChild('radioNo') radioNo: SwtRadioItem;
  @ViewChild('radioAfter') radioAfter: SwtRadioItem;

  
  @ViewChild('uniqExpLockImg') uniqExpLockImg: SwtImage;
  @ViewChild('acctLockImg') acctLockImg: SwtImage;
  @ViewChild('valDateLockImg') valDateLockImg: SwtImage;
  @ViewChild('signLockImg') signLockImg: SwtImage;
  @ViewChild('mvtLockImg') mvtLockImg: SwtImage;
  @ViewChild('matchLockImg') matchLockImg: SwtImage;
  @ViewChild('sweepLockImg') sweepLockImg: SwtImage;
  @ViewChild('payLockImg') payLockImg: SwtImage;
  @ViewChild('otherIdLockImg') otherIdLockImg: SwtImage;
  @ViewChild('otherIdTypeLockImg') otherIdTypeLockImg: SwtImage;
  
  private swtalert: SwtAlert;
  public otherIdOldVal;
  public selectedTreeBreakdown1="";
  public selectedTreeBreakdown2="";
  private baseURL = SwtUtil.getBaseURL();

  constructor(private commonService: CommonService, private element: ElementRef) {
    super(element, commonService);
    this.swtalert = new SwtAlert(commonService);
  }

  public static ngOnDestroy(): any {
    instanceElement = null;
  }

  ngOnInit() {
    instanceElement = this;
    this.instExpTxt.text = 60;
    this.afterMinTxt.text = 0;
    this.uniqExpLbl.text = SwtUtil.getPredictMessage('scenario.uniqueExpression', null) + "*";
    this.acctIdLbl.text = SwtUtil.getPredictMessage('scenario.accountIdColumn', null);
    this.signColLbl.text = SwtUtil.getPredictMessage('scenario.signColumn', null);
    this.mvtColLbl.text = SwtUtil.getPredictMessage('scenario.mvtIdColumn', null);
    this.matchColLbl.text = SwtUtil.getPredictMessage('scenario.matchColumn', null);
    this.sweepColLbl.text = SwtUtil.getPredictMessage('scenario.sweepIdColumn', null);
    this.payColLbl.text = SwtUtil.getPredictMessage('scenario.payIdColumn', null);
    this.instExpLbl.text = SwtUtil.getPredictMessage('scenario.instanceExpiry', null);
    this.raiseLbl.text = SwtUtil.getPredictMessage('scenario.reRaiseAfter', null);
    this.radioNo.label = SwtUtil.getPredictMessage('scenario.no', null);
    this.radioAfter.label = SwtUtil.getPredictMessage('scenario.afterInterval', null);
    this.minLbl.text = SwtUtil.getPredictMessage("scenario.minAfter", null);
    this.treeBreakDown1Lbl.text= SwtUtil.getPredictMessage("scenario.treeBreakDown1", null);
    this.treeBreakDown2Lbl.text= SwtUtil.getPredictMessage("scenario.treeBreakDown2", null);
    this.valueDateLbl.text= SwtUtil.getPredictMessage("scenario.valueDateColumn", null);
    this.otherIdColLbl.text= SwtUtil.getPredictMessage("scenario.otherIdColumn", null);
    this.otherIdTypeLbl.text= SwtUtil.getPredictMessage("scenario.otherIdType", null);
    this.alertInstanceColumnLbl.text = SwtUtil.getPredictMessage("scenarioAdvanced.alertInstanceColumns", null);
    
    //tooltip part
    this.uniqueExpression.toolTip = SwtUtil.getPredictMessage("scenario.tooltip.uniqueExp", null);
    this.acctIdCombo.toolTip = SwtUtil.getPredictMessage("scenario.tooltip.acctIdCombo", null);
    this.valueDateCombo.toolTip = SwtUtil.getPredictMessage("scenario.tooltip.valueDateCombo", null);
    this.signColCombo.toolTip = SwtUtil.getPredictMessage("scenario.tooltip.signColCombo", null);
    this.mvtColCombo.toolTip = SwtUtil.getPredictMessage("scenario.tooltip.mvtColCombo", null);
    this.matchColCombo.toolTip = SwtUtil.getPredictMessage("scenario.tooltip.matchColCombo", null);
    this.sweepColCombo.toolTip = SwtUtil.getPredictMessage("scenario.tooltip.sweepColCombo", null);
    this.payColCombo.toolTip = SwtUtil.getPredictMessage("scenario.tooltip.payColCombo", null);
    this.otherIdColCombo.toolTip = SwtUtil.getPredictMessage("scenario.tooltip.otherIdColCombo", null); 
    this.otherIdTypeCombo.toolTip = SwtUtil.getPredictMessage("scenario.tooltip.otherIdTypeCombo", null);
    this.treeBreakDown1Combo.toolTip = SwtUtil.getPredictMessage("scenario.tooltip.treeBreakDown1Combo", null);
    this.treeBreakDown2Combo.toolTip = SwtUtil.getPredictMessage("scenario.tooltip.treeBreakDown2Combo", null);
    this.instExpTxt.toolTip = SwtUtil.getPredictMessage("scenario.tooltip.instExpTxt", null);
    this.radioNo.toolTip = SwtUtil.getPredictMessage("scenario.tooltip.radioNo", null);
    this.radioAfter.toolTip = SwtUtil.getPredictMessage("scenario.tooltip.radioAfter", null);
    this.afterMinTxt.toolTip = SwtUtil.getPredictMessage("scenario.tooltip.afterMinTxt", null);
    this.alertInstanceColumnCombo.toolTip = SwtUtil.getPredictMessage("tooltip.scenarioAlertInstanceColumn", null);
    this.alertInstanceColumnCombo.placeholder="Select columns";
    /**MultiSelectCombo item limit */
    this.alertInstanceColumnCombo.itemLimit=5;
    //make alertInstanceColumnCombo disabled
    this.alertInstanceColumnCombo.isDropdownDisabled=true;
    this.uniqueExpression.required=true;
  }
  onLoad() {
    this.alertInstanceColumnCombo.DESELECT_ALL.subscribe((target) => {
      this.alertInstanceColumnCombo.toolTip = SwtUtil.getPredictMessage("tooltip.scenarioAlertInstanceColumn", null);
    });

    this.alertInstanceColumnCombo.ITEM_DESELECT.subscribe((target) => {
      if(!target.tooltip.toString())
      this.alertInstanceColumnCombo.toolTip = SwtUtil.getPredictMessage("tooltip.scenarioAlertInstanceColumn", null);
    });
  }
  requiredField() {
    this.afterMinTxt.required = (this.raiseRadioGroup.selectedValue == "A" && this.afterMinTxt.text == "")
    this.afterMinTxt.enabled=this.raiseRadioGroup.selectedValue == "A"?true:false;

  }

  changeScenarioCheck(){
    this.parentDocument.refreshGridGuiHighlight();
  }


  changeComboBox(comboCol:string, column, combo){
    //in case of other ID type update other ID type description
    if(comboCol=='otherIdType'){
    this.otherIdTypeDesc.text=this.otherIdTypeCombo.selectedValue;
    }else{
    this.parentDocument.doRefreshGuiHighlight(comboCol);
    /*if (comboCol=="otherIdCol"){
    column=this.otherIdColCombo.selectedLabel; 
    }else{*/
    column= '"' + column + '"'
   // }
    //update treeBreakDown combo dataprovider
    if(combo.selectedLabel){
    this.otherIdOldVal=this.otherIdColCombo.selectedLabel;
    this.parentDocument.updateTreeProvider(column, comboCol);
    this.parentDocument.updateAlertInstProvider(column);
  }else{
    this.parentDocument.deleteColFromTreeProvider(column, comboCol); 
    this.parentDocument.deleteColFromAlertInstProvider(column, comboCol); 
 
    }
    this.parentDocument.refreshGridGuiHighlight();
  }
  }


  addExpression(){
    let expression = (this.uniqueExpression)? this.uniqueExpression.text:"";
    let variable = null;

    variable = this.acctIdLbl.text.split(" ");
    if(expression.indexOf(expression) != -1){
      this.acctIdCombo.selectedLabel = '"'+expression+'"';
      this.matchColCombo.selectedLabel ="";
      this.signColCombo.selectedLabel = "";
      this.mvtColCombo.selectedLabel ="";
      this.sweepColCombo.selectedLabel = "";
      this.payColCombo.selectedLabel="";
    }

    variable = this.matchColLbl.text.split(" ");
    if(expression.indexOf(variable[0]) != -1){
      this.acctIdCombo.selectedLabel = "";
      this.matchColCombo.selectedLabel =  '"'+expression+'"';
      this.signColCombo.selectedLabel = "";
      this.mvtColCombo.selectedLabel ="";
      this.sweepColCombo.selectedLabel = "";
      this.payColCombo.selectedLabel ="";
    }


    variable = this.signColLbl.text.split(" ");
    if(expression.indexOf(variable[0]) != -1){
      this.acctIdCombo.selectedLabel = "";
      this.matchColCombo.selectedLabel ="";
      this.signColCombo.selectedLabel = '"'+expression+'"';
      this.mvtColCombo.selectedLabel ="";
      this.sweepColCombo.selectedLabel = "";
      this.payColCombo.selectedLabel ="";
    }


    variable = this.mvtColLbl.text.split(" ");
    if(expression.indexOf(variable[0]) != -1){
      this.acctIdCombo.selectedLabel = "";
      this.matchColCombo.selectedLabel ="";
      this.signColCombo.selectedLabel ="";
      this.mvtColCombo.selectedLabel ='"'+expression+'"';
      this.sweepColCombo.selectedLabel = "";
      this.payColCombo.selectedLabel ="";
    }

    variable = this.sweepColLbl.text.split(" ");
    if(expression.indexOf(variable[0]) != -1){
      this.acctIdCombo.selectedLabel = "";
      this.matchColCombo.selectedLabel ="";
      this.signColCombo.selectedLabel ="";
      this.mvtColCombo.selectedLabel ="";
      this.sweepColCombo.selectedLabel = '"'+expression+'"';
      this.payColCombo.selectedLabel ="";
    }


    variable = this.payColLbl.text.split(" ");
    if(expression.indexOf(variable[0]) != -1){
      this.acctIdCombo.selectedLabel = "";
      this.matchColCombo.selectedLabel ="";
      this.signColCombo.selectedLabel ="";
      this.mvtColCombo.selectedLabel ="";
      this.sweepColCombo.selectedLabel = "";
      this.payColCombo.selectedLabel =  '"'+expression+'"';
    }
  }


  saveComboVal(field) {
    if (field == 'treeBreakDown1Combo') {
      this.selectedTreeBreakdown1 = this.treeBreakDown1Combo.selectedLabel;

    } else {
      this.selectedTreeBreakdown2 = this.treeBreakDown2Combo.selectedLabel;

    }
  }

  changeTxtAreaStatus(){
    if(this.uniqExpLockImg.source==this.baseURL + this.parentDocument.lockIcon){
    this.uniqueExpression.editable=true;
    this.uniqExpLockImg.source =this.baseURL + this.parentDocument.unlockIcon;
    }else{
    this.uniqueExpression.editable=false;
    this.uniqExpLockImg.source =this.baseURL + this.parentDocument.lockIcon; 
    }
  }

  changeComboStatus(combo, comboImage){
    if(comboImage.source==this.baseURL + this.parentDocument.lockIcon){
    combo.enabled=true;
    comboImage.source =this.baseURL + this.parentDocument.unlockIcon;
    }else{
    combo.enabled=false;
    comboImage.source =this.baseURL + this.parentDocument.lockIcon; 
    }
  }


}
