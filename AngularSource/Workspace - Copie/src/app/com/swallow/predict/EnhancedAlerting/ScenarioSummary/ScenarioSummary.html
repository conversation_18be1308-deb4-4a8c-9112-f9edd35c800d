<SwtModule (creationComplete)='onLoad()' width="100%"  height="100%">
  <VBox  width="100%" height="100%" paddingBottom="5" paddingTop="5" paddingLeft="5" paddingRight="5" verticalGap="0">
    <SwtCanvas width="100%" height="13%">
      <VBox width="100%" height="100%">
        <HBox width="100%" height="100%">
          <VBox width="50%" height="100%">
            <HBox width="100%" height="80%">
              <SwtLabel id="lblCombo"
                      #lblCombo
                      fontWeight="bold"  >
            </SwtLabel>
              <SwtComboBox  #entityCombo
                          id="entityCombo"
                          dataLabel="entity"
                          (change)="entityComboChange($event)">
            </SwtComboBox>
              <SwtLabel id="selectedEntity" #selectedEntity fontWeight="normal"
                      paddingLeft="10"
                      text=""
                      textAlign="left">
            </SwtLabel>
            </HBox>
            <HBox width="100%" height="25%">
                <SwtTabNavigator #tabCategoryList id="tabCategoryList"  width="100%" height="100%"  (onChange)="updateData('tree')" borderBottom="false"> </SwtTabNavigator>
            </HBox>
          </VBox>
          <VBox  width="50%" height="100%"  verticalGap="0">

            <HBox width="100%" height="30%" horizontalAlign="right">
              <SwtLabel width="200" fontWeight="bold" #currLabel></SwtLabel>
              <SwtCheckBox  #currBox  id="currBox" selected="false" value ="N"
                            (change)="updateData('scenario')"></SwtCheckBox>
            </HBox>
            <HBox width="100%" height="30%" horizontalAlign="right">
              <SwtLabel width="200" #zeroLabel  fontWeight="bold"></SwtLabel>
              <SwtCheckBox   #zeroBox  id="zeroBox" selected="false"   value ="N"
                             (change)="updateData('scenario')">
              </SwtCheckBox>
            </HBox>
            <HBox width="100%" height="30%" horizontalAlign="right">
              <SwtLabel width="200" #alertLabel  fontWeight="bold"></SwtLabel>
              <SwtCheckBox #alertBox  id="alertBox" selected="false" value ="N"
                           (change)="updateData('scenario')">
              </SwtCheckBox>
            </HBox>
          </VBox>
        </HBox>

      </VBox>
    </SwtCanvas>
    <SwtCanvas  #mainGroup  width="100%" height="75%">
      <SwtSummary id="summary" #summary IDField="id" width="100%"  height="100%">
      </SwtSummary>
    </SwtCanvas>
    <SwtCanvas width="100%"  height="7%">
      <HBox id="controlContainer" width="100%" height="100%">
        <HBox paddingLeft="8" width="50%">
          <SwtButton #refreshButton id="refreshButton" (click)="updateData('tree')">
          </SwtButton>
          <SwtButton #closeButton id="closeButton" (click)="closeHandler($event)">
          </SwtButton>
        </HBox>
        <HBox width="50%" horizontalAlign="right">
          <SwtText
            visible="false"
            text="CONNECTION ERROR"
            styleName="redText"
            #lostConnectionText
            id="lostConnectionText"
            right="45"
            height="16">
          </SwtText>
          <SwtText #lastRefTimeLabel
                   id="lastRefTimeLabel"
                   fontWeight="normal"
                   right="65"
                   height="16">
          </SwtText>
          <SwtText #lastRefTime
                   id="lastRefTime"
                   visible="false"
                   width="90"
                   right="65"
                   height="16" >
          </SwtText>
          <SwtLoadingImage #loadingImage></SwtLoadingImage>
        </HBox>
      </HBox>
    </SwtCanvas>
  </VBox>
</SwtModule>
