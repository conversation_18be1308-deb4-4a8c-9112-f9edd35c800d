<SwtModule (creationComplete)="onLoad()"  width="100%" height="100%">
  <VBox width="100%" height="100%" paddingTop="5" paddingLeft="5" paddingBottom="5" paddingRight="5">
    <SwtCanvas width="100%" height="90%">
      <VBox width="100%" height="100%">
    <Grid width="100%" height="20%" paddingTop="10">
      <GridRow paddingLeft="10">
        <GridItem width="120">
          <SwtLabel #scenarioIdLbl></SwtLabel>
        </GridItem>
        <GridItem>
          <SwtTextInput #scenarioIdtxt width="200" enabled="false"></SwtTextInput>
        </GridItem>
      </GridRow>
      <GridRow paddingLeft="10">
        <GridItem width="120">
          <SwtLabel #runAt></SwtLabel>
        </GridItem>
        <GridItem width="90">
          <SwtTextInput #runAtTxt width="80" maxChars="5" (focusOut)="validateTime(runAtTxt)" ></SwtTextInput>
        </GridItem>
        <GridItem>
          <SwtLabel #runAtDesc fontWeight="normal"></SwtLabel>
        </GridItem>
      </GridRow>
    </Grid>
    <SwtCanvas #scheduleCanvas id="scheduleCanvas" width="100%" height="75%"></SwtCanvas>
      </VBox>

    </SwtCanvas>
    <SwtCanvas width="100%" height="10%" paddingTop="8">
      <HBox width="100%" horizontalGap="5" paddingLeft="10">
        <SwtButton #okButton enabled="true" (click)="prepareData()"></SwtButton>
        <SwtButton #cancelButton enabled="true" (click)="cancelHandler()"></SwtButton>
      </HBox>
    </SwtCanvas>
  </VBox>
</SwtModule>
