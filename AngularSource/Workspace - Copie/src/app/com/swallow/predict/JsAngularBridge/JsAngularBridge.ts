import { Component, OnInit, ModuleWithProviders, NgModule, ElementRef } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';
import { SwtToolBoxModule, SwtModule, CommonService, SwtAlert, Encryptor, ExternalInterface, Timer, HTTPComms, SwtUtil } from 'swt-tool-box';
declare var instanceElement: any;
declare var require: any;
const CryptoJS = require("crypto-js");
import * as workerTimers from 'worker-timers';

// import * as CryptoJS from 'crypto-js';
@Component({
  selector: 'js-angular-bridge',
  templateUrl: './JsAngularBridge.html',
  styleUrls: ['./JsAngularBridge.css']
})
export class JsAngularBridge extends SwtModule implements OnInit {
  private swtAlert: SwtAlert;
  private autoRefresh: Timer;
  public inputData = new HTTPComms(this.commonService);
  public baseURL = SwtUtil.getBaseURL();
  private actionMethod: string = "";
  private actionPath: string = "";
  private requestParams = [];
  private intervalId = null;
  constructor(private commonService: CommonService, private element: ElementRef) {
    super(element, commonService);
    this.swtAlert = new SwtAlert(commonService);
  }

  public static ngOnDestroy(): any {
    instanceElement = null;
  }
  ngOnInit(): void {
    instanceElement = this;
    var isMainScreen = ExternalInterface.call('eval', 'isMainScreen');
    if (isMainScreen) {
      /*Set event listener to dataRefresh*/
      //Commented to use web workers instead of timers , as web workers will work even if the main screen page is not focused 
      // Mantis 5150: Users are logged off suddenly
      /* this.autoRefresh = new Timer(20000);
       this.autoRefresh.addEventListener("timer", this.dataRefresh.bind(this));
       this.autoRefresh.start(); */
      this.intervalId = workerTimers.setInterval(() => {
        // do something many times
        this.dataRefresh(null);
      }, 20000);

    }

  }



  /**
			 * This function is called when clicking refresh button
			 * @param event
			 **/
  private dataRefresh(evt): void {

    this.actionPath = "sessionValidation.do?";
    // Framing final url
    this.inputData.url = this.baseURL + this.actionPath + this.actionMethod;
    // Sending request

    this.inputData.send(this.requestParams);
  }

  /**
        * Encode in base 64 with custom changes
        * */
  public encode64(text: string): string {
    return Encryptor.encode64(text);
  }

  /**
   * Decode in base 64 with custom changes
   * */
  public decode64(text: string): string {
    return Encryptor.decode64(text);
  }


  eraseCookie(name) {
    document.cookie = name + '=; Max-Age=0'
}
  

setCookie(cname, cvalue, exdays) {
	  var d = new Date();
	  d.setTime(d.getTime() + (exdays*24*60*60*1000));
	  var expires = "expires="+ d.toUTCString();
	   document.cookie = cname + "=" + cvalue + ";" + expires + ";path=/swallowtech";
	} 
	

  prelogin(userId){
    //this.setCookie("{dXNl-cm5h-bWU}", this.encode64(userId), 365);
  }


  /**
     * AES CBC mode encryption: Returned result is in hex format
     * <AUTHOR>
     * */
  encrypt(jsessionid: string, userId: string, clear: string) {
    let keySession: string = jsessionid.substring(0, jsessionid.length > 12 ? 12 : jsessionid.length)
    keySession += userId.substring(0, userId.length > 4 ? 4 : userId.length);
    if(keySession.length<16){
      keySession= keySession.padEnd(16,'-');
    }

    const key = CryptoJS.enc.Utf8.parse(keySession);
    const iv = CryptoJS.enc.Utf8.parse(keySession);
    const encrypted = CryptoJS.AES.encrypt(clear, key, {
      keySize: 16,
      iv: iv,
      mode: CryptoJS.mode.CBC,
      padding: CryptoJS.pad.Pkcs7
    });
    return encrypted;
  }

}


const routes: Routes = [
  { path: '', component: JsAngularBridge }
];
export const routing: ModuleWithProviders = RouterModule.forChild(routes);
//Define an NgModule that will be loaded by app module
@NgModule({
  imports: [routing, SwtToolBoxModule],
  declarations: [JsAngularBridge],
  entryComponents: []
})
export class JsAngularBridgeModule { }