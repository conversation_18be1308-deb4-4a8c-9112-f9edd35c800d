import {CommonService, ExternalInterface, SwtButton, SwtLabel, SwtModule, SwtRadioButtonGroup, SwtRadioItem, SwtTextInput, SwtUtil} from "swt-tool-box";
import {Component, ElementRef, OnInit, ViewChild} from "@angular/core";

@Component({
    selector: 'app-workflow-options-pop-up',
    templateUrl: './WorkFlowOptionsPopUp.html',
    styleUrls: ['./WorkFlowOptionsPopUp.css']
})

export class WorkFlowOptionsPopUp extends SwtModule implements OnInit {

    @ViewChild("okButton")  okButton: SwtButton;
    @ViewChild("cancelButton")  cancelButton: SwtButton;
    @ViewChild('enabledOption')  enabledOption: SwtRadioItem;
    @ViewChild('disabledOption')  disabledOption: SwtRadioItem;
    @ViewChild('workflowOptions')  workflowOptions: SwtRadioButtonGroup;
    @ViewChild('labelOption')  labelOption: SwtLabel;


    
    public lastValue: string;

    ngOnInit(){
      this.enabledOption.label = SwtUtil.getPredictMessage('workflowmonitor.option.applied', null);
      this.enabledOption.toolTip = SwtUtil.getPredictMessage('workflowmonitor.option.applied', null);
      this.disabledOption.label = SwtUtil.getPredictMessage('workflowmonitor.option.notApplied', null);
      this.disabledOption.toolTip = SwtUtil.getPredictMessage('workflowmonitor.option.notApplied=', null);
      this.labelOption.text=SwtUtil.getPredictMessage('workflowmonitor.option.optionLabel', null);
      setTimeout(() => {
        this.workflowOptions.selectedValue = this.lastValue;
      }, 0);
    }
    constructor(private element: ElementRef, private commonService: CommonService) {
        super(element, commonService);
    }

  onLoad() {
    // this.workflowOptions.selectedValue = this.lastValue;
  }

  closePopup() {
      try {
        if(this.titleWindow) {
          this.close();
        } else {
          window.close();
        }
      } catch (error) {
        console.log(error, "OptionPopUp", "closePopup");
      }
    }

    okHandler() {
        try {
          this.parentDocument.saveApplyCcyThreshold(this.workflowOptions.selectedValue);
          this.closePopup();
        } catch(error) {
            console.error('error :',error);
        }
    }
}
