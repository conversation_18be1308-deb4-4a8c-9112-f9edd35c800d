  <SwtModule (creationComplete)="onLoad()" width="100%" height="100%">
    <VBox paddingLeft="5" paddingRight="5" paddingBottom="5" paddingTop="5" height="100%" minWidth="900" width="100%" verticalGap="6">
      <SwtCanvas #dataInputContainer id="dataInputContainer" width="100%" height="5%" styleName="filterAndCntrlContainer">
        <HBox width="100%" height="100%">
          <SwtLabel #lblDate id="lblDate" styleName="labelBold">
          </SwtLabel>
          <SwtDateField
            #startDate
            id="startDate"
            tabIndex="1"
            editable="true"
            restrict="0-9/"
            width="70"
            (change)="updateFromDate()"
            (focusOut)="validateStartDate($event)">
          </SwtDateField>
        </HBox>
      </SwtCanvas>
      <VBox height="88%" width="100%">
        <VDividedBox height="100%" width="100%">
          <SwtCanvas width="100%" height="50%" #dataGridContainer id="dataGridContainer" class="top" styleName="imdatagridContainer">
          </SwtCanvas>
          <SwtCanvas width="100%" height="50%" #storProcContainer id="storProcContainer" class="bottom" styleName="imdatagridContainer"></SwtCanvas>
        </VDividedBox>
      </VBox>
      <SwtCanvas width="100%"  height="5%"  #buttonContainer id="buttonContainer" styleName="filterAndCntrlContainer">
        <HBox width="100%" height="100%">
          <HBox width="50%" height="100%">
            <SwtButton #refreshButton
              id="refreshButton"
              enabled="true"
              buttonMode="true"
              (click)="updateData('yes')">
            </SwtButton>
            <SwtButton #rateButton
                       id="rateButton"
                       enabled="true"
                       buttonMode="true"
                       (click)="rateHandler()">
            </SwtButton>
            <SwtButton #closeButton
                       id="closeButton"
                       enabled="true"
                       buttonMode="true"
                       (click)="closeHandle()"></SwtButton>
          </HBox>
          <HBox width="50%" verticalAlign="middle">
            <HBox width="85%" >
              <SwtText visible="false" text="DATA BUILD IN PROGRESS" styleName="redText" #dataBuildingText
              id="dataBuildingText"
                     right="45"
                     height="16">
            </SwtText>
              <SwtText
              visible="false"
              text="CONNECTION ERROR"
              styleName="redText"
              #lostConnectionText
              id="lostConnectionText"
              right="45"
              height="16">
            </SwtText>
              <SwtText #lastRefText
                     id="lastRefText"
                     fontWeight="bold"
                     right="65"
                     height="16">
            </SwtText>
              <SwtText #lastRefTime
                     id="lastRefTime"
                     width="120" right="65"
                     height="16" ></SwtText>
            </HBox>
            <HBox horizontalAlign="right" width="15%">
              <DataExport  #exportContainer
                           id="exportContainer">
              </DataExport>
              <SwtHelpButton id="helpIcon"
                             [buttonMode]="true"
                             enabled="true"
                             (click)="doHelp()">
              </SwtHelpButton>
              <SwtLoadingImage #loadingImage></SwtLoadingImage>
            </HBox>

          </HBox>
        </HBox>
      </SwtCanvas>
    </VBox>
  </SwtModule>
