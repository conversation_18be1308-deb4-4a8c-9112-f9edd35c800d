.monaco-editor .margin-view-overlays .cldr{
    width: 0px !important;
  }

  .row{
    height: 100% !important;
  }

.col-md-12 {
    height: 100% !important;
}
.editor-container {
  height: 100% !important;
  border: 2px solid rgb(190, 185, 185) !important;
}

.diffOverview {
  height: 100% !important;
}

.diffViewport {
  height: 100% !important;
}

canvas.decorationsOverviewRuler{
  height: 100% !important;
}

div.invisible.scrollbar.vertical{
  height: 100% !important;
}

div.slider{
  height: 100% !important;
}

.view-lines{
  height: 100% !important;

}

.monaco-scrollable-element.editor-scrollable.vs{
  height: 100% !important;
}

.overflow-guard{
  height: 100% !important;  
}

.monaco-editor.original-in-monaco-diff-editor.no-user-select.vs{
  height: 100% !important; 
}

.monaco-editor.modified-in-monaco-diff-editor.no-user-select.vs{
  height: 100% !important; 
}
