import { Component, OnInit, ElementRef, ViewChild } from '@angular/core';
import { SwtModule, CommonService, SwtCommonGrid, SwtCanvas } from 'swt-tool-box';

@Component({
  selector: 'app-log-grid',
  templateUrl: './LogGridTab.html',
  styleUrls: ['./LogGridTab.css']
})
export class LogGridTab  extends SwtModule implements OnInit {
  public logGrid: SwtCommonGrid;
  @ViewChild('logGridContainer') logGridContainer: SwtCanvas;
  
  constructor(private commonService: CommonService, private element: ElementRef) {
    super(element, commonService);
   // this.swtAlert = new SwtAlert(commonService);

  }

  ngOnInit() {
    this.logGrid = <SwtCommonGrid>this.logGridContainer.addChild(SwtCommonGrid);
  }

  onLoad(){

  }

}
