<SwtModule (close)='popupClosed()' (creationComplete)='onLoad()' height='100%' title='{{title}}' width='100%'>
  <VBox id="vBox1" width='100%' height='100%' paddingBottom="5" paddingLeft="5" paddingTop="5" paddingRight="5">
    <SwtCanvas width="100%" height="60%">
      <Grid width="100%" height="100%">
        <GridRow>
          <GridItem width="40%">
            <SwtLabel #profileNameLabel   #profileName>
            </SwtLabel>
          </GridItem>
          <GridItem width="60%">
            <SwtEditableComboBox #profileCombo></SwtEditableComboBox>
          </GridItem>
        </GridRow>
      </Grid>
    </SwtCanvas>
    <SwtCanvas id="canvasButtons" width="100%" height="40%">
      <HBox width="100%" height="100%">
        <HBox paddingLeft="5" width="100%">
          <SwtButton #saveButton (click)="saveProfileClickHandlerFromIframe()" (keyDown)="keyDownEventHandler($event)" id="saveButton">
          </SwtButton>
          <SwtButton buttonMode="true" id="cancelButton" #cancelButton (click)="popupClosed();"
            (keyDown)="keyDownEventHandler($event)">
          </SwtButton>
        </HBox>
        <HBox horizontalAlign="right" paddingRight="5">
          <SwtHelpButton id="helpIcon" [buttonMode]="true" enabled="true" (click)="doHelp()">
          </SwtHelpButton>
        </HBox>
      </HBox>
    </SwtCanvas>
  </VBox>
</SwtModule>
