<SwtModule (creationComplete)='onLoad()' width="100%" height="100%">
  <VBox  width="100%" height="100%" paddingBottom="10" paddingRight="10" paddingTop="10" paddingLeft="10">
    <SwtCanvas #canvasOption width="100%" height="90%"></SwtCanvas>
    <HBox width="100%" horizontalAlign="right" marginTop="5"  marginBottom="5">
      <SwtButton #orderButton label = "Apply Ordering" (click)="orderHandler()"></SwtButton>
    </HBox>
    <SwtCanvas width="100%" height="40">
    <HBox width="100%" height="100%">
      <HBox width="100%" height="100%">
      <SwtButton #saveButton label = "Save" (click)="saveHandler()"></SwtButton>
      <SwtButton #cancelButton label = "Cancel" (click)="cancelHandler()"></SwtButton>
      <!-- <SwtButton #closeButton label = "Close" (click)="closeHandler()"></SwtButton> -->
      </HBox>
      <HBox horizontalAlign="right">
        <DataExport  #exportContainer id="exportContainer"></DataExport>
        <!-- <SwtButton #closeButton label = "Close" (click)="closeHandler()"></SwtButton> -->
        <SwtLoadingImage #loadingImage>
        </SwtLoadingImage>

      </HBox>

    </HBox>
    </SwtCanvas>
  </VBox>
</SwtModule>
