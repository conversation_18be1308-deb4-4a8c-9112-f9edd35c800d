import { Component, OnInit, ViewChild, ElementRef, ChangeDetectionStrategy, ChangeDetectorRef } from '@angular/core';
import {
  SwtLabel,
  CommonService,
  SwtModule,
  SwtAlert,
  HTTPComms,
  JSONReader,
  Timer,
  ExternalInterface,
  SwtCheckBox,
  SwtUtil,
  SwtCommonGrid,
  SwtCanvas,
  CustomTree,
  ILMTreeIndeterminate,
  LinkButton,
  SwtButton,
  SwtTabNavigator,
  Encryptor,
  StringUtils,
  DateUtils,
  ExportEvent,
  SwtDataExport,
  SwtComboBox,
  HDividedBox,
  VDividedBox,
  SwtAdvSlider,
  HBox,
  VBox,
  CheckBoxLegend,
  AssetsLegend,
  ILMSeriesLiveValue,
  ILMLineChart,
  SeriesHighlightEvent,
  LegendItemChangedEvent,
  SwtCheckboxEvent,
  Series,
  SeriesStyleProvider,
  AdvancedExportEvent,
  Tab, XML, TabChange, TabPushStategy
} from 'swt-tool-box';

declare var require: any;
const $ = require('jquery');
import moment from "moment";
import { Subscription } from 'rxjs';

var htmlToImage = require('html-to-image');


declare let html2canvas: any;

//var convert = require('xml-js');
@Component({
  selector: 'app-group-analysis',
  templateUrl: './GroupAnalysis.html',
  styleUrls: ['./GroupAnalysis.css'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  host: {
    '(window:resize)': 'resizeTab()'
  }
})
export class GroupAnalysis extends SwtModule implements OnInit {

  @ViewChild("divRestOfspace") divRestOfspace: ElementRef;
  @ViewChild("ilmTree") ilmTree: ILMTreeIndeterminate;
  @ViewChild('canvas') canvas: ElementRef;
  @ViewChild('groupGridContainer') groupGridContainer: SwtCanvas;
  @ViewChild('scenarioGridContainer') scenarioGridContainer: SwtCanvas;
  @ViewChild('balanceGridContainer') balanceGridContainer: SwtCanvas;
  @ViewChild('topChartContainer') topChartContainer: SwtCanvas;


  @ViewChild('thresholdsLegends') thresholdsLegends: VBox;
  @ViewChild('chartsContainer') chartsContainer: VBox;
  @ViewChild('thresholdsLegendLabel') thresholdsLegendLabel: SwtLabel;


  @ViewChild('htmlText') htmlText: SwtLabel;
  @ViewChild('ExpandCollapse') ExpandCollapse: LinkButton;
  @ViewChild('entityTimeFrameLabel') entityTimeFrameLabel: SwtLabel;
  @ViewChild('alignScaleCB') alignScaleCB: SwtCheckBox;

  @ViewChild('includeOpenMvnts') includeOpenMvnts: SwtCheckBox;
  @ViewChild('sumByCutOff') sumByCutOff: SwtCheckBox;
  @ViewChild('sourceLiquidityTop') sourceLiquidityTop: SwtCheckBox;

  @ViewChild('styleBtn') styleBtn: SwtButton;


  @ViewChild('ShowActualDSOnly') ShowActualDSOnly: SwtCheckBox;
  @ViewChild('maintainButton') maintainButton: SwtButton;
  @ViewChild('tabNavigator') tabNavigator: SwtTabNavigator;
  @ViewChild('exportContainer') exportContainer: SwtDataExport;
  @ViewChild('groupCombo') groupCombo: SwtComboBox;
  @ViewChild('scenarioCombo') scenarioCombo: SwtComboBox;


  @ViewChild('legendsDivider') legendsDivider: HDividedBox;
  @ViewChild('treeDivider') treeDivider: HDividedBox;
  @ViewChild('gridDivider') gridDivider: VDividedBox;
  @ViewChild('legendsBox') legendsBox: SwtCanvas;


  @ViewChild('timeRange') timeRange: SwtAdvSlider;


  @ViewChild('linechart') linechart: ILMLineChart;

  @ViewChild('balanceLegend') balanceLegend: CheckBoxLegend;
  @ViewChild('AccumulatedDCLegend') AccumulatedDCLegend: CheckBoxLegend;
  @ViewChild('assetsLegend') assetsLegend: AssetsLegend;



  @ViewChild('accumLabel') accumLabel: SwtLabel;
  @ViewChild('balanceLabel') balanceLabel: SwtLabel;
  @ViewChild('labelForCombined') labelForCombined: SwtLabel;
  @ViewChild('chartValuesContainer') chartValuesContainer: HBox;
  @ViewChild('timeDynamicValue') timeDynamicValue: ILMSeriesLiveValue;

  @ViewChild('timeLabel') timeLabel: SwtLabel;
  @ViewChild('sourcesLabel') sourcesLabel: SwtLabel;
  @ViewChild('groupsLegendLabel') groupsLegendLabel: SwtLabel;
  @ViewChild('scenarioLegendLabel') scenarioLegendLabel: SwtLabel;
  @ViewChild('groupsTab') groupsTab: TabPushStategy;
  @ViewChild('scenarioTab') scenarioTab: TabPushStategy;
  @ViewChild('balancesTab') balancesTab: TabPushStategy;
  @ViewChild('closeButton') closeButton: SwtButton;




  protected saveGlobalFirstTimeProfile: boolean = false;
  protected saveAnalysisFirstTimeProfile: boolean = false;
  protected swtAlert: SwtAlert;

  private _currencyFormat: string = '';






  protected checked: boolean = false;

  public optionsSelectedGroups: string = "";

  protected timeData = []
  //This variable holds if we should get data from profile or not (first load)
  protected saveLastCheckedValues: boolean = true;
  protected resetScreenProfile: boolean = false;

  public usedGroupData = []


  /**
   * Communication Objects
   **/
  public chartsInputData = new HTTPComms(this.commonService);
  public gridsAndTreeInputData = new HTTPComms(this.commonService);
  public iLMConfData = new HTTPComms(this.commonService);
  public saveProfileData = new HTTPComms(this.commonService);
  public grpScenComboData = new HTTPComms(this.commonService);
  public seriesStyleProperty = new HTTPComms(this.commonService);
  protected colWidthOrder: HTTPComms = new HTTPComms(this.commonService);

  /**
   * Data Objects
   **/
  protected jsonReader: JSONReader = new JSONReader();
  protected lastRecievedXML;
  protected prevRecievedXML;
  protected lastRecievedXMLGrid;
  protected prevRecievedXMLGrid;
 // TesT
  protected treeXML;

  // Holds the action method
  public actionMethod: string = "";
  // Holds the action path
  public actionPath: string = "";


  protected requestParams = [];
  protected ilmConfParams: Object;
  public now: string = "";
  protected systemDate: string;
  protected baseURL = SwtUtil.getBaseURL();
  public balanceCharts = []

  public accumulatedDCCharts = []

  //container a list of all nodes
  protected listNode;

  //container a list of selected nodes only
  protected listNodeSelected;

  public isExpand: boolean = true;
  //Save unchecked element in legend
  public uncheckedItemsInLengend = []
  public group_id: string = "";
  public scenario_id: string = "";

  public nowCurrencyTime: Date;
  public nowEntityTime: Date;
  public currencyTimeDiff: number;
  public entityTimeDiff: number;

  protected serverTime: Date = null;
  protected serverTimeDiff: number = 0;

  //Clock Timer
  protected clockRefresh: Timer = null;

  // Old values for now date for currency and entity timeframe

  public creator_grp: string = "";
  public creator_scn: string = "";
  public grp_type: string = "";
  public scn_type: string = "";

  protected hSliderValues = [];

  protected static isGroupGrid: string = "groupGrid";
  protected static isGlobalGroupGrid: string = "globalGroupGrid";
  protected static isScenarioGrid: string = "scenarioGrid";
  protected static isTree: string = "tree";
  protected static isTimeRange: string = "timeRange";
  protected static isIncludeStartOfDay: string = "includeStartOfDay";
  protected static isIncludeOpenMvnts: string = "includeOpenMvnts";
  protected static isSumByCutOff: string = "sumByCutOff";
  protected static isShowEntityScale: string = "showEntityScale";
  protected static isShowActualDataSetOnly: string = "showDataSetOnly";
  protected static isSelectedLegend: string = "selectedLegend";
  protected static isLastUsedProfile: string = "lastUsedProfile";
  protected static isShowSourceLiquidity: string = "showSourceLiquidity";
  protected static isGroupSourceLiquidityChanged: string = "isGroupSourceLiquidityChanged";
  protected static isScenarioSourceLiquidityChanged: string = "isScenarioSourceLiquidityChanged";
  protected static isUncheckedThresholds: string = "uncheckedThresholds";

  protected static GBL_LEGEND_DIVIDER_CLOSED: string = "gblLegendDividerIsClosed";
  protected static GBL_GRID_DIVIDER_CLOSED: string = "gblGridDividerIsClosed";
  protected static GBL_TREE_DIVIDER_CLOSED: string = "gblTreeDividerIsClosed";
  protected static GRP_LEGEND_DIVIDER_CLOSED: string = "grpLegendDividerIsClosed";
  protected static GRP_GRID_DIVIDER_CLOSED: string = "grpGridDividerIsClosed";
  protected static GRP_TREE_DIVIDER_CLOSED: string = "grpTreeDividerIsClosed";

  protected resetZoomBeforeTimeRange: boolean = false;
  protected applyZoomFromButtons: boolean = true;
  protected subscriptions: Subscription[] = [];

  public highlightedSeries = []
  public uncheckedThresholdItems = []
  public prevVisibleTreeItems = []
  public entityTimeFrameSelected: boolean = false;
  public calledwhenEntityChanged: boolean = false;
  public onLoadisCalled: boolean = false;
  protected defaultProfileSaved: string = "";

  public lastSelectedGroup: string;
  public lastSelectedScenario: string;
  public noneLabel: string = "";
  public uncheckedThresholds: string;
  public analysisLegendsBoxWidth: number = 250;
  public analysistreeBoxWidth: number = 270;
  public analysisgridBoxHeight: number = 150;
  public globalLegendsBoxWidth: number = 250;
  public globaltreeBoxWidth: number = 270;
  public globalgridBoxHeight: number = 150;
  protected addNewCharts: boolean = false;

  protected segmentsLineCharts = []
  protected areasLineCharts = []
  public showSourceLiquidity: boolean = false;




  public groupGrid: SwtCommonGrid;
  public scenarioGrid: SwtCommonGrid;
  public balanceGrid: SwtCommonGrid;
  public tabName: string = '';
  optionsUsedForGroups: boolean = false;
  globalGroupId: any;
  ilmTabId: any;


  constructor(private commonService: CommonService, private element: ElementRef, private cd: ChangeDetectorRef) {
    super(element, commonService);
    this.swtAlert = new SwtAlert(commonService);
  }

  resizeTab() {
    const posData = this.divRestOfspace.nativeElement.getBoundingClientRect();
    //Called after every check of the component's view. Applies to components only.
    //Add 'implements AfterViewChecked' to the class.
    const calRest = (window.innerHeight - posData.top);
    this.divRestOfspace.nativeElement.style.height = Math.round(calRest) + "px";

  }


  public get currencyFormat(): string {
    return this.parentDocument.currencyFormat;
  }


  public set currencyFormat(value: string) {
    this.parentDocument.currencyFormat = value;
  }
  ngAfterViewInit() {
    // this.linechart.iframeContaier.setAttribute('id', 'iframe' + this.tabName);
    this.resizeTab();
  }
  
  isIE(){
    return /Trident\/|MSIE/.test(window.navigator.userAgent);
  }

  ngOnInit() {
    this.noneLabel = '<None>'; // ExternalInterface.call('getBundle', 'text', 'label-noneProfile', '<None>');
    this.legendsDivider.name = this.id;
    this.legendsDivider.id = this.id;
    this.ShowActualDSOnly.label = ExternalInterface.call('getBundle', 'text', 'showactual', 'Show Actual Datasets Only');
    this.ExpandCollapse.label = ExternalInterface.call('getBundle', 'text', 'expandall', 'Expand All');
    this.labelForCombined.text = ExternalInterface.call('getBundle', 'text', 'groupanalysis', 'Group Analysis');
    this.timeLabel.text = ExternalInterface.call('getBundle', 'text', 'time', 'Time') + ' (';
    this.balanceLabel.text = ExternalInterface.call('getBundle', 'text', 'balance', 'Balance');
    this.accumLabel.text = ExternalInterface.call('getBundle', 'text', 'accumDC', 'Accumulated D/C');
    this.thresholdsLegendLabel.text = ExternalInterface.call('getBundle', 'text', 'label-thresholds', 'Thresholds');
    this.sourcesLabel.text = ExternalInterface.call('getBundle', 'text', 'label-sourcesOfLiquidity', 'Sources of Liquidity');
    this.groupsLegendLabel.text = ExternalInterface.call('getBundle', 'text', 'combo-groupLabel', 'Group');
    this.groupCombo.toolTip = ExternalInterface.call('getBundle', 'tip', 'combo-groupTooltip', 'Select a group Id');
    this.scenarioLegendLabel.text = ExternalInterface.call('getBundle', 'text', 'combo-scenarioLabel', 'Scenario');
    this.scenarioCombo.toolTip = ExternalInterface.call('getBundle', 'tip', 'combo-ScenarioTooltip', 'Select a Scenario');
    this.groupsTab.label = ExternalInterface.call('getBundle', 'text', 'groups', 'Groups');
    this.scenarioTab.label = ExternalInterface.call('getBundle', 'text', 'scenarios', 'Scenarios');
    this.balancesTab.label = ExternalInterface.call('getBundle', 'text', 'balances', 'Balances');
    this.maintainButton.label = ExternalInterface.call('getBundle', 'text', 'maintain', 'Maintain');
    this.styleBtn.label = ExternalInterface.call('getBundle', 'text', 'label-setStyle', 'Set Style');
    this.styleBtn.toolTip = ExternalInterface.call('getBundle', 'tip', 'button-setStyle', 'Change the style of visible Series');
    this.closeButton.label = ExternalInterface.call('getBundle', 'text', 'button-close', 'Close');
    this.closeButton.toolTip = ExternalInterface.call('getBundle', 'tip', 'button-close', 'Close window');
    if(this.isIE()){
      this.topChartContainer.height="90"
    }
    // Event listener to highlight the legend with its related series
    this.subscriptions.push(
      SeriesHighlightEvent.subscribe((value) => {
        // if (value && value.parentTab && value.parentTab == this.tabName)
        if (value && value.parentTab && value.parentTab == this.id)
          this.highlighLegend(value);
      }),
    );
    this.subscriptions.push(
    TabChange.subscribe((tab) => {
        // setTimeout(() => {
          // if (this.isVisible(this.element.nativeElement)) {
              this.cd.markForCheck();
          // }
        // }, 400);
    })
  );
    // // Event listener when changing the status of any checkbox in the tree in order to show/hide the legends and charts
    this.subscriptions.push(
      LegendItemChangedEvent.subscribe((value) => {
        // if (value && value.parentTab && value.parentTab == this.tabName)
        if (value && value.parentTab && value.parentTab == this.id)
          this.legendItemChanged(value);
      }),
    );
    this.subscriptions.push(
      SwtCheckboxEvent.subscribe((value) => {
        // if (value && value.parentTab && value.parentTab == this.tabName){
        if (value && value.parentTab && value.parentTab == this.id){
          this.showHideLegendsAndCharts(value);

        }
      }),
    );
    // // Event listener when the user changes the time range via the slider 

    this.timeRange.ZOOM_EVENT_ZOOMED.subscribe((event) => {
      this.applyZoomInChart(event);
    });

    this.treeDivider.DIVIDER_DRAG_COMPLETE.subscribe((event) => {
      this.saveDividerStatusListener(event);
    });
    this.legendsDivider.DIVIDER_DRAG_COMPLETE.subscribe((event) => {
      this.saveDividerStatusListener(event);
    });
    this.gridDivider.DIVIDER_DRAG_COMPLETE.subscribe((event) => {
      this.saveDividerStatusListener(event);
    });

    this.treeDivider.DIVIDER_BUTTON_CLICK.subscribe((event) => {
      this.saveDividerStatusListener(event);
    });
    this.legendsDivider.DIVIDER_BUTTON_CLICK.subscribe((event) => {
      this.saveDividerStatusListener(event);
    });
    this.gridDivider.DIVIDER_BUTTON_CLICK.subscribe((event) => {
      this.saveDividerStatusListener(event);
    });







  }

  isVisible(e) {
    return !!(e.offsetWidth || e.offsetHeight || e.getClientRects().length);
  }


  /**
   * The result when saving the order or the width of columns
   * 
   * @param event
   * 
   */
  protected saveWidthOrderResult(event): void {
    var xmlReader: JSONReader = new JSONReader();
    xmlReader.setInputJSON(event);
    if (!xmlReader.getRequestReplyStatus())
      this.swtAlert.show(xmlReader.getRequestReplyMessage(), ExternalInterface.call('getBundle', 'text', 'label-error', 'Error'));
  }

  /**
   * 
   * The result when saving the order or the width of columns
   * 
   * @param event
   * 
   */
  protected saveWidthOrderFault(event): void {
    // console.log(ExternalInterface.call('getBundle', 'text', 'label-unableSave', 'Unable to save to server\nPossible loss of connection'), ExternalInterface.call('getBundle', 'text', 'label-error', 'Error'));
    console.log('Unable to save to server\nPossible loss of connection');
  }

  /**
 * This function acts as a helper method to set the column width.<br>
 */
  updateColumnWidths(gridName): void {
    let columnWidth = [];
    let cols = null;
    //"groupGrid" : "scenarioGrid";
    if (gridName) {
      if (gridName === "groupGrid") {
        cols = this.groupGrid.gridObj.getColumns();
      } else {
        cols = this.scenarioGrid.gridObj.getColumns();
      }

      for (let i = 0; i < cols.length - 1; i++) {
        if (cols[i].field != null) {
          columnWidth.push(cols[i].field + "=" + cols[i].width);
        }
      }
      if (this.colWidthOrder.isBusy()) {
        this.colWidthOrder.cancel();
      }
      this.actionPath = 'ilmAnalysisMonitor.do?';
      this.actionMethod = 'method=saveLiquidityMonitorConfig&amp;';
      this.colWidthOrder.url = this.baseURL + this.actionPath + this.actionMethod;
      let colParams = new Object();

      colParams["width"] = columnWidth.join(",");


      if (this.tabName == "GlobalView") {
        colParams["paramName"] = gridName === "groupGrid" ? "gblGrpColWidth" : "gblScnColWidth";
        colParams["paramValue"] = columnWidth.join(",");
      } else {
        colParams["paramName"] = gridName === "groupGrid" ? "grpGrpColWidth" : "grpScnColWidth";
        colParams["paramValue"] = columnWidth.join(",");
      }
      colParams["isGeneral"] = "true";

      this.colWidthOrder.send(colParams);
    }
  }
  /**
 * This function acts as a helper method to set the column width.<br>
 */
  updateColumnOrder(gridName): void {
    let columnWidth = [];
    let cols = null;
    //"groupGrid" : "scenarioGrid";
    if (gridName) {
      if (gridName === "groupGrid") {
        cols = this.groupGrid.gridObj.getColumns();
      } else {
        cols = this.scenarioGrid.gridObj.getColumns();
      }

      for (let i = 0; i < cols.length - 1; i++) {
        if (cols[i].field != null) {
          columnWidth.push(cols[i].field);
        }
      }
      if (this.colWidthOrder.isBusy()) {
        this.colWidthOrder.cancel();
      }
      this.actionPath = 'ilmAnalysisMonitor.do?';
      this.actionMethod = 'method=saveLiquidityMonitorConfig&amp;';
      this.colWidthOrder.url = this.baseURL + this.actionPath + this.actionMethod;
      let colParams = new Object();

      colParams["width"] = columnWidth.join(",");


      if (this.tabName == "GlobalView") {
        colParams["paramName"] = gridName === "groupGrid" ? "gblGrpColOrder" : "gblScnColOrder";
        colParams["paramValue"] = columnWidth.join(",");
      } else {
        colParams["paramName"] = gridName === "groupGrid" ? "grpGrpColOrder" : "grpScnColOrder";
        colParams["paramValue"] = columnWidth.join(",");
      }
      colParams["isGeneral"] = "true";

      this.colWidthOrder.send(colParams);
    }
  }






  public resetScreen(expand: boolean = true): void {
    this.saveLastCheckedValues = false;
    if (expand) {
      this.uncheckedThresholdItems = new Array();
      this.isExpand = true;
      this.ExpandCollapse.label = ExternalInterface.call('getBundle', 'text', 'expandall', 'Expand All');
      this.alignScaleCB.selected = true;
      this.hSliderValues = null;
      this.resetScreenProfile = true;
      this.entityTimeFrameSelected = false;
      this.entityTimeFrameLabel.text = ExternalInterface.call('getBundle', 'text', 'currency', 'Currency')+ "[" + (this.parentDocument.timeframe.text?this.parentDocument.timeframe.text:"")+"]";
      this.entityTimeFrameLabel.toolTip = ExternalInterface.call('getBundle', 'text', 'tip-currency', 'Click to show scale in entity timeframe');
      this.showSourceLiquidity = false;
      this.highlightedSeries = new Array;
      this.linechart.includeSOD = true;
      this.linechart.resetILMLineChart();
    }
  }



  /**
         * Applying a zoom on the chart area
         * */
  protected applyZoomInChart(event, saveProfile: Boolean = true): void {
    var timeFromAsInt: Number;
    var timeToAsInt: Number;
    // Update the NowVerticalLine after zoom
    this.drawNowLineAndLiquidityZones(null, true);


    var groupId: string = this.groupCombo.selectedLabel;
    var scenarioId: string = this.scenarioCombo.selectedLabel;
    if (!StringUtils.isEmpty(groupId) && !StringUtils.isEmpty(scenarioId)) {
      this.linechart.drawLiquidityZones(groupId, scenarioId, null, null, this.alignScaleCB.selected, this.showSourceLiquidity);
    }

    //ApplyZoom in IFrame from Flex
    //Get timeStampFrom values

    if (!StringUtils.isEmpty(event.from) && !StringUtils.isEmpty(event.to) && (this.linechart.timeRangeArray)) {
      timeFromAsInt = moment(event.from).toDate().getTime()
      timeToAsInt = moment(event.to).toDate().getTime()

    }
    //zoom(fromTimeStamp, toTimeStamp);
    this.linechart.callMethodInIframe('zoom', ["" + timeFromAsInt, "" + timeToAsInt])
    if (saveProfile) {
      if (this.tabName === "GlobalView") {
        this.callLater('saveProfileSettings', [GroupAnalysis.isTimeRange, true]);
      } else {
        this.callLater('saveProfileSettings', [GroupAnalysis.isTimeRange, false]);
      }
    }
  }

  runCheckProfileOnLoad(): void {
    this.saveGlobalFirstTimeProfile = true;
    this.saveAnalysisFirstTimeProfile = true;
  }

  onLoad(newProfile?: boolean) {
    if (!this.groupGrid) {
      this.groupGrid = <SwtCommonGrid>this.groupGridContainer.addChild(SwtCommonGrid);
      this.scenarioGrid = <SwtCommonGrid>this.scenarioGridContainer.addChild(SwtCommonGrid);
      this.balanceGrid = <SwtCommonGrid>this.balanceGridContainer.addChild(SwtCommonGrid);


      this.groupGrid.editable = true;
      this.scenarioGrid.editable = true;
      this.balanceGrid.editable = true;
      this.linechart.parentDocument = this;

      this.linechart.accumulatedDCLegend = this.AccumulatedDCLegend;
      this.linechart.balancesLegend = this.balanceLegend;
      this.linechart.assetsLegend = this.assetsLegend;
      this.linechart.accumLabel = this.accumLabel;
      this.linechart.balanceLabel = this.balanceLabel;
      this.linechart.chartValuesContainer = this.chartValuesContainer;
      this.linechart.timeDynamicValue = this.timeDynamicValue;
      AdvancedExportEvent.subscribe((event) => {
        if (event.id == this.exportContainer.id) {
          this.export(event.type)
        }
      });


      this.scenarioGrid.uniqueColumn = "scenario";
      this.groupGrid.uniqueColumn = "group";

      this.groupGrid.columnWidthChanged.subscribe((event) => {
        this.updateColumnWidths("groupGrid");
      });
      this.scenarioGrid.columnWidthChanged.subscribe((event) => {
        this.updateColumnWidths("scenarioGrid");
      });
      this.groupGrid.columnOrderChanged.subscribe((event) => {
        this.updateColumnOrder("groupGrid");
      });
      this.scenarioGrid.columnOrderChanged.subscribe((event) => {
        this.updateColumnOrder("scenarioGrid");
      });

    }

    setTimeout(() => {
      
    

    // this.htmlText.htmlText = "<center><br/><br/><b>Flex Rules</b><br/><br/><br/>The HTML Component merges HTML seemlessly within your Flex application.</center>";

    // Calls to start the Http communication
    this.gridsAndTreeInputData.cbStart = this.startOfComms.bind(this);
    // Calls to stop the Http communication
    this.gridsAndTreeInputData.cbStop = this.endOfComms.bind(this);
    // Calls the inputDataResult function to load the datagrid
    this.gridsAndTreeInputData.cbResult = (data) => {
      this.inputDataResultGrid(data);
    };

    this.iLMConfData.cbResult = this.saveResult.bind(this);
    this.iLMConfData.cbFault = this.saveFault.bind(this);
    this.iLMConfData.encodeURL = false;


    this.saveProfileData.cbResult = this.saveBasicProfileResult.bind(this);
    this.saveProfileData.cbFault = this.saveFault.bind(this);
    this.saveProfileData.encodeURL = false;

    this.seriesStyleProperty.cbResult = this.saveStyleResult.bind(this);
    this.seriesStyleProperty.cbFault = this.saveStyleFault.bind(this);
    this.seriesStyleProperty.encodeURL = false;

    this.colWidthOrder.cbResult = this.saveWidthOrderResult.bind(this);
    this.colWidthOrder.cbFault = this.saveWidthOrderFault.bind(this);
    this.colWidthOrder.encodeURL = false;

    // Init the timeData array
    this.timeData = [];

    // Sets the action path for Interface Monitor
    this.actionPath = "ilmAnalysisMonitor.do?";
    // Sets the action method to get the Interface Monitor Details
    this.actionMethod = "method=getGroupTreeAndGridData";
    // Sets the full URL for Interface Monitor
    this.gridsAndTreeInputData.url = this.baseURL + this.actionPath + this.actionMethod;
    // Sets the flag for encoding URL to false
    this.gridsAndTreeInputData.encodeURL = false;
    // Calls the inputDataFault function
    this.gridsAndTreeInputData.cbFault = this.inputDataFault.bind(this);
    // this.requestParams["entityId"] = this.parentDocument.entityCombo.selectedItem;
    // this.requestParams["currencyId"] = this.parentDocument.ccyCombo.selectedItem;
    // this.requestParams["selectedDate"] = DateUtils.getDateAsString(this.parentDocument.valueDate);
    this.whileWithSleep(this.checkFunc, 300)
    .then(() => {
        this.requestParams["entityId"] = this.parentDocument.entityTabName;
        this.requestParams["currencyId"] = this.parentDocument.ccyTabName;
        //FIXME:
        // this.requestParams["selectedDate"] = "30/09/2008"//DateUtils.getDateAsString(this.parentDocument.valueDate);
        this.requestParams["selectedDate"] = this.parentDocument.valueDate.text
        this.requestParams["useCcyMultiplier"] = "N";//ExternalInterface.call('eval', 'useCcyMultiplier');
        //FIXME:Check
        if (this.parentDocument.profileCombo.selectedItem == this.noneLabel || newProfile) {
          this.requestParams["currentProfile"] = this.parentDocument.profileCombo.selectedItem.content;
          this.defaultProfileSaved = this.parentDocument.profileCombo.selectedItem.content;
        }
        else {
          this.requestParams["currentProfile"] = "";
          this.defaultProfileSaved = "";
        }
        // Send the request to the server
        
        this.gridsAndTreeInputData.send(this.requestParams);

        this.maintainButton.enabled = false;
        //FIXME:
        // Listeners for the dividers of group analysis when clicking the button of the divider in order to save the status
        // this.legendsDivider.addEventListener(DividerButtonEvent.DIVIDER_BUTTON_CLICK, saveDividerStatusListener);
        // this.treeDivider.addEventListener(DividerButtonEvent.DIVIDER_BUTTON_CLICK, saveDividerStatusListener);
        // this.gridDivider.addEventListener(DividerButtonEvent.DIVIDER_BUTTON_CLICK, saveDividerStatusListener);
        // // Listeners for the dividers of group analysis when clicking the button of the divider in order to save the status
        // this.legendsDivider.addEventListener(DividerButtonEvent.DIVIDER_DRAG_COMPLETE, saveDividerStatusListener);
        // this.treeDivider.addEventListener(DividerButtonEvent.DIVIDER_DRAG_COMPLETE, saveDividerStatusListener);
        // this.gridDivider.addEventListener(DividerButtonEvent.DIVIDER_DRAG_COMPLETE, saveDividerStatusListener); 
        this.onLoadisCalled = true;
        //FIXME:
        // ExternalInterface.addCallback("closeAlertWindowFromCallBack",closeAlertWindowFromCallBack);
        // ExternalInterface.addCallback("callHTMLAlert", callHTMLAlert);
    })
    .catch(error => {
      console.error('An error occurred:', error);
    });
    }, 500);
  }

  public DIVIDER_DRAG_COMPLETE = 'DIVIDER_DRAG_COMPLETE';
  public DIVIDER_BUTTON_CLICK = 'DIVIDER_BUTTON_CLICK';

  public saveDividerStatusListener(event): void {
    const isSelectedTab =  this.tabName == "GlobalView" && (this.parentDocument.tabNavigator.selectedIndex == 0) || 
    this.tabName == "GroupAnalysis" && (this.parentDocument.tabNavigator.selectedIndex == 1);


    // Save the divider status in the database if it is closed or opened
    if (event.type == this.DIVIDER_DRAG_COMPLETE && isSelectedTab) {
      if (event.id == this.legendsDivider.id) {
        // if (this.legendsDivider.widthRightPixel == 0 || this.analysisLegendsBoxWidth == 0) {
          if (this.tabName !== "GlobalView")
            this.saveProfileSettings(GroupAnalysis.GRP_LEGEND_DIVIDER_CLOSED, false, "" + (this.legendsDivider.widthRightPixel == 0));
          else
            this.saveProfileSettings(GroupAnalysis.GBL_LEGEND_DIVIDER_CLOSED, false, "" + (this.legendsDivider.widthRightPixel == 0));
          //FIXME:updateMinWidthMainScreen
          // this.updateMinWidthMainScreen(this.legendsBox.width == 0, this.treeDivider.buttonSelected);
        // }
        if (this.tabName !== "GlobalView")
          this.analysisLegendsBoxWidth = this.legendsDivider.widthRightPixel;
        else
          this.globalLegendsBoxWidth = this.legendsDivider.widthRightPixel;


      }
      else if (event.id == this.treeDivider.id) {
        // if (this.treeDivider.widthLeftPixel == 0 || this.analysistreeBoxWidth == 0) {
          if (this.tabName !== "GlobalView")
            this.saveProfileSettings(GroupAnalysis.GRP_TREE_DIVIDER_CLOSED, false, "" + (this.treeDivider.widthLeftPixel == 0))
          else
            this.saveProfileSettings(GroupAnalysis.GBL_TREE_DIVIDER_CLOSED, false, "" + (this.treeDivider.widthLeftPixel == 0))
          // this.updateMinWidthMainScreen(this.legendsDivider.buttonSelected, this.treeBox.width == 0);
        // }
        if (this.tabName !== "GlobalView")
          this.analysistreeBoxWidth = this.treeDivider.widthLeftPixel;
        else
          this.globaltreeBoxWidth = this.treeDivider.widthLeftPixel;
      }
      else if (event.id == this.gridDivider.id) {
        // if (this.gridDivider.heightBottomPixel == 0 || this.analysisgridBoxHeight == 0) {


          if (this.tabName !== "GlobalView")
            this.saveProfileSettings(GroupAnalysis.GRP_GRID_DIVIDER_CLOSED, false, "" + (this.gridDivider.heightBottomPixel == 0));
          else
            this.saveProfileSettings(GroupAnalysis.GBL_GRID_DIVIDER_CLOSED, false, "" + (this.gridDivider.heightBottomPixel == 0))


        // }
        if (this.tabName !== "GlobalView")
          this.analysisgridBoxHeight = this.gridDivider.heightBottomPixel;
        else
          this.globalgridBoxHeight = this.gridDivider.heightBottomPixel;
      }
    }
    else if (event.type == this.DIVIDER_BUTTON_CLICK && isSelectedTab) {
      if (event.id == this.legendsDivider.id) {
        // this.updateMinWidthMainScreen(event.dividerObject.isClosed, this.treeDivider.buttonSelected);
        if (this.tabName !== "GlobalView") {
          this.analysisLegendsBoxWidth = event.isClosed ? 0 : 270;
          this.callLater('saveProfileSettings', [GroupAnalysis.GRP_LEGEND_DIVIDER_CLOSED, false, event.isClosed]);
        }
        else {
          this.globalLegendsBoxWidth = event.isClosed ? 0 : 270;
          this.callLater('saveProfileSettings', [GroupAnalysis.GBL_LEGEND_DIVIDER_CLOSED, false, event.isClosed]);
        }

      }
      else if (event.id == this.treeDivider.id) {

        // this.updateMinWidthMainScreen(this.legendsDivider.buttonSelected, event.dividerObject.isClosed);
        if (this.tabName !== "GlobalView") {
          this.analysistreeBoxWidth = event.isClosed ? 0 : 270;
          this.saveProfileSettings(GroupAnalysis.GRP_TREE_DIVIDER_CLOSED, false, event.isClosed);
        }
        else {
          this.globaltreeBoxWidth = event.isClosed ? 0 : 270;
          this.saveProfileSettings(GroupAnalysis.GBL_TREE_DIVIDER_CLOSED, false, event.isClosed);
        }
      }
      else if (event.id == this.gridDivider.id) {

        if (this.tabName !== "GlobalView") {
          this.saveProfileSettings(GroupAnalysis.GRP_GRID_DIVIDER_CLOSED, false, event.isClosed);
          this.analysisgridBoxHeight = event.isClosed ? 0 : 150;
        }
        else {
          this.saveProfileSettings(GroupAnalysis.GRP_GRID_DIVIDER_CLOSED, false, event.isClosed);
          this.globalgridBoxHeight = event.isClosed ? 0 : 150;
        }

      }
    }

    setTimeout(() => {
      if (!(this.cd as any).destroyed) {
      this.cd.markForCheck();
      }
    }, 0);
  }


  sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
  
  async whileWithSleep(functionToCheck , sleepDuration) {
    while (this.checkFunc()) {
      // Perform your desired actions inside the loop
      // Sleep for the specified duration
      await this.sleep(sleepDuration);
    }
  }
  checkFunc = () => this.parentDocument.valueDate.text == "" || this.parentDocument.valueDate.text == undefined || this.parentDocument.valueDate.text == null;





  public findYFields(xmlNodes) {
    var yFiledsNames = [];
    if (!xmlNodes.length)
      xmlNodes = [xmlNodes];
    for (let i: number = 0; i < xmlNodes.length; i++) {
      this.findYFieldsRecursive(xmlNodes[i], yFiledsNames);
    }

    return yFiledsNames;
  }

  protected findYFieldsRecursive(xmlNode, yFieldNames): void {

    if (xmlNode.isBranch == true && xmlNode.visible == true)
      for (let i: number = 0; i < xmlNode.node.length; i++) {
        this.findYFieldsRecursive(xmlNode.node[i], yFieldNames);
      }

    const yFiled = xmlNode.yField;
    const hideActual = xmlNode.hideActual;
    const hideAccumulated = xmlNode.hideAccumulated;

    if (yFiled) {
      if (hideActual != true) {

        if (yFiled.indexOf(",") > 0 && hideAccumulated != true)
          for (var i: number = 0; i < yFiled.split(",").length; i++)
            yFieldNames.push(yFiled.split(",")[i]);
        else
          yFieldNames.push(yFiled);
      }
    }
  }
  /**
     * When a tree node is selected/deselected
     **/
  protected showHideLegendsAndCharts(event): void {
    var yFieldValue: string = event.node.data.yField;
    // var legendNames = this.findYFields((event.data as XMLListCollection));
    var legendNames = this.findYFields(event.node.data);

    var groupId: string = "";
    var scenarioId: string = "";
    var grpScrToDisplay: string = "";
    var drawZones: boolean = true;
    var seriesToShow = [];
    if (event.node.selected) {
      var groupScen = [];
      var grpScenName: string = "";
      for (var i: number = 0; i < legendNames.length; i++) {
        if (!this.linechart.addChart(legendNames[i])) {
          var arr = (legendNames[i] as String).split(".");
          grpScenName = arr[0] + ':' + arr[1];
          if (groupScen.indexOf(grpScenName) == -1 && arr[1] != 'Thresholds') {
            groupScen.push(grpScenName);
          }
        } else {
          seriesToShow.push(legendNames[i]);
        }
      }

      var unselectedItemsFromLegends = [];


      unselectedItemsFromLegends = this.balanceLegend.getUncheckedLegends();
      const unselectedAccumulatedDCLegend = this.AccumulatedDCLegend.getUncheckedLegends();
      for (let k = 0; k < unselectedAccumulatedDCLegend.length; k++) {
        unselectedItemsFromLegends.push(unselectedAccumulatedDCLegend[k]);
      }

      for (var a = 0; a < unselectedItemsFromLegends.length; a++) {
        if (seriesToShow.indexOf(unselectedItemsFromLegends[a]) != -1)
          seriesToShow.splice(seriesToShow.indexOf(unselectedItemsFromLegends[a]), 1);
      }

      this.linechart.callMethodInIframe('showOrHideMultipleSeries', [seriesToShow.toString(), true])

      // Construct the list of group scenarios to be returned
      for (let index = 0; index < groupScen.length; index++) {
        const grpScn = groupScen[index];
        grpScrToDisplay = grpScrToDisplay + grpScn + '|';
      }
      if (grpScrToDisplay != "") {
        if (grpScrToDisplay.substr(-1) == "|") {
          grpScrToDisplay = grpScrToDisplay.slice(0, -1);
        }
      }
      //Group scenarios to be got from DB
      if (grpScrToDisplay != "") {
        //FIXME:Check if needed
        //this.linechart.resetZoom();

        if (this.tabName === "GlobalView") {
          this.actionMethod = "method=getGlobalChartsData";
        }
        else
          this.actionMethod = "method=getGroupChartsData";

        this.chartsInputData.url = this.baseURL + this.actionPath + this.actionMethod;

        this.requestParams = [];

        if (this.tabName === "GlobalView")
          this.requestParams["globalChart"] = "true";

        this.requestParams["entityId"] = this.parentDocument.entityTabName;
        this.requestParams["currencyId"] = this.parentDocument.ccyTabName;
        this.requestParams["selectedDate"] = this.parentDocument.valueDate.text;
        this.requestParams["useCcyMultiplier"] = this.parentDocument.parentDocument.ccyMuliplierCB.selected ? "Y" : "N";
        this.requestParams["selectedFigures"] = grpScrToDisplay;
        this.requestParams["currentProfile"] = null;
        //Make initial request
        this.chartsInputData.send(this.requestParams);
        this.ilmTree.enabled = false;
        $("body").addClass("wait"); // to add wait cursor
        drawZones = false;
        this.addNewCharts = true;

        if (this.showSourceLiquidity) {
          const groupId: string = this.groupCombo.selectedItem.value
          const scenarioId: string = this.scenarioCombo.selectedItem.value;
          setTimeout(() => {
            this.linechart.removeLiquidityRegion(groupId, scenarioId);
          }, 0);
        }
      }
    }
    else {
      for (let i: number = 0; i < legendNames.length; i++) {
        this.linechart.removeSeries(legendNames[i]);
      }
      this.linechart.callMethodInIframe('showOrHideMultipleSeries', [legendNames.toString(), false])

    }
    setTimeout(() => {
      this.drawNowLineAndLiquidityZones(null, drawZones);
    }, 0);
    if ((yFieldValue && yFieldValue.indexOf("Thresholds") != -1) || event.data.toString().length > 0)
      setTimeout(() => {
        this.refreshThresholdLegend();
      }, 0);
    //FIXME:
    // 	callLater(linechart.alignScale, [alignScaleCB.selected, showSourceLiquidity,true]);
    // Save the 
    if (this.tabName === "GlobalView")
      this.callLater('saveProfileSettings', [GroupAnalysis.isTree, true]);
    else
      this.callLater('saveProfileSettings', [GroupAnalysis.isTree, false]);


  }



  /**
 * Creates the now vertical line routine
 **/
  public drawNowLineAndLiquidityZones(evt, drawZones: boolean = false): void {
    // Calculate now time
    var nowDate: Date = new Date();
    if (this.entityTimeFrameSelected)
      nowDate.setTime(nowDate.getTime() + this.entityTimeDiff);
    else
      nowDate.setTime(nowDate.getTime() + this.currencyTimeDiff);

    this.linechart.callMethodInIframe('updateClock', [nowDate.getTime()])

  }


  /**
* Align the scale of balances with Accumulated D/C, The SOD (balance) value will be aligned 
* with Zero value (accumulated) 
**/
  public alignScale(event): void {
    this.linechart.includeSOD = this.alignScaleCB.selected;
    // Swith the data provider that will apply the SOD for balances
    this.linechart.switchDataProvider();
    setTimeout(() => {
      this.drawNowLineAndLiquidityZones(null, true);
    }, 0);
    this.refreshAssetsZones(null);
    if (event.component.id == "alignScaleCB") {
      if (this.tabName === "GlobalView") {
        this.callLater('saveProfileSettings', [GroupAnalysis.isIncludeStartOfDay, true]);
      } else {
        this.callLater('saveProfileSettings', [GroupAnalysis.isIncludeStartOfDay, false]);
      }
    }
    this.linechart.callMethodInIframe('alignScaleWithSOD', [this.alignScaleCB.selected])

  }

  /**
 * Creates the now vertical line routine
 **/
  public refreshAssetsZones(evt): void {
    var groupId: string = this.groupCombo.selectedLabel;
    var scenarioId: string = this.scenarioCombo.selectedLabel;
    if (!StringUtils.isEmpty(groupId) && !StringUtils.isEmpty(scenarioId)) {
      this.linechart.drawLiquidityZones(groupId, scenarioId, null, null, this.alignScaleCB.selected, this.showSourceLiquidity);
    }
  }


  public addedFirstTime: boolean = false;


  ngOnDestroy(): void {
    //- unscbscribe all Observables.
    this.subscriptions = this.unsubscribeAllObservables(this.subscriptions);
  }

  /**
   * Update the vertical axis if the the checkbox of update Vertical Axis Min/Max is picked
   **/
  public legendItemChanged(event): void {
    // update the vertical axis minimum/maximum values to ignore the hidden series
    const serie: Series = this.linechart.seriesList.getValue(event.yField);
    if (serie) {
      serie.visible = event.selected;
    }
    if (serie.seriesType == 'line' && StringUtils.isEmpty(serie.minField)) {
      serie.seriesLabel.includeInLayout = event.selected;
      serie.seriesLabel.visible = event.selected;
    }
    this.linechart.callMethodInIframe('showSerie', [event.yField, event.selected])
    if (this.tabName === "GlobalView") {
      this.callLater('saveProfileSettings', [GroupAnalysis.isSelectedLegend, true]);
    } else {
      this.callLater('saveProfileSettings', [GroupAnalysis.isSelectedLegend, false]);
    }
  }
  /**
   * Unsubscribe all Observables Subscriptions
   * It will return an empty array if it all went well
   * @param subscriptions
   */
  unsubscribeAllObservables(subscriptions: Subscription[]): Subscription[] {
    if (Array.isArray(subscriptions)) {
      subscriptions.forEach((subscription: Subscription) => {
        if (subscription && subscription.unsubscribe) {
          subscription.unsubscribe();
        }
      });
      subscriptions = [];
    }

    return subscriptions;
  }


  /**
			 * The result when saving the ilm configuration: withRefresh, 
			 * refreshRate and ranger delimiters
			 * @param event
			 */
  protected saveResult(event): void {
    var xmlReader: JSONReader = new JSONReader();
    xmlReader.setInputJSON(event);
    if (!xmlReader.getRequestReplyStatus())
      this.swtAlert.show(ExternalInterface.call('getBundle', 'text', 'label-contactAdmin', 'Error occurred, Please contact your System Administrator: \n') + xmlReader.getRequestReplyMessage(), ExternalInterface.call('getBundle', 'text', 'alert-error', 'Error'));
    else {
      //FIXME:
      if (xmlReader.getRequestReplyMessage() == "profile_reverted") {
        this.parentDocument.profileContentReverted();
      } else if (this.parentDocument.profileCombo.dataProvider[this.parentDocument.profileCombo.selectedIndex] && this.parentDocument.profileCombo.dataProvider[this.parentDocument.profileCombo.selectedIndex].content.toString().indexOf("*") == -1) {
        this.parentDocument.profileContentChanged();
        if (!this.isLastUsedProfileChanged) {
          if (this.parentDocument.profileCombo.selectedIndex == 0) {
            this.saveProfileSettings("lastUsedProfile", this.tabName === "GlobalView");
          }
          this.isLastUsedProfileChanged = true;
        }
      }
    }
  }


  isLastUsedProfileChanged: boolean = false;
  public saveProfileSettings(param: string, isGlobal: boolean = false, paramValue: string = null): void {
    if (paramValue == null)
      paramValue = '';
    this.ilmConfParams = {};

    this.ilmConfParams["isGeneral"] = "false";
    this.ilmConfParams["entityId"] = this.parentDocument.entityTabName;
    this.ilmConfParams["currencyId"] = this.parentDocument.ccyTabName;
    this.ilmConfParams["selectedTab"] = "groupAnalysis";
    if (param == GroupAnalysis.isGroupGrid || param == GroupAnalysis.isScenarioGrid) {

      for (let i = 0; i < (param == GroupAnalysis.isGroupGrid ? this.groupGrid.dataProvider.length : this.scenarioGrid.dataProvider.length); i++) {
        let xml = (param == GroupAnalysis.isGroupGrid ? this.groupGrid.dataProvider[i] : this.scenarioGrid.dataProvider[i]);
        if (StringUtils.isTrue(xml["use"]))
          paramValue += param == GroupAnalysis.isGroupGrid ? xml.group_id + ";" : xml.scenario + ";";
      }
      if (paramValue.length == 0) {
        paramValue = 'no_selection';
        this.ilmConfParams["paramValue"] = paramValue;
      } else {
        this.ilmConfParams["paramValue"] = paramValue.substr(0, paramValue.length - 1);
      }

      this.ilmConfParams["paramName"] = param == GroupAnalysis.isGroupGrid ? "groupGrid" : "scenarioGrid";

    } else if (param == GroupAnalysis.isGlobalGroupGrid) {
      for (let i = 0; i < this.groupGrid.dataProvider.length; i++) {
        let xml = this.groupGrid.dataProvider[i];
        if (StringUtils.isTrue(xml["use"]))
          paramValue += xml.group_id + ";";
      }
      if (paramValue.length == 0) {
        paramValue = 'no_selection';
        this.ilmConfParams["paramValue"] = paramValue;
      } else {
        this.ilmConfParams["paramValue"] = paramValue.substr(0, paramValue.length - 1);
      }
      this.ilmConfParams["paramName"] = "globalGroupGrid";

    } else if (param == GroupAnalysis.isTree) {
      try {

        this.ilmConfParams["paramName"] = (isGlobal ? "global" : "analysis") + "Tree";
        var array = [];
        this.ilmTree.getCheckedScenarioNodesTree(array, this.ilmTree.dataProvider);
        if (array.length == 0)
          paramValue = 'no_selection';
        else
          paramValue = Encryptor.encode64(array.toString());

        this.ilmConfParams["paramValue"] = paramValue;
      } catch (error) {

      }
    } else if (param == GroupAnalysis.isTimeRange) {
      this.hSliderValues = this.timeRange.values;

      this.ilmConfParams["paramName"] = (isGlobal ? "global" : "analysis") + "TimeRange";
      var arr = [];
      arr.push(parseInt(this.timeRange.values[0]));
      arr.push(parseInt(this.timeRange.values[1]));
      paramValue = Encryptor.encode64(arr.toString());
      this.ilmConfParams["paramValue"] = paramValue;
    } else if (param == GroupAnalysis.isShowEntityScale) {
      this.ilmConfParams["paramName"] = (isGlobal ? "global" : "analysis") + "EntityScale";
      paramValue = "" + this.entityTimeFrameSelected;
      this.ilmConfParams["paramValue"] = paramValue;
    } else if (param == GroupAnalysis.isIncludeStartOfDay) {
      this.ilmConfParams["paramName"] = (isGlobal ? "global" : "analysis") + "IncSod";
      paramValue = "" + this.alignScaleCB.selected;
      this.ilmConfParams["paramValue"] = paramValue;
    } else if (param == GroupAnalysis.isShowActualDataSetOnly) {
      this.ilmConfParams["paramName"] = (isGlobal ? "global" : "analysis") + "ShowDataSetOnly";
      paramValue = "" + this.ShowActualDSOnly.selected;
      this.ilmConfParams["paramValue"] = paramValue;
    } else if (param == GroupAnalysis.isIncludeOpenMvnts) {
      this.ilmConfParams["paramName"] = (isGlobal ? "global" : "analysis") + "IncOpenMvnts";
      paramValue = "" + this.includeOpenMvnts.selected;
      this.ilmConfParams["paramValue"] = paramValue;
    } else if (param == GroupAnalysis.isSumByCutOff) {
      this.ilmConfParams["paramName"] = (isGlobal ? "global" : "analysis") + "SumByCutOff";
      paramValue = "" + this.sumByCutOff.selected;
      this.ilmConfParams["paramValue"] = paramValue;
    } else if (param == GroupAnalysis.isSelectedLegend) {
      this.ilmConfParams["paramName"] = (isGlobal ? "global" : "analysis") + "SelectedLegend";
      paramValue = Encryptor.encode64(this.getInvisibleLegend().toString());
      this.ilmConfParams["paramValue"] = paramValue;
    } else if (param == GroupAnalysis.isLastUsedProfile) {
      this.ilmConfParams["isGeneral"] = "true";
      this.ilmConfParams["paramName"] = param;
      if (this.parentDocument.profileCombo.selectedIndex == 0)
        paramValue = "*";
      else
        paramValue = this.parentDocument.profileCombo.selectedLabel.replace("*", "");

      this.ilmConfParams["paramValue"] = paramValue;
      this.isLastUsedProfileChanged = false;
    } else if (param == GroupAnalysis.isShowSourceLiquidity) {
      this.ilmConfParams["paramName"] = (isGlobal ? "global" : "analysis") + "ShowSourceLiquidity";
      paramValue = "" + this.showSourceLiquidity;
      this.ilmConfParams["paramValue"] = paramValue;

    } else if (param == GroupAnalysis.isGroupSourceLiquidityChanged) {
      this.ilmConfParams["paramName"] = (isGlobal ? "global" : "analysis") + "LastSelectedGroup";
      paramValue = "" + this.groupCombo.selectedLabel;
      this.ilmConfParams["paramValue"] = paramValue;

    } else if (param == GroupAnalysis.isScenarioSourceLiquidityChanged) {
      this.ilmConfParams["paramName"] = (isGlobal ? "global" : "analysis") + "LastSelectedScenario";
      paramValue = "" + this.scenarioCombo.selectedLabel;
      this.ilmConfParams["paramValue"] = paramValue;
    } else if (param == GroupAnalysis.isUncheckedThresholds) {
      this.ilmConfParams["paramName"] = (isGlobal ? "global" : "analysis") + "UncheckedThresholds";
      paramValue = this.uncheckedThresholdItems.toString();
      this.ilmConfParams["paramValue"] = paramValue;
    } else if (param == GroupAnalysis.GRP_LEGEND_DIVIDER_CLOSED) {
      if (this.parentDocument.tabNavigator.selectedIndex == 2)
        return;

      this.ilmConfParams["paramName"] = GroupAnalysis.GRP_LEGEND_DIVIDER_CLOSED;
      this.ilmConfParams["paramValue"] = paramValue;
    }
    else if (param == GroupAnalysis.GRP_GRID_DIVIDER_CLOSED) {
      this.ilmConfParams["paramName"] = GroupAnalysis.GRP_GRID_DIVIDER_CLOSED;
      this.ilmConfParams["paramValue"] = paramValue;
    }
    else if (param == GroupAnalysis.GRP_TREE_DIVIDER_CLOSED) {
      this.ilmConfParams["paramName"] = GroupAnalysis.GRP_TREE_DIVIDER_CLOSED;
      this.ilmConfParams["paramValue"] = paramValue;
    } else if (param == GroupAnalysis.GBL_LEGEND_DIVIDER_CLOSED) {
      if (this.parentDocument.tabNavigator.selectedIndex == 2)
        return;

      this.ilmConfParams["paramName"] = GroupAnalysis.GBL_LEGEND_DIVIDER_CLOSED;
      this.ilmConfParams["paramValue"] = paramValue;
    }
    else if (param == GroupAnalysis.GBL_GRID_DIVIDER_CLOSED) {
      this.ilmConfParams["paramName"] = GroupAnalysis.GBL_GRID_DIVIDER_CLOSED;
      this.ilmConfParams["paramValue"] = paramValue;
    }
    else if (param == GroupAnalysis.GBL_TREE_DIVIDER_CLOSED) {
      this.ilmConfParams["paramName"] = GroupAnalysis.GBL_TREE_DIVIDER_CLOSED;
      this.ilmConfParams["paramValue"] = paramValue;
    }

    this.ilmConfParams["selectedProfile"] = String(this.parentDocument.profileCombo.selectedLabel).replace("*", "");
    this.iLMConfData.url = this.baseURL + this.actionPath + "method=saveLiquidityMonitorConfig&";

    this.resetParamsWhenBusy(this.iLMConfData, this.ilmConfParams);
  }

  //Resend ilm config if the httpCom is busy
  resetParamsWhenBusy(httpCom: HTTPComms, params) {
    if (httpCom.isBusy()) {
      setTimeout(() => {
        this.resetParamsWhenBusy(httpCom, params)
      }, 0);
    } else {
      httpCom.send(params);
    }
  }

  protected getInvisibleLegend() {

    var yFieldCheckbox: string = "";
    var visibleLegends = [];
    for (let i = 0; i < this.linechart.seriesList.getValues().length; i++) {
      let series: Series = this.linechart.seriesList.getValues()[i];
      if (!series.visible) {
        yFieldCheckbox = series.yField;
        if (yFieldCheckbox.indexOf("Thresholds") == -1 && yFieldCheckbox.indexOf('SUM_UN_LIQUID_ASSETS') == -1 && yFieldCheckbox.indexOf('SUM_OTHER_TOTAL') == -1
          && yFieldCheckbox.indexOf('NO_MORE_LIQUIDITY_AVAILABLE') == -1 && yFieldCheckbox.indexOf('SUM_COLLATERAL') == -1
          && yFieldCheckbox.indexOf('SUM_CREDIT_LINE_TOTAL') == -1) {
          visibleLegends.push(yFieldCheckbox);
        }
      }
    }
    return visibleLegends;
  }
  /**
     * 
     **/
  protected inputDataFault(event): void {
    // trace("fault");
  }

  /**
   * 
   **/
  protected startOfComms(): void {
    this.parentDocument.loadingImage.visible = true;
  }

  /**
   * 
   **/
  protected endOfComms(): void {
    if (!this.parentDocument.isHttpComsBusy()){
      this.parentDocument.loadingImage.visible = false;
    }
  }

  public isBusyComms():boolean {
    if(!this.chartsInputData.isBusy() &&  !this.gridsAndTreeInputData.isBusy()){
      return false;
    }else {
      return true;
    }
  }
  /**
   * The result when saving the ilm configuration: withRefresh, 
   * refreshRate and ranger delimiters
   * @param event
   */
  protected saveFault(event): void {
    // this.swtAlert.show(ExternalInterface.call('getBundle', 'text', 'label-lossConnection', 'Unable to save to server\nPossible loss of connection'), ExternalInterface.call('getBundle', 'text', 'alert-error', 'Error'));
    console.log('Unable to save to server\nPossible loss of connection');
  }

  /**
   * The result when saving the ilm configuration: withRefresh, 
   * refreshRate and ranger delimiters
   * @param event
   */
  protected saveBasicProfileResult(event): void {
    var xmlReader: JSONReader = new JSONReader();
    xmlReader.setInputJSON(event);
    if (!xmlReader.getRequestReplyStatus())
      this.swtAlert.show(ExternalInterface.call('getBundle', 'text', 'label-contactAdmin', 'Error occurred, Please contact your System Administrator: \n') + xmlReader.getRequestReplyMessage(), ExternalInterface.call('getBundle', 'text', 'alert-error', 'Error'));
  }



  /**
     * Left vertical axis formatter
     */
  leftVerticalAxisFormatter(labelValue) {
    var sigDigitDcPlaces = this.smallestLabelSigDigitDcPlaces((1 / (this.parentDocument.parentDocument.ccyMuliplierCB.selected ? this.parentDocument.currencyMutiplierValue : 1)));
    let result = this.commonAxisFormatter(labelValue, sigDigitDcPlaces);
    return result;
  }


  /**
   * Formats a label value, when sigDigitDcPlaces is not NaN or 0, the number of decimal places is forced to sigDigitDcPlaces
   */
  commonAxisFormatter(labelValue, sigDigitDcPlaces) {
    let formattedAmount = "";

    // Apply the currency multiplier
    labelValue = labelValue / (this.parentDocument.parentDocument.ccyMuliplierCB.selected ? this.parentDocument.currencyMutiplierValue : 1);

    if (Math.abs(labelValue) >= 1) {
      if (this.parentDocument.parentDocument.ccyMuliplierCB.selected && this.parentDocument.currencyMutiplierValue != 1) {

        if (this.parentDocument.currencyFormat == 'currencyPat2')
          formattedAmount = this.formatMoney(labelValue, (sigDigitDcPlaces == NaN || sigDigitDcPlaces == 0) ? this.parentDocument.currencyDecimalPlaces : sigDigitDcPlaces, ",", ".");
        else {
          formattedAmount = this.formatMoney(labelValue, (sigDigitDcPlaces == NaN || sigDigitDcPlaces == 0) ? this.parentDocument.currencyDecimalPlaces : sigDigitDcPlaces, ".", ",");
        }

      }

      else {
        if (this.parentDocument.currencyFormat == 'currencyPat2')
          formattedAmount = this.formatMoney(labelValue, 0, ",", ".");
        else {
          formattedAmount = this.formatMoney(labelValue, 0, ".", ",");
        }

      }

      // format the amount, if sigDigitDcPlaces is not NaN or 0, then number of decimal places will be forced to sigDigitDcPlaces
      this.currencyFormat = this.parentDocument.currencyFormat;

    } else {
      // Format the amount based on the most significant number,eg: 0.00014 ==> is formatted into 0.0001.
      // If sigDigitDcPlaces is not NaN or 0, then number of decimal places will be forced to sigDigitDcPlaces
      sigDigitDcPlaces = sigDigitDcPlaces == NaN || sigDigitDcPlaces == 0 ? 3 : sigDigitDcPlaces;
      formattedAmount = this.getFirstSignificantDigit(labelValue, sigDigitDcPlaces, true, (this.parentDocument.currencyFormat == 'currencyPat2'));
    }
    return formattedAmount;
  }

  /**
   * Returns the decimal places for the most significant digit for the smallet label in the axis
   */
  smallestLabelSigDigitDcPlaces(smallestLabel) {
    var decSeparator = this.currencyFormat == 'currencyPat1' ? "." : ",";
    if (smallestLabel == 0 && smallestLabel == NaN)
      smallestLabel = 0;

    var smallestSigDigits: any = this.getFirstSignificantDigit(smallestLabel, 3, false, (this.currencyFormat == 'currencyPat2'));
    if (smallestSigDigits != 0) {
      var sigDigitDcPlaces = smallestSigDigits.indexOf(decSeparator) != -1 ? (smallestSigDigits.length - 2) : 0;
    }

    return sigDigitDcPlaces;

  }

  /**
   * This function allow getting the significant digit after the decimal
   * When sigDigitDcPlaces is not NaN or 0, the number of decimal places is forced to sigDigitDcPlaces
   */
  getFirstSignificantDigit(number, maxDecimals, forceDecimals, siStyle) {
    try {

      maxDecimals = typeof maxDecimals !== 'undefined' ? maxDecimals : 3;
      forceDecimals = typeof forceDecimals !== 'undefined' ? forceDecimals : false;
      siStyle = typeof siStyle !== 'undefined' ? siStyle : true;


      var i = 0;
      var inc = Math.pow(10, maxDecimals);
      var str = String(Math.round(inc * number) / inc);
      var sep: number;

      if (str != "0") {
        var hasSep = str.indexOf(".") == -1, sep = hasSep ? str.length : str.indexOf(".");
        var ret = (hasSep && !forceDecimals ? "" : (siStyle ? "," : ".")) + str.substr(sep + 1);
        if (forceDecimals) {
          for (var j = 0; j <= maxDecimals - (str.length - (hasSep ? sep - 1 : sep)); j++) ret += "0";
        }

        while (i + 3 < (str.substr(0, 1) == "-" ? sep - 1 : sep)) ret = (siStyle ? "." : ",") + str.substr(sep - (i += 3), 3) + ret;

        return str.substr(0, sep - i) + ret;
      } else {
        return '0';
      }

    } catch (err) {
      console.log(err.message);
      console.log(err.stack)
    }

  }



  public changeDatasetsInTree(event): void {
    let tmpArr = [];

    var inActualDataSet = [];
    var unselectedItemsFromLegends = [];
    var visibleitemsInTheTree = [];
    var xml = [...this.ilmTree.dataProvider];
    if (this.ShowActualDSOnly.selected) {
      this.ilmTree.showActualDatasetsOnly(xml, inActualDataSet);



      this.linechart.stopUpdateLegend = true;
      for (let index = 0; index < inActualDataSet.length; index++) {
        const yfield = inActualDataSet[index];
        this.linechart.removeSeries(yfield);
      }
      this.linechart.stopUpdateLegend = false;
      this.linechart.updateAreaLegend()
      this.linechart.updateLineLegend();

    } else {
      this.setActualFlag(xml);
    }
    this.ilmTree.dataProvider = xml;
    //FIXME: ALL this method check comments checn if working!!
    visibleitemsInTheTree = this.ilmTree.getVisibleYFields();
    this.linechart.stopUpdateLegend = true;
    for (let index = 0; index < visibleitemsInTheTree.length; index++) {
      const yField = visibleitemsInTheTree[index];
      this.linechart.addChart(yField, true);
    }

    this.linechart.stopUpdateLegend = false;
    this.linechart.updateAreaLegend()
    this.linechart.updateLineLegend();

    var groupId: string = this.groupCombo.selectedLabel;
    var scenarioId: string = this.scenarioCombo.selectedLabel;
    if (groupId != this.lastSelectedGroup || scenarioId != this.lastSelectedScenario)
      // setTimeout(() => {
      this.linechart.drawLiquidityZones(groupId, scenarioId, this.lastSelectedGroup, this.lastSelectedScenario, this.alignScaleCB.selected, this.showSourceLiquidity);
    // }, 0);
    else {
      // setTimeout(() => {
      this.linechart.drawLiquidityZones(groupId, scenarioId, null, null, this.alignScaleCB.selected, this.showSourceLiquidity)
      // }, 0);
    }

    unselectedItemsFromLegends = this.balanceLegend.getUncheckedLegends();
    const unselectedAccumulatedDCLegend = this.AccumulatedDCLegend.getUncheckedLegends();
    for (let k = 0; k < unselectedAccumulatedDCLegend.length; k++) {
      unselectedItemsFromLegends.push(unselectedAccumulatedDCLegend[k]);
    }

    for (let a = 0; a < unselectedItemsFromLegends.length; a++) {
      if (visibleitemsInTheTree.indexOf(unselectedItemsFromLegends[a]) != -1)
        visibleitemsInTheTree.splice(visibleitemsInTheTree.indexOf(unselectedItemsFromLegends[a]), 1);
    }

    if (event != null) {
      this.linechart.callMethodInIframe('showHideActualDataSet', [this.ShowActualDSOnly.selected, visibleitemsInTheTree]);
      if (this.tabName === "GlobalView") {
        this.callLater('saveProfileSettings', [GroupAnalysis.isShowActualDataSetOnly, true]);
      } else {
        this.callLater('saveProfileSettings', [GroupAnalysis.isShowActualDataSetOnly, false]);
      }

    }
  }


  //FIXME:
  public gridsContentItemRender(raw: any, dataField: string, value: string, type: string): string {
    let tmpValue = "";
    try {
      if (dataField === "thresholds") {


        var originalValue: string = "" + value;
        // If the field is threshold
        if (originalValue.search("Min1") != -1) {
          var thresholdsValue = originalValue.split(";");
          // If the value of the threshold min/max 1/2 is null, then Do not include it in the field
          if (thresholdsValue[0] != "Min1:") {
            // var min1:String = FlexGlobals.topLevelApplication.groupAnalysis.linechart.leftVerticalAxisFormatter(Number(thresholdsValue[0].split(":")[1]));
            let min1: String = this.leftVerticalAxisFormatter(Number(thresholdsValue[0].split(":")[1]));
            tmpValue += "<b>Min1:</b>" + min1;
            // labelTooltip += "Min1:" + min1;

          }

          if (thresholdsValue[1] != " Min2:") {
            // var min2:String = FlexGlobals.topLevelApplication.groupAnalysis.linechart.leftVerticalAxisFormatter(Number(thresholdsValue[1].split(":")[1]));
            var min2: String = this.leftVerticalAxisFormatter(Number(thresholdsValue[1].split(":")[1]));
            tmpValue += "<b> Min2:</b>" + min2;
            // labelTooltip += "\nMin2:" + min2;
          }
          if (thresholdsValue[2] != " Max1:") {
            // var max1:String = FlexGlobals.topLevelApplication.groupAnalysis.linechart.leftVerticalAxisFormatter(Number(thresholdsValue[2].split(":")[1]));
            var max1: String = this.leftVerticalAxisFormatter(Number(thresholdsValue[2].split(":")[1]));
            tmpValue += "<b> Max1:</b>" + max1;
            // labelTooltip += "\nMax1:" + max1;
          }
          if (thresholdsValue[3] != " Max2:") {
            var max2: String = this.leftVerticalAxisFormatter(Number(thresholdsValue[3].split(":")[1]));
            tmpValue += "<b> Max2:</b>" + max2;
            // labelTooltip += "\nMax2:" + max2;
          }


        } else {
          tmpValue = this.leftVerticalAxisFormatter(Number(originalValue));
          // labelTooltip = textLabel.htmlText;
          // The horizontal align is right for all the numbers
          // this.setStyle("horizontalAlign", "right");
        }

        // textLabel.truncateToFit = true ;
        // textLabel.toolTip = labelTooltip;

        return tmpValue;
      } else {
        return value;
      }
    } catch (e) {

    }
  }

  formatMoney(n, c, d, t) {

    var c = isNaN(c = Math.abs(c)) ? 2 : c,
      d = d == undefined ? "." : d,
      t = t == undefined ? "," : t,
      s = n < 0 ? "-" : "";

    let i: any = String(parseInt(n = Math.abs(Number(n) || 0).toFixed(c)));
    var j = (j = i.length) > 3 ? j % 3 : 0;

    return s + (j ? i.substr(0, j) + t : "") + i.substr(j).replace(/(\d{3})(?=\d)/g, "$1" + t) + (c ? d + Math.abs(n - i).toFixed(c).slice(2) : "");
  };


  public balanceGridCustomContent(raw: any, dataField: string, value, type: string): string {
    if (type === "num") {
          var originalValue: string = "" + value;
        return this.leftVerticalAxisFormatter(originalValue);
    } else {
      return value;
    }
  }

  roundTo2significantDigits(n) {
    return n.toFixed(1 - Math.floor(Math.log(n) / Math.log(10)));
  }

  public updateValues(currencyMutiplierValue: number, currencyDecimalPlaces: number, currencyFormat: string, useCcyMultiplier: boolean): void {
    this.balanceGrid.refresh();
    this.groupGrid.refresh();
    if (!(this.cd as any).destroyed) {
    this.cd.markForCheck();
  }
  }




  /**
   * This function is used to load the main and the bottom grid on load of the screen.<br>
   *
   * @param event - ResultEvent
   */
  public inputDataResultGrid(event): void {
    try {
      
      // Gets the last received XML
      this.lastRecievedXMLGrid = event.intradayliquidity;
      // Sets the last received XML in the XMLReader
      this.jsonReader.setInputJSON(event);
      // this.listNode = [...this.lastRecievedXMLGrid.tree.copy();
      this.listNode = $.extend(true, [], this.lastRecievedXMLGrid.tree);
      // Test the reply status first, as if the reply status is false then the data will not be valid

      if (this.jsonReader.getRequestReplyStatus()) {
        // // Set the timeframe in the main application
        this.parentDocument.currencyMutiplierValue = Number(this.jsonReader.getScreenAttributes()["currencyMutiplierValue"]);
        // // Set the currency multipier unit next to currency multiplier checkbox ('B', 'M' or 'T')
        this.parentDocument.setCcyMultiplierLabelValue();
        // // Set the currency decimal places value  in the main application
        this.parentDocument.currencyDecimalPlaces = Number(this.jsonReader.getScreenAttributes()["currencyDecimalPlaces"]);

        

        this.parentDocument.valueDate.selectedDate = this.parentDocument.lastSelectedDate =  moment(this.jsonReader.getScreenAttributes()["selectedDateTimeFame"], this.parentDocument.dateFormat.toUpperCase()).toDate();
        this.parentDocument.refreshTestDate(moment(this.jsonReader.getScreenAttributes()["sysDateWithCcyTimeFrame"], this.parentDocument.dateFormat.toUpperCase()).toDate());


        // // Set the ccy multiplier in the main application 
        this.parentDocument.timeframe.text = this.jsonReader.getScreenAttributes()["timeframe"];

        var checkedProfileGroupList: string = (this.jsonReader.getScreenAttributes()["profileGroupGrid"]);
        var checkedProfileScenarioList: string = (this.jsonReader.getScreenAttributes()["profileScenarioGrid"]);

        if (this.groupGrid.gridData.length == 0) {
          this.saveLastCheckedValues = false;
          this.jsonReader.setInputJSON(this.lastRecievedXMLGrid.group);

          this.groupGrid.CustomGrid(this.lastRecievedXMLGrid.group.grid.metadata);
          this.groupGrid.gridData = this.lastRecievedXMLGrid.group.grid.rows;
          setTimeout(() => {
          this.groupGrid.customContentFunction = this.gridsContentItemRender.bind(this);
          }, 500);
          this.groupGrid.parentTabId = "groupsTab";

          // Frames the Top Grid for Interface Monitor
          //FIXME:Check if currency Format and currency decimal places and mulitpiser working in the grid
          // groupGrid=new GroupGrid(xmlReader.getColumnData(), baseURL, this.parentDocument.parentDocument.currencyMutiplierValue,
          // 	this.parentDocument.parentDocument.currencyDecimalPlaces,
          // 	this.parentDocument.parentDocument.ccyMuliplierCB.selected,
          // 	this.parentDocument.currencyFormat);
          // Set the tab name for the group grid
          //FIXME:See what is doing with tabName
          // this.groupGrid.tabName = "group";
          // // Sorts the Top Grid based on the Interface Id
          // this.groupGrid.gridData=this.jsonReader.getGridData();
          // // Adds the Main grid in the Canvas
          // this.groupGridContainer.addElement(this.groupGrid);
          //FIXME: fix event
          // // Add the listener to the group grid, when changing the status of any use checkbox, we have to amend the tree
          // this.groupGrid.addEventListener(SwtCheckboxEvent.STATUS_CHANGE, setTreeData);	
          //FIXME:CHECK IF the event is recreated again and again each refresh !!!!!!!!!!!!!
          this.groupGrid.ITEM_CLICK.subscribe((selectedRowData) => {
            this.setTreeData(selectedRowData, "GroupGrid");
            this.onGridCellClick()
          });
          this.groupGrid.keyup.subscribe(() => {
            this.onGridCellClick()
          });
          // this.groupGrid.addEventListener(ListEvent.ITEM_CLICK, onGridCellClick);
        } else {
          if (!this.resetScreenProfile) {
            this.saveLastCheckedValues = true;
            this.hSliderValues = this.timeRange.values;
          }
        }
        let listOfCheckedScenarioItems = []
        let listOfCheckedGroupItems = []
        if (this.saveLastCheckedValues) {

          for (let i = 0; i < this.groupGrid.dataProvider.length; i++) {
            let xml = this.groupGrid.dataProvider[i];
            JSONReader.jsonpath(this.lastRecievedXMLGrid, '$..group.grid[*]..row[?(@.group_id.content=="' + xml.group_id + '")]').forEach(function (value: Object) {
              value["use"].content = xml["use"];
            });
          }

        } else if (checkedProfileGroupList.length > 0) {

          for (let i = 0; i < this.lastRecievedXMLGrid.group.grid.rows.row.length; i++) {
            this.lastRecievedXMLGrid.group.grid.rows.row[i]["use"].content = "false";
          }
          for (let i = 0; i < this.lastRecievedXMLGrid.balance.grid.rows.row.length; i++) {
            this.lastRecievedXMLGrid.balance.grid.rows.row[i]["use"].content = "false";
          }
          listOfCheckedGroupItems = checkedProfileGroupList.split(";");
          for (var ii = 0; ii < listOfCheckedGroupItems.length; ii++) {

            for (let j = 0; j < this.lastRecievedXMLGrid.balance.grid.rows.row.length; j++) {

              if (listOfCheckedGroupItems[ii] == this.lastRecievedXMLGrid.balance.grid.rows.row[j]["group_id"].content) {
                this.lastRecievedXMLGrid.balance.grid.rows.row[j]["use"].content = "true";
              }
            }

            for (let j = 0; j < this.lastRecievedXMLGrid.group.grid.rows.row.length; j++) {
              if (listOfCheckedGroupItems[ii] == this.lastRecievedXMLGrid.group.grid.rows.row[j]["group_id"].content)
                this.lastRecievedXMLGrid.group.grid.rows.row[j]["use"].content = "true";
            }
          }
        }else {

          if(this.optionsSelectedGroups){
          

          this.optionsUsedForGroups = true;
          for (let i = 0; i < this.lastRecievedXMLGrid.group.grid.rows.row.length; i++) {
            this.lastRecievedXMLGrid.group.grid.rows.row[i]["use"].content = "false";
            if(this.lastRecievedXMLGrid.group.grid.rows.row[i]["use"].global == "Y"){
              this.globalGroupId = this.lastRecievedXMLGrid.group.grid.rows.row[i]["group_id"].content;
            }
          }
          for (let i = 0; i < this.lastRecievedXMLGrid.balance.grid.rows.row.length; i++) {
            this.lastRecievedXMLGrid.balance.grid.rows.row[i]["use"].content = "false";
          }
          listOfCheckedGroupItems = this.optionsSelectedGroups.split(",");
          for (var ii = 0; ii < listOfCheckedGroupItems.length; ii++) {

            for (let j = 0; j < this.lastRecievedXMLGrid.balance.grid.rows.row.length; j++) {

              if (listOfCheckedGroupItems[ii] == this.lastRecievedXMLGrid.balance.grid.rows.row[j]["group_id"].content) {
                this.lastRecievedXMLGrid.balance.grid.rows.row[j]["use"].content = "true";
              }
            }

            for (let j = 0; j < this.lastRecievedXMLGrid.group.grid.rows.row.length; j++) {
              if (listOfCheckedGroupItems[ii] == this.lastRecievedXMLGrid.group.grid.rows.row[j]["group_id"].content)
                this.lastRecievedXMLGrid.group.grid.rows.row[j]["use"].content = "true";
            }
          }
        }
          
        }
        // // Apply the data to the grid
        this.groupGrid.gridData = this.lastRecievedXMLGrid.group.grid.rows;
        // // Gets the row size for the Group Grid
        if (this.scenarioGrid.gridData.length == 0) {

          this.jsonReader.setInputJSON(this.lastRecievedXMLGrid.scenario);
          // Frames the Top Grid for Interface Monitor
          // this.scenarioGrid=new ScenarioGrid(xmlReader.getColumnData(), baseURL);
          this.scenarioGrid.CustomGrid(this.lastRecievedXMLGrid.scenario.grid.metadata);
          this.scenarioGrid.gridData = this.lastRecievedXMLGrid.scenario.grid.rows;
          // // Set the tab name for the scenario grid
          // this.scenarioGrid.tabName = "group";
          this.scenarioGrid.parentTabId = "scenarioTab";
          // // Sorts the Top Grid based on the Interface Id
          // // Add the listener to the scenario grid, when changing the status of any use checkbox, we have to amend the tree
          this.scenarioGrid.ITEM_CLICK.subscribe((selectedRowData) => {
            this.setTreeData(selectedRowData, "ScenarioGrid");
            this.onGridCellClick();
          });
          this.scenarioGrid.keyup.subscribe(() => {
            this.onGridCellClick()
          });
        }
        if (this.balanceGrid.gridData.length == 0) {

          this.jsonReader.setInputJSON(this.lastRecievedXMLGrid.balance);
          this.balanceGrid.CustomGrid(this.lastRecievedXMLGrid.balance.grid.metadata);

          // Set the tab name for the scenario grid
          //FIXME:CHECK WHAT IS THIS TABNAME
          // balanceGrid.tabName = "group";
          this.balanceGrid.parentTabId = "balancesTab";
          // Sorts the Top Grid based on the Interface Id


          this.balanceGrid.gridData = this.lastRecievedXMLGrid.balance.grid.rows;
          setTimeout(() => {
          this.balanceGrid.customContentFunction = this.balanceGridCustomContent.bind(this);
          }, 500);
          this.balanceGrid.ITEM_CLICK.subscribe((selectedRowData) => {
            this.setTreeData(selectedRowData, "BalanceGrid");
            this.onGridCellClick();
          });
          this.balanceGrid.keyup.subscribe(() => {
            this.onGridCellClick()
          });
        }
        listOfCheckedScenarioItems = [];
        if (this.saveLastCheckedValues) {
          for (let i = 0; i < this.scenarioGrid.dataProvider.length; i++) {
            let xml = this.scenarioGrid.dataProvider[i];
            JSONReader.jsonpath(this.lastRecievedXMLGrid, '$..scenario.grid[*]..row[?(@.scenario.content=="' + xml.scenario + '")]').forEach(function (value: Object) {
              value["use"].content = xml["use"];
            });
          }

        } else if (checkedProfileScenarioList.length > 0) {
          for (let i = 0; i < this.lastRecievedXMLGrid.scenario.grid.rows.row.length; i++) {
            this.lastRecievedXMLGrid.scenario.grid.rows.row[i]["use"].content = "false";
          }

          //lastRecievedXMLGrid.scenario.grid.rows.children()[0]["use"] = "no";
          listOfCheckedScenarioItems = checkedProfileScenarioList.split(";");
          for (let ii = 0; ii < listOfCheckedScenarioItems.length; ii++) {
            for (let j = 0; j < this.lastRecievedXMLGrid.scenario.grid.rows.row.length; j++) {

              if (listOfCheckedScenarioItems[ii] == this.lastRecievedXMLGrid.scenario.grid.rows.row[j]["scenario"].content)
                this.lastRecievedXMLGrid.scenario.grid.rows.row[j]["use"].content = "true";
            }
          }

        }
        listOfCheckedScenarioItems = [];
        if (this.prevRecievedXMLGrid == null || this.lastRecievedXMLGrid.scenario != this.prevRecievedXMLGrid.scenario) {
          // this.jsonReader.setInputJSON(this.lastRecievedXMLGrid.scenario);
          // Apply the data to the grid
          this.scenarioGrid.gridData = this.lastRecievedXMLGrid.scenario.grid.rows;
          // // Gets the row size for the Scenario Grid
        }
        if (this.prevRecievedXMLGrid == null || this.lastRecievedXMLGrid.balance != this.prevRecievedXMLGrid.balance) {
          this.jsonReader.setInputJSON(this.lastRecievedXMLGrid.balance);
          // Apply the data to the grid
          this.balanceGrid.gridData = this.lastRecievedXMLGrid.balance.grid.rows;
          // Gets the row size for the Balance Grid
          this.balanceGrid.setRowSize = this.lastRecievedXMLGrid.balance.grid.rows.size;
        }
        var storedTree: string = this.lastRecievedXMLGrid.profileTreeData;
        if (this.saveLastCheckedValues) {
          for (let j = 0; j < this.ilmTree.dataProvider.length; j++) {
            let xml = this.ilmTree.dataProvider[j];
            // let element = JSONReader.jsonpath(this.lastRecievedXMLGrid, '$..tree.node[?(@.label=="'+xml.label+'")]');
            for (let index = 0; index < this.lastRecievedXMLGrid.tree.node.length; index++) {
              if (xml.label == this.lastRecievedXMLGrid.tree.node[index].label) {
                this.lastRecievedXMLGrid.tree.node[index] = xml;
              }
            }
          }
        }
        else if (storedTree != null && storedTree.length > 0) {
          if(storedTree == "no_selection"){
            storedTree="";
          }
          storedTree = storedTree.replace(/\\u0028/g, '(').replace(/\\u0029/g, ')');
          storedTree = Encryptor.decode64(storedTree);
          this.ilmTree.setCheckedScenarioNodesTree(storedTree, this.lastRecievedXMLGrid.tree.node);
        }else {
          storedTree="";
          if(this.globalGroupId && listOfCheckedGroupItems && listOfCheckedGroupItems.indexOf(this.globalGroupId) > -1 ){
            storedTree+=this.globalGroupId+".Thresholds,"
          }
          for (var ii = 0; ii < listOfCheckedGroupItems.length; ii++) {
            if(!storedTree){
              listOfCheckedGroupItems[ii]+".Thresholds,"
            }
            storedTree+=listOfCheckedGroupItems[ii]+".Standard.ab," + listOfCheckedGroupItems[ii]+".Standard.fbb,";
            this.ilmTree.setCheckedScenarioNodesTree(storedTree, this.lastRecievedXMLGrid.tree.node);
          }
          
        if(!storedTree){

          let groupId;
          for (let i = 0; i < this.lastRecievedXMLGrid.group.grid.rows.row.length; i++) {
            if(this.lastRecievedXMLGrid.group.grid.rows.row[i]["use"].global == "Y"){
              groupId = this.lastRecievedXMLGrid.group.grid.rows.row[i]["group_id"].content;
              break;
            }
        }
            storedTree = "";
            storedTree+=groupId+".Thresholds,"
            storedTree+=groupId+".Standard.ab," + groupId+".Standard.fbb,";
            this.ilmTree.setCheckedScenarioNodesTree(storedTree, this.lastRecievedXMLGrid.tree.node);
        }

        }

        if (this.groupGrid.dataProvider.filter(function (item) { return StringUtils.isTrue(item['use']) }).length == 0) {
          checkedProfileGroupList = "no_selection";
        }
        if (listOfCheckedGroupItems.length > 0 || checkedProfileGroupList == "no_selection") {


          for (let index = 0; index < this.lastRecievedXMLGrid.tree.node.length; index++) {
            let group = this.lastRecievedXMLGrid.tree.node[index];
            if (listOfCheckedGroupItems.indexOf(StringUtils.trim(group.label)) != -1) {
              group.visible = true;
            } else {
              group.visible = false;
            }
          }
        }
        if (checkedProfileScenarioList.length > 0) {


          JSONReader.jsonpath(this.lastRecievedXMLGrid, '$..tree.node[*].node[*]').forEach(function (scenario) {
            var yField: string = scenario.yField ? scenario.yField : "";
            if (checkedProfileScenarioList.indexOf(scenario.label) != -1 || yField.indexOf("Thresholds") != -1) {
              scenario.visible = true;
            } else {
              scenario.visible = false;
            }
          });
        }

        // Set the data provider of the ilm tree 
        this.ilmTree.dataProvider = SwtUtil.convertObjectToArray(this.lastRecievedXMLGrid.tree.node);
        this.checkShowActualDSTree();
        //FIXME: check if needed 
        // this.ilmTree.reOpenSavedState();
        // Sets the previous received xml
        var globalValues = this.groupGrid.dataProvider.map(function (item) { return item.slickgrid_rowcontent.use.global }).join('');

        this.enableDisableEntityTimeFrameLabel(false);
        this.alignScaleCB.enabled = false;
        this.includeOpenMvnts.enabled = false;
        this.sumByCutOff.enabled = false;
        this.sourceLiquidityTop.enabled = false;
        this.styleBtn.enabled = false;
        this.timeRange.enabled = false;
        //FIXME:  clearup series
        // this.linechart.cleanUpSeries(ilmTree.getVisibleYFields());

        if (this.groupGrid.dataProvider.length == 0) {
          // Remove all the series from the chart
          this.linechart.removeAllSeries();
          this.thresholdsLegends.removeAllChildren();
          if (this.parentDocument.tabNavigator.selectedIndex == 1) {
            // Create a dummy horizontal scale
            this.swtAlert.show(ExternalInterface.call('getBundle', 'text', 'nodata', 'No ILM data exists for the selected entity, currency and date.'), ExternalInterface.call('getBundle', 'text', 'alert-warning', 'Warning'));
          }
        }
        else if (globalValues.indexOf("Y") == -1) {
          // 
          this.swtAlert.show(ExternalInterface.call('getBundle', 'text', 'noglobalgroup', 'No global group exists for the selected entity and currency.'), ExternalInterface.call('getBundle', 'text', 'alert-warning', 'Warning'));
        }
        else {
          //Initialize the communication objects
          this.chartsInputData.cbStart = this.startOfComms.bind(this);
          // Calls to stop the Http communication
          this.chartsInputData.cbStop = this.endOfComms.bind(this);
          // Calls the inputDataResult function to load the datagrid
          this.chartsInputData.cbResult = (data) => {
            this.inputDataResult(data);
          };

          this.chartsInputData.cbFault = this.inputDataFault.bind(this);
          this.chartsInputData.encodeURL = false;


          this.actionMethod = "method=getGroupChartsData";
          this.chartsInputData.url = this.baseURL + this.actionPath + this.actionMethod;
        	/* Add a listener event to the chart component when the legend update completes so it can filter 
          lineseries on the legend's dataprovider in [onUpdateLegendComplete]*/
          //FIXME:Check if needed
          // this.linechart.addEventListener(FlexEvent.UPDATE_COMPLETE, onUpdateLinechartComplete);
          this.requestParams = new Array();
          this.requestParams["entityId"] = this.parentDocument.entityTabName;
          this.requestParams["currencyId"] = this.parentDocument.ccyTabName;
          //FIXME:DateUtils.getDateAsString(this.parentDocument.valueDate)
          // this.requestParams["selectedDate"] = '30/09/2008';
          this.requestParams["selectedDate"] = this.parentDocument.valueDate.text
          this.requestParams["useCcyMultiplier"] = "N";//this.parentDocument.parentDocument.ccyMuliplierCB.selected ? "Y" : "N";
          this.requestParams["selectedFigures"] = this.ilmTree.getDisplayedScenarios();
          if (this.defaultProfileSaved == this.noneLabel) {
            this.requestParams["currentProfile"] = "<None>";
          }
          else if (this.onLoadisCalled) {
            
            if(this.parentDocument.profileCombo && this.parentDocument.profileCombo.selectedItem &&  this.parentDocument.profileCombo.selectedItem.content){
              this.requestParams["currentProfile"] = this.parentDocument.profileCombo.selectedItem.content;
            }else {
            this.requestParams["currentProfile"] = "<last>";
            }
            this.onLoadisCalled = false;
          }
          else {
            this.requestParams["currentProfile"] = null;
          }

          //Make initial request
          this.chartsInputData.send(this.requestParams);
          this.prevRecievedXMLGrid = this.lastRecievedXMLGrid;
          this.defaultProfileSaved = "";

        }

      }
      else {
        // Alerts the user if any exceptions occured in the server side
        if (this.parentDocument.tabNavigator.selectedIndex == 1)
          this.swtAlert.show(this.jsonReader.getRequestReplyMessage() + "\n" + this.jsonReader.getRequestReplyLocation(), ExternalInterface.call('getBundle', 'text', 'alert-error', 'Error'));
      }

    } catch (e) {

    }

    const viewPort = $($(this.element.nativeElement).find('.slick-viewport'))[0];
    if (viewPort && viewPort.offsetHeight == 0) {
      setTimeout(() => {
        this.groupGrid.validateNow();
      }, 1000);
    }

  }

  /**
			 * Build elements of the screen such us the chart and slider to define the interval from XML   
			 **/
  protected inputDataResult(event): void {

    var profileTimeRange: string = null;
    var profileSelectedLegend: string = null;
    var profileSelectedLegendAsArray = []
    var showDataSetOnlyProfile: string = null;
    var showSourceLiquidityString: string = null;
    var isAnalysisView: boolean = true;
    if (this.chartsInputData.isBusy())
      this.chartsInputData.cbStop();
    else {


      //Retrieves the XML from ResultEvent
      this.lastRecievedXML = event.intradayliquidity;
      //Pass the XML received to xmlReader
      this.jsonReader.setInputJSON(event);
      //Disabling visibility of lostConnectionText

      this.exportContainer.enabled = true;
      //Check the requst reply status in XML
      if (this.jsonReader.getRequestReplyStatus()) {
        // Condition to check the last received and previous xml to build data
        if ((this.lastRecievedXML != this.prevRecievedXML)) {
          if (this.tabName === "GlobalView") {
            isAnalysisView = false;
          }
          if (this.parentDocument.isCalculationFinished) {
            this.alignScaleCB.enabled = true;
            this.includeOpenMvnts.enabled = true;
            this.sumByCutOff.enabled = true;
            this.sourceLiquidityTop.enabled = true;
            this.timeRange.enabled = true;
            this.styleBtn.enabled = true;
          }
          // Get the enity and currency now time, update the clock if it is already created
          this.nowCurrencyTime = DateUtils.isoToDate(this.lastRecievedXML.nowCurrencyTime);
          this.nowEntityTime = DateUtils.isoToDate(this.lastRecievedXML.nowEntityTime);
          if (isAnalysisView) {
            profileTimeRange = this.lastRecievedXML.analysisTimeRange;
            showDataSetOnlyProfile = this.lastRecievedXML.analysisIsShowActualOnly;
            profileSelectedLegend = this.lastRecievedXML.analysisSelectedLegend;
            showSourceLiquidityString = this.lastRecievedXML.analysisIsShowSourcesLiquidity;
            this.lastSelectedGroup = this.lastRecievedXML.analysisLastSelectedGroup;
            this.lastSelectedScenario = this.lastRecievedXML.analysisLastSelectedScenario;
            this.uncheckedThresholds = this.lastRecievedXML.analysisUncheckedThresholds;
            this.showSourceLiquidity = (showSourceLiquidityString == "true");
            setTimeout(() => {
              this.fillGroupScenarioCombo();
            }, 0);
            //FIXME:
            // this.analysisLegendsBoxWidth  = this.legendsBox.width;
            // this.analysistreeBoxWidth = this.treeBox.width;
            // this.analysisgridBoxHeight = this.gridBox.height;
          }
          else {
            profileTimeRange = this.lastRecievedXML.globalTimeRange;
            showDataSetOnlyProfile = this.lastRecievedXML.globalIsShowActualOnly;
            profileSelectedLegend = this.lastRecievedXML.globalSelectedLegend;
            showSourceLiquidityString = this.lastRecievedXML.globalIsShowSourcesLiquidity;

            this.lastSelectedGroup = this.lastRecievedXML.globalLastSelectedGroup;
            this.lastSelectedScenario = this.lastRecievedXML.globalLastSelectedScenario;

            this.uncheckedThresholds = this.lastRecievedXML.globalUncheckedThresholds;
            this.showSourceLiquidity = StringUtils.isTrue(showSourceLiquidityString)
            setTimeout(() => {
              this.fillGroupScenarioCombo();
            }, 0);
            //FIXME:
            // this.globalLegendsBoxWidth  = this.legendsBox.width;
            // this.globaltreeBoxWidth = this.treeBox.width;
            // this.globalgridBoxHeight = this.gridBox.height;
          }
          /* 	Update the status of all the dividers in the global view tab. 
          It depends on the profile setting saved (open or closed divider) */
          if (this.parentDocument.tabNavigator.selectedIndex != 2) {
            if (isAnalysisView) {
              if (StringUtils.isTrue(this.lastRecievedXML.grpLegendDividerIsClosed)) {
                // this.legendsDivider.resizeDividedBox(null,true,0);
                if(this.parentDocument.tabNavigator.selectedIndex == 1){
                  this.legendsDivider.widthRight = '0';
                }
                this.analysisLegendsBoxWidth = 0;
                this.parentDocument.analysisLegendsBoxWidth = 0;
              }
              else if (this.legendsDivider.widthRight == '0') {
                // this.legendsDivider.resizeDividedBox(null,true,250);
                if(this.parentDocument.tabNavigator.selectedIndex == 1){
                  this.legendsDivider.widthRight = '250';
                }
                this.parentDocument.analysisLegendsBoxWidth = 250;
                this.analysisLegendsBoxWidth = 250;
              } else {
                this.parentDocument.analysisLegendsBoxWidth = 250;
                this.analysisLegendsBoxWidth = 250;
              }

              if (StringUtils.isTrue(this.lastRecievedXML.grpTreeDividerIsClosed)) {
                // treeDivider.closeAll();
                this.treeDivider.widthLeft = '0';
                this.analysistreeBoxWidth = 0;
              }
              //FIXME:TEST if working 
              else if (this.treeDivider.widthLeft == '0') {
                // treeDivider.resetAll();
                if(this.parentDocument.tabNavigator.selectedIndex == 1){
                  this.treeDivider.widthLeft = '270';
                }
                this.analysistreeBoxWidth = 270;
              } else {
                this.analysistreeBoxWidth = 270;
              }

              if (StringUtils.isTrue(this.lastRecievedXML.grpGridDividerIsClosed)) {
                this.gridDivider.heightBottom = '0';
                this.analysisgridBoxHeight = 0;
              }
              //FIXME:TEST if working 
              else if (this.gridDivider.heightBottom == '0') {
                // this.gridDivider.resetAll();
                if(this.parentDocument.tabNavigator.selectedIndex == 1){
                  this.gridDivider.heightBottom = '150';
                }
                this.analysisgridBoxHeight = 150;
              } else {
                this.analysisgridBoxHeight = 150;
              }

            } else {

              if (StringUtils.isTrue(this.lastRecievedXML.gblLegendDividerIsClosed)) {
                // this.legendsDivider.resizeDividedBox(null,true,0);
                if(this.parentDocument.tabNavigator.selectedIndex == 0){
                  this.legendsDivider.widthRight = '0';
                }
                this.globalLegendsBoxWidth = 0;
                this.parentDocument.globalLegendsBoxWidth = 0;
              }
              else if (this.legendsDivider.widthRight == '0'){
                // this.legendsDivider.resizeDividedBox(null,true,250);
                if(this.parentDocument.tabNavigator.selectedIndex == 0){
                  this.legendsDivider.widthRight = '250';
                }
                this.parentDocument.globalLegendsBoxWidth = 250;
                this.globalLegendsBoxWidth = 250;
              } else {
                this.parentDocument.globalLegendsBoxWidth = 250;
                this.globalLegendsBoxWidth = 250;
              }

              if (StringUtils.isTrue(this.lastRecievedXML.gblTreeDividerIsClosed)) {
                // treeDivider.closeAll();
                if(this.parentDocument.tabNavigator.selectedIndex == 0){
                  this.treeDivider.widthLeft = '0';
                }
                this.globaltreeBoxWidth = 0;
              }
              //FIXME:TEST if working 
              else if (this.treeDivider.widthLeft == '0') {
                // treeDivider.resetAll();
                if(this.parentDocument.tabNavigator.selectedIndex == 0){
                  this.treeDivider.widthLeft = '270';
                }
                this.globaltreeBoxWidth = 270;
              } else {
                this.globaltreeBoxWidth = 270;
              }

              if (StringUtils.isTrue(this.lastRecievedXML.gblGridDividerIsClosed)) {
                if(this.parentDocument.tabNavigator.selectedIndex == 0){
                  this.gridDivider.heightBottom = '0';
                }
                this.globalgridBoxHeight = 0;
              }
              //FIXME:TEST if working 
              else if (this.gridDivider.heightBottom == '0') {
                // this.gridDivider.resetAll();
                if(this.parentDocument.tabNavigator.selectedIndex == 0){
                  this.gridDivider.heightBottom = '150';
                }
                this.globalgridBoxHeight = 150;
              } else {
                this.globalgridBoxHeight = 150;
              }
            }
          }
          if (profileSelectedLegend != null && profileSelectedLegend.length > 0) {
            profileSelectedLegend = Encryptor.decode64(profileSelectedLegend);
            profileSelectedLegendAsArray = profileSelectedLegend.split(",");
          } else {
            profileSelectedLegendAsArray = new Array;
          }


          var uncheckedThresholdFromProfile = [];
          if (this.uncheckedThresholds) {
            uncheckedThresholdFromProfile = this.uncheckedThresholds.split(",");
          }
          if (this.hSliderValues == null) {

            this.ShowActualDSOnly.selected = StringUtils.isTrue(showDataSetOnlyProfile);
            this.checkShowActualDSTree();

            if (profileTimeRange != null && profileTimeRange.length > 0) {
              this.hSliderValues = Encryptor.decode64(profileTimeRange).split(",");
            }

            var incStartOfday = isAnalysisView ? this.lastRecievedXML.analysisIncSod : this.lastRecievedXML.globalIncSod;
            var inclSumByCutOff = isAnalysisView ? this.lastRecievedXML.analysisSumByCutOff : this.lastRecievedXML.globalSumByCutOff;
            var incOpenMvmt = isAnalysisView ? this.lastRecievedXML.analysisIncOpenMvnts : this.lastRecievedXML.globalIncOpenMvnts;
            var showScaleEntityTimeFrame = isAnalysisView ? this.lastRecievedXML.analysisEntityScale : this.lastRecievedXML.globalEntityScale;

            if (StringUtils.isTrue(incStartOfday)) {
              this.alignScaleCB.selected = true;
              this.linechart.includeSOD = true;
            } else {
              this.alignScaleCB.selected = false;
              this.linechart.includeSOD = false;
            }

            if (StringUtils.isTrue(inclSumByCutOff)) {
              this.sumByCutOff.selected = true;
            } else {
              this.sumByCutOff.selected = false;
            }


            if (StringUtils.isTrue(incOpenMvmt)) {
              this.includeOpenMvnts.selected = true;
            } else {
              this.includeOpenMvnts.selected = false;
            }
            if (StringUtils.isTrue(showScaleEntityTimeFrame)) {

              this.entityTimeFrameLabel.text = ExternalInterface.call('getBundle', 'text', 'entity', 'Entity')+ " " + (this.parentDocument.lastRefTime?this.parentDocument.lastRefTime:"");
              this.entityTimeFrameLabel.toolTip = ExternalInterface.call('getBundle', 'text', 'tip-entity', 'Click to show scale in currency timeframe');
              this.entityTimeFrameSelected = true;
            } else {
              
              this.entityTimeFrameLabel.text = ExternalInterface.call('getBundle', 'text', 'currency', 'Currency')+ "[" + (this.parentDocument.timeframe.text?this.parentDocument.timeframe.text:"")+"]";
              this.entityTimeFrameLabel.toolTip = ExternalInterface.call('getBundle', 'text', 'tip-currency', 'Click to show scale in entity timeframe');;
              this.entityTimeFrameSelected = false;
            }
          }

          if (this.parentDocument.entityTabName == 'All') {
            this.enableDisableEntityTimeFrameLabel(false);
          } else {
            this.enableDisableEntityTimeFrameLabel(true);
          }
          this.currencyTimeDiff = this.nowCurrencyTime.getTime() - (new Date().getTime());
          this.entityTimeDiff = this.nowEntityTime.getTime() - (new Date().getTime());

          if (this.clockRefresh) {
            this.clockRefresh.stop();
            //Listener for clock timer 
          }

          this.clockRefresh = new Timer(20000);
          /*Set event listener to dataRefresh*/

          this.clockRefresh.addEventListener("timer", this.drawNowLineAndLiquidityZones.bind(this));
          // 	// start the timer
          this.clockRefresh.start();

          // 	// Update dataprovider and register charts metadata

          this.seriesStylePropertyURL(this.baseURL + this.actionPath);
          //FIXME:CHECK
          // 	//if(prevRecievedXML == null ||  !areEqual(prevVisibleTreeItems,ilmTree.getVisibleYFields()) || (prevRecievedXML.metadatas !=lastRecievedXML.metadatas) || (prevRecievedXML.datasets != lastRecievedXML.datasets)){
          this.linechart.updateMetadatas(this.lastRecievedXML.metadatas);
          this.linechart.updateDataprovider(this.lastRecievedXML.datasets, this.alignScaleCB.selected, this.ilmTree.getVisibleYFields(), profileSelectedLegendAsArray, uncheckedThresholdFromProfile);
          if (!this.alignScaleCB.selected)
            this.linechart.switchDataProvider();
          this.linechart.includeSOD = this.alignScaleCB.selected;

          this.groupCombo.selectedLabel = this.lastSelectedGroup;
          this.scenarioCombo.selectedLabel = this.lastSelectedScenario;

          this.showHideSourceLiquidity(StringUtils.isTrue(showSourceLiquidityString));
//FIXME:
          this.linechart.selectedEntityId = this.parentDocument.entityTabName;
          this.linechart.selectedCurrencyId = this.parentDocument.ccyTabName;
          try {

            let visibleCharts = this.ilmTree.getVisibleYFields();
            this.linechart.stopUpdateLegend = true;
            for (let index = 0; index < visibleCharts.length; index++) {
              const yField = visibleCharts[index];
              if (this.uncheckedItemsInLengend.indexOf(yField) == -1)
                this.linechart.addChart(yField, true);
              else
                this.linechart.addChart(yField, false);
            }
          } catch (e) {

          }


          this.linechart.stopUpdateLegend = false;
          this.linechart.updateAreaLegend()
          this.linechart.updateLineLegend();

          this.timeData = new Array();
          if (this.entityTimeFrameSelected) {
            this.linechart.isEntityTimeframe = true;
            this.timeData = this.linechart.timeRangeArrayEntity;
          } else {
            this.linechart.isEntityTimeframe = false;
            this.timeData = this.linechart.timeRangeArray;
          }
          // 	// Initialize the range that the user could modify the interval shown in the chart

          this.setTimeRange();
          setTimeout(() => {
            this.addThresholdLegend();
          }, 0);
          this.prevVisibleTreeItems = this.ilmTree.getVisibleYFields();
          this.prevRecievedXML = this.lastRecievedXML;
          setTimeout(() => {
            this.refreshSelectedLegendFromProfile(profileSelectedLegendAsArray);

          }, 0);

          	if(isAnalysisView){
          		if(this.saveAnalysisFirstTimeProfile == true && String(this.parentDocument.profileCombo.selectedLabel).indexOf("*") == -1){
          			this.callLater('saveProfileFirstTime');
                this.saveAnalysisFirstTimeProfile = false;
          		}
          	}else {
          		if(this.saveGlobalFirstTimeProfile == true && String(this.parentDocument.profileCombo.selectedLabel).indexOf("*") == -1){
          			this.callLater('saveProfileFirstTime');
          			this.saveGlobalFirstTimeProfile = false;
          		}
          	}

        }
      } else {
        // Display the alert for the user 
        if (this.lastRecievedXML.hasOwnProperty("request_reply"))
          if (this.parentDocument.tabNavigator.selectedIndex == 1)
            this.swtAlert.show(ExternalInterface.call('getBundle', 'text', 'label-errorContactSystemAdmin', 'Error occurred, Please contact your System Administrator: ') + "\n" + this.jsonReader.getRequestReplyMessage() + "\n" + this.jsonReader.getRequestReplyLocation(), ExternalInterface.call('getBundle', 'text', 'alert-warning', 'Warning'));
      }

    }
    //FIXME: closeCancelPopup check usage
    // FlexGlobals.topLevelApplication.closeCancelPopup();
    if (!this.ilmTree.enabled) {
      this.ilmTree.enabled = true;
      $("body").removeClass("wait"); // to add wait cursor
    }

    this.setJSONDataForIFrameCharts();    
    // window.parent.postMessage(['chartCreationCompleteHandler', [this.tabName]], "*");

    this.parentDocument.chartCreationCompleteHandler(this.tabName)
  }



  protected refreshSelectedLegendFromProfile(selectedCheckBoxLegends): void {

    var yFieldCheckbox: String = "";
    for (let i = 0; i < this.linechart.seriesList.getValues().length; i++) {
      let series: Series = this.linechart.seriesList.getValues()[i];
      if (series.seriesType === 'line') {
        yFieldCheckbox = series.yField;
        if (selectedCheckBoxLegends.indexOf(yFieldCheckbox) != -1 && yFieldCheckbox.indexOf("Thresholds") == -1) {
          series.visible = false;
          this.balanceLegend.setCheckBoxLegendSelected(series, false);
          series.seriesLabel.visible = false;
          series.seriesLabel.includeInLayout = false;
        } else if (this.prevVisibleTreeItems.indexOf(yFieldCheckbox) != -1) {
          series.visible = true;
          series.seriesLabel.visible = true;
          series.seriesLabel.includeInLayout = true;
          this.balanceLegend.setCheckBoxLegendSelected(series, true);
        }
      } else if (series.seriesType === 'area') {
        yFieldCheckbox = series.yField;
        if (selectedCheckBoxLegends.indexOf(yFieldCheckbox) != -1 && yFieldCheckbox.indexOf("Thresholds") == -1) {
          series.visible = false;
          this.AccumulatedDCLegend.setCheckBoxLegendSelected(series, false);
        } else if (this.prevVisibleTreeItems.indexOf(yFieldCheckbox) != -1) {
          series.visible = true;
          this.AccumulatedDCLegend.setCheckBoxLegendSelected(series, true);
        }
      }
    }

  }

  public updateData(event): void {
    this.requestParams = [];
    this.requestParams["entityId"] = this.parentDocument.entityTabName;
    this.requestParams["currencyId"] = this.parentDocument.ccyTabName;
    // this.requestParams["selectedDate"] = "30/09/2008"//DateUtils.getDateAsString(this.parentDocument.valueDate);
    this.requestParams["selectedDate"] = this.parentDocument.valueDate.text
    this.requestParams["useCcyMultiplier"] = "N";//ExternalInterface.call('eval', 'useCcyMultiplier');
    this.requestParams["includeOpenMvnts"] = this.includeOpenMvnts.selected ? "true" : "false";
    this.requestParams["currentProfile"] = "";
    this.gridsAndTreeInputData.send(this.requestParams);
    if (event) {
      if (event.component.id == "includeOpenMvnts") {
        if (this.tabName === "GlobalView") {
          this.callLater('saveProfileSettings', [GroupAnalysis.isIncludeOpenMvnts, true]);
        } else {
          this.callLater('saveProfileSettings', [GroupAnalysis.isIncludeOpenMvnts, false]);
        }

      } else {

        if (this.tabName === "GlobalView") {
          this.callLater('saveProfileSettings', [GroupAnalysis.isSumByCutOff, true]);
        } else {
          this.callLater('saveProfileSettings', [GroupAnalysis.isSumByCutOff, false]);
        }
      }
    }
    this.fromIncOpenMvntsOrSumByCut = true;
  }

  protected fromIncOpenMvntsOrSumByCut: boolean = false;

  public setJSONDataForIFrameCharts(): void {
    //setJSONChartsData(forGlobal, data, includeSOD, sourceOfLiqudity, useCcyMulitplier, entityTimeFrameSelected, entityTimeDiff, fromTime, toTime )
    var timeFromAsInt: number;
    var timeToAsInt: number;
    let unselectedItemsFromLegends = [];
    var saveHighligtedCharts: boolean = this.fromIncOpenMvntsOrSumByCut;
    this.fromIncOpenMvntsOrSumByCut = false;
    try {
      if (this.hSliderValues != null) {
        timeFromAsInt = moment(this.timeRange.timeData[this.timeRange.values[0]]).toDate().getTime()
        timeToAsInt = moment(this.timeRange.timeData[this.timeRange.values[1]]).toDate().getTime()

      } else {
        if (this.timeRange.timeData) {
          timeFromAsInt = new Date(this.timeRange.timeData[0]).getTime();
          timeToAsInt = new Date(this.timeRange.timeData[1440]).getTime();
        }
      }
    } catch (err) {
      ExternalInterface.call("console.log", "groupanalysis.setJSONDataForIFrameCharts() err=" + err.getStackTrace());
    }


    


    var nowDate: Date = new Date();
    if (this.entityTimeFrameSelected)
      nowDate.setTime(nowDate.getTime() + this.entityTimeDiff);
    else
      nowDate.setTime(nowDate.getTime() + this.currencyTimeDiff);


    var groupId: string = this.groupCombo.selectedItem.value;
    var scenarioId: string = this.scenarioCombo.selectedItem.value;


    this.linechart.calculateLiquidityZonesLimites(groupId, scenarioId);

    let data = ["[" + this.linechart.JSONDataSODAsString + "]", this.alignScaleCB.selected,
    this.showSourceLiquidity, "[" + this.linechart.JsonLiquditiyZonesNOSOD + "]", this.parentDocument.parentDocument.ccyMuliplierCB.selected, this.entityTimeFrameSelected, timeFromAsInt, timeToAsInt,
    this.ShowActualDSOnly.selected, this.parentDocument.currencyFormat, this.parentDocument.currencyMutiplierValue, this.parentDocument.currencyDecimalPlaces, 'dd/mm/yyyy', saveHighligtedCharts, this.highlightedSeries.toString(), false, unselectedItemsFromLegends];
    if (!this.addedFirstTime || !this.addNewCharts) {
      setTimeout(() => {
        this.linechart.callMethodInIframe('setILMData', [data, false, false, this.tabName]);
      }, 1000);
      if (!this.addedFirstTime) {
        this.addedFirstTime = true;
      }
    } else {
      // ExternalInterface.call("setILMDataForCharts", false, "["+this.linechart.JSONDataSODAsString+"]",this.addNewCharts,  false);	
      this.linechart.callMethodInIframe('setILMData', ["[" + this.linechart.JSONDataSODAsString + "]", this.addNewCharts, false, this.tabName]);
    }


    if (this.addNewCharts) {
      setTimeout(() => {
        this.updateILMSeriesStyle();
      }, 0);
      this.addNewCharts = false;
    }
  }



  public addThresholdLegend(): void {
    var displayedThresholds = this.ilmTree.getDisplayedThresoldsArray();
    var newUncheckedThreshold = [];
    if (this.uncheckedThresholds.length != 0) {
      this.uncheckedThresholdItems = this.uncheckedThresholds.split(",");
    }
    this.thresholdsLegends.removeAllChildren();
    if (displayedThresholds != null) {
      for (let i = 0; i < this.groupGrid.dataProvider.length; i++) {
        let xml = this.groupGrid.dataProvider[i];
        var tmp: string = StringUtils.trim(xml.group_id);
        // var box:HBox = new HBox();
        var box = <HBox>this.thresholdsLegends.addChild(HBox);

        box.id = "thresholdLegend" + tmp;
        box.name = tmp;
        var checkbox = <SwtCheckBox>box.addChild(SwtCheckBox);
        var label = <SwtLabel>box.addChild(SwtLabel);

        checkbox.name = xml.group_id;
        checkbox.change = this.showHideThresholdsEvent.bind(this);

        if (xml.default_legend_text == "I") {
          label.text = xml.group_id;
        } else {
          label.text = xml.group_name;
        }
        checkbox.selected = true;
        label.paddingTop = "3";
        label.fontWeight = "normal";
        box.horizontalGap = '15';
        if (StringUtils.isTrue(xml["use"]) && displayedThresholds.toString().indexOf(tmp) != -1) {
          box.includeInLayout = true;
          box.visible = true;
          checkbox.selected = (this.uncheckedThresholds.indexOf(xml.group_id) == -1);
          if (!checkbox.selected) {
            this.linechart.showHideThreshold(checkbox.name, checkbox.selected);
            newUncheckedThreshold.push(xml.group_id);
          }
        } else {
          box.includeInLayout = false;
          box.visible = false;
        }
      }
    }
    this.hideThresholds();
    this.uncheckedThresholds = newUncheckedThreshold.toString();


  }


  public showHideThresholdsEvent(event): void {
    var checkbox: SwtCheckBox = event.component;
    this.linechart.showHideThreshold(checkbox.name, checkbox.selected);

    if (!checkbox.selected) {
      this.uncheckedThresholdItems.push(checkbox.name);
    } else {
      this.uncheckedThresholdItems.splice(this.uncheckedThresholdItems.indexOf(checkbox.name), 1);
    }

    this.linechart.callMethodInIframe('showHideThreshold', [checkbox.selected, checkbox.name])

    if (this.tabName === "GlobalView") {
      this.callLater('saveProfileSettings', [GroupAnalysis.isUncheckedThresholds, true]);
    } else {
      this.callLater('saveProfileSettings', [GroupAnalysis.isUncheckedThresholds, false]);
    }
  }


  public refreshThresholdLegend(): void {
    try {

      var displayedThresholds = this.ilmTree.getDisplayedThresoldsArray();
      if (displayedThresholds != null) {

        for (var i: number = 0; i < this.thresholdsLegends.getChildren().length; i++) {
          var group = this.thresholdsLegends.getChildAt(i);
          for (let j = 0; j < this.groupGrid.dataProvider.length; j++) {
            let xml = this.groupGrid.dataProvider[j];
            var tmp: string = StringUtils.trim(xml.group_id);
            if (tmp === group.component.name) {
              if (StringUtils.isTrue(xml["use"]) && displayedThresholds.toString().indexOf(tmp) != -1) {
                group.component.includeInLayout = true;
                group.component.visible = true;
              }
              else {
                group.component.includeInLayout = false;
                group.component.visible = false;
                group.component.getChildAt(0).selected = true;

                const index = this.uncheckedThresholdItems.indexOf(tmp);
                this.uncheckedThresholdItems.splice(index, 1);
              }
            }
          }
        }

      }
      setTimeout(() => {
        this.hideThresholds();
      }, 0);

    } catch (error) {

    }

  }


  public hideThresholds(): void {
    var numberOfVisible: number = 0;
    for (var i: number = 0; i < this.thresholdsLegends.getChildren().length; i++) {
      var group = this.thresholdsLegends.getChildAt(i);
      if (group.component.visible == true) {
        numberOfVisible++;
        break;
      }

    }
    if (numberOfVisible == 0) {
      this.thresholdsLegendLabel.visible = false;
      this.thresholdsLegendLabel.includeInLayout = false;
    }
    else {
      this.thresholdsLegendLabel.visible = true;
      this.thresholdsLegendLabel.includeInLayout = true;
    }

    if (!(this.cd as any).destroyed) {
    this.cd.markForCheck();
  }
  }



  /**
   * Init all the properties related to the slider. With this component the user 
   * could select the interval that will be shown in the chart for all the series 
   **/
  protected setTimeRange(): void {
    var yField: string;
    if (this.timeData) {
      // Draw the region of the elapsed time 
      //drawElapsedTimeRegion(slicedTimeData.source[0].Time, vAxisLeft.maximum, now, vAxisLeft.minimum);
      // Init the array that will contains the list of times that we have in the XML
      this.timeRange.timeData = this.timeData;
      // 	// Init the max and min of the slider
      this.timeRange.sliderComp();
      if (this.hSliderValues != null) {

        if (!this.resetZoomBeforeTimeRange)
          this.timeRange.values = this.hSliderValues;


        // this.timeRange.valueUpdate();
        this.applyZoomFromButtons = false;

        // 	// For some cases, the zoom is not well applied, so force it manually
        // 	// SBR: Scenario: When screen first loads with no data group, then changing the entity might bring data. In this case, Zoom is applied at point 00:00,00:01
        if ((this.timeRange.values[1] - this.timeRange.values[0]) == 1) {
          try {
            this.timeRange.values = [0, 1440];
          }
          catch (error) {

          }
        }

        if(this.timeData && this.timeData.length>1){

          this.timeRange.valueDate = this.timeData[0].split(" ")[0];
          this.timeRange.startDate = this.timeData[0].split(" ")[0];
          this.timeRange.endDate = this.timeData[this.timeData.length - 1].split(" ")[0];
        }
      } else {
        // 		// Init slider labels 
        this.timeRange.initTextInputs();
      }
    }
    else {
      this.timeRange.enabled = false;
    }
    this.resetZoomBeforeTimeRange = false;
  }



  fillGroupScenarioCombo() {
    // var selectList:XML =<selects></selects>;
    var selectList = { selects: { select: {} } };
    var selectArray = [];
    // var groupSelect:XML = <select/>;
    var groupSelect = {};
    groupSelect['id'] = "group";

    /* 				lastSelectedGroup = groupCombo.selectedItem;
    lastSelectedScenario = scenarioCombo.selectedItem; */ 
    let optionList = [];
    let groupDataProvider = SwtUtil.convertObjectToArray(this.groupGrid.dataProvider);
    for (let i = 0; i < groupDataProvider.length; i++) {
      let xml = groupDataProvider[i];

      if (StringUtils.isTrue(xml["use"])) {
        const groupId = xml.group_id;
        optionList.push({ value: groupId, selected: this.lastSelectedGroup == groupId ? 1 : 0, content: groupId });
      }
    }

    // Sort groupSelect options alphabetically
    optionList.sort((a, b) => a.value.localeCompare(b.value));

    groupSelect['option'] = optionList;
    selectArray.push(groupSelect);

    let scenarioSelect = {};
    scenarioSelect['id'] = "scenario";

    optionList = [];

    let scenarioDataProvider = SwtUtil.convertObjectToArray(this.scenarioGrid.dataProvider);
    for (let i = 0; i < scenarioDataProvider.length; i++) {
      let xml = scenarioDataProvider[i];

      if (StringUtils.isTrue(xml["use"])) {
        const scenarioId = xml.scenario;
        optionList.push({ value: scenarioId, selected: this.lastSelectedScenario == scenarioId ? 1 : 0, content: scenarioId });
      }
    }

    // Sort scenarioSelect options with "Standard" first, then alphabetically
    optionList.sort((a, b) => {
      if (a.value === "Standard") return -1; // "Standard" comes first
      if (b.value === "Standard") return 1;  // "Standard" comes first
      return a.value.localeCompare(b.value); // Alphabetical order for the rest
    });

    scenarioSelect['option'] = optionList;
    selectArray.push(scenarioSelect);

    selectList.selects.select = selectArray;
    
    this.groupCombo.setComboData(selectList.selects);
    this.scenarioCombo.setComboData(selectList.selects);

    var groupId = this.groupCombo.selectedItem.value;
    var scenarioId = this.scenarioCombo.selectedItem.value;
    //FIXME:CHECK IF TIMEout is needed
    if (groupId != this.lastSelectedGroup || scenarioId != this.lastSelectedScenario)
      // setTimeout(() => {
      this.linechart.drawLiquidityZones(groupId, scenarioId, this.lastSelectedGroup, this.lastSelectedScenario, this.alignScaleCB.selected, this.showSourceLiquidity);
    // }, 0);
    else
      // setTimeout(() => {
      this.linechart.drawLiquidityZones(groupId, scenarioId, null, null, this.alignScaleCB.selected, this.showSourceLiquidity);
    // }, 0);

    if (groupId != this.lastSelectedGroup) {
      this.lastSelectedGroup = groupId;
      if (this.saveAnalysisFirstTimeProfile != true) {

        if (this.tabName === "GlobalView") {
          this.callLater('saveProfileSettings', [GroupAnalysis.isGroupSourceLiquidityChanged, true]);
        } else {
          this.callLater('saveProfileSettings', [GroupAnalysis.isGroupSourceLiquidityChanged, false]);
        }
      }

    }

    if (scenarioId != this.lastSelectedScenario) {
      this.lastSelectedScenario = scenarioId;
      if (this.saveAnalysisFirstTimeProfile != true) {
        if (this.tabName === "GlobalView") {
          this.callLater('saveProfileSettings', [GroupAnalysis.isScenarioSourceLiquidityChanged, true]);
        } else {
          this.callLater('saveProfileSettings', [GroupAnalysis.isScenarioSourceLiquidityChanged, false]);
        }
      }
    }
  }


  protected checkShowActualDSTree() {
    var inActualDataSet = []
    var xml = [...this.ilmTree.dataProvider];
    if (this.ShowActualDSOnly.selected) {
      this.ilmTree.showActualDatasetsOnly(xml, inActualDataSet);
    } else {
      this.setActualFlag(xml);
    }
    this.ilmTree.dataProvider = xml;
  }

  /**
     * This function reset the hideActual attribute after clicking on the showHide Actual balances checkboxes
     * */

  protected setActualFlag(xmlList): void {
    for (let i: number = 0; i < xmlList.length; i++) {
      if (xmlList[i].children && xmlList[i].children.length > 0) {
        // Iterate recursively into the children node if it is a branch 
        this.setActualFlag(xmlList[i].children);
      }
      else {
        if (xmlList[i].hideActual == true) {
          // Reset HideActual attribute to false 
          xmlList[i].hideActual = false;
        }
      }
    }
  }

  public enableDisableEntityTimeFrameLabel(enable: boolean): void {
    this.entityTimeFrameLabel.buttonMode = enable;
    if (!enable) {
      this.entityTimeFrameLabel.color = "#909090";
      this.entityTimeFrameLabel.buttonMode = false;
    } else {
      this.entityTimeFrameLabel.color = "blue";
      this.entityTimeFrameLabel.buttonMode = true;
    }
  }


  public onChangeEntityTimeframe(event): void {
    try {
      if(this.entityTimeFrameLabel.buttonMode){

    
      var timeFromAsInt: number;
      var timeToAsInt: number;
      this.timeData = [];
      if (this.entityTimeFrameLabel.text.indexOf(ExternalInterface.call('getBundle', 'text', 'entity', 'Entity'))>-1) {

        this.entityTimeFrameLabel.text = ExternalInterface.call('getBundle', 'text', 'currency', 'Currency')+ "[" + (this.parentDocument.timeframe.text?this.parentDocument.timeframe.text:"")+"]"
        this.entityTimeFrameLabel.toolTip = ExternalInterface.call('getBundle', 'text', 'tip-currency', 'Click to show scale in entity timeframe');
        this.entityTimeFrameSelected = false;
      }
      else {
        this.entityTimeFrameLabel.text = ExternalInterface.call('getBundle', 'text', 'entity', 'Entity')+ " " + (this.parentDocument.lastRefTime);
        this.entityTimeFrameLabel.toolTip = ExternalInterface.call('getBundle', 'text', 'tip-entity', 'Click to show scale in currency timeframe');
        this.entityTimeFrameSelected = true;
      }

      this.resetZoomBeforeTimeRange = true;
      if (this.entityTimeFrameSelected) {
        this.timeData = this.linechart.timeRangeArrayEntity;
      } else {
        this.timeData = this.linechart.timeRangeArray;
      }
      
      setTimeout(() => {
        this.setTimeRange();
      }, 0);

      if (this.tabName === "GlobalView") {
        this.callLater('saveProfileSettings', [GroupAnalysis.isTimeRange, true]);
        this.callLater('saveProfileSettings', [GroupAnalysis.isShowEntityScale, true]);
      } else {
        this.callLater('saveProfileSettings', [GroupAnalysis.isTimeRange, false]);
        this.callLater('saveProfileSettings', [GroupAnalysis.isShowEntityScale, false]);
      }

      timeFromAsInt = new Date(this.timeRange.timeData[0]).getTime();
      timeToAsInt = new Date(this.timeRange.timeData[1440]).getTime();
      this.linechart.callMethodInIframe('setEntityOrCurrencyTimeFrame', [!this.entityTimeFrameSelected, timeFromAsInt, timeToAsInt])
      setTimeout(() => {

        timeFromAsInt = new Date(this.timeRange.timeData[0]).getTime();
        timeToAsInt = new Date(this.timeRange.timeData[1440]).getTime();

        this.linechart.callMethodInIframe('zoom', ["" + timeFromAsInt, "" + timeToAsInt])
      }, 0);

    }
    } catch (err) {
      console.log(err);
    }
  }

  public initData(): void {
    /*this.parentDocument.entityCombo.enabled = true;
    this.parentDocument.ccyCombo.enabled = true;*/
    this.parentDocument.valueDate.enabled = true;
    // this.parentDocument.ccyMuliplierCB.enabled = true;
    this.resizeTab();
    if (this.isVisible(this.element.nativeElement)) {
      this.linechart.callMethodInIframe('forceDrawChartIfNotDrawn', [])
      
    }
  }

  /**
 * This function reset the selected index of grids when currency 
 * or entity has changed using the comboboxes in Intraday Liquidity
 */
  public resetSelectedIndex(): void {
    if (this.groupGrid != null) {
      this.groupGrid.selectedIndex = -1;
      this.maintainButton.enabled = false;
    }
    if (this.scenarioGrid != null) {
      this.scenarioGrid.selectedIndex = -1;
      this.maintainButton.enabled = false;
    }
  }


  public setTreeData(event, gridName): void {

    let field = event.target.field
    if (field === "use") {
      var eventData: any = event.target;
      eventData.data.isSelected = ("" + eventData.data.use).toLowerCase() == "true";
      //if click come from groupGrid then update balanceGrid with the same action otherwise update groupgrid with
      //same action done in groupGrid


      if (gridName.indexOf("GroupGrid") != -1)
        this.updateGrids(eventData.data, "true");
      else if (gridName.indexOf("BalanceGrid") != -1)
        this.updateGrids(eventData.data, "false");



      if (this.tabName === "GlobalView") {
        this.callLater('saveProfileSettings', [GroupAnalysis.isGlobalGroupGrid]);
      } else {
        if (gridName.indexOf("GroupGrid") != -1)
          this.callLater('saveProfileSettings', [GroupAnalysis.isGroupGrid]);
        else if (gridName.indexOf("ScenarioGrid") != -1)
          this.callLater('saveProfileSettings', [GroupAnalysis.isScenarioGrid]);
      }

      this.ilmTree.showHideGroupScenarioNodes(eventData.data);


      var visibleYFields = this.ilmTree.getVisibleYFields();

      for (let i = 0; i < visibleYFields.length; i++) {
        let yfield = visibleYFields[i];
        this.linechart.addChart(yfield);
      }
      if (eventData.data.isSelected == false) {
        const hiddenSeries = this.ilmTree.checkedGroupScenarioCharts(eventData.data);
        for (let index = 0; index < hiddenSeries.length; index++) {
          const yfield = hiddenSeries[index];
          this.linechart.removeSeries(yfield);
        }

        this.linechart.removeFromDataSet(eventData.data.group_id);
        this.linechart.callMethodInIframe('removeMultipleCharts', [hiddenSeries.toString()])
      }

      this.refreshThresholdLegend();

      if (this.tabName === "GlobalView") {
        this.callLater('saveProfileSettings', [GroupAnalysis.isTree, true]);
      } else {
        this.callLater('saveProfileSettings', [GroupAnalysis.isTree, false]);
      }



      if (this.tabName === "GlobalView") {
        this.callLater('saveProfileSettings', [GroupAnalysis.isSelectedLegend, true]);
      } else {
        this.callLater('saveProfileSettings', [GroupAnalysis.isSelectedLegend, false]);
      }

      setTimeout(() => {
        this.fillGroupScenarioCombo();
      }, 0);
      //FIXME:
      //this.callLater(this.linechart.refreshAxisVsibility, [this.showSourceLiquidity]);
    }
  }

  public updateGrids(currentselectedNode, fromGroupGrid: String): void {
    if (fromGroupGrid == "true") {
      this.balanceGrid.updateRowByCondition('group_id', currentselectedNode.group_id, "use", currentselectedNode.use)
    } else {
      this.groupGrid.updateRowByCondition('group_id', currentselectedNode.group_id, "use", currentselectedNode.use)

    }
  }

  expandOrCollapse(event) {
    if (this.isExpand) {
      this.ilmTree.expandAll();
      this.isExpand = false;
      this.ExpandCollapse.label = ExternalInterface.call('getBundle', 'text', 'collapseall', 'Collapse All')
    } else {
      this.ilmTree.collapseAll();
      this.isExpand = true;
      this.ExpandCollapse.label = ExternalInterface.call('getBundle', 'text', 'expandall', 'Expand All')
    }
  }


  public onGridCellClick(): void {

    if (this.tabNavigator.selectedIndex == 0 && this.groupGrid.selectedIndex != -1) {
      this.maintainButton.enabled = true;
      this.group_id = this.groupGrid.selectedItem.group_id.content;
      this.creator_grp = this.groupGrid.selectedItem.creator.content;
      this.grp_type = this.groupGrid.selectedItem.type.content;
      this.maintainButton.toolTip = ExternalInterface.call('getBundle', 'tip', 'button-maintain-grp', 'Maintain Selected Group');

    }
    else if (this.tabNavigator.selectedIndex == 2 && this.balanceGrid.selectedIndex != -1) {
      this.maintainButton.enabled = true;
      this.group_id = this.balanceGrid.selectedItem.group_id.content;
      let groupGridRow = this.groupGrid.getRowByCondition('group_id', this.group_id)
      this.creator_grp = groupGridRow.creator;
      this.grp_type = groupGridRow.type;
      this.maintainButton.toolTip = ExternalInterface.call('getBundle', 'tip', 'button-maintain-grp', 'Maintain Selected Group');

    }
    else if (this.tabNavigator.selectedIndex == 1 && this.scenarioGrid.selectedIndex != -1) {
      this.maintainButton.toolTip = ExternalInterface.call('getBundle', 'tip', 'button-maintain-scn', 'Maintain Selected Scenario');
      this.scenario_id = this.scenarioGrid.selectedItem.scenario.content;
      if (this.scenario_id.toLocaleLowerCase() != "standard") {
        this.creator_scn = this.scenarioGrid.selectedItem.creator.content;
        this.scn_type = this.scenarioGrid.selectedItem.type.content;
        this.maintainButton.enabled = true;
      } else
        this.maintainButton.enabled = false;
    }
    else
      this.maintainButton.enabled = false;
  }


  private showAllAccumulatedData(xmlList): void {
    // for (var i:int = 0;i < (xmlList as XMLList).length();i++)
    for (let j = 0; j < xmlList.length; j++) {
      let xml = xmlList[j];
      if (xml.children && xml.children.length > 0) {
        // Iterate recursively into the children node if it is a branch 
        this.showAllAccumulatedData(xml.children);
      }
      else {
        if (xml.hideAccumulated == true) {
          // Reset HideActual attribute to false 
          xml.hideAccumulated = false;
          // xml.parent().@hideAccumulated=false;
          xml.parentData.hideAccumulated = false;
        }
      }
    }
  }

  /**
     * Apply highlight for all legends, but only one legend item will be highlightened
     * */
  public highlighLegend(event, fromCallBack: boolean = false): void {
    if (event.highligh) {
      if (this.highlightedSeries.indexOf(event.yField) == -1)
        this.highlightedSeries.push(event.yField);

      if (!fromCallBack) {
        this.linechart.callMethodInIframe('highlightSerie', [event.yField, true])
      }
    }

    else if (!event.highligh) {
      if (this.highlightedSeries.indexOf(event.yField) != -1) {
        this.highlightedSeries.splice(this.highlightedSeries.indexOf(event.yField), 1);
        if (!fromCallBack) {
          this.linechart.callMethodInIframe('highlightSerie', [event.yField, false])
        }
      }
    }
    const serie: Series = this.linechart.seriesList.getValue(event.yField);
    if(serie)
      serie.highlighted = event.highligh;
    this.balanceLegend.highlighTrueFalse(event);
    this.AccumulatedDCLegend.highlighTrueFalse(event);
  }


  public sourceLiquidity_clickHandler(event): void {


    const groupId: string = this.groupCombo.selectedItem.value;
    const scenarioId: string = this.scenarioCombo.selectedItem.value;
    if (!StringUtils.isEmpty(groupId) && !StringUtils.isEmpty(scenarioId)) {
      this.lastSelectedGroup = groupId;
      this.lastSelectedScenario = scenarioId;
      this.showSourceLiquidity = event.component.selected;
      if (!this.showSourceLiquidity) {
        var xml = [...this.ilmTree.dataProvider];
        this.showAllAccumulatedData(xml);
        this.ilmTree.dataProvider = xml;

        //remove source liquidity from chart
        setTimeout(() => {
          this.linechart.removeLiquidityRegion(groupId, scenarioId);

        }, 0);
        //display source liquidity in chart
        setTimeout(() => {
          this.linechart.drawLiquidityZones(groupId, scenarioId, null, null, this.alignScaleCB.selected, false);
        }, 0);
        let visibleCharts = this.ilmTree.getVisibleYFields();
        this.linechart.stopUpdateLegend = true;
        for (let index = 0; index < visibleCharts.length; index++) {
          const yField = visibleCharts[index];
          this.linechart.addChart(yField);
        }
        this.linechart.stopUpdateLegend = false;
        this.linechart.updateAreaLegend()
        this.linechart.updateLineLegend();
      } else {
        var inBalanceDataSet = [];
        //TODO: remove all Accumulated Area
        let xml = this.ilmTree.dataProvider;
        this.ilmTree.showBalanceDatasetsOnly(xml, inBalanceDataSet);
        this.ilmTree.dataProvider = xml;
        this.linechart.stopUpdateLegend = true;
        for (let index = 0; index < inBalanceDataSet.length; index++) {
          const yfield = inBalanceDataSet[index];
          this.linechart.removeSeries(yfield);
        }
        this.linechart.stopUpdateLegend = false;
        this.linechart.updateAreaLegend()
        this.linechart.updateLineLegend();

        //FIXME:CHECK IF TIMEOUT IS NEEDED
        // setTimeout(() => {
        this.linechart.removeLiquidityRegion(this.groupCombo.selectedItem.value, this.scenarioCombo.selectedItem.value);
        // }, 0);
        //display source liquidity in chart
        // setTimeout(() => {
        this.linechart.drawLiquidityZones(groupId, scenarioId, null, null, this.alignScaleCB.selected);
        // }, 0);

        if (this.assetsLegend.dataProvider) {
          setTimeout(() => {
            this.linechart.updateLiquidityZonesLegend();
          }, 0);

        }

      }

    }
    // setTimeout(() => {
    //   this.linechart.alignScale(this.alignScaleCB.selected, this.showSourceLiquidity);  
    // }, 0);


    if (this.tabName === "GlobalView") {
      this.callLater('saveProfileSettings', [GroupAnalysis.isShowSourceLiquidity, true]);
    } else {
      this.callLater('saveProfileSettings', [GroupAnalysis.isShowSourceLiquidity, false]);
    }

  }


  protected previousStateOfShowLiquidity: boolean = false;
  public refreshSourceOfLiquidityForIFrame(isUpdated: boolean = true): void {
    if (this.previousStateOfShowLiquidity == this.showSourceLiquidity && isUpdated == false) {
      return;
    } else {

      var unselectedItemsFromLegends = [];
      var visibleitemsInTheTree = [];
      visibleitemsInTheTree = this.ilmTree.getVisibleYFields();

      unselectedItemsFromLegends = this.balanceLegend.getUncheckedLegends();
      const unselectedAccumulatedDCLegend = this.AccumulatedDCLegend.getUncheckedLegends();
      for (let k = 0; k < unselectedAccumulatedDCLegend.length; k++) {
        unselectedItemsFromLegends.push(unselectedAccumulatedDCLegend[k]);
      }


      for (let a = 0; a < unselectedItemsFromLegends.length; a++) {
        if (visibleitemsInTheTree.indexOf(unselectedItemsFromLegends[a]) != -1)
          visibleitemsInTheTree.splice(visibleitemsInTheTree.indexOf(unselectedItemsFromLegends[a]), 1);
      }

      // ExternalInterface.call("showHideSourcesOfLiquidity", (this.toString().indexOf("globalView")!=-1), this.showSourceLiquidity, isUpdated, "["+this.linechart.JsonLiquditiyZonesNOSOD+"]", visibleitemsInTheTree);

      this.linechart.callMethodInIframe('showHideSourcesOfLiquidity', [this.showSourceLiquidity, isUpdated, "[" + this.linechart.JsonLiquditiyZonesNOSOD + "]", visibleitemsInTheTree])

      this.previousStateOfShowLiquidity = this.showSourceLiquidity;
    }
  }


  public getSourceLiquidity(isGroupChanged: boolean): void {
    var groupId: string = this.groupCombo.selectedItem.content;
    var scenarioId: string = this.scenarioCombo.selectedItem.content;

    if (isGroupChanged){
      if(this.lastSelectedGroup === groupId)
        return;
      this.saveProfileSettings(GroupAnalysis.isGroupSourceLiquidityChanged, this.tabName === "GlobalView");
    }
    else{
      if(this.lastSelectedScenario === scenarioId)
        return;
      this.saveProfileSettings(GroupAnalysis.isScenarioSourceLiquidityChanged, this.tabName === "GlobalView");
    }


    this.linechart.drawLiquidityZones(groupId, scenarioId, this.lastSelectedGroup, this.lastSelectedScenario, this.alignScaleCB.selected, this.showSourceLiquidity);
    this.lastSelectedGroup = groupId;
    this.lastSelectedScenario = scenarioId;

  }



  public showHideSourceLiquidity(showLiquidity: boolean): void {
    this.showSourceLiquidity = this.sourceLiquidityTop.selected = showLiquidity;
    if (showLiquidity) {
      var inBalanceDataSet = [];
      //TODO:FIXME: remove all Accumulated Area(check)
      var xml = [...this.ilmTree.dataProvider];
      this.ilmTree.showBalanceDatasetsOnly(xml, inBalanceDataSet);
      this.ilmTree.dataProvider = xml;
      for (let index = 0; index < inBalanceDataSet.length; index++) {
        const yfield = inBalanceDataSet[index];
        this.linechart.removeSeries(yfield);
      }
      //display source liquidity in chart
      this.linechart.drawLiquidityZones(this.groupCombo.selectedItem.content, this.scenarioCombo.selectedItem.content, null, null, this.alignScaleCB.selected, true);
      if (this.assetsLegend.dataProvider) {
        this.linechart.updateLiquidityZonesLegend();
      }
    } else {
      // add last param for visiblity as we need to show legend in startup even if no liquidity charts is visible
      this.linechart.drawLiquidityZones(this.groupCombo.selectedItem.content, this.scenarioCombo.selectedItem.content, null, null, this.alignScaleCB.selected, false);
      if (this.assetsLegend.dataProvider) {
        this.linechart.updateLiquidityZonesLegend();
      }
      var xml = [...this.ilmTree.dataProvider];
      this.showAllAccumulatedData(xml);
      this.ilmTree.dataProvider = xml;
      //remove source liquidity from chart
    }
    this.lastSelectedGroup = this.groupCombo.selectedItem.content;
    this.lastSelectedScenario = this.scenarioCombo.selectedItem.content;
  }


  public redrawLiquidityZones(): void {
    var groupId: string = this.groupCombo.selectedLabel;
    var scenarioId: string = this.scenarioCombo.selectedLabel;
    this.linechart.removeLiquidityRegion(groupId, scenarioId);
    this.linechart.drawLiquidityZones(groupId, scenarioId, null, null, this.alignScaleCB.selected, this.showSourceLiquidity);
  }


  setGrpScnStyle() {
    try {
      // var newWindow = window.open("/stopRuleAdd", 'Stop Rule Add', 'height=700,width=960,left=100,top=100,resizable=yes,scrollbars=yes,toolbar=yes,menubar=no,location=no,directories=no, status=yes');
      // if (window.focus) {
      //   newWindow.focus();
      // }
      this.parentDocument.parentDocument.lastTabToOpenSeriesStyle = this.ilmTabId;
      this.parentDocument.parentDocument.lastTabToOpenSeriesStyleIsGlobal = (this.tabName === "GlobalView") ;
      this.setValueStyles();
      ExternalInterface.call('openChildWindow', 'seriesStylePopup');


    } catch (error) {
      // SwtUtil.logError(error, this.moduleId, 'ClassName', 'doViewPCStopRule', this.errorLocation);
    }
  }


  public setValueStyles() {
    this.segmentsLineCharts = [];
    this.areasLineCharts = []

    for (let i = 0; i < this.linechart.seriesList.getValues().length; i++) {
      let series: Series = this.linechart.seriesList.getValues()[i];
      if (series) {
        if ((series.seriesType == 'area')) {

          if (series.legendTooltip) {
            // var style:SeriesStyle = SeriesStyleProvider.getStyle(areaSeries.appliedStyle);
            let areasLineChartsObj = { id: series.yField, label: series.legendDisplayName, value: series.appliedStyle, type: 10 };
            // areasLineChartsObj.id = series.yField;
            // areasLineChartsObj.label = series.legendDisplayName;
            // areasLineChartsObj.value = series.appliedStyle;
            // areasLineChartsObj.type= SeriesStyleProvider.AREA;


            this.areasLineCharts.push(areasLineChartsObj);
          }
        }
        else {
          // var lineSeries:CustomLineSeries = (series as CustomLineSeries);
          if (series.legendTooltip) {
            var type = SeriesStyleProvider.getStyleType(series.appliedStyle);
            var segmentLineChartsObj: Object = {};
            segmentLineChartsObj['id'] = series.yField;
            segmentLineChartsObj['label'] = series.legendDisplayName;
            segmentLineChartsObj['value'] = series.appliedStyle;
            // var type:int = style.type;
            if (type == 2)
              segmentLineChartsObj['type'] = SeriesStyleProvider.LINE_DOTTED;
            else if (type == 3)
              segmentLineChartsObj['type'] = SeriesStyleProvider.LINE_DASHED;
            else
              segmentLineChartsObj['type'] = SeriesStyleProvider.LINE_SOLID;


            this.segmentsLineCharts.push(segmentLineChartsObj);
          }
        }
      }
    }
    // this.linechart.callMethodInIframe('setILMStylesValues', [this.tabName, this.segmentsLineCharts, this.areasLineCharts])
    ExternalInterface.call("setILMStylesValues", this.tabName, this.segmentsLineCharts, this.areasLineCharts);
  }



  public updateILMSeriesStyle(): void {
    var chartsStyles: string = "";
    var seriesStyle: string = "";
    var yField: string = "";
    var selectedTab: String = "";

    if (this.tabName === "GlobalView") {
      selectedTab = "globalView";
    } else
      selectedTab = "groupAnalysis";

    chartsStyles = this.linechart.getChartStyleSeriesStyleAsString();

    var stlyesParams = [];

    if (chartsStyles != "") {
      stlyesParams["paramName"] = "chartsStyles";
      stlyesParams["paramValue"] = chartsStyles;
      stlyesParams["isGeneral"] = "false";
      stlyesParams["entityId"] = this.parentDocument.entityTabName;
      stlyesParams["currencyId"] = this.parentDocument.ccyTabName;
      stlyesParams["selectedTab"] = selectedTab;
      stlyesParams["selectedProfile"] = this.parentDocument.profileCombo.selectedLabel.replace("*", "");
      this.seriesStyleProperty.send(stlyesParams);
    }
  }



  private saveStyleResult(event): void {
    var xmlReader: JSONReader = new JSONReader();
    xmlReader.setInputJSON(event);
    if (!xmlReader.getRequestReplyStatus())
      this.swtAlert.show(ExternalInterface.call('getBundle', 'text', 'label-contactAdmin', 'Error occurred, Please contact your System Administrator: \n') + xmlReader.getRequestReplyMessage(), ExternalInterface.call('getBundle', 'text', 'alert-error', 'Error'));
    else {
      if (xmlReader.getRequestReplyMessage() == "profile_reverted") {
        //FIXME:
        this.parentDocument.profileContentReverted();
      } else if (this.parentDocument.profileCombo.dataProvider[this.parentDocument.profileCombo.selectedIndex].toString().indexOf("*") == -1) {
        this.parentDocument.profileContentChanged();
      }
    }
  }


  private saveStyleFault(event): void {
    // this.swtAlert.show(ExternalInterface.call('getBundle', 'text', 'label-unableSave', 'Unable to save to server\nPossible loss of connection'), ExternalInterface.call('getBundle', 'text', 'alert-error', 'Error'));
    console.log('Unable to save to server\nPossible loss of connection');
  }

  public seriesStylePropertyURL(seriesStylePropertyActionPath: string): void {
    this.seriesStyleProperty.url = seriesStylePropertyActionPath + "method=saveLiquidityMonitorConfig&";
  }




  public saveProfileFirstTime():void {
    var profileParams = [];
    var isGlobal:Boolean = false;
    var paramValue:String = "";
    
    if (this.tabName === "GlobalView") 
      isGlobal = true;
    
      // if (this.tabName === "GlobalView") {


        for (let i = 0; i <  this.groupGrid.dataProvider.length ; i++) {
          let xml =  this.groupGrid.dataProvider[i] ;
          if (StringUtils.isTrue(xml["use"]))
            paramValue +=  xml.group_id + ";" ;
        }

      if(paramValue.length == 0){
        paramValue = 'no_selection';
        profileParams["groupGrid"]= paramValue;
      }else {
        profileParams["groupGrid"]= paramValue.substr(0,paramValue.length-1);
      }
      paramValue = "";
      for (let i = 0; i <  this.scenarioGrid.dataProvider.length ; i++) {
        let xml =  this.scenarioGrid.dataProvider[i] ;
        if (StringUtils.isTrue(xml["use"]))
          paramValue +=  xml.scenario + ";" ;
      }


      if(paramValue.length == 0){
        paramValue = 'no_selection';
        profileParams["scenarioGrid"]= paramValue;
      }else {
        profileParams["scenarioGrid"]= paramValue.substr(0,paramValue.length-1);
      }
    // }
    paramValue = "";
    var array = [];
    this.ilmTree.getCheckedScenarioNodesTree(array, this.ilmTree.dataProvider);
    if(array.length == 0)
      paramValue = '';
    else 
      paramValue = Encryptor.encode64(array.toString());
    if(isGlobal) {
      profileParams["globalTree"]= paramValue;
    }else {
      profileParams["analysisTree"]= paramValue;
    }
    
    paramValue = "";
    var arr = [];
    arr.push(parseInt(this.timeRange.values[0]));
    arr.push(parseInt(this.timeRange.values[1]));
    paramValue = Encryptor.encode64(arr.toString());
    if(isGlobal) {
      profileParams["globalTimeRange"]= paramValue;
    }else {
      profileParams["analysisTimeRange"]= paramValue;
    }
    
    paramValue = "";
    paramValue = Encryptor.encode64(this.getInvisibleLegend().toString());
    profileParams[(isGlobal?"global":"analysis")+"SelectedLegend"]= paramValue;
    
    paramValue = ""+this.groupCombo.selectedLabel;
    if(isGlobal) {
      profileParams["globalLastSelectedGroup"]= paramValue;
    }else {
      profileParams["analysisLastSelectedGroup"]= paramValue;
    }
    
    //lastSelectedGroup = groupCombo.selectedItem;
    
    paramValue = ""+this.scenarioCombo.selectedLabel;
    if(isGlobal) {
      profileParams["globalLastSelectedScenario"]= paramValue;
    }else {
      profileParams["analysisLastSelectedScenario"]= paramValue;
    }
    
    //refreshThresholdLegend();
    paramValue = this.uncheckedThresholdItems.toString();
    if(isGlobal) {
      profileParams["globalUncheckedThresholds"]= paramValue;
    }else {
      profileParams["analysisUncheckedThresholds"]= paramValue;
    }
    //lastSelectedScenario = scenarioCombo.selectedItem;
    
    paramValue = ""+this.linechart.getChartStyleSeriesStyleAsString();
    profileParams["chartsStyles"]= paramValue;
    
    var selectedTab:string = "";
    if(isGlobal) {
      selectedTab = "globalView";
    }else 
      selectedTab = "groupAnalysis";
    
    profileParams["selectedTab"] = selectedTab;
    profileParams["entityId"] = this.parentDocument.entityTabName;
    profileParams["currencyId"] = this.parentDocument.ccyTabName;
    var selectedProfile:string = this.parentDocument.profileCombo.selectedLabel.replace("*","");
    
    if(selectedProfile == this.noneLabel) {
      profileParams["selectedProfile"] = "";
    }else 
      profileParams["selectedProfile"] = selectedProfile;
    
    
    profileParams = this.saveDividersFirstTime(isGlobal, profileParams);
    
    this.saveProfileData.url = this.baseURL + this.actionPath + "method=saveBasicProfile&";
    this.saveProfileData.send(profileParams);
}



protected saveDividersFirstTime(isGlobalTabl:Boolean, profileParams ) {
  if(!isGlobalTabl){
    profileParams[GroupAnalysis.GRP_LEGEND_DIVIDER_CLOSED] = ""+ (this.analysisLegendsBoxWidth == 0);
    profileParams[GroupAnalysis.GRP_TREE_DIVIDER_CLOSED] =""+ (this.analysistreeBoxWidth == 0);
    profileParams[GroupAnalysis.GRP_GRID_DIVIDER_CLOSED] = "" +(this.analysisgridBoxHeight == 0);
  }else {
    profileParams[""+GroupAnalysis.GBL_LEGEND_DIVIDER_CLOSED] = ""+ (this.globalLegendsBoxWidth == 0);
    profileParams[""+GroupAnalysis.GBL_TREE_DIVIDER_CLOSED]= ""+ (this.globaltreeBoxWidth == 0);
    profileParams[""+GroupAnalysis.GBL_GRID_DIVIDER_CLOSED] = "" +(this.globalgridBoxHeight == 0);
  }
  
  return profileParams;
  
}



  public getChartStyleSeriesStyleAsString(): string {
    var chartsStyles: string = "";
    var seriesStyle: string = "";
    var yField: string = "";

    for (let i = 0; i < this.linechart.seriesList.getValues().length; i++) {
      let series: Series = this.linechart.seriesList.getValues()[i];

      if (series.seriesType === 'area') {
        yField = series.yField;
        if (yField.indexOf('SUM_UN_LIQUID_ASSETS') == -1 && yField.indexOf('SUM_OTHER_TOTAL') == -1
          && yField.indexOf('SUM_COLLATERAL') == -1
          && yField.indexOf('SUM_CREDIT_LINE_TOTAL') == -1) {
          seriesStyle = series.appliedStyle;
          chartsStyles = chartsStyles + yField + ":" + seriesStyle + "|";
        }
        //FIXME:CHECK IF CORRECT
      } else if (series.seriesType == 'line' && series.legendTooltip) {
        yField = series.yField;
        if (yField.indexOf('NO_MORE_LIQUIDITY_AVAILABLE') == -1) {
          seriesStyle = series.appliedStyle;
          chartsStyles = chartsStyles + yField + ":" + seriesStyle + "|";
        }
      }
    }

    if (chartsStyles.length > 1)
      chartsStyles = chartsStyles.substr(0, chartsStyles.length - 1);

    return chartsStyles;
  }
  public initScenarioTab(event): void {
    if (this.tabNavigator.selectedIndex == 0) {
      if (this.groupGrid.selectedIndex != -1) {
        this.maintainButton.enabled = true;
      } else {
        this.maintainButton.enabled = false;
      }
    } else if (this.tabNavigator.selectedIndex == 1) {
      // Add the scenario grid in the second bottom tab
      //TODO scenarioGridContainer.addElement(scenarioGrid);
      if (this.scenarioGrid != null && this.scenarioGrid.selectedIndex != -1) {
        this.scenario_id = this.scenarioGrid.selectedItem.scenario.content
        if (this.scenario_id.toLocaleLowerCase() != "standard") {
          this.creator_scn = this.scenarioGrid.selectedItem.creator;
          this.scn_type = this.scenarioGrid.selectedItem.type;
          this.maintainButton.enabled = true;
        } else
          this.maintainButton.enabled = false;
      } else {
        this.maintainButton.enabled = false;
      }
    } else if (this.tabNavigator.selectedIndex == 2) {
      // Add the balance grid in the third bottom tab
      //TODO balanceGridContainer.addElement(balanceGrid);
      if (this.balanceGrid.selectedIndex != -1) {
        this.maintainButton.enabled = true;
      } else {
        this.maintainButton.enabled = false;
      }
    }

  }
  maintainButton_clickHandler(event): void {
    var access: boolean = false;
    if (this.tabNavigator.selectedIndex == 0 || this.tabNavigator.selectedIndex == 2) {
      access = ExternalInterface.call("openILMGroup", this.group_id, this.grp_type, this.creator_grp, this.parentDocument.entityTabName, this.parentDocument.ccyTabName);

    } else if (this.tabNavigator.selectedIndex == 1)
      access = ExternalInterface.call("openILMScenario", this.scenario_id, this.scn_type, this.creator_scn, this.parentDocument.entityTabName, this.parentDocument.ccyTabName);
    if (!access)
      this.swtAlert.show(ExternalInterface.call('eval', 'label[\'text\'][\'enableToMaintain\']'), "Warning");
  }
  closeHandler(event): void {
    ExternalInterface.call("close");
  }
  /**
   * This function is invoked when export button is clicked,
   * used to export the line chart and the data.
   *
   **/
  export(type: string): void {

    try {


      var imageList = [];
      var chartSnapshot: string = null;
      var legendSnapshot: string = null;
      var entityId: string = null;
      var currencyId: string = null;
      var selectedDate: string = null;
      var timeFrame: string = null;

      //reportType  = event.data.toString();


      if (type != "csv") {
        // imageList = linechart.exportAsImageData();
        // chartSnapshot = imageList[0];
        if (this.legendsBox.width == 0) {
          // legendSnapshot = null;
        } else {
          // legendSnapshot = imageList[1];
        }
      }
      if (!this.linechart.seriesList && type != "pdf") {
        this.swtAlert.show(ExternalInterface.call('eval', 'label[\'text\'][\'nodata.forexport\']'), "Warning");
      } else {
        entityId = this.parentDocument.entityTabName;
        currencyId = this.parentDocument.ccyTabName;
        selectedDate = this.parentDocument.valueDate.text;
        timeFrame = this.parentDocument.timeframe.text;
        if (type != "csv" && this.legendsBox.width) {

              htmlToImage.toPng(this.chartsContainer.domElement[0])
              .then( (legendSnapshot) => {
            if (legendSnapshot)
              legendSnapshot = legendSnapshot.split(",")[1];
              
            this.linechart.callMethodInIframe('exportChart', [null, legendSnapshot, null, type, entityId, currencyId, selectedDate, timeFrame])

          });
        } else {
          
          this.linechart.callMethodInIframe('exportChart', [null, legendSnapshot, null, type, entityId, currencyId, selectedDate, timeFrame])
        }

        //Comment to make it called by the HTML page
        //	ExternalInterface.call('onExport',chartSnapshot , legendSnapshot, dpXML!=null?dpXML.toString():"",reportType ,entityId,currencyId,selectedDate,timeFrame);
        // this.linechart.callMethodInIframe('exportChart', [null , legendSnapshot, dpXML!=null?dpXML.toString():"",reportType ,entityId,currencyId,selectedDate,timeFrame])
        // ExternalInterface.call("exportChartAsPDF", (this.toString().indexOf("globalView")!=-1), null , legendSnapshot, dpXML!=null?dpXML.toString():"",reportType ,entityId,currencyId,selectedDate,timeFrame);
        //No need the progressBar any more as the export is quite fast
        //FlexGlobals.topLevelApplication.progresswinPopup();
      }
    } catch (error) {
    console.log("GroupAnalysis -> export -> error", error)

    }
  }





}