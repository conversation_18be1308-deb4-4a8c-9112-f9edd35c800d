import { Component, OnInit } from '@angular/core';
import { GroupAnalysis } from '../GroupAnalysis/GroupAnalysis';
import { SwtCommonGrid, SeriesHighlightEvent, LegendItemChangedEvent, SwtCheckboxEvent, ExternalInterface, CommonLogic, JSONReader, Encryptor, StringUtils, SwtUtil, ExportEvent, AdvancedExportEvent, CommonUtil } from 'swt-tool-box';
import moment from "moment";
@Component({
  selector: 'app-global-view',
  templateUrl: '../GroupAnalysis/GroupAnalysis.html',
  styleUrls: ['./GlobalView.css'],
  host: {
    '(window:resize)': 'resizeTab()'
  }
})
export class GlobalView extends GroupAnalysis implements OnInit {

	public ilmLogic:IlmLogic= new IlmLogic();
  onLoad(newProfile?: boolean) {
    {
      if (!this.groupGrid) {
        this.groupGrid = <SwtCommonGrid>this.groupGridContainer.addChild(SwtCommonGrid);
        this.scenarioGrid = <SwtCommonGrid>this.scenarioGridContainer.addChild(SwtCommonGrid);
        this.balanceGrid = <SwtCommonGrid>this.balanceGridContainer.addChild(SwtCommonGrid);

        this.groupGrid.editable = true;
        this.scenarioGrid.editable = true;
        this.balanceGrid.editable = true;
        this.linechart.parentDocument = this;

        this.linechart.accumulatedDCLegend = this.AccumulatedDCLegend;
        this.linechart.balancesLegend = this.balanceLegend;
        //FIXME:
        this.linechart.assetsLegend = this.assetsLegend;
        this.linechart.accumLabel = this.accumLabel;
        this.linechart.balanceLabel = this.balanceLabel;
        this.linechart.chartValuesContainer = this.chartValuesContainer;
        this.linechart.timeDynamicValue = this.timeDynamicValue;
        
        AdvancedExportEvent.subscribe((event) => {
          // const isSelectedTab =  this.tabName == "GlobalView" && (this.parentDocument.tabNavigator.selectedIndex == 0)

          if (event.id == this.exportContainer.id) {
            
            this.export(event.type)
          }
        });

        this.scenarioGrid.uniqueColumn = "scenario";
        this.groupGrid.uniqueColumn = "group";
  
        this.groupGrid.columnWidthChanged.subscribe((event) => {
          this.updateColumnWidths("groupGrid");
        });
        this.scenarioGrid.columnWidthChanged.subscribe((event) => {
          this.updateColumnWidths("scenarioGrid");
        });
        this.groupGrid.columnOrderChanged.subscribe((event) => {
          this.updateColumnOrder("groupGrid");
        });
        this.scenarioGrid.columnOrderChanged.subscribe((event) => {
          this.updateColumnOrder("scenarioGrid");
        });
        
      }

      setTimeout(() => {
        
      

      // Calls to start the Http communication
      this.gridsAndTreeInputData.cbStart = this.startOfComms.bind(this);
      // Calls to stop the Http communication
      this.gridsAndTreeInputData.cbStop = this.endOfComms.bind(this);
      // Calls the inputDataResult function to load the datagrid
      this.gridsAndTreeInputData.cbResult = (data) => {
        this.inputDataResultGrid(data);
      };

      // Sets the action path for Interface Monitor
      this.actionPath = "ilmAnalysisMonitor.do?";
      // Sets the action method to get the Interface Monitor Details
      this.actionMethod = "method=getGlobalTreeAndGridData";

      // Sets the full URL for Interface Monitor
      this.gridsAndTreeInputData.url = this.baseURL + this.actionPath + this.actionMethod;
      //gridsAndTreeInputData.url = "globalgridandtreedata.xml";
      // Sets the flag for encoding URL to false
      this.gridsAndTreeInputData.encodeURL = false;
      // Calls the inputDataFault function

      this.gridsAndTreeInputData.cbFault = this.inputDataFault;

      this.iLMConfData.cbResult = this.saveResult.bind(this);
      this.iLMConfData.cbFault = this.saveFault.bind(this);
      this.iLMConfData.encodeURL = false;


      this.saveProfileData.cbResult = this.saveBasicProfileResult.bind(this);
      this.saveProfileData.cbFault = this.saveFault.bind(this);
      this.saveProfileData.encodeURL = false;

      // Init the timeData array
      //FIXME:CHECK IF NEEDED


      this.whileWithSleep(this.checkFunc, 300)
      .then(() => {
        this.timeData = [];

      this.requestParams["entityId"] = this.parentDocument.entityTabName;
      this.requestParams["currencyId"] = this.parentDocument.ccyTabName;
      this.requestParams["previousCurrency"] = this.parentDocument.previousCCy;
      this.requestParams["previousEntity"] = this.parentDocument.previousEntity;
      this.requestParams["selectedDate"] = this.parentDocument.valueDate.text;//FIXME:this.parentDocument.valueDate.text;
      this.requestParams["useCcyMultiplier"] = "N";//ExternalInterface.call('eval', 'useCcyMultiplier');
      this.requestParams["currentProfile"] = this.parentDocument.profileCombo.selectedItem.content;


      if (this.parentDocument.profileCombo.selectedItem == this.noneLabel || newProfile) {
        this.requestParams["currentProfile"] = this.parentDocument.profileCombo.selectedItem.content;
        this.defaultProfileSaved = this.parentDocument.profileCombo.selectedItem.content;
      } else {
        this.requestParams["currentProfile"] = "";
        this.defaultProfileSaved = "";
      }
      // Send the request to the server
      this.gridsAndTreeInputData.send(this.requestParams);
      //FIXME:CHEK IF NEEDED
      // // Event listener to highlight the legend with its related series
      // addEventListener(SeriesHighlightEvent.HIGHLIGHT_EVENT, highlighLegend);
      // // Event listener when the user changes the time range via the slider
      // timeRange.addEventListener(ZoomEvent.ZOOMED, applyZoomInChart);
      // // Event listener when changing the status of any checkbox in the tree in order to show/hide the legends and charts
      // ilmTree.addEventListener(SwtCheckboxEvent.STATUS_CHANGE, showHideLegendsAndCharts);
      // Remove the maintain button as we don't have this option in global view tab
      //FIXME:CHEK IF NEEDED
      // if (this.maintainGrp.contains(this.maintainButton))
      //   this.maintainGrp.removeElement(this.maintainButton);
      this.maintainButton.includeInLayout = false;
      this.maintainButton.visible = false;
      // Change the label that will be used for the combined view
      this.labelForCombined.text = ExternalInterface.call('getBundle', 'text', 'globalview', 'Global View');
      // link the ILM tree with the ILM line chart
      // linechart.ilmTree = ilmTree;
      //FIXME:CHEK IF NEEDED
      // Listeners for the dividers of glboal view when clicking the button of the divider in order to save the status
      // legendsDivider.addEventListener(DividerButtonEvent.DIVIDER_BUTTON_CLICK, saveDividerStatusListener);
      // treeDivider.addEventListener(DividerButtonEvent.DIVIDER_BUTTON_CLICK, saveDividerStatusListener);
      // gridDivider.addEventListener(DividerButtonEvent.DIVIDER_BUTTON_CLICK, saveDividerStatusListener);
      // // Listeners for the dividers of global view when clicking the button of the divider in order to save the status
      // legendsDivider.addEventListener(DividerButtonEvent.DIVIDER_DRAG_COMPLETE, saveDividerStatusListener);
      // treeDivider.addEventListener(DividerButtonEvent.DIVIDER_DRAG_COMPLETE, saveDividerStatusListener);
      // gridDivider.addEventListener(DividerButtonEvent.DIVIDER_DRAG_COMPLETE, saveDividerStatusListener);
      this.onLoadisCalled = true;
      
        })
        .catch(error => {
          console.error('An error occurred:', error);
        });
      
    }, 500);
    }
  }

  sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
  
  async whileWithSleep(functionToCheck , sleepDuration) {
    while (this.checkFunc()) {
      // Perform your desired actions inside the loop
      // Sleep for the specified duration
      await this.sleep(sleepDuration);
    }
  }
  checkFunc = () => this.parentDocument.valueDate.text == "" || this.parentDocument.valueDate.text == undefined || this.parentDocument.valueDate.text == null;


  /**
			 * This function is used to load the main and the bottom grid on load of the screen.<br>
			 *
			 * @param event - ResultEvent
			 */
  public inputDataResultGrid(event): void {
    // Gets the last received XML
    this.lastRecievedXMLGrid = event.intradayliquidity;
    // Sets the last received XML in the this.jsonReader
    this.jsonReader.setInputJSON(event);
    this.enableDisableEntityTimeFrameLabel(false);

    this.alignScaleCB.enabled = false;
    this.includeOpenMvnts.enabled = false;
    this.sumByCutOff.enabled = false;
    this.timeRange.enabled = false;
    this.styleBtn.enabled = false;
//FIXME:CHECK IF NEEDED
    // this.ilmTree.saveTreeOpenElements();

    /* exportContainer.enabled=false;
    exportContainer.isEnabled=false; */
    if (this.jsonReader.getRequestReplyStatus()) {

      //update value date 
      //FIXME:moment(this.jsonReader.getScreenAttributes()["valueDate"], this.dateFormat.toUpperCase()).toDate();
      this.parentDocument.valueDate.selectedDate = this.parentDocument.lastSelectedDate =  moment(this.jsonReader.getScreenAttributes()["selectedDateTimeFame"], this.parentDocument.dateFormat.toUpperCase()).toDate();
      

      this.parentDocument.refreshTestDate(moment(this.jsonReader.getScreenAttributes()["sysDateWithCcyTimeFrame"], this.parentDocument.dateFormat.toUpperCase()).toDate());
      this.parentDocument.previousCCy = this.jsonReader.getScreenAttributes()["currencyId"];
      this.parentDocument.previousEntity = this.jsonReader.getScreenAttributes()["entityId"];
      // Set the timeframe in the main application
      //fixme fatmaaa
      this.parentDocument.currencyMutiplierValue = Number(this.jsonReader.getScreenAttributes()["currencyMutiplierValue"]);
      // Set the currency decimal places value  in the main application
      this.parentDocument.currencyDecimalPlaces = Number(this.jsonReader.getScreenAttributes()["currencyDecimalPlaces"]);
      // Update the last refresh value
      //FIXME:CHECK IF NEEDED THE LAST REF TIME
      // this.parentDocument.lastRefTime.text = this.jsonReader.getScreenAttributes()["lastRefTime"];

      var checkedProfileGlobalGroupList: string = (this.jsonReader.getScreenAttributes()["profileGlobalGroupGrid"]);


      // Set the ccy multiplier in the main application 
      this.parentDocument.timeframe.text = this.jsonReader.getScreenAttributes()["timeframe"];
      if (this.groupGrid.gridData.length == 0) {
        this.saveLastCheckedValues = false;
        this.jsonReader.setInputJSON(this.lastRecievedXMLGrid.group);

        this.groupGrid.CustomGrid(this.lastRecievedXMLGrid.group.grid.metadata);
        this.groupGrid.gridData = this.lastRecievedXMLGrid.group.grid.rows;
        setTimeout(() => {
        this.groupGrid.customContentFunction = this.gridsContentItemRender.bind(this);
        }, 500);
        //FIXME:FIX tab inside tab grid need to be refreshd if change tabs
        this.groupGrid.parentTabId = "global";
        this.groupGrid.parentTabId = "groupsTab";
        // Add the listener to the group grid, when changing the status of any use checkbox, we have to amend the tree
        this.groupGrid.ITEM_CLICK.subscribe((selectedRowData) => {
          this.setTreeData(selectedRowData, "GroupGrid");
          this.onGridCellClick();
        });
        this.groupGrid.keyup.subscribe(() => {
          this.onGridCellClick();
        });

      } else {
        if (!this.resetScreenProfile) {
          this.saveLastCheckedValues = true;
          //FIXME:
          this.hSliderValues = this.timeRange.values;
        }
      }
      // var checkedProfileGlobalGroupList: String = (lastRecievedXMLGrid.@profileGlobalGroupGrid);
      var listOfCheckedGroupItems = []
      if (this.saveLastCheckedValues) {
        for (let i = 0; i < this.groupGrid.dataProvider.length; i++) {
          let xml = this.groupGrid.dataProvider[i];
          // JSONReader.jsonpath(this.lastRecievedXMLGrid, '$..group.grid[*]..row[?(@.group_id.content=="'+xml.group_id+'")]').forEach(function (value: Object) {
          JSONReader.jsonpath(this.lastRecievedXMLGrid, '$..group.grid[*]..row[?(@.group_id.content=="' + xml.group_id + '")]').forEach(function (value: Object) {
            value["use"].content = xml["use"];
          });
        }
      } else if (checkedProfileGlobalGroupList.length > 0) {
        for (let i = 0; i < this.lastRecievedXMLGrid.group.grid.rows.row.length; i++) {
          this.lastRecievedXMLGrid.group.grid.rows.row[i]["use"].content = "false";
        }
        for (let i = 0; i < this.lastRecievedXMLGrid.balance.grid.rows.row.length; i++) {
          this.lastRecievedXMLGrid.balance.grid.rows.row[i]["use"].content = "false";
        }

        listOfCheckedGroupItems = checkedProfileGlobalGroupList.split(";");
        for (var ii = 0; ii < listOfCheckedGroupItems.length; ii++) {

          for (let j = 0; j < this.lastRecievedXMLGrid.balance.grid.rows.row.length; j++) {

            if (listOfCheckedGroupItems[ii] == this.lastRecievedXMLGrid.balance.grid.rows.row[j]["group_id"].content) {
              this.lastRecievedXMLGrid.balance.grid.rows.row[j]["use"].content = "true";
            }
          }

          for (let j = 0; j < this.lastRecievedXMLGrid.group.grid.rows.row.length; j++) {
            if (listOfCheckedGroupItems[ii] == this.lastRecievedXMLGrid.group.grid.rows.row[j]["group_id"].content)
              this.lastRecievedXMLGrid.group.grid.rows.row[j]["use"].content = "true";
          }
  }
      }
      //FIXME:CHECK IF WORKING
      if (this.prevRecievedXMLGrid == null || this.lastRecievedXMLGrid.group != this.prevRecievedXMLGrid.group) {
        // // Apply the data to the grid
        this.groupGrid.gridData = this.lastRecievedXMLGrid.group.grid.rows;
        // // Gets the row size for the Group Grid
        //FIXME:CHECK IF WORKING
        // this.groupGrid.setRowSize=this.jsonReader.getRowSize();
      }

      if (this.scenarioGrid.gridData.length == 0) {
        this.scenarioGrid.CustomGrid(this.lastRecievedXMLGrid.scenario.grid.metadata);
        this.scenarioGrid.gridData = this.lastRecievedXMLGrid.scenario.grid.rows;
        // // Set the tab name for the scenario grid
        // this.scenarioGrid.tabName = "group";
        this.scenarioGrid.parentTabId = "scenarioTab";
        // // Sorts the Top Grid based on the Interface Id
        // this.scenarioGrid.gridData=xmlReader.getGridData();
        // // Add the listener to the scenario grid, when changing the status of any use checkbox, we have to amend the tree
        this.scenarioGrid.ITEM_CLICK.subscribe((selectedRowData) => {
          this.setTreeData(selectedRowData, "ScenarioGrid");
          this.onGridCellClick();
        });
        this.scenarioGrid.keyup.subscribe(() => {
          this.onGridCellClick()
        });
      }
      if (this.balanceGrid.gridData.length == 0) {
        this.balanceGrid.CustomGrid(this.lastRecievedXMLGrid.balance.grid.metadata);

        // Set the tab name for the scenario grid
        this.balanceGrid.parentTabId = "balancesTab";
        // Sorts the Top Grid based on the Interface Id
        this.balanceGrid.gridData = this.lastRecievedXMLGrid.balance.grid.rows;
        setTimeout(() => {
        this.balanceGrid.customContentFunction = this.balanceGridCustomContent.bind(this);
        },500);
        //FIXME:
        // this.balanceGrid.addEventListener(SwtCheckboxEvent.STATUS_CHANGE, setTreeData);	
        this.balanceGrid.ITEM_CLICK.subscribe((selectedRowData) => {
          this.setTreeData(selectedRowData, "BalanceGrid");
          this.onGridCellClick();
        });
        this.balanceGrid.keyup.subscribe(() => {
          this.onGridCellClick()
        });
      }
      if (this.prevRecievedXMLGrid == null || this.lastRecievedXMLGrid.scenario != this.prevRecievedXMLGrid.scenario) {
        // this.jsonReader.setInputJSON(this.lastRecievedXMLGrid.scenario);
        // Apply the data to the grid
        this.scenarioGrid.gridData = this.lastRecievedXMLGrid.scenario.grid.rows;
        // // Gets the row size for the Scenario Grid
      }
      if (this.prevRecievedXMLGrid == null || this.lastRecievedXMLGrid.balance != this.prevRecievedXMLGrid.balance) {
        this.jsonReader.setInputJSON(this.lastRecievedXMLGrid.balance);
        // Apply the data to the grid
        this.balanceGrid.gridData = this.lastRecievedXMLGrid.balance.grid.rows;
        // Gets the row size for the Balance Grid
        this.balanceGrid.setRowSize = this.lastRecievedXMLGrid.balance.grid.rows.size;
      }
      //Save tree state if clicking on refresh button
      var storedTree: string = this.lastRecievedXMLGrid.profileTreeData;

             if (this.saveLastCheckedValues) {

          // for each (var xml:XML in ilmTree.dataProvider.children() ) {
          // 	lastRecievedXMLGrid.tree.children().(@label==xml.@label)[0] = xml;
          // }  

          for (let j = 0; j < this.ilmTree.dataProvider.length; j++) {
            let xml = this.ilmTree.dataProvider[j];
            // let element = JSONReader.jsonpath(this.lastRecievedXMLGrid, '$..tree.node[?(@.label=="'+xml.label+'")]');
            for (let index = 0; index < this.lastRecievedXMLGrid.tree.node.length; index++) {
              if (xml.label == this.lastRecievedXMLGrid.tree.node[index].label) {
                this.lastRecievedXMLGrid.tree.node[index] = xml;
              }
            }
          }
        }
        else if (storedTree) {
          try {
            
            storedTree = storedTree.replace(/\\u0028/g, '(').replace(/\\u0029/g, ')');
            storedTree = Encryptor.decode64(storedTree);
            this.ilmTree.setCheckedScenarioNodesTree(storedTree, this.lastRecievedXMLGrid.tree.node);
          } catch (error) {
          }
        }else {
          let groupId;
          for (let i = 0; i < this.lastRecievedXMLGrid.group.grid.rows.row.length; i++) {
            groupId = this.lastRecievedXMLGrid.group.grid.rows.row[i]["group_id"].content ;
            break;
          }
            storedTree = "";
            storedTree+=groupId+".Thresholds,"
            storedTree+=groupId+".Standard.ab," + groupId+".Standard.fbb,";
            this.ilmTree.setCheckedScenarioNodesTree(storedTree, this.lastRecievedXMLGrid.tree.node);
        }
        if (this.groupGrid.dataProvider.filter(function (item) { return StringUtils.isTrue(item['use']) }).length == 0) {
          checkedProfileGlobalGroupList = "no_selection";
        }

        if (listOfCheckedGroupItems.length > 0 || checkedProfileGlobalGroupList == "no_selection") {

          if(this.lastRecievedXMLGrid.tree.node) {
          for (let index = 0; index < this.lastRecievedXMLGrid.tree.node.length; index++) {
            let group = this.lastRecievedXMLGrid.tree.node[index];
            if (listOfCheckedGroupItems.indexOf(StringUtils.trim(group.label)) != -1) {
              group.visible = true;
            } else {
              group.visible = false;
            }
          }
          }
        }
      // Set the data provider of the ilm tree 
      this.ilmTree.dataProvider = SwtUtil.convertObjectToArray(this.lastRecievedXMLGrid.tree.node);

      // Sets the previous received xml
      this.prevRecievedXMLGrid = this.lastRecievedXMLGrid;

      if (this.groupGrid.dataProvider.length == 0) {
        // Remove all the series from the chart
        this.linechart.removeAllSeries();
        if (this.parentDocument.tabNavigator.selectedIndex == 0)
          this.swtAlert.show(ExternalInterface.call('getBundle', 'text', 'nodata', 'No ILM data exists for the selected entity, currency and date.'), ExternalInterface.call('getBundle', 'text', 'alert-warning', 'Warning'));
      } else {
        /* exportContainer.isEnabled = true;
        exportContainer.enabled = true; */

        //Initialize the communication objects
       //Initialize the communication objects
       this.chartsInputData.cbStart = this.startOfComms.bind(this);
       // Calls to stop the Http communication
       this.chartsInputData.cbStop = this.endOfComms.bind(this);
       // Calls the inputDataResult function to load the datagrid
       this.chartsInputData.cbResult = (data) => {
         this.inputDataResult(data);
       };

       this.chartsInputData.cbFault = this.inputDataFault.bind(this);
       this.chartsInputData.encodeURL = false;

       this.actionMethod = "method=getGlobalChartsData";
       this.chartsInputData.url = this.baseURL + this.actionPath + this.actionMethod;
        //chartsInputData.url="chartsdata.xml";

        /* Add a listener event to the chart component when the legend update completes so it can filter 
        lineseries on the legend's dataprovider in [onUpdateLegendComplete]*/
        //FIXME:CHECK IF NEEDED
        // linechart.addEventListener(FlexEvent.UPDATE_COMPLETE, onUpdateLinechartComplete);
       //FIXME:CHECK IF NEEDED
        // var selectedFiguresArray: Array = ilmTree.getGroupScenarioCombinations(XMLList(ilmTree.dataProvider));
        // ExternalInterface.call("console.log", "globalview.inputDataResultGrid(event) selectedFiguresArray" + selectedFiguresArray.toString());
        // var selectedFigures: String = "";

        // if (selectedFiguresArray.length != 0) {
        //   if (selectedFiguresArray.length == 1)
        //     selectedFigures = selectedFiguresArray[0];
        //   else {
        //     for (var i: int = 0; i < selectedFiguresArray.length - 1; i++)
        //       selectedFigures += selectedFiguresArray[i] + "|";
        //     selectedFigures += selectedFiguresArray[selectedFiguresArray.length - 1];
        //   }
        // }
        this.requestParams = new Array();
        this.requestParams["entityId"] = this.parentDocument.entityTabName;
        this.requestParams["currencyId"] = this.parentDocument.ccyTabName;
        this.requestParams["globalChart"] = "true";
        this.requestParams["selectedDate"] = this.parentDocument.valueDate.text
        this.requestParams["useCcyMultiplier"] = "N";//FlexGlobals.topLevelApplication.ccyMuliplierCB.selected ? "Y" : "N";
        this.requestParams["selectedFigures"] = this.ilmTree.getDisplayedScenarios();
        if (this.defaultProfileSaved == this.noneLabel) {
          this.requestParams["currentProfile"] = "<None>";
        }
        else if (this.onLoadisCalled) {
          if(this.parentDocument.profileCombo && this.parentDocument.profileCombo.selectedItem &&  this.parentDocument.profileCombo.selectedItem.content){
            this.requestParams["currentProfile"] = this.parentDocument.profileCombo.selectedItem.content;
          }else {
          this.requestParams["currentProfile"] = "<last>";
          }
          this.onLoadisCalled = false;
          setTimeout(() => {
            this.groupGrid.validateNow()
          }, 0);
        }
        else {
          this.requestParams["currentProfile"] = null;
        }
        // Make initial request
        this.chartsInputData.send(this.requestParams);
        this.defaultProfileSaved = "";
      }
    }
    else {
      // Alerts the user if any exceptions occured in the server side
      if (this.parentDocument.tabNavigator.selectedIndex == 0)
        this.swtAlert.show(ExternalInterface.call('getBundle', 'text', 'label-errorContactSystemAdmin', 'Error occurred, Please contact your System Administrator: ') + "\n" + this.jsonReader.getRequestReplyMessage() + "\n" + this.jsonReader.getRequestReplyLocation(), ExternalInterface.call('getBundle', 'text', 'alert-error', 'Error'));
    }
  }

}

class IlmLogic extends CommonLogic {

}
