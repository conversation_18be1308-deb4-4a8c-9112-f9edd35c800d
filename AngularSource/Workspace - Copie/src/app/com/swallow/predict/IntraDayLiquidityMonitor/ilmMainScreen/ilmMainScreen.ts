import { Component, OnInit, NgModule, ModuleWithProviders, ViewChild, ElementRef } from '@angular/core';
import {
  SwtToolBoxModule,
  SwtTabNavigator,
  Tab,
  ExternalInterface,
  Timer,
  HTTPComms,
  CommonService,
  SwtAlert,
  JSONReader,
  SwtModule,
  SwtLoadingImage,
  SwtButton,
  SwtComboBox,
  SwtTextInput,
  SwtCheckBox,
  SwtLabel, SwtCanvas, SwtDataExport, SwtUtil, SwtTreeCommonGrid, SwtTotalCommonGrid, StringUtils, ExportEvent,  TabPushStategy, TabSelectEvent,TabCloseEvent, AdvancedExportEvent, SwtPopUpManager, EnhancedAlertingTooltip
} from 'swt-tool-box';
import { Routes, RouterModule } from '@angular/router';
import { IntraDayLiquidityMonitor } from '../IntraDayLiquidityMonitor';
import { Observable } from 'rxjs';
import 'rxjs/add/observable/fromEvent';
import { AlertingRenderer } from '../../EnhancedAlerting/Render/AlertingRenderer';
declare var require: any;
const $ = require( 'jquery' );
declare var instanceElement: any;
@Component({
  selector: 'app-ilm',
  templateUrl: './ilmMainScreen.html',
  styleUrls: ['./ilmMainScreen.css']
})
export class ILMMainScreen extends SwtModule implements OnInit {

  @ViewChild('entityLabel') entityLabel: SwtLabel;
  @ViewChild('ccyLabel') ccyLabel: SwtLabel;
  @ViewChild('ccyMultiplierLabel') ccyMultiplierLabel: SwtLabel;
  @ViewChild('entityCombo') entityCombo: SwtComboBox;
  @ViewChild('currencyCombo') currencyCombo: SwtComboBox;
  @ViewChild('refreshCB') refreshCB: SwtCheckBox;
  @ViewChild('ccyMuliplierCB') ccyMuliplierCB: SwtCheckBox;
  @ViewChild('includeSod') includeSod: SwtCheckBox;
  @ViewChild('includecreditLine') includecreditLine: SwtCheckBox;
  @ViewChild('sumCutOff') sumCutOff: SwtCheckBox;
  @ViewChild('includeOpenMvmt') includeOpenMvmt: SwtCheckBox;
  @ViewChild('hideNonSumAccount') hideNonSumAccount: SwtCheckBox;
  @ViewChild('refreshText') refreshText: SwtTextInput;
  @ViewChild('refreshButton') refreshButton: SwtButton;
  @ViewChild('optionButton') optionButton: SwtButton;
  @ViewChild('closeButton') closeButton: SwtButton;
  @ViewChild('loadingImage') loadingImage: SwtLoadingImage;
  @ViewChild('exportContainer') exportContainer: SwtDataExport;
  @ViewChild('tabs') tabNavigator: SwtTabNavigator;
  @ViewChild('summaryTab') summaryTab: TabPushStategy;
  @ViewChild('summaryCanvas') summaryCanvas: SwtCanvas;
  @ViewChild('totalsContainer') totalsContainer: SwtCanvas;

  

  private swtAlert: SwtAlert;
  public inputData = new HTTPComms(this.commonService);
  public alertingData = new HTTPComms(this.commonService);
  
  public refreshTabsData = new HTTPComms(this.commonService);
  public iLMConfData = new HTTPComms(this.commonService);
  private baseURL = SwtUtil.getBaseURL();
  private actionMethod: string = "";
  private actionPath: string = "";
  private requestParams = [];
  private jsonReader: JSONReader = new JSONReader();
  private jsonReaderTabs: JSONReader = new JSONReader();
  private lastRecievedJSON;
  public lastTabToOpenSeriesStyle = null;
  public lastTabToOpenSeriesStyleIsGlobal = null;
  private currencyGrid: SwtTreeCommonGrid;
  private totalsGrid: SwtTotalCommonGrid;
  private menuAccess = 2;
  private currencyPattern: string;
  public tabsConfig = [];
  private ilmConfParams: Object;
  private tabsName = [];
  private tabsOrder = [];
  // private tabsSelectedGroups = [];
//Main Timer.
  private autoRefresh: Timer;
  private refreshRate: number = 10;
  public refValueChanged: boolean = false;
  tooltipEntityId = null;
  tooltipCurrencyCode = null;
  tooltipFacilityId = null;
  tooltipSelectedDate = null;
  tooltipOtherParams = [];
  private positionX:number;
  private positionY:number;
  private hostId;
  private entityId;
  private currencyId;
  private selectedNodeId = null;
  private treeLevelValue = null;
  constructor(private commonService: CommonService, private element: ElementRef) {
    super(element, commonService);
    this.swtAlert = new SwtAlert(commonService);
  }
  public static ngOnDestroy(): any {
    window['Main'] = instanceElement = null;
  }
  ngOnDestroy(): any {
    
  }
  ngOnInit() {
    window['Main'] = instanceElement = this;
    this.entityLabel.text = SwtUtil.getPredictMessage('ilm.entityFilter', null);
    this.entityCombo.toolTip = ExternalInterface.call('getBundle', 'tip', 'entity', 'Select an entity ID');
    this.ccyLabel.text = SwtUtil.getPredictMessage('ilm.ccyFilter', null);
    this.currencyCombo.toolTip = ExternalInterface.call('getBundle', 'tip', 'currency', 'Select currency code');
    this.refreshCB.label = ExternalInterface.call('getBundle', 'text', 'refreshevery', 'Refresh Every');
    this.refreshText.toolTip = ExternalInterface.call('getBundle', 'tip', 'refreshevery', 'Enter Refresh Every');
    this.refreshButton.label = ExternalInterface.call('getBundle', 'text', 'label-refresh', 'Refresh');
    this.ccyMuliplierCB.label = ExternalInterface.call('getBundle', 'text', 'ccymultiplier', 'Use Currency Multiplier');
    this.optionButton.label = SwtUtil.getPredictMessage('button.option', null);
    this.closeButton.label = SwtUtil.getPredictMessage('button.close', null);
  }
  onLoad() {
    this.iLMConfData.cbResult = this.saveResult.bind(this);
    this.iLMConfData.cbFault = this.saveFault.bind(this);
    this.iLMConfData.encodeURL = false;
    this.currencyGrid = this.summaryCanvas.addChild(SwtTreeCommonGrid) as SwtTreeCommonGrid;
    this.currencyGrid.listenHorizontalScrollEvent = true;
    this.currencyGrid.hideHorizontalScrollBar = true;
    this.currencyGrid.lockedColumnCount = 2;
    this.currencyGrid.treeMaxLevel = 3;
    this.totalsGrid = <SwtTotalCommonGrid>this.totalsContainer.addChild(SwtTotalCommonGrid);
    this.totalsGrid.selectable = false;
    this.totalsGrid.fireHorizontalScrollEvent = true;
    this.totalsGrid.lockedColumnCount = 2;
    this.currencyGrid.uniqueColumn = "seqN";
    
    
    
    this.currencyGrid.colWidthURL ( this.baseURL+"ilmAnalysisMonitor.do?screenName=ilmSummaryGrid");
    this.currencyGrid.colOrderURL ( this.baseURL+"ilmAnalysisMonitor.do?screenName=ilmSummaryGrid");
    this.currencyGrid.saveWidths = true;
    this.currencyGrid.saveColumnOrder = true;


    this.currencyGrid.ITEM_CLICK.subscribe((selectedRowData) => {
      this.itemClickFunction(selectedRowData);
      this.cellLogic(selectedRowData);
    });

    this.currencyGrid.columnWidthChanged.subscribe((event) => {
      this.resizeGrids(event);
    });
    this.currencyGrid.columnOrderChanged.subscribe((event) => {
      this.resizeGrids(event);
    });
    Observable.fromEvent(document.body, 'click').subscribe(e => {
      this.positionX=e["clientX"];
      this.positionY=e["clientY"];
    });
    this.inputData.cbStart = this.startOfComms.bind(this);
    this.inputData.cbStop = this.endOfComms.bind(this);
    this.inputData.cbResult = (data) => {
      this.inputDataResult(data);
    };
    this.inputData.cbFault = this.inputDataFault.bind(this);
    this.inputData.encodeURL = false;
    this.requestParams = [];
    this.requestParams['firstLoad'] = true;
    this.actionPath = "ilmAnalysisMonitor.do?";
    this.actionMethod = 'method=ilmSummaryGridDisplay';
    this.inputData.url = this.baseURL + this.actionPath + this.actionMethod;
    this.inputData.send(this.requestParams);
      
    AdvancedExportEvent.subscribe((event) => {
      // const isSelectedTab =  this.tabName == "GlobalView" && (this.parentDocument.tabNavigator.selectedIndex == 0)
      if (event.id == this.exportContainer.id) {
        this.report(event.type)
      }
    });
    
    TabCloseEvent.subscribe((tabId) => {
      if(this.tabNavigator.selectedIndex >= this.tabNavigator.tabChildrenArray.length){
        this.tabNavigator.selectedIndex = 0;
      }
      this.updateILMOptionConfig(tabId)
    });

  }
      
  resizeGrids(event) {
    try {
      // this.totalGrid.gridObj.setColumns(this.accountGrid.columnDefinitions);
    this.totalsGrid.setRefreshColumnWidths(this.currencyGrid.gridObj.getColumns());
    } catch(e) {
      console.log("resizeGrids", e)
  }

  }

  private lastSelectedTooltipParams = null;
  getParamsFromParent() {
    // return this.screenName;
    // const params = { sqlParams: this.lastSelectedTooltipParams, facilityId: this.tooltipFacilityId };

    const params = { sqlParams: this.lastSelectedTooltipParams, facilityId: this.tooltipFacilityId, selectedNodeId:this.selectedNodeId, treeLevelValue:this.treeLevelValue ,
      tooltipCurrencyCode :this.tooltipCurrencyCode , tooltipEntityId:this.tooltipEntityId,tooltipSelectedDate:this.tooltipSelectedDate,tooltipSelectedAccount: null,
      tooltipOtherParams:this.tooltipOtherParams};


    return params;
  }

  private eventsCreated = false;
  private customTooltip: any = null;
  public createTooltip (){
      if(this.customTooltip && this.customTooltip.close)
        this.removeTooltip();
      try {    

      const toolTipWidth = 410;
      this.customTooltip = SwtPopUpManager.createPopUp(parent, EnhancedAlertingTooltip, {
      });

      if (this.positionY>360)
      this.positionY=360;
      this.customTooltip.setWindowXY(this.positionX, this.positionY);
      this.customTooltip.enableResize = false;
      this.customTooltip.width = ''+toolTipWidth;
      this.customTooltip.height = "450";
      this.customTooltip.enableResize = false;
      this.customTooltip.title = "Alert Summary Tooltip";
      this.customTooltip.showControls = true;

      this.customTooltip.showHeader = false;
      this.customTooltip.parentDocument = this;
      this.customTooltip.processBox = this;
      this.customTooltip.display();
        //event for display list button click
        setTimeout(() => {

          if (!this.eventsCreated) {
            this.customTooltip.getChild().DISPLAY_LIST_CLICK.subscribe((target) => {
              this.lastSelectedTooltipParams = target.noode.data
              ExternalInterface.call("openAlertInstanceSummary", "openAlertInstSummary");
            });
          }
        }, 0);

        //event for display list button click
        setTimeout(() => {

          if (!this.eventsCreated) {
            this.customTooltip.getChild().LINK_TO_SPECIF_CLICK.subscribe((target) => {
              this.getScenarioFacility(target.noode.data.scenario_id);
              this.lastSelectedTooltipParams = target.noode.data
              this.hostId = target.hostId;
              this.entityId = this.lastSelectedTooltipParams.ENTITY;
              this.currencyId = this.lastSelectedTooltipParams.CCY;
            });
          }
        }, 0);

        //event for display list button click
        setTimeout(() => {

          if (!this.eventsCreated) {
            this.customTooltip.getChild().ITEM_CLICK.subscribe((target) => {
              this.selectedNodeId= target.noode.data.id;
              this.treeLevelValue= target.noode.data.treeLevelValue;
              this.customTooltip.getChild().linkToSpecificButton.enabled = false;
              if (target.noode.data.count == 1 && target.noode.isBranch ==false ) {
                this.customTooltip.getChild().linkToSpecificButton.enabled = true;
              }
            });
          }
        }, 0);

      } catch (error) {
          console.log("SwtCommonGrid -> createTooltip -> error", error)
              
      }
  }

  getScenarioFacility(scenarioId) {
    this.requestParams = [];
    this.alertingData.cbStart = this.startOfComms.bind(this);
    this.alertingData.cbStop = this.endOfComms.bind(this);
    this.alertingData.cbFault = this.inputDataFault.bind(this);
    this.alertingData.encodeURL = false;
    // Define the action to send the request
    this.actionPath = "scenarioSummary.do?";
    // Define method the request to access
    this.actionMethod = 'method=getScenarioFacility';
    this.requestParams['scenarioId'] = scenarioId;
    this.alertingData.url = this.baseURL + this.actionPath + this.actionMethod;
    this.alertingData.cbResult = (event) => {
      this.openGoToScreen(event);
    };
    this.alertingData.send(this.requestParams);
  }

  openGoToScreen(event) {
    if(event && event.ScenarioSummary && event.ScenarioSummary.scenarioFacility){
    var facilityId = event.ScenarioSummary.scenarioFacility;


    const selectedEntity = this.lastSelectedTooltipParams != null && this.lastSelectedTooltipParams['entity_id'] != null?this.lastSelectedTooltipParams['entity_id']:this.tooltipEntityId;
    const selectedcurrency_code = this.lastSelectedTooltipParams != null && this.lastSelectedTooltipParams['currency_code'] != null?this.lastSelectedTooltipParams['currency_code']:this.tooltipCurrencyCode;
    const selectedmatch_id = this.lastSelectedTooltipParams != null && this.lastSelectedTooltipParams['match_id'] != null?this.lastSelectedTooltipParams['match_id']:null;
    const selectedmovement_id = this.lastSelectedTooltipParams != null && this.lastSelectedTooltipParams['movement_id'] != null?this.lastSelectedTooltipParams['movement_id']:null
    const selectedsweep_id = this.lastSelectedTooltipParams != null && this.lastSelectedTooltipParams['sweep_id'] != null?this.lastSelectedTooltipParams['sweep_id']:null;


    ExternalInterface.call("goTo", facilityId, this.hostId, selectedEntity, selectedmatch_id, selectedcurrency_code, selectedmovement_id, selectedsweep_id, "");
    }

    // ExternalInterface.call("goTo", facilityId, this.hostId, this.entityId, "", this.currencyId, "", "", "");

  }

  public removeTooltip (){
    if(this.customTooltip != null)
    this.customTooltip.close();
  }


  itemClickFunction(event) {
    if (event.target != null && event.target.field != null && event.target.field == "alerting") {
      this.tooltipCurrencyCode = (event.target.data.__treeLevel==0) ? event.target.data.currencyEntityGroupAccount:event.target.data.ccyCode;
      this.tooltipEntityId = (event.target.data.__treeLevel>0) ? event.target.data.entityId:null;
      this.tooltipFacilityId = this.getFacilityName(event.target.data.__treeLevel);
      this.tooltipOtherParams["ilmAccountGroup"] = (event.target.data.__treeLevel>1 ?event.target.data.accountGroupId:null);
      
      this.tooltipSelectedDate = this.jsonReader.getScreenAttributes()["valueDate"];
      this.createTooltip();
    }
  }


  getFacilityName(level) {
    var facility = null;
    switch (level) {
      case 0:
        facility = "ILM_MONITOR_SUMMARY_CCY_ROW";
        break;
      case 1:
        facility = "ILM_MONITOR_SUMMARY_ENTITY_ROW";
        break;
      case 2:
        facility = "ILM_MONITOR_SUMMARY_GROUP_ROW";
        break;
      default:
        break;
    }

    return facility;
  }

  startOfComms() {
    this.loadingImage.setVisible(true);
    this.enableDisableFields(false);
  }
  endOfComms() {
    this.loadingImage.setVisible(false);
    this.enableDisableFields(true);
  }
  inputDataFault() {
    this.swtAlert.error('Generic exception error')
  }
  inputDataResult(event) {
    let jsonList = null;

    if (this.inputData.isBusy())
      this.inputData.cbStop();
    else {
      this.lastRecievedJSON = event;
      this.jsonReader.setInputJSON(this.lastRecievedJSON);
      this.entityCombo.setComboData(this.jsonReader.getSelects());
      this.currencyCombo.setComboData(this.jsonReader.getSelects());

      this.currencyPattern = this.jsonReader.getSingletons().currencyPattern;
      this.menuAccess = Number(this.jsonReader.getScreenAttributes()["menuaccess"]);
      this.refreshCB.selected = (this.jsonReader.getScreenAttributes()["withrefresh"] == "Y");
      this.ccyMuliplierCB.selected = (this.jsonReader.getScreenAttributes()["currencymultiplier"] == "Y");

      this.hideNonSumAccount.selected = (this.jsonReader.getScreenAttributes()["hideNonSumAccount"] == "Y");
      this.includecreditLine.selected = (this.jsonReader.getScreenAttributes()["includecreditLine"] == "Y");
      this.sumCutOff.selected = (this.jsonReader.getScreenAttributes()["sumCutOff"] == "Y");
      this.includeSod.selected = (this.jsonReader.getScreenAttributes()["includeSod"] == "Y");
      this.includeOpenMvmt.selected = (this.jsonReader.getScreenAttributes()["includeOpenMvmt"] == "Y");

      jsonList = this.jsonReader.getColumnData();
      let totalColumnData = JSON.parse(JSON.stringify(jsonList));
        for (let i = 0; i < totalColumnData.column.length; i++) {
          // header = SwtUtil.getAMLMessages(jsonList.column[i].heading);
        if (totalColumnData.column[i].dataelement == "currencyEntityGroupAccount") {
            totalColumnData.column[i].type = "str";
            break;
          }
        }
      const obj = { columns: jsonList };
        if (this.currencyGrid === null || this.currencyGrid === undefined) {
          this.currencyGrid.componentID = this.jsonReader.getSingletons().screenid;
        }
        this.currencyGrid.doubleClickEnabled = true;
      this.currencyGrid.currencyFormat = this.currencyPattern;
        this.currencyGrid.CustomGrid(obj);
        for (let i = 0; i < this.currencyGrid.columnDefinitions.length; i++) {
          let column = this.currencyGrid.columnDefinitions[i];
          if (column.field == "alerting") {
            const alertUrl = "./"+ ExternalInterface.call('eval', 'alertOrangeImage');
            const alerCrittUrl = "./"+ ExternalInterface.call('eval', 'alertRedImage');
            column['properties'] = {
              enabled: false,
              columnName: 'alerting',
              imageEnabled: alertUrl,
              imageCritEnabled:alerCrittUrl,
              imageDisabled: "",
              _toolTipFlag: true,
              style: ' display: block; margin-left: auto; margin-right: auto;'
            };
            this.currencyGrid.columnDefinitions[i].editor = null;
            this.currencyGrid.columnDefinitions[i].formatter = AlertingRenderer;
          }
        }




        if (this.jsonReader.getGridData().size > 0) {
          this.currencyGrid.dataProvider = null;
          this.currencyGrid.gridData = this.jsonReader.getGridData();
          this.currencyGrid.setRowSize = this.jsonReader.getRowSize();
          this.currencyGrid.doubleClickEnabled = true;

          this.currencyGrid.customContentFunction = this.gridsContentItemRender.bind(this);
        this.currencyGrid.rowColorFunction = (dataContext, dataIndex, color, field) => {
          return this.drawRowBackground(dataContext, dataIndex, color, field);
          };

        } else {
          this.currencyGrid.dataProvider = null;
          this.currencyGrid.gridData = { size: 0, row: {} };
          this.currencyGrid.refresh();
          this.currencyGrid.selectedIndex = -1;
        }


          let tabsToCreate = [];

          this.tabsName = [];
          this.tabsOrder =[];
          // this.tabsSelectedGroups = [];
        if (this.lastRecievedJSON.ilmSummary.tabs.row) {
            this.tabsConfig = $.extend(true, [], this.lastRecievedJSON.ilmSummary.tabs.row);
          if (this.tabsConfig.length == 0) {
              this.tabsConfig[0] = this.tabsConfig
            }
          for (let i = 0; i < this.tabsConfig.length; i++) {
              // tabsToCreate.push(this.tabsConfig[i].name)
              tabsToCreate.push(this.tabsConfig[i].id)
            this.tabsName[this.tabsConfig[i].id] = this.tabsConfig[i].name;
            this.tabsOrder[this.tabsConfig[i].id] = this.tabsConfig[i].tabOrder;
            // this.tabsSelectedGroups[this.tabsConfig[i].id] = this.tabsConfig[i].selectedGroups;
            }

          }
          this.createTabs(tabsToCreate);
          //tabsToCreate = ["EUR&&RABONL2U"]

      this.totalsGrid.CustomGrid({ columns: totalColumnData });
        this.totalsGrid.gridData = this.jsonReader.getTotalsData();
      if (!this.autoRefresh) {

        this.refreshText.text = "" + this.jsonReader.getRefreshRate();
        this.refreshRate = parseInt(this.jsonReader.getRefreshRate());

        this.autoRefresh = new Timer((this.refreshRate * 1000 * 60), 0);
        /*Set event listener to dataRefresh*/
        this.autoRefresh.addEventListener("timer", this.updateData.bind(this));
        
      }

    }

    if (this.autoRefresh != null  && this.refreshCB.selected) {
      if (!this.autoRefresh.running) {
        this.autoRefresh.start();
      }
    }

  }
  public seriesStyleComboboxValues = null;
  public seriesStyleSizeOfAllCombo = null;
  public seriesStyleIsGlobal = null;
  public saveChangesForILMStyles(isGlobal: boolean, comboboxValues: any, sizeOfAllCombo: number): void {
this.seriesStyleComboboxValues = comboboxValues;
this.seriesStyleSizeOfAllCombo = sizeOfAllCombo;
this.seriesStyleIsGlobal = this.lastTabToOpenSeriesStyleIsGlobal;
  TabSelectEvent.emit("updateSeriesStyle#"+this.lastTabToOpenSeriesStyle );
  }


  drawRowBackground(dataContext, dataIndex, color, field): string {
  
      let rColor: string;
    if (StringUtils.isTrue(dataContext["cutOffExceeded"])) {
        rColor = "#DDDDDD";
      }
      try {
      if (field == "predicted" || field == "external" || field == "confD" ||field == "minBalT" ) {
        let colorFlag = dataContext[field + "Color"];
        if (colorFlag)
              rColor = colorFlag;
      }else if(field == "currencyEntityGroupAccount"){
        let colorFlag = dataContext["rowColor"];
        if (colorFlag)
         rColor = colorFlag;
        }
  
      }
    catch (error) {
      }
      return rColor;
  
  
  
    }
  
    public gridsContentItemRender(raw: any, dataField: string, value: string, type: string): string {
      let tmpValue = "";
      try {
        if (dataField === "cutOff" && raw["indent"] == "0") {
        if (!raw["__collapsed"]) {
            value = "";
          }
          
        }
    } catch (e) {
        console.log(e);
        
      }
  
      return value;
    }
  public tabChanged(): void {
    try {
      TabSelectEvent.emit(this.tabNavigator.getSelectedTab().id);
    } catch (e) {

    }
  }
  public updateILMConf(event, profileValue): void {

    this.iLMConfData.url = this.baseURL + this.actionPath + "method=saveLiquidityMonitorConfig&";
    this.ilmConfParams = [];
    if (event == null) {
      /*this.lastProfileAction = "saveProfile";
      this.lastUsedProfile = profileValue;// (event as TextEvent).text;

      this.ilmConfParams["paramName"] = "profileName";
      this.ilmConfParams["paramValue"] = profileValue;
      this.ilmConfParams["entityId"] = this.entityCombo.selectedItem.content;
      this.ilmConfParams["currencyId"] = this.ccyCombo.selectedItem.content;
      this.ilmConfParams["isGeneral"] = "false";*/

    } else {
      if (event == "refreshText" || event == "refreshCB") {
        this.autoRefresh.stop();

        if (this.refreshText.text == "" || parseInt(this.refreshText.text) == 0) {
          this.swtAlert.show(SwtUtil.getPredictMessage('alert.ilmanalysis.nonValidValue', null), "Error");
          return;
        }
        this.refreshRate = parseInt(this.refreshText.text);

        this.autoRefresh.stop();
        this.autoRefresh = new Timer((this.refreshRate * 1000 * 60), 0);
        this.autoRefresh.addEventListener("timer", this.updateData.bind(this));
        if (this.autoRefresh != null && this.refreshCB.selected) {
          if (!this.autoRefresh.running)
            this.autoRefresh.start();
        }

        TabSelectEvent.emit("changeRefreshRate#"+this.refreshRate );

      }
      if (event == "refreshCB") {
        this.ilmConfParams["paramName"] = "withRefresh";
        this.ilmConfParams["paramValue"] = this.refreshCB.selected ? "Y" : "N";
      } else if (event == "refreshText" && this.refValueChanged) {
        this.ilmConfParams["paramName"] = "refreshRate";
        this.ilmConfParams["paramValue"] = this.refreshText.text;
      } else if (event == "ccyMuliplierCB") {
        this.ilmConfParams["paramName"] = "useCurrencyMultiplier";
        this.ilmConfParams["paramValue"] = this.ccyMuliplierCB.selected ? "Y" : "N";
      }else if (event == "includeSod") {
        this.ilmConfParams["paramName"] = "includeSodSummary";
        this.ilmConfParams["paramValue"] = this.includeSod.selected ? "Y" : "N";
      }else if (event == "includecreditLine") {
        this.ilmConfParams["paramName"] = "includecreditLineSummary";
        this.ilmConfParams["paramValue"] = this.includecreditLine.selected ? "Y" : "N";
      }else if (event == "includeOpenMvmt") {
        this.ilmConfParams["paramName"] = "includeOpenMvmtSummary";
        this.ilmConfParams["paramValue"] = this.includeOpenMvmt.selected ? "Y" : "N";
      }else if (event == "hideNonSumAccount") {
        this.ilmConfParams["paramName"] = "hideNonSumAccountSummary";
        this.ilmConfParams["paramValue"] = this.hideNonSumAccount.selected ? "Y" : "N";
      }else if (event == "sumCutOff") {
        this.ilmConfParams["paramName"] = "sumCutOffSummary";
        this.ilmConfParams["paramValue"] = this.sumCutOff.selected ? "Y" : "N";
      }else if (event == "lastSelectedEntity") {
        this.ilmConfParams["paramName"] = "lastSelectedEntity";
        this.ilmConfParams["paramValue"] = this.entityCombo.selectedItem.content;
      }else if (event == "lastSelectedCcy") {
        this.ilmConfParams["paramName"] = "lastSelectedCcy";
        this.ilmConfParams["paramValue"] = this.currencyCombo.selectedItem.content;
      }


      this.ilmConfParams["isGeneral"] = "true";
    }
    this.iLMConfData.send(this.ilmConfParams);
  }
  saveResult(event): void {
    var jsonReader: JSONReader = new JSONReader();
    jsonReader.setInputJSON(event);
    if (!jsonReader.getRequestReplyStatus())
      this.swtAlert.error(ExternalInterface.call('getBundle', 'text', 'label-contactAdmin', 'Error occurred, Please contact your System Administrator: \n') + jsonReader.getRequestReplyMessage(), ExternalInterface.call('getBundle', 'text', 'alert-error', 'Error'));
  }
  saveFault(event): void {
    // this.swtAlert.show(ExternalInterface.call('getBundle', 'text', 'label-lossConnection', 'Unable to save to server\nPossible loss of connection'), ExternalInterface.call('getBundle', 'text', 'alert-error', 'Error'));
  }

  updateValuesUsingCcyMultiplier(event) {
    TabSelectEvent.emit("useCurrencyMutliper#"+this.ccyMuliplierCB.selected );

  }
  optionHandler() {
    // var newWindow = window.open("/ilmOptions", 'Stop Rule Add', 'height=700,width=960,left=100,top=100,resizable=yes,scrollbars=yes,toolbar=yes,menubar=no,location=no,directories=no, status=yes');
    //   if (window.focus) {
    //     newWindow.focus();  
    // }
    ExternalInterface.call('openOptions')
  }

  cellLogic(selectedRowData): void {
    let fieldName = selectedRowData.target.field;
    if(fieldName == 'confD'){
      
      const data = selectedRowData.target.data;
      const accountGroupId = data.accountGroupId;
      const ccyCode = data.ccyCode;
      const entityId = data.entityId;
      let isClickable = (data.slickgrid_rowcontent[fieldName]) ? data.slickgrid_rowcontent[fieldName].clickable : null;
      if (isClickable) {
          ExternalInterface.call("openILMThroughPutRatioMonitor", entityId, ccyCode, accountGroupId);
          // var newWindow = window.open("/ILMThroughPutRatioMonitor", 'Acccount Grousp Change Detail', 'height=600,width=1200,left=100,top=100,resizable=yes,scrollbars=yes,toolbar=yes,menubar=no,location=no,directories=no, status=yes');
    // if (window.focus) {
    //   newWindow.focus();
    // }

      }
    }
    // let isClickable = (data.slickgrid_rowcontent[fieldName]) ? data.slickgrid_rowcontent[fieldName].clickable : null;
    // if (isClickable) {
    //   const currencyCode = data.ccy;
    //   const entityId = data.entity;
    //   const ilm_group = data.ilm_group;
    //   const scenarioId = this.scenarioCombo.selectedLabel;
    //   this.throughPutRatioMonitorDrillDownHandler(entityId, currencyCode, ilm_group, scenarioId, this.valueDate.text);
    // }
  }


  // clickLink(sEntityId: string, sCurrencyCode: string, ilm_group: string, scenarioId, valueDate: string): void {
  //   ExternalInterface.call("openChildWindow", sEntityId, sCurrencyCode, ilm_group, scenarioId, valueDate);
  //   // var newWindow = window.open("/ILMThroughPutBreakDown", 'Acccount Grousp Change Detail', 'height=600,width=1200,left=100,top=100,resizable=yes,scrollbars=yes,toolbar=yes,menubar=no,location=no,directories=no, status=yes');
  //   // if (window.focus) {
  //   //   newWindow.focus();
  //   // }
  // }



  /**
   * Drill down to throuput mointor when clicking on Confg.D% column
   */
  throughPutRatioMonitorDrillDownHandler() {
    var newWindow = window.open("/ILMThroughPutRatioMonitor", 'Stop Rule Add', 'height=700,width=960,left=100,top=100,resizable=yes,scrollbars=yes,toolbar=yes,menubar=no,location=no,directories=no, status=yes');
      if (window.focus) {
        newWindow.focus();
    }
    // ExternalInterface.call('openOptions')
  }
  closeHandler() {
    ExternalInterface.call('close')
  }
  public arrayOfTabs = [];
  private nameOfTabs = [];
  createTabs(ilmTabEntityList) {
    try {
    // for (let index = 0; index < ilmTabEntityList.length; index++) {
    //     ilmTabEntityList[index] = ilmTabEntityList[index].split('&&')[1] + "/" + ilmTabEntityList[index].split('&&')[0];
      
    // }


    let oldTabItems = [];
      for (let i = this.arrayOfTabs.length; i--;) {
        if (ilmTabEntityList.indexOf(this.arrayOfTabs[i].id) == -1) {
          if(this.tabNavigator.selectedIndex ==  this.tabNavigator.tabChildrenArray.indexOf(this.arrayOfTabs[i])){
            this.tabNavigator.selectedIndex = 0;
          }
          this.tabNavigator.removeChild(this.arrayOfTabs[i]);
          this.arrayOfTabs.splice(i, 1);
        } else {
          oldTabItems.push(this.arrayOfTabs[i].id)
          this.arrayOfTabs[i].label = this.tabsName[this.arrayOfTabs[i].id];
          this.arrayOfTabs[i].order = this.tabsOrder[this.arrayOfTabs[i].id];
          TabSelectEvent.emit("updateTabs#" + this.arrayOfTabs[i].id);
        }
    }
    

      for (let i = 0; i < ilmTabEntityList.length; i++) {

      if (oldTabItems.indexOf(ilmTabEntityList[i]) == -1) {
          let entityCcyTab = <TabPushStategy>this.tabNavigator.addChildPushStategy(TabPushStategy);
          entityCcyTab.height = "100%";
          entityCcyTab.closable = true;
          entityCcyTab.label = this.tabsName[ilmTabEntityList[i]];
            entityCcyTab.order = this.tabsOrder[ilmTabEntityList[i]];
            
          entityCcyTab.id = ilmTabEntityList[i];
          entityCcyTab.enabled = false;
          let intradayLiquidityTab: IntraDayLiquidityMonitor = entityCcyTab.addChild(IntraDayLiquidityMonitor);
        intradayLiquidityTab.height = "100%";
          intradayLiquidityTab.entityTabName = entityCcyTab.id.split('/')[0];
          intradayLiquidityTab.ccyTabName = entityCcyTab.id.split('/')[1];
        // intradayLiquidityTab.optionsSelectedGroups = this.tabsSelectedGroups[ilmTabEntityList[i]];
        intradayLiquidityTab.parentDocument = this;
        this.nameOfTabs.push(ilmTabEntityList[i])
          this.arrayOfTabs.push(entityCcyTab);

      }

    }
  } catch (error) {
      
  }


     }

  enableDisableTab(tabId,enable){
    for (let i = this.arrayOfTabs.length; i--;) {
      if (tabId == this.arrayOfTabs[i].id ) {
        this.arrayOfTabs[i].enabled = true;

      }
    }

  }
  refreshTabList() {
    this.requestParams = [];
    this.requestParams["entityId"] = this.entityCombo.selectedItem.content;
    this.requestParams["currencyId"] = this.currencyCombo.selectedItem.content;

    this.inputData.cbStart = this.startOfComms.bind(this);
    this.inputData.cbStop = this.endOfComms.bind(this);
    this.inputData.cbResult = (data) => {
      this.refreshTabListResult(data);
    };
    this.inputData.cbFault = this.inputDataFault.bind(this);
    this.inputData.encodeURL = false;
    this.actionPath = "ilmAnalysisMonitor.do?";
    this.actionMethod = 'method=ilmGetSummaryTabList';
    this.inputData.url = this.baseURL + this.actionPath + this.actionMethod;
    // this.inputData.send(this.requestParams);
    this.resetParamsWhenBusy(this.inputData, this.requestParams)

  }
  updateILMOptionConfig(tabId) {
    this.requestParams = [];
    this.requestParams["tabId"] = tabId;

    this.inputData.cbStart = this.startOfComms.bind(this);
    this.inputData.cbStop = this.endOfComms.bind(this);
    this.inputData.cbResult = (data) => {
      this.refreshTabListResult(data);
    };
    this.inputData.cbFault = this.inputDataFault.bind(this);
    this.inputData.encodeURL = false;
    this.actionPath = "ilmAnalysisMonitor.do?";
    this.actionMethod = 'method=updateILMOptions';
    this.inputData.url = this.baseURL + this.actionPath + this.actionMethod;
    // this.inputData.send(this.requestParams);
    this.resetParamsWhenBusy(this.inputData, this.requestParams)

  }

  refreshTabListResult(event) {

    if (this.refreshTabsData.isBusy())
      this.refreshTabsData.cbStop();
    else {
      let tabsToCreate = [];
      this.tabsName = [];
      this.tabsOrder = [];
      // this.tabsSelectedGroups = [];
      if (event.ilmSummary.tabs && event.ilmSummary.tabs.row) {
        this.tabsConfig = $.extend(true, [], event.ilmSummary.tabs.row);
        if (this.tabsConfig.length == 0) {
          this.tabsConfig[0] = this.tabsConfig
        }
        for (let i = 0; i < this.tabsConfig.length; i++) {
          tabsToCreate.push(this.tabsConfig[i].id)
          this.tabsName[this.tabsConfig[i].id] = this.tabsConfig[i].name;
          this.tabsOrder[this.tabsConfig[i].id] = this.tabsConfig[i].tabOrder;
          // this.tabsSelectedGroups[this.tabsConfig[i].id] = this.tabsConfig[i].selectedGroups;
        }

      }
      this.createTabs(tabsToCreate);

    }
  }

    //Resend ilm config if the httpCom is busy
    resetParamsWhenBusy(httpCom: HTTPComms, params) {
      if (httpCom.isBusy()) {
        setTimeout(() => {
          this.resetParamsWhenBusy(httpCom, params)
        }, 0);
      } else {
        httpCom.send(params);
      }
  }



  updateData(event) {
    this.requestParams = [];
    if (event == 'entityCombo') {
      this.requestParams["entityChanged"] = (this.entityCombo.selectedItem.content != "None") ? "true" : "false";
    } else if (event == "ccyCombo") {
      this.requestParams["currencyChanged"] = (this.entityCombo.selectedItem.content != "None") ? "true" : "false";
    }
    this.requestParams["entityId"] = this.entityCombo.selectedItem.content;
    this.requestParams["currencyId"] = this.currencyCombo.selectedItem.content;

    this.requestParams["includeSod"]=  (this.includeSod.selected ? "Y" : "N");
    this.requestParams["includecreditLine"] = (this.includecreditLine.selected ? "Y" : "N");
    this.requestParams["sumCutOff"] =(this.sumCutOff.selected ? "Y" : "N");
    this.requestParams["includeOpenMvmt"] = (this.includeOpenMvmt.selected ? "Y" : "N");
    this.requestParams["hideNonSumAccount"] = (this.hideNonSumAccount.selected ? "Y" : "N");
    this.requestParams["applyCcyMultiplier"] = (this.ccyMuliplierCB.selected ? "Y" : "N");

    this.actionMethod = 'method=ilmSummaryGridDisplay';
    this.inputData.url = this.baseURL + this.actionPath + this.actionMethod;
    this.resetParamsWhenBusy(this.inputData, this.requestParams)
    // this.inputData.send(this.requestParams)
  }
  report(type): void {
    let selects = [];
    selects.push("Include SOD =" + (this.includeSod.selected ? "Y" : "N"));
    selects.push("Include Credit Line =" + (this.includecreditLine.selected ? "Y" : "N"));
    selects.push("Sum by Cut-Off =" + (this.sumCutOff.selected ? "Y" : "N"));
    selects.push("Include Open Movements =" + (this.includeOpenMvmt.selected ? "Y" : "N"));
    selects.push("Hide Non-sum Accounts =" + (this.hideNonSumAccount.selected ? "Y" : "N"));

    this.exportContainer.convertData(this.lastRecievedJSON.ilmSummary.grid.metadata.columns, this.currencyGrid, this.totalsGrid.gridData, selects, type, true, true);
  }
  enableDisableFields(value: boolean) {
    this.exportContainer.enabled = value;
    this.includeSod.enabled = value;
    this.includecreditLine.enabled = value;
    this.sumCutOff.enabled = value;
    this.includeOpenMvmt.enabled = value;
    this.hideNonSumAccount.enabled = value;
    this.refreshButton.enabled = value;
    this.refreshCB.enabled = value;
    this.refreshText.enabled = value;
    this.ccyMuliplierCB.enabled = value;
  }



  arrayRemove(arr, value) {
    return arr.filter(function (ele) {
      return ele != value;
    });
  }
}
//Define lazy loading routes
const routes: Routes = [
  { path: '', component: ILMMainScreen }
];
export const routing: ModuleWithProviders = RouterModule.forChild(routes);
//Define an NgModule that will be loaded by app module
@NgModule({
  imports: [routing, SwtToolBoxModule],
  declarations: [ILMMainScreen],
  entryComponents: []
})
export class ILMMainScreenModule { }