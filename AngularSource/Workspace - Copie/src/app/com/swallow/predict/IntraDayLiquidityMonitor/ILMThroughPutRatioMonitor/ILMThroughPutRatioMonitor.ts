import { Component, OnInit, ElementRef, ViewChild, NgModule, ModuleWithProviders, ViewContainerRef, ComponentFactoryResolver } from '@angular/core';
import { SwtModule, CommonService, SwtAlert, JSONReader, ScreenVersion, SwtUtil, HTTPComms, ContextMenuItem, JSONViewer, SwtPopUpManager, SwtButton, SwtLoadingImage, SwtDateField, SwtTabNavigator, SwtComboBox, SwtTextInput, SwtLabel, ExternalInterface, CommonLogic, Timer, SwtRadioButtonGroup, SwtRadioItem, StringUtils, Tab, HashMap, SwtCommonGrid, SwtCanvas, TitleWindow, SwtToolBoxModule, SwtDataExport, ExportEvent, XML, SwtGroupedCommonGrid, EnhancedAlertingTooltip, SwtImage } from 'swt-tool-box';
import moment from "moment";
import { OptionsPopUp } from '../../../pcm/OptionsPopUp/OptionsPopUp';
import { ChartTabComponent } from './control/ChartTab/ChartTabComponent';
import { Routes, RouterModule } from '@angular/router';
import { ChartData } from './control/ChartTab/ChartData';
import { AlertingRenderer } from '../../EnhancedAlerting/Render/AlertingRenderer';
import {Observable} from 'rxjs/Observable';
import 'rxjs/add/observable/fromEvent';
declare let html2canvas: any;
declare var instanceElement: any;
@Component({
  selector: 'ilmthrough-put-ratio-monitor',
  templateUrl: './ILMThroughPutRatioMonitor.html',
  styleUrls: ['./ILMThroughPutRatioMonitor.css']
})
export class ILMThroughPutRatioMonitor extends SwtModule implements OnInit {

  @ViewChild('refreshButton') refreshButton: SwtButton;
  @ViewChild('optionsButton') optionsButton: SwtButton;
  @ViewChild('closeButton') closeButton: SwtButton;
  @ViewChild('loadingImage') loadingImage: SwtLoadingImage;
  @ViewChild('valueDate') valueDate: SwtDateField;
  @ViewChild('tabs') tabs: SwtTabNavigator;
  @ViewChild('entityCombo') entityCombo: SwtComboBox;
  @ViewChild('ccyCombo') ccyCombo: SwtComboBox;
  @ViewChild('groupCombo') groupCombo: SwtComboBox;
  @ViewChild('scenarioCombo') scenarioCombo: SwtComboBox;
  @ViewChild('calculateASCombo') calculateASCombo: SwtComboBox;
  @ViewChild('dataExport') dataExport: SwtDataExport;

  @ViewChild('lastRefTime') lastRefTime: SwtLabel;

  @ViewChild('entityLabel') entityLabel: SwtLabel;
  @ViewChild('ccyLabel') ccyLabel: SwtLabel;

  @ViewChild('selectedEntity') selectedEntity: SwtLabel;
  @ViewChild('selectedCcy') selectedCcy: SwtLabel;
  @ViewChild('selectedGroup') selectedGroup: SwtLabel;

  @ViewChild('valueDateLabel') valueDateLabel: SwtLabel;
  @ViewChild('lastRefLabel') lastRefLabel: SwtLabel;


  @ViewChild('dateSelected') dateSelected: SwtRadioButtonGroup;
  @ViewChild('todayRadio') todayRadio: SwtRadioItem;
  @ViewChild('selectedDateRadio') selectedDateRadio: SwtRadioItem;

  @ViewChild('displaycontainer') displaycontainer: SwtCanvas;

  @ViewChild('dynamicChartTab', { read: ViewContainerRef }) dynamicChartTab: ViewContainerRef;



  private throughputGrid: SwtGroupedCommonGrid;
  public inputData = new HTTPComms(this.commonService);
  public alertingData = new HTTPComms(this.commonService);
  
  private baseURL = SwtUtil.getBaseURL();
  private invalidComms: string = null;
  private actionMethod: string = "";
  private actionPath: string = "";
  private ilmConfParams: Object;
  private requestParams = [];
  public previousCCy: String = null;
  public previousEntity: String = null;
  private swtAlert: SwtAlert;
  private jsonReader: JSONReader = new JSONReader();
  public currencyFormat: string;
  public screenVersion = new ScreenVersion(this.commonService);
  private showJSONPopup: any;
  public lastRecievedJSON;
  public prevRecievedJSON;
  private throuputLogic: CommonLogic = new CommonLogic();

  public dateFormat: string = "";
  public sysdate: Date;
  private autoRefresh: Timer;
  private refreshRate: number = 30;

  private versionNumber: string = "1.0";
  private screenName: string = "";
  public tabsChartsData: HashMap = new HashMap;
  private comboOpen: boolean = false;
  private comboChange: boolean = false;
  private entitycomboChange: boolean = false;
  public arrayOftabs = [];
  private moduleId = "PREDICT.THROUGHPUTMONITOR";

  private fromILMScreen = false;
  tooltipFacilityId = null;
  tooltipOtherParams = [];
  tooltipEntityId = null;
  tooltipCurrencyCode = null;
  tooltipSelectedDate = null;
  private positionX:number;
  private positionY:number;
  private hostId;
  private entityId;
  private currencyId;
  private selectedNodeId = null;
  private treeLevelValue = null;
  constructor(private commonService: CommonService, private element: ElementRef, private viewContainerRef: ViewContainerRef, private cfr: ComponentFactoryResolver) {
    super(element, commonService);
    this.swtAlert = new SwtAlert(commonService);
    window['Main'] = this;
  }
  ngOnDestroy(): any {
    instanceElement = null;
  }
  ngOnInit() {
    instanceElement = this;

    
    this.throughputGrid = <SwtGroupedCommonGrid>this.displaycontainer.addChild(SwtGroupedCommonGrid);

    this.throughputGrid.uniqueColumn = "ilm_group";
    this.throughputGrid.lockedColumnCount = 4;

    this.screenName = ExternalInterface.call('getBundle', 'text', 'label-screenName', 'Inra-Day Liquidity Monitor - Main Screen');

    this.entityLabel.text = ExternalInterface.call('getBundle', 'text', 'entity', 'Entity');
    this.entityCombo.toolTip = ExternalInterface.call('getBundle', 'tip', 'entity', 'Select an entity ID');
    this.ccyLabel.text = ExternalInterface.call('getBundle', 'text', 'currency', 'Currency');
    this.ccyCombo.toolTip = ExternalInterface.call('getBundle', 'tip', 'currency', 'Select currency code');
    this.valueDateLabel.text = ExternalInterface.call('getBundle', 'text', 'valuedate', 'Value Date');
    this.lastRefLabel.text = ExternalInterface.call('getBundle', 'text', 'lastrefresh', 'Last Refresh:');


    this.ccyLabel.text = ExternalInterface.call('getBundle', 'text', 'currency', 'Currency');
    this.ccyCombo.toolTip = ExternalInterface.call('getBundle', 'tip', 'currency', 'Select currency code');

    this.refreshButton.toolTip = ExternalInterface.call('getBundle', 'tip', 'button-refresh', 'Refresh window');
    this.refreshButton.label = ExternalInterface.call('getBundle', 'text', 'button-refresh', 'Refresh');

    this.closeButton.label = ExternalInterface.call('getBundle', 'text', 'button-close', 'Close');
    this.closeButton.toolTip = ExternalInterface.call('getBundle', 'tip', 'button-close', 'Close window');

    this.todayRadio.label = SwtUtil.getPredictMessage('ilmthroughput.Today');

    this.optionsButton.toolTip = ExternalInterface.call('getBundle', 'tip', 'button-options', 'Options');
    this.optionsButton.label = ExternalInterface.call('getBundle', 'text', 'button-options', 'Options');

    this.throughputGrid.rowColorFunction = (dataContext, dataIndex, color, fieldId) => {
      return this.drawRowBackground(dataContext, dataIndex, color, fieldId);
    };

    this.throughputGrid.ITEM_CLICK.subscribe((selectedRowData) => {
      this.cellLogic(selectedRowData);
    });

    ExportEvent.subscribe((type) => {
      this.report(type)
    });


    this.throughputGrid.colWidthURL ( this.baseURL+"ilmAnalysisMonitor.do?screenName=ilmThroughPutMonitor");
    this.throughputGrid.colOrderURL ( this.baseURL+"ilmAnalysisMonitor.do?");
    this.throughputGrid.saveWidths = true;
    this.throughputGrid.saveColumnOrder = true;

  }
  report(type: string): void {
    let selectedDate = "";
    if (this.todayRadio.selected)
      selectedDate = "";
    else
      selectedDate = this.valueDate.text;

      this.lastExportType= type;

    // html2canvas(this.legendsBox.domElement[0]).then(canvas => {
    //   legendSnapshot = canvas.toDataURL();
    //   if (legendSnapshot)
    //     legendSnapshot = legendSnapshot.split(",")[1];
    //   this.linechart.callMethodInIframe('exportChart', [null, legendSnapshot, null, type, entityId, currencyId, selectedDate, timeFrame])

    // });
    // html2canvas((this.arrayOftabs[0] as Tab).getChildAt(1).component.containerHighChart.nativeElement).then(canvas => {
    //   let image = canvas.toDataURL();
    //   if (image)
    //     image = image.split(",")[1];

    //   // this.linechart.callMethodInIframe('exportChart', [null, legendSnapshot, null, type, entityId, currencyId, selectedDate, timeFrame])

    // });


    // (this.arrayOftabs[0] as Tab).getChildAt(1).component.id
    // const chart = (this.arrayOftabs[0] as Tab).getChildAt(1).component.chart;
    // var svgChart = chart.getSVG();

    // var svg = chart
    // .getSVG({
    //   chart: {
    //     width: 100,
    //     height: 100
    //   }
    // })
    var chartsArray = [];
    this.chartsAsDATAURl.clear();
    this.chartsToExportSize = this.arrayOftabs.length;
    for (let index = 0; index < this.arrayOftabs.length; index++) {
      const element = <Tab>this.arrayOftabs[index]
      let svgString = element.getChildAt(1).component.chart.getSVG();

      // var groupSelect:XML = <select/>;
      // chartData['id'] = element.label;
       this.svg_to_png_data(element.label, svgString);
      // chartsArray.push(chartData);
    }
    // html2canvas((this.arrayOftabs[0] as Tab).getChildAt(1).component.containerHighChart.nativeElement,
    //   {
    //     onclone: (clonedDoc) => {

    //       // I made the div hidden and here I am changing it to visible
    //       clonedDoc.getElementById((this.arrayOftabs[0] as Tab).id).style.display = 'block';
    //     }
    //   }).then(canvas => {
    //     let image = canvas.toDataURL();
    //     if (image)
    //       image = image.split(",")[1];

    //     // this.linechart.callMethodInIframe('exportChart', [null, legendSnapshot, null, type, entityId, currencyId, selectedDate, timeFrame])

    //   });

    // ExternalInterface.call("report", this.entityCombo.selectedLabel, this.ccyCombo.selectedLabel,
    //   this.scenarioCombo.selectedLabel, selectedDate, this.groupCombo.selectedLabel, type, JSON.stringify(chartsArray));
  }


  svg_to_png_data(id, svg_string) {
    // var ctx, mycanvas,  img, child;
  
    // // Flatten CSS styles into the SVG
  
    // img = new Image();
    // img.src = "data:image/svg+xml," + encodeURIComponent(svg_data);
  
    // // Draw the SVG image to a canvas
    // mycanvas = document.createElement('canvas');
    // mycanvas.width = 400;
    // mycanvas.height = 400;
    // ctx = mycanvas.getContext("2d");
    // ctx.drawImage(img, 0, 0);
  
    // // Return the canvas's data
    // return mycanvas.toDataURL("image/png");
    const svg = this.createElementFromHTML(svg_string)
    let clonedSvgElement = <HTMLElement> svg.cloneNode(true);
    let outerHTML = clonedSvgElement.outerHTML,
    blob = new Blob([outerHTML],{type:'image/svg+xml;charset=utf-8'});

let blobURL = webkitURL.createObjectURL(blob);


let image = new Image();
  image.onload = () => {
    
    let canvas = document.createElement('canvas');
    
    canvas.width = 950;
    
    canvas.height = 550;
    let context = canvas.getContext('2d');
    // draw image in canvas starting left-0 , top - 0  
    context.drawImage(image, 0, 0, 950, 500 );
    //  downloadImage(canvas); need to implement
    let png = canvas.toDataURL();
    this.runReport(id, png)
  };
  image.src = blobURL;


  }

  createElementFromHTML(htmlString) {
    var div = document.createElement('div');
    div.innerHTML = htmlString.trim();
  
    // Change this to div.childNodes to support multiple top-level nodes
    return div.firstChild; 
  }

  private chartsAsDATAURl : HashMap = new HashMap;
  private chartsToExportSize : number = 0;
  private lastExportType = "";

  runReport(chartsId, data){
    this.chartsAsDATAURl.put(chartsId, data);
    
    if(this.chartsAsDATAURl.size() == this.chartsToExportSize){
      let selectedDate = "";
      if (this.todayRadio.selected)
        selectedDate = "";
      else
        selectedDate = this.valueDate.text;

      let chartsArray = [];
      let accountGroupValue = null;
      let accountGroupLabel = null;
      for (let i = 0; i < this.chartsAsDATAURl.getKeys().length; i++) {
        let chartData = {};
        const key = this.chartsAsDATAURl.getKeys()[i];
        this.chartsAsDATAURl.getValue(key)
        chartData['id'] = key;
        chartData['chartsData'] = this.chartsAsDATAURl.getValue(key).split(",")[1];
        chartsArray.push(chartData);

      }
      // chartData['id'] = element.label;
      // chartData['chartsData'] = this.svg_to_png_data(element.label, svgString);
      if(this.throughputGrid.selectedItem){
        accountGroupValue = this.throughputGrid.selectedItem.ilm_group.content
        accountGroupLabel = this.groupCombo.dataProvider[this.groupCombo.getIndexOf(accountGroupValue)].value;
      }else {
        accountGroupValue = this.groupCombo.selectedLabel;
        accountGroupLabel = this.groupCombo.selectedValue;
      }
          ExternalInterface.call("report", this.entityCombo.selectedLabel, this.ccyCombo.selectedLabel,
      this.scenarioCombo.selectedLabel, selectedDate, accountGroupValue, this.lastExportType, JSON.stringify(chartsArray),
      this.entityCombo.selectedValue, this.ccyCombo.selectedValue,this.scenarioCombo.selectedValue, accountGroupLabel, this.calculateASCombo.selectedValue);
    }

  }

 

  private lastSelectedField = null;
  cellLogic(selectedRowData): void {
    if(this.throughputGrid.selectedIndex>-1){
    let fieldName = this.lastSelectedField = selectedRowData.target.field;
    const data = selectedRowData.target.data;
    let isClickable = (data.slickgrid_rowcontent[fieldName]) ? data.slickgrid_rowcontent[fieldName].clickable : null;
    if (isClickable) {
      const currencyCode = data.ccy;
      const entityId = data.entity;
      const ilm_group = data.ilm_group;
      const scenarioId = this.scenarioCombo.selectedLabel;
      this.clickLink(entityId, currencyCode, ilm_group, scenarioId, this.valueDate.text);
    }
  }
  }


  clickLink(sEntityId: string, sCurrencyCode: string, ilm_group: string, scenarioId, valueDate: string): void {
    ExternalInterface.call("openChildWindow", sEntityId, sCurrencyCode, ilm_group, scenarioId, valueDate);
    // var newWindow = window.open("/ILMThroughPutBreakDown", 'Acccount Grousp Change Detail', 'height=600,width=1200,left=100,top=100,resizable=yes,scrollbars=yes,toolbar=yes,menubar=no,location=no,directories=no, status=yes');
    // if (window.focus) {
    //   newWindow.focus();
    // }
  }



  changeRadioGroup(event) {
    if (!this.todayRadio.selected) {
      this.valueDate.enabled = true;
    } else {
      this.valueDate.enabled = false;
      this.valueDate.text = "";
      this.dataRefresh(null);
    }
  }


  validateDate(): void {
    if (this.validateDateField(this.valueDate)) {
      this.dataRefresh(null);
    }
  }
  validateDateField(dateField) {
    try {
      let date;
      let alert = SwtUtil.getPredictMessage('alert.enterValidDate', null);
      if (dateField.text) {

        date = moment(dateField.text, this.dateFormat.toUpperCase(), true);
        if (!date.isValid()) {
          this.swtAlert.error(alert, null, null, null, () => {
            this.setFocusDateField(dateField)
          });
          return false;
        }
      } else {
        this.swtAlert.error(alert, null, null, null, () => {
          this.setFocusDateField(dateField)
        });
        return false;
      }
      dateField.selectedDate = date.toDate();
    } catch (error) {
    }

    return true;
  }
  setFocusDateField(dateField) {
    dateField.setFocus();
    dateField.text = this.jsonReader.getScreenAttributes()["datefrom"]
  }


  /**
   * Function called for combo change event.
   **/
  changeCombo(e): void {
    // e.stopImmediatePropagation();
    this.comboChange = true;
    this.entitycomboChange = false;
    this.dataRefresh(null);
  }

  /**
   * Function called for combo change event.
   **/
  entityChangeCombo(e): void {
    // e.stopImmediatePropagation();
    this.comboChange = true;
    this.entitycomboChange = true;
    this.dataRefresh(null);
  }

  /**
   * When a combobox is open then any requests to the server need to be cancelled
   **/
  openedCombo(event): void {
    this.comboOpen = true;
  }

  /**
   * When the combobox has closed, we need to know if the closure was caused by the user clicking away from the box
   **/
  closedCombo(event): void {
    this.comboOpen = false;
  }
  closeHandler() {
    ExternalInterface.call("close");
  }

  /**
   * When the datefield has closed, we need to know if the closure was caused by the user clicking away from the box
   **/
  closedDateField(event): void {
    this.comboOpen = false;
    this.valueDate.interruptComms = false;
  }

  drawRowBackground(dataContext, dataIndex, color, fieldId): string {
    if ("threshold1" == fieldId || "threshold2" == fieldId) {
      try {
        if ("threshold1" == fieldId)
          return dataContext.slickgrid_rowcontent.threshold1Color.content;
        if ("threshold2" == fieldId)
          return dataContext.slickgrid_rowcontent.threshold2Color.content;
      }
      catch (error) {
      }
    }

    return "";
  }

  /**The function initializes the menus in the right click event on the metagroup monitor screen.
 * The links are redirected to their respective pages.
 */
  initializeMenus(): void {
    this.screenVersion.loadScreenVersion(this, this.screenName, this.versionNumber, null);
    let addMenuItem: ContextMenuItem = new ContextMenuItem('Show JSON');
    addMenuItem.MenuItemSelect = this.showGridJSON.bind(this);
    this.screenVersion.svContextMenu.customItems.push(addMenuItem);
    this.contextMenu = this.screenVersion.svContextMenu;
  }
  showGridJSON(): void {

    this.showJSONPopup = SwtPopUpManager.createPopUp(this,
      JSONViewer,
      {
        jsonData: this.lastRecievedJSON,
      });
    this.showJSONPopup.width = "700";
    this.showJSONPopup.title = "Last Received JSON";
    this.showJSONPopup.height = "500";
    this.showJSONPopup.enableResize = false;
    this.showJSONPopup.showControls = true;
    this.showJSONPopup.isModal = true;
    this.showJSONPopup.display();
  }


  public saveRefreshRate(rate) {
    try {
      this.refreshRate = parseInt(rate)
      this.actionPath = 'ilmAnalysisMonitor.do?';
      this.actionMethod = "method=saveThroughputRefreshRate";
      this.requestParams = [];
      // Define method the request to access
      this.requestParams['refresh'] = rate;
      this.rateData.encodeURL = false;
      this.rateData.url = this.baseURL + this.actionPath + this.actionMethod;
      this.rateData.send(this.requestParams);
    } catch (e) {
      console.log(e, this.moduleId, 'PCM Monitor', 'saveRefreshRate');
    }
  }



  private rateData = new HTTPComms(this.commonService);
  private win: TitleWindow;
  /**
   * This function serves when the rate button is clicked it opens the popup withthe
   * autorefresh rate set.
   **/
  optionsHandler(): void {
    try {
      // Instantiates the OptionPopUp object
      this.win = SwtPopUpManager.createPopUp(this,
        OptionsPopUp,
        {
          title: "Auto-refresh Rate", // childTitle,
          refreshText: this.refreshRate
        });
      this.win.width = "340";
      this.win.height = "150";
      this.win.id = "myOptionsPopUp";
      this.win.enableResize = false;
      this.win.showControls = true;
      this.win.isModal = true;
      this.win.onClose.subscribe(() => {
        this.autoRefreshAfterStop();
      }, error => {
        console.log(error);
      });
      this.win.display();
    } catch (error) {
      SwtUtil.logError(error, this.moduleId, "Dashboard", "optionsHandler", 0);
    }
  }

  autoRefreshAfterStop() {
    if (this.autoRefresh)
      this.autoRefresh.delay(this.refreshRate * 1000);

  }

  

  onLoad() {
    try {

      this.requestParams = [];
      

      // Initialize the context menu
      this.initializeMenus();
      // It defines the format to be displayed in Date field
      this.dateFormat = ExternalInterface.call('eval', 'dateFormat') || "DD/MM/YYYY";

      this.fromILMScreen =  StringUtils.isTrue(ExternalInterface.call('eval', 'fromILMMonitor'));

      if(this.fromILMScreen){
        this.requestParams['entityId'] = ExternalInterface.call('eval', 'entityId');
        this.requestParams['currencyId'] = ExternalInterface.call('eval', 'currencyId');
        this.requestParams['selectedScenario'] = ExternalInterface.call('eval', 'selectedScenario');
        this.requestParams['accountGroup'] = ExternalInterface.call('eval', 'accountGroup');
        this.requestParams['fromILMScreen'] = this.fromILMScreen;
        this.requestParams["entityChanged"] = false;
      }else {
        this.requestParams["entityChanged"] = true;
      }





      this.throuputLogic.testDate = ExternalInterface.call('eval', 'dbDate');
      this.valueDate.formatString = this.dateFormat.toUpperCase();
      // Set the tooltip for date field
      if (this.dateFormat == "DD/MM/YYYY")
        this.valueDate.toolTip = ExternalInterface.call('getBundle', 'tip', 'valuedateDDMMYY', 'Enter value date (\'DD/MM/YYYY\')');
      else
        this.valueDate.toolTip = ExternalInterface.call('getBundle', 'tip', 'valuedateMMDDYY', 'Enter value date (\'MM/DD/YYYY\')');

      //Initialize the communication objects
      this.inputData.cbStart = this.startOfComms.bind(this);
      this.inputData.cbStop = this.endOfComms.bind(this);
      this.inputData.cbResult = (data) => {
        this.inputDataResult(data);
      };
      this.inputData.cbFault = this.inputDataFault.bind(this);
      this.inputData.encodeURL = false;
      this.actionPath = "ilmAnalysisMonitor.do?";
      this.actionMethod = "method=ilmThroughPutMonitorDisplay";
    
      this.requestParams["firstLoad"] = true;
      this.inputData.url = this.baseURL + this.actionPath + this.actionMethod;
      this.inputData.send(this.requestParams);

      this.throughputGrid.ITEM_CLICK.subscribe((selectedRowData) => {
        setTimeout(() => {
        this.itemClickFunction(selectedRowData);
      }, 0);
  
      });
      Observable.fromEvent(document.body, 'click').subscribe(e => {
        this.positionX = e["clientX"];
        this.positionY = e["clientY"];
      });


    } catch (e) {
      console.log(e);


    }

  }


  inputDataFault(event): void {
    let errorLocation = 0;
    try {
      //lostConnectionText.visible=true;
      this.invalidComms = event.fault.faultString + "\n" + event.fault.faultCode + "\n" + event.fault.faultDetail;
      // If autoRefresh is not equal to null, start the timer.
      if (this.autoRefresh != null) {
        if (!this.autoRefresh.running)
          this.autoRefresh.start();
      }
    } catch (e) {
      // log the error in ERROR LOG
      SwtUtil.logError(e, this.screenName, 'ClassName', 'inputDataFault', errorLocation);
    }
  }

  getParams(): any {
    let params = [];

    const entityId = this.throughputGrid.selectedItem.entity.content;
    const currencyCode = this.throughputGrid.selectedItem.ccy.content;
    const ilm_group = this.throughputGrid.selectedItem.ilm_group.content;
    const scenarioId = this.scenarioCombo.selectedLabel;

    params = [entityId, currencyCode, ilm_group, scenarioId, this.lastSelectedField, this.valueDate.text];
    return params;
  }

  calculateHeight() {
    return window.outerHeight - 230;
  }

  inputDataResult(data): void {


    try {
      if (this.inputData.isBusy())
        this.inputData.cbStop();
      else {
        //Retrieves the XML from ResultEvent
        this.lastRecievedJSON = data;
        //Pass the XML received to this.jsonReader
        this.jsonReader.setInputJSON(this.lastRecievedJSON);



        //Disabling visibility of lostConnectionText
        //Check the requst reply status in XML
        if (this.jsonReader.getRequestReplyStatus()) {
          // Condition to check the last received and previous xml to build data
          if ((this.lastRecievedJSON != this.prevRecievedJSON)) {
            //Clear charts data
            this.tabsChartsData.clear();
            this.sysdate = moment(this.jsonReader.getSingletons().sysdate, this.dateFormat.toUpperCase()).toDate();

            //Fill comboboxes and set selected items
            this.entityCombo.setComboData(this.jsonReader.getSelects());
            this.ccyCombo.setComboData(this.jsonReader.getSelects());
            this.groupCombo.setComboData(this.jsonReader.getSelects());
            this.scenarioCombo.setComboData(this.jsonReader.getSelects());
            this.calculateASCombo.setComboData(this.jsonReader.getSelects(), true);

            this.selectedEntity.text = this.entityCombo.selectedValue;
            this.selectedCcy.text = this.ccyCombo.selectedValue;
            this.selectedGroup.text = this.groupCombo.selectedValue;

            this.valueDate.showToday = false;
            // Gets the selected value date
            this.valueDate.enabled = true;
            this.valueDate.selectedDate = moment(this.jsonReader.getSingletons().valueDate, this.dateFormat.toUpperCase()).toDate();
            //var minDate = moment(this.jsonReader.getSingletons().minDate, this.dateFormat.toUpperCase(), true);
            this.valueDate.selectableRange = { rangStart: null, rangeEnd: this.sysdate };

            if (this.selectedDateRadio.selected) {
              this.valueDate.enabled = true;
            }else {
              this.valueDate.enabled = false;
            }
            this.lastRefTime.text = this.jsonReader.getSingletons().lastRefTime;
            const tabList: string = this.jsonReader.getSingletons().tabList;
            const tabListAsArray = tabList.split(',')
            //Remove items without any data in the new JSON
            let oldTabItems = [];
            for (var i = this.arrayOftabs.length; i--;) {
              if (tabListAsArray.indexOf(this.arrayOftabs[i].label) == -1) {
                if(this.tabs.selectedIndex != 0)
                  this.tabs.selectedIndex = 0;
                this.tabs.removeChild(this.arrayOftabs[i]);
                this.arrayOftabs.splice(i, 1);
              } else {
                oldTabItems.push(this.arrayOftabs[i].label)
              }
            }

            //Create new tabs and set new data charts 
            const tabsData = SwtUtil.convertObjectToArray(this.lastRecievedJSON.ilmThrouputMonitor.tabs.row);
            let currentDateTab = null;
            for (let i = 0; i < tabsData.length; i++) {
              try {
                let elementData = new ChartData;
                elementData.id = tabsData[i].id;
                elementData.timeData = tabsData[i].dataTime;
                elementData.percentData = "" + tabsData[i].dataPercentage;
                elementData.threshold1Time = tabsData[i].threshold1Time;
                elementData.threshold2Time = tabsData[i].threshold2Time;
                elementData.threshold1Percentage = tabsData[i].threshold1Percentage;
                elementData.threshold2Percentage = tabsData[i].threshold2Percentage;
                elementData.threshold1State = tabsData[i].threshold1State;
                elementData.threshold2State = tabsData[i].threshold2State;
                elementData.threshold1Color = tabsData[i].threshold1Color;
                elementData.threshold2Color = tabsData[i].threshold2Color;
                elementData.startTime = tabsData[i].startTime;
                elementData.endTime = tabsData[i].endTime;
                this.tabsChartsData.put(tabsData[i].id, elementData);

                if (oldTabItems.indexOf(tabsData[i].id) == -1) {
                  currentDateTab = <Tab>this.tabs.addChild(Tab);
                  currentDateTab.height = "100%";
                  currentDateTab.label = tabsData[i].id;

                  var elem: ChartTabComponent = currentDateTab.addChild(ChartTabComponent);
                  elem.height = "100%";
                  // elem.timeData = tabsData[i].dataTime;
                  // elem.percentData = tabsData[i].dataPercentage;


                  elem.setChartsData(elementData);

                  this.arrayOftabs.push(currentDateTab);
                }
              } catch (error) {

              }


            }

            for (var i = this.arrayOftabs.length; i--;) {
              if (oldTabItems.indexOf(this.arrayOftabs[i].label) != -1) {
                (this.arrayOftabs[i] as Tab).getChildAt(1).component.updateChart(this.tabsChartsData.getValue(this.arrayOftabs[i].label));
              }
            }
            this.throughputGrid.CustomGrid(data.ilmThrouputMonitor.grid.metadata);
            for (let i = 0; i < this.throughputGrid.columnDefinitions.length; i++) {
              let column = this.throughputGrid.columnDefinitions[i];
              if (column.field == "alerting") {
                const alertUrl = "./"+ ExternalInterface.call('eval', 'alertOrangeImage');
                const alerCrittUrl = "./"+ ExternalInterface.call('eval', 'alertRedImage');               
                column['properties'] = {
                  enabled: false,
                  columnName: 'alerting',
                  imageEnabled: alertUrl,
                  imageCritEnabled:alerCrittUrl,
                  imageDisabled: "",
                  _toolTipFlag: true,
                  style: ' display: block; margin-left: auto; margin-right: auto;'
                };
                this.throughputGrid.columnDefinitions[i].editor = null;
                this.throughputGrid.columnDefinitions[i].formatter = AlertingRenderer;
              }
            }
            this.refreshRate = parseInt(this.jsonReader.getSingletons().refresh);

            // Triggers the auto refresh if it's null
            if (this.autoRefresh == null) {
              this.autoRefresh = new Timer((this.refreshRate * 1000), 0);
              this.autoRefresh.addEventListener("timer", this.dataRefresh.bind(this));
            } else {
              // Sets delay to auto refresh and triggers it
              this.autoRefresh.delay(this.refreshRate * 1000);
            }


            this.throughputGrid.gridData = this.jsonReader.getGridData();
            this.throughputGrid.setRowSize = this.jsonReader.getRowSize();

            if (this.jsonReader.getRowSize() < 1)
            {
              this.dataExport.enabled=false;
            }
            else
            {
              this.dataExport.enabled = true;
            }


          }
        }
        else {
          // Alerts the user if any exceptions occured in the server side
          this.swtAlert.show(ExternalInterface.call('getBundle', 'text', 'label-contactAdmin', 'Error occurred, Please contact your System Administrator: \n') + this.jsonReader.getRequestReplyMessage(), ExternalInterface.call('getBundle', 'text', 'alert-error', 'Error'));
        }

      }
      if (this.autoRefresh != null) {
        if (!this.autoRefresh.running)
          this.autoRefresh.start();
      }
      this.entitycomboChange = false;
    } catch (e) {
      console.log(e);

    }



  }

  doHelp() {

  }

  dataRefresh(event): void {

    this.actionPath = "ilmAnalysisMonitor.do?";
    this.actionMethod = "method=ilmThroughPutMonitorDisplay";

    this.requestParams["entityId"] = this.entityCombo.selectedLabel;
    this.requestParams["entityChanged"] = this.entitycomboChange;
    this.requestParams["currencyId"] = this.ccyCombo.selectedLabel;
    this.requestParams["selectedScenario"] = this.scenarioCombo.selectedLabel;
    if (this.todayRadio.selected)
      this.requestParams["selectedDate"] = "";
    else
      this.requestParams["selectedDate"] = this.valueDate.text;


    this.requestParams["accountGroup"] = this.groupCombo.selectedLabel;
    this.requestParams["calculateAs"] = this.calculateASCombo.selectedValue;
    this.requestParams["firstLoad"] = false;
    this.requestParams['fromILMScreen'] = this.fromILMScreen;

    this.inputData.url = this.baseURL + this.actionPath + this.actionMethod;
    this.inputData.send(this.requestParams);
  }



  startOfComms(): void {
    this.loadingImage.setVisible(true);
    this.refreshButton.enabled = false;
    this.refreshButton.buttonMode = false;
    if (this.valueDate.enabled)
      this.valueDate.enabled = false;
  }

  endOfComms(): void {
    if (!this.inputData.isBusy()) {
      this.loadingImage.setVisible(false);
    }
    this.refreshButton.enabled = true;
    this.refreshButton.buttonMode = true;
  }


  
  private lastSelectedTooltipParams = null;
  getParamsFromParent() {
    const params = { sqlParams: this.lastSelectedTooltipParams, facilityId: this.tooltipFacilityId, selectedNodeId:this.selectedNodeId, treeLevelValue:this.treeLevelValue ,
      tooltipCurrencyCode :this.tooltipCurrencyCode , tooltipEntityId:this.tooltipEntityId,tooltipSelectedDate:this.tooltipSelectedDate,tooltipSelectedAccount: null,
      tooltipOtherParams:this.tooltipOtherParams};
    return params;
  }

  private eventsCreated = false;
  private customTooltip: any = null;
  public createTooltip (event){
      if(this.customTooltip && this.customTooltip.close)
        this.removeTooltip();
      try {   
      const toolTipWidth = 420;
      this.customTooltip = SwtPopUpManager.createPopUp(parent, EnhancedAlertingTooltip, {
      });
      if (window.innerHeight < this.positionY+450)
      this.positionY=120;
      this.customTooltip.setWindowXY( this.positionX+20, this.positionY);
      this.customTooltip.enableResize = false;
      this.customTooltip.width = ''+toolTipWidth;
      this.customTooltip.height = "450";
      this.customTooltip.enableResize = false;
      this.customTooltip.title = "Alert Summary Tooltip";
      this.customTooltip.showControls = true;
      this.customTooltip.showHeader = false;
    this.customTooltip.parentDocument = this;
    this.customTooltip.processBox = this;
    this.customTooltip.display();
    //event for display list button click
        setTimeout(() => {

          if (!this.eventsCreated) {
            this.customTooltip.getChild().DISPLAY_LIST_CLICK.subscribe((target) => {
              this.lastSelectedTooltipParams = target.noode.data
              ExternalInterface.call("openAlertInstanceSummary", "openAlertInstSummary", this.selectedNodeId, this.treeLevelValue);
            });
          }
        }, 0);

        //event for link to specific button click
        setTimeout(() => {
          if (!this.eventsCreated) {
            this.customTooltip.getChild().LINK_TO_SPECIF_CLICK.subscribe((target) => {
              this.getScenarioFacility(target.noode.data.scenario_id);
              this.lastSelectedTooltipParams = target.noode.data
              this.hostId = target.hostId;
              this.entityId = this.lastSelectedTooltipParams.ENTITY;
              this.currencyId = this.lastSelectedTooltipParams.CCY;
            });
          }
        }, 0);

        //event for display list button click
        setTimeout(() => {

          if (!this.eventsCreated) {
            this.customTooltip.getChild().ITEM_CLICK.subscribe((target) => {
              this.selectedNodeId= target.noode.data.id;
              this.treeLevelValue= target.noode.data.treeLevelValue;
              this.customTooltip.getChild().linkToSpecificButton.enabled = false;
              if (target.noode.data.count == 1 && target.noode.isBranch ==false ) {
                this.customTooltip.getChild().linkToSpecificButton.enabled = true;
              }
            });
          }
        }, 0);

    } catch (error) {
        console.log("SwtCommonGrid -> createTooltip -> error", error)
            
    }
}

public removeTooltip (){
  if(this.customTooltip != null)
  this.customTooltip.close();
}

getScenarioFacility(scenarioId) {
  this.requestParams = [];
  this.alertingData.cbStart = this.startOfComms.bind(this);
  this.alertingData.cbStop = this.endOfComms.bind(this);
  this.alertingData.cbFault = this.inputDataFault.bind(this);
  this.alertingData.encodeURL = false;
  // Define the action to send the request
  this.actionPath = "scenarioSummary.do?";
  // Define method the request to access
  this.actionMethod = 'method=getScenarioFacility';
  this.requestParams['scenarioId'] = scenarioId;
  this.alertingData.url = this.baseURL + this.actionPath + this.actionMethod;
  this.alertingData.cbResult = (event) => {
    this.openGoToScreen(event);
  };
  this.alertingData.send(this.requestParams);
}

openGoToScreen(event) {
  if(event && event.ScenarioSummary && event.ScenarioSummary.scenarioFacility){
  var facilityId = event.ScenarioSummary.scenarioFacility;

  
  const selectedEntity = this.lastSelectedTooltipParams != null && this.lastSelectedTooltipParams['entity_id'] != null?this.lastSelectedTooltipParams['entity_id']:this.tooltipEntityId;
  const selectedcurrency_code = this.lastSelectedTooltipParams != null && this.lastSelectedTooltipParams['currency_code'] != null?this.lastSelectedTooltipParams['currency_code']:this.tooltipCurrencyCode;
  const selectedmatch_id = this.lastSelectedTooltipParams != null && this.lastSelectedTooltipParams['match_id'] != null?this.lastSelectedTooltipParams['match_id']:null;
  const selectedmovement_id = this.lastSelectedTooltipParams != null && this.lastSelectedTooltipParams['movement_id'] != null?this.lastSelectedTooltipParams['movement_id']:null
  const selectedsweep_id = this.lastSelectedTooltipParams != null && this.lastSelectedTooltipParams['sweep_id'] != null?this.lastSelectedTooltipParams['sweep_id']:null;


  ExternalInterface.call("goTo", facilityId, this.hostId, selectedEntity, selectedmatch_id, selectedcurrency_code, selectedmovement_id, selectedsweep_id, "");
  }

  // ExternalInterface.call("goTo", facilityId, this.hostId, this.entityId, "", this.currencyId, "", "", "");
}

itemClickFunction(event){
  if (event.target != null && event.target.field != null && event.target.field == "alerting" && (event.target.data.alerting=="C" || event.target.data.alerting=="Y")) {
    this.tooltipCurrencyCode = event.target.data.ccy;
    this.tooltipEntityId = event.target.data.entity;
    this.tooltipFacilityId = "ILM_THROUGHPUT_MONITOR_GRP_ROW";
    this.tooltipOtherParams["ilmAccountGroup"] = event.target.data.ilm_group;
    this.tooltipSelectedDate = this.valueDate.text;
    setTimeout(() => {
      this.createTooltip(null);
    }, 100);
  } else {
    this.removeTooltip();
  }
}

}

//Define lazy loading routes
const routes: Routes = [
  { path: '', component: ILMThroughPutRatioMonitor }
];
export const routing: ModuleWithProviders = RouterModule.forChild(routes);
//Define an NgModule that will be loaded by app module
@NgModule({
  imports: [routing, SwtToolBoxModule],
  declarations: [ILMThroughPutRatioMonitor],
  entryComponents: []
})
export class ILMThroughPutRatioMonitorModule { }
