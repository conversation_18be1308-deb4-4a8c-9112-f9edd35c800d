<SwtModule (creationComplete)='onLoad()' width="100%" height="100%">
  <VBox width='100%' height='100%' paddingLeft="5" paddingRight="5" paddingTop="5">
      <Grid width="100%" height="100%" paddingLeft="5">
        <GridRow width="100%" height="110">
          <VBox width="100%" height="100%" >
            <GridRow width="100%" height="26">
              <GridItem width="65%">
                <GridItem width="300">
                  <GridItem width="100">
                    <SwtLabel id="entity" #entity></SwtLabel>
                  </GridItem>
                  <GridItem>
                    <SwtComboBox id="entityCombo" #entityCombo width="200" (change)="refreshComboList(entityCombo);resetTree()"
                      dataLabel="entityList"> </SwtComboBox>
                  </GridItem>
                </GridItem>
                <GridItem paddingLeft="15">
                  <SwtLabel id="entityDesc" #entityDesc fontWeight="normal"></SwtLabel>
                </GridItem>
              </GridItem>
            </GridRow>

            <GridRow width="100%" height="26">
              <GridItem width="65%">
                <GridItem width="300">
                  <GridItem width="100">
                    <SwtLabel id="currency" #currency></SwtLabel>
                  </GridItem>
                  <GridItem>
                    <SwtComboBox id="currencyCombo" #currencyCombo width="200" (change)="refreshComboList(currencyCombo);resetTree()"
                      dataLabel="currencyList"> </SwtComboBox>
                  </GridItem>
                </GridItem>
                <GridItem paddingLeft="15">
                  <SwtLabel id="currencyDesc" #currencyDesc fontWeight="normal"></SwtLabel>
                </GridItem>
              </GridItem>
            </GridRow>

            <GridRow width="100%" height="26">
              <GridItem width="65%">
                <GridItem width="300">
                  <GridItem width="100">
                    <SwtLabel id="accountType" #accountType></SwtLabel>
                  </GridItem>
                  <GridItem>
                    <SwtComboBox id="accountTypeCombo" #accountTypeCombo width="200" (change)="refreshComboList(currencyCombo);resetTree()"
                      dataLabel="accountTypeList"> </SwtComboBox>
                  </GridItem>
                </GridItem>
              </GridItem>
            </GridRow>
            
            <GridRow width="100%" height="26">
              <GridItem width="65%">
                <GridItem width="300">
                  <GridItem width="100">
                    <SwtLabel id="account" #account></SwtLabel>
                  </GridItem>
                  <GridItem>
                    <SwtComboBox id="accountCombo" #accountCombo width="200" (change)="refreshComboList(accountCombo);resetTree()"
                      dataLabel="accountList"> </SwtComboBox>
                  </GridItem>
                </GridItem>
                <GridItem paddingLeft="15">
                  <SwtLabel id="accountDesc" #accountDesc fontWeight="normal"></SwtLabel>
                </GridItem>
              </GridItem>
            </GridRow>

          </VBox>
        </GridRow>
        <GridRow width="100%" height="90%">
          <VBox width="100%" height="100%">
          <SwtTabNavigator #tabs id="tabs"  (onChange)="updateData(false);resetTree()" borderBottom="false"
          style="height: 30px; width: 100%;">

          </SwtTabNavigator>
          <GridRow width="100%" height="26">
            <GridItem>
              <GridItem width="320">
                <GridItem width="100">
                  <SwtLabel id="startDateLbl" #startDateLbl></SwtLabel>
                </GridItem>
                <GridItem>
                  <SwtTextInput id="startDateField" #startDateField editable="false"
                  width="90" restrict="0-9/"
                 ></SwtTextInput>
                </GridItem>
              </GridItem>
              <GridItem paddingLeft="0"  width="130">
                <SwtLabel id="endDateLbl" #endDateLbl  restrict="0-9/"
                ></SwtLabel>
              </GridItem>
              <GridItem>
                <SwtTextInput id="endDateField" #endDateField editable="false"
                width="90"></SwtTextInput>
              </GridItem>
            </GridItem>
          </GridRow>

          <GridRow width="100%" height="26">
            <GridItem >
              <GridItem width="320">
                <GridItem width="100">
                  <SwtLabel id="targtAvgBalLbl" #targtAvgBalLbl textAlign="right"></SwtLabel>
                </GridItem>
                <GridItem>
                  <SwtTextInput id="targtAvgBalTxtInput" #targtAvgBalTxtInput 
                  editable="false" width="200">
                  </SwtTextInput>
                </GridItem>
              </GridItem>
              <GridItem paddingLeft="0" width="130">
                <SwtLabel id="minTarBalanceLbl" #minTarBalanceLbl textAlign="right"></SwtLabel>
              </GridItem>
              <GridItem>
                <SwtTextInput id="minTarBalanceTxtInput" #minTarBalanceTxtInput 
                editable="false" width="200">
              </SwtTextInput>
              </GridItem>
            </GridItem>
          </GridRow>
          <GridRow width="100%" height="28" paddingBottom="3">
            <GridItem width="65%">
              <GridItem width="320">
                <GridItem width="100">
                  <SwtLabel id="fillDaysLbl" #fillDaysLbl></SwtLabel>
                </GridItem>
                <GridItem>
                  <SwtTextInput id="fillDaysTxtInput" #fillDaysTxtInput 
                  editable="false" width="200">
                  </SwtTextInput>
                </GridItem>
              </GridItem>
              <GridItem paddingLeft="0" width="130">
                <SwtLabel id="fillBalLbl" #fillBalLbl textAlign="right"></SwtLabel>
              </GridItem>
              <GridItem>
                <SwtTextInput id="fillBalTxtInput" #fillBalTxtInput 
                editable="false" width="200">
              </SwtTextInput>
              </GridItem>
            </GridItem>
          </GridRow>
            
            <SwtCanvas styleName="canvasWithGreyBorder" id="displaycontainer" border="false" width="100%" height="100%"
              #displaycontainer>
            </SwtCanvas>
          </VBox>
        </GridRow>
      </Grid>

      <SwtCanvas width="100%" id="canvasButtons" >
        <HBox width="100%" >
          <HBox paddingLeft="5" width="100%">
            <SwtButton buttonMode="true" id="refreshButton" #refreshButton label="Refresh"    (click)="updateData(false)" >
          </SwtButton>
          <!-- <SwtButton buttonMode="true" id="optionsButton" #optionsButton enabled="false"
            (click)="optionHandler()" >
          </SwtButton> -->
          <SwtButton buttonMode="true" id="closeButton" label="Close"  #closeButton (click)="closeHandler()">
          </SwtButton>
          </HBox>
          <HBox horizontalAlign="right" paddingRight="5">
            <SwtLoadingImage #loadingImage></SwtLoadingImage>
            <SwtButton id="pdf"
              #pdf
              styleName="pdfIcon"
              enabled="true"
              buttonMode="true"
              (click)="report('pdf')"></SwtButton>
            <SwtButton  id="excel"
              #excel
              styleName="excelIcon"
              enabled="true"
              buttonMode="true"
              (click)="report('excel')"></SwtButton>
            <SwtHelpButton id="helpIcon"
              [buttonMode]="true"
              enabled="true"
              helpFile="groups-of-rules"
              >
            </SwtHelpButton>
            <DataExport #exportContainer id="exportContainerSummary" width="10" visible="false"></DataExport>
       
          </HBox>
        </HBox>
      </SwtCanvas>
  </VBox>
</SwtModule>