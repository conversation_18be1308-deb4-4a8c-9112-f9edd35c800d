
<SwtModule (creationComplete)="onLoad()" (close)='closeHandler()'  width="100%" height="100%" >
  <VBox  height="100%" width="100%" paddingLeft="5" paddingRight="5" paddingBottom="5" paddingTop="5">
    <SwtCanvas   width="100%"
                height="15%">
      <VBox width="100%" height="100%" verticalGap="0" paddingLeft="10" paddingTop="5">
        <HBox width="100%">
          <HBox width="50%">
          <SwtLabel #lblTempId width="120"></SwtLabel>
          <SwtTextInput #txtTemplateId
                        maxChars="15"
                        width="160" (focusOut)="changeHighLightBorder()"></SwtTextInput>
          </HBox>
          <HBox width="50%">
          <SwtLabel #lblUserId width="70"></SwtLabel>
          <SwtComboBox #cbUserId
                       width="160"
                       dataLabel="users"
                      (change)="userChange()" ></SwtComboBox>
          </HBox>
        </HBox>
        <HBox width="100%">
          <HBox width="50%">
          <SwtLabel #templateNameLabel width="120"></SwtLabel>
          <SwtTextInput #txtTemplateName
                        maxChars="50"
                        width="160" (focusOut)="changeHighLightBorder()"></SwtTextInput>
          </HBox>
          <HBox width="50%">
          <SwtLabel #publicLabel width="70"></SwtLabel>
          <SwtCheckBox #chbPublic></SwtCheckBox>
          </HBox>
        </HBox>
      </VBox>
    </SwtCanvas>
    <SwtCanvas width="100%" height="75%">
      <HBox width="100%" height="100%">
    <SwtCanvas #forecastMonitorTemplateMainCanvas
                width="96%"
                height="100%"></SwtCanvas>
    <VBox width="5%" height="100%"  horizontalAlign="center" verticalAlign="middle">
    <SwtButton id="upButton"
               #imgUpButton
               styleName="upButtonEnable"
               (click)="moveRecord('imgUpButton')"
               buttonMode="false"
               enabled="false">

    </SwtButton>
    <SwtButton id="downButton"
               #imgDownButton
               styleName="downButtonEnable"
               (click)="moveRecord('imgDownButton')"
               buttonMode="false"
               enabled="false">
    </SwtButton>
    </VBox>
      </HBox>
    </SwtCanvas>
    <SwtCanvas  width="100%" height="8%">
      <HBox width="100%">
        <HBox paddingLeft="5" width="100%">
          <SwtButton #cpyFromButton
                     (click)="loadCopyfrom()" width="70"></SwtButton>
          <SwtButton #addButton
                     (click)="addClickHandler()" width="70"></SwtButton>
          <SwtButton #changeButton
                     enabled="false"
                     (click)="changeClickHandler()" width="70"></SwtButton>
          <SwtButton #deleteButton
                     id="deleteButton"
                     enabled="false"
                     (click)="deleteHandler()" width="70"></SwtButton>
          <SwtButton #saveButton
                     id="saveButton"
                     (click)="closeHandler()" width="70"></SwtButton>
        </HBox>
        <HBox horizontalAlign="right"  right="5" top="3">
          <SwtHelpButton id="helpIcon"
                         #helpIcon (click)="doHelp()"></SwtHelpButton>
          <SwtLoadingImage #loadingImage></SwtLoadingImage>
        </HBox>
      </HBox>
    </SwtCanvas>
  </VBox>
</SwtModule>

