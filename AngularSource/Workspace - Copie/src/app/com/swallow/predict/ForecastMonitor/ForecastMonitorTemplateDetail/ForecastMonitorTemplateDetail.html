
<SwtModule (creationComplete)="onLoad()" width="100%" height="100%" >
  <VBox  height="100%" width="100%" paddingLeft="5" paddingRight="5" paddingBottom="5" paddingTop="5">
    <SwtCanvas   width="100%"
                 height="30%">
      <VBox width="100%" height="100%" paddingLeft="10" paddingTop="5" verticalGap="0">
        <HBox width="100%">
          <SwtLabel #lblColumnType width="110"></SwtLabel>
          <SwtComboBox #cbColumnType
                       width="163"
                       dataLabel="columnType"
                       (change)="typeChange()"></SwtComboBox>
        </HBox>
          <HBox width="100%">
          <SwtLabel #lblColumnNo  width="110"></SwtLabel>
            <SwtNumericInput #txtColumnNo
                          maxChars="12"
                          textAlign="right"
                          width="160"></SwtNumericInput>

          </HBox>
        <HBox width="100%">
          <SwtLabel #lblShortName width="110"></SwtLabel>
          <SwtTextInput #txtShortName
                        restrict="^áéíóúñÁÉÍÓÚÑ@\.\'\/#$%()=*?¡¿'\,\&quot;\&amp;|°!\&lt;>;:\^¨+\{\}\[\]\`~\\"
                        maxChars="12"
                        width="160"
                        (change)="changeHighLightBorder()"></SwtTextInput>
        </HBox>
        <HBox>
          <SwtLabel #lblDesc  width="110"></SwtLabel>
          <SwtTextInput #txtDescription
                        textAlign="left"
                        maxChars="50"
                        width="360"
                        (change)="changeHighLightBorder()"></SwtTextInput>
        </HBox>
        <HBox>
          <SwtCheckBox #chbPublic
                      (change)="changeTextMultiplier()"></SwtCheckBox>

          <SwtLabel #lblContribute  paddingTop="2"></SwtLabel>
          <SwtTextInput #txtMulitiplier
                        restrict="0-9\-\."
                        enabled="false"
                        width="160"
                        textAlign="right"
                        (focusOut)="validateNumber()"></SwtTextInput>
        </HBox>
      </VBox>
    </SwtCanvas>

      <SwtCanvas #forecastMonitorTemplateDetailCanvas
                 width="100%"
                 height="59%"></SwtCanvas>

    <SwtCanvas  width="100%"  height="7%">
      <HBox width="100%">
        <HBox paddingLeft="5" width="100%">
          <SwtButton #suggestButton
                     (click)="loadAddPopup('suggest')"></SwtButton>
          <SwtButton #addButton
                     (click)="loadAddPopup('add')"></SwtButton>
          <SwtButton #deleteButton
                     id="deleteButton"
                     enabled="false"
                     (click)="deleteHandler()"></SwtButton>
          <SwtButton #okButton
                     (click)="saveTemplateDetail()"></SwtButton>
          <SwtButton #closeButton
                     id="closeButton"
                     (click)="closeHandler()"></SwtButton>
        </HBox>
        <HBox horizontalAlign="right"  right="5" top="3">
          <SwtHelpButton id="helpIcon"
                         #helpIcon (click)="doHelp()"></SwtHelpButton>
          <SwtLoadingImage #loadingImage></SwtLoadingImage>
        </HBox>
      </HBox>
    </SwtCanvas>
  </VBox>
</SwtModule>

