import {Component, ElementRef, NgModule, OnInit, ViewChild} from '@angular/core';
import {
  Alert,
  CommonService,
  ExternalInterface,
  HTTPComms,
  JSONReader,
  SwtAlert,
  SwtButton,
  SwtModule,
  SwtUtil,
  SwtToolBoxModule,
  SwtLabel,
  ContextMenuItem,
  SwtPopUpManager,
  JSONViewer,
  ScreenVersion,
  SwtTextInput,
  SwtComboBox,
  focusManager,
} from 'swt-tool-box';
import {RouterModule, Routes} from "@angular/router";
import {ModuleWithProviders} from "@angular/compiler/src/core";

@Component({
  selector: 'app-forecast-monitor-assumptions-add',
  templateUrl: './ForecastMonitorAssumptionsAdd.html',
  styleUrls: ['./ForecastMonitorAssumptionsAdd.css']
})
export class ForecastMonitorAssumptionsAdd extends  SwtModule  implements OnInit {

  @ViewChild('cbEntity') cbEntity: SwtComboBox;
  @ViewChild('cbCurrency') cbCurrency: SwtComboBox;
  @ViewChild('selectedEntity') selectedEntity: SwtLabel;
  @ViewChild('selectedCurrency') selectedCurrency: SwtLabel;
  @ViewChild('txtDate') txtDate: SwtTextInput;
  @ViewChild('txtAmount') txtAmount: SwtTextInput;
  @ViewChild('txtAssumption') txtAssumption: SwtTextInput;
  @ViewChild('btnSave') btnSave: SwtButton;
  @ViewChild('btnCancel') btnCancel: SwtButton;

  private swtAlert: SwtAlert;

  private baseURL = SwtUtil.getBaseURL();
  private actionPath ="";
  private actionMethod ="";
  private invalidComms;
  private lastReceivedJSON;
  private prevRecievedJSON;
  private inputData: HTTPComms =new HTTPComms(this.commonService);
  private saveData: HTTPComms=new HTTPComms(this.commonService);
  private jsonReader: JSONReader=new JSONReader();
  private requestParams = [];
  private menuAccessIdParent=0;
  private showJSON: any;
  private screenName="Forecast Assumption Add";
  private versionNumber="1.1.0001";
  private screenVersion = new ScreenVersion(this.commonService);
  private isValidAmount = true;
  constructor(private commonService: CommonService, private element: ElementRef ) {
    super(element, commonService);
    this.swtAlert = new SwtAlert(commonService);
  }

  ngOnInit() {
    this.btnSave.label = SwtUtil.getPredictMessage('button.forecastMonitor.save', null);
    this.btnSave.toolTip = SwtUtil.getPredictMessage('tooltip.forecastMonitor.save', null);
    this.btnCancel.label = SwtUtil.getPredictMessage('button.forecastMonitor.cancel', null);
    this.btnCancel.toolTip = SwtUtil.getPredictMessage('tooltip.forecastMonitor.cancel', null);
    this.cbEntity.toolTip = SwtUtil.getPredictMessage('tooltip.forecastMonitor.selectEntity', null);
    this.cbCurrency.toolTip = SwtUtil.getPredictMessage('tooltip.forecastMonitor.selectCurrency', null);
    this.txtDate.toolTip = SwtUtil.getPredictMessage('tooltip.forecastAssumptionAdd.date', null);
  }
  onLoad(): void {
    this.requestParams= [];
    // get the menuAccessId
    this.menuAccessIdParent=ExternalInterface.call('eval', 'menuAccessIdParent');
    if (this.menuAccessIdParent == 1) {
      this.btnSave.enabled=false;
    }
    this.initializeMenus();
    this.cbCurrency.enabled = false;
    this.inputData.cbResult= (event) => {
      this.inputDataResult(event);
    };
    this.inputData.cbFault= this.inputDataFault.bind(this);
    this.inputData.encodeURL=false;
    this.actionPath="forecastMonitor.do?";
    this.actionMethod="method=displayAddAssumption";
    this.actionMethod=this.actionMethod + "&selectedCurrencyCode=" + ExternalInterface.call('eval', 'currencyCode')+"&selectedEntityId=" + ExternalInterface.call('eval', 'entityId')+ "&currencyName=" + ExternalInterface.call('eval', 'currencyName');

    this.inputData.url=this.baseURL + this.actionPath + this.actionMethod;
    this.saveData.cbResult= (event) => {
      this.saveDataResult(event);
    };
    this.saveData.cbFault=this.inputDataFault.bind(this);
    this.saveData.encodeURL=false;
    this.inputData.send(this.requestParams);
  }

  /**
   * This method closes the Entity Monitor Options window upon saving any changes made.<br>
   * @param event:ResultEvent
   */
  saveDataResult(event): void {
    if (this.inputData.isBusy()) {
      this.inputData.cbStop();
    } else {
      this.lastReceivedJSON= event;
      this.jsonReader.setInputJSON(this.lastReceivedJSON);
      if (this.jsonReader.getRequestReplyStatus()) {
        ExternalInterface.call("unloadCloseWindow");
      }
    }
  }
  initializeMenus(): void {
    this.screenVersion.loadScreenVersion(this, this.screenName, this.versionNumber, null);
    let addMenuItem: ContextMenuItem =new ContextMenuItem('Show JSON');
    addMenuItem.MenuItemSelect = this.showGridJSON.bind(this);
    this.screenVersion.svContextMenu.customItems.push(addMenuItem);
    this.contextMenu=this.screenVersion.svContextMenu;
  }
  showGridJSON(): void {

    this.showJSON = SwtPopUpManager.createPopUp(this,
      JSONViewer,
      {
        jsonData: this.lastReceivedJSON,
      });
    this.showJSON.width = "500";
    this.showJSON.title = "Last Received JSON";
    this.showJSON.height = "170";
    this.showJSON.enableResize = false;
    this.showJSON.showControls = true;
    this.showJSON.isModal = true;
    this.showJSON.display();
  }

  inputDataResult(event): void {
    if (this.inputData.isBusy()) {
      this.inputData.cbStop();
    } else {
      this.lastReceivedJSON= event;
      this.jsonReader.setInputJSON(this.lastReceivedJSON);
      if (this.jsonReader.getRequestReplyStatus()) {
        if ((this.lastReceivedJSON != this.prevRecievedJSON)) {
          this.cbEntity.setComboData(this.jsonReader.getSelects());
          this.cbCurrency.setComboData(this.jsonReader.getSelects());
          if (this.cbEntity.selectedLabel.trim() != "") {
            this.selectedEntity.text=this.cbEntity.selectedValue;
          }
          if (this.cbCurrency.selectedLabel != "") {
            this.selectedCurrency.text= this.cbCurrency.selectedValue;
          }
          if (ExternalInterface.call('eval','entityId') != "All") {
            this.cbEntity.selectedLabel = ExternalInterface.call('eval','entityId');
            this.cbEntity.enabled = false;
          }
          this.txtDate.text = ExternalInterface.call('eval','date').toString();
          if (ExternalInterface.call('eval','assumption')!= undefined) {
            this.txtAssumption.text = ExternalInterface.call('eval','assumption');
          }

          if (ExternalInterface.call('eval','amount') != undefined) {
            this.txtAmount.text = ExternalInterface.call('eval','amount');
          }

          this.changeEntityCombo();
        }
      }
    }
  }
   inputDataFault(event): void {
    this.invalidComms=event.fault.faultString + "\n" + event.fault.faultCode + "\n" + event.fault.faultDetail;
  }
  onAmountChange(): void {
    let amount: string;
    amount=this.txtAmount.text;
    let amtSign  = false;
    if(amount.indexOf("-") >= 0) {
      amtSign = true;
      amount = amount.substr(1,amount.length);
    }
    let changedAmt: string = ExternalInterface.call("formatCurrency", amount);
    let buttonId: string = Object(focusManager.getFocus()).id;
    if(buttonId == null ||buttonId == "btnCancel" ) {

    } else if (changedAmt == "invalid") {
      this.isValidAmount = false;
      this.swtAlert.warning(
         SwtUtil.getPredictMessage('alert.assumptionAdd.validAmount', null),
        "Warning",
         Alert.OK,
        null,
         this.focusListener.bind(this),
        null);
      return;
    } else {
      this.isValidAmount = true;
      if(amtSign) {
        this.txtAmount.text= "-" + changedAmt;
      } else {
        this.txtAmount.text=changedAmt;
      }
    }
  }
  focusListener() {
    this.txtAmount.setFocus();
  }

  changeEntityCombo(): void {
    this.selectedEntity.text = this.cbEntity.selectedValue;
  }

  updateData(): void {
    if(!this.isValidAmount) {
      this.onAmountChange();
    } else {
      this.requestParams= [];
      this.actionMethod="method=saveForecastAssumption";
      let entity= this.cbEntity.selectedItem.content;
      this.requestParams["forecastAssumption.currencyCode"]= this.cbCurrency.selectedItem.content;
      this.requestParams["forecastAssumption.entityId"]=entity;
      this.requestParams["forecastAssumption.valueDateAsString"]=this.txtDate.text;
      this.requestParams["forecastAssumption.templateId"]=ExternalInterface.call('eval','templateId');
      if (this.txtAmount.text.trim() != "") {
        this.requestParams["assumptionsAmount"]= this.txtAmount.text;
      }
      let assumptionId = ExternalInterface.call('eval','assumptionId');
      this.requestParams["forecastAssumption.assumptionId"] = (assumptionId !== null && assumptionId !== undefined && assumptionId !== "undefined") ? assumptionId : "";
      //this.requestParams["forecastAssumption.assumptionId"]=ExternalInterface.call('eval','assumptionId');
      this.saveData.url=this.baseURL + this.actionPath + this.actionMethod;
      this.saveData.send(this.requestParams);
      this.btnSave.enabled=false;
    }

  }

  closeHandler(): void {
    ExternalInterface.call("closeWindow");
  }
  help() {
    ExternalInterface.call("help");
  }



}
// Define lazy loading routes
const routes: Routes = [
  { path: '', component: ForecastMonitorAssumptionsAdd }
];
export const routing: ModuleWithProviders = RouterModule.forChild(routes);
// Define an NgModule that will be loaded by app module
@NgModule({
  imports: [routing, SwtToolBoxModule],
  declarations: [ForecastMonitorAssumptionsAdd],
  entryComponents: []
})
export class ForecastMonitorAssumptionsAddModule {}
