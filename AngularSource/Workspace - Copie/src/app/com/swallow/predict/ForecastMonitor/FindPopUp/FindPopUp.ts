import {Component, ElementRef, NgModule, ViewChild} from '@angular/core';
import {
  Alert,
  CommonService, ExternalInterface, HashMap,
  HTTPComms,
  JSONReader,
  SwtAlert,
  SwtButton,
  SwtCanvas, SwtCommonGrid,
  SwtLoadingImage, SwtModule, SwtUtil, SwtToolBoxModule, SwtLabel, SwtTextInput, SwtComboBox
} from 'swt-tool-box';
import {RouterModule, Routes} from "@angular/router";
import {ModuleWithProviders} from "@angular/compiler/src/core";

@Component({
  selector: 'app-findpopup',
  templateUrl: './FindPopUp.html',
  styleUrls: ['./FindPopUp.css']
})
export class FindPopUp extends SwtModule {
  @ViewChild('lblType') lblType: SwtLabel;
  @ViewChild('lblEntity') lblEntity: SwtLabel;
  @ViewChild('lblId') lblId: SwtLabel;
  @ViewChild('lblName') lblName: SwtLabel;
  @ViewChild('entityNamelbl') entityNamelbl: SwtLabel;
  @ViewChild('cbType') cbType: SwtComboBox;
  @ViewChild('cbEntity') cbEntity: SwtComboBox;
  @ViewChild('txtId') txtId: SwtTextInput;
  @ViewChild('txtName') txtName: SwtTextInput;
  @ViewChild('findPopUpCanvas') findPopUpCanvas: SwtCanvas;
  @ViewChild('addButton') addButton: SwtButton;
  @ViewChild('closeButton') closeButton: SwtButton;
  @ViewChild('btnSearch') btnSearch: SwtButton;
  @ViewChild('loadingImage') loadingImage: SwtLoadingImage;

  /* Data Objects
     **/
  public jsonReader: JSONReader = new JSONReader();
  public lastRecievedJSON;
  public prevRecievedJSON;
  /**
   * Communication Objects
   **/
  public inputData = new HTTPComms(this.commonService);
  private saveData = new HTTPComms(this.commonService);
  public  requestParams = [];
  private baseURL = SwtUtil.getBaseURL();
  private actionMethod: string;
  private actionPath: string;
  private swtAlert: SwtAlert;
  private findPopUpGrid: SwtCommonGrid;
  private templateId: string = null;
  private templateName: string = null;
  private userId: string = null;
  private columnId: string = null;
  private description: string = null;
  private shortName: string = null;
  private detailRowCount: string = null;
  private idValue: string=null;
  private nameValue: string=null;
  private selectedIds = []
  private selectedNames = [];
  private entityList = [];
  private comboChange: boolean = false;
  private gridJSONList: any;


  constructor(private commonService: CommonService, private element: ElementRef ) {
    super(element, commonService);
    this.swtAlert = new SwtAlert(commonService);
  }
  ngOnInit(): void {
  this.lblType.text = SwtUtil.getPredictMessage('label.findoraddpopup.type', null);
  this.cbType.toolTip = SwtUtil.getPredictMessage('tooltip.findoraddpopup.type', null);
  this.lblEntity.text = SwtUtil.getPredictMessage('label.findoraddpopup.entity', null);
  this.cbEntity.toolTip = SwtUtil.getPredictMessage('tooltip.findoraddpopup.entity', null);
  this.lblId.text = SwtUtil.getPredictMessage('label.findoraddpopup.id', null);
  this.txtId.toolTip = SwtUtil.getPredictMessage('tooltip.findoraddpopup.id', null);
  this.btnSearch.label = SwtUtil.getPredictMessage('button.search', null);
  this.btnSearch.toolTip= SwtUtil.getPredictMessage('tooltip.findoraddpopup.Search', null);
  this.lblName.text= SwtUtil.getPredictMessage('label.findoraddpopup.name', null);
  this.txtName.toolTip= SwtUtil.getPredictMessage('tooltip.findoraddpopup.name', null);
  this.addButton.label = SwtUtil.getPredictMessage('button.add', null);
  this.closeButton.label = SwtUtil.getPredictMessage('button.cancel', null);

  }

  /**
   * onLoad()
   *
   * Upon completion of loading into the flash player this method is called
   **/
   onLoad():void
  {
    this.requestParams = [];
    this.findPopUpGrid = <SwtCommonGrid> this.findPopUpCanvas.addChild(SwtCommonGrid);
    this.findPopUpGrid.onFilterChanged = this.disableAddBtn.bind(this);
    this.findPopUpGrid.onSortChanged = this.disableAddBtn.bind(this);
    this.inputData.cbStart=this.startOfComms.bind(this);
    this.inputData.cbStop=this.endOfComms.bind(this);
    //result event 
    this.inputData.cbResult= (event) => {
      this.inputDataResult(event);
    };
    //fault event
    this.inputData.cbFault=this.inputDataFault.bind(this);
    this.inputData.encodeURL=false;
    //action url	
    this.actionPath="forecastMonitorTemplate.do?method=";
    //Then declare the action method:					
    this.actionMethod="addPopUp&loadFlex=true";

    this.templateId=ExternalInterface.call('eval', 'templateId');
    this.templateName=ExternalInterface.call('eval', 'templateName');
    this.userId=ExternalInterface.call('eval', 'userId');
    this.columnId=ExternalInterface.call('eval', 'columnId');
    this.description=ExternalInterface.call('eval', 'description');
    this.shortName=ExternalInterface.call('eval', 'shortName');
    this.detailRowCount=ExternalInterface.call('eval', 'detailRowCount');


    this.actionMethod+="&templateId=" + this.templateId;
    this.actionMethod+="&templateName=" + this.templateName;
    this.actionMethod+="&userId=" + this.userId;
    this.actionMethod+="&columnId=" + this.columnId;
    this.actionMethod+="&description=" + this.description;
    this.actionMethod+="&shortName=" + this.shortName;
    //Then apply them to the url member of the HTTPComms object:
    this.inputData.url=this.baseURL + this.actionPath + this.actionMethod;
    this.findPopUpGrid.onRowClick = (event) => {
      this.cellLogic(event);
    }

    //Make initial request
    this.inputData.send(this.requestParams);


  }
  /**
   * Part of a callback function to all for control of the loading swf from the HTTPComms Object
   **/
  startOfComms(): void {
    this.loadingImage.setVisible(true);
  }

  /**
   * Part of a callback function to all for control of the loading swf from the HTTPComms Object
   **/
  endOfComms(): void {
    this.loadingImage.setVisible(false);
  }
  /**
   * If a fault occurs with the connection with the server then display the lost connection label
   **/
  inputDataFault(event): void {
    this.swtAlert.error('alert.generic_exception');

  }

inputDataResult(event):void
  {
    this.lastRecievedJSON=event;
    this.jsonReader.setInputJSON(this.lastRecievedJSON);
    if (this.jsonReader.getRequestReplyStatus())
    {
      this.gridJSONList=this.jsonReader.getGridData();
      if (!this.jsonReader.isDataBuilding())
      {

        // Condition to chcek result not acquired through combo vchange
        if (!this.comboChange) {
          this.cbEntity.setComboData(this.jsonReader.getSelects());
          this.cbType.setComboData(this.jsonReader.getSelects());
        }
        this.entityNamelbl.text=this.cbEntity.selectedValue;
        this.comboChange=false;
       let entityObj = this.jsonReader.getSelects()["select"].find(x => x.id == "entity").option;
       for (let i =0; i<entityObj.length; i ++ ) {
         this.entityList.push(entityObj[i].value)
       }
        // Condition to chceck grid is null
        const obj = {columns: this.jsonReader.getColumnData()};
        this.findPopUpGrid.CustomGrid(obj);
        if (this.jsonReader.getGridData().row  ) {
          this.findPopUpGrid.gridData = this.jsonReader.getGridData();
          this.findPopUpGrid.allowMultipleSelection=true;
          this.findPopUpGrid.setRowSize = this.jsonReader.getRowSize();
        } else {
          this.findPopUpGrid.dataProvider = [];
          this.findPopUpGrid.selectedIndex = -1;
        }


        // Set properties for entity type
        if (this.cbType.selectedLabel == "Entity")
        {
          this.txtId.enabled=false;
          this.txtName.enabled=false;
          this.btnSearch.enabled=false;
          this.findPopUpGrid.enabled=false;
          this.findPopUpGrid.dataProvider= [];
          this.findPopUpGrid.setRowSize=0;
          this.addButton.enabled = true;

        }

      }
      else
      {
        if (this.findPopUpGrid.selectedIndex > -1)
        {
          this.addButton.enabled=true;
        }
        else
          this.addButton.enabled=false;
      }
    }
    else
    {
      this.swtAlert.error(
        this.jsonReader.getRequestReplyMessage() + "\n" + this.jsonReader.getRequestReplyLocation(), //message
        "Error", Alert.OK); // close handler
    }
  }
  cellLogic(e):void
  {
    if (this.findPopUpGrid.selectedIndex >= 0)
    {
      this.idValue=this.findPopUpGrid.selectedItem.id.content;
      this.nameValue=this.findPopUpGrid.selectedItem.name.content;
      this.addButton.enabled=true;
      this.selectedIds = [];
      this.selectedNames= [];
      // Multiple row selection is enabled so get selected values in array
      for (var selMvmtCounter=0; selMvmtCounter < this.findPopUpGrid.selectedIndices.length; selMvmtCounter++)
      {
        this.selectedIds.push(this.findPopUpGrid.selectedItems[selMvmtCounter].id.content);
        this.selectedNames.push(this.findPopUpGrid.selectedItems[selMvmtCounter].name.content);
      }

    }
    //added by Imed b for Issue 1054this.SELthis.141 on 24/04/2012
    else
      this.addButton.enabled=false;


  }
  disableAddBtn() {
    if(this.findPopUpGrid.selectedIndex == -1) this.addButton.enabled=false;
  }
  /**
   * typeChange
   * Method to change the data grid for Selected type
   */
  typeChange():void
  {

    // Condition to chcek selected type is entity
    if (this.cbType.selectedLabel == "Entity")
    {
      this.txtId.enabled=false;
      this.txtName.enabled=false;
      this.btnSearch.enabled=false;
      // get selected entity id and name
      this.findPopUpGrid.enabled=false;
      //set the grid row data
      this.findPopUpGrid.gridData=null;
      //set the row size
      this.findPopUpGrid.setRowSize=0;
      this.txtId.text="";
      this.txtName.text="";
      this.addButton.enabled=true;
      this.entityNamelbl.text = this.cbEntity.selectedValue;
      /*for (var j=0; j < this.entityList.length; j++)
      {
        if (this.cbEntity.selectedLabel == this.entityList[j].value)
        {
          this.entityNamelbl.text=this.entityList[j];
          break;
        }
      }*/


    }
    else
    {
      // get records from DB for other types
      this.comboChange=true;
      this.txtId.enabled=true;
      this.txtName.enabled=true;
      this.addButton.enabled=false;
      this.btnSearch.enabled=true;
      this.findPopUpGrid.enabled=true;
      this.requestParams["typeId"]= this.cbType.selectedLabel;
      this.requestParams["entityId"]=this.cbEntity.selectedLabel;
      this.requestParams["fieldId"]= this.txtId.text;
      this.requestParams["fieldName"]= this.txtName.text;
      this.inputData.send(this.requestParams);
    }
  }
  setColSource(event):void {
   // ExternalInterface.call("refreshParent");
    if (window.opener && window.opener.instanceElement) {
      window.opener.instanceElement.refreshDetail();
    window.close();
  }
  }
 addValuesToParent():void
  {
    this.requestParams =  [];

    this.requestParams["shortName"]=this.shortName;
    this.requestParams["description"]=this.description;
    this.requestParams["columnId"]=this.columnId;
    this.requestParams["sourceType"]=this.cbType.selectedLabel;
    this.requestParams["entity"]= this.cbEntity.selectedLabel;

    if (this.cbType.selectedLabel != "Entity")
    {
      this.requestParams["selectedIds"]=this.selectedIds.toString();
      this.requestParams["selectedNames"]=this.selectedNames.toString();
    }
    else
    {
      this.requestParams["selectedIds"]= this.cbEntity.selectedLabel;
      this.requestParams["selectedNames"]= this.entityNamelbl.text;
    }
    this.inputData.cbResult= (event) => {
      this.setColSource(event);
    }
    this.actionPath="forecastMonitorTemplate.do?method=";
    this.actionMethod="addColumnSources";
    this.inputData.url=this.baseURL + this.actionPath + this.actionMethod;
    this.inputData.send(this.requestParams);

  }
  closeHandler():void
  {
    ExternalInterface.call("close");
  }




}
//Define lazy loading routes
const routes: Routes = [
  { path: '', component: FindPopUp }
];
export const routing: ModuleWithProviders = RouterModule.forChild(routes);
//Define an NgModule that will be loaded by app module
@NgModule({
  imports: [routing, SwtToolBoxModule],
  declarations: [FindPopUp],
  entryComponents: []
})
export class FindPopUpModule {}
