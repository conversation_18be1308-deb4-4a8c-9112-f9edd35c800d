import { Component, ElementRef, ViewChild } from '@angular/core';
import {
  SwtModule,
  CommonService,
  SwtButton,
  SwtLabel, SwtNumericInput, SwtRadioItem, SwtRadioButtonGroup, SwtTextInput
} from "swt-tool-box";

@Component({
  selector: 'OptionsSetting',
  templateUrl: './OptionsSetting.html',
  styleUrls: ['./OptionsSetting.css']
})
export class OptionsSetting extends SwtModule {
  public refreshText: string;
  public fontSizeValue : number = 0;

  /*----------------------------------------import all the screen controls here.-------------------------------------*/
  // import close button
  @ViewChild("autoRefreshLabel") autoRefreshLabel: SwtLabel;
  @ViewChild("secondsLabel") secondsLabel: SwtLabel;
  @ViewChild("fonSizeLabel") fonSizeLabel: SwtLabel;
  @ViewChild("fontSize") fontSize: SwtRadioButtonGroup;
  @ViewChild("normalFontSize") normalFontSize: SwtRadioItem;
  @ViewChild("smallFontSize") smallFontSize: SwtRadioItem;
  @ViewChild("okButton") okButton: SwtButton;
  @ViewChild("cancelButton") cancelButton: SwtButton;
  @ViewChild("refreshRate") refreshRate: SwtTextInput;
// Screen constructor.
  constructor(private element: ElementRef, private commonService: CommonService) {
    super(element, commonService);
  }

  // Method called in starting of the screen
  onLoad() {
    // this.autoRefreshLabel.text = ExternalInterface.call('getBundle', 'text', 'label-autoRefreshRate', 'Auto-refresh rate');
    // this.refreshRate.toolTip = ExternalInterface.call('getBundle', 'tip', 'label-refreshRate', 'Enter refresh rate');
    // this.secondsLabel.text = ExternalInterface.call('getBundle', 'text', 'label-seconds', 'Seconds') ;
    // this.fonSizeLabel.text = ExternalInterface.call('getBundle', 'text', 'label-fontSize', 'Font Size');
    // this.normalFontSize.label = ExternalInterface.call('getBundle', 'text', 'label-normal', 'Normal');
    // this.normalFontSize.toolTip = ExternalInterface.call('getBundle', 'tip', 'label-normal', 'Select Normal Font Size');
    // this.smallFontSize.label = ExternalInterface.call('getBundle', 'text', 'label-small', 'Small');
    // this.smallFontSize.toolTip = ExternalInterface.call('getBundle', 'tip', 'label-small', 'Select Normal Font Size');
    // this.okButton.label = ExternalInterface.call('getBundle', 'text', 'button-valider', 'Ok');
    // this.okButton.toolTip = ExternalInterface.call('getBundle', 'tip', 'button-valider', 'Save changes and exit');
    // this.cancelButton.label = ExternalInterface.call('getBundle', 'text', 'button-close', 'Cancel');
    // this.cancelButton.toolTip = ExternalInterface.call('getBundle', 'tip', 'button-close', 'Cancel changes and exit');

    try{
      this.refreshRate.text=""+this.refreshText;
      /*this.okButton.label='Ok';
      this.okButton.toolTip='Save changes and exit';
      //this.okButton.onClick = this.function.bind(this);


      this.cancelButton.label='Close';
      this.cancelButton.toolTip='Close Window';*/
      if(this.fontSizeValue == 0){
        this.fontSize.selectedValue = "N";
      }else {
        this.fontSize.selectedValue = "S"
      }
    }
    catch(error){
      console.log("onLoad====",error);

    }
  }

  removePopUp(){

    this.close();
  }
  saveResult(): void {
    let fontLabel;
    if(this.fontSize.selectedValue == "N"){
      fontLabel = this.normalFontSize.label;
    }else {
      fontLabel = this.smallFontSize.label;
    }
    this.result = {fontSize:  {value: this.fontSize.selectedValue , label:fontLabel } , refreshRate:   Number(this.refreshRate.text)};
    this.removePopUp();

  }


}

 
