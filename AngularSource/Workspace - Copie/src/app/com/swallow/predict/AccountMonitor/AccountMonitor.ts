import {
  Component,
  OnInit,
  ViewChild,
  ModuleWithProviders,
  NgModule,
  ElementRef,
  ViewEncapsulation
} from '@angular/core';
import {
  SwtCanvas,
  SwtCommonGrid,
  CommonService,
  HTTPComms,
  SwtUtil,
  JSONReader,
  SwtLoadingImage,
  SwtComboBox,
  SwtTextInput,
  SwtDateField,
  SwtLabel,
  SwtCheckBox,
  SwtModule,
  SwtPopUpManager,
  SwtButton,
  SwtAlert,
  SwtTabNavigator,
  Tab,
  StringUtils,
  ExternalInterface,
  SwtToolBoxModule,
  SwtRadioButtonGroup,
  SwtRadioItem,
  Alert,
  SwtDataExport,
  ExportEvent,
  CommonUtil,
  ContextMenuItem,
  JSONViewer, ScreenVersion, SwtTotalCommonGrid, EnhancedAlertingTooltip, SwtImage
} from "swt-tool-box";
import 'rxjs/add/observable/interval';
import {RouterModule, Routes} from "@angular/router";
import moment from "moment";
import {OptionsSetting} from '../OptionsSetting/OptionsSetting';
import { Observable } from 'rxjs';
import 'rxjs/add/observable/fromEvent';
import { AlertingRenderer } from '../EnhancedAlerting/Render/AlertingRenderer';
declare var require: any;
const $ = require('jquery');
declare var instanceElement: any;
@Component({
  selector: 'app-account-monitor',
  templateUrl: './AccountMonitor.html',
  styleUrls: ['./AccountMonitor.css'],
  encapsulation: ViewEncapsulation.None
})

export class AccountMonitor extends SwtModule implements OnInit {
  selected = 'pdf';
  @ViewChild("gridCanvas") gridCanvas: SwtCanvas;
  @ViewChild("totalCanvas") totalCanvas: SwtCanvas;
  @ViewChild("tabs") tabs: SwtTabNavigator;
  @ViewChild("displayContainerToday") displayContainerToday: Tab;
  @ViewChild("displayContainerTodayPlus") displayContainerTodayPlus: Tab;
  @ViewChild("displayContainerTodayPlusPlus") displayContainerTodayPlusPlus: Tab;
  @ViewChild("displayContainerTodayPlusThree") displayContainerTodayPlusThree: Tab;
  @ViewChild("displayContainerTodayPlusFour") displayContainerTodayPlusFour: Tab;
  @ViewChild("displayContainerTodayPlusFive") displayContainerTodayPlusFive: Tab;
  @ViewChild("displayContainerTodayPlusSix") displayContainerTodayPlusSix: Tab;
  @ViewChild("displayContainerSelected") displayContainerSelected: Tab;
  @ViewChild('alertImage') alertImage: SwtImage;
  @ViewChild("globalCanvas") globalCanvas: SwtCanvas;
  @ViewChild("totalsContainer") totalsContainer: SwtCanvas;
  @ViewChild("loader") loader: SwtLoadingImage;
  /***Combo*****/
  @ViewChild('groupCombo') groupCombo: SwtComboBox;
  @ViewChild('entityCombo') entityCombo: SwtComboBox;
  @ViewChild('ccyCombo') ccyCombo: SwtComboBox;
  @ViewChild('typeCombo') typeCombo: SwtComboBox;
  @ViewChild('classCombo') classCombo: SwtComboBox;
  /*******Checkbox*/
  @ViewChild('hideZero') hideZero: SwtCheckBox;
  @ViewChild('currencyThreshold') currencyThreshold: SwtCheckBox;

  /*****SwtLabel**********/
  @ViewChild('currencyLabel') currencyLabel: SwtLabel;
  @ViewChild('currencyToday') currencyToday: SwtLabel;
  @ViewChild('selectedCcy') selectedCcy: SwtLabel;
  @ViewChild('accountTypeLabel') accountTypeLabel: SwtLabel;
  @ViewChild('accoutClassLabel') accoutClassLabel: SwtLabel;
  @ViewChild('dateLabel') dateLabel: SwtLabel;
  @ViewChild('startDate') startDate: SwtDateField;
  @ViewChild('entityLabel') entityLabel: SwtLabel;
  @ViewChild('selectedEntity') selectedEntity: SwtLabel;
  @ViewChild('alwaysSumLabel') alwaysSumLabel: SwtLabel;
  @ViewChild('sumByCutOffLabel') sumByCutOffLabel: SwtLabel;
  @ViewChild('hideZeroLabel') hideZeroLabel: SwtLabel;
  @ViewChild('applyCurrencyLabel') applyCurrencyLabel: SwtLabel;
  @ViewChild('openLabel') openLabel: SwtLabel;
  @ViewChild('sodLabel') sodLabel: SwtLabel;
  @ViewChild('lastRefTime') lastRefTime: SwtLabel;
  @ViewChild('lastRefText') lastRefText: SwtLabel;
  @ViewChild('dataBuildingText') dataBuildingText: SwtLabel;
  @ViewChild('lostConnectionText') lostConnectionText: SwtLabel;
  /*******SwtTextInput***********/
  @ViewChild('unexpectedText') unexpectedText: SwtTextInput;
  @ViewChild('sodText') sodText: SwtTextInput;
  /**SwtRadioButton*********/
  @ViewChild('breakdown') breakdown: SwtRadioButtonGroup;
  @ViewChild('radioC') radioC: SwtRadioItem ;
  @ViewChild('radioE') radioE: SwtRadioItem ;
  /***Buttons**********/
  @ViewChild('optionsButton') optionsButton: SwtButton;
  @ViewChild('refreshButton') refreshButton: SwtButton;
  @ViewChild('sumButton') sumButton: SwtButton;
  @ViewChild('manswpButton') manswpButton: SwtButton;
  @ViewChild('closeButton') closeButton: SwtButton;
  @ViewChild('module')  swtModule: SwtModule;
  @ViewChild('dataExport')  dataExport: SwtDataExport;
  @ViewChild('accountRadio')  accountRadio: SwtRadioItem;
  @ViewChild('movementRadio')  movementRadio: SwtRadioItem;
  @ViewChild('breakLabel')  breakLabel: SwtLabel;

  private callFrom: string="CM";

  private accountGrid: SwtCommonGrid;
  private totalGrid: SwtTotalCommonGrid;
  private inputData = new HTTPComms(this.commonService);
  private alertingData = new HTTPComms(this.commonService);
  private ordertData = new HTTPComms(this.commonService);
  private widthData = new HTTPComms(this.commonService);
  private  updateFontSize:HTTPComms=new HTTPComms(this.commonService);
  private  updateRefreshRate:HTTPComms=new HTTPComms(this.commonService);
  private  logicUpdate:HTTPComms=new HTTPComms(this.commonService);
  private actionPath;
  private actionMethod: string = "";
  private requestParams;
  private jsonReader: JSONReader = new JSONReader();
  private lastRecievedJSON;
  private prevRecievedJSON;
  private comboOpen: boolean = false;
  private comboChange: Boolean = false;
  private entityComboChange: Boolean = false;
  private baseURL: string = SwtUtil.getBaseURL();
  public fontSize: string = null;
  private swtAlert: SwtAlert;
  public selectedSort: string = null;
  private arrayOftabs = [];
  private refreshRate: number =10;
  private autoRefresh: boolean = false;
  selectedRowData : any = null;
  public sysDateFrmSession: string;
  private manualSweepAccessId; 
  private SwtAlert: SwtAlert;
  private invalidComms:string = null;
  private dateValue: boolean = true;
  private  menuEntityCurrGrpAccess: string;
  private interval;
  private dateFormat : string;
  public currencyDay = "";
  private win: any;
  private fontValue: string="";
  private fontLabel: string="";
  private fontRequest: string="";
  private selectedFont: number;
  private acctSelectedIndex: number = -1;
  public indexSelectedPay: number = -1;
  private  menuAccessId: string;
  private  versionNumber:string="1.1.0043";
  public screenVersion  = new ScreenVersion(this.commonService);
  public showJSONPopup: any;
  private d: any;
  private testDate: string;
  public today: string = "Today";
  private columnsNewOrders: string;
  private columnDefinitionsTempArray= [];
  private columnsNewWidths: string= "";
  private currencyPattern: string;
  tooltipEntityId = null;
  tooltipCurrencyCode = null;
  tooltipSelectedAccount = null;
  tooltipFacilityId = null;
  tooltipSelectedDate = null;
  tooltipOtherParams = [];
  private positionX:number;
  private positionY:number;
  private hostId;
  private entityId;
  private currencyId;
  private selectedNodeId = null;
  private treeLevelValue = null;
  constructor(private commonService: CommonService, private element: ElementRef ) {
    super(element, commonService);
    this.swtAlert = new SwtAlert(commonService);
  }
  resizeGrids(event) {
    try {
      // this.totalGrid.gridObj.setColumns(this.accountGrid.columnDefinitions);
      this.totalGrid.setRefreshColumnWidths(this.accountGrid.gridObj.getColumns());

    } catch(e) {
      console.log("resizeGrids", e)
    }

  }
  ngOnInit(): void {
    instanceElement = this;
    this.accountGrid = <SwtCommonGrid>this.gridCanvas.addChild(SwtCommonGrid);
    this.totalGrid = <SwtTotalCommonGrid>this.totalsContainer.addChild(SwtTotalCommonGrid);
    this.totalGrid.initialColumnsToSkip = 4;
    this.accountGrid.lockedColumnCount = 4 ;
    this.totalGrid.lockedColumnCount = 1 ;
    this.totalGrid.selectable = false;
    this.accountGrid.columnWidthChanged.subscribe((event) => {
      this.resizeGrids(event);
    });
    this.accountGrid.columnOrderChanged.subscribe((event) => {
      this.resizeGrids(event);
    });
    this.accountGrid.onFilterChanged=  this.disableButtons.bind(this);
    this.accountGrid.listenHorizontalScrollEvent = true;
    this.totalGrid.fireHorizontalScrollEvent = true;
    this.accountGrid.hideHorizontalScrollBar = true;
    this.entityLabel.text = ExternalInterface.call('getBundle', 'text', 'entity', 'Entity');
    this.entityCombo.toolTip = ExternalInterface.call('getBundle', 'tip', 'entity', 'Select an Entity ID');
    this.currencyLabel.text = ExternalInterface.call('getBundle','text', 'currency', 'Currency');
    this.ccyCombo.toolTip = ExternalInterface.call('getBundle','tip', 'currency', 'Select currency code');
    this.accountTypeLabel.text = ExternalInterface.call('getBundle', 'text', 'accounttype', 'Account Type');
    this.typeCombo.toolTip = ExternalInterface.call('getBundle', 'tip', 'accounttype', 'Select account type');
    this.accoutClassLabel.text = ExternalInterface.call('getBundle', 'text', 'accountclass', 'Account Class');
    this.classCombo.toolTip = ExternalInterface.call('getBundle', 'tip', 'accountclass', 'Select account class');
    this.dateLabel.text = ExternalInterface.call('getBundle', 'text', 'date', 'Date');
    this.startDate.toolTip =
      this.hideZeroLabel.text = ExternalInterface.call('getBundle','text', 'hidezero', 'Hide Zero Balances');
    this.hideZero.toolTip = ExternalInterface.call('getBundle','tip', 'hidezero', 'Hide Zero Balances');
    this.applyCurrencyLabel.text = ExternalInterface.call('getBundle', 'text', 'threshold', 'Apply Currency Threshold');
    this.currencyThreshold.toolTip = ExternalInterface.call('getBundle', 'tip', 'threshold', 'Apply Currency Threshold');
    this.refreshButton.label = ExternalInterface.call('getBundle', 'text', 'button-refresh', 'Refresh window');
    this.refreshButton.toolTip = ExternalInterface.call('getBundle', 'tip', 'button-refresh', 'Refresh');
    this.optionsButton.label = ExternalInterface.call('getBundle', 'text', 'button-options', 'Options');
    this.optionsButton.toolTip = ExternalInterface.call('getBundle', 'tip', 'button-options', 'Change Options');
    this.sumButton.label = ExternalInterface.call('getBundle', 'text', 'button-sum', 'Sum');
    this.sumButton.toolTip = ExternalInterface.call('getBundle', 'tip', 'button-sum', 'Manipulate Sum flag');
    this.manswpButton.label = ExternalInterface.call('getBundle','text', 'button-manswp', 'ManSwp');
    this.manswpButton.toolTip = ExternalInterface.call('getBundle','tip', 'button-manswp', 'Manual sweep');
    this.closeButton.label = ExternalInterface.call('getBundle', 'text', 'button-close', 'Close');
    this.closeButton.toolTip = ExternalInterface.call('getBundle', 'tip', 'button-close', 'Close window');
    this.dataBuildingText.text = ExternalInterface.call('getBundle', 'text', 'label-buildInProgress', 'DATA BUILD IN PORGRESS');
    this.lostConnectionText.text = ExternalInterface.call('getBundle', 'text', 'label-connectionError', 'CONNECTION ERROR');
    this.lastRefText.text = ExternalInterface.call('getBundle', 'text', 'label-lastRefresh', 'Last Refresh:');
    this.sodLabel.text=ExternalInterface.call('getBundle', 'text', 'sod', 'Start of day balance');
    this.sodText.toolTip=ExternalInterface.call('getBundle', 'tip', 'sod', 'Start of day balance');
    this.openLabel.text = ExternalInterface.call('getBundle', 'text', 'openunexpected', 'Open Unexpected');
    this.unexpectedText.toolTip = ExternalInterface.call('getBundle', 'tip', 'openunexpected', 'Afficher équilibre inattendu ouvert');
    this.breakLabel.text = ExternalInterface.call('getBundle', 'text', 'breakdownlegend', 'Breakdown');
    this.accountRadio.label = ExternalInterface.call('getBundle', 'text', 'accountbreakdown', 'Account Breakdown');
    this.accountRadio.toolTip = ExternalInterface.call('getBundle', 'tip', 'accountbreakdown', 'Refresh Account Breakdown Grid');
    this.movementRadio.label = ExternalInterface.call('getBundle','text', 'movement', 'Movement');
    this.movementRadio.toolTip = ExternalInterface.call('getBundle', 'tip', 'movement', 'Select Movement to view movement summary display');
  }

  public static ngOnDestroy(): any {
    instanceElement = null;
  }

  onLoad() {
    this.initializeMenus();

    
    this.accountGrid.extraHTMLContentFunction = this.gridsExtraContetnItemRender.bind(this);  

      //TODO
    this.testDate= ExternalInterface.call('eval', 'dbDate');
    this.callFrom= ExternalInterface.call('eval', 'callFrom');
    this.menuAccessId=ExternalInterface.call('eval', 'menuAccessId');
    if (this.callFrom != "CM") {
      this.callFrom="AM";
    }
    this.inputData.cbStart= this.startOfComms.bind(this);
    this.inputData.cbStop= this.endOfComms.bind(this);
    this.inputData.cbResult = (event) => {
      this.inputDataResult(event);
    };
    this.requestParams = [];
   	this.d = this.getUrlParams();
    if(this.d["entityId"]) {
      this.requestParams["accountMonitorNew.dateAsString"]= this.d["valueDate"];
      this.requestParams["accountMonitorNew.entityId"]=this.d["entityId"];
      this.requestParams["accountMonitorNew.currencyCode"]=this.d["currencyCode"];      this.requestParams["accountMonitorNew.accountType"]="All";
      this.requestParams["accountMonitorNew.accountClass"]="All";
      this.actionMethod="method=displayFilteredDetails";
     }
    this.actionMethod = (this.actionMethod=="")?"method=unspecified": this.actionMethod;
    this.inputData.cbFault = this.inputDataFault.bind(this);
    this.inputData.encodeURL = false;
    this.actionPath = "acctmonitornew.do?";
    this.inputData.url = this.baseURL + this.actionPath + this.actionMethod;
    this.requestParams["callFrom"] = this.callFrom;
    //set menuAccessId in params
    this.requestParams["menuAccessId"] = this.menuAccessId;
    this.inputData.send(this.requestParams, null);
    this.accountGrid.ITEM_CLICK.subscribe((selectedRowData) => {
      this.itemClickFunction(selectedRowData);
      this.cellLogic(selectedRowData);
    });

    Observable.fromEvent(document.body, 'click').subscribe(e => {
      this.positionX=e["clientX"];
      this.positionY=e["clientY"];
    });

    ExportEvent.subscribe((type) => {
      this.report(type)
    });
  }
  initializeMenus(): void {
    this.screenVersion.loadScreenVersion(this, 'Account Monitor Screen', this.versionNumber, "");
    let addMenuItem: ContextMenuItem =new ContextMenuItem('Show JSON');
    addMenuItem.MenuItemSelect = this.showGridJSON.bind(this);
    this.screenVersion.svContextMenu.customItems.push(addMenuItem);
    this.contextMenu=this.screenVersion.svContextMenu;
  }


  /** This function is used to display the JSON
   *    for data showed in grid
   */
  showGridJSON(event): void {

    this.showJSONPopup = SwtPopUpManager.createPopUp(this,
      JSONViewer,
      {
        jsonData: this.lastRecievedJSON,
      });
    this.showJSONPopup.width = "700";
    this.showJSONPopup.title = "Last Received JSON";
    this.showJSONPopup.height = "500";
    this.showJSONPopup.enableResize = false;
    this.showJSONPopup.showControls = true;
    this.showJSONPopup.isModal = true;
    this.showJSONPopup.display();
  }
  getUrlParams():{}
  {
    let fullUrl:string=ExternalInterface.call("document_location_href");
    let paramStr:string=fullUrl.split('?')[1];
    let obj = {};
    if (paramStr) {
      paramStr = paramStr.split('#')[0];
      let arr = paramStr.split('&');
      for (let i = 0; i < arr.length; i++) {
        let a = arr[i].split('=');
        let paramName = a[0];
        let paramValue = typeof (a[1]) === 'undefined' ? true : a[1];
        if (paramName.match(/\[(\d+)?\]$/)) {
          let key = paramName.replace(/\[(\d+)?\]/, '');
          if (!obj[key]) obj[key] = [];

          if (paramName.match(/\[\d+\]$/)) {
            let index = /\[(\d+)\]/.exec(paramName)[1];
            obj[key][index] = paramValue;
          } else {
            obj[key].push(paramValue);
          }
        } else {
          if (!obj[paramName]) {
            obj[paramName] = paramValue;
          } else if (obj[paramName] && typeof obj[paramName] === 'string') {
            obj[paramName] = [obj[paramName]];
            obj[paramName].push(paramValue);
          } else {
            obj[paramName].push(paramValue);
          }
        }
      }
    }
    return obj;
  }

  startOfComms(): void {
    this.loader.setVisible(true);
    this.disableInterface();
    this.startDate.enabled=false;
  }

  endOfComms(): void {
    this.loader.setVisible(false);
    this.enableInterface();
    this.startDate.enabled=true;
  }
  disableInterface():void
  {
    this.refreshButton.enabled=false;
    this.refreshButton.buttonMode=false;
  }

  enableInterface():void
  {
    this.refreshButton.enabled=true;
    this.refreshButton.buttonMode=true;
  }

  inputDataResult(event) {
    if (this.inputData.isBusy()) {
      this.inputData.cbStop();
    }
    else {
      this.lastRecievedJSON = event;
      this.jsonReader.setInputJSON(this.lastRecievedJSON);
      this.lostConnectionText.visible = false;
      let lastRef = this.jsonReader.getScreenAttributes()["lastRefTime"];
      this.currencyPattern = (this.jsonReader.getScreenAttributes()["currencyformat"]);
      let lastRefDecode = lastRef.replace(/\\u0028/g, '(').replace(/\\u0029/g, ')');
      this.fontLabel = this.jsonReader.getScreenAttributes()["currfontsize"];
      this.lastRefTime.text = lastRefDecode;
      if (this.lastRecievedJSON != this.prevRecievedJSON) {
        //Check the requst reply status
        if (this.jsonReader.getRequestReplyStatus()) {    
          if (!this.jsonReader.isDataBuilding()) {
            this.sysDateFrmSession = this.jsonReader.getScreenAttributes()["sysDateFrmSession"];
            this.manualSweepAccessId = this.jsonReader.getScreenAttributes()["manualSweepAccessId"];
            this.dateFormat = this.startDate.formatString = this.jsonReader.getScreenAttributes()["dateformat"];
            this.fillComboDataAndText();
            //this condition to create tabs one time
            if (this.lastRecievedJSON.accountmonitor.tabs != "") {
              if(this.arrayOftabs.length == 0) {
                if (this.tabs.getTabChildren().length > 0) {
                  for (let i = 0; i < this.arrayOftabs.length; i++) {
                    this.tabs.removeChild(this.arrayOftabs[i]);
                  }
                }

                this.displayContainerToday = <Tab>this.tabs.addChild(Tab);
                this.displayContainerTodayPlus = <Tab>this.tabs.addChild(Tab);
                this.displayContainerTodayPlusPlus = <Tab>this.tabs.addChild(Tab);
                this.displayContainerTodayPlusThree = <Tab>this.tabs.addChild(Tab);
                this.displayContainerTodayPlusFour = <Tab>this.tabs.addChild(Tab);
                this.displayContainerTodayPlusFive = <Tab>this.tabs.addChild(Tab);
                this.displayContainerTodayPlusSix = <Tab>this.tabs.addChild(Tab);
                this.displayContainerSelected = <Tab>this.tabs.addChild(Tab);
                this.arrayOftabs = [];
                this.arrayOftabs = [this.displayContainerToday, this.displayContainerTodayPlus, this.displayContainerTodayPlusPlus, this.displayContainerTodayPlusThree,
                  this.displayContainerTodayPlusFour, this.displayContainerTodayPlusFive, this.displayContainerTodayPlusSix, this.displayContainerSelected];
              }
              for (let i = 0; i < this.lastRecievedJSON.accountmonitor.tabs.predictdate.length; i++) {
                this.arrayOftabs[i].label = this.lastRecievedJSON.accountmonitor.tabs.predictdate[i].dateLabel;
                this.arrayOftabs[i].businessday = this.lastRecievedJSON.accountmonitor.tabs.predictdate[i].businessday;
                if(this.arrayOftabs[i].businessday ==false){
                  this.arrayOftabs[i].setTabHeaderStyle("color","darkgray");
                }else
                  this.arrayOftabs[i].setTabHeaderStyle("color","black");
              }
              //this.arrayOftabs[7].label = ExternalInterface.call('getBundle', 'text', 'tab4', 'Selected');
              this.arrayOftabs[7].label = 'Selected';
            }
            this.handleDate();
            /*Condition to check jSonReader databuilding*/
            if (!StringUtils.isTrue(this.jsonReader.getScreenAttributes()["databuilding"])) {
              this.dataBuildingText.visible = false;
              this.accountGrid.currencyFormat = this.currencyPattern;
              const obj = {columns: this.jsonReader.getColumnData()};
              this.accountGrid.CustomGrid(obj);
              if (this.jsonReader.getGridData().size > 0) {
                this.accountGrid.gridData = this.jsonReader.getGridData();
                this.accountGrid.setRowSize = this.jsonReader.getRowSize();
                //this.accountGrid.lockedColumnCount = 3;
                // this.accountGrid.allowMultipleSelection = true;
                this.dataExport.enabled = true;
              }
              else {
                this.accountGrid.gridData = {row: {}, size: 0};
                this.dataExport.enabled = false;
              }

              for (let i = 0; i < this.accountGrid.columnDefinitions.length; i++) {
                let column = this.accountGrid.columnDefinitions[i];
                if (column.field == "alerting") {
                  let alertUrl = "./"+ ExternalInterface.call('eval', 'alertOrangeImage');
                  const alerCrittUrl = "./"+ ExternalInterface.call('eval', 'alertRedImage');
                  if (this.fontLabel == "Normal"){
                    column['properties'] = {
                      enabled: false,
                      columnName: 'alerting',
                      imageEnabled: alertUrl,
                      imageCritEnabled:alerCrittUrl,
                      imageDisabled: "",
                      _toolTipFlag: true,
                      style: ' display: block; margin-left: auto; margin-right: auto;'
  
                    };
  
                  }else{
                    column['properties'] = {
                      enabled: false,
                      columnName: 'alerting',
                      imageEnabled: alertUrl,
                      imageCritEnabled:alerCrittUrl,
                      imageDisabled: "",
                      _toolTipFlag: true,
                      style: 'height:15px; width:15px; display: block; margin-left: auto; margin-right: auto;'
  
                    };
            
         }
          
                  this.accountGrid.columnDefinitions[i].editor = null;
                  this.accountGrid.columnDefinitions[i].formatter = AlertingRenderer;
                }
              }
              this.accountGrid.colWidthURL ( this.baseURL+"acctmonitornew.do?");
              this.accountGrid.colOrderURL ( this.baseURL+"acctmonitornew.do?");
              this.accountGrid.entityID = this.entityCombo.selectedLabel;
              this.accountGrid.saveWidths = true;
              this.accountGrid.saveColumnOrder = true;
              //keep the selected index of grid after refresh
              if(this.acctSelectedIndex != null) {
                this.indexSelectedPay= this.accountGrid.gridData.findIndex(x=> x.account == this.acctSelectedIndex);
                if(this.indexSelectedPay >=0) {
                  this.accountGrid.selectedIndex = this.indexSelectedPay;
                }else {
                  this.disableButtons()
                }
              } else {
                this.accountGrid.selectedIndex = -1;
              }
              this.totalGrid.CustomGrid(obj);
              this.totalGrid.gridData = this.jsonReader.getTotalsData();
              this.refreshRate = this.jsonReader.getScreenAttributes()["refresh"];
              
              if(this.fontLabel == "Normal") {
                this.selectedFont = 0;
                this.accountGrid.rowHeight = 18;
              } else {
                this.accountGrid.rowHeight = 15;
                this.selectedFont = 1
              }

              if (this.accountGrid.selectedIndex > -1 && this.menuEntityCurrGrpAccess == "0")
              {
                //disable the sum button where sum flag is "C"
                if ((this.accountGrid.dataset[this.accountGrid.selectedIndex].sum == "C" && this.accountGrid.dataset[this.accountGrid.selectedIndex].slickgrid_rowcontent.sum.summable == "false") || this.accountGrid.dataset[this.accountGrid.selectedIndex].accountStatus == "C")
                {
                  this.sumButton.enabled=false;
                  this.sumButton.buttonMode=false;
                } else {
                  this.sumButton.enabled=true;
                  this.sumButton.buttonMode=true;
                }
                if(this.menuAccessId=='0' && this.manualSweepAccessId!='2'){
                this.manswpButton.enabled=true;
                this.manswpButton.buttonMode=true;
                }
              } else {
                this.sumButton.enabled=false;
                this.sumButton.buttonMode=false;
                this.manswpButton.enabled=false;
                this.manswpButton.buttonMode=false;
              }
              /**AutoRfresh***********/
              let timeLeft = this.refreshRate;
              clearInterval(this.interval);
              this.interval = setInterval(() => {
                this.dataRefresh();
              }, timeLeft * 1000);
              this.accountGrid.rowColorFunction = ( dataContext, dataIndex, color, dataField ) => {
                return this.drawRowBackground( dataContext, dataIndex, color , dataField);
              };
              if (this.entityComboChange == true)
              {
                this.entityComboChange=false;
                //prevRecievedXML=new XML();
              }
              else
              {
                this.prevRecievedJSON=this.lastRecievedJSON;
              }
            }else
            {
              this.dataBuildingText.visible=true;
            }
          }
        }
      }
      this.prevRecievedJSON = this.lastRecievedJSON;

    }

  }
  disableButtons() {
    if( this.accountGrid.selectedIndex == -1) {
      this.sumButton.enabled=false;
      this.sumButton.buttonMode=false;
      this.manswpButton.enabled=false;
      this.manswpButton.buttonMode=false;
      this.sumButton.toolTip='';
      this.manswpButton.toolTip='';
    }
  }
  drawRowBackground( dataContext, dataIndex, color,dataField ): string {

    let rColor: string;
    try {
      let colorFlag: string;
      colorFlag = (dataContext.slickgrid_rowcontent && dataContext.slickgrid_rowcontent.colorflag) ? dataContext.slickgrid_rowcontent.colorflag.content : "";
      if (colorFlag == "D") {
        rColor = "#AAAAAA";
      } else if(colorFlag == "L") {
        rColor = "#DDDDDD";
      } else {
        
        
        if (dataField === 'predicted') {
          const includeLoroInPredictedColor = (dataContext.slickgrid_rowcontent && dataContext.slickgrid_rowcontent.includeLoroInPredictedColor) ? dataContext.slickgrid_rowcontent.includeLoroInPredictedColor.content : "";
          if(includeLoroInPredictedColor != ""){
            rColor = includeLoroInPredictedColor;
          }
        }else if(dataField === "lorocurr"){
          const includePredictedInLoroColor = (dataContext.slickgrid_rowcontent && dataContext.slickgrid_rowcontent.includePredictedInLoroColor) ? dataContext.slickgrid_rowcontent.includePredictedInLoroColor.content : "";
          if(includePredictedInLoroColor != ""){
            rColor = includePredictedInLoroColor;
          }
        }

      }
    }
    catch ( error ) {
      console.log('error drawRowBackground ', error)
    }
    return rColor;
  }


  public gridsExtraContetnItemRender(raw: any, dataField: string, value: string, type: string): string {
      
    try {
      
      if (dataField === 'predicted') {
        const indicator = raw.includeLoroInPredictedIndicator!=null && raw.includeLoroInPredictedIndicator != ""?raw.includeLoroInPredictedIndicator:"&nbsp"
        return "<span style='position:absolute;left:2px;top:0px; width:18px;'>"+indicator+"</span>";
      }else if(dataField === "lorocurr"){
        const indicator = raw.includePredictedInLoroIndicator!=null && raw.includePredictedInLoroIndicator != ""?raw.includePredictedInLoroIndicator:"&nbsp"
        return "<span style='position:absolute;left:2px;top:0px; width:18px;'>"+indicator+"</span>";
      }else{
        return '';
      }
    }catch(e){
      console.log(e);
      
    }

    return value;
  }


  fillComboDataAndText() {
    try {
      this.entityCombo.setComboData(this.jsonReader.getSelects(), false);
      this.ccyCombo.setComboData(this.jsonReader.getSelects(), false);
      this.typeCombo.setComboData(this.jsonReader.getSelects(), true);
      this.classCombo.setComboData(this.jsonReader.getSelects(), true);
      this.selectedEntity.text = this.entityCombo.selectedItem.value;
      this.selectedCcy.text = this.ccyCombo.selectedItem.value;
      this.startDate.text = this.jsonReader.getScreenAttributes()["datefrom"];
      //this.autoRefresh = this.jsonReader.getScreenAttributes()["isautorefresh"];
      //this function is to be reviewed
      if (StringUtils.trim(this.selectedCcy.text) == "") {
        if (this.autoRefresh != null) {
          clearInterval(this.interval);
        }
        this.SwtAlert.error('Invalid: your role does not specify access to currencies/groups for this entity')
      }
      this.currencyThreshold.selected = this.jsonReader.getScreenAttributes()["currencythreshold"];
      this.hideZero.selected = this.jsonReader.getScreenAttributes()["hidezerobalances"];
      this.dateValue = (this.jsonReader.getScreenAttributes()["dateComparing"]);
      this.menuEntityCurrGrpAccess = (this.jsonReader.getScreenAttributes()["menuentitycurrgrpaccess"]);
      if (this.dateValue) {
        this.today  = "";
      }
      else {
        this.today = "Today";
      }
      this.currencyDay = this.ccyCombo.selectedLabel;
      this.sodText.text = (this.jsonReader.getSingletons().sod.content != undefined) ? this.jsonReader.getSingletons().sod.content : "" ;
      if (this.jsonReader.getSingletons().openunexpected.content != undefined) {
        this.unexpectedText.text =this.jsonReader.getSingletons().openunexpected.content;
      } else {
        this.unexpectedText.text = "";
      }

        const alerting = (this.jsonReader.getSingletons().scenarioAlerting != undefined) ? this.jsonReader.getSingletons().scenarioAlerting : "" ;
        if (alerting == "Y") {
          this.alertImage.source =this.baseURL + ExternalInterface.call('eval', 'alertOrangeImage');
          this.alertImage.toolTip="alerts Available";
        } else if(alerting == "C"){
          this.alertImage.source=this.baseURL + ExternalInterface.call('eval', 'alertRedImage');
          this.alertImage.toolTip="alerts Available";
        } else {
          this.alertImage.source=this.baseURL + ExternalInterface.call('eval', 'blankImage');
          this.alertImage.toolTip="";
        }


        
    } catch(e) {
      console.log('eee fillComboDataAndText', e)
    }
  }

  /**
   * this function is not finished yet
   * @param selectedRowData
   */
  cellLogic(selectedRowData = null):void
  {
    try {
      let fieldName = selectedRowData.target.field;
      let isClickable =(selectedRowData.target.data.slickgrid_rowcontent[fieldName]) ? selectedRowData.target.data.slickgrid_rowcontent[fieldName].clickable :"";
      if (this.accountGrid.selectedIndex >=0 && this.menuEntityCurrGrpAccess == "0" )
      {

        this.acctSelectedIndex = this.accountGrid.selectedItem.account.content;
        this.selectedRowData = selectedRowData;

        if ((this.selectedRowData.target.data.sum== "C" && this.selectedRowData.target.data.slickgrid_rowcontent.sum.summable == "false") || this.selectedRowData.target.data.accountStatus == "C")
        {
          this.sumButton.enabled=false;
          this.sumButton.buttonMode=false;
          this.sumButton.toolTip=''
        }
        else
        {
          this.sumButton.enabled=true;
          this.sumButton.buttonMode=true;
          this.sumButton.toolTip="Manipulate Sum flag"
        }
        if(this.menuAccessId=='0' && this.manualSweepAccessId!='2'){
        this.manswpButton.enabled=true;
        this.manswpButton.buttonMode=true;
        this.manswpButton.toolTip="Manual sweep";
        }
      } else {
        this.acctSelectedIndex = null;
        this.sumButton.enabled=false;
        this.sumButton.buttonMode=false;
        this.manswpButton.enabled=false;
        this.manswpButton.buttonMode=false;
        this.sumButton.toolTip='';
        this.manswpButton.toolTip='';
      }
      if(isClickable) {
        let balType:string= selectedRowData.target.name;
        if(fieldName == "lorocurr") {
          balType = "Loro"
        }
        if (this.accountGrid.selectedIndex > -1)
        {
          this.clickLink(this.entityCombo.selectedLabel, this.accountGrid.dataProvider[this.accountGrid.selectedIndex].ccy, this.accountGrid.dataProvider[this.accountGrid.selectedIndex].account, this.startDate.text, (this.currencyThreshold.selected), (this.hideZero.selected), (this.breakdown.selectedValue).toString(), balType);
        }
      }

    } catch(e) {
      console.log('error in cell click', e)
    }


  }

  imageClickFunction(event) {
    const alerting = (this.jsonReader.getSingletons().scenarioAlerting != undefined) ? this.jsonReader.getSingletons().scenarioAlerting : "" ;
        if (alerting == "Y" || alerting == "C") {

      this.tooltipSelectedAccount=null;
      this.tooltipSelectedDate=null;
      this.tooltipCurrencyCode = this.ccyCombo.selectedLabel;
      this.tooltipEntityId = this.entityCombo.selectedLabel;
      this.tooltipFacilityId = "ACCOUNT_MONITOR_SOD_BALANCE";
      this.tooltipOtherParams = [];
      this.tooltipOtherParams["currencythresholdFlag"] = (this.currencyThreshold.selected ? "Y" : "N");
      this.createTooltip(event);
    }

  }

  private lastSelectedTooltipParams = null;
  getParamsFromParent() {
    const params = { sqlParams: this.lastSelectedTooltipParams, facilityId: this.tooltipFacilityId, selectedNodeId:this.selectedNodeId, treeLevelValue:this.treeLevelValue ,
      tooltipCurrencyCode :this.tooltipCurrencyCode , tooltipEntityId:this.tooltipEntityId,tooltipSelectedDate:this.tooltipSelectedDate,tooltipSelectedAccount: this.tooltipSelectedAccount,
      tooltipOtherParams:this.tooltipOtherParams};
    return params;
  }

  private eventsCreated = false;
  private customTooltip: any = null;
  public createTooltip (event){
    if(this.customTooltip && this.customTooltip.close)
      this.removeTooltip();
    try {
      const toolTipWidth = 430;
      this.customTooltip = SwtPopUpManager.createPopUp(parent, EnhancedAlertingTooltip, {
      });
      this.customTooltip.enableResize = false;
      if(event != null ){
        this.positionX=event["clientX"];
        this.positionY=event["clientY"];
      }
      this.customTooltip.width = ''+toolTipWidth;
      this.customTooltip.height = "450";
      this.customTooltip.enableResize = false;
      this.customTooltip.title = "Alert Summary Tooltip";
      this.customTooltip.showControls = true;
      if (window.innerHeight < this.positionY+450)
        this.positionY=200;
      this.customTooltip.setWindowXY(this.positionX+20, this.positionY);
      this.customTooltip.showHeader = true;
      this.customTooltip.parentDocument = this;
      this.customTooltip.processBox = this;
      this.customTooltip.display();
      //event for display list button click
      setTimeout(() => {

         if (!this.eventsCreated) {
          this.customTooltip.getChild().DISPLAY_LIST_CLICK.subscribe((target) => {
            this.lastSelectedTooltipParams = target.noode.data;
             ExternalInterface.call("openAlertInstanceSummary", "openAlertInstSummary", this.selectedNodeId, this.treeLevelValue);
            /*var newWindow = window.open('/AlertInstanceSummary', 'Stop Rule Add', 'height=700,width=960,left=100,top=100,resizable=yes,scrollbars=yes,toolbar=yes,menubar=no,location=no,directories=no, status=yes');
            if (window.focus) {
              newWindow.focus();
            }*/
          });
        }
      }, 0);

      //event for link to specific button click
      setTimeout(() => {

        if (!this.eventsCreated) {
          this.customTooltip.getChild().LINK_TO_SPECIF_CLICK.subscribe((target) => {
            this.getScenarioFacility(target.noode.data.scenario_id);
            this.lastSelectedTooltipParams = target.noode.data;
            this.hostId = target.hostId;
          });
        }
      }, 0);

      //event for tree item click
      setTimeout(() => {

        if (!this.eventsCreated) {
          this.customTooltip.getChild().ITEM_CLICK.subscribe((target) => {
            this.selectedNodeId= target.noode.data.id;
            this.treeLevelValue= target.noode.data.treeLevelValue;
            this.customTooltip.getChild().linkToSpecificButton.enabled = false;
            if(this.tooltipFacilityId == 'ACCOUNT_MONITOR_ACCOUNT_ROW'){
              if (target.noode.data.count == 1 && target.noode.isBranch ==false ) {
                this.customTooltip.getChild().linkToSpecificButton.enabled = true;
              }
            } 
          });
        }
      }, 0);

    } catch (error) {
      console.log("SwtCommonGrid -> createTooltip -> error", error)

    }
  }

  getScenarioFacility(scenarioId) {
    this.requestParams = [];
    this.alertingData.cbStart = this.startOfComms.bind(this);
    this.alertingData.cbStop = this.endOfComms.bind(this);
    this.alertingData.cbFault = this.inputDataFault.bind(this);
    this.alertingData.encodeURL = false;
    // Define the action to send the request
    this.actionPath = "scenarioSummary.do?";
    // Define method the request to access
    this.actionMethod = 'method=getScenarioFacility';
    this.requestParams['scenarioId'] = scenarioId;
    this.alertingData.url = this.baseURL + this.actionPath + this.actionMethod;
    this.alertingData.cbResult = (event) => {
      this.openGoToScreen(event);
    };
    this.alertingData.send(this.requestParams);
  }

  openGoToScreen(event) {
    if(event && event.ScenarioSummary && event.ScenarioSummary.scenarioFacility){

      var facilityId = event.ScenarioSummary.scenarioFacility;
      
      const selectedEntity = this.lastSelectedTooltipParams != null && this.lastSelectedTooltipParams['entity_id'] != null?this.lastSelectedTooltipParams['entity_id']:this.tooltipEntityId;
      const selectedcurrency_code = this.lastSelectedTooltipParams != null && this.lastSelectedTooltipParams['currency_code'] != null?this.lastSelectedTooltipParams['currency_code']:this.tooltipCurrencyCode
      const selectedmatch_id = this.lastSelectedTooltipParams != null && this.lastSelectedTooltipParams['match_id'] != null?this.lastSelectedTooltipParams['match_id']:null
      const selectedmovement_id = this.lastSelectedTooltipParams != null && this.lastSelectedTooltipParams['movement_id'] != null?this.lastSelectedTooltipParams['movement_id']:null
      const selectedsweep_id = this.lastSelectedTooltipParams != null && this.lastSelectedTooltipParams['sweep_id'] != null?this.lastSelectedTooltipParams['sweep_id']:null
      
      
      ExternalInterface.call("goTo", facilityId, this.hostId, selectedEntity, selectedmatch_id, selectedcurrency_code, selectedmovement_id, selectedsweep_id, "");
    }

  }

  public removeTooltip (){
    if(this.customTooltip != null)
      this.customTooltip.close();
  }

  itemClickFunction(event) {
    if (event.target != null && event.target.field != null && event.target.field == "alerting" && (event.target.data.alerting=="C" || event.target.data.alerting=="Y")) {
      this.tooltipCurrencyCode = this.ccyCombo.selectedLabel;
      this.tooltipEntityId = this.entityCombo.selectedLabel;
      this.tooltipSelectedAccount = event.target.data.account;
      this.tooltipFacilityId = "ACCOUNT_MONITOR_ACCOUNT_ROW";
      this.tooltipSelectedDate = this.startDate.text;
      this.tooltipOtherParams = [];
      this.tooltipOtherParams["currencythresholdFlag"] = (this.currencyThreshold.selected ? "Y" : "N");
      setTimeout(() => {
        this.createTooltip(null);
      }, 100);
    }else {
      this.removeTooltip();
    }
  }

  clickLink(sEntityId:string, sCurrency:string, sAccountID:string, sDate:string, sThreshold:boolean, sHideZero:boolean, sSelectScreen:string, sBalanceType:string):void {
    ExternalInterface.call("clickLink", sEntityId, sCurrency, sAccountID, sDate, sThreshold.toString(), sHideZero.toString(), sSelectScreen, sBalanceType);
  }

  /**
   * dataRefresh()
   *
   *
   * Timing result methods
   **/
  private  dataRefresh():void {

    try {
      //Check on the comboOpen flag, do not want to update if there is a combobox open as this would cause it to close
      if ( !this.comboOpen )
      {
        this.updateData("yes");
      }
    } catch ( error ) {
      console.log(error)
    }
  }



  tabIndexchangeHandler(): void {
    try {

      let todayDate;
      todayDate = new Date(CommonUtil.parseDate(this.sysDateFrmSession, this.dateFormat.toUpperCase()));
      todayDate.setHours(12, 0, 0);
      if (this.tabs.selectedLabel != 'Selected') {
        // this.sysDateFrmSession = this.tabs.selectedLabel + year;
        this.startDate.selectedDate = new Date(todayDate.setDate(todayDate.getDate() + this.tabs.selectedIndex));
      }
      this.updateData("no");

    } catch (e) {
      console.log('tabIndexchangeHandler error', e)
    }
  }

  /**
   * comboBoxClosed()
   *
   * @param event:Event
   *
   * Set variable comboOpen false, method called when close comboBox
   **/
  closedCombo(event: Event): void {
    this.comboOpen = false;
  }

  /**
   * This method is used to handle the entity change.
   * This loads the entity with the default currency.
   */
  entityChangeCombo(){

    //this.selectedEntity.text= this.entityCombo.selectedItem.value;
    this.comboChange=true;
    this.entityComboChange=true;
    this.updateData("no");

  }

  /*change data grid*/
  updateData(autorefresh: string): void {
    let currGrp: string = "";
    this.requestParams = [];
    this.selectedSort = this.getSortedGridColumn();
    /* this.inputData.cbResult = (event) => {
       this.inputDataResult(event);
     };*/
    if (!this.entityComboChange)
    {
      currGrp=this.ccyCombo.selectedItem.content;
    }
    this.inputData.cbStart= this.startOfComms.bind(this);
    this.inputData.cbStop= this.endOfComms.bind(this);
    this.inputData.encodeURL = false;
    /*Define the action to send the request*/
    this.actionPath = "acctmonitornew.do?";
    this.actionMethod = "method=displayFilteredDetails";
    this.requestParams["accountMonitorNew.dateAsString"] = this.startDate.text;
    this.requestParams["accountMonitorNew.entityId"] = this.entityCombo.selectedItem.content;
    this.requestParams["accountMonitorNew.currencyCode"] = currGrp;
    this.requestParams["accountMonitorNew.accountType"] = this.typeCombo.selectedItem.value;
    this.requestParams["accountMonitorNew.accountClass"] = this.classCombo.selectedItem.value;
    this.requestParams["applyCurrencyThreshold"] = (this.currencyThreshold.selected ? "Y" : "N");
    this.requestParams["hideZeroBalances"] = (this.hideZero.selected ? "Y" : "N");
    this.requestParams["selectedTabIndex"] = this.tabs.selectedIndex;
    this.requestParams["autoRefresh"] = autorefresh;
    this.requestParams["systemDate"] = this.sysDateFrmSession;
    //Then apply them to the url member of the HTTPComms object:

    this.inputData.url = this.baseURL + this.actionPath +this.actionMethod;
    this.requestParams["callFrom"] = this.callFrom;
    //set menuAccessId in params
    this.requestParams["menuAccessId"] = this.menuAccessId;
    this.requestParams["existingEntityId"] = this.jsonReader.getScreenAttributes()["existingEntityId"];
    this.inputData.send(this.requestParams);

  }

  /**
   * changeCombo
   * @param event
   */
  changeCombo(event: Event): void {
    this.acctSelectedIndex = null;
    this.updateData("no");

  }
  handleDate() :void {
    let exist = false;
    let tempDate= moment(this.sysDateFrmSession, this.dateFormat.toUpperCase());
    let selectedDate = moment(this.startDate.text, this.dateFormat.toUpperCase());
    if(selectedDate) {
      for (let index = 0; index < this.tabs.getTabChildren().length -1; index++) {

        tempDate= moment(this.sysDateFrmSession, this.dateFormat.toUpperCase()).add(index, 'days');

        if(selectedDate.diff(tempDate) == 0){
          exist = true;
          this.tabs.selectedIndex = index;
          break;
        }
      }
    }
    if(!exist){
      this.tabs.selectedIndex = 7;
    }
  }
  validateDate() : void {
    if(this.validateDateField(this.startDate)){
      this.handleDate();
      this.updateData("no");
    } else {

    }
  }
  validateDateField(dateField){
    try{
      let date;
      let alert = SwtUtil.getPredictMessage('alert.enterValidDate', null);
      if(dateField.text) {

        date = moment(dateField.text, this.dateFormat.toUpperCase() , true);
        if(!date.isValid()) {
          this.swtAlert.error(alert, null, null, null,  ()=> {
            this.setFocusDateField(dateField)
          });
          return false;
        }
      } else {
        this.swtAlert.error(alert, null, null, null,  ()=> {
          this.setFocusDateField(dateField)
        });
        return false;
      }
      dateField.selectedDate = date.toDate();
    }catch(error){
    }

    return true;
  }
  setFocusDateField(dateField) {
    dateField.setFocus();
    dateField.text = this.jsonReader.getScreenAttributes()["datefrom"]
  }
  doHelp(): void {

    ExternalInterface.call("help");
    // try {
    //   SwtHelpWindow.open(this.baseURL + "help.do?method=print&screenName=Account+Monitor");
    // } catch (error) {
    //   SwtUtil.logError(error, "", "doHelp", "", null);
    //   //this.logger.error("method [doHelp] - error ", error);
    // }
  }

  optionsHandler(): void {
    try {
      clearInterval(this.interval);
      this.win =  SwtPopUpManager.createPopUp(this, OptionsSetting, {
        title: "Options", //SwtUtil.getPredictMessage('cutOff.title.addRule', null), // childTitle,
        fontSizeValue: this.selectedFont,
        refreshText: this.refreshRate.toString()
      });
      this.win.isModal = true;
      this.win.enableResize = false;
      this.win.width = '370';
      this.win.height = '190';
      this.win.showControls = true;
      this.win.id = "optionsAccountMonitor";
      this.win.onClose.subscribe((res) => {
        this.submitFontSize(res);
        setTimeout(() => {
          this.submitRate(res);
        }, 0);

      });

      this.win.display();

    }
    catch (error) {
    }
  }
  /**
   * This function is used to set the refresh rate for the monitors.
   */
  submitRate(res) : void {
    let notANumber = "Not a number";
    let minRate = "Refresh rate selected was below minimum.\nSet to 5 seconds";
    if (isNaN(res.refreshRate ) || res.refreshRate == "" ) {
      //this.refreshRate =5;
      this.swtAlert.error(notANumber)
    }  else {
      this.refreshRate=Number(res.refreshRate);
      if( this.refreshRate < 5) {
        this.refreshRate =5;
        this.swtAlert.error(minRate);
      }
    }

    let request: string = ExternalInterface.call("getUpdateRefreshRequest", this.refreshRate);
    if (request != null && request != "")
    {
      let originalURL ;
      // Sets the original URL
      originalURL=this.updateFontSize.url;
      // Sets the request in the URL
      this.updateRefreshRate.url = this.baseURL + request;
      // Calls the send function
      this.updateRefreshRate.send();
      // Sets the URL
      this.updateRefreshRate.url=originalURL;
    }
    this.autoRefreshAfterStop();


  }
  submitFontSize(event): void {
    // Gets the RadioButton value from RefreshPopUp
    this.fontValue=event.fontSize.value;
    /*Sets the screen Id based on the monitor type*/
    if (this.fontValue == "N")
    {
      this.selectedFont=0;

      this.fontLabel =event.fontSize.label;
      this.accountGrid.styleName="dataGridNormal";
      this.accountGrid.rowHeight=18;

      this.fontRequest=ExternalInterface.call("getUpdateFontSize", this.fontLabel);
    }
    else if (this.fontValue == "S")
    {
      this.selectedFont=1;
      this.fontLabel= event.fontSize.label;
      this.accountGrid.styleName="dataGridSmall";
      this.accountGrid.rowHeight=15;
      // Sets the style for totalsGrid if the monitor type is Book
      this.fontRequest=ExternalInterface.call("getUpdateFontSize", this.fontLabel);
    }
    // Updates the HttpComms with the given request if its available
    if (this.fontRequest != null && this.fontRequest != "")
    {
      let originalURL ;
      // Sets the original URL
      originalURL=this.updateFontSize.url;
      // Sets the request in the URL
      this.updateFontSize.url = this.baseURL + this.fontRequest;
      // Calls the send function
      this.updateFontSize.send();
      // Sets the URL
      this.updateFontSize.url=originalURL;


      // this.updateFontSize.getThis(this.baseURL + this.fontRequest);
    }
  }




  closeHandler() {
    ExternalInterface.call("close");
  }

  /**
   * SortedGridColumn
   *
   *
   * This function is used to get the sorted column and its direction from the  grid
   */
  getSortedGridColumn(): string {
    //sortDirection
    let sortDirection: boolean = false;
    // Index of the sorted column
    let sortColumnIndex: number = -1;
    let selectedSort = '';
    try {
      // this.logger.info("method [getSortedGridColumn] - START  ");

      //Get the sortColumnIndex
      sortColumnIndex = this.accountGrid.sortColumnIndex;
      // Grid columns
      let gridColumns = this.accountGrid.getFilterColumns();
      if (sortColumnIndex != -1) {
        //sort direction
        sortDirection = this.accountGrid.sortDirection;
        selectedSort = gridColumns[sortColumnIndex].field + "|" + sortDirection + "|";

      } else {
        selectedSort = null;
      }
      // this.logger.info("method [getSortedGridColumn] - END  ");

      // return selected Sort

      return selectedSort;
    }
    catch (error) {
      console.log(error);
      //this.logger.error("Method [getSortedGridColumn] - error ",error, "- errorLocation = ",errorLocation);
    }
  }
  /**
   * If a fault occurs with the connection with the server then display the lost connection label
   **/
  public inputDataFault( event ):void {
    this.lostConnectionText.visible = true;
    let message = SwtUtil.getPredictMessage('label.genericException', null);
    this.swtAlert.error(message);

  }
  /**
   * connError
   *
   * Mouse event to handle check the availability of connection
   */
  private connError() :void {
    this.SwtAlert.error( "" + this.invalidComms );
  }
  /**
   * START : following code are handlers for the button presses SUM, MOVE, SWEEP and open Unexpected
   **/
  sumHandler() : void {
    let sEntity: string= this.entityCombo.selectedLabel;
    let sCCY: string= this.accountGrid.dataProvider[this.accountGrid.selectedIndex].ccy;
    let sAccount: string= this.accountGrid.dataProvider[this.accountGrid.selectedIndex].account;
    let sSum: string= this.accountGrid.dataProvider[this.accountGrid.selectedIndex].sum;
    let sSummable: string= (this.accountGrid.selectedItem.sum.summable) ? this.accountGrid.selectedItem.sum.summable : "false" ;
    let testMessageY = 'Exclude this account and all linked accounts from the totals?';
    let testMessageN = 'Include this account and all linked accounts in the totals?';
    let testMessageP = 'Include/Exclude this account and all linked accounts from the totals?';
    let testMessageChanges = testMessageP+ " This change will not update accounts that are flagged to sum according to cut-off";

    let operation = null;
    let d = new Date();
    let t = d.getTime();
    clearInterval(this.interval);
    if (sSum == "Y") {
      operation = "ExcludeFromTotals";
      this.swtAlert.question(testMessageY, null,Alert.OK | Alert.CANCEL, null, (data) => {this.sumRequest(data, sEntity, sCCY, sAccount, operation, t) }, Alert.CANCEL );
    } else if(sSum == "N") {
      operation = "IncludeInTotals";
      this.swtAlert.question(testMessageN, null,Alert.OK | Alert.CANCEL, null, (data) => {this.sumRequest(data, sEntity, sCCY, sAccount, operation, t) }, Alert.CANCEL);
    } else if(sSum == "P") {
      Alert.yesLabel = "Include";
      Alert.noLabel = "Exclude";
      if (sSummable == "true") {
        this.swtAlert.question(testMessageChanges, null,Alert.YES | Alert.NO  | Alert.CANCEL, null, (data) => {this.sumRequest(data, sEntity, sCCY, sAccount, operation, t) }, Alert.CANCEL  );

      } else {
        this.swtAlert.question(testMessageP, null,Alert.YES | Alert.NO  | Alert.CANCEL, null, (data) => {this.sumRequest(data, sEntity, sCCY, sAccount, operation, t) }, Alert.CANCEL  )
      }
    } else if (sSum == "C" && sSummable == "true") {
      Alert.yesLabel = "Include";
      Alert.noLabel = "Exclude";
      this.swtAlert.question(testMessageChanges, null,Alert.YES | Alert.NO  | Alert.CANCEL, null, (data) => {this.sumRequest(data, sEntity, sCCY, sAccount, operation, t) }, Alert.CANCEL  )
    }

  }

  sumRequest(event, sEntity, sCCY, sAccount, operation, t) : void {
    if(event.detail == Alert.YES) {
      operation = "IncludeInTotals";
    } else if (event.detail == Alert.NO) {
      operation = "ExcludeFromTotals";
    } else if(event.detail == Alert.OK) {

    } else {
      this.autoRefreshAfterStop();
      return;
    }
    this.logicUpdate.encodeURL = false;
    this.actionPath = "acctmonitornew.do?";
    this.actionMethod = "method=updateMonitorSumFlag";
    this.requestParams = [];
    this.requestParams["operation"] = operation;
    this.requestParams["accountId"] = sAccount;
    this.requestParams["accountMonitorNew.entityId"] = sEntity;
    this.requestParams["accountMonitorNew.currencyCode"] = sCCY;
    this.requestParams["nocache"] = t;
    this.requestParams["method"] = "updateMonitorSumFlag";
    this.logicUpdate.url = this.baseURL + this.actionPath + this.actionMethod;
    this.logicUpdate.send(this.requestParams);
    this.sumButton.enabled=false;
    this.sumButton.buttonMode=false;
    this.updateData("no");

  }


  moveHandler():void {
    try {
      let testMessageY: string = 'Move balance to Loro/Curr column?';
      let testMessageN: string = 'Move balance to Predicted column?';
      let operation: string = "";
      let sEntity: string= this.entityCombo.selectedLabel;
      let sCCY: string= this.accountGrid.dataProvider[this.accountGrid.selectedIndex].ccy;
      let sAccount: string= this.accountGrid.dataProvider[this.accountGrid.selectedIndex].account;
      let sL2P: string=this.accountGrid.dataProvider[this.accountGrid.selectedIndex].slickgrid_rowcontent.lorotopredictedflag.content;
      let d = new Date();
      let t = d.getTime();
      clearInterval(this.interval);
      //get the move url
      if (sL2P == "Y") {
        operation = "MoveToLoro";
        this.swtAlert.question(testMessageY, null,Alert.OK | Alert.CANCEL, null, (data) => {this.moveRequest(data, sEntity, sCCY, sAccount, operation, t)} , Alert.CANCEL)

      } else if (sL2P == "N") {
        operation = "MoveToPredicted";
        this.swtAlert.question(testMessageN, null,Alert.OK | Alert.CANCEL, null, (data) => {this.moveRequest(data, sEntity, sCCY, sAccount, operation, t)}, Alert.CANCEL)
      }
    } catch (e) {
      console.log('error in move ', e)
    }

  }
  moveRequest( event, sEntity, sCCY, sAccount, operation, t): void {
    if(event.detail == Alert.OK) {
      this.requestParams = [];
      /*Define the action to send the request*/
      this.actionPath = "acctmonitornew.do?";
      this.actionMethod = "method=updateLoroToPredictedFlag";
      this.requestParams["operation"] = operation;
      this.requestParams["accountId"] = sAccount;
      this.requestParams["accountMonitorNew.entityId"] = sEntity;
      this.requestParams["accountMonitorNew.currencyCode"] = sCCY;
      this.requestParams["nocache"] = t;
      this.requestParams["method"] = "updateLoroToPredictedFlag";
      this.logicUpdate.encodeURL = false;
      this.logicUpdate.url = this.baseURL + this.actionPath + this.actionMethod;
      this.logicUpdate.send(this.requestParams);
      this.sumButton.enabled=false;
      this.sumButton.buttonMode=false;
      this.updateData("no");
    } else  {
      this.autoRefreshAfterStop();
      return;
    }
  }
  swpHandler() : void {
    let sEntity: string= this.entityCombo.selectedLabel;
    let sCCY: string= this.ccyCombo.selectedLabel;
    let sAccountType: string=null;
    if (this.typeCombo.selectedIndex > -1) {
      sAccountType = this.typeCombo.selectedItem.value
    }
    this.swp(sEntity, sCCY, sAccountType);

  }
  swp(sEntity: string, sCCY:string, sAccountType:string):void
  {
    //call the link for open  manul sweeping
    ExternalInterface.call("openManualsweep", sEntity, sCCY, sAccountType);
  }
  report(type) : void {
    let selects = [];
    selects.push(ExternalInterface.call('getBundle', 'text', 'label-entity', 'Entity')+"="+ this.entityCombo.selectedLabel);
    selects.push("Ccy=" + this.ccyCombo.selectedLabel);
    selects.push(ExternalInterface.call('getBundle', 'text', 'label-accountType', 'Account Type')+"=" + this.typeCombo.selectedLabel);
    selects.push(ExternalInterface.call('getBundle', 'text', 'label-accountClass', 'Account Class')+"=" + this.classCombo.selectedLabel);
    selects.push("Date=" + this.startDate.text);
    selects.push(ExternalInterface.call('getBundle', 'text', 'label-startDayBalance', 'Start of Day Balance')+"=" + (this.sodText.text.toString() != "" ? this.sodText.text : "None"));
    selects.push(ExternalInterface.call('getBundle', 'text', 'label-openUnexpected', 'Open Unexpected')+"="+ (this.unexpectedText.text.toString() != "" ? this.unexpectedText.text :  "None" ));
    selects.push(ExternalInterface.call('getBundle', 'text', 'label-hideZeroBalances', 'Hide Zero Balance')+"="+((this.hideZero.selected) ? "On" : "Off"));
    selects.push(ExternalInterface.call('getBundle', 'text', 'label-currencyThresold', 'Currency Threshold')+"="+((this.currencyThreshold.selected) ? "On" : "Off"));
    this.dataExport.convertData(this.lastRecievedJSON.accountmonitor.grid.metadata.columns , this.accountGrid, this.totalGrid.gridData , selects,type, true);


  }
  autoRefreshAfterStop() {
    let timeLeft = this.refreshRate;
    this.interval = setInterval(() => {
      this.dataRefresh();
    }, timeLeft*1000)
  }

  unexpectedHandler():void
  {
    let sEntity:string=(this.entityCombo.selectedLabel).toString();
    let sCCY: string=(this.ccyCombo.selectedLabel).toString();
    let sAccount:string="";
    let sD1:string= this.startDate.text;

    let sAccountType:string=this.typeCombo.selectedValue;
    let currThreshold:String=(this.currencyThreshold.selected ? "Y" : "N");
    if (sEntity.length > 0) {
      if (sCCY != "All") {
        ExternalInterface.call("showOpenUnexpectedMovs", sEntity, sCCY, sAccount, sD1, sAccountType, currThreshold);
      }
    }

  }
  underlineText(){
    this.unexpectedText.setStyle('text-decoration', 'underline', this.unexpectedText.domElement);
  }
  removeUnderline(){
    this.unexpectedText.setStyle('text-decoration', 'none', this.unexpectedText.domElement);
  }



}
//Define lazy loading routes
const routes: Routes = [
  { path: '', component: AccountMonitor }
];
export const routing: ModuleWithProviders = RouterModule.forChild(routes);
//Define an NgModule that will be loaded by app module
@NgModule({
  imports: [routing, SwtToolBoxModule],
  declarations: [AccountMonitor],
  entryComponents: []
})
export class AccountMonitorModule {}
