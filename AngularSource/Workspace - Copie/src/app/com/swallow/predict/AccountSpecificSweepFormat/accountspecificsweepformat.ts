import { Component, ViewChild, NgModule, ElementRef, OnInit, OnDestroy } from '@angular/core';
import {
	SwtToolBoxModule,
	SwtModule,
	CommonService,
	SwtAlert,
	SwtCommonGrid,
	SwtCanvas,
	JSONReader,
	ExternalInterface,
	HTTPComms,
	SwtButton,
	SwtLoadingImage,
	Logger,
	SwtComboBox,
	SwtLabel,
	TitleWindow,
	ModuleLoader,
	Alert,
	SwtUtil,
} from "swt-tool-box";
import { Routes, RouterModule } from "@angular/router";
import { ModuleWithProviders } from "@angular/compiler/src/core";
declare var instanceElement: any;
/**
 * <AUTHOR> <PERSON>
 */

@Component({
	selector: 'app-accountspecificsweepformat',
	templateUrl: './accountspecificsweepformat.html'
})
export class AccountSpecificSweepFormat extends SwtModule implements OnDestroy,OnInit{

	private swtAlert: SwtAlert;
	private logger: Lo<PERSON>;
	private child: TitleWindow
	private mLoader: ModuleLoader = null;
	@ViewChild("cvGridContainer") cvGridContainer: SwtCanvas
	@ViewChild("addButton") addButton: SwtButton
	@ViewChild("changeButton") changeButton: SwtButton
	@ViewChild("deleteButton") deleteButton: SwtButton
	@ViewChild("viewButton") viewButton: SwtButton
	@ViewChild("closeButton") closeButton: SwtButton
	@ViewChild('loadingImage') loadingImage: SwtLoadingImage;
	@ViewChild("entityCombo") entityCombo: SwtComboBox
	@ViewChild("ccyCombo") ccyCombo: SwtComboBox
	@ViewChild("accountCombo") accountCombo: SwtComboBox
	@ViewChild("selectedEntity") selectedEntity: SwtLabel
	@ViewChild("selectedCcy") selectedCcy: SwtLabel
	@ViewChild("selectedAccount") selectedAccount: SwtLabel

	constructor(private commonService: CommonService, private element: ElementRef) {
		super(element, commonService);
		this.logger = new Logger('Account Specific Sweep Format', this.commonService.httpclient);
		this.swtAlert = new SwtAlert(commonService);
		window["Main"] = this;
	}
	/**                                                                                       
		   * Display Objects                                                                        
		   **/
	private _accountSpecificSweepFormatGrid: SwtCommonGrid = null;
	/**                                                                                       
	 * Data Objects                                                                           
	 **/
	private jsonReader: JSONReader = new JSONReader();
	private lastRecievedJSON: any = null;
	// Variable to hold  menuAccessId                                                         
	private _menuAccessId: string = null;
	// Variable to hold _isPublic                                                             
	private _isPublic: string = null;
	// Variable to hold _comboOpen
	private comboOpen: Boolean = false;
	// Variable holds the name of the screen      
	//TODO:                                            
	private _screenName: string = ExternalInterface.call('getBundle', 'text', 'screen-tilte', 'AccountSpecificSweepFormat Screen');
	// private _screenName="AccountSpecificSweepFormat Screen"                                                                             
	// Variable that holds the version number for this mxml                                                       
	private _versionNumber: string = "1";
	/**                                                                                                           
	 * Communication Objects                                                                                      
	 **/
	private _inputData = new HTTPComms(this.commonService);
	private _baseURL: string = SwtUtil.getBaseURL();
	private _actionMethod: string = null;
	private _actionPath: string = null;
	private _requestParams = new Array();
	private _invalidComms: string;
	private _closeWindow: Boolean = false;

	// Variable to hold entityId
	private entityId: string = null;
	// Variable to hold currencyCode
	private currencyId: string = null;


	private accountId: string = null;

	private specifiedAccountId: string = null;
	private specifiedEntityId: string = null;
	private idCsrf: string = null;
	private parentMethodName: string = null;

	private previousSelectedIndex: number = -1;

	ngOnDestroy(): any {
		instanceElement = null;
	  }
	  ngOnInit(): void {

		instanceElement = this;
		this._accountSpecificSweepFormatGrid = <SwtCommonGrid>this.cvGridContainer.addChild(SwtCommonGrid);
    this.addButton.toolTip = SwtUtil.getPredictMessage('button.add', null);
    this.changeButton.toolTip = SwtUtil.getPredictMessage('button.change', null);
    this.viewButton.toolTip = SwtUtil.getPredictMessage('button.viewButton', null);
    this.deleteButton.toolTip = SwtUtil.getPredictMessage('button.delete', null);
    this.closeButton.toolTip = SwtUtil.getPredictMessage('button.close', null);
		
	
	  }
	/**                                                                                                           
	 * Upon completion of loading into the flash player this method is called                                     
	 **/
	onLoad(): void {
		this._accountSpecificSweepFormatGrid.uniqueColumn = "specifiedAccountId";
		try {
			// _baseURL=obtainURL();                                                                                     
			// set version number                                                                                        
			// Read menua cces from request                                                                           
			this._menuAccessId = ExternalInterface.call('eval', 'menuAccessId');
			this.entityId = ExternalInterface.call('eval', 'entityId');
			this.accountId = ExternalInterface.call('eval', 'accountId');
			this.currencyId = ExternalInterface.call('eval', 'currencyId');
			this.parentMethodName = ExternalInterface.call('eval', 'parentMethodName');
			this.idCsrf = ExternalInterface.call('eval', 'id');
			// add call back method, to call the given method from jsp         
			//TODO:                                       
			// ExternalInterface.addCallback("refreshdetails", refreshdetails);
			//Add the event listener to listen for a cell click on a datagrid, be it the main one or a totals grid    
			this._accountSpecificSweepFormatGrid.cellClick.subscribe((selectedRowData) => {
				this.cellLogic(event);
			});
			// this.addEventListener("filterUpdate", cellLogic, true);                                                 
			// this.addEventListener("MenuClick", hideShowColumn, true);                                               
			// Condition to check menuAccess is not full access   
			if (this._menuAccessId != "0") {
				this.addButton.enabled = false;
			}

			this._inputData.cbStart = this.startOfComms.bind(this);
			this._inputData.cbStop = this.endOfComms.bind(this);
			//result event                                                                                            
			this._inputData.cbResult = (data) => {
				this.inputDataResult(data);
			};
			//fault event                                                                                             
			this._inputData.cbFault = this.inputDataFault.bind(this);
			this._inputData.encodeURL = false;
			//action url	                                                                                          
			this._actionPath = "accountSpecificSweepFormat.do?method=";
			//Then declare the action method:					                                                      
			this._actionMethod = "displayAccountSpecificSweepFormatList";
			this._actionMethod = this._actionMethod + "&entityId=" + this.entityId;
			this._actionMethod = this._actionMethod + "&currencyId=" + this.currencyId;
			this._actionMethod = this._actionMethod + "&accountId=" + this.accountId;
			this._actionMethod = this._actionMethod + "&parentMethodName=" + this.parentMethodName;
			this._actionMethod = this._actionMethod + "&id=" + this.idCsrf;
			//Then apply them to the url member of the HTTPComms object:                                              
			this._inputData.url = this._baseURL + this._actionPath + this._actionMethod;
			//Make initial request                                                                                    
			this._inputData.send(this._requestParams);

		} catch (error) {
			this.logger.error("Method [onLoad]", error);
		}

	}

	/**                                                                                                                                   
	 * This method is called by the HTTPComms when result event occurs.                                                                   
	 * @param event:ResultEvent                                                                                                           
	 * */
	private inputDataResult(event): void {
		
		//get the received xml  
		try {
			this.lastRecievedJSON = event;
			this.jsonReader.setInputJSON(this.lastRecievedJSON);
			//test the reply status first, as if the reply status is false then the data will not be valid                                    
			if (this.jsonReader.getRequestReplyStatus()) {
				//Some monitors have a database job that runs to build the data.                                                              
				//If this is running then the databuilding flag will be set                                                                   
				/*                                                                                                                        
				If the main datagrid has not been initialize, for example it is the first time data as been recieved then initialise it   
				and add it to the appropriate display container.                                                                          
				*/

				if (!this.jsonReader.isDataBuilding()) {
					//If the code has reached this point then the database is not databuilding, turn off the dataBuildingText 
					if (this._accountSpecificSweepFormatGrid.columnDefinitions.length == 0) {
						this._accountSpecificSweepFormatGrid.CustomGrid(this.lastRecievedJSON.accountspecificsweepformat.grid.metadata);                                                                          
						this._accountSpecificSweepFormatGrid.colWidthURL ( this._baseURL+"accountSpecificSweepFormat.do?screenName=accountSpecificSweepFormat");
						this._accountSpecificSweepFormatGrid.colOrderURL ( this._baseURL+"accountSpecificSweepFormat.do?screenName=accountSpecificSweepFormat");

						this._accountSpecificSweepFormatGrid.saveWidths = true;
						this._accountSpecificSweepFormatGrid.saveColumnOrder = true;
					}
					//set the grid row data
          this._accountSpecificSweepFormatGrid.CustomGrid(this.lastRecievedJSON.accountspecificsweepformat.grid.metadata);
          this._accountSpecificSweepFormatGrid.gridData = this.jsonReader.getGridData();
					//set the row size                                                                                                    
					this._accountSpecificSweepFormatGrid.setRowSize = this._accountSpecificSweepFormatGrid.gridData.length;
					this.entityCombo.setComboData(this.jsonReader.getSelects(), false);
					// this.selectedEntity.text = this.entityCombo.textLabel;
					this.selectedEntity.text = this.entityCombo.selectedValue;
					this.ccyCombo.setComboData(this.jsonReader.getSelects(), false);
					// this.selectedCcy.text = this.ccyCombo.textLabel;
					this.selectedCcy.text = this.ccyCombo.selectedValue;
					this.accountCombo.setComboData(this.jsonReader.getSelects(), false);
					if (this.parentMethodName == "add") {
						this.selectedAccount.text = '';
						this.accountCombo.selectedIndex = -1;
					} else {
						// this.selectedAccount.text = this.accountCombo.textLabel;
						this.selectedAccount.text = this.accountCombo.selectedValue;

					}
					this._menuAccessId = this.jsonReader.getSingletons().menuAccessId;
					if (this._menuAccessId == "0" && this.parentMethodName != "view") {
						this.addButton.enabled = true;
					} else {
						this.addButton.enabled = false;
					}
					if (this.previousSelectedIndex != -1 && this._accountSpecificSweepFormatGrid.gridData.length >= this.previousSelectedIndex + 1) {
						this._accountSpecificSweepFormatGrid.selectedIndex = this.previousSelectedIndex;
					}

					this.cellLogic(null);

				}

			}
			else {
				if (this.lastRecievedJSON.hasOwnProperty("request_reply")) {
					//get the attribute for closewindow  flag                                                                                 
					if (this.lastRecievedJSON.accountspecificsweepformat.request_reply.closewindow == "true") {
						this._closeWindow = true;
					}
				}
				this.swtAlert.show(this.jsonReader.getRequestReplyMessage() + "\n" + this.jsonReader.getRequestReplyLocation(), "Error", Alert.OK, this, this.errorHandler.bind(this));
			}


			this._accountSpecificSweepFormatGrid.validateNow();
		} catch (error) {
			this.logger.error("Method [inputDataResult]", error);
			this.swtAlert.show(ExternalInterface.call('eval', 'label[\'alert\'][\'server_error\']'));
		}
	}

	/**                                                                                                                  
	 * If a fault occurs with the connection with the server then display the lost connection label                      
	 * @param event:FaultEvent                                                                                           
	 **/
	private inputDataFault(event): void {
		this._invalidComms = event.fault.faultString + "\n" + event.fault.faultCode + "\n" + event.fault.faultDetail;
		this.swtAlert.show("fault " + this._invalidComms);
	}


	/**                                                                                                                  
	 * Method used for call back, to refresh the grid data.                                                              
	 **/
	refreshdetails(): void {
		try {
			this.previousSelectedIndex = this._accountSpecificSweepFormatGrid.selectedIndex;
			this.entityId = this.entityCombo.selectedLabel;
			this.currencyId = this.ccyCombo.selectedLabel;
			this.accountId = this.accountCombo.selectedLabel;
			// this.entityId = this.entityCombo.selectedValue;
			// this.currencyId = this.ccyCombo.selectedValue;
			// this.accountId = this.accountCombo.selectedValue;


			this._inputData.cbStart = this.startOfComms.bind(this);
			this._inputData.cbStop = this.endOfComms.bind(this);
			//result event                                                                                                   
			this._inputData.cbResult = (data) => {
				this.inputDataResult(data);
			};
			//fault event                                                                                                    
			this._inputData.cbFault = this.inputDataFault.bind(this);
			this._inputData.encodeURL = false;
			//action url	                                                                                                 
			this._actionPath = "accountSpecificSweepFormat.do?method=";
			//Then declare the action method:					                                                             
			this._actionMethod = "displayAccountSpecificSweepFormatList";
			this._actionMethod = this._actionMethod + "&entityId=" + this.entityId;
			this._actionMethod = this._actionMethod + "&currencyId=" + this.currencyId;
			this._actionMethod = this._actionMethod + "&accountId=" + this.accountId;
			this._actionMethod = this._actionMethod + "&parentMethodName=" + this.parentMethodName;
			this._actionMethod = this._actionMethod + "&id=" + this.idCsrf;
			//Add the event listener to listen for a cell click on a datagrid, be it the main one or a totals grid 
			//TODO:          
			// this.addEventListener("CellClick", cellLogic, true);                                                           
			//Then apply them to the url member of the HTTPComms object:                                                     
			this._inputData.url = this._baseURL + this._actionPath + this._actionMethod;
			//Make initial request                                                                                           
			this._inputData.send(this._requestParams);
		} catch (error) {
			this.logger.error("Method [refreshdetails]", error);
		}
	}
	/**                                                                                                                  
	 * Method used to open "AccountSpecificSweepFormat" screen to add                                                            
	 *  a new Account Attribute HDR                                                                                      
	 *                                                                                                                   
	 **/
	addAccountSpecificSweepFormat(): void {
		this._screenName = "addScreen";
		/* Url to load Define Attribute screen */
		this._actionMethod = "displayAccountSpecificSweepFormat&screenName=" + this._screenName + "&parentMethodName=" + this.parentMethodName;
		ExternalInterface.call("openChildWindow", this._actionMethod);
	}
	/**                                                                                                                  
	 * Method used to open "AccountSpecificSweepFormat" screen to change or View                                                 
	 * an "AccountSpecificSweepFormat"                                                                                         
	 */
	changeViewAccountSpecificSweepFormat(isChange): void {
		/* Url to load change AccountSpecificSweepFormat screen */
		this._actionMethod = "displayAccountSpecificSweepFormat";
		this._actionMethod = this._actionMethod + "&entityId=" + this.entityId;
		this._actionMethod = this._actionMethod + "&accountId=" + this.accountId;
		this._actionMethod = this._actionMethod + "&specifiedAccountId=" + this.specifiedAccountId;
		this._actionMethod = this._actionMethod + "&specifiedEntityId=" + this.specifiedEntityId;
		this._actionMethod = this._actionMethod + "&id=" + this.idCsrf;
		this._screenName = isChange ? "changeScreen" : "viewScreen";
		this._actionMethod = this._actionMethod + "&screenName=" + this._screenName;
		this._actionMethod = this._actionMethod + "&parentMethodName=" + this.parentMethodName;
		ExternalInterface.call("openChildWindow", this._actionMethod);
	}

	/**                                                                                                                  
	  * Method to pop up delete confirmation to remove an account AccountSpecificSweepFormat                                         
	 * @param event: Event                                                                                               
	 */
	deleteAccountSpecificSweepFormat(): void {
		try {

			this.swtAlert.show(
				//TODO:
				ExternalInterface.call('getBundle', 'text', 'delete-record', 'Are you sure you want to delete?'), //message                        
				ExternalInterface.call('getBundle', 'text', 'delete-confirm', 'Confirm Deletion'),//pop up title                        
				Alert.YES | Alert.NO,
				null, //no parent                                                                                            
				this.deletionDecision.bind(this), // close handler                                                                     
				null, 0); //icon and default button     
		} catch (error) {
			this.logger.error("Method [deleteAccountSpecificSweepFormat]", error);
		}
	}



	/**                                                                                                                  
	 * Method used to know if the decision to delete (or not) has been made                                              
	 * @param event: CloseEvent                                                                                          
	 */
	deletionDecision(event): void {

		try {

			this._inputData.cbStart = this.startOfComms.bind(this);
			this._inputData.cbStop = this.endOfComms.bind(this);
			this._inputData.cbResult = (data) => {
				this.inputDataResult(data);
			};
			this._inputData.cbFault = this.inputDataFault.bind(this);
			this._inputData.encodeURL = false;
			this._actionMethod = "deleteAccountSpecificSweepFormat";
			this._requestParams["entityId"] = this.entityId;
			this._requestParams["accountId"] = this.accountId;
			this._requestParams["currencyId"] = this.currencyId;
			this._requestParams["menuAccessId"] = this._menuAccessId;
			this._requestParams["id"] = this.idCsrf;
			this._requestParams["specifiedAccountId"] = this.specifiedAccountId;	
			this._requestParams["specifiedEntityId"] = this.specifiedEntityId;	
			this._requestParams["parentMethodName"] = this.parentMethodName;
			/* Delete account attribute HDR and related records from                                                         
				 functional group and account attribute */
			if (event.detail == Alert.YES) {
				this._inputData.url = this._baseURL + this._actionPath + this._actionMethod;
				this._inputData.send(this._requestParams);
			}
		} catch (error) {
			this.logger.error("Method [refreshdetails]", error);
		}

	}

	/**                                                                                                                  
	 * close the window from the close button                                                                            
	 **/
	closeHandler(): void {
		//window.opener.instanceElement.updateSpecificAcctCount(this._accountSpecificSweepFormatGrid.gridData.length);
		//call for close window                                                                                          
		ExternalInterface.call("closeHandler");
	}

	/**                                                                                                                  
	 * When item click on the datagrd is method will be called                                                           
	 * @param e:CellEvent                                                                                                
	 **/
	cellLogic(e): void {
		var selectedRow: number = this._accountSpecificSweepFormatGrid.selectedIndex;
		if (selectedRow > -1) {
			this.entityId = this._accountSpecificSweepFormatGrid.dataProvider[selectedRow].entityId;
			this.accountId = this._accountSpecificSweepFormatGrid.dataProvider[selectedRow].accountId;
			this.specifiedAccountId = this._accountSpecificSweepFormatGrid.dataProvider[selectedRow].specifiedAccountId;
			this.specifiedEntityId = this._accountSpecificSweepFormatGrid.dataProvider[selectedRow].specifiedEntityId;
			this.disableOrEnableButtons(true);

		}
		else {
			this.disableOrEnableButtons(false);
		}

	}
            
	/**                                                                                                                  
	 * Method to disable or enable buttons                                                                               
	 * @param isRowSelected:Boolean                                                                                      
	 * @return                                                                                                           
	 */
	disableOrEnableButtons(isRowSelected): void {
		var menuAccess: Number = parseInt(this._menuAccessId, 10);
		if (isRowSelected) {
			this.changeButton.enabled = (menuAccess == 0 && this.parentMethodName != "view");
			this.deleteButton.enabled = (menuAccess == 0 && this.parentMethodName != "view");
			this.viewButton.enabled = (menuAccess < 2);
		} else {
			this.changeButton.enabled = false;
			this.deleteButton.enabled = false;
			this.viewButton.enabled = false;
		}

	}


	/**                                                                                                                  
	 * Part of a callback function to all for control of the loading swf from the HTTPComms Object                       
	 **/
	startOfComms(): void {
		this.loadingImage.visible = true;
	}

	/**                                                                                                                  
	 * Part of a callback function to all for control of the loading swf from the HTTPComms Object                       
	 **/
	endOfComms(): void {
		this.loadingImage.visible = false;
	}

	/**                                                                                                                  
	 * Listener for Error alert message and                                                                              
	 * perform action for when click ok button                                                                           
	 * @param event:CloseEvent                                                                                           
	 * */
	errorHandler(event): void {
		//check event click is ok button                                                                                 
		if (event.detail == Alert.OK) {
			if (this._closeWindow) {
				//closeHandler();                                                                                        
				this._closeWindow = false;
			}
		}
	}
	hideShowColumn(event): void {
		//TODO:                                                                                                                
		// (_accountSpecificSweepFormatGrid.columns[event.menuDTO["columnIndex"]] as CustomColumn).visible=(event.menuDTO["selected"] == "true") ? true : false;
	}




	/**
	 * When there is a change in the in one of the combo's
	 * @param event :Event
	 **/
	changeCombo(event): void {
		// event.stopImmediatePropagation();
		this.comboOpen = true;
		this.refreshdetails();
	}

	/**
	 * When a combobox is open then any requests to the server need to be cancelled
	 * @param event :Event
	 **/
	openedCombo(event): void {
		this.comboOpen=true;
		// if (event.currentTarget is SwtComboBox)
		// {
		// 	if ((event.type != "HeaderComboOpen"))
		// 	{
		// 		if((event.target as SwtComboBox).isDropDownOpen) 
		// 			(event.target as SwtComboBox).dropDown.height = 215;
		// 	}
		// }

		// if (this._inputData.isBusy())
		// {
		// 	this._inputData.cancel();
		// 	if (event.currentTarget is SwtComboBox)
		// 	{
		// 		(event.currentTarget as SwtComboBox).interruptComms=true;
		// 	}
		// 	else if (event.currentTarget is SwtDateField)
		// 	{
		// 		(event.currentTarget as SwtDateField).interruptComms=true;
		// 	}
		// }
	}

	/**
	 * When the combobox has closed, we need to know if the closure was caused by the user clicking away from the box
	 **/
	closedCombo(event): void {
		this.comboOpen=false; 
		// if ((event.triggerEvent != null) && (event.triggerEvent.type == "mouseDownOutside"))
		// {
		// 	if (event.currentTarget is SwtComboBox)
		// 	{
		// 		if ((event.currentTarget as SwtComboBox).interruptComms)
		// 		{
		// 			(event.currentTarget as SwtComboBox).interruptComms=false;
					this.refreshdetails();
		// 		}
		// 	}

		// }
	}
	doHelp() {
	  ExternalInterface.call('help')
  }

	updateSpecAcctCount(count) {
		window.opener.instanceElement.updateSpecificAcctCount(count);
	}

}


//Define lazy loading routes
const routes: Routes = [
	{ path: '', component: AccountSpecificSweepFormat }
];
export const routing: ModuleWithProviders = RouterModule.forChild(routes);

//Define an NgModule that will be loaded by app module
@NgModule({
	imports: [routing, SwtToolBoxModule],
	declarations: [AccountSpecificSweepFormat],
	entryComponents: []
})
export class AccountSpecificSweepFormatModule { }
