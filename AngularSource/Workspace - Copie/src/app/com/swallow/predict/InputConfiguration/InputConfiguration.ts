import {Component, Inject, OnInit, ViewChild, ModuleWithProviders, NgModule, ElementRef} from '@angular/core';
import {
  SwtCanvas,
  SwtCommonGrid,
  CommonService,
  HTTPComms,
  SwtUtil,
  JSONReader,
  SwtLoadingImage,
  SwtComboBox,
  SwtTextInput,
  SwtDateField,
  SwtLabel,
  SwtHelpWindow,
  SwtCheckBox,
  SwtModule,
  SwtPopUpManager,
  SwtButton,
  SwtAlert,
  SwtTabNavigator,
  Logger,
  Tab,
  SwtText,
  StringUtils,
  ExternalInterface,
  SwtToolBoxModule,
  HBox,
  SwtRadioButtonGroup,
  SwtRadioItem,
  TitleWindow,
  Alert,
  CommonUtil,
  focusManager,
  SwtDataExport,
  Timer,
  ExportEvent, EmailValidator, Encryptor, ContextMenuItem, ScreenVersion, JSONViewer
} from "swt-tool-box";
import {RouterModule, Routes} from "@angular/router";
import {SwtCheckRenderer} from "./SwtCheckRenderer";
/*declare var require: any;
const equal = require('deep-equal');*/
@Component({
  selector: 'app-input-configuration',
  templateUrl: './InputConfiguration.html',
  styleUrls: ['./InputConfiguration.css']
})
export class InputConfiguration extends SwtModule {
  @ViewChild('swtModule') swtModule: SwtModule;
  @ViewChild('dataGridContainer') dataGridContainer: SwtCanvas;
  @ViewChild('bottomGridContainer') bottomGridContainer: SwtCanvas;
  @ViewChild('changeButton') changeButton: SwtButton;
  @ViewChild('saveButton') saveButton: SwtButton;
  @ViewChild('cancelButton') cancelButton: SwtButton;
  @ViewChild('closeButton') closeButton: SwtButton;
  @ViewChild('dataBuildingText') dataBuildingText: SwtLabel;
  @ViewChild('lostConnectionText') lostConnectionText: SwtLabel;
  @ViewChild('loadingImage') loadingImage: SwtLoadingImage;
  @ViewChild('exportContainer') exportContainer: SwtDataExport;
  @ViewChild('diskSaveImage') diskSaveImage: SwtButton;
  @ViewChild('diskSaveImageError') diskSaveImageError: SwtButton;
  private swtAlert: SwtAlert;
  private configGrid: SwtCommonGrid;
  private bottomGrid: SwtCommonGrid;
  public jsonReader: JSONReader = new JSONReader();
  public jsonReaderBottomGrid: JSONReader = new JSONReader();
  public lastRecievedJSON;
  public bottomGridLastRecievedJSON;
  public prevRecievedJSON;
  private noCache;
  public inputData = new HTTPComms(this.commonService);
  public bottomData = new HTTPComms(this.commonService);
  private sendData = new HTTPComms(this.commonService);
  public requestParams = [];
  private baseURL = SwtUtil.getBaseURL();
  private actionMethod: string;
  private actionPath: string;
  private interfaceId: string = null;
  //set the objects
  private imageDataChanged: any;
  private updateImage: any;
  public prevSelectedIndex: number = -1;
  public eventColIndex: number = -1;
  public passwrd: string = null;
  private menuAccessId: number;
  private flagAlert: boolean = false;
  private fromPCM: string = "false";
  private diskTimer: Timer;
  public screenVersion  = new ScreenVersion(this.commonService) ;
  private  screenName: string= ""; //ExternalInterface.call('getBundle', 'text', 'label-inputConfiguration', 'Input Configuration Screen');
  private  versionNumber: string="1.1.0021";
  public showJSONPopup: any;
  private previousSelectedInterface: string = "";
  constructor(private commonService: CommonService, private element: ElementRef) {
    super(element, commonService);
    this.swtAlert = new SwtAlert(commonService);
  }

  ngOnInit() {
    this.configGrid = <SwtCommonGrid>this.dataGridContainer.addChild(SwtCommonGrid);
    //this.configGrid.uniqueColumn = "interface_id";
    this.configGrid.editable = true;
    this.bottomGrid = <SwtCommonGrid>this.bottomGridContainer.addChild(SwtCommonGrid);
    this.bottomGrid.editable = true;
    this.configGrid.clientSideFilter = false;
    this.configGrid.clientSideSort = false;
    this.configGrid.onFilterChanged = (data) => {
      this.filterUpdate(data, true);
    }
    this.configGrid.onSortChanged = (data)=> {
      this.filterUpdate(data, false);
    }
    this.configGrid.doubleClickEnabled = false;
    this.configGrid.allowMultipleSelection = false;
  }
  filterUpdate(event, checkChange: boolean): void {
    if(!checkChange && (this.configGrid.changes.getValues().length > 0 || this.bottomGrid.changes.getValues().length > 0))
      checkChange = true;

    if (this.saveButton.enabled && checkChange) {
      if (this.validateSaveFlag == "success") {
        if (this.checkAlerts()) {
          Alert.okLabel = SwtUtil.getPredictMessage('interfaceSettings.save', null);
          var alertMessage = SwtUtil.getPredictMessage("alert.interfaceSettings.savecancelconfirmation", null);
          alertMessage = alertMessage.replace("an interface", this.configGrid.dataProvider[this.prevSelectedIndex].interface_id + " interface");

          this.swtAlert.warning(alertMessage, 'Warning', Alert.OK | Alert.CANCEL, null, this.alertListener.bind(this));
          this.flagAlert = false;
        }
      }
    } else {
      this.updateData();
    }
  }

  onLoad() {
    this.initializeMenus();
    this.inputData.cbStart = this.startOfComms.bind(this);
    this.inputData.cbStop = this.endOfComms.bind(this);
    this.inputData.cbResult = (event) => {
      this.inputDataResult(event)
    }
    var d = new Date();
    this.noCache = d.getTime();
    this.inputData.cbFault = this.inputDataFault.bind(this);
    this.fromPCM = ExternalInterface.call('eval', 'fromPCM');
    // this.addEventListener("CellClick", onMainGridImageChecked, true);
    //this.useHandCursor=false;
    this.sendData.cbStart = this.startOfComms.bind(this);
    this.sendData.cbStop = this.endOfComms.bind(this);
    this.sendData.cbResult = (event) => {
      this.sendDataResult(event);
    };
    this.inputData.encodeURL = false;
    this.sendData.encodeURL = false;
    this.sendData.cbFault = this.sendDataFault.bind(this);
    //set the actionpath
    this.actionPath = "inputconfiguration.do?";
    //Then declare the action method:
    this.actionMethod = "method=fetchData";
    this.actionMethod = this.actionMethod + "&fromPCM=" + this.fromPCM;
    //Then apply them to the url member of the HTTPComms object:
    this.inputData.url = this.baseURL + this.actionPath + this.actionMethod;
    this.sendData.url = this.baseURL + this.actionPath + "method=saveData" + "&fromPCM=" + this.fromPCM;

    this.bottomData.cbResult = (data) => {
      this.inputDataBottomGridResult(data);
    };
    this.bottomData.encodeURL = false;
    this.actionMethod = "method=fetchBottomGridData&interface_id="+ ((this.selectedInterface != "" && this.configGrid.selectedIndex >= 0) ? this.selectedInterface : "");
    this.actionMethod = this.actionMethod + "&fromPCM=" + this.fromPCM;
    //set the url
    this.bottomData.url = this.baseURL + this.actionPath + this.actionMethod;
    this.bottomData.cbFault = this.inputDataFault.bind(this);
    this.bottomData.send(this.requestParams);

    //diskTimer.addEventListener(TimerEvent.TIMER_COMPLETE, turnOffDisk);
    this.configGrid.ITEM_CLICK.subscribe((item) => {
      this.onGridCellClick(item)
    })
    this.configGrid.enableDisableCells=(row, field) => {
      return this.enableDisableRow(row, field);
    };
    this.bottomGrid.enableDisableCells=(row, field) => {
      return this.enableDisableBottomRow(row, field);
    }

    this.inputData.send(this.requestParams);
    ExportEvent.subscribe((type) => {
      this.report(type)
    });

  }
  private enableDisableRow(row:any, field:string):boolean
  {
    if(row.id == this.prevSelectedIndex && (row.expand != 'Y' || field == "emaillogs" || field == "emaillogsto") )
        return true;
     return false;
  }
  private enableDisableBottomRow(row:any, field:string):boolean
  {
    if(this.configGrid.selectedIndex == this.prevSelectedIndex )
        return true;
     return false;
  }
  startOfComms(): void {
    this.loadingImage.setVisible(true);
  }

  endOfComms(): void {
    this.loadingImage.setVisible(false);
  }
  initializeMenus(): void {
    this.screenVersion.loadScreenVersion(this, this.screenName, this.versionNumber, null);
    let summaryMenu: ContextMenuItem = new ContextMenuItem('Show JSON - Summary Details');
    let BottomMenu: ContextMenuItem = new ContextMenuItem('Show JSON - Bottom grid Details');
    summaryMenu.MenuItemSelect = this.showJSONSummarySelect.bind(this);
    BottomMenu.MenuItemSelect = this.showBottomJSONSelect.bind(this);
    this.screenVersion.svContextMenu.customItems.push(summaryMenu);
    this.screenVersion.svContextMenu.customItems.push(BottomMenu);
    this.contextMenu=this.screenVersion.svContextMenu;
  }
  showJSONSummarySelect(): void {

    this.showJSONPopup = SwtPopUpManager.createPopUp(this,
      JSONViewer,
      {
        jsonData: this.lastRecievedJSON,
      });
    this.showJSONPopup.width = "700";
    this.showJSONPopup.title = SwtUtil.getPredictMessage('screen.showJSON');
    this.showJSONPopup.height = "500";
    this.showJSONPopup.enableResize = false;
    this.showJSONPopup.showControls = true;
    this.showJSONPopup.isModal = true;
    this.showJSONPopup.display();
  }
  showBottomJSONSelect(): void {

    this.showJSONPopup = SwtPopUpManager.createPopUp(this,
      JSONViewer,
      {
        jsonData: this.bottomGridLastRecievedJSON,
      });
    this.showJSONPopup.width = "700";
    this.showJSONPopup.title = SwtUtil.getPredictMessage('screen.showJSON');
    this.showJSONPopup.height = "500";
    this.showJSONPopup.enableResize = false;
    this.showJSONPopup.showControls = true;
    this.showJSONPopup.isModal = true;
    this.showJSONPopup.display();
  }

  inputDataResult(event): void {
    this.requestParams = [];
    this.lastRecievedJSON = event;
    this.jsonReader.setInputJSON(this.lastRecievedJSON);
    this.lostConnectionText.visible = false;
    if (this.jsonReader.getRequestReplyStatus()) {
      if (!this.jsonReader.isDataBuilding()) {
        this.dataBuildingText.visible = false;
        if ((this.lastRecievedJSON != this.prevRecievedJSON)) {
          //set the config grid
          this.configGrid.GroupId = "interfaceId";

          const obj = {columns: this.jsonReader.getColumnData()};
          this.configGrid.gridComboDataProviders(event.inputconfiguration.grid.selects);
          this.configGrid.CustomGrid(obj);
          this.configGrid.saveWidths = true;

          //set the path
          this.configGrid.colWidthURL(this.baseURL + "inputconfiguration.do?fromPCM=" + ExternalInterface.call('eval', 'fromPCM') + "&");

          for (let i = 0; i < this.configGrid.columnDefinitions.length; i++) {
            let column = this.configGrid.columnDefinitions[i];
            if (column.field == "engine_active") {
              let greenLight = "./assets/images/new-tick.gif";
              let redLight = "./assets/images/new-cross.gif";
              column['properties'] = {
                enabled: false,
                columnName: 'enabled',
                imageEnabled: greenLight,
                imageDisabled: redLight,
                _toolTipFlag: true,
                style: ' display: block; margin-left: auto; margin-right: auto;'
              };
              this.configGrid.columnDefinitions[i].editor = null;
              this.configGrid.columnDefinitions[i].formatter = SwtCheckRenderer;
            }
          }
          for (let i = 0; i < this.jsonReader.getRowSize(); i++) {
            if (this.jsonReader.getGridData().row[i] && this.jsonReader.getGridData().row[i].interfaceId)
              this.jsonReader.getGridData().row[i].interface_id.content = '\t' + this.jsonReader.getGridData().row[i].interface_id.content
          }
          this.configGrid.gridData = this.jsonReader.getGridData();
          //set the row size
          this.configGrid.setRowSize = this.jsonReader.getRowSize();
          this.configGrid.resetOriginalDp = true;
          this.configGrid.changes.clear();
          /*******Save selectd index after fitler/sort**********/
          let gridDataAsArray: any[] = Array.of(this.jsonReader.getGridData().row);
          //case of 1 row, cast object to array
          //if (gridDataAsArray[0].length > 1) {
            gridDataAsArray = gridDataAsArray[0];
            if(this.selectedInterface !="" && gridDataAsArray) {
              let columnIndex = gridDataAsArray.findIndex((x) => x.interface_id.content == this.selectedInterface);
              if (columnIndex != -1) {
                setTimeout(()=> {
                  this.configGrid.selectedIndex = columnIndex;
                },0);
               // this.configGrid.gridObj.setSelectedRows([columnIndex])
                if (!this.changeButton.enabled)
                  this.prevSelectedIndex = this.configGrid.selectedIndex;//FIXME
               // }, 0)


              } else {
                //setTimeout(()=> {
                  this.configGrid.selectedIndex = -1;
                  this.changeButton.enabled = false
                  this.configGrid.gridObj.setSelectedRows([]);
                  this.selectedInterface ="";
                  this.bottomGrid.gridData = {row: [], size: 0};
                //}, 0)



              }
            } else {
              this.configGrid.selectedIndex = -1;
              this.configGrid.gridObj.setSelectedRows([]);
            }

            this.inputData.cbResult = (data) => {
            this.inputDataBottomGridResult(data);
          };
          this.inputData.encodeURL = false;
          this.actionMethod = "method=fetchBottomGridData&interface_id="+ ((this.selectedInterface != "" && this.configGrid.selectedIndex >= 0) ? this.selectedInterface : "");
          this.actionMethod = this.actionMethod + "&fromPCM=" + this.fromPCM;
          //set the url
          this.inputData.url = this.baseURL + this.actionPath + this.actionMethod;
          this.inputData.cbFault = this.inputDataFault.bind(this);
          this.inputData.send(this.requestParams);


          this.prevRecievedJSON = this.lastRecievedJSON;


          this.configGrid.ITEM_CHANGED.subscribe((event) => {
            this.validateCheckCombo(event);
          });
          this.configGrid.validate = (newValue, previousValue) => {
            if (this.prevSelectedIndex != -1 && this.prevSelectedIndex == this.configGrid.selectedIndex)
            this.validateHeaderGrid(newValue, previousValue);
            return true;
          };

        }
      } else {
        this.dataBuildingText.visible = true;
      }
    } else {
      this.swtAlert.error(this.jsonReader.getRequestReplyMessage(), ExternalInterface.call('getBundle', 'text', 'alert-error', 'Error'));
    }
  }

  public validateCheckCombo(event): void {

  //  if (event.target == "ComboBoxItemRenderer") {
      if (String(event.listData.newValue) == "NONE") {

        this.swtAlert.warning(SwtUtil.getPredictMessage('alert.interfaceSettings.emaillogsto'), 'Warning', Alert.OK, null, () => {
          this.validateSaveFlag = "success"
        }) //.setFocus();

        this.configGrid.dataProvider[this.prevSelectedIndex].slickgrid_rowcontent.emaillogsto.content = "";
        this.configGrid.dataProvider[this.prevSelectedIndex].emaillogsto = "";
       this.configGrid.refresh();
        //this.configGrid.refresh()//this line is add to ensure changes of email logs to
      }
     /* else {
          if (this.configGrid.dataProvider[this.prevSelectedIndex] && this.configGrid.dataProvider[this.prevSelectedIndex].emaillogs != "NONE" && this.configGrid.dataProvider[this.prevSelectedIndex].emaillogsto == "") {
            this.swtAlert.warning(SwtUtil.getPredictMessage('alert.interfaceSettings.requiresEmailLogsTo'), 'Warning', Alert.OK);
            this.validateSaveFlag = "reqMail";
          }
      }*/
   // }
  }

  public validateBottomFields(event): void {
    let alertFlag: boolean = true;
    let newData = event.listData.newValue;
    let oldData = event.listData.oldValue;
    let channelName: string;
    let beanId = event.listData.new_row.beanId;
    let hostName: string;
    if (event.listData.new_row.name == "frequency" ||event.listData.new_row.name=="startLine") {
      let frequencyPattern: RegExp = /^[0-9]*$/;
      let frequencyPatResult = frequencyPattern.test(newData);
      if (!frequencyPatResult || newData == "") {
        alertFlag = false;
        this.configGrid.changes.clear();
        this.swtAlert.warning(SwtUtil.getPredictMessage('alert.interfaceSettings.number'), "Warning");
        //newData = event.listData.oldValue
      }
    } else if (event.listData.new_row.name == "interval") {
      let intervalPattern: RegExp = /^[-]?([0-9]*)$/;
      let intervalPatResult = intervalPattern.test(newData);
      if (!intervalPatResult || parseInt(newData) < -1) {
        alertFlag = false;
        this.swtAlert.warning(SwtUtil.getPredictMessage('alert.interfaceSettings.number'), "Warning");
      }
    } else if (event.listData.new_row.name == "headerLength" || event.listData.new_row.name == "serverPort") {
      if (isNaN(parseInt(newData)) || parseInt(newData) <= 0) {
        alertFlag = false;
        this.swtAlert.warning(SwtUtil.getPredictMessage('alert.interfaceSettings.number'), "Warning");
      }
    } else if (event.listData.new_row.name== "mqChannel")
    {
      if (newData != "")
      {
        let mqChannelPattern:RegExp=/^[a-zA-Z0-9\.]*$/;
        let mqChannelPatResult=mqChannelPattern.test(newData);
        if (!mqChannelPatResult)
        {
          alertFlag=false;
          this.swtAlert.warning(SwtUtil.getPredictMessage('alert.interfaceSettings.password'), "Warning");
         // field.setFocus();
        }
      }
    } else if (event.listData.new_row.name == "mqHostName")
    {

      for (let channelCounter  = 0 ; channelCounter < this.bottomGrid.dataProvider.length ; channelCounter++){
        if(this.bottomGrid.dataProvider[channelCounter].name == "mqChannel" && this.bottomGrid.dataProvider[channelCounter].beanId == beanId){
          channelName = this.bottomGrid.dataProvider[channelCounter].value;
        }
      }
      if (channelName == ""){
        if (newData != "")
        {
          alertFlag=false;
          this.swtAlert.warning(SwtUtil.getPredictMessage('alert.interfaceSettings.requiresChannelName'), "Warning");
        }
      }else
      {
        if (newData != "")
        {
          let mqHostPattern:RegExp=/^[a-zA-Z0-9\.]*$/;
          let mqHostPatResult=mqHostPattern.test(newData);
          if (!mqHostPatResult)
          {
            alertFlag=false;
            this.swtAlert.warning(SwtUtil.getPredictMessage('alert.interfaceSettings.password'), "Warning");
            //field.setFocus();
          }
        }else{
          alertFlag=false;
          this.swtAlert.warning(SwtUtil.getPredictMessage('alert.interfaceSettings.requiresHostName'), "Warning");
        }
      }
    } else if (event.listData.new_row.name == "mqPort")
    {
      for (var channelCounter = 0 ; channelCounter < this.bottomGrid.dataProvider.length ; channelCounter++){
        if(this.bottomGrid.dataProvider[channelCounter].name == "mqChannel"  && this.bottomGrid.dataProvider[channelCounter].beanId == beanId){
          channelName = this.bottomGrid.dataProvider[channelCounter].value;
        }

      }
      for (var hostCounter = 0 ; hostCounter < this.bottomGrid.dataProvider.length ; hostCounter++){
        if(this.bottomGrid.dataProvider[hostCounter].name == "mqHostName" && this.bottomGrid.dataProvider[hostCounter].beanId == beanId ){
          hostName = this.bottomGrid.dataProvider[hostCounter].value;
        }
      }
      if (channelName == "")
      {
        if (newData != "")
        {
          alertFlag=false;
          this.swtAlert.warning(SwtUtil.getPredictMessage('alert.interfaceSettings.requiresChannelName'), "Warning");
        }
      }else
      {
        if (hostName == "")
        {
          if (newData != "")
          {
            alertFlag=false;
            this.swtAlert.warning(SwtUtil.getPredictMessage('alert.interfaceSettings.requiresHostName'), "Warning");
          }
        }else{
          //check the the number validation
          var mqPortPattern:RegExp=/^[0-9]{4,5}$/;
          //test the enetered value
          var mqPortPatternResult =mqPortPattern.test(newData);
          if (!mqPortPatternResult)
          {
            alertFlag=false;
            this.swtAlert.warning(SwtUtil.getPredictMessage('alert.interfaceSettings.number'), "Warning");
          }
        }
      }

    } else if (event.listData.new_row.name == "user" ||event.listData.new_row.name == "mqQueueManager" ||event.listData.new_row.name == "mqQueueName"|| event.listData.new_row.name == "queueName") {

        if (newData == "") {
          alertFlag = false;
          this.swtAlert.warning(SwtUtil.getPredictMessage('alert.interfaceSettings.text'), "Warning");
        }
      } else if (event.listData.new_row.name == "providerUrl" || event.listData.new_row.name == "url")
      {
        var providerUrlPattern:RegExp=/^(http:\/\/|www.)+[a-zA-Z0-9~#%@\&:=?\/\.,_-]+\.+[a-zA-Z]{2,4}$/;
        //to test the regular expression
        var providerUrlResult:Object=providerUrlPattern.test(newData);
        if (!providerUrlResult)
        {
          alertFlag=false;
          this.swtAlert.warning(SwtUtil.getPredictMessage('alert.interfaceSettings.url'), "Warning");
         // field.setFocus();
        }
      } else 	if (event.listData.new_row.name == "uploadFiles" || event.listData.new_row.name == "archiveFiles" || event.listData.new_row.name == "fromFiles" || event.listData.new_row.name == "toFiles" || event.listData.new_rowname == "directoryWFile")
    {

      let pattern:RegExp=/.*!/;
      //test the enetered value
      let result=pattern.test(newData);
      //if result is false
      if (!result)
      {
        alertFlag=false;
        //display an alert
        this.swtAlert.warning(SwtUtil.getPredictMessage('alert.interfaceSettings.directory'), "Warning");
      }

    }
    if(!alertFlag) {
      this.bottomGrid.dataProvider[event.rowIndex].value =oldData;
      var crudChangeIndex = this.bottomGrid.changes.getValues().findIndex(x=>((x.crud_data.id == x)));
      this.bottomGrid.changes.remove(this.bottomGrid.changes.getKeys()[crudChangeIndex ]);

    }

  }
  removeChanges() {
    var crudChangeIndex = this.configGrid.changes.getValues().findIndex(x=>((x.crud_data.id == x)));
    this.configGrid.changes.remove(this.configGrid.changes.getKeys()[crudChangeIndex ]);
  }


  inputDataFault(event): void {
    this.swtAlert.error(event.fault.faultstring + '\n' + event.fault.faultCode + '\n' + event.fault.faultDetail);
  }

  sendDataFault(event): void {
    this.swtAlert.error(event.fault.faultstring + '\n' + event.fault.faultCode + '\n' + event.fault.faultDetail);
  }

  onMainGridImageChecked(item) {

    if (item.target.field == "engine_active") {
      if (item.target.data.slickgrid_rowcontent.engine_active.content) {
        if (item.target.data.slickgrid_rowcontent.engine_active.content == "on") {
          this.configGrid.dataProvider[item.rowIndex].slickgrid_rowcontent.engine_active.content = "off";
          this.configGrid.dataProvider[item.rowIndex].engine_active = "off";
        } else if (item.target.data.slickgrid_rowcontent.engine_active.content == "off") {
          this.configGrid.dataProvider[item.rowIndex].slickgrid_rowcontent.engine_active.content = "on";
          this.configGrid.dataProvider[item.rowIndex].engine_active = "on";
        }
        SwtCheckRenderer(item.rowIndex, item.cellIndex, null, this.configGrid.gridObj.getColumns()[item.cellIndex], this.configGrid.dataProvider[item.rowIndex]);
        this.configGrid.refresh();//FIXME cause issie with grid.changes
      }

    }
  }

  public validateSaveFlag: string = "success";
  public selectedInterfaceId: string = "";

  resetFlag() {
    this.validateSaveFlag = "success"
  }

  public validateHeaderGrid(newValue, previousValue, fromSaveButton = false) {
      this.validateSaveFlag = 'success';
      var dataField: string = null;
      var fieldsToValidate = [];
      //let interfaceids: string = "";
      if(this.configGrid.dataProvider[this.prevSelectedIndex].slickgrid_rowcontent.interfaceId && this.configGrid.dataProvider[this.prevSelectedIndex].slickgrid_rowcontent.interfaceId.content)
        fieldsToValidate.push( 'begin_alert', 'end_alert');
      else
        fieldsToValidate.push( "emaillogsto",'begin_alert', 'end_alert');
      for (var jj: number = 0; jj < fieldsToValidate.length; jj++) {
        dataField = fieldsToValidate[jj];
        if (dataField == "emaillogsto") {
          var valResult: any;
          var emailVal: EmailValidator = new EmailValidator();
          var flag: boolean = true;
          var alertFlag: boolean = true;
          if (newValue["emaillogs"] != "NONE") {
            //this.validateSaveFlag="success";
            for (var i: number = 0; i < newValue["emaillogsto"].split(";").length; i++) {
              if (newValue["emaillogsto"].split(";")[i] != "") {

                valResult = EmailValidator.validateEmail(emailVal, newValue[dataField].split(";")[i], "text");
                if (valResult.length > 0) {
                  this.swtAlert.warning(SwtUtil.getPredictMessage('alert.interfaceSettings.emaillog'), 'Warning', Alert.OK, null, () => {
                    this.validateSaveFlag = "success";
                  })
                  this.validateSaveFlag = "invalidMail";
                  newValue["emaillogsto"] = previousValue["emaillogsto"];
                  this.configGrid.dataProvider[this.prevSelectedIndex].slickgrid_rowcontent.emaillogsto.content = previousValue["emaillogsto"];
                  this.configGrid.refresh();
                  break;
                }
              }
            }
          } else if (newValue["emaillogs"] == "NONE") {
            //set the flag as false
            flag = false;
            valResult = null;
            if (newValue["emaillogsto"] != "") {
              this.swtAlert.warning(SwtUtil.getPredictMessage('alert.interfaceSettings.emaillogsto'), 'Warning', Alert.OK, null, ()=> {
              })
              //this.validateSaveFlag = "notReqMail";
              //newValue["emaillogsto"] = "aaaa";
                this.configGrid.dataProvider[this.prevSelectedIndex].slickgrid_rowcontent.emaillogsto.content = "";
                this.configGrid.dataProvider[this.prevSelectedIndex].emaillogsto = ""
                this.configGrid.refresh()//this line is added when typing email while isNONE changes in grid are not updated
//this.removeChanges();

              break;
              //this.removeChanges();
            }
          }

        }

        var regEx: RegExp = /^([0-1][0-9]|[2][0-3]):([0-5][0-9])$/;
        var newData: string = null;

        if (dataField == "begin_alert" || dataField == "end_alert") {
          newData = (newValue[dataField] != undefined) ? newValue[dataField] : "";
          if (newData != undefined && !regEx.test(newData) && StringUtils.trim(newData).length > 0) {
            this.swtAlert.warning(SwtUtil.getPredictMessage('alert.interfaceSettings.time'), 'Warning', Alert.OK, null, () => {
            })
            //this.validateSaveFlag = "noValidTime";
            newValue[dataField] = previousValue[dataField];
            this.configGrid.dataProvider[this.prevSelectedIndex].slickgrid_rowcontent[dataField].content = previousValue[dataField];
            this.configGrid.refresh()
            break;
          }
        }

      }

  }

  checkAlerts() : boolean {
    if (this.prevSelectedIndex == -1)
      return;
    let alertFlag : boolean = true;
    let interfaceids: string = "";
    let channelName: string = "";
    let hostName: string = "";
    let portNumber: string = "";
    let bottomGridCounter = this.bottomGrid.dataProvider;



    for (let index = 0; index < this.configGrid.dataProvider.length; index++) {
        if (this.configGrid.dataProvider[this.prevSelectedIndex].expand != "") {
          if (this.configGrid.dataProvider[index].interface_id == this.configGrid.dataProvider[this.prevSelectedIndex].interface_id) {
            if (this.configGrid.dataProvider[this.prevSelectedIndex].emaillogs != "NONE") {
              if (this.configGrid.dataProvider[this.prevSelectedIndex].emaillogsto == "") {
                this.swtAlert.warning(SwtUtil.getPredictMessage('alert.interfaceSettings.requiresEmailLogsTo', null), 'Warning');
                alertFlag = false;
              }
            }
          }
        }
      }
    //Condition to check first the emails logs to and then Alert start and end times
    if (alertFlag) {
      //iterate the xml list for validating the start and end alert times.
      let configData = this.configGrid.dataProvider
      for (let i = 0; i < configData.length; i++) {
        if ((StringUtils.trim(configData[i].begin_alert.toString()) == "" && StringUtils.trim(configData[i].end_alert.toString()) != "") || (StringUtils.trim(configData[i].begin_alert.toString()) != "" && StringUtils.trim(configData[i].end_alert.toString()) == "")) {
          //check the flag
          if (alertFlag)
            interfaceids = interfaceids + StringUtils.trim(configData[i].interface_id.toString());
          else
            interfaceids = interfaceids + "\n" + StringUtils.trim(configData[i].interface_id.toString());
          //set the alertfalg as false
          alertFlag = false;
        }
      }
      if (!alertFlag) {
        //display the alert
        this.swtAlert.warning(SwtUtil.getPredictMessage('alert.interfaceSettings.startendtime', null) + interfaceids, 'Warning');

      } else {
        let tresholdData = this.configGrid.dataProvider;
        for (let counter = 0; counter < tresholdData.length; counter++) {
          if (tresholdData[counter].interface_id == this.configGrid.dataProvider[this.prevSelectedIndex].interface_id) {
            if ((StringUtils.trim(tresholdData[counter].begin_alert.toString()) == "" && StringUtils.trim(tresholdData[counter].end_alert.toString()) == "")) {
              if (this.configGrid.dataProvider[this.prevSelectedIndex].threshold != "") {
                this.swtAlert.warning(SwtUtil.getPredictMessage('alert.interfaceSettings.startendtime', null) + interfaceids, 'Warning');
                alertFlag = false;
              }
            } else {
              if (this.configGrid.dataProvider[this.prevSelectedIndex].threshold == "") {
                this.swtAlert.warning(SwtUtil.getPredictMessage('alert.interfaceSettings.requiresAlertThreshold', null) + tresholdData[counter].interface_id, 'Warning');
                alertFlag = false;
              } else {
                if (parseInt(this.configGrid.dataProvider[this.prevSelectedIndex].threshold) < 1) {
                  alertFlag = false;
                  this.swtAlert.warning(SwtUtil.getPredictMessage('alert.interfaceSettings.alertThresholdRange', null) + tresholdData[counter].interface_id, 'Warning');
                }
              }
            }
          }
        }
        }
      }
    if(alertFlag) {
      for (let index = 0; index < bottomGridCounter.length; index++) {
        if (bottomGridCounter[index].name == "mqChannel") {
          channelName = bottomGridCounter[index].value;
        }
        if (bottomGridCounter[index].name == "mqHostName") {
          hostName = bottomGridCounter[index].value;
        }
        if (bottomGridCounter[index].name == "mqPort") {
          portNumber = bottomGridCounter[index].value;
        }
      }
      if (channelName == "") {
        if (hostName != "") {
          alertFlag = false;
          this.swtAlert.warning(SwtUtil.getPredictMessage('alert.interfaceSettings.requiresChannelName', null), 'Warning');
        }
        if (portNumber != "" && alertFlag) {
          alertFlag = false;
          this.swtAlert.warning(SwtUtil.getPredictMessage('alert.interfaceSettings.requiresChannelName', null), 'Warning');
        }
      } else {
        if (hostName == "") {
          alertFlag = false;
          this.swtAlert.warning(SwtUtil.getPredictMessage('alert.interfaceSettings.requiresHostName', null), 'Warning');
        } else {
          if (portNumber == "") {
            alertFlag = false;
            this.swtAlert.warning(SwtUtil.getPredictMessage('alert.interfaceSettings.requiresPortNumber', null), 'Warning');
          } else {
          }
        }
      }
    }
    return alertFlag;
  }

  sendDataResult(event): void {
    if (event.request_reply.status_ok)
      if (event.request_reply.status_ok) {
        //this.diskTimer.start();
        this.diskSaveImageError.visible = false;
        this.diskSaveImage.visible = false;
      } else {
        //this.swtAlert.warning("" + xml.message, ExternalInterface.call('getBundle', 'text', 'alert-error', 'Error'));
        this.diskSaveImage.visible = false;
        this.diskSaveImageError.visible = true;
      }
    this.updateData();
  }

  inputDataBottomGridResult(event): void {
    this.bottomGridLastRecievedJSON = event;
    this.jsonReaderBottomGrid.setInputJSON(this.bottomGridLastRecievedJSON);
    this.lostConnectionText.visible = false;
    if (this.jsonReaderBottomGrid.getRequestReplyStatus()) {
      if (!this.jsonReaderBottomGrid.isDataBuilding()) {
        this.dataBuildingText.visible = false;
        if ((this.bottomGridLastRecievedJSON != this.prevRecievedJSON)) {
          const obj = {columns: this.jsonReaderBottomGrid.getColumnData()};
          this.bottomGrid.CustomGrid(obj);
          /*for each (var i:XML in bottomGridLastRecievedXML.grid.rows.row)
          {
            if (i.name == "password")
            {
              //assign the value
              bottomGrid.password=i.value;
              var str:string="";
              for (var j:int=0; j < bottomGrid.password.length; j++)
                str="*" + str;
              //assign the i value
              i.value=str;
            }
          }*/
          this.bottomGrid.gridData = this.jsonReaderBottomGrid.getGridData();
          this.bottomGrid.resetOriginalDp = true;
          this.bottomGrid.changes.clear();
          this.bottomGrid.setRowSize = this.jsonReaderBottomGrid.getRowSize();
          this.bottomGrid.selectable = false;
          //set the last recievd xml as prerecieved xml
          this.prevRecievedJSON = this.bottomGridLastRecievedJSON;
          //set the xmllist to lastrecieved xml from config grid
          //bottomGrid.xmlList=bottomGridLastRecievedXML;
        }
      } else {
        this.dataBuildingText.visible = true;
      }


    } else {
      this.swtAlert.error(this.jsonReaderBottomGrid.getRequestReplyMessage(), ExternalInterface.call('getBundle', 'text', 'alert-error', 'Error'));
    }

  }
  onGridCellClick(event): void {
    if (this.configGrid.selectedIndex > -1) {
      this.isEdit = false;
      this.selectedInterface = this.configGrid.selectedItem.interface_id.content;
      // Get the column index when selecting
      var colIndex: number = event.columnIndex;
      // Set the boolean expand attribute. It will be true if we have 'Y'
      var expand: boolean = event.target.data.expand == "Y";
      this.eventColIndex = colIndex;
      if (colIndex == 0 && expand) {
        this.interfaceId = event.target.data.interface_id;
        var dataprovider = this.configGrid.deepCopy(this.jsonReader.getGridData().row);
        for (var index in dataprovider) {
          var xmlElement = dataprovider[index];
          if (xmlElement['interface_id'] && this.interfaceId == xmlElement['interface_id'].content) {
            xmlElement.expand.opened = !xmlElement.expand.opened;
          }
          if (xmlElement[this.configGrid.GroupId] && this.interfaceId == xmlElement[this.configGrid.GroupId] && xmlElement.expand.content != 'Y') {
            xmlElement.hidden = !xmlElement.hidden;

          }
        }
        this.configGrid.gridData = {row: dataprovider, size: dataprovider.length}

      }
      if ( this.prevSelectedIndex != this.configGrid.selectedIndex) {
        //this.checkAlerts();
        if (this.saveButton.enabled) {
          if (this.validateSaveFlag == "success") {
            if (this.checkAlerts() || ( this.checkAlerts() && (event.columnIndex == 1 || event.columnIndex == 4))) {
              this.previousSelectedInterface = this.configGrid.dataProvider[this.prevSelectedIndex].interface_id;
              Alert.okLabel = SwtUtil.getPredictMessage('interfaceSettings.save', null);
              var alertMessage = SwtUtil.getPredictMessage("alert.interfaceSettings.savecancelconfirmation", null)
              alertMessage = alertMessage.replace("an interface", this.configGrid.dataProvider[this.prevSelectedIndex].interface_id + " interface");

              this.swtAlert.warning(alertMessage, 'Warning', Alert.OK | Alert.CANCEL, null, this.alertListener.bind(this));
              this.flagAlert = false;
            }
          }
        } else {
          //event.stopPropagation();
          //set the inputdata
          this.inputData.cbResult = (data) => {
            this.inputDataBottomGridResult(data);
          }
          //assign the interfaceId
          this.interfaceId = event.target.data.interface_id;
          //set the interface id through External interface call
          this.requestParams["inputInterface.interface_id"] = ExternalInterface.call('eval', 'interface_id');
          //set the senddata params
          this.sendData.cbStart = this.startOfComms.bind(this);
          this.sendData.cbStop = this.endOfComms.bind(this);
          this.sendData.cbResult = (data) => {
            this.sendDataResult(data);
          }
          this.sendData.cbFault = this.sendDataFault.bind(this);
          this.actionMethod = "method=fetchBottomGridData&interface_id=" + event.target.data.interface_id;
          //set the input data
          this.inputData.url = this.baseURL + this.actionPath + this.actionMethod + "&fromPCM=" + this.fromPCM;
          this.sendData.url = this.baseURL + this.actionPath + "method=saveData" + "&fromPCM=" + this.fromPCM;
          this.inputData.cbFault = this.inputDataFault.bind(this);
          //send the request params
          this.inputData.send(this.requestParams);
          this.menuAccessId = parseInt(ExternalInterface.call('eval', "menuAccessId"));
          //if (this.menuAccessId == 0)

          this.changeButton.enabled = true;
          this.changeButton.buttonMode = true;

        }
      }
    } else {
      this.bottomGrid.dataProvider = [];
      this.changeButton.enabled = false;
      this.changeButton.buttonMode = false;
    }
  }

  alertListener(eventObj): void {
    Alert.okLabel = "Ok";
    if (eventObj.detail == Alert.OK) {
      this.saveHandle(eventObj);
    } else {
      this.cancelHandle();
    }
  }


  saveHandle(eventObj): void {
    if (eventObj.detail == Alert.OK) {
      this.prevSelectedIndex = -1;
      this.exportContainer.enabled = true;
      this.saveButton.enabled = false;
      this.saveButton.buttonMode = false;
      this.cancelButton.enabled = false;
      this.cancelButton.buttonMode = false;
      this.bottomGrid.gridData = {row: [], size: 0};

      var strConfig: String = "";
      var strBottom: String = "";
      var strImage: String = "";
      //set the object
      var requestParams = [];
      let columnName: string;
      let columnValue: string;
      let changes = this.configGrid.changes.getValues();
      changes = changes.slice(-1);
      let bottomChanges = this.bottomGrid.changes.getValues();
      for (let i = 0; i < changes.length; i++) {

        let interfaceName = changes[i].crud_data.interface_id;
        strConfig += (interfaceName + ",");
        if (changes[i].crud_operation.indexOf("engine_active") != -1) {
          strImage += (interfaceName + ",")
        }
        let crudOpList = changes[i].crud_operation.split('>');
        for (let k = 0; k < crudOpList.length; k++) {
          columnName = crudOpList[k].substring(2, crudOpList[k].length - 1);
          columnValue = changes[i].crud_data[columnName];
          if (columnName == "engine_active")
            requestParams[interfaceName + "_image"] = changes[i].crud_data.engine_active == "on" ? 1 : 0;
          else
            requestParams[interfaceName + "_" + columnName] = columnValue
          if(interfaceName.indexOf("\t") !=-1) {
            requestParams['interfaceId'] = changes[i].crud_data.slickgrid_rowcontent["interfaceId"].content
            requestParams["isMessage"] = "Y"
          }
        }
      }

      for (let i = 0; i < bottomChanges.length; i++) {
        let interfaceName = this.previousSelectedInterface;
        let crudOpList = bottomChanges[i].crud_operation.split('>');
        for (let k = 0; k < crudOpList.length; k++) {
          columnName = bottomChanges[i].crud_data.beanId + "&" + bottomChanges[i].crud_data.name;
          columnValue = bottomChanges[i].crud_data.value;
          if (bottomChanges[i].crud_data.name.toLowerCase().indexOf('pass') != -1)
            requestParams[interfaceName + "&" + columnName] = ExternalInterface.call("encrypt", columnValue);
          else
            requestParams[interfaceName + "&" + columnName] = columnValue;
          strBottom += interfaceName + "&" + columnName + ",";
        }
        requestParams[interfaceName] = true;
      }
      strConfig = strConfig.substr(0, strConfig.length - 1);
      strImage = strImage.substr(0, strImage.length - 1);
      strBottom = strBottom.substr(0, strBottom.length - 1);
      requestParams["update"] = strConfig;
      requestParams["bottom"] = strBottom; //FIXME
      requestParams["image"] = strImage;

      //set interface id to request params
      this.sendData.encodeURL = false;
      //set the requestparams of update,image and bottom*/
      if ((requestParams["update"] as String).length > 0 || (requestParams["bottom"] as String).length > 0 || (requestParams["image"] as String).length > 0) {
        this.diskSaveImage.visible = true;
        this.selectedInterface = "";
        this.configGrid.selectedIndex = -1;
        this.sendData.send(requestParams);
        this.configGrid.changes.clear();


      } else {
        this.swtAlert.warning(SwtUtil.getPredictMessage('alert.interfaceSettings.nothingToSave', null), 'Warning');
        this.changeButton.enabled = false;
        this.changeButton.buttonMode = false;
        this.configGrid.gridComboDataProviders(this.lastRecievedJSON.inputconfiguration.grid.selects);
        // Enable the export icons
        this.exportContainer.enabled = true;
        this.configGrid.selectedIndex = -1;
        this.selectedInterface = "";
        this.updateData();

      }


    }


  }


  cancelHandle(): void {
    this.exportContainer.enabled = true;
    this.prevSelectedIndex = -1;
    this.saveButton.enabled = false;
    this.saveButton.buttonMode = false;
    this.cancelButton.enabled = false;
    this.cancelButton.buttonMode = false;
    this.configGrid.gridComboDataProviders(this.lastRecievedJSON.inputconfiguration.grid.selects);
    this.bottomGrid.gridData = {row: [], size: 0};
    this.configGrid.selectedIndex = -1;
    this.selectedInterface = "";
    //this.configGrid.gridObj.setSelectedRows([]);
    this.selectedInterface = "";

    this.updateData();
  }
private selectedInterface: string = "";
  updateData(): void {
    //set the input data params
    let gridColumns = this.configGrid.getFilterColumns();
    let filteredColumns = this.configGrid.filteredGridColumns;
    let filteredColumnsFields = [];
    let selectedFilter = "";
    let order= "";
    let ascDesc= "";
    for (var i = 0; i < gridColumns.length; i++) {
      filteredColumnsFields[i] = gridColumns[i].field;
    }
    if (filteredColumns != '') {
      let filterdValues = filteredColumns.split('|');
      for (var i = 0; i < filteredColumnsFields.length-1; i++) {
        if (filterdValues[i] != "") {
          if (filterdValues[i] != "All" && filterdValues[i] != undefined)
        if(filteredColumnsFields[i] =="engine_active")
      selectedFilter = selectedFilter + filteredColumnsFields[i] + "=" + "'" + ((filterdValues[i] == "on") ? '1' : '0' )+ "'" + " and ";
    else
          selectedFilter = selectedFilter + filteredColumnsFields[i] + "=" + "'" + filterdValues[i] + "'" + " and ";
        }
      else {
      selectedFilter= selectedFilter + filteredColumnsFields[i] + " is null" +" and ";

    }
      }
    }
    for(let i = 0; i< this.configGrid.sorters.length; i++) {
    order = this.configGrid.sorters[i].columnId;
    ascDesc = this.configGrid.sorters[i].direction ? "asc": "desc";
    }
    selectedFilter = selectedFilter.substring(0, selectedFilter.length - 5);
    this.requestParams['sortGrid'] = (order != "") ? order +' '+ascDesc : null;
    this.requestParams['filterGrid'] = selectedFilter;
    this.inputData.cbStart = this.startOfComms.bind(this);
    this.inputData.cbStop = this.endOfComms.bind(this);
    this.inputData.cbResult = (event) => {
      this.inputDataResult(event);
    };
    this.inputData.cbFault = this.inputDataFault.bind(this);
    this.actionMethod = "method=fetchData";
    //set the url
    this.inputData.url = this.baseURL + this.actionPath + this.actionMethod + "&fromPCM=" + this.fromPCM;
    //Then apply them to the url member of the HTTPComms object:
    this.inputData.send(this.requestParams);
  }

  private isEdit: boolean = false;

  changeHandle() {
     // this.configGrid.editable = true

    if (this.configGrid || this.bottomGrid) {
      //set change button enabled
      this.changeButton.enabled = false;
      this.changeButton.buttonMode = false;
      //set the exportcontainer
      this.exportContainer.enabled = false;
      //set the swtimage
      // SwtImage.checkFlag=true;
      //set the grid selected index
      this.prevSelectedIndex = this.configGrid.selectedIndex;
      this.previousSelectedInterface = this.configGrid.dataProvider[this.prevSelectedIndex].interface_id
      this.saveButton.enabled = true;
      this.saveButton.buttonMode = true;
      this.cancelButton.enabled = true;

      this.configGrid.ITEM_CLICK.subscribe((item) => {
        if (!this.isEdit && this.prevSelectedIndex == this.configGrid.selectedIndex) {
          this.isEdit = true;
          this.onMainGridImageChecked(item)
        }
      });

      this.bottomGrid.ITEM_CHANGED.subscribe((event) => {
        this.validateBottomFields(event);
      });
//these two lines are added to show that the selected row is now enabled
     this.configGrid.refresh();
     this.bottomGrid.refresh();



    }
  }

  report(type: string) {
    this.exportContainer.convertData(this.lastRecievedJSON.inputconfiguration.grid.metadata.columns, this.configGrid, null, null, type, false);
  }

  closeHandle() {
    ExternalInterface.call("close");
  }

  doHelp(): void {
    ExternalInterface.call('help')
  }

  confirmSave(): void {
    if (this.checkAlerts()) {
      this.swtAlert.warning(SwtUtil.getPredictMessage('alert.interfaceSettings.save', null), 'Warning', Alert.OK, null, this.saveHandle.bind(this));
    }

  }


}

//Define lazy loading routes
const routes: Routes = [
  {path: '', component: InputConfiguration}
];
export const routing: ModuleWithProviders = RouterModule.forChild(routes);

//Define an NgModule that will be loaded by app module
@NgModule({
  imports: [routing, SwtToolBoxModule],
  declarations: [InputConfiguration],
  entryComponents: []
})
export class InputConfigurationModule {
}

