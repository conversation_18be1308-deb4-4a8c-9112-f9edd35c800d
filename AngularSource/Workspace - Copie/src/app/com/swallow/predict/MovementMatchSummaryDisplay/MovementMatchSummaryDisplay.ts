import { ProgressBar } from './ProgressBar/ProgressBar';
import {<PERSON>mponent, ElementRef, NgModule, OnDestroy, OnInit, ViewChild, HostListener} from '@angular/core';
import {
  Alert, CommonService, ContextMenuItem, ExportEvent, ExternalInterface,
  HBox, HTTPComms, JSONReader, JSONViewer,
  Keyboard, ScreenVersion, SwtAlert, SwtButton,
  SwtCanvas, SwtComboBox, SwtCommonGrid, SwtDataExport, SwtImage,
  SwtLabel, SwtLoadingImage, SwtModule, SwtPopUpManager,
  SwtTextInput, SwtToolBoxModule, SwtUtil, TitleWindow, EnhancedAlertingTooltip,
} from 'swt-tool-box';

import {RouterModule, Routes} from '@angular/router';
import {ModuleWithProviders} from '@angular/compiler/src/core';
import {FontSetting} from '../FontSetting/FontSetting';
import { Observable } from 'rxjs';
import 'rxjs/add/observable/fromEvent';
import { AlertingRenderer } from '../EnhancedAlerting/Render/AlertingRenderer';
import { BooleanParser } from '../SwtUtills/BooleanParser';

declare let instanceElement: any;
declare function checkCurrencyPlacesFromNumber(strVal, strPat, currCode): any;
@Component({
  selector: 'app-movement-match-summary',
  templateUrl: './MovementMatchSummaryDisplay.html',
  styleUrls: ['./MovementMatchSummaryDisplay.css']
})
export class MovementMatchSummaryDisplay extends  SwtModule implements OnDestroy, OnInit {

  constructor( private commonService: CommonService, private element: ElementRef) {
    super(element, commonService);
    this.swtAlert  = new SwtAlert(commonService);
    window["Main"] = this;
  }
  /*********SwtCanvas*********/
  @ViewChild('gridCanvas') gridCanvas: SwtCanvas;
  /*********Combobox*********/
  @ViewChild('entityCombo') entityCombo: SwtComboBox;
  /*********SwtTextInput*********/
  @ViewChild('matchIdText') matchIdText: SwtTextInput;
  /*********SwtText*********/
  @ViewChild('dataBuildingText') dataBuildingText: SwtTextInput;
  @ViewChild('lostConnectionText') lostConnectionText: SwtTextInput;

  /*********SwtLabel*********/
  @ViewChild('loadingImage') loadingImage: SwtLoadingImage;
  @ViewChild('currencyLabel') currencyLabel: SwtLabel;
  @ViewChild('currencyCode') currencyCode: SwtLabel; 
  @ViewChild('ccyTolLabel') ccyTolLabel: SwtLabel;
  @ViewChild('ccyTolValue') ccyTolValue: SwtLabel;
  @ViewChild('entityLabel') entityLabel: SwtLabel;
  @ViewChild('selectedEntityId') selectedEntityId: SwtLabel;
  @ViewChild('selectedEntityName') selectedEntityName: SwtLabel;
  @ViewChild('matchLabel') matchLabel: SwtLabel;
  @ViewChild('matchIndices') matchIndices: SwtLabel;
  @ViewChild('matchStatus') matchStatus: SwtLabel;
  @ViewChild('matchQuality') matchQuality: SwtLabel;
  @ViewChild('updateDate') updateDate: SwtLabel;
  @ViewChild('updateUser') updateUser: SwtLabel;
  @ViewChild('noteImage') noteImage: SwtImage;
  @ViewChild('alertImage') alertImage: SwtImage;
  @ViewChild('selectedEntity') selectedEntity: SwtLabel;
  @ViewChild('posIntlvl') posIntlvl: SwtLabel;
  @ViewChild('posExtlvl') posExtlvl: SwtLabel;
  @ViewChild('internalLabel') internalLabel: SwtLabel;
  @ViewChild('externalLabel') externalLabel: SwtLabel;
  @ViewChild('usedBye') usedBye: SwtLabel;
  @ViewChild('movement') movement: SwtLabel;
  @ViewChild('doesnotexist') doesnotexist: SwtLabel;
  @ViewChild('movementCannotBeUnlocked') movementCannotBeUnlocked: SwtLabel;
  @ViewChild('diffLabel') diffLabel: SwtLabel; 
  @ViewChild('diffValue') diffValue: SwtLabel;
  /*********SwtDataExport*********/
  @ViewChild('exportContainer') exportContainer: SwtDataExport;
  /*********SwtButton*********/
  @ViewChild('nextButton') nextButton: SwtButton;
  @ViewChild('prevButton') prevButton: SwtButton;
  @ViewChild('logButton') logButton: SwtButton;
  @ViewChild('noteButton') noteButton: SwtButton;
  @ViewChild('mvmntButton') mvmntButton: SwtButton;
  @ViewChild('unMatchButton') unMatchButton: SwtButton;
  @ViewChild('suspendButton') suspendButton: SwtButton;
  @ViewChild('reconButton') reconButton: SwtButton;
  @ViewChild('closeButton') closeButton: SwtButton;
  @ViewChild('confirmButton') confirmButton: SwtButton;
  @ViewChild('addButton') addButton: SwtButton;
  @ViewChild('removeButton') removeButton: SwtButton;

  @ViewChild('optionsButton') optionsButton: SwtButton;
  @ViewChild('helpIcon') helpIcon: SwtButton;

  /*********HBox*********/
  @ViewChild('buttonsContainer') buttonsContainer: HBox;



  /**
   * Communication Objects
   **/
  private mmsdGrid: SwtCommonGrid;

  /**
   * Data Objects
   **/
  private jsonReader: JSONReader  = new JSONReader();
  private lastReceivedJSON;
  private prevRecievedJSON;

  /**
   * Communication Objects
   **/
  private inputData  = new HTTPComms(this.commonService);
  private alertingData  = new HTTPComms(this.commonService);
  
  private  logicUpdate = new HTTPComms(this.commonService);
  private updateFontSize = new HTTPComms(this.commonService);
  private changeMatchService = new HTTPComms(this.commonService);
  private baseURL  = SwtUtil.getBaseURL();
  private requestURL ="";
  private actionMethod = "";
  private actionPath = "";
  private  matchIds="";
  private  inScreenMatchId="";
  private  matchType="";
  private requestParams = [];
  private  requestParameter = [];
  private  movIds= [];
  private invalidComms = "";

  private  confirmbuttonIndex: number;
  private  suspendbuttonIndex: number;
  private  reconcilebuttonIndex: number;

  private  fullAccess: string = null;

  /**
   * Logic Objects
   **/
  private  removeFlagUnmatch=false;
  private  suspendFlag=false;
  private  confirmFlagOk=false;
  private  reconcileFlag=false;
  private  predictFlagConfirm=false;
  private  selectedMovements = [];
  private  previousSelectedIndices = [];

  private  suspendFlagListener=false;
  private  confirmFlagListener=false;
  private  reconcileFlagLisner=false;

  private  refreshFlag=false;
  private  matchHash="";

  /**
   * Popup Objects
   **/
  private showJsonPopup = null;
  public screenVersion  = new ScreenVersion(this.commonService) ;
  private  screenName = "Movement Match Summary Display";
  private  versionNumber = "1.1.0029";
  private versionDate = "04/11/2019";

  private  currentUser: string;

  private win: TitleWindow;
  private winProgress: TitleWindow;
  private fontValue = "";
  private fontLabel = "";
  private fontRequest = "";
  private  currentFontSize="";
  private  selectedFont: number;
  private  tempFontSize="";


  private subScreenName: string=null;
  private reportType: string=null;
  private  serverBusyFlag=false;

  private  acctAccessFlag = false;

  public posTotal = "Position Totals";
  private swtAlert: SwtAlert;
  private errorLocation = 0;
  public moduleId  = 'Predict';
  tooltipMatchId = null;
  tooltipMvtId = null;
  tooltipFacilityId = null;
  tooltipSelectedDate = null;
  private positionX:number;
  private positionY:number;
  private hostId;
  private entityId;
  private currencyId;
  private selectedNodeId = null;
  private treeLevelValue = null;
  tooltipOtherParams = [];
  ccyTol = null;
  suppMatchDiffWarning = null;
  currencyPattern = null;
  /**
   * This function is used to validate  the Export when the Export buutons are clicked.
   * */
  ngOnInit(): void {
    instanceElement  = this;
    this.mmsdGrid  = this.gridCanvas.addChild(SwtCommonGrid) as SwtCommonGrid;
    this.mmsdGrid.uniqueColumn = "movement";
    this.mmsdGrid.lockedColumnCount = 2;
    this.mmsdGrid.clientSideFilter = true;
    this.mmsdGrid.onFilterChanged = this.filterUpdate.bind(this);
    this.matchLabel.text  = SwtUtil.getPredictMessage('matchQuality.matchId', null);
    this.matchIdText.toolTip  = SwtUtil.getPredictMessage('matchQuality.matchId', null);
    this.currencyLabel.text  = SwtUtil.getPredictMessage('matchQuality.currencyCode', null);
    this.entityLabel.text  = SwtUtil.getPredictMessage('matchQuality.entityId', null);
    this.entityLabel.toolTip  = SwtUtil.getPredictMessage('matchQuality.entityId', null);
    this.externalLabel.text = SwtUtil.getPredictMessage('matchQuality.posTotalExternal', null);
    this.internalLabel.text = SwtUtil.getPredictMessage('matchQuality.posTotalInternal', null);
    this.posTotal = SwtUtil.getPredictMessage('matchQuality.posTotal', null);
    this.ccyTolLabel.text  = SwtUtil.getPredictMessage('matchQuality.ccyTolerance', null);
    this.diffLabel.text  = SwtUtil.getPredictMessage('matchQuality.difference', null);
  }

  ngOnDestroy(): any {
    instanceElement  = null;
  }


  setButtonsProperties() {
    /*Displays the Note button*/
    this.noteButton.id="noteButton";
    this.noteButton.toolTip= SwtUtil.getPredictMessage('tooltip.matchNotes', null);
    this.noteButton.label= SwtUtil.getPredictMessage('button.notes', null);
    this.noteButton.click = () => {
      this.openNotes();
    };

    /*Displays the Log button*/
    this.logButton.id="logButton";
    this.logButton.toolTip= SwtUtil.getPredictMessage('tooltip.logSelMvm', null);
    this.logButton.label= SwtUtil.getPredictMessage('button.log', null);
    this.logButton.click = () => {
      this.openLog();
    };

    /*Displays the Unmatch button*/
    this.unMatchButton.id="unMatchButton";
    this.unMatchButton.toolTip= SwtUtil.getPredictMessage('tooltip.unMatch', null);
    this.unMatchButton.label=SwtUtil.getPredictMessage('button.unmatch', null);
    this.unMatchButton.click = () => {
      this.openUnMatch();
    };
    /*Displays the Movement button*/
    this.mvmntButton.id="mvmntButton";
    this.mvmntButton.toolTip=SwtUtil.getPredictMessage('tooltip.showSelMovDetail', null);
    this.mvmntButton.label=SwtUtil.getPredictMessage('button.mvmnt', null);
    this.mvmntButton.click =(data) => {
      this.openMvmnt();
    };
    /*Displays the Reconcile button*/
    this.reconButton.id="reconButton";
    this.reconButton.toolTip=SwtUtil.getPredictMessage('tooltip.reconMatch', null);
    this.reconButton.label=SwtUtil.getPredictMessage('button.reconcile', null);
    this.reconButton.click = () => {
      this.reconcileMvmnt();
    };

    /*Displays the Remove button*/
    this.removeButton.id="removeButton";
    this.removeButton.toolTip=SwtUtil.getPredictMessage('tooltip.removeSelMov', null);
    this.removeButton.label=SwtUtil.getPredictMessage('button.remove', null);
    this.removeButton.click = () => {
      this.removeMvmnt();
    } ;

    /*Displays the Add button*/
    this.addButton.id="addButton";
    this.addButton.toolTip=SwtUtil.getPredictMessage('tooltip.addMov', null);
    this.addButton.label=SwtUtil.getPredictMessage('button.add', null);
    this.addButton.click = () => {
      this.addMvmnt();
    };

    /*Displays the Options button*/
    this.optionsButton.id="optionsButton";
    this.optionsButton.toolTip=SwtUtil.getPredictMessage('tooltip.options', null);
    this.optionsButton.label=SwtUtil.getPredictMessage('button.options', null);
    this.optionsButton.click = () => {
      this.fontSettingHandler();
    } ;

    /*Displays the Close button*/
    this.closeButton.id="closeButton";
    this.closeButton.toolTip=SwtUtil.getPredictMessage('button.close', null);
    this.closeButton.label=SwtUtil.getPredictMessage('button.close', null);
    this.closeButton.click = (data) => {
      this.closeHandler(data);
    };
  }
  setAllButtonsStatus() {
    // Sets the button status
    this.prevButton.id="prevButton";
    this.prevButton.toolTip= SwtUtil.getPredictMessage('tooltip.previousMatch', null);
    this.prevButton.label= SwtUtil.getPredictMessage('button.previous', null);
    this.prevButton.click = () => {
      this.openPreviousRecord();
    };
    this.nextButton.id="nextButton";
    this.nextButton.toolTip=SwtUtil.getPredictMessage('tooltip.nextMatch', null);
    this.nextButton.label=SwtUtil.getPredictMessage('button.next', null);
    this.nextButton.click = () => {
      this.openNextRecord();
    };
    this.setButtonsProperties();
  }

  setButtonsStatus() {
    // Sets the button status
    this.noteButton = this.buttonsContainer.addChild(SwtButton) as SwtButton ;
    this.logButton =  this.buttonsContainer.addChild(SwtButton) as SwtButton ;
    this.mvmntButton = this.buttonsContainer.addChild(SwtButton) as SwtButton ;
    this.unMatchButton = this.buttonsContainer.addChild(SwtButton) as SwtButton;
    this.suspendButton = this.buttonsContainer.addChild(SwtButton) as SwtButton ;
    this.confirmButton = this.buttonsContainer.addChild(SwtButton) as SwtButton ;
    this.reconButton = this.buttonsContainer.addChild(SwtButton) as SwtButton;
    this.removeButton =  this.buttonsContainer.addChild(SwtButton) as SwtButton;
    this.addButton = this.buttonsContainer.addChild(SwtButton) as SwtButton;
    this.optionsButton  = this.buttonsContainer.addChild(SwtButton) as SwtButton;
    this.closeButton = this.buttonsContainer.addChild(SwtButton) as SwtButton;
    this.setButtonsProperties();
  }

  /**
   * Upon completion of loading into the flash player this method is called
   **/
  onLoad() {
    this.initializeMenus();
    ExportEvent.subscribe((type) => {
      this.export(type);
    });

    this.currentUser = ExternalInterface.call('eval', 'currentUser');
    this.matchType = ExternalInterface.call('eval', 'match');
    let calledFrom = ExternalInterface.call('eval', 'calledFrom');
    this.changeMatchService.cbStart  = this.startOfCommsAction.bind(this);
    this.changeMatchService.cbStop  = this.endOfCommsAction.bind(this);
    this.inputData.cbStart  = this.startOfComms.bind(this);
    this.inputData.cbStop  = this.endOfComms.bind(this);
    this.inputData.cbResult  = (event)  => {
      this.inputDataResult(event);
    };
    this.inputData.cbFault  = this.inputDataFault.bind(this);
    this.inputData.encodeURL  = false;
    this.actionPath  = 'movementmatchdisplay.do?';
    this.actionMethod  = 'method=flexdisplay';
    this.requestParams  = [];

    this.mmsdGrid.onRowClick  = (event)  => {
      this.cellLogic(event);
    };
    Observable.fromEvent(document.body, 'click').subscribe(e => {
      this.positionX = e["clientX"];
      this.positionY = e["clientY"];
    });
    this.mmsdGrid.ITEM_CLICK.subscribe((selectedRowData) => {
      setTimeout(() => {
      this.itemClickFunction(selectedRowData);
    }, 0);

    });


    if (this.matchType != "manual") {
      this.buttonsContainer.removeAllChildren();
      // this.setAllButtonsStatus();
      if(calledFrom == "scenarioSummary") {
        this.actionMethod= this.actionMethod + "&day=" + ExternalInterface.call('eval', 'day') + "&matchCount=" + ExternalInterface.call('eval', 'matchCount') + "&currencyCode=" + ExternalInterface.call('eval', 'currencyCode') +
          "&status=" + ExternalInterface.call('eval', 'status') + "&date=" + ExternalInterface.call('eval', 'date') + "&entityId=" +
          ExternalInterface.call('eval', 'entityId') + "&quality=" + ExternalInterface.call('eval', 'quality') + "&menuAccessId=" + ExternalInterface.call('eval', 'menuAccessId') + "&applyCurrencyThreshold=" + ExternalInterface.call('eval', 'applyCurrencyThreshold') + "&noIncludedMovementMatches=" +
          ExternalInterface.call('eval', 'noIncludedMovementMatches') + "&dateTabIndFlag=" + ExternalInterface.call('eval', 'dateTabIndFlag') + "&dateTabInd=" + ExternalInterface.call('eval', 'dateTabInd') +
          "&valueDate=" + ExternalInterface.call('eval', 'valueDate') + "&scenarioId=" + ExternalInterface.call('eval', 'scenarioId')+ "&currGrp=" + ExternalInterface.call('eval', 'currGrp') +  "&calledFrom=" + ExternalInterface.call('eval', 'calledFrom') ;

        this.inputData.url=this.baseURL + this.actionPath + this.actionMethod;
        this.inputData.send(this.requestParams);

        // Sets the button status
        this.prevButton = this.buttonsContainer.addChild(SwtButton) as SwtButton ;
        this.nextButton =  this.buttonsContainer.addChild(SwtButton) as SwtButton ;
        this.noteButton = this.buttonsContainer.addChild(SwtButton) as SwtButton ;
        this.logButton = this.buttonsContainer.addChild(SwtButton) as SwtButton;
        this.mvmntButton = this.buttonsContainer.addChild(SwtButton) as SwtButton ;
        this.unMatchButton = this.buttonsContainer.addChild(SwtButton) as SwtButton ;
        this.suspendButton = this.buttonsContainer.addChild(SwtButton) as SwtButton ;
        this.confirmButton = this.buttonsContainer.addChild(SwtButton) as SwtButton ;
        this.reconButton = this.buttonsContainer.addChild(SwtButton) as SwtButton;
        this.removeButton =  this.buttonsContainer.addChild(SwtButton) as SwtButton;
        this.addButton = this.buttonsContainer.addChild(SwtButton) as SwtButton;
        this.optionsButton  = this.buttonsContainer.addChild(SwtButton) as SwtButton;
        this.closeButton = this.buttonsContainer.addChild(SwtButton) as SwtButton;
        /*Displays the Confirm button*/
        this.confirmButton.id="confirmButton";
        this.confirmButton.toolTip=SwtUtil.getPredictMessage('tooltip.ConfMatch', null);
        this.confirmButton.label=SwtUtil.getPredictMessage('button.confirm', null);
        this.confirmButton.click = () => {
          this.confirmMvmnt();
        };
        /*Displays the Suspend button*/
        this.suspendButton.id="suspendButton";
        this.suspendButton.toolTip = SwtUtil.getPredictMessage('tooltip.suspMatch', null);
        this.suspendButton.label = SwtUtil.getPredictMessage('button.suspend', null);
        this.suspendButton.click = (data) => {
          this.suspendMvmnt();
        };
        this.setAllButtonsStatus();

        this.prevButton.enabled=false;
        this.mvmntButton.enabled=false;
        this.removeButton.enabled=false;
        this.nextButton.enabled=false;
        this.noteButton.enabled=false;
        this.logButton.enabled=false;
        this.unMatchButton.enabled=false;
        this.suspendButton.enabled=false;
        this.confirmButton.enabled=false;
        this.reconButton.enabled=false;
        this.addButton.enabled=false;

      } else {
        this.actionMethod = this.actionMethod + "&day=" + ExternalInterface.call('eval', 'day') + "&matchCount=" + ExternalInterface.call('eval', 'matchCount') + "&currencyCode=" + ExternalInterface.call('eval', 'currencyCode') + "&status=" + ExternalInterface.call('eval', 'status') + "&date=" + ExternalInterface.call('eval', 'date') + "&entityId=" + ExternalInterface.call('eval', 'entityId') + "&quality=" + ExternalInterface.call('eval', 'quality') + "&menuAccessId=" + ExternalInterface.call('eval', 'menuAccessId') + "&applyCurrencyThreshold=" + ExternalInterface.call('eval', 'applyCurrencyThreshold') + "&noIncludedMovementMatches=" + ExternalInterface.call('eval', 'noIncludedMovementMatches') + "&dateTabIndFlag=" + ExternalInterface.call('eval', 'dateTabIndFlag') + "&dateTabInd=" + ExternalInterface.call('eval', 'dateTabInd') + "&valueDate=" + ExternalInterface.call('eval', 'valueDate');
        // Any updates as a result of a logic change. E.g a button press resulted in a change of data
        this.logicUpdate.cbStart=this.startOfComms.bind(this);
        this.logicUpdate.cbStop=this.endOfComms.bind(this);
        this.logicUpdate.cbResult  = (event)  => {
          this.logicUpdateResult(event);
        };

        if (ExternalInterface.call('eval', 'status') == "M") {
          // Sets the button status
          this.prevButton = this.buttonsContainer.addChild(SwtButton) as SwtButton ;
          this.nextButton =  this.buttonsContainer.addChild(SwtButton) as SwtButton ;
          this.noteButton = this.buttonsContainer.addChild(SwtButton) as SwtButton ;
          this.logButton = this.buttonsContainer.addChild(SwtButton) as SwtButton;
          this.mvmntButton = this.buttonsContainer.addChild(SwtButton) as SwtButton ;
          this.unMatchButton = this.buttonsContainer.addChild(SwtButton) as SwtButton ;
          this.suspendButton = this.buttonsContainer.addChild(SwtButton) as SwtButton ;
          this.confirmButton = this.buttonsContainer.addChild(SwtButton) as SwtButton ;
          this.reconButton = this.buttonsContainer.addChild(SwtButton) as SwtButton;
          this.removeButton =  this.buttonsContainer.addChild(SwtButton) as SwtButton;
          this.addButton = this.buttonsContainer.addChild(SwtButton) as SwtButton;
          this.optionsButton  = this.buttonsContainer.addChild(SwtButton) as SwtButton;
          this.closeButton = this.buttonsContainer.addChild(SwtButton) as SwtButton;
          /*Displays the Confirm button*/
          this.confirmButton.id="confirmButton";
          this.confirmButton.toolTip=SwtUtil.getPredictMessage('tooltip.ConfMatch', null);
          this.confirmButton.label=SwtUtil.getPredictMessage('button.confirm', null);
          this.confirmButton.click = () => {
            this.confirmMvmnt();
          };

          /*Displays the Suspend button*/
          this.suspendButton.id="suspendButton";
          this.suspendButton.toolTip = SwtUtil.getPredictMessage('tooltip.suspMatch', null);
          this.suspendButton.label = SwtUtil.getPredictMessage('button.suspend', null);
          this.suspendButton.click = (data) => {
            this.suspendMvmnt();
          };
          this.setAllButtonsStatus();

          this.prevButton.enabled=false;
          this.mvmntButton.enabled=false;
          this.removeButton.enabled=false;
          this.nextButton.enabled=false;
          this.noteButton.enabled=false;
          this.logButton.enabled=false;
          this.unMatchButton.enabled=false;
          this.suspendButton.enabled=false;
          this.confirmButton.enabled=false;
          this.reconButton.enabled=false;
          this.addButton.enabled=false;
        }
        if (ExternalInterface.call('eval', 'status') == "S") {
          // Sets the button status
          this.prevButton = this.buttonsContainer.addChild(SwtButton) as SwtButton ;
          this.nextButton =  this.buttonsContainer.addChild(SwtButton) as SwtButton ;
          this.noteButton = this.buttonsContainer.addChild(SwtButton) as SwtButton ;
          this.logButton = this.buttonsContainer.addChild(SwtButton) as SwtButton;
          this.mvmntButton = this.buttonsContainer.addChild(SwtButton) as SwtButton ;
          this.unMatchButton = this.buttonsContainer.addChild(SwtButton) as SwtButton ;
          this.confirmButton = this.buttonsContainer.addChild(SwtButton) as SwtButton ;
          this.reconButton = this.buttonsContainer.addChild(SwtButton) as SwtButton;
          this.removeButton =  this.buttonsContainer.addChild(SwtButton) as SwtButton;
          this.addButton = this.buttonsContainer.addChild(SwtButton) as SwtButton;
          this.optionsButton  = this.buttonsContainer.addChild(SwtButton) as SwtButton;
          this.closeButton = this.buttonsContainer.addChild(SwtButton) as SwtButton;
          /*Displays the Confirm button*/
          this.confirmButton.id="confirmButton";
          this.confirmButton.toolTip=SwtUtil.getPredictMessage('tooltip.ConfMatch', null);
          this.confirmButton.label=SwtUtil.getPredictMessage('button.confirm', null);
          this.confirmButton.click = () => {
            this.confirmMvmnt();
          };

          this.setAllButtonsStatus();
          this.prevButton.enabled=false;
          this.mvmntButton.enabled=false;
          this.removeButton.enabled=false;
          this.nextButton.enabled=false;
          this.noteButton.enabled=false;
          this.logButton.enabled=false;
          this.unMatchButton.enabled=false;
          this.confirmButton.enabled=false;
          this.reconButton.enabled=false;
          this.addButton.enabled=false;

        }
        /* Sets the buton status for Confirmed match screen */
        if (ExternalInterface.call('eval', 'status') == "C") {
          this.prevButton = this.buttonsContainer.addChild(SwtButton) as SwtButton ;
          this.nextButton =  this.buttonsContainer.addChild(SwtButton) as SwtButton ;
          this.noteButton = this.buttonsContainer.addChild(SwtButton) as SwtButton ;
          this.logButton = this.buttonsContainer.addChild(SwtButton) as SwtButton;
          this.mvmntButton = this.buttonsContainer.addChild(SwtButton) as SwtButton ;
          this.unMatchButton = this.buttonsContainer.addChild(SwtButton) as SwtButton ;
          this.suspendButton = this.buttonsContainer.addChild(SwtButton) as SwtButton ;
          this.reconButton = this.buttonsContainer.addChild(SwtButton) as SwtButton;
          this.removeButton =  this.buttonsContainer.addChild(SwtButton) as SwtButton;
          this.addButton = this.buttonsContainer.addChild(SwtButton) as SwtButton;
          this.optionsButton  = this.buttonsContainer.addChild(SwtButton) as SwtButton;
          this.closeButton = this.buttonsContainer.addChild(SwtButton) as SwtButton;
          /*Displays the Suspend button*/
          this.suspendButton.id="suspendButton";
          this.suspendButton.toolTip = SwtUtil.getPredictMessage('tooltip.suspMatch', null);
          this.suspendButton.label = SwtUtil.getPredictMessage('button.suspend', null);
          this.suspendButton.click = (data) => {
            this.suspendMvmnt();
          };
          this.setAllButtonsStatus();
          this.prevButton.enabled=false;
          this.mvmntButton.enabled=false;
          this.removeButton.enabled=false;
          this.nextButton.enabled=false;
          this.noteButton.enabled=false;
          this.logButton.enabled=false;
          this.unMatchButton.enabled=false;
          this.suspendButton.enabled=false;
          // this.confirmButton.enabled=false;
          this.reconButton.enabled=false;
          this.addButton.enabled=false;

        }

        this.requestParams = [];
        this.inputData.url = this.baseURL + this.actionPath + this.actionMethod;
        this.inputData.send(this.requestParams);
      }
    } else { // == manual
      this.noteButton = this.buttonsContainer.addChild(SwtButton) as SwtButton ;
      this.logButton = this.buttonsContainer.addChild(SwtButton) as SwtButton;
      this.mvmntButton = this.buttonsContainer.addChild(SwtButton) as SwtButton ;
      this.unMatchButton = this.buttonsContainer.addChild(SwtButton) as SwtButton ;
      this.suspendButton = this.buttonsContainer.addChild(SwtButton) as SwtButton ;
      this.confirmButton = this.buttonsContainer.addChild(SwtButton) as SwtButton ;
      this.reconButton = this.buttonsContainer.addChild(SwtButton) as SwtButton;
      this.removeButton =  this.buttonsContainer.addChild(SwtButton) as SwtButton;
      this.addButton = this.buttonsContainer.addChild(SwtButton) as SwtButton;
      this.optionsButton  = this.buttonsContainer.addChild(SwtButton) as SwtButton;
      this.closeButton = this.buttonsContainer.addChild(SwtButton) as SwtButton;
      /*Displays the Confirm button*/
      this.confirmButton.id="confirmButton";
      this.confirmButton.toolTip=SwtUtil.getPredictMessage('tooltip.ConfMatch', null);
      this.confirmButton.label=SwtUtil.getPredictMessage('button.confirm', null);
      this.confirmButton.click = () => {
        this.confirmMvmnt();
      };

      /*Displays the Suspend button*/
      this.suspendButton.id="suspendButton";
      this.suspendButton.toolTip = SwtUtil.getPredictMessage('tooltip.suspMatch', null);
      this.suspendButton.label = SwtUtil.getPredictMessage('button.suspend', null);
      this.suspendButton.click = (data) => {
        this.suspendMvmnt();
      };
      this.setButtonsProperties();

      if (calledFrom == "mvmntdisplay") {
        this.actionMethod="method=flexshowMatchDetails";
        this.actionMethod= this.actionMethod + "&menuAccessId=" + ExternalInterface.call('eval', 'menuAccessId') + "&movId=" + ExternalInterface.call('eval', 'movId') + "&matchType=" + ExternalInterface.call('eval', 'screen') + "&matchList=" + ExternalInterface.call('eval', 'matches');
        this.actionMethod= this.actionMethod + "&day=" + "&matchCount=" + "&currencyCode=" + "&date=" + "&entityId=" + ExternalInterface.call('eval', 'entityId') + "&quality=" + ExternalInterface.call('eval', 'quality') + "&archiveId=" + ExternalInterface.call('eval', 'archiveId') + "&status=" + ExternalInterface.call('eval', 'status');
        // Then apply them to the url member of the HTTPComms object:
        this.inputData.url= this.baseURL + this.actionPath + this.actionMethod;
        this.inputData.send(this.requestParams);

        this.noteButton.enabled=true;
        this.logButton.enabled=true;
        this.mvmntButton.enabled=true;
        this.unMatchButton.enabled=true;
        this.suspendButton.enabled=true;
        this.confirmButton.enabled=true;
        this.reconButton.enabled=true;
        this.removeButton.enabled=true;
        this.addButton.enabled=true;
        this.optionsButton.enabled=true;

      } else if (calledFrom == "msd") {
        if (ExternalInterface.call('eval', 'matchId') != "") {
          this.actionMethod="method=flexdisplay";
          this.actionMethod= this.actionMethod + "&menuAccessId=" + ExternalInterface.call('eval', 'menuAccessId') + "&matchIds='" + ExternalInterface.call('eval', 'matchId') + "'" + "&matchType=manual";
          this.actionMethod= this.actionMethod + "&day=" + "&matchCount=" + "&currencyCode=" + "&status=" + "&date=" + "&entityId=" + "&quality=" + "&menuAccessId=" + "&applyCurrencyThreshold=" + "&noIncludedMovementMatches=" + "&dateTabIndFlag=" + "&dateTabInd=";
        } else {
          this.actionMethod="method=flexofferedMatch";
          this.actionMethod=this.actionMethod + "&menuAccessId=" + ExternalInterface.call('eval', 'menuAccessId') + "&selectedList=" + ExternalInterface.call('eval', 'selectedList') + "&selectedTab=" + ExternalInterface.call('eval', 'selectedTab') + "&selectedCurrencyCode=" + ExternalInterface.call('eval', 'currencyId');
          this.actionMethod=this.actionMethod + "&day=" + "&matchCount=" + "&currencyCode=" + "&date=" + "&entityCode=" + ExternalInterface.call('eval', 'entityId');
        }
        // Then apply them to the url member of the HTTPComms object:
        this.inputData.url=this.baseURL + this.actionPath + this.actionMethod;
        this.inputData.send(this.requestParams);

        this.noteButton.enabled=true;
        this.logButton.enabled=true;
        this.mvmntButton.enabled=true;
        this.unMatchButton.enabled=true;
        this.suspendButton.enabled=true;
        this.confirmButton.enabled=true;
        this.reconButton.enabled=true;
        this.removeButton.enabled=true;
        this.addButton.enabled=true;
        this.optionsButton.enabled=true;

      } else if (calledFrom == "generic") {
        // Display the given matchId details
        this.actionMethod="method=flexdisplay";
        this.actionMethod= this.actionMethod + "&menuAccessId=" + ExternalInterface.call('eval', 'menuAccessId') + "&matchIds='" + ExternalInterface.call("eval", "matchIdFromGeneric") +"'" + "&matchType=manual";
        this.actionMethod= this.actionMethod + "&day=" + "&matchCount=" + "&currencyCode=" + "&status=" + "&date=" + "&quality=" + "&menuAccessId=" + "&applyCurrencyThreshold=" + "&noIncludedMovementMatches=" + "&dateTabIndFlag=" + "&dateTabInd="+ "&archiveId=";
        this.actionMethod= this.actionMethod + "&entityId="+ExternalInterface.call('eval', 'entityId') + "&hostId="+ExternalInterface.call('eval', 'hostId');
        this.actionMethod= this.actionMethod + "&ScreenName=manual";
        this. inputData.url= this.baseURL + this.actionPath + this.actionMethod;
        this.inputData.send(this.requestParams);

        this.noteButton.enabled=false;
        this.logButton.enabled=false;
        this.mvmntButton.enabled=false;
        this.unMatchButton.enabled=false;
        this.suspendButton.enabled=false;
        this.confirmButton.enabled=false;
        this.reconButton.enabled=false;
        this.removeButton.enabled=false;
        this.addButton.enabled=false;
        this.optionsButton.enabled=false;
      } else {
        this.matchIdText.editable=true;
        this.matchIdText.setFocus();
        this.loadingImage.setVisible(false);
        this.noteButton.enabled=false;
        this.logButton.enabled=false;
        this.mvmntButton.enabled=false;
        this.unMatchButton.enabled=false;
        this.suspendButton.enabled=false;
        this.confirmButton.enabled=false;
        this.reconButton.enabled=false;
        this.removeButton.enabled=false;
        this.addButton.enabled=false;
        this.optionsButton.enabled=false;
      }
    }

    // ExternalInterface.call("onFlexInitialized");
  }

  private lastSelectedTooltipParams = null;
  getParamsFromParent() {
    const params = { sqlParams: this.lastSelectedTooltipParams, facilityId: this.tooltipFacilityId, selectedNodeId:this.selectedNodeId, treeLevelValue:this.treeLevelValue ,
      tooltipMvtId :this.tooltipMvtId, tooltipMatchId :this.tooltipMatchId};
    return params;
  }

  private eventsCreated = false;
  private customTooltip: any = null;
  public createTooltip (event){
      if(this.customTooltip && this.customTooltip.close)
        this.removeTooltip();
      try {   
      const toolTipWidth = 420;
      this.customTooltip = SwtPopUpManager.createPopUp(parent, EnhancedAlertingTooltip, {
      });
      if (window.innerHeight < this.positionY+450)
      this.positionY=120;
      this.customTooltip.setWindowXY( this.positionX+20, this.positionY);
      this.customTooltip.enableResize = false;
      this.customTooltip.width = ''+toolTipWidth;
      this.customTooltip.height = "450";
      this.customTooltip.enableResize = false;
      this.customTooltip.title = "Alert Summary Tooltip";
      this.customTooltip.showControls = true;
      this.customTooltip.showHeader = false;
    this.customTooltip.parentDocument = this;
    this.customTooltip.processBox = this;
    this.customTooltip.display();
    //event for display list button click
        setTimeout(() => {

          if (!this.eventsCreated) {
            this.customTooltip.getChild().DISPLAY_LIST_CLICK.subscribe((target) => {
              this.lastSelectedTooltipParams = target.noode.data
              ExternalInterface.call("openAlertInstanceSummary", "openAlertInstSummary", this.selectedNodeId, this.treeLevelValue);
            });
          }
        }, 0);

        //event for link to specific button click
        setTimeout(() => {
          if (!this.eventsCreated) {
            this.customTooltip.getChild().LINK_TO_SPECIF_CLICK.subscribe((target) => {
              this.getScenarioFacility(target.noode.data.scenario_id);
              this.lastSelectedTooltipParams = target.noode.data
              this.hostId = target.hostId;
            });
          }
        }, 0);

        //event for display list button click
        setTimeout(() => {

          if (!this.eventsCreated) {
            this.customTooltip.getChild().ITEM_CLICK.subscribe((target) => {
              this.selectedNodeId= target.noode.data.id;
              this.treeLevelValue= target.noode.data.treeLevelValue;
              this.customTooltip.getChild().linkToSpecificButton.enabled = false;
              if (target.noode.data.count == 1 && target.noode.isBranch ==false ) {
                this.customTooltip.getChild().linkToSpecificButton.enabled = true;
              }
            });
          }
        }, 0);

    } catch (error) {
        console.log("SwtCommonGrid -> createTooltip -> error", error)
            
    }
}

  getScenarioFacility(scenarioId) {
    this.requestParams = [];
    this.alertingData.cbStart = this.startOfComms.bind(this);
    this.alertingData.cbStop = this.endOfComms.bind(this);
    this.alertingData.cbFault = this.inputDataFault.bind(this);
    this.alertingData.encodeURL = false;
    // Define the action to send the request
    this.actionPath = "scenarioSummary.do?";
    // Define method the request to access
    this.actionMethod = 'method=getScenarioFacility';
    this.requestParams['scenarioId'] = scenarioId;
    this.alertingData.url = this.baseURL + this.actionPath + this.actionMethod;
    this.alertingData.cbResult = (event) => {
      this.openGoToScreen(event);
    };
    this.alertingData.send(this.requestParams);
  }

  openGoToScreen(event) {
    if(event && event.ScenarioSummary && event.ScenarioSummary.scenarioFacility){
    var facilityId = event.ScenarioSummary.scenarioFacility;


   	  
	  const selectedEntity = this.lastSelectedTooltipParams != null && this.lastSelectedTooltipParams['entity_id'] != null?this.lastSelectedTooltipParams['entity_id']:this.entityCombo.selectedLabel;
    const selectedcurrency_code = this.lastSelectedTooltipParams != null && this.lastSelectedTooltipParams['currency_code'] != null?this.lastSelectedTooltipParams['currency_code']:'All'
    const selectedmatch_id = this.lastSelectedTooltipParams != null && this.lastSelectedTooltipParams['match_id'] != null?this.lastSelectedTooltipParams['match_id']:null
    const selectedmovement_id = this.lastSelectedTooltipParams != null && this.lastSelectedTooltipParams['movement_id'] != null?this.lastSelectedTooltipParams['movement_id']:this.tooltipMvtId
    const selectedsweep_id = this.lastSelectedTooltipParams != null && this.lastSelectedTooltipParams['sweep_id'] != null?this.lastSelectedTooltipParams['sweep_id']:null


    ExternalInterface.call("goTo", facilityId, this.hostId, selectedEntity, selectedmatch_id, selectedcurrency_code, selectedmovement_id, selectedsweep_id, "");
    }
  }

public removeTooltip (){
  this.customTooltip.close();
}

  imageClickFunction(event) {
    const alerting = (this.jsonReader.getScreenAttributes()["scenarioAlerting"].toString() != undefined) ? this.jsonReader.getScreenAttributes()["scenarioAlerting"].toString() : "";
    if (alerting == "Y" || alerting == "C") {
      this.tooltipMvtId = null;
      this.tooltipMatchId = this.matchIdText.text;
      this.tooltipFacilityId = "MATCH_DISPLAY_MOVEMENT_ROW";
      this.tooltipOtherParams = [];
      this.createTooltip(event);
    }
  }


  itemClickFunction(event){
    this.tooltipMatchId = null;
    if (event.selectedCellTarget != null && event.selectedCellTarget.field != null && event.selectedCellTarget.field == "alerting" && (event.selectedCellTarget.data.alerting == "C" || event.selectedCellTarget.data.alerting == "Y")) {
      this.tooltipMvtId = event.selectedCellTarget.data.movement;
      this.tooltipFacilityId = "MATCH_DISPLAY_MOVEMENT_ROW";
      this.tooltipOtherParams = [];
      setTimeout(() => {
        this.createTooltip(null);
      }, 100);
    } else {
      this.removeTooltip();
    }
  }


  logicUpdateResult(event): void {
    let response  = event;
    let jsonLogicReader: JSONReader  = new JSONReader();
    jsonLogicReader.setInputJSON(response);
    if (jsonLogicReader.getRequestReplyStatus()) {

    } else {
      this.swtAlert.error("" + response.message);
    }
  }

  /**
   * inputDataResult
   * @param event:ResultEvent
   *
   * This method is called by the HTTPComms when result event occurs.
   *
   */
  inputDataResult(event) {
    let closeFlag = true;
    if (this.inputData.isBusy()) {
      this.inputData.cbStop();
    } else {
      this.lastReceivedJSON  = event;
      this.jsonReader.setInputJSON(this.lastReceivedJSON);
      this.exportContainer.enabled=true;
      this.lostConnectionText.visible=false;
      if (this.jsonReader.getRequestReplyStatus()) {
        this.entityCombo.setComboData(this.jsonReader.getSelects());
        if (JSON.stringify(this.lastReceivedJSON)  !== JSON.stringify(this.prevRecievedJSON) || (this.mmsdGrid.dataProvider.length == 0)) {
          this.selectedEntityId.text= this.jsonReader.getScreenAttributes()["entityid"];
          this.selectedEntityName.text= this.jsonReader.getScreenAttributes()["entityname"];
          this.currentFontSize = this.jsonReader.getScreenAttributes()["currfontsize"];
          if(this.jsonReader.getScreenAttributes()["matchid"]) {
            this.matchIdText.text = this.jsonReader.getScreenAttributes()["matchid"];
          } else {
            this.matchIdText.text = "";
          }

          this.matchIdText.editable = this.matchType == "manual";
          this.fullAccess = this.jsonReader.getScreenAttributes()["fullAccess"];

          this.matchStatus.text = this.jsonReader.getScreenAttributes()["matchstatus"];

          if (this.matchType != "manual") {
            this.matchIndices.text=this.jsonReader.getScreenAttributes()["matchindex"] + "/" + this.jsonReader.getScreenAttributes()["totalmatches"];
          }

          if (this.matchStatus.text == "CONFIRMED") {
            this.reconButton.visible = true;
            this.reconButton.includeInLayout=true;
          } else {
            this.reconButton.visible = false;
            this.reconButton.includeInLayout=false;
          }
          this.matchQuality.text=this.jsonReader.getScreenAttributes()["matchquality"];
          this.updateDate.text=this.jsonReader.getScreenAttributes()["updatedate"];
          this.updateUser.text=this.jsonReader.getScreenAttributes()["updateuser"];
          this.currencyCode.text=this.jsonReader.getScreenAttributes()["currencycode"];
          this.posIntlvl.text=this.jsonReader.getScreenAttributes()["posintlvl"];
          this.posExtlvl.text=this.jsonReader.getScreenAttributes()["posextlvl"];
          this.matchHash=this.jsonReader.getScreenAttributes()["matchHash"];
          this.ccyTol= this.jsonReader.getScreenAttributes()["ccyTolerance"]?this.jsonReader.getScreenAttributes()["ccyTolerance"]:0;
          this.suppMatchDiffWarning= this.jsonReader.getScreenAttributes()["suppMatchDiffWarning"];
          this.currencyPattern= this.jsonReader.getScreenAttributes()["currencyPattern"];
          this.ccyTolValue.text=checkCurrencyPlacesFromNumber((this.ccyTol).toString(),this.currencyPattern,this.currencyCode.text);
          if (!this.jsonReader.isDataBuilding()) {
            this.dataBuildingText.visible=false;
            const obj  = {columns: this.jsonReader.getColumnData()};
            this.mmsdGrid.doubleClickEnabled  = true;
            this.mmsdGrid.CustomGrid(obj);
            for (let i = 0; i < this.mmsdGrid.columnDefinitions.length; i++) {
              let column = this.mmsdGrid.columnDefinitions[i];
              if (column.field == "alerting") {
                const alertUrl = "./"+ ExternalInterface.call('eval', 'alertOrangeImage');
                const alerCrittUrl = "./"+ ExternalInterface.call('eval', 'alertRedImage');               
                if (this.currentFontSize == "Normal"){
                  column['properties'] = {
                    enabled: false,
                    columnName: 'alerting',
                    imageEnabled: alertUrl,
                    imageCritEnabled:alerCrittUrl,
                    imageDisabled: "",
                    _toolTipFlag: true,
                    style: ' display: block; margin-left: auto; margin-right: auto;'

                  };

                }else{
                  column['properties'] = {
                    enabled: false,
                    columnName: 'alerting',
                    imageEnabled: alertUrl,
                    imageCritEnabled:alerCrittUrl,
                    imageDisabled: "",
                    _toolTipFlag: true,
                    style: 'height:15px; width:15px; display: block; margin-left: auto; margin-right: auto;'

                  };
				  
		  }
				
                this.mmsdGrid.columnDefinitions[i].editor = null;
                this.mmsdGrid.columnDefinitions[i].formatter = AlertingRenderer;
              }
            }
            this.mmsdGrid.colWidthURL ( this.baseURL +"movementmatchdisplay.do?");
            this.mmsdGrid.colOrderURL ( this.baseURL +"movementmatchdisplay.do?");
            this.mmsdGrid.saveWidths = true;
            this.mmsdGrid.saveColumnOrder = true;
            this.mmsdGrid.listenHorizontalScrollEvent = true;
            if (this.jsonReader.getGridData()) {
              if (this.jsonReader.getGridData().size > 0) {

                this.mmsdGrid.gridData  = this.jsonReader.getGridData();
                this.mmsdGrid.setRowSize  = this.jsonReader.getRowSize();
                this.mmsdGrid.allowMultipleSelection = true;

              } else {
                this.mmsdGrid.gridData = {row: [], size: 0};
                // this.mmsdGrid.selectedIndex = -1;
              }
            } else {
              this.mmsdGrid.gridData = {row: [], size: 0};
            }

           
            this.acctAccessFlag = 
            typeof this.jsonReader.getScreenAttributes()["acctaccessstatus"] === "boolean" 
                ? (this.jsonReader.getScreenAttributes()["acctaccessstatus"] as boolean)
                : String(this.jsonReader.getScreenAttributes()["acctaccessstatus"]).toLowerCase() !== "false";

            // Sets the data grid style based on font size
            if (this.currentFontSize == "Normal") {
              this.mmsdGrid.styleName="dataGridNormal";
              this.mmsdGrid.rowHeight = 18;
              this.selectedFont=0;

            } else if (this.currentFontSize == "Small") {
              this.mmsdGrid.styleName="dataGridSmall";

              this.mmsdGrid.rowHeight=15;
              this.selectedFont=1;
            }

          } else {
            this.dataBuildingText.visible=true;
          }
          this.mmsdGrid.entityID = this.entityCombo.selectedLabel;
          this.prevRecievedJSON = this.lastReceivedJSON;
        }
      } else {
        if ((this.jsonReader.getRequestReplyMessage()) == "Match not on file") {
          if (ExternalInterface.call('eval', 'calledFrom') == "msd") {
            this.swtAlert.warning(this.jsonReader.getRequestReplyMessage() + "\n", SwtUtil.getPredictMessage('screen.warning', null), Alert.OK, this, this.alertListenerCloseWindow, null);
            closeFlag=false;
            return;
          } else {
            this.swtAlert.warning(this.jsonReader.getRequestReplyMessage() + "\n", SwtUtil.getPredictMessage('screen.warning', null));
            //return;
          }
        } else if ((this.jsonReader.getRequestReplyMessage()) == "Movements not available for this MatchId") {
          this.swtAlert.warning(this.jsonReader.getRequestReplyMessage() + "\n" + this.jsonReader.getRequestReplyLocation(), SwtUtil.getPredictMessage('screen.warning', null));
          return;
        } else {
          this.swtAlert.error(this.jsonReader.getRequestReplyMessage() + "\n" + this.jsonReader.getRequestReplyLocation(), SwtUtil.getPredictMessage('screen.error', null));
          return;
        }
      }// else
    }// else not busy
    this.previousSelectedIndices = [];
    this.matchType = ExternalInterface.call('eval', 'match');
    if (this.matchType !== "manual") {
      /* Condition checked to disable the previous button while the first match is displayed */
      if (parseInt(this.jsonReader.getScreenAttributes()["matchindex"],10) == 1 && (parseInt(this.jsonReader.getScreenAttributes()["totalmatches"],10) - 1)) {
        this.prevButton.enabled=false;
        this.prevButton.buttonMode=false;
        this.nextButton.enabled=true;
        this.nextButton.buttonMode=true;
      }

      /* Condition checked to enable the previous button and next button  */
      if (parseInt(this.jsonReader.getScreenAttributes()["matchindex"],10) != 1 && (parseInt(this.jsonReader.getScreenAttributes()["totalmatches"],10) - 1)) {
        this.prevButton.enabled=true;
        this.prevButton.buttonMode=true;
        this.nextButton.enabled=true;
        this.nextButton.buttonMode=true;
      }
      /* Condition checked to disable the next button while the current match index equals to
              total matches.  */
      if (parseInt(this.jsonReader.getScreenAttributes()["matchindex"],10) == parseInt(this.jsonReader.getScreenAttributes()["totalmatches"],10)) {
        this.nextButton.enabled=false;
        this.nextButton.buttonMode=false;
      }
      if (parseInt(this.jsonReader.getScreenAttributes()["totalmatches"],10) == 1) {
        this.prevButton.enabled=false;
        this.prevButton.buttonMode=false;
        this.nextButton.enabled=false;
        this.nextButton.buttonMode=false;
      }
    }
    setTimeout(() => {
      this.predictStatusAmtTotalWarning(); 
    }, 100);
  
    //calculate the difference
    setTimeout(() => {
      this.CalculateAmtDiff();
    }, 100);


    if (this.matchType === "manual") {
      // this.exportContainer.enabled = true;
      if (this.jsonReader.getScreenAttributes()["matchstatus"] == "OFFERED") {
        if (!this.buttonsContainer.contains(this.confirmButton)) {
          this.addButtonsToButtonContainer("confirmButton",this.confirmbuttonIndex);
        }
        if (!this.buttonsContainer.contains(this.suspendButton)) {
          this.addButtonsToButtonContainer("suspendButton", this.suspendbuttonIndex);
        }
        if (!this.buttonsContainer.contains(this.reconButton)) {
          this.addButtonsToButtonContainer("reconButton", this.reconcilebuttonIndex);
        }
        this.noteButton.enabled=true;
        this.logButton.enabled=true;
        this.mvmntButton.enabled=false;
        this.unMatchButton.enabled=true;
        this.suspendButton.enabled=true;
        this.confirmButton.enabled=true;
        this.reconButton.enabled=true;
        this.removeButton.enabled=false;
        this.addButton.enabled=true;
      }

      if (this.jsonReader.getScreenAttributes()["matchstatus"] == "SUSPENDED") {
        if (!this.buttonsContainer.contains(this.reconButton)) {
          this.addButtonsToButtonContainer("reconButton", this.reconcilebuttonIndex);
        }
        if (!this.buttonsContainer.contains(this.confirmButton)) {
          this.addButtonsToButtonContainer("confirmButton",this.confirmbuttonIndex);
        }
        this.noteButton.enabled=true;
        this.logButton.enabled=true;
        this.mvmntButton.enabled=false;
        this.unMatchButton.enabled=true;
        this.confirmButton.enabled=true;
        this.reconButton.enabled=true;
        this.removeButton.enabled=false;
        this.addButton.enabled=true;
        if (this.buttonsContainer.contains(this.suspendButton)) {
          this.suspendbuttonIndex=this.buttonsContainer.getChildIndex(this.suspendButton);
          this.buttonsContainer.removeChild(this.suspendButton);
        }
      }

      if (this.jsonReader.getScreenAttributes()["matchstatus"] == "CONFIRMED") {
        if (!this.buttonsContainer.contains(this.reconButton)) {
          this.addButtonsToButtonContainer("reconButton", this.reconcilebuttonIndex);
        }

        if (!this.buttonsContainer.contains(this.suspendButton)) {
          this.addButtonsToButtonContainer("suspendButton", this.suspendbuttonIndex);
        }

        this.noteButton.enabled=true;
        this.logButton.enabled=true;
        this.mvmntButton.enabled=false;
        this.unMatchButton.enabled=true;
        this.suspendButton.enabled=true;
        this.reconButton.enabled=true;
        this.removeButton.enabled=false;
        this.addButton.enabled=true;
        if (this.buttonsContainer.contains(this.confirmButton)) {
          this.confirmbuttonIndex = this.buttonsContainer.getChildIndex(this.confirmButton);
          this.buttonsContainer.removeChild(this.confirmButton);
        }

      }

      if (this.jsonReader.getScreenAttributes()["matchstatus"] == "RECONCILED") {
        if (!this.buttonsContainer.contains(this.confirmButton)) {
          this.addButtonsToButtonContainer("confirmButton",this.confirmbuttonIndex);
        }

        if (!this.buttonsContainer.contains(this.suspendButton)) {
          this.addButtonsToButtonContainer("suspendButton", this.suspendbuttonIndex);
        }

        this.noteButton.enabled=true;
        this.logButton.enabled=true;
        this.mvmntButton.enabled=false;
        this.unMatchButton.enabled=true;
        this.suspendButton.enabled=true;
        this.confirmButton.enabled=true;
        this.removeButton.enabled=false;
        this.addButton.enabled=true;
        if (this.buttonsContainer.contains(this.reconButton)) {
          this.reconcilebuttonIndex=this.buttonsContainer.getChildIndex(this.reconButton);
          this.buttonsContainer.removeChild(this.reconButton);
        }
      }
      if (this.jsonReader.getScreenAttributes()["matchstatus"] == "") {
        this.noteButton.enabled = false;
        this.logButton.enabled = false;
        this.mvmntButton.enabled =false;
        this.unMatchButton.enabled = false;
        this.suspendButton.enabled = false;
        if (this.buttonsContainer.contains(this.suspendButton)) {
          this.suspendButton.enabled = false;
        }
        if (this.buttonsContainer.contains(this.confirmButton)) {
          this.confirmButton.enabled = false;
        }
        // reconButton.enabled=false;
        this.removeButton.enabled=false;
        this.addButton.enabled=false;
      }
      if (this.lastReceivedJSON.movementmatchsummarydisplay.grid == undefined || this.lastReceivedJSON.movementmatchsummarydisplay.grid.rows.size== 0) {
        this.matchIndices.text="";
        this.matchStatus.text="";
        this.matchQuality.text="";
        this.updateDate.text="";
        this.updateUser.text="";
        this.currencyCode.text="";
        this.posIntlvl.text="";
        this.posExtlvl.text="";
        this.selectedEntityId.text="";
        if (this.mmsdGrid.gridData.length != 0) {
          this.mmsdGrid.gridData = {row: [], size: 0};
          //this.mmsdGrid.clearSortFilter();
        }
        this.selectedEntityName.text="";
        this.noteButton.enabled=false;
        this.logButton.enabled=false;
        this.mvmntButton.enabled=false;
        this.unMatchButton.enabled=false;
        if (this.buttonsContainer.contains(this.suspendButton)) {
          this.suspendButton.enabled=false;
        }

        if (this.buttonsContainer.contains(this.confirmButton)) {
          this.confirmButton.enabled = false;
        }
        if (this.buttonsContainer.contains(this.reconButton)) {
          this.reconButton.enabled = false;
        }


        this.removeButton.enabled=false;
        this.addButton.enabled=false;
        this.exportContainer.enabled=false;
      }
    } else { // (this.matchType != "manual")
      if (ExternalInterface.call('eval', 'status') == "M") {
        this.noteButton.enabled=true;
        this.logButton.enabled=true;
        this.mvmntButton.enabled=false;
        this.unMatchButton.enabled=true;
        this.suspendButton.enabled=true;
        this.confirmButton.enabled=true;
        this.reconButton.enabled=true;
        this.removeButton.enabled=false;
        this.addButton.enabled=true;
      }
      if (ExternalInterface.call('eval', 'status') == "S") {
        this.noteButton.enabled=true;
        this.logButton.enabled=true;
        this.mvmntButton.enabled=false;
        this.unMatchButton.enabled=true;
        this.confirmButton.enabled=true;
        this.reconButton.enabled=true;
        this.removeButton.enabled=false;
        this.addButton.enabled=true;
      }
      if (ExternalInterface.call('eval', 'status') == "C") {
        if (this.jsonReader.getScreenAttributes()["matchstatus"] == "RECONCILED") {
          this.noteButton.enabled=true;
          this.logButton.enabled=true;
          this.mvmntButton.enabled=false;
          this.unMatchButton.enabled=true;
          this.suspendButton.enabled=true;
          this.removeButton.enabled=false;
          this.addButton.enabled=true;
          if (this.buttonsContainer.contains(this.reconButton)) {
            this.reconcilebuttonIndex=this.buttonsContainer.getChildIndex(this.reconButton);
            this.buttonsContainer.removeChild(this.reconButton);
          }
          if (!this.buttonsContainer.contains(this.confirmButton)) {
            this.addButtonsToButtonContainer("confirmButton",this.confirmbuttonIndex);
          }
          this.confirmButton.enabled=true;
        } else {
          this.noteButton.enabled=true;
          this.logButton.enabled=true;
          this.mvmntButton.enabled=false;
          this.unMatchButton.enabled=true;
          this.suspendButton.enabled=true;
          this.reconButton.enabled=true;
          this.removeButton.enabled=false;
          this.addButton.enabled=true;
        }
      }
    }
    let notes: string = this.jsonReader.getScreenAttributes()["usernotes"];
    if (notes == "Y") {
      this.noteImage.source =this.baseURL + ExternalInterface.call('eval', 'notesImage');
      this.noteImage.toolTip="Notes Available";
    } else {
      this.noteImage.source=this.baseURL + ExternalInterface.call('eval', 'blankImage');
      this.noteImage.toolTip="";
    }

    // add GUI Indicator
    const alerting = (this.jsonReader.getScreenAttributes()["scenarioAlerting"].toString() != undefined) ? this.jsonReader.getScreenAttributes()["scenarioAlerting"].toString() : "";
    if (alerting == "Y") {
      this.alertImage.source = this.baseURL + ExternalInterface.call('eval', 'alertOrangeImage');
      this.alertImage.toolTip = "alerts Available";
    } else if (alerting == "C") {
      this.alertImage.source = this.baseURL + ExternalInterface.call('eval', 'alertRedImage');
      this.alertImage.toolTip = "alerts Available";
    } else {
      this.alertImage.source = this.baseURL + ExternalInterface.call('eval', 'blankImage');
      this.alertImage.toolTip = "";
    }

    if ( this.matchType != "manual" && (this.lastReceivedJSON.movementmatchsummarydisplay.grid && this.lastReceivedJSON.movementmatchsummarydisplay.grid.rows.size == 0))  {
      ExternalInterface.call("parentFormRefresh1");
    }

    if (this.matchType == "manual" && (ExternalInterface.call('eval', 'inputMethod') == "movementDisplay")) {
      if (this.lastReceivedJSON.movementmatchsummarydisplay.grid.rows.size == 0) {
        ExternalInterface.call("parentFormMovDispRefresh");
      }
    }

    if (closeFlag && (ExternalInterface.call('eval', 'calledFrom') == "msd") && (this.lastReceivedJSON.movementmatchsummarydisplay.grid && this.lastReceivedJSON.movementmatchsummarydisplay.grid.rows.size == 0)) {
      ExternalInterface.call("close");
    }

    if (this.lastReceivedJSON.movementmatchsummarydisplay.grid != undefined && this.lastReceivedJSON.movementmatchsummarydisplay.grid.rows.size != 0) {
      this.matchIds = this.matchIdText.text;
    }
    if (! this.acctAccessFlag) {
      this.removeButton.enabled=false;
      this.removeButton.buttonMode=false;
      this.unMatchButton.enabled=false;
      this.reconButton.enabled=false;
      this.reconButton.buttonMode=false;
      this.unMatchButton.buttonMode=false;
      this.suspendButton.enabled=false;
      this.suspendButton.buttonMode=false;
      if (this.buttonsContainer.contains(this.confirmButton)) {
        this.confirmButton.enabled = false;
        this.confirmButton.buttonMode=false;
      }

      this.addButton.enabled=false;
      this.addButton.buttonMode=false;
    }
    if (ExternalInterface.call('eval', 'archiveId') != "") {
      this.removeButton.enabled=false;
      this.removeButton.buttonMode=false;
      this.mvmntButton.enabled=false;
      this.mvmntButton.buttonMode=false;
      this.logButton.enabled=false;
      this.logButton.buttonMode=false;
      this.unMatchButton.enabled=false;
      this.unMatchButton.buttonMode=false;
      this.suspendButton.enabled=false;
      this.suspendButton.buttonMode=false;
      if (this.buttonsContainer.contains(this.confirmButton)) {
        this.confirmButton.enabled = false;
        this.confirmButton.buttonMode=false;
      }

      this.addButton.enabled=false;
      this.addButton.buttonMode=false;
    }
    if (this.refreshFlag) {
      ExternalInterface.call("CallBackApp");
    }
    this.mmsdDisableButton();
    if (!BooleanParser.parseBooleanValue(this.fullAccess)) {
      this.unMatchButton.enabled=false;
      this.suspendButton.enabled=false;
      if (this.buttonsContainer.contains(this.confirmButton)) {
        this.confirmButton.enabled = false;
      }
      this.reconButton.enabled=false;
      this.addButton.enabled=false;
    }
  }

  addButtonsToButtonContainer(buttonId, index){
    if(buttonId == 'confirmButton'){
      this.confirmButton = this.buttonsContainer.addChildAt(SwtButton, index) as SwtButton;
      this.confirmButton.id="confirmButton";
      this.confirmButton.toolTip=SwtUtil.getPredictMessage('tooltip.ConfMatch', null);
      this.confirmButton.label=SwtUtil.getPredictMessage('button.confirm', null);
      this.confirmButton.click = () => {
        this.confirmMvmnt();
      };
    }
    if(buttonId == 'suspendButton'){

      this.suspendButton = this.buttonsContainer.addChildAt(SwtButton, index) as SwtButton;
      this.suspendButton.id="suspendButton";
      this.suspendButton.toolTip = SwtUtil.getPredictMessage('tooltip.suspMatch', null);
      this.suspendButton.label = SwtUtil.getPredictMessage('button.suspend', null);
      this.suspendButton.click = (data) => {
        this.suspendMvmnt();
      };
    }
    if(buttonId == 'reconButton'){
      this.reconButton = this.buttonsContainer.addChildAt(SwtButton, index) as SwtButton;
      this.reconButton.id="reconButton";
      this.reconButton.toolTip=SwtUtil.getPredictMessage('tooltip.reconMatch', null);
      this.reconButton.label=SwtUtil.getPredictMessage('button.reconcile', null);
      this.reconButton.click = () => {
        this.reconcileMvmnt();
      };
    }

  }


  /**
   *  If a fault occurs with the connection with the server then display the lost connection label
   **/
  public inputDataFault(event): void {
    this.invalidComms = event.fault.faultstring + "\n" + event.fault.faultCode + "\n" + event.fault.faultDetail;
  }

  predictStatusAmtTotalWarning(): void {
    let highestPosLvl = [];
    let checkextbalInc="";
    if ((this.lastReceivedJSON.movementmatchsummarydisplay.grid)&& this.lastReceivedJSON.movementmatchsummarydisplay.grid.rows.size > 0) {
      for (let high=0; high < this.mmsdGrid.dataProvider.length; high++) {
        highestPosLvl[high]= this.mmsdGrid.dataProvider[high].slickgrid_rowcontent.pos.positionlevel;
      }



      if (this.PosLvlCorrAmts(highestPosLvl)) {
        this.swtAlert.warning( SwtUtil.getPredictMessage('label.matchDifferentAmountTotalsAcrossPositionLevels', null));
      }
      if (this.PosLvlCorrPreductStatus(highestPosLvl)) {
        this.swtAlert.warning( SwtUtil.getPredictMessage('label.includedItemsAtMultiplePositionLevels', null));
      }
      checkextbalInc = ExternalInterface.call("checkUnmatchFlag", this.matchIdText.text, "", "ExtBal",this.selectedEntityId.text);
      if (BooleanParser.parseBooleanValue(checkextbalInc)) {
        this.swtAlert.warning(
            SwtUtil.getPredictMessage('label.includedItemsForExternalBalance', null)
        );
    }
    }
  }


  /**
   * Function called when help button is clicked.
   */
  helpHandler(): void {
    try {
      ExternalInterface.call("help");
    } catch (e) {
      SwtUtil.logError(e,   this.moduleId, 'MovementMatchSummaryDisplay', 'doHelp',   this.errorLocation);
    }
  }

  mmsdDisableButton(): void {
    let screenName: string = ExternalInterface.call('eval', 'calledFrom');
    let parentParentScreen: string = ExternalInterface.call('eval', 'parentScreen');
    if (screenName == "mvmntdisplay" && (parentParentScreen != "movementSummaryDisplay") && this.matchIdText.text != "") {
      let lockUserId: string = ExternalInterface.call("checkLockForMatch", this.matchIdText.text);
      if (lockUserId != "false") {
        this.swtAlert.warning( SwtUtil.getPredictMessage('label.mvmtAreBusy', null) + " " + lockUserId);
        this.mvmntButton.enabled=false;
        this.mvmntButton.buttonMode=false;
        this.unMatchButton.enabled=false;
        this.unMatchButton.buttonMode=false;
        this.suspendButton.enabled=false;
        this.suspendButton.buttonMode=false;
        if (this.buttonsContainer.contains(this.confirmButton)) {
          this.confirmButton.enabled = false;
          this.confirmButton.buttonMode=false;
        }

        this.reconButton.enabled=false;
        this.reconButton.buttonMode=false;
        this.removeButton.enabled=false;
        this.removeButton.buttonMode=false;
        this.addButton.enabled=false;
        this.addButton.buttonMode=false;
        this.mmsdGrid.selectable=false;
        this.mmsdGrid.removeEventListener('onRowClick', this.cellLogic, false);
        this.mmsdGrid.removeEventListener('onFilterChanged', this.cellLogic, false);
      }
    }
  }

  alertListenerCloseWindow(eventObj): void {
    if (eventObj.detail == Alert.OK) {
      ExternalInterface.call("close");
    }
  }

  /** This function is used to display notes avialable for the selected movements.
   */
  openNotes(): void {
    this.subScreenName="Notes";
    this.validateMatchId();
  }


  /** This function is used to suspend the  movement when the remove buuton
   * is clicked.
   */
  openSuspendMvmnt(): void {

    this.confirmFlagOk=false;
    this.reconcileFlag=false;
    this.removeFlagUnmatch=false;
    this.confirmFlagListener=false;
    this.reconcileFlagLisner=false;
    let rowCount = this.mmsdGrid.dataProvider.length;
    let CheckLockFlag="";
    let movements="";
    let checkUser=false;
    // get the screen name from calledFrom
    let parentScreen: string=ExternalInterface.call('eval', 'calledFrom');
    // get the screen name from parentScreen
    let parentParentScreen: string=ExternalInterface.call('eval', 'parentScreen');
    // get the screen name from loggedInUser
    let loggedInUser: string=ExternalInterface.call('eval', 'loggedInUser');
    for (let i=0; i < rowCount; i++) {
      movements+=this.mmsdGrid.dataProvider[i].movement + ",";
    }
    // CheckLockFlag is used to  Gets the match status
    CheckLockFlag=ExternalInterface.call("checkLocksOnServer", movements);


    if ((loggedInUser == CheckLockFlag) && ((parentScreen == "mvmntdisplay") && (parentParentScreen == "movementSummaryDisplay"))) {
      checkUser=true;
    }
    // check the match status
    if (!BooleanParser.parseBooleanValue(CheckLockFlag) && !checkUser) {
      this.swtAlert.warning( SwtUtil.getPredictMessage('label.mvmtAreBusy', null) + " " + CheckLockFlag);
    } else {
      let positionLevel=0;
      let highestPosLvl=[];
      let submitFlag=false;
      let PredictFlag=false;
      let confirmFlag=false;
      for (let high=0; high < this.mmsdGrid.dataProvider.length; high++) {
        highestPosLvl[high]=this.mmsdGrid.dataProvider[high].slickgrid_rowcontent.pos.positionlevel;
      }
      for (let k=1; k < highestPosLvl.length; k++) {
        if (highestPosLvl[k - 1] != highestPosLvl[k]) {
          confirmFlag=true;
        }

      }
      if (confirmFlag) {
        if (this.PosLvlCorrAmts(highestPosLvl)) {
          let windowTitle='Microsoft Internet Explorer';
          let suspendTestMessage='This match has different amount totals across position levels.Do you wish to continue?';
          this.swtAlert.question(suspendTestMessage, windowTitle, Alert.OK | Alert.CANCEL, this, this.alertListener1.bind(this), null);
          this.suspendFlagListener=true;

        } else {
          let entityId: string= this.selectedEntityId.text;
          let lockflag1: string=ExternalInterface.call("lockMatchOnServer", "suspend", this.matchIds, entityId);
          if (!BooleanParser.parseBooleanValue(lockflag1)) {
            this.swtAlert.warning(SwtUtil.getPredictMessage('label.matchIsInUseByanotherProcess', null));
          } else {
            let screenName: string=ExternalInterface.call('eval', 'match');
            if (screenName == "manual" && ExternalInterface.call('eval', 'calledFrom') != "msd") {
              this.refreshFlag=true;
              this.actionMethod="method=suspend";
              this.actionMethod=this.actionMethod + "&nextRecord=next";
              this.actionMethod=this.actionMethod + "&day=" + "&matchCount=" + "&menuAccessId=" + "&manualMatchScreen=frommanualMatchScreen" + "&matchId=" + this.jsonReader.getScreenAttributes()["matchid"] + "&applyCurrencyThreshold=" + ExternalInterface.call('eval', 'applyCurrencyThreshold') + "&noIncludedMovementMatches=" + ExternalInterface.call('eval', 'noIncludedMovementMatches') + "&dateTabIndFlag=" + "&dateTabInd=" + ExternalInterface.call('eval', 'dateTabInd') + "&matchType=" + ExternalInterface.call('eval', 'screen') + "&inputMethod=" + ExternalInterface.call('eval', 'inputMethod') + "&currentIndex=" + "" + (parseInt(this.jsonReader.getScreenAttributes()["matchindex"],10) - 1) + "&accountAccessStatus=" +  this.acctAccessFlag;

              let matchId: string = this.jsonReader.getScreenAttributes()["matchid"];
              this.changeMatchService.url=this.baseURL + this.actionPath + this.actionMethod;
              this.requestParams["movement.id.entityId"]=this.jsonReader.getScreenAttributes()["entityid"];
              this.requestParams["movement.matchStatus"]= ExternalInterface.call('eval', 'status');
              this.requestParams["movement.currencyCode"]=this.jsonReader.getScreenAttributes()["currencycode"];
              this.requestParams["movementDetail.matchQuality"]=this.jsonReader.getScreenAttributes()["matchquality"];
              this.changeMatchService.cbResult  = (event)  => {
                this.callFlexDisplay();
              };
              this.changeMatchService.send(this.requestParams);
            } else {
              this.actionMethod="method=suspend";
              this.actionMethod=this.actionMethod + "&nextRecord=next";
              this.actionMethod=this.actionMethod + "&day=" + "&matchCount=" + "&menuAccessId=" + "&matchId=" + this.jsonReader.getScreenAttributes()["matchid"] + "&applyCurrencyThreshold=" + ExternalInterface.call('eval', 'applyCurrencyThreshold') + "&noIncludedMovementMatches=" + ExternalInterface.call('eval', 'noIncludedMovementMatches') + "&dateTabIndFlag=" + "&dateTabInd=" + ExternalInterface.call('eval', 'dateTabInd') + "&matchType=" + ExternalInterface.call('eval', 'screen') + "&inputMethod=" + ExternalInterface.call('eval', 'inputMethod') + "&currentIndex=" + "" + (parseInt(this.jsonReader.getScreenAttributes()["matchindex"],10) - 1) + "&valueDate=" + ExternalInterface.call('eval', 'valueDate') + "&accountAccessStatus=" +  this.acctAccessFlag;
              this.inputData.url=this.baseURL + this.actionPath + this.actionMethod;
              this.requestParams["movement.id.entityId"]=this.jsonReader.getScreenAttributes()["entityid"];
              this.requestParams["movement.matchStatus"]= ExternalInterface.call('eval', 'status');
              this.requestParams["movement.currencyCode"]=this.jsonReader.getScreenAttributes()["currencycode"];
              this.requestParams["movementDetail.matchQuality"]=this.jsonReader.getScreenAttributes()["matchquality"];

              this.inputData.send(this.requestParams);
            }

          }
        }

      } else {
        let setWindowTitle='Microsoft Internet Explorer';
        let popupTestMessage='Only 1 Position level is available. Do you want to Continue ?';
        this.swtAlert.question(popupTestMessage, setWindowTitle, Alert.OK | Alert.CANCEL, this, this.alertListener.bind(this), null);
        this.suspendFlag=true;
      }

    }
  }

  /** This function is used to remove the selected movement when the remove buuton
   * is clicked.
   */
  openRemoveMvmnt(): void {
    this.suspendFlag=false;
    this.confirmFlagOk=false;
    this.reconcileFlag=false;
    this.removeFlagUnmatch=true;
    let selectedMvntList="";
    if (this.mmsdGrid.selectedIndices.length > 0) {
      for (let selectedListCounter=0; selectedListCounter < this.mmsdGrid.selectedIndices.length; selectedListCounter++) {
        selectedMvntList+="'" + this.mmsdGrid.dataProvider[this.mmsdGrid.selectedIndices[selectedListCounter]].movement + "',";
      }
    }
    let checkUnmatchFlag: string =ExternalInterface.call("checkUnmatchFlag", this.matchIdText.text, selectedMvntList, "unmatch", this.selectedEntityId.text);
    let title='Microsoft Internet Explorer';
    if (BooleanParser.parseBooleanValue(checkUnmatchFlag)) {
      let testMessageY='Removing these movements will delete the match. Continue?';
      this.swtAlert.question(testMessageY, title, Alert.OK | Alert.CANCEL, this, this.alertListener.bind(this), null);
    } else if (this.calculateRowsSelected()|| this.remainingRows()) {
      let removeTitle ='Microsoft Internet Explorer';
      let removePopupTestMessage='Removing these movements will delete the match. Continue?';

      this.swtAlert.question(removePopupTestMessage, removeTitle, Alert.OK | Alert.CANCEL, this, this.alertListener.bind(this), null);

    } else {
      let selectedMovementList="";
      if (this.mmsdGrid.selectedIndices.length > 0) {
        for (let selectedListCntr=0; selectedListCntr < this.mmsdGrid.selectedIndices.length; selectedListCntr++) {
          selectedMovementList+="'" + this.mmsdGrid.dataProvider[this.mmsdGrid.selectedIndices[selectedListCntr]].movement + "',";
        }
      }
      /* Forms the query */
      this.actionMethod="method=remove";
      this.actionMethod=this.actionMethod + "&day=" + "&matchCount=" + ExternalInterface.call('eval', 'matchCount') + "&selectedList=" + selectedMovementList + "&matchList=" + ExternalInterface.call('eval', 'matches') + "&menuAccessId=" + "&applyCurrencyThreshold=" + ExternalInterface.call('eval', 'applyCurrencyThreshold') + "&noIncludedMovementMatches=" + ExternalInterface.call('eval', 'noIncludedMovementMatches') + "&dateTabIndFlag=" + "&dateTabInd=" + ExternalInterface.call('eval', 'dateTabInd') + "&matchType=" + ExternalInterface.call('eval', 'screen') + "&inputMethod=" + ExternalInterface.call('eval', 'inputMethod') + "&currentIndex=" + "" + (parseInt(this.jsonReader.getScreenAttributes()["matchindex"],10) - 1) + "&valueDate=" + ExternalInterface.call('eval', 'valueDate') + "&accountAccessStatus=" +  this.acctAccessFlag;
      this.inputData.url=this.baseURL + this.actionPath + this.actionMethod;
      this.requestParams["movement.id.entityId"]=this.jsonReader.getScreenAttributes()["entityid"];
      this.requestParams["movement.matchId"]=this.jsonReader.getScreenAttributes()["matchid"];
      this.requestParams["movement.matchStatus"]= ExternalInterface.call('eval', 'status');
      this.requestParams["movementDetail.matchQuality"]=this.jsonReader.getScreenAttributes()["matchquality"];
      this.inputData.send(this.requestParams);

    }

  }


  /** This function is used to add the movement when the add buuton is clicked.
   *
   */
  openAddMovement(): void {
    let matchCount: string=ExternalInterface.call('eval', 'matchCount');
    let entityId: string=this.selectedEntityId.text;
    this.selectedMovements = [];
    let currencycode: string=this.currencyCode.text;
    let isAmountDiffer="N";
    let amt0: string=this.mmsdGrid.dataProvider[0].amount;
    let sign0: string=this.mmsdGrid.dataProvider[0].sign;
    for (let x=1; x < this.mmsdGrid.dataProvider.length; x++) {
      let amtTemp: string=this.mmsdGrid.dataProvider[x].amount;
      let signTemp: string=this.mmsdGrid.dataProvider[x].sign;
      if (amtTemp != amt0 || sign0 != signTemp) {
        isAmountDiffer="Y";
        break;
      }
    }
    let selectedMvmntsAmount: string =amt0 + sign0;
    ExternalInterface.call("openAddScreen", "addMvmnt", entityId, currencycode, isAmountDiffer, selectedMvmntsAmount, matchCount);
  }


  remainingRows(): boolean {
    let remainingRowsCount: number = this.lastReceivedJSON.movementmatchsummarydisplay.grid.rows.size - this.mmsdGrid.selectedIndices.length;
    let posLevel ="";
    if (remainingRowsCount > 0) {
      // case 1: one is remaining
      // case 1.1: there is only two rows: only one is selected -
      // case 1.2: only one row remains un-selected among many others
      // case 2: remaining rows in the same position level
      // case 2.1: there is many rows: only one is selected
      // case 2.2: there is many rows: many are selected
      if (remainingRowsCount == 1) {
        return true;
      } else if (this.mmsdGrid.selectedIndices.length == 1) {
        for (let k = 0; k < this.lastReceivedJSON.movementmatchsummarydisplay.grid.rows.size; k++) {
          if (this.mmsdGrid.dataProvider[k].movement !=  this.mmsdGrid.dataProvider[this.mmsdGrid.selectedIndex].movement) {
            if (posLevel == "") {
              posLevel = this.mmsdGrid.dataProvider[k].slickgrid_rowcontent.pos.positionlevel;
            } else {
              if (this.mmsdGrid.dataProvider[k].slickgrid_rowcontent.pos.positionlevel !== posLevel ) {
                return false;
              }
            }
          }
        }
        return true;
      } else if (this.mmsdGrid.selectedIndices.length > 1) {
        let movementSelected = [];
        for (let j = 0; j < this.mmsdGrid.dataProvider.length; j++) {
          if (this.mmsdGrid.selectedIndices.indexOf(j) != -1) {
            movementSelected.push(this.mmsdGrid.dataProvider[j].movement);
          }
        }
        for (let i = 0; i < this.lastReceivedJSON.movementmatchsummarydisplay.grid.rows.size; i++) {
          if (this.selectedMovements.indexOf(this.mmsdGrid.dataProvider[i].movement) == -1) {
            if (posLevel == "") {
              posLevel = this.mmsdGrid.dataProvider[i].slickgrid_rowcontent.pos.positionlevel;
            } else {
              if (this.mmsdGrid.dataProvider[i].slickgrid_rowcontent.pos.positionlevel !== posLevel) {
                return false;
              }
            }
          }
        }
        return true;
      }
    }
    return false;
  }



  /** This function is used to calculate the selected row */
  calculateRowsSelected() {
    let rowsSelected=0;
    let noOfRow=false;
    for (let selectedListCounter=0; selectedListCounter < this.mmsdGrid.selectedIndices.length; selectedListCounter++) {
      rowsSelected=rowsSelected + 1;
    }
    if (rowsSelected == (this.lastReceivedJSON.movementmatchsummarydisplay.grid.rows.size)) {
      noOfRow=true;
    }
    return noOfRow;
  }
  isActionRun:boolean = false;
  isRefreshRun:boolean = false;
  /**
   * This function is used to Open subscreen if the given match id is valid
   */
  postValidateMatchIdAction(): void {
    switch ( this.subScreenName) {
      case "Notes": {
        ExternalInterface.call("showMatchNotes", "showMatchNotes",  this.removeLeadingZeros( this.matchIdText.text),  this.selectedEntityId.text);
        break;
      }
      case "Log": {
        ExternalInterface.call("matchLog", "matchLog",  this.removeLeadingZeros( this.matchIdText.text),  this.updateDate.text);
        break;
      }
      case "Mvmnt": {
        ExternalInterface.call("showMvmnt", "showMovementDetails",  this.mmsdGrid.dataProvider[this.mmsdGrid.selectedIndex].movement,  this.selectedEntityId.text);
        break;
      }
      case "UnMatch": {
        this.isActionRun=true;
        this.isRefreshRun=true;
        this.unMatchMatchId();
        break;
      }
      case "Suspend": {
        this.isActionRun=true;
        this.isRefreshRun=true;
        this.openSuspendMvmnt();
        break;

      }
      case "Confirm": {
        this.isActionRun=true;
        this.isRefreshRun=true;
        this.openConfirmMvmnt();
        break;
      }
      case "Recon": {
        this.isActionRun=true;
        this.isRefreshRun=true;
        this.openReconcileMvmnt();
        break;
      }

      case "Remove": {
        this.isActionRun=true;
        this.isRefreshRun=true;
        this.openRemoveMvmnt();
        break;
      }
      case "Add": {
        this.openAddMovement();
        break;
      }
      case "Export": {
        this.exportGridData();
        break;
      }
      default:
      // do nothing
    }
  }




  /**
   * This function is used to confirm the  movement when the confirm buuton
   * is clicked.
   */
  openConfirmMvmnt(): void {

    this.suspendFlag = false;
    this.reconcileFlag = false;
    this.removeFlagUnmatch = false;
    this.suspendFlagListener = false;
    this.reconcileFlagLisner=false;
    let rowCount = this.mmsdGrid.dataProvider.length;
    let CheckLockFlag="";
    let movements = "";
    let checkUser = false;
    let parentScreen: string = ExternalInterface.call('eval', 'calledFrom');
    let parentParentScreen: string = ExternalInterface.call('eval', 'parentScreen');
    let loggedInUser: string = ExternalInterface.call('eval', 'loggedInUser');
    for (let i = 0; i < rowCount; i++) {
      movements += this.mmsdGrid.dataProvider[i].movement + ",";
    }
    CheckLockFlag  = ExternalInterface.call("checkLocksOnServer", movements);
    if ((loggedInUser == CheckLockFlag) && ((parentScreen == "mvmntdisplay") && (parentParentScreen == "movementSummaryDisplay"))) {
      checkUser=true;
    }
    // check the match status
    if (!BooleanParser.parseBooleanValue(CheckLockFlag) && !checkUser) {
      this.swtAlert.warning(SwtUtil.getPredictMessage('label.mvmtAreBusy', null) + " " +CheckLockFlag);
    } else {
      let positionLevel=0;
      let highestPosLvl=[];
      let submitFlag=false;
      let PredictFlag=false;
      let confirmFlag=false;
      for (let high=0; high < this.mmsdGrid.dataProvider.length; high++) {
        highestPosLvl[high] = this.mmsdGrid.dataProvider[high].slickgrid_rowcontent.pos.positionlevel;
      }
      /* Flag sets for consecutive positive level should not be shame */
      for (let k=1; k < highestPosLvl.length; k++) {
        if (highestPosLvl[k - 1] != highestPosLvl[k]) {
          confirmFlag=true;
        }
      }
      if (confirmFlag) {
        /* Condition checked for different amount totals across position levels */
        if (this.PosLvlCorrAmts(highestPosLvl)) {
          let title='Microsoft Internet Explorer';
          let testMessageY = 'This match has different amount totals across position levels. Do you wish to continue?';
          this.swtAlert.question(testMessageY, title, Alert.OK | Alert.CANCEL, this, this.alertListener1.bind(this), null);
          this.confirmFlagListener=true;

        } else {
          let entityId: string=this.selectedEntityId.text;
          let lockflag3: string=ExternalInterface.call("lockMatchOnServer", "confirm", this.matchIds, entityId);
          if (!BooleanParser.parseBooleanValue(lockflag3)) {
            this.swtAlert.warning(SwtUtil.getPredictMessage('label.matchIsInUseByanotherProcess', null));
          } else {
            // form a query
            let screenName: string=ExternalInterface.call('eval', 'match');
            /*Modified by SamirL for M2266 to ensure MMSD window close when calledFrom="msd" */
            if (screenName == "manual" && ExternalInterface.call('eval', 'calledFrom') != "msd") {
              this.refreshFlag=true;
              this.actionMethod="method=confirm";
              this.actionMethod=this.actionMethod + "&nextRecord=next";
              // Modified for Mantis 1691 by Marshal on 20-June-2012
              this.actionMethod=this.actionMethod + "&day=" + "&matchCount=" + "&menuAccessId=" + "&manualMatchScreen=frommanualMatchScreen" + "&matchId=" + this.jsonReader.getScreenAttributes()["matchid"] + "&applyCurrencyThreshold=" + ExternalInterface.call('eval', 'applyCurrencyThreshold') + "&noIncludedMovementMatches=" + ExternalInterface.call('eval', 'noIncludedMovementMatches') + "&dateTabIndFlag=" + "&dateTabInd=" + ExternalInterface.call('eval', 'dateTabInd') + "&matchType=" + ExternalInterface.call('eval', 'screen') + "&inputMethod=" + ExternalInterface.call('eval', 'inputMethod') + "&currentIndex=" + "" + (parseInt(this.jsonReader.getScreenAttributes()["matchindex"],10) - 1) + "&accountAccessStatus=" +  this.acctAccessFlag;

              this.changeMatchService.url=this.baseURL + this.actionPath + this.actionMethod;
              this.requestParams["movement.id.entityId"]=this.jsonReader.getScreenAttributes()["entityid"];
              this.requestParams["movement.matchStatus"]= ExternalInterface.call('eval', 'status');
              this.requestParams["movement.currencyCode"]=this.jsonReader.getScreenAttributes()["currencycode"];
              this.requestParams["movementDetail.matchQuality"]=this.jsonReader.getScreenAttributes()["matchquality"];
              this.changeMatchService.cbResult  = (event)  => {
                this.callFlexDisplay();
              };
              this.changeMatchService.send(this.requestParams);
            } else {
              // form a query
              this.actionMethod="method=confirm";
              this.actionMethod=this.actionMethod + "&nextRecord=next";
              // Modified for Mantis 1691 by Marshal on 20-June-2012
              this.actionMethod=this.actionMethod + "&day=" + "&matchCount=" + "&menuAccessId=" + "&matchId=" + this.jsonReader.getScreenAttributes()["matchid"] + "&applyCurrencyThreshold=" + ExternalInterface.call('eval', 'applyCurrencyThreshold') + "&noIncludedMovementMatches=" + ExternalInterface.call('eval', 'noIncludedMovementMatches') + "&dateTabIndFlag=" + "&dateTabInd=" + ExternalInterface.call('eval', 'dateTabInd') + "&matchType=" + ExternalInterface.call('eval', 'screen') + "&inputMethod=" + ExternalInterface.call('eval', 'inputMethod') + "&currentIndex=" + "" + (parseInt(this.jsonReader.getScreenAttributes()["matchindex"],10) - 1) + "&valueDate=" + ExternalInterface.call('eval', 'valueDate') + "&accountAccessStatus=" +  this.acctAccessFlag;
              this.inputData.url=this.baseURL + this.actionPath + this.actionMethod;
              this.requestParams["movement.id.entityId"]=this.jsonReader.getScreenAttributes()["entityid"];
              this.requestParams["movement.matchStatus"]= ExternalInterface.call('eval', 'status');
              this.requestParams["movement.currencyCode"]=this.jsonReader.getScreenAttributes()["currencycode"];
              this.requestParams["movementDetail.matchQuality"]=this.jsonReader.getScreenAttributes()["matchquality"];

              this.inputData.send(this.requestParams);

            }

          }
        }
      } else {

        let confirmWndTitle='Microsoft Internet Explorer';
        let popupTestMessageCnfrm='Only 1 Position level is available. Do you want to Continue ?';
        this.swtAlert.question(popupTestMessageCnfrm, confirmWndTitle, Alert.OK | Alert.CANCEL, this, this.alertListener.bind(this), null);
        this.confirmFlagOk=true;
      }
    }
  }


  /** This function is used to reconcile the  movement when the reconcile buuton
   * is clicked.
   */
  openReconcileMvmnt(): void {
    this.suspendFlag=false;
    this.confirmFlagOk=false;
    this.removeFlagUnmatch=false;
    this.suspendFlagListener=false;
    this.confirmFlagListener=false;
    let rowCount = this.mmsdGrid.dataProvider.length;
    let CheckLockFlag="";
    let movements="";
    let checkUser=false;
    let parentScreen: string=ExternalInterface.call('eval', 'calledFrom');
    let parentParentScreen: string=ExternalInterface.call('eval', 'parentScreen');
    let loggedInUser: string=ExternalInterface.call('eval', 'loggedInUser');
    for (let i=0; i < rowCount; i++) {
      movements+=this.mmsdGrid.dataProvider[i].movement + ",";
    }
    // CheckLockFlag is used to  Gets the match status
    CheckLockFlag=ExternalInterface.call("checkLocksOnServer", movements);

    if ((loggedInUser == CheckLockFlag) && ((parentScreen == "mvmntdisplay") && (parentParentScreen == "movementSummaryDisplay"))) {
      checkUser=true;
    }
    if (!BooleanParser.parseBooleanValue(CheckLockFlag) && !checkUser) {
      this.swtAlert.warning( SwtUtil.getPredictMessage('label.mvmtAreBusy', null) + " " + CheckLockFlag);
    } else {
      // set reconcileFlag as true
      this.reconcileFlag=true;
      let positionLevel=0;
      let highestPosLvl = [];
      let submitFlag=false;
      let PredictFlag=false;
      let confirmFlag=false;
      for (let high=0; high < this.mmsdGrid.dataProvider.length; high++) {
        highestPosLvl[high]=this.mmsdGrid.dataProvider[high].slickgrid_rowcontent.pos.positionlevel
      }

      for (let k=1; k < highestPosLvl.length; k++) {
        if (highestPosLvl[k - 1] != highestPosLvl[k]) {
          confirmFlag=true;
        }

      }
      if (confirmFlag) {
        if (this.PosLvlCorrAmts(highestPosLvl)) {
          let title='Microsoft Internet Explorer';
          let testMessageY='This match has different amount totals across position levels.Do you wish to continue?';
          this.swtAlert.question(testMessageY, title, Alert.OK | Alert.CANCEL, this, this.alertListener1.bind(this), null);
          this.reconcileFlagLisner=true;
        } else {
          let entityId: string=this.selectedEntityId.text;
          let lockflag2: string=ExternalInterface.call("lockMatchOnServer", "reconcile", this.matchIds, entityId);
          if (!BooleanParser.parseBooleanValue(lockflag2)) {
            this.swtAlert.warning(SwtUtil.getPredictMessage('label.matchIsInUseByanotherProcess', null));
          } else {
            let screenName: string=ExternalInterface.call('eval', 'match');
            if (screenName == "manual" && ExternalInterface.call('eval', 'calledFrom') != "msd") {

              this.refreshFlag=true;
              this.actionMethod="method=reconcile";
              this.actionMethod=this.actionMethod + "&nextRecord=next";
              this.actionMethod=this.actionMethod + "&day=" + "&matchCount=" + "&menuAccessId=" + "&matchList=" + ExternalInterface.call('eval', 'matches') + "&manualMatchScreen=frommanualMatchScreen" + "&matchId=" + this.jsonReader.getScreenAttributes()["matchid"] + "&applyCurrencyThreshold=" + ExternalInterface.call('eval', 'applyCurrencyThreshold') + "&noIncludedMovementMatches=" + ExternalInterface.call('eval', 'noIncludedMovementMatches') + "&dateTabIndFlag=" + "&dateTabInd=" + ExternalInterface.call('eval', 'dateTabInd') + "&matchType=" + ExternalInterface.call('eval', 'screen') + "&inputMethod=" + ExternalInterface.call('eval', 'inputMethod') + "&currentIndex=" + "" + (parseInt(this.jsonReader.getScreenAttributes()["matchindex"],10) - 1) + "&accountAccessStatus=" +  this.acctAccessFlag;

              let matchId: string = this.jsonReader.getScreenAttributes()["matchid"];
              this.changeMatchService.url=this.baseURL + this.actionPath + this.actionMethod;
              this.requestParams["movement.id.entityId"]=this.jsonReader.getScreenAttributes()["entityid"];
              this.requestParams["movement.matchStatus"]= ExternalInterface.call('eval', 'status');
              this.requestParams["movement.currencyCode"]=this.jsonReader.getScreenAttributes()["currencycode"];
              this.requestParams["movementDetail.matchQuality"]=this.jsonReader.getScreenAttributes()["matchquality"];

              this.changeMatchService.cbResult  = (event)  => {
                this.callFlexDisplay();
              };
              this.changeMatchService.send(this.requestParams);
            } else {
              this.actionMethod="method=reconcile";
              this.actionMethod=this.actionMethod + "&nextRecord=next";
              this.actionMethod=this.actionMethod + "&day=" + "&matchCount=" + "&menuAccessId=" + "&matchId=" + this.jsonReader.getScreenAttributes()["matchid"] + "&applyCurrencyThreshold=" + ExternalInterface.call('eval', 'applyCurrencyThreshold') + "&noIncludedMovementMatches=" + ExternalInterface.call('eval', 'noIncludedMovementMatches') + "&dateTabIndFlag=" + "&dateTabInd=" + ExternalInterface.call('eval', 'dateTabInd') + "&matchType=" + ExternalInterface.call('eval', 'screen') + "&inputMethod=" + ExternalInterface.call('eval', 'inputMethod') + "&currentIndex=" + "" + (parseInt(this.jsonReader.getScreenAttributes()["matchindex"],10) - 1) + "&valueDate=" + ExternalInterface.call('eval', 'valueDate') + "&accountAccessStatus=" +  this.acctAccessFlag;
              this.inputData.url= this.baseURL + this.actionPath + this.actionMethod;
              this.requestParams["movement.id.entityId"]=this.jsonReader.getScreenAttributes()["entityid"];
              this.requestParams["movement.matchStatus"]= ExternalInterface.call('eval', 'status');
              this.requestParams["movement.currencyCode"]=this.jsonReader.getScreenAttributes()["currencycode"];
              this.requestParams["movementDetail.matchQuality"]=this.jsonReader.getScreenAttributes()["matchquality"];

              this.inputData.send(this.requestParams);

            }

          }
        }
      } else {
        // set the window title name
        let wndTitleForRecon='Microsoft Internet Explorer';
        // set the message for popup
        let popupTestMessageRecon='Only 1 Position level is available. Do you want to Continue ?';
        this.swtAlert.question(popupTestMessageRecon, wndTitleForRecon, Alert.OK | Alert.CANCEL, this, this.alertListener.bind(this), null);
        this.reconcileFlag = true;
      }
    }
  }




  /** This function is used for getting the amounts based on postion level */

  PosLvlCorrAmts(highestPosLvl): boolean {
    let prevPosLvl="";
    let currPosLvl="";
    let amtTotalstring="";
    let amtFlag=false;
    let assignFlag=false;
    let amtTotals=[];
    for (let high01=0; high01 < highestPosLvl.length; high01++) {
      currPosLvl=highestPosLvl[high01];
      if (currPosLvl != prevPosLvl) {
        amtTotalstring=amtTotalstring + this.getPosLvlTotalAmount(highestPosLvl[high01]).toString() + ",";
      }

      prevPosLvl=currPosLvl;
    }
    amtTotalstring=amtTotalstring.substring(0, amtTotalstring.length - 1);
    amtTotals=amtTotalstring.split(",");
    /* Flag sets for consecutive amount should not be same */
    for (let j=1; j < amtTotals.length; j++) {
      if(this.suppMatchDiffWarning=="N"){
      if (parseFloat(Number(amtTotals[j - 1]).toFixed(2))!= parseFloat(Number(amtTotals[j]).toFixed(2))) {
        amtFlag=true;
        break;

      }
    }else{
        if (parseFloat((Math.abs(Number(amtTotals[j - 1]) - Number(amtTotals[j]))).toFixed(2)) > parseFloat((Number(this.ccyTol).toFixed(2)))) {
        amtFlag=true;
        break;
      }
    }
    }
    assignFlag=amtFlag;

    return assignFlag;
  }

  /** This function is used for adding the amount based on postion level */
  getPosLvlTotalAmount(posLvl: string): number {

    let amountPosLvlTotal=0;
    for (let j=0; j < this.mmsdGrid.dataProvider.length; j++) {
      let tempPosLvl: string=this.mmsdGrid.dataProvider[j].slickgrid_rowcontent.pos.positionlevel;
      if (tempPosLvl == posLvl) {
        let amount: number=  Number(this.getAmtValue(this.mmsdGrid.dataProvider[j].amount));
        let sign: string=this.mmsdGrid.dataProvider[j].sign;
        if (sign == 'C') {
          amountPosLvlTotal+=amount;
        } else if (sign == 'D') {
          amountPosLvlTotal-=amount;
        }
      }
    }
    return amountPosLvlTotal;
  }

  /** This function is used to get the amount without . and , */
  getAmtValue(amt: string): string {

    let amtvalue='';
    let el='';
    for (let idx=0; idx < amt.length; idx++) {
      el=amt.charAt(idx);
      if (this.currencyPattern == 'currencyPat1') {
        if (el != ',')
          amtvalue += el;
      } else {
        if (el != '.') {
          amtvalue += el;
        }
      }

    }
    return amtvalue.replace(/,/g, '.');
  }


  export(event): void {
    this.subScreenName="Export";
    this.reportType = event.toString();
    this.validateMatchId();

  }

  /**
   *  This Function  is used to open the export based on the reportType when data export button is clicked
   */
  exportGridData(): void {
    let selects=[];
    // let filter = new Object();
    let selectedMovementsIndices= this.mmsdGrid.selectedIndices;
    selects.push("Entity=" + this.selectedEntityId.text);
    selects.push("Match Id=" + this.removeLeadingZeros(this.matchIdText.text));
    selects.push("Currency Code=" + this.currencyCode.text);
    selects.push("Match Status=" + this.matchStatus.text);
    selects.push("Match Quality=" + this.matchQuality.text);
    selects.push("Update date=" + this.updateDate.text);
    selects.push("Update user=" + this.updateUser.text);
    selects.push("No. of Internal Position level=" + this.posIntlvl.text);
    selects.push("No. of External Position level=" + this.posExtlvl.text);
    let str="<data>";
    let totals="";
    let columnsHeaderList =(this.lastReceivedJSON.movementmatchsummarydisplay.grid.metadata.columns.column);
    let rowData="<rows>";
    for (let valueCount=0; valueCount < this.mmsdGrid.dataProvider.length; valueCount++) {
      rowData+="<row>";
      // this.mmsdGrid.selectedIndex = valueCount;
      for (let headerCount=0; headerCount < columnsHeaderList.length; headerCount++) {
        let columnName: string = columnsHeaderList[headerCount].dataelement;
        if (this.mmsdGrid.dataProvider[valueCount][columnName.toLowerCase()] == null || this.mmsdGrid.dataProvider[valueCount][columnName.toLowerCase()] == "") {
          rowData+="<" + columnName.toLowerCase() + ">" + " " + "</" + columnName.toLowerCase() + ">";
        } else {
          rowData+="<" + columnName.toLowerCase() + ">" + this.mmsdGrid.dataProvider[valueCount][columnName.toLowerCase()] + "</" + columnName.toLowerCase() + ">";
        }
      }
      rowData+="</row>";
    }
    rowData+="</rows>";
    // str+=CommonLogic.removeLineBreaks(columnMetaData);
    // str+=CommonLogic.removeLineBreaks(rowData);
    totals="<total>" + totals + "</total>";
    str+=totals;
    let filters="<filters>";
    if (selects != null) {
      for (let k=0; k < selects.length; k++) {
        filters+="<filter id=\"" + selects[k].split("=")[0] + "\">" + selects[k].split("=")[1] + "</filter>";
      }
    }
    // filter the vlaues based on the selection
    if (this.mmsdGrid.isFiltered) {
      for (let i=0; i < this.mmsdGrid.getFilterColumns().length; i++) {
        let filterVal=this.mmsdGrid.getFilterColumns()[i];
        for (let j = 0; j < filterVal.length; j++) {
          filters+="<filter id=\"Column Filter " + j + "\">" + filterVal[j] + "</filter>";
        }
      }
    }
    filters+="</filters>";
    str+=filters;
    str+="</data>";
    let strArr=str.split("&");
    for (let strArrCounter=0; strArrCounter < strArr.length; strArrCounter++) {
      if (strArrCounter == 0) {
        str=strArr[strArrCounter];
      } else {
        str+="&amp;" + strArr[strArrCounter];
      }
    }

    // checks the length of selectedMovementsIndices
    if (selectedMovementsIndices.length <= 0) {
      this.mmsdGrid.selectedIndices=[];
    } else {
      this.mmsdGrid.selectedIndices=selectedMovementsIndices;
    }
    this.exportContainer.convertData(this.lastReceivedJSON.movementmatchsummarydisplay.grid.metadata.columns, this.mmsdGrid, null, selects, this.reportType , false );
    ExternalInterface.call('eval', 'document.getElementById(\'exportDataForm\').action=\'flexdataexport.do?method=' + this.reportType + '\';');
    ExternalInterface.call('eval', 'document.getElementById(\'exportData\').value=\'' + str + '\';');
    ExternalInterface.call('eval', 'document.getElementById(\'exportDataForm\').submit();');
  }


  /** This function is used to validate  the movement id when the Suspend buuton is clicked.
   */
  suspendMvmnt(): void {
    this.suspendButton.enabled = false;
    this.subScreenName="Suspend";
    this.validateMatchId();
  }

  /* start: code added by Mansour for mantis 1743 related to mantis 1561 */
  checkStatus(rowCount: number): string {
    let entityId: string= this.selectedEntityId.text;
    let maxValue=0;
    let count=1;
    let minValue=0;
    // let status: string=ExternalInterface.call('eval', 'status');
    // let archiveId: string=ExternalInterface.call('eval', 'archiveId');
    let matchStatus: string=this.matchStatus.text;
    let selectedListMovements = "";
    let checkstatusFlag = "";
    do {
      if (rowCount - minValue > 100) {
        maxValue=minValue + 100;
      } else {
        maxValue=rowCount;
      }
      for (let i: number = minValue; ((i < maxValue) && (i < this.mmsdGrid.dataProvider.length)); i++) {
        selectedListMovements+=this.mmsdGrid.dataProvider[i].movement + ",";
      }
      checkstatusFlag= ExternalInterface.call("checkStatus", this.matchIds, entityId, matchStatus, selectedListMovements, rowCount, count, maxValue, this.matchHash);
      minValue=maxValue;
      count++;
      selectedListMovements="";
    } while (maxValue < rowCount);

    return checkstatusFlag;
  }
  /**
   * This Function is used to get matchid and displays the corresponding movements related to matchid
   * and apply them to the url member of the HTTPComms object
   */
  validateMatchId(): void {
    let checkStatusResult: string = null;
    if(this.serverBusyFlag) {
      this.swtAlert.warning(SwtUtil.getPredictMessage('label.resourceBusy', null));
      return;
    }

    let rowCount: number = this.mmsdGrid.dataProvider.length;
    checkStatusResult  = this.checkStatus(rowCount);

    if (checkStatusResult == "no_match" ) {
      this.swtAlert.error(SwtUtil.getPredictMessage('match.doesnotexist', null));
      return;
    }

    if (!BooleanParser.parseBooleanValue(checkStatusResult)) {
      this.swtAlert.warning(SwtUtil.getPredictMessage('label.matchHasBeenChanged', null));
      return;
    }

    if (this.inScreenMatchId == "" ||(this.inScreenMatchId == this.removeLeadingZeros(this.matchIdText.text))) {
      this.postValidateMatchIdAction();
    } else {
      this.swtAlert.warning(SwtUtil.getPredictMessage('label.matchIDFiledAmended', null),null ,0, this, this.alertCloseHandler);
    }
  }


  /** This function calls when the 'OK' button is clicked on the Alert box.
   * is clicked.
   */
  alertListener(eventObj): void {
    // let status: string=ExternalInterface.call('eval', 'status');
    this.matchIds=this.removeLeadingZeros(this.matchIdText.text);
    let entityId: string=this.selectedEntityId.text;
    // let matchStatus: string=this.matchStatus.text;
    let selectedList="";
    for (let i=0; i < this.mmsdGrid.dataProvider.length; i++) {
      selectedList+=this.mmsdGrid.dataProvider[i].movement + ",";
    }
    let allowMatch=false;
    if (eventObj.detail == Alert.OK) {
      allowMatch=true;
    }
    if (allowMatch) {
      if (this.removeFlagUnmatch) {
        let lockflag0: string=ExternalInterface.call("lockMatchOnServer", "remove", this.matchIds, entityId);
        if (!BooleanParser.parseBooleanValue(lockflag0)) {
          this.swtAlert.warning(SwtUtil.getPredictMessage('label.matchIsInUseByanotherProcess', null));
        } else {

          let screenName: string=ExternalInterface.call('eval', 'match');
          if (screenName == "manual") {
            this.actionMethod="method=unmatch";
            this.actionMethod=this.actionMethod + "&day=" + "&matchCount=" + "&menuAccessId=" + "&matchId=" + this.matchIds + "&manualMatchScreen=frommanualMatchScreen" + "&applyCurrencyThreshold=" + ExternalInterface.call('eval', 'applyCurrencyThreshold') + "&noIncludedMovementMatches=" + ExternalInterface.call('eval', 'noIncludedMovementMatches') + "&dateTabIndFlag=" + "&dateTabInd=" + ExternalInterface.call('eval', 'dateTabInd') + "&matchType=" + ExternalInterface.call('eval', 'screen') + "&inputMethod=" + ExternalInterface.call('eval', 'inputMethod') + "&currentIndex=" + "" + (parseInt(this.jsonReader.getScreenAttributes()["matchindex"],10) - 1) + "&accountAccessStatus=" +  this.acctAccessFlag;

            this.inputData.url=this.baseURL + this.actionPath + this.actionMethod;
            this.requestParams["movement.id.entityId"]=this.jsonReader.getScreenAttributes()["entityid"];
            this.requestParams["movement.matchId"]=this.jsonReader.getScreenAttributes()["matchid"];
            this.requestParams["movement.matchStatus"]= ExternalInterface.call('eval', 'status');
            this.requestParams["movement.currencyCode"]=this.jsonReader.getScreenAttributes()["currencycode"];
            this.requestParams["movementDetail.matchQuality"]=this.jsonReader.getScreenAttributes()["matchquality"];

            this.inputData.send(this.requestParams);

          } else {

            this.actionMethod="method=unmatch";
            this.actionMethod=this.actionMethod + "&day=" + "&matchCount=" + "&menuAccessId=" + "&matchId=" + this.matchIds + "&applyCurrencyThreshold=" + ExternalInterface.call('eval', 'applyCurrencyThreshold') + "&noIncludedMovementMatches=" + ExternalInterface.call('eval', 'noIncludedMovementMatches') + "&dateTabIndFlag=" + "&dateTabInd=" + ExternalInterface.call('eval', 'dateTabInd') + "&matchType=" + ExternalInterface.call('eval', 'screen') + "&inputMethod=" + ExternalInterface.call('eval', 'inputMethod') + "&currentIndex=" + "" + (parseInt(this.jsonReader.getScreenAttributes()["matchindex"],10) - 1) + "&valueDate=" + ExternalInterface.call('eval', 'valueDate') + "&accountAccessStatus=" +  this.acctAccessFlag;
            this.inputData.url=this.baseURL + this.actionPath + this.actionMethod;
            this.requestParams["movement.id.entityId"]=this.jsonReader.getScreenAttributes()["entityid"];
            this.requestParams["movement.matchId"]=this.jsonReader.getScreenAttributes()["matchid"];
            this.requestParams["movement.matchStatus"]= ExternalInterface.call('eval', 'status');
            this.requestParams["movement.currencyCode"]=this.jsonReader.getScreenAttributes()["currencycode"];
            this.requestParams["movementDetail.matchQuality"]=this.jsonReader.getScreenAttributes()["matchquality"];
            this.inputData.send(this.requestParams);

          }

        }

      }

      if (this.suspendFlag) {
        let lockflag1: string=ExternalInterface.call("lockMatchOnServer", "suspend", this.matchIds, entityId);
        if (!BooleanParser.parseBooleanValue(lockflag1)) {
          this.swtAlert.warning(SwtUtil.getPredictMessage('label.matchIsInUseByanotherProcess', null));
        } else {
          let screenNameSuspend: string=ExternalInterface.call('eval', 'match');
          if (screenNameSuspend == "manual") {
            this.refreshFlag=true;
            this.actionMethod="method=suspend";
            this.actionMethod=this.actionMethod + "&nextRecord=next";
            this.actionMethod=this.actionMethod + "&day=" + "&matchCount=" + "&menuAccessId=" + "&manualMatchScreen=frommanualMatchScreen" + "&matchId=" + this.jsonReader.getScreenAttributes()["matchid"] + "&applyCurrencyThreshold=" + ExternalInterface.call('eval', 'applyCurrencyThreshold') + "&noIncludedMovementMatches=" + ExternalInterface.call('eval', 'noIncludedMovementMatches') + "&dateTabIndFlag=" + "&dateTabInd=" + ExternalInterface.call('eval', 'dateTabInd') + "&matchType=" + ExternalInterface.call('eval', 'screen') + "&inputMethod=" + ExternalInterface.call('eval', 'inputMethod') + "&currentIndex=" + "" + (parseInt(this.jsonReader.getScreenAttributes()["matchindex"],10) - 1) + "&accountAccessStatus=" +  this.acctAccessFlag;

            this.inputData.url=this.baseURL + this.actionPath + this.actionMethod;
            this.requestParams["movement.id.entityId"]=this.jsonReader.getScreenAttributes()["entityid"];
            this.requestParams["movement.matchStatus"]= ExternalInterface.call('eval', 'status');
            this.requestParams["movement.currencyCode"]=this.jsonReader.getScreenAttributes()["currencycode"];
            this.requestParams["movementDetail.matchQuality"]=this.jsonReader.getScreenAttributes()["matchquality"];

            this.inputData.send(this.requestParams);
          } else {
            this.actionMethod="method=suspend";
            this.actionMethod=this.actionMethod + "&nextRecord=next";
            // Modified for Mantis 1691 by Marshal on 20-June-2012
            this.actionMethod=this.actionMethod + "&day=" + "&matchCount=" + "&menuAccessId=" + "&matchId=" + this.jsonReader.getScreenAttributes()["matchid"] + "&applyCurrencyThreshold=" + ExternalInterface.call('eval', 'applyCurrencyThreshold') + "&noIncludedMovementMatches=" + ExternalInterface.call('eval', 'noIncludedMovementMatches') + "&dateTabIndFlag=" + "&dateTabInd=" + ExternalInterface.call('eval', 'dateTabInd') + "&matchType=" + ExternalInterface.call('eval', 'screen') + "&inputMethod=" + ExternalInterface.call('eval', 'inputMethod') + "&currentIndex=" + "" + (parseInt(this.jsonReader.getScreenAttributes()["matchindex"],10) - 1) + "&valueDate=" + ExternalInterface.call('eval', 'valueDate') + "&accountAccessStatus=" +  this.acctAccessFlag;
            this.inputData.url=this.baseURL + this.actionPath + this.actionMethod;
            this.requestParams["movement.id.entityId"]=this.jsonReader.getScreenAttributes()["entityid"];
            this.requestParams["movement.matchStatus"]= ExternalInterface.call('eval', 'status');
            this.requestParams["movement.currencyCode"]=this.jsonReader.getScreenAttributes()["currencycode"];
            this.requestParams["movementDetail.matchQuality"]=this.jsonReader.getScreenAttributes()["matchquality"];

            this.inputData.send(this.requestParams);
          }

        }
      }
      /* Condition checked for confirm match */
      if (this.confirmFlagOk) {
        let lockflag3: string=ExternalInterface.call("lockMatchOnServer", "confirm", this.matchIds, entityId);
        if (!BooleanParser.parseBooleanValue(lockflag3)) {
          this.swtAlert.warning(  SwtUtil.getPredictMessage('label.matchIsInUseByanotherProcess', null));
        } else {
          let screenNameConfirm: string=ExternalInterface.call('eval', 'match');
          if (screenNameConfirm == "manual") {
            this.refreshFlag=true;
            this.actionMethod="method=confirm";
            this.actionMethod=this.actionMethod + "&nextRecord=next";
            this.actionMethod=this.actionMethod + "&day=" + "&matchCount=" + "&menuAccessId=" + "&manualMatchScreen=frommanualMatchScreen" + "&matchId=" + this.jsonReader.getScreenAttributes()["matchid"] + "&applyCurrencyThreshold=" + ExternalInterface.call('eval', 'applyCurrencyThreshold') + "&noIncludedMovementMatches=" + ExternalInterface.call('eval', 'noIncludedMovementMatches') + "&dateTabIndFlag=" + "&dateTabInd=" + ExternalInterface.call('eval', 'dateTabInd') + "&matchType=" + ExternalInterface.call('eval', 'screen') + "&inputMethod=" + ExternalInterface.call('eval', 'inputMethod') + "&currentIndex=" + "" + (parseInt(this.jsonReader.getScreenAttributes()["matchindex"],10) - 1) + "&accountAccessStatus=" +  this.acctAccessFlag;

            this.inputData.url=this.baseURL + this.actionPath + this.actionMethod;
            this.requestParams["movement.id.entityId"]=this.jsonReader.getScreenAttributes()["entityid"];
            this.requestParams["movement.matchStatus"]= ExternalInterface.call('eval', 'status');
            this.requestParams["movement.currencyCode"]=this.jsonReader.getScreenAttributes()["currencycode"];
            this.requestParams["movementDetail.matchQuality"]=this.jsonReader.getScreenAttributes()["matchquality"];

            this.inputData.send(this.requestParams);
          } else {
            this.actionMethod="method=confirm";
            this.actionMethod=this.actionMethod + "&nextRecord=next";
            // Modified for Mantis 1691 by Marshal on 20-June-2012
            this.actionMethod=this.actionMethod + "&day=" + "&matchCount=" + "&menuAccessId=" + "&matchId=" + this.jsonReader.getScreenAttributes()["matchid"] + "&applyCurrencyThreshold=" + ExternalInterface.call('eval', 'applyCurrencyThreshold') + "&noIncludedMovementMatches=" + ExternalInterface.call('eval', 'noIncludedMovementMatches') + "&dateTabIndFlag=" + "&dateTabInd=" + ExternalInterface.call('eval', 'dateTabInd') + "&matchType=" + ExternalInterface.call('eval', 'screen') + "&inputMethod=" + ExternalInterface.call('eval', 'inputMethod') + "&currentIndex=" + "" + (parseInt(this.jsonReader.getScreenAttributes()["matchindex"],10) - 1) + "&valueDate=" + ExternalInterface.call('eval', 'valueDate') + "&accountAccessStatus=" +  this.acctAccessFlag;
            this.inputData.url=this.baseURL + this.actionPath + this.actionMethod;
            this.requestParams["movement.id.entityId"]=this.jsonReader.getScreenAttributes()["entityid"];
            this.requestParams["movement.matchStatus"]= ExternalInterface.call('eval', 'status');
            this.requestParams["movement.currencyCode"]=this.jsonReader.getScreenAttributes()["currencycode"];
            this.requestParams["movementDetail.matchQuality"]=this.jsonReader.getScreenAttributes()["matchquality"];

            this.inputData.send(this.requestParams);

          }

        }
      }
      /* Condition checked for reconcile match */
      if (this.reconcileFlag) {
        let lockflag2: string=ExternalInterface.call("lockMatchOnServer", "reconcile", this.matchIds, entityId);
        if (!BooleanParser.parseBooleanValue(lockflag2)) {
          this.swtAlert.warning(SwtUtil.getPredictMessage('label.matchIsInUseByanotherProcess', null));
        } else {
          let screenNameRecon: string=ExternalInterface.call('eval', 'match');
          if (screenNameRecon == "manual") {
            this.refreshFlag=true;
            this.actionMethod="method=reconcile";
            this.actionMethod=this.actionMethod + "&nextRecord=next";
            this.actionMethod=this.actionMethod + "&day=" + "&matchCount=" + "&menuAccessId=" + "&matchList=" + ExternalInterface.call('eval', 'matches') + "&manualMatchScreen=frommanualMatchScreen" + "&matchId=" + this.jsonReader.getScreenAttributes()["matchid"] + "&applyCurrencyThreshold=" + ExternalInterface.call('eval', 'applyCurrencyThreshold') + "&noIncludedMovementMatches=" + ExternalInterface.call('eval', 'noIncludedMovementMatches') + "&dateTabIndFlag=" + "&dateTabInd=" + ExternalInterface.call('eval', 'dateTabInd') + "&matchType=" + ExternalInterface.call('eval', 'screen') + "&inputMethod=" + ExternalInterface.call('eval', 'inputMethod') + "&currentIndex=" + "" + (parseInt(this.jsonReader.getScreenAttributes()["matchindex"],10) - 1) + "&accountAccessStatus=" +  this.acctAccessFlag;

            this.inputData.url=this.baseURL + this.actionPath + this.actionMethod;
            this.requestParams["movement.id.entityId"]=this.jsonReader.getScreenAttributes()["entityid"];
            this.requestParams["movement.matchStatus"]= ExternalInterface.call('eval', 'status');
            this.requestParams["movement.currencyCode"]=this.jsonReader.getScreenAttributes()["currencycode"];
            this.requestParams["movementDetail.matchQuality"]=this.jsonReader.getScreenAttributes()["matchquality"];

            this.inputData.send(this.requestParams);
          } else {
            this.actionMethod="method=reconcile";
            this.actionMethod=this.actionMethod + "&nextRecord=next";
            // Modified for Mantis 1691 by Marshal on 20-June-2012
            this.actionMethod=this.actionMethod + "&day=" + "&matchCount=" + "&menuAccessId=" + "&matchId=" + this.jsonReader.getScreenAttributes()["matchid"] + "&applyCurrencyThreshold=" + ExternalInterface.call('eval', 'applyCurrencyThreshold') + "&noIncludedMovementMatches=" + ExternalInterface.call('eval', 'noIncludedMovementMatches') + "&dateTabIndFlag=" + "&dateTabInd=" + ExternalInterface.call('eval', 'dateTabInd') + "&matchType=" + ExternalInterface.call('eval', 'screen') + "&inputMethod=" + ExternalInterface.call('eval', 'inputMethod') + "&currentIndex=" + "" + (parseInt(this.jsonReader.getScreenAttributes()["matchindex"],10) - 1) + "&valueDate=" + ExternalInterface.call('eval', 'valueDate') + "&accountAccessStatus=" +  this.acctAccessFlag;
            this.inputData.url=this.baseURL + this.actionPath + this.actionMethod;
            this.requestParams["movement.id.entityId"]=this.jsonReader.getScreenAttributes()["entityid"];
            this.requestParams["movement.matchStatus"]= ExternalInterface.call('eval', 'status');
            this.requestParams["movement.currencyCode"]=this.jsonReader.getScreenAttributes()["currencycode"];
            this.requestParams["movementDetail.matchQuality"]=this.jsonReader.getScreenAttributes()["matchquality"];

            this.inputData.send(this.requestParams);

          }

        }
      }
    } else {
      this.alertCancelButtonHandler(this.suspendFlag,this.confirmFlagOk);
    }
  }




  /*Start:code modified by chinniah for Issue 1054_SEL_263*/
  alertCancelButtonHandler(suspendFlag: boolean,confirmFlag: boolean) {
    if(confirmFlag) {
      if (this.buttonsContainer.contains(this.confirmButton)) {
        this.confirmButton.enabled = true;
      }
    }
    if (suspendFlag) {
      if (this.buttonsContainer.contains(this.suspendButton)) {
        this.suspendButton.enabled = true;
      }
    }
  }

  /** This function calls when the 'OK' button is clicked on the Alert box.
   * is clicked.
   */
  alertListener1(eventObj): void {
    let allowMatch=false;
    if (eventObj.detail == Alert.OK) {
      allowMatch=true;
    }
    if (allowMatch) {
      let highestPosLvl=[];
      let entityId: string=this.selectedEntityId.text;
      this.matchIds=this.removeLeadingZeros(this.matchIdText.text);
      let screenName: string=ExternalInterface.call('eval', 'match');
      for (let high=0; high < this.mmsdGrid.dataProvider.length; high++) {
        highestPosLvl[high]=this.mmsdGrid.dataProvider[high].slickgrid_rowcontent.pos.positionlevel;
      }

      if (this.suspendFlagListener) {
        if (this.PosLvlCorrPreductStatus(highestPosLvl)) {
          let windowTitle='Microsoft Internet Explorer';
          let suspendTestMessage='There are included items at multiple position levels. Do you wish to continue?';
          this.swtAlert.question(suspendTestMessage, windowTitle, Alert.OK | Alert.CANCEL, this, this.alertListener.bind(this), null);
          this.suspendFlag=true;

        } else {
          let lockflag1: string=ExternalInterface.call("lockMatchOnServer", "suspend", this.matchIds, entityId);
          if (!BooleanParser.parseBooleanValue(lockflag1)) {
            this.swtAlert.warning(SwtUtil.getPredictMessage('label.matchIsInUseByanotherProcess', null));
          } else {
            if (screenName == "manual") {
              this.refreshFlag=true;
              this.actionMethod="method=suspend";
              this.actionMethod=this.actionMethod + "&nextRecord=next";
              // Modified for Mantis 1691 by Marshal on 20-June-2012
              this.actionMethod=this.actionMethod + "&day=" + "&matchCount=" + "&menuAccessId=" + "&manualMatchScreen=frommanualMatchScreen" + "&matchId=" + this.jsonReader.getScreenAttributes()["matchid"] + "&applyCurrencyThreshold=" + ExternalInterface.call('eval', 'applyCurrencyThreshold') + "&noIncludedMovementMatches=" + ExternalInterface.call('eval', 'noIncludedMovementMatches') + "&dateTabIndFlag=" + "&dateTabInd=" + ExternalInterface.call('eval', 'dateTabInd') + "&matchType=" + ExternalInterface.call('eval', 'screen') + "&inputMethod=" + ExternalInterface.call('eval', 'inputMethod') + "&currentIndex=" + "" + (parseInt(this.jsonReader.getScreenAttributes()["matchindex"],10) - 1) + "&accountAccessStatus=" +  this.acctAccessFlag;

              this.inputData.url=this.baseURL + this.actionPath + this.actionMethod;
              this.requestParams["movement.id.entityId"]=this.jsonReader.getScreenAttributes()["entityid"];
              this.requestParams["movement.matchStatus"]= ExternalInterface.call('eval', 'status');
              this.requestParams["movement.currencyCode"]=this.jsonReader.getScreenAttributes()["currencycode"];
              this.requestParams["movementDetail.matchQuality"]=this.jsonReader.getScreenAttributes()["matchquality"];

              this.inputData.send(this.requestParams);
            } else {
              this.actionMethod="method=suspend";
              this.actionMethod=this.actionMethod + "&nextRecord=next";
              // Modified for Mantis 1691 by Marshal on 20-June-2012
              this.actionMethod=this.actionMethod + "&day=" + "&matchCount=" + "&menuAccessId=" + "&matchId=" + this.jsonReader.getScreenAttributes()["matchid"] + "&applyCurrencyThreshold=" + ExternalInterface.call('eval', 'applyCurrencyThreshold') + "&noIncludedMovementMatches=" + ExternalInterface.call('eval', 'noIncludedMovementMatches') + "&dateTabIndFlag=" + "&dateTabInd=" + ExternalInterface.call('eval', 'dateTabInd') + "&matchType=" + ExternalInterface.call('eval', 'screen') + "&inputMethod=" + ExternalInterface.call('eval', 'inputMethod') + "&currentIndex=" + "" + (parseInt(this.jsonReader.getScreenAttributes()["matchindex"],10) - 1) + "&valueDate=" + ExternalInterface.call('eval', 'valueDate') + "&accountAccessStatus=" +  this.acctAccessFlag;
              this.inputData.url=this.baseURL + this.actionPath + this.actionMethod;

              this.requestParams["movement.id.entityId"]=this.jsonReader.getScreenAttributes()["entityid"];
              this.requestParams["movement.matchStatus"]= ExternalInterface.call('eval', 'status');
              this.requestParams["movement.currencyCode"]=this.jsonReader.getScreenAttributes()["currencycode"];
              this.requestParams["movementDetail.matchQuality"]=this.jsonReader.getScreenAttributes()["matchquality"];

              this.inputData.send(this.requestParams);
            }

          }
        }
      }
      if (this.confirmFlagListener) {
        /* Condition checked for included items at multiple position levels */
        if (this.PosLvlCorrPreductStatus(highestPosLvl)) {
          let confirmWndTitle='Microsoft Internet Explorer';
          let popupTestMessageCnfrm='There are included items at multiple position levels. Do you wish to continue?';
          this.swtAlert.question(popupTestMessageCnfrm, confirmWndTitle, Alert.OK | Alert.CANCEL, this, this.alertListener.bind(this), null);
          this.confirmFlagOk=true;

        } else {
          let lockflag3: string=ExternalInterface.call("lockMatchOnServer", "confirm", this.matchIds, entityId);
          if (!BooleanParser.parseBooleanValue(lockflag3)) {
            this.swtAlert.warning(  SwtUtil.getPredictMessage('label.matchIsInUseByanotherProcess', null));
          } else {
            if (screenName == "manual") {
              this.refreshFlag=true;
              this.actionMethod="method=confirm";
              this.actionMethod=this.actionMethod + "&nextRecord=next";
              this.actionMethod=this.actionMethod + "&day=" + "&matchCount=" + "&menuAccessId=" + "&manualMatchScreen=frommanualMatchScreen" + "&matchId=" + this.jsonReader.getScreenAttributes()["matchid"] + "&applyCurrencyThreshold=" + ExternalInterface.call('eval', 'applyCurrencyThreshold') + "&noIncludedMovementMatches=" + ExternalInterface.call('eval', 'noIncludedMovementMatches') + "&dateTabIndFlag=" + "&dateTabInd=" + ExternalInterface.call('eval', 'dateTabInd') + "&matchType=" + ExternalInterface.call('eval', 'screen') + "&inputMethod=" + ExternalInterface.call('eval', 'inputMethod') + "&currentIndex=" + "" + (parseInt(this.jsonReader.getScreenAttributes()["matchindex"],10) - 1) + "&accountAccessStatus=" +  this.acctAccessFlag;

              this.inputData.url=this.baseURL + this.actionPath + this.actionMethod;
              this.requestParams["movement.id.entityId"]=this.jsonReader.getScreenAttributes()["entityid"];
              this.requestParams["movement.matchStatus"]= ExternalInterface.call('eval', 'status');
              this.requestParams["movement.currencyCode"]=this.jsonReader.getScreenAttributes()["currencycode"];
              this.requestParams["movementDetail.matchQuality"]=this.jsonReader.getScreenAttributes()["matchquality"];

              this.inputData.send(this.requestParams);
            } else {
              this.actionMethod="method=confirm";
              this.actionMethod=this.actionMethod + "&nextRecord=next";
              // Modified for Mantis 1691 by Marshal on 20-June-2012
              this.actionMethod=this.actionMethod + "&day=" + "&matchCount=" + "&menuAccessId=" + "&matchId=" + this.jsonReader.getScreenAttributes()["matchid"] + "&applyCurrencyThreshold=" + ExternalInterface.call('eval', 'applyCurrencyThreshold') + "&noIncludedMovementMatches=" + ExternalInterface.call('eval', 'noIncludedMovementMatches') + "&dateTabIndFlag=" + "&dateTabInd=" + ExternalInterface.call('eval', 'dateTabInd') + "&matchType=" + ExternalInterface.call('eval', 'screen') + "&inputMethod=" + ExternalInterface.call('eval', 'inputMethod') + "&currentIndex=" + "" + (parseInt(this.jsonReader.getScreenAttributes()["matchindex"],10) - 1) + "&valueDate=" + ExternalInterface.call('eval', 'valueDate') + "&accountAccessStatus=" +  this.acctAccessFlag;
              this.inputData.url=this.baseURL + this.actionPath + this.actionMethod;
              this.requestParams["movement.id.entityId"]=this.jsonReader.getScreenAttributes()["entityid"];
              this.requestParams["movement.matchStatus"]= ExternalInterface.call('eval', 'status');
              this.requestParams["movement.currencyCode"]=this.jsonReader.getScreenAttributes()["currencycode"];
              this.requestParams["movementDetail.matchQuality"]=this.jsonReader.getScreenAttributes()["matchquality"];

              this.inputData.send(this.requestParams);

            }

          }
        }
      }
      if (this.reconcileFlagLisner) {
        /* Condition checked for included items at multiple position levels */
        if (this.PosLvlCorrPreductStatus(highestPosLvl)) {

          let titleMsg='Microsoft Internet Explorer';
          let testMsgY='There are included items at multiple position levels. Do you wish to continue?';
          this.swtAlert.question(testMsgY, titleMsg, Alert.OK | Alert.CANCEL, this, this.alertListener.bind(this), null);
          this.reconcileFlag=true;

        } else {

          let lockflag2: string=ExternalInterface.call("lockMatchOnServer", "reconcile", this.matchIds, entityId);
          if (!BooleanParser.parseBooleanValue(lockflag2)) {
            this.swtAlert.warning(SwtUtil.getPredictMessage('label.matchIsInUseByanotherProcess', null));
          } else {
            if (screenName == "manual") {
              this.refreshFlag=true;
              this.actionMethod="method=reconcile";
              this.actionMethod=this.actionMethod + "&nextRecord=next";
              // Modified for Mantis 1691 by Marshal on 20-June-2012
              this.actionMethod=this.actionMethod + "&day=" + "&matchCount=" + "&menuAccessId=" + "&matchList=" + ExternalInterface.call('eval', 'matches') + "&manualMatchScreen=frommanualMatchScreen" + "&matchId=" + this.jsonReader.getScreenAttributes()["matchid"] + "&applyCurrencyThreshold=" + ExternalInterface.call('eval', 'applyCurrencyThreshold') + "&noIncludedMovementMatches=" + ExternalInterface.call('eval', 'noIncludedMovementMatches') + "&dateTabIndFlag=" + "&dateTabInd=" + ExternalInterface.call('eval', 'dateTabInd') + "&matchType=" + ExternalInterface.call('eval', 'screen') + "&inputMethod=" + ExternalInterface.call('eval', 'inputMethod') + "&currentIndex=" + "" + (parseInt(this.jsonReader.getScreenAttributes()["matchindex"],10) - 1) + "&accountAccessStatus=" +  this.acctAccessFlag;

              this.inputData.url=this.baseURL + this.actionPath + this.actionMethod;
              this.requestParams["movement.id.entityId"]=this.jsonReader.getScreenAttributes()["entityid"];
              this.requestParams["movement.matchStatus"]= ExternalInterface.call('eval', 'status');
              this.requestParams["movement.currencyCode"]=this.jsonReader.getScreenAttributes()["currencycode"];
              this.requestParams["movementDetail.matchQuality"]=this.jsonReader.getScreenAttributes()["matchquality"];

              this.inputData.send(this.requestParams);
            } else {

              this.actionMethod="method=reconcile";
              this.actionMethod=this.actionMethod + "&nextRecord=next";
              // Modified for Mantis 1691 by Marshal on 20-June-2012
              this.actionMethod=this.actionMethod + "&day=" + "&matchCount=" + "&menuAccessId=" + "&matchId=" + this.jsonReader.getScreenAttributes()["matchid"] + "&applyCurrencyThreshold=" + ExternalInterface.call('eval', 'applyCurrencyThreshold') + "&noIncludedMovementMatches=" + ExternalInterface.call('eval', 'noIncludedMovementMatches') + "&dateTabIndFlag=" + "&dateTabInd=" + ExternalInterface.call('eval', 'dateTabInd') + "&matchType=" + ExternalInterface.call('eval', 'screen') + "&inputMethod=" + ExternalInterface.call('eval', 'inputMethod') + "&currentIndex=" + "" + (parseInt(this.jsonReader.getScreenAttributes()["matchindex"],10) - 1) + "&valueDate=" + ExternalInterface.call('eval', 'valueDate') + "&accountAccessStatus=" +  this.acctAccessFlag;
              this.inputData.url=this.baseURL + this.actionPath + this.actionMethod;
              this.requestParams["movement.id.entityId"]=this.jsonReader.getScreenAttributes()["entityid"];
              this.requestParams["movement.matchStatus"]= ExternalInterface.call('eval', 'status');
              this.requestParams["movement.currencyCode"]=this.jsonReader.getScreenAttributes()["currencycode"];
              this.requestParams["movementDetail.matchQuality"]=this.jsonReader.getScreenAttributes()["matchquality"];

              this.inputData.send(this.requestParams);

            }

          }
        }
      }
      /*Start:code modified by chinniah for Issue 1054_SEL_263*/
    } else {
      this.alertCancelButtonHandler(this.suspendFlagListener,this.confirmFlagListener);
      /*End:code modified by chinniah for Issue 1054_SEL_263*/
    }

  }


  /**
   * This Function is used to get matchid and displays the corresponding movements related to matchid
   * and apply them to the url member of the HTTPComms object
   */
  callFlexDisplay(): void {
    this.requestParameter["movement.matchId"]=this.removeLeadingZeros(this.matchIdText.text);
    this.actionMethod="method=flexdisplay";
    this.actionMethod=this.actionMethod + "&menuAccessId=" + ExternalInterface.call('eval', 'menuAccessId') + "&matchIds='" + this.matchIds + "'" + "&matchType=manual";
    this.actionMethod=this.actionMethod + "&day=" + "&matchCount=" + "&currencyCode=" + "&status=" + "&date=" + "&entityId=" + "&quality=" + "&menuAccessId=" + "&applyCurrencyThreshold=" + "&noIncludedMovementMatches=" + "&dateTabIndFlag=" + "&dateTabInd=";
    this.inputData.url=this.baseURL + this.actionPath + this.actionMethod;

    this.inputData.cbStart=this.startOfComms.bind(this);
    this.inputData.cbStop=this.endOfComms.bind(this);
    this.inputData.send(this.requestParameter);
  }



  /** This function is used for getting the predict status based on postion level */
  PosLvlCorrPreductStatus(highestPosLvl): boolean {
    let prevPosLvl1="";
    let currPosLvl1="";
    let predictStatus="";
    let predictI="";
    let statusFlag=false;

    for (let high1=0; high1 < highestPosLvl.length; high1++) {
      currPosLvl1=highestPosLvl[high1];
      if (currPosLvl1 != prevPosLvl1) {
        predictStatus+=this.mmsdGrid.dataProvider[high1].pred + ",";
      }
      prevPosLvl1=currPosLvl1;
    }

    predictStatus=predictStatus.substring(0, predictStatus.length - 1);
    let predictStatusArray=predictStatus.split(",");
    for (let k=1; k < predictStatusArray.length; k++) {
      if (predictStatusArray[k - 1] == predictStatusArray[k]) {
        statusFlag=true;
        predictI=predictStatusArray[k];
        break;
      }

    }
    if (statusFlag) {
      statusFlag=predictI != "E";
    }

    return statusFlag;

  }

  /**
   * This function is the CloseHandler of the alert when Match ID field has been amended
   */

  alertCloseHandler(event): void {

    let tmpMovId: number;
    while (this.movIds.length) {
      tmpMovId = this.movIds.pop();
      ExternalInterface.call("unlockMovementOnServer", tmpMovId);
    }

    if (this.isValidMatchId()) {
      this.actionMethod="method=flexdisplay";
      this.actionMethod=this.actionMethod + "&menuAccessId=" + ExternalInterface.call('eval', 'menuAccessId') + "&matchIds='" + this.matchIds + "'" + "&matchType=manual";
      this.actionMethod=this.actionMethod + "&day=" + "&matchCount=" + "&currencyCode=" + "&status=" + "&date=" + "&entityId=" + "&quality=" + "&menuAccessId=" + "&applyCurrencyThreshold=" + "&noIncludedMovementMatches=" + "&dateTabIndFlag=" + "&dateTabInd=";
      this.actionMethod=this.actionMethod + "&ScreenName=manual";
      this.inputData.url=this.baseURL + this.actionPath + this.actionMethod;

      // Make initial request
      this.inputData.send(this.requestParams);
      this.noteButton.enabled=true;
      this.logButton.enabled=true;
      this.unMatchButton.enabled=true;
      this.suspendButton.enabled=true;
      if (this.buttonsContainer.contains(this.confirmButton)) {
        this.confirmButton.enabled = true;
      }
      this.reconButton.enabled=true;
      this.addButton.enabled=true;
      this.mvmntButton.enabled=false;
      this.removeButton.enabled=false;
      this.exportContainer.enabled=true;
      this.optionsButton.enabled=true;

      this.inScreenMatchId = this.matchIdText.text;
    } else {
      this.mmsdGrid.dataProvider = [];
      this.matchStatus.text="";
      this.matchQuality.text="";
      this.updateDate.text="";
      this.updateUser.text="";
      this.currencyCode.text="";
      this.posIntlvl.text="";
      this.posExtlvl.text="";
      this.selectedEntityId.text="";
      this.selectedEntityName.text="";
      this.logButton.enabled=false;
      this.logButton.buttonMode=false;
      this.noteButton.enabled=false;
      this.noteButton.buttonMode=false;
      this.mvmntButton.enabled=false;
      this.mvmntButton.buttonMode=false;
      this.unMatchButton.enabled=false;
      this.unMatchButton.buttonMode=false;
      this.suspendButton.enabled=false;
      this.suspendButton.buttonMode=false;
      if (this.buttonsContainer.contains(this.confirmButton)) {
        this.confirmButton.enabled = false;
        this.confirmButton.buttonMode=false;
      }


      this.removeButton.enabled=false;
      this.removeButton.buttonMode=false;
      this.addButton.enabled=false;
      this.addButton.buttonMode=false;
      this.mmsdGrid.selectable=false;

      this.noteImage.setVisible(false);
      this.exportContainer.enabled=false;
    }
  }


  /**
   * This function is used to check the given matchid is valid
   */
  isValidMatchId(): boolean {

    this.matchIds= this.removeLeadingZeros(this.matchIdText.text);
    if ( this.matchIds == "" || Number(this.matchIds)) {
      this.swtAlert.warning( SwtUtil.getPredictMessage('label.validMatchId', null));
      return false;
    } else {
      return true;
    }
  }


  /**
   * This function is used to validate  the movement id when the Confirm buuton is clicked.
   */
  confirmMvmnt(): void {
    if (this.buttonsContainer.contains(this.confirmButton)) {
      this.confirmButton.enabled = false;
    }
    this.subScreenName = "Confirm";
    this.validateMatchId();
  }

  /** This function is used to open log avialable for the selected movements.
   */
  openLog(): void {
    this.subScreenName="Log";
    this.validateMatchId();
  }

  /** This function is used to validate  the movement id when the add buuton is clicked.
   */
  addMvmnt(): void {
    this.subScreenName="Add";
    this.validateMatchId();
  }

  /**
   * Function called to close the window when window close button is clicked
   **/
  unlockMvmnt(): void {
    if (this.mmsdGrid != null && this.mmsdGrid.selectedIndices.length > 0) {
      for (let i=0; i < this.mmsdGrid.selectedIndices.length; i++) {
        let lockflag1 = new Object();
        let lock1 = "";
        let lockUser: string = ExternalInterface.call("checkLockOnServer", this.mmsdGrid.dataProvider[this.mmsdGrid.selectedIndices[i]].movement);
        if (this.currentUser.toLowerCase() == lockUser.toLowerCase() || lockUser.toString() == "true") {
          lockflag1 = ExternalInterface.call("unlockMovementOnServer", this.mmsdGrid.dataProvider[this.mmsdGrid.selectedIndices[i]].movement);
          lock1 = lockflag1.toString();
          if (!BooleanParser.parseBooleanValue(lock1)) {
            SwtUtil.getPredictMessage('label.movementCannotBeUnlocked', null);
            this.swtAlert.warning( SwtUtil.getPredictMessage('label.movementCannotBeUnlocked', null), 'Warning');
          }
        }
      }
    }
  }

  /** This function is used to validate and display the selected movement details when the movement buuton
   * is clicked.
   */
  openMvmnt(): void {
    this.subScreenName="Mvmnt";
    this.validateMatchId();
  }

  /** This function is used to display the previous match details when the previous buuton
   * is clicked.
   */
  openPreviousRecord(): void {
    this.diffValue.text="";
    if(this.serverBusyFlag) {
      return;
    }

    this.actionMethod="method=next";
    this.actionMethod= this.actionMethod + "&nextRecord=previous";
    let minus: number = (ExternalInterface.call("checkMatchId", this.matchIdText.text) == "false") ? 0 : 1;
    this.actionMethod=this.actionMethod + "&day=" + "&matchCount=" + "&menuAccessId=" + ExternalInterface.call('eval', 'menuAccessId') + "&status=" + ExternalInterface.call('eval', 'status') + "&applyCurrencyThreshold=" + ExternalInterface.call('eval', 'applyCurrencyThreshold') + "&noIncludedMovementMatches=" + ExternalInterface.call('eval', 'noIncludedMovementMatches') + "&dateTabIndFlag=" + "&dateTabInd=" + ExternalInterface.call('eval', 'dateTabInd') + "&matchType=" + ExternalInterface.call('eval', 'screen') + "&inputMethod=" + ExternalInterface.call('eval', 'inputMethod') + "&currentIndex=" + "" + (Number(this.jsonReader.getScreenAttributes()["matchindex"]) - minus) + "&valueDate=" + ExternalInterface.call('eval', 'valueDate');
    let parentScreen: string=ExternalInterface.call('eval', 'calledFrom');
    if(parentScreen == "scenarioSummary") {
      this.actionMethod=this.actionMethod+"&scenarioId=" + ExternalInterface.call('eval', 'scenarioId') +"&currGrp=" + ExternalInterface.call('eval', 'currGrp')+"&calledFrom=" + ExternalInterface.call('eval', 'calledFrom') ;
      this.requestParams["movement.id.entityId"]=ExternalInterface.call('eval', 'entityId');
      this.requestParams["movement.matchStatus"]= ExternalInterface.call('eval', 'status');
      this.requestParams["movement.currencyCode"]=ExternalInterface.call('eval', 'currencyCode');
      this.requestParams["movementDetail.matchQuality"]=ExternalInterface.call('eval', 'quality');
    } else {
      this.requestParams["movement.id.entityId"]=this.jsonReader.getScreenAttributes()["entityid"];
      this.requestParams["movement.matchStatus"]= ExternalInterface.call('eval', 'status');
      this.requestParams["movement.currencyCode"]=this.jsonReader.getScreenAttributes()["currencycode"];
      this.requestParams["movementDetail.matchQuality"]=this.jsonReader.getScreenAttributes()["matchquality"];
    }
    this.inputData.url=this.baseURL + this.actionPath + this.actionMethod;
    this.selectedMovements = [];
    let tmpMovId: number;
    while (this.movIds.length) {
      tmpMovId = this.movIds.pop();
      ExternalInterface.call("unlockMovementOnServer", tmpMovId);
    }
    this.inputData.send(this.requestParams);
    this.mmsdGrid.selectedIndex = -1;

  }



  /** This function is used to display the next match details when the next buuton
   * is clicked.
   */
  openNextRecord(): void {
    this.diffValue.text="";
    if(this.serverBusyFlag) {
      return;
    }
    this.actionMethod="method=next";
    this.actionMethod = this.actionMethod + "&nextRecord=next";
    let minus: number = (ExternalInterface.call("checkMatchId", this.matchIdText.text) == "false") ? 2 : 1;
    this.actionMethod= this.actionMethod + "&day=" + "&matchCount=" + "&menuAccessId=" + ExternalInterface.call('eval', 'menuAccessId') + "&status=" + ExternalInterface.call('eval', 'status') + "&applyCurrencyThreshold=" + ExternalInterface.call('eval', 'applyCurrencyThreshold') + "&noIncludedMovementMatches=" + ExternalInterface.call('eval', 'noIncludedMovementMatches') + "&dateTabIndFlag=" + "&dateTabInd=" + ExternalInterface.call('eval', 'dateTabInd') + "&matchType=" + ExternalInterface.call('eval', 'screen') + "&inputMethod=" + ExternalInterface.call('eval', 'inputMethod') + "&currentIndex=" + "" + (Number(this.jsonReader.getScreenAttributes()["matchindex"]) - minus ) + "&valueDate=" + ExternalInterface.call('eval', 'valueDate');
    let parentScreen: string = ExternalInterface.call('eval', 'calledFrom');
    if(parentScreen == "scenarioSummary") {
      this.actionMethod= this.actionMethod+"&scenarioId=" + ExternalInterface.call('eval', 'scenarioId') + "&currGrp=" + ExternalInterface.call('eval', 'currGrp') + "&calledFrom=" + ExternalInterface.call('eval', 'calledFrom') ;
      this.requestParams["movement.id.entityId"]=ExternalInterface.call('eval', 'entityId');
      this.requestParams["movement.matchStatus"]= ExternalInterface.call('eval', 'status');
      this.requestParams["movement.currencyCode"]=ExternalInterface.call('eval', 'currencyCode');
      this.requestParams["movementDetail.matchQuality"]=ExternalInterface.call('eval', 'quality');
    } else {
      this.requestParams["movement.id.entityId"]=this.jsonReader.getScreenAttributes()["entityid"];
      this.requestParams["movement.matchStatus"]= ExternalInterface.call('eval', 'status');
      this.requestParams["movement.currencyCode"]= this.jsonReader.getScreenAttributes()["currencycode"];
      this.requestParams["movementDetail.matchQuality"]= this.jsonReader.getScreenAttributes()["matchquality"];
    }
    this.inputData.url= this.baseURL + this.actionPath + this.actionMethod;
    let tmpMovId: number;
    this.selectedMovements = [];
    while (this.movIds.length) {
      tmpMovId = this.movIds.pop();
      ExternalInterface.call("unlockMovementOnServer", tmpMovId);
    }

    this.inputData.send(this.requestParams);
    this.mmsdGrid.selectedIndex = -1;
  }

  /** This function is used to validate  the match id when the UnMatch buuton is clicked.
   */
  openUnMatch(): void {
    this.subScreenName="UnMatch";
    this.validateMatchId();
  }

  /** This function is used to validate  the movement id when the Reconcile buuton is clicked.
   */
  reconcileMvmnt(): void {
    this.subScreenName="Recon";
    this.validateMatchId();
  }

  /** This function is used to validate  the movement id when Remove buuton is clicked.
   */
  removeMvmnt(): void {
    this.subScreenName="Remove";
    this.validateMatchId();
  }
  unMatchMatchId(): void {
    let screenName: string = ExternalInterface.call('eval', 'match');
    let rowCount: number = this.lastReceivedJSON.movementmatchsummarydisplay.grid.rows.size;
    let CheckLockFlag="";
    let movements = "";
    let checkUser = false;
    let parentScreen: string = ExternalInterface.call('eval', 'calledFrom');
    let parentParentScreen: string = ExternalInterface.call('eval', 'parentScreen');
    let loggedInUser: string = ExternalInterface.call('eval', 'loggedInUser');
   // if (this.mmsdGrid.selectedIndices.length == 0) {
    for (let i=0; i < rowCount; i++) {
        movements += this.mmsdGrid.dataProvider[i].movement + ",";
    }
    CheckLockFlag = ExternalInterface.call("checkLocksOnServer", movements);
    if ((loggedInUser == CheckLockFlag) && ((parentScreen == "mvmntdisplay") && (parentParentScreen == "movementSummaryDisplay"))) {
        checkUser=true;
    }

    if (!BooleanParser.parseBooleanValue(CheckLockFlag) && !checkUser) {
        this.swtAlert.warning(SwtUtil.getPredictMessage('label.mvmtAreBusy', null) + " "+CheckLockFlag);
    } else {
        let entityId: string = this.selectedEntityId.text;
        /* Gets the match status */
        let lockflag1: string = ExternalInterface.call("lockMatchOnServer", "unmatch", this.matchIds, entityId);
        if (!BooleanParser.parseBooleanValue(lockflag1)) {
          this.swtAlert.warning( SwtUtil.getPredictMessage('label.matchIsInUseByanotherProces', null));
        } else {
          if (screenName == "manual") {
            this.refreshFlag=true;
            this.actionMethod="method=unmatch";
            this.actionMethod= this.actionMethod + "&day=" + "&matchCount=" + "&menuAccessId=" + "&manualMatchScreen=frommanualMatchScreen" + "&matchId=" + this.jsonReader.getScreenAttributes()["matchid"] + "&applyCurrencyThreshold=" + ExternalInterface.call('eval', 'applyCurrencyThreshold') + "&noIncludedMovementMatches=" + ExternalInterface.call('eval', 'noIncludedMovementMatches') + "&dateTabIndFlag=" + "&dateTabInd=" + ExternalInterface.call('eval', 'dateTabInd') + "&matchType=" + ExternalInterface.call('eval', 'screen') + "&inputMethod=" + ExternalInterface.call('eval', 'inputMethod') + "&currentIndex=" + "" + (parseInt(this.jsonReader.getScreenAttributes()["matchindex"],10) - 1) + "&accountAccessStatus=" +  this.acctAccessFlag;
            this.inputData.url= this.baseURL +  this.actionPath +  this.actionMethod;
            this.requestParams["movement.id.entityId"]= this.jsonReader.getScreenAttributes()["entityid"];
            this.requestParams["movement.matchStatus"]= ExternalInterface.call('eval', 'status');
            this.requestParams["movement.currencyCode"]= this.jsonReader.getScreenAttributes()["currencycode"];
            this.requestParams["movementDetail.matchQuality"]= this.jsonReader.getScreenAttributes()["matchquality"];
            this.inputData.send( this.requestParams);
          } else {
            this.actionMethod="method=unmatch";
            this.actionMethod= this.actionMethod + "&day=" + "&matchCount=" + "&menuAccessId=" + "&matchId=" + this.jsonReader.getScreenAttributes()["matchid"] + "&applyCurrencyThreshold=" + ExternalInterface.call('eval', 'applyCurrencyThreshold') + "&noIncludedMovementMatches=" + ExternalInterface.call('eval', 'noIncludedMovementMatches') + "&dateTabIndFlag=" + "&dateTabInd=" + ExternalInterface.call('eval', 'dateTabInd') + "&matchType=" + ExternalInterface.call('eval', 'screen') + "&inputMethod=" + ExternalInterface.call('eval', 'inputMethod') + "&currentIndex=" + "" + (parseInt(this.jsonReader.getScreenAttributes()["matchindex"],10) - 1) + "&valueDate=" + ExternalInterface.call('eval', 'valueDate') + "&accountAccessStatus=" +  this.acctAccessFlag;
            this.inputData.url= this.baseURL +  this.actionPath +  this.actionMethod;
            this.requestParams["movement.id.entityId"] = this.jsonReader.getScreenAttributes()["entityid"];
            this.requestParams["movement.matchStatus"]= ExternalInterface.call('eval', 'status');
            this.requestParams["movement.currencyCode"] = this.jsonReader.getScreenAttributes()["currencycode"];
            this.requestParams["movementDetail.matchQuality"] = this.jsonReader.getScreenAttributes()["matchquality"];

            this.inputData.send( this.requestParams);
          }
        }
      }
    // } else {
    //       if (screenName == "manual") {
    //         this.refreshFlag=true;
    //         this.actionMethod="method=unmatch";
    //         this.actionMethod= this.actionMethod + "&day=" + "&matchCount=" + "&menuAccessId=" + "&manualMatchScreen=frommanualMatchScreen" + "&matchId=" + this.jsonReader.getScreenAttributes()["matchid"] + "&applyCurrencyThreshold=" + ExternalInterface.call('eval', 'applyCurrencyThreshold') + "&noIncludedMovementMatches=" + ExternalInterface.call('eval', 'noIncludedMovementMatches') + "&dateTabIndFlag=" + "&dateTabInd=" + ExternalInterface.call('eval', 'dateTabInd') + "&matchType=" + ExternalInterface.call('eval', 'screen') + "&inputMethod=" + ExternalInterface.call('eval', 'inputMethod') + "&currentIndex=" + "" + (parseInt(this.jsonReader.getScreenAttributes()["matchindex"],10) - 1) + "&accountAccessStatus=" +  this.acctAccessFlag;
    //         this.inputData.url= this.baseURL +  this.actionPath +  this.actionMethod;
    //         this.requestParams["movement.id.entityId"]=this.jsonReader.getScreenAttributes()["entityid"];
    //         this.requestParams["movement.matchStatus"]= ExternalInterface.call('eval', 'status');
    //         this.requestParams["movement.currencyCode"]=this.jsonReader.getScreenAttributes()["currencycode"];
    //         this.requestParams["movementDetail.matchQuality"]=this.jsonReader.getScreenAttributes()["matchquality"];
    //         this.inputData.send( this.requestParams);
    //       } else {
    //         this.actionMethod="method=unmatch";
    //         this.actionMethod=this.actionMethod + "&day=" + "&matchCount=" + "&menuAccessId=" + "&matchId=" + this.jsonReader.getScreenAttributes()["matchid"] + "&applyCurrencyThreshold=" + ExternalInterface.call('eval', 'applyCurrencyThreshold') + "&noIncludedMovementMatches=" + ExternalInterface.call('eval', 'noIncludedMovementMatches') + "&dateTabIndFlag=" + "&dateTabInd=" + ExternalInterface.call('eval', 'dateTabInd') + "&matchType=" + ExternalInterface.call('eval', 'screen') + "&inputMethod=" + ExternalInterface.call('eval', 'inputMethod') + "&currentIndex=" + "" + (parseInt(this.jsonReader.getScreenAttributes()["matchindex"],10) - 1) + "&valueDate=" + ExternalInterface.call('eval', 'valueDate') + "&accountAccessStatus=" +  this.acctAccessFlag;
    //         this.inputData.url=this.baseURL + this.actionPath + this.actionMethod;
    //         this.requestParams["movement.id.entityId"]=this.jsonReader.getScreenAttributes()["entityid"];
    //         this.requestParams["movement.matchStatus"]= ExternalInterface.call('eval', 'status');
    //         this.requestParams["movement.currencyCode"]=this.jsonReader.getScreenAttributes()["currencycode"];
    //         this.requestParams["movementDetail.matchQuality"]=this.jsonReader.getScreenAttributes()["matchquality"];
    //         this.inputData.send(this.requestParams);
    //       }
    //     }

   // }
  }


  /**
   *  This function is used to set font size for data grid
   */
  fontSettingHandler(): void {
    this.win= SwtPopUpManager.createPopUp(this,
      FontSetting,
      {
        title: "Options",
        fontSizeValue: this.selectedFont
      });
    this.win.isModal = true;
    this.win.enableResize = false;
    this.win.width = "340";
    this.win.height = "140";
    this.win.id = "fontPopUp";
    this.win.enableResize = false;
    this.win.showControls = true;
    this.win.onClose.subscribe((res) => {
      this.submitFontSize(res);
    }, error => {
      console.log(error);
    });
    this.win.display();
  }

  submitFontSize(event): void {
    this.fontValue = event.fontSize.value;
    if (this.fontValue == "N") {
      this.selectedFont=0;
      this.fontLabel = event.fontSize.label;
      this.mmsdGrid.styleName="dataGridNormal";
      this.mmsdGrid.rowHeight= 18;
      this.tempFontSize="Normal";
      this.fontRequest = ExternalInterface.call("getUpdateFontSize", this.fontLabel);
    } else if (this.fontValue == "S") {
      this.selectedFont = 1;
      this.fontLabel = event.fontSize.label;
      this.mmsdGrid.styleName="dataGridSmall";
      this.mmsdGrid.rowHeight = 15;
      this.fontRequest = ExternalInterface.call("getUpdateFontSize", this.fontLabel);
    }
    if (this.fontRequest != null && this.fontRequest != "") {
      this.updateFontSize.url = this.baseURL + this.fontRequest;
      this.updateFontSize.send();
    }


  }


  cellLogic(e): void {
    let lockflag = null;
    let lock = null;
    let checkUser=false;
    let parentScreen: string = ExternalInterface.call('eval', 'calledFrom');
    let parentParentScreen: string = ExternalInterface.call('eval', 'parentScreen');

    for(let i=0; i< this.mmsdGrid.selectedItems.length; i++) {
      if ((parentScreen == "mvmntdisplay") && (parentParentScreen == "movementSummaryDisplay")) {
        checkUser=true;
      }
      lockflag = new Object();
      lock = "";
      if(this.previousSelectedIndices.indexOf(this.mmsdGrid.selectedItems[i].movement.content) == -1) {
        lockflag = ExternalInterface.call("lockMovementOnServer", this.mmsdGrid.selectedItems[i].movement.content);
        lock = lockflag.toString();
        if (!BooleanParser.parseBooleanValue(lock) && !checkUser) {
          this.swtAlert.warning(SwtUtil.getPredictMessage('manualInput.id.movementId', null)+ " " + this.mmsdGrid.selectedItems[i].movement.content + " "+SwtUtil.getPredictMessage('label.isInBusyBy', null)+" " + lock);
        }
      }
    }
    let tempTable =[];
    for(let i =0 ; i<this.mmsdGrid.selectedItems.length ; i++) {
    tempTable.push(this.mmsdGrid.selectedItems[i].movement.content);
  }
    for(let i=0; i< this.previousSelectedIndices.length; i++) {
      lockflag = new Object();
      lock = "";
      if(tempTable.indexOf(this.previousSelectedIndices[i]) == -1) {
        let lockUser: string = ExternalInterface.call("checkLockOnServer", this.previousSelectedIndices[i]);
        if (this.currentUser.toLowerCase() == lockUser.toLowerCase() || lockUser.toString() == "true") {
          lockflag = ExternalInterface.call("unlockMovementOnServer", this.previousSelectedIndices[i]);
          lock = lockflag.toString();
          if (!BooleanParser.parseBooleanValue(lock)) {
            SwtUtil.getPredictMessage('label.movementCannotBeUnlocked', null);
            this.swtAlert.warning( SwtUtil.getPredictMessage('label.movementCannotBeUnlocked', null), 'Warning');
          }
        }
      }
    }
    this.previousSelectedIndices=[];
    for(let i =0 ; i<this.mmsdGrid.selectedItems.length ; i++) {
      this.previousSelectedIndices.push(this.mmsdGrid.selectedItems[i].movement.content);
    }
    if (this.mmsdGrid.selectedIndices.length > 1) {
      if (!BooleanParser.parseBooleanValue(this.fullAccess)) {
        this.unMatchButton.enabled=false;
        this.removeButton.enabled=false;
        this.mvmntButton.enabled=false;
        this.mvmntButton.buttonMode=true;
      } else {
        this.unMatchButton.enabled=true;
        this.removeButton.enabled=true;
        this.noteButton.enabled=true;
        this.noteButton.buttonMode=true;
        this.logButton.enabled=true;
        this.logButton.buttonMode=true;
        if (this.buttonsContainer.contains(this.suspendButton)) {
        this.suspendButton.enabled=true;
        this.suspendButton.buttonMode=true;
        }
        if (this.buttonsContainer.contains(this.confirmButton)) {
          this.confirmButton.enabled = true;
          this.confirmButton.buttonMode=true;
        }

        this.reconButton.enabled=true;
        this.reconButton.buttonMode=true;
        this.mvmntButton.enabled=false;
        this.mvmntButton.buttonMode=true;
        this.unMatchButton.buttonMode=true;
        this.removeButton.buttonMode=true;
        this.removeButton.enabled=true;
      }

    } else if (this.mmsdGrid.selectedIndices.length == 1) {
      this.mvmntButton.enabled=true;
      if (!BooleanParser.parseBooleanValue(this.fullAccess)) {
        this.unMatchButton.enabled=false;
        this.removeButton.enabled=false;

        this.mvmntButton.buttonMode=true;
      } else {
        /*Modified By Fatma when selecting a row enable the other buttons*/
        this.noteButton.enabled=true;
        this.noteButton.buttonMode=true;
        this.logButton.enabled=true;
        this.logButton.buttonMode=true;
        if (this.buttonsContainer.contains(this.suspendButton)) {
        this.suspendButton.enabled=true;
        this.suspendButton.buttonMode=true;
        }
        if (this.buttonsContainer.contains(this.confirmButton)) {
          this.confirmButton.enabled = true;
          this.confirmButton.buttonMode=true;
        }

        this.reconButton.enabled=true;
        this.reconButton.buttonMode=true;
        this.mvmntButton.buttonMode=true;
        this.unMatchButton.buttonMode=true;
        this.removeButton.buttonMode=true;
        this.removeButton.enabled=true;
      }
    } else if (this.mmsdGrid.selectedIndices.length == 0) {
      // Modified by KaisBS for issue 1054_SEL_212
      if (this.mmsdGrid.dataProvider.length == 0) {
        this.noteButton.enabled=false;
        this.logButton.enabled=false;
        this.unMatchButton.enabled=false;
        if (this.buttonsContainer.contains(this.suspendButton)) {
        this.suspendButton.enabled=false;
        }
        // reconButton.enabled=false;
        if (this.buttonsContainer.contains(this.confirmButton)) {
          this.confirmButton.enabled = false;
        }
      } else if (!BooleanParser.parseBooleanValue(this.fullAccess)) {
        this.noteButton.enabled=true;
        this.logButton.enabled=true;
        this.unMatchButton.enabled=false;
        if (this.buttonsContainer.contains(this.suspendButton)) {
        this.suspendButton.enabled=false;
        }
        this.reconButton.enabled=false;
        if (this.buttonsContainer.contains(this.confirmButton)) {
          this.confirmButton.enabled = false;
        }
      } else {
        this.noteButton.enabled=true;
        this.logButton.enabled=true;
        this.unMatchButton.enabled=true;
        if (this.buttonsContainer.contains(this.suspendButton)) {
        this.suspendButton.enabled=true;
        }
        this.reconButton.enabled=true;
        if (this.buttonsContainer.contains(this.confirmButton)) {
          this.confirmButton.enabled = true;
          this.confirmButton.buttonMode=true;
        }
      }
      this.removeButton.enabled=false;
      this.removeButton.buttonMode=false;
      this.mvmntButton.enabled=false;
      this.mvmntButton.buttonMode=false;
      this.noteButton.buttonMode=true;
      this.logButton.buttonMode=true;
      this.unMatchButton.buttonMode=true;
      if (this.buttonsContainer.contains(this.suspendButton)) {
      this.suspendButton.buttonMode=true;
      }
      this.reconButton.buttonMode=true;


    }

    if (ExternalInterface.call('eval', 'archiveId') != "") {
      this.removeButton.enabled=false;
      this.removeButton.buttonMode=false;
      this.mvmntButton.enabled=false;
      this.mvmntButton.buttonMode=false;
      this.logButton.enabled=false;
      this.logButton.buttonMode=false;
      this.unMatchButton.enabled=false;
      this.unMatchButton.buttonMode=false;
      if (this.buttonsContainer.contains(this.suspendButton)) {
      this.suspendButton.enabled=false;
      this.suspendButton.buttonMode=false;
      }
      if (this.buttonsContainer.contains(this.confirmButton)) {
        this.confirmButton.enabled = false;
        this.confirmButton.buttonMode=false;
      }

      this.addButton.enabled=false;
      this.addButton.buttonMode=false;
    }
    if (!this.acctAccessFlag) {
      this.removeButton.enabled=false;
      this.removeButton.buttonMode=false;
      this.unMatchButton.enabled=false;
      this.unMatchButton.buttonMode=false;
      if (this.buttonsContainer.contains(this.suspendButton)) {
        this.suspendButton.enabled=false;
        this.suspendButton.buttonMode=false;
      }
      this.reconButton.enabled=false;
      this.reconButton.buttonMode=false;
      if (this.buttonsContainer.contains(this.confirmButton)) {
        this.confirmButton.enabled = false;
        this.confirmButton.buttonMode=false;
      }

      this.addButton.enabled=false;
      this.addButton.buttonMode=false;
    }


    let selectedList ="";
      for(let z: number = 0; z < this.mmsdGrid.dataProvider.length; z++ ) {
        selectedList+= this.mmsdGrid.dataProvider[z].movement +',';
      }
      ExternalInterface.call('setSelectedMovementForLock',selectedList);

  }

  /**
   * This method is used to maintain the button status when a row is clicked as well as the
   *  user's movement lock/unlock status.
   */
  cellLogic2(e): void {
    let checkUser=false;
    let parentScreen: string = ExternalInterface.call('eval', 'calledFrom');
    let parentParentScreen: string = ExternalInterface.call('eval', 'parentScreen');
    if (this.mmsdGrid.selectedIndices.length > 0) {
      this.previousSelectedIndices = this.mmsdGrid.selectedIndices;
      for (let chkselectLockCount=0; chkselectLockCount < this.mmsdGrid.selectedIndices.length; chkselectLockCount++) {
        let lockFlag=false;
        for (let selectLockCount=0; selectLockCount < this.selectedMovements.length; selectLockCount++) {
          if (this.selectedMovements[selectLockCount] == this.mmsdGrid.dataProvider[this.mmsdGrid.selectedIndices[chkselectLockCount]].movement) {
            lockFlag=true;
          }
        }
        if ((parentScreen == "mvmntdisplay") && (parentParentScreen == "movementSummaryDisplay")) {
          checkUser=true;
        }
        if (!lockFlag) {
          let lockflag = new Object();
          let lock = "";
          lockflag = ExternalInterface.call("lockMovementOnServer", this.mmsdGrid.dataProvider[this.mmsdGrid.selectedIndices[chkselectLockCount]].movement);
          this.movIds.push(this.mmsdGrid.dataProvider[this.mmsdGrid.selectedIndices[chkselectLockCount]].movement);
          lock = lockflag.toString();
          if (!BooleanParser.parseBooleanValue(lock) && !checkUser) {
            this.swtAlert.warning(SwtUtil.getPredictMessage('manualInput.id.movementId', null)+ " " + this.mmsdGrid.dataProvider[this.mmsdGrid.selectedIndices[chkselectLockCount]].movement + " "+SwtUtil.getPredictMessage('label.isInBusyBy', null)+" " + lock);
            let selectedIndexes = this.mmsdGrid.selectedIndices;
            for (let selectedCounter=0; selectedCounter < selectedIndexes.length; selectedCounter++) {
              if (selectedIndexes[selectedCounter] == this.mmsdGrid.selectedIndices[chkselectLockCount]) {
                selectedIndexes.splice(selectedCounter, 1);
                chkselectLockCount = chkselectLockCount - 1;
              }
            }
            this.mmsdGrid.selectedIndices=[];
            this.mmsdGrid.selectedIndices = selectedIndexes;
          }
        }
      }
    }
    let chkSelectedMovements=[];
    let chkSelectedAccount=[];
    for (let chkSelectCounter = 0; chkSelectCounter < this.mmsdGrid.selectedIndices.length; chkSelectCounter++) {
      chkSelectedMovements.push(this.mmsdGrid.dataProvider[this.mmsdGrid.selectedIndices[chkSelectCounter]].movement);
      chkSelectedAccount.push(this.mmsdGrid.dataProvider[this.mmsdGrid.selectedIndices[chkSelectCounter]].account);
    }

    for (let selectCount=0; selectCount < this.selectedMovements.length; selectCount++) {
      let unlockFlag=false;
      let currenctPageFlag=false;
      for (let chkBtmSelectCount=0; chkBtmSelectCount < this.mmsdGrid.dataProvider.length; chkBtmSelectCount++) {
        if (this.selectedMovements[selectCount] == this.mmsdGrid.dataProvider[chkBtmSelectCount].movement) {
          currenctPageFlag=true;
        }
      }
      if (!currenctPageFlag) {
        unlockFlag=true;
      }

      for (let chkSelectCount=0; chkSelectCount < chkSelectedMovements.length; chkSelectCount++) {
        if (currenctPageFlag && this.selectedMovements[selectCount] == chkSelectedMovements[chkSelectCount]) {
          unlockFlag=true;
        }
      }
      if (!unlockFlag) {
        let lockflag1 = new Object();
        let lock1="";
        let lockUser: string = ExternalInterface.call("checkLockOnServer", this.selectedMovements[selectCount]);
        if (this.currentUser.toLowerCase() == lockUser.toLowerCase() || lockUser.toString() == "true") {
          lockflag1=ExternalInterface.call("unlockMovementOnServer", this.selectedMovements[selectCount]);
          lock1 = lockflag1.toString();
          if (!BooleanParser.parseBooleanValue(lock1)) {
            this.swtAlert.warning(SwtUtil.getPredictMessage('label.movementCannotBeUnlocked', null));
          }
        }
      }
    }

    this.selectedMovements =[];
    for (let selMvmtCounter=0; selMvmtCounter < this.mmsdGrid.selectedIndices.length; selMvmtCounter++) {
      this.selectedMovements.push(this.mmsdGrid.dataProvider[this.mmsdGrid.selectedIndices[selMvmtCounter]].movement);
    }

    if (this.mmsdGrid.selectedIndices.length > 1) {
      if (!BooleanParser.parseBooleanValue(this.fullAccess)) {
        this.unMatchButton.enabled=false;
        this.removeButton.enabled=false;
        this.mvmntButton.enabled=false;
        this.mvmntButton.buttonMode=true;
      } else {
        this.unMatchButton.enabled=true;
        this.removeButton.enabled=true;
        this.noteButton.enabled=true;
        this.noteButton.buttonMode=true;
        this.logButton.enabled=true;
        this.logButton.buttonMode=true;
        if (this.buttonsContainer.contains(this.suspendButton)) {
          this.suspendButton.enabled=true;
          this.suspendButton.buttonMode=true;
        }

        if (this.buttonsContainer.contains(this.confirmButton)) {
          this.confirmButton.enabled = true;
          this.confirmButton.buttonMode=true;
        }

        this.reconButton.enabled=true;
        this.reconButton.buttonMode=true;
        this.mvmntButton.enabled=false;
        this.mvmntButton.buttonMode=true;
        this.unMatchButton.buttonMode=true;
        this.removeButton.buttonMode=true;
        this.removeButton.enabled=true;
      }

    } else if (this.mmsdGrid.selectedIndices.length == 1) {
      this.mvmntButton.enabled=true;
      if (!BooleanParser.parseBooleanValue(this.fullAccess)) {
        this.unMatchButton.enabled=false;
        this.removeButton.enabled=false;

        this.mvmntButton.buttonMode=true;
      } else {
        /*Modified By Fatma when selecting a row enable the other buttons*/
        this.noteButton.enabled=true;
        this.noteButton.buttonMode=true;
        this.logButton.enabled=true;
        this.logButton.buttonMode=true;
        if (this.buttonsContainer.contains(this.suspendButton)) {
          this.suspendButton.enabled=true;
          this.suspendButton.buttonMode=true;
        }
        if (this.buttonsContainer.contains(this.confirmButton)) {
          this.confirmButton.enabled = true;
          this.confirmButton.buttonMode=true;
        }

        this.reconButton.enabled=true;
        this.reconButton.buttonMode=true;
        this.mvmntButton.buttonMode=true;
        this.unMatchButton.buttonMode=true;
        this.removeButton.buttonMode=true;
        this.removeButton.enabled=true;
      }
    } else if (this.mmsdGrid.selectedIndices.length == 0) {
      // Modified by KaisBS for issue 1054_SEL_212
      if (this.mmsdGrid.dataProvider.length == 0) {
        this.noteButton.enabled=false;
        this.logButton.enabled=false;
        this.unMatchButton.enabled=false;
        if (this.buttonsContainer.contains(this.suspendButton)) {
          this.suspendButton.enabled=false;
        }
        // reconButton.enabled=false;
        if (this.buttonsContainer.contains(this.confirmButton)) {
          this.confirmButton.enabled = false;
        }
      } else if (!BooleanParser.parseBooleanValue(this.fullAccess)) {
        this.noteButton.enabled=true;
        this.logButton.enabled=true;
        this.unMatchButton.enabled=false;
        if (this.buttonsContainer.contains(this.suspendButton)) {
          this.suspendButton.enabled=false;
        }
        // reconButton.enabled=false;
        if (this.buttonsContainer.contains(this.confirmButton)) {
          this.confirmButton.enabled = false;
        }
      } else {
        this.noteButton.enabled=true;
        this.logButton.enabled=true;
        this.unMatchButton.enabled=true;
        if (this.buttonsContainer.contains(this.suspendButton)) {
          this.suspendButton.enabled=true;
          this.suspendButton.buttonMode=true;
        }
        this.reconButton.enabled=true;
        if (this.buttonsContainer.contains(this.confirmButton)) {
          this.confirmButton.enabled = true;
          this.confirmButton.buttonMode=true;
        }
      }
      this.removeButton.enabled=false;
      this.removeButton.buttonMode=false;
      this.mvmntButton.enabled=false;
      this.mvmntButton.buttonMode=false;
      this.noteButton.buttonMode=true;
      this.logButton.buttonMode=true;
      this.unMatchButton.buttonMode=true;

      this.reconButton.buttonMode=true;


    }

    if (ExternalInterface.call('eval', 'archiveId') != "") {
      this.removeButton.enabled=false;
      this.removeButton.buttonMode=false;
      this.mvmntButton.enabled=false;
      this.mvmntButton.buttonMode=false;
      this.logButton.enabled=false;
      this.logButton.buttonMode=false;
      this.unMatchButton.enabled=false;
      this.unMatchButton.buttonMode=false;
      if (this.buttonsContainer.contains(this.suspendButton)) {
        this.suspendButton.enabled=false;
        this.suspendButton.buttonMode=false;
      }
      if (this.buttonsContainer.contains(this.confirmButton)) {
        this.confirmButton.enabled = false;
        this.confirmButton.buttonMode=false;
      }

      this.addButton.enabled=false;
      this.addButton.buttonMode=false;
    }
    if (!this.acctAccessFlag) {
      this.removeButton.enabled=false;
      this.removeButton.buttonMode=false;
      this.unMatchButton.enabled=false;
      this.unMatchButton.buttonMode=false;
      if (this.buttonsContainer.contains(this.suspendButton)) {
        this.suspendButton.enabled=false;
        this.suspendButton.buttonMode=false;
      }
      this.reconButton.enabled=false;
      this.reconButton.buttonMode=false;
      if (this.buttonsContainer.contains(this.confirmButton)) {
        this.confirmButton.enabled = false;
        this.confirmButton.buttonMode=false;
      }

      this.addButton.enabled=false;
      this.addButton.buttonMode=false;
    }

  }

  @HostListener('window:mmsd.movementNotes', ['$event']) 
  onPaymentSuccess(event): void {
      setTimeout(()=> {
        // this.updateData('no');
        if (event.detail) {
          this.movementNotes(event.detail.method,event.detail.movementId,event.detail.notesFlag);
        }
      }, 0)
  }



  /* Call back method.It calls when the movement is added while giving the movementid through
          add button as well as while adding notes image through notes button */
  movementNotes(methodName: string, mvmntId: string, notesFlag: string): void {
    if (methodName == "flexadd") {
      this.isActionRun = true;
      this.isRefreshRun=true;
      this.actionMethod = "method=" + methodName;
      /*start: Modified for Mantis 1691 by sandeepkumar on 12-July-2012 */
      this.actionMethod=this.actionMethod + "&menuAccessId=" + ExternalInterface.call('eval', 'menuAccessId') + "&copiedMovementId=" + mvmntId + "&matchType=" + ExternalInterface.call('eval', 'screen') + "&matchList=" + ExternalInterface.call('eval', 'matches');
      this. actionMethod = this.actionMethod + "&day=" + "&currencyCode=" + "&status=" +this.jsonReader.getScreenAttributes()["mvmntstatus"] + "&date=" + "&entityId=" + this.jsonReader.getScreenAttributes()["entityid"] + "&quality=" + "&menuAccessId=" + "&applyCurrencyThreshold=" + ExternalInterface.call('eval', 'applyCurrencyThreshold') + "&noIncludedMovementMatches=" + ExternalInterface.call('eval', 'noIncludedMovementMatches') + "&dateTabIndFlag=" + "&dateTabInd=" + ExternalInterface.call('eval', 'dateTabInd') + "&currentIndex=" + "" + (parseInt(this.jsonReader.getScreenAttributes()["matchindex"],10) - 1);
      let lockflag1 = new Object();
      let lock1="";
      if (this.mmsdGrid.selectedIndices.length > 0) {
        // displays movements one by one on grid
        for (let i = 0; i < this.mmsdGrid.selectedIndices.length; i++) {
          lockflag1=ExternalInterface.call("unlockMovementOnServer", this.mmsdGrid.dataProvider[this.mmsdGrid.selectedIndices[i]].movement);
          lock1=lockflag1.toString();
          if (!BooleanParser.parseBooleanValue(lock1)) {
            this.swtAlert.warning(SwtUtil.getPredictMessage('label.movementCannotBeUnlocked', null), 'Warning');
        }
        }
      }
      this.mmsdGrid.selectedIndex = -1;
      /*End: Modified for Mantis 1691 by sandeepkumar on 12-July-2012 */
      if (notesFlag != "") {
        this.actionMethod = this.actionMethod + "&matchCount=" + notesFlag;
      }
      this.requestParams["movement.matchId"]= this.jsonReader.getScreenAttributes()["matchid"];

      this.requestParams["movement.matchStatus"]= ExternalInterface.call('eval', 'status');

      this.requestParams["movementDetail.matchQuality"] = this.jsonReader.getScreenAttributes()["matchquality"];
      // Then apply them to the url member of the HTTPComms object:
      this.inputData.url = this.baseURL + this.actionPath + this.actionMethod;
      this.inputData.send(this.requestParams);
    } else {
      this.requestURL = SwtUtil.getBaseURL();
      if (notesFlag == "Y") {
        this.noteImage.source = this.requestURL + ExternalInterface.call('eval', 'notesImage');
        this.noteImage.toolTip="Notes Available";
      } else {
        this.noteImage.source = this.requestURL + ExternalInterface.call('eval', 'blankImage');
        this.noteImage.toolTip="";
      }

    }

    if (methodName == "unlockallmovements") {
      let movementId="";
      if (this.mmsdGrid != null && this.mmsdGrid.dataProvider.length > 0) {
        for (let j=0; j < this.mmsdGrid.dataProvider.length; j++) {
          movementId = this.mmsdGrid.dataProvider[j].movement;
          ExternalInterface.call("unlockMovementOnServer", movementId);
        }
      }
    }
  }


  /**
   * Part of a callback function to all for control of the loading swf from the HTTPComms Object
   **/
  startOfComms(): void {
    try{
      if(this.isRefreshRun){
        this.isRefreshRun = false;
        let mvmCount = 0;
        if(this.lastReceivedJSON && this.lastReceivedJSON.movementmatchsummarydisplay && this.lastReceivedJSON.movementmatchsummarydisplay.grid && this.lastReceivedJSON.movementmatchsummarydisplay.grid.rows )
        mvmCount = this.lastReceivedJSON.movementmatchsummarydisplay.grid.rows.size ;
        if(mvmCount>100){
          this.winProgress= SwtPopUpManager.createPopUp(this,
            ProgressBar,
            {
              title: "Processing...", // childTitle,
            });
          this.winProgress.width = "280";
          this.winProgress.height = "120";
          this.winProgress.id = "myOptionsPopUp";
          this.winProgress.enableResize = false;
          this.winProgress.showControls = false;
          this.winProgress.isModal = true;
          this.winProgress.onClose.subscribe(() => {
            // this.autoRefreshAfterStop();
          }, error => {
            console.log(error);
          });
          this.winProgress.display();
        }
      }
    } catch (error) {
      SwtUtil.logError(error, this.moduleId,"Dashboard"  , "optionsHandler", this.errorLocation);
    }
    this.loadingImage.setVisible(true);
  }

  /**
   * Part of a callback function to all for control of the loading swf from the HTTPComms Object
   **/
  endOfComms(): void {
    try {
      this.winProgress.close();
      
    } catch (error) {
      
    }
    this.loadingImage.setVisible(false);
  }

   /**
   * Part of a callback function to all for control of the loading swf from the HTTPComms Object
   **/
  startOfCommsAction(): void {
    try{
      if(this.isActionRun){
        this.isActionRun = false;
        let mvmCount = 0;
        if(this.lastReceivedJSON && this.lastReceivedJSON.movementmatchsummarydisplay && this.lastReceivedJSON.movementmatchsummarydisplay.grid && this.lastReceivedJSON.movementmatchsummarydisplay.grid.rows )
        mvmCount = this.lastReceivedJSON.movementmatchsummarydisplay.grid.rows.size ;
        if(mvmCount>100){
          this.winProgress= SwtPopUpManager.createPopUp(this,
            ProgressBar,
            {
              title: "Processing...", // childTitle,
            });
          this.winProgress.width = "280";
          this.winProgress.height = "120";
          this.winProgress.id = "myOptionsPopUp";
          this.winProgress.enableResize = false;
          this.winProgress.showControls = false;
          this.winProgress.isModal = true;
          this.winProgress.onClose.subscribe(() => {
            // this.autoRefreshAfterStop();
          }, error => {
            console.log(error);
          });
          this.winProgress.display();
        }
      }
    } catch (error) {
      SwtUtil.logError(error, this.moduleId,"Dashboard"  , "optionsHandler", this.errorLocation);
    }
    this.loadingImage.setVisible(true);
  }

  /**
   * Part of a callback function to all for control of the loading swf from the HTTPComms Object
   **/
  endOfCommsAction(): void {
    try {
      this.winProgress.close();
      
    } catch (error) {
      
    }
    this.loadingImage.setVisible(false);
  }

  /**
   * This method is used to display the connection error
   **/
  connError(): void {
    this.swtAlert.error("" + this.invalidComms, "Error");
  }



  /**
   *  Keydown event calls when the matchId is given on the matchId texbox  on the Manual Match screen
   **/
  getMatchId(event): void {
    if (this.matchType=="manual") {
      if (event.keyCode == Keyboard.ENTER) {
        /* added by Kais and Mansour for mantis 1736: the movement remains locked
                    when clicking on the buttons next or previous: BEGIN*/
        let tmpMovId: number;
        this.selectedMovements = [];
        while (this.movIds.length) {
          tmpMovId = this.movIds.pop();
          ExternalInterface.call("unlockMovementOnServer", tmpMovId);
        }
        this.inScreenMatchId = this.removeLeadingZeros(this.matchIdText.text);
        this.matchIds = this.removeLeadingZeros(this.matchIdText.text);
        if (this.matchIds == "" ) { // || Number(this.matchIds)
          this.swtAlert.warning("Please enter a valid MatchId");
          this.mmsdGrid.dataProvider = [];
          this.matchStatus.text="";
          this.matchQuality.text="";
          this.updateDate.text="";
          this.updateUser.text="";
          this.currencyCode.text="";
          this.posIntlvl.text="";
          this.posExtlvl.text="";
          this.selectedEntityId.text="";
          this.selectedEntityName.text="";
          this.logButton.enabled=false;
          this.logButton.buttonMode=false;
          if (this.buttonsContainer.contains(this.noteButton)) {

            this.noteButton.enabled=false;
            this.noteButton.buttonMode=false;
          }

          this.mvmntButton.enabled=false;
          this.mvmntButton.buttonMode=false;
          if (this.buttonsContainer.contains(this.unMatchButton)) {
            this.unMatchButton.enabled=false;
            this.unMatchButton.buttonMode=false;
          }
          if (this.buttonsContainer.contains(this.suspendButton)) {
            this.suspendButton.enabled=false;
            this.suspendButton.buttonMode=false;
          }
          if (this.buttonsContainer.contains(this.confirmButton)) {
            this.confirmButton.enabled = false;
            this.confirmButton.buttonMode=false;
          }
          if (this.buttonsContainer.contains(this.removeButton)) {
            this.removeButton.enabled=false;
            this.removeButton.buttonMode=false;
          }
          if (this.buttonsContainer.contains(this.addButton)) {
            this.addButton.enabled=false;
            this.addButton.buttonMode=false;
          }
          this.noteImage.setVisible(false);
          this.exportContainer.enabled=false;
        } else {
          this.actionMethod="method=flexdisplay";
          this.actionMethod=this.actionMethod + "&menuAccessId=" + ExternalInterface.call('eval', 'menuAccessId') + "&matchIds='" + this.matchIds + "'" + "&matchType=manual";
          this.actionMethod=this.actionMethod + "&day=" + "&matchCount=" + "&currencyCode=" + "&status=" + "&date=" + "&quality=" + "&menuAccessId=" + "&applyCurrencyThreshold=" + "&noIncludedMovementMatches=" + "&dateTabIndFlag=" + "&dateTabInd="+ "&archiveId="+ExternalInterface.call('eval', 'archiveId')+ "&entityId="+ExternalInterface.call('eval', 'entityId');
          this.actionMethod=this.actionMethod + "&ScreenName=manual";
          this.inputData.url=this.baseURL + this.actionPath + this.actionMethod;

          this.inputData.send(this.requestParams);
          this.noteButton.enabled=true;
          this.logButton.enabled=true;
          this.unMatchButton.enabled=true;
          if (this.buttonsContainer.contains(this.suspendButton)) {
            this.suspendButton.enabled=true;
          }
          if (this.buttonsContainer.contains(this.confirmButton)) {
            this.confirmButton.enabled=true;
          }
          if (this.buttonsContainer.contains(this.reconButton)) {
            this.reconButton.enabled=true;
          }
          if (this.buttonsContainer.contains(this.addButton)) {
            this.addButton.enabled=true;
          }
          this.mvmntButton.enabled=false;
          if (this.buttonsContainer.contains(this.removeButton)) {
            this.removeButton.enabled=false;
          }
          this.exportContainer.enabled=true;
          this.optionsButton.enabled=true;
          this.noteImage.setVisible(true);

        }
      }
    }
  }


  /**
   * The function initializes the menus in the right click event on the Entity Monitor screen.
   * The links are redirected to their respective pages.
   */
  initializeMenus(): void {
    this.screenVersion.loadScreenVersion(this, this.screenName, this.versionNumber, this.versionDate);
    let addMenuItem = new ContextMenuItem(SwtUtil.getPredictMessage('screen.showJSON', null));
    // add the listener to addMenuItem
    addMenuItem.MenuItemSelect = this.showJSONSelect.bind(this);
    this.screenVersion.svContextMenu.customItems.push(addMenuItem);
    this.contextMenu=this.screenVersion.svContextMenu;
  }

  /** This function is used to display the XML for the monitor selected in the monitor combo
   */
  showJSONSelect(event): void {

    this.showJsonPopup = SwtPopUpManager.createPopUp(this,
      JSONViewer,
      {
        jsonData: this.lastReceivedJSON,
      });
    this.showJsonPopup.width = "700";
    this.showJsonPopup.title = "Last Received JSON";
    this.showJsonPopup.height = "500";
    this.showJsonPopup.enableResize = false;
    this.showJsonPopup.showControls = true;
    this.showJsonPopup.display();
  }


  /**
   * This function remove the leading zeros to allow a correct
   * comparison between strings.
   */
  removeLeadingZeros(matchId: string): string {
    while(matchId.indexOf('0') == 0) {
      matchId = matchId.substr(1);
    }
    return matchId;
  }


  /**
   * This function remove the leading zeros to allow a correct
   * comparison between strings.
   */
  filterUpdate(event): void {
    this.cellLogic(event);
  }

  /**
   * Function called to close the window when close button is clicked
   **/
  closeHandler(event): void {
    if (this.mmsdGrid != null && this.mmsdGrid.selectedIndices.length > 0) {
      for (let i=0; i < this.mmsdGrid.selectedIndices.length; i++) {
        let lockflag1 = new Object();
        let lock1="";
        let lockUser: string = ExternalInterface.call("checkLockOnServer", this.mmsdGrid.dataProvider[this.mmsdGrid.selectedIndices[i]].movement);
        if (
          this.currentUser.toLowerCase() === lockUser.toLowerCase() || 
          BooleanParser.parseBooleanValue(lockUser)
      ) {
          // Call external interface and convert response to string
          const lockflag1 = ExternalInterface.call(
              "unlockMovementOnServer", 
              this.mmsdGrid.dataProvider[this.mmsdGrid.selectedIndices[i]].movement
          );
      
          // Check if unlock was unsuccessful
          if (!BooleanParser.parseBooleanValue(lockflag1)) {
              this.swtAlert.warning(
                  SwtUtil.getPredictMessage('label.movementCannotBeUnlocked', null)
              );
          }
      }
      }
    }
    if ((ExternalInterface.call('eval', 'inputMethod') != "movementDisplay")) {
      if (this.matchType != "manual") {
        ExternalInterface.call("QueueRefresh");
      } else {
        ExternalInterface.call("close");
      }
    } else {
      ExternalInterface.call("close");
    }

  }

  CalculateAmtDiff() {
    var var_positionLevel: number;
    var var_sign: string;
    var var_amount: number = 0;
    var amount: string = "";
    var var_max: number = 0;
    var var_min: number = 0;
    var diffArr = [];
    var arrayPos = [];
    let amountArr = [];
    let totalSelectedAmount: number = 0;
    this.diffValue.text = "";

    for (var i = 0; i < this.mmsdGrid.dataProvider.length; i++) {
      if (this.mmsdGrid.dataProvider[i].positionlevel)
        var_positionLevel = parseInt(this.mmsdGrid.dataProvider[i].positionlevel);
      else
        var_positionLevel = parseInt(this.mmsdGrid.dataProvider[i].slickgrid_rowcontent.pos.positionlevel);
      arrayPos.push(var_positionLevel);

      if (this.mmsdGrid.dataProvider[i].sign)
      var_sign = this.mmsdGrid.dataProvider[i].sign;
      else
      var_sign = this.mmsdGrid.dataProvider[i].slickgrid_rowcontent.sign.content;

      if (this.mmsdGrid.dataProvider[i].amount)
      amount = this.mmsdGrid.dataProvider[i].amount;
      else
      amount = this.mmsdGrid.dataProvider[i].slickgrid_rowcontent.amount.content;

      if (this.currencyPattern == "currencyPat1") {
        amountArr = amount.split(',');
        amount = "";
        for (var commaCounter = 0; commaCounter < amountArr.length; commaCounter++) {
          amount += amountArr[commaCounter];
        }
      }
      else {
        amountArr = amount.split('.');
        amount = "";
        for (var dotCounter = 0; dotCounter < amountArr.length; dotCounter++) {
          amount += amountArr[dotCounter];
        }
        amount = amount.replace(',', '.');
      }
      var_amount = parseFloat(amount);
      if (var_sign == "D")
        totalSelectedAmount -= var_amount;
      else
        totalSelectedAmount += var_amount;
      switch (var_positionLevel) {
        case 1:
          if (diffArr[0] == undefined) {
            diffArr[0] = 0;
          }
          if (var_sign == "D") {
            diffArr[0] = diffArr[0] - var_amount;
          }
          else {
            diffArr[0] = diffArr[0] + var_amount;

          }
          break;
        case 2:
          if (diffArr[1] == undefined) {
            diffArr[1] = 0;
          }
          if (var_sign == "D") {
            diffArr[1] = diffArr[1] - var_amount;
          }
          else {
            diffArr[1] = diffArr[1] + var_amount;
          }
          break;
        case 3:
          if (diffArr[2] == undefined) {
            diffArr[2] = 0;
          }
          if (var_sign == "D") {
            diffArr[2] = diffArr[2] - var_amount;
          }
          else {
            diffArr[2] = diffArr[2] + var_amount;
          }
          break;
        case 4:
          if (diffArr[3] == undefined) {
            diffArr[3] = 0;
          }
          if (var_sign == "D") {
            diffArr[3] = diffArr[3] - var_amount;
          }
          else {
            diffArr[3] = diffArr[3] + var_amount;
          }
          break;
        case 5:
          if (diffArr[4] == undefined) {
            diffArr[4] = 0;
          }
          if (var_sign == "D") {
            diffArr[4] = diffArr[4] - var_amount;
          }
          else {
            diffArr[4] = diffArr[4] + var_amount;
          }
          break;
        case 6:
          if (diffArr[5] == undefined) {
            diffArr[5] = 0;
          }
          if (var_sign == "D") {
            diffArr[5] = diffArr[5] - var_amount;
          }
          else {
            diffArr[5] = diffArr[5] + var_amount;
          }
          break;
        case 7:
          if (diffArr[6] == undefined) {
            diffArr[6] = 0;
          }
          if (var_sign == "D") {
            diffArr[6] = diffArr[6] - var_amount;

          }
          else {
            diffArr[6] = diffArr[6] + var_amount;

          }
          break;
        case 8:
          if (diffArr[7] == undefined) {
            diffArr[7] = 0;
          }
          if (var_sign == "D") {
            diffArr[7] = diffArr[7] - var_amount;
          }
          else {
            diffArr[7] = diffArr[7] + var_amount;
          }
          break;
        case 9:
          if (diffArr[8] == undefined) {
            diffArr[8] = 0;
          }
          if (var_sign == "D") {
            diffArr[8] = diffArr[8] - var_amount;
          }
          else {
            diffArr[8] = diffArr[8] + var_amount;
          }
      }

      var count = 0;
      var diffPositons: string = "";
      for (var j = 0; j < diffArr.length; j++) {
        if (diffArr[j] != null) {
          if (count == 0) {
            count = -1;
            var_max = diffArr[j];
            var_min = diffArr[j];
          }
          else {
            diffPositons = 'Y';
            if (diffArr[j] < var_min) {
              var_min = diffArr[j];
            }
            if (diffArr[j] > var_max) {
              var_max = diffArr[j];
            }
          }
        }
      }

      var numFormat = { "precision": "", "rounding": "" };
      numFormat.precision = "2";
      numFormat.rounding = "nearest";
      var difference: number = 0;
      var isDifferentPosition: boolean = false;
      for (let i = 0; i < arrayPos.length; i++) {
        if (arrayPos[i] && arrayPos[i + 1] && (arrayPos[i] !== arrayPos[i + 1])) {
          isDifferentPosition = true;
          break;
        }
      }
      difference = var_max - var_min;
      if (difference == 0) {
        if (isDifferentPosition) {
          if(this.currencyPattern=='currencyPat1')
          this.diffValue.text = "0.00";
          else
          this.diffValue.text = "0,00";
        }
        else {
          this.diffValue.text = "";
        }
      }
      else {
        var expandDiffAmt: string = this.addZeroes(difference.toString()); //numFormat.format(difference);
        if (expandDiffAmt.indexOf(".") == -1) {
          expandDiffAmt += "00";
        }
        else {
          var expandDiffArr = [];
          expandDiffArr = expandDiffAmt.split(".");
          expandDiffAmt = expandDiffArr[0] + expandDiffArr[1]
        }
        if (isDifferentPosition) {
          this.diffValue.text = ExternalInterface.call("expandAmtDifference", expandDiffAmt, this.currencyPattern, this.currencyCode.text);
        } else {
          this.diffValue.text = "";
        }

      }

    }
  }

  addZeroes(num) {
    const dec = num.split('.')[1]
    const len = 2; // dec && dec.length > 2 ? dec.length : 2;
    return Number(num).toFixed(len)
  }

}


// Define lazy loading routes
const routes: Routes = [
  { path: '', component: MovementMatchSummaryDisplay }
];
export const routing: ModuleWithProviders = RouterModule.forChild(routes);
// Define an NgModule that will be loaded by app module
@NgModule({
  imports: [routing, SwtToolBoxModule],
  declarations: [MovementMatchSummaryDisplay],
  entryComponents: []
})
export class MovementMatchSummaryDisplayModule {}
