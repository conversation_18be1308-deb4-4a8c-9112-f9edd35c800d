<SwtModule #swtModule (creationComplete)='onLoad()' width="100%" height="100%">
  <VBox width='100%' height='100%' paddingLeft="5" paddingRight="5" paddingBottom="5" paddingTop="5">
      <Grid width="100%" height="93%" paddingLeft="5" paddingBottom="10">

        <GridRow width="100%" height="100%">
          <SwtCanvas #rateGridContainer id="rateGridContainer" styleName="canvasWithGreyBorder" width="100%"
          height="100%" border="false"></SwtCanvas>
        </GridRow>

      </Grid>

    <SwtCanvas width="100%" height="7%">
      <HBox width="100%">
        <HBox paddingLeft="5" width="90%">
          <SwtButton [buttonMode]="true" id="addButton" #addButton (click)="openAddChildScreen()">
          </SwtButton>
          <SwtButton [buttonMode]="true" id="changeButton" #changeButton enabled ="false"
          (click)="openChangeChildScreen()">
          </SwtButton>
          <SwtButton [buttonMode]="true" id="deleteButton" #deleteButton enabled ="false"
          (click)="deleteAcctInterestRateSweep()">
          </SwtButton>
          <SwtButton [buttonMode]="true" id="closeButton" #closeButton (click)="closeHandler()">
          </SwtButton>
        </HBox>
       <HBox width="10%" horizontalAlign="right" paddingLeft="5">
          <SwtLoadingImage #loadingImage></SwtLoadingImage>
        </HBox>
    
      </HBox>
    </SwtCanvas>
  </VBox>
</SwtModule>
