<SwtModule #swtModule (creationComplete)='onLoad()' width="100%" height="100%">
  <VBox width='100%' height='100%' paddingLeft="5" paddingRight="5" paddingBottom="5" paddingTop="5">
    <SwtFieldSet id="fieldSet" #fieldSet style="padding-bottom: 5px; height: 100px; width: 100%; color:blue;">
      <Grid width="100%" height="100%" paddingLeft="5">
        <GridRow width="100%" height="25">
          <GridItem width="120">
            <SwtLabel id="dateLbl" #dateLbl></SwtLabel>
          </GridItem>

          <SwtDateField id="dateField" #dateField width="70"
          (change)="validateDateField(dateField)"></SwtDateField>
        </GridRow>

        <GridRow width="100%" height="25">
          <GridItem width="120">
            <SwtLabel id="crMarginLbl" #crMarginLbl></SwtLabel>
          </GridItem>
          <GridItem paddingRight="20">
            <SwtTextInput id='crMarginTxt' #crMarginTxt maxChars="20" width="200"
            (focusOut)="validateFieldAccRate()" textAlign="right"></SwtTextInput>
          </GridItem>
        </GridRow>

        <GridRow width="100%" height="25">
          <GridItem width="120">
            <SwtLabel id="drMarginLbl" #drMarginLbl></SwtLabel>
          </GridItem>
          <GridItem paddingRight="20">
            <SwtTextInput id='drMarginTxt' #drMarginTxt maxChars="20" width="200"
            (focusOut)="validateFieldAccDraft()" textAlign="right"></SwtTextInput>
          </GridItem>
        </GridRow>

      </Grid>
    </SwtFieldSet>

    <SwtCanvas width="100%" height="35">
      <HBox width="100%">
        <HBox paddingLeft="5" width="90%">
          <SwtButton [buttonMode]="true" id="okButton" #okButton (click)="saveHandler()">
          </SwtButton>
          <SwtButton [buttonMode]="true" id="closeButton" #closeButton (click)="closeHandler()">
          </SwtButton>
        </HBox>
       <!--<HBox width="10%" horizontalAlign="right" paddingLeft="5">
          <SwtLoadingImage #loadingImage></SwtLoadingImage>
        </HBox>-->
    
      </HBox>
    </SwtCanvas>
  </VBox>
</SwtModule>