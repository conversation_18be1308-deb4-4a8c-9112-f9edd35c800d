<SwtModule #swtModule (creationComplete)='onLoad()' width="100%" height="100%">
  <VBox width='100%' height='100%' paddingLeft="5" paddingRight="5" paddingBottom="5">
    <SwtFieldSet id="generalFieldSet" #generalFieldSet style="height: 250px; width: 100%; color:blue;">
      <Grid width="100%" height="100%"  paddingLeft="5">
        <GridRow width="100%" height="8%">
          <GridItem width="300">
            <GridItem width="150">
              <SwtLabel id="acctLevelLbl" #acctLevelLbl></SwtLabel>
            </GridItem>
            <GridItem width="380">
              <SwtRadioButtonGroup #acctLevGrp id="acctLevGrp" align="horizontal" (change) ="enableDisableCombo()" >
                <SwtRadioItem value="M" width="80" groupName="acctLevGrp" selected="true" id="main" #main>
                </SwtRadioItem>
                <SwtRadioItem value="S" width="80" groupName="acctLevGrp" id="sub" #sub></SwtRadioItem>
              </SwtRadioButtonGroup>
            </GridItem>
            <GridItem>
              <SwtButton [buttonMode]="true" id="subButton" #subButton (click)="subAccounts()">
              </SwtButton>
            </GridItem>
          </GridItem>
        </GridRow>

        <GridRow width="100%" height="9%">
          <GridItem width="300">
            <GridItem width="150">
              <SwtLabel id="mainAcctLbl" #mainAcctLbl></SwtLabel>
            </GridItem>
            <GridItem>
              <SwtComboBox id="mainAccCombo" #mainAccCombo width="200" dataLabel="mainAcctList"> </SwtComboBox>
            </GridItem>
            <GridItem paddingLeft="15">
              <SwtLabel id='mainAcctDesc' #mainAcctDesc width="200"></SwtLabel>
            </GridItem>
          </GridItem>
        </GridRow>

        <GridRow width="100%" height="24%">
          <GridItem width="300">
            <GridItem width="150">
              <SwtLabel id="autoSweepLbl" #autoSweepLbl></SwtLabel>
            </GridItem>
            <GridItem width="190">
              <SwtRadioButtonGroup #autoSweepGrp id="autoSweepGrp" align="vertical" (change)="callShowSweeping()" >
                <SwtRadioItem value="S" width="150" groupName="autoSweepGrp"  id="stp" #stp>
                </SwtRadioItem>
                <SwtRadioItem value="U" width="150" groupName="autoSweepGrp" id="submit" #submit></SwtRadioItem>
                <SwtRadioItem value="N" width="150" groupName="autoSweepGrp" selected="true" id="notIncl" #notIncl></SwtRadioItem>
              </SwtRadioButtonGroup>
            </GridItem>
            <GridItem>
                <Grid width="100%" height="100%" paddingLeft="5">
                  <GridRow width="100%" height="5%">
                    <HBox horizontalAlign="right"  paddingRight="10" width="220">
                      <SwtLabel id="manSweepLbl" #manSweepLbl></SwtLabel>
                    </HBox>
                    <GridItem width="88%">
                      <SwtCheckBox id="manSweepChk" #manSweepChk selected="false"
                      (change)="callShowSweeping()"></SwtCheckBox>

                    </GridItem>
                  </GridRow>
                  <GridRow width="100%" height="5%">
                    <HBox horizontalAlign="right"  paddingRight="10" width="220">
                      <SwtLabel id="useTimingOfAcctLbl" #useTimingOfAcctLbl></SwtLabel>
                    </HBox>
                    <GridItem width="88%">
                      <SwtCheckBox id="useTimingOfAcctChk" #useTimingOfAcctChk selected="false"></SwtCheckBox>

                    </GridItem>
                  </GridRow>
                </Grid>
            </GridItem>
          </GridItem>
        </GridRow>


        <GridRow width="100%" height="10%">
          <GridItem width="395">
            <GridItem width="150">
              <SwtLabel id="earliestSweepTimeLbl" #earliestSweepTimeLbl></SwtLabel>
            </GridItem>
          <GridItem>
            <SwtTextInput id='earliestSweepTimeTxt' #earliestSweepTimeTxt 
            (focusOut)="validateTime(earliestSweepTimeTxt)"  maxChars="5" width="50"></SwtTextInput>
          </GridItem>
          </GridItem>

          <GridItem width="300">
            <HBox horizontalAlign="right"  paddingRight="10" width="170">
              <SwtLabel id="cutOfTimeLbl" #cutOfTimeLbl></SwtLabel>
            </HBox>
          <GridItem>
            <SwtTextInput id='cutOfTimeTxt' #cutOfTimeTxt 
            (focusOut)="validateTime(cutOfTimeTxt)"   maxChars="5" width="50"></SwtTextInput>
          </GridItem>
          </GridItem>

        </GridRow>

        <GridRow width="100%" height="11%">
          <GridItem width="395">
            <GridItem width="150">
              <SwtLabel id="sweepDaysLbl" #sweepDaysLbl></SwtLabel>
            </GridItem>
          <GridItem>
            <SwtTextInput id='sweepDaysTxt' #sweepDaysTxt maxChars="3" width="35"
            (focusOut)="validateField(sweepDaysTxt,'swptime','numberPatAll',99,-99)"></SwtTextInput>
          </GridItem>
          </GridItem>

          <GridItem width="300">
            <HBox horizontalAlign="right"  paddingRight="10" width="170">
              <SwtLabel id="sweepCodeLbl" #sweepCodeLbl></SwtLabel>
            </HBox>
          <GridItem>
            <SwtTextInput id='sweepCodeTxt' #sweepCodeTxt maxChars="3" width="50"
            (focusOut)="validateField(sweepCodeTxt,'sweepCode','alphaNumPat','','')"></SwtTextInput>
          </GridItem>
          </GridItem>

        </GridRow>

          <GridRow width="100%" height="10%">
            <GridItem width="395">
              <GridItem width="150">
                <SwtLabel id="sweepFrmBalLbl" #sweepFrmBalLbl></SwtLabel>
              </GridItem>
            <GridItem>
              <SwtComboBox id="sweepFrmBalCombo" #sweepFrmBalCombo width="100" dataLabel="sweepFrBalList">
                </SwtComboBox>
              </GridItem>
            </GridItem>
  
            <GridItem width="300">
              <HBox horizontalAlign="right"  paddingRight="10" width="170">
                <SwtLabel id="bookLbl" #bookLbl></SwtLabel>
              </HBox>
            <GridItem>
              <SwtComboBox id="bookCombo" #bookCombo width="140" 
              (change)="refreshBookTxt()" dataLabel="bookCodeList">
              </SwtComboBox>
            </GridItem>
            <GridItem paddingLeft="15">
              <SwtLabel id='bookDesc' #bookDesc width="200" fontWeight="normal"></SwtLabel>
            </GridItem>
            </GridItem>
  
          </GridRow>


          <GridRow width="100%" height="10%">
            <GridItem width="395">
              <GridItem width="150">
                <SwtLabel id="defaultTargetBalLbl" #defaultTargetBalLbl></SwtLabel>
              </GridItem>
            <GridItem>
              <SwtTextInput id='defaultTargetBalTxt' #defaultTargetBalTxt maxChars="20" 
              (focusOut)="validateReserve(defaultTargetBalTxt)" textAlign="right" width="200"></SwtTextInput>
            </GridItem>
            <GridItem paddingLeft="5">
              <SwtComboBox id="defaultTargetBalCombo" #defaultTargetBalCombo width="50" dataLabel="targetSignList">
              </SwtComboBox>
            </GridItem>
            </GridItem>
  
            <GridItem width="300">
              <HBox horizontalAlign="right"  paddingRight="10" width="170">
                <SwtLabel id="interLbl" #interLbl></SwtLabel>
              </HBox>
            <GridItem>
              <SwtTextInput id='interTxt' #interTxt maxChars="20" width="300"></SwtTextInput>
            </GridItem>
            </GridItem>
  
          </GridRow>

          <GridRow width="100%" height="10%">
            <GridItem width="395">
              <GridItem width="150">
                <SwtLabel id="maxAmtLbl" #maxAmtLbl></SwtLabel>
              </GridItem>
            <GridItem>
              <SwtTextInput id='maxAmtTxt' #maxAmtTxt maxChars="20" width="200"
              (focusOut)="validateReserve(maxAmtTxt)" textAlign="right"></SwtTextInput>
            </GridItem>
            </GridItem>
            <GridItem width="300">
              <HBox horizontalAlign="right"  paddingRight="10" width="170">
                <SwtLabel id="defaultSettMethodLbl" #defaultSettMethodLbl></SwtLabel>
              </HBox>
            <GridItem>
              <SwtComboBox id="defaultSettMethodCombo" #defaultSettMethodCombo width="140" dataLabel="settMethodList">
              </SwtComboBox>
            </GridItem>
            </GridItem>

            </GridRow>

            <GridRow width="100%" height="10%">
              <GridItem width="395">
                <GridItem width="150">
                  <SwtLabel id="minAmtLbl" #minAmtLbl></SwtLabel>
                </GridItem>
              <GridItem>
                <SwtTextInput id='minAmtTxt' #minAmtTxt maxChars="20" width="200"
                (focusOut)="validateReserve(minAmtTxt)" textAlign="right"></SwtTextInput>
              </GridItem>
              </GridItem>
              <GridItem width="300">
                <HBox horizontalAlign="right"  paddingRight="10" width="170">
                  <SwtLabel id="assAcctLbl" #assAcctLbl></SwtLabel>
                </HBox>
              <GridItem width="70">
                <SwtTextInput id='assAcctTxt' #assAcctTxt maxChars="20" width="50" enabled="false"></SwtTextInput>
  
              </GridItem>
  
              <GridItem >
                <SwtButton [buttonMode]="true" id="maintButton1" #maintButton1 enabled="true" (click)="buildSpecificAcctSweepBalGrp()">
                </SwtButton>
              </GridItem>
  
              </GridItem>
  
              </GridRow>

      </Grid>
    </SwtFieldSet>








    <SwtFieldSet id="eodSweepFieldSet" #eodSweepFieldSet style=" height: 140px; width: 100%; color:blue;">
      <Grid width="100%" height="100%"  paddingLeft="5">
          <GridRow width="100%" height="81%" width="100%">
            <SwtCanvas #sweepGridContainer id="sweepGridContainer" styleName="canvasWithGreyBorder" width="100%"
            height="100%" paddingTop="3" border="false"></SwtCanvas>
          </GridRow>
        <GridRow width="100%" height="10%" paddingTop="3">
          <GridItem width="90%">
            <HBox width="100%">
            <SwtButton [buttonMode]="true" id="addButton" #addButton enabled ="true" (click)="buildAccsSweepsScheduleDetails('add')">
            </SwtButton>
            <SwtButton [buttonMode]="true" id="changeButton" #changeButton enabled ="false" (click)="buildAccsSweepsScheduleDetails('change')">
            </SwtButton>
            <SwtButton [buttonMode]="true" id="viewButton" #viewButton enabled ="false" (click)="buildAccsSweepsScheduleDetails('view')">
            </SwtButton>
            <SwtButton [buttonMode]="true" id="deleteButton" #deleteButton enabled ="false" (click)="deleteAccountScheduleSweep()">
            </SwtButton>
          </HBox>
          </GridItem>

          <GridItem width="10%">
            <HBox width="100%" horizontalAlign="right">
            <GridItem width="150">
              <SwtLabel id="usedInLbl" #usedInLbl></SwtLabel>
          </GridItem>

          <GridItem paddingLeft="10">
            <SwtTextInput id='usedInTxt' #usedInTxt maxChars="20" width="50" enabled="false"></SwtTextInput>
          </GridItem>

          <GridItem paddingLeft="10">
            <SwtButton [buttonMode]="true" id="showButton" #showButton (click)="buildAccsSweepsSchedule()" enabled ="true">
            </SwtButton>

            </GridItem>
            </HBox>
          </GridItem>

        </GridRow>

  </Grid>
  </SwtFieldSet>

      <SwtFieldSet id="formats" #formats style="height: 100px; width: 100%; color:blue;">
      <Grid width="100%" height="100%"  paddingLeft="5">
      <GridRow width="100%" height="20%">
        <GridItem width="85%">
          <GridItem width="140">
            <SwtLabel id="defaultLbl" #defaultLbl></SwtLabel>
          </GridItem>
          <GridItem width="160">
            <SwtLabel id="creditLbl" #creditLbl></SwtLabel>
          </GridItem>
          <GridItem width="120">
            <SwtLabel id="debitLbl" #debitLbl></SwtLabel>
          </GridItem>
        </GridItem>

        <GridItem width="10%">
        <HBox width="100%" horizontalAlign="right">
          
          <GridItem width="120">
            <SwtLabel id="acctSpecLbl" #acctSpecLbl></SwtLabel>
          </GridItem>
          <GridItem width="70">
            <SwtTextInput id='acctSpecTxt' #acctSpecTxt maxChars="20" width="60" enabled="false"></SwtTextInput>
          </GridItem>
          <GridItem >
              <SwtButton [buttonMode]="true" id="maintButton" #maintButton (click)="buildSpecificAccount()">
              </SwtButton>
            </GridItem>
          </HBox>
        </GridItem>

      </GridRow>


      <GridRow width="100%" height="26%">
        <GridItem width="300">
          <GridItem width="140">
            <SwtLabel id="internalLbl" #internalLbl></SwtLabel>
          </GridItem>

          <GridItem width="120">
          <SwtComboBox id="interCredCombo" #interCredCombo width="150" dataLabel="formatList">
          </SwtComboBox>
          </GridItem>

        </GridItem>

        <GridItem width="300">
          <GridItem>
          <SwtComboBox id="interDebitCombo" #interDebitCombo width="150" dataLabel="formatList">
          </SwtComboBox>
          </GridItem>
        </GridItem>

        </GridRow>


        <GridRow width="100%" height="26%">
          <GridItem width="300">
            <GridItem width="140">
              <SwtLabel id="externalLbl" #externalLbl></SwtLabel>
            </GridItem>
  
            <GridItem width="120">
            <SwtComboBox id="extCredCombo" #extCredCombo width="150" dataLabel="formatList">
            </SwtComboBox>
            </GridItem>
  
          </GridItem>
  
          <GridItem width="300">
            <GridItem>
            <SwtComboBox id="extDebitCombo" #extDebitCombo width="150" dataLabel="formatList">
            </SwtComboBox>
            </GridItem>
          </GridItem>
  
          </GridRow>

          <GridRow width="100%" height="26%">
            <GridItem width="300">
              <GridItem width="140">
                <SwtLabel id="extInterLbl" #extInterLbl></SwtLabel>
              </GridItem>
    
              <GridItem width="120">
              <SwtComboBox id="extInterCredCombo" #extInterCredCombo width="150" dataLabel="formatList">
              </SwtComboBox>
              </GridItem>
    
            </GridItem>
    
            <GridItem width="300">
              <GridItem>
              <SwtComboBox id="extInterDebitCombo" #extInterDebitCombo width="150" dataLabel="formatList">
              </SwtComboBox>
              </GridItem>
            </GridItem>
    
            </GridRow>

</Grid>
</SwtFieldSet>

  </VBox>
</SwtModule>