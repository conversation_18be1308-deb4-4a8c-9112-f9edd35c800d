import { Component, OnInit, ViewChild, ElementRef } from '@angular/core';
import { SwtLabel, SwtComboBox, SwtRadioButtonGroup, SwtRadioItem, SwtTextInput, SwtCheckBox, SwtFieldSet, SwtAlert, JSONReader, HTTPComms, SwtUtil, CommonService, SwtButton, SwtModule, SwtCommonGrid, SwtCanvas, ExternalInterface, Alert, SwtGroupedCommonGrid, Logger } from 'swt-tool-box';
declare function validateCurrencyPlaces(strField, strPat, currCode): any;
declare  function validateFormatTime(strField): any;
declare function validateField(strField, strLabel, strPat, maxValue, minValue): any;
@Component({
  selector: 'app-sweeping',
  templateUrl: './Sweeping.html',
  styleUrls: ['./Sweeping.css']
})
export class Sweeping extends SwtModule implements OnInit {

  /***********SwtCanvas***********/
  @ViewChild('sweepGridContainer') sweepGridContainer: SwtCanvas;

  /***********SwtLabel***********/
  @ViewChild('acctLevelLbl') acctLevelLbl: SwtLabel;
  @ViewChild('mainAcctLbl') mainAcctLbl: SwtLabel;
  @ViewChild('autoSweepLbl') autoSweepLbl: SwtLabel;
  @ViewChild('useTimingOfAcctLbl') useTimingOfAcctLbl: SwtLabel;
  @ViewChild('manSweepLbl') manSweepLbl: SwtLabel;

  @ViewChild('earliestSweepTimeLbl') earliestSweepTimeLbl: SwtLabel;
  @ViewChild('defaultTargetBalLbl') defaultTargetBalLbl: SwtLabel;
  @ViewChild('defaultSettMethodLbl') defaultSettMethodLbl: SwtLabel;
  @ViewChild('assAcctLbl') assAcctLbl: SwtLabel;
  @ViewChild('mainAcctDesc') mainAcctDesc: SwtLabel;
  @ViewChild('sweepDaysLbl') sweepDaysLbl: SwtLabel;
  @ViewChild('sweepCodeLbl') sweepCodeLbl: SwtLabel;
  @ViewChild('cutOfTimeLbl') cutOfTimeLbl: SwtLabel;
  @ViewChild('minAmtLbl') minAmtLbl: SwtLabel;
  @ViewChild('maxAmtLbl') maxAmtLbl: SwtLabel;
  @ViewChild('bookLbl') bookLbl: SwtLabel;
  @ViewChild('bookDesc') bookDesc: SwtTextInput;
  @ViewChild('sweepFrmBalLbl') sweepFrmBalLbl: SwtLabel;
  @ViewChild('interLbl') interLbl: SwtLabel;
  @ViewChild('defaultLbl') defaultLbl: SwtLabel;
  @ViewChild('creditLbl') creditLbl: SwtLabel;
  @ViewChild('debitLbl') debitLbl: SwtLabel;
  @ViewChild('acctSpecLbl') acctSpecLbl: SwtLabel;
  @ViewChild('internalLbl') internalLbl: SwtLabel;
  @ViewChild('externalLbl') externalLbl: SwtLabel;
  @ViewChild('extInterLbl') extInterLbl: SwtLabel;
  @ViewChild('usedInLbl') usedInLbl: SwtLabel;



  /***********SwtComboBox***********/
  @ViewChild('mainAccCombo') mainAccCombo: SwtComboBox;
  @ViewChild('bookCombo') bookCombo: SwtComboBox;

  @ViewChild('defaultSettMethodCombo') defaultSettMethodCombo: SwtComboBox;
  @ViewChild('defaultTargetBalCombo') defaultTargetBalCombo: SwtComboBox;

  @ViewChild('sweepFrmBalCombo') sweepFrmBalCombo: SwtComboBox;
  @ViewChild('interCredCombo') interCredCombo: SwtComboBox;
  @ViewChild('interDebitCombo') interDebitCombo: SwtComboBox;
  @ViewChild('extCredCombo') extCredCombo: SwtComboBox;
  @ViewChild('extDebitCombo') extDebitCombo: SwtComboBox;
  @ViewChild('extInterCredCombo') extInterCredCombo: SwtComboBox;
  @ViewChild('extInterDebitCombo') extInterDebitCombo: SwtComboBox;

  /***********SwtRadioButtonGroup***********/
  @ViewChild('acctLevGrp') acctLevGrp: SwtRadioButtonGroup;
  @ViewChild('main') main: SwtRadioItem;
  @ViewChild('sub') sub: SwtRadioItem;
  @ViewChild('autoSweepGrp') autoSweepGrp: SwtRadioButtonGroup;
  @ViewChild('stp') stp: SwtRadioItem;
  @ViewChild('submit') submit: SwtRadioItem;
  @ViewChild('notIncl') notIncl: SwtRadioItem;

  /***********SwtTextInput***********/

  @ViewChild('earliestSweepTimeTxt') earliestSweepTimeTxt: SwtTextInput;
  @ViewChild('defaultTargetBalTxt') defaultTargetBalTxt: SwtTextInput;
  @ViewChild('assAcctTxt') assAcctTxt: SwtTextInput;

  @ViewChild('sweepDaysTxt') sweepDaysTxt: SwtTextInput;
  @ViewChild('sweepCodeTxt') sweepCodeTxt: SwtTextInput;
  @ViewChild('cutOfTimeTxt') cutOfTimeTxt: SwtTextInput;
  @ViewChild('minAmtTxt') minAmtTxt: SwtTextInput;
  @ViewChild('maxAmtTxt') maxAmtTxt: SwtTextInput;
  @ViewChild('interTxt') interTxt: SwtTextInput;
  @ViewChild('acctSpecTxt') acctSpecTxt: SwtTextInput;
  @ViewChild('usedInTxt') usedInTxt: SwtTextInput;

  /***********SwtCheckBox***********/
  @ViewChild('useTimingOfAcctChk') useTimingOfAcctChk: SwtCheckBox;
  @ViewChild('manSweepChk') manSweepChk: SwtCheckBox;

  /***********SwtFieldSet***********/
  @ViewChild('generalFieldSet') generalFieldSet: SwtFieldSet;
  @ViewChild('eodSweepFieldSet') eodSweepFieldSet: SwtFieldSet;
  @ViewChild('intraDaySweepFieldSet') intraDaySweepFieldSet: SwtFieldSet;
  @ViewChild('formats') formats: SwtFieldSet;
  /***********SwtButton***********/
  @ViewChild('subButton') subButton: SwtButton;

  @ViewChild('addButton') addButton: SwtButton;
  @ViewChild('changeButton') changeButton: SwtButton;
  @ViewChild('viewButton') viewButton: SwtButton;
  @ViewChild('deleteButton') deleteButton: SwtButton;
  @ViewChild('showButton') showButton: SwtButton;

  @ViewChild('maintButton') maintButton: SwtButton;
  @ViewChild('maintButton1') maintButton1: SwtButton;

  private logger: Logger = null;
  private swtAlert: SwtAlert;
  private menuAccessId;
  /**
   * Data Objects
   **/
  public jsonReader: JSONReader = new JSONReader();
  public lastRecievedJSON;
  public prevRecievedJSON;
  public initReceivedJSON;
  /**
    * Communication Objects
    **/
  public inputData = new HTTPComms(this.commonService);
  public baseURL: string = SwtUtil.getBaseURL();
  private actionMethod: string = "";
  private actionPath: string = "";
  private requestParams = [];
  private _invalidComms: string;
  /**
   * Variables to hold the grid
   **/
  public sweepGrid: SwtGroupedCommonGrid;

  private entityId = "";
  private accountId = "";
  private selectedCurrency = "";
  private subAccountsPresent = "";
  private sweepScheduledId = "";


  constructor(private commonService: CommonService, private element: ElementRef) {
    super(element, commonService);
    this.logger = new Logger('Sweeping', this.commonService.httpclient);
    this.swtAlert = new SwtAlert(commonService);
  }

  ngOnInit() {
    this.sweepGrid = <SwtGroupedCommonGrid>this.sweepGridContainer.addChild(SwtGroupedCommonGrid);

    //label
    this.acctLevelLbl.text = SwtUtil.getPredictMessage('acclevel', null);
    this.mainAcctLbl.text = SwtUtil.getPredictMessage('mainacctId', null);
    this.autoSweepLbl.text = SwtUtil.getPredictMessage('autoswpswitch', null);
    this.useTimingOfAcctLbl.text = SwtUtil.getPredictMessage('subacctim', null);
    this.manSweepLbl.text = SwtUtil.getPredictMessage('tooltip.manualSweep', null);


    this.earliestSweepTimeLbl.text = SwtUtil.getPredictMessage('accountmaintenanceadd.earliestSweepTime', null);
    this.defaultTargetBalLbl.text = SwtUtil.getPredictMessage('accountmaintenanceadd.defaulttargetBalance', null);
    this.defaultSettMethodLbl.text = SwtUtil.getPredictMessage('accountmaintenanceadd.defaultSettMethod', null);
    this.assAcctLbl.text = SwtUtil.getPredictMessage('accountmaintenanceadd.assocForSweepBalnce', null);


    this.sweepDaysLbl.text = SwtUtil.getPredictMessage('swpdays', null);
    this.sweepCodeLbl.text = SwtUtil.getPredictMessage('sweepCode', null);
    this.cutOfTimeLbl.text = SwtUtil.getPredictMessage('cutofftime', null);
    this.maxAmtLbl.text = SwtUtil.getPredictMessage('maxswpamnt', null);
    this.minAmtLbl.text = SwtUtil.getPredictMessage('minswpamnt', null);
    this.bookLbl.text = SwtUtil.getPredictMessage('bookcode', null);
    this.sweepFrmBalLbl.text = SwtUtil.getPredictMessage('SweepFromBalance', null);
    this.interLbl.text = SwtUtil.getPredictMessage('intermediary', null);
    this.defaultLbl.text = SwtUtil.getPredictMessage('format.default', null);
    this.creditLbl.text = SwtUtil.getPredictMessage('format.Credit', null);
    this.debitLbl.text = SwtUtil.getPredictMessage('format.Debit', null);
    this.acctSpecLbl.text = SwtUtil.getPredictMessage('format.accountSpecific', null);
    this.internalLbl.text = SwtUtil.getPredictMessage('label.format.Int', null);
    this.externalLbl.text = SwtUtil.getPredictMessage('label.format.Ext', null);
    this.extInterLbl.text = SwtUtil.getPredictMessage('fomat.cdExtInt', null);
    this.usedInLbl.text = SwtUtil.getPredictMessage('account.schedSweep.usedInOther', null);

    //text input
    this.earliestSweepTimeTxt.toolTip = SwtUtil.getPredictMessage('tooltip.earliestSweepTime', null);
    this.sweepDaysTxt.toolTip = SwtUtil.getPredictMessage('swpdays', null);
    this.sweepCodeTxt.toolTip = SwtUtil.getPredictMessage('sweepCode', null);
    this.cutOfTimeTxt.toolTip = SwtUtil.getPredictMessage('tooltip.cutOffTime', null);
    this.defaultTargetBalTxt.toolTip= SwtUtil.getPredictMessage('tooltip.tarBalAmount', null);
    this.interTxt.toolTip = SwtUtil.getPredictMessage('intermediary', null);
    this.minAmtTxt.toolTip = SwtUtil.getPredictMessage('tooltip.minSweepAmount', null);
    this.maxAmtTxt.toolTip = SwtUtil.getPredictMessage('tooltip.maxSweepAmount', null);
    this.usedInTxt.toolTip = SwtUtil.getPredictMessage('tooltip.usedInOtherSchedulers', null);
    this.assAcctTxt.toolTip = SwtUtil.getPredictMessage('accountmaintenanceadd.assocForSweepBalnce', null);
    this.acctSpecTxt.toolTip = SwtUtil.getPredictMessage('format.accountSpecific', null);
  

    //combo
    this.mainAccCombo.toolTip = SwtUtil.getPredictMessage('tooltip.mainAcctID', null);
    this.bookCombo.toolTip = SwtUtil.getPredictMessage('tooltip.bookCode', null);
    this.sweepFrmBalCombo.toolTip = SwtUtil.getPredictMessage('SweepFromBalance', null);
    this.defaultSettMethodCombo.toolTip = SwtUtil.getPredictMessage('accountmaintenanceadd.defaultSettleMethodTooltip', null);
    this.defaultTargetBalCombo.toolTip = SwtUtil.getPredictMessage('tooltip.tarBalSign', null);
    this.interCredCombo.toolTip = SwtUtil.getPredictMessage('tooltip.creditIntMsg', null);
    this.interDebitCombo.toolTip = SwtUtil.getPredictMessage('tooltip.debitIntMsg', null);
    this.extCredCombo.toolTip = SwtUtil.getPredictMessage('tooltip.creditExtMsg', null);
    this.extDebitCombo.toolTip = SwtUtil.getPredictMessage('tooltip.debitExtMsg', null);
    this.extInterCredCombo.toolTip = SwtUtil.getPredictMessage('tooltip.creditInterMsg', null);
    this.extInterDebitCombo.toolTip = SwtUtil.getPredictMessage('tooltip.debitInterMsg', null);

    //checkBox
    this.useTimingOfAcctChk.toolTip = SwtUtil.getPredictMessage('subacctim.tooltip', null);
    this.manSweepChk.toolTip = SwtUtil.getPredictMessage('tooltip.manSweep', null);

    //fieldSet
    this.generalFieldSet.legendText = SwtUtil.getPredictMessage('fielset.genDeatils', null);
    this.eodSweepFieldSet.legendText = SwtUtil.getPredictMessage('accountmaintenance.EODSweeping', null);
    this.formats.legendText = SwtUtil.getPredictMessage('accountmaintenance.Format', null);

    //radio buttons
    this.main.label = SwtUtil.getPredictMessage('tooltip.main', null);
    this.sub.label = SwtUtil.getPredictMessage('tooltip.sub', null);
    this.stp.label = SwtUtil.getPredictMessage('tooltip.stp', null);
    this.submit.label = SwtUtil.getPredictMessage('tooltip.submit', null);
    this.notIncl.label = SwtUtil.getPredictMessage('notincl', null);
    this.main.toolTip = SwtUtil.getPredictMessage('tooltip.main', null);
    this.sub.toolTip = SwtUtil.getPredictMessage('tooltip.sub', null);
    this.stp.toolTip = SwtUtil.getPredictMessage('tooltip.stp', null);
    this.submit.toolTip = SwtUtil.getPredictMessage('tooltip.submit', null);
    this.notIncl.toolTip = SwtUtil.getPredictMessage('tooltip.notincluded', null);

    //Buttons
    this.subButton.label = SwtUtil.getPredictMessage('button.sub', null);
    this.subButton.toolTip = SwtUtil.getPredictMessage('tooltip.subAc', null);

    this.addButton.label = SwtUtil.getPredictMessage('button.add', null);
    this.addButton.toolTip = SwtUtil.getPredictMessage('tooltip.add', null);
    this.viewButton.label = SwtUtil.getPredictMessage('button.view', null);
    this.viewButton.toolTip = SwtUtil.getPredictMessage('tooltip.view', null);
    this.changeButton.label = SwtUtil.getPredictMessage('button.change', null);
    this.changeButton.toolTip = SwtUtil.getPredictMessage('tooltip.change', null);
    this.deleteButton.label = SwtUtil.getPredictMessage('button.delete', null);
    this.deleteButton.toolTip = SwtUtil.getPredictMessage('tooltip.delete', null);
    this.showButton.label = SwtUtil.getPredictMessage('account.schedSweep.show', null);
    this.showButton.toolTip = SwtUtil.getPredictMessage('account.schedSweep.show', null);

    this.maintButton.label = SwtUtil.getPredictMessage('button.maintain', null);
    this.maintButton.toolTip = SwtUtil.getPredictMessage('button.maintain', null);

    this.maintButton1.label = SwtUtil.getPredictMessage('button.maintain', null);
    this.maintButton1.toolTip = SwtUtil.getPredictMessage('button.maintain', null);

  }

  onLoad() {
    //Variable for errorLocation
    let errorLocation = 0;
    try {
      this.sweepGrid.onRowClick = (event) => {
        this.cellClickEventHandler(event);
      };
    } catch (error) {
      // log the error in ERROR LOG
      this.logger.error('method [onLoad] - error: ', error, 'errorLocation: ', errorLocation);
      SwtUtil.logError(error, SwtUtil.PREDICT_MODULE_ID, 'Sweeping.ts', "onLoad", errorLocation);
    }
  }

  /** This method is called when selecting a row from bottom grid**/

  cellClickEventHandler(event): void {
    //Variable for errorLocation
    let errorLocation = 0;
    try {
      if (this.sweepGrid.selectedIndex >= 0) {
        errorLocation = 10;
        var index = this.sweepGrid.selectedIndex;
        errorLocation = 20;
        this.sweepScheduledId = this.sweepGrid.selectedItem.sweepScheduleId.content ?
        this.sweepGrid.selectedItem.sweepScheduleId.content:this.sweepGrid.selectedItem.uniqueId.content;
        errorLocation = 30;
        //this.entityId = this.sweepGrid.selectedItem.entity.content;
        //this.accountId = this.sweepGrid.selectedItem.accountID.content;
        this.changeButton.enabled = true;
        this.changeButton.buttonMode = true;
        this.viewButton.enabled = true;
        this.viewButton.buttonMode = true;
        this.deleteButton.enabled = true;
        this.deleteButton.buttonMode = true;
      } else {
        errorLocation = 40;
        this.changeButton.enabled = false;
        this.changeButton.buttonMode = false;
        this.viewButton.enabled = false;
        this.viewButton.buttonMode = false;
        this.deleteButton.enabled = false;
        this.deleteButton.buttonMode = false;
      }
    } catch (error) {
      // log the error in ERROR LOG
      this.logger.error('method [cellClickEventHandler] - error: ', error, 'errorLocation: ', errorLocation);
      SwtUtil.logError(error, SwtUtil.PREDICT_MODULE_ID, 'Sweeping.ts', "cellClickEventHandler", errorLocation);
    }
  }

  refreshBookTxt() {
    //Variable for errorLocation
    let errorLocation = 0;
    try {
      this.bookDesc.text = this.bookCombo.selectedValue;
    } catch (error) {
      // log the error in ERROR LOG
      this.logger.error('method [refreshBookTxt] - error: ', error, 'errorLocation: ', errorLocation);
      SwtUtil.logError(error, SwtUtil.PREDICT_MODULE_ID, 'Sweeping.ts', "refreshBookTxt", errorLocation);
    }
  }

  closeHandler(): void {
    ExternalInterface.call("closeWindow");
  }

  startOfComms(): void {
  }

  endOfComms(): void {
  }

  inputDataFault() {
    this.swtAlert.error(SwtUtil.getPredictMessage('alert.generic_exception'));
  }

  subAccounts() {
    //Variable for errorLocation
    let errorLocation = 0;
    try {
      this.entityId = this.parentDocument.entityCode;
      errorLocation = 10;
      this.accountId = this.parentDocument.selectedAccountId;
      errorLocation = 20;
      this.selectedCurrency = this.parentDocument.selectedCurrencyCode;
      errorLocation = 30;
      this.subAccountsPresent = this.parentDocument.subAccountsPresent;
      errorLocation = 40;
      ExternalInterface.call('buildSubAccount', 'subAccounts', this.entityId,
        this.accountId, this.selectedCurrency, this.subAccountsPresent);
    } catch (error) {
      // log the error in ERROR LOG
      this.logger.error('method [subAccounts] - error: ', error, 'errorLocation: ', errorLocation);
      SwtUtil.logError(error, SwtUtil.PREDICT_MODULE_ID, 'Sweeping.ts', "subAccounts", errorLocation);
    }
  }

  buildSpecificAccount() {
    //Variable for errorLocation
    let errorLocation = 0;
    try {
      this.entityId = this.parentDocument.entityCode;
      errorLocation = 10;
      this.accountId = this.parentDocument.selectedAccountId;
      errorLocation = 20;
      this.selectedCurrency = this.parentDocument.selectedCurrencyCode;
      errorLocation = 30;
      ExternalInterface.call('buildSpecificAccount', 'getSpecificAccounts', this.entityId,
        this.accountId, this.selectedCurrency);
    } catch (error) {
      // log the error in ERROR LOG
      this.logger.error('method [buildSpecificAccount] - error: ', error, 'errorLocation: ', errorLocation);
      SwtUtil.logError(error, SwtUtil.PREDICT_MODULE_ID, 'Sweeping.ts', "buildSpecificAccount", errorLocation);
    }
  }


  buildSpecificAcctSweepBalGrp() {
    //Variable for errorLocation
    let errorLocation = 0;
    try {
      this.entityId = this.parentDocument.entityCode;
      errorLocation = 10;
      this.accountId = this.parentDocument.selectedAccountId;
      errorLocation = 20;
      this.selectedCurrency = this.parentDocument.selectedCurrencyCode;
      errorLocation = 30;
      ExternalInterface.call('buildSpecificAcctSweepBalGrp', 'handleAcctSweepBalGrp', this.entityId,
        this.accountId, this.selectedCurrency);
    } catch (error) {
      // log the error in ERROR LOG
      this.logger.error('method [buildSpecificAcctSweepBalGrp] - error: ', error, 'errorLocation: ', errorLocation);
      SwtUtil.logError(error, SwtUtil.PREDICT_MODULE_ID, 'Sweeping.ts', "buildSpecificAcctSweepBalGrp", errorLocation);
    }
  }

  buildAccsSweepsSchedule() {
    //Variable for errorLocation
    let errorLocation = 0;
    try {
      this.entityId = this.parentDocument.entityCode;
      errorLocation = 10;
      this.accountId = this.parentDocument.selectedAccountId;
      errorLocation = 20;
      this.selectedCurrency = this.parentDocument.selectedCurrencyCode;
      errorLocation = 30;
      ExternalInterface.call('buildAccsSweepsScheduleUrl', 'getAccsSweepSchedule', this.entityId,
        this.accountId, this.selectedCurrency);
    } catch (error) {
      // log the error in ERROR LOG
      this.logger.error('method [buildSpecificAccount] - error: ', error, 'errorLocation: ', errorLocation);
      SwtUtil.logError(error, SwtUtil.PREDICT_MODULE_ID, 'Sweeping.ts', "buildSpecificAccount", errorLocation);
    }
  }

  buildAccsSweepsScheduleDetails(method) {
    //Variable for errorLocation
    let errorLocation = 0;
    try {
      this.entityId = this.parentDocument.entityCode;
      errorLocation = 10;
      this.accountId = this.parentDocument.acctTxt.text;
      errorLocation = 20;
      var acctScheduleNextVal = this.parentDocument.acctScheduleNextVal;
      errorLocation = 30;
      this.selectedCurrency = this.parentDocument.selectedCurrencyCode;
      errorLocation = 40;
      var defaultTargetBalance = this.defaultTargetBalTxt.text;
      errorLocation = 50;
      var defaultTargetBalType = this.defaultTargetBalCombo.selectedValue;
      errorLocation = 60;
      var defaultBookCode = this.bookCombo.selectedValue;
      errorLocation = 70;
      var defaultSettlementMethod = this.defaultSettMethodCombo.selectedValue;
      errorLocation = 80;
      var defaultMinAmount = this.minAmtTxt.text;
      errorLocation = 90;
      var defaultFromBalanceType = this.sweepFrmBalCombo.selectedValue;
      errorLocation = 100;
      var defaultMaxAmount = this.maxAmtTxt.text;
      errorLocation = 110;
      ExternalInterface.call('buildAccsSweepsScheduleDetailsUrl', method, this.entityId,
        this.accountId, this.selectedCurrency, defaultTargetBalance, defaultTargetBalType,
        defaultBookCode, defaultSettlementMethod, defaultMinAmount, defaultFromBalanceType,
        defaultMaxAmount, this.sweepScheduledId, acctScheduleNextVal);
    } catch (error) {
      // log the error in ERROR LOG
      this.logger.error('method [buildAccsSweepsScheduleDetails] - error: ', error, 'errorLocation: ', errorLocation);
      SwtUtil.logError(error, SwtUtil.PREDICT_MODULE_ID, 'Sweeping.ts', "buildAccsSweepsScheduleDetails", errorLocation);
    }
  }
  validateTime(textInput): any {
    //Variable for errorLocation
    let errorLocation = 0;
    try {
      let validTimeMessage = SwtUtil.getPredictMessage('alert.validTime', null);
      errorLocation = 10;
      if (textInput.text.endsWith(":")) {
        errorLocation = 20;
        textInput.text = textInput.text + "00";
      }
      if (textInput.text && validateFormatTime(textInput) == false) {
        errorLocation = 30;
        this.swtAlert.warning(validTimeMessage, null);
        errorLocation = 40;
        textInput.text = "";
        return false;
      } else {
        errorLocation = 50;
        textInput.text = textInput.text.substring(0, 5);
        return true;

      }
    } catch (error) {
      // log the error in ERROR LOG
      this.logger.error('method [validateTime] - error: ', error, 'errorLocation: ', errorLocation);
      SwtUtil.logError(error, SwtUtil.PREDICT_MODULE_ID, 'Sweeping.ts', "validateTime", errorLocation);
    }
  }
 

  deleteAccountScheduleSweep() {
    //Variable for errorLocation
    let errorLocation = 0;
    try {
      this.requestParams = [];
      this.menuAccessId = ExternalInterface.call('eval', 'menuAccessId');
      errorLocation = 10;
      if (this.menuAccessId) {
        if (this.menuAccessId !== "") {
          this.menuAccessId = Number(this.menuAccessId);
        }
      }
      errorLocation = 20;
      this.inputData.cbStart = this.startOfComms.bind(this);
      this.inputData.cbStop = this.endOfComms.bind(this);
      this.inputData.cbResult = () => {
        this.updateSweepGridData();
      };
      errorLocation = 30;
      this.inputData.cbFault = this.inputDataFault.bind(this);
      this.inputData.encodeURL = false;
      this.actionPath = "acctMaintenance.do?";
      this.actionMethod = 'method=deleteAccountScheduleSweepNew';
      this.requestParams['menuAccessId'] = this.menuAccessId;
      this.requestParams['entityId'] = this.sweepGrid.selectedItem.entity.content; //entityTxt
      this.requestParams['accountId'] = this.parentDocument.acctTxt.text;
      this.requestParams['seqNumber'] = this.sweepGrid.selectedItem.uniqueId.content;
      this.requestParams['callerMethod'] = this.parentDocument.methodName;
      this.inputData.url = this.baseURL + this.actionPath + this.actionMethod;
      errorLocation = 40;
      this.inputData.send(this.requestParams);
      errorLocation = 50;

    } catch (error) {
      // log the error in ERROR LOG
      this.logger.error('method [deleteAccountScheduleSweep] - error: ', error, 'errorLocation: ', errorLocation);
      SwtUtil.logError(error, SwtUtil.PREDICT_MODULE_ID, 'Sweeping.ts', "deleteAccountScheduleSweep", errorLocation);
    }
  }

  updateSweepGridData() {
    //Variable for errorLocation
    let errorLocation = 0;
    try {
      this.sweepGrid.removeSelected();
      errorLocation = 10;
      this.parentDocument.swpGridData = this.sweepGrid.gridData;
      errorLocation = 20;
      this.disableButtons();
    } catch (error) {
      // log the error in ERROR LOG
      this.logger.error('method [updateSweepGridData] - error: ', error, 'errorLocation: ', errorLocation);
      SwtUtil.logError(error, SwtUtil.PREDICT_MODULE_ID, 'Sweeping.ts', "updateSweepGridData", errorLocation);
    }
  }

  disableButtons() {
    //Variable for errorLocation
    let errorLocation = 0;
    try {
      this.changeButton.enabled = false;
      this.changeButton.buttonMode = false;
      errorLocation = 10;
      this.viewButton.enabled = false;
      this.viewButton.buttonMode = false;
      errorLocation = 20;
      this.deleteButton.enabled = false;
      this.deleteButton.buttonMode = false;
    } catch (error) {
      // log the error in ERROR LOG
      this.logger.error('method [disableButtons] - error: ', error, 'errorLocation: ', errorLocation);
      SwtUtil.logError(error, SwtUtil.PREDICT_MODULE_ID, 'Sweeping.ts', "disableButtons", errorLocation);
    }
  }


  enableDisableCombo() {
    //Variable for errorLocation
    let errorLocation = 0;
    try {
      if(this.acctLevGrp.selectedValue=='S'){
        errorLocation = 10;
        this.mainAccCombo.enabled=true;
        }else{
          errorLocation = 20;
        this.mainAccCombo.enabled=false;
        }
    } catch (error) {
      // log the error in ERROR LOG
      this.logger.error('method [enableDisableCombo] - error: ', error, 'errorLocation: ', errorLocation);
      SwtUtil.logError(error, SwtUtil.PREDICT_MODULE_ID, 'Sweeping.ts', "enableDisableCombo", errorLocation);
    }
  }

  validateReserve(field): any {
    let validAmount = SwtUtil.getPredictMessage('alert.validAmount', null);
    if (!(validateCurrencyPlaces(field,  this.parentDocument.currencyPattern, this.selectedCurrency))) {
      this.swtAlert.warning(validAmount);
      return false;
    }
  }

  
  validateField(field, strLabel, strPat, maxValue, minValue) {
    //Variable for errorLocation
    let errorLocation = 0;
    try {
      var isValidField = validateField(field, strLabel, strPat, maxValue, minValue);
      if (!isValidField) {
        field.text = "";
        return false;
      }
    } catch (error) {
      // log the error in ERROR LOG
      this.logger.error('method [validateField] - error: ', error, 'errorLocation: ', errorLocation);
      SwtUtil.logError(error, SwtUtil.PREDICT_MODULE_ID, 'Sweeping.ts', "validateField", errorLocation);
    }
  }

  callShowSweeping() {
    this.parentDocument.callShowSweeping();
  }
}
