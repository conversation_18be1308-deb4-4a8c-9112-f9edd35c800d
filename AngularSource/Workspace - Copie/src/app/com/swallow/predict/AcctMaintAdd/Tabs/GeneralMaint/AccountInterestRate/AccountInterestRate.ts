import { Component, OnInit, ModuleWithProviders, NgModule, ViewChild, ElementRef } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';
import { SwtToolBoxModule, ExternalInterface, SwtCanvas, SwtLabel, SwtTextInput, SwtButton, SwtLoadingImage, SwtCommonGrid, SwtUtil, SwtAlert, JSONReader, HTTPComms, Logger, CommonService, SwtModule } from 'swt-tool-box';
declare var instanceElement: any;

@Component({
  selector: 'app-account-interest-rate',
  templateUrl: './AccountInterestRate.html',
  styleUrls: ['./AccountInterestRate.css']
})
export class AccountInterestRate extends SwtModule implements OnInit {

  /***********SwtCanvas***********/
  @ViewChild('rateGridContainer') rateGridContainer: SwtCanvas;

  /***********SwtButton***********/
  @ViewChild('addButton') addButton: SwtButton;
  @ViewChild('changeButton') changeButton: SwtButton;
  @ViewChild('deleteButton') deleteButton: SwtButton;
  @ViewChild('closeButton') closeButton: SwtButton;

  /***********SwtLoadingImage***********/
  @ViewChild('loadingImage') loadingImage: SwtLoadingImage;

  private swtAlert: SwtAlert;
  private menuAccessId;

  /**
   * Data Objects
   **/
  public jsonReader = new JSONReader();
  public jsonReader2 = new JSONReader();
  public lastReceivedJSON;
  public prevReceivedJSON;
  /**
    * Communication Objects
    **/
  public inputData = new HTTPComms(this.commonService);
  public baseURL: string = SwtUtil.getBaseURL();
  private actionMethod: string = "";
  private actionPath: string = "";
  private requestParams = [];
  private _invalidComms: string;
  /**
   * Variables to hold the grid
   **/
  private rateInterestGrid: SwtCommonGrid;
  private logger: Logger = null;

  public entityId = "";
  public accountId = "";
  public selectedCurrencyCode = "";
  private rateInterestGridCol;
  private rateInterestGridRows;
  public parentMethode;
  private acctInerestData = [];
  public currencyPattern;
  public dateFormat;
  private callerMethod;
  constructor(private commonService: CommonService, private element: ElementRef) {
    super(element, commonService);
    this.logger = new Logger('Linked', this.commonService.httpclient);
    this.swtAlert = new SwtAlert(commonService);
    this.logger.info("method [constructor] - START/END ");
  }


  ngOnInit() {
    this.rateInterestGrid = <SwtCommonGrid>this.rateGridContainer.addChild(SwtCommonGrid);

    //buttons
    this.addButton.label = SwtUtil.getPredictMessage('button.add', null);
    this.addButton.toolTip = SwtUtil.getPredictMessage('button.ratesAdd', null);
    this.changeButton.label = SwtUtil.getPredictMessage('button.change', null);
    this.changeButton.toolTip = SwtUtil.getPredictMessage('button.ratesChange', null);
    this.deleteButton.label = SwtUtil.getPredictMessage('button.delete', null);
    this.deleteButton.toolTip = SwtUtil.getPredictMessage('button.ratesDelete', null);
    this.closeButton.label = SwtUtil.getPredictMessage('button.close', null);
    this.closeButton.toolTip = SwtUtil.getPredictMessage('tooltip.close', null);

  }

  onLoad() {
    instanceElement = this;
    //Variable for errorLocation
    let errorLocation = 0;
    try {
      this.selectedCurrencyCode = window.opener.instanceElement.selectedCurrencyCode;
      this.callerMethod= window.opener.instanceElement.methodName;
      errorLocation = 10;
      this.requestParams = [];
      if (this.menuAccessId) {
        if (this.menuAccessId !== "") {
          this.menuAccessId = Number(this.menuAccessId);
        }
      }
      errorLocation = 20;
      this.inputData.cbStart = this.startOfComms.bind(this);
      this.inputData.cbStop = this.endOfComms.bind(this);
      this.inputData.cbResult = (event) => {
        this.inputDataResult(event);
      };
      errorLocation = 30;
      this.inputData.cbFault = this.inputDataFault.bind(this);
      this.inputData.encodeURL = false;
      this.actionPath = "acctMaintenance.do?";
      this.actionMethod = 'method=displayRateScreen';
      this.requestParams['menuAccessId'] = this.menuAccessId;
      errorLocation = 40;
      this.requestParams['entityId'] = window.opener.instanceElement.entityCode;
      errorLocation = 30;
      this.requestParams['accountId'] = window.opener.instanceElement.selectedAccountId;
      errorLocation = 40;
      this.requestParams['selectedCurrencyCode'] = window.opener.instanceElement.selectedCurrencyCode;
      errorLocation = 50;
      this.requestParams['callerMethod'] = window.opener.instanceElement.methodName;
      errorLocation = 60;
      this.inputData.url = this.baseURL + this.actionPath + this.actionMethod;
      this.inputData.send(this.requestParams);
      errorLocation = 30;

      this.rateInterestGrid.onRowClick = (event) => {
        errorLocation = 40;
        this.cellClickEventHandler(event);
      };

    } catch (error) {
      // log the error in ERROR LOG
      this.logger.error('method [onLoad] - error: ', error, 'errorLocation: ', errorLocation);
      SwtUtil.logError(error, SwtUtil.PREDICT_MODULE_ID, 'AccountInterestRate.ts', "onLoad", errorLocation);
    }
  }


  cellClickEventHandler(event): void {
    //Variable for errorLocation
    let errorLocation = 0;
    try {
      if (this.rateInterestGrid.selectedIndex >= 0) {
        errorLocation = 10;
        this.changeButton.enabled = true;
        this.changeButton.buttonMode = true;
        errorLocation = 20;
        this.deleteButton.enabled = true;
        this.deleteButton.buttonMode = true;
      } else {
        errorLocation = 30;
        this.changeButton.enabled = false;
        this.changeButton.buttonMode = false;
        errorLocation = 40;
        this.deleteButton.enabled = false;
        this.deleteButton.buttonMode = false;
      }
    } catch (error) {
      // log the error in ERROR LOG
      this.logger.error('method [cellClickEventHandler] - error: ', error, 'errorLocation: ', errorLocation);
      SwtUtil.logError(error, SwtUtil.PREDICT_MODULE_ID, 'AccountInterestRate.ts', "cellClickEventHandler", errorLocation);
    }
  }



  inputDataResult(event): void {
    //Variable for errorLocation
    let errorLocation = 0;
    try {
      // Checks the inputData and stops the communication
      if (this.inputData.isBusy()) {
        this.inputData.cbStop();
      } else {
        this.lastReceivedJSON = event;
        this.jsonReader.setInputJSON(this.lastReceivedJSON);

        if (this.jsonReader.getRequestReplyStatus()) {
          if ((this.lastReceivedJSON != this.prevReceivedJSON)) {
            this.enableDisableButtons();
            errorLocation = 10;
            this.parentMethode = window.opener.instanceElement.methodName;
            errorLocation = 20;
            this.accountId = window.opener.instanceElement.selectedAccountId;
            errorLocation = 30;
            if (!this.jsonReader.isDataBuilding()) {
              errorLocation = 40;
              this.currencyPattern = this.jsonReader.getSingletons().currencyPattern;
              errorLocation = 50;
              this.dateFormat = this.jsonReader.getSingletons().dateFormat;
              errorLocation = 60;
              const obj = { columns: this.lastReceivedJSON.acctInterestRate.rateGrid.metadata.columns };
              errorLocation = 70;
              this.rateInterestGridCol = this.lastReceivedJSON.acctInterestRate.rateGrid.metadata.columns.column;
              errorLocation = 80;
              this.rateInterestGrid.CustomGrid(obj);
              errorLocation = 90;
              this.rateInterestGridRows = this.lastReceivedJSON.acctInterestRate.rateGrid.rows;
              errorLocation = 100;
              if (this.rateInterestGridRows && this.rateInterestGridRows.size > 0) {
                errorLocation = 110;
                this.rateInterestGrid.gridData = this.rateInterestGridRows;
                errorLocation = 120;
                this.rateInterestGrid.setRowSize = this.jsonReader.getRowSize();
              }
              else {
                this.rateInterestGrid.gridData = { size: 0, row: [] };
              }
            }
          }
        } else {
          if (this.lastReceivedJSON.hasOwnProperty("request_reply")) {
            this.swtAlert.error(this.jsonReader.getRequestReplyMessage() + "\n" + this.jsonReader.getRequestReplyLocation(), "Error");
          }
        }

      }
    } catch (error) {
      // log the error in ERROR LOG
      this.logger.error('method [inputDataResult] - error: ', error, 'errorLocation: ', errorLocation);
      SwtUtil.logError(error, SwtUtil.PREDICT_MODULE_ID, 'AccountInterestRate.ts', "inputDataResult", errorLocation);
    }
  }



  saveUpdateAcctInterestRate(methodName, parentMethod, accountId, date, crMargin, drMargin) {
    //Variable for errorLocation
    let errorLocation = 0;
    try {
      this.requestParams = [];
      let method = methodName == 'add' ? 'saveAcctInterestRate' : 'updateAcctInterestRate';
      errorLocation = 10;
      this.menuAccessId = ExternalInterface.call('eval', 'menuAccessId');
      errorLocation = 20;
      if (this.menuAccessId) {
        if (this.menuAccessId !== "") {
          this.menuAccessId = Number(this.menuAccessId);
        }
      }
      this.inputData.cbStart = this.startOfComms.bind(this);
      this.inputData.cbStop = this.endOfComms.bind(this);
      this.inputData.cbResult = (event) => {
        errorLocation = 30;
        this.saveDataResult(event);
      };
      this.inputData.cbFault = this.inputDataFault.bind(this);
      this.inputData.encodeURL = false;
      this.actionPath = "acctMaintenance.do?";
      this.actionMethod = 'method=' + method;
      this.requestParams['menuAccessId'] = this.menuAccessId;
      this.requestParams['callerMethod'] = this.parentMethode;
      this.requestParams['relatedToaccountId'] = this.accountId;
      this.requestParams['date'] = date;
      this.requestParams['crMargin'] = crMargin;
      this.requestParams['drMargin'] = drMargin;
      this.inputData.url = this.baseURL + this.actionPath + this.actionMethod;
      errorLocation = 40;
      this.inputData.send(this.requestParams);
    } catch (error) {
      // log the error in ERROR LOG
      this.logger.error('method [saveUpdateAcctInterestRate] - error: ', error, 'errorLocation: ', errorLocation);
      SwtUtil.logError(error, SwtUtil.PREDICT_MODULE_ID, 'AccountInterestRate.ts', "saveUpdateAcctInterestRate", errorLocation);
    }
  }

  saveDataResult(event): void {
    //Variable for errorLocation
    let errorLocation = 0;
    try {
      if (this.inputData.isBusy()) {
        this.inputData.cbStop();
      } else {
        this.lastReceivedJSON = event;
        errorLocation = 10;
        let jsonResponse: JSONReader = new JSONReader();
        jsonResponse.setInputJSON(this.lastReceivedJSON);
        errorLocation = 20;
        if (jsonResponse.getRequestReplyStatus()) {
          let acctInterestGridData = JSON.parse(jsonResponse.getSingletons().acctInterestRateObject);
          errorLocation = 30;
          this.updateAcctInterestGridData(acctInterestGridData);
        } else {// Display an alert warning since the record is already exist
          if (jsonResponse.getRequestReplyMessage() == "errors.DataIntegrityViolationExceptioninAdd") {
            errorLocation = 40;
            this.swtAlert.warning(SwtUtil.getPredictMessage('errors.DataIntegrityViolationExceptioninAdd', null));
          }
        }
      }
    } catch (error) {
      // log the error in ERROR LOG
      this.logger.error('method [saveDataResult] - error: ', error, 'errorLocation: ', errorLocation);
      SwtUtil.logError(error, SwtUtil.PREDICT_MODULE_ID, 'AccountInterestRate.ts', "saveDataResult", errorLocation);
    }
  }

  updateAcctInterestGridData(data) {
    //Variable for errorLocation
    let errorLocation = 0;
    try {
      this.acctInerestData = [];
      for (let i = 0; i < data.length; i++) {
        this.acctInerestData.push({
          date: { clickable: false, content: data[i].interestDateRateAsString ? data[i].interestDateRateAsString : "", negative: false },
          ceditMargin: { clickable: false, content: data[i].creditRate ? data[i].creditRate : "", negative: false },
          debitMargin: { clickable: false, content: data[i].overdraftRate ? data[i].overdraftRate : "", negative: false },
          updateDate: { clickable: false, content: data[i].updateDateAsString ? data[i].updateDateAsString : "", negative: false },
          user: { clickable: false, content: data[i].updateUser ? data[i].updateUser : "", negative: false }
        });
        errorLocation = 10;
      }
      this.rateInterestGrid.gridData = { row: this.acctInerestData, size: this.acctInerestData.length };
      errorLocation = 20;
      this.rateInterestGrid.refresh();
      //this.swpGridData=this.sweepAcctShedData;
    } catch (error) {
      // log the error in ERROR LOG
      this.logger.error('method [updateAcctInterestGridData] - error: ', error, 'errorLocation: ', errorLocation);
      SwtUtil.logError(error, SwtUtil.PREDICT_MODULE_ID, 'AccountInterestRate.ts', "updateAcctInterestGridData", errorLocation);
    }
  }


  deleteAcctInterestRateSweep() {
    //Variable for errorLocation
    let errorLocation = 0;
    try {
      this.requestParams = [];
      this.menuAccessId = ExternalInterface.call('eval', 'menuAccessId');
      //this.methodName = ExternalInterface.call('eval', 'methodName');
      if (this.menuAccessId) {
        if (this.menuAccessId !== "") {
          this.menuAccessId = Number(this.menuAccessId);
        }
      }
      errorLocation = 10;
      this.inputData.cbStart = this.startOfComms.bind(this);
      this.inputData.cbStop = this.endOfComms.bind(this);
      this.inputData.cbResult = () => {
        this.updateAcctInterestGrid();
      };
      errorLocation = 20;
      this.inputData.cbFault = this.inputDataFault.bind(this);
      this.inputData.encodeURL = false;
      this.actionPath = "acctMaintenance.do?";
      this.actionMethod = 'method=deleteAcctInterestRate';
      this.requestParams['menuAccessId'] = this.menuAccessId;
      this.requestParams['creditInterestRate'] = this.rateInterestGrid.selectedItem.ceditMargin.content;
      errorLocation = 30;
      this.requestParams['overdraftInterestRate'] = this.rateInterestGrid.selectedItem.debitMargin.content;
      errorLocation = 40;
      this.requestParams['interestRateDate'] = this.rateInterestGrid.selectedItem.date.content;
      errorLocation = 50;
      this.requestParams['relatedToaccountId'] = window.opener.instanceElement.selectedAccountId;
      errorLocation = 60;
      this.requestParams['callerMethod'] = window.opener.instanceElement.methodName;
      errorLocation = 70;
      this.inputData.url = this.baseURL + this.actionPath + this.actionMethod;
      this.inputData.send(this.requestParams);
      errorLocation = 80;

    } catch (error) {
      // log the error in ERROR LOG
      this.logger.error('method [deleteAcctInterestRateSweep] - error: ', error, 'errorLocation: ', errorLocation);
      SwtUtil.logError(error, SwtUtil.PREDICT_MODULE_ID, 'AccountInterestRate.ts', "deleteAcctInterestRateSweep", errorLocation);
    }
  }

  updateAcctInterestGrid() {
    //Variable for errorLocation
    let errorLocation = 0;
    try {
      this.rateInterestGrid.removeSelected();
      errorLocation = 10;
      //this.parentDocument.swpGridData=this.rateInterestGrid.gridData;
      this.disableButtons();
    } catch (error) {
      // log the error in ERROR LOG
      this.logger.error('method [updateAcctInterestGrid] - error: ', error, 'errorLocation: ', errorLocation);
      SwtUtil.logError(error, SwtUtil.PREDICT_MODULE_ID, 'AccountInterestRate.ts', "updateAcctInterestGrid", errorLocation);
    }
  }

  disableButtons() {
    //Variable for errorLocation
    let errorLocation = 0;
    try {
      this.changeButton.enabled = false;
      this.changeButton.buttonMode = false;
      errorLocation = 10;
      this.deleteButton.enabled = false;
      this.deleteButton.buttonMode = false;
    } catch (error) {
      // log the error in ERROR LOG
      this.logger.error('method [disableButtons] - error: ', error, 'errorLocation: ', errorLocation);
      SwtUtil.logError(error, SwtUtil.PREDICT_MODULE_ID, 'AccountInterestRate.ts', "disableButtons", errorLocation);
    }
  }

  startOfComms(): void {
    this.loadingImage.setVisible(true);
  }

  endOfComms(): void {
    this.loadingImage.setVisible(false);
  }

  inputDataFault() {
    this.swtAlert.error(SwtUtil.getPredictMessage('alert.generic_exception'));
  }


  closeHandler(): void {
    var data= this.rateInterestGrid.gridData[0];
    window.opener.instanceElement.updateRateSection(data);
    ExternalInterface.call('close');
  }

  openAddChildScreen(): void {
    //Variable for errorLocation
    let errorLocation = 0;
    try {
      ExternalInterface.call("buildAddAccIntRateURL", "addAcctInterestRate",
        this.parentMethode, this.accountId);
      errorLocation = 10;
    } catch (error) {
      // log the error in ERROR LOG
      this.logger.error('method [openAddChildScreen] - error: ', error, 'errorLocation: ', errorLocation);
      SwtUtil.logError(error, SwtUtil.PREDICT_MODULE_ID, 'AccountInterestRate.ts', "openAddChildScreen", errorLocation);
    }
  }

  openChangeChildScreen(): void {
    //Variable for errorLocation
    let errorLocation = 0;
    try {
      let selectedRow = this.rateInterestGrid.selectedItem;
      errorLocation = 10;
      let interestRateDate = this.rateInterestGrid.selectedItem.date.content;
      errorLocation = 20;
      let creditIntRate = this.rateInterestGrid.selectedItem.ceditMargin.content;
      errorLocation = 30;
      let overdraftIntRate = this.rateInterestGrid.selectedItem.debitMargin.content;
      errorLocation = 40;
      ExternalInterface.call("buildChangeAccIntRateURL", "changeAcctInterestRate",
        this.parentMethode, this.accountId, interestRateDate, creditIntRate, overdraftIntRate);
    } catch (error) {
      // log the error in ERROR LOG
      this.logger.error('method [openChangeChildScreen] - error: ', error, 'errorLocation: ', errorLocation);
      SwtUtil.logError(error, SwtUtil.PREDICT_MODULE_ID, 'AccountInterestRate.ts', "openChangeChildScreen", errorLocation);
    }
  }


  enableDisableButtons() {
    //Variable for errorLocation
    let errorLocation = 0;
    try {
 
      if (this.callerMethod != "view") {
        errorLocation = 10;
        this.addButton.enabled= true;
        this.addButton.buttonMode= true;
        this.addButton.includeInLayout= true;
        this.addButton.visible= true;
        errorLocation = 20;
      } else {
        errorLocation = 30;
        this.addButton.enabled= false;
        this.addButton.buttonMode= false;
        this.addButton.includeInLayout= false;
        this.addButton.visible= false;
        errorLocation = 40;
        this.changeButton.enabled= false;
        this.changeButton.buttonMode= false;
        this.changeButton.includeInLayout= false;
        this.changeButton.visible= false;
        errorLocation = 50;
        this.deleteButton.enabled= false;
        this.deleteButton.buttonMode= false;
        this.deleteButton.includeInLayout= false;
        this.deleteButton.visible= false;
      }
    } catch (error) {
      // log the error in ERROR LOG
      this.logger.error('method [enableDisableButtons] - error: ', error, 'errorLocation: ', errorLocation);
      SwtUtil.logError(error, SwtUtil.PREDICT_MODULE_ID, 'AccountInterestRate.ts', "enableDisableButtons", errorLocation);
    }
  }


}

//Define lazy loading routes
const routes: Routes = [
  { path: '', component: AccountInterestRate }
];
export const routing: ModuleWithProviders = RouterModule.forChild(routes);

//Define an NgModule that will be loaded by app module
@NgModule({
  imports: [routing, SwtToolBoxModule],
  declarations: [AccountInterestRate],
  entryComponents: []
})
export class AccountInterestRateModule {
}