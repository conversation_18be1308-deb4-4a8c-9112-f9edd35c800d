<SwtModule #swtModule (creationComplete)='onLoad()' width="100%" height="100%">
  <VBox width='100%' height='100%' paddingLeft="5" paddingRight="5" paddingBottom="5" paddingTop="5">
      <Grid width="100%" height="93%" paddingLeft="5">
          <SwtCanvas #acctMaintSchedSwpContainer id="acctMaintSchedSwpContainer" styleName="canvasWithGreyBorder" width="100%"
          height="100%" border="false"></SwtCanvas>
      </Grid>

    <SwtCanvas width="100%" height="10%">
      <HBox width="100%">
        <HBox paddingLeft="5" width="90%">
          <SwtButton [buttonMode]="true" id="closeButton" #closeButton (click)="closeHandler()">
          </SwtButton>
        </HBox>
       <HBox width="10%" horizontalAlign="right" paddingLeft="5">
        <SwtHelpButton id="helpIcon"
        #helpIcon (click)="doHelp()"></SwtHelpButton>
          <SwtLoadingImage #loadingImage></SwtLoadingImage>
        </HBox>
    
      </HBox>
    </SwtCanvas>
  </VBox>
</SwtModule>