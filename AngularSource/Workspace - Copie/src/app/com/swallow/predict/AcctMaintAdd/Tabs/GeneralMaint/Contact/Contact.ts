import { Component, OnInit, ViewChild, ElementRef, ModuleWithProviders, NgModule } from '@angular/core';
import { SwtLabel, SwtComboBox, SwtFieldSet, SwtRadioButtonGroup, SwtRadioItem, SwtTextInput, SwtButton, SwtCheckBox, SwtLoadingImage, SwtAlert, JSONReader, HTTPComms, SwtUtil, CommonService, SwtModule, SwtToolBoxModule, ExternalInterface, Logger } from 'swt-tool-box';
import { Routes, RouterModule } from '@angular/router';
declare function validateField(strField, strLabel, strPat, maxValue, minValue): any;

@Component({
  selector: 'app-contact',
  templateUrl: './Contact.html',
  styleUrls: ['./Contact.css']
})
export class Contact extends SwtModule implements OnInit {
  /***********SwtLabel***********/
  @ViewChild('nameLbl') nameLbl: SwtLabel;
  @ViewChild('phoneLbl') phoneLbl: SwtLabel;
  @ViewChild('emailLbl') emailLbl: SwtLabel;

  /***********SwtFieldSet***********/
  @ViewChild('fieldSet') fieldSet: SwtFieldSet;

  /***********SwtTextInput***********/
  @ViewChild('nameTxt') nameTxt: SwtTextInput;
  @ViewChild('phoneTxt') phoneTxt: SwtTextInput;
  @ViewChild('emailTxt') emailTxt: SwtTextInput;

  /***********SwtButton***********/
  @ViewChild('okButton') okButton: SwtButton;
  @ViewChild('closeButton') closeButton: SwtButton;

  /***********SwtLoadingImage***********/
  @ViewChild('loadingImage') loadingImage: SwtLoadingImage;

  private logger: Logger = null;
  private swtAlert: SwtAlert;
  private menuAccessId;
  /**
   * Data Objects
   **/
  public jsonReader: JSONReader = new JSONReader();
  public lastRecievedJSON;
  public prevRecievedJSON;
  public initReceivedJSON;
  /**
    * Communication Objects
    **/
  public inputData = new HTTPComms(this.commonService);
  public baseURL: string = SwtUtil.getBaseURL();
  private actionMethod: string = "";
  private actionPath: string = "";
  private requestParams = [];
  private _invalidComms: string;


  constructor(private commonService: CommonService, private element: ElementRef) {
    super(element, commonService);
    this.logger = new Logger('Contact', this.commonService.httpclient);
    this.swtAlert = new SwtAlert(commonService);

  }

  ngOnInit() {

    //label
    this.nameLbl.text = SwtUtil.getPredictMessage('section.sectionName', null) + "*";
    this.phoneLbl.text = SwtUtil.getPredictMessage('contact.phone', null);
    this.emailLbl.text = SwtUtil.getPredictMessage('contact.email', null);

    //text input
    this.nameTxt.toolTip = SwtUtil.getPredictMessage('tooltip.contactName', null);
    this.phoneTxt.toolTip = SwtUtil.getPredictMessage('tooltip.enterPhNo', null);
    this.emailTxt.toolTip = SwtUtil.getPredictMessage('tooltip.emailId', null);

    //fieldSet
    this.fieldSet.legendText = SwtUtil.getPredictMessage('contact.fieldet', null);

    //buttons
    this.okButton.label = SwtUtil.getPredictMessage('button.ok', null);
    this.okButton.toolTip = SwtUtil.getPredictMessage('tooltip.ok', null);
    this.closeButton.label = SwtUtil.getPredictMessage('button.close', null);
    this.closeButton.toolTip = SwtUtil.getPredictMessage('tooltip.close', null);

    this.nameTxt.required=true;
  }


  onLoad() {
    //Variable for errorLocation
    let errorLocation = 0;
    try {
      this.nameTxt.text = window.opener.instanceElement.accContactName;
      errorLocation = 10;
      this.phoneTxt.text = window.opener.instanceElement.accPhoneNumber;
      errorLocation = 20;
      this.emailTxt.text = window.opener.instanceElement.accEmailAddr;
    } catch (error) {
      // log the error in ERROR LOG
      this.logger.error('method [onLoad] - error: ', error, 'errorLocation: ', errorLocation);
      SwtUtil.logError(error, SwtUtil.PREDICT_MODULE_ID, 'Contact.ts', "onLoad", errorLocation);
    }
  }


  /*inputDataResult(event) {
    if (this.inputData.isBusy()) {
      this.inputData.cbStop();
    } else {
      this.lastRecievedJSON = event;
      this.jsonReader.setInputJSON(this.lastRecievedJSON);
      if (this.jsonReader.getRequestReplyStatus()) {
        if (this.lastRecievedJSON != this.prevRecievedJSON) {
          if (!this.jsonReader.isDataBuilding()) {
  
          }
        }
      }
    }
  }*/



  saveAcctContact() {
    //Variable for errorLocation
    let errorLocation = 0;
    this.requestParams = [];
    try { 
      this.menuAccessId = ExternalInterface.call('eval', 'menuAccessId');
      if (this.menuAccessId) {
        if (this.menuAccessId !== "") {
          this.menuAccessId = Number(this.menuAccessId);
        }
      }
      errorLocation = 10;
      this.inputData.cbStart = this.startOfComms.bind(this);
      this.inputData.cbStop = this.endOfComms.bind(this);
      this.inputData.cbResult = (event) => {
        //this.inputDataResult(event);
      };
      errorLocation = 20;
      this.inputData.cbFault = this.inputDataFault.bind(this);
      this.inputData.encodeURL = false;
      this.actionPath = "acctMaintenance.do?";
      this.actionMethod = 'method=saveContactDetailsNew';
      this.requestParams['menuAccessId'] = this.menuAccessId;
      this.requestParams['accContactName'] = this.nameTxt.text;
      this.requestParams['accPhoneNumber'] = this.phoneTxt.text;
      this.requestParams['accEmailAddr'] = this.emailTxt.text;
      this.inputData.url = this.baseURL + this.actionPath + this.actionMethod;
      errorLocation = 30;
      this.inputData.send(this.requestParams);
      errorLocation = 40;

    } catch (error) {
      // log the error in ERROR LOG
      this.logger.error('method [saveAcctContact] - error: ', error, 'errorLocation: ', errorLocation);
      SwtUtil.logError(error, SwtUtil.PREDICT_MODULE_ID, 'Contact.ts', "saveAcctContact", errorLocation);
    }
  }

  refreshParent() {
    //Variable for errorLocation
    let errorLocation = 0;
    try {
      if(this.checkFields()){
      window.opener.instanceElement.accContactName = this.nameTxt.text;
      errorLocation = 10;
      window.opener.instanceElement.accPhoneNumber = this.phoneTxt.text;
      errorLocation = 20;
      window.opener.instanceElement.accEmailAddr = this.emailTxt.text;
      errorLocation = 30;
      ExternalInterface.call("close");
      }
    } catch (error) {
      // log the error in ERROR LOG
      this.logger.error('method [refreshParent] - error: ', error, 'errorLocation: ', errorLocation);
      SwtUtil.logError(error, SwtUtil.PREDICT_MODULE_ID, 'Contact.ts', "refreshParent", errorLocation);
    }
  }


  checkFields() {

    //Variable for errorLocation
    let errorLocation = 0;
    try {
      var contactName = validateField(this.nameTxt, 'acctMaintenance.acctContactName', 'alphaNumPatWithSpace', "", "");
      if (contactName) {
        if (this.nameTxt.text != "") {
          if (this.isNumeric(this.phoneTxt.text)) {
            if (this.validateEmail(this.emailTxt.text)) {
              return true;
            } else {
              return false;
            }
          } else {
            return false;
          }

        } else {
          this.swtAlert.error(SwtUtil.getPredictMessage('contactDetails.alert.fillnamefield'));
          return false;
        }
      } else {
        return false;
      }

    } catch (error) {
      // log the error in ERROR LOG
      this.logger.error('method [checkFields] - error: ', error, 'errorLocation: ', errorLocation);
      SwtUtil.logError(error, SwtUtil.PREDICT_MODULE_ID, 'Contact.ts', "checkFields", errorLocation);
    }
  }


  //phone number validation starts
  isNumeric(strString)
  //  check for valid numeric strings	
  {
    //Variable for errorLocation
    let errorLocation = 0;
    try {
      // alert("inside phone validation");
      var strValidChars = "0123456789-";
      var strChar;
      var blnResult = true;

      if (strString.length == 0) return true;

      //  test strString consists of valid characters listed above
      for (let i = 0; i < strString.length && blnResult == true; i++) {
        strChar = strString.charAt(i);
        if (strValidChars.indexOf(strChar) == -1) {
          blnResult = false;
          this.swtAlert.error(SwtUtil.getPredictMessage('contactDetails.alert.phonenocontain0123456789'));
        }
      }
    } catch (error) {
      // log the error in ERROR LOG
      this.logger.error('method [IsNumeric] - error: ', error, 'errorLocation: ', errorLocation);
      SwtUtil.logError(error, SwtUtil.PREDICT_MODULE_ID, 'Contact.ts', "IsNumeric", errorLocation);
    }
    return blnResult;
  }


  validateEmail(addr) {
    //Variable for errorLocation
    let errorLocation = 0;
    try {
      //alert ("email address is "+addr);
      if (addr.length > 0) {

        var invalidChars = '\/\'\\ ";:?!()[]\{\}^|';
        for (let i = 0; i < invalidChars.length; i++) {
          if (addr.indexOf(invalidChars.charAt(i), 0) > -1) {
            this.swtAlert.error(SwtUtil.getPredictMessage('contactDetails.alert.emailaddinvalidchar'));
            return false;
          }
        }
        for (let i = 0; i < addr.length; i++) {
          if (addr.charCodeAt(i) > 127) {
            this.swtAlert.error(SwtUtil.getPredictMessage('contactDetails.alert.emailaddcontainsnonasciichar'));
            return false;
          }
        }

        var atPos = addr.indexOf('@', 0);
        if (atPos == -1) {
          this.swtAlert.error(SwtUtil.getPredictMessage('contactDetails.alert.emailaddcontain@'));

          return false;
        }
        if (atPos == 0) {
          this.swtAlert.error(SwtUtil.getPredictMessage('contactDetails.alert.notstrtemailaddcontain@'));
          return false;
        }
        if (addr.indexOf('@', atPos + 1) > - 1) {
          this.swtAlert.error(SwtUtil.getPredictMessage('contactDetails.alert.cntainonceemailaddcontain@'));
          return false;
        }
        if (addr.indexOf('.', atPos) == -1) {
          this.swtAlert.error(SwtUtil.getPredictMessage('contactDetails.alert.emailaddcontainperiod'));

          return false;
        }
        if (addr.indexOf('@.', 0) != -1) {
          this.swtAlert.error(SwtUtil.getPredictMessage('contactDetails.alert.emailaddntfollow@'));

          return false;
        }
        if (addr.indexOf('.@', 0) != -1) {
          this.swtAlert.error(SwtUtil.getPredictMessage('contactDetails.alert.emailaddntprecede@'));
          return false;
        }
        if (addr.indexOf('..', 0) != -1) {
          this.swtAlert.error(SwtUtil.getPredictMessage('contactDetails.alert.twoperiodntadjacentemailadd'));
          return false;
        }
        var suffix = addr.substring(addr.lastIndexOf('.') + 1);
        if (suffix.length != 2 && suffix != 'com' && suffix != 'net' && suffix != 'org' && suffix != 'edu' && suffix != 'int' && suffix != 'mil' && suffix != 'gov' && suffix != 'arpa' && suffix != 'biz' && suffix != 'aero' && suffix != 'name' && suffix != 'coop' && suffix != 'info' && suffix != 'pro' && suffix != 'museum') {
          this.swtAlert.error(SwtUtil.getPredictMessage('contactDetails.alert.invaildprimrydomain'));

          return false;
        }
      }
      return true;
    } catch (error) {
      // log the error in ERROR LOG
      this.logger.error('method [validateEmail] - error: ', error, 'errorLocation: ', errorLocation);
      SwtUtil.logError(error, SwtUtil.PREDICT_MODULE_ID, 'Contact.ts', "validateEmail", errorLocation);
    }
  }

closeHandler() {
  ExternalInterface.call("close");
}

startOfComms(): void {
  this.loadingImage.setVisible(true);
}

endOfComms(): void {
  this.loadingImage.setVisible(false);
}

inputDataFault() {
  this.swtAlert.error(SwtUtil.getPredictMessage('alert.generic_exception'));
}

}

//Define lazy loading routes
const routes: Routes = [
  { path: '', component: Contact }
];
export const routing: ModuleWithProviders = RouterModule.forChild(routes);

//Define an NgModule that will be loaded by app module
@NgModule({
  imports: [routing, SwtToolBoxModule],
  declarations: [Contact],
  entryComponents: []
})
export class ContactModule {
}
