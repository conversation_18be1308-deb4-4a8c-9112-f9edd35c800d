<SwtModule #swtModule (creationComplete)='onLoad()' width="100%" height="100%">
  <VBox width='100%' height='100%' paddingLeft="5" paddingRight="5" paddingBottom="5" paddingTop="5">
    <SwtFieldSet id="fieldSet" #fieldSet style="padding-bottom: 5px; height: 100px; width: 100%; color:blue;">
      <Grid width="100%" height="100%" paddingLeft="5">
        <GridRow width="100%" height="25">
          <GridItem width="60">
            <SwtLabel id="nameLbl" #nameLbl></SwtLabel>
          </GridItem>
          <GridItem paddingRight="20">
            <SwtTextInput id='nameTxt' #nameTxt maxChars="30" width="250"></SwtTextInput>
          </GridItem>
        </GridRow>

        <GridRow width="100%" height="25">
          <GridItem width="60">
            <SwtLabel id="phoneLbl" #phoneLbl></SwtLabel>
          </GridItem>
          <GridItem paddingRight="20">
            <SwtTextInput id='phoneTxt' #phoneTxt maxChars="20" width="250"></SwtTextInput>
          </GridItem>
        </GridRow>

        <GridRow width="100%" height="25">
          <GridItem width="60">
            <SwtLabel id="emailLbl" #emailLbl></SwtLabel>
          </GridItem>
          <GridItem paddingRight="20">
            <SwtTextInput id='emailTxt' #emailTxt maxChars="50" width="250"></SwtTextInput>
          </GridItem>
        </GridRow>

      </Grid>
    </SwtFieldSet>

    <SwtCanvas width="100%" height="35">
      <HBox width="100%">
        <HBox paddingLeft="5" width="90%">
          <SwtButton [buttonMode]="true" id="okButton" #okButton (click)="refreshParent()">
          </SwtButton>
          <SwtButton [buttonMode]="true" id="closeButton" #closeButton (click)="closeHandler()">
          </SwtButton>
        </HBox>
       <!--<HBox width="10%" horizontalAlign="right" paddingLeft="5">
          <SwtLoadingImage #loadingImage></SwtLoadingImage>
        </HBox>-->
    
      </HBox>
    </SwtCanvas>
  </VBox>
</SwtModule>