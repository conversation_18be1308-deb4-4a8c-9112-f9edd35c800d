import {Component, ElementRef, ModuleWithProviders, NgModule, OnInit, ViewChild} from '@angular/core';
import {
  CommonService, ContextMenuItem, ExternalInterface, HTTPComms,
  JSONReader, JSONViewer,
  SwtAlert,
  SwtButton,
  SwtCanvas,
  SwtCommonGrid, SwtLabel,
  SwtLoadingImage,
  SwtModule, SwtPopUpManager, SwtTextInput, SwtToolBoxModule, SwtUtil, Alert, Encryptor
} from "swt-tool-box";
import {RouterModule, Routes} from "@angular/router";
import {AddSumWindow} from "./AddSumWindow/AddSumWindow";

@Component({
  selector: 'app-personalentitylist',
  templateUrl: './PersonalEntityList.html',
  styleUrls: ['./PersonalEntityList.css']
})
export class PersonalEntityList extends SwtModule implements OnInit {

  @ViewChild('dataGridContainer') dataGridContainer: SwtCanvas;
  @ViewChild('cvSaveOrCancel') cvSaveOrCancel: SwtCanvas;
  @ViewChild('btnOk') btnOk: SwtButton;
  @ViewChild('addSumButton') addSumButton: SwtButton;
  @ViewChild('changeButton') changeButton: SwtButton;
  @ViewChild('deleteButton') deleteButton: SwtButton;
  @ViewChild('btnCancel') btnCancel: SwtButton;
  @ViewChild('loadingImage') loadingImage: SwtLoadingImage;
  @ViewChild('lostConnectionText') lostConnectionText: SwtLabel;
  private swtAlert: SwtAlert;
  public configGrid: SwtCommonGrid;
  public jsonReader: JSONReader = new JSONReader();
  public lastRecievedJSON;
  public prevRecievedJSON;
  private actionMethod: string = "";
  private actionPath: string = "";
  public inputData = new HTTPComms(this.commonService);
  public sendData = new HTTPComms(this.commonService);
  public deleteData = new HTTPComms(this.commonService);
  public updateRefreshRate = new HTTPComms(this.commonService);
  public baseURL: string = SwtUtil.getBaseURL();
  private  requestParams = [];
  private  saveFlag:boolean=true;
  //private  titleWindow:any;
  private  cropNameText : SwtTextInput;
  private  amountText : SwtTextInput;
  private  deletestring:string = "";
  private  listOfSumEntitiesFromDB = [];
  private menuAccessIdParent: number = 0;
  public showJSONPopup: any;
  private win:any;


  constructor(private commonService: CommonService, private element: ElementRef) {
    super(element, commonService);
    this.swtAlert = new SwtAlert(commonService);
  }

  ngOnInit() {
    this.btnOk.label = SwtUtil.getPredictMessage("label.personalEntityList.button.ok", null);
    this.btnOk.toolTip = SwtUtil.getPredictMessage("tooltip.personalEntityList.button.ok", null);
    this.addSumButton.label = SwtUtil.getPredictMessage("label.personalEntityList.button.addsum", null);
    this.addSumButton.toolTip = SwtUtil.getPredictMessage("tooltip.personalEntityList.button.addsum", null);
    this.changeButton.label = SwtUtil.getPredictMessage("label.personalEntityList.button.modifysum", null);
    this.changeButton.toolTip = SwtUtil.getPredictMessage("tooltip.personalEntityList.button.modifysum", null);
    this.deleteButton.label = SwtUtil.getPredictMessage("label.personalEntityList.button.deletesum", null);
    this.deleteButton.toolTip = SwtUtil.getPredictMessage("tooltip.personalEntityList.button.deletesum", null);
    this.btnCancel.label = SwtUtil.getPredictMessage("label.personalEntityList.button.cancel", null);
    this.btnCancel.toolTip = SwtUtil.getPredictMessage("tooltip.personalEntityList.button.cancel", null);
    this.lostConnectionText.text =SwtUtil.getPredictMessage("screen.connectionError", null)
  }
  onLoad():void
  {
    this.configGrid = <SwtCommonGrid>this.dataGridContainer.addChild(SwtCommonGrid);
    this.configGrid.uniqueColumn="entityid";
    this.configGrid.editable = true;
    this.initializeMenus();
    this.inputData.cbStart= this.startOfComms.bind(this);
    this.inputData.cbStop=this.endOfComms.bind(this);
    this.inputData.cbResult= (event) => {
      this.inputDataResult(event);
    };
    this.inputData.cbFault= this.inputDataFault.bind(this);
    this.inputData.encodeURL=false;

    this.sendData.cbStart= this.startOfComms.bind(this);
    this.sendData.cbStop= this.endOfComms.bind(this);
    this.sendData.cbResult= (event) => {
      this.sendDataResult(event);
    };
    this.sendData.cbFault=this.sendDataFault.bind(this);
    this.sendData.encodeURL=false;

    this.deleteData.cbStart=this.startOfComms.bind(this);
    this.deleteData.cbStop=this.endOfComms.bind(this);
    this.deleteData.cbResult= (event) => {
      this.deleteDataResult(event);
    };
    this.deleteData.cbFault=this.sendDataFault.bind(this);
    this.deleteData.encodeURL=false;
    this.configGrid.ITEM_CLICK.subscribe((selectedRowData) => {
      this.cellLogic(selectedRowData);
    });

    this.menuAccessIdParent= ExternalInterface.call('eval', 'menuAccessIdParent');
    /*if (this.menuAccessIdParent == 1)
    {
      // disable the button
      this.btnOk.enabled=false;
    }*/
    this.actionPath="entityMonitor.do?";
    this.actionMethod="method=displayPersonalEntityList";
    //this.actionMethod += '&fromFlex = true';
    this.inputData.url= this.baseURL + this.actionPath + this.actionMethod;
    this.sendData.url=this.baseURL + this.actionPath + "method=savePersonalEntityList";
    this.deleteData.url=this.baseURL + this.actionPath + "method=deletePersonalSumEntityList";

    this.inputData.send(this.requestParams);
  }
  cellLogic(e):void {

    if (this.configGrid.selectedIndex != -1)
    {
      if(e.target.field =="display")
        this.btnCancel.enabled = true;
      let aggregate = this.configGrid.dataProvider[this.configGrid.selectedIndex].slickgrid_rowcontent.entityid.aggregate;
      if (aggregate == "Y") {
        this.changeButton.enabled = true;
        this.deleteButton.enabled = true;
      } else {
        this.changeButton.enabled = false;
        this.deleteButton.enabled = false;
      }
    }
    else {
      this.changeButton.enabled = false;
      this.deleteButton.enabled = false;

    }
  }
  initializeMenus(): void {
    //this.screenVersion.loadScreenVersion(this, thi, this.versionNumber, this.releaseDate);
    let addMenuItem: ContextMenuItem = new ContextMenuItem('Show JSON');
    addMenuItem.MenuItemSelect = this.showGridJSON.bind(this);
    /*this.screenVersion.svContextMenu.customItems.push(addMenuItem);
    this.contextMenu=this.screenVersion.svContextMenu;*/
  }

  showGridJSON(event): void {

    this.showJSONPopup = SwtPopUpManager.createPopUp(this,
      JSONViewer,
      {
        jsonData: this.lastRecievedJSON,
      });
    this.showJSONPopup.width = "700";
    //this.showJSONPopup.title = "Last Received JSON";
    this.showJSONPopup.height = "400";
    this.showJSONPopup.enableResize = false;
    this.showJSONPopup.showControls = true;
    this.showJSONPopup.display();
  }
  sendDataFault(event):void {
    this.lostConnectionText.visible=true;
    //invalidComms=event.fault.faultstring + "\n" + event.fault.faultCode + "\n" + event.fault.faultDetail;
    this.swtAlert.error('Error')
  }
  
 inputDataResult(event):void {
    if (this.inputData.isBusy())
    {
      this.inputData.cbStop();
    } else {
      this.btnCancel.enabled=false;
      this.lastRecievedJSON=event;
      this.jsonReader.setInputJSON(this.lastRecievedJSON);
      this.lostConnectionText.visible=false;
      if (this.jsonReader.getRequestReplyStatus())
      {
        if ((this.lastRecievedJSON != this.prevRecievedJSON)) {
          const obj = {columns: this.jsonReader.getColumnData()};
          this.configGrid.CustomGrid(obj);
          this.configGrid.gridData=this.jsonReader.getGridData();
          this.configGrid.setRowSize=this.jsonReader.getRowSize();
          this.prevRecievedJSON=this.lastRecievedJSON;
          this.listOfSumEntitiesFromDB = this.getListOfSumEntitiesFromDB();
        }
        if (!this.saveFlag)
        {
          ExternalInterface.call("closeWindow");
        }
      }
      this.prevRecievedJSON = this.lastRecievedJSON;
    }
  }
  updateData():void {
    this.inputData.send(this.requestParams);
  }
  inputDataFault(event):void {
    this.lostConnectionText.visible=true;
    //invalidComms=event.fault.faultstring + "\n" + event.fault.faultCode + "\n" + event.fault.faultDetail;
    this.swtAlert.error('Error')
  }
  startOfComms():void {
    this.loadingImage.setVisible(true);

  }
  endOfComms():void {
    this.loadingImage.setVisible(false);
  }

   saveHandle():void
  {
    this.btnOk.enabled=false;
    this.btnOk.buttonMode=false;
    let updatestring = [];
    let aggregatestring = [];
    let requestParams = [];
    let changes = this.configGrid.changes.getValues();
    for (let i=0; i< changes.length; i++)
    {
      let crudOpList = changes[i].crud_operation.split('>');
      if(crudOpList == "D") {
        requestParams['entityId'] = changes[i].crud_data.entityid;
      } else {
        for (let k = 0; k < crudOpList.length; k++) {
          let columnName = crudOpList[k].substring(2, crudOpList[k].length - 1);
          if(changes[i].crud_data[columnName])
          requestParams[changes[i].crud_data.entityid+"_"+columnName] = changes[i].crud_data[columnName];
        }
        updatestring.push(Encryptor.encode64(changes[i].crud_data.entityid.toString()));
        if(changes[i].crud_data.entityname.includes("Aggregation")) {
          requestParams[changes[i].crud_data.entityid+"_display"] = changes[i].crud_data.slickgrid_rowcontent["display"].content;
          requestParams[changes[i].crud_data.entityid+"_displaydays"] = changes[i].crud_data.slickgrid_rowcontent["displaydays"].content;
          requestParams[changes[i].crud_data.entityid+"_priority"] = changes[i].crud_data.slickgrid_rowcontent["priority"].content;
          aggregatestring.push("Y");//temporaire
        }

        else
          aggregatestring.push("N");//temporaire
      }
    }
    // set the keys to request
    if(updatestring.length > 0) {
      requestParams["update"]=updatestring.toString();
      requestParams["aggregate"]=aggregatestring.toString();
    }

    if(changes.length > 0)// (this.configGrid.originalDataprovider.length != this.configGrid.dataProvider.length)
    {
      this.sendData.send(requestParams);
    }
    else
    {
      // call the close window method in jsp
      ExternalInterface.call("closeWindow");
    }
  }
  sumWindowAdd():void
  {
    this.win =  SwtPopUpManager.createPopUp(this, AddSumWindow, {
      title:"Add Sum Entity", //SwtUtil.getPredictMessage('cutOff.title.addRule', null), // childTitle,
      cameFrom: 'Add',
    });
    this.win.isModal = true;
    this.win.enableResize = false;
    this.win.width = '500';
    this.win.height = '435';
    this.win.showControls = true;
    this.win.id = "addWindow";
    this.win.onClose.subscribe((res) => {
    });

    this.win.display();
    /*let addSumWindow:AddSumWindow = new AddSumWindow();
    addSumWindow.cameFrom = "Add";
    PopUpManager.addPopUp(addSumWindow, this, true);
    PopUpManager.centerPopUp(addSumWindow);*/
  }
  /**
   *  This function saves the Personal Currency List
   */
  sumWindowChange():void {
    let entityId:string = null;
    let entityName:string = null;
    let entityNotSaved:string = null;
    let entitiesList:string = null;
    if (this.configGrid.selectedIndex != -1)
    {
      entityId = this.configGrid.dataProvider[this.configGrid.selectedIndex].entityid;
      entityName = this.configGrid.dataProvider[this.configGrid.selectedIndex].entityname;
      entityNotSaved = this.configGrid.dataProvider[this.configGrid.selectedIndex].notSaved;
      entitiesList = this.configGrid.dataProvider[this.configGrid.selectedIndex].entitiesList;
    }
    this.win =  SwtPopUpManager.createPopUp(this, AddSumWindow, {
      title: "Change Sum Entity", //SwtUtil.getPredictMessage('cutOff.title.addRule', null), // childTitle,
      entityId : entityId,
      entityName :entityName,
      entityNotSaved: entityNotSaved,
      entitiesList : entitiesList,
      cameFrom : "Change"
    });
    this.win.isModal = true;
    this.win.enableResize = false;
    this.win.width = '500';
    this.win.height = '435';
    this.win.showControls = true;
    this.win.id = "addWindow";
    this.win.onClose.subscribe((res) => {
    });

    this.win.display();
  }
   sumDelete():void {
    let alert = SwtUtil.getPredictMessage("alert.personalEntityList.deletesum", null)
    this.swtAlert.question(
      alert, //ExternalInterface.call('getBundle', 'alert', 'delete-sum', 'Are you sure to delete sum Entity?')
      'Confirm Deletion' , //ExternalInterface.call('getBundle', 'alert', 'delete-confirm', 'Confirm Deletion')
      Alert.YES | Alert.NO, null,
      this.confirmSumDelete.bind(this)); //icon and default button
  }
  confirmSumDelete(event):void {
    if (event.detail == Alert.YES)
    {
      let entityId:string = this.configGrid.dataProvider[this.configGrid.selectedIndex].entityid;
      this.requestParams = [];
      this.requestParams["entityId"] = entityId;
      this.deleteData.send(this.requestParams);
      this.configGrid.removeSelected();
      this.configGrid.selectedIndex = -1;
      this.changeButton.enabled = false;
      this.deleteButton.enabled = false;
    }
  }
  entityExists(entityId:string):boolean {
    for (let i:number = 0; i < this.configGrid.dataProvider.length; i++)
    {
      if (this.configGrid.dataProvider[i].entityid == entityId)
      {
        return true;
      }
    }
    return false;
  }
  
   sendDataResult(event):void {
    if (this.inputData.isBusy()) {
      this.inputData.cbStop();
    } else {
      this.lastRecievedJSON= event;
      this.jsonReader.setInputJSON(this.lastRecievedJSON);
      if (this.jsonReader.getRequestReplyStatus()) {
        this.saveFlag=false;
        this.updateData();
      }
    }
  }
  deleteDataResult(event):void {
    if (this.inputData.isBusy())
    {
      this.inputData.cbStop();
    } else {
      if (!this.jsonReader.getRequestReplyStatus())
      {
        this.swtAlert.error('Error occurred, Please contact your System Administrator: \n'
          + this.jsonReader.getRequestReplyMessage(), 'Error');
      }
    }
  }

  cancelHandle():void {
    ExternalInterface.call("close");
  }
   addChangeSumEntityToGrid(entityId:string, entityName:string, updatestring:string):void {
    this.changeButton.enabled = false;
     this.deleteButton.enabled = false;
    let rowSize:number = this.jsonReader.getRowSize();
    let entityFound:boolean = false;
    for (let i:number = 0; i < this.configGrid.dataProvider.length; i++)
    {
      if (this.configGrid.dataProvider[i].entityid == entityId)
      {
        this.configGrid.dataProvider[i].entityname = entityName;
        this.configGrid.dataProvider[i].notSaved = "true";
        this.configGrid.dataProvider[i].entitiesList = updatestring;

        entityFound = true;
        this.configGrid.refresh();

      }
    }
    if (!entityFound)
    {
      var newRow ={
        'notSaved' : {'content' : "true"},
        'entitiesList' :{'content' :  String(updatestring)},
        'entityid' :{'content' :  String(entityId), 'aggregate': "Y"},
        'entityname' :{'content' :  String(entityName)},
        'display' :{'content' :  "N", 'selected': 'false'},
        'priority' :{'content' : 500},
        'displaydays' :{'content' :  1},
      };
      this.configGrid.appendRow(newRow);
      this.configGrid.dataProvider[this.configGrid.dataProvider.length -1].slickgrid_rowcontent.entityid.aggregate = 'Y';
      this.configGrid.dataProvider[this.configGrid.dataProvider.length -1].slickgrid_rowcontent.display.selected = 'false';
      //check fatma
    }
     this.changeButton.enabled = true;
     this.deleteButton.enabled= true;


  }

   getListOfSumEntitiesFromDB():any {
    let array = [];
    for (let i:number = 0; i < this.configGrid.dataProvider.length; i++)
    {
      if (this.configGrid.dataProvider[i].entityid.aggregate == "Y")
      {
        array.push(this.configGrid.dataProvider[i].entityid);
      }
    }
    return array;
  }

  doHelp() {
    ExternalInterface.call('help')
  }

}
const routes: Routes = [
  { path: '', component: PersonalEntityList }
];
export const routing: ModuleWithProviders = RouterModule.forChild(routes);
//Define an NgModule that will be loaded by app module
@NgModule({
  imports: [routing, SwtToolBoxModule],
  declarations: [PersonalEntityList],
  entryComponents: []
})
export class PersonalEntityListModule { }

