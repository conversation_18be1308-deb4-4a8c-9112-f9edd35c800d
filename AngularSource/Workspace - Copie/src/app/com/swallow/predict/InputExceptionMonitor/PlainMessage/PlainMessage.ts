import { Component, ElementRef, ModuleWithProviders, NgModule, OnDestroy, OnInit, ViewChild } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import {
  CommonService,
  ExternalInterface,
  HashMap,
  HTTPComms,
  SwtModule,
  SwtTextArea,
  SwtToolBoxModule,
  SwtUtil,
} from 'swt-tool-box';

declare var require: any;
var parser = require('fast-xml-parser');
var prettyData = require('pretty-data');
@Component({
  selector: 'app-plainmessage',
  templateUrl: './PlainMessage.html',
  styleUrls: ['./PlainMessage.css']
})
export class PlainMessage extends SwtModule implements OnInit {

  public messageRPC = new HTTPComms(this.commonService);
  @ViewChild('messageForm') messageForm: SwtTextArea;
  private d: any;
  private  fromPCM: string='';
  private baseURL = SwtUtil.getBaseURL();
  constructor(private commonService: CommonService, private element: ElementRef) {
    super(element, commonService);

  }

  onLoad() {
    this.fromPCM = ExternalInterface.call('eval', 'fromPCM');
    this.messageRPC.url = this.baseURL + "inputexceptionsmessages.do?method=messageData&amp;";
    this.messageRPC.url = this.messageRPC.url + 'fromPCM=' + this.fromPCM + '&';

    this.d = this.getUrlParams();
    var messageParams : any;
    var messageSeqId:String = this.d["seqid"];
    messageParams = {seqid:messageSeqId };
    this.messageRPC.cbResult = (event) => {
      this.messageRPCResult(event)
    };
    this.messageRPC.send(messageParams);

  }
   messageRPCResult (event) :void {
    try
    {
      let message = event.inputexceptions.message;
      message= message.split("$#$").join("\n");
      message = message.split("&@&").join("&nbsp;");
      let messageAsXML = prettyData.pd.xml(message.split('&nbsp;').join(' '));
      if( parser.validate(messageAsXML) !== true){
        this.messageForm.htmlText = messageAsXML;
      } else {
        messageAsXML = this.htmlEntities(messageAsXML);
        this.messageForm.htmlText = messageAsXML;
      }



    }
    catch(error)
    {
     // messageForm.text = message.replace(/\r/g, '');
    }
  }
  htmlEntities(str) {
    try {
      return String(str).replace(/&/g, '&amp;').replace(/</g, '&lt;').
      replace(/>/g, '&gt;').replace(/"/g, '&quot;').replace(/ /g, '&nbsp;');
    } catch (e) {
      console.log('error', e, str)
    }
  }
  getUrlParams(): {} {
    var urlParams: HashMap;
    var fullUrl: String = ExternalInterface.call("document_location_href");
    var paramStr: String = fullUrl.split('?')[1];

    // we'll store the parameters here
    var obj = {};

    // if query string exists
    if (paramStr) {

      // stuff after # is not part of query string, so get rid of it
      paramStr = paramStr.split('#')[0];
      // split our query string into its component parts
      var arr = paramStr.split('&');
      for (var i = 0; i < arr.length; i++) {
        // separate the keys and the values
        var a = arr[i].split('=');
        // set parameter name and value (use 'true' if empty)
        var paramName = a[0];
        var paramValue = typeof (a[1]) === 'undefined' ? true : a[1];
        //if (typeof paramValue === 'string') paramValue = paramValue.toLowerCase();
        // if the paramName ends with square brackets, e.g. colors[] or colors[2]
        if (paramName.match(/\[(\d+)?\]$/)) {
          // create key if it doesn't exist
          var key = paramName.replace(/\[(\d+)?\]/, '');
          if (!obj[key]) obj[key] = [];

          // if it's an indexed array e.g. colors[2]
          if (paramName.match(/\[\d+\]$/)) {
            // get the index value and add the entry at the appropriate position
            var index = /\[(\d+)\]/.exec(paramName)[1];
            obj[key][index] = paramValue;
          } else {
            // otherwise add the value to the end of the array
            obj[key].push(paramValue);
          }
        } else {
          // we're dealing with a string
          if (!obj[paramName]) {
            // if it doesn't exist, create property
            obj[paramName] = paramValue;
          } else if (obj[paramName] && typeof obj[paramName] === 'string') {
            // if property does exist and it's a string, convert it to an array
            obj[paramName] = [obj[paramName]];
            obj[paramName].push(paramValue);
          } else {
            // otherwise add the property
            obj[paramName].push(paramValue);
          }
        }
      }
    }
    return obj;
  }

}
//Define lazy loading routes
const routes: Routes = [
  { path: '', component: PlainMessage }
];
export const routing: ModuleWithProviders = RouterModule.forChild(routes);
//Define an NgModule that will be loaded by app module
@NgModule({
  imports: [routing, SwtToolBoxModule],
  declarations: [PlainMessage],
  entryComponents: []
})
export class PlainMessageModule { }
