import { Compo<PERSON>, <PERSON>ement<PERSON><PERSON>, <PERSON><PERSON><PERSON>roy, OnInit, ViewChild, ModuleWithProviders, NgModule } from '@angular/core';
import {
  Alert,
  CommonService,
  ExternalInterface,
  focusManager,
  HTTPComms,
  JSONReader,
  Keyboard,
  SwtAlert,
  SwtButton,
  SwtCanvas,
  SwtCommonGrid,
  SwtLabel,
  SwtLoadingImage,
  SwtModule,
  SwtPopUpManager,
  SwtUtil,
  TitleWindow,
  SwtToolBoxModule,
  SwtDateField,
  CommonUtil,
  SwtNumericInput,
  Timer,
  SwtDataExport,
  ExportEvent,
  ScreenVersion, ContextMenuItem, JSONViewer, Encryptor
} from 'swt-tool-box';
import { Routes, RouterModule } from '@angular/router';
import moment from 'moment';
import {OptionsPopUp} from "../../pcm/OptionsPopUp/OptionsPopUp";
declare let instanceElement: any;

@Component({
  selector: 'app-input-exception',
  templateUrl: './InputException.html',
  styleUrls: ['./InputException.css']
})
export class InputException extends SwtModule implements OnInit, OnDestroy {

  @ViewChild('canvasGrid') canvasGrid: SwtCanvas;
  @ViewChild('loadingImage') loadingImage: SwtLoadingImage;
  /********SwtButton*********************/
  @ViewChild('refreshButton') refreshButton: SwtButton;
  @ViewChild('rateButton') rateButton: SwtButton;
  @ViewChild('closeButton') closeButton: SwtButton;
  @ViewChild('csv') csv: SwtButton;
  @ViewChild('excel') excel: SwtButton;
  @ViewChild('pdf') pdf: SwtButton;
  @ViewChild('helpIcon') helpIcon: SwtButton;
  /***********Swtlabel**************/
  @ViewChild('daysLabel') daysLabel: SwtLabel;
  @ViewChild('dataBuildingText') dataBuildingText: SwtLabel;
  @ViewChild('lostConnectionText') lostConnectionText: SwtLabel;
  @ViewChild('lastRefTime') lastRefTime: SwtLabel;
  /***********TextInput**************/
  @ViewChild('showDays') showDays: SwtNumericInput;
  /***********TextInput**************/
  @ViewChild('startDate') startDate: SwtDateField;

  @ViewChild('dataExport') dataExport: SwtDataExport;

  private exceptionsGrid: SwtCommonGrid;
  /**
   * Data Objects
   **/
  public jsonReader: JSONReader = new JSONReader();
  public lastRecievedJSON;
  public prevRecievedJSON;
  /**
   * Communication Objects
   **/
  public inputData = new HTTPComms(this.commonService);
  private updateRefreshRate = new HTTPComms(this.commonService);
  public requestParams = [];
  private baseURL = SwtUtil.getBaseURL();
  private actionMethod = '';
  private actionPath = '';
  private invalidComms : string;

  /* - START -- Screen Name and Version Number ---- */
  private moduleName = 'Input Exception';
  private versionNumber = '1.00.00';
  private releaseDate = '03 September 2019';
  public screenVersion  = new ScreenVersion(this.commonService);
  /* - END -- Screen Name and Version Number ---- */

  private swtAlert: SwtAlert;
  public helpURL: string = null;
  private errorLocation = 0;
  public moduleId = '';
  public searchQuery = '';
  public searchFlag = false;
  public queryToDisplay = '';
  private win:TitleWindow;
  private showJSONPopup:any;
  public screenName;

  /**
   * Timer Objects
   **/
  //Main Timer.
  private autoRefresh: Timer;
  private refreshRate: number = 10;

  //Background refresh
  //private backGroundTimer: BackgroundTimer = new BackgroundTimer();

  /**
   * Logic Objects
   **/
  private comboOpen: boolean = false;

  /**
   * Popup Objects
   **/
  //private showXML: ShowXML;
  //private refreshRatePopup: RefreshPopUp;

  private systemDate:string;
	private dateCompare:string="";
	private sessionToDate:string;

  private fromDate:Date;
	private toDate:Date;
	private prevChosenDate:Date;
	private prevNumberOfDays:string;
  private dateFormat:string;
  private testDate:string;
  private fromPCM:String = "false";
  private showBuildInProgress = false;

  constructor(private commonService: CommonService, private element: ElementRef) {
    super(element, commonService);
    this.swtAlert = new SwtAlert(commonService);
  }

  ngOnDestroy(): any {
    instanceElement = null;
  }



  ngOnInit(): void {
    this.lostConnectionText.visible = false;
    this.dataBuildingText.visible = false;
    instanceElement = this;
    this.refreshButton.label = SwtUtil.getPredictMessage("inputException.refresh", null);
    this.refreshButton.toolTip = SwtUtil.getPredictMessage("tooltip.refreshWindow", null);
    this.rateButton.label = SwtUtil.getPredictMessage("inputException.rate", null);
    this.rateButton.toolTip = SwtUtil.getPredictMessage("tooltip.RateWindow", null);
    this.closeButton.label = SwtUtil.getPredictMessage("inputException.close", null);
    this.closeButton.toolTip = SwtUtil.getPredictMessage("tooltip.close", null);
  }
  /**
	  * Upon completion of loading into the flash player this method is called
		**/
  onLoad() {
    this.exceptionsGrid = <SwtCommonGrid>this.canvasGrid.addChild(SwtCommonGrid);
    this.exceptionsGrid.lockedColumnCount = 1;
    this.exceptionsGrid.uniqueColumn = "interface";
    try {
      // Is screen for PCM ?
      this.fromPCM = ExternalInterface.call('eval', 'fromPCM');

      //Initialize the context menu
      this.initializeMenus();
      this.dateFormat= ExternalInterface.call('eval', 'dateFormat');

      /*Get and stores the testDate from jsp*/
      this.systemDate= ExternalInterface.call('eval', 'dbDate');
      this.testDate=this.systemDate;

      this.startDate.selectedDate=new Date(this.testDate);
      this.startDate.formatString=this.dateFormat;
      this.inputData.cbStart = this.startOfComms.bind(this);
      this.inputData.cbStop = this.endOfComms.bind(this);
      this.actionPath = 'inputexceptions.do?fromPCM=' + this.fromPCM + '&';
      this.actionMethod = 'method=summaryData';
      this.requestParams['moduleId'] = this.moduleId;
      this.inputData.cbResult = (data) => {
        this.inputDataResult(data);
      };

      //this.inputDataResult(data);

      this.inputData.cbFault = this.inputDataFault.bind(this);
      this.inputData.encodeURL = false;
      this.inputData.url = this.baseURL + this.actionPath + this.actionMethod;
      //Add the event listener to listen for a cell click on a datagrid, be it the main one or a totals grid
      this.exceptionsGrid.ITEM_CLICK.subscribe((selectedRowData) => {
        this.cellLogic(selectedRowData);
      });
      ExportEvent.subscribe((type) => {
        this.export(type);
      });

      //Make initial request
      this.requestParams["fromDate"] = this.testDate;
      this.requestParams["toDate"] = this.testDate;
      this.requestParams["systemDate"] = this.systemDate;
      this.requestParams["autoRefresh"] = "no";
      this.inputData.send(this.requestParams);
    } catch (error) {
        console.log("error", error);
    }
  }
  /**The function initializes the menus in the right click event on the metagroup monitor screen.
   * The links are redirected to their respective pages.
   */
  initializeMenus(): void {
    this.screenVersion.loadScreenVersion(this, 'Input Exception Screen', this.versionNumber, this.releaseDate);
    let addMenuItem: ContextMenuItem =new ContextMenuItem(SwtUtil.getPredictMessage("screen.showXML", null));
    addMenuItem.MenuItemSelect = this.showGridJSON.bind(this);
    this.screenVersion.svContextMenu.customItems.push(addMenuItem);
    this.contextMenu=this.screenVersion.svContextMenu;
  }


  /** This function is used to display the JSON
   *    for data showed in grid
   */
  showGridJSON(): void {

    this.showJSONPopup = SwtPopUpManager.createPopUp(this,
      JSONViewer,
      {
        jsonData: this.lastRecievedJSON,
      });
    this.showJSONPopup.width = "700";
    this.showJSONPopup.title = "Last Received JSON";
    this.showJSONPopup.height = "400";
    this.showJSONPopup.enableResize = false;
    this.showJSONPopup.showControls = true;
    this.showJSONPopup.display();
  }

  /**
   * startOfComms
   *
   * Part of a callback function to all for control of the loading swf from the HTTPComms Object
   */
  startOfComms(): void {
    try {
      this.loadingImage.setVisible(true);
      if (this.showBuildInProgress == true)
      this.dataBuildingText.visible = true;
      this.disableInterface();
      this.startDate.enabled = false;
     this.showDays.enabled = false; //hidden because it displays id as prompt
    } catch (e) {
      // log the error in ERROR LOG
      SwtUtil.logError(e, this.moduleId, 'ClassName', 'startOfComms', this.errorLocation);
    }
  }

  /**
   * endOfComms
   *
   * Part of a callback function to all for control of the loading swf from the HTTPComms Object
   */
  endOfComms(): void {
    try {
      this.loadingImage.setVisible(false);
      this.enableInterface();
      this.startDate.enabled=true;
			this.showDays.enabled=true;
    } catch (e) {
      // log the error in ERROR LOG
      SwtUtil.logError(e, this.moduleId, 'ClassName', 'endOfComms', this.errorLocation);
    }
  }

  /**
   * inputDataResult
   *
   * @param data: ResultEvent
   *
   * This is a callback method, to handle result event
   *
   */
  public inputDataResult(data): void {
    try {
      if (this.inputData.isBusy()) {
        this.inputData.cbStop();
      } else {
        this.lastRecievedJSON = data;
        this.jsonReader.setInputJSON(this.lastRecievedJSON);
				this.lostConnectionText.visible=false;
        //FIXME:
        this.dataExport.enabled=true;
        let lastRefAsString: string = this.jsonReader.getScreenAttributes()["lastRefTime"];
        lastRefAsString = this.convertFromUnicodeToString(lastRefAsString);
        this.lastRefTime.text = lastRefAsString;
        if (this.lastRecievedJSON != this.prevRecievedJSON) {
          if (this.jsonReader.getRequestReplyStatus()) {
            // Condition to check data is building
            if (!this.jsonReader.isDataBuilding()) {
              //If the code has reached this point then the database is not databuilding, turn off the dataBuildingText
              this.dataBuildingText.visible = false;
              //If the previousJSON is different to new JSON then allow an update. Otherwise leave it alone
              if (!(JSON.stringify(this.lastRecievedJSON) === JSON.stringify(this.prevRecievedJSON))) {
                this.sessionToDate = this.jsonReader.getScreenAttributes()["sessionToDate"];
                this.systemDate = this.jsonReader.getScreenAttributes()["sysDateFrmSession"];
                this.dateCompare = (this.jsonReader.getScreenAttributes()["dateComparing"]);
                this.refreshRate = parseInt(this.jsonReader.getRefreshRate());
                if (this.dateCompare == "true") {


                }
                this.startDate.showToday = false;
                this.fromDate = moment(this.jsonReader.getScreenAttributes()["from"], this.dateFormat.toUpperCase()).toDate();
                this.prevChosenDate = this.fromDate;
                this.toDate = moment(this.jsonReader.getScreenAttributes()["to"], this.dateFormat.toUpperCase()).toDate();

                this.startDate.selectedDate = this.fromDate;
                let daysInMilliseconds: number = 1000 * 60 * 60 * 24;
                this.showDays.text = Math.round((this.toDate.getTime() - this.fromDate.getTime()) / daysInMilliseconds + 1).toString();
                this.prevNumberOfDays = this.showDays.text;
                this.updateDayLabel();

                let dateFormat: String = this.jsonReader.getScreenAttributes()["dateformat"];

                if (dateFormat == "dd/MM/yyyy") {
                  this.startDate.toolTip = "Enter From date";//TODO:ExternalInterface.call('getBundle', 'tip', 'datefromDDMMYY', 'Enter From date(\'DD/MM/YYYY\')');
                } else
                  this.startDate.toolTip = "Enter To date";//TODO:ExternalInterface.call('getBundle', 'tip', 'datefromMMDDYY', 'Enter From date(\'MM/DD/YYYY\')');
                this.showDays.toolTip = "Number of days to show"//TODO:ExternalInterface.call('getBundle', 'tip', 'showdays', 'Number of days to show');

                this.exceptionsGrid.CustomGrid(data.inputexceptions.grid.metadata);

                // Triggers the auto refresh if it's null
                if (this.autoRefresh == null) {
                  this.autoRefresh = new Timer((this.refreshRate * 1000), 0);
                  this.autoRefresh.addEventListener("timer", this.dataRefresh.bind(this));
                } else {
                  // Sets delay to auto refresh and triggers it
                  this.autoRefresh.delay(this.refreshRate * 1000);
                }

                // Apply the data to the grid
                // this.exceptionsGrid.selectable = false;
                //this.exceptionsGrid.draggableColumns = false;
                this.exceptionsGrid.gridData = this.jsonReader.getGridData();
                this.exceptionsGrid.setRowSize = this.jsonReader.getRowSize();
                this.exceptionsGrid.colWidthURL(this.baseURL + "inputexceptions.do?fromPCM=" + ExternalInterface.call('eval', 'fromPCM') + "&");
                this.exceptionsGrid.saveWidths = true;

                this.showBuildInProgress = false;
              }
            } else {
              this.dataBuildingText.visible = true;
            }

            this.exceptionsGrid.selectedIndex = -1;

          }
          if (this.autoRefresh != null) {
            if (!this.autoRefresh.running) {

              this.autoRefresh.start();
            }
          }
        }
        this.prevRecievedJSON = this.lastRecievedJSON;
      }
    } catch (e) {
      // log the error in ERROR LOG
      console.log('error inputDataResult', e);
    }
  }


  convertFromUnicodeToString(strTmp) {
    let r = /\\u([\d\w]{4})/gi;
    strTmp = strTmp.replace(r, function (match, grp) {
      return String.fromCharCode(parseInt(grp, 16));
    });
    strTmp = unescape(strTmp);

    return strTmp;
  }

  /**
		* Timing result methods
		**/
  dataRefresh(event): void {
    //Check on the comboOpen flag, do not want to update if there is a combobox open as this would cause it to close
    if (!this.comboOpen) {
      if ((Object(focusManager.getFocus()).id == "showDays" || Object(focusManager.getFocus()).id == "startDate") && this.validateDate( this.startDate) && this.validateNumberOfDays(this.showDays)) {
        if ((this.showDays.text != this.prevNumberOfDays) || (this.startDate.selectedDate.toDateString() != this.prevChosenDate.toDateString()))
          setTimeout(() => {
            this.updateData('yes')
          }, 0);
        else
          setTimeout(() => {
            this.updateData('yes', true)
          }, 0)

        this.refreshButton.setFocus();
      }
      else if (Object(focusManager.getFocus()).id != "showDays" && Object(focusManager.getFocus()).id != "startDate" && this.validateDate( this.startDate) && this.validateNumberOfDays(this.showDays))
        this.updateData('yes', true);
    }

    this.autoRefresh.stop();
  }

  /**
		 * This function validates the date field - Mantis 1262. <br>
		 * The date field is an editable one where the user can type the desired date.<br>
		 * The date is taken as an argument and it is validate against certain rules.<br>
		 *
		 * Author: Marshal.<br>
		 * Date: 19-10-2010<br>
		 */
  validateDate(startDay: SwtDateField): boolean {

    try{
      let date;
      let alert = SwtUtil.getPredictMessage('alert.enterValidDate', null);
      if(startDay.text) {
        date = moment(startDay.text, this.dateFormat.toUpperCase() , true);
        if(!date.isValid()) {
          this.swtAlert.error(alert, null, null, null,  ()=> {
            this.setFocusDateField(startDay)
          });
          return false;
        }
      } else {
        this.swtAlert.error(alert, null, null, null,  ()=> {
          this.setFocusDateField(startDay)
        });
        return false;
      }
      startDay.selectedDate = date.toDate();
    }catch(error){
      console.log('error in validateDateField', error);
    }

    return true;
  }
  setFocusDateField(dateField) {
    dateField.setFocus();
    dateField.text = this.jsonReader.getScreenAttributes()["from"]
  }

  validateNumberOfDays(showDays): Boolean {

    let parsedDays = parseInt(showDays.text);

    if (isNaN(parsedDays) || parsedDays <= 0) {
      this.showAlertForNumberOfDays(showDays);
      return false;
    } else
      return true;

  }

  /**
   * Displays an alert if the user choses an invalid show days value
   **/
  showAlertForNumberOfDays(showDays): void {
    this.swtAlert.error(SwtUtil.getPredictMessage("inputException.showValue", null), "Error", Alert.OK, null, function (event: CloseEvent): void {
      showDays.setFocusAndSelect();
    });
  }

  /**
			 * Added by KaisBS and Meftah for Mantis 2016 (Enhanced date range selection on all screens with a From and To Date field )
			 * Change the dayLabel to day/days according to the number in the showdays field
			 **/
  updateDayLabel(): void {
    if (parseInt(this.showDays.text) == 0 || parseInt(this.showDays.text) == 1) {
      //TODO:
      this.daysLabel.text = SwtUtil.getPredictMessage("text.day", null);
    } else
    this.daysLabel.text = SwtUtil.getPredictMessage("text.days", null);
  }
  /**
   * inputDataFault
   *
   * @param event:  FaultEvent
   *
   * This is a callback function, used to handle fault event.
   * Shows fault message in alert window.
   */
  inputDataFault(event): void {
    this.lostConnectionText.visible=true;
    /*TODO:this.lostConnectionText.buttonMode=true;
    this.lostConnectionText.useHandCursor=true;
    this.lostConnectionText.mouseChildren=false;*/
    this.invalidComms=event.fault.faultString + "\n" + event.fault.faultCode + "\n" + event.fault.faultDetail;

    if (this.autoRefresh != null) {
      if (!this.autoRefresh.running) {

        this.autoRefresh.start();
      }
    }
  }

  /**
   * printPage
   *
   * param event
   *
   * Method to get call the action to get reports
   */
  printPage(): void {
    try {
      this.actionMethod = "type=" + "pdf";
      this.actionMethod = this.actionMethod + '&action=' + 'EXPORT';
      this.actionMethod = this.actionMethod + "&currentModuleId=" + this.moduleId;
      this.actionMethod = this.actionMethod + "&print=" + "PAGE";
      ExternalInterface.call('getReports', this.actionPath + this.actionMethod);

    } catch (error) {
      // log the error in ERROR LOG
      SwtUtil.logError(error, this.moduleId, "className", "printPage", this.errorLocation);
    }
  }

  /**
     * closeCurrentTab
     *
     * Function called when close button is called
     *
     */
  cLoseHandler(): void {
    try {
      this.dispose();
    } catch (e) {
      SwtUtil.logError(e, SwtUtil.SYSTEM_MODULE_ID, 'ClassName', 'refreshGrid', this.errorLocation);
    }
  }

  /**
   * dispose
   *
   * This is an event handler, used to close the current tab/window
   */
  dispose(): void {
    try {
      this.exceptionsGrid = null;
      this.requestParams = null;
      this.inputData = null;
      this.jsonReader = null;
      this.lastRecievedJSON = null;
      this.prevRecievedJSON = null;
      this.searchQuery = '';
      this.searchFlag = false;
      ExternalInterface.call("close");
    } catch (e) {
      SwtUtil.logError(e, this.moduleId, 'ClassName', 'dispose', this.errorLocation);
    }
  }

  /**
  * report
  *
  * @param type: string
  *
  * This is a report icon action handler method
  */
  report(type: string): void {
    let selectedFilter: string = null;
    let selectedSort = '';
    let moduleId: string = null;
    try {
      moduleId = this.moduleId;
      if (this.exceptionsGrid.filteredGridColumns !== '') {
        selectedFilter = this.exceptionsGrid.getFilteredGridColumns();
      } else {
        selectedFilter = '';
      }
      selectedSort = this.exceptionsGrid.getSortedGridColumn();
      //set the action path
      this.actionMethod = 'method=displayReport';
      this.actionMethod = this.actionMethod + '&type=' + type;
      this.actionMethod = this.actionMethod + '&action=' + 'EXPORT';
      this.actionMethod = this.actionMethod + '&selectedFilter=' + selectedFilter;
      this.actionMethod = this.actionMethod + '&selectedSort=' + selectedSort;
      this.actionMethod = this.actionMethod + '&print=' + 'ALL';
      ExternalInterface.call('getReports', this.actionPath + this.actionMethod);
    } catch (e) {
      // log the error in ERROR LOG
      SwtUtil.logError(e, moduleId, 'ClassName', 'report', this.errorLocation);
    }
  }

  getParamsFromParent() {
    let spreadId = '';
    if(this.exceptionsGrid.selectedIndex>-1) {
      spreadId = this.exceptionsGrid.selectedItem.spreadId.content;
    }
    let params = [
      {screenName: this.screenName, spreadId:spreadId},
    ];
    return params;
  }

  /**
   * This is a key event listener, used to perform the operation
	 * when hit the enter key based on the currently focused property(button)
   * @param event
   */
  keyDownEventHandler(event: KeyboardEvent): void {
    //Currently focussed property name
    let eventString: String = Object(focusManager.getFocus()).id;
    if ((event.keyCode == Keyboard.ENTER)) {
      if (eventString == "refreshButton") {
        if (Object(focusManager.getFocus()).id == "showDays") {
          setTimeout(() => {
            this.updateData('yes');
          }, 0)
        } else if (Object(focusManager.getFocus()).id != "showDays")
        this.updateData('yes', true);
      }
      else if (eventString == "rateButton") {
        this.rateHandler();
      }
      else if (eventString == "closeButton") {
        this.cLoseHandler();
      }

    }
  }

  /**
   * Added by Mefteh for Mantis 2016
   * this method is called on clicking on refresh Button
   **/
  updateDatafromRefresh():void {
    if((Object(focusManager.getFocus()).id=="showDays" || (Object(focusManager.getFocus()).id=="startDate"))) {
      setTimeout(() => {
        this.updateData('yes');
      }, 0);
    }
    else if((Object(focusManager.getFocus()).id!="showDays" && Object(focusManager.getFocus()).id!="startDate")) {
      setTimeout(() => {
        this.updateData('yes',true);
      }, 0)
    }

  }

  rateHandler(): void {
    //clearInterval(this.interval);
    // Instantiates the OptionPopUp object
    this.win =  SwtPopUpManager.createPopUp(this, OptionsPopUp, {
      title: "Options",
      refreshText: this.refreshRate.toString()
    });
    this.win.isModal = true;
    this.win.enableResize = false;
    this.win.width = '340';
    this.win.height = '150';
    this.win.showControls = true;
    this.win.id = "optionsWindow";
    this.win.display();
  }
  saveRefreshRate(res):void
  {
    let minRate = SwtUtil.getPredictMessage("inputException.rateSelected", null);
    if (res == '' || res == null)
    {
      this.swtAlert.error(SwtUtil.getPredictMessage("inputException.notANumber", null), 'Error');
    }
    else
    {
      let selectedRateBelowMinimum:Boolean=false;
      this.refreshRate=Number(res);
      //refreshRate=Number(refreshRatePopup.refresh.text);
      if (this.refreshRate < 5)
      {
        //set the default rate
        this.refreshRate=5;
        selectedRateBelowMinimum=true;
      }
      /*if (autoRefresh)
        autoRefresh.delay=(refreshRate * 1000);*/
      //call the 	getUpdateRefreshRequest
      let request: string=ExternalInterface.call("getUpdateRefreshRequest", this.refreshRate);
      if (request != null && request != "")
      {
        this.updateRefreshRate.url = this.baseURL + request;
        this.updateRefreshRate.send();
      }
      //remove the popup window
      //PopUpManager.removePopUp(refreshRatePopup);
      //check rate is minimum
      if (selectedRateBelowMinimum)
      {
        this.swtAlert.warning(minRate);
      }
    }
  }
/**
	* Update the data, this is called whenever a fresh of the data is required.
	* This could be called from either a change in a combobox selection of from the timer
  * The update of data is realized in two cases:
  * 1- If we don't have an excess of the date range.
  * 2- If we have the excess of the date range and the user clicks OK from the displayed alert "The data for this date range selection may not be available...".
  **/
  updateData(autoRefreshFlag: string, fromCheckDateRange: boolean = false, cancelled: Boolean = false): void {
    this.fromDate = this.startDate.selectedDate;
    this.toDate = new Date(this.startDate.selectedDate);
    this.toDate.setDate(this.toDate.getDate() + parseInt(this.showDays.text) - 1);
    let fromDateAsMoment = moment(this.startDate.text, this.dateFormat.toUpperCase());
    let toDateAsMoment =  moment(this.startDate.text, this.dateFormat.toUpperCase()).add(Number(this.showDays.text) -1, 'days');

    if (cancelled) {
      this.startDate.selectedDate = this.prevChosenDate;
      this.fromDate = this.prevChosenDate;
      this.showDays.text = this.prevNumberOfDays;
      this.updateDayLabel();
      this.toDate.setDate(this.fromDate.getDate() + parseInt(this.showDays.text) - 1);
      this.showDays.setFocus();
      return;
    }

    if (!fromCheckDateRange && this.checkDateRange(autoRefreshFlag, fromDateAsMoment, toDateAsMoment, this.systemDate, this.dateFormat, this.updateData))
      return;
      this.prevChosenDate = this.fromDate;
      this.requestParams = [];

    if (this.fromDate && this.toDate) {
      let DateToStr =  moment(toDateAsMoment).format(this.dateFormat.toUpperCase());
      this.requestParams["fromDate"] = this.startDate.text;
      this.requestParams["toDate"] = DateToStr;
      this.requestParams["sessionToDate"] = this.sessionToDate;
      this.requestParams["autoRefresh"] = autoRefreshFlag;
      this.requestParams["systemDate"] = this.systemDate;
    }

    //Then apply them to the url member of the HTTPComms object:
    this.inputData.url = this.baseURL + this.actionPath + this.actionMethod;

    this.inputData.send(this.requestParams);
  }


  /**
		* Enable interface, turn on certain UI elements when a request is made to the server
		**/
  enableInterface(): void {
    this.refreshButton.enabled = true;
    this.refreshButton.buttonMode = true;
  }


  /**
    * Disable interface, turn off certain UI elements when a request is made to the server
    **/
  disableInterface(): void {
    this.refreshButton.enabled = false;
    this.refreshButton.buttonMode = false;
  }

  cellLogic(e): void {
    let fieldName = e.target.field;
    let isClickable = e.target.data.slickgrid_rowcontent[fieldName].clickable;
    let interfaceName = escape(e.target.data.interface);
    let sD1: any;
    let sD2: any;
    if(this.validateDate(this.startDate)) {
  let dateFormat: string = this.dateFormat;
  sD1 =  moment(this.startDate.text, this.dateFormat.toUpperCase()).format(this.dateFormat.toUpperCase());
  sD2 =  moment(this.startDate.text, this.dateFormat.toUpperCase()).add(Number(this.showDays.text) -1, 'days').format(this.dateFormat.toUpperCase());
  if (isClickable) {
    this.clickLink(e.target.data.slickgrid_rowcontent[fieldName], interfaceName, this.baseURL, sD1, sD2);
  }
  } else {

    }

  }

  /**
   * Method is used to open the input exceptions message details monitor
   * @param cellDTO :Object
   * @param baseURL :String
   * @param sD1 :String
   * @param sD2 :String
   **/
  clickLink (cellDTO :any, interfaceName: string, baseURL :string, sD1 :string, sD2 :string) :void {
    //assign the url path
    let urlString : string =  baseURL+"inputexceptionsmessages.do?fromPCM="+ExternalInterface.call('eval', 'fromPCM')+"&";
    //get the amount value
    try {


    let amount :number = Number(cellDTO.content);
    //declare the status
    let status :number;
    //set the amount which one greater than 50
    if (amount > 50) {
      amount = 50;
    }
    //set the status for row data
    /*for ( let i = 0; i < new XMLList(cellDTO["rowData"]).children().length(); i++ ) {
      if (new XMLList(cellDTO["rowData"]).children()[i].name() == cellDTO["clickedColumnName"]) {
        status = new XMLList(cellDTO["rowData"]).children()[i].@status;
        break;
      }
    }*/
    //Instanitate the url letiable
   /*let letiable : Object = new Array();

      letiable= { fromDate: sD1, toDate: sD2, status: cellDTO.status, type: interfaceName, p: 1, n: amount, m: cellDTO.content };*/

    urlString += "fromDate="+ sD1 + "&";
    urlString += "toDate="+ sD2 + "&";
    urlString += "status="+ cellDTO.status + "&";
    urlString += "type="+ interfaceName+ "&";
    urlString += "p="+ "1"+"&";
    urlString += "n="+ amount.toString()+"&";
    urlString += "m="+ cellDTO.content;
    //code modified for 1053_STL_2 issue by karthik on 20110921
    //call to open the input exceptions message details monitor
    ExternalInterface.call("clickInterface", urlString);
    } catch (e) {
      console.log('errir', e)
    }
  }


  /**
		 * Added for mantis by KaisBS 2016 + 1468
		 * _Mantis 2016 (Enhanced date range selection on all screens with a From and To Date field)
		 * According to prior and ahead dates, plus the from and to date values, we check if we has the excess of the date range.
		 * If yes, the alert "The data for this date range selection may not be available..." displays and the data will be updated only if the user clicks OK
		 *
		 * _Mantis 1468 (Input Exception Monitor - Problem in selection of a new period)
		 * 	Note that mantis concerns other screens that contain from and to date.
		 **/
    checkDateRange(autoRefreshOrcheckLocalDateRange: string, fromDate: any, toDate: any, systemDate: string, dateFormat: string, updateDataFunction: Function = null): boolean {
      let dateRangeExceeded: boolean = false;
      let nDaysPriorToToday: number = ExternalInterface.call('eval', 'nDaysPriorToToday');
      let priorDate= moment(systemDate, this.dateFormat.toUpperCase()).subtract(nDaysPriorToToday , 'days');
      let nDaysAheadToToday: number = ExternalInterface.call('eval', 'nDaysAheadToToday');
      let aheadDate= moment(systemDate, this.dateFormat.toUpperCase()).add(nDaysAheadToToday , 'days');
      if (fromDate.diff(priorDate) < 0  || toDate.diff(aheadDate) > 0) {
        dateRangeExceeded = true;
        let warningMessage: string =  SwtUtil.getPredictMessage("currencyMonitor.alert.dateRange", null);
        this.swtAlert.question(warningMessage, "", Alert.YES | Alert.CANCEL, null, (data) => {this.checkDateRangeListener(data, autoRefreshOrcheckLocalDateRange,updateDataFunction )} );
      }

      return dateRangeExceeded;
    }

    checkDateRangeListener(event, autoRefreshOrcheckLocalDateRange: string, updateDataFunction: Function): any {
      try {
        if (event.detail == Alert.YES) {
          this.showBuildInProgress = true;
          this.updateData(autoRefreshOrcheckLocalDateRange, true);
        } else {
          this.updateData(autoRefreshOrcheckLocalDateRange, true, true);
        }

      } catch (e) {

      }
    }

    connError(event: MouseEvent): void {
      this.swtAlert.show("" + this.invalidComms, ExternalInterface.call('getBundle', 'text', 'alert-error', 'Error'));

    }

  mouseOverHandler(event: MouseEvent): void {
    event.preventDefault();
    this.lostConnectionText.setStyle("styleName", "myItemHover");
  }

  mouseOutHandler(event: MouseEvent): void {
    event.preventDefault();
    this.lostConnectionText.setStyle("styleName", "myItem");
  }

  validateStartDate(e, type): void {
    /*//if (type === 'focusOut') {
      if (Object(focusManager.getFocus()).id == "closeButton") {
        this.closeHandler();
      } else if ((Object(focusManager.getFocus()).id == "showDays") && this.validateDate( this.startDate)) {
        //e.stopImmediatePropagation();
      } else if (this.validateDate( this.startDate)) {
        if ((this.showDays.text != this.prevNumberOfDays) || (this.startDate.selectedDate.toDateString() != this.prevChosenDate.toDateString()))
						{
						  console.log('enterrrr')
							setTimeout(() => {
                this.updateData('yes')
              }, 0)
						}
      }
    else */if (type === "change" && this.validateDate(this.startDate)) {
         this.showDays.setFocusAndSelect();
       }

  }

 /**
   * close the window from the close button
   **/
  closeHandler(): void {
    ExternalInterface.call("close");
  }

  /**
   * @param event:  KeyboardEvent
   *
   * Added by KaisBS and Meftah for mantis 2016 (Enhanced date range selection on all screens with a From and To Date field)
   * this method is the enter key handler of the showdays field
   * */
  keyDownInShowDaysValue(event: KeyboardEvent): void {
    if (event.keyCode == Keyboard.ENTER) {
      if (this.validateDate( this.startDate) && this.validateNumberOfDays(this.showDays)) {
        if ((this.showDays.text != this.prevNumberOfDays) || (this.startDate.selectedDate.toDateString() != this.prevChosenDate.toDateString()))
        setTimeout(() => {
          this.updateData('yes')
        }, 0)
        else {
          setTimeout(() => {
            this.updateData('yes', true)
          }, 0)
        }
        this.refreshButton.setFocus();
        //FIXME:
        //this.showDays.selectRange(0,showDays.text.length);
      }
    }
  }
  keyDownInNumberOfDays(event): void {
//event.keyCode == Keyboard.ENTER
    if (event.keyCode ==13) {
      this.validateShowDaysValue(event)
        //FIXME:
        //this.showDays.selectRange(0,showDays.text.length);
      }
    }


  /**
   * @param event: Event
   *
   * Added by KaisBS and Meftah for mantis 2016 (Enhanced date range selection on all screens with a From and To Date field)
   * this method is the focus out handler of the showdays field
   * */
  validateShowDaysValue(event: Event): void {
    let fromDateAsString = moment(this.startDate.text,this.dateFormat.toUpperCase());
    let prevChosenDate = moment(this.prevChosenDate ,this.dateFormat.toUpperCase());
    this.updateDayLabel();
    /*if (Object(focusManager.getFocus()).id == "closeButton") {
      event.stopImmediatePropagation();
      this.closeHandler();
    }
    else if (Object(focusManager.getFocus()).id == "fromDate") {
      event.stopImmediatePropagation();
    }
    else {*/
      let showDayText: String = "" + this.showDays.text;
      if (
        (showDayText == "")
        ||
        (showDayText.indexOf('0') != 0 && showDayText != "")
        ||
        (showDayText.indexOf('0') == 0 && showDayText.indexOf("0", 1) == 1)
        ||
        (showDayText.indexOf('0') == 0 && showDayText.indexOf("0", 1) == -1)
      ) {
        if (this.validateNumberOfDays(this.showDays) && (this.validateDate( this.startDate)))
          if ((showDayText != this.prevNumberOfDays) || (fromDateAsString.diff(prevChosenDate) != 0)) {
            setTimeout(() => {
              this.updateData('no')
            }, 0)
            this.autoRefresh.stop();
          }
      }
    //}
  }

  doHelp() {
    ExternalInterface.call("help");
  }
  export(type): void {

    let selects = [];
    let sD1: String = "";
    let sD2: String = "";
    this.fromDate=this.startDate.selectedDate;
    this.toDate=new Date(this.startDate.selectedDate);
    this.toDate.setDate(this.toDate.getDate() + parseInt(this.showDays.text) - 1);

    if (this.dateFormat.toLowerCase() == "dd/mm/yyyy") {

      /*   sD1 = this.d1 ? (this.d1.getDate() < 10 ? "0" + this.d1.getDate() : this.d1.getDate()) + "/" + (this.d1.getMonth() + 1 < 10 ? "0" + (this.d1.getMonth() + 1) : this.d1.getMonth() + 1) + "/" + this.d1.getFullYear() : null;
         sD2 = this.d2 ? (this.d2.getDate() < 10 ? "0" + this.d2.getDate() : this.d2.getDate()) + "/" + (this.d2.getMonth() + 1 < 10 ? "0" + (this.d2.getMonth() + 1) : this.d2.getMonth() + 1) + "/" + this.d2.getFullYear() : null;
 */
      sD1 = CommonUtil.formatDate(this.fromDate, "DD/MM/YYYY");
      sD2 = CommonUtil.formatDate(this.toDate, "DD/MM/YYYY");

    }
    else {
      /*   sD1 =this.d1 ? ((this.d1.getMonth() + 1) < 10 ? "0" + (this.d1.getMonth() + 1) : (this.d1.getMonth() + 1)) + "/" + (this.d1.getDate() < 10 ? "0" + (this.d1.getDate()) :this.d1.getDate()) + "/" +this.d1.getFullYear() : null;
         sD2 = this.d2 ? ((this.d2.getMonth() + 1) < 10 ? "0" +(this.d2.getMonth() + 1) :(this.d2.getMonth() + 1)) + "/" +(this.d2.getDate() < 10 ? "0" +(this.d2.getDate()) : this.d2.getDate()) + "/" + this.d2.getFullYear() : null;
 */
      sD1 = CommonUtil.formatDate(this.fromDate, "MM/DD/YYYY")
      sD2 = CommonUtil.formatDate(this.toDate, "MM/DD/YYYY")

    }
    selects.push("Start Date=" + sD1);
    selects.push("End Date=" + sD2);
    this.dataExport.convertData(this.lastRecievedJSON.inputexceptions.grid.metadata.columns, this.exceptionsGrid, null, selects, type, false );
  }




}




//Define lazy loading routes
const routes: Routes = [
  { path: '', component: InputException }
];
export const routing: ModuleWithProviders = RouterModule.forChild(routes);
//Define an NgModule that will be loaded by app module
@NgModule({
  imports: [routing, SwtToolBoxModule],
  declarations: [InputException],
  entryComponents: []
})
export class InputExceptionModule { }
