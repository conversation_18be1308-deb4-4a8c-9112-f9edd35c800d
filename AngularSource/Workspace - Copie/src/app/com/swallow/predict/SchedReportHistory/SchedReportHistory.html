<SwtModule (creationComplete)="onLoad()" width="100%" height="100%">
  <VBox width="100%" height="100%" minWidth="670" paddingBottom="5" paddingLeft="5" paddingRight="5" paddingTop="5">
    <SwtCanvas width="100%" height="15%">
      <VBox height="100%" width="100%" paddingLeft="10">
        <Grid height="100%" width="100%" paddingLeft="10">
          <GridRow height="25%" width="100%">
            <GridItem width="150">
              <SwtLabel #lblReportJob id="lblReportJob" styleName="labelBold" text="Report Job">
              </SwtLabel>
            </GridItem>
            <GridItem width="100%">
              <SwtComboBox #reportJobsCombo id="reportJobsCombo" width="550" (close)="closedCombo($event)"
                           dataLabel="reportJobs" (change)="refreshDetails($event)">
              </SwtComboBox>
            </GridItem>
            <GridItem width="100%" >
              <SwtLabel #selectedAccount id="selectedAccount" styleName="labelLeft"></SwtLabel>
            </GridItem>
          </GridRow>
          <GridRow height="25%" width="100%">
            <GridItem width="150" height="100%">
              <SwtLabel #lblReportType id="lblReportType" styleName="labelBold"
                        text="Report Type">
              </SwtLabel>
            </GridItem>
            <GridItem width="100%" height="100%">
              <SwtComboBox #reportTypesCombo id="reportTypesCombo" width="400"
                           dataLabel="reportTypes" (change)="refreshDetails($event)"></SwtComboBox>
            </GridItem>
            <GridItem width="100%">
              <SwtLabel #selectedAttribute id="selectedAttribute" styleName="labelLeft"></SwtLabel>
            </GridItem>
          </GridRow>
          <GridRow height="25%" width="100%"   >
            <GridItem width="150" height="100%">
              <SwtLabel #dateLabel id="dateLabel"  styleName="labelBold" text="Date"></SwtLabel>
            </GridItem>
            <GridItem width="100%" height="100%">
              <SwtRadioButtonGroup #dateSingleOrRange id="dateSingleOrRange" align="horizontal">

                <SwtRadioItem #singleDate id="singleDate" width="200" groupName="dateSingleOrRange" selected="true" value="S"
                              (change)="dateTypeChanged('singleDate')"></SwtRadioItem>

                <SwtRadioItem #dateRange id="dateRange" width="200" groupName="dateSingleOrRange" value="D"
                              (change)="dateTypeChanged('dateRange')"></SwtRadioItem>
              </SwtRadioButtonGroup>
            </GridItem>
          </GridRow>
          <GridRow height="25%" width="100%">
            <GridItem width="150" height="100%"></GridItem>
              <GridItem width="40">
                <SwtLabel #startDateLabel id="startDateLabel" styleName="labelBold" >
                </SwtLabel>
              </GridItem>
              <GridItem width="14%">
                <SwtDateField #startDate id="startDate" (change)="validateDateFieldValue($event)" restrict="0-9/" width="70" [editable]="false"></SwtDateField>
              </GridItem>
              <GridItem width="2%">
                <SwtLabel #endDateLabel id="endDateLabel" styleName="labelBold" visible="false">
              </SwtLabel>
              </GridItem>
              <GridItem #dateItem visible="false" width="13%">
                <SwtDateField #endDate id="endDate"  (change)="validateDateFieldValue($event)" restrict="0-9/" width="70" [editable]="false"></SwtDateField>
              </GridItem>
          </GridRow>
        </Grid>
      </VBox>
    </SwtCanvas>
    <SwtCanvas id="canvasGrid" #canvasGrid width="100%" height="80%">
    </SwtCanvas>
    <SwtCanvas width="100%" height="5%">
      <HBox width="100%">
        <HBox width="80%">
          <SwtButton #refreshButton id="refreshButton" (click)="refreshDetails($event)"></SwtButton>
          <SwtButton #downloadButton id="downloadButton" enabled="false" (click)="downloadButton_clickHandler($event)">
          </SwtButton>
          <SwtButton #resendMailButton id="resendMailButton" enabled="false"
            (click)="resendMailButton_clickHandler($event)">
          </SwtButton>
          <SwtButton #viewButton id="viewButton" (click)="changeViewSchedReportHist(false)" enabled="false">
          </SwtButton>
          <SwtButton #deleteButton id="deleteButton" (click)="deleteSchedReportHist()" enabled="false">
          </SwtButton>
          <SwtButton #closeButton id="closeButton" (click)="closeHandler()">
          </SwtButton>
        </HBox>
        <HBox horizontalAlign="right" width="20%" paddingTop="5">
          <SwtHelpButton #helpButton id="helpButton"  enabled="true" (click)="doHelp()"
            styleName="helpIcon"></SwtHelpButton>
          <SwtLoadingImage #loadingImage> </SwtLoadingImage>
        </HBox>
      </HBox>
    </SwtCanvas>
  </VBox>
</SwtModule>
