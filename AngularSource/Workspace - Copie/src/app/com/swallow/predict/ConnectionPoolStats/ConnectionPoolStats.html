<SwtModule (creationComplete)='onLoad()' width="100%" height="100%">
  <VBox id="vbParent" paddingLeft="5" paddingRight="5" paddingBottom="5" paddingTop="5" height="100%" width="100%">
    <SwtCanvas id="attAccContainer" width="100%" height="7%">
      <!-- Main Data Container -->
      <HBox paddingLeft="5" width="100%" height="100%">
              <SwtLabel paddingTop="3"  width="120" styleName="labelBold" textDictionaryId="connectionPool.connectionPool">
              </SwtLabel>
              <SwtComboBox #moduleCombo id="moduleCombo"  width="120"
              (change)="refresh()"
                 dataLabel="moduleList" ></SwtComboBox>
      </HBox>
    </SwtCanvas>

    <SwtCanvas  width="100%"  height="85%">


        <VBox width="100%" height="100%">
          <HBox  width="100%" paddingLeft="5">
            <SwtLabel styleName="labelBold" textDictionaryId="connectionPool.poolStats"></SwtLabel>
          </HBox>

          <HBox  width="100%" >
            <SwtLabel paddingLeft="15" styleName="labelBold" width="250" textDictionaryId="connectionPool.activeConnections"></SwtLabel>
              <SwtLabel paddingLeft="20" #activeStatsLabel id="activeStatsLabel">
              </SwtLabel>
          </HBox>


          <HBox  width="100%">
            <SwtLabel paddingLeft="15" styleName="labelBold"  width="250" textDictionaryId="connectionPool.idleConnections"></SwtLabel>
              <SwtLabel paddingLeft="20" #idleStatsLabel id="idleStatsLabel">
              </SwtLabel>
          </HBox>


          <HBox width="100%" paddingLeft="5">
            <SwtLabel  styleName="labelBold" textDictionaryId="connectionPool.Connections"></SwtLabel>
          </HBox>
  
         <HBox  height="80%"  width="100%" paddingLeft="5">
          <SwtCanvas height="100%" width="100%" border="false" #cvGridContainer id="cvGridContainer" ></SwtCanvas>
        </HBox>
        </VBox>
    </SwtCanvas>

    <SwtCanvas id="swtButtonBar" width="100%"  height="7%">
      <HBox width="100%" height="100%">
        <SwtCanvas height="100%" width="100%" paddingTop="5"  border="false">
        <HBox width="100%" height="100%" paddingLeft="11"   styleName="hgroupLeft">
          <SwtButton id="refreshButton" #refreshButton (click)="refresh()"></SwtButton>
          <SwtButton id="viewButton" #viewButton (click)="viewDetails()" ></SwtButton>
          <SwtButton id="killButton" #killButton  (click)="doKillConnectionEventHandler($event)" 
            enabled="false"></SwtButton>
          <SwtButton id="closeButton" #closeButton  (click)="popupClosed()"></SwtButton>
        </HBox>
      </SwtCanvas>
        <HBox  horizontalAlign="right"   styleName="hgroupRight">
            <SwtText #lastRef width="80"  paddingTop="8"  id="lastRef"></SwtText>
            <SwtText width="100" paddingTop="8"  id="lastRefTime"
                     #lastRefTime>
            </SwtText>

          <HBox paddingTop="8" >
            <SwtLoadingImage  #loadingImage></SwtLoadingImage>
            <SwtHelpButton   id="btnHelp" [buttonMode]="true" enabled="true" helpFile="spread-profile" (click)="doHelp()"></SwtHelpButton>
            <!-- <DataExport #dataExport id="dataExport"></DataExport> -->
            <SwtButton  id="excel"
                       #excel
                       styleName="excelIcon"
                       enabled="true"
                       buttonMode="true"
                       (click)="report('excel')"></SwtButton>
            <SwtButton id="csv"
                       #csv
                       styleName="csvIcon"
                       enabled="true"
                       buttonMode="true"
                       (click)="report('csv')"></SwtButton>
          </HBox>

        </HBox>
      </HBox>
    </SwtCanvas>
  </VBox>
</SwtModule>
