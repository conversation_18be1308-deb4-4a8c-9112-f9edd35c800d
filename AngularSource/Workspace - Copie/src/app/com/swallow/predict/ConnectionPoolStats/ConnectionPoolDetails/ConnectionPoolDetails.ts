import { Compo<PERSON>, OnInit, OnDestroy, ElementRef, ViewChild, NgModule, ModuleWithProviders } from '@angular/core';
import {
  SwtToolBoxModule, SwtModule, CommonService, SwtAlert, Logger, JSONReader, HTTPComms, SwtUtil,
  ExternalInterface, SwtCommonGrid, SwtCanvas, SwtButton, SwtLoadingImage, SwtComboBox, Alert, SwtLabel, SwtTextInput, SwtTextArea, focusManager, Keyboard
} from 'swt-tool-box';
import { RouterModule, Routes } from '@angular/router';
@Component({
  selector: 'app-connection-pool-details',
  templateUrl: './ConnectionPoolDetails.html',
  styleUrls: ['./ConnectionPoolDetails.css']
})
export class ConnectionPoolDetails extends SwtModule implements OnInit, OnDestroy {


  @ViewChild("module") moduleTextInput: SwtTextInput;
  @ViewChild("connectionId") connectionIdTextInput: SwtTextInput;
  @ViewChild("status") statusTextInput: SwtTextInput;
  @ViewChild("duration") durationTextInput: SwtTextInput;
  @ViewChild("lastActionTime") lastActionTimeTextInput: SwtTextInput;
  @ViewChild("sqlStatus") sqlStatusTextInput: SwtTextInput;
  @ViewChild("sid") sidTextInput: SwtTextInput;
  @ViewChild("audsid") audsidTextInput: SwtTextInput;
  @ViewChild("sqlId") sqlIdTextInput: SwtTextInput;
  @ViewChild("sqlExecStart") sqlExecStartInput: SwtTextInput;

  @ViewChild("stackTrace") stackTraceTextArea: SwtTextArea;
  @ViewChild("sqlStatement") sqlStatementTextArea: SwtTextArea;

  
  @ViewChild("killButton") killButton: SwtButton;
  @ViewChild("cancelButton") cancelButton: SwtButton;
  status: string;
  duration: string;
  lastActionTime: string;
  sqlStatus: string;
  sid: string;
  audsid: string;
  sqlId: string;
  stackTrace: string;
  sqlStatement: string;
  sqlExecStart : string

  ngOnDestroy(): void {
  }
  private swtAlert: SwtAlert;
  private inputData = new HTTPComms(this.commonService);
  private baseURL = SwtUtil.getBaseURL();

  connectionIdAsString: string;
  menuAccessId: any;
  moduleId: any;
  public errorLocation = 0;

  /**
   * Data Objects
   **/
  public jsonReader = new JSONReader();
  public lastRecievedJSON;

  private actionMethod = "";
  private actionPath = "";
  private requestParams = [];

  constructor(private commonService: CommonService, private element: ElementRef) {
    super(element, commonService);
    this.swtAlert = new SwtAlert(commonService);
  }

  ngOnInit() {
    this.killButton.label = SwtUtil.getPredictMessage('button.kill', null);
    this.cancelButton.label = SwtUtil.getPredictMessage('button.close', null);
  }


  onLoad() {
    let paramsFromParent = [];
    try {
      if (window.opener && window.opener.instanceElement) {
        paramsFromParent = window.opener.instanceElement.getParamsFromParent();
        if (paramsFromParent) {
          this.menuAccessId = paramsFromParent[0].menuAccessId;

          this.moduleTextInput.text = this.moduleId = paramsFromParent[0].moduleId;
          this.connectionIdTextInput.text = this.connectionIdAsString = paramsFromParent[0].connectionId;
          this.statusTextInput.text = this.status =  paramsFromParent[0].status;
          this.durationTextInput.text =  this.duration = paramsFromParent[0].duration;
          this.lastActionTimeTextInput.text = this.lastActionTime = paramsFromParent[0].lastActionTime;
          this.sqlStatusTextInput.text =  this.sqlStatus = paramsFromParent[0].sqlStatus;
          this.sidTextInput.text = this.sid = paramsFromParent[0].ssid;
          this.audsidTextInput.text = this.audsid = paramsFromParent[0].audSid;
          this.sqlIdTextInput.text = this.sqlId = paramsFromParent[0].sqlId;
          this.sqlExecStartInput.text = this.sqlExecStart = paramsFromParent[0].sqlExecStart;

          this.stackTraceTextArea.text = this.stackTrace = paramsFromParent[0].stackTrace;
          this.sqlStatementTextArea.text = this.sqlStatement = paramsFromParent[0].sqlStatement;

        }
      }
    } catch (e) {
      console.log("e", e);

    }
  }
""

  doKillConnectionEventHandler(event) {
    var message: string = SwtUtil.getPredictMessage('connectionPool.alertKillingConsequences', null);
    this.swtAlert.confirm(message, null, Alert.YES | Alert.NO, null, this.checkConnectionStatusChanged.bind(this));

  }

  checkConnectionStatusChanged(event) {
    if (event.detail === Alert.YES) {
      this.killButton.enabled = false;
        this.requestParams = [];
        this.actionMethod = 'method=checkConnectionChanged';
        this.actionPath = 'connectionPool.do?';
        this.requestParams["connectionId"] = this.connectionIdAsString;
        this.requestParams["moduleId"] = this.moduleId;


        this.requestParams["connectionId"] = this.connectionIdAsString;
        this.requestParams["sqlId"] = this.sqlId ;
        this.requestParams["duration"] = this.duration;
        this.requestParams["sqlStatement"] = this.sqlStatement;
        this.requestParams["sqlStatus"] = this.sqlStatus;
        this.requestParams["moduleId"] = this.moduleId;
        this.requestParams["audSid"] = this.audsid;
        this.requestParams["stackTrace"] = this.stackTrace;
        this.requestParams["ssid"] = this.sid;
        this.requestParams["lastActionTime"] = this.lastActionTime;
        this.requestParams["status"] = this.status;




        this.inputData.url = this.baseURL + this.actionPath + this.actionMethod;

        this.inputData.cbResult = (data) => {
          this.checkConnectionChangedDataResult(data);
        };
        this.inputData.send(this.requestParams);
    }

  }


  /**                                                                                                                                   
 * This method is called by the HTTPComms when result event occurs.                                                                   
 * @param event:ResultEvent                                                                                                           
 * */
  private checkConnectionChangedDataResult(event): void {

    //get the received xml  
    try {
      this.lastRecievedJSON = event;
      this.jsonReader.setInputJSON(this.lastRecievedJSON);
      //test the reply status first, as if the reply status is false then the data will not be valid                                    

      if (this.jsonReader.getRequestReplyStatus()) {
        this.killConnection();
      } else {
        if (this.jsonReader.getRequestReplyMessage() == "CONNECTION_NOT_EXIST") {
          let message = SwtUtil.getPredictMessage('connectionPool.alertConnectionKilled', null);
          this.swtAlert.confirm(message, null, Alert.YES, null, this.popupClosed.bind(this));
        }
        else if (this.jsonReader.getRequestReplyMessage() == "CONNECTION_DETAILS_CHANGED") {
          let message = SwtUtil.getPredictMessage('connectionPool.alertDetailsChanged', null);
          this.swtAlert.confirm(message, null, Alert.YES | Alert.NO, null, this.connectionChangeAlertHandler.bind(this));
        }
      }
    } catch (error) {
      this.swtAlert.show(ExternalInterface.call('eval', 'label[\'alert\'][\'server_error\']'));
    }
  }


  connectionChangeAlertHandler(event) {

    if (event.detail === Alert.YES) {
      this.killConnection();
    }
  }


  /**
 * doHelp
 * Function is called when "Help" button is click. Displays help window
 */
  killConnection(): void {
    try {
      this.requestParams = [];
      this.requestParams["connectionIds"] = this.connectionIdAsString;
      this.requestParams["moduleId"] = this.moduleId;
      this.actionMethod = 'method=killConnectionPool';
      this.actionPath = 'connectionPool.do?';
      this.inputData.url = this.baseURL + this.actionPath + this.actionMethod;
      this.inputData.cbResult = (data) => {
        this.inputDataResult(data);
      };
      this.inputData.send(this.requestParams);

    } catch (e) {
      // log the error in ERROR LOG
      SwtUtil.logError(e, this.moduleId, 'killConnection', 'doHelp', 10);
    }
  }



  inputDataResult(event): void {
    try {
      if (this.inputData.isBusy()) {
        this.inputData.cbStop();
      } else {

        /* Get result as xml */
        this.lastRecievedJSON = event;
        this.jsonReader.setInputJSON(this.lastRecievedJSON);
        /* Condition to check lastRecievedXML not equal to prevRecievedXML */
        /* Condition to check request reply status is true*/
        if (this.jsonReader.getRequestReplyMessage()) {

          this.popupClosed();
        }
      }
    } catch (error) {
      console.log("error:   ", error);
      SwtUtil.logError(error, this.moduleId, "className", "inputDataResult", this.errorLocation);
    }
    this.killButton.enabled = true;  
	}


  /**
  * startOfComms
  * Part of a callback  to all for control of the loading swf from the HTTPComms Object
  */
  startOfComms(): void {
    // this.loadingImage.setVisible(true);
  }

  /**
   * endOfComms
   * Part of a callback  to all for control of the loading swf from the HTTPComms Object
   */
  endOfComms(): void {
    // this.loadingImage.setVisible(false);
  }

    /**
   * keyDownEventHandler
   *
   * @param event:  KeyboardEvent
   *
   * This is a key event listener, used to perform the operation
   * when hit the enter key based on the currently focused property(Button)
   */
  keyDownEventHandler(event): void {
    try {
      const eventString = Object(focusManager.getFocus()).name;
      if ((event.keyCode === Keyboard.ENTER)) {
        if (eventString === 'killButton') {
          this.killConnection();
        } else if (eventString === 'cancelButton') {
          this.popupClosed();
        } 
      }
    } catch (e) {
      // log the error in ERROR LOG
      SwtUtil.logError(e,   this.moduleId, 'CategoryMaintenance', 'keyDownEventHandler',   this.errorLocation);
    }
  }

    /**
   * popupClosed
   * Method to close child windows when this screen is closed
   */
  popupClosed(): void {
    window.close();
    //SwtPopUpManager.getPopUpById("addGroupTitleWindow").close();

  }

  /**
  * inputDataFault
  * param event:  FaultEvent
  * This is a callback , used to handle fault event.
  * Shows fault message in alert window.
  */
  inputDataFault(event): void {
    this.swtAlert.error(event.fault.faultstring + "\n" + event.fault.faultCode + "\n" + event.fault.faultDetail);
  }




}


//Define lazy loading routes
const routes: Routes = [
  { path: '', component: ConnectionPoolDetails }
];
export const routing: ModuleWithProviders = RouterModule.forChild(routes);
//Define an NgModule that will be loaded by app module
@NgModule({
  imports: [routing, SwtToolBoxModule],
  declarations: [ConnectionPoolDetails],
  entryComponents: []
})
export class ConnectionPoolDetailsModule { }
