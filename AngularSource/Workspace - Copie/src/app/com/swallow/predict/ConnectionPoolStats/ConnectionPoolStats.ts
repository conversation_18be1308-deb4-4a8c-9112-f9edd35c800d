import { Component, OnInit, NgModule, ModuleWithProviders, OnDestroy, ElementRef, ViewChild } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';
import {
  SwtToolBoxModule, SwtModule, CommonService, SwtAlert, Logger, JSONReader, HTTPComms, SwtUtil,
  ExternalInterface, SwtCommonGrid, SwtCanvas, SwtButton, SwtLoadingImage, SwtComboBox, Alert, SwtLabel, SwtText
} from 'swt-tool-box';
const $ = require('jquery');
declare var require: any;
var convert = require('xml-js');
declare var instanceElement: any;
@Component({
  selector: 'app-connection-pool-stats',
  templateUrl: './ConnectionPoolStats.html',
  styleUrls: ['./ConnectionPoolStats.css']
})


export class ConnectionPoolStats extends SwtModule implements OnDestroy, OnInit {



  @ViewChild("cvGridContainer") cvGridContainer: SwtCanvas;
  @ViewChild("viewButton") viewButton: SwtButton;
  @ViewChild("killButton") killButton: SwtButton;
  @ViewChild("refreshButton") refreshButton: SwtButton;
  @ViewChild("closeButton") closeButton: SwtButton;
  @ViewChild('loadingImage') loadingImage: SwtLoadingImage;

  @ViewChild("activeStatsLabel") activeStatsLabel: SwtLabel;
  @ViewChild("idleStatsLabel") idleStatsLabel: SwtLabel;


  @ViewChild("moduleCombo") moduleCombo: SwtComboBox;

  /*********SwtText*********/
  @ViewChild('lastRef') lastRef: SwtText;
  @ViewChild('lastRefTime') lastRefTime: SwtText;

  private swtAlert: SwtAlert;
  private logger: Logger;


  /**                                                                                       
	 * Data Objects                                                                           
	 **/
  private jsonReader: JSONReader = new JSONReader();
  private lastRecievedJSON: any = null;
  // Variable to hold  menuAccessId                                                         
  private _menuAccessId: string = null;
  // Variable to hold _isPublic                                                             
  private _isPublic: string = null;
  // Variable to hold _comboOpen
  private comboOpen: Boolean = false;
  // Variable holds the name of the screen      
  //TODO:                                            
  private _screenName: string = "Connection Pool Stats Monitor";//ExternalInterface.call('getBundle', 'text', 'screen-tilte', 'AccountSpecificSweepFormat Screen');
  // private _screenName="AccountSpecificSweepFormat Screen"                                                                             
  // Variable that holds the version number for this mxml                                                       
  private _versionNumber: string = "1.0.0";
	/**                                                                                                           
	 * Communication Objects                                                                                      
	 **/
  private _inputData = new HTTPComms(this.commonService);
  private _baseURL: string = SwtUtil.getBaseURL();
  private _actionMethod: string = null;
  private _actionPath: string = null;
  private _requestParams = new Array();
  private _invalidComms: string;
  private _closeWindow: Boolean = false;

  // Variable to hold entityId
  private connectionModuleId: string = null;
  private moduleId: string = null;
  // Variable to hold currencyCode
  private connectionPoolId: string = null;

  private parentMethodName: string = null;

  private previousSelectedIndex: number = -1;
  freshViewsData: any;


  ngOnDestroy(): void {
    //throw new Error("Method not implemented.");
    instanceElement = null;
  }


  constructor(private commonService: CommonService, private element: ElementRef) {
    super(element, commonService);
    this.logger = new Logger('Connection Pool Stats Monitor', this.commonService.httpclient);
    this.swtAlert = new SwtAlert(commonService);
  }

  /**                                                                                       
     * Display Objects                                                                        
     **/
  private _connectionPoolStatsGrid: SwtCommonGrid = null;

  ngOnInit() {
    instanceElement = this;
    this.viewButton.label = SwtUtil.getPredictMessage('button.view', null);
    this.killButton.label = SwtUtil.getPredictMessage('button.kill', null);
    this.refreshButton.label = SwtUtil.getPredictMessage('button.refresh', null);
    this.closeButton.label = SwtUtil.getPredictMessage('button.close', null);
    this.lastRef.text = SwtUtil.getPredictMessage('screen.lastRefresh', null);
  }




  /**                                                                                                           
	 * Upon completion of loading into the flash player this method is called                                     
	 **/
  onLoad(): void {
    this._connectionPoolStatsGrid = <SwtCommonGrid>this.cvGridContainer.addChild(SwtCommonGrid);
    try {
      // _baseURL=obtainURL();                                                                                     
      // set version number                                                                                        
      // Read menua cces from request                                                                           
      this._menuAccessId = ExternalInterface.call('eval', 'menuAccessId');
      // ExternalInterface.addCallback("refreshdetails", refreshdetails);
      //Add the event listener to listen for a cell click on a datagrid, be it the main one or a totals grid    
      // this._connectionPoolStatsGrid.cellClick.subscribe((selectedRowData) => {
      //   this.cellLogic(event);
      // });

      this._connectionPoolStatsGrid.onRowClick = (event) => {
        this.cellLogic(event);
      };
      this._connectionPoolStatsGrid.onRowDoubleClick = (event) => {
        this.viewDetails();
      };
      // this.addEventListener("filterUpdate", cellLogic, true);                                                 
      // this.addEventListener("MenuClick", hideShowColumn, true);                                               
      // Condition to check menuAccess is not full access   
      if (this._menuAccessId != "0") {
        this.viewButton.enabled = false;
        this.killButton.enabled = false;
      }

      this._inputData.cbStart = this.startOfComms.bind(this);
      this._inputData.cbStop = this.endOfComms.bind(this);
      //result event                                                                                            
      this._inputData.cbResult = (data) => {
        this.inputDataResult(data);
      };
      //fault event                                                                                             
      this._inputData.cbFault = this.inputDataFault.bind(this);
      this._inputData.encodeURL = false;
      //action url	                                                                                          
      this._actionPath = "connectionPool.do?method=";
      //Then declare the action method:					                                                      
      this._actionMethod = "displayConnectionPoolList";
      this._inputData.url = this._baseURL + this._actionPath + this._actionMethod;
      this._inputData.send(this._requestParams);

    } catch (error) {
      this.logger.error("Method [onLoad]", error);
    }

  }



  /**                                                                                                                                   
     * This method is called by the HTTPComms when result event occurs.                                                                   
     * @param event:ResultEvent                                                                                                           
     * */
  private inputDataResult(event): void {

    //get the received xml  
    try {
      this.lastRecievedJSON = event;
      this.jsonReader.setInputJSON(this.lastRecievedJSON);
      //test the reply status first, as if the reply status is false then the data will not be valid                                    

      if (this.jsonReader.getRequestReplyStatus()) {
        //Some monitors have a database job that runs to build the data.                                                              
        //If this is running then the databuilding flag will be set                                                                   
				/*                                                                                                                        
				If the main datagrid has not been initialize, for example it is the first time data as been recieved then initialise it   
				and add it to the appropriate display container.                                                                          
				*/

        if (!this.jsonReader.isDataBuilding()) {

          //If the code has reached this point then the database is not databuilding, turn off the dataBuildingText 
          if (this._connectionPoolStatsGrid.columnDefinitions.length == 0) {
            this._connectionPoolStatsGrid.allowMultipleSelection = true;
            this._connectionPoolStatsGrid.doubleClickEnabled = true;
            this._connectionPoolStatsGrid.CustomGrid(this.lastRecievedJSON.connectionPoolMonitor.grid.metadata);
            //FIXME:
            // this._connectionPoolStatsGrid.colWidthURL(this._baseURL + "accountSpecificSweepFormat.do?screenName=accountSpecificSweepFormat");
            // this._connectionPoolStatsGrid.colOrderURL(this._baseURL + "accountSpecificSweepFormat.do?screenName=accountSpecificSweepFormat");
            // this._connectionPoolStatsGrid.saveWidths = true;
            // this._connectionPoolStatsGrid.saveColumnOrder = true;
          }
          //set the grid row data
          this._connectionPoolStatsGrid.CustomGrid(this.lastRecievedJSON.connectionPoolMonitor.grid.metadata);
          this._connectionPoolStatsGrid.gridData = this.jsonReader.getGridData();
          this._connectionPoolStatsGrid.rowColorFunction = (dataGrid, dataIndex, color) => {
            return this.drawRowBackground(dataGrid, dataIndex, color);
          };
          //set the row size                                                                                                    
          this._connectionPoolStatsGrid.setRowSize = this._connectionPoolStatsGrid.gridData.length;
          this.moduleCombo.setComboData(this.jsonReader.getSelects(), false);
          this.activeStatsLabel.text = this.jsonReader.getSingletons().activeStats;
          this.freshViewsData = this.jsonReader.getSingletons().freshViewsData;

          this.idleStatsLabel.text = this.jsonReader.getSingletons().idleStats;
          this._menuAccessId = this.jsonReader.getScreenAttributes()["menuaccess"];

          if (this._menuAccessId == "0") {
            this.killButton.enabled = true;
          } else {
            this.killButton.enabled = false;
          }
          if (this.previousSelectedIndex != -1 && this._connectionPoolStatsGrid.gridData.length >= this.previousSelectedIndex + 1) {
            this._connectionPoolStatsGrid.selectedIndex = this.previousSelectedIndex;
          }

          //Get lastRefTime
          let lastRef = this.jsonReader.getScreenAttributes()["lastRefTime"];
          this.lastRefTime.text = lastRef.replace(/\\u0028/g, '(').replace(/\\u0029/g, ')');

          this.cellLogic(null);

          
          if ("" + this.freshViewsData == "false")
            this.swtAlert.error(SwtUtil.getPredictMessage('connectionPool.alertDBConnectionErr', null));
        }

      }
      else {
        if (this.jsonReader.getRequestReplyMessage() == "NO_ENOUGH_GRANTS") {
          //get the attribute for closewindow  flag                                                                                 
          this.swtAlert.error(SwtUtil.getPredictMessage('connectionPool.alertDBGrantsNeeded', null));
        } else {
          this.swtAlert.show(this.jsonReader.getRequestReplyMessage() + "\n" + this.jsonReader.getRequestReplyLocation(), "Error", Alert.OK, this, this.errorHandler.bind(this));
        }
      }


      this._connectionPoolStatsGrid.validateNow();
    } catch (error) {
      console.log("e", error);

      this.logger.error("Method [inputDataResult]", error);
      this.swtAlert.show(ExternalInterface.call('eval', 'label[\'alert\'][\'server_error\']'));
    }
  }

  public drawRowBackground( dataContext, dataIndex, color ): string {

    let rColor: string;
    try {
      let colorFlag: string;
      if (dataContext) {
          if (dataContext.slickgrid_rowcontent.bgColor == "RED") {
            rColor = "#FA8C8C";
          } else if (dataContext.slickgrid_rowcontent.bgColor == "GREY") {
            rColor = "grey";
          } else {
            rColor = "transparent";
          }
      }
    }
    catch (error) {
      console.log('error drawRowBackground ', error)
    }
    return rColor;
  }


  /**                                                                                                                  
 * Listener for Error alert message and                                                                              
 * perform action for when click ok button                                                                           
 * @param event:CloseEvent                                                                                           
 * */
  errorHandler(event): void {
    //check event click is ok button                                                                                 
    if (event.detail == Alert.OK) {
      if (this._closeWindow) {
        //closeHandler();                                                                                        
        this._closeWindow = false;
      }
    }
  }

  /**                                                                                                                  
 * If a fault occurs with the connection with the server then display the lost connection label                      
 * @param event:FaultEvent                                                                                           
 **/
  private inputDataFault(event): void {
    this._invalidComms = event.fault.faultString + "\n" + event.fault.faultCode + "\n" + event.fault.faultDetail;
    this.swtAlert.show("fault " + this._invalidComms);
  }


	/**                                                                                                                  
	 * Part of a callback function to all for control of the loading swf from the HTTPComms Object                       
	 **/
  startOfComms(): void {
    this.loadingImage.visible = true;
  }

	/**                                                                                                                  
	 * Part of a callback function to all for control of the loading swf from the HTTPComms Object                       
	 **/
  endOfComms(): void {
    this.loadingImage.visible = false;
  }



  /**                                                                                                                  
	 * When item click on the datagrd is method will be called                                                           
	 * @param e:CellEvent                                                                                                
	 **/
  cellLogic(e): void {
    if (this._connectionPoolStatsGrid.selectedIndices.length < 1) {
      this.connectionModuleId = null;
      this.connectionPoolId = null;
    } else if (this._connectionPoolStatsGrid.selectedIndices.length == 1) {
      var selectedRow: number = this._connectionPoolStatsGrid.selectedIndex;
      this.connectionModuleId = this._connectionPoolStatsGrid.dataProvider[selectedRow].connectionModuleId;
      this.connectionPoolId = this._connectionPoolStatsGrid.dataProvider[selectedRow].connectionPoolId;
    } else {
      this.connectionModuleId = null;
      this.connectionPoolId = null;

    }

    this.disableOrEnableButtons(this._connectionPoolStatsGrid.selectedIndices.length)

  }


  /**                                                                                                                  
     * Method to disable or enable buttons                                                                               
     * @param isRowSelected:Boolean                                                                                      
     * @return                                                                                                           
     */
  disableOrEnableButtons(numberOfSelectedRows): void {

    var menuAccess: Number = parseInt(this._menuAccessId, 10);
    if (numberOfSelectedRows == 1) {
      this.killButton.enabled = (menuAccess == 0);
      this.viewButton.enabled = (menuAccess < 2);
    } else if (numberOfSelectedRows > 1) {
      this.killButton.enabled = (menuAccess == 0);
      this.viewButton.enabled = false;
    } else {
      this.killButton.enabled = false;
      this.viewButton.enabled = false;
    }

  }


  /**
 * doHelp
 * Function is called when "Help" button is click. Displays help window
 */
  doHelp(): void {
    try {
      //SwtHelpWindow.open(this.baseURL + "help.do?method=print&screenName=Category+Maintenance");
      ExternalInterface.call("help");
    } catch (e) {
      // log the error in ERROR LOG
      SwtUtil.logError(e, this.moduleId, 'CategoryMaintenance', 'doHelp', 10);
    }
  }

  /**
 * doHelp
 * Function is called when "Help" button is click. Displays help window
 */
  killConnection(): void {
    try {
      this._requestParams = [];
      this._requestParams["connectionIds"] = JSONReader.jsonpath(this._connectionPoolStatsGrid.selectedItems, '$.*.id.content').toString();
      this._requestParams["moduleId"] = this.moduleCombo.selectedValue;
      this._actionMethod = 'method=killConnectionPool';
      this._actionPath = 'connectionPool.do?';
      this._inputData.url = this._baseURL + this._actionPath + this._actionMethod;
      this._inputData.cbResult = (data) => {
        this.inputDataResult(data);
      };
      this._inputData.send(this._requestParams);

    } catch (e) {
      // log the error in ERROR LOG
      SwtUtil.logError(e, this.moduleId, 'killConnection', 'doHelp', 10);
    }
  }


  doKillConnectionEventHandler(event) {
    var message: string = SwtUtil.getPredictMessage('connectionPool.alertKillingConsequences', null);
    this.swtAlert.confirm(message, null, Alert.YES | Alert.NO, null, this.checkConnectionStatusChanged.bind(this));

  }

  checkConnectionStatusChanged(event) {
    if (event.detail === Alert.YES) {
      if (this._connectionPoolStatsGrid.selectedItems.length == 1) {

        this._requestParams = [];
        this._actionMethod = 'method=checkConnectionChanged';
        this._actionPath = 'connectionPool.do?';
        this._requestParams["connectionId"] = this._connectionPoolStatsGrid.selectedItem.id.content;
        this._requestParams["moduleId"] = this.moduleCombo.selectedValue;


        this._requestParams["connectionId"] = this._connectionPoolStatsGrid.selectedItem.id.content;
        this._requestParams["sqlId"] = this._connectionPoolStatsGrid.selectedItem.sqlId.content;
        this._requestParams["duration"] = this._connectionPoolStatsGrid.selectedItem.duration.content;
        this._requestParams["sqlStatement"] = this._connectionPoolStatsGrid.selectedItem.sqlStatement.content;
        this._requestParams["sqlStatus"] = this._connectionPoolStatsGrid.selectedItem.sqlStatus.content;
        this._requestParams["moduleId"] = this._connectionPoolStatsGrid.selectedItem.module.content;
        this._requestParams["audSid"] = this._connectionPoolStatsGrid.selectedItem.audSid.content;
        this._requestParams["stackTrace"] = this._connectionPoolStatsGrid.selectedItem.stackTrace.content;
        this._requestParams["ssid"] = this._connectionPoolStatsGrid.selectedItem.SSID.content;
        this._requestParams["lastActionTime"] = this._connectionPoolStatsGrid.selectedItem.lastActionTime.content;
        this._requestParams["status"] = this._connectionPoolStatsGrid.selectedItem.status.content;




        this._inputData.url = this._baseURL + this._actionPath + this._actionMethod;

        this._inputData.cbResult = (data) => {
          this.checkConnectionChangedDataResult(data);
        };
        this._inputData.send(this._requestParams);
      } else {
        this.killConnection();
      }
    }

  }


  /**                                                                                                                                   
 * This method is called by the HTTPComms when result event occurs.                                                                   
 * @param event:ResultEvent                                                                                                           
 * */
  private checkConnectionChangedDataResult(event): void {

    //get the received xml  
    try {
      this.lastRecievedJSON = event;
      this.jsonReader.setInputJSON(this.lastRecievedJSON);
      //test the reply status first, as if the reply status is false then the data will not be valid                                    

      if (this.jsonReader.getRequestReplyStatus()) {
        this.killConnection();
      } else {
        if (this.jsonReader.getRequestReplyMessage() == "CONNECTION_NOT_EXIST") {
          let message = SwtUtil.getPredictMessage('connectionPool.alertConnectionKilled', null);
          this.swtAlert.confirm(message, null, Alert.YES, null, this.refresh.bind(this));
        }
        else if (this.jsonReader.getRequestReplyMessage() == "CONNECTION_DETAILS_CHANGED") {
          this.swtAlert.confirm(SwtUtil.getPredictMessage('connectionPool.alertDetailsChanged', null), null, Alert.YES | Alert.NO, null, this.connectionChangeAlertHandler.bind(this));
        }
      }
    } catch (error) {
      this.logger.error("Method [inputDataResult]", error);
      this.swtAlert.show(ExternalInterface.call('eval', 'label[\'alert\'][\'server_error\']'));
    }
  }


  connectionChangeAlertHandler(event) {

    if (event.detail === Alert.YES) {
      this.killConnection();
    } else {
      this.refresh();
    }
  }


  /**
 * viewDetails
 * Function is called when "Help" button is click. Displays help window
 */
  viewDetails(): void {
    try {
      // var newWindow = window.open("/connectionPoolDetails", 'Conneciton Pool details', 'height=700,width=960,left=100,top=100,resizable=yes,scrollbars=yes,toolbar=yes,menubar=no,location=no,directories=no, status=yes');
      // if (window.focus) {
      //   newWindow.focus();
      // }
      

      ExternalInterface.call('openChildWindow');

    } catch (e) {
      // log the error in ERROR LOG
      SwtUtil.logError(e, this.moduleId, 'viewDetails', 'doHelp', 10);
    }
  }
  //FIXME:ADd comments
  /**
  /**
 * doHelp
 * Function is called when "Help" button is click. Displays help window
 */
  refresh(): void {
    try {
      this._requestParams = [];
      this._requestParams["moduleId"] = this.moduleCombo.selectedValue;
      this._actionMethod = 'method=displayConnectionPoolList';
      this._actionPath = 'connectionPool.do?';
      this._inputData.url = this._baseURL + this._actionPath + this._actionMethod;
      this._inputData.cbResult = (data) => {
        this.inputDataResult(data);
      };
      this._inputData.send(this._requestParams);
    } catch (e) {
      // log the error in ERROR LOG
      SwtUtil.logError(e, this.moduleId, 'refresh', 'doHelp', 10);
    }
  }


  getParamsFromParent() {
    let params = [];
    params = [
      {
        connectionId: this._connectionPoolStatsGrid.selectedItem.id.content, menuAccessId: this._menuAccessId,
        sqlId: this._connectionPoolStatsGrid.selectedItem.sqlId.content, duration: this._connectionPoolStatsGrid.selectedItem.duration.content,
        sqlStatement: this._connectionPoolStatsGrid.selectedItem.sqlStatement.content, sqlStatus: this._connectionPoolStatsGrid.selectedItem.sqlStatus.content,
        moduleId: this._connectionPoolStatsGrid.selectedItem.module.content, audSid: this._connectionPoolStatsGrid.selectedItem.audSid.content,
        stackTrace: this._connectionPoolStatsGrid.selectedItem.stackTrace.content, ssid: this._connectionPoolStatsGrid.selectedItem.SSID.content,
        lastActionTime: this._connectionPoolStatsGrid.selectedItem.lastActionTime.content, status: this._connectionPoolStatsGrid.selectedItem.status.content,
        sqlExecStart:this._connectionPoolStatsGrid.selectedItem.sqlExecStart.content

      }];
    return params;
  }


  convertArrayToXML(josnData, objectName, parentName?: string, attributeList?: Array<string>) {
    let xmlData = parentName ? "<" + parentName + ">" : "";
    try {

      var options = { compact: true, ignoreComment: true, spaces: 4 };


      for (let i = 0; i < josnData.length; i++) {
        delete josnData[i].id;
        delete josnData[i].content;
        delete josnData[i].contains;
        delete josnData[i].remove;

        if (attributeList) {
          josnData[i]._attributes = {};
          for (let index = 0; index < attributeList.length; index++) {
            const element = attributeList[index];
            if(element == "dataelement" && josnData[i][element] == "id"){
              josnData[i]._attributes[element] = "_id";
            }else{
              josnData[i]._attributes[element] = josnData[i][element];

            }
            delete josnData[i][element];
          }
          xmlData += convert.js2xml({ [objectName]: josnData[i] as Object }, options)
        } else {
          if (josnData[i].slickgrid_rowcontent) {
            for (var tagName in josnData[i]) {
              if (josnData[i].slickgrid_rowcontent[tagName]) {

                let slickgridRowContent = josnData[i].slickgrid_rowcontent[tagName];
                delete slickgridRowContent.contains;
                delete slickgridRowContent.remove;
                delete slickgridRowContent.content;


                var valueText = josnData[i][tagName];
                delete josnData[i][tagName];
                josnData[i][tagName] = new Object;
                josnData[i][tagName]['_text'] = valueText;
                josnData[i][tagName]._attributes = {};
                for (var attributeName in slickgridRowContent) {
                  if (attributeName != 'content' && attributeName != 'contains' && attributeName != 'remove') {
                    josnData[i][tagName]._attributes[attributeName] = slickgridRowContent[attributeName];
                  }
                }
              }

            }
          }
          delete josnData[i].id;
          delete josnData[i].content;
          delete josnData[i].remove;
          delete josnData[i].contains;
          delete josnData[i].slickgrid_rowcontent;
          xmlData += "<" + objectName + ">" + convert.js2xml(josnData[i], options) + "</" + objectName + ">";

        }



      }

      xmlData += parentName ? "</" + parentName + ">" : "";
    } catch (e) {
      console.log("e", e);

    }

    return xmlData;

  }
	/**
			   *  This method is used to convert the xmlData to String and handle the special character
			   *
			   * @param cMetaData :XMLList
			   * @param cGrid :CustomGrid
			   * @param tData :XMLList
			   * @param selects :Array
			   * @param type :String
			   */
  convertData(cMetaData, cGrid: SwtCommonGrid, tData, selects, type: string = "pdf", isTotalGrid: boolean = false): void {
    var str: string = "<data>";
    var totals: string = "";
    var columnMetaData: string = JSON.parse(JSON.stringify(cMetaData.column))

    let tempData = $.extend(true, [], cGrid.getFilteredItems());
    var rowData: string = this.convertArrayToXML(tempData, "row", "rows");
    var columnMetaDataAsString: string = this.convertArrayToXML(columnMetaData, "column", "columns", ['filterable', 'dataelement', 'draggable', 'heading', 'width', 'type', 'holiday', 'visible']);

    str += this.removeLineBreaks(columnMetaDataAsString);
    str +=  this.removeLineBreaks(rowData);
    
    // End: Code modified by Bala on 09-Sep-2011 for Issue found in 1053 Beta testing - Assumption report not opened when special character present in Assumption  
    str = str.split("\\").join("BACKSLASH_REPLACE");
    // str = str.split("'").join("\\\\'");
    // str = str.split("&amp;").join("\\&");
    str = str.split("%").join("PERCENTAGE_REPLACE");
    // str = str.split("&lt;").join("\\<");
    // str = str.split("&gt;").join("\\>");
    str = str.split("+").join("PLUSSYMBOL_REPLACE");

    if (tData != null && isTotalGrid) {
      var totalData: string = this.convertArrayToXML($.extend(true, [], tData), "total");
      totals = this.removeLineBreaks(totalData);
    }
    //totals = "<total>" + totals + "</total>";
    str += totals;

    var filters: string = "<filters>";

    if (selects != null) {
      for (var k: number = 0; k < selects.length; k++) {
        filters += "<filter id=\"" + selects[k].split("=")[0] + "\">" + selects[k].split("=")[1] + "</filter>";
      }
    }
    if (cGrid.isFiltered) {

      // for (var i: number = 0; i < cGrid.getCurrentFilter().length; i++) {
      var filterVal = "";// cGrid.getCurrentFilter();

      /*for (var j in 10) {
        if(j != 'content' && j != 'contains' && j != 'remove'){
          filters += "<filter id=\"Column Filter " + j + "\"><![CDATA[" + filterVal[j] + "\]\]></filter>";

        }
        // Special Characters included in selected filter.
        // filters = filters.split("\\").join("BACKSLASH_REPLACE");
        // filters = filters.split("'").join("\\\\'");
        // filters = filters.split("&amp;").join("\\&");
        // filters = filters.split("%").join("PERCENTAGE_REPLACE");
        // filters = filters.split("&lt;").join("\\<");
        // filters = filters.split("&gt;").join("\\>");
        // filters = filters.split("+").join("PLUSSYMBOL_REPLACE");
      }*/
      // }

    }
    filters += "</filters>";
    str += filters;
    str += "</data>";
    ExternalInterface.call('exportDataExcel', type, str);
  }

  removeLineBreaks(input: string): string {

    var rtn: string = "";
    var postSplit = [];
    postSplit = input.split("\n");
    for (var i: number = 0; i < postSplit.length; i++) {
      rtn += postSplit[i];
    }
    return rtn;
  }

  report(type) {
    this.convertData(this.lastRecievedJSON.connectionPoolMonitor.grid.metadata.columns, this._connectionPoolStatsGrid, null, null, type, false);

  }

  /**
  * popupClosed
  * Method to close child windows when this screen is closed
  */
  popupClosed(): void {
    window.close();
  }







}

//Define lazy loading routes
const routes: Routes = [
  { path: '', component: ConnectionPoolStats }
];
export const routing: ModuleWithProviders = RouterModule.forChild(routes);
//Define an NgModule that will be loaded by app module
@NgModule({
  imports: [routing, SwtToolBoxModule],
  declarations: [ConnectionPoolStats],
  entryComponents: []
})
export class ConnectionPoolStatsModule { }
