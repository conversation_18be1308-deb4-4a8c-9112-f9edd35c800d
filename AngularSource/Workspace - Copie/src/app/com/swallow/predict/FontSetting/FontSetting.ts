import { Component, ElementRef, ViewChild } from '@angular/core';
import {
  SwtModule,
  CommonService,
  Logger,
  SwtUtil,
  SwtButton,
  SwtLabel,
  SwtRadioItem, SwtRadioButtonGroup
} from "swt-tool-box";

@Component({
  selector: 'app-font-setting',
  templateUrl: './FontSetting.html',
  styleUrls: ['./FontSetting.css']
})
export class FontSetting extends SwtModule {
  /*----------------------------------------declare here all the screen private variable.-------------------------------------*/

  private logger: Logger;
  public fontSizeValue = 0;

  /*----------------------------------------import all the screen controls here.-------------------------------------*/
  // import close button

  @ViewChild("secondsLabel") secondsLabel: SwtLabel;
  @ViewChild("fonSizeLabel") fonSizeLabel: SwtLabel;
  @ViewChild("fontSize") fontSize: SwtRadioButtonGroup;
  @ViewChild("normalFontSize") normalFontSize: SwtRadioItem;
  @ViewChild("smallFontSize") smallFontSize: SwtRadioItem;
  @ViewChild("okButton") okButton: SwtButton;
  @ViewChild("cancelButton") cancelButton: SwtButton;
  showCloseButton: boolean;
// Screen constructor.
  constructor(private element: ElementRef, private commonService: CommonService) {
    super(element, commonService);
    // instantiate the logger object.
    this.logger = new Logger("FontSetting", commonService.httpclient);
  }

  // Method called in starting of the screen
  onLoad() {

    this.logger.info("Method onLoad - Start.");
    // Variable for errorLocation
    let errorLocation=0;
    try {

      this.okButton.toolTip='Save changes and exit';
      this.okButton.label='Ok';
      this.cancelButton.label='Close';
      this.cancelButton.toolTip='Close Window';
      this.cancelButton.setStyle("styleName","flexButton");
      this.logger.info("Method onLoad End.");
      
      if(this.fontSizeValue == 0) {
        this.fontSize.selectedValue = "N";
      } else {
        this.fontSize.selectedValue = "S";
      }
    } catch(error) {
      console.log("onLoad====",error);
      
      SwtUtil.logError(error, SwtUtil.SYSTEM_MODULE_ID, this.commonService.getQualifiedClassName(this)  , "init", errorLocation);
    }
  }

  removePopUp() {

    this.close();
  }
  saveResult(): void {
    let fontLabel;
    if(this.fontSize.selectedValue == "N") {
      fontLabel = this.normalFontSize.label;
    } else {
      fontLabel = this.smallFontSize.label;
    }
    this.result = {fontSize:  {value: this.fontSize.selectedValue , label:fontLabel }};
    this.removePopUp();
  }


}

 
