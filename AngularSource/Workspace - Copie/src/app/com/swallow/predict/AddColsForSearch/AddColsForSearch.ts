import { Component, OnInit, ModuleWithProviders, NgModule, ElementRef, ViewChild } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';
import { SwtToolBoxModule, SwtModule, CommonService, SwtCanvas, SwtButton, SwtUtil, SwtCommonGrid, SwtAlert, HTTPComms, JSONReader, ExternalInterface, SwtComboBox, SwtCheckBox, SwtLabel, Logger, Alert, StringUtils, SwtPopUpManager, ModuleLoader, ModuleEvent } from 'swt-tool-box';
import moment, { constructor } from 'moment';
declare var require: any;
const $ = require('jquery');
var prettyData = require('pretty-data');
let convert = require('xml-js');
@Component({
  selector: 'app-add-cols-for-search',
  templateUrl: './AddColsForSearch.html',
  styleUrls: ['./AddColsForSearch.css']
})
export class AddColsForSearch extends SwtModule implements OnInit {

  /***********SwtCanvas***********/
  @ViewChild('colCanvas') colCanvas: SwtCanvas;

  /***********SwtButton***********/
  @ViewChild('addButton') addButton: SwtButton; 
  @ViewChild('deleteButton') deleteButton: SwtButton;
  @ViewChild('closeButton') closeButton: SwtButton;
  @ViewChild('okButton') okButton: SwtButton;


  private columnsGrid: SwtCommonGrid;
  private colGridRows;
  private gridColumns;
  private menuAccessId;
  private swtAlert: SwtAlert;
  private ordertData = new HTTPComms(this.commonService);
  /**
   * Data Objects
   **/
  public jsonReader: JSONReader = new JSONReader();
  public lastRecievedJSON;
  public prevRecievedJSON;
  public initReceivedJSON;
  /**
    * Communication Objects
    **/
  public inputData = new HTTPComms(this.commonService);
  public baseURL: string = SwtUtil.getBaseURL();
  private actionMethod: string = "";
  private actionPath: string = "";
  private requestParams = [];
  private _invalidComms: string;
  private selectValues;
  private logger: Logger = null;
  private methodName;
  private deletedRow: number = -1;
  public enabledRow: number = -1;
  private addColsData = [];
  private copiedAddColsData = [];
  private gridMetadata;
  private deletedAccount;
  private rowIndex;
  private dataField;
  private oldComboVal;
  private newComboVal;
  private xml="";
  private additionalColsData= [];
  private addColsForFilter= [];
  private saveProfilePopupWindow;
  private errorLocation = 0;
  private additionalColsList =[];
  private columnsList = [];
  private selectedProfile;
  private profileId;
  private entityId;
  private source;
  public operationsList = [];
  public win : any;
  private deletedProfile;
  private msdDisplayColumnsData= [];
  private formatIsoTime : string = "yyyy-mm-dd hh24:mi:ss";
  constructor(private commonService: CommonService, private element: ElementRef) {
    super(element, commonService);
    this.swtAlert = new SwtAlert(commonService);
    this.logger = new Logger('Account Specific Sweep Format', this.commonService.httpclient);
    window["Main"] = this;
  }

  ngOnInit() {
    this.columnsGrid = <SwtCommonGrid>this.colCanvas.addChild(SwtCommonGrid);

    this.addButton.label = SwtUtil.getPredictMessage('button.add', null);
    this.deleteButton.label = SwtUtil.getPredictMessage('button.delete', null);
    this.closeButton.label = SwtUtil.getPredictMessage('button.close', null); 
    this.okButton.label = SwtUtil.getPredictMessage('button.ok', null); 

    this.addButton.toolTip = SwtUtil.getPredictMessage('button.tooltip.msd.addColumn', null);
    this.deleteButton.toolTip = SwtUtil.getPredictMessage('button.tooltip.msd.deleteColumn', null);
    this.closeButton.toolTip = SwtUtil.getPredictMessage('tooltip.close', null);
    this.okButton.toolTip = SwtUtil.getPredictMessage('tooltip.ok', null); 

    this.columnsGrid.editable=true;
  }



onLoad() {
  this.msdDisplayColumnsData= ExternalInterface.call('eval', 'msdDisplayColsList')?JSON.parse(ExternalInterface.call('eval', 'msdDisplayColsList')):[];
  this.requestParams = [];
  this.menuAccessId = ExternalInterface.call('eval', 'menuAccessId');
  this.source = ExternalInterface.call('eval', 'source');
  if (this.menuAccessId) {
    if (this.menuAccessId !== "") {
      this.menuAccessId = Number(this.menuAccessId);
    }
  }
  this.inputData.cbStart = this.startOfComms.bind(this);
  this.inputData.cbStop = this.endOfComms.bind(this);
  this.inputData.cbResult = (event) => {
    this.inputDataResult(event);
  };
  this.inputData.cbFault = this.inputDataFault.bind(this);
  this.inputData.encodeURL = false;
  this.actionPath = "outstandingmovement.do?";
  this.actionMethod = 'method=displayAddColsForSearch';
  this.requestParams['menuAccessId'] = this.menuAccessId;
  this.inputData.url = this.baseURL + this.actionPath + this.actionMethod;
  this.inputData.send(this.requestParams);

  this.columnsGrid.ITEM_CHANGED.subscribe((item) => {
    this.methodName="change";
    this.updateColumnDetails(item);
  });
  this.columnsGrid.onRowClick = (event) => {
    this.cellClickEventHandler(event);
  };


}


 
inputDataResult(event): void {
  // Checks the inputData and stops the communication
  if (this.inputData.isBusy()) {
    this.inputData.cbStop();
  } else {
    this.lastRecievedJSON = event;
    this.jsonReader.setInputJSON(this.lastRecievedJSON);

    if (this.jsonReader.getRequestReplyStatus()) {
      if ((this.lastRecievedJSON != this.prevRecievedJSON)) {

        if (!this.jsonReader.isDataBuilding()) {
          this.addColsData=[];
          this.copiedAddColsData=[];
          this.newComboVal="";
          this.oldComboVal="";
          this.additionalColsList= this.jsonReader.getSelects().select[0].option;
          const obj = { columns: this.lastRecievedJSON.mvtAdditionalCol.addColsGridForSearch.metadata.columns };
          this.gridMetadata = this.lastRecievedJSON.mvtAdditionalCol.addColsGridForSearch.metadata;
          this.selectValues=this.lastRecievedJSON.mvtAdditionalCol.selects;
          this.columnsGrid.gridComboDataProviders(this.selectValues);

          this.gridColumns=this.lastRecievedJSON.mvtAdditionalCol.addColsGridForSearch.metadata.columns.column;
          this.columnsGrid.CustomGrid(obj);

          //this.colGridRows = this.lastRecievedJSON.mvtAdditionalCol.addColsGridForSearch.rows;
          if(window.opener.extraFilter){
            this.generateGridRows();
          }
          else {
            this.columnsGrid.gridData = { size: 0, row: [] };
          }

        }
      }
    } else {
      if (this.lastRecievedJSON.hasOwnProperty("request_reply")) {
        this.swtAlert.error(this.jsonReader.getRequestReplyMessage() + "\n" + this.jsonReader.getRequestReplyLocation(), "Error");
      }
    }

  }
 
}

cellClickEventHandler(event) {
  //Variable for errorLocation
  let errorLocation = 0;
  try {
    this.deletedAccount = this.columnsGrid.selectedItem?this.columnsGrid.selectedItem.value.content:"";
    errorLocation = 10;
    if (this.columnsGrid.selectedIndex >= 0) {
      errorLocation = 20;
      this.deleteButton.enabled = true;
      this.deleteButton.buttonMode = true;
    } else {
      errorLocation = 30;
      this.deleteButton.enabled = false;
      this.deleteButton.buttonMode = false;
    }
  } catch (error) {
    // log the error in ERROR LOG
    this.logger.error('method [cellClickEventHandler] - error: ', error, 'errorLocation: ', errorLocation);
    SwtUtil.logError(error, SwtUtil.PREDICT_MODULE_ID, 'AddColsForSearch.ts', "cellClickEventHandler", errorLocation);
  }

}


  addHandler() {

    //Variable for errorLocation
    let errorLocation = 0;
    try {
      this.methodName = "add";
      this.deletedRow = -1;
      this.enabledRow++;
      //reset row selections and disable buttons
      //this.acctSweepBalGrpGrid.selectedIndex = -1;
      this.deleteButton.enabled = true;
      this.deleteButton.buttonMode = true;
      if (this.columnsGrid.gridData.length > 0 && this.columnsGrid.gridData[0].table == ""){
        this.swtAlert.error(SwtUtil.getPredictMessage('additionalColumns.alert.emptyField', null) + " table name");
      }else if (this.columnsGrid.gridData.length > 0 && this.columnsGrid.gridData[0].column == "") {
        this.swtAlert.error(SwtUtil.getPredictMessage('additionalColumns.alert.emptyField', null) + " column name");
      }else if (this.columnsGrid.gridData.length > 0 && this.columnsGrid.gridData[0].operator == "") {
        this.swtAlert.error(SwtUtil.getPredictMessage('additionalColumns.alert.emptyField', null) + " operator");
      } else {
        this.addColsData.splice(0, 0, {
          table: { clickable: false, content: "P_ACCOUNT", negative: false },
          column: { clickable: false, content: "", negative: false },
          operator: { clickable: false, content: "", negative: false },
          value: { clickable: false, content: "", negative: false },
          sequence: { clickable: false, content: "", negative: false }
        });
        errorLocation = 40;
        for (let index = 0; index < this.gridMetadata.columns.column.length; index++) {
          errorLocation = 20;
          this.gridMetadata.columns.column[index]['editable'] = true;
        }
        errorLocation = 30;
        this.columnsGrid.CustomGrid(this.gridMetadata);
        this.columnsGrid.gridData = { row: this.addColsData, size: this.addColsData.length };
        errorLocation = 50;
        this.columnsGrid.enableDisableCells = (row, field) => {
          return this.enableDisableRow(row, field);
        };

        this.columnsGrid.refresh();
        //this.columnsGrid.selectedIndex=0;
        errorLocation = 60;
      }
    } catch (error) {
      // log the error in ERROR LOG
      this.logger.error('method [addHandler] - error: ', error, 'errorLocation: ', errorLocation);
      SwtUtil.logError(error, SwtUtil.PREDICT_MODULE_ID, 'AddColsForSearch.ts', "addHandler", errorLocation);
    }
  }

  private enableDisableRow(row: any, field: string): boolean {
    //Variable for errorLocation
    let errorLocation = 0;
    try {
      if (row.id == 0) {
        errorLocation = 10;
        return true;
      } else {
        if(field=='value'){
          return true;
          }else{
        return false;
      }
      }
    } catch (error) {
      // log the error in ERROR LOG
      this.logger.error('method [enableDisableRow] - error: ', error, 'errorLocation: ', errorLocation);
      SwtUtil.logError(error, SwtUtil.PREDICT_MODULE_ID, 'AddColsForSearch.ts', "enableDisableRow", errorLocation);
    }
  }

  updateColumnDetails(event) {
    //Variable for errorLocation
    let errorLocation = 0;
    // string to hold the data element 
    var dataElement: string;
    try {
      this.requestParams = [];
      //list of checks in case of change action
      if (event) {
        this.rowIndex = event.rowIndex;
        errorLocation = 10;
        this.dataField = event.dataField;
        errorLocation = 20;
        this.oldComboVal = event.listData.oldValue;
        errorLocation = 30;
        this.newComboVal = event.listData.newValue;
        let colName = this.columnsGrid.dataProvider[this.rowIndex].column;
        //date, amount validation
        if (this.dataField=='value'){
          let dataType;           
            for (const item of this.msdDisplayColumnsData) {
              if (colName == item.columnName) {
                dataType = item.dataType;
              }
            }
            if(dataType=="date"){
              if(!this.validateDate(this.newComboVal)){
                //revert back old value
                this.columnsGrid.dataProvider[this.rowIndex].slickgrid_rowcontent[this.dataField].content = this.oldComboVal;
                this.columnsGrid.dataProvider[this.rowIndex][this.dataField] = this.oldComboVal;
                this.columnsGrid.refresh();
              }

            }else if (dataType == "num" || dataType == "amt") {
              if(isNaN(this.newComboVal) &&  isNaN(parseFloat(this.newComboVal))){
                this.swtAlert.error(SwtUtil.getPredictMessage('errors.invalidNumber', null));
                //revert back old value
                this.columnsGrid.dataProvider[this.rowIndex].slickgrid_rowcontent[this.dataField].content = this.oldComboVal;
                this.columnsGrid.dataProvider[this.rowIndex][this.dataField] = this.oldComboVal;
                this.columnsGrid.refresh();
               }
            }
        }


         if (this.dataField=='table'){

          if(this.newComboVal=="Account"){
            this.newComboVal="P_ACCOUNT";
          }
          //reset combo column value
          this.columnsGrid.dataProvider[this.rowIndex].slickgrid_rowcontent['column'].content = "";
          this.columnsGrid.dataProvider[this.rowIndex]['column'] = "";
          this.columnsGrid.dataProvider[this.rowIndex].slickgrid_rowcontent['value'].content = "";
          this.columnsGrid.dataProvider[this.rowIndex]['value'] = "";
          this.columnsGrid.dataProvider[this.rowIndex].slickgrid_rowcontent['operator'].content = "";
          this.columnsGrid.dataProvider[this.rowIndex]['operator'] = "";
          this.columnsGrid.dataProvider[this.rowIndex].slickgrid_rowcontent[this.dataField].content = this.newComboVal;
          this.columnsGrid.dataProvider[this.rowIndex][this.dataField] = this.newComboVal;
          errorLocation = 40;
          this.columnsGrid.refresh();
          this.addColsData[this.rowIndex]['column'].content = "";
          this.addColsData[this.rowIndex]['value'].content = "";
          this.addColsData[this.rowIndex]['operator'].content = "";
          this.addColsData[this.rowIndex][this.dataField].content = this.newComboVal;
       }else{
        if (this.newComboVal=="" && this.dataField!='value'){
          this.swtAlert.error(SwtUtil.getPredictMessage('additionalColumns.alert.emptyValue', null));
           //revert back old value
           this.columnsGrid.dataProvider[this.rowIndex].slickgrid_rowcontent[this.dataField].content = this.oldComboVal;
           this.columnsGrid.dataProvider[this.rowIndex][this.dataField] = this.oldComboVal;
           errorLocation = 90;
           this.columnsGrid.refresh();
           this.addColsData[this.rowIndex][this.dataField].content = this.oldComboVal;
           return;
        }else {
          this.addColsData[this.rowIndex][this.dataField].content = this.newComboVal;
        }
      }
      }
    } catch (error) {
      // log the error in ERROR LOG
      this.logger.error('method [updateColumnDetails] - error: ', error, 'errorLocation: ', errorLocation);
      SwtUtil.logError(error, SwtUtil.PREDICT_MODULE_ID, 'AddColsForSearch.ts', "updateColumnDetails", errorLocation);
    }
  }


deleteHandler() {
  //Variable for errorLocation
  let errorLocation = 0;
  try {
    Alert.yesLabel = SwtUtil.getPredictMessage('alert.yes.label');
    Alert.noLabel = SwtUtil.getPredictMessage('alert.no.label');
    errorLocation = 10;
    var message: string = StringUtils.substitute((SwtUtil.getPredictMessage('additionalColumns.alert.deleteColumn', null)));
    errorLocation = 20;
    this.swtAlert.confirm(message, SwtUtil.getPredictMessage('alert_header.confirm'), Alert.YES | Alert.NO, null, this.proceedWithDelete.bind(this));
  } catch (error) {
    // log the error in ERROR LOG
    this.logger.error('method [deleteHandler] - error: ', error, 'errorLocation: ', errorLocation);
    SwtUtil.logError(error, SwtUtil.PREDICT_MODULE_ID, 'AddColsForSearch.ts', "deleteHandler", errorLocation);
  }
}

proceedWithDelete(event) {
  //Variable for errorLocation
  let errorLocation = 0;
  try {
    if (event.detail == Alert.YES) {
      errorLocation = 0;
      this.methodName = "delete";
      //this.rowDeletedFlag = true;
      this.deletedRow = this.columnsGrid.selectedIndex;
      errorLocation = 10;
      let deletedColumn = this.columnsGrid.selectedItem.value.content;
      errorLocation = 20;
      this.columnsGrid.removeSelected();
      errorLocation = 30;
      this.addColsData.splice(this.deletedRow, 1);
      this.columnsGrid.gridData = { row: this.addColsData, size: this.addColsData.length };
      errorLocation = 40;
      this.columnsGrid.enableDisableCells = (row, field) => {
        return this.enableDisableRow(row, field);
      };
      this.columnsGrid.refresh();
      this.enabledRow--;
      this.deleteButton.enabled= false;
      this.deleteButton.buttonMode= false;
      this.columnsGrid.selectedIndex=-1;
      errorLocation = 60;
     
    }
  } catch (error) {
    // log the error in ERROR LOG
    this.logger.error('method [proceedWithDelete] - error: ', error, 'errorLocation: ', errorLocation);
    SwtUtil.logError(error, SwtUtil.PREDICT_MODULE_ID, 'AddtionalColumns.ts', "proceedWithDelete", errorLocation);
  }
}

/**                                                                                                                  
 * close the window from the close button                                                                            
 **/
closeHandler(): void {
  //call for close window                                                                                          
  ExternalInterface.call("closeHandler");
}


  closeAndUpdate() {
    if  (JSON.stringify(this.addColsData) !== JSON.stringify(this.copiedAddColsData)) {
      Alert.yesLabel = SwtUtil.getPredictMessage('alert.yes.label');
      Alert.noLabel = SwtUtil.getPredictMessage('alert.no.label');
      var message: string = StringUtils.substitute((SwtUtil.getPredictMessage('entity.CloseConfirm', null)));
      this.swtAlert.confirm(message, SwtUtil.getPredictMessage('alert_header.confirm'), Alert.YES | Alert.NO, null, this.proceedWithClose.bind(this));

    } else {
      ExternalInterface.call("closeHandler");
    }
  }


  proceedWithClose(event){
    //Variable for errorLocation
let errorLocation = 0;
try {
  if (event.detail == Alert.YES) {
    ExternalInterface.call("closeHandler");
  }

} catch (error) {
  // log the error in ERROR LOG
  this.logger.error('method [proceedWithClose] - error: ', error, 'errorLocation: ', errorLocation);
  SwtUtil.logError(error, SwtUtil.PREDICT_MODULE_ID, 'AddColsForSearch.ts', "proceedWithClose", errorLocation);
}
}

saveNewProfile(selectedProfile){
    //Variable for errorLocation
    let errorLocation = 0;
    try {
      this.requestParams = [];
      if (!this.additionalColsList.length)
      this.additionalColsList = [this.additionalColsList];
      this.additionalColsList.push( {type: '', value: selectedProfile, selected: 0, content: selectedProfile});
      errorLocation = 100;
      this.menuAccessId = ExternalInterface.call('eval', 'menuAccessId');
      errorLocation = 110;
      if (this.menuAccessId) {
        if (this.menuAccessId !== "") {
          this.menuAccessId = Number(this.menuAccessId);
        }
      }
      errorLocation = 10;
      this.inputData.cbStart = this.startOfComms.bind(this);
      this.inputData.cbStop = this.endOfComms.bind(this);
      this.inputData.cbResult = (data) => {
       this.swtAlert.show(SwtUtil.getPredictMessage('additionalColumns.alertProfileSaved', null));
      };
      errorLocation = 20;
      this.inputData.cbFault = this.inputDataFault.bind(this);
      this.inputData.encodeURL = false;
      this.actionPath = "outstandingmovement.do?";
      this.actionMethod = 'method=saveProfileAddCols';
      this.requestParams["additionalColumns"] = JSON.stringify(this.prepareGridData());
      this.inputData.url = this.baseURL + this.actionPath + this.actionMethod;
      errorLocation = 120;
      this.inputData.send(this.requestParams);
    } catch (error) {
      // log the error in ERROR LOG
      this.logger.error('method [saveNew] - error: ', error, 'errorLocation: ', errorLocation);
      SwtUtil.logError(error, SwtUtil.PREDICT_MODULE_ID, 'AddColsForSearch.ts', "saveNew", errorLocation);
    }
  }

  prepareGridData(){
    this.additionalColsData=[];
    if (this.columnsGrid.gridData.length > 0){
      for (let i = 0; i < this.columnsGrid.gridData.length; i++) {
      this.additionalColsData.push({
        Table: this.columnsGrid.gridData[i].table,
        Column: this.columnsGrid.gridData[i].column,
        Value: this.columnsGrid.gridData[i].value,
        Sequence: this.columnsGrid.gridData[i].sequence
      });
    }
    /*  this.xml = "<msdAdditionalColumns>" ;
      for (let i = 0; i < this.gridColumns.gridData.length; i++) {
        if(this.gridColumns.gridData[i].label){
          this.xml += "<additionalColumn>" ;
          this.xml +="<table><![CDATA["  + this.gridColumns.gridData[i].table + "]]></table>";
          this.xml +="<column><![CDATA["  + this.gridColumns.gridData[i].column + "]]></column>";
          this.xml +="<label><![CDATA["  + this.gridColumns.gridData[i].label + "]]></label>";
          this.xml += "</additionalColumn>" ;
        }
      }
      this.xml += "</msdAdditionalColumns>" ;*/
  
    }
    //this.xml = prettyData.pd.xml(this.xml);
    return this.additionalColsData;
  }

  
  
  private moduleReadyEventHandler(event) {
    this.saveProfilePopupWindow.addChild(event.target);
    this.saveProfilePopupWindow.display();
    this.saveProfilePopupWindow.onClose.subscribe(() => {
    }, error => {
      console.log(error);
    });
  }



 

  addColsGridChanges() {
    let row = {};
    let gridChanges = this.columnsGrid.changes.getValues();
    for (let i = 0; i < gridChanges.length; i++) {
      row = {
        'OPERATION': gridChanges[i].crud_operation.substring(0, 1), 'Table': gridChanges[i].crud_data.table, 
        'Column': gridChanges[i].crud_data.column, 'Value': gridChanges[i].crud_data.value,
        'Sequence': gridChanges[i].crud_data.sequence
      };
      this.operationsList.push(row)
    }

  }

  

    /**
     * comboBoxChangeHandler
     *
     * @param : event DataGridEvent
     *
     * This method is called when change event occurs in datagrid combo boxes and sets the related cells with
     * values from the XML
     **/
    private comboBoxChangeHandler(event): void {
        
      var errorLocation: number = 0;
      try {
          // set errorLocation    
          this.errorLocation = 10;
          var eventListData: any;
          // string to hold the data element 
          var dataElement: string;
          // number to hold the row index 
          var rowIndex: number;
          // var to hold the rowData 
          var rowData: any = null;
          // string to hold the new data 
          var newData: string = null;
          // var to hold cbo data 
          var data: string = null;
          // var to hold attribute
          var attributeValue: string = null;

          // check if the event target is ComboBoxItemRenderer
          if (event.target == "ComboBoxItemRenderer") {
              this.errorLocation = 20;
              // Get the list data from event
              //TODO             " eventListData = this.DataGridListData.listData;
              // Get the new data value
              newData = event.listData.newValue;
              // get data from item rendrerer 
              data = event.listData.newValue;
              // The data element that will be used as column key
              dataElement = event.dataField;
              // Get the rowindex 
              rowIndex = event.rowIndex;
              // Get the full row data
              rowData = event.listData.new_row;
              // set errorlocation 
              this.errorLocation = 30;
              // if the rec_unit_id cbo is changed 
              if (dataElement == "rec_unit_id") {
                  // set errorlocation
                  this.errorLocation = 40;
                  // var attValuesList = this.lastRecievedJSON.parties.grid1["selects"]["select"].find(x => (String(x.id).indexOf("rec_unit_id|1054.1") != -1)).option;
                  if (this.lastRecievedJSON.parties.grid1["selects"]["select"].find(x => (String(x.id).indexOf("rec_unit_id|1054.1") != -1)).option.length == undefined) {
                      var arr: any = new Array();
                      if (this.lastRecievedJSON.parties.grid1["selects"]["select"].find(x => (String(x.id).indexOf("rec_unit_id|1054.1") != -1)).option) {
                          arr[0] = this.lastRecievedJSON.parties.grid1["selects"]["select"].find(x => (String(x.id).indexOf("rec_unit_id|1054.1") != -1)).option;
                      }
                      this.lastRecievedJSON.parties.grid1["selects"]["select"].find(x => (String(x.id).indexOf("rec_unit_id|1054.1") != -1)).option = arr;
                  }
                  // set attribute value 
                  attributeValue = this.lastRecievedJSON.parties.grid1["selects"]["select"].find(x => (String(x.id).indexOf("rec_unit_id|1054.1") != -1)).option.find(x => x.value == data).attribute

                  this.errorLocation = 45;
                  // set the attribute of rowData 
                  rowData.attribute = (StringUtils.trim(attributeValue).length > 0) ? attributeValue : "EMPTY";
                  this.columnsGrid.updateRow(this.columnsGrid.selectedIndex, "attribute", rowData.attribute);

                  // check if the party is current entity 
                  if (String(rowData.rec_unit_id).length > 0) {
                     /* if (this.partyIsEntity) {
                          // set refOrigibn as origin 
                          rowData.origin = this.RefOrigin;
                      } else {
                          // set Nostro 
                          rowData.origin = this.lastRecievedJSON.parties.grid1["selects"]["select"].find(x => (String(x.id).indexOf("origin|1032") != -1)).option[1].value;
                      }*/
                  }
                  else {
                      rowData.origin = "";
                  }

                  this.columnsGrid.updateRow(this.columnsGrid.selectedIndex, "origin", rowData.origin);

                  // reset data if empty 
                  if (rowData.attribute == "EMPTY")
                      rowData.attribute = null;

                  if (!rowData.new_row)
                      rowData['item'] = { "content": 'Y' };

              }
              else if (dataElement == "attribute") {
                  //-Fix M4944/ISS-172 by Rihab.JB @13/07/2020 -START- 
                  let unfilteredList = [];
                  let filteredList = [];
                  this.errorLocation = 48;
                  unfilteredList = this.lastRecievedJSON.parties.grid1["selects"]["select"].find(x => (String(x.id).indexOf("rec_unit_id|1054.1") != -1)).option;
                  if (unfilteredList.length == undefined) {
                      var arr: any = new Array();
                      if (this.lastRecievedJSON.parties.grid1["selects"]["select"].find(x => (String(x.id).indexOf("rec_unit_id|1054.1") != -1)).option) {
                          arr[0] = this.lastRecievedJSON.parties.grid1["selects"]["select"].find(x => (String(x.id).indexOf("rec_unit_id|1054.1") != -1)).option;
                      }
                      unfilteredList = arr;
                  }
                  this.errorLocation = 49;
                  unfilteredList.filter(x => {
                      //-Fix M4874/Issue 51 by Rihab.JB @14/07/2020 -START- 
                      if (data == "" || x.attribute == data) {
                          filteredList.push(x);
                      }
                      //-END-
                  });
                  this.errorLocation = 491;
                  var col = this.columnsGrid.columnDefinitions.find(x => x.id == "rec_unit_id");
                  if (col) {
                      col['params']['selectDataSource'] = filteredList;
                  }
                  //-END-
                  this.errorLocation = 50;
                  // refresh dataprovider
                  // ComboBoxItemRenderer._externalAttributeFillList = "rec_unit_id";
              }
              else if (dataElement == "origin") {
                  // do nothing 
              }
          }
      }
      catch (error) {
          //Alert.show("error " + error.getStackTrace());
          //SwtUtil.logError(error, this.currActivityId, this.commonService.getQualifiedClassName(this), "comboBoxChangeHandler", this.errorLocation);
      }
  }

  prepareAddColsForFilter(){
    this.addColsForFilter=[];
    if (this.columnsGrid.gridData.length > 0){
      for (let i = 0; i < this.columnsGrid.gridData.length; i++) {
     /* this.additionalColsData.push({
        Table: this.columnsGrid.gridData[i].table,
        Column: this.columnsGrid.gridData[i].column,
        Label: this.columnsGrid.gridData[i].label,
        Sequence: this.columnsGrid.gridData[i].sequence
      });*/
      const seq = "";
      const table = "";
      const column = "";
      const colObject = Object.assign(seq+"*"+table, column);
    }
    }
    return this.additionalColsData;
  }

startOfComms(): void {
  //this.loadingImage.setVisible(true);
}

/**
 * Part of a callback function to all for control of the loading swf from the HTTPComms Object
 */
endOfComms(): void {
  //this.loadingImage.setVisible(false);
}


/**                                                                                                                  
 * If a fault occurs with the connection with the server then display the lost connection label                      
 * @param event:FaultEvent                                                                                           
 **/
private inputDataFault(event): void {
  this._invalidComms = event.fault.faultString + "\n" + event.fault.faultCode + "\n" + event.fault.faultDetail;
  this.swtAlert.show("fault " + this._invalidComms);
}


  prepareSearchFilter(){ 
    if (this.columnsGrid.gridData.length > 0 && this.columnsGrid.gridData[0].table == ""){
      this.swtAlert.error(SwtUtil.getPredictMessage('additionalColumns.alert.emptyField', null) + " table name");
    }else if (this.columnsGrid.gridData.length > 0 && this.columnsGrid.gridData[0].column == "") {
      this.swtAlert.error(SwtUtil.getPredictMessage('additionalColumns.alert.emptyField', null) + " column name");
    }else if (this.columnsGrid.gridData.length > 0 && this.columnsGrid.gridData[0].operator == "") {
      this.swtAlert.error(SwtUtil.getPredictMessage('additionalColumns.alert.emptyField', null) + " operator");
    } else{
      let filtertXml = "<filters>";  
      let  dataType;
      for (var i = 0; i < this.columnsGrid.gridData.length; i++) {
        let colName=this.columnsGrid.gridData[i].column;
        for (const item of this.msdDisplayColumnsData) {
          if (colName == item.columnName) {
            dataType = item.dataType;
          }
        }
      let operator= this.columnsGrid.gridData[i].operator;
      filtertXml += "<filter>";
      filtertXml += "<table_name><![CDATA[" + this.columnsGrid.gridData[i].table + "]]></table_name>";
      filtertXml += "<column_name><![CDATA[" + this.columnsGrid.gridData[i].column + "]]></column_name>";
      filtertXml += "<type><![CDATA[" + dataType + "]]></type>";
      filtertXml += "<operator><![CDATA[" + this.columnsGrid.gridData[i].operator + "]]></operator>";
      let amtVal;
      if (dataType == "date") {
        const val=this.columnsGrid.gridData[i].value !='(EMPTY)'?this.convertToIso(this.columnsGrid.gridData[i].value):this.columnsGrid.gridData[i].value;
        filtertXml += "<value><![CDATA[" + val + "]]></value>";
        } else if (dataType == "datetime") {
          const val=this.columnsGrid.gridData[i].value !='(EMPTY)'?this.convertToIsoWithTime(this.columnsGrid.gridData[i].value):this.columnsGrid.gridData[i].value;
        filtertXml += "<value><![CDATA[" + val + "]]></value>";
        }else if (dataType == "num" && colName == "AMOUNT") {
          let ccyFormat:string=ExternalInterface.call('eval', 'currencyFormat');
          if (ccyFormat == "currencyPat2") {
            amtVal = Number(this.columnsGrid.gridData[i].value.replace(/\./g, '').replace(/,/g, '.'));
          } else if (ccyFormat == "currencyPat1") {
            amtVal= Number(this.columnsGrid.gridData[i].value.replace(/,/g, ''));
          }
        filtertXml += "<value><![CDATA[" +amtVal + "]]></value>";
        }else{
        filtertXml += "<value><![CDATA[" + this.columnsGrid.gridData[i].value + "]]></value>"; 
        }

      //filtertXml += "<value><![CDATA[" + this.columnsGrid.gridData[i].value + "]]></value>";
      filtertXml += "</filter>";  
    }
    filtertXml += "</filters>";
    //filtertXml = prettyData.pd.xml(filtertXml);  
    ExternalInterface.call("updateSearchFilter", StringUtils.encode64(filtertXml));
    //return filtertXml;
  }
  }
  

  convertToIso(dateAsString) {
    let formattedDate="" ;
    let dateFormat= ExternalInterface.call('eval', 'dateFormat');
    if(dateFormat=='dd/MM/yyyy'){
    const [day, month, year] = dateAsString.includes("/") ? dateAsString.split("/"):dateAsString.split("-"); // split the string into day, month, and year components
    formattedDate = `${year}-${month}-${day}`;
    }else{
    const [month, day, year] =  dateAsString.includes("/") ? dateAsString.split("/"):dateAsString.split("-"); // split the string into day, month, and year components
    formattedDate = `${year}-${month}-${day}`;
    }
    return formattedDate;
  }

  convertToIsoWithTime(dateTimeAsString) {
    let formattedDateTime="" ;
    let time= " 00:00:00";
    let dateFormat= ExternalInterface.call('eval', 'dateFormat');
    const [dateString, timeString] = dateTimeAsString.split(' ');
    if(`${timeString}`!='undefined'){
       time= ` ${timeString}`;
    }
    if(dateFormat=='dd/MM/yyyy'){
    const [day, month, year] = dateString.split("/"); // split the string into day, month, and year components    
    formattedDateTime = `${year}-${month}-${day}`+time;
    }else{
    const [month, day, year] = dateString.split("/"); // split the string into day, month, and year components
    formattedDateTime = `${year}-${month}-${day}`+time;
    }
    return formattedDateTime;
  }

  formatDate(dateAsString) {
    let formattedDate="" ;
    let dateFormat= ExternalInterface.call('eval', 'dateFormat');
    const [year, month, day] = dateAsString.split("-");
    if(dateFormat=='dd/MM/yyyy'){
    formattedDate = `${day}/${month}/${year}`;
    }else{
    formattedDate = `${month}/${day}/${year}`;
    }
    return formattedDate;
  }

  validateDate(dateAsString) {
    //Variable for errorLocation
    let errorLocation = 0;
    let dateFormat= ExternalInterface.call('eval', 'dateFormat');
    try {
      let date;
      let dateTime;
      let alert = SwtUtil.getPredictMessage('alert.enterValidDate', null);
      if (dateAsString) {
        date = moment(dateAsString, dateFormat.toUpperCase(), true);
        dateTime= moment(dateAsString, dateFormat.toUpperCase()+ ' HH:MM:SS', true);
        if (!date.isValid() && !dateTime.isValid()) {
          this.swtAlert.error(alert, null, null, null, () => {
          });
          return false;
        }
      }
    } catch (error) {
      // log the error in ERROR LOG
      this.logger.error('method [validateDateField] - error: ', error, 'errorLocation: ', errorLocation);
      SwtUtil.logError(error, SwtUtil.PREDICT_MODULE_ID, 'AccountInterestRateAdd.ts', "validateDateField", errorLocation);
    }

    return true;
  }


  generateGridRows() {
    let xmlAsString= window.opener.extraFilter?StringUtils.decode64(window.opener.extraFilter):"";
    if(xmlAsString) {
      let options = {
        object: false,
        reversible: false,
        coerce: false,
        sanitize: true,
        trim: true,
        arrayNotation: false,
        alternateTextNode: false,
        compact: true
      };
      let jsonData = convert.xml2js(xmlAsString, options);
      let data =  jsonData.filters.filter ;
      if (!data.length)
      data = [data];
      for (let i = 0; i < data.length; i++) {
        let type= data[i].type._cdata;
          this.addColsData.push({
            table: { clickable: false, content: data[i].table_name._cdata, negative: false },
            column: { clickable: false, content: data[i].column_name._cdata, negative: false },
            operator: { clickable: false, content: data[i].operator._cdata, negative: false },
            value: { clickable: false, content: type=='date'?this.formatDate(data[i].value._cdata):data[i].value._cdata, negative: false },
            sequence: { clickable: false, content: "", negative: false }
          });
        }
    }
    this.copiedAddColsData= $.extend(true, [], this.addColsData);
    this.columnsGrid.gridData = { row: this.addColsData, size: this.addColsData.length };
    this.columnsGrid.enableDisableCells = (row, field) => {
      if(field=='value'){
        return true;
        }else{
        return false;
        }
    }
    this.columnsGrid.refresh();
  }

}
//Define lazy loading routes
const routes: Routes = [
  { path: '', component: AddColsForSearch }
];
export const routing: ModuleWithProviders = RouterModule.forChild(routes);
//Define an NgModule that will be loaded by app module
@NgModule({
  imports: [routing, SwtToolBoxModule],
  declarations: [AddColsForSearch],
  entryComponents: []
})
export class AddColsForSearchModule { }