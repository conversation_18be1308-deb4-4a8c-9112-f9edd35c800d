<SwtModule (creationComplete)="onLoad()" width="100%" height="450" >
  <VBox  height="100%" width="100%" paddingLeft="5" paddingRight="5" paddingBottom="5" paddingTop="5">
    <SwtCanvas width="100%" height="14%">
      <VBox width="100%" height="100%">
        <HBox width="100%" height="50%">
          <SwtLabel #lblEntity text="Entity" width="100"></SwtLabel>
          <SwtTextInput #entityId editable="false" width="120"></SwtTextInput>
          <SwtLabel #entityName fontWeight="normal"></SwtLabel>
        </HBox>
        <HBox width="100%" height="50%">
          <SwtLabel #lblDate text="Value Date" width="100"></SwtLabel>
          <SwtTextInput #valueDate editable="false" width="120"></SwtTextInput>
        </HBox>
      </VBox>
    </SwtCanvas>
    <SwtCanvas height="67%" width="100%" #corpAccContainer></SwtCanvas>
    <SwtCanvas width="100%" height="7%">
      <HBox>
      <HBox paddingLeft="5" width="100%">
      <SwtButton id="addButton"
                 #addButton
                 label="Add"
                 width="70"
                 buttonMode="true"
                 (click)="addCropName($event)"></SwtButton>
      <SwtButton id="changeButton"
                 #changeButton
                 label="Change"
                 width="70"
                 buttonMode="true"
                 (click)="updateCropName($event)"
                 enabled="false"
                 toolTip=""></SwtButton>
      <SwtButton id="deleteButton"
                 #deleteButton
                 label="Delete"
                 width="70"
                 buttonMode="true"
                 (click)="deleteCropName($event)"
                 enabled="false"
                 toolTip=""></SwtButton>
      <SwtButton id="closeButton"
                 #closeButton
                 label="Close"
                 width="70"
                 buttonMode="true"
                 (click)="closeHandler($event)"
                 toolTip=""></SwtButton>
      </HBox>
      <HBox paddingTop="8">
        <SwtLabel visible="false" color="red" text="DATA BUILD IN PORGRESS" #dataBuildingText></SwtLabel>
        <SwtLabel visible="false" color="red" text="CONNECTION ERROR" #lostConnectionText></SwtLabel>
      </HBox>
      <HBox horizontalAlign="right" paddingRight="5">
        <SwtLoadingImage #loadingImage></SwtLoadingImage>
      </HBox>
      </HBox>
    </SwtCanvas>
  </VBox>
</SwtModule>
