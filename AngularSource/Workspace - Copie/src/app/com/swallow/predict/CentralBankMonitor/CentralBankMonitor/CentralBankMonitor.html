<SwtModule (creationComplete)="onLoad()" width="100%" height="100%" >
  <VBox  height="100%" width="100%" paddingLeft="5" paddingRight="5" paddingBottom="5" paddingTop="5">
    <SwtCanvas minWidth="1100" width="100%" height="70">
      <Grid width="100%" height="100%">
        <GridRow width="100%" paddingLeft="5">
          <GridItem width="65%">
          <GridItem width="120">
            <SwtLabel #lblEntity text="Entity"></SwtLabel>
          </GridItem>
          <GridItem width="180">
            <SwtComboBox #entityCombo id="entityCombo"
                         width="170"
                         (change)="changeCombo()"
                         dataLabel="entity"
                         toolTip="Select an Entity ID"></SwtComboBox>
          </GridItem>
          <GridItem width="10%">
            <SwtLabel #selectedEntity fontWeight="normal"></SwtLabel>
          </GridItem>
          </GridItem>
          <GridItem width="35%">
            <GridItem width="35%">
              <SwtLabel #lblWeek text="Week Commencing"></SwtLabel>
            </GridItem>
            <GridItem width="28%">
              <SwtDateField #fromDate id="fromDate" width="70" (change)="validateDateFocusOut($event)" (keypress)="validateDateEnterPress($event)"  restrict="0-9/"></SwtDateField>
            </GridItem>
            <GridItem width="10%">
              <SwtLabel #lblDays text="show"></SwtLabel>
            </GridItem>
            <GridItem width="10%">
              <SwtNumericInput #showDays  maxChars="2"  width="30" (keypress)="keyDownInNumberOfDays($event)" (focusOut)="validateShowDaysValue()"></SwtNumericInput>
            </GridItem>
            <GridItem width="5%">
              <SwtLabel #daysLabel text="days" toolTip="Number of days to show"  fontWeight="normal"></SwtLabel>
            </GridItem>
          </GridItem>
        </GridRow>
        <GridRow width="100%"  paddingLeft="5">
          <GridItem width="65%">
            <GridItem width="120">
              <SwtLabel #lblWeek text="Currency Limit"></SwtLabel>
            </GridItem>
            <GridItem width="180">
              <SwtTextInput #currLimitText id="currLimitText"
                            toolTip="Enter the currency limit" width="95%"
                            restrict="0-9.,bBtTmM" textAlign="right" editable="false"></SwtTextInput>
            </GridItem>
            <GridItem width="10%">
              <SwtLabel #multiplier fontWeight="normal"></SwtLabel>
            </GridItem>
          </GridItem>
          <GridItem width="35%">
            <GridItem width="35%">
              <SwtLabel #lblbreak text="Breakdown" width="100" paddingTop="2"></SwtLabel>
            </GridItem>
            <GridItem>
              <SwtRadioButtonGroup #breakdown   id="breakdown"
                                   align="horizontal">
                <SwtRadioItem #accountRadio id="accountRadio" value="A"  label="Account Breakdown"   groupName="breakdown"  selected="true" ></SwtRadioItem>
                <SwtRadioItem #movementRadio id="movementRadio" value="M" label="Movement" groupName="breakdown"></SwtRadioItem>
              </SwtRadioButtonGroup>
            </GridItem>
          </GridItem>
        </GridRow>
      </Grid>
    </SwtCanvas>
    <SwtCanvas width="100%" height="100%" minWidth="1100" #cbCanvas></SwtCanvas>
    <SwtCanvas  id="canvasButtons"  minWidth="1100" width="100%" height="40"  marginBottom="0">
      <HBox width="100%">
        <HBox paddingLeft="5" width="42%">
          <SwtButton #refreshButton
                     width="70"
                     label="Refresh"
                     id="refreshButton"
                     (click)="updateData('yes', true, false)"
                     toolTip="Refresh window"
                     [buttonMode]="true"></SwtButton>
          <SwtButton #rateButton width="70"
                     label="Rate"
                     id="rateButton"
                     (click)="rateHandler()"
                     toolTip="Rate Window"></SwtButton>
          <SwtButton #optionButton
                     width="70"
                     label="Options"
                     toolTip="Change options"
                     (click)="optionHandler()"
                     id="optionButton"></SwtButton>
          <SwtButton  #closeButton
                      width="70"
                      label="Close"
                      (click)="closeHandler()"
                      toolTip="Close window"
                      id="closeButton"></SwtButton>
        </HBox>
        <HBox width="58%" horizontalAlign="right" top="4">
          <SwtLabel visible="false" color="red" text="DATA BUILD IN PORGRESS" #dataBuildingText></SwtLabel>
          <SwtLabel visible="false" color="red" text="CONNECTION ERROR" #lostConnectionText></SwtLabel>
          <SwtLabel #lastRefText text="Last Refresh:" fontWeight="normal"></SwtLabel> <SwtLabel #lastRefTime fontWeight="normal"></SwtLabel>
            <DataExport  #dataExport id="dataExport"></DataExport>
          <SwtHelpButton id="helpIcon"
                         #helpIcon
                         (click)="doHelp()">
          </SwtHelpButton>
          <SwtLoadingImage #loadingImage></SwtLoadingImage>
        </HBox>
      </HBox>
    </SwtCanvas>
  </VBox>
</SwtModule>
