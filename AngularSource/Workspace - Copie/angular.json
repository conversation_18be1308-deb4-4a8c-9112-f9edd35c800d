{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "projects", "projects": {"Smart": {"root": "", "sourceRoot": "src", "projectType": "application", "architect": {"build": {"builder": "@angular-devkit/build-angular:browser", "options": {"outputPath": "dist", "index": "src/index.html", "main": "src/main.ts", "tsConfig": "src/tsconfig.app.json", "polyfills": "src/polyfills.ts", "assets": ["src/assets", "src/favicon.ico", {"glob": "**/*", "input": "node_modules/tinymce/skins", "output": "/tinymce/skins/"}, {"glob": "**/*", "input": "node_modules/tinymce/themes", "output": "/tinymce/themes/"}, {"glob": "**/*", "input": "node_modules/tinymce/plugins", "output": "/tinymce/plugins/"}, {"glob": "**/*", "input": "node_modules/swt-tool-box/src/assets/images", "output": "/assets/images"}, {"glob": "**/*", "input": "node_modules/ngx-monaco-editor/assets/monaco", "output": "/assets/monaco"}], "styles": ["src/styles.css", "node_modules/jquery-ui-dist/jquery-ui.min.css", "node_modules/bootstrap/dist/css/bootstrap.min.css", "node_modules/font-awesome/css/font-awesome.css", "node_modules/swt-tool-box/src/assets/css/main.css", "node_modules/swt-tool-box/src/styles.css", "node_modules/angular-slickgrid/styles/css/slickgrid-theme-bootstrap.css", "node_modules/swt-tool-box/src/assets/css/slick.grid.css", "node_modules/angular-slickgrid/lib/multiple-select/multiple-select.css", "node_modules/swt-tool-box/src/assets/css/advancedDataGrid/skin-win8/ui.fancytree.css", "node_modules/quill/dist/quill.snow.css", "node_modules/tinymce/skins/ui/oxide/skin.min.css", "node_modules/tinymce/skins/ui/oxide/content.min.css", "src/assets/css/jquery.slider.min.css", "src/assets/css/jquery-editable-select.min.css", "node_modules/tinymce/skins/content/default/content.min.css"], "scripts": ["node_modules/jquery/dist/jquery.js", "node_modules/jquery-ui-dist/jquery-ui.js", "node_modules/angular-slickgrid/lib/multiple-select/multiple-select.js", "node_modules/jquery.fancytree/dist/jquery.fancytree-all-deps.min.js", "node_modules/html2canvas/dist/html2canvas.min.js", "src/assets/js/datavalidation.js", "src/assets/js/instance.js", "src/assets/js/jquery.slider.min.js", "src/assets/js/jquery-editable-select.min.js", "node_modules/tinymce/tinymce.min.js", "./node_modules/ngx-monaco-editor/assets/monaco/vs/loader.js", "node_modules/tinymce/themes/silver/theme.js"]}, "configurations": {"production": {"optimization": true, "outputHashing": "all", "sourceMap": false, "extractCss": true, "namedChunks": false, "aot": true, "extractLicenses": true, "vendorChunk": false, "buildOptimizer": true, "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.prod.ts"}]}}}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "options": {"browserTarget": "Smart:build"}, "configurations": {"production": {"browserTarget": "Smart:build:production"}}}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n", "options": {"browserTarget": "Smart:build"}}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"main": "src/test.ts", "karmaConfig": "./karma.conf.js", "polyfills": "src/polyfills.ts", "tsConfig": "src/tsconfig.spec.json", "scripts": ["node_modules/jquery/dist/jquery.js", "node_modules/jquery-ui-dist/jquery-ui.js", "node_modules/bootstrap/dist/js/bootstrap.js", "node_modules/angular-slickgrid/lib/multiple-select/multiple-select.js", "node_modules/jquery.fancytree/dist/jquery.fancytree-all-deps.min.js", "node_modules/html2canvas/dist/html2canvas.min.js", "src/js/commonJS.js", "src/js/jquery.format-sql.js", "src/js/moment-with-langs.js", "src/assets/js/jquery.slider.min.js", "src/assets/js/jquery-editable-select.min.js", "src/js/jquery.cookie.js"], "styles": ["src/styles.css", "src/assets/css/jquery.slider.min.css", "src/assets/css/jquery-editable-select.min.css", "node_modules/jquery-ui-dist/jquery-ui.min.css", "node_modules/bootstrap/dist/css/bootstrap.min.css", "node_modules/font-awesome/css/font-awesome.css", "node_modules/swt-tool-box/src/assets/css/advancedDataGrid/skin-win8/ui.fancytree.css", "node_modules/angular-slickgrid/styles/css/slickgrid-theme-bootstrap.css", "node_modules/swt-tool-box/src/assets/css/slick.grid.css", "node_modules/angular-slickgrid/lib/multiple-select/multiple-select.css", "node_modules/quill/dist/quill.snow.css"], "assets": ["src/assets", "src/favicon.ico", {"glob": "**/*", "input": "node_modules/swt-tool-box/src/assets/images", "output": "/assets/images"}]}}, "lint": {"builder": "@angular-devkit/build-angular:tslint", "options": {"tsConfig": ["src/tsconfig.app.json", "src/tsconfig.spec.json"], "exclude": ["**/node_modules/**"]}}}}, "Smart-e2e": {"root": "e2e", "sourceRoot": "e2e", "projectType": "application", "architect": {"e2e": {"builder": "@angular-devkit/build-angular:protractor", "options": {"protractorConfig": "./protractor.conf.js", "devServerTarget": "Smart:serve"}}, "lint": {"builder": "@angular-devkit/build-angular:tslint", "options": {"tsConfig": ["e2e/tsconfig.e2e.json"], "exclude": ["**/node_modules/**"]}}}}}, "defaultProject": "Smart", "schematics": {"@schematics/angular:component": {"prefix": "app", "styleext": "css"}, "@schematics/angular:directive": {"prefix": "app"}}}